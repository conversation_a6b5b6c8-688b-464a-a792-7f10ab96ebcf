package com.augmentcode.intellij.chat

import com.augmentcode.api.ChatInstructionStreamPayload
import com.augmentcode.api.SmartPasteResolution
import com.augmentcode.intellij.actions.ApplyAllChangesAction
import com.augmentcode.intellij.actions.CopySmartPasteIdAction
import com.augmentcode.intellij.actions.RejectAllChangesAction
import com.augmentcode.intellij.api.AugmentAPI
import com.augmentcode.intellij.metrics.ChatMetricName
import com.augmentcode.intellij.metrics.ClientMetricsReporter
import com.augmentcode.intellij.utils.metricTime
import com.github.benmanes.caffeine.cache.Caffeine
import com.google.common.primitives.Bytes
import com.intellij.diff.DiffContentFactory
import com.intellij.diff.DiffDialogHints
import com.intellij.diff.DiffManager
import com.intellij.diff.chains.SimpleDiffRequestChain
import com.intellij.diff.requests.SimpleDiffRequest
import com.intellij.diff.util.DiffUserDataKeysEx
import com.intellij.openapi.Disposable
import com.intellij.openapi.application.invokeLater
import com.intellij.openapi.application.runReadAction
import com.intellij.openapi.components.Service
import com.intellij.openapi.fileEditor.FileDocumentManager
import com.intellij.openapi.project.Project
import com.intellij.openapi.util.Key
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.util.io.DigestUtil.sha256Hex
import kotlinx.coroutines.*
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.channels.ClosedReceiveChannelException
import kotlinx.coroutines.selects.select
import java.io.IOException
import java.nio.charset.StandardCharsets
import java.util.concurrent.TimeUnit

val SMART_PASTE_RESOLUTION_KEY = Key.create<SmartPasteResolution>("augment.smart.paste.resolution.key")

@Service(Service.Level.PROJECT)
class SmartPasteManager(
  private val project: Project,
  private val cs: CoroutineScope,
) : Disposable {
  companion object {
    fun getInstance(project: Project): SmartPasteManager = project.getService(SmartPasteManager::class.java)

    private const val CACHE_MAX = 10L
    private const val TTL_HOURS = 1L

    /**
     * Get the most up-to-date content of a virtual file.
     */
    fun latestFileContent(virtualFile: VirtualFile): String =
      runReadAction {
        try {
          // First try to get content from the document as it's most up-to-date with the editor.
          FileDocumentManager.getInstance().getDocument(virtualFile)?.text
            ?: virtualFile.contentsToByteArray().toString(Charsets.UTF_8)
        } catch (e: IllegalStateException) {
          virtualFile.contentsToByteArray().toString(Charsets.UTF_8)
        } catch (e: IOException) {
          virtualFile.contentsToByteArray().toString(Charsets.UTF_8)
        }
      }
  }

  private data class SmartPasteResult(
    val startLine: Int,
    val endLine: Int,
    val replacementText: String,
    val requestID: String,
    val initialRequestTime: Long,
    val streamFinishTime: Long,
  )

  private val cache =
    Caffeine
      .newBuilder()
      .maximumSize(CACHE_MAX)
      .expireAfterWrite(TTL_HOURS, TimeUnit.HOURS)
      .build<String, CompletableDeferred<SmartPasteResult>>()

  private val highPriorityChannel = Channel<Pair<ChatInstructionStreamPayload, CompletableDeferred<SmartPasteResult>>>(Channel.UNLIMITED)
  private val lowPriorityChannel = Channel<Pair<ChatInstructionStreamPayload, CompletableDeferred<SmartPasteResult>>>(Channel.UNLIMITED)

  private val clientMetricsReporter = ClientMetricsReporter.getInstance(project)

  init {
    cs.launch {
      while (isActive) {
        val (request, deferred) =
          try {
            select<Pair<ChatInstructionStreamPayload, CompletableDeferred<SmartPasteResult>>> {
              highPriorityChannel.onReceive { it }
              lowPriorityChannel.onReceive { it }
            }
          } catch (e: ClosedReceiveChannelException) {
            break
          }

        try {
          val result = processRequest(request)
          deferred.complete(result)
        } catch (e: Throwable) {
          deferred.completeExceptionally(e)
        }
      }
    }
  }

  private suspend fun processRequest(request: ChatInstructionStreamPayload): SmartPasteResult {
    val initialRequestTime = System.currentTimeMillis()
    val (requestID, stream) = AugmentAPI.instance.smartPaste(request)
    val replacement = StringBuilder()
    var startLine = 0
    var started = false
    var endLine = 0

    stream.collect { result ->
      if (!started) {
        startLine = result.replacementStartLine
        started = true
      } else {
        endLine = result.replacementEndLine
      }
      if (result.replacementText.isNotEmpty()) {
        replacement.append(result.replacementText)
      }
    }
    val streamFinishTime = System.currentTimeMillis()
    return SmartPasteResult(startLine, endLine, replacement.toString(), requestID, initialRequestTime, streamFinishTime)
  }

  /**
   * Main entry point for smart paste.
   *
   * 1. Retrieves or computes the replacement text for the given file and code block
   * 2. Caches the result for future requests with the same input
   * 3. Optionally shows a diff preview of the changes (if showDiff is true) using the intellij DiffManager
   */
  suspend fun processSmartPasteRequest(
    project: Project,
    virtualFile: VirtualFile,
    request: ChatInstructionStreamPayload,
    showDiff: Boolean,
  ) {
    // The time that the user clicked apply
    val applyTime = System.currentTimeMillis()
    val replacement = getReplacement(virtualFile, request, showDiff).await()

    // We report all smart paste requests, even if they were cached.
    if (showDiff) {
      clientMetricsReporter.reportWebviewClientMetricAsync(
        webviewName = "chat",
        clientMetric = ChatMetricName.CHAT_SMART_PASTE,
        value = 1L,
      )
    } else {
      clientMetricsReporter.reportWebviewClientMetricAsync(
        webviewName = "chat",
        clientMetric = ChatMetricName.CHAT_PRECOMPUTE_SMART_PASTE,
        value = 1L,
      )
    }

    if (showDiff) {
      showDiff(project, virtualFile, replacement, applyTime)
    }
  }

  private fun getCacheKey(
    virtualFile: VirtualFile,
    generatedCodeBlock: String,
  ): String {
    val bytes =
      runReadAction {
        Bytes.concat(
          virtualFile.path.toByteArray(StandardCharsets.UTF_8),
          latestFileContent(virtualFile).toByteArray(StandardCharsets.UTF_8),
          generatedCodeBlock.toByteArray(StandardCharsets.UTF_8),
        )
      }
    return sha256Hex(bytes)
  }

  /**
   * Gets the replacement text for the given virtual file and generated code block.
   * If there's no cached result, adds the request to a channel to be processed.
   *
   * If [showDiff] is true, the request will be added to the high priority channel.
   */
  private fun getReplacement(
    virtualFile: VirtualFile,
    request: ChatInstructionStreamPayload,
    showDiff: Boolean,
  ): CompletableDeferred<SmartPasteResult> {
    var wasCached = true
    val replacement =
      cache.get(getCacheKey(virtualFile, request.codeBlock)) {
        val deferred = CompletableDeferred<SmartPasteResult>()
        wasCached = false
        deferred
      }

    if (!wasCached) {
      runBlocking {
        // runBlocking should be safe here since UNLIMITED channels mean we won't block on send
        if (showDiff) {
          highPriorityChannel.send(request to replacement)
        } else {
          lowPriorityChannel.send(request to replacement)
        }
      }
    }
    return replacement
  }

  /**
   * Shows a diff preview of the smart paste changes.
   */
  private fun showDiff(
    project: Project,
    virtualFile: VirtualFile,
    replacement: SmartPasteResult,
    applyTime: Long,
  ) {
    val diffFactory = DiffContentFactory.getInstance()
    val originalContent = diffFactory.create(project, virtualFile)

    // Apply the replacement to the original content based on the start and end lines
    val originalText = latestFileContent(virtualFile)
    val originalLines = originalText.lines()
    val replacementText =
      buildString {
        originalLines.take(maxOf(0, replacement.startLine - 1)).forEach { appendLine(it) }
        append(replacement.replacementText)
        originalLines.drop(replacement.endLine).forEach { appendLine(it) }
      }

    val contentToModify = diffFactory.create(project, replacementText, virtualFile.fileType)
    val request =
      SimpleDiffRequest(
        "Review Code Changes",
        originalContent,
        contentToModify,
        "${virtualFile.name} (Original)",
        "${virtualFile.name} (Augmented)",
      )
    val resolution =
      SmartPasteResolution().apply {
        requestId = replacement.requestID

        val (applySec, applyNs) = metricTime(applyTime)
        applyTimeSec = applySec
        applyTimeNsec = applyNs

        val (requestSec, requestNs) = metricTime(replacement.initialRequestTime)
        initialRequestTimeSec = requestSec
        initialRequestTimeNsec = requestNs

        val (finishSec, finishNs) = metricTime(replacement.streamFinishTime)
        streamFinishTimeSec = finishSec
        streamFinishTimeNsec = finishNs
      }
    request.putUserData(SMART_PASTE_RESOLUTION_KEY, resolution)

    val requestChain = SimpleDiffRequestChain(request)
    requestChain.putUserData(DiffUserDataKeysEx.DIFF_NEW_TOOLBAR, true)
    requestChain.putUserData(DiffUserDataKeysEx.DISABLE_CONTENTS_EQUALS_NOTIFICATION, true)
    requestChain.putUserData(
      DiffUserDataKeysEx.CONTEXT_ACTIONS,
      listOf(
        ApplyAllChangesAction(replacementText, virtualFile, resolution),
        RejectAllChangesAction(originalText, virtualFile, resolution),
        CopySmartPasteIdAction(replacement.requestID),
      ),
    )

    // If we call DiffManager.getInstance() inside of invokeLater, it doesn't use the mock in tests.
    val diffManager = DiffManager.getInstance()
    invokeLater {
      diffManager.showDiff(project, requestChain, DiffDialogHints.DEFAULT)
    }
  }

  override fun dispose() {
    cache.asMap().values.forEach { it.cancel() }
    cache.invalidateAll()
    cache.cleanUp()
    highPriorityChannel.close()
    lowPriorityChannel.close()
  }
}
