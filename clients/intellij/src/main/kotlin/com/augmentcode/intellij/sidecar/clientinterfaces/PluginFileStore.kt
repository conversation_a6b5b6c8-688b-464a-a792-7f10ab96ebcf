package com.augmentcode.intellij.sidecar.clientinterfaces

import com.augmentcode.intellij.idea.AugmentFileStore
import com.intellij.openapi.project.Project
import com.intellij.openapi.util.io.FileUtil
import java.io.File
import java.nio.file.Files
import java.nio.file.Path

class PluginFileStore(private val project: Project) {
  fun loadAsset(path: String): ByteArray? {
    val file = getFileStoreDir().resolve(path).toFile()
    return if (file.exists() && file.isFile) {
      file.readBytes()
    } else {
      return null
    }
  }

  fun getAssetAbsPath(path: String): String {
    val fileStoreDir = getFileStoreDir()
    return FileUtil.toSystemDependentName("$fileStoreDir/$path")
  }

  fun saveAsset(
    path: String,
    contents: ByteArray,
  ) {
    val file = getFileStoreDir().resolve(path).toFile()
    file.parentFile.mkdirs()
    file.writeBytes(contents)
  }

  fun deleteAsset(path: String) {
    val file = getFileStoreDir().resolve(path).toFile()
    if (file.isDirectory) {
      file.deleteRecursively()
    } else {
      file.delete()
    }
  }

  /**
   * List all assets with paths that start with the given prefix
   *
   * @param prefix The prefix to filter assets by
   * @return A list of asset paths
   */
  fun listAssets(prefix: String): List<String> {
    val fileStoreDir = getFileStoreDir().toFile()
    if (!fileStoreDir.exists() || !fileStoreDir.isDirectory) {
      return emptyList()
    }

    return listFilesRecursively(fileStoreDir, prefix, fileStoreDir.path)
  }

  /**
   * Recursively list files in a directory that match a prefix
   *
   * @param dir The directory to search in
   * @param prefix The prefix to filter files by
   * @param basePath The base path to make paths relative to
   * @return A list of file paths
   */
  private fun listFilesRecursively(
    dir: File,
    prefix: String,
    basePath: String,
  ): List<String> {
    val result = mutableListOf<String>()
    val files = dir.listFiles() ?: return emptyList()

    for (file in files) {
      // Make the path relative to the base directory
      val relativePath = file.path.substring(basePath.length + 1)

      if (file.isDirectory) {
        // Recursively search subdirectories
        result.addAll(listFilesRecursively(file, prefix, basePath))
      } else if (file.isFile && (prefix.isEmpty() || relativePath.startsWith(prefix))) {
        // Add file if it matches the prefix (or if no prefix is specified)
        result.add(relativePath)
      }
    }

    return result
  }

  private fun getFileStoreDir(): Path {
    val pluginDataDir = AugmentFileStore.getSubDirectoryForProject(project, "plugin-file-store")
    Files.createDirectories(pluginDataDir)
    return pluginDataDir
  }
}
