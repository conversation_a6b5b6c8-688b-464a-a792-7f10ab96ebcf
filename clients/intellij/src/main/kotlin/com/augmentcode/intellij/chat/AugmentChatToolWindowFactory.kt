package com.augmentcode.intellij.chat

import com.augmentcode.intellij.AugmentBundle
import com.augmentcode.intellij.pluginstate.AugmentAppStateService
import com.augmentcode.intellij.pluginstate.PluginContext
import com.augmentcode.intellij.pluginstate.PluginState
import com.augmentcode.intellij.pluginstate.PluginStateListener
import com.augmentcode.intellij.settings.AugmentSettings
import com.augmentcode.intellij.utils.AugmentDisposable
import com.augmentcode.intellij.utils.AugmentToolWindowType
import com.augmentcode.intellij.utils.TOOL_WINDOW_TYPE_KEY
import com.intellij.openapi.actionSystem.ActionManager
import com.intellij.openapi.actionSystem.DefaultActionGroup
import com.intellij.openapi.actionSystem.Separator
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.project.DumbAware
import com.intellij.openapi.project.Project
import com.intellij.openapi.wm.ToolWindow
import com.intellij.openapi.wm.ToolWindowFactory
import com.intellij.openapi.wm.ToolWindowManager
import icons.AugmentIcons

class AugmentChatToolWindowFactory : ToolWindowFactory, DumbAware {
  override fun shouldBeAvailable(project: Project): Boolean {
    return true
  }

  override fun createToolWindowContent(
    project: Project,
    toolWindow: ToolWindow,
  ) {
    if (project.isDisposed) {
      thisLogger().warn("Project is disposed, cannot create tool window")
      return
    }

    toolWindow.setIcon(AugmentIcons.StatusInitial)
    toolWindow.title = AugmentBundle.message("chat.toolwindow.title")
    toolWindow.stripeTitle = AugmentBundle.message("chat.toolwindow.title")

    // Set up gear actions with state listener
    setupGearActionsWithStateListener(project, toolWindow)

    val contentManager = toolWindow.contentManager
    val chatToolWindow = AugmentChatToolWindow(project)
    val content =
      contentManager.factory.createContent(
        chatToolWindow,
        null,
        true,
      ).apply {
        isCloseable = false
      }
    content.putUserData(TOOL_WINDOW_TYPE_KEY, AugmentToolWindowType.CHAT)
    contentManager.addContent(content)
  }

  private fun setupGearActionsWithStateListener(
    project: Project,
    toolWindow: ToolWindow,
  ) {
    // Set initial gear actions
    setMenuActions(project, toolWindow)

    // Subscribe to plugin state changes to update gear actions dynamically
    val connection = project.messageBus.connect(AugmentDisposable.getInstance(project))
    AugmentAppStateService.instance.subscribe(
      connection,
      object : PluginStateListener {
        override fun onStateChange(
          context: PluginContext,
          state: PluginState,
        ) {
          setMenuActions(project, toolWindow)
        }
      },
    )
  }

  private fun setMenuActions(
    project: Project,
    toolWindow: ToolWindow,
  ) {
    ToolWindowManager.getInstance(project).invokeLater {
      if (!project.isDisposed) {
        val gearActions = createFlattenedGearActions()
        toolWindow.setAdditionalGearActions(gearActions)
      }
    }
  }

  private fun createFlattenedGearActions(): DefaultActionGroup {
    val ctx = AugmentAppStateService.instance.context
    val actionGroup = DefaultActionGroup()

    actionGroup.add(Separator.create())

    if (ctx.isSignedIn) {
      addSignedInActions(actionGroup, ctx)
    } else {
      addSignedOutActions(actionGroup)
    }

    return actionGroup
  }

  private fun addSignedInActions(
    actionGroup: DefaultActionGroup,
    ctx: PluginContext,
  ) {
    actionGroup.add(ActionManager.getInstance().getAction("com.augmentcode.intellij.actions.ToggleCompletionsAction"))
    actionGroup.add(ActionManager.getInstance().getAction("com.augmentcode.intellij.actions.OpenSettingsWebviewAction"))

    actionGroup.add(Separator.create())
    actionGroup.add(ActionManager.getInstance().getAction("com.augmentcode.intellij.actions.ReindexAction"))
    actionGroup.add(ActionManager.getInstance().getAction("com.augmentcode.intellij.actions.ManageAccountAction"))
    actionGroup.add(ActionManager.getInstance().getAction("com.augmentcode.intellij.actions.ExtensionStatusAction"))

    if (ctx.flags.enableCompletionsHistory) {
      actionGroup.add(ActionManager.getInstance().getAction("com.augmentcode.intellij.actions.ShowHistoryAction"))
    }

    if (AugmentSettings.debugFeaturesEnabled) {
      actionGroup.add(ActionManager.getInstance().getAction("com.augmentcode.intellij.actions.GenerateSyncReport"))
    }

    actionGroup.add(Separator.create())
    actionGroup.add(ActionManager.getInstance().getAction("com.augmentcode.intellij.actions.SignOutAction"))

    actionGroup.add(Separator.create())
    actionGroup.add(ActionManager.getInstance().getAction("com.augmentcode.intellij.actions.ShowHelpAction"))
  }

  private fun addSignedOutActions(actionGroup: DefaultActionGroup) {
    actionGroup.add(ActionManager.getInstance().getAction("com.augmentcode.intellij.actions.SignInAction"))

    actionGroup.add(Separator.create())
    actionGroup.add(ActionManager.getInstance().getAction("com.augmentcode.intellij.actions.ExtensionStatusAction"))

    actionGroup.add(Separator.create())
    actionGroup.add(ActionManager.getInstance().getAction("com.augmentcode.intellij.actions.ShowHelpAction"))
  }
}
