package com.augmentcode.intellij.status

import com.augmentcode.intellij.AugmentBundle
import com.augmentcode.intellij.utils.colorizeIcon
import icons.AugmentIcons
import java.awt.Color
import javax.swing.Icon
import javax.swing.UIManager

data class StateDefinition(
  val priority: StatePriority,
  val tooltip: String,
  val icon: Icon,
)

object StateDefinitions {
  // Use progress bar color since there are progress bars in the status bar where we are.
  private val errorFocusColor: Color? = UIManager.getColor("ProgressBar.failedColor")
  private val warningFocusColor: Color? = UIManager.getColor("Component.warningFocusColor")

  val Base: StateDefinition =
    StateDefinition(
      StatePriority.NEUTRAL,
      AugmentBundle.message("augment.status.initial.tooltip"),
      AugmentIcons.StatusInitial,
    )

  val Initializing: StateDefinition =
    StateDefinition(
      StatePriority.NEUTRAL,
      AugmentBundle.message("augment.status.initializing.tooltip"),
      AugmentIcons.StatusInitial,
    )

  val SignInNeeded: StateDefinition =
    StateDefinition(
      StatePriority.HIGH,
      AugmentBundle.message("augment.status.signinNeeded.tooltip"),
      AugmentIcons.StatusInitial,
    )

  val Enabled: StateDefinition =
    StateDefinition(
      StatePriority.NEUTRAL,
      AugmentBundle.message("augment.status.ok.tooltip"),
      AugmentIcons.StatusDefault,
    )

  val GetModelInfoFailed: StateDefinition =
    StateDefinition(
      StatePriority.HIGH,
      AugmentBundle.message("augment.status.get-model-info-failed.tooltip"),
      colorizeIcon(AugmentIcons.StatusInitial, errorFocusColor),
    )

  val AutoCompletionsDisabled: StateDefinition =
    StateDefinition(
      StatePriority.LOW,
      AugmentBundle.message("augment.status.disabled.tooltip"),
      AugmentIcons.StatusDisabled,
    )

  val Unauthorized: StateDefinition =
    StateDefinition(
      StatePriority.HIGH,
      AugmentBundle.message("augment.status.unauthorized.tooltip"),
      colorizeIcon(AugmentIcons.StatusDefault, errorFocusColor),
    )

  val GeneratingCompletion: StateDefinition =
    StateDefinition(
      StatePriority.MEDIUM,
      AugmentBundle.message("augment.status.generating-completion.tooltip"),
      AugmentIcons.StatusDots,
    )

  val NoCompletions: StateDefinition =
    StateDefinition(
      StatePriority.MEDIUM,
      AugmentBundle.message("augment.status.no-completions.tooltip"),
      AugmentIcons.StatusNoCompletions,
    )
}
