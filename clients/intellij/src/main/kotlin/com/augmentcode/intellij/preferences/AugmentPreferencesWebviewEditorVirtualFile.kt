package com.augmentcode.intellij.preferences

import com.augmentcode.intellij.webviews.AbstractWebviewMessagingService
import com.augmentcode.intellij.webviews.AugmentWebviewStateKey
import com.augmentcode.intellij.webviews.WebviewFactory
import com.intellij.ide.actions.SplitAction
import com.intellij.ide.plugins.UIComponentFileEditor
import com.intellij.ide.plugins.UIComponentVirtualFile
import com.intellij.openapi.project.Project
import java.awt.BorderLayout
import javax.swing.JPanel

/**
 * UI component which creates and owns the preferences webview and is opened as an editor tab.
 */
class AugmentPreferencesWebviewEditorVirtualFile(
  private val project: Project,
  val preferencesMessageHandler: AbstractWebviewMessagingService,
) : UIComponentVirtualFile(
    PREFERENCES_VIRTUAL_FILE_NAME,
    null,
  ) {
  companion object {
    const val PREFERENCES_VIRTUAL_FILE_NAME = "Augment Preferences"
    const val ENTRY_FILE_PATH = "preference.html"
  }

  init {
    putUserData(SplitAction.FORBID_TAB_SPLIT, true)
  }

  override fun createContent(editor: UIComponentFileEditor): Content {
    return Content {
      // Create a webview with a load handler for preferences
      val webview =
        WebviewFactory.create(
          project,
          ENTRY_FILE_PATH,
          AugmentWebviewStateKey.PREFERENCES_STATE,
          preferencesMessageHandler,
          editor,
        )

      JPanel(BorderLayout()).apply {
        add(webview.browser.component, BorderLayout.CENTER)
      }
    }
  }
}
