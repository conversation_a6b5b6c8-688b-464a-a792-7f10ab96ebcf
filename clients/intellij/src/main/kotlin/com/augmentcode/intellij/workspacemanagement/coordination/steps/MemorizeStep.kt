package com.augmentcode.intellij.workspacemanagement.coordination.steps

import com.augmentcode.intellij.index.AugmentEditorHistoryService
import com.augmentcode.intellij.index.AugmentRoot
import com.augmentcode.intellij.utils.IndexUtil.expectedBlobName
import com.augmentcode.intellij.utils.IndexUtil.normalizedText
import com.augmentcode.intellij.workspacemanagement.utils.RoughlySizedChannel
import com.intellij.openapi.Disposable
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.openapi.project.Project
import com.intellij.openapi.vfs.VirtualFile
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlin.coroutines.cancellation.CancellationException

class MemorizeStep(
  private val project: Project,
  private val scope: CoroutineScope,
  private val memorizeChannel: RoughlySizedChannel<VirtualFile>,
) :
  Disposable, BaseProcessingStep("MemorizeStep") {
  private val logger = thisLogger()

  override fun createProcessingJob(): Job {
    return scope.launch {
      while (isActive) {
        try {
          processMemorizeChannel()
        } catch (e: CancellationException) {
          throw e
        } catch (e: Exception) {
          logger.warn("Failed to process memorize channel", e)
        }
      }
    }
  }

  private suspend fun processMemorizeChannel() {
    // This logic is copied almost verbatim from AugmentLocalIndexer.map(),
    // i.e. the old indexing pipeline.
    // At the time of writing, the priority is to get something simple and working for Index V3.
    // But if you find a way to simplify how we do RecencyInfo, please do so.

    val virtualFile = memorizeChannel.receive()

    // If file is open in editor, memorize it. This is used in RecencyInfo for completions
    val fileEditors = FileEditorManager.getInstance(project).getEditors(virtualFile)
    if (fileEditors.isEmpty()) {
      return
    }

    val content = normalizedText(virtualFile) ?: return
    val qualifiedPath = AugmentRoot.findQualifiedPathName(project, virtualFile) ?: return
    val expectedBlobName = expectedBlobName(qualifiedPath.relPath, content)
    for (editor in fileEditors) {
      AugmentEditorHistoryService.getInstance(project)
        .memorizeContent(editor, expectedBlobName, content)
    }
  }
}
