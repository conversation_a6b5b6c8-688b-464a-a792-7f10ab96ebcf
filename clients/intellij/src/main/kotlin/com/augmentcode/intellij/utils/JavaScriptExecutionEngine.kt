package com.augmentcode.intellij.utils

import com.augmentcode.intellij.sidecar.NodeInstallationService
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.coroutines.withTimeout
import java.nio.file.Path
import java.util.concurrent.TimeUnit

/**
 * Generic JavaScript executor that can run JavaScript code using Node.js.
 */
object JavaScriptExecutionEngine {
  private const val TIMEOUT_MS = 30_000L

  /**
   * Executes JavaScript code and returns the output.
   */
  suspend fun execute(script: String): String {
    return withTimeout(TIMEOUT_MS) {
        withContext(Dispatchers.IO) {
            val nodeBinary = getNodeBinary()
            runScript(nodeBinary, script)
        }
    }
  }

  private suspend fun getNodeBinary() =
    NodeInstallationService.instance.getNodeBinary().await() ?: throw RuntimeException("Node.js not available")

  private fun runScript(nodeBinary: Path, script: String): String {
    val process = ProcessBuilder(nodeBinary.toString())
      .redirectErrorStream(true)
      .start()

    process.outputStream.use { stdin ->
      stdin.write(script.toByteArray())
      stdin.flush()
    }

    val completed = process.waitFor(TIMEOUT_MS, TimeUnit.MILLISECONDS)
    if (!completed) {
      process.destroyForcibly()
      throw RuntimeException("Execution timed out")
    }

    val output = process.inputStream.bufferedReader().readText()
    if (process.exitValue() != 0) {
      throw RuntimeException("Execution failed: $output")
    }

    return output.trim()
  }
}