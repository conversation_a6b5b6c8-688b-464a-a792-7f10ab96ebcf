package com.augmentcode.intellij.sentry

import com.augmentcode.intellij.AugmentBundle
import com.intellij.openapi.components.service
import com.intellij.openapi.diagnostic.ErrorReportSubmitter
import com.intellij.openapi.diagnostic.IdeaLoggingEvent
import com.intellij.openapi.diagnostic.SubmittedReportInfo
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.project.Project
import com.intellij.openapi.project.ProjectManager
import com.intellij.openapi.wm.WindowManager
import com.intellij.util.Consumer
import java.awt.Component

/**
 * Error report submitter provides a "Report to Augment" button in IntelliJ's error Dialog.
 * If an exception happens in plugin user is prompted to submit the error report to Augment.
 */
class SentryErrorReportSubmitter : ErrorReportSubmitter() {
  override fun getReportActionText(): String = AugmentBundle.message("error.report.action.text")

  override fun submit(
    events: Array<out IdeaLoggingEvent>,
    additionalInfo: String?,
    parentComponent: Component,
    consumer: Consumer<in SubmittedReportInfo>,
  ): Boolean {
    try {
      val sentryService = service<SentryService>()

      // Try to determine the current project context for better error reporting
      val currentProject = getCurrentProject(parentComponent)

      for (event in events) {
        val throwable = event.throwable

        // Add additional info as a breadcrumb if available
        if (!additionalInfo.isNullOrBlank()) {
          sentryService.addBreadcrumb(
            message = "User provided additional information: $additionalInfo",
            category = "user.feedback",
            project = currentProject,
          )
        }

        // Capture the exception with project context if available
        sentryService.captureException(throwable, currentProject)
      }

      // Notify the consumer that the report was submitted
      consumer.consume(
        SubmittedReportInfo(
          SubmittedReportInfo.SubmissionStatus.NEW_ISSUE,
        ),
      )

      return true
    } catch (e: Exception) {
      // If there's an error submitting to Sentry, return false
      thisLogger().warn("Failed to submit error report to Sentry", e)
      return false
    }
  }

  /**
   * Attempts to determine the current project context for better error reporting.
   * Uses the parent component to find the exact project context where the error dialog appears.
   * @param parentComponent The parent component of the error dialog
   * @return The project associated with the parent component, or null if not determinable
   */
  private fun getCurrentProject(parentComponent: Component?): Project? {
    return parentComponent?.let { component ->
      try {
        // Find the window that contains the parent component
        (component as? java.awt.Window ?: javax.swing.SwingUtilities.getWindowAncestor(component))?.let { window ->
          // Check all open projects to find which one owns this window
          ProjectManager.getInstance().openProjects
            .asSequence()
            .filter { !it.isDisposed }
            .mapNotNull { project ->
              WindowManager.getInstance().getFrame(project)?.let { projectFrame ->
                project.takeIf {
                  projectFrame == window || isWindowRelatedToFrame(window, projectFrame)
                }
              }
            }
            .firstOrNull()
        } ?: run {
          thisLogger().debug("Failed to find window for parent component")
          null
        }
      } catch (e: Exception) {
        thisLogger().debug("Failed to determine project context from parent component", e)
        null
      }
    }
  }

  /**
   * Checks if a window is related to (child of, or same as) a project frame.
   */
  private fun isWindowRelatedToFrame(
    window: java.awt.Window,
    projectFrame: java.awt.Window,
  ): Boolean {
    return generateSequence(window) { it.owner }
      .any { it == projectFrame }
  }
}
