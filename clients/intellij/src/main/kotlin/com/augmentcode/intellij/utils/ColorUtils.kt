package com.augmentcode.intellij.utils

import com.intellij.util.ui.ColorizeProxyIcon
import java.awt.Color
import javax.swing.Icon

fun colorizeIcon(
  icon: Icon,
  color: Color?,
): Icon {
  if (color == null) {
    return icon
  }
  // to workaround https://platform.jetbrains.com/t/iconutil-colorize-change-breaks-verification-agains-the-latest-252-21735-32/2025
  return ColorizeProxyIcon.Simple(icon, color)
}
