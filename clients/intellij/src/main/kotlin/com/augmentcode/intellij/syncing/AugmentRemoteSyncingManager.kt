package com.augmentcode.intellij.syncing

import com.augmentcode.api.BlobsPayload
import com.augmentcode.intellij.index.AugmentBlobState
import com.intellij.openapi.components.service
import com.intellij.openapi.project.Project
import com.intellij.openapi.vfs.VirtualFile

interface AugmentRemoteSyncingManager {
  companion object {
    fun getInstance(project: Project): AugmentRemoteSyncingManager {
      return project.service<AugmentRemoteSyncingManager>()
    }
  }

  fun syncedBlobs(): Collection<AugmentBlobState>

  fun queuedBlobs(): Collection<AugmentBlobState>

  // size of the queue of blobs awaiting remote indexing
  fun queueSize(): Int

  // size of the set of blobs completed remote indexing
  fun indexSize(): Int

  // Ratio of indexed blobs to total blobs, which consists of queued and indexed blobs
  fun ratioIndexed(): Double

  /**
   * Removes tracking for a file and its corresponding blob.
   * This deregisters the file from both the synced blobs list and the checkpoint manager.
   *
   * @param rootPath The root path of the project
   * @param relativePath The path of the file relative to the root
   */
  suspend fun notifyContentRemoved(absolutePath: String)

  // notify about content changes so we can schedule an upload
  suspend fun notifyContentChanged(
    rootPath: String,
    relPath: String,
    expectedBlobName: String,
    blobFile: VirtualFile,
  ): AugmentBlobState

  /**
   * Validates if given blobs are remotely indexed and if so adds them to the in-memory index.
   *
   * @return set of unknown blob names that were not validated as already remotely indexed
   */
  suspend fun findUnsynced(blobs: Collection<AugmentBlobState>): Set<String>

  // returns a payload that contains all blobs that are currently synchronized,
  // meaning that the local blob has been uploaded to the server, the server has finished indexing,
  // and the blob is available remotely for retrieval
  suspend fun synchronizedBlobsPayload(): BlobsPayload

  // resets in-memory state
  suspend fun reset()

  /**
   * Notifies the syncing manager that a directory has been moved/renamed.
   * This allows the syncing manager to handle all files within that directory efficiently.
   *
   * @param oldPath The previous path of the directory
   * @param newPath The new path of the directory
   */
  suspend fun notifyDirectoryRemoved(oldPath: String)
}
