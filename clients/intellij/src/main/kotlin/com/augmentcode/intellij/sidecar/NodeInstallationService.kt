package com.augmentcode.intellij.sidecar

import com.augmentcode.intellij.idea.AugmentFileStore
import com.intellij.openapi.components.Service
import com.intellij.openapi.components.service
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.util.net.ssl.CertificateManager
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.async
import kotlinx.coroutines.delay
import org.jetbrains.annotations.VisibleForTesting
import java.io.File
import java.io.IOException
import java.net.URI
import java.nio.file.Files
import java.nio.file.Path
import java.util.concurrent.TimeUnit
import java.util.zip.ZipFile
import javax.net.ssl.HttpsURLConnection

@VisibleForTesting
internal var urlConnectionFactory: (String) -> HttpsURLConnection = { url ->
  URI(url).toURL().openConnection() as HttpsURLConnection
}

@Service(Service.Level.APP)
class NodeInstallationService(val cs: CoroutineScope) {
  companion object {
    val instance: NodeInstallationService
      get() = service()

    const val NODE_VERSION = "v22.14.0" // Node LTS version

    // Map of platform-architecture to expected SHA-256 checksums
    // From https://nodejs.org/dist/v22.14.0/SHASUMS256.txt
    @VisibleForTesting
    internal val NODE_CHECKSUMS =
      mapOf(
        "win-x64" to "55b639295920b219bb2acbcfa00f90393a2789095b7323f79475c9f34795f217", // node-v22.14.0-win-x64.7z
        "win-x86" to "4bf00caba7b0f3c7a4e8ee6a5b73049db19b5ee5510473219ae5fb649c2017b6", // node-v22.14.0-win-x86.7z
        "darwin-arm64" to "e9404633bc02a5162c5c573b1e2490f5fb44648345d64a958b17e325729a5e42", // node-v22.14.0-darwin-arm64.tar.gz
        "darwin-x64" to "6698587713ab565a94a360e091df9f6d91c8fadda6d00f0cf6526e9b40bed250", // node-v22.14.0-darwin-x64.tar.gz
        "linux-arm64" to "8cf30ff7250f9463b53c18f89c6c606dfda70378215b2c905d0a9a8b08bd45e0", // node-v22.14.0-linux-arm64.tar.gz
        "linux-armv7l" to "1cadf5aee7d71b6f0921235faec05e42d908ba5e8a76959f0697968fe0741204", // node-v22.14.0-linux-armv7l.tar.gz
        "linux-x64" to "9d942932535988091034dc94cc5f42b6dc8784d6366df3a36c4c9ccb3996f0c2", // node-v22.14.0-linux-x64.tar.gz
      )

    private val delaySequence =
      listOf(
        5_000L, // 5 seconds
        20_000L, // 20 seconds
        60_000L, // 1 minute
        600_000L, // 10 minutes
      )

    val logger = thisLogger()
  }

  private var nodeDownloadDeferred: kotlinx.coroutines.Deferred<Path?>? = null

  fun getNodeBinary(): kotlinx.coroutines.Deferred<Path?> {
    synchronized(this) {
      nodeDownloadDeferred?.let {
        return it
      }

      val nodeDir = getPluginNodeDir()
      logger.info("Node dir: $nodeDir")

      val os = System.getProperty("os.name").lowercase()
      val isWindows = os.contains("windows")

      // Path for node executable
      val nodePath = nodeDir.resolve(if (isWindows) "node.exe" else "node")
      logger.info("Node path: $nodePath")
      if (Files.exists(nodePath)) {
        logger.info("Node binary already downloaded")
        return kotlinx.coroutines.CompletableDeferred(nodePath)
      }

      val deferred =
        cs.async {
          try {
            downloadNodeBinary(nodePath)
          } finally {
            synchronized(this@NodeInstallationService) {
              nodeDownloadDeferred = null
            }
          }
        }

      nodeDownloadDeferred = deferred
      return deferred
    }
  }

  @VisibleForTesting
  internal suspend fun downloadNodeBinary(
    nodeBinaryPath: Path,
    checksums: Map<String, String> = NODE_CHECKSUMS,
  ): Path? {
    logger.info("Downloading node binary")

    for (retry in 0..delaySequence.size) {
      try {
        logger.info("Attempting download of node binary (attempt ${retry + 1} of ${delaySequence.size})...")
        downloadAndExtractNodeBinary(nodeBinaryPath, checksums)
        logger.info("Finished download and extraction of node binary.")
        return nodeBinaryPath
      } catch (e: Exception) {
        logger.info("Failed to download node (attempt ${retry + 1})", e)

        if (retry < delaySequence.size) {
          val delayMs = delaySequence[retry]
          logger.info("    - retrying in ${delayMs / 1000} seconds")
          delay(delaySequence[retry])
        } else {
          logger.info("    - reached max retries, giving up")
        }
      }
    }
    return null
  }

  /**
   * Downloads and extracts the node binary.
   * @param nodeBinaryPath The desired path for the node binary
   */
  private fun downloadAndExtractNodeBinary(
    nodeBinaryPath: Path,
    checksums: Map<String, String>,
  ) {
    val (platform, architecture, isWindows) = getPlatformInfo()

    val nodeBinaryDir = nodeBinaryPath.parent
    val archiveName = "node-${NODE_VERSION}-$platform-$architecture"
    val archiveFileName = "$archiveName.${if (isWindows) "zip" else "tar.gz"}"
    val archiveFilePath = nodeBinaryDir.parent.resolve(archiveFileName)
    val archiveExtractPath = nodeBinaryDir.parent.resolve(archiveName)

    // Download archive
    val url = "https://nodejs.org/dist/${NODE_VERSION}/$archiveFileName"
    logger.info("Downloading archive to $archiveFilePath...")

    // Expected Checksum
    val expectedChecksum =
      checksums["$platform-$architecture"] ?: throw RuntimeException(
        "No checksum for $platform-$architecture",
      )

    try {
      // Remove any previous download attempts
      cleanupPaths(archiveFilePath, archiveExtractPath)

      // Try to download the file
      downloadFile(url, archiveFilePath)

      // Verify the checksum
      verifyChecksum(archiveFilePath, expectedChecksum)

      // Extract the archive
      if (isWindows) {
        extractWindowsFile(archiveFilePath, archiveName, nodeBinaryPath)
      } else {
        extractFile(archiveFilePath, archiveName, nodeBinaryPath)
      }
    } finally {
      // Clean up files we don't need after this work is finished
      cleanupPaths(archiveFilePath, archiveExtractPath)
    }
  }

  private fun cleanupPaths(vararg paths: Path) {
    for (path in paths) {
      if (Files.exists(path)) {
        path.toFile().deleteRecursively()
      }
    }
  }

  private fun verifyChecksum(
    archivePath: Path,
    expectedChecksum: String,
  ) {
    logger.info("Verifying checksum for $archivePath")
    val fileBytes = Files.readAllBytes(archivePath)
    val digest = java.security.MessageDigest.getInstance("SHA-256")
    val calculatedChecksum =
      digest.digest(fileBytes).joinToString("") {
        "%02x".format(it)
      }

    if (calculatedChecksum != expectedChecksum) {
      throw IOException("Checksum verification failed for $archivePath. Expected: $expectedChecksum, Got: $calculatedChecksum")
    }

    logger.info("Checksum verification successful")
  }

  @VisibleForTesting
  fun getPluginNodeDir(): Path {
    // Writing to the plugins dir means the node binary is deleted with the plugin.
    val pluginDataDir = AugmentFileStore.getGlobalSubDirectory("sidecar")
    val nodeDir = pluginDataDir.resolve("node").resolve(NODE_VERSION)
    Files.createDirectories(nodeDir)
    return nodeDir
  }

  private fun extractFile(
    archivePath: Path,
    archiveBase: String,
    nodePath: Path,
  ) {
    // Extract tar.gz using native commands
    val process =
      ProcessBuilder(
        "tar",
        "-xzf",
        archivePath.toString(),
        "-C",
        nodePath.parent.toString(),
        "$archiveBase/bin/node",
      ).redirectError(ProcessBuilder.Redirect.PIPE).start()

    val result = process.waitFor(30, TimeUnit.SECONDS)
    if (!result) {
      process.destroyForcibly()
      throw RuntimeException("Timeout while extracting Node.js binary")
    }
    if (process.exitValue() != 0) {
      throw RuntimeException("Failed to extract Node.js binary: ${process.errorStream.bufferedReader().readText()}")
    }

    // Move the extracted file to the final location
    File(nodePath.parent.toFile(), "$archiveBase/bin/node").copyTo(nodePath.toFile(), overwrite = true)

    // Make it executable
    nodePath.toFile().setExecutable(true)
  }

  private fun extractWindowsFile(
    archivePath: Path,
    archiveBase: String,
    nodePath: Path,
  ) {
    ZipFile(archivePath.toFile()).use { zip ->
      val entry =
        zip.getEntry("$archiveBase/node.exe")
          ?: throw RuntimeException("node.exe not found in archive")
      zip.getInputStream(entry).use { input ->
        Files.newOutputStream(nodePath).use { output ->
          input.copyTo(output)
        }
      }
    }
  }

  private fun downloadFile(
    url: String,
    destination: Path,
    timeout: Int = 30000,
  ) {
    val connection = urlConnectionFactory(url)
    try {
      connection.connectTimeout = timeout
      connection.readTimeout = timeout
      connection.sslSocketFactory = CertificateManager.getInstance().sslContext.socketFactory

      val code = connection.responseCode
      if (code !in 200..299) {
        throw IOException("Failed to download $url: $code ${connection.responseMessage}")
      }

      Files.createDirectories(destination.parent)
      connection.inputStream.use { input ->
        Files.newOutputStream(destination).use { output ->
          input.copyTo(output)
        }
      }
    } finally {
      connection.disconnect()
    }
  }
}

fun getPlatformInfo(): Triple<String, String, Boolean> {
  // Determine OS and architecture
  val os = System.getProperty("os.name").lowercase()
  val arch = System.getProperty("os.arch").lowercase()

  val (platform, architecture) =
    when {
      os.contains("windows") -> "win" to if (arch.contains("64")) "x64" else "x86"
      os.contains("mac") -> "darwin" to if (arch.contains("aarch64")) "arm64" else "x64"
      else ->
        "linux" to
          when {
            arch.contains("aarch64") -> "arm64"
            arch.contains("arm") -> "armv7l"
            else -> "x64"
          }
    }

  val isWindows = platform == "win"
  return Triple(platform, architecture, isWindows)
}
