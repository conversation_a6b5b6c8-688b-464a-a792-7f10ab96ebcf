package com.augmentcode.intellij.completion

import com.augmentcode.api.CompletionResolution
import com.intellij.codeInsight.inline.completion.elements.InlineCompletionElement
import com.intellij.codeInsight.inline.completion.elements.InlineCompletionGrayTextElement

// this is the way to indicate a completion was rejected
private const val COMPLETION_REJECTED_INDEX = -1

class AugmentCompletionElement(
  val idx: Int,
  override val text: String,
  val requestId: String,
) : InlineCompletionElement {
  private val emitTimestamp = System.currentTimeMillis()

  override fun toPresentable(): InlineCompletionElement.Presentable = InlineCompletionGrayTextElement.Presentable(this)

  fun generateCompletionResolution(accepted: Boolean): CompletionResolution {
    return CompletionResolution().also {
      it.requestId = requestId

      it.emitTimeSec = emitTimestamp / 1000
      it.emitTimeNsec = (emitTimestamp % 1000) * 1_000_000

      val now = System.currentTimeMillis()
      it.resolveTimeSec = now / 1000
      it.resolveTimeNsec = (now % 1000) * 1_000_000

      it.acceptedIdx = if (accepted) idx else COMPLETION_REJECTED_INDEX
    }
  }
}
