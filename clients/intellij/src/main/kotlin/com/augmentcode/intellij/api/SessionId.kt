package com.augmentcode.intellij.api

import com.intellij.ide.util.PropertiesComponent
import com.intellij.openapi.application.PermanentInstallationID
import com.jetbrains.rd.util.UUID
import kotlin.text.isNotBlank

object SessionId {
  private const val SESSION_ID = "augment.session.id"

  fun getSessionId(): String {
    val installationID = PermanentInstallationID.get()
    if (installationID.isNotBlank()) {
      return installationID
    } else {
      // We've seen some users on Windows where the installation ID is blank.
      // Should be fixed in the future releases of IntelliJ via https://github.com/JetBrains/intellij-community/pull/3020
      var storedSessionID = PropertiesComponent.getInstance().getValue(SESSION_ID)
      if (storedSessionID == null) {
        storedSessionID = UUID.randomUUID().toString()
        PropertiesComponent.getInstance().setValue(SESSION_ID, storedSessionID)
      }
      return storedSessionID
    }
  }
}
