package com.augmentcode.intellij.webviews

import com.google.protobuf.Message

/**
 * This is an interface webview apps should implement to listen for messages from the webview
 * and respond to them.
 */
interface AugmentMessagingService {
  fun processMessageFromWebview(
    jsonText: String,
    callback: suspend (String) -> Unit,
  )

  fun serializeMessageToJson(message: Message): String

  fun registerRPCAdapter(rpcAdapter: RPCAdapter)

  fun unregisterRPCAdapter(rpcAdapter: RPCAdapter)
}
