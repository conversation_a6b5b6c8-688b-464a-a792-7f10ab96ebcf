package com.augmentcode.intellij.utils

/**
 * Checks if a configuration flag is enabled, with custom property override support.
 *
 * @param propertyKey The custom property key to check for override
 * @param fallbackFlag The feature flag value to use if no custom property is set
 * @return true if the functionality should be enabled, false otherwise
 */
fun propOrFlagBool(
  propertyKey: String,
  fallbackFlag: Boolean,
): Boolean {
  // If custom property is explicitly set, use that value
  val customProperty = CustomPropertyReader.readProperty(propertyKey)?.toBooleanStrictOrNull()

  customProperty?.let { return it }

  // Otherwise use the passed feature flag
  return fallbackFlag
}
