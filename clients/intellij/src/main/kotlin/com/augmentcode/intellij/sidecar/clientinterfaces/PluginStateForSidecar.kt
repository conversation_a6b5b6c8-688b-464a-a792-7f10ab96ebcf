package com.augmentcode.intellij.sidecar.clientinterfaces

import com.augmentcode.sidecar.rpc.clientInterfaces.GetStateValueRequest
import com.augmentcode.sidecar.rpc.clientInterfaces.GetStateValueResponse
import com.augmentcode.sidecar.rpc.clientInterfaces.PluginStateScope
import com.augmentcode.sidecar.rpc.clientInterfaces.SetStateValueRequest
import com.google.protobuf.Empty
import com.intellij.openapi.diagnostic.thisLogger

/**
 * This class implements the plugin state interface for the sidecar.
 * The sidecar can call into this class to get and set state.
 */
class PluginStateForSidecar {
  companion object {
    private val logger = thisLogger()
  }

  fun handleGetRequest(request: GetStateValueRequest): GetStateValueResponse {
    when (request.scope) {
      PluginStateScope.GLOBAL -> {
        val value = GlobalPluginStateForSidecar.instance.getValue(request.key)
        val builder = GetStateValueResponse.newBuilder()
        if (value != null) {
          builder.setJsonValue(value)
        }
        return builder.build()
      }
      else -> {
        logger.warn("Unsupported state scope for get-value: ${request.scope}")
        return GetStateValueResponse.newBuilder().build()
      }
    }
  }

  fun handleSetRequest(request: SetStateValueRequest): Empty {
    when (request.scope) {
      PluginStateScope.GLOBAL -> {
        GlobalPluginStateForSidecar.instance.setValue(request.key, request.jsonValue)
        return Empty.getDefaultInstance()
      }
      else -> {
        logger.warn("Unsupported state scope for set-value: ${request.scope}")
        return Empty.getDefaultInstance()
      }
    }
  }
}
