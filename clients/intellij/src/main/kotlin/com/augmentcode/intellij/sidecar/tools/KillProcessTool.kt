package com.augmentcode.intellij.sidecar.tools

import com.augmentcode.intellij.sidecarrpc.convertStruct
import com.augmentcode.sidecar.rpc.KillProcessInputSchema
import com.augmentcode.sidecar.rpc.chat.ChatHistoryItem
import com.augmentcode.sidecar.rpc.tools.ToolCallResponse
import com.google.protobuf.Struct
import com.intellij.execution.ExecutionManager
import com.intellij.openapi.project.Project

class KillProcessTool : IdeTool {
  override val name = "kill-process"

  override val description = "Kill a process by its terminal ID."

  override val inputMessageDescriptor = KillProcessInputSchema.getDescriptor()

  override suspend fun call(
    project: Project,
    requestId: String,
    toolUseId: String,
    input: Struct,
    chatHistory: List<ChatHistoryItem>,
    conversationId: String,
  ): ToolCallResponse {
    val killRequest = convertStruct(input) { KillProcessInputSchema.newBuilder() }.build()

    val processHandler =
      ExecutionManager.getInstance(project).getRunningProcesses()
        .find { AugmentTerminalInfo.TERMINAL_INFO_KEY.get(it)?.terminalId == killRequest.terminalId }
    if (processHandler == null) {
      return ToolCallResponse.newBuilder()
        .setText("Terminal ${killRequest.terminalId} not found")
        .setIsError(true)
        .build()
    }

    try {
      processHandler.destroyProcess()
    } catch (ex: Throwable) {
      return ToolCallResponse.newBuilder()
        .setText(ex.message ?: "Unknown error")
        .setIsError(true)
        .build()
    }
    return ToolCallResponse.newBuilder()
      .setText("Terminal ${killRequest.terminalId} killed")
      .build()
  }
}
