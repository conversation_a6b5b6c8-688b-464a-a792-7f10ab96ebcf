package com.augmentcode.intellij.api

import com.intellij.util.net.JdkProxyProvider
import com.intellij.util.net.ssl.CertificateManager
import io.ktor.client.*
import io.ktor.client.engine.cio.*
import io.ktor.client.plugins.*
import io.ktor.http.*
import java.net.Proxy
import java.net.URI
import java.util.concurrent.ConcurrentHashMap

/**
 * Default [HttpClientProvider] implementation that configures a Ktor [HttpClient] with retry logic,
 * timeouts, and user agent settings.
 *
 * For testing, replace this implementation with a mock using [MockEngine]:
 * ```
 * val mockEngine = MockEngine { ... }
 * HttpUtil.registerMockHttpClient(mockEngine, testRootDisposable)
 * ```
 *
 * You may need to set fake credentials for your test to be able to reach the HTTP client:
 * ```
 * AugmentSettings.instance.apiToken = "test-token"
 * AugmentSettings.instance.completionURL = "http://test-server"
 * ```
 */
class HttpClientProviderImpl : HttpClientProvider {
  private val clients: MutableMap<String, HttpClient> = ConcurrentHashMap()

  override fun clientFor(uri: URI): HttpClient {
    // Inspired by com.intellij.util.net.HttpConnectionUtils#openConnection
    val proxies =
      JdkProxyProvider.getInstance().proxySelector.select(uri)
        // direct proxy is just a regular connection so let's ignore it
        .filter { it.type() != Proxy.Type.DIRECT }
    val cachingKey = uri.toString() + proxies.joinToString(",") { it.toString() }
    return clients.computeIfAbsent(cachingKey) { createClient(proxies) }
  }

  private fun createClient(proxies: List<Proxy>): HttpClient {
    return try {
      HttpClient(CIO) {
        expectSuccess = false
        engine {
          proxy = proxies.firstOrNull()
          https {
            trustManager = CertificateManager.getInstance().trustManager
          }
          endpoint {
            connectAttempts = 3
            maxConnectionsPerRoute = 1000
            pipelineMaxSize = 100
          }
        }
        install(HttpTimeout) {
          requestTimeoutMillis = 60_000
          connectTimeoutMillis = 60_000
          socketTimeoutMillis = 60_000
        }
        install(HttpRequestRetry) {
          maxRetries = 3
          retryIf { _, response ->
            response.status.value >= 500 ||
              response.status.value == 499 ||
              response.status.value == HttpStatusCode.TooManyRequests.value
          }
          exponentialDelay()
        }
        configureUserAgent(this)
      }
    } catch (e: Throwable) {
      // workaround for 2024.3 version which doesn't have CIO engine
      if (e !is ClassNotFoundException && e !is NoClassDefFoundError) {
        throw e
      }
      HttpClient {
        expectSuccess = false
        install(HttpTimeout) {
          requestTimeoutMillis = 60_000
          connectTimeoutMillis = 60_000
          socketTimeoutMillis = 60_000
        }
        configureUserAgent(this)
      }
    }
  }

  private fun configureUserAgent(config: HttpClientConfig<*>) {
    config.install(UserAgent) {
      agent = getUserAgent()
    }
  }
}
