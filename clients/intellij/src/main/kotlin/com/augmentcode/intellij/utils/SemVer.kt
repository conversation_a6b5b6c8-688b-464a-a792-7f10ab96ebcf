package com.augmentcode.intellij.utils

import java.util.regex.Pattern

data class SemVer(val major: Long, val minor: Long, val patch: Long) : Comparable<SemVer> {
  companion object {
    fun parse(version: String): SemVer {
      val pattern = Pattern.compile("^(\\d+)\\.(\\d+)\\.(\\d+).*$")
      val matcher = pattern.matcher(version)
      if (matcher.matches()) {
        return SemVer(matcher.group(1).toLong(), matcher.group(2).toLong(), matcher.group(3).toLong())
      } else {
        throw IllegalArgumentException("Invalid version: \"$version\"")
      }
    }
  }

  constructor(version: String) : this(SemVer.parse(version).major, SemVer.parse(version).minor, SemVer.parse(version).patch)

  override fun toString(): String {
    return "$major.$minor.$patch"
  }

  override fun compareTo(other: SemVer): Int {
    return when {
      major != other.major -> major.compareTo(other.major)
      minor != other.minor -> minor.compareTo(other.minor)
      else -> patch.compareTo(other.patch)
    }
  }
}
