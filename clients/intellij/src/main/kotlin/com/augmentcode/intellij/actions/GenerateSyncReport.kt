package com.augmentcode.intellij.actions

import com.augmentcode.intellij.settings.AugmentSettings
import com.augmentcode.intellij.syncing.AugmentRemoteSyncingManager
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.project.DumbAwareAction
import com.intellij.openapi.project.Project
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.testFramework.LightVirtualFile
import kotlinx.coroutines.runBlocking
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

class GenerateSyncReport : DumbAwareAction() {
  private fun getStats(project: Project): String {
    val syncedBlobs = AugmentRemoteSyncingManager.getInstance(project).syncedBlobs()
    val queueSize = AugmentRemoteSyncingManager.getInstance(project).queueSize()
    val uniqueFileNames = syncedBlobs.map { it.relativePath }.toSet()
    val duplicateCount = syncedBlobs.size - uniqueFileNames.size

    val uniqueBlobNames = syncedBlobs.map { it.remoteName }.toSet()
    val duplicateBlobCount = syncedBlobs.size - uniqueBlobNames.size

    val statsContent =
      """
      Unique File Names: ${uniqueFileNames.size}
      Duplicate File Names: $duplicateCount

      Unique Remote Blob Names: ${uniqueBlobNames.size}
      Duplicate Remote Blob Names: $duplicateBlobCount

      Tracked files: ${syncedBlobs.size}
      Files to analyze: $queueSize
      """.trimIndent()
    return statsContent
  }

  override fun actionPerformed(e: AnActionEvent) {
    if (!AugmentSettings.debugFeaturesEnabled) return
    val project = e.project ?: return

    runBlocking {
      val syncManager = AugmentRemoteSyncingManager.getInstance(project)

      // Create timestamp for file names
      val timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy_MM_dd_HH:mm:ss"))

      // Create file with synced paths
      val syncedFiles = syncManager.syncedBlobs().map { it.relativePath }.sorted()
      createScratchFile(
        project,
        "synced_files_$timestamp.txt",
        StringBuilder()
          .append("Synced files:")
          .append("\n")
          .append(syncedFiles.joinToString("\n"))
          .toString(),
      )

      // Create file with queued paths
      val queuedFiles = syncManager.queuedBlobs().map { it.relativePath }.sorted()
      createScratchFile(
        project,
        "queued_files_$timestamp.txt",
        StringBuilder()
          .append("Queued files (to sync or ignore):")
          .append("\n")
          .append(queuedFiles.joinToString("\n"))
          .toString(),
      )

      val statsContent = getStats(project)

      createScratchFile(
        project,
        "sync_stats_$timestamp.txt",
        statsContent,
      )
    }
  }

  private fun createScratchFile(
    project: Project,
    fileName: String,
    content: String,
  ): VirtualFile =
    LightVirtualFile(fileName, content).also { file ->
      com.intellij.openapi.fileEditor.FileEditorManager
        .getInstance(project)
        .openFile(file, true)
    }
}
