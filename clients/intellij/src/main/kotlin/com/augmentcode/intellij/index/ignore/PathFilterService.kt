package com.augmentcode.intellij.index.ignore

import com.augmentcode.intellij.ignore.GitIgnore
import com.augmentcode.intellij.ignore.InvalidGitIgnorePatternException
import com.augmentcode.intellij.index.AugmentRoot
import com.augmentcode.intellij.utils.SymlinkUtil.isSymlink
import com.intellij.openapi.components.Service
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.project.Project
import com.intellij.openapi.vfs.VfsUtil
import com.intellij.openapi.vfs.VirtualFile
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Deferred
import kotlinx.coroutines.async
import org.jetbrains.annotations.VisibleForTesting
import java.util.concurrent.ConcurrentHashMap
import java.util.regex.PatternSyntaxException

@Service(Service.Level.PROJECT)
class PathFilterService(val project: Project, val cs: CoroutineScope) {
  companion object {
    fun getInstance(project: Project): PathFilterService {
      return project.getService(PathFilterService::class.java)
    }

    val ignoreFilenames = arrayOf(".gitignore", ".augmentignore")
  }

  private val pathFilters = ConcurrentHashMap<VirtualFile, Deferred<FullPathFilter>>()

  /**
   * @returns true if the file is accepted by the gitignore and augmentignore rules
   * or if the .*ignore check is inconclusive
   */
  suspend fun isAccepted(
    root: VirtualFile,
    file: VirtualFile,
  ): Boolean {
    val stack = getPathFilter(root)
    val verdict = stack.isAccepted(file)
    if (verdict != null) {
      return verdict
    }
    return true
  }

  /**
   * @returns true if the file is accepted by the gitignore and augmentignore rules
   * or if the .*ignore check is inconclusive
   */
  suspend fun isAccepted(file: VirtualFile): Boolean {
    val root = AugmentRoot.findRootForProjectFile(project, file) ?: return false
    return isAccepted(root, file)
  }

  /**
   * @returns true if the file is accepted by the gitignore and augmentignore rules
   * or if the .*ignore check is inconclusive
   */
  suspend fun isAccepted(
    rootPath: String,
    relativePath: String,
  ): Boolean {
    val rootFile = AugmentRoot.findFile(rootPath, "") ?: return false
    val file = AugmentRoot.findFile(rootPath, relativePath) ?: return false
    return isAccepted(rootFile, file)
  }

  @VisibleForTesting
  suspend fun getPathFilter(root: VirtualFile): FullPathFilter {
    return pathFilters.computeIfAbsent(root) {
      cs.async {
        createPathFilter(root)
      }
    }.await()
  }

  fun uncachePathFilter(root: VirtualFile) {
    cs.async {
      pathFilters.remove(root)
    }
  }

  private suspend fun createPathFilter(root: VirtualFile): FullPathFilter {
    // Construct initial multi ignore stack - the order here is important, highest priority is last.
    // See MultiIgnoreStack.isAccepted() where the order becomes important.
    val ignoreStacks = mutableListOf<SingleIgnoreStack>()
    for (ignoreFilename in ignoreFilenames) {
      val ignoreVirtualFile = root.findChild(ignoreFilename)
      var top: IgnoreStackEntry? = null
      if (ignoreVirtualFile != null) {
        val gitIgnore = createGitIgnore(ignoreVirtualFile)
        top = IgnoreStackEntry(ignoreVirtualFile, gitIgnore, null)
      }
      // TODO(diehuxx): why do we add this even when ignoreVirtualFile is null
      ignoreStacks.add(SingleIgnoreStack(ignoreFilename, ignoreVirtualFile, top))
    }
    val initialIgnoreStack = MultiIgnoreStack(ignoreStacks)

    // Iterate through file tree looking for ignore files
    val ignorePathMap = mutableMapOf<String, MultiIgnoreStack>()
    ignorePathMap[""] = initialIgnoreStack
    val directories = mutableListOf(Pair(root, initialIgnoreStack))
    while (directories.isNotEmpty()) {
      val pair = directories.removeAt(0)
      val dir = pair.first
      val parentStack = pair.second

      val ignoreStack = parentStack.buildAtop(dir)
      // If the stack differs from the parent, we found a nested .gitignore and/or .augmentignore
      if (ignoreStack != parentStack) {
        val relPath = VfsUtil.getRelativePath(dir, root)
        if (relPath != null) {
          ignorePathMap[relPath] = ignoreStack
        }
      }

      for (child in dir.children) {
        if (!child.isDirectory) {
          continue
        }
        if (isSymlink(child)) {
          thisLogger().warn("Skipping symlink while creating path filter: ${child.path}")
          continue
        }
        if (ignoreStack.isAccepted(child) != null) {
          continue
        }
        directories.add(Pair(child, ignoreStack))
      }
    }

    return FullPathFilter(root, ignorePathMap)
  }

  // https://linear.app/augmentcode/issue/AU-5240/0560-crash-with-gitignore-error
  // If a customer has an invalid .gitignore or .augmentigore file, we should ignore the invalid rule and continue
  // rather than crash or fail to process the entire file.
  private fun createGitIgnore(ignoreFile: VirtualFile): GitIgnore {
    // Optimistically try to read the file as-is
    try {
      return GitIgnore(ignoreFile.inputStream.bufferedReader().readText())
    } catch (e: PatternSyntaxException) {
      // File has invalid lines
    } catch (e: InvalidGitIgnorePatternException) {
      // File has invalid lines
    } catch (e: NoSuchFileException) {
      // Some other error reading the file
    }

    // Filter out invalid rules
    val validRules = StringBuilder()
    ignoreFile.inputStream.bufferedReader().useLines { lines ->
      for ((index, line) in lines.withIndex()) {
        try {
          GitIgnore(line.trim())
          validRules.appendLine(line)
        } catch (e: PatternSyntaxException) {
          thisLogger().warn("Invalid ignore rule in file ${ignoreFile.name} at line ${index + 1}: $line")
        } catch (e: InvalidGitIgnorePatternException) {
          // File has invalid lines
        }
      }
    }

    return GitIgnore(validRules.toString())
  }
}
