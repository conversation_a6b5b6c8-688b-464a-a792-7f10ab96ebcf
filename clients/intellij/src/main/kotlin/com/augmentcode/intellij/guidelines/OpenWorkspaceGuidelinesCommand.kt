package com.augmentcode.intellij.guidelines

import com.augmentcode.intellij.actions.AnActionWithCtx
import com.augmentcode.intellij.pluginstate.AugmentModel
import com.augmentcode.intellij.pluginstate.PluginContext
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.application.WriteActionAware
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.project.Project
import org.jetbrains.annotations.VisibleForTesting

/**
 * Command to open the workspace guidelines file for editing.
 * If the file doesn't exist, it will be created.
 */
open class OpenWorkspaceGuidelinesCommand : AnActionWithCtx(), WriteActionAware {
  companion object {
    private val logger = thisLogger()
  }

  override fun actionPerformed(e: AnActionEvent) {
    val project = e.project ?: return
    openWorkspaceGuidelines(project)
  }

  override fun startInWriteAction(): Boolean = true // so we can create the file if it doesn't exist

  override fun updateWithCtx(
    e: AnActionEvent,
    ctx: PluginContext,
    model: AugmentModel,
  ) {
    // Guidelines are always enabled
    logger.info("Updating workspace guidelines action state. Enabled: true")
    e.presentation.isEnabled = true
  }

  /**
   * Opens the workspace guidelines file for editing.
   * If the file doesn't exist, it will be created.
   *
   * @param project The project to open guidelines for
   */
  @VisibleForTesting
  fun openWorkspaceGuidelines(project: Project) {
    val guidelinesService = getGuidelinesService(project)
    guidelinesService.openWorkspaceGuidelines()
  }

  /**
   * Gets the guidelines service instance. Extracted for testing.
   */
  @VisibleForTesting
  protected fun getGuidelinesService(project: Project): GuidelinesService {
    return GuidelinesService.getInstance(project)
  }
}
