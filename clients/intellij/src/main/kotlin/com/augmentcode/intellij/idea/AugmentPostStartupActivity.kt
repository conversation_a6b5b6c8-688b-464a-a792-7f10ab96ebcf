package com.augmentcode.intellij.idea

import com.augmentcode.intellij.featurevector.FeatureVectorService
import com.augmentcode.intellij.chat.EditorTrackingService
import com.augmentcode.intellij.metrics.EdtFreezeDetector
import com.augmentcode.intellij.pluginstate.AugmentAppStateService
import com.augmentcode.intellij.pluginstate.AugmentProjectStateService
import com.augmentcode.intellij.workspacemanagement.coordination.WorkspaceCoordinatorService
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.components.service
import com.intellij.openapi.project.Project
import com.intellij.openapi.startup.ProjectActivity

class AugmentPostStartupActivity : ProjectActivity {
  override suspend fun execute(project: Project) {
    AugmentFileStore.migrateFiles()
    if (!ApplicationManager.getApplication().isUnitTestMode) {
      // Initialize app state service
      AugmentAppStateService.instance
      // Initialize project state service
      AugmentProjectStateService.getInstance(project)

      // Initialize EDT freeze detector
      EdtFreezeDetector.instance

      // Initialize workspace manager service
      project.service<WorkspaceCoordinatorService>()
      // Initialize feature vector service
      FeatureVectorService.getInstance()
      // Initialize editor tracking service
      EditorTrackingService.getInstance(project)
    }
  }
}
