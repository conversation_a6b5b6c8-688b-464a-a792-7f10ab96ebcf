package com.augmentcode.api

import com.google.gson.Gson
import com.google.gson.JsonSyntaxException
import com.google.gson.reflect.TypeToken
import public_api.PublicApi.GetModelsResponse.FeatureFlags

private val gson = Gson()

fun FeatureFlags.additionalChatModelsMap(): Map<String, String> {
  if (additionalChatModels.isNullOrEmpty()) {
    return emptyMap()
  }

  val nullableResult: Map<String, String?>? =
    try {
      gson.fromJson(
        additionalChatModels,
        object : TypeToken<Map<String, String>>() {}.type,
      )
    } catch (e: JsonSyntaxException) {
      return emptyMap()
    }

  if (nullableResult == null) {
    return emptyMap()
  }

  return nullableResult.entries.associate { entry ->
    entry.key to (entry.value ?: "null")
  }
}

fun FeatureFlags.modelRegistryMap(): Map<String, String> {
  if (modelRegistry.isNullOrEmpty()) {
    return emptyMap()
  }

  val nullableResult: Map<String, String?>? =
    try {
      gson.fromJson(
        modelRegistry,
        object : TypeToken<Map<String, String>>() {}.type,
      )
    } catch (e: JsonSyntaxException) {
      return emptyMap()
    }

  if (nullableResult == null) {
    return emptyMap()
  }

  return nullableResult.entries.associate { entry ->
    entry.key to (entry.value ?: "null")
  }
}
