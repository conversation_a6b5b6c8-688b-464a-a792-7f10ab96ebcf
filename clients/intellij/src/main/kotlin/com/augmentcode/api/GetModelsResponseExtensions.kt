package com.augmentcode.api

import com.intellij.openapi.util.text.Strings
import public_api.PublicApi.GetModelsResponse
import public_api.PublicApi.Model
import java.util.concurrent.ConcurrentHashMap

private val modelLookupCache = ConcurrentHashMap<GetModelsResponse, Map<String, Model>>()
private val extensionsCache = ConcurrentHashMap<GetModelsResponse, Set<String>>()

fun GetModelsResponse.findDefaultModel(): Model? {
  return findModel(defaultModel)
}

fun GetModelsResponse.findModel(modelName: String?): Model? {
  val targetModelName = modelName ?: defaultModel

  val lookup =
    modelLookupCache.computeIfAbsent(this) { response ->
      response.modelsList.associateBy { it.name }
    }

  return lookup[targetModelName]
}

fun GetModelsResponse.isSupportedFileExtension(extension: String?): Boolean {
  if (featureFlags?.bypassLanguageFilter == true) {
    return true
  }
  if (extension == null) {
    return false
  }

  val supportedExtensions =
    extensionsCache.computeIfAbsent(this) { response ->
      val extensions = HashSet<String>()
      response.languagesList.forEach { language ->
        language.extensionsList.forEach { ext ->
          // normalize to VFS file extension format
          extensions.add(Strings.trimStart(ext.lowercase(), "."))
        }
      }
      extensions
    }

  return supportedExtensions.contains(extension)
}
