package com.augmentcode.intellij.config

import kotlinx.serialization.Serializable

/**
 * Configuration for test projects used in integration tests.
 *
 * This data class defines the structure for test project configurations,
 * allowing the integration test framework to clone repositories, open projects,
 * and verify plugin functionality in real project environments.
 */
@Serializable
data class TestProjectConfig(
  /** Unique identifier for the test project */
  val projectName: String,
  /** GitHub repository URL to clone */
  val repositoryUrl: String,
  /** Branch names to support (e.g., ["main", "develop", "feature-branch"]) */
  val branchNames: List<String>,
  /** Specific file path to open and verify (relative to project root) */
  val targetFilePath: String,
  /** Optional description of what this test project verifies */
  val description: String? = null,
  /** Timeout for project indexing in seconds */
  val indexingTimeoutSeconds: Int = 300,
) {
  val branchName: String
    get() = branchNames.first()
}

/**
 * Predefined test project configurations for integration tests.
 *
 * These configurations are designed to test the Augment plugin with
 * real-world projects of different types and complexities.
 */
object TestProjectConfigs {
  /**
   * Small Kotlin project - IntelliJ PDF Viewer plugin
   * Good for testing basic Kotlin support and plugin functionality
   */
  val KOTLIN_PDF_VIEWER =
    TestProjectConfig(
      projectName = "intellij-pdf-viewer",
      repositoryUrl = "https://github.com/FirstTimeInForever/intellij-pdf-viewer",
      branchNames = listOf("master", "64d43b88268999b96485e13aec3e6c6467d577d8"),
      targetFilePath = "build.gradle.kts",
      description = "Small IntelliJ plugin project for testing Kotlin support",
      indexingTimeoutSeconds = 180,
    )

  val CHROMIUM =
    TestProjectConfig(
      projectName = "chromium",
      repositoryUrl = "https://github.com/chromium/chromium",
      branchNames = listOf("main", "139.0.7258.41"),
      targetFilePath = "README.md",
      description = "Chromium browser for testing C++ support",
      indexingTimeoutSeconds = 600,
    )

  /**
   * Get all available test project configurations
   */
  fun getAllConfigs(): List<TestProjectConfig> =
    listOf(
      KOTLIN_PDF_VIEWER,
      CHROMIUM,
    )
}
