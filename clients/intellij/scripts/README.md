# IntelliJ Plugin Test Conversation Generator

This Node.js script generates an `AugmentWebviewState.xml` file containing N copies of a conversation for testing the scalability of the IntelliJ plugin's chat functionality.

## Quick Start

```bash
# Generate 100 test conversations with default template
node scripts/generate-chat-state.js 100

# Generate 50 conversations from a custom conversation export file
node scripts/generate-chat-state.js 50 my-conversation.json

# Generate 25 conversations with custom output file and prefix
node scripts/generate-chat-state.js 25 --output custom-test.xml --prefix "[Load Test] "
```

## Usage

```
node generate-chat-state.js <count> [conversation-file.json] [options]
```

### Arguments

- `count` (required): Number of conversations to generate
- `conversation-file.json` (optional): Path to a conversation export JSON file to use as template

### Options

- `--output <file>`: Output XML file path (default: `AugmentWebviewState.xml`)
- `--prefix <text>`: Prefix for conversation names (default: `"[Test] "`)
- `--help`, `-h`: Show help message

## Conversation Templates

To use a custom conversation as a template, export a conversation from the webview using the "export conversation" functionality. The script will use this exported JSON file to generate multiple test conversations with realistic data.

## Installation and Setup

1. **Copy the generated XML file** to your IntelliJ project's `.idea` directory:
   ```bash
   cp AugmentWebviewState.xml /path/to/your/project/.idea/
   ```

2. **Restart IntelliJ IDEA** to load the new state

3. **Open the Augment chat panel** to see the generated conversations

## Examples

### Basic Usage

```bash
# Generate 50 conversations for basic testing
node scripts/generate-chat-state.js 50
```

### Using Custom Conversation Template

```bash
# Export a conversation from the webview first, then use it as template
node scripts/generate-chat-state.js 100 exported-conversation.json
```

### Performance Testing Setup

```bash
# Generate 500 conversations for performance testing
node scripts/generate-chat-state.js 500 \
  --output performance-test.xml \
  --prefix "[Perf Test] "
```

### Stress Testing Setup

```bash
# Generate 1000 conversations for stress testing
node scripts/generate-chat-state.js 1000 \
  --output stress-test.xml \
  --prefix "[Stress] "
```

## Troubleshooting

### Common Issues

1. **"Invalid conversation export format" error**
   - Ensure your JSON file follows the correct export format
   - Check that `conversation.id` and `conversation.chatHistory` exist

2. **IntelliJ doesn't load the conversations**
   - Verify the XML file is in the correct `.idea` directory
   - Restart IntelliJ completely
   - Check that the XML file isn't corrupted

3. **Performance issues with many conversations**
   - Start with smaller numbers (50-100) and increase gradually
   - Monitor IntelliJ's memory usage
   - Consider using the `--prefix` option to identify test conversations
