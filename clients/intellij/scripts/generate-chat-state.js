#!/usr/bin/env node

/**
 * Generate AugmentWebviewStateStore.xml file with N copies of a conversation for IntelliJ plugin testing
 *
 * This script creates realistic conversation data with proper message IDs, timestamps, and content structure
 * that matches the exact format expected by the IntelliJ plugin's webview state persistence.
 *
 * Usage:
 *   node generate-intellij-test-conversations.js <count> [conversation-file.json] [options]
 *
 * Examples:
 *   node generate-intellij-test-conversations.js 100
 *   node generate-intellij-test-conversations.js 50 my-conversation.json
 *   node generate-intellij-test-conversations.js 25 --output custom-output.xml
 *
 * The generated XML file should be copied to your IntelliJ project's .idea directory.
 */
//@ts-expect-error this is a nodejs script file, I dont want typescript or mjs!!
const fs = require("fs");
//@ts-expect-error this is a nodejs script file, I dont want typescript or mjs!!
const crypto = require("crypto");

// Default conversation template based on the conversation export format
const DEFAULT_CONVERSATION_TEMPLATE = {
  version: "1.0.0",
  exportedAt: new Date().toISOString(),
  conversation: {
    id: "template-conversation-id",
    name: "Sample Test Conversation",
    createdAtIso: new Date().toISOString(),
    lastInteractedAtIso: new Date().toISOString(),
    chatHistory: [
      {
        request_message:
          "Hello, can you help me understand how to implement user authentication?",
        response_text:
          "I'd be happy to help you implement user authentication! There are several approaches depending on your technology stack. Could you tell me more about what framework or language you're using?",
        status: "success",
        request_id: "sample-request-1",
        seen_state: "seen",
        timestamp: new Date().toISOString(),
        structured_request_nodes: [
          {
            id: 1,
            type: 0, // ChatRequestNodeType.TEXT
            text_node: {
              content:
                "Hello, can you help me understand how to implement user authentication?",
            },
          },
        ],
        structured_output_nodes: [
          {
            id: 1,
            type: 0, // ChatResultNodeType.RAW_RESPONSE
            content:
              "I'd be happy to help you implement user authentication! There are several approaches depending on your technology stack. Could you tell me more about what framework or language you're using?",
          },
        ],
      },
      {
        request_message:
          "I'm working with React and Node.js. What would you recommend?",
        response_text:
          "For a React and Node.js stack, I'd recommend using JWT (JSON Web Tokens) for authentication. Here's a comprehensive approach:\n\n## Backend (Node.js)\n\n1. **Install dependencies:**\n```bash\nnpm install jsonwebtoken bcryptjs express-validator\n```\n\n2. **Create authentication middleware:**\n```javascript\nconst jwt = require('jsonwebtoken');\n\nconst authenticateToken = (req, res, next) => {\n  const authHeader = req.headers['authorization'];\n  const token = authHeader && authHeader.split(' ')[1];\n  \n  if (!token) {\n    return res.sendStatus(401);\n  }\n  \n  jwt.verify(token, process.env.JWT_SECRET, (err, user) => {\n    if (err) return res.sendStatus(403);\n    req.user = user;\n    next();\n  });\n};\n```\n\nThis approach provides secure, stateless authentication that scales well.",
        status: "success",
        request_id: "sample-request-2",
        seen_state: "seen",
        timestamp: new Date().toISOString(),
        structured_request_nodes: [
          {
            id: 1,
            type: 0,
            text_node: {
              content:
                "I'm working with React and Node.js. What would you recommend?",
            },
          },
        ],
        structured_output_nodes: [
          {
            id: 1,
            type: 0,
            content:
              "For a React and Node.js stack, I'd recommend using JWT (JSON Web Tokens) for authentication. Here's a comprehensive approach:\n\n## Backend (Node.js)\n\n1. **Install dependencies:**\n```bash\nnpm install jsonwebtoken bcryptjs express-validator\n```\n\n2. **Create authentication middleware:**\n```javascript\nconst jwt = require('jsonwebtoken');\n\nconst authenticateToken = (req, res, next) => {\n  const authHeader = req.headers['authorization'];\n  const token = authHeader && authHeader.split(' ')[1];\n  \n  if (!token) {\n    return res.sendStatus(401);\n  }\n  \n  jwt.verify(token, process.env.JWT_SECRET, (err, user) => {\n    if (err) return res.sendStatus(403);\n    req.user = user;\n    next();\n  });\n};\n```\n\nThis approach provides secure, stateless authentication that scales well.",
          },
        ],
      },
    ],
    feedbackStates: {},
    toolUseStates: {},
    requestIds: ["sample-request-1", "sample-request-2"],
    isPinned: false,
    isShareable: false,
    extraData: {},
    personaType: 0, // PersonaType.DEFAULT
  },
};

/**
 * Generate a unique conversation ID
 */
function generateConversationId() {
  return crypto.randomUUID();
}

/**
 * Generate a unique request ID
 */
function generateRequestId() {
  return `req-${crypto.randomBytes(8).toString("hex")}`;
}

/**
 * Create a conversation from template with unique IDs and timestamps
 */
function createConversationFromTemplate(
  template,
  index,
  namePrefix = "[Test] ",
) {
  const now = new Date();
  const conversationId = generateConversationId();

  // Ensure namePrefix is never undefined or null
  const safePrefix = namePrefix || "[Test] ";

  // Create conversation with unique data
  return {
    ...template.conversation,
    id: conversationId,
    name: `${safePrefix}${index} - ${template.conversation.name}`,
    createdAtIso: new Date(now.getTime() - index * 60000).toISOString(), // Stagger creation times
    lastInteractedAtIso: new Date(now.getTime() - index * 30000).toISOString(), // Stagger interaction times
    chatHistory: template.conversation.chatHistory.map(
      (exchange, exchangeIndex) => {
        const requestId = generateRequestId();
        const exchangeTime = new Date(
          now.getTime() - index * 60000 + exchangeIndex * 30000,
        );

        return {
          ...exchange,
          request_id: requestId,
          timestamp: exchangeTime.toISOString(),
          structured_request_nodes: exchange.structured_request_nodes?.map(
            (node) => ({
              ...node,
              id: node.id + index * 100, // Ensure unique node IDs across conversations
            }),
          ),
          structured_output_nodes: exchange.structured_output_nodes?.map(
            (node) => ({
              ...node,
              id: node.id + index * 100, // Ensure unique node IDs across conversations
            }),
          ),
        };
      },
    ),
    requestIds: template.conversation.chatHistory.map(() =>
      generateRequestId(),
    ),
  };
}

/**
 * Create the StoredState object containing all conversations
 */
function createStoredState(conversations) {
  const conversationMap = {};
  conversations.forEach((conv) => {
    conversationMap[conv.id] = conv;
  });

  return {
    currentConversationId:
      conversations.length > 0 ? conversations[0].id : undefined,
    conversations: conversationMap,
    agentExecutionMode: "manual",
    isPanelCollapsed: false,
    shouldDisplayAgentBanner: false,
    hasNotUsedAgent: false,
    sortConversationsBy: "lastInteractedAt",
    displayedAnnouncements: [],
    sendMode: "enter",
  };
}

/**
 * Generate the XML content for AugmentWebviewStateStore.xml
 */
function generateXmlContent(storedState) {
  const chatStateJson = JSON.stringify(storedState);

  // XML structure matching IntelliJ's state persistence format
  return `<?xml version="1.0" encoding="UTF-8"?>
<application>
  <component name="AugmentWebviewStateStore">
    <option name="stateMap">
      <map>
        <entry key="CHAT_STATE" value="${escapeXml(chatStateJson)}" />
      </map>
    </option>
  </component>
</application>`;
}

/**
 * Escape XML special characters
 */
function escapeXml(text) {
  return text
    .replace(/&/g, "&amp;")
    .replace(/</g, "&lt;")
    .replace(/>/g, "&gt;")
    .replace(/"/g, "&quot;")
    .replace(/'/g, "&apos;");
}

/**
 * Load conversation template from file
 */
function loadConversationTemplate(filePath) {
  try {
    const content = fs.readFileSync(filePath, "utf8");
    const data = JSON.parse(content);

    // Validate the conversation export format
    if (
      !data.conversation ||
      !data.conversation.id ||
      !Array.isArray(data.conversation.chatHistory)
    ) {
      throw new Error("Invalid conversation export format");
    }

    return data;
  } catch (error) {
    console.error(
      `Error loading conversation template from ${filePath}:`,
      error.message,
    );
    process.exit(1);
  }
}

/**
 * Parse command line arguments
 */
function parseArgs() {
  const args = process.argv.slice(2);

  if (args.length === 0 || args.includes("--help") || args.includes("-h")) {
    console.log(`
Usage: node generate-intellij-test-conversations.js <count> [conversation-file.json] [options]

Arguments:
  count                    Number of conversations to generate (required)
  conversation-file.json   Path to conversation export JSON file (optional)

Options:
  --output <file>         Output XML file path (default: AugmentWebviewStateStore.xml)
  --prefix <text>         Prefix for conversation names (default: "[Test] ")
  --help, -h              Show this help message

Examples:
  node generate-intellij-test-conversations.js 100
  node generate-intellij-test-conversations.js 50 my-conversation.json
  node generate-intellij-test-conversations.js 25 --output custom-output.xml --prefix "[Load Test] "
`);
    process.exit(0);
  }

  const count = parseInt(args[0]);
  if (isNaN(count) || count <= 0) {
    console.error("Error: Count must be a positive integer");
    process.exit(1);
  }

  let conversationFile = null;
  let outputFile = "AugmentWebviewStateStore.xml";
  let namePrefix = "[Test] ";

  for (let i = 1; i < args.length; i++) {
    const arg = args[i];

    if (arg === "--output" && i + 1 < args.length) {
      outputFile = args[++i];
    } else if (arg === "--prefix" && i + 1 < args.length) {
      namePrefix = args[++i];
    } else if (!arg.startsWith("--") && conversationFile === null) {
      conversationFile = arg;
    }
  }

  // Ensure namePrefix is never undefined or null
  if (namePrefix === undefined || namePrefix === null) {
    namePrefix = "[Test] ";
  }

  return { count, conversationFile, outputFile, namePrefix };
}

/**
 * Main function
 */
function main() {
  const { count, conversationFile, outputFile, namePrefix } = parseArgs();

  console.log(`Generating ${count} conversations...`);

  // Load conversation template
  const template = conversationFile
    ? loadConversationTemplate(conversationFile)
    : DEFAULT_CONVERSATION_TEMPLATE;

  console.log(`Using conversation template: ${template.conversation.name}`);

  // Generate conversations
  const conversations = [];
  for (let i = 1; i <= count; i++) {
    const conversation = createConversationFromTemplate(
      template,
      i,
      namePrefix,
    );
    conversations.push(conversation);

    if (i % 100 === 0) {
      console.log(`Generated ${i}/${count} conversations...`);
    }
  }

  // Create stored state
  const storedState = createStoredState(conversations);

  // Generate XML content
  const xmlContent = generateXmlContent(storedState);

  // Write to file
  fs.writeFileSync(outputFile, xmlContent, "utf8");

  console.log(
    `✅ Successfully generated ${count} conversations in ${outputFile}`,
  );
  console.log(
    `📊 File size: ${(fs.statSync(outputFile).size / 1024 / 1024).toFixed(2)} MB`,
  );
  console.log(
    `🎯 Current conversation ID: ${storedState.currentConversationId}`,
  );

  // Provide usage instructions
  console.log(`
📋 Usage Instructions:
1. Copy ${outputFile} to your IntelliJ project's .idea directory
2. Restart IntelliJ IDEA
3. Open the Augment chat panel to see the generated conversations
4. The conversations will be sorted by last interaction time

⚠️  Note: This will replace any existing chat state in your IntelliJ project.
`);
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = {
  generateConversationId,
  createConversationFromTemplate,
  createStoredState,
};
