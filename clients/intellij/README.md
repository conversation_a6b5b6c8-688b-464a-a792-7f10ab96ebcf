# <img src="src/main/resources/META-INF/pluginIcon.svg" width="80" height="80" alt="Augment Icon" align="left"/> Augment Code - World's most advanced AI Coding Assistant for Professionals

[![Official Augment Plugin](https://img.shields.io/badge/Augment-Official-3D855E)](https://augmentcode.com)
[![JetBrains Marketplace](https://img.shields.io/jetbrains/plugin/v/com.augmentcode?label=version)](https://plugins.jetbrains.com/plugin/24072-augment)
[![Downloads](https://img.shields.io/jetbrains/plugin/d/com.augmentcode)](https://plugins.jetbrains.com/plugin/24072-augment)
[![Rating](https://img.shields.io/jetbrains/plugin/r/rating/com.augmentcode)](https://plugins.jetbrains.com/plugin/24072-augment)

<!-- Plugin description -->

**Augment Code** is the most advanced AI coding assistant for JetBrains IDEs, designed for professional software engineers working with large, complex codebases. Our AI Agent, intelligent code completions, and context-aware chat accelerate your development workflow while maintaining code quality and consistency.

[![Augment Code](https://augment-assets.com/augment-hero-sm.png)](https://www.augmentcode.com)

## Why Augment?

Augment is not just another AI coding tool – it's an **AI-powered coding platform** built specifically for enterprise-scale development:

- **Advanced AI Agent**: Complete complex tasks, build features, and solve production issues with an AI agent that deeply understands your codebase
- **Lightning-fast AI Code Completions**: Get intelligent, context-aware suggestions as you type, tailored to your project's patterns and conventions
- **Intelligent AI Chat**: Ask questions, plan features, and get instant answers with deep codebase understanding
- **World-class Context Engine**: Our proprietary context engine understands your entire codebase, dependencies, and project structure
- **Native Integrations**: Connect with Jira, Linear, Notion, GitHub, and more to bring all your development context together

### Get up to speed instantly

Whether you're exploring a new codebase or diving into unfamiliar territory, Augment's AI helps you understand complex systems in minutes. Our AI coding assistant can:

- Explain how systems work
- Help investigate bugs
- Guide you through new APIs and frameworks
- Accelerate onboarding to new projects

### Make updates with confidence

In production-grade software, there are no simple changes. Augment's AI Agent manages the complexity for you by understanding:

- Your code structure and patterns
- API contracts and schemas
- Dependencies and their interactions
- Best practices specific to your codebase

## Key Features

### AI Agent - Your Intelligent Coding Partner

Let our AI Agent handle complex engineering tasks while you focus on architecture and design. The Agent can:

- **Complete Features**: Build entire features with proper error handling and tests
- **Fix Bugs**: Investigate and resolve production issues with full context
- **Refactor Code**: Modernize legacy code while maintaining functionality
- **Write Tests**: Generate comprehensive test suites that actually work

### Intelligent Chat with Smart Apply

Get instant answers and apply changes with confidence:

- **Codebase Q&A**: Ask anything about your code and get accurate answers
- **Smart Apply**: One-click code updates that respect your project's patterns
- **Multi-file Edits**: Make coordinated changes across multiple files
- **Context Awareness**: Chat understands your current file, recent changes, and project structure

### Lightning-Fast Code Completions

Experience AI-powered completions that feel like magic:

- **Instant Suggestions**: Sub-100ms latency for seamless coding flow
- **Multi-line Completions**: Complete entire functions and code blocks
- **Pattern Recognition**: Learns from your codebase to suggest idiomatic code
- **Language Support**: Works with all major programming languages

## Supported IDEs

Augment works seamlessly with all JetBrains IDEs:

- IntelliJ IDEA (Community & Ultimate)
- PyCharm (Community & Professional)
- WebStorm
- GoLand
- PhpStorm
- CLion
- RubyMine
- DataGrip
- Rider
- Android Studio
- And more!

## Installation

1. Open your JetBrains IDE
2. Go to **Settings/Preferences** → **Plugins**
3. Search for **"Augment"** in the Marketplace
4. Click **Install** and restart your IDE
5. Sign in with your Augment account to start coding with AI

## Getting Started

### Quick Start

1. **Install the Plugin**: Follow the installation steps above
2. **Sign In**: Click "Start using Augment" to sign up for a 7-day free trial
3. **Open Chat**: Press `Ctrl+Alt+I` (Windows/Linux) or `Cmd+Ctrl+I` (macOS)
4. **Enable Completions**: Code completions are enabled by default
5. **Try Agent**: Ask the AI to complete a task or build a feature

### Essential Shortcuts

- **Open Chat**: `Ctrl+Alt+I` / `Cmd+Ctrl+I`
- **Show History**: `Ctrl+Alt+7` / `Cmd+Ctrl+7`
- **Toggle Completions**: `Ctrl+Alt+9` / `Cmd+Ctrl+9`

## Configuration

### Customize Your Experience

- **Completions**: Configure when and how completions appear
- **File Types**: Disable Augment for specific file types if needed
- **Integrations**: Connect your development tools for enhanced context

Access settings via **Settings/Preferences** → **Editor** → **Augment**

## Pro Tips

1. **Use Natural Language**: Chat understands context, so ask questions naturally
2. **Leverage Integrations**: Connect Jira, Linear, or Notion for richer context
3. **Review Agent Changes**: Always review AI-generated code before committing
4. **Provide Feedback**: Use thumbs up/down to help improve suggestions

## Resources

- [Documentation](https://docs.augmentcode.com/)
- [Community Discord](https://discord.gg/augmentcode)
- [Support & Issues](https://support.augmentcode.com/)
- [Enterprise Solutions](https://www.augmentcode.com/contact)

## Pricing

Get started with a **7-day free trial** including Agent, Chat, and Completions. After your trial:

- **Professional**: Best for individual developers
- **Team**: Collaborate with your entire team
- **Enterprise**: Custom solutions for large organizations

Visit [augmentcode.com/pricing](https://augmentcode.com/pricing) for details.

<!-- Plugin description end -->

## About Augment Code

Augment Code is built by engineers, for engineers. We understand the challenges of modern software development and have created an AI platform that actually helps you ship better code, faster.

Join thousands of developers who are already coding with Augment Code's AI assistance.

---

**Ready to supercharge your development workflow?** Install Augment Code today and experience the future of AI-powered coding in your JetBrains IDE!

<!-- Plugin description end -->
