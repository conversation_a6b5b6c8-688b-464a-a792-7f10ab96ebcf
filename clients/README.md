# Augment Clients

## TSConfig

This directory has a `tsconfig.json` file that references a number of other
`tsconfig.json` files.

The top level file `//clients/tsconfig.json` serves two purposes:
1. Defines path aliases that all sub tsconfig files can use
2. References all sub tsconfig files so that editors can follow path aliases

`//clients/common/webviews/tsconfig.json` references
`//clients/vscode/tsconfig.json` because the common webviews uses types
from the VSCode extension. In the future these types should be removed.
