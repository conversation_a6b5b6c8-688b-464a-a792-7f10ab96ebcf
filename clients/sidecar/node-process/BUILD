load("@aspect_bazel_lib//lib:copy_to_directory.bzl", "copy_to_directory")
load("@aspect_rules_esbuild//esbuild:defs.bzl", "esbuild")
load("@aspect_rules_jest//jest:defs.bzl", "jest_test")
load("@aspect_rules_ts//ts:defs.bzl", "ts_config", "ts_project")
load("@bazel_skylib//rules:build_test.bzl", "build_test")
load("@npm//:defs.bzl", "npm_link_all_packages")

npm_link_all_packages()

SRC_FILES = glob(
    ["src/**/*.ts"],
    exclude = [
        "src/**/__tests__/**",
        "src/**/__mocks__/**",
    ],
) + [
    "//clients/sidecar/libs:src",
    "//clients/sidecar/node-process/protos:sidecar_ts_protos",
]

TEST_FILES = glob([
    "src/**/__tests__/**/*.ts",
    "src/**/__tests__/__fixtures__/*.json",
    "src/**/__tests__/**/__snapshots__/*.snap",
    "src/**/__tests__/**/*.html",
    "src/**/__mocks__/**/*.ts",
])

BUILD_DEPS = [
    ":node_modules",
    "//:node_modules",  # Needed for @bufbuild/protobuf
    ":node_modules/level",  # Needed for leveldb
]

ts_config(
    name = "tsconfig",
    src = "tsconfig.json",
    deps = [
        "//clients:tsconfig",
        "//clients/sidecar/libs:tsconfig",
    ],
)

# target to compile the extension with integrated output structure
copy_to_directory(
    name = "build",
    srcs = [
        ":esbuild_js",
        "//clients/sidecar/libs:runtime_bundle",
    ],
    out = "out",
    replace_prefixes = {
        "build_js": "",
        "clients/sidecar/libs/runtime_prebuilds": "",
    },
)

# Internal esbuild target
esbuild(
    name = "esbuild_js",
    # Esbuild can build with json files, whereas ts_project cannot.
    srcs = SRC_FILES + [
        ":tsconfig",
    ],
    config = {
        "loader": {
            ".node": "file",
        },
    },
    data = ["//clients/sidecar/libs:runtime_bundle"],
    entry_point = "src/index.ts",
    external = [
    ],
    format = "cjs",
    minify = False,
    output = "build_js/index.js",
    platform = "node",
    target = "ES2022",
    tsconfig = ":tsconfig",
    deps = BUILD_DEPS,
)

jest_test(
    name = "test",
    timeout = "moderate",
    config = "jest.config.js",
    data = SRC_FILES + TEST_FILES + BUILD_DEPS + [
        "babel.config.js",
        ":build",
        ":tsconfig",
    ],
    include_transitive_types = True,  # Needed for type checking
    node_modules = ":node_modules",
)

# This target should never be depended on, instead rely on the :build target.
# This target is only for typechecking.
ts_project(
    name = "ts",
    srcs = SRC_FILES,
    no_emit = True,
    root_dir = "..",
    tsconfig = ":tsconfig",
    deps = BUILD_DEPS,
)

# Build test ensures that CI/CD runs the :ts build that checks typing
build_test(
    name = "build_test",
    targets = [
        ":ts",
        ":build",
    ],
)
