#!/bin/bash

# Build script for IntelliJ that includes native modules for LevelDB
# This script uses <PERSON><PERSON> to build the sidecar with all native dependencies
# and copies the output to the IntelliJ resources directory.

# Exit on any error
set -e

# Exit on undefined variable
set -u

# Exit if any command in a pipe fails
set -o pipefail

# Build the sidecar using the existing Bazel target (which already includes native modules)
bazel build //clients/sidecar/node-process:build

# Get the Bazel output directory
BAZEL_BIN=$(bazel info bazel-bin)
BUILD_OUTPUT="$BAZEL_BIN/clients/sidecar/node-process/out"

# Define the IntelliJ resources directory
INTELLIJ_DIR="$(dirname "$0")/../../../intellij/src/main/resources/sidecar"

# Create the IntelliJ resources directory if it doesn't exist
mkdir -p "$INTELLIJ_DIR"

# Remove existing files (handle read-only files from Bazel)
if [ -d "$INTELLIJ_DIR" ] && [ "$(ls -A "$INTELLIJ_DIR" 2>/dev/null)" ]; then
	chmod -R u+w "$INTELLIJ_DIR"/* 2>/dev/null || true
	rm -rf "${INTELLIJ_DIR:?}"/*
fi

# Copy the main JavaScript file
cp "$BUILD_OUTPUT/index.js" "$INTELLIJ_DIR/"

# Copy the native modules (prebuilds directory)
cp -r "$BUILD_OUTPUT/prebuilds" "$INTELLIJ_DIR/"

# Fix permissions on copied files (Bazel outputs are read-only)
chmod -R u+w "$INTELLIJ_DIR"

# Remove android prebuilds
rm -rf "$INTELLIJ_DIR/prebuilds/android-"*

# Verify that native modules were copied
if [ ! -f "$INTELLIJ_DIR/prebuilds/darwin-x64+arm64/classic-level.node" ]; then
	echo -e "✗ ARM64 macOS native module NOT found"
	exit 1
fi

# Verify that the main JavaScript file was copied
if [ ! -f "$INTELLIJ_DIR/index.js" ]; then
	echo -e "✗ Main JavaScript file not found"
	exit 1
fi

echo -e "✓ IntelliJ sidecar build completed successfully!"
echo -e "Output location: $INTELLIJ_DIR"
