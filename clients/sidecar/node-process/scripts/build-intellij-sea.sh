#!/bin/bash

##
## If you run into issues with "Multiple occurences of sentinel found in the binary"
## try installing node via nvm.
## Issue: https://github.com/nodejs/postject/issues/92
##

# Exit on any error
set -e

# Exit on undefined variable
set -u

# Exit if any command in a pipe fails
set -o pipefail

# Log commands run to stdout
set -x

## Build index.js
pnpm run build

## Clear output directory
rm -rf out-sea

pnpm dlx xsea out/index.js -o out-sea/sidecar-binary \
	-t linux-x64 \
	-t win-x64 \
	-t darwin-x64 \
	-t win-arm64 \
	-t darwin-arm64

## Mkdir for sidecar in IntelliJ
rm -rf ../../intellij/src/main/resources/sidecar
mkdir -p ../../intellij/src/main/resources/sidecar

## Copy to IntelliJ plugin resources
cp out/index.js ../../intellij/src/main/resources/sidecar/index.js
cp out-sea/sidecar-binary* ../../intellij/src/main/resources/sidecar
