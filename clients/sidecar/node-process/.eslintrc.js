const path = require("path");

module.exports = {
  root: true,
  extends: [
    "eslint:recommended",
    "plugin:@typescript-eslint/recommended-type-checked",
  ],
  env: {
    node: true,
  },
  parser: "@typescript-eslint/parser",
  parserOptions: {
    ecmaVersion: 6,
    sourceType: "module",
    project: true,
  },
  plugins: ["@typescript-eslint", "unused-imports"],
  rules: {
    "@typescript-eslint/no-unused-vars": [
      "error",
      {
        vars: "all",
        varsIgnorePattern: "^_",
        args: "after-used",
        argsIgnorePattern: "^_",
      },
    ],
  },
  ignorePatterns: [
    "out",
    "**/*.d.ts",
    ".eslintrc.js",
    "jest.config.js",
    "babel.config.js",
  ],
  overrides: [
    {
      extends: ["plugin:jest/recommended"],
      files: ["**/__tests__/**/*", "**/__mocks__/**/*", "**/*.test.*"],
      env: {
        jest: true,
      },
      rules: {
        // We use expect.any(<T>) in our tests which returns an `any` value.
        "@typescript-eslint/no-unsafe-assignment": "off",
      },
    },
  ],
};
