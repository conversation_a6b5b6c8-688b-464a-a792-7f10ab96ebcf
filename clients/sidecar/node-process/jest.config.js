const { pathsToModuleNameMapper } = require("ts-jest");
const { compilerOptions } = require("../../tsconfig");

module.exports = {
  roots: ["<rootDir>"],
  transform: {
    // eslint-disable-next-line @typescript-eslint/naming-convention
    "^.+\\.ts?$": ["ts-jest"],
    // This is needed to support using the generated protobuf js files
    // in our tests.
    "^.+\\.js$": "babel-jest",
  },
  testRegex: "(/__tests__/.*|(\\.|/)(test|spec))\\.test.ts?$",
  moduleFileExtensions: ["ts", "js", "json", "node"],
  collectCoverage: true,
  clearMocks: true,
  resetMocks: true,
  coverageDirectory: "coverage",
  testEnvironment: "node",
  setupFilesAfterEnv: [],
  // Any local modules that we wish to mock need to be added to this list.
  moduleNameMapper: pathsToModuleNameMapper(compilerOptions.paths, {
    prefix: "<rootDir>/../../",
  }),
};
