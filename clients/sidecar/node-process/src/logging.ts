import * as fs from "node:fs";
import { Logger, createLogger, format, transports } from "winston";
import * as os from "os";
import * as path from "path";
export { type AugmentLogger } from "@augment-internal/sidecar-libs/src/logging";
import {
  setLibraryLogger,
  type AugmentLogger,
} from "@augment-internal/sidecar-libs/src/logging";

// If a custom log file path is provided via command line, use that
// Otherwise, use the default location
let LOG_FILE: string;

// Check for --log-file argument
const logFileArgIndex = process.argv.findIndex((arg) => arg === "--log-file");
if (logFileArgIndex >= 0 && logFileArgIndex < process.argv.length - 1) {
  LOG_FILE = process.argv[logFileArgIndex + 1];
} else {
  // If running in the test environment, use a temporary directory for storage
  const XDG_STATE_HOME = process.env.TEST_TMPDIR
    ? path.join(process.env.TEST_TMPDIR, "state")
    : process.env.XDG_STATE_HOME || path.join(os.homedir(), ".local", "state");
  LOG_FILE = path.join(XDG_STATE_HOME, "augment", "sidecar-node.log");
}

let logger: Logger | undefined;

function singletonLogger(): Logger {
  if (logger) {
    return logger;
  }

  // Ensure the directory for the log file exists
  const logDir = path.dirname(LOG_FILE);
  if (!fs.existsSync(logDir)) {
    fs.mkdirSync(logDir, { recursive: true });
  }

  // Set the log level, defaulting to "info"
  let logLevel = (process.env.AUGMENT_LOG_LEVEL || "debug").toLowerCase();
  let logLevelWarning: string | undefined = undefined;
  if (
    !["trace", "verbose", "debug", "info", "warn", "error"].includes(logLevel)
  ) {
    logLevelWarning = `Environment variable AUGMENT_LOG_LEVEL set to invalid log level "${logLevel}". Defaulting to "info"`;
    logLevel = "info";
  }

  // Create Winston logger with file
  logger = createLogger({
    level: logLevel,
    exitOnError: false,
    format: format.combine(
      format.timestamp({
        format: "YYYY-MM-DD HH:mm:ss.SSS",
      }),
      format.printf(
        (info) =>
          // This is the same format line as vscode, so muting the complaint.
          // eslint-disable-next-line @typescript-eslint/restrict-template-expressions
          `${info.timestamp} [${info.level}] '${info.prefix}': ${info.message}`,
      ),
    ),
    transports: [new transports.File({ filename: LOG_FILE })],
  });

  // Log the warning if there was one. We do this here because the log level must be set before
  // the logger is initialized and the logger must be initialized before we can log anything.
  if (logLevelWarning) {
    logger.warn(logLevelWarning);
  }

  setLibraryLogger(logger);

  return logger;
}

export function getLogger(prefix: string): AugmentLogger {
  return singletonLogger().child({ prefix });
}
