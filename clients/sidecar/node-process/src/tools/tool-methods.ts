import { Connection } from "vscode-languageserver";
import { ToolsModel } from "@augment-internal/sidecar-libs/src/tools/tools-model";
import { getLogger } from "../logging";
import {
  ToolCallRequest,
  ToolCallResponse,
  ToolCancelRunRequest,
  ToolDefinition,
  ChatMode as ProtoChatMode,
  ChangeChatModeRequest,
  ToolsStateResponse,
  GetToolStatusForSettingsPanelResponse,
  ToolDefinitionWithSettings,
  ToolIdentifier,
  GetToolStatusForSettingsPanelRequest,
  SetMcpServersRequest,
  McpServerConfig as ProtoMcpServerConfig,
} from "$clients/sidecar/node-process/protos/tools_pb";
import { ChatRequestContentNodeType } from "$clients/sidecar/node-process/protos/chat_pb";
import {
  ToolDefinitionWithSettings as LibToolDefinitionWithSettings,
  ToolResponseContentNodeType,
  McpServerConfig as LibMcpServerConfig,
} from "@augment-internal/sidecar-libs/src/tools/tool-types";
import { getImageFormatFromMediaType } from "@augment-internal/sidecar-libs/src/chat/chat-utils";
import { ChatHistoryItem } from "$clients/sidecar/node-process/protos/chat_pb";
import {
  ChatMode,
  Exchange,
} from "@augment-internal/sidecar-libs/src/chat/chat-types";
import { JsonValue } from "@bufbuild/protobuf";
import {
  clientFeatureFlags,
  flagManagerEvents,
} from "../client-interfaces/client-feature-flags";
import { limitChatHistoryTruncate } from "@augment-internal/sidecar-libs/src/chat/chat-truncation";

const logger = getLogger("tool-methods");

function convertProtoMcpServerToLib(
  protoServer: ProtoMcpServerConfig,
): LibMcpServerConfig {
  // Determine server type - if type is explicitly set, use it
  // Otherwise, infer from available properties (backwards compatibility)
  const serverType = protoServer.type || (protoServer.url ? "http" : "stdio");

  if (serverType === "http" || serverType === "sse") {
    return {
      type: serverType,
      url: protoServer.url || "",
      timeoutMs: protoServer.timeoutMs,
      name: protoServer.name,
      disabled: protoServer.disabled,
    };
  } else {
    // Default to stdio
    return {
      type: "stdio",
      command: protoServer.command || "",
      args: protoServer.args,
      timeoutMs: protoServer.timeoutMs,
      env: protoServer.env,
      useShellInterpolation: protoServer.useShellInterpolation,
      name: protoServer.name,
      disabled: protoServer.disabled,
    };
  }
}

function toolToProtobufValidTool(
  tool: LibToolDefinitionWithSettings,
): ToolDefinitionWithSettings {
  return new ToolDefinitionWithSettings({
    ...tool,
    definition: new ToolDefinition({
      name: tool.definition.name.toString(),
      description: tool.definition.description,
      inputSchemaJson: tool.definition.input_schema_json,
      toolSafety: tool.definition.tool_safety.valueOf(),
      mcpServerName: tool.definition.mcp_server_name ?? "",
      mcpToolName: tool.definition.mcp_tool_name ?? "",
      originalMcpServerName: tool.definition.original_mcp_server_name ?? "",
    }),
    identifier: new ToolIdentifier({
      hostName: tool.identifier.hostName,
      toolId: tool.identifier.toolId.toString(),
    }),
    toolSafety: tool.toolSafety.valueOf(),
  });
}

export function registerToolMethods(
  connection: Connection,
  toolsModel: ToolsModel,
) {
  clientFeatureFlags.addListener(flagManagerEvents.FlagsChanged, () => {
    toolsModel.onFlagsChanged();
  });

  connection.onRequest(
    "augmentcode/tools/state",
    async (): Promise<JsonValue> => {
      try {
        // Get the current mode first
        const mode = toolsModel.chatMode;

        // Get the tool definitions - these should reflect the current mode
        const tools = await toolsModel.getToolDefinitions();

        logger.info(
          `Getting tool state. Mode: ${mode}, Tools: ${tools.length}`,
        );

        // Create the response with the current mode and tools
        const response = new ToolsStateResponse({
          mode:
            mode === ChatMode.agent ? ProtoChatMode.AGENT : ProtoChatMode.CHAT,
          tools: tools.map((tool) => {
            return new ToolDefinition({
              name: tool.definition.name.toString(),
              description: tool.definition.description,
              inputSchemaJson: tool.definition.input_schema_json,
              toolSafety: tool.definition.tool_safety.valueOf(),
            });
          }),
        });

        return response.toJson();
      } catch (error) {
        logger.error(
          `Error getting tool state: ${error instanceof Error ? error.message : String(error)}`,
        );
        // Return a default response in case of error
        return new ToolsStateResponse({
          mode:
            toolsModel.chatMode === ChatMode.agent
              ? ProtoChatMode.AGENT
              : ProtoChatMode.CHAT,
          tools: [],
        }).toJson();
      }
    },
  );

  connection.onRequest(
    "augmentcode/tools/get-tool-status-for-settings-panel",
    async (params: JsonValue): Promise<JsonValue> => {
      logger.info(`Getting tool status for settings panel....`);
      const request = GetToolStatusForSettingsPanelRequest.fromJson(params, {
        // This is needed so that @type fields from intellij are ignored
        ignoreUnknownFields: true,
      });
      const tools = await toolsModel.getToolStatusForSettingsPanel(
        request.useCache,
      );
      return new GetToolStatusForSettingsPanelResponse({
        tools: tools.map(toolToProtobufValidTool),
      }).toJson();
    },
  );

  connection.onRequest(
    "augmentcode/tools/call",
    async (params: JsonValue): Promise<JsonValue> => {
      logger.info(`Received augmentcode/tools/call request.`);
      const request = ToolCallRequest.fromJson(params, {
        // This is needed so that @type fields from intellij are ignored
        ignoreUnknownFields: true,
      });
      logger.info(`Calling ${request.name} tool`);

      try {
        const history = request.history.map(
          (item: ChatHistoryItem): Exchange => {
            return {
              request_message: item.requestMessage,
              response_text: item.responseText,
              request_id: item.requestId,
              // request_nodes?: ChatRequestNode[],
              // response_nodes?: ChatResultNode[],
            };
          },
        );
        const limitedHistory = limitChatHistoryTruncate(history);

        const result = await toolsModel.callTool(
          request.requestId,
          request.toolUseId,
          request.name,
          (request.input?.toJson() ?? {}) as Record<string, unknown>,
          limitedHistory,
          request.conversationId,
        );
        logger.info(
          `Called ${request.name} tool and got result: ${JSON.stringify(result)}`,
        );

        // Convert the content nodes to protobufs types
        const toolResultContentNodes = !result.contentNodes
          ? undefined
          : result.contentNodes.map((content) => {
              if (content.type === ToolResponseContentNodeType.ContentText) {
                return {
                  /* eslint-disable @typescript-eslint/naming-convention */
                  type: ChatRequestContentNodeType.CONTENT_TEXT,
                  text_content: content.text_content,
                  /* eslint-enable @typescript-eslint/naming-convention */
                };
              } else if (
                content.type === ToolResponseContentNodeType.ContentImage &&
                content.image_content
              ) {
                return {
                  /* eslint-disable @typescript-eslint/naming-convention */
                  type: ChatRequestContentNodeType.CONTENT_IMAGE,
                  image_content: {
                    image_data: content.image_content.image_data,
                    format: getImageFormatFromMediaType(
                      content.image_content.media_type,
                    ),
                  },
                  /* eslint-enable @typescript-eslint/naming-convention */
                };
              }
              return {
                /* eslint-disable @typescript-eslint/naming-convention */
                type: ChatRequestContentNodeType.CONTENT_TEXT,
                text_content: "[Error: Invalid content node]",
                /* eslint-enable @typescript-eslint/naming-convention */
              };
            });

        // Convert ToolUseResponse to ToolCallResponse (protobuf type)
        return new ToolCallResponse({
          text: result.text,
          isError: result.isError,
          contentNodes: toolResultContentNodes,
        }).toJson();
      } catch (err: unknown) {
        logger.warn(
          `Failed to call tool: ${err instanceof Error ? err.message : (err as string)}`,
        );
        return new ToolCallResponse({
          text: `Failed to call tool: ${err instanceof Error ? err.message : (err as string)}`,
          isError: true,
        }).toJson();
      }
    },
  );

  connection.onRequest(
    "augmentcode/tools/cancel-run",
    async (params: JsonValue): Promise<void> => {
      logger.info(`Calling cancel-run....: ${JSON.stringify(params, null, 2)}`);
      try {
        const request = ToolCancelRunRequest.fromJson(params, {
          // This is needed so that @type fields from intellij are ignored
          ignoreUnknownFields: true,
        });
        logger.info(
          `Calling cancel-run....parsed proto: ${request.toJsonString()}`,
        );
        await toolsModel.cancelToolRun(request.requestId, request.toolUseId);
      } catch (err: unknown) {
        logger.warn(
          `Failed to cancel tool run: ${err instanceof Error ? err.message : (err as string)}`,
        );
      }
    },
  );

  connection.onRequest(
    "augmentcode/tools/change-chat-mode",
    (params: JsonValue): Promise<void> => {
      logger.info(
        `Calling change-chat-mode....: ${JSON.stringify(params, null, 2)}`,
      );
      try {
        const request = ChangeChatModeRequest.fromJson(params, {
          // This is needed so that @type fields from intellij are ignored
          ignoreUnknownFields: true,
        });
        logger.info(
          `Calling change-chat-mode....parsed proto: ${request.toJsonString()}`,
        );

        toolsModel.setMode(
          request.mode === ProtoChatMode.AGENT ? ChatMode.agent : ChatMode.chat,
        );
      } catch (err: unknown) {
        logger.warn(
          `Failed to change chat mode: ${err instanceof Error ? err.message : (err as string)}`,
        );
      }
      return Promise.resolve();
    },
  );

  connection.onRequest(
    "augmentcode/tools/set-mcp-servers",
    (params: JsonValue): Promise<void> => {
      logger.info(`setting mcp servers....` + JSON.stringify(params, null, 2));
      const request = SetMcpServersRequest.fromJson(params, {
        // This is needed so that @type fields from intellij are ignored
        ignoreUnknownFields: true,
      });
      const libMcpServers = request.mcpServers.map(convertProtoMcpServerToLib);
      toolsModel.setMcpServers(libMcpServers);
      return Promise.resolve();
    },
  );

  connection.onRequest(
    "augmentcode/tools/generate-mcp-oauth-url",
    async (params: JsonValue): Promise<JsonValue> => {
      logger.info(
        `Generating MCP OAuth URL: ${JSON.stringify(params, null, 2)}`,
      );
      try {
        const { mcpName } = params as { mcpName: string };
        if (!mcpName || typeof mcpName !== "string") {
          throw new Error("mcpName is required and must be a string");
        }

        const oauthUrl = await toolsModel.generateMcpOAuthUrl(mcpName);
        return { oauthUrl };
      } catch (error) {
        logger.error(`Failed to generate MCP OAuth URL: ${String(error)}`);
        throw error;
      }
    },
  );

  return toolsModel;
}
