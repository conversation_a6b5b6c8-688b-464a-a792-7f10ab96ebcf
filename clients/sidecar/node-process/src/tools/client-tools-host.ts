import {
  Chat<PERSON><PERSON>,
  Exchange,
} from "@augment-internal/sidecar-libs/src/chat/chat-types";
import { IToolHost } from "@augment-internal/sidecar-libs/src/tools/tool-host";
import {
  ITool,
  ToolHostBase,
} from "@augment-internal/sidecar-libs/src/tools/tool-host-base";
import {
  LocalToolType,
  ToolHostName,
  ToolHostOptions,
  ToolUseResponse,
} from "@augment-internal/sidecar-libs/src/tools/tool-types";
import { errorToolResponse } from "@augment-internal/sidecar-libs/src/tools/sidecar-tools/tool-use-response";
import { LaunchProcessTool } from "./client-tools/launch-process-tool";
import { ReadProcessTool } from "./client-tools/read-process-tool";
import { KillProcessTool } from "./client-tools/kill-process-tool";
import { WriteProcessTool } from "./client-tools/write-process-tool";
import { ListProcessesTool } from "./client-tools/list-processes-tools";

/**
 * IMPORTANT:
 * This class is in a weird/hacky state. We used to rely on IntelliJ
 * intercepting messages from webview and node-process and appending
 * client tools or responding to messages for client tools.
 *
 * Since then, webview has added methods and we need to share details
 * about tools etc.
 */
export class ClientTools extends ToolHostBase<LocalToolType> {
  constructor(private _options: ToolHostOptions) {
    const { chatMode: _chatMode } = _options;
    const tools: ITool<LocalToolType>[] = [];
    if (_chatMode === ChatMode.agent) {
      tools.push(
        new LaunchProcessTool(),
        new ReadProcessTool(),
        new KillProcessTool(),
        new WriteProcessTool(),
        new ListProcessesTool(),
      );
    }
    super(tools, ToolHostName.localToolHost);
  }

  callTool(
    _requestId: string,
    _toolUseId: string,
    _toolName: string,
    _toolInput: Record<string, unknown>,
    _chatHistory: Exchange[],
  ): Promise<ToolUseResponse> {
    return Promise.resolve(
      errorToolResponse("Expected client to implement tool."),
    );
  }

  factory(_preconditionWait: Promise<void>): IToolHost {
    return new ClientTools(this._options);
  }
}
