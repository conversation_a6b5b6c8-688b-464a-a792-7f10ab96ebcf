import { Exchange } from "@augment-internal/sidecar-libs/src/chat/chat-types";
import { errorToolResponse } from "@augment-internal/sidecar-libs/src/tools/sidecar-tools/tool-use-response";
import {
  LocalToolType,
  ToolBase,
  ToolSafety,
  ToolUseResponse,
} from "@augment-internal/sidecar-libs/src/tools/tool-types";

/**
 * A tool that kills a process.
 * Note that this only kills processes launched with the launch_process tool.
 */
export class KillProcessTool extends ToolBase<LocalToolType> {
  constructor() {
    super(LocalToolType.killProcess, ToolSafety.Safe);
  }

  public readonly description: string = "Kill a process by its process ID.";

  public readonly inputSchemaJson: string = JSON.stringify({
    /* eslint-disable @typescript-eslint/naming-convention */
    type: "object",
    properties: {
      terminal_id: {
        type: "integer",
        description: "Process ID to kill.",
      },
    },
    required: ["terminal_id"],
    /* eslint-enable @typescript-eslint/naming-convention */
  });

  public checkToolCallSafe(_toolInput: Record<string, unknown>): boolean {
    return true;
  }

  public call(
    _toolInput: Record<string, unknown>,
    _chatHistory: Exchange[],
    _abortSignal: AbortSignal,
    _toolUseId: string,
  ): Promise<ToolUseResponse> {
    return Promise.resolve(
      errorToolResponse(`Expected client to implement save file tool.`),
    );
  }
}
