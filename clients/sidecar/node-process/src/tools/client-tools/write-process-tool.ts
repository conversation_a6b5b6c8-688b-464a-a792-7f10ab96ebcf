import { Exchange } from "@augment-internal/sidecar-libs/src/chat/chat-types";
import { errorToolResponse } from "@augment-internal/sidecar-libs/src/tools/sidecar-tools/tool-use-response";
import {
  LocalToolType,
  ToolBase,
  ToolSafety,
  ToolUseResponse,
} from "@augment-internal/sidecar-libs/src/tools/tool-types";

/**
 * A tool that writes input to a process's stdin.
 * Note that this only writes to processes launched with the launch_process tool.
 */
export class WriteProcessTool extends ToolBase<LocalToolType> {
  constructor() {
    super(LocalToolType.writeProcess, ToolSafety.Safe);
  }

  public readonly description: string = "Write input to a process's stdin.";

  public readonly inputSchemaJson: string = JSON.stringify({
    /* eslint-disable @typescript-eslint/naming-convention */
    type: "object",
    properties: {
      terminal_id: {
        type: "integer",
        description: "Process ID to write to.",
      },
      input_text: {
        type: "string",
        description: "Text to write to the process's stdin.",
      },
    },
    required: ["terminal_id", "input_text"],
    /* eslint-enable @typescript-eslint/naming-convention */
  });

  public checkToolCallSafe(_toolInput: Record<string, unknown>): boolean {
    return true;
  }

  public call(
    _toolInput: Record<string, unknown>,
    _chatHistory: Exchange[],
    _abortSignal: AbortSignal,
    _toolUseId: string,
  ): Promise<ToolUseResponse> {
    return Promise.resolve(
      errorToolResponse(`Expected client to implement save file tool.`),
    );
  }
}
