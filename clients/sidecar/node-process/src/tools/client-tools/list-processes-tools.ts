import { Exchange } from "@augment-internal/sidecar-libs/src/chat/chat-types";
import { errorToolResponse } from "@augment-internal/sidecar-libs/src/tools/sidecar-tools/tool-use-response";
import {
  LocalToolType,
  ToolBase,
  ToolSafety,
  ToolUseResponse,
} from "@augment-internal/sidecar-libs/src/tools/tool-types";

/**
 * A tool that lists all known processes and their states.
 * Note that this only lists processes launched with the launch_process tool.
 */
export class ListProcessesTool extends ToolBase<LocalToolType> {
  constructor() {
    super(LocalToolType.listProcesses, ToolSafety.Safe);
  }

  public readonly description: string =
    "List all known processes and their states.";

  public readonly inputSchemaJson: string = JSON.stringify({
    /* eslint-disable @typescript-eslint/naming-convention */
    type: "object",
    properties: {},
    required: [],
    /* eslint-enable @typescript-eslint/naming-convention */
  });

  public checkToolCallSafe(_toolInput: Record<string, unknown>): boolean {
    return true;
  }

  public call(
    _toolInput: Record<string, unknown>,
    _chatHistory: Exchange[],
    _abortSignal: AbortSignal,
    _toolUseId: string,
  ): Promise<ToolUseResponse> {
    return Promise.resolve(
      errorToolResponse(`Expected client to implement save file tool.`),
    );
  }
}
