import {
  RemoteInfoSource,
  RemoteToolDefinition,
  RemoteToolResponseStatus,
  RunRemoteToolResult,
} from "@augment-internal/sidecar-libs/src/tools/remote-tools/remote-tool-host";
import {
  convertIntToToolSafety,
  RemoteToolId,
} from "@augment-internal/sidecar-libs/src/tools/tool-types";
import { Connection } from "vscode-languageserver";
import { sendRequestWithTimeout } from "../connection-utils";
import {
  RetrieveRemoteToolsResponse,
  CallRemoteToolRequest,
  CallRemoteToolResponse,
  FilterToolsWithExtraInputResponse,
  FilterToolsWithExtraInputRequest,
  RetrieveRemoteToolsRequest,
} from "$clients/sidecar/node-process/protos/tools_pb";
import { getLogger } from "../logging";

export class NodeProcessRemoteInfo implements RemoteInfoSource {
  private logger = getLogger("NodeProcessRemoteInfo");

  constructor(private readonly _connection: Connection) {}

  public async retrieveRemoteTools(
    supportedTools: RemoteToolId[],
  ): Promise<RemoteToolDefinition[]> {
    this.logger.info("Requesting remote tools from host");
    const jsonResponse = await sendRequestWithTimeout(
      this._connection,
      "augmentcode/tools/retrieve-remote-tools",
      new RetrieveRemoteToolsRequest({
        supportedTools,
      }).toJson(),
    );
    if (!jsonResponse) {
      return [];
    }
    try {
      const response = RetrieveRemoteToolsResponse.fromJson(jsonResponse, {
        // This is needed so that @type fields from intellij are ignored
        ignoreUnknownFields: true,
      });
      return response.tools
        .filter((tool) => tool.toolDefinition !== undefined)
        .map((tool): RemoteToolDefinition => {
          return {
            toolDefinition: {
              name: tool.toolDefinition!.name,
              description: tool.toolDefinition!.description,
              input_schema_json: tool.toolDefinition!.inputSchemaJson,
              tool_safety: convertIntToToolSafety(
                tool.toolDefinition!.toolSafety,
              ),
            },
            remoteToolId: tool.remoteToolId,
            availabilityStatus: tool.availabilityStatus,
            toolSafety: convertIntToToolSafety(tool.toolSafety),
            oauthUrl: tool.oauthUrl,
          };
        });
    } catch (err) {
      this.logger.warn(
        `Failed to parse retrieve remote tools response: ${err instanceof Error ? err.message : (err as string)}`,
      );
      return [];
    }
  }

  public async filterToolsWithExtraInput(
    toolIds: RemoteToolId[],
  ): Promise<Set<RemoteToolId>> {
    const jsonResponse = await sendRequestWithTimeout(
      this._connection,
      "augmentcode/tools/filter-tool-with-extra-input",
      new FilterToolsWithExtraInputRequest({
        toolIds,
      }).toJson(),
    );

    if (!jsonResponse) {
      return new Set();
    }

    try {
      const response = FilterToolsWithExtraInputResponse.fromJson(
        jsonResponse,
        {
          // This is needed so that @type fields from intellij are ignored
          ignoreUnknownFields: true,
        },
      );
      return new Set(response.toolIds);
    } catch (err) {
      this.logger.warn(
        `Failed to parse filter tools response: ${err instanceof Error ? err.message : (err as string)}`,
      );
      return new Set();
    }
  }

  public async runRemoteTool(
    requestId: string,
    toolName: string,
    toolInputJson: string,
    toolId: RemoteToolId,
    _signal: AbortSignal,
  ): Promise<RunRemoteToolResult> {
    const jsonResponse = await sendRequestWithTimeout(
      this._connection,
      "augmentcode/tools/call-remote-tool",
      new CallRemoteToolRequest({
        name: toolName,
        // Stringify here to reduce the risk of integers being parsed as doubles.
        input: toolInputJson,
        toolId: toolId,
        requestId: requestId,
      }).toJson(),
    );
    if (!jsonResponse) {
      return {
        toolOutput: "",
        toolResultMessage: "Failed to run remote tool: no response from host",
        status: RemoteToolResponseStatus.ExecutionUnknownStatus,
      };
    }
    try {
      const response = CallRemoteToolResponse.fromJson(jsonResponse, {
        // This is needed so that @type fields from intellij are ignored
        ignoreUnknownFields: true,
      });
      return {
        toolOutput: response.toolOutput,
        toolResultMessage: response.toolResultMessage,
        status: response.status,
      };
    } catch (err) {
      this.logger.warn(
        `Failed to parse retrieve remote tools response: ${err instanceof Error ? err.message : (err as string)}`,
      );
      return {
        toolOutput: "",
        toolResultMessage: `Failed to run remote tool: ${err instanceof Error ? err.message : (err as string)}`,
        status: RemoteToolResponseStatus.ExecutionError,
      };
    }
  }
}
