import { LSPClient } from "./lsp-client";

export class MockClient {
  private currentChatMode = 0; // ChatMode.CHAT
  constructor(private client: LSPClient) {
    // Register all request handlers with direct function references to ensure consistent behavior
    this.client.onRequest("augmentcode/client-workspaces/getcwd", () =>
      this.getCwd(),
    );
    this.client.onRequest("augmentcode/tools/retrieve-remote-tools", () =>
      this.retrieveRemoteTools(),
    );
    this.client.onRequest(
      "augmentcode/tools/filter-tool-with-extra-input",
      () => this.filteToolsWithExtraInput(),
    );

    // Register the tools state handler with a direct implementation to ensure consistent behavior
    this.client.onRequest("augmentcode/tools/state", () => {
      console.log("Mock client handling augmentcode/tools/state request");
      if (this.currentChatMode === 0) {
        // ChatMode.CHAT
        return {
          mode: 0, // ChatMode.CHAT
          tools: [
            {
              name: "web-fetch",
              description: "Web fetch tool description",
              inputSchemaJson: "{}",
              toolSafety: 0, // ToolSafety.UNSAFE
            },
            {
              name: "codebase-retrieval",
              description: "Codebase retrieval tool description",
              inputSchemaJson: "{}",
              toolSafety: 1, // ToolSafety.SAFE
            },
          ],
        };
      } else {
        return {
          mode: 1, // ChatMode.AGENT
          tools: [],
        };
      }
    });

    this.client.onRequest("augmentcode/tools/change-chat-mode", (params) =>
      this.changeChatMode(params),
    );
  }

  getCwd() {
    return "/example/path/to/cwd";
  }

  retrieveRemoteTools() {
    return {
      tools: [],
    };
  }

  filteToolsWithExtraInput() {
    return {
      tool_ids: [],
    };
  }

  getToolsState() {
    if (this.currentChatMode === 0) {
      // ChatMode.CHAT
      // Return the exact structure expected by the test
      return {
        mode: 0, // ChatMode.CHAT
        tools: [
          {
            name: "web-fetch",
            description: "Web fetch tool description",
            inputSchemaJson: "{}",
            toolSafety: 0, // ToolSafety.UNSAFE
          },
          {
            name: "codebase-retrieval",
            description: "Codebase retrieval tool description",
            inputSchemaJson: "{}",
            toolSafety: 1, // ToolSafety.SAFE
          },
        ],
      };
    } else {
      // Return agent mode tools
      return {
        mode: 1, // ChatMode.AGENT
        tools: [],
      };
    }
  }

  changeChatMode(params: unknown) {
    const mode = (params as { mode: number }).mode;
    this.currentChatMode = mode;
    return null;
  }
}
