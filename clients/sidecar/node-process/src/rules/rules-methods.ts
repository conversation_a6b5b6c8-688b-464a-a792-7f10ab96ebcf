import { Connection } from "vscode-languageserver/node";
import { JsonValue } from "@bufbuild/protobuf";
import { getLogger } from "../logging";
import { RulesService } from "@augment-internal/sidecar-libs/src/chat/rules-service";
import {
  Rule as InternalRule,
  RuleType as InternalRuleType,
} from "@augment-internal/sidecar-libs/src/chat/chat-types";
import {
  LoadRulesRequest as ProtoLoadRulesRequest,
  LoadRulesResponse as ProtoLoadRulesResponse,
  Rule as ProtoRule,
} from "$clients/sidecar/node-process/protos/client-workspaces_pb";

const logger = getLogger("rules-methods");

/**
 * Register rules-related RPC methods with the LSP connection
 */
export function registerRulesMethods(
  connection: Connection,
  rulesService: RulesService,
) {
  connection.onRequest(
    "augmentcode/rules/loadRules",
    async (params: JsonValue): Promise<JsonValue> => {
      logger.info("Received augmentcode/rules/loadRules request");

      try {
        // Parse the entire request using proto fromJson method
        // Use ignoreUnknownFields to handle @type fields from IntelliJ clients
        const protoRequest = ProtoLoadRulesRequest.fromJson(params, {
          ignoreUnknownFields: true,
        });

        logger.debug(
          `Loading rules with includeGuidelines=${protoRequest.includeGuidelines}, contextRules=${protoRequest.contextRules.length}`,
        );

        // Convert proto context rules to internal format
        const contextRules: InternalRule[] = protoRequest.contextRules.map(
          (protoRule) => ({
            type: protoRule.type as InternalRuleType,
            path: protoRule.path,
            content: protoRule.content,
          }),
        );

        // Load rules using the rules service
        const rules = await rulesService.loadRules({
          includeGuidelines: protoRequest.includeGuidelines,
          contextRules,
        });

        logger.debug(`Loaded ${rules.length} rules`);

        // Convert internal rules to proto Rule objects
        const protoRules = rules.map(
          (rule) =>
            new ProtoRule({
              type: rule.type, // Use the enum value directly (0 = ALWAYS_ATTACHED, 1 = MANUAL, 2 = AGENT_REQUESTED)
              path: rule.path,
              content: rule.content,
              description: rule.description,
            }),
        );

        // Create and return proto LoadRulesResponse
        const response = new ProtoLoadRulesResponse({
          rules: protoRules,
        });

        return response.toJson();
      } catch (error) {
        logger.error("Failed to load rules", error);
        throw error;
      }
    },
  );
}
