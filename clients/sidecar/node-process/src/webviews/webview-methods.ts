import { Connection } from "vscode-languageserver";
import { JsonValue } from "@bufbuild/protobuf";
import {
  ProcessWebviewMessageRequest,
  ProcessWebviewMessageResponse,
} from "$clients/sidecar/node-process/protos/sidecarrpc_pb";
import { getWebviewMessaging } from "@augment-internal/sidecar-libs/src/client-interfaces/webview-messaging";
import { WebViewMessage } from "@augment-internal/sidecar-libs/src/webview-messages/common-webview-messages";
import { MessageConsumerTypes } from "@augment-internal/sidecar-libs/src/webview-messages/webview-messaging";

export function registerWebviewMethods(connection: Connection) {
  connection.onRequest(
    "augmentcode/webview-message",
    async (params: JsonValue) => {
      const request = ProcessWebviewMessageRequest.fromJson(params, {
        // This is needed so that @type fields from intellij are ignored
        ignoreUnknownFields: true,
      });
      const msg = JSON.parse(
        request.message,
      ) as WebViewMessage<MessageConsumerTypes>;
      const result = await new Promise((resolve) => {
        // NOTE: The callback function here seems unnecessary, can it be removed?
        getWebviewMessaging().onMessage(msg, (response) => {
          resolve(response);
        });
      });
      return new ProcessWebviewMessageResponse({
        message: JSON.stringify(result),
      }).toJson();
    },
  );
}
