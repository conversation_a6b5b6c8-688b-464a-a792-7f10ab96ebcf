import { IClientActions } from "@augment-internal/sidecar-libs/src/client-interfaces/client-actions";
import { ShowDiffViewOptions } from "@augment-internal/sidecar-libs/src/client-interfaces/client-actions-types";
import { IQualifiedPathName } from "@augment-internal/sidecar-libs/src/workspace/workspace-types";
import { Connection } from "vscode-languageserver";
import { sendRequestWithTimeout } from "../connection-utils";
import { getLogger } from "../logging";
import { ShowDiffViewRequest } from "$clients/sidecar/node-process/protos/client-actions_pb";

export class ClientActions implements IClientActions {
  private _logger = getLogger("ClientActions");

  constructor(private readonly _connection: Connection) {}

  async showDiffView(
    path: IQualifiedPathName,
    original: string | undefined,
    modified: string | undefined,
    opts: ShowDiffViewOptions,
  ): Promise<void> {
    this._logger.info(`Requesting diff view: ${path.relPath}`);
    await sendRequestWithTimeout(
      this._connection,
      "augmentcode/client-actions/showDiffView",
      new ShowDiffViewRequest({ path, original, modified, opts }).toJson(),
    );
    this._logger.info(`Show diff view complete`);
  }
}
