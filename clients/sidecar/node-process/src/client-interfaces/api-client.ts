import {
  AgentCodebaseRetrievalRequest,
  AgentCodebaseRetrievalResponse,
  AgentInterruptionData,
  AgentRequestEventData,
  AgentRequestEvent as AgentRequestEventProto,
  AgentSessionEvent as AgentSessionEventProto,
  AgentTracingData,
  ChatHistorySummarizationData,
  FirstTokenTimingData,
  CheckToolSafetyRequest,
  CheckToolSafetyResponse,
  ContentTruncationData,
  EventData,
  GenericTracingData,
  InitialOrientationData,
  LogAgentRequestEvent,
  LogAgentSessionEvent,
  MemoriesFileOpenData,
  MemoryUsageData as MemoryUsageDataProto,
  ModelSelectionChangeData,
  RememberToolCallData,
  StringStats as StringStatsProto,
  TimedStringStats,
} from "$clients/sidecar/node-process/protos/api-client_pb";
import {
  ChatHistoryItem,
  ChatStreamRequest,
  ChatStreamResponse,
} from "$clients/sidecar/node-process/protos/chat_pb";
import {
  ChatMode,
  ChatPayload,
  ChatRequestNode,
  Exchange,
} from "@augment-internal/sidecar-libs/src/chat/chat-types";
import { IAPIClient } from "@augment-internal/sidecar-libs/src/client-interfaces/api-client";
import {
  AgentCodebaseRetrievalOptions,
  AgentCodebaseRetrievalResult,
  AgentRequestEvent,
  AgentSessionEvent,
  ChatResult,
  RemoteAgentSessionEvent,
  ToolUseRequestEvent,
} from "@augment-internal/sidecar-libs/src/client-interfaces/api-client-types";
import {
  ClassifyAndDistillDebugFlag,
  FlushMemoriesDebugFlag,
  InitialOrientationDebugFlag,
  RememberToolCallDebugFlag,
  StringStats,
  Timed,
} from "@augment-internal/sidecar-libs/src/metrics/types";
import {
  RemoteToolId,
  ToolDefinition,
} from "@augment-internal/sidecar-libs/src/tools/tool-types";
import { JsonValue } from "@bufbuild/protobuf";
import { Connection, ProgressType } from "vscode-languageserver";
import { getLogger } from "../logging";

// Timeout for chat stream requests after which we consider the stream closed
// This is important in case the plugin fails to close the connection
const CHAT_STREAM_TIMEOUT_MS = 3 * 60 * 1000; // 3 min

export class APIClient implements IAPIClient {
  private _logger = getLogger("APIClient");

  constructor(private _connection: Connection) {}

  chatStream(
    message: string,
    requestId: string,
    chatHistory: Exchange[],
    toolDefinitions: ToolDefinition[],
    requestNodes: ChatRequestNode[],
    mode: ChatMode,
    modelId: string | undefined,
    silent: boolean,
  ): Promise<AsyncIterable<ChatResult>> {
    this._logger.info(
      `Invocation of chatStream with message: ${message}, requestId: ${requestId}`,
    );

    // Construct chat payload
    const chatPayload: ChatPayload = {
      message,
      chat_history: chatHistory,
      tool_definitions: toolDefinitions,
      nodes: requestNodes,
      mode: mode,
      model: modelId,
      silent: silent,
      // NOTE: For our current use case in the llmChatStream call in the remember tool,
      //       we don't use actual blobs state. In the future we may want to update
      blobs: { checkpoint_id: undefined, added_blobs: [], deleted_blobs: [] },
      vcs_change: { working_directory_changes: [] },
    };
    const chatPayloadJson = JSON.stringify(chatPayload);

    // Create a unique token for this stream
    const progressToken = `chat-stream-${requestId}`;

    // Create a response queue and control variables
    const responseQueue: ChatStreamResponse[] = [];
    let streamEnded = false;
    let error: Error | null = null;

    // Listen for chat response from plugin. Append responses to the queue
    const disposable = this._connection.onProgress(
      new ProgressType<ChatStreamResponse>(),
      progressToken,
      (params: ChatStreamResponse) => {
        responseQueue.push(params);
      },
    );

    // Track whether the disposable has been disposed
    let isDisposed = false;
    const safeDispose = () => {
      if (!isDisposed) {
        disposable.dispose();
        isDisposed = true;
      }
    };

    try {
      // set up chatstream request with request id and message based on proto
      const request = new ChatStreamRequest({
        progressToken,
        requestPayloadJson: chatPayloadJson,
      });

      this._connection
        .sendRequest("augmentcode/api-client/chat-stream", request.toJson())
        .then(() => {
          streamEnded = true;
        })
        .catch((err) => {
          // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
          error = err;
          streamEnded = true;
        });

      // Close stream after a timeout
      setTimeout(() => {
        streamEnded = true;
      }, CHAT_STREAM_TIMEOUT_MS);

      const logger = this._logger;
      return Promise.resolve(
        (async function* () {
          try {
            // Keep yielding responses until the stream is ended and the queue is empty
            while (!streamEnded || responseQueue.length > 0) {
              if (responseQueue.length > 0) {
                try {
                  const response = responseQueue.shift()!;

                  yield JSON.parse(response.responsePayloadJson) as ChatResult;
                } catch (err) {
                  logger.error(
                    `Error parsing chat stream response: ${err as string}`,
                  );
                  throw new Error(
                    `Failed to parse chat stream response: ${err as string}`,
                  );
                }
              } else if (!streamEnded) {
                // Wait a bit before checking again
                await new Promise((resolve) => setTimeout(resolve, 50));
              }
            }

            if (error) {
              logger.error(`Error in chatStream: ${error as string}`);
              throw error;
            }
          } finally {
            // Clean up the progress handler
            safeDispose();
          }
        })(),
      );
    } catch (err) {
      // Make sure we dispose of the progress handler if there's an error
      this._logger.error(`Error in chatStream: ${err as string}`);
      safeDispose();
      throw err;
    }
  }

  logToolUseRequestEvent(_events: ToolUseRequestEvent[]): Promise<void> {
    throw new Error("Method not implemented.");
  }

  async logAgentRequestEvent(events: AgentRequestEvent[]): Promise<void> {
    await this._connection.sendRequest(
      "augmentcode/api-client/log-agent-request-event",
      new LogAgentRequestEvent({
        events: events.map((e: AgentRequestEvent): AgentRequestEventProto => {
          const eventDataPayload = new AgentRequestEventData({
            chatHistorySummarizationData: e.event_data
              ?.chat_history_summarization_data
              ? new ChatHistorySummarizationData({
                  totalHistoryCharCount:
                    e.event_data.chat_history_summarization_data
                      .total_history_char_count,
                  totalHistoryExchangeCount:
                    e.event_data.chat_history_summarization_data
                      .total_history_exchange_count,
                  headCharCount:
                    e.event_data.chat_history_summarization_data
                      .head_char_count,
                  headExchangeCount:
                    e.event_data.chat_history_summarization_data
                      .head_exchange_count,
                  headLastRequestId:
                    e.event_data.chat_history_summarization_data
                      .head_last_request_id,
                  tailCharCount:
                    e.event_data.chat_history_summarization_data
                      .tail_char_count,
                  tailExchangeCount:
                    e.event_data.chat_history_summarization_data
                      .tail_exchange_count,
                  tailLastRequestId:
                    e.event_data.chat_history_summarization_data
                      .tail_last_request_id,
                  summaryCharCount:
                    e.event_data.chat_history_summarization_data
                      .summary_char_count,
                  summarizationDurationMs:
                    e.event_data.chat_history_summarization_data
                      .summarization_duration_ms,
                  isCacheAboutToExpire:
                    e.event_data.chat_history_summarization_data
                      .is_cache_about_to_expire,
                  isAborted:
                    e.event_data.chat_history_summarization_data?.is_aborted,
                })
              : undefined,
            firstTokenTimingData: e.event_data?.first_token_timing_data
              ? new FirstTokenTimingData({
                  userMessageSentTimestampMs: BigInt(
                    e.event_data.first_token_timing_data
                      .user_message_sent_timestamp_ms,
                  ),
                  firstTokenReceivedTimestampMs: BigInt(
                    e.event_data.first_token_timing_data
                      .first_token_received_timestamp_ms,
                  ),
                  timeToFirstTokenMs:
                    e.event_data.first_token_timing_data.time_to_first_token_ms,
                })
              : undefined,
          });

          return new AgentRequestEventProto({
            eventTimeSec: e.event_time_sec,
            eventTimeNsec: e.event_time_nsec,
            eventName: e.event_name,
            conversationId: e.conversation_id,
            requestId: e.request_id,
            chatHistoryLength: e.chat_history_length,
            eventData: eventDataPayload,
          });
        }),
      }).toJson(),
    );
  }

  async logAgentSessionEvent(_events: AgentSessionEvent[]): Promise<void> {
    await this._connection.sendRequest(
      "augmentcode/api-client/log-agent-session-event",
      new LogAgentSessionEvent({
        events: _events.map((e: AgentSessionEvent): AgentSessionEventProto => {
          return new AgentSessionEventProto({
            eventTimeSec: e.event_time_sec,
            eventTimeNsec: e.event_time_nsec,
            eventName: e.event_name,
            conversationId: e.conversation_id,
            eventData: e.event_data
              ? new EventData({
                  agentInterruptionData: e.event_data?.agent_interruption_data
                    ? new AgentInterruptionData({
                        requestId:
                          e.event_data?.agent_interruption_data?.request_id,
                        currConversationLength:
                          e.event_data?.agent_interruption_data
                            ?.curr_conversation_length,
                      })
                    : undefined,
                  rememberToolCallData: e.event_data?.remember_tool_call_data
                    ? new RememberToolCallData({
                        caller: e.event_data?.remember_tool_call_data?.caller,
                        isComplexNewMemory:
                          e.event_data?.remember_tool_call_data
                            ?.is_complex_new_memory,
                        tracingData: new AgentTracingData({
                          flags:
                            e.event_data?.remember_tool_call_data?.tracing_data
                              .flags,
                          nums: e.event_data?.remember_tool_call_data
                            ?.tracing_data.nums,
                          stringStats:
                            convertStringStats<RememberToolCallDebugFlag>(
                              e.event_data?.remember_tool_call_data
                                ?.tracing_data.string_stats,
                            ),
                          requestIds:
                            e.event_data?.remember_tool_call_data?.tracing_data
                              .request_ids,
                        }),
                      })
                    : undefined,
                  memoriesFileOpenData: e.event_data?.memories_file_open_data
                    ? new MemoriesFileOpenData({
                        memoriesPathUndefined:
                          e.event_data?.memories_file_open_data
                            ?.memories_path_undefined,
                      })
                    : undefined,
                  initialOrientationData: e.event_data?.initial_orientation_data
                    ? new InitialOrientationData({
                        caller: e.event_data?.initial_orientation_data?.caller,
                        tracingData: new AgentTracingData({
                          flags:
                            e.event_data?.initial_orientation_data?.tracing_data
                              .flags,
                          nums: e.event_data?.initial_orientation_data
                            ?.tracing_data.nums,
                          stringStats:
                            convertStringStats<InitialOrientationDebugFlag>(
                              e.event_data?.initial_orientation_data
                                ?.tracing_data.string_stats,
                            ),
                          requestIds:
                            e.event_data?.initial_orientation_data?.tracing_data
                              .request_ids,
                        }),
                      })
                    : undefined,
                  classifyAndDistillData: e.event_data
                    ?.classify_and_distill_data
                    ? new GenericTracingData({
                        tracingData: new AgentTracingData({
                          flags:
                            e.event_data?.classify_and_distill_data
                              ?.tracing_data.flags,
                          nums: e.event_data?.classify_and_distill_data
                            ?.tracing_data.nums,
                          stringStats:
                            convertStringStats<ClassifyAndDistillDebugFlag>(
                              e.event_data?.classify_and_distill_data
                                ?.tracing_data.string_stats,
                            ),
                          requestIds:
                            e.event_data?.classify_and_distill_data
                              ?.tracing_data.request_ids,
                        }),
                      })
                    : undefined,
                  flushMemoriesData: e.event_data?.flush_memories_data
                    ? new GenericTracingData({
                        tracingData: new AgentTracingData({
                          flags:
                            e.event_data?.flush_memories_data?.tracing_data
                              .flags,
                          nums: e.event_data?.flush_memories_data?.tracing_data
                            .nums,
                          stringStats:
                            convertStringStats<FlushMemoriesDebugFlag>(
                              e.event_data?.flush_memories_data?.tracing_data
                                .string_stats,
                            ),
                          requestIds:
                            e.event_data?.flush_memories_data?.tracing_data
                              .request_ids,
                        }),
                      })
                    : undefined,
                  contentTruncationData: e.event_data?.content_truncation_data
                    ? new ContentTruncationData({
                        originalCharCount:
                          e.event_data?.content_truncation_data
                            ?.original_char_count,
                        originalLineCount:
                          e.event_data?.content_truncation_data
                            ?.original_line_count,
                        truncatedCharCount:
                          e.event_data?.content_truncation_data
                            ?.truncated_char_count,
                        truncatedLineCount:
                          e.event_data?.content_truncation_data
                            ?.truncated_line_count,
                        toolType:
                          e.event_data?.content_truncation_data?.tool_type,
                      })
                    : undefined,
                  memoryUsageData: e.event_data?.memory_usage_data
                    ? new MemoryUsageDataProto({
                        action: e.event_data?.memory_usage_data?.action,
                        memoryId: e.event_data?.memory_usage_data?.memory_id,
                        triggeredBy:
                          e.event_data?.memory_usage_data?.triggered_by,
                        memoryState:
                          e.event_data?.memory_usage_data?.memory_state,
                        memoryVersion:
                          e.event_data?.memory_usage_data?.memory_version,
                      })
                    : undefined,
                  modelSelectionChangeData: e.event_data
                    ?.model_selection_change_data
                    ? new ModelSelectionChangeData({
                        previousModelId:
                          e.event_data?.model_selection_change_data
                            ?.previous_model_id ?? "",
                        previousModelName:
                          e.event_data?.model_selection_change_data
                            ?.previous_model_name ?? "",
                        newModelId:
                          e.event_data?.model_selection_change_data
                            ?.new_model_id ?? "",
                        newModelName:
                          e.event_data?.model_selection_change_data
                            ?.new_model_name ?? "",
                      })
                    : undefined,
                })
              : undefined,
          });
        }),
      }).toJson(),
    );
  }

  logRemoteAgentSessionEvent(
    _events: RemoteAgentSessionEvent[],
  ): Promise<void> {
    throw new Error("Method not implemented.");
  }

  public async agentCodebaseRetrieval(
    toolRequestId: string,
    informationRequest: string,
    chatHistory: Exchange[],
    maxOutputLength: number,
    _options?: AgentCodebaseRetrievalOptions,
    _signal?: AbortSignal,
  ): Promise<AgentCodebaseRetrievalResult> {
    this._logger.warn(
      `Invocation of agentCodebaseRetrieval discarding requestId ${toolRequestId}`,
    );
    const jsonResponse = await this._connection.sendRequest<JsonValue>(
      "augmentcode/api-client/agent-codebase-retrieval",
      new AgentCodebaseRetrievalRequest({
        informationRequest,
        chatHistory: chatHistory.map((item) => {
          return new ChatHistoryItem({
            requestMessage: item.request_message,
            responseText: item.response_text,
            requestId: item.request_id,
          });
        }),
        maxOutputLength,
        // TODO(arun): Add options to the client interfaces proto.
      }).toJson(),
    );
    if (!jsonResponse) {
      throw new Error(
        "Failed to get agent codebase retrieval response from host",
      );
    }
    try {
      const response = AgentCodebaseRetrievalResponse.fromJson(jsonResponse, {
        // This is needed so that @type fields from intellij are ignored
        ignoreUnknownFields: true,
      });
      this._logger.info(`Got codebase retrieval from host as proto`);
      return {
        formattedRetrieval: response.formattedRetrieval,
      };
    } catch (err) {
      this._logger.warn(
        `Failed to parse cwd response: ${err instanceof Error ? err.message : (err as string)}`,
      );
      throw new Error(
        "Received invalid agent codebase retrieval response from host",
      );
    }
  }

  async checkToolSafety(
    toolId: RemoteToolId,
    toolInputJson: string,
  ): Promise<boolean> {
    this._logger.warn(`API call to check tool safety ${toolId}`);
    const jsonResponse = await this._connection.sendRequest<JsonValue>(
      "augmentcode/api-client/check-tool-safety",
      new CheckToolSafetyRequest({
        toolId: toolId,
        toolInputJson: toolInputJson,
      }).toJson(),
    );
    if (!jsonResponse) {
      throw new Error("Failed to get tool safety response from host");
    }
    try {
      const response = CheckToolSafetyResponse.fromJson(jsonResponse, {
        // This is needed so that @type fields from intellij are ignored
        ignoreUnknownFields: true,
      });
      return response.isSafe;
    } catch (err) {
      this._logger.warn(
        `Failed to parse check tool safety response: ${err instanceof Error ? err.message : (err as string)}`,
      );
      throw new Error("Received invalid tool safety response from host");
    }
  }
}

function convertStringStats<T extends string>(
  stats: Record<T, Timed<StringStats>> | undefined,
): { [key: string]: TimedStringStats } | undefined {
  if (!stats) {
    return undefined;
  }

  const protoStats: { [key: string]: TimedStringStats } = {};
  for (const key of Object.keys(stats)) {
    const timedValue = stats[key as T];
    protoStats[key] = new TimedStringStats({
      value: new StringStatsProto({
        numLines: timedValue.value.num_lines,
        numChars: timedValue.value.num_chars,
      }),
      timestamp: timedValue.timestamp,
    });
  }
  return protoStats;
}
