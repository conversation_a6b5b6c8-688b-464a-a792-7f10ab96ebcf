import {
  IPluginStorageForSidecar,
  PluginStateNamespace,
  PluginStateScope,
} from "@augment-internal/sidecar-libs/src/client-interfaces/plugin-state";
import { sendRequestWithTimeout } from "../connection-utils";
import { getLogger } from "../logging";
import { Connection } from "vscode-languageserver";
import {
  GetStateValueRequest,
  GetStateValueResponse,
  SetStateValueRequest,
  PluginStateScope as ProtoPluginStateScope,
} from "$clients/sidecar/node-process/protos/plugin-storage-for-sidecar_pb";

export class PluginStateForSidecar implements IPluginStorageForSidecar {
  private _logger = getLogger("PluginState");
  private _debug = false;
  private scopeMap: Map<PluginStateScope, ProtoPluginStateScope> = new Map([
    [PluginStateScope.global, ProtoPluginStateScope.GLOBAL],
    // [PluginStateScope.workspace, ProtoPluginStateScope.WORKSPACE],
  ]);

  constructor(private readonly _connection: Connection) {}

  // As of March 27th 2025 the logs from this file are very noisy due to agent
  // shard manager.
  private log(message: string) {
    if (this._debug) {
      this._logger.info(message);
    }
  }

  // eslint-disable-next-line @typescript-eslint/require-await
  async getValue<T>(
    namespace: PluginStateNamespace,
    key: string,
    scope: PluginStateScope,
  ): Promise<T | undefined> {
    if (scope !== PluginStateScope.global) {
      throw new Error(`Scope ${String(scope)} is not supported`);
    }
    try {
      const fullKey = this.getKey(namespace, key);
      this.log(`Requesting state from host: ${fullKey}`);
      const jsonResponse = await sendRequestWithTimeout(
        this._connection,
        "augmentcode/plugin-state-for-sidecar/get-value",
        new GetStateValueRequest({
          key: fullKey,
          scope: this.scopeMap.get(scope),
        }).toJson(),
      );
      if (!jsonResponse) {
        return undefined;
      }

      const response = GetStateValueResponse.fromJson(jsonResponse, {
        // This is needed so that @type fields from intellij are ignored
        ignoreUnknownFields: true,
      });
      // eslint-disable-next-line @typescript-eslint/no-base-to-string, @typescript-eslint/restrict-template-expressions
      this.log(`Got state for ${key}: ${response.jsonValue}`);
      if (response.jsonValue === undefined) {
        return undefined;
      }
      return JSON.parse(response.jsonValue) as T;
    } catch (err) {
      this._logger.warn(
        `Failed to parse load asset response: ${err instanceof Error ? err.message : (err as string)}`,
      );
      return undefined;
    }
  }

  async setValue<T>(
    namesapce: PluginStateNamespace,
    key: string,
    value: T | undefined,
    scope: PluginStateScope,
  ): Promise<void> {
    if (scope !== PluginStateScope.global) {
      throw new Error(`Scope ${String(scope)} is not supported`);
    }
    const fullKey = this.getKey(namesapce, key);
    this.log(`Setting state on: ${fullKey}: ${JSON.stringify(value)}`);
    const jsonValue = value !== undefined ? JSON.stringify(value) : undefined;
    await sendRequestWithTimeout(
      this._connection,
      "augmentcode/plugin-state-for-sidecar/set-value",
      new SetStateValueRequest({
        key: fullKey,
        jsonValue,
        scope: this.scopeMap.get(scope),
      }).toJson(),
    );
  }

  private getKey(namespace: PluginStateNamespace, key: string) {
    return ["sidecar", namespace, key].join(".");
  }
}
