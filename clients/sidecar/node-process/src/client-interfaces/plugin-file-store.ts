import { IPluginFileStore } from "@augment-internal/sidecar-libs/src/client-interfaces/plugin-file-store";
import { Connection } from "vscode-languageserver";
import { getLogger } from "../logging";
import { sendRequestWithTimeout } from "../connection-utils";
import {
  DeleteAsset,
  GetAssetPath,
  GetAssetPathResponse,
  ListAssetsResponse,
  LoadAsset,
  LoadAssetResponse,
  SaveAsset,
} from "$clients/sidecar/node-process/protos/plugin-file-store_pb";

export class PluginFileStore implements IPluginFileStore {
  private _logger = getLogger("PluginFileStore");
  private _debug = false;

  constructor(private readonly _connection: Connection) {}

  // As of March 27th 2025 the logs from this file are very noisy due to agent
  // shard manager.
  private log(message: string) {
    if (this._debug) {
      this._logger.info(message);
    }
  }

  async saveAsset(path: string, contents: Uint8Array): Promise<void> {
    this.log(`Requesting write file from host: ${path}`);
    await sendRequestWithTimeout(
      this._connection,
      "augmentcode/plugin-filestore/saveAsset",
      new SaveAsset({ path, contents }).toJson(),
    );
    this.log(`Save asset complete`);
  }

  async loadAsset(path: string): Promise<Uint8Array | undefined> {
    this.log(`Requesting load asset from host: ${path}`);
    const jsonResposne = await sendRequestWithTimeout(
      this._connection,
      "augmentcode/plugin-filestore/loadAsset",
      new LoadAsset({ path: path }).toJson(),
    );
    if (!jsonResposne) {
      return undefined;
    }
    try {
      const response = LoadAssetResponse.fromJson(jsonResposne, {
        // This is needed so that @type fields from intellij are ignored
        ignoreUnknownFields: true,
      });
      this.log(`Load asset complete`);
      return response.contents;
    } catch (err) {
      this._logger.warn(
        `Failed to parse load asset response: ${err instanceof Error ? err.message : (err as string)}`,
      );
      return undefined;
    }
  }

  async deleteAsset(path: string): Promise<void> {
    this.log(`Requesting delete asset from host: ${path}`);
    await sendRequestWithTimeout(
      this._connection,
      "augmentcode/plugin-filestore/deleteAsset",
      new DeleteAsset({ path }).toJson(),
    );
    this.log(`Delete asset complete`);
  }

  async listAssets(prefix: string): Promise<string[]> {
    this.log(`Requesting list assets from host with prefix: ${prefix}`);
    const jsonResponse = await sendRequestWithTimeout(
      this._connection,
      "augmentcode/plugin-filestore/listAssets",
      JSON.stringify({ prefix }),
    );
    if (!jsonResponse) {
      return [];
    }
    try {
      const response = ListAssetsResponse.fromJson(jsonResponse, {
        // This is needed so that @type fields from intellij are ignored
        ignoreUnknownFields: true,
      });
      this.log(`List assets complete`);
      return Array.isArray(response.paths) ? response.paths : [];
    } catch (err) {
      this._logger.warn(
        `Failed to parse list assets response: ${err instanceof Error ? err.message : (err as string)}`,
      );
      return [];
    }
  }

  async getAssetPath(path: string): Promise<string> {
    this.log(`Requesting get asset path from host: ${path}`);
    const jsonResponse = await sendRequestWithTimeout(
      this._connection,
      "augmentcode/plugin-filestore/getAssetPath",
      new GetAssetPath({ path }).toJson(),
    );
    if (!jsonResponse) {
      throw new Error(`Failed to get asset path for: ${path}`);
    }
    try {
      const response = GetAssetPathResponse.fromJson(jsonResponse, {
        // This is needed so that @type fields from intellij are ignored
        ignoreUnknownFields: true,
      });
      this.log(`Get asset path complete`);
      return response.absolutePath;
    } catch (err) {
      this._logger.warn(
        `Failed to parse get asset path response: ${err instanceof Error ? err.message : (err as string)}`,
      );
      throw new Error(`Failed to get asset path for: ${path}`);
    }
  }
}
