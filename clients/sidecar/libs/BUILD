load("@aspect_bazel_lib//lib:copy_to_directory.bzl", "copy_to_directory")
load("@aspect_rules_jest//jest:defs.bzl", "jest_test")
load("@aspect_rules_js//js:defs.bzl", "js_library")
load("@aspect_rules_ts//ts:defs.bzl", "ts_config", "ts_project")
load("@bazel_skylib//rules:build_test.bzl", "build_test")
load("@npm//:defs.bzl", "npm_link_all_packages")

npm_link_all_packages()

SRC_FILES = glob(
    [
        "src/**/*.ts",
        "protos/**/*.ts",
        "protos/**/*.d.ts",
        "protos/**/*.js",
    ],
    exclude = [
        "src/**/__tests__/**",
        "src/**/__mocks__/**",
        "src/**/*.test.ts",
    ],
)

TEST_FILES = glob([
    "src/**/__tests__/**/*.ts",
    "src/**/__tests__/__fixtures__/*.json",
    "src/**/__tests__/**/__snapshots__/*.snap",
    "src/**/__mocks__/**/*.ts",
])

TEST_DATA = glob([
    "src/**/__tests__/**/test_data/**",
    "src/**/__tests__/**/match_test_data/**",
])

LIB_DEPS = [
    ":node_modules/turndown",
    ":node_modules/zod",
    ":node_modules/winston",
    ":node_modules/@types/node",
    ":node_modules/diff",
    ":node_modules/@types/diff",
    ":node_modules/lru-cache",
    ":node_modules/short-uuid",
    ":node_modules/level",
]

ts_config(
    name = "tsconfig",
    src = "tsconfig.json",
    visibility = [
        "//clients:__subpackages__",
    ],
    deps = [
        "//clients:tsconfig",
    ],
)

# Target that provides the correctly structured prebuilds with final layout
copy_to_directory(
    name = "runtime_bundle",
    srcs = [":node_modules/level"],
    out = "runtime_prebuilds",
    include_external_repositories = ["npm"],
    replace_prefixes = {
        "node_modules/.aspect_rules_js/classic-level@3.0.0/node_modules/classic-level/prebuilds": "prebuilds",
        "node_modules/.aspect_rules_js/classic-level@3.0.0/node_modules/classic-level": "runtime",
    },
    visibility = [
        "//clients:__subpackages__",
    ],
)

# This target should never be depended on, instead rely on the :src target.
# This target is only for typechecking.
ts_project(
    name = "ts",
    srcs = SRC_FILES + LIB_DEPS,
    data = [
        ":node_modules",
        ":runtime_bundle",
    ],
    no_emit = True,  # Use for typechecking only
    tsconfig = ":tsconfig",
    deps = [
        ":node_modules",
        "//clients/sidecar/libs/protos:sidecar_libs_ts_protos",
        "//clients/sidecar/libs/transport/grpc/ts",
    ],
)

# Build test ensures that CI/CD runs the :ts build that checks typing
build_test(
    name = "build_test",
    targets = [":ts"],
)

js_library(
    name = "src",
    srcs = SRC_FILES + LIB_DEPS,
    data = [
        ":runtime_bundle",
    ],
    visibility = [
        "//clients/beachhead:__pkg__",
        "//clients/common/webviews:__pkg__",
        "//clients/sidecar/node-process:__pkg__",
        "//clients/vscode:__pkg__",
    ],
    deps = [
        ":node_modules",  # Transitive node_modules
        "//clients/sidecar/libs/protos:sidecar_libs_ts_protos",
        "//clients/sidecar/libs/transport/grpc/ts",
    ],
)

# Mocks for common interfaces
js_library(
    name = "mocks",
    srcs = glob(["src/__tests__/mocks/**/*.ts"]),
    visibility = [
        "//clients/vscode:__pkg__",
    ],
    deps = [],
)

jest_test(
    name = "test",
    timeout = "moderate",
    config = "jest.config.js",
    data = SRC_FILES + TEST_FILES + TEST_DATA + LIB_DEPS + [
        "babel.config.js",
        ":node_modules",
        ":runtime_bundle",
        ":tsconfig",
        "//base/blob_names/test_data:blob-name-test-data-js",
        "//clients/sidecar/libs/protos:sidecar_libs_ts_protos",  # Need this in the build tree to resolve proto/grpc
    ],
    include_transitive_types = True,  # Needed for type checking
    node_modules = ":node_modules",
)
