# Sidecar Libs Protos BUILD File

load("@aspect_bazel_lib//lib:write_source_files.bzl", "write_source_files")
load("@aspect_rules_js//js:defs.bzl", "js_library")
load("@rules_proto//proto:defs.bzl", "proto_library")
load("//tools/bzl:typescript.bzl", "ts_proto_library")

proto_library(
    name = "echo_service_proto",
    srcs = ["echo_service.proto"],
    visibility = ["//clients/sidecar/libs/protos:__subpackages__"],
)

ts_proto_library(
    name = "echo_service_ts_proto",
    copy_files = True,
    node_modules = "//clients/sidecar/libs:node_modules",
    proto = ":echo_service_proto",
    visibility = ["//clients:__subpackages__"],
)

proto_library(
    name = "test_service_proto",
    srcs = ["test_service.proto"],
    visibility = ["//clients/sidecar/libs/protos:__subpackages__"],
)

ts_proto_library(
    name = "test_service_ts_proto",
    copy_files = True,
    node_modules = "//clients/sidecar/libs:node_modules",
    proto = ":test_service_proto",
    visibility = ["//clients:__subpackages__"],
)

# Bundle all sidecar proto outputs as a js_library for convenience of dependency
# declaration in downstream targets (we have this convenience target to match
# historical target format where all proto outputs were bundled).
js_library(
    name = "sidecar_libs_ts_protos",
    srcs = [
        ":echo_service_ts_proto",
        ":test_service_ts_proto",
    ],
    visibility = ["//clients:__subpackages__"],
)

# This target copies all generated proto files into the source tree for IDE support.
write_source_files(
    name = "sidecar_libs_ts_protos.copy",
    additional_update_targets = [
        # Add additional copy targets here for convenience.
        ":echo_service_ts_proto.generate_stubs",
        ":test_service_ts_proto.generate_stubs",
    ],
)
