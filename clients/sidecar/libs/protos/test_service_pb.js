// @generated by protoc-gen-es v2.3.0 with parameter "keep_empty_files=true,target=js+dts"
// @generated from file clients/sidecar/libs/protos/test_service.proto (package test, syntax proto3)
/* eslint-disable */

import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";

/**
 * Describes the file clients/sidecar/libs/protos/test_service.proto.
 */
export const file_clients_sidecar_libs_protos_test_service = /*@__PURE__*/
  fileDesc("Ci5jbGllbnRzL3NpZGVjYXIvbGlicy9wcm90b3MvdGVzdF9zZXJ2aWNlLnByb3RvEgR0ZXN0IhoKC1Rlc3RSZXF1ZXN0EgsKA2ZvbxgBIAEoCSIeCgxUZXN0UmVzcG9uc2USDgoGcmVzdWx0GAEgASgJMngKC1Rlc3RTZXJ2aWNlEjMKClRlc3RNZXRob2QSES50ZXN0LlRlc3RSZXF1ZXN0GhIudGVzdC5UZXN0UmVzcG9uc2USNAoLRXJyb3JNZXRob2QSES50ZXN0LlRlc3RSZXF1ZXN0GhIudGVzdC5UZXN0UmVzcG9uc2ViBnByb3RvMw");

/**
 * Describes the message test.TestRequest.
 * Use `create(TestRequestSchema)` to create a new message.
 */
export const TestRequestSchema = /*@__PURE__*/
  messageDesc(file_clients_sidecar_libs_protos_test_service, 0);

/**
 * Describes the message test.TestResponse.
 * Use `create(TestResponseSchema)` to create a new message.
 */
export const TestResponseSchema = /*@__PURE__*/
  messageDesc(file_clients_sidecar_libs_protos_test_service, 1);

/**
 * @generated from service test.TestService
 */
export const TestService = /*@__PURE__*/
  serviceDesc(file_clients_sidecar_libs_protos_test_service, 0);

