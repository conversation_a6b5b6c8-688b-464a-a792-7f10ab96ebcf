# Sidecar Libs Protos

This directory contains the Protocol Buffer definitions for the Sidecar Libs.

## Generating TypeScript Files from Proto Definitions

The TypeScript files are generated using Bazel. For proper IDE syntax/error
highlighting it's nice to have the generated sources in the source tree. To
generate and copy the TypeScript files to this directory, run:
```
bazel run //clients/sidecar/libs/protos:sidecar_libs_ts_protos.copy
```

Note that you'll need to occasionally redo this whenever the proto definitions change.

## Using the Generated TypeScript Files

After generating and copying the TypeScript files, you can import them in your TypeScript code like this:

```typescript
import {
  GetAgentEditListRequest,
  GetAgentEditListResponse,
  AgentEdit,
  QualifiedPathName,
  AgentFileChangeSummary,
} from "$clients/sidecar/libs/protos/agent_pb";
```

(assuming `$` aliases to the repo root in the build tree).

## Adding New Proto Files

To add a new proto file:

1. Create the new proto file in this directory.

Then within the `BUILD` file:
1. Add a `proto_library` target for the new proto file.
2. Add a`ts_proto_library` target for the new proto file.
3. Add the `ts_proto_library` to the `sidecar_libs_ts_protos` target's deps.
4. Add the `ts_proto_library.copy` target to the deps of the `sidecar_libs_ts_protos.copy`.
