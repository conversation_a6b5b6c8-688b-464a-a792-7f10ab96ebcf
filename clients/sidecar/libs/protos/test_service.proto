syntax = "proto3";

package test;

// A simple test service for testing the PostMessageTransport
service TestService {
  // A simple unary method
  rpc TestMethod(TestRequest) returns (TestResponse);

  // A method that always returns an error
  rpc ErrorMethod(TestRequest) returns (TestResponse);
}

// Test request message
message TestRequest {
  string foo = 1;
}

// Test response message
message TestResponse {
  string result = 1;
}
