{"private": true, "scripts": {"lint": "npm run eslint && npm run prettier", "lint:fix": "npm run eslint:fix && npm run prettier:fix", "eslint": "git ls-files -- . | xargs pre-commit run eslint --files", "eslint:fix": "git ls-files -- . | xargs pre-commit run eslint --hook-stage=manual --files", "prettier": "git ls-files -- . | xargs pre-commit run prettier --hook-stage=manual --files", "prettier:fix": "git ls-files -- . | xargs pre-commit run prettier --files", "jest": "jest --config ./jest.config.js", "build-and-copy-protos": "bazel build //clients/sidecar/libs/protos:sidecar_libs_ts_protos && bazel run //clients/sidecar/libs/protos:sidecar_libs_ts_protos.copy"}, "devDependencies": {"@babel/core": "^7.23.9", "@babel/preset-env": "^7.23.9", "@types/diff": "^7.0.1", "@types/jest": "^29.5.11", "@types/js-yaml": "^4.0.9", "@types/lodash": "^4.14.202", "@types/node": "^22.9.0", "@types/turndown": "^5.0.5", "babel-jest": "^29.7.0", "jest": "^29.7.0", "jest-cli": "^29.7.0", "jest-junit": "^15.0.0", "lodash": "^4.17.21", "ts-jest": "^29.3.3", "turndown": "^7.2.0", "winston": "^3.17.0", "zod": "^3.23.8"}, "dependencies": {"@bufbuild/protobuf": "^2.3.0", "@bufbuild/protoc-gen-es": "^2.3.0", "@connectrpc/connect": "^2.0.2", "@connectrpc/protoc-gen-connect-es": "^1.6.1", "@modelcontextprotocol/sdk": "1.12.1", "@segment/analytics-node": "^2.2.1", "ajv": "^8.17.1", "ajv-formats": "^3.0.1", "diff": "^7.0.0", "globby": "^14.1.0", "isomorphic-git": "^1.32.1", "js-yaml": "^4.1.0", "level": "^10.0.0", "lru-cache": "^11.0.0", "short-uuid": "^5.2.0", "typescript": "5.4.5", "uuid": "^11.1.0"}}