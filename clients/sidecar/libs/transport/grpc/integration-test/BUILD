load("@aspect_rules_esbuild//esbuild:defs.bzl", "esbuild")
load("@aspect_rules_js//js:defs.bzl", "js_test")

# Dependencies needed for the integration test
DEPS = [
    "//clients/sidecar/libs:node_modules/@bufbuild/protobuf",
    "//clients/sidecar/libs:node_modules/@connectrpc/connect",
    "//clients/sidecar/libs:node_modules/@types/node",
    "//clients/sidecar/libs/transport/grpc/ts:grpc_transport",
    "//clients/sidecar/libs/protos:sidecar_libs_ts_protos",
]

# Build the below processes with esbuild to allow easy standalone
# execution (the echo client and server are run as spawned node processes).
esbuild(
    name = "test-executor",
    srcs = ["test-executor.ts"],
    entry_point = "test-executor.ts",
    format = "cjs",
    output = "test-executor.js",
    platform = "node",
    target = "node18",
    deps = DEPS,
)

esbuild(
    name = "ts-echo-client",
    srcs = ["ts-echo-client.ts"],
    entry_point = "ts-echo-client.ts",
    format = "cjs",
    output = "ts-echo-client.js",
    platform = "node",
    target = "node18",
    deps = DEPS,
)

# Bundle the TypeScript echo server with esbuild
esbuild(
    name = "ts-echo-server",
    srcs = ["ts-echo-server.ts"],
    entry_point = "ts-echo-server.ts",
    format = "cjs",
    output = "ts-echo-server.js",
    platform = "node",
    target = "node18",
    deps = DEPS,
)

# Integration test that runs the test executor using bundled outputs
js_test(
    name = "integration_test",
    timeout = "moderate",
    data = [
        ":test-executor",
        ":ts-echo-client",
        ":ts-echo-server",
    ],
    entry_point = "test-executor.js",
)
