{"compilerOptions": {"target": "ES2022", "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "declaration": true, "sourceMap": true, "removeComments": true, "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "lib": ["ES2022", "DOM"], "types": ["node", "@bufbuild/protobuf"], "baseUrl": "./", "paths": {"../../../protos/*": ["../../../protos/*"]}}, "include": ["*.ts", "../../../protos/*.ts"], "exclude": ["node_modules", "out", "__tests__/**/*"]}