load("@aspect_rules_js//js:defs.bzl", "js_library")
load("@aspect_rules_ts//ts:defs.bzl", "ts_config", "ts_project")

# TypeScript configuration for the grpc transport library
ts_config(
    name = "tsconfig",
    src = "tsconfig.json",
)

# Source files for the grpc transport library
SRC_FILES = [
    "index.ts",
    "message-target.ts",
    "send-message-transport.ts",
    "service-registry.ts",
    "stdio-message-target.ts",
]

# Dependencies needed for the grpc transport library
DEPS = [
    "//clients/sidecar/libs:node_modules/@bufbuild/protobuf",
    "//clients/sidecar/libs:node_modules/@connectrpc/connect",
    "//clients/sidecar/libs:node_modules/@types/node",
    "//clients/sidecar/libs/protos:sidecar_libs_ts_protos",
]

# Compile TypeScript sources
ts_project(
    name = "ts",
    srcs = SRC_FILES,
    declaration = True,
    resolve_json_module = True,
    source_map = True,
    tsconfig = ":tsconfig",
    visibility = ["//clients/sidecar/libs:__subpackages__"],
    deps = DEPS,
)

# JavaScript library for runtime use
js_library(
    name = "grpc_transport",
    srcs = [":ts"],
    visibility = ["//clients/sidecar/libs:__subpackages__"],
    deps = DEPS,
)
