import { QualifiedPathName } from "../../../workspace/qualified-path-name";
import { DiffViewDocument } from "../../../diff-view/document";
import { CheckpointKeyInputData } from "../types";
import { HydratedCheckpoint } from "../checkpoint-types";

/**
 * Helper function to generate checkpoint IDs for testing
 */
export const testCheckpointDocumentIdFn = (
  keyData: CheckpointKeyInputData,
): string => {
  return `${keyData.conversationId}:${keyData.path.absPath}`;
};

/**
 * Creates a test key data object for testing
 */
export const createTestKeyData = (
  path: QualifiedPathName,
  conversationId: string = "test-conversation",
): CheckpointKeyInputData => ({
  conversationId,
  path,
});

/**
 * Creates a test hydrated checkpoint for testing
 */
export const createTestHydratedCheckpoint = (
  original: string | undefined,
  modified: string | undefined,
  path: QualifiedPathName,
  conversationId: string = "test-conversation",
  timestamp?: number,
): HydratedCheckpoint => ({
  sourceToolCallRequestId: "test",
  timestamp: timestamp ?? Date.now(),
  document: new DiffViewDocument(path, original, modified, {}),
  conversationId,
});

/**
 * Creates a test hydrated checkpoint with a custom request ID for testing
 */
export const createTestHydratedCheckpointWithRequestId = (
  requestId: string,
  original: string | undefined,
  modified: string | undefined,
  path: QualifiedPathName,
  conversationId: string = "test-conversation",
  timestamp: number = Date.now(),
): HydratedCheckpoint => ({
  sourceToolCallRequestId: requestId,
  timestamp,
  document: new DiffViewDocument(path, original, modified, {}),
  conversationId,
});
