import { QualifiedPathName } from "../../../workspace/qualified-path-name";
import { ShardManager } from "../shard-manager";
import type { ShardedStorage } from "../storage";
import type { ShardIdFunction, CheckpointKeyInputData } from "../types";
import type { SerializedStore } from "../../agent-edit-types";
import type { HydratedCheckpoint } from "../checkpoint-types";
import { createTestHydratedCheckpointWithRequestId } from "./test-helpers";
import { AugmentLogger } from "@augment-internal/sidecar-libs/src/logging";

describe("ShardManager Flush Behavior", () => {
  // Test utilities
  const testPath = QualifiedPathName.from({
    rootPath: "/test",
    relPath: "file.ts",
  });

  const createTestKeyData = (
    path: QualifiedPathName = testPath,
    conversationId: string = "test-conversation",
  ): CheckpointKeyInputData => ({
    conversationId,
    path,
  });

  const createTestCheckpoint = (
    id: string,
    original: string,
    modified: string,
    path: QualifiedPathName = testPath,
    conversationId: string = "test-conversation",
  ): HydratedCheckpoint => {
    return createTestHydratedCheckpointWithRequestId(
      id,
      original,
      modified,
      path,
      conversationId,
    );
  };

  // Mock storage
  let mockStorage: ShardedStorage<SerializedStore>;
  let mockStorageHooks: {
    loadManifest: jest.Mock;
    saveManifest: jest.Mock;
    loadShard: jest.Mock;
    saveShard: jest.Mock;
    deleteShard: jest.Mock;
    load: jest.Mock;
    save: jest.Mock;
  };

  // Mock shard function
  let mockShardFunction: ShardIdFunction;
  let mockLogger: jest.Mocked<AugmentLogger>;

  // Manager instance
  let manager: ShardManager;

  beforeEach(() => {
    jest.useFakeTimers();

    // Create mock storage
    mockStorageHooks = {
      loadManifest: jest.fn().mockResolvedValue(undefined),
      saveManifest: jest.fn().mockResolvedValue(undefined),
      loadShard: jest.fn().mockResolvedValue(undefined),
      saveShard: jest.fn().mockResolvedValue(undefined),
      deleteShard: jest.fn().mockResolvedValue(undefined),
      load: jest.fn().mockResolvedValue(undefined),
      save: jest.fn().mockResolvedValue(undefined),
    };

    mockStorage = mockStorageHooks;

    // Create mock shard function
    mockShardFunction = jest
      .fn()
      .mockImplementation(
        (keyData: CheckpointKeyInputData) => `shard-${keyData.path.relPath}`,
      );

    mockLogger = {
      debug: jest.fn(),
      info: jest.fn(),
      warn: jest.fn(),
      error: jest.fn(),
      log: jest.fn(),
      verbose: jest.fn(),
    };

    // Create manager with throttling
    manager = new ShardManager(mockStorage, mockShardFunction, {
      flushIntervalMs: 1000, // 1 second throttle
      logger: mockLogger,
    });
  });

  afterEach(() => {
    jest.useRealTimers();
    jest.clearAllMocks();
  });

  describe("Throttled Flushing", () => {
    beforeEach(async () => {
      await manager.initialize();
    });

    it("should not save to disk immediately when adding a checkpoint", async () => {
      const keyData = createTestKeyData();
      const checkpoint = createTestCheckpoint("test", "original", "modified");

      // Clear any previous calls
      jest.clearAllMocks();

      await manager.addCheckpoint(keyData, checkpoint);

      // Should not have saved to disk yet
      expect(mockStorage.saveShard).not.toHaveBeenCalled();
      expect(mockStorage.saveManifest).not.toHaveBeenCalled();
    });

    it("should save to disk after throttle period", async () => {
      const keyData = createTestKeyData();
      const checkpoint = createTestCheckpoint("test", "original", "modified");

      await manager.addCheckpoint(keyData, checkpoint);

      // Advance timer past throttle period
      jest.advanceTimersByTime(1100);

      // Force a flush
      await manager.flush();

      // Now it should have saved
      expect(mockStorage.saveShard).toHaveBeenCalled();
      expect(mockStorage.saveManifest).toHaveBeenCalled();
    });

    it("should batch multiple operations into a single flush", async () => {
      const keyData = createTestKeyData();
      const checkpoint1 = createTestCheckpoint(
        "test1",
        "original1",
        "modified1",
      );
      const checkpoint2 = createTestCheckpoint(
        "test2",
        "original2",
        "modified2",
      );

      // Clear any previous calls
      jest.clearAllMocks();

      // Add two checkpoints in quick succession
      await manager.addCheckpoint(keyData, checkpoint1);
      await manager.addCheckpoint(keyData, checkpoint2);

      // Advance timer past throttle period
      jest.advanceTimersByTime(1100);

      // Force a flush
      await manager.flush();

      // Should have only saved once
      expect(mockStorage.saveShard).toHaveBeenCalled();
      expect(mockStorage.saveManifest).toHaveBeenCalled();
    });

    it("should flush immediately when explicitly requested", async () => {
      const keyData = createTestKeyData();
      const checkpoint = createTestCheckpoint("test", "original", "modified");

      // Clear any previous calls
      jest.clearAllMocks();

      await manager.addCheckpoint(keyData, checkpoint);

      // No saves yet
      expect(mockStorage.saveShard).not.toHaveBeenCalled();

      // Explicitly flush
      await manager.flush();

      // Should have saved immediately
      expect(mockStorage.saveShard).toHaveBeenCalledTimes(1);
      expect(mockStorage.saveManifest).toHaveBeenCalledTimes(1);
    });

    it("should flush on dispose", async () => {
      const keyData = createTestKeyData();
      const checkpoint = createTestCheckpoint("test", "original", "modified");

      await manager.addCheckpoint(keyData, checkpoint);

      // No saves yet
      expect(mockStorage.saveShard).not.toHaveBeenCalled();

      // Clear any previous calls
      jest.clearAllMocks();

      // Dispose manager
      await manager.dispose();

      // Should have saved immediately
      expect(mockStorage.saveShard).toHaveBeenCalledTimes(1);
      expect(mockStorage.saveManifest).toHaveBeenCalledTimes(1);
    });
  });

  describe("Error Handling", () => {
    beforeEach(async () => {
      await manager.initialize();
    });

    it("should log errors but not fail operations when flush fails", async () => {
      // Make saveShard fail
      mockStorageHooks.saveShard.mockRejectedValue(new Error("Save failed"));

      const keyData = createTestKeyData();
      const checkpoint = createTestCheckpoint("test", "original", "modified");

      // This should not throw
      await manager.addCheckpoint(keyData, checkpoint);

      // Explicitly flush to trigger the error
      await manager.flush();

      // Should have logged the error
      expect(mockLogger.error).toHaveBeenCalledWith(
        "Failed to flush shards:",
        expect.any(Error),
      );
    });
  });

  describe("Helper Functions", () => {
    beforeEach(async () => {
      await manager.initialize();
    });

    it("should use _performShardOperation for addCheckpoint", async () => {
      const keyData = createTestKeyData();
      const checkpoint = createTestCheckpoint("test", "original", "modified");

      // Clear any previous calls
      jest.clearAllMocks();

      await manager.addCheckpoint(keyData, checkpoint);

      // Force a flush
      await manager.flush();

      // Should have saved the shard and manifest
      expect(mockStorage.saveShard).toHaveBeenCalled();
      expect(mockStorage.saveManifest).toHaveBeenCalled();
    });
  });
});
