import type { ShardingContext, ShardManifest, ShardMetadata } from "../types";

describe("Sharding Types", () => {
  describe("ShardingContext", () => {
    it("should accept valid context data", () => {
      const manifest: ShardManifest = {
        version: 1,
        lastUpdated: Date.now(),
        shards: {},
      };

      const context: ShardingContext = {
        manifestSnapshot: manifest,
        currentShardId: "test-shard",
        pathStats: {
          checkpointCount: 1,
          estimatedSize: 1000,
        },
      };

      // Type compilation test
      expect(context.manifestSnapshot.version).toBe(1);
      expect(context.pathStats.checkpointCount).toBe(1);
    });
  });

  describe("ShardMetadata", () => {
    it("should accept valid metadata", () => {
      const metadata: ShardMetadata = {
        checkpointDocumentIds: ["test-conversation:test-file"],
        size: 1000,
        checkpointCount: 1,
        lastModified: Date.now(),
      };

      // Type compilation test
      expect(metadata.checkpointDocumentIds).toHaveLength(1);
      expect(metadata.size).toBe(1000);
    });
  });
});
