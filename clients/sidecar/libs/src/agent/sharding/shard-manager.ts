import type { ShardedStorage } from "./storage";
import type { SerializedStore } from "../agent-edit-types";
import type { HydratedCheckpoint } from "./checkpoint-types";
import type {
  ShardIdFunction,
  ShardId,
  ShardManifest,
  CheckpointKeyInputData,
  CheckpointDocumentIdFunction,
  CheckpointFilterOptions,
  SerializedShard,
} from "./types";
import { ShardData } from "./shard-data";
import { filterInvalidShardsFromManifest, validateManifest } from "./utils";
import { DisposableService } from "../../lifecycle/disposable-service";
import { getClientWorkspaces } from "../../client-interfaces/client-workspaces";
import { DiffViewDocument } from "../../diff-view/document";
import { QualifiedPathName } from "../../workspace/qualified-path-name";
import throttle from "lodash/throttle";
import type { DebouncedFunc } from "lodash";
import { AugmentLogger, getLogger } from "../../logging";

/**
 * Options for configuring the ShardManager
 */
interface ShardManagerOptions {
  /** Maximum number of shards to keep in memory */
  maxCachedShards?: number;
  /** Flush interval in milliseconds */
  flushIntervalMs?: number;
  /** Logger for ShardManager */
  logger?: AugmentLogger;
}

/**
 * Manages the lifecycle of shards and their data.
 *
 * The ShardManager is the central component of the sharding architecture, responsible for:
 *
 * 1. Shard Lifecycle Management:
 *    - Loading shards from storage on demand
 *    - Unloading infrequently used shards (LRU cache)
 *    - Flushing dirty shards to storage
 *
 * 2. Manifest Maintenance:
 *    - Tracking all shards in the system
 *    - Maintaining metadata about each shard
 *    - Ensuring manifest consistency with actual shard data
 *
 * 3. Checkpoint Operations:
 *    - Adding, updating, and retrieving checkpoints
 *    - Mapping file paths to appropriate shards
 *    - Filtering checkpoints by timestamp or other criteria
 *
 * 4. Backwards Compatibility:
 *    - Working with both legacy and current checkpoint formats
 *    - Using the hydration/dehydration system for format conversions
 *
 * The ShardManager uses a throttled flush mechanism to balance performance
 * and data safety, ensuring changes are persisted efficiently without
 * excessive I/O operations.
 */
export class ShardManager extends DisposableService {
  /**
   * Default flush interval in milliseconds.
   * This means shard updates will be flushed to disk at most every 5 seconds.
   */
  private static DEFAULT_FLUSH_INTERVAL_MS = 5000;

  /** In-memory cache of loaded shards */
  private readonly _shardCache: Map<ShardId, ShardData> = new Map();
  /** Current manifest state */
  private _manifest: ShardManifest = {
    version: 1,
    lastUpdated: Date.now(),
    shards: {},
  };
  /** Access order of shards for LRU cache */
  private _accessOrder: ShardId[] = [];
  /** Throttled flush function */
  private _flushThrottled: DebouncedFunc<() => Promise<void>>;
  /** Flag indicating whether the manager is being disposed */
  private _isDisposing: boolean = false;
  /** Promise that resolves when initialization is complete, undefined if not started */
  private _initializePromise: Promise<void> | undefined;
  private readonly _logger: AugmentLogger;

  /**
   * Creates a new ShardManager instance.
   *
   * @param _storage - Storage implementation for persistence
   * @param _shardFunction - Function to determine shard assignment
   * @param _options - Configuration options
   */
  private readonly _checkpointDocumentIdFn: CheckpointDocumentIdFunction;

  constructor(
    private readonly _storage: ShardedStorage<SerializedStore>,
    private readonly _shardFunction: ShardIdFunction,
    private readonly _options: ShardManagerOptions = {},
  ) {
    super();

    // Create checkpoint ID function that matches the format used in shard data
    this._checkpointDocumentIdFn = (keyData: CheckpointKeyInputData) =>
      `${keyData.conversationId}:${keyData.path.absPath}`;
    this._logger = this._options.logger ?? getLogger("ShardManager");

    // Create throttled flush function
    this._flushThrottled = throttle(
      () => this._flush(),
      this._options.flushIntervalMs ?? ShardManager.DEFAULT_FLUSH_INTERVAL_MS,
      { leading: false, trailing: true },
    );
  }

  /**
   * Gets the current manifest.
   * @returns Read-only view of the current manifest
   */
  get manifest(): Readonly<ShardManifest> {
    return this._manifest;
  }

  /**
   * Loads initial state from storage and prepares the ShardManager for use.
   *
   * This method is a critical part of the initialization flow and must be called
   * before using any other methods on the ShardManager. It handles:
   *
   * 1. Loading the manifest from storage
   * 2. Validating the manifest structure and content
   * 3. Repairing invalid manifests by filtering out invalid shards
   * 4. Creating a new manifest if none exists
   *
   * The method implements a singleton pattern for the initialization process,
   * ensuring that multiple concurrent calls will all wait for the same initialization
   * to complete rather than starting multiple initialization processes.
   *
   * @returns A promise that resolves when initialization is complete
   */
  initialize = async (): Promise<void> => {
    // Return existing promise if initialization is in progress or completed
    if (this._initializePromise) {
      return this._initializePromise;
    }

    // Create a new initialization promise
    this._initializePromise = (async () => {
      try {
        const manifest = await this._storage.loadManifest();
        if (manifest) {
          if (validateManifest(manifest).isValid) {
            this._manifest = manifest;
          } else {
            this._manifest = filterInvalidShardsFromManifest(manifest);
            await this._storage.saveManifest(this._manifest);
          }
        } else {
          // Reset to clean manifest if loaded one is invalid
          this._manifest = {
            version: 1,
            lastUpdated: Date.now(),
            shards: {},
          };
          await this._storage.saveManifest(this._manifest);
        }
      } catch (error) {
        // Reset the promise if initialization fails
        this._initializePromise = undefined;
        throw error;
      }
    })();

    return this._initializePromise;
  };

  /**
   * Gets the shard ID for a path.
   * If the path is not yet assigned to a shard, assigns it using the shard function.
   *
   * @param path - Path to get shard for
   * @returns ID of the assigned shard
   */
  private _getShardId(keyData: CheckpointKeyInputData): ShardId {
    const checkpointDocumentId = this._checkpointDocumentIdFn(keyData);

    // Find existing shard assignment
    for (const [shardId, metadata] of Object.entries(this._manifest.shards)) {
      if (metadata.checkpointDocumentIds.includes(checkpointDocumentId)) {
        return shardId;
      }
    }

    // Assign to new shard
    return this._shardFunction(keyData, {
      manifestSnapshot: this._manifest,
      pathStats: {
        checkpointCount: 0,
        estimatedSize: 0,
      },
    });
  }

  /**
   * Updates the access time for a shard
   */
  private _updateAccessTime(shardId: ShardId): void {
    const index = this._accessOrder.indexOf(shardId);
    if (index > -1) {
      this._accessOrder.splice(index, 1);
    }
    this._accessOrder.push(shardId);
  }

  /**
   * Updates checkpoints in a shard based on current file contents on disk.
   * Uses transaction-like semantics to ensure consistency:
   * 1. Collect all file read operations and execute them in parallel
   * 2. Prepare all checkpoint updates based on file contents
   * 3. Apply all updates to the shard at once
   *
   * @param shard - The shard to update
   * @returns The updated shard
   */
  private async _updateCheckpointsFromDisk(
    shard: ShardData,
  ): Promise<ShardData> {
    // Collect all files that need to be checked
    type FileCheckInfo = {
      conversationId: string;
      path: QualifiedPathName;
      latestCheckpoint: HydratedCheckpoint;
    };

    const filesToCheck: FileCheckInfo[] = [];

    // First, identify all files that need to be checked
    for (const conversationId of shard.getAllTrackedConversationIds()) {
      for (const path of shard.getAllTrackedFilePaths(conversationId)) {
        const latestCheckpoint = shard.getLatestCheckpoint({
          conversationId,
          path,
        });
        if (!latestCheckpoint) {
          continue;
        }
        filesToCheck.push({
          conversationId,
          path,
          latestCheckpoint,
        });
      }
    }

    // No files to check, return early
    if (filesToCheck.length === 0) {
      return shard;
    }

    // Read all files in parallel
    const fileReadResults = await Promise.all(
      filesToCheck.map(async (fileInfo) => {
        try {
          const contents = (
            await getClientWorkspaces().readFile(fileInfo.path.absPath)
          ).contents;
          return {
            ...fileInfo,
            onDiskContents: contents,
            error: null,
          };
        } catch (error) {
          this._logger.error(
            `Failed to read file ${fileInfo.path.absPath}:`,
            error,
          );
          return {
            ...fileInfo,
            onDiskContents: null,
            error,
          };
        }
      }),
    );

    // Prepare checkpoint updates
    const checkpointsToAdd: Array<{
      keyData: CheckpointKeyInputData;
      checkpoint: HydratedCheckpoint;
    }> = [];

    for (const result of fileReadResults) {
      // Skip files with errors or no changes
      if (
        result.error ||
        !result.onDiskContents ||
        result.onDiskContents === result.latestCheckpoint.document.modifiedCode
      ) {
        continue;
      }

      // Prepare a new checkpoint for this file
      const newDoc = new DiffViewDocument(
        result.path,
        result.latestCheckpoint.document.modifiedCode,
        result.onDiskContents,
        {},
      );

      checkpointsToAdd.push({
        keyData: {
          conversationId: result.conversationId,
          path: result.path,
        },
        checkpoint: {
          sourceToolCallRequestId: crypto.randomUUID(),
          timestamp: Date.now(),
          document: newDoc,
          conversationId: result.conversationId,
        },
      });
    }

    // Apply all checkpoint updates to the shard
    for (const { keyData, checkpoint } of checkpointsToAdd) {
      shard.addCheckpoint(keyData, checkpoint);
    }

    return shard;
  }

  /**
   * Loads a shard into memory.
   * Updates access time but defers cache eviction to flush operations.
   */
  private async _loadShard(shardId: ShardId): Promise<ShardData> {
    // Check cache first
    let shard = this._shardCache.get(shardId);
    if (shard) {
      this._updateAccessTime(shardId);
      return shard;
    }

    // Load from storage
    const data = await this._storage.loadShard(shardId);
    this._logger.debug(`Loading shard ${shardId} from storage`);
    shard = data
      ? await ShardData.fromSerialized(
          shardId,
          data,
          this._checkpointDocumentIdFn,
        )
      : new ShardData(shardId, this._checkpointDocumentIdFn);

    // Update checkpoints based on current file contents on disk
    shard = await this._updateCheckpointsFromDisk(shard);

    // Add to cache
    this._shardCache.set(shardId, shard);
    this._updateAccessTime(shardId);
    return shard;
  }

  /**
   * Updates the manifest with current shard state.
   * @param shardId - ID of shard that changed
   * @param shard - Current state of the shard
   */
  private _updateManifest(shardId: ShardId, shard: ShardData): void {
    this._manifest.shards[shardId] = shard.getMetadata();
    this._manifest.lastUpdated = Date.now();
  }

  /**
   * Helper function to trigger a flush based on the current state.
   * If the manager is being disposed, flushes immediately.
   * Otherwise, schedules a throttled flush.
   */
  private _triggerFlush(): void | Promise<void> {
    if (this._isDisposing) {
      return this._flush();
    } else {
      void this._flushThrottled();
      return;
    }
  }

  /**
   * Flushes all dirty shards and the manifest to storage.
   * This method is called automatically by the throttled flush function,
   * but can also be called explicitly to force an immediate flush.
   *
   * Uses transaction-like semantics to ensure consistency:
   * 1. Collect all dirty shards and their serialized data
   * 2. Save all shards to storage
   * 3. Save the manifest to storage
   * 4. Only mark shards as clean after all operations succeed
   */
  private async _flush(): Promise<void> {
    const dirtyShardIds: ShardId[] = [];
    const emptyShardIds: ShardId[] = [];
    let manifestDirty = false;

    // Identify dirty shards and empty shards
    for (const [shardId, shard] of this._shardCache.entries()) {
      // Check for empty shards
      if (shard.checkpointCount === 0) {
        emptyShardIds.push(shardId);
        manifestDirty = true;
      }
      // Check for dirty shards
      else if (shard.isDirty) {
        dirtyShardIds.push(shardId);
        // Update manifest with latest shard metadata
        this._manifest.shards[shardId] = shard.getMetadata();
        manifestDirty = true;
      }
    }

    // Update manifest timestamp if there are any changes
    if (manifestDirty) {
      this._manifest.lastUpdated = Date.now();

      try {
        // Collect serialized data for all dirty shards before saving anything
        // This prevents partial updates if serialization fails
        const dirtyShardData = new Map<ShardId, SerializedShard>();
        for (const shardId of dirtyShardIds) {
          const shard = this._shardCache.get(shardId);
          if (shard) {
            dirtyShardData.set(shardId, await shard.serialize());
          }
        }

        // Save all dirty shards in parallel
        await Promise.all(
          Array.from(dirtyShardData.entries()).map(
            ([shardId, serializedData]) =>
              this._storage.saveShard(shardId, serializedData),
          ),
        );

        // Delete all empty shards in parallel
        await Promise.all(
          emptyShardIds.map((shardId) => this._storage.deleteShard(shardId)),
        );

        // Handle cache eviction if needed
        const maxShards = this._options.maxCachedShards ?? 10;
        const evictedShardIds: ShardId[] = [];
        const evictionSavePromises: Promise<void>[] = [];

        // Identify shards to evict
        while (
          this._shardCache.size > maxShards &&
          this._accessOrder.length > 0
        ) {
          const oldestId = this._accessOrder.shift();
          if (oldestId) {
            const oldShard = this._shardCache.get(oldestId);
            if (oldShard && oldShard.isDirty) {
              // Queue the shard for saving before eviction
              evictionSavePromises.push(
                this._storage.saveShard(oldestId, await oldShard.serialize()),
              );
            }
            evictedShardIds.push(oldestId);
          }
        }

        // Save all evicted dirty shards in parallel
        await Promise.all(evictionSavePromises);

        // Save manifest after all operations are complete
        await this._storage.saveManifest(this._manifest);

        // Only after all operations succeed, update the in-memory state

        // Mark all saved shards as clean - only do this AFTER manifest is saved
        // to ensure consistency if manifest save fails
        for (const shardId of dirtyShardIds) {
          const shard = this._shardCache.get(shardId);
          if (shard) {
            shard.clearDirty();
          }
        }

        // Remove empty shards from cache and manifest
        for (const shardId of emptyShardIds) {
          this._shardCache.delete(shardId);
          delete this._manifest.shards[shardId];
        }

        // Remove evicted shards from cache
        for (const shardId of evictedShardIds) {
          this._shardCache.delete(shardId);
        }
      } catch (error) {
        // Log error but don't fail the operation
        this._logger.error("Failed to flush shards:", error);
        // Don't clear dirty flags when flush fails - this ensures data will be saved on next attempt
        // Could implement retry logic here
      }
    }
  }

  /**
   * Flushes all pending changes to storage.
   * This can be called explicitly to ensure all changes are persisted.
   */
  public async flush(): Promise<void> {
    await this._flush();
  }

  /**
   * Gets the current size of the shard cache.
   * This is primarily used for testing.
   * @returns The number of shards in the cache
   */
  public getCacheSize(): number {
    return this._shardCache.size;
  }

  /**
   * Gets the shard data for a key.
   *
   * @param keyData - Key data to get shard for
   * @returns The shard data
   */
  getShard = async (keyData: CheckpointKeyInputData): Promise<ShardData> => {
    const shardId = this._getShardId(keyData);
    return await this.getShardById(shardId);
  };

  /**
   * Gets the shard data for a shard ID.
   *
   * @param shardId - Shard ID to get shard for
   * @returns The shard data
   */
  getShardById = async (shardId: ShardId): Promise<ShardData> => {
    return await this._loadShard(shardId);
  };

  /**
   * Checks if a key exists in any shard.
   *
   * @param keyData - Key data to check
   * @returns true if the key exists in any shard
   */
  hasKey = async (keyData: CheckpointKeyInputData): Promise<boolean> => {
    const shardId = this._getShardId(keyData);
    const shard = await this._loadShard(shardId);
    return shard.hasKey(keyData);
  };

  /**
   * Gets checkpoints for a key, optionally filtered by timestamp and/or index ranges.
   *
   * @param keyData - Key data to get checkpoints for
   * @param options - Optional filter criteria
   * @returns Array of matching checkpoints in chronological order
   */
  getCheckpoints = async (
    keyData: CheckpointKeyInputData,
    options?: CheckpointFilterOptions,
  ): Promise<HydratedCheckpoint[]> => {
    const shardId = this._getShardId(keyData);
    const shard = await this._loadShard(shardId);
    return shard.getCheckpoints(keyData, options) || [];
  };

  /**
   * Gets the latest checkpoint for a key.
   *
   * @param keyData - Key data to get checkpoint for
   * @returns Latest checkpoint or undefined if none exists
   */
  getLatestCheckpoint = async (
    keyData: CheckpointKeyInputData,
  ): Promise<HydratedCheckpoint | undefined> => {
    const shardId = this._getShardId(keyData);
    const shard = await this._loadShard(shardId);
    return shard.getLatestCheckpoint(keyData);
  };

  /**
   * Adds a new checkpoint for a key.
   *
   * @param keyData - Key data to add checkpoint for
   * @param checkpoint - Checkpoint to add
   */
  addCheckpoint = async (
    keyData: CheckpointKeyInputData,
    checkpoint: HydratedCheckpoint,
  ): Promise<void> => {
    await this._performShardOperation(keyData, (shard) => {
      shard.addCheckpoint(keyData, checkpoint);
    });
  };

  /**
   * Updates an existing checkpoint for a key.
   *
   * Note: Dirty state is tracked internally by the ShardData class.
   *
   * @param keyData - Key data to update checkpoint for
   * @param checkpoint - Checkpoint to update
   */
  updateCheckpoint = async (
    keyData: CheckpointKeyInputData,
    checkpoint: HydratedCheckpoint,
  ): Promise<void> => {
    await this._performShardOperation(keyData, (shard) => {
      shard.updateCheckpoint(keyData, checkpoint);
    });
  };

  /**
   * Removes checkpoints for a key based on timestamp and/or index ranges.
   *
   * @param keyData - Key data to remove checkpoints for
   * @param options - Optional filter criteria for which checkpoints to remove
   * @returns true if any checkpoints were removed
   */
  removeCheckpoint = async (
    keyData: CheckpointKeyInputData,
    options?: CheckpointFilterOptions,
  ): Promise<boolean> => {
    let removed = false;
    await this._performShardOperation(keyData, (shard) => {
      removed = shard.removeCheckpoint(keyData, options);
      // Note: Empty shard detection and deletion will be handled in _flush
    });
    return removed;
  };

  /**
   * Helper function to perform an operation on a shard and trigger a flush.
   * @param keyData - Key data to identify the shard
   * @param operation - Function that performs the operation on the shard
   */
  private async _performShardOperation(
    keyData: CheckpointKeyInputData,
    operation: (shard: ShardData) => void,
  ): Promise<void> {
    const shardId = this._getShardId(keyData);
    await this._performShardOperationById(shardId, operation);
  }

  /**
   * Helper function to perform an operation on a shard by ID and trigger a flush.
   * @param shardId - ID of the shard to operate on
   * @param operation - Function that performs the operation on the shard
   */
  private async _performShardOperationById(
    shardId: ShardId,
    operation: (shard: ShardData) => void,
  ): Promise<void> {
    const shard = await this._loadShard(shardId);

    // Perform the operation
    operation(shard);

    // Update manifest metadata
    this._updateManifest(shardId, shard);

    // Trigger flush
    await this._triggerFlush();
  }

  /**
   * Clears all data from a shard.
   *
   * @param shardId - Shard ID to clear
   */
  clearShard = async (shardId: ShardId): Promise<void> => {
    await this._performShardOperationById(shardId, (shard) => {
      shard.clear();
    });
  };

  /**
   * Clears all data from all shards.
   * Uses transaction-like semantics to ensure consistency:
   * 1. Mark all shards as empty in memory
   * 2. Reset the manifest
   * 3. Flush all changes to storage
   * 4. Clear the cache only after successful flush
   */
  clear = async (): Promise<void> => {
    // Clear all cached shards in memory
    for (const [, shard] of this._shardCache) {
      shard.clear();
      // Mark as dirty - will be saved during flush
    }

    // Reset manifest
    this._manifest = {
      version: 1,
      lastUpdated: Date.now(),
      shards: {},
    };

    // Flush changes to storage
    await this._flush();

    // Clear cache after flush to ensure everything is saved
    this._shardCache.clear();
    this._accessOrder = [];
  };

  /**
   * Disposes of the ShardManager, ensuring all pending changes are flushed.
   * This should be called when the application is shutting down.
   *
   * Note: This method is async but the base class dispose() is synchronous.
   * To ensure proper cleanup, the application should await this method when possible.
   */
  override dispose = async (): Promise<void> => {
    this._isDisposing = true;

    // Cancel any pending throttled flush
    if (this._flushThrottled.cancel) {
      this._flushThrottled.cancel();
    }

    // Flush any pending changes
    await this._flush();

    // Call super.dispose() after flush completes
    super.dispose();
  };
}
