import type { SerializedStore } from "../agent-edit-types";
import type { ShardId, ShardManifest, SerializedShard } from "./types";

/**
 * Interface for storage operations that support sharding.
 * @template T Type of the serialized data
 */
export interface ShardedStorage<T = SerializedStore> {
  // Base storage operations
  save: (this: ShardedStorage<T>, value: T) => Promise<void>;
  load: (this: ShardedStorage<T>) => Promise<T | undefined>;

  // Shard operations
  saveShard: (
    this: ShardedStorage<T>,
    shardId: ShardId,
    data: SerializedShard,
  ) => Promise<void>;
  loadShard: (
    this: ShardedStorage<T>,
    shardId: ShardId,
  ) => Promise<SerializedShard | undefined>;
  deleteShard: (this: ShardedStorage<T>, shardId: ShardId) => Promise<void>;

  // Manifest operations
  saveManifest: (
    this: ShardedStorage<T>,
    manifest: ShardManifest,
  ) => Promise<void>;
  loadManifest: (this: ShardedStorage<T>) => Promise<ShardManifest | undefined>;
}
