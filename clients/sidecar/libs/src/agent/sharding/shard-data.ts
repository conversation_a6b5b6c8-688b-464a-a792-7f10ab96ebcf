import type {
  CheckpointDocumentId,
  CheckpointDocumentIdFunction,
  CheckpointKeyInputData,
  ShardId,
  ShardMetadata,
  CheckpointFilterOptions,
  SerializedShard,
} from "./types";
import { filterCheckpoints } from "./utils";
import { QualifiedPathName } from "../../workspace/qualified-path-name";
import {
  HydratedCheckpoint,
  isSerializedCheckpoint,
  isDehydratedCheckpoint,
} from "./checkpoint-types";
import { hydrateCheckpoint, dehydrateCheckpoint } from "./checkpoint-hydration";
import { getLogger } from "../../logging";

/**
 * Manages the data for a single shard, including all checkpoints for paths
 * assigned to this shard.
 *
 * The ShardData class is responsible for the in-memory representation of a shard,
 * providing a rich API for checkpoint operations while abstracting away the
 * storage details. Key responsibilities include:
 *
 * 1. Checkpoint Management:
 *    - Adding, updating, and removing checkpoints
 *    - Retrieving checkpoints by various criteria (ID, timestamp, etc.)
 *    - Maintaining chronological ordering of checkpoints
 *
 * 2. Serialization/Deserialization:
 *    - Converting between in-memory and storage formats
 *    - Working with the hydration/dehydration system
 *    - Supporting backwards compatibility with legacy formats
 *
 * 3. Tracking:
 *    - Maintaining metadata about the shard's contents
 *    - Tracking which files and conversations are present
 *    - Managing dirty state for efficient storage operations
 *
 * Each path in a shard can have multiple checkpoints, which are always stored
 * in chronological order by timestamp for consistent retrieval behavior.
 */
export class ShardData {
  /** Map of checkpoint IDs to their checkpoints */
  private _checkpoints: Map<CheckpointDocumentId, HydratedCheckpoint[]> =
    new Map();
  /** Timestamp of the last modification to this shard */
  private _lastModified: number = Date.now();
  /** Flag indicating whether the shard has unsaved changes */
  private _isDirty: boolean = false;
  /** Set of checkpoint IDs that are dirty and need to be saved */
  private _dirtyCheckpoints: Set<string> = new Set();

  /**
   * Creates a new ShardData instance.
   *
   * @param _id - Unique identifier for this shard
   * @param _checkpointDocumentIdFn - Function to generate checkpoint IDs
   */
  constructor(
    private readonly _id: ShardId,
    private readonly _checkpointDocumentIdFn: CheckpointDocumentIdFunction,
  ) {}

  /**
   * Creates a new ShardData instance from serialized data.
   *
   * This static factory method is a key part of the deserialization process and
   * backwards compatibility strategy. It handles:
   *
   * 1. Validating the serialized shard data (ID matching, structure)
   * 2. Detecting and processing different checkpoint formats (SerializedCheckpoint or DehydratedCheckpoint)
   * 3. Hydrating checkpoints using the universal hydration system
   * 4. Reconstructing the in-memory representation with proper ordering
   *
   * The method works with both legacy and current checkpoint formats, ensuring
   * that older data can still be loaded and used with the current system.
   *
   * @param id - The shard ID to validate against the serialized data
   * @param shard - The serialized shard data from storage
   * @param checkpointDocumentIdFn - Function to generate consistent checkpoint IDs
   * @returns A new ShardData instance with hydrated checkpoints
   * @throws Error if the shard ID doesn't match or deserialization fails
   */
  public static async fromSerialized(
    id: ShardId,
    shard: SerializedShard,
    checkpointDocumentIdFn: CheckpointDocumentIdFunction,
  ): Promise<ShardData> {
    if (shard.id !== id) {
      throw new Error(`Shard ID mismatch: expected ${id}, got ${shard.id}`);
    }

    try {
      // Create new ShardData instance
      const shardData = new ShardData(id, checkpointDocumentIdFn);
      // Process each checkpoint in the serialized shard
      for (const entry of Object.entries(shard.checkpoints)) {
        for (const checkpoint of entry[1]) {
          // Try to parse path information from the checkpoint
          let path: QualifiedPathName;
          if (isSerializedCheckpoint(checkpoint)) {
            path = QualifiedPathName.from(checkpoint.document.path);
          } else if (isDehydratedCheckpoint(checkpoint)) {
            path = QualifiedPathName.from(checkpoint.documentMetadata.path);
          } else {
            getLogger("ShardData").warn(
              `Unknown checkpoint format: ${JSON.stringify(checkpoint)}`,
            );
            continue;
          }

          // Create key data for the checkpoint
          const keyData = {
            conversationId: checkpoint.conversationId,
            path,
          };

          // Hydrate the checkpoint (handles both serialized and dehydrated formats)
          const hydratedCheckpoint = await hydrateCheckpoint(checkpoint, path);

          // Add the hydrated checkpoint to the shard
          shardData.addCheckpoint(keyData, hydratedCheckpoint);
        }
      }

      return shardData;
    } catch (e) {
      // Create a properly typed error
      const errorMessage = `Failed to deserialize shard: ${e instanceof Error ? e.message : String(e)}`;
      // Using a type assertion to avoid ESLint errors
      throw new Error(errorMessage);
    }
  }

  /** Gets the unique identifier of this shard */
  get id(): ShardId {
    return this._id;
  }

  /**
   * Gets the total size in bytes of all checkpoint data.
   * Computed by summing the lengths of all original and modified code.
   */
  get size(): number {
    return Array.from(this._checkpoints.values()).reduce(
      (total, checkpoints) =>
        total +
        checkpoints.reduce(
          (pathTotal, cp) =>
            pathTotal +
            (cp.document.originalCode ? cp.document.originalCode.length : 0) +
            (cp.document.modifiedCode ? cp.document.modifiedCode.length : 0),
          0,
        ),
      0,
    );
  }

  /** Gets the total number of checkpoints across all paths */
  get checkpointCount(): number {
    return Array.from(this._checkpoints.values()).reduce(
      (total, checkpoints) => total + checkpoints.length,
      0,
    );
  }

  /** Gets the timestamp of the last modification */
  get lastModified(): number {
    return this._lastModified;
  }

  /** Gets the list of checkpoint IDs stored in this shard */
  get checkpointDocumentIds(): CheckpointDocumentId[] {
    return Array.from(this._checkpoints.keys());
  }

  /**
   * Gets all checkpoints for a checkpoint ID.
   * @param checkpointDocumentId - The checkpoint ID to get checkpoints for
   * @returns Array of checkpoints in chronological order, or undefined if ID not found
   */
  getCheckpointsById(
    checkpointDocumentId: CheckpointDocumentId,
  ): HydratedCheckpoint[] | undefined {
    return this._checkpoints.get(checkpointDocumentId);
  }

  /**
   * Serializes the shard data for storage.
   * @returns Serialized representation of the shard
   */
  async serialize(): Promise<SerializedShard> {
    try {
      const serialized: SerializedShard = {
        id: this.id,
        checkpoints: {},
        metadata: this.getMetadata(),
      };

      // Process each checkpoint ID
      for (const checkpointDocumentId of this.checkpointDocumentIds) {
        const checkpoints = this.getCheckpointsById(checkpointDocumentId);
        if (checkpoints) {
          // Dehydrate each checkpoint that is dirty
          serialized.checkpoints[checkpointDocumentId] = await Promise.all(
            checkpoints.map(async (checkpoint) => {
              // Check if the checkpoint is dirty
              const isDirty = this.isCheckpointDirty(checkpoint);
              // Dehydrate the checkpoint with the dirty flag to indicate it should be written to disk
              const dehydrated = await dehydrateCheckpoint(checkpoint, isDirty);
              // Mark the checkpoint as clean after dehydration
              this.markCheckpointClean(checkpoint);
              return dehydrated;
            }),
          );
        }
      }
      return serialized;
    } catch (e) {
      const errorMessage = `Failed to serialize shard: ${e instanceof Error ? e.message : String(e)}`;
      throw new Error(errorMessage);
    }
  }

  /**
   * Gets the metadata for this shard.
   * @returns Summary information about the shard's contents
   */
  getMetadata(): ShardMetadata {
    return {
      checkpointDocumentIds: this.checkpointDocumentIds,
      size: this.size,
      checkpointCount: this.checkpointCount,
      lastModified: this._lastModified,
    };
  }

  /**
   * Checks if the shard has unsaved changes.
   * @returns true if the shard has been modified since last save
   */
  get isDirty(): boolean {
    return this._isDirty;
  }

  /**
   * Clears the dirty flag after the shard has been saved.
   */
  clearDirty(): void {
    this._isDirty = false;
    this._dirtyCheckpoints.clear();
  }

  /**
   * Checks if a checkpoint is dirty.
   * @param checkpoint - The checkpoint to check
   * @returns true if the checkpoint is dirty
   */
  isCheckpointDirty(checkpoint: HydratedCheckpoint): boolean {
    return this._dirtyCheckpoints.has(checkpoint.sourceToolCallRequestId);
  }

  /**
   * Marks a checkpoint as dirty.
   * @param checkpoint - The checkpoint to mark as dirty
   */
  markCheckpointDirty(checkpoint: HydratedCheckpoint): void {
    this._dirtyCheckpoints.add(checkpoint.sourceToolCallRequestId);
    this._isDirty = true;
  }

  /**
   * Marks a checkpoint as clean.
   * @param checkpoint - The checkpoint to mark as clean
   */
  markCheckpointClean(checkpoint: HydratedCheckpoint): void {
    this._dirtyCheckpoints.delete(checkpoint.sourceToolCallRequestId);
  }

  /**
   * Checks if a key exists in this shard.
   * @param keyData - The key data to check
   * @returns true if the key has checkpoints in this shard
   */
  hasKey(keyData: CheckpointKeyInputData): boolean {
    return this._checkpoints.has(this._checkpointDocumentIdFn(keyData));
  }

  /**
   * Gets all checkpoints in the shard, in no particular order.
   * @returns Array of all checkpoints
   */
  getAllCheckpoints(): HydratedCheckpoint[] {
    return Array.from(this._checkpoints.values()).flat();
  }

  /**
   * Gets all checkpoints in the shard.
   * @returns Array of checkpoints in chronological order
   */
  getAllCheckpointsSorted(): HydratedCheckpoint[] {
    return this.getAllCheckpoints().sort(
      // Sort by timestamp, oldest to newest
      // If a is older than b, a.timestamp < b.timestamp, so the result is negative
      // which means a comes before b
      (a, b) => a.timestamp - b.timestamp,
    );
  }

  /**
   * Gets all checkpoints in chronological order, from oldest to newest.
   * This is an alias for getAllCheckpointsSorted() to match AgentEditStore API.
   *
   * @returns Array of checkpoints
   */
  get orderedCheckpointsByTime(): HydratedCheckpoint[] {
    return this.getAllCheckpointsSorted();
  }

  /**
   * Gets the total number of checkpoints across all paths.
   * This is an alias for checkpointCount to match AgentEditStore API.
   */
  get totalCheckpointCount(): number {
    return this.checkpointCount;
  }

  /**
   * Gets the global checkpoint number (0-based index) for a specific tool call.
   * This uses the global ordering of checkpoints across all files.
   *
   * @param sourceToolCallRequestId The ID of the tool call
   * @returns The global checkpoint number (0-based) or undefined if not found
   */
  getCheckpointNumberForToolCall(
    sourceToolCallRequestId: string,
  ): number | undefined {
    const checkpointNumber = this.orderedCheckpointsByTime.findIndex(
      (checkpoint) =>
        checkpoint.sourceToolCallRequestId === sourceToolCallRequestId,
    );

    return checkpointNumber === -1 ? undefined : checkpointNumber;
  }

  /**
   * Gets a checkpoint by its source tool call request ID.
   *
   * @param sourceToolCallRequestId The ID of the tool call
   * @returns The checkpoint if found, undefined otherwise
   */
  getCheckpointBySourceId(
    sourceToolCallRequestId: string,
  ): HydratedCheckpoint | undefined {
    return this.getAllCheckpoints().find(
      (checkpoint) =>
        checkpoint.sourceToolCallRequestId === sourceToolCallRequestId,
    );
  }

  /**
   * Gets checkpoints for a key, optionally filtered by timestamp and/or index ranges.
   * @param keyData - The key data to get checkpoints for
   * @param options - Optional filter criteria
   * @returns Array of matching checkpoints in chronological order, or undefined if key not found
   */
  getCheckpoints(
    keyData: CheckpointKeyInputData,
    options?: CheckpointFilterOptions,
  ): HydratedCheckpoint[] | undefined {
    const checkpoints = this._checkpoints.get(
      this._checkpointDocumentIdFn(keyData),
    );
    if (!checkpoints?.length) return undefined;
    if (!options) return checkpoints;

    const filtered = filterCheckpoints(checkpoints, options);
    return filtered.length > 0 ? filtered : [];
  }

  /**
   * Gets the most recent checkpoint for a key.
   * @param keyData - The key data to get the checkpoint for
   * @returns The latest checkpoint, or undefined if key not found
   */
  getLatestCheckpoint(
    keyData: CheckpointKeyInputData,
  ): HydratedCheckpoint | undefined {
    const checkpoints = this._checkpoints.get(
      this._checkpointDocumentIdFn(keyData),
    );
    return checkpoints?.[checkpoints.length - 1];
  }

  /**
   * Adds a new checkpoint for a key.
   * The checkpoint is added to the end of the key's checkpoint array,
   * maintaining chronological order by timestamp.
   *
   * @param keyData - The key data to add the checkpoint for
   * @param checkpoint - The checkpoint to add
   */
  addCheckpoint(
    keyData: CheckpointKeyInputData,
    checkpoint: HydratedCheckpoint,
  ): void {
    const id = this._checkpointDocumentIdFn(keyData);
    const existing = this._checkpoints.get(id) || [];

    // Add new checkpoint and sort by timestamp
    const updated = [...existing, checkpoint].sort(
      (a, b) => a.timestamp - b.timestamp,
    );
    this._checkpoints.set(id, updated);

    // Mark the checkpoint as dirty
    this.markCheckpointDirty(checkpoint);
    this._lastModified = Date.now();
  }

  /**
   * Updates an existing checkpoint for a key.
   * The checkpoint is updated in place, based on sourceToolCallRequestId.
   *
   * @param keyData - The key data to update the checkpoint for
   * @param checkpoint - The checkpoint to update
   */
  updateCheckpoint(
    keyData: CheckpointKeyInputData,
    checkpoint: HydratedCheckpoint,
  ): void {
    const id = this._checkpointDocumentIdFn(keyData);
    const existing = this._checkpoints.get(id) || [];

    // Find the index of the checkpoint to update
    const index = existing.findIndex(
      (cp) => cp.sourceToolCallRequestId === checkpoint.sourceToolCallRequestId,
    );
    if (index === -1) {
      return;
    }

    // Update the checkpoint in place
    existing[index] = checkpoint;
    this._checkpoints.set(id, existing);

    // Mark the checkpoint as dirty
    this.markCheckpointDirty(checkpoint);
    this._lastModified = Date.now();
  }

  /**
   * Gets all conversation IDs that have checkpoints in this shard.
   *
   * @returns Array of conversation IDs
   */
  getAllTrackedConversationIds(): string[] {
    const conversationIds = new Set<string>();
    for (const checkpoint of this.getAllCheckpoints()) {
      conversationIds.add(checkpoint.conversationId);
    }
    return Array.from(conversationIds);
  }

  /**
   * Gets all file paths that currently have checkpoints for a conversation.
   *
   * @param conversationId - The conversation ID
   * @returns Array of QualifiedPathName objects for tracked files
   */
  getAllTrackedFilePaths(conversationId: string): QualifiedPathName[] {
    // Use a map to deduplicate paths
    const trackedFiles = new Map<string, QualifiedPathName>();
    for (const checkpoint of this.getAllCheckpoints()) {
      if (checkpoint.conversationId === conversationId) {
        trackedFiles.set(
          checkpoint.document.filePath.absPath,
          checkpoint.document.filePath,
        );
      }
    }
    return Array.from(trackedFiles.values());
  }

  /**
   * Removes checkpoints for a key based on timestamp and/or index ranges.
   * If no options are provided, removes all checkpoints for the key.
   *
   * @param keyData - The key data to remove checkpoints for
   * @param options - Optional filter criteria for which checkpoints to remove
   * @returns true if any checkpoints were removed
   */
  removeCheckpoint(
    keyData: CheckpointKeyInputData,
    options?: CheckpointFilterOptions,
  ): boolean {
    const id = this._checkpointDocumentIdFn(keyData);
    const checkpoints = this._checkpoints.get(id);

    if (!checkpoints?.length) {
      return false;
    }

    if (!options) {
      // Remove all checkpoints for key
      this._checkpoints.delete(id);
      this._lastModified = Date.now();
      this._isDirty = true;
      return true;
    }

    // Get checkpoints that match the removal criteria
    const toKeep = filterCheckpoints(checkpoints, options, false);
    if (toKeep.length === checkpoints.length) {
      return false;
    }

    if (toKeep.length === 0) {
      this._checkpoints.delete(id);
    } else {
      this._checkpoints.set(id, toKeep);
    }
    this._lastModified = Date.now();
    this._isDirty = true;
    return true;
  }

  /**
   * Removes all checkpoints from the shard.
   */
  clear(): void {
    this._checkpoints.clear();
    this._lastModified = Date.now();
    this._isDirty = true;
  }

  /**
   * Marks all checkpoints in the shard as dirty.
   * This is useful for forcing dehydration of all checkpoints, for example
   * if the document content storage format has changed.
   */
  markAllCheckpointsDirty(): void {
    for (const checkpoints of this._checkpoints.values()) {
      for (const checkpoint of checkpoints) {
        this.markCheckpointDirty(checkpoint);
      }
    }
  }
}
