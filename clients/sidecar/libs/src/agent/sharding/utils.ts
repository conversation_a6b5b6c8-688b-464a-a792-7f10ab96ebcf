import type {
  ShardId,
  ShardManifest,
  SerializedShard,
  CheckpointFilterOptions,
  ValidationResult,
} from "./types";
import {
  isSerializedCheckpoint,
  isDehydratedCheckpoint,
} from "./checkpoint-types";

/**
 * Validates that a string is a valid shard ID.
 * Valid shard IDs are non-empty strings.
 *
 * @param shardId - String to validate
 * @returns true if the shard ID is valid
 */
export function validateShardId(shardId: ShardId): ValidationResult {
  if (typeof shardId !== "string") {
    return {
      isValid: false,
      reason: "Shard ID must be a string",
    };
  }
  if (shardId.length === 0) {
    return {
      isValid: false,
      reason: "Shard ID cannot be empty",
    };
  }
  return { isValid: true };
}

/**
 * Validates a manifest object.
 * Checks that:
 * - Version and timestamps are numbers
 * - Shards object exists and is not null
 * - All shard IDs are valid
 * - All metadata fields have correct types
 * - All path hashes are valid
 *
 * @param manifest - Manifest object to validate
 * @returns true if the manifest is valid
 */
export function validateManifest(manifest: ShardManifest): ValidationResult {
  if (
    typeof manifest.version !== "number" ||
    typeof manifest.lastUpdated !== "number"
  ) {
    return {
      isValid: false,
      reason: "Invalid manifest version or lastUpdated timestamp",
    };
  }

  if (!manifest.shards || typeof manifest.shards !== "object") {
    return {
      isValid: false,
      reason: "Missing or invalid shards object",
    };
  }

  for (const [shardId, metadata] of Object.entries(manifest.shards)) {
    const shardIdResult = validateShardId(shardId);
    if (!shardIdResult.isValid) {
      return shardIdResult;
    }
    if (!metadata || typeof metadata !== "object") {
      return {
        isValid: false,
        reason: `Invalid metadata object for shard ${shardId}`,
      };
    }
    if (typeof metadata.size !== "number") {
      return {
        isValid: false,
        reason: `Invalid size field for shard ${shardId}`,
      };
    }
    if (typeof metadata.checkpointCount !== "number") {
      return {
        isValid: false,
        reason: `Invalid checkpointCount field for shard ${shardId}`,
      };
    }
    if (typeof metadata.lastModified !== "number") {
      return {
        isValid: false,
        reason: `Invalid lastModified field for shard ${shardId}`,
      };
    }
    if (!Array.isArray(metadata.checkpointDocumentIds)) {
      return {
        isValid: false,
        reason: `Invalid checkpointDocumentIds array for shard ${shardId}`,
      };
    }

    for (const id of metadata.checkpointDocumentIds) {
      if (typeof id !== "string" || id.length === 0) {
        return {
          isValid: false,
          reason: `Invalid checkpoint document ID in shard ${shardId}: ${id}`,
        };
      }
    }
  }

  return { isValid: true };
}

/**
 * Filters a manifest to only include valid shards.
 * @param manifest - Manifest to filter
 * @returns Filtered manifest
 */
export function filterInvalidShardsFromManifest(
  manifest: ShardManifest,
): ShardManifest {
  const validShards: ShardManifest["shards"] = {};
  for (const [shardId, metadata] of Object.entries(manifest.shards)) {
    const shardResult = validateShard({
      id: shardId,
      checkpoints: {},
      metadata,
    } as unknown as SerializedShard);
    if (shardResult.isValid) {
      validShards[shardId] = metadata;
    }
  }
  let version: number = 1;
  if (typeof manifest.version === "number" && manifest.version >= 1) {
    version = manifest.version + 1;
  } else {
    version = 1;
  }
  return {
    ...manifest,
    version,
    lastUpdated: Date.now(),
    shards: validShards,
  };
}

/**
 * Validates a serialized shard object.
 * Checks that:
 * - Shard ID is valid
 * - Metadata is valid (through manifest validation)
 * - Checkpoint structure is valid
 * - All path hashes are valid
 * - Each checkpoint has required fields
 * - Metadata path hashes match checkpoint keys
 *
 * @param shard - Serialized shard to validate
 * @returns true if the shard is valid
 */
export function validateShard(shard: SerializedShard): ValidationResult {
  // Validate shard ID
  const shardIdResult = validateShardId(shard.id);
  if (!shardIdResult.isValid) return shardIdResult;

  // Validate metadata through manifest validation
  const manifestResult = validateManifest({
    version: 1,
    lastUpdated: Date.now(),
    shards: { [shard.id]: shard.metadata },
  });
  if (!manifestResult.isValid) return manifestResult;

  // Validate checkpoints structure
  if (typeof shard.checkpoints !== "object" || !shard.checkpoints) {
    return {
      isValid: false,
      reason: "Missing or invalid checkpoints object",
    };
  }

  // Validate each checkpoint ID and its checkpoints
  for (const [id, checkpoints] of Object.entries(shard.checkpoints)) {
    // Validate checkpoint ID
    if (typeof id !== "string" || id.length === 0) {
      return {
        isValid: false,
        reason: `Invalid checkpoint ID: ${id}`,
      };
    }

    // Validate checkpoints array
    if (!Array.isArray(checkpoints)) {
      return {
        isValid: false,
        reason: `Checkpoints for ID ${id} must be an array`,
      };
    }

    // Validate each checkpoint
    for (const cp of checkpoints) {
      // Check common properties
      if (
        typeof cp !== "object" ||
        !cp ||
        typeof cp.sourceToolCallRequestId !== "string" ||
        typeof cp.timestamp !== "number" ||
        typeof cp.conversationId !== "string"
      ) {
        return {
          isValid: false,
          reason: `Invalid checkpoint common properties in ID ${id}`,
        };
      }

      // Use type guards to check if it's a SerializedCheckpoint or DehydratedCheckpoint
      if (isSerializedCheckpoint(cp)) {
        // Validate SerializedCheckpoint
        const validationResult = validateSerializedCheckpointDocument(
          cp.document,
          id,
        );
        if (!validationResult.isValid) {
          return validationResult;
        }
      } else if (isDehydratedCheckpoint(cp)) {
        // Validate DehydratedCheckpoint
        if (typeof cp.documentMetadata !== "object" || !cp.documentMetadata) {
          return {
            isValid: false,
            reason: `Invalid DehydratedCheckpoint structure in ID ${id}`,
          };
        }
      } else {
        return {
          isValid: false,
          reason: `Unknown checkpoint type in ID ${id}`,
        };
      }
    }
  }

  // Validate that metadata checkpoint IDs match checkpoint keys
  const checkpointDocumentIds = new Set(Object.keys(shard.checkpoints));
  const metadataIds = new Set(shard.metadata.checkpointDocumentIds);

  if (checkpointDocumentIds.size !== metadataIds.size) {
    return {
      isValid: false,
      reason:
        "Mismatch between checkpoint IDs in metadata and actual checkpoints",
    };
  }

  for (const id of checkpointDocumentIds) {
    if (!metadataIds.has(id)) {
      return {
        isValid: false,
        reason:
          "Mismatch between checkpoint IDs in metadata and actual checkpoints",
      };
    }
  }

  return { isValid: true };
}

/**
 * Validates the document structure of a SerializedCheckpoint.
 * Breaks down the complex validation into smaller, more readable checks.
 *
 * @param document - The document to validate
 * @param id - The checkpoint ID (for error messages)
 * @returns Validation result with isValid flag and reason if invalid
 */
function validateSerializedCheckpointDocument(
  document: unknown,
  id: string,
): { isValid: boolean; reason?: string } {
  // Check if document is a valid object
  if (typeof document !== "object" || !document) {
    return {
      isValid: false,
      reason: `Invalid SerializedCheckpoint structure in ID ${id}`,
    };
  }

  // Check document code properties
  const doc = document as Record<string, unknown>;
  if (
    (doc.originalCode !== undefined && typeof doc.originalCode !== "string") ||
    (doc.modifiedCode !== undefined && typeof doc.modifiedCode !== "string")
  ) {
    return {
      isValid: false,
      reason: `Invalid SerializedCheckpoint structure in ID ${id}`,
    };
  }

  // Check document path
  if (typeof doc.path !== "object" || !doc.path) {
    return {
      isValid: false,
      reason: `Invalid SerializedCheckpoint structure in ID ${id}`,
    };
  }

  // Check path properties
  const path = doc.path as Record<string, unknown>;
  if (typeof path.rootPath !== "string" || typeof path.relPath !== "string") {
    return {
      isValid: false,
      reason: `Invalid SerializedCheckpoint structure in ID ${id}`,
    };
  }

  return { isValid: true };
}

export function filterCheckpoints<
  T extends { timestamp: number; sourceToolCallRequestId?: string },
>(
  checkpoints: T[],
  options: CheckpointFilterOptions,
  keep: boolean = true,
): T[] {
  const {
    minTimestamp,
    maxTimestamp,
    minIdx,
    maxIdx,
    sourceToolCallRequestIds,
  } = options;

  return checkpoints.filter((cp, idx) => {
    // Check if checkpoint matches timestamp criteria
    const matchesTimestamp =
      (minTimestamp === undefined || cp.timestamp >= minTimestamp) &&
      (maxTimestamp === undefined || cp.timestamp < maxTimestamp);

    // Check if checkpoint matches index criteria
    const matchesIndex =
      (minIdx === undefined || idx >= minIdx) &&
      (maxIdx === undefined || idx < maxIdx);

    // Check if checkpoint matches sourceToolCallRequestId criteria
    const matchesSourceToolCallRequestId =
      sourceToolCallRequestIds === undefined ||
      (cp.sourceToolCallRequestId !== undefined &&
        sourceToolCallRequestIds.has(cp.sourceToolCallRequestId));

    // If no criteria specified, match based on keep mode
    const hasTimestampCriteria =
      minTimestamp !== undefined || maxTimestamp !== undefined;
    const hasIndexCriteria = minIdx !== undefined || maxIdx !== undefined;
    const hasSourceToolCallRequestIdCriteria =
      sourceToolCallRequestIds !== undefined;
    const hasCriteria =
      hasTimestampCriteria ||
      hasIndexCriteria ||
      hasSourceToolCallRequestIdCriteria;

    // When no criteria are specified:
    // - In keep mode (keep=true), keep all checkpoints
    // - In remove mode (keep=false), remove all checkpoints
    if (!hasCriteria) {
      return keep;
    }

    // When criteria are specified:
    // - In keep mode (keep=true), keep checkpoints that match ALL criteria
    // - In remove mode (keep=false), remove checkpoints that match ALL criteria
    const matches =
      (!hasTimestampCriteria || matchesTimestamp) &&
      (!hasIndexCriteria || matchesIndex) &&
      (!hasSourceToolCallRequestIdCriteria || matchesSourceToolCallRequestId);

    return keep ? matches : !matches;
  });
}
