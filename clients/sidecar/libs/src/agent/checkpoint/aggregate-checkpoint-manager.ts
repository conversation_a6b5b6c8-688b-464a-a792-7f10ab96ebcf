import { QualifiedPathName } from "../../workspace/qualified-path-name";
import { ShardManager } from "../sharding/shard-manager";
import { DisposableService } from "../../lifecycle/disposable-service";
import { ShardedStorage } from "../sharding/storage";
import {
  FileDeletedEvent,
  FileDidMoveEvent,
  SerializedStore,
  TextDocumentChangeEvent,
} from "../agent-edit-types";
import { HydratedCheckpoint } from "../sharding/checkpoint-types";
import { CheckpointKeyInputData, ShardId } from "../sharding/types";
import { createRequestId } from "../../utils/request-id";
import { DiffViewDocument } from "../../diff-view/document";
import type {
  AggregateCheckpointInfo,
  GetAggregateCheckpointsOptions,
  UpdateLatestCheckpointOptions,
} from "./checkpoint-types";
import { diffLines } from "diff";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>ileChanges, EditEventSource } from "../../chat/chat-types";
import { type ISidecarDisposable } from "../../lifecycle/disposable-types";
import { type AugmentLogger, getLogger } from "../../logging";
import { getClientWorkspaces } from "../../client-interfaces/client-workspaces";

/**
 * AggregateCheckpointManager provides a high-level API for managing file checkpoints
 * across conversations. It acts as an index on top of file-specific checkpoints stored
 * in the ShardManager.
 *
 * Key responsibilities:
 * 1. Track files for specific conversations
 * 2. Create and update checkpoints for tracked files
 * 3. Retrieve file states at specific timestamps
 * 4. Generate aggregate checkpoints across multiple files
 * 5. Handle document change events to keep checkpoints in sync
 * 6. Support reverting to previous states
 * 7. Handle file deletion events by removing checkpoints for deleted files
 * 8. Handle file move/rename events by updating file paths in checkpoints
 *
 * This class is designed to work with the ShardManager for storage and retrieval
 * of checkpoint data, while providing a simpler API for consumers.
 */
export class AggregateCheckpointManager extends DisposableService {
  public readonly shardManager: ShardManager;
  private readonly _shardFunction = (keyData: {
    conversationId: string;
  }): ShardId => `shard-${keyData.conversationId}`;
  private _currentConversationId: string | undefined;
  private readonly _logger: AugmentLogger = getLogger(
    "AggregateCheckpointManager",
  );
  private _agentEditListHasUpdatesCallbacks = new Set<() => void>();

  constructor(
    private _storage: ShardedStorage<SerializedStore>,
    private _getMemoriesAbsPath: () => string | undefined,
    private _onDocumentChange: (
      cb: (event: TextDocumentChangeEvent) => void,
    ) => ISidecarDisposable,
    private _onFileDeleted: (
      cb: (event: FileDeletedEvent) => void,
    ) => ISidecarDisposable,
    private _onFileDidMove: (
      cb: (event: FileDidMoveEvent) => void,
    ) => ISidecarDisposable,
  ) {
    super();
    this.shardManager = new ShardManager(this._storage, this._shardFunction);

    // Register document change handler
    this.addDisposable(
      this._onDocumentChange((event: TextDocumentChangeEvent) => {
        void this._handleDocumentChange(event);
      }),
    );

    // Register file deletion handler
    this.addDisposable(
      this._onFileDeleted((event: FileDeletedEvent) => {
        void this._handleFileDeleted(event);
      }),
    );

    // Register file move handler
    this.addDisposable(
      this._onFileDidMove((event: FileDidMoveEvent) => {
        void this._handleFileMoved(event);
      }),
    );

    this.addDisposable({
      dispose: () => {
        this._agentEditListHasUpdatesCallbacks.clear();
      },
    });
  }

  public get currentConversationId(): string | undefined {
    return this._currentConversationId;
  }

  public setCurrentConversation(conversationId: string): void {
    this._currentConversationId = conversationId;
  }

  /**
   * Updates the conversation ID for all checkpoints from an old ID to a new ID.
   * This is used when a conversation transitions from a temporary ID (like NEW_AGENT_KEY)
   * to a real UUID.
   *
   * @param oldConversationId - The old conversation ID to migrate from
   * @param newConversationId - The new conversation ID to migrate to
   */
  public migrateConversationId = async (
    oldConversationId: string,
    newConversationId: string,
  ): Promise<void> => {
    try {
      this._logger.debug(
        `Starting migration from ${oldConversationId} to ${newConversationId}`,
      );

      await this.shardManager.initialize();

      // Check if the old conversation shard exists in the manifest
      const oldShardId = this._shardFunction({
        conversationId: oldConversationId,
      });
      const shardExists =
        this.shardManager.manifest.shards[oldShardId] !== undefined;

      if (!shardExists) {
        this._logger.debug(
          `No shard exists for conversation ${oldConversationId}, nothing to migrate`,
        );
        return;
      }

      // Get all tracked files for the old conversation
      const trackedFiles = await this._getAllTrackedFiles(oldConversationId);

      if (trackedFiles.length === 0) {
        this._logger.debug(
          `No files to migrate from ${oldConversationId} to ${newConversationId}`,
        );
        return;
      }

      this._logger.debug(
        `Migrating ${trackedFiles.length} files from conversation ${oldConversationId} to ${newConversationId}`,
      );

      // For each tracked file, migrate all its checkpoints
      for (const filePath of trackedFiles) {
        try {
          const oldKey = { conversationId: oldConversationId, path: filePath };
          const checkpoints = await this.shardManager.getCheckpoints(oldKey);

          if (checkpoints.length === 0) {
            continue;
          }

          this._logger.debug(
            `Migrating ${checkpoints.length} checkpoints for file ${filePath.absPath}`,
          );

          // Create new checkpoints with the updated conversation ID
          const newKey = { conversationId: newConversationId, path: filePath };

          for (const checkpoint of checkpoints) {
            const newCheckpoint = {
              ...checkpoint,
              conversationId: newConversationId,
            };

            await this.shardManager.addCheckpoint(newKey, newCheckpoint);
          }

          // Remove the old checkpoints
          await this.shardManager.removeCheckpoint(oldKey);
        } catch (error) {
          this._logger.error(
            `Failed to migrate file ${filePath.absPath}: ${String(error)}`,
          );
          // Continue with other files even if one fails
        }
      }

      // Clear the old shard if it's now empty
      try {
        await this.shardManager.clearShard(oldShardId);
      } catch (error) {
        this._logger.warn(
          `Failed to clear old shard ${oldShardId}: ${String(error)}`,
        );
        // Don't fail the migration if shard cleanup fails
      }

      this._notifyAgentEditListHasUpdates();
      this._logger.debug(
        `Successfully migrated conversation from ${oldConversationId} to ${newConversationId}`,
      );
    } catch (error) {
      this._logger.error(
        `Migration failed from ${oldConversationId} to ${newConversationId}: ${String(error)}`,
      );
      throw error;
    }
  };

  public getAgentMemoriesAbsPath = (): string | undefined => {
    return this._getMemoriesAbsPath();
  };

  public onAgentEditListHasUpdates = (cb: () => void): ISidecarDisposable => {
    this._agentEditListHasUpdatesCallbacks.add(cb);
    return {
      dispose: () => {
        this._agentEditListHasUpdatesCallbacks.delete(cb);
      },
    };
  };

  private _notifyAgentEditListHasUpdates = () => {
    this._agentEditListHasUpdatesCallbacks.forEach((cb) => cb());
  };

  /**
   * Gets all file paths that currently have checkpoints for a conversation.
   *
   * @param conversationId - The conversation ID
   * @returns Array of QualifiedPathName objects for tracked files
   */
  private _getAllTrackedFiles = async (
    conversationId: string,
  ): Promise<QualifiedPathName[]> => {
    await this.shardManager.initialize();
    const shardId = this._shardFunction({ conversationId });
    const shard = await this.shardManager.getShardById(shardId);
    return shard.getAllTrackedFilePaths(conversationId);
  };

  /**
   * Creates a checkpoint for a specific file based on the current file state.
   *
   * @param conversationId - The conversation ID
   * @param filePath - The path of the file to checkpoint
   * @param options - Options for creating the checkpoint, including timestamp and editSource
   */
  private _checkpointFileState = async (
    conversationId: string,
    filePath: QualifiedPathName,
    options: { timestamp?: number; editSource?: EditEventSource } = {},
  ): Promise<string | undefined> => {
    let currentContent: string | undefined;
    try {
      currentContent = (await getClientWorkspaces().readFile(filePath.absPath))
        .contents;
    } catch (e) {
      // Probably means file does not exist
      this._logger.debug(
        `Failed to read file ${filePath.absPath}: ${String(e)}`,
      );
    }

    // If the file doesn't exist, don't create a checkpoint
    if (currentContent === undefined) {
      return undefined;
    }

    // Pull out the options w/ defaults
    const { timestamp = Date.now(), editSource = EditEventSource.UNSPECIFIED } =
      options;

    const checkpoint: HydratedCheckpoint = {
      sourceToolCallRequestId: createRequestId(),
      timestamp,
      document: new DiffViewDocument(filePath, currentContent, currentContent, {
        logger: this._logger,
      }),
      conversationId,
      editSource,
    };
    const key = { conversationId, path: filePath };
    await this.shardManager.addCheckpoint(key, checkpoint);
    this._notifyAgentEditListHasUpdates();
    return checkpoint.sourceToolCallRequestId;
  };

  /**
   * Tracks a file for a conversation by creating an initial checkpoint
   * with the current content of the file.
   *
   * @param conversationId - The conversation ID
   * @param filePath - The path of the file to track
   */
  private _trackFile = async (
    conversationId: string,
    filePath: QualifiedPathName,
  ): Promise<void> => {
    await this.shardManager.initialize();
    const key = { conversationId, path: filePath };

    // Check if this key already has a checkpoint. If so, do nothing.
    const checkpoints = await this.shardManager.getCheckpoints(key);
    if (checkpoints.length > 0) {
      this._logger.debug(`File already tracked: ${filePath.absPath}`);
      return;
    }

    this._logger.debug(`Tracking file: ${filePath.absPath}`);
    // Since this is the first checkpoint, we set the timestamp to 0, this
    // marks the checkpoint as the sentinal value and ensures
    // any future checkpoints are after this one.
    await this._checkpointFileState(conversationId, filePath, { timestamp: 0 });
  };

  /**
   * Wrapper around the shardManager's addCheckpoint method.
   * Tracks the file before adding the checkpoint.
   *
   * @param key - The key data for the checkpoint
   * @param conversationId - The conversation ID
   */
  public addCheckpoint = async (
    key: CheckpointKeyInputData,
    checkpoint: Omit<HydratedCheckpoint, "isDirty">,
  ): Promise<void> => {
    await this.shardManager.initialize();
    // Track the file before adding the checkpoint
    await this._trackFile(key.conversationId, checkpoint.document.filePath);
    await this.shardManager.addCheckpoint(key, checkpoint);
    // Write checkpoint
    if (checkpoint.document.modifiedCode === undefined) {
      this._logger.debug(
        `Deleting file: ${checkpoint.document.filePath.absPath}`,
      );
      await getClientWorkspaces().deleteFile(checkpoint.document.filePath);
    } else {
      this._logger.debug(
        `Writing file: ${checkpoint.document.filePath.absPath}`,
      );
      await getClientWorkspaces().writeFile(
        checkpoint.document.filePath,
        checkpoint.document.modifiedCode,
      );
    }
    this._notifyAgentEditListHasUpdates();
  };

  /**
   * Gets the file state at a specific timestamp by finding the closest checkpoint.
   *
   * This method implements the following logic:
   * 1. First, look for checkpoints before or at the specified timestamp
   *    - If found, return the modified code from the most recent one
   * 2. If no checkpoints exist before the timestamp, look for checkpoints after it
   *    - If found, return the original code from the earliest one
   * 3. If no checkpoints exist at all, return undefined
   *
   * This approach ensures we get the most accurate representation of the file
   * at the requested point in time, even if there isn't an exact checkpoint match.
   *
   * @param conversationId - The conversation ID to search within
   * @param filePath - The qualified path of the file
   * @param timestamp - The target timestamp to find the state for
   * @returns The file content at the timestamp, or undefined if no checkpoints exist
   */
  private _getFileStateAtTimestamp = async (
    conversationId: string,
    filePath: QualifiedPathName,
    timestamp: number,
  ): Promise<string | undefined | null> => {
    const hasFile = await this.shardManager.hasKey({
      conversationId,
      path: filePath,
    });
    if (!hasFile) {
      return null;
    }

    const key: CheckpointKeyInputData = { conversationId, path: filePath };
    const beforeCkpts = await this.shardManager.getCheckpoints(key, {
      maxTimestamp: timestamp,
    });

    // Return the state of the last checkpoint before the timestamp, if any
    const lastBeforeState = beforeCkpts.at(-1);
    if (lastBeforeState) {
      return lastBeforeState.document.modifiedCode;
    }

    // Otherwise, return the first-known state of the file
    const afterCkpts = await this.shardManager.getCheckpoints(key, {
      minTimestamp: timestamp,
    });
    const firstAfterState = afterCkpts.at(0);
    if (firstAfterState) {
      return firstAfterState.document.originalCode;
    }
    return null;
  };

  /**
   * Handles document change events by updating the latest checkpoint.
   *
   * @param event - The document change event
   */
  private _handleDocumentChange = async (
    event: TextDocumentChangeEvent,
  ): Promise<void> => {
    // Check if the qualified path name is tracked
    const filePath = QualifiedPathName.from(event.document.qualifiedPathName);
    if (filePath === undefined || this._currentConversationId === undefined) {
      return;
    }

    const newContent = event.document.getText?.();
    // No need to save, since we are being notified of a change from the workspace,
    // we don't need to propagate it back
    await this.updateLatestCheckpoint(filePath, newContent, {
      saveToWorkspace: false,
      updateSource: EditEventSource.USER_EDIT,
    });
  };

  /**
   * Handles file deletion events by removing the file from tracking.
   *
   * @param event - The file deleted event
   */
  private _handleFileDeleted = async (
    event: FileDeletedEvent,
  ): Promise<void> => {
    const filePath = QualifiedPathName.from(event.qualifiedPathName);
    if (filePath === undefined || this._currentConversationId === undefined) {
      return;
    }

    this._logger.debug(
      `Handling file deleted notification: ${filePath.absPath}`,
    );

    // Add a checkpoint with undefined content to mark the file as deleted
    const key = { conversationId: this._currentConversationId, path: filePath };
    const lastKnownState = await this._getFileStateAtTimestamp(
      this._currentConversationId,
      filePath,
      Number.MAX_SAFE_INTEGER,
    );
    if (lastKnownState !== null) {
      await this.addCheckpoint(key, {
        sourceToolCallRequestId: createRequestId(),
        timestamp: Date.now(),
        document: new DiffViewDocument(filePath, lastKnownState, undefined, {}),
        conversationId: this._currentConversationId,
        editSource: EditEventSource.USER_EDIT,
      });
      this._logger.debug(`Marked file deleted: ${filePath.absPath}`);
    }
  };

  /**
   * Handles file move/rename events by updating the file path in all checkpoints.
   *
   * @param event - The file moved event
   */
  private _handleFileMoved = async (event: FileDidMoveEvent): Promise<void> => {
    const oldFilePath = QualifiedPathName.from(event.oldQualifiedPathName);
    const newFilePath = QualifiedPathName.from(event.newQualifiedPathName);

    if (
      oldFilePath === undefined ||
      newFilePath === undefined ||
      this._currentConversationId === undefined
    ) {
      return;
    }

    this._logger.debug(
      `File moved: ${oldFilePath.absPath} -> ${newFilePath.absPath}`,
    );

    // Get all checkpoints for the old file path
    const oldKey = {
      conversationId: this._currentConversationId,
      path: oldFilePath,
    };
    const checkpoints = await this.shardManager.getCheckpoints(oldKey);

    if (checkpoints.length === 0) {
      // File wasn't tracked, nothing to do
      return;
    }

    // Create new checkpoints with the updated file path
    const newKey = {
      conversationId: this._currentConversationId,
      path: newFilePath,
    };

    // Add each checkpoint with the new path
    for (const checkpoint of checkpoints) {
      const newCheckpoint = {
        ...checkpoint,
        document: new DiffViewDocument(
          newFilePath,
          checkpoint.document.originalCode,
          checkpoint.document.modifiedCode,
          {},
        ),
        editSource: checkpoint.editSource ?? EditEventSource.USER_EDIT,
      };

      await this.shardManager.addCheckpoint(newKey, newCheckpoint);
    }

    // Remove the old checkpoints
    await this.shardManager.removeCheckpoint(oldKey);
    this._notifyAgentEditListHasUpdates();
  };

  /**
   * Updates the latest checkpoint for a file and saves the changes to disk.
   * Only updates the latest checkpoint if it exists. If it does not, does nothing.
   *
   * @param filePath - The path of the file
   * @param newContent - The new content of the file
   * @param options - Options for updating the checkpoint, including saveToWorkspace and updateSource
   */
  public updateLatestCheckpoint = async (
    filePath: QualifiedPathName,
    newContent: string | undefined,
    options?: UpdateLatestCheckpointOptions,
  ): Promise<void> => {
    const conversationId = this._currentConversationId;
    if (conversationId === undefined) {
      this._logger.warn(
        "Cannot update latest checkpoint. No current conversation ID.",
      );
      return;
    }

    const key = { conversationId, path: filePath };
    const checkpoints = await this.shardManager.getCheckpoints(key);
    const latestCheckpoint = checkpoints.at(-1);
    if (latestCheckpoint === undefined) {
      return;
    }

    // Determine whether to create a new checkpoint or update the existing one.
    // We create a new checkpoint in two scenarios:
    // 1. Edit source mismatch: When the source of the current update differs from the latest checkpoint's source,
    //    we preserve the history by creating a separate checkpoint rather than merging changes.
    //    This maintains clear attribution of who made which changes (user vs agent vs other sources).
    // 2. Already communicated to agent: When the latest checkpoint has been explicitly sent to the agent
    //    (indicated by lastIncludedInRequestId being set), we must not overwrite it to preserve the
    //    agent's understanding of the file state at the time of that communication.
    const shouldCreateNewCheckpoint =
      (latestCheckpoint.editSource ?? EditEventSource.UNSPECIFIED) !==
        (options?.updateSource ?? EditEventSource.UNSPECIFIED) ||
      latestCheckpoint.lastIncludedInRequestId !== undefined;

    if (shouldCreateNewCheckpoint) {
      const updatedCheckpoint = {
        sourceToolCallRequestId: createRequestId(),
        timestamp: Date.now(),
        document: new DiffViewDocument(
          filePath,
          latestCheckpoint.document.modifiedCode,
          newContent,
          {},
        ),
        conversationId,
        editSource: options?.updateSource ?? EditEventSource.UNSPECIFIED,
      };

      // Create a new checkpoint with the new content
      await this.shardManager.addCheckpoint(key, updatedCheckpoint);
      this._notifyAgentEditListHasUpdates();
      return;
    } else {
      const updatedCheckpoint = {
        ...latestCheckpoint,
        document: new DiffViewDocument(
          filePath,
          latestCheckpoint.document.originalCode,
          newContent,
          {},
        ),
        editSource: options?.updateSource ?? EditEventSource.UNSPECIFIED,
      };
      await this.shardManager.updateCheckpoint(key, updatedCheckpoint);
      this._logger.debug(
        `Updated latest checkpoint for ${filePath.absPath} with ${newContent?.length ?? 0} bytes`,
      );
    }

    this._notifyAgentEditListHasUpdates();
    if (options?.saveToWorkspace ?? false) {
      if (newContent === undefined) {
        await getClientWorkspaces().deleteFile(filePath);
        this._logger.debug(`Deleted file: ${filePath.absPath}`);
      } else {
        await getClientWorkspaces().writeFile(filePath, newContent);
        this._logger.debug(`Wrote file: ${filePath.absPath}`);
      }
    }
  };

  /**
   * For each file tracked by the agent, check the last checkpoint to see
   * if it was modified by the user. Then, aggregate all of the user-modified edits
   * and return them as a single aggregate checkpoint.
   *
   * @param filePath
   * @param options
   * @returns
   */
  public getRecentUserEdits = async (): Promise<AggregateCheckpointInfo> => {
    await this.shardManager.initialize();
    const conversationId = this._currentConversationId;
    if (conversationId === undefined) {
      this._logger.debug("No current conversation ID");
      return {
        fromTimestamp: 0,
        toTimestamp: Infinity,
        conversationId: "",
        files: [],
      };
    }

    const shard = await this.shardManager.getShardById(
      this._shardFunction({ conversationId }),
    );
    if (shard === undefined) {
      this._logger.debug("No shard found for conversation");
      return {
        fromTimestamp: 0,
        toTimestamp: Infinity,
        conversationId,
        files: [],
      };
    }

    // Get latest checkpoint for all files
    const allFiles = shard.getAllTrackedFilePaths(conversationId);
    const allCheckpoints: HydratedCheckpoint[] = allFiles
      .map((file) => {
        const latestCheckpoint = shard.getLatestCheckpoint({
          conversationId,
          path: file,
        });
        return latestCheckpoint;
      })
      .filter((cp): cp is HydratedCheckpoint => cp !== undefined);
    const userModifiedCheckpoints = allCheckpoints.filter(
      (cp) => cp.editSource === EditEventSource.USER_EDIT,
    );

    if (userModifiedCheckpoints.length === 0) {
      this._logger.debug("No user-modified checkpoints found");
      return {
        fromTimestamp: 0,
        toTimestamp: Infinity,
        conversationId,
        files: [],
      };
    }

    this._logger.debug(
      `Found ${userModifiedCheckpoints.length} user-modified checkpoints`,
    );

    return {
      fromTimestamp: 0,
      toTimestamp: Infinity,
      conversationId,
      files: userModifiedCheckpoints.map((cp) => ({
        changesSummary: computeChangesSummary(cp.document),
        changeDocument: cp.document,
      })),
    };
  };

  /**
   * Gets all user-modified changes grouped by the request ID they were included in.
   * This method retrieves all user-modified changes and groups them by their
   * lastIncludedInRequestId field.
   *
   * @returns A map of request IDs to AggregateCheckpointInfo objects
   */
  public getAllUserModifiedChanges = async (): Promise<
    Map<string, AggregateCheckpointInfo>
  > => {
    await this.shardManager.initialize();
    const conversationId = this._currentConversationId;
    if (conversationId === undefined) {
      this._logger.debug("No current conversation ID");
      return new Map();
    }

    const shard = await this.shardManager.getShardById(
      this._shardFunction({ conversationId }),
    );
    if (shard === undefined) {
      this._logger.debug("No shard found for conversation");
      return new Map();
    }

    // Get all checkpoints for all files (not just latest)
    const allFiles = shard.getAllTrackedFilePaths(conversationId);
    const allCheckpoints: HydratedCheckpoint[] = [];

    for (const file of allFiles) {
      const key = { conversationId, path: file };
      const fileCheckpoints = await this.shardManager.getCheckpoints(key);
      allCheckpoints.push(...fileCheckpoints);
    }

    // Filter for user-modified checkpoints that have been included in requests
    const userModifiedCheckpoints = allCheckpoints.filter(
      (cp) =>
        cp.editSource === EditEventSource.USER_EDIT &&
        cp.lastIncludedInRequestId !== undefined,
    );

    // Group checkpoints by request ID
    const checkpointsByRequestId = new Map<string, HydratedCheckpoint[]>();
    for (const checkpoint of userModifiedCheckpoints) {
      const requestId = checkpoint.lastIncludedInRequestId!;
      if (!checkpointsByRequestId.has(requestId)) {
        checkpointsByRequestId.set(requestId, []);
      }
      checkpointsByRequestId.get(requestId)!.push(checkpoint);
    }

    // Convert to map of AggregateCheckpointInfo objects
    const result = new Map<string, AggregateCheckpointInfo>();
    for (const [requestId, checkpoints] of checkpointsByRequestId) {
      result.set(requestId, {
        fromTimestamp: Math.min(...checkpoints.map((cp) => cp.timestamp)),
        toTimestamp: Math.max(...checkpoints.map((cp) => cp.timestamp)),
        conversationId,
        files: checkpoints.map((cp) => ({
          changesSummary: computeChangesSummary(cp.document),
          changeDocument: cp.document,
        })),
      });
    }

    this._logger.debug(
      `Found ${userModifiedCheckpoints.length} user-modified checkpoints across ${result.size} requests`,
    );

    return result;
  };

  /**
   * Pulls all user-modified changes and marks them as included in the specified request.
   * This method is similar to getRecentUserEdits but also updates the lastIncludedInRequestId field.
   *
   * @param requestId - The ID of the request that is including these changes
   * @returns An AggregateCheckpointInfo containing user-modified changes
   */
  public pullAllUserModifiedChanges = async (
    requestId: string,
  ): Promise<AggregateCheckpointInfo> => {
    await this.shardManager.initialize();
    const conversationId = this._currentConversationId;
    if (conversationId === undefined) {
      this._logger.debug("No current conversation ID");
      return {
        fromTimestamp: 0,
        toTimestamp: Infinity,
        conversationId: "",
        files: [],
      };
    }

    const shard = await this.shardManager.getShardById(
      this._shardFunction({ conversationId }),
    );
    if (shard === undefined) {
      this._logger.debug("No shard found for conversation");
      return {
        fromTimestamp: 0,
        toTimestamp: Infinity,
        conversationId,
        files: [],
      };
    }

    // Get latest checkpoint for all files
    const allFiles = shard.getAllTrackedFilePaths(conversationId);
    const allCheckpoints: HydratedCheckpoint[] = allFiles
      .map((file) => {
        const latestCheckpoint = shard.getLatestCheckpoint({
          conversationId,
          path: file,
        });
        return latestCheckpoint;
      })
      .filter((cp): cp is HydratedCheckpoint => cp !== undefined);

    // Find user-modified checkpoints that haven't been seen by the agent
    const unseenUserModifiedCheckpoints = allCheckpoints.filter(
      (cp) =>
        cp.editSource === EditEventSource.USER_EDIT &&
        cp.lastIncludedInRequestId === undefined,
    );

    if (unseenUserModifiedCheckpoints.length === 0) {
      this._logger.debug("No unseen user-modified checkpoints found");
      return {
        fromTimestamp: 0,
        toTimestamp: Infinity,
        conversationId,
        files: [],
      };
    }

    this._logger.debug(
      `Found ${unseenUserModifiedCheckpoints.length} unseen user-modified checkpoints, marking as included in request ${requestId}`,
    );

    // Mark all retrieved checkpoints as included in the current request
    for (const checkpoint of unseenUserModifiedCheckpoints) {
      const updatedCheckpoint = {
        ...checkpoint,
        lastIncludedInRequestId: requestId,
      };

      const key = {
        conversationId,
        path: checkpoint.document.filePath,
      };

      await this.shardManager.updateCheckpoint(key, updatedCheckpoint);
    }

    return {
      fromTimestamp: 0,
      toTimestamp: Infinity,
      conversationId,
      files: unseenUserModifiedCheckpoints.map((cp) => ({
        changesSummary: computeChangesSummary(cp.document),
        changeDocument: cp.document,
      })),
    };
  };

  /**
   * Gets an aggregate checkpoint for a specific file.
   *
   * This method retrieves the state of a file at two points in time (specified by
   * minTimestamp and maxTimestamp in options) and creates a DiffViewDocument
   * representing the changes between those states. It's used to show the changes
   * made to a file during a specific time period.
   *
   * The method works with the underlying sharding system by:
   * 1. Using the current conversation ID to identify the relevant shard
   * 2. Retrieving file states at both timestamps using _getFileStateAtTimestamp
   * 3. Creating a DiffViewDocument with the before/after states
   * 4. Computing change summaries for the file
   *
   * This is particularly useful for showing changes made to a specific file
   * during an agent conversation or for reverting a file to a previous state.
   *
   * @param filePath - The path of the file to get checkpoint for
   * @param options - Options specifying the time range (minTimestamp and maxTimestamp)
   * @returns An AggregateCheckpointInfo containing the file changes in the specified range
   */
  public getAggregateCheckpointForFile = async (
    filePath: QualifiedPathName,
    options: GetAggregateCheckpointsOptions,
  ): Promise<AggregateCheckpointInfo> => {
    await this.shardManager.initialize();
    const conversationId = this._currentConversationId;
    if (conversationId === undefined || conversationId === "__NEW_AGENT__") {
      return {
        fromTimestamp: 0,
        toTimestamp: Infinity,
        conversationId: "",
        files: [],
      };
    }

    const minTimestamp = options.minTimestamp ?? 0;
    const maxTimestamp = options.maxTimestamp ?? Infinity;

    const original = await this._getFileStateAtTimestamp(
      conversationId,
      filePath,
      minTimestamp,
    );
    const modified = await this._getFileStateAtTimestamp(
      conversationId,
      filePath,
      maxTimestamp,
    );
    if (original === null || modified === null) {
      this._logger.warn(
        `Cannot get aggregate checkpoint for ${filePath.absPath}. No checkpoints found.`,
      );
      return {
        fromTimestamp: minTimestamp,
        toTimestamp: maxTimestamp,
        conversationId,
        files: [],
      };
    }
    const doc = new DiffViewDocument(filePath, original, modified, {});
    return {
      fromTimestamp: minTimestamp,
      toTimestamp: maxTimestamp,
      conversationId,
      files: [
        {
          changesSummary: computeChangesSummary(doc),
          changeDocument: doc,
        },
      ],
    };
  };

  /**
   * Gets an aggregate checkpoint for a specific request ID
   *
   * @param conversationId - The conversation ID
   * @param requestId - The request ID
   * @returns The aggregate checkpoint
   */
  public getCheckpointByRequestId = async (
    requestId: string,
  ): Promise<AggregateCheckpointInfo | undefined> => {
    await this.shardManager.initialize();
    const conversationId = this._currentConversationId;
    if (conversationId === undefined || conversationId === "__NEW_AGENT__") {
      return undefined;
    }

    const id = this._shardFunction({ conversationId });
    const shard = await this.shardManager.getShardById(id);
    const checkpoint = shard.getCheckpointBySourceId(requestId);
    if (checkpoint === undefined) {
      return undefined;
    }
    return {
      fromTimestamp: checkpoint.timestamp,
      toTimestamp: checkpoint.timestamp,
      conversationId,
      files: [
        {
          changesSummary: computeChangesSummary(checkpoint.document),
          changeDocument: checkpoint.document,
        },
      ],
    };
  };

  /**
   * Gets an aggregate checkpoint for all tracked files in the current conversation.
   *
   * This method:
   * 1. Retrieves all files tracked in the current conversation
   * 2. For each file, gets its state at both the min and max timestamps
   * 3. Creates a DiffViewDocument for each file showing changes between those states
   * 4. Computes change summaries for each file
   * 5. Returns a comprehensive AggregateCheckpointInfo containing all file changes
   *
   * This is particularly useful for showing all changes made during a specific
   * time period or for reverting multiple files to a previous state.
   *
   * @param options - Options specifying the time range for the checkpoint
   *                  (minTimestamp and maxTimestamp)
   * @returns An AggregateCheckpointInfo containing all file changes in the specified range
   */
  public getAggregateCheckpoint = async (
    options: GetAggregateCheckpointsOptions,
  ): Promise<AggregateCheckpointInfo> => {
    await this.shardManager.initialize();
    const minTimestamp = options.minTimestamp ?? 0;
    const maxTimestamp = options.maxTimestamp ?? Infinity;

    // For each tracked file, get the state before/after. If there is no before/after, use min and max times.
    const currentConversationId = this._currentConversationId;
    if (
      currentConversationId === undefined ||
      currentConversationId === "__NEW_AGENT__"
    ) {
      return {
        fromTimestamp: 0,
        toTimestamp: Infinity,
        conversationId: "",
        files: [],
      };
    }

    const trackedFiles = await this._getAllTrackedFiles(currentConversationId);
    const fileDocuments = await Promise.all(
      trackedFiles.map(async (filePath) => {
        const original = await this._getFileStateAtTimestamp(
          currentConversationId,
          filePath,
          minTimestamp,
        );
        const modified = await this._getFileStateAtTimestamp(
          currentConversationId,
          filePath,
          maxTimestamp,
        );
        if (original === null || modified === null) {
          this._logger.warn(
            `Cannot get aggregate checkpoint for ${filePath.absPath}. No checkpoints found.`,
          );
          return undefined;
        }
        return new DiffViewDocument(filePath, original, modified, {});
      }),
    );

    return {
      fromTimestamp: minTimestamp,
      toTimestamp: maxTimestamp,
      conversationId: currentConversationId,
      files: fileDocuments
        .filter(
          (doc: DiffViewDocument | undefined): doc is DiffViewDocument =>
            doc !== undefined,
        )
        .map((doc) => ({
          changesSummary: computeChangesSummary(doc),
          changeDocument: doc,
        })),
    };
  };

  /**
   * Clears all checkpoints for a conversation.
   *
   * @param conversationId - The conversation ID
   */
  public clearConversationCheckpoints = async (
    conversationId: string,
  ): Promise<void> => {
    await this.shardManager.initialize();
    await this.shardManager.clearShard(this._shardFunction({ conversationId }));
  };

  /**
   * Reverts a specific file to its state at the specified timestamp
   * by creating a new checkpoint.
   *
   * @param qualifiedPathName - The qualified path name of the file to revert
   * @param timestamp - The timestamp to revert to
   */
  public revertDocumentToTimestamp = async (
    qualifiedPathName: QualifiedPathName,
    timestamp: number,
  ): Promise<void> => {
    await this.shardManager.initialize();
    const currentConversationId = this._currentConversationId;
    if (currentConversationId === undefined) {
      return;
    }

    const original = await this._getFileStateAtTimestamp(
      currentConversationId,
      qualifiedPathName,
      timestamp,
    );
    const latest = await this._getFileStateAtTimestamp(
      currentConversationId,
      qualifiedPathName,
      Number.MAX_SAFE_INTEGER,
    );
    if (original === null || latest === null) {
      return;
    }

    await this.addCheckpoint(
      {
        conversationId: currentConversationId,
        path: qualifiedPathName,
      },
      {
        sourceToolCallRequestId: createRequestId(),
        timestamp: Date.now(),
        document: new DiffViewDocument(
          qualifiedPathName,
          latest ?? "",
          original,
          {},
        ),
        conversationId: currentConversationId,
        editSource: EditEventSource.CHECKPOINT_REVERT,
      },
    );
  };

  /**
   * Reverts all tracked files to their state at the specified timestamp
   * by creating new checkpoints for each file.
   *
   * @param timestamp - The timestamp to revert to
   */
  public revertToTimestamp = async (timestamp: number): Promise<void> => {
    await this.shardManager.initialize();
    const currentConversationId = this._currentConversationId;
    if (currentConversationId === undefined) {
      return;
    }

    const trackedFiles = await this.getAggregateCheckpoint({
      minTimestamp: timestamp,
      maxTimestamp: undefined,
    });
    this._logger.debug(
      `Reverting to timestamp: ${timestamp}. Includes files ${trackedFiles.files
        .map((file) => file.changeDocument.filePath.absPath)
        .join(", ")}`,
    );
    await Promise.all(
      trackedFiles.files.map(async (file) => {
        await this.revertDocumentToTimestamp(
          file.changeDocument.filePath,
          timestamp,
        );
      }),
    );
  };

  /**
   * Removes the last (most recent) checkpoint for a specific document.
   * This is useful for undoing the most recent change to a file.
   *
   * @param filePath - The path of the file to remove the last checkpoint for
   * @param expectedSourceToolCallRequestId - The expected sourceToolCallRequestId of the last checkpoint for safety verification
   * @returns true if a checkpoint was removed, false if no checkpoints exist or sourceToolCallRequestId doesn't match
   */
  public removeDocumentLastCheckpoint = async (
    filePath: QualifiedPathName,
    expectedSourceToolCallRequestId: string,
  ): Promise<boolean> => {
    await this.shardManager.initialize();
    const currentConversationId = this._currentConversationId;
    if (currentConversationId === undefined) {
      this._logger.warn(
        "Cannot remove last checkpoint. No current conversation ID.",
      );
      return false;
    }

    const key = { conversationId: currentConversationId, path: filePath };
    const checkpoints = await this.shardManager.getCheckpoints(key);

    if (checkpoints.length === 0) {
      this._logger.debug(
        `No checkpoints found for ${filePath.absPath}. Nothing to remove.`,
      );
      return false;
    }

    // Get the latest checkpoint to determine the removal criteria
    const latestCheckpoint = checkpoints[checkpoints.length - 1];

    // Verify the sourceToolCallRequestId matches for safety
    if (
      latestCheckpoint.sourceToolCallRequestId !==
      expectedSourceToolCallRequestId
    ) {
      this._logger.warn(
        `SourceToolCallRequestId mismatch for ${filePath.absPath}. Expected: ${expectedSourceToolCallRequestId}, Found: ${latestCheckpoint.sourceToolCallRequestId}. Aborting removal.`,
      );
      return false;
    }

    // Create a Set with the sourceToolCallRequestId to remove
    const sourceToolCallRequestIds = new Set<string>([
      expectedSourceToolCallRequestId,
    ]);

    // Remove the checkpoint by using its sourceToolCallRequestId
    const removed = await this.shardManager.removeCheckpoint(key, {
      sourceToolCallRequestIds,
    });

    if (removed) {
      this._logger.debug(
        `Removed checkpoint for ${filePath.absPath} (sourceToolCallRequestId: ${expectedSourceToolCallRequestId})`,
      );
      // Update file contents in the workspace to reflect the removal of the checkpoint
      if (latestCheckpoint.document.originalCode === undefined) {
        await getClientWorkspaces().deleteFile(filePath);
        this._logger.debug(`Deleted file: ${filePath.absPath}`);
      } else {
        await getClientWorkspaces().writeFile(
          filePath,
          latestCheckpoint.document.originalCode,
        );
        this._logger.debug(`Wrote file: ${filePath.absPath}`);
      }
      this._notifyAgentEditListHasUpdates();
    } else {
      this._logger.warn(
        `Failed to remove checkpoint for ${filePath.absPath} (sourceToolCallRequestId: ${expectedSourceToolCallRequestId})`,
      );
    }

    return removed;
  };
}

/**
 * Computes a summary of changes between two versions of a file.
 *
 * This utility function:
 * 1. Uses the diff-lines algorithm to identify line-level changes
 * 2. Calculates aggregate statistics (total added/removed lines)
 * 3. Preserves the detailed change information for rendering diffs
 *
 * The resulting ChatAgentFileChanges object provides both high-level statistics
 * for UI summaries and detailed change information for rendering diffs.
 *
 * @param doc - The DiffViewDocument containing original and modified code versions
 * @returns A ChatAgentFileChanges object with change statistics and detailed changes
 */
function computeChangesSummary(doc: DiffViewDocument): ChatAgentFileChanges {
  const changes = diffLines(doc.originalCode ?? "", doc.modifiedCode ?? "");

  const totalAddedLines = changes
    .filter((change) => change.added)
    .reduce((total, change) => total + (change.count ?? 0), 0);

  const totalRemovedLines = changes
    .filter((change) => change.removed)
    .reduce((total, change) => total + (change.count ?? 0), 0);

  return {
    totalAddedLines,
    totalRemovedLines,
    changes,
  };
}
