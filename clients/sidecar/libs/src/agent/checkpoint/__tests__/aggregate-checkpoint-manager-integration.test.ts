import { QualifiedPathName } from "@augment-internal/sidecar-libs/src/workspace/qualified-path-name";
import { AgentShardStorage } from "../../agent-shard-storage";
import { AggregateCheckpointManager } from "../aggregate-checkpoint-manager";
import { DocumentCheckpoint } from "../../agent-edit-types";
import { DiffViewDocument } from "@augment-internal/sidecar-libs/src/diff-view/document";
import {
  IPluginFileStore,
  setLibraryPluginFileStore,
} from "@augment-internal/sidecar-libs/src/client-interfaces/plugin-file-store";
import {
  IClientWorkspaces,
  setLibraryClientWorkspaces,
} from "@augment-internal/sidecar-libs/src/client-interfaces/client-workspaces";

/**
 * These tests use the actual implementations of classes, rather than
 * the heavily mocker unit tests ins aggregate-checkpoint-manager.test.ts.
 */
describe("Complete Tests", () => {
  // Test data
  const testConversationId = "test-conversation";
  const testPath = QualifiedPathName.from({
    rootPath: "/test",
    relPath: "file.ts",
  });

  // Helper function to create a test checkpoint
  const createTestCheckpoint = (
    original: string,
    modified: string,
    path: QualifiedPathName = testPath,
    conversationId: string = testConversationId,
    requestId: string = "test-request-id",
    timestamp: number = Date.now(),
  ): DocumentCheckpoint => ({
    sourceToolCallRequestId: requestId,
    timestamp,
    document: new DiffViewDocument(path, original, modified, {}),
    conversationId,
  });

  let mockPluginFileStore: jest.Mocked<IPluginFileStore>;
  let mockClientWorkspaces: jest.Mocked<IClientWorkspaces>;

  beforeEach(() => {
    mockPluginFileStore = {
      saveAsset: jest.fn(),
      loadAsset: jest.fn(),
      deleteAsset: jest.fn(),
      listAssets: jest.fn(),
      getAssetPath: jest
        .fn()
        .mockImplementation((path: string) =>
          Promise.resolve(`/mock/path/${path}`),
        ),
    };
    setLibraryPluginFileStore(mockPluginFileStore);

    mockClientWorkspaces = {
      getCwd: jest.fn().mockResolvedValue("/mock/cwd"),
      getWorkspaceRoot: jest.fn().mockResolvedValue("/mock/workspace"),
      readFile: jest.fn().mockResolvedValue({ contents: "mock content" }),
      writeFile: jest.fn().mockResolvedValue(undefined),
      deleteFile: jest.fn().mockResolvedValue(undefined),
      getQualifiedPathName: jest.fn().mockResolvedValue({
        rootPath: "/mock/cwd",
        relPath: "example/path.txt",
      }),
      findFiles: jest.fn().mockResolvedValue([]),
      listDirectory: jest.fn().mockResolvedValue({ entries: [] }),
      getPathInfo: jest.fn().mockResolvedValue({ filepath: undefined }),
      getRipgrepPath: jest.fn().mockResolvedValue("/usr/bin/rg"),
      onFileDeleted: jest.fn().mockReturnValue({
        dispose: jest.fn(),
      }),
      onFileDidMove: jest.fn().mockReturnValue({
        dispose: jest.fn(),
      }),
    };
    setLibraryClientWorkspaces(mockClientWorkspaces);
  });

  it("checkpoint for a untracked file should have a sentinal checkpoint", async () => {
    const manager = new AggregateCheckpointManager(
      new AgentShardStorage(),
      () => undefined,
      () => ({ dispose: () => {} }),
      () => ({ dispose: () => {} }),
      () => ({ dispose: () => {} }),
    );
    manager.setCurrentConversation(testConversationId);

    mockClientWorkspaces.readFile.mockResolvedValue({
      contents: "original",
    });

    // Add checkpoint for an untracked file
    await manager.addCheckpoint(
      { conversationId: testConversationId, path: testPath },
      createTestCheckpoint("original", "modified"),
    );

    // check the aggregate checkpoint is what we want
    const gotAggCheckpoint = await manager.getAggregateCheckpoint({});
    expect(gotAggCheckpoint.files.length).toBe(1);
    expect(gotAggCheckpoint.files[0].changeDocument.originalCode).toBe(
      "original",
    );
    expect(gotAggCheckpoint.files[0].changeDocument.modifiedCode).toBe(
      "modified",
    );

    // Check the shard manager has the checkpoints we expect
    const gotShardCheckpoints = await manager.shardManager.getCheckpoints({
      conversationId: testConversationId,
      path: testPath,
    });
    // When adding a checkpoint via the aggregate checkpoint, ensure
    // that the sentinal checkpoint is added with timestamp 0.
    expect(gotShardCheckpoints.length).toBe(2);
    expect(gotShardCheckpoints[0].document.originalCode).toBe("original");
    expect(gotShardCheckpoints[0].document.modifiedCode).toBe("original");
    // This timestamp is the sentinal value and is zero to ensure its added
    // BEFORE the checkpoint added by the aggregate checkpoint manager.
    expect(gotShardCheckpoints[0].timestamp).toEqual(0);

    expect(gotShardCheckpoints[1].document.originalCode).toBe("original");
    expect(gotShardCheckpoints[1].document.modifiedCode).toBe("modified");
    expect(gotShardCheckpoints[1].timestamp).toBeGreaterThan(0);
  });
});
