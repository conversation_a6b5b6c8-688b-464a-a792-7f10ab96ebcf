import { MemorySnapshotManager } from "../memory-snapshot-manager";

describe("MemorySnapshotManager", () => {
  let manager: MemorySnapshotManager;
  let mockGetMemoriesContent: jest.Mock;

  beforeEach(() => {
    jest.useFakeTimers();
    mockGetMemoriesContent = jest.fn();
    manager = new MemorySnapshotManager(mockGetMemoriesContent);
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  it("should create a snapshot on first request", async () => {
    const mockMemories = "Test memories content";
    mockGetMemoriesContent.mockResolvedValue(mockMemories);

    const result = await manager.getMemorySnapshot("thread1");

    expect(result).toBe(mockMemories);
    expect(mockGetMemoriesContent).toHaveBeenCalledTimes(1);
  });

  it("should reuse snapshot for same thread", async () => {
    const mockMemories = "Test memories content";
    mockGetMemoriesContent.mockResolvedValue(mockMemories);

    // First call
    await manager.getMemorySnapshot("thread1");
    // Second call to same thread
    const result = await manager.getMemorySnapshot("thread1");

    expect(result).toBe(mockMemories);
    expect(mockGetMemoriesContent).toHaveBeenCalledTimes(1); // Only called once
  });

  it("should create new snapshot for different thread", async () => {
    const mockMemories1 = "Test memories content 1";
    const mockMemories2 = "Test memories content 2";
    mockGetMemoriesContent
      .mockResolvedValueOnce(mockMemories1)
      .mockResolvedValueOnce(mockMemories2);

    // First thread
    await manager.getMemorySnapshot("thread1");
    // Different thread
    const result = await manager.getMemorySnapshot("thread2");

    expect(result).toBe(mockMemories2);
    expect(mockGetMemoriesContent).toHaveBeenCalledTimes(2);
  });

  it("should update snapshot after inactivity timeout", async () => {
    const mockMemories1 = "Test memories content 1";
    const mockMemories2 = "Test memories content 2";
    mockGetMemoriesContent
      .mockResolvedValueOnce(mockMemories1)
      .mockResolvedValueOnce(mockMemories2);

    // First call
    await manager.getMemorySnapshot("thread1");

    // Advance time past inactivity threshold (5 minutes)
    jest.advanceTimersByTime(5 * 60 * 1000 + 1000);

    // Second call after timeout
    const result = await manager.getMemorySnapshot("thread1");

    expect(result).toBe(mockMemories2);
    expect(mockGetMemoriesContent).toHaveBeenCalledTimes(2);
  });

  it("should reset inactivity timer on each request", async () => {
    const mockMemories = "Test memories content";
    mockGetMemoriesContent.mockResolvedValue(mockMemories);

    // First call
    await manager.getMemorySnapshot("thread1");

    // Advance time but not past threshold
    jest.advanceTimersByTime(4 * 60 * 1000);

    // Second call before timeout
    await manager.getMemorySnapshot("thread1");

    // Advance time again but not past threshold from second call
    jest.advanceTimersByTime(4 * 60 * 1000);

    // Third call
    const result = await manager.getMemorySnapshot("thread1");

    expect(result).toBe(mockMemories);
    expect(mockGetMemoriesContent).toHaveBeenCalledTimes(1); // Still only called once
  });

  it("should handle errors gracefully", async () => {
    mockGetMemoriesContent.mockRejectedValue(
      new Error("Failed to read memories"),
    );

    const result = await manager.getMemorySnapshot("thread1");

    expect(result).toBeUndefined();
    expect(mockGetMemoriesContent).toHaveBeenCalledTimes(1);
  });

  it("should force update snapshot", async () => {
    const mockMemories1 = "Test memories content 1";
    const mockMemories2 = "Test memories content 2";
    mockGetMemoriesContent
      .mockResolvedValueOnce(mockMemories1)
      .mockResolvedValueOnce(mockMemories2);

    // First call
    await manager.getMemorySnapshot("thread1");

    // Force update
    await manager.forceUpdateSnapshot();

    // Get snapshot again
    const result = await manager.getMemorySnapshot("thread1");

    expect(result).toBe(mockMemories2);
    expect(mockGetMemoriesContent).toHaveBeenCalledTimes(2);
  });
});
