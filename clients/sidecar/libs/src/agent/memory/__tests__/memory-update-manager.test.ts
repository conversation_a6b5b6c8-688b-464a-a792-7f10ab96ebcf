import { MemoryUpdateManager } from "../memory-update-manager";

describe("MemoryUpdateManager", () => {
  let memoryUpdateManager: MemoryUpdateManager;
  let callbackMock: jest.Mock;

  beforeEach(() => {
    memoryUpdateManager = new MemoryUpdateManager();
    callbackMock = jest.fn();
  });

  afterEach(() => {
    memoryUpdateManager.dispose();
    jest.clearAllMocks();
  });

  test("registers and calls callbacks when notifyMemoryHasUpdates is called", () => {
    // Register callback
    const disposable = memoryUpdateManager.onMemoryHasUpdates(callbackMock);

    // Notify of updates
    memoryUpdateManager.notifyMemoryHasUpdates();

    // Verify callback was called
    expect(callbackMock).toHaveBeenCalledTimes(1);

    // Unregister callback
    disposable.dispose();

    // Notify again
    memoryUpdateManager.notifyMemoryHasUpdates();

    // Verify callback wasn't called again
    expect(callbackMock).toHaveBeenCalledTimes(1);
  });
});
