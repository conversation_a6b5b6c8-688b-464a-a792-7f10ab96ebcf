import { DisposableService } from "../../lifecycle/disposable-service";

/**
 * MemorySnapshotManager manages in-memory snapshots of agent memories.
 * It creates a snapshot when a user sends a message in a conversation and maintains
 * that snapshot until 5 minutes of inactivity or conversation switch.
 */
export class MemorySnapshotManager extends DisposableService {
  private _currentSnapshot: string | undefined;
  private _currentConversationId: string | undefined;
  private _lastActivityTime: number = 0;
  private _inactivityThresholdMs = 5 * 60 * 1000; // 5 minutes

  constructor(private _getMemoriesContent: () => Promise<string | undefined>) {
    super();
  }

  /**
   * Get the current memory snapshot or create one if needed.
   * @param conversationId The current conversation ID
   * @returns The memory snapshot content
   */
  public async getMemorySnapshot(
    conversationId: string,
  ): Promise<string | undefined> {
    const now = Date.now();

    // Check if we need to update the snapshot
    if (this._shouldUpdateSnapshot(conversationId, now)) {
      await this._updateSnapshot(conversationId);
    }

    // Update activity time
    this._lastActivityTime = now;

    return this._currentSnapshot;
  }

  /**
   * Force an update of the memory snapshot.
   * This is used for manual edits to the memories file.
   */
  public async forceUpdateSnapshot(): Promise<void> {
    await this._updateSnapshot(this._currentConversationId);
  }

  private _shouldUpdateSnapshot(conversationId: string, now: number): boolean {
    // Update if no snapshot exists
    if (!this._currentSnapshot) {
      return true;
    }

    // Update if conversation has changed
    if (conversationId !== this._currentConversationId) {
      return true;
    }

    // Update if inactivity threshold has been exceeded
    if (now - this._lastActivityTime > this._inactivityThresholdMs) {
      return true;
    }

    return false;
  }

  private async _updateSnapshot(conversationId?: string): Promise<void> {
    try {
      // Read the current memories file
      const content = await this._getMemoriesContent();
      this._currentSnapshot = content;
      this._currentConversationId = conversationId;
    } catch (error) {
      this._currentSnapshot = undefined;
    }
  }

  public override dispose = (): void => {
    super.dispose();
  };
}
