import { DisposableService } from "../../lifecycle/disposable-service";
import type { ISidecarDisposable } from "../../lifecycle/disposable-types";
import throttle from "lodash/throttle";

/**
 * MemoryUpdateManager provides a way to notify listeners when agent memories are updated.
 * This is used to trigger animations in the UI when memories are written.
 * It can also monitor specific memory files for changes on disk.
 */
export class MemoryUpdateManager extends DisposableService {
  // Throttle notifications to once per second
  // eslint-disable-next-line @typescript-eslint/naming-convention
  private static THROTTLE_DELAY_MS = 500; // 0.5 seconds
  private _memoryHasUpdatesCallbacks = new Set<() => void>();
  private _throttledNotifyMemoryHasUpdates: () => void;

  constructor() {
    super();
    this._throttledNotifyMemoryHasUpdates = throttle(
      this._notifyMemoryHasUpdatesImmediate,
      MemoryUpdateManager.THROTTLE_DELAY_MS,
      { trailing: true },
    );
  }

  /**
   * Register a callback to be called when memories are updated.
   * @param cb The callback to register
   * @returns A disposable that can be used to unregister the callback
   */
  public onMemoryHasUpdates = (cb: () => void): ISidecarDisposable => {
    this._memoryHasUpdatesCallbacks.add(cb);
    return {
      dispose: () => {
        this._memoryHasUpdatesCallbacks.delete(cb);
      },
    };
  };

  /**
   * Notify all registered callbacks that memories have been updated.
   * This method is throttled to prevent excessive notifications.
   */
  public notifyMemoryHasUpdates = (): void => {
    this._throttledNotifyMemoryHasUpdates();
  };

  /**
   * Immediate implementation of notifying callbacks.
   * This is wrapped by the throttled version.
   */
  private _notifyMemoryHasUpdatesImmediate = (): void => {
    this._memoryHasUpdatesCallbacks.forEach((cb) => cb());
  };
}
