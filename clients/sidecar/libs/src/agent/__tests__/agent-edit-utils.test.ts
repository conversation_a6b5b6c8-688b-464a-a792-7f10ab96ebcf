import { DiffViewDocument } from "../../diff-view/document";
import { AugmentLogger } from "../../logging";
import { QualifiedPathName } from "../../workspace/qualified-path-name";
import { DocumentCheckpoint } from "../agent-edit-types";
import {
  areSameFileCheckpoints,
  canNormalizeCheckpointRange,
  computeCheckpointChanges,
  deserializeCheckpoint,
  getCheckpointByIndex,
  isCheckpointInTimeRange,
  normalizeCheckpointRange,
  serializeCheckpoint,
  serializeCheckpointsByPath,
} from "../agent-edit-utils";

describe("agent-edit-utils", () => {
  const mockLogger = {
    debug: jest.fn(),
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
  } as unknown as AugmentLogger;

  const createMockDocument = (
    rootPath: string = "/root",
    relPath: string = "test.ts",
    originalCode: string = "",
    modifiedCode: string = "",
  ): DiffViewDocument => {
    return new DiffViewDocument(
      QualifiedPathName.from({ rootPath, relPath }),
      originalCode,
      modifiedCode,
      { logger: mockLogger },
    );
  };

  const createMockCheckpoint = (
    id: string = "test-id",
    timestamp: number = 123,
    rootPath: string = "/root",
    relPath: string = "test.ts",
    originalCode: string = "some original code",
    modifiedCode: string = "some modified code",
  ): DocumentCheckpoint => {
    return {
      sourceToolCallRequestId: id,
      timestamp,
      conversationId: "test-conversation",
      document: createMockDocument(
        rootPath,
        relPath,
        originalCode,
        modifiedCode,
      ),
    };
  };

  // Helper function to filter checkpoints by timestamp using isCheckpointInTimeRange
  const filterCheckpointsByTimestamp = (
    checkpoints: DocumentCheckpoint[],
    minTimestamp?: number,
    maxTimestamp?: number,
  ): DocumentCheckpoint[] => {
    return checkpoints.filter((checkpoint) =>
      isCheckpointInTimeRange(checkpoint, minTimestamp, maxTimestamp),
    );
  };

  describe("canNormalizeCheckpointRange", () => {
    it("should handle basic ranges", () => {
      expect(canNormalizeCheckpointRange(5, 0, 4)).toBe(true);
      expect(canNormalizeCheckpointRange(5, 4, 2)).toBe(true); // Changed to true
      expect(canNormalizeCheckpointRange(5, -1, 4)).toBe(true);
      expect(canNormalizeCheckpointRange(5, 0, 5)).toBe(true);
    });

    it("should handle undefined bounds", () => {
      expect(canNormalizeCheckpointRange(5, undefined, undefined)).toBe(true);
      expect(canNormalizeCheckpointRange(5, 2, undefined)).toBe(true);
      expect(canNormalizeCheckpointRange(5, undefined, 3)).toBe(true);
    });

    it("should handle both out of bounds cases", () => {
      expect(canNormalizeCheckpointRange(5, 6, 7)).toBe(false); // normalizes to [4, 4]
      expect(canNormalizeCheckpointRange(5, -2, -1)).toBe(false); // normalizes to [0, 0]
    });

    it("should handle reversed indices", () => {
      expect(canNormalizeCheckpointRange(5, 3, 2)).toBe(true);
      expect(canNormalizeCheckpointRange(5, 4, 0)).toBe(true);
    });

    it("should handle reversed and out-of-bounds indices", () => {
      expect(canNormalizeCheckpointRange(5, 5, 4)).toBe(true);
      expect(canNormalizeCheckpointRange(5, 3, -1)).toBe(true);
      expect(canNormalizeCheckpointRange(5, 0, -1)).toBe(true);
    });
  });

  describe("normalizeCheckpointRange", () => {
    it("should normalize basic ranges", () => {
      expect(normalizeCheckpointRange(5, 0, 4)).toEqual([0, 4]);
      expect(normalizeCheckpointRange(5, 4, 2)).toEqual([4, 2]); // Reversed order is ok
      expect(normalizeCheckpointRange(5, -1, 4)).toEqual([0, 4]);
      expect(normalizeCheckpointRange(5, 0, 5)).toEqual([0, 4]);
    });

    it("should handle undefined bounds", () => {
      expect(normalizeCheckpointRange(5, undefined, undefined)).toEqual([0, 4]);
      expect(normalizeCheckpointRange(5, 2, undefined)).toEqual([2, 4]);
      expect(normalizeCheckpointRange(5, undefined, 3)).toEqual([0, 3]);
    });

    it("should handle one out of bounds", () => {
      expect(normalizeCheckpointRange(5, 4, 6)).toEqual([4, 4]);
      expect(normalizeCheckpointRange(5, 2, 6)).toEqual([2, 4]);
    });

    it("should handle out of bounds cases", () => {
      expect(normalizeCheckpointRange(5, 6, 7)).toEqual(undefined);
      expect(normalizeCheckpointRange(5, -2, -1)).toEqual(undefined);
      expect(normalizeCheckpointRange(5, -1, 6)).toEqual(undefined);
    });

    it("should handle reversed indices", () => {
      expect(normalizeCheckpointRange(5, 3, 2)).toEqual([3, 2]); // Reversed order is ok
      expect(normalizeCheckpointRange(5, 4, 0)).toEqual([4, 0]); // Reversed order is ok
    });

    it("should handle reversed and out-of-bounds indices", () => {
      expect(normalizeCheckpointRange(5, 5, 4)).toEqual([4, 4]);
      expect(normalizeCheckpointRange(5, 3, -1)).toEqual([3, 0]);
      expect(normalizeCheckpointRange(5, 0, -1)).toEqual([0, 0]);
    });
  });

  describe("filterCheckpointsByTimestamp", () => {
    const checkpoints = [
      createMockCheckpoint("test", 100, "/root", "test.ts", "", ""),
      createMockCheckpoint("test", 200, "/root", "test.ts", "", ""),
      createMockCheckpoint("test", 300, "/root", "test.ts", "", ""),
    ];

    it("should filter by min timestamp", () => {
      expect(filterCheckpointsByTimestamp(checkpoints, 150)).toHaveLength(2);
    });

    it("should filter by max timestamp", () => {
      expect(
        filterCheckpointsByTimestamp(checkpoints, undefined, 250),
      ).toHaveLength(2);
    });

    it("should filter by timestamp range", () => {
      expect(filterCheckpointsByTimestamp(checkpoints, 150, 250)).toHaveLength(
        1,
      );
    });

    it("should handle undefined timestamps", () => {
      expect(filterCheckpointsByTimestamp(checkpoints)).toEqual(checkpoints);
      expect(
        filterCheckpointsByTimestamp(checkpoints, undefined, undefined),
      ).toEqual(checkpoints);
    });

    it("should handle empty checkpoint array", () => {
      expect(filterCheckpointsByTimestamp([], 100, 200)).toHaveLength(0);
    });

    it("should handle exact timestamp matches", () => {
      expect(filterCheckpointsByTimestamp(checkpoints, 200, 200)).toHaveLength(
        1,
      );
    });
  });

  describe("getCheckpointByIndex", () => {
    const checkpoint = createMockCheckpoint();
    const sourceIds = ["id1", "id2"];
    const checkpointMap = new Map([["id1", checkpoint]]);

    it("should get checkpoint by valid index", () => {
      expect(getCheckpointByIndex(sourceIds, checkpointMap, 0)).toBe(
        checkpoint,
      );
    });

    it("should return undefined for invalid index", () => {
      expect(getCheckpointByIndex(sourceIds, checkpointMap, 2)).toBeUndefined();
    });

    it("should return undefined for non-existent checkpoint", () => {
      expect(getCheckpointByIndex(sourceIds, checkpointMap, 1)).toBeUndefined();
    });

    it("should handle empty source IDs array", () => {
      expect(getCheckpointByIndex([], checkpointMap, 0)).toBeUndefined();
    });
  });

  describe("checkpoint serialization", () => {
    const checkpoint = createMockCheckpoint();

    it("should serialize and deserialize checkpoint correctly", () => {
      const serialized = serializeCheckpoint(checkpoint);
      const deserialized = deserializeCheckpoint(
        serialized,
        () => false,
        mockLogger,
      );

      expect(deserialized.sourceToolCallRequestId).toBe(
        checkpoint.sourceToolCallRequestId,
      );
      expect(deserialized.timestamp).toBe(checkpoint.timestamp);
      expect(deserialized.document.filePath).toEqual(
        checkpoint.document.filePath,
      );
      expect(deserialized.document.originalCode).toBe(
        checkpoint.document.originalCode,
      );
      expect(deserialized.document.modifiedCode).toBe(
        checkpoint.document.modifiedCode,
      );
    });

    it("should serialize checkpoints by path", () => {
      const checkpointIds = new Map([["test.ts", ["id1"]]]);
      const sourceToCheckpoint = new Map([["id1", checkpoint]]);

      const serialized = serializeCheckpointsByPath(
        checkpointIds,
        sourceToCheckpoint,
      );
      expect(serialized.checkpointsByPath["test.ts"]).toBeDefined();
      expect(serialized.checkpointsByPath["test.ts"]).toHaveLength(1);
    });

    it("should handle empty checkpoint maps", () => {
      const serialized = serializeCheckpointsByPath(new Map(), new Map());
      expect(Object.keys(serialized.checkpointsByPath)).toHaveLength(0);
    });

    it("should handle missing checkpoints in source map", () => {
      const checkpointIds = new Map([["test.ts", ["id1", "missing"]]]);
      const sourceToCheckpoint = new Map([["id1", checkpoint]]);

      const serialized = serializeCheckpointsByPath(
        checkpointIds,
        sourceToCheckpoint,
      );
      expect(serialized.checkpointsByPath["test.ts"]).toHaveLength(1);
    });

    it("should skip paths with no valid checkpoints", () => {
      const checkpointIds = new Map([
        ["test.ts", ["id1"]],
        ["empty.ts", ["missing"]],
      ]);
      const sourceToCheckpoint = new Map([["id1", checkpoint]]);

      const serialized = serializeCheckpointsByPath(
        checkpointIds,
        sourceToCheckpoint,
      );
      expect(Object.keys(serialized.checkpointsByPath)).toHaveLength(1);
      expect(serialized.checkpointsByPath["empty.ts"]).toBeUndefined();
    });
  });

  describe("computeCheckpointChanges", () => {
    const checkpoint1 = createMockCheckpoint(
      "test1",
      100,
      "/root",
      "test.ts",
      "original",
      "original",
    );
    const checkpoint2 = createMockCheckpoint(
      "test2",
      200,
      "/root",
      "test.ts",
      "original",
      "modified\nlines\nadded",
    );

    it("should compute line changes correctly", () => {
      const changes1 = computeCheckpointChanges(checkpoint1, checkpoint1);
      expect(changes1.totalAddedLines).toBe(0);
      expect(changes1.totalRemovedLines).toBe(0);

      const changes2 = computeCheckpointChanges(checkpoint1, checkpoint2);
      expect(changes2.totalAddedLines).toBe(3);
      expect(changes2.totalRemovedLines).toBe(1);
    });

    it("should handle empty files", () => {
      const emptyCheckpoint = createMockCheckpoint(
        "empty",
        300,
        "/root",
        "test.ts",
        "",
        "",
      );
      const changes = computeCheckpointChanges(
        emptyCheckpoint,
        emptyCheckpoint,
      );
      expect(changes.totalAddedLines).toBe(0);
      expect(changes.totalRemovedLines).toBe(0);
    });

    it("should handle reversed timestamps", () => {
      const changes = computeCheckpointChanges(checkpoint2, checkpoint1);
      expect(changes.totalAddedLines).toBe(1);
      expect(changes.totalRemovedLines).toBe(3);
    });
  });

  describe("areSameFileCheckpoints", () => {
    const createCheckpoint = (relPath: string): DocumentCheckpoint =>
      createMockCheckpoint("test-id", 123, "/root", relPath);

    it("should identify same file checkpoints", () => {
      const checkpoint1 = createCheckpoint("test.ts");
      const checkpoint2 = createCheckpoint("test.ts");
      const checkpoint3 = createCheckpoint("other.ts");

      expect(areSameFileCheckpoints(checkpoint1, checkpoint2)).toBe(true);
      expect(areSameFileCheckpoints(checkpoint1, checkpoint3)).toBe(false);
    });

    it("should handle same instance", () => {
      const checkpoint = createCheckpoint("test.ts");
      expect(areSameFileCheckpoints(checkpoint, checkpoint)).toBe(true);
    });
  });
});
