/**
 * @file agent-shard-storage.ts
 * This file contains the implementation of the AgentShardStorage class, which provides
 * functionality for storing and retrieving serialized data and shards.
 */
import type { SerializedStore } from "@augment-internal/sidecar-libs/src/agent/agent-edit-types";
import { ShardedStorage } from "@augment-internal/sidecar-libs/src/agent/sharding/storage";
import type {
  SerializedShard,
  ShardId,
  ShardManifest,
} from "@augment-internal/sidecar-libs/src/agent/sharding/types";

import { getPluginFileStore } from "../client-interfaces/plugin-file-store";
import { getLogger } from "../logging";

/**
 * AgentShardStorage class implements the ShardedStorage interface for SerializedStore.
 * It provides methods for saving and loading serialized data, shards, and manifests.
 */
export class AgentShardStorage implements ShardedStorage<SerializedStore> {
  private readonly _logger = getLogger("AgentShardStorage");

  /**
   * The base path prefix for storing shards.
   */
  private static readonly storagePathKeyPrefix = "agent-edit-shard-storage";

  /**
   * The key used for storing and retrieving the manifest.
   */
  private static readonly manifestKey = "manifest";

  /**
   * The key used for storing and retrieving the serialized store.
   */
  private static readonly serializedStoreKey = "serialized-store";

  /**
   * Saves the serialized store to the global state.
   * @param value - The serialized store to save.
   * @returns A promise that resolves when the save operation is complete.
   */
  public async save(value: SerializedStore): Promise<void> {
    await this._saveJson(
      this._getStoragePath(
        StorageSubDirs.serializedStore,
        AgentShardStorage.serializedStoreKey,
      ),
      value,
    );
  }

  /**
   * Loads the serialized store from the global state.
   * @returns A promise that resolves with the loaded serialized store, or undefined if not found.
   */
  public async load(): Promise<SerializedStore | undefined> {
    return this._loadJson<SerializedStore>(
      this._getStoragePath(
        StorageSubDirs.serializedStore,
        AgentShardStorage.serializedStoreKey,
      ),
    );
  }

  /**
   * Saves a JSON object to the asset manager.
   * @param key - The key to use for storing the JSON object.
   * @param value - The JSON object to save.
   * @returns A promise that resolves when the save operation is complete.
   */
  private async _saveJson<T>(key: string, value: T): Promise<void> {
    const buffer = Buffer.from(JSON.stringify(value), "utf8");
    await getPluginFileStore().saveAsset(key, new Uint8Array(buffer));
  }

  /**
   * Loads a JSON object from the asset manager.
   * @param key - The key of the JSON object to load.
   * @returns A promise that resolves with the loaded JSON object, or undefined if not found.
   */
  private async _loadJson<T>(key: string): Promise<T | undefined> {
    const buffer = await getPluginFileStore().loadAsset(key);
    if (!buffer || buffer.length === 0) {
      return undefined;
    }
    return JSON.parse(Buffer.from(buffer).toString("utf8")) as T;
  }

  /**
   * Generates the storage path for a given key.
   * @param key - The key to generate the storage path for.
   * @returns The generated storage path.
   */
  private _getStoragePath(dir: StorageSubDirs, key: string): string {
    return `agent-edits/${dir}/${AgentShardStorage.storagePathKeyPrefix}-${key}.json`;
  }

  /**
   * Saves a shard to the storage.
   * @param shardId - The ID of the shard to save.
   * @param data - The serialized shard data to save.
   * @returns A promise that resolves when the save operation is complete.
   */
  public async saveShard(
    shardId: ShardId,
    data: SerializedShard,
  ): Promise<void> {
    await this._saveJson(
      this._getStoragePath(StorageSubDirs.shards, shardId),
      data,
    );
  }

  /**
   * Loads a shard from the storage.
   * @param shardId - The ID of the shard to load.
   * @returns A promise that resolves with the loaded shard data, or undefined if not found.
   */
  public async loadShard(
    shardId: ShardId,
  ): Promise<SerializedShard | undefined> {
    return this._loadJson<SerializedShard>(
      this._getStoragePath(StorageSubDirs.shards, shardId),
    );
  }

  /**
   * Deletes a shard from the storage.
   * @param shardId - The ID of the shard to delete.
   * @returns A promise that resolves when the delete operation is complete.
   */
  public async deleteShard(shardId: ShardId): Promise<void> {
    await getPluginFileStore().deleteAsset(
      this._getStoragePath(StorageSubDirs.shards, shardId),
    );
  }

  /**
   * Saves the shard manifest to the storage.
   * @param manifest - The shard manifest to save.
   * @returns A promise that resolves when the save operation is complete.
   */
  public async saveManifest(manifest: ShardManifest): Promise<void> {
    await this._saveJson(
      this._getStoragePath(
        StorageSubDirs.manifest,
        AgentShardStorage.manifestKey,
      ),
      manifest,
    );
  }

  /**
   * Loads the shard manifest from the storage.
   * @returns A promise that resolves with the loaded shard manifest, or undefined if not found.
   */
  public async loadManifest(): Promise<ShardManifest | undefined> {
    return this._loadJson<ShardManifest>(
      this._getStoragePath(
        StorageSubDirs.manifest,
        AgentShardStorage.manifestKey,
      ),
    );
  }
}

enum StorageSubDirs {
  shards = "shards",
  manifest = "manifest",
  serializedStore = "serialized-store",
}
