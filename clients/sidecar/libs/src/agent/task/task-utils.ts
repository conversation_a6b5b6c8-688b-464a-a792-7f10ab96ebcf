/**
 * @file task-utils.ts
 * This file contains utility functions for working with tasks.
 */

import {
  HydratedTask,
  type SerializedTask,
  TaskState,
  TaskUpdatedBy,
} from "./task-types";
import shortUuid from "short-uuid";

/**
 * Mapping from TaskState to markdown state markers.
 * Used for consistent representation of task states in markdown.
 */
export const STATE_MARKDOWN: Record<TaskState, string> = {
  [TaskState.NOT_STARTED]: "[ ]",
  [TaskState.IN_PROGRESS]: "[/]", // Use [/] to represent in-progress tasks
  [TaskState.COMPLETE]: "[x]",
  [TaskState.CANCELLED]: "[-]",
};

// Create a translator instance for UUID conversion
const translator = shortUuid(undefined, { consistentLength: true });

/**
 * Convert a full UUID to short UUID for markdown representation
 * @param fullUuid - The full UUID to convert
 * @returns A short UUID string, or the original string if conversion fails
 */
function toShortUuid(fullUuid: string): string {
  try {
    return translator.fromUUID(fullUuid);
  } catch (error) {
    // If conversion fails (e.g., invalid UUID format), return the original string
    // This provides backward compatibility with test UUIDs and malformed UUIDs
    return fullUuid;
  }
}

/**
 * Convert a short UUID back to full UUID for internal use
 * @param shortId - The short UUID to convert
 * @returns A full UUID string, or the original string if conversion fails
 */
export function fromShortUuid(shortId: string): string {
  try {
    return translator.toUUID(shortId);
  } catch (error) {
    // If conversion fails (e.g., not a valid short UUID), return the original string
    // This provides backward compatibility with regular UUIDs and test UUIDs
    return shortId;
  }
}

/**
 * Utility functions for working with tasks.
 */
export class TaskFactory {
  /**
   * Creates a new task object.
   * @param name - The name of the task
   * @param description - The description of the task
   * @param state - The initial state of the task (default: NOT_STARTED)
   * @param subTasks - The initial sub-tasks of the task (default: [])
   * @returns A new task object
   */
  public static createTask(
    name: string,
    description: string,
    state: TaskState = TaskState.NOT_STARTED,
    subTasks: string[] = [],
  ): SerializedTask {
    return {
      uuid: crypto.randomUUID(),
      name,
      description,
      state,
      subTasks,
      lastUpdated: Date.now(),
      lastUpdatedBy: TaskUpdatedBy.USER,
    };
  }

  /**
   * Creates a sub-task and adds it to the parent task.
   * @param parentTask - The parent task
   * @param name - The name of the sub-task
   * @param description - The description of the sub-task
   * @param state - The initial state of the sub-task (default: NOT_STARTED)
   * @returns A tuple containing the updated parent task and the new sub-task
   */
  public static createSubTask(
    parentTask: SerializedTask,
    name: string,
    description: string,
    state: TaskState = TaskState.NOT_STARTED,
  ): [SerializedTask, SerializedTask] {
    const subTask = this.createTask(name, description, state);
    const updatedParentTask = {
      ...parentTask,
      subTasks: [...parentTask.subTasks, subTask.uuid],
      lastUpdated: Date.now(),
      lastUpdatedBy: TaskUpdatedBy.USER,
    };

    return [updatedParentTask, subTask];
  }
}

/**
 * Utility functions for querying tasks.
 */
export class TaskQueries {
  /**
   * Filters tasks by state.
   * @param tasks - The tasks to filter
   * @param states - The states to filter by
   * @returns The filtered tasks
   */
  public static filterByState(
    tasks: SerializedTask[],
    states: TaskState[],
  ): SerializedTask[] {
    return tasks.filter((task) => states.includes(task.state));
  }

  /**
   * Gets active tasks (not cancelled or completed).
   * @param tasks - The tasks to filter
   * @returns The active tasks
   */
  public static getActiveTasks(tasks: SerializedTask[]): SerializedTask[] {
    return this.filterByState(tasks, [
      TaskState.NOT_STARTED,
      TaskState.IN_PROGRESS,
    ]);
  }

  /**
   * Gets completed tasks.
   * @param tasks - The tasks to filter
   * @returns The completed tasks
   */
  public static getCompletedTasks(tasks: SerializedTask[]): SerializedTask[] {
    return this.filterByState(tasks, [TaskState.COMPLETE]);
  }

  /**
   * Gets cancelled tasks.
   * @param tasks - The tasks to filter
   * @returns The cancelled tasks
   */
  public static getCancelledTasks(tasks: SerializedTask[]): SerializedTask[] {
    return this.filterByState(tasks, [TaskState.CANCELLED]);
  }

  /**
   * Filters tasks by last updated time range.
   * @param tasks - The tasks to filter
   * @param minTime - The minimum time (inclusive, optional)
   * @param maxTime - The maximum time (inclusive, optional)
   * @returns The filtered tasks
   */
  public static filterByTimeRange(
    tasks: SerializedTask[],
    minTime?: number,
    maxTime?: number,
  ): SerializedTask[] {
    return tasks.filter((task) => {
      if (minTime !== undefined && task.lastUpdated < minTime) {
        return false;
      }
      if (maxTime !== undefined && task.lastUpdated > maxTime) {
        return false;
      }
      return true;
    });
  }

  /**
   * Filters tasks by who last updated them.
   * @param tasks - The tasks to filter
   * @param updatedBy - Who last updated the tasks
   * @returns The filtered tasks
   */
  public static filterByUpdatedBy(
    tasks: SerializedTask[],
    updatedBy: TaskUpdatedBy,
  ): SerializedTask[] {
    return tasks.filter((task) => task.lastUpdatedBy === updatedBy);
  }
}

export function findTaskInTree(
  rootTask: HydratedTask,
  uuid: string,
): HydratedTask | undefined {
  if (rootTask.uuid === uuid) {
    return rootTask;
  }
  if (rootTask.subTasksData) {
    for (const subTask of rootTask.subTasksData) {
      const found = findTaskInTree(subTask, uuid);
      if (found) {
        return found;
      }
    }
  }
  return undefined;
}

export function toFlatTask(task: HydratedTask): HydratedTask[] {
  // DFS and collect all tasks
  const tasks: HydratedTask[] = [];
  const stack: HydratedTask[] = [task];
  while (stack.length > 0) {
    const currentTask = stack.pop()!;
    tasks.push(currentTask);
    if (currentTask.subTasksData) {
      stack.push(...currentTask.subTasksData);
    }
  }
  return tasks;
}

/**
 * Options for markdown representation and parsing
 */
export interface MarkdownOptions {
  /** If true, only include the task itself, not its subtasks (default: false) */
  shallow?: boolean;
  /** If true, exclude UUIDs from markdown and generate new ones when parsing (default: false) */
  excludeUuid?: boolean;
  /** If true, use short UUIDs in markdown representation (default: true) */
  shortUuid?: boolean;
}

/**
 * Generates a markdown representation of a task.
 * @param task - The task to represent
 * @param options - Options for markdown generation
 * @returns A string containing the markdown representation of the task
 */
export function getMarkdownRepresentation(
  task: HydratedTask,
  options: MarkdownOptions = {},
): string {
  const { shallow = false, excludeUuid = false, shortUuid = true } = options;
  return _getMarkdownRepresentation(task, {
    shallow,
    excludeUuid,
    shortUuid,
  }).join("\n");
}

/**
 * Internal helper function to generate markdown representation of a task.
 * @param task - The task to represent
 * @param options - Options for markdown generation
 * @returns An array of strings, each representing a line in the markdown
 */
function _getMarkdownRepresentation(
  task: HydratedTask,
  options: MarkdownOptions = {},
): string[] {
  const { shallow = false, excludeUuid = false, shortUuid = true } = options;

  let uuidPart = "";
  if (!excludeUuid) {
    const displayUuid = shortUuid ? toShortUuid(task.uuid) : task.uuid;
    uuidPart = `UUID:${displayUuid} `;
  }
  const currentTaskMarkdown = `${STATE_MARKDOWN[task.state]} ${uuidPart}NAME:${task.name} DESCRIPTION:${task.description}`;

  // If shallow is true or there are no subtasks, just return the current task
  if (shallow || !task.subTasksData || task.subTasksData.length === 0) {
    return [currentTaskMarkdown];
  }

  // Otherwise, include subtasks recursively
  const subtaskMarkdown = (task.subTasksData || [])
    .map((subtask) => {
      const subtaskMd = _getMarkdownRepresentation(subtask, options);
      return subtaskMd.map((line) => `-${line}`);
    })
    .flat();

  return [currentTaskMarkdown, ...subtaskMarkdown];
}

/**
 * Deep clone a task with new UUIDs
 *
 * @param task
 * @returns
 */
export function deepCloneTask(
  task: HydratedTask,
  options?: {
    keepUuid?: boolean;
  },
): HydratedTask {
  const subTasksData = task.subTasksData?.map((subtask) =>
    deepCloneTask(subtask, options),
  );
  return {
    ...task,
    uuid: options?.keepUuid ? task.uuid : crypto.randomUUID(),
    subTasks: subTasksData?.map((subtask) => subtask.uuid) || [],
    subTasksData,
  };
}

// Get serialized tasks to update
/**
 * Compare two task trees and identify created, updated, and deleted tasks.
 * @param oldTree - The original task tree
 * @param newTree - The updated task tree
 * @returns Object containing created, updated, and deleted tasks
 */
export function diffTaskTrees(
  oldTree: HydratedTask,
  newTree: HydratedTask,
): {
  created: HydratedTask[];
  updated: HydratedTask[];
  deleted: HydratedTask[];
} {
  const oldList = toFlatTask(oldTree);
  const newList = toFlatTask(newTree);

  // Create maps for quick lookup by UUID
  const uuidToOld = new Map(oldList.map((task) => [task.uuid, task]));
  const uuidToNew = new Map(newList.map((task) => [task.uuid, task]));

  // Get all tasks that are new (in newList but not in oldList)
  const newTasks = newList.filter((task) => !uuidToOld.has(task.uuid));

  // Get all tasks that are deleted (in oldList but not in newList)
  const deletedTasks = oldList.filter((task) => !uuidToNew.has(task.uuid));

  // Get all tasks that exist in both trees and have actually changed
  const updatedTasks: HydratedTask[] = [];
  for (const oldTask of oldList) {
    const newTask = uuidToNew.get(oldTask.uuid);
    if (newTask && hasTaskChanged(oldTask, newTask)) {
      updatedTasks.push(newTask);
    }
  }

  return {
    created: newTasks,
    updated: updatedTasks,
    deleted: deletedTasks,
  };
}

/**
 * Check if a task has actually changed by comparing relevant fields.
 * @param oldTask - The original task
 * @param newTask - The new task
 * @returns True if the task has changed, false otherwise
 */
function hasTaskChanged(oldTask: HydratedTask, newTask: HydratedTask): boolean {
  // Compare the fields that matter for task updates
  return (
    oldTask.name !== newTask.name ||
    oldTask.description !== newTask.description ||
    oldTask.state !== newTask.state ||
    // Compare subTasks arrays (order matters)
    JSON.stringify(oldTask.subTasks) !== JSON.stringify(newTask.subTasks)
  );
}

export function parseMarkdownToTaskTree(
  markdown: string,
  options: MarkdownOptions = {},
): HydratedTask {
  if (!markdown.trim()) {
    throw new Error("Empty markdown");
  }

  const lines = markdown.split("\n");
  let rootTaskCount = 0;

  // First pass: count root tasks (level 0) to ensure there's only one
  for (const line of lines) {
    const trimmedLine = line.trim();
    if (!trimmedLine) continue;

    const indentLevel = getIndentationLevel(line);
    if (indentLevel === 0) {
      try {
        parseTaskLine(line, options);
        rootTaskCount++;
      } catch (error) {
        // Ignore invalid lines
      }
    }
  }

  if (rootTaskCount === 0) {
    throw new Error("No root task found");
  }

  if (rootTaskCount > 1) {
    throw new Error(
      `Multiple root tasks found (${rootTaskCount}). There can only be one root task per conversation. ` +
        `All other tasks must be subtasks (indented with dashes). ` +
        `Root task format: [ ] UUID:xxx NAME:yyy DESCRIPTION:zzz (no dashes). ` +
        `Subtask format: -[ ] UUID:xxx NAME:yyy DESCRIPTION:zzz (with dashes).`,
    );
  }

  // Reset lines for actual parsing
  const parseLines = markdown.split("\n");
  function popNextValidTask():
    | {
        task: HydratedTask;
        level: number;
      }
    | undefined {
    while (parseLines.length > 0) {
      const line = parseLines.shift()!;
      const indentLevel = getIndentationLevel(line);
      try {
        return {
          task: parseTaskLine(line, options),
          level: indentLevel,
        };
      } catch (error) {
        // Ignore and try next line
      }
    }
    return undefined;
  }

  const rootTask = popNextValidTask();
  if (!rootTask) {
    throw new Error("No root task found");
  }
  const taskStack: HydratedTask[] = [rootTask.task];

  let currTaskInfo: { task: HydratedTask; level: number } | undefined;
  while ((currTaskInfo = popNextValidTask())) {
    const parentTask = taskStack[currTaskInfo.level - 1];
    if (!parentTask) {
      throw new Error(
        `Invalid markdown: level ${currTaskInfo.level + 1} has no parent\n` +
          `Line: ${currTaskInfo.task.name} is missing a parent\n` +
          `Current tasks: \n${getMarkdownRepresentation(rootTask.task)}`,
      );
    }

    if (!parentTask.subTasksData || !parentTask.subTasks) {
      parentTask.subTasks = [];
      parentTask.subTasksData = [];
    }

    parentTask.subTasksData.push(currTaskInfo.task);
    parentTask.subTasks.push(currTaskInfo.task.uuid);
    taskStack[currTaskInfo.level] = currTaskInfo.task;
    taskStack.splice(currTaskInfo.level + 1);
  }

  return rootTask.task;
}

function getIndentationLevel(line: string): number {
  // Count leading whitespace and dashes as hierarchy levels
  // This allows for flexible indentation using either whitespace or dashes:
  // - 2 spaces = 1 level
  // - 1 tab = 1 level
  // - 1 dash = 1 level
  // - Mixed combinations are supported (e.g., "  -" = 2 levels)
  let level = 0;
  let index = 0;

  // Count leading whitespace (2 spaces = 1 indentation level)
  while (index < line.length && (line[index] === " " || line[index] === "\t")) {
    if (line[index] === " ") {
      level += 0.5; // 2 spaces = 1 level
    } else if (line[index] === "\t") {
      level += 1; // 1 tab = 1 level
    }
    index++;
  }

  // Count dashes (each dash = 1 level)
  while (index < line.length && line[index] === "-") {
    level += 1;
    index++;
  }

  // Round down to nearest integer (so 1 space = 0 levels, 2 spaces = 1 level)
  return Math.floor(level);
}

/**
 * Parse a line of markdown into a task.
 * @param line - The line to parse
 * @param level - The indentation level of the line
 * @param options - Options for parsing behavior
 * @returns The parsed task
 */
function parseTaskLine(
  line: string,
  options: MarkdownOptions = {},
): HydratedTask {
  const { excludeUuid = false, shortUuid = true } = options;

  // Skip over the leading whitespace and dashes that contribute to the indentation level
  // Note: level parameter is kept for backward compatibility but not used here
  // since we now calculate indentation dynamically
  let index = 0;
  while (
    index < line.length &&
    (line[index] === " " || line[index] === "\t" || line[index] === "-")
  ) {
    index++;
  }
  const cleanLine = line.substring(index);

  // First, check if the line has a valid state marker
  // Allow 0 or more whitespace characters between the dashes and the state marker
  const stateMatch = cleanLine.match(/^\s*\[([ x\-/?])\]/);
  if (!stateMatch) {
    throw new Error(`Invalid task line: ${line} (missing state)`);
  }

  // Extract the state character
  const stateChar = stateMatch[1];

  // Create a reverse mapping from markdown markers to task states
  const markdownToState = Object.entries(STATE_MARKDOWN).reduce(
    (acc, [state, marker]) => {
      // Extract just the character inside the brackets
      const char = marker.substring(1, 2);
      acc[char] = state as TaskState;
      return acc;
    },
    {} as Record<string, TaskState>,
  );

  // Determine the task state based on the state character
  const state = markdownToState[stateChar] || TaskState.NOT_STARTED;

  // Extract the content after the state marker for field parsing
  const contentAfterState = cleanLine
    .substring(stateMatch.index! + stateMatch[0].length)
    .trim();

  let uuid: string;
  let name: string;
  let description: string;

  if (!excludeUuid) {
    // Regex pattern to extract UUID, name, and description
    // This pattern is more flexible with whitespace between fields
    // It also handles the case where there are no spaces between fields
    const fieldsPattern =
      /(?:uuid|UUID):([^]*?)(?=(?:name|NAME):)(?:name|NAME):([^]*?)(?=(?:description|DESCRIPTION):)(?:description|DESCRIPTION):(.*)$/i;

    const fieldsMatch = contentAfterState.match(fieldsPattern);

    if (!fieldsMatch) {
      // Check if it's missing required fields
      const hasUuid = /\b(?:uuid|UUID):/i.test(contentAfterState);
      const hasName = /\b(?:name|NAME):/i.test(contentAfterState);
      const hasDesc = /\b(?:description|DESCRIPTION):/i.test(contentAfterState);

      if (!hasUuid || !hasName || !hasDesc) {
        throw new Error(`Invalid task line: ${line} (missing required fields)`);
      }

      // Check if the fields are in the wrong order
      const uuidPos = contentAfterState.toLowerCase().indexOf("uuid:");
      const namePos = contentAfterState.toLowerCase().indexOf("name:");
      const descPos = contentAfterState.toLowerCase().indexOf("description:");

      if (!(uuidPos < namePos && namePos < descPos)) {
        throw new Error(`Invalid task line: ${line} (incorrect field order)`);
      }

      // If we get here, it's some other format issue
      throw new Error(`Invalid task line: ${line} (invalid format)`);
    }

    // Extract values from the regex match
    uuid = fieldsMatch[1].trim();
    name = fieldsMatch[2].trim();
    description = fieldsMatch[3].trim();

    // Validate that UUID and name are not empty
    if (!uuid || !name) {
      throw new Error(`Invalid task line: ${line} (missing required fields)`);
    }

    // Use the UUID from the markdown. If it's exactly "NEW_UUID", generate a new one.
    // Otherwise, use the provided string (e.g., "NEW_UUID_123" or an actual UUID).
    if (uuid === "NEW_UUID") {
      uuid = crypto.randomUUID();
    } else if (shortUuid) {
      // If shortUuid is true, convert the short UUID back to a full UUID
      try {
        uuid = fromShortUuid(uuid);
      } catch (error) {
        // If conversion fails, treat it as a regular UUID (backward compatibility)
        // This handles cases where the UUID in markdown is already a full UUID
      }
    }
  } else {
    // When excludeUuid is true, parse without UUID and generate a new one
    const fieldsPattern =
      /(?:name|NAME):([^]*?)(?=(?:description|DESCRIPTION):)(?:description|DESCRIPTION):(.*)$/i;

    const fieldsMatch = contentAfterState.match(fieldsPattern);

    if (!fieldsMatch) {
      // Check if it's missing required fields
      const hasName = /\b(?:name|NAME):/i.test(contentAfterState);
      const hasDesc = /\b(?:description|DESCRIPTION):/i.test(contentAfterState);

      if (!hasName || !hasDesc) {
        throw new Error(`Invalid task line: ${line} (missing required fields)`);
      }

      // Check if the fields are in the wrong order
      const namePos = contentAfterState.toLowerCase().indexOf("name:");
      const descPos = contentAfterState.toLowerCase().indexOf("description:");

      if (!(namePos < descPos)) {
        throw new Error(`Invalid task line: ${line} (incorrect field order)`);
      }

      // If we get here, it's some other format issue
      throw new Error(`Invalid task line: ${line} (invalid format)`);
    }

    // Extract values from the regex match
    name = fieldsMatch[1].trim();
    description = fieldsMatch[2].trim();

    // Validate that name is not empty
    if (!name) {
      throw new Error(`Invalid task line: ${line} (missing required fields)`);
    }

    // Always generate a new UUID when excludeUuid is true
    uuid = crypto.randomUUID();
  }

  return {
    uuid,
    name,
    description,
    state,
    subTasks: [],
    lastUpdated: Date.now(),
    lastUpdatedBy: TaskUpdatedBy.USER,
  };
}

const createTaskFromPartial = (
  partialTask: Partial<HydratedTask>,
): HydratedTask => {
  return {
    uuid: crypto.randomUUID(),
    name: "New Task",
    description: "New task description",
    state: TaskState.NOT_STARTED,
    subTasks: [],
    lastUpdated: Date.now(),
    lastUpdatedBy: TaskUpdatedBy.USER,
    ...partialTask,
  };
};

const SAMPLE_TASK_1_1: HydratedTask = createTaskFromPartial({
  name: "Task 1.1",
  description: "This is the first sub task",
  state: TaskState.IN_PROGRESS,
});

const SAMPLE_TASK_1_2_1: HydratedTask = createTaskFromPartial({
  name: "Task 1.2.1",
  description: "This is a nested sub task, child of Task 1.2",
  state: TaskState.NOT_STARTED,
});

const SAMPLE_TASK_1_2_2: HydratedTask = createTaskFromPartial({
  name: "Task 1.2.2",
  description: "This is another nested sub task, child of Task 1.2",
  state: TaskState.IN_PROGRESS,
});

const SAMPLE_TASK_1_2: HydratedTask = createTaskFromPartial({
  name: "Task 1.2",
  description: "This is the second sub task",
  state: TaskState.COMPLETE,
  subTasks: [SAMPLE_TASK_1_2_1.uuid, SAMPLE_TASK_1_2_2.uuid],
  subTasksData: [SAMPLE_TASK_1_2_1, SAMPLE_TASK_1_2_2],
});

const SAMPLE_TASK_1_3: HydratedTask = createTaskFromPartial({
  name: "Task 1.3",
  description: "This is the third sub task",
  state: TaskState.CANCELLED,
});

const SAMPLE_TASK_1: HydratedTask = createTaskFromPartial({
  name: "Task 1",
  description: "This is the first task",
  state: TaskState.NOT_STARTED,
  subTasks: [SAMPLE_TASK_1_1.uuid, SAMPLE_TASK_1_2.uuid, SAMPLE_TASK_1_3.uuid],
  subTasksData: [SAMPLE_TASK_1_1, SAMPLE_TASK_1_2, SAMPLE_TASK_1_3],
});

export const SAMPLE_TASK_MARKDOWN = getMarkdownRepresentation(SAMPLE_TASK_1);

/**
 * Interface representing the diff results from diffTaskTrees
 */
export interface TaskTreeDiff {
  created: HydratedTask[];
  updated: HydratedTask[];
  deleted: HydratedTask[];
}

/**
 * Interface representing parsed diff counts
 */
export interface TaskDiffCounts {
  created: number;
  updated: number;
  deleted: number;
}

/**
 * Converts task tree diff results into a markdown format
 * @param diff - The diff results from diffTaskTrees
 * @returns Markdown string representing the changes
 */
export function taskDiffToMarkdown(diff: TaskTreeDiff): string {
  const sections: string[] = [];

  // Created tasks section
  if (diff.created.length > 0) {
    sections.push("## Created Tasks");
    sections.push("");
    for (const task of diff.created) {
      sections.push(
        `${STATE_MARKDOWN[task.state]} UUID:${toShortUuid(task.uuid)} NAME:${task.name} DESCRIPTION:${task.description}`,
      );
    }
    sections.push("");
  }

  // Updated tasks section
  if (diff.updated.length > 0) {
    sections.push("## Updated Tasks");
    sections.push("");
    for (const task of diff.updated) {
      sections.push(
        `${STATE_MARKDOWN[task.state]} UUID:${toShortUuid(task.uuid)} NAME:${task.name} DESCRIPTION:${task.description}`,
      );
    }
    sections.push("");
  }

  // Deleted tasks section
  if (diff.deleted.length > 0) {
    sections.push("## Deleted Tasks");
    sections.push("");
    for (const task of diff.deleted) {
      sections.push(
        `${STATE_MARKDOWN[task.state]} UUID:${toShortUuid(task.uuid)} NAME:${task.name} DESCRIPTION:${task.description}`,
      );
    }
    sections.push("");
  }

  return sections.join("\n");
}

/**
 * Parses markdown format and extracts task data from diff sections
 * @param markdown - The markdown string containing task changes
 * @returns Object with arrays of tasks by category
 */
export function parseTaskDiffMarkdown(markdown: string): TaskTreeDiff {
  const lines = markdown.split("\n");
  let currentSection: "created" | "updated" | "deleted" | null = null;
  const result = {
    created: [] as HydratedTask[],
    updated: [] as HydratedTask[],
    deleted: [] as HydratedTask[],
  };

  for (const line of lines) {
    const trimmedLine = line.trim();

    // Check for section headers
    if (trimmedLine === "## Created Tasks") {
      currentSection = "created";
      continue;
    } else if (trimmedLine === "## Updated Tasks") {
      currentSection = "updated";
      continue;
    } else if (trimmedLine === "## Deleted Tasks") {
      currentSection = "deleted";
      continue;
    }

    // Parse task lines
    if (
      currentSection &&
      (trimmedLine.startsWith("[ ]") ||
        trimmedLine.startsWith("[/]") ||
        trimmedLine.startsWith("[x]") ||
        trimmedLine.startsWith("[-]"))
    ) {
      try {
        const task = parseTaskLine(trimmedLine, {
          excludeUuid: false,
          shortUuid: true,
        });
        if (task) {
          result[currentSection].push(task);
        }
      } catch (error) {
        // Skip invalid task lines
      }
    }
  }

  return result;
}

/**
 * Extracts task counts from full tool result text
 * @param text - The full tool result text containing counts and task changes
 * @returns Object with created, updated, deleted counts
 */
export function getTaskDiffCounts(text: string): TaskDiffCounts {
  // First try to extract from the summary line (e.g., "Created: 2, Updated: 1, Deleted: 0")
  const match = text.match(/Created: (\d+), Updated: (\d+), Deleted: (\d+)/);
  if (match) {
    return {
      created: parseInt(match[1], 10),
      updated: parseInt(match[2], 10),
      deleted: parseInt(match[3], 10),
    };
  }

  // Fallback to parsing the markdown section
  const markdown = extractTaskDiffMarkdown(text);
  const diff = parseTaskDiffMarkdown(markdown);
  return {
    created: diff.created.length,
    updated: diff.updated.length,
    deleted: diff.deleted.length,
  };
}

/**
 * Extracts the task diff markdown section from full tool result text
 * @param text - The full tool result text
 * @returns The markdown section containing task changes
 */
export function extractTaskDiffMarkdown(text: string): string {
  const startMarker = "# Task Changes";
  const startIndex = text.indexOf(startMarker);
  if (startIndex === -1) return "";

  const afterStart = text.substring(startIndex);
  const endMarkers = ["\nNew and Updated Tasks:", "\nRemember:", "\n\n---"];

  let endIndex = afterStart.length;
  for (const marker of endMarkers) {
    const markerIndex = afterStart.indexOf(marker);
    if (markerIndex !== -1 && markerIndex < endIndex) {
      endIndex = markerIndex;
    }
  }

  const fullSection = afterStart.substring(0, endIndex);
  const contentStart = fullSection.indexOf("\n");
  if (contentStart === -1) return "";

  return fullSection.substring(contentStart + 1).trim();
}

/**
 * Parses full tool result text and extracts task data from diff sections
 * @param text - The full tool result text containing task changes
 * @returns Object with arrays of tasks by category
 */
export function parseTaskDiffFromFullText(text: string): TaskTreeDiff {
  const markdown = extractTaskDiffMarkdown(text);
  return parseTaskDiffMarkdown(markdown);
}

/**
 * Utility functions for generating instructional text for task tools.
 * These functions provide consistent messaging across different task tools.
 */
export class TaskInstructionUtils {
  /**
   * Generates the main task list instructions for the ViewTaskListTool.
   * @returns Formatted instruction text for viewing and updating task lists
   */
  public static getTaskListInstructions(): string {
    return (
      `To update this task list, use the update_tasks tool. To add new tasks, use the add_tasks tool.\n\n` +
      `IMPORTANT: When updating the task list:\n` +
      `1. Maintain the proper hierarchy with correct indentation:\n` +
      `   - Root tasks have no dashes: [ ] UUID:xxx NAME:yyy DESCRIPTION:zzz\n` +
      `   - Level 1 tasks have one dash: -[ ] UUID:xxx NAME:yyy DESCRIPTION:zzz\n` +
      `   - Level 2 tasks have two dashes: --[ ] UUID:xxx NAME:yyy DESCRIPTION:zzz\n` +
      `   - Every sub-task MUST have a parent task one level above it\n` +
      `   - New tasks should use UUID:NEW_UUID\n\n` +
      `2. Use the correct task state markers:\n` +
      `   - [ ] = Not started (for tasks you haven't begun working on yet)\n` +
      `   - [/] = In progress (for tasks you're currently working on)\n` +
      `   - [-] = Cancelled (for tasks that are no longer relevant)\n` +
      `   - [x] = Completed (for tasks the user has confirmed are complete)\n\n` +
      `3. Update task states as you work:\n` +
      `   - Start with tasks in the not started state ([ ])\n` +
      `   - Mark tasks as in progress ([/]) when you start working on them\n` +
      `   - Mark tasks as completed ([x]) when they are done\n` +
      `   - Mark tasks as cancelled ([-]) if they're no longer needed`
    );
  }

  /**
   * Generates help text for markdown parsing errors in BulkUpdateTaskListTool.
   * @returns Formatted help text for fixing markdown parsing issues
   */
  public static getMarkdownParsingHelpText(): string {
    return (
      `IMPORTANT: Make sure each task follows the correct hierarchy:\n` +
      `- There can only be ONE root task (no dashes): [ ] UUID:xxx NAME:yyy DESCRIPTION:zzz\n` +
      `- Level 1 tasks have one dash: -[ ] UUID:xxx NAME:yyy DESCRIPTION:zzz\n` +
      `- Level 2 tasks have two dashes: --[ ] UUID:xxx NAME:yyy DESCRIPTION:zzz\n` +
      `- Every task at level N (N > 0) MUST have a parent task at level N-1\n` +
      `- Do not skip levels (e.g., going from root to -- without a - level)\n` +
      `- Ensure all tasks have a UUID, NAME, and DESCRIPTION in that order\n` +
      `- Use the correct state marker: [ ] = not started, [/] = in progress, [-] = cancelled, [x] = completed\n` +
      `- Remember to update task states as you work: mark as in progress ([/]) when starting, mark as completed ([x]) when finished, and mark as cancelled ([-]) if they're no longer needed.`
    );
  }

  /**
   * Generates reminder text for future task list updates.
   * @returns Formatted reminder text for task state management
   */
  public static getTaskUpdateReminder(): string {
    return (
      `Remember: When updating the task list in the future:\n` +
      `- Mark tasks as in progress ([/]) when you start working on them\n` +
      `- Mark tasks as completed ([x]) when the user explicitly confirms they are done\n` +
      `- Mark tasks as cancelled ([-]) if they're no longer needed\n` +
      `- Use the correct UUID for existing tasks. Make sure to use the generated UUIDs for new tasks.`
    );
  }

  /**
   * Formats a complete task list view response.
   * @param markdown - The task list markdown content
   * @returns Complete formatted response for ViewTaskListTool
   */
  public static formatTaskListViewResponse(markdown: string): string {
    return `# Current Task List\n\n${markdown}`;
  }

  /**
   * Formats a bulk update success response with diff information.
   * @param taskDiff - The diff results from diffTaskTrees
   * @returns Complete formatted response for ReorganizeTaskListTool
   */
  public static formatBulkUpdateResponse(taskDiff: TaskTreeDiff): string {
    const diffMarkdown = taskDiffToMarkdown(taskDiff);
    const { created, updated, deleted } = taskDiff;
    const createdCount = created.length;
    const updatedCount = updated.length;
    const deletedCount = deleted.length;

    let responseMessage = `Task list updated successfully. Created: ${createdCount}, Updated: ${updatedCount}, Deleted: ${deletedCount}.\n\n`;
    if (diffMarkdown.trim()) {
      responseMessage += `# Task Changes\n\n${diffMarkdown}`;
    }

    return responseMessage;
  }

  /**
   * Tool descriptions for consistent messaging across task tools.
   */
  public static getToolDescriptions() {
    return {
      viewTaskList: "View the current task list for the conversation.",

      updateTasks:
        "Update one or more tasks' properties (state, name, description). Can update a single task or multiple tasks in one call. Use this on complex sequences of work to plan, track progress, and manage work.",

      addTasks:
        "Add one or more new tasks to the task list. Can add a single task or multiple tasks in one call. Tasks can be added as subtasks or after specific tasks. Use this when planning complex sequences of work.",
      reorganizeTaskList:
        "Reorganize the task list structure for the current conversation. Use this only for major restructuring like reordering tasks, changing hierarchy. For individual task updates, use update_tasks tool.",
    };
  }
}

/**
 * Task prompt utilities for generating prompts and mentions for task delegation.
 * These functions are used when tasks are mentioned in chat for sub-agent delegation.
 */
export class TaskPromptUtils {
  /**
   * Generate a task delegation prompt for a given task.
   * This prompt is used when a task is mentioned in chat to delegate work to sub-agents.
   *
   * @param task The task object containing task details and context
   * @returns A formatted prompt string for task delegation
   */
  public static getTaskOrchestratorPrompt(task: {
    taskUuid: string;
    taskTree: HydratedTask;
    surroundingContext: {
      rootTask: HydratedTask;
      targetTaskPath: string[];
    };
  }): string {
    const { taskTree, surroundingContext } = task;

    // Build the task context with the full task tree structure
    const taskContext = this.buildTaskContext(taskTree, surroundingContext);

    return `Please utilize sub-agents to complete the following task tree.
Here are the details, along with a suggestion prompt.
You may use 1 or more sub-agents in to complete the below task.
For each sub-agent, please give it the relevant context and breakdown of the below task.

## Task Details
**Name:** ${taskTree.name}
${taskTree.description ? `**Description:** ${taskTree.description}` : ""}
**Status:** ${taskTree.state}

## Task Context
${taskContext}

## Instructions
Please complete this task according to the requirements.
When you are done, report back on the completion status with a summary of changes made,
important context, and other relevant information for the supervisor.

Focus on this specific task tree while being aware of the broader context provided above.`;
  }

  /**
   * Generate a unique mention ID for a task.
   * This ID is used to identify the task mention in the chat.
   *
   * @param taskData The task data containing UUID and context
   * @returns A unique string ID for the task mention
   */
  public static getTaskMentionId(taskData: {
    taskUuid: string;
    taskTree: HydratedTask;
    surroundingContext: {
      rootTask: HydratedTask;
      targetTaskPath: string[];
    };
  }): string {
    // Create a unique ID that includes the task UUID and some context
    return `task:${taskData.taskUuid}:${taskData.taskTree.name.replace(/\s+/g, "_")}`;
  }

  /**
   * Generate a human-readable label for a task mention.
   * This label is displayed in the chat UI when the task is mentioned.
   *
   * @param taskData The task data containing task details
   * @returns A human-readable label for the task mention
   */
  public static getTaskMentionLabel(taskData: {
    taskUuid: string;
    taskTree: HydratedTask;
    surroundingContext: {
      rootTask: HydratedTask;
      targetTaskPath: string[];
    };
  }): string {
    const { taskTree, surroundingContext } = taskData;

    // If this is a subtask, show the path for context
    if (surroundingContext.targetTaskPath.length > 1) {
      const parentPath = surroundingContext.targetTaskPath
        .slice(0, -1)
        .join(" → ");
      return `${parentPath} → ${taskTree.name}`;
    }

    // For top-level tasks, just use the name
    return taskTree.name;
  }

  /**
   * Build a context string that shows the task's position within the larger task tree
   */
  private static buildTaskContext(
    targetTask: HydratedTask,
    surroundingContext: { rootTask: HydratedTask; targetTaskPath: string[] },
  ): string {
    const { rootTask, targetTaskPath } = surroundingContext;

    let context = `This task is part of a larger project: "${rootTask.name}"`;

    if (rootTask.description) {
      context += `\n\n**Project Description:** ${rootTask.description}`;
    }

    // Show the path to this task within the tree
    if (targetTaskPath.length > 1) {
      context += `\n\n**Task Path:** ${targetTaskPath.join(" → ")}`;
    }

    // Show subtasks if any
    if (targetTask.subTasksData && targetTask.subTasksData.length > 0) {
      context += `\n\n**Subtasks:**`;
      targetTask.subTasksData.forEach(
        (subtask: HydratedTask, index: number) => {
          context += `\n${index + 1}. ${subtask.name} (${subtask.state})`;
          if (subtask.description) {
            context += ` - ${subtask.description}`;
          }
        },
      );
    }

    return context;
  }
}

export const testOnlyFunctions = {
  _parseTaskLine: parseTaskLine,
  _parseMarkdownToTaskTree: parseMarkdownToTaskTree,
  _getMarkdownRepresentation: getMarkdownRepresentation,
  _getIndentationLevel: getIndentationLevel,
};
