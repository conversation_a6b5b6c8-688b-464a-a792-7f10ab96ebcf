/**
 * @file task-storage.ts
 * This file contains the implementation of the TaskStorage interface.
 */

import { getPluginFileStore } from "../../client-interfaces/plugin-file-store";
import { getLogger } from "../../logging";
import type { SerializedTask, TaskManifest, TaskStorage } from "./task-types";

/**
 * Enum representing the sub-directories used for storage.
 */
enum StorageSubDirs {
  tasks = "tasks",
  manifest = "manifest",
}

/**
 * FileBackedTaskStorage implements the TaskStorage interface
 * using the plugin file store for persistence.
 */
export class FileBackedTaskStorage implements TaskStorage {
  private readonly _logger = getLogger("FileBackedTaskStorage");

  /**
   * The base path prefix for storing tasks.
   */
  private static readonly storagePathKeyPrefix = "task-storage";

  /**
   * The key used for storing and retrieving the manifest.
   */
  private static readonly manifestKey = "manifest";

  constructor() {}

  /**
   * Gets the storage path for a task or manifest.
   * @param subDir - The sub-directory to use
   * @param key - The key to use
   * @returns The full storage path
   */
  private _getStoragePath(subDir: string, key: string): string {
    return `${FileBackedTaskStorage.storagePathKeyPrefix}/${subDir}/${key}`;
  }

  /**
   * Saves JSON data to the plugin file store.
   * @param path - The path to save to
   * @param data - The data to save
   */
  private async _saveJson<T>(path: string, data: T): Promise<void> {
    const fileStore = getPluginFileStore();
    const jsonString = JSON.stringify(data, null, 2);
    await fileStore.saveAsset(path, new TextEncoder().encode(jsonString));
  }

  /**
   * Loads JSON data from the plugin file store.
   * @param path - The path to load from
   * @returns The loaded data, or undefined if not found
   */
  private async _loadJson<T>(path: string): Promise<T | undefined> {
    const fileStore = getPluginFileStore();
    const data = await fileStore.loadAsset(path);
    if (!data) {
      return undefined;
    }

    try {
      const jsonString = new TextDecoder().decode(data);
      return JSON.parse(jsonString) as T;
    } catch (error) {
      this._logger.error(`Failed to parse JSON from ${path}`, error);
      return undefined;
    }
  }

  /**
   * Saves a task to storage.
   * @param uuid - The UUID of the task to save
   * @param task - The task to save
   */
  public async saveTask(uuid: string, task: SerializedTask): Promise<void> {
    await this._saveJson(
      this._getStoragePath(StorageSubDirs.tasks, uuid),
      task,
    );
  }

  /**
   * Loads a task from storage.
   * @param uuid - The UUID of the task to load
   * @returns The loaded task, or undefined if not found
   */
  public async loadTask(uuid: string): Promise<SerializedTask | undefined> {
    return this._loadJson<SerializedTask>(
      this._getStoragePath(StorageSubDirs.tasks, uuid),
    );
  }

  /**
   * Saves the task manifest to storage.
   * @param manifest - The manifest to save
   */
  public async saveManifest(manifest: TaskManifest): Promise<void> {
    await this._saveJson(
      this._getStoragePath(
        StorageSubDirs.manifest,
        FileBackedTaskStorage.manifestKey,
      ),
      manifest,
    );
  }

  /**
   * Loads the task manifest from storage.
   * @returns The loaded manifest, or undefined if not found
   */
  public async loadManifest(): Promise<TaskManifest | undefined> {
    return this._loadJson<TaskManifest>(
      this._getStoragePath(
        StorageSubDirs.manifest,
        FileBackedTaskStorage.manifestKey,
      ),
    );
  }
}
