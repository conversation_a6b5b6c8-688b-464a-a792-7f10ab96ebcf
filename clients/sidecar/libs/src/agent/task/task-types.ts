/**
 * @file task-types.ts
 * This file contains the type definitions for the task system.
 */

/**
 * Enum representing the possible states of a task.
 */
export enum TaskState {
  NOT_STARTED = "NOT_STARTED",
  IN_PROGRESS = "IN_PROGRESS",
  CANCELLED = "CANCELLED",
  COMPLETE = "COMPLETE",
}

/**
 * Enum representing who last updated a task.
 */
export enum TaskUpdatedBy {
  USER = "USER",
  AGENT = "AGENT",
}

/**
 * Interface representing a serialized task for storage.
 */
export interface SerializedTask {
  /** Unique identifier for the task */
  uuid: string;
  /** Name of the task */
  name: string;
  /** Description of the task */
  description: string;
  /** Current state of the task */
  state: TaskState;
  /** List of sub-task UUIDs */
  subTasks: string[];
  /** Timestamp of when the task was last updated */
  lastUpdated: number;
  /** Who last updated the task */
  lastUpdatedBy: TaskUpdatedBy;
}

/**
 * Interface representing a hydrated task for in-memory operations.
 * Extends SerializedTask with additional runtime properties.
 */
export interface HydratedTask extends SerializedTask {
  /** Actual sub-task objects instead of just UUIDs */
  subTasksData?: HydratedTask[];
}

/**
 * Interface for task storage operations.
 */
export interface TaskStorage {
  /**
   * Saves a task to storage.
   * @param uuid - The UUID of the task to save
   * @param task - The task to save
   */
  saveTask: (uuid: string, task: SerializedTask) => Promise<void>;

  /**
   * Loads a task from storage.
   * @param uuid - The UUID of the task to load
   * @returns The loaded task, or undefined if not found
   */
  loadTask: (uuid: string) => Promise<SerializedTask | undefined>;

  /**
   * Saves the task manifest to storage.
   * @param manifest - The manifest to save
   */
  saveManifest: (manifest: TaskManifest) => Promise<void>;

  /**
   * Loads the task manifest from storage.
   * @returns The loaded manifest, or undefined if not found
   */
  loadManifest: () => Promise<TaskManifest | undefined>;
}

/**
 * Interface representing metadata about a task.
 */
export interface TaskMetadata {
  /** Unique identifier for the task */
  uuid: string;
  /** Name of the task */
  name: string;
  /** Timestamp of when the task was last updated */
  lastUpdated: number;
  /** Current state of the task */
  state: TaskState;
  /** UUID of parent task if any */
  parentTask?: string;
}

/**
 * Interface representing the task manifest.
 * The manifest tracks all tasks in the system.
 */
export interface TaskManifest {
  /** Version number of the manifest format */
  version: number;
  /** Timestamp of the last manifest update */
  lastUpdated: number;
  /** Map of task UUIDs to their metadata */
  tasks: {
    [uuid: string]: TaskMetadata;
  };
}
