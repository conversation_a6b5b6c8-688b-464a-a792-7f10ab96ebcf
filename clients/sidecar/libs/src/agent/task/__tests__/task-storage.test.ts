/**
 * @file task-storage.test.ts
 * This file contains tests for the FileBackedTaskStorage class.
 */

import { FileBackedTaskStorage } from "../task-storage";
import { SerializedTask, TaskManifest } from "../task-types";
import {
  IPluginFileStore,
  setLibraryPluginFileStore,
  resetLibraryPluginFileStore,
} from "../../../client-interfaces/plugin-file-store";
import { createMockTask, createMockTaskManifest } from "./task-test-kit";

describe("FileBackedTaskStorage", () => {
  let storage: FileBackedTaskStorage;
  let mockPluginFileStore: jest.Mocked<IPluginFileStore>;

  beforeEach(() => {
    // Create a mock plugin file store
    mockPluginFileStore = {
      saveAsset: jest.fn().mockResolvedValue(undefined),
      loadAsset: jest.fn().mockResolvedValue(undefined),
      deleteAsset: jest.fn().mockResolvedValue(undefined),
      listAssets: jest.fn().mockResolvedValue([]),
      getAssetPath: jest.fn().mockImplementation((path: string) => {
        return Promise.resolve(`/mock/path/to/assets/${path}`);
      }),
    };

    // Set the mock plugin file store
    setLibraryPluginFileStore(mockPluginFileStore);

    // Create a storage instance
    storage = new FileBackedTaskStorage();
  });

  afterEach(() => {
    jest.clearAllMocks();
    resetLibraryPluginFileStore();
  });

  describe("saveTask", () => {
    it("should save a task to the plugin file store", async () => {
      // Create a task to save
      const task: SerializedTask = createMockTask("test-uuid", "Test Task");

      // Save the task
      await storage.saveTask("test-uuid", task);

      // Verify the task was saved
      expect(mockPluginFileStore.saveAsset).toHaveBeenCalledTimes(1);
      expect(mockPluginFileStore.saveAsset).toHaveBeenCalledWith(
        "task-storage/tasks/test-uuid",
        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
        expect.any(Uint8Array),
      );

      // Verify the content of the saved task
      const saveCall = mockPluginFileStore.saveAsset.mock.calls[0];
      const savedContent = new TextDecoder().decode(saveCall[1]);
      expect(JSON.parse(savedContent)).toEqual(task);
    });
  });

  describe("loadTask", () => {
    it("should load a task from the plugin file store", async () => {
      // Create a task to load
      const task: SerializedTask = createMockTask("test-uuid", "Test Task");

      // Mock the plugin file store to return the task
      mockPluginFileStore.loadAsset.mockResolvedValueOnce(
        new TextEncoder().encode(JSON.stringify(task)),
      );

      // Load the task
      const loadedTask = await storage.loadTask("test-uuid");

      // Verify the task was loaded
      expect(mockPluginFileStore.loadAsset).toHaveBeenCalledTimes(1);
      expect(mockPluginFileStore.loadAsset).toHaveBeenCalledWith(
        "task-storage/tasks/test-uuid",
      );

      // Verify the loaded task
      expect(loadedTask).toEqual(task);
    });

    it("should return undefined when the task doesn't exist", async () => {
      // Mock the plugin file store to return undefined
      mockPluginFileStore.loadAsset.mockResolvedValueOnce(undefined);

      // Load a non-existent task
      const loadedTask = await storage.loadTask("non-existent-uuid");

      // Verify the result
      expect(loadedTask).toBeUndefined();
    });

    it("should handle JSON parse errors", async () => {
      // Mock the plugin file store to return invalid JSON
      mockPluginFileStore.loadAsset.mockResolvedValueOnce(
        new TextEncoder().encode("invalid-json"),
      );

      // Load a task with invalid JSON
      const loadedTask = await storage.loadTask("test-uuid");

      // Verify the result
      expect(loadedTask).toBeUndefined();
    });
  });

  describe("saveManifest", () => {
    it("should save a manifest to the plugin file store", async () => {
      // Create a manifest to save
      const manifest: TaskManifest = createMockTaskManifest();

      // Save the manifest
      await storage.saveManifest(manifest);

      // Verify the manifest was saved
      expect(mockPluginFileStore.saveAsset).toHaveBeenCalledTimes(1);
      expect(mockPluginFileStore.saveAsset).toHaveBeenCalledWith(
        "task-storage/manifest/manifest",
        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
        expect.any(Uint8Array),
      );

      // Verify the content of the saved manifest
      const saveCall = mockPluginFileStore.saveAsset.mock.calls[0];
      const savedContent = new TextDecoder().decode(saveCall[1]);
      expect(JSON.parse(savedContent)).toEqual(manifest);
    });
  });

  describe("loadManifest", () => {
    it("should load a manifest from the plugin file store", async () => {
      // Create a manifest to load
      const manifest: TaskManifest = createMockTaskManifest();

      // Mock the plugin file store to return the manifest
      mockPluginFileStore.loadAsset.mockResolvedValueOnce(
        new TextEncoder().encode(JSON.stringify(manifest)),
      );

      // Load the manifest
      const loadedManifest = await storage.loadManifest();

      // Verify the manifest was loaded
      expect(mockPluginFileStore.loadAsset).toHaveBeenCalledTimes(1);
      expect(mockPluginFileStore.loadAsset).toHaveBeenCalledWith(
        "task-storage/manifest/manifest",
      );

      // Verify the loaded manifest
      expect(loadedManifest).toEqual(manifest);
    });

    it("should return undefined when the manifest doesn't exist", async () => {
      // Mock the plugin file store to return undefined
      mockPluginFileStore.loadAsset.mockResolvedValueOnce(undefined);

      // Load a non-existent manifest
      const loadedManifest = await storage.loadManifest();

      // Verify the result
      expect(loadedManifest).toBeUndefined();
    });

    it("should handle JSON parse errors", async () => {
      // Mock the plugin file store to return invalid JSON
      mockPluginFileStore.loadAsset.mockResolvedValueOnce(
        new TextEncoder().encode("invalid-json"),
      );

      // Load a manifest with invalid JSON
      const loadedManifest = await storage.loadManifest();

      // Verify the result
      expect(loadedManifest).toBeUndefined();
    });
  });

  describe("_getStoragePath", () => {
    it("should generate the correct storage path", () => {
      // Call the private method using type assertion
      const path = storage["_getStoragePath"]("tasks", "test-uuid");

      // Verify the path
      expect(path).toBe("task-storage/tasks/test-uuid");
    });
  });
});
