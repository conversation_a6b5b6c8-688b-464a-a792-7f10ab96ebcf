/**
 * @file task-utils.test.ts
 * This file contains tests for the task utility functions.
 */

import {
  TaskFactory,
  TaskQueries,
  parseMarkdownToTaskTree,
  getMarkdownRepresentation,
  findTaskInTree,
  diffTaskTrees,
  toFlatTask,
  parseTaskDiffMarkdown,
  getTaskDiffCounts,
  extractTaskDiffMarkdown,
  parseTaskDiffFromFullText,
  taskDiffToMarkdown,
  testOnlyFunctions,
} from "../task-utils";
import {
  SerializedTask,
  TaskState,
  TaskUpdatedBy,
  HydratedTask,
} from "../task-types";
import { createMockTask } from "./task-test-kit";

// Mock UUID generation for deterministic tests
jest.mock("uuid", () => ({
  v4: jest.fn().mockReturnValue("test-uuid"),
}));

crypto.randomUUID = jest.fn().mockReturnValue("test-uuid");

describe("TaskFactory", () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("createTask", () => {
    it("should create a task with the given properties", () => {
      // Create a task with all properties specified
      const task = TaskFactory.createTask(
        "Test Task",
        "Test Description",
        TaskState.IN_PROGRESS,
        ["sub-task-1", "sub-task-2"],
      );

      // Verify the task properties
      expect(task.uuid).toBe("test-uuid");
      expect(task.name).toBe("Test Task");
      expect(task.description).toBe("Test Description");
      expect(task.state).toBe(TaskState.IN_PROGRESS);
      expect(task.subTasks).toEqual(["sub-task-1", "sub-task-2"]);
      expect(task.lastUpdatedBy).toBe(TaskUpdatedBy.USER);
      expect(task.lastUpdated).toBeDefined();
    });

    it("should create a task with default properties", () => {
      // Create a task with minimal properties
      const task = TaskFactory.createTask("Test Task", "Test Description");

      // Verify the default properties
      expect(task.uuid).toBe("test-uuid");
      expect(task.name).toBe("Test Task");
      expect(task.description).toBe("Test Description");
      expect(task.state).toBe(TaskState.NOT_STARTED);
      expect(task.subTasks).toEqual([]);
      expect(task.lastUpdatedBy).toBe(TaskUpdatedBy.USER);
      expect(task.lastUpdated).toBeDefined();
    });
  });

  describe("createSubTask", () => {
    it("should create a sub-task and update the parent task", () => {
      // Create a parent task
      const parentTask = createMockTask(
        "parent-uuid",
        "Parent Task",
        "Parent Description",
        TaskState.NOT_STARTED,
        ["existing-sub-task"],
      );

      // Create a sub-task
      const [updatedParentTask, subTask] = TaskFactory.createSubTask(
        parentTask,
        "Sub Task",
        "Sub Description",
        TaskState.IN_PROGRESS,
      );

      // Verify the sub-task
      expect(subTask.uuid).toBe("test-uuid");
      expect(subTask.name).toBe("Sub Task");
      expect(subTask.description).toBe("Sub Description");
      expect(subTask.state).toBe(TaskState.IN_PROGRESS);
      expect(subTask.subTasks).toEqual([]);
      expect(subTask.lastUpdatedBy).toBe(TaskUpdatedBy.USER);

      // Verify the updated parent task
      expect(updatedParentTask.uuid).toBe("parent-uuid");
      expect(updatedParentTask.subTasks).toEqual([
        "existing-sub-task",
        "test-uuid",
      ]);
      expect(updatedParentTask.lastUpdatedBy).toBe(TaskUpdatedBy.USER);
      expect(updatedParentTask.lastUpdated).toBeGreaterThanOrEqual(
        parentTask.lastUpdated,
      );
    });

    it("should create a sub-task with default state", () => {
      // Create a parent task
      const parentTask = createMockTask("parent-uuid", "Parent Task");

      // Create a sub-task with default state
      const [, subTask] = TaskFactory.createSubTask(
        parentTask,
        "Sub Task",
        "Sub Description",
      );

      // Verify the sub-task state
      expect(subTask.state).toBe(TaskState.NOT_STARTED);
    });
  });
});

describe("TaskQueries", () => {
  describe("filterByState", () => {
    it("should filter tasks by a single state", () => {
      // Create tasks with different states
      const tasks: SerializedTask[] = [
        createMockTask(
          "task1",
          "Task 1",
          "Description 1",
          TaskState.NOT_STARTED,
        ),
        createMockTask(
          "task2",
          "Task 2",
          "Description 2",
          TaskState.IN_PROGRESS,
        ),
        createMockTask("task3", "Task 3", "Description 3", TaskState.CANCELLED),
        createMockTask("task4", "Task 4", "Description 4", TaskState.COMPLETE),
        createMockTask("task5", "Task 5", "Description 5", TaskState.COMPLETE),
      ];

      // Filter by NOT_STARTED
      const notStartedTasks = TaskQueries.filterByState(tasks, [
        TaskState.NOT_STARTED,
      ]);
      expect(notStartedTasks).toHaveLength(1);
      expect(notStartedTasks[0].uuid).toBe("task1");

      // Filter by IN_PROGRESS
      const inProgressTasks = TaskQueries.filterByState(tasks, [
        TaskState.IN_PROGRESS,
      ]);
      expect(inProgressTasks).toHaveLength(1);
      expect(inProgressTasks[0].uuid).toBe("task2");

      // Filter by CANCELLED
      const cancelledTasks = TaskQueries.filterByState(tasks, [
        TaskState.CANCELLED,
      ]);
      expect(cancelledTasks).toHaveLength(1);
      expect(cancelledTasks[0].uuid).toBe("task3");

      // Filter by COMPLETE
      const completeTasks = TaskQueries.filterByState(tasks, [
        TaskState.COMPLETE,
      ]);
      expect(completeTasks).toHaveLength(2);
      expect(completeTasks.map((task) => task.uuid).sort()).toEqual([
        "task4",
        "task5",
      ]);
    });

    it("should filter tasks by multiple states", () => {
      // Create tasks with different states
      const tasks: SerializedTask[] = [
        createMockTask(
          "task1",
          "Task 1",
          "Description 1",
          TaskState.NOT_STARTED,
        ),
        createMockTask(
          "task2",
          "Task 2",
          "Description 2",
          TaskState.IN_PROGRESS,
        ),
        createMockTask("task3", "Task 3", "Description 3", TaskState.CANCELLED),
        createMockTask("task4", "Task 4", "Description 4", TaskState.COMPLETE),
        createMockTask("task5", "Task 5", "Description 5", TaskState.COMPLETE),
      ];

      // Filter by NOT_STARTED and IN_PROGRESS
      const activeTasks = TaskQueries.filterByState(tasks, [
        TaskState.NOT_STARTED,
        TaskState.IN_PROGRESS,
      ]);
      expect(activeTasks).toHaveLength(2);
      expect(activeTasks.map((task) => task.uuid)).toEqual(["task1", "task2"]);

      // Filter by IN_PROGRESS and COMPLETE
      const inProgressAndCompleteTasks = TaskQueries.filterByState(tasks, [
        TaskState.IN_PROGRESS,
        TaskState.COMPLETE,
      ]);
      expect(inProgressAndCompleteTasks).toHaveLength(3);
      expect(inProgressAndCompleteTasks.map((task) => task.uuid)).toEqual([
        "task2",
        "task4",
        "task5",
      ]);

      // Filter by CANCELLED and COMPLETE
      const inactiveTasks = TaskQueries.filterByState(tasks, [
        TaskState.CANCELLED,
        TaskState.COMPLETE,
      ]);
      expect(inactiveTasks).toHaveLength(3);
      expect(inactiveTasks.map((task) => task.uuid).sort()).toEqual([
        "task3",
        "task4",
        "task5",
      ]);
    });

    it("should return an empty array when no tasks match the filter", () => {
      // Create tasks with different states
      const tasks: SerializedTask[] = [
        createMockTask(
          "task1",
          "Task 1",
          "Description 1",
          TaskState.NOT_STARTED,
        ),
        createMockTask(
          "task2",
          "Task 2",
          "Description 2",
          TaskState.IN_PROGRESS,
        ),
      ];

      // Filter by CANCELLED and COMPLETE
      const inactiveTasks = TaskQueries.filterByState(tasks, [
        TaskState.CANCELLED,
        TaskState.COMPLETE,
      ]);
      expect(inactiveTasks).toHaveLength(0);
    });
  });

  describe("getActiveTasks", () => {
    it("should return tasks with NOT_STARTED or IN_PROGRESS state", () => {
      // Create tasks with different states
      const tasks: SerializedTask[] = [
        createMockTask(
          "task1",
          "Task 1",
          "Description 1",
          TaskState.NOT_STARTED,
        ),
        createMockTask(
          "task2",
          "Task 2",
          "Description 2",
          TaskState.IN_PROGRESS,
        ),
        createMockTask("task3", "Task 3", "Description 3", TaskState.CANCELLED),
        createMockTask("task4", "Task 4", "Description 4", TaskState.COMPLETE),
      ];

      // Get active tasks
      const activeTasks = TaskQueries.getActiveTasks(tasks);
      expect(activeTasks).toHaveLength(2);
      expect(activeTasks.map((task) => task.uuid).sort()).toEqual([
        "task1",
        "task2",
      ]);
    });
  });

  describe("getCompletedTasks", () => {
    it("should return tasks with COMPLETE state", () => {
      // Create tasks with different states
      const tasks: SerializedTask[] = [
        createMockTask(
          "task1",
          "Task 1",
          "Description 1",
          TaskState.NOT_STARTED,
        ),
        createMockTask(
          "task2",
          "Task 2",
          "Description 2",
          TaskState.IN_PROGRESS,
        ),
        createMockTask("task3", "Task 3", "Description 3", TaskState.CANCELLED),
        createMockTask("task4", "Task 4", "Description 4", TaskState.COMPLETE),
      ];

      // Get completed tasks
      const completedTasks = TaskQueries.getCompletedTasks(tasks);
      expect(completedTasks).toHaveLength(1);
      expect(completedTasks[0].uuid).toBe("task4");
    });
  });

  describe("getCancelledTasks", () => {
    it("should return tasks with CANCELLED state", () => {
      // Create tasks with different states
      const tasks: SerializedTask[] = [
        createMockTask(
          "task1",
          "Task 1",
          "Description 1",
          TaskState.NOT_STARTED,
        ),
        createMockTask(
          "task2",
          "Task 2",
          "Description 2",
          TaskState.IN_PROGRESS,
        ),
        createMockTask("task3", "Task 3", "Description 3", TaskState.CANCELLED),
        createMockTask("task4", "Task 4", "Description 4", TaskState.COMPLETE),
      ];

      // Get cancelled tasks
      const cancelledTasks = TaskQueries.getCancelledTasks(tasks);
      expect(cancelledTasks).toHaveLength(1);
      expect(cancelledTasks[0].uuid).toBe("task3");
    });
  });

  describe("filterByTimeRange", () => {
    it("should filter tasks by minimum time", () => {
      const now = Date.now();
      const tasks: SerializedTask[] = [
        createMockTask(
          "task1",
          "Task 1",
          "Description 1",
          TaskState.NOT_STARTED,
          [],
          now - 1000,
        ),
        createMockTask(
          "task2",
          "Task 2",
          "Description 2",
          TaskState.IN_PROGRESS,
          [],
          now - 500,
        ),
        createMockTask(
          "task3",
          "Task 3",
          "Description 3",
          TaskState.CANCELLED,
          [],
          now,
        ),
      ];

      // Filter by minimum time
      const recentTasks = TaskQueries.filterByTimeRange(tasks, now - 600);
      expect(recentTasks).toHaveLength(2);
      expect(recentTasks.map((task) => task.uuid)).toEqual(["task2", "task3"]);
    });

    it("should filter tasks by maximum time", () => {
      const now = Date.now();
      const tasks: SerializedTask[] = [
        createMockTask(
          "task1",
          "Task 1",
          "Description 1",
          TaskState.NOT_STARTED,
          [],
          now - 1000,
        ),
        createMockTask(
          "task2",
          "Task 2",
          "Description 2",
          TaskState.IN_PROGRESS,
          [],
          now - 500,
        ),
        createMockTask(
          "task3",
          "Task 3",
          "Description 3",
          TaskState.CANCELLED,
          [],
          now,
        ),
      ];

      // Filter by maximum time
      const oldTasks = TaskQueries.filterByTimeRange(
        tasks,
        undefined,
        now - 600,
      );
      expect(oldTasks).toHaveLength(1);
      expect(oldTasks[0].uuid).toBe("task1");
    });

    it("should filter tasks by time range", () => {
      const now = Date.now();
      const tasks: SerializedTask[] = [
        createMockTask(
          "task1",
          "Task 1",
          "Description 1",
          TaskState.NOT_STARTED,
          [],
          now - 1000,
        ),
        createMockTask(
          "task2",
          "Task 2",
          "Description 2",
          TaskState.IN_PROGRESS,
          [],
          now - 500,
        ),
        createMockTask(
          "task3",
          "Task 3",
          "Description 3",
          TaskState.CANCELLED,
          [],
          now,
        ),
      ];

      // Filter by time range
      const midTasks = TaskQueries.filterByTimeRange(
        tasks,
        now - 1100,
        now - 100,
      );
      expect(midTasks).toHaveLength(2);
      expect(midTasks.map((task) => task.uuid)).toEqual(["task1", "task2"]);
    });
  });

  describe("filterByUpdatedBy", () => {
    it("should filter tasks by who updated them", () => {
      const tasks: SerializedTask[] = [
        createMockTask(
          "task1",
          "Task 1",
          "Description 1",
          TaskState.NOT_STARTED,
          [],
          Date.now(),
          TaskUpdatedBy.USER,
        ),
        createMockTask(
          "task2",
          "Task 2",
          "Description 2",
          TaskState.IN_PROGRESS,
          [],
          Date.now(),
          TaskUpdatedBy.AGENT,
        ),
        createMockTask(
          "task3",
          "Task 3",
          "Description 3",
          TaskState.CANCELLED,
          [],
          Date.now(),
          TaskUpdatedBy.USER,
        ),
      ];

      // Filter by USER
      const userTasks = TaskQueries.filterByUpdatedBy(
        tasks,
        TaskUpdatedBy.USER,
      );
      expect(userTasks).toHaveLength(2);
      expect(userTasks.map((task) => task.uuid)).toEqual(["task1", "task3"]);

      // Filter by AGENT
      const agentTasks = TaskQueries.filterByUpdatedBy(
        tasks,
        TaskUpdatedBy.AGENT,
      );
      expect(agentTasks).toHaveLength(1);
      expect(agentTasks[0].uuid).toBe("task2");
    });
  });
});

describe("parseMarkdownToTaskTree", () => {
  const originalDateNow = Date.now;
  const mockTimestamp = 1234567890;

  beforeEach(() => (Date.now = jest.fn().mockReturnValue(mockTimestamp)));
  afterEach(() => (Date.now = originalDateNow));

  it("should parse a simple task", () => {
    const markdown =
      "[ ] UUID:task-1 NAME:Root Task DESCRIPTION:This is the root task";
    const task = parseMarkdownToTaskTree(markdown);

    expect(task).toEqual({
      uuid: "task-1",
      name: "Root Task",
      description: "This is the root task",
      state: TaskState.NOT_STARTED,
      subTasks: [],
      lastUpdated: mockTimestamp,
      lastUpdatedBy: TaskUpdatedBy.USER,
    });
  });

  it("should parse a task with subtasks", () => {
    const markdown = [
      "[ ] UUID:task-1 NAME:Root Task DESCRIPTION:This is the root task",
      "-[x] UUID:task-2 NAME:Sub Task 1 DESCRIPTION:This is the first sub task",
      "-[-] UUID:task-3 NAME:Sub Task 2 DESCRIPTION:This is the second sub task",
    ].join("\n");

    const task = parseMarkdownToTaskTree(markdown);

    expect(task).toEqual({
      uuid: "task-1",
      name: "Root Task",
      description: "This is the root task",
      state: TaskState.NOT_STARTED,
      subTasks: ["task-2", "task-3"],
      lastUpdated: mockTimestamp,
      lastUpdatedBy: TaskUpdatedBy.USER,
      subTasksData: [
        {
          uuid: "task-2",
          name: "Sub Task 1",
          description: "This is the first sub task",
          state: TaskState.COMPLETE,
          subTasks: [],
          lastUpdated: mockTimestamp,
          lastUpdatedBy: TaskUpdatedBy.USER,
        },
        {
          uuid: "task-3",
          name: "Sub Task 2",
          description: "This is the second sub task",
          state: TaskState.CANCELLED,
          subTasks: [],
          lastUpdated: mockTimestamp,
          lastUpdatedBy: TaskUpdatedBy.USER,
        },
      ],
    });
  });

  it("should correctly parse a task tree with all task states", () => {
    const markdown = [
      "[ ] UUID:root-task NAME:Root Task DESCRIPTION:This is the root task",
      "-[ ] UUID:not-started-task NAME:Not Started Task DESCRIPTION:This task is not started",
      "-[/] UUID:in-progress-task NAME:In Progress Task DESCRIPTION:This task is in progress",
      "-[x] UUID:completed-task NAME:Completed Task DESCRIPTION:This task is completed",
      "-[-] UUID:cancelled-task NAME:Cancelled Task DESCRIPTION:This task is cancelled",
    ].join("\n");

    const task = parseMarkdownToTaskTree(markdown);

    // Verify the root task
    expect(task.uuid).toBe("root-task");
    expect(task.name).toBe("Root Task");
    expect(task.state).toBe(TaskState.NOT_STARTED);
    expect(task.subTasks).toHaveLength(4);

    // Verify the subtasks
    const notStartedTask = task.subTasksData?.find(
      (t) => t.uuid === "not-started-task",
    );
    const inProgressTask = task.subTasksData?.find(
      (t) => t.uuid === "in-progress-task",
    );
    const completedTask = task.subTasksData?.find(
      (t) => t.uuid === "completed-task",
    );
    const cancelledTask = task.subTasksData?.find(
      (t) => t.uuid === "cancelled-task",
    );

    expect(notStartedTask?.state).toBe(TaskState.NOT_STARTED);
    expect(inProgressTask?.state).toBe(TaskState.IN_PROGRESS);
    expect(completedTask?.state).toBe(TaskState.COMPLETE);
    expect(cancelledTask?.state).toBe(TaskState.CANCELLED);
  });

  it("should parse a task with nested subtasks", () => {
    const markdown = [
      "[ ] UUID:task-1 NAME:Root Task DESCRIPTION:This is the root task",
      "-[x] UUID:task-2 NAME:Sub Task 1 DESCRIPTION:This is the first sub task",
      "--[ ] UUID:task-4 NAME:Sub Sub Task DESCRIPTION:This is a nested sub task",
      "-[-] UUID:task-3 NAME:Sub Task 2 DESCRIPTION:This is the second sub task",
    ].join("\n");

    const task = parseMarkdownToTaskTree(markdown);

    expect(task).toEqual({
      uuid: "task-1",
      name: "Root Task",
      description: "This is the root task",
      state: TaskState.NOT_STARTED,
      subTasks: ["task-2", "task-3"],
      lastUpdated: mockTimestamp,
      lastUpdatedBy: TaskUpdatedBy.USER,
      subTasksData: [
        {
          uuid: "task-2",
          name: "Sub Task 1",
          description: "This is the first sub task",
          state: TaskState.COMPLETE,
          subTasks: ["task-4"],
          lastUpdated: mockTimestamp,
          lastUpdatedBy: TaskUpdatedBy.USER,
          subTasksData: [
            {
              uuid: "task-4",
              name: "Sub Sub Task",
              description: "This is a nested sub task",
              state: TaskState.NOT_STARTED,
              subTasks: [],
              lastUpdated: mockTimestamp,
              lastUpdatedBy: TaskUpdatedBy.USER,
            },
          ],
        },
        {
          uuid: "task-3",
          name: "Sub Task 2",
          description: "This is the second sub task",
          state: TaskState.CANCELLED,
          subTasks: [],
          lastUpdated: mockTimestamp,
          lastUpdatedBy: TaskUpdatedBy.USER,
        },
      ],
    });
  });

  it("should throw an error for empty markdown", () => {
    expect(() => parseMarkdownToTaskTree("")).toThrow("Empty markdown");
  });

  it("should throw an error for invalid task line", () => {
    expect(() => parseMarkdownToTaskTree("Invalid task line")).toThrow(
      "No root task found",
    );
  });

  it("should throw an error for invalid task hierarchy", () => {
    const markdown = [
      "[ ] UUID:task-1 NAME:Root Task DESCRIPTION:This is the root task",
      "---[ ] UUID:task-2 NAME:Sub Task DESCRIPTION:This has no parent",
    ].join("\n");

    expect(() => parseMarkdownToTaskTree(markdown)).toThrow("has no parent");
  });

  it("should handle placeholder UUIDs correctly", () => {
    const markdown = [
      "[ ] UUID:NEW_UUID_123 NAME:Root Task DESCRIPTION:This is the root task",
    ].join("\n");

    const task = parseMarkdownToTaskTree(markdown);
    expect(task.uuid).toBe("NEW_UUID_123");
  });

  it("should handle a task with multiple levels of nesting", () => {
    const markdown = [
      "[ ] UUID:task-1 NAME:Root Task DESCRIPTION:This is the root task",
      "-[ ] UUID:task-2 NAME:Level 1 Task DESCRIPTION:This is a level 1 task",
      "--[ ] UUID:task-3 NAME:Level 2 Task DESCRIPTION:This is a level 2 task",
      "---[ ] UUID:task-4 NAME:Level 3 Task DESCRIPTION:This is a level 3 task",
      "----[ ] UUID:task-5 NAME:Level 4 Task DESCRIPTION:This is a level 4 task",
      "-[ ] UUID:task-6 NAME:Another Level 1 DESCRIPTION:This is another level 1 task",
    ].join("\n");

    const task = parseMarkdownToTaskTree(markdown);

    expect(task.uuid).toBe("task-1");
    expect(task.subTasks).toEqual(["task-2", "task-6"]);

    const level1Task = task.subTasksData?.[0];
    expect(level1Task?.uuid).toBe("task-2");
    expect(level1Task?.subTasks).toEqual(["task-3"]);

    const level2Task = level1Task?.subTasksData?.[0];
    expect(level2Task?.uuid).toBe("task-3");
    expect(level2Task?.subTasks).toEqual(["task-4"]);

    const level3Task = level2Task?.subTasksData?.[0];
    expect(level3Task?.uuid).toBe("task-4");
    expect(level3Task?.subTasks).toEqual(["task-5"]);

    const level4Task = level3Task?.subTasksData?.[0];
    expect(level4Task?.uuid).toBe("task-5");
    expect(level4Task?.subTasks).toEqual([]);

    const anotherLevel1Task = task.subTasksData?.[1];
    expect(anotherLevel1Task?.uuid).toBe("task-6");
    expect(anotherLevel1Task?.subTasks).toEqual([]);
  });
});

describe("diffTaskTrees", () => {
  it("should identify created tasks", () => {
    // Create old tree
    const oldRoot: HydratedTask = {
      uuid: "root",
      name: "Root Task",
      description: "Root task description",
      state: TaskState.NOT_STARTED,
      subTasks: ["child1"],
      lastUpdated: 1000,
      lastUpdatedBy: TaskUpdatedBy.USER,
      subTasksData: [
        {
          uuid: "child1",
          name: "Child 1",
          description: "Child 1 description",
          state: TaskState.NOT_STARTED,
          subTasks: [],
          lastUpdated: 1000,
          lastUpdatedBy: TaskUpdatedBy.USER,
        },
      ],
    };

    // Create new tree with an additional task
    const newRoot: HydratedTask = {
      uuid: "root",
      name: "Root Task",
      description: "Root task description",
      state: TaskState.NOT_STARTED,
      subTasks: ["child1", "child2"],
      lastUpdated: 1000,
      lastUpdatedBy: TaskUpdatedBy.USER,
      subTasksData: [
        {
          uuid: "child1",
          name: "Child 1",
          description: "Child 1 description",
          state: TaskState.NOT_STARTED,
          subTasks: [],
          lastUpdated: 1000,
          lastUpdatedBy: TaskUpdatedBy.USER,
        },
        {
          uuid: "child2",
          name: "Child 2",
          description: "Child 2 description",
          state: TaskState.NOT_STARTED,
          subTasks: [],
          lastUpdated: 2000,
          lastUpdatedBy: TaskUpdatedBy.USER,
        },
      ],
    };

    const diff = diffTaskTrees(oldRoot, newRoot);

    // Verify created tasks
    expect(diff.created).toHaveLength(1);
    expect(diff.created[0].uuid).toBe("child2");

    // Verify updated tasks - only root should be updated since its subTasks array changed
    expect(diff.updated).toHaveLength(1);
    expect(diff.updated.map((t) => t.uuid)).toEqual(["root"]);

    // Verify deleted tasks
    expect(diff.deleted).toHaveLength(0);
  });

  it("should identify updated tasks", () => {
    // Create old tree
    const oldRoot: HydratedTask = {
      uuid: "root",
      name: "Root Task",
      description: "Root task description",
      state: TaskState.NOT_STARTED,
      subTasks: ["child1"],
      lastUpdated: 1000,
      lastUpdatedBy: TaskUpdatedBy.USER,
      subTasksData: [
        {
          uuid: "child1",
          name: "Child 1",
          description: "Child 1 description",
          state: TaskState.NOT_STARTED,
          subTasks: [],
          lastUpdated: 1000,
          lastUpdatedBy: TaskUpdatedBy.USER,
        },
      ],
    };

    // Create new tree with updated task state
    const newRoot: HydratedTask = {
      uuid: "root",
      name: "Root Task",
      description: "Root task description",
      state: TaskState.NOT_STARTED,
      subTasks: ["child1"],
      lastUpdated: 1000,
      lastUpdatedBy: TaskUpdatedBy.USER,
      subTasksData: [
        {
          uuid: "child1",
          name: "Child 1",
          description: "Child 1 description",
          state: TaskState.COMPLETE, // Changed state
          subTasks: [],
          lastUpdated: 2000, // Updated timestamp
          lastUpdatedBy: TaskUpdatedBy.USER,
        },
      ],
    };

    const diff = diffTaskTrees(oldRoot, newRoot);

    // Verify created tasks
    expect(diff.created).toHaveLength(0);

    // Verify updated tasks - only child1 should be updated since its state changed
    expect(diff.updated).toHaveLength(1);
    expect(diff.updated.map((t) => t.uuid)).toEqual(["child1"]);
    expect(diff.updated.find((t) => t.uuid === "child1")?.state).toBe(
      TaskState.COMPLETE,
    );

    // Verify deleted tasks
    expect(diff.deleted).toHaveLength(0);
  });

  it("should identify deleted tasks", () => {
    // Create old tree
    const oldRoot: HydratedTask = {
      uuid: "root",
      name: "Root Task",
      description: "Root task description",
      state: TaskState.NOT_STARTED,
      subTasks: ["child1", "child2"],
      lastUpdated: 1000,
      lastUpdatedBy: TaskUpdatedBy.USER,
      subTasksData: [
        {
          uuid: "child1",
          name: "Child 1",
          description: "Child 1 description",
          state: TaskState.NOT_STARTED,
          subTasks: [],
          lastUpdated: 1000,
          lastUpdatedBy: TaskUpdatedBy.USER,
        },
        {
          uuid: "child2",
          name: "Child 2",
          description: "Child 2 description",
          state: TaskState.NOT_STARTED,
          subTasks: [],
          lastUpdated: 1000,
          lastUpdatedBy: TaskUpdatedBy.USER,
        },
      ],
    };

    // Create new tree with a task removed
    const newRoot: HydratedTask = {
      uuid: "root",
      name: "Root Task",
      description: "Root task description",
      state: TaskState.NOT_STARTED,
      subTasks: ["child1"],
      lastUpdated: 1000,
      lastUpdatedBy: TaskUpdatedBy.USER,
      subTasksData: [
        {
          uuid: "child1",
          name: "Child 1",
          description: "Child 1 description",
          state: TaskState.NOT_STARTED,
          subTasks: [],
          lastUpdated: 1000,
          lastUpdatedBy: TaskUpdatedBy.USER,
        },
      ],
    };

    const diff = diffTaskTrees(oldRoot, newRoot);

    // Verify created tasks
    expect(diff.created).toHaveLength(0);

    // Verify updated tasks - only root should be updated since its subTasks array changed
    expect(diff.updated).toHaveLength(1);
    expect(diff.updated.map((t) => t.uuid)).toEqual(["root"]);

    // Verify deleted tasks
    expect(diff.deleted).toHaveLength(1);
    expect(diff.deleted[0].uuid).toBe("child2");
  });

  it("should handle complex task trees with nested changes", () => {
    // Create old tree with nested structure
    const oldRoot: HydratedTask = {
      uuid: "root",
      name: "Root Task",
      description: "Root task description",
      state: TaskState.NOT_STARTED,
      subTasks: ["child1"],
      lastUpdated: 1000,
      lastUpdatedBy: TaskUpdatedBy.USER,
      subTasksData: [
        {
          uuid: "child1",
          name: "Child 1",
          description: "Child 1 description",
          state: TaskState.NOT_STARTED,
          subTasks: ["grandchild1"],
          lastUpdated: 1000,
          lastUpdatedBy: TaskUpdatedBy.USER,
          subTasksData: [
            {
              uuid: "grandchild1",
              name: "Grandchild 1",
              description: "Grandchild 1 description",
              state: TaskState.NOT_STARTED,
              subTasks: [],
              lastUpdated: 1000,
              lastUpdatedBy: TaskUpdatedBy.USER,
            },
          ],
        },
      ],
    };

    // Create new tree with changes at multiple levels
    const newRoot: HydratedTask = {
      uuid: "root",
      name: "Root Task Updated", // Changed name
      description: "Root task description",
      state: TaskState.NOT_STARTED,
      subTasks: ["child1", "child2"], // Added child2
      lastUpdated: 2000, // Updated timestamp
      lastUpdatedBy: TaskUpdatedBy.USER,
      subTasksData: [
        {
          uuid: "child1",
          name: "Child 1",
          description: "Child 1 description",
          state: TaskState.COMPLETE, // Changed state
          subTasks: [], // Removed grandchild1
          lastUpdated: 2000, // Updated timestamp
          lastUpdatedBy: TaskUpdatedBy.USER,
        },
        {
          uuid: "child2", // New task
          name: "Child 2",
          description: "Child 2 description",
          state: TaskState.NOT_STARTED,
          subTasks: [],
          lastUpdated: 2000,
          lastUpdatedBy: TaskUpdatedBy.USER,
        },
      ],
    };

    const diff = diffTaskTrees(oldRoot, newRoot);

    // Verify created tasks
    expect(diff.created).toHaveLength(1);
    expect(diff.created[0].uuid).toBe("child2");

    // Verify updated tasks
    expect(diff.updated).toHaveLength(2);
    expect(diff.updated.map((t) => t.uuid).sort()).toEqual(["child1", "root"]);
    expect(diff.updated.find((t) => t.uuid === "root")?.name).toBe(
      "Root Task Updated",
    );
    expect(diff.updated.find((t) => t.uuid === "child1")?.state).toBe(
      TaskState.COMPLETE,
    );

    // Verify deleted tasks
    expect(diff.deleted).toHaveLength(1);
    expect(diff.deleted[0].uuid).toBe("grandchild1");
  });

  it("should handle completely different trees", () => {
    // Create old tree
    const oldRoot: HydratedTask = {
      uuid: "oldRoot",
      name: "Old Root",
      description: "Old root description",
      state: TaskState.NOT_STARTED,
      subTasks: ["oldChild"],
      lastUpdated: 1000,
      lastUpdatedBy: TaskUpdatedBy.USER,
      subTasksData: [
        {
          uuid: "oldChild",
          name: "Old Child",
          description: "Old child description",
          state: TaskState.NOT_STARTED,
          subTasks: [],
          lastUpdated: 1000,
          lastUpdatedBy: TaskUpdatedBy.USER,
        },
      ],
    };

    // Create completely new tree
    const newRoot: HydratedTask = {
      uuid: "newRoot",
      name: "New Root",
      description: "New root description",
      state: TaskState.NOT_STARTED,
      subTasks: ["newChild"],
      lastUpdated: 2000,
      lastUpdatedBy: TaskUpdatedBy.USER,
      subTasksData: [
        {
          uuid: "newChild",
          name: "New Child",
          description: "New child description",
          state: TaskState.NOT_STARTED,
          subTasks: [],
          lastUpdated: 2000,
          lastUpdatedBy: TaskUpdatedBy.USER,
        },
      ],
    };

    const diff = diffTaskTrees(oldRoot, newRoot);

    // Verify created tasks
    expect(diff.created).toHaveLength(2);
    expect(diff.created.map((t) => t.uuid).sort()).toEqual([
      "newChild",
      "newRoot",
    ]);

    // Verify updated tasks
    expect(diff.updated).toHaveLength(0);

    // Verify deleted tasks
    expect(diff.deleted).toHaveLength(2);
    expect(diff.deleted.map((t) => t.uuid).sort()).toEqual([
      "oldChild",
      "oldRoot",
    ]);
  });

  it("should correctly handle the specific task structure with subtasks and state changes", () => {
    // Mock Date.now() for deterministic tests
    const originalDateNow = Date.now;
    const mockTimestamp = 1234567890;
    Date.now = jest.fn().mockReturnValue(mockTimestamp);

    // Create the old tree structure
    const oldMarkdown = [
      "[ ] UUID:ddfded92-d526-40d8-8591-6dfe96e1aca4 NAME:Conversation: New Chat DESCRIPTION:Root task for conversation 5263ff63-0adf-4474-a9de-be51ede6798b",
      "-[ ] UUID:20f968e3-32a5-4040-b270-6ac16bc162d5 NAME:New subtask DESCRIPTION:New subtask description",
    ].join("\n");

    // Create the new tree structure
    const newMarkdown = [
      "[ ] UUID:ddfded92-d526-40d8-8591-6dfe96e1aca4 NAME:Conversation: New Chat DESCRIPTION:Root task for conversation 5263ff63-0adf-4474-a9de-be51ede6798b",
      "-[x] UUID:20f968e3-32a5-4040-b270-6ac16bc162d5 NAME:New subtask DESCRIPTION:New subtask description",
      "-[ ] UUID:new-task-1 NAME:Increase test coverage for task-utils.ts DESCRIPTION:Add tests for functions with limited or no coverage",
      "--[ ] UUID:new-task-2 NAME:Add tests for findTaskInTree DESCRIPTION:Test finding tasks at different levels of nesting and edge cases",
      "--[ ] UUID:new-task-3 NAME:Add tests for toFlatTask DESCRIPTION:Test flattening task trees of various depths and structures",
      "--[ ] UUID:new-task-4 NAME:Add tests for getMarkdownRepresentation DESCRIPTION:Test generating markdown for tasks with different states and nesting levels",
      "--[ ] UUID:new-task-5 NAME:Fix and test diffTaskTrees bug DESCRIPTION:Fix the bug in uuidToNew map creation and add more comprehensive tests",
    ].join("\n");

    // Parse the markdown into task trees
    const oldTree = parseMarkdownToTaskTree(oldMarkdown);
    const newTree = parseMarkdownToTaskTree(newMarkdown);

    // Verify the old tree structure
    expect(oldTree.uuid).toBe("ddfded92-d526-40d8-8591-6dfe96e1aca4");
    expect(oldTree.subTasks).toHaveLength(1);
    expect(oldTree.subTasks[0]).toBe("20f968e3-32a5-4040-b270-6ac16bc162d5");
    expect(oldTree.subTasksData?.[0].state).toBe(TaskState.NOT_STARTED);

    // Verify the new tree structure
    expect(newTree.uuid).toBe("ddfded92-d526-40d8-8591-6dfe96e1aca4");
    expect(newTree.subTasks).toHaveLength(2);
    expect(newTree.subTasks[0]).toBe("20f968e3-32a5-4040-b270-6ac16bc162d5");
    expect(newTree.subTasks[1]).toBe("new-task-1");
    expect(newTree.subTasksData?.[0].state).toBe(TaskState.COMPLETE);
    expect(newTree.subTasksData?.[1].subTasks).toHaveLength(4);

    // Diff the trees
    const diff = diffTaskTrees(oldTree, newTree);

    // Verify created tasks
    expect(diff.created).toHaveLength(5);
    const createdUuids = diff.created.map((t) => t.uuid).sort();
    expect(createdUuids).toEqual(
      [
        "new-task-1",
        "new-task-2",
        "new-task-3",
        "new-task-4",
        "new-task-5",
      ].sort(),
    );

    // Verify the structure of the created tasks
    const newParentTask = diff.created.find((t) => t.uuid === "new-task-1");
    expect(newParentTask).toBeDefined();
    expect(newParentTask?.subTasks).toHaveLength(4);

    // Verify updated tasks
    expect(diff.updated).toHaveLength(2);
    const updatedUuids = diff.updated.map((t) => t.uuid).sort();
    expect(updatedUuids).toEqual([
      "20f968e3-32a5-4040-b270-6ac16bc162d5",
      "ddfded92-d526-40d8-8591-6dfe96e1aca4",
    ]);

    // Verify the state change in the updated subtask
    const updatedSubtask = diff.updated.find(
      (t) => t.uuid === "20f968e3-32a5-4040-b270-6ac16bc162d5",
    );
    expect(updatedSubtask?.state).toBe(TaskState.COMPLETE);

    // Verify deleted tasks
    expect(diff.deleted).toHaveLength(0);

    // Restore Date.now
    Date.now = originalDateNow;
  });
});

describe("findTaskInTree", () => {
  it("should find a task at the root level", () => {
    const rootTask: HydratedTask = {
      uuid: "root-uuid",
      name: "Root Task",
      description: "Root Description",
      state: TaskState.NOT_STARTED,
      subTasks: [],
      lastUpdated: Date.now(),
      lastUpdatedBy: TaskUpdatedBy.USER,
    };

    const foundTask = findTaskInTree(rootTask, "root-uuid");
    expect(foundTask).toBe(rootTask);
  });

  it("should find a task at the first level of nesting", () => {
    const childTask: HydratedTask = {
      uuid: "child-uuid",
      name: "Child Task",
      description: "Child Description",
      state: TaskState.NOT_STARTED,
      subTasks: [],
      lastUpdated: Date.now(),
      lastUpdatedBy: TaskUpdatedBy.USER,
    };

    const rootTask: HydratedTask = {
      uuid: "root-uuid",
      name: "Root Task",
      description: "Root Description",
      state: TaskState.NOT_STARTED,
      subTasks: ["child-uuid"],
      lastUpdatedBy: TaskUpdatedBy.USER,
      lastUpdated: Date.now(),
      subTasksData: [childTask],
    };

    const foundTask = findTaskInTree(rootTask, "child-uuid");
    expect(foundTask).toBe(childTask);
  });

  it("should find a task at a deeper level of nesting", () => {
    const grandchildTask: HydratedTask = {
      uuid: "grandchild-uuid",
      name: "Grandchild Task",
      description: "Grandchild Description",
      state: TaskState.NOT_STARTED,
      subTasks: [],
      lastUpdated: Date.now(),
      lastUpdatedBy: TaskUpdatedBy.USER,
    };

    const childTask: HydratedTask = {
      uuid: "child-uuid",
      name: "Child Task",
      description: "Child Description",
      state: TaskState.NOT_STARTED,
      subTasks: ["grandchild-uuid"],
      lastUpdated: Date.now(),
      lastUpdatedBy: TaskUpdatedBy.USER,
      subTasksData: [grandchildTask],
    };

    const rootTask: HydratedTask = {
      uuid: "root-uuid",
      name: "Root Task",
      description: "Root Description",
      state: TaskState.NOT_STARTED,
      subTasks: ["child-uuid"],
      lastUpdatedBy: TaskUpdatedBy.USER,
      lastUpdated: Date.now(),
      subTasksData: [childTask],
    };

    const foundTask = findTaskInTree(rootTask, "grandchild-uuid");
    expect(foundTask).toBe(grandchildTask);
  });

  it("should return undefined when task is not found", () => {
    const rootTask: HydratedTask = {
      uuid: "root-uuid",
      name: "Root Task",
      description: "Root Description",
      state: TaskState.NOT_STARTED,
      subTasks: [],
      lastUpdated: Date.now(),
      lastUpdatedBy: TaskUpdatedBy.USER,
    };

    const foundTask = findTaskInTree(rootTask, "non-existent-uuid");
    expect(foundTask).toBeUndefined();
  });

  it("should handle a task with subTasks but no subTasksData", () => {
    const rootTask: HydratedTask = {
      uuid: "root-uuid",
      name: "Root Task",
      description: "Root Description",
      state: TaskState.NOT_STARTED,
      subTasks: ["child-uuid"], // Has subTasks but no subTasksData
      lastUpdated: Date.now(),
      lastUpdatedBy: TaskUpdatedBy.USER,
    };

    const foundTask = findTaskInTree(rootTask, "child-uuid");
    expect(foundTask).toBeUndefined();
  });
});

describe("toFlatTask", () => {
  it("should flatten a task with no subtasks", () => {
    const task: HydratedTask = {
      uuid: "task-uuid",
      name: "Task",
      description: "Description",
      state: TaskState.NOT_STARTED,
      subTasks: [],
      lastUpdated: Date.now(),
      lastUpdatedBy: TaskUpdatedBy.USER,
    };

    const flatTasks = toFlatTask(task);
    expect(flatTasks).toHaveLength(1);
    expect(flatTasks[0]).toBe(task);
  });

  it("should flatten a task with one level of subtasks", () => {
    const childTask1: HydratedTask = {
      uuid: "child1-uuid",
      name: "Child 1",
      description: "Child 1 Description",
      state: TaskState.NOT_STARTED,
      subTasks: [],
      lastUpdated: Date.now(),
      lastUpdatedBy: TaskUpdatedBy.USER,
    };

    const childTask2: HydratedTask = {
      uuid: "child2-uuid",
      name: "Child 2",
      description: "Child 2 Description",
      state: TaskState.NOT_STARTED,
      subTasks: [],
      lastUpdated: Date.now(),
      lastUpdatedBy: TaskUpdatedBy.USER,
    };

    const rootTask: HydratedTask = {
      uuid: "root-uuid",
      name: "Root Task",
      description: "Root Description",
      state: TaskState.NOT_STARTED,
      subTasks: ["child1-uuid", "child2-uuid"],
      lastUpdated: Date.now(),
      lastUpdatedBy: TaskUpdatedBy.USER,
      subTasksData: [childTask1, childTask2],
    };

    const flatTasks = toFlatTask(rootTask);
    expect(flatTasks).toHaveLength(3);
    expect(flatTasks).toContain(rootTask);
    expect(flatTasks).toContain(childTask1);
    expect(flatTasks).toContain(childTask2);
  });

  it("should flatten a task with multiple levels of subtasks", () => {
    const grandchildTask: HydratedTask = {
      uuid: "grandchild-uuid",
      name: "Grandchild",
      description: "Grandchild Description",
      state: TaskState.NOT_STARTED,
      subTasks: [],
      lastUpdated: Date.now(),
      lastUpdatedBy: TaskUpdatedBy.USER,
    };

    const childTask: HydratedTask = {
      uuid: "child-uuid",
      name: "Child",
      description: "Child Description",
      state: TaskState.NOT_STARTED,
      subTasks: ["grandchild-uuid"],
      lastUpdated: Date.now(),
      lastUpdatedBy: TaskUpdatedBy.USER,
      subTasksData: [grandchildTask],
    };

    const rootTask: HydratedTask = {
      uuid: "root-uuid",
      name: "Root Task",
      description: "Root Description",
      state: TaskState.NOT_STARTED,
      subTasks: ["child-uuid"],
      lastUpdated: Date.now(),
      lastUpdatedBy: TaskUpdatedBy.USER,
      subTasksData: [childTask],
    };

    const flatTasks = toFlatTask(rootTask);
    expect(flatTasks).toHaveLength(3);
    expect(flatTasks).toContain(rootTask);
    expect(flatTasks).toContain(childTask);
    expect(flatTasks).toContain(grandchildTask);
  });

  it("should handle a task with subTasksData but empty array", () => {
    const rootTask: HydratedTask = {
      uuid: "root-uuid",
      name: "Root Task",
      description: "Root Description",
      state: TaskState.NOT_STARTED,
      subTasks: [],
      lastUpdated: Date.now(),
      lastUpdatedBy: TaskUpdatedBy.USER,
      subTasksData: [], // Empty array
    };

    const flatTasks = toFlatTask(rootTask);
    expect(flatTasks).toHaveLength(1);
    expect(flatTasks[0]).toBe(rootTask);
  });
});

describe("getMarkdownRepresentation", () => {
  it("should generate markdown for a task with no subtasks", () => {
    const task: HydratedTask = {
      uuid: "task-uuid",
      name: "Task Name",
      description: "Task Description",
      state: TaskState.NOT_STARTED,
      subTasks: [],
      lastUpdated: Date.now(),
      lastUpdatedBy: TaskUpdatedBy.USER,
    };

    const markdown = getMarkdownRepresentation(task);
    expect(markdown).toBe(
      "[ ] UUID:task-uuid NAME:Task Name DESCRIPTION:Task Description",
    );
  });

  it("should generate markdown for a task with subtasks", () => {
    const childTask: HydratedTask = {
      uuid: "child-uuid",
      name: "Child Task",
      description: "Child Description",
      state: TaskState.COMPLETE,
      subTasks: [],
      lastUpdated: Date.now(),
      lastUpdatedBy: TaskUpdatedBy.USER,
    };

    const rootTask: HydratedTask = {
      uuid: "root-uuid",
      name: "Root Task",
      description: "Root Description",
      state: TaskState.NOT_STARTED,
      subTasks: ["child-uuid"],
      lastUpdated: Date.now(),
      lastUpdatedBy: TaskUpdatedBy.USER,
      subTasksData: [childTask],
    };

    const markdown = getMarkdownRepresentation(rootTask);
    const expectedMarkdown = [
      "[ ] UUID:root-uuid NAME:Root Task DESCRIPTION:Root Description",
      "-[x] UUID:child-uuid NAME:Child Task DESCRIPTION:Child Description",
    ].join("\n");

    expect(markdown).toBe(expectedMarkdown);
  });

  it("should generate markdown for a task with nested subtasks", () => {
    const grandchildTask: HydratedTask = {
      uuid: "grandchild-uuid",
      name: "Grandchild Task",
      description: "Grandchild Description",
      state: TaskState.COMPLETE,
      subTasks: [],
      lastUpdated: Date.now(),
      lastUpdatedBy: TaskUpdatedBy.USER,
    };

    const childTask: HydratedTask = {
      uuid: "child-uuid",
      name: "Child Task",
      description: "Child Description",
      state: TaskState.COMPLETE,
      subTasks: [grandchildTask.uuid],
      lastUpdated: Date.now(),
      lastUpdatedBy: TaskUpdatedBy.USER,
      subTasksData: [grandchildTask],
    };

    const rootTask: HydratedTask = {
      uuid: "root-uuid",
      name: "Root Task",
      description: "Root Description",
      state: TaskState.IN_PROGRESS,
      subTasks: [childTask.uuid],
      lastUpdated: Date.now(),
      lastUpdatedBy: TaskUpdatedBy.USER,
      subTasksData: [childTask],
    };

    const markdown = getMarkdownRepresentation(rootTask);

    // Check each line separately to avoid issues with indentation
    expect(markdown).toContain(
      "[/] UUID:root-uuid NAME:Root Task DESCRIPTION:Root Description",
    );
    expect(markdown).toContain(
      "[x] UUID:child-uuid NAME:Child Task DESCRIPTION:Child Description",
    );
    expect(markdown).toContain(
      "[x] UUID:grandchild-uuid NAME:Grandchild Task DESCRIPTION:Grandchild Description",
    );

    // Verify the structure (each subtask is indented with one more dash)
    const lines = markdown.split("\n");
    expect(lines).toHaveLength(3);
    // The first line is the root task and should not start with a dash
    expect(lines[0].startsWith("-")).toBe(false);
    // The second line is the child task and should start with a dash
    expect(lines[1].startsWith("-")).toBe(true);
    // The third line is the grandchild task and should start with two dashes
    expect(lines[2].startsWith("--")).toBe(true);
  });

  it("should handle different task states correctly", () => {
    const notStartedTask: HydratedTask = {
      uuid: "not-started-uuid",
      name: "Not Started Task",
      description: "Not Started Description",
      state: TaskState.NOT_STARTED,
      subTasks: [],
      lastUpdated: Date.now(),
      lastUpdatedBy: TaskUpdatedBy.USER,
    };

    const pendingTask: HydratedTask = {
      uuid: "pending-uuid",
      name: "Pending Task",
      description: "Pending Description",
      state: TaskState.IN_PROGRESS,
      subTasks: [],
      lastUpdated: Date.now(),
      lastUpdatedBy: TaskUpdatedBy.USER,
    };

    const completeTask: HydratedTask = {
      uuid: "complete-uuid",
      name: "Complete Task",
      description: "Complete Description",
      state: TaskState.COMPLETE,
      subTasks: [],
      lastUpdated: Date.now(),
      lastUpdatedBy: TaskUpdatedBy.USER,
    };

    const cancelledTask: HydratedTask = {
      uuid: "cancelled-uuid",
      name: "Cancelled Task",
      description: "Cancelled Description",
      state: TaskState.CANCELLED,
      subTasks: [],
      lastUpdated: Date.now(),
      lastUpdatedBy: TaskUpdatedBy.USER,
    };

    expect(getMarkdownRepresentation(notStartedTask)).toContain("[ ]");
    expect(getMarkdownRepresentation(pendingTask)).toContain("[/]");
    expect(getMarkdownRepresentation(completeTask)).toContain("[x]");
    expect(getMarkdownRepresentation(cancelledTask)).toContain("[-]");
  });

  it("should include cancelled tasks in the markdown representation", () => {
    // Create a task with a cancelled subtask
    const cancelledSubTask: HydratedTask = {
      uuid: "cancelled-subtask-uuid",
      name: "Cancelled Sub Task",
      description: "This subtask is cancelled",
      state: TaskState.CANCELLED,
      subTasks: [],
      lastUpdated: Date.now(),
      lastUpdatedBy: TaskUpdatedBy.USER,
    };

    const parentTask: HydratedTask = {
      uuid: "parent-uuid",
      name: "Parent Task",
      description: "Parent task with cancelled subtask",
      state: TaskState.NOT_STARTED,
      subTasks: [cancelledSubTask.uuid],
      lastUpdated: Date.now(),
      lastUpdatedBy: TaskUpdatedBy.USER,
      subTasksData: [cancelledSubTask],
    };

    const markdown = getMarkdownRepresentation(parentTask);

    // Verify the parent task is included
    expect(markdown).toContain("[ ] UUID:parent-uuid");

    // Verify the cancelled subtask is included with the correct state marker
    expect(markdown).toContain("-[-] UUID:cancelled-subtask-uuid");
  });

  it("should perform round-trip conversion with all task states", () => {
    // Create a markdown string with tasks in different states, including cancelled
    const originalMarkdown = [
      "[ ] UUID:root-task NAME:Root Task DESCRIPTION:This is the root task",
      "-[ ] UUID:not-started-task NAME:Not Started Task DESCRIPTION:This task is not started",
      "-[/] UUID:in-progress-task NAME:In Progress Task DESCRIPTION:This task is in progress",
      "-[x] UUID:completed-task NAME:Completed Task DESCRIPTION:This task is completed",
      "-[-] UUID:cancelled-task NAME:Cancelled Task DESCRIPTION:This task is cancelled",
    ].join("\n");

    // Parse the markdown to a task tree
    const taskTree = parseMarkdownToTaskTree(originalMarkdown);

    // Convert the task tree back to markdown
    const regeneratedMarkdown = getMarkdownRepresentation(taskTree);

    // Split both markdown strings into lines for easier comparison
    const originalLines = originalMarkdown.split("\n");
    const regeneratedLines = regeneratedMarkdown.split("\n");

    // Verify the number of lines is the same
    expect(regeneratedLines.length).toBe(originalLines.length);

    // Verify each line contains the expected task state marker
    expect(regeneratedLines[0]).toContain("[ ]"); // Root task (not started)
    expect(regeneratedLines[1]).toContain("-[ ]"); // Not started subtask
    expect(regeneratedLines[2]).toContain("-[/]"); // In progress subtask
    expect(regeneratedLines[3]).toContain("-[x]"); // Completed subtask
    expect(regeneratedLines[4]).toContain("-[-]"); // Cancelled subtask

    // Verify the cancelled task is preserved in the round-trip conversion
    const cancelledTaskLine = regeneratedLines.find((line) =>
      line.includes("cancelled-task"),
    );
    expect(cancelledTaskLine).toBeDefined();
    expect(cancelledTaskLine).toContain("-[-]");
    expect(cancelledTaskLine).toContain("NAME:Cancelled Task");
  });

  it("should generate and parse deeply-nested task structure with multiple levels", () => {
    // Create a deeply nested task structure similar to the example
    const task121: HydratedTask = {
      uuid: "86c61ccd-2933-4a62-8fc2-fb174b178fb8",
      name: "Task 1.2.1",
      description: "This is a nested sub task, child of Task 1.2",
      state: TaskState.NOT_STARTED,
      subTasks: [],
      lastUpdated: Date.now(),
      lastUpdatedBy: TaskUpdatedBy.USER,
    };

    const task122: HydratedTask = {
      uuid: "17fc9a06-2f7f-4f0f-acd1-121d0b253071",
      name: "Task 1.2.2",
      description: "This is another nested sub task, child of Task 1.2",
      state: TaskState.NOT_STARTED,
      subTasks: [],
      lastUpdated: Date.now(),
      lastUpdatedBy: TaskUpdatedBy.USER,
    };

    const task11: HydratedTask = {
      uuid: "a0a4b17c-324d-4cea-aab3-308160efb26d",
      name: "Task 1.1",
      description: "This is the first sub task",
      state: TaskState.NOT_STARTED,
      subTasks: [],
      lastUpdated: Date.now(),
      lastUpdatedBy: TaskUpdatedBy.USER,
    };

    const task12: HydratedTask = {
      uuid: "8a7887b8-c9e4-40b5-b83e-be09388bf752",
      name: "Task 1.2",
      description: "This is the second sub task",
      state: TaskState.COMPLETE,
      subTasks: [task121.uuid, task122.uuid],
      lastUpdated: Date.now(),
      lastUpdatedBy: TaskUpdatedBy.USER,
      subTasksData: [task121, task122],
    };

    const task1: HydratedTask = {
      uuid: "487d3dec-450f-4f45-89e5-e263cee6ca98",
      name: "Task 1",
      description: "This is the first task",
      state: TaskState.NOT_STARTED,
      subTasks: [task11.uuid, task12.uuid],
      lastUpdated: Date.now(),
      lastUpdatedBy: TaskUpdatedBy.USER,
      subTasksData: [task11, task12],
    };

    // Generate markdown representation with full UUIDs
    const markdown = getMarkdownRepresentation(task1, { shortUuid: false });

    // Expected markdown structure
    const expectedMarkdown = [
      "[ ] UUID:487d3dec-450f-4f45-89e5-e263cee6ca98 NAME:Task 1 DESCRIPTION:This is the first task",
      "-[ ] UUID:a0a4b17c-324d-4cea-aab3-308160efb26d NAME:Task 1.1 DESCRIPTION:This is the first sub task",
      "-[x] UUID:8a7887b8-c9e4-40b5-b83e-be09388bf752 NAME:Task 1.2 DESCRIPTION:This is the second sub task",
      "--[ ] UUID:86c61ccd-2933-4a62-8fc2-fb174b178fb8 NAME:Task 1.2.1 DESCRIPTION:This is a nested sub task, child of Task 1.2",
      "--[ ] UUID:17fc9a06-2f7f-4f0f-acd1-121d0b253071 NAME:Task 1.2.2 DESCRIPTION:This is another nested sub task, child of Task 1.2",
    ].join("\n");

    // Verify the markdown matches the expected structure
    expect(markdown).toBe(expectedMarkdown);

    // Now parse the markdown back to a task tree
    const parsedTask = parseMarkdownToTaskTree(markdown, { shortUuid: false });

    // Verify the parsed task structure matches the original
    expect(parsedTask.uuid).toBe(task1.uuid);
    expect(parsedTask.name).toBe(task1.name);
    expect(parsedTask.description).toBe(task1.description);
    expect(parsedTask.state).toBe(task1.state);
    expect(parsedTask.subTasks).toHaveLength(2);
    expect(parsedTask.subTasks).toContain(task11.uuid);
    expect(parsedTask.subTasks).toContain(task12.uuid);

    // Verify first subtask
    const parsedTask11 = parsedTask.subTasksData?.find(
      (t) => t.uuid === task11.uuid,
    );
    expect(parsedTask11).toBeDefined();
    expect(parsedTask11?.name).toBe(task11.name);
    expect(parsedTask11?.state).toBe(task11.state);
    expect(parsedTask11?.subTasks).toHaveLength(0);

    // Verify second subtask
    const parsedTask12 = parsedTask.subTasksData?.find(
      (t) => t.uuid === task12.uuid,
    );
    expect(parsedTask12).toBeDefined();
    expect(parsedTask12?.name).toBe(task12.name);
    expect(parsedTask12?.state).toBe(task12.state);
    expect(parsedTask12?.subTasks).toHaveLength(2);
    expect(parsedTask12?.subTasks).toContain(task121.uuid);
    expect(parsedTask12?.subTasks).toContain(task122.uuid);

    // Verify nested subtasks
    const parsedTask121 = parsedTask12?.subTasksData?.find(
      (t) => t.uuid === task121.uuid,
    );
    expect(parsedTask121).toBeDefined();
    expect(parsedTask121?.name).toBe(task121.name);
    expect(parsedTask121?.state).toBe(task121.state);

    const parsedTask122 = parsedTask12?.subTasksData?.find(
      (t) => t.uuid === task122.uuid,
    );
    expect(parsedTask122).toBeDefined();
    expect(parsedTask122?.name).toBe(task122.name);
    expect(parsedTask122?.state).toBe(task122.state);
  });

  describe("excludeUuid option", () => {
    it("should exclude UUIDs from markdown when excludeUuid is true", () => {
      const task: HydratedTask = {
        uuid: "task-uuid",
        name: "Task Name",
        description: "Task Description",
        state: TaskState.NOT_STARTED,
        subTasks: [],
        lastUpdated: Date.now(),
        lastUpdatedBy: TaskUpdatedBy.USER,
      };

      const markdown = getMarkdownRepresentation(task, { excludeUuid: true });
      expect(markdown).toBe("[ ] NAME:Task Name DESCRIPTION:Task Description");
      expect(markdown).not.toContain("UUID:");
    });

    it("should include UUIDs in markdown when excludeUuid is false (default)", () => {
      const task: HydratedTask = {
        uuid: "task-uuid",
        name: "Task Name",
        description: "Task Description",
        state: TaskState.NOT_STARTED,
        subTasks: [],
        lastUpdated: Date.now(),
        lastUpdatedBy: TaskUpdatedBy.USER,
      };

      const markdown = getMarkdownRepresentation(task, { excludeUuid: false });
      expect(markdown).toBe(
        "[ ] UUID:task-uuid NAME:Task Name DESCRIPTION:Task Description",
      );
      expect(markdown).toContain("UUID:");
    });

    it("should exclude UUIDs from nested subtasks when excludeUuid is true", () => {
      const childTask: HydratedTask = {
        uuid: "child-uuid",
        name: "Child Task",
        description: "Child Description",
        state: TaskState.COMPLETE,
        subTasks: [],
        lastUpdated: Date.now(),
        lastUpdatedBy: TaskUpdatedBy.USER,
      };

      const rootTask: HydratedTask = {
        uuid: "root-uuid",
        name: "Root Task",
        description: "Root Description",
        state: TaskState.NOT_STARTED,
        subTasks: ["child-uuid"],
        lastUpdated: Date.now(),
        lastUpdatedBy: TaskUpdatedBy.USER,
        subTasksData: [childTask],
      };

      const markdown = getMarkdownRepresentation(rootTask, {
        excludeUuid: true,
      });
      const expectedMarkdown = [
        "[ ] NAME:Root Task DESCRIPTION:Root Description",
        "-[x] NAME:Child Task DESCRIPTION:Child Description",
      ].join("\n");

      expect(markdown).toBe(expectedMarkdown);
      expect(markdown).not.toContain("UUID:");
    });

    it("should work with shallow option and excludeUuid together", () => {
      const childTask: HydratedTask = {
        uuid: "child-uuid",
        name: "Child Task",
        description: "Child Description",
        state: TaskState.COMPLETE,
        subTasks: [],
        lastUpdated: Date.now(),
        lastUpdatedBy: TaskUpdatedBy.USER,
      };

      const rootTask: HydratedTask = {
        uuid: "root-uuid",
        name: "Root Task",
        description: "Root Description",
        state: TaskState.NOT_STARTED,
        subTasks: ["child-uuid"],
        lastUpdated: Date.now(),
        lastUpdatedBy: TaskUpdatedBy.USER,
        subTasksData: [childTask],
      };

      const markdown = getMarkdownRepresentation(rootTask, {
        shallow: true,
        excludeUuid: true,
      });

      expect(markdown).toBe("[ ] NAME:Root Task DESCRIPTION:Root Description");
      expect(markdown).not.toContain("UUID:");
      expect(markdown).not.toContain("Child Task");
    });
  });
});

describe("parseMarkdownToTaskTree with excludeUuid option", () => {
  it("should generate new UUIDs when excludeUuid is true", () => {
    const markdown = [
      "[ ] NAME:Root Task DESCRIPTION:Root Description",
      "-[x] NAME:Child Task DESCRIPTION:Child Description",
    ].join("\n");

    const taskTree = parseMarkdownToTaskTree(markdown, { excludeUuid: true });

    expect(taskTree.uuid).toBe("test-uuid"); // Generated UUID
    expect(taskTree.name).toBe("Root Task");
    expect(taskTree.description).toBe("Root Description");
    expect(taskTree.state).toBe(TaskState.NOT_STARTED);

    expect(taskTree.subTasksData).toHaveLength(1);
    expect(taskTree.subTasksData![0].uuid).toBe("test-uuid"); // Generated UUID
    expect(taskTree.subTasksData![0].name).toBe("Child Task");
    expect(taskTree.subTasksData![0].description).toBe("Child Description");
    expect(taskTree.subTasksData![0].state).toBe(TaskState.COMPLETE);
  });

  it("should parse UUIDs normally when excludeUuid is false (default)", () => {
    const markdown = [
      "[ ] UUID:root-uuid NAME:Root Task DESCRIPTION:Root Description",
      "-[x] UUID:child-uuid NAME:Child Task DESCRIPTION:Child Description",
    ].join("\n");

    const taskTree = parseMarkdownToTaskTree(markdown, { excludeUuid: false });

    expect(taskTree.uuid).toBe("root-uuid");
    expect(taskTree.name).toBe("Root Task");
    expect(taskTree.description).toBe("Root Description");
    expect(taskTree.state).toBe(TaskState.NOT_STARTED);

    expect(taskTree.subTasksData).toHaveLength(1);
    expect(taskTree.subTasksData![0].uuid).toBe("child-uuid");
    expect(taskTree.subTasksData![0].name).toBe("Child Task");
    expect(taskTree.subTasksData![0].description).toBe("Child Description");
    expect(taskTree.subTasksData![0].state).toBe(TaskState.COMPLETE);
  });

  it("should generate new UUIDs even when UUIDs are present in markdown with excludeUuid true", () => {
    const markdown = [
      "[ ] UUID:original-root-uuid NAME:Root Task DESCRIPTION:Root Description",
      "-[x] UUID:original-child-uuid NAME:Child Task DESCRIPTION:Child Description",
    ].join("\n");

    const taskTree = parseMarkdownToTaskTree(markdown, { excludeUuid: true });

    // Should ignore the UUIDs in the markdown and generate new ones
    expect(taskTree.uuid).toBe("test-uuid");
    expect(taskTree.uuid).not.toBe("original-root-uuid");

    expect(taskTree.subTasksData).toHaveLength(1);
    expect(taskTree.subTasksData![0].uuid).toBe("test-uuid");
    expect(taskTree.subTasksData![0].uuid).not.toBe("original-child-uuid");
  });

  it("should handle complex nested structures with excludeUuid", () => {
    const markdown = [
      "[ ] NAME:Root Task DESCRIPTION:Root Description",
      "-[/] NAME:Child Task DESCRIPTION:Child Description",
      "--[x] NAME:Grandchild Task DESCRIPTION:Grandchild Description",
    ].join("\n");

    const taskTree = parseMarkdownToTaskTree(markdown, { excludeUuid: true });

    expect(taskTree.uuid).toBe("test-uuid");
    expect(taskTree.name).toBe("Root Task");
    expect(taskTree.subTasksData).toHaveLength(1);

    const childTask = taskTree.subTasksData![0];
    expect(childTask.uuid).toBe("test-uuid");
    expect(childTask.name).toBe("Child Task");
    expect(childTask.state).toBe(TaskState.IN_PROGRESS);
    expect(childTask.subTasksData).toHaveLength(1);

    const grandchildTask = childTask.subTasksData![0];
    expect(grandchildTask.uuid).toBe("test-uuid");
    expect(grandchildTask.name).toBe("Grandchild Task");
    expect(grandchildTask.state).toBe(TaskState.COMPLETE);
  });
});

describe("round-trip conversion with excludeUuid", () => {
  it("should maintain task structure when using excludeUuid for both export and import", () => {
    const originalTask: HydratedTask = {
      uuid: "original-uuid",
      name: "Original Task",
      description: "Original Description",
      state: TaskState.IN_PROGRESS,
      subTasks: ["child-uuid"],
      lastUpdated: Date.now(),
      lastUpdatedBy: TaskUpdatedBy.USER,
      subTasksData: [
        {
          uuid: "child-uuid",
          name: "Child Task",
          description: "Child Description",
          state: TaskState.COMPLETE,
          subTasks: [],
          lastUpdated: Date.now(),
          lastUpdatedBy: TaskUpdatedBy.USER,
        },
      ],
    };

    // Export with excludeUuid
    const markdown = getMarkdownRepresentation(originalTask, {
      excludeUuid: true,
    });
    expect(markdown).not.toContain("UUID:");

    // Import with excludeUuid
    const parsedTask = parseMarkdownToTaskTree(markdown, { excludeUuid: true });

    // Structure should be preserved, but UUIDs should be new
    expect(parsedTask.name).toBe(originalTask.name);
    expect(parsedTask.description).toBe(originalTask.description);
    expect(parsedTask.state).toBe(originalTask.state);
    expect(parsedTask.uuid).not.toBe(originalTask.uuid);
    expect(parsedTask.uuid).toBe("test-uuid");

    expect(parsedTask.subTasksData).toHaveLength(1);
    expect(parsedTask.subTasksData![0].name).toBe(
      originalTask.subTasksData![0].name,
    );
    expect(parsedTask.subTasksData![0].description).toBe(
      originalTask.subTasksData![0].description,
    );
    expect(parsedTask.subTasksData![0].state).toBe(
      originalTask.subTasksData![0].state,
    );
    expect(parsedTask.subTasksData![0].uuid).not.toBe(
      originalTask.subTasksData![0].uuid,
    );
    expect(parsedTask.subTasksData![0].uuid).toBe("test-uuid");
  });
});

describe("parseLine", () => {
  // Mock Date.now for consistent test results
  const originalDateNow = Date.now;
  const mockTimestamp = 1234567890;

  beforeEach(() => (Date.now = jest.fn().mockReturnValue(mockTimestamp)));
  afterEach(() => (Date.now = originalDateNow));

  // Basic task line parsing with different states
  describe("Basic functionality", () => {
    it("should parse all task states correctly", () => {
      const testCases = [
        {
          line: "-[ ] UUID:test-uuid-1 NAME:Not Started DESCRIPTION:Not started task",
          expectedState: TaskState.NOT_STARTED,
        },
        {
          line: "-[/] UUID:test-uuid-2 NAME:In Progress DESCRIPTION:In progress task",
          expectedState: TaskState.IN_PROGRESS,
        },
        {
          line: "-[x] UUID:test-uuid-3 NAME:Completed DESCRIPTION:Completed task",
          expectedState: TaskState.COMPLETE,
        },
        {
          line: "-[-] UUID:test-uuid-4 NAME:Cancelled DESCRIPTION:Cancelled task",
          expectedState: TaskState.CANCELLED,
        },
      ];

      testCases.forEach(({ line, expectedState }) => {
        const task = testOnlyFunctions._parseTaskLine(line);
        expect(task.state).toBe(expectedState);
        expect(task.subTasks).toEqual([]);
        expect(task.lastUpdatedBy).toBe(TaskUpdatedBy.USER);
        expect(task.lastUpdated).toBe(mockTimestamp);
      });
    });

    it("should parse comprehensive hierarchy patterns", () => {
      const hierarchyTests = [
        // Traditional dash-based hierarchy
        {
          line: "[ ] UUID:root NAME:Root DESCRIPTION:Root task",
          expectedLevel: 0,
        },
        {
          line: "-[ ] UUID:l1 NAME:Level1 DESCRIPTION:Level 1 task",
          expectedLevel: 1,
        },
        {
          line: "--[ ] UUID:l2 NAME:Level2 DESCRIPTION:Level 2 task",
          expectedLevel: 2,
        },
        {
          line: "---[ ] UUID:l3 NAME:Level3 DESCRIPTION:Level 3 task",
          expectedLevel: 3,
        },

        // Whitespace-based hierarchy
        {
          line: "  [ ] UUID:w1 NAME:WhiteLevel1 DESCRIPTION:2 spaces = level 1",
          expectedLevel: 1,
        },
        {
          line: "    [ ] UUID:w2 NAME:WhiteLevel2 DESCRIPTION:4 spaces = level 2",
          expectedLevel: 2,
        },
        {
          line: "      [ ] UUID:w3 NAME:WhiteLevel3 DESCRIPTION:6 spaces = level 3",
          expectedLevel: 3,
        },

        // Tab-based hierarchy
        {
          line: "\t[ ] UUID:t1 NAME:TabLevel1 DESCRIPTION:1 tab = level 1",
          expectedLevel: 1,
        },
        {
          line: "\t\t[ ] UUID:t2 NAME:TabLevel2 DESCRIPTION:2 tabs = level 2",
          expectedLevel: 2,
        },

        // Mixed hierarchy patterns
        {
          line: "  -[ ] UUID:m1 NAME:Mixed1 DESCRIPTION:2 spaces + 1 dash = level 2",
          expectedLevel: 2,
        },
        {
          line: "\t-[ ] UUID:m2 NAME:Mixed2 DESCRIPTION:1 tab + 1 dash = level 2",
          expectedLevel: 2,
        },
        {
          line: "   --[ ] UUID:m3 NAME:Mixed3 DESCRIPTION:3 spaces + 2 dashes = level 3",
          expectedLevel: 3,
        },

        // Edge cases
        {
          line: " [ ] UUID:e1 NAME:Edge1 DESCRIPTION:1 space = level 0 (rounds down)",
          expectedLevel: 0,
        },
        {
          line: "   [ ] UUID:e2 NAME:Edge2 DESCRIPTION:3 spaces = level 1 (rounds down)",
          expectedLevel: 1,
        },
      ];

      hierarchyTests.forEach(({ line, expectedLevel }) => {
        const actualLevel = testOnlyFunctions._getIndentationLevel(line);
        const task = testOnlyFunctions._parseTaskLine(line);

        expect(task.uuid).toBeTruthy();
        expect(task.name).toBeTruthy();
        expect(task.description).toBeTruthy();
        expect(actualLevel).toBe(expectedLevel);
      });
    });

    it("should handle NEW_UUID placeholder correctly", () => {
      const line =
        "-[ ] UUID:NEW_UUID NAME:New Task DESCRIPTION:This is a new task";
      const task = testOnlyFunctions._parseTaskLine(line);
      expect(task.uuid).toBe("test-uuid");
    });

    it("should handle NEW_UUID_* placeholders correctly", () => {
      const line =
        "-[ ] UUID:NEW_UUID_123 NAME:New Task DESCRIPTION:This is a new task";
      const task = testOnlyFunctions._parseTaskLine(line);
      expect(task.uuid).toBe("NEW_UUID_123");
    });
  });

  // Edge case tests
  describe("Edge cases", () => {
    it("should handle various content and formatting edge cases", () => {
      const edgeCases = [
        {
          line: "-[ ]    UUID:test-uuid-1    NAME:Task 1    DESCRIPTION:Extra whitespace between fields",
          expectedName: "Task 1",
          expectedDesc: "Extra whitespace between fields",
          description: "extra whitespace between fields",
        },
        {
          line: "-[ ] UUID:test-uuid-2 NAME:Task with spaces DESCRIPTION:Multiple   spaces   preserved",
          expectedName: "Task with spaces",
          expectedDesc: "Multiple   spaces   preserved",
          description: "multiple spaces in content",
        },
        {
          line: "-[ ] UUID:test-uuid-3 NAME:Task: with colons DESCRIPTION:Description: with: multiple: colons",
          expectedName: "Task: with colons",
          expectedDesc: "Description: with: multiple: colons",
          description: "colons in content",
        },
        {
          line: "   -[ ] UUID:test-uuid-4 NAME:Leading whitespace DESCRIPTION:Task with leading whitespace   ",
          expectedName: "Leading whitespace",
          expectedDesc: "Task with leading whitespace",
          description: "leading and trailing whitespace",
        },
        {
          line: "-[ ] UUID:test-uuid-5 NAME:Empty desc DESCRIPTION:",
          expectedName: "Empty desc",
          expectedDesc: "",
          description: "empty description",
        },
        {
          line: "-[ ] UUID:test-uuid-6 NAME:Special !@#$%^&*() DESCRIPTION:Chars: !@#$%^&*()_+{}|:<>?",
          expectedName: "Special !@#$%^&*()",
          expectedDesc: "Chars: !@#$%^&*()_+{}|:<>?",
          description: "special characters",
        },
      ];

      edgeCases.forEach(({ line, expectedName, expectedDesc }) => {
        const task = testOnlyFunctions._parseTaskLine(line);
        expect(task.name).toBe(expectedName);
        expect(task.description).toBe(expectedDesc);
        expect(task.uuid).toBeTruthy();
      });
    });

    it("should handle unusual whitespace patterns", () => {
      // Tabs instead of spaces
      const line1 =
        "-[ ]\tUUID:test-uuid-1234\tNAME:Task 1\tDESCRIPTION:This is a task";
      const task1 = testOnlyFunctions._parseTaskLine(line1);
      expect(task1.uuid).toBe("test-uuid-1234");
      expect(task1.name).toBe("Task 1");
      expect(task1.description).toBe("This is a task");

      // Multiple spaces between fields
      const line2 =
        "-[ ]  UUID:test-uuid-1234  NAME:Task 1  DESCRIPTION:This is a task";
      const task2 = testOnlyFunctions._parseTaskLine(line2);
      expect(task2.uuid).toBe("test-uuid-1234");
      expect(task2.name).toBe("Task 1");
      expect(task2.description).toBe("This is a task");

      // No spaces between fields (should still work)
      const line3 =
        "-[ ]UUID:test-uuid-1234NAME:Task 1DESCRIPTION:This is a task";
      const task3 = testOnlyFunctions._parseTaskLine(line3);
      expect(task3.uuid).toBe("test-uuid-1234");
      expect(task3.name).toBe("Task 1");
      expect(task3.description).toBe("This is a task");
    });

    it("should handle whitespace between dashes and brackets", () => {
      // Single space between dash and bracket
      const line1 =
        "- [ ] UUID:test-uuid-1234 NAME:Task 1 DESCRIPTION:This is a task";
      const task1 = testOnlyFunctions._parseTaskLine(line1);
      expect(task1.uuid).toBe("test-uuid-1234");
      expect(task1.name).toBe("Task 1");
      expect(task1.description).toBe("This is a task");

      // Multiple spaces between dash and bracket
      const line2 =
        "-   [ ] UUID:test-uuid-1234 NAME:Task 1 DESCRIPTION:This is a task";
      const task2 = testOnlyFunctions._parseTaskLine(line2);
      expect(task2.uuid).toBe("test-uuid-1234");
      expect(task2.name).toBe("Task 1");
      expect(task2.description).toBe("This is a task");

      // Tab between dash and bracket
      const line3 =
        "-\t[ ] UUID:test-uuid-1234 NAME:Task 1 DESCRIPTION:This is a task";
      const task3 = testOnlyFunctions._parseTaskLine(line3);
      expect(task3.uuid).toBe("test-uuid-1234");
      expect(task3.name).toBe("Task 1");
      expect(task3.description).toBe("This is a task");

      // Mixed whitespace between dash and bracket
      const line4 =
        "- \t [ ] UUID:test-uuid-1234 NAME:Task 1 DESCRIPTION:This is a task";
      const task4 = testOnlyFunctions._parseTaskLine(line4);
      expect(task4.uuid).toBe("test-uuid-1234");
      expect(task4.name).toBe("Task 1");
      expect(task4.description).toBe("This is a task");
    });

    it("should handle whitespace-based hierarchy levels", () => {
      // 2 spaces = level 1
      const line1 =
        "  [ ] UUID:test-uuid-1234 NAME:Task 1 DESCRIPTION:This is a task";
      const task1 = testOnlyFunctions._parseTaskLine(line1);
      expect(task1.uuid).toBe("test-uuid-1234");
      expect(task1.name).toBe("Task 1");
      expect(task1.description).toBe("This is a task");

      // 4 spaces = level 2
      const line2 =
        "    [ ] UUID:test-uuid-1234 NAME:Task 1 DESCRIPTION:This is a task";
      const task2 = testOnlyFunctions._parseTaskLine(line2);
      expect(task2.uuid).toBe("test-uuid-1234");
      expect(task2.name).toBe("Task 1");
      expect(task2.description).toBe("This is a task");

      // 1 tab = level 1
      const line3 =
        "\t[ ] UUID:test-uuid-1234 NAME:Task 1 DESCRIPTION:This is a task";
      const task3 = testOnlyFunctions._parseTaskLine(line3);
      expect(task3.uuid).toBe("test-uuid-1234");
      expect(task3.name).toBe("Task 1");
      expect(task3.description).toBe("This is a task");

      // Mixed: 2 spaces + 1 dash = level 2
      const line4 =
        "  -[ ] UUID:test-uuid-1234 NAME:Task 1 DESCRIPTION:This is a task";
      const task4 = testOnlyFunctions._parseTaskLine(line4);
      expect(task4.uuid).toBe("test-uuid-1234");
      expect(task4.name).toBe("Task 1");
      expect(task4.description).toBe("This is a task");

      // 1 space = level 0 (rounds down)
      const line5 =
        " [ ] UUID:test-uuid-1234 NAME:Task 1 DESCRIPTION:This is a task";
      const task5 = testOnlyFunctions._parseTaskLine(line5);
      expect(task5.uuid).toBe("test-uuid-1234");
      expect(task5.name).toBe("Task 1");
      expect(task5.description).toBe("This is a task");
    });

    it("should parse complex task tree with mixed whitespace and dash hierarchy", () => {
      const markdown = [
        "[ ] UUID:root-task NAME:Root Task DESCRIPTION:This is the root task",
        "  [ ] UUID:level1-task1 NAME:Level 1 Task 1 DESCRIPTION:2 spaces = level 1",
        "    [ ] UUID:level2-task1 NAME:Level 2 Task 1 DESCRIPTION:4 spaces = level 2",
        "-[ ] UUID:level1-task2 NAME:Level 1 Task 2 DESCRIPTION:1 dash = level 1",
        "  -[ ] UUID:level2-task2 NAME:Level 2 Task 2 DESCRIPTION:2 spaces + 1 dash = level 2",
        "\t[ ] UUID:level1-task3 NAME:Level 1 Task 3 DESCRIPTION:1 tab = level 1",
      ].join("\n");

      const task = testOnlyFunctions._parseMarkdownToTaskTree(markdown);

      // Verify root task
      expect(task.uuid).toBe("root-task");
      expect(task.name).toBe("Root Task");
      expect(task.subTasks).toHaveLength(3);

      // Verify level 1 tasks
      const level1Tasks = task.subTasksData!;
      expect(level1Tasks[0].uuid).toBe("level1-task1");
      expect(level1Tasks[0].name).toBe("Level 1 Task 1");
      expect(level1Tasks[0].subTasks).toHaveLength(1);

      expect(level1Tasks[1].uuid).toBe("level1-task2");
      expect(level1Tasks[1].name).toBe("Level 1 Task 2");
      expect(level1Tasks[1].subTasks).toHaveLength(1);

      expect(level1Tasks[2].uuid).toBe("level1-task3");
      expect(level1Tasks[2].name).toBe("Level 1 Task 3");
      expect(level1Tasks[2].subTasks).toHaveLength(0);

      // Verify level 2 tasks
      expect(level1Tasks[0].subTasksData![0].uuid).toBe("level2-task1");
      expect(level1Tasks[0].subTasksData![0].name).toBe("Level 2 Task 1");

      expect(level1Tasks[1].subTasksData![0].uuid).toBe("level2-task2");
      expect(level1Tasks[1].subTasksData![0].name).toBe("Level 2 Task 2");
    });

    it("should handle boundary conditions and stress tests", () => {
      const boundaryTests = [
        // Maximum reasonable indentation
        {
          line: "                [ ] UUID:max-indent NAME:Max Indent DESCRIPTION:16 spaces = level 8",
          expectedLevel: 8,
        },

        // Mixed extreme whitespace
        {
          line: "\t\t\t\t[ ] UUID:tab-extreme NAME:Tab Extreme DESCRIPTION:4 tabs = level 4",
          expectedLevel: 4,
        },

        // Very long content (should still parse correctly)
        {
          line:
            "[ ] UUID:long-content NAME:" +
            "A".repeat(100) +
            " DESCRIPTION:" +
            "B".repeat(200),
          expectedLevel: 0,
          expectedName: "A".repeat(100),
          expectedDesc: "B".repeat(200),
        },

        // Minimal valid content
        {
          line: "[ ] UUID:min NAME:A DESCRIPTION:B",
          expectedLevel: 0,
          expectedName: "A",
          expectedDesc: "B",
        },

        // Complex mixed hierarchy at boundaries
        {
          line: " -[ ] UUID:boundary NAME:Boundary DESCRIPTION:1 space + 1 dash = level 1",
          expectedLevel: 1,
        },
        {
          line: "   -[ ] UUID:boundary2 NAME:Boundary2 DESCRIPTION:3 spaces + 1 dash = level 2",
          expectedLevel: 2,
        },
      ];

      boundaryTests.forEach(
        ({ line, expectedLevel, expectedName, expectedDesc }) => {
          const actualLevel = testOnlyFunctions._getIndentationLevel(line);
          const task = testOnlyFunctions._parseTaskLine(line);

          expect(actualLevel).toBe(expectedLevel);
          expect(expectedName ?? task.name).toBe(task.name);
          expect(expectedDesc ?? task.description).toBe(task.description);
        },
      );
    });

    it("should handle property-based parsing consistency", () => {
      // Test that parsing is consistent across different valid formats
      const baseTask = {
        uuid: "prop-test-uuid",
        name: "Property Test Task",
        description: "Testing parsing consistency",
      };

      const validFormats = [
        `[ ] UUID:${baseTask.uuid} NAME:${baseTask.name} DESCRIPTION:${baseTask.description}`,
        `  [ ] UUID:${baseTask.uuid} NAME:${baseTask.name} DESCRIPTION:${baseTask.description}`,
        `-[ ] UUID:${baseTask.uuid} NAME:${baseTask.name} DESCRIPTION:${baseTask.description}`,
        `- [ ] UUID:${baseTask.uuid} NAME:${baseTask.name} DESCRIPTION:${baseTask.description}`,
        `\t[ ] UUID:${baseTask.uuid} NAME:${baseTask.name} DESCRIPTION:${baseTask.description}`,
        `  -[ ] UUID:${baseTask.uuid} NAME:${baseTask.name} DESCRIPTION:${baseTask.description}`,
      ];

      validFormats.forEach((line) => {
        const task = testOnlyFunctions._parseTaskLine(line);

        expect(task.uuid).toBe(baseTask.uuid);
        expect(task.name).toBe(baseTask.name);
        expect(task.description).toBe(baseTask.description);
        expect(task.state).toBe(TaskState.NOT_STARTED);
      });
    });

    it("should handle non-ASCII characters in task name and description", () => {
      // Unicode characters
      const line1 =
        "-[ ] UUID:test-uuid-1234 NAME:Tâsk wïth üñicödë DESCRIPTION:Dèsçriptïøn with üñicödë";
      const task1 = testOnlyFunctions._parseTaskLine(line1);
      expect(task1.uuid).toBe("test-uuid-1234");
      expect(task1.name).toBe("Tâsk wïth üñicödë");
      expect(task1.description).toBe("Dèsçriptïøn with üñicödë");

      // Emoji
      const line2 =
        "-[ ] UUID:test-uuid-1234 NAME:Task with 😀 emoji 🚀 DESCRIPTION:Description with 🎉 emoji 🔥";
      const task2 = testOnlyFunctions._parseTaskLine(line2);
      expect(task2.uuid).toBe("test-uuid-1234");
      expect(task2.name).toBe("Task with 😀 emoji 🚀");
      expect(task2.description).toBe("Description with 🎉 emoji 🔥");
    });

    it("should handle very long task names and descriptions", () => {
      const longName = "A".repeat(100);
      const longDescription = "B".repeat(1000);
      const line = `-[ ] UUID:test-uuid-1234 NAME:${longName} DESCRIPTION:${longDescription}`;
      const task = testOnlyFunctions._parseTaskLine(line);
      expect(task.uuid).toBe("test-uuid-1234");
      expect(task.name).toBe(longName);
      expect(task.description).toBe(longDescription);
    });

    it("should handle case insensitivity in field markers", () => {
      const line =
        "-[ ] uuid:test-uuid-1234 name:Task 1 description:This is a task";
      const task = testOnlyFunctions._parseTaskLine(line);
      expect(task.uuid).toBe("test-uuid-1234");
      expect(task.name).toBe("Task 1");
      expect(task.description).toBe("This is a task");
    });

    it("should handle mixed case in field markers", () => {
      const line =
        "-[ ] UuId:test-uuid-1234 NaMe:Task 1 DeScRiPtIoN:This is a task";
      const task = testOnlyFunctions._parseTaskLine(line);
      expect(task.uuid).toBe("test-uuid-1234");
      expect(task.name).toBe("Task 1");
      expect(task.description).toBe("This is a task");
    });
  });

  // Error handling tests
  describe("Error handling", () => {
    it("should throw an error for missing state", () => {
      const line = "-UUID:1234 NAME:Task 1 DESCRIPTION:This is a task";
      expect(() => testOnlyFunctions._parseTaskLine(line)).toThrow(
        "missing state",
      );
    });

    it("should throw an error for invalid state format", () => {
      const line = "-[Z] UUID:1234 NAME:Task 1 DESCRIPTION:This is a task";
      expect(() => testOnlyFunctions._parseTaskLine(line)).toThrow(
        "missing state",
      );
    });

    it("should throw an error for missing UUID", () => {
      const line = "-[ ] NAME:Task 1 DESCRIPTION:This is a task";
      expect(() => testOnlyFunctions._parseTaskLine(line)).toThrow(
        "missing required fields",
      );
    });

    it("should throw an error for missing NAME", () => {
      const line = "-[ ] UUID:1234 DESCRIPTION:This is a task";
      expect(() => testOnlyFunctions._parseTaskLine(line)).toThrow(
        "missing required fields",
      );
    });

    it("should throw an error for missing DESCRIPTION", () => {
      const line = "-[ ] UUID:1234 NAME:Task 1";
      expect(() => testOnlyFunctions._parseTaskLine(line)).toThrow(
        "missing required fields",
      );
    });

    it("should throw an error for completely malformed line", () => {
      const line = "This is not a task line at all";
      expect(() => testOnlyFunctions._parseTaskLine(line)).toThrow(
        "missing state",
      );
    });

    it("should throw an error for incorrect field order", () => {
      const line = "-[ ] NAME:Task 1 UUID:1234 DESCRIPTION:This is a task";
      expect(() => testOnlyFunctions._parseTaskLine(line)).toThrow(
        "incorrect field order",
      );
    });

    it("should throw an error for incorrect field order (DESCRIPTION before NAME)", () => {
      const line = "-[ ] UUID:1234 DESCRIPTION:This is a task NAME:Task 1";
      expect(() => testOnlyFunctions._parseTaskLine(line)).toThrow(
        "incorrect field order",
      );
    });

    it("should throw an error for empty UUID", () => {
      const line = "-[ ] UUID: NAME:Task 1 DESCRIPTION:This is a task";
      expect(() => testOnlyFunctions._parseTaskLine(line)).toThrow(
        "missing required fields",
      );
    });

    it("should throw an error for empty NAME", () => {
      const line = "-[ ] UUID:1234 NAME: DESCRIPTION:This is a task";
      expect(() => testOnlyFunctions._parseTaskLine(line)).toThrow(
        "missing required fields",
      );
    });
  });

  describe("excludeUuid option", () => {
    it("should parse task line without UUID when excludeUuid is true", () => {
      const line = "-[ ] NAME:Task 1 DESCRIPTION:This is a task";
      const task = testOnlyFunctions._parseTaskLine(line, {
        excludeUuid: true,
      });

      expect(task.uuid).toBe("test-uuid"); // Generated UUID
      expect(task.name).toBe("Task 1");
      expect(task.description).toBe("This is a task");
      expect(task.state).toBe(TaskState.NOT_STARTED);
      expect(task.subTasks).toEqual([]);
      expect(task.lastUpdatedBy).toBe(TaskUpdatedBy.USER);
    });

    it("should parse task line with UUID when excludeUuid is false (default)", () => {
      const line =
        "-[ ] UUID:test-uuid-1234 NAME:Task 1 DESCRIPTION:This is a task";
      const task = testOnlyFunctions._parseTaskLine(line, {
        excludeUuid: false,
      });

      expect(task.uuid).toBe("test-uuid-1234");
      expect(task.name).toBe("Task 1");
      expect(task.description).toBe("This is a task");
      expect(task.state).toBe(TaskState.NOT_STARTED);
    });

    it("should generate new UUID even when UUID is present in line with excludeUuid true", () => {
      const line =
        "-[ ] UUID:original-uuid NAME:Task 1 DESCRIPTION:This is a task";
      const task = testOnlyFunctions._parseTaskLine(line, {
        excludeUuid: true,
      });

      expect(task.uuid).toBe("test-uuid"); // Generated UUID, not the one from the line
      expect(task.uuid).not.toBe("original-uuid");
      expect(task.name).toBe("Task 1");
      expect(task.description).toBe("This is a task");
    });

    it("should handle different task states with excludeUuid true", () => {
      const testCases = [
        {
          line: "-[ ] NAME:Not Started DESCRIPTION:Test",
          state: TaskState.NOT_STARTED,
        },
        {
          line: "-[/] NAME:In Progress DESCRIPTION:Test",
          state: TaskState.IN_PROGRESS,
        },
        {
          line: "-[x] NAME:Complete DESCRIPTION:Test",
          state: TaskState.COMPLETE,
        },
        {
          line: "-[-] NAME:Cancelled DESCRIPTION:Test",
          state: TaskState.CANCELLED,
        },
      ];

      testCases.forEach(({ line, state }) => {
        const task = testOnlyFunctions._parseTaskLine(line, {
          excludeUuid: true,
        });
        expect(task.state).toBe(state);
        expect(task.uuid).toBe("test-uuid");
      });
    });

    it("should handle NEW_UUID placeholder with excludeUuid true", () => {
      const line = "-[ ] UUID:NEW_UUID NAME:Task 1 DESCRIPTION:This is a task";
      const task = testOnlyFunctions._parseTaskLine(line, {
        excludeUuid: true,
      });

      expect(task.uuid).toBe("test-uuid"); // Generated UUID
      expect(task.name).toBe("Task 1");
      expect(task.description).toBe("This is a task");
    });

    it("should throw error for missing NAME when excludeUuid is true", () => {
      const line = "-[ ] DESCRIPTION:This is a task";
      expect(() =>
        testOnlyFunctions._parseTaskLine(line, { excludeUuid: true }),
      ).toThrow("missing required fields");
    });

    it("should throw error for missing DESCRIPTION when excludeUuid is true", () => {
      const line = "-[ ] NAME:Task 1";
      expect(() =>
        testOnlyFunctions._parseTaskLine(line, { excludeUuid: true }),
      ).toThrow("missing required fields");
    });

    it("should throw error for incorrect field order (DESCRIPTION before NAME) when excludeUuid is true", () => {
      const line = "-[ ] DESCRIPTION:This is a task NAME:Task 1";
      expect(() =>
        testOnlyFunctions._parseTaskLine(line, { excludeUuid: true }),
      ).toThrow("incorrect field order");
    });

    it("should handle special characters in name and description with excludeUuid true", () => {
      const line =
        "-[ ] NAME:Task with: colons & symbols DESCRIPTION:Description with: special & chars";
      const task = testOnlyFunctions._parseTaskLine(line, {
        excludeUuid: true,
      });

      expect(task.uuid).toBe("test-uuid");
      expect(task.name).toBe("Task with: colons & symbols");
      expect(task.description).toBe("Description with: special & chars");
    });

    it("should handle empty description with excludeUuid true", () => {
      const line = "-[ ] NAME:Task 1 DESCRIPTION:";
      const task = testOnlyFunctions._parseTaskLine(line, {
        excludeUuid: true,
      });

      expect(task.uuid).toBe("test-uuid");
      expect(task.name).toBe("Task 1");
      expect(task.description).toBe("");
    });
  });
});

describe("shortUuid functionality", () => {
  describe("getMarkdownRepresentation with shortUuid option", () => {
    it("should use short UUIDs when shortUuid is true (default)", () => {
      const task: HydratedTask = {
        uuid: "550e8400-e29b-41d4-a716-************", // Valid UUID
        name: "Task Name",
        description: "Task Description",
        state: TaskState.NOT_STARTED,
        subTasks: [],
        lastUpdated: Date.now(),
        lastUpdatedBy: TaskUpdatedBy.USER,
      };

      const markdown = getMarkdownRepresentation(task, { shortUuid: true });
      expect(markdown).toContain("UUID:");
      expect(markdown).not.toContain("550e8400-e29b-41d4-a716-************");
      // Should contain a short UUID instead
      const uuidMatch = markdown.match(/UUID:([^\s]+)/);
      expect(uuidMatch).toBeTruthy();
      expect(uuidMatch![1]).not.toBe("550e8400-e29b-41d4-a716-************");
      expect(uuidMatch![1].length).toBeLessThan(36); // Short UUID should be shorter
    });

    it("should use full UUIDs when shortUuid is false", () => {
      const task: HydratedTask = {
        uuid: "550e8400-e29b-41d4-a716-************", // Valid UUID
        name: "Task Name",
        description: "Task Description",
        state: TaskState.NOT_STARTED,
        subTasks: [],
        lastUpdated: Date.now(),
        lastUpdatedBy: TaskUpdatedBy.USER,
      };

      const markdown = getMarkdownRepresentation(task, { shortUuid: false });
      expect(markdown).toBe(
        "[ ] UUID:550e8400-e29b-41d4-a716-************ NAME:Task Name DESCRIPTION:Task Description",
      );
    });

    it("should handle invalid UUIDs gracefully when shortUuid is true", () => {
      const task: HydratedTask = {
        uuid: "invalid-uuid", // Invalid UUID format
        name: "Task Name",
        description: "Task Description",
        state: TaskState.NOT_STARTED,
        subTasks: [],
        lastUpdated: Date.now(),
        lastUpdatedBy: TaskUpdatedBy.USER,
      };

      const markdown = getMarkdownRepresentation(task, { shortUuid: true });
      expect(markdown).toBe(
        "[ ] UUID:invalid-uuid NAME:Task Name DESCRIPTION:Task Description",
      );
    });

    it("should use short UUIDs for nested subtasks when shortUuid is true", () => {
      const childTask: HydratedTask = {
        uuid: "550e8400-e29b-41d4-a716-************",
        name: "Child Task",
        description: "Child Description",
        state: TaskState.COMPLETE,
        subTasks: [],
        lastUpdated: Date.now(),
        lastUpdatedBy: TaskUpdatedBy.USER,
      };

      const rootTask: HydratedTask = {
        uuid: "550e8400-e29b-41d4-a716-************",
        name: "Root Task",
        description: "Root Description",
        state: TaskState.NOT_STARTED,
        subTasks: ["550e8400-e29b-41d4-a716-************"],
        lastUpdated: Date.now(),
        lastUpdatedBy: TaskUpdatedBy.USER,
        subTasksData: [childTask],
      };

      const markdown = getMarkdownRepresentation(rootTask, { shortUuid: true });
      expect(markdown).toContain("UUID:");
      expect(markdown).not.toContain("550e8400-e29b-41d4-a716-************");
      expect(markdown).not.toContain("550e8400-e29b-41d4-a716-************");

      // Should have two short UUIDs
      const uuidMatches = markdown.match(/UUID:([^\s]+)/g);
      expect(uuidMatches).toHaveLength(2);
    });
  });

  describe("parseMarkdownToTaskTree with shortUuid option", () => {
    it("should convert short UUIDs back to full UUIDs when shortUuid is true", () => {
      // First generate a task with short UUIDs
      const originalTask: HydratedTask = {
        uuid: "550e8400-e29b-41d4-a716-************",
        name: "Test Task",
        description: "Test Description",
        state: TaskState.NOT_STARTED,
        subTasks: [],
        lastUpdated: Date.now(),
        lastUpdatedBy: TaskUpdatedBy.USER,
      };

      const markdown = getMarkdownRepresentation(originalTask, {
        shortUuid: true,
      });

      // Parse it back
      const parsedTask = parseMarkdownToTaskTree(markdown, { shortUuid: true });

      // Should have the original full UUID
      expect(parsedTask.uuid).toBe("550e8400-e29b-41d4-a716-************");
      expect(parsedTask.name).toBe("Test Task");
      expect(parsedTask.description).toBe("Test Description");
    });

    it("should handle regular UUIDs when shortUuid is false", () => {
      const markdown =
        "[ ] UUID:550e8400-e29b-41d4-a716-************ NAME:Test Task DESCRIPTION:Test Description";

      const parsedTask = parseMarkdownToTaskTree(markdown, {
        shortUuid: false,
      });

      expect(parsedTask.uuid).toBe("550e8400-e29b-41d4-a716-************");
      expect(parsedTask.name).toBe("Test Task");
      expect(parsedTask.description).toBe("Test Description");
    });

    it("should handle invalid short UUIDs gracefully", () => {
      const markdown =
        "[ ] UUID:invalid-short-uuid NAME:Test Task DESCRIPTION:Test Description";

      const parsedTask = parseMarkdownToTaskTree(markdown, { shortUuid: true });

      // Should keep the original string if conversion fails
      expect(parsedTask.uuid).toBe("invalid-short-uuid");
      expect(parsedTask.name).toBe("Test Task");
      expect(parsedTask.description).toBe("Test Description");
    });
  });

  describe("round-trip conversion with shortUuid", () => {
    it("should maintain UUID consistency in round-trip conversion", () => {
      const originalTask: HydratedTask = {
        uuid: "550e8400-e29b-41d4-a716-************",
        name: "Original Task",
        description: "Original Description",
        state: TaskState.IN_PROGRESS,
        subTasks: ["550e8400-e29b-41d4-a716-************"],
        lastUpdated: Date.now(),
        lastUpdatedBy: TaskUpdatedBy.USER,
        subTasksData: [
          {
            uuid: "550e8400-e29b-41d4-a716-************",
            name: "Child Task",
            description: "Child Description",
            state: TaskState.COMPLETE,
            subTasks: [],
            lastUpdated: Date.now(),
            lastUpdatedBy: TaskUpdatedBy.USER,
          },
        ],
      };

      // Export with short UUIDs
      const markdown = getMarkdownRepresentation(originalTask, {
        shortUuid: true,
      });

      // Import with short UUIDs
      const parsedTask = parseMarkdownToTaskTree(markdown, { shortUuid: true });

      // UUIDs should be preserved
      expect(parsedTask.uuid).toBe(originalTask.uuid);
      expect(parsedTask.subTasksData![0].uuid).toBe(
        originalTask.subTasksData![0].uuid,
      );

      // Other properties should be preserved
      expect(parsedTask.name).toBe(originalTask.name);
      expect(parsedTask.description).toBe(originalTask.description);
      expect(parsedTask.state).toBe(originalTask.state);
      expect(parsedTask.subTasksData![0].name).toBe(
        originalTask.subTasksData![0].name,
      );
    });

    it("should work with mixed shortUuid and excludeUuid options", () => {
      const originalTask: HydratedTask = {
        uuid: "550e8400-e29b-41d4-a716-************",
        name: "Original Task",
        description: "Original Description",
        state: TaskState.NOT_STARTED,
        subTasks: [],
        lastUpdated: Date.now(),
        lastUpdatedBy: TaskUpdatedBy.USER,
      };

      // Export without UUIDs
      const markdown = getMarkdownRepresentation(originalTask, {
        excludeUuid: true,
        shortUuid: true, // This should be ignored when excludeUuid is true
      });
      expect(markdown).not.toContain("UUID:");

      // Import without UUIDs (should generate new ones)
      const parsedTask = parseMarkdownToTaskTree(markdown, {
        excludeUuid: true,
        shortUuid: true,
      });

      expect(parsedTask.uuid).toBe("test-uuid"); // Generated UUID
      expect(parsedTask.uuid).not.toBe(originalTask.uuid);
      expect(parsedTask.name).toBe(originalTask.name);
      expect(parsedTask.description).toBe(originalTask.description);
    });
  });
});

describe("parseTaskDiffMarkdown", () => {
  const originalDateNow = Date.now;
  const mockTimestamp = 1234567890;

  beforeEach(() => (Date.now = jest.fn().mockReturnValue(mockTimestamp)));
  afterEach(() => (Date.now = originalDateNow));

  it("should parse empty markdown", () => {
    const markdown = "";
    const result = parseTaskDiffMarkdown(markdown);

    expect(result.created).toHaveLength(0);
    expect(result.updated).toHaveLength(0);
    expect(result.deleted).toHaveLength(0);
  });

  it("should parse markdown with only created tasks", () => {
    const markdown = [
      "## Created Tasks",
      "",
      "[ ] UUID:task-1 NAME:New Task 1 DESCRIPTION:First new task",
      "[/] UUID:task-2 NAME:New Task 2 DESCRIPTION:Second new task",
      "",
    ].join("\n");

    const result = parseTaskDiffMarkdown(markdown);

    expect(result.created).toHaveLength(2);
    expect(result.updated).toHaveLength(0);
    expect(result.deleted).toHaveLength(0);

    expect(result.created[0]).toEqual({
      uuid: "task-1",
      name: "New Task 1",
      description: "First new task",
      state: TaskState.NOT_STARTED,
      subTasks: [],
      lastUpdated: mockTimestamp,
      lastUpdatedBy: TaskUpdatedBy.USER,
    });

    expect(result.created[1]).toEqual({
      uuid: "task-2",
      name: "New Task 2",
      description: "Second new task",
      state: TaskState.IN_PROGRESS,
      subTasks: [],
      lastUpdated: mockTimestamp,
      lastUpdatedBy: TaskUpdatedBy.USER,
    });
  });

  it("should parse markdown with only updated tasks", () => {
    const markdown = [
      "## Updated Tasks",
      "",
      "[x] UUID:task-1 NAME:Updated Task 1 DESCRIPTION:First updated task",
      "[-] UUID:task-2 NAME:Updated Task 2 DESCRIPTION:Second updated task",
      "",
    ].join("\n");

    const result = parseTaskDiffMarkdown(markdown);

    expect(result.created).toHaveLength(0);
    expect(result.updated).toHaveLength(2);
    expect(result.deleted).toHaveLength(0);

    expect(result.updated[0]).toEqual({
      uuid: "task-1",
      name: "Updated Task 1",
      description: "First updated task",
      state: TaskState.COMPLETE,
      subTasks: [],
      lastUpdated: mockTimestamp,
      lastUpdatedBy: TaskUpdatedBy.USER,
    });

    expect(result.updated[1]).toEqual({
      uuid: "task-2",
      name: "Updated Task 2",
      description: "Second updated task",
      state: TaskState.CANCELLED,
      subTasks: [],
      lastUpdated: mockTimestamp,
      lastUpdatedBy: TaskUpdatedBy.USER,
    });
  });

  it("should parse markdown with only deleted tasks", () => {
    const markdown = [
      "## Deleted Tasks",
      "",
      "[ ] UUID:task-1 NAME:Deleted Task 1 DESCRIPTION:First deleted task",
      "[x] UUID:task-2 NAME:Deleted Task 2 DESCRIPTION:Second deleted task",
      "",
    ].join("\n");

    const result = parseTaskDiffMarkdown(markdown);

    expect(result.created).toHaveLength(0);
    expect(result.updated).toHaveLength(0);
    expect(result.deleted).toHaveLength(2);

    expect(result.deleted[0]).toEqual({
      uuid: "task-1",
      name: "Deleted Task 1",
      description: "First deleted task",
      state: TaskState.NOT_STARTED,
      subTasks: [],
      lastUpdated: mockTimestamp,
      lastUpdatedBy: TaskUpdatedBy.USER,
    });

    expect(result.deleted[1]).toEqual({
      uuid: "task-2",
      name: "Deleted Task 2",
      description: "Second deleted task",
      state: TaskState.COMPLETE,
      subTasks: [],
      lastUpdated: mockTimestamp,
      lastUpdatedBy: TaskUpdatedBy.USER,
    });
  });

  it("should parse markdown with all three sections", () => {
    const markdown = [
      "## Created Tasks",
      "",
      "[ ] UUID:new-task NAME:New Task DESCRIPTION:A new task",
      "",
      "## Updated Tasks",
      "",
      "[x] UUID:updated-task NAME:Updated Task DESCRIPTION:An updated task",
      "",
      "## Deleted Tasks",
      "",
      "[-] UUID:deleted-task NAME:Deleted Task DESCRIPTION:A deleted task",
      "",
    ].join("\n");

    const result = parseTaskDiffMarkdown(markdown);

    expect(result.created).toHaveLength(1);
    expect(result.updated).toHaveLength(1);
    expect(result.deleted).toHaveLength(1);

    expect(result.created[0].uuid).toBe("new-task");
    expect(result.updated[0].uuid).toBe("updated-task");
    expect(result.deleted[0].uuid).toBe("deleted-task");
  });

  it("should handle sections in different order", () => {
    const markdown = [
      "## Deleted Tasks",
      "",
      "[-] UUID:deleted-task NAME:Deleted Task DESCRIPTION:A deleted task",
      "",
      "## Created Tasks",
      "",
      "[ ] UUID:new-task NAME:New Task DESCRIPTION:A new task",
      "",
      "## Updated Tasks",
      "",
      "[x] UUID:updated-task NAME:Updated Task DESCRIPTION:An updated task",
      "",
    ].join("\n");

    const result = parseTaskDiffMarkdown(markdown);

    expect(result.created).toHaveLength(1);
    expect(result.updated).toHaveLength(1);
    expect(result.deleted).toHaveLength(1);

    expect(result.created[0].uuid).toBe("new-task");
    expect(result.updated[0].uuid).toBe("updated-task");
    expect(result.deleted[0].uuid).toBe("deleted-task");
  });

  it("should ignore invalid task lines", () => {
    const markdown = [
      "## Created Tasks",
      "",
      "[ ] UUID:valid-task NAME:Valid Task DESCRIPTION:A valid task",
      "Invalid line without proper format",
      "Another invalid line",
      "[ ] Invalid task without proper UUID format",
      "",
    ].join("\n");

    const result = parseTaskDiffMarkdown(markdown);

    expect(result.created).toHaveLength(1);
    expect(result.created[0].uuid).toBe("valid-task");
  });

  it("should handle markdown with extra whitespace and empty lines", () => {
    const markdown = [
      "",
      "   ## Created Tasks   ",
      "",
      "",
      "   [ ] UUID:task-1 NAME:Task 1 DESCRIPTION:First task   ",
      "",
      "   ## Updated Tasks   ",
      "",
      "   [x] UUID:task-2 NAME:Task 2 DESCRIPTION:Second task   ",
      "",
      "",
    ].join("\n");

    const result = parseTaskDiffMarkdown(markdown);

    expect(result.created).toHaveLength(1);
    expect(result.updated).toHaveLength(1);
    expect(result.deleted).toHaveLength(0);

    expect(result.created[0].uuid).toBe("task-1");
    expect(result.updated[0].uuid).toBe("task-2");
  });

  it("should handle NEW_UUID placeholder correctly", () => {
    const markdown = [
      "## Created Tasks",
      "",
      "[ ] UUID:NEW_UUID NAME:New Task DESCRIPTION:Task with placeholder UUID",
      "",
    ].join("\n");

    const result = parseTaskDiffMarkdown(markdown);

    expect(result.created).toHaveLength(1);
    expect(result.created[0].uuid).toBe("test-uuid"); // Should be replaced by mock
    expect(result.created[0].name).toBe("New Task");
  });

  it("should handle custom NEW_UUID variants", () => {
    const markdown = [
      "## Created Tasks",
      "",
      "[ ] UUID:NEW_UUID_123 NAME:Custom UUID Task DESCRIPTION:Task with custom UUID",
      "",
    ].join("\n");

    const result = parseTaskDiffMarkdown(markdown);

    expect(result.created).toHaveLength(1);
    expect(result.created[0].uuid).toBe("NEW_UUID_123"); // Should keep custom variant
    expect(result.created[0].name).toBe("Custom UUID Task");
  });
});

describe("getTaskDiffCounts (legacy markdown)", () => {
  it("should return zero counts for empty markdown", () => {
    const markdown = "";
    const diff = parseTaskDiffMarkdown(markdown);
    const counts = {
      created: diff.created.length,
      updated: diff.updated.length,
      deleted: diff.deleted.length,
    };

    expect(counts).toEqual({
      created: 0,
      updated: 0,
      deleted: 0,
    });
  });

  it("should count tasks correctly", () => {
    const markdown = [
      "## Created Tasks",
      "",
      "[ ] UUID:task-1 NAME:New Task 1 DESCRIPTION:First new task",
      "[/] UUID:task-2 NAME:New Task 2 DESCRIPTION:Second new task",
      "",
      "## Updated Tasks",
      "",
      "[x] UUID:task-3 NAME:Updated Task DESCRIPTION:An updated task",
      "",
      "## Deleted Tasks",
      "",
      "[-] UUID:task-4 NAME:Deleted Task 1 DESCRIPTION:First deleted task",
      "[ ] UUID:task-5 NAME:Deleted Task 2 DESCRIPTION:Second deleted task",
      "[x] UUID:task-6 NAME:Deleted Task 3 DESCRIPTION:Third deleted task",
      "",
    ].join("\n");

    const diff = parseTaskDiffMarkdown(markdown);
    const counts = {
      created: diff.created.length,
      updated: diff.updated.length,
      deleted: diff.deleted.length,
    };

    expect(counts).toEqual({
      created: 2,
      updated: 1,
      deleted: 3,
    });
  });

  it("should handle sections with no tasks", () => {
    const markdown = [
      "## Created Tasks",
      "",
      "## Updated Tasks",
      "",
      "## Deleted Tasks",
      "",
    ].join("\n");

    const diff = parseTaskDiffMarkdown(markdown);
    const counts = {
      created: diff.created.length,
      updated: diff.updated.length,
      deleted: diff.deleted.length,
    };

    expect(counts).toEqual({
      created: 0,
      updated: 0,
      deleted: 0,
    });
  });
});

describe("taskDiffToMarkdown and parseTaskDiffMarkdown integration", () => {
  const originalDateNow = Date.now;
  const mockTimestamp = 1234567890;

  beforeEach(() => (Date.now = jest.fn().mockReturnValue(mockTimestamp)));
  afterEach(() => (Date.now = originalDateNow));

  it("should perform round-trip conversion correctly", () => {
    // Create a task diff
    const originalDiff = {
      created: [
        {
          uuid: "created-1",
          name: "Created Task 1",
          description: "First created task",
          state: TaskState.NOT_STARTED,
          subTasks: [],
          lastUpdated: mockTimestamp,
          lastUpdatedBy: TaskUpdatedBy.USER,
        },
        {
          uuid: "created-2",
          name: "Created Task 2",
          description: "Second created task",
          state: TaskState.IN_PROGRESS,
          subTasks: [],
          lastUpdated: mockTimestamp,
          lastUpdatedBy: TaskUpdatedBy.USER,
        },
      ],
      updated: [
        {
          uuid: "updated-1",
          name: "Updated Task",
          description: "An updated task",
          state: TaskState.COMPLETE,
          subTasks: [],
          lastUpdated: mockTimestamp,
          lastUpdatedBy: TaskUpdatedBy.USER,
        },
      ],
      deleted: [
        {
          uuid: "deleted-1",
          name: "Deleted Task",
          description: "A deleted task",
          state: TaskState.CANCELLED,
          subTasks: [],
          lastUpdated: mockTimestamp,
          lastUpdatedBy: TaskUpdatedBy.USER,
        },
      ],
    };

    // Convert to markdown
    const markdown = taskDiffToMarkdown(originalDiff);

    // Parse back to diff
    const parsedDiff = parseTaskDiffMarkdown(markdown);

    // Verify the round-trip conversion
    expect(parsedDiff.created).toHaveLength(2);
    expect(parsedDiff.updated).toHaveLength(1);
    expect(parsedDiff.deleted).toHaveLength(1);

    // Check specific tasks
    expect(parsedDiff.created[0]).toEqual(originalDiff.created[0]);
    expect(parsedDiff.created[1]).toEqual(originalDiff.created[1]);
    expect(parsedDiff.updated[0]).toEqual(originalDiff.updated[0]);
    expect(parsedDiff.deleted[0]).toEqual(originalDiff.deleted[0]);
  });

  it("should handle empty diff correctly", () => {
    const emptyDiff = {
      created: [],
      updated: [],
      deleted: [],
    };

    const markdown = taskDiffToMarkdown(emptyDiff);
    const parsedDiff = parseTaskDiffMarkdown(markdown);

    expect(parsedDiff).toEqual(emptyDiff);
  });

  it("should handle diff with only one section", () => {
    const createdOnlyDiff = {
      created: [
        {
          uuid: "created-1",
          name: "Created Task",
          description: "A created task",
          state: TaskState.NOT_STARTED,
          subTasks: [],
          lastUpdated: mockTimestamp,
          lastUpdatedBy: TaskUpdatedBy.USER,
        },
      ],
      updated: [],
      deleted: [],
    };

    const markdown = taskDiffToMarkdown(createdOnlyDiff);
    const parsedDiff = parseTaskDiffMarkdown(markdown);

    expect(parsedDiff.created).toHaveLength(1);
    expect(parsedDiff.updated).toHaveLength(0);
    expect(parsedDiff.deleted).toHaveLength(0);
    expect(parsedDiff.created[0]).toEqual(createdOnlyDiff.created[0]);
  });
});

describe("extractTaskDiffMarkdown", () => {
  it("should extract markdown from full tool result text", () => {
    const fullText = [
      "Task list updated successfully. Created: 2, Updated: 1, Deleted: 0.",
      "",
      "New and Updated Tasks:",
      "[ ] UUID:7d3fbba2-41f0-4178-9c0f-fad2f147075f NAME:Analyze current parsing functions DESCRIPTION:Examine parseTaskDiffMarkdown and extractTasksFromDiffMarkdown to understand duplication",
      "",
      "# Task Changes",
      "",
      "## Created Tasks",
      "",
      "[ ] UUID:task-1 NAME:New Task 1 DESCRIPTION:First new task",
      "[/] UUID:task-2 NAME:New Task 2 DESCRIPTION:Second new task",
      "",
      "## Updated Tasks",
      "",
      "[x] UUID:task-3 NAME:Updated Task DESCRIPTION:An updated task",
      "",
      "Remember: When updating the task list in the future:",
      "- Mark tasks as in progress ([/]) when you start working on them",
    ].join("\n");

    const markdown = extractTaskDiffMarkdown(fullText);

    expect(markdown).toContain("## Created Tasks");
    expect(markdown).toContain("## Updated Tasks");
    expect(markdown).toContain(
      "[ ] UUID:task-1 NAME:New Task 1 DESCRIPTION:First new task",
    );
    expect(markdown).toContain(
      "[x] UUID:task-3 NAME:Updated Task DESCRIPTION:An updated task",
    );
    expect(markdown).not.toContain("Remember:");
    expect(markdown).not.toContain("New and Updated Tasks:");
  });

  it("should return empty string when no task changes section found", () => {
    const fullText = [
      "Task list updated successfully. Created: 0, Updated: 0, Deleted: 0.",
      "",
      "No changes were made to the task list.",
    ].join("\n");

    const markdown = extractTaskDiffMarkdown(fullText);
    expect(markdown).toBe("");
  });

  it("should handle text with only the header", () => {
    const fullText = [
      "Task list updated successfully. Created: 1, Updated: 0, Deleted: 0.",
      "",
      "# Task Changes",
      "",
      "Remember: When updating the task list in the future:",
    ].join("\n");

    const markdown = extractTaskDiffMarkdown(fullText);
    expect(markdown).toBe("");
  });
});

describe("parseTaskDiffFromFullText", () => {
  const originalDateNow = Date.now;
  const mockTimestamp = 1234567890;

  beforeEach(() => (Date.now = jest.fn().mockReturnValue(mockTimestamp)));
  afterEach(() => (Date.now = originalDateNow));

  it("should parse full tool result text and extract tasks", () => {
    const fullText = [
      "Task list updated successfully. Created: 2, Updated: 1, Deleted: 0.",
      "",
      "New and Updated Tasks:",
      "[ ] UUID:7d3fbba2-41f0-4178-9c0f-fad2f147075f NAME:Analyze current parsing functions DESCRIPTION:Examine parseTaskDiffMarkdown and extractTasksFromDiffMarkdown to understand duplication",
      "",
      "# Task Changes",
      "",
      "## Created Tasks",
      "",
      "[ ] UUID:task-1 NAME:New Task 1 DESCRIPTION:First new task",
      "[/] UUID:task-2 NAME:New Task 2 DESCRIPTION:Second new task",
      "",
      "## Updated Tasks",
      "",
      "[x] UUID:task-3 NAME:Updated Task DESCRIPTION:An updated task",
      "",
      "## Deleted Tasks",
      "",
      "[-] UUID:task-4 NAME:Deleted Task DESCRIPTION:A deleted task",
      "",
      "Remember: When updating the task list in the future:",
      "- Mark tasks as in progress ([/]) when you start working on them",
    ].join("\n");

    const result = parseTaskDiffFromFullText(fullText);

    expect(result.created).toHaveLength(2);
    expect(result.updated).toHaveLength(1);
    expect(result.deleted).toHaveLength(1);

    expect(result.created[0]).toEqual({
      uuid: "task-1",
      name: "New Task 1",
      description: "First new task",
      state: TaskState.NOT_STARTED,
      subTasks: [],
      lastUpdated: mockTimestamp,
      lastUpdatedBy: TaskUpdatedBy.USER,
    });

    expect(result.updated[0]).toEqual({
      uuid: "task-3",
      name: "Updated Task",
      description: "An updated task",
      state: TaskState.COMPLETE,
      subTasks: [],
      lastUpdated: mockTimestamp,
      lastUpdatedBy: TaskUpdatedBy.USER,
    });
  });

  it("should handle full text with no task changes section", () => {
    const fullText = [
      "Task list updated successfully. Created: 0, Updated: 0, Deleted: 0.",
      "",
      "No changes were made to the task list.",
    ].join("\n");

    const result = parseTaskDiffFromFullText(fullText);

    expect(result.created).toHaveLength(0);
    expect(result.updated).toHaveLength(0);
    expect(result.deleted).toHaveLength(0);
  });
});

describe("getTaskDiffCounts with full text", () => {
  it("should extract counts from summary line", () => {
    const fullText = [
      "Task list updated successfully. Created: 3, Updated: 2, Deleted: 1.",
      "",
      "New and Updated Tasks:",
      "[ ] UUID:task-1 NAME:Task 1 DESCRIPTION:Description 1",
    ].join("\n");

    const counts = getTaskDiffCounts(fullText);

    expect(counts).toEqual({
      created: 3,
      updated: 2,
      deleted: 1,
    });
  });

  it("should fallback to parsing markdown when no summary line", () => {
    const fullText = [
      "# Task Changes",
      "",
      "## Created Tasks",
      "",
      "[ ] UUID:task-1 NAME:New Task 1 DESCRIPTION:First new task",
      "[/] UUID:task-2 NAME:New Task 2 DESCRIPTION:Second new task",
      "",
      "## Updated Tasks",
      "",
      "[x] UUID:task-3 NAME:Updated Task DESCRIPTION:An updated task",
      "",
    ].join("\n");

    const counts = getTaskDiffCounts(fullText);

    expect(counts).toEqual({
      created: 2,
      updated: 1,
      deleted: 0,
    });
  });

  it("should return zero counts when no information available", () => {
    const fullText = "No task changes found.";

    const counts = getTaskDiffCounts(fullText);

    expect(counts).toEqual({
      created: 0,
      updated: 0,
      deleted: 0,
    });
  });
});
