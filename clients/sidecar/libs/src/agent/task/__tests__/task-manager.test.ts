/**
 * @file task-manager.test.ts
 * This file contains comprehensive tests for the TaskManager class.
 */

import { TaskManager } from "../task-manager";
import { FileBackedTaskStorage } from "../task-storage";
import {
  TaskState,
  TaskUpdatedBy,
  type SerializedTask,
  type TaskManifest,
  type HydratedTask,
} from "../task-types";
import {
  IPluginFileStore,
  setLibraryPluginFileStore,
  resetLibraryPluginFileStore,
} from "../../../client-interfaces/plugin-file-store";

/**
 * Creates a mock task with the given properties.
 */
function createMockTask(
  uuid: string = "test-uuid",
  name: string = "Test Task",
  description: string = "Test Description",
  state: TaskState = TaskState.NOT_STARTED,
  subTasks: string[] = [],
  lastUpdated: number = Date.now(),
  lastUpdatedBy: TaskUpdatedBy = TaskUpdatedBy.USER,
): SerializedTask {
  return {
    uuid,
    name,
    description,
    state,
    subTasks,
    lastUpdated,
    lastUpdatedBy,
  };
}

/**
 * Creates a mock task manifest.
 */
function createMockTaskManifest(
  tasks: Record<string, SerializedTask> = {},
): TaskManifest {
  const taskMetadata: TaskManifest["tasks"] = {};

  for (const [uuid, task] of Object.entries(tasks)) {
    taskMetadata[uuid] = {
      uuid: task.uuid,
      name: task.name,
      lastUpdated: task.lastUpdated,
      state: task.state,
      parentTask: undefined, // Set this manually if needed
    };
  }

  return {
    version: 1,
    lastUpdated: Date.now(),
    tasks: taskMetadata,
  };
}

// Mock UUID generation for deterministic tests
jest.mock("uuid", () => ({
  v4: jest.fn().mockReturnValue("test-uuid"),
}));

// Mock crypto.randomUUID for deterministic tests
crypto.randomUUID = jest.fn().mockReturnValue("test-uuid");

describe("TaskManager", () => {
  let taskManager: TaskManager;
  let mockStorage: FileBackedTaskStorage;
  let mockPluginFileStore: jest.Mocked<IPluginFileStore>;

  beforeEach(() => {
    // Create a mock plugin file store
    mockPluginFileStore = {
      saveAsset: jest.fn().mockResolvedValue(undefined),
      loadAsset: jest.fn().mockImplementation((path: string) => {
        if (path.includes("manifest")) {
          return Promise.resolve(
            new TextEncoder().encode(
              JSON.stringify({
                version: 1,
                lastUpdated: Date.now(),
                tasks: {},
              }),
            ),
          );
        }
        return Promise.resolve(undefined);
      }),
      deleteAsset: jest.fn().mockResolvedValue(undefined),
      listAssets: jest.fn().mockResolvedValue([]),
      getAssetPath: jest
        .fn()
        .mockImplementation((path: string) =>
          Promise.resolve(`/mock/path/${path}`),
        ),
    };

    // Set the mock plugin file store
    setLibraryPluginFileStore(mockPluginFileStore);

    // Create a mock storage
    mockStorage = new FileBackedTaskStorage();

    // Create a task manager with the mock storage
    taskManager = new TaskManager(mockStorage);
  });

  afterEach(() => {
    jest.clearAllMocks();
    resetLibraryPluginFileStore();
  });

  describe("Initialization", () => {
    it("should initialize with an empty manifest if none exists", async () => {
      // Mock the storage to return undefined for manifest
      jest.spyOn(mockStorage, "loadManifest").mockResolvedValueOnce(undefined);

      // Initialize the manager
      await taskManager.initialize();

      // Get all tasks (should be empty)
      const tasks = await taskManager.getAllTasks();
      expect(tasks).toEqual([]);
    });

    it("should load existing manifest during initialization", async () => {
      // Create a mock manifest with a task
      const initialTask = createMockTask("existing-uuid", "Existing Task");
      const initialManifest = createMockTaskManifest({
        "existing-uuid": initialTask,
      });

      // Mock the storage to return the manifest
      jest
        .spyOn(mockStorage, "loadManifest")
        .mockResolvedValueOnce(initialManifest);
      jest.spyOn(mockStorage, "loadTask").mockResolvedValue(initialTask);

      // Initialize the manager
      await taskManager.initialize();

      // Get all tasks
      const tasks = await taskManager.getAllTasks();
      expect(tasks).toHaveLength(1);
      expect(tasks[0].uuid).toBe("existing-uuid");
    });
  });

  describe("Task Retrieval", () => {
    it("should get a task by UUID", async () => {
      // Create a mock task
      const mockTask = createMockTask("test-uuid", "Test Task");

      // Mock the storage to return the task
      jest.spyOn(mockStorage, "loadTask").mockResolvedValueOnce(mockTask);

      // Get the task
      const task = await taskManager.getTask("test-uuid");

      // Verify the task was returned
      expect(task).toEqual(mockTask);
    });

    it("should return undefined when getting a non-existent task", async () => {
      // Mock the storage to return undefined
      jest.spyOn(mockStorage, "loadTask").mockResolvedValueOnce(undefined);

      // Get a non-existent task
      const task = await taskManager.getTask("non-existent-uuid");

      // Verify undefined was returned
      expect(task).toBeUndefined();
    });

    it("should get an hydrated task with all sub-tasks", async () => {
      // Create mock tasks with parent-child relationships
      const parentTask = createMockTask(
        "parent-uuid",
        "Parent Task",
        "Parent Description",
        TaskState.NOT_STARTED,
        ["child1-uuid", "child2-uuid"],
      );
      const childTask1 = createMockTask(
        "child1-uuid",
        "Child Task 1",
        "Child Description 1",
      );
      const childTask2 = createMockTask(
        "child2-uuid",
        "Child Task 2",
        "Child Description 2",
        TaskState.IN_PROGRESS,
        ["grandchild-uuid"],
      );
      const grandchildTask = createMockTask(
        "grandchild-uuid",
        "Grandchild Task",
        "Grandchild Description",
        TaskState.COMPLETE,
      );

      // Mock the storage to return the tasks
      jest.spyOn(mockStorage, "loadTask").mockImplementation((uuid: string) => {
        if (uuid === "parent-uuid") return Promise.resolve(parentTask);
        if (uuid === "child1-uuid") return Promise.resolve(childTask1);
        if (uuid === "child2-uuid") return Promise.resolve(childTask2);
        if (uuid === "grandchild-uuid") return Promise.resolve(grandchildTask);
        return Promise.resolve(undefined);
      });

      // Get the hydrated task
      const aggregateTask = await taskManager.getHydratedTask("parent-uuid");

      // Verify the hydrated task structure
      expect(aggregateTask).toBeDefined();
      expect(aggregateTask?.uuid).toBe("parent-uuid");
      expect(aggregateTask?.subTasksData).toHaveLength(2);

      // Verify first child
      const firstChild = aggregateTask?.subTasksData?.find(
        (task) => task.uuid === "child1-uuid",
      );
      expect(firstChild).toBeDefined();
      expect(firstChild?.name).toBe("Child Task 1");
      expect(firstChild?.subTasksData).toHaveLength(0);

      // Verify second child and its grandchild
      const secondChild = aggregateTask?.subTasksData?.find(
        (task) => task.uuid === "child2-uuid",
      );
      expect(secondChild).toBeDefined();
      expect(secondChild?.name).toBe("Child Task 2");
      expect(secondChild?.subTasksData).toHaveLength(1);

      // Verify grandchild
      const grandchild = secondChild?.subTasksData?.[0];
      expect(grandchild).toBeDefined();
      expect(grandchild?.uuid).toBe("grandchild-uuid");
      expect(grandchild?.name).toBe("Grandchild Task");
      expect(grandchild?.state).toBe(TaskState.COMPLETE);
    });

    it("should return undefined when getting an hydrated task that doesn't exist", async () => {
      // Mock the storage to return undefined
      jest.spyOn(mockStorage, "loadTask").mockResolvedValueOnce(undefined);

      // Get a non-existent hydrated task
      const aggregateTask =
        await taskManager.getHydratedTask("non-existent-uuid");

      // Verify undefined was returned
      expect(aggregateTask).toBeUndefined();
    });
  });

  describe("Task Creation", () => {
    it("should create a task with the correct properties", async () => {
      // Mock the storage methods
      const saveTaskSpy = jest
        .spyOn(mockStorage, "saveTask")
        .mockResolvedValue();
      const saveManifestSpy = jest
        .spyOn(mockStorage, "saveManifest")
        .mockResolvedValue();

      // Create a task
      const uuid = await taskManager.createTask(
        "Test Task",
        "Test Description",
      );

      // Verify the task was saved with the correct properties
      expect(saveTaskSpy).toHaveBeenCalledWith(
        "test-uuid",
        expect.objectContaining({
          uuid: "test-uuid",
          name: "Test Task",
          description: "Test Description",
          state: TaskState.NOT_STARTED,
          subTasks: [],
          lastUpdatedBy: TaskUpdatedBy.USER,
        }),
      );

      // Verify the manifest was updated
      expect(saveManifestSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          version: 1,
          /* eslint-disable @typescript-eslint/no-unsafe-assignment */
          lastUpdated: expect.any(Number),
          tasks: expect.objectContaining({
            "test-uuid": expect.objectContaining({
              uuid: "test-uuid",
              name: "Test Task",
              state: TaskState.NOT_STARTED,
            }),
          }),
          /* eslint-enable @typescript-eslint/no-unsafe-assignment */
        }),
      );

      // Verify the UUID was returned
      expect(uuid).toBe("test-uuid");
    });

    it("should update the parent task when creating a sub-task", async () => {
      // Mock the storage methods
      const saveTaskSpy = jest
        .spyOn(mockStorage, "saveTask")
        .mockResolvedValue();
      jest.spyOn(mockStorage, "loadTask").mockResolvedValue({
        uuid: "parent-uuid",
        name: "Parent Task",
        description: "Parent Description",
        state: TaskState.NOT_STARTED,
        subTasks: [],
        lastUpdated: Date.now(),
        lastUpdatedBy: TaskUpdatedBy.USER,
      });

      // Create a sub-task
      await taskManager.createTask(
        "Sub Task",
        "Sub Description",
        "parent-uuid",
      );

      // Verify the parent task was updated
      expect(saveTaskSpy).toHaveBeenCalledWith(
        "parent-uuid",
        expect.objectContaining({
          uuid: "parent-uuid",
          subTasks: ["test-uuid"],
        }),
      );
    });

    it("should handle creating a sub-task when parent doesn't exist", async () => {
      // Mock the storage methods
      const saveTaskSpy = jest
        .spyOn(mockStorage, "saveTask")
        .mockResolvedValue();
      jest.spyOn(mockStorage, "loadTask").mockResolvedValue(undefined);

      // Create a task with a non-existent parent
      const uuid = await taskManager.createTask(
        "Sub Task",
        "Sub Description",
        "non-existent-uuid",
      );

      // Verify the task was created
      expect(saveTaskSpy).toHaveBeenCalledWith(
        "test-uuid",
        expect.objectContaining({
          uuid: "test-uuid",
          name: "Sub Task",
          description: "Sub Description",
        }),
      );

      // Verify the UUID was returned
      expect(uuid).toBe("test-uuid");
    });
  });

  describe("updateTask", () => {
    it("should update a task with the correct properties", async () => {
      // Mock the storage methods
      const saveTaskSpy = jest
        .spyOn(mockStorage, "saveTask")
        .mockResolvedValue();
      jest.spyOn(mockStorage, "loadTask").mockResolvedValue({
        uuid: "test-uuid",
        name: "Test Task",
        description: "Test Description",
        state: TaskState.NOT_STARTED,
        subTasks: [],
        lastUpdated: Date.now(),
        lastUpdatedBy: TaskUpdatedBy.USER,
      });

      // Update a task
      await taskManager.updateTask(
        "test-uuid",
        {
          name: "Updated Task",
          state: TaskState.IN_PROGRESS,
        },
        TaskUpdatedBy.AGENT,
      );

      // Verify the task was saved with the correct properties
      expect(saveTaskSpy).toHaveBeenCalledWith(
        "test-uuid",
        expect.objectContaining({
          uuid: "test-uuid",
          name: "Updated Task",
          state: TaskState.IN_PROGRESS,
          lastUpdatedBy: TaskUpdatedBy.AGENT,
        }),
      );
    });

    it("should handle updating a non-existent task", async () => {
      // Mock the storage methods
      const saveTaskSpy = jest
        .spyOn(mockStorage, "saveTask")
        .mockResolvedValue();
      jest.spyOn(mockStorage, "loadTask").mockResolvedValue(undefined);

      // Update a non-existent task
      await taskManager.updateTask(
        "non-existent-uuid",
        { name: "Updated Name" },
        TaskUpdatedBy.USER,
      );

      // Verify no task was saved
      expect(saveTaskSpy).not.toHaveBeenCalled();
    });
  });

  describe("cancelTask", () => {
    it("should cancel a task", async () => {
      // Mock the storage methods
      const saveTaskSpy = jest
        .spyOn(mockStorage, "saveTask")
        .mockResolvedValue();
      jest.spyOn(mockStorage, "loadTask").mockResolvedValue({
        uuid: "test-uuid",
        name: "Test Task",
        description: "Test Description",
        state: TaskState.NOT_STARTED,
        subTasks: [],
        lastUpdated: Date.now(),
        lastUpdatedBy: TaskUpdatedBy.USER,
      });

      // Cancel a task
      await taskManager.cancelTask("test-uuid");

      // Verify the task was saved with the correct properties
      expect(saveTaskSpy).toHaveBeenCalledWith(
        "test-uuid",
        expect.objectContaining({
          uuid: "test-uuid",
          state: TaskState.CANCELLED,
          lastUpdatedBy: TaskUpdatedBy.USER,
        }),
      );
    });

    it("should cancel sub-tasks when cancelSubTasks is true", async () => {
      // Mock the storage methods
      const saveTaskSpy = jest
        .spyOn(mockStorage, "saveTask")
        .mockResolvedValue();
      jest.spyOn(mockStorage, "loadTask").mockImplementation((uuid: string) => {
        if (uuid === "parent-uuid") {
          return Promise.resolve({
            uuid: "parent-uuid",
            name: "Parent Task",
            description: "Parent Description",
            state: TaskState.NOT_STARTED,
            subTasks: ["sub-uuid"],
            lastUpdated: Date.now(),
            lastUpdatedBy: TaskUpdatedBy.USER,
          });
        } else if (uuid === "sub-uuid") {
          return Promise.resolve({
            uuid: "sub-uuid",
            name: "Sub Task",
            description: "Sub Description",
            state: TaskState.NOT_STARTED,
            subTasks: [],
            lastUpdated: Date.now(),
            lastUpdatedBy: TaskUpdatedBy.USER,
          });
        }
        return Promise.resolve(undefined);
      });

      // Cancel a task and its sub-tasks
      await taskManager.cancelTask("parent-uuid", true);

      // Verify the parent task was cancelled
      expect(saveTaskSpy).toHaveBeenCalledWith(
        "parent-uuid",
        expect.objectContaining({
          uuid: "parent-uuid",
          state: TaskState.CANCELLED,
        }),
      );

      // Verify the sub-task was cancelled
      expect(saveTaskSpy).toHaveBeenCalledWith(
        "sub-uuid",
        expect.objectContaining({
          uuid: "sub-uuid",
          state: TaskState.CANCELLED,
        }),
      );
    });

    it("should handle cancelling a non-existent task", async () => {
      // Mock the storage methods
      const saveTaskSpy = jest
        .spyOn(mockStorage, "saveTask")
        .mockResolvedValue();
      jest.spyOn(mockStorage, "loadTask").mockResolvedValue(undefined);

      // Cancel a non-existent task
      await taskManager.cancelTask("non-existent-uuid");

      // Verify no task was saved
      expect(saveTaskSpy).not.toHaveBeenCalled();
    });
  });

  describe("Task Queries", () => {
    it("should get all root tasks", async () => {
      // Create mock manifest with parent-child relationships
      const manifest: TaskManifest = {
        version: 1,
        lastUpdated: Date.now(),
        tasks: {
          "root1-uuid": {
            uuid: "root1-uuid",
            name: "Root Task 1",
            lastUpdated: Date.now(),
            state: TaskState.NOT_STARTED,
          },
          "root2-uuid": {
            uuid: "root2-uuid",
            name: "Root Task 2",
            lastUpdated: Date.now(),
            state: TaskState.IN_PROGRESS,
          },
          "child-uuid": {
            uuid: "child-uuid",
            name: "Child Task",
            lastUpdated: Date.now(),
            state: TaskState.NOT_STARTED,
            parentTask: "root1-uuid",
          },
        },
      };

      // Mock the storage methods
      jest.spyOn(mockStorage, "loadManifest").mockResolvedValue(manifest);
      jest.spyOn(mockStorage, "loadTask").mockImplementation((uuid: string) => {
        if (uuid === "root1-uuid") {
          return Promise.resolve(createMockTask("root1-uuid", "Root Task 1"));
        } else if (uuid === "root2-uuid") {
          return Promise.resolve(
            createMockTask(
              "root2-uuid",
              "Root Task 2",
              "Root Description 2",
              TaskState.IN_PROGRESS,
            ),
          );
        }
        return Promise.resolve(undefined);
      });

      // Get all root tasks
      const rootTasks = await taskManager.getRootTasks();

      // Verify only root tasks were returned
      expect(rootTasks).toHaveLength(2);
      expect(rootTasks.map((task) => task.uuid).sort()).toEqual(
        ["root1-uuid", "root2-uuid"].sort(),
      );
    });

    it("should get all tasks", async () => {
      // Create mock manifest
      const manifest: TaskManifest = {
        version: 1,
        lastUpdated: Date.now(),
        tasks: {
          "task1-uuid": {
            uuid: "task1-uuid",
            name: "Task 1",
            lastUpdated: Date.now(),
            state: TaskState.NOT_STARTED,
          },
          "task2-uuid": {
            uuid: "task2-uuid",
            name: "Task 2",
            lastUpdated: Date.now(),
            state: TaskState.IN_PROGRESS,
          },
        },
      };

      // Mock the storage methods
      jest.spyOn(mockStorage, "loadManifest").mockResolvedValue(manifest);
      jest.spyOn(mockStorage, "loadTask").mockImplementation((uuid: string) => {
        if (uuid === "task1-uuid") {
          return Promise.resolve(createMockTask("task1-uuid", "Task 1"));
        } else if (uuid === "task2-uuid") {
          return Promise.resolve(
            createMockTask(
              "task2-uuid",
              "Task 2",
              "Task Description 2",
              TaskState.IN_PROGRESS,
            ),
          );
        }
        return Promise.resolve(undefined);
      });

      // Get all tasks
      const tasks = await taskManager.getAllTasks();

      // Verify all tasks were returned
      expect(tasks).toHaveLength(2);
      expect(tasks.map((task) => task.uuid).sort()).toEqual(
        ["task1-uuid", "task2-uuid"].sort(),
      );
    });
  });

  describe("updateHydratedTask", () => {
    it("should maintain task hierarchy when updating tasks", async () => {
      // Create a complex task hierarchy
      const rootTask = createMockTask(
        "root-uuid",
        "Root Task",
        "Root Description",
        TaskState.NOT_STARTED,
        ["child1-uuid", "child2-uuid"],
      );

      const child1Task = createMockTask(
        "child1-uuid",
        "Child 1",
        "Child 1 Description",
        TaskState.NOT_STARTED,
        [],
      );

      const child2Task = createMockTask(
        "child2-uuid",
        "Child 2",
        "Child 2 Description",
        TaskState.NOT_STARTED,
        ["grandchild-uuid"],
      );

      const grandchildTask = createMockTask(
        "grandchild-uuid",
        "Grandchild",
        "Grandchild Description",
        TaskState.NOT_STARTED,
        [],
      );

      // Create the hydrated task structure with proper typing
      const existingHydratedTask: HydratedTask = {
        ...rootTask,
        subTasksData: [
          child1Task,
          {
            ...child2Task,
            subTasksData: [grandchildTask],
          },
        ],
      };

      // Mock the storage to return the existing task hierarchy
      jest.spyOn(mockStorage, "loadTask").mockImplementation((uuid: string) => {
        if (uuid === "root-uuid") return Promise.resolve(rootTask);
        if (uuid === "child1-uuid") return Promise.resolve(child1Task);
        if (uuid === "child2-uuid") return Promise.resolve(child2Task);
        if (uuid === "grandchild-uuid") return Promise.resolve(grandchildTask);
        return Promise.resolve(undefined);
      });

      // Mock getHydratedTask to return the full hierarchy
      jest
        .spyOn(taskManager, "getHydratedTask")
        .mockResolvedValue(existingHydratedTask);

      // Create a modified version of the task tree with updated states
      const updatedHydratedTask: HydratedTask = {
        ...existingHydratedTask,
        state: TaskState.IN_PROGRESS,
        subTasksData: [
          {
            ...existingHydratedTask.subTasksData![0],
            state: TaskState.COMPLETE,
          },
          {
            ...existingHydratedTask.subTasksData![1],
            state: TaskState.IN_PROGRESS,
            subTasksData: [
              {
                ...existingHydratedTask.subTasksData![1].subTasksData![0],
                state: TaskState.COMPLETE,
              },
            ],
          },
        ],
      };

      // Mock the saveTask method to capture the updates
      const saveTaskSpy = jest
        .spyOn(mockStorage, "saveTask")
        .mockResolvedValue();

      // Update the hydrated task
      const result = await taskManager.updateHydratedTask(
        updatedHydratedTask,
        TaskUpdatedBy.USER,
      );

      // Verify the result counts
      expect(result.created).toBe(0);
      expect(result.updated).toBe(4); // All tasks were updated
      expect(result.deleted).toBe(0);

      // Verify that each task was updated with the correct state while maintaining hierarchy
      expect(saveTaskSpy).toHaveBeenCalledWith(
        "root-uuid",
        expect.objectContaining({
          uuid: "root-uuid",
          state: TaskState.IN_PROGRESS,
          subTasks: ["child1-uuid", "child2-uuid"], // Hierarchy maintained
        }),
      );

      expect(saveTaskSpy).toHaveBeenCalledWith(
        "child1-uuid",
        expect.objectContaining({
          uuid: "child1-uuid",
          state: TaskState.COMPLETE,
          subTasks: [], // Hierarchy maintained
        }),
      );

      expect(saveTaskSpy).toHaveBeenCalledWith(
        "child2-uuid",
        expect.objectContaining({
          uuid: "child2-uuid",
          state: TaskState.IN_PROGRESS,
          subTasks: ["grandchild-uuid"], // Hierarchy maintained
        }),
      );

      expect(saveTaskSpy).toHaveBeenCalledWith(
        "grandchild-uuid",
        expect.objectContaining({
          uuid: "grandchild-uuid",
          state: TaskState.COMPLETE,
          subTasks: [], // Hierarchy maintained
        }),
      );
    });

    it("should correctly handle new tasks in the hierarchy", async () => {
      // Create an existing task hierarchy
      const rootTask = createMockTask(
        "root-uuid",
        "Root Task",
        "Root Description",
        TaskState.NOT_STARTED,
        ["child1-uuid"],
      );

      const child1Task = createMockTask(
        "child1-uuid",
        "Child 1",
        "Child 1 Description",
        TaskState.NOT_STARTED,
        [],
      );

      // Create the hydrated task structure
      const existingHydratedTask: HydratedTask = {
        ...rootTask,
        subTasksData: [child1Task],
      };

      // Mock the storage to return the existing task hierarchy
      jest.spyOn(mockStorage, "loadTask").mockImplementation((uuid: string) => {
        if (uuid === "root-uuid") return Promise.resolve(rootTask);
        if (uuid === "child1-uuid") return Promise.resolve(child1Task);
        return Promise.resolve(undefined);
      });

      // Mock getHydratedTask to return the full hierarchy
      jest
        .spyOn(taskManager, "getHydratedTask")
        .mockResolvedValue(existingHydratedTask);

      // Mock createTask to return a predictable UUID
      const createTaskSpy = jest
        .spyOn(taskManager, "createTask")
        .mockImplementation((name) => {
          if (name === "New Child") return Promise.resolve("new-child-uuid");
          if (name === "New Grandchild")
            return Promise.resolve("new-grandchild-uuid");
          return Promise.resolve("unknown-uuid");
        });

      // Mock updateTask to avoid actual implementation
      const updateTaskSpy = jest
        .spyOn(taskManager, "updateTask")
        .mockResolvedValue();

      // Create an updated version with new tasks
      const updatedHydratedTask: HydratedTask = {
        ...existingHydratedTask,
        subTasksData: [
          child1Task,
          {
            uuid: "NEW_UUID", // This will be replaced with "new-child-uuid"
            name: "New Child",
            description: "New Child Description",
            state: TaskState.NOT_STARTED,
            subTasks: ["NEW_UUID_GRANDCHILD"], // This will be replaced
            lastUpdated: Date.now(),
            lastUpdatedBy: TaskUpdatedBy.USER,
            subTasksData: [
              {
                uuid: "NEW_UUID_GRANDCHILD", // This will be replaced with "new-grandchild-uuid"
                name: "New Grandchild",
                description: "New Grandchild Description",
                state: TaskState.NOT_STARTED,
                subTasks: [],
                lastUpdated: Date.now(),
                lastUpdatedBy: TaskUpdatedBy.USER,
              },
            ],
          },
        ],
        subTasks: ["child1-uuid", "NEW_UUID"], // This will be updated
      };

      // Update the hydrated task
      const result = await taskManager.updateHydratedTask(
        updatedHydratedTask,
        TaskUpdatedBy.USER,
      );

      // Verify the result counts
      expect(result.created).toBe(2); // Two new tasks were created
      expect(result.updated).toBe(1); // Root task was updated
      expect(result.deleted).toBe(0);

      // Verify that createTask was called for both new tasks
      expect(createTaskSpy).toHaveBeenCalledWith(
        "New Child",
        "New Child Description",
      );
      expect(createTaskSpy).toHaveBeenCalledWith(
        "New Grandchild",
        "New Grandchild Description",
      );

      // Verify that updateTask was called for the root task and the new tasks
      // The implementation calls updateTask for both created and updated tasks
      expect(updateTaskSpy).toHaveBeenCalledTimes(3);

      // Verify the root task was updated with proper subTasks
      expect(updateTaskSpy).toHaveBeenCalledWith(
        "root-uuid",
        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
        expect.objectContaining({
          // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
          subTasks: expect.arrayContaining(["child1-uuid"]),
        }),
        TaskUpdatedBy.USER,
      );
    });
  });
});
