/**
 * @file task-test-kit.ts
 * This file contains utilities for testing the task system.
 */

import {
  IPluginFileStore,
  setLibraryPluginFileStore,
  resetLibraryPluginFileStore,
} from "../../../client-interfaces/plugin-file-store";
import { TaskManager } from "../task-manager";
import { FileBackedTaskStorage } from "../task-storage";
import {
  type SerializedTask,
  TaskState,
  TaskUpdatedBy,
  type TaskManifest,
} from "../task-types";

/**
 * Creates a mock task with the given properties.
 * @param uuid - The UUID of the task
 * @param name - The name of the task
 * @param description - The description of the task
 * @param state - The state of the task
 * @param subTasks - The sub-tasks of the task
 * @param lastUpdated - The last updated timestamp
 * @param lastUpdatedBy - Who last updated the task
 * @returns A mock task
 */
export function createMockTask(
  uuid: string = "test-uuid",
  name: string = "Test Task",
  description: string = "Test Description",
  state: TaskState = TaskState.NOT_STARTED,
  subTasks: string[] = [],
  lastUpdated: number = Date.now(),
  lastUpdatedBy: TaskUpdatedBy = TaskUpdatedBy.USER,
): SerializedTask {
  return {
    uuid,
    name,
    description,
    state,
    subTasks,
    lastUpdated,
    lastUpdatedBy,
  };
}

/**
 * Creates a mock task manifest.
 * @param tasks - The tasks to include in the manifest
 * @returns A mock task manifest
 */
export function createMockTaskManifest(
  tasks: Record<string, SerializedTask> = {},
): TaskManifest {
  const taskMetadata: TaskManifest["tasks"] = {};

  for (const [uuid, task] of Object.entries(tasks)) {
    taskMetadata[uuid] = {
      uuid: task.uuid,
      name: task.name,
      lastUpdated: task.lastUpdated,
      state: task.state,
      parentTask: undefined, // Set this manually if needed
    };
  }

  return {
    version: 1,
    lastUpdated: Date.now(),
    tasks: taskMetadata,
  };
}

/**
 * Creates a mock plugin file store for testing.
 * @param initialTasks - Initial tasks to include in the store
 * @param initialManifest - Initial manifest to include in the store
 * @returns A mock plugin file store
 */
export function createMockPluginFileStore(
  initialTasks: Record<string, SerializedTask> = {},
  initialManifest?: TaskManifest,
): jest.Mocked<IPluginFileStore> {
  // Create a storage for mock data
  const mockStorage = new Map<string, Uint8Array>();

  // Store initial tasks
  for (const [uuid, task] of Object.entries(initialTasks)) {
    const path = `task-storage/tasks/${uuid}`;
    mockStorage.set(path, new TextEncoder().encode(JSON.stringify(task)));
  }

  // Store initial manifest
  if (initialManifest) {
    const path = `task-storage/manifest/manifest`;
    mockStorage.set(
      path,
      new TextEncoder().encode(JSON.stringify(initialManifest)),
    );
  }

  // Create mock plugin file store
  const mockPluginFileStore: jest.Mocked<IPluginFileStore> = {
    saveAsset: jest
      .fn()
      .mockImplementation((path: string, content: Uint8Array) => {
        mockStorage.set(path, content);
        return Promise.resolve();
      }),
    loadAsset: jest.fn().mockImplementation((path: string) => {
      const content = mockStorage.get(path);
      return Promise.resolve(content);
    }),
    deleteAsset: jest.fn().mockImplementation((path: string) => {
      mockStorage.delete(path);
      return Promise.resolve();
    }),
    listAssets: jest.fn().mockResolvedValue([]),
    getAssetPath: jest.fn().mockImplementation((path: string) => {
      return Promise.resolve(`/mock/path/to/assets/${path}`);
    }),
  };

  return mockPluginFileStore;
}

/**
 * Creates a test environment for the task system.
 * @param initialTasks - Initial tasks to include in the store
 * @param initialManifest - Initial manifest to include in the store
 * @returns A test environment
 */
export function createTaskTestEnvironment(
  initialTasks: Record<string, SerializedTask> = {},
  initialManifest?: TaskManifest,
) {
  // Create mock plugin file store
  const mockPluginFileStore = createMockPluginFileStore(
    initialTasks,
    initialManifest,
  );

  // Set the mock plugin file store
  setLibraryPluginFileStore(mockPluginFileStore);

  // Create storage and manager
  const storage = new FileBackedTaskStorage();
  const manager = new TaskManager(storage);

  return {
    mockPluginFileStore,
    storage,
    manager,
    cleanup: () => {
      resetLibraryPluginFileStore();
    },
  };
}
