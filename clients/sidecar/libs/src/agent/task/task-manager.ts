/**
 * @file task-manager.ts
 * This file contains the implementation of the TaskManager class.
 */

import { DisposableService } from "../../lifecycle/disposable-service";
import { getLogger } from "../../logging";
import {
  type HydratedTask,
  type SerializedTask,
  type TaskManifest,
  type TaskStorage,
  TaskState,
  TaskUpdatedBy,
} from "./task-types";
import { TaskFactory, diffTaskTrees } from "./task-utils";

/**
 * TaskManager provides a high-level API for managing tasks.
 * It handles the creation, updating, and retrieval of tasks,
 * including their hierarchical relationships.
 */
export class TaskManager extends DisposableService {
  private readonly _logger = getLogger("TaskManager");
  private _initialized = false;
  private _manifest: TaskManifest = {
    version: 1,
    lastUpdated: Date.now(),
    tasks: {},
  };

  /**
   * The UUID of the current root task.
   * This is used to track which task is currently active in the UI.
   */
  private _currentRootTaskUuid: string | undefined;

  constructor(private _storage: TaskStorage) {
    super();
  }

  /**
   * Initializes the task manager.
   * Loads the manifest from storage if available.
   */
  public initialize = async (): Promise<void> => {
    if (this._initialized) {
      return;
    }

    // Load manifest from storage
    const manifest = await this._storage.loadManifest();
    if (manifest) {
      this._manifest = manifest;
    }

    this._initialized = true;
  };

  /**
   * Ensures the task manager is initialized.
   */
  private _ensureInitialized = async (): Promise<void> => {
    if (!this._initialized) {
      await this.initialize();
    }
  };

  /**
   * Updates the manifest with a task's metadata.
   * @param task - The task to update in the manifest
   * @param parentTaskUuid - The UUID of the parent task, if any
   */
  private _updateManifest = async (
    task: SerializedTask,
    parentTaskUuid?: string,
  ): Promise<void> => {
    await this._ensureInitialized();

    // Update manifest
    this._manifest.tasks[task.uuid] = {
      uuid: task.uuid,
      name: task.name,
      lastUpdated: task.lastUpdated,
      state: task.state,
      parentTask: parentTaskUuid,
    };
    this._manifest.lastUpdated = Date.now();

    // Save manifest
    await this._storage.saveManifest(this._manifest);
  };

  /**
   * Creates a new task.
   * @param name - The name of the task
   * @param description - The description of the task
   * @param parentTaskUuid - The UUID of the parent task, if any
   * @returns The UUID of the created task
   */
  public createTask = async (
    name: string,
    description: string,
    parentTaskUuid?: string,
  ): Promise<string> => {
    await this._ensureInitialized();

    // Create task
    const task = TaskFactory.createTask(name, description);

    // If parent task is provided, update it
    if (parentTaskUuid) {
      const parentTask = await this.getTask(parentTaskUuid);
      if (parentTask) {
        // Add this task as a sub-task of the parent. Put it at the bottom of the list.
        await this.updateTask(
          parentTaskUuid,
          {
            subTasks: [...parentTask.subTasks, task.uuid],
          },
          TaskUpdatedBy.USER,
        );
      } else {
        this._logger.warn(`Parent task ${parentTaskUuid} not found`);
      }
    }

    // Save task
    await this._storage.saveTask(task.uuid, task);

    // Update manifest
    await this._updateManifest(task, parentTaskUuid);

    return task.uuid;
  };

  /**
   * Updates an existing task.
   * @param uuid - The UUID of the task to update
   * @param updates - The updates to apply to the task
   * @param updatedBy - Who is updating the task
   */
  public updateTask = async (
    uuid: string,
    updates: Partial<SerializedTask>,
    updatedBy: TaskUpdatedBy,
  ): Promise<void> => {
    await this._ensureInitialized();

    // Load task
    const task = await this.getTask(uuid);
    if (!task) {
      this._logger.warn(`Task ${uuid} not found`);
      return;
    }

    // Apply updates
    const updatedTask: SerializedTask = {
      ...task,
      ...updates,
      lastUpdated: Date.now(),
      lastUpdatedBy: updatedBy,
    };

    // Save task
    await this._storage.saveTask(uuid, updatedTask);

    // Update manifest
    await this._updateManifest(
      updatedTask,
      this._manifest.tasks[uuid]?.parentTask,
    );
  };

  /**
   * Gets a task by UUID.
   * @param uuid - The UUID of the task to get
   * @returns The task, or undefined if not found
   */
  public getTask = async (uuid: string): Promise<HydratedTask | undefined> => {
    await this._ensureInitialized();

    // Load task from storage
    return this._storage.loadTask(uuid);
  };

  /**
   * Gets a task with all its sub-tasks aggregated.
   * @param uuid - The UUID of the task to get
   * @returns The hydrated task, or undefined if not found
   */
  public getHydratedTask = async (
    uuid: string,
  ): Promise<HydratedTask | undefined> => {
    await this._ensureInitialized();

    // Load task
    const task = await this.getTask(uuid);
    if (!task) {
      return undefined;
    }

    // Load all sub-tasks recursively
    const subTasksData: HydratedTask[] = [];
    for (const subTaskUuid of task.subTasks) {
      const subTask = await this.getHydratedTask(subTaskUuid);
      if (subTask) {
        subTasksData.push(subTask);
      }
    }

    // Return hydrated task with sub-tasks
    return {
      ...task,
      subTasksData,
    };
  };

  /**
   * Cancels a task and optionally its sub-tasks.
   * @param uuid - The UUID of the task to cancel
   * @param cancelSubTasks - Whether to cancel sub-tasks as well
   * @param updatedBy - Who is cancelling the task
   */
  public cancelTask = async (
    uuid: string,
    cancelSubTasks: boolean = false,
    updatedBy: TaskUpdatedBy = TaskUpdatedBy.USER,
  ): Promise<void> => {
    await this._ensureInitialized();

    // Load task
    const task = await this.getTask(uuid);
    if (!task) {
      this._logger.warn(`Task ${uuid} not found`);
      return;
    }

    // Update state to CANCELLED
    await this.updateTask(
      uuid,
      {
        state: TaskState.CANCELLED,
      },
      updatedBy,
    );

    // If cancelSubTasks, recursively cancel all sub-tasks
    if (cancelSubTasks) {
      for (const subTaskUuid of task.subTasks) {
        await this.cancelTask(subTaskUuid, true, updatedBy);
      }
    }
  };

  /**
   * Gets all tasks in the system.
   * @returns All tasks
   */
  public getAllTasks = async (): Promise<SerializedTask[]> => {
    await this._ensureInitialized();

    // Get all task UUIDs from manifest
    const taskUuids = Object.keys(this._manifest.tasks);

    // Load all tasks
    const tasks: SerializedTask[] = [];
    for (const uuid of taskUuids) {
      const task = await this.getTask(uuid);
      if (task) {
        tasks.push(task);
      }
    }

    return tasks;
  };

  /**
   * Gets all root tasks (tasks with no parent).
   * @returns All root tasks
   */
  public getRootTasks = async (): Promise<SerializedTask[]> => {
    await this._ensureInitialized();

    // Filter for tasks with no parent
    const rootTaskUuids = Object.entries(this._manifest.tasks)
      .filter(([_, metadata]) => !metadata.parentTask)
      .map(([uuid]) => uuid);

    // Load all root tasks
    const rootTasks: SerializedTask[] = [];
    for (const uuid of rootTaskUuids) {
      const task = await this.getTask(uuid);
      if (task) {
        rootTasks.push(task);
      }
    }

    return rootTasks;
  };

  /**
   * Gets the current root task UUID.
   * @returns The UUID of the current root task, or undefined if none is set
   */
  public getCurrentRootTaskUuid = (): string | undefined => {
    return this._currentRootTaskUuid;
  };

  /**
   * Sets the current root task UUID.
   * @param uuid - The UUID of the current root task
   */
  public setCurrentRootTaskUuid = (uuid: string): void => {
    this._logger.info(`Setting current root task UUID to ${uuid}`);
    this._currentRootTaskUuid = uuid;
  };

  /**
   * Updates a hydrated task tree by diffing it against the existing tree.
   * This allows updating an entire task tree at once.
   * @param newTree - The updated task tree
   * @param updatedBy - Who is updating the task
   * @returns Object containing counts of created, updated, and deleted tasks
   */
  public updateHydratedTask = async (
    newTree: HydratedTask,
    updatedBy: TaskUpdatedBy,
  ): Promise<{ created: number; updated: number; deleted: number }> => {
    await this._ensureInitialized();

    // Get the existing task tree
    const existingTree = await this.getHydratedTask(newTree.uuid);
    if (!existingTree) {
      // If the task doesn't exist, we can't update it
      return { created: 0, updated: 0, deleted: 0 };
    }

    // Compare the trees and identify changes
    const changes = diffTaskTrees(existingTree, newTree);

    // For each task marked created, create it and then update it with its new UUID
    // Also, populate a map of old UUIDs to new UUIDs
    const tempUuidToRealUuid = new Map<string, string>();
    await Promise.all(
      changes.created.map(async (task): Promise<void> => {
        const oldUuid = task.uuid;
        // Do not use a parent at all, since the tree will be updated later
        // When UUIDs are actually properly calculated
        const newUuid = await this.createTask(task.name, task.description);
        task.uuid = newUuid;
        tempUuidToRealUuid.set(oldUuid, newUuid);
      }),
    );

    // For all tasks, update the subtask UUIDs to the new UUIDs
    changes.created.forEach((task) => {
      task.subTasks = task.subTasks.map(
        (uuid) => tempUuidToRealUuid.get(uuid) || uuid,
      );
    });
    changes.updated.forEach((task) => {
      task.subTasks = task.subTasks.map(
        (uuid) => tempUuidToRealUuid.get(uuid) || uuid,
      );
    });
    changes.deleted.forEach((task) => {
      task.subTasks = task.subTasks.map(
        (uuid) => tempUuidToRealUuid.get(uuid) || uuid,
      );
    });

    // Update existing tasks (in parallel)
    await Promise.all(
      [...changes.created, ...changes.updated].map(async (task) => {
        await this.updateTask(
          task.uuid,
          {
            name: task.name,
            description: task.description,
            state: task.state,
            subTasks: task.subTasks,
          },
          updatedBy,
        );
      }),
    );

    // Delete tasks (by cancelling them) (in parallel)
    await Promise.all(
      changes.deleted.map(async (task) => {
        // Skip the root task
        if (task.uuid === newTree.uuid) return;
        await this.cancelTask(
          task.uuid,
          true, // Cancel sub-tasks as well
          updatedBy,
        );
      }),
    );

    return {
      created: changes.created.length,
      updated: changes.updated.length,
      deleted: changes.deleted.length,
    };
  };

  /**
   * Hydrates a task tree from a serialized task.
   * This is a convenience method that takes a SerializedTask and returns a HydratedTask.
   * @param task - The serialized task to hydrate
   * @returns The hydrated task tree
   */
  public hydrateTaskTree = async (
    task: SerializedTask,
  ): Promise<HydratedTask> => {
    // Start with the base task
    const hydratedTask: HydratedTask = { ...task };

    // Load all sub-tasks recursively
    const subTasksData: HydratedTask[] = [];
    for (const subTaskUuid of task.subTasks) {
      const subTask = await this.getTask(subTaskUuid);
      if (subTask) {
        // Recursively hydrate the subtask
        const hydratedSubTask = await this.hydrateTaskTree(subTask);
        subTasksData.push(hydratedSubTask);
      }
    }

    // Add the hydrated sub-tasks to the task
    hydratedTask.subTasksData = subTasksData;

    return hydratedTask;
  };
}
