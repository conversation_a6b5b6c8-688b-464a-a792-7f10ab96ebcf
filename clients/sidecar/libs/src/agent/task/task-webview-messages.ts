/**
 * @file task-webview-messages.ts
 * This file contains the handler for task-related webview messages.
 */

import { TaskManager } from "./task-manager";
import {
  CommonWebViewMessageType,
  WebViewMessage,
} from "../../webview-messages/common-webview-messages";
import {
  TaskWebViewMessageType,
  GetHydratedTaskRequest,
  GetHydratedTaskResponse,
  CreateTaskRequest,
  CreateTaskResponse,
  UpdateTaskRequest,
  UpdateTaskResponse,
  UpdateHydratedTaskRequest,
  UpdateHydratedTaskResponse,
  SetCurrentRootTaskUuid,
} from "../../webview-messages/message-types/task-messages";
import { IWebviewMessageConsumer } from "../../webview-messages/webview-messages-broker";

/**
 * Handler for task-related webview messages.
 * Implements the IWebviewMessageConsumer interface to handle task-related messages.
 */
export class TaskWebviewMessageHandler
  implements IWebviewMessageConsumer<TaskWebViewMessageType>
{
  public readonly supportedTypes = TaskWebViewMessageType;

  constructor(private readonly _taskManager: TaskManager) {}

  /**
   * Handles incoming webview messages.
   * @param msg - The incoming message
   * @param postMessage - Function to post a response message
   */
  public async handle(
    msg: WebViewMessage<TaskWebViewMessageType>,
    postMessage: (
      msg: WebViewMessage<TaskWebViewMessageType | CommonWebViewMessageType>,
    ) => void,
  ): Promise<void> {
    switch (msg.type) {
      case TaskWebViewMessageType.getHydratedTaskRequest: {
        const webviewMsg = msg as GetHydratedTaskRequest;
        const response = await this._getHydratedTask(webviewMsg);
        postMessage(response);
        break;
      }
      case TaskWebViewMessageType.createTaskRequest: {
        const webviewMsg = msg as CreateTaskRequest;
        const response = await this._createTask(webviewMsg);
        postMessage(response);
        break;
      }
      case TaskWebViewMessageType.updateTaskRequest: {
        const webviewMsg = msg as UpdateTaskRequest;
        const response = await this._updateTask(webviewMsg);
        postMessage(response);
        break;
      }
      case TaskWebViewMessageType.setCurrentRootTaskUuid: {
        const webviewMsg = msg as SetCurrentRootTaskUuid;
        // Set the current root task UUID in the task manager
        this._taskManager.setCurrentRootTaskUuid(webviewMsg.data.uuid);
        // Send an empty response
        postMessage({
          type: CommonWebViewMessageType.empty,
        });
        break;
      }
      case TaskWebViewMessageType.updateHydratedTaskRequest: {
        const webviewMsg = msg as UpdateHydratedTaskRequest;
        const response = await this._updateHydratedTask(webviewMsg);
        postMessage(response);
        break;
      }
    }
  }

  /**
   * Gets an hydrated task.
   * @param message - The GetHydratedTaskRequest message
   * @returns A promise that resolves to a GetHydratedTaskResponse
   */
  private _getHydratedTask = async (
    message: GetHydratedTaskRequest,
  ): Promise<GetHydratedTaskResponse> => {
    const task = await this._taskManager.getHydratedTask(message.data.uuid);
    return {
      type: TaskWebViewMessageType.getHydratedTaskResponse,
      data: { task },
    };
  };

  /**
   * Creates a task.
   * @param message - The CreateTaskRequest message
   * @returns A promise that resolves to a CreateTaskResponse
   */
  private _createTask = async (
    message: CreateTaskRequest,
  ): Promise<CreateTaskResponse> => {
    // Create the task using the task manager
    // Note: TaskManager.createTask already handles updating the parent task's subtasks list
    const newUuid = await this._taskManager.createTask(
      message.data.name,
      message.data.description,
      message.data.parentTaskUuid,
    );

    return {
      type: TaskWebViewMessageType.createTaskResponse,
      data: { uuid: newUuid },
    };
  };

  /**
   * Updates a task.
   * @param message - The UpdateTaskRequest message
   * @returns A promise that resolves to an UpdateTaskResponse
   */
  private _updateTask = async (
    message: UpdateTaskRequest,
  ): Promise<UpdateTaskResponse> => {
    await this._taskManager.updateTask(
      message.data.uuid,
      message.data.updates,
      message.data.updatedBy,
    );
    return {
      type: TaskWebViewMessageType.updateTaskResponse,
    };
  };

  /**
   * Updates a hydrated task tree by diffing it against the existing tree.
   * @param message - The UpdateHydratedTaskRequest message
   * @returns A promise that resolves to an UpdateHydratedTaskResponse
   */
  private _updateHydratedTask = async (
    message: UpdateHydratedTaskRequest,
  ): Promise<UpdateHydratedTaskResponse> => {
    const { task: newTree, updatedBy } = message.data;

    // Use the TaskManager's updateHydratedTask method
    const result = await this._taskManager.updateHydratedTask(
      newTree,
      updatedBy,
    );

    return {
      type: TaskWebViewMessageType.updateHydratedTaskResponse,
      data: result,
    };
  };
}
