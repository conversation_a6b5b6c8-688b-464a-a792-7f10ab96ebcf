import { diffLines } from "diff";
import { DocumentCheckpoint, SerializedStore } from "./agent-edit-types";
import { QualifiedPathName } from "../workspace/qualified-path-name";
import { AugmentLogger } from "../logging";
import { DiffViewDocument, isUntitledFileFn } from "../diff-view/document";
import { ChatAgentFileChanges } from "../chat/chat-types";

/**
 * Normalizes a checkpoint range to valid bounds.
 * - Converts undefined to 0 (fromIdx) or totalCheckpoints-1 (toIdx)
 * - Clamps values to [0, totalCheckpoints)
 * - Handles reversed ranges (where fromIdx > toIdx)
 *
 * @param totalCheckpoints Total number of checkpoints available
 * @param fromIdx Starting index (inclusive, optional)
 * @param toIdx Ending index (inclusive, optional)
 * @returns Normalized [fromIdx, toIdx] tuple or undefined if range cannot be normalized
 */
export function normalizeCheckpointRange(
  totalCheckpoints: number,
  fromIdx?: number,
  toIdx?: number,
): [number, number] | undefined {
  if (!canNormalizeCheckpointRange(totalCheckpoints, fromIdx, toIdx)) {
    return undefined;
  }

  const normalizedFrom =
    fromIdx === undefined
      ? 0
      : Math.max(0, Math.min(fromIdx, totalCheckpoints - 1));
  const normalizedTo =
    toIdx === undefined
      ? totalCheckpoints - 1
      : Math.max(0, Math.min(toIdx, totalCheckpoints - 1));

  // Return indices in the order they were provided (don't sort)
  return [normalizedFrom, normalizedTo];
}

/**
 * Validates a checkpoint range, ensuring:
 * - fromIdx can be clamped to [0, totalCheckpoints)
 * - toIdx can be clamped to [0, totalCheckpoints)
 * - at least one of the indices is within bounds
 *
 * @param totalCheckpoints Total number of checkpoints available
 * @param fromIdx Starting index (inclusive, optional)
 * @param toIdx Ending index (inclusive, optional)
 * @returns true if the range is valid or can be normalized to a valid range
 */
export function canNormalizeCheckpointRange(
  totalCheckpoints: number,
  fromIdx?: number,
  toIdx?: number,
): boolean {
  // Helper to check if an index is within bounds
  const isInBounds = (idx: number) => idx >= 0 && idx < totalCheckpoints;

  // If both indices are undefined, the range is valid
  if (fromIdx === undefined && toIdx === undefined) {
    return true;
    // If fromIdx is undefined, make sure toIdx is within bounds
  } else if (fromIdx === undefined) {
    return toIdx !== undefined && isInBounds(toIdx);
    // If toIdx is undefined, make sure fromIdx is within bounds
  } else if (toIdx === undefined) {
    return fromIdx !== undefined && isInBounds(fromIdx);
  }

  // Clamp the bounds
  fromIdx = fromIdx ?? 0;
  toIdx = toIdx ?? totalCheckpoints - 1;

  // Check if at least one index is within range
  return isInBounds(fromIdx) || isInBounds(toIdx);
}

/**
 * Checks if a checkpoint is within a given timestamp range.
 * Both minTimestamp and maxTimestamp are inclusive in the range.
 *
 * @param checkpoint The checkpoint to check
 * @param minTimestamp Minimum timestamp (inclusive, optional)
 * @param maxTimestamp Maximum timestamp (inclusive, optional)
 * @returns true if the checkpoint is within the timestamp range, false otherwise
 */
export function isCheckpointInTimeRange(
  checkpoint: DocumentCheckpoint,
  minTimestamp?: number,
  maxTimestamp?: number,
): boolean {
  if (minTimestamp && checkpoint.timestamp < minTimestamp) return false;
  if (maxTimestamp && checkpoint.timestamp > maxTimestamp) return false;
  return true;
}

/**
 * Gets a single checkpoint by index from a source ID array.
 *
 * @param sourceIds Array of source tool call request IDs
 * @param checkpointMap Map of source IDs to checkpoints
 * @param index Index to retrieve
 * @returns The checkpoint at the specified index or undefined if not found
 */
export function getCheckpointByIndex(
  sourceIds: string[],
  checkpointMap: Map<string, DocumentCheckpoint>,
  index: number,
): DocumentCheckpoint | undefined {
  if (index >= sourceIds.length) return undefined;
  return checkpointMap.get(sourceIds[index]);
}

/**
 * Represents a serialized checkpoint that can be stored persistently.
 */
export interface SerializedCheckpoint {
  sourceToolCallRequestId: string;
  timestamp: number;
  filePath: QualifiedPathName;
  originalCode: string | undefined;
  modifiedCode: string | undefined;
}

/**
 * Serializes a checkpoint for storage.
 *
 * @param checkpoint The checkpoint to serialize
 * @returns A serialized representation of the checkpoint
 */
export function serializeCheckpoint(
  checkpoint: DocumentCheckpoint,
): SerializedCheckpoint {
  return {
    sourceToolCallRequestId: checkpoint.sourceToolCallRequestId,
    timestamp: checkpoint.timestamp,
    filePath: checkpoint.document.filePath,
    originalCode: checkpoint.document.originalCode,
    modifiedCode: checkpoint.document.modifiedCode,
  };
}

/**
 * Creates a store-ready checkpoint from serialized data.
 *
 * @param serialized The serialized checkpoint data
 * @param logger Logger instance for the new document
 * @returns A new DocumentCheckpoint instance
 */
export function deserializeCheckpoint(
  serialized: SerializedCheckpoint,
  isUntitled: isUntitledFileFn,
  logger: AugmentLogger,
): DocumentCheckpoint {
  const doc = new DiffViewDocument(
    QualifiedPathName.from(serialized.filePath),
    serialized.originalCode,
    serialized.modifiedCode,
    {
      logger,
      isUntitled,
    },
  );

  return {
    sourceToolCallRequestId: serialized.sourceToolCallRequestId,
    timestamp: serialized.timestamp,
    document: doc,
    conversationId: "",
  };
}

/**
 * Serializes checkpoints by path for storage.
 *
 * @param checkpointIds Map of paths to source ID arrays
 * @param sourceToCheckpoint Map of source IDs to checkpoints
 * @returns A serialized store object
 */
export function serializeCheckpointsByPath(
  checkpointIds: Map<string, string[]>,
  sourceToCheckpoint: Map<string, DocumentCheckpoint>,
): SerializedStore {
  const serialized: SerializedStore = { checkpointsByPath: {} };

  for (const [path, ids] of checkpointIds) {
    const checkpoints = ids
      .map((id) => sourceToCheckpoint.get(id))
      .filter((c): c is DocumentCheckpoint => c !== undefined)
      .map(serializeCheckpoint);

    if (checkpoints.length > 0) {
      serialized.checkpointsByPath[path] = checkpoints;
    }
  }

  return serialized;
}

/**
 * Computes line-level changes between two checkpoints.
 *
 * @param checkpoint1 First checkpoint
 * @param checkpoint2 Second checkpoint
 * @returns Object containing change statistics and detailed changes
 */
export function computeCheckpointChanges(
  checkpoint1: DocumentCheckpoint,
  checkpoint2: DocumentCheckpoint,
): ChatAgentFileChanges {
  // If first checkpoint is *after* last checkpoint, we need to swap the original and modified code
  if (checkpoint1.timestamp > checkpoint2.timestamp) {
    // Compute the swapped version, then swap the added and removed lines
    const changes = computeCheckpointChanges(checkpoint2, checkpoint1);
    return {
      totalAddedLines: changes.totalRemovedLines,
      totalRemovedLines: changes.totalAddedLines,
      changes: changes.changes,
    };
  }

  const changes = diffLines(
    checkpoint1.document.originalCode ?? "",
    checkpoint2.document.modifiedCode ?? "",
  );

  const fileChanges: ChatAgentFileChanges = {
    totalAddedLines: 0,
    totalRemovedLines: 0,
    changes,
  };

  for (const change of changes) {
    if (change.added) {
      fileChanges.totalAddedLines += change.count || 0;
    }
    if (change.removed) {
      fileChanges.totalRemovedLines += change.count || 0;
    }
  }

  return fileChanges;
}

/**
 * Checks if two checkpoints are for the same file.
 *
 * @param checkpoint1 First checkpoint
 * @param checkpoint2 Second checkpoint
 * @returns true if checkpoints are for the same file
 */
export function areSameFileCheckpoints(
  checkpoint1: DocumentCheckpoint,
  checkpoint2: DocumentCheckpoint,
): boolean {
  return checkpoint1.document.filePath.equals(checkpoint2.document.filePath);
}
