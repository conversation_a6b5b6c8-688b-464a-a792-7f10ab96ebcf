import { Analytics } from "@segment/analytics-node";
import { getLogger, AugmentLogger } from "../logging";

export interface AnalyticsContext {
  clientType: "vscode" | "intellij" | "vim";
  clientVersion: string;
  platform: string;
  arch: string;
  tenantId?: string;
}

export class SidecarAnalytics {
  private _analytics: Analytics | undefined;
  private _logger: AugmentLogger = getLogger("SidecarAnalytics");
  private _isInitialized = false;
  private _context: AnalyticsContext | undefined;
  private _currentUserId: string | undefined;
  private _anonymousId: string | undefined;

  // List of property names that might contain email addresses
  private readonly _emailPropertyNames = new Set([
    "email",
    "emailaddress",
    "email_address",
    "useremail",
    "user_email",
  ]);

  // List of property names that might contain names
  private readonly _namePropertyNames = new Set([
    "name",
    "fullname",
    "full_name",
    "firstname",
    "first_name",
    "lastname",
    "last_name",
    "displayname",
    "display_name",
    "username",
    "user_name",
  ]);

  constructor(private _writeKey: string) {}

  public initialize(context: AnalyticsContext, anonymousId: string): void {
    this._context = context;
    this._anonymousId = anonymousId;

    // Only initialize if write key is provided (empty string = disabled)
    if (!this._writeKey || this._writeKey.trim() === "") {
      this._logger.debug("Analytics disabled - no Segment write key provided");
      return;
    }

    try {
      this._analytics = new Analytics({
        writeKey: this._writeKey,
        flushAt: 10,
        flushInterval: 10000,
      });

      this._isInitialized = true;
      this._logger.info(
        `Segment analytics initialized for ${context.clientType} in tenant ${context.tenantId}`,
      );
    } catch (error) {
      this._logger.error("Failed to initialize Segment analytics:", error);
    }
  }

  public identifyUser(userId: string, traits?: Record<string, any>): void {
    if (
      !this._isInitialized ||
      !this._analytics ||
      !this._context ||
      !this._anonymousId
    ) {
      return;
    }

    try {
      this._currentUserId = userId;

      const traitsToSend = traits ? { ...traits } : {};
      const { filtered: filteredTraits, piiDetected } =
        this._filterPII(traitsToSend);

      if (piiDetected) {
        this._logger.warn(
          "PII detected and filtered from user identification traits",
        );
      }

      this._analytics.identify({
        userId,
        anonymousId: this._anonymousId,
        traits: {
          ...filteredTraits,
          clientType: this._context.clientType,
          clientVersion: this._context.clientVersion,
          platform: this._context.platform,
          tenantId: this._context.tenantId,
        },
      });

      this._logger.debug("User identified in analytics");
    } catch (error) {
      this._logger.error("Failed to identify user:", error);
    }
  }

  public trackEvent(eventName: string, properties?: Record<string, any>): void {
    if (
      !this._isInitialized ||
      !this._analytics ||
      !this._context ||
      !this._anonymousId
    ) {
      return;
    }

    try {
      const propsToSend = properties ? { ...properties } : {};
      const { filtered: filteredProperties, piiDetected } =
        this._filterPII(propsToSend);

      if (piiDetected) {
        this._logger.warn(
          `PII detected and filtered from event properties for event: ${eventName}`,
        );
      }

      this._analytics.track({
        userId: this._currentUserId,
        anonymousId: this._anonymousId,
        event: eventName,
        properties: {
          ...filteredProperties,
          clientType: this._context.clientType,
          clientVersion: this._context.clientVersion,
          platform: this._context.platform,
          tenantId: this._context.tenantId,
        },
      });

      this._logger.debug(
        `Tracked event: ${eventName}, properties: ${JSON.stringify(
          filteredProperties,
        )} tenantId: ${this._context.tenantId}`,
      );
    } catch (error) {
      this._logger.error(`Failed to track event ${eventName}:`, error);
    }
  }

  public async dispose(): Promise<void> {
    if (this._analytics) {
      try {
        await this._analytics.closeAndFlush();
        this._logger.debug("Analytics disposed successfully");
      } catch (error) {
        this._logger.error("Failed to dispose analytics:", error);
      }
    }
  }

  /**
   * Filters PII from the given data object by removing top-level properties which could contain PII.
   * @param data The data to filter
   * @returns A new object with PII removed and a boolean indicating if PII was found
   */
  private _filterPII(data: Record<string, any>): {
    filtered: Record<string, any>;
    piiDetected: boolean;
  } {
    let piiDetected = false;
    const filteredProperties: string[] = [];
    const filtered: Record<string, any> = {};

    for (const [key, value] of Object.entries(data)) {
      // Check if the property name contains sensitive terms (case-insensitive)
      const normalizedKey = key.toLowerCase().replace(/[-_]/g, "");

      if (
        this._emailPropertyNames.has(normalizedKey) ||
        this._namePropertyNames.has(normalizedKey)
      ) {
        piiDetected = true;
        filteredProperties.push(key);
        // Skip this property entirely
        continue;
      }

      // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
      filtered[key] = value;
    }

    if (filteredProperties.length > 0) {
      this._logger.debug(
        `Filtered PII properties: ${filteredProperties.join(", ")}`,
      );
    }

    return { filtered, piiDetected };
  }
}
