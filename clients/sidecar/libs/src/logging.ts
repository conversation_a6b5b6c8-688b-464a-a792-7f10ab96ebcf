// We restrict the logger API to the subset that we use.
// This avoids a footgun: https://github.com/winstonjs/winston/issues/1711
export interface AugmentLogger {
  /* eslint-disable @typescript-eslint/no-explicit-any */
  log: (level: string, message: string, ...args: any[]) => void;
  info: (message: string, ...args: any[]) => void;
  debug: (message: string, ...args: any[]) => void;
  warn: (message: string, ...args: any[]) => void;
  error: (message: string, ...args: any[]) => void;
  verbose: (message: string, ...args: any[]) => void;
  /* eslint-enable @typescript-eslint/no-explicit-any */
}

export interface WinstonLogger {
  child: (options: { prefix: string }) => AugmentLogger;
  warn: (message: string) => void;
}

const isWinstonLogger = (logger: any): logger is WinstonLogger => {
  return (
    !!logger &&
    typeof (logger as <PERSON><PERSON><PERSON><PERSON>).child === "function" &&
    typeof (logger as <PERSON><PERSON><PERSON><PERSON>).warn === "function"
  );
};

// Private variable to hold the configured logger
let configuredLogger: WinstonLogger | AugmentLogger | undefined;

/**
 * Sets the logger implementation to be used by the library. This is expected
 * to be initialized by the different clients at logger initialization time.
 * This must be called before any calls to getLogger().
 */
export function setLibraryLogger(logger: WinstonLogger | AugmentLogger): void {
  if (configuredLogger) {
    configuredLogger.warn(
      "Attempting to initialize logger when one is already configured. Keeping existing logger.",
    );
    return;
  }
  configuredLogger = logger;
}

function createChildLogger(
  prefix: string,
  augmentLogger: AugmentLogger,
): AugmentLogger {
  return {
    /* eslint-disable @typescript-eslint/no-unsafe-argument */
    log: (level: string, message: string, ...args: any[]) => {
      augmentLogger.log(level, `[${prefix}] ${message}`, ...args);
    },
    info: (message: string, ...args: any[]) => {
      augmentLogger.info(`[${prefix}] ${message}`, ...args);
    },
    debug: (message: string, ...args: any[]) => {
      augmentLogger.debug(`[${prefix}] ${message}`, ...args);
    },
    warn: (message: string, ...args: any[]) => {
      augmentLogger.warn(`[${prefix}] ${message}`, ...args);
    },
    error: (message: string, ...args: any[]) => {
      augmentLogger.error(`[${prefix}] ${message}`, ...args);
    },
    verbose: (message: string, ...args: any[]) => {
      augmentLogger.verbose(`[${prefix}] ${message}`, ...args);
    },
    /* eslint-enable @typescript-eslint/no-unsafe-argument */
  };
}

/**
 * Gets a logger instance with the specified prefix.
 * @param prefix - The prefix to be added to all log messages
 * @throws Error if setLogger() hasn't been called
 */
export function getLogger(prefix: string): AugmentLogger {
  if (!configuredLogger) {
    throw new Error(
      "Logger not initialized. Call setLibraryLogger() before using getLogger().",
    );
  }

  // Return a wrapped version of the logger that adds the prefix
  if (isWinstonLogger(configuredLogger)) {
    return configuredLogger.child({ prefix });
  } else {
    return createChildLogger(prefix, configuredLogger);
  }
}
