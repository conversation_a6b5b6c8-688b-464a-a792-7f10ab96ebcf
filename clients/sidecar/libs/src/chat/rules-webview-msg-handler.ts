import * as fs from "fs";
import * as path from "path";
import { getLogger } from "../logging";
import {
  CommonWebViewMessageType,
  WebViewMessage,
} from "../webview-messages/common-webview-messages";
import {
  IWebviewMessageConsumer,
  PostMessageFn,
} from "../webview-messages/webview-messages-broker";
import { RuleType } from "./chat-types";
import { RulesService } from "./rules-service";
import {
  RulesWebViewMessageType,
  GetRulesListRequest,
  GetRulesListResponse,
  CreateRuleRequest,
  CreateRuleResponse,
  DeleteRuleRequest,
  DeleteRuleResponse,
  UpdateRuleFileRequest,
  UpdateRuleFileResponse,
  GetWorkspaceRootRequest,
  GetWorkspaceRootResponse,
  AutoImportRulesRequest,
  AutoImportRulesOptionsResponse,
  ProcessSelectedPathsRequest,
  ProcessSelectedPathsResponse,
  AutoImportRulesSelectionRequest,
  AutoImportRulesResponse,
} from "../webview-messages/message-types/rules-messages";
import { getClientWorkspaces } from "../client-interfaces/client-workspaces";

/**
 * Handles rules-related webview messages in the sidecar.
 * This replaces the message handling that was previously done in settings-panel.ts
 */
export class RulesWebviewMessageHandler
  implements IWebviewMessageConsumer<RulesWebViewMessageType>
{
  private _logger = getLogger("RulesWebviewMessageHandler");
  private _rulesService: RulesService;

  constructor(rulesService: RulesService) {
    this._rulesService = rulesService;
  }

  get supportedTypes(): Record<string, RulesWebViewMessageType> {
    return RulesWebViewMessageType;
  }

  /**
   * Handles incoming webview messages.
   * @param msg - The incoming message
   * @param postMessage - Function to post a response message
   */
  public async handle(
    msg: WebViewMessage<RulesWebViewMessageType>,
    postMessage: PostMessageFn<
      RulesWebViewMessageType | CommonWebViewMessageType
    >,
  ): Promise<void> {
    switch (msg.type) {
      case RulesWebViewMessageType.getRulesListRequest: {
        const response = await this._getRulesList(msg as GetRulesListRequest);
        postMessage(response);
        break;
      }
      case RulesWebViewMessageType.createRule: {
        const response = await this._createRule(msg as CreateRuleRequest);
        postMessage(response);
        break;
      }
      case RulesWebViewMessageType.deleteRule: {
        const response = await this._deleteRule(msg as DeleteRuleRequest);
        postMessage(response);
        break;
      }
      case RulesWebViewMessageType.updateRuleFile: {
        const response = await this._updateRuleFile(
          msg as UpdateRuleFileRequest,
        );
        postMessage(response);
        break;
      }
      case RulesWebViewMessageType.getWorkspaceRoot: {
        const response = await this._getWorkspaceRoot(
          msg as GetWorkspaceRootRequest,
        );
        postMessage(response);
        break;
      }
      case RulesWebViewMessageType.autoImportRules: {
        const response = await this._autoImportRules(
          msg as AutoImportRulesRequest,
        );
        postMessage(response);
        break;
      }
      case RulesWebViewMessageType.autoImportRulesSelectionRequest: {
        const response = await this._processAutoImportSelection(
          msg as AutoImportRulesSelectionRequest,
        );
        postMessage(response);
        break;
      }
      case RulesWebViewMessageType.processSelectedPathsRequest: {
        const response = await this._processSelectedPaths(
          msg as ProcessSelectedPathsRequest,
        );
        postMessage(response);
        break;
      }
      default:
        this._logger.warn(`Unhandled message type: ${msg.type}`);
    }
  }

  /**
   * Get the list of rules
   */
  private async _getRulesList(
    msg: GetRulesListRequest,
  ): Promise<GetRulesListResponse> {
    try {
      const includeGuidelines = Boolean(msg.data?.includeGuidelines ?? false);
      const query = msg.data?.query;
      const maxResults = msg.data?.maxResults;
      const contextRules = msg.data?.contextRules;

      this._logger.debug(
        `Handling getRulesList request with includeGuidelines=${includeGuidelines}, query=${query}, maxResults=${maxResults}, contextRules=${contextRules?.length || 0}`,
      );

      const rules = await this._rulesService.loadRules({
        includeGuidelines,
        query,
        maxResults,
        contextRules,
      });

      this._logger.debug(`Returning ${rules.length} rules in response`);
      return {
        type: RulesWebViewMessageType.getRulesListResponse,
        data: {
          rules: rules,
        },
      };
    } catch (error) {
      this._logger.error(`Failed to get rules list: ${String(error)}`);
      throw error;
    }
  }

  /**
   * Create a new rule
   */
  private async _createRule(
    msg: CreateRuleRequest,
  ): Promise<CreateRuleResponse> {
    try {
      // The actual input dialog will be handled by the VSCode extension
      // This expects the rule name to be passed in the message
      if (!msg.data?.ruleName) {
        throw new Error("Rule name is required");
      }

      const rule = await this._rulesService.createRule({
        path: msg.data.ruleName,
        content: "",
        type: RuleType.MANUAL,
      });

      if (!rule) {
        throw new Error("Failed to create rule");
      }

      // Get the workspace root to construct the proper file path
      const workspaces = getClientWorkspaces();
      let workspaceRoot = "";
      if (workspaces) {
        workspaceRoot = (await workspaces.getWorkspaceRoot()) || "";
      }

      return {
        type: RulesWebViewMessageType.createRuleResponse,
        data: {
          importedRulesCount: 1,
          createdRule: {
            path: rule.path,
            repoRoot: workspaceRoot,
          },
        },
      };
    } catch (error) {
      this._logger.error(`Failed to create rule: ${String(error)}`);
      throw error;
    }
  }

  /**
   * Delete a rule
   */
  private async _deleteRule(
    msg: DeleteRuleRequest,
  ): Promise<DeleteRuleResponse> {
    try {
      // The confirmation dialog will be handled by the VSCode extension
      // This expects the confirmation to already be done
      if (msg.data.confirmed) {
        await this._rulesService.deleteRule(msg.data.path);
      }
      return {
        type: RulesWebViewMessageType.deleteRule,
      };
    } catch (error) {
      this._logger.error(`Failed to delete rule: ${String(error)}`);
      throw error;
    }
  }

  /**
   * Update a rule file
   */
  private async _updateRuleFile(
    msg: UpdateRuleFileRequest,
  ): Promise<UpdateRuleFileResponse> {
    try {
      await this._rulesService.updateRuleFile(msg.data.path, msg.data.content);
      return {
        type: RulesWebViewMessageType.updateRuleFileResponse,
        data: {
          success: true,
        },
      };
    } catch (error) {
      this._logger.error(`Failed to update rule file: ${String(error)}`);
      return {
        type: RulesWebViewMessageType.updateRuleFileResponse,
        data: {
          success: false,
          error: String(error),
        },
      };
    }
  }

  /**
   * Get the workspace root
   */
  private async _getWorkspaceRoot(
    _msg: GetWorkspaceRootRequest,
  ): Promise<GetWorkspaceRootResponse> {
    try {
      const workspaces = getClientWorkspaces();
      let workspaceRoot = "";
      if (workspaces) {
        workspaceRoot = (await workspaces.getWorkspaceRoot()) || "";
      }
      return {
        type: RulesWebViewMessageType.getWorkspaceRootResponse,
        data: {
          workspaceRoot,
        },
      };
    } catch (error) {
      this._logger.error(`Failed to get workspace root: ${String(error)}`);
      return {
        type: RulesWebViewMessageType.getWorkspaceRootResponse,
        data: {
          workspaceRoot: "",
        },
      };
    }
  }

  /**
   * Auto import rules - detect available options and return them
   */
  private async _autoImportRules(
    _msg: AutoImportRulesRequest,
  ): Promise<AutoImportRulesOptionsResponse> {
    try {
      const options = await this._rulesService.detectAutoImportOptions();
      return {
        type: RulesWebViewMessageType.autoImportRulesOptionsResponse,
        data: { options },
      };
    } catch (error) {
      this._logger.error(
        `Failed to detect auto import options: ${String(error)}`,
      );
      throw error;
    }
  }

  /**
   * Process auto import rules selection
   */
  private async _processAutoImportSelection(
    msg: AutoImportRulesSelectionRequest,
  ): Promise<AutoImportRulesResponse> {
    try {
      const result = await this._rulesService.processAutoImportSelection(
        msg.data.selectedLabel,
      );
      return {
        type: RulesWebViewMessageType.autoImportRulesResponse,
        data: result,
      };
    } catch (error) {
      this._logger.error(
        `Failed to process auto import selection: ${String(error)}`,
      );
      throw error;
    }
  }

  /**
   * Process selected paths from the import dialog
   */
  private async _processSelectedPaths(
    msg: ProcessSelectedPathsRequest,
  ): Promise<ProcessSelectedPathsResponse> {
    const selectedPaths: string[] = msg.data.selectedPaths;
    const autoImport: boolean = msg.data.autoImport;
    this._logger.debug(
      `Processing ${selectedPaths.length} selected paths for import`,
    );

    if (selectedPaths.length === 0) {
      return {
        type: RulesWebViewMessageType.processSelectedPathsResponse,
        data: {
          importedRulesCount: 0,
          directoryOrFile: "file" as const,
          errors: ["No paths selected"],
        },
      } as ProcessSelectedPathsResponse;
    }

    try {
      const workspaces = getClientWorkspaces();
      if (!workspaces) {
        throw new Error("Client workspaces not initialized");
      }

      const workspaceRoot = await workspaces.getWorkspaceRoot();
      if (!workspaceRoot) {
        throw new Error("No workspace root found");
      }

      let totalImportedCount = 0;
      const errors: string[] = [];
      let hasFiles = false;
      let hasDirectories = false;

      // Process each selected path
      for (const selectedPath of selectedPaths) {
        let pathToImport: string;
        let fullPath: string;

        // Determine the path to use for import and validation
        if (path.isAbsolute(selectedPath)) {
          // For absolute paths, use them directly
          pathToImport = selectedPath;
          fullPath = selectedPath;
          this._logger.debug(`Processing absolute path: ${selectedPath}`);
        } else {
          // For relative paths, resolve against workspace root
          pathToImport = selectedPath;
          fullPath = path.resolve(workspaceRoot, selectedPath);
          this._logger.debug(
            `Processing relative path: ${selectedPath} -> ${fullPath}`,
          );
        }

        try {
          if (!fs.existsSync(fullPath)) {
            errors.push(`Path ${selectedPath} does not exist`);
            continue;
          }

          const stats = fs.statSync(fullPath);
          if (stats.isDirectory()) {
            hasDirectories = true;
            const result = await this._rulesService.importDirectory(
              pathToImport,
              autoImport,
            );
            totalImportedCount += result.successfulImports;
            this._logger.info(
              `Imported ${result.successfulImports} rules from directory: ${selectedPath}`,
            );
          } else if (stats.isFile()) {
            hasFiles = true;
            const result = await this._rulesService.importFile(
              pathToImport,
              autoImport,
            );
            totalImportedCount += result.successfulImports;
            this._logger.info(
              `Imported ${result.successfulImports} rules from file: ${selectedPath}`,
            );
          } else {
            errors.push(`Path ${selectedPath} is neither a file nor directory`);
          }
        } catch (error) {
          const errorMsg = `Failed to import ${selectedPath}: ${String(error)}`;
          this._logger.error(errorMsg);
          errors.push(errorMsg);
        }
      }

      // Determine the type based on what was processed
      let directoryOrFile: "file" | "directory" | "mixed";
      if (hasFiles && hasDirectories) {
        directoryOrFile = "mixed";
      } else if (hasDirectories) {
        directoryOrFile = "directory";
      } else {
        directoryOrFile = "file";
      }

      this._logger.debug(
        `Import completed: ${totalImportedCount} rules imported, ${errors.length} errors`,
      );

      return {
        type: RulesWebViewMessageType.processSelectedPathsResponse,
        data: {
          importedRulesCount: totalImportedCount,
          directoryOrFile,
          errors: errors.length > 0 ? errors : undefined,
        },
      } as ProcessSelectedPathsResponse;
    } catch (error) {
      this._logger.error(`Failed to process selected paths: ${String(error)}`);
      return {
        type: RulesWebViewMessageType.processSelectedPathsResponse,
        data: {
          importedRulesCount: 0,
          directoryOrFile: "file" as const,
          errors: [String(error)],
        },
      } as ProcessSelectedPathsResponse;
    }
  }

  /**
   * Dispose of resources
   */
  public dispose(): void {
    // No resources to dispose
  }
}
