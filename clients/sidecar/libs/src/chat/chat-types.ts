import { Change } from "diff";
import { ToolDefinition } from "@augment-internal/sidecar-libs/src/tools/tool-types";

/**
 * The type definitions in this file are used in a few places.
 *
 * Some are used when mapping Augment APIs -> Types
 * Some are used when defining webview messages
 * Some are used for both
 *
 * We should clean this up to be easier to understand.
 */
export type Exchange = {
  /* eslint-disable @typescript-eslint/naming-convention */
  request_message: string;
  response_text: string;
  request_id: string;
  request_nodes?: ChatRequestNode[];
  response_nodes?: ChatResultNode[];
  rich_text_json_repr?: any; // JSONContent type from @tiptap/core, using any to avoid dependency
  mentioned_items?: any[]; // IChatMentionable[] type, using any to avoid circular dependency
  /* eslint-enable @typescript-eslint/naming-convention */
};

export enum ChatRequestNodeType {
  /* eslint-disable @typescript-eslint/naming-convention */
  TEXT = 0,
  TOOL_RESULT = 1,
  IMAGE = 2, // JPEG, PNG (Default: PNG)
  IMAGE_ID = 3,
  IDE_STATE = 4,
  EDIT_EVENTS = 5,
  CHECKPOINT_REF = 6, // Client-only node betwen webview and extension
  CHANGE_PERSONALITY = 7, // Client-only node for changing personality
  FILE = 8, // Client- only placeholder for now, implement in backend when uploading pdfs
  FILE_ID = 9, // Client-only node for referencing a document
  /* eslint-enable @typescript-eslint/naming-convention */
}

export enum ImageFormatType {
  IMAGE_FORMAT_UNSPECIFIED = 0,
  PNG = 1,
  JPEG = 2,
  GIF = 3,
  WEBP = 4,
}

export interface ChatRequestImageId {
  /* eslint-disable @typescript-eslint/naming-convention */
  image_id: string; // The image ID in the asset manager
  format: ImageFormatType;
  /* eslint-enable @typescript-eslint/naming-convention */
}

export interface WorkspaceFolderInfo {
  /* eslint-disable @typescript-eslint/naming-convention */
  // NOTE(arun): The repository root is the first ancestor directory containing either
  // an `.augmentroot` or `.git` directory. The folder root is the workspace folder
  // root as defined by the IDE. It's guaranteed that the repository root is the same or
  // a parent of the folder root.
  repository_root: string;
  folder_root: string;
  /* eslint-enable @typescript-eslint/naming-convention */
}

export interface TerminalInfo {
  /* eslint-disable @typescript-eslint/naming-convention */
  terminal_id: number;
  current_working_directory: string;
  /* eslint-enable @typescript-eslint/naming-convention */
}

export interface ChatRequestIdeState {
  /* eslint-disable @typescript-eslint/naming-convention */
  // NOTE(arun): At some point, we will want to sparsely populate `workspace_folders`
  // to avoid large payload sizes. The API supports this by sending an empty list and
  // setting `workspace_folders_unchanged` to true.
  workspace_folders: WorkspaceFolderInfo[];
  workspace_folders_unchanged: boolean;
  current_terminal?: TerminalInfo;
  /* eslint-enable @typescript-eslint/naming-convention */
}

export interface ChatRequestImage {
  /* eslint-disable @typescript-eslint/naming-convention */
  image_data: string; // Base64 encoded image data
  format: ImageFormatType;
  /* eslint-enable @typescript-eslint/naming-convention */
}

export interface ChatRequestSingleEdit {
  /* eslint-disable @typescript-eslint/naming-convention */
  before_line_start: number;
  before_text: string;
  after_line_start: number;
  after_text: string;
  /* eslint-enable @typescript-eslint/naming-convention */
}

export interface ChatRequestFileEdit {
  /* eslint-disable @typescript-eslint/naming-convention */
  path: string;
  before_blob_name?: string;
  after_blob_name?: string;
  edits: ChatRequestSingleEdit[];
  /* eslint-enable @typescript-eslint/naming-convention */
}

export enum EditEventSource {
  /* eslint-disable @typescript-eslint/naming-convention */
  UNSPECIFIED = 0,
  USER_EDIT = 1,
  CHECKPOINT_REVERT = 2,
  /* eslint-enable @typescript-eslint/naming-convention */
}

export interface ChatRequestEditEvents {
  /* eslint-disable @typescript-eslint/naming-convention */
  edit_events: ChatRequestFileEdit[];
  source?: EditEventSource;
  /* eslint-enable @typescript-eslint/naming-convention */
}

/**
 * Reference to a checkpoint that needs to be hydrated on the extension side
 */
export interface ChatRequestCheckpointRef {
  /* eslint-disable @typescript-eslint/naming-convention */
  request_id: string; // The request ID associated with the checkpoint
  from_timestamp: number; // Start timestamp of the checkpoint
  to_timestamp: number; // End timestamp of the checkpoint
  source?: EditEventSource; // Source of the checkpoint (e.g., CHECKPOINT_REVERT for reversion)
  /* eslint-enable @typescript-eslint/naming-convention */
}

/**
 * Interface for changing the personality of the AI assistant
 */
export interface ChatRequestChangePersonality {
  /* eslint-disable @typescript-eslint/naming-convention */
  personality_type: PersonaType;
  custom_instructions?: string; // Optional field for custom personalities
  /* eslint-enable @typescript-eslint/naming-convention */
}

export interface ChatRequestFile {
  /* eslint-disable @typescript-eslint/naming-convention */
  file_data: string; // Base64 encoded file data
  format: string; // MIME type of the file
  /* eslint-enable @typescript-eslint/naming-convention */
}

export interface ChatRequestFileId {
  /* eslint-disable @typescript-eslint/naming-convention */
  file_id: string; // The file ID in the asset manager
  file_name: string; // The original file name
}

export interface ChatRequestNode {
  id: number;
  type: ChatRequestNodeType;
  /* eslint-disable @typescript-eslint/naming-convention */
  text_node?: ChatRequestText;
  tool_result_node?: ChatRequestToolResult;
  image_node?: ChatRequestImage;
  image_id_node?: ChatRequestImageId;
  ide_state_node?: ChatRequestIdeState;
  edit_events_node?: ChatRequestEditEvents;
  checkpoint_ref_node?: ChatRequestCheckpointRef; // Client-only node betwen webview and extension
  change_personality_node?: ChatRequestChangePersonality; // Client-only node for changing personality
  file_node?: ChatRequestFile;
  file_id_node?: ChatRequestFileId;
  /* eslint-enable @typescript-eslint/naming-convention */
}

export interface ChatRequestText {
  content: string;
}

export enum ChatRequestContentNodeType {
  /* eslint-disable @typescript-eslint/naming-convention */
  CONTENT_TYPE_UNSPECIFIED = 0,
  CONTENT_TEXT = 1,
  CONTENT_IMAGE = 2,
  /* eslint-enable @typescript-eslint/naming-convention */
}

export interface ChatRequestContentNode {
  /* eslint-disable @typescript-eslint/naming-convention */
  type: ChatRequestContentNodeType;
  text_content?: string;
  image_content?: ChatRequestImage;
  /* eslint-enable @typescript-eslint/naming-convention */
}

export interface ChatRequestToolResult {
  /* eslint-disable @typescript-eslint/naming-convention */
  tool_use_id: string;
  // Plain text content (deprecated when content_nodes is present)
  content: string;
  is_error: boolean;
  // If the tool is implemented by Augment services
  request_id?: string;
  // List of content nodes (text or images)
  // If present, takes precedence over content field
  content_nodes?: ChatRequestContentNode[];
  /* eslint-enable @typescript-eslint/naming-convention */
}

export interface ChatResultNode {
  /* eslint-disable @typescript-eslint/naming-convention */
  id: number;
  type: ChatResultNodeType;
  content: string;
  tool_use?: ChatResultToolUse;
  agent_memory?: ChatResultAgentMemory;
  requestId?: string;
  /* eslint-enable @typescript-eslint/naming-convention */
}

export enum ChatResultNodeType {
  /* eslint-disable @typescript-eslint/naming-convention */
  RAW_RESPONSE = 0,
  SUGGESTED_QUESTIONS = 1,
  MAIN_TEXT_FINISHED = 2,
  TOOL_USE = 5,
  AGENT_MEMORY = 6,
  TOOL_USE_START = 7,
  THINKING = 8,
  /* eslint-enable @typescript-eslint/naming-convention */
}

export interface ChatResultToolUse {
  /* eslint-disable @typescript-eslint/naming-convention */
  tool_use_id: string;
  tool_name: string;
  input_json: string;
  mcp_server_name?: string; // MCP server name for MCP tools
  mcp_tool_name?: string; // Original MCP tool name without server suffix
  requestId?: string;
  /* eslint-enable @typescript-eslint/naming-convention */
}

export interface ChatResultAgentMemory {
  content: string;
  isFlushed?: boolean;
}

/**
 * Memories-specific flags
 */
export interface MemoriesInfo {
  isClassifyAndDistill?: boolean;
  isDistill?: boolean;
  promptKey?: string; // Optional key for custom prompt selection
}

// Strings appropriate for use in Augment API calls
// Adding new value here without adding to public API is
// a breaking change
export enum ChatMode {
  chat = "CHAT",
  agent = "AGENT",
  remoteAgent = "REMOTE_AGENT",
  memories = "MEMORIES",
  orientation = "ORIENTATION",
  memoriesCompression = "MEMORIES_COMPRESSION",
  cliAgent = "CLI_AGENT",
}

export type ChatModeType = "chat" | "localAgent" | "remoteAgent";

// The persona type that the AI assistant should adopt for this chat turn.
export enum PersonaType {
  // The default persona, which is an expert software engineer.
  DEFAULT = 0,
  // The prototyper persona, which is an expert software engineer that is
  // focused on building new web apps.
  PROTOTYPER = 1,
  // The brainstorm persona, which is an expert software engineer that is
  // focused on planning and brainstorming solutions.
  BRAINSTORM = 2,
  // The reviewer persona, which is an expert software engineer that is
  // focused on reviewing code changes and identifying potential issues.
  REVIEWER = 3,
}

/**
 * Represents the changes between two file versions, including both
 * aggregate counts and detailed changes.
 */
export interface ChatAgentFileChanges {
  // Aggregate counts
  totalAddedLines: number;
  totalRemovedLines: number;
  // Detailed changes
  changes: Change[];
}

export type ReplacementText = {
  blob_name: string;
  path: string;
  char_start: number;
  char_end: number;
  replacement_text: string;
  present_in_blob: boolean;
  expected_blob_name: string;
  timestamp: string; // ISO string format
};
export type VCSChangePayload = {
  working_directory_changes: WorkingDirectoryChange[];
};
export type ChatPayload = {
  model?: string;
  path?: string;
  prefix?: string;
  selected_code?: string;
  suffix?: string;
  message: string;
  chat_history: Exchange[];
  lang?: string;
  blobs?: BlobsPayload;
  user_guided_blobs?: string[];
  external_source_ids?: string[];
  enable_preference_collection?: boolean;
  context_code_exchange_request_id?: string;
  vcs_change: VCSChangePayload;
  recency_info_recent_changes?: ReplacementText[];
  disable_auto_external_sources?: boolean;
  user_guidelines?: string;
  workspace_guidelines?: string;
  feature_detection_flags?: FeatureDetectionFlags;
  tool_definitions?: ToolDefinition[];
  nodes?: ChatRequestNode[];
  mode?: ChatMode;
  agent_memories?: string;
  persona_type?: PersonaType;
  rules?: Rule[];
  silent?: boolean;
};
export type BlobsPayload = {
  checkpoint_id: string | undefined;
  added_blobs: string[];
  deleted_blobs: string[];
};

export enum ChangeType {
  modified = "MODIFIED",
  added = "ADDED",
  deleted = "DELETED",
  renamed = "RENAMED",
}

type WorkingDirectoryChange = {
  after_path?: string;
  before_path?: string;
  change_type: ChangeType;
  head_blob_name?: string;
  indexed_blob_name?: string;
  current_blob_name?: string;
};
type FeatureDetectionFlags = {
  support_raw_output?: boolean; // Added Nov 2024
  support_tool_use_start?: boolean;
  support_parallel_tool_use?: boolean;
};

export enum RuleType {
  ALWAYS_ATTACHED = 0,
  MANUAL = 1,
  AGENT_REQUESTED = 2,
}

export interface Rule {
  type: RuleType;
  path: string;
  content: string;
  description?: string;
}
