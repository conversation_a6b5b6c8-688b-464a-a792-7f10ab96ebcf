import {
  ChatRequestFileEdit,
  ChatRequestNode,
  ChatRequestNodeType,
  ChatRequestSingleEdit,
} from "./chat-types";
import { DiffViewDocument } from "../diff-view/document";
import { AggregateCheckpointInfo } from "../agent/checkpoint/checkpoint-types";

/**
 * Converts a string to an array of lines
 */
function getLines(text: string): string[] {
  return text.split(/\r?\n/);
}

/**
 * Calculates line-based diffs between original and modified code
 * Returns an array of ChatRequestSingleEdit objects
 */
export function calculateLineBasedEdits(
  originalCode: string | undefined,
  modifiedCode: string | undefined,
): ChatRequestSingleEdit[] {
  originalCode = originalCode ?? "";
  modifiedCode = modifiedCode ?? "";

  const originalLines = getLines(originalCode);
  const modifiedLines = getLines(modifiedCode);

  // Use a simple diff algorithm to find line-based changes
  const edits: ChatRequestSingleEdit[] = [];

  // Find common prefix
  let prefixLength = 0;
  const minLength = Math.min(originalLines.length, modifiedLines.length);
  while (
    prefixLength < minLength &&
    originalLines[prefixLength] === modifiedLines[prefixLength]
  ) {
    prefixLength++;
  }

  // Find common suffix
  let originalSuffix = originalLines.length - 1;
  let modifiedSuffix = modifiedLines.length - 1;
  while (
    originalSuffix >= prefixLength &&
    modifiedSuffix >= prefixLength &&
    originalLines[originalSuffix] === modifiedLines[modifiedSuffix]
  ) {
    originalSuffix--;
    modifiedSuffix--;
  }

  // If there are changes, create a single edit
  if (prefixLength <= originalSuffix || prefixLength <= modifiedSuffix) {
    const beforeText = originalLines
      .slice(prefixLength, originalSuffix + 1)
      .join("\n");
    const afterText = modifiedLines
      .slice(prefixLength, modifiedSuffix + 1)
      .join("\n");

    // Add newlines if needed to maintain correct formatting
    const formattedBeforeText = beforeText.length > 0 ? beforeText : "";
    const formattedAfterText = afterText.length > 0 ? afterText : "";

    edits.push({
      /* eslint-disable @typescript-eslint/naming-convention */
      before_line_start: prefixLength,
      before_text: formattedBeforeText,
      after_line_start: prefixLength,
      after_text: formattedAfterText,
      /* eslint-enable @typescript-eslint/naming-convention */
    });
  }

  return edits;
}

/**
 * Converts a DiffViewDocument to a ChatRequestFileEdit
 */
export function diffViewDocumentToFileEdit(
  document: DiffViewDocument,
  beforeBlobName?: string,
  afterBlobName?: string,
): ChatRequestFileEdit {
  const edits = calculateLineBasedEdits(
    document.originalCode,
    document.modifiedCode,
  );

  return {
    /* eslint-disable @typescript-eslint/naming-convention */
    path: document.filePath.absPath,
    before_blob_name: beforeBlobName,
    after_blob_name: afterBlobName,
    edits: edits,
    /* eslint-enable @typescript-eslint/naming-convention */
  };
}

/**
 * Creates a ChatRequestNode for edit events from a DiffViewDocument
 */
export function createEditEventsNodeFromDocument(
  id: number,
  document: DiffViewDocument,
  beforeBlobName?: string,
  afterBlobName?: string,
): ChatRequestNode {
  const fileEdit = diffViewDocumentToFileEdit(
    document,
    beforeBlobName,
    afterBlobName,
  );

  return {
    id,
    type: ChatRequestNodeType.EDIT_EVENTS,
    /* eslint-disable @typescript-eslint/naming-convention */
    edit_events_node: {
      edit_events: [fileEdit],
    },
    /* eslint-enable @typescript-eslint/naming-convention */
  };
}

/**
 * Creates a ChatRequestNode for edit events from multiple DiffViewDocuments
 */
export function createEditEventsNodeFromDocuments(
  id: number,
  documents: DiffViewDocument[],
  getBlobNames?: (document: DiffViewDocument) => {
    beforeBlobName?: string;
    afterBlobName?: string;
  },
): ChatRequestNode {
  const fileEdits = documents.map((document) => {
    const blobNames = getBlobNames
      ? getBlobNames(document)
      : { beforeBlobName: undefined, afterBlobName: undefined };
    return diffViewDocumentToFileEdit(
      document,
      blobNames.beforeBlobName,
      blobNames.afterBlobName,
    );
  });

  return {
    id,
    type: ChatRequestNodeType.EDIT_EVENTS,
    /* eslint-disable @typescript-eslint/naming-convention */
    edit_events_node: {
      edit_events: fileEdits,
    },
    /* eslint-enable @typescript-eslint/naming-convention */
  };
}

/**
 * Creates a ChatRequestNode for edit events from a map of file paths to before/after content
 */
export function createEditEventsNodeFromContentMap(
  id: number,
  contentMap: Map<
    string,
    {
      before: string;
      after: string;
      beforeBlobName?: string;
      afterBlobName?: string;
    }
  >,
): ChatRequestNode {
  const fileEdits: ChatRequestFileEdit[] = [];

  for (const [path, content] of contentMap.entries()) {
    const edits = calculateLineBasedEdits(content.before, content.after);

    if (edits.length > 0) {
      fileEdits.push({
        /* eslint-disable @typescript-eslint/naming-convention */
        path,
        before_blob_name: content.beforeBlobName,
        after_blob_name: content.afterBlobName,
        edits,
        /* eslint-enable @typescript-eslint/naming-convention */
      });
    }
  }

  return {
    id,
    type: ChatRequestNodeType.EDIT_EVENTS,
    /* eslint-disable @typescript-eslint/naming-convention */
    edit_events_node: {
      edit_events: fileEdits,
    },
    /* eslint-enable @typescript-eslint/naming-convention */
  };
}

/**
 * Creates a ChatRequestNode for edit events from an AggregateCheckpointInfo
 *
 * This function takes the checkpoint information from the AggregateCheckpointManager
 * and converts it to a ChatRequestNode that can be sent to the API.
 *
 * @param id The node ID
 * @param checkpointInfo The aggregate checkpoint info from the checkpoint manager
 * @param getBlobNames Optional function to get blob names for each document
 * @returns A ChatRequestNode for edit events
 */
export function createEditEventsNodeFromCheckpoint(
  id: number,
  checkpointInfo: AggregateCheckpointInfo,
  getBlobNames?: (document: DiffViewDocument) => {
    beforeBlobName?: string;
    afterBlobName?: string;
  },
): ChatRequestNode {
  // Extract all the DiffViewDocuments from the checkpoint info
  const documents = checkpointInfo.files.map((file) => file.changeDocument);

  // Use the existing function to create a node from the documents
  return createEditEventsNodeFromDocuments(id, documents, getBlobNames);
}
