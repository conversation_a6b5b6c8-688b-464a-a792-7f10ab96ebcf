import { AggregateCheckpointInfo } from "../agent/checkpoint/checkpoint-types";
import { DiffViewDocument } from "../diff-view/document";
import {
  ChatRequestEditEvents,
  ChatRequestFileEdit,
  ChatRequestNode,
  ChatRequestNodeType,
  ChatRequestSingleEdit,
  EditEventSource,
} from "./chat-types";
import { diffLines } from "diff";

/**
 * Converts a string to an array of lines
 */
function getLines(text: string): string[] {
  return text.split(/\r?\n/);
}

/**
 * Calculates line-based diffs between original and modified code
 * Returns an array of ChatRequestSingleEdit objects
 * Uses the diffLines function from the diff library for more accurate diffs
 */
function calculateLineBasedEdits(
  originalCode: string | undefined,
  modifiedCode: string | undefined,
): ChatRequestSingleEdit[] {
  originalCode = originalCode || "";
  modifiedCode = modifiedCode || "";

  // Use diffLines from the diff library for more complex cases
  const diffResult = diffLines(originalCode, modifiedCode);
  const edits: ChatRequestSingleEdit[] = [];

  let beforeLineStart = 1; // Start at 1 for 1-indexed line numbers
  let afterLineStart = 1; // Start at 1 for 1-indexed line numbers
  let currentEdit: ChatRequestSingleEdit | null = null;

  // Process each part of the diff
  for (const part of diffResult) {
    const lines = getLines(part.value);
    const lineCount =
      part.count ||
      (lines.length > 0
        ? lines.length - (part.value.endsWith("\n") ? 0 : 1)
        : 0);

    // Skip empty parts
    if (lineCount === 0) continue;

    if (part.added || part.removed) {
      // Start a new edit if we don't have one yet
      if (!currentEdit) {
        currentEdit = {
          /* eslint-disable @typescript-eslint/naming-convention */
          before_line_start: beforeLineStart,
          before_text: part.removed ? part.value : "",
          after_line_start: afterLineStart,
          after_text: part.added ? part.value : "",
          /* eslint-enable @typescript-eslint/naming-convention */
        };
      } else {
        // Update the existing edit
        if (part.removed) {
          currentEdit.before_text += part.value;
        } else if (part.added) {
          currentEdit.after_text += part.value;
        }
      }

      // Update line counters
      if (part.removed) {
        beforeLineStart += lineCount;
      } else if (part.added) {
        afterLineStart += lineCount;
      }
    } else {
      // This is an unchanged part
      // If we have a current edit, push it and reset
      if (currentEdit) {
        edits.push(currentEdit);
        currentEdit = null;
      }

      // Update both line counters for unchanged parts
      beforeLineStart += lineCount;
      afterLineStart += lineCount;
    }
  }

  // Don't forget to add the last edit if there is one
  if (currentEdit) {
    edits.push(currentEdit);
  }

  return edits;
}

/**
 * Converts a DiffViewDocument to a ChatRequestFileEdit
 */
function diffViewDocumentToFileEdit(
  document: DiffViewDocument,
  beforeBlobName?: string,
  afterBlobName?: string,
): ChatRequestFileEdit {
  const edits = calculateLineBasedEdits(
    document.originalCode,
    document.modifiedCode,
  );

  return {
    /* eslint-disable @typescript-eslint/naming-convention */
    path: document.filePath.absPath,
    before_blob_name: beforeBlobName,
    after_blob_name: afterBlobName,
    edits: edits,
    /* eslint-enable @typescript-eslint/naming-convention */
  };
}

/**
 * Converts an AggregateCheckpointInfo to a ChatRequestEditEvents
 *
 * @param checkpointInfo The aggregate checkpoint info from the checkpoint manager
 * @param getBlobNames Optional function to get blob names for each document
 * @param source The source of the edit events
 * @returns A ChatRequestEditEvents object
 */
export function checkpointToEditEvents(
  checkpointInfo: AggregateCheckpointInfo,
  getBlobNames?: (document: DiffViewDocument) => {
    beforeBlobName?: string;
    afterBlobName?: string;
  },
  source?: EditEventSource,
): ChatRequestEditEvents {
  const fileEdits: ChatRequestFileEdit[] = checkpointInfo.files
    .filter((file) => {
      return (
        file.changesSummary.totalAddedLines > 0 ||
        file.changesSummary.totalRemovedLines > 0
      );
    })
    .map((file) => {
      const document = file.changeDocument;
      const blobNames = getBlobNames
        ? getBlobNames(document)
        : { beforeBlobName: undefined, afterBlobName: undefined };
      return diffViewDocumentToFileEdit(
        document,
        blobNames.beforeBlobName,
        blobNames.afterBlobName,
      );
    });

  return {
    /* eslint-disable @typescript-eslint/naming-convention */
    edit_events: fileEdits,
    source,
    /* eslint-enable @typescript-eslint/naming-convention */
  };
}

/**
 * Converts an AggregateCheckpointInfo to a ChatRequestNode for edit events
 *
 * @param id The node ID
 * @param checkpointInfo The aggregate checkpoint info from the checkpoint manager
 * @param getBlobNames Optional function to get blob names for each document
 * @param source The source of the edit events
 * @returns A ChatRequestNode for edit events
 */
export function checkpointToEditEventsNode(
  id: number,
  checkpointInfo: AggregateCheckpointInfo,
  getBlobNames?: (document: DiffViewDocument) => {
    beforeBlobName?: string;
    afterBlobName?: string;
  },
  source?: EditEventSource,
): ChatRequestNode {
  return {
    id: id % 2 ** 31 /* i32, needed by the backend for now. This is a hack. */,
    type: ChatRequestNodeType.EDIT_EVENTS,
    /* eslint-disable @typescript-eslint/naming-convention */
    edit_events_node: checkpointToEditEvents(
      checkpointInfo,
      getBlobNames,
      source,
    ),
    /* eslint-enable @typescript-eslint/naming-convention */
  };
}
