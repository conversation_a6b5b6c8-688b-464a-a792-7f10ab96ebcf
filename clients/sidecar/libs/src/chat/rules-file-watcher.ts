import * as fs from "fs";
import * as path from "path";
import {
  AUGMENT_DIRECTORY_ROOT,
  AUGMENT_GUIDELINES_FILE,
  AUGMENT_RULES_FOLDER,
} from "../utils/rules-parser";
import { DisposableService } from "../lifecycle/disposable-service";
import { getClientWorkspaces } from "../client-interfaces/client-workspaces";
import { EventEmitter } from "stream";

/**
 * File system watcher for rules files and guidelines.
 * Watches for changes in the .augment/rules directory and guidelines file.
 */
export class RulesFileWatcher extends DisposableService {
  private _rulesChangedEmitter = new EventEmitter();
  private _fileWatchers: fs.FSWatcher[] = [];

  constructor() {
    super();
    // Setup file system watcher asynchronously
    void this._setupFileSystemWatcher();
  }

  /**
   * Event emitted when rules change
   */
  onDidChange(listener: () => void): { dispose: () => void } {
    this._rulesChangedEmitter.on("change", listener);
    return {
      dispose: () => {
        // Remove specific listener (simplified implementation)
        this._rulesChangedEmitter.removeAllListeners();
      },
    };
  }

  /**
   * Set up file system watchers for rule files and directories
   */
  private async _setupFileSystemWatcher(): Promise<void> {
    try {
      const workspaces = getClientWorkspaces();
      if (!workspaces) {
        return;
      }

      const workspaceRoot = await workspaces.getWorkspaceRoot();
      if (!workspaceRoot) {
        return;
      }

      // Watch the rules directory
      const rulesDir = path.join(
        workspaceRoot,
        AUGMENT_DIRECTORY_ROOT,
        AUGMENT_RULES_FOLDER,
      );

      if (fs.existsSync(rulesDir)) {
        const watcher = fs.watch(
          rulesDir,
          { recursive: true },
          (eventType, filename) => {
            // Handle both file creation/deletion (rename events) and modifications (change events)
            // For rename events (create/delete), filename might be null, so we emit change for any rename
            // For change events (modify), only emit for .md files
            if (
              eventType === "rename" || // File created or deleted
              (eventType === "change" && filename && filename.endsWith(".md")) // File modified
            ) {
              this._rulesChangedEmitter.emit("change");
            }
          },
        );
        this._fileWatchers.push(watcher);
      }

      // Watch guidelines file in workspace root
      const guidelinesPath = path.join(workspaceRoot, AUGMENT_GUIDELINES_FILE);
      if (fs.existsSync(guidelinesPath)) {
        const watcher = fs.watch(guidelinesPath, () => {
          this._rulesChangedEmitter.emit("change");
        });
        this._fileWatchers.push(watcher);
      }
    } catch (error) {
      /* empty */
    }
  }

  /**
   * Manually trigger a rules change event
   */
  public triggerChange(): void {
    this._rulesChangedEmitter.emit("change");
  }

  /**
   * Dispose of resources
   */
  public dispose(): void {
    super.dispose();
    for (const watcher of this._fileWatchers) {
      watcher.close();
    }
    this._fileWatchers = [];
    this._rulesChangedEmitter.removeAllListeners();
  }
}
