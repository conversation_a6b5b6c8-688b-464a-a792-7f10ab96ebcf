import * as path from "path";
import { getLogger } from "../logging";
import { Rule, RuleType } from "./chat-types";
import {
  AUGMENT_DIRECTORY_ROOT,
  AUGMENT_GUIDELINES_FILE,
  AUGMENT_RULES_FOLDER,
  RulesParser,
} from "../utils/rules-parser";
import { DisposableService } from "../lifecycle/disposable-service";
import {
  getClientWorkspaces,
  FileType,
  IClientWorkspaces,
} from "../client-interfaces/client-workspaces";
import { AutoImportRulesOption } from "../webview-messages/message-types/rules-messages";

const IMPORTED_DIRECTORY = "imported";
const MARKDOWN_FILE_EXTENSION = ".md";
const CURSOR_RULES_FILE_EXTENSION = ".mdc";
export const MARKDOWN_FILE_ENDINGS = [
  MARKDOWN_FILE_EXTENSION,
  CURSOR_RULES_FILE_EXTENSION,
];

type AutoDetectRulesConfig = {
  directory?: string;
  file?: string;
  name: string;
};

type ImportResult = {
  successfulImports: number;
  duplicates: number;
  totalAttempted: number;
};

const AUTO_DETECT_RULES_CONFIG: AutoDetectRulesConfig[] = [
  { directory: ".cursor/rules", file: ".cursorrules", name: "Cursor" },
  { directory: ".windsurf/rules", file: ".windsurfrules", name: "Windsurf" },
  {
    directory: ".github/instructions",
    file: ".github/copilot-instructions.md",
    name: "GitHub Copilot",
  },
  { directory: ".clinerules", file: ".clinerules", name: "Cline" },
  { directory: ".roo/rules", file: ".roorules", name: "Roo Code" },
  { directory: ".trae/rules", name: "Trae" },
] as const;

/**
 * Parameters for calculating rules and guidelines character count
 */
interface CalculateRulesCharacterCountParams {
  rules: Rule[];
  workspaceGuidelinesContent: string;
  contextRules?: Rule[];
  rulesAndGuidelinesLimit?: number;
}

/**
 * Service for managing rules in the sidecar.
 * This replaces the RulesLoader functionality from VSCode.
 */
export class RulesService extends DisposableService {
  private _logger = getLogger("RulesService");

  constructor() {
    super();
  }

  /**
   * Load rules from the .augment/rules directory and workspace guidelines files
   * This method uses the client workspaces RPC for consistent behavior across clients
   */
  public async loadRules({
    includeGuidelines = false,
    query = undefined,
    maxResults = undefined,
    contextRules = undefined,
  }: {
    includeGuidelines?: boolean;
    query?: string;
    maxResults?: number;
    contextRules?: Rule[];
  } = {}): Promise<Rule[]> {
    this._logger.debug(
      `Loading rules with includeGuidelines=${includeGuidelines}, query=${query}, maxResults=${maxResults}`,
    );

    // Fallback to file system approach (for VSCode and other clients)
    this._logger.debug("Using file system approach to load rules");
    const rules = await this.loadDirectory(
      path.join(AUGMENT_DIRECTORY_ROOT, AUGMENT_RULES_FOLDER),
    );
    this._logger.debug(`Loaded ${rules.length} rules from directory`);

    let allRules: Rule[];
    if (!includeGuidelines) {
      allRules = rules;
    } else {
      // Also load workspace guidelines files from workspace roots
      const guidelinesRules = await this.loadGuidelinesFiles();
      this._logger.debug(`Loaded ${guidelinesRules.length} guidelines rules`);
      allRules = [...guidelinesRules, ...rules];
    }

    // Apply query filter if provided
    if (query && query.trim()) {
      const queryLower = query.toLowerCase().trim();
      allRules = allRules.filter((rule) => {
        const pathMatch = rule.path.toLowerCase().includes(queryLower);
        const contentMatch = rule.content.toLowerCase().includes(queryLower);
        return pathMatch || contentMatch;
      });
      this._logger.debug(
        `Filtered to ${allRules.length} rules matching query: ${query}`,
      );
    }

    // Apply limit if provided
    if (maxResults && maxResults > 0) {
      allRules = allRules.slice(0, maxResults);
      this._logger.debug(`Limited to ${allRules.length} rules`);
    }

    this._logger.debug(`Returning ${allRules.length} total rules`);

    // Apply context-based filtering if contextRules is provided
    if (contextRules !== undefined) {
      allRules = RulesService.filterRulesByContext(allRules, contextRules);
      this._logger.debug(
        `Filtered to ${allRules.length} rules based on context`,
      );
    }
    return allRules;
  }

  /**
   * Filter rules based on context to determine which rules should be included in a chat request.
   *
   * This method implements the core rule filtering logic used across the application:
   * - ALWAYS_ATTACHED rules are always included regardless of context
   * - AGENT_REQUESTED rules are always included regardless of context
   * - MANUAL rules are only included if they appear in the contextRules list
   *
   * @param rules - All available rules from the rules system
   * @param contextRules - List of rules that have been explicitly mentioned or selected in the current context
   * @returns Filtered array of rules that should be attached to the chat request
   *
   * @example
   * ```typescript
   * const allRules = await rulesService.loadRules();
   * const contextRules = getContextRulesFromMentions();
   * const filteredRules = RulesService.filterRulesByContext(allRules, contextRules);
   * // filteredRules now contains only the rules that should be sent with the chat request
   * ```
   */
  public static filterRulesByContext(
    rules: Rule[],
    contextRules: Rule[],
  ): Rule[] {
    // Always include ALWAYS_ATTACHED and AGENT_REQUESTED rules
    const nonManualRules = rules.filter((r) => r.type !== RuleType.MANUAL);

    // Only include MANUAL rules if they are mentioned in context
    const manualRules = rules.filter((r) => {
      if (r.type === RuleType.MANUAL) {
        return contextRules.some((rule) => rule.path === r.path);
      }
      return false;
    });

    return [...nonManualRules, ...manualRules];
  }

  /**
   * Approximates the total character count of all rules and workspace guidelines
   * that would be included in the chat context. This is approximated as number of characters
   * in the rule content and path, plus some amount of boilerplate for each rule, actual calculation is
   * done in the prompt on the backend in python
   *
   * @param params - Object containing rules, workspace guidelines content, and optional context rules
   * @returns Total character count of rules and guidelines
   */
  public static calculateRulesAndGuidelinesCharacterCount(
    params: CalculateRulesCharacterCountParams,
  ) {
    const { rules, workspaceGuidelinesContent, contextRules = [] } = params;
    const alwaysRules = rules.filter(
      (r) => r.type === RuleType.ALWAYS_ATTACHED,
    );
    // Calculate character count from rules content
    const alwaysRulesCharCount = alwaysRules.reduce(
      (total, rule) => total + rule.content.length + rule.path.length,
      0,
    );

    const AUTO_RULE_BOILERPLATE_LENGTH = 100;
    const autoRequestedRules = rules.filter(
      (r) => r.type === RuleType.AGENT_REQUESTED,
    );
    const autoRequestedCharCount = autoRequestedRules.reduce(
      (total, rule) =>
        total +
        AUTO_RULE_BOILERPLATE_LENGTH +
        (rule.description?.length ?? 0) +
        rule.path.length,
      0,
    );

    // Only include manual rules in context if available
    const manualRules = rules
      .filter((r) => r.type === RuleType.MANUAL)
      .filter((r) => contextRules.some((rule) => rule.path === r.path));
    const manualRulesCharCount = manualRules.reduce(
      (total, rule) => total + rule.content.length + rule.path.length,
      0,
    );

    // Add workspace guidelines character count
    const workspaceGuidelinesCharCount = workspaceGuidelinesContent.length;

    const totalCharacterCount =
      alwaysRulesCharCount +
      manualRulesCharCount +
      autoRequestedCharCount +
      workspaceGuidelinesCharCount;
    const isOverLimit =
      params.rulesAndGuidelinesLimit &&
      totalCharacterCount > params.rulesAndGuidelinesLimit;

    return {
      totalCharacterCount,
      isOverLimit,
      warningMessage:
        isOverLimit && params.rulesAndGuidelinesLimit
          ? `Total number of characters in included rules and workspace guidelines (${totalCharacterCount} chars)
        exceeds the limit of ${params.rulesAndGuidelinesLimit} characters, remove some rules
        or reduce the length of your guidelines.`
          : undefined,
    };
  }

  /**
   * Load guidelines files from workspace root
   */
  private async loadGuidelinesFiles(): Promise<Rule[]> {
    const rules: Rule[] = [];
    const workspaces = getClientWorkspaces();
    if (!workspaces) {
      this._logger.warn("Client workspaces not initialized");
      return rules;
    }

    const workspaceRoot = await workspaces.getWorkspaceRoot();
    if (!workspaceRoot) {
      return rules;
    }

    const guidelinesPath = path.join(workspaceRoot, AUGMENT_GUIDELINES_FILE);
    const pathInfo = await workspaces.getPathInfo(guidelinesPath);
    if (pathInfo.exists && pathInfo.type === FileType.File) {
      try {
        const fileDetails = await workspaces.readFile(guidelinesPath);
        const content = fileDetails.contents;
        if (!content) {
          this._logger.warn(`Guidelines file is empty: ${guidelinesPath}`);
          return rules;
        }
        const parsedRule = RulesParser.parseRuleFile(
          content,
          AUGMENT_GUIDELINES_FILE,
        );
        rules.push({
          path: AUGMENT_GUIDELINES_FILE,
          content: parsedRule.content,
          type: parsedRule.type,
          description: parsedRule.description,
        });
      } catch (error) {
        this._logger.error(
          `Error loading guidelines file ${guidelinesPath}: ${String(error)}`,
        );
      }
    }

    return rules;
  }

  /**
   * Load rules from a directory
   */
  public async loadDirectory(directory: string): Promise<Rule[]> {
    const rules: Rule[] = [];

    try {
      const workspaces = getClientWorkspaces();
      if (!workspaces) {
        this._logger.warn("Client workspaces not initialized");
        return rules;
      }

      const workspaceRoot = await workspaces.getWorkspaceRoot();
      if (!workspaceRoot) {
        this._logger.warn("No workspace root found");
        return rules;
      }

      const rulesFolder = path.join(workspaceRoot, directory);
      this._logger.debug(`Looking for rules in: ${rulesFolder}`);

      const pathInfo = await workspaces.getPathInfo(rulesFolder);
      this._logger.debug(
        `Path info for ${rulesFolder}: ${JSON.stringify(pathInfo)}`,
      );
      if (!pathInfo.exists || pathInfo.type !== FileType.Directory) {
        this._logger.debug(`Rules folder not found at ${rulesFolder}`);
        return rules;
      }

      this._logger.debug(`Rules folder exists at ${rulesFolder}`);

      // Recursively process the rules folder
      await this.processRuleDirectory(workspaces, rulesFolder, rules, "");

      this._logger.debug(`Loaded ${rules.length} rules from ${rulesFolder}`);
      return rules;
    } catch (error) {
      this._logger.error(`Error loading rules: ${String(error)}`);
      return rules;
    }
  }

  /**
   * Load rules from a directory that can be either relative to workspace or absolute
   */
  public async loadDirectoryFromPath(directoryPath: string): Promise<Rule[]> {
    const rules: Rule[] = [];

    try {
      const workspaces = getClientWorkspaces();
      if (!workspaces) {
        this._logger.warn("Client workspaces not initialized");
        return rules;
      }

      let rulesFolder: string;

      if (path.isAbsolute(directoryPath)) {
        // Use absolute path directly
        rulesFolder = directoryPath;
        this._logger.debug(`Loading rules from absolute path: ${rulesFolder}`);
      } else {
        // Handle relative path (existing behavior)
        const workspaceRoot = await workspaces.getWorkspaceRoot();
        if (!workspaceRoot) {
          this._logger.warn("No workspace root found");
          return rules;
        }

        rulesFolder = path.join(workspaceRoot, directoryPath);
        this._logger.debug(
          `Loading rules from workspace-relative path: ${rulesFolder}`,
        );
      }

      const pathInfo = await workspaces.getPathInfo(rulesFolder);
      if (!pathInfo.exists || pathInfo.type !== FileType.Directory) {
        this._logger.debug(`Rules folder not found at ${rulesFolder}`);
        return rules;
      }

      this._logger.debug(`Rules folder exists at ${rulesFolder}`);

      // Recursively process the rules folder
      await this.processRuleDirectory(workspaces, rulesFolder, rules, "");

      this._logger.debug(`Loaded ${rules.length} rules from ${rulesFolder}`);
      return rules;
    } catch (error) {
      this._logger.error(`Error loading rules from path: ${String(error)}`);
      return rules;
    }
  }

  /**
   * Recursively process a directory for rule files
   */
  private async processRuleDirectory(
    workspaces: IClientWorkspaces,
    dirPath: string,
    rules: Rule[],
    relativePath: string,
  ): Promise<void> {
    const listResult = await workspaces.listDirectory(dirPath, 1, false);
    if (listResult.errorMessage) {
      this._logger.error(
        `Error listing directory ${dirPath}: ${listResult.errorMessage}`,
      );
      return;
    }

    this._logger.debug(
      `Processing directory: ${dirPath}, found ${listResult.entries.length} entries`,
    );

    for (const entryPath of listResult.entries) {
      const fullPath = path.join(dirPath, entryPath);
      const newRelativePath = path.join(relativePath, entryPath);

      const pathInfo = await workspaces.getPathInfo(fullPath);
      if (!pathInfo.exists) {
        continue;
      }

      if (pathInfo.type === FileType.Directory) {
        this._logger.debug(`Processing subdirectory: ${entryPath}`);
        await this.processRuleDirectory(
          workspaces,
          fullPath,
          rules,
          newRelativePath,
        );
      } else if (
        pathInfo.type === FileType.File &&
        MARKDOWN_FILE_ENDINGS.some((ext) => entryPath.endsWith(ext))
      ) {
        this._logger.debug(`Processing rule file: ${entryPath}`);
        try {
          const fileDetails = await workspaces.readFile(fullPath);
          const content = fileDetails.contents || "";
          const parsedRule = RulesParser.parseRuleFile(content, entryPath);
          rules.push({
            path: newRelativePath,
            content: parsedRule.content,
            type: parsedRule.type,
            description: parsedRule.description,
          });
          this._logger.debug(`Successfully loaded rule: ${newRelativePath}`);
        } catch (error) {
          this._logger.error(
            `Error loading rule file ${fullPath}: ${String(error)}`,
          );
        }
      } else if (pathInfo.type === FileType.File) {
        this._logger.debug(`Skipping non-markdown file: ${entryPath}`);
      }
    }
  }

  /**
   * Create a new rule file
   */
  public async createRule(
    rule: Rule,
    autoImport = false,
  ): Promise<Rule | undefined> {
    const workspaces = getClientWorkspaces();
    if (!workspaces) {
      throw new Error("Client workspaces not initialized");
    }

    const workspaceRoot = await workspaces.getWorkspaceRoot();
    if (!workspaceRoot) {
      throw new Error("No workspace root found");
    }

    let rulesDir = path.join(
      workspaceRoot,
      AUGMENT_DIRECTORY_ROOT,
      AUGMENT_RULES_FOLDER,
    );

    if (autoImport) {
      rulesDir = path.join(rulesDir, IMPORTED_DIRECTORY);
    }

    // Ensure the filename ends with .md
    const fileName = rule.path.endsWith(".md") ? rule.path : `${rule.path}.md`;
    const fullPath = path.join(rulesDir, fileName);

    // Get qualified path name for the file
    const qualifiedPath = await workspaces.getQualifiedPathName(fullPath);
    if (!qualifiedPath) {
      throw new Error(`Unable to get qualified path for: ${fullPath}`);
    }

    // Check if file already exists using workspaces interface
    const pathInfo = await workspaces.getPathInfo(fullPath);
    if (pathInfo.exists) {
      throw new Error(`Rule file already exists: ${fileName}`);
    }

    // Create the file with frontmatter - this will automatically create parent directories
    const content = RulesParser.formatRuleFileForMarkdown(rule);
    await workspaces.writeFile(qualifiedPath, content);

    return {
      ...rule,
      path: fileName,
    };
  }

  /**
   * Delete a rule file
   */
  public async deleteRule(rulePath: string): Promise<void> {
    if (typeof rulePath !== "string") {
      throw new Error(
        `Expected rulePath to be a string, got ${typeof rulePath}: ${String(rulePath)}`,
      );
    }

    const workspaces = getClientWorkspaces();
    if (!workspaces) {
      throw new Error("Client workspaces not initialized");
    }

    const workspaceRoot = await workspaces.getWorkspaceRoot();
    if (!workspaceRoot) {
      throw new Error("No workspace root found");
    }

    let fullPath: string;

    // Check if rulePath is already an absolute path
    if (path.isAbsolute(rulePath)) {
      fullPath = rulePath;
    } else {
      // Construct path relative to rules directory
      fullPath = path.join(
        workspaceRoot,
        AUGMENT_DIRECTORY_ROOT,
        AUGMENT_RULES_FOLDER,
        rulePath,
      );
    }

    // Check if file exists and delete it using workspaces interface
    const pathInfo = await workspaces.getPathInfo(fullPath);
    if (pathInfo.exists) {
      const qualifiedPath = await workspaces.getQualifiedPathName(fullPath);
      if (qualifiedPath) {
        await workspaces.deleteFile(qualifiedPath);
        this._logger.debug(`Deleted rule file: ${fullPath}`);
      }

      this._logger.debug(`Deleted rule file: ${fullPath}`);
    }
  }

  /**
   * Update a rule file with new content
   */
  public async updateRuleFile(
    rulePath: string,
    content: string,
  ): Promise<void> {
    if (typeof rulePath !== "string") {
      throw new Error(
        `Expected rulePath to be a string, got ${typeof rulePath}: ${String(rulePath)}`,
      );
    }

    const workspaces = getClientWorkspaces();
    if (!workspaces) {
      throw new Error("Client workspaces not initialized");
    }

    const workspaceRoot = await workspaces.getWorkspaceRoot();
    if (!workspaceRoot) {
      throw new Error("No workspace root found");
    }

    let fullPath: string;

    // Check if rulePath is already an absolute path
    if (path.isAbsolute(rulePath)) {
      fullPath = rulePath;
    } else if (rulePath.startsWith(AUGMENT_DIRECTORY_ROOT)) {
      // Path already includes the .augment directory structure
      fullPath = path.join(workspaceRoot, rulePath);
    } else {
      // Construct path relative to rules directory
      fullPath = path.join(
        workspaceRoot,
        AUGMENT_DIRECTORY_ROOT,
        AUGMENT_RULES_FOLDER,
        rulePath,
      );
    }

    // Get qualified path name for the file
    const qualifiedPath = await workspaces.getQualifiedPathName(fullPath);
    if (!qualifiedPath) {
      throw new Error(`Unable to get qualified path for: ${fullPath}`);
    }

    // Write the content to the file - this will automatically create parent directories
    await workspaces.writeFile(qualifiedPath, content);
    this._logger.debug(`Updated rule file: ${fullPath}`);
  }

  /**
   * Import a rule from a file
   */
  public async importFile(
    filename: string,
    autoImport: boolean,
  ): Promise<ImportResult> {
    const workspaces = getClientWorkspaces();
    if (!workspaces) {
      throw new Error("Client workspaces not initialized");
    }

    let filePath: string;
    let baseFilename: string;

    if (path.isAbsolute(filename)) {
      // Use absolute path directly
      filePath = filename;
      baseFilename = path.basename(filename);
      this._logger.debug(`Importing file from absolute path: ${filePath}`);
    } else {
      // Handle relative path (existing behavior)
      const workspaceRoot = await workspaces.getWorkspaceRoot();
      if (!workspaceRoot) {
        throw new Error("No workspace root found");
      }

      filePath = path.join(workspaceRoot, filename);
      baseFilename = filename;
      this._logger.debug(
        `Importing file from workspace-relative path: ${filePath}`,
      );
    }

    const pathInfo = await workspaces.getPathInfo(filePath);
    if (!pathInfo.exists || pathInfo.type !== FileType.File) {
      this._logger.error(`File not found: ${filePath}`);
      return { successfulImports: 0, duplicates: 0, totalAttempted: 1 };
    }

    try {
      const fileDetails = await workspaces.readFile(filePath);
      const content = fileDetails.contents;
      if (!content) {
        this._logger.error(`File is empty: ${filePath}`);
        return { successfulImports: 0, duplicates: 0, totalAttempted: 1 };
      }
      const parsedRule = RulesParser.parseRuleFile(content, baseFilename);
      const newFilename = path.parse(baseFilename).name.replace(".", "");

      await this.createRule(
        {
          path: newFilename,
          content: parsedRule.content,
          type: parsedRule.type,
        },
        autoImport,
      );

      return { successfulImports: 1, duplicates: 0, totalAttempted: 1 };
    } catch (error) {
      this._logger.error(`Error importing file ${filename}: ${String(error)}`);
      // Check if it's a duplicate file error
      const isDuplicate = String(error).includes("already exists");
      return {
        successfulImports: 0,
        duplicates: isDuplicate ? 1 : 0,
        totalAttempted: 1,
      };
    }
  }

  /**
   * Import rules from a directory
   */
  public async importDirectory(
    directoryPath: string,
    autoImport: boolean,
  ): Promise<ImportResult> {
    try {
      const existingRules = await this.loadDirectoryFromPath(directoryPath);
      if (existingRules.length === 0) {
        this._logger.debug(`No rules found in directory: ${directoryPath}`);
        return { successfulImports: 0, duplicates: 0, totalAttempted: 0 };
      }

      this._logger.debug(
        `Loaded ${existingRules.length} existing rules from ${directoryPath}`,
      );

      let importedCount = 0;
      let duplicateCount = 0;
      const totalAttempted = existingRules.length;

      for (const existingRule of existingRules) {
        try {
          // Create a new rule path in the .augment/rules directory
          // Remove the .md extension and use the relative path structure
          const ruleBaseName = path.parse(existingRule.path).name;
          const ruleDir = path.dirname(existingRule.path);
          const newRulePath =
            ruleDir === "." ? ruleBaseName : path.join(ruleDir, ruleBaseName);

          await this.createRule(
            {
              path: newRulePath,
              content: existingRule.content,
              type: existingRule.type,
            },
            autoImport,
          );
          importedCount++;
          this._logger.debug(
            `Successfully imported rule: ${existingRule.path} -> ${newRulePath}`,
          );
        } catch (error) {
          this._logger.warn(
            `Failed to import rule ${existingRule.path}: ${String(error)}`,
          );
          // Check if it's a duplicate file error
          if (String(error).includes("already exists")) {
            duplicateCount++;
          }
        }
      }

      this._logger.info(
        `Imported ${importedCount} rules from ${directoryPath}, ${duplicateCount} duplicates skipped`,
      );
      return {
        successfulImports: importedCount,
        duplicates: duplicateCount,
        totalAttempted,
      };
    } catch (error) {
      this._logger.error(`Error importing directory: ${String(error)}`);
      return { successfulImports: 0, duplicates: 0, totalAttempted: 0 };
    }
  }

  /**
   * Detect available auto-import rules options in the workspace
   */
  public async detectAutoImportOptions(): Promise<AutoImportRulesOption[]> {
    const workspaces = getClientWorkspaces();
    if (!workspaces) {
      this._logger.warn("No workspace available for auto-import detection");
      return [];
    }

    const workspaceRoot = await workspaces.getWorkspaceRoot();
    if (!workspaceRoot) {
      this._logger.warn("No workspace root found for auto-import detection");
      return [];
    }

    const options: AutoImportRulesOption[] = [];

    for (const { directory, file, name } of AUTO_DETECT_RULES_CONFIG) {
      let ruleDirectoryExists = false;
      let ruleFileExists = false;

      // Check if directory exists
      if (directory) {
        try {
          const dirPath = path.join(workspaceRoot, directory);
          const pathInfo = await workspaces.getPathInfo(dirPath);
          ruleDirectoryExists =
            pathInfo.exists === true && pathInfo.type === FileType.Directory;
        } catch (error) {
          this._logger.debug(
            `Error checking directory ${directory}: ${String(error)}`,
          );
        }
      }

      // Check if file exists
      if (file) {
        try {
          const filePath = path.join(workspaceRoot, file);
          const pathInfo = await workspaces.getPathInfo(filePath);
          ruleFileExists =
            pathInfo.exists === true && pathInfo.type === FileType.File;
        } catch (error) {
          this._logger.debug(`Error checking file ${file}: ${String(error)}`);
        }
      }

      // Create option based on what exists
      if (ruleDirectoryExists && ruleFileExists) {
        options.push({
          label: name,
          description: `Import existing rules from ${directory} and ${file}`,
          directory,
          file,
        });
      } else if (ruleDirectoryExists) {
        options.push({
          label: name,
          description: `Import existing rules from ${directory}`,
          directory,
        });
      } else if (ruleFileExists) {
        options.push({
          label: name,
          description: `Import existing rules from ${file}`,
          file,
        });
      }
    }

    return options;
  }

  /**
   * Process auto-import rules selection and perform the import
   */
  public async processAutoImportSelection(selectedLabel: string): Promise<{
    importedRulesCount: number;
    duplicatesCount: number;
    totalAttempted: number;
    source: string;
  }> {
    const config = AUTO_DETECT_RULES_CONFIG.find(
      (c) => c.name === selectedLabel,
    );
    if (!config) {
      throw new Error(`Unknown auto-import option: ${selectedLabel}`);
    }

    const importPromises: Promise<ImportResult>[] = [];

    if (config.directory) {
      importPromises.push(this.importDirectory(config.directory, true));
    }

    if (config.file) {
      importPromises.push(this.importFile(config.file, true));
    }

    const results = await Promise.all(importPromises);
    const totalImportedCount = results.reduce(
      (sum, result) => sum + result.successfulImports,
      0,
    );
    const totalDuplicates = results.reduce(
      (sum, result) => sum + result.duplicates,
      0,
    );
    const totalAttempted = results.reduce(
      (sum, result) => sum + result.totalAttempted,
      0,
    );

    this._logger.debug(
      `Auto-import rules completed for ${selectedLabel}, imported: ${totalImportedCount}, duplicates: ${totalDuplicates}, total attempted: ${totalAttempted}`,
    );

    return {
      importedRulesCount: totalImportedCount,
      duplicatesCount: totalDuplicates,
      totalAttempted,
      source: selectedLabel,
    };
  }

  /**
   * Auto import rules - this will be handled by the VSCode extension
   * as it requires file picker UI
   */
  public autoImportRules(): void {
    // This method is a placeholder as the actual file/directory selection
    // needs to be done in the VSCode extension
    this._logger.debug("Auto import rules requested");
  }

  /**
   * Dispose of resources
   */
  public dispose(): void {
    super.dispose();
  }
}
