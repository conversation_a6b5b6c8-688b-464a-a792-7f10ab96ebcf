import {
  limitChatHistoryTruncate,
  splitChatHistoryByCharLimit,
  estimateExchangeSizeInChars,
} from "../chat-truncation";
import {
  Exchange,
  ChatRequestNode,
  ChatResultNode,
  ChatRequestNodeType,
  ChatResultNodeType,
} from "../chat-types";

describe("limitChatHistoryTruncate", () => {
  test("returns full history when under segment limit", () => {
    // Create a small history that should be under the limit
    /* eslint-disable @typescript-eslint/naming-convention */
    const smallHistory: Exchange[] = [
      {
        request_message: "Hello",
        response_text: "Hi there",
        request_id: "1",
      },
      {
        request_message: "How are you?",
        response_text: "I'm good",
        request_id: "2",
      },
    ];
    /* eslint-enable @typescript-eslint/naming-convention */

    const result = limitChatHistoryTruncate(smallHistory);
    expect(result).toEqual(smallHistory);
  });

  test("history stable while growing", () => {
    // Create a large history that will exceed the limit
    // This test does rely on the limit hard-coded in the implementation: currently 800000
    /* eslint-disable @typescript-eslint/naming-convention */
    const largeHistory = Array(20)
      .fill(0)
      .map((_, i) => ({
        request_message: `Message ${i}`,
        response_text: `Response ${i}`,
        request_id: "x".repeat(100000),
      }));
    /* eslint-enable @typescript-eslint/naming-convention */

    const cases = [
      { count: 1, start: 0 },
      { count: 2, start: 0 },
      { count: 3, start: 0 },
      { count: 4, start: 0 },
      { count: 5, start: 0 },
      { count: 6, start: 0 },
      { count: 7, start: 4 },
      { count: 8, start: 4 },
      { count: 9, start: 4 },
      { count: 10, start: 4 },
      { count: 11, start: 8 },
      { count: 12, start: 8 },
      { count: 13, start: 8 },
      { count: 14, start: 8 },
      { count: 15, start: 12 },
      { count: 16, start: 12 },
      { count: 17, start: 12 },
      { count: 18, start: 12 },
      { count: 19, start: 16 },
      { count: 20, start: 16 },
    ];
    for (const testCase of cases) {
      const result = limitChatHistoryTruncate(
        largeHistory.slice(0, testCase.count),
      );
      expect(JSON.stringify(result).length).toBeLessThan(800000);
      expect(result[0].request_message).toEqual(
        largeHistory[testCase.start].request_message,
      );
    }
  });
});

describe("splitChatHistoryByCharLimit", () => {
  // Helper function to create an exchange with a specific size
  function createExchangeWithSize(id: string, size: number): Exchange {
    const makeMessage = (len: number): Exchange => ({
      /* eslint-disable @typescript-eslint/naming-convention */
      request_message: `Message ${id}`,
      response_text: `Response ${id} ${"x".repeat(len)}`,
      request_id: id,
      /* eslint-enable @typescript-eslint/naming-convention */
    });

    const overhead = getExchangeSize(makeMessage(0));
    if (overhead > size) {
      throw new Error(
        `Overhead is larger than the desired size. Overhead: ${overhead}, Desired size: ${size}`,
      );
    }
    return makeMessage(size - overhead);
  }

  // Helper function to get the size of an exchange in characters
  function getExchangeSize(exchange: Exchange): number {
    return estimateExchangeSizeInChars(exchange);
  }

  function getHistorySize(exchanges: Exchange[]): number {
    return exchanges.reduce(
      (sum, exchange) => sum + getExchangeSize(exchange),
      0,
    );
  }

  test("returns empty arrays when history is empty", () => {
    const result = splitChatHistoryByCharLimit([], 1000, 2000, 0);
    expect(result).toEqual({
      head: [],
      tail: [],
      headSizeChars: 0,
      tailSizeChars: 0,
    });
  });

  test("returns all history in withinLimits when total size is under upperCharLimit", () => {
    // Create a history that's under the upper limit
    const history: Exchange[] = [
      createExchangeWithSize("1", 500),
      createExchangeWithSize("2", 500),
      createExchangeWithSize("3", 500),
    ];

    const totalSize = getHistorySize(history);
    expect(totalSize).toBeLessThan(2000); // Verify our test setup

    const { head, tail, headSizeChars, tailSizeChars } =
      splitChatHistoryByCharLimit(history, 1000, 2000, 0);

    // All history should be in tail (withinLimits)
    expect(head).toEqual([]);
    expect(tail).toEqual(history);
    expect(headSizeChars).toBe(0);
    expect(tailSizeChars).toBe(totalSize);
  });

  test("splits history when total size exceeds upperCharLimit", () => {
    // Create a history that exceeds the upper limit
    const history: Exchange[] = Array(10)
      .fill(0)
      .map((_, i) => createExchangeWithSize(String(i), 500));

    const totalSize = getHistorySize(history);
    expect(totalSize).toBeGreaterThan(2000); // Verify our test setup

    const { head, tail, headSizeChars, tailSizeChars } =
      splitChatHistoryByCharLimit(history, 1000, 2000, 0);

    // Some history should be in head, some in tail
    expect(head.length).toBeGreaterThan(0);
    expect(tail.length).toBeGreaterThan(0);

    // The total should equal the original history length
    expect(head.length + tail.length).toBe(history.length);

    // tail should be under lowerCharLimit
    expect(getHistorySize(tail)).toBeLessThanOrEqual(1000);
    expect(tailSizeChars).toBeLessThanOrEqual(1000);

    // The most recent exchanges should be in tail
    expect(tail[tail.length - 1]).toEqual(history[history.length - 1]);

    // Size calculations should match actual sizes
    expect(headSizeChars).toBe(getHistorySize(head));
    expect(tailSizeChars).toBe(getHistorySize(tail));
  });

  test("handles case where a single exchange exceeds lowerCharLimit", () => {
    // Create a history with one large exchange that exceeds the lower limit
    const largeExchange = createExchangeWithSize("large", 1500);
    const history: Exchange[] = [
      createExchangeWithSize("1", 300),
      largeExchange,
      createExchangeWithSize("3", 300),
    ];
    expect(getHistorySize(history)).toBeGreaterThan(2000);

    const { head, tail, headSizeChars, tailSizeChars } =
      splitChatHistoryByCharLimit(history, 1000, 2000, 0);
    expect(getHistorySize(tail)).toBeLessThan(1000);
    expect(tailSizeChars).toBeLessThan(1000);

    expect(head).toEqual(history.slice(0, 2));
    // The most recent exchange should be in tail
    expect(tail).toEqual(history.slice(2));

    // Size calculations should match actual sizes
    expect(headSizeChars).toBe(getHistorySize(head));
    expect(tailSizeChars).toBe(getHistorySize(tail));
  });

  test("preserves order of exchanges in both parts", () => {
    const history: Exchange[] = Array(6)
      .fill(0)
      .map((_, i) => createExchangeWithSize(String(i), 400));

    const { head, tail } = splitChatHistoryByCharLimit(history, 1000, 2000, 0);

    // Check that the order is preserved in both arrays
    for (let i = 0; i < head.length - 1; i++) {
      expect(parseInt(head[i].request_id)).toBeLessThan(
        parseInt(head[i + 1].request_id),
      );
    }

    for (let i = 0; i < tail.length - 1; i++) {
      expect(parseInt(tail[i].request_id)).toBeLessThan(
        parseInt(tail[i + 1].request_id),
      );
    }

    // Check that head contains older exchanges and tail contains newer ones
    // Only run this check if both arrays have elements
    expect(
      head.length === 0 ||
        tail.length === 0 ||
        parseInt(head[head.length - 1].request_id) <
          parseInt(tail[0].request_id),
    ).toBe(true);
  });

  test("handles edge case where total size exactly hits limits", () => {
    // Create exchanges that add up exactly to the upper limit
    const exchange1 = createExchangeWithSize("1", 500);
    const exchange2 = createExchangeWithSize("2", 500);
    const exchange3 = createExchangeWithSize("3", 1000);

    const history = [exchange1, exchange2, exchange3];
    const totalSize = getHistorySize(history);

    expect(totalSize).toBe(2000);

    const { head, tail, headSizeChars, tailSizeChars } =
      splitChatHistoryByCharLimit(history, 1000, 2000, 0);
    expect(head).toEqual(history);
    expect(tail).toEqual([]);
    expect(headSizeChars).toBe(totalSize);
    expect(tailSizeChars).toBe(0);
  });

  test("handles lowerMinCount param properly", () => {
    const exchange1 = createExchangeWithSize("1", 500);
    const exchange2 = createExchangeWithSize("2", 500);
    const exchange3 = createExchangeWithSize("3", 500);

    const history = [exchange1, exchange2, exchange3];
    const totalSize = getHistorySize(history);

    expect(totalSize).toBe(1500);

    const { head, tail, headSizeChars, tailSizeChars } =
      splitChatHistoryByCharLimit(history, 400, 1000, 1);
    expect(head).toEqual([exchange1, exchange2]);
    expect(tail).toEqual([exchange3]);
    expect(headSizeChars).toBe(getHistorySize([exchange1, exchange2]));
    expect(tailSizeChars).toBe(getHistorySize([exchange3]));
  });
});

describe("estimateExchangeSizeInChars", () => {
  test("calculates size for exchange with only request_message and response_text", () => {
    const exchange: Exchange = {
      /* eslint-disable @typescript-eslint/naming-convention */
      request_message: "Hello world",
      response_text: "Hi there!",
      request_id: "test-1",
      /* eslint-enable @typescript-eslint/naming-convention */
    };

    const size = estimateExchangeSizeInChars(exchange);
    expect(size).toBe("Hello world".length + "Hi there!".length);
    expect(size).toBe(11 + 9); // 20 total
  });

  test("uses request_nodes instead of request_message when nodes are present", () => {
    const requestNodes: ChatRequestNode[] = [
      {
        id: 1,
        type: ChatRequestNodeType.TEXT,
        /* eslint-disable @typescript-eslint/naming-convention */
        text_node: { content: "Node content" },
        /* eslint-enable @typescript-eslint/naming-convention */
      },
    ];

    const exchange: Exchange = {
      /* eslint-disable @typescript-eslint/naming-convention */
      request_message: "This should be ignored",
      response_text: "Response",
      request_id: "test-2",
      request_nodes: requestNodes,
      /* eslint-enable @typescript-eslint/naming-convention */
    };

    const size = estimateExchangeSizeInChars(exchange);
    const expectedRequestSize = JSON.stringify(requestNodes).length;
    const expectedResponseSize = "Response".length;

    expect(size).toBe(expectedRequestSize + expectedResponseSize);
    // Should NOT include the request_message length
    expect(size).not.toBe("This should be ignored".length + "Response".length);
  });

  test("uses response_nodes instead of response_text when nodes are present", () => {
    const responseNodes: ChatResultNode[] = [
      {
        id: 1,
        type: ChatResultNodeType.RAW_RESPONSE,
        content: "Node response content",
      },
    ];

    const exchange: Exchange = {
      /* eslint-disable @typescript-eslint/naming-convention */
      request_message: "Request",
      response_text: "This should be ignored",
      request_id: "test-3",
      response_nodes: responseNodes,
      /* eslint-enable @typescript-eslint/naming-convention */
    };

    const size = estimateExchangeSizeInChars(exchange);
    const expectedRequestSize = "Request".length;
    const expectedResponseSize = JSON.stringify(responseNodes).length;

    expect(size).toBe(expectedRequestSize + expectedResponseSize);
    // Should NOT include the response_text length
    expect(size).not.toBe("Request".length + "This should be ignored".length);
  });

  test("uses both request_nodes and response_nodes when both are present", () => {
    const requestNodes: ChatRequestNode[] = [
      {
        id: 1,
        type: ChatRequestNodeType.TEXT,
        /* eslint-disable @typescript-eslint/naming-convention */
        text_node: { content: "Request node" },
        /* eslint-enable @typescript-eslint/naming-convention */
      },
    ];

    const responseNodes: ChatResultNode[] = [
      {
        id: 1,
        type: ChatResultNodeType.RAW_RESPONSE,
        content: "Response node",
      },
    ];

    const exchange: Exchange = {
      /* eslint-disable @typescript-eslint/naming-convention */
      request_message: "Ignored request",
      response_text: "Ignored response",
      request_id: "test-4",
      request_nodes: requestNodes,
      response_nodes: responseNodes,
      /* eslint-enable @typescript-eslint/naming-convention */
    };

    const size = estimateExchangeSizeInChars(exchange);
    const expectedRequestSize = JSON.stringify(requestNodes).length;
    const expectedResponseSize = JSON.stringify(responseNodes).length;

    expect(size).toBe(expectedRequestSize + expectedResponseSize);
    // Should NOT include the message/text lengths
    expect(size).not.toBe("Ignored request".length + "Ignored response".length);
  });

  test("handles empty strings correctly", () => {
    const exchange: Exchange = {
      /* eslint-disable @typescript-eslint/naming-convention */
      request_message: "",
      response_text: "",
      request_id: "test-5",
      /* eslint-enable @typescript-eslint/naming-convention */
    };

    const size = estimateExchangeSizeInChars(exchange);
    expect(size).toBe(0);
  });

  test("handles complex nodes with multiple properties", () => {
    const requestNodes: ChatRequestNode[] = [
      {
        id: 1,
        type: ChatRequestNodeType.TEXT,
        /* eslint-disable @typescript-eslint/naming-convention */
        text_node: { content: "First text node" },
        /* eslint-enable @typescript-eslint/naming-convention */
      },
      {
        id: 2,
        type: ChatRequestNodeType.TOOL_RESULT,
        /* eslint-disable @typescript-eslint/naming-convention */
        tool_result_node: {
          tool_use_id: "tool-123",
          content: "Tool result content",
          is_error: false,
        },
        /* eslint-enable @typescript-eslint/naming-convention */
      },
    ];

    const responseNodes: ChatResultNode[] = [
      {
        id: 1,
        type: ChatResultNodeType.RAW_RESPONSE,
        content: "Main response",
      },
      {
        id: 2,
        type: ChatResultNodeType.TOOL_USE,
        content: "Tool use content",
        /* eslint-disable @typescript-eslint/naming-convention */
        tool_use: {
          tool_use_id: "tool-456",
          tool_name: "test_tool",
          input_json: '{"param": "value"}',
        },
        /* eslint-enable @typescript-eslint/naming-convention */
      },
    ];

    const exchange: Exchange = {
      /* eslint-disable @typescript-eslint/naming-convention */
      request_message: "Should be ignored",
      response_text: "Should also be ignored",
      request_id: "test-6",
      request_nodes: requestNodes,
      response_nodes: responseNodes,
      /* eslint-enable @typescript-eslint/naming-convention */
    };

    const size = estimateExchangeSizeInChars(exchange);
    const expectedRequestSize = JSON.stringify(requestNodes).length;
    const expectedResponseSize = JSON.stringify(responseNodes).length;

    expect(size).toBe(expectedRequestSize + expectedResponseSize);
    expect(size).toBeGreaterThan(0);
  });

  test("demonstrates avoiding double counting - nodes take precedence over text", () => {
    // This test demonstrates the key feature: avoiding double counting
    // when the same content might exist in both message/text and nodes
    const duplicatedContent = "This content appears in both places";

    const requestNodes: ChatRequestNode[] = [
      {
        id: 1,
        type: ChatRequestNodeType.TEXT,
        /* eslint-disable @typescript-eslint/naming-convention */
        text_node: { content: duplicatedContent },
        /* eslint-enable @typescript-eslint/naming-convention */
      },
    ];

    const exchange: Exchange = {
      /* eslint-disable @typescript-eslint/naming-convention */
      request_message: duplicatedContent, // Same content as in nodes
      response_text: "Response",
      request_id: "test-7",
      request_nodes: requestNodes,
      /* eslint-enable @typescript-eslint/naming-convention */
    };

    const size = estimateExchangeSizeInChars(exchange);
    const expectedRequestSize = JSON.stringify(requestNodes).length;
    const expectedResponseSize = "Response".length;

    expect(size).toBe(expectedRequestSize + expectedResponseSize);

    // Verify it's NOT double counting by checking it's not the sum of both
    const wouldBeDoubleCountedSize =
      duplicatedContent.length +
      JSON.stringify(requestNodes).length +
      "Response".length;
    expect(size).toBeLessThan(wouldBeDoubleCountedSize);
  });
});
