import { ChatRequestNodeType } from "../chat-types";
import {
  calculateLineBasedEdits,
  createEditEventsNodeFromContentMap,
  createEditEventsNodeFromDocument,
  createEditEventsNodeFromDocuments,
  diffViewDocumentToFileEdit,
} from "../chat-edit-events-utils";
import { DiffViewDocument } from "../../diff-view/document";
import { QualifiedPathName } from "../../workspace/qualified-path-name";

describe("chat-edit-events-utils", () => {
  describe("calculateLineBasedEdits", () => {
    it("should return empty array for identical content", () => {
      const originalCode = "function test() {\n  return true;\n}";
      const modifiedCode = "function test() {\n  return true;\n}";

      const result = calculateLineBasedEdits(originalCode, modifiedCode);

      expect(result).toEqual([]);
    });

    it("should detect simple line changes", () => {
      const originalCode = "function test() {\n  return false;\n}";
      const modifiedCode = "function test() {\n  return true;\n}";

      const result = calculateLineBasedEdits(originalCode, modifiedCode);

      expect(result).toEqual([
        {
          before_line_start: 1,
          before_text: "  return false;",
          after_line_start: 1,
          after_text: "  return true;",
        },
      ]);
    });

    it("should detect added lines", () => {
      const originalCode = "function test() {\n  return true;\n}";
      const modifiedCode =
        "function test() {\n  console.log('test');\n  return true;\n}";

      const result = calculateLineBasedEdits(originalCode, modifiedCode);

      expect(result).toEqual([
        {
          before_line_start: 1,
          before_text: "",
          after_line_start: 1,
          after_text: "  console.log('test');",
        },
      ]);
    });

    it("should detect removed lines", () => {
      const originalCode =
        "function test() {\n  console.log('test');\n  return true;\n}";
      const modifiedCode = "function test() {\n  return true;\n}";

      const result = calculateLineBasedEdits(originalCode, modifiedCode);

      expect(result).toEqual([
        {
          before_line_start: 1,
          before_text: "  console.log('test');",
          after_line_start: 1,
          after_text: "",
        },
      ]);
    });
  });

  describe("diffViewDocumentToFileEdit", () => {
    it("should convert a DiffViewDocument to a ChatRequestFileEdit", () => {
      const filePath = new QualifiedPathName("/root", "test.js");
      const originalCode = "function test() {\n  return false;\n}";
      const modifiedCode = "function test() {\n  return true;\n}";
      const document = new DiffViewDocument(
        filePath,
        originalCode,
        modifiedCode,
        {},
      );

      const result = diffViewDocumentToFileEdit(
        document,
        "before-blob",
        "after-blob",
      );

      expect(result).toEqual({
        path: "/root/test.js",
        before_blob_name: "before-blob",
        after_blob_name: "after-blob",
        edits: [
          {
            before_line_start: 1,
            before_text: "  return false;",
            after_line_start: 1,
            after_text: "  return true;",
          },
        ],
      });
    });
  });

  describe("createEditEventsNodeFromDocument", () => {
    it("should create a ChatRequestNode for edit events from a DiffViewDocument", () => {
      const filePath = new QualifiedPathName("/root", "test.js");
      const originalCode = "function test() {\n  return false;\n}";
      const modifiedCode = "function test() {\n  return true;\n}";
      const document = new DiffViewDocument(
        filePath,
        originalCode,
        modifiedCode,
        {},
      );

      const result = createEditEventsNodeFromDocument(
        1,
        document,
        "before-blob",
        "after-blob",
      );

      expect(result).toEqual({
        id: 1,
        type: ChatRequestNodeType.EDIT_EVENTS,
        edit_events_node: {
          edit_events: [
            {
              path: "/root/test.js",
              before_blob_name: "before-blob",
              after_blob_name: "after-blob",
              edits: [
                {
                  before_line_start: 1,
                  before_text: "  return false;",
                  after_line_start: 1,
                  after_text: "  return true;",
                },
              ],
            },
          ],
        },
      });
    });
  });

  describe("createEditEventsNodeFromDocuments", () => {
    it("should create a ChatRequestNode for edit events from multiple DiffViewDocuments", () => {
      const filePath1 = new QualifiedPathName("/root", "test1.js");
      const originalCode1 = "function test1() {\n  return false;\n}";
      const modifiedCode1 = "function test1() {\n  return true;\n}";
      const document1 = new DiffViewDocument(
        filePath1,
        originalCode1,
        modifiedCode1,
        {},
      );

      const filePath2 = new QualifiedPathName("/root", "test2.js");
      const originalCode2 = "function test2() {\n  return null;\n}";
      const modifiedCode2 = "function test2() {\n  return undefined;\n}";
      const document2 = new DiffViewDocument(
        filePath2,
        originalCode2,
        modifiedCode2,
        {},
      );

      const getBlobNames = (doc: DiffViewDocument) => {
        if (doc.filePath.relPath === "test1.js") {
          return {
            beforeBlobName: "before-blob-1",
            afterBlobName: "after-blob-1",
          };
        } else {
          return {
            beforeBlobName: "before-blob-2",
            afterBlobName: "after-blob-2",
          };
        }
      };

      const result = createEditEventsNodeFromDocuments(
        1,
        [document1, document2],
        getBlobNames,
      );

      expect(result).toEqual({
        id: 1,
        type: ChatRequestNodeType.EDIT_EVENTS,
        edit_events_node: {
          edit_events: [
            {
              path: "/root/test1.js",
              before_blob_name: "before-blob-1",
              after_blob_name: "after-blob-1",
              edits: [
                {
                  before_line_start: 1,
                  before_text: "  return false;",
                  after_line_start: 1,
                  after_text: "  return true;",
                },
              ],
            },
            {
              path: "/root/test2.js",
              before_blob_name: "before-blob-2",
              after_blob_name: "after-blob-2",
              edits: [
                {
                  before_line_start: 1,
                  before_text: "  return null;",
                  after_line_start: 1,
                  after_text: "  return undefined;",
                },
              ],
            },
          ],
        },
      });
    });
  });

  describe("createEditEventsNodeFromContentMap", () => {
    it("should create a ChatRequestNode for edit events from a content map", () => {
      const contentMap = new Map([
        [
          "/root/test1.js",
          {
            before: "function test1() {\n  return false;\n}",
            after: "function test1() {\n  return true;\n}",
            beforeBlobName: "before-blob-1",
            afterBlobName: "after-blob-1",
          },
        ],
        [
          "/root/test2.js",
          {
            before: "function test2() {\n  return null;\n}",
            after: "function test2() {\n  return undefined;\n}",
            beforeBlobName: "before-blob-2",
            afterBlobName: "after-blob-2",
          },
        ],
      ]);

      const result = createEditEventsNodeFromContentMap(1, contentMap);

      expect(result).toEqual({
        id: 1,
        type: ChatRequestNodeType.EDIT_EVENTS,
        edit_events_node: {
          edit_events: [
            {
              path: "/root/test1.js",
              before_blob_name: "before-blob-1",
              after_blob_name: "after-blob-1",
              edits: [
                {
                  before_line_start: 1,
                  before_text: "  return false;",
                  after_line_start: 1,
                  after_text: "  return true;",
                },
              ],
            },
            {
              path: "/root/test2.js",
              before_blob_name: "before-blob-2",
              after_blob_name: "after-blob-2",
              edits: [
                {
                  before_line_start: 1,
                  before_text: "  return null;",
                  after_line_start: 1,
                  after_text: "  return undefined;",
                },
              ],
            },
          ],
        },
      });
    });
  });
});
