import { AggregateCheckpointInfo } from "../../agent/checkpoint/checkpoint-types";
import { DiffViewDocument } from "../../diff-view/document";
import { QualifiedPathName } from "../../workspace/qualified-path-name";
import { ChatRequestNodeType } from "../chat-types";
import {
  checkpointToEditEvents,
  checkpointToEditEventsNode,
} from "../checkpoint-to-edit-events";

describe("checkpoint-to-edit-events", () => {
  describe("checkpointToEditEvents", () => {
    it("should convert an AggregateCheckpointInfo to a ChatRequestEditEvents", () => {
      // Create a test DiffViewDocument
      const filePath = new QualifiedPathName("/root", "test.js");
      const originalCode = "function test() {\n  return false;\n}";
      const modifiedCode = "function test() {\n  return true;\n}";
      const document = new DiffViewDocument(
        filePath,
        originalCode,
        modifiedCode,
        {},
      );

      // Create a test AggregateCheckpointInfo
      const checkpointInfo: AggregateCheckpointInfo = {
        fromTimestamp: 0,
        toTimestamp: 1000,
        conversationId: "test-conversation",
        files: [
          {
            changesSummary: {
              totalAddedLines: 1,
              totalRemovedLines: 1,
              changes: [],
            },
            changeDocument: document,
          },
        ],
      };

      // Convert to ChatRequestEditEvents
      const result = checkpointToEditEvents(checkpointInfo);

      // Verify the result
      expect(result).toEqual({
        edit_events: [
          {
            path: "/root/test.js",
            before_blob_name: undefined,
            after_blob_name: undefined,
            edits: [
              {
                before_line_start: 2,
                before_text: "  return false;\n",
                after_line_start: 2,
                after_text: "  return true;\n",
              },
            ],
          },
        ],
      });
    });

    it("should use blob names from the getBlobNames function", () => {
      // Create a test DiffViewDocument
      const filePath = new QualifiedPathName("/root", "test.js");
      const originalCode = "function test() {\n  return false;\n}";
      const modifiedCode = "function test() {\n  return true;\n}";
      const document = new DiffViewDocument(
        filePath,
        originalCode,
        modifiedCode,
        {},
      );

      // Create a test AggregateCheckpointInfo
      const checkpointInfo: AggregateCheckpointInfo = {
        fromTimestamp: 0,
        toTimestamp: 1000,
        conversationId: "test-conversation",
        files: [
          {
            changesSummary: {
              totalAddedLines: 1,
              totalRemovedLines: 1,
              changes: [],
            },
            changeDocument: document,
          },
        ],
      };

      // Create a getBlobNames function
      const getBlobNames = () => ({
        beforeBlobName: "before-blob",
        afterBlobName: "after-blob",
      });

      // Convert to ChatRequestEditEvents
      const result = checkpointToEditEvents(checkpointInfo, getBlobNames);

      // Verify the result
      expect(result).toEqual({
        edit_events: [
          {
            path: "/root/test.js",
            before_blob_name: "before-blob",
            after_blob_name: "after-blob",
            edits: [
              {
                before_line_start: 2,
                before_text: "  return false;\n",
                after_line_start: 2,
                after_text: "  return true;\n",
              },
            ],
          },
        ],
      });
    });

    it("should handle multi-line additions", () => {
      // Create a test DiffViewDocument with multi-line additions
      const filePath = new QualifiedPathName("/root", "test.js");
      const originalCode = "function test() {\n  return false;\n}";
      const modifiedCode =
        "function test() {\n  console.log('Testing');\n  console.log('Multiple lines');\n  return false;\n}";
      const document = new DiffViewDocument(
        filePath,
        originalCode,
        modifiedCode,
        {},
      );

      // Create a test AggregateCheckpointInfo
      const checkpointInfo: AggregateCheckpointInfo = {
        fromTimestamp: 0,
        toTimestamp: 1000,
        conversationId: "test-conversation",
        files: [
          {
            changesSummary: {
              totalAddedLines: 2,
              totalRemovedLines: 0,
              changes: [],
            },
            changeDocument: document,
          },
        ],
      };

      // Convert to ChatRequestEditEvents
      const result = checkpointToEditEvents(checkpointInfo);

      // Verify the result
      expect(result).toEqual({
        edit_events: [
          {
            path: "/root/test.js",
            before_blob_name: undefined,
            after_blob_name: undefined,
            edits: [
              {
                before_line_start: 2,
                before_text: "",
                after_line_start: 2,
                after_text:
                  "  console.log('Testing');\n  console.log('Multiple lines');\n",
              },
            ],
          },
        ],
      });
    });

    it("should handle multi-line deletions", () => {
      // Create a test DiffViewDocument with multi-line deletions
      const filePath = new QualifiedPathName("/root", "test.js");
      const originalCode =
        "function test() {\n  console.log('Line 1');\n  console.log('Line 2');\n  console.log('Line 3');\n  return false;\n}";
      const modifiedCode = "function test() {\n  return false;\n}";
      const document = new DiffViewDocument(
        filePath,
        originalCode,
        modifiedCode,
        {},
      );

      // Create a test AggregateCheckpointInfo
      const checkpointInfo: AggregateCheckpointInfo = {
        fromTimestamp: 0,
        toTimestamp: 1000,
        conversationId: "test-conversation",
        files: [
          {
            changesSummary: {
              totalAddedLines: 0,
              totalRemovedLines: 3,
              changes: [],
            },
            changeDocument: document,
          },
        ],
      };

      // Convert to ChatRequestEditEvents
      const result = checkpointToEditEvents(checkpointInfo);

      // Verify the result
      expect(result).toEqual({
        edit_events: [
          {
            path: "/root/test.js",
            before_blob_name: undefined,
            after_blob_name: undefined,
            edits: [
              {
                before_line_start: 2,
                before_text:
                  "  console.log('Line 1');\n  console.log('Line 2');\n  console.log('Line 3');\n",
                after_line_start: 2,
                after_text: "",
              },
            ],
          },
        ],
      });
    });

    it("should handle multi-line replacements", () => {
      // Create a test DiffViewDocument with multi-line replacements
      const filePath = new QualifiedPathName("/root", "test.js");
      const originalCode =
        "function test() {\n  // Old comment line 1\n  // Old comment line 2\n  return false;\n}";
      const modifiedCode =
        "function test() {\n  /* New comment block\n   * with multiple lines\n   * that replaces the old comments\n   */\n  return false;\n}";
      const document = new DiffViewDocument(
        filePath,
        originalCode,
        modifiedCode,
        {},
      );

      // Create a test AggregateCheckpointInfo
      const checkpointInfo: AggregateCheckpointInfo = {
        fromTimestamp: 0,
        toTimestamp: 1000,
        conversationId: "test-conversation",
        files: [
          {
            changesSummary: {
              totalAddedLines: 3,
              totalRemovedLines: 2,
              changes: [],
            },
            changeDocument: document,
          },
        ],
      };

      // Convert to ChatRequestEditEvents
      const result = checkpointToEditEvents(checkpointInfo);

      // Verify the result
      expect(result).toEqual({
        edit_events: [
          {
            path: "/root/test.js",
            before_blob_name: undefined,
            after_blob_name: undefined,
            edits: [
              {
                before_line_start: 2,
                before_text:
                  "  // Old comment line 1\n  // Old comment line 2\n",
                after_line_start: 2,
                after_text:
                  "  /* New comment block\n   * with multiple lines\n   * that replaces the old comments\n   */\n",
              },
            ],
          },
        ],
      });
    });

    it("should handle multiple separate edits in the same file", () => {
      // Create a test DiffViewDocument with multiple separate edits
      const filePath = new QualifiedPathName("/root", "test.js");
      const originalCode =
        "function test1() {\n  return false;\n}\n\nfunction test2() {\n  return false;\n}\n\nfunction test3() {\n  return false;\n}";
      const modifiedCode =
        "function test1() {\n  return true;\n}\n\nfunction test2() {\n  return false;\n}\n\nfunction test3() {\n  return true;\n}";
      const document = new DiffViewDocument(
        filePath,
        originalCode,
        modifiedCode,
        {},
      );

      // Create a test AggregateCheckpointInfo
      const checkpointInfo: AggregateCheckpointInfo = {
        fromTimestamp: 0,
        toTimestamp: 1000,
        conversationId: "test-conversation",
        files: [
          {
            changesSummary: {
              totalAddedLines: 2,
              totalRemovedLines: 2,
              changes: [],
            },
            changeDocument: document,
          },
        ],
      };

      // Convert to ChatRequestEditEvents
      const result = checkpointToEditEvents(checkpointInfo);

      // Verify the result
      expect(result).toEqual({
        edit_events: [
          {
            path: "/root/test.js",
            before_blob_name: undefined,
            after_blob_name: undefined,
            edits: [
              {
                before_line_start: 2,
                before_text: "  return false;\n",
                after_line_start: 2,
                after_text: "  return true;\n",
              },
              {
                before_line_start: 10,
                before_text: "  return false;\n",
                after_line_start: 10,
                after_text: "  return true;\n",
              },
            ],
          },
        ],
      });
    });

    it("should handle changes across multiple files", () => {
      // Create test DiffViewDocuments for multiple files
      const filePath1 = new QualifiedPathName("/root", "file1.js");
      const originalCode1 = "function test1() {\n  return false;\n}";
      const modifiedCode1 = "function test1() {\n  return true;\n}";
      const document1 = new DiffViewDocument(
        filePath1,
        originalCode1,
        modifiedCode1,
        {},
      );

      const filePath2 = new QualifiedPathName("/root", "file2.js");
      const originalCode2 =
        "function test2() {\n  console.log('old');\n  return false;\n}";
      const modifiedCode2 =
        "function test2() {\n  console.log('new');\n  return false;\n}";
      const document2 = new DiffViewDocument(
        filePath2,
        originalCode2,
        modifiedCode2,
        {},
      );

      // Create a test AggregateCheckpointInfo with multiple files
      const checkpointInfo: AggregateCheckpointInfo = {
        fromTimestamp: 0,
        toTimestamp: 1000,
        conversationId: "test-conversation",
        files: [
          {
            changesSummary: {
              totalAddedLines: 1,
              totalRemovedLines: 1,
              changes: [],
            },
            changeDocument: document1,
          },
          {
            changesSummary: {
              totalAddedLines: 1,
              totalRemovedLines: 1,
              changes: [],
            },
            changeDocument: document2,
          },
        ],
      };

      // Convert to ChatRequestEditEvents
      const result = checkpointToEditEvents(checkpointInfo);

      // Verify the result
      expect(result).toEqual({
        edit_events: [
          {
            path: "/root/file1.js",
            before_blob_name: undefined,
            after_blob_name: undefined,
            edits: [
              {
                before_line_start: 2,
                before_text: "  return false;\n",
                after_line_start: 2,
                after_text: "  return true;\n",
              },
            ],
          },
          {
            path: "/root/file2.js",
            before_blob_name: undefined,
            after_blob_name: undefined,
            edits: [
              {
                before_line_start: 2,
                before_text: "  console.log('old');\n",
                after_line_start: 2,
                after_text: "  console.log('new');\n",
              },
            ],
          },
        ],
      });
    });
  });

  describe("checkpointToEditEventsNode", () => {
    it("should convert an AggregateCheckpointInfo to a ChatRequestNode", () => {
      // Create a test DiffViewDocument
      const filePath = new QualifiedPathName("/root", "test.js");
      const originalCode = "function test() {\n  return false;\n}";
      const modifiedCode = "function test() {\n  return true;\n}";
      const document = new DiffViewDocument(
        filePath,
        originalCode,
        modifiedCode,
        {},
      );

      // Create a test AggregateCheckpointInfo
      const checkpointInfo: AggregateCheckpointInfo = {
        fromTimestamp: 0,
        toTimestamp: 1000,
        conversationId: "test-conversation",
        files: [
          {
            changesSummary: {
              totalAddedLines: 1,
              totalRemovedLines: 1,
              changes: [],
            },
            changeDocument: document,
          },
        ],
      };

      // Convert to ChatRequestNode
      const result = checkpointToEditEventsNode(1, checkpointInfo);

      // Verify the result
      expect(result).toEqual({
        id: 1,
        type: ChatRequestNodeType.EDIT_EVENTS,
        edit_events_node: {
          edit_events: [
            {
              path: "/root/test.js",
              before_blob_name: undefined,
              after_blob_name: undefined,
              edits: [
                {
                  before_line_start: 2,
                  before_text: "  return false;\n",
                  after_line_start: 2,
                  after_text: "  return true;\n",
                },
              ],
            },
          ],
        },
      });
    });

    it("should convert an AggregateCheckpointInfo with multi-line changes to a ChatRequestNode", () => {
      // Create a test DiffViewDocument with multi-line changes
      const filePath = new QualifiedPathName("/root", "test.js");
      const originalCode =
        "function test() {\n  // Old comment\n  return false;\n}";
      const modifiedCode =
        "function test() {\n  // New comment line 1\n  // New comment line 2\n  return true;\n}";
      const document = new DiffViewDocument(
        filePath,
        originalCode,
        modifiedCode,
        {},
      );

      // Create a test AggregateCheckpointInfo
      const checkpointInfo: AggregateCheckpointInfo = {
        fromTimestamp: 0,
        toTimestamp: 1000,
        conversationId: "test-conversation",
        files: [
          {
            changesSummary: {
              totalAddedLines: 2,
              totalRemovedLines: 1,
              changes: [],
            },
            changeDocument: document,
          },
        ],
      };

      // Convert to ChatRequestNode
      const result = checkpointToEditEventsNode(1, checkpointInfo);

      // Verify the result
      expect(result).toEqual({
        id: 1,
        type: ChatRequestNodeType.EDIT_EVENTS,
        edit_events_node: {
          edit_events: [
            {
              path: "/root/test.js",
              before_blob_name: undefined,
              after_blob_name: undefined,
              edits: [
                {
                  before_line_start: 2,
                  before_text: "  // Old comment\n  return false;\n",
                  after_line_start: 2,
                  after_text:
                    "  // New comment line 1\n  // New comment line 2\n  return true;\n",
                },
              ],
            },
          ],
        },
      });
    });
  });
});
