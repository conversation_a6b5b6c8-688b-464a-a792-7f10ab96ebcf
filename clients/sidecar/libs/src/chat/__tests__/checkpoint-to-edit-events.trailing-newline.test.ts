import { diffLines } from "diff";
import { ChatRequestSingleEdit } from "../chat-types";

// Copy of the function from checkpoint-to-edit-events.ts to test in isolation
function calculateLineBasedEdits(
  originalCode: string | undefined,
  modifiedCode: string | undefined,
): ChatRequestSingleEdit[] {
  originalCode = originalCode || "";
  modifiedCode = modifiedCode || "";

  // Use diffLines from the diff library for more complex cases
  const diffResult = diffLines(originalCode, modifiedCode);
  const edits: ChatRequestSingleEdit[] = [];

  let beforeLineStart = 1; // Start at 1 for 1-indexed line numbers
  let afterLineStart = 1; // Start at 1 for 1-indexed line numbers
  let currentEdit: ChatRequestSingleEdit | null = null;

  // Process each part of the diff
  for (const part of diffResult) {
    const lines = getLines(part.value);
    const lineCount =
      part.count ||
      (lines.length > 0
        ? lines.length - (part.value.endsWith("\n") ? 0 : 1)
        : 0);

    // Skip empty parts
    if (lineCount === 0) continue;

    if (part.added || part.removed) {
      // Start a new edit if we don't have one yet
      if (!currentEdit) {
        currentEdit = {
          /* eslint-disable @typescript-eslint/naming-convention */
          before_line_start: beforeLineStart,
          before_text: part.removed ? part.value : "",
          after_line_start: afterLineStart,
          after_text: part.added ? part.value : "",
          /* eslint-enable @typescript-eslint/naming-convention */
        };
      } else {
        // Update the existing edit
        if (part.removed) {
          currentEdit.before_text += part.value;
        } else if (part.added) {
          currentEdit.after_text += part.value;
        }
      }

      // Update line counters
      if (part.removed) {
        beforeLineStart += lineCount;
      } else if (part.added) {
        afterLineStart += lineCount;
      }
    } else {
      // This is an unchanged part
      // If we have a current edit, push it and reset
      if (currentEdit) {
        // No longer removing trailing newlines
        edits.push(currentEdit);
        currentEdit = null;
      }

      // Update both line counters for unchanged parts
      beforeLineStart += lineCount;
      afterLineStart += lineCount;
    }
  }

  // Don't forget to add the last edit if there is one
  if (currentEdit) {
    // No longer removing trailing newlines
    edits.push(currentEdit);
  }

  return edits;
}

/**
 * Converts a string to an array of lines
 */
function getLines(text: string): string[] {
  return text.split(/\r?\n/);
}

describe("calculateLineBasedEdits with trailing newlines", () => {
  it("should preserve trailing newlines in edits", () => {
    const originalCode = "function test() {\n  return false;\n}\n";
    const modifiedCode = "function test() {\n  return true;\n}\n";

    const result = calculateLineBasedEdits(originalCode, modifiedCode);

    // The edit should include the trailing newline
    expect(result).toEqual([
      {
        before_line_start: 2,
        before_text: "  return false;\n",
        after_line_start: 2,
        after_text: "  return true;\n",
      },
    ]);
  });

  it("should handle multiple trailing newlines", () => {
    const originalCode = "function test() {\n  return false;\n\n\n";
    const modifiedCode = "function test() {\n  return true;\n\n\n";

    const result = calculateLineBasedEdits(originalCode, modifiedCode);

    // The edit should include the trailing newlines
    expect(result).toEqual([
      {
        before_line_start: 2,
        before_text: "  return false;\n",
        after_line_start: 2,
        after_text: "  return true;\n",
      },
    ]);
  });

  it("should handle added trailing newlines", () => {
    const originalCode = "function test() {\n  return false;\n}";
    const modifiedCode = "function test() {\n  return true;\n}\n\n";

    const result = calculateLineBasedEdits(originalCode, modifiedCode);

    // Should detect the added newlines
    expect(result).toEqual([
      {
        before_line_start: 2,
        before_text: "  return false;\n}",
        after_line_start: 2,
        after_text: "  return true;\n}\n\n",
      },
    ]);
  });

  it("should handle removed trailing newlines", () => {
    const originalCode = "function test() {\n  return false;\n}\n\n";
    const modifiedCode = "function test() {\n  return true;\n}";

    const result = calculateLineBasedEdits(originalCode, modifiedCode);

    // Should detect the removed newlines
    expect(result).toEqual([
      {
        before_line_start: 2,
        before_text: "  return false;\n}\n\n",
        after_line_start: 2,
        after_text: "  return true;\n}",
      },
    ]);
  });

  it("should handle changes only in trailing newlines", () => {
    const originalCode = "function test() {\n  return true;\n}";
    const modifiedCode = "function test() {\n  return true;\n}\n\n";

    const result = calculateLineBasedEdits(originalCode, modifiedCode);

    // Should detect only the newline changes
    expect(result).toEqual([
      {
        before_line_start: 3,
        before_text: "}",
        after_line_start: 3,
        after_text: "}\n\n",
      },
    ]);
  });

  it("should handle empty files with newlines", () => {
    const originalCode = "\n\n";
    const modifiedCode = "function test() {\n  return true;\n}\n";

    const result = calculateLineBasedEdits(originalCode, modifiedCode);

    // Should handle empty files with newlines correctly
    expect(result.length).toBeGreaterThan(0);
    expect(result[0].before_text).toBe("\n\n");
    expect(result[0].after_text).toBe("function test() {\n  return true;\n}\n");
  });

  it("should handle completely empty files", () => {
    const originalCode = "";
    const modifiedCode = "function test() {\n  return true;\n}\n";

    const result = calculateLineBasedEdits(originalCode, modifiedCode);

    // Should handle completely empty files correctly
    expect(result.length).toBeGreaterThan(0);
    expect(result[0].before_text).toBe("");
    expect(result[0].after_text).toBe("function test() {\n  return true;\n}\n");
  });

  it("should handle line count correctly with multiple trailing newlines", () => {
    const originalCode = "line1\nline2\nline3\n\n\n";
    const modifiedCode = "line1\nmodified\nline3\n\n\n";

    const result = calculateLineBasedEdits(originalCode, modifiedCode);

    // Should have correct line counts with trailing newlines
    expect(result).toEqual([
      {
        before_line_start: 2,
        before_text: "line2\n",
        after_line_start: 2,
        after_text: "modified\n",
      },
    ]);
  });

  it("should handle changes at the beginning of file with trailing newlines", () => {
    const originalCode = "first line\nsecond line\nthird line\n";
    const modifiedCode = "modified first\nsecond line\nthird line\n";

    const result = calculateLineBasedEdits(originalCode, modifiedCode);

    // Should correctly identify changes at the beginning
    expect(result).toEqual([
      {
        before_line_start: 1,
        before_text: "first line\n",
        after_line_start: 1,
        after_text: "modified first\n",
      },
    ]);
  });

  it("should handle changes at the end of file with trailing newlines", () => {
    const originalCode = "first line\nsecond line\nthird line\n";
    const modifiedCode = "first line\nsecond line\nmodified third\n";

    const result = calculateLineBasedEdits(originalCode, modifiedCode);

    // Should correctly identify changes at the end
    expect(result).toEqual([
      {
        before_line_start: 3,
        before_text: "third line\n",
        after_line_start: 3,
        after_text: "modified third\n",
      },
    ]);
  });

  it("should handle mixed newline types (\\r\\n and \\n)", () => {
    const originalCode = "line1\r\nline2\r\nline3\r\n";
    const modifiedCode = "line1\nmodified\nline3\n";

    const result = calculateLineBasedEdits(originalCode, modifiedCode);

    // Should handle mixed newline types correctly
    // The diffLines function treats different newline types as different content
    // So we expect the entire file to be treated as a change
    expect(result.length).toBeGreaterThan(0);
    expect(result[0].before_text).toContain("line1");
    expect(result[0].before_text).toContain("line2");
    expect(result[0].before_text).toContain("line3");
    expect(result[0].after_text).toContain("line1");
    expect(result[0].after_text).toContain("modified");
    expect(result[0].after_text).toContain("line3");
  });
});
