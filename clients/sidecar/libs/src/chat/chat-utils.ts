import { ImageFormatType } from "./chat-types";

export function getImageFormatFromMediaType(
  mediaType: string,
): ImageFormatType {
  const lowerMediaType = mediaType.toLowerCase();
  if (lowerMediaType.includes("jpeg") || lowerMediaType.includes("jpg")) {
    return ImageFormatType.JPEG;
  } else if (lowerMediaType.includes("gif")) {
    return ImageFormatType.GIF;
  } else if (lowerMediaType.includes("webp")) {
    return ImageFormatType.WEBP;
  }
  return ImageFormatType.PNG; // Default to PNG
}
