import * as git from "isomorphic-git";
import * as fs from "fs/promises";

/**
 * Git configuration object that matches isomorphic-git's expected interface
 * Uses isomorphic-git's parameter types directly
 */
export type GitConfig = Parameters<typeof git.log>[0] & {
  fs: typeof fs;
  dir: string;
  gitdir?: string;
  cache?: object; // isomorphic-git cache object for performance optimization
};

/**
 * Git author information for commits
 */
export interface GitAuthor {
  name: string;
  email: string;
}

/**
 * Default author for git operations
 */
export const DEFAULT_AUTHOR: GitAuthor = {
  name: "Augment Agent",
  email: "<EMAIL>",
};
