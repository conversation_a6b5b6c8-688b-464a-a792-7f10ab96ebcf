/**
 * GitStateManager - Workspace tracking for real git repositories only
 *
 * Responsibilities:
 * - Track workspace repositories by root path
 * - Provide git configurations using isomorphic-git types directly
 */

import * as path from "path";
import * as fs from "fs/promises";
import * as git from "isomorphic-git";
import type { WalkerEntry } from "isomorphic-git";
import { GitConfig, DEFAULT_AUTHOR } from "./types";
import { findGitDir, isGitRepository } from "../git-utils";
import { createRealGitConfig } from "./git-config";

// Constants for auto-adding files during git initialization
const MAX_AUTO_ADD_FILE_SIZE = 2 * 1024 * 1024; // 2MB

/**
 * Scan directory for existing files and return those suitable for auto-adding
 * Uses git walk infrastructure for efficiency
 */
async function scanForAutoAddFiles(
  gitRoot: string,
  fs: git.FsClient,
): Promise<string[]> {
  const filesToAdd: string[] = [];

  await git.walk({
    fs,
    dir: gitRoot,
    trees: [git.WORKDIR()],
    map: async (
      filepath: string,
      [workdirEntry]: Array<WalkerEntry | null>,
    ) => {
      // Skip root directory
      if (filepath === ".") return;

      // Skip and prevent traversal of .git directory entirely
      if (filepath === ".git") return null;

      // Skip README.md and .gitignore (already handled)
      if (filepath === "README.md" || filepath === ".gitignore") return;

      if (workdirEntry && (await workdirEntry.type()) === "blob") {
        try {
          // Check if file should be ignored
          const isIgnored = await git.isIgnored({ fs, dir: gitRoot, filepath });
          if (isIgnored) return;

          // Check file size using walker's stat method
          const stat = await workdirEntry.stat();
          if (stat.size <= MAX_AUTO_ADD_FILE_SIZE) {
            filesToAdd.push(filepath);
          }
        } catch (error) {
          // Skip errors and continue with other files
        }
      }
    },
  });

  return filesToAdd;
}

/**
 * Represents a tracked workspace with its git repository
 */
export interface TrackedWorkspace {
  /** Workspace root path */
  root: string;
  /** Real git repository config - includes its own cache object */
  realRepo: GitConfig;
}

/**
 * Options for adding a workspace to tracking
 */
export interface AddWorkspaceOptions {}

/**
 * GitStateManager - Manages multiple workspace git repositories
 */
export class GitStateManager {
  private trackedWorkspaces = new Map<string, TrackedWorkspace>();

  constructor() {}

  /**
   * Add a workspace to tracking by discovering its git root
   * Only tracks actual git repositories - throws error if no git root found
   */
  async addWorkspace(
    requestedPath: string,
    _options: AddWorkspaceOptions = {},
  ): Promise<TrackedWorkspace> {
    // Find the actual git root for this path
    let gitRoot = await findGitDir(requestedPath);

    // Validate that the found git directory is actually a proper git repository
    if (gitRoot && !(await isGitRepository(gitRoot))) {
      gitRoot = undefined;
    }

    // If no git repository exists at or above the requested path, initialize one at the requested path
    if (!gitRoot) {
      gitRoot = requestedPath;

      // Initialize a real git repository
      await git.init({ fs, dir: gitRoot, defaultBranch: "main" });

      // Create a minimal README to allow an initial commit
      const readmePath = path.join(gitRoot, "README.md");
      try {
        await fs.stat(readmePath);
      } catch {
        await fs.writeFile(readmePath, "# Initialized by Augment\n");
      }

      // Create .gitignore to ignore .git directory
      const gitignorePath = path.join(gitRoot, ".gitignore");
      try {
        await fs.stat(gitignorePath);
      } catch {
        await fs.writeFile(gitignorePath, ".git/\n");
      }

      // Scan for existing files to auto-add (files < 2MB)
      const filesToAdd = await scanForAutoAddFiles(gitRoot, fs);

      // Stage README.md and .gitignore
      await git.add({ fs, dir: gitRoot, filepath: "README.md" });
      await git.add({ fs, dir: gitRoot, filepath: ".gitignore" });

      // Stage existing files
      for (const filepath of filesToAdd) {
        await git.add({ fs, dir: gitRoot, filepath });
      }

      // Create single initial commit with all files
      const commitMessage =
        filesToAdd.length > 0
          ? `Initial commit with existing files (${filesToAdd.length} files added)`
          : "Initial commit";

      await git.commit({
        fs,
        dir: gitRoot,
        message: commitMessage,
        author: DEFAULT_AUTHOR,
      });
    }

    const workspaceRoot = gitRoot;

    // Check if already tracked
    if (this.trackedWorkspaces.has(workspaceRoot)) {
      return this.trackedWorkspaces.get(workspaceRoot)!;
    }

    // Create real repo config and workspace
    const realRepo = createRealGitConfig(gitRoot);

    const workspace: TrackedWorkspace = {
      root: workspaceRoot,
      realRepo: { ...realRepo, cache: {} },
    };

    this.trackedWorkspaces.set(workspaceRoot, workspace);
    return workspace;
  }

  /**
   * Get or add workspace to tracking
   * Only tracks actual git repositories - throws error if no git root found
   */
  async getOrAddWorkspace(
    requestedPath: string,
    options: AddWorkspaceOptions = {},
  ): Promise<TrackedWorkspace> {
    const gitRoot = await findGitDir(requestedPath);

    // Only track actual git repositories
    if (!gitRoot) {
      throw new Error(
        `No git repository found at or above path: ${requestedPath}`,
      );
    }

    const workspaceRoot = gitRoot;

    if (this.trackedWorkspaces.has(workspaceRoot)) {
      return this.trackedWorkspaces.get(workspaceRoot)!;
    }

    return this.addWorkspace(requestedPath, options);
  }

  /**
   * Try to add workspace to tracking, returning null if no git repository found
   * This is a safe version that doesn't initialize repos for non-git directories
   */
  async tryAddWorkspace(
    requestedPath: string,
    options: AddWorkspaceOptions = {},
  ): Promise<TrackedWorkspace | null> {
    // Check if git repository exists first
    const gitRoot = await findGitDir(requestedPath);
    if (!gitRoot) {
      // Return null if no git repository found - don't initialize
      return null;
    }

    // If git repo exists, use addWorkspace to track it
    return await this.addWorkspace(requestedPath, options);
  }

  /**
   * Get workspace by root path or any nested path within the workspace
   */
  getWorkspace(filePath: string): TrackedWorkspace | undefined {
    // First try exact match
    const exactMatch = this.trackedWorkspaces.get(filePath);
    if (exactMatch) return exactMatch;

    // Then try to find by nested path
    for (const [root, workspace] of this.trackedWorkspaces) {
      if (filePath.startsWith(root + path.sep) || filePath === root) {
        return workspace;
      }
    }

    return undefined;
  }

  /**
   * Get working git configuration for any file path
   */
  async getWorkingConfig(filePath: string): Promise<GitConfig | undefined> {
    const gitRoot = await findGitDir(filePath);

    // If we found a git repo, use it as the workspace root
    if (gitRoot) {
      const workspace = await this.getOrAddWorkspace(gitRoot);
      return workspace.realRepo;
    }

    // If no git repo found, check if this path is already tracked as a workspace
    const existingWorkspace = this.getWorkspace(filePath);
    if (existingWorkspace) {
      return existingWorkspace.realRepo;
    }

    return undefined;
  }

  /**
   * Get configuration for a workspace
   */
  getConfigs(workspaceRoot: string):
    | {
        real: GitConfig;
      }
    | undefined {
    const workspace = this.trackedWorkspaces.get(workspaceRoot);
    if (!workspace) return undefined;

    return {
      real: workspace.realRepo,
    };
  }

  /**
   * Get all tracked workspaces
   */
  getTrackedWorkspaces(): string[] {
    return Array.from(this.trackedWorkspaces.keys());
  }

  /**
   * Get workspace information
   */
  getWorkspaceInfo(workspaceRoot: string):
    | {
        root: string;
        hasRealRepo: boolean;
      }
    | undefined {
    const workspace = this.trackedWorkspaces.get(workspaceRoot);
    if (!workspace) return undefined;

    return {
      root: workspace.root,
      hasRealRepo: !!workspace.realRepo,
    };
  }

  /**
   * Remove workspace from tracking and clear its cache
   */
  removeWorkspace(workspaceRoot: string): boolean {
    const workspace = this.trackedWorkspaces.get(workspaceRoot);
    const removed = this.trackedWorkspaces.delete(workspaceRoot);

    if (removed && workspace) {
      // Clear the cache object by replacing it with a new empty object
      workspace.realRepo.cache = {};
    }

    return removed;
  }

  /**
   * Clear all tracked workspaces and their caches
   */
  clear(): void {
    // Clear all cache objects before removing workspaces
    for (const workspace of this.trackedWorkspaces.values()) {
      workspace.realRepo.cache = {};
    }

    this.trackedWorkspaces.clear();
  }

  /**
   * Cache management methods
   */

  /**
   * Clear cache for a specific workspace
   */
  clearWorkspaceCache(workspaceRoot: string): void {
    const workspace = this.trackedWorkspaces.get(workspaceRoot);
    if (workspace) {
      // Replace cache object with new empty one directly in GitConfig
      workspace.realRepo.cache = {};
    }
  }

  /**
   * Clear all caches (useful for memory management)
   */
  clearAllCaches(): void {
    for (const workspace of this.trackedWorkspaces.values()) {
      // Replace cache object with new empty one directly in GitConfig
      workspace.realRepo.cache = {};
    }
  }

  /**
   * Get simple cache statistics for monitoring
   */
  getCacheStats() {
    const workspaceCount = this.trackedWorkspaces.size;

    return {
      totalWorkspaces: workspaceCount,
      workspacesWithRealRepos: workspaceCount,
    };
  }
}
