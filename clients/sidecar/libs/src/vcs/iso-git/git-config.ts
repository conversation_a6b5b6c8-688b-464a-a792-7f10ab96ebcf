/**
 * Git configuration utilities using isomorphic-git types directly
 */

import * as fs from "fs/promises";
import * as path from "path";
import { GitConfig } from "./types";

/**
 * Create real git configuration for production commits
 */
export function createRealGitConfig(
  workingDir: string,
  overrides: Partial<GitConfig> = {},
): GitConfig {
  return {
    fs,
    dir: workingDir,
    gitdir: path.join(workingDir, ".git"),
    ...overrides,
  };
}
