/**
 * Core Status Matrix Algorithm
 * Contains the core logic for status matrix calculation following isomorphic-git patterns
 */

import * as git from "isomorphic-git";
import type { WalkerEntry } from "isomorphic-git";
import type { StatusMatrixOptions } from "./types";
import { worthWalking } from "./utils";

// Git file mode constants
export const GIT_FILE_MODES = {
  DIRECTORY: 0o40000,
  FILE: 0o100644,
  EXECUTABLE: 0o100755,
  SYMLINK: 0o120000,
} as const;

// Git object constants
export const GIT_CONSTANTS = {
  EMPTY_TREE_OID: "4b825dc642cb6eb9a060e54bf8d69288fbee4904",
  PLACEHOLDER_OID: "42",
} as const;

/**
 * Create HEAD walker with fallback for missing refs
 */
export async function createHeadWalker(options: StatusMatrixOptions) {
  try {
    const resolvedRef = await git.resolveRef({
      fs: options.fs,
      dir: options.dir,
      gitdir: options.gitdir,
      ref: options.ref || "HEAD",
    });
    return git.TREE({ ref: resolvedRef });
  } catch {
    // Use empty tree for new repos with no commits
    return git.TREE({ ref: GIT_CONSTANTS.EMPTY_TREE_OID });
  }
}

/**
 * Check if file should be ignored (optimized version)
 */
export async function checkIgnored(
  filepath: string,
  options: StatusMatrixOptions,
): Promise<boolean> {
  try {
    return await git.isIgnored({
      fs: options.fs,
      dir: options.dir,
      gitdir: options.gitdir,
      filepath,
    });
  } catch {
    return false;
  }
}

/**
 * Calculate status row using isomorphic-git's exact algorithm
 * This is the core optimization - follows the reference implementation exactly
 */
export async function calculateOptimizedStatusRow(
  filepath: string,
  head: WalkerEntry | null,
  workdir: WalkerEntry | null,
  stage: WalkerEntry | null,
  headType?: string,
  workdirType?: string,
  stageType?: string,
): Promise<[string, number, number, number]> {
  // Get OIDs following the reference implementation's exact pattern
  const headOid = headType === "blob" ? await head?.oid() : undefined;
  const stageOid = stageType === "blob" ? await stage?.oid() : undefined;

  let workdirOid: string | undefined;
  if (headType !== "blob" && workdirType === "blob" && stageType !== "blob") {
    // Optimization from reference implementation: use placeholder OID
    workdirOid = GIT_CONSTANTS.PLACEHOLDER_OID;
  } else if (workdirType === "blob") {
    workdirOid = await workdir?.oid();
  }

  // Use the exact algorithm from isomorphic-git
  const entry = [undefined, headOid, workdirOid, stageOid];
  const result = entry.map((value) => entry.indexOf(value));
  result.shift(); // remove leading undefined entry

  return [filepath, ...result] as [string, number, number, number];
}

/**
 * Process a single entry in the status matrix walk
 * This contains the core logic from the map function
 */
export async function processStatusEntry(
  filepath: string,
  head: WalkerEntry | null,
  workdir: WalkerEntry | null,
  stage: WalkerEntry | null,
  options: StatusMatrixOptions,
  metricsTracker: { incrementIgnored: () => void; incrementFiles: () => void },
): Promise<[string, number, number, number] | null | undefined> {
  // Skip root directory
  if (filepath === ".") return;

  // Handle ignored files early (following reference implementation)
  if (!head && !stage && workdir && !options.ignored) {
    const isIgnored = await checkIgnored(filepath, options);
    if (isIgnored) {
      metricsTracker.incrementIgnored();
      return null;
    }
  }

  // Apply filepath filtering (following reference implementation)
  if (
    options.filepaths &&
    !options.filepaths.some((base) => worthWalking(filepath, base))
  ) {
    return null;
  }

  // Apply custom filter if provided
  if (options.filter && !options.filter(filepath)) {
    return;
  }

  // Check for symlinks using mode() first (0o120000 = symlink)
  const [headMode, workdirMode, stageMode] = await Promise.all([
    head?.mode().catch(() => undefined),
    workdir?.mode().catch(() => undefined),
    stage?.mode().catch(() => undefined),
  ]);

  // If any entry is a symlink, skip it (following git's behavior)
  if (
    headMode === GIT_FILE_MODES.SYMLINK ||
    workdirMode === GIT_FILE_MODES.SYMLINK ||
    stageMode === GIT_FILE_MODES.SYMLINK
  ) {
    return null; // Skip symlinks entirely
  }

  // Get types in parallel (following reference implementation)
  const [headType, workdirType, stageType] = await Promise.all([
    head?.type(),
    workdir?.type(),
    stage?.type(),
  ]);

  const isBlob = [headType, workdirType, stageType].includes("blob");

  // Follow isomorphic-git's exact logic for handling different file types
  // For directories, bail unless there's also a blob in another tree
  if (headType === "tree" && !isBlob) return;
  if (headType === "commit") return null;
  if (workdirType === "tree" && !isBlob) return;
  if (stageType === "commit") return null;
  if (stageType === "tree" && !isBlob) return;

  // Calculate status row using optimized algorithm
  const statusRow = await calculateOptimizedStatusRow(
    filepath,
    head,
    workdir,
    stage,
    headType,
    workdirType,
    stageType,
  );

  metricsTracker.incrementFiles();
  return statusRow;
}
