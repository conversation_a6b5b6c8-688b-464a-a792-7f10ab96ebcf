/**
 * Enhanced Status Matrix Module
 *
 * This module provides a configurable implementation of isomorphic-git's statusMatrix
 * with additional features for performance optimization and customization.
 *
 * Key Features:
 * - Drop-in replacement for isomorphic-git's statusMatrix
 * - Configurable symlink and submodule traversal
 * - Custom ignore patterns beyond .gitignore
 * - Performance metrics and monitoring
 * - Binary file detection
 * - Depth limiting for large repositories
 * - Enhanced error handling
 * - Batch processing for memory efficiency
 *
 * Based on research of isomorphic-git's internal implementation patterns.
 */

// Core implementation
export { enhancedStatusMatrix, statusMatrix } from "./enhanced-status-matrix";

// Git operations
export { compareGitRefs } from "./git-operations";

// Core algorithm utilities
export {
  createHeadWalker,
  processStatusEntry,
  GIT_FILE_MODES,
  GIT_CONSTANTS,
} from "./core-algorithm";

// Types and interfaces
export type {
  StatusMatrixOptions,
  StatusMatrixResult,
  StatusMatrix,
  StatusRow,
  StatusValue,
  StatusMatrixMetrics,
} from "./types";

// Utilities
export {
  worthWalking,
  createMetricsTracker,
  validateOptions,
  mergeOptions,
  formatFilePath,
  matchesPatterns,
  formatStatusMatrix,
} from "./utils";

/**
 * Quick start example:
 *
 * ```typescript
 * import { statusMatrix, enhancedStatusMatrix } from './status-matrix';
 *
 * // Drop-in replacement for isomorphic-git
 * const matrix = await statusMatrix({
 *   fs,
 *   dir: '/path/to/repo'
 * });
 *
 * // Enhanced version with custom options
 * const result = await enhancedStatusMatrix({
 *   fs,
 *   dir: '/path/to/repo',
 *   traverseSymlinks: false,
 *   traverseSubmodules: false,
 *   maxDepth: 10,
 *   detectBinary: true,
 *   customIgnorePatterns: ['*.tmp', 'node_modules']
 * });
 *
 * console.log('Files processed:', result.metrics.filesProcessed);
 * console.log('Execution time:', result.metrics.executionTimeMs, 'ms');
 * ```
 */

/**
 * Performance optimization tips:
 *
 * 1. Set traverseSymlinks: false for better performance (default)
 * 2. Set traverseSubmodules: false if you don't need submodule info (default)
 * 3. Use filepaths to limit scope when checking specific files
 * 4. Set maxDepth for very deep directory structures
 * 5. Use batchSize to control memory usage in large repositories
 * 6. Enable caching through the cache parameter
 * 7. Use fastFileStatus for checking specific files
 * 8. Use getChangedFiles if you only need changed files
 */

/**
 * Compatibility notes:
 *
 * - statusMatrix() provides exact compatibility with isomorphic-git
 * - enhancedStatusMatrix() adds configuration options while maintaining the same core algorithm
 * - All functions follow isomorphic-git's internal patterns for optimal performance
 * - Error handling is enhanced but maintains compatibility
 * - Results format matches isomorphic-git's StatusMatrix type exactly
 */
