/* eslint-disable @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unsafe-call, @typescript-eslint/no-unsafe-member-access */
/**
 * Git Operations
 * Git-specific operations for comparing references and detecting changes
 */

import * as git from "isomorphic-git";
import type { WalkerEntry } from "isomorphic-git";
import type { StatusMatrixOptions } from "./types";
import { validateOptions, mergeOptions } from "./utils";
import { GIT_FILE_MODES, GIT_CONSTANTS } from "./core-algorithm";

/**
 * Compare two git references and return changed files
 * Optimized version following isomorphic-git patterns
 */
export async function compareGitRefs(
  options: StatusMatrixOptions & {
    fromRef: string;
    toRef: string;
  },
): Promise<Array<{ filepath: string; status: string }>> {
  // Validate and merge options with defaults
  validateOptions(options);
  const mergedOptions = mergeOptions(options);

  // Create base config
  const config = {
    fs: mergedOptions.fs,
    dir: mergedOptions.dir,
    gitdir: mergedOptions.gitdir,
    cache: mergedOptions.cache,
  };

  // Create walkers for the two references
  const walkers = [];

  // Handle special WORKDIR reference
  if (options.fromRef === "WORKDIR") {
    walkers.push(git.WORKDIR());
  } else {
    try {
      const resolvedRef = await git.resolveRef({
        ...config,
        ref: options.fromRef,
      });
      walkers.push(git.TREE({ ref: resolvedRef }));
    } catch {
      // Use empty tree for missing refs
      walkers.push(git.TREE({ ref: GIT_CONSTANTS.EMPTY_TREE_OID }));
    }
  }

  if (options.toRef === "WORKDIR") {
    walkers.push(git.WORKDIR());
  } else {
    try {
      const resolvedRef = await git.resolveRef({
        ...config,
        ref: options.toRef,
      });
      walkers.push(git.TREE({ ref: resolvedRef }));
    } catch {
      // Use empty tree for missing refs
      walkers.push(git.TREE({ ref: GIT_CONSTANTS.EMPTY_TREE_OID }));
    }
  }

  // Use git.walk to compare the two trees
  const results = await git.walk({
    ...config,
    trees: walkers,
    map: async (
      filepath: string,
      [fromEntry, toEntry]: Array<WalkerEntry | null>,
    ) => {
      // Skip root directory
      if (filepath === ".") return;

      // Skip .git directory and its contents (following git status behavior)
      if (filepath === ".git" || filepath.startsWith(".git/")) {
        return null; // Return null to prevent traversal
      }

      // Check for symlinks using mode() first (0o120000 = symlink)
      const [fromMode, toMode] = await Promise.all([
        fromEntry?.mode().catch(() => undefined),
        toEntry?.mode().catch(() => undefined),
      ]);

      // Handle symlinks based on configuration
      if (
        fromMode === GIT_FILE_MODES.SYMLINK ||
        toMode === GIT_FILE_MODES.SYMLINK
      ) {
        if (!mergedOptions.traverseSymlinks) {
          return null; // Return null to prevent traversal of symlink children
        }
      }

      // Get types in parallel
      const [fromType, toType] = await Promise.all([
        fromEntry?.type(),
        toEntry?.type(),
      ]);

      // Handle submodules based on configuration
      if (fromType === "commit" || toType === "commit") {
        if (!mergedOptions.traverseSubmodules) {
          return null; // Return null to prevent traversal of submodule children
        }
      }

      // Determine if this is a blob change
      const isBlob = [fromType, toType].includes("blob");

      // For directories, allow traversal but don't report unless there's a blob conflict
      if ((fromType === "tree" || toType === "tree") && !isBlob) {
        return; // Return undefined to allow traversal but not include in results
      }

      // Detect file changes for blobs
      if (isBlob) {
        return await detectFileChange(
          filepath,
          fromEntry,
          toEntry,
          fromType,
          toType,
        );
      }
    },
  });

  return (results || []).filter(Boolean) as Array<{
    filepath: string;
    status: string;
  }>;
}

/**
 * Detect changes between two file entries
 */
export async function detectFileChange(
  filepath: string,
  fromEntry: WalkerEntry | null,
  toEntry: WalkerEntry | null,
  fromType?: string,
  toType?: string,
): Promise<{ filepath: string; status: string } | null> {
  // File added
  if (!fromEntry && toEntry) {
    return { filepath, status: "added" };
  }

  // File deleted
  if (fromEntry && !toEntry) {
    return { filepath, status: "deleted" };
  }

  // File exists in both - check for modifications
  if (fromEntry && toEntry) {
    const actualFromType = fromType || (await fromEntry.type());
    const actualToType = toType || (await toEntry.type());

    // Type changed
    if (actualFromType !== actualToType) {
      return { filepath, status: "modified" };
    }

    // Content changed (for blobs)
    if (actualFromType === "blob" && actualToType === "blob") {
      const [fromOid, toOid] = await Promise.all([
        fromEntry.oid(),
        toEntry.oid(),
      ]);

      if (fromOid !== toOid) {
        return { filepath, status: "modified" };
      }
    }
  }

  return null;
}
