/* eslint-disable @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unsafe-member-access, @typescript-eslint/no-unsafe-return, @typescript-eslint/no-unsafe-call, @typescript-eslint/no-unsafe-argument */
/**
 * Tests for Enhanced Status Matrix
 */

import * as fs from "fs/promises";
import * as git from "isomorphic-git";
import { enhancedStatusMatrix, statusMatrix } from "../enhanced-status-matrix";
import { type StatusMatrixOptions } from "../types";

// Mock setup
jest.mock("isomorphic-git");
const mockGit = git as jest.Mocked<typeof git>;

// Helper to create mock WalkerEntry
function createMockWalkerEntry(type: string, oid?: string): any {
  return {
    type: () => Promise.resolve(type),
    mode: () => Promise.resolve(0o100644),
    oid: () => Promise.resolve(oid || "abc123"),
    content: () => Promise.resolve(new Uint8Array()),
    stat: () =>
      Promise.resolve({
        ctimeSeconds: 0,
        ctimeNanoseconds: 0,
        mtimeSeconds: Date.now() / 1000,
        mtimeNanoseconds: 0,
        dev: 0,
        ino: 0,
        mode: 0o100644,
        uid: 0,
        gid: 0,
        size: 1024,
      }),
  };
}

describe("Enhanced Status Matrix", () => {
  const baseOptions: StatusMatrixOptions = {
    fs,
    dir: "/test/repo",
    gitdir: "/test/repo/.git",
  };

  beforeEach(() => {
    jest.clearAllMocks();

    // Mock basic git operations
    mockGit.resolveRef.mockResolvedValue("abc123");
    mockGit.TREE.mockReturnValue({} as any);
    mockGit.WORKDIR.mockReturnValue({} as any);
    mockGit.STAGE.mockReturnValue({} as any);
  });

  describe("statusMatrix", () => {
    it("should provide drop-in compatibility with isomorphic-git", async () => {
      // Mock walk to return basic status matrix results
      mockGit.walk.mockImplementation(async ({ map }) => {
        const results = [];
        if (map) {
          // Simulate processing files
          const file1Result = await map("file1.txt", [
            createMockWalkerEntry("blob", "abc123"),
            createMockWalkerEntry("blob", "def456"),
            createMockWalkerEntry("blob", "abc123"),
          ]);
          if (file1Result) results.push(file1Result);

          const file2Result = await map("file2.txt", [
            null,
            createMockWalkerEntry("blob", "def456"),
            null,
          ]);
          if (file2Result) results.push(file2Result);
        }
        return results;
      });

      const result = await statusMatrix(baseOptions);

      expect(Array.isArray(result)).toBe(true);
      expect(mockGit.walk).toHaveBeenCalledWith(
        expect.objectContaining({
          trees: expect.any(Array),
          map: expect.any(Function),
        }),
      );
    });

    it("should handle missing HEAD reference gracefully", async () => {
      mockGit.resolveRef.mockRejectedValue(new Error("Reference not found"));
      mockGit.walk.mockResolvedValue([]);

      const result = await statusMatrix(baseOptions);

      expect(result).toEqual([]);
      // Should use empty tree fallback
      expect(mockGit.TREE).toHaveBeenCalledWith({
        ref: "4b825dc642cb6eb9a060e54bf8d69288fbee4904",
      });
    });
  });

  describe("enhancedStatusMatrix", () => {
    it("should return metrics and enhanced data", async () => {
      mockGit.walk.mockImplementation(async ({ map }) => {
        const results = [];
        if (map) {
          const result = await map("file1.txt", [
            createMockWalkerEntry("blob", "abc123"),
            createMockWalkerEntry("blob", "def456"),
            createMockWalkerEntry("blob", "abc123"),
          ]);
          if (result) results.push(result);
        }
        return results;
      });

      const result = await enhancedStatusMatrix({
        ...baseOptions,
        detectBinary: true,
      });

      expect(Array.isArray(result.matrix)).toBe(true);
      expect(result.metrics).toMatchObject({
        filesProcessed: expect.any(Number),
        executionTimeMs: expect.any(Number),
      });
    });

    it("should respect symlink traversal configuration", async () => {
      mockGit.walk.mockImplementation(async ({ map }) => {
        if (map) {
          // Simulate symlink entry
          await map("symlink", [
            createMockWalkerEntry("special"),
            createMockWalkerEntry("special"),
            null,
          ]);
        }
        return [];
      });

      await enhancedStatusMatrix({
        ...baseOptions,
        traverseSymlinks: false,
      });

      // Should be called but return null for symlinks
      expect(mockGit.walk).toHaveBeenCalled();
    });

    it("should respect submodule traversal configuration", async () => {
      mockGit.walk.mockImplementation(async ({ map }) => {
        if (map) {
          // Simulate submodule entry
          await map("submodule", [createMockWalkerEntry("commit"), null, null]);
        }
        return [];
      });

      await enhancedStatusMatrix({
        ...baseOptions,
        traverseSubmodules: false,
      });

      expect(mockGit.walk).toHaveBeenCalled();
    });

    it("should apply custom ignore patterns", async () => {
      mockGit.walk.mockImplementation(async ({ map }) => {
        if (map) {
          // Simulate files that should be ignored
          await map("temp.tmp", [null, createMockWalkerEntry("blob"), null]);
          await map("regular.txt", [null, createMockWalkerEntry("blob"), null]);
        }
        return [];
      });

      await enhancedStatusMatrix({
        ...baseOptions,
        customIgnorePatterns: ["*.tmp"],
      });

      expect(mockGit.walk).toHaveBeenCalled();
    });

    it("should respect depth limits", async () => {
      mockGit.walk.mockImplementation(async ({ map }) => {
        if (map) {
          // Simulate deep directory structure
          await map("level1/level2/level3/file.txt", [
            null,
            createMockWalkerEntry("blob"),
            null,
          ]);
        }
        return [];
      });

      await enhancedStatusMatrix({
        ...baseOptions,
        maxDepth: 2,
      });

      expect(mockGit.walk).toHaveBeenCalled();
    });
  });

  describe("error handling", () => {
    it("should handle walk errors gracefully", async () => {
      mockGit.walk.mockRejectedValue(new Error("Walk failed"));

      await expect(enhancedStatusMatrix(baseOptions)).rejects.toThrow(
        "Status matrix error: Walk failed",
      );
    });

    it("should validate required options", async () => {
      await expect(
        enhancedStatusMatrix({ fs, dir: "" } as StatusMatrixOptions),
      ).rejects.toThrow("dir path is required");

      await expect(
        enhancedStatusMatrix({ dir: "/test" } as StatusMatrixOptions),
      ).rejects.toThrow("fs client is required");
    });

    it("should validate depth limits", async () => {
      await expect(
        enhancedStatusMatrix({
          ...baseOptions,
          maxDepth: -1,
        }),
      ).rejects.toThrow("maxDepth must be non-negative");
    });
  });
});
