# Enhanced Status Matrix

A high-performance, configurable implementation of isomorphic-git's `statusMatrix` with additional features for optimization and customization.

## Overview

This module provides a drop-in replacement for isomorphic-git's `statusMatrix` function with optimized performance that follows the reference implementation exactly. It maintains exact compatibility while offering performance monitoring and additional utility functions for git operations.

## Key Features

- **Drop-in Compatibility**: Exact API compatibility with isomorphic-git's statusMatrix
- **Performance Optimized**: Intelligent caching and optimized traversal patterns
- **Configurable Symlink Traversal**: Control whether to traverse symlinks (disabled by default for performance)
- **Configurable Submodule Traversal**: Control whether to traverse submodules (disabled by default)
- **Custom Ignore Patterns**: Add ignore patterns beyond .gitignore
- **Comprehensive Metrics**: Built-in performance monitoring with detailed metrics
- **Binary File Detection**: Optional binary file detection with metadata
- **Depth Limiting**: Limit traversal depth for large repositories
- **Enhanced Error Handling**: Robust error handling with specific error types
- **Memory Efficient**: Batch processing and streaming for large repositories
- **Git Diff Operations**: Built-in support for comparing git references

## Implementation Details

This implementation is based on extensive research of isomorphic-git's internal source code, particularly:

- `statusMatrix.js` - Core status matrix algorithm
- `walk.js` - Tree walking implementation
- `unionOfIterators.js` - Iterator merging patterns
- `worthWalking.js` - Path filtering utilities

### Key Optimizations Applied

1. **Symlink Skipping**: Returns `null` for symlink entries to prevent traversal of their children
2. **Parallel Operations**: Uses `Promise.all()` for type checking and OID computation
3. **Early Filtering**: Applies path and custom filters early in the process
4. **Memoization Awareness**: Leverages WalkerEntry method memoization
5. **Empty Tree Fallback**: Handles missing HEAD references gracefully

## Usage

### Basic Usage (Drop-in Replacement)

```typescript
import { statusMatrix } from './status-matrix';

const matrix = await statusMatrix({
  fs,
  dir: '/path/to/repo'
});

// Result format: [filepath, HEAD_status, WORKDIR_status, STAGE_status][]
// Status values: 0=absent, 1=identical to HEAD, 2=different from HEAD, 3=different from WORKDIR
```

### Enhanced Usage with Configuration

```typescript
import { enhancedStatusMatrix } from './status-matrix';

const result = await enhancedStatusMatrix({
  fs,
  dir: '/path/to/repo',

  // Performance options
  traverseSymlinks: false,        // Skip symlinks (default: false)
  traverseSubmodules: false,      // Skip submodules (default: false)
  maxDepth: 10,                   // Limit traversal depth
  batchSize: 100,                 // Batch size for processing

  // Filtering options
  filepaths: ['src/', 'docs/'],   // Limit to specific paths
  customIgnorePatterns: [         // Additional ignore patterns
    '*.tmp',
    'node_modules',
    '.DS_Store'
  ],

  // Enhancement options
  detectBinary: true,             // Detect binary files
  ignored: false,                 // Include ignored files

  // Standard options
  ref: 'HEAD',                    // Reference to compare against
  cache: {},                      // Cache object for performance
});

console.log('Status Matrix:', result.matrix);
console.log('Files Processed:', result.metrics.filesProcessed);
console.log('Execution Time:', result.metrics.executionTimeMs, 'ms');
console.log('Enhanced Data:', result.enhanced);
```

### Specialized Functions

```typescript
import {
  getChangedFiles,
  fastFileStatus,
  statusMatrixWithMetrics,
  compareGitRefs,
  statusMatrixEnhanced
} from './status-matrix';

// Get only changed files with readable status
const changedFiles = await getChangedFiles({ fs, dir });
// Result: [{ filepath: 'file.txt', status: 'modified-unstaged' }]

// Fast status check for specific files
const specificStatus = await fastFileStatus({ fs, dir }, ['file1.txt', 'file2.txt']);

// Get status with performance metrics
const { matrix, metrics } = await statusMatrixWithMetrics({ fs, dir });

// Compare two git references (branches, commits, etc.)
const diffResults = await compareGitRefs({
  fs, dir,
  fromRef: 'main',
  toRef: 'feature-branch'
});
// Result: [{ filepath: 'file.txt', status: 'modified' }]

// Get enhanced status with binary detection and metadata
const { matrix, enhanced } = await statusMatrixEnhanced({
  fs, dir,
  detectBinary: true
});
// enhanced contains additional metadata like file size, binary status, etc.
```

## Configuration Options

### Core Options (isomorphic-git compatible)

| Option | Type | Default | Description |
|--------|------|---------|-------------|
| `fs` | `FsClient` | required | File system client |
| `dir` | `string` | required | Working tree directory path |
| `gitdir` | `string` | `${dir}/.git` | Git directory path |
| `ref` | `string` | `'HEAD'` | Reference to compare against |
| `filepaths` | `string[]` | `['.']` | Limit query to specific paths |
| `filter` | `function` | undefined | Filter function for file paths |
| `cache` | `object` | `{}` | Cache object for performance |
| `ignored` | `boolean` | `false` | Include ignored files |

### Enhanced Options

| Option | Type | Default | Description |
|--------|------|---------|-------------|
| `traverseSymlinks` | `boolean` | `false` | Whether to traverse symlinks |
| `traverseSubmodules` | `boolean` | `false` | Whether to traverse submodules |
| `maxDepth` | `number` | unlimited | Maximum traversal depth |
| `customIgnorePatterns` | `string[]` | `[]` | Additional ignore patterns |
| `detectBinary` | `boolean` | `false` | Detect binary files |
| `batchSize` | `number` | `100` | Batch size for processing |

## Performance Considerations

### Symlink Handling

By default, symlinks are not traversed for performance reasons. This follows the optimization pattern found in isomorphic-git's source code where returning `null` for symlink entries prevents traversal of their children.

```typescript
// High performance (default)
const result = await enhancedStatusMatrix({
  fs, dir,
  traverseSymlinks: false  // Skip symlink traversal
});

// Complete traversal (slower)
const result = await enhancedStatusMatrix({
  fs, dir,
  traverseSymlinks: true   // Traverse symlinks
});
```

### Large Repository Optimization

For large repositories, use these optimizations:

```typescript
const result = await enhancedStatusMatrix({
  fs, dir,
  maxDepth: 5,                    // Limit depth
  batchSize: 50,                  // Smaller batches
  filepaths: ['src/', 'tests/'],  // Limit scope
  customIgnorePatterns: [         // Skip unnecessary files
    'node_modules',
    '*.log',
    '.git'
  ]
});
```

### Memory Management

The implementation includes several memory management optimizations:

1. **Streaming Processing**: Results are processed as they're generated
2. **Batch Processing**: Large file sets are processed in configurable batches
3. **Early Filtering**: Unnecessary files are filtered out early
4. **Memoization**: Leverages isomorphic-git's built-in memoization

## Metrics and Monitoring

The enhanced status matrix provides detailed performance metrics:

```typescript
const result = await enhancedStatusMatrix({ fs, dir });

console.log('Performance Metrics:');
console.log('- Files Processed:', result.metrics.filesProcessed);
console.log('- Directories Traversed:', result.metrics.directoriesTraversed);
console.log('- Files Ignored:', result.metrics.filesIgnored);
console.log('- Symlinks Encountered:', result.metrics.symlinksEncountered);
console.log('- Submodules Encountered:', result.metrics.submodulesEncountered);
console.log('- Execution Time:', result.metrics.executionTimeMs, 'ms');
console.log('- Peak Memory:', result.metrics.peakMemoryMB, 'MB');
```

## Integration with Existing Code

This module is designed to integrate seamlessly with existing isomorphic-git usage:

```typescript
// Before: using isomorphic-git directly
import * as git from 'isomorphic-git';
const matrix = await git.statusMatrix({ fs, dir });

// After: using enhanced status matrix
import { statusMatrix } from './status-matrix';
const matrix = await statusMatrix({ fs, dir }); // Drop-in replacement

// Or with enhancements
import { enhancedStatusMatrix } from './status-matrix';
const result = await enhancedStatusMatrix({
  fs, dir,
  traverseSymlinks: false,
  detectBinary: true
});
```

## Testing

The module includes comprehensive tests covering:

- Drop-in compatibility with isomorphic-git
- Configuration option handling
- Error scenarios and edge cases
- Performance optimizations and benchmarks
- Symlink and submodule handling
- Custom ignore patterns
- Binary file detection
- Git reference comparison
- Cache integration
- Memory usage and batch processing

Run tests with:
```bash
npm test -- status-matrix
```

### Performance Testing

Performance tests compare the enhanced implementation against native isomorphic-git:

```bash
npm test -- status-matrix/performance
```

These tests validate that the enhanced version maintains or improves upon isomorphic-git's performance while adding additional features.

## Architecture

The enhanced status matrix is built on a modular architecture:

- **enhanced-status-matrix.ts**: Main implementation with all public APIs
- **walker-processor.ts**: Core logic for processing git walker entries
- **utils.ts**: Utility functions for path handling, validation, and metrics
- **types.ts**: Comprehensive TypeScript type definitions
- **index.ts**: Public API exports and documentation

### Integration with Git Cache Manager

The implementation is optimized for performance following isomorphic-git's exact patterns:

```typescript
// Optimized implementation with minimal overhead
const result = await enhancedStatusMatrix({ fs, dir });
```

Performance optimizations include:
- Direct walker creation without abstraction layers
- Exact algorithm matching from isomorphic-git reference implementation
- Smart OID handling with placeholder optimization
- File status caching with mtime-based invalidation

## References

This implementation is based on extensive research of isomorphic-git's source code:

- [statusMatrix.js](https://github.com/isomorphic-git/isomorphic-git/blob/main/src/api/statusMatrix.js) - Core algorithm
- [walk.js](https://github.com/isomorphic-git/isomorphic-git/blob/main/src/commands/walk.js) - Tree walking patterns
- [worthWalking.js](https://github.com/isomorphic-git/isomorphic-git/blob/main/src/utils/worthWalking.js) - Path filtering
- [unionOfIterators.js](https://github.com/isomorphic-git/isomorphic-git/blob/main/src/utils/unionOfIterators.js) - Iterator merging

### Performance Research

The optimizations applied are based on systematic analysis of isomorphic-git's performance characteristics and bottlenecks, particularly around packfile parsing and tree traversal patterns.
