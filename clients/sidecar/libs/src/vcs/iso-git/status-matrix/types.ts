/**
 * Enhanced Status Matrix Types
 * Extends isomorphic-git's native types with configurable options
 */

import type {
  FsClient,
  StatusRow as IsoGitStatusRow,
  HeadStatus,
  WorkdirStatus,
  StageStatus,
} from "isomorphic-git";

// Re-export isomorphic-git types directly
export type StatusRow = IsoGitStatusRow;
export type StatusValue = HeadStatus | WorkdirStatus | StageStatus;
export type StatusMatrix = StatusRow[];

/**
 * Configuration options for enhanced status matrix
 * Extends isomorphic-git's statusMatrix parameters with additional options
 */
export interface StatusMatrixOptions {
  // Core isomorphic-git statusMatrix parameters
  fs: FsClient;
  dir: string;
  gitdir?: string;
  ref?: string;
  filepaths?: string[];
  filter?: (filepath: string) => boolean;
  cache?: object;
  ignored?: boolean;

  // Enhanced configuration options (only used by compareGitRefs, not enhancedStatusMatrix)
  traverseSymlinks?: boolean;
  traverseSubmodules?: boolean;
  maxDepth?: number;
  customIgnorePatterns?: string[];
  detectBinary?: boolean;
}

/**
 * Performance metrics for status matrix operations
 */
export interface StatusMatrixMetrics {
  filesProcessed: number;
  directoriesTraversed: number;
  filesIgnored: number;
  symlinksEncountered: number;
  submodulesEncountered: number;
  executionTimeMs: number;
  peakMemoryMB?: number;
}

/**
 * Status matrix result with metrics
 */
export interface StatusMatrixResult {
  matrix: StatusMatrix;
  metrics: StatusMatrixMetrics;
}
