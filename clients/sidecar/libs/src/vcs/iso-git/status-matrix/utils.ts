/**
 * Utility functions for status matrix operations
 * Based on isomorphic-git's internal utilities
 */

import type { StatusMatrixOptions, StatusMatrixMetrics } from "./types";

/**
 * Path filtering utility based on isomorphic-git's worthWalking function
 * Determines if a filepath is worth walking based on base paths
 */
export function worthWalking(filepath: string, base: string): boolean {
  if (filepath === "." || base == null || base.length === 0 || base === ".") {
    return true;
  }
  if (base.length >= filepath.length) {
    return base.startsWith(filepath);
  } else {
    return filepath.startsWith(base);
  }
}

/**
 * Create performance metrics tracker
 */
export function createMetricsTracker(): {
  metrics: StatusMatrixMetrics;
  incrementFiles: () => void;
  incrementDirectories: () => void;
  incrementIgnored: () => void;
  incrementSymlinks: () => void;
  incrementSubmodules: () => void;
  finish: () => void;
} {
  const startTime = performance.now();
  const metrics: StatusMatrixMetrics = {
    filesProcessed: 0,
    directoriesTraversed: 0,
    filesIgnored: 0,
    symlinksEncountered: 0,
    submodulesEncountered: 0,
    executionTimeMs: 0,
  };

  return {
    metrics,
    incrementFiles: () => metrics.filesProcessed++,
    incrementDirectories: () => metrics.directoriesTraversed++,
    incrementIgnored: () => metrics.filesIgnored++,
    incrementSymlinks: () => metrics.symlinksEncountered++,
    incrementSubmodules: () => metrics.submodulesEncountered++,
    finish: () => {
      metrics.executionTimeMs = performance.now() - startTime;

      // Add memory usage if available
      if (typeof process !== "undefined" && process.memoryUsage) {
        const memUsage = process.memoryUsage();
        metrics.peakMemoryMB = Math.round(memUsage.heapUsed / 1024 / 1024);
      }
    },
  };
}

/**
 * Validate status matrix options
 */
export function validateOptions(options: StatusMatrixOptions): void {
  if (!options.fs) {
    throw new Error("fs client is required");
  }

  if (!options.dir) {
    throw new Error("dir path is required");
  }

  if (options.maxDepth !== undefined && options.maxDepth < 0) {
    throw new Error("maxDepth must be non-negative");
  }
}

/**
 * Merge default options with user options
 */
export function mergeOptions(
  userOptions: StatusMatrixOptions,
): StatusMatrixOptions {
  const hasFilepaths = !!(
    userOptions.filepaths && userOptions.filepaths.length
  );
  return {
    ref: "HEAD",
    ignored: false,
    traverseSymlinks: false,
    traverseSubmodules: false,
    detectBinary: false,
    ...userOptions,
    filepaths: hasFilepaths ? userOptions.filepaths : undefined,
    gitdir: userOptions.gitdir || `${userOptions.dir}/.git`,
  };
}

/**
 * Format file path for consistent output
 */
export function formatFilePath(filepath: string): string {
  // Normalize path separators and remove leading ./
  return filepath.replace(/^\.\//, "").replace(/\\/g, "/");
}

/**
 * Check if a path matches any of the given patterns
 */
export function matchesPatterns(filepath: string, patterns: string[]): boolean {
  return patterns.some((pattern) => {
    // Simple glob-like matching
    if (pattern.includes("*")) {
      const regex = new RegExp(pattern.replace(/\*/g, ".*"));
      return regex.test(filepath);
    }
    return filepath.includes(pattern);
  });
}

/**
 * Convert status matrix to human-readable format
 */
export function formatStatusMatrix(
  matrix: Array<[string, number, number, number]>,
): string[] {
  return matrix.map(([filepath, head, workdir, stage]) => {
    let status = "";

    // Following git status --short format
    if (head === 0 && workdir === 2 && stage === 0)
      status = "??"; // untracked
    else if (head === 0 && workdir === 2 && stage === 2)
      status = "A "; // added
    else if (head === 0 && workdir === 2 && stage === 3)
      status = "AM"; // added, modified
    else if (head === 1 && workdir === 0 && stage === 0)
      status = "D "; // deleted
    else if (head === 1 && workdir === 0 && stage === 1)
      status = " D"; // deleted, unstaged
    else if (head === 1 && workdir === 1 && stage === 1)
      status = "  "; // unmodified
    else if (head === 1 && workdir === 2 && stage === 1)
      status = " M"; // modified, unstaged
    else if (head === 1 && workdir === 2 && stage === 2)
      status = "M "; // modified, staged
    else if (head === 1 && workdir === 2 && stage === 3)
      status = "MM"; // modified, staged, with unstaged changes
    else status = `${head}${workdir}${stage}`; // fallback to numeric

    return `${status} ${filepath}`;
  });
}

/**
 * Deep clone an object (for options merging)
 */
export function deepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== "object") return obj;
  if (obj instanceof Date) return new Date(obj.getTime()) as unknown as T;
  if (Array.isArray(obj)) return obj.map(deepClone) as unknown as T;

  const cloned = {} as T;
  for (const key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      cloned[key] = deepClone(obj[key]);
    }
  }
  return cloned;
}
