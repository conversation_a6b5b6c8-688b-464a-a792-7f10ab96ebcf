/**
 * Tests for GitStateManager
 */

// No need to import jest globals - they're available globally
import * as fs from "fs/promises";
import * as path from "path";
import * as os from "os";
import { GitStateManager } from "../iso-git/workspace-manager";
import * as git from "isomorphic-git";

describe("GitStateManager", () => {
  let manager: GitStateManager;
  let tempDir: string;
  let realRepoDir: string;
  let secondRepoDir: string;
  let emptyDir: string;

  beforeEach(async () => {
    manager = new GitStateManager();

    // Create temporary directories for testing
    tempDir = await fs.mkdtemp(
      path.join(os.tmpdir(), "git-state-manager-test-"),
    );
    realRepoDir = path.join(tempDir, "real-repo");
    secondRepoDir = path.join(tempDir, "second-repo");
    emptyDir = path.join(tempDir, "empty-dir");

    await fs.mkdir(realRepoDir, { recursive: true });
    await fs.mkdir(secondRepoDir, { recursive: true });
    await fs.mkdir(emptyDir, { recursive: true });

    // Initialize a real git repo
    await git.init({ fs, dir: realRepoDir, defaultBranch: "main" });

    // Create initial commit
    await fs.writeFile(path.join(realRepoDir, "README.md"), "# Test Repo");
    await git.add({ fs, dir: realRepoDir, filepath: "README.md" });
    await git.commit({
      fs,
      dir: realRepoDir,
      message: "Initial commit",
      author: { name: "Test", email: "<EMAIL>" },
    });

    // Initialize a second git repo for testing multiple repositories
    await git.init({ fs, dir: secondRepoDir, defaultBranch: "main" });
    await fs.writeFile(
      path.join(secondRepoDir, "README.md"),
      "# Second Test Repo",
    );
    await git.add({ fs, dir: secondRepoDir, filepath: "README.md" });
    await git.commit({
      fs,
      dir: secondRepoDir,
      message: "Initial commit",
      author: { name: "Test", email: "<EMAIL>" },
    });
  });

  afterEach(async () => {
    manager.clear();
    // Clean up temp directory
    await fs.rm(tempDir, { recursive: true, force: true });
  });

  describe("addWorkspace", () => {
    it("should add workspace with existing git repo", async () => {
      const workspace = await manager.addWorkspace(realRepoDir);

      expect(workspace.root).toBe(realRepoDir);
      expect(workspace.realRepo).toBeDefined();
    });

    it("should initialize a new git repo when none exists", async () => {
      const workspace = await manager.addWorkspace(emptyDir);

      // Workspace root should be the requested directory
      expect(workspace.root).toBe(emptyDir);

      // .git should exist and HEAD should resolve
      const headOid = await git.resolveRef({ fs, dir: emptyDir, ref: "HEAD" });
      expect(typeof headOid).toBe("string");

      // README.md should have been created and committed
      const readmeContent = await fs.readFile(
        path.join(emptyDir, "README.md"),
        "utf8",
      );
      expect(readmeContent).toContain("Initialized by Augment");

      // .gitignore should have been created and committed
      const gitignoreContent = await fs.readFile(
        path.join(emptyDir, ".gitignore"),
        "utf8",
      );
      expect(gitignoreContent).toContain(".git/");
    });

    it("should auto-add existing files smaller than 2MB during initialization", async () => {
      // Create some test files in the empty directory
      await fs.writeFile(
        path.join(emptyDir, "small-file.txt"),
        "Small content",
      );
      await fs.writeFile(
        path.join(emptyDir, "another-file.js"),
        "console.log('hello');",
      );

      // Create a large file (>2MB)
      const largeContent = "x".repeat(3 * 1024 * 1024); // 3MB
      await fs.writeFile(path.join(emptyDir, "large-file.txt"), largeContent);

      // Create a subdirectory with a file
      await fs.mkdir(path.join(emptyDir, "subdir"));
      await fs.writeFile(
        path.join(emptyDir, "subdir", "nested-file.txt"),
        "Nested content",
      );

      const workspace = await manager.addWorkspace(emptyDir);

      // Check that git repo was initialized
      expect(workspace.root).toBe(emptyDir);

      // Check git status to see what was committed
      const statusMatrix = await git.statusMatrix({ fs, dir: emptyDir });
      const committedFiles = statusMatrix
        .filter(
          ([, head, workdir, stage]) =>
            head === 1 && workdir === 1 && stage === 1,
        )
        .map(([filepath]) => filepath);

      // Should include README.md, .gitignore, and small files
      expect(committedFiles).toContain("README.md");
      expect(committedFiles).toContain(".gitignore");
      expect(committedFiles).toContain("small-file.txt");
      expect(committedFiles).toContain("another-file.js");
      expect(committedFiles).toContain("subdir/nested-file.txt");

      // Should NOT include the large file
      expect(committedFiles).not.toContain("large-file.txt");

      // Verify commit message mentions the added files
      const commits = await git.log({ fs, dir: emptyDir, depth: 1 });
      expect(commits[0].commit.message).toContain("existing files");
      expect(commits[0].commit.message).toContain("files added");
    });

    it("should not traverse or add files from .git directory", async () => {
      const emptyDir = path.join(tempDir, "test-git-ignore");
      await fs.mkdir(emptyDir, { recursive: true });

      // Create a file inside .git directory (this should never be added)
      const gitDir = path.join(emptyDir, ".git");
      await fs.mkdir(gitDir, { recursive: true });
      await fs.writeFile(path.join(gitDir, "config"), "test config content");
      await fs.writeFile(path.join(gitDir, "HEAD"), "ref: refs/heads/main");

      // Create a normal file that should be added
      await fs.writeFile(
        path.join(emptyDir, "normal-file.txt"),
        "normal content",
      );

      const manager = new GitStateManager();
      await manager.addWorkspace(emptyDir);

      // Check git status to see what was actually committed
      const status = await git.statusMatrix({ fs, dir: emptyDir });
      const committedFiles = status
        .filter(
          ([, head, workdir, stage]) =>
            head === 1 && workdir === 1 && stage === 1,
        )
        .map(([filepath]) => filepath);

      // Should include normal file, README.md, and .gitignore
      expect(committedFiles).toContain("normal-file.txt");
      expect(committedFiles).toContain("README.md");
      expect(committedFiles).toContain(".gitignore");

      // Should NOT include any files from .git directory
      expect(committedFiles).not.toContain(".git/config");
      expect(committedFiles).not.toContain(".git/HEAD");
      expect(committedFiles.some((f) => f.startsWith(".git/"))).toBe(false);
    });

    it("should return existing workspace if already tracked", async () => {
      const workspace1 = await manager.addWorkspace(realRepoDir);
      const workspace2 = await manager.addWorkspace(realRepoDir);

      expect(workspace1).toBe(workspace2);
    });
  });

  describe("tryAddWorkspace", () => {
    it("should add workspace with existing git repo", async () => {
      const workspace = await manager.tryAddWorkspace(realRepoDir);

      expect(workspace).toBeDefined();
      expect(workspace!.root).toBe(realRepoDir);
      expect(workspace!.realRepo).toBeDefined();
    });

    it("should return null for workspace without git repo", async () => {
      const workspace = await manager.tryAddWorkspace(emptyDir);
      expect(workspace).toBeNull();
    });
  });

  describe("getWorkspace", () => {
    it("should find workspace by exact root path", async () => {
      await manager.addWorkspace(realRepoDir);

      const workspace = manager.getWorkspace(realRepoDir);
      expect(workspace).toBeDefined();
      expect(workspace!.root).toBe(realRepoDir);
    });

    it("should find workspace by nested path", async () => {
      await manager.addWorkspace(realRepoDir);

      const nestedPath = path.join(realRepoDir, "src", "file.ts");
      const workspace = manager.getWorkspace(nestedPath);
      expect(workspace).toBeDefined();
      expect(workspace!.root).toBe(realRepoDir);
    });

    it("should return undefined for untracked path", () => {
      const workspace = manager.getWorkspace("/nonexistent/path");
      expect(workspace).toBeUndefined();
    });
  });

  describe("getWorkingConfig", () => {
    it("should return real repo config when git repo exists", async () => {
      await manager.addWorkspace(realRepoDir);

      const config = await manager.getWorkingConfig(realRepoDir);
      expect(config).toBeDefined();
      expect(config!.gitdir).toBe(path.join(realRepoDir, ".git"));
    });

    it("should return undefined for non-git workspace", async () => {
      const config = await manager.getWorkingConfig(emptyDir);
      expect(config).toBeUndefined();
    });
  });

  describe("workspace management", () => {
    it("should list tracked workspaces", async () => {
      await manager.addWorkspace(realRepoDir);
      await manager.addWorkspace(secondRepoDir);

      const workspaces = manager.getTrackedWorkspaces();
      expect(workspaces).toHaveLength(2);
      expect(workspaces).toContain(realRepoDir);
      expect(workspaces).toContain(secondRepoDir);
    });

    it("should remove workspace from tracking", async () => {
      await manager.addWorkspace(realRepoDir);
      await manager.addWorkspace(secondRepoDir);

      manager.removeWorkspace(realRepoDir);

      const workspaces = manager.getTrackedWorkspaces();
      expect(workspaces).toHaveLength(1);
      expect(workspaces[0]).toBe(secondRepoDir);
    });

    it("should clear all workspaces", async () => {
      await manager.addWorkspace(realRepoDir);
      await manager.addWorkspace(secondRepoDir);

      manager.clear();

      const workspaces = manager.getTrackedWorkspaces();
      expect(workspaces).toHaveLength(0);
    });
  });
});
