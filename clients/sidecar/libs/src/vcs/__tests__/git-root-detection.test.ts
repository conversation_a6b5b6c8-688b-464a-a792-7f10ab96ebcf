/**
 * Test git root detection to ensure only actual git repositories are tracked
 */

import * as fs from "fs/promises";
import * as path from "path";
import * as os from "os";
import { findGitDir, findUniqueGitRoots, isGitRepository } from "../git-utils";

describe("Git Root Detection", () => {
  let tempDir: string;
  let gitRepoDir: string;
  let nonGitDir: string;
  let nestedDir: string;

  beforeAll(async () => {
    // Create temporary directory structure for testing
    tempDir = await fs.mkdtemp(path.join(os.tmpdir(), "git-root-test-"));

    // Create a git repository
    gitRepoDir = path.join(tempDir, "git-repo");
    await fs.mkdir(gitRepoDir, { recursive: true });
    await fs.mkdir(path.join(gitRepoDir, ".git"), { recursive: true });

    // Create a non-git directory
    nonGitDir = path.join(tempDir, "non-git");
    await fs.mkdir(nonGitDir, { recursive: true });

    // Create a nested directory inside the git repo
    nestedDir = path.join(gitRepoDir, "nested", "deep");
    await fs.mkdir(nestedDir, { recursive: true });
  });

  afterAll(async () => {
    // Clean up temporary directory
    await fs.rm(tempDir, { recursive: true, force: true });
  });

  describe("findGitDir", () => {
    it("should find git root for git repository", async () => {
      const result = await findGitDir(gitRepoDir);
      expect(result).toBe(gitRepoDir);
    });

    it("should find git root for nested directory in git repository", async () => {
      const result = await findGitDir(nestedDir);
      expect(result).toBe(gitRepoDir);
    });

    it("should return undefined for non-git directory", async () => {
      const result = await findGitDir(nonGitDir);
      expect(result).toBeUndefined();
    });

    it("should return undefined for non-existent directory", async () => {
      const result = await findGitDir(path.join(tempDir, "does-not-exist"));
      expect(result).toBeUndefined();
    });
  });

  describe("isGitRepository", () => {
    it("should return true for git repository", async () => {
      // Create a minimal HEAD file to make it a valid git repo
      await fs.writeFile(
        path.join(gitRepoDir, ".git", "HEAD"),
        "ref: refs/heads/main\n",
      );
      await fs.mkdir(path.join(gitRepoDir, ".git", "refs", "heads"), {
        recursive: true,
      });
      await fs.writeFile(
        path.join(gitRepoDir, ".git", "refs", "heads", "main"),
        "0000000000000000000000000000000000000000\n",
      );

      const result = await isGitRepository(gitRepoDir);
      expect(result).toBe(true);
    });

    it("should return false for non-git directory", async () => {
      const result = await isGitRepository(nonGitDir);
      expect(result).toBe(false);
    });

    it("should return false for non-existent directory", async () => {
      const result = await isGitRepository(
        path.join(tempDir, "does-not-exist"),
      );
      expect(result).toBe(false);
    });
  });

  describe("findUniqueGitRoots", () => {
    it("should return only git repository roots", async () => {
      const workspacePaths = [gitRepoDir, nonGitDir, nestedDir];
      const result = await findUniqueGitRoots(workspacePaths);

      // Should only return the git repository root, not the non-git directory
      expect(result).toEqual([gitRepoDir]);
    });

    it("should deduplicate git roots", async () => {
      const workspacePaths = [gitRepoDir, nestedDir, gitRepoDir];
      const result = await findUniqueGitRoots(workspacePaths);

      // Should return only one instance of the git root
      expect(result).toEqual([gitRepoDir]);
    });

    it("should return empty array for no git repositories", async () => {
      const workspacePaths = [nonGitDir, path.join(tempDir, "another-non-git")];
      const result = await findUniqueGitRoots(workspacePaths);

      expect(result).toEqual([]);
    });

    it("should handle empty input", async () => {
      const result = await findUniqueGitRoots([]);
      expect(result).toEqual([]);
    });

    it("should handle non-existent paths gracefully", async () => {
      const workspacePaths = [
        gitRepoDir,
        path.join(tempDir, "does-not-exist"),
        nonGitDir,
      ];
      const result = await findUniqueGitRoots(workspacePaths);

      // Should only return the valid git repository
      expect(result).toEqual([gitRepoDir]);
    });
  });
});
