/**
 * Tests for git utilities
 */

import * as fs from "fs/promises";
import * as path from "path";
import * as os from "os";
import { findGitDir, isGitRepository, findUniqueGitRoots } from "../git-utils";
import * as git from "isomorphic-git";

describe("Git Utils", () => {
  let tempDir: string;

  beforeEach(async () => {
    tempDir = await fs.mkdtemp(path.join(os.tmpdir(), "git-utils-test-"));
  });

  afterEach(async () => {
    await fs.rm(tempDir, { recursive: true, force: true });
  });

  describe("findGitDir", () => {
    it("should return undefined for non-git directory", async () => {
      const result = await findGitDir(tempDir);
      expect(result).toBeUndefined();
    });

    it("should find git directory", async () => {
      // Initialize git repo
      await git.init({ fs, dir: tempDir, defaultBranch: "main" });

      // Create initial commit to make it a valid git repository
      await fs.writeFile(path.join(tempDir, "test.txt"), "test");
      await git.add({ fs, dir: tempDir, filepath: "test.txt" });
      await git.commit({
        fs,
        dir: tempDir,
        message: "Initial commit",
        author: { name: "Test", email: "<EMAIL>" },
      });

      const result = await findGitDir(tempDir);
      expect(result).toBe(tempDir);
    });
  });

  describe("isGitRepository", () => {
    it("should return false for non-git directory", async () => {
      const result = await isGitRepository(tempDir);
      expect(result).toBe(false);
    });

    it("should return true for git directory", async () => {
      await git.init({ fs, dir: tempDir, defaultBranch: "main" });

      // Create initial commit to establish HEAD
      await fs.writeFile(path.join(tempDir, "test.txt"), "test");
      await git.add({ fs, dir: tempDir, filepath: "test.txt" });
      await git.commit({
        fs,
        dir: tempDir,
        message: "Initial commit",
        author: { name: "Test", email: "<EMAIL>" },
      });

      const result = await isGitRepository(tempDir);
      expect(result).toBe(true);
    });
  });

  describe("findUniqueGitRoots", () => {
    it("should return empty array for non-git directories", async () => {
      const nonGitDir = path.join(tempDir, "non-git");
      await fs.mkdir(nonGitDir);

      const result = await findUniqueGitRoots([nonGitDir]);
      expect(result).toEqual([]);
    });

    it("should find git root for git directory", async () => {
      await git.init({ fs, dir: tempDir, defaultBranch: "main" });

      // Create initial commit to make it a valid git repository
      await fs.writeFile(path.join(tempDir, "test.txt"), "test");
      await git.add({ fs, dir: tempDir, filepath: "test.txt" });
      await git.commit({
        fs,
        dir: tempDir,
        message: "Initial commit",
        author: { name: "Test", email: "<EMAIL>" },
      });

      const result = await findUniqueGitRoots([tempDir]);
      expect(result).toEqual([tempDir]);
    });

    it("should find git root for subdirectory of git repository", async () => {
      await git.init({ fs, dir: tempDir, defaultBranch: "main" });

      // Create initial commit to make it a valid git repository
      await fs.writeFile(path.join(tempDir, "test.txt"), "test");
      await git.add({ fs, dir: tempDir, filepath: "test.txt" });
      await git.commit({
        fs,
        dir: tempDir,
        message: "Initial commit",
        author: { name: "Test", email: "<EMAIL>" },
      });

      const subDir = path.join(tempDir, "subdir");
      await fs.mkdir(subDir);

      const result = await findUniqueGitRoots([subDir]);
      expect(result).toEqual([tempDir]);
    });

    it("should deduplicate git roots from multiple paths in same repository", async () => {
      await git.init({ fs, dir: tempDir, defaultBranch: "main" });

      // Create initial commit to make it a valid git repository
      await fs.writeFile(path.join(tempDir, "test.txt"), "test");
      await git.add({ fs, dir: tempDir, filepath: "test.txt" });
      await git.commit({
        fs,
        dir: tempDir,
        message: "Initial commit",
        author: { name: "Test", email: "<EMAIL>" },
      });

      const subDir1 = path.join(tempDir, "subdir1");
      const subDir2 = path.join(tempDir, "subdir2");
      await fs.mkdir(subDir1);
      await fs.mkdir(subDir2);

      const result = await findUniqueGitRoots([tempDir, subDir1, subDir2]);
      expect(result).toEqual([tempDir]);
    });

    it("should find multiple unique git roots", async () => {
      // Create first git repo
      const gitRepo1 = path.join(tempDir, "repo1");
      await fs.mkdir(gitRepo1);
      await git.init({ fs, dir: gitRepo1, defaultBranch: "main" });

      // Create initial commit for first repo
      await fs.writeFile(path.join(gitRepo1, "test1.txt"), "test1");
      await git.add({ fs, dir: gitRepo1, filepath: "test1.txt" });
      await git.commit({
        fs,
        dir: gitRepo1,
        message: "Initial commit",
        author: { name: "Test", email: "<EMAIL>" },
      });

      // Create second git repo
      const gitRepo2 = path.join(tempDir, "repo2");
      await fs.mkdir(gitRepo2);
      await git.init({ fs, dir: gitRepo2, defaultBranch: "main" });

      // Create initial commit for second repo
      await fs.writeFile(path.join(gitRepo2, "test2.txt"), "test2");
      await git.add({ fs, dir: gitRepo2, filepath: "test2.txt" });
      await git.commit({
        fs,
        dir: gitRepo2,
        message: "Initial commit",
        author: { name: "Test", email: "<EMAIL>" },
      });

      const result = await findUniqueGitRoots([gitRepo1, gitRepo2]);
      expect(result.sort()).toEqual([gitRepo1, gitRepo2].sort());
    });

    it("should handle mix of git and non-git directories", async () => {
      // Create git repo
      const gitRepo = path.join(tempDir, "git-repo");
      await fs.mkdir(gitRepo);
      await git.init({ fs, dir: gitRepo, defaultBranch: "main" });

      // Create initial commit to make it a valid git repository
      await fs.writeFile(path.join(gitRepo, "README.md"), "# Test");
      await git.add({ fs, dir: gitRepo, filepath: "README.md" });
      await git.commit({
        fs,
        dir: gitRepo,
        message: "Initial commit",
        author: { name: "Test", email: "<EMAIL>" },
      });

      // Create non-git directory
      const nonGitDir = path.join(tempDir, "non-git");
      await fs.mkdir(nonGitDir);

      const result = await findUniqueGitRoots([gitRepo, nonGitDir]);
      expect(result).toEqual([gitRepo]);
    });
  });
});
