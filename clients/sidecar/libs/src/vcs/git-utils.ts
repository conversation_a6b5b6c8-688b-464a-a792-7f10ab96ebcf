/**
 * Git detection and workspace utilities using isomorphic-git types directly
 */

import * as fs from "fs/promises";
import * as git from "isomorphic-git";

/**
 * Find git repository using isomorphic-git's built-in detection
 */
export async function findGitDir(
  workspacePath: string,
): Promise<string | undefined> {
  try {
    return await git.findRoot({ fs, filepath: workspacePath });
  } catch {
    return undefined;
  }
}

/**
 * Check if a directory is a git repository
 */
export async function isGitRepository(dirPath: string): Promise<boolean> {
  try {
    await git.resolveRef({ fs, dir: dirPath, ref: "HEAD" });
    return true;
  } catch {
    return false;
  }
}

/**
 * Find unique git repository roots from a list of workspace folder paths
 * Crawls up the directory tree to find the nearest ancestor git root for each path
 * Returns deduplicated list of git repository roots
 * ONLY returns actual git repository roots - never returns non-git directories
 */
export async function findUniqueGitRoots(
  workspacePaths: string[],
): Promise<string[]> {
  const gitRoots = new Set<string>();

  for (const workspacePath of workspacePaths) {
    try {
      // Use isomorphic-git's findGitDir to crawl up directory tree to find git root
      const gitRoot = await findGitDir(workspacePath);
      if (gitRoot) {
        // Double-check that this is actually a git repository
        const isValidGitRepo = await isGitRepository(gitRoot);
        if (isValidGitRepo) {
          gitRoots.add(gitRoot);
        }
      }
    } catch (error) {
      // Skip paths that don't have git repositories or have access issues
      continue;
    }
  }

  return Array.from(gitRoots);
}
