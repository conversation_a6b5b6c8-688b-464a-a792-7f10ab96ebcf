import { ChangeType } from "../../chat/chat-types";

export type VCSChange = {
  commits: Commit[];
  workingDirectory: WorkingDirectoryChange[];
};

export type Commit = {
  hash: string;
  files: CommitChange[];
};
export type CommitChange = {
  afterPath?: string;
  beforePath?: string;
  changeType: ChangeType;
  beforeBlobName?: string; // empty if file deleted
  afterBlobName?: string; // empty if file deleted
};

export type WorkingDirectoryChange = {
  afterPath?: string;
  beforePath?: string;
  changeType: ChangeType;
  headBlobName?: string; // empty if file added
  indexedBlobName?: string; // empty if file deleted or not indexed yet.
  currentBlobName?: string; // empty if file deleted
};
