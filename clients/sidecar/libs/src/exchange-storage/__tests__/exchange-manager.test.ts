/**
 * @file exchange-manager.test.ts
 * Tests for the ExchangeManager class.
 */

import { ExchangeManager } from "../exchange-manager";
import { createTestExchange, TestKvStore } from "./setup";
import {
  setLibraryPluginKvStore,
  resetLibraryPluginKvStore,
} from "../../client-interfaces/plugin-kv-store";

describe("ExchangeManager", () => {
  let exchangeManager: ExchangeManager;
  let mockKvStore: TestKvStore;

  beforeEach(() => {
    // Set up mock KV store
    mockKvStore = new TestKvStore();
    setLibraryPluginKvStore(mockKvStore);

    exchangeManager = new ExchangeManager();
  });

  afterEach(async () => {
    await exchangeManager.close();
    resetLibraryPluginKvStore();
  });

  describe("basic operations", () => {
    it("should save and load exchanges", async () => {
      const exchanges = [
        createTestExchange("uuid1"),
        createTestExchange("uuid2"),
      ];

      // Save exchanges
      await exchangeManager.saveExchanges("test-conv", exchanges);

      // Load all exchanges for conversation
      const loadedExchanges =
        await exchangeManager.loadConversationExchanges("test-conv");
      expect(loadedExchanges).toHaveLength(2);
      expect(loadedExchanges.map((e) => e.uuid)).toContain("uuid1");
      expect(loadedExchanges.map((e) => e.uuid)).toContain("uuid2");
    });

    it("should preserve rich_text_json_repr and mentioned_items fields", async () => {
      const exchange = createTestExchange("uuid1");
      exchange.rich_text_json_repr = {
        type: "doc",
        content: [
          {
            type: "paragraph",
            content: [
              { type: "text", text: "Hello " },
              {
                type: "mention",
                attrs: {
                  id: "task:abc-123:test-task",
                  label: "Test Task",
                  name: "test-task",
                  data: {
                    taskUuid: "abc-123",
                    taskTree: { id: "abc-123", name: "Test Task" },
                    surroundingContext: "Test context",
                  },
                },
              },
            ],
          },
        ],
      };
      exchange.mentioned_items = [
        {
          id: "task:abc-123:test-task",
          label: "Test Task",
          name: "test-task",
          data: {
            taskUuid: "abc-123",
            taskTree: { id: "abc-123", name: "Test Task" },
            surroundingContext: "Test context",
          },
        },
      ];

      // Save exchange
      await exchangeManager.saveExchanges("test-conv", [exchange]);

      // Load exchange back
      const loadedExchanges = await exchangeManager.loadExchangesByUuids(
        "test-conv",
        ["uuid1"],
      );
      expect(loadedExchanges).toHaveLength(1);

      const loadedExchange = loadedExchanges[0];
      expect(loadedExchange.rich_text_json_repr).toEqual(
        exchange.rich_text_json_repr,
      );
      expect(loadedExchange.mentioned_items).toEqual(exchange.mentioned_items);
    });

    it("should support upsert semantics - update existing exchanges", async () => {
      const originalExchange = createTestExchange("uuid1");
      originalExchange.request_message = "Original message";

      // Save original exchange
      await exchangeManager.saveExchanges("test-conv", [originalExchange]);

      // Verify count is 1
      const count1 = await exchangeManager.countExchanges("test-conv");
      expect(count1).toBe(1);

      // Update the same exchange
      const updatedExchange = { ...originalExchange };
      updatedExchange.request_message = "Updated message";

      // Save updated exchange (should upsert, not create new)
      await exchangeManager.saveExchanges("test-conv", [updatedExchange]);

      // Verify count is still 1 (not 2)
      const count2 = await exchangeManager.countExchanges("test-conv");
      expect(count2).toBe(1);

      // Verify the exchange was updated
      const loadedExchanges =
        await exchangeManager.loadConversationExchanges("test-conv");
      expect(loadedExchanges).toHaveLength(1);
      expect(loadedExchanges[0].request_message).toBe("Updated message");
    });

    it("should load specific exchanges by UUIDs using batch iterator", async () => {
      const exchanges = [
        createTestExchange("uuid1"),
        createTestExchange("uuid2"),
        createTestExchange("uuid3"),
      ];
      await exchangeManager.saveExchanges("test-conv", exchanges);

      // Load specific exchanges by UUIDs (should use batch iterator)
      const result = await exchangeManager.loadExchangesByUuids("test-conv", [
        "uuid1",
        "uuid3",
      ]);

      expect(result).toHaveLength(2);
      expect(result.map((e) => e.uuid)).toContain("uuid1");
      expect(result.map((e) => e.uuid)).toContain("uuid3");
      expect(result.map((e) => e.uuid)).not.toContain("uuid2");
    });

    it("should handle batch loading with non-existent UUIDs", async () => {
      const exchanges = [
        createTestExchange("uuid1"),
        createTestExchange("uuid2"),
      ];
      await exchangeManager.saveExchanges("test-conv", exchanges);

      // Try to load mix of existing and non-existing UUIDs
      const result = await exchangeManager.loadExchangesByUuids("test-conv", [
        "uuid1",
        "non-existent",
        "uuid2",
        "also-missing",
      ]);

      // Should only return the existing exchanges
      expect(result).toHaveLength(2);
      expect(result.map((e) => e.uuid)).toContain("uuid1");
      expect(result.map((e) => e.uuid)).toContain("uuid2");
    });

    it("should correctly detect existing exchanges in batch upsert", async () => {
      // Save initial exchanges
      const initialExchanges = [
        createTestExchange("uuid1"),
        createTestExchange("uuid2"),
      ];
      await exchangeManager.saveExchanges("test-conv", initialExchanges);

      // Verify count is 2
      const count1 = await exchangeManager.countExchanges("test-conv");
      expect(count1).toBe(2);

      // Now try to upsert a mix of existing and new exchanges
      const mixedExchanges = [
        createTestExchange("uuid1"), // existing
        createTestExchange("uuid3"), // new
        createTestExchange("uuid2"), // existing
        createTestExchange("uuid4"), // new
      ];

      await exchangeManager.saveExchanges("test-conv", mixedExchanges);

      // Count should be 4 (2 existing + 2 new)
      const count2 = await exchangeManager.countExchanges("test-conv");
      expect(count2).toBe(4);

      // Verify all exchanges are present
      const allExchanges =
        await exchangeManager.loadConversationExchanges("test-conv");
      expect(allExchanges).toHaveLength(4);
      expect(allExchanges.map((e) => e.uuid)).toContain("uuid1");
      expect(allExchanges.map((e) => e.uuid)).toContain("uuid2");
      expect(allExchanges.map((e) => e.uuid)).toContain("uuid3");
      expect(allExchanges.map((e) => e.uuid)).toContain("uuid4");
    });

    it("should use batch iterator for loading exchanges efficiently", async () => {
      // Create many exchanges to test batch efficiency
      const manyExchanges = Array.from({ length: 10 }, (_, i) =>
        createTestExchange(`uuid-${i}`),
      );
      await exchangeManager.saveExchanges("test-conv", manyExchanges);

      // Load a subset using batch iterator
      const targetUuids = ["uuid-1", "uuid-3", "uuid-7", "uuid-9"];
      const loadedExchanges = await exchangeManager.loadExchangesByUuids(
        "test-conv",
        targetUuids,
      );

      // Should return exactly the requested exchanges
      expect(loadedExchanges).toHaveLength(4);
      expect(loadedExchanges.map((e) => e.uuid).sort()).toEqual(
        targetUuids.sort(),
      );
    });

    it("should use keys iterator for efficient existence checking", async () => {
      // Save some exchanges
      const exchanges = [
        createTestExchange("uuid1"),
        createTestExchange("uuid2"),
        createTestExchange("uuid3"),
      ];
      await exchangeManager.saveExchanges("test-conv", exchanges);

      // Try to upsert mix of existing and new (should use keys iterator internally)
      const mixedExchanges = [
        createTestExchange("uuid1"), // existing
        createTestExchange("uuid4"), // new
        createTestExchange("uuid2"), // existing
      ];

      await exchangeManager.saveExchanges("test-conv", mixedExchanges);

      // Should have 4 total exchanges (3 original + 1 new)
      const count = await exchangeManager.countExchanges("test-conv");
      expect(count).toBe(4);

      // Verify all exchanges are present
      const allExchanges =
        await exchangeManager.loadConversationExchanges("test-conv");
      expect(allExchanges).toHaveLength(4);
      expect(allExchanges.map((e) => e.uuid).sort()).toEqual([
        "uuid1",
        "uuid2",
        "uuid3",
        "uuid4",
      ]);
    });

    it("should delete exchanges", async () => {
      const exchanges = [
        createTestExchange("uuid1"),
        createTestExchange("uuid2"),
      ];
      await exchangeManager.saveExchanges("test-conv", exchanges);

      await exchangeManager.deleteExchanges("test-conv", ["uuid1"]);

      const remaining =
        await exchangeManager.loadConversationExchanges("test-conv");
      expect(remaining).toHaveLength(1);
      expect(remaining[0].uuid).toBe("uuid2");
    });

    it("should count exchanges", async () => {
      const exchanges = [
        createTestExchange("uuid1"),
        createTestExchange("uuid2"),
      ];
      await exchangeManager.saveExchanges("test-conv", exchanges);

      const count = await exchangeManager.countExchanges("test-conv");
      expect(count).toBe(2);
    });

    it("should delete all exchanges for a conversation", async () => {
      // Setup: Create exchanges in multiple conversations
      const conv1Exchanges = [
        createTestExchange("uuid1"),
        createTestExchange("uuid2"),
      ];
      const conv2Exchanges = [
        createTestExchange("uuid3"),
        createTestExchange("uuid4"),
      ];

      await exchangeManager.saveExchanges("conv1", conv1Exchanges);
      await exchangeManager.saveExchanges("conv2", conv2Exchanges);

      // Verify both conversations have exchanges
      expect(await exchangeManager.countExchanges("conv1")).toBe(2);
      expect(await exchangeManager.countExchanges("conv2")).toBe(2);

      // Delete all exchanges for conv1
      await exchangeManager.deleteConversationExchanges("conv1");

      // Verify conv1 has no exchanges and conv2 is unaffected
      expect(await exchangeManager.countExchanges("conv1")).toBe(0);
      expect(await exchangeManager.countExchanges("conv2")).toBe(2);

      // Verify conv1 exchanges are actually deleted
      const conv1Remaining =
        await exchangeManager.loadConversationExchanges("conv1");
      expect(conv1Remaining).toHaveLength(0);

      // Verify conv2 exchanges are still there
      const conv2Remaining =
        await exchangeManager.loadConversationExchanges("conv2");
      expect(conv2Remaining).toHaveLength(2);
    });

    it("should handle deleting exchanges for non-existent conversation", async () => {
      // Should not throw an error
      await expect(
        exchangeManager.deleteConversationExchanges("non-existent"),
      ).resolves.toBeUndefined();
    });
  });
});
