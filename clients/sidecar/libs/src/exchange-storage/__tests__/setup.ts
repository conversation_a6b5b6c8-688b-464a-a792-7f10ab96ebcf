/**
 * @file setup.ts
 * Test setup and shared utilities for exchange storage tests.
 */

import type { StoredExchange } from "../storage-types";
import type {
  IPluginKvStore,
  KvBatchOperation,
  KvIteratorOptions,
} from "../../client-interfaces/plugin-kv-store";

// Mock the logger to avoid console output during tests
jest.mock("../../logging", () => ({
  getLogger: jest.fn(() => ({
    debug: jest.fn(),
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
  })),
}));

// Global test timeout
jest.setTimeout(10000);

/**
 * Simple in-memory KV store for testing.
 * This is a shared utility to avoid duplication across test files.
 */
export class TestKvStore implements IPluginKvStore {
  private readonly _store = new Map<string, string>();

  async get(key: string): Promise<string | undefined> {
    return Promise.resolve(this._store.get(key));
  }

  async put(key: string, value: string): Promise<void> {
    this._store.set(key, value);
    return Promise.resolve();
  }

  async batch(operations: Array<KvBatchOperation>): Promise<void> {
    for (const operation of operations) {
      if (operation.type === "put" && operation.value !== undefined) {
        this._store.set(operation.key, operation.value);
      } else if (operation.type === "del") {
        this._store.delete(operation.key);
      }
    }
    return Promise.resolve();
  }

  async close(): Promise<void> {
    // No-op for in-memory store
    return Promise.resolve();
  }

  async *iterator(
    options?: KvIteratorOptions,
  ): AsyncIterable<[string, string]> {
    const entries = Array.from(this._store.entries());

    // Apply filtering based on options
    let filteredEntries = entries;

    if (options?.gte) {
      filteredEntries = filteredEntries.filter(([key]) => key >= options.gte!);
    }
    if (options?.gt) {
      filteredEntries = filteredEntries.filter(([key]) => key > options.gt!);
    }
    if (options?.lte) {
      filteredEntries = filteredEntries.filter(([key]) => key <= options.lte!);
    }
    if (options?.lt) {
      filteredEntries = filteredEntries.filter(([key]) => key < options.lt!);
    }

    // Sort entries by key
    filteredEntries.sort(([a], [b]) => a.localeCompare(b));

    // Apply reverse if needed
    if (options?.reverse) {
      filteredEntries.reverse();
    }

    // Apply limit if specified
    if (options?.limit && options.limit > 0) {
      filteredEntries = filteredEntries.slice(0, options.limit);
    }

    // Yield entries
    for (const entry of filteredEntries) {
      yield Promise.resolve(entry);
    }
  }

  async *keys(options?: KvIteratorOptions): AsyncIterable<string> {
    for await (const [key] of this.iterator(options)) {
      yield key;
    }
  }

  /**
   * Test helper method to clear all data
   */
  clear(): void {
    this._store.clear();
  }

  /**
   * Test helper method to get the size of the store
   */
  size(): number {
    return this._store.size;
  }

  /**
   * Test helper method to check if a key exists
   */
  has(key: string): boolean {
    return this._store.has(key);
  }
}

/**
 * Creates a test exchange with the given UUID.
 * This is a shared utility to avoid duplication across test files.
 */
export const createTestExchange = (
  uuid: string,
  conversationId = "test-conv",
): StoredExchange => ({
  uuid,
  conversationId,
  status: "success",
  timestamp: new Date().toISOString(),
  request_message: `Test message ${uuid}`,
  response_text: `Test response ${uuid}`,
  request_id: `req-${uuid}`,
});
