# Exchange Storage

This directory contains the exchange storage implementation for the sidecar, providing efficient key-value storage for chat exchanges with atomic batch operations and conversation metadata indexing.

## Directory Structure

```
exchange-storage/
├── exchange-manager.ts              # Main API entry point and KV store integration
├── exchange-webview-messages.ts     # Webview message handler
├── storage-types.ts                 # Storage type definitions
└── __tests__/                       # Comprehensive test suite
    ├── exchange-manager.test.ts     # ExchangeManager tests
    └── setup.ts                     # Test utilities and TestKvStore
```

## Architecture Overview

The exchange storage system follows a simplified, high-performance architecture using direct key-value storage:

### 1. **Webview Layer** (External Interface)
- Uses `ExchangeWebViewMessageType` for communication
- Works with `StoredExchange` objects
- No knowledge of storage implementation details
- Communicates via message passing through `ExchangeWebviewMessageHandler`

### 2. **Manager Layer** (`ExchangeManager`)
- Main API entry point that coordinates all operations
- Uses pluggable `IPluginKvStore` interface for storage abstraction
- Provides immediate, atomic operations for data consistency
- Handles conversation metadata and exchange counting
- Supports efficient range queries for conversation-based loading

### 3. **Storage Layer** (Pluggable KV Store)
- Uses `IPluginKvStore` interface for storage abstraction
- Actual implementation provided by client (LevelDB for production, in-memory for tests)
- Direct key-value storage with JSON serialization
- Atomic batch operations for consistency
- Efficient prefix-based range queries

## Key Features

### High Performance
- **LevelDB Integration**: Uses LevelDB for production deployments with 250,000+ exchanges/second throughput
- **Efficient Range Queries**: Conversation-based loading using prefix scanning with `gte` and `lt` bounds
- **Atomic Batch Operations**: Ensures data consistency across multiple operations
- **Minimal Memory Footprint**: Direct key-value storage without intermediate caching layers

### Simple Operations
- **Upsert Semantics**: Save operations create new or update existing exchanges automatically
- **Conversation-Based Loading**: Load all exchanges for a conversation efficiently using range queries
- **UUID-Based Lookups**: Load specific exchanges by their UUIDs with batch operations
- **Atomic Deletes**: Remove multiple exchanges in a single atomic operation
- **Exchange Counting**: Fast conversation exchange counting with metadata tracking

### Data Model
- **Exchange Keys**: `exchange:{conversationId}:{exchangeUuid}` → StoredExchange JSON
- **Metadata Keys**: `metadata:{conversationId}` → ConversationMetadata JSON
- **No Reverse Lookups**: Eliminated complex reverse mappings for simplicity
- **JSON Serialization**: Human-readable storage format for debugging and inspection

### Conversation Metadata
- **Minimal Tracking**: Only stores essential counters and timestamps
- **Fast Updates**: Simple increment operations for totalExchanges
- **No UUID Arrays**: Eliminates exchangeUuids array for better performance
- **Automatic Management**: Metadata updated automatically during save/delete operations

## Core Components

### `ExchangeManager` - Main API Entry Point
The central coordinator that provides a clean interface for all exchange operations:

**Key Methods:**
- `loadConversationExchanges(conversationId)` - Load all exchanges for a conversation
- `loadExchangesByUuids(conversationId, uuids)` - Load specific exchanges by UUID
- `saveExchanges(conversationId, exchanges)` - Save exchanges (creates or updates)
- `deleteExchanges(conversationId, uuids)` - Delete exchanges by UUID
- `countExchanges(conversationId)` - Count total exchanges in a conversation
- `close()` - Close the underlying KV store and clean up resources

**Features:**
- Uses pluggable `IPluginKvStore` interface for storage abstraction
- Mirrors KV store semantics with read/save/delete operations
- Automatic upsert behavior (save creates or updates)
- Efficient conversation-based and UUID-based loading
- Atomic batch operations for data consistency

### `IPluginKvStore` - Storage Interface
The pluggable storage interface that abstracts the underlying storage implementation:

**Storage Structure:**
- **Key Format:**
  - Exchanges: `exchange:{conversationId}:{uuid}`
  - Metadata: `metadata:{conversationId}`
- **Value Format:** JSON-serialized exchange data and metadata

**Key Features:**
- Pluggable storage backend (LevelDB for production, in-memory for tests)
- Atomic batch operations for data consistency
- Efficient range queries for conversation-based operations using iterators
- Built-in compression and caching (implementation dependent)
- Cross-platform compatibility


### `ExchangeWebviewMessageHandler` - Message Interface
Handles communication between webviews and the sidecar:

**Supported Message Types:**
- `loadConversationExchangesRequest/Response` - Load all exchanges for a conversation
- `loadExchangesByUuidsRequest/Response` - Load specific exchanges by UUID
- `saveExchangesRequest/Response` - Save exchanges using upsert semantics
- `deleteExchangesRequest/Response` - Delete exchanges by UUID
- `countExchangesRequest/Response` - Count exchanges in a conversation

**Integration:**
- Registered in `webview-messaging.ts` alongside other message handlers
- Follows the same pattern as `TaskWebviewMessageHandler`
- Provides error handling and logging for all operations

## Data Types and Interfaces

### Core Storage Types (`storage-types.ts`)

**`StoredExchange`** - The complete exchange data structure used for persistence:
```typescript
interface StoredExchange extends Exchange {
  uuid: string;                    // Unique identifier
  conversationId: string;          // Parent conversation
  status: "sent" | "success" | "failed";
  timestamp?: string;              // ISO timestamp
  seen_state?: "seen" | "unseen";  // User interaction state
}
```

**Simple Operations** - The system uses basic read/save/delete operations:
- Direct save operations with automatic upsert behavior
- Atomic batch operations for consistency
- Simple error handling with standard exceptions

### Conversation Metadata (`ConversationMetadata`)

**`ConversationMetadata`** - Conversation metadata structure:
```typescript
interface ConversationMetadata {
  conversationId: string;          // Parent conversation
  totalExchanges: number;          // Total exchange count
  lastUpdated: number;             // Unix timestamp
}
```

## Storage Implementation Details

### Key-Value Store Structure
```
kv-store/
├── exchange:{conversationId}:{uuid}  # Individual exchange data
└── metadata:{conversationId}         # Conversation metadata
```

### Data Organization
- **Exchange Keys:** `exchange:{conversationId}:{uuid}` → JSON-serialized `StoredExchange`
- **Metadata Keys:** `metadata:{conversationId}` → JSON-serialized `ConversationMetadata`
- **Atomic Operations:** Batch operations ensure consistency across multiple updates

### Performance Features
- **Pluggable Backend:** LevelDB for production, in-memory for tests
- **Efficient Range Queries:** Prefix-based key structure enables fast conversation queries using iterators
- **Atomic Batch Operations:** Multiple operations committed together for consistency
- **Built-in Optimizations:** Implementation-dependent compression and caching

### Performance Optimizations
1. **Key-Value Efficiency:** Direct key-based access without file system overhead
2. **Batch Operations:** Atomic batch writes for multiple operations
3. **Immediate Processing:** Process updates immediately for consistency
4. **Operation Merging:** Combine multiple updates to the same exchange in batch operations
5. **Efficient Queries:** Range queries using key prefixes for conversation-based operations
6. **Iterator-based Loading:** Efficient scanning using KV store iterators
7. **Pluggable Backends:** Optimized implementations for different environments

## API Usage Examples

### Loading All Exchanges for a Conversation
```typescript
const exchangeManager = new ExchangeManager();
const exchanges = await exchangeManager.loadConversationExchanges("conversation-id");
console.log(`Loaded ${exchanges.length} exchanges`);
```

### Loading Specific Exchanges by UUID
```typescript
const exchanges = await exchangeManager.loadExchangesByUuids("conversation-id", [
  "uuid1", "uuid2", "uuid3"
]);
```

### Saving Exchanges (Upsert Semantics)
```typescript
const exchanges = [
  {
    uuid: "new-uuid",
    conversationId: "conversation-id",
    request_message: "Hello",
    response_text: "Hi there!",
    request_id: "req-123",
    status: "success",
    timestamp: new Date().toISOString()
  }
];

await exchangeManager.saveExchanges("conversation-id", exchanges);
```

### Deleting Exchanges
```typescript
await exchangeManager.deleteExchanges("conversation-id", ["uuid1", "uuid2"]);
```

### Counting Exchanges
```typescript
const count = await exchangeManager.countExchanges("conversation-id");
console.log(`Total exchanges: ${count}`);
```

## Integration Points

### Webview Communication
The exchange storage integrates with the webview system through:

1. **Message Types:** Defined in `webview-messages/message-types/exchange-messages.ts`
2. **Handler Registration:** In `webview-messaging.ts` alongside other handlers
3. **Extension Client:** Webviews use `IExtensionClient` methods that map to exchange messages

### Sidecar Integration
- **Initialization:** `ExchangeManager` is instantiated in `webview-messaging.ts`
- **Lifecycle:** Managed alongside other sidecar services
- **Resource Management:** Provides `close()` for cleanup

### Storage Backend
- **Interface:** Uses `IPluginKvStore` for storage abstraction
- **Implementation:** LevelDB for VSCode extension, in-memory for tests
- **Configuration:** Storage path and options configured by the client
- **Format:** JSON-serialized values with UTF-8 encoding

## Testing

The exchange storage includes comprehensive tests covering:

- **Unit Tests:** Individual component testing with mocked KV store
- **Integration Tests:** Cross-component interaction testing with in-memory KV store
- **Error Handling:** Failure scenarios and recovery testing
- **Performance Tests:** Batch operations and atomic transaction validation
- **Test Isolation:** Each test uses a separate in-memory KV store instance for isolation

Test files are located in `__tests__/` and follow Jest conventions with comprehensive coverage of critical paths. The `TestKvStore` class provides an in-memory implementation of `IPluginKvStore` for testing.

## Migration and Versioning

### Current Status
- **KV Store Migration:** Successfully migrated from direct LevelDB usage to pluggable KV store interface
- **Metadata Versioning:** Conversation metadata includes version tracking for future migrations
- **Format Stability:** Current key-value format is stable across different storage backends

### Future Migration Support
The architecture supports future migrations through:
- Conversation metadata version tracking
- Extensible key-value storage format
- Pluggable storage backend interface
- Backward compatibility considerations
- Storage migration framework hooks (when needed)

## Monitoring and Debugging

### Resource Management
```typescript
// Close KV store and cleanup (call on shutdown)
await exchangeManager.close();
```

### Logging
All components use structured logging with the following loggers:
- `ExchangeManager` - High-level operation logging and KV store interactions
- `ExchangeWebviewMessageHandler` - Message handling and errors

### Storage Backend Implementations
- **VSCode Extension:** Uses `LevelKvStore` with LevelDB for persistent storage
- **Sidecar Node Process:** Uses `LevelKvStore` with LevelDB for persistent storage
- **Tests:** Uses `TestKvStore` with in-memory Map for fast, isolated testing
