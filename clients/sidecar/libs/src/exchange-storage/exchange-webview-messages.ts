/**
 * @file exchange-webview-messages.ts
 * This file contains the handler for exchange-related webview messages.
 * Follows the exact pattern from TaskWebviewMessageHandler.
 */

import { ExchangeManager } from "./exchange-manager";
import {
  CommonWebViewMessageType,
  WebViewMessage,
} from "../webview-messages/common-webview-messages";
import {
  ExchangeWebViewMessageType,
  LoadConversationExchangesRequest,
  LoadConversationExchangesResponse,
  LoadExchangesByUuidsRequest,
  LoadExchangesByUuidsResponse,
  SaveExchangesRequest,
  SaveExchangesResponse,
  DeleteExchangesRequest,
  DeleteExchangesResponse,
  DeleteConversationExchangesRequest,
  DeleteConversationExchangesResponse,
  CountExchangesRequest,
  CountExchangesResponse,
} from "../webview-messages/message-types/exchange-messages";
import { IWebviewMessageConsumer } from "../webview-messages/webview-messages-broker";
import { getLogger } from "../logging";

/**
 * Handler for exchange-related webview messages.
 * Implements the IWebviewMessageConsumer interface to handle exchange-related messages.
 * Follows the exact pattern from TaskWebviewMessageHandler.
 */
export class ExchangeWebviewMessageHandler
  implements IWebviewMessageConsumer<ExchangeWebViewMessageType>
{
  private readonly _logger = getLogger("ExchangeWebviewMessageHandler");
  public readonly supportedTypes = ExchangeWebViewMessageType;

  constructor(private readonly _exchangeManager: ExchangeManager) {}

  /**
   * Handles incoming webview messages.
   * @param msg - The incoming message
   * @param postMessage - Function to post a response message
   */
  public async handle(
    msg: WebViewMessage<ExchangeWebViewMessageType>,
    postMessage: (
      msg: WebViewMessage<
        ExchangeWebViewMessageType | CommonWebViewMessageType
      >,
    ) => void,
  ): Promise<void> {
    try {
      switch (msg.type) {
        case ExchangeWebViewMessageType.loadConversationExchangesRequest: {
          const webviewMsg = msg as LoadConversationExchangesRequest;
          const response = await this._loadConversationExchanges(webviewMsg);
          postMessage(response);
          break;
        }
        case ExchangeWebViewMessageType.loadExchangesByUuidsRequest: {
          const webviewMsg = msg as LoadExchangesByUuidsRequest;
          const response = await this._loadExchangesByUuids(webviewMsg);
          postMessage(response);
          break;
        }
        case ExchangeWebViewMessageType.saveExchangesRequest: {
          const webviewMsg = msg as SaveExchangesRequest;
          const response = await this._saveExchanges(webviewMsg);
          postMessage(response);
          break;
        }
        case ExchangeWebViewMessageType.deleteExchangesRequest: {
          const webviewMsg = msg as DeleteExchangesRequest;
          const response = await this._deleteExchanges(webviewMsg);
          postMessage(response);
          break;
        }
        case ExchangeWebViewMessageType.countExchangesRequest: {
          const webviewMsg = msg as CountExchangesRequest;
          const response = await this._countExchanges(webviewMsg);
          postMessage(response);
          break;
        }
        case ExchangeWebViewMessageType.deleteConversationExchangesRequest: {
          const webviewMsg = msg as DeleteConversationExchangesRequest;
          const response = await this._deleteConversationExchanges(webviewMsg);
          postMessage(response);
          break;
        }
        default: {
          this._logger.warn(`Unhandled exchange message type: ${msg.type}`);
          postMessage({ type: CommonWebViewMessageType.empty });
          break;
        }
      }
    } catch (error) {
      this._logger.error(`Error handling exchange message ${msg.type}:`, error);
      postMessage({ type: CommonWebViewMessageType.empty });
    }
  }

  /**
   * Loads all exchanges for a conversation.
   */
  private async _loadConversationExchanges(
    message: LoadConversationExchangesRequest,
  ): Promise<LoadConversationExchangesResponse> {
    this._logger.debug(
      `Loading all exchanges for conversation ${message.data.conversationId}`,
    );

    const exchanges = await this._exchangeManager.loadConversationExchanges(
      message.data.conversationId,
    );

    return {
      type: ExchangeWebViewMessageType.loadConversationExchangesResponse,
      data: { exchanges },
    };
  }

  /**
   * Loads specific exchanges by UUIDs.
   */
  private async _loadExchangesByUuids(
    message: LoadExchangesByUuidsRequest,
  ): Promise<LoadExchangesByUuidsResponse> {
    this._logger.debug(
      `Loading ${message.data.uuids.length} exchanges for conversation ${message.data.conversationId}`,
    );

    const exchanges = await this._exchangeManager.loadExchangesByUuids(
      message.data.conversationId,
      message.data.uuids,
    );

    return {
      type: ExchangeWebViewMessageType.loadExchangesByUuidsResponse,
      data: { exchanges },
    };
  }

  /**
   * Saves exchanges using upsert semantics (creates new or updates existing).
   */
  private async _saveExchanges(
    message: SaveExchangesRequest,
  ): Promise<SaveExchangesResponse> {
    this._logger.debug(
      `Upserting ${message.data.exchanges.length} exchanges for conversation ${message.data.conversationId}`,
    );

    await this._exchangeManager.saveExchanges(
      message.data.conversationId,
      message.data.exchanges,
    );

    return {
      type: ExchangeWebViewMessageType.saveExchangesResponse,
      data: {},
    };
  }

  /**
   * Deletes exchanges by UUIDs.
   */
  private async _deleteExchanges(
    message: DeleteExchangesRequest,
  ): Promise<DeleteExchangesResponse> {
    this._logger.debug(
      `Deleting ${message.data.uuids.length} exchanges for conversation ${message.data.conversationId}`,
    );

    await this._exchangeManager.deleteExchanges(
      message.data.conversationId,
      message.data.uuids,
    );

    return {
      type: ExchangeWebViewMessageType.deleteExchangesResponse,
      data: {},
    };
  }

  /**
   * Counts exchanges in a conversation.
   */
  private async _countExchanges(
    message: CountExchangesRequest,
  ): Promise<CountExchangesResponse> {
    this._logger.debug(
      `Counting exchanges for conversation ${message.data.conversationId}`,
    );

    const count = await this._exchangeManager.countExchanges(
      message.data.conversationId,
    );

    return {
      type: ExchangeWebViewMessageType.countExchangesResponse,
      data: { count },
    };
  }

  /**
   * Deletes all exchanges for a conversation.
   */
  private async _deleteConversationExchanges(
    message: DeleteConversationExchangesRequest,
  ): Promise<DeleteConversationExchangesResponse> {
    this._logger.debug(
      `Deleting all exchanges for conversation ${message.data.conversationId}`,
    );

    await this._exchangeManager.deleteConversationExchanges(
      message.data.conversationId,
    );

    return {
      type: ExchangeWebViewMessageType.deleteConversationExchangesResponse,
      data: {},
    };
  }
}
