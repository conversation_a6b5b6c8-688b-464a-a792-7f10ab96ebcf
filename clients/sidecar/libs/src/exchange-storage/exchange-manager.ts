/**
 * ExchangeManager handles exchange storage and provides the main API for exchange operations.
 * It uses a pluggable key-value store for efficient storage, eliminating the need
 * for page-based storage and manifest management.
 */

import { getLogger } from "../logging";
import { getPluginKvStore } from "../client-interfaces/plugin-kv-store";
import {
  IPluginKvStore,
  KvBatchOperation,
  KvIteratorOptions,
} from "../client-interfaces/plugin-kv-store";
import type { StoredExchange } from "./storage-types";

/**
 * Optimized conversation metadata - only stores what's expensive to compute.
 *
 * ## Design Principles
 * - **Minimal data**: Only stores essential counters and timestamps
 * - **No UUID arrays**: Eliminates exchangeUuids array for simplicity
 * - **Fast updates**: Simple increment operations for totalExchanges
 *
 * @interface ConversationMetadata
 */
interface ConversationMetadata {
  /** The conversation identifier */
  conversationId: string;
  /** Total number of exchanges in this conversation */
  totalExchanges: number;
  /** Unix timestamp of last update */
  lastUpdated: number;
}

/**
 * ExchangeManager provides the main API for exchange storage operations.
 *
 * ## Key Features
 * - **High Performance**: Uses LevelDB for fast key-value operations
 * - **Simple Operations**: Basic read/save/delete operations that mirror KV store semantics
 * - **Conversation-Based Loading**: Efficient prefix scanning for conversation exchanges
 * - **Atomic Operations**: Batch saves and deletes for data consistency
 * - **Automatic Upsert**: Save operations create or update automatically
 *
 * ## Data Model
 * - **Exchanges**: Stored with keys `exchange:{conversationId}:{exchangeUuid}`
 * - **Metadata**: Stored with keys `metadata:{conversationId}` containing totalExchanges and lastUpdated
 * - **No reverse lookups**: Eliminated `reverse:{exchangeUuid}` → `conversationId` mappings for simplicity
 *
 * ## Key Structure
 * - Exchange keys: `exchange:conv-123:uuid-456` → StoredExchange JSON
 * - Metadata keys: `metadata:conv-123` → ConversationMetadata JSON
 *
 * ## Performance
 * - Range queries using prefix scanning: `gte: "exchange:conv-123:"`, `lt: "exchange:conv-123:\xFF"`
 * - Efficient conversation-based loading without UUID lists
 * - Batch operations for atomic updates
 * - Supports 250,000+ exchanges/second throughput with LevelDB
 */
export class ExchangeManager {
  private readonly _logger = getLogger("ExchangeManager");

  /** Key-value store instance for storing exchanges */
  private readonly _kvStore: IPluginKvStore;

  /** Suffix used for range query upper bounds in LevelDB */
  private static readonly RANGE_SUFFIX = "\xFF";

  constructor() {
    this._kvStore = getPluginKvStore();
  }

  /**
   * Gets the storage key for an exchange.
   */
  private _getExchangeKey(
    conversationId: string,
    exchangeUuid: string,
  ): string {
    return `${this._getExchangePrefix(conversationId)}${exchangeUuid}`;
  }

  /**
   * Gets the storage key for conversation metadata.
   */
  private _getMetadataKey(conversationId: string): string {
    return `metadata:${conversationId}`;
  }

  /**
   * Gets the prefix for all exchanges in a conversation.
   * Used for range queries to load all exchanges efficiently.
   */
  private _getExchangePrefix(conversationId: string): string {
    return `exchange:${conversationId}:`;
  }

  /**
   * Loads conversation metadata.
   */
  private async _loadMetadata(
    conversationId: string,
  ): Promise<ConversationMetadata | undefined> {
    const key = this._getMetadataKey(conversationId);

    try {
      const jsonString = await this._kvStore.get(key);
      if (jsonString === undefined) {
        return undefined;
      }
      return JSON.parse(jsonString) as ConversationMetadata;
    } catch (error: any) {
      this._logger.error(
        `Failed to load metadata for conversation ${conversationId}`,
        error,
      );
      return undefined;
    }
  }

  /**
   * Loads exchanges by their UUIDs directly from LevelDB.
   */
  async loadExchangesByUuids(
    conversationId: string,
    uuids: string[],
  ): Promise<StoredExchange[]> {
    if (uuids.length === 0) {
      return [];
    }

    this._logger.debug(
      `Loading ${uuids.length} exchanges from conversation ${conversationId}`,
    );

    // Create a set of target keys for efficient lookup
    const targetKeys = new Set(
      uuids.map((uuid) => this._getExchangeKey(conversationId, uuid)),
    );

    // Use iterator to batch load exchanges from the conversation
    const exchangePrefix = this._getExchangePrefix(conversationId);
    const results: StoredExchange[] = [];

    try {
      for await (const [key, value] of this._kvStore.iterator({
        gte: exchangePrefix,
        lt: exchangePrefix + ExchangeManager.RANGE_SUFFIX,
      })) {
        // Only process keys that match our target UUIDs
        if (targetKeys.has(key)) {
          try {
            const exchange = JSON.parse(value) as StoredExchange;
            results.push(exchange);
          } catch (error: any) {
            this._logger.error(
              `Failed to parse exchange data for key ${key}`,
              error,
            );
          }
        }
      }
    } catch (error: any) {
      this._logger.error(
        `Failed to iterate exchanges for conversation ${conversationId}`,
        error,
      );
      throw error;
    }

    this._logger.debug(
      `Successfully loaded ${results.length} exchanges from conversation ${conversationId}`,
    );
    return results;
  }

  /**
   * Saves exchanges to storage using upsert semantics and optimized batch operations.
   *
   * ## Behavior
   * - Uses upsert semantics: creates new exchanges or updates existing ones
   * - Stores exchanges with keys: `exchange:{conversationId}:{exchangeUuid}`
   * - Updates conversation metadata with accurate total count
   * - Ensures all exchanges have the correct conversationId
   * - Uses atomic batch operations for consistency
   *
   * @param conversationId The conversation ID to store exchanges under
   * @param exchanges Array of exchanges to save (upsert)
   */
  async saveExchanges(
    conversationId: string,
    exchanges: StoredExchange[],
  ): Promise<void> {
    if (exchanges.length === 0) {
      return;
    }
    this._logger.debug(
      `Upserting ${exchanges.length} exchanges to conversation ${conversationId}`,
    );

    // Check which exchanges already exist to calculate accurate count using batch iterator
    const existingKeys = new Set<string>();
    const targetKeys = new Set(
      exchanges.map((exchange) =>
        this._getExchangeKey(conversationId, exchange.uuid),
      ),
    );

    try {
      const exchangePrefix = this._getExchangePrefix(conversationId);
      // Use keys iterator for better performance since we only need keys
      for await (const key of this._kvStore.keys({
        gte: exchangePrefix,
        lt: exchangePrefix + ExchangeManager.RANGE_SUFFIX,
      })) {
        if (targetKeys.has(key)) {
          existingKeys.add(key);
        }
      }
    } catch (error: any) {
      this._logger.error(
        `Failed to check existing exchanges for conversation ${conversationId}`,
        error,
      );
      // Fall back to treating all as new exchanges
    }

    // Get existing metadata to update count
    const existingMetadata = await this._loadMetadata(conversationId);
    const currentCount = existingMetadata?.totalExchanges || 0;

    // Only increment count for new exchanges
    const newExchangesCount = exchanges.length - existingKeys.size;
    const updatedMetadata: ConversationMetadata = {
      conversationId,
      totalExchanges: currentCount + newExchangesCount,
      lastUpdated: Date.now(),
    };

    // Build batch operations for exchanges and metadata
    const batchOps: Array<KvBatchOperation> = [
      // Upsert exchanges (ensure they have the correct conversationId)
      ...exchanges.map((exchange) => ({
        type: "put" as const,
        key: this._getExchangeKey(conversationId, exchange.uuid),
        value: JSON.stringify({ ...exchange, conversationId }),
      })),
      // Update metadata
      {
        type: "put" as const,
        key: this._getMetadataKey(conversationId),
        value: JSON.stringify(updatedMetadata),
      },
    ];

    // Execute all operations in a single atomic batch
    await this._kvStore.batch(batchOps);
    this._logger.debug(
      `Successfully upserted ${exchanges.length} exchanges (${newExchangesCount} new, ${existingKeys.size} updated) to conversation ${conversationId}`,
    );
  }

  /**
   * Deletes exchanges directly from LevelDB using a single batch operation.
   * Updates metadata count by subtracting deleted exchanges.
   */
  async deleteExchanges(
    conversationId: string,
    uuids: string[],
  ): Promise<void> {
    if (uuids.length === 0) {
      return;
    }

    this._logger.debug(
      `Deleting ${uuids.length} exchanges from conversation ${conversationId}`,
    );

    // Get existing metadata to update count
    const existingMetadata = await this._loadMetadata(conversationId);
    const currentCount = existingMetadata?.totalExchanges || 0;

    // Create updated metadata with reduced count
    const updatedMetadata: ConversationMetadata = {
      conversationId,
      totalExchanges: Math.max(0, currentCount - uuids.length),
      lastUpdated: Date.now(),
    };

    // Build batch operations to delete exchanges and update metadata
    const batchOps: Array<KvBatchOperation> = [
      // Delete exchanges
      ...uuids.map((uuid) => ({
        type: "del" as const,
        key: this._getExchangeKey(conversationId, uuid),
      })),
      // Update metadata
      {
        type: "put" as const,
        key: this._getMetadataKey(conversationId),
        value: JSON.stringify(updatedMetadata),
      },
    ];

    // Execute all operations in a single atomic batch
    await this._kvStore.batch(batchOps);

    this._logger.debug(
      `Successfully deleted ${uuids.length} exchanges from conversation ${conversationId}`,
    );
  }

  /**
   * Deletes all exchanges for a conversation using efficient prefix scanning and batch operations.
   * This method removes all exchanges and their metadata for the specified conversation.
   *
   * @param conversationId The conversation ID to delete all exchanges for
   */
  async deleteConversationExchanges(conversationId: string): Promise<void> {
    this._logger.debug(
      `Deleting all exchanges for conversation ${conversationId}`,
    );

    const prefix = this._getExchangePrefix(conversationId);
    const keysToDelete: string[] = [];

    try {
      // Use keys iterator for better performance since we only need keys
      for await (const key of this._kvStore.keys({
        gte: prefix,
        lt: prefix + ExchangeManager.RANGE_SUFFIX,
      })) {
        keysToDelete.push(key);
      }

      if (keysToDelete.length === 0) {
        this._logger.debug(
          `No exchanges found for conversation ${conversationId}`,
        );
        return;
      }

      // Build batch operations to delete all exchanges and metadata
      const batchOps: Array<KvBatchOperation> = [
        // Delete all exchanges
        ...keysToDelete.map((key) => ({
          type: "del" as const,
          key,
        })),
        // Delete metadata
        {
          type: "del" as const,
          key: this._getMetadataKey(conversationId),
        },
      ];

      // Execute all operations in a single atomic batch
      await this._kvStore.batch(batchOps);

      this._logger.debug(
        `Successfully deleted ${keysToDelete.length} exchanges and metadata for conversation ${conversationId}`,
      );
    } catch (error: any) {
      this._logger.error(
        `Failed to delete exchanges for conversation ${conversationId}`,
        error,
      );
      throw error;
    }
  }

  /**
   * Counts the number of exchanges in a conversation using metadata.
   */
  async countExchanges(conversationId: string): Promise<number> {
    const metadata = await this._loadMetadata(conversationId);
    return metadata?.totalExchanges || 0;
  }

  /**
   * Loads all exchanges for a conversation using efficient prefix scanning.
   *
   * ## Performance
   * - Uses range query: `gte: "exchange:{conversationId}:"`, `lt: "exchange:{conversationId}:\xFF"`
   * - No intermediate UUID lookups required
   * - Scales efficiently with conversation size (250,000+ exchanges/second)
   *
   * @param conversationId The conversation ID to load exchanges for
   * @returns Array of all exchanges in the conversation
   */
  async loadConversationExchanges(
    conversationId: string,
  ): Promise<StoredExchange[]> {
    this._logger.debug(
      `Loading all exchanges for conversation ${conversationId}`,
    );

    const prefix = this._getExchangePrefix(conversationId);
    const exchanges: StoredExchange[] = [];

    try {
      // Use iterator to scan all exchanges for this conversation
      const options: KvIteratorOptions = {
        gte: prefix,
        lt: prefix + ExchangeManager.RANGE_SUFFIX,
      };

      for await (const [key, value] of this._kvStore.iterator(options)) {
        try {
          const exchange = JSON.parse(value) as StoredExchange;
          exchanges.push(exchange);
        } catch (parseError: any) {
          this._logger.error(
            `Failed to parse exchange at key ${key}`,
            parseError,
          );
        }
      }

      this._logger.debug(
        `Loaded ${exchanges.length} exchanges for conversation ${conversationId}`,
      );
      return exchanges;
    } catch (error: any) {
      this._logger.error(
        `Failed to load exchanges for conversation ${conversationId}`,
        error,
      );
      return [];
    }
  }

  /**
   * Closes the key-value store and cleans up resources.
   */
  async close(): Promise<void> {
    await this._kvStore.close();
    this._logger.debug("Closed key-value store");
  }
}
