/**
 * Storage types for exchange storage.
 * Re-exports types from existing libraries where possible to avoid duplication.
 */

// Re-export core types from existing libraries
export type { Exchange } from "../chat/chat-types";
import type { Exchange } from "../chat/chat-types";

/**
 * Exchange status - re-using the same values as webview layer for consistency
 */
export type ExchangeStatus = "sent" | "success" | "failed";

/**
 * Seen state - re-using the same values as webview layer for consistency
 */
export type SeenState = "seen" | "unseen";

/**
 * StoredExchange extends the basic Exchange type with storage-specific fields.
 * This is the full exchange data structure used for persistence in the sidecar.
 */
export interface StoredExchange extends Exchange {
  /** Unique identifier for this exchange */
  uuid: string;

  /** ID of the conversation this exchange belongs to */
  conversationId: string;

  /** Current status of the exchange */
  status: ExchangeStatus;

  /** ISO timestamp when the exchange was created/updated */
  timestamp?: string;

  /** Whether the user has seen this exchange */
  seen_state?: SeenState;
}
