import { getClientWorkspaces } from "../client-interfaces/client-workspaces";
import type { QualifiedPathName } from "../workspace/qualified-path-name";
import { DiffViewDocument, type DiffViewDocumentOptions } from "./document";

/**
 * This document class represent an opt-out document rather than an opt-in document.
 * The difference is:
 * - opt-in means in order for changes to persist to disk, they must *explicitly* be accepted
 * - opt-out means in order for changes to *not* persist to disk, they must *explicitly* be rejected
 *
 * This means opt-in documents always persist the *original* code, while opt-out documents always
 * persist the *modified* code (since rejections will revert the modified code)
 */
export class AgentEditDiffViewDocument extends DiffViewDocument {
  constructor(
    filePath: QualifiedPathName,
    originalCode: string | undefined,
    modifiedCode: string | undefined,
    opts: DiffViewDocumentOptions,
  ) {
    super(filePath, originalCode, modifiedCode, { ...opts });

    // If the modified code is updated, we want to persist this document
    this.addDisposable(
      this.onModifiedUpdated(() => void this.writeDocumentToFile()),
    );
  }

  private async writeDocumentToFile() {
    if (this.modifiedCode === undefined) {
      await getClientWorkspaces().deleteFile(this.filePath);
    } else {
      await getClientWorkspaces().writeFile(this.filePath, this.modifiedCode);
    }
  }
}
