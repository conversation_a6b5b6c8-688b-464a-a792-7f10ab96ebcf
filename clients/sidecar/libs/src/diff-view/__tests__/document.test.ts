import { getLogger } from "../../logging";
import { QualifiedPathName } from "../../workspace/qualified-path-name";
import { DiffViewDocument } from "../document";

const logger = getLogger("test");

let document: DiffViewDocument;
const testPath = QualifiedPathName.from({
  rootPath: "/test",
  relPath: "file.ts",
});

beforeEach(() => {
  jest.clearAllMocks();
  document = new DiffViewDocument(
    testPath,
    "original content\nwith multiple\nlines",
    "modified content\nwith multiple\nlines",
    { logger },
  );
});

describe("DiffViewDocument", () => {
  describe("constructor and basic properties", () => {
    test("creates document with correct initial state", () => {
      expect(document.originalCode).toBe(
        "original content\nwith multiple\nlines",
      );
      expect(document.modifiedCode).toBe(
        "modified content\nwith multiple\nlines",
      );
      expect(document.absPath).toBe("/test/file.ts");
      expect(document.isEmptyDocument).toBe(false);
      expect(document.isUntitled).toBe(false);
      expect(document.filePath).toBe(testPath);
    });

    test("empty() creates empty document", () => {
      const emptyDoc = DiffViewDocument.empty();
      expect(emptyDoc.isEmptyDocument).toBe(true);
      expect(emptyDoc.originalCode).toBe("");
      expect(emptyDoc.modifiedCode).toBe("");
    });

    test("deleted() creates document representing a deleted file", () => {
      const deletedDoc = DiffViewDocument.deleted(testPath);
      expect(deletedDoc.isEmptyDocument).toBe(false);
      expect(deletedDoc.originalCode).toBeUndefined();
      expect(deletedDoc.modifiedCode).toBeUndefined();
      expect(deletedDoc.filePath).toBe(testPath);
    });

    test("isUntitled returns true for untitled documents", () => {
      const untitledDoc = new DiffViewDocument(
        QualifiedPathName.from({ rootPath: "", relPath: "Untitled-1" }),
        "content",
        "content",
        {
          isUntitled: () => true,
        },
      );
      expect(untitledDoc.isUntitled).toBe(true);
    });

    test("isUntitled returns false for non-untitled documents", () => {
      expect(document.isUntitled).toBe(false);
    });

    test("creates document without optional logger", () => {
      const docWithoutLogger = new DiffViewDocument(
        testPath,
        "content",
        "content",
        {},
      );
      expect(docWithoutLogger.toString("test")).toContain(
        "[test] DiffViewDocument",
      );
    });
  });

  describe("code modifications", () => {
    test("originalCode and modifiedCode getters", () => {
      document.updateCodeVersions("foo\nbar\n", "buzz\nbaz\n");
      expect(document.originalCode).toBe("foo\nbar\n");
      expect(document.modifiedCode).toBe("buzz\nbaz\n");
    });

    test("updateOriginal() updates code and notifies listeners", () => {
      const listener = jest.fn();
      document.onOriginalUpdated(listener);

      const result = document.updateOriginal("new content");

      expect(result).toBe(true);
      expect(document.originalCode).toBe("new content");
      expect(listener).toHaveBeenCalledWith(document);
    });

    test("updateOriginal() returns false if content hasn't changed", () => {
      const listener = jest.fn();
      document.onOriginalUpdated(listener);

      const result = document.updateOriginal(document.originalCode);

      expect(result).toBe(false);
      expect(listener).not.toHaveBeenCalled();
    });

    test("updateBuffer() updates modified code and notifies listeners", () => {
      const listener = jest.fn();
      document.onModifiedUpdated(listener);
      const result = document.updateBuffer("new modified content");

      expect(result).toBe(true);
      expect(document.modifiedCode).toBe("new modified content");
      expect(listener).toHaveBeenCalledWith(document);
    });

    test("updateBuffer() returns false if content hasn't changed", () => {
      const listener = jest.fn();
      document.onModifiedUpdated(listener);
      const result = document.updateBuffer(document.modifiedCode);
      expect(result).toBe(false);
      expect(listener).not.toHaveBeenCalled();
    });

    test("updateCodeVersions() updates both versions independently", () => {
      const originalListener = jest.fn();
      const modifiedListener = jest.fn();
      document.onOriginalUpdated(originalListener);
      document.onModifiedUpdated(modifiedListener);

      const result = document.updateCodeVersions(
        "new original",
        "new modified",
      );

      expect(result).toBe(true);
      expect(document.originalCode).toBe("new original");
      expect(document.modifiedCode).toBe("new modified");
      expect(originalListener).toHaveBeenCalledWith(document);
      expect(modifiedListener).toHaveBeenCalledWith(document);
    });

    test("updateCodeVersions() only updates modified code when original is undefined", () => {
      const originalListener = jest.fn();
      const modifiedListener = jest.fn();
      document.onOriginalUpdated(originalListener);
      document.onModifiedUpdated(modifiedListener);

      const originalBefore = document.originalCode;
      const result = document.updateCodeVersions(undefined, "new modified");

      expect(result).toBe(true);
      expect(document.originalCode).toBe(originalBefore);
      expect(document.modifiedCode).toBe("new modified");
      expect(originalListener).not.toHaveBeenCalled();
      expect(modifiedListener).toHaveBeenCalledWith(document);
    });

    test("updateCodeVersions() only updates original code when modified is undefined", () => {
      const originalListener = jest.fn();
      const modifiedListener = jest.fn();
      document.onOriginalUpdated(originalListener);
      document.onModifiedUpdated(modifiedListener);

      const modifiedBefore = document.modifiedCode;
      const result = document.updateCodeVersions("new original", undefined);

      expect(result).toBe(true);
      expect(document.originalCode).toBe("new original");
      expect(document.modifiedCode).toBe(modifiedBefore);
      expect(originalListener).toHaveBeenCalledWith(document);
      expect(modifiedListener).not.toHaveBeenCalled();
    });

    test("updateCodeVersions() returns false when no changes are made", () => {
      const originalListener = jest.fn();
      const modifiedListener = jest.fn();
      document.onOriginalUpdated(originalListener);
      document.onModifiedUpdated(modifiedListener);

      const result = document.updateCodeVersions(
        document.originalCode,
        document.modifiedCode,
      );
      expect(result).toBe(false);
      expect(originalListener).not.toHaveBeenCalled();
      expect(modifiedListener).not.toHaveBeenCalled();
    });

    test("updateCodeVersions() returns true when both inputs are undefined", () => {
      const originalListener = jest.fn();
      const modifiedListener = jest.fn();
      document.onOriginalUpdated(originalListener);
      document.onModifiedUpdated(modifiedListener);

      const result = document.updateCodeVersions(undefined, undefined);
      expect(result).toBe(true);
      expect(originalListener).toHaveBeenCalled();
      expect(modifiedListener).toHaveBeenCalled();
    });
  });

  describe("code getters", () => {
    test("originalCode returns empty string for empty document", () => {
      const emptyDoc = DiffViewDocument.empty();
      expect(emptyDoc.originalCode).toBe("");
    });

    test("modifiedCode returns empty string for empty document", () => {
      const emptyDoc = DiffViewDocument.empty();
      expect(emptyDoc.modifiedCode).toBe("");
    });

    test("originalCode returns undefined for deleted document", () => {
      const deletedDoc = DiffViewDocument.deleted(testPath);
      expect(deletedDoc.originalCode).toBeUndefined();
    });

    test("modifiedCode returns undefined for deleted document", () => {
      const deletedDoc = DiffViewDocument.deleted(testPath);
      expect(deletedDoc.modifiedCode).toBeUndefined();
    });

    test("originalCode handles code with trailing newline", () => {
      document.updateOriginal("content with newline\n");
      expect(document.originalCode).toBe("content with newline\n");
    });

    test("modifiedCode handles code with trailing newline", () => {
      document.updateBuffer("content with newline\n");
      expect(document.modifiedCode).toBe("content with newline\n");
    });

    test("originalCode adds newline to code without trailing newline", () => {
      document.updateOriginal("content without newline");
      expect(document.originalCode).toBe("content without newline");
    });

    test("modifiedCode adds newline to code without trailing newline", () => {
      document.updateBuffer("content without newline");
      expect(document.modifiedCode).toBe("content without newline");
    });
  });

  describe("handling undefined content", () => {
    test("updateOriginal accepts undefined to represent deleted file", () => {
      const result = document.updateOriginal(undefined);
      expect(result).toBe(true);
      expect(document.originalCode).toBeUndefined();
    });

    test("updateBuffer accepts undefined to represent deleted file", () => {
      const result = document.updateBuffer(undefined);
      expect(result).toBe(true);
      expect(document.modifiedCode).toBeUndefined();
    });

    test("updateCodeVersions can set both original and modified to undefined", () => {
      const result = document.updateCodeVersions(undefined, undefined);
      expect(result).toBe(true);
      expect(document.originalCode).toBeUndefined();
      expect(document.modifiedCode).toBeUndefined();
    });
  });

  describe("toString", () => {
    test("returns full string representation by default", () => {
      const result = document.toString("test");
      expect(result).toContain("[test] DiffViewDocument");
      expect(result).toContain(document.absPath);
      expect(result).toContain("Status    : Modified");
      expect(result).toContain("Orig Size : ");
      expect(result).toContain("Mod Size  : ");
      expect(result).toContain("Original Code:");
      expect(result).toContain("Modified Code:");
    });

    test("returns short string representation when short=true", () => {
      const result = document.toString("test", true);
      expect(result).toContain("[test] DiffViewDocument");
      expect(result).toContain(document.absPath);
      expect(result).toContain("Original  : 3 lines");
      expect(result).toContain("Modified  : 3 lines");
      expect(result).not.toContain("Original Code:");
      expect(result).not.toContain("Modified Code:");
    });

    test("indicates untitled files in both formats", () => {
      const untitledDoc = new DiffViewDocument(
        QualifiedPathName.from({ rootPath: "", relPath: "Untitled-1" }),
        "content",
        "content",
        {
          isUntitled: () => true,
        },
      );
      const fullResult = untitledDoc.toString("test");
      const shortResult = untitledDoc.toString("test", true);
      expect(fullResult).toContain("(untitled)");
      expect(shortResult).toContain("(untitled)");
    });

    test("returns formatted string for empty document in both formats", () => {
      const emptyDoc = DiffViewDocument.empty();
      const shortResult = emptyDoc.toString("test", true);
      const fullResult = emptyDoc.toString("test");

      // Short format checks
      expect(shortResult).toContain("[test] DiffViewDocument");
      expect(shortResult).toContain("Path      : .");
      expect(shortResult).toContain("Original  : 0 lines");
      expect(shortResult).toContain("Modified  : 0 lines");

      // Full format checks
      expect(fullResult).toContain("[test] DiffViewDocument");
      expect(fullResult).toContain("Path      : .");
      expect(fullResult).toContain("Status    : Unchanged");
      expect(fullResult).toContain("Orig Size : 0 chars");
      expect(fullResult).toContain("Mod Size  : 0 chars");
      expect(fullResult).toContain("(empty)");
    });

    test("returns formatted string for deleted document in both formats", () => {
      const deletedDoc = DiffViewDocument.deleted(testPath);
      const shortResult = deletedDoc.toString("test", true);
      const fullResult = deletedDoc.toString("test");

      // Short format checks
      expect(shortResult).toContain("[test] DiffViewDocument");
      expect(shortResult).toContain(`Path      : ${testPath.absPath}`);
      expect(shortResult).toContain("Original  : (file deleted)");
      expect(shortResult).toContain("Modified  : (file deleted)");

      // Full format checks
      expect(fullResult).toContain("[test] DiffViewDocument");
      expect(fullResult).toContain(`Path      : ${testPath.absPath}`);
      expect(fullResult).toContain("Status    : Unchanged");
      expect(fullResult).toContain("Orig Size : (file deleted)");
      expect(fullResult).toContain("Mod Size  : (file deleted)");
      expect(fullResult).toContain("Original Code:");
      expect(fullResult).toContain("(file deleted)");
      expect(fullResult).toContain("Modified Code:");
      expect(fullResult).toContain("(file deleted)");
    });

    test("shows correct line counts when content is modified in short format", () => {
      document.updateBuffer("different content");
      const result = document.toString("test", true);
      expect(result).toContain("Original  : 3 lines");
      expect(result).toContain("Modified  : 1 lines");
    });

    test("shows modified status and content in full format", () => {
      document.updateBuffer("different content");
      const result = document.toString("test");
      expect(result).toContain("Status    : Modified");
      expect(result).toContain("Original Code:");
      expect(result).toContain("original content");
      expect(result).toContain("Modified Code:");
      expect(result).toContain("different content");
    });
  });

  describe("cleanup", () => {
    test("dispose() removes all listeners", () => {
      const listener = jest.fn();
      document.onOriginalUpdated(listener);
      document.dispose();

      document.updateOriginal("new content");
      expect(listener).not.toHaveBeenCalled();
    });

    test("dispose() removes modified code listeners", () => {
      const listener = jest.fn();
      document.onModifiedUpdated(listener);
      document.dispose();

      document.updateBuffer("new content");
      expect(listener).not.toHaveBeenCalled();
    });
  });

  describe("event handling", () => {
    test("onOriginalUpdated returns disposable that removes listener when disposed", () => {
      const listener = jest.fn();
      const disposable = document.onOriginalUpdated(listener);

      document.updateOriginal("new content");
      expect(listener).toHaveBeenCalledWith(document);

      disposable.dispose();
      document.updateOriginal("another content");
      expect(listener).toHaveBeenCalledTimes(1); // Listener not called after dispose
    });

    test("onModifiedUpdated returns disposable that removes listener when disposed", () => {
      const listener = jest.fn();
      const disposable = document.onModifiedUpdated(listener);

      document.updateBuffer("new content");
      expect(listener).toHaveBeenCalledWith(document);

      disposable.dispose();
      document.updateBuffer("another content");
      expect(listener).toHaveBeenCalledTimes(1); // Listener not called after dispose
    });
  });

  describe("absPath handling", () => {
    test("absPath returns correct path", () => {
      expect(document.absPath).toBe("/test/file.ts");
    });
  });
});
