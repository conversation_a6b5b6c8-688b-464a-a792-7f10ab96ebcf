/**
 * A VSCode-agnostic representation of a document that we are tracking.
 * This is useful to manage the logic of managing chunks of edits to a document.
 *
 * If you need this to use VSCode APIs, the recommendation is to abstract it into something
 * non-specific, inject it through @DiffViewDocumentOptions, and use it that way.
 */
import { DisposableService } from "../lifecycle/disposable-service";
import type { AugmentLogger } from "../logging";
import { QualifiedPathName } from "../workspace/qualified-path-name";
import { type IQualifiedPathName } from "../workspace/workspace-types";

/**
 * Listener function type for document modifications.
 * @param newState The updated state of the DiffViewDocument.
 */
interface DocumentModifiedListener {
  (newState: DiffViewDocument): void;
}

/**
 * Disposer function type for document modification listeners.
 */
interface DocumentModifiedDisposable {
  dispose(): void;
}

/**
 * Represents a chunk of text to be edited in the document.
 */
export interface EditChunk {
  startLine: number; // The starting line number of the edit.
  endLine: number; // The ending line number of the edit.
  newText: string; // The new text to be inserted.
}

export type DiffViewContents = {
  originalCode: string | undefined;
  modifiedCode: string | undefined;
};

export interface DiffViewDocumentOptions {
  logger?: AugmentLogger;
  isUntitled?: isUntitledFileFn;
}

/**
 * DiffViewDocument is a class that represents the state of a document being diffed.
 *
 * During the lifecycle of this document, `modifiedCode` can be updated, and generally represents
 * the set of changes in review, but not yet committed. `originalCode` also is mutable, but more closely
 * represents what the user has "accepted" as a change. Note that this is *not* the same as what is actually
 * written to disk.
 */
export class DiffViewDocument extends DisposableService {
  /* eslint-disable @typescript-eslint/naming-convention */
  /** Represents an empty path name. */
  private static EMPTY_PATH_NAME: IQualifiedPathName = {
    rootPath: "",
    relPath: "",
  };
  /* eslint-enable @typescript-eslint/naming-convention */

  // Original code is immutable. Modified can be mutable.
  // Note that Original code does *not* represent what is actually on disk.
  // The user of this document is responsible for keeping the code up-to-date
  // with the file on-disk.
  /** Set of listeners for document modifications. */
  private _originalListeners: Set<DocumentModifiedListener>;
  private _modifiedListeners: Set<DocumentModifiedListener>;

  private _isUntitled: isUntitledFileFn;

  /**
   * Creates a new DiffViewDocument instance.
   * @param filePath The qualified path name of the document.
   * @param _originalCode The original code content.
   * @param _modifiedCode The modified code content.
   * @param _opts Optional options for the document.
   */
  constructor(
    public readonly filePath: QualifiedPathName,
    protected _originalCode: string | undefined,
    protected _modifiedCode: string | undefined,
    protected readonly _opts: DiffViewDocumentOptions,
  ) {
    super();
    this._isUntitled = this._opts.isUntitled ?? (() => false);
    this._originalListeners = new Set();
    this._modifiedListeners = new Set();
    this.addDisposable({ dispose: () => this._originalListeners.clear() });
    this.addDisposable({ dispose: () => this._modifiedListeners.clear() });
    this._opts?.logger?.debug(this.toString("constructor", true));
  }

  /**
   * Returns a string representation of the document for logging purposes.
   *
   * @param context A string providing context for the log.
   * @param short Optional flag to return a shorter version with only file path and line counts.
   * @returns A formatted string representation of the document.
   */
  public toString(context: string, short: boolean = false): string {
    const isUntitled = this.isUntitled ? " (untitled)" : "";
    const hasChanges = this._originalCode !== this._modifiedCode;
    const originalLines = this._originalCode
      ? this._originalCode.split("\n").length
      : 0;
    const modifiedLines = this._modifiedCode
      ? this._modifiedCode.split("\n").length
      : 0;
    const originalExists = this._originalCode !== undefined;
    const modifiedExists = this._modifiedCode !== undefined;

    if (short) {
      return [
        `[${context}] DiffViewDocument`,
        "----------------------------------------",
        `Path      : ${this.filePath.absPath}${isUntitled}`,
        `Original  : ${originalExists ? `${originalLines} lines` : "(file deleted)"}`,
        `Modified  : ${modifiedExists ? `${modifiedLines} lines` : "(file deleted)"}`,
        "----------------------------------------",
      ].join("\n");
    }

    return [
      `[${context}] DiffViewDocument`,
      "----------------------------------------",
      `Path      : ${this.filePath.absPath}${isUntitled}`,
      `Status    : ${hasChanges ? "Modified" : "Unchanged"}`,
      `Orig Size : ${originalExists ? `${this._originalCode?.length} chars` : "(file deleted)"}`,
      `Mod Size  : ${modifiedExists ? `${this._modifiedCode?.length} chars` : "(file deleted)"}`,
      "",
      "Original Code:",
      originalExists ? this._originalCode || "(empty)" : "(file deleted)",
      "",
      "Modified Code:",
      modifiedExists ? this._modifiedCode || "(empty)" : "(file deleted)",
      "----------------------------------------",
    ].join("\n");
  }

  /**
   * Creates an empty DiffViewDocument.
   * @returns A new DiffViewDocument with empty content.
   */
  public static empty(): DiffViewDocument {
    return new DiffViewDocument(
      QualifiedPathName.from(DiffViewDocument.EMPTY_PATH_NAME),
      "",
      "",
      {},
    );
  }

  /**
   * Creates a DiffViewDocument representing a deleted file.
   * @param filePath The qualified path name of the document.
   * @returns A new DiffViewDocument with undefined content.
   */
  public static deleted(filePath: QualifiedPathName): DiffViewDocument {
    return new DiffViewDocument(filePath, undefined, undefined, {});
  }

  /**
   * Checks if the document is empty.
   * @returns True if the document is empty, false otherwise.
   */
  public get isEmptyDocument(): boolean {
    return this.filePath.equals(DiffViewDocument.EMPTY_PATH_NAME);
  }

  /**
   * Gets the absolute path of the document.
   * @returns The absolute path as a string.
   */
  public get absPath(): string {
    return this.filePath.absPath;
  }

  /**
   * Gets the original code content.
   * @returns The original code as a string.
   */
  public get originalCode(): string | undefined {
    if (this.isEmptyDocument) {
      return "";
    }
    return this._originalCode;
  }

  /**
   * Gets the modified code content.
   * @returns The modified code as a string.
   */
  public get modifiedCode(): string | undefined {
    if (this.isEmptyDocument) {
      return "";
    }
    return this._modifiedCode;
  }

  /**
   * Checks if the document is untitled.
   * @returns True if the document is untitled, false otherwise.
   */
  public get isUntitled(): boolean {
    return this._isUntitled(this.filePath);
  }

  /**
   * Adds a listener that will be called when the original document is modified.
   * @param listener The listener function to add.
   */
  onOriginalUpdated = (
    listener: DocumentModifiedListener,
  ): DocumentModifiedDisposable => {
    this._originalListeners.add(listener);
    return { dispose: () => this._originalListeners.delete(listener) };
  };

  /**
   * Adds a listener that will be called when the modified document is modified.
   */
  onModifiedUpdated = (
    listener: DocumentModifiedListener,
  ): DocumentModifiedDisposable => {
    this._modifiedListeners.add(listener);
    return { dispose: () => this._modifiedListeners.delete(listener) };
  };

  /**
   * Updates the original code content.
   * @param newCode The new code to set as original.
   * @returns True if the code was updated, false otherwise.
   */
  updateOriginal = (newCode: string | undefined): boolean => {
    if (this.originalCode === newCode) {
      return false;
    }
    this._originalCode = newCode;
    // Notify listeners of the change
    this._originalListeners.forEach((listener) => listener(this));
    return true;
  };

  /**
   * Updates the modified code content.
   * @param newCode The new code to set as modified.
   * @returns True if the code was updated, false otherwise.
   */
  updateBuffer = (newCode: string | undefined): boolean => {
    if (this.modifiedCode === newCode) {
      return false;
    }
    this._modifiedCode = newCode;
    // Notify listeners of the change
    this._modifiedListeners.forEach((listener) => listener(this));
    return true;
  };

  /**
   * Updates both original and modified code versions.
   * @param originalCode The new original code (optional).
   * @param modifiedCode The new modified code (optional).
   * @returns True if either code version was updated, false otherwise.
   */
  updateCodeVersions = (
    originalCode: string | undefined,
    modifiedCode: string | undefined,
  ): boolean => {
    let hasChanged = false;

    // Handle the case where both are undefined explicitly
    if (originalCode === undefined && modifiedCode === undefined) {
      // Special case: if both are undefined, we want to set both to undefined
      hasChanged = this.updateOriginal(undefined) || hasChanged;
      hasChanged = this.updateBuffer(undefined) || hasChanged;
      return hasChanged;
    }

    // Only update if the values are provided
    if (originalCode !== undefined) {
      hasChanged = this.updateOriginal(originalCode) || hasChanged;
    }

    if (modifiedCode !== undefined) {
      hasChanged = this.updateBuffer(modifiedCode) || hasChanged;
    }

    return hasChanged;
  };
}

export type isUntitledFileFn = (qualifiedPath: QualifiedPathName) => boolean;
