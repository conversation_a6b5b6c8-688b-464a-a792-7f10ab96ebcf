import { MetricsReporter } from "@augment-internal/sidecar-libs/src/metrics/metrics-reporter";
import { AgentRequestEventData } from "@augment-internal/sidecar-libs/src/metrics/types";
import { msecToTimestamp } from "@augment-internal/sidecar-libs/src/utils/time";
import { AgentRequestEvent } from "../client-interfaces/api-client-types";
import { getAPIClient } from "../client-interfaces/api-client";

class AgentRequestEventReporter extends MetricsReporter<AgentRequestEvent> {
  private static maxRecords = 10000;
  private static batchSize = 1000;
  private static uploadMsec = 10000;

  private static instance: AgentRequestEventReporter | null = null;

  /**
   * Gets the singleton instance of AgentRequestEventReporter
   * @param maxRecords Optional maximum number of records
   * @param uploadMs Optional upload interval in milliseconds
   * @param batchSize Optional batch size
   * @returns The singleton instance of AgentRequestEventReporter
   */
  public static getInstance(): AgentRequestEventReporter {
    if (!AgentRequestEventReporter.instance) {
      AgentRequestEventReporter.instance = new AgentRequestEventReporter();
      AgentRequestEventReporter.instance.enableUpload();
    }
    return AgentRequestEventReporter.instance;
  }

  public static reset(): void {
    if (AgentRequestEventReporter.instance) {
      AgentRequestEventReporter.instance.dispose();
      AgentRequestEventReporter.instance = null;
    }
  }

  private constructor() {
    super(
      "AgentRequestEventReporter",
      AgentRequestEventReporter.maxRecords,
      AgentRequestEventReporter.uploadMsec,
      AgentRequestEventReporter.batchSize,
    );
  }

  // reportEvent reports an agent request event.
  public reportEvent(eventData: AgentRequestEventData): void {
    const [eventTimeSec, eventTimeNsec] = msecToTimestamp(Date.now());

    let eventDataPayload = undefined;
    /* eslint-disable @typescript-eslint/naming-convention */

    if (eventData.eventData?.chatHistorySummarizationData) {
      eventDataPayload = {
        chat_history_summarization_data: {
          total_history_char_count:
            eventData.eventData.chatHistorySummarizationData
              .totalHistoryCharCount,
          total_history_exchange_count:
            eventData.eventData.chatHistorySummarizationData
              .totalHistoryExchangeCount,
          head_char_count:
            eventData.eventData.chatHistorySummarizationData.headCharCount,
          head_exchange_count:
            eventData.eventData.chatHistorySummarizationData.headExchangeCount,
          head_last_request_id:
            eventData.eventData.chatHistorySummarizationData.headLastRequestId,
          tail_char_count:
            eventData.eventData.chatHistorySummarizationData.tailCharCount,
          tail_exchange_count:
            eventData.eventData.chatHistorySummarizationData.tailExchangeCount,
          tail_last_request_id:
            eventData.eventData.chatHistorySummarizationData.tailLastRequestId,
          summary_char_count:
            eventData.eventData.chatHistorySummarizationData.summaryCharCount,
          summarization_duration_ms:
            eventData.eventData.chatHistorySummarizationData
              .summarizationDurationMs,
          is_cache_about_to_expire:
            eventData.eventData.chatHistorySummarizationData
              .isCacheAboutToExpire,
          is_aborted:
            eventData.eventData.chatHistorySummarizationData.isAborted,
        },
      };
    } else if (eventData.eventData?.firstTokenTimingData) {
      eventDataPayload = {
        first_token_timing_data: {
          user_message_sent_timestamp_ms:
            eventData.eventData.firstTokenTimingData.userMessageSentTimestampMs,
          first_token_received_timestamp_ms:
            eventData.eventData.firstTokenTimingData
              .firstTokenReceivedTimestampMs,
          time_to_first_token_ms:
            eventData.eventData.firstTokenTimingData.timeToFirstTokenMs,
        },
      };
    }

    this.report({
      event_time_sec: eventTimeSec,
      event_time_nsec: eventTimeNsec,
      event_name: eventData.eventName,
      conversation_id: eventData.conversationId,
      request_id: eventData.requestId,
      chat_history_length: eventData.chatHistoryLength,
      event_data: eventDataPayload,
    });
    /* eslint-enable @typescript-eslint/naming-convention */
  }

  protected performUpload(batch: AgentRequestEvent[]): Promise<void> {
    return getAPIClient().logAgentRequestEvent(batch);
  }
}

/**
 * Gets the singleton instance of AgentRequestEventReporter
 * @returns The singleton instance of AgentRequestEventReporter
 */
export function getAgentRequestEventReporter(): AgentRequestEventReporter {
  return AgentRequestEventReporter.getInstance();
}

export function resetAgentRequestEventReporter() {
  AgentRequestEventReporter.reset();
}
