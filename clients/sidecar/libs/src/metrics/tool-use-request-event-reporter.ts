import { MetricsReporter } from "@augment-internal/sidecar-libs/src/metrics/metrics-reporter";

import { getAPIClient } from "@augment-internal/sidecar-libs/src/client-interfaces/api-client";
import { ToolUseRequestEvent } from "../client-interfaces/api-client-types";
import { msecToTimestamp } from "../utils/time";

export class ToolUseRequestEventReporter extends MetricsReporter<ToolUseRequestEvent> {
  public static defaultMaxRecords = 10000;
  public static defaultBatchSize = 1000;
  public static defaultUploadMsec = 10000;

  constructor(maxRecords?: number, uploadMs?: number, batchSize?: number) {
    super(
      "ToolUseRequestEventReporter",
      maxRecords ?? ToolUseRequestEventReporter.defaultMaxRecords,
      uploadMs ?? ToolUseRequestEventReporter.defaultUploadMsec,
      batchSize ?? ToolUseRequestEventReporter.defaultBatchSize,
    );
  }

  // reportEvent reports a user tool use event.
  public reportEvent(
    requestId: string,
    toolName: string,
    toolUseId: string,
    toolInput: Record<string, unknown>,
    toolOutputIsError: boolean,
    toolRunDurationMs: number,
    isMcpTool: boolean,
    conversationId: string,
    chatHistoryLength: number,
    toolRequestId?: string,
    toolOutputLen?: number,
  ): void {
    const [eventTimeSec, eventTimeNsec] = msecToTimestamp(Date.now());
    this.report({
      requestId: requestId,
      toolName: toolName,
      toolUseId: toolUseId,
      toolInput: toolInput,
      toolOutputIsError: toolOutputIsError,
      toolRunDurationMs: toolRunDurationMs,
      isMcpTool: isMcpTool,
      conversationId: conversationId,
      chatHistoryLength: chatHistoryLength,
      eventTimeSec: eventTimeSec,
      eventTimeNsec: eventTimeNsec,
      toolRequestId: toolRequestId,
      toolOutputLen: toolOutputLen,
    });
  }

  protected performUpload(batch: ToolUseRequestEvent[]): Promise<void> {
    return getAPIClient().logToolUseRequestEvent(batch);
  }
}
