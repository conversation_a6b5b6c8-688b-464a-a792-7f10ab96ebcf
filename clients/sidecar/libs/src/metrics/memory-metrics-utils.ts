/**
 * Shared utility functions for emitting memory usage metrics
 */

import { getAgentSessionEventReporter } from "./agent-session-event-reporter";
import {
  AgentSessionEventName,
  MemoryAction,
  MemoryActionTrigger,
  MemoryUsageData,
} from "./types";

/**
 * Options for emitting memory usage metrics
 */
export interface EmitMemoryMetricOptions {
  /** The memory action being performed */
  action: MemoryAction;
  /** Who triggered the action */
  triggeredBy: MemoryActionTrigger;
  /** The conversation ID (optional - will be filled by reporter if not provided) */
  conversationId?: string;
  /** The memory ID (optional) */
  memoryId?: string;
  /** The memory state (optional) */
  memoryState?: string;
  /** The memory version (optional) */
  memoryVersion?: string;
}

/**
 * Emits a memory usage metric with flexible options
 * This is the unified function that can be used by both UI components and agent tools
 *
 * @param options - The options for emitting the metric
 */
export function emitMemoryMetric(options: EmitMemoryMetricOptions): void {
  try {
    const {
      action,
      triggeredBy,
      conversationId = "",
      memoryId,
      memoryState,
      memoryVersion,
    } = options;

    const memoryUsageData: MemoryUsageData = {
      action,
      memoryId,
      triggeredBy,
      memoryState,
      memoryVersion,
    };

    getAgentSessionEventReporter().reportEvent({
      eventName: AgentSessionEventName.memoryUsage,
      conversationId,
      eventData: {
        memoryUsageData,
      },
    });
  } catch (error) {
    // Don't let metrics reporting break the actual functionality
    // Silently fail - metrics are not critical to functionality
  }
}

/**
 * Convenience function for emitting memory usage metrics from agent tools
 * Always sets triggeredBy to agent
 *
 * @param action - The memory action being performed
 * @param conversationId - The conversation ID (optional, defaults to empty string)
 * @param memoryId - The memory ID (optional)
 * @param memoryState - The memory state (optional)
 * @param memoryVersion - The memory version (optional)
 */
export function emitAgentMemoryMetric(
  action: MemoryAction,
  conversationId?: string,
  memoryId?: string,
  memoryState?: string,
  memoryVersion?: string,
): void {
  emitMemoryMetric({
    action,
    triggeredBy: MemoryActionTrigger.agent,
    conversationId,
    memoryId,
    memoryState,
    memoryVersion,
  });
}

/**
 * Convenience function for emitting memory usage metrics from user actions
 * Always sets triggeredBy to user
 *
 * @param action - The memory action being performed
 * @param conversationId - The conversation ID (optional, defaults to empty string)
 * @param memoryId - The memory ID (optional)
 * @param memoryState - The memory state (optional)
 * @param memoryVersion - The memory version (optional)
 */
export function emitUserMemoryMetric(
  action: MemoryAction,
  conversationId?: string,
  memoryId?: string,
  memoryState?: string,
  memoryVersion?: string,
): void {
  emitMemoryMetric({
    action,
    triggeredBy: MemoryActionTrigger.user,
    conversationId,
    memoryId,
    memoryState,
    memoryVersion,
  });
}
