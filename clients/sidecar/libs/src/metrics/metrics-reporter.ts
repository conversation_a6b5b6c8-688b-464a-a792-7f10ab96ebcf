import { <PERSON><PERSON><PERSON>er } from "../ring-buffer";
import { retryWithBackoff } from "../utils/promise-utils";
import { type AugmentLogger, getLogger } from "../logging";

/**
 * MetricsReporter is a class for batching metrics
 * for uploading at regular intervals.
 */
export abstract class MetricsReporter<T> {
  /**
   * MetricsReporter uploads metrics in the background. It stores metrics reported by
   * `report` in a buffer (`_store`) and uploads them every `_uploadMsec`
   * milliseconds. The store has a maximum size (`_maxRecords`). If the periodic uploads
   * fail, or are not able to keep up with the rate of newly reported metrics, the store
   * may eventually fill up. If this happens, old metrics will be dropped to make space for new metrics.
   *
   * Each upload operation attempts to upload the entire contents of the store, but it does
   * so in batches of `batchSize` records.
   */

  #logger: AugmentLogger; // using # for private fields because typescript will not allow a logger in extending classes otherwise
  private _store: RingBuffer<T>;
  private _uploadIntervalId: NodeJS.Timeout | undefined = undefined;
  private _currentUploadPromise: Promise<void> | undefined;
  constructor(
    loggerName: string,
    maxRecords: number,
    private _uploadMsec: number,
    private _uploadBatchSize: number,
  ) {
    this._store = new RingBuffer<T>(maxRecords);
    this.#logger = getLogger(loggerName);
  }

  public report(item: T): void {
    this._store.addItem(item);
  }

  public get uploadEnabled(): boolean {
    return this._uploadIntervalId !== undefined;
  }

  // enableReporting begins periodic uploading of metrics to the back end. If uploading is
  // already enabled, it does nothing.
  public enableUpload(): void {
    if (this.uploadEnabled) {
      return;
    }

    this._uploadIntervalId = setInterval(() => {
      if (this._currentUploadPromise !== undefined) {
        // Don't start a new upload if the previous one hasn't completed.
        return;
      }

      void (async () => {
        try {
          this._currentUploadPromise = this._doUpload();
          await this._currentUploadPromise;
        } catch (error: unknown) {
          // Critical: Never let background upload crash the process
          const errorMessage =
            error instanceof Error ? error.message : String(error);
          const errorStack = error instanceof Error ? error.stack : "";
          this.#logger.error(
            `Critical error in background metrics upload (will continue): ${errorMessage} ${errorStack}`,
          );
        } finally {
          this._currentUploadPromise = undefined;
        }
      })();
    }, this._uploadMsec);
  }

  /**
   * Immediately performs the metric upload now in case the app closes before an interval upload is ready.
   * This method is crash-safe and will not throw exceptions.
   */
  public async uploadNow(): Promise<void> {
    try {
      await this._doUpload();
    } catch (error: unknown) {
      // Critical: Never let manual upload crash the caller
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      const errorStack = error instanceof Error ? error.stack : "";
      this.#logger.error(
        `Critical error in manual metrics upload (will continue): ${errorMessage} ${errorStack}`,
      );
    }
  }

  // _doUpload batches up the current store values and calls
  // performUpload() to upload them.
  private async _doUpload(): Promise<void> {
    if (this._store.length === 0) {
      return;
    }

    const toUpload = this._store.slice();
    this._store.clear();

    for (
      let startIdx = 0;
      startIdx < toUpload.length;
      startIdx += this._uploadBatchSize
    ) {
      const batch = toUpload.slice(startIdx, startIdx + this._uploadBatchSize);
      await retryWithBackoff<void>(async () => {
        if (!this.uploadEnabled) {
          return;
        }
        try {
          this.#logger.debug(`Uploading ${batch.length} metric(s)`);
          return await this.performUpload(batch);
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
        } catch (e: any) {
          this.#logger.error(
            `Error uploading metrics: ${e} ${e instanceof Error ? e.stack : ""}`,
          );
          throw e;
        }
      }, this.#logger);
    }
  }

  // disableUpload stops uploading of metrics to the back end. If uploading is not enabled,
  // it does nothing.
  public disableUpload(): void {
    clearInterval(this._uploadIntervalId);
    this._uploadIntervalId = undefined;
  }

  public dispose() {
    this.disableUpload();
  }

  protected abstract performUpload(batch: T[]): Promise<void>;
}
