/**
 * Shared utility functions for emitting task list metrics
 */

import { getAgentSessionEventReporter } from "./agent-session-event-reporter";
import {
  AgentSessionEventName,
  TaskListAction,
  TaskListActionTrigger,
  TaskListUsageData,
} from "./types";
import type { HydratedTask } from "../agent/task/task-types";

/**
 * Counts the total number of tasks in a hydrated task tree
 * @param task - The root task to count from
 * @returns Total number of tasks (excluding the root task itself)
 */
export function countTasksInTree(task: HydratedTask | undefined): number {
  if (!task) {
    return 0;
  }

  let count = 0;

  // Count direct subtasks
  if (task.subTasks && task.subTasks.length > 0) {
    count += task.subTasks.length;

    // If we have hydrated subtask data, recursively count their subtasks
    if (task.subTasksData) {
      for (const subTask of task.subTasksData) {
        count += countTasksInTree(subTask);
      }
    }
  }

  return count;
}

/**
 * Options for emitting task list metrics
 */
export interface EmitTaskListMetricOptions {
  /** The task list action being performed */
  action: TaskListAction;
  /** Who triggered the action */
  triggeredBy: TaskListActionTrigger;
  /** The conversation ID (optional - will be filled by reporter if not provided) */
  conversationId?: string;
  /** Total number of tasks (optional - will be calculated by reporter if not provided) */
  totalTasksCount?: number;
}

/**
 * Emits a task list metric with flexible options
 * This is the unified function that can be used by both UI components and agent tools
 *
 * @param options - The options for emitting the metric
 */
export function emitTaskListMetric(options: EmitTaskListMetricOptions): void {
  try {
    const {
      action,
      triggeredBy,
      conversationId = "",
      totalTasksCount = 0,
    } = options;

    const taskListUsageData: TaskListUsageData = {
      action,
      totalTasksCount,
      triggeredBy,
    };

    getAgentSessionEventReporter().reportEvent({
      eventName: AgentSessionEventName.taskListUsage,
      conversationId,
      eventData: {
        taskListUsageData,
      },
    });
  } catch (error) {
    // Don't let metrics reporting break the actual functionality
    // Silently fail - metrics are not critical to functionality
  }
}

/**
 * Convenience function for emitting task list metrics from agent tools
 * Always sets triggeredBy to agent
 *
 * @param action - The task list action being performed
 * @param conversationId - The conversation ID (optional, defaults to empty string)
 * @param totalTasksCount - Optional total number of tasks (if available)
 */
export function emitAgentTaskListMetric(
  action: TaskListAction,
  conversationId?: string,
  totalTasksCount?: number,
): void {
  emitTaskListMetric({
    action,
    triggeredBy: TaskListActionTrigger.agent,
    conversationId,
    totalTasksCount,
  });
}
