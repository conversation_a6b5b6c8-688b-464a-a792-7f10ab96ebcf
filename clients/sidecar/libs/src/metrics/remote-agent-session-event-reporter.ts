import { MetricsReporter } from "@augment-internal/sidecar-libs/src/metrics/metrics-reporter";
import { RemoteAgentSessionEventData } from "@augment-internal/sidecar-libs/src/metrics/types";
import { msecToTimestamp } from "@augment-internal/sidecar-libs/src/utils/time";

import { getAPIClient } from "../client-interfaces/api-client";
import { RemoteAgentSessionEvent } from "../client-interfaces/api-client-types";

export class RemoteAgentSessionEventReporter extends MetricsReporter<RemoteAgentSessionEvent> {
  private static maxRecords = 10000;
  private static batchSize = 1000;
  private static uploadMsec = 10000;

  private static instance: RemoteAgentSessionEventReporter | null = null;

  /**
   * Gets the singleton instance of AgentSessionEventReporter
   * @param featureFlagManager The feature flag manager
   * @param maxRecords Optional maximum number of records
   * @param uploadMs Optional upload interval in milliseconds
   * @param batchSize Optional batch size
   * @returns The singleton instance of AgentSessionEventReporter
   */
  public static getInstance(): RemoteAgentSessionEventReporter {
    if (!RemoteAgentSessionEventReporter.instance) {
      RemoteAgentSessionEventReporter.instance =
        new RemoteAgentSessionEventReporter();
      RemoteAgentSessionEventReporter.instance.enableUpload();
    }
    return RemoteAgentSessionEventReporter.instance;
  }

  public static reset(): void {
    if (RemoteAgentSessionEventReporter.instance) {
      RemoteAgentSessionEventReporter.instance.dispose();
      RemoteAgentSessionEventReporter.instance = null;
    }
  }

  private constructor() {
    super(
      "RemoteAgentSessionEventReporter",
      RemoteAgentSessionEventReporter.maxRecords,
      RemoteAgentSessionEventReporter.uploadMsec,
      RemoteAgentSessionEventReporter.batchSize,
    );
  }

  // reportEvent reports a user agent event.
  public reportEvent(eventData: RemoteAgentSessionEventData): void {
    const [eventTimeSec, eventTimeNsec] = msecToTimestamp(Date.now());

    let eventDataPayload = undefined;

    if (eventData.eventData?.remoteAgentSetupData) {
      eventDataPayload = {
        remote_agent_setup_data: {
          used_generated_setup_script:
            eventData.eventData.remoteAgentSetupData.usedGeneratedSetupScript,
          setup_state: eventData.eventData.remoteAgentSetupData.setupState,
        },
      };
    }
    if (eventData.eventData?.setupScriptData) {
      eventDataPayload = {
        setup_script_data: {
          num_tries: eventData.eventData.setupScriptData.numTries,
          num_messages_sent:
            eventData.eventData.setupScriptData.numMessagesSent,
          generation_time_ms:
            eventData.eventData.setupScriptData.generationTimeMs,
          manual_modification:
            eventData.eventData.setupScriptData.manualModification,
        },
      };
    }
    if (eventData.eventData?.sshInteractionData) {
      eventDataPayload = {
        ssh_interaction_data: {
          interaction_type:
            eventData.eventData.sshInteractionData.interactionType,
        },
      };
    }
    if (eventData.eventData?.notificationBellData) {
      eventDataPayload = {
        notification_bell_data: {
          bell_state: eventData.eventData.notificationBellData.bellState,
        },
      };
    }
    if (eventData.eventData?.diffPanelData) {
      eventDataPayload = {
        diff_panel_data: {
          loading_time_ms: eventData.eventData.diffPanelData.loadingTimeMs,
          applied: eventData.eventData.diffPanelData.applied,
        },
      };
    }
    if (eventData.eventData?.setupPageOpened) {
      eventDataPayload = {
        setup_page_opened: {},
      };
    }
    if (eventData.eventData?.githubAPIFailure) {
      eventDataPayload = {
        github_api_failure: {
          error_code: eventData.eventData.githubAPIFailure.errorCode,
        },
      };
    }
    if (eventData.eventData?.remoteAgentCreated) {
      eventDataPayload = {
        remote_agent_created: {
          changed_repo: eventData.eventData.remoteAgentCreated.changedRepo,
          changed_branch: eventData.eventData.remoteAgentCreated.changedBranch,
        },
      };
    }
    if (eventData.eventData?.changesAppliedData) {
      eventDataPayload = {
        changes_applied_data: {},
      };
    }
    if (eventData.eventData?.createdPRData) {
      eventDataPayload = {
        created_pr_data: {},
      };
    }
    if (eventData.eventData?.modeSelectorData) {
      eventDataPayload = {
        mode_selector_data: {
          action: eventData.eventData.modeSelectorData.action,
          mode: eventData.eventData.modeSelectorData.mode,
          source_control: eventData.eventData.modeSelectorData.sourceControl,
        },
      };
    }
    if (eventData.eventData?.remoteAgentSetupWindowData) {
      eventDataPayload = {
        remote_agent_setup_window_data: {
          action: eventData.eventData.remoteAgentSetupWindowData.action,
          repo_hash: eventData.eventData.remoteAgentSetupWindowData.repoHash,
          branch_hash: eventData.eventData.remoteAgentSetupWindowData.branchHash,
          has_setup_script_selected: eventData.eventData.remoteAgentSetupWindowData.hasSetupScriptSelected,
          github_integration_enabled: eventData.eventData.remoteAgentSetupWindowData.githubIntegrationEnabled,
          num_repos_available: eventData.eventData.remoteAgentSetupWindowData.numReposAvailable,
          source_control: eventData.eventData.remoteAgentSetupWindowData.sourceControl,
        },
      };
    }
    if (eventData.eventData?.remoteAgentThreadListData) {
      eventDataPayload = {
        remote_agent_thread_list_data: {
          action: eventData.eventData.remoteAgentThreadListData.action,
          num_agents_in_list: eventData.eventData.remoteAgentThreadListData.numAgentsInList,
          source_control: eventData.eventData.remoteAgentThreadListData.sourceControl,
        },
      };
    }
    if (eventData.eventData?.remoteAgentNewThreadButtonData) {
      eventDataPayload = {
        remote_agent_new_thread_button_data: {
          action: eventData.eventData.remoteAgentNewThreadButtonData.action,
          thread_type: eventData.eventData.remoteAgentNewThreadButtonData.threadType,
          source_control: eventData.eventData.remoteAgentNewThreadButtonData.sourceControl,
        },
      };
    }

    this.report({
      event_time_sec: eventTimeSec,
      event_time_nsec: eventTimeNsec,
      event_name: eventData.eventName,
      remote_agent_id: eventData.remoteAgentId,
      event_data: eventDataPayload,
    });
  }

  protected performUpload(batch: RemoteAgentSessionEvent[]): Promise<void> {
    return getAPIClient().logRemoteAgentSessionEvent(batch);
  }
}

/**
 * Gets the singleton instance of RemoteAgentSessionEventReporter
 * @param featureFlagManager The feature flag manager
 * @returns The singleton instance of RemoteAgentSessionEventReporter
 */
export function getRemoteAgentSessionEventReporter(): RemoteAgentSessionEventReporter {
  return RemoteAgentSessionEventReporter.getInstance();
}

export function resetAgentSessionEventReporter() {
  RemoteAgentSessionEventReporter.reset();
}
