export type Listen<T> = (v: T, oldV: T) => void;
type VarArgs<T> = T extends [infer O, ...infer R]
  ? [Observable<O>, ...VarArgs<R>]
  : [];
/**
 * A function that takes a value and returns a boolean.
 */
type Predicate<T> = (v: T) => boolean;
type AnyFn = (...args: any[]) => any;

/** Boxes a value and notifies listeners when the value changes. */
export class Observable<T> {
  /**
   * Creates an Observable, that runs a function anytime a dependent observable changes.
   * Keeping its value in sync with the dependents.   This can help performance as the function only runs on change,
   * not on every set.  More importantly it makes the relationship between observables explicit.
   * <code>
   * const obs1 = new Observable('test);
   * const obs2 = new Observable(2);
   *
   * const obs = Observable.watch((a: string, b: number) => a + b, obs1, obs2);
   * console.log(obs.value) // 'test2'
   *
   * obs2.value = 4;
   * console.log(obs.value) // 'test4'
   * </code>
   *
   *
   * @param fn - Takes Observable values and returns a new value.
   * @param observables - Observables to watch
   * @returns - Observable
   */
  static watch<Fn extends AnyFn>(
    fn: Fn,
    ...observables: VarArgs<Parameters<Fn>>
  ): Observable<ReturnType<Fn>> {
    const run = (): ReturnType<Fn> => {
      // eslint-disable-next-line @typescript-eslint/no-unsafe-return
      return fn(...(observables.map((v) => v.value) as Parameters<Fn>));
    };

    const obs = new Observable<ReturnType<Fn>>(run());

    const unlisten = observables.map((o) =>
      o.listen(() => {
        obs.value = run();
      }),
    );
    const dispose = obs.dispose;
    obs.dispose = () => {
      dispose();
      unlisten.forEach((v) => v());
    };
    return obs;
  }

  constructor(
    private _value: T,
    private readonly _equalityFn: (a: T, b: T) => boolean = (a, b) => a === b,
    private _listeners: Listen<T>[] = [],
  ) {}

  dispose = () => {
    this._listeners = [];
  };

  listen(fn: Listen<T>, fire = false) {
    if (fire) {
      fn(this._value, this._value);
    }
    this._listeners.push(fn);
    return () => {
      this._listeners = this._listeners.filter((v) => v !== fn);
    };
  }

  get value() {
    return this._value;
  }

  set value(v: T) {
    if (this._equalityFn(v, this._value)) {
      return;
    }

    const oldV = this._value;
    this._value = v;

    for (const cb of this._listeners) {
      cb(v, oldV);
    }
  }

  /**
   * Wait until the value satisfies the predicate.
   *
   * @param predicate The predicate to wait for.
   * @param timeoutMs The optional timeout in milliseconds.
   * @returns A promise that resolves when the value satisfies the predicate.
   */
  waitUntil(predicate: Predicate<T>, timeoutMs?: number): Promise<T> {
    return new Promise<T>((resolve, reject) => {
      let unlisten: (() => void) | undefined = undefined;
      const timeout =
        timeoutMs !== undefined &&
        setTimeout(() => {
          unlisten?.();
          reject(new Error("Timeout exceeded."));
        }, timeoutMs);
      unlisten = this.listen((v) => {
        if (predicate(v)) {
          if (timeout) {
            clearTimeout(timeout);
          }
          unlisten?.();
          resolve(v);
        }
      }, true);
    });
  }
}
