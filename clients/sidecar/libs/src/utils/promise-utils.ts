import { AugmentLogger } from "../logging";
import { APIError } from "@augment-internal/sidecar-libs/src/exceptions";

export type BackoffParams = {
  initialMS: number;
  mult: number;
  maxMS: number;
  maxTries?: number;
  maxTotalMs?: number;
  canRetry?: (e: unknown) => boolean;
};

const defaultBackoffParams: BackoffParams = {
  initialMS: 100,
  mult: 2,
  maxMS: 30000,
};

/**
 * delayMs returns a promise that resolves after the given number of ms.
 */
export function delayMs(delayMs: number): Promise<void> {
  if (delayMs === 0) {
    return Promise.resolve();
  }
  return new Promise((resolve) => {
    setTimeout(resolve, delayMs);
  });
}

/**
 * retryWithBackoff returns a promise that runs the given `fn`.
 * - If fn succeeds, the Promise resolves to its returned value.
 * - If fn fails with a non-retriable error, the Promise rejects with that error.
 * - If fn fails with a retriable error, the Promise will retry with backoff as specified
 *   by the given BackoffParams.
 */
export async function retryWithBackoff<T>(
  fn: () => Promise<T>,
  logger: AugmentLogger,
  backoffParams: BackoffParams = defaultBackoffParams,
): Promise<T> {
  let backoffMs = 0;

  const startTime =
    backoffParams.maxTotalMs !== undefined ? Date.now() : undefined;
  const canRetryFn = backoffParams.canRetry
    ? backoffParams.canRetry
    : (e: unknown) => APIError.isRetriableAPIError(e);
  for (let tries = 0; ; tries++) {
    try {
      const ret = await fn();
      if (tries > 0) {
        logger.info(`Operation succeeded after ${tries} transient failures`);
      }
      return ret;
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (e: any) {
      if (!canRetryFn(e)) {
        throw e;
      }

      // Check if we have exceeded the max number of retries.
      const currTryCount: number = tries + 1;
      if (
        backoffParams.maxTries !== undefined &&
        currTryCount >= backoffParams.maxTries
      ) {
        throw e;
      }

      // Compute the back-off we will use next time around.
      if (backoffMs === 0) {
        backoffMs = backoffParams.initialMS;
      } else {
        backoffMs = Math.min(
          backoffMs * backoffParams.mult,
          backoffParams.maxMS,
        );
      }
      logger.info(
        `Operation failed with error ${e}, retrying in ${backoffMs} ms; retries = ${tries}`,
      );

      // Check if the backoff delay will exceed total time. If it will, don't issue another request.
      if (
        backoffParams.maxTotalMs !== undefined &&
        startTime !== undefined &&
        Date.now() - startTime + backoffMs > backoffParams.maxTotalMs
      ) {
        throw e;
      }
      await delayMs(backoffMs);
    }
  }
}

/**
 * DeferredPromise is a promise that can be resolved or rejected from the outside.
 */
export class DeferredPromise<T> implements Promise<T> {
  private promise: Promise<T>;
  public resolve!: (value: T | PromiseLike<T>) => void;
  public reject!: (reason?: any) => void;

  constructor() {
    this.promise = new Promise<T>((resolve, reject) => {
      this.resolve = resolve;
      this.reject = reject;
    });
  }

  resolveWith(value: T | PromiseLike<T>): void {
    this.resolve(value);
  }

  rejectWith(reason?: any): void {
    this.reject(reason);
  }

  then<TResult1 = T, TResult2 = never>(
    onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | null,
    onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | null,
  ): Promise<TResult1 | TResult2> {
    return this.promise.then(onfulfilled, onrejected);
  }

  catch<TResult = never>(
    onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | null,
  ): Promise<T | TResult> {
    return this.promise.catch(onrejected);
  }

  finally(onfinally?: (() => void) | null): Promise<T> {
    return this.promise.finally(onfinally);
  }

  get [Symbol.toStringTag](): string {
    return "Promise";
  }
}

/**
 * Execute an async function with a timeout
 *
 * node-fetch and native fetch both seem to have issue respecting timeouts.  This is to enforce it.
 */
export async function withTimeout<T>(
  promise: Promise<T>,
  timeoutMs: number,
): Promise<T> {
  return await new Promise<T>((resolve, reject) => {
    const timeout = setTimeout(
      () => reject(new Error("Execution aborted due to timeout.")),
      timeoutMs,
    );
    promise.finally(() => clearTimeout(timeout)).then(resolve, reject);
  });
}
