import { IPluginFileStore } from "../client-interfaces/plugin-file-store";
import { v4 as uuidv4 } from "uuid";
import { getLogger } from "../logging";
import { LocalToolType } from "../tools/tool-types";

/**
 * Types of content that can be truncated
 */
export enum TruncatedContentType {
  ToolOutput = "tool-output",
  UserMessage = "user-message",
  ConversationHistory = "conversation-history",
}

/**
 * Metadata about truncated content
 */
export interface TruncatedContentMetadata {
  /** Type of the truncated content */
  contentType: TruncatedContentType;
  /** Total size of the untruncated content in bytes */
  totalSize: number;
  /** Total number of lines in the untruncated content */
  totalLines: number;
  /**
   * Range of lines shown in the truncated content (1-based, inclusive)
   * Can be either [start, end] for a single range or [start1, end1, start2, end2] for two ranges
   */
  shownRange: [number, number] | [number, number, number, number];
  /** Reference ID for the truncated content */
  referenceId: string;
  /** Associated tool use ID if applicable */
  toolUseId?: string;
  /** Associated request ID if applicable */
  requestId?: string;
  /** Associated conversation ID if applicable */
  conversationId?: string;
  /** Tool type that generated the content being truncated */
  toolType?: LocalToolType;
  /** Original command that generated the content (for launch-process tools) */
  originalCommand?: string;
  /** Timestamp when the content was stored */
  timestamp: number;
}

/**
 * Result of a search in untruncated content
 */
export interface SearchResult {
  /** The matching lines with context */
  content: string;
  /** The line numbers of the matching content (1-based, inclusive) */
  matchedRange: [number, number];
  /** Total number of matches found */
  totalMatches: number;
}

/**
 * Manager for storing and retrieving untruncated content
 */
export class UntruncatedContentManager {
  private static readonly ASSET_PREFIX = "untruncated";
  private readonly _logger = getLogger("UntruncatedContentManager");

  constructor(private readonly _fileStore: IPluginFileStore) {}

  /**
   * Store untruncated content and return metadata
   *
   * @param content The full untruncated content
   * @param contentType The type of content being stored
   * @param shownRange The range of lines shown in the truncated version (1-based, inclusive)
   * @param toolUseId Optional tool use ID
   * @param requestId Optional request ID
   * @param conversationId Optional conversation ID
   * @param toolType Optional tool type that generated the content
   * @param originalCommand Optional original command that generated the content (for launch-process tools)
   * @returns Metadata about the stored content
   */
  public async storeUntruncatedContent(
    content: string,
    contentType: TruncatedContentType,
    shownRange: [number, number] | [number, number, number, number],
    toolUseId?: string,
    requestId?: string,
    conversationId?: string,
    toolType?: LocalToolType,
    originalCommand?: string,
  ): Promise<TruncatedContentMetadata> {
    const referenceId = uuidv4();
    const lines = content.split("\n");
    const totalLines = lines.length;
    const totalSize = content.length;

    const metadata: TruncatedContentMetadata = {
      contentType,
      totalSize,
      totalLines,
      shownRange,
      referenceId,
      toolUseId,
      requestId,
      conversationId,
      toolType,
      originalCommand,
      timestamp: Date.now(),
    };

    // Store the content
    const assetPath = this.getAssetPath(referenceId);
    await this._fileStore.saveAsset(
      assetPath,
      new TextEncoder().encode(content),
    );

    // Store the metadata
    const metadataPath = this.getMetadataPath(referenceId);
    await this._fileStore.saveAsset(
      metadataPath,
      new TextEncoder().encode(JSON.stringify(metadata)),
    );

    // Create pointer files for each ID type
    if (conversationId) {
      const conversationPath = `${UntruncatedContentManager.ASSET_PREFIX}/by-conversationId/${conversationId}/${referenceId}`;
      await this._fileStore.saveAsset(conversationPath, new Uint8Array(0));
    }

    if (requestId) {
      const requestPath = `${UntruncatedContentManager.ASSET_PREFIX}/by-requestId/${requestId}/${referenceId}`;
      await this._fileStore.saveAsset(requestPath, new Uint8Array(0));
    }

    if (toolUseId) {
      const toolPath = `${UntruncatedContentManager.ASSET_PREFIX}/by-toolUseId/${toolUseId}/${referenceId}`;
      await this._fileStore.saveAsset(toolPath, new Uint8Array(0));
    }

    return metadata;
  }

  /**
   * Get a range of content from untruncated content
   *
   * @param referenceId The reference ID of the content
   * @param startLine The starting line (1-based, inclusive)
   * @param endLine The ending line (1-based, inclusive)
   * @returns The requested range of content or undefined if not found
   */
  public async getContentRange(
    referenceId: string,
    startLine: number,
    endLine: number,
  ): Promise<string | undefined> {
    const content = await this.getFullContent(referenceId);
    if (!content) {
      return undefined;
    }

    const lines = content.split("\n");
    const totalLines = lines.length;

    // Validate and adjust range
    startLine = Math.max(1, Math.min(startLine, totalLines));
    endLine = Math.max(startLine, Math.min(endLine, totalLines));

    // Return the requested range (adjusting for 0-based array)
    return lines.slice(startLine - 1, endLine).join("\n");
  }

  /**
   * Search for a term in untruncated content
   *
   * @param referenceId The reference ID of the content
   * @param searchTerm The term to search for
   * @param contextLines Number of context lines to include before and after matches
   * @returns Search results or undefined if content not found
   */
  public async searchContent(
    referenceId: string,
    searchTerm: string,
    contextLines: number = 2,
  ): Promise<SearchResult | undefined> {
    const content = await this.getFullContent(referenceId);
    if (!content) {
      return undefined;
    }

    const lines = content.split("\n");
    const matches: number[] = [];

    // Find all matching lines
    for (let i = 0; i < lines.length; i++) {
      if (lines[i].toLowerCase().includes(searchTerm.toLowerCase())) {
        matches.push(i);
      }
    }

    if (matches.length === 0) {
      return {
        content: `No matches found for "${searchTerm}"`,
        matchedRange: [0, 0],
        totalMatches: 0,
      };
    }

    // Group nearby matches to avoid duplicating context
    const groups: [number, number][] = [];
    let currentGroup: [number, number] | null = null;

    for (const match of matches) {
      if (currentGroup === null) {
        currentGroup = [match, match];
      } else if (match <= currentGroup[1] + contextLines * 2) {
        // Extend the current group
        currentGroup[1] = match;
      } else {
        // Start a new group
        groups.push(currentGroup);
        currentGroup = [match, match];
      }
    }

    if (currentGroup !== null) {
      groups.push(currentGroup);
    }

    // Build the result with context
    const resultLines: string[] = [];
    let firstMatchLine = Number.MAX_SAFE_INTEGER;
    let lastMatchLine = 0;

    for (const [groupStart, groupEnd] of groups) {
      const contextStart = Math.max(0, groupStart - contextLines);
      const contextEnd = Math.min(lines.length - 1, groupEnd + contextLines);

      firstMatchLine = Math.min(firstMatchLine, contextStart + 1);
      lastMatchLine = Math.max(lastMatchLine, contextEnd + 1);

      // Add a separator if this isn't the first group
      if (resultLines.length > 0) {
        resultLines.push("...");
      }

      // Add lines with context
      for (let i = contextStart; i <= contextEnd; i++) {
        const prefix = matches.includes(i) ? ">> " : "   ";
        resultLines.push(`${prefix}${i + 1}: ${lines[i]}`);
      }
    }

    return {
      content: resultLines.join("\n"),
      matchedRange: [firstMatchLine, lastMatchLine],
      totalMatches: matches.length,
    };
  }

  /**
   * Get metadata for untruncated content
   *
   * @param referenceId The reference ID of the content
   * @returns The metadata or undefined if not found
   */
  public async getMetadata(
    referenceId: string,
  ): Promise<TruncatedContentMetadata | undefined> {
    const metadataPath = this.getMetadataPath(referenceId);
    const metadataBytes = await this._fileStore.loadAsset(metadataPath);

    if (!metadataBytes) {
      return undefined;
    }

    try {
      const metadataJson = new TextDecoder().decode(metadataBytes);
      return JSON.parse(metadataJson) as TruncatedContentMetadata;
    } catch (error) {
      this._logger.debug("Failed to parse metadata:", error);
      return undefined;
    }
  }

  /**
   * Clean up all untruncated content associated with a conversation
   *
   * @param conversationId The ID of the conversation to clean up
   * @returns The number of assets deleted
   */
  public async cleanupConversationContent(
    conversationId: string,
  ): Promise<number> {
    return this._cleanupContentByField(
      "conversationId",
      conversationId,
      `conversation ${conversationId}`,
    );
  }

  /**
   * Clean up all untruncated content associated with a request
   *
   * @param requestId The ID of the request to clean up
   * @returns The number of assets deleted
   */
  public async cleanupRequestContent(requestId: string): Promise<number> {
    return this._cleanupContentByField(
      "requestId",
      requestId,
      `request ${requestId}`,
    );
  }

  /**
   * Clean up all untruncated content associated with a tool use
   *
   * @param toolUseId The ID of the tool use to clean up
   * @returns The number of assets deleted
   */
  public async cleanupToolUseContent(toolUseId: string): Promise<number> {
    return this._cleanupContentByField(
      "toolUseId",
      toolUseId,
      `tool use ${toolUseId}`,
    );
  }

  /**
   * Generic method to clean up content by a specific metadata field
   *
   * @param fieldName The name of the field to match in metadata
   * @param fieldValue The value to match
   * @param logDescription Description to use in log messages
   * @returns The number of assets deleted
   */
  private async _cleanupContentByField(
    fieldName: keyof TruncatedContentMetadata,
    fieldValue: string,
    logDescription: string,
  ): Promise<number> {
    let deletedCount = 0;
    try {
      // Get pointer files directly from the index directory
      const indexPrefix = `${UntruncatedContentManager.ASSET_PREFIX}/by-${fieldName}/${fieldValue}/`;
      const pointerFiles = await this._fileStore.listAssets(indexPrefix);

      for (const pointerPath of pointerFiles) {
        try {
          // Extract referenceId from the path
          const referenceId = pointerPath.split("/").pop();
          if (!referenceId) continue;

          // Delete the content file
          const contentPath = this.getAssetPath(referenceId);
          await this._fileStore.deleteAsset(contentPath);

          // Delete the metadata file
          const metadataPath = this.getMetadataPath(referenceId);
          await this._fileStore.deleteAsset(metadataPath);

          // Delete pointer files for this referenceId
          // We need to check all possible index directories

          // Get the metadata to check for other IDs
          const metadata = await this.getMetadata(referenceId);
          if (metadata) {
            // Handle conversationId pointer
            if (fieldName !== "conversationId" && metadata.conversationId) {
              const conversationPointerPath = `${UntruncatedContentManager.ASSET_PREFIX}/by-conversationId/${metadata.conversationId}/${referenceId}`;
              await this._fileStore.deleteAsset(conversationPointerPath);
            }

            // Handle requestId pointer
            if (fieldName !== "requestId" && metadata.requestId) {
              const requestPointerPath = `${UntruncatedContentManager.ASSET_PREFIX}/by-requestId/${metadata.requestId}/${referenceId}`;
              await this._fileStore.deleteAsset(requestPointerPath);
            }

            // Handle toolUseId pointer
            if (fieldName !== "toolUseId" && metadata.toolUseId) {
              const toolPointerPath = `${UntruncatedContentManager.ASSET_PREFIX}/by-toolUseId/${metadata.toolUseId}/${referenceId}`;
              await this._fileStore.deleteAsset(toolPointerPath);
            }
          }

          // Delete this pointer file
          await this._fileStore.deleteAsset(pointerPath);

          deletedCount++;
        } catch (error) {
          this._logger.error(
            `Error processing pointer file ${pointerPath}:`,
            error,
          );
          // Continue with other files even if one fails
        }
      }

      // Try to clean up the empty index directory
      if (deletedCount > 0) {
        try {
          // Check if the directory is now empty
          const remainingFiles = await this._fileStore.listAssets(indexPrefix);
          if (remainingFiles.length === 0) {
            // Directory is empty, so try to remove it
            const indexDir = `${UntruncatedContentManager.ASSET_PREFIX}/by-${fieldName}/${fieldValue}`;
            await this._fileStore.deleteAsset(indexDir);
          }
        } catch (error: any) {
          // Ignore errors when trying to clean up directories
          this._logger.debug(
            // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
            `Could not clean up index directory: ${error.message}`,
          );
        }

        this._logger.debug(
          `Cleaned up ${deletedCount} untruncated content assets for ${logDescription}`,
        );
      }
      return deletedCount;
    } catch (error) {
      this._logger.error(
        `Failed to cleanup content for ${logDescription}:`,
        error,
      );
      return deletedCount;
    }
  }

  /**
   * Get the full untruncated content
   *
   * @param referenceId The reference ID of the content
   * @returns The full content or undefined if not found
   */
  private async getFullContent(
    referenceId: string,
  ): Promise<string | undefined> {
    const assetPath = this.getAssetPath(referenceId);
    const contentBytes = await this._fileStore.loadAsset(assetPath);

    if (!contentBytes) {
      return undefined;
    }

    return new TextDecoder().decode(contentBytes);
  }

  /**
   * Get the asset path for content
   */
  private getAssetPath(referenceId: string): string {
    return `${UntruncatedContentManager.ASSET_PREFIX}/content/${referenceId}.txt`;
  }

  /**
   * Get the asset path for metadata
   */
  private getMetadataPath(referenceId: string): string {
    return `${UntruncatedContentManager.ASSET_PREFIX}/metadata/${referenceId}.json`;
  }
}
