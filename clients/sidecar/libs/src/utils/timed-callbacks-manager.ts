export class TimedCallbacksManager {
  private _controllers = new Set<AbortController>();
  private _timeoutIds = new Set<NodeJS.Timeout>();

  public addCallback(
    callback: (abortSignal: AbortSignal) => void,
    delayMs: number,
  ) {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => {
      callback(controller.signal);
      // Clean up after execution
      this._controllers.delete(controller);
      this._timeoutIds.delete(timeoutId);
    }, delayMs);
    this._controllers.add(controller);
    this._timeoutIds.add(timeoutId);
  }

  public cancelAll() {
    this._controllers.forEach((controller) => controller.abort());
    this._timeoutIds.forEach((timeoutId) => clearTimeout(timeoutId));
    this._controllers.clear();
    this._timeoutIds.clear();
  }
}
