import { splitIntoSymbols, fuzzyMatchLines } from "../match-lines";

describe("splitIntoSymbols", () => {
  it("should split text into symbols correctly", () => {
    expect(splitIntoSymbols("hello world")).toEqual(["hello", "world"]);
    expect(splitIntoSymbols("function(param1, param2)")).toEqual([
      "function",
      "(",
      "param1",
      ",",
      "param2",
      ")",
    ]);
    expect(splitIntoSymbols("a+b-c*d/e")).toEqual([
      "a",
      "+",
      "b",
      "-",
      "c",
      "*",
      "d",
      "/",
      "e",
    ]);
    expect(splitIntoSymbols("snake_case camelCase PascalCase")).toEqual([
      "snake_case",
      "camelCase",
      "PascalCase",
    ]);
    expect(splitIntoSymbols("  leading and trailing  spaces  ")).toEqual([
      "leading",
      "and",
      "trailing",
      "spaces",
    ]);
    expect(splitIntoSymbols("")).toEqual([]);
  });

  it("should handle special characters and whitespace", () => {
    expect(splitIntoSymbols("if (x > 0) { return true; }")).toEqual([
      "if",
      "(",
      "x",
      ">",
      "0",
      ")",
      "{",
      "return",
      "true",
      ";",
      "}",
    ]);
    expect(splitIntoSymbols("\t\nwhitespace\r\n")).toEqual(["whitespace"]);
    expect(splitIntoSymbols("symbols!@#$%^&*()")).toEqual([
      "symbols",
      "!",
      "@",
      "#",
      "$",
      "%",
      "^",
      "&",
      "*",
      "(",
      ")",
    ]);
  });
});

describe("fuzzyMatchLines", () => {
  it("should match identical lines", () => {
    const linesA = ["line 1", "line 2", "line 3"];
    const linesB = ["line 1", "line 2", "line 3"];
    const expected = [[0], [1], [2]]; // Each line maps to the same index
    expect(fuzzyMatchLines(linesA, linesB)).toEqual(expected);
  });

  it("should handle split lines", () => {
    const linesA = ["function foo(x, y) { return x + y; }"];
    const linesB = ["function foo(x, y) {", "  return x + y;", "}"];

    const result = fuzzyMatchLines(linesA, linesB);

    // The first line in A should map to all lines in B since the symbols are spread across them
    expect(result[0].sort()).toEqual([0, 1, 2]);
  });

  it("should handle merged lines", () => {
    const linesA = ["function foo(x, y) {", "  return x + y;", "}"];
    const linesB = ["function foo(x, y) { return x + y; }"];

    const result = fuzzyMatchLines(linesA, linesB);

    // All lines in A should map to the first line in B
    expect(result[0]).toContain(0);
    expect(result[1]).toContain(0);
    expect(result[2]).toContain(0);
  });

  it("should handle empty arrays", () => {
    expect(fuzzyMatchLines([], [])).toEqual([]);
    expect(fuzzyMatchLines(["line"], [])).toEqual([[]]);
    expect(fuzzyMatchLines([], ["line"])).toEqual([]);
  });

  it("should handle complex formatting changes", () => {
    const linesA = [
      "if (condition) {",
      "  doSomething();",
      "  if (nestedCondition) {",
      "    doSomethingElse();",
      "  }",
      "}",
    ];

    const linesB = [
      "if (condition) { doSomething();",
      "  if (nestedCondition) { doSomethingElse(); }",
      "}",
    ];

    const result = fuzzyMatchLines(linesA, linesB);

    // Line 0 in A should map to line 0 in B
    expect(result[0]).toContain(0);

    // Line 1 in A should map to line 0 in B
    expect(result[1]).toContain(0);

    // Line 2 in A should map to line 1 in B
    expect(result[2]).toContain(1);

    // Line 3 in A should map to line 1 in B
    expect(result[3]).toContain(1);

    // Lines 4 and 5 in A should map to line 1 or 2 in B
    expect(result[4].some((idx) => idx === 1 || idx === 2)).toBeTruthy();
    expect(result[5].some((idx) => idx === 2)).toBeTruthy();
  });
});
