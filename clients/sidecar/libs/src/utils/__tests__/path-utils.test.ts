import * as pathLib from "path";
import { joinPath, isSuffixPath } from "../path-utils";

describe("path-utils", () => {
  describe("joinPath", () => {
    describe("asDirName parameter", () => {
      test("adds trailing separator when asDirName is true", () => {
        const result = joinPath("src", "utils", true);
        const expected = pathLib.join("src", "utils") + pathLib.sep;
        expect(result).toBe(expected);
      });

      test("does not add trailing separator when asDirName is false", () => {
        const result = joinPath("src", "utils", false);
        const expected = pathLib.join("src", "utils");
        expect(result).toBe(expected);
      });

      test("does not add trailing separator when asDirName is undefined (default)", () => {
        const result = joinPath("src", "utils");
        const expected = pathLib.join("src", "utils");
        expect(result).toBe(expected);
      });

      test("does not add duplicate separator when path already ends with separator", () => {
        const pathWithSep = pathLib.join("src", "utils") + pathLib.sep;
        const result = joinPath("src", "utils" + pathLib.sep, true);
        expect(result).toBe(pathWithSep);
      });
    });
  });

  describe("isSuffixPath", () => {
    describe("basic suffix matching", () => {
      test("returns true for exact match", () => {
        expect(isSuffixPath("src/utils/file.ts", "src/utils/file.ts")).toBe(
          true,
        );
      });

      test("returns true for valid suffix", () => {
        expect(isSuffixPath("src/utils/file.ts", "utils/file.ts")).toBe(true);
      });

      test("returns true for filename only suffix", () => {
        expect(isSuffixPath("src/utils/file.ts", "file.ts")).toBe(true);
      });

      test("returns false for non-suffix", () => {
        expect(isSuffixPath("src/utils/file.ts", "other/file.ts")).toBe(false);
      });

      test("returns false when query is longer than target", () => {
        expect(isSuffixPath("file.ts", "src/utils/file.ts")).toBe(false);
      });
    });

    describe("path segment boundary checking", () => {
      test("returns false for partial segment match", () => {
        // "ile.ts" is a suffix of "src/utils/file.ts" but not a valid path suffix
        expect(isSuffixPath("src/utils/file.ts", "ile.ts")).toBe(false);
      });

      test("returns false for partial directory name match", () => {
        // "tils/file.ts" is a suffix but "tils" is not a complete directory name
        expect(isSuffixPath("src/utils/file.ts", "tils/file.ts")).toBe(false);
      });

      test("returns true when query starts at path separator", () => {
        expect(isSuffixPath("src/utils/file.ts", "utils/file.ts")).toBe(true);
      });

      test("returns true when query matches from beginning", () => {
        expect(isSuffixPath("file.ts", "file.ts")).toBe(true);
      });
    });

    describe("path normalization", () => {
      test("normalizes redundant separators", () => {
        expect(isSuffixPath("src//utils/file.ts", "utils/file.ts")).toBe(true);
      });

      test("handles relative path components", () => {
        const target = pathLib.normalize("src/./utils/file.ts");
        const query = pathLib.normalize("utils/file.ts");
        expect(isSuffixPath(target, query)).toBe(true);
      });
    });

    describe("edge cases", () => {
      test("handles empty query string", () => {
        // Empty string gets normalized to "." which is not a valid suffix
        expect(isSuffixPath("src/utils/file.ts", "")).toBe(false);
      });

      test("handles empty target string", () => {
        expect(isSuffixPath("", "file.ts")).toBe(false);
      });

      test("handles both empty strings", () => {
        // Both empty strings get normalized to "." so this should be true
        expect(isSuffixPath("", "")).toBe(true);
      });

      test("handles single character paths", () => {
        expect(isSuffixPath("a", "a")).toBe(true);
        expect(isSuffixPath("a", "b")).toBe(false);
      });

      test("handles paths with special characters", () => {
        expect(
          isSuffixPath("src/file with spaces.ts", "file with spaces.ts"),
        ).toBe(true);
        expect(isSuffixPath("src/file-name_test.ts", "file-name_test.ts")).toBe(
          true,
        );
      });

      test("handles paths with dots", () => {
        expect(isSuffixPath("src/.hidden/file.ts", ".hidden/file.ts")).toBe(
          true,
        );
        expect(isSuffixPath("src/../utils/file.ts", "file.ts")).toBe(true);
      });
    });

    describe("false positive prevention", () => {
      test("prevents false positive with similar endings", () => {
        expect(isSuffixPath("src/myutils/file.ts", "utils/file.ts")).toBe(
          false,
        );
      });

      test("prevents false positive with substring match", () => {
        expect(isSuffixPath("src/utilities/file.ts", "utils/file.ts")).toBe(
          false,
        );
      });

      test("prevents false positive with partial filename", () => {
        expect(isSuffixPath("src/utils/myfile.ts", "file.ts")).toBe(false);
      });

      test("case sensitivity", () => {
        // Path comparison should be case-sensitive on case-sensitive filesystems
        expect(isSuffixPath("src/Utils/file.ts", "utils/file.ts")).toBe(false);
        expect(isSuffixPath("src/utils/File.ts", "file.ts")).toBe(false);
      });
    });

    describe("absolute vs relative paths", () => {
      test("handles absolute target with relative query", () => {
        expect(isSuffixPath("/home/<USER>/src/file.ts", "src/file.ts")).toBe(
          true,
        );
      });

      test("handles relative target with relative query", () => {
        expect(isSuffixPath("./src/file.ts", "src/file.ts")).toBe(true);
      });

      test("handles complex relative paths", () => {
        const target = pathLib.normalize("../parent/src/utils/file.ts");
        const query = pathLib.normalize("utils/file.ts");
        expect(isSuffixPath(target, query)).toBe(true);
      });
    });
  });
});
