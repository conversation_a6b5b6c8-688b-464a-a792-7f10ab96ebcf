import { RulesParser } from "../rules-parser";
import { RuleType } from "../../chat/chat-types";

describe("RulesParser", () => {
  describe("parseRuleFile", () => {
    it("should parse rule with explicit type field", () => {
      const content = `---
type: always_apply
description: Test rule
---

# Test Rule

This is a test rule.`;

      const result = RulesParser.parseRuleFile(content, "test.md");

      expect(result.type).toBe(RuleType.ALWAYS_ATTACHED);
      expect(result.path).toBe("test.md");
      expect(result.content).toBe("# Test Rule\n\nThis is a test rule.");
      expect(result.description).toBe("Test rule");
    });

    it("should parse rule with manual type", () => {
      const content = `---
type: manual
---

# Manual Rule

This is a manual rule.`;

      const result = RulesParser.parseRuleFile(content, "manual.md");

      expect(result.type).toBe(RuleType.MANUAL);
      expect(result.path).toBe("manual.md");
      expect(result.content).toBe("# Manual Rule\n\nThis is a manual rule.");
    });

    it("should parse rule with agent_requested type", () => {
      const content = `---
type: agent_requested
description: Agent requested rule
---

# Agent Rule

This is an agent requested rule.`;

      const result = RulesParser.parseRuleFile(content, "agent.md");

      expect(result.type).toBe(RuleType.AGENT_REQUESTED);
      expect(result.path).toBe("agent.md");
      expect(result.content).toBe(
        "# Agent Rule\n\nThis is an agent requested rule.",
      );
      expect(result.description).toBe("Agent requested rule");
    });

    it("should handle invalid type values by falling back to default", () => {
      const content = `---
type: invalid_type
---

# Invalid Type Rule

This rule has an invalid type.`;

      const result = RulesParser.parseRuleFile(content, "invalid.md");

      expect(result.type).toBe(RuleType.MANUAL); // Default type
    });

    it("should handle case-insensitive type values", () => {
      const content = `---
type: ALWAYS_APPLY
---

# Case Test Rule

This rule tests case insensitivity.`;

      const result = RulesParser.parseRuleFile(content, "case.md");

      expect(result.type).toBe(RuleType.ALWAYS_ATTACHED);
    });

    // Backward compatibility tests
    it("should fall back to legacy logic when no explicit type field", () => {
      const content = `---
alwaysApply: true
description: Legacy rule
---

# Legacy Rule

This is a legacy rule.`;

      const result = RulesParser.parseRuleFile(content, "legacy.md");

      expect(result.type).toBe(RuleType.ALWAYS_ATTACHED);
      expect(result.description).toBe("Legacy rule");
    });

    it("should infer AGENT_REQUESTED from description when no type field", () => {
      const content = `---
description: This rule should be agent requested
---

# Agent Inferred Rule

This rule should be inferred as agent requested.`;

      const result = RulesParser.parseRuleFile(content, "inferred.md");

      expect(result.type).toBe(RuleType.AGENT_REQUESTED);
      expect(result.description).toBe("This rule should be agent requested");
    });

    it("should default to MANUAL when no type, alwaysApply, or description", () => {
      const content = `---
---

# Default Rule

This rule should default to manual.`;

      const result = RulesParser.parseRuleFile(content, "default.md");

      expect(result.type).toBe(RuleType.MANUAL);
    });

    it("should handle rule without frontmatter", () => {
      const content = `# No Frontmatter Rule

This rule has no frontmatter.`;

      const result = RulesParser.parseRuleFile(content, "no-frontmatter.md");

      expect(result.type).toBe(RuleType.MANUAL);
      expect(result.content).toBe(content);
      expect(result.description).toBeUndefined();
    });

    it("should prioritize explicit type over legacy fields", () => {
      const content = `---
type: manual
alwaysApply: true
description: Conflicting rule
---

# Conflicting Rule

This rule has conflicting type indicators.`;

      const result = RulesParser.parseRuleFile(content, "conflict.md");

      // Explicit type should take precedence
      expect(result.type).toBe(RuleType.MANUAL);
      expect(result.description).toBe("Conflicting rule");
    });
  });

  describe("formatRuleFileForMarkdown", () => {
    it("should format rule with explicit type field", () => {
      const rule = {
        type: RuleType.ALWAYS_ATTACHED,
        path: "test.md",
        content: "# Test Rule\n\nThis is a test rule.",
        description: "Test description",
      };

      const result = RulesParser.formatRuleFileForMarkdown(rule);

      expect(result).toContain('type: "always_apply"');
      expect(result).toContain('description: "Test description"');
      expect(result).toContain("# Test Rule");
    });

    it("should format manual rule correctly", () => {
      const rule = {
        type: RuleType.MANUAL,
        path: "manual.md",
        content: "# Manual Rule\n\nThis is a manual rule.",
      };

      const result = RulesParser.formatRuleFileForMarkdown(rule);

      expect(result).toContain('type: "manual"');
      expect(result).toContain("# Manual Rule");
    });

    it("should format agent requested rule correctly", () => {
      const rule = {
        type: RuleType.AGENT_REQUESTED,
        path: "agent.md",
        content: "# Agent Rule\n\nThis is an agent rule.",
        description: "Agent description",
      };

      const result = RulesParser.formatRuleFileForMarkdown(rule);

      expect(result).toContain('type: "agent_requested"');
      expect(result).toContain('description: "Agent description"');
      expect(result).toContain("# Agent Rule");
    });

    it("should not add description field when not present", () => {
      const rule = {
        type: RuleType.MANUAL,
        path: "no-desc.md",
        content: "# No Description Rule\n\nThis rule has no description.",
      };

      const result = RulesParser.formatRuleFileForMarkdown(rule);

      expect(result).toContain('type: "manual"');
      expect(result).not.toContain("description:");
      expect(result).toContain("# No Description Rule");
    });
  });

  describe("helper methods", () => {
    it("should get type from frontmatter", () => {
      const content = `---
type: always_apply
---

# Test`;

      const result = RulesParser.getTypeFrontmatterKey(content);
      expect(result).toBe("always_apply");
    });

    it("should update type in frontmatter", () => {
      const content = `---
type: manual
---

# Test`;

      const result = RulesParser.updateTypeFrontmatterKey(
        content,
        RuleType.ALWAYS_ATTACHED,
      );
      expect(result).toContain('type: "always_apply"');
    });

    it("should handle empty type field", () => {
      const content = `---
description: test
---

# Test`;

      const result = RulesParser.getTypeFrontmatterKey(content);
      expect(result).toBe("");
    });
  });

  describe("getRuleTypeFromContent", () => {
    it("should prioritize explicit type field", () => {
      const content = `---
type: manual
alwaysApply: true
description: Test
---

# Test`;

      const result = RulesParser.getRuleTypeFromContent(content);
      expect(result).toBe(RuleType.MANUAL);
    });

    it("should fall back to legacy alwaysApply logic", () => {
      const content = `---
alwaysApply: true
---

# Test`;

      const result = RulesParser.getRuleTypeFromContent(content);
      expect(result).toBe(RuleType.ALWAYS_ATTACHED);
    });

    it("should fall back to description logic", () => {
      const content = `---
description: Test description
---

# Test`;

      const result = RulesParser.getRuleTypeFromContent(content);
      expect(result).toBe(RuleType.AGENT_REQUESTED);
    });

    it("should default to MANUAL", () => {
      const content = `# Test`;

      const result = RulesParser.getRuleTypeFromContent(content);
      expect(result).toBe(RuleType.MANUAL);
    });
  });
});
