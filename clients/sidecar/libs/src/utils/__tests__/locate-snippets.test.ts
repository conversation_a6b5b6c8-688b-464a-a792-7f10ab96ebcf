import { fuzzyLocateSnippet } from "../locate-snippet";

describe("fuzzyLocateSnippet", () => {
  // Test 1: Simple sanity check
  it("should find the correct range in a simple case", () => {
    const lines = ["1", "2", "3", "4", "5"];
    const fileContent = lines.join("\n");
    const patternLines = ["2", "3"];
    const pattern = patternLines.join("\n");
    const expectedRange = { start: 1, end: 2 }; // Indices for '2' and '3'

    const result = fuzzyLocateSnippet(fileContent, pattern);
    expect(result).toEqual(expectedRange);
  });

  // Test 2: No match found
  it("should return null when there is no match", () => {
    const lines = ["1", "2", "3", "4", "5"];
    const fileContent = lines.join("\n");
    const patternLines = ["6", "7"];
    const pattern = patternLines.join("\n");
    const result = fuzzyLocateSnippet(fileContent, pattern);
    expect(result).toBeNull();
  });

  // Test 3: Pattern matches the entire file content
  it("should return the full range when the pattern matches the entire file", () => {
    const lines = ["line1", "line2", "line3"];
    const fileContent = lines.join("\n");
    const patternLines = ["line1", "line2", "line3"];
    const pattern = patternLines.join("\n");
    const expectedRange = { start: 0, end: 2 };

    const result = fuzzyLocateSnippet(fileContent, pattern);
    expect(result).toEqual(expectedRange);
  });

  // Test 4: Multiple possible matches
  it("should find the earliest occurrence when multiple matches exist", () => {
    const lines = ["A", "B", "C", "A", "B", "C", "D"];
    const fileContent = lines.join("\n");
    const patternLines = ["A", "B", "C"];
    const pattern = patternLines.join("\n");
    const expectedRange = { start: 0, end: 2 }; // First occurrence

    const result = fuzzyLocateSnippet(fileContent, pattern);
    expect(result).toEqual(expectedRange);
  });

  // Test 5: Pattern longer than file content
  it("should return the best match even when the pattern is longer than the file content", () => {
    const lines = ["line1", "line2"];
    const fileContent = lines.join("\n");
    const patternLines = ["line1", "line2", "line3"];
    const pattern = patternLines.join("\n");
    const expectedRange = { start: 0, end: 1 }; // Lines 'line1' and 'line2'

    const result = fuzzyLocateSnippet(fileContent, pattern);
    expect(result).toEqual(expectedRange);
  });

  // Test 6: Non-consecutive matching lines
  it("should handle non-consecutive matching lines", () => {
    const lines = ["lineA", "match1", "lineB", "match2", "lineC", "match3"];
    const fileContent = lines.join("\n");
    const patternLines = ["match1", "match2", "match3"];
    const pattern = patternLines.join("\n");
    const expectedRange = { start: 1, end: 5 }; // Indices for 'match1' to 'match3'

    const result = fuzzyLocateSnippet(fileContent, pattern);
    expect(result).toEqual(expectedRange);
  });

  // Test 7: Case sensitivity
  it("should be case-sensitive in matching", () => {
    const lines = ["Line1", "Line2", "Line3"];
    const fileContent = lines.join("\n");
    const patternLines = ["line1", "line2"];
    const pattern = patternLines.join("\n");
    const result = fuzzyLocateSnippet(fileContent, pattern);
    expect(result).toBeNull(); // Lines differ in case
  });

  // Test 8: Empty pattern
  it("should return null for an empty pattern", () => {
    const lines = ["line1", "line2", "line3"];
    const fileContent = lines.join("\n");
    const pattern = "";
    const result = fuzzyLocateSnippet(fileContent, pattern);
    expect(result).toBeNull(); // No pattern to match
  });

  // Test 9: Empty file content
  it("should return null for empty file content", () => {
    const fileContent = "";
    const patternLines = ["line1", "line2"];
    const pattern = patternLines.join("\n");
    const result = fuzzyLocateSnippet(fileContent, pattern);
    expect(result).toBeNull(); // No content to search in
  });

  // Test 10: Pattern with repeated lines
  it("should correctly handle patterns with repeated lines", () => {
    const lines = ["line1", "line2", "line1", "line2", "line3"];
    const fileContent = lines.join("\n");
    const patternLines = ["line1", "line2", "line3"];
    const pattern = patternLines.join("\n");
    const expectedRange = { start: 2, end: 4 };

    const result = fuzzyLocateSnippet(fileContent, pattern);
    expect(result).toEqual(expectedRange);
  });

  // Test 11: Pattern partially matches at the end of file
  it("should return best match when pattern partially matches at the end of file", () => {
    const lines = ["line1", "line2", "line3", "line4"];
    const fileContent = lines.join("\n");
    const patternLines = ["line3", "line4", "line5"];
    const pattern = patternLines.join("\n");
    const expectedRange = { start: 2, end: 3 };

    const result = fuzzyLocateSnippet(fileContent, pattern);
    expect(result).toEqual(expectedRange);
  });

  // Test 12: Pattern with Unicode characters
  it("should correctly handle patterns with Unicode characters", () => {
    const lines = ["line1", "línea2", "行3", "ライン4"];
    const fileContent = lines.join("\n");
    const patternLines = ["línea2", "行3"];
    const pattern = patternLines.join("\n");
    const expectedRange = { start: 1, end: 2 };

    const result = fuzzyLocateSnippet(fileContent, pattern);
    expect(result).toEqual(expectedRange);
  });

  // Test 13: Patterns with empty lines
  it("should handle patterns with empty lines", () => {
    const lines = ["line1", "", "line2", "", "line3"];
    const fileContent = lines.join("\n");
    const patternLines = ["line1", "", "line2"];
    const pattern = patternLines.join("\n");
    const expectedRange = { start: 0, end: 2 };

    const result = fuzzyLocateSnippet(fileContent, pattern);
    expect(result).toEqual(expectedRange);
  });

  // Test 14: Whitespace differences
  it("should be sensitive to whitespace differences", () => {
    const lines = ["line 1", "line  2", "line   3"];
    const fileContent = lines.join("\n");
    const patternLines = ["line 1", "line 2"];
    const pattern = patternLines.join("\n");
    const expectedRange = { start: 0, end: 0 };

    const result = fuzzyLocateSnippet(fileContent, pattern);
    expect(result).toEqual(expectedRange);
  });

  // Test 15: Pattern includes first and last lines of file
  it("should correctly match when pattern includes first and last lines of file", () => {
    const lines = ["first", "middle1", "middle2", "last"];
    const fileContent = lines.join("\n");
    const patternLines = ["first", "last"];
    const pattern = patternLines.join("\n");
    const expectedRange = { start: 0, end: 3 };

    const result = fuzzyLocateSnippet(fileContent, pattern);
    expect(result).toEqual(expectedRange);
  });

  // TODO: FIX THIS TEST
  // Test 16: Windows-style line endings (CRLF)
  // it("should handle Windows-style line endings (CRLF)", () => {
  //     const lines = ["line1", "line2", "line3"];
  //     const fileContent = lines.join("\r\n");
  //     const patternLines = ["line1", "line2"];
  //     const pattern = patternLines.join("\n");
  //     const expectedRange = { start: 0, end: 1 };

  //     const result = fuzzyLocateSnippet(fileContent, pattern);
  //     expect(result).toEqual(expectedRange);
  // });
});

describe("fuzzyLocateSnippet with indentation differences", () => {
  // Test 1: Basic indentation difference
  it("should find pattern with less indentation in a simple case", () => {
    const fileContent = `
for x in range(10):
    print("hello world!")
    if x % 2 == 0:
        print("even")
`;
    const pattern = `print("hello world!")`;
    const result = fuzzyLocateSnippet(fileContent, pattern);
    expect(result).toEqual({ start: 2, end: 2 });
  });

  // Test 2: Multiple levels of indentation
  it("should find pattern within multiple levels of indentation", () => {
    const fileContent = `
def outer_function():
    def inner_function():
        if True:
            for i in range(5):
                print("nested")
    inner_function()
`;
    const pattern = `print("nested")`;
    const result = fuzzyLocateSnippet(fileContent, pattern);
    expect(result).toEqual({ start: 5, end: 5 });
  });

  // Test 3: Pattern with partial indentation
  it("should find pattern with partial indentation", () => {
    const fileContent = `
class MyClass:
    def __init__(self):
        self.value = 42

    def print_value(self):
        print(f"Value: {self.value}")
`;
    const pattern = `
def print_value(self):
    print(f"Value: {self.value}")
`;
    const result = fuzzyLocateSnippet(fileContent, pattern);
    expect(result).toEqual({ start: 5, end: 6 });
  });

  // Test 4: Pattern spanning multiple indentation levels
  it("should find pattern spanning multiple indentation levels", () => {
    const fileContent = `
if condition:
    with open('file.txt', 'r') as f:
        for line in f:
            if 'important' in line:
                print("Found important line")
                break
`;
    const pattern = `
with open('file.txt', 'r') as f:
for line in f:
    if 'important' in line:
        print("Found important line")
`;
    const result = fuzzyLocateSnippet(fileContent, pattern);
    expect(result).toEqual({ start: 2, end: 5 });
  });

  // Test 5: Mixed indentation styles
  it("should find pattern with different indentation style", () => {
    const fileContent = `
def calculate(x, y):
\tresult = x + y
\tif result > 10:
\t\tprint("Large result")
\treturn result
`;
    const pattern = `
result = x + y
if result > 10:
    print("Large result")
`;
    const result = fuzzyLocateSnippet(fileContent, pattern);
    expect(result).toEqual({ start: 2, end: 4 });
  });

  it("should prioritize match that ignores indentation but have higher LCS", () => {
    const fileContent = `
    pub async fn get_models(&self, req: &HttpRequest) -> Result<HttpResponse, tonic::Status> {
        let (user, tenant_info, _request_context) = request_context_from_req(req)?;

        let feature_flags = self.get_feature_flags(&user, &tenant_info)?;

        if CB_GET_MODELS.get_from(&feature_flags) {
            return Err(tonic::Status::resource_exhausted("Circuit breaker is open"));
        }

        response.feature_flags = Some(frontend_feature_flags);
        Ok(HttpResponse::Ok().json(response))
    }
}

async fn instruction_stream<MR: ModelRegistry, CNC: ContentManagerClient>(
    data: web::Data<Handler<MR, CNC>>,
    req: &HttpRequest,
    front_instruction_request: public_api_proto::InstructionRequest,
    root_span: RootSpan,
) -> Result<Receiver<tonic::Result<edit::InstructionResponse>>, tonic::Status> {
    tracing::info!("edit request model={:?}", front_instruction_request.model,);

    let (user, tenant_info, request_context) = request_context_from_req(req)?;
    root_span.record("tenant_name", &tenant_info.tenant_name);

    let feature_flags = data.get_feature_flags(&user, &tenant_info)?;

    if CB_EDIT.get_from(&feature_flags) {
        return Err(tonic::Status::resource_exhausted("Circuit breaker is open"));
    }

    let user_agent = req
        .headers()
        .get("user-agent")
        .map(|h| h.to_str().unwrap_or("not-utf8"))
        .unwrap_or("");

    data.request_insight_publisher
        .record_request_metadata(
            &request_context,
            &tenant_info,
            request_insight::RequestMetadata {
                request_type: request_insight::RequestType::Edit.into(),
                session_id: request_context.request_session_id().to_string(),
                user_id: user.user_id.to_string(),
                user_agent: user_agent.to_string(),
            },
        )
        .await;

    let (com, mi) = get_model::<MR>(
        front_instruction_request.model.as_ref(),
        &data.model_registry,
        &feature_flags,
        &INSTRUCTION_MODEL_FLAG,
        ModelType::Edit,
    )
    .await?;

    let com = match com {
        Client::Edit(c) => c.clone(),
        _ => return Err(tonic::Status::internal("Model is not an instruction model")),
    };
    let edit_request: InstructionRequest = (front_instruction_request, &mi.name).try_into()?;

    com.instruction_stream(&request_context, edit_request).await
}
`;
    const pattern = `
pub async fn get_models(&self, req: &HttpRequest) -> Result<HttpResponse, tonic::Status> {
    let (user, tenant_info, _request_context) = request_context_from_req(req)?;

    let feature_flags = self.get_feature_flags(&user, &tenant_info)?;

    if CB_GET_MODELS.get_from(&feature_flags) {
        return Err(tonic::Status::resource_exhausted("Circuit breaker is open"));
    }

    // ... (rest of the implementation)
}
`;
    const result = fuzzyLocateSnippet(fileContent, pattern);
    expect(result).toEqual({ start: 1, end: 12 });
  });
});
