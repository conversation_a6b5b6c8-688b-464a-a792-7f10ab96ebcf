import { IPluginFileStore } from "../../client-interfaces/plugin-file-store";
import {
  TruncatedContentType,
  UntruncatedContentManager,
} from "../untruncated-content-manager";
import { LocalToolType } from "../../tools/tool-types";

// Mock the IPluginFileStore
class MockPluginFileStore implements IPluginFileStore {
  private storedAssets: Map<string, Uint8Array> = new Map();

  saveAsset(name: string, content: Uint8Array): Promise<void> {
    this.storedAssets.set(name, content);
    return Promise.resolve();
  }

  loadAsset(name: string): Promise<Uint8Array | undefined> {
    return Promise.resolve(this.storedAssets.get(name));
  }

  deleteAsset(name: string): Promise<void> {
    this.storedAssets.delete(name);
    return Promise.resolve();
  }

  listAssets(prefix: string): Promise<string[]> {
    return Promise.resolve(
      Array.from(this.storedAssets.keys()).filter((name) =>
        name.startsWith(prefix),
      ),
    );
  }

  getAssetPath(path: string): Promise<string> {
    // Mock implementation returns a fake absolute path
    return Promise.resolve(`/mock/path/to/assets/${path}`);
  }
}

describe("UntruncatedContentManager", () => {
  let fileStore: MockPluginFileStore;
  let contentManager: UntruncatedContentManager;
  const originalCommand = 'echo "test-original-command"';

  beforeEach(() => {
    fileStore = new MockPluginFileStore();
    contentManager = new UntruncatedContentManager(fileStore);
  });

  describe("storeUntruncatedContent", () => {
    test("should store content and metadata", async () => {
      const content = "This is test content\nwith multiple lines\nfor testing";
      const contentType = TruncatedContentType.ToolOutput;
      const shownRange: [number, number] = [1, 2];
      const toolUseId = "test-tool-use-id";
      const requestId = "test-request-id";
      const conversationId = "test-conversation-id";

      const metadata = await contentManager.storeUntruncatedContent(
        content,
        contentType,
        shownRange,
        toolUseId,
        requestId,
        conversationId,
        LocalToolType.strReplaceEditor,
        originalCommand,
      );

      expect(metadata.contentType).toBe(contentType);
      expect(metadata.totalSize).toBe(content.length);
      expect(metadata.totalLines).toBe(3);
      expect(metadata.shownRange).toEqual(shownRange);
      expect(metadata.toolUseId).toBe(toolUseId);
      expect(metadata.requestId).toBe(requestId);
      expect(metadata.conversationId).toBe(conversationId);
      expect(metadata.toolType).toBe(LocalToolType.strReplaceEditor);
      expect(metadata.referenceId).toBeDefined();
      expect(metadata.originalCommand).toBe(originalCommand);
    });
  });

  describe("getContentRange", () => {
    test("should retrieve a range of content", async () => {
      const content = "Line 1\nLine 2\nLine 3\nLine 4\nLine 5";
      const metadata = await contentManager.storeUntruncatedContent(
        content,
        TruncatedContentType.ToolOutput,
        [1, 2],
        undefined,
        undefined,
        undefined,
        LocalToolType.launchProcess,
        originalCommand,
      );

      const range = await contentManager.getContentRange(
        metadata.referenceId,
        2,
        4,
      );

      expect(range).toBe("Line 2\nLine 3\nLine 4");
    });

    test("should handle out of bounds ranges", async () => {
      const content = "Line 1\nLine 2\nLine 3";
      const metadata = await contentManager.storeUntruncatedContent(
        content,
        TruncatedContentType.ToolOutput,
        [1, 2],
        undefined,
        undefined,
        undefined,
        LocalToolType.strReplaceEditor,
        originalCommand,
      );

      const range = await contentManager.getContentRange(
        metadata.referenceId,
        2,
        10,
      );

      expect(range).toBe("Line 2\nLine 3");
    });

    test("should return undefined for non-existent reference ID", async () => {
      const range = await contentManager.getContentRange(
        "non-existent-id",
        1,
        2,
      );

      expect(range).toBeUndefined();
    });
  });

  describe("searchContent", () => {
    test("should find matches in content", async () => {
      const content =
        "This is line one\nThis is line two\nThis is line three\nThis is line four\nThis is line five";
      const metadata = await contentManager.storeUntruncatedContent(
        content,
        TruncatedContentType.ToolOutput,
        [1, 2],
        undefined,
        undefined,
        undefined,
        LocalToolType.strReplaceEditor,
        originalCommand,
      );

      const searchResult = await contentManager.searchContent(
        metadata.referenceId,
        "line three",
        1,
      );

      expect(searchResult).toBeDefined();
      expect(searchResult!.totalMatches).toBe(1);
      expect(searchResult!.content).toContain("line three");
      expect(searchResult!.matchedRange[0]).toBeLessThanOrEqual(3);
      expect(searchResult!.matchedRange[1]).toBeGreaterThanOrEqual(3);
    });

    test("should handle case-insensitive search", async () => {
      const content = "This is LINE one\nThis is line two";
      const metadata = await contentManager.storeUntruncatedContent(
        content,
        TruncatedContentType.ToolOutput,
        [1, 2],
        undefined,
        undefined,
        undefined,
        LocalToolType.strReplaceEditor,
        originalCommand,
      );

      const searchResult = await contentManager.searchContent(
        metadata.referenceId,
        "line",
        0,
      );

      expect(searchResult).toBeDefined();
      expect(searchResult!.totalMatches).toBe(2);
    });

    test("should return appropriate message when no matches found", async () => {
      const content = "This is some content";
      const metadata = await contentManager.storeUntruncatedContent(
        content,
        TruncatedContentType.ToolOutput,
        [1, 1],
        undefined,
        undefined,
        undefined,
        LocalToolType.strReplaceEditor,
        originalCommand,
      );

      const searchResult = await contentManager.searchContent(
        metadata.referenceId,
        "nonexistent",
      );

      expect(searchResult).toBeDefined();
      expect(searchResult!.totalMatches).toBe(0);
      expect(searchResult!.content).toContain("No matches found");
    });

    test("should return undefined for non-existent reference ID", async () => {
      const searchResult = await contentManager.searchContent(
        "non-existent-id",
        "test",
      );

      expect(searchResult).toBeUndefined();
    });
  });

  describe("getMetadata", () => {
    test("should store metadata with undefined originalCommand", async () => {
      const content = "Test content";
      const originalMetadata = await contentManager.storeUntruncatedContent(
        content,
        TruncatedContentType.ToolOutput,
        [1, 1],
        undefined,
        undefined,
        undefined,
        LocalToolType.strReplaceEditor,
        undefined,
      );

      expect(originalMetadata.originalCommand).toBeUndefined();
    });

    test("should return undefined for non-existent reference ID", async () => {
      const metadata = await contentManager.getMetadata("non-existent-id");

      expect(metadata).toBeUndefined();
    });
  });
});
