import { range } from "lodash";
import { truncate, truncateMiddle } from "../strings";

describe("strings utility functions", () => {
  describe("truncate", () => {
    test("should not truncate strings shorter than maxLen", () => {
      expect(truncate("hello", 10)).toBe("hello");
    });

    test("should truncate strings longer than maxLen", () => {
      expect(truncate("hello world", 8)).toBe("hello...");
    });

    test("should handle maxLen less than 3", () => {
      expect(truncate("hello", 2)).toBe("...");
    });

    test("should handle empty string", () => {
      expect(truncate("", 5)).toBe("");
    });

    test("should handle maxLen equal to string length", () => {
      expect(truncate("hello", 5)).toBe("hello");
    });

    test("should handle multibyte chars correctly", () => {
      expect(truncate("😃😃😃😃😃123", 8)).toBe("😃😃😃😃😃123");
      expect(truncate("😃😃😃😃😃", 4)).toBe("😃...");
      expect(truncate("😃😃😃😃😃", 4, true)).toBe("...😃");
    });
  });

  describe("truncate with preserveTail=true", () => {
    test("should not truncate strings shorter than maxLen", () => {
      expect(truncate("hello", 10, true)).toBe("hello");
    });

    test("should preserve tail when specified", () => {
      expect(truncate("hello world", 8, true)).toBe("...world");
    });

    test("should handle maxLen less than 3", () => {
      expect(truncate("hello", 2, true)).toBe("...");
    });

    test("should handle empty string", () => {
      expect(truncate("", 5, true)).toBe("");
    });

    test("should handle maxLen equal to string length", () => {
      expect(truncate("hello", 5, true)).toBe("hello");
    });
  });

  describe("truncateMiddle", () => {
    test("should not truncate strings shorter than maxLen", () => {
      expect(truncateMiddle("hello", 10)).toBe("hello");
    });

    test("should truncate strings longer than maxLen", () => {
      expect(truncateMiddle("hello world", 8)).toBe("hell<...>orld");
    });

    test("should handle maxLen less than 3", () => {
      expect(truncateMiddle("hello", 2)).toBe("h<...>o");
    });

    test("should handle empty string", () => {
      expect(truncateMiddle("", 5)).toBe("");
    });

    test("should handle maxLen equal to string length", () => {
      expect(truncateMiddle("hello", 5)).toBe("hello");
    });

    test("should handle CRLF line endings correctly", () => {
      expect(truncateMiddle("hello\r\nworld", 8)).toBe("hell<...>orld");
    });

    test("should handle multiple lines correctly", () => {
      const longText = range(100)
        .map((i) => `line${i + 1}\n`)
        .join("");
      expect(truncateMiddle(longText, 80)).toEqual(
        expect.stringContaining("... additional lines truncated ..."),
      );
    });
  });
});
