import { IPluginFileStore } from "../../client-interfaces/plugin-file-store";
import {
  truncateWithMetadata,
  TruncateOptions,
  createTruncationFooter,
} from "../truncation-utils";
import {
  TruncatedContentMetadata,
  TruncatedContentType,
  UntruncatedContentManager,
} from "../untruncated-content-manager";
import { LocalToolType } from "../../tools/tool-types";
import {
  getAgentSessionEventReporter,
  AgentSessionEventReporter,
} from "../../metrics/agent-session-event-reporter";
import { AgentSessionEventData } from "../../metrics/types";

// Create a mock reporter that implements the AgentSessionEventReporter interface
const createMockReporter = () => {
  const mockReportEvent = jest.fn();
  return {
    reportEvent: mockReportEvent,
    // Add other required methods if they exist
  } as unknown as AgentSessionEventReporter & {
    reportEvent: jest.MockedFunction<
      (eventData: AgentSessionEventData) => void
    >;
  };
};

// Mock the agent session event reporter
jest.mock("../../metrics/agent-session-event-reporter", () => ({
  getAgentSessionEventReporter: jest.fn(),
}));

const mockGetAgentSessionEventReporter =
  getAgentSessionEventReporter as jest.MockedFunction<
    typeof getAgentSessionEventReporter
  >;

// Mock the UntruncatedContentManager
class MockUntruncatedContentManager extends UntruncatedContentManager {
  private storedContent: Map<string, string> = new Map();
  private storedMetadata: Map<string, TruncatedContentMetadata> = new Map();
  private lastReferenceId: string = "";

  constructor() {
    super({} as IPluginFileStore);
  }

  storeUntruncatedContent(
    content: string,
    contentType: TruncatedContentType,
    shownRange: [number, number],
    toolUseId?: string,
    requestId?: string,
    conversationId?: string,
    toolType?: LocalToolType,
    originalCommand?: string,
  ): Promise<TruncatedContentMetadata> {
    // Use a predictable reference ID for testing
    const referenceId = "test-reference-id";
    this.lastReferenceId = referenceId;

    const lines = content.split("\n");
    const metadata: TruncatedContentMetadata = {
      contentType,
      totalSize: content.length,
      totalLines: lines.length,
      shownRange,
      referenceId,
      toolUseId,
      requestId,
      conversationId,
      toolType,
      originalCommand,
      timestamp: Date.now(),
    };

    this.storedContent.set(referenceId, content);
    this.storedMetadata.set(referenceId, metadata);

    return Promise.resolve(metadata);
  }

  getContentRange(
    referenceId: string,
    startLine: number,
    endLine: number,
  ): Promise<string | undefined> {
    const content = this.storedContent.get(referenceId);
    if (!content) {
      return Promise.resolve(undefined);
    }

    const lines = content.split("\n");
    const totalLines = lines.length;

    startLine = Math.max(1, Math.min(startLine, totalLines));
    endLine = Math.max(startLine, Math.min(endLine, totalLines));

    return Promise.resolve(lines.slice(startLine - 1, endLine).join("\n"));
  }

  getMetadata(
    referenceId: string,
  ): Promise<TruncatedContentMetadata | undefined> {
    return Promise.resolve(this.storedMetadata.get(referenceId));
  }

  getLastReferenceId(): string {
    return this.lastReferenceId;
  }
}

describe("truncation-utils", () => {
  let contentManager: MockUntruncatedContentManager;

  beforeEach(() => {
    contentManager = new MockUntruncatedContentManager();
  });

  describe("truncateWithMetadata", () => {
    test("should not truncate content under the limit", async () => {
      const content = "This is a short content";
      const options: TruncateOptions = {
        maxBytes: 100,
        contentType: TruncatedContentType.ToolOutput,
        maxLinesTerminalProcessOutput: 0,
        toolType: LocalToolType.strReplaceEditor,
      };

      const result = await truncateWithMetadata(
        content,
        options,
        contentManager,
        false,
      );

      expect(result.truncatedContent).toBe(content);
      expect(result.metadata).toBeUndefined();
    });

    test("should truncate content over the byte limit", async () => {
      const content = "a".repeat(1000);
      const options: TruncateOptions = {
        maxBytes: 100,
        contentType: TruncatedContentType.ToolOutput,
        maxLinesTerminalProcessOutput: 0,
        toolType: LocalToolType.launchProcess,
      };

      const result = await truncateWithMetadata(
        content,
        options,
        contentManager,
        true,
      );

      expect(result.truncatedContent.length).toBeGreaterThan(100); // Due to footer
      expect(result.truncatedContent).toContain("<...>");
      expect(result.truncatedContent).toContain("[This result was truncated.");
      expect(result.metadata).toBeDefined();
      expect(result.metadata!.totalSize).toBe(1000);
    });

    test("should not store untruncated content when feature flag is disabled", async () => {
      const content = "a".repeat(1000);
      const options: TruncateOptions = {
        maxBytes: 100,
        contentType: TruncatedContentType.ToolOutput,
        maxLinesTerminalProcessOutput: 0,
      };

      const result = await truncateWithMetadata(
        content,
        options,
        contentManager,
        false,
      );

      expect(result.truncatedContent.length).toBeLessThan(200); // Should be much shorter without metadata footer
      expect(result.truncatedContent).toContain("<...>");
      expect(result.truncatedContent).not.toContain(
        "[This result was truncated.",
      );
      expect(result.metadata).toBeUndefined();
    });

    test("should store untruncated content when feature flag is enabled", async () => {
      const content = "a".repeat(1000);
      const options: TruncateOptions = {
        maxBytes: 100,
        contentType: TruncatedContentType.ToolOutput,
        maxLinesTerminalProcessOutput: 0,
      };

      const result = await truncateWithMetadata(
        content,
        options,
        contentManager,
        true,
      );

      expect(result.truncatedContent.length).toBeGreaterThan(100); // Due to footer
      expect(result.truncatedContent).toContain("<...>");
      expect(result.truncatedContent).toContain("[This result was truncated.");
      expect(result.metadata).toBeDefined();
      expect(result.metadata!.totalSize).toBe(1000);
    });

    test("should truncate to last N lines with metadata when both flags are enabled", async () => {
      const lines = Array.from({ length: 50 }, (_, i) => `Line ${i + 1}`);
      const content = lines.join("\n");
      const options: TruncateOptions = {
        maxBytes: 10000, // Large enough to not trigger byte truncation
        contentType: TruncatedContentType.ToolOutput,
        maxLinesTerminalProcessOutput: 3,
      };

      const result = await truncateWithMetadata(
        content,
        options,
        contentManager,
        true,
      );

      // Should contain the last 3 lines plus metadata footer
      expect(result.truncatedContent).toContain("Line 48");
      expect(result.truncatedContent).toContain("Line 49");
      expect(result.truncatedContent).toContain("Line 50");
      expect(result.truncatedContent).toContain("[This result was truncated.");
      expect(result.metadata).toBeDefined();
      expect(result.metadata!.shownRange).toEqual([48, 50]);
      expect(result.metadata!.totalLines).toBe(50);
    });

    test("should truncate to last N lines with metadata even if enableUntruncatedContentStorage is disabled", async () => {
      const lines = Array.from({ length: 50 }, (_, i) => `Line ${i + 1}`);
      const content = lines.join("\n");
      const options: TruncateOptions = {
        maxBytes: 10000, // Large enough to not trigger byte truncation
        contentType: TruncatedContentType.ToolOutput,
        maxLinesTerminalProcessOutput: 4,
      };

      const result = await truncateWithMetadata(
        content,
        options,
        contentManager,
        false,
      );

      // Should contain the last 4 lines plus metadata footer
      expect(result.truncatedContent).toContain("Line 47");
      expect(result.truncatedContent).toContain("Line 48");
      expect(result.truncatedContent).toContain("Line 49");
      expect(result.truncatedContent).toContain("Line 50");
      expect(result.truncatedContent).toContain("[This result was truncated.");
      expect(result.metadata).toBeDefined();
      expect(result.metadata!.shownRange).toEqual([47, 50]);
      expect(result.metadata!.totalLines).toBe(50);
    });

    test("should not truncate when content has fewer lines than maxLinesTerminalProcessOutput", async () => {
      const content = "Line 1\nLine 2\nLine 3";
      const options: TruncateOptions = {
        maxBytes: 10000,
        contentType: TruncatedContentType.ToolOutput,
        maxLinesTerminalProcessOutput: 10, // More than the actual number of lines
      };

      const result = await truncateWithMetadata(
        content,
        options,
        contentManager,
        true,
      );

      expect(result.truncatedContent).toBe(content);
      expect(result.metadata).toBeUndefined();
    });
  });

  describe("createTruncationFooter", () => {
    test("should create a properly formatted footer", () => {
      const metadata: TruncatedContentMetadata = {
        contentType: TruncatedContentType.ToolOutput,
        totalSize: 1000,
        totalLines: 100,
        shownRange: [1, 50],
        referenceId: "test-reference-id",
        timestamp: Date.now(),
      };

      const footer = createTruncationFooter(
        metadata,
        "**I am a footer addition**",
      );

      expect(footer).toContain("Showing lines 1-50 of 100 lines");
      expect(footer).toContain("Reference ID: test-reference-id");
      expect(footer).toContain("view-range-untruncated");
      expect(footer).toContain("search-untruncated");
      expect(footer).toContain("**I am a footer addition**");
    });
  });

  describe("truncation reporting", () => {
    test("should report truncation event when content is truncated", async () => {
      const mockReporter = createMockReporter();
      mockGetAgentSessionEventReporter.mockReturnValue(mockReporter);

      const longContent = "A".repeat(1000) + "\n" + "B".repeat(1000);
      const options: TruncateOptions = {
        maxBytes: 500,
        contentType: TruncatedContentType.ToolOutput,
        toolType: LocalToolType.readFile,
        conversationId: "test-conversation-id",
        requestId: "test-request-id",
      };

      await truncateWithMetadata(longContent, options, contentManager, true);

      expect(mockReporter.reportEvent).toHaveBeenCalledWith({
        eventName: "content-truncation",
        conversationId: "test-conversation-id",
        eventData: {
          contentTruncationData: {
            originalCharCount: longContent.length,
            originalLineCount: 2,
            truncatedCharCount: expect.any(Number) as number,
            truncatedLineCount: expect.any(Number) as number,
            toolType: "read-file",
          },
        },
      });
    });

    test("should report truncation event for maxLinesTerminalProcessOutput truncation", async () => {
      const mockReporter = createMockReporter();
      mockGetAgentSessionEventReporter.mockReturnValue(mockReporter);

      const multiLineContent = Array.from(
        { length: 100 },
        (_, i) => `Line ${i + 1}`,
      ).join("\n");
      const options: TruncateOptions = {
        maxBytes: 10000,
        contentType: TruncatedContentType.ToolOutput,
        maxLinesTerminalProcessOutput: 10,
        toolType: LocalToolType.readProcess,
        conversationId: "test-conversation-id",
        requestId: "test-request-id",
      };

      await truncateWithMetadata(
        multiLineContent,
        options,
        contentManager,
        true,
      );

      expect(mockReporter.reportEvent).toHaveBeenCalledWith({
        eventName: "content-truncation",
        conversationId: "test-conversation-id",
        eventData: {
          contentTruncationData: {
            originalCharCount: multiLineContent.length,
            originalLineCount: 100,
            truncatedCharCount: expect.any(Number) as number,
            truncatedLineCount: 10,
            toolType: "read-process",
          },
        },
      });
    });

    test("should not report truncation event when content is not truncated", async () => {
      const mockReporter = createMockReporter();
      mockGetAgentSessionEventReporter.mockReturnValue(mockReporter);

      const shortContent = "This is short content";
      const options: TruncateOptions = {
        maxBytes: 1000,
        contentType: TruncatedContentType.ToolOutput,
        toolType: LocalToolType.readFile,
        conversationId: "test-conversation-id",
        requestId: "test-request-id",
      };

      await truncateWithMetadata(shortContent, options, contentManager, true);

      expect(mockReporter.reportEvent).not.toHaveBeenCalled();
    });
  });
});
