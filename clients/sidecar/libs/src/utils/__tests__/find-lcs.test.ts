import { findLongestCommonSubsequence } from "../find-lcs";

describe("findLongestCommonSubsequence", () => {
  it("should find the longest common subsequence with exact matches", () => {
    const symbolsA = ["a", "b", "c", "d", "e"];
    const symbolsB = ["a", "b", "c", "d", "e"];
    const expected = [0, 1, 2, 3, 4]; // Each symbol maps to the same index
    expect(findLongestCommonSubsequence(symbolsA, symbolsB)).toEqual(expected);
  });

  it("should handle subsequences with gaps", () => {
    const symbolsA = ["a", "b", "c", "d", "e"];
    const symbolsB = ["a", "x", "c", "y", "e"];
    const expected = [0, -1, 2, -1, 4]; // 'b' and 'd' don't map
    expect(findLongestCommonSubsequence(symbolsA, symbolsB)).toEqual(expected);
  });

  it("should handle empty arrays", () => {
    expect(findLongestCommonSubsequence([], [])).toEqual([]);
    expect(findLongestCommonSubsequence(["a", "b"], [])).toEqual([-1, -1]);
    expect(findLongestCommonSubsequence([], ["a", "b"])).toEqual([]);
  });

  it("should respect maxIndexDiff parameter", () => {
    const symbolsA = ["a", "b", "c", "d", "e"];
    const symbolsB = ["x", "y", "a", "b", "c", "d", "e"];

    // With default maxIndexDiff (10), all should match
    const resultDefault = findLongestCommonSubsequence(symbolsA, symbolsB);
    expect(resultDefault).toEqual([2, 3, 4, 5, 6]);

    // With maxIndexDiff = 1, none should match because the index difference is too large
    const resultSmallDiff = findLongestCommonSubsequence(symbolsA, symbolsB, 1);
    expect(resultSmallDiff).toEqual([-1, -1, -1, -1, -1]);
  });

  it("should handle complex cases", () => {
    const symbolsA = [
      "function",
      "foo",
      "(",
      "x",
      ",",
      "y",
      ")",
      "{",
      "return",
      "x",
      "+",
      "y",
      ";",
      "}",
    ];
    const symbolsB = [
      "function",
      "foo",
      "(",
      "a",
      ",",
      "b",
      ")",
      "{",
      "return",
      "a",
      "+",
      "b",
      ";",
      "}",
    ];

    // The symbols at indices 0, 1, 2, 4, 6, 7, 8, 10, 12, 13 should match
    // The symbols at indices 3, 5, 9, 11 are different but in the same position
    const result = findLongestCommonSubsequence(symbolsA, symbolsB);

    // Check that matching symbols are mapped correctly
    expect(result[0]).toBe(0); // 'function'
    expect(result[1]).toBe(1); // 'foo'
    expect(result[2]).toBe(2); // '('
    expect(result[4]).toBe(4); // ','
    expect(result[6]).toBe(6); // ')'
    expect(result[7]).toBe(7); // '{'
    expect(result[8]).toBe(8); // 'return'
    expect(result[10]).toBe(10); // '+'
    expect(result[12]).toBe(12); // ';'
    expect(result[13]).toBe(13); // '}'
  });
});
