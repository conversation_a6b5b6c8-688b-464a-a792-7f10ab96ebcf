import {
  ChatMode,
  ChatRequestNode,
  Exchange,
} from "@augment-internal/sidecar-libs/src/chat/chat-types";
import { ToolDefinition } from "@augment-internal/sidecar-libs/src/tools/tool-types";
import { ChatResult } from "@augment-internal/sidecar-libs/src/client-interfaces/api-client-types";
import { getAPIClient } from "../client-interfaces/api-client";

export async function llmCallStream(
  prompt: string,
  requestId: string,
  chatHistory: Exchange[],
  toolDefinitions: ToolDefinition[],
  requestNodes: ChatRequestNode[],
  mode: ChatMode,
  silent: boolean,
  modelName?: string,
): Promise<AsyncIterable<ChatResult>> {
  return await getAPIClient().chatStream(
    prompt,
    requestId,
    chatHistory,
    toolDefinitions,
    requestNodes,
    mode,
    modelName,
    silent,
  );
}
