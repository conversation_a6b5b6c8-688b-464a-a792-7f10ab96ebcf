import { truncate<PERSON><PERSON><PERSON>el<PERSON> } from "./strings";
import {
  TruncatedContentMetadata,
  TruncatedContentType,
  UntruncatedContentManager,
} from "./untruncated-content-manager";
import { LocalToolType } from "../tools/tool-types";
import { getAgentSessionEventReporter } from "../metrics/agent-session-event-reporter";
import { AgentSessionEventName } from "../metrics/types";
import { getLogger } from "../logging";

/**
 * Options for truncating content
 */
export interface TruncateOptions {
  /** Maximum length in bytes */
  maxBytes: number;
  /** Type of content being truncated */
  contentType: TruncatedContentType;
  /** Tool use ID if applicable */
  toolUseId?: string;
  /** Request ID if applicable */
  requestId?: string;
  /** Conversation ID if applicable */
  conversationId?: string;
  /** Maximum number of lines to show from the end (0 = use default truncation) */
  maxLinesTerminalProcessOutput?: number;
  /** Additional message to reduce untruncated content usage */
  truncationFooterAdditionText?: string;
  /** Tool type that generated the content being truncated */
  toolType?: LocalToolType;
  /** Original command that generated the content (for launch-process tools) */
  originalCommand?: string;
}

/**
 * Result of truncating content
 */
export interface TruncateResult {
  /** The truncated content */
  truncatedContent: string;
  /** Metadata about the truncation */
  metadata?: TruncatedContentMetadata;
}

/**
 * Reports a content truncation event to the backend for telemetry purposes.
 * This function is non-blocking and will not throw errors to avoid disrupting
 * the truncation functionality.
 *
 * @param originalCharCount Character count of original content before truncation
 * @param originalLineCount Line count of original content before truncation
 * @param truncatedCharCount Character count of content after truncation
 * @param truncatedLineCount Line count of content after truncation
 * @param toolType Tool type that generated the content being truncated
 * @param conversationId Conversation ID for context
 */
function reportTruncationEvent(
  originalCharCount: number,
  originalLineCount: number,
  truncatedCharCount: number,
  truncatedLineCount: number,
  toolType: string,
  conversationId?: string,
): void {
  try {
    const reporter = getAgentSessionEventReporter();

    // Report the event asynchronously without awaiting to avoid blocking
    void reporter.reportEvent({
      eventName: AgentSessionEventName.contentTruncation,
      conversationId: conversationId || "unknown",
      eventData: {
        contentTruncationData: {
          originalCharCount,
          originalLineCount,
          truncatedCharCount,
          truncatedLineCount,
          toolType,
        },
      },
    });
  } catch (error: unknown) {
    // Log warning but don't throw to avoid disrupting truncation functionality
    const logger = getLogger("truncation-utils");
    logger.warn("Failed to report truncation event:", error);
  }
}

/**
 * Truncates content in the middle and keep some metadata
 *
 * @param content The content to truncate
 * @param options Truncation options
 * @param contentManager The untruncated content manager
 * @param enableUntruncatedContentStorage Whether to store untruncated content (feature flag)
 * @returns The truncated content with metadata and the metadata object
 */
export async function truncateWithMetadata(
  content: string,
  options: TruncateOptions,
  contentManager: UntruncatedContentManager,
  enableUntruncatedContentStorage: boolean,
): Promise<TruncateResult> {
  const lines = content.split("\n");
  const totalLines = lines.length;
  const totalSize = content.length;

  // Handle maxLinesTerminalProcessOutput truncation (if enabled)
  const maxLinesTerminalProcessOutput =
    options.maxLinesTerminalProcessOutput ?? 0;
  if (
    maxLinesTerminalProcessOutput > 0 &&
    totalLines > maxLinesTerminalProcessOutput
  ) {
    const lastNLines = lines.slice(-maxLinesTerminalProcessOutput);
    const truncatedContent = lastNLines.join("\n");

    const shownRange: [number, number] = [
      totalLines - maxLinesTerminalProcessOutput + 1,
      totalLines,
    ];

    const metadata = await contentManager.storeUntruncatedContent(
      content,
      options.contentType,
      shownRange,
      options.toolUseId,
      options.requestId,
      options.conversationId,
      options.toolType,
      options.originalCommand,
    );

    // Add standardized metadata footer
    const metadataFooter = createTruncationFooter(
      metadata,
      options.truncationFooterAdditionText,
    );
    const contentWithFooter = `${truncatedContent}\n\n${metadataFooter}`;

    // Report truncation event to backend for telemetry
    const truncatedLines = truncatedContent.split("\n");
    reportTruncationEvent(
      totalSize,
      totalLines,
      truncatedContent.length,
      truncatedLines.length,
      options.toolType || "unknown",
      options.conversationId,
    );

    return {
      truncatedContent: contentWithFooter,
      metadata,
    };
  }

  if (totalSize <= options.maxBytes) {
    return {
      truncatedContent: content,
    };
  }

  const { truncatedText, shownRangeWhenTruncated } = truncateMiddleHelper(
    content,
    options.maxBytes,
  );

  // Return truncated content without metadata when storage is disabled
  if (!enableUntruncatedContentStorage) {
    return {
      truncatedContent: truncatedText,
    };
  }

  // Store the full content based on feature flag
  const metadata = await contentManager.storeUntruncatedContent(
    content,
    options.contentType,
    shownRangeWhenTruncated || [1, totalLines],
    options.toolUseId,
    options.requestId,
    options.conversationId,
    options.toolType,
    options.originalCommand,
  );

  // Add standardized metadata footer
  const metadataFooter = createTruncationFooter(
    metadata,
    options.truncationFooterAdditionText,
  );
  const truncatedContent = `${truncatedText}\n\n${metadataFooter}`;

  // Report truncation event to backend for telemetry
  const truncatedLines = truncatedText.split("\n");
  reportTruncationEvent(
    totalSize,
    totalLines,
    truncatedText.length,
    truncatedLines.length,
    options.toolType || "unknown",
    options.conversationId,
  );

  return {
    truncatedContent,
    metadata,
  };
}

/**
 * Creates a standardized footer for truncated content
 *
 * @param metadata The truncation metadata
 * @returns A formatted footer string
 */
export function createTruncationFooter(
  metadata: TruncatedContentMetadata,
  truncationFooterAdditionText: string | undefined,
): string {
  const { shownRange, totalLines, referenceId } = metadata;

  // Check if we have a complex range (showing both beginning and end)
  let rangeText: string;
  if (shownRange.length === 4) {
    // Format as "lines 1-50, 150-200 of 200 lines"
    rangeText = `lines ${shownRange[0]}-${shownRange[1]}, ${shownRange[2]}-${shownRange[3]} of ${totalLines} lines`;
  } else {
    // Format as "lines 1-50 of 200 lines"
    rangeText = `lines ${shownRange[0]}-${shownRange[1]} of ${totalLines} lines`;
  }

  return (
    `[This result was truncated. Showing ${rangeText}. ` +
    `Use view-range-untruncated or search-untruncated tools to access the full content. ` +
    `Reference ID: ${referenceId}]` +
    (truncationFooterAdditionText ? `\n${truncationFooterAdditionText}` : "")
  );
}
