import { Diagnostic } from "$clients/sidecar/libs/src/tools/sidecar-tools/str-replace-editor-tool/diagnostic-types";

/**
 * Helper function to format a single line with line number
 */
function formatLine(lineIndex: number, fileLines: string[]): string {
  const lineText = fileLines[lineIndex];
  const lineNum = lineIndex + 1; // Convert to 1-based
  return `${String(lineNum).padStart(6)}\t${lineText}`;
}

/**
 * Helper function to add lines to result within a range
 */
export function addLinesInRange(
  result: string[],
  fileLines: string[],
  startIndex: number,
  endIndex: number,
): void {
  for (let i = startIndex; i <= endIndex; i++) {
    if (i >= 0 && i < fileLines.length) {
      result.push(formatLine(i, fileLines));
    }
  }
}

/**
 * Helper function to calculate context boundaries
 */
export function getContextBounds(
  diagnosticStart: number,
  diagnosticEnd: number,
  maxContextLines: number,
  fileLength: number,
): { contextStart: number; contextEnd: number } {
  return {
    contextStart: Math.max(0, diagnosticStart - maxContextLines),
    contextEnd: Math.min(fileLength - 1, diagnosticEnd + maxContextLines),
  };
}

/**
 * Helper function to format diagnostic message
 */
export function formatDiagnosticMessage(diagnostic: Diagnostic): string {
  const startLine = diagnostic.range.start.line + 1;
  const endLine = diagnostic.range.end.line + 1;
  return `L${startLine}-${endLine}: ${diagnostic.message}`;
}

/**
 * Helper function to add remaining diagnostics message
 */
export function addRemainingDiagnosticsMessage(
  result: string[],
  remainingCount: number,
): void {
  if (remainingCount > 0) {
    const plural = remainingCount === 1 ? "" : "s";
    result.push(`... and ${remainingCount} more issue${plural} for this file`);
    result.push("");
  }
}

/**
 * Helper function to format diagnostic with context lines
 */
export function formatDiagnosticWithContext(
  result: string[],
  diagnostic: Diagnostic,
  fileLines: string[],
  maxContextLines: number,
  maxDiagnosticLineRange?: number,
): void {
  const diagnosticStart = diagnostic.range.start.line;
  const diagnosticEnd = diagnostic.range.end.line;
  const lineRange = diagnosticEnd - diagnosticStart + 1;

  // Add the diagnostic message
  result.push(formatDiagnosticMessage(diagnostic));

  // Check if diagnostic line range exceeds the limit
  if (maxDiagnosticLineRange && lineRange > maxDiagnosticLineRange) {
    // Calculate context bounds (don't count toward diagnostic limit)
    const { contextStart, contextEnd } = getContextBounds(
      diagnosticStart,
      diagnosticEnd,
      maxContextLines,
      fileLines.length,
    );

    // Show context lines before diagnostic range
    addLinesInRange(result, fileLines, contextStart, diagnosticStart - 1);

    // Show truncated diagnostic content
    const startContextLines = Math.floor(maxDiagnosticLineRange / 2);
    const endContextLines = maxDiagnosticLineRange - startContextLines;

    // Show lines from start of diagnostic
    addLinesInRange(
      result,
      fileLines,
      diagnosticStart,
      diagnosticStart + startContextLines - 1,
    );

    // Add separator indicating omitted lines
    const omittedLines = lineRange - maxDiagnosticLineRange;
    result.push(`      \t... (${omittedLines} more lines omitted) ...`);

    // Show lines from end of diagnostic
    addLinesInRange(
      result,
      fileLines,
      diagnosticEnd - endContextLines + 1,
      diagnosticEnd,
    );

    // Show context lines after diagnostic range
    addLinesInRange(result, fileLines, diagnosticEnd + 1, contextEnd);
  } else {
    // Show full diagnostic with context
    const { contextStart, contextEnd } = getContextBounds(
      diagnosticStart,
      diagnosticEnd,
      maxContextLines,
      fileLines.length,
    );
    addLinesInRange(result, fileLines, contextStart, contextEnd);
  }

  // Add separator between diagnostics
  result.push("");
}

/**
 * Format diagnostics with context lines
 * @param diagnosticsMap Map of file paths to diagnostics
 * @param readFile Function to read file content
 * @param maxContextLines Maximum number of context lines to show before and after diagnostic (default: 3)
 * @param maxNumDiagnosticsPerFile Maximum number of diagnostics to show per file (default: unlimited)
 * @param maxDiagnosticLineRange Maximum line range to show for a diagnostic; if exceeded, show start, end and ... in between (default: unlimited)
 * @returns Formatted string with diagnostics and context
 */
export async function formatDiagnostics(
  diagnosticsMap: Map<string, Diagnostic[]>,
  readFile: (filePath: string) => Promise<string | undefined>,
  maxContextLines: number = 3,
  maxNumDiagnosticsPerFile?: number,
  maxDiagnosticLineRange?: number,
): Promise<string> {
  const result: string[] = [];

  for (const [diagPath, diagnostics] of diagnosticsMap.entries()) {
    if (diagnostics.length === 0) {
      continue;
    }

    result.push(diagPath);

    // Limit the number of diagnostics per file if specified
    const diagnosticsToShow = maxNumDiagnosticsPerFile
      ? diagnostics.slice(0, maxNumDiagnosticsPerFile)
      : diagnostics;

    const remainingDiagnostics = diagnostics.length - diagnosticsToShow.length;

    try {
      // Get the document content
      const fileLines = (await readFile(diagPath))?.split("\n") ?? [];

      // Format each diagnostic with context
      for (const diagnostic of diagnosticsToShow) {
        formatDiagnosticWithContext(
          result,
          diagnostic,
          fileLines,
          maxContextLines,
          maxDiagnosticLineRange,
        );
      }

      addRemainingDiagnosticsMessage(result, remainingDiagnostics);
    } catch (error) {
      // If we can't get the document, just show the diagnostic without context
      for (const diagnostic of diagnosticsToShow) {
        result.push(formatDiagnosticMessage(diagnostic));
      }

      addRemainingDiagnosticsMessage(result, remainingDiagnostics);
    }
  }

  return result.join("\n");
}
