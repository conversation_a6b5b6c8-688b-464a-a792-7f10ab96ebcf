import { hasProperty } from "./types-utils";

// A util function to perform `AbortSignal.any`
export function abortSignalAny(signals: AbortSignal[]): AbortSignal {
  // Check if the `AbortSignal` has `any` method
  if (
    hasProperty(AbortSignal, "any") &&
    typeof AbortSignal.any === "function"
  ) {
    return AbortSignal.any(signals);
  }

  const controller = new AbortController();

  // Check if any signal is already aborted
  if (signals.some((signal) => signal.aborted)) {
    controller.abort();
    return controller.signal;
  }

  // Store cleanup functions for each listener
  const cleanupFns: (() => void)[] = [];

  const abortHandler = () => {
    controller.abort();
    // Clean up all listeners when any signal aborts
    cleanupFns.forEach((cleanup) => cleanup());
  };

  // Add listeners and store cleanup functions
  for (const signal of signals) {
    signal.addEventListener("abort", abortHandler);
    cleanupFns.push(() => signal.removeEventListener("abort", abortHandler));
  }

  return controller.signal;
}
