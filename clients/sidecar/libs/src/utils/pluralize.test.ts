import { pluralize, pluralizeWithCount } from "./pluralize";

describe("pluralize", () => {
  it("should return singular form for count of 1", () => {
    expect(pluralize(1, "file")).toBe("file");
    expect(pluralize(1, "agent")).toBe("agent");
    expect(pluralize(1, "thread")).toBe("thread");
  });

  it("should return plural form for count greater than 1", () => {
    expect(pluralize(2, "file")).toBe("files");
    expect(pluralize(3, "agent")).toBe("agents");
    expect(pluralize(5, "thread")).toBe("threads");
  });

  it("should return plural form for count of 0", () => {
    expect(pluralize(0, "file")).toBe("files");
    expect(pluralize(0, "agent")).toBe("agents");
    expect(pluralize(0, "thread")).toBe("threads");
  });

  it("should add 's' for default plural form", () => {
    expect(pluralize(2, "box")).toBe("boxs"); // Simple 's' addition
    expect(pluralize(3, "child")).toBe("childs"); // Simple 's' addition
    expect(pluralize(2, "person")).toBe("persons"); // Simple 's' addition
  });

  it("should use custom plural form when provided", () => {
    expect(pluralize(2, "box", "boxes")).toBe("boxes");
    expect(pluralize(3, "child", "children")).toBe("children");
    expect(pluralize(2, "person", "people")).toBe("people");
  });

  it("should handle edge cases", () => {
    expect(pluralize(1, "")).toBe("");
    expect(pluralize(2, "")).toBe("s");
    expect(pluralize(-1, "word")).toBe("word"); // -1 is "one" in English plural rules
  });

  it("should use Intl.PluralRules for en-US locale", () => {
    // Test that it uses proper plural rules for English
    expect(pluralize(1, "item")).toBe("item"); // 1 is "one" in English
    expect(pluralize(21, "item")).toBe("items"); // 21 is "other" in English (not "one")
    expect(pluralize(101, "item")).toBe("items"); // 101 is "other" in English
  });

  it("should handle fractional numbers", () => {
    expect(pluralize(1.5, "item")).toBe("items"); // 1.5 is not "one" in English
    expect(pluralize(0.5, "item")).toBe("items");
  });
});

describe("pluralizeWithCount", () => {
  it("should return count with singular form for count of 1", () => {
    expect(pluralizeWithCount(1, "file")).toBe("1 file");
    expect(pluralizeWithCount(1, "agent")).toBe("1 agent");
    expect(pluralizeWithCount(1, "thread")).toBe("1 thread");
  });

  it("should return count with plural form for count greater than 1", () => {
    expect(pluralizeWithCount(2, "file")).toBe("2 files");
    expect(pluralizeWithCount(3, "agent")).toBe("3 agents");
    expect(pluralizeWithCount(5, "thread")).toBe("5 threads");
  });

  it("should return count with plural form for count of 0", () => {
    expect(pluralizeWithCount(0, "file")).toBe("0 files");
    expect(pluralizeWithCount(0, "agent")).toBe("0 agents");
    expect(pluralizeWithCount(0, "thread")).toBe("0 threads");
  });

  it("should add 's' for default plural form with count", () => {
    expect(pluralizeWithCount(5, "box")).toBe("5 boxs");
    expect(pluralizeWithCount(2, "child")).toBe("2 childs");
    expect(pluralizeWithCount(3, "person")).toBe("3 persons");
  });

  it("should use custom plural form with count", () => {
    expect(pluralizeWithCount(5, "box", "boxes")).toBe("5 boxes");
    expect(pluralizeWithCount(2, "child", "children")).toBe("2 children");
    expect(pluralizeWithCount(3, "person", "people")).toBe("3 people");
  });
});
