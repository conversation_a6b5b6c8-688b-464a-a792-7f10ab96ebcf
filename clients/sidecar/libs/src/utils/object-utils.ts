type AnyObject = {
  [key: string]: {} | Array<any> | string | boolean | number | null | undefined;
};

export function getPropertySizes(obj: AnyObject, indent: string = ""): string {
  const result: string[] = []; // Use an array as a buffer

  if (typeof obj !== "object" || obj === null) {
    return getItemSize(obj);
  }

  for (const [key, value] of Object.entries(obj)) {
    if (Array.isArray(value)) {
      // Handle arrays
      result.push(
        `${indent}${key}: (array) ${value.length} (array length) ${
          JSON.stringify(value).length
        } (char length)`,
      );
      const CUTOFF_LENGTH = 20;
      value.slice(0, CUTOFF_LENGTH).forEach((item, index) => {
        // cutoff for oversized arrays
        result.push(
          `${indent}  [${index}]: ${getPropertySizes(item as AnyObject, indent + "  ")}`,
        );
      });
      if (value.length > CUTOFF_LENGTH) {
        result.push(`${indent}  ${value.length - CUTOFF_LENGTH} more items...`);
      }
    } else if (typeof value === "object" && value !== null) {
      // Handle objects
      result.push(
        `${indent}${key}: (object) ${Object.keys(value).length} (object size) ${
          JSON.stringify(value).length
        } (char length)`,
      );
      result.push(getPropertySizes(value as AnyObject, indent + "  ")); // Recursive call with increased indentation
    } else {
      // Handle primitives
      result.push(`${indent}${key}: ${getItemSize(value)}`);
    }
  }

  return result.join("\n"); // Join all array elements into a single string with new lines
}

// Helper function to determine size of primitive values
function getItemSize(value: any): string {
  if (typeof value === "string") {
    return `${value.length} (string length)`; // For strings, return length
  } else if (
    typeof value === "boolean" ||
    value === null ||
    typeof value === "number"
  ) {
    return "1"; // For booleans, null, and numbers, return 1
  } else {
    return "N/A"; // For any other types if necessary
  }
}
