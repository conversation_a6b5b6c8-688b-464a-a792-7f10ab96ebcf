import { FrontmatterParser } from "./FrontmatterParser";

describe("FrontmatterParser", () => {
  const sampleMarkdown = `---
title: Test Document
description: This is a test
alwaysApply: true
---

# Test Document

This is a test document with frontmatter.`;

  const markdownWithoutFrontmatter = `# Test Document

This is a test document without frontmatter.`;

  describe("hasFrontmatter", () => {
    it("should return true when frontmatter exists", () => {
      expect(FrontmatterParser.hasFrontmatter(sampleMarkdown)).toBe(true);
    });

    it("should return false when frontmatter does not exist", () => {
      expect(FrontmatterParser.hasFrontmatter(markdownWithoutFrontmatter)).toBe(
        false,
      );
    });
  });

  describe("extractFrontmatter", () => {
    it("should extract frontmatter content", () => {
      const frontmatter = FrontmatterParser.extractFrontmatter(sampleMarkdown);
      expect(frontmatter).toContain("title: Test Document");
      expect(frontmatter).toContain("description: This is a test");
      expect(frontmatter).toContain("alwaysApply: true");
    });

    it("should return null when no frontmatter exists", () => {
      expect(
        FrontmatterParser.extractFrontmatter(markdownWithoutFrontmatter),
      ).toBeNull();
    });
  });

  describe("extractContent", () => {
    it("should extract content after frontmatter", () => {
      const content = FrontmatterParser.extractContent(sampleMarkdown);
      expect(content).toContain("# Test Document");
      expect(content).toContain("This is a test document with frontmatter.");
      expect(content).not.toContain("title: Test Document");
    });

    it("should return the original text when no frontmatter exists", () => {
      expect(FrontmatterParser.extractContent(markdownWithoutFrontmatter)).toBe(
        markdownWithoutFrontmatter,
      );
    });
  });

  describe("parseBoolean", () => {
    it("should parse boolean values correctly", () => {
      expect(
        FrontmatterParser.parseBoolean(sampleMarkdown, "alwaysApply"),
      ).toBe(true);

      const falseSample = sampleMarkdown.replace(
        "alwaysApply: true",
        "alwaysApply: false",
      );
      expect(FrontmatterParser.parseBoolean(falseSample, "alwaysApply")).toBe(
        false,
      );
    });

    it("should return default value when key does not exist", () => {
      expect(
        FrontmatterParser.parseBoolean(sampleMarkdown, "nonExistentKey"),
      ).toBe(true);
      expect(
        FrontmatterParser.parseBoolean(sampleMarkdown, "nonExistentKey", false),
      ).toBe(false);
    });
  });

  describe("parseString", () => {
    it("should parse string values correctly", () => {
      expect(FrontmatterParser.parseString(sampleMarkdown, "title")).toBe(
        "Test Document",
      );
      expect(FrontmatterParser.parseString(sampleMarkdown, "description")).toBe(
        "This is a test",
      );
    });

    it("should return default value when key does not exist", () => {
      expect(
        FrontmatterParser.parseString(sampleMarkdown, "nonExistentKey"),
      ).toBe("");
      expect(
        FrontmatterParser.parseString(
          sampleMarkdown,
          "nonExistentKey",
          "default",
        ),
      ).toBe("default");
    });
  });

  describe("updateFrontmatter", () => {
    it("should update existing frontmatter values", () => {
      const updated = FrontmatterParser.updateFrontmatter(
        sampleMarkdown,
        "alwaysApply",
        false,
      );
      expect(updated).toContain("alwaysApply: false");
      expect(updated).toContain("title: Test Document");
    });

    it("should add new frontmatter values", () => {
      const updated = FrontmatterParser.updateFrontmatter(
        sampleMarkdown,
        "newKey",
        "newValue",
      );
      expect(updated).toContain('newKey: "newValue"');
      expect(updated).toContain("alwaysApply: true");
    });

    it("should create frontmatter when none exists", () => {
      const updated = FrontmatterParser.updateFrontmatter(
        markdownWithoutFrontmatter,
        "alwaysApply",
        true,
      );
      expect(updated).toContain("---\nalwaysApply: true\n---");
      expect(updated).toContain("# Test Document");
    });
  });

  describe("createFrontmatter", () => {
    it("should create frontmatter with multiple properties", () => {
      const properties = {
        title: "New Title",
        description: "New Description",
        alwaysApply: false,
        tags: ["tag1", "tag2"],
      };

      const updated = FrontmatterParser.createFrontmatter(
        markdownWithoutFrontmatter,
        properties,
      );
      expect(updated).toContain('title: "New Title"');
      expect(updated).toContain('description: "New Description"');
      expect(updated).toContain("alwaysApply: false");
      expect(updated).toContain("# Test Document");
    });

    it("should replace existing frontmatter", () => {
      const properties = {
        newTitle: "Replaced Title",
        newDescription: "Replaced Description",
      };

      const updated = FrontmatterParser.createFrontmatter(
        sampleMarkdown,
        properties,
      );
      expect(updated).toContain('newTitle: "Replaced Title"');
      expect(updated).toContain('newDescription: "Replaced Description"');
      expect(updated).not.toContain("title: Test Document");
      expect(updated).toContain("# Test Document");
    });
  });
});
