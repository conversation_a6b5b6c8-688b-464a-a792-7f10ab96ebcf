const pluralRules = new Intl.PluralRules("en-US");

/**
 * Pluralize a word based on count using Intl.PluralRules for en-US locale
 * @param count The count to determine if plural is needed
 * @param singular The singular form of the word
 * @param plural Optional custom plural form (defaults to singular + 's')
 * @returns The pluralized string
 *
 * @example
 * pluralize(1, 'file') // 'file'
 * pluralize(2, 'file') // 'files'
 * pluralize(2, 'box', 'boxes') // 'boxes'
 * pluralize(2, 'child', 'children') // 'children'
 * pluralize(0, 'item') // 'items' (0 is plural in English)
 * pluralize(1, 'item') // 'item'
 * pluralize(2, 'item') // 'items'
 */
export function pluralize(
  count: number,
  singular: string,
  plural?: string,
): string {
  const rule = pluralRules.select(count);

  // For English, if the rule is 'one', return singular form
  if (rule === "one") {
    return singular;
  }

  // Use custom plural form or default (add 's')
  return plural || `${singular}s`;
}

/**
 * Pluralize with count prefix using Intl.PluralRules for en-US locale
 * @param count The count to determine if plural is needed
 * @param singular The singular form of the word
 * @param plural Optional custom plural form (defaults to singular + 's')
 * @returns The count followed by the pluralized string
 *
 * @example
 * pluralizeWithCount(1, 'file') // '1 file'
 * pluralizeWithCount(3, 'file') // '3 files'
 * pluralizeWithCount(5, 'box', 'boxes') // '5 boxes'
 */
export function pluralizeWithCount(
  count: number,
  singular: string,
  plural?: string,
): string {
  return `${count} ${pluralize(count, singular, plural)}`;
}
