import { FrontmatterParser } from "@augment-internal/sidecar-libs/src/utils/FrontmatterParser";
import { type Rule, RuleType } from "../chat/chat-types";

export class RulesParser {
  private static ALWAYS_APPLY_FRONTMATTER_KEY = "alwaysApply";
  private static DESCRIPTION_FRONTMATTER_KEY = "description";
  private static TYPE_FRONTMATTER_KEY = "type";

  // Valid type values for the frontmatter
  private static VALID_TYPE_VALUES = [
    "always_apply",
    "manual",
    "agent_requested",
  ] as const;

  // Default type for new rules (based on user preference for MANUAL)
  private static DEFAULT_RULE_TYPE = RuleType.MANUAL;

  public static parseRuleFile(content: string, fileName: string): Rule {
    const description = FrontmatterParser.parseString(
      content,
      this.DESCRIPTION_FRONTMATTER_KEY,
      "",
    );
    const markdownContent = FrontmatterParser.extractContent(content);
    return {
      type: this.getRuleTypeFromContent(content),
      path: fileName,
      content: markdownContent,
      description: description || undefined,
    };
  }
  public static formatRuleFileForMarkdown(rule: Rule): string {
    let content = rule.content;

    // Add explicit type field
    content = FrontmatterParser.updateFrontmatter(
      content,
      this.TYPE_FRONTMATTER_KEY,
      this.mapRuleTypeToString(rule.type),
    );

    // Add description if present
    if (rule.description) {
      content = FrontmatterParser.updateFrontmatter(
        content,
        this.DESCRIPTION_FRONTMATTER_KEY,
        rule.description,
      );
    }

    return content;
  }

  public static getAlwaysApplyFrontmatterKey(content: string): boolean {
    return FrontmatterParser.parseBoolean(
      content,
      this.ALWAYS_APPLY_FRONTMATTER_KEY,
      false,
    );
  }

  public static extractContent(content: string): string {
    return FrontmatterParser.extractContent(content);
  }

  public static updateAlwaysApplyFrontmatterKey(
    content: string,
    newValue: boolean,
  ): string {
    return FrontmatterParser.updateFrontmatter(
      content,
      this.ALWAYS_APPLY_FRONTMATTER_KEY,
      newValue,
    );
  }

  public static getDescriptionFrontmatterKey(content: string): string {
    return FrontmatterParser.parseString(
      content,
      this.DESCRIPTION_FRONTMATTER_KEY,
      "",
    );
  }

  public static updateDescriptionFrontmatterKey(
    content: string,
    newValue: string,
  ): string {
    return FrontmatterParser.updateFrontmatter(
      content,
      this.DESCRIPTION_FRONTMATTER_KEY,
      newValue,
    );
  }

  /**
   * Maps string type values to RuleType enum values
   */
  private static mapStringToRuleType(typeString: string): RuleType {
    switch (typeString.toLowerCase()) {
      case "always_apply":
        return RuleType.ALWAYS_ATTACHED;
      case "manual":
        return RuleType.MANUAL;
      case "agent_requested":
        return RuleType.AGENT_REQUESTED;
      default:
        return this.DEFAULT_RULE_TYPE;
    }
  }

  /**
   * Maps RuleType enum values to string type values
   */
  private static mapRuleTypeToString(ruleType: RuleType): string {
    switch (ruleType) {
      case RuleType.ALWAYS_ATTACHED:
        return "always_apply";
      case RuleType.MANUAL:
        return "manual";
      case RuleType.AGENT_REQUESTED:
        return "agent_requested";
      default:
        return "manual";
    }
  }

  /**
   * Validates if a type string is valid
   */
  private static isValidTypeValue(typeString: string): boolean {
    return (this.VALID_TYPE_VALUES as readonly string[]).includes(
      typeString.toLowerCase(),
    );
  }

  /**
   * Gets the type field from frontmatter
   */
  public static getTypeFrontmatterKey(content: string): string {
    return FrontmatterParser.parseString(
      content,
      this.TYPE_FRONTMATTER_KEY,
      "",
    );
  }

  /**
   * Updates the type field in frontmatter
   */
  public static updateTypeFrontmatterKey(
    content: string,
    newValue: RuleType,
  ): string {
    const typeString = this.mapRuleTypeToString(newValue);
    return FrontmatterParser.updateFrontmatter(
      content,
      this.TYPE_FRONTMATTER_KEY,
      typeString,
    );
  }

  public static getRuleTypeFromContent(content: string): RuleType {
    // Prefer checking for explicit type field
    const explicitType = this.getTypeFrontmatterKey(content);
    if (explicitType && this.isValidTypeValue(explicitType)) {
      return this.mapStringToRuleType(explicitType);
    }

    // Fall back to legacy logic for backward compatibility
    const shouldAlwaysApply = this.getAlwaysApplyFrontmatterKey(content);
    const description = this.getDescriptionFrontmatterKey(content);

    if (shouldAlwaysApply) {
      return RuleType.ALWAYS_ATTACHED;
    } else if (description && description.trim() !== "") {
      return RuleType.AGENT_REQUESTED;
    } else {
      return RuleType.MANUAL;
    }
  }
}

export const AUGMENT_DIRECTORY_ROOT = ".augment";
export const AUGMENT_RULES_FOLDER = "rules";
export const AUGMENT_GUIDELINES_FILE = ".augment-guidelines";
