/**
 * A utility class for parsing and manipulating YAML frontmatter in markdown files.
 * in the format:
 * ---
 * key: value
 * ---
 */
export class FrontmatterParser {
  private static readonly frontmatterRegex = /^---\s*\n([\s\S]*?)\n---\s*\n/;

  /**
   * Checks if the given text contains YAML frontmatter.
   * @param text The text to check
   * @returns True if the text contains frontmatter, false otherwise
   */
  public static hasFrontmatter(text: string): boolean {
    return this.frontmatterRegex.test(text);
  }

  /**
   * Extracts the frontmatter content from the given text.
   * @param text The text containing frontmatter
   * @returns The frontmatter content or null if no frontmatter is found
   */
  public static extractFrontmatter(text: string): string | null {
    const match = text.match(this.frontmatterRegex);
    return match && match[1] ? match[1] : null;
  }

  /**
   * Extracts the content after the frontmatter.
   * @param text The text containing frontmatter
   * @returns The content after the frontmatter, or the original text if no frontmatter is found
   */
  public static extractContent(text: string): string {
    return text.replace(this.frontmatterRegex, "");
  }

  /**
   * Parses a boolean value from the frontmatter.
   * @param text The text containing frontmatter
   * @param key The key to look for in the frontmatter
   * @param defaultValue The default value to return if the key is not found
   * @returns The parsed boolean value or the default value
   */
  public static parseBoolean(
    text: string,
    key: string,
    defaultValue = true,
  ): boolean {
    const frontmatter = this.extractFrontmatter(text);

    if (frontmatter) {
      const keyRegex = new RegExp(`${key}\\s*:\\s*(true|false)`, "i");
      const match = frontmatter.match(keyRegex);

      if (match && match[1]) {
        return match[1].toLowerCase() === "true";
      }
    }

    return defaultValue;
  }

  /**
   * Parses a string value from the frontmatter.
   * @param text The text containing frontmatter
   * @param key The key to look for in the frontmatter
   * @param defaultValue The default value to return if the key is not found
   * @returns The parsed string value or the default value
   */
  public static parseString(
    text: string,
    key: string,
    defaultValue = "",
  ): string {
    const frontmatter = this.extractFrontmatter(text);

    if (frontmatter) {
      const keyRegex = new RegExp(`${key}\\s*:\\s*["']?([^"'\n]*)["']?`, "i");
      const match = frontmatter.match(keyRegex);

      if (match && match[1]) {
        return match[1].trim();
      }
    }

    return defaultValue;
  }

  /**
   * Updates or adds a key-value pair in the frontmatter.
   * @param text The text containing frontmatter
   * @param key The key to update or add
   * @param value The value to set
   * @returns The updated text with the new frontmatter
   */
  public static updateFrontmatter(
    text: string,
    key: string,
    value: any,
  ): string {
    const match = text.match(this.frontmatterRegex);

    // Format the value appropriately based on its type
    const formattedValue =
      typeof value === "string" && !/^(true|false)$/.test(value.toLowerCase())
        ? `"${value}"` // Wrap strings in quotes unless they are boolean strings
        : String(value); // Convert any non-string value to string

    if (match) {
      // Frontmatter exists, update or add the key
      const frontmatter = match[1];
      const keyRegex = new RegExp(`(${key}\\s*:\\s*)([^\\n]*)`, "i");

      if (frontmatter.match(keyRegex)) {
        // Replace existing key value
        const updatedFrontmatter = frontmatter.replace(
          keyRegex,
          `$1${formattedValue}`,
        );
        return text.replace(
          this.frontmatterRegex,
          `---\n${updatedFrontmatter}---\n`,
        );
      } else {
        // Add key to existing frontmatter
        // Make sure there's a newline at the end of the existing frontmatter
        const frontmatterWithNewline = frontmatter.endsWith("\n")
          ? frontmatter
          : frontmatter + "\n";
        const updatedFrontmatter = `${frontmatterWithNewline}${key}: ${formattedValue}\n`;
        return text.replace(
          this.frontmatterRegex,
          `---\n${updatedFrontmatter}---\n`,
        );
      }
    } else {
      // No frontmatter, add it
      return `---\n${key}: ${formattedValue}\n---\n\n${text}`;
    }
  }

  /**
   * Creates a new frontmatter object with the specified key-value pairs.
   * @param content The content to add frontmatter to
   * @param properties An object containing the key-value pairs to add to the frontmatter
   * @returns The content with the new frontmatter
   */
  public static createFrontmatter(
    content: string,
    properties: Record<string, any>,
  ): string {
    // Start with an empty frontmatter
    let result = content;

    // Remove any existing frontmatter
    if (this.hasFrontmatter(result)) {
      result = this.extractContent(result);
    }

    // Add each property to the frontmatter
    for (const [key, value] of Object.entries(properties)) {
      result = this.updateFrontmatter(result, key, value);
    }

    return result;
  }
}
