/**
 * Computes a fast hash of the input string using the FNV-1a algorithm.
 *
 * This function implements a non-cryptographic hash function that is
 * designed for speed while still providing a good distribution of hash
 * values. It's particularly useful for hash table implementations and
 * quick string comparisons.
 *
 * The FNV-1a algorithm is chosen for its simplicity and efficiency in
 * software implementations, especially for shorter strings typically
 * found in source code lines.
 *
 * Note: This function is not suitable for cryptographic purposes.
 *
 * @param str - The input string to be hashed.
 * @param ignoreLeadingWhitespace - If true, leading whitespace will be ignored when computing the hash.
 * @returns A 32-bit unsigned integer representing the hash of the input string.
 */
function fastHash(
  str: string,
  ignoreLeadingWhitespace: boolean = false,
): number {
  let hash = 0x811c9dc5; // FNV offset basis
  const startIndex = ignoreLeadingWhitespace
    ? str.length - str.trimLeft().length
    : 0;
  for (let i = startIndex; i < str.length; i++) {
    hash ^= str.charCodeAt(i);
    hash *= 0x01000193; // FNV prime
  }
  return hash >>> 0; // Convert to 32-bit unsigned integer
}

/**
 * Computes the Longest Common Subsequence (LCS) between two arrays of numbers.
 *
 * @param document An array of numbers representing the document content.
 * @param pattern An array of numbers representing the pattern to match.
 * @returns An object containing the length of the LCS, the minimum substring length,
 *          and the end index of the match in the document content.
 */
function computeLCS(
  document: number[],
  pattern: number[],
): {
  maxLCSLength: number;
  minSubstringLength: number;
  endIndex: number;
} {
  const n = document.length;
  const m = pattern.length;

  let previousRow: { lcsLength: number; minSubstringLength: number }[] = Array(
    m + 1,
  )
    .fill(null)
    .map(() => ({ lcsLength: 0, minSubstringLength: Infinity }));
  let currentRow: { lcsLength: number; minSubstringLength: number }[] = Array(
    m + 1,
  )
    .fill(null)
    .map(() => ({ lcsLength: 0, minSubstringLength: Infinity }));

  previousRow[0].minSubstringLength = 0;

  let maxLCSLength = 0;
  let minSubstringLength = Infinity;
  let endIndex = -1;

  for (let i = 1; i <= n; i++) {
    currentRow[0] = { lcsLength: 0, minSubstringLength: 0 };

    for (let j = 1; j <= m; j++) {
      if (document[i - 1] === pattern[j - 1]) {
        const lcsLength = previousRow[j - 1].lcsLength + 1;
        const minSubLength = previousRow[j - 1].minSubstringLength + 1;
        currentRow[j] = { lcsLength, minSubstringLength: minSubLength };
      } else {
        const fromTop = previousRow[j];
        const fromLeft = currentRow[j - 1];

        if (fromTop.lcsLength > fromLeft.lcsLength) {
          const lcsLength = fromTop.lcsLength;
          const minSubLength = fromTop.minSubstringLength + 1;
          currentRow[j] = { lcsLength, minSubstringLength: minSubLength };
        } else if (fromTop.lcsLength < fromLeft.lcsLength) {
          const lcsLength = fromLeft.lcsLength;
          const minSubLength = fromLeft.minSubstringLength;
          currentRow[j] = { lcsLength, minSubstringLength: minSubLength };
        } else {
          const lcsLength = fromTop.lcsLength;
          const minSubLength = Math.min(
            fromTop.minSubstringLength + 1,
            fromLeft.minSubstringLength,
          );
          currentRow[j] = { lcsLength, minSubstringLength: minSubLength };
        }
      }

      if (j === m) {
        const current = currentRow[j];
        if (
          current.lcsLength > maxLCSLength ||
          (current.lcsLength === maxLCSLength &&
            current.minSubstringLength < minSubstringLength)
        ) {
          maxLCSLength = current.lcsLength;
          minSubstringLength = current.minSubstringLength;
          endIndex = i;
        }
      }
    }

    [previousRow, currentRow] = [currentRow, previousRow];
  }

  return { maxLCSLength, minSubstringLength, endIndex };
}

/**
 * Locates the shortest range in the file content that contains the maximum
 * longest common subsequence (LCS) with the provided pattern, where comparisons
 * are performed line by line using hash-based matching.
 *
 * This function treats the file content and pattern as sequences of lines.
 * Each line is hashed using the fastHash function, and these hashes are used
 * for comparison instead of direct string matching. This approach can
 * significantly improve performance for large files or patterns.
 *
 * The function handles cases where the snippet might have different indentation
 * levels compared to the original file content. This is particularly useful
 * when dealing with code snippets returned by chat or other AI systems, which
 * may not preserve the original indentation. To account for this, the function
 * performs two searches:
 * 1. Without ignoring indentation (preserving original whitespace)
 * 2. Ignoring leading whitespace (to match snippets with different indentation)
 *
 * The function prioritizes matches found without ignoring indentation, but
 * falls back to the indentation-ignoring match if no exact match is found.
 * This approach ensures the most accurate match while still being flexible
 * enough to handle indentation differences.
 *
 * @param fileContent The content of the file, represented as a string where each line is separated by '\n'.
 * @param pattern The pattern to match, represented as a string where each line is separated by '\n'.
 * @returns An object containing the start and end indices of the range in the file content,
 *          or null if no match is found. The indices are zero-based and inclusive.
 */
export function fuzzyLocateSnippet(
  fileContent: string,
  pattern: string,
): null | { start: number; end: number } {
  const fileLines = fileContent.split("\n");
  const patternLines = pattern.trim().split("\n");

  function computeHashesAndLCS(ignoreLeadingWhitespace: boolean) {
    const fileHashes = fileLines.map((line) =>
      fastHash(line, ignoreLeadingWhitespace),
    );
    const patternHashes = patternLines.map((line) =>
      fastHash(line, ignoreLeadingWhitespace),
    );
    return computeLCS(fileHashes, patternHashes);
  }

  // Try both options: with and without ignoring indentation
  const resultWithoutIgnoreIndentation = computeHashesAndLCS(false);
  const resultWithIgnoreIndentation = computeHashesAndLCS(true);

  // Choose the result, giving priority to the one without ignoring indentation
  const { maxLCSLength, minSubstringLength, endIndex } =
    resultWithoutIgnoreIndentation.maxLCSLength >=
    resultWithIgnoreIndentation.maxLCSLength
      ? resultWithoutIgnoreIndentation
      : resultWithIgnoreIndentation;

  if (maxLCSLength === 0) {
    return null;
  }

  const startIndex = endIndex - minSubstringLength;
  const endIndexInclusive = endIndex - 1;

  return { start: startIndex, end: endIndexInclusive };
}
