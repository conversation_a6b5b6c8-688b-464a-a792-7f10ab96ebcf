import * as pathLib from "path";

/**
 * joinPath returns the path formed by joining the given directory and file names,
 * formatted according to the given fileType (a trailing separator is added to
 * directory names). If `asDirName` is true then the resulting path is formatted as
 * a directory name (with a trailing '/').
 */
export function joinPath(
  dirname: string,
  filename: string,
  asDirName = false,
): string {
  let joined = pathLib.join(dirname, filename);
  if (asDirName && !joined.endsWith(pathLib.sep)) {
    joined += pathLib.sep;
  }
  return joined;
}

export function isSuffixPath(targetPath: string, queryPath: string): boolean {
  // Normalize paths to handle different separators
  const normalizedTarget = pathLib.normalize(targetPath);
  const normalizedQuery = pathLib.normalize(queryPath);

  // If query is longer than target, it can't be a suffix
  if (normalizedQuery.length > normalizedTarget.length) {
    return false;
  }

  // Check if target ends with query
  if (normalizedTarget.endsWith(normalizedQuery)) {
    // Make sure it's a complete path segment by checking that
    // the character before the query is a separator or the query starts at the beginning
    const position = normalizedTarget.length - normalizedQuery.length;
    return position === 0 || normalizedTarget[position - 1] === pathLib.sep;
  }

  return false;
}
