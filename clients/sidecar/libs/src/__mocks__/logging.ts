import { createLogger, format, Logger, transports } from "winston";

const logger = createLogger({
  exitOnError: false,
  format: format.combine(
    format.printf(
      // eslint-disable-next-line @typescript-eslint/restrict-template-expressions
      (info) => `[${info.level}] '${info.prefix}': ${info.message}`,
    ),
  ),
  transports: [
    new transports.Console({
      level:
        process.env.LOG_LEVEL?.toLowerCase() === "verbose" ? "debug" : "silent",
    }),
  ],
});

export function getLogger(prefix: string): Logger {
  return logger.child({ prefix });
}
