/**
 * @file plugin-kv-store.integration.test.ts
 * Integration tests for the DynamicLevelKvStore using real LevelDB.
 * These tests verify that the bundled level package works correctly.
 */

import { promises as fs } from "fs";
import { join } from "path";
import { tmpdir } from "os";
import {
  DynamicLevelKvStore,
  createDynamicLevelKvStore,
} from "../plugin-kv-store";

// Mock the logger to avoid console output during tests
jest.mock("../../logging", () => ({
  getLogger: jest.fn(() => ({
    debug: jest.fn(),
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
  })),
}));

describe("DynamicLevelKvStore Integration Tests", () => {
  let tempDir: string;
  let kvStore: DynamicLevelKvStore;

  beforeEach(async () => {
    // Create a unique temporary directory for each test
    tempDir = await fs.mkdtemp(join(tmpdir(), "kv-store-test-"));
    const dbPath = join(tempDir, "test.db");
    kvStore = createDynamicLevelKvStore(dbPath);
  });

  afterEach(async () => {
    // Clean up: close the store and remove temp directory
    if (kvStore) {
      await kvStore.close();
    }
    try {
      await fs.rm(tempDir, { recursive: true, force: true });
    } catch (error) {
      // Ignore cleanup errors
      console.warn("Failed to clean up temp directory:", error);
    }
  });

  describe("Basic Operations", () => {
    test("should store and retrieve a value", async () => {
      const key = "test-key";
      const value = "test-value";

      await kvStore.put(key, value);
      const retrieved = await kvStore.get(key);

      expect(retrieved).toBe(value);
    });

    test("should return undefined for non-existent key", async () => {
      const retrieved = await kvStore.get("non-existent-key");
      expect(retrieved).toBeUndefined();
    });

    test("should overwrite existing values", async () => {
      const key = "test-key";
      const value1 = "first-value";
      const value2 = "second-value";

      await kvStore.put(key, value1);
      await kvStore.put(key, value2);
      const retrieved = await kvStore.get(key);

      expect(retrieved).toBe(value2);
    });
  });

  describe("Batch Operations", () => {
    test("should perform batch put operations", async () => {
      const operations = [
        { type: "put" as const, key: "key1", value: "value1" },
        { type: "put" as const, key: "key2", value: "value2" },
        { type: "put" as const, key: "key3", value: "value3" },
      ];

      await kvStore.batch(operations);

      const value1 = await kvStore.get("key1");
      const value2 = await kvStore.get("key2");
      const value3 = await kvStore.get("key3");

      expect(value1).toBe("value1");
      expect(value2).toBe("value2");
      expect(value3).toBe("value3");
    });

    test("should perform batch delete operations", async () => {
      // First, put some values
      await kvStore.put("key1", "value1");
      await kvStore.put("key2", "value2");

      // Then delete them in a batch
      const operations = [
        { type: "del" as const, key: "key1" },
        { type: "del" as const, key: "key2" },
      ];

      await kvStore.batch(operations);

      const value1 = await kvStore.get("key1");
      const value2 = await kvStore.get("key2");

      expect(value1).toBeUndefined();
      expect(value2).toBeUndefined();
    });

    test("should perform mixed batch operations", async () => {
      // Setup initial data
      await kvStore.put("key1", "initial1");
      await kvStore.put("key2", "initial2");

      const operations = [
        { type: "put" as const, key: "key1", value: "updated1" },
        { type: "del" as const, key: "key2" },
        { type: "put" as const, key: "key3", value: "new3" },
      ];

      await kvStore.batch(operations);

      const value1 = await kvStore.get("key1");
      const value2 = await kvStore.get("key2");
      const value3 = await kvStore.get("key3");

      expect(value1).toBe("updated1");
      expect(value2).toBeUndefined();
      expect(value3).toBe("new3");
    });
  });

  describe("Iterator Operations", () => {
    beforeEach(async () => {
      // Setup test data
      const operations = [
        { type: "put" as const, key: "a", value: "value-a" },
        { type: "put" as const, key: "b", value: "value-b" },
        { type: "put" as const, key: "c", value: "value-c" },
        { type: "put" as const, key: "d", value: "value-d" },
        { type: "put" as const, key: "e", value: "value-e" },
      ];
      await kvStore.batch(operations);
    });

    test("should iterate over all entries", async () => {
      const entries: Array<[string, string]> = [];
      for await (const entry of kvStore.iterator()) {
        entries.push(entry);
      }

      expect(entries).toHaveLength(5);
      expect(entries).toEqual([
        ["a", "value-a"],
        ["b", "value-b"],
        ["c", "value-c"],
        ["d", "value-d"],
        ["e", "value-e"],
      ]);
    });

    test("should iterate with gte filter", async () => {
      const entries: Array<[string, string]> = [];
      for await (const entry of kvStore.iterator({ gte: "c" })) {
        entries.push(entry);
      }

      expect(entries).toEqual([
        ["c", "value-c"],
        ["d", "value-d"],
        ["e", "value-e"],
      ]);
    });

    test("should iterate with lt filter", async () => {
      const entries: Array<[string, string]> = [];
      for await (const entry of kvStore.iterator({ lt: "d" })) {
        entries.push(entry);
      }

      expect(entries).toEqual([
        ["a", "value-a"],
        ["b", "value-b"],
        ["c", "value-c"],
      ]);
    });

    test("should iterate with limit", async () => {
      const entries: Array<[string, string]> = [];
      for await (const entry of kvStore.iterator({ limit: 3 })) {
        entries.push(entry);
      }

      expect(entries).toHaveLength(3);
      expect(entries).toEqual([
        ["a", "value-a"],
        ["b", "value-b"],
        ["c", "value-c"],
      ]);
    });

    test("should iterate in reverse", async () => {
      const entries: Array<[string, string]> = [];
      for await (const entry of kvStore.iterator({ reverse: true })) {
        entries.push(entry);
      }

      expect(entries).toEqual([
        ["e", "value-e"],
        ["d", "value-d"],
        ["c", "value-c"],
        ["b", "value-b"],
        ["a", "value-a"],
      ]);
    });

    test("should iterate keys only", async () => {
      const keys: string[] = [];
      for await (const key of kvStore.keys()) {
        keys.push(key);
      }

      expect(keys).toEqual(["a", "b", "c", "d", "e"]);
    });
  });

  describe("Persistence", () => {
    test("should persist data across store instances", async () => {
      const dbPath = join(tempDir, "persistent.db");

      // Create first store instance and add data
      const store1 = createDynamicLevelKvStore(dbPath);
      await store1.put("persistent-key", "persistent-value");
      await store1.close();

      // Create second store instance and verify data persists
      const store2 = createDynamicLevelKvStore(dbPath);
      const retrieved = await store2.get("persistent-key");
      await store2.close();

      expect(retrieved).toBe("persistent-value");
    });
  });

  describe("Error Handling", () => {
    test("should handle concurrent operations gracefully", async () => {
      const promises = [];

      // Perform multiple concurrent operations
      for (let i = 0; i < 10; i++) {
        promises.push(kvStore.put(`key-${i}`, `value-${i}`));
      }

      await Promise.all(promises);

      // Verify all operations completed successfully
      for (let i = 0; i < 10; i++) {
        const value = await kvStore.get(`key-${i}`);
        expect(value).toBe(`value-${i}`);
      }
    });
  });

  describe("Level Package Integration", () => {
    test("should successfully import and use Level class", async () => {
      // This test verifies that the bundled level package works
      // and that we can create a store without dynamic installation
      expect(kvStore).toBeInstanceOf(DynamicLevelKvStore);

      // Perform a basic operation to ensure Level is working
      await kvStore.put("integration-test", "success");
      const result = await kvStore.get("integration-test");

      expect(result).toBe("success");
    });
  });
});
