import { getLogger } from "../logging";
import { WebViewMessage } from "../webview-messages/common-webview-messages";
import { PostMessageFn } from "../webview-messages/webview-messages-broker";
import {
  MessageConsumerTypes,
  WebviewMessaging,
} from "../webview-messages/webview-messaging";

/**
 * In some cases WebViews will emit messages intended for the sidecar to
 * handle and respond to. This interface defines the contract for handling
 * those messages.
 */

class WebviewMessagesSingleton {
  private static _instance: WebviewMessaging | undefined = undefined;

  static setWebviewMessagingClient(cw: WebviewMessaging) {
    if (this._instance !== undefined) {
      const logger = getLogger("WebviewMessages");
      logger.warn(
        "Attempting to initialize webview messages when one is already configured. Keeping existing webview message client.",
      );
      return;
    }

    this._instance = cw;
  }

  static getWebviewMessagingClient(): WebviewMessaging {
    if (this._instance === undefined) {
      throw new Error("Webview messaging client not set");
    }
    return this._instance;
  }

  static reset() {
    this._instance = undefined;
  }
}

// Some shorthand functions to make usage a bit easier
export const setLibraryWebviewMessaging = (c: WebviewMessaging) =>
  WebviewMessagesSingleton.setWebviewMessagingClient(c);
export const getWebviewMessaging = () =>
  WebviewMessagesSingleton.getWebviewMessagingClient();
export const resetLibraryWebviewMessaging = () =>
  WebviewMessagesSingleton.reset();
export function sendMessageToSidecar(
  msg: WebViewMessage<MessageConsumerTypes>,
  postMessage: PostMessageFn<MessageConsumerTypes>,
) {
  getWebviewMessaging().onMessage(msg, postMessage);
}
