import { getLogger } from "../logging";

export enum PluginStateNamespace {
  agent = "agent",
}

export enum PluginStateScope {
  global = "global",
  // Workspace is not supported at the moment, but should be added when needed.
  // workspace = "workspace",
}

/**
 * This interface covers methods relating to the management of files created
 * and managed by the plugin itself (i.e. not user files).
 */
export interface IPluginStorageForSidecar {
  getValue<T>(
    namespace: PluginStateNamespace,
    key: string,
    scope: PluginStateScope,
  ): Promise<T | undefined>;
  setValue<T>(
    namespace: PluginStateNamespace,
    key: string,
    value: T,
    scope: PluginStateScope,
  ): Promise<void>;
}

class PluginStateForSidecarSingleton {
  private static _instance: IPluginStorageForSidecar | undefined = undefined;

  static setStateForSidecar(cw: IPluginStorageForSidecar) {
    if (this._instance !== undefined) {
      const logger = getLogger("PluginStateForSidecarSingleton");
      logger.warn(
        "Attempting to initialize plugin state when one is already configured. Keeping existing plugin state.",
      );
      return;
    }

    this._instance = cw;
  }

  static getStateForSidecar(): IPluginStorageForSidecar {
    if (this._instance === undefined) {
      throw new Error("PluginStateForSidecar not set");
    }
    return this._instance;
  }

  static reset() {
    this._instance = undefined;
  }
}

// Some shorthand functions to make usage a bit easier
export const setLibraryStateForSidecar = (c: IPluginStorageForSidecar) =>
  PluginStateForSidecarSingleton.setStateForSidecar(c);
export const getStateForSidecar = () =>
  PluginStateForSidecarSingleton.getStateForSidecar();
export const resetLibraryStateForSidecar = () =>
  PluginStateForSidecarSingleton.reset();
