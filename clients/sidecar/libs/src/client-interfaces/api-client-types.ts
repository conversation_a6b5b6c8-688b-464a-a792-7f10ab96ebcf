import { type ChatStopReason } from "../api/types";
import { type ChatResultNode } from "../chat/chat-types";
import {
  AgentRequestEventName,
  AgentSessionEventName,
  AgentTracingData,
  ClassifyAndDistillDebugFlag,
  FlushMemoriesDebugFlag,
  InitialOrientationCaller,
  InitialOrientationDebugFlag,
  InteractionType,
  MemoriesMoveTarget,
  ModeSelectorAction,
  ModeSelectorMode,
  NotificationBellState,
  RememberToolCallDebugFlag,
  RememberToolCaller,
  RemoteAgentThreadListAction,
  RemoteAgentNewThreadButtonAction,
  ThreadType,
  RemoteAgentSessionEventName,
  RemoteAgentSetupState,
  RemoteAgentSetupWindowAction,
  RulesImportedType,
  SourceControlType,
} from "../metrics/types";

export type ChatResult = {
  text: string;
  unknownBlobNames?: string[];
  checkpointNotFound?: boolean;
  workspaceFileChunks?: WorkspaceFileChunk[];
  nodes?: ChatResultNode[];
  stop_reason?: ChatStopReason;
};

export interface WorkspaceFileChunk {
  charStart: number;
  charEnd: number;
  blobName: string;
  file?: FileDetails;
}

export interface FileDetails {
  repoRoot: string;
  pathName: string;
  fileType?: FileType;
  uriScheme?: string;
  // The display range of the file. Expressed with 1-indexing for historical reasons.
  // Only contains the start and end lines, 1-indexed.
  range?: {
    start: number;
    stop: number;
  };
  // These are the full range of the file, expressed with 0-indexing.
  // The full range includes the start and end lines, as well as the
  // start and end character columns.
  fullRange?: {
    startLineNumber: number;
    startColumn: number;
    endLineNumber: number;
    endColumn: number;
  };
  originalCode?: string;
  modifiedCode?: string;
  lineChanges?: LineChanges;
  // Whether actions on this file should be triggered from a different tab.
  differentTab?: boolean;
  requestId?: string;
  suggestionId?: string;
  // The snippet to highlight in the file.
  snippet?: string;
}

export interface LineChanges {
  lineChanges: LineChange[];
  lineOffset: number;
}

export interface LineChange {
  originalStart: number;
  originalEnd: number;
  modifiedStart: number;
  modifiedEnd: number;
}

export enum FileType {
  directory = "Directory",
  file = "File",
  other = "Other",
}

export type AgentCodebaseRetrievalOptions = {
  // If true, disable codebase retrieval.
  // Note that this is `disable` instead of `enable` for backwards compatibility.
  disableCodebaseRetrieval?: boolean;
  // If true, enable commit retrieval.
  enableCommitRetrieval?: boolean;
};

export type AgentCodebaseRetrievalResult = {
  formattedRetrieval: string;
};

export type ToolUseRequestEvent = {
  requestId: string;
  toolName: string;
  toolUseId: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  toolInput: Record<string, any>;
  toolOutputIsError: boolean;
  toolRunDurationMs: number;
  isMcpTool: boolean;
  conversationId: string;
  chatHistoryLength: number;
  toolRequestId?: string;
  toolOutputLen?: number;
  eventTimeSec: number;
  eventTimeNsec: number;
};

export type ToolUseRequestEventPayload = {
  tool_name: string;
  tool_use_id: string;
  tool_input: string;
  tool_output_is_error: boolean;
  tool_run_duration_ms: number;
  is_mcp_tool: boolean;
  conversation_id: string;
  chat_history_length: number;
  tool_request_id?: string;
  tool_output_len?: number;
  tool_input_len?: number;
};

/**
 * AgentSessionEvent is an agent session event that can be logged.
 */
export type MemoriesFileOpenData = {
  memories_path_undefined?: boolean;
};

export type MemoriesMoveData = {
  target?: MemoriesMoveTarget;
};

export type RulesImportedData = {
  type?: RulesImportedType;
  num_files?: number;
  source?: string;
};

/**
 * API payload version of ContentTruncationData with snake_case field names
 * to match backend expectations
 */
export type ContentTruncationDataPayload = {
  original_char_count: number;
  original_line_count: number;
  truncated_char_count: number;
  truncated_line_count: number;
  tool_type: string;
};

/**
 * API payload version of ModelSelectionChangeData with snake_case field names
 * to match backend expectations
 */
export type ModelSelectionChangeDataPayload = {
  previous_model_id: string | null;
  previous_model_name: string;
  new_model_id: string | null;
  new_model_name: string;
};

export type AgentSessionEvent = {
  event_time_sec: number;
  event_time_nsec: number;
  event_name: AgentSessionEventName;
  conversation_id: string;
  event_data?: {
    agent_reversion_data?: AgentReversionData;
    agent_interruption_data?: AgentInterruptionData;
    remember_tool_call_data?: RememberToolCallData;
    memories_file_open_data?: MemoriesFileOpenData;
    initial_orientation_data?: InitialOrientationData;
    classify_and_distill_data?: ClassifyAndDistillData;
    flush_memories_data?: FlushMemoriesData;
    memories_move_data?: MemoriesMoveData;
    rules_imported_data?: RulesImportedData;
    task_list_usage_data?: TaskListUsageData;
    memory_usage_data?: MemoryUsageData;
    content_truncation_data?: ContentTruncationDataPayload;
    model_selection_change_data?: ModelSelectionChangeDataPayload;
  };
};

/**
 * AgentRequestEvent is an agent request event that can be logged.
 * This is different from AgentSessionEvent as it's tied to specific requests rather than sessions.
 */
export type AgentRequestEvent = {
  event_time_sec: number;
  event_time_nsec: number;
  event_name: AgentRequestEventName;
  conversation_id: string;
  request_id: string;
  chat_history_length: number;
  event_data?: {
    chat_history_summarization_data?: ChatHistorySummarizationData;
    first_token_timing_data?: FirstTokenTimingData;
  };
};

export type ChatHistorySummarizationData = {
  total_history_char_count: number;
  total_history_exchange_count: number;
  head_char_count: number;
  head_exchange_count: number;
  head_last_request_id: string;
  tail_char_count: number;
  tail_exchange_count: number;
  tail_last_request_id: string;

  // Summarization completion data
  summary_char_count: number;
  summarization_duration_ms: number;

  is_cache_about_to_expire: boolean;
  is_aborted: boolean;
};

export type FirstTokenTimingData = {
  user_message_sent_timestamp_ms: number;
  first_token_received_timestamp_ms: number;
  time_to_first_token_ms: number;
};

export type AgentReversionData = {
  // Leaving this object empty for now as the fields are still in flux.
  // See comment in `clients/vscode/src/metrics/types.ts` for context.
};

export type AgentInterruptionData = {
  request_id: string;
  curr_conversation_length: number;
};

export type RememberToolCallData = {
  caller: RememberToolCaller;
  is_complex_new_memory: boolean;
  tracing_data: AgentTracingData<RememberToolCallDebugFlag>;
};

export type InitialOrientationData = {
  caller: InitialOrientationCaller;
  tracing_data: AgentTracingData<InitialOrientationDebugFlag>;
};

export type ClassifyAndDistillData = {
  tracing_data: AgentTracingData<ClassifyAndDistillDebugFlag>;
};

export type FlushMemoriesData = {
  tracing_data: AgentTracingData<FlushMemoriesDebugFlag>;
};

export type RemoteAgentSessionEvent = {
  event_time_sec: number;
  event_time_nsec: number;
  event_name: RemoteAgentSessionEventName;
  remote_agent_id: string;
  event_data?: {
    remote_agent_setup_data?: RemoteAgentSetupData;
    setup_script_data?: RemoteAgentSetupScriptData;
    ssh_interaction_data?: SSHInteractionData;
    notification_bell_data?: NotificationBellData;
    diff_panel_data?: DiffPanelData;
    setup_page_opened?: SetupPageOpened;
    github_api_failure?: GithubAPIFailure;
    remote_agent_created?: RemoteAgentCreated;
    changes_applied_data?: ChangesAppliedData;
    created_pr_data?: CreatedPRData;
    mode_selector_data?: ModeSelectorData;
    remote_agent_setup_window_data?: RemoteAgentSetupWindowData;
    remote_agent_thread_list_data?: RemoteAgentThreadListData;
    remote_agent_new_thread_button_data?: RemoteAgentNewThreadButtonData;
  };
};

export type RemoteAgentSetupData = {
  used_generated_setup_script: boolean;
  setup_state: RemoteAgentSetupState;
};

export type RemoteAgentSetupScriptData = {
  num_tries: number;
  num_messages_sent: number;
  generation_time_ms: number;
  manual_modification: boolean;
};

export type SSHInteractionData = {
  interaction_type: InteractionType;
};

export type NotificationBellData = {
  bell_state: NotificationBellState;
};

export type DiffPanelData = {
  loading_time_ms: number;
  applied: boolean;
};

export type SetupPageOpened = {};

export type GithubAPIFailure = {
  error_code: number;
};

export type RemoteAgentCreated = {
  changed_repo: boolean;
  changed_branch: boolean;
};

export type ChangesAppliedData = {};

export type CreatedPRData = {};

export type ModeSelectorData = {
  action: ModeSelectorAction;
  mode: ModeSelectorMode;
  source_control: SourceControlType;
};

export type RemoteAgentSetupWindowData = {
  action: RemoteAgentSetupWindowAction;
  repo_hash: string;
  branch_hash: string;
  has_setup_script_selected: boolean;
  github_integration_enabled: boolean;
  num_repos_available: number;
  source_control: SourceControlType;
};

export type RemoteAgentThreadListData = {
  action: RemoteAgentThreadListAction;
  num_agents_in_list: number;
  source_control: SourceControlType;
};

export type RemoteAgentNewThreadButtonData = {
  action: RemoteAgentNewThreadButtonAction;
  thread_type: ThreadType;
  source_control: SourceControlType;
};

export enum TaskListAction {
  unknown = 0,
  // Task creation actions
  addTask = 1,
  addSubtask = 2,
  // Task modification actions
  updateTaskStatus = 3,
  updateTaskName = 4,
  updateTaskDescription = 5,
  reorganizeTaskList = 6,
  // Task deletion actions
  deleteTask = 7,
  // Task execution actions
  runSingleTask = 8,
  runAllTasks = 9,
  // Task list management actions
  viewTaskList = 10,
  exportTaskList = 11,
  importTaskList = 12,
  syncTaskList = 13,
}

export enum TaskListActionTrigger {
  unknown = 0,
  user = 1,
  agent = 2,
}

export type TaskListUsageData = {
  action: TaskListAction;
  total_tasks_count: number;
  triggered_by: TaskListActionTrigger;
};

export enum MemoryAction {
  unknown = 0,
  // Memory state management actions
  saveMemory = 1,
  discardMemory = 2,
  editMemory = 3,
  // Memory viewing actions
  viewMemories = 4,
  refreshMemories = 5,
  // Memory filtering actions
  filterByState = 6,
  filterByVersion = 7,
  // Memory file operations
  openMemoriesFile = 8,
  // Memory creation actions (from agent)
  createMemory = 9,
}

export enum MemoryActionTrigger {
  unknown = 0,
  user = 1,
  agent = 2,
}

export type MemoryUsageData = {
  action: MemoryAction;
  memory_id?: string;
  triggered_by: MemoryActionTrigger;
  memory_state?: string;
  memory_version?: string;
};
