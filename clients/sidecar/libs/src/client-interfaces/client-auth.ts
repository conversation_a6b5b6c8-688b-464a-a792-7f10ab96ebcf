import { getLogger } from "../logging";

/**
 * This interface covers methods relating to client authentication.
 */
export interface IClientAuth {
  getAPIToken: () => Promise<string>;
  getCompletionURL: () => Promise<string>;
  removeAuthSession: () => Promise<void>;
}

class ClientAuthSingleton {
  private static _instance: IClientAuth | undefined = undefined;

  static setClientAuth(clientAuth: IClientAuth) {
    if (this._instance !== undefined) {
      const logger = getLogger("ClientAuth");
      logger.warn(
        "Attempting to initialize client auth when one is already configured. Keeping existing client auth.",
      );
      return;
    }

    this._instance = clientAuth;
  }

  static getClientAuth(): IClientAuth {
    if (this._instance === undefined) {
      throw new Error("ClientAuth not set");
    }
    return this._instance;
  }

  static reset() {
    this._instance = undefined;
  }
}

export const setLibraryClientAuth = (c: IClientAuth) =>
  ClientAuthSingleton.setClientAuth(c);
export const getClientAuth = () => ClientAuthSingleton.getClientAuth();
export const resetLibraryClientAuth = () => ClientAuthSingleton.reset();
