import type { Level } from "level";
import { getLogger } from "../logging";

/**
 * Simple batch operation for KV store
 */
export interface KvBatchOperation {
  type: "put" | "del";
  key: string;
  value?: string;
}

/**
 * Options for iterator operations
 */
export interface KvIteratorOptions {
  /** Greater than or equal to (inclusive lower bound) */
  gte?: string;
  /** Less than (exclusive upper bound) */
  lt?: string;
  /** Greater than (exclusive lower bound) */
  gt?: string;
  /** Less than or equal to (inclusive upper bound) */
  lte?: string;
  /** Maximum number of entries to return */
  limit?: number;
  /** Iterate in reverse order */
  reverse?: boolean;
}

/**
 * Configuration options for LevelDB
 */
export interface LevelDbOptions {
  /** Cache size in bytes (default: 64MB) */
  cacheSize?: number;
  /** Write buffer size in bytes (default: 16MB) */
  writeBufferSize?: number;
  /** Maximum file size in bytes (default: 8MB) */
  maxFileSize?: number;
  /** Key encoding (default: 'utf8') */
  keyEncoding?: string;
  /** Value encoding (default: 'utf8') */
  valueEncoding?: string;
}

/**
 * Plugin KV Store interface for string-to-string key-value operations
 */
export interface IPluginKvStore {
  get(key: string): Promise<string | undefined>;
  put(key: string, value: string): Promise<void>;
  batch(operations: Array<KvBatchOperation>): Promise<void>;
  close(): Promise<void>;

  /**
   * Create an iterator for key-value pairs
   * @param options Iterator options for range queries and limits
   * @returns AsyncIterable that yields [key, value] pairs
   */
  iterator(options?: KvIteratorOptions): AsyncIterable<[string, string]>;

  /**
   * Create an iterator for keys only
   * @param options Iterator options for range queries and limits
   * @returns AsyncIterable that yields keys
   */
  keys(options?: KvIteratorOptions): AsyncIterable<string>;
}

class PluginKvStoreSingleton {
  private static _instance: IPluginKvStore | undefined = undefined;

  static setPluginKvStore(kvStore: IPluginKvStore) {
    if (this._instance !== undefined) {
      const logger = getLogger("PluginKvStore");
      logger.warn(
        "Attempting to initialize plugin KV store when one is already configured. Keeping existing KV store.",
      );
      return;
    }

    this._instance = kvStore;
  }

  static getPluginKvStore(): IPluginKvStore {
    if (this._instance === undefined) {
      throw new Error("PluginKvStore not set");
    }
    return this._instance;
  }

  static reset() {
    this._instance = undefined;
  }
}

/**
 * LevelDB implementation of the KV store interface with dynamic loading
 */
export class DynamicLevelKvStore implements IPluginKvStore {
  private _db: Level | null = null;
  private readonly _logger = getLogger("DynamicLevelKvStore");
  private readonly _dbPath: string;
  private readonly _options: LevelDbOptions;
  private _initPromise: Promise<void> | null = null;

  constructor(dbPath: string, options: LevelDbOptions = {}) {
    this._dbPath = dbPath;
    this._options = {
      cacheSize: 64 * 1024 * 1024, // 64MB
      writeBufferSize: 16 * 1024 * 1024, // 16MB
      maxFileSize: 8 * 1024 * 1024, // 8MB
      keyEncoding: "utf8",
      valueEncoding: "utf8",
      ...options,
    };
  }

  /**
   * Initialize the Level database, installing the package if necessary.
   * Uses promise memoization to ensure initialization only happens once,
   * even with concurrent calls.
   */
  private async ensureLevel(): Promise<Level> {
    // Return the existing promise if initialization is already in progress or complete
    if (this._initPromise) {
      await this._initPromise;
      if (!this._db) {
        throw new Error("Failed to initialize LevelDB");
      }
      return this._db;
    }

    // Create and memoize the initialization promise
    this._initPromise = this._initializeLevel();
    await this._initPromise;
    if (!this._db) {
      throw new Error("Failed to initialize LevelDB");
    }
    return this._db;
  }

  private async _initializeLevel(): Promise<void> {
    this._logger.info("Ensuring LevelDB is initialized");
    if (this._db) {
      return; // Already initialized
    }

    try {
      // We need to do this because:
      // 1. Require allows us to do static imports + bundling, so our build process will pull in the right deps.
      //    If we do dynamic imports, they're treated as external
      // 2. There are native libs bundled + loaded in, so a static, hoisted `import` will actually error
      //    at import time if the native libs are not available
      // eslint-disable-next-line @typescript-eslint/no-var-requires, @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unsafe-member-access
      const LevelDB: typeof Level = require("level").Level;
      if (!LevelDB) {
        throw new Error("Failed to import level package");
      }

      // Create database instance directly - level package is now bundled
      this._db = new LevelDB(this._dbPath, this._options);
      // Explicitly open the database
      if (this._db) {
        await this._db.open();
      }
      this._logger.debug(`LevelDB initialized and opened at ${this._dbPath}`);
    } catch (error: unknown) {
      this._logger.error("Failed to initialize LevelDB:", error);
      // Reset the promise so we can retry on next call
      this._initPromise = null;
      throw error;
    }
  }
  async get(key: string): Promise<string | undefined> {
    const db = await this.ensureLevel();
    return await db.get(key);
  }

  async put(key: string, value: string): Promise<void> {
    const db = await this.ensureLevel();
    await db.put(key, value);
  }

  async batch(operations: Array<KvBatchOperation>): Promise<void> {
    this._logger.debug(
      `Executing batch operation with ${operations.length} operations`,
    );
    const db = await this.ensureLevel();

    const batch = db.batch();
    for (const op of operations) {
      if (op.type === "put" && op.value !== undefined) {
        batch.put(op.key, op.value);
      } else if (op.type === "del") {
        batch.del(op.key);
      } else {
        throw new Error(`Unsupported operation type: ${op.type}`);
      }
    }
    await batch.write();

    this._logger.debug("Batch operation complete");
  }

  async close(): Promise<void> {
    if (this._db) {
      await this._db.close();
      this._db = null;
    }
  }

  async *iterator(
    options?: KvIteratorOptions,
  ): AsyncIterable<[string, string]> {
    const db = await this.ensureLevel();
    const levelOptions = { ...options };

    for await (const [key, value] of db.iterator(levelOptions)) {
      yield [key, value];
    }
  }

  async *keys(options?: KvIteratorOptions): AsyncIterable<string> {
    const db = await this.ensureLevel();
    const levelOptions = { ...options };

    for await (const key of db.keys(levelOptions)) {
      yield key;
    }
  }
}

/**
 * Factory function to create a DynamicLevelKvStore instance
 */
export function createDynamicLevelKvStore(
  dbPath: string,
  options?: LevelDbOptions,
): DynamicLevelKvStore {
  return new DynamicLevelKvStore(dbPath, options);
}

// Some shorthand functions to make usage a bit easier
export const setLibraryPluginKvStore = (kvStore: IPluginKvStore) =>
  PluginKvStoreSingleton.setPluginKvStore(kvStore);
export const getPluginKvStore = () => PluginKvStoreSingleton.getPluginKvStore();
export const resetLibraryPluginKvStore = () => PluginKvStoreSingleton.reset();
