import { getLogger } from "../logging";
import { IQualifiedPathName } from "../workspace/workspace-types";
import { ShowDiffViewOptions } from "./client-actions-types";

/**
 * This interface covers methods relating to the workspaces in the users
 * clients.
 */
export interface IClientActions {
  showDiffView: (
    path: IQualifiedPathName,
    original: string | undefined,
    modified: string | undefined,
    opts: ShowDiffViewOptions,
  ) => Promise<void>;
}

class ClientActionsSingleton {
  private static _instance: IClientActions | undefined = undefined;

  static setClientActions(c: IClientActions) {
    if (this._instance !== undefined) {
      const logger = getLogger("ClientActions");
      logger.warn(
        "Attempting to initialize client actions when one is already configured. Keeping existing client actions.",
      );
      return;
    }

    this._instance = c;
  }

  static getClientActions(): IClientActions {
    if (this._instance === undefined) {
      throw new Error("ClientActions not set");
    }
    return this._instance;
  }

  static reset() {
    this._instance = undefined;
  }
}

// Some shorthand functions to make usage a bit easier
export const setLibraryClientActions = (c: IClientActions) =>
  ClientActionsSingleton.setClientActions(c);
export const getClientActions = () => ClientActionsSingleton.getClientActions();
export const resetLibraryClientActions = () => ClientActionsSingleton.reset();
