import { getLogger } from "../logging";

/**
 * This interface covers methods relating to the management of files created
 * and managed by the plugin itself (i.e. not user files).
 */
export interface IPluginFileStore {
  /**
   * Save an asset to the plugin's file store
   * @param path The path to save the asset to
   * @param content The content of the asset
   */
  saveAsset: (path: string, content: Uint8Array) => Promise<void>;

  /**
   * Load an asset from the plugin's file store
   * @param path The path of the asset to load
   * @returns The content of the asset, or undefined if it doesn't exist
   */
  loadAsset: (path: string) => Promise<Uint8Array | undefined>;

  /**
   * Delete an asset from the plugin's file store
   * @param path The path of the asset to delete
   */
  deleteAsset(path: string): Promise<void>;

  /**
   * List all assets in the plugin's file store with paths that start with the given prefix
   * @param prefix The prefix to filter assets by
   * @returns A list of asset paths
   */
  listAssets(prefix: string): Promise<string[]>;

  /**
   * Get the absolute path to an asset in the plugin's file store
   * @param path The path of the asset
   * @returns The absolute path to the asset
   */
  getAssetPath(path: string): Promise<string>;
}

class PluginFileStoreSingleton {
  private static _instance: IPluginFileStore | undefined = undefined;

  static setPluginFileStore(cw: IPluginFileStore) {
    if (this._instance !== undefined) {
      const logger = getLogger("PluginFileStore");
      logger.warn(
        "Attempting to initialize client workspaces when one is already configured. Keeping existing client workspaces.",
      );
      return;
    }

    this._instance = cw;
  }

  static getPluginFileStore(): IPluginFileStore {
    if (this._instance === undefined) {
      throw new Error("PluginFileStore not set");
    }
    return this._instance;
  }

  static reset() {
    this._instance = undefined;
  }
}

// Some shorthand functions to make usage a bit easier
export const setLibraryPluginFileStore = (c: IPluginFileStore) =>
  PluginFileStoreSingleton.setPluginFileStore(c);
export const getPluginFileStore = () =>
  PluginFileStoreSingleton.getPluginFileStore();
export const resetLibraryPluginFileStore = () =>
  PluginFileStoreSingleton.reset();
