import { getLogger } from "../logging";

// Contains the subset of VSCode's AugmentConfig that's used in the sidecar
export type SidecarAugmentConfig = {
  apiToken: string;
  completionURL: string;
  chat: {
    url?: string;
    model?: string;
  };
  agent: {
    model?: string;
  };
  enableDebugFeatures: boolean;
};

/**
 * This interface provides a way for the client to pass configuration to the
 * sidecar library. The config is used to configure the behavior of the library
 * for components such as the API client.
 */
export interface IClientConfig {
  getConfig: () => Promise<Readonly<SidecarAugmentConfig>>;
}

class ClientConfigSingleton {
  private static _instance: IClientConfig | undefined = undefined;

  static setClientConfig(clientConfig: IClientConfig) {
    if (this._instance !== undefined) {
      const logger = getLogger("ClientConfig");
      logger.warn(
        "Attempting to initialize client config when one is already configured. Keeping existing client config.",
      );
      return;
    }

    this._instance = clientConfig;
  }

  static getClientConfig(): IClientConfig {
    if (this._instance === undefined) {
      throw new Error("ClientConfig not set");
    }
    return this._instance;
  }

  static reset() {
    this._instance = undefined;
  }
}

export const setLibraryClientConfig = (c: IClientConfig) =>
  ClientConfigSingleton.setClientConfig(c);
export const getClientConfig = () => ClientConfigSingleton.getClientConfig();
export const resetLibraryClientConfig = () => ClientConfigSingleton.reset();
