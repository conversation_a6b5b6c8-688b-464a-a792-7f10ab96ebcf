import { joinPath } from "../utils/path-utils";
import type { IQualifiedPathName } from "./workspace-types";

/**
 * This interface exists so vscode.URI can be compared
 * to a QualifiedPathName.  This should reduce the likelyhood
 * of the path of an editor being compared to the path of a result.
 */
export interface HasFSPath {
  fsPath: string;
}
// QualifiedPathName is a pair of a repo root path and a relative path name.
export class QualifiedPathName implements IQualifiedPathName {
  constructor(
    public readonly rootPath: string,
    public readonly relPath: string,
  ) {}

  public static from(pathName: IQualifiedPathName): QualifiedPathName {
    return new QualifiedPathName(pathName.rootPath, pathName.relPath);
  }

  public get absPath(): string {
    return joinPath(this.rootPath, this.relPath);
  }

  public equals(other: IQualifiedPathName | HasFSPath | undefined): boolean {
    return QualifiedPathName.equals(this, other);
  }

  public static equals(
    a: IQualifiedPathName | HasFSPath | undefined,
    b: IQualifiedPathName | HasFSPath | undefined,
  ): boolean {
    if (a === b) {
      return true;
    }
    if (a == null || b == null) {
      return false;
    }
    const fullPathA = isQualifiedPathName(a)
      ? joinPath(a.rootPath, a.relPath)
      : a.fsPath;
    const fullPathB = isQualifiedPathName(b)
      ? joinPath(b.rootPath, b.relPath)
      : b.fsPath;
    return fullPathA === fullPathB;
  }
}

function isQualifiedPathName(
  pathName: unknown,
): pathName is IQualifiedPathName {
  return (
    pathName != null &&
    typeof pathName === "object" &&
    ("rootPath" in pathName || "relPath" in pathName)
  );
}
