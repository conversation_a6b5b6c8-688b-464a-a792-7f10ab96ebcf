import { DisposableCollection } from "./disposable-collection";
import { ISidecarDisposable } from "./disposable-types";

/**
 * DisposableService is a base class for components that are disposable and want
 * to dispose some of their components when they are disposed.
 */
export class DisposableService implements ISidecarDisposable {
  private _disposables = new DisposableCollection();
  private _priorityDisposables = new DisposableCollection();

  constructor(
    disposables: DisposableCollection = new DisposableCollection(),
    priorityDisposables: DisposableCollection = new DisposableCollection(),
  ) {
    this._disposables.adopt(disposables);
    this._priorityDisposables.adopt(priorityDisposables);
  }

  // Arrange for the given disposable to be disposed when this object is disposed. Items added
  // with the priority flag will be disposed before items added without it.
  public addDisposable<T extends ISidecarDisposable>(
    item: T,
    priority = false,
  ): T {
    if (priority) {
      return this._priorityDisposables.add(item);
    } else {
      return this._disposables.add(item);
    }
  }

  // Arrange for the given array of disposable to be disposed when this object is disposed.
  public addDisposables(...items: ISidecarDisposable[]) {
    this._disposables.addAll(...items);
  }

  dispose() {
    this._priorityDisposables.dispose();
    this._disposables.dispose();
  }
}
