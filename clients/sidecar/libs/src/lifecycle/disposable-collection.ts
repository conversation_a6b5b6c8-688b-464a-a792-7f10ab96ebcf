import { ISidecarDisposable } from "./disposable-types";

/**
 * DisposableCollection is a class for managing a collection of disposables.
 */
export class DisposableCollection implements ISidecarDisposable {
  private _disposables: Array<ISidecarDisposable> = [];

  // Arrange for the given disposable to be disposed when this object is disposed.
  public add<T extends ISidecarDisposable>(item: T): T {
    if (item === undefined) {
      throw new Error(
        "Attempt to add undefined disposable to DisposableCollection",
      );
    }
    this._disposables.push(item);
    return item;
  }

  // Arrange for the given array of disposable to be disposed when this object is disposed.
  public addAll(...items: ISidecarDisposable[]) {
    items.forEach((item) => this.add(item));
  }

  // Transfer the disposables from the given collection to this one.
  public adopt(disposables: DisposableCollection) {
    this._disposables.push(...disposables._disposables);
    disposables._disposables.length = 0;
  }

  public dispose() {
    for (const disposable of this._disposables) {
      disposable.dispose();
    }
    this._disposables.length = 0;
  }
}
