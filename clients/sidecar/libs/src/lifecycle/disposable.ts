import { ISidecarDisposable } from "./disposable-types";

export class SidecarDisposable implements ISidecarDisposable {
  public dispose: () => void;

  constructor(callOnDispose: () => void) {
    this.dispose = (): void => callOnDispose();
  }

  static from(...disposables: ISidecarDisposable[]): ISidecarDisposable {
    return new SidecarDisposable(() => {
      for (const disposable of disposables) {
        disposable.dispose();
      }
    });
  }
}
