/**
 * @file types.ts
 * Centralized type definitions for tool use states.
 * This is the authoritative source for tool use state types in the sidecar.
 */

import type { ToolUseResponse } from "../tools/tool-types";

/**
 * Tool use phase enum - represents the current state of a tool use operation.
 * This is the authoritative definition used throughout the sidecar.
 */
export enum ToolUsePhase {
  /** Unknown state. Should not be used. */
  unknown = 0,
  /** A new tool use block has been returned by the model. */
  new = 1,
  /** Checking if the tool is safe to run without user approval. */
  checkingSafety = 2,
  /** <PERSON>l is waiting for user approval to run. */
  runnable = 3,
  /** Tool is running. */
  running = 4,
  /** Tool has completed successfully. */
  completed = 5,
  /** Tool has failed. */
  error = 6,
  /** Tool use is being cancelled. */
  cancelling = 7,
  /** Tool use has been cancelled. */
  cancelled = 8,
}

/**
 * Tool use state interface - represents the complete state of a tool use operation.
 * This is the authoritative definition used throughout the sidecar.
 */
export interface ToolUseState {
  /** The request ID this tool use belongs to */
  requestId: string;
  /** Unique identifier for this specific tool use */
  toolUseId: string;
  /** Current phase/state of the tool use */
  phase: ToolUsePhase;
  /** Result of the tool use operation (if completed) */
  result?: ToolUseResponse;
}

/**
 * Key data for generating tool use state keys.
 * Used for consistent key generation across the system.
 */
export interface ToolUseKeyData {
  requestId: string;
  toolUseId: string;
}

/**
 * Generates a consistent key for tool use state storage.
 * Format: {requestId};{toolUseId}
 */
export function getToolUseKey(state: ToolUseKeyData): string {
  return `${state.requestId};${state.toolUseId}`;
}

/**
 * Parses a tool use key back into its component parts.
 */
export function getToolUseKeyData(key: string): ToolUseKeyData {
  const [requestId, toolUseId] = key.split(";");
  return { requestId, toolUseId };
}
