/**
 * @file storage-types.ts
 * Type definitions for tool use state storage operations.
 */

import type { ToolUseState } from "./types";

/**
 * StoredToolUseState extends the basic ToolUseState type with storage-specific fields.
 * This is the full tool use state data structure used for persistence in the sidecar.
 */
export interface StoredToolUseState extends ToolUseState {
  /** ID of the conversation this tool use state belongs to */
  conversationId: string;

  /** ISO timestamp when the tool use state was created/updated */
  timestamp?: string;
}
