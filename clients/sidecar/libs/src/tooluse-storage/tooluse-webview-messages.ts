/**
 * @file tooluse-webview-messages.ts
 * This file contains the handler for tool use state-related webview messages.
 * It provides the interface between the webview and the ToolUseStateManager for
 * tool use state storage operations.
 */

import { ToolUseStateManager } from "./tooluse-manager";
import {
  CommonWebViewMessageType,
  WebViewMessage,
} from "../webview-messages/common-webview-messages";
import {
  ToolUseStateWebViewMessageType,
  LoadConversationToolUseStatesRequest,
  LoadConversationToolUseStatesResponse,
  SaveToolUseStatesRequest,
  SaveToolUseStatesResponse,
  DeleteConversationToolUseStatesRequest,
  DeleteConversationToolUseStatesResponse,
} from "../webview-messages/message-types/tooluse-messages";
import { IWebviewMessageConsumer } from "../webview-messages/webview-messages-broker";
import { getLogger } from "../logging";

/**
 * ToolUseStateWebviewMessageHandler handles webview messages related to tool use state operations.
 * It acts as a bridge between the webview and the ToolUseStateManager, providing a clean
 * interface for tool use state storage operations without exposing implementation details.
 */
export class ToolUseStateWebviewMessageHandler
  implements IWebviewMessageConsumer<ToolUseStateWebViewMessageType>
{
  private readonly _logger = getLogger("ToolUseStateWebviewMessageHandler");
  public readonly supportedTypes = ToolUseStateWebViewMessageType;

  constructor(private readonly _toolUseStateManager: ToolUseStateManager) {}

  public async handle(
    msg: WebViewMessage<ToolUseStateWebViewMessageType>,
    postMessage: (
      msg: WebViewMessage<
        ToolUseStateWebViewMessageType | CommonWebViewMessageType
      >,
    ) => void,
  ): Promise<void> {
    try {
      switch (msg.type) {
        case ToolUseStateWebViewMessageType.loadConversationToolUseStatesRequest: {
          const webviewMsg = msg as LoadConversationToolUseStatesRequest;
          const response =
            await this._handleLoadConversationToolUseStates(webviewMsg);
          postMessage(response);
          break;
        }

        case ToolUseStateWebViewMessageType.saveToolUseStatesRequest: {
          const webviewMsg = msg as SaveToolUseStatesRequest;
          const response = await this._handleSaveToolUseStates(webviewMsg);
          postMessage(response);
          break;
        }

        case ToolUseStateWebViewMessageType.deleteConversationToolUseStatesRequest: {
          const webviewMsg = msg as DeleteConversationToolUseStatesRequest;
          const response =
            await this._handleDeleteConversationToolUseStates(webviewMsg);
          postMessage(response);
          break;
        }

        default:
          this._logger.warn(`Unhandled message type: ${String(msg.type)}`);
          break;
      }
    } catch (error: any) {
      this._logger.error(`Error handling message ${String(msg.type)}:`, error);
      throw error;
    }
  }

  /**
   * Handles loading all tool use states for a conversation.
   */
  private async _handleLoadConversationToolUseStates(
    message: LoadConversationToolUseStatesRequest,
  ): Promise<LoadConversationToolUseStatesResponse> {
    const { conversationId } = message.data;

    this._logger.debug(
      `Loading tool use states for conversation: ${conversationId}`,
    );

    const toolUseStates =
      await this._toolUseStateManager.loadConversationToolUseStates(
        conversationId,
      );

    return {
      type: ToolUseStateWebViewMessageType.loadConversationToolUseStatesResponse,
      data: {
        toolUseStates,
      },
    };
  }

  /**
   * Handles saving tool use states for a conversation.
   */
  private async _handleSaveToolUseStates(
    message: SaveToolUseStatesRequest,
  ): Promise<SaveToolUseStatesResponse> {
    const { conversationId, toolUseStates } = message.data;

    this._logger.debug(
      `Saving ${Object.keys(toolUseStates).length} tool use states for conversation: ${conversationId}`,
    );

    await this._toolUseStateManager.saveToolUseStates(
      conversationId,
      toolUseStates,
    );

    return {
      type: ToolUseStateWebViewMessageType.saveToolUseStatesResponse,
      data: {
        success: true,
      },
    };
  }

  /**
   * Handles deleting all tool use states for a conversation.
   */
  private async _handleDeleteConversationToolUseStates(
    message: DeleteConversationToolUseStatesRequest,
  ): Promise<DeleteConversationToolUseStatesResponse> {
    const { conversationId } = message.data;

    this._logger.debug(
      `Deleting tool use states for conversation: ${conversationId}`,
    );

    await this._toolUseStateManager.deleteConversationToolUseStates(
      conversationId,
    );

    return {
      type: ToolUseStateWebViewMessageType.deleteConversationToolUseStatesResponse,
      data: {
        success: true,
      },
    };
  }
}
