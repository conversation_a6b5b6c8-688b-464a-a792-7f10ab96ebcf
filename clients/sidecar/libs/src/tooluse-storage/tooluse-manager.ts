/**
 * ToolUseStateManager handles tool use state storage and provides the main API for tool use state operations.
 * It uses a pluggable key-value store for efficient storage, similar to ExchangeManager but for tool use states.
 */

import { getLogger } from "../logging";
import { getPluginKvStore } from "../client-interfaces/plugin-kv-store";
import {
  IPluginKvStore,
  KvBatchOperation,
  KvIteratorOptions,
} from "../client-interfaces/plugin-kv-store";
import { ToolUseState } from "./types";

/**
 * ToolUseStateManager provides the main API for tool use state storage operations.
 *
 * ## Key Features
 * - **High Performance**: Uses LevelDB for fast key-value operations
 * - **Simple Operations**: Basic read/save/delete operations that mirror KV store semantics
 * - **Conversation-Based Loading**: Efficient prefix scanning for conversation tool use states
 * - **Atomic Operations**: Batch saves and deletes for data consistency
 * - **Automatic Upsert**: Save operations create or update automatically
 *
 * ## Data Model
 * - **Tool Use States**: Stored with keys `tooluse:{conversationId}:{requestId};{toolUseId}`
 * - **No metadata needed**: Tool use states are lightweight, no additional metadata required
 *
 * ## Key Structure
 * - Tool use state keys: `tooluse:conv-123:req-456;tool-789` → ToolUseState JSON
 *
 * ## Performance
 * - Range queries using prefix scanning: `gte: "tooluse:conv-123:"`, `lt: "tooluse:conv-123:\xFF"`
 * - Efficient conversation-based loading without intermediate lookups
 * - Batch operations for atomic updates
 */
export class ToolUseStateManager {
  private readonly _logger = getLogger("ToolUseStateManager");

  /** Key-value store instance for storing tool use states */
  private readonly _kvStore: IPluginKvStore;

  /** Suffix used for range query upper bounds in LevelDB */
  private static readonly RANGE_SUFFIX = "\xFF";

  constructor() {
    this._kvStore = getPluginKvStore();
  }

  /**
   * Gets the storage key for a tool use state.
   * Format: tooluse:{conversationId}:{requestId};{toolUseId}
   */
  private _getToolUseStateKey(
    conversationId: string,
    requestId: string,
    toolUseId: string,
  ): string {
    return `${this._getToolUseStatePrefix(conversationId)}${requestId};${toolUseId}`;
  }

  /**
   * Gets the prefix for all tool use states in a conversation.
   * Used for range queries to load all tool use states efficiently.
   */
  private _getToolUseStatePrefix(conversationId: string): string {
    return `tooluse:${conversationId}:`;
  }

  /**
   * Saves tool use states to storage using upsert semantics and optimized batch operations.
   *
   * ## Behavior
   * - Uses upsert semantics: creates new tool use states or updates existing ones
   * - Stores tool use states with keys: `tooluse:{conversationId}:{requestId};{toolUseId}`
   * - Uses atomic batch operations for consistency
   *
   * @param conversationId The conversation ID to store tool use states under
   * @param toolUseStates Record of tool use states to save (upsert)
   */
  async saveToolUseStates(
    conversationId: string,
    toolUseStates: Record<string, ToolUseState>,
  ): Promise<void> {
    const entries = Object.entries(toolUseStates);
    if (entries.length === 0) {
      return;
    }

    this._logger.debug(
      `Saving ${entries.length} tool use states for conversation ${conversationId}`,
    );

    // Build batch operations for tool use states
    const batchOps: Array<KvBatchOperation> = entries.map(([key, state]) => {
      // Extract requestId and toolUseId from the key (format: requestId;toolUseId)
      const [requestId, toolUseId] = key.split(";");
      if (!requestId || !toolUseId) {
        throw new Error(`Invalid tool use state key format: ${key}`);
      }

      return {
        type: "put" as const,
        key: this._getToolUseStateKey(conversationId, requestId, toolUseId),
        value: JSON.stringify(state),
      };
    });

    // Execute all operations in a single atomic batch
    await this._kvStore.batch(batchOps);
    this._logger.debug(
      `Successfully saved ${entries.length} tool use states for conversation ${conversationId}`,
    );
  }

  /**
   * Loads all tool use states for a conversation using efficient prefix scanning.
   *
   * ## Performance
   * - Uses range query: `gte: "tooluse:{conversationId}:"`, `lt: "tooluse:{conversationId}:\xFF"`
   * - No intermediate lookups required
   * - Scales efficiently with conversation size
   *
   * @param conversationId The conversation ID to load tool use states for
   * @returns Record of all tool use states in the conversation
   */
  async loadConversationToolUseStates(
    conversationId: string,
  ): Promise<Record<string, ToolUseState>> {
    this._logger.debug(
      `Loading all tool use states for conversation ${conversationId}`,
    );

    const prefix = this._getToolUseStatePrefix(conversationId);
    const toolUseStates: Record<string, ToolUseState> = {};

    try {
      // Use iterator to scan all tool use states for this conversation
      const options: KvIteratorOptions = {
        gte: prefix,
        lt: prefix + ToolUseStateManager.RANGE_SUFFIX,
      };

      for await (const [key, value] of this._kvStore.iterator(options)) {
        try {
          const toolUseState = JSON.parse(value) as ToolUseState;

          // Extract the tool use state key from the storage key
          // Storage key format: tooluse:{conversationId}:{requestId};{toolUseId}
          // Tool use state key format: {requestId};{toolUseId}
          const keyWithoutPrefix = key.substring(prefix.length);
          toolUseStates[keyWithoutPrefix] = toolUseState;
        } catch (parseError: any) {
          this._logger.error(
            `Failed to parse tool use state at key ${key}`,
            parseError,
          );
        }
      }

      this._logger.debug(
        `Loaded ${Object.keys(toolUseStates).length} tool use states for conversation ${conversationId}`,
      );
      return toolUseStates;
    } catch (error: any) {
      this._logger.error(
        `Failed to load tool use states for conversation ${conversationId}`,
        error,
      );
      return {};
    }
  }

  /**
   * Deletes all tool use states for a conversation.
   *
   * @param conversationId The conversation ID to delete tool use states for
   */
  async deleteConversationToolUseStates(conversationId: string): Promise<void> {
    this._logger.debug(
      `Deleting all tool use states for conversation ${conversationId}`,
    );

    const prefix = this._getToolUseStatePrefix(conversationId);
    const keysToDelete: string[] = [];

    try {
      // Collect all keys to delete
      const options: KvIteratorOptions = {
        gte: prefix,
        lt: prefix + ToolUseStateManager.RANGE_SUFFIX,
      };

      for await (const key of this._kvStore.keys(options)) {
        keysToDelete.push(key);
      }

      if (keysToDelete.length === 0) {
        this._logger.debug(
          `No tool use states found for conversation ${conversationId}`,
        );
        return;
      }

      // Build batch operations to delete all tool use states
      const batchOps: Array<KvBatchOperation> = keysToDelete.map((key) => ({
        type: "del" as const,
        key,
      }));

      // Execute all operations in a single atomic batch
      await this._kvStore.batch(batchOps);

      this._logger.debug(
        `Successfully deleted ${keysToDelete.length} tool use states for conversation ${conversationId}`,
      );
    } catch (error: any) {
      this._logger.error(
        `Failed to delete tool use states for conversation ${conversationId}`,
        error,
      );
      throw error;
    }
  }

  /**
   * Closes the tool use state manager and releases resources.
   */
  async close(): Promise<void> {
    await this._kvStore.close();
  }
}
