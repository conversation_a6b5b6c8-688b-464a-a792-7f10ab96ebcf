import { GitOperationsService } from "../git-operations-service";
import { GitStateManager } from "../../vcs/iso-git/workspace-manager";
import { GitSpecialRef } from "../git-types";
import * as fs from "fs";
import * as path from "path";
import * as os from "os";
import * as git from "isomorphic-git";

describe("GitOperationsService", () => {
  let service: GitOperationsService;
  let tempDir: string;

  beforeEach(async () => {
    tempDir = fs.mkdtempSync(path.join(os.tmpdir(), "git-test-"));
    service = new GitOperationsService(new GitStateManager());

    // Initialize a git repository
    await git.init({ fs, dir: tempDir, defaultBranch: "main" });

    // Configure git user for commits
    await git.setConfig({
      fs,
      dir: tempDir,
      path: "user.name",
      value: "Test User",
    });
    await git.setConfig({
      fs,
      dir: tempDir,
      path: "user.email",
      value: "<EMAIL>",
    });

    // Create an initial commit to establish the main branch
    const initialFile = path.join(tempDir, "README.md");
    fs.writeFileSync(initialFile, "# Test Repository");
    await git.add({ fs, dir: tempDir, filepath: "README.md" });
    await git.commit({
      fs,
      dir: tempDir,
      message: "Initial commit",
      author: { name: "Test User", email: "<EMAIL>" },
    });

    // Track the repository
    await service.trackRepository({ repoRoot: tempDir });
  });

  afterEach(() => {
    if (fs.existsSync(tempDir)) {
      fs.rmSync(tempDir, { recursive: true, force: true });
    }
  });

  describe("trackRepository", () => {
    it("should track a repository successfully", async () => {
      const repoInfo = await service.trackRepository({ repoRoot: tempDir });
      expect(repoInfo.repoRoot).toBe(tempDir);
      expect(repoInfo.isValid).toBe(true);
    });
  });

  describe("getRepositoryState", () => {
    it("should get repository state", async () => {
      const state = await service.getRepositoryState({ repoRoot: tempDir });
      expect(state.workdir).toBe(tempDir);
    });
  });

  describe("validateRepository", () => {
    it("should validate a tracked repository", async () => {
      const isValid = await service.validateRepository({ repoRoot: tempDir });
      expect(isValid).toBe(true);
    });
  });

  describe("getStatusMatrix", () => {
    it("should get status matrix", async () => {
      const statusMatrix = await service.getStatusMatrix({ repoRoot: tempDir });
      expect(Array.isArray(statusMatrix)).toBe(true);
    });

    it("should get status matrix with options", async () => {
      const statusMatrix = await service.getStatusMatrix({
        repoRoot: tempDir,
        includeStatistics: true,
        filterChangedOnly: true,
      });
      expect(Array.isArray(statusMatrix)).toBe(true);
    });
  });

  describe("file operations", () => {
    beforeEach(() => {
      // Create a test file
      const testFile = path.join(tempDir, "test.txt");
      fs.writeFileSync(testFile, "test content");
    });

    it("should stage files", async () => {
      await service.stageFiles({ repoRoot: tempDir, filePaths: ["test.txt"] });
      const statusMatrix = await service.getStatusMatrix({ repoRoot: tempDir });
      expect(statusMatrix.length).toBeGreaterThan(0);
    });

    it("should unstage files", async () => {
      await service.stageFiles({ repoRoot: tempDir, filePaths: ["test.txt"] });
      await service.unstageFiles({
        repoRoot: tempDir,
        filePaths: ["test.txt"],
      });
      const statusMatrix = await service.getStatusMatrix({ repoRoot: tempDir });
      expect(statusMatrix.length).toBeGreaterThan(0);
    });

    it("should stage deleted files", async () => {
      // First stage and commit the file so it exists in HEAD
      await service.stageFiles({ repoRoot: tempDir, filePaths: ["test.txt"] });
      await service.commitChanges({
        repoRoot: tempDir,
        message: "Add test file",
      });

      // Delete the file from working directory
      const testFile = path.join(tempDir, "test.txt");
      fs.unlinkSync(testFile);

      // Stage the deletion - this should use git.remove internally
      await service.stageFiles({ repoRoot: tempDir, filePaths: ["test.txt"] });

      // Verify the deletion is staged
      const statusMatrix = await service.getStatusMatrix({ repoRoot: tempDir });
      const deletedFile = statusMatrix.find(
        (file) => file.qualifiedPathName.relPath === "test.txt",
      );
      expect(deletedFile).toBeDefined();

      // Check that it's marked as deleted and staged
      // Status should be [1, 0, 0] - exists in HEAD, deleted in workdir, deleted in stage
      const [, head, workdir, stage] = deletedFile!.statusRow;
      expect(head).toBe(1); // Exists in HEAD
      expect(workdir).toBe(0); // Deleted in working directory
      expect(stage).toBe(0); // Deletion staged
    });
  });

  describe("commit operations", () => {
    beforeEach(async () => {
      const testFile = path.join(tempDir, "commit-test.txt");
      fs.writeFileSync(testFile, "commit test content");
      await service.stageFiles({
        repoRoot: tempDir,
        filePaths: ["commit-test.txt"],
      });
    });

    it("should commit changes", async () => {
      const commitOid = await service.commitChanges({
        repoRoot: tempDir,
        message: "Test commit",
      });
      expect(typeof commitOid).toBe("string");
      expect(commitOid.length).toBeGreaterThan(0);
    });

    it("should commit with custom author", async () => {
      const commitOid = await service.commitChanges({
        repoRoot: tempDir,
        message: "Test commit with author",
        author: { name: "Test Author", email: "<EMAIL>" },
      });
      expect(typeof commitOid).toBe("string");
      expect(commitOid.length).toBeGreaterThan(0);
    });
  });

  describe("resetToIndex", () => {
    let testFile: string;
    const originalContent = "original content";
    const stagedContent = "staged content";
    const modifiedContent = "modified content";

    beforeEach(async () => {
      testFile = path.join(tempDir, "reset-test.txt");
      fs.writeFileSync(testFile, originalContent);
      await service.stageFiles({
        repoRoot: tempDir,
        filePaths: ["reset-test.txt"],
      });
      await service.commitChanges({
        repoRoot: tempDir,
        message: "Add test file for reset tests",
      });
    });

    it("should reset file to index state when file is staged", async () => {
      // Modify file and stage it
      fs.writeFileSync(testFile, stagedContent);
      await service.stageFiles({
        repoRoot: tempDir,
        filePaths: ["reset-test.txt"],
      });

      // Modify file again in working directory
      fs.writeFileSync(testFile, modifiedContent);

      // Reset to index - should restore staged content
      await service.resetToIndex({
        repoRoot: tempDir,
        filePaths: ["reset-test.txt"],
      });

      // File should have staged content
      const content = fs.readFileSync(testFile, "utf8");
      expect(content).toBe(stagedContent);
    });

    it("should reset file to HEAD state when file is not staged", async () => {
      // Modify file in working directory only (don't stage)
      fs.writeFileSync(testFile, modifiedContent);

      // Reset to index - should restore HEAD content since not staged
      await service.resetToIndex({
        repoRoot: tempDir,
        filePaths: ["reset-test.txt"],
      });

      // File should have original content from HEAD
      const content = fs.readFileSync(testFile, "utf8");
      expect(content).toBe(originalContent);
    });

    it("should remove untracked files", async () => {
      // Create a new untracked file
      const untrackedFile = path.join(tempDir, "untracked.txt");
      fs.writeFileSync(untrackedFile, "untracked content");

      // Verify file exists
      expect(fs.existsSync(untrackedFile)).toBe(true);

      // Reset to index - should remove untracked file
      await service.resetToIndex({
        repoRoot: tempDir,
        filePaths: ["untracked.txt"],
      });

      // File should be removed
      expect(fs.existsSync(untrackedFile)).toBe(false);
    });

    it("should handle mixed scenarios in batch", async () => {
      // Setup multiple files in different states

      // File 1: Staged changes
      const stagedFile = path.join(tempDir, "staged-file.txt");
      fs.writeFileSync(stagedFile, "staged original");
      await service.stageFiles({
        repoRoot: tempDir,
        filePaths: ["staged-file.txt"],
      });
      await service.commitChanges({
        repoRoot: tempDir,
        message: "Add staged file",
      });
      fs.writeFileSync(stagedFile, "staged modified");
      await service.stageFiles({
        repoRoot: tempDir,
        filePaths: ["staged-file.txt"],
      });
      fs.writeFileSync(stagedFile, "working modified");

      // File 2: Unstaged changes only
      const unstagedFile = path.join(tempDir, "unstaged-file.txt");
      fs.writeFileSync(unstagedFile, "unstaged original");
      await service.stageFiles({
        repoRoot: tempDir,
        filePaths: ["unstaged-file.txt"],
      });
      await service.commitChanges({
        repoRoot: tempDir,
        message: "Add unstaged file",
      });
      fs.writeFileSync(unstagedFile, "unstaged modified");

      // File 3: Untracked file
      const untrackedFile = path.join(tempDir, "untracked-batch.txt");
      fs.writeFileSync(untrackedFile, "untracked content");

      // Reset all files
      await service.resetToIndex({
        repoRoot: tempDir,
        filePaths: [
          "staged-file.txt",
          "unstaged-file.txt",
          "untracked-batch.txt",
        ],
      });

      // Verify results
      expect(fs.readFileSync(stagedFile, "utf8")).toBe("staged modified"); // Reset to staged state
      expect(fs.readFileSync(unstagedFile, "utf8")).toBe("unstaged original"); // Reset to HEAD state
      expect(fs.existsSync(untrackedFile)).toBe(false); // Untracked file removed
    });

    it("should handle non-existent files gracefully", async () => {
      // Try to reset a file that doesn't exist anywhere
      await expect(
        service.resetToIndex({
          repoRoot: tempDir,
          filePaths: ["non-existent.txt"],
        }),
      ).resolves.not.toThrow();
    });
  });

  describe("getFileContentForDiff", () => {
    it("should get file content for diff", async () => {
      // Create and stage a file first
      const testFile = path.join(tempDir, "diff-test.txt");
      fs.writeFileSync(testFile, "diff test content");
      await service.stageFiles({
        repoRoot: tempDir,
        filePaths: ["diff-test.txt"],
      });

      const result = await service.getFileContentForDiff(
        tempDir,
        "diff-test.txt",
      );
      expect(result).toBeDefined();

      // Log the result to debug
      console.log("getFileContentForDiff result:", result);

      // The method should return an object with the expected structure
      expect(typeof result).toBe("object");
      expect(result).toHaveProperty("workingContent");
      expect(result).toHaveProperty("stagedContent");
      expect(result).toHaveProperty("headContent");
    });
  });

  describe("enhanced revert operations", () => {
    let testFile: string;
    const originalContent = "original content";
    const modifiedContent = "modified content";
    const stagedContent = "staged content";

    beforeEach(async () => {
      testFile = path.join(tempDir, "revert-test.txt");

      // Create and commit initial file
      fs.writeFileSync(testFile, originalContent);
      await service.stageFiles({
        repoRoot: tempDir,
        filePaths: ["revert-test.txt"],
      });
      await service.commitChanges({
        repoRoot: tempDir,
        message: "Initial commit",
      });
    });

    it("should checkout files to HEAD by default", async () => {
      // Modify file and stage it
      fs.writeFileSync(testFile, stagedContent);
      await service.stageFiles({
        repoRoot: tempDir,
        filePaths: ["revert-test.txt"],
      });

      // Modify file again (unstaged changes)
      fs.writeFileSync(testFile, modifiedContent);

      // Checkout to HEAD (default reference)
      await service.checkoutFiles({
        repoRoot: tempDir,
        filePaths: ["revert-test.txt"],
      });

      // File should have original content (checked out from HEAD)
      const content = fs.readFileSync(testFile, "utf8");
      expect(content).toBe(originalContent);

      // Staging area should still have the staged changes
      const statusMatrix = await service.getStatusMatrix({ repoRoot: tempDir });
      const file = statusMatrix.find(
        (f) => f.qualifiedPathName.relPath === "revert-test.txt",
      );
      expect(file).toBeDefined();
      const [, head, workdir, stage] = file!.statusRow;
      expect(head).toBe(1); // Exists in HEAD
      expect(workdir).toBe(1); // Exists in workdir (now matches HEAD)
      expect(stage).toBe(3); // Staged changes still present (different from HEAD and workdir)
    });

    it("should checkout files to explicit HEAD reference", async () => {
      // Modify file and stage it
      fs.writeFileSync(testFile, stagedContent);
      await service.stageFiles({
        repoRoot: tempDir,
        filePaths: ["revert-test.txt"],
      });

      // Modify file again (unstaged changes)
      fs.writeFileSync(testFile, modifiedContent);

      // Checkout explicitly to HEAD reference
      await service.checkoutFiles({
        repoRoot: tempDir,
        filePaths: ["revert-test.txt"],
        reference: GitSpecialRef.HEAD,
      });

      // File should have original content (checked out from HEAD)
      const content = fs.readFileSync(testFile, "utf8");
      expect(content).toBe(originalContent);

      // Staging area should still have the staged changes
      const statusMatrix = await service.getStatusMatrix({ repoRoot: tempDir });
      const file = statusMatrix.find(
        (f) => f.qualifiedPathName.relPath === "revert-test.txt",
      );
      expect(file).toBeDefined();
      const [, head, workdir, stage] = file!.statusRow;
      expect(head).toBe(1); // Exists in HEAD
      expect(workdir).toBe(1); // Matches HEAD content
      expect(stage).toBe(3); // Staged changes still present
    });

    it("should checkout files to HEAD and reset staging area", async () => {
      // Modify file and stage it
      fs.writeFileSync(testFile, stagedContent);
      await service.stageFiles({
        repoRoot: tempDir,
        filePaths: ["revert-test.txt"],
      });

      // Modify file again (unstaged changes)
      fs.writeFileSync(testFile, modifiedContent);

      // Checkout to HEAD (discards both working directory and staged changes)
      await service.checkoutFiles({
        repoRoot: tempDir,
        filePaths: ["revert-test.txt"],
        reference: GitSpecialRef.HEAD,
      });

      // Reset staging area to match HEAD as well
      await service.unstageFiles({
        repoRoot: tempDir,
        filePaths: ["revert-test.txt"],
      });

      // File should have original content (checked out from HEAD)
      const content = fs.readFileSync(testFile, "utf8");
      expect(content).toBe(originalContent);

      // Both staging area and working directory should match HEAD
      const statusMatrix = await service.getStatusMatrix({ repoRoot: tempDir });
      const file = statusMatrix.find(
        (f) => f.qualifiedPathName.relPath === "revert-test.txt",
      );
      expect(file).toBeDefined();
      const [, head, workdir, stage] = file!.statusRow;
      expect(head).toBe(1); // Exists in HEAD
      expect(workdir).toBe(1); // Matches HEAD
      expect(stage).toBe(1); // Matches HEAD
    });

    it("should checkout files to specific commit SHA", async () => {
      // Modify file and stage it
      fs.writeFileSync(testFile, stagedContent);
      await service.stageFiles({
        repoRoot: tempDir,
        filePaths: ["revert-test.txt"],
      });

      // Modify file again (unstaged changes)
      fs.writeFileSync(testFile, modifiedContent);

      // Get the HEAD commit SHA
      const headSha = await git.resolveRef({
        fs,
        dir: tempDir,
        ref: "HEAD",
      });

      // Checkout to specific commit SHA (should be same as HEAD in this case)
      await service.checkoutFiles({
        repoRoot: tempDir,
        filePaths: ["revert-test.txt"],
        reference: headSha,
      });

      // File should have original content (checked out from specific commit)
      const content = fs.readFileSync(testFile, "utf8");
      expect(content).toBe(originalContent);
    });

    it("should handle checking out deleted files", async () => {
      // Delete the file
      fs.unlinkSync(testFile);

      // Checkout from HEAD to restore the file
      await service.checkoutFiles({
        repoRoot: tempDir,
        filePaths: ["revert-test.txt"],
        reference: GitSpecialRef.HEAD,
      });

      // File should be restored with original content
      expect(fs.existsSync(testFile)).toBe(true);
      const content = fs.readFileSync(testFile, "utf8");
      expect(content).toBe(originalContent);
    });
  });
});
