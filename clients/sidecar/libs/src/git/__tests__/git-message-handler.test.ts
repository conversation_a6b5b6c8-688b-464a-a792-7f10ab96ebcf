import { Git<PERSON>essageHandler } from "../git-message-handler";
import { GitOperationsService } from "../git-operations-service";
import { GitMessageType } from "../git-messages";
import type {
  GitRepoStateRequest,
  GitRepoValidateRequest,
  GitFileStageRequest,
  GitCommitCreateRequest,
} from "../git-messages";

const repoDir = "/tmp/test-repo";

describe("GitMessageHandler", () => {
  let handler: GitMessageHandler;
  let mockService: jest.Mocked<GitOperationsService>;

  beforeEach(() => {
    mockService = {
      trackRepository: jest.fn(),
      listRepositories: jest.fn(),
      discoverRepositories: jest.fn(),
      getRepositoryState: jest.fn(),
      getStatusMatrix: jest.fn(),
      validateRepository: jest.fn(),
      refreshRepository: jest.fn(),
      stageFiles: jest.fn(),
      unstageFiles: jest.fn(),
      revertFiles: jest.fn(),
      commitChanges: jest.fn(),
      getFileContentForDiff: jest.fn(),
      dispose: jest.fn(),
      addDisposable: jest.fn(),
      addDisposables: jest.fn(),
    } as unknown as jest.Mocked<GitOperationsService>;

    handler = new GitMessageHandler(mockService);
  });

  describe("handle", () => {
    it("should handle REPO_STATE_REQUEST", async () => {
      const mockState = { workdir: repoDir };
      // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
      mockService.getRepositoryState.mockResolvedValue(mockState as any);

      const message: GitRepoStateRequest = {
        type: GitMessageType.REPO_STATE_REQUEST,
        data: { repoRoot: repoDir },
      };

      const mockPostMessage = jest.fn();
      await handler.handle(message, mockPostMessage);

      // eslint-disable-next-line @typescript-eslint/unbound-method
      expect(mockService.getRepositoryState).toHaveBeenCalledWith(
        expect.objectContaining({ repoRoot: repoDir }),
      );
      expect(mockPostMessage).toHaveBeenCalledWith(
        expect.objectContaining({
          type: GitMessageType.REPO_STATE_RESPONSE,
        }),
      );
    });

    it("should handle REPO_VALIDATE_REQUEST", async () => {
      mockService.validateRepository.mockResolvedValue(true);

      const message: GitRepoValidateRequest = {
        type: GitMessageType.REPO_VALIDATE_REQUEST,
        data: { repoRoot: repoDir },
      };

      const mockPostMessage = jest.fn();
      await handler.handle(message, mockPostMessage);

      // eslint-disable-next-line @typescript-eslint/unbound-method
      expect(mockService.validateRepository).toHaveBeenCalledWith(
        expect.objectContaining({ repoRoot: repoDir }),
      );
      expect(mockPostMessage).toHaveBeenCalledWith(
        expect.objectContaining({
          type: GitMessageType.REPO_VALIDATE_RESPONSE,
        }),
      );
    });

    it("should handle FILE_STAGE_REQUEST", async () => {
      mockService.stageFiles.mockResolvedValue();

      const message: GitFileStageRequest = {
        type: GitMessageType.FILE_STAGE_REQUEST,
        data: { repoRoot: repoDir, filePaths: ["test.txt"] },
      };

      const mockPostMessage = jest.fn();
      await handler.handle(message, mockPostMessage);

      // eslint-disable-next-line @typescript-eslint/unbound-method
      expect(mockService.stageFiles).toHaveBeenCalledWith(
        expect.objectContaining({
          repoRoot: repoDir,
          filePaths: ["test.txt"],
        }),
      );
      expect(mockPostMessage).toHaveBeenCalledWith(
        expect.objectContaining({
          type: GitMessageType.FILE_STAGE_RESPONSE,
        }),
      );
    });

    it("should handle COMMIT_CREATE_REQUEST", async () => {
      mockService.commitChanges.mockResolvedValue("abc123");

      const message: GitCommitCreateRequest = {
        type: GitMessageType.COMMIT_CREATE_REQUEST,
        data: { repoRoot: repoDir, message: "Test commit" },
      };

      const mockPostMessage = jest.fn();
      await handler.handle(message, mockPostMessage);

      // eslint-disable-next-line @typescript-eslint/unbound-method
      expect(mockService.commitChanges).toHaveBeenCalledWith(
        expect.objectContaining({
          repoRoot: repoDir,
          message: "Test commit",
        }),
      );
      expect(mockPostMessage).toHaveBeenCalledWith(
        expect.objectContaining({
          type: GitMessageType.COMMIT_CREATE_RESPONSE,
        }),
      );
    });
  });
});
