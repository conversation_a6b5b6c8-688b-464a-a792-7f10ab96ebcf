/**
 * Unified Git Types for Agent Edit System
 *
 * Single source of truth for all git-related types.
 * Uses isomorphic-git as foundation with minimal UI extensions.
 * Batch-first API design - all operations accept arrays.
 */

// =============================================================================
// ISOMORPHIC-GIT TYPE IMPORTS
// =============================================================================

/**
 * Organized type imports from isomorphic-git
 */
import type * as git from "isomorphic-git";

import type { IQualifiedPathName } from "../workspace/workspace-types";
import type { ChatAgentFileChangeSummary } from "../webview-messages/message-types/agent-messages";

/**
 * Git operation types for tracking current operations
 * Using const assertion for better tree-shaking and type inference
 */
export const GitOperation = {
  STAGE: "STAGE",
  UNSTAGE: "UNSTAGE",
  REFRESH: "REFRESH",
} as const;

export type GitOperation = (typeof GitOperation)[keyof typeof GitOperation];

/**
 * Special git references for checkout operations
 * Using const assertion for better tree-shaking and type inference
 */
export const GitSpecialRef = {
  /** Checkout from HEAD commit */
  HEAD: "HEAD",
  /** Checkout from staging area (index) */
  INDEX: "INDEX",
  /** Restore working directory state (no-op for checkout, used for reference) */
  WORKTREE: "WORKTREE",
} as const;

export type GitSpecialRef = (typeof GitSpecialRef)[keyof typeof GitSpecialRef];

/**
 * Git reference for checkout operations
 * Can be a special reference (HEAD, INDEX, WORKTREE) or any commit SHA/branch name
 */
// eslint-disable-next-line @typescript-eslint/no-redundant-type-constituents
export type GitReference = GitSpecialRef | string;

/**
 * Git file status interpretation utilities
 * Instead of a single status, we provide utility functions to check various states
 */
export const GitFileStatus = {
  /**
   * Check if file is new (doesn't exist in HEAD)
   */
  isNew: (statusRow: git.StatusRow): boolean => {
    const [, head] = statusRow;
    return head === 0;
  },

  /**
   * Check if file is untracked (new and not staged)
   */
  isUntracked: (statusRow: git.StatusRow): boolean => {
    const [, head, workdir, stage] = statusRow;
    return head === 0 && workdir === 2 && stage === 0;
  },

  /**
   * Check if file has staged changes
   */
  hasStaged: (statusRow: git.StatusRow): boolean => {
    const [, head, , stage] = statusRow;
    return stage !== head;
  },

  /**
   * Check if file has unstaged changes
   */
  hasUnstaged: (statusRow: git.StatusRow): boolean => {
    const [, , workdir, stage] = statusRow;

    // Untracked files should appear in unstaged section for review
    if (GitFileStatus.isUntracked(statusRow)) {
      return true; // Include untracked files in unstaged section
    }

    // File has unstaged changes if workdir differs from stage
    return workdir !== stage;
  },

  /**
   * Check if file is deleted in working directory
   */
  isDeleted: (statusRow: git.StatusRow): boolean => {
    const [, , workdir] = statusRow;
    return workdir === 0;
  },

  /**
   * Check if file deletion is staged
   */
  isDeletionStaged: (statusRow: git.StatusRow): boolean => {
    const [, head, workdir, stage] = statusRow;
    return head === 1 && workdir === 0 && stage !== 1;
  },

  /**
   * Check if file is completely unmodified
   */
  isUnmodified: (statusRow: git.StatusRow): boolean => {
    const [, head, workdir, stage] = statusRow;
    return head === 1 && workdir === 1 && stage === 1;
  },

  /**
   * Check if file is partially staged (has both staged and unstaged changes)
   */
  isPartiallyStaged: (statusRow: git.StatusRow): boolean => {
    return (
      GitFileStatus.hasStaged(statusRow) && GitFileStatus.hasUnstaged(statusRow)
    );
  },

  /**
   * Check if file has any changes since HEAD (added, modified, deleted, staged, etc.)
   * Returns false only for completely unmodified files (1-1-1)
   */
  hasChangedSinceHead: (statusRow: git.StatusRow): boolean => {
    const [, head, workdir, stage] = statusRow;

    // File is unmodified if it exists in HEAD, workdir, and stage are the same
    // Status (1-1-1) means: exists in HEAD, unchanged in workdir, unchanged in stage
    return !(head === 1 && workdir === 1 && stage === 1);
  },
} as const;

/**
 * Git file state using native StatusRow as foundation
 * Minimal wrapper with only essential UI extensions
 */
export interface GitFileState {
  /** File path information (UI extension) */
  qualifiedPathName: IQualifiedPathName;

  /** Native isomorphic-git status row [filepath, head, workdir, stage] */
  statusRow: git.StatusRow;

  /** Optional change summary (UI extension) */
  changesSummary?: ChatAgentFileChangeSummary;
}

/**
 * Factory function to create GitFileState from native StatusRow
 * Uses better type inference and computed properties
 */
export const createGitFileState = (
  statusRow: git.StatusRow,
  qualifiedPathName: IQualifiedPathName,
  changesSummary?: ChatAgentFileChangeSummary,
): GitFileState => ({
  qualifiedPathName,
  statusRow,
  changesSummary,
});

/**
 * SIMPLIFIED: Repository state using native isomorphic-git data
 * Minimal wrapper with only essential UI state
 */
export interface GitRepositoryState {
  /** Current HEAD reference OID (from isomorphic-git resolveRef) */
  headOid: string;

  /** Current branch name (from isomorphic-git currentBranch) */
  currentBranch: string;

  /** Working directory path (native isomorphic-git workdir) */
  workdir: string;

  /** Git directory path (native isomorphic-git gitdir) */
  gitdir: string;

  /** Array of file states (built from native StatusRow[]) */
  files: GitFileState[];

  /** UI state: whether any operation is in progress */
  isOperationInProgress: boolean;

  /** UI state: type of operation currently in progress */
  currentOperation?: GitOperation;

  /** UI state: last error message, if any */
  lastError?: string;

  /** UI state: timestamp of last update */
  lastUpdated: number;
}

/**
 * Factory function to create GitRepositoryState from native isomorphic-git data
 * Uses better type inference and array methods
 */
export const createGitRepositoryState = (
  headOid: string,
  currentBranch: string,
  workdir: string,
  gitdir: string,
  statusMatrix: readonly git.StatusRow[],
  qualifiedPathNames: readonly IQualifiedPathName[],
) =>
  ({
    headOid,
    currentBranch,
    workdir,
    gitdir,
    files: statusMatrix.map((statusRow, index) =>
      createGitFileState(statusRow, qualifiedPathNames[index]),
    ),
    isOperationInProgress: false as const,
    lastUpdated: Date.now(),
  }) as const satisfies Omit<
    GitRepositoryState,
    "currentOperation" | "lastError"
  >;

// =============================================================================
// NATIVE ISOMORPHIC-GIT OPERATION TYPES
// Use isomorphic-git function parameters directly with batch wrappers
// =============================================================================

/**
 * Generic type to extract parameters from isomorphic-git functions
 * Eliminates repetitive parameter extraction patterns
 */
type GitFunctionParams<T extends (...args: any[]) => any> = Parameters<T>[0];

/**
 * Native isomorphic-git operation parameters extracted using generic helper
 */
export type GitAddParams = GitFunctionParams<typeof git.add>;
export type GitRemoveParams = GitFunctionParams<typeof git.remove>;
export type GitCheckoutParams = GitFunctionParams<typeof git.checkout>;

/**
 * Generic batch request type for file operations
 * Eliminates repetition across different git operations
 */
type BatchFileRequest<TParams, TExcludeKey extends keyof TParams = never> = {
  /** Array of file paths (single file = array with 1 element) */
  filePaths: string[];
  /** Optional common parameters for all operations */
  commonParams?: Partial<Omit<TParams, TExcludeKey | "filepath">>;
};

/**
 * Git commit request parameters
 */
export interface GitCommitRequest {
  /** Commit message */
  message: string;
  /** Optional author information (defaults to Augment Agent) */
  author?: {
    name: string;
    email: string;
  };
}

/**
 * Batch wrapper for staging multiple files using native isomorphic-git add
 */
export type GitStageFilesRequest = BatchFileRequest<GitAddParams>;

/**
 * Batch wrapper for unstaging multiple files using native isomorphic-git remove
 */
export type GitUnstageFilesRequest = BatchFileRequest<GitRemoveParams>;

/**
 * Batch wrapper for checking out multiple files to a specific git reference
 */
export interface GitCheckoutFilesRequest {
  /** Array of file paths to checkout */
  filePaths: string[];
  /** Git reference to checkout from (defaults to HEAD) */
  reference?: GitReference;
}

/**
 * Batch wrapper for resetting multiple files to index state
 * Implements smart reset logic:
 * - If file is in index: reset to index state
 * - If file is not in index but in HEAD: reset to HEAD state
 * - If file is untracked: remove entirely
 */
export interface GitResetToIndexFilesRequest {
  /** Array of file paths to reset to index */
  filePaths: string[];
}

/**
 * Request to open a diff view for a file
 */
export interface GitOpenDiffRequest {
  /** Path to the file to diff */
  filePath: string;
  /** Whether to show staged vs HEAD (true) or working vs staged (false) */
  staged?: boolean;
}

// =============================================================================
// MULTI-REPO SHARED TYPES
// =============================================================================

/**
 * Base interface for all multi-repo git operations
 */
export interface GitMultiRepoRequest {
  /** Repository root path - required for all git operations */
  repoRoot: string;
}

/**
 * Repository information returned by discovery and tracking operations
 */
export interface GitRepositoryInfo {
  /** Repository root path */
  repoRoot: string;
  /** Whether a real git repository exists */
  hasRealRepo: boolean;
  /** Current branch name if available */
  currentBranch?: string;
  /** Current HEAD OID if available */
  headOid?: string;
  /** Whether the repository is valid and accessible */
  isValid: boolean;
  /** Last error message if repository is invalid */
  lastError?: string;
}

/**
 * Repository discovery options
 */
export interface GitRepositoryDiscoveryOptions {
  /** Include directories that are not git repositories */
  includeNonGitDirectories?: boolean;
  /** Specific paths to search for repositories */
  searchPaths?: string[];
  /** Maximum depth to search for repositories */
  maxDepth?: number;
}

/**
 * File status request options for multi-repo operations
 */
export interface GitFileStatusOptions extends GitMultiRepoRequest {
  /** Specific file paths to check (optional) */
  filepaths?: string[];
  /** Include line count statistics */
  includeStatistics?: boolean;
  /** Only return files with changes */
  filterChangedOnly?: boolean;
}
