/**
 * Multi-Repository Git Message Types
 *
 * All git operations require a repoRoot parameter for repository identification.
 *
 * Message Naming Convention:
 * - git.repo.* for repository-level operations (state, refresh, validate, discovery)
 * - git.file.* for file-level operations (status, stage, unstage, checkout)
 * - git.commit.* for commit operations
 * - git.diff.* for diff operations
 */

/* eslint-disable @typescript-eslint/no-unsafe-member-access, @typescript-eslint/no-unsafe-call, @typescript-eslint/no-unsafe-return */

import type {
  GitRepositoryState,
  GitStageFilesRequest,
  GitUnstageFilesRequest,
  GitCheckoutFilesRequest,
  GitResetToIndexFilesRequest,
  GitCommitRequest,
  GitFileState,
  GitMultiRepoRequest,
  GitRepositoryInfo,
  GitRepositoryDiscoveryOptions,
  GitFileStatusOptions,
  GitOpenDiffRequest,
} from "./git-types";

/**
 * Multi-repository git message types with namespaced operations
 * All operations require repoRoot parameter for repository identification
 */
export enum GitMessageType {
  // Repository discovery and management
  REPO_LIST_REQUEST = "git.repo.list.request",
  REPO_LIST_RESPONSE = "git.repo.list.response",
  REPO_TRACK_REQUEST = "git.repo.track.request",
  REPO_TRACK_RESPONSE = "git.repo.track.response",
  REPO_INFO_REQUEST = "git.repo.info.request",
  REPO_INFO_RESPONSE = "git.repo.info.response",

  // Repository state operations
  REPO_STATE_REQUEST = "git.repo.state.request",
  REPO_STATE_RESPONSE = "git.repo.state.response",
  REPO_REFRESH_REQUEST = "git.repo.refresh.request",
  REPO_REFRESH_RESPONSE = "git.repo.refresh.response",
  REPO_VALIDATE_REQUEST = "git.repo.validate.request",
  REPO_VALIDATE_RESPONSE = "git.repo.validate.response",

  // File operations
  FILE_STATUS_REQUEST = "git.file.status.request",
  FILE_STATUS_RESPONSE = "git.file.status.response",
  FILE_STAGE_REQUEST = "git.file.stage.request",
  FILE_STAGE_RESPONSE = "git.file.stage.response",
  FILE_UNSTAGE_REQUEST = "git.file.unstage.request",
  FILE_UNSTAGE_RESPONSE = "git.file.unstage.response",
  FILE_CHECKOUT_REQUEST = "git.file.checkout.request",
  FILE_CHECKOUT_RESPONSE = "git.file.checkout.response",
  FILE_RESET_TO_INDEX_REQUEST = "git.file.resetToIndex.request",
  FILE_RESET_TO_INDEX_RESPONSE = "git.file.resetToIndex.response",

  // Commit operations
  COMMIT_CREATE_REQUEST = "git.commit.create.request",
  COMMIT_CREATE_RESPONSE = "git.commit.create.response",

  // Diff operations
  DIFF_OPEN_REQUEST = "git.diff.open.request",
  DIFF_OPEN_RESPONSE = "git.diff.open.response",
}

// =============================================================================
// NEW MULTI-REPO REQUEST MESSAGES (Client to Backend)
// =============================================================================

/**
 * Repository Discovery Messages
 */

export interface GitRepoListRequest {
  type: GitMessageType.REPO_LIST_REQUEST;
  data?: GitRepositoryDiscoveryOptions;
}

export interface GitRepoListResponse {
  type: GitMessageType.REPO_LIST_RESPONSE;
  data: {
    repositories: GitRepositoryInfo[];
  };
}

export interface GitRepoTrackRequest {
  type: GitMessageType.REPO_TRACK_REQUEST;
  data: GitMultiRepoRequest;
}

export interface GitRepoTrackResponse {
  type: GitMessageType.REPO_TRACK_RESPONSE;
  data: GitRepositoryInfo;
}

export interface GitRepoInfoRequest {
  type: GitMessageType.REPO_INFO_REQUEST;
  data: GitMultiRepoRequest;
}

export interface GitRepoInfoResponse {
  type: GitMessageType.REPO_INFO_RESPONSE;
  data: GitRepositoryInfo;
}

/**
 * Repository State Messages
 */

export interface GitRepoStateRequest {
  type: GitMessageType.REPO_STATE_REQUEST;
  data: GitMultiRepoRequest;
}

export interface GitRepoStateResponse {
  type: GitMessageType.REPO_STATE_RESPONSE;
  data: GitRepositoryState & GitMultiRepoRequest;
}

export interface GitRepoRefreshRequest {
  type: GitMessageType.REPO_REFRESH_REQUEST;
  data: GitMultiRepoRequest;
}

export interface GitRepoRefreshResponse {
  type: GitMessageType.REPO_REFRESH_RESPONSE;
  data: {
    success: boolean;
    repoRoot: string;
  };
}

export interface GitRepoValidateRequest {
  type: GitMessageType.REPO_VALIDATE_REQUEST;
  data: GitMultiRepoRequest;
}

export interface GitRepoValidateResponse {
  type: GitMessageType.REPO_VALIDATE_RESPONSE;
  data: {
    isValid: boolean;
    repoRoot: string;
  };
}

/**
 * File Operation Messages
 */

export interface GitFileStatusRequest {
  type: GitMessageType.FILE_STATUS_REQUEST;
  data: GitFileStatusOptions;
}

export interface GitFileStatusResponse {
  type: GitMessageType.FILE_STATUS_RESPONSE;
  data: {
    repoRoot: string;
    statusMatrix: GitFileState[];
  };
}

export interface GitFileStageRequest {
  type: GitMessageType.FILE_STAGE_REQUEST;
  data: GitMultiRepoRequest & GitStageFilesRequest;
}

export interface GitFileStageResponse {
  type: GitMessageType.FILE_STAGE_RESPONSE;
  data: {
    success: boolean;
    repoRoot: string;
    stagedFiles: string[];
  };
}

export interface GitFileUnstageRequest {
  type: GitMessageType.FILE_UNSTAGE_REQUEST;
  data: GitMultiRepoRequest & GitUnstageFilesRequest;
}

export interface GitFileUnstageResponse {
  type: GitMessageType.FILE_UNSTAGE_RESPONSE;
  data: {
    success: boolean;
    repoRoot: string;
    unstagedFiles: string[];
  };
}

export interface GitFileCheckoutRequest {
  type: GitMessageType.FILE_CHECKOUT_REQUEST;
  data: GitMultiRepoRequest & GitCheckoutFilesRequest;
}

export interface GitFileCheckoutResponse {
  type: GitMessageType.FILE_CHECKOUT_RESPONSE;
  data: {
    success: boolean;
    repoRoot: string;
    checkedOutFiles: string[];
    reference: string;
  };
}

export interface GitFileResetToIndexRequest {
  type: GitMessageType.FILE_RESET_TO_INDEX_REQUEST;
  data: GitMultiRepoRequest & GitResetToIndexFilesRequest;
}

export interface GitFileResetToIndexResponse {
  type: GitMessageType.FILE_RESET_TO_INDEX_RESPONSE;
  data: {
    success: boolean;
    repoRoot: string;
    resetFiles: string[];
  };
}

/**
 * Commit Operation Messages
 */

export interface GitCommitCreateRequest {
  type: GitMessageType.COMMIT_CREATE_REQUEST;
  data: GitMultiRepoRequest & GitCommitRequest;
}

export interface GitCommitCreateResponse {
  type: GitMessageType.COMMIT_CREATE_RESPONSE;
  data: {
    success: boolean;
    repoRoot: string;
    commitOid: string;
  };
}

/**
 * Diff Operation Messages
 */

export interface GitDiffOpenRequest {
  type: GitMessageType.DIFF_OPEN_REQUEST;
  data: GitMultiRepoRequest & GitOpenDiffRequest;
}

export interface GitDiffOpenResponse {
  type: GitMessageType.DIFF_OPEN_RESPONSE;
  data: {
    success: boolean;
    repoRoot: string;
  };
}

// =============================================================================
// UNION TYPES
// =============================================================================

/**
 * All git request messages
 */
export type GitRequestMessage =
  | GitRepoListRequest
  | GitRepoTrackRequest
  | GitRepoInfoRequest
  | GitRepoStateRequest
  | GitRepoRefreshRequest
  | GitRepoValidateRequest
  | GitFileStatusRequest
  | GitFileStageRequest
  | GitFileUnstageRequest
  | GitFileCheckoutRequest
  | GitFileResetToIndexRequest
  | GitCommitCreateRequest
  | GitDiffOpenRequest;

/**
 * All git response messages
 */
export type GitResponseMessage =
  | GitRepoListResponse
  | GitRepoTrackResponse
  | GitRepoInfoResponse
  | GitRepoStateResponse
  | GitRepoRefreshResponse
  | GitRepoValidateResponse
  | GitFileStatusResponse
  | GitFileStageResponse
  | GitFileUnstageResponse
  | GitFileCheckoutResponse
  | GitFileResetToIndexResponse
  | GitCommitCreateResponse
  | GitDiffOpenResponse;

/**
 * All git messages
 */
export type GitMessage = GitRequestMessage | GitResponseMessage;

// =============================================================================
// UTILITY FUNCTIONS
// =============================================================================

/**
 * Type guard to check if a message is a git request
 */
export function isGitRequestMessage(
  message: any,
): message is GitRequestMessage {
  return (
    message &&
    typeof message.type === "string" &&
    message.type.startsWith("git.") &&
    message.type.endsWith(".request")
  );
}

/**
 * Type guard to check if a message is a git response
 */
export function isGitResponseMessage(
  message: any,
): message is GitResponseMessage {
  return (
    message &&
    typeof message.type === "string" &&
    message.type.startsWith("git.") &&
    message.type.endsWith(".response")
  );
}

/**
 * Helper to create multi-repo requests with repoRoot
 */
export const GitMultiRepoHelpers = {
  /**
   * Create stage files request for a specific repository
   */
  stageFiles(repoRoot: string, filePaths: string[]): GitFileStageRequest {
    return {
      type: GitMessageType.FILE_STAGE_REQUEST,
      data: { repoRoot, filePaths },
    };
  },

  /**
   * Create unstage files request for a specific repository
   */
  unstageFiles(repoRoot: string, filePaths: string[]): GitFileUnstageRequest {
    return {
      type: GitMessageType.FILE_UNSTAGE_REQUEST,
      data: { repoRoot, filePaths },
    };
  },

  /**
   * Create reset to index files request for a specific repository
   */
  resetToIndexFiles(
    repoRoot: string,
    filePaths: string[],
  ): GitFileResetToIndexRequest {
    return {
      type: GitMessageType.FILE_RESET_TO_INDEX_REQUEST,
      data: { repoRoot, filePaths },
    };
  },

  /**
   * Create repository state request for a specific repository
   */
  getRepoState(repoRoot: string): GitRepoStateRequest {
    return {
      type: GitMessageType.REPO_STATE_REQUEST,
      data: { repoRoot },
    };
  },

  /**
   * Create track repository request for a specific repository
   */
  trackRepository(repoRoot: string): GitRepoTrackRequest {
    return {
      type: GitMessageType.REPO_TRACK_REQUEST,
      data: { repoRoot },
    };
  },

  /**
   * Create file status request for a specific repository
   */
  getFileStatus(
    repoRoot: string,
    options?: {
      filepaths?: string[];
      includeStatistics?: boolean;
      filterChangedOnly?: boolean;
    },
  ): GitFileStatusRequest {
    return {
      type: GitMessageType.FILE_STATUS_REQUEST,
      data: { repoRoot, ...options },
    };
  },
};
