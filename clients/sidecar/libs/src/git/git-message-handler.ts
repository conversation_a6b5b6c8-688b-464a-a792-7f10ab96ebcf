import { getLogger } from "../logging";
import { IWebviewMessageConsumer } from "../webview-messages/webview-messages-broker";
import { WebViewMessage } from "../webview-messages/common-webview-messages";
import {
  GitMessageType,
  GitRequestMessage,
  GitResponseMessage,
  GitRepoTrackRequest,
  GitRepoInfoRequest,
  GitRepoStateRequest,
  GitRepoValidateRequest,
  GitRepoRefreshRequest,
  GitFileStatusRequest,
  GitFileStageRequest,
  GitFileUnstageRequest,
  GitFileCheckoutRequest,
  GitFileResetToIndexRequest,
  GitCommitCreateRequest,
  GitDiffOpenRequest,
} from "./git-messages";
import { GitOperationsService } from "./git-operations-service";
import { getClientActions } from "../client-interfaces/client-actions";
import { QualifiedPathName } from "../workspace/qualified-path-name";

/**
 * Handles git-related messages from webviews
 *
 * @example
 * ```typescript
 * const handler = new GitMessageHandler(gitService);
 * await handler.handle(stageFilesMessage, postMessage);
 * ```
 */
export class GitMessageHandler
  implements IWebviewMessageConsumer<GitMessageType>
{
  private readonly _logger = getLogger("GitMessageHandler");
  public readonly supportedTypes = GitMessageType;

  constructor(private readonly _gitOperationsService: GitOperationsService) {}

  /**
   * Handles incoming git messages and routes them to appropriate handlers
   * @param msg - The git message to handle
   * @param postMessage - Function to send response back to webview
   */
  public async handle(
    msg: WebViewMessage<GitMessageType>,
    postMessage: (msg: WebViewMessage<GitMessageType>) => void,
  ): Promise<void> {
    try {
      this._logger.debug(`Handling git message: ${msg.type}`);

      // Type assertion needed to narrow WebViewMessage to GitRequestMessage
      // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
      const response = await this._handleRequest(msg as GitRequestMessage);
      if (response) {
        postMessage(response);
      }
    } catch (error) {
      this._logger.error(`Error handling git message ${msg.type}:`, error);

      // Send error response
      const responseType = msg.type.replace(
        "_REQUEST",
        "_RESPONSE",
      ) as GitMessageType;
      const msgData = msg as { data?: { repoRoot?: string } };
      postMessage({
        type: responseType,
        data: {
          success: false,
          error: error instanceof Error ? error.message : "Unknown error",
          repoRoot: msgData.data?.repoRoot || "",
        },
      });
    }
  }

  /**
   * Routes git request messages to their specific handlers
   * @param msg - The git request message
   * @returns Promise resolving to response message or undefined
   * @private
   */
  private async _handleRequest(
    msg: GitRequestMessage,
  ): Promise<GitResponseMessage | undefined> {
    switch (msg.type) {
      // Repository operations
      case GitMessageType.REPO_LIST_REQUEST: {
        const repositories =
          await this._gitOperationsService.listRepositories();
        return {
          type: GitMessageType.REPO_LIST_RESPONSE,
          data: { repositories },
        };
      }

      case GitMessageType.REPO_TRACK_REQUEST: {
        // Type assertion needed for discriminated union narrowing
        // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion
        const trackMsg = msg as GitRepoTrackRequest;
        const repositoryInfo = await this._gitOperationsService.trackRepository(
          trackMsg.data,
        );
        return {
          type: GitMessageType.REPO_TRACK_RESPONSE,
          data: repositoryInfo,
        };
      }

      case GitMessageType.REPO_INFO_REQUEST: {
        // Type assertion needed for discriminated union narrowing
        // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion
        const infoMsg = msg as GitRepoInfoRequest;
        const repositoryInfo =
          await this._gitOperationsService.getRepositoryInfo(infoMsg.data);
        return {
          type: GitMessageType.REPO_INFO_RESPONSE,
          data: repositoryInfo,
        };
      }

      case GitMessageType.REPO_STATE_REQUEST: {
        // Type assertion needed for discriminated union narrowing
        // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion
        const stateMsg = msg as GitRepoStateRequest;
        const state = await this._gitOperationsService.getRepositoryState(
          stateMsg.data,
        );
        return {
          type: GitMessageType.REPO_STATE_RESPONSE,
          data: { ...state, repoRoot: stateMsg.data.repoRoot },
        };
      }

      case GitMessageType.REPO_REFRESH_REQUEST: {
        // Type assertion needed for discriminated union narrowing
        // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion
        const refreshMsg = msg as GitRepoRefreshRequest;
        await this._gitOperationsService.refreshRepository(refreshMsg.data);
        return {
          type: GitMessageType.REPO_REFRESH_RESPONSE,
          data: { success: true, repoRoot: refreshMsg.data.repoRoot },
        };
      }

      case GitMessageType.REPO_VALIDATE_REQUEST: {
        // Type assertion needed for discriminated union narrowing
        // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion
        const validateMsg = msg as GitRepoValidateRequest;
        const isValid = await this._gitOperationsService.validateRepository(
          validateMsg.data,
        );
        return {
          type: GitMessageType.REPO_VALIDATE_RESPONSE,
          data: { isValid, repoRoot: validateMsg.data.repoRoot },
        };
      }

      // File operations
      case GitMessageType.FILE_STATUS_REQUEST: {
        // Type assertion needed for discriminated union narrowing
        // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion
        const statusMsg = msg as GitFileStatusRequest;
        const statusMatrix = await this._gitOperationsService.getStatusMatrix(
          statusMsg.data,
        );
        return {
          type: GitMessageType.FILE_STATUS_RESPONSE,
          data: { statusMatrix, repoRoot: statusMsg.data.repoRoot },
        };
      }

      case GitMessageType.FILE_STAGE_REQUEST: {
        // Type assertion needed for discriminated union narrowing
        // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion
        const stageMsg = msg as GitFileStageRequest;
        await this._gitOperationsService.stageFiles(stageMsg.data);
        return {
          type: GitMessageType.FILE_STAGE_RESPONSE,
          data: {
            success: true,
            stagedFiles: stageMsg.data.filePaths,
            repoRoot: stageMsg.data.repoRoot,
          },
        };
      }

      case GitMessageType.FILE_UNSTAGE_REQUEST: {
        // Type assertion needed for discriminated union narrowing
        // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion
        const unstageMsg = msg as GitFileUnstageRequest;
        await this._gitOperationsService.unstageFiles(unstageMsg.data);
        return {
          type: GitMessageType.FILE_UNSTAGE_RESPONSE,
          data: {
            success: true,
            unstagedFiles: unstageMsg.data.filePaths,
            repoRoot: unstageMsg.data.repoRoot,
          },
        };
      }

      case GitMessageType.FILE_CHECKOUT_REQUEST: {
        // Type assertion needed for discriminated union narrowing
        // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion
        const checkoutMsg = msg as GitFileCheckoutRequest;
        await this._gitOperationsService.checkoutFiles(checkoutMsg.data);
        return {
          type: GitMessageType.FILE_CHECKOUT_RESPONSE,
          data: {
            success: true,
            checkedOutFiles: checkoutMsg.data.filePaths,
            repoRoot: checkoutMsg.data.repoRoot,
            reference: checkoutMsg.data.reference || "HEAD",
          },
        };
      }

      case GitMessageType.FILE_RESET_TO_INDEX_REQUEST: {
        // Type assertion needed for discriminated union narrowing
        // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion
        const resetMsg = msg as GitFileResetToIndexRequest;
        await this._gitOperationsService.resetToIndex(resetMsg.data);
        return {
          type: GitMessageType.FILE_RESET_TO_INDEX_RESPONSE,
          data: {
            success: true,
            resetFiles: resetMsg.data.filePaths,
            repoRoot: resetMsg.data.repoRoot,
          },
        };
      }

      // Commit operations
      case GitMessageType.COMMIT_CREATE_REQUEST: {
        // Type assertion needed for discriminated union narrowing
        // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion
        const commitMsg = msg as GitCommitCreateRequest;
        const commitOid = await this._gitOperationsService.commitChanges(
          commitMsg.data,
        );
        return {
          type: GitMessageType.COMMIT_CREATE_RESPONSE,
          data: { success: true, commitOid, repoRoot: commitMsg.data.repoRoot },
        };
      }

      // Diff operations
      case GitMessageType.DIFF_OPEN_REQUEST: {
        // Type assertion needed for discriminated union narrowing
        // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion
        const diffMsg = msg as GitDiffOpenRequest;
        await this._handleOpenDiff(diffMsg);
        return {
          type: GitMessageType.DIFF_OPEN_RESPONSE,
          data: { success: true, repoRoot: diffMsg.data.repoRoot },
        };
      }

      default:
        // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
        this._logger.warn(`Unhandled git message type: ${(msg as any).type}`);
        return Promise.resolve(undefined);
    }
  }

  /**
   * Handles opening a git diff view for a specific file in a repository
   * @param msg - Diff open request with repository and file information
   * @private
   */
  private async _handleOpenDiff(msg: GitDiffOpenRequest): Promise<void> {
    const { repoRoot, filePath, staged } = msg.data;

    try {
      const { headContent, stagedContent, workingContent } =
        await this._gitOperationsService.getFileContentForDiff(
          repoRoot,
          filePath,
        );

      this._logger.info(`Opening diff for ${filePath} in ${repoRoot}`);

      // Determine the original and modified content based on whether we're viewing staged changes
      const originalContent = staged
        ? headContent
        : stagedContent || headContent;
      const modifiedContent = staged ? stagedContent : workingContent;

      // Create qualified path name for the file
      const qualifiedPath = new QualifiedPathName(repoRoot, filePath);

      // Use client actions to show the diff view
      const clientActions = getClientActions();
      await clientActions.showDiffView(
        qualifiedPath,
        originalContent,
        modifiedContent,
        {
          useNativeDiffIfAvailable: true, // Prefer native diff viewer when possible
        },
      );

      this._logger.info(`Successfully opened diff for ${filePath}`);
    } catch (error) {
      this._logger.error(`Failed to open diff for ${filePath}:`, error);
      throw error;
    }
  }
}
