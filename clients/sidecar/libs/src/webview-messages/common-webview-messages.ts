// This defines the base type for webview messages.
// As of time of writing it overlaps with `interface DataWebViewMessage` defined in
// `clients/vscode/src/webview-providers/webview-messages.ts`.
//
// (Tenzin) The main difference I see so far is that all messages which extend this
// WebViewMessage type should be sidecar only messages (which should
// not need extension specific logic, and should be sent with `sendToSidecar`),
// and `sendToSidecar` can typecheck that sent messages are of this type (helping
// to prevent bugs where messages are sent with the wrong method).
// It's definitely somewhat confusing though that this type has the exact same
// structural definition as `interface DataWebViewMessage` and they serve
// largely similar purposes and need to both go through AsyncMessaging.
export type WebViewMessage<T extends string> = {
  type: T;
  data?: unknown;
};

export enum CommonWebViewMessageType {
  empty = "empty",
}
