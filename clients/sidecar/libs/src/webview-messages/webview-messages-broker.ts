// When a message is received from a client, the broker will pass the message

import { getLogger } from "../logging";
import {
  CommonWebViewMessageType,
  WebViewMessage,
} from "./common-webview-messages";

// onto a relevant handler.
export class WebviewMessageBroker<T extends string> {
  private _handlers: Map<T, WebviewResponseCallback<T>> = new Map();
  private _logger = getLogger("WebviewMessageBroker");

  registerHandler<U extends T>(consumer: IWebviewMessageConsumer<U>) {
    const types = Object.values(consumer.supportedTypes);
    for (const t of types) {
      this._handlers.set(t, (msg, postMessage) =>
        consumer.handle(
          msg as WebViewMessage<U>,
          postMessage as PostMessageFn<U>,
        ),
      );
    }
  }

  handle(msg: WebViewMessage<T>, postMessage: PostMessageFn<T>): boolean {
    const handler = this._handlers.get(msg.type);
    if (!handler) {
      this._logger.debug(
        `No webview message handler found for '${msg.type}' in the sidecar broker.`,
      );
      return false;
    }
    void handler(msg, postMessage);
    return true;
  }
}

export type PostMessageFn<X extends string> = (
  msg: WebViewMessage<X | CommonWebViewMessageType>,
) => void;

type WebviewResponseCallback<V extends string> = (
  msg: WebViewMessage<V>,
  postMessage: PostMessageFn<V>,
) => void;

export interface IWebviewMessageConsumer<U extends string> {
  handle: WebviewResponseCallback<U>;
  supportedTypes: Record<string, U>;
}
