/**
 * @file task-messages.ts
 * This file contains the message types for task-related operations.
 */

import { WebViewMessage } from "../common-webview-messages";
import {
  HydratedTask,
  type SerializedTask,
  TaskUpdatedBy,
} from "../../agent/task/task-types";

/**
 * Enum representing the message types for task-related operations.
 */
export enum TaskWebViewMessageType {
  // Task retrieval
  getHydratedTaskRequest = "get-hydrated-task-request",
  getHydratedTaskResponse = "get-hydrated-task-response",
  setCurrentRootTaskUuid = "set-current-root-task-uuid",

  // Task creation and updates
  createTaskRequest = "create-task-request",
  createTaskResponse = "create-task-response",
  updateTaskRequest = "update-task-request",
  updateTaskResponse = "update-task-response",

  // Hydrated task updates
  updateHydratedTaskRequest = "update-hydrated-task-request",
  updateHydratedTaskResponse = "update-hydrated-task-response",
}

/**
 * Interface for the request to get an hydrated task.
 */
export type GetHydratedTaskRequest =
  WebViewMessage<TaskWebViewMessageType.getHydratedTaskRequest> & {
    data: {
      uuid: string;
    };
  };

/**
 * Interface for the response to get an hydrated task.
 */
export type GetHydratedTaskResponse =
  WebViewMessage<TaskWebViewMessageType.getHydratedTaskResponse> & {
    data: {
      task?: HydratedTask;
    };
  };

/**
 * Interface for the request to set the current root task UUID.
 */
export type SetCurrentRootTaskUuid =
  WebViewMessage<TaskWebViewMessageType.setCurrentRootTaskUuid> & {
    data: {
      uuid: string;
    };
  };

/**
 * Interface for the request to create a task.
 */
export type CreateTaskRequest =
  WebViewMessage<TaskWebViewMessageType.createTaskRequest> & {
    data: {
      name: string;
      description: string;
      parentTaskUuid?: string;
    };
  };

/**
 * Interface for the response to create a task.
 */
export type CreateTaskResponse =
  WebViewMessage<TaskWebViewMessageType.createTaskResponse> & {
    data: {
      uuid: string;
    };
  };

/**
 * Interface for the request to update a task.
 */
export type UpdateTaskRequest =
  WebViewMessage<TaskWebViewMessageType.updateTaskRequest> & {
    data: {
      uuid: string;
      updates: Partial<SerializedTask>;
      updatedBy: TaskUpdatedBy;
    };
  };

/**
 * Interface for the response to update a task.
 */
export type UpdateTaskResponse =
  WebViewMessage<TaskWebViewMessageType.updateTaskResponse>;

/**
 * Interface for the request to update a hydrated task.
 * This allows updating an entire task tree at once by diffing the new tree against the existing one.
 */
export type UpdateHydratedTaskRequest =
  WebViewMessage<TaskWebViewMessageType.updateHydratedTaskRequest> & {
    data: {
      task: HydratedTask;
      updatedBy: TaskUpdatedBy;
    };
  };

/**
 * Interface for the response to update a hydrated task.
 */
export type UpdateHydratedTaskResponse =
  WebViewMessage<TaskWebViewMessageType.updateHydratedTaskResponse> & {
    data: {
      created: number;
      updated: number;
      deleted: number;
    };
  };
