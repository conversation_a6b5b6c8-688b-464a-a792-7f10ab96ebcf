/**
 * @file exchange-messages.ts
 * This file contains the message types for exchange-related operations.
 * These messages enable communication between the webview and sidecar for
 * exchange storage operations without exposing storage implementation details.
 */

import { WebViewMessage } from "../common-webview-messages";
import type { StoredExchange } from "../../exchange-storage/storage-types";

/**
 * Enum representing the message types for exchange-related operations.
 * Follows the same pattern as TaskWebViewMessageType and AgentWebViewMessageType.
 */
export enum ExchangeWebViewMessageType {
  // Load operations
  loadConversationExchangesRequest = "load-conversation-exchanges-request",
  loadConversationExchangesResponse = "load-conversation-exchanges-response",

  loadExchangesByUuidsRequest = "load-exchanges-by-uuids-request",
  loadExchangesByUuidsResponse = "load-exchanges-by-uuids-response",

  // Save operations (upsert semantics - creates or updates)
  saveExchangesRequest = "save-exchanges-request",
  saveExchangesResponse = "save-exchanges-response",

  // Delete operations
  deleteExchangesRequest = "delete-exchanges-request",
  deleteExchangesResponse = "delete-exchanges-response",

  deleteConversationExchangesRequest = "delete-conversation-exchanges-request",
  deleteConversationExchangesResponse = "delete-conversation-exchanges-response",

  // Count operations
  countExchangesRequest = "count-exchanges-request",
  countExchangesResponse = "count-exchanges-response",
}

/**
 * Interface for the request to load all exchanges for a conversation.
 */
export type LoadConversationExchangesRequest =
  WebViewMessage<ExchangeWebViewMessageType.loadConversationExchangesRequest> & {
    data: {
      conversationId: string;
    };
  };

/**
 * Interface for the response to load conversation exchanges.
 */
export type LoadConversationExchangesResponse =
  WebViewMessage<ExchangeWebViewMessageType.loadConversationExchangesResponse> & {
    data: {
      exchanges: StoredExchange[];
    };
  };

/**
 * Interface for the request to load specific exchanges by UUIDs.
 */
export type LoadExchangesByUuidsRequest =
  WebViewMessage<ExchangeWebViewMessageType.loadExchangesByUuidsRequest> & {
    data: {
      conversationId: string;
      uuids: string[];
    };
  };

/**
 * Interface for the response to load exchanges by UUIDs.
 */
export type LoadExchangesByUuidsResponse =
  WebViewMessage<ExchangeWebViewMessageType.loadExchangesByUuidsResponse> & {
    data: {
      exchanges: StoredExchange[];
    };
  };

/**
 * Interface for the request to save exchanges using upsert semantics.
 * This operation will create new exchanges or update existing ones based on their UUIDs.
 */
export type SaveExchangesRequest =
  WebViewMessage<ExchangeWebViewMessageType.saveExchangesRequest> & {
    data: {
      conversationId: string;
      exchanges: StoredExchange[];
    };
  };

/**
 * Interface for the response to save exchanges.
 */
export type SaveExchangesResponse =
  WebViewMessage<ExchangeWebViewMessageType.saveExchangesResponse> & {
    data: {};
  };

/**
 * Interface for the request to delete exchanges.
 */
export type DeleteExchangesRequest =
  WebViewMessage<ExchangeWebViewMessageType.deleteExchangesRequest> & {
    data: {
      conversationId: string;
      uuids: string[];
    };
  };

/**
 * Interface for the response to delete exchanges.
 */
export type DeleteExchangesResponse =
  WebViewMessage<ExchangeWebViewMessageType.deleteExchangesResponse> & {
    data: {};
  };

/**
 * Interface for the request to count exchanges.
 */
export type CountExchangesRequest =
  WebViewMessage<ExchangeWebViewMessageType.countExchangesRequest> & {
    data: {
      conversationId: string;
    };
  };

/**
 * Interface for the response to count exchanges.
 */
export type CountExchangesResponse =
  WebViewMessage<ExchangeWebViewMessageType.countExchangesResponse> & {
    data: {
      count: number;
    };
  };

/**
 * Interface for the request to delete all exchanges for a conversation.
 */
export type DeleteConversationExchangesRequest =
  WebViewMessage<ExchangeWebViewMessageType.deleteConversationExchangesRequest> & {
    data: {
      conversationId: string;
    };
  };

/**
 * Interface for the response to delete all exchanges for a conversation.
 */
export type DeleteConversationExchangesResponse =
  WebViewMessage<ExchangeWebViewMessageType.deleteConversationExchangesResponse> & {
    data: {};
  };
