import { Rule } from "../../chat/chat-types";
import { WebViewMessage } from "../common-webview-messages";

/**
 * Rules-related webview message types
 */
export enum RulesWebViewMessageType {
  getRulesListRequest = "get-rules-list-request",
  getRulesListResponse = "get-rules-list-response",
  createRule = "create-rule",
  createRuleResponse = "create-rule-response",
  openRule = "open-rule",
  openGuidelines = "open-guidelines",
  deleteRule = "delete-rule",
  updateRuleFile = "update-rule-file",
  updateRuleFileResponse = "update-rule-file-response",
  getWorkspaceRoot = "get-workspace-root",
  getWorkspaceRootResponse = "get-workspace-root-response",
  autoImportRules = "auto-import-rules",
  autoImportRulesOptionsResponse = "auto-import-rules-options-response",
  autoImportRulesSelectionRequest = "auto-import-rules-selection-request",
  autoImportRulesResponse = "auto-import-rules-response",
  processSelectedPathsRequest = "process-selected-paths-request",
  processSelectedPathsResponse = "process-selected-paths-response",
}

// Request/Response interfaces

export interface GetRulesListRequest
  extends WebViewMessage<RulesWebViewMessageType> {
  type: RulesWebViewMessageType.getRulesListRequest;
  data?: {
    includeGuidelines?: boolean;
    query?: string;
    maxResults?: number;
    contextRules?: Rule[]; // List of rule paths from context to filter MANUAL rules
  };
}

export interface GetRulesListResponse
  extends WebViewMessage<RulesWebViewMessageType> {
  type: RulesWebViewMessageType.getRulesListResponse;
  data: {
    rules: Rule[];
  };
}

export interface CreateRuleRequest
  extends WebViewMessage<RulesWebViewMessageType> {
  type: RulesWebViewMessageType.createRule;
  data?: {
    ruleName?: string;
  };
}

export interface CreateRuleResponse
  extends WebViewMessage<RulesWebViewMessageType> {
  type: RulesWebViewMessageType.createRuleResponse;
  data: {
    importedRulesCount: number;
    createdRule?: {
      path: string;
      repoRoot: string;
    };
  };
}

export interface OpenRuleRequest
  extends WebViewMessage<RulesWebViewMessageType> {
  type: RulesWebViewMessageType.openRule;
  data: {
    path: string;
  };
}

export interface OpenRuleResponse
  extends WebViewMessage<RulesWebViewMessageType> {
  type: RulesWebViewMessageType.openRule;
  data?: {
    filePath?: string;
  };
}

export interface OpenGuidelinesRequest
  extends WebViewMessage<RulesWebViewMessageType> {
  type: RulesWebViewMessageType.openGuidelines;
  data?: {
    workspaceFolder?: string;
  };
}

export interface OpenGuidelinesResponse
  extends WebViewMessage<RulesWebViewMessageType> {
  type: RulesWebViewMessageType.openGuidelines;
  data?: {
    filePath?: string;
  };
}

export interface DeleteRuleRequest
  extends WebViewMessage<RulesWebViewMessageType> {
  type: RulesWebViewMessageType.deleteRule;
  data: {
    path: string;
    confirmed?: boolean;
  };
}

export interface DeleteRuleResponse
  extends WebViewMessage<RulesWebViewMessageType> {
  type: RulesWebViewMessageType.deleteRule;
}

export interface UpdateRuleFileRequest
  extends WebViewMessage<RulesWebViewMessageType> {
  type: RulesWebViewMessageType.updateRuleFile;
  data: {
    path: string;
    content: string;
  };
}

export interface UpdateRuleFileResponse
  extends WebViewMessage<RulesWebViewMessageType> {
  type: RulesWebViewMessageType.updateRuleFileResponse;
  data: {
    success: boolean;
    error?: string;
  };
}

export interface GetWorkspaceRootRequest
  extends WebViewMessage<RulesWebViewMessageType> {
  type: RulesWebViewMessageType.getWorkspaceRoot;
}

export interface GetWorkspaceRootResponse
  extends WebViewMessage<RulesWebViewMessageType> {
  type: RulesWebViewMessageType.getWorkspaceRootResponse;
  data: {
    workspaceRoot: string;
  };
}

export interface AutoImportRulesRequest
  extends WebViewMessage<RulesWebViewMessageType> {
  type: RulesWebViewMessageType.autoImportRules;
}

export interface AutoImportRulesOptionsResponse
  extends WebViewMessage<RulesWebViewMessageType> {
  type: RulesWebViewMessageType.autoImportRulesOptionsResponse;
  data: {
    options: AutoImportRulesOption[];
  };
}

export interface AutoImportRulesOption {
  label: string;
  description: string;
  directory?: string;
  file?: string;
}

export interface AutoImportRulesSelectionRequest
  extends WebViewMessage<RulesWebViewMessageType> {
  type: RulesWebViewMessageType.autoImportRulesSelectionRequest;
  data: {
    selectedLabel: string;
  };
}

export interface AutoImportRulesResponse
  extends WebViewMessage<RulesWebViewMessageType> {
  type: RulesWebViewMessageType.autoImportRulesResponse;
  data: {
    importedRulesCount: number;
    duplicatesCount: number;
    totalAttempted: number;
    source: string;
  };
}

export interface ProcessSelectedPathsRequest
  extends WebViewMessage<RulesWebViewMessageType> {
  type: RulesWebViewMessageType.processSelectedPathsRequest;
  data: {
    selectedPaths: string[];
    autoImport: boolean;
  };
}

export interface ProcessSelectedPathsResponse
  extends WebViewMessage<RulesWebViewMessageType> {
  type: RulesWebViewMessageType.processSelectedPathsResponse;
  data: {
    importedRulesCount: number;
    directoryOrFile: "file" | "directory" | "mixed";
    errors?: string[];
  };
}

// Type guards
export function isRulesMessage(
  msg: WebViewMessage<any>,
): msg is WebViewMessage<RulesWebViewMessageType> {
  return Object.values(RulesWebViewMessageType).includes(
    msg.type as RulesWebViewMessageType,
  );
}
