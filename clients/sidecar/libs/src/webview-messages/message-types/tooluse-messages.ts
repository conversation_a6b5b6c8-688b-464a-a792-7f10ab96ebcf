/**
 * @file tooluse-messages.ts
 * This file contains the message types for tool use state-related operations.
 * These messages enable communication between the webview and sidecar for
 * tool use state storage operations without exposing storage implementation details.
 */

import { WebViewMessage } from "../common-webview-messages";
import type { ToolUseState } from "../../tooluse-storage/types";

/**
 * Enum representing the message types for tool use state-related operations.
 * Follows the same pattern as ExchangeWebViewMessageType and TaskWebViewMessageType.
 */
export enum ToolUseStateWebViewMessageType {
  // Load operations
  loadConversationToolUseStatesRequest = "load-conversation-tooluse-states-request",
  loadConversationToolUseStatesResponse = "load-conversation-tooluse-states-response",

  // Save operations (upsert semantics - creates or updates)
  saveToolUseStatesRequest = "save-tooluse-states-request",
  saveToolUseStatesResponse = "save-tooluse-states-response",

  // Delete operations
  deleteConversationToolUseStatesRequest = "delete-conversation-tooluse-states-request",
  deleteConversationToolUseStatesResponse = "delete-conversation-tooluse-states-response",
}

// Load operations

export type LoadConversationToolUseStatesRequest =
  WebViewMessage<ToolUseStateWebViewMessageType.loadConversationToolUseStatesRequest> & {
    data: {
      conversationId: string;
    };
  };

export type LoadConversationToolUseStatesResponse =
  WebViewMessage<ToolUseStateWebViewMessageType.loadConversationToolUseStatesResponse> & {
    data: {
      toolUseStates: Record<string, ToolUseState>;
    };
  };

// Save operations

export type SaveToolUseStatesRequest =
  WebViewMessage<ToolUseStateWebViewMessageType.saveToolUseStatesRequest> & {
    data: {
      conversationId: string;
      toolUseStates: Record<string, ToolUseState>;
    };
  };

export type SaveToolUseStatesResponse =
  WebViewMessage<ToolUseStateWebViewMessageType.saveToolUseStatesResponse> & {
    data: {
      success: boolean;
    };
  };

// Delete operations

export type DeleteConversationToolUseStatesRequest =
  WebViewMessage<ToolUseStateWebViewMessageType.deleteConversationToolUseStatesRequest> & {
    data: {
      conversationId: string;
    };
  };

export type DeleteConversationToolUseStatesResponse =
  WebViewMessage<ToolUseStateWebViewMessageType.deleteConversationToolUseStatesResponse> & {
    data: {
      success: boolean;
    };
  };
