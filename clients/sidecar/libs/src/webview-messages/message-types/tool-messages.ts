import { ToolApprovalConfig, ToolIdentifier } from "../../tools/tool-types";
import { WebViewMessage } from "../common-webview-messages";

export enum ToolsWebViewMessageType {
  checkToolCallSafeRequest = "check-tool-call-safe-request",
  checkToolCallSafeResponse = "check-tool-call-safe-response",
  closeAllToolProcesses = "close-all-tool-processes",
  getToolIdentifierRequest = "get-tool-identifier-request",
  getToolIdentifierResponse = "get-tool-identifier-response",
}

export type GetToolIdentifierRequest =
  WebViewMessage<ToolsWebViewMessageType.getToolIdentifierRequest> & {
    data: { toolName: string };
  };

export type GetToolIdentifierResponse =
  WebViewMessage<ToolsWebViewMessageType.getToolIdentifierResponse> & {
    data: GetToolIdentifierResponseData;
  };

export type GetToolIdentifierResponseData =
  | {
      found: true;
      toolIdentifier: ToolIdentifier;
      mcpToolName?: string;
      mcpServerName?: string;
    }
  | { found: false };

export type CheckToolCallSafeRequestData = {
  // The name of the tool.
  toolName: string;
  // The input the tool is going to be called with.
  input: Record<string, unknown>;
  // The current agent mode (e.g.: "manual", "auto").
  agentMode?: "manual" | "auto";
  // The tool approval config for the tool, if available.
  toolApprovalConfig?: ToolApprovalConfig;
};

export type CheckToolCallSafeRequest =
  WebViewMessage<ToolsWebViewMessageType.checkToolCallSafeRequest> & {
    data: CheckToolCallSafeRequestData;
  };

export type CheckToolCallSafeResponseData = {
  isSafe: boolean;
};

export type CheckToolCallSafeResponse =
  WebViewMessage<ToolsWebViewMessageType.checkToolCallSafeResponse> & {
    data: CheckToolCallSafeResponseData;
  };
