import { Chat<PERSON><PERSON> } from "../../chat/chat-types";
import { ToolsModel } from "../../tools/tools-model";
import {
  PluginStateNamespace,
  PluginStateScope,
} from "../../client-interfaces/plugin-state";
import * as pluginState from "../../client-interfaces/plugin-state";
import { MockClientFeatureFlags } from "../mocks/mock-client-feature-flags";
import { AggregateCheckpointManager } from "../../agent/checkpoint/aggregate-checkpoint-manager";
import { MockToolApprovalConfigManager } from "../mocks/mock-tool-approval-config-manager";

// Mock the plugin state module
jest.mock("../../client-interfaces/plugin-state", () => {
  const originalModule = jest.requireActual<
    typeof import("../../client-interfaces/plugin-state")
  >("../../client-interfaces/plugin-state");
  return {
    ...originalModule,
    getStateForSidecar: jest.fn(),
  };
});

// Mock the ToolsModel to avoid initialization issues
jest.mock("../../tools/tools-model", () => {
  const actual = jest.requireActual<typeof import("../../tools/tools-model")>(
    "../../tools/tools-model",
  );

  // Create a mock class that extends the actual ToolsModel
  class MockToolsModel extends actual.ToolsModel {
    // Mock the restartHosts method
    restartHosts = jest.fn();
  }

  return {
    ...actual,
    ToolsModel: MockToolsModel,
  };
});

describe("ToolsModel Chat Mode Persistence", () => {
  // Mock plugin state storage
  const mockPluginState = {
    getValue: jest.fn(),
    setValue: jest.fn(),
  };

  // Mock for other dependencies
  const mockClientToolHostFactory = jest.fn();
  const mockRemoteInfoSource = {
    getRemoteToolDefinitions: jest.fn(),
    retrieveRemoteTools: jest.fn(),
    filterToolsWithExtraInput: jest.fn(),
    runRemoteTool: jest.fn(),
  };
  const mockMcpToolsStartupErrorFn = jest.fn();
  const mockClientFeatureFlags = new MockClientFeatureFlags({});
  const mockCheckpointManager = {} as AggregateCheckpointManager;
  const mockGetAgentMemories = jest.fn().mockResolvedValue(undefined);
  const mockGetMemoriesAbsPath = jest.fn().mockReturnValue(undefined);
  const mockGetToolUseRequestEventReporter = jest
    .fn()
    .mockReturnValue(undefined);

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();

    // Setup plugin state mock
    (pluginState.getStateForSidecar as jest.Mock).mockReturnValue(
      mockPluginState,
    );
    mockPluginState.getValue.mockResolvedValue(undefined);
    mockPluginState.setValue.mockResolvedValue(undefined);
  });

  it("should save chat mode to plugin state when mode changes", () => {
    // Create a tools model
    const model = new ToolsModel(
      [], // mcpServers
      mockClientToolHostFactory,
      mockRemoteInfoSource,
      mockMcpToolsStartupErrorFn,
      mockClientFeatureFlags,
      mockCheckpointManager,
      mockGetAgentMemories,
      mockGetMemoriesAbsPath,
      mockGetToolUseRequestEventReporter,
      new MockToolApprovalConfigManager(),
    );

    // Change the mode
    model.setMode(ChatMode.agent);

    // Verify that setValue was called with the correct parameters
    expect(mockPluginState.setValue).toHaveBeenCalledWith(
      PluginStateNamespace.agent,
      "augment/clients/sidecar/chat-mode",
      ChatMode.agent,
      PluginStateScope.global,
    );
  });

  it("should load saved chat mode during initialization", async () => {
    // Setup mock to return a saved mode
    mockPluginState.getValue.mockResolvedValue(ChatMode.agent);

    // Create a tools model
    const model = new ToolsModel(
      [], // mcpServers
      mockClientToolHostFactory,
      mockRemoteInfoSource,
      mockMcpToolsStartupErrorFn,
      mockClientFeatureFlags,
      mockCheckpointManager,
      mockGetAgentMemories,
      mockGetMemoriesAbsPath,
      mockGetToolUseRequestEventReporter,
      new MockToolApprovalConfigManager(),
    );

    // Wait for async initialization to complete
    await new Promise((resolve) => process.nextTick(resolve));

    // Verify that getValue was called with the correct parameters
    expect(mockPluginState.getValue).toHaveBeenCalledWith(
      PluginStateNamespace.agent,
      "augment/clients/sidecar/chat-mode",
      PluginStateScope.global,
    );

    // Verify that the mode was set correctly
    expect(model.chatMode).toBe(ChatMode.agent);
  });

  it("should handle invalid saved mode values", async () => {
    // Setup mock to return an invalid mode
    mockPluginState.getValue.mockResolvedValue("INVALID_MODE");

    // Create a tools model
    const model = new ToolsModel(
      [], // mcpServers
      mockClientToolHostFactory,
      mockRemoteInfoSource,
      mockMcpToolsStartupErrorFn,
      mockClientFeatureFlags,
      mockCheckpointManager,
      mockGetAgentMemories,
      mockGetMemoriesAbsPath,
      mockGetToolUseRequestEventReporter,
      new MockToolApprovalConfigManager(),
    );

    // Wait for async initialization to complete
    await new Promise((resolve) => process.nextTick(resolve));

    // Verify that getValue was called
    expect(mockPluginState.getValue).toHaveBeenCalled();

    // Verify that the mode defaulted to chat
    expect(model.chatMode).toBe(ChatMode.chat);
  });

  it("should handle storage errors gracefully", async () => {
    // Setup mock to throw an error
    mockPluginState.getValue.mockRejectedValue(new Error("Storage error"));

    // Create a tools model
    const model = new ToolsModel(
      [], // mcpServers
      mockClientToolHostFactory,
      mockRemoteInfoSource,
      mockMcpToolsStartupErrorFn,
      mockClientFeatureFlags,
      mockCheckpointManager,
      mockGetAgentMemories,
      mockGetMemoriesAbsPath,
      mockGetToolUseRequestEventReporter,
      new MockToolApprovalConfigManager(),
    );

    // Wait for async initialization to complete
    await new Promise((resolve) => process.nextTick(resolve));

    // Verify that the mode defaulted to chat despite the error
    expect(model.chatMode).toBe(ChatMode.chat);
  });
});
