import {
  GetToolApprovalConfigInput,
  IToolApprovalConfigManager,
  SetToolApprovalConfigInput,
} from "../../tools/approval-config/i-tool-approval-config-manager";
import { ToolIdentifier, ToolApprovalConfig } from "../../tools/tool-types";

/**
 * Mock tool approval config manager for testing.
 *
 * Backed by an in-memory map of paths to ToolApproval configs to simulate an actual store.
 */
export class MockToolApprovalConfigManager
  implements IToolApprovalConfigManager
{
  private _configs: Map<string, ToolApprovalConfig> = new Map();

  getToolApprovalConfig(
    input: GetToolApprovalConfigInput,
  ): Promise<ToolApprovalConfig | undefined> {
    const { toolId } = input;
    const key = this._getKey(toolId);
    return Promise.resolve(this._configs.get(key));
  }

  setToolApprovalConfig(input: SetToolApprovalConfigInput): Promise<void> {
    const { toolId, config } = input;
    const key = this._getKey(toolId);
    this._configs.set(key, config);
    return Promise.resolve();
  }

  // Helper method to generate a consistent key for the tool identifier
  private _getKey(toolId: ToolIdentifier): string {
    return `${toolId.hostName}-${String(toolId.toolId)}`;
  }

  // Test helper methods
  clearConfigs(): void {
    this._configs.clear();
  }

  hasConfig(toolId: ToolIdentifier): boolean {
    const key = this._getKey(toolId);
    return this._configs.has(key);
  }
}
