import { IClientWorkspaces } from "../../client-interfaces/client-workspaces";
import {
  FileDeletedEvent,
  FileDidMoveEvent,
} from "../../agent/agent-edit-types";
import { ISidecarDisposable } from "../../lifecycle/disposable-types";

export class MockClientWorkspaces implements IClientWorkspaces {
  getCwd = jest.fn(() => Promise.resolve(process.cwd()));
  getWorkspaceRoot = jest.fn(() => Promise.resolve("/workspace/root"));
  readFile = jest.fn().mockReturnValue(
    Promise.resolve({
      contents: "file contents",
      filepath: {
        rootPath: "/workspace/root",
        relPath: "example/dir/file.txt",
      },
    }),
  );
  writeFile = jest.fn().mockReturnValue(Promise.resolve());
  deleteFile = jest.fn().mockReturnValue(Promise.resolve());
  getQualifiedPathName = jest.fn().mockImplementation((path: string) =>
    Promise.resolve({
      rootPath: "/workspace/root",
      relPath: path,
    }),
  );
  getPathInfo = jest.fn().mockImplementation(
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    (_path: string) => Promise.resolve({ filepath: undefined }),
  );
  findFiles = jest.fn().mockImplementation(
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    (
      _includeGlob: string,
      _excludeGlob?: string | null,
      _maxResults?: number,
      _timelimit?: number,
    ) => Promise.resolve([]),
  );
  listDirectory = jest.fn().mockImplementation(
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    (_path: string, _depth?: number, _showHidden?: boolean) =>
      Promise.resolve({ entries: [] }),
  );
  getRipgrepPath = jest.fn().mockReturnValue(Promise.resolve("/usr/bin/rg"));

  // File deletion event handling
  private _fileDeletedCallbacks: Set<(event: FileDeletedEvent) => void> =
    new Set();
  onFileDeleted = jest.fn(
    (callback: (event: FileDeletedEvent) => void): ISidecarDisposable => {
      this._fileDeletedCallbacks.add(callback);
      return {
        dispose: () => {
          this._fileDeletedCallbacks.delete(callback);
        },
      };
    },
  );

  // File moved event handling
  private _fileMovedCallbacks: Set<(event: FileDidMoveEvent) => void> =
    new Set();
  onFileDidMove = jest.fn(
    (callback: (event: FileDidMoveEvent) => void): ISidecarDisposable => {
      this._fileMovedCallbacks.add(callback);
      return {
        dispose: () => {
          this._fileMovedCallbacks.delete(callback);
        },
      };
    },
  );

  // Helper methods to trigger events in tests
  emitFileDeleted(event: FileDeletedEvent): void {
    this._fileDeletedCallbacks.forEach((callback) => callback(event));
  }

  emitFileMoved(event: FileDidMoveEvent): void {
    this._fileMovedCallbacks.forEach((callback) => callback(event));
  }
}
