import { APIError, ErrorDetails } from "../exceptions";
import { APIStatus, ErrorCode } from "../utils/types";

describe("exceptions-apierror", () => {
  test("constructor", () => {
    const e = new APIError(APIStatus.unavailable, "test message");
    expect(e.message).toEqual("test message");
    expect(e.status).toEqual(APIStatus.unavailable);
  });

  test("constructor with error details", () => {
    const errorDetails: ErrorDetails = {
      code: ErrorCode.INVALID_TOOL_DEFINITION,
      message: "Invalid tool definition",
      detail: "Tool name contains invalid characters",
      help_uri: "https://docs.augmentcode.com/errors/invalid-tool-definition",
    };
    const e = new APIError(
      APIStatus.invalidArgument,
      "test message",
      errorDetails,
    );
    expect(e.message).toEqual("test message");
    expect(e.status).toEqual(APIStatus.invalidArgument);
    expect(e.errorDetails).toBeDefined();
    expect(e.errorDetails?.code).toEqual(ErrorCode.INVALID_TOOL_DEFINITION);
    expect(e.errorDetails?.message).toEqual("Invalid tool definition");
    expect(e.errorDetails?.detail).toEqual(
      "Tool name contains invalid characters",
    );
    expect(e.errorDetails?.help_uri).toEqual(
      "https://docs.augmentcode.com/errors/invalid-tool-definition",
    );
  });

  test("transientIssue", () => {
    const e = APIError.transientIssue("test message");
    expect(e.message).toEqual("test message");
    expect(e.status).toEqual(APIStatus.unavailable);
  });

  const testCases: [number, APIStatus][] = [
    [400, APIStatus.invalidArgument],
    [401, APIStatus.unauthenticated],
    [403, APIStatus.permissionDenied],
    [413, APIStatus.augmentTooLarge],
    [429, APIStatus.resourceExhausted],
    [499, APIStatus.cancelled],
    [500, APIStatus.unavailable],
    [503, APIStatus.unavailable],
    [504, APIStatus.deadlineExceeded],
    [418, APIStatus.unknown],
  ];
  for (const [code, status] of testCases) {
    test(`fromResponse ${code}`, async () => {
      const resp = new Response(undefined, {
        status: code,
        statusText: "Example Text",
      });
      const e = await APIError.fromResponse(resp);
      expect(e.message).toEqual(`HTTP error: ${code} Example Text`);
      expect(e.status).toEqual(status);
    });
  }

  for (const status of [400, 413, 500, 429]) {
    test(`fromResponse with error details (${status})}`, async () => {
      const errorDetails = {
        code: ErrorCode.INVALID_TOOL_DEFINITION,
        message: "Invalid tool definition",
        detail: "Tool name contains invalid characters",
        help_uri: "https://docs.augmentcode.com/errors/invalid-tool-definition",
      };
      // eslint-disable-next-line @typescript-eslint/naming-convention
      const responseBody = JSON.stringify({ error_details: errorDetails });
      const resp = new Response(responseBody, {
        status: status,
        statusText: "Bad Request",
        // eslint-disable-next-line @typescript-eslint/naming-convention
        headers: { "content-type": "application/json" },
      });

      const e = await APIError.fromResponse(resp);
      expect(e.message).toEqual(`HTTP error: ${status} Bad Request`);
      expect(e.errorDetails).toBeDefined();
      expect(e.errorDetails?.code).toEqual(ErrorCode.INVALID_TOOL_DEFINITION);
      expect(e.errorDetails?.message).toEqual("Invalid tool definition");
      expect(e.errorDetails?.detail).toEqual(
        "Tool name contains invalid characters",
      );
      expect(e.errorDetails?.help_uri).toEqual(
        "https://docs.augmentcode.com/errors/invalid-tool-definition",
      );
    });
  }

  const respCases: [string, string][] = [
    ["application/json", "{}"],
    ["application/json", '{"error": "Internal Server Error"}'],
    ["application/json", '"just a string"'],
    ["application/json", "[]"],
    ["application/json", "null"],
    ["application/json", "true"],
    ["application/json", "1"],
    // eslint-disable-next-line @typescript-eslint/naming-convention
    [
      "text/html",
      JSON.stringify({ error_details: { code: 0, message: "", detail: "" } }),
    ],
  ];
  for (const [i, [contentType, body]] of respCases.entries()) {
    test(`fromResponse with no error details ${i}`, async () => {
      const resp = new Response(body, {
        status: 400,
        statusText: "Bad Request",
        // eslint-disable-next-line @typescript-eslint/naming-convention
        headers: { "content-type": contentType },
      });
      const e = await APIError.fromResponse(resp);
      expect(e.errorDetails).toBeUndefined();
      expect(e.status).toEqual(APIStatus.invalidArgument);
      expect(e.message).toEqual("HTTP error: 400 Bad Request");
    });
  }
});
