import { Analytics } from "@segment/analytics-node";
import {
  SidecarAnalytics,
  AnalyticsContext,
} from "../../analytics/segment-analytics";

jest.mock("@segment/analytics-node");

describe("SidecarAnalytics", () => {
  let analytics: SidecarAnalytics;
  let mockAnalyticsInstance: jest.Mocked<
    Pick<Analytics, "identify" | "track" | "closeAndFlush">
  >;
  let mockContext: AnalyticsContext;

  beforeEach(() => {
    jest.clearAllMocks();

    mockAnalyticsInstance = {
      identify: jest.fn(),
      track: jest.fn(),
      closeAndFlush: jest.fn().mockResolvedValue(undefined),
    };

    const MockedAnalytics = Analytics as jest.MockedClass<typeof Analytics>;
    MockedAnalytics.mockImplementation(
      () => mockAnalyticsInstance as unknown as Analytics,
    );

    mockContext = {
      clientType: "vscode",
      clientVersion: "1.0.0",
      platform: "darwin",
      arch: "x64",
    };
  });

  describe("initialization", () => {
    it("should not initialize when write key is empty", () => {
      analytics = new SidecarAnalytics("");
      analytics.initialize(mockContext, "anonymous-id");

      expect(Analytics).not.toHaveBeenCalled();
    });

    it("should not initialize when write key is whitespace only", () => {
      analytics = new SidecarAnalytics("   ");
      analytics.initialize(mockContext, "anonymous-id");

      expect(Analytics).not.toHaveBeenCalled();
    });

    it("should initialize successfully with valid write key", () => {
      analytics = new SidecarAnalytics("test-write-key");
      analytics.initialize(mockContext, "anonymous-id");

      expect(Analytics).toHaveBeenCalledWith({
        writeKey: "test-write-key",
        flushAt: 10,
        flushInterval: 10000,
      });
    });
  });

  describe("identifyUser", () => {
    beforeEach(() => {
      analytics = new SidecarAnalytics("test-write-key");
      analytics.initialize(mockContext, "anonymous-id");
    });

    it("should identify user with proper context", () => {
      const userId = "user-123";
      const traits = { company: "Test Company", userTier: "pro" };

      analytics.identifyUser(userId, traits);

      expect(mockAnalyticsInstance.identify).toHaveBeenCalledWith({
        userId,
        anonymousId: "anonymous-id",
        traits: {
          ...traits,
          clientType: "vscode",
          clientVersion: "1.0.0",
          platform: "darwin",
        },
      });
    });

    it("should include tenantId in traits when provided", () => {
      const contextWithTenant = {
        ...mockContext,
        tenantId: "test-tenant",
      };

      analytics = new SidecarAnalytics("test-write-key");
      analytics.initialize(contextWithTenant, "anonymous-id");

      const userId = "user-123";
      const traits = { company: "Test Company" };

      analytics.identifyUser(userId, traits);

      expect(mockAnalyticsInstance.identify).toHaveBeenCalledWith({
        userId,
        anonymousId: "anonymous-id",
        traits: {
          ...traits,
          clientType: "vscode",
          clientVersion: "1.0.0",
          platform: "darwin",
          tenantId: "test-tenant",
        },
      });
    });

    it("should not include tenantId in traits when not provided", () => {
      const userId = "user-123";
      const traits = { company: "Test Company" };

      analytics.identifyUser(userId, traits);

      expect(mockAnalyticsInstance.identify).toHaveBeenCalledWith({
        userId,
        anonymousId: "anonymous-id",
        traits: {
          ...traits,
          clientType: "vscode",
          clientVersion: "1.0.0",
          platform: "darwin",
        },
      });
    });
  });

  describe("trackEvent", () => {
    beforeEach(() => {
      analytics = new SidecarAnalytics("test-write-key");
      analytics.initialize(mockContext, "anonymous-id");
    });

    it("should track events with proper context", () => {
      const eventName = "Extension Installed";
      const properties = {
        version: "1.0.0",
        timestamp: new Date().toISOString(),
      };

      analytics.trackEvent(eventName, properties);

      expect(mockAnalyticsInstance.track).toHaveBeenCalledWith({
        userId: undefined,
        anonymousId: "anonymous-id",
        event: eventName,
        properties: {
          ...properties,
          clientType: "vscode",
          clientVersion: "1.0.0",
          platform: "darwin",
        },
      });
    });

    it("should track events with user ID when user is identified", () => {
      analytics.identifyUser("user-123");
      analytics.trackEvent("Test Event");

      expect(mockAnalyticsInstance.track).toHaveBeenCalledWith(
        expect.objectContaining({
          userId: "user-123",
          anonymousId: "anonymous-id",
        }),
      );
    });

    it("should include tenantId in properties when provided", () => {
      const contextWithTenant = {
        ...mockContext,
        tenantId: "test-tenant",
      };

      analytics = new SidecarAnalytics("test-write-key");
      analytics.initialize(contextWithTenant, "anonymous-id");

      const eventName = "Test Event";
      const properties = { version: "1.0.0" };

      analytics.trackEvent(eventName, properties);

      expect(mockAnalyticsInstance.track).toHaveBeenCalledWith({
        userId: undefined,
        anonymousId: "anonymous-id",
        event: eventName,
        properties: {
          ...properties,
          clientType: "vscode",
          clientVersion: "1.0.0",
          platform: "darwin",
          tenantId: "test-tenant",
        },
      });
    });
  });

  describe("dispose", () => {
    beforeEach(() => {
      analytics = new SidecarAnalytics("test-write-key");
      analytics.initialize(mockContext, "anonymous-id");
    });

    it("should dispose resources correctly", async () => {
      await analytics.dispose();

      expect(mockAnalyticsInstance.closeAndFlush).toHaveBeenCalled();
    });

    it("should not throw when disposing uninitialized analytics", async () => {
      const uninitializedAnalytics = new SidecarAnalytics("test-write-key");

      await expect(uninitializedAnalytics.dispose()).resolves.not.toThrow();
    });
  });

  describe("persistent anonymous user ID", () => {
    it("should use the same anonymous ID for all tracking calls", () => {
      const anonymousId = "persistent-anonymous-id";
      analytics = new SidecarAnalytics("test-write-key");
      analytics.initialize(mockContext, anonymousId);

      // Track an event
      analytics.trackEvent("Test Event", { testProp: "value" });

      // Identify a user
      analytics.identifyUser("user-123", { company: "Test Company" });

      // Track another event
      analytics.trackEvent("Another Event");

      // Verify all calls used the same anonymous ID
      expect(mockAnalyticsInstance.track).toHaveBeenCalledTimes(2);
      expect(mockAnalyticsInstance.identify).toHaveBeenCalledTimes(1);

      // Check first track call
      expect(mockAnalyticsInstance.track).toHaveBeenNthCalledWith(
        1,
        expect.objectContaining({
          anonymousId: anonymousId,
        }),
      );

      // Check identify call
      expect(mockAnalyticsInstance.identify).toHaveBeenCalledWith(
        expect.objectContaining({
          anonymousId: anonymousId,
        }),
      );

      // Check second track call
      expect(mockAnalyticsInstance.track).toHaveBeenNthCalledWith(
        2,
        expect.objectContaining({
          anonymousId: anonymousId,
        }),
      );
    });
  });

  describe("PII filtering", () => {
    beforeEach(() => {
      analytics = new SidecarAnalytics("test-write-key");
      analytics.initialize(mockContext, "anonymous-id");
    });

    describe("identifyUser", () => {
      it("should filter out email and name properties from traits", () => {
        const traits = {
          name: "Test User",
          email: "<EMAIL>",
          userTier: "pro",
          company: "Test Company",
        };

        analytics.identifyUser("user-123", traits);

        expect(mockAnalyticsInstance.identify).toHaveBeenCalledWith({
          userId: "user-123",
          anonymousId: "anonymous-id",
          traits: {
            userTier: "pro",
            company: "Test Company",
            clientType: "vscode",
            clientVersion: "1.0.0",
            platform: "darwin",
          },
        });

        // Verify email and name were filtered out
        const callArgs = mockAnalyticsInstance.identify.mock.calls[0][0];
        expect(callArgs.traits).not.toHaveProperty("email");
        expect(callArgs.traits).not.toHaveProperty("name");
      });

      it("should filter out various email and name property formats", () => {
        const traits = {
          // Name properties
          name: "Test User",
          fullName: "Test Full User",
          full_name: "Test Full User 2",
          firstName: "Test",
          first_name: "Test2",
          lastName: "User",
          last_name: "User2",
          displayName: "TestDisplay",
          display_name: "TestDisplay2",
          userName: "testuser",
          user_name: "testuser2",
          // Email properties
          email: "<EMAIL>",
          emailAddress: "<EMAIL>",
          email_address: "<EMAIL>",
          userEmail: "<EMAIL>",
          user_email: "<EMAIL>",
          // Properties that should remain
          company: "Test Company",
          userTier: "pro",
          otherProp: "should remain",
        };

        analytics.identifyUser("user-123", traits);

        const callArgs = mockAnalyticsInstance.identify.mock.calls[0][0];

        // Verify all email properties were filtered out
        expect(callArgs.traits).not.toHaveProperty("email");
        expect(callArgs.traits).not.toHaveProperty("emailAddress");
        expect(callArgs.traits).not.toHaveProperty("email_address");
        expect(callArgs.traits).not.toHaveProperty("userEmail");
        expect(callArgs.traits).not.toHaveProperty("user_email");

        // Verify all name properties were filtered out
        expect(callArgs.traits).not.toHaveProperty("name");
        expect(callArgs.traits).not.toHaveProperty("fullName");
        expect(callArgs.traits).not.toHaveProperty("full_name");
        expect(callArgs.traits).not.toHaveProperty("firstName");
        expect(callArgs.traits).not.toHaveProperty("first_name");
        expect(callArgs.traits).not.toHaveProperty("lastName");
        expect(callArgs.traits).not.toHaveProperty("last_name");
        expect(callArgs.traits).not.toHaveProperty("displayName");
        expect(callArgs.traits).not.toHaveProperty("display_name");
        expect(callArgs.traits).not.toHaveProperty("userName");
        expect(callArgs.traits).not.toHaveProperty("user_name");

        // Verify non-PII properties remain
        expect(callArgs.traits?.company).toBe("Test Company");
        expect(callArgs.traits?.userTier).toBe("pro");
        expect(callArgs.traits?.otherProp).toBe("should remain");
      });
    });

    describe("trackEvent", () => {
      it("should filter out email properties from event properties", () => {
        const properties = {
          action: "button_clicked",
          email: "<EMAIL>",
          timestamp: "2024-01-01T00:00:00Z",
        };

        analytics.trackEvent("User Action", properties);

        expect(mockAnalyticsInstance.track).toHaveBeenCalledWith({
          userId: undefined,
          anonymousId: "anonymous-id",
          event: "User Action",
          properties: {
            action: "button_clicked",
            timestamp: "2024-01-01T00:00:00Z",
            clientType: "vscode",
            clientVersion: "1.0.0",
            platform: "darwin",
          },
        });

        // Verify email was filtered out
        const callArgs = mockAnalyticsInstance.track.mock.calls[0][0];
        expect(callArgs.properties).not.toHaveProperty("email");
      });

      it("should not filter properties that contain 'email' or 'name' as part of a larger word", () => {
        const properties = {
          emailingEnabled: true,
          premail: "some value",
          femailUser: "another value",
          filename: "test.txt",
          renamed: true,
          namespace: "default",
          tournament: "chess",
          validProp: "should remain",
        };

        analytics.trackEvent("Settings Updated", properties);

        const callArgs = mockAnalyticsInstance.track.mock.calls[0][0];

        // These should NOT be filtered as they don't match our PII property patterns
        expect(callArgs.properties?.emailingEnabled).toBe(true);
        expect(callArgs.properties?.premail).toBe("some value");
        expect(callArgs.properties?.femailUser).toBe("another value");
        expect(callArgs.properties?.filename).toBe("test.txt");
        expect(callArgs.properties?.renamed).toBe(true);
        expect(callArgs.properties?.namespace).toBe("default");
        expect(callArgs.properties?.tournament).toBe("chess");
        expect(callArgs.properties?.validProp).toBe("should remain");
      });
    });
  });
});
