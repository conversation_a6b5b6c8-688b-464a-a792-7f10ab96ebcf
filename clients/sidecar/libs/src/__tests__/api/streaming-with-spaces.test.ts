import { SidecarAPIServerImpl } from "../../api/augment-api";
import { setLibraryClientConfig, resetLibraryClientConfig, IClientConfig } from "../../client-interfaces/client-config";
import { setLibraryClientAuth, resetLibrary<PERSON><PERSON>A<PERSON>, IClient<PERSON>uth } from "../../client-interfaces/client-auth";

// Mock fetch function that simulates streaming responses with spaces
function createMockFetch(responseContent: string): jest.MockedFunction<typeof fetch> {
  return jest.fn().mockImplementation(() => {
    const encoder = new TextEncoder();
    const stream = new ReadableStream({
      start(controller) {
        const bytes = encoder.encode(responseContent);
        controller.enqueue(bytes);
        controller.close();
      },
    });

    return Promise.resolve({
      ok: true,
      status: 200,
      headers: new Headers({ "content-type": "application/json" }),
      body: stream,
    });
  });
}

// Mock client config
const mockConfig = {
  apiToken: "test-token",
  completionURL: "http://test-server",
  chat: {
    url: "http://test-server",
    model: "test-model",
  },
  agent: {
    model: "test-agent-model",
  },
  enableDebugFeatures: false,
};

// Create a test subclass to access protected methods
class TestSidecarAPIServerImpl extends SidecarAPIServerImpl {
  public async testCallApiStream<T>(
    requestId: string,
    config: any,
    apiEndpoint: string,
    body?: Record<string, any>,
    convert: (json: any) => T = (json: any) => json
  ): Promise<AsyncIterable<T>> {
    return this.callApiStream(requestId, config, apiEndpoint, body, convert);
  }
}

describe("SidecarAPIServerImpl streaming with spaces", () => {
  let mockFetch: jest.MockedFunction<typeof fetch>;

  beforeEach(() => {
    // Set up ClientConfig before creating the API server
    const clientConfig: IClientConfig = {
      getConfig: async () => mockConfig
    };
    setLibraryClientConfig(clientConfig);

    // Set up ClientAuth
    const clientAuth: IClientAuth = {
      getAPIToken: async () => mockConfig.apiToken,
      getCompletionURL: async () => mockConfig.completionURL,
      removeAuthSession: async () => {}
    };
    setLibraryClientAuth(clientAuth);

    // Initialize mock fetch
    mockFetch = jest.fn();
  });

  afterEach(() => {
    // Clean up ClientConfig and ClientAuth after each test
    resetLibraryClientConfig();
    resetLibraryClientAuth();
  });

  test("handles spaces before JSON payloads in line-delimited stream", async () => {
    const responseContent = [
      '   {"text": "Hello"}',      // Leading spaces
      '     {"text": " world"}',   // More leading spaces
      '\t  {"text": "!"}',         // Tab and spaces
      '{"text": " How are you?"}', // No leading spaces
      '   ',                       // Only spaces (should be ignored)
      '    {"text": " Fine."}',    // Spaces before JSON
    ].join('\n') + '\n';

    mockFetch = createMockFetch(responseContent);

    // Create a new API server instance with the mock fetch
    const testApiServer = new TestSidecarAPIServerImpl(
      "test-session",
      "test-user-agent",
      mockFetch
    );

    const stream = await testApiServer.testCallApiStream(
      "test-request",
      mockConfig,
      "chat-stream",
      { message: "test" },
      (json: any) => json
    );

    const results: any[] = [];
    for await (const result of stream) {
      results.push(result);
    }

    // Should capture all JSON lines, including those with leading spaces
    expect(results).toHaveLength(5);
    expect(results[0]).toEqual({ text: "Hello" });
    expect(results[1]).toEqual({ text: " world" });
    expect(results[2]).toEqual({ text: "!" });
    expect(results[3]).toEqual({ text: " How are you?" });
    expect(results[4]).toEqual({ text: " Fine." });
  });


});
