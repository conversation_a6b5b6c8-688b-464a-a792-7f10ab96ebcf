// Jest <PERSON>napshot v1, https://goo.gl/fbAQLP

exports[`object-utils getPropertiesSize should return the size of the object properties 1`] = `
"name: 5 (string length)
age: 1
isActive: 1
hobbies: (array) 3 (array length) 29 (char length)
  [0]: 7 (string length)
  [1]: 6 (string length)
  [2]: 6 (string length)
address: (object) 3 (object size) 65 (char length)
  street: 11 (string length)
  city: 10 (string length)
  postalCode: 5 (string length)
metadata: (object) 3 (object size) 86 (char length)
  createdAt: 20 (string length)
  tags: (array) 2 (array length) 24 (char length)
    [0]: 7 (string length)
    [1]: 10 (string length)
  hasVisited: 1
nestedArrays: (array) 1 (array length) 67 (char length)
  [0]:   children: (array) 1 (array length) 52 (char length)
    [0]:     grandchildren: (array) 1 (array length) 32 (char length)
      [0]:       greatgrandchildren: 5 (string length)
emptyArray: (array) 0 (array length) 2 (char length)
arrayWithNils: (array) 2 (array length) 11 (char length)
  [0]: 1
  [1]: N/A
emptyObject: (object) 0 (object size) 2 (char length)

objectWithNils: (object) 2 (object size) 11 (char length)
  a1: 1
  a2: N/A
longArrays: (array) 29 (array length) 79 (char length)
  [0]: 1
  [1]: 1
  [2]: 1
  [3]: 1
  [4]: 1
  [5]: 1
  [6]: 1
  [7]: 1
  [8]: 1
  [9]: 1
  [10]: 1
  [11]: 1
  [12]: 1
  [13]: 1
  [14]: 1
  [15]: 1
  [16]: 1
  [17]: 1
  [18]: 1
  [19]: 1
  9 more items..."
`;
