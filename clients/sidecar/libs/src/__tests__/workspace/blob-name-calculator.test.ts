import * as fs from "fs";
import path from "path";

import { BlobNameCalculator } from "../../workspace/blob-name-calculator";

const ANCHOR_PATH = require.resolve("../__fixtures__/anchor.json");
const ANCHOR_TO_REPO_ROOT = "../../../../../..";

const BLOB_PATH = "base/blob_names/test_data/blob-0";
const EXPECTED_BLOBNAME_PATH = "base/blob_names/test_data/blob-0.name";

const blobNameCalculator = new BlobNameCalculator(1000);

describe("blob-names-calculator", () => {
  test("that it calculates a sha256 hash from path and content strings", async () => {
    const repoRoot = path.resolve(
      path.dirname(ANCHOR_PATH),
      ANCHOR_TO_REPO_ROOT,
    );

    const blobPath = path.resolve(repoRoot, BLOB_PATH);
    const blobContents = await fs.promises.readFile(blobPath);
    const blobName = blobNameCalculator.calculate(BLOB_PATH, blobContents);

    const expectedBlobNamePath = path.resolve(repoRoot, EXPECTED_BLOBNAME_PATH);
    const expectedBlobName = (
      await fs.promises.readFile(expectedBlobNamePath, { encoding: "utf8" })
    ).trim();

    expect(blobName).toBe(expectedBlobName);
  });
});
