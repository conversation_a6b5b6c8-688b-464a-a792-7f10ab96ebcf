import { APIStatus, ErrorCode } from "./utils/types";

/**
 * Detailed error information from the API
 */
export interface ErrorDetails {
  code: <PERSON>rrorCode;
  message: string;
  detail: string;
  help_uri?: string;
}

/**
 * APIError is an Error that results from a server error.
 */
export class APIError extends Error {
  public errorDetails?: ErrorDetails;

  constructor(
    public status: APIStatus,
    msg: string,
    errorDetails?: ErrorDetails,
    cause?: unknown,
  ) {
    super(msg, { cause });
    this.errorDetails = errorDetails;
  }

  static transientIssue(err: Error | string): APIError {
    if (err instanceof Error) {
      return new APIError(APIStatus.unavailable, err.message, undefined, err);
    }
    return new APIError(APIStatus.unavailable, err);
  }

  static async fromResponse(resp: Response): Promise<APIError> {
    const status = statusFromHTTPCode(resp.status);
    const baseMsg = `HTTP error: ${resp.status} ${resp.statusText}`;

    // Try to extract error details from the response body
    try {
      const contentType = resp.headers.get("content-type");
      if (contentType && contentType.includes("application/json")) {
        const clonedResp = resp.clone();
        const body = (await clonedResp.json()) as {
          error_details?: ErrorDetails;
        };

        if (body && body.error_details) {
          return new APIError(status, baseMsg, body.error_details);
        }
      }
    } catch (e) {
      // If we can't parse the response body, just return the basic error
      // Using a more TypeScript-friendly logging approach
      if (e instanceof Error) {
        // eslint-disable-next-line no-console
        console.warn(
          `Failed to parse error details from response: ${e.message}`,
        );
      }
    }

    return new APIError(status, baseMsg);
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  static isAPIErrorWithStatus(e: any, s: APIStatus): boolean {
    if (!(e instanceof APIError)) {
      return false;
    }
    return e.status === s;
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  static isRetriableAPIError(e: any): boolean {
    if (!(e instanceof APIError)) {
      return false;
    }
    return retriableStatuses.has(e.status);
  }
}

// The following code is based on gRPC mappings of HTTP codes to gRPC statuses.
// https://github.com/grpc/grpc/blob/master/doc/http-grpc-status-mapping.md
function statusFromHTTPCode(code: number): APIStatus {
  switch (code) {
    case 200:
      return APIStatus.ok;
    case 400:
      return APIStatus.invalidArgument;
    case 401:
      return APIStatus.unauthenticated;
    case 403:
      return APIStatus.permissionDenied;
    case 404:
      return APIStatus.unimplemented;
    case 408:
      return APIStatus.augmentClientTimeout;
    case 413:
      return APIStatus.augmentTooLarge;
    case 426:
      return APIStatus.augmentUpgradeRequired;
    case 429:
      return APIStatus.resourceExhausted;
    case 499:
      return APIStatus.cancelled;
    case 504:
      return APIStatus.deadlineExceeded;
  }
  if (code >= 500 && code < 600) {
    return APIStatus.unavailable;
  }
  return APIStatus.unknown;
}

const retriableStatuses = new Set<APIStatus>([
  APIStatus.unavailable,
  APIStatus.cancelled,
]);

function getCause(e: Error): string {
  if (e.cause instanceof String) {
    return String(e.cause);
  }
  if (e.cause instanceof Object) {
    return JSON.stringify(e.cause);
  }
  return "";
}

export function getErrmsg(e: any, includeCause: boolean = false): string {
  if (e instanceof Error) {
    if (includeCause) {
      const cause = getCause(e);
      if (cause !== "") {
        return `${e.message} (due to ${cause})`;
      }
    }
    return e.message;
  }
  return String(e);
}
