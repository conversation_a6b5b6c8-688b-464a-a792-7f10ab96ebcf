import { Exchange } from "../chat/chat-types";
import { ITool } from "./tool-host-base";
import {
  ToolDefinitionWithSettings,
  ToolHostName,
  ToolType,
  ToolUseResponse,
} from "./tool-types";
import { CheckToolCallSafeRequestData } from "../webview-messages/message-types/tool-messages";

/**
 * A tool host is responsible for providing and managing a set of tools.
 */
export interface IToolHost {
  /**
   * Returns the list of tools provided by this host that are available to use.
   */
  getToolDefinitions(): Promise<ToolDefinitionWithSettings[]>;

  /**
   * Returns all tools, including unavailable ones.
   * @param useCache Whether to use cached tool definitions if available (default: true)
   */
  getAllToolDefinitions(
    useCache?: boolean,
  ): Promise<ToolDefinitionWithSettings[]>;

  /**
   * Get a tool by name if possible.
   *
   * This will return undefined for remote tools that cannot be got.
   *
   * @param toolName The name of the tool to get
   */
  getTool<T extends ToolType>(toolName: string): ITool<T> | undefined;

  /**
   * Returns the host name.
   */
  getName(): ToolHostName;

  /**
   * Calls a tool.
   */
  callTool(
    requestId: string,
    toolUseId: string,
    toolName: string,
    toolInput: Record<string, unknown>,
    chatHistory: Exchange[],
  ): Promise<ToolUseResponse>;

  /**
   * Checks if a tool call is safe.
   */
  checkToolCallSafe(request: CheckToolCallSafeRequestData): Promise<boolean>;

  /**
   * Returns whether a tool call is active.
   */
  isRequestActive(requestId: string, toolUseId: string): boolean;

  /**
   * Closes the tool host.
   */
  close(cancelledByUser?: boolean): Promise<void>;

  /**
   * Closes all running tool processes.
   */
  closeAllToolProcesses(): Promise<void>;

  /**
   * A factory function that can be used to create a new instance of the tool host.
   */
  factory(preconditionWait: Promise<void>): IToolHost;
}
