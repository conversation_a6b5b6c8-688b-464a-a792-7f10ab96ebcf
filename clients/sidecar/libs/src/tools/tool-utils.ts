import { exec, ExecOptions } from "child_process";
import { promisify } from "util";

/**
 * Execute a shell command with timeout
 */
export async function executeCommand(
  command: string,
  options: ExecOptions,
): Promise<string | undefined> {
  const execPromise = promisify(exec);
  try {
    const { stdout } = await execPromise(command, options);
    return stdout.trim();
  } catch {
    return undefined;
  }
}
