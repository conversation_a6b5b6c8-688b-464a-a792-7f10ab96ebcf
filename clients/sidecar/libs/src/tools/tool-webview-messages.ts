import { IWebviewMessageConsumer } from "../webview-messages/webview-messages-broker";
import { getLogger } from "../logging";
import { ToolsModel } from "./tools-model";
import {
  CommonWebViewMessageType,
  WebViewMessage,
} from "../webview-messages/common-webview-messages";
import {
  CheckToolCallSafeRequest,
  CheckToolCallSafeResponse,
  GetToolIdentifierRequest,
  GetToolIdentifierResponse,
  ToolsWebViewMessageType,
} from "../webview-messages/message-types/tool-messages";

/**
 * Handler for tool-related messages from the webviews that are sent
 * to the sidecar with sendToSidecar.
 */
export class ToolsWebviewMessageHandler
  implements IWebviewMessageConsumer<ToolsWebViewMessageType>
{
  private _logger = getLogger("ToolsWebviewMessageHandler");

  public readonly supportedTypes = ToolsWebViewMessageType;

  constructor(private readonly _toolsModel: ToolsModel) {}

  public async handle(
    msg: WebViewMessage<ToolsWebViewMessageType>,
    postMessage: (
      msg: WebViewMessage<ToolsWebViewMessageType | CommonWebViewMessageType>,
    ) => void,
  ): Promise<void> {
    switch (msg.type) {
      case ToolsWebViewMessageType.checkToolCallSafeRequest: {
        const response = await this.toolCheckSafe(
          msg as CheckToolCallSafeRequest,
        );
        postMessage(response);
        break;
      }
      case ToolsWebViewMessageType.closeAllToolProcesses: {
        this._logger.info("Received closeAllToolProcesses message");
        this._toolsModel.closeAllToolProcesses();
        postMessage({ type: CommonWebViewMessageType.empty });
        break;
      }
      case ToolsWebViewMessageType.getToolIdentifierRequest: {
        const response = await this.getToolIdentifier(
          msg as GetToolIdentifierRequest,
        );
        postMessage(response);
        break;
      }
    }
    return Promise.resolve();
  }

  /**
   * Checks if a tool call is safe to execute without user approval.
   *
   * @param message - The tool check safety request message
   * @returns A promise that resolves to a ToolCheckSafeResponse containing whether the tool is safe to run
   *
   * This is part of the tool safety system that determines if a tool needs explicit user
   * approval before execution. Tools can be marked as:
   * - "unsafe": Always needs user approval
   * - "safe": Never needs user approval
   * - "check": Needs approval depending on inputs
   */
  private toolCheckSafe = async (
    message: CheckToolCallSafeRequest,
  ): Promise<CheckToolCallSafeResponse> => {
    const toolCheck = message.data;
    const result = await this._toolsModel.checkToolCallSafe(toolCheck);
    return {
      type: ToolsWebViewMessageType.checkToolCallSafeResponse,
      data: result,
    };
  };

  /**
   * Gets the canonical tool identifier for a given tool name. This function can also be used
   * to check for a tool's existence.
   *
   * @param message - The get tool identifier request message
   * @returns A promise that resolves to a GetToolIdentifierResponse containing the tool identifier
   *  if the tool exists, or an object with `found: false` if the tool does not exist.
   */
  private getToolIdentifier = async (
    message: GetToolIdentifierRequest,
  ): Promise<GetToolIdentifierResponse> => {
    // Get all tool definitions from the tools model and linearly search for the tool name
    // across all tool hosts, returning the identifier of the first match.
    const toolDefinitions = await this._toolsModel.getToolDefinitions();

    // Find the tool with the matching name
    for (const tool of toolDefinitions) {
      if (tool.definition.name === message.data.toolName) {
        return {
          type: ToolsWebViewMessageType.getToolIdentifierResponse,
          data: {
            found: true,
            toolIdentifier: tool.identifier,
            mcpToolName: tool.definition.mcp_tool_name, // optional
            mcpServerName: tool.definition.mcp_server_name, // optional
          },
        };
      }
    }

    // This should never happen as the webview should only ask for tool identifiers
    // that we have, but rather than error out we return to the webview can handle it.
    return {
      type: ToolsWebViewMessageType.getToolIdentifierResponse,
      data: { found: false },
    };
  };
}
