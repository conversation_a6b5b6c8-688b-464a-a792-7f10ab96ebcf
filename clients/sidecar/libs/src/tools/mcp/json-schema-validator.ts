/**
 * JSON Schema validator for 2020-12 specification.
 *
 * This module provides utilities for validating JSON Schema against the 2020-12 specification.
 * Uses Ajv (Another JSON Schema Validator) for validation with the official 2020-12 schema.
 */

import { type AugmentLogger } from "../../logging";
import Ajv from "ajv";
import addFormats from "ajv-formats";

/**
 * Custom error class for JSON Schema validation errors.
 */
export class JsonSchemaValidationError extends Error {
  constructor(message: string) {
    super(message);
    this.name = "JsonSchemaValidationError";
  }
}

/**
 * Validates a JSON Schema against the JSON Schema 2020-12 specification.
 *
 * This method uses Ajv with the official 2020-12 schema to validate the schema structure.
 * It provides proper validation according to the JSON Schema 2020-12 specification.
 * It also supports schemas with different $schema versions.
 *
 * @param schema The JSON Schema to validate
 * @param schemaName The name of the schema (for logging purposes)
 * @param logger The logger instance to use for logging
 * @throws {JsonSchemaValidationError} If the schema is invalid
 */
export function validateJsonSchema(
  schema: any,
  schemaName: string,
  logger: AugmentLogger,
): void {
  try {
    // Check if schema is an object
    if (!schema || typeof schema !== "object" || Array.isArray(schema)) {
      const errorMsg = `Invalid schema for tool ${schemaName}: Schema must be an object`;
      logger.error(errorMsg);
      throw new JsonSchemaValidationError(errorMsg);
    }

    // Clone the schema to avoid modifying the original
    const schemaToValidate = { ...(schema as Record<string, unknown>) };

    // Determine which Ajv instance to use based on $schema
    // let ajv;
    const schemaVersion = schemaToValidate.$schema as string | undefined;

    const ajv = new Ajv({
      allErrors: true,
      validateFormats: true,
    });
    addFormats(ajv);
    logger.debug(
      `Using compatible validator for schema ${schemaName} with $schema: ${schemaVersion}`,
    );
    // }

    // Compile the schema to validate it
    try {
      ajv.compile(schemaToValidate);
      // If we get here, the schema is valid
      logger.debug(
        `Schema for ${schemaName} is valid according to JSON Schema specification`,
      );
    } catch (error) {
      // Format the error message and throw
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      const errorMsg = `Invalid schema for tool ${schemaName}: ${errorMessage}`;
      logger.error(errorMsg);
      throw new JsonSchemaValidationError(errorMsg);
    }
  } catch (error) {
    // If it's already a JsonSchemaValidationError, just re-throw it
    if (error instanceof JsonSchemaValidationError) {
      throw error;
    }

    // Otherwise, wrap it in a JsonSchemaValidationError
    const errorMsg = `Error validating schema for ${schemaName}: ${error instanceof Error ? error.message : String(error)}`;
    logger.error(errorMsg);
    throw new JsonSchemaValidationError(errorMsg);
  }
}
