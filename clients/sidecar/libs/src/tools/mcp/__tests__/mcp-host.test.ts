import { McpHost } from "../mcp-host";
import { McpServerConfig, ToolStartupErrorFn } from "../../tool-types";
import { JsonSchemaValidationError } from "../json-schema-validator";
import { type Client } from "@modelcontextprotocol/sdk/client/index.js";
import { type Tool } from "@modelcontextprotocol/sdk/types.js";

// Mock the logger
const mockLogger = {
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
  log: jest.fn(),
  verbose: jest.fn(),
};

// Mock the json-schema-validator module
jest.mock("../json-schema-validator", () => {
  // Create a proper class for JsonSchemaValidationError
  class MockJsonSchemaValidationError extends Error {
    constructor(message: string) {
      super(message);
      this.name = "JsonSchemaValidationError";
    }
  }

  return {
    validateJsonSchema: jest.fn(),
    JsonSchemaValidationError: MockJsonSchemaValidationError,
  };
});

jest.mock("../../../logging", () => ({
  getLogger: jest.fn().mockReturnValue(mockLogger),
}));

// We need to access private methods for testing, so we'll create a test class
// that exposes the methods we want to test
class TestableHost extends McpHost {
  constructor(config: McpServerConfig, onStartupError: ToolStartupErrorFn) {
    super(config, undefined, onStartupError);
  }

  // Expose private methods for testing
  public createNamespacedToolNameForTest(toolName: string): string {
    return this["createNamespacedToolName"](toolName);
  }

  public extractOriginalToolNameForTest(namespacedToolName: string): string {
    return this["extractOriginalToolName"](namespacedToolName);
  }

  // Expose server name for testing
  public getServerName(): string {
    return this["_serverName"];
  }

  // Expose validation errors for testing
  public getValidationErrors(): JsonSchemaValidationError[] {
    return this["_validationErrors"];
  }

  // Expose client for testing
  public getClientForTest(): Client {
    return this["_client"]!;
  }
}

describe("McpHost", () => {
  // Mock config and error handler
  const mockConfig: McpServerConfig = {
    type: "stdio",
    command: "test-command",
    args: [],
  };
  const mockErrorHandler: jest.MockedFunction<ToolStartupErrorFn> = jest.fn();

  describe("Tool name namespacing", () => {
    let host: TestableHost;

    beforeEach(() => {
      host = new TestableHost(mockConfig, mockErrorHandler);
    });

    it("should create namespaced tool names with server name at the end", () => {
      const toolName = "test-tool";
      const serverName = host.getServerName();
      const namespacedName = host.createNamespacedToolNameForTest(toolName);

      expect(namespacedName).toBe(`${toolName}_${serverName}`);
    });

    it("should extract original tool name from namespaced name", () => {
      const toolName = "test-tool";
      const namespacedName = host.createNamespacedToolNameForTest(toolName);
      const extractedName = host.extractOriginalToolNameForTest(namespacedName);

      expect(extractedName).toBe(toolName);
    });

    it("should handle tool names that would be truncated", () => {
      // Create a very long tool name that will be truncated
      const longToolName =
        "very-long-tool-name-that-will-definitely-be-truncated-due-to-length-limits-in-the-system";
      const namespacedName = host.createNamespacedToolNameForTest(longToolName);
      const extractedName = host.extractOriginalToolNameForTest(namespacedName);

      // The extracted name should match the original, even though the namespaced name was truncated
      expect(extractedName).toBe(longToolName);
      // The namespaced name should be truncated to 64 characters
      expect(namespacedName.length).toBeLessThanOrEqual(64);
    });

    it("should handle multiple tools with different truncation patterns", () => {
      // Create two long tool names with the same prefix but different endings
      const longToolName1 =
        "very-long-tool-name-that-will-be-truncated-version-one";
      const longToolName2 =
        "very-long-tool-name-that-will-be-truncated-version-two";

      const namespacedName1 =
        host.createNamespacedToolNameForTest(longToolName1);
      const namespacedName2 =
        host.createNamespacedToolNameForTest(longToolName2);

      const extractedName1 =
        host.extractOriginalToolNameForTest(namespacedName1);
      const extractedName2 =
        host.extractOriginalToolNameForTest(namespacedName2);

      // Both extracted names should match their originals
      expect(extractedName1).toBe(longToolName1);
      expect(extractedName2).toBe(longToolName2);

      // The namespaced names should be different
      expect(namespacedName1).not.toBe(namespacedName2);
    });

    it("should fall back to suffix extraction when tool is not in the map", () => {
      const serverName = host.getServerName();
      const toolName = "unknown-tool";
      const namespacedName = `${toolName}_${serverName}`;

      // This tool name wasn't created through createNamespacedToolName, so it won't be in the map
      const extractedName = host.extractOriginalToolNameForTest(namespacedName);

      // Should still extract correctly using the suffix pattern
      expect(extractedName).toBe(toolName);
    });

    it("should return the original name if extraction fails", () => {
      const unrelatedName = "some-unrelated-tool-name";

      // This name has no relation to our server name
      const extractedName = host.extractOriginalToolNameForTest(unrelatedName);

      // Should return the original name unchanged
      expect(extractedName).toBe(unrelatedName);
    });
  });

  describe("Server name handling", () => {
    it("should extract server name from command", () => {
      const host = new TestableHost(
        { type: "stdio", command: "/path/to/test-server", args: [] },
        mockErrorHandler,
      );

      expect(host.getServerName()).toBe("test-server");
    });

    it("should use provided name if available", () => {
      const host = new TestableHost(
        {
          type: "stdio",
          command: "/path/to/test-server",
          args: [],
          name: "custom-server-name",
        },
        mockErrorHandler,
      );

      expect(host.getServerName()).toBe("custom-server-name");
    });

    it("should sanitize server name", () => {
      const host = new TestableHost(
        {
          type: "stdio",
          command: "/path/to/test-server",
          args: [],
          name: "custom server name with spaces!",
        },
        mockErrorHandler,
      );

      // Special characters should be replaced with underscores
      expect(host.getServerName()).toBe("custom_server_name_with_spaces_");
    });
    describe("parseToolSchema", () => {
      let host: TestableHost;
      // Type assertion to handle the any type from jest.requireMock
      const validateJsonSchemaMock = jest.requireMock(
        "../json-schema-validator",
        // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
      ).validateJsonSchema as jest.Mock;

      beforeEach(() => {
        host = new TestableHost(mockConfig, mockErrorHandler);
        validateJsonSchemaMock.mockClear();
      });

      it("should return an enabled tool when schema validation passes", () => {
        // Mock the validateJsonSchema function to not throw an error
        validateJsonSchemaMock.mockImplementation(() => {
          // Do nothing, which means validation passes
        });

        // Create a mock tool with a valid schema
        const mockTool: Tool = {
          name: "valid-tool",
          description: "A valid tool",
          inputSchema: {
            type: "object",
            properties: {
              foo: { type: "string" },
            },
          },
        };

        // Call parseToolSchema
        const result = host.parseToolSchema(mockTool); // Type assertion to bypass Zod validation in tests

        // Verify validateJsonSchema was called with the correct arguments
        expect(validateJsonSchemaMock).toHaveBeenCalledWith(
          mockTool.inputSchema,
          mockTool.name,
          expect.anything(),
        );

        // Verify the result is correct
        expect(result.enabled).toBe(true);
        expect(result.definition.name).toContain(mockTool.name);
        expect(result.definition.description).toBe(mockTool.description);
        expect(result.definition.input_schema_json).toBe(
          JSON.stringify(mockTool.inputSchema),
        );
        expect(result.definition.mcp_tool_name).toBe(mockTool.name);
      });

      it("should return a disabled tool when schema validation fails", () => {
        // Mock the validateJsonSchema function to throw an error
        const validationError = new JsonSchemaValidationError(
          "Invalid schema: missing required property",
        );
        validateJsonSchemaMock.mockImplementation(() => {
          throw validationError;
        });

        // Create a mock tool with an invalid schema
        const mockTool: Tool = {
          name: "invalid-tool",
          description: "An invalid tool",
          inputSchema: {
            // Invalid schema missing required properties
            type: "object",
          },
        };

        // Call parseToolSchema
        const result = host.parseToolSchema(mockTool); // Type assertion to bypass Zod validation in tests

        // Verify validateJsonSchema was called
        expect(validateJsonSchemaMock).toHaveBeenCalled();

        // Verify the result is correct
        expect(result.enabled).toBe(false);
        expect(result.definition.name).toContain(mockTool.name);
        expect(result.definition.description).toBe(mockTool.description);
      });

      it("should add JsonSchemaValidationError to _validationErrors array", () => {
        // Mock the validateJsonSchema function to throw a JsonSchemaValidationError
        const validationError = new JsonSchemaValidationError(
          "Invalid schema: missing required property",
        );
        validateJsonSchemaMock.mockImplementation(() => {
          throw validationError;
        });

        // Create a mock tool with an invalid schema
        const mockTool: Tool = {
          name: "invalid-tool",
          inputSchema: {
            type: "object",
          },
        };

        // Call parseToolSchema
        host.parseToolSchema(mockTool); // Type assertion to bypass Zod validation in tests

        // Verify the validation error was added to the _validationErrors array
        const validationErrors = host.getValidationErrors();
        expect(validationErrors.length).toBe(1);
        expect(validationErrors[0]).toBeInstanceOf(JsonSchemaValidationError);
        expect(validationErrors[0].message).toBe(validationError.message);
      });

      it("should wrap non-JsonSchemaValidationError errors in a JsonSchemaValidationError", () => {
        // Mock the validateJsonSchema function to throw a generic Error
        const genericError = new Error("Something went wrong");
        validateJsonSchemaMock.mockImplementation(() => {
          throw genericError;
        });

        // Create a mock tool
        const mockTool: Tool = {
          name: "error-tool",
          inputSchema: {
            type: "object",
          },
        };

        // Call parseToolSchema
        host.parseToolSchema(mockTool); // Type assertion to bypass Zod validation in tests

        // Verify the error was wrapped and added to the _validationErrors array
        const validationErrors = host.getValidationErrors();
        expect(validationErrors.length).toBe(1);
        expect(validationErrors[0]).toBeInstanceOf(JsonSchemaValidationError);
        expect(validationErrors[0].message).toContain(genericError.message);
      });

      it("should include server name and original tool name in the tool definition", () => {
        // Mock the validateJsonSchema function to not throw an error
        validateJsonSchemaMock.mockImplementation(() => {
          // Do nothing, which means validation passes
        });

        // Create a mock tool
        const mockTool: Tool = {
          name: "server-name-test-tool",
          inputSchema: {
            type: "object",
          },
        };

        // Call parseToolSchema
        const result = host.parseToolSchema(mockTool); // Type assertion to bypass Zod validation in tests

        // Verify the server name and original tool name are included in the definition
        expect(result.definition.mcp_server_name).toBe(host.getServerName());
        expect(result.definition.mcp_tool_name).toBe(mockTool.name);
      });
    });
  });
});
