import {
  ToolDefinitionWithSettings,
  ToolHostName,
  ToolSafety,
  ToolType,
} from "./tool-types";

export enum REMOTE_PARTNER_MCP_SERVERS {
  STRIPE = "augment-partner-remote-mcp-stripe",
  SENTRY = "augment-partner-remote-mcp-sentry",
}

enum LOCAL_PARTNER_MCP_SERVERS {
  REDIS = "Redis",
  MONGODB = "MongoDB",
  CIRCLECI = "CircleCI",
}

export const PARTNER_MCP_SERVERS = {
  ...REMOTE_PARTNER_MCP_SERVERS,
  ...LOCAL_PARTNER_MCP_SERVERS,
} as const;

export type PARTNER_MCP_SERVERS =
  (typeof PARTNER_MCP_SERVERS)[keyof typeof PARTNER_MCP_SERVERS];

const STRIPE_MCP_TOOL: ToolDefinitionWithSettings = {
  definition: {
    name: PARTNER_MCP_SERVERS.STRIPE as ToolType,
    description: "Stripe MCP Server",
    input_schema_json: "{}",
    tool_safety: ToolSafety.Safe,
    mcp_server_name: PARTNER_MCP_SERVERS.STRIPE,
    mcp_tool_name: PARTNER_MCP_SERVERS.STRIPE,
    original_mcp_server_name: "stripe-mcp-remote",
  },
  identifier: {
    hostName: ToolHostName.mcpHost,
    toolId: PARTNER_MCP_SERVERS.STRIPE,
  },
  isConfigured: false,
  enabled: false,
  toolSafety: ToolSafety.Safe,
};

const SENTRY_MCP_TOOL: ToolDefinitionWithSettings = {
  definition: {
    name: PARTNER_MCP_SERVERS.SENTRY as ToolType,
    description: "Sentry MCP Server",
    input_schema_json: "{}",
    tool_safety: ToolSafety.Safe,
    mcp_server_name: PARTNER_MCP_SERVERS.SENTRY,
    mcp_tool_name: PARTNER_MCP_SERVERS.SENTRY,
    original_mcp_server_name: "sentry-mcp-remote",
  },
  identifier: {
    hostName: ToolHostName.mcpHost,
    toolId: PARTNER_MCP_SERVERS.SENTRY,
  },
  isConfigured: false,
  enabled: false,
  toolSafety: ToolSafety.Safe,
};

interface PartnerRemoteMCP {
  authDefinition: {
    baseUrl: string;
    tokenUrl: string;
    resourceMetadataUrl: string;
    params: {
      response_type: string;
      resource: string;
      redirect_uri: string;
    };
  };
  toolDefinition: ToolDefinitionWithSettings;
}

export const REMOTE_PARTNER_MCP_CONFIG: Record<
  REMOTE_PARTNER_MCP_SERVERS,
  PartnerRemoteMCP
> = {
  [PARTNER_MCP_SERVERS.STRIPE]: {
    authDefinition: {
      baseUrl: "https://marketplace.stripe.com/oauth/v2/authorize",
      tokenUrl: "https://marketplace.stripe.com/oauth/v2/token",
      resourceMetadataUrl:
        "https://mcp.stripe.com/.well-known/oauth-authorization-server",
      params: {
        response_type: "code",
        resource: "https://mcp.stripe.com",
        redirect_uri: "vscode://augment.vscode-augment/auth/mcp/stripe",
      },
    },
    toolDefinition: STRIPE_MCP_TOOL,
  },
  [PARTNER_MCP_SERVERS.SENTRY]: {
    authDefinition: {
      baseUrl: "https://mcp.sentry.dev/oauth/authorize",
      tokenUrl: "https://mcp.sentry.dev/oauth/token",
      resourceMetadataUrl:
        "https://mcp.sentry.dev/.well-known/oauth-authorization-server",
      params: {
        response_type: "code",
        resource: "https://mcp.sentry.dev/mcp",
        redirect_uri: "vscode://augment.vscode-augment/auth/mcp/sentry",
      },
    },
    toolDefinition: SENTRY_MCP_TOOL,
  },
};
