import { spawn } from "child_process";
import * as path from "path";
import { Exchange } from "../../chat/chat-types";
import { getLogger, type AugmentLogger } from "../../logging";
import { ToolBase, ToolSafety, ToolUseResponse } from "../tool-types";
import { SidecarToolType } from "./sidecar-tool-types";
import { successToolResponse, errorToolResponse } from "./tool-use-response";
import { getClientWorkspaces } from "../../client-interfaces/client-workspaces";
import { getClientFeatureFlags } from "../../client-interfaces/feature-flags";
import { REGEX_SYNTAX_GUIDE } from "./regex-syntax-guide";

type Inputs = {
  directory_absolute_path: string;
  query: string;
  case_sensitive: boolean;
  files_include_glob_pattern?: string;
  files_exclude_glob_pattern?: string;
  context_lines_before: number;
  context_lines_after: number;
  disable_ignore_files: boolean;
};

export class GrepSearchTool extends ToolBase<SidecarToolType> {
  private readonly _logger: AugmentLogger;
  private _ripgrepPath: string | undefined = undefined;

  constructor() {
    super(SidecarToolType.grepSearch, ToolSafety.Safe);
    this._logger = getLogger("GrepSearchTool");
  }

  private get _defaultContextNumLinesBefore(): number {
    return getClientFeatureFlags().flags.grepSearchToolNumContextLines ?? 5;
  }

  private get _defaultContextNumLinesAfter(): number {
    return getClientFeatureFlags().flags.grepSearchToolNumContextLines ?? 5;
  }

  public readonly description: string = `
Runs a fast, exact regex search over text files using the ripgrep engine. Useful for finding exact text matches or patterns.

Use the following regex syntax for \`query\`:
${REGEX_SYNTAX_GUIDE}

# File filter behavior
- Only files matching \`files_include_glob_pattern\` would be searched
- Files matching \`files_exclude_glob_pattern\` would be skipped even if they match \`files_include_glob_pattern\`
- \`files_include_glob_pattern\` and \`files_exclude_glob_pattern\` should be standard glob patterns. Pipe character '|' is not supported.
`;

  public readonly inputSchemaJson: string = JSON.stringify({
    /* eslint-disable @typescript-eslint/naming-convention */
    type: "object",
    properties: {
      directory_absolute_path: {
        type: "string",
        description: "Absolute path to the directory to search in.",
      },
      query: {
        type: "string",
        description:
          "The regex pattern to search for. Only use core regex syntax common to JavaScript and Rust. See the regex syntax guide in the tool description",
      },
      case_sensitive: {
        type: "boolean",
        description:
          "Optional parameter for case sensitivity when matching `query`. By default matching is not case sensitive.",
      },
      files_include_glob_pattern: {
        type: "string",
        description: "Optional glob pattern for files to include",
      },
      files_exclude_glob_pattern: {
        type: "string",
        description: "Optional glob pattern for files to exclude",
      },
      context_lines_before: {
        type: "integer",
        description: `Optional parameter for number of lines of context to include before the match. Default is ${this._defaultContextNumLinesBefore}`,
      },
      context_lines_after: {
        type: "integer",
        description: `Optional parameter for number of lines of context to include after the match. Default is ${this._defaultContextNumLinesAfter}`,
      },
      disable_ignore_files: {
        type: "boolean",
        description:
          "Optional parameter to disable ignore logic which skips hidden files, files matching .gitignore, etc.",
      },
    },
    required: ["directory_absolute_path", "query"],
    /* eslint-enable @typescript-eslint/naming-convention */
  });

  public checkToolCallSafe(_toolInput: Record<string, unknown>): boolean {
    return true;
  }

  private validateInputs(toolInput: Record<string, unknown>): Inputs | Error {
    // Validate required string parameters
    if (typeof toolInput.directory_absolute_path !== "string") {
      return Error("Parameter 'directory_absolute_path' must be a string");
    }
    if (!toolInput.directory_absolute_path.startsWith("/")) {
      return Error(
        "Parameter 'directory_absolute_path' must be an absolute path",
      );
    }
    if (typeof toolInput.query !== "string") {
      return Error("Parameter 'query' must be a string");
    }

    // Validate optional boolean parameters
    const case_sensitive = toolInput.case_sensitive ?? false;
    if (typeof case_sensitive !== "boolean") {
      return Error("Parameter 'case_sensitive' must be a boolean");
    }

    const disable_ignore_files = toolInput.disable_ignore_files ?? false;
    if (typeof disable_ignore_files !== "boolean") {
      return Error("Parameter 'disable_ignore_files' must be a boolean");
    }

    // Validate optional string parameters
    let files_include_glob_pattern: string | undefined;
    if (toolInput.files_include_glob_pattern !== undefined) {
      if (typeof toolInput.files_include_glob_pattern !== "string") {
        return Error("Parameter 'files_include_glob_pattern' must be a string");
      }
      files_include_glob_pattern = toolInput.files_include_glob_pattern;
    }

    let files_exclude_glob_pattern: string | undefined;
    if (toolInput.files_exclude_glob_pattern !== undefined) {
      if (typeof toolInput.files_exclude_glob_pattern !== "string") {
        return Error("Parameter 'files_exclude_glob_pattern' must be a string");
      }
      files_exclude_glob_pattern = toolInput.files_exclude_glob_pattern;
    }

    // Validate optional integer parameters
    const context_lines_before =
      toolInput.context_lines_before ?? this._defaultContextNumLinesBefore;
    if (
      typeof context_lines_before !== "number" ||
      !Number.isInteger(context_lines_before)
    ) {
      return Error("Parameter 'context_lines_before' must be an integer");
    }
    if (context_lines_before < 0) {
      return Error(
        "Parameter 'context_lines_before' must be a non-negative integer",
      );
    }

    const context_lines_after =
      toolInput.context_lines_after ?? this._defaultContextNumLinesAfter;
    if (
      typeof context_lines_after !== "number" ||
      !Number.isInteger(context_lines_after)
    ) {
      return Error("Parameter 'context_lines_after' must be an integer");
    }
    if (context_lines_after < 0) {
      return Error(
        "Parameter 'context_lines_after' must be a non-negative integer",
      );
    }

    return {
      directory_absolute_path: toolInput.directory_absolute_path,
      query: toolInput.query,
      case_sensitive,
      files_include_glob_pattern,
      files_exclude_glob_pattern,
      context_lines_before,
      context_lines_after,
      disable_ignore_files,
    };
  }

  public async call(
    toolInput: Record<string, unknown>,
    _chatHistory: Exchange[],
    abortSignal: AbortSignal,
  ): Promise<ToolUseResponse> {
    this._logger.debug(`Tool called with query: ${String(toolInput.query)}`);

    const validationResult = this.validateInputs(toolInput);
    if (validationResult instanceof Error) {
      return errorToolResponse(
        `Input validation failed: ${validationResult.message}`,
      );
    }
    const inputs = validationResult;

    if (!this._ripgrepPath) {
      this._ripgrepPath = await getClientWorkspaces().getRipgrepPath();
    }
    if (!this._ripgrepPath) {
      return errorToolResponse(
        "Ripgrep binary is not found. grep-search tool is not available. Do not try to call it again. Use other tools.",
      );
    }

    try {
      // Build ripgrep arguments
      const args: string[] = [
        "--json", // Output in JSON format
        "--no-config", // Don't load configuration files
      ];

      if (inputs.disable_ignore_files) {
        args.push("--no-ignore");
        args.push("--hidden");
      }

      // Add case sensitivity flag if needed
      if (!inputs.case_sensitive) {
        args.push("-i"); // Case insensitive search
      }

      // Add file include pattern if provided
      if (inputs.files_include_glob_pattern) {
        args.push("-g", inputs.files_include_glob_pattern);
      }

      // Add file exclude pattern if provided
      if (inputs.files_exclude_glob_pattern) {
        args.push("-g", `!${inputs.files_exclude_glob_pattern}`);
      }

      args.push("-n"); // Show line numbers

      args.push("--before-context", String(inputs.context_lines_before));
      args.push("--after-context", String(inputs.context_lines_after));

      args.push(inputs.query);
      args.push("."); // Search in current directory

      this._logger.debug(`Running ripgrep with args: ${args.join(" ")}`);

      // Execute ripgrep with a timeout
      const results = await this.executeRipgrep(
        inputs.directory_absolute_path,
        args,
        abortSignal,
      );

      if (results.length === 0) {
        return successToolResponse("No matches found.");
      }

      return successToolResponse(results);
    } catch (e) {
      const message = e instanceof Error ? e.message : String(e);
      return errorToolResponse(`Failed to execute grep search: ${message}`);
    }
  }

  private executeRipgrep(
    workingDir: string,
    args: string[],
    abortSignal: AbortSignal,
  ): Promise<string> {
    return new Promise((resolve, reject) => {
      // Set timeout from feature flag
      const timeoutSec =
        getClientFeatureFlags().flags.grepSearchToolTimelimitSec ?? 10;
      const timeoutMs = timeoutSec * 1000;
      let processKilled = false;
      const timeout = setTimeout(() => {
        processKilled = true;
        if (rgProcess && !rgProcess.killed) {
          rgProcess.kill();
        }
        resolve(
          output +
            `\n\n[Search timed out after ${timeoutSec} seconds. Results may be incomplete.]`,
        );
      }, timeoutMs);

      let output = "";
      let errorOutput = "";

      const rgProcess = spawn(this._ripgrepPath!, args, {
        cwd: workingDir,
      });

      // Handle process output
      rgProcess.stdout.on("data", (data) => {
        this._logger.verbose(`rgProcess.stdout.on data: ${data}`);
        const dataStr: string = (data as Buffer).toString();
        const newOutput = this.processRipgrepOutput(dataStr, workingDir);
        const outputLimit =
          getClientFeatureFlags().flags.grepSearchToolOutputCharsLimit ?? 5000;

        if (output.length + newOutput.length > outputLimit) {
          const remainingSpace = outputLimit - output.length;
          if (remainingSpace > 0) {
            output += newOutput.substring(0, remainingSpace);
          }
          output += `\n\n[Output truncated at ${outputLimit} characters limit.]`;
          // Kill the process to stop further output
          processKilled = true;
          if (rgProcess && !rgProcess.killed) {
            rgProcess.kill();
          }
          resolve(output);
        } else {
          output += newOutput;
        }
      });

      rgProcess.stderr.on("data", (data) => {
        // eslint-disable-next-line @typescript-eslint/no-unsafe-argument, @typescript-eslint/no-unsafe-call, @typescript-eslint/no-unsafe-member-access
        errorOutput += data.toString();
      });

      // Handle process completion
      rgProcess.on("close", (code) => {
        clearTimeout(timeout);
        if (!processKilled) {
          if (code === 0 || code === 1) {
            // Exit code 1 means no matches found, which is not an error
            resolve(output);
          } else {
            reject(
              new Error(
                `ripgrep process exited with code ${code}: ${errorOutput}`,
              ),
            );
          }
        }
      });

      rgProcess.on("error", (err) => {
        clearTimeout(timeout);
        reject(new Error(`Failed to start ripgrep process: ${err.message}`));
      });

      // Handle abort signal
      abortSignal.addEventListener(
        "abort",
        () => {
          clearTimeout(timeout);
          processKilled = true;
          if (rgProcess && !rgProcess.killed) {
            rgProcess.kill();
          }
          resolve(
            output + "\n\n[Search was aborted. Results may be incomplete.]",
          );
        },
        { once: true },
      );
    });
  }

  private processRipgrepOutput(jsonLines: string, workingDir: string): string {
    const lines = jsonLines.split("\n").filter((line) => line.trim());
    let formattedOutput = "";
    let lastLineNumber = -1;

    for (const line of lines) {
      try {
        const result = JSON.parse(line) as RipgrepResult;

        if (result.type === "begin") {
          // Output informative message for file start
          const absolutePath = path.resolve(workingDir, result.data.path.text);
          formattedOutput += `=== Search results start in file: ${absolutePath} ===\n`;
          lastLineNumber = -1;
        } else if (result.type === "end") {
          // Output informative message for file end
          const absolutePath = path.resolve(workingDir, result.data.path.text);
          formattedOutput += `=== Search results end in file: ${absolutePath} ===\n`;
          lastLineNumber = -1;
        } else if (result.type === "match" || result.type === "context") {
          const { lines: matchLines, line_number: lineNumber } = result.data;

          if (matchLines && lineNumber !== undefined) {
            // Add gap indicator if there's a gap in line numbers
            if (lastLineNumber !== -1 && lineNumber > lastLineNumber + 1) {
              formattedOutput += "...\n";
            }

            // Format with tab separator like cat -n: line_number\tcontent
            formattedOutput += `${lineNumber.toString().padStart(6)}\t${matchLines.text.trimEnd()}\n`;
            lastLineNumber = lineNumber;
          }
        }
      } catch (e) {
        // Skip invalid JSON lines
        this._logger.debug(`Failed to parse ripgrep output line: ${line}`);
      }
    }

    return formattedOutput;
  }
}

interface RipgrepResult {
  type: "begin" | "end" | "match" | "context";
  data: {
    path: { text: string };
    lines?: { text: string };
    line_number?: number;
    absolute_offset?: number;
    submatches?: {
      start: number;
      end: number;
      match: { text: string };
    }[];
    binary_offset?: number | null;
    stats?: {
      elapsed: { secs: number; nanos: number; human: string };
      searches: number;
      searches_with_match: number;
      bytes_searched: number;
      bytes_printed: number;
      matched_lines: number;
      matches: number;
    };
  };
}
