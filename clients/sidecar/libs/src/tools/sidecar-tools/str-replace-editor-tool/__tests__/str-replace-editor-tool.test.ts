/* eslint-disable @typescript-eslint/unbound-method */
import * as path from "path";
import { StrReplaceEditorTool } from "../str-replace-editor-tool";
import { setLibraryClientWorkspaces } from "../../../../client-interfaces/client-workspaces";
import { QualifiedPathName } from "../../../../workspace/qualified-path-name";
import { MockWorkspaceWithCheckpoints } from "../../__tests__/mocks/mock-workspace-with-checkpoints";
import { StrReplaceEditorToolDefinitionNested } from "../tool-definition-nested";
import { StrReplaceEditorToolDefinitionFlat } from "../tool-definition-flat";
import { EditResult } from "../utils";
import { attemptPathAutoCorrection } from "../../file-utils";

// Mock the feature flags module
jest.mock(
  "@augment-internal/sidecar-libs/src/client-interfaces/feature-flags",
  () => {
    // Use the actual implementation but with a mock
    const actual = jest.requireActual<
      typeof import("../../../../client-interfaces/feature-flags")
    >("../../../../client-interfaces/feature-flags");
    return {
      ...actual,
      getClientFeatureFlags: jest.fn().mockReturnValue({
        flags: {
          agentEditToolMinViewSize: 0, // Default value, will be changed in specific tests
          agentEditToolEnableFuzzyMatching: true,
          agentEditToolFuzzyMatchSuccessMessage:
            "Replacement successful. old_str and new_str were slightly modified to match the original file content.",
          agentEditToolShowResultSnippet: true, // Show snippets in tests by default
        },
      }),
    };
  },
);

// Helper to set the minViewSize feature flag for testing
function setMinViewSizeFlag(size: number) {
  // Import the mocked module
  const featureFlagsModule = jest.requireMock<{
    getClientFeatureFlags: jest.Mock;
  }>("@augment-internal/sidecar-libs/src/client-interfaces/feature-flags");

  featureFlagsModule.getClientFeatureFlags.mockReturnValue({
    flags: {
      agentEditToolMinViewSize: size,
      agentEditToolEnableFuzzyMatching: true,
      agentEditToolFuzzyMatchSuccessMessage:
        "Replacement successful. old_str and new_str were slightly modified to match the original file content.",
      agentEditToolShowResultSnippet: true, // Show snippets in tests by default
    },
  });
}

// Helper to set the enableFuzzyMatching feature flag for testing
function setEnableFuzzyMatchingFlag(enabled: boolean) {
  // Import the mocked module
  const featureFlagsModule = jest.requireMock<{
    getClientFeatureFlags: jest.Mock;
  }>("@augment-internal/sidecar-libs/src/client-interfaces/feature-flags");

  featureFlagsModule.getClientFeatureFlags.mockReturnValue({
    flags: {
      agentEditToolMinViewSize: 0,
      agentEditToolEnableFuzzyMatching: enabled,
      agentEditToolFuzzyMatchSuccessMessage:
        "Replacement successful. old_str and new_str were slightly modified to match the original file content.",
      agentEditToolShowResultSnippet: true, // Show snippets in tests by default
    },
  });
}

// Helper to set the agentEditToolShowResultSnippet feature flag for testing
function setShowResultSnippetFlag(showSnippet: boolean) {
  // Import the mocked module
  const featureFlagsModule = jest.requireMock<{
    getClientFeatureFlags: jest.Mock;
  }>("@augment-internal/sidecar-libs/src/client-interfaces/feature-flags");

  featureFlagsModule.getClientFeatureFlags.mockReturnValue({
    flags: {
      agentEditToolMinViewSize: 0,
      agentEditToolEnableFuzzyMatching: true,
      agentEditToolFuzzyMatchSuccessMessage:
        "Replacement successful. old_str and new_str were slightly modified to match the original file content.",
      agentEditToolShowResultSnippet: showSnippet,
    },
  });
}

describe("StrReplaceEditorTool", () => {
  let testWorkspace: MockWorkspaceWithCheckpoints;
  let tool: StrReplaceEditorTool;

  // Helper functions are now imported directly from utils

  beforeEach(() => {
    // Create a new instance of MockWorkspaceWithCheckpoints that acts as both
    // clientWorkspace and checkpointManager
    testWorkspace = new MockWorkspaceWithCheckpoints();
    setLibraryClientWorkspaces(testWorkspace);

    // Reset the feature flag to default value before each test
    setMinViewSizeFlag(0);

    tool = new StrReplaceEditorTool(
      testWorkspace, // Now extends AggregateCheckpointManager
      0.2, // 20% tolerance
      0, // No wait for auto-format
      new StrReplaceEditorToolDefinitionNested(),
    );
  });

  // Helper function to access private method
  const callSingleStrReplace = (
    path: string,
    content: string,
    oldStr: string,
    newStr: string,
    index: number,
    oldStrStartLineNumber?: number,
    oldStrEndLineNumber?: number,
  ): {
    isError: boolean;
    message: string;
    newContent?: string;
    genMessageFunc?: (result: EditResult) => string;
  } => {
    // Cast to unknown first to avoid TypeScript errors about private methods
    const result = (
      tool as unknown as {
        singleStrReplace: (
          path: string,
          content: string,
          oldStr: string,
          newStr: string,
          index: number,
          oldStrStartLineNumber?: number,
          oldStrEndLineNumber?: number,
        ) => EditResult;
      }
    ).singleStrReplace(
      path,
      content,
      oldStr,
      newStr,
      index,
      oldStrStartLineNumber,
      oldStrEndLineNumber,
    );

    // Convert the new EditResult format to the old format expected by tests
    return {
      isError: result.isError,
      message: result.genMessageFunc
        ? result.genMessageFunc(result)
        : "No message available",
      newContent: result.newContent,
    };
  };

  describe("constructor and basic methods", () => {
    it("should always return true for checkToolCallSafe", () => {
      const result = tool.checkToolCallSafe({});
      expect(result).toBe(true);
    });
  });

  describe("call", () => {
    it("should return error for unknown command", async () => {
      // Using string path directly in tests
      const fileContents = "line1\nline2\nline3";

      // Set the file content in our test workspace
      testWorkspace.setFileContent("test.txt", fileContents);

      // Create a custom tool with our test workspace
      const customTool = new StrReplaceEditorTool(
        testWorkspace,
        0.2, // 20% tolerance
        0, // No wait for auto-format
        new StrReplaceEditorToolDefinitionNested(),
      );

      const result = await customTool.call(
        {
          command: "unknown_command",
          path: "test.txt",
        },
        [],
        new AbortController().signal,
        "",
      );

      expect(result.text).toContain("Unknown command: unknown_command");
      expect(result.isError).toBe(true);
    });

    describe("command deduction", () => {
      describe("nested schema", () => {
        it("should deduce str_replace command when str_replace_entries is present", async () => {
          // Using string path directly in tests
          const fileContents = "line1\nline2\nline3";

          // Set the file content in our test workspace
          testWorkspace.setFileContent("test.txt", fileContents);

          const result = await tool.call(
            {
              // command is intentionally omitted
              path: "test.txt",
              str_replace_entries: [
                {
                  old_str: "line2",
                  new_str: "replaced line",
                },
              ],
            },
            [],
            new AbortController().signal,
            "",
          );

          expect(result.isError).toBe(false);
          expect(result.text).toContain(
            "Successfully edited the file test.txt",
          );
          expect(testWorkspace.getFileContent("test.txt")).toEqual(
            "line1\nreplaced line\nline3",
          );
        });

        it("should deduce insert command when insert_line_entries is present", async () => {
          // Using string path directly in tests
          const fileContents = "line1\nline2\nline3";

          // Set the file content in our test workspace
          testWorkspace.setFileContent("test.txt", fileContents);

          const result = await tool.call(
            {
              // command is intentionally omitted
              path: "test.txt",
              insert_line_entries: [
                {
                  insert_line: 1,
                  new_str: "inserted line",
                },
              ],
            },
            [],
            new AbortController().signal,
            "",
          );

          expect(result.isError).toBe(false);
          expect(result.text).toContain(
            "Successfully edited the file test.txt",
          );
          expect(testWorkspace.getFileContent("test.txt")).toEqual(
            "line1\ninserted line\nline2\nline3",
          );
        });
      });

      describe("flat schema", () => {
        let flatTool: StrReplaceEditorTool;

        beforeEach(() => {
          // Create a tool with the flat schema definition
          flatTool = new StrReplaceEditorTool(
            testWorkspace,
            0.2, // 20% tolerance
            0, // No wait for auto-format
            new StrReplaceEditorToolDefinitionFlat(),
          );
        });

        it("should deduce str_replace command when old_str and new_str are present", async () => {
          // Using string path directly in tests
          const fileContents = "line1\nline2\nline3";

          // Set the file content in our test workspace
          testWorkspace.setFileContent("test.txt", fileContents);

          const result = await flatTool.call(
            {
              // command is intentionally omitted
              path: "test.txt",
              old_str: "line2",
              new_str: "replaced line",
              old_str_start_line_number: 2,
              old_str_end_line_number: 2,
            },
            [],
            new AbortController().signal,
            "",
          );

          expect(result.isError).toBe(false);
          expect(result.text).toContain(
            "Successfully edited the file test.txt",
          );
          expect(testWorkspace.getFileContent("test.txt")).toEqual(
            "line1\nreplaced line\nline3",
          );
        });

        it("should deduce str_replace command when old_str_1 and new_str_1 are present", async () => {
          // Using string path directly in tests
          const fileContents = "line1\nline2\nline3";

          // Set the file content in our test workspace
          testWorkspace.setFileContent("test.txt", fileContents);

          const result = await flatTool.call(
            {
              // command is intentionally omitted
              path: "test.txt",
              old_str_1: "line2",
              new_str_1: "replaced line",
              old_str_start_line_number_1: 2,
              old_str_end_line_number_1: 2,
            },
            [],
            new AbortController().signal,
            "",
          );

          expect(result.isError).toBe(false);
          expect(result.text).toContain(
            "Successfully edited the file test.txt",
          );
          expect(testWorkspace.getFileContent("test.txt")).toEqual(
            "line1\nreplaced line\nline3",
          );
        });

        it("should deduce insert command when insert_line and new_str are present", async () => {
          // Using string path directly in tests
          const fileContents = "line1\nline2\nline3";

          // Set the file content in our test workspace
          testWorkspace.setFileContent("test.txt", fileContents);

          const result = await flatTool.call(
            {
              // command is intentionally omitted
              path: "test.txt",
              insert_line: 1,
              new_str: "inserted line",
            },
            [],
            new AbortController().signal,
            "",
          );

          expect(result.isError).toBe(false);
          expect(result.text).toContain(
            "Successfully edited the file test.txt",
          );
          expect(testWorkspace.getFileContent("test.txt")).toEqual(
            "line1\ninserted line\nline2\nline3",
          );
        });

        it("should deduce insert command when insert_line_1 and new_str_1 are present", async () => {
          // Using string path directly in tests
          const fileContents = "line1\nline2\nline3";

          // Set the file content in our test workspace
          testWorkspace.setFileContent("test.txt", fileContents);

          const result = await flatTool.call(
            {
              // command is intentionally omitted
              path: "test.txt",
              insert_line_1: 1,
              new_str_1: "inserted line",
            },
            [],
            new AbortController().signal,
            "",
          );

          expect(result.isError).toBe(false);
          expect(result.text).toContain(
            "Successfully edited the file test.txt",
          );
          expect(testWorkspace.getFileContent("test.txt")).toEqual(
            "line1\ninserted line\nline2\nline3",
          );
        });
      });

      it("should deduce view command when view_range is present", async () => {
        // Using string path directly in tests
        const fileContents = "line1\nline2\nline3\nline4\nline5";

        // Set the file content in our test workspace
        testWorkspace.setFileContent("test.txt", fileContents);

        const result = await tool.call(
          {
            // command is intentionally omitted
            path: "test.txt",
            view_range: [2, 4],
          },
          [],
          new AbortController().signal,
          "",
        );

        expect(result.isError).toBe(false);
        expect(result.text).toContain("line2");
        expect(result.text).toContain("line3");
        expect(result.text).toContain("line4");
        expect(result.text).toContain("Total lines in file: 5");
      });
    });
  });

  describe("handleView", () => {
    it("should include total lines in the view output", async () => {
      // Set a large minViewSize via feature flag
      const minViewSize = 500;
      setMinViewSizeFlag(minViewSize);

      // Using string path directly in tests
      const fileContents = "line1\nline2\nline3";

      // Set the file content in our test workspace
      testWorkspace.setFileContent("test.txt", fileContents);

      const result = await tool.call(
        { command: "view", path: "test.txt" },
        [],
        new AbortController().signal,
        "",
      );

      const expectedOutput =
        "Here's the result of running `cat -n` on test.txt:\n" +
        "     1\tline1\n" +
        "     2\tline2\n" +
        "     3\tline3\n" +
        "Total lines in file: 3\n";

      expect(result.text).toBe(expectedOutput);
    });

    it("should handle windows line endings correctly", async () => {
      // Set a large minViewSize via feature flag
      const minViewSize = 500;
      setMinViewSizeFlag(minViewSize);

      // Using QualifiedPathName for path construction but only using the string in tests
      const fileContents = "line1\r\nline2\r\nline3";

      // Set the file content in our test workspace
      testWorkspace.setFileContent("test.txt", fileContents);

      const result = await tool.call(
        { command: "view", path: "test.txt" },
        [],
        new AbortController().signal,
        "",
      );

      const expectedOutput =
        "Here's the result of running `cat -n` on test.txt:\n" +
        "     1\tline1\n" +
        "     2\tline2\n" +
        "     3\tline3\n" +
        "Total lines in file: 3\n";

      expect(result.text).toBe(expectedOutput);
    });

    it("should handle trailing new line correctly", async () => {
      // Set a large minViewSize via feature flag
      const minViewSize = 500;
      setMinViewSizeFlag(minViewSize);

      // Using string path directly in tests
      const fileContents = "line1\nline2\nline3\n";

      // Set the file content in our test workspace
      testWorkspace.setFileContent("test.txt", fileContents);

      const result = await tool.call(
        { command: "view", path: "test.txt" },
        [],
        new AbortController().signal,
        "",
      );

      const expectedOutput =
        "Here's the result of running `cat -n` on test.txt:\n" +
        "     1\tline1\n" +
        "     2\tline2\n" +
        "     3\tline3\n" +
        "     4\t\n" +
        "Total lines in file: 4\n";

      expect(result.text).toBe(expectedOutput);
    });

    it("should handle single line correctly", async () => {
      // Set a large minViewSize via feature flag
      const minViewSize = 500;
      setMinViewSizeFlag(minViewSize);

      // Using string path directly in tests
      const fileContents = "line1";

      // Set the file content in our test workspace
      testWorkspace.setFileContent("test.txt", fileContents);

      const result = await tool.call(
        { command: "view", path: "test.txt" },
        [],
        new AbortController().signal,
        "",
      );

      const expectedOutput =
        "Here's the result of running `cat -n` on test.txt:\n" +
        "     1\tline1\n" +
        "Total lines in file: 1\n";

      expect(result.text).toBe(expectedOutput);
    });

    it("should handle view_range parameter correctly", async () => {
      // Set a small minViewSize via feature flag to avoid expansion
      const minViewSize = 3; // Just enough for our test range
      setMinViewSizeFlag(minViewSize);

      // Using string path directly in tests
      const fileContents = "line1\nline2\nline3\nline4\nline5";

      // Set the file content in our test workspace
      testWorkspace.setFileContent("test.txt", fileContents);

      const result = await tool.call(
        { command: "view", path: "test.txt", view_range: [2, 4] },
        [],
        new AbortController().signal,
        "",
      );

      const expectedOutput =
        "Here's the result of running `cat -n` on test.txt:\n" +
        "     2\tline2\n" +
        "     3\tline3\n" +
        "     4\tline4\n" +
        "Total lines in file: 5\n";

      expect(result.text).toBe(expectedOutput);
    });

    it("should expand view_range to meet minimum view size", async () => {
      // Set a large minViewSize via feature flag
      const minViewSize = 500;
      setMinViewSizeFlag(minViewSize);

      // Create a file with 1000 lines to test the expansion logic
      const fileLines = Array.from({ length: 1000 }, (_, i) => `line${i + 1}`);
      const fileContents = fileLines.join("\n");

      // Set the file content in our test workspace
      testWorkspace.setFileContent("test.txt", fileContents);

      // Request a small range (only 3 lines)
      const result = await tool.call(
        { command: "view", path: "test.txt", view_range: [500, 502] },
        [],
        new AbortController().signal,
        "",
      );

      // The result should contain at least minViewSize lines
      const resultLines = result.text.split("\n");

      // Count the actual content lines (excluding header and footer)
      const contentLines = resultLines.filter((line) =>
        /^\s+\d+\s+/.test(line),
      );

      // Verify we have at least minViewSize lines
      expect(contentLines.length).toBeGreaterThanOrEqual(minViewSize);

      // Verify the range includes our requested lines
      expect(result.text).toContain("line500");
      expect(result.text).toContain("line501");
      expect(result.text).toContain("line502");

      // Verify total lines is correct
      expect(result.text).toContain("Total lines in file: 1000");
    });

    it("should expand view_range roughly equally on both sides", async () => {
      // Set a large minViewSize via feature flag
      const minViewSize = 500;
      setMinViewSizeFlag(minViewSize);

      // Create a file with 1000 lines
      const fileLines = Array.from({ length: 1000 }, (_, i) => `line${i + 1}`);
      const fileContents = fileLines.join("\n");

      // Set the file content in our test workspace
      testWorkspace.setFileContent("test.txt", fileContents);

      // Request a small range in the middle of the file
      const result = await tool.call(
        { command: "view", path: "test.txt", view_range: [400, 402] },
        [],
        new AbortController().signal,
        "",
      );

      // Verify the range includes our requested lines
      expect(result.text).toContain("line400");
      expect(result.text).toContain("line401");
      expect(result.text).toContain("line402");

      // Verify total lines is correct
      expect(result.text).toContain("Total lines in file: 1000");
    });

    it("should expand view_range to beginning of file when close to start", async () => {
      // Set a large minViewSize via feature flag
      const minViewSize = 500;
      setMinViewSizeFlag(minViewSize);

      // Create a file with 1000 lines
      const fileLines = Array.from({ length: 1000 }, (_, i) => `line${i + 1}`);
      const fileContents = fileLines.join("\n");

      // Set the file content in our test workspace
      testWorkspace.setFileContent("test.txt", fileContents);

      // Request a small range near the beginning of the file
      const result = await tool.call(
        { command: "view", path: "test.txt", view_range: [2, 4] },
        [],
        new AbortController().signal,
        "",
      );

      // The expansion should include the beginning of the file
      expect(result.text).toContain("line1");
      expect(result.text).toContain("line2");
      expect(result.text).toContain("line3");
      expect(result.text).toContain("line4");

      // Count the actual content lines
      const contentLines = result.text
        .split("\n")
        .filter((line) => /^\s+\d+\t/.test(line));
      expect(contentLines.length).toBeGreaterThanOrEqual(minViewSize);
    });

    it("should expand view_range to end of file when close to end", async () => {
      // Set a large minViewSize via feature flag
      const minViewSize = 500;
      setMinViewSizeFlag(minViewSize);

      // Create a file with 1000 lines
      const fileLines = Array.from({ length: 1000 }, (_, i) => `line${i + 1}`);
      const fileContents = fileLines.join("\n");

      // Set the file content in our test workspace
      testWorkspace.setFileContent("test.txt", fileContents);

      // Request a small range near the end of the file
      const result = await tool.call(
        { command: "view", path: "test.txt", view_range: [997, 999] },
        [],
        new AbortController().signal,
        "",
      );

      // The expansion should include the end of the file
      expect(result.text).toContain("line997");
      expect(result.text).toContain("line998");
      expect(result.text).toContain("line999");
      expect(result.text).toContain("line1000");
    });

    it("should respect custom minViewSize when expanding view_range", async () => {
      // Set a custom minViewSize via feature flag
      const minViewSize = 100;
      setMinViewSizeFlag(minViewSize);

      // Create a file with 1000 lines
      const fileLines = Array.from({ length: 1000 }, (_, i) => `line${i + 1}`);
      const fileContents = fileLines.join("\n");

      // Set the file content in our test workspace
      testWorkspace.setFileContent("test.txt", fileContents);

      // Request a small range in the middle of the file
      const result = await tool.call(
        { command: "view", path: "test.txt", view_range: [500, 502] },
        [],
        new AbortController().signal,
        "",
      );

      // Count the actual content lines
      const contentLines = result.text
        .split("\n")
        .filter((line) => /^\s+\d+\t/.test(line));

      // Should be at least the custom minViewSize but less than the default 500
      expect(contentLines.length).toBeGreaterThanOrEqual(minViewSize);
      expect(contentLines.length).toBeLessThan(500);

      // Verify the range includes our requested lines
      expect(result.text).toContain("line500");
      expect(result.text).toContain("line501");
      expect(result.text).toContain("line502");
    });
  });

  describe("str_replace command", () => {
    it("should respect the enableFuzzyMatching feature flag", async () => {
      // Mock the fuzzyMatchReplacementStrings function to track calls
      const mockFuzzyMatch = jest
        .spyOn(
          // Import from the module
          // eslint-disable-next-line @typescript-eslint/no-var-requires
          require("../match-utils"),
          "fuzzyMatchReplacementStrings",
        )
        .mockImplementation(() => {
          return { oldStr: "modified old", newStr: "modified new" };
        });

      // Use try/finally to ensure cleanup runs even if test fails
      try {
        // Disable fuzzy matching
        setEnableFuzzyMatchingFlag(false);

        // Using string path directly in tests
        const fileContents =
          "This is a test-content that doesn't match exactly";

        // Set the file content in our test workspace
        testWorkspace.setFileContent("test.txt", fileContents);

        // Call with a string that doesn't match exactly
        const result = await tool.call(
          {
            command: "str_replace",
            path: "test.txt",
            str_replace_entries: [
              {
                old_str: "test content",
                new_str: "new content",
                old_str_start_line_number: 1,
                old_str_end_line_number: 1,
              },
            ],
          },
          [],
          new AbortController().signal,
          "",
        );

        // Should fail because fuzzy matching is disabled
        expect(result.isError).toBe(true);
        expect(result.text).toContain("No replacement was performed");

        // The fuzzy match function should not have been called
        expect(mockFuzzyMatch).not.toHaveBeenCalled();

        // Now enable fuzzy matching
        setEnableFuzzyMatchingFlag(true);

        // Try again with fuzzy matching enabled
        await tool.call(
          {
            command: "str_replace",
            path: "test.txt",
            str_replace_entries: [
              {
                old_str: "test content",
                new_str: "new content",
                old_str_start_line_number: 1,
                old_str_end_line_number: 1,
              },
            ],
          },
          [],
          new AbortController().signal,
          "",
        );

        // The fuzzy match function should have been called
        expect(mockFuzzyMatch).toHaveBeenCalled();
      } finally {
        // Clean up - this will run even if the test fails
        mockFuzzyMatch.mockRestore();
      }
    });

    it("should allow empty old_str when file content is empty", async () => {
      // Using string path directly in tests
      const fileContents = "";

      // Set the file content in our test workspace
      testWorkspace.setFileContent("test.txt", fileContents);

      const result = await tool.call(
        {
          command: "str_replace",
          path: "test.txt",
          str_replace_entries: [
            {
              old_str: "",
              new_str: "new content",
            },
          ],
        },
        [],
        new AbortController().signal,
        "",
      );

      expect(result.text).toContain("Successfully edited the file test.txt");
      expect(testWorkspace.addCheckpoint).toHaveBeenCalled();

      // Verify the file content was updated in our test workspace
      expect(testWorkspace.getFileContent("test.txt")).toEqual("new content");

      // Get the actual modified code from the mock call
      const callArgs = testWorkspace.addCheckpoint.mock.calls[0][1];

      // Verify the document was created with the correct content
      // Using string path directly in tests, so we check for the string path
      expect(callArgs.document.originalCode).toEqual(fileContents);
      expect(callArgs.document.modifiedCode).toEqual("new content");
      expect(callArgs.timestamp).toBeGreaterThan(0);
    });

    it("should match content with different indentation levels", async () => {
      const _filepath = new QualifiedPathName("/root/path", "test.txt");
      const fileContents = `function test() {
  if (condition) {
    console.log("test");
  }
}`;

      // Set the file content in our test workspace
      testWorkspace.setFileContent("test.txt", fileContents);

      // The oldStr has different indentation than the file content
      const result = await tool.call(
        {
          command: "str_replace",
          path: "test.txt",
          str_replace_entries: [
            {
              old_str: `    console.log("test");`,
              new_str: `    console.log("modified test");`,
            },
          ],
        },
        [],
        new AbortController().signal,
        "",
      );

      expect(result.text).toContain("Successfully edited the file test.txt");
      expect(testWorkspace.addCheckpoint).toHaveBeenCalled();

      // Verify the file content was updated in our test workspace
      const expectedModifiedContent = `function test() {
  if (condition) {
    console.log("modified test");
  }
}`;
      expect(testWorkspace.getFileContent("test.txt")).toEqual(
        expectedModifiedContent,
      );

      // Verify the document was created with the correct content
      const callArgs = testWorkspace.addCheckpoint.mock.calls[0][1];
      expect(callArgs.document.filePath).toEqual(_filepath);
      expect(callArgs.document.originalCode).toEqual(fileContents);
      expect(callArgs.document.modifiedCode).toEqual(expectedModifiedContent);
      expect(callArgs.timestamp).toBeGreaterThan(0);

      // Verify the file content was updated in our test workspace
      const updatedContent = testWorkspace.getFileContent("test.txt");
      expect(updatedContent).toContain('console.log("modified test");');
      expect(updatedContent).not.toContain('console.log("test");');
    });

    it("should return error when entry has empty old_str and file content is not empty", async () => {
      // Using string path directly in tests
      const fileContents = "existing content";

      // Set the file content in our test workspace
      testWorkspace.setFileContent("test.txt", fileContents);

      const result = await tool.call(
        {
          command: "str_replace",
          path: "test.txt",
          str_replace_entries: [
            {
              old_str: "",
              new_str: "new content",
            },
          ],
        },
        [],
        new AbortController().signal,
        "",
      );

      expect(result.text).toContain(
        "old_str is empty which is only allowed when the file is empty or contains only whitespace",
      );
      expect(testWorkspace.addCheckpoint).not.toHaveBeenCalled();
      expect(result.isError).toBe(true);
    });

    it("should not return error when oldStr and newStr are identical", async () => {
      // Using string path directly in tests
      const fileContents = "line1\nline2\nline3";
      const sameContent = "line2";

      // Set the file content in our test workspace
      testWorkspace.setFileContent("test.txt", fileContents);

      const result = await tool.call(
        {
          command: "str_replace",
          path: "test.txt",
          str_replace_entries: [
            {
              old_str: sameContent,
              new_str: sameContent,
            },
          ],
        },
        [],
        new AbortController().signal,
        "",
      );

      expect(result.isError).toBe(false);
      expect(result.text).toContain("Successfully edited the file test.txt");
      // No checkpoint should be added since there was no actual change
      expect(testWorkspace.addCheckpoint).not.toHaveBeenCalled();
    });

    it("should return error with line numbers when oldStr appears multiple times", async () => {
      // Using string path directly in tests
      const fileContents = "repeated line\nsome other content\nrepeated line";
      const repeatedContent = "repeated line";

      // Set the file content in our test workspace
      testWorkspace.setFileContent("test.txt", fileContents);

      const result = await tool.call(
        {
          command: "str_replace",
          path: "test.txt",
          str_replace_entries: [
            {
              old_str: repeatedContent,
              new_str: "new content",
            },
          ],
        },
        [],
        new AbortController().signal,
        "",
      );

      expect(result.text).toContain("Multiple occurrences of oldStr");
      expect(result.text).toContain(
        "Please provide line numbers to disambiguate",
      );
      expect(testWorkspace.addCheckpoint).not.toHaveBeenCalled();
      expect(result.isError).toBe(true);
    });

    it("should replace matching content and verify the result", async () => {
      const _filepath = new QualifiedPathName("/root/path", "test.txt");
      const fileContents = "line1\nline2\nline3";

      // Set the file content in our test workspace
      testWorkspace.setFileContent("test.txt", fileContents);

      const result = await tool.call(
        {
          command: "str_replace",
          path: "test.txt",
          str_replace_entries: [
            {
              old_str: "line2",
              new_str: "replaced line",
            },
          ],
        },
        [],
        new AbortController().signal,
        "",
      );

      expect(result.text).toContain("Successfully edited the file test.txt");
      expect(testWorkspace.addCheckpoint).toHaveBeenCalled();

      // Verify the document was created with the correct content
      expect(testWorkspace.addCheckpoint).toHaveBeenCalledWith(
        expect.objectContaining({
          path: _filepath,
        }),
        expect.objectContaining({
          // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
          document: expect.objectContaining({
            filePath: _filepath,
            _originalCode: fileContents,
            _modifiedCode: "line1\nreplaced line\nline3",
          }),
        }),
      );
    });

    it("should replace multi-line content and verify the result", async () => {
      const _filepath = new QualifiedPathName("/root/path", "test.txt");
      const fileContents = "line1\nline2\nline3\nline4";

      // Set the file content in our test workspace
      testWorkspace.setFileContent("test.txt", fileContents);

      const result = await tool.call(
        {
          command: "str_replace",
          path: "test.txt",
          str_replace_entries: [
            {
              old_str: "line2\nline3",
              new_str: "new line\nanother new line",
            },
          ],
        },
        [],
        new AbortController().signal,
        "",
      );

      expect(result.text).toContain("Successfully edited the file test.txt");
      expect(testWorkspace.addCheckpoint).toHaveBeenCalled();

      // Verify the document was created with the correct content
      const callArgs = testWorkspace.addCheckpoint.mock.calls[0][1];
      expect(callArgs.document.filePath).toEqual(_filepath);
      expect(callArgs.document.originalCode).toEqual(fileContents);
      expect(callArgs.document.modifiedCode).toEqual(
        "line1\nnew line\nanother new line\nline4",
      );
      expect(callArgs.timestamp).toBeGreaterThan(0);
    });

    it("should handle multiple occurrences of mid-line matches with line numbers", async () => {
      const filepath = new QualifiedPathName("/root/path", "test.txt");
      const fileContents =
        "function test() {\n" +
        "  if (condition) {\n" +
        '    console.log("first");\n' +
        "  }\n" +
        "}\n" +
        "function another() {\n" +
        "  if (condition) {\n" +
        '    console.log("second");\n' +
        "  }\n" +
        "}";

      // Set the file content in our test workspace
      testWorkspace.setFileContent("test.txt", fileContents);

      // This pattern "if (condition) {\n    console" appears twice in the file
      // We'll use line numbers to target the second occurrence
      const result = await tool.call(
        {
          command: "str_replace",
          path: "test.txt",
          str_replace_entries: [
            {
              old_str: "if (condition) {\n    console",
              old_str_start_line_number: 7, // Target the second occurrence
              old_str_end_line_number: 8,
              new_str: "if (newCondition) {\n    logger",
            },
          ],
        },
        [],
        new AbortController().signal,
        "",
      );

      expect(result.text).toContain("Successfully edited the file test.txt");
      expect(testWorkspace.addCheckpoint).toHaveBeenCalled();

      // Verify the document was created with the correct content
      // Only the second occurrence should be modified
      const expectedModifiedCode =
        "function test() {\n" +
        "  if (condition) {\n" +
        '    console.log("first");\n' +
        "  }\n" +
        "}\n" +
        "function another() {\n" +
        "  if (newCondition) {\n" +
        '    logger.log("second");\n' +
        "  }\n" +
        "}";

      const callArgs = testWorkspace.addCheckpoint.mock.calls[0][1];
      expect(callArgs.document.filePath).toEqual(filepath);
      expect(callArgs.document.originalCode).toEqual(fileContents);
      expect(callArgs.document.modifiedCode).toEqual(expectedModifiedCode);
      expect(callArgs.timestamp).toBeGreaterThan(0);
    });

    it("should replace multiple lines with mid-line replacements that start and end mid-line", async () => {
      const filepath = new QualifiedPathName("/root/path", "test.txt");
      const fileContents =
        "class Example {\n" +
        "  constructor() {\n" +
        "    this.value = 10;\n" +
        "    this.name = 'example';\n" +
        "  }\n" +
        "\n" +
        "  getValue() {\n" +
        "    return this.value;\n" +
        "  }\n" +
        "}";

      // Set the file content in our test workspace
      testWorkspace.setFileContent("test.txt", fileContents);

      // Replace a section that starts in the middle of one line and ends in the middle of another
      const result = await tool.call(
        {
          command: "str_replace",
          path: "test.txt",
          str_replace_entries: [
            {
              old_str: "value = 10;\n    this.name = 'example';\n  }\n\n  get",
              new_str: "count = 0;\n    this.id = 'test';\n  }\n\n  get",
            },
          ],
        },
        [],
        new AbortController().signal,
        "",
      );

      expect(result.text).toContain("Successfully edited the file test.txt");
      expect(testWorkspace.addCheckpoint).toHaveBeenCalled();

      // Verify the document was created with the correct content
      const expectedModifiedCode =
        "class Example {\n" +
        "  constructor() {\n" +
        "    this.count = 0;\n" +
        "    this.id = 'test';\n" +
        "  }\n" +
        "\n" +
        "  getValue() {\n" +
        "    return this.value;\n" +
        "  }\n" +
        "}";

      const callArgs = testWorkspace.addCheckpoint.mock.calls[0][1];
      expect(callArgs.document.filePath).toEqual(filepath);
      expect(callArgs.document.originalCode).toEqual(fileContents);
      expect(callArgs.document.modifiedCode).toEqual(expectedModifiedCode);
      expect(callArgs.timestamp).toBeGreaterThan(0);
    });
  });

  describe("str_replace with multiple entries", () => {
    it("should return error with zero tolerance when line numbers don't match exactly", async () => {
      // Create a tool with zero tolerance
      const zeroToleranceTool = new StrReplaceEditorTool(
        testWorkspace,
        0, // Zero tolerance - must match exactly
        1000,
        new StrReplaceEditorToolDefinitionNested(),
      );

      // Using string path directly in tests
      const fileContents =
        "repeated line\nsome content\nrepeated line\nmore content";

      // Set the file content in our test workspace
      testWorkspace.setFileContent("test.txt", fileContents);

      const result = await zeroToleranceTool.call(
        {
          command: "str_replace",
          path: "test.txt",
          str_replace_entries: [
            {
              old_str: "repeated line",
              old_str_start_line_number: 2, // Incorrect line number (actual is 1)
              old_str_end_line_number: 2,
              new_str: "first replacement",
            },
          ],
        },
        [],
        new AbortController().signal,
        "",
      );

      // The test shows that with zero tolerance, the tool returns an error
      // when line numbers don't match exactly
      expect(result.isError).toBe(true);
      // Check that the error message is appropriate
      expect(result.text).toContain("Failed to edit the file test.txt");
      expect(testWorkspace.addCheckpoint).not.toHaveBeenCalled();
    });

    it("should succeed with zero tolerance when line numbers match exactly", async () => {
      // Create a tool with zero tolerance
      const zeroToleranceTool = new StrReplaceEditorTool(
        testWorkspace,
        0, // Zero tolerance - must match exactly
        1000,
        new StrReplaceEditorToolDefinitionNested(),
      );

      const filepath = new QualifiedPathName("/root/path", "test.txt");
      const fileContents =
        "repeated line\nsome content\nrepeated line\nmore content";

      // Set the file content in our test workspace
      testWorkspace.setFileContent("test.txt", fileContents);

      const result = await zeroToleranceTool.call(
        {
          command: "str_replace",
          path: "test.txt",
          str_replace_entries: [
            {
              old_str: "repeated line",
              old_str_start_line_number: 1, // Exact match for first occurrence
              old_str_end_line_number: 1,
              new_str: "first replacement",
            },
          ],
        },
        [],
        new AbortController().signal,
        "",
      );

      expect(result.isError).toBe(false);
      expect(result.text).toContain("Successfully edited the file test.txt");
      expect(testWorkspace.addCheckpoint).toHaveBeenCalled();

      // Verify the document was created with the correct content
      const callArgs = testWorkspace.addCheckpoint.mock.calls[0][1];
      expect(callArgs.document.filePath).toEqual(filepath);
      expect(callArgs.document.originalCode).toEqual(fileContents);
      expect(callArgs.document.modifiedCode).toEqual(
        "first replacement\nsome content\nrepeated line\nmore content",
      );
      expect(callArgs.timestamp).toBeGreaterThan(0);
    });

    it("should handle multiple replacements in a file", async () => {
      const filepath = new QualifiedPathName("/root/path", "test.txt");
      const fileContents = "line1\nline2\nline3\nline4\nline5";

      // Set the file content in our test workspace
      testWorkspace.setFileContent("test.txt", fileContents);

      const result = await tool.call(
        {
          command: "str_replace",
          path: "test.txt",
          str_replace_entries: [
            {
              old_str: "line2",
              new_str: "replaced line 2",
            },
            {
              old_str: "line4",
              new_str: "replaced line 4",
            },
          ],
        },
        [],
        new AbortController().signal,
        "",
      );

      expect(result.text).toContain("Successfully edited the file test.txt");
      expect(testWorkspace.addCheckpoint).toHaveBeenCalled();

      // Verify the document was created with the correct content
      const callArgs = testWorkspace.addCheckpoint.mock.calls[0][1];
      expect(callArgs.document.filePath).toEqual(filepath);
      expect(callArgs.document.originalCode).toEqual(fileContents);
      expect(callArgs.document.modifiedCode).toEqual(
        "line1\nreplaced line 2\nline3\nreplaced line 4\nline5",
      );
      expect(callArgs.timestamp).toBeGreaterThan(0);
    });

    it("should handle multiple replacements with line numbers for disambiguation", async () => {
      const filepath = new QualifiedPathName("/root/path", "test.txt");
      const fileContents =
        "repeated line\nsome content\nrepeated line\nmore content";

      // Set the file content in our test workspace
      testWorkspace.setFileContent("test.txt", fileContents);

      const result = await tool.call(
        {
          command: "str_replace",
          path: "test.txt",
          str_replace_entries: [
            {
              old_str: "repeated line",
              old_str_start_line_number: 1,
              old_str_end_line_number: 1,
              new_str: "first replacement",
            },
            {
              old_str: "repeated line",
              old_str_start_line_number: 3,
              old_str_end_line_number: 3,
              new_str: "second replacement",
            },
          ],
        },
        [],
        new AbortController().signal,
        "",
      );

      expect(result.text).toContain("Successfully edited the file test.txt");
      expect(testWorkspace.addCheckpoint).toHaveBeenCalled();

      // Verify the document was created with the correct content
      const callArgs = testWorkspace.addCheckpoint.mock.calls[0][1];
      expect(callArgs.document.filePath).toEqual(filepath);
      expect(callArgs.document.originalCode).toEqual(fileContents);
      expect(callArgs.document.modifiedCode).toEqual(
        "first replacement\nsome content\nsecond replacement\nmore content",
      );
      expect(callArgs.timestamp).toBeGreaterThan(0);
    });

    it("should return error if str_replace_entries is empty", async () => {
      // Using string path directly in tests
      const fileContents = "line1\nline2\nline3";

      // Set the file content in our test workspace
      testWorkspace.setFileContent("test.txt", fileContents);

      const result = await tool.call(
        {
          command: "str_replace",
          path: "test.txt",
          str_replace_entries: [],
        },
        [],
        new AbortController().signal,
        "",
      );

      expect(result.text).toContain(
        "Empty required parameter `str_replace_entries` for `str_replace` command.",
      );
      expect(testWorkspace.addCheckpoint).not.toHaveBeenCalled();
      expect(result.isError).toBe(true);
    });

    it("should return error if str_replace_entries is undefined", async () => {
      // Using string path directly in tests
      const fileContents = "line1\nline2\nline3";

      // Set the file content in our test workspace
      testWorkspace.setFileContent("test.txt", fileContents);

      const result = await tool.call(
        {
          command: "str_replace",
          path: "test.txt",
          // str_replace_entries is intentionally omitted
        },
        [],
        new AbortController().signal,
        "",
      );

      expect(result.text).toContain(
        "Missing required parameter `str_replace_entries` for `str_replace` command.",
      );
      expect(testWorkspace.addCheckpoint).not.toHaveBeenCalled();
      expect(result.isError).toBe(true);
    });

    it("should return error if any entry is missing required properties", async () => {
      // Using string path directly in tests
      const fileContents = "line1\nline2\nline3";

      // Set the file content in our test workspace
      testWorkspace.setFileContent("test.txt", fileContents);

      const result = await tool.call(
        {
          command: "str_replace",
          path: "test.txt",
          str_replace_entries: [
            {
              old_str: "line2",
              // missing new_str
            },
          ],
        },
        [],
        new AbortController().signal,
        "",
      );

      expect(result.text).toContain(
        "Missing required parameter `new_str` for `str_replace` command.",
      );
      expect(testWorkspace.addCheckpoint).not.toHaveBeenCalled();
      expect(result.isError).toBe(true);
    });

    it("should return error if str_replace_entries has wrong format", async () => {
      // Using string path directly in tests
      const fileContents = "line1\nline2\nline3";

      // Set the file content in our test workspace
      testWorkspace.setFileContent("test.txt", fileContents);

      const result = await tool.call(
        {
          command: "str_replace",
          path: "test.txt",
          str_replace_entries: [
            {
              old_str: "line2",
              random_attr: "random value",
            },
          ],
        },
        [],
        new AbortController().signal,
        "",
      );

      expect(result.text).toContain(
        "Missing required parameter `new_str` for `str_replace` command.",
      );
      expect(testWorkspace.addCheckpoint).not.toHaveBeenCalled();
      expect(result.isError).toBe(true);
    });

    it("should return error if str_replace_entries is an empty object", async () => {
      // Using string path directly in tests
      const fileContents = "line1\nline2\nline3";

      // Set the file content in our test workspace
      testWorkspace.setFileContent("test.txt", fileContents);

      // Test with str_replace_entries as an object instead of an array
      const result = await tool.call(
        {
          command: "str_replace",
          path: "test.txt",
          str_replace_entries: {},
        },
        [],
        new AbortController().signal,
        "",
      );

      expect(result.text).toContain(
        "Invalid parameter `str_replace_entries` for `str_replace` command. It must be an array of objects.",
      );
      expect(testWorkspace.addCheckpoint).not.toHaveBeenCalled();
      expect(result.isError).toBe(true);
    });

    it("should allow empty old_str when file contains only whitespace", async () => {
      const filepath = new QualifiedPathName("/root/path", "test.txt");
      const fileContents = "   \n  \t  \n   "; // File with only whitespace

      // Set the file content in our test workspace
      testWorkspace.setFileContent("test.txt", fileContents);

      const result = await tool.call(
        {
          command: "str_replace",
          path: "test.txt",
          str_replace_entries: [
            {
              old_str: "",
              new_str: "new content",
            },
          ],
        },
        [],
        new AbortController().signal,
        "",
      );

      expect(result.text).toContain("Successfully edited the file test.txt");
      expect(testWorkspace.addCheckpoint).toHaveBeenCalled();

      // Get the actual modified code from the mock call
      const callArgs = testWorkspace.addCheckpoint.mock.calls[0][1];

      // Verify the document was created with the correct content
      expect(callArgs.document.filePath).toEqual(filepath);
      // Don't check exact originalCode because whitespace normalization can vary
      expect(callArgs.document.modifiedCode).toEqual("new content");
    });

    it("should allow empty new_str to replace content with an empty line", async () => {
      const filepath = new QualifiedPathName("/root/path", "test.txt");
      const fileContents = "line1\nline2\nline3";

      // Set the file content in our test workspace
      testWorkspace.setFileContent("test.txt", fileContents);

      const result = await tool.call(
        {
          command: "str_replace",
          path: "test.txt",
          str_replace_entries: [
            {
              old_str: "line2",
              new_str: "",
            },
          ],
        },
        [],
        new AbortController().signal,
        "",
      );

      expect(result.text).toContain("Successfully edited the file test.txt");
      expect(testWorkspace.addCheckpoint).toHaveBeenCalled();

      // Get the actual modified code from the mock call
      const callArgs = testWorkspace.addCheckpoint.mock.calls[0][1];

      // Verify the document was created with the correct content - line2 should be deleted
      expect(callArgs.document.filePath).toEqual(filepath);
      expect(callArgs.document.originalCode).toEqual(fileContents);

      // The exact modified code should be "line1\n\nline3" with an extra newline
      expect(callArgs.document.modifiedCode).toEqual("line1\n\nline3");
    });

    it("should return error when line number ranges completely overlap", async () => {
      // Using string path directly in tests
      const fileContents = "line1\nline2\nline3\nline4\nline5";

      // Set the file content in our test workspace
      testWorkspace.setFileContent("test.txt", fileContents);

      const result = await tool.call(
        {
          command: "str_replace",
          path: "test.txt",
          str_replace_entries: [
            {
              old_str: "line2\nline3",
              new_str: "replaced lines 2-3",
              old_str_start_line_number: 2,
              old_str_end_line_number: 3,
            },
            {
              old_str: "line3",
              new_str: "replaced line 3",
              old_str_start_line_number: 3,
              old_str_end_line_number: 3,
            },
          ],
        },
        [],
        new AbortController().signal,
        "",
      );

      expect(result.isError).toBe(true);
      expect(result.text).toContain("Failed to edit the file test.txt");
      expect(result.text).toContain(
        "old_str line numbers range overlaps with another entry",
      );
      expect(result.text).toContain("This entry range: [3-3]");
      expect(result.text).toContain("Overlapping entry range: [2-3]");
      expect(testWorkspace.addCheckpoint).not.toHaveBeenCalled();
    });

    it("should return error when line number ranges partially overlap", async () => {
      // Using string path directly in tests
      const fileContents = "line1\nline2\nline3\nline4\nline5";

      // Set the file content in our test workspace
      testWorkspace.setFileContent("test.txt", fileContents);

      const result = await tool.call(
        {
          command: "str_replace",
          path: "test.txt",
          str_replace_entries: [
            {
              old_str: "line2\nline3",
              new_str: "replaced lines 2-3",
              old_str_start_line_number: 2,
              old_str_end_line_number: 3,
            },
            {
              old_str: "line3\nline4",
              new_str: "replaced lines 3-4",
              old_str_start_line_number: 3,
              old_str_end_line_number: 4,
            },
          ],
        },
        [],
        new AbortController().signal,
        "",
      );

      expect(result.isError).toBe(true);
      expect(result.text).toContain("Failed to edit the file test.txt");
      expect(result.text).toContain(
        "old_str line numbers range overlaps with another entry",
      );
      expect(result.text).toContain("This entry range: [3-4]");
      expect(result.text).toContain("Overlapping entry range: [2-3]");
      expect(testWorkspace.addCheckpoint).not.toHaveBeenCalled();
    });

    it("should not return error when line number ranges don't overlap", async () => {
      const filepath = new QualifiedPathName("/root/path", "test.txt");
      const fileContents = "line1\nline2\nline3\nline4\nline5";

      // Set the file content in our test workspace
      testWorkspace.setFileContent("test.txt", fileContents);

      const result = await tool.call(
        {
          command: "str_replace",
          path: "test.txt",
          str_replace_entries: [
            {
              old_str: "line1\nline2",
              new_str: "replaced lines 1-2",
              old_str_start_line_number: 1,
              old_str_end_line_number: 2,
            },
            {
              old_str: "line4\nline5",
              new_str: "replaced lines 4-5",
              old_str_start_line_number: 4,
              old_str_end_line_number: 5,
            },
          ],
        },
        [],
        new AbortController().signal,
        "",
      );

      expect(result.isError).toBe(false);
      expect(result.text).toContain("Successfully edited the file test.txt");
      expect(testWorkspace.addCheckpoint).toHaveBeenCalled();

      // Verify the document was created with the correct content
      const callArgs = testWorkspace.addCheckpoint.mock.calls[0][1];
      expect(callArgs.document.filePath).toEqual(filepath);
      expect(callArgs.document.originalCode).toEqual(fileContents);
      expect(callArgs.document.modifiedCode).toEqual(
        "replaced lines 1-2\nline3\nreplaced lines 4-5",
      );
      expect(callArgs.timestamp).toBeGreaterThan(0);
    });

    it("should not check for overlaps when entries don't have line numbers", async () => {
      const filepath = new QualifiedPathName("/root/path", "test.txt");
      const fileContents = "unique1\nunique2\nunique3";

      // Set the file content in our test workspace
      testWorkspace.setFileContent("test.txt", fileContents);

      const result = await tool.call(
        {
          command: "str_replace",
          path: "test.txt",
          str_replace_entries: [
            {
              old_str: "unique1",
              new_str: "replaced 1",
            },
            {
              old_str: "unique2",
              new_str: "replaced 2",
            },
          ],
        },
        [],
        new AbortController().signal,
        "",
      );

      expect(result.isError).toBe(false);
      expect(result.text).toContain("Successfully edited the file test.txt");
      expect(testWorkspace.addCheckpoint).toHaveBeenCalled();

      // Verify the document was created with the correct content
      const callArgs = testWorkspace.addCheckpoint.mock.calls[0][1];
      expect(callArgs.document.filePath).toEqual(filepath);
      expect(callArgs.document.originalCode).toEqual(fileContents);
      expect(callArgs.document.modifiedCode).toEqual(
        "replaced 1\nreplaced 2\nunique3",
      );
      expect(callArgs.timestamp).toBeGreaterThan(0);
    });

    it("should return error when some entries overlap and some don't", async () => {
      // Using string path directly in tests
      const fileContents = "line1\nline2\nline3\nline4\nline5\nline6";

      // Set the file content in our test workspace
      testWorkspace.setFileContent("test.txt", fileContents);

      const result = await tool.call(
        {
          command: "str_replace",
          path: "test.txt",
          str_replace_entries: [
            {
              old_str: "line1",
              new_str: "replaced line 1",
              old_str_start_line_number: 1,
              old_str_end_line_number: 1,
            },
            {
              old_str: "line3\nline4",
              new_str: "replaced lines 3-4",
              old_str_start_line_number: 3,
              old_str_end_line_number: 4,
            },
            {
              old_str: "line4\nline5",
              new_str: "replaced lines 4-5",
              old_str_start_line_number: 4,
              old_str_end_line_number: 5,
            },
            {
              old_str: "line6",
              new_str: "replaced line 6",
              old_str_start_line_number: 6,
              old_str_end_line_number: 6,
            },
          ],
        },
        [],
        new AbortController().signal,
        "",
      );

      // Should not fail because not all entries overlap
      expect(result.isError).toBe(false);
      expect(result.text).toContain(
        "Partially edited the file test.txt. See below for details.",
      );

      // Should specifically mention the overlapping ranges
      expect(result.text).toContain("This entry range: [4-5]");
      expect(result.text).toContain("Overlapping entry range: [3-4]");

      // Should have made partial changes
      expect(testWorkspace.addCheckpoint).toHaveBeenCalled();
    });
  });

  describe("insert command", () => {
    it("should insert content at the specified line and verify the result", async () => {
      const filepath = new QualifiedPathName("/root/path", "test.txt");
      const fileContents = "line1\nline2\nline3";

      // Set the file content in our test workspace
      testWorkspace.setFileContent("test.txt", fileContents);

      const result = await tool.call(
        {
          command: "insert",
          path: "test.txt",
          insert_line_entries: [
            {
              insert_line: 1,
              new_str: "inserted line",
            },
          ],
        },
        [],
        new AbortController().signal,
        "",
      );

      expect(result.text).toContain("Successfully edited the file test.txt");
      expect(testWorkspace.addCheckpoint).toHaveBeenCalled();

      // Verify the document was created with the correct content
      expect(testWorkspace.addCheckpoint).toHaveBeenCalledWith(
        expect.objectContaining({
          path: filepath,
        }),
        expect.objectContaining({
          // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
          document: expect.objectContaining({
            filePath: filepath,
            _originalCode: fileContents,
            _modifiedCode: "line1\ninserted line\nline2\nline3",
          }),
        }),
      );
    });

    it("should insert content at the beginning of the file", async () => {
      const filepath = new QualifiedPathName("/root/path", "test.txt");
      const fileContents = "line1\nline2\nline3";

      // Set the file content in our test workspace
      testWorkspace.setFileContent("test.txt", fileContents);

      const result = await tool.call(
        {
          command: "insert",
          path: "test.txt",
          insert_line_entries: [
            {
              insert_line: 1,
              new_str: "first line",
            },
          ],
        },
        [],
        new AbortController().signal,
        "",
      );

      expect(result.text).toContain("Successfully edited the file test.txt");
      expect(testWorkspace.addCheckpoint).toHaveBeenCalled();

      // Verify the document was created with the correct content
      const callArgs = testWorkspace.addCheckpoint.mock.calls[0][1];
      expect(callArgs.document.filePath).toEqual(filepath);
      expect(callArgs.document.originalCode).toEqual(fileContents);
      expect(callArgs.document.modifiedCode).toEqual(
        "line1\nfirst line\nline2\nline3",
      );
      expect(callArgs.timestamp).toBeGreaterThan(0);
    });

    it("should insert content at the end of the file", async () => {
      const filepath = new QualifiedPathName("/root/path", "test.txt");
      const fileContents = "line1\nline2\nline3";

      // Set the file content in our test workspace
      testWorkspace.setFileContent("test.txt", fileContents);

      const result = await tool.call(
        {
          command: "insert",
          path: "test.txt",
          insert_line_entries: [
            {
              insert_line: 3,
              new_str: "last line",
            },
          ],
        },
        [],
        new AbortController().signal,
        "",
      );

      expect(result.text).toContain("Successfully edited the file test.txt");
      expect(testWorkspace.addCheckpoint).toHaveBeenCalled();

      // Verify the document was created with the correct content
      expect(testWorkspace.addCheckpoint).toHaveBeenCalledWith(
        expect.objectContaining({
          path: filepath,
        }),
        expect.objectContaining({
          // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
          document: expect.objectContaining({
            filePath: filepath,
            _originalCode: fileContents,
            _modifiedCode: "line1\nline2\nline3\nlast line",
          }),
        }),
      );
    });

    it("should insert multi-line content and verify the result", async () => {
      const filepath = new QualifiedPathName("/root/path", "test.txt");
      const fileContents = "line1\nline2\nline3";

      // Set the file content in our test workspace
      testWorkspace.setFileContent("test.txt", fileContents);

      const result = await tool.call(
        {
          command: "insert",
          path: "test.txt",
          insert_line_entries: [
            {
              insert_line: 2,
              new_str: "new line 1\nnew line 2",
            },
          ],
        },
        [],
        new AbortController().signal,
        "",
      );

      expect(result.text).toContain("Successfully edited the file test.txt");
      expect(testWorkspace.addCheckpoint).toHaveBeenCalled();

      // Verify the document was created with the correct content
      expect(testWorkspace.addCheckpoint).toHaveBeenCalledWith(
        expect.objectContaining({
          path: filepath,
        }),
        expect.objectContaining({
          // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
          document: expect.objectContaining({
            filePath: filepath,
            _originalCode: fileContents,
            _modifiedCode: "line1\nline2\nnew line 1\nnew line 2\nline3",
          }),
        }),
      );
    });

    it("should allow empty new_str in insert command", async () => {
      const filepath = new QualifiedPathName("/root/path", "test.txt");
      const fileContents = "line1\nline2\nline3";

      // Set the file content in our test workspace
      testWorkspace.setFileContent("test.txt", fileContents);

      const result = await tool.call(
        {
          command: "insert",
          path: "test.txt",
          insert_line_entries: [
            {
              insert_line: 1,
              new_str: "",
            },
          ],
        },
        [],
        new AbortController().signal,
        "",
      );

      expect(result.text).toContain("Successfully edited the file test.txt");
      expect(testWorkspace.addCheckpoint).toHaveBeenCalled();

      // Get the actual modified code from the mock call
      const callArgs = testWorkspace.addCheckpoint.mock.calls[0][1];

      // Verify the document was created with the correct content
      expect(callArgs.document.filePath).toEqual(filepath);
      expect(callArgs.document.originalCode).toEqual(fileContents);

      // An empty string insertion should still create a new line
      expect(callArgs.document.modifiedCode).toEqual("line1\n\nline2\nline3");
    });
  });

  describe("insert with multiple entries", () => {
    it("should handle multiple insertions in a file", async () => {
      const filepath = new QualifiedPathName("/root/path", "test.txt");
      const fileContents = "line1\nline2\nline3";

      // Set the file content in our test workspace
      testWorkspace.setFileContent("test.txt", fileContents);

      const result = await tool.call(
        {
          command: "insert",
          path: "test.txt",
          insert_line_entries: [
            {
              insert_line: 1,
              new_str: "inserted after line1",
            },
            {
              insert_line: 3,
              new_str: "inserted after line3",
            },
          ],
        },
        [],
        new AbortController().signal,
        "",
      );

      expect(result.text).toContain("Successfully edited the file test.txt");
      expect(testWorkspace.addCheckpoint).toHaveBeenCalled();

      // Verify the document was created with the correct content
      // Note: insertions are processed from bottom to top to avoid line number shifts
      const callArgs = testWorkspace.addCheckpoint.mock.calls[0][1];
      expect(callArgs.document.filePath).toEqual(filepath);
      expect(callArgs.document.originalCode).toEqual(fileContents);
      expect(callArgs.document.modifiedCode).toEqual(
        "line1\ninserted after line1\nline2\nline3\ninserted after line3",
      );
      expect(callArgs.timestamp).toBeGreaterThan(0);
    });

    it("should handle insertions at the beginning and end of the file", async () => {
      const filepath = new QualifiedPathName("/root/path", "test.txt");
      const fileContents = "line1\nline2\nline3";

      // Set the file content in our test workspace
      testWorkspace.setFileContent("test.txt", fileContents);

      const result = await tool.call(
        {
          command: "insert",
          path: "test.txt",
          insert_line_entries: [
            {
              insert_line: 1,
              new_str: "first line",
            },
            {
              insert_line: 3,
              new_str: "last line",
            },
          ],
        },
        [],
        new AbortController().signal,
        "",
      );

      expect(result.text).toContain("Successfully edited the file test.txt");
      expect(testWorkspace.addCheckpoint).toHaveBeenCalled();

      // Verify the document was created with the correct content
      const callArgs = testWorkspace.addCheckpoint.mock.calls[0][1];
      expect(callArgs.document.filePath).toEqual(filepath);
      expect(callArgs.document.originalCode).toEqual(fileContents);
      expect(callArgs.document.modifiedCode).toEqual(
        "line1\nfirst line\nline2\nline3\nlast line",
      );
      expect(callArgs.timestamp).toBeGreaterThan(0);
    });

    it("should return error if insert_line_entries is empty", async () => {
      // Using string path directly in tests
      const fileContents = "line1\nline2\nline3";

      // Set the file content in our test workspace
      testWorkspace.setFileContent("test.txt", fileContents);

      const result = await tool.call(
        {
          command: "insert",
          path: "test.txt",
          insert_line_entries: [],
        },
        [],
        new AbortController().signal,
        "",
      );

      expect(result.text).toContain(
        "Empty required parameter `insert_line_entries` for `insert` command.",
      );
      expect(testWorkspace.addCheckpoint).not.toHaveBeenCalled();
      expect(result.isError).toBe(true);
    });

    it("should return error if any entry is missing required properties", async () => {
      // Using string path directly in tests
      const fileContents = "line1\nline2\nline3";

      // Set the file content in our test workspace
      testWorkspace.setFileContent("test.txt", fileContents);

      const result = await tool.call(
        {
          command: "insert",
          path: "test.txt",
          insert_line_entries: [
            {
              insert_line: 1,
              // missing new_str
            },
          ],
        },
        [],
        new AbortController().signal,
        "",
      );

      expect(result.text).toContain(
        "Missing required parameter `new_str` for `insert` command.",
      );
      expect(testWorkspace.addCheckpoint).not.toHaveBeenCalled();
      expect(result.isError).toBe(true);
    });

    it("should return error if insert_line_entries has wrong format", async () => {
      // Using string path directly in tests
      const fileContents = "line1\nline2\nline3";

      // Set the file content in our test workspace
      testWorkspace.setFileContent("test.txt", fileContents);

      // Test with insert_line_entries as a string instead of an array
      const result = await tool.call(
        {
          command: "insert",
          path: "test.txt",
          insert_line_entries: "This is not an array",
        },
        [],
        new AbortController().signal,
        "",
      );

      expect(result.text).toContain(
        "Invalid parameter `insert_line_entries` for `insert` command. It must be an array of objects.",
      );
      expect(testWorkspace.addCheckpoint).not.toHaveBeenCalled();
      expect(result.isError).toBe(true);
    });

    it("should return error if insert_line_entries is an empty object", async () => {
      // Using string path directly in tests
      const fileContents = "line1\nline2\nline3";

      // Set the file content in our test workspace
      testWorkspace.setFileContent("test.txt", fileContents);

      // Test with insert_line_entries as an object instead of an array
      const result = await tool.call(
        {
          command: "insert",
          path: "test.txt",
          insert_line_entries: {},
        },
        [],
        new AbortController().signal,
        "",
      );

      expect(result.text).toContain(
        "Invalid parameter `insert_line_entries` for `insert` command. It must be an array of objects.",
      );
      expect(testWorkspace.addCheckpoint).not.toHaveBeenCalled();
      expect(result.isError).toBe(true);
    });

    it("should return error if insert_line is out of range", async () => {
      // Using string path directly in tests
      const fileContents = "line1\nline2\nline3";

      // Set the file content in our test workspace
      testWorkspace.setFileContent("test.txt", fileContents);

      const result = await tool.call(
        {
          command: "insert",
          path: "test.txt",
          insert_line_entries: [
            {
              insert_line: 10, // Out of range
              new_str: "invalid line",
            },
          ],
        },
        [],
        new AbortController().signal,
        "",
      );

      expect(result.text).toContain("Invalid `insert_line` parameter");
      expect(testWorkspace.addCheckpoint).not.toHaveBeenCalled();
      expect(result.isError).toBe(true);
    });
  });

  it("should ensure output snippets show correct location when inserting large content", async () => {
    const filepath = new QualifiedPathName("/root/path", "test.txt");
    const fileContents = "line1\nline2\nline3\nline4\nline5";

    // Set the file content in our test workspace
    testWorkspace.setFileContent("test.txt", fileContents);

    // Create a large first insert (10 lines)
    const largeInsert = Array.from(
      { length: 10 },
      (_, i) => `large insert line ${i + 1}`,
    ).join("\n");

    const result = await tool.call(
      {
        command: "insert",
        path: "test.txt",
        insert_line_entries: [
          {
            insert_line: 1, // Insert after line 1
            new_str: largeInsert,
          },
          {
            insert_line: 4, // This should be line 4 in the original file, which becomes line 14 after first insert
            new_str: "second insert",
          },
        ],
      },
      [],
      new AbortController().signal,
      "",
    );

    expect(result.text).toEqual(`\
Successfully edited the file test.txt.
Result for insert for entry with index [0]:
Successfully inserted new_str.
Edited section after IDE auto-formatting was applied:
     1\tline1
     2\tlarge insert line 1
     3\tlarge insert line 2
     4\tlarge insert line 3
     5\tlarge insert line 4
     6\tlarge insert line 5
     7\tlarge insert line 6
     8\tlarge insert line 7
     9\tlarge insert line 8
    10\tlarge insert line 9
    11\tlarge insert line 10
    12\tline2
    13\tline3
    14\tline4
    15\tsecond insert

Result for insert for entry with index [1]:
Successfully inserted new_str.
Edited section after IDE auto-formatting was applied:
    11\tlarge insert line 10
    12\tline2
    13\tline3
    14\tline4
    15\tsecond insert
    16\tline5

Review the changes and make sure they are as expected (correct indentation, no duplicate lines, etc).
Edit the file again if necessary.
`);

    expect(testWorkspace.addCheckpoint).toHaveBeenCalled();

    // Verify the document was created with the correct content
    const callArgs = testWorkspace.addCheckpoint.mock.calls[0][1];
    expect(callArgs.document.filePath).toEqual(filepath);
    expect(callArgs.document.originalCode).toEqual(fileContents);

    // Expected content should have the large insert after line1 and second insert after line4
    const expectedContent =
      "line1\n" +
      largeInsert +
      "\n" +
      "line2\nline3\nline4\n" +
      "second insert\n" +
      "line5";

    expect(callArgs.document.modifiedCode).toEqual(expectedContent);
  });

  it("should automatically remove trailing whitespace in content and oldStr", async () => {
    const filepath = new QualifiedPathName("/root/path", "test.txt");
    const fileContents = "line1\nline2  \nline3";

    // Set the file content in our test workspace
    testWorkspace.setFileContent("test.txt", fileContents);

    const result = await tool.call(
      {
        command: "str_replace",
        path: "test.txt",
        str_replace_entries: [
          {
            old_str: "line2  ", // With trailing spaces
            new_str: "replaced line",
          },
        ],
      },
      [],
      new AbortController().signal,
      "",
    );

    expect(result.text).toContain("Successfully edited the file test.txt");
    expect(testWorkspace.addCheckpoint).toHaveBeenCalled();

    // Verify the document was created with the correct content
    // Note: trailing whitespace should be removed from both content and oldStr
    expect(testWorkspace.addCheckpoint).toHaveBeenCalledWith(
      expect.objectContaining({
        path: filepath,
        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
        conversationId: expect.any(String),
      }),
      expect.objectContaining({
        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
        document: expect.objectContaining({
          filePath: filepath,
          _originalCode: "line1\nline2  \nline3", // Original content preserves trailing spaces
          _modifiedCode: "line1\nreplaced line\nline3",
        }),
        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
        conversationId: expect.any(String),
        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
        sourceToolCallRequestId: expect.any(String),
        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
        timestamp: expect.any(Number),
      }),
    );
  });
  it("should automatically remove trailing whitespace in newStr for str_replace", async () => {
    const filepath = new QualifiedPathName("/root/path", "test.txt");
    const fileContents = "line1\nline2\nline3";

    // Set the file content in our test workspace
    testWorkspace.setFileContent("test.txt", fileContents);

    const result = await tool.call(
      {
        command: "str_replace",
        path: "test.txt",
        str_replace_entries: [
          {
            old_str: "line2",
            new_str: "replaced line  \nwith trailing spaces  ", // With trailing spaces
          },
        ],
      },
      [],
      new AbortController().signal,
      "",
    );

    expect(result.text).toContain("Successfully edited the file test.txt");
    expect(testWorkspace.addCheckpoint).toHaveBeenCalled();

    // Verify the document was created with the correct content
    // Note: trailing whitespace should be removed from newStr
    expect(testWorkspace.addCheckpoint).toHaveBeenCalledWith(
      expect.objectContaining({
        path: filepath,
      }),
      expect.objectContaining({
        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
        document: expect.objectContaining({
          filePath: filepath,
          _originalCode: fileContents,
          _modifiedCode: "line1\nreplaced line\nwith trailing spaces\nline3", // Trailing spaces removed
        }),
      }),
    );
  });

  it("should ensure output snippets reflect the final state after multiple inserts", async () => {
    const filepath = new QualifiedPathName("/root/path", "test.txt");
    const fileContents = "line1\nline2\nline3";

    // Set the file content in our test workspace
    testWorkspace.setFileContent("test.txt", fileContents);

    const result = await tool.call(
      {
        command: "insert",
        path: "test.txt",
        insert_line_entries: [
          {
            insert_line: 1, // Insert after line 1
            new_str: "first insert",
          },
          {
            insert_line: 2, // This should be line 2 in the original file, which becomes line 3 after first insert
            new_str: "second insert",
          },
        ],
      },
      [],
      new AbortController().signal,
      "",
    );

    expect(result.text).toEqual(`\
Successfully edited the file test.txt.
Result for insert for entry with index [0]:
Successfully inserted new_str.
Edited section after IDE auto-formatting was applied:
     1\tline1
     2\tfirst insert
     3\tline2
     4\tsecond insert
     5\tline3

Result for insert for entry with index [1]:
Successfully inserted new_str.
Edited section after IDE auto-formatting was applied:
     1\tline1
     2\tfirst insert
     3\tline2
     4\tsecond insert
     5\tline3

Review the changes and make sure they are as expected (correct indentation, no duplicate lines, etc).
Edit the file again if necessary.
`);

    expect(testWorkspace.addCheckpoint).toHaveBeenCalled();

    // Verify the document was created with the correct content
    const callArgs = testWorkspace.addCheckpoint.mock.calls[0][1];
    expect(callArgs.document.filePath).toEqual(filepath);
    expect(callArgs.document.originalCode).toEqual(fileContents);
    expect(callArgs.document.modifiedCode).toEqual(
      "line1\nfirst insert\nline2\nsecond insert\nline3",
    );
  });

  it("should ensure output snippets reflect the final state after multiple str_replace operations", async () => {
    const filepath = new QualifiedPathName("/root/path", "test.txt");
    const fileContents = "line1\nline2\nline3\nline4\nline5";

    // Set the file content in our test workspace
    testWorkspace.setFileContent("test.txt", fileContents);

    const result = await tool.call(
      {
        command: "str_replace",
        path: "test.txt",
        str_replace_entries: [
          {
            old_str: "line2",
            new_str: "replaced line 2",
            old_str_start_line_number: 2,
            old_str_end_line_number: 2,
          },
          {
            old_str: "line4",
            new_str: "replaced line 4",
            old_str_start_line_number: 4,
            old_str_end_line_number: 4,
          },
        ],
      },
      [],
      new AbortController().signal,
      "",
    );

    // Verify the result text shows the final state after both replacements
    expect(result.text).toContain("Successfully edited the file test.txt");

    // Check that both replacements are mentioned in the result
    expect(result.text).toContain(
      "Result for str_replace for entry with index [0]",
    );
    expect(result.text).toContain(
      "Result for str_replace for entry with index [1]",
    );

    // Check that the edited sections in the output reflect the final state
    // Both edited sections should show the same final state with both replacements applied
    const expectedSnippet =
      "     1\tline1\n" +
      "     2\treplaced line 2\n" +
      "     3\tline3\n" +
      "     4\treplaced line 4\n" +
      "     5\tline5";

    // Both result sections should show the same final state
    const resultSections = result.text.split(
      "Result for str_replace for entry with index",
    );
    expect(resultSections.length).toBe(3); // Original text + 2 sections
    expect(resultSections[1]).toContain(expectedSnippet);
    expect(resultSections[2]).toContain(expectedSnippet);

    expect(testWorkspace.addCheckpoint).toHaveBeenCalled();

    // Verify the document was created with the correct content
    const callArgs = testWorkspace.addCheckpoint.mock.calls[0][1];
    expect(callArgs.document.filePath).toEqual(filepath);
    expect(callArgs.document.originalCode).toEqual(fileContents);
    expect(callArgs.document.modifiedCode).toEqual(
      "line1\nreplaced line 2\nline3\nreplaced line 4\nline5",
    );
  });

  it("should automatically remove trailing whitespace in newStr for insert", async () => {
    const filepath = new QualifiedPathName("/root/path", "test.txt");
    const fileContents = "line1\nline2\nline3";

    // Set the file content in our test workspace
    testWorkspace.setFileContent("test.txt", fileContents);

    const result = await tool.call(
      {
        command: "insert",
        path: "test.txt",
        insert_line_entries: [
          {
            insert_line: 1,
            new_str: "inserted line  \nwith trailing spaces  ", // With trailing spaces
          },
        ],
      },
      [],
      new AbortController().signal,
      "",
    );

    expect(result.text).toContain("Successfully edited the file test.txt");
    expect(testWorkspace.addCheckpoint).toHaveBeenCalled();

    // Verify the document was created with the correct content
    // Note: trailing whitespace should be removed from newStr
    expect(testWorkspace.addCheckpoint).toHaveBeenCalledWith(
      expect.objectContaining({
        path: filepath,
      }),
      expect.objectContaining({
        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
        document: expect.objectContaining({
          filePath: filepath,
          _originalCode: fileContents,
          _modifiedCode:
            "line1\ninserted line\nwith trailing spaces\nline2\nline3", // Trailing spaces removed
        }),
      }),
    );
  });

  it("should demonstrate the combined MockWorkspaceWithCheckpoints functionality", async () => {
    // Set up initial file content
    const initialContent = "line1\nline2\nline3";
    testWorkspace.setFileContent("test.txt", initialContent);

    // Verify initial content
    expect(testWorkspace.getFileContent("test.txt")).toEqual(initialContent);
    expect(testWorkspace.getFileContent("test.txt")).toContain("line2");

    // Now modify the file using str_replace
    const replaceResult = await tool.call(
      {
        command: "str_replace",
        path: "test.txt",
        str_replace_entries: [
          {
            old_str: "line2",
            new_str: "MODIFIED LINE",
          },
        ],
      },
      [],
      new AbortController().signal,
      "",
    );
    expect(replaceResult.text).toContain(
      "Successfully edited the file test.txt",
    );
    expect(testWorkspace.addCheckpoint).toHaveBeenCalled();

    // Verify the file content was updated in our test workspace
    expect(testWorkspace.getFileContent("test.txt")).toEqual(
      "line1\nMODIFIED LINE\nline3",
    );
    expect(testWorkspace.getFileContent("test.txt")).toContain("MODIFIED LINE");
    expect(testWorkspace.getFileContent("test.txt")).not.toContain("line2");

    // Now make another change using insert
    const insertResult = await tool.call(
      {
        command: "insert",
        path: "test.txt",
        insert_line_entries: [
          {
            insert_line: 1,
            new_str: "INSERTED LINE",
          },
        ],
      },
      [],
      new AbortController().signal,
      "",
    );
    expect(insertResult.text).toContain(
      "Successfully edited the file test.txt",
    );

    // The final content should have both changes
    const expectedFinalContent = "line1\nINSERTED LINE\nMODIFIED LINE\nline3";
    expect(testWorkspace.getFileContent("test.txt")).toEqual(
      expectedFinalContent,
    );
    expect(testWorkspace.getFileContent("test.txt")).toContain("INSERTED LINE");
    expect(testWorkspace.getFileContent("test.txt")).toContain("MODIFIED LINE");
    expect(testWorkspace.getFileContent("test.txt")).not.toContain("line2");
  });

  it("should only apply tab indentation fix when all three have tab indentation", async () => {
    const _filepath = new QualifiedPathName("/root/path", "test.txt");
    const fileContents =
      "function test() {\n\tif (condition) {\n\t\tconsole.log('test');\n\t}\n}";

    // Set the file content in our test workspace
    testWorkspace.setFileContent("test.txt", fileContents);

    // Both old_str and new_str have an extra tab compared to the file content
    const result = await tool.call(
      {
        command: "str_replace",
        path: "test.txt",
        str_replace_entries: [
          {
            old_str: "\t\tconsole.log('test');",
            new_str: "\t\tconsole.log('modified test');",
          },
        ],
      },
      [],
      new AbortController().signal,
      "",
    );

    expect(result.text).toContain("Successfully edited the file test.txt");
    expect(testWorkspace.addCheckpoint).toHaveBeenCalled();

    // Verify the document was created with the correct content
    // The indentation fix should have been applied
    const callArgs = testWorkspace.addCheckpoint.mock.calls[0][1];
    expect(callArgs.document.filePath).toEqual(_filepath);
    expect(callArgs.document.originalCode).toEqual(fileContents);
    expect(callArgs.document.modifiedCode).toEqual(
      "function test() {\n\tif (condition) {\n\t\tconsole.log('modified test');\n\t}\n}",
    );
    expect(callArgs.timestamp).toBeGreaterThan(0);
  });

  it("should not apply tab indentation fix when not all have tab indentation", async () => {
    // Using string path directly in tests
    const fileContents =
      "function test() {\n  if (condition) {\n    console.log('test');\n  }\n}";

    // Set the file content in our test workspace
    testWorkspace.setFileContent("test.txt", fileContents);

    // Try to replace with tab indentation when file uses spaces
    const result = await tool.call(
      {
        command: "str_replace",
        path: "test.txt",
        str_replace_entries: [
          {
            old_str: "\t\tconsole.log('test');",
            new_str: "\t\tconsole.log('modified test');",
          },
        ],
      },
      [],
      new AbortController().signal,
      "",
    );

    expect(result.isError).toBe(true);
    expect(result.text).toContain("No replacement was performed");
    expect(testWorkspace.addCheckpoint).not.toHaveBeenCalled();
  });

  describe("singleStrReplace", () => {
    describe("basic functionality", () => {
      it("should return error when oldStr is empty", () => {
        const result = callSingleStrReplace(
          "test.txt",
          "content",
          "", // empty string
          "new",
          0,
        );
        expect(result.isError).toBe(true);
        expect(result.message).toContain(
          "old_str is empty which is only allowed when the file is empty",
        );
      });

      it("should return error when oldStr doesn't match content", () => {
        const result = callSingleStrReplace(
          "test.txt",
          "content",
          "old", // doesn't exist in content
          "",
          0,
        );
        expect(result.isError).toBe(true);
        expect(result.message).toContain(
          "oldStr did not appear verbatim in test.txt",
        );
      });

      it("should return diff when oldStr doesn't match content and line numbers are provided", () => {
        const content = "line1\nline2\nline3";
        const oldStr = "different content";
        const result = callSingleStrReplace(
          "test.txt",
          content,
          oldStr,
          "new content",
          0, // index
          0, // start line
          1, // end line
        );

        expect(result.isError).toBe(true);
        const tab = "\t";
        const expectedMessage = `
No replacement was performed, oldStr did not appear verbatim in test.txt.
The content in the specified region is:
     1${tab}line1
     2${tab}line2
     3${tab}line3

Diff between oldStr and the specified region is:
===================================================================
--- oldStr
+++ regionContent
@@ -1,1 +1,2 @@
-different content
+line1
+line2
`;
        expect(result.message).toBe(expectedMessage.trimStart());
      });

      it("should not return error when oldStr and newStr are identical", () => {
        const result = callSingleStrReplace(
          "test.txt",
          "content\nsame",
          "same",
          "same",
          0,
        );
        expect(result.isError).toBe(false);
        expect(result.message).toContain("Replacement successful.");
      });

      it("should handle empty file and empty oldStr", () => {
        const result = callSingleStrReplace(
          "test.txt",
          "",
          "",
          "new content",
          0,
        );
        expect(result.isError).toBe(false);
        expect(result.newContent).toBe("new content");
      });

      it("should return error when oldStr is empty but file is not", () => {
        const result = callSingleStrReplace(
          "test.txt",
          "content",
          "",
          "new",
          0,
        );
        expect(result.isError).toBe(true);
        expect(result.message).toContain(
          "No replacement was performed, old_str is empty which is only allowed when the file is empty",
        );
      });
    });

    describe("single match cases", () => {
      it("should replace content when there is a single match", () => {
        const content = "line1\nline2\nline3";
        const result = callSingleStrReplace(
          "test.txt",
          content,
          "line2",
          "replaced",
          0,
        );

        expect(result.isError).toBe(false);
        expect(result.newContent).toBe("line1\nreplaced\nline3");
      });

      it("should replace multi-line content", () => {
        const content = "line1\nline2\nline3\nline4";
        const result = callSingleStrReplace(
          "test.txt",
          content,
          "line2\nline3",
          "replaced",
          0,
        );

        expect(result.isError).toBe(false);
        expect(result.newContent).toBe("line1\nreplaced\nline4");
      });
    });

    describe("multiple matches with line number disambiguation", () => {
      it("should use line numbers to disambiguate between multiple matches", () => {
        const content = "repeated\nother content\nrepeated";

        // Should match the first occurrence (line 0)
        const result1 = callSingleStrReplace(
          "test.txt",
          content,
          "repeated",
          "replaced",
          0, // index
          0, // start line
          0, // end line
        );
        expect(result1.isError).toBe(false);
        expect(result1.newContent).toBe("replaced\nother content\nrepeated");

        // Should match the second occurrence (line 2)
        const result2 = callSingleStrReplace(
          "test.txt",
          content,
          "repeated",
          "replaced",
          1, // index
          2, // start line
          2, // end line
        );
        expect(result2.isError).toBe(false);
        expect(result2.newContent).toBe("repeated\nother content\nreplaced");
      });

      it("should use line number tolerance to find closest match", () => {
        // Create a tool with zero tolerance
        const zeroToleranceTool = new StrReplaceEditorTool(
          testWorkspace,
          0, // Zero tolerance - must match exactly
          1000,
          new StrReplaceEditorToolDefinitionNested(),
        );

        const content = "repeated\nother content\nrepeated";

        // Should not match with zero tolerance when line numbers are off
        // Use the tool directly instead of the helper function
        const result = (
          zeroToleranceTool as unknown as {
            singleStrReplace: (
              path: string,
              content: string,
              oldStr: string,
              newStr: string,
              index: number,
              oldStrStartLineNumber?: number,
              oldStrEndLineNumber?: number,
            ) => EditResult;
          }
        ).singleStrReplace(
          "test.txt",
          content,
          "repeated",
          "replaced",
          0, // index
          1, // Slightly off from actual line 0
          1,
        );

        expect(result.isError).toBe(true);
        const message = result.genMessageFunc
          ? result.genMessageFunc(result)
          : "";
        expect(message).toContain(
          "No match found close to the provided line numbers",
        );
      });

      it("should match exactly with zero tolerance when line numbers are correct", () => {
        // Create a tool with zero tolerance
        const zeroToleranceTool = new StrReplaceEditorTool(
          testWorkspace,
          0, // Zero tolerance - must match exactly
          500,
          new StrReplaceEditorToolDefinitionNested(),
        );

        const content = "repeated\nother content\nrepeated";

        // Should match when line numbers are exact, even with zero tolerance
        const result = (
          zeroToleranceTool as unknown as {
            singleStrReplace: (
              path: string,
              content: string,
              oldStr: string,
              newStr: string,
              index: number,
              oldStrStartLineNumber?: number,
              oldStrEndLineNumber?: number,
            ) => EditResult;
          }
        ).singleStrReplace(
          "test.txt",
          content,
          "repeated",
          "replaced",
          0, // index
          0, // Exact match for first occurrence
          0,
        );

        expect(result.isError).toBe(false);
        expect(result.newContent).toBe("replaced\nother content\nrepeated");
        const message = result.genMessageFunc
          ? result.genMessageFunc(result)
          : "";
        expect(message).not.toContain("No replacement was performed");
      });

      it("should return error when there are multiple matches but no line numbers provided", () => {
        const content = "repeated\nother content\nrepeated";
        const result = callSingleStrReplace(
          "test.txt",
          content,
          "repeated",
          "replaced",
          0,
        );

        expect(result.isError).toBe(true);
        expect(result.message).toContain("Multiple occurrences of oldStr");
        expect(result.message).toContain("Please provide line numbers");
      });
    });

    describe("indentation handling", () => {
      it("should handle tab indentation fix", () => {
        const content =
          "function test() {\n\tif (condition) {\n\t\tconsole.log('test');\n\t}\n}";

        // Extra tab in both old_str and new_str
        const result = callSingleStrReplace(
          "test.txt",
          content,
          "\t\t\tconsole.log('test');",
          "\t\t\tconsole.log('modified');",
          0,
        );

        expect(result.newContent).toBe(
          "function test() {\n\tif (condition) {\n\t\tconsole.log('modified');\n\t}\n}",
        );
      });
    });
  });

  describe("agentEditToolShowResultSnippet feature flag", () => {
    beforeEach(() => {
      // Reset to default state before each test
      setShowResultSnippetFlag(true);
    });

    it("should show snippet when agentEditToolShowResultSnippet is true", async () => {
      setShowResultSnippetFlag(true);

      const fileContents = "line1\nline2\nline3";
      testWorkspace.setFileContent("test.txt", fileContents);

      const result = await tool.call(
        {
          command: "str_replace",
          path: "test.txt",
          str_replace_entries: [
            {
              old_str: "line2",
              new_str: "replaced line 2",
            },
          ],
        },
        [],
        new AbortController().signal,
        "",
      );

      const expectedText = `Successfully edited the file test.txt.
Result for str_replace for entry with index [0]:
Replacement successful.
Edited section after IDE auto-formatting was applied:
     1\tline1
     2\treplaced line 2
     3\tline3

Review the changes and make sure they are as expected (correct indentation, no duplicate lines, etc).
Edit the file again if necessary.
`;

      expect(result.text).toBe(expectedText);
    });

    it("should show only line numbers when agentEditToolShowResultSnippet is false", async () => {
      setShowResultSnippetFlag(false);

      const fileContents = "line1\nline2\nline3";
      testWorkspace.setFileContent("test.txt", fileContents);

      const result = await tool.call(
        {
          command: "str_replace",
          path: "test.txt",
          str_replace_entries: [
            {
              old_str: "line2",
              new_str: "replaced line 2",
            },
          ],
        },
        [],
        new AbortController().signal,
        "",
      );

      const expectedText = `Successfully edited the file test.txt.
Result for str_replace for entry with index [0]:
Replacement successful.
new_str starts at line 2 and ends at line 2.

Review the changes and make sure they are as expected (correct indentation, no duplicate lines, etc).
Edit the file again if necessary.
`;

      expect(result.text).toBe(expectedText);
    });

    it("should show snippet when agentEditToolShowResultSnippet is false but IDE reformatted the code", async () => {
      setShowResultSnippetFlag(false);

      const fileContents = "line1\nline2\nline3";
      testWorkspace.setFileContent("test.txt", fileContents);

      // Set up auto-formatting to simulate IDE reformatting
      // The tool will write "line1\nreplaced line 2\nline3" and the mock will return it with an extra newline
      testWorkspace.setAutoFormatMapping(
        "test.txt",
        "line1\nreplaced line 2\nline3",
        "line1\nreplaced line 2\nline3\n", // Add newline to simulate formatting
      );

      const result = await tool.call(
        {
          command: "str_replace",
          path: "test.txt",
          str_replace_entries: [
            {
              old_str: "line2",
              new_str: "replaced line 2",
            },
          ],
        },
        [],
        new AbortController().signal,
        "",
      );

      const expectedText = `Successfully edited the file test.txt.
Result for str_replace for entry with index [0]:
Replacement successful.
Edited section after IDE auto-formatting was applied:
     1\tline1
     2\treplaced line 2
     3\tline3
     4\t

Review the changes and make sure they are as expected (correct indentation, no duplicate lines, etc).
Edit the file again if necessary.
`;

      expect(result.text).toBe(expectedText);
    });

    it("should show snippet for multiple replacements when agentEditToolShowResultSnippet is true", async () => {
      setShowResultSnippetFlag(true);

      const fileContents = "line1\nline2\nline3\nline4\nline5";
      testWorkspace.setFileContent("test.txt", fileContents);

      const result = await tool.call(
        {
          command: "str_replace",
          path: "test.txt",
          str_replace_entries: [
            {
              old_str: "line2",
              new_str: "replaced line 2",
              old_str_start_line_number: 2,
              old_str_end_line_number: 2,
            },
            {
              old_str: "line4",
              new_str: "replaced line 4",
              old_str_start_line_number: 4,
              old_str_end_line_number: 4,
            },
          ],
        },
        [],
        new AbortController().signal,
        "",
      );

      const expectedText = `Successfully edited the file test.txt.
Result for str_replace for entry with index [0]:
Replacement successful.
Edited section after IDE auto-formatting was applied:
     1\tline1
     2\treplaced line 2
     3\tline3
     4\treplaced line 4
     5\tline5

Result for str_replace for entry with index [1]:
Replacement successful.
Edited section after IDE auto-formatting was applied:
     1\tline1
     2\treplaced line 2
     3\tline3
     4\treplaced line 4
     5\tline5

Review the changes and make sure they are as expected (correct indentation, no duplicate lines, etc).
Edit the file again if necessary.
`;

      expect(result.text).toBe(expectedText);
    });

    it("should show only line numbers for multiple replacements when agentEditToolShowResultSnippet is false", async () => {
      setShowResultSnippetFlag(false);

      const fileContents = "line1\nline2\nline3\nline4\nline5";
      testWorkspace.setFileContent("test.txt", fileContents);

      const result = await tool.call(
        {
          command: "str_replace",
          path: "test.txt",
          str_replace_entries: [
            {
              old_str: "line2",
              new_str: "replaced line 2",
              old_str_start_line_number: 2,
              old_str_end_line_number: 2,
            },
            {
              old_str: "line4",
              new_str: "replaced line 4",
              old_str_start_line_number: 4,
              old_str_end_line_number: 4,
            },
          ],
        },
        [],
        new AbortController().signal,
        "",
      );

      const expectedText = `Successfully edited the file test.txt.
Result for str_replace for entry with index [0]:
Replacement successful.
new_str starts at line 2 and ends at line 2.

Result for str_replace for entry with index [1]:
Replacement successful.
new_str starts at line 4 and ends at line 4.

Review the changes and make sure they are as expected (correct indentation, no duplicate lines, etc).
Edit the file again if necessary.
`;

      expect(result.text).toBe(expectedText);
    });

    it("should show snippet for fuzzy matching when agentEditToolShowResultSnippet is true", async () => {
      setShowResultSnippetFlag(true);

      const fileContents = "test content here with more text";
      testWorkspace.setFileContent("test.txt", fileContents);

      const result = await tool.call(
        {
          command: "str_replace",
          path: "test.txt",
          str_replace_entries: [
            {
              old_str: "test content here", // This will match exactly, so let's use a different approach
              new_str: "new content here",
              old_str_start_line_number: 1,
              old_str_end_line_number: 1,
            },
          ],
        },
        [],
        new AbortController().signal,
        "",
      );

      const expectedText = `Successfully edited the file test.txt.
Result for str_replace for entry with index [0]:
Replacement successful.
Edited section after IDE auto-formatting was applied:
     1\tnew content here with more text

Review the changes and make sure they are as expected (correct indentation, no duplicate lines, etc).
Edit the file again if necessary.
`;

      expect(result.text).toBe(expectedText);
    });

    it("should show only line numbers for fuzzy matching when agentEditToolShowResultSnippet is false", async () => {
      setShowResultSnippetFlag(false);

      const fileContents = "test content here with more text";
      testWorkspace.setFileContent("test.txt", fileContents);

      const result = await tool.call(
        {
          command: "str_replace",
          path: "test.txt",
          str_replace_entries: [
            {
              old_str: "test content here", // This will match exactly, so let's use a different approach
              new_str: "new content here",
              old_str_start_line_number: 1,
              old_str_end_line_number: 1,
            },
          ],
        },
        [],
        new AbortController().signal,
        "",
      );

      const expectedText = `Successfully edited the file test.txt.
Result for str_replace for entry with index [0]:
Replacement successful.
new_str starts at line 1 and ends at line 1.

Review the changes and make sure they are as expected (correct indentation, no duplicate lines, etc).
Edit the file again if necessary.
`;

      expect(result.text).toBe(expectedText);
    });
  });

  describe("path auto-correction", () => {
    it("should automatically correct relative path when there's exactly one path with matching suffix", async () => {
      // Set up a file with a path that will be found as a suffix match
      const shortPath = "str-replace-editor-tool.ts";
      const fullPath =
        "clients/sidecar/libs/src/tools/sidecar-tools/str-replace-editor-tool/str-replace-editor-tool.ts";
      const absolutePath = path.join(testWorkspace.rootPath, fullPath);
      const fileContents = "line1\nline2\nline3";

      // Set up the file in the test workspace
      testWorkspace.setFileContent(fullPath, fileContents);

      // Mock findFiles to return the full path when searching for the file
      (testWorkspace.findFiles as jest.Mock) = jest
        .fn()
        .mockResolvedValue([
          new QualifiedPathName(testWorkspace.rootPath, fullPath),
        ]);

      const result = await tool.call(
        {
          command: "str_replace",
          path: shortPath,
          str_replace_entries: [
            {
              old_str: "line2",
              new_str: "replaced line",
            },
          ],
        },
        [],
        new AbortController().signal,
        "",
      );

      expect(result.isError).toBe(false);
      expect(result.text).toContain(
        `Note: Path was automatically corrected from '${shortPath}' to '${absolutePath}'.`,
      );
      expect(result.text).toContain("Successfully edited the file");
      expect(testWorkspace.getFileContent(fullPath)).toEqual(
        "line1\nreplaced line\nline3",
      );
    });

    it("should automatically correct path for insert command", async () => {
      // Set up a file with a path that will be found as a suffix match
      const shortPath = "tool-definition.ts";
      const fullPath =
        "clients/sidecar/libs/src/tools/sidecar-tools/str-replace-editor-tool/tool-definition.ts";
      const absolutePath = path.join(testWorkspace.rootPath, fullPath);
      const fileContents = "line1\nline2\nline3";

      // Set up the file in the test workspace
      testWorkspace.setFileContent(fullPath, fileContents);

      // Mock findFiles to return the full path when searching for the file
      (testWorkspace.findFiles as jest.Mock) = jest
        .fn()
        .mockResolvedValue([
          new QualifiedPathName(testWorkspace.rootPath, fullPath),
        ]);

      const result = await tool.call(
        {
          command: "insert",
          path: shortPath,
          insert_line_entries: [
            {
              insert_line: 1,
              new_str: "inserted line",
            },
          ],
        },
        [],
        new AbortController().signal,
        "",
      );

      expect(result.isError).toBe(false);
      expect(result.text).toContain(
        `Note: Path was automatically corrected from '${shortPath}' to '${absolutePath}'.`,
      );
      expect(result.text).toContain("Successfully edited the file");
      expect(testWorkspace.getFileContent(fullPath)).toEqual(
        "line1\ninserted line\nline2\nline3",
      );
    });

    it("should automatically correct path for view command", async () => {
      // Set up a file with a path that will be found as a suffix match
      const shortPath = "utils.ts";
      const fullPath =
        "clients/sidecar/libs/src/tools/sidecar-tools/str-replace-editor-tool/utils.ts";
      const absolutePath = path.join(testWorkspace.rootPath, fullPath);
      const fileContents = "line1\nline2\nline3\nline4\nline5";

      // Set up the file in the test workspace
      testWorkspace.setFileContent(fullPath, fileContents);

      // Mock findFiles to return the full path when searching for the file
      (testWorkspace.findFiles as jest.Mock) = jest
        .fn()
        .mockResolvedValue([
          new QualifiedPathName(testWorkspace.rootPath, fullPath),
        ]);

      const result = await tool.call(
        {
          command: "view",
          path: shortPath,
          view_range: [2, 4],
        },
        [],
        new AbortController().signal,
        "",
      );

      expect(result.isError).toBe(false);
      expect(result.text).toContain(
        `Note: Path was automatically corrected from '${shortPath}' to '${absolutePath}'.`,
      );
      expect(result.text).toContain("line2");
      expect(result.text).toContain("line3");
      expect(result.text).toContain("line4");
    });

    it("should not auto-correct absolute paths", async () => {
      const absolutePath = "/absolute/path/to/file.ts";

      // Mock findFiles to return a similar path that would match as a suffix
      (testWorkspace.findFiles as jest.Mock) = jest
        .fn()
        .mockResolvedValue([
          new QualifiedPathName("/workspace", "some/path/to/file.ts"),
        ]);

      const result = await tool.call(
        {
          command: "str_replace",
          path: absolutePath,
          str_replace_entries: [
            {
              old_str: "line2",
              new_str: "replaced line",
            },
          ],
        },
        [],
        new AbortController().signal,
        "",
      );

      expect(result.isError).toBe(true);
      expect(result.text).toContain(`File not found: ${absolutePath}`);
      expect(result.text).toContain("/workspace/some/path/to/file.ts");
      // Should not contain any auto-correction note
      expect(result.text).not.toContain("Path was automatically corrected");
    });

    it("should show suggestions when multiple paths match the suffix", async () => {
      const shortPath = "index.ts";

      // Mock findFiles to return multiple paths with the same filename
      (testWorkspace.findFiles as jest.Mock) = jest
        .fn()
        .mockResolvedValue([
          new QualifiedPathName("/workspace", "clients/vscode/index.ts"),
          new QualifiedPathName("/workspace", "services/api/index.ts"),
        ]);

      const result = await tool.call(
        {
          command: "str_replace",
          path: shortPath,
          str_replace_entries: [
            {
              old_str: "line2",
              new_str: "replaced line",
            },
          ],
        },
        [],
        new AbortController().signal,
        "",
      );

      expect(result.isError).toBe(true);
      expect(result.text).toContain("File not found: index.ts");
      expect(result.text).toContain("/workspace/clients/vscode/index.ts");
      expect(result.text).toContain("/workspace/services/api/index.ts");
    });
  });

  describe("attemptPathAutoCorrection", () => {
    it("should return corrected path when there's exactly one path with matching suffix", async () => {
      const originalPath = "utils.ts";

      // Mock findFiles to return a single path that matches the suffix
      (testWorkspace.findFiles as jest.Mock) = jest
        .fn()
        .mockResolvedValue([
          new QualifiedPathName(
            testWorkspace.rootPath,
            "clients/sidecar/libs/src/tools/sidecar-tools/str-replace-editor-tool/utils.ts",
          ),
        ]);

      const result = await attemptPathAutoCorrection(originalPath);

      expect(result.corrected).toBe(true);
      expect(result.correctedPath).toBe(
        path.join(
          testWorkspace.rootPath,
          "clients/sidecar/libs/src/tools/sidecar-tools/str-replace-editor-tool/utils.ts",
        ),
      );
      expect(result.correctionNote).toContain(
        `Note: Path was automatically corrected from '${originalPath}' to`,
      );
      expect(result.similarPaths).toHaveLength(1);
    });

    it("should not correct absolute paths", async () => {
      const absolutePath = "/absolute/path/to/file.ts";

      // Mock findFiles to return a similar path
      (testWorkspace.findFiles as jest.Mock) = jest
        .fn()
        .mockResolvedValue([
          new QualifiedPathName("/workspace", "some/path/to/file.ts"),
        ]);

      const result = await attemptPathAutoCorrection(absolutePath);

      expect(result.corrected).toBe(false);
      expect(result.correctedPath).toBeUndefined();
      expect(result.correctionNote).toBeUndefined();
      expect(result.similarPaths).toHaveLength(1);
    });

    it("should not correct when multiple paths match the suffix", async () => {
      const originalPath = "index.ts";

      // Mock findFiles to return multiple paths with the same filename
      (testWorkspace.findFiles as jest.Mock) = jest
        .fn()
        .mockResolvedValue([
          new QualifiedPathName("/workspace", "clients/vscode/index.ts"),
          new QualifiedPathName("/workspace", "services/api/index.ts"),
        ]);

      const result = await attemptPathAutoCorrection(originalPath);

      expect(result.corrected).toBe(false);
      expect(result.correctedPath).toBeUndefined();
      expect(result.correctionNote).toBeUndefined();
      expect(result.similarPaths).toHaveLength(2);
    });

    it("should return empty similar paths when no files found", async () => {
      const originalPath = "nonexistent.ts";

      // Mock findFiles to return no results
      (testWorkspace.findFiles as jest.Mock) = jest.fn().mockResolvedValue([]);

      const result = await attemptPathAutoCorrection(originalPath);

      expect(result.corrected).toBe(false);
      expect(result.correctedPath).toBeUndefined();
      expect(result.correctionNote).toBeUndefined();
      expect(result.similarPaths).toHaveLength(0);
    });
  });
});
