import { json, redirect } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import { useMutation, useQuery } from "@tanstack/react-query";
import { Box, Container } from "@radix-ui/themes";
import { userQueryOptions } from "../client-cache";
import { plans } from "../data/plans";
import { PlanPicker } from "../components/account/Billing/PlanPicker";
import { toast } from "../components/ui/Toast";
import { withAuth } from "../.server/auth";
import { getOrbCustomerInfo } from "../utils/orb.server";
import { AuthCentralClient } from "../.server/grpc/auth-central";
import { convertPlansToOptions } from "../utils/plan-converter";

export const loader = withAuth(
  async ({ user }) => {
    // Get user details from Auth Central
    const authCentralClient = AuthCentralClient.getInstance();
