	}

	// Generate a name-based UUID using SHA-256
	agentID := newSHA256UUID(remoteAgentUUIDNamespace, []byte(requestUUID.String()), []byte(opaqueUserID))

	return agentID.String(), nil
}

// stripInstructionFlags parses "instruction flags" of the form `_flags:{key[=value],...}` from text nodes
// AND removes them.
func stripInstructionFlags(nodes ...*chatproto.ChatRequestNode) map[string]string {
	iflags := map[string]string{}

	for _, node := range nodes {
		if node.Type != chatproto.ChatRequestNodeType_TEXT {
			continue
		}

		content := node.GetTextNode().GetContent()
		pfx := "_flags:{"

		if start := len(pfx) + strings.Index(content, pfx); start < len(pfx) {
			continue
		} else if end := start + strings.Index(content[start:], "}"); end < start {
			continue
		} else {
			// First split by commas.
			flags := strings.Split(content[start:end], ",")
