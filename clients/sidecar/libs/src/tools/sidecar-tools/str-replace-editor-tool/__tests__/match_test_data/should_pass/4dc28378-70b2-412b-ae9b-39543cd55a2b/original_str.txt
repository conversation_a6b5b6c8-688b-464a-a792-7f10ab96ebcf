	"google.golang.org/grpc/credentials"
	"google.golang.org/grpc/metadata"
)

type ChatArbiterClient interface {
	GetTarget(ctx context.Context, requestContext requestcontext.RequestContext) (*proto.GetTargetResponse, error)
}

type chatArbiterClientImpl struct {
	endpoint string
	stub     proto.ChatArbiterClient
}

func New(endpoint string, creds credentials.TransportCredentials) (ChatArbiterClient, error) {
	conn, err := grpc.Dial(endpoint,
		grpc.WithTransportCredentials(creds),
		grpc.WithStatsHandler(otelgrpc.NewClientHandler()),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to dial chat arbiter service: %w", err)
	}

	return &chatArbiterClientImpl{
		endpoint: endpoint,
		stub:     proto.NewChatArbiterClient(conn),
	}, nil
}

func (c *chatArbiterClientImpl) GetTarget(ctx context.Context, requestContext requestcontext.RequestContext) (*proto.GetTargetResponse, error) {
	ctx = metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())
