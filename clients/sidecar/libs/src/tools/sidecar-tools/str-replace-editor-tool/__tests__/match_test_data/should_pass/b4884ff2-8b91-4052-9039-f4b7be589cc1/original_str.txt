        "//services/integrations/github/processor/client:client_go",
        "//services/lib/grpc/auth:grpc_auth_go",
        "//services/lib/grpc/recovery:grpc_recovery_go",
        "//services/lib/grpc/tls_config:grpc_tls_config_go",
        "//services/lib/request_context:request_context_go",
        "//services/remote_agents:remote_agents_go_proto",
        "//services/remote_agents/server:remote_agent_entities_go_proto",
        "//services/remote_agents/server/ws",
        "//services/request_insight:request_insight_go_proto",
        "//services/request_insight/publisher:publisher_go",
        "//services/token_exchange/client:client_go",
        "@com_github_google_uuid//:uuid",
        "@com_github_grpc_ecosystem_go_grpc_middleware_providers_prometheus//:prometheus",
        "@com_github_prometheus_client_golang//prometheus",
        "@com_github_prometheus_client_golang//prometheus/promhttp",
        "@com_github_rs_zerolog//log",
        "@com_google_cloud_go_bigtable//apiv2/bigtablepb:go_default_library",
