    ) -> dict[str, object]:
        """Returns a row that can be written to the remote_agents_add_ssh_key_request table."""
        base_row = self._common_request_event_row(
            request_id,
            session_id,
            tenant_info,
            event,
            _sanitize_remote_agents_add_ssh_key_request(
                event.remote_agents_add_ssh_key_request
            ),
        )
        if event.remote_agents_add_ssh_key_request.HasField("request"):
            base_row["agent_id"] = (
                event.remote_agents_add_ssh_key_request.request.remote_agent_id
            )
        return base_row

    def _remote_agents_add_ssh_key_response_row(
        self,
        request_id: str,
        session_id: str,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.RequestEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the remote_agents_add_ssh_key_response table."""
        return self._common_request_event_row(
            request_id,
            session_id,
            tenant_info,
            event,
            request_insight_pb2.RemoteAgentsAddSSHKeyResponse(),
