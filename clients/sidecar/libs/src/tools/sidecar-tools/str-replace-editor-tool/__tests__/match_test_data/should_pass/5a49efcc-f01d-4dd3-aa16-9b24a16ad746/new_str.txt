// PutUserOnSubscription associates a user with an Orb subscription
// This is an async operation that will be handled by the auth central service
func (s *TeamManagementServer) PutUserOnSubscription(
	ctx context.Context, req *authpb.PutUserOnSubscriptionRequest,
) (*authpb.PutUserOnSubscriptionResponse, error) {
	if !s.teamManagementEnabled() {
		return nil, status.Error(codes.Internal, "Disabled")
	}

	// Check permissions - only allow admin users to perform this operation
	err := s.teamManagementAuthCheck(ctx, nil, &req.UserId, tokenexchangepb.Scope_AUTH_RW, true, "PutUserOnSubscription")
	if err != nil {
		return nil, err
	}

	// Validate the request
	if req.UserId == "" {
		return nil, status.Error(codes.InvalidArgument, "User ID is required")
	}
	if req.SubscriptionId == "" {
		return nil, status.Error(codes.InvalidArgument, "Subscription ID is required")
	}

	// Get the user to check if they exist and to get their current subscription info
	userDAO := s.daoFactory.GetUserDAO()
	user, err := userDAO.Get(ctx, req.UserId)
	if err != nil {
		log.Error().Err(err).Msgf("Failed to get user %s", req.UserId)
		return nil, status.Error(codes.Internal, "Failed to get user information")
	}
	if user == nil {
		log.Error().Msgf("User %s not found", req.UserId)
		return nil, status.Error(codes.NotFound, "User not found")
	}

	// Get the subscription to check if it exists
	subscriptionDAO := s.daoFactory.GetSubscriptionDAO()
	subscription, err := subscriptionDAO.Get(ctx, req.SubscriptionId)
	if err != nil {
		log.Error().Err(err).Msgf("Failed to get subscription %s", req.SubscriptionId)
		return nil, status.Error(codes.Internal, "Failed to get subscription information")
	}
	if subscription == nil {
		log.Error().Msgf("Subscription %s not found", req.SubscriptionId)
		return nil, status.Error(codes.NotFound, "Subscription not found")
	}

	// Check if the user already has a subscription
	needsNewSubscription := false
	if user.OrbSubscriptionId == "" {
		// User doesn't have a subscription, we need to create one
		needsNewSubscription = true
		log.Info().
			Str("user_id", req.UserId).
			Msg("User doesn't have a subscription, will create one")
	} else if user.OrbSubscriptionId != req.SubscriptionId {
		// User has a different subscription than the one requested
		log.Info().
			Str("user_id", req.UserId).
			Str("current_subscription_id", user.OrbSubscriptionId).
			Str("requested_subscription_id", req.SubscriptionId).
			Msg("User has a different subscription than requested")
	}

	// Get the user's current tenant to determine the appropriate plan
	if len(user.Tenants) == 0 {
		log.Error().Str("user_id", req.UserId).Msg("User doesn't belong to any tenant")
		return nil, status.Error(codes.FailedPrecondition, "User doesn't belong to any tenant")
	}

	// Get the current tenant
	currentTenantID := user.Tenants[0]
	currentTenant, err := s.tenantMap.GetTenantByID(currentTenantID)
	if err != nil {
		log.Error().Err(err).Str("tenant_id", currentTenantID).Msg("Failed to get tenant")
		return nil, status.Error(codes.Internal, "Failed to get tenant information")
	}
	if currentTenant == nil {
		log.Error().Str("tenant_id", currentTenantID).Msg("Tenant not found")
		return nil, status.Error(codes.NotFound, "Tenant not found")
	}

	// Get the subscription's plan ID
	targetPlanID := subscription.ExternalPlanId

	// Determine if we need to do a tier change based on the tenant tier and target plan
	needsTierChange := false
	var targetTier auth_entities.UserTier

	// Check if the user's current tenant tier matches the subscription's plan
	switch currentTenant.Tier {
	case tw_pb.TenantTier_COMMUNITY:
		if targetPlanID == s.orbConfig.ProfessionalPlanID || targetPlanID == s.orbConfig.TrialPlanID {
			// User is in community tier but subscription is for professional tier
			needsTierChange = true
			targetTier = auth_entities.UserTier_PROFESSIONAL
			log.Info().
				Str("user_id", req.UserId).
				Str("current_tenant_tier", currentTenant.Tier.String()).
				Str("target_plan_id", targetPlanID).
				Msg("User needs tier change from COMMUNITY to PROFESSIONAL")
		}
	case tw_pb.TenantTier_PROFESSIONAL:
		if targetPlanID == s.orbConfig.CommunityPlanID {
			// User is in professional tier but subscription is for community tier
			needsTierChange = true
			targetTier = auth_entities.UserTier_COMMUNITY
			log.Info().
				Str("user_id", req.UserId).
				Str("current_tenant_tier", currentTenant.Tier.String()).
				Str("target_plan_id", targetPlanID).
				Msg("User needs tier change from PROFESSIONAL to COMMUNITY")
		}
	case tw_pb.TenantTier_ENTERPRISE:
		log.Error().Str("user_id", req.UserId).Msg("Enterprise users cannot change subscriptions through this endpoint")
		return nil, status.Error(codes.FailedPrecondition, "Enterprise users cannot change subscriptions through this endpoint")
	default:
		log.Error().Str("user_id", req.UserId).Str("tenant_tier", currentTenant.Tier.String()).Msg("Unknown tenant tier")
		return nil, status.Error(codes.Internal, "Unknown tenant tier")
	}

	// Special case: If user is going from trial to developer plan, we don't need a tier change
	if currentTenant.Tier == tw_pb.TenantTier_PROFESSIONAL &&
	   user.OrbSubscriptionId != "" &&
	   targetPlanID == s.orbConfig.ProfessionalPlanID {
		// Get the current subscription to check if it's a trial
		currentSubscription, err := subscriptionDAO.Get(ctx, user.OrbSubscriptionId)
		if err != nil {
			log.Error().Err(err).Str("subscription_id", user.OrbSubscriptionId).Msg("Failed to get current subscription")
			return nil, status.Error(codes.Internal, "Failed to get current subscription information")
		}
		if currentSubscription != nil && currentSubscription.ExternalPlanId == s.orbConfig.TrialPlanID {
			// User is going from trial to developer plan, no tier change needed
			needsTierChange = false
			log.Info().
				Str("user_id", req.UserId).
				Str("current_plan", s.orbConfig.TrialPlanID).
				Str("target_plan", s.orbConfig.ProfessionalPlanID).
				Msg("User is going from trial to developer plan, no tier change needed")
		}
	}

	// Generate a unique operation ID
	operationID := uuid.New().String()
	log.Info().
		Str("operation_id", operationID).
		Str("user_id", req.UserId).
		Str("subscription_id", req.SubscriptionId).
		Bool("needs_new_subscription", needsNewSubscription).
		Bool("needs_tier_change", needsTierChange).
		Msg("Creating put user on subscription operation")

	// If the user needs a tier change, initiate that process
	if needsTierChange {
		// Check if a tier change is already in progress
		if user.TierChange != nil {
			log.Error().
				Str("user_id", req.UserId).
				Str("tier_change_id", user.TierChange.Id).
				Str("current_target_tier", user.TierChange.TargetTier.String()).
				Msg("User already has a tier change in progress")
			return nil, status.Errorf(codes.FailedPrecondition,
				"User already has a tier change in progress: user_id=%s, tier_change_id=%s, current_target_tier=%s",
				user.Id, user.TierChange.Id, user.TierChange.TargetTier)
		}

		// Find the appropriate tenant for the target tier
		var newTenant *tw_pb.Tenant
		for _, tenantID := range user.Tenants {
			if tenantID == currentTenantID {
				continue // Skip current tenant
			}
			tenant, err := s.tenantMap.GetTenantByID(tenantID)
			if err != nil {
				log.Warn().Err(err).Str("tenant_id", tenantID).Msg("Failed to get tenant")
				continue
			}
			if tenant == nil {
				continue
			}

			// Check if this tenant matches the target tier
			if (targetTier == auth_entities.UserTier_PROFESSIONAL && tenant.Tier == tw_pb.TenantTier_PROFESSIONAL) ||
				(targetTier == auth_entities.UserTier_COMMUNITY && tenant.Tier == tw_pb.TenantTier_COMMUNITY) {
				newTenant = tenant
				break
			}
		}

		if newTenant == nil {
			log.Error().
				Str("user_id", req.UserId).
				Str("target_tier", targetTier.String()).
				Msg("No suitable tenant found for tier change")
			return nil, status.Error(codes.FailedPrecondition, "No suitable tenant found for tier change")
		}

		// Construct the UserTierChangeMessage
		tierChangeID := uuid.New().String()
		tierChangeMsg := &auth_internal.UserTierChangeMessage{
			User:          user,
			CurrentTenant: currentTenant,
			NewTenant:     newTenant,
			NewTier:       targetTier,
			TierChangeId:  tierChangeID,
			PublishTime:   timestamppb.Now(),
		}

		// Publish the tier change message
		if err := s.asyncOpsPublisher.PublishUserTierChange(ctx, tierChangeMsg); err != nil {
			log.Error().
				Err(err).
				Str("user_id", user.Id).
				Str("tier_change_id", tierChangeID).
				Str("old_tenant", currentTenant.Name).
				Str("new_tenant", newTenant.Name).
				Str("tier", targetTier.String()).
				Msg("Failed to publish tier change message")
			return nil, status.Errorf(codes.Internal,
				"Failed to publish user tier change: user_id=%s, tier_change_id=%s, old_tenant=%s, new_tenant=%s, tier=%s: %v",
				user.Id, tierChangeID, currentTenant.Name, newTenant.Name, targetTier.String(), err)
		}

		// Update the user with the tier change info
		updateUser := func(u *auth_entities.User) bool {
			// If the user already has a tier change in progress, don't update
			if u.TierChange != nil {
				log.Warn().
					Str("user_id", user.Id).
					Str("existing_tier_change_id", u.TierChange.Id).
					Str("new_tier_change_id", tierChangeID).
					Msg("User already has a tier change in progress during update")
				// Return false to indicate no changes were made
				return false
			}

			// Only set the tier change if it wasn't already set
			u.TierChange = &auth_entities.User_TierChangeInfo{
				Id:         tierChangeID,
				TargetTier: targetTier,
			}
			return true
		}

		_, err = userDAO.TryUpdate(ctx, req.UserId, updateUser, DefaultRetry)
		if err != nil {
			log.Error().
				Err(err).
				Str("user_id", user.Id).
				Str("tier_change_id", tierChangeID).
				Msg("Failed to update user with tier change info")
			return nil, status.Errorf(codes.Internal,
				"Failed to update user with tier change info: user_id=%s, tier_change_id=%s: %v",
				user.Id, tierChangeID, err)
		}

		log.Info().
			Str("user_id", user.Id).
			Str("tier_change_id", tierChangeID).
			Str("old_tenant", currentTenant.Name).
			Str("new_tenant", newTenant.Name).
			Str("new_tier", targetTier.String()).
			Msg("User tier change successfully initiated")
	} else if needsNewSubscription || user.OrbSubscriptionId != req.SubscriptionId {
		// Create and publish the message for async processing
		msg := &auth_internal.PutUserOnSubscriptionMessage{
			Id:             operationID,
			UserId:         req.UserId,
			SubscriptionId: req.SubscriptionId,
			PublishTime:    timestamppb.Now(),
		}

		err = s.asyncOpsPublisher.PublishPutUserOnSubscription(ctx, msg)
		if err != nil {
			log.Error().Err(err).Msgf("Failed to publish put user on subscription message for id %s", operationID)
			return nil, status.Error(codes.Internal, "Failed to publish put user on subscription message")
		}

		log.Info().
			Str("operation_id", operationID).
			Str("user_id", req.UserId).
			Str("subscription_id", req.SubscriptionId).
			Msg("Successfully published put user on subscription message")
	} else {
		log.Info().
			Str("user_id", req.UserId).
			Str("subscription_id", req.SubscriptionId).
			Msg("User already has the requested subscription, no action needed")
	}

	return &authpb.PutUserOnSubscriptionResponse{}, nil
}
