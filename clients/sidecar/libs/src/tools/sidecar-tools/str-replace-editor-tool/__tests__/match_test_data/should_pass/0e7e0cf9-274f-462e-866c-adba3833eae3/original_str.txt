package client

import (
	"context"

	proto "github.com/augmentcode/augment/services/chat_arbiter/proto"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
)

type MockChatArbiterClient struct {
	GetTargetFunc func(ctx context.Context, requestContext requestcontext.RequestContext) (*proto.GetTargetResponse, error)
}

func (m *MockChatArbiterClient) GetTarget(ctx context.Context, requestContext requestcontext.RequestContext) (*proto.GetTargetResponse, error) {
	if m.GetTargetFunc != nil {
		return m.GetTargetFunc(ctx, requestContext)
	}
	return &proto.GetTargetResponse{
		Region:  "",
		Service: "",
	}, nil
}
