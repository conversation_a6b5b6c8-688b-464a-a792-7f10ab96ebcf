export default function SelectPlanPage() {
  const { message } = useLoaderData<typeof loader>();
  const { data: userData } = useQuery(userQueryOptions);

  // Mutation for changing the user's plan
  const switchPlanMutation = useMutation({
    mutationFn: async (newPlan: string) => {
      const formData = new FormData();
      formData.append("action", "switchPlan");
      formData.append("newPlan", newPlan);

      const response = await fetch("/api/user", {
        method: "POST",
        body: formData,
      });

      if (!response.ok) {
        throw new Error("Failed to switch plan");
      }

      return response.json();
    },
    onSuccess: () => {
      toast({
        title: "Plan selected",
        description: "Your plan has been updated successfully.",
        status: "success",
      });
    },
    onError: () => {
      toast({
        title: "Error",
        description: "Failed to update your plan. Please try again.",
        status: "error",
      });
    },
  });

  const handleSelectPlan = (planId: string) => {
    // Only allow community or developer plans
    if (planId === "community" || planId === "developer") {
      switchPlanMutation.mutate(planId);
    }
  };

  // Convert plans to the format expected by PlanPicker
  const planOptions = convertPlansToOptions(plans);

  return (
    <Container size="3" style={{ maxWidth: "800px", margin: "0 auto", padding: "40px 0" }}>
      <Box style={{ padding: "24px" }}>
        <PlanPicker
          plans={planOptions}
          currentPlanId={userData?.plan.name}
          onSelectPlan={handleSelectPlan}
          title="Select a Plan"
          description="Choose the plan that best fits your needs"
          showMessage={true}
          message={message}
        />
      </Box>
    </Container>
  );
}
