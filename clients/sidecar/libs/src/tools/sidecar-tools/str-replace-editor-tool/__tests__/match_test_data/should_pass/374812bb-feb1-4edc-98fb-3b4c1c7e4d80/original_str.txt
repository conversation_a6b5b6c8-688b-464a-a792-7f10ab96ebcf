import { AuthCentralClient } from "../.server/grpc/auth-central";
import { mapPlanNameToUserTier } from "../utils/subscription.server";
import { convertPlansToOptions } from "../utils/plan-converter";

export const loader = withAuth(
  async ({ request, user }: LoaderFunctionArgs) => {
    // Get user details from Auth Central
    const authCentralClient = AuthCentralClient.getInstance();
    const userResponse = await authCentralClient.getUser(user);
    const orbCustomerId = userResponse.user?.orbCustomerId;

    // If no Orb customer ID, we can't check subscription status
    if (!orbCustomerId) {
      return json({
        message: "You need to select a plan to continue using Augment",
        hasExpiredPlan: false,
      });
    }

    // Get customer info from Orb
    const customerInfo = await getOrbCustomerInfo(orbCustomerId);

    // If no customer info, we can't check subscription status
    if (!customerInfo) {
      return json({
        message: "You need to select a plan to continue using Augment",
        hasExpiredPlan: false,
      });
    }

    // Check if the subscription has an end date and if it's in the past
    const hasExpiredPlan = customerInfo.subscriptionEndDate
      ? new Date(customerInfo.subscriptionEndDate) < new Date()
      : false;

    // If the user has an active subscription, redirect to the account page
    if (customerInfo.billingPeriodEnd && !hasExpiredPlan) {
      return redirect("/account/plan");
    }

    // Otherwise, show the plan picker with a message
    const message = hasExpiredPlan
      ? "Your subscription has expired. Please select a plan to continue using Augment."
      : "You need to select a plan to continue using Augment";

    return json({ message, hasExpiredPlan });
  },
  {
    adminOnly: false,
  },
);

export default function SelectPlanPage() {
  const { message, hasExpiredPlan } = useLoaderData<typeof loader>();
  const { data: userData } = useQuery(userQueryOptions);
