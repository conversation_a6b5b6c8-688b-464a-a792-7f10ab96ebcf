import { fuzzyMatchReplacementStrings, MatchFailReason } from "../match-utils";

describe("fuzzyMatchReplacementStrings", () => {
  // Tests for conditions that should return undefined

  it("should return FirstSymbolOfOldStrNotInOriginal when first symbol doesn't match", () => {
    const oriStr = "a b";
    const oldStr = "x b";
    const newStr = "x b";

    const result = fuzzyMatchReplacementStrings(
      oriStr,
      oldStr,
      newStr,
      5,
      0.5,
      1,
    );

    expect(result).toEqual(MatchFailReason.FirstSymbolOfOldStrNotInOriginal);
  });

  it("should return LastSymbolOfOldStrNotInOriginal when last symbol doesn't match", () => {
    const oriStr = "a b c";
    const oldStr = "a b x";
    const newStr = "a b x";

    const result = fuzzyMatchReplacementStrings(
      oriStr,
      oldStr,
      newStr,
      5,
      0.5,
      1,
    );

    expect(result).toEqual(MatchFailReason.LastSymbolOfOldStrNotInOriginal);
  });

  it("should return ExceedsMaxDiff when differences exceed maxDiff", () => {
    const oriStr = "a b c d e f g h";
    const oldStr = "a b c f g h"; // missing 'de'
    const newStr = "a c f g h"; // removed 'b'

    const result = fuzzyMatchReplacementStrings(
      oriStr,
      oldStr,
      newStr,
      1,
      0.5,
      1,
    );

    expect(result).toEqual(MatchFailReason.ExceedsMaxDiff);
  });

  it("should return ExceedsMaxDiffRatio when differences exceed maxDiffRatio", () => {
    const oriStr = "a b c d e f g h";
    const oldStr = "a b c f g h"; // missing 'd e'
    const newStr = "a c f g h"; // removed 'b'

    const result = fuzzyMatchReplacementStrings(
      oriStr,
      oldStr,
      newStr,
      5,
      0.01,
      1,
    );

    expect(result).toEqual(MatchFailReason.ExceedsMaxDiffRatio);
  });

  it("should return SymbolInOldNotInOriginalOrNew when symbols in old are not in original or new", () => {
    const oriStr = "abc xyz";
    const oldStr = "abc q xyz"; // 'q' is not in original or new
    const newStr = "abc xyz";

    const result = fuzzyMatchReplacementStrings(
      oriStr,
      oldStr,
      newStr,
      5,
      0.5,
      1,
    );

    expect(result).toEqual(MatchFailReason.SymbolInOldNotInOriginalOrNew);
  });

  it("should return AmbiguousReplacement for cases where there are overlapping differences between old vs new and old vs original. New str diff first", () => {
    const oriStr = "a b+c";
    const oldStr = "a b-c"; // '-' doesn't match original
    const newStr = "a B-c"; // 'B' is capitalized in new

    const result = fuzzyMatchReplacementStrings(
      oriStr,
      oldStr,
      newStr,
      5,
      0.5,
      1,
    );

    expect(result).toEqual(MatchFailReason.AmbiguousReplacement);
  });

  it("should return AmbiguousReplacement for cases where there are overlapping differences between old vs new and old vs original. Original str diff first", () => {
    const oriStr = "a b-c";
    const oldStr = "a B-c"; // 'B' doesn't match original
    const newStr = "a B+c"; // '+' in new

    const result = fuzzyMatchReplacementStrings(
      oriStr,
      oldStr,
      newStr,
      5,
      0.5,
      1,
    );

    expect(result).toEqual(MatchFailReason.AmbiguousReplacement);
  });

  it("should return AmbiguousReplacement for cases where there are extra chars in original and new in the same gap", () => {
    const oriStr = "a o b"; // extra 'o'
    const oldStr = "a b";
    const newStr = "a n b"; // extra 'n'

    const result = fuzzyMatchReplacementStrings(
      oriStr,
      oldStr,
      newStr,
      5,
      0.5,
      1,
    );

    expect(result).toEqual(MatchFailReason.AmbiguousReplacement);
  });

  it("should return AmbiguousReplacement for cases where match streak between diffs is too short. Original diff first", () => {
    const oriStr = "a _ c d e"; // missing b
    const oldStr = "a b c d e";
    const newStr = "a b c D e"; // capitalized D

    // match streak between diffs is 3 (including spaces)
    // setting minAllMatchStreakBetweenDiffs to 4 should fail
    const result = fuzzyMatchReplacementStrings(
      oriStr,
      oldStr,
      newStr,
      5,
      0.5,
      4,
    );

    expect(result).toEqual(MatchFailReason.AmbiguousReplacement);
  });

  it("should return AmbiguousReplacement for cases where match streak between diffs is too short. New diff first", () => {
    const oriStr = "a b c _ e"; // missing d
    const oldStr = "a b c d e";
    const newStr = "a B c d e"; // capitalized b

    // match streak between diffs is 3(including spaces)
    // setting minAllMatchStreakBetweenDiffs to 4 should fail
    const result = fuzzyMatchReplacementStrings(
      oriStr,
      oldStr,
      newStr,
      5,
      0.5,
      4,
    );

    expect(result).toEqual(MatchFailReason.AmbiguousReplacement);
  });

  it("should return AmbiguousReplacement for cases where match streak between diffs is too short. Extra symbol. Original diff first", () => {
    const oriStr = "a b _ c d e"; // extra _ before c
    const oldStr = "a b c d e";
    const newStr = "a b c - d e"; // extra - before d

    // match streak between diffs is 2(including spaces)
    // setting minAllMatchStreakBetweenDiffs to 3 should fail
    const result = fuzzyMatchReplacementStrings(
      oriStr,
      oldStr,
      newStr,
      5,
      0.5,
      3,
    );

    expect(result).toEqual(MatchFailReason.AmbiguousReplacement);
  });

  it("should return AmbiguousReplacement for cases where match streak between diffs is too short. Extra symbol. New diff first", () => {
    const oriStr = "a b c _ d e"; // extra _ before d
    const oldStr = "a b c d e";
    const newStr = "a b - c d e"; // extra - before c

    // match streak between diffs is 2(including spaces)
    // setting minAllMatchStreakBetweenDiffs to 3 should fail
    const result = fuzzyMatchReplacementStrings(
      oriStr,
      oldStr,
      newStr,
      5,
      0.5,
      3,
    );

    expect(result).toEqual(MatchFailReason.AmbiguousReplacement);
  });

  // Tests for successful cases

  it("should handle successful case with extra suffix in original", () => {
    const oriStr = "function foo() {\n  return 42;\n}\n// Extra comment";
    const oldStr = "function foo() {\n  return 42;\n}";
    const newStr = "function foo() {\n  return 43;\n}";

    const result = fuzzyMatchReplacementStrings(
      oriStr,
      oldStr,
      newStr,
      0,
      0,
      1,
    );

    expect(result).toEqual({
      oldStr: "function foo() {\n  return 42;\n}",
      newStr: "function foo() {\n  return 43;\n}",
    });
  });

  it("should handle successful case with extra prefix in original", () => {
    const oriStr = "// Extra comment before\nfunction foo() {\n  return 42;\n}";
    const oldStr = "function foo() {\n  return 42;\n}";
    const newStr = "function foo() {\n  return 43;\n}";

    const result = fuzzyMatchReplacementStrings(
      oriStr,
      oldStr,
      newStr,
      0,
      0,
      1,
    );

    expect(result).toEqual({
      oldStr: "function foo() {\n  return 42;\n}",
      newStr: "function foo() {\n  return 43;\n}",
    });
  });

  it("should handle successful case with extra prefix and suffix in original", () => {
    const oriStr =
      "// Extra comment before\nfunction foo() {\n  return 42;\n}\n// Extra comment after";
    const oldStr = "function foo() {\n  return 42;\n}";
    const newStr = "function foo() {\n  return 43;\n}";

    const result = fuzzyMatchReplacementStrings(
      oriStr,
      oldStr,
      newStr,
      0,
      0,
      1,
    );

    expect(result).toEqual({
      oldStr: "function foo() {\n  return 42;\n}",
      newStr: "function foo() {\n  return 43;\n}",
    });
  });

  it("should handle successful case with extra content in the middle of original", () => {
    const oriStr = "function foo() {\n// Extra comment\n  return 42;\n}";
    const oldStr = "function foo() {\n  return 42;\n}";
    const newStr = "function foo() {\n  return 43;\n}";

    const result = fuzzyMatchReplacementStrings(
      oriStr,
      oldStr,
      newStr,
      20,
      0.9,
      1,
    );

    expect(result).toEqual({
      oldStr: "function foo() {\n// Extra comment\n  return 42;\n}",
      newStr: "function foo() {\n// Extra comment\n  return 43;\n}",
    });
  });

  it("should handle successful case with trailing content in newStr", () => {
    const oriStr = "function foo() {\n  return 42;\n}";
    const oldStr = "function foo() {\n  return 42;\n}";
    const newStr =
      "function foo() {\n  return 42;\n}\n// Extra trailing content";

    const result = fuzzyMatchReplacementStrings(
      oriStr,
      oldStr,
      newStr,
      5,
      0.5,
      1,
    );

    expect(result).toEqual({
      oldStr: "function foo() {\n  return 42;\n}",
      newStr: "function foo() {\n  return 42;\n}\n// Extra trailing content",
    });
  });

  it("should handle successful case with leading content in newStr", () => {
    const oriStr = "function foo() {\n  return 42;\n}";
    const oldStr = "function foo() {\n  return 42;\n}";
    const newStr = "// Extra comment before\nfunction foo() {\n  return 42;\n}";

    const result = fuzzyMatchReplacementStrings(
      oriStr,
      oldStr,
      newStr,
      5,
      0.5,
      1,
    );

    expect(result).toEqual({
      oldStr: "function foo() {\n  return 42;\n}",
      newStr: "// Extra comment before\nfunction foo() {\n  return 42;\n}",
    });
  });

  it("should handle successful case with both leading and trailing content in newStr", () => {
    const oriStr = "function foo() {\n  return 42;\n}";
    const oldStr = "function foo() {\n  return 42;\n}";
    const newStr =
      "// Extra comment before\nfunction foo() {\n  return 42;\n}\n// Extra comment after";

    const result = fuzzyMatchReplacementStrings(
      oriStr,
      oldStr,
      newStr,
      5,
      0.5,
      1,
    );

    expect(result).toEqual({
      oldStr: "function foo() {\n  return 42;\n}",
      newStr:
        "// Extra comment before\nfunction foo() {\n  return 42;\n}\n// Extra comment after",
    });
  });

  it("should handle successful case with extra content in the middle of original and new", () => {
    const oriStr =
      "function calculateTotal(items, discount) {\n  return items.reduce((sum, item) => sum + item.price, 0);\n}";
    const oldStr =
      "function calculateTotal(items) {\n  return items.reduce((sum, item) => sum + item.price, 0);\n}";
    const newStr =
      "function calculateTotal(items) {\n  const total = items.reduce((sum, item) => sum + item.price, 0);\n  return total;\n}";

    const result = fuzzyMatchReplacementStrings(
      oriStr,
      oldStr,
      newStr,
      10,
      0.5,
      1,
    );

    expect(result).toEqual({
      oldStr:
        "function calculateTotal(items, discount) {\n  return items.reduce((sum, item) => sum + item.price, 0);\n}",
      newStr:
        "function calculateTotal(items, discount) {\n  const total = items.reduce((sum, item) => sum + item.price, 0);\n  return total;\n}",
    });
  });
});
