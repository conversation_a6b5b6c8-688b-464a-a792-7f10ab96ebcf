// PutUserOnSubscription associates a user with an Orb subscription
// This is an async operation that will be handled by the auth central service
func (s *TeamManagementServer) PutUserOnSubscription(
	ctx context.Context, req *authpb.PutUserOnSubscriptionRequest,
) (*authpb.PutUserOnSubscriptionResponse, error) {
	if !s.teamManagementEnabled() {
		return nil, status.Error(codes.Internal, "Disabled")
	}

	// Check permissions - only allow admin users to perform this operation
	err := s.teamManagementAuthCheck(ctx, nil, &req.UserId, tokenexchangepb.Scope_AUTH_RW, true, "PutUserOnSubscription")
	if err != nil {
		return nil, err
	}

	// Validate the request
	if req.UserId == "" {
		return nil, status.Error(codes.InvalidArgument, "User ID is required")
	}
	if req.SubscriptionId == "" {
		return nil, status.Error(codes.InvalidArgument, "Subscription ID is required")
	}

	// Get the user to check if they exist and to get their current subscription info
	userDAO := s.daoFactory.GetUserDAO()
	user, err := userDAO.Get(ctx, req.UserId)
	if err != nil {
		log.Error().Err(err).Msgf("Failed to get user %s", req.UserId)
		return nil, status.Error(codes.Internal, "Failed to get user information")
	}
	if user == nil {
		log.Error().Msgf("User %s not found", req.UserId)
		return nil, status.Error(codes.NotFound, "User not found")
	}

	// Get the subscription to check if it exists
	subscriptionDAO := s.daoFactory.GetSubscriptionDAO()
	subscription, err := subscriptionDAO.Get(ctx, req.SubscriptionId)
	if err != nil {
		log.Error().Err(err).Msgf("Failed to get subscription %s", req.SubscriptionId)
		return nil, status.Error(codes.Internal, "Failed to get subscription information")
	}
	if subscription == nil {
		log.Error().Msgf("Subscription %s not found", req.SubscriptionId)
		return nil, status.Error(codes.NotFound, "Subscription not found")
	}

	// Generate a unique operation ID
	operationID := uuid.New().String()
	log.Info().
		Str("operation_id", operationID).
		Str("user_id", req.UserId).
		Str("subscription_id", req.SubscriptionId).
		Msg("Creating put user on subscription operation")

	// Create and publish the message for async processing
	msg := &auth_internal.PutUserOnSubscriptionMessage{
		Id:             operationID,
		UserId:         req.UserId,
		SubscriptionId: req.SubscriptionId,
		PublishTime:    timestamppb.Now(),
	}

	err = s.asyncOpsPublisher.PublishPutUserOnSubscription(ctx, msg)
	if err != nil {
		log.Error().Err(err).Msgf("Failed to publish put user on subscription message for id %s", operationID)
		return nil, status.Error(codes.Internal, "Failed to publish put user on subscription message")
	}

	log.Info().
		Str("operation_id", operationID).
		Str("user_id", req.UserId).
		Str("subscription_id", req.SubscriptionId).
		Msg("Successfully published put user on subscription message")

	return &authpb.PutUserOnSubscriptionResponse{}, nil
}
