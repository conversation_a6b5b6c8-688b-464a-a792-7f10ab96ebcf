from services.chat_arbiter import chat_arbiter_pb2
from services.lib.request_context.request_context import RequestContext


class MockChatArbiterClient:
    """Mock implementation of the Chat Arbiter client for testing."""

    def __init__(self):
        self.get_target_response = chat_arbiter_pb2.GetTargetResponse(
            region="",
            service="",
        )

    def get_target(
        self,
        request_context: RequestContext,  # pylint: disable=unused-argument
    ) -> chat_arbiter_pb2.GetTargetResponse:
        """Mock implementation of get_target."""
        return self.get_target_response
