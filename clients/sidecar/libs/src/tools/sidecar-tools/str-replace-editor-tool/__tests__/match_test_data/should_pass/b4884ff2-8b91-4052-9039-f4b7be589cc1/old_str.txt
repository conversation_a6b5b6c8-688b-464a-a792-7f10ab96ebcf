        "//services/remote_agents/server/ws",
        "//services/request_insight/proto",
        "//services/request_insight/publisher",
        "//services/token_exchange/client",
        "@com_github_google_uuid//:uuid",
        "@com_github_grpc_ecosystem_go_grpc_middleware_providers_prometheus//:prometheus",
        "@com_github_prometheus_client_golang//prometheus",
        "@com_github_prometheus_client_golang//prometheus/promhttp",
