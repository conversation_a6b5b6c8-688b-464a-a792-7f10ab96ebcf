import { StrReplaceEditorToolDefinitionNested } from "../tool-definition-nested";

// Only mock the logging module
jest.mock("@augment-internal/sidecar-libs/src/logging");

describe("StrReplaceEditorToolDefinitionNested", () => {
  let toolDefinition: StrReplaceEditorToolDefinitionNested;

  beforeEach(() => {
    jest.clearAllMocks();
    toolDefinition = new StrReplaceEditorToolDefinitionNested();
  });

  describe("extractStrReplaceEntries", () => {
    it("should extract and validate str_replace entries from nested schema", () => {
      // Test input with nested schema format
      const mockEntries = [
        {
          old_str: "old content 1",
          new_str: "new content 1",
          old_str_start_line_number: 5,
          old_str_end_line_number: 10,
        },
        {
          old_str: "old content 2",
          new_str: "new content 2",
          old_str_start_line_number: 15,
          old_str_end_line_number: 20,
        },
      ];

      const toolInput = {
        str_replace_entries: mockEntries,
      };

      // Execute
      const result = toolDefinition.extractStrReplaceEntries(toolInput);

      // Verify
      expect(result).toHaveLength(2);
      expect(result[0]).toEqual({
        old_str: "old content 1",
        new_str: "new content 1",
        old_str_start_line_number: 5,
        old_str_end_line_number: 10,
        index: 0,
      });
      expect(result[1]).toEqual({
        old_str: "old content 2",
        new_str: "new content 2",
        old_str_start_line_number: 15,
        old_str_end_line_number: 20,
        index: 1,
      });
    });

    it("should throw error for empty array", () => {
      // Test input with empty array
      const toolInput = {
        str_replace_entries: [],
      };

      // Execute & Verify
      expect(() => toolDefinition.extractStrReplaceEntries(toolInput)).toThrow(
        "Empty required parameter `str_replace_entries` for `str_replace` command.",
      );
    });

    it("should handle missing required fields", () => {
      // Test input with missing required fields
      const toolInput = {
        str_replace_entries: [
          {
            // Missing old_str
            new_str: "new content 1",
            old_str_start_line_number: 5,
            old_str_end_line_number: 10,
          },
          {
            old_str: "old content 2",
            // Missing new_str
            old_str_start_line_number: 15,
            old_str_end_line_number: 20,
          },
        ],
      };

      // Execute & Verify
      expect(() =>
        toolDefinition.extractStrReplaceEntries(toolInput),
      ).toThrow();
    });

    it("should handle invalid line numbers", () => {
      // Test input with invalid line numbers
      const toolInput = {
        str_replace_entries: [
          {
            old_str: "old content 1",
            new_str: "new content 1",
            old_str_start_line_number: "5" as unknown as number, // Invalid type
            old_str_end_line_number: 10,
          },
        ],
      };

      // Execute & Verify
      expect(() =>
        toolDefinition.extractStrReplaceEntries(toolInput),
      ).toThrow();
    });

    it("should throw error for missing str_replace_entries", () => {
      // Test input without str_replace_entries
      const toolInput = {};

      // Execute & Verify
      expect(() => toolDefinition.extractStrReplaceEntries(toolInput)).toThrow(
        "Missing required parameter `str_replace_entries` for `str_replace` command.",
      );
    });
  });

  describe("extractInsertLineEntries", () => {
    it("should extract and validate insert_line entries from nested schema", () => {
      // Test input with nested schema format
      const mockEntries = [
        { insert_line: 5, new_str: "new content 1" },
        { insert_line: 10, new_str: "new content 2" },
      ];

      const toolInput = {
        insert_line_entries: mockEntries,
      };

      // Execute
      const result = toolDefinition.extractInsertLineEntries(toolInput);

      // Verify
      expect(result).toHaveLength(2);
      expect(result[0]).toEqual({
        insert_line: 5,
        new_str: "new content 1",
        index: 0,
      });
      expect(result[1]).toEqual({
        insert_line: 10,
        new_str: "new content 2",
        index: 1,
      });
    });

    it("should throw error for empty array", () => {
      // Test input with empty array
      const toolInput = {
        insert_line_entries: [],
      };

      // Execute & Verify
      expect(() => toolDefinition.extractInsertLineEntries(toolInput)).toThrow(
        "Empty required parameter `insert_line_entries` for `insert` command.",
      );
    });

    it("should handle missing required fields", () => {
      // Test input with missing required fields
      const toolInput = {
        insert_line_entries: [
          {
            // Missing insert_line
            new_str: "new content 1",
          },
          {
            insert_line: 10,
            // Missing new_str
          },
        ],
      };

      // Execute & Verify
      expect(() =>
        toolDefinition.extractInsertLineEntries(toolInput),
      ).toThrow();
    });

    it("should handle invalid line numbers", () => {
      // Test input with invalid line numbers
      const toolInput = {
        insert_line_entries: [
          {
            insert_line: "5" as unknown as number, // Invalid type
            new_str: "new content 1",
          },
        ],
      };

      // Execute & Verify
      expect(() =>
        toolDefinition.extractInsertLineEntries(toolInput),
      ).toThrow();
    });

    it("should throw error for missing insert_line_entries", () => {
      // Test input without insert_line_entries
      const toolInput = {};

      // Execute & Verify
      expect(() => toolDefinition.extractInsertLineEntries(toolInput)).toThrow(
        "Missing required parameter `insert_line_entries` for `insert` command.",
      );
    });
  });

  describe("description and inputSchemaJson", () => {
    it("should have a non-empty description", () => {
      expect(toolDefinition.description).toBeDefined();
      expect(typeof toolDefinition.description).toBe("string");
      expect(toolDefinition.description.length).toBeGreaterThan(0);
    });

    it("should have a valid JSON schema", () => {
      expect(toolDefinition.inputSchemaJson).toBeDefined();
      expect(typeof toolDefinition.inputSchemaJson).toBe("string");

      // Should be parseable as JSON
      const schema = JSON.parse(toolDefinition.inputSchemaJson) as {
        type: string;
        properties: Record<string, unknown>;
      };
      expect(schema.type).toBe("object");
      expect(schema.properties).toBeDefined();
      expect(schema.properties.command).toBeDefined();
      expect(schema.properties.path).toBeDefined();
      expect(schema.properties.str_replace_entries).toBeDefined();
      expect(schema.properties.insert_line_entries).toBeDefined();
    });
  });
});
