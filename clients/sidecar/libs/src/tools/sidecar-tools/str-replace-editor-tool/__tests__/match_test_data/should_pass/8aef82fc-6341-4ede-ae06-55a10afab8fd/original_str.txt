                    # Yield the turn limit response
                    yield turn_limit_response
                    return

                # We will actually call model. Determine if this is a user message
                is_user_message = True
                for node in request.nodes or []:
                    if node.type == chat_pb2.ChatRequestNodeType.TOOL_RESULT:
                        is_user_message = False
                        break

                # Emit a UserMessage event if this is a user message
                if is_user_message:
                    # Create a request event
                    user_message_event = new_event()

                    # Determine if this is an agent chat based on agent_memories
                    is_agent_chat = request.HasField("agent_memories") and request.agent_memories
