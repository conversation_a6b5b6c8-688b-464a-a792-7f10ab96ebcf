    def _daily_request_limit_exceeded_row(
        self,
        request_id: str,
        session_id: str,
        tenant_info: request_insight_pb2.TenantInfo,
        event: request_insight_pb2.RequestEvent,
    ) -> dict[str, object]:
        """Returns a row that can be written to the daily_request_limit_exceeded table."""
        daily_request_limit_exceeded = event.daily_request_limit_exceeded
        base_row = self._common_request_event_row(
            request_id,
            session_id,
            tenant_info,
            event,
            _sanitize_daily_request_limit_exceeded(daily_request_limit_exceeded),
        )
        base_row["opaque_user_id"] = daily_request_limit_exceeded.opaque_user_id
        base_row["user_id_type"] = (
            request_insight_pb2.OpaqueUserIdType.Name(
                daily_request_limit_exceeded.user_id_type
            )
            if daily_request_limit_exceeded.HasField("user_id_type")
            else None
        )
        base_row["limit"] = daily_request_limit_exceeded.limit
        return base_row
