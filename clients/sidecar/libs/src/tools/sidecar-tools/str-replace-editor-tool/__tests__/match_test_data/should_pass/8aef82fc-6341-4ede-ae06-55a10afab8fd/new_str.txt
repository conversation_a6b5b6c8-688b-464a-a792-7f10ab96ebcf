                # We will actually call model. Determine if this is a user message
                is_user_message = True
                for node in request.nodes or []:
                    if node.type == chat_pb2.ChatRequestNodeType.TOOL_RESULT:
                        is_user_message = False
                        break

                # Emit a UserMessage event if this is a user message
                if is_user_message:
                    # Create a request event
                    user_message_event = new_event()

                    # Determine if this is an agent chat based on agent_memories
                    is_agent_chat = request.HasField("agent_memories") and request.agent_memories

                    # Create a UserMessage event
                    ri_user_message = request_insight_pb2.UserMessage(
                        is_agent_chat=is_agent_chat,
                        request_source=request_context.request_source
                    )
                    user_message_event.user_message.MergeFrom(ri_user_message)

                    self.ri_publisher.publish_request_insight(
                        self.ri_publisher.update_request_info_request(
                            request_context.request_id, [user_message_event], auth_info
                        )
                    )

                generator = handler.chat_stream(
