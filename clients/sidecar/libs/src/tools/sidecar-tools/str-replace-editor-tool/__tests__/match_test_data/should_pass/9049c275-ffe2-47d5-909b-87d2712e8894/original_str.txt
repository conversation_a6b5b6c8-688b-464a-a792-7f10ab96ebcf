		Msg("Published invitation resolution message")

	return nil
}

func (p *asyncOpsPublisher) PublishSubscriptionCreation(
	ctx context.Context, msg *auth_internal.CreateSubscriptionMessage,
) error {
	log.Info().
		Str("user_id", msg.UserId).
		Str("tenant_id", msg.TenantId).
		Msg("Publishing subscription creation message")

	wrapperMsg := &auth_internal.AuthCentralAsyncOpsMessage{
		Message: &auth_internal.AuthCentralAsyncOpsMessage_CreateSubscriptionMessage{
			CreateSubscriptionMessage: msg,
		},
	}

	if err := p.Publish(ctx, wrapperMsg); err != nil {
		return err
	}

	log.Info().
		Str("user_id", msg.UserId).
		Str("tenant_id", msg.TenantId).
		Msg("Published subscription creation message")

	return nil
}

func (p *asyncOpsPublisher) PublishPutUserOnSubscription(
	ctx context.Context, msg *auth_internal.PutUserOnSubscriptionMessage,
) error {
	log.Info().
		Str("id", msg.Id).
		Str("user_id", msg.UserId).
		Str("subscription_id", msg.SubscriptionId).
