import { <PERSON>, Flex, <PERSON>, <PERSON><PERSON>, Card, <PERSON>ing, Link } from "@radix-ui/themes";
import { PlusIcon, IdCardIcon } from "@radix-ui/react-icons";
import { Progress } from "app/components/ui/Progress";
import { format, formatDistance } from "date-fns";
import { CreditsIcon } from "./CreditsIcon";
import { CalendarIcon } from "./CalendarIcon";
import PlanFeature from "app/components/account/PlanFeature";
import { PlanPickerDialog } from "./PlanPickerDialog";
import { useMutation } from "@tanstack/react-query";
import { makeUserHavePlan } from "app/client-cache/mutations/make-user-have-plan";
import { toast } from "app/components/ui/Toast";
import type { PlanOption } from "./PlanPickerDialog";
import { getPlanDescriptionWithPricingLink } from "./utils";
