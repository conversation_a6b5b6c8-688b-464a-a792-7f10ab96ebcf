// WorkspaceReportStatus reports the status of a workspace
func (s *RemoteAgentsServer) WorkspaceReportStatus(ctx context.Context, req *remoteagentsproto.WorkspaceReportStatusRequest) (*remoteagentsproto.WorkspaceReportStatusResponse, error) {
	ctx, userId, claims, requestContext, err := checkAndSetupRequestContext(ctx, req.RemoteAgentId)
	if err != nil {
		return nil, err
	}
	if err := checkUserAuthorization(ctx, requestContext, s.bigtableProxyClient, claims.TenantID, userId, req.RemoteAgentId); err != nil {
		return nil, err
	}

	log.Ctx(ctx).Debug().Msgf("Reporting remote agent status for user %s", userId)

	// Start timing the status update
	startTime := time.Now()

	// Get current status to track state change
	oldStatus, err := readRemoteAgentStatus(ctx, requestContext, s.bigtableProxyClient, claims.TenantID, userId, req.RemoteAgentId)
	if err != nil {
		log.Ctx(ctx).Warn().Err(err).Msg("Failed to read current agent status")
		// Continue even if we can't get the old status
	}

	// Write the status to bigtable
	err = writeRemoteAgentStatus(ctx, requestContext, s.bigtableProxyClient, claims.TenantID, userId, req.RemoteAgentId, req.Status)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to write agent status to BigTable")
		return nil, err
	}

	// Update state metrics
	updateAgentState(claims.TenantName, oldStatus.String(), req.Status.String())

	// Record the duration of the status update
	duration := time.Since(startTime).Seconds()
	agentStagesDuration.WithLabelValues(claims.TenantName, "status_update").Observe(duration)

	log.Ctx(ctx).Info().Msgf("Successfully updated status for agent %s to %s", req.RemoteAgentId, req.Status.String())
	return &remoteagentsproto.WorkspaceReportStatusResponse{}, nil
}
