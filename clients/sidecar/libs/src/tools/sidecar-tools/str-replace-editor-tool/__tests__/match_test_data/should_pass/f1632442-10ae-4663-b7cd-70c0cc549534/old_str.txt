from typing import Optional

import grpc

from base.python.grpc.client_options import ClientOptions
from services.chat_arbiter import chat_arbiter_pb2, chat_arbiter_pb2_grpc
from services.lib.request_context import request_context


class ChatArbiterClient:
    def __init__(self, endpoint: str, client_options: ClientOptions):
        self.endpoint = endpoint
        self.channel = grpc.secure_channel(endpoint, client_options.credentials)
        self.stub = chat_arbiter_pb2_grpc.ChatArbiterStub(self.channel)

    def get_target(
        self, request_context: request_context.RequestContext
    ) -> chat_arbiter_pb2.GetTargetResponse:
        metadata = request_context.to_metadata()
        request = chat_arbiter_pb2.GetTargetRequest()
        return self.stub.GetTarget(request, metadata=metadata)

    def close(self):
        self.channel.close()
