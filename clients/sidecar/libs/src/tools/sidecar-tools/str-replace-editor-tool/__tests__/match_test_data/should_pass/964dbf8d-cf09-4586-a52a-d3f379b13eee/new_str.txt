message DailyRequestLimitExceeded {
  // The request type that exceeded the limit
  RequestType request_type = 1;

  // The limit that was exceeded
  uint32 limit = 2;
}

// UserMessage represents a message sent by a user in a chat or agent conversation.
// This event is used to track user messages and distinguish them from tool results.
message UserMessage {
  // The request that contains the user message
  chat.ChatRequest request = 1;

  // The request type (CHAT or AGENT_CHAT) as determined by the API proxy
  RequestType request_type = 2;

  // The source of the request, e.g. "vscode", "intellij", etc.
  string request_source = 3;
}
