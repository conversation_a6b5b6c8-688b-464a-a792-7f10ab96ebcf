/* eslint-disable jest/no-conditional-expect */
/* eslint-disable @typescript-eslint/unbound-method */
import * as fs from "fs";
import * as path from "path";
import { StrReplaceEditorTool } from "../str-replace-editor-tool";
import { MockWorkspaceWithCheckpoints } from "../../__tests__/mocks/mock-workspace-with-checkpoints";
import { setLibraryClientWorkspaces } from "../../../../client-interfaces/client-workspaces";
import { StrReplaceEditorToolDefinitionNested } from "../tool-definition-nested";
import { StrReplaceEditorToolDefinitionFlat } from "../tool-definition-flat";
import { IStrReplaceEditorToolDefinition } from "../tool-definition";

// Mock the feature flags module
jest.mock(
  "@augment-internal/sidecar-libs/src/client-interfaces/feature-flags",
  () => {
    // Use the actual implementation but with a mock
    const actual = jest.requireActual<
      typeof import("../../../../client-interfaces/feature-flags")
    >("../../../../client-interfaces/feature-flags");
    return {
      ...actual,
      getClientFeatureFlags: jest.fn().mockReturnValue({
        flags: {
          agentEditToolMinViewSize: 0, // Default value, will be changed in specific tests
          agentEditToolEnableFuzzyMatching: true,
          agentEditToolFuzzyMatchSuccessMessage:
            "Replacement successful. old_str and new_str were slightly modified to match the original file content.",
          agentEditToolFuzzyMatchMaxDiff: 50,
          agentEditToolFuzzyMatchMaxDiffRatio: 0.15,
          agentEditToolFuzzyMatchMinAllMatchStreakBetweenDiffs: 5,
          agentEditToolShowResultSnippet: true, // Show snippets in tests by default
        },
      }),
    };
  },
);

type TestCase = {
  // Input parameters to be passed to the str-replace-editor tool
  toolInputs: Record<string, unknown>;
  // Original file content before applying the tool
  originalContent: string;
  // Expected file content after applying the tool and formatting
  expectedContent: string | undefined;
  // Optional expected text output from the tool (for validation)
  expectedToolOutput?: string;
  // Optional expected content before auto-formatting is applied
  expectedBeforeFormattingContent?: string;
  // Configuration for test and tool instances
  setup: { test: Record<string, unknown>; tool: Record<string, unknown> };
};

// Map of test directory names to their corresponding tool definitions
type TestSetConfig = {
  toolDefinition: IStrReplaceEditorToolDefinition;
  description: string;
};

const TEST_SET_CONFIGS: Record<string, TestSetConfig> = {
  nested_schema: {
    toolDefinition: new StrReplaceEditorToolDefinitionNested(),
    description: "Tests using nested schema format",
  },
  flat_schema: {
    toolDefinition: new StrReplaceEditorToolDefinitionFlat(),
    description: "Tests using flat schema format",
  },
};

describe("StrReplaceEditorTool E2E Tests", () => {
  let testWorkspace: MockWorkspaceWithCheckpoints;
  let tool: StrReplaceEditorTool;

  // Helper function to read test case files
  const readTestCase = (testCaseDir: string): TestCase => {
    const toolInputsPath = path.join(testCaseDir, "tool_inputs.json");
    const originalPath = path.join(testCaseDir, "original.txt");
    const expectedPath = path.join(testCaseDir, "expected.txt");
    const expectedToolOutputPath = path.join(
      testCaseDir,
      "expected_tool_output.txt",
    );
    const expectedBeforeFormattingPath = path.join(
      testCaseDir,
      "expected_before_formatting.txt",
    );
    const setupPath = path.join(testCaseDir, "setup.json");

    const toolInputs = JSON.parse(
      fs.readFileSync(toolInputsPath, "utf8"),
    ) as Record<string, unknown>;
    const originalContent = fs.readFileSync(originalPath, "utf8");

    let expectedContent: string | undefined = undefined;
    if (fs.existsSync(expectedPath)) {
      expectedContent = fs.readFileSync(expectedPath, "utf8");
    }

    let expectedToolOutput: string | undefined = undefined;
    if (fs.existsSync(expectedToolOutputPath)) {
      expectedToolOutput = fs.readFileSync(expectedToolOutputPath, "utf8");
    }

    let expectedBeforeFormattingContent: string | undefined = undefined;
    if (fs.existsSync(expectedBeforeFormattingPath)) {
      expectedBeforeFormattingContent = fs.readFileSync(
        expectedBeforeFormattingPath,
        "utf8",
      );
    }

    let setup: { test: Record<string, unknown>; tool: Record<string, unknown> };
    if (fs.existsSync(setupPath)) {
      setup = JSON.parse(fs.readFileSync(setupPath, "utf8")) as {
        test: Record<string, unknown>;
        tool: Record<string, unknown>;
      };
    } else {
      setup = { test: {}, tool: {} };
    }

    return {
      toolInputs,
      originalContent,
      expectedContent,
      expectedToolOutput,
      expectedBeforeFormattingContent,
      setup,
    };
  };

  // Get all test sets from test_data directory
  const testDataDir = path.join(__dirname, "test_data");
  const testSets = fs
    .readdirSync(testDataDir)
    .map((dir) => path.join(testDataDir, dir))
    .filter((dir) => fs.statSync(dir).isDirectory())
    .filter((dir) =>
      Object.keys(TEST_SET_CONFIGS).includes(path.basename(dir)),
    );

  // For each test set, create a describe block
  testSets.forEach((testSetDir) => {
    const testSetName = path.basename(testSetDir);
    const testSetConfig = TEST_SET_CONFIGS[testSetName];

    describe(`${testSetName} - ${testSetConfig.description}`, () => {
      // Dynamically generate tests for each test case in the testset directory
      const testCaseDirs = fs
        .readdirSync(testSetDir)
        .map((dir) => path.join(testSetDir, dir))
        .filter((dir) => fs.statSync(dir).isDirectory());

      test.each(testCaseDirs)("Test: %s", async (testCaseDir) => {
        const {
          toolInputs,
          originalContent,
          expectedContent,
          expectedToolOutput,
          expectedBeforeFormattingContent,
          setup,
        } = readTestCase(testCaseDir);

        // Setup the test environment based on setup.json if it exists
        const testSetup = (setup.test || {}) as {
          maxLineLength?: number;
          addCheckpointDelayMs?: number;
        };
        testWorkspace = new MockWorkspaceWithCheckpoints(testSetup);
        setLibraryClientWorkspaces(testWorkspace);

        const filePath = toolInputs.path as string;
        testWorkspace.setFileContent(filePath, originalContent);
        testWorkspace.setAutoFormatMapping(
          filePath,
          expectedBeforeFormattingContent ?? expectedContent ?? originalContent,
          expectedContent ?? originalContent,
        );

        const defaultToolSetup = {
          minViewSize: 0,
          lineNumberErrorTolerance: 0.2,
          waitForAutoFormatMs: 0,
        };
        const toolSetup = {
          ...defaultToolSetup,
          ...setup.tool,
        };
        tool = new StrReplaceEditorTool(
          testWorkspace,
          toolSetup.lineNumberErrorTolerance,
          toolSetup.waitForAutoFormatMs,
          testSetConfig.toolDefinition,
        );

        const toolResponse = await tool.call(
          toolInputs,
          [],
          new AbortController().signal,
          "",
        );

        const updatedContent = testWorkspace.getFileContent(filePath);

        if (expectedContent !== undefined) {
          // expect no error
          if (toolResponse.isError) {
            // save the error message to a file for debugging
            fs.writeFileSync(
              path.join(testCaseDir, "error.txt"),
              toolResponse.text,
              "utf8",
            );
          }
          expect(toolResponse.isError).toBe(false);
          if (updatedContent !== expectedContent) {
            // save the updated content to a file for debugging
            fs.writeFileSync(
              path.join(testCaseDir, "actual.txt"),
              updatedContent as string,
              "utf8",
            );
          }
          expect(updatedContent).toEqual(expectedContent);
        } else {
          expect(toolResponse.isError).toBe(true);
        }

        if (expectedToolOutput !== undefined) {
          expect(toolResponse.text.trim()).toEqual(expectedToolOutput.trim());
        }
      });
    });
  });
});
