    });
  });

  it("should return ExceedsMaxDiffRatio when differences exceed maxDiffRatio", () => {
    const originalStr = "abcdefgh";
    const oldStr = "abcfgh"; // missing 'de', 2/6 = 0.33 ratio
    const newStr = "acfgh"; // removed 'b'

    const result = fuzzyMatchReplacementStrings(originalStr, oldStr, newStr, 5, 0.3);

    // Check for the specific reason
    expect(result).toHaveProperty("reason", MatchFailReason.ExceedsMaxDiffRatio);
    // Check for exact match with undefined
    expect(result).toEqual({
      reason: MatchFailReason.ExceedsMaxDiffRatio,
      oldStr: undefined,
      newStr: undefined
    });
  });
