import { StrReplaceEditorToolDefinitionFlat } from "../tool-definition-flat";

// Only mock the logging module
jest.mock("@augment-internal/sidecar-libs/src/logging");

describe("StrReplaceEditorToolDefinitionFlat", () => {
  let toolDefinition: StrReplaceEditorToolDefinitionFlat;

  beforeEach(() => {
    jest.clearAllMocks();
    toolDefinition = new StrReplaceEditorToolDefinitionFlat();
  });

  describe("extractStrReplaceEntries", () => {
    it("should extract str_replace entries from flat schema with indexed entries", () => {
      // Test input with flat schema format (indexed)
      const toolInput = {
        command: "str_replace",
        path: "test.txt",
        old_str_1: "old content 1",
        new_str_1: "new content 1",
        old_str_start_line_number_1: 5,
        old_str_end_line_number_1: 10,
        old_str_2: "old content 2",
        new_str_2: "new content 2",
        old_str_start_line_number_2: 15,
        old_str_end_line_number_2: 20,
      };

      // Execute
      const result = toolDefinition.extractStrReplaceEntries(toolInput);

      // Verify
      expect(result).toHaveLength(2);
      expect(result[0]).toEqual({
        old_str: "old content 1",
        new_str: "new content 1",
        old_str_start_line_number: 5,
        old_str_end_line_number: 10,
        index: 1,
      });
      expect(result[1]).toEqual({
        old_str: "old content 2",
        new_str: "new content 2",
        old_str_start_line_number: 15,
        old_str_end_line_number: 20,
        index: 2,
      });
    });

    it("should handle non-indexed entries", () => {
      // Test input with flat schema format (non-indexed)
      const toolInput = {
        command: "str_replace",
        path: "test.txt",
        old_str: "old content",
        new_str: "new content",
        old_str_start_line_number: 5,
        old_str_end_line_number: 10,
      };

      // Execute
      const result = toolDefinition.extractStrReplaceEntries(toolInput);

      // Verify
      expect(result).toHaveLength(1);
      expect(result[0]).toEqual({
        old_str: "old content",
        new_str: "new content",
        old_str_start_line_number: 5,
        old_str_end_line_number: 10,
        index: 0,
      });
    });

    it("should handle both indexed and non-indexed entries", () => {
      // Test input with both indexed and non-indexed entries
      const toolInput = {
        command: "str_replace",
        path: "test.txt",
        old_str: "old content",
        new_str: "new content",
        old_str_start_line_number: 5,
        old_str_end_line_number: 10,
        old_str_1: "old content 1",
        new_str_1: "new content 1",
        old_str_start_line_number_1: 15,
        old_str_end_line_number_1: 20,
      };

      // Execute
      const result = toolDefinition.extractStrReplaceEntries(toolInput);

      // Verify
      expect(result).toHaveLength(2);
      // The order might depend on the implementation
      const hasNonIndexed = result.some(
        (entry) =>
          entry.old_str === "old content" &&
          entry.new_str === "new content" &&
          entry.old_str_start_line_number === 5 &&
          entry.old_str_end_line_number === 10,
      );
      const hasIndexed = result.some(
        (entry) =>
          entry.old_str === "old content 1" &&
          entry.new_str === "new content 1" &&
          entry.old_str_start_line_number === 15 &&
          entry.old_str_end_line_number === 20,
      );
      expect(hasNonIndexed).toBe(true);
      expect(hasIndexed).toBe(true);
    });

    it("should handle entries with partial line numbers", () => {
      // Test input with partial line numbers
      const toolInput = {
        command: "str_replace",
        path: "test.txt",
        old_str_1: "old content 1",
        new_str_1: "new content 1",
        old_str_start_line_number_1: 5,
        // Missing old_str_end_line_number_1
        old_str_2: "old content 2",
        new_str_2: "new content 2",
        // Missing old_str_start_line_number_2
        old_str_end_line_number_2: 20,
      };

      // Execute
      const result = toolDefinition.extractStrReplaceEntries(toolInput);

      // Verify
      expect(result).toHaveLength(2);
      const entry1 = result.find((e) => e.old_str === "old content 1");
      const entry2 = result.find((e) => e.old_str === "old content 2");

      expect(entry1).toBeDefined();
      expect(entry1?.old_str_start_line_number).toBe(5);
      expect(entry1?.old_str_end_line_number).toBeUndefined();

      expect(entry2).toBeDefined();
      expect(entry2?.old_str_start_line_number).toBeUndefined();
      expect(entry2?.old_str_end_line_number).toBe(20);
    });

    it("should handle non-consecutive indices", () => {
      // Test input with non-consecutive indices
      const toolInput = {
        command: "str_replace",
        path: "test.txt",
        old_str_1: "old content 1",
        new_str_1: "new content 1",
        old_str_start_line_number_1: 5,
        old_str_end_line_number_1: 10,
        old_str_3: "old content 3",
        new_str_3: "new content 3",
        old_str_start_line_number_3: 15,
        old_str_end_line_number_3: 20,
        // Missing index 2
      };

      // Execute
      const result = toolDefinition.extractStrReplaceEntries(toolInput);

      // Verify
      expect(result).toHaveLength(2);
      const entry1 = result.find((e) => e.old_str === "old content 1");
      const entry3 = result.find((e) => e.old_str === "old content 3");

      expect(entry1).toBeDefined();
      expect(entry3).toBeDefined();
    });

    it("should handle non-indexed strings with indexed line numbers", () => {
      // Test input with non-indexed strings but indexed line numbers
      const toolInput = {
        command: "str_replace",
        path: "test.txt",
        old_str: "old content",
        new_str: "new content",
        old_str_start_line_number_1: 5,
        old_str_end_line_number_1: 10,
      };

      // Execute
      const result = toolDefinition.extractStrReplaceEntries(toolInput);

      // Verify
      expect(result).toHaveLength(1);
      expect(result[0]).toEqual({
        old_str: "old content",
        new_str: "new content",
        old_str_start_line_number: 5,
        old_str_end_line_number: 10,
        index: 1,
      });
    });

    it("should handle indexed strings with non-indexed line numbers", () => {
      // Test input with indexed strings but non-indexed line numbers
      const toolInput = {
        command: "str_replace",
        path: "test.txt",
        old_str_1: "old content 1",
        new_str_1: "new content 1",
        old_str_start_line_number: 5,
        old_str_end_line_number: 10,
      };

      // Execute
      const result = toolDefinition.extractStrReplaceEntries(toolInput);

      // Verify
      expect(result).toHaveLength(1);
      expect(result[0]).toEqual({
        old_str: "old content 1",
        new_str: "new content 1",
        old_str_start_line_number: 5,
        old_str_end_line_number: 10,
        index: 1,
      });
    });

    it("should not apply special case when both indexed and non-indexed strings exist", () => {
      // Test input with both indexed and non-indexed strings
      const toolInput = {
        command: "str_replace",
        path: "test.txt",
        old_str: "old content non-indexed",
        new_str: "new content non-indexed",
        old_str_1: "old content 1",
        new_str_1: "new content 1",
        old_str_start_line_number_1: 5,
        old_str_end_line_number_1: 10,
      };

      // Execute
      const result = toolDefinition.extractStrReplaceEntries(toolInput);

      // Verify
      expect(result).toHaveLength(2);
      const nonIndexedEntry = result.find((e) => e.index === 0);
      const indexedEntry = result.find((e) => e.index === 1);

      expect(nonIndexedEntry).toEqual({
        old_str: "old content non-indexed",
        new_str: "new content non-indexed",
        index: 0,
      });

      expect(indexedEntry).toEqual({
        old_str: "old content 1",
        new_str: "new content 1",
        old_str_start_line_number: 5,
        old_str_end_line_number: 10,
        index: 1,
      });
    });

    it("should not apply special case when both indexed and non-indexed line numbers exist", () => {
      // Test input with both indexed and non-indexed line numbers
      const toolInput = {
        command: "str_replace",
        path: "test.txt",
        old_str_1: "old content 1",
        new_str_1: "new content 1",
        old_str_start_line_number: 3,
        old_str_end_line_number: 4,
        old_str_start_line_number_1: 5,
        old_str_end_line_number_1: 10,
      };

      // Execute
      const result = toolDefinition.extractStrReplaceEntries(toolInput);

      // Verify
      expect(result).toHaveLength(1);
      expect(result[0]).toEqual({
        old_str: "old content 1",
        new_str: "new content 1",
        old_str_start_line_number: 5,
        old_str_end_line_number: 10,
        index: 1,
      });
    });

    it("should not apply special case when only one of the line number fields is indexed", () => {
      // Test input with mixed indexing on line numbers
      const toolInput = {
        command: "str_replace",
        path: "test.txt",
        old_str: "old content",
        new_str: "new content",
        old_str_start_line_number: 3,
        old_str_end_line_number_1: 10,
      };

      // Execute
      const result = toolDefinition.extractStrReplaceEntries(toolInput);

      // Verify
      expect(result).toHaveLength(1);
      expect(result[0]).toEqual({
        old_str: "old content",
        new_str: "new content",
        old_str_start_line_number: 3,
        index: 0,
      });
    });

    it("should throw error for input without str_replace entries", () => {
      // Test input without any str_replace entries
      const toolInput = {
        command: "str_replace",
        path: "test.txt",
      };

      // Execute & Verify
      expect(() => toolDefinition.extractStrReplaceEntries(toolInput)).toThrow(
        "Empty required parameter `str_replace_entries` for `str_replace` command.",
      );
    });
  });

  describe("extractInsertLineEntries", () => {
    it("should extract insert_line entries from flat schema with indexed entries", () => {
      // Test input with flat schema format (indexed)
      const toolInput = {
        command: "insert",
        path: "test.txt",
        insert_line_1: 5,
        new_str_1: "new content 1",
        insert_line_2: 10,
        new_str_2: "new content 2",
      };

      // Execute
      const result = toolDefinition.extractInsertLineEntries(toolInput);

      // Verify
      expect(result).toHaveLength(2);
      expect(result[0]).toEqual({
        insert_line: 5,
        new_str: "new content 1",
        index: 1,
      });
      expect(result[1]).toEqual({
        insert_line: 10,
        new_str: "new content 2",
        index: 2,
      });
    });

    it("should handle non-indexed entries", () => {
      // Test input with flat schema format (non-indexed)
      const toolInput = {
        command: "insert",
        path: "test.txt",
        insert_line: 5,
        new_str: "new content",
      };

      // Execute
      const result = toolDefinition.extractInsertLineEntries(toolInput);

      // Verify
      expect(result).toHaveLength(1);
      expect(result[0]).toEqual({
        insert_line: 5,
        new_str: "new content",
        index: 0,
      });
    });

    it("should handle both indexed and non-indexed entries", () => {
      // Test input with both indexed and non-indexed entries
      const toolInput = {
        command: "insert",
        path: "test.txt",
        insert_line: 5,
        new_str: "new content",
        insert_line_1: 10,
        new_str_1: "new content 1",
      };

      // Execute
      const result = toolDefinition.extractInsertLineEntries(toolInput);

      // Verify
      expect(result).toHaveLength(2);
      // The order might depend on the implementation
      const hasNonIndexed = result.some(
        (entry) => entry.insert_line === 5 && entry.new_str === "new content",
      );
      const hasIndexed = result.some(
        (entry) =>
          entry.insert_line === 10 && entry.new_str === "new content 1",
      );
      expect(hasNonIndexed).toBe(true);
      expect(hasIndexed).toBe(true);
    });

    it("should handle non-consecutive indices", () => {
      // Test input with non-consecutive indices
      const toolInput = {
        command: "insert",
        path: "test.txt",
        insert_line_1: 5,
        new_str_1: "new content 1",
        insert_line_3: 15,
        new_str_3: "new content 3",
        // Missing index 2
      };

      // Execute
      const result = toolDefinition.extractInsertLineEntries(toolInput);

      // Verify
      expect(result).toHaveLength(2);
      const entry1 = result.find((e) => e.insert_line === 5);
      const entry3 = result.find((e) => e.insert_line === 15);

      expect(entry1).toBeDefined();
      expect(entry3).toBeDefined();
    });

    it("should throw error for input without insert entries", () => {
      // Test input without any insert entries
      const toolInput = {
        command: "insert",
        path: "test.txt",
      };

      // Execute & Verify
      expect(() => toolDefinition.extractInsertLineEntries(toolInput)).toThrow(
        "Empty required parameter `insert_line_entries` for `insert` command.",
      );
    });
  });

  describe("description and inputSchemaJson", () => {
    it("should have a non-empty description", () => {
      expect(toolDefinition.description).toBeDefined();
      expect(typeof toolDefinition.description).toBe("string");
      expect(toolDefinition.description.length).toBeGreaterThan(0);
    });

    it("should have a valid JSON schema", () => {
      expect(toolDefinition.inputSchemaJson).toBeDefined();
      expect(typeof toolDefinition.inputSchemaJson).toBe("string");

      // Should be parseable as JSON
      const schema = JSON.parse(toolDefinition.inputSchemaJson) as {
        type: string;
        properties: Record<string, unknown>;
      };
      expect(schema.type).toBe("object");
      expect(schema.properties).toBeDefined();
      expect(schema.properties.command).toBeDefined();
      expect(schema.properties.path).toBeDefined();
      expect(schema.properties.old_str_1).toBeDefined();
      expect(schema.properties.new_str_1).toBeDefined();
      expect(schema.properties.insert_line_1).toBeDefined();
    });
  });
});
