/* eslint-disable @typescript-eslint/unbound-method */
import * as fs from "fs";
import * as path from "path";
import { fuzzyMatchReplacementStrings, MatchFailReason } from "../match-utils";

describe("match-utils E2E Tests", () => {
  // Constants for fuzzyMatchReplacementStrings parameters
  const FUZZY_MATCH_PARAMS = {
    maxDiff: 50,
    maxDiffRatio: 0.15,
    minAllMatchStreakBetweenDiffs: 5,
  };

  // Helper function to read test case files
  const readTestCase = (testCaseDir: string) => {
    const originalStrPath = path.join(testCaseDir, "original_str.txt");
    const oldStrPath = path.join(testCaseDir, "old_str.txt");
    const newStrPath = path.join(testCaseDir, "new_str.txt");
    const modifiedOldStrPath = path.join(testCaseDir, "modified_old_str.txt");
    const modifiedNewStrPath = path.join(testCaseDir, "modified_new_str.txt");

    const originalStr = fs.readFileSync(originalStrPath, "utf8");
    const oldStr = fs.readFileSync(oldStrPath, "utf8");
    const newStr = fs.readFileSync(newStrPath, "utf8");

    let modifiedOldStr: string | undefined = undefined;
    let modifiedNewStr: string | undefined = undefined;

    // Check if modified files exist
    if (fs.existsSync(modifiedOldStrPath)) {
      modifiedOldStr = fs.readFileSync(modifiedOldStrPath, "utf8");
    }

    if (fs.existsSync(modifiedNewStrPath)) {
      modifiedNewStr = fs.readFileSync(modifiedNewStrPath, "utf8");
    }

    return {
      originalStr,
      oldStr,
      newStr,
      modifiedOldStr,
      modifiedNewStr,
    };
  };

  // Get all test cases from match_test_data directory
  const testDataDir = path.join(__dirname, "match_test_data", "should_pass");
  const testCaseDirs = fs
    .readdirSync(testDataDir)
    .map((dir) => path.join(testDataDir, dir))
    .filter((dir) => fs.statSync(dir).isDirectory());

  // For each test case, create a single test
  testCaseDirs.forEach((testCaseDir) => {
    const testCaseName = path.basename(testCaseDir);

    // Read test case data once
    const { originalStr, oldStr, newStr, modifiedOldStr, modifiedNewStr } =
      readTestCase(testCaseDir);

    // Determine test type:
    // 1. Skip if old_str.txt is the same as modified_old_str.txt and new_str.txt is the same as modified_new_str.txt
    // 2. Match case if both modified files exist
    // 3. No-match case if modified files don't exist
    const shouldSkip =
      modifiedOldStr !== undefined &&
      modifiedNewStr !== undefined &&
      oldStr === modifiedOldStr &&
      newStr === modifiedNewStr;

    const isMatchCase =
      modifiedOldStr !== undefined && modifiedNewStr !== undefined;

    // Create a single test for this directory
    if (shouldSkip) {
      // eslint-disable-next-line jest/no-disabled-tests, jest/expect-expect
      it.skip(`Test case: ${testCaseName} (skipped)`, () => {
        // This test is intentionally skipped
      });
    } else if (isMatchCase) {
      it(`Test case: ${testCaseName} (match case)`, () => {
        // Run fuzzyMatchReplacementStrings
        let result = fuzzyMatchReplacementStrings(
          originalStr,
          oldStr,
          newStr,
          FUZZY_MATCH_PARAMS.maxDiff,
          FUZZY_MATCH_PARAMS.maxDiffRatio,
          FUZZY_MATCH_PARAMS.minAllMatchStreakBetweenDiffs,
        );

        // For match cases, verify success
        expect(typeof result === "object").toBe(true);
        result = result as { oldStr: string; newStr: string };
        expect("oldStr" in result).toBe(true);
        expect("newStr" in result).toBe(true);
        expect(result.oldStr).toEqual(modifiedOldStr);
        expect(result.newStr).toEqual(modifiedNewStr);
      });
    } else {
      it(`Test case: ${testCaseName} (no-match case)`, () => {
        // Run fuzzyMatchReplacementStrings
        const result = fuzzyMatchReplacementStrings(
          originalStr,
          oldStr,
          newStr,
          FUZZY_MATCH_PARAMS.maxDiff,
          FUZZY_MATCH_PARAMS.maxDiffRatio,
          FUZZY_MATCH_PARAMS.minAllMatchStreakBetweenDiffs,
        );

        // For no match cases, verify failure
        expect(typeof result !== "object").toBe(true);
        // Check that result is one of the MatchFailReason enum values
        expect(Object.values(MatchFailReason)).toContain(result);
      });
    }
  });
});
