from typing import Callable, Optional

from services.third_party_arbiter import third_party_arbiter_pb2
from services.lib.request_context import request_context


class MockThirdPartyArbiterClient:
    def __init__(self):
        self.get_target_func: Optional[
            Callable[
                [request_context.RequestContext], third_party_arbiter_pb2.GetTargetResponse
            ]
        ] = None

    def get_target(
        self, request_context: request_context.RequestContext
    ) -> third_party_arbiter_pb2.GetTargetResponse:
        if self.get_target_func:
            return self.get_target_func(request_context)
        return third_party_arbiter_pb2.GetTargetResponse(region="", service="")
