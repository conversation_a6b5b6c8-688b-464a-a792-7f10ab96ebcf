export const loader = withAuth(
  async ({ user }) => {
    // Get user details from Auth Central
    const authCentralClient = AuthCentralClient.getInstance();
    const userResponse = await authCentralClient.getUser(user);
    const orbCustomerId = userResponse.user?.orbCustomerId;

    // Get all available plans
    const plansResponse = await authCentralClient.getAllOrbPlans(user);
    const plans = plansResponse.orbPlans
      .filter((plan) => plan.externalPlanId !== "orb_trial_plan")
      .map((orbPlan) => {
        // Map OrbPlanInfo to PlanOption format
        const planId = orbPlan.externalPlanId;
        const colorSchemes = {
          orb_community_plan: {
            color: "var(--blue-9)",
            colorScheme: {
              radixColor: "blue",
              gradientStart: "#3b82f6",
              gradientEnd: "#1d4ed8",
            },
          },
          orb_developer_plan: {
            color: "var(--purple-9)",
            colorScheme: {
              radixColor: "purple",
              gradientStart: "#8b5cf6",
              gradientEnd: "#6d28d9",
            },
          },
          orb_enterprise_plan: {
            color: "var(--cyan-9)",
            colorScheme: {
              radixColor: "cyan",
              gradientStart: "#06b6d4",
              gradientEnd: "#0891b2",
            },
          },
          default: {
            color: "var(--gray-9)",
            colorScheme: {
              radixColor: "gray",
              gradientStart: "#6b7280",
              gradientEnd: "#4b5563",
            },
          },
        };

        const colorScheme = colorSchemes[planId] || colorSchemes.default;
        const price = orbPlan.pricePerSeat;
        const priceLabel = price === 0 ? "Free" : `$${price}/mo`;

        return {
          id: orbPlan.externalPlanId,
          name: orbPlan.formattedPlanName,
          description: `${orbPlan.formattedPlanName} plan with ${orbPlan.usageUnitsPerSeat} ${orbPlan.usageUnitName} per seat.`,
          agentRequests: orbPlan.usageUnitsPerSeat,
          hasTraining: orbPlan.trainingAllowed,
          hasTeams: orbPlan.teamsAllowed,
          price: price,
          priceLabel: priceLabel,
          color: colorScheme.color,
          colorScheme: colorScheme.colorScheme,
        };
      });

    // Add mock enterprise plan if it doesn't exist
    const hasEnterprisePlan = plans.some(
      (plan) => plan.id === "orb_enterprise_plan",
    );
    if (!hasEnterprisePlan) {
      plans.push({
        id: "orb_enterprise_plan",
        name: "Enterprise",
        description: "Enterprise plan with custom pricing and features.",
        agentRequests: 5000,
        hasTraining: false,
        hasTeams: true,
        price: 0, // Custom pricing
        priceLabel: "",
        color: "var(--cyan-9)",
        colorScheme: {
          radixColor: "cyan",
          gradientStart: "#06b6d4",
          gradientEnd: "#0891b2",
        },
      });
    }

    // If no Orb customer ID, we can't check subscription status
    if (!orbCustomerId) {
      return json({
        message: "Please select a plan below to continue using Augment.",
        hasExpiredPlan: false,
        plans,
      });
    }

    // Get customer info from Orb
    const customerInfo = await getOrbCustomerInfo(orbCustomerId);

    // If no customer info, we can't check subscription status
    if (!customerInfo) {
      return json({
        message: "Please select a plan below to continue using Augment.",
        hasExpiredPlan: false,
        plans,
      });
    }

    // Check if the subscription has an end date and if it's in the past
    const hasExpiredPlan = customerInfo.subscriptionEndDate
      ? new Date(customerInfo.subscriptionEndDate) < new Date()
      : false;

    // If the user has an active subscription, redirect to the account page
    if (customerInfo.billingPeriodEnd && !hasExpiredPlan) {
      return redirect("/account/plan");
    }

    // Otherwise, show the plan picker with a message
    const message = hasExpiredPlan
      ? "Your subscription has expired. Please select a plan below to continue using Augment."
      : "Please select a plan below to continue using Augment.";

    return json({ message, hasExpiredPlan, plans });
  },
  {
    adminOnly: false,
  },
);
