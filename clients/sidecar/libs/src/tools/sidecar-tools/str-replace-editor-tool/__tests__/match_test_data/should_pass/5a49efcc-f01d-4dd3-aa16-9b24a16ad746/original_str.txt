	}
	return enabled
}

// PutUserOnSubscription associates a user with an Orb subscription
// This is an async operation that will be handled by the auth central service
func (s *TeamManagementServer) PutUserOnSubscription(
	ctx context.Context, req *authpb.PutUserOnSubscriptionRequest,
) (*authpb.PutUserOnSubscriptionResponse, error) {
	if !s.teamManagementEnabled() {
		return nil, status.Error(codes.Internal, "Disabled")
	}

	// Check permissions - only allow admin users to perform this operation
	err := s.teamManagementAuthCheck(ctx, nil, &req.UserId, tokenexchangepb.Scope_AUTH_RW, true, "PutUserOnSubscription")
	if err != nil {
		return nil, err
	}

	// Validate the request
	if req.UserId == "" {
		return nil, status.Error(codes.InvalidArgument, "User ID is required")
	}
	if req.SubscriptionId == "" {
		return nil, status.Error(codes.InvalidArgument, "Subscription ID is required")
	}

	// TODO: Implement the actual logic to associate the user with the subscription
	// This will be an async operation that publishes a message to the async ops topic
	// For now, just log the request and return success
	log.Info().
		Str("user_id", req.UserId).
		Str("subscription_id", req.SubscriptionId).
		Msg("PutUserOnSubscription called - implementation pending")

	return &authpb.PutUserOnSubscriptionResponse{}, nil
}

// Get all the invitations for a tenant with the given status.
func (s *TeamManagementServer) getInvitationsForTenant(
	ctx context.Context, tenantID string, status auth_entities.TenantInvitation_Status,
) ([]*auth_entities.TenantInvitation, error) {
	var invitations []*auth_entities.TenantInvitation
	invitationDAO := s.daoFactory.GetTenantInvitationDAO(tenantID)

	err := invitationDAO.FindAll(ctx, func(invitation *auth_entities.TenantInvitation) bool {
		if invitation.Status == status {
			invitations = append(invitations, invitation)
		}
		return true
	})
	if err != nil {
		return nil, fmt.Errorf("failed to get invitations: %w", err)
	}

	return invitations, nil
}

// GetActiveTeamMemberCount returns the number of active team members for a tenant.
func (s *TeamManagementServer) GetActiveTeamMemberCount(
	ctx context.Context, tenantID string,
) (int, error) {
	userTenantMappingDAO := s.daoFactory.GetUserTenantMappingDAO(tenantID)
	count := 0
	err := userTenantMappingDAO.FindAll(ctx, func(mapping *auth_entities.UserTenantMapping) bool {
		count++
		return true
	})
	if err != nil {
		return 0, fmt.Errorf("failed to fetch team members: %w", err)
	}

	return count, nil
}

// ValidateSubscriptionSeats checks if a subscription has enough seats for the current team members,
// pending invitations, and optionally additional new invitations.
func (s *TeamManagementServer) ValidateSubscriptionSeats(
	ctx context.Context,
	tenantID string,
	seatLimit int32,
	newInvitationCount int,
) error {
	// 1. Count active team members in the tenant
	activeTeamMemberCount, err := s.GetActiveTeamMemberCount(ctx, tenantID)
