  // Check if this is the enterprise plan
  const isEnterprisePlan = plan.id === "orb_enterprise_plan";

  return (
    <Card
      size="2"
      style={{
        position: "relative",
        overflow: "hidden",
        border: isSelected
          ? `2px solid ${plan.color}`
          : "1px solid var(--gray-5)",
        borderRadius: "var(--radius-4)",
        boxShadow: "0 8px 30px rgba(0, 0, 0, 0.08)",
        cursor: isEnterprisePlan ? "default" : "pointer",
        transition: "all 0.2s ease",
        padding: isSelected ? "15px" : "16px", // Adjust padding to prevent content shift
        opacity: isEnterprisePlan ? 0.8 : 1,
      }}
      onClick={isEnterprisePlan ? undefined : onClick}
      className="plan-card"
    >
