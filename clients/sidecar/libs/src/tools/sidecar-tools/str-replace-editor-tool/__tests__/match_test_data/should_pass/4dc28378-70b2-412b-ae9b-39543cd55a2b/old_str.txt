type ChatArbiterClient interface {
	GetTarget(ctx context.Context, requestContext requestcontext.RequestContext) (*proto.GetTargetResponse, error)
}

type chatArbiterClient struct {
	client proto.ChatArbiterClient
}

func New(endpoint string, opts ...grpc.DialOption) (ChatArbiterClient, error) {
	opts = append(opts, grpc.WithStatsHandler(otelgrpc.NewClientHandler()))
	conn, err := grpc.Dial(endpoint, opts...)
	if err != nil {
		return nil, fmt.Errorf("failed to dial chat arbiter: %w", err)
	}
	return &chatArbiterClient{
		client: proto.NewChatArbiterClient(conn),
	}, nil
}

func NewWithCredentials(endpoint string, creds credentials.TransportCredentials) (ChatArbiterClient, error) {
	return New(endpoint, grpc.WithTransportCredentials(creds))
}
