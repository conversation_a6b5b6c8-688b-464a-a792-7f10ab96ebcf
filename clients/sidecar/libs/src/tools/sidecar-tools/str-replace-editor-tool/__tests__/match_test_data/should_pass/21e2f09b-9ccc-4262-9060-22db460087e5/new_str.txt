def _sanitize_daily_request_limit_exceeded(
    daily_request_limit_exceeded: request_insight_pb2.DailyRequestLimitExceeded,
) -> request_insight_pb2.DailyRequestLimitExceeded:
    """Returns a DailyRequestLimitExceeded with sensitive fields removed."""
    sanitized = request_insight_pb2.DailyRequestLimitExceeded()
    sanitized.limit = daily_request_limit_exceeded.limit
    return sanitized


def _sanitize_user_message(
    user_message: request_insight_pb2.UserMessage,
) -> request_insight_pb2.UserMessage:
    """Returns a UserMessage with sensitive fields removed."""
    sanitized = request_insight_pb2.UserMessage()
    sanitized.conversation_id = user_message.conversation_id
    sanitized.chat_mode = user_message.chat_mode
    sanitized.chat_history_length = user_message.chat_history_length
    sanitized.text_node_count = user_message.text_node_count
    sanitized.image_node_count = user_message.image_node_count
    sanitized.tool_result_node_count = user_message.tool_result_node_count
    sanitized.character_count = user_message.character_count
    sanitized.line_count = user_message.line_count
    return sanitized
