  it("should return AmbiguousReplacement for cases where there are overlapping differences between old vs new and old vs original", () => {
    const originalStr = "abcxyz";
    const oldStr = "abcxYz"; // 'Y' doesn't match original
    const newStr = "ABCxyz"; // 'ABC' doesn't match old

    const result = fuzzyMatchReplacementStrings(originalStr, oldStr, newStr, 5, 0.5);

    // Check for the specific reason
    expect(result).toHaveProperty("reason", MatchFailReason.AmbiguousReplacement);
    // Check for exact match with undefined
    expect(result).toEqual({
      reason: MatchFailReason.AmbiguousReplacement,
      oldStr: undefined,
      newStr: undefined
    });
  });
