type ThirdPartyArbiterClient interface {
	GetTarget(ctx context.Context, requestContext requestcontext.RequestContext) (*proto.GetTargetResponse, error)
}

type thirdPartyArbiterClient struct {
	client proto.ThirdPartyArbiterClient
}

func New(endpoint string, opts ...grpc.DialOption) (ThirdPartyArbiterClient, error) {
	opts = append(opts, grpc.WithStatsHandler(otelgrpc.NewClientHandler()))
	conn, err := grpc.Dial(endpoint, opts...)
	if err != nil {
		return nil, fmt.Errorf("failed to dial third party arbiter: %w", err)
	}
	return &thirdPartyArbiterClient{
		client: proto.NewThirdPartyArbiterClient(conn),
	}, nil
}

func NewWithCredentials(endpoint string, creds credentials.TransportCredentials) (ThirdPartyArbiterClient, error) {
	return New(endpoint, grpc.WithTransportCredentials(creds))
}
