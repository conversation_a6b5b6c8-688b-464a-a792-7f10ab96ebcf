This test fails because in `fuzzyMatchReplacementStrings` we limit the `maxIndexDiff` when computing longest common subsequence which results in not finding the correct mapping between old and new because new has a very large addition
This is fixed by increasing `computeBudgetIterations` to `100 * 1000 * 1000` but it's too slow
Better solution is to match by line first and then match within lines which is TODO
