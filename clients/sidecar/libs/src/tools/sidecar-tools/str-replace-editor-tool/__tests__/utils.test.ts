import {
  removeTrailingWhitespace,
  prepareTextForEditing,
  createSnippet,
  expandViewRange,
  findMultipleOccurrences,
  detectIndentation,
  removeOneIndentLevel,
  allLinesHaveIndent,
  findMatches,
  findClosestMatch,
  validateStrReplaceEntries,
  validateInsertLineEntries,
  prepareStrReplaceEntries,
  findOverlappingEntry,
  genOverlappingEntryErrorMessage,
  extractStrReplaceEntries,
  extractInsertLineEntries,
  EditResult,
  updateEditResultLineNumbers,
  removeAllIndents,
  serializeWithIndent,
  createJsonObjectErrorResult,
} from "../utils";
import { StrReplaceEntry } from "../tool-definition";

describe("str-replace-editor-tool-utils", () => {
  describe("removeTrailingWhitespace", () => {
    it("should remove trailing whitespace from each line", () => {
      const input =
        "line with spaces   \nline with tabs\t\t\nno trailing space";
      const expected = "line with spaces\nline with tabs\nno trailing space";
      expect(removeTrailingWhitespace(input)).toBe(expected);
    });

    it("should preserve original line endings (CRLF)", () => {
      const input = "line 1  \r\nline 2\t\r\nline 3";
      const expected = "line 1\r\nline 2\r\nline 3";
      expect(removeTrailingWhitespace(input)).toBe(expected);
    });

    it("should remove trailing spaces from each line with LF line endings", () => {
      const input = "line1  \nline2\t \nline3   ";
      const expected = "line1\nline2\nline3";
      const result = removeTrailingWhitespace(input);
      expect(result).toBe(expected);
    });

    it("should preserve leading spaces", () => {
      const input = "  line1\n  line2  \n\tline3";
      const expected = "  line1\n  line2\n\tline3";
      const result = removeTrailingWhitespace(input);
      expect(result).toBe(expected);
    });

    it("should handle empty lines correctly", () => {
      const input = "line1\n\n  \nline4";
      const expected = "line1\n\n\nline4";
      const result = removeTrailingWhitespace(input);
      expect(result).toBe(expected);
    });

    it("should handle empty input", () => {
      const input = "";
      const expected = "";
      const result = removeTrailingWhitespace(input);
      expect(result).toBe(expected);
    });
  });

  describe("prepareTextForEditing", () => {
    it("should normalize line endings and remove trailing whitespace", () => {
      const input = "line 1  \r\nline 2\t\t\r\nline 3";
      const result = prepareTextForEditing(input);
      expect(result.content).toBe("line 1\nline 2\nline 3");
      expect(result.originalLineEnding).toBe("\r\n");
    });
  });

  describe("createSnippet", () => {
    it("should create a snippet with context lines", () => {
      const content = "line 1\nline 2\nline 3\nline 4\nline 5";
      const result = createSnippet(content, 2, 1, 1);
      expect(result.snippet).toBe("line 2\nline 3\nline 4");
      expect(result.startLine).toBe(1);
    });

    it("should handle start of file correctly", () => {
      const content = "line 1\nline 2\nline 3\nline 4\nline 5";
      const result = createSnippet(content, 0, 1, 1);
      expect(result.snippet).toBe("line 1\nline 2");
      expect(result.startLine).toBe(0);
    });
  });

  describe("expandViewRange", () => {
    it("should not expand if range is already large enough", () => {
      const result = expandViewRange(5, 15, 10, 20);
      expect(result).toEqual({ initLine: 5, finalLine: 15 });
    });

    it("should expand range to meet minimum size", () => {
      const result = expandViewRange(10, 12, 10, 20);
      expect(result.finalLine - result.initLine + 1).toBeGreaterThanOrEqual(10);
    });

    it("should not expand beyond file boundaries", () => {
      const result = expandViewRange(18, 19, 10, 20);
      expect(result.initLine).toBeGreaterThanOrEqual(1);
      expect(result.finalLine).toBeLessThanOrEqual(20);
    });

    it("should handle -1 as finalLine", () => {
      const result = expandViewRange(15, -1, 10, 20);
      expect(result).toEqual({ initLine: 15, finalLine: -1 });
    });
  });

  describe("findMultipleOccurrences", () => {
    it("should return null if string appears only once", () => {
      const content = "line 1\nline 2\nline 3";
      expect(findMultipleOccurrences(content, "line 1")).toBeNull();
    });

    it("should return line numbers for multiple occurrences", () => {
      const content = "repeated\nother\nrepeated";
      const result = findMultipleOccurrences(content, "repeated");
      expect(result).toEqual([1, 3]);
    });
  });

  describe("detectIndentation", () => {
    it("should detect space indentation", () => {
      const content = "line 1\n  line 2\n    line 3";
      const result = detectIndentation(content);
      expect(result).toEqual({ type: "space", size: 2 });
    });

    it("should detect tab indentation", () => {
      const content = "line 1\n\tline 2\n\t\tline 3";
      const result = detectIndentation(content);
      expect(result).toEqual({ type: "tab", size: 1 });
    });

    it("should default to 2 spaces if no indentation found", () => {
      const content = "line 1\nline 2\nline 3";
      const result = detectIndentation(content);
      expect(result).toEqual({ type: "space", size: 2 });
    });

    it("should detect space indentation and size in code blocks", () => {
      const content =
        "function test() {\n  if (condition) {\n    console.log('test');\n  }\n}";
      const result = detectIndentation(content);
      expect(result.type).toBe("space");
      expect(result.size).toBe(2);
    });

    it("should detect tab indentation in code blocks", () => {
      const content =
        "function test() {\n\tif (condition) {\n\t\tconsole.log('test');\n\t}\n}";
      const result = detectIndentation(content);
      expect(result.type).toBe("tab");
      expect(result.size).toBe(1);
    });
  });

  describe("removeOneIndentLevel", () => {
    it("should remove one level of space indentation", () => {
      const content = "  line 1\n  line 2\n    line 3";
      const result = removeOneIndentLevel(content, { type: "space", size: 2 });
      expect(result).toBe("line 1\nline 2\n  line 3");
    });

    it("should remove one level of tab indentation", () => {
      const content = "\tline 1\n\tline 2\n\t\tline 3";
      const result = removeOneIndentLevel(content, { type: "tab", size: 1 });
      expect(result).toBe("line 1\nline 2\n\tline 3");
    });

    it("should handle lines with no indentation", () => {
      const text = "  line1\nline2\n  line3";
      const indentation = { type: "space" as const, size: 2 };
      const result = removeOneIndentLevel(text, indentation);
      expect(result).toBe("line1\nline2\nline3");
    });
  });

  describe("allLinesHaveIndent", () => {
    it("should return true if all non-empty lines have indentation", () => {
      const content = "  line 1\n  line 2\n\n  line 3";
      expect(
        allLinesHaveIndent(content, { type: "space" as const, size: 2 }),
      ).toBe(true);
    });

    it("should return false if any non-empty line lacks indentation", () => {
      const content = "  line 1\nline 2\n  line 3";
      expect(
        allLinesHaveIndent(content, { type: "space" as const, size: 2 }),
      ).toBe(false);
    });

    it("should return true when all lines have indentation (spaces)", () => {
      const text = "  line1\n  line2\n  line3";
      const indentation = { type: "space" as const, size: 2 };
      expect(allLinesHaveIndent(text, indentation)).toBe(true);
    });

    it("should return true when there are empty lines and all non-empty lines have indentation (spaces)", () => {
      const text = "  line1\n\n  line3";
      const indentation = { type: "space" as const, size: 2 };
      expect(allLinesHaveIndent(text, indentation)).toBe(true);
    });

    it("should return false when a non-empty line has no indentation (tabs)", () => {
      const text = "\tline1\nline2\n\tline3";
      const indentation = { type: "tab" as const, size: 1 };
      expect(allLinesHaveIndent(text, indentation)).toBe(false);
    });

    it("should return true when all lines have indentation (tabs)", () => {
      const text = "\tline1\n\tline2\n\tline3";
      const indentation = { type: "tab" as const, size: 1 };
      expect(allLinesHaveIndent(text, indentation)).toBe(true);
    });

    it("should return true when there are empty lines and all non-empty lines have indentation (tabs)", () => {
      const text = "\tline1\n\n\tline3";
      const indentation = { type: "tab" as const, size: 1 };
      expect(allLinesHaveIndent(text, indentation)).toBe(true);
    });

    it("should handle mixed indentation correctly", () => {
      const text = "\tline1\n  line2\n\tline3";

      // When checking for tabs, should return false because line2 uses spaces
      const tabIndentation = { type: "tab" as const, size: 1 };
      expect(allLinesHaveIndent(text, tabIndentation)).toBe(false);

      // When checking for spaces, should return false because line1 and line3 use tabs
      const spaceIndentation = { type: "space" as const, size: 2 };
      expect(allLinesHaveIndent(text, spaceIndentation)).toBe(false);
    });
  });

  describe("findMatches", () => {
    it("should find single line matches", () => {
      const content = "line 1\nline 2\nline 1\nline 3";
      const matches = findMatches(content, "line 1");
      expect(matches).toEqual([
        { startLine: 0, endLine: 0 },
        { startLine: 2, endLine: 2 },
      ]);
    });

    it("should find multi-line matches", () => {
      const content = "line 1\nline 2\nline 3\nline 1\nline 2\nline 4";
      const searchStr = "line 1\nline 2";
      const matches = findMatches(content, searchStr);
      expect(matches).toEqual([
        { startLine: 0, endLine: 1 },
        { startLine: 3, endLine: 4 },
      ]);
    });

    it("should return empty array for empty search string", () => {
      const content = "line 1\nline 2";
      expect(findMatches(content, "")).toEqual([]);
    });

    it("should return empty array if search string has more lines than content", () => {
      const content = "line 1\nline 2";
      const searchStr = "line 1\nline 2\nline 3";
      expect(findMatches(content, searchStr)).toEqual([]);
    });

    it("should handle partial matches correctly", () => {
      const content = "line 1\nline 2\nline 3";
      const searchStr = "line";
      const matches = findMatches(content, searchStr);
      expect(matches).toEqual([
        { startLine: 0, endLine: 0 },
        { startLine: 1, endLine: 1 },
        { startLine: 2, endLine: 2 },
      ]);
    });

    it("should not find matches when none exist", () => {
      const content = "line 1\nline 2\nline 3";
      expect(findMatches(content, "not found")).toEqual([]);
    });

    it("should handle mid-line matches in multi-line search", () => {
      const content =
        "start of line 1 end\nstart of line 2 end\nstart of line 3 end";
      const searchStr = "end\nstart of";
      const matches = findMatches(content, searchStr);
      expect(matches).toEqual([
        { startLine: 0, endLine: 1 },
        { startLine: 1, endLine: 2 },
      ]);
    });

    it("should handle matches that start and end mid-line", () => {
      const content =
        "prefix match1 suffix\nprefix match2 suffix\nprefix match3 suffix";
      const searchStr = "match1 suffix\nprefix match2";
      const matches = findMatches(content, searchStr);
      expect(matches).toEqual([{ startLine: 0, endLine: 1 }]);
    });
  });

  describe("findClosestMatch", () => {
    it("should return -1 for empty matches array", () => {
      const result = findClosestMatch([], 1, 2, 0.2);
      expect(result).toBe(-1);
    });

    it("should return 0 for single match regardless of line numbers", () => {
      const matches = [{ startLine: 5, endLine: 6 }];
      const result = findClosestMatch(matches, 1, 2, 0.2);
      expect(result).toBe(0);
    });

    it("should find exact match when available", () => {
      const matches = [
        { startLine: 1, endLine: 2 },
        { startLine: 5, endLine: 6 },
        { startLine: 10, endLine: 12 },
      ];
      const result = findClosestMatch(matches, 5, 6, 0.2);
      expect(result).toBe(1);
    });

    it("should find closest match when no exact match exists", () => {
      const matches = [
        { startLine: 1, endLine: 2 },
        { startLine: 5, endLine: 6 },
        { startLine: 10, endLine: 12 },
      ];
      const result = findClosestMatch(matches, 6, 7, 0.6);
      expect(result).toBe(1); // Closest to { startLine: 5, endLine: 6 }
    });

    it("should apply tolerance to find match", () => {
      const matches = [
        { startLine: 1, endLine: 2 },
        { startLine: 10, endLine: 12 },
        { startLine: 20, endLine: 22 },
      ];

      const resultSuccess = findClosestMatch(matches, 3, 4, 0.45);
      expect(resultSuccess).toBe(0); // Closest to { startLine: 1, endLine: 2 }

      const resultFail = findClosestMatch(matches, 3, 4, 0.43);
      expect(resultFail).toBe(-1);
    });

    it("should return -1 when no exact match exists and tolerance is 0", () => {
      const matches = [
        { startLine: 1, endLine: 2 },
        { startLine: 5, endLine: 6 },
        { startLine: 10, endLine: 12 },
      ];
      const result = findClosestMatch(matches, 6, 7, 0);
      expect(result).toBe(-1); // No match with zero tolerance
    });

    it("should return -1 when closest match is far away and tolerance is 0", () => {
      const matches = [
        { startLine: 1, endLine: 2 },
        { startLine: 10, endLine: 12 },
        { startLine: 20, endLine: 22 },
      ];
      const result = findClosestMatch(matches, 15, 17, 0);
      expect(result).toBe(-1); // No match with zero tolerance
    });

    it("should only find exact matches when tolerance is 0", () => {
      const matches = [
        { startLine: 1, endLine: 2 },
        { startLine: 5, endLine: 6 },
        { startLine: 10, endLine: 12 },
      ];

      // Should find exact match
      const exactMatchResult = findClosestMatch(matches, 5, 6, 0);
      expect(exactMatchResult).toBe(1);

      // Should not find non-exact match
      const nonExactMatchResult = findClosestMatch(matches, 5, 7, 0);
      expect(nonExactMatchResult).toBe(-1);
    });

    it("should handle tolerance of 1 (match exactly between two points)", () => {
      const matches = [
        { startLine: 10, endLine: 12 },
        { startLine: 20, endLine: 22 },
      ];

      // Point exactly between the two matches
      const result = findClosestMatch(matches, 15, 17, 1);
      expect(result).toBe(0); // Should match the closest one
    });

    it("should handle tolerance of 0.2 (match within 10% of distance)", () => {
      const matches = [
        { startLine: 10, endLine: 12 },
        { startLine: 20, endLine: 22 },
      ];

      // Distance between matches is 10 lines, so 10% is 1 line
      // This point is 1 line away from first match (within tolerance)
      const withinToleranceResult = findClosestMatch(matches, 11, 13, 0.2);
      expect(withinToleranceResult).toBe(0);

      // This point is 2 lines away from first match (outside tolerance)
      const outsideToleranceResult = findClosestMatch(matches, 12, 14, 0.2);
      expect(outsideToleranceResult).toBe(-1);
    });

    it("should round down the tolerance threshold as specified in docstring", () => {
      const matches = [
        { startLine: 10, endLine: 12 },
        { startLine: 20, endLine: 22 },
      ];

      // Half distance between matches is 5 lines
      // With tolerance 0.25, threshold should be 1.25 rounded down to 1
      const withinThresholdResult = findClosestMatch(matches, 11, 14, 0.25);
      expect(withinThresholdResult).toBe(0); // Within threshold of 1

      const outsideThresholdResult = findClosestMatch(matches, 12, 15, 0.25);
      expect(outsideThresholdResult).toBe(-1); // Outside threshold of 1
    });
  });

  describe("validateStrReplaceEntries", () => {
    it("should throw error when str_replace_entries is undefined", () => {
      expect(() => validateStrReplaceEntries(undefined!)).toThrow(
        "Missing required parameter `str_replace_entries` for `str_replace` command.",
      );
    });

    it("should throw error when str_replace_entries is not an array", () => {
      expect(() =>
        validateStrReplaceEntries(
          {} as Array<{ old_str: string; new_str: string }>,
        ),
      ).toThrow(
        "Invalid parameter `str_replace_entries` for `str_replace` command. It must be an array of objects.",
      );
    });

    it("should throw error when str_replace_entries is an empty array", () => {
      expect(() => validateStrReplaceEntries([])).toThrow(
        "Empty required parameter `str_replace_entries` for `str_replace` command.",
      );
    });

    it("should throw error when an entry is not an object", () => {
      expect(() =>
        validateStrReplaceEntries(["not an object"] as unknown as Array<{
          old_str: string;
          new_str: string;
        }>),
      ).toThrow(
        "Invalid parameter `str_replace_entries` for `str_replace` command. It must be an array of objects.",
      );
    });

    it("should throw error when old_str is missing", () => {
      expect(() =>
        validateStrReplaceEntries([{ new_str: "new" }] as Array<{
          old_str: string;
          new_str: string;
        }>),
      ).toThrow(
        "Missing required parameter `old_str` for `str_replace` command.",
      );
    });

    it("should throw error when new_str is missing", () => {
      expect(() =>
        validateStrReplaceEntries([{ old_str: "old" }] as Array<{
          old_str: string;
          new_str: string;
        }>),
      ).toThrow(
        "Missing required parameter `new_str` for `str_replace` command.",
      );
    });

    it("should throw error when old_str_start_line_number is not a positive integer", () => {
      expect(() =>
        validateStrReplaceEntries([
          {
            old_str: "old",
            new_str: "new",
            old_str_start_line_number: -1,
            old_str_end_line_number: 2,
          },
        ]),
      ).toThrow(
        "Invalid parameter `old_str_start_line_number` for `str_replace` command. It must be a positive integer.",
      );

      expect(() =>
        validateStrReplaceEntries([
          {
            old_str: "old",
            new_str: "new",
            old_str_start_line_number: 1.5,
            old_str_end_line_number: 2,
          },
        ]),
      ).toThrow(
        "Invalid parameter `old_str_start_line_number` for `str_replace` command. It must be a positive integer.",
      );
    });

    it("should treat old_str_start_line_number with value 0 as undefined", () => {
      // Should not throw when old_str_start_line_number is 0
      expect(() =>
        validateStrReplaceEntries([
          {
            old_str: "old",
            new_str: "new",
            old_str_start_line_number: 0,
            old_str_end_line_number: 2,
          },
        ]),
      ).not.toThrow();
    });

    it("should throw error when old_str_end_line_number is not a positive integer", () => {
      expect(() =>
        validateStrReplaceEntries([
          {
            old_str: "old",
            new_str: "new",
            old_str_start_line_number: 1,
            old_str_end_line_number: -1,
          },
        ]),
      ).toThrow(
        "Invalid parameter `old_str_end_line_number` for `str_replace` command. It must be a positive integer.",
      );

      expect(() =>
        validateStrReplaceEntries([
          {
            old_str: "old",
            new_str: "new",
            old_str_start_line_number: 1,
            old_str_end_line_number: 2.5,
          },
        ]),
      ).toThrow(
        "Invalid parameter `old_str_end_line_number` for `str_replace` command. It must be a positive integer.",
      );
    });

    it("should treat old_str_end_line_number with value 0 as undefined", () => {
      // Should not throw when old_str_end_line_number is 0
      expect(() =>
        validateStrReplaceEntries([
          {
            old_str: "old",
            new_str: "new",
            old_str_start_line_number: 1,
            old_str_end_line_number: 0,
          },
        ]),
      ).not.toThrow();
    });

    it("should not throw for valid entries", () => {
      // Valid entries with line numbers
      expect(() =>
        validateStrReplaceEntries([
          {
            old_str: "old1",
            new_str: "new1",
            old_str_start_line_number: 1,
            old_str_end_line_number: 3,
          },
          {
            old_str: "old2",
            new_str: "new2",
            old_str_start_line_number: 5,
            old_str_end_line_number: 7,
          },
        ]),
      ).not.toThrow();

      // Valid entries without line numbers
      expect(() =>
        validateStrReplaceEntries([
          { old_str: "old1", new_str: "new1" },
          { old_str: "old2", new_str: "new2" },
        ]),
      ).not.toThrow();

      // Mix of entries with and without line numbers
      expect(() =>
        validateStrReplaceEntries([
          { old_str: "old1", new_str: "new1" },
          {
            old_str: "old2",
            new_str: "new2",
            old_str_start_line_number: 5,
            old_str_end_line_number: 7,
          },
        ]),
      ).not.toThrow();

      // Mix of entries with zero and valid line numbers
      expect(() =>
        validateStrReplaceEntries([
          {
            old_str: "old1",
            new_str: "new1",
            old_str_start_line_number: 0,
            old_str_end_line_number: 0,
          },
          {
            old_str: "old2",
            new_str: "new2",
            old_str_start_line_number: 5,
            old_str_end_line_number: 7,
          },
        ]),
      ).not.toThrow();
    });
  });

  describe("validateInsertLineEntries", () => {
    it("should throw error when insert_line_entries is undefined", () => {
      expect(() => validateInsertLineEntries(undefined!)).toThrow(
        "Missing required parameter `insert_line_entries` for `insert` command.",
      );
    });

    it("should throw error when insert_line_entries is not an array", () => {
      expect(() =>
        validateInsertLineEntries(
          {} as Array<{ insert_line: number; new_str: string }>,
        ),
      ).toThrow(
        "Invalid parameter `insert_line_entries` for `insert` command. It must be an array of objects.",
      );
    });

    it("should throw error when insert_line_entries is an empty array", () => {
      expect(() => validateInsertLineEntries([])).toThrow(
        "Empty required parameter `insert_line_entries` for `insert` command.",
      );
    });

    it("should throw error when an entry is not an object", () => {
      expect(() =>
        validateInsertLineEntries(["not an object"] as unknown as Array<{
          insert_line: number;
          new_str: string;
        }>),
      ).toThrow(
        "Invalid parameter `insert_line_entries` for `insert` command. It must be an array of objects.",
      );
    });

    it("should throw error when insert_line is missing", () => {
      expect(() =>
        validateInsertLineEntries([{ new_str: "new" }] as Array<{
          insert_line: number;
          new_str: string;
        }>),
      ).toThrow(
        "Missing required parameter `insert_line` for `insert` command.",
      );
    });

    it("should throw error when new_str is missing", () => {
      expect(() =>
        validateInsertLineEntries([{ insert_line: 1 }] as Array<{
          insert_line: number;
          new_str: string;
        }>),
      ).toThrow("Missing required parameter `new_str` for `insert` command.");
    });

    it("should throw error when insert_line is not a non-negative integer", () => {
      expect(() =>
        validateInsertLineEntries([{ insert_line: -1, new_str: "new" }]),
      ).toThrow(
        "Invalid parameter `insert_line` for `insert` command. It must be a non-negative integer.",
      );

      expect(() =>
        validateInsertLineEntries([{ insert_line: 1.5, new_str: "new" }]),
      ).toThrow(
        "Invalid parameter `insert_line` for `insert` command. It must be a non-negative integer.",
      );
    });

    it("should not throw for valid entries", () => {
      // Valid entries with insert_line as 0
      expect(() =>
        validateInsertLineEntries([{ insert_line: 0, new_str: "new1" }]),
      ).not.toThrow();

      // Valid entries with positive insert_line
      expect(() =>
        validateInsertLineEntries([
          { insert_line: 1, new_str: "new1" },
          { insert_line: 5, new_str: "new2" },
        ]),
      ).not.toThrow();

      // Valid entries with empty new_str
      expect(() =>
        validateInsertLineEntries([{ insert_line: 1, new_str: "" }]),
      ).not.toThrow();
    });
  });

  describe("prepareStrReplaceEntries", () => {
    it("should convert line numbers to zero-based", () => {
      const entries = [
        {
          index: 0,
          old_str: "old1",
          new_str: "new1",
          old_str_start_line_number: 1,
          old_str_end_line_number: 3,
        },
        {
          index: 1,
          old_str: "old2",
          new_str: "new2",
          old_str_start_line_number: 5,
          old_str_end_line_number: 7,
        },
      ];
      const result = prepareStrReplaceEntries(entries);
      // After sorting, the second entry (index 1) will be first in the result array
      // because it has a higher line number and sorting is in reverse order
      expect(result[0].old_str_start_line_number).toBe(4); // 5-1
      expect(result[0].old_str_end_line_number).toBe(6); // 7-1
      expect(result[1].old_str_start_line_number).toBe(0); // 1-1
      expect(result[1].old_str_end_line_number).toBe(2); // 3-1
    });

    it("should sort entries by start line number in reverse order", () => {
      const entries = [
        {
          index: 0,
          old_str: "old1",
          new_str: "new1",
          old_str_start_line_number: 1,
          old_str_end_line_number: 3,
        },
        {
          index: 1,
          old_str: "old2",
          new_str: "new2",
          old_str_start_line_number: 5,
          old_str_end_line_number: 7,
        },
        { index: 2, old_str: "old3", new_str: "new3" },
      ];
      const result = prepareStrReplaceEntries(entries);
      // Entries with line numbers should be sorted in reverse order
      expect(result[0].old_str).toBe("old2");
      expect(result[1].old_str).toBe("old1");
      // Entry without line numbers should be at the end
      expect(result[2].old_str).toBe("old3");
    });

    it("should preserve original indices after sorting", () => {
      const entries = [
        {
          index: 0,
          old_str: "old1",
          new_str: "new1",
          old_str_start_line_number: 1,
          old_str_end_line_number: 3,
        },
        {
          index: 1,
          old_str: "old2",
          new_str: "new2",
          old_str_start_line_number: 5,
          old_str_end_line_number: 7,
        },
      ];
      const result = prepareStrReplaceEntries(entries);
      // Even though entries are sorted, their original indices should be preserved
      expect(result[0].index).toBe(1); // original index of the second entry
      expect(result[1].index).toBe(0); // original index of the first entry
    });
  });

  describe("findOverlappingEntry", () => {
    it("should return undefined when no overlap exists", () => {
      const entries: StrReplaceEntry[] = [
        {
          index: 0,
          old_str: "old1",
          new_str: "new1",
          old_str_start_line_number: 1,
          old_str_end_line_number: 3,
        },
        {
          index: 1,
          old_str: "old2",
          new_str: "new2",
          old_str_start_line_number: 4,
          old_str_end_line_number: 6,
        },
      ];
      const result = findOverlappingEntry(entries[0], entries);
      expect(result).toBeUndefined();
    });

    it("should find overlapping entry when start line overlaps", () => {
      const entries: StrReplaceEntry[] = [
        {
          index: 0,
          old_str: "old1",
          new_str: "new1",
          old_str_start_line_number: 1,
          old_str_end_line_number: 5,
        },
        {
          index: 1,
          old_str: "old2",
          new_str: "new2",
          old_str_start_line_number: 3,
          old_str_end_line_number: 7,
        },
      ];
      const result = findOverlappingEntry(entries[0], entries);
      expect(result).toBe(entries[1]);
    });

    it("should find overlapping entry when end line overlaps", () => {
      const entries: StrReplaceEntry[] = [
        {
          index: 0,
          old_str: "old1",
          new_str: "new1",
          old_str_start_line_number: 5,
          old_str_end_line_number: 10,
        },
        {
          index: 1,
          old_str: "old2",
          new_str: "new2",
          old_str_start_line_number: 1,
          old_str_end_line_number: 7,
        },
      ];
      const result = findOverlappingEntry(entries[0], entries);
      expect(result).toBe(entries[1]);
    });

    it("should ignore entries without line numbers", () => {
      const entries: StrReplaceEntry[] = [
        {
          index: 0,
          old_str: "old1",
          new_str: "new1",
          old_str_start_line_number: 1,
          old_str_end_line_number: 3,
        },
        {
          index: 1,
          old_str: "old2",
          new_str: "new2",
        },
      ];
      const result = findOverlappingEntry(entries[0], entries);
      expect(result).toBeUndefined();
    });

    it("should ignore the entry itself when checking for overlaps", () => {
      const entries: StrReplaceEntry[] = [
        {
          index: 0,
          old_str: "old1",
          new_str: "new1",
          old_str_start_line_number: 1,
          old_str_end_line_number: 3,
        },
      ];
      const result = findOverlappingEntry(entries[0], entries);
      expect(result).toBeUndefined();
    });

    it("should return undefined when current entry has no line numbers", () => {
      const entries: StrReplaceEntry[] = [
        {
          index: 0,
          old_str: "old1",
          new_str: "new1",
        },
        {
          index: 1,
          old_str: "old2",
          new_str: "new2",
          old_str_start_line_number: 1,
          old_str_end_line_number: 3,
        },
      ];
      const result = findOverlappingEntry(entries[0], entries);
      expect(result).toBeUndefined();
    });
  });

  describe("genOverlappingEntryErrorMessage", () => {
    it("should generate correct error message with 1-based line numbers", () => {
      const currentEntry: StrReplaceEntry = {
        index: 0,
        old_str: "old1",
        new_str: "new1",
        old_str_start_line_number: 1, // 0-based
        old_str_end_line_number: 3, // 0-based
      };
      const overlappingEntry: StrReplaceEntry = {
        index: 1,
        old_str: "old2",
        new_str: "new2",
        old_str_start_line_number: 2, // 0-based
        old_str_end_line_number: 5, // 0-based
      };
      const message = genOverlappingEntryErrorMessage(
        currentEntry,
        overlappingEntry,
      );
      expect(message).toContain("This entry range: [2-4]");
      expect(message).toContain("Overlapping entry index: 1");
      expect(message).toContain("Overlapping entry range: [3-6]");
    });
  });

  describe("extractStrReplaceEntries", () => {
    it("should extract a single entry without index", () => {
      const toolInput = {
        old_str: "old content",
        new_str: "new content",
        old_str_start_line_number: 5,
        old_str_end_line_number: 10,
      };

      const entries = extractStrReplaceEntries(toolInput);

      expect(entries).toHaveLength(1);
      expect(entries[0]).toEqual({
        old_str: "old content",
        new_str: "new content",
        old_str_start_line_number: 5,
        old_str_end_line_number: 10,
        index: 0,
      });
    });

    it("should extract a single entry without line numbers", () => {
      const toolInput = {
        old_str: "old content",
        new_str: "new content",
      };

      const entries = extractStrReplaceEntries(toolInput);

      expect(entries).toHaveLength(1);
      expect(entries[0]).toEqual({
        old_str: "old content",
        new_str: "new content",
        index: 0,
      });
    });

    it("should extract multiple indexed entries", () => {
      const toolInput = {
        old_str_1: "old content 1",
        new_str_1: "new content 1",
        old_str_start_line_number_1: 5,
        old_str_end_line_number_1: 10,
        old_str_2: "old content 2",
        new_str_2: "new content 2",
        old_str_start_line_number_2: 15,
        old_str_end_line_number_2: 20,
      };

      const entries = extractStrReplaceEntries(toolInput);

      expect(entries).toHaveLength(2);
      expect(entries[0]).toEqual({
        old_str: "old content 1",
        new_str: "new content 1",
        old_str_start_line_number: 5,
        old_str_end_line_number: 10,
        index: 1,
      });
      expect(entries[1]).toEqual({
        old_str: "old content 2",
        new_str: "new content 2",
        old_str_start_line_number: 15,
        old_str_end_line_number: 20,
        index: 2,
      });
    });

    it("should extract both non-indexed and indexed entries", () => {
      const toolInput = {
        old_str: "old content",
        new_str: "new content",
        old_str_start_line_number: 5,
        old_str_end_line_number: 10,
        old_str_1: "old content 1",
        new_str_1: "new content 1",
        old_str_start_line_number_1: 15,
        old_str_end_line_number_1: 20,
      };

      const entries = extractStrReplaceEntries(toolInput);

      expect(entries).toHaveLength(2);
      expect(entries[0]).toEqual({
        old_str: "old content",
        new_str: "new content",
        old_str_start_line_number: 5,
        old_str_end_line_number: 10,
        index: 0,
      });
      expect(entries[1]).toEqual({
        old_str: "old content 1",
        new_str: "new content 1",
        old_str_start_line_number: 15,
        old_str_end_line_number: 20,
        index: 1,
      });
    });

    it("should handle entries with partial line numbers", () => {
      const toolInput = {
        old_str_1: "old content 1",
        new_str_1: "new content 1",
        old_str_start_line_number_1: 5,
        // Missing old_str_end_line_number_1
        old_str_2: "old content 2",
        new_str_2: "new content 2",
        // Missing old_str_start_line_number_2
        old_str_end_line_number_2: 20,
      };

      const entries = extractStrReplaceEntries(toolInput);

      expect(entries).toHaveLength(2);
      expect(entries[0]).toEqual({
        old_str: "old content 1",
        new_str: "new content 1",
        old_str_start_line_number: 5,
        index: 1,
      });
      expect(entries[1]).toEqual({
        old_str: "old content 2",
        new_str: "new content 2",
        old_str_end_line_number: 20,
        index: 2,
      });
    });

    it("should return an empty array when no entries are found", () => {
      const toolInput = {
        command: "str_replace",
        path: "test.txt",
        // No old_str or new_str entries
      };

      const entries = extractStrReplaceEntries(toolInput);

      expect(entries).toHaveLength(0);
    });

    it("should handle non-consecutive indices", () => {
      const toolInput = {
        old_str_1: "old content 1",
        new_str_1: "new content 1",
        old_str_start_line_number_1: 5,
        old_str_end_line_number_1: 10,
        old_str_3: "old content 3",
        new_str_3: "new content 3",
        old_str_start_line_number_3: 15,
        old_str_end_line_number_3: 20,
        // Missing index 2
      };

      const entries = extractStrReplaceEntries(toolInput);

      expect(entries).toHaveLength(2);
      expect(entries[0]).toEqual({
        old_str: "old content 1",
        new_str: "new content 1",
        old_str_start_line_number: 5,
        old_str_end_line_number: 10,
        index: 1,
      });
      expect(entries[1]).toEqual({
        old_str: "old content 3",
        new_str: "new content 3",
        old_str_start_line_number: 15,
        old_str_end_line_number: 20,
        index: 3,
      });
    });
  });

  describe("extractInsertLineEntries", () => {
    it("should extract a single entry without index", () => {
      const toolInput = {
        insert_line: 5,
        new_str: "new content",
      };

      const entries = extractInsertLineEntries(toolInput);

      expect(entries).toHaveLength(1);
      expect(entries[0]).toEqual({
        insert_line: 5,
        new_str: "new content",
        index: 0,
      });
    });

    it("should extract multiple indexed entries", () => {
      const toolInput = {
        insert_line_1: 5,
        new_str_1: "new content 1",
        insert_line_2: 10,
        new_str_2: "new content 2",
      };

      const entries = extractInsertLineEntries(toolInput);

      expect(entries).toHaveLength(2);
      expect(entries[0]).toEqual({
        insert_line: 5,
        new_str: "new content 1",
        index: 1,
      });
      expect(entries[1]).toEqual({
        insert_line: 10,
        new_str: "new content 2",
        index: 2,
      });
    });

    it("should extract both non-indexed and indexed entries", () => {
      const toolInput = {
        insert_line: 5,
        new_str: "new content",
        insert_line_1: 10,
        new_str_1: "new content 1",
      };

      const entries = extractInsertLineEntries(toolInput);

      expect(entries).toHaveLength(2);
      expect(entries[0]).toEqual({
        insert_line: 5,
        new_str: "new content",
        index: 0,
      });
      expect(entries[1]).toEqual({
        insert_line: 10,
        new_str: "new content 1",
        index: 1,
      });
    });

    it("should return an empty array when no entries are found", () => {
      const toolInput = {
        command: "insert",
        path: "test.txt",
        // No insert_line or new_str entries
      };

      const entries = extractInsertLineEntries(toolInput);

      expect(entries).toHaveLength(0);
    });

    it("should handle non-consecutive indices", () => {
      const toolInput = {
        insert_line_1: 5,
        new_str_1: "new content 1",
        insert_line_3: 15,
        new_str_3: "new content 3",
        // Missing index 2
      };

      const entries = extractInsertLineEntries(toolInput);

      expect(entries).toHaveLength(2);
      expect(entries[0]).toEqual({
        insert_line: 5,
        new_str: "new content 1",
        index: 1,
      });
      expect(entries[1]).toEqual({
        insert_line: 15,
        new_str: "new content 3",
        index: 3,
      });
    });
  });
});

describe("updateEditResultLineNumbers", () => {
  it("should update line numbers correctly for line breaks", () => {
    // Setup
    const beforeFormatting =
      "line1\nlong line that will be broken into multiple lines\nline3\nline4\nline5";
    const afterFormatting =
      "line1\nlong line that will\n be broken into multiple lines\nline3\nline4\nline5";

    // Create an EditResult with initial line numbers
    const result: EditResult = {
      isError: false,
      oldStr: "<not used>",
      newContent: beforeFormatting,
      newStr: "long line that will be broken into multiple lines\nline3",
      newStrStartLineNumber: 1,
      newStrEndLineNumber: 2,
      numLinesDiff: 0,
      index: 0,
    };

    updateEditResultLineNumbers(result, afterFormatting);

    expect(result.newStrStartLineNumber).toBe(1);
    expect(result.newStrEndLineNumber).toBe(3);
  });
});

describe("JSON object utility functions", () => {
  describe("removeAllIndents", () => {
    it("should remove all indentation from text", () => {
      const input = "  line 1\n    line 2\n\tline 3\n\t\tline 4";
      const expected = "line 1\nline 2\nline 3\nline 4";
      expect(removeAllIndents(input)).toBe(expected);
    });

    it("should handle empty lines", () => {
      const input = "  line 1\n\n    line 3";
      const expected = "line 1\n\nline 3";
      expect(removeAllIndents(input)).toBe(expected);
    });

    it("should handle lines with only whitespace", () => {
      const input = "  line 1\n   \n    line 3";
      const expected = "line 1\n\nline 3";
      expect(removeAllIndents(input)).toBe(expected);
    });

    it("should handle empty input", () => {
      expect(removeAllIndents("")).toBe("");
    });

    it("should handle single line", () => {
      const input = "    single line";
      const expected = "single line";
      expect(removeAllIndents(input)).toBe(expected);
    });
  });

  describe("serializeWithIndent", () => {
    it("should serialize object with space indentation", () => {
      const obj = { key: "value", nested: { inner: "data" } };
      const baseIndent = "  ";
      const indentChar = "  ";

      const result = serializeWithIndent(obj, baseIndent, indentChar, true);

      expect(result).toContain("  {");
      expect(result).toContain('    "key": "value",');
      expect(result).toContain("  }");
    });

    it("should serialize object with tab indentation", () => {
      const obj = { key: "value" };
      const baseIndent = "\t";
      const indentChar = "\t";

      const result = serializeWithIndent(obj, baseIndent, indentChar, true);

      expect(result).toContain("\t{");
      expect(result).toContain('\t\t"key": "value"');
      expect(result).toContain("\t}");
    });

    it("should handle empty base indentation", () => {
      const obj = { key: "value" };
      const baseIndent = "";
      const indentChar = "  ";

      const result = serializeWithIndent(obj, baseIndent, indentChar, true);

      expect(result).toContain("{");
      expect(result).toContain('  "key": "value"');
      expect(result).toContain("}");
    });

    it("should handle arrays", () => {
      const obj = ["item1", "item2"];
      const baseIndent = "  ";
      const indentChar = "  ";

      const result = serializeWithIndent(obj, baseIndent, indentChar, true);

      expect(result).toContain("  [");
      expect(result).toContain('    "item1",');
      expect(result).toContain("  ]");
    });

    it("should handle shouldIndentFirstLine=false", () => {
      const obj = { key: "value" };
      const baseIndent = "  ";
      const indentChar = "  ";

      const result = serializeWithIndent(obj, baseIndent, indentChar, false);

      expect(result).toMatch(/^{/); // First line should not be indented
      expect(result).toContain('    "key": "value"');
      expect(result).toContain("  }");
    });
  });

  describe("createJsonObjectErrorResult", () => {
    it("should create error result with all parameters", () => {
      const result = createJsonObjectErrorResult(1, "old string", 5, 10);

      expect(result.isError).toBe(true);
      expect(result.index).toBe(1);
      expect(result.oldStr).toBe("old string");
      expect(result.oldStrStartLineNumber).toBe(5);
      expect(result.oldStrEndLineNumber).toBe(10);
      expect(result.numLinesDiff).toBe(0);
      expect(result.genMessageFunc).toBeDefined();

      const message = result.genMessageFunc!(result);
      expect(message).toBe(
        "Invalid parameter `old_str` for `str_replace` command. It must be a string.",
      );
    });

    it("should create error result without line numbers", () => {
      const result = createJsonObjectErrorResult(0, "old string");

      expect(result.isError).toBe(true);
      expect(result.index).toBe(0);
      expect(result.oldStr).toBe("old string");
      expect(result.oldStrStartLineNumber).toBeUndefined();
      expect(result.oldStrEndLineNumber).toBeUndefined();
      expect(result.numLinesDiff).toBe(0);
    });
  });
});
