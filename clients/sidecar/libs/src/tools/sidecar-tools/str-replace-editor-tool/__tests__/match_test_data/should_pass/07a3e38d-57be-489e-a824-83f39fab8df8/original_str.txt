package server

import (
	"context"
	"crypto/md5"
	"encoding/hex"
	"math/rand"
	"strings"
	"sync"
	"time"

	featureflags "github.com/augmentcode/augment/base/feature_flags"
	proto "github.com/augmentcode/augment/services/chat_arbiter/proto"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	"github.com/rs/zerolog/log"
)

// Feature flags for load balancing
var (
	chatAnthropicDirectRate = featureflags.NewFloatFlag(
