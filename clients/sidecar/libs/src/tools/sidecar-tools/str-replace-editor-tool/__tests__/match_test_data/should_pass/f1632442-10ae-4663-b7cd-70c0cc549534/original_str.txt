import typing

import grpc

from services.chat_arbiter import chat_arbiter_pb2
from services.chat_arbiter import chat_arbiter_pb2_grpc
from services.lib.request_context.request_context import RequestContext


class ChatArbiterClient(typing.Protocol):
    """Interface for the Chat Arbiter service."""

    def get_target(
        self,
        request_context: RequestContext,
    ) -> chat_arbiter_pb2.GetTargetResponse:
        raise NotImplementedError()


class GrpcChatArbiterClientImpl:
    """Class to call Chat Arbiter APIs remotely."""

    def __init__(self, endpoint: str, credentials: grpc.ChannelCredentials | None):
        self.endpoint = endpoint
        if credentials:
            channel = grpc.secure_channel(endpoint, credentials)
        else:
            channel = grpc.insecure_channel(endpoint)
