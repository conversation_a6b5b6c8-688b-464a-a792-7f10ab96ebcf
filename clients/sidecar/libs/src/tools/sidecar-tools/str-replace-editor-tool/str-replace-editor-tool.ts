import { Exchange } from "../../../chat/chat-types";
import { DiffViewDocument } from "../../../diff-view/document";
import { getLogger, type AugmentLogger } from "../../../logging";
import { createRequestId } from "../../../utils/request-id";
import { QualifiedPathName } from "../../../workspace/qualified-path-name";
import { ToolBase, ToolSafety, ToolUseResponse } from "../../tool-types";
import { SidecarToolType } from "../sidecar-tool-types";
import { errorToolResponse, successToolResponse } from "../tool-use-response";
import { AggregateCheckpointManager } from "../../../agent/checkpoint/aggregate-checkpoint-manager";
import {
  detectIndentation,
  allLinesHaveIndent,
  prepareTextForEditing,
  removeOneIndentLevel,
  findMatches,
  findClosestMatch,
  createSnippetStr,
  findOverlappingEntry,
  genOverlappingEntryErrorMessage,
  prepareStrReplaceEntries,
  EditResult,
  updateEditResultLineNumbers,
  createSnippet,
  deduceCommand,
  Match,
  removeTrailingWhitespace,
  removeAllIndents,
  serializeWithIndent,
  createJsonObjectErrorResult,
} from "./utils";
import { createTwoFilesPatch } from "diff";
import { delayMs } from "@augment-internal/sidecar-libs/src/utils/promise-utils";
import { getClientFeatureFlags } from "@augment-internal/sidecar-libs/src/client-interfaces/feature-flags";
import {
  InsertLineEntry,
  IStrReplaceEditorToolDefinition,
  StrReplaceEntry,
} from "./tool-definition";
import { StrReplaceEditorToolDefinitionFlat } from "./tool-definition-flat";
import { StrReplaceEditorToolDefinitionNested } from "./tool-definition-nested";
import { fuzzyMatchReplacementStrings } from "./match-utils";
import {
  readFile,
  validatePath,
  restoreLineEndings,
  detectLineEnding,
  normalizeLineEndings,
} from "../file-utils";
import { ViewTool } from "../view-tool/view-tool";
import { StrReplaceEditorToolDefinitionFlatWithReminder } from "./tool-definition-flat-with-reminder";

const SNIPPET_CONTEXT_LINES = 4;
const SNIPPET_CONTEXT_LINES_FOR_FUZZY_MATCH = 10;

type ValidFileDetails = {
  contents: string;
  filepath?: QualifiedPathName;
};

export class StrReplaceEditorTool extends ToolBase<SidecarToolType> {
  private readonly _logger: AugmentLogger;
  private readonly _toolDefinition: IStrReplaceEditorToolDefinition;
  private readonly _viewTool: ViewTool;
  protected _abortSignal: AbortSignal | undefined = undefined;
  private _lastEditSourceToolCallRequestId: string | undefined = undefined;
  private _lastEditPath: QualifiedPathName | undefined = undefined;

  /**
   * @param _lineNumberErrorTolerance Tolerance for line number errors when matching text.
   * If A and B are two matches closest to the specified line number:
   * - 0: Numbers must match exactly
   * - 1: Even if line number is exactly between two matches, it's considered a match
   * - 0.2: If line number is within 10% of distance between matches, it's considered a match
   * Exact integer threshold is rounded down
   */
  constructor(
    private readonly _checkpointManager: AggregateCheckpointManager,
    private readonly _lineNumberErrorTolerance: number = 0.2,
    private readonly _waitForAutoFormatMs: number = 1000,
    toolDefinition?: IStrReplaceEditorToolDefinition,
  ) {
    super(SidecarToolType.strReplaceEditor, ToolSafety.Safe);

    if (toolDefinition) {
      this._toolDefinition = toolDefinition;
    } else {
      const flags = getClientFeatureFlags().flags;
      if (
        flags.agentEditToolSchemaType === "StrReplaceEditorToolDefinitionFlat"
      ) {
        if (flags.agentEditToolInstructionsReminder) {
          this._toolDefinition =
            new StrReplaceEditorToolDefinitionFlatWithReminder();
        } else {
          this._toolDefinition = new StrReplaceEditorToolDefinitionFlat();
        }
      } else {
        this._toolDefinition = new StrReplaceEditorToolDefinitionNested();
      }
    }

    this.description = this._toolDefinition.description;
    this.inputSchemaJson = this._toolDefinition.inputSchemaJson;

    this._viewTool = new ViewTool();

    this._logger = getLogger("StrReplaceEditorTool");
    this._logger.debug(
      `Initialized with params: lineNumberErrorTolerance=${_lineNumberErrorTolerance}, waitForAutoFormatSec=${_waitForAutoFormatMs}`,
    );
  }

  public readonly description: string;
  public readonly inputSchemaJson: string;

  public checkToolCallSafe(_toolInput: Record<string, unknown>): boolean {
    return true;
  }

  private async createCheckpoint(
    pathName: QualifiedPathName,
    content: string,
    newContent: string,
    chatHistory: Exchange[],
  ): Promise<void> {
    this._logger.debug(`Creating checkpoint for file: ${pathName.absPath}`);
    const document = new DiffViewDocument(pathName, content, newContent, {});

    // Get the exchange ID that resulted in this tool call.
    const toolUseExchangeRequestId =
      chatHistory.at(-1)?.request_id ?? createRequestId();
    const conversationId = this._checkpointManager.currentConversationId ?? "";
    this._logger.debug(
      `Adding checkpoint with conversationId: ${conversationId}, requestId: ${toolUseExchangeRequestId}`,
    );
    this._lastEditSourceToolCallRequestId = toolUseExchangeRequestId;
    this._lastEditPath = pathName;
    await this._checkpointManager.addCheckpoint(
      {
        conversationId,
        path: pathName,
      },
      {
        sourceToolCallRequestId: toolUseExchangeRequestId,
        timestamp: Date.now(),
        document,
        conversationId,
      },
    );
  }

  public async call(
    toolInput: Record<string, unknown>,
    chatHistory: Exchange[],
    abortSignal: AbortSignal,
    _toolUseId: string,
  ): Promise<ToolUseResponse> {
    this._abortSignal = abortSignal;
    this._lastEditPath = undefined;
    this._lastEditSourceToolCallRequestId = undefined;
    try {
      const command = deduceCommand(toolInput, this._logger);

      this._logger.debug(
        `Tool called with command: ${String(command)}, path: ${String(toolInput.path)}`,
      );

      const filePath = validatePath(toolInput.path);
      const fileDetails = await readFile(filePath);

      // Use the corrected path if available, otherwise use the original path
      const actualPath: string = fileDetails.correctedPath || filePath;

      let response: ToolUseResponse;
      switch (command) {
        // Keeping logic to handle view in this tool for backwards compatibility
        case "view": {
          this._logger.debug(
            `Handling 'view' command for ${actualPath} with range: ${JSON.stringify(toolInput.view_range)}`,
          );
          const viewToolInput = {
            path: actualPath,
            view_range: toolInput.view_range,
            type: "file",
          };
          response = await this._viewTool.call(
            viewToolInput,
            chatHistory,
            abortSignal,
          );
          break;
        }
        case "str_replace": {
          this._logger.debug(
            `Handling 'str_replace' command for ${actualPath} with ${Array.isArray(toolInput.str_replace_entries) ? toolInput.str_replace_entries.length : 0} entries`,
          );
          response = await this.handleStrReplace(
            actualPath,
            fileDetails,
            this._toolDefinition.extractStrReplaceEntries(toolInput),
            chatHistory,
          );
          break;
        }
        case "insert": {
          this._logger.debug(
            `Handling 'insert' command for ${actualPath} with ${Array.isArray(toolInput.insert_line_entries) ? toolInput.insert_line_entries.length : 0} entries`,
          );
          response = await this.handleInsert(
            actualPath,
            fileDetails,
            this._toolDefinition.extractInsertLineEntries(toolInput),
            chatHistory,
          );
          break;
        }
        default:
          throw new Error(`Unknown command: ${String(command)}`);
      }
      if (fileDetails.correctionNote) {
        response.text = fileDetails.correctionNote + "\n\n" + response.text;
      }
      return response;
    } catch (error: unknown) {
      this._logger.error(
        `Error in tool call: ${error instanceof Error ? error.message : String(error)}`,
      );
      if (error instanceof Error) {
        return errorToolResponse(error.message);
      } else {
        return errorToolResponse(`Unknown error: ${String(error)}`);
      }
    }
  }

  private async prepareToolResponse(
    path: string,
    fileDetails: ValidFileDetails,
    originalContent: string,
    originalLineEnding: string,
    newContent: string,
    chatHistory: Exchange[],
    command: "str_replace" | "insert",
    results: Array<EditResult>,
  ): Promise<ToolUseResponse> {
    this._logger.debug(
      `Preparing tool response for ${command} command on ${path}`,
    );
    // Restore the original line endings
    const originalContentWithLineEndings = restoreLineEndings(
      originalContent,
      originalLineEnding,
    );
    const newContentWithLineEndings = restoreLineEndings(
      newContent,
      originalLineEnding,
    );

    if (this._abortSignal?.aborted) {
      this._logger.debug("Tool call was cancelled.");
      return errorToolResponse("Tool call was cancelled.");
    }

    if (originalContentWithLineEndings !== newContentWithLineEndings) {
      this._logger.debug(
        "Content changed, creating checkpoint and waiting for auto-formatting",
      );
      await this.createCheckpoint(
        fileDetails.filepath as QualifiedPathName,
        originalContentWithLineEndings,
        newContentWithLineEndings,
        chatHistory,
      );

      // Wait for auto-formatting to kick in
      await delayMs(this._waitForAutoFormatMs);
      const contentAfterAutoFormat = (await readFile(path)).contents;
      const wasReformatted =
        contentAfterAutoFormat !== newContentWithLineEndings;
      if (wasReformatted) {
        results.forEach((result) => {
          result.wasReformattedByIDE = true;
        });
        this._logger.debug("File was auto-formatted after edit");
      }

      // shift line numbers to reflect all edits
      results.sort(
        (a, b) =>
          (a.newStrStartLineNumber ?? -1) - (b.newStrStartLineNumber ?? -1),
      );
      let lineShift = 0;
      for (const result of results) {
        if (
          result.newStrStartLineNumber !== undefined &&
          result.newStrEndLineNumber !== undefined
        ) {
          this._logger.debug(
            `Adjusting line numbers for result: start=${result.newStrStartLineNumber}, end=${result.newStrEndLineNumber}, shift=${lineShift}`,
          );
          result.newStrStartLineNumber += lineShift;
          result.newStrEndLineNumber += lineShift;
          // update newContent to be in sync with new line numbers
          result.newContent = newContent;
          if (wasReformatted) {
            updateEditResultLineNumbers(result, contentAfterAutoFormat);
            this._logger.debug(
              `Updated line numbers after reformatting: start=${result.newStrStartLineNumber}, end=${result.newStrEndLineNumber}`,
            );
          }
          lineShift += result.numLinesDiff;
        }

        // update newContent to reflect the final version
        result.newContent = contentAfterAutoFormat;
      }
    } else {
      this._logger.debug("No content changes detected");
    }

    const isAllErrors = results.every((result) => result.isError);
    const isAllSuccess = results.every((result) => !result.isError);
    this._logger.debug(
      `Results summary: all errors=${isAllErrors}, all success=${isAllSuccess}, total results=${results.length}`,
    );

    const resultMessages = results.map((result) => {
      const message = result.genMessageFunc
        ? result.genMessageFunc(result)
        : "No message available";
      return `Result for ${command} for entry with index [${result.index}]:\n${message}\n`;
    });
    const resultMessagesStr = resultMessages.join("\n");

    let summaryMessage: string;
    if (isAllErrors) {
      summaryMessage = `Failed to edit the file ${path}. See below for details.
${resultMessagesStr}
Fix failed ${command} entries accordingly and try again.
`;
    } else if (isAllSuccess) {
      summaryMessage = `Successfully edited the file ${path}.
${resultMessagesStr}
Review the changes and make sure they are as expected (correct indentation, no duplicate lines, etc).
Edit the file again if necessary.
`;
    } else {
      summaryMessage = `Partially edited the file ${path}. See below for details.
${resultMessagesStr}
Fix failed ${command} entries accordingly and try again.
`;
    }

    if (this._abortSignal?.aborted) {
      this._logger.debug("Tool call was cancelled. Reverting the change");
      await this.revertCheckpoint();
      return errorToolResponse("Tool call was cancelled.");
    }

    if (isAllErrors) {
      this._logger.debug(`Returning error response for ${path}`);
      return errorToolResponse(summaryMessage);
    } else {
      this._logger.debug(`Returning success response for ${path}`);
      return successToolResponse(summaryMessage);
    }
  }

  protected async revertCheckpoint() {
    if (
      this._lastEditPath === undefined ||
      this._lastEditSourceToolCallRequestId === undefined
    ) {
      this._logger.debug(
        "Cannot revert. Last edit path or sourceToolCallRequestId is undefined",
      );
      return;
    }
    await this._checkpointManager.removeDocumentLastCheckpoint(
      this._lastEditPath,
      this._lastEditSourceToolCallRequestId,
    );
    this._lastEditPath = undefined;
    this._lastEditSourceToolCallRequestId = undefined;
  }

  private tryTabIndentFix(
    content: string,
    oldStr: string,
    newStr: string,
  ): {
    matches: Match[];
    oldStr: string;
    newStr: string;
  } {
    // Handle the special case for tab indentation
    // Claude often adds an extra tab to both old_str and new_str
    const contentIndentation = detectIndentation(content);
    const oldStrIndentation = detectIndentation(oldStr);
    const newStrIndentation = detectIndentation(newStr);

    // Only apply tab indentation fix when all three have tab indentation
    if (
      contentIndentation.type === "tab" &&
      oldStrIndentation.type === "tab" &&
      (newStrIndentation.type === "tab" || newStr.trim() === "") &&
      allLinesHaveIndent(oldStr, contentIndentation) &&
      allLinesHaveIndent(newStr, contentIndentation)
    ) {
      // Remove only one level of indentation from both strings
      const currentOldStr = removeOneIndentLevel(oldStr, contentIndentation);
      const currentNewStr = removeOneIndentLevel(newStr, contentIndentation);

      // Check if we have a match now
      const matches = findMatches(content, currentOldStr);
      if (matches.length > 0) {
        return { matches, oldStr: currentOldStr, newStr: currentNewStr };
      }
    }
    return { matches: [], oldStr, newStr };
  }

  private tryFuzzyMatching(
    content: string,
    oldStr: string,
    newStr: string,
    oldStrStartLineNumber: number | undefined,
    oldStrEndLineNumber: number | undefined,
  ): { matches: Match[]; oldStr: string; newStr: string } {
    const featureFlags = getClientFeatureFlags().flags;
    if (
      featureFlags.agentEditToolEnableFuzzyMatching &&
      oldStrStartLineNumber !== undefined &&
      oldStrEndLineNumber !== undefined
    ) {
      this._logger.debug(
        `No verbatim match found for old_str. Trying fuzzy matching...`,
      );
      // Crop code snippet around provided line numbers
      const snippet = createSnippet(
        content,
        oldStrStartLineNumber,
        oldStrEndLineNumber - oldStrStartLineNumber + 1,
        SNIPPET_CONTEXT_LINES_FOR_FUZZY_MATCH,
      ).snippet;

      const matchResult = fuzzyMatchReplacementStrings(
        snippet,
        oldStr,
        newStr,
        featureFlags.agentEditToolFuzzyMatchMaxDiff,
        featureFlags.agentEditToolFuzzyMatchMaxDiffRatio,
        featureFlags.agentEditToolFuzzyMatchMinAllMatchStreakBetweenDiffs,
      );
      if (
        typeof matchResult === "object" &&
        "oldStr" in matchResult &&
        "newStr" in matchResult
      ) {
        this._logger.debug(`Fuzzy match was successful.`);
        return {
          matches: findMatches(content, matchResult.oldStr),
          oldStr: matchResult.oldStr,
          newStr: matchResult.newStr,
        };
      } else {
        this._logger.debug(`Fuzzy match failed. Reason: ${matchResult}`);
      }
    }
    return { matches: [], oldStr, newStr };
  }

  private singleStrReplace(
    path: string,
    content: string,
    oldStr: string,
    newStr: string,
    index: number,
    // Line numbers here are 0-based
    oldStrStartLineNumber?: number,
    oldStrEndLineNumber?: number,
  ): EditResult {
    const normalizedContent = removeTrailingWhitespace(content);
    oldStr = prepareTextForEditing(oldStr).content;
    newStr = prepareTextForEditing(newStr).content;

    const makeSuccessEditResult = (
      newContent: string,
      newStr: string,
      newStrStartLineNumber: number,
      newStrEndLineNumber: number,
      numLinesDiff: number,
      genMessageFunc: (result: EditResult) => string,
    ): EditResult => {
      return {
        isError: false,
        index: index,
        oldStr: oldStr,
        oldStrStartLineNumber: oldStrStartLineNumber,
        oldStrEndLineNumber: oldStrEndLineNumber,
        newContent: newContent,
        newStr: newStr,
        newStrStartLineNumber: newStrStartLineNumber,
        newStrEndLineNumber: newStrEndLineNumber,
        numLinesDiff: numLinesDiff,
        genMessageFunc: genMessageFunc,
      };
    };
    const makeErrorEditResult = (
      genMessageFunc: (result: EditResult) => string,
    ): EditResult => {
      return {
        isError: true,
        index: index,
        oldStr: oldStr,
        oldStrStartLineNumber: oldStrStartLineNumber,
        oldStrEndLineNumber: oldStrEndLineNumber,
        numLinesDiff: 0,
        genMessageFunc: genMessageFunc,
      };
    };

    let newContent: string | undefined;
    let newStrStartLine: number = 0;
    let newStrEndLine: number = 0;
    let usedFuzzyMatching: boolean = false;

    if (oldStr.trim() === "") {
      if (content.trim() === "") {
        // Allow empty old_str when file is empty or contains only whitespace
        newContent = newStr;
        newStrStartLine = 0;
        newStrEndLine = newStr.split("\n").length - 1;
      } else {
        return makeErrorEditResult(
          () =>
            `No replacement was performed, old_str is empty which is only allowed when the file is empty or contains only whitespace. The file ${path} is not empty.`,
        );
      }
    } else {
      let matches = findMatches(normalizedContent, oldStr);

      if (matches.length === 0) {
        this._logger.debug(
          `No verbatim match found for old_str. Trying tab indentation fix...`,
        );
        const tabIndentFixResult = this.tryTabIndentFix(
          normalizedContent,
          oldStr,
          newStr,
        );
        matches = tabIndentFixResult.matches;
        oldStr = tabIndentFixResult.oldStr;
        newStr = tabIndentFixResult.newStr;
      }

      if (matches.length === 0) {
        this._logger.debug(
          `No verbatim match found for old_str. Trying fuzzy matching...`,
        );
        const fuzzyMatchingResult = this.tryFuzzyMatching(
          normalizedContent,
          oldStr,
          newStr,
          oldStrStartLineNumber,
          oldStrEndLineNumber,
        );
        matches = fuzzyMatchingResult.matches;
        oldStr = fuzzyMatchingResult.oldStr;
        newStr = fuzzyMatchingResult.newStr;
        usedFuzzyMatching = matches.length > 0;
      }

      if (matches.length === 0) {
        const genMessageFunc = (result: EditResult) => {
          let message = `No replacement was performed, oldStr did not appear verbatim in ${path}.`;
          if (
            result.oldStrStartLineNumber !== undefined &&
            result.oldStrEndLineNumber !== undefined
          ) {
            const oldStrRegionSnippet = createSnippetStr(
              content,
              result.oldStrStartLineNumber,
              result.oldStrEndLineNumber - result.oldStrStartLineNumber + 1,
              SNIPPET_CONTEXT_LINES,
            );
            // Create a diff between oldStr and the content in the specified region
            const regionContent = content
              .split("\n")
              .slice(
                result.oldStrStartLineNumber,
                result.oldStrEndLineNumber + 1,
              )
              .join("\n");
            const diff = createTwoFilesPatch(
              "oldStr",
              "regionContent",
              result.oldStr + "\n",
              regionContent + "\n",
              undefined,
              undefined,
              { context: 3 },
            );
            message += `\nThe content in the specified region is:\n${oldStrRegionSnippet}\n\nDiff between oldStr and the specified region is:\n${diff}`;
          }
          return message;
        };

        return makeErrorEditResult(genMessageFunc);
      }

      let matchIndex = -1;
      if (matches.length === 1) {
        matchIndex = 0;
      } else {
        // Multiple matches found - need to disambiguate using line numbers
        if (
          oldStrStartLineNumber === undefined ||
          oldStrEndLineNumber === undefined
        ) {
          return makeErrorEditResult(
            () =>
              `Multiple occurrences of oldStr \`${oldStr}\` found. Please provide line numbers to disambiguate.`,
          );
        }

        // Find the closest match using line numbers and tolerance
        matchIndex = findClosestMatch(
          matches,
          oldStrStartLineNumber,
          oldStrEndLineNumber,
          this._lineNumberErrorTolerance,
        );
      }

      if (matchIndex === -1) {
        // convert to 1-based line numbers for error message
        const startLine = oldStrStartLineNumber! + 1;
        const endLine = oldStrEndLineNumber! + 1;
        return makeErrorEditResult(
          () =>
            `No match found close to the provided line numbers (${startLine}, ${endLine}).`,
        );
      }

      const match = matches[matchIndex];

      // Replace only the specific occurrence
      const contentLines = content.split("\n");
      const normalizedContentLines = normalizedContent.split("\n");

      // Find the specific occurrence in the content to replace
      // We need to handle mid-line replacements properly
      // Use original unnormalized content for before and after lines
      // to preserve original trailing whitespaces
      const linesBeforeMatch = contentLines.slice(0, match.startLine);
      const linesAfterMatch = contentLines.slice(match.endLine + 1);

      // Get the lines that contain the match
      // Use normalized content for match lines to make sure we find the match
      const matchLines = normalizedContentLines
        .slice(match.startLine, match.endLine + 1)
        .join("\n");

      // Find the exact position of oldStr in the matchLines
      const matchPosition = matchLines.indexOf(oldStr);
      if (matchPosition === -1) {
        // This shouldn't happen if findMatches worked correctly
        return makeErrorEditResult(
          () =>
            `Internal error: Could not find the exact position of the match.`,
        );
      }

      // Split the matched content into before, match, and after parts
      const beforeMatch = matchLines.substring(0, matchPosition);
      const afterMatch = matchLines.substring(matchPosition + oldStr.length);

      // Construct the new content by replacing only the matched part
      newContent =
        linesBeforeMatch.join("\n") +
        (linesBeforeMatch.length > 0 ? "\n" : "") +
        beforeMatch +
        newStr +
        afterMatch +
        (linesAfterMatch.length > 0 ? "\n" : "") +
        linesAfterMatch.join("\n");
      newStrStartLine = match.startLine;
      newStrEndLine = match.startLine + newStr.split("\n").length - 1;
    }

    const oldStrLines = oldStr.split("\n").length;
    const newStrLines = newStr.split("\n").length;
    const numLinesDiff = newStrLines - oldStrLines;

    return makeSuccessEditResult(
      newContent,
      newStr,
      newStrStartLine,
      newStrEndLine,
      numLinesDiff,
      (result: EditResult) => {
        return this.genSuccessMessage(result, usedFuzzyMatching);
      },
    );
  }

  private genSuccessMessage(
    result: EditResult,
    usedFuzzyMatching: boolean,
  ): string {
    const flags = getClientFeatureFlags().flags;
    let successMessage = "Replacement successful.";
    if (usedFuzzyMatching) {
      successMessage =
        flags.agentEditToolFuzzyMatchSuccessMessage ?? successMessage;
    }

    // Even if agentEditToolShowResultSnippet is false still show the snippet if the IDE reformatted the code
    if (result.wasReformattedByIDE || flags.agentEditToolShowResultSnippet) {
      const snippetStr = createSnippetStr(
        result.newContent!,
        result.newStrStartLineNumber!,
        result.newStrEndLineNumber! - result.newStrStartLineNumber! + 1,
        SNIPPET_CONTEXT_LINES,
      );
      return `\
${successMessage}
Edited section after IDE auto-formatting was applied:
${snippetStr}`;
    } else {
      // Only show line numbers for new str
      return `\
${successMessage}
new_str starts at line ${result.newStrStartLineNumber! + 1} and ends at line ${result.newStrEndLineNumber! + 1}.`;
    }
  }

  /**
   * Given oldStr and newStr are JSON objects we try to determine the correct indentation
   * and apply it to both oldStr and newStr before calling singleStrReplace
   * We do that by matching unindented strings first
   * Then determine the base indent of the oldStr by looking at the first line of the match in the content
   */
  private singleStrReplaceWithJsonObjects(
    path: string,
    content: string,
    oldStr: string | object,
    newStr: string | object,
    index: number,
    // Line numbers here are 0-based
    oldStrStartLineNumber?: number,
    oldStrEndLineNumber?: number,
  ): EditResult {
    // We only support the case when both old_str and new_str are objects
    if (typeof oldStr !== "object" || typeof newStr !== "object") {
      return createJsonObjectErrorResult(
        index,
        String(oldStr),
        oldStrStartLineNumber,
        oldStrEndLineNumber,
      );
    }

    // Convert old_str to JSON string
    const oldStrJson = JSON.stringify(oldStr, null, 2);

    // Remove all indentation from content and old_str for matching
    const contentNoIndents = removeAllIndents(content);
    const oldStrJsonNoIndents = removeAllIndents(oldStrJson);

    // Find all matches in the indentless content
    const matches = findMatches(contentNoIndents, oldStrJsonNoIndents);

    if (matches.length === 0) {
      return createJsonObjectErrorResult(
        index,
        oldStrJson,
        oldStrStartLineNumber,
        oldStrEndLineNumber,
      );
    }

    // Try each match
    const contentLines = content.split("\n");
    const oldStrFirstLine = oldStrJson.split("\n")[0];
    for (const match of matches) {
      const baseIndent =
        contentLines[match.startLine].match(/^([ \t]*)/)?.[1] ?? "";
      const indentType = detectIndentation(content);
      const singleIndent =
        indentType.type === "tab" ? "\t" : " ".repeat(indentType.size);
      // Check if the first line of old_str is a full line or a suffix of the original line
      const shouldIndentFirstLine =
        contentLines[match.startLine].indexOf(oldStrFirstLine) == 0;

      // Serialize both old and new objects with proper indentation
      const oldStrSerialized = serializeWithIndent(
        oldStr,
        baseIndent,
        singleIndent,
        shouldIndentFirstLine,
      );
      const newStrSerialized = serializeWithIndent(
        newStr,
        baseIndent,
        singleIndent,
        shouldIndentFirstLine,
      );

      // Try the replacement with properly indented strings
      const result = this.singleStrReplace(
        path,
        content,
        oldStrSerialized,
        newStrSerialized,
        index,
        oldStrStartLineNumber,
        oldStrEndLineNumber,
      );

      // If successful, return the result
      if (!result.isError) {
        return result;
      }
    }

    // If no match worked, return error
    return createJsonObjectErrorResult(
      index,
      oldStrJson,
      oldStrStartLineNumber,
      oldStrEndLineNumber,
    );
  }

  protected async handleStrReplace(
    path: string,
    fileDetails: ValidFileDetails,
    strReplaceEntries: Array<StrReplaceEntry>,
    chatHistory: Exchange[],
  ): Promise<ToolUseResponse> {
    this._logger.debug(`Handling str_replace command for ${path}`);
    const strReplaceEntriesWithIndex =
      prepareStrReplaceEntries(strReplaceEntries);

    const originalLineEnding = detectLineEnding(fileDetails.contents);
    const originalContent = normalizeLineEndings(fileDetails.contents);

    let currentContent = originalContent;

    const results = new Map<number, EditResult>();
    for (const entry of strReplaceEntriesWithIndex) {
      const overlappingEntry = findOverlappingEntry(
        entry,
        strReplaceEntriesWithIndex,
      );
      let result;
      if (overlappingEntry !== undefined) {
        result = {
          isError: true,
          index: entry.index,
          oldStr: String(entry.old_str),
          oldStrStartLineNumber: entry.old_str_start_line_number,
          oldStrEndLineNumber: entry.old_str_end_line_number,
          numLinesDiff: 0,
          genMessageFunc: () =>
            genOverlappingEntryErrorMessage(entry, overlappingEntry),
        };
      } else {
        if (
          typeof entry.old_str === "string" &&
          typeof entry.new_str === "string"
        ) {
          result = this.singleStrReplace(
            path,
            currentContent,
            entry.old_str,
            entry.new_str,
            entry.index,
            entry.old_str_start_line_number,
            entry.old_str_end_line_number,
          );
        } else {
          // handle the case when model fails to wrap json object into a string
          // when editing json files
          result = this.singleStrReplaceWithJsonObjects(
            path,
            currentContent,
            entry.old_str,
            entry.new_str,
            entry.index,
            entry.old_str_start_line_number,
            entry.old_str_end_line_number,
          );
        }
      }
      results.set(entry.index, result);

      if (!result.isError && result.newContent !== undefined) {
        currentContent = result.newContent;
      }
    }

    return this.prepareToolResponse(
      path,
      fileDetails,
      originalContent,
      originalLineEnding,
      currentContent,
      chatHistory,
      "str_replace",
      Array.from(results.values()),
    );
  }

  protected async handleInsert(
    path: string,
    fileDetails: ValidFileDetails,
    insertLineEntries: Array<InsertLineEntry>,
    chatHistory: Exchange[],
  ): Promise<ToolUseResponse> {
    this._logger.debug(`Handling insert command for ${path}`);

    const { content: originalContent, originalLineEnding } =
      prepareTextForEditing(fileDetails.contents);

    let currentContent = originalContent;
    let contentLines = currentContent.split("\n");

    // Sort entries by insert_line in descending order to avoid line number shifts
    // This way we insert from bottom to top
    insertLineEntries.sort((a, b) => b.insert_line - a.insert_line);

    const results = new Map<number, EditResult>();

    // Process each insertion entry in order (from bottom to top)
    for (const entry of insertLineEntries) {
      const insertLine = entry.insert_line;
      const newStr = prepareTextForEditing(entry.new_str).content;
      contentLines = currentContent.split("\n");

      // Validate insert line
      if (insertLine < 0 || insertLine > contentLines.length) {
        results.set(entry.index, {
          isError: true,
          index: entry.index,
          oldStr: "",
          oldStrStartLineNumber: insertLine,
          oldStrEndLineNumber: insertLine,
          numLinesDiff: 0,
          genMessageFunc: () =>
            `Invalid \`insert_line\` parameter: ${insertLine}. It should be within the range of lines of the file: [0, ${contentLines.length}]`,
        });
        continue;
      }

      // insertLine is 1-based, so we need to subtract 1
      // insert command docstring says "insert AFTER the line `insert_line`"
      // it is more convenient to insert before the line, so we need subtract 1
      // these two cancel each other out
      // so from now on we can treat insertLine as 0-based

      // Perform the insertion
      const newStrLines = newStr.split("\n");
      const newContentLines = [
        ...contentLines.slice(0, insertLine),
        ...newStrLines,
        ...contentLines.slice(insertLine),
      ];
      currentContent = newContentLines.join("\n");

      results.set(entry.index, {
        isError: false,
        index: entry.index,
        oldStr: "",
        oldStrStartLineNumber: insertLine,
        oldStrEndLineNumber: insertLine,
        newContent: currentContent,
        newStr,
        newStrStartLineNumber: insertLine,
        newStrEndLineNumber: insertLine + newStrLines.length - 1,
        numLinesDiff: newStrLines.length,
        genMessageFunc: (result: EditResult) => {
          const snippetStr = createSnippetStr(
            result.newContent!,
            result.newStrStartLineNumber!,
            result.newStrEndLineNumber! - result.newStrStartLineNumber! + 1,
            SNIPPET_CONTEXT_LINES,
          );
          return `\
Successfully inserted new_str.
Edited section after IDE auto-formatting was applied:
${snippetStr}`;
        },
      });
    }

    return this.prepareToolResponse(
      path,
      fileDetails,
      originalContent,
      originalLineEnding,
      currentContent,
      chatHistory,
      "insert",
      Array.from(results.values()),
    );
  }
}
