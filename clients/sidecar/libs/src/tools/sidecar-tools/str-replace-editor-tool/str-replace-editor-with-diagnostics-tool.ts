import { StrReplaceEditorTool as BaseStrReplaceEditorTool } from "../../../tools/sidecar-tools/str-replace-editor-tool/str-replace-editor-tool";
import { AggregateCheckpointManager } from "../../../agent/checkpoint/aggregate-checkpoint-manager";
import { Diagnostic } from "../../../tools/sidecar-tools/str-replace-editor-tool/diagnostic-types";
import { ToolUseResponse } from "../../../tools/tool-types";
import { formatDiagnostics } from "../../../utils/diagnostics-utils";
import {
  errorToolResponse,
  successToolResponse,
} from "../../../tools/sidecar-tools/tool-use-response";
import { QualifiedPathName } from "../../../workspace/qualified-path-name";
import {
  InsertLineEntry,
  StrReplaceEntry,
} from "../../../tools/sidecar-tools/str-replace-editor-tool/tool-definition";
import { Exchange } from "../../../chat/chat-types";
import { delayMs } from "../../../utils/promise-utils";

/**
 * Implementation of the string replace editor tool that adds diagnostics support.
 */
export class StrReplaceEditorWithDiagnosticsTool extends BaseStrReplaceEditorTool {
  private readonly _maxDiagnosticDelayMs = 3000;

  constructor(
    checkpointManager: AggregateCheckpointManager,
    private readonly _getDiagnostics: () => Map<string, Diagnostic[]>,
    private readonly _readFile: (
      filePath: string,
    ) => Promise<string | undefined>,
    lineNumberErrorTolerance: number = 0.2,
  ) {
    super(checkpointManager, lineNumberErrorTolerance);
  }

  /**
   * Helper method to add diagnostics to a tool response
   */
  private async _addDiagnosticsToResponse(
    baseResponse: ToolUseResponse,
    startingDiagnostics: Map<string, Diagnostic[]>,
  ): Promise<ToolUseResponse> {
    if (baseResponse.isError) {
      return baseResponse;
    }

    try {
      // Wait for new diagnostics to be computed
      const endingDiagnostics =
        await this._waitForNewDiagnostics(startingDiagnostics);

      // Filter to find new diagnostics
      const newDiagnostics = this._filterDiagnosticsMap(
        endingDiagnostics,
        startingDiagnostics,
      );

      // Format diagnostics for display.
      // Note that we convert from 0-based indexing (used by vscode.Position) to 1-based
      // indexing (used by this tool).
      const maxContextLines = 3;
      const maxNumDiagnosticsPerFile = 5;
      const maxDiagnosticLineRange = 10;
      const newDiagnosticsString = await formatDiagnostics(
        newDiagnostics,
        this._readFile,
        maxContextLines,
        maxNumDiagnosticsPerFile,
        maxDiagnosticLineRange,
      );

      // Combine the base response with diagnostics information
      const diagnosticsMessage =
        newDiagnosticsString.trim() === ""
          ? "The IDE reports no new issues."
          : `The IDE reports the following new issues:\n${newDiagnosticsString}`;
      return successToolResponse(
        `${baseResponse.text}\n\n${diagnosticsMessage}`,
      );
    } catch (e: any) {
      // If there's an error getting diagnostics, still return the base response
      return baseResponse;
    }
  }

  /**
   * Override the handleStrReplace method to add diagnostics support
   */
  protected override async handleStrReplace(
    path: string,
    fileDetails: { contents: string; filepath?: QualifiedPathName },
    strReplaceEntries: Array<StrReplaceEntry>,
    chatHistory: Exchange[],
  ): Promise<ToolUseResponse> {
    // Get starting diagnostics before making edits
    const startingDiagnostics = this._getDiagnostics();

    // Call the base implementation to perform the edit
    const baseResponse = await super.handleStrReplace(
      path,
      fileDetails,
      strReplaceEntries,
      chatHistory,
    );

    // Add diagnostics to the response
    const responseWithDiagnostics = await this._addDiagnosticsToResponse(
      baseResponse,
      startingDiagnostics,
    );
    if (this._abortSignal?.aborted) {
      await this.revertCheckpoint();
      return errorToolResponse("Tool call was cancelled.");
    }
    return responseWithDiagnostics;
  }

  /**
   * Override the handleInsert method to add diagnostics support
   */
  protected override async handleInsert(
    path: string,
    fileDetails: { contents: string; filepath?: QualifiedPathName },
    insertLineEntries: Array<InsertLineEntry>,
    chatHistory: Exchange[],
  ): Promise<ToolUseResponse> {
    // Get starting diagnostics before making edits
    const startingDiagnostics = this._getDiagnostics();

    // Call the base implementation to perform the edit
    const baseResponse = await super.handleInsert(
      path,
      fileDetails,
      insertLineEntries,
      chatHistory,
    );

    // Add diagnostics to the response
    const responseWithDiagnostics = await this._addDiagnosticsToResponse(
      baseResponse,
      startingDiagnostics,
    );
    if (this._abortSignal?.aborted) {
      await this.revertCheckpoint();
      return errorToolResponse("Tool call was cancelled.");
    }
    return responseWithDiagnostics;
  }

  /**
   * Filter diagnostics by comparing two maps
   */
  private _filterDiagnosticsMap(
    diagnostics: Map<string, Diagnostic[]>,
    toRemove: Map<string, Diagnostic[]>,
  ): Map<string, Diagnostic[]> {
    const result = new Map<string, Diagnostic[]>();
    diagnostics.forEach((diagnostics, path) => {
      const toRemoveForPath = toRemove.get(path) ?? [];
      const filteredDiagnostics = this._filterDiagnostics(
        diagnostics,
        toRemoveForPath,
      );
      if (filteredDiagnostics.length > 0) {
        result.set(path, filteredDiagnostics);
      }
    });
    return result;
  }

  /**
   * Filter diagnostics by comparing two arrays
   */
  private _filterDiagnostics(
    diagnostics: Diagnostic[],
    toRemove: Diagnostic[],
  ): Diagnostic[] {
    return diagnostics.filter((diagnostic) => {
      return !toRemove.some((d) => {
        return (
          diagnostic.range.start.line === d.range.start.line &&
          diagnostic.range.end.line === d.range.end.line &&
          diagnostic.message === d.message &&
          diagnostic.severity === d.severity
        );
      });
    });
  }

  /**
   * Check if two diagnostic maps are different
   */
  private _hasDifferentDiagnostics(
    diag1: Map<string, Diagnostic[]>,
    diag2: Map<string, Diagnostic[]>,
  ): boolean {
    if (diag1.size !== diag2.size) {
      return true;
    }

    for (const [path, diagnostics] of diag1) {
      const otherDiagnostics = diag2.get(path);
      if (!otherDiagnostics || diagnostics.length !== otherDiagnostics.length) {
        return true;
      }

      for (let i = 0; i < diagnostics.length; i++) {
        const d1 = diagnostics[i];
        const d2 = otherDiagnostics[i];
        if (
          d1.range.start.line !== d2.range.start.line ||
          d1.range.end.line !== d2.range.end.line ||
          d1.message !== d2.message ||
          d1.severity !== d2.severity
        ) {
          return true;
        }
      }
    }
    return false;
  }

  /**
   * Wait for new diagnostics to appear after an edit
   */
  private async _waitForNewDiagnostics(
    startingDiagnostics: Map<string, Diagnostic[]>,
  ): Promise<Map<string, Diagnostic[]>> {
    const startTime = Date.now();
    while (Date.now() - startTime < this._maxDiagnosticDelayMs) {
      const currentDiagnostics = this._getDiagnostics();
      if (
        this._hasDifferentDiagnostics(currentDiagnostics, startingDiagnostics)
      ) {
        return currentDiagnostics;
      }
      if (this._abortSignal?.aborted) {
        break;
      }
      await delayMs(1000); // Poll every second
    }

    // Return the current diagnostics if we timeout
    return this._getDiagnostics();
  }
}
