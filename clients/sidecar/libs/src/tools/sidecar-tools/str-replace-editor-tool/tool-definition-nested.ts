import {
  InsertLineEntry,
  IStrReplaceEditorToolDefinition,
  StrReplaceEntry,
} from "./tool-definition";
import { validateInsertLineEntries, validateStrReplaceEntries } from "./utils";
import { getLogger, type AugmentLogger } from "../../../logging";

export class StrReplaceEditorToolDefinitionNested
  implements IStrReplaceEditorToolDefinition
{
  private readonly _logger: AugmentLogger;

  constructor() {
    this._logger = getLogger("StrReplaceEditorToolDefinitionNested");
  }

  extractStrReplaceEntries(
    toolInput: Record<string, any>,
  ): Array<StrReplaceEntry> {
    const validatedStrReplaceEntries = validateStrReplaceEntries(
      toolInput.str_replace_entries,
    );
    this._logger.debug(
      `Validated ${validatedStrReplaceEntries.length} str_replace entries`,
    );
    return validatedStrReplaceEntries;
  }

  extractInsertLineEntries(
    toolInput: Record<string, any>,
  ): Array<InsertLineEntry> {
    const validatedInsertLineEntries = validateInsertLineEntries(
      toolInput.insert_line_entries,
    );
    this._logger.debug(
      `Validated ${validatedInsertLineEntries.length} insert entries`,
    );
    return validatedInsertLineEntries;
  }

  public readonly description: string = `\
Tool for editing files.
* \`path\` is a file path relative to the workspace root
* \`insert\` and \`str_replace\` commands output a snippet of the edited section for each entry. This snippet reflects the final state of the file after all edits and IDE auto-formatting have been applied.

Notes for using the \`str_replace\` command:
* Use the \`str_replace_entries\` parameter with an array of objects
* Each object should have \`old_str\`, \`new_str\`, \`old_str_start_line_number\` and \`old_str_end_line_number\` properties
* The \`old_str_start_line_number\` and \`old_str_end_line_number\` parameters are 1-based line numbers
* Both \`old_str_start_line_number\` and \`old_str_end_line_number\` are INCLUSIVE
* The \`old_str\` parameter should match EXACTLY one or more consecutive lines from the original file. Be mindful of whitespace!
* Empty \`old_str\` is allowed only when the file is empty or contains only whitespaces
* It is important to specify \`old_str_start_line_number\` and \`old_str_end_line_number\` to disambiguate between multiple occurrences of \`old_str\` in the file
* Make sure that \`old_str_start_line_number\` and \`old_str_end_line_number\` do not overlap with other entries in \`str_replace_entries\`
* The \`new_str\` parameter should contain the edited lines that should replace the \`old_str\`. Can be an empty string to delete content

Notes for using the \`insert\` command:
* Use the \`insert_line_entries\` parameter with an array of objects
* Each object should have \`insert_line\` and \`new_str\` properties
* The \`insert_line\` parameter specifies the line number after which to insert the new string
* The \`insert_line\` parameter is 1-based line number
* To insert at the very beginning of the file, use \`insert_line: 0\`

IMPORTANT:
* This is the only tool you should use for editing files.
* If it fails try your best to fix inputs and retry.
* DO NOT fall back to removing the whole file and recreating it from scratch.
* DO NOT use sed or any other command line tools for editing files.
* Try to fit as many edits in one tool call as possible
* Use the view tool to read files before editing them.
`;

  public readonly inputSchemaJson: string = JSON.stringify({
    type: "object",
    properties: {
      command: {
        type: "string",
        enum: ["str_replace", "insert"],
        description:
          "The commands to run. Allowed options are: 'str_replace', 'insert'.",
      },
      path: {
        description:
          "Full path to file relative to the workspace root, e.g. 'services/api_proxy/file.py' or 'services/api_proxy'.",
        type: "string",
      },
      insert_line_entries: {
        description:
          "Required parameter of `insert` command. A list of entries to insert. Each entry is a dictionary with keys `insert_line` and `new_str`.",
        type: "array",
        items: {
          type: "object",
          properties: {
            insert_line: {
              description:
                "The line number after which to insert the new string. This line number is relative to the state of the file before any insertions in the current tool call have been applied.",
              type: "integer",
            },
            new_str: {
              description: "The string to insert. Can be an empty string.",
              type: "string",
            },
          },
          required: ["insert_line", "new_str"],
        },
      },
      str_replace_entries: {
        description:
          "Required parameter of `str_replace` command. A list of entries to replace. Each entry is a dictionary with keys `old_str`, `old_str_start_line_number`, `old_str_end_line_number` and `new_str`. `old_str` from different entries should not overlap.",
        type: "array",
        items: {
          type: "object",
          properties: {
            old_str: {
              description: "The string in `path` to replace.",
              type: "string",
            },
            old_str_start_line_number: {
              description:
                "The line number of the first line of `old_str` in the file. This is used to disambiguate between multiple occurrences of `old_str` in the file.",
              type: "integer",
            },
            old_str_end_line_number: {
              description:
                "The line number of the last line of `old_str` in the file. This is used to disambiguate between multiple occurrences of `old_str` in the file.",
              type: "integer",
            },
            new_str: {
              description:
                "The string to replace `old_str` with. Can be an empty string to delete content.",
              type: "string",
            },
          },
          required: [
            "old_str",
            "new_str",
            "old_str_start_line_number",
            "old_str_end_line_number",
          ],
        },
      },
    },
    required: ["command", "path"],
  });
}
