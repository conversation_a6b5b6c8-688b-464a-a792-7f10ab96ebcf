import {
  InsertLineEntry,
  IStrReplaceEditorToolDefinition,
  StrReplaceEntry,
} from "./tool-definition";
import {
  extractStrReplaceEntries,
  extractInsertLineEntries,
  validateStrReplaceEntries,
  validateInsertLineEntries,
} from "./utils";
import { getLogger, type AugmentLogger } from "../../../logging";

export class StrReplaceEditorToolDefinitionFlat
  implements IStrReplaceEditorToolDefinition
{
  private readonly _logger: AugmentLogger;

  constructor() {
    this._logger = getLogger("StrReplaceEditorToolDefinitionFlat");
  }

  extractStrReplaceEntries(
    toolInput: Record<string, any>,
  ): Array<StrReplaceEntry> {
    const extractedEntries = extractStrReplaceEntries(toolInput);
    this._logger.debug(
      `Extracted ${extractedEntries.length} str_replace entries from flat schema`,
    );
    const validatedEntries = validateStrReplaceEntries(extractedEntries);
    this._logger.debug(
      `Validated ${validatedEntries.length} str_replace entries from flat schema`,
    );
    return validatedEntries;
  }

  extractInsertLineEntries(
    toolInput: Record<string, any>,
  ): Array<InsertLineEntry> {
    const extractedEntries = extractInsertLineEntries(toolInput);
    this._logger.debug(
      `Extracted ${extractedEntries.length} insert entries from flat schema`,
    );
    const validatedEntries = validateInsertLineEntries(extractedEntries);
    this._logger.debug(
      `Validated ${validatedEntries.length} insert entries from flat schema`,
    );
    return validatedEntries;
  }

  public readonly description: string = `\
Tool for editing files.
* \`path\` is a file path relative to the workspace root
* \`insert\` and \`str_replace\` commands output a snippet of the edited section for each entry. This snippet reflects the final state of the file after all edits and IDE auto-formatting have been applied.

Notes for using the \`str_replace\` command:
* Specify \`old_str_1\`, \`new_str_1\`, \`old_str_start_line_number_1\` and \`old_str_end_line_number_1\` properties for the first replacement, \`old_str_2\`, \`new_str_2\`, \`old_str_start_line_number_2\` and \`old_str_end_line_number_2\` for the second replacement, and so on
* The \`old_str_start_line_number_1\` and \`old_str_end_line_number_1\` parameters are 1-based line numbers
* Both \`old_str_start_line_number_1\` and \`old_str_end_line_number_1\` are INCLUSIVE
* The \`old_str_1\` parameter should match EXACTLY one or more consecutive lines from the original file. Be mindful of whitespace!
* Empty \`old_str_1\` is allowed only when the file is empty or contains only whitespaces
* It is important to specify \`old_str_start_line_number_1\` and \`old_str_end_line_number_1\` to disambiguate between multiple occurrences of \`old_str_1\` in the file
* Make sure that \`old_str_start_line_number_1\` and \`old_str_end_line_number_1\` do not overlap with other \`old_str_start_line_number_2\` and \`old_str_end_line_number_2\` entries
* The \`new_str_1\` parameter should contain the edited lines that should replace the \`old_str_1\`. Can be an empty string to delete content
* To make multiple replacements in one tool call add multiple sets of replacement parameters. For example, \`old_str_1\`, \`new_str_1\`, \`old_str_start_line_number_1\` and \`old_str_end_line_number_1\` properties for the first replacement, \`old_str_2\`, \`new_str_2\`, \`old_str_start_line_number_2\`, \`old_str_end_line_number_2\` for the second replacement, etc.

Notes for using the \`insert\` command:
* Specify \`insert_line_1\` and \`new_str_1\` properties for the first insertion, \`insert_line_2\` and \`new_str_2\` for the second insertion, and so on
* The \`insert_line_1\` parameter specifies the line number after which to insert the new string
* The \`insert_line_1\` parameter is 1-based line number
* To insert at the very beginning of the file, use \`insert_line_1: 0\`
* To make multiple insertions in one tool call add multiple sets of insertion parameters. For example, \`insert_line_1\` and \`new_str_1\` properties for the first insertion, \`insert_line_2\` and \`new_str_2\` for the second insertion, etc.

IMPORTANT:
* This is the only tool you should use for editing files.
* If it fails try your best to fix inputs and retry.
* DO NOT fall back to removing the whole file and recreating it from scratch.
* DO NOT use sed or any other command line tools for editing files.
* Try to fit as many edits in one tool call as possible
* Use the view tool to read files before editing them.
`;

  public readonly inputSchemaJson: string = JSON.stringify({
    type: "object",
    properties: {
      command: {
        type: "string",
        enum: ["str_replace", "insert"],
        description:
          "The commands to run. Allowed options are: 'str_replace', 'insert'.",
      },
      path: {
        description:
          "Full path to file relative to the workspace root, e.g. 'services/api_proxy/file.py' or 'services/api_proxy'.",
        type: "string",
      },
      insert_line_1: {
        description:
          "Required parameter of `insert` command. The line number after which to insert the new string. This line number is relative to the state of the file before any insertions in the current tool call have been applied.",
        type: "integer",
      },
      new_str_1: {
        description:
          "Required parameter of `str_replace` command containing the new string. Can be an empty string to delete content. Required parameter of `insert` command containing the string to insert.",
        type: "string",
      },
      old_str_1: {
        description:
          "Required parameter of `str_replace` command containing the string in `path` to replace.",
        type: "string",
      },
      old_str_start_line_number_1: {
        description:
          "The line number of the first line of `old_str_1` in the file. This is used to disambiguate between multiple occurrences of `old_str_1` in the file.",
        type: "integer",
      },
      old_str_end_line_number_1: {
        description:
          "The line number of the last line of `old_str_1` in the file. This is used to disambiguate between multiple occurrences of `old_str_1` in the file.",
        type: "integer",
      },
    },
    required: ["command", "path"],
  });
}
