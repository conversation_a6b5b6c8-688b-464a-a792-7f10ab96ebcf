/**
 * Generic diagnostic interfaces that can be implemented by different IDEs
 * These match the VSCode diagnostic structure for compatibility
 */

export enum DiagnosticSeverity {
  Error = 0,
  Warning = 1,
  Information = 2,
  Hint = 3,
}

export interface Position {
  /** Line position (0-based) */
  line: number;
  /** Character position (0-based) */
  character: number;
}

export interface Range {
  /** The start position */
  start: Position;
  /** The end position */
  end: Position;
}

export interface Diagnostic {
  /** The diagnostic message */
  message: string;
  /** The severity of the diagnostic */
  severity: DiagnosticSeverity;
  /** The range where the diagnostic applies */
  range: Range;
  /** Optional diagnostic code */
  code?: string | number | { value: string | number; target: string };
  /** Optional source (e.g., "typescript", "eslint") */
  source?: string;
  /** Optional related information */
  relatedInformation?: DiagnosticRelatedInformation[];
  /** Optional tags */
  tags?: DiagnosticTag[];
}

export interface DiagnosticRelatedInformation {
  /** The location of this related diagnostic information */
  location: Location;
  /** The message of this related diagnostic information */
  message: string;
}

export interface Location {
  /** The resource identifier of this location */
  uri: string;
  /** The document range of this location */
  range: Range;
}

export enum DiagnosticTag {
  Unnecessary = 1,
  Deprecated = 2,
}
