export type StrReplaceEntry = {
  index: number;
  old_str: string | object;
  new_str: string | object;
  old_str_start_line_number?: number;
  old_str_end_line_number?: number;
};

export type InsertLineEntry = {
  index: number;
  insert_line: number;
  new_str: string;
};

export interface IStrReplaceEditorToolDefinition {
  description: string;
  inputSchemaJson: string;

  extractStrReplaceEntries(
    toolInput: Record<string, any>,
  ): Array<StrReplaceEntry>;
  extractInsertLineEntries(
    toolInput: Record<string, any>,
  ): Array<InsertLineEntry>;
}
