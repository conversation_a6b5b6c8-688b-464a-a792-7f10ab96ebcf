import { fuzzyMatchLines } from "@augment-internal/sidecar-libs/src/utils/match-lines";
import { InsertLineEntry, StrReplaceEntry } from "./tool-definition";
import { detectLineEnding, normalizeLineEndings } from "../file-utils";
import { AugmentLogger } from "../../../logging";

export type EditResult = {
  isError: boolean;
  // This allows us to generate the result message after postprocessing was done
  // postprocessing includes changes to line numbers and code due to other edits or auto formatting
  // Showing the most recent snippet of the edited section helps the Agent to make more accurate follow up edits
  genMessageFunc?: (result: EditResult) => string;

  // these are just copied from inputs
  // used to generate informative error message
  oldStr: string;
  // 0-based
  oldStrStartLineNumber?: number;
  oldStrEndLineNumber?: number;

  // following fields are only set if isError is false
  // Content after the edit has been applied
  newContent?: string;
  // newStr after any adjustments have been applied
  newStr?: string;
  // newStr line numbers in the edited file. 0-based
  newStrStartLineNumber?: number;
  newStrEndLineNumber?: number;
  // Number of lines added or removed by the edit
  numLinesDiff: number;

  // Index as it was specified in tool input
  index: number;

  wasReformattedByIDE?: boolean;
};

/**
 * Removes trailing whitespace from each line in the text while preserving original line endings
 */
export function removeTrailingWhitespace(text: string): string {
  // Detect line ending (CRLF or LF)
  const lineEnding = text.includes("\r\n") ? "\r\n" : "\n";

  // Split by the detected line ending
  const lines = text.split(lineEnding);

  // Remove trailing whitespace from each line
  const trimmedLines = lines.map((line) => line.replace(/\s+$/, ""));

  // Join back with the original line ending
  return trimmedLines.join(lineEnding);
}

/**
 * Prepares text for editing by normalizing line endings and removing trailing whitespace
 */
export function prepareTextForEditing(text: string): {
  content: string;
  originalLineEnding: string;
} {
  const originalLineEnding = detectLineEnding(text);
  const content = normalizeLineEndings(removeTrailingWhitespace(text));
  return { content, originalLineEnding };
}

/**
 * Creates a snippet of content around a specific line
 */
export function createSnippet(
  content: string,
  replacementStartLine: number,
  replacementNumLines: number,
  snippetContextLines: number,
): { snippet: string; startLine: number } {
  const startLine = Math.max(0, replacementStartLine - snippetContextLines);
  const endLine =
    replacementStartLine + replacementNumLines - 1 + snippetContextLines;

  content = content.replaceAll("\r\n", "\n");

  const snippet = content
    .split("\n")
    .slice(startLine, endLine + 1)
    .join("\n");

  return { snippet, startLine };
}

export function createSnippetStr(
  content: string,
  replacementStartLine: number,
  replacementNumLines: number,
  snippetContextLines: number,
): string {
  const { snippet, startLine } = createSnippet(
    content,
    replacementStartLine,
    replacementNumLines,
    snippetContextLines,
  );
  const snippetStr = snippet
    .split("\n")
    .map((line, i) => `${String(i + startLine + 1).padStart(6)}\t${line}`)
    .join("\n");
  return snippetStr;
}

/**
 * Expands a view range to meet the minimum view size
 */
export function expandViewRange(
  initLine: number,
  finalLine: number,
  minViewSize: number,
  numLinesFile: number,
): { initLine: number; finalLine: number } {
  // Special case for showing all lines from initLine to end of file
  if (finalLine === -1) {
    return { initLine, finalLine };
  }

  const currentRangeSize = finalLine - initLine + 1;

  // If range is already large enough, no need to expand
  if (currentRangeSize >= minViewSize) {
    return { initLine, finalLine };
  }

  // Calculate how many lines to add on each side
  const linesToAdd = minViewSize - currentRangeSize;
  let addToStart = Math.floor(linesToAdd / 2);
  addToStart = Math.min(addToStart, initLine - 1);
  let addToEnd = linesToAdd - addToStart;
  addToEnd = Math.min(addToEnd, numLinesFile - finalLine);

  // If we couldn't add enough lines at the end, try to add more at the start
  addToStart = Math.min(initLine - 1, linesToAdd - addToEnd);

  // Adjust the start line, ensuring it doesn't go below 1
  const newInitLine = Math.max(1, initLine - addToStart);

  // Adjust the end line, ensuring it doesn't exceed the file size
  const newFinalLine = Math.min(numLinesFile, finalLine + addToEnd);

  return { initLine: newInitLine, finalLine: newFinalLine };
}

/**
 * Checks if a string appears multiple times in a file and returns the line numbers
 */
export function findMultipleOccurrences(
  content: string,
  searchStr: string,
): number[] | null {
  const occurrences = content.split(searchStr).length - 1;

  if (occurrences <= 1) {
    return null;
  }

  const contentLines = content.split("\n");
  const lines = contentLines
    .map((line, idx) => (line.includes(searchStr) ? idx + 1 : null))
    .filter((line): line is number => line !== null);

  return lines;
}

/**
 * Detects the indentation type used in a string
 */
export function detectIndentation(content: string): {
  type: "space" | "tab";
  size: number;
} {
  const lines = content.split("\n");

  // Count occurrences of different indentation types
  let spaceIndents = 0;
  let tabIndents = 0;
  let spaceSize = 0;

  // Find non-empty lines with indentation
  for (const line of lines) {
    if (line.trim() === "") continue;

    const leadingSpaces = line.match(/^( +)/);
    const leadingTabs = line.match(/^(\t+)/);

    if (leadingSpaces) {
      spaceIndents++;
      // If we haven't determined space size yet, use this line's indent
      if (spaceSize === 0) {
        spaceSize = leadingSpaces[1].length;
      }
    } else if (leadingTabs) {
      tabIndents++;
    }
  }

  // Determine the dominant indentation type
  if (tabIndents > spaceIndents) {
    return { type: "tab", size: 1 };
  } else {
    // Default to 2 spaces if we couldn't determine
    return { type: "space", size: spaceSize || 2 };
  }
}

/**
 * Removes one level of indentation from each line in a string
 */
export function removeOneIndentLevel(
  text: string,
  indentation: { type: "space" | "tab"; size: number },
): string {
  const lines = text.split("\n");
  const pattern =
    indentation.type === "tab"
      ? /^\t/
      : new RegExp(`^ {1,${indentation.size}}`);

  return lines.map((line) => line.replace(pattern, "")).join("\n");
}

/**
 * Checks if all non-empty lines in the text have indentation left
 * Empty lines are ignored
 */
export function allLinesHaveIndent(
  text: string,
  indentation: {
    type: "space" | "tab";
    size: number;
  },
): boolean {
  const lines = text.split("\n");
  return lines.every((line) => {
    if (line.trim() === "") return true;
    const pattern =
      indentation.type === "tab"
        ? /^\t/
        : new RegExp(`^ {1,${indentation.size}}`);
    return line.match(pattern);
  });
}

export type Match = {
  startLine: number;
  endLine: number;
};

export function findMatches(content: string, str: string): Match[] {
  const contentLines = content.split("\n");
  const strLines = str.split("\n");
  const matches: Match[] = [];

  // If the search string is empty or has more lines than the content, return empty array
  if (str.trim() === "" || strLines.length > contentLines.length) {
    return matches;
  }

  // For single line search
  if (strLines.length === 1) {
    contentLines.forEach((line, index) => {
      if (line.includes(str)) {
        matches.push({
          startLine: index,
          endLine: index,
        });
      }
    });
    return matches;
  }

  // For multi-line search with potential mid-line matches
  const contentText = content;
  const searchText = str;

  let startIndex = 0;
  let foundIndex: number;

  // Find all occurrences of the search string in the content
  while ((foundIndex = contentText.indexOf(searchText, startIndex)) !== -1) {
    // Find the line numbers for start and end positions
    const textBeforeMatch = contentText.substring(0, foundIndex);
    const textUpToEndOfMatch = contentText.substring(
      0,
      foundIndex + searchText.length,
    );

    // Count newlines to determine line numbers
    const startLine = (textBeforeMatch.match(/\n/g) || []).length;
    const endLine = (textUpToEndOfMatch.match(/\n/g) || []).length;

    matches.push({
      startLine,
      endLine,
    });

    // Move past this match to find the next one
    startIndex = foundIndex + 1;
  }

  return matches;
}

/**
 * Finds the closest match to the target line numbers using a tolerance threshold
 * @param matches Array of matches with start and end line numbers
 * @param targetStartLine Target start line number
 * @param targetEndLine Target end line number
 * @param lineNumberErrorTolerance Tolerance for line number errors (0-1)
 *    If A and B are two matches closest to the specified line number
 *    Distance calculated based only on start line numbers
 *    let 1 be half the distance between A and B
 *    if the specified line number is within lineNumberErrorTolerance of A or B, it's still considered a match
 *    0 - means numbers must match exactly
 *    1 - means even if specified line number is exactly between two matches, it's still considered a match
 *    0.2 - means if specified line number is within 10% of the distance between A and B, it's still considered a match
 *    exact integer threshold is rounded down
 * @returns Index of the closest match, or -1 if no match is close enough
 */
export function findClosestMatch(
  matches: Array<{ startLine: number; endLine: number }>,
  targetStartLine: number,
  targetEndLine: number,
  lineNumberErrorTolerance: number,
): number {
  if (matches.length === 0) return -1;
  if (matches.length === 1) return 0;

  // First, look for exact matches
  for (let i = 0; i < matches.length; i++) {
    const match = matches[i];
    if (
      match.startLine === targetStartLine &&
      match.endLine === targetEndLine
    ) {
      return i;
    }
  }

  // If lineNumberErrorTolerance is 0, we need an exact match
  if (lineNumberErrorTolerance === 0) {
    return -1; // No exact match found and tolerance is 0
  }

  // If no exact match, find the closest match using tolerance
  let closestIndex = -1;
  let minDistance = Number.MAX_SAFE_INTEGER;

  // Find the closest match - only using startLine for distance calculation
  for (let i = 0; i < matches.length; i++) {
    const match = matches[i];
    // Calculate distance based only on startLine difference
    const distance = Math.abs(match.startLine - targetStartLine);

    if (distance < minDistance) {
      minDistance = distance;
      closestIndex = i;
    }
  }

  // If tolerance is 1, always return the closest match
  if (lineNumberErrorTolerance === 1) {
    return closestIndex;
  }

  if (closestIndex === -1) {
    return -1;
  }

  // Apply tolerance to determine if the closest match is close enough
  let nextClosestDistance = Number.MAX_SAFE_INTEGER;
  let nextClosestIndex = -1;
  for (let i = 0; i < matches.length; i++) {
    if (i === closestIndex) continue;

    const match = matches[i];
    // Calculate distance based only on startLine difference
    const distance = Math.abs(match.startLine - targetStartLine);

    if (distance < nextClosestDistance) {
      nextClosestDistance = distance;
      nextClosestIndex = i;
    }
  }

  // Calculate the threshold based on the distance between the two closest matches
  // as described in the docstring
  const distanceBetweenMatches = Math.abs(
    matches[nextClosestIndex].startLine - matches[closestIndex].startLine,
  );

  // Calculate the tolerance threshold by applying the tolerance factor
  // to the half of the distance between the two closest matches
  const toleranceThreshold = Math.floor(
    (distanceBetweenMatches / 2) * lineNumberErrorTolerance,
  );

  // If the closest match is within the tolerance threshold, return it
  // otherwise return -1 to indicate no match was close enough
  if (minDistance <= toleranceThreshold) {
    return closestIndex;
  } else {
    // If we're not within threshold, no match
    return -1;
  }
}

/**
 * Validates the str_replace_entries parameter for the str_replace command
 * Line numbers (old_str_start_line_number and old_str_end_line_number) are 1-based
 */
export function validateStrReplaceEntries(
  strReplaceEntries: unknown,
): Array<StrReplaceEntry> {
  if (strReplaceEntries === undefined) {
    throw new Error(
      "Missing required parameter `str_replace_entries` for `str_replace` command.",
    );
  }

  if (!Array.isArray(strReplaceEntries)) {
    throw new Error(
      `Invalid parameter \`str_replace_entries\` for \`str_replace\` command. It must be an array of objects.`,
    );
  }

  if (strReplaceEntries.length === 0) {
    throw new Error(
      `Empty required parameter \`str_replace_entries\` for \`str_replace\` command.`,
    );
  }

  const validatedEntries = [];
  for (const [i, entry] of strReplaceEntries.entries()) {
    // check if entry is an object
    if (typeof entry !== "object" || !entry) {
      throw new Error(
        `Invalid parameter \`str_replace_entries\` for \`str_replace\` command. It must be an array of objects.`,
      );
    }
    const entryTyped = entry as {
      index?: number;
      old_str: string | object;
      new_str: string | object;
      old_str_start_line_number?: number;
      old_str_end_line_number?: number;
    };

    if (entryTyped.old_str === undefined) {
      throw new Error(
        `Missing required parameter \`old_str\` for \`str_replace\` command.`,
      );
    }

    // Allow old_str to be an object to handle cases when model fails to wrap json object into a string
    if (
      typeof entryTyped.old_str !== "string" &&
      typeof entryTyped.old_str !== "object"
    ) {
      throw new Error(
        `Invalid parameter \`old_str\` for \`str_replace\` command. It must be a string.`,
      );
    }

    if (entryTyped.new_str === undefined) {
      throw new Error(
        `Missing required parameter \`new_str\` for \`str_replace\` command.`,
      );
    }

    if (
      typeof entryTyped.new_str !== "string" &&
      typeof entryTyped.new_str !== "object"
    ) {
      throw new Error(
        `Invalid parameter \`new_str\` for \`str_replace\` command. It must be a string.`,
      );
    }

    // Looks like Claude sets line numbers as 0 when it doesn't know them
    // So we should treat them as undefined
    if (entryTyped.old_str_start_line_number === 0) {
      entryTyped.old_str_start_line_number = undefined;
    }
    if (entryTyped.old_str_end_line_number === 0) {
      entryTyped.old_str_end_line_number = undefined;
    }

    // check that line_numbers are positive integers
    if (
      entryTyped.old_str_start_line_number !== undefined &&
      (!Number.isInteger(entryTyped.old_str_start_line_number) ||
        entryTyped.old_str_start_line_number < 1)
    ) {
      throw new Error(
        `Invalid parameter \`old_str_start_line_number\` for \`str_replace\` command. It must be a positive integer.`,
      );
    }

    if (
      entryTyped.old_str_end_line_number !== undefined &&
      (!Number.isInteger(entryTyped.old_str_end_line_number) ||
        entryTyped.old_str_end_line_number < 1)
    ) {
      throw new Error(
        `Invalid parameter \`old_str_end_line_number\` for \`str_replace\` command. It must be a positive integer.`,
      );
    }
    validatedEntries.push({
      index: entryTyped.index ?? i,
      old_str: entryTyped.old_str,
      new_str: entryTyped.new_str,
      old_str_start_line_number: entryTyped.old_str_start_line_number,
      old_str_end_line_number: entryTyped.old_str_end_line_number,
    });
  }
  return validatedEntries;
}

export function prepareStrReplaceEntries(
  strReplaceEntries: Array<StrReplaceEntry>,
): Array<StrReplaceEntry> {
  strReplaceEntries = toZeroBased(strReplaceEntries);

  // Sort the entries based on start line number in reverse order
  // This ensures that edits don't change line numbers for subsequent edits
  // Entries without a start line number are sorted to the end
  strReplaceEntries.sort((a, b) => {
    const a_num = a.old_str_start_line_number ?? -1;
    const b_num = b.old_str_start_line_number ?? -1;
    return b_num - a_num; // reverse order
  });

  return strReplaceEntries;
}

export function findOverlappingEntry(
  currentEntry: StrReplaceEntry,
  strReplaceEntries: Array<StrReplaceEntry>,
): StrReplaceEntry | undefined {
  const startLine = currentEntry.old_str_start_line_number;
  const endLine = currentEntry.old_str_end_line_number;
  if (startLine === undefined || endLine === undefined) {
    return undefined;
  }
  for (const entry of strReplaceEntries) {
    if (
      currentEntry.index == entry.index ||
      entry.old_str_start_line_number === undefined ||
      entry.old_str_end_line_number === undefined
    ) {
      continue;
    }
    const range = {
      startLine: entry.old_str_start_line_number,
      endLine: entry.old_str_end_line_number,
    };
    if (
      (startLine <= range.startLine && range.startLine <= endLine) ||
      (startLine <= range.endLine && range.endLine <= endLine) ||
      (range.startLine <= startLine && startLine <= range.endLine)
    ) {
      return entry;
    }
  }
  return undefined;
}

export function genOverlappingEntryErrorMessage(
  currentEntry: StrReplaceEntry,
  overlappingEntry: StrReplaceEntry,
) {
  // Convert to 1-based line numbers
  const curStart = currentEntry.old_str_start_line_number! + 1;
  const curEnd = currentEntry.old_str_end_line_number! + 1;
  const overlapStart = overlappingEntry.old_str_start_line_number! + 1;
  const overlapEnd = overlappingEntry.old_str_end_line_number! + 1;
  return `old_str line numbers range overlaps with another entry.
This entry range: [${curStart}-${curEnd}]
Overlapping entry index: ${overlappingEntry.index}
Overlapping entry range: [${overlapStart}-${overlapEnd}]`;
}

export function validateInsertLineEntries(
  insertLineEntries: unknown,
): Array<InsertLineEntry> {
  if (insertLineEntries === undefined) {
    throw new Error(
      "Missing required parameter `insert_line_entries` for `insert` command.",
    );
  }

  if (!Array.isArray(insertLineEntries)) {
    throw new Error(
      `Invalid parameter \`insert_line_entries\` for \`insert\` command. It must be an array of objects.`,
    );
  }

  if (insertLineEntries.length === 0) {
    throw new Error(
      `Empty required parameter \`insert_line_entries\` for \`insert\` command.`,
    );
  }

  const validatedEntries = [];
  for (const [i, entry] of insertLineEntries.entries()) {
    // check if entry is an object
    if (typeof entry !== "object" || !entry) {
      throw new Error(
        `Invalid parameter \`insert_line_entries\` for \`insert\` command. It must be an array of objects.`,
      );
    }

    const entryTyped = entry as {
      index?: number;
      insert_line: unknown;
      new_str: unknown;
    };

    if (entryTyped.insert_line === undefined) {
      throw new Error(
        `Missing required parameter \`insert_line\` for \`insert\` command.`,
      );
    }

    if (entryTyped.new_str === undefined) {
      throw new Error(
        `Missing required parameter \`new_str\` for \`insert\` command.`,
      );
    }

    if (typeof entryTyped.new_str !== "string") {
      throw new Error(
        `Invalid parameter \`new_str\` for \`insert\` command. It must be a string.`,
      );
    }

    // check that insert_line is a non-negative integer
    if (
      !Number.isInteger(entryTyped.insert_line) ||
      (entryTyped.insert_line as number) < 0
    ) {
      throw new Error(
        `Invalid parameter \`insert_line\` for \`insert\` command. It must be a non-negative integer.`,
      );
    }
    validatedEntries.push({
      index: entryTyped.index ?? i,
      insert_line: entryTyped.insert_line as number,
      new_str: entryTyped.new_str,
    });
  }
  return validatedEntries;
}

export function toZeroBased(
  strReplaceEntries: Array<StrReplaceEntry>,
): Array<StrReplaceEntry> {
  return strReplaceEntries.map((entry) => ({
    ...entry,
    old_str_start_line_number:
      entry.old_str_start_line_number !== undefined
        ? entry.old_str_start_line_number - 1
        : undefined,
    old_str_end_line_number:
      entry.old_str_end_line_number !== undefined
        ? entry.old_str_end_line_number - 1
        : undefined,
  }));
}

/**
 * Updates the line numbers of an EditResult after file content has changed
 *
 * This function is used to update the line numbers of an edit result after the file content
 * has been modified (e.g., by formatting). It works by creating a snippet from the original
 * edited content, locating that snippet in the updated content, and then adjusting the line
 * numbers accordingly.
 *
 * @param result - The EditResult to update with new line numbers
 * @param updatedContent - The new content of the file after external modifications
 */
export function updateEditResultLineNumbers(
  result: EditResult,
  updatedContent: string,
) {
  updatedContent = updatedContent.replaceAll("\r\n", "\n");
  const linesA = result.newContent!.split("\n");
  const linesB = updatedContent.split("\n");
  const lineMapping = fuzzyMatchLines(linesA, linesB);
  result.newStrStartLineNumber =
    lineMapping[result.newStrStartLineNumber!][0] ??
    result.newStrStartLineNumber;
  result.newStrEndLineNumber =
    lineMapping[result.newStrEndLineNumber!][
      lineMapping[result.newStrEndLineNumber!].length - 1
    ] ?? result.newStrEndLineNumber;
}

export type StrReplaceEntryRaw = {
  index: number;
  old_str: any;
  new_str: any;
  old_str_start_line_number?: unknown;
  old_str_end_line_number?: unknown;
};

/**
 * Extracts str_replace entries from tool input
 * Handles both indexed and non-indexed entries
 * Also handles non-consecutive indices
 */
export function extractStrReplaceEntries(
  toolInput: Record<string, unknown>,
): Array<StrReplaceEntryRaw> {
  const entries: Array<StrReplaceEntryRaw> = [];

  // Handle special case for the first entry that might have index for line numbers but not for the strings
  if (
    "old_str" in toolInput &&
    "new_str" in toolInput &&
    !("old_str_1" in toolInput) &&
    !("new_str_1" in toolInput) &&
    !("old_str_start_line_number" in toolInput) &&
    !("old_str_end_line_number" in toolInput) &&
    "old_str_start_line_number_1" in toolInput &&
    "old_str_end_line_number_1" in toolInput
  ) {
    toolInput["old_str_1"] = toolInput["old_str"];
    toolInput["new_str_1"] = toolInput["new_str"];
    delete toolInput["old_str"];
    delete toolInput["new_str"];
  }

  // Handle special case for the first entry that might have index for strings but not for the line numbers
  if (
    !("old_str" in toolInput) &&
    !("new_str" in toolInput) &&
    "old_str_1" in toolInput &&
    "new_str_1" in toolInput &&
    "old_str_start_line_number" in toolInput &&
    "old_str_end_line_number" in toolInput &&
    !("old_str_start_line_number_1" in toolInput) &&
    !("old_str_end_line_number_1" in toolInput)
  ) {
    toolInput["old_str_start_line_number_1"] = toolInput[
      "old_str_start_line_number"
    ] as number;
    toolInput["old_str_end_line_number_1"] = toolInput[
      "old_str_end_line_number"
    ] as number;
    delete toolInput["old_str_start_line_number"];
    delete toolInput["old_str_end_line_number"];
  }

  // Check for the first entry (no index)
  if ("old_str" in toolInput && "new_str" in toolInput) {
    const entry: StrReplaceEntryRaw = {
      index: 0,
      old_str: toolInput["old_str"],
      new_str: toolInput["new_str"],
    };

    if ("old_str_start_line_number" in toolInput) {
      entry.old_str_start_line_number = toolInput["old_str_start_line_number"];
    }

    if ("old_str_end_line_number" in toolInput) {
      entry.old_str_end_line_number = toolInput["old_str_end_line_number"];
    }

    entries.push(entry);
  }

  // Find all keys that match the pattern old_str_X where X is a number
  const indexedEntryKeys = Object.keys(toolInput).filter(
    (key) => key.startsWith("old_str_") && /^old_str_\d+$/.test(key),
  );

  // Sort the keys based on the index number
  indexedEntryKeys.sort((a, b) => {
    const indexA = parseInt(a.replace("old_str_", ""));
    const indexB = parseInt(b.replace("old_str_", ""));
    return indexA - indexB;
  });

  // Process each indexed entry
  for (const key of indexedEntryKeys) {
    // Extract the index number from the key
    const index = key.replace("old_str_", "");

    // Check if the corresponding new_str_X exists
    if (`new_str_${index}` in toolInput) {
      const entry: StrReplaceEntryRaw = {
        index: parseInt(index),
        old_str: toolInput[`old_str_${index}`],
        new_str: toolInput[`new_str_${index}`],
      };

      if (`old_str_start_line_number_${index}` in toolInput) {
        entry.old_str_start_line_number =
          toolInput[`old_str_start_line_number_${index}`];
      }

      if (`old_str_end_line_number_${index}` in toolInput) {
        entry.old_str_end_line_number =
          toolInput[`old_str_end_line_number_${index}`];
      }

      entries.push(entry);
    }
  }

  return entries;
}

export type InsertLineEntryRaw = {
  index: number;
  insert_line: unknown;
  new_str: unknown;
};

/**
 * Extracts insert_line entries from tool input
 * Handles both indexed and non-indexed entries
 * Also handles non-consecutive indices
 */
export function extractInsertLineEntries(
  toolInput: Record<string, any>,
): Array<InsertLineEntryRaw> {
  const entries: Array<InsertLineEntryRaw> = [];

  // Check for the first entry (no index)
  if ("insert_line" in toolInput && "new_str" in toolInput) {
    entries.push({
      index: 0,
      insert_line: toolInput["insert_line"],
      new_str: toolInput["new_str"],
    });
  }

  // Find all keys that match the pattern insert_line_X where X is a number
  const indexedEntryKeys = Object.keys(toolInput).filter(
    (key) => key.startsWith("insert_line_") && /^insert_line_\d+$/.test(key),
  );

  // Sort the keys based on the index number
  indexedEntryKeys.sort((a, b) => {
    const indexA = parseInt(a.replace("insert_line_", ""));
    const indexB = parseInt(b.replace("insert_line_", ""));
    return indexA - indexB;
  });

  // Process each indexed entry
  for (const key of indexedEntryKeys) {
    // Extract the index number from the key
    const index = key.replace("insert_line_", "");

    // Check if the corresponding new_str_X exists
    if (`new_str_${index}` in toolInput) {
      entries.push({
        index: parseInt(index),
        insert_line: toolInput[`insert_line_${index}`],
        new_str: toolInput[`new_str_${index}`],
      });
    }
  }

  return entries;
}

/**
 * Removes all indentation from text by trimming each line
 * Used for matching JSON objects regardless of indentation
 */
export function removeAllIndents(text: string): string {
  const lineEnding = detectLineEnding(text);
  return text
    .split(lineEnding)
    .map((line) => line.trim())
    .join(lineEnding);
}

/**
 * Serializes an object with proper indentation
 * @param obj The object to serialize
 * @param baseIndent The base indentation to add to each line
 * @param indentChar The character to use for indentation (tab or spaces)
 * @param shouldIndentFirstLine Whether to indent the first line
 * @returns The serialized string with proper indentation
 */
export function serializeWithIndent(
  obj: any,
  baseIndent: string,
  indentChar: string,
  shouldIndentFirstLine: boolean,
): string {
  const jsonStr = JSON.stringify(obj, null, indentChar);
  const lines = jsonStr.split("\n");

  // Add base indent to each line
  const firstLine = shouldIndentFirstLine ? baseIndent + lines[0] : lines[0];
  const restLines = lines.slice(1).map((line) => baseIndent + line);
  return [firstLine, ...restLines].join("\n");
}

/**
 * Creates an error EditResult for JSON object handling
 * @param index The index of the entry
 * @param oldStr The old string (serialized JSON)
 * @param oldStrStartLineNumber Optional start line number
 * @param oldStrEndLineNumber Optional end line number
 * @returns Error EditResult
 */
export function createJsonObjectErrorResult(
  index: number,
  oldStr: string,
  oldStrStartLineNumber?: number,
  oldStrEndLineNumber?: number,
): EditResult {
  return {
    isError: true,
    index: index,
    oldStr: oldStr,
    oldStrStartLineNumber: oldStrStartLineNumber,
    oldStrEndLineNumber: oldStrEndLineNumber,
    numLinesDiff: 0,
    // We actually want the model to use strings instead of objects hence this error message
    genMessageFunc: () =>
      "Invalid parameter `old_str` for `str_replace` command. It must be a string.",
  };
}

/**
 * Deduces the command based on the tool input parameters
 * If command is explicitly provided, it is returned as is
 * @param toolInput The tool input parameters
 * @returns The deduced command or undefined if no command could be deduced
 */
export function deduceCommand(
  toolInput: Record<string, unknown>,
  logger: AugmentLogger,
): string | undefined {
  // If command is explicitly provided, use it
  if (toolInput.command) {
    return toolInput.command as string;
  }

  // Check for nested schema parameters
  if (toolInput.str_replace_entries) {
    logger.debug(
      `Command not provided, deduced 'str_replace' from presence of str_replace_entries`,
    );
    return "str_replace";
  } else if (toolInput.insert_line_entries) {
    logger.debug(
      `Command not provided, deduced 'insert' from presence of insert_line_entries`,
    );
    return "insert";
  }
  // Check for flat schema parameters
  else if (
    toolInput.old_str ||
    Object.keys(toolInput).some((key) => key.match(/^old_str_\d+$/))
  ) {
    logger.debug(
      `Command not provided, deduced 'str_replace' from presence of old_str parameters`,
    );
    return "str_replace";
  } else if (
    toolInput.insert_line ||
    Object.keys(toolInput).some((key) => key.match(/^insert_line_\d+$/))
  ) {
    logger.debug(
      `Command not provided, deduced 'insert' from presence of insert_line parameters`,
    );
    return "insert";
  } else if (toolInput.view_range !== undefined) {
    logger.debug(
      `Command not provided, deduced 'view' from presence of view_range`,
    );
    return "view";
  }

  // No command could be deduced
  return undefined;
}
