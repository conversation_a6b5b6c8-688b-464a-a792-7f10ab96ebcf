/**
 * Very simple shell command parser
 *
 * The parser rejects any command containing "dangerous" characters and then
 * splits the command by spaces. It does not handle quoted arguments or
 * other shell-specific parsing rules.
 *
 * This type of parsing is sufficient to implement a simple allowlist-based
 * safety mechanism for shell commands.
 */
export type ShellType = "bash" | "powershell" | "zsh" | "fish";

const bashDangerousChars = [">", "<", "|", "&", "$", "`", ";"];
const zshDangerousChars = [...bashDangerousChars, "="]; // also protect against =()
const fishDangerousChars = [...bashDangerousChars, "("]; // fish uses (command) for command substitution

const powershellDangerousChars = [
  // Command chaining and separation
  "|",
  "&",
  ";",
  "$",
  // Variable expansion and escape character
  "`",
  // Used for subexpressions, command execution, and grouping
  "(",
  ")",
  // Script blocks
  "{",
  "}",
  // Redirection
  "<",
  ">",
  // Type casting, which could perhaps be dangerous
  "[",
  "]",
  // String delimiters (e.g., `InvokeExpression "..."`)
  '"',
  "'",
  // Dot sourcing
  ".",
];

export function getDefaultShell(platform: string): ShellType {
  if (platform === "win32") {
    return "powershell";
  }
  if (platform === "darwin") {
    return "zsh";
  }
  return "bash";
}

export function isSupportedShell(shell: string): shell is ShellType {
  return (
    shell === "bash" ||
    shell === "powershell" ||
    shell === "zsh" ||
    shell === "fish"
  );
}

/**
 * Parses a shell command string into an array of command and arguments.
 *
 * This is a naive implementation that simply checks for disallowed characters
 * and splits the command by spaces. It does not handle quoted arguments or
 * other shell-specific parsing rules.
 *
 * @param input - The shell command string to parse
 * @param shell - The type of shell to parse for (bash or powershell)
 * @returns An array of command and arguments, or undefined if the command contains disallowed characters
 */
export function parseCommandNaive(
  input: string,
  shell: ShellType,
): string[] | undefined {
  if (shell === "bash") {
    for (const ch of bashDangerousChars) {
      if (input.includes(ch)) {
        return undefined;
      }
    }
    return input.split(" ");
  } else if (shell === "powershell") {
    for (const ch of powershellDangerousChars) {
      if (input.includes(ch)) {
        return undefined;
      }
    }
    return input.split(" ");
  } else if (shell === "zsh") {
    for (const ch of zshDangerousChars) {
      if (input.includes(ch)) {
        return undefined;
      }
    }
    return input.split(" ");
  } else if (shell === "fish") {
    for (const ch of fishDangerousChars) {
      if (input.includes(ch)) {
        return undefined;
      }
    }
    return input.split(" ");
  }
  return undefined;
}

/**
 * Quotes and escapes shell command arguments
 * @param xs Array of arguments to quote
 * @returns Quoted and escaped shell command string
 */
export function quote(xs: Array<string | { op: string }>): string {
  return xs
    .map((s) => {
      if (s === "") {
        return "''";
      }
      if (s && typeof s === "object") {
        return s.op.replace(/(.)/g, "\\$1");
      }
      if (/["\s]/.test(s) && !/[']/g.test(s)) {
        return "'" + s.replace(/(['\\])/g, "\\$1") + "'";
      }
      if (/["'\s]/.test(s)) {
        return '"' + s.replace(/(["\\$`!])/g, "\\$1") + '"';
      }
      return String(s).replace(
        /([A-Za-z]:)?([#!"$&'()*,:;<=>?@[\\\]^`{|}])/g,
        "$1\\$2",
      );
    })
    .join(" ");
}
