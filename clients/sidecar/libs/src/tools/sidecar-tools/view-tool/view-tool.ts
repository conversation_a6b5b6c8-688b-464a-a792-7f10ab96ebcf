import * as path from 'path';
import { Exchange } from "../../../chat/chat-types";
import { getLogger, type AugmentLogger } from "../../../logging";
import { ToolBase, ToolSafety, ToolUseResponse } from "../../tool-types";
import { SidecarToolType } from "../sidecar-tool-types";
import { successToolResponse, errorToolResponse } from "../tool-use-response";
import { getClientFeatureFlags } from "@augment-internal/sidecar-libs/src/client-interfaces/feature-flags";
import { ViewToolDefinition } from "./tool-definition";
import {
  attemptPathAutoCorrection,
  formatFileContent,
  genSimilarPathsResponse,
  normalizeLineEndings,
} from "../file-utils";
import {
  FileType,
  getClientWorkspaces,
} from "@augment-internal/sidecar-libs/src/client-interfaces/client-workspaces";

type ViewToolInputs = {
  path: string;
  type?: "file" | "directory";
  viewRange?: [number, number];
  searchQueryRegex?: string;
  caseSensitive?: boolean;
  contextLinesBefore?: number;
  contextLinesAfter?: number;
};

export class ViewTool extends ToolBase<SidecarToolType> {
  private readonly _logger: AugmentLogger;
  private readonly _toolDefinition: ViewToolDefinition;

  constructor() {
    super(SidecarToolType.view, ToolSafety.Safe);
    this._toolDefinition = new ViewToolDefinition();
    this.description = this._toolDefinition.description;
    this.inputSchemaJson = this._toolDefinition.inputSchemaJson;

    this._logger = getLogger("ViewTool");
  }

  public readonly description: string;
  public readonly inputSchemaJson: string;

  public checkToolCallSafe(_toolInput: Record<string, unknown>): boolean {
    return true;
  }

  private validateInputs(
    toolInput: Record<string, unknown>,
  ): ViewToolInputs | Error {
    // Validate required string parameter: path
    if (toolInput.path === undefined) {
      return new Error("Missing required parameter `path`");
    }
    if (typeof toolInput.path !== "string") {
      return Error("Parameter 'path' must be a string");
    }
    if (toolInput.path.trim() === "") {
      throw new Error("Invalid parameter `path`. It must not be empty.");
    }

    // Validate optional type parameter
    let type: "file" | "directory" | undefined;
    if (toolInput.type !== undefined) {
      if (typeof toolInput.type !== "string") {
        return Error("Parameter 'type' must be a string");
      }
      if (toolInput.type !== "file" && toolInput.type !== "directory") {
        return Error("Parameter 'type' must be either 'file' or 'directory'");
      }
      type = toolInput.type;
    }

    // Validate optional view_range parameter
    let viewRange: [number, number] | undefined;
    if (toolInput.view_range !== undefined) {
      if (
        !Array.isArray(toolInput.view_range) ||
        toolInput.view_range.length !== 2
      ) {
        return Error("Parameter 'view_range' must be an array of two numbers");
      }
      if (!toolInput.view_range.every((item) => typeof item === "number")) {
        return Error("Parameter 'view_range' must contain only numbers");
      }
      viewRange = toolInput.view_range as [number, number];
    }

    // Validate optional search_query_regex parameter
    let searchQueryRegex: string | undefined;
    if (toolInput.search_query_regex !== undefined) {
      if (typeof toolInput.search_query_regex !== "string") {
        return Error("Parameter 'search_query_regex' must be a string");
      }
      searchQueryRegex = toolInput.search_query_regex;
    }

    // Validate optional case_sensitive parameter
    let caseSensitive: boolean | undefined;
    if (toolInput.case_sensitive !== undefined) {
      if (typeof toolInput.case_sensitive !== "boolean") {
        return Error("Parameter 'case_sensitive' must be a boolean");
      }
      caseSensitive = toolInput.case_sensitive;
    }

    // Validate optional context_lines_before parameter
    let contextLinesBefore: number | undefined;
    if (toolInput.context_lines_before !== undefined) {
      if (
        typeof toolInput.context_lines_before !== "number" ||
        !Number.isInteger(toolInput.context_lines_before)
      ) {
        return Error("Parameter 'context_lines_before' must be an integer");
      }
      if (toolInput.context_lines_before < 0) {
        return Error(
          "Parameter 'context_lines_before' must be a non-negative integer",
        );
      }
      contextLinesBefore = toolInput.context_lines_before;
    }

    // Validate optional context_lines_after parameter
    let contextLinesAfter: number | undefined;
    if (toolInput.context_lines_after !== undefined) {
      if (
        typeof toolInput.context_lines_after !== "number" ||
        !Number.isInteger(toolInput.context_lines_after)
      ) {
        return Error("Parameter 'context_lines_after' must be an integer");
      }
      if (toolInput.context_lines_after < 0) {
        return Error(
          "Parameter 'context_lines_after' must be a non-negative integer",
        );
      }
      contextLinesAfter = toolInput.context_lines_after;
    }

    return {
      path: toolInput.path,
      type,
      viewRange,
      searchQueryRegex,
      caseSensitive,
      contextLinesBefore,
      contextLinesAfter,
    };
  }

  public async call(
    toolInput: Record<string, unknown>,
    _chatHistory: Exchange[],
    _abortSignal: AbortSignal,
  ): Promise<ToolUseResponse> {
    try {
      this._logger.debug(
        `Tool called with inputs: ${JSON.stringify(toolInput)}`,
      );

      const validationResult = this.validateInputs(toolInput);
      if (validationResult instanceof Error) {
        return errorToolResponse(
          `Input validation failed: ${validationResult.message}`,
        );
      }
      const inputs = validationResult;

      this._logger.info(
        `Tool called with path: ${inputs.path} and view_range: ${JSON.stringify(inputs.viewRange)}`,
      );
      const pathInfo = await getClientWorkspaces().getPathInfo(inputs.path);

      if (!pathInfo.exists || pathInfo.type === undefined) {
        return this.handlePathNotFound(inputs);
      }

      return this.handleExistingPath(inputs, pathInfo);
    } catch (error: unknown) {
      this._logger.error(
        `Error in tool call: ${error instanceof Error ? error.message : String(error)}`,
      );
      if (error instanceof Error) {
        return errorToolResponse(error.message);
      } else {
        return errorToolResponse(`Unknown error: ${String(error)}`);
      }
    }
  }

  private async handlePathNotFound(
    inputs: ViewToolInputs,
  ): Promise<ToolUseResponse> {
    this._logger.info(`Path does not exist: ${inputs.path}`);

    if (inputs.type === "directory") {
      return errorToolResponse(`Directory not found: ${inputs.path}`);
    }

    // Attempt path auto-correction
    const correctionResult = await attemptPathAutoCorrection(inputs.path);
    this._logger.debug(
      `Attempted path auto-correction, result: ${JSON.stringify(correctionResult)}`,
    );

    // If no similar paths found, return error
    if (correctionResult.similarPaths.length === 0) {
      return errorToolResponse(`File not found: ${inputs.path}`);
    }

    if (correctionResult.corrected && correctionResult.correctedPath) {
      this._logger.info(`Corrected path to: ${correctionResult.correctedPath}`);

      // Get info for the corrected path
      const correctedPathInfo = await getClientWorkspaces().getPathInfo(
        correctionResult.correctedPath,
      );

      if (correctedPathInfo.exists && correctedPathInfo.type !== undefined) {
        inputs.path = correctionResult.correctedPath;
        const response = await this.handleExistingPath(
          inputs,
          correctedPathInfo,
        );

        // Add the correction note to the response
        response.text = `${correctionResult.correctionNote}\n\n${response.text}`;
        return response;
      }
    }

    // If we get here, either there are multiple paths with the suffix, none,
    // or the path is absolute, so we show the similar paths as suggestions
    return errorToolResponse(
      genSimilarPathsResponse(inputs.path, correctionResult.similarPaths),
    );
  }

  private async handleExistingPath(
    inputs: ViewToolInputs,
    pathInfo: { type?: FileType },
  ): Promise<ToolUseResponse> {
    if (pathInfo.type && pathInfo.type & FileType.File) {
      return await this.handleFileView(inputs);
    } else if (pathInfo.type && pathInfo.type & FileType.Directory) {
      return await this.handleDirectoryView(inputs.path);
    } else {
      this._logger.error(`Path is not a file or directory: ${inputs.path}`);
      return errorToolResponse(
        `Path is not a file or directory: ${inputs.path}`,
      );
    }
  }

  public async handleFileView(
    inputs: ViewToolInputs,
  ): Promise<ToolUseResponse> {
    // Read the file
    const fileDetails = await getClientWorkspaces().readFile(inputs.path);
    if (
      fileDetails === undefined ||
      fileDetails.contents === undefined ||
      fileDetails.filepath === undefined
    ) {
      this._logger.error(
        `Failed to read file even though we got pathInfo: ${inputs.path}`,
      );
      return errorToolResponse(`Cannot read file: ${inputs.path}`);
    }

    const fileContents = normalizeLineEndings(fileDetails.contents);
    const fileLines = fileContents.split("\n");
    const numLinesFile = fileLines.length;
    this._logger.debug(`File ${inputs.path} has ${numLinesFile} lines`);

    // Check if regex search is requested
    const searchQueryRegex = inputs.searchQueryRegex;
    if (searchQueryRegex) {
      return this.handleRegexSearch(inputs, fileLines);
    }

    // Default to showing the entire file
    let initLine = 1;
    let finalLine = numLinesFile;
    let rangeMessage = "";

    // Process view range if provided
    if (inputs.viewRange) {
      try {
        // Validate the view range
        const minViewSize =
          getClientFeatureFlags().flags?.agentEditToolMinViewSize ?? 0;
        const validRange = validateViewRange(
          inputs.viewRange,
          numLinesFile,
          minViewSize,
        );
        initLine = validRange.initLine;
        finalLine = validRange.finalLine;

        // Add any validation messages to the output
        if (validRange.message) {
          rangeMessage = `Note:\n${validRange.message}\n\n`;
        }
      } catch (error) {
        if (error instanceof Error) {
          throw error;
        }
        throw new Error(`Invalid view_range: ${String(error)}`);
      }
    }

    // Extract the requested lines
    const slicedContents =
      finalLine === -1
        ? fileLines.slice(initLine - 1).join("\n")
        : fileLines.slice(initLine - 1, finalLine).join("\n");

    const output = formatFileContent(
      slicedContents,
      inputs.path,
      initLine,
      numLinesFile,
    );

    return successToolResponse(rangeMessage + output);
  }

  public async handleDirectoryView(pathStr: string): Promise<ToolUseResponse> {
    // Get limits and depth from feature flags, with fallback to default values
    let limits: number[] = [-1, -1]; // Default: show everything upto depth 2 
    let depth: number = 2;
    try {
      const featureFlags = getClientFeatureFlags();
      const agentViewToolParams = featureFlags.flags.agentViewToolParams;
      if (agentViewToolParams) {
        const params = JSON.parse(agentViewToolParams);
        if (params.view_dir_max_entries_per_depth && Array.isArray(params.view_dir_max_entries_per_depth)) {
          limits = params.view_dir_max_entries_per_depth;
        }
        if (params.view_dir_max_depth && Number.isInteger(params.view_dir_max_depth)) {
          depth = params.view_dir_max_depth;
        }
      }
    } catch (error) {
      this._logger.warn(`Failed to parse agentViewToolParams. Using default limits and depth: ${error}`);
    }

    this._logger.info(
      `Listing directory: ${pathStr} (depth: ${depth}, showHidden: false, limits: ${JSON.stringify(limits)})`,
    );
    const result = await getClientWorkspaces().listDirectory(pathStr, depth, false);

    if (result.errorMessage) {
      this._logger.error(
        `Failed to list directory: ${pathStr} - ${result.errorMessage}`,
      );
      return errorToolResponse(result.errorMessage);
    }

    this._logger.debug(
      `Directory ${pathStr} has ${result.entries.length} entries (before truncation)`,
    );

    // Apply depth-based truncation to the entries to reduce output size and format them
    const formattedEntries: string = this.truncateAndFormatDirectoryViewEntries(result.entries, limits, pathStr);

    // Add a header line.
    let output = `Here's the files and directories up to ${depth} levels deep in ${pathStr}, excluding hidden items:\n`;
    if (formattedEntries.trim() === '') {
      output += "(empty directory)\n";
    } else {
      output += formattedEntries + '\n';
    }

    return successToolResponse(output);
  }

  /**
   * Limits the number of entries at each depth based on the limits array and formats them for display.
   * The limit is applied separately for each subdirectory at each depth.
   * The primary purpose is to truncate sized directory trees for the LLM prompt.
   * @param entries Array of relative paths from listDirectory
   * @param limits Array where index represents depth and value represents max entries (-1 for no limit)
   * @param basePath The base path to prepend to each entry for display
   * @returns Formatted string with paths and truncation messages
   */
  private truncateAndFormatDirectoryViewEntries(entries: string[], limits: number[], basePath: string): string {
    // Group entries by their parent directory path (using Map to preserve insertion order)
    const entriesByParent = new Map<string, string[]>();
    const emptyParent = '';
    for (const entry of entries) {
      // Calculate depth by counting path separators
      // Root level files/dirs have depth 0, subdirectory contents have depth 1, etc.
      const depth = entry.split(path.sep).length - 1;

      // Root level entries have no parent, use '' as the key
      const parentDir = depth === 0 ? emptyParent : path.dirname(entry);

      if (!entriesByParent.has(parentDir)) {
        entriesByParent.set(parentDir, []);
      }
      entriesByParent.get(parentDir)!.push(entry);
    }

    // Apply limits and format results
    const formattedLines: string[] = [];
    for (const [parent, children] of entriesByParent) {
      // Compute depth from any entry in this parent directory, and the limit.
      const depth = parent === emptyParent ? 0 : parent.split(path.sep).length;
      const limit = depth < limits.length ? limits[depth] : -1;
      this._logger.debug(`Processing parent: ${parent}, depth: ${depth}, limit: ${limit}`);

      if (limit === -1) {
        // No limit for this depth - add all children as formatted paths
        for (const child of children) {
          // Handle "." specially - it represents the current directory itself
          if (child === ".") {
            formattedLines.push(basePath);
          } else {
            formattedLines.push(`${basePath}/${child}`);
          }
        }
      } else {
        // Apply the limit separately for each parent directory
        const limitedChildren = children.slice(0, limit);
        for (const child of limitedChildren) {
          // Handle "." specially - it represents the current directory itself
          if (child === ".") {
            formattedLines.push(basePath);
          } else {
            formattedLines.push(`${basePath}/${child}`);
          }
        }
        // Add truncation message if entries were left out
        if (children.length > limit) {
          const truncatedCount = children.length - limit;
          const parentPath = parent === emptyParent ? basePath : `${basePath}/${parent}`;
          formattedLines.push(`${parentPath}/... (${truncatedCount} more entries in this subdirectory truncated)`);
        }
      }
    }
    return formattedLines.join('\n');
  }

  private handleRegexSearch(
    inputs: ViewToolInputs,
    fileLines: string[],
  ): ToolUseResponse {
    this._logger.debug(
      `Performing regex search in ${inputs.path} with pattern: ${inputs.searchQueryRegex}`,
    );

    // Get context line parameters
    const contextLinesBefore = inputs.contextLinesBefore ?? 5;
    const contextLinesAfter = inputs.contextLinesAfter ?? 5;

    // Determine the range to search within
    // 0-based index
    let searchStartLine = 0;
    let searchEndLine = fileLines.length - 1;

    if (inputs.viewRange) {
      try {
        const validRange = validateViewRange(
          inputs.viewRange,
          fileLines.length,
          0,
        );
        // convert to 0-based index
        searchStartLine = validRange.initLine - 1;
        searchEndLine = validRange.finalLine - 1;
      } catch (error) {
        // If view range is invalid, search the entire file
        this._logger.warn(
          `Invalid view_range for regex search, searching entire file: ${String(error)}`,
        );
      }
    }

    // Get case sensitivity parameter
    const caseSensitive = inputs.caseSensitive ?? false;

    // Create the regex pattern
    let regex: RegExp;
    try {
      const flags = caseSensitive ? "" : "i";
      regex = new RegExp(inputs.searchQueryRegex!, flags);
    } catch (error) {
      const errorMessage = `Invalid regex pattern: ${inputs.searchQueryRegex} - ${error instanceof Error ? error.message : String(error)}`;
      this._logger.error(errorMessage);
      return errorToolResponse(errorMessage);
    }

    // Find all matching lines within the search range
    const matches: Array<{ lineNum: number; line: string }> = [];
    for (
      let i = searchStartLine;
      // searchEndLine is inclusive
      i <= searchEndLine && i < fileLines.length;
      i++
    ) {
      const line = fileLines[i];
      // Reset regex lastIndex for each line
      regex.lastIndex = 0;
      if (regex.test(line)) {
        matches.push({ lineNum: i + 1, line });
      }
    }

    if (matches.length === 0) {
      const rangeNote = inputs.viewRange
        ? ` within lines ${searchStartLine + 1}-${searchEndLine + 1}`
        : "";
      return successToolResponse(
        `No matches found for regex pattern: ${inputs.searchQueryRegex}${rangeNote} in ${inputs.path}`,
      );
    }

    // Build the output with context
    const outputLines: string[] = [];
    const processedLines = new Set<number>();

    outputLines.push(
      `Regex search results for pattern: ${inputs.searchQueryRegex} in ${inputs.path}`,
    );
    if (inputs.viewRange) {
      outputLines.push(
        `Search limited to lines ${searchStartLine + 1}-${searchEndLine + 1}`,
      );
    }
    outputLines.push(`Found ${matches.length} matching lines:\n`);

    for (let i = 0; i < matches.length; i++) {
      const match = matches[i];
      const startContext = Math.max(1, match.lineNum - contextLinesBefore);
      const endContext = Math.min(
        fileLines.length,
        match.lineNum + contextLinesAfter,
      );

      // Check if we need to add ellipsis
      if (
        i > 0 &&
        startContext > matches[i - 1].lineNum + contextLinesAfter + 1
      ) {
        outputLines.push("...");
      }

      // Add lines with context
      for (let lineNum = startContext; lineNum <= endContext; lineNum++) {
        if (!processedLines.has(lineNum)) {
          processedLines.add(lineNum);
          const line = fileLines[lineNum - 1];
          const lineNumStr = String(lineNum).padStart(6);
          const isMatch = lineNum === match.lineNum;
          const prefix = isMatch ? ">" : " ";
          outputLines.push(`${prefix}${lineNumStr}\t${line}`);
        }
      }
    }

    // Add final count
    outputLines.push(`\nTotal matches: ${matches.length}`);
    outputLines.push(`Total lines in file: ${fileLines.length}`);

    return successToolResponse(outputLines.join("\n"));
  }
}

/**
 * Validates a view range against file line count
 * If invalid values are provided, falls back to safe defaults instead of throwing errors
 * Returns a message describing any adjustments made to the range
 */
export function validateViewRange(
  viewRange: unknown,
  numLinesFile: number,
  minViewSize: number,
): { initLine: number; finalLine: number; message: string } {
  let message = "";

  // Default to showing the whole file if viewRange is not a valid array of two numbers
  if (
    !Array.isArray(viewRange) ||
    viewRange.length !== 2 ||
    !viewRange.every((i) => typeof i === "number")
  ) {
    message = `Invalid view range provided. Showing entire file (lines 1-${numLinesFile}).`;
    return { initLine: 1, finalLine: numLinesFile, message };
  }

  let initLine = viewRange[0] as number;
  let finalLine = viewRange[1] as number;

  if (initLine < 1) {
    message += `Start line ${initLine} is less than 1. Adjusted to 1.\n`;
    initLine = 1;
  } else if (initLine > numLinesFile) {
    message += `Start line ${initLine} exceeds file length (${numLinesFile}). Adjusted to 1.\n`;
    initLine = 1;
  }

  if (finalLine === -1) {
    finalLine = numLinesFile;
  } else if (finalLine > numLinesFile) {
    message += `End line ${finalLine} exceeds file length (${numLinesFile}). Adjusted to ${numLinesFile}. `;
    finalLine = numLinesFile;
  } else if (finalLine < initLine) {
    message += `End line ${finalLine} is less than start line ${initLine}. Adjusted to ${numLinesFile}. `;
    finalLine = numLinesFile;
  } else if (finalLine - initLine + 1 < minViewSize) {
    const finalLineExpanded = Math.min(
      initLine + minViewSize - 1,
      numLinesFile,
    );
    message += `View range expanded to meet minimum size of ${minViewSize} lines. `;

    if (finalLineExpanded >= numLinesFile) {
      message += `End line adjusted to last line of file (${numLinesFile}).`;
    } else {
      message += `New range: [${initLine}, ${finalLineExpanded}].`;
    }
    finalLine = finalLineExpanded;
  }

  // Trim any trailing spaces from the message
  message = message.trim();

  return { initLine, finalLine, message };
}
