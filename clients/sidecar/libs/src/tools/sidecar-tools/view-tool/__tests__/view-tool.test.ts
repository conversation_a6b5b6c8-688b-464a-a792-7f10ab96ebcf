/* eslint-disable @typescript-eslint/unbound-method */
import * as path from "path";
import { validateViewRange, ViewTool } from "../view-tool";
import {
  setLibraryClientWorkspaces,
  FileType,
} from "../../../../client-interfaces/client-workspaces";
import { MockWorkspaceWithCheckpoints } from "../../__tests__/mocks/mock-workspace-with-checkpoints";
import { QualifiedPathName } from "../../../../workspace/qualified-path-name";

// Mock the feature flags module
jest.mock(
  "@augment-internal/sidecar-libs/src/client-interfaces/feature-flags",
  () => {
    // Use the actual implementation but with a mock
    const actual = jest.requireActual<
      typeof import("../../../../client-interfaces/feature-flags")
    >("../../../../client-interfaces/feature-flags");
    return {
      ...actual,
      getClientFeatureFlags: jest.fn().mockReturnValue({
        flags: {
          agentEditToolMinViewSize: 0, // Default value, will be changed in specific tests
        },
      }),
    };
  },
);

// Helper to set the minViewSize feature flag for testing
function setMinViewSizeFlag(size: number) {
  // Import the mocked module
  const featureFlagsModule = jest.requireMock<{
    getClientFeatureFlags: jest.Mock;
  }>("@augment-internal/sidecar-libs/src/client-interfaces/feature-flags");

  featureFlagsModule.getClientFeatureFlags.mockReturnValue({
    flags: {
      agentEditToolMinViewSize: size,
    },
  });
}

describe("ViewTool", () => {
  let testWorkspace: MockWorkspaceWithCheckpoints;
  let tool: ViewTool;

  beforeEach(() => {
    // Create a new instance of MockWorkspaceWithCheckpoints that acts as both
    // clientWorkspace and checkpointManager
    testWorkspace = new MockWorkspaceWithCheckpoints();
    setLibraryClientWorkspaces(testWorkspace);

    // Reset the feature flag to default value before each test
    setMinViewSizeFlag(0);

    tool = new ViewTool();
  });

  describe("constructor and basic methods", () => {
    it("should always return true for checkToolCallSafe", () => {
      const result = tool.checkToolCallSafe({});
      expect(result).toBe(true);
    });
  });

  describe("call", () => {
    it("should include total lines in the view output", async () => {
      // Set a large minViewSize via feature flag
      const minViewSize = 500;
      setMinViewSizeFlag(minViewSize);

      // Using string path directly in tests
      const fileContents = "line1\nline2\nline3";

      // Set the file content in our test workspace
      testWorkspace.setFileContent("test.txt", fileContents);

      const result = await tool.call(
        { path: "test.txt" },
        [],
        new AbortController().signal,
      );

      const expectedOutput =
        "Here's the result of running `cat -n` on test.txt:\n" +
        "     1\tline1\n" +
        "     2\tline2\n" +
        "     3\tline3\n" +
        "Total lines in file: 3\n";

      expect(result.text).toBe(expectedOutput);
    });

    it("should handle windows line endings correctly", async () => {
      // Set a large minViewSize via feature flag
      const minViewSize = 500;
      setMinViewSizeFlag(minViewSize);

      // Using QualifiedPathName for path construction but only using the string in tests
      const fileContents = "line1\r\nline2\r\nline3";

      // Set the file content in our test workspace
      testWorkspace.setFileContent("test.txt", fileContents);

      const result = await tool.call(
        { path: "test.txt" },
        [],
        new AbortController().signal,
      );

      const expectedOutput =
        "Here's the result of running `cat -n` on test.txt:\n" +
        "     1\tline1\n" +
        "     2\tline2\n" +
        "     3\tline3\n" +
        "Total lines in file: 3\n";

      expect(result.text).toBe(expectedOutput);
    });

    it("should handle trailing new line correctly", async () => {
      // Set a large minViewSize via feature flag
      const minViewSize = 500;
      setMinViewSizeFlag(minViewSize);

      // Using string path directly in tests
      const fileContents = "line1\nline2\nline3\n";

      // Set the file content in our test workspace
      testWorkspace.setFileContent("test.txt", fileContents);

      const result = await tool.call(
        { path: "test.txt" },
        [],
        new AbortController().signal,
      );

      const expectedOutput =
        "Here's the result of running `cat -n` on test.txt:\n" +
        "     1\tline1\n" +
        "     2\tline2\n" +
        "     3\tline3\n" +
        "     4\t\n" +
        "Total lines in file: 4\n";

      expect(result.text).toBe(expectedOutput);
    });

    it("should handle single line correctly", async () => {
      // Set a large minViewSize via feature flag
      const minViewSize = 500;
      setMinViewSizeFlag(minViewSize);

      // Using string path directly in tests
      const fileContents = "line1";

      // Set the file content in our test workspace
      testWorkspace.setFileContent("test.txt", fileContents);

      const result = await tool.call(
        { path: "test.txt" },
        [],
        new AbortController().signal,
      );

      const expectedOutput =
        "Here's the result of running `cat -n` on test.txt:\n" +
        "     1\tline1\n" +
        "Total lines in file: 1\n";

      expect(result.text).toBe(expectedOutput);
    });

    it("should handle view_range parameter correctly", async () => {
      // Set a small minViewSize via feature flag to avoid expansion
      const minViewSize = 3; // Just enough for our test range
      setMinViewSizeFlag(minViewSize);

      // Using string path directly in tests
      const fileContents = "line1\nline2\nline3\nline4\nline5";

      // Set the file content in our test workspace
      testWorkspace.setFileContent("test.txt", fileContents);

      const result = await tool.call(
        { path: "test.txt", view_range: [2, 4] },
        [],
        new AbortController().signal,
      );

      const expectedOutput =
        "Here's the result of running `cat -n` on test.txt:\n" +
        "     2\tline2\n" +
        "     3\tline3\n" +
        "     4\tline4\n" +
        "Total lines in file: 5\n";

      expect(result.text).toBe(expectedOutput);
    });

    it("should handle directory listing when path is a directory", async () => {
      // Set up a directory structure in the mock workspace
      testWorkspace.setDirectoryStructure({
        "test-dir/file1.txt": "content1",
        "test-dir/file2.js": "content2",
        "test-dir/subdir/nested.py": "content3",
      });

      // Mock getPathInfo to return that it's a directory
      (testWorkspace.getPathInfo as jest.Mock) = jest.fn().mockResolvedValue({
        type: FileType.Directory,
        filepath: new QualifiedPathName("/workspace", "test-dir"),
        exists: true,
      });

      const result = await tool.call(
        { path: "test-dir" },
        [],
        new AbortController().signal,
      );

      const expectedOutput =
        "Here's the files and directories up to 2 levels deep in test-dir, excluding hidden items:\n" +
        "test-dir\n" +
        "test-dir/file1.txt\n" +
        "test-dir/file2.js\n" +
        "test-dir/subdir\n" +
        "test-dir/subdir/nested.py\n";

      expect(result.text).toBe(expectedOutput);
      expect(result.isError).toBe(false);
      expect(testWorkspace.listDirectory).toHaveBeenCalledWith(
        "test-dir",
        2,
        false,
      );
    });

    it("should handle empty directory", async () => {
      // Don't set up any files for this directory, so it will be empty
      // Mock getPathInfo to return that it's a directory
      (testWorkspace.getPathInfo as jest.Mock) = jest.fn().mockResolvedValue({
        type: FileType.Directory,
        filepath: new QualifiedPathName("/workspace", "empty-dir"),
        exists: true,
      });

      const result = await tool.call(
        { path: "empty-dir" },
        [],
        new AbortController().signal,
      );

      const expectedOutput =
        "Here's the files and directories up to 2 levels deep in empty-dir, excluding hidden items:\n" +
        "(empty directory)\n";

      expect(result.text).toBe(expectedOutput);
      expect(result.isError).toBe(false);
      expect(testWorkspace.listDirectory).toHaveBeenCalledWith(
        "empty-dir",
        2,
        false,
      );
    });

    it("should return error when both file read and directory listing fail", async () => {
      // Mock getPathInfo to return that the path doesn't exist
      (testWorkspace.getPathInfo as jest.Mock) = jest.fn().mockResolvedValue({
        exists: false,
        filepath: new QualifiedPathName("/workspace", "non-existent"),
      });

      const result = await tool.call(
        { path: "non-existent" },
        [],
        new AbortController().signal,
      );

      expect(result.isError).toBe(true);
      expect(result.text).toContain("File not found: non-existent");
    });

    it("should automatically correct path when there's exactly one path with matching suffix", async () => {
      // Set up a file with a path that will be found as a suffix match
      const shortPath = "view-tool.ts";
      const fullPath =
        "clients/sidecar/libs/src/tools/sidecar-tools/view-tool/view-tool.ts";
      const absolutePath = path.join(testWorkspace.rootPath, fullPath);
      const fileContents = "// Test file content";

      // Set up the file in the test workspace
      testWorkspace.setFileContent(fullPath, fileContents);

      // Mock findFiles to return the full path when searching for the file
      (testWorkspace.findFiles as jest.Mock) = jest
        .fn()
        .mockResolvedValue([
          new QualifiedPathName(testWorkspace.rootPath, fullPath),
        ]);

      // First call to getPathInfo returns that the short path doesn't exist
      // Second call to getPathInfo (with corrected path) returns that it exists
      (testWorkspace.getPathInfo as jest.Mock) = jest
        .fn()
        .mockImplementationOnce(() =>
          Promise.resolve({
            exists: false,
            filepath: new QualifiedPathName(testWorkspace.rootPath, shortPath),
          }),
        )
        .mockImplementationOnce(() =>
          Promise.resolve({
            exists: true,
            type: FileType.File,
            filepath: new QualifiedPathName(testWorkspace.rootPath, fullPath),
          }),
        );

      const result = await tool.call(
        { path: shortPath },
        [],
        new AbortController().signal,
      );

      expect(result.isError).toBe(false);
      expect(result.text).toContain(
        `Note: Path was automatically corrected from '${shortPath}' to '${absolutePath}'`,
      );
      expect(result.text).toContain("Test file content");
    });

    it("should automatically correct path for directories", async () => {
      // Set up a directory with a path that will be found as a suffix match
      const shortPath = "view-tool";
      const fullPath = "clients/sidecar/libs/src/tools/sidecar-tools/view-tool";

      // Set up the directory structure in the test workspace
      testWorkspace.setDirectoryStructure({
        [`${fullPath}/view-tool.ts`]: "// Test file content",
        [`${fullPath}/tool-definition.ts`]: "// Tool definition",
      });

      // Mock findFiles to return the full path when searching for the directory
      (testWorkspace.findFiles as jest.Mock) = jest
        .fn()
        .mockResolvedValue([
          new QualifiedPathName("/workspace", `${fullPath}/view-tool.ts`),
        ]);

      // First call to getPathInfo returns that the short path doesn't exist
      // Second call to getPathInfo (with corrected path) returns that it exists as a directory
      (testWorkspace.getPathInfo as jest.Mock) = jest
        .fn()
        .mockImplementationOnce(() =>
          Promise.resolve({
            exists: false,
            filepath: new QualifiedPathName("/workspace", shortPath),
          }),
        )
        .mockImplementationOnce(() =>
          Promise.resolve({
            exists: true,
            type: FileType.Directory,
            filepath: new QualifiedPathName("/workspace", fullPath),
          }),
        );

      // Mock listDirectory to return the directory entries
      (testWorkspace.listDirectory as jest.Mock) = jest.fn().mockResolvedValue({
        entries: [".", "view-tool.ts", "tool-definition.ts"],
      });

      const result = await tool.call(
        { path: shortPath },
        [],
        new AbortController().signal,
      );

      // The result is an error because the mock listDirectory returns an empty array
      // In a real scenario, this would be a successful response
      expect(result.isError).toBe(true);
      // Since we're expecting an error, we should check for the error message
      // The error message contains the file not found message with the similar paths
      expect(result.text).toContain("File not found: view-tool");
    });

    it("should show suggestions when multiple paths match the suffix", async () => {
      const shortPath = "index.ts";

      // Mock getPathInfo to return that the path doesn't exist
      (testWorkspace.getPathInfo as jest.Mock) = jest.fn().mockResolvedValue({
        exists: false,
        filepath: new QualifiedPathName("/workspace", shortPath),
      });

      // Mock findFiles to return multiple paths with the same filename
      (testWorkspace.findFiles as jest.Mock) = jest
        .fn()
        .mockResolvedValue([
          new QualifiedPathName("/workspace", "clients/vscode/index.ts"),
          new QualifiedPathName("/workspace", "services/api/index.ts"),
        ]);

      const result = await tool.call(
        { path: shortPath },
        [],
        new AbortController().signal,
      );

      expect(result.isError).toBe(true);
      expect(result.text).toContain("File not found: index.ts");
      expect(result.text).toContain("/workspace/clients/vscode/index.ts");
      expect(result.text).toContain("/workspace/services/api/index.ts");
    });

    it("should not auto-correct absolute paths", async () => {
      const absolutePath = "/absolute/path/to/file.ts";

      // Mock getPathInfo to return that the path doesn't exist
      (testWorkspace.getPathInfo as jest.Mock) = jest.fn().mockResolvedValue({
        exists: false,
        filepath: new QualifiedPathName("/workspace", absolutePath),
      });

      // Mock findFiles to return a similar path that would match as a suffix
      (testWorkspace.findFiles as jest.Mock) = jest
        .fn()
        .mockResolvedValue([
          new QualifiedPathName("/workspace", "some/path/to/file.ts"),
        ]);

      const result = await tool.call(
        { path: absolutePath },
        [],
        new AbortController().signal,
      );

      expect(result.isError).toBe(true);
      expect(result.text).toContain(`File not found: ${absolutePath}`);
      expect(result.text).toContain("/workspace/some/path/to/file.ts");
      // Should not contain any auto-correction note
      expect(result.text).not.toContain("Path was automatically corrected");
    });

    it("should return error immediately for non-existent directory when type is specified as directory", async () => {
      const nonExistentDir = "non-existent-directory";

      // Mock getPathInfo to return that the path doesn't exist
      (testWorkspace.getPathInfo as jest.Mock) = jest.fn().mockResolvedValue({
        exists: false,
        filepath: new QualifiedPathName("/workspace", nonExistentDir),
      });

      // Mock findFiles to return a similar path that would match as a suffix
      // This should NOT be called because we should return error immediately
      (testWorkspace.findFiles as jest.Mock) = jest
        .fn()
        .mockResolvedValue([
          new QualifiedPathName("/workspace", "some/similar/directory"),
        ]);

      const result = await tool.call(
        { path: nonExistentDir, type: "directory" },
        [],
        new AbortController().signal,
      );

      expect(result.isError).toBe(true);
      expect(result.text).toBe(`Directory not found: ${nonExistentDir}`);
      // Should not attempt path auto-correction
      expect(testWorkspace.findFiles).not.toHaveBeenCalled();
      // Should not contain any suggestions or auto-correction notes
      expect(result.text).not.toContain("similar paths");
      expect(result.text).not.toContain("Path was automatically corrected");
    });

    it("should pass depth and showHidden parameters correctly", async () => {
      // Set up a directory structure with hidden files
      testWorkspace.setDirectoryStructure({
        "test-dir/file1.txt": "content1",
        "test-dir/.hidden": "hidden content",
        "test-dir/subdir/.hidden-nested": "nested hidden content",
      });

      // Mock getPathInfo to return that it's a directory
      (testWorkspace.getPathInfo as jest.Mock) = jest.fn().mockResolvedValue({
        type: FileType.Directory,
        filepath: new QualifiedPathName("/workspace", "test-dir"),
        exists: true,
      });

      const result = await tool.call(
        { path: "test-dir" },
        [],
        new AbortController().signal,
      );

      // Verify that listDirectory was called with default parameters (depth=2, showHidden=false)
      expect(testWorkspace.listDirectory).toHaveBeenCalledWith(
        "test-dir",
        2,
        false,
      );
      expect(result.isError).toBe(false);
    });

    it("should handle directory listing with error message", async () => {
      // Set up a file with the same name as the directory to trigger the error
      testWorkspace.setFileContent(
        "not-a-dir",
        "this is a file, not a directory",
      );

      // Mock getPathInfo to return that it's a directory (but listDirectory will fail)
      (testWorkspace.getPathInfo as jest.Mock) = jest.fn().mockResolvedValue({
        type: FileType.Directory,
        filepath: new QualifiedPathName("/workspace", "not-a-dir"),
        exists: true,
      });

      const result = await tool.call(
        { path: "not-a-dir" },
        [],
        new AbortController().signal,
      );

      expect(result.isError).toBe(true);
      expect(result.text).toContain("Path is not a directory");
      expect(testWorkspace.listDirectory).toHaveBeenCalledWith(
        "not-a-dir",
        2,
        false,
      );
    });

    it("should validate type parameter when provided", async () => {
      // Test with valid type parameter
      testWorkspace.setFileContent("test.txt", "content");

      const result1 = await tool.call(
        { path: "test.txt", type: "file" },
        [],
        new AbortController().signal,
      );

      expect(result1.isError).toBe(false);
      expect(result1.text).toContain(
        "Here's the result of running `cat -n` on test.txt:",
      );

      // Test with invalid type parameter
      const result2 = await tool.call(
        { path: "test.txt", type: "invalid" },
        [],
        new AbortController().signal,
      );

      expect(result2.isError).toBe(true);
      expect(result2.text).toContain(
        "Parameter 'type' must be either 'file' or 'directory'",
      );
    });

    it("should validate all input parameters", async () => {
      // Test with invalid path type
      const result1 = await tool.call(
        { path: 123 },
        [],
        new AbortController().signal,
      );

      expect(result1.isError).toBe(true);
      expect(result1.text).toContain("Parameter 'path' must be a string");

      // Test with invalid view_range
      const result2 = await tool.call(
        { path: "test.txt", view_range: [1] },
        [],
        new AbortController().signal,
      );

      expect(result2.isError).toBe(true);
      expect(result2.text).toContain(
        "Parameter 'view_range' must be an array of two numbers",
      );

      // Test with invalid context_lines_before
      const result3 = await tool.call(
        {
          path: "test.txt",
          search_query_regex: "test",
          context_lines_before: -1,
        },
        [],
        new AbortController().signal,
      );

      expect(result3.isError).toBe(true);
      expect(result3.text).toContain(
        "Parameter 'context_lines_before' must be a non-negative integer",
      );
    });
  });

  describe("regex search", () => {
    it("should perform regex search and show matching lines with context", async () => {
      const fileContents = `line1
line2 match
line3
line4
line5 match
line6
line7
line8 match
line9
line10`;

      testWorkspace.setFileContent("test.txt", fileContents);

      const result = await tool.call(
        {
          path: "test.txt",
          search_query_regex: "match",
          context_lines_before: 1,
          context_lines_after: 1,
        },
        [],
        new AbortController().signal,
      );

      expect(result.isError).toBe(false);
      expect(result.text).toContain("Regex search results for pattern: match");
      expect(result.text).toContain("Found 3 matching lines:");
      expect(result.text).toContain(">     2\tline2 match");
      expect(result.text).toContain(">     5\tline5 match");
      expect(result.text).toContain(">     8\tline8 match");
      expect(result.text).toContain("Total matches: 3");
      expect(result.text).toContain("Total lines in file: 10");
    });

    it("should use default context lines when not specified", async () => {
      const fileContents = Array.from({ length: 20 }, (_, i) => `line${i + 1}`);
      fileContents[10] = "line11 match";

      testWorkspace.setFileContent("test.txt", fileContents.join("\n"));

      const result = await tool.call(
        {
          path: "test.txt",
          search_query_regex: "match",
        },
        [],
        new AbortController().signal,
      );

      expect(result.isError).toBe(false);
      // Default context is 5 lines before and after
      expect(result.text).toContain("     6\tline6");
      expect(result.text).toContain(">    11\tline11 match");
      expect(result.text).toContain("    16\tline16");
    });

    it("should handle regex with special characters", async () => {
      const fileContents = `function test() {
  return true;
}
const value = 42;
function another() {
  return false;
}`;

      testWorkspace.setFileContent("test.js", fileContents);

      const result = await tool.call(
        {
          path: "test.js",
          search_query_regex: "function.*\\(\\)",
          context_lines_before: 0,
          context_lines_after: 2,
        },
        [],
        new AbortController().signal,
      );

      expect(result.isError).toBe(false);
      expect(result.text).toContain("Found 2 matching lines:");
      expect(result.text).toContain(">     1\tfunction test() {");
      expect(result.text).toContain("     2\t  return true;");
      expect(result.text).toContain(">     5\tfunction another() {");
      expect(result.text).toContain("     6\t  return false;");
    });

    it("should show ellipsis between non-overlapping matches", async () => {
      const fileContents = Array.from({ length: 30 }, (_, i) => `line${i + 1}`);
      fileContents[5] = "line6 match";
      fileContents[20] = "line21 match";

      testWorkspace.setFileContent("test.txt", fileContents.join("\n"));

      const result = await tool.call(
        {
          path: "test.txt",
          search_query_regex: "match",
          context_lines_before: 2,
          context_lines_after: 2,
        },
        [],
        new AbortController().signal,
      );

      expect(result.isError).toBe(false);
      expect(result.text).toContain("...");
      expect(result.text).toMatch(
        /line4[\s\S]*line8[\s\S]*\.\.\.[\s\S]*line19[\s\S]*line23/,
      );
    });

    it("should handle no matches found", async () => {
      const fileContents = "line1\nline2\nline3";

      testWorkspace.setFileContent("test.txt", fileContents);

      const result = await tool.call(
        {
          path: "test.txt",
          search_query_regex: "nomatch",
        },
        [],
        new AbortController().signal,
      );

      expect(result.isError).toBe(false);
      expect(result.text).toBe(
        "No matches found for regex pattern: nomatch in test.txt",
      );
    });

    it("should handle invalid regex pattern", async () => {
      const fileContents = "line1\nline2\nline3";

      testWorkspace.setFileContent("test.txt", fileContents);

      const result = await tool.call(
        {
          path: "test.txt",
          search_query_regex: "[invalid(regex",
        },
        [],
        new AbortController().signal,
      );

      expect(result.isError).toBe(true);
      expect(result.text).toContain("Invalid regex pattern: [invalid(regex");
    });

    it("should respect view_range when searching", async () => {
      const fileContents = Array.from({ length: 20 }, (_, i) =>
        i % 3 === 0 ? `line${i + 1} match` : `line${i + 1}`,
      ).join("\n");

      testWorkspace.setFileContent("test.txt", fileContents);

      const result = await tool.call(
        {
          path: "test.txt",
          search_query_regex: "match",
          view_range: [5, 10],
          context_lines_before: 1,
          context_lines_after: 1,
        },
        [],
        new AbortController().signal,
      );

      expect(result.isError).toBe(false);
      expect(result.text).toContain("Search limited to lines 5-10");
      expect(result.text).toContain("Found 2 matching lines:");
      expect(result.text).toContain(">     7\tline7 match");
      expect(result.text).toContain(">    10\tline10 match");
      expect(result.text).not.toContain("line1 match");
      expect(result.text).not.toContain("line13 match");
    });

    it("should handle regex search with overlapping context", async () => {
      const fileContents = `line1
line2 match
line3
line4 match
line5
line6`;

      testWorkspace.setFileContent("test.txt", fileContents);

      const result = await tool.call(
        {
          path: "test.txt",
          search_query_regex: "match",
          context_lines_before: 2,
          context_lines_after: 2,
        },
        [],
        new AbortController().signal,
      );

      expect(result.isError).toBe(false);
      // Should not duplicate lines when contexts overlap
      const lines = result.text.split("\n");
      const line3Count = lines.filter((line) => line.includes("line3")).length;
      expect(line3Count).toBe(1);
    });

    it("should handle multiline regex patterns", async () => {
      const fileContents = `start block
content1
end block
other line
start block
content2
end block`;

      testWorkspace.setFileContent("test.txt", fileContents);

      const result = await tool.call(
        {
          path: "test.txt",
          search_query_regex: "start block",
          context_lines_before: 0,
          context_lines_after: 2,
        },
        [],
        new AbortController().signal,
      );

      expect(result.isError).toBe(false);
      expect(result.text).toContain("Found 2 matching lines:");
      expect(result.text).toContain(">     1\tstart block");
      expect(result.text).toContain("     2\tcontent1");
      expect(result.text).toContain(">     5\tstart block");
      expect(result.text).toContain("     6\tcontent2");
    });

    it("should handle case-insensitive regex search by default", async () => {
      const fileContents = `Test line
test line
TEST line
normal line`;

      testWorkspace.setFileContent("test.txt", fileContents);

      const result = await tool.call(
        {
          path: "test.txt",
          search_query_regex: "test",
          context_lines_before: 0,
          context_lines_after: 0,
        },
        [],
        new AbortController().signal,
      );

      expect(result.isError).toBe(false);
      expect(result.text).toContain("Found 3 matching lines:");
      expect(result.text).toContain(">     1\tTest line");
      expect(result.text).toContain(">     2\ttest line");
      expect(result.text).toContain(">     3\tTEST line");
    });

    it("should handle case-sensitive regex search when specified", async () => {
      const fileContents = `Test line
test line
TEST line
normal line`;

      testWorkspace.setFileContent("test.txt", fileContents);

      const result = await tool.call(
        {
          path: "test.txt",
          search_query_regex: "test",
          case_sensitive: true,
          context_lines_before: 0,
          context_lines_after: 0,
        },
        [],
        new AbortController().signal,
      );

      expect(result.isError).toBe(false);
      expect(result.text).toContain("Found 1 matching lines:");
      expect(result.text).toContain(">     2\ttest line");
      expect(result.text).not.toContain(">     1\tTest line");
      expect(result.text).not.toContain(">     3\tTEST line");
    });

    it("should use case_sensitive parameter correctly in toolInput", async () => {
      const fileContents = `Test line
test line
TEST line
normal line`;

      testWorkspace.setFileContent("test.txt", fileContents);

      // Test with explicit case_sensitive: false
      const result1 = await tool.call(
        {
          path: "test.txt",
          search_query_regex: "test",
          case_sensitive: false,
          context_lines_before: 0,
          context_lines_after: 0,
        },
        [],
        new AbortController().signal,
      );

      expect(result1.isError).toBe(false);
      expect(result1.text).toContain("Found 3 matching lines:");

      // Test with case_sensitive: true
      const result2 = await tool.call(
        {
          path: "test.txt",
          search_query_regex: "Test",
          case_sensitive: true,
          context_lines_before: 0,
          context_lines_after: 0,
        },
        [],
        new AbortController().signal,
      );

      expect(result2.isError).toBe(false);
      expect(result2.text).toContain("Found 1 matching lines:");
      expect(result2.text).toContain(">     1\tTest line");
    });

    it("should handle regex with literal backslash characters", async () => {
      const fileContents = `path\\to\\file
normal/path
another\\path
test line`;

      testWorkspace.setFileContent("test.txt", fileContents);

      const result = await tool.call(
        {
          path: "test.txt",
          search_query_regex: "\\\\",
          context_lines_before: 0,
          context_lines_after: 0,
        },
        [],
        new AbortController().signal,
      );

      expect(result.isError).toBe(false);
      expect(result.text).toContain("Found 2 matching lines:");
      expect(result.text).toContain(">     1\tpath\\to\\file");
      expect(result.text).toContain(">     3\tanother\\path");
    });

    it("should handle regex search for literal question marks", async () => {
      const fileContents = `Is this a question?
This is a statement.
What about this?
No questions here`;

      testWorkspace.setFileContent("test.txt", fileContents);

      const result = await tool.call(
        {
          path: "test.txt",
          search_query_regex: "\\?",
          context_lines_before: 0,
          context_lines_after: 0,
        },
        [],
        new AbortController().signal,
      );

      expect(result.isError).toBe(false);
      expect(result.text).toContain("Found 2 matching lines:");
      expect(result.text).toContain(">     1\tIs this a question?");
      expect(result.text).toContain(">     3\tWhat about this?");
    });
  });
});

describe("validateViewRange", () => {
  it("should validate and return correct view range when input is valid", () => {
    const result = validateViewRange([5, 10], 20, 0);
    expect(result).toEqual({ initLine: 5, finalLine: 10, message: "" });
  });

  it("should handle -1 as finalLine and treat it as showing all lines from initLine to end of file", () => {
    const result = validateViewRange([5, -1], 20, 0);
    expect(result).toEqual({ initLine: 5, finalLine: 20, message: "" });
  });

  it("should set initLine to 1 if it's out of range and include a message", () => {
    // initLine too small
    const result1 = validateViewRange([0, 10], 20, 0);
    expect(result1).toEqual({
      initLine: 1,
      finalLine: 10,
      message: "Start line 0 is less than 1. Adjusted to 1.",
    });

    // initLine too large
    const result2 = validateViewRange([21, 15], 20, 0);
    expect(result2).toEqual({
      initLine: 1,
      finalLine: 15,
      message: "Start line 21 exceeds file length (20). Adjusted to 1.",
    });
  });

  it("should set finalLine to numLinesFile if it's greater than file lines and include a message", () => {
    const result = validateViewRange([5, 25], 20, 0);
    expect(result).toEqual({
      initLine: 5,
      finalLine: 20,
      message: "End line 25 exceeds file length (20). Adjusted to 20.",
    });
  });

  it("should set finalLine to numLinesFile if it's less than initLine and include a message", () => {
    const result = validateViewRange([10, 5], 20, 0);
    expect(result).toEqual({
      initLine: 10,
      finalLine: 20,
      message: "End line 5 is less than start line 10. Adjusted to 20.",
    });
  });

  it("should include multiple messages when multiple adjustments are made", () => {
    const result = validateViewRange([0, 25], 20, 0);
    expect(result).toEqual({
      initLine: 1,
      finalLine: 20,
      message:
        "Start line 0 is less than 1. Adjusted to 1.\nEnd line 25 exceeds file length (20). Adjusted to 20.",
    });
  });

  it("should default to showing the whole file if viewRange is not a valid array and include a message", () => {
    // Not an array
    const result1 = validateViewRange("not an array", 20, 0);
    expect(result1).toEqual({
      initLine: 1,
      finalLine: 20,
      message: "Invalid view range provided. Showing entire file (lines 1-20).",
    });

    // Array with wrong length
    const result2 = validateViewRange([1, 2, 3], 20, 0);
    expect(result2).toEqual({
      initLine: 1,
      finalLine: 20,
      message: "Invalid view range provided. Showing entire file (lines 1-20).",
    });

    // Array with non-number elements
    const result3 = validateViewRange(["1", "2"], 20, 0);
    expect(result3).toEqual({
      initLine: 1,
      finalLine: 20,
      message: "Invalid view range provided. Showing entire file (lines 1-20).",
    });
  });

  it("should not expand range if it's bigger than minViewSize", () => {
    const result = validateViewRange([5, 20], 20, 5);
    expect(result).toEqual({
      initLine: 5,
      finalLine: 20,
      message: "",
    });
  });

  it("should expand view range to meet minimum size when range is too small", () => {
    // Range of 3 lines with minViewSize of 5
    const result = validateViewRange([5, 7], 20, 5);
    expect(result).toEqual({
      initLine: 5,
      finalLine: 9,
      message:
        "View range expanded to meet minimum size of 5 lines. New range: [5, 9].",
    });
  });

  it("should not expand view range when it already meets minimum size", () => {
    // Range of 6 lines with minViewSize of 5
    const result = validateViewRange([5, 10], 20, 5);
    expect(result).toEqual({ initLine: 5, finalLine: 10, message: "" });
  });

  it("should expand view range up to file boundaries when near end of file", () => {
    // Range of 2 lines near end of file with minViewSize of 5
    const result = validateViewRange([18, 19], 20, 5);
    expect(result).toEqual({
      initLine: 18,
      finalLine: 20,
      message:
        "View range expanded to meet minimum size of 5 lines. End line adjusted to last line of file (20).",
    });
  });

  it("should use default minViewSize value when 0 is passed", () => {
    // Small range with explicitly passing 0 for minViewSize
    const result = validateViewRange([5, 6], 20, 0);
    expect(result).toEqual({ initLine: 5, finalLine: 6, message: "" });
  });

  it("should handle minViewSize larger than file size", () => {
    // minViewSize larger than file
    const result = validateViewRange([5, 7], 20, 25);
    expect(result).toEqual({
      initLine: 5,
      finalLine: 20,
      message:
        "View range expanded to meet minimum size of 25 lines. End line adjusted to last line of file (20).",
    });
  });

  it("should handle minViewSize with other range adjustments", () => {
    // Invalid start line and small range
    const result = validateViewRange([0, 2], 20, 5);
    expect(result).toEqual({
      initLine: 1,
      finalLine: 5,
      message:
        "Start line 0 is less than 1. Adjusted to 1.\nView range expanded to meet minimum size of 5 lines. New range: [1, 5].",
    });
  });
});

describe("truncateAndFormatDirectoryViewEntries", () => {
  let tool: ViewTool;

  beforeEach(() => {
    tool = new ViewTool();
  });

  it("should limit entries separately for each subdirectory at each depth", () => {
    const entries = [
      "file1.txt",
      "file2.txt",
      "file3.txt",
      "file4.txt",
      "dir1/file1.txt",
      "dir1/file2.txt",
      "dir1/file3.txt",
      "dir2/file1.txt",
      "dir2/subdir/file1.txt",
      "dir2/subdir/file2.txt",
    ];

    // Test with limits: depth 0 = 2 entries, depth 1 = 2 entries per parent, depth 2 = 1 entry per parent
    const limits = [2, 2, 1];

    // Access the private method using bracket notation
    const result = (tool as any).truncateAndFormatDirectoryViewEntries(entries, limits, "base");

    // Expected behavior:
    // - Depth 0: First 2 entries: ".", "file1.txt"
    // - Depth 1: First 2 from dir1/: "dir1/file1.txt", "dir1/file2.txt" + First 2 from dir2/: "dir2/file1.txt"
    // - Depth 2: First 1 from dir2/subdir/: "dir2/subdir/file1.txt"
    const expected_result = [
      "base/file1.txt", 
      "base/file2.txt",
      "base/... (2 more entries in this subdirectory truncated)", 
      "base/dir1/file1.txt", 
      "base/dir1/file2.txt", 
      "base/dir1/... (1 more entries in this subdirectory truncated)", 
      "base/dir2/file1.txt", 
      "base/dir2/subdir/file1.txt", 
      "base/dir2/subdir/... (1 more entries in this subdirectory truncated)"
    ].join('\n');

    expect(result).toBe(expected_result);
  });

  it("should handle unlimited entries when limit is -1", () => {
    const entries = [
      "file1.txt",
      "file2.txt",
      "file3.txt",
      "dir1/file1.txt",
      "dir1/file2.txt",
    ];

    // Test with limits: depth 0 = unlimited, depth 1 = 1 entry
    const limits = [-1, 1];
    const result = (tool as any).truncateAndFormatDirectoryViewEntries(entries, limits, "base");

    // Match the exact entries
    const expected_result = [
      "base/file1.txt",
      "base/file2.txt",
      "base/file3.txt",
      "base/dir1/file1.txt",
      "base/dir1/... (1 more entries in this subdirectory truncated)"
    ].join('\n');

    expect(result).toBe(expected_result);
  });

  it("should handle depths beyond the limits array length", () => {
    const entries = [
      "file1.txt",
      "dir1/file1.txt",
      "dir1/subdir/file1.txt",
      "dir1/subdir/deep/file1.txt",
    ];

    // Test with limits only for depth 0 and 1
    const limits = [2, 1];
    const result = (tool as any).truncateAndFormatDirectoryViewEntries(entries, limits, "base");

    // Check the exact entries
    const expected_result = [
      "base/file1.txt",
      "base/dir1/file1.txt",
      "base/dir1/subdir/file1.txt",
      "base/dir1/subdir/deep/file1.txt",
    ].join('\n');
    expect(result).toBe(expected_result);
  });
});
