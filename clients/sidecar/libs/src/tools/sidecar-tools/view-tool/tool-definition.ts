import { REGEX_SYNTAX_GUIDE } from "../regex-syntax-guide";

export class ViewToolDefinition {
  public readonly description: string = `\
Custom tool for viewing files and directories and searching within files with regex query
* \`path\` is a file or directory path relative to the workspace root
* For files: displays the result of applying \`cat -n\` to the file
* For directories: lists files and subdirectories up to 2 levels deep
* If the output is long, it will be truncated and marked with \`<response clipped>\`

Regex search (for files only):
* Use \`search_query_regex\` to search for patterns in the file using regular expressions
* Use \`case_sensitive\` parameter to control case sensitivity (default: false)
* When using regex search, only matching lines and their context will be shown
* Use \`context_lines_before\` and \`context_lines_after\` to control how many lines of context to show (default: 5)
* Non-matching sections between matches are replaced with \`...\`
* If \`view_range\` is also specified, the search is limited to that range

Use the following regex syntax for \`search_query_regex\`:
${REGEX_SYNTAX_GUIDE}

Notes for using the tool:
* Strongly prefer to use \`search_query_regex\` instead of \`view_range\` when looking for a specific symbol in the file.
* Use the \`view_range\` parameter to specify a range of lines to view, e.g. [501, 1000] will show lines from 501 to 1000
* Indices are 1-based and inclusive
* Setting \`[start_line, -1]\` shows all lines from \`start_line\` to the end of the file
* The \`view_range\` and \`search_query_regex\` parameters are only applicable when viewing files, not directories
`;

  public readonly inputSchemaJson: string = JSON.stringify({
    type: "object",
    properties: {
      type: {
        type: "string",
        description:
          "Type of path to view. Allowed options are: 'file', 'directory'.",
        enum: ["file", "directory"],
      },
      path: {
        description:
          "Full path to file or directory relative to the workspace root, e.g. 'services/api_proxy/file.py' or 'services/api_proxy'.",
        type: "string",
      },
      view_range: {
        description:
          "Optional parameter when `path` points to a file. If none is given, the full file is shown. If provided, the file will be shown in the indicated line number range, e.g. [501, 1000] will show lines from 501 to 1000. Indices are 1-based and inclusive. Setting `[start_line, -1]` shows all lines from `start_line` to the end of the file.",
        type: "array",
        items: { type: "integer" },
      },
      search_query_regex: {
        description:
          "Optional parameter for files only. The regex pattern to search for. Only use core regex syntax common to JavaScript and Rust. See the regex syntax guide in the tool description. When specified, only lines matching the pattern (plus context lines) will be shown. Non-matching sections are replaced with '...'.",
        type: "string",
      },
      case_sensitive: {
        description:
          "Whether the regex search should be case-sensitive. Only used when search_query_regex is specified. Default: false (case-insensitive).",
        type: "boolean",
        default: false,
      },
      context_lines_before: {
        description:
          "Number of lines to show before each regex match. Only used when search_query_regex is specified. Default: 5.",
        type: "integer",
        default: 5,
      },
      context_lines_after: {
        description:
          "Number of lines to show after each regex match. Only used when search_query_regex is specified. Default: 5.",
        type: "integer",
        default: 5,
      },
    },
    required: ["path", "type"],
  });
}
