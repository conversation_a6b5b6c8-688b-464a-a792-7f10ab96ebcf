/**
 * @file task-tools.ts
 * This file contains tools for the agent to interact with the task system.
 */

import { Exchange } from "../../chat/chat-types";
import { getLogger } from "../../logging";
import { errorToolResponse, successToolResponse } from "./tool-use-response";
import { ToolBase, ToolSafety, ToolUseResponse } from "../tool-types";
import { SidecarToolType } from "./sidecar-tool-types";
import { TaskManager } from "../../agent/task/task-manager";
import { TaskListAction } from "../../metrics/types";
import {
  emitAgentTaskListMetric,
  countTasksInTree,
} from "../../metrics/task-metrics-utils";
import {
  TaskUpdatedBy,
  TaskState,
  SerializedTask,
} from "../../agent/task/task-types";
import {
  getMarkdownRepresentation,
  parseMarkdownToTaskTree,
  diffTaskTrees,
  TaskInstructionUtils,
  fromShortUuid,
} from "../../agent/task/task-utils";
import {
  validateTaskUpdateInput,
  buildUpdateObject,
  validateTaskInput,
  validateTaskState,
} from "./task-tools-utils";

/**
 * Tool for viewing the current conversation's task list.
 * This tool retrieves the root task for the current conversation
 * and returns its markdown representation.
 */
export class ViewTaskListTool extends ToolBase<SidecarToolType.viewTaskList> {
  private readonly _logger = getLogger("ViewTaskListTool");

  constructor(private readonly _taskManager: TaskManager) {
    super(SidecarToolType.viewTaskList, ToolSafety.Safe);
  }

  public description = TaskInstructionUtils.getToolDescriptions().viewTaskList;

  public inputSchemaJson = JSON.stringify({
    type: "object",
    properties: {},
    required: [],
  });

  public checkToolCallSafe(): boolean {
    return true; // This tool is always safe
  }

  public async call(
    _toolInput: Record<string, unknown>,
    chatHistory: Exchange[],
    _abortSignal: AbortSignal,
  ): Promise<ToolUseResponse> {
    try {
      const rootTaskUuid = this._taskManager.getCurrentRootTaskUuid();
      if (!rootTaskUuid) {
        return errorToolResponse("No root task found.");
      }

      // Get the hydrated task tree
      const rootTask = await this._taskManager.getHydratedTask(rootTaskUuid);
      if (!rootTask) {
        return errorToolResponse(`Task with UUID ${rootTaskUuid} not found.`);
      }

      // Emit metrics with task count and current exchange ID
      const taskCount = countTasksInTree(rootTask);
      const conversationId =
        chatHistory.length > 0
          ? chatHistory[chatHistory.length - 1].request_id
          : "";
      emitAgentTaskListMetric(
        TaskListAction.viewTaskList,
        conversationId,
        taskCount,
      );

      // Convert the task tree to markdown
      const markdown = getMarkdownRepresentation(rootTask);

      return successToolResponse(
        TaskInstructionUtils.formatTaskListViewResponse(markdown),
      );
    } catch (error) {
      this._logger.error("Error in ViewTaskListTool:", error);
      return errorToolResponse(
        `Failed to view task list: ${error instanceof Error ? error.message : String(error)}`,
      );
    }
  }
}

/**
 * Tool for updating one or more tasks' properties (state, name, description).
 * All updates use batch operations via the tasks array.
 *
 * ## How it works:
 * 1. Validates input (task IDs, state values, string fields)
 * 2. Checks task existence before updating
 * 3. Applies updates atomically per task
 * 4. Returns detailed success/failure results
 *
 * ## Usage:
 * - All updates: Provide `tasks` array with task_id and updates
 * - State changes: Use NOT_STARTED, IN_PROGRESS, CANCELLED, COMPLETE
 * - Partial updates: Only specify fields that need changing
 */
export class UpdateTasksTool extends ToolBase<SidecarToolType.updateTasks> {
  private readonly _logger = getLogger("UpdateTasksTool");

  constructor(private readonly _taskManager: TaskManager) {
    super(SidecarToolType.updateTasks, ToolSafety.Safe);
  }

  public description = TaskInstructionUtils.getToolDescriptions().updateTasks;

  public inputSchemaJson = JSON.stringify({
    type: "object",
    properties: {
      tasks: {
        type: "array",
        description:
          "Array of tasks to update. Each task should have a task_id and the properties to update.",
        items: {
          type: "object",
          properties: {
            task_id: {
              type: "string",
              description: "The UUID of the task to update.",
            },
            state: {
              type: "string",
              enum: ["NOT_STARTED", "IN_PROGRESS", "CANCELLED", "COMPLETE"],
              description:
                "New task state. Use NOT_STARTED for [ ], IN_PROGRESS for [/], CANCELLED for [-], COMPLETE for [x].",
            },
            name: {
              type: "string",
              description: "New task name.",
            },
            description: {
              type: "string",
              description: "New task description.",
            },
          },
          required: ["task_id"],
        },
      },
    },
    required: ["tasks"],
  });

  public checkToolCallSafe(): boolean {
    return true; // This tool is always safe
  }

  /**
   * Main entry point - handles batch task updates only.
   *
   * @param toolInput - Tool input with tasks array
   * @returns Success/error response with update details
   */
  public async call(
    toolInput: Record<string, unknown>,
    chatHistory: Exchange[],
    _abortSignal: AbortSignal,
  ): Promise<ToolUseResponse> {
    try {
      const tasks = toolInput.tasks as
        | Array<Record<string, unknown>>
        | undefined;

      if (!tasks || tasks.length === 0) {
        return errorToolResponse(
          "tasks array is required and must not be empty.",
        );
      }

      // Determine the primary action type for metrics
      const firstTask = tasks[0];
      let action: TaskListAction;
      if (firstTask?.state !== undefined) {
        action = TaskListAction.updateTaskStatus;
      } else if (firstTask?.name !== undefined) {
        action = TaskListAction.updateTaskName;
      } else if (firstTask?.description !== undefined) {
        action = TaskListAction.updateTaskDescription;
      } else {
        // Default action if no specific update type is detected
        action = TaskListAction.updateTaskStatus;
      }

      const result = await this.handleBatchUpdate(tasks);

      // Emit metrics after successful update with current task count
      if (!result.isError) {
        const rootTaskUuid = this._taskManager.getCurrentRootTaskUuid();
        if (rootTaskUuid) {
          const rootTask =
            await this._taskManager.getHydratedTask(rootTaskUuid);
          const taskCount = countTasksInTree(rootTask);
          const conversationId =
            chatHistory.length > 0
              ? chatHistory[chatHistory.length - 1].request_id
              : "";
          emitAgentTaskListMetric(action, conversationId, taskCount);
        }
      }

      return result;
    } catch (error) {
      this._logger.error("Error in UpdateTasksTool:", error);
      return errorToolResponse(
        `Failed to update task(s): ${error instanceof Error ? error.message : String(error)}`,
      );
    }
  }

  /**
   * Checks if task exists and returns it.
   *
   * @param taskId - Task UUID to check
   * @returns Task object
   * @throws Error if task not found
   */
  private async validateTaskExists(taskId: string): Promise<SerializedTask> {
    const task = await this._taskManager.getTask(taskId);
    if (!task) {
      throw new Error(`Task with UUID ${taskId} not found.`);
    }
    return task;
  }

  /**
   * Updates multiple tasks, continues on individual failures.
   *
   * @param tasks - Array of task update inputs
   * @returns Response with results for each task (success/failure)
   */
  private async handleBatchUpdate(
    tasks: Array<Record<string, unknown>>,
  ): Promise<ToolUseResponse> {
    // Get the root task and capture the state before updates
    const rootTaskUuid = this._taskManager.getCurrentRootTaskUuid();
    if (!rootTaskUuid) {
      return errorToolResponse("No root task found.");
    }

    const beforeTree = await this._taskManager.getHydratedTask(rootTaskUuid);
    if (!beforeTree) {
      return errorToolResponse(`Task with UUID ${rootTaskUuid} not found.`);
    }

    const results: Array<{
      taskId: string;
      success: boolean;
      error?: string;
      updates?: Record<string, any>;
    }> = [];

    for (const taskInput of tasks) {
      try {
        const validatedInput = validateTaskUpdateInput(taskInput);

        // Check task exists
        await this.validateTaskExists(validatedInput.taskId);

        // Build updates and apply them
        const updates = buildUpdateObject(validatedInput);
        await this._taskManager.updateTask(
          validatedInput.taskId,
          updates,
          TaskUpdatedBy.AGENT,
        );

        results.push({
          taskId: validatedInput.taskId,
          success: true,
          updates,
        });
      } catch (error) {
        const rawTaskId = (taskInput.task_id as string) || "unknown";
        const taskId =
          rawTaskId !== "unknown" ? fromShortUuid(rawTaskId) : rawTaskId;
        results.push({
          taskId,
          success: false,
          error: error instanceof Error ? error.message : String(error),
        });
      }
    }

    // Get the task tree after updates and compute diff
    const afterTree = await this._taskManager.getHydratedTask(rootTaskUuid);
    if (!afterTree) {
      return errorToolResponse(`Failed to retrieve updated task tree.`);
    }

    // Build response message using diff-based formatting like ReorganizeTaskListTool
    const responseMessage = TaskInstructionUtils.formatBulkUpdateResponse(
      diffTaskTrees(beforeTree, afterTree),
    );
    return successToolResponse(responseMessage);
  }
}

/**
 * Tool for reorganizing the current conversation's task list.
 * This tool takes a markdown representation of tasks from the agent,
 * parses it back into a task tree, and applies the changes to the
 * existing task tree. Use this only for major restructuring like
 * reordering tasks, changing hierarchy, or adding many tasks at once.
 * For individual task updates (state, name, description), use update_task tool.
 */
export class ReorganizeTaskListTool extends ToolBase<SidecarToolType.reorganizeTaskList> {
  private readonly _logger = getLogger("ReorganizeTaskListTool");

  constructor(private readonly _taskManager: TaskManager) {
    super(SidecarToolType.reorganizeTaskList, ToolSafety.Safe);
  }

  public description =
    TaskInstructionUtils.getToolDescriptions().reorganizeTaskList;

  public inputSchemaJson = JSON.stringify({
    type: "object",
    properties: {
      markdown: {
        type: "string",
        description:
          "The markdown representation of the task list to update. Should be in the format specified by the view_tasklist tool. New tasks should have a UUID of 'NEW_UUID'. Must contain exactly one root task with proper hierarchy using dash indentation.",
      },
    },
    required: ["markdown"],
  });

  public checkToolCallSafe(): boolean {
    return true; // This tool is always safe
  }

  public async call(
    toolInput: Record<string, unknown>,
    chatHistory: Exchange[],
    _abortSignal: AbortSignal,
  ): Promise<ToolUseResponse> {
    try {
      // Get the markdown from the tool input
      const markdown = toolInput.markdown as string;
      if (!markdown) {
        return errorToolResponse("No markdown provided.");
      }

      const rootTaskUuid = this._taskManager.getCurrentRootTaskUuid();
      if (!rootTaskUuid) {
        return errorToolResponse("No root task found.");
      }

      // Get the existing task tree
      const existingRootTask =
        await this._taskManager.getHydratedTask(rootTaskUuid);
      if (!existingRootTask) {
        return errorToolResponse(`Task with UUID ${rootTaskUuid} not found.`);
      }

      // Parse the markdown into a new task tree
      let newRootTask;
      try {
        newRootTask = parseMarkdownToTaskTree(markdown);
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : String(error);

        const helpText = TaskInstructionUtils.getMarkdownParsingHelpText();
        return errorToolResponse(
          `Failed to parse markdown: ${errorMessage}\n\n${helpText}`,
        );
      }

      // Preserve the root task's UUID
      newRootTask.uuid = rootTaskUuid;

      // Use the TaskManager's updateHydratedTask method to handle all changes at once
      const { deleted } = await this._taskManager.updateHydratedTask(
        newRootTask,
        TaskUpdatedBy.AGENT,
      );

      // Get the updated task tree to show the new UUIDs
      const updatedRootTask =
        await this._taskManager.getHydratedTask(rootTaskUuid);
      if (!updatedRootTask) {
        return errorToolResponse(`Failed to retrieve updated task tree.`);
      }

      // Emit metrics with updated task count
      const taskCount = countTasksInTree(updatedRootTask);
      const conversationId =
        chatHistory.length > 0
          ? chatHistory[chatHistory.length - 1].request_id
          : "";
      emitAgentTaskListMetric(
        TaskListAction.reorganizeTaskList,
        conversationId,
        taskCount,
      );

      // Emit additional metrics for deletions if any occurred
      if (deleted > 0) {
        emitAgentTaskListMetric(
          TaskListAction.deleteTask,
          conversationId,
          taskCount,
        );
      }

      // Build response with task update information using utility, given the existing and new task trees
      const responseMessage = TaskInstructionUtils.formatBulkUpdateResponse(
        diffTaskTrees(existingRootTask, newRootTask),
      );

      return successToolResponse(responseMessage);
    } catch (error) {
      this._logger.error("Error in UpdateTasksTool:", error);
      return errorToolResponse(
        `Failed to update task list: ${error instanceof Error ? error.message : String(error)}`,
      );
    }
  }
}

/**
 * Tool for adding new tasks to the task list.
 * All task creation uses batch operations via the tasks array.
 *
 * ## How it works:
 * 1. Validates input (name, description, parent, ordering)
 * 2. Creates task in specified parent (defaults to root)
 * 3. Sets initial state (defaults to NOT_STARTED)
 * 4. Positions task after another task if requested
 *
 * ## Usage:
 * - All tasks: Provide `tasks` array with name and description
 * - Subtasks: Add `parent_task_id`
 * - Ordering: Add `after_task_id`
 */
export class AddTasksTool extends ToolBase<SidecarToolType.addTasks> {
  private readonly _logger = getLogger("AddTasksTool");

  constructor(private readonly _taskManager: TaskManager) {
    super(SidecarToolType.addTasks, ToolSafety.Safe);
  }

  public description = TaskInstructionUtils.getToolDescriptions().addTasks;

  public inputSchemaJson = JSON.stringify({
    type: "object",
    properties: {
      tasks: {
        type: "array",
        description:
          "Array of tasks to create. Each task should have name and description.",
        items: {
          type: "object",
          properties: {
            name: {
              type: "string",
              description: "The name of the new task.",
            },
            description: {
              type: "string",
              description: "The description of the new task.",
            },
            parent_task_id: {
              type: "string",
              description:
                "UUID of the parent task if this should be a subtask.",
            },
            after_task_id: {
              type: "string",
              description:
                "UUID of the task after which this task should be inserted.",
            },
            state: {
              type: "string",
              enum: ["NOT_STARTED", "IN_PROGRESS", "CANCELLED", "COMPLETE"],
              description:
                "Initial state of the task. Defaults to NOT_STARTED.",
            },
          },
          required: ["name", "description"],
        },
      },
    },
    required: ["tasks"],
  });

  public checkToolCallSafe(): boolean {
    return true; // This tool is always safe
  }

  /**
   * Main entry point - handles batch task creation only.
   *
   * @param toolInput - Tool input with tasks array
   * @returns Success/error response with created task details
   */
  public async call(
    toolInput: Record<string, unknown>,
    chatHistory: Exchange[],
    _abortSignal: AbortSignal,
  ): Promise<ToolUseResponse> {
    try {
      const tasks = toolInput.tasks as
        | Array<Record<string, unknown>>
        | undefined;

      if (!tasks || tasks.length === 0) {
        return errorToolResponse(
          "tasks array is required and must not be empty.",
        );
      }

      // Determine if adding tasks or subtasks for metrics
      const firstTask = tasks[0];
      const hasParent = firstTask?.parent_task_id !== undefined;
      const action = hasParent
        ? TaskListAction.addSubtask
        : TaskListAction.addTask;

      const result = await this.handleBatchCreation(tasks);

      // Emit metrics after successful creation with updated task count
      if (!result.isError) {
        const rootTaskUuid = this._taskManager.getCurrentRootTaskUuid();
        if (rootTaskUuid) {
          const rootTask =
            await this._taskManager.getHydratedTask(rootTaskUuid);
          const taskCount = countTasksInTree(rootTask);
          const conversationId =
            chatHistory.length > 0
              ? chatHistory[chatHistory.length - 1].request_id
              : "";
          emitAgentTaskListMetric(action, conversationId, taskCount);
        }
      }

      return result;
    } catch (error) {
      this._logger.error("Error in AddTasksTool:", error);
      return errorToolResponse(
        `Failed to add task(s): ${error instanceof Error ? error.message : String(error)}`,
      );
    }
  }

  /**
   * Gets parent task ID, defaults to root task if none specified.
   *
   * @param parentTaskId - Optional parent UUID
   * @returns Parent task UUID
   * @throws Error if parent not found or no root task exists
   */
  private async resolveParentTaskId(
    parentTaskId: string | undefined,
  ): Promise<string> {
    let effectiveParentTaskId = parentTaskId;
    if (!effectiveParentTaskId) {
      effectiveParentTaskId = this._taskManager.getCurrentRootTaskUuid();
      if (!effectiveParentTaskId) {
        throw new Error("No root task found and no parent task specified.");
      }
    }

    const parentTask = await this._taskManager.getTask(effectiveParentTaskId);
    if (!parentTask) {
      throw new Error(
        `Parent task with UUID ${effectiveParentTaskId} not found.`,
      );
    }

    return effectiveParentTaskId;
  }

  /**
   * Positions new task after specified task in parent's subtask list.
   * Silently skips if parent or after task not found.
   *
   * @param newTaskId - Task to position
   * @param afterTaskId - Task to insert after
   * @param parentTaskId - Parent containing both tasks
   */
  private async handleTaskOrdering(
    newTaskId: string,
    afterTaskId: string,
    parentTaskId: string,
  ): Promise<void> {
    const parentTaskForOrdering = await this._taskManager.getTask(parentTaskId);
    if (!parentTaskForOrdering) return; // Parent doesn't exist, skip ordering

    const afterIndex = parentTaskForOrdering.subTasks.indexOf(afterTaskId);
    if (afterIndex === -1) return; // afterTaskId not found, skip ordering

    const updatedSubTasks = parentTaskForOrdering.subTasks.filter(
      (id) => id !== newTaskId,
    );
    updatedSubTasks.splice(afterIndex + 1, 0, newTaskId);

    await this._taskManager.updateTask(
      parentTaskId,
      { subTasks: updatedSubTasks },
      TaskUpdatedBy.AGENT,
    );
  }

  /**
   * Core task creation: validates input, creates task, sets state, handles ordering.
   *
   * @param taskInput - Raw task input
   * @returns New task ID and name
   * @throws Error if validation or creation fails
   */
  private async createSingleTaskFromInput(
    taskInput: Record<string, unknown>,
  ): Promise<{ taskId: string; taskName: string }> {
    // Validate inputs and get typed result
    const validatedInput = validateTaskInput(taskInput);
    const state = validateTaskState(taskInput);

    // Resolve parent, then create task
    const effectiveParentTaskId = await this.resolveParentTaskId(
      validatedInput.parentTaskId,
    );
    const newTaskId = await this._taskManager.createTask(
      validatedInput.name,
      validatedInput.description,
      effectiveParentTaskId,
    );

    // Update state if needed
    if (state !== TaskState.NOT_STARTED) {
      await this._taskManager.updateTask(
        newTaskId,
        { state },
        TaskUpdatedBy.AGENT,
      );
    }

    // Handle ordering if needed
    if (validatedInput.afterTaskId) {
      await this.handleTaskOrdering(
        newTaskId,
        validatedInput.afterTaskId,
        effectiveParentTaskId,
      );
    }

    return { taskId: newTaskId, taskName: validatedInput.name };
  }

  /**
   * Creates multiple tasks, continues on individual failures.
   *
   * @param tasks - Array of task inputs
   * @returns Response with results for each task (success/failure)
   */
  private async handleBatchCreation(
    tasks: Array<Record<string, unknown>>,
  ): Promise<ToolUseResponse> {
    // Get the root task and capture the state before creation
    const rootTaskUuid = this._taskManager.getCurrentRootTaskUuid();
    if (!rootTaskUuid) {
      return errorToolResponse("No root task found.");
    }

    const beforeTree = await this._taskManager.getHydratedTask(rootTaskUuid);
    if (!beforeTree) {
      return errorToolResponse(`Task with UUID ${rootTaskUuid} not found.`);
    }

    const results: Array<{
      taskId?: string;
      taskName?: string;
      success: boolean;
      error?: string;
    }> = [];

    for (const taskInput of tasks) {
      try {
        const result = await this.createSingleTaskFromInput(taskInput);
        results.push({
          taskId: result.taskId,
          taskName: result.taskName,
          success: true,
        });
      } catch (error) {
        const name = taskInput.name as string;
        results.push({
          taskName: name || "unknown",
          success: false,
          error: error instanceof Error ? error.message : String(error),
        });
      }
    }

    // Get the task tree after creation and compute diff
    const afterTree = await this._taskManager.getHydratedTask(rootTaskUuid);
    if (!afterTree) {
      return errorToolResponse(`Failed to retrieve updated task tree.`);
    }

    // Build response message using diff-based formatting like ReorganizeTaskListTool
    const responseMessage = TaskInstructionUtils.formatBulkUpdateResponse(
      diffTaskTrees(beforeTree, afterTree),
    );
    return successToolResponse(responseMessage);
  }
}
