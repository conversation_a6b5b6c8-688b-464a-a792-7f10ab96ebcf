import {
  IClientWorkspaces,
  FileDetails,
  ListDirectoryResult,
  PathInfo,
  FileType,
} from "@augment-internal/sidecar-libs/src/client-interfaces/client-workspaces";
import { AggregateCheckpointManager } from "@augment-internal/sidecar-libs/src/agent/checkpoint/aggregate-checkpoint-manager";
import { QualifiedPathName } from "@augment-internal/sidecar-libs/src/workspace/qualified-path-name";
import {
  DocumentCheckpoint,
  SerializedStore,
} from "@augment-internal/sidecar-libs/src/agent/agent-edit-types";
import { CheckpointKeyInputData } from "@augment-internal/sidecar-libs/src/agent/sharding/types";
import { ShardedStorage } from "@augment-internal/sidecar-libs/src/agent/sharding/storage";
import { delayMs } from "@augment-internal/sidecar-libs/src/utils/promise-utils";
import * as pathLib from "node:path";

/**
 * A simplified test utility class that combines IClientWorkspaces and AggregateCheckpointManager
 * functionality. It maintains an in-memory representation of files and their changes.
 */
export class MockWorkspaceWithCheckpoints
  extends AggregateCheckpointManager
  implements IClientWorkspaces
{
  public rootPath: string = "/root/path";
  private fileContents: Map<string, string> = new Map();
  private autoFormatMapping: Map<string, string> = new Map();
  private checkpoints: DocumentCheckpoint[] = [];
  private _testConversationId: string = "test-conversation";
  private _maxLineLength: number | undefined;
  private _addCheckpointDelayMs: number | undefined;

  override get currentConversationId(): string | undefined {
    return this._testConversationId;
  }

  constructor(options?: {
    maxLineLength?: number;
    addCheckpointDelayMs?: number;
  }) {
    // Create a minimal mock storage with just the required methods
    const mockStorage: Partial<ShardedStorage<SerializedStore>> = {
      save: jest.fn().mockResolvedValue(undefined),
      load: jest.fn().mockResolvedValue(undefined),
      saveShard: jest.fn().mockResolvedValue(undefined),
      loadShard: jest.fn().mockResolvedValue(undefined),
      deleteShard: jest.fn().mockResolvedValue(undefined),
      saveManifest: jest.fn().mockResolvedValue(undefined),
      loadManifest: jest.fn().mockResolvedValue(undefined),
    };

    // Call the parent constructor with minimal required parameters
    super(
      mockStorage as ShardedStorage<SerializedStore>,
      () => undefined, // getMemoriesAbsPath
      () => ({ dispose: () => {} }), // onDocumentChange
      () => ({ dispose: () => {} }), // onFileDeleted
      () => ({ dispose: () => {} }), // onFileDidMove
    );

    // Initialize conversation ID
    this.setCurrentConversation("test-conversation");

    // Set max line length if provided
    this._maxLineLength = options?.maxLineLength;
    this._addCheckpointDelayMs = options?.addCheckpointDelayMs;
  }

  // Override the setter from AggregateCheckpointManager
  override setCurrentConversation(conversationId: string): void {
    this._testConversationId = conversationId;
    super.setCurrentConversation(conversationId);
  }

  // IClientWorkspaces implementation
  getCwd(): Promise<string | undefined> {
    return Promise.resolve(process.cwd());
  }

  getWorkspaceRoot(): Promise<string | undefined> {
    return Promise.resolve(this.rootPath);
  }

  readFile(path: string): Promise<FileDetails> {
    if (pathLib.isAbsolute(path)) {
      path = pathLib.relative(this.rootPath, path);
    }
    const contents = this.fileContents.get(path);
    const filepath = new QualifiedPathName(this.rootPath, path);
    return Promise.resolve({ contents, filepath });
  }

  writeFile(filePath: QualifiedPathName, contents: string): Promise<void> {
    const autoFormatKey = `${filePath.relPath}-${contents}`;
    if (this.autoFormatMapping.has(autoFormatKey)) {
      contents = this.autoFormatMapping.get(autoFormatKey)!;
    }

    // If max line length is set, break long lines
    const processedContents = this._maxLineLength
      ? this.breakLongLines(contents, this._maxLineLength)
      : contents;
    this.fileContents.set(filePath.relPath, processedContents);
    return Promise.resolve();
  }

  deleteFile(filePath: QualifiedPathName): Promise<void> {
    this.fileContents.delete(filePath.relPath);
    return Promise.resolve();
  }

  onFileDeleted = jest.fn();
  onFileDidMove = jest.fn();

  getRipgrepPath(): Promise<string> {
    return Promise.resolve("/usr/bin/rg");
  }

  getQualifiedPathName(path: string): Promise<QualifiedPathName | undefined> {
    return Promise.resolve(new QualifiedPathName(this.rootPath, path));
  }

  getPathInfo = jest.fn((path: string): Promise<PathInfo> => {
    if (pathLib.isAbsolute(path)) {
      path = pathLib.relative(this.rootPath, path);
    }

    // Check if the file exists in our mock file system
    if (this.fileContents.has(path)) {
      return Promise.resolve({
        filepath: new QualifiedPathName(this.rootPath, path),
        type: FileType.File,
        exists: true,
      });
    }

    // If it doesn't exist as a file, return that it doesn't exist
    return Promise.resolve({
      filepath: new QualifiedPathName(this.rootPath, path),
      exists: false,
    });
  });

  findFiles(
    _includeGlob: string,
    _excludeGlob?: string | null,
    _maxResults?: number,
  ): Promise<QualifiedPathName[]> {
    return Promise.resolve([]);
  }

  listDirectory = jest.fn(
    (
      path: string,
      depth: number = 2,
      showHidden: boolean = false,
    ): Promise<ListDirectoryResult> => {
      if (pathLib.isAbsolute(path)) {
        path = pathLib.relative(this.rootPath, path);
      }

      // Check if this path exists as a directory in our mock system
      // For simplicity, we'll consider any path that doesn't exist as a file to be a potential directory
      if (this.fileContents.has(path)) {
        // It's a file, not a directory
        return Promise.resolve({
          entries: [],
          errorMessage: "Path is not a directory",
        });
      }

      // Generate mock directory entries based on the files we have
      const entries: string[] = [];
      const pathPrefix = path === "." || path === "" ? "" : path + "/";

      // Find all files that start with this path
      for (const filePath of this.fileContents.keys()) {
        if (filePath.startsWith(pathPrefix)) {
          const relativePath = filePath.substring(pathPrefix.length);

          // Skip hidden files if showHidden is false
          if (!showHidden && relativePath.startsWith(".")) {
            continue;
          }

          // Handle depth limitation
          const pathParts = relativePath.split("/");
          if (pathParts.length > depth) {
            continue;
          }

          // Add intermediate directories
          for (let i = 1; i <= Math.min(pathParts.length, depth); i++) {
            const partialPath = pathParts.slice(0, i).join("/");
            if (!entries.includes(partialPath)) {
              entries.push(partialPath);
            }
          }
        }
      }

      // Add the directory itself only if there are entries
      if (entries.length > 0 && path !== "." && path !== "") {
        entries.unshift(".");
      }

      // Sort entries for consistent output (but keep "." at the beginning)
      const dotEntry = entries.find((e) => e === ".");
      const otherEntries = entries.filter((e) => e !== ".").sort();
      const sortedEntries = dotEntry
        ? [dotEntry, ...otherEntries]
        : otherEntries;

      return Promise.resolve({ entries: sortedEntries });
    },
  );

  /**
   * Breaks long lines in the content at the specified maximum length
   * @param content The content to process
   * @param maxLength The maximum line length
   * @returns The content with long lines broken
   */
  private breakLongLines(content: string, maxLength: number): string {
    if (!maxLength || maxLength <= 0) return content;

    const lines = content.split("\n");
    const processedLines: string[] = [];

    for (const line of lines) {
      if (line.length <= maxLength) {
        processedLines.push(line);
      } else {
        // Break the line into chunks of maxLength
        let remainingLine = line;
        while (remainingLine.length > maxLength) {
          processedLines.push(remainingLine.substring(0, maxLength).trimEnd());
          remainingLine = remainingLine.substring(maxLength);
        }

        if (remainingLine.length > 0) {
          processedLines.push(remainingLine.trimEnd());
        }
      }
    }

    return processedLines.join("\n");
  }

  // Mock implementation of addCheckpoint that updates in-memory file storage
  addCheckpoint = jest.fn(
    async (
      _key: CheckpointKeyInputData,
      checkpoint: DocumentCheckpoint,
    ): Promise<void> => {
      if (this._addCheckpointDelayMs !== undefined) {
        await delayMs(this._addCheckpointDelayMs);
      }
      this.checkpoints.push(checkpoint);
      if (checkpoint.document.modifiedCode === undefined) {
        await this.deleteFile(checkpoint.document.filePath);
      } else {
        await this.writeFile(
          checkpoint.document.filePath,
          checkpoint.document.modifiedCode,
        );
      }
    },
  );

  // Helper methods for testing
  setFileContent(path: string, content: string): void {
    this.fileContents.set(path, content);
  }

  setAutoFormatMapping(path: string, before: string, after: string): void {
    this.autoFormatMapping.set(`${path}-${before}`, after);
  }

  getFileContent(path: string): string | undefined {
    return this.fileContents.get(path);
  }

  /**
   * Helper method to set up a directory structure for testing
   * @param files Object mapping file paths to their content
   */
  setDirectoryStructure(files: Record<string, string>): void {
    for (const [path, content] of Object.entries(files)) {
      this.setFileContent(path, content);
    }
  }

  // Minimal implementation of updateLatestCheckpoint
  updateLatestCheckpoint = jest.fn(
    async (
      filePath: QualifiedPathName,
      newContent: string | undefined,
    ): Promise<void> => {
      if (newContent === undefined) {
        await this.deleteFile(filePath);
      } else {
        await this.writeFile(filePath, newContent);
      }
    },
  );
}
