import { Exchange } from "@augment-internal/sidecar-libs/src/chat/chat-types";
import { QualifiedPathName } from "@augment-internal/sidecar-libs/src/workspace/qualified-path-name";
import { SaveFileTool } from "../save-file-tool";
import { DiffViewDocument } from "@augment-internal/sidecar-libs/src/diff-view/document";
import { MockWorkspaceWithCheckpoints } from "./mocks/mock-workspace-with-checkpoints";
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { getClientWorkspaces } from "@augment-internal/sidecar-libs/src/client-interfaces/client-workspaces";
import * as clientWorkspaces from "@augment-internal/sidecar-libs/src/client-interfaces/client-workspaces";

// Mock dependencies
jest.mock(
  "@augment-internal/sidecar-libs/src/client-interfaces/client-workspaces",
);
jest.mock("@augment-internal/sidecar-libs/src/logging", () => ({
  getLogger: jest.fn().mockReturnValue({
    debug: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
  }),
}));
jest.mock("fs", () => ({
  promises: {
    mkdir: jest.fn().mockResolvedValue(undefined),
  },
}));
jest.mock(
  "@augment-internal/sidecar-libs/src/client-interfaces/feature-flags",
  () => ({
    getClientFeatureFlags: jest.fn().mockReturnValue({
      flags: {
        agentSaveFileToolInstructionsReminder: false,
      },
    }),
  }),
);

describe("SaveFileTool", () => {
  let tool: SaveFileTool;
  let mockWorkspace: MockWorkspaceWithCheckpoints;
  let abortSignal: AbortSignal;
  let chatHistory: Exchange[];
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  let getClientWorkspacesMock: jest.SpyInstance;

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();

    // Setup mock workspace with checkpoints
    mockWorkspace = new MockWorkspaceWithCheckpoints();
    mockWorkspace.setCurrentConversation("test-conversation-id");

    // Mock getClientWorkspaces to return our mock workspace
    getClientWorkspacesMock = jest
      .spyOn(clientWorkspaces, "getClientWorkspaces")
      .mockReturnValue(mockWorkspace);

    // Create tool instance
    tool = new SaveFileTool(mockWorkspace);

    // Setup test data
    abortSignal = new AbortController().signal;
    chatHistory = [
      {
        request_id: "test-request-id",
        request_message: "test message",
        response_text: "",
      },
    ];
  });

  it("should save a new file successfully", async () => {
    // Arrange
    const toolInput = {
      instructions_reminder:
        "LIMIT THE FILE CONTENT TO AT MOST 300 LINES. IF MORE CONTENT NEEDS TO BE ADDED USE THE str-replace-editor TOOL TO EDIT THE FILE AFTER IT HAS BEEN CREATED.",
      path: "test/file.txt",
      file_content: "Test content",
    };

    // Act
    const result = await tool.call(toolInput, chatHistory, abortSignal, "");

    // Assert
    expect(result.isError).toBe(false);
    expect(result.text).toContain("File saved");
    expect(mockWorkspace.addCheckpoint).toHaveBeenCalledWith(
      expect.objectContaining({
        conversationId: "test-conversation-id",
        path: expect.any(QualifiedPathName) as QualifiedPathName,
      }),
      expect.objectContaining({
        sourceToolCallRequestId: "test-request-id",
        timestamp: expect.any(Number) as number,
        document: expect.any(DiffViewDocument) as DiffViewDocument,
        conversationId: "test-conversation-id",
      }),
    );

    // Verify file was actually saved in the mock workspace
    const savedContent = mockWorkspace.getFileContent("test/file.txt");
    expect(savedContent).toBe("Test content\n"); // Note the newline at the end
  });

  it("should return error if file already exists", async () => {
    // Arrange
    const toolInput = {
      instructions_reminder:
        "LIMIT THE FILE CONTENT TO AT MOST 300 LINES. IF MORE CONTENT NEEDS TO BE ADDED USE THE str-replace-editor TOOL TO EDIT THE FILE AFTER IT HAS BEEN CREATED.",
      path: "test/existing-file.txt",
      file_content: "Test content",
    };

    // Set up existing file in mock workspace
    mockWorkspace.setFileContent("test/existing-file.txt", "Existing content");

    // Act
    const result = await tool.call(toolInput, chatHistory, abortSignal, "");

    // Assert
    expect(result.isError).toBe(true);
    expect(result.text).toContain("File already exists");
    expect(mockWorkspace.addCheckpoint).not.toHaveBeenCalled();

    // Verify file content wasn't changed
    const existingContent = mockWorkspace.getFileContent(
      "test/existing-file.txt",
    );
    expect(existingContent).toBe("Existing content");
  });

  it("should validate input parameters", async () => {
    // Arrange
    const toolInput = {
      instructions_reminder:
        "LIMIT THE FILE CONTENT TO AT MOST 300 LINES. IF MORE CONTENT NEEDS TO BE ADDED USE THE str-replace-editor TOOL TO EDIT THE FILE AFTER IT HAS BEEN CREATED.",
      file_content: "Test content",
      // Missing path
    };

    // Act
    const result = await tool.call(toolInput, chatHistory, abortSignal, "");

    // Assert
    expect(result.isError).toBe(true);
    // The error message contains details about the path being undefined
    expect(result.text).toBe(
      "Failed to save file: undefined: Missing required parameter `path`",
    );
  });

  it("should add newline at end of file by default", async () => {
    // Arrange
    const toolInput = {
      path: "test/file-with-newline.txt",
      file_content: "Test content",
    };

    // Act
    const result = await tool.call(toolInput, chatHistory, abortSignal, "");

    // Assert
    expect(result.isError).toBe(false);

    // Verify file was saved with newline
    const savedContent = mockWorkspace.getFileContent(
      "test/file-with-newline.txt",
    );
    expect(savedContent).toBe("Test content\n");
  });

  it("should respect add_last_line_newline=false parameter", async () => {
    // Arrange
    const toolInput = {
      path: "test/file-without-newline.txt",
      file_content: "Test content",
      add_last_line_newline: false,
    };

    // Act
    const result = await tool.call(toolInput, chatHistory, abortSignal, "");

    // Assert
    expect(result.isError).toBe(false);

    // Verify file was saved without newline
    const savedContent = mockWorkspace.getFileContent(
      "test/file-without-newline.txt",
    );
    expect(savedContent).toBe("Test content");
  });

  it("should accept undefined file_content", async () => {
    // Arrange
    const toolInput = {
      path: "test/empty-file.txt",
    };

    // Act
    const result = await tool.call(toolInput, chatHistory, abortSignal, "");

    // Assert
    expect(result.isError).toBe(false);

    // Verify file was saved without newline
    const savedContent = mockWorkspace.getFileContent("test/empty-file.txt");
    expect(savedContent).toBe("\n");
  });
});
