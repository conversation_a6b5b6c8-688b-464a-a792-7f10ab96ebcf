import { CommandTimeoutPredictor } from "../command-timeout-predictor";
import { IPluginFileStore } from "../../../client-interfaces/plugin-file-store";

// Mock storage for testing
class MockStorage implements IPluginFileStore {
    private storage = new Map<string, Uint8Array>();

    saveAsset(path: string, content: Uint8Array): Promise<void> {
        this.storage.set(path, content);
        return Promise.resolve();
    }

    async loadAsset(path: string): Promise<Uint8Array | undefined> {
        return Promise.resolve(this.storage.get(path));
    }

    deleteAsset(path: string): Promise<void> {
        this.storage.delete(path);
        return Promise.resolve();
    }

    listAssets(prefix: string): Promise<string[]> {
        return Promise.resolve(Array.from(this.storage.keys()).filter(key => key.startsWith(prefix)));
    }

    getAssetPath(path: string): Promise<string> {
        return Promise.resolve(path);
    }

    // Helper method for tests
    clear(): void {
        this.storage.clear();
    }
}

describe("CommandTimeoutPredictor", () => {
    let predictor: CommandTimeoutPredictor;
    let mockStorage: MockStorage;

    beforeEach(() => {
        mockStorage = new MockStorage();
        predictor = new CommandTimeoutPredictor(mockStorage);
    });

    describe("with null storage", () => {
        let nullStoragePredictor: CommandTimeoutPredictor;

        beforeEach(() => {
            nullStoragePredictor = new CommandTimeoutPredictor(null);
        });



        it("should handle null storage gracefully for recordExecution", async () => {
            // Should not throw an error
            await expect(
                nullStoragePredictor.recordExecution("test-command", 5.0, 30)
            ).resolves.not.toThrow();
        });

        it("should handle null storage gracefully for getOptimalTimeout", async () => {
            const timeout = await nullStoragePredictor.getOptimalTimeout("test-command", 30);
            expect(timeout).toBe(30); // Should return the requested timeout when no prediction is available
        });
    });

    describe("getOptimalTimeout", () => {
        it("should return requested timeout when no prediction available", async () => {
            const timeout = await predictor.getOptimalTimeout("unknown-command", 60);
            expect(timeout).toBe(60);
        });

        it("should return max of predicted and requested timeout", async () => {
            // Record execution that would predict 22s timeout (max(12 * 1.2, 12 + 10) = max(14.4, 22) = 22)
            await predictor.recordExecution("test-command", 5, 30);
            await predictor.recordExecution("test-command", 8, 30);
            await predictor.recordExecution("test-command", 12, 30);

            // Test with lower requested timeout
            let timeout = await predictor.getOptimalTimeout("test-command", 10);
            expect(timeout).toBe(22);

            // Test with higher requested timeout
            timeout = await predictor.getOptimalTimeout("test-command", 25);
            expect(timeout).toBe(25);
        });
    });

    describe("recordExecution", () => {
        it("should record successful execution", async () => {
            await predictor.recordExecution("test-command", 10, 30);

            // Verify it affects optimal timeout calculation
            const optimalTimeout = await predictor.getOptimalTimeout("test-command", 5);
            expect(optimalTimeout).toBeGreaterThan(5); // Should use predicted timeout
        });

        it("should record timeout execution", async () => {
            await predictor.recordExecution("test-command", null, 30);

            // Verify it affects optimal timeout calculation
            const optimalTimeout = await predictor.getOptimalTimeout("test-command", 30);
            expect(optimalTimeout).toBe(60); // Should use 2x the failed timeout
        });

        it("should handle multiple executions of same command", async () => {
            await predictor.recordExecution("test-command", 5, 30);
            await predictor.recordExecution("test-command", 10, 30);
            await predictor.recordExecution("test-command", 15, 30);

            // Verify it affects optimal timeout calculation
            const optimalTimeout = await predictor.getOptimalTimeout("test-command", 10);
            expect(optimalTimeout).toBe(25); // Should use max(15 * 1.2, 15 + 10) = max(18, 25) = 25
        });
    });

    describe("persistence", () => {
        it("should persist and load execution history", async () => {
            // Record some executions
            await predictor.recordExecution("cmd1", 5, 30);
            await predictor.recordExecution("cmd2", null, 45);

            // Create new predictor with same storage
            const newPredictor = new CommandTimeoutPredictor(mockStorage);

            // Should load previous history and use it in optimal timeout calculation
            const timeout1 = await newPredictor.getOptimalTimeout("cmd1", 5);
            const timeout2 = await newPredictor.getOptimalTimeout("cmd2", 30);

            expect(timeout1).toBeGreaterThan(5); // Should use predicted timeout
            expect(timeout2).toBe(90); // Should use 2x the failed timeout
        });

        it("should handle storage errors gracefully", async () => {
            // Create a storage that throws errors
            const errorStorage: IPluginFileStore = {
                saveAsset: jest.fn().mockRejectedValue(new Error("Storage error")),
                loadAsset: jest.fn().mockRejectedValue(new Error("Storage error")),
                deleteAsset: jest.fn().mockRejectedValue(new Error("Storage error")),
                listAssets: jest.fn().mockRejectedValue(new Error("Storage error")),
                getAssetPath: jest.fn().mockRejectedValue(new Error("Storage error")),
            };

            const errorPredictor = new CommandTimeoutPredictor(errorStorage);

            // Should not throw and return requested timeout when no prediction available
            const timeout = await errorPredictor.getOptimalTimeout("test-command", 30);
            expect(timeout).toBe(30);

            // Should not throw when recording
            await expect(errorPredictor.recordExecution("test-command", 10, 30)).resolves.not.toThrow();
        });

        it("should start loading in constructor and use the loaded data", async () => {
            // Record some data first
            await predictor.recordExecution("eager-cmd", 10, 30);

            // Create a spy to track loadAsset calls
            const loadAssetSpy = jest.spyOn(mockStorage, 'loadAsset');

            // Create new predictor - this should start loading immediately
            const newPredictor = new CommandTimeoutPredictor(mockStorage);

            // Verify loadAsset was called during construction (or very shortly after)
            // We need to wait a bit for the async operation to start
            await new Promise(resolve => setTimeout(resolve, 0));
            expect(loadAssetSpy).toHaveBeenCalledWith("commandExecutionHistory");

            // Should be able to use the loaded data
            const timeout = await newPredictor.getOptimalTimeout("eager-cmd", 5);
            expect(timeout).toBe(20); // Should use predicted timeout max(10 * 1.2, 10 + 10) = max(12, 20) = 20
        });
    });

    describe("edge cases", () => {
        it("should handle very fast execution times", async () => {
            // Record very fast execution
            await predictor.recordExecution("fast-command", 0.1, 30);

            const timeout = await predictor.getOptimalTimeout("fast-command", 0.05);

            // Should use predicted timeout max(0.1 * 1.2, 0.1 + 10) = max(0.12, 10.1) = 10.1
            expect(timeout).toBeCloseTo(10.1, 1);
        });

        it("should handle zero execution time", async () => {
            await predictor.recordExecution("instant-command", 0, 30);

            const timeout = await predictor.getOptimalTimeout("instant-command", 1);
            // Should use predicted timeout max(0 * 1.2, 0 + 10) = max(0, 10) = 10
            expect(timeout).toBe(10);
        });

        it("should handle very large execution times", async () => {
            await predictor.recordExecution("slow-command", 3600, 7200); // 1 hour

            const timeout = await predictor.getOptimalTimeout("slow-command", 1000);

            // Should use predicted timeout (3600 * 1.2 = 4320)
            expect(timeout).toBe(4320);
        });
    });
});
