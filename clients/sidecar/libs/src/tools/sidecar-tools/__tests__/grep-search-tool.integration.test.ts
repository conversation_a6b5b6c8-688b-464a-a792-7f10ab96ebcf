import { Exchange } from "@augment-internal/sidecar-libs/src/chat/chat-types";
import { GrepSearchTool } from "../grep-search-tool";
import { MockWorkspaceWithCheckpoints } from "./mocks/mock-workspace-with-checkpoints";
import * as clientWorkspaces from "@augment-internal/sidecar-libs/src/client-interfaces/client-workspaces";
import * as fs from "fs";
import * as path from "path";
import * as os from "os";

// Mock dependencies
jest.mock("@augment-internal/sidecar-libs/src/logging", () => ({
  getLogger: jest.fn().mockReturnValue({
    debug: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
  }),
}));

// Mock the feature flags module
jest.mock(
  "@augment-internal/sidecar-libs/src/client-interfaces/feature-flags",
  () => {
    const actual = jest.requireActual<
      typeof import("../../../client-interfaces/feature-flags")
    >("../../../client-interfaces/feature-flags");
    return {
      ...actual,
      getClientFeatureFlags: jest.fn().mockReturnValue({
        flags: {
          grepSearchToolEnable: true,
          grepSearchToolTimelimitSec: 10,
          grepSearchToolOutputCharsLimit: 2000,
          grepSearchToolNumContextLines: 5,
        },
      }),
    };
  },
);

const runIntegration = process.env.RUN_INTEGRATION === "true";

(runIntegration ? describe : describe.skip)(
  "GrepSearchTool Integration Tests",
  () => {
    let tool: GrepSearchTool;
    let mockWorkspace: MockWorkspaceWithCheckpoints;
    let abortSignal: AbortSignal;
    let chatHistory: Exchange[];
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    let getClientWorkspacesMock: jest.SpyInstance;
    let tempDir: string;

    beforeEach(() => {
      // Reset mocks
      jest.clearAllMocks();

      // Create a real temporary directory for the tests
      tempDir = fs.mkdtempSync(path.join(os.tmpdir(), "grep-search-test-"));

      // Setup mock workspace with checkpoints
      mockWorkspace = new MockWorkspaceWithCheckpoints();
      mockWorkspace.rootPath = tempDir;

      // Mock getClientWorkspaces to return our mock workspace
      getClientWorkspacesMock = jest
        .spyOn(clientWorkspaces, "getClientWorkspaces")
        .mockReturnValue(mockWorkspace);

      // Create tool instance
      tool = new GrepSearchTool();

      // Setup test data
      abortSignal = new AbortController().signal;
      chatHistory = [];

      // Create real test files on the filesystem
      const testFiles = {
        "test.txt": "Hello world\nThis is a test file\nWith multiple lines",
        "src/main.js":
          "function main() {\n  console.log('Hello JavaScript');\n  return 42;\n}",
        "src/utils.py":
          "def hello():\n    print('Hello Python')\n    return True",
        "docs/README.md":
          "# Project Documentation\n\nThis is the main documentation.\n\n## Features\n- Feature 1\n- Feature 2",
        ".hidden/secret.txt": "This is a hidden file\nWith secret content",
        "large-file.txt": Array(100)
          .fill("This is line number")
          .map((text, i) => `${text} ${i + 1}`)
          .join("\n"),
        "special-chars.txt":
          "Line with $pecial characters: []{}()*+?.^|\\\nQuestion mark? and asterisk* and plus+ and dot.\nBackslash\\ and caret^ and pipe| symbols\nParentheses() and brackets[] and braces{}",
        "case-test.txt": "UPPERCASE text\nlowercase text\nMiXeD cAsE text",
      };

      // Create directories and files
      for (const [filePath, content] of Object.entries(testFiles)) {
        const fullPath = path.join(tempDir, filePath);
        const dir = path.dirname(fullPath);

        // Create directory if it doesn't exist
        if (!fs.existsSync(dir)) {
          fs.mkdirSync(dir, { recursive: true });
        }

        // Write file
        fs.writeFileSync(fullPath, content);
      }
    });

    afterEach(() => {
      // Clean up temporary directory
      if (tempDir && fs.existsSync(tempDir)) {
        fs.rmSync(tempDir, { recursive: true, force: true });
      }
    });

    describe("Basic search functionality", () => {
      it("should find simple text matches", async () => {
        const toolInput = {
          directory_absolute_path: tempDir,
          query: "Hello",
        };

        const result = await tool.call(toolInput, chatHistory, abortSignal);

        expect(result.isError).toBe(false);
        // Check for begin/end messages with absolute paths
        expect(result.text).toContain(
          `=== Search results start in file: ${path.join(tempDir, "test.txt")} ===`,
        );
        expect(result.text).toContain(
          `=== Search results end in file: ${path.join(tempDir, "test.txt")} ===`,
        );
        expect(result.text).toContain(
          `=== Search results start in file: ${path.join(tempDir, "src/main.js")} ===`,
        );
        expect(result.text).toContain(
          `=== Search results start in file: ${path.join(tempDir, "src/utils.py")} ===`,
        );
        // Check for tab-separated line format
        expect(result.text).toContain("     1\tHello world");
        expect(result.text).toContain(
          "     2\t  console.log('Hello JavaScript');",
        );
        expect(result.text).toContain("     1\tdef hello():");
      });

      it('should return "No matches found" when no matches exist', async () => {
        const toolInput = {
          directory_absolute_path: tempDir,
          query: "nonexistent-text-12345",
        };

        const result = await tool.call(toolInput, chatHistory, abortSignal);

        expect(result.isError).toBe(false);
        expect(result.text).toBe("No matches found.");
      });

      it("should handle regex patterns", async () => {
        const toolInput = {
          directory_absolute_path: tempDir,
          query: "function\\s+\\w+",
        };

        const result = await tool.call(toolInput, chatHistory, abortSignal);

        expect(result.isError).toBe(false);
        expect(result.text).toContain(
          `=== Search results start in file: ${path.join(tempDir, "src/main.js")} ===`,
        );
        expect(result.text).toContain("     1\tfunction main() {");
      });
    });

    describe("Case sensitivity", () => {
      it("should be case insensitive by default", async () => {
        const toolInput = {
          directory_absolute_path: tempDir,
          query: "hello",
        };

        const result = await tool.call(toolInput, chatHistory, abortSignal);

        expect(result.isError).toBe(false);
        expect(result.text).toContain("     1\tHello world");
        expect(result.text).toContain(
          "     2\t  console.log('Hello JavaScript');",
        );
        expect(result.text).toContain("     1\tdef hello():");
      });

      it("should respect case_sensitive=true", async () => {
        const toolInput = {
          directory_absolute_path: tempDir,
          query: "hello",
          case_sensitive: true,
        };

        const result = await tool.call(toolInput, chatHistory, abortSignal);

        expect(result.isError).toBe(false);
        // Should not match "Hello" (uppercase H)
        expect(result.text).not.toContain("Hello world");
        expect(result.text).not.toContain("Hello JavaScript");
        // Should match "hello()" in Python
        expect(result.text).toContain("     1\tdef hello():");
      });

      it("should respect case_sensitive=false explicitly", async () => {
        const toolInput = {
          directory_absolute_path: tempDir,
          query: "HELLO",
          case_sensitive: false,
        };

        const result = await tool.call(toolInput, chatHistory, abortSignal);

        expect(result.isError).toBe(false);
        expect(result.text).toContain("     1\tHello world");
        expect(result.text).toContain(
          "     2\t  console.log('Hello JavaScript');",
        );
        expect(result.text).toContain("     1\tdef hello():");
      });
    });

    describe("File filtering", () => {
      it("should filter by include pattern", async () => {
        const toolInput = {
          directory_absolute_path: tempDir,
          query: "Hello",
          files_include_glob_pattern: "*.js",
        };

        const result = await tool.call(toolInput, chatHistory, abortSignal);

        expect(result.isError).toBe(false);
        expect(result.text).toContain(
          `=== Search results start in file: ${path.join(tempDir, "src/main.js")} ===`,
        );
        expect(result.text).toContain(
          "     2\t  console.log('Hello JavaScript');",
        );
        // Should not contain matches from other file types
        expect(result.text).not.toContain("test.txt");
        expect(result.text).not.toContain("src/utils.py");
      });

      it("should filter by exclude pattern", async () => {
        const toolInput = {
          directory_absolute_path: tempDir,
          query: "Hello",
          files_exclude_glob_pattern: "*.py",
        };

        const result = await tool.call(toolInput, chatHistory, abortSignal);

        expect(result.isError).toBe(false);
        expect(result.text).toContain(
          `=== Search results start in file: ${path.join(tempDir, "test.txt")} ===`,
        );
        expect(result.text).toContain(
          `=== Search results start in file: ${path.join(tempDir, "src/main.js")} ===`,
        );
        // Should not contain matches from Python files
        expect(result.text).not.toContain("src/utils.py");
      });

      it("should combine include and exclude patterns", async () => {
        const toolInput = {
          directory_absolute_path: tempDir,
          query: "Hello",
          files_include_glob_pattern: "src/*",
          files_exclude_glob_pattern: "*.py",
        };

        const result = await tool.call(toolInput, chatHistory, abortSignal);

        expect(result.isError).toBe(false);
        expect(result.text).toContain(
          `=== Search results start in file: ${path.join(tempDir, "src/main.js")} ===`,
        );
        expect(result.text).toContain(
          "     2\t  console.log('Hello JavaScript');",
        );
        // Should not contain matches from outside src/ or from Python files
        expect(result.text).not.toContain("test.txt");
        expect(result.text).not.toContain("src/utils.py");
      });
    });

    describe("Context lines", () => {
      it("should use default context lines (5 before, 5 after)", async () => {
        const toolInput = {
          directory_absolute_path: tempDir,
          query: "line number 50",
        };

        const result = await tool.call(toolInput, chatHistory, abortSignal);

        expect(result.isError).toBe(false);
        expect(result.text).toContain(
          `=== Search results start in file: ${path.join(tempDir, "large-file.txt")} ===`,
        );
        expect(result.text).toContain("    50\tThis is line number 50");
        // Should contain context lines around line 50
        expect(result.text).toContain("    45\tThis is line number 45");
        expect(result.text).toContain("    55\tThis is line number 55");
      });

      it("should respect custom context_lines_before", async () => {
        const toolInput = {
          directory_absolute_path: tempDir,
          query: "line number 50",
          context_lines_before: 2,
        };

        const result = await tool.call(toolInput, chatHistory, abortSignal);

        expect(result.isError).toBe(false);
        expect(result.text).toContain("    50\tThis is line number 50");
        // Should contain 2 lines before
        expect(result.text).toContain("    48\tThis is line number 48");
        expect(result.text).toContain("    49\tThis is line number 49");
        // Should not contain lines further back
        expect(result.text).not.toContain("    47\tThis is line number 47");
      });

      it("should respect custom context_lines_after", async () => {
        const toolInput = {
          directory_absolute_path: tempDir,
          query: "line number 50",
          context_lines_after: 1,
        };

        const result = await tool.call(toolInput, chatHistory, abortSignal);

        expect(result.isError).toBe(false);
        expect(result.text).toContain("    50\tThis is line number 50");
        // Should contain 1 line after
        expect(result.text).toContain("    51\tThis is line number 51");
        // Should not contain lines further ahead
        expect(result.text).not.toContain("    52\tThis is line number 52");
      });

      it("should handle zero context lines", async () => {
        const toolInput = {
          directory_absolute_path: tempDir,
          query: "line number 50",
          context_lines_before: 0,
          context_lines_after: 0,
        };

        const result = await tool.call(toolInput, chatHistory, abortSignal);

        expect(result.isError).toBe(false);
        expect(result.text).toContain("    50\tThis is line number 50");
        // Should not contain context lines
        expect(result.text).not.toContain("    49\tThis is line number 49");
        expect(result.text).not.toContain("    51\tThis is line number 51");
      });
    });

    describe("Hidden files and ignore logic", () => {
      it("should ignore hidden files by default", async () => {
        const toolInput = {
          directory_absolute_path: tempDir,
          query: "hidden",
        };

        const result = await tool.call(toolInput, chatHistory, abortSignal);

        expect(result.isError).toBe(false);
        // Should not find matches in hidden files
        expect(result.text).not.toContain(".hidden/secret.txt");
      });

      it("should include hidden files when disable_ignore_files=true", async () => {
        const toolInput = {
          directory_absolute_path: tempDir,
          query: "hidden",
          disable_ignore_files: true,
        };

        const result = await tool.call(toolInput, chatHistory, abortSignal);

        expect(result.isError).toBe(false);
        // Should find matches in hidden files
        expect(result.text).toContain(
          `=== Search results start in file: ${path.join(tempDir, ".hidden/secret.txt")} ===`,
        );
        expect(result.text).toContain("     1\tThis is a hidden file");
      });
    });

    describe("Special characters and escaping", () => {
      it("should handle special regex characters in search", async () => {
        const toolInput = {
          directory_absolute_path: tempDir,
          query: "\\$pecial",
        };

        const result = await tool.call(toolInput, chatHistory, abortSignal);

        expect(result.isError).toBe(false);
        expect(result.text).toContain(
          `=== Search results start in file: ${path.join(tempDir, "special-chars.txt")} ===`,
        );
        expect(result.text).toContain(
          "     1\tLine with $pecial characters: []{}()*+?.^|\\",
        );
      });

      it("should handle bracket characters", async () => {
        const toolInput = {
          directory_absolute_path: tempDir,
          query: "\\[\\]\\{\\}",
        };

        const result = await tool.call(toolInput, chatHistory, abortSignal);

        expect(result.isError).toBe(false);
        expect(result.text).toContain(
          `=== Search results start in file: ${path.join(tempDir, "special-chars.txt")} ===`,
        );
        expect(result.text).toContain(
          "     1\tLine with $pecial characters: []{}()*+?.^|\\",
        );
      });

      it("should find literal question mark when escaped", async () => {
        const toolInput = {
          directory_absolute_path: tempDir,
          query: "Question mark\\?",
        };

        const result = await tool.call(toolInput, chatHistory, abortSignal);

        expect(result.isError).toBe(false);
        expect(result.text).toContain(
          `=== Search results start in file: ${path.join(tempDir, "special-chars.txt")} ===`,
        );
        expect(result.text).toContain(
          "     2\tQuestion mark? and asterisk* and plus+ and dot.",
        );
      });

      it("should find literal asterisk when escaped", async () => {
        const toolInput = {
          directory_absolute_path: tempDir,
          query: "asterisk\\*",
        };

        const result = await tool.call(toolInput, chatHistory, abortSignal);

        expect(result.isError).toBe(false);
        expect(result.text).toContain("special-chars.txt");
        expect(result.text).toContain("asterisk*");
      });

      it("should find literal plus when escaped", async () => {
        const toolInput = {
          directory_absolute_path: tempDir,
          query: "plus\\+",
        };

        const result = await tool.call(toolInput, chatHistory, abortSignal);

        expect(result.isError).toBe(false);
        expect(result.text).toContain("special-chars.txt");
        expect(result.text).toContain("plus+");
      });

      it("should find literal dot when escaped", async () => {
        const toolInput = {
          directory_absolute_path: tempDir,
          query: "dot\\.",
        };

        const result = await tool.call(toolInput, chatHistory, abortSignal);

        expect(result.isError).toBe(false);
        expect(result.text).toContain("special-chars.txt");
        expect(result.text).toContain("dot.");
      });

      it("should find literal backslash when escaped", async () => {
        const toolInput = {
          directory_absolute_path: tempDir,
          query: "Backslash\\\\",
        };

        const result = await tool.call(toolInput, chatHistory, abortSignal);

        expect(result.isError).toBe(false);
        expect(result.text).toContain("special-chars.txt");
        expect(result.text).toContain("Backslash\\");
      });

      it("should find literal caret when escaped", async () => {
        const toolInput = {
          directory_absolute_path: tempDir,
          query: "caret\\^",
        };

        const result = await tool.call(toolInput, chatHistory, abortSignal);

        expect(result.isError).toBe(false);
        expect(result.text).toContain("special-chars.txt");
        expect(result.text).toContain("caret^");
      });

      it("should find literal pipe when escaped", async () => {
        const toolInput = {
          directory_absolute_path: tempDir,
          query: "pipe\\|",
        };

        const result = await tool.call(toolInput, chatHistory, abortSignal);

        expect(result.isError).toBe(false);
        expect(result.text).toContain("special-chars.txt");
        expect(result.text).toContain("pipe|");
      });

      it("should find literal parentheses when escaped", async () => {
        const toolInput = {
          directory_absolute_path: tempDir,
          query: "Parentheses\\(\\)",
        };

        const result = await tool.call(toolInput, chatHistory, abortSignal);

        expect(result.isError).toBe(false);
        expect(result.text).toContain("special-chars.txt");
        expect(result.text).toContain("Parentheses()");
      });

      it("should demonstrate difference between escaped and unescaped dot", async () => {
        // First test: unescaped dot (matches any character)
        const unescapedToolInput = {
          directory_absolute_path: tempDir,
          query: "dot.",
        };

        const unescapedResult = await tool.call(
          unescapedToolInput,
          chatHistory,
          abortSignal,
        );

        expect(unescapedResult.isError).toBe(false);
        // Should match both "dot." and potentially other patterns like "dots", "dota", etc.
        expect(unescapedResult.text).toContain("special-chars.txt");

        // Second test: escaped dot (matches literal dot only)
        const escapedToolInput = {
          directory_absolute_path: tempDir,
          query: "dot\\.",
        };

        const escapedResult = await tool.call(
          escapedToolInput,
          chatHistory,
          abortSignal,
        );

        expect(escapedResult.isError).toBe(false);
        expect(escapedResult.text).toContain("special-chars.txt");
        expect(escapedResult.text).toContain("dot.");
      });

      it("should handle multiple escaped characters in one query", async () => {
        const toolInput = {
          directory_absolute_path: tempDir,
          query: "\\[\\]\\{\\}\\(\\)\\*\\+\\?\\.",
        };

        const result = await tool.call(toolInput, chatHistory, abortSignal);

        expect(result.isError).toBe(false);
        expect(result.text).toContain("special-chars.txt");
        expect(result.text).toContain("[]{}()*+?.");
      });
    });

    describe("Input validation", () => {
      it("should validate required directory parameter", async () => {
        const toolInput = {
          query: "test",
          // Missing directory
        };

        const result = await tool.call(toolInput, chatHistory, abortSignal);

        expect(result.isError).toBe(true);
        expect(result.text).toContain("Input validation failed");
        expect(result.text).toContain("directory");
        expect(result.text).toContain("must be a string");
      });

      it("should validate required query parameter", async () => {
        const toolInput = {
          directory_absolute_path: tempDir,
          // Missing query
        };

        const result = await tool.call(toolInput, chatHistory, abortSignal);

        expect(result.isError).toBe(true);
        expect(result.text).toContain("Input validation failed");
        expect(result.text).toContain("query");
        expect(result.text).toContain("must be a string");
      });

      it("should validate directory parameter type", async () => {
        const toolInput = {
          directory_absolute_path: 123, // Wrong type
          query: "test",
        };

        const result = await tool.call(toolInput, chatHistory, abortSignal);

        expect(result.isError).toBe(true);
        expect(result.text).toContain("Input validation failed");
        expect(result.text).toContain("directory");
        expect(result.text).toContain("must be a string");
      });

      it("should validate directory parameter is an absolute path", async () => {
        const toolInput = {
          directory_absolute_path: "relative/path", // Relative path instead of absolute
          query: "test",
        };

        const result = await tool.call(toolInput, chatHistory, abortSignal);

        expect(result.isError).toBe(true);
        expect(result.text).toContain("Input validation failed");
        expect(result.text).toContain(
          "Parameter 'directory_absolute_path' must be an absolute path",
        );
      });

      it("should validate query parameter type", async () => {
        const toolInput = {
          directory_absolute_path: tempDir,
          query: 123, // Wrong type
        };

        const result = await tool.call(toolInput, chatHistory, abortSignal);

        expect(result.isError).toBe(true);
        expect(result.text).toContain("Input validation failed");
        expect(result.text).toContain("query");
        expect(result.text).toContain("must be a string");
      });

      it("should validate case_sensitive parameter type", async () => {
        const toolInput = {
          directory_absolute_path: tempDir,
          query: "test",
          case_sensitive: "true", // Wrong type (string instead of boolean)
        };

        const result = await tool.call(toolInput, chatHistory, abortSignal);

        expect(result.isError).toBe(true);
        expect(result.text).toContain("Input validation failed");
        expect(result.text).toContain("case_sensitive");
        expect(result.text).toContain("must be a boolean");
      });

      it("should validate disable_ignore_files parameter type", async () => {
        const toolInput = {
          directory_absolute_path: tempDir,
          query: "test",
          disable_ignore_files: "false", // Wrong type (string instead of boolean)
        };

        const result = await tool.call(toolInput, chatHistory, abortSignal);

        expect(result.isError).toBe(true);
        expect(result.text).toContain("Input validation failed");
        expect(result.text).toContain("disable_ignore_files");
        expect(result.text).toContain("must be a boolean");
      });

      it("should validate files_include_glob_pattern parameter type", async () => {
        const toolInput = {
          directory_absolute_path: tempDir,
          query: "test",
          files_include_glob_pattern: 123, // Wrong type
        };

        const result = await tool.call(toolInput, chatHistory, abortSignal);

        expect(result.isError).toBe(true);
        expect(result.text).toContain("Input validation failed");
        expect(result.text).toContain("files_include_glob_pattern");
        expect(result.text).toContain("must be a string");
      });

      it("should validate files_exclude_glob_pattern parameter type", async () => {
        const toolInput = {
          directory_absolute_path: tempDir,
          query: "test",
          files_exclude_glob_pattern: 123, // Wrong type
        };

        const result = await tool.call(toolInput, chatHistory, abortSignal);

        expect(result.isError).toBe(true);
        expect(result.text).toContain("Input validation failed");
        expect(result.text).toContain("files_exclude_glob_pattern");
        expect(result.text).toContain("must be a string");
      });

      it("should validate context_lines_before parameter type", async () => {
        const toolInput = {
          directory_absolute_path: tempDir,
          query: "test",
          context_lines_before: "5", // Wrong type (string instead of number)
        };

        const result = await tool.call(toolInput, chatHistory, abortSignal);

        expect(result.isError).toBe(true);
        expect(result.text).toContain("Input validation failed");
        expect(result.text).toContain("context_lines_before");
        expect(result.text).toContain("must be an integer");
      });

      it("should validate context_lines_after parameter type", async () => {
        const toolInput = {
          directory_absolute_path: tempDir,
          query: "test",
          context_lines_after: "5", // Wrong type (string instead of number)
        };

        const result = await tool.call(toolInput, chatHistory, abortSignal);

        expect(result.isError).toBe(true);
        expect(result.text).toContain("Input validation failed");
        expect(result.text).toContain("context_lines_after");
        expect(result.text).toContain("must be an integer");
      });

      it("should validate context_lines_before is integer", async () => {
        const toolInput = {
          directory_absolute_path: tempDir,
          query: "test",
          context_lines_before: 5.5, // Not an integer
        };

        const result = await tool.call(toolInput, chatHistory, abortSignal);

        expect(result.isError).toBe(true);
        expect(result.text).toContain("Input validation failed");
        expect(result.text).toContain("context_lines_before");
        expect(result.text).toContain("must be an integer");
      });

      it("should validate context_lines_after is integer", async () => {
        const toolInput = {
          directory_absolute_path: tempDir,
          query: "test",
          context_lines_after: 5.5, // Not an integer
        };

        const result = await tool.call(toolInput, chatHistory, abortSignal);

        expect(result.isError).toBe(true);
        expect(result.text).toContain("Input validation failed");
        expect(result.text).toContain("context_lines_after");
        expect(result.text).toContain("must be an integer");
      });

      it("should validate context_lines_before is non-negative", async () => {
        const toolInput = {
          directory_absolute_path: tempDir,
          query: "test",
          context_lines_before: -1, // Negative number
        };

        const result = await tool.call(toolInput, chatHistory, abortSignal);

        expect(result.isError).toBe(true);
        expect(result.text).toContain("Input validation failed");
        expect(result.text).toContain("context_lines_before");
        expect(result.text).toContain("must be a non-negative integer");
      });

      it("should validate context_lines_after is non-negative", async () => {
        const toolInput = {
          directory_absolute_path: tempDir,
          query: "test",
          context_lines_after: -1, // Negative number
        };

        const result = await tool.call(toolInput, chatHistory, abortSignal);

        expect(result.isError).toBe(true);
        expect(result.text).toContain("Input validation failed");
        expect(result.text).toContain("context_lines_after");
        expect(result.text).toContain("must be a non-negative integer");
      });
    });

    describe("Error handling", () => {
      it("should handle ripgrep not found error", async () => {
        // Mock getRipgrepPath to return undefined
        mockWorkspace.getRipgrepPath = jest.fn().mockResolvedValue(undefined);

        const toolInput = {
          directory_absolute_path: tempDir,
          query: "test",
        };

        const result = await tool.call(toolInput, chatHistory, abortSignal);

        expect(result.isError).toBe(true);
        expect(result.text).toContain("Ripgrep binary is not found");
        expect(result.text).toContain("grep-search tool is not available");
      });

      it("should handle invalid directory path", async () => {
        const toolInput = {
          directory_absolute_path: "/nonexistent/directory/path",
          query: "test",
        };

        const result = await tool.call(toolInput, chatHistory, abortSignal);

        // For non-existent directories, ripgrep returns an error
        expect(result.isError).toBe(true);
        expect(result.text).toContain("Failed to execute grep search");
      });

      it("should handle abort signal", async () => {
        const abortController = new AbortController();
        const toolInput = {
          directory_absolute_path: tempDir,
          query: ".*", // Match everything to make it take some time
        };

        // Start the search and abort after a short delay
        const searchPromise = tool.call(
          toolInput,
          chatHistory,
          abortController.signal,
        );

        // Abort after 1ms to ensure it gets aborted
        setTimeout(() => abortController.abort(), 1);

        const result = await searchPromise;

        expect(result.isError).toBe(false);
        // The result should either contain "aborted" message or be the partial results
        // Since the search might complete before abort, we just check it doesn't error
        expect(typeof result.text).toBe("string");
      });

      it("should handle timeout and return partial results", async () => {
        // Create a slow mock ripgrep script
        const slowRipgrepScript = `#!/bin/bash
# Slow ripgrep mock that outputs some results then sleeps
echo '{"type":"begin","data":{"path":{"text":"${path.join(tempDir, "test.txt")}"}}}'
echo '{"type":"match","data":{"path":{"text":"${path.join(tempDir, "test.txt")}"},"lines":{"text":"Hello world\\n"},"line_number":1,"absolute_offset":0,"submatches":[{"match":{"text":"Hello"},"start":0,"end":5}]}}'
echo '{"type":"context","data":{"path":{"text":"${path.join(tempDir, "test.txt")}"},"lines":{"text":"This is a test file\\n"},"line_number":2,"absolute_offset":12,"submatches":[]}}'
sleep 5  # Sleep longer than the timeout
echo '{"type":"match","data":{"path":{"text":"${path.join(tempDir, "test.txt")}"},"lines":{"text":"With multiple lines\\n"},"line_number":3,"absolute_offset":32,"submatches":[{"match":{"text":"multiple"},"start":5,"end":13}]}}'
echo '{"type":"end","data":{"path":{"text":"${path.join(tempDir, "test.txt")}"},"binary_offset":null,"stats":{"elapsed":{"secs":0,"nanos":36296,"human":"0.0000s"},"searches":1,"searches_with_match":1,"bytes_searched":50,"bytes_printed":200,"matched_lines":2,"matches":2}}}'
`;

        const slowRipgrepPath = path.join(tempDir, "slow-ripgrep.sh");
        fs.writeFileSync(slowRipgrepPath, slowRipgrepScript);
        fs.chmodSync(slowRipgrepPath, 0o755);

        // Store original getRipgrepPath mock to restore later
        // eslint-disable-next-line @typescript-eslint/unbound-method
        const originalGetRipgrepPath = mockWorkspace.getRipgrepPath;

        // Mock getRipgrepPath to return our slow script
        mockWorkspace.getRipgrepPath = jest
          .fn()
          .mockResolvedValue(slowRipgrepPath);

        // Set a short timeout (1 second)
        const featureFlagsModule = jest.requireMock<{
          getClientFeatureFlags: jest.Mock;
        }>(
          "@augment-internal/sidecar-libs/src/client-interfaces/feature-flags",
        );

        featureFlagsModule.getClientFeatureFlags.mockReturnValue({
          flags: {
            grepSearchToolEnable: true,
            grepSearchToolTimelimitSec: 1, // 1 second timeout
            grepSearchToolOutputCharsLimit: 2000,
            grepSearchToolNumContextLines: 5,
          },
        });

        const toolInput = {
          directory_absolute_path: tempDir,
          query: "Hello",
        };

        const result = await tool.call(toolInput, chatHistory, abortSignal);

        expect(result.isError).toBe(false);
        // Should contain the timeout message
        expect(result.text).toContain(
          "Search timed out after 1 seconds. Results may be incomplete.",
        );
        // Should still contain some partial results from before timeout
        expect(result.text).toContain(
          `=== Search results start in file: ${path.join(tempDir, "test.txt")} ===`,
        );
        expect(result.text).toContain("     1\tHello world");
        expect(result.text).toContain("     2\tThis is a test file");
        // Should NOT contain results that come after the sleep
        expect(result.text).not.toContain("With multiple lines");

        // Restore original getRipgrepPath mock
        mockWorkspace.getRipgrepPath = originalGetRipgrepPath;

        // Restore default flags
        featureFlagsModule.getClientFeatureFlags.mockReturnValue({
          flags: {
            grepSearchToolEnable: true,
            grepSearchToolTimelimitSec: 10,
            grepSearchToolOutputCharsLimit: 2000,
            grepSearchToolNumContextLines: 5,
          },
        });

        // Clean up the slow script
        if (fs.existsSync(slowRipgrepPath)) {
          fs.unlinkSync(slowRipgrepPath);
        }
      });
    });

    describe("Performance and edge cases", () => {
      it("should handle large result sets without truncation when limit is high", async () => {
        // Set a high output limit to avoid truncation
        const featureFlagsModule = jest.requireMock<{
          getClientFeatureFlags: jest.Mock;
        }>(
          "@augment-internal/sidecar-libs/src/client-interfaces/feature-flags",
        );

        featureFlagsModule.getClientFeatureFlags.mockReturnValue({
          flags: {
            grepSearchToolEnable: true,
            grepSearchToolTimelimitSec: 10,
            grepSearchToolOutputCharsLimit: 50000, // High limit to avoid truncation
            grepSearchToolNumContextLines: 5,
          },
        });

        const toolInput = {
          directory_absolute_path: tempDir,
          query: "line number", // Should match many lines in large-file.txt
        };

        const result = await tool.call(toolInput, chatHistory, abortSignal);

        expect(result.isError).toBe(false);
        expect(result.text).toContain(
          `=== Search results start in file: ${path.join(tempDir, "large-file.txt")} ===`,
        );
        expect(result.text).not.toContain("Output truncated");
        // Should contain many matches - count tab-separated lines instead
        const matchCount = result.text.split("\t").length - 1;
        expect(matchCount).toBeGreaterThan(10);

        // Restore default flags
        featureFlagsModule.getClientFeatureFlags.mockReturnValue({
          flags: {
            grepSearchToolEnable: true,
            grepSearchToolTimelimitSec: 10,
            grepSearchToolOutputCharsLimit: 2000,
            grepSearchToolNumContextLines: 5,
          },
        });
      });

      it("should truncate large result sets when output limit is low", async () => {
        // Set a low output limit to force truncation
        const featureFlagsModule = jest.requireMock<{
          getClientFeatureFlags: jest.Mock;
        }>(
          "@augment-internal/sidecar-libs/src/client-interfaces/feature-flags",
        );

        featureFlagsModule.getClientFeatureFlags.mockReturnValue({
          flags: {
            grepSearchToolEnable: true,
            grepSearchToolTimelimitSec: 10,
            grepSearchToolOutputCharsLimit: 500, // Low limit to force truncation
            grepSearchToolNumContextLines: 5,
          },
        });

        const toolInput = {
          directory_absolute_path: tempDir,
          query: "line number", // Should match many lines in large-file.txt
        };

        const result = await tool.call(toolInput, chatHistory, abortSignal);

        expect(result.isError).toBe(false);
        expect(result.text).toContain(
          `=== Search results start in file: ${path.join(tempDir, "large-file.txt")} ===`,
        );
        expect(result.text).toContain(
          "Output truncated at 500 characters limit",
        );
        // Should still have some matches before truncation - count tab-separated lines
        const matchCount = result.text.split("\t").length - 1;
        expect(matchCount).toBeGreaterThan(0);

        // Restore default flags
        featureFlagsModule.getClientFeatureFlags.mockReturnValue({
          flags: {
            grepSearchToolEnable: true,
            grepSearchToolTimelimitSec: 10,
            grepSearchToolOutputCharsLimit: 2000,
            grepSearchToolNumContextLines: 5,
          },
        });
      });

      it("should handle empty query gracefully", async () => {
        const toolInput = {
          directory_absolute_path: tempDir,
          query: "",
        };

        const result = await tool.call(toolInput, chatHistory, abortSignal);

        // Empty query in ripgrep matches everything (all lines)
        expect(result.isError).toBe(false);
        expect(result.text).toContain(
          `=== Search results start in file: ${path.join(tempDir, "test.txt")} ===`,
        );
        expect(result.text).toContain(
          `=== Search results start in file: ${path.join(tempDir, "large-file.txt")} ===`,
        );
        // Should contain many matches since empty query matches all lines
        expect(result.text.split("\n").length).toBeGreaterThan(10);
      });

      it("should handle complex regex patterns", async () => {
        const toolInput = {
          directory_absolute_path: tempDir,
          query: "^\\s*function\\s+\\w+\\s*\\(", // Match function declarations
        };

        const result = await tool.call(toolInput, chatHistory, abortSignal);

        expect(result.isError).toBe(false);
        expect(result.text).toContain(
          `=== Search results start in file: ${path.join(tempDir, "src/main.js")} ===`,
        );
        expect(result.text).toContain("     1\tfunction main() {");
      });
    });
  },
);
