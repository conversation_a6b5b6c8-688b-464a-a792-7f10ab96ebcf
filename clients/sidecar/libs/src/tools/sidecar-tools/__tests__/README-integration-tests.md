# Integration Tests for Sidecar Tools

This directory contains integration tests that test tools with real external dependencies.

## GrepSearchTool Integration Tests

The `grep-search-tool.integration.test.ts` file contains comprehensive integration tests for the `GrepSearchTool` that use the real `ripgrep` binary.

### Prerequisites

1. **ripgrep must be installed** on the host system:
   ```bash
   # Ubuntu/Debian
   sudo apt install ripgrep

   # macOS
   brew install ripgrep

   # Or download from: https://github.com/BurntSushi/ripgrep/releases
   ```

2. The `ripgrep` binary should be available in the system PATH as `rg`.

### Running Integration Tests

Integration tests are **skipped by default** to avoid dependencies on external tools during regular test runs.

To run integration tests, set the `RUN_INTEGRATION` environment variable:

```bash
# Run only the grep-search-tool integration tests
RUN_INTEGRATION=true node clients/sidecar/libs/node_modules/jest/bin/jest --config clients/sidecar/libs/jest.config.js clients/sidecar/libs/src/tools/sidecar-tools/__tests__/grep-search-tool.integration.test.ts

# Run all integration tests (if more are added)
RUN_INTEGRATION=true node clients/sidecar/libs/node_modules/jest/bin/jest --config clients/sidecar/libs/jest.config.js --testNamePattern="integration"
```

### Test Coverage

The integration tests cover:

#### Basic Functionality
- Simple text searches
- Regex pattern matching
- "No matches found" scenarios

#### Case Sensitivity
- Default case-insensitive behavior
- Explicit case-sensitive searches
- Explicit case-insensitive searches

#### File Filtering
- Include patterns (`files_include_pattern`)
- Exclude patterns (`files_exclude_pattern`)
- Combined include/exclude patterns

#### Context Lines
- Default context (5 lines before/after)
- Custom `context_lines_before`
- Custom `context_lines_after`
- Zero context lines

#### Hidden Files and Ignore Logic
- Default behavior (ignores hidden files)
- `no_ignore=true` (includes hidden files)

#### Special Characters and Escaping
- Special regex characters (`$`, `[]`, `{}`, etc.)
- Proper escaping in search patterns

#### Input Validation
- Required parameter validation (`directory`, `query`)
- Type validation for all parameters
- Range validation (non-negative integers)
- Comprehensive error messages

#### Error Handling
- Ripgrep binary not found
- Invalid directory paths
- Abort signal handling

#### Performance and Edge Cases
- Large result sets
- Empty queries
- Complex regex patterns

### Test Structure

The tests use the `MockWorkspaceWithCheckpoints` class to:
- Simulate a file system with test files
- Mock the `getClientWorkspaces()` function
- Provide a controlled test environment

Test files are created in memory with various content types:
- Simple text files
- Source code files (JavaScript, Python)
- Documentation files (Markdown)
- Hidden files
- Large files (100+ lines)
- Files with special characters

### Adding New Integration Tests

When adding new integration tests:

1. Use the same pattern with `RUN_INTEGRATION` environment variable:
   ```typescript
   const runIntegration = process.env.RUN_INTEGRATION === 'true';

   (runIntegration ? describe : describe.skip)('My Integration Tests', () => {
     // tests here
   });
   ```

2. Document any external dependencies in this README
3. Ensure tests are comprehensive and cover edge cases
4. Use the `MockWorkspaceWithCheckpoints` for consistent test setup
