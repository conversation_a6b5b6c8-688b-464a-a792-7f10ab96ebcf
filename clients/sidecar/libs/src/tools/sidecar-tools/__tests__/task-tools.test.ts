/**
 * @file task-tools.test.ts
 * Tests for the task management tools.
 */

import { Exchange } from "../../../chat/chat-types";
import { TaskState } from "../../../agent/task/task-types";
import { createTaskTestEnvironment } from "../../../agent/task/__tests__/task-test-kit";
import {
  ViewTaskListTool,
  UpdateTasksTool,
  AddTasksTool,
  ReorganizeTaskListTool,
} from "../task-tools";

// Mock crypto.randomUUID to generate unique UUIDs for testing
let uuidCounter = 0;
const mockRandomUUID = jest.fn(() => `test-uuid-${++uuidCounter}`);
Object.defineProperty(global, "crypto", {
  value: {
    randomUUID: mockRandomUUID,
  },
  writable: true,
});

describe("Task Tools", () => {
  let testEnv: ReturnType<typeof createTaskTestEnvironment>;
  let mockChatHistory: Exchange[];
  let mockAbortSignal: AbortSignal;

  beforeEach(() => {
    // Reset UUID counter for predictable test UUIDs
    uuidCounter = 0;
    mockRandomUUID.mockClear();

    testEnv = createTaskTestEnvironment();
    mockChatHistory = [];
    mockAbortSignal = new AbortController().signal;
  });

  afterEach(() => {
    testEnv.cleanup();
  });

  describe("ViewTaskListTool", () => {
    it("should return error when no root task is set", async () => {
      const tool = new ViewTaskListTool(testEnv.manager);
      const result = await tool.call({}, mockChatHistory, mockAbortSignal);

      expect(result.isError).toBe(true);
      expect(result.text).toContain("No root task found");
    });

    it("should return task list markdown when root task exists", async () => {
      await testEnv.manager.initialize();

      // Create a root task
      const rootTaskId = await testEnv.manager.createTask(
        "Root Task",
        "Root description",
      );
      testEnv.manager.setCurrentRootTaskUuid(rootTaskId);

      const tool = new ViewTaskListTool(testEnv.manager);
      const result = await tool.call({}, mockChatHistory, mockAbortSignal);

      expect(result.isError).toBe(false);
      expect(result.text).toContain("# Current Task List");
      expect(result.text).toContain("Root Task");
      expect(result.text).toContain("Root description");
    });
  });

  describe("UpdateTasksTool", () => {
    let rootTaskId: string;

    beforeEach(async () => {
      await testEnv.manager.initialize();
      rootTaskId = await testEnv.manager.createTask(
        "Root Task",
        "Root description",
      );
      testEnv.manager.setCurrentRootTaskUuid(rootTaskId);
    });

    it("should return error when tasks array is missing", async () => {
      const tool = new UpdateTasksTool(testEnv.manager);
      const result = await tool.call({}, mockChatHistory, mockAbortSignal);

      expect(result.isError).toBe(true);
      expect(result.text).toContain(
        "tasks array is required and must not be empty",
      );
    });

    it("should return error when tasks array is empty", async () => {
      const tool = new UpdateTasksTool(testEnv.manager);
      const result = await tool.call(
        { tasks: [] },
        mockChatHistory,
        mockAbortSignal,
      );

      expect(result.isError).toBe(true);
      expect(result.text).toContain("tasks array is required and must not be empty");
    });

    it("should update task state successfully", async () => {
      const tool = new UpdateTasksTool(testEnv.manager);
      const result = await tool.call(
        { tasks: [{ task_id: rootTaskId, state: "IN_PROGRESS" }] },
        mockChatHistory,
        mockAbortSignal,
      );

      expect(result.isError).toBe(false);
      expect(result.text).toContain("Task list updated successfully");
      expect(result.text).toContain("Updated: 1");
      expect(result.text).toContain("## Updated Tasks");
      expect(result.text).toContain("[/]"); // IN_PROGRESS state marker

      // Verify the task was actually updated
      const updatedTask = await testEnv.manager.getTask(rootTaskId);
      expect(updatedTask?.state).toBe(TaskState.IN_PROGRESS);
    });

    it("should update task name and description", async () => {
      const tool = new UpdateTasksTool(testEnv.manager);
      const result = await tool.call(
        {
          tasks: [{
            task_id: rootTaskId,
            name: "Updated Name",
            description: "Updated Description",
          }],
        },
        mockChatHistory,
        mockAbortSignal,
      );

      expect(result.isError).toBe(false);
      expect(result.text).toContain("Task list updated successfully");
      expect(result.text).toContain("Updated: 1");
      expect(result.text).toContain("## Updated Tasks");
      expect(result.text).toContain("NAME:Updated Name");
      expect(result.text).toContain("DESCRIPTION:Updated Description");

      // Verify the task was actually updated
      const updatedTask = await testEnv.manager.getTask(rootTaskId);
      expect(updatedTask?.name).toBe("Updated Name");
      expect(updatedTask?.description).toBe("Updated Description");
    });

    it("should return error for invalid state", async () => {
      const tool = new UpdateTasksTool(testEnv.manager);
      const result = await tool.call(
        { tasks: [{ task_id: rootTaskId, state: "INVALID_STATE" }] },
        mockChatHistory,
        mockAbortSignal,
      );

      expect(result.isError).toBe(false); // Batch operations continue on failures
      expect(result.text).toContain("Task list updated successfully");
      expect(result.text).toContain("Updated: 0"); // No tasks were updated due to validation error
    });

    it("should return error when no updates provided", async () => {
      const tool = new UpdateTasksTool(testEnv.manager);
      const result = await tool.call(
        { tasks: [{ task_id: rootTaskId }] },
        mockChatHistory,
        mockAbortSignal,
      );

      expect(result.isError).toBe(false); // Batch operations continue on failures
      expect(result.text).toContain("Task list updated successfully");
      expect(result.text).toContain("Updated: 0"); // No tasks were updated due to validation error
    });

    it("should update multiple tasks in batch", async () => {
      // Create additional tasks for batch testing
      const task1Id = await testEnv.manager.createTask(
        "Task 1",
        "First task",
        rootTaskId,
      );
      const task2Id = await testEnv.manager.createTask(
        "Task 2",
        "Second task",
        rootTaskId,
      );

      const tool = new UpdateTasksTool(testEnv.manager);
      const result = await tool.call(
        {
          tasks: [
            { task_id: task1Id, state: "IN_PROGRESS" },
            { task_id: task2Id, state: "COMPLETE", name: "Updated Task 2" },
          ],
        },
        mockChatHistory,
        mockAbortSignal,
      );

      expect(result.isError).toBe(false);
      expect(result.text).toContain("Task list updated successfully");
      expect(result.text).toContain("Updated: 2");
      expect(result.text).toContain("## Updated Tasks");

      // Verify the tasks were actually updated
      const updatedTask1 = await testEnv.manager.getTask(task1Id);
      const updatedTask2 = await testEnv.manager.getTask(task2Id);
      expect(updatedTask1?.state).toBe(TaskState.IN_PROGRESS);
      expect(updatedTask2?.state).toBe(TaskState.COMPLETE);
      expect(updatedTask2?.name).toBe("Updated Task 2");
    });

    it("should handle batch update with some failures", async () => {
      const validTaskId = await testEnv.manager.createTask(
        "Valid Task",
        "Valid task",
        rootTaskId,
      );

      const tool = new UpdateTasksTool(testEnv.manager);
      const result = await tool.call(
        {
          tasks: [
            { task_id: validTaskId, state: "IN_PROGRESS" },
            { task_id: "non-existent-id", state: "COMPLETE" },
            { task_id: validTaskId, state: "INVALID_STATE" },
          ],
        },
        mockChatHistory,
        mockAbortSignal,
      );

      expect(result.isError).toBe(false);
      expect(result.text).toContain("Task list updated successfully");
      expect(result.text).toContain("Updated: 1");
      expect(result.text).toContain("## Updated Tasks");
      // Only the first valid update should succeed, the others should fail silently
      // since we continue processing and only show successful updates in the diff
    });

    it("should return error when tasks array is not provided", async () => {
      const tool = new UpdateTasksTool(testEnv.manager);
      const result = await tool.call(
        { state: "IN_PROGRESS" },
        mockChatHistory,
        mockAbortSignal,
      );

      expect(result.isError).toBe(true);
      expect(result.text).toContain(
        "tasks array is required and must not be empty",
      );
    });
  });

  describe("AddTasksTool", () => {
    let rootTaskId: string;

    beforeEach(async () => {
      await testEnv.manager.initialize();
      rootTaskId = await testEnv.manager.createTask(
        "Root Task",
        "Root description",
      );
      testEnv.manager.setCurrentRootTaskUuid(rootTaskId);
    });

    it("should return error when tasks array is missing", async () => {
      const tool = new AddTasksTool(testEnv.manager);
      const result = await tool.call(
        { description: "Test description" },
        mockChatHistory,
        mockAbortSignal,
      );

      expect(result.isError).toBe(true);
      expect(result.text).toContain(
        "tasks array is required and must not be empty",
      );
    });

    it("should return error when tasks array is empty", async () => {
      const tool = new AddTasksTool(testEnv.manager);
      const result = await tool.call(
        { tasks: [] },
        mockChatHistory,
        mockAbortSignal,
      );

      expect(result.isError).toBe(true);
      expect(result.text).toContain(
        "tasks array is required and must not be empty",
      );
    });

    it("should create a new root-level task", async () => {
      const tool = new AddTasksTool(testEnv.manager);
      const result = await tool.call(
        {
          tasks: [{
            name: "New Task",
            description: "New task description",
          }],
        },
        mockChatHistory,
        mockAbortSignal,
      );

      expect(result.isError).toBe(false);
      expect(result.text).toContain("Task list updated successfully");
      expect(result.text).toContain("Created: 1");
      expect(result.text).toContain("## Created Tasks");
      expect(result.text).toContain("NAME:New Task");
    });

    it("should create a subtask when parent_task_id is provided", async () => {
      const tool = new AddTasksTool(testEnv.manager);

      const result = await tool.call(
        {
          tasks: [{
            name: "Subtask",
            description: "Subtask description",
            parent_task_id: rootTaskId,
          }],
        },
        mockChatHistory,
        mockAbortSignal,
      );

      expect(result.isError).toBe(false);
      expect(result.text).toContain("Task list updated successfully");
      expect(result.text).toContain("Created: 1");
      expect(result.text).toContain("## Created Tasks");
      expect(result.text).toContain("NAME:Subtask");

      // Verify the subtask was added to the parent
      const parentTask = await testEnv.manager.getTask(rootTaskId);
      expect(parentTask?.subTasks).toHaveLength(1);
    });

    it("should return error when parent task does not exist", async () => {
      const tool = new AddTasksTool(testEnv.manager);
      const result = await tool.call(
        {
          tasks: [{
            name: "Subtask",
            description: "Subtask description",
            parent_task_id: "non-existent-id",
          }],
        },
        mockChatHistory,
        mockAbortSignal,
      );

      expect(result.isError).toBe(false); // Batch operations continue on failures
      expect(result.text).toContain("Task list updated successfully");
      expect(result.text).toContain("Created: 0"); // No tasks were created due to error
    });

    it("should create task with specified state", async () => {
      const tool = new AddTasksTool(testEnv.manager);
      const result = await tool.call(
        {
          tasks: [{
            name: "In Progress Task",
            description: "Task description",
            state: "IN_PROGRESS",
          }],
        },
        mockChatHistory,
        mockAbortSignal,
      );

      expect(result.isError).toBe(false);
      expect(result.text).toContain("Task list updated successfully");
      expect(result.text).toContain("Created: 1");
      expect(result.text).toContain("## Created Tasks");
      expect(result.text).toContain("[/]"); // IN_PROGRESS state marker

      // Extract task ID from response and verify state
      const taskIdMatch = result.text.match(/UUID:(test-uuid-\d+)/);
      expect(taskIdMatch).toBeTruthy();

      const newTaskId = taskIdMatch?.[1];
      expect(newTaskId).toBeTruthy();
      const newTask = await testEnv.manager.getTask(newTaskId!);
      expect(newTask?.state).toBe(TaskState.IN_PROGRESS);
    });
  });

  describe("ReorganizeTaskListTool", () => {
    let rootTaskId: string;

    beforeEach(async () => {
      await testEnv.manager.initialize();
      rootTaskId = await testEnv.manager.createTask(
        "Root Task",
        "Root description",
      );
      testEnv.manager.setCurrentRootTaskUuid(rootTaskId);
    });

    it("should return error when markdown is missing", async () => {
      const tool = new ReorganizeTaskListTool(testEnv.manager);
      const result = await tool.call({}, mockChatHistory, mockAbortSignal);

      expect(result.isError).toBe(true);
      expect(result.text).toContain("No markdown provided");
    });

    it("should return error when no root task is set", async () => {
      testEnv.manager.setCurrentRootTaskUuid("");

      const tool = new ReorganizeTaskListTool(testEnv.manager);
      const result = await tool.call(
        { markdown: "[ ] UUID:test NAME:Test DESCRIPTION:Test" },
        mockChatHistory,
        mockAbortSignal,
      );

      expect(result.isError).toBe(true);
      expect(result.text).toContain("No root task found");
    });

    it("should return error for invalid markdown format", async () => {
      const tool = new ReorganizeTaskListTool(testEnv.manager);
      const result = await tool.call(
        { markdown: "Invalid markdown format" },
        mockChatHistory,
        mockAbortSignal,
      );

      expect(result.isError).toBe(true);
      expect(result.text).toContain("Failed to parse markdown");
    });

    it("should update task list successfully with valid markdown", async () => {
      const validMarkdown = `[/] UUID:${rootTaskId} NAME:Updated Root DESCRIPTION:Updated root description
-[ ] UUID:NEW_UUID NAME:New Subtask DESCRIPTION:New subtask description`;

      const tool = new ReorganizeTaskListTool(testEnv.manager);
      const result = await tool.call(
        { markdown: validMarkdown },
        mockChatHistory,
        mockAbortSignal,
      );

      expect(result.isError).toBe(false);
      expect(result.text).toContain("Task list updated successfully");
      expect(result.text).toContain("Created: 1");
      expect(result.text).toContain("Updated: 1");

      // Verify the root task was updated
      const updatedRootTask = await testEnv.manager.getTask(rootTaskId);
      expect(updatedRootTask?.name).toBe("Updated Root");
      expect(updatedRootTask?.state).toBe(TaskState.IN_PROGRESS);
      expect(updatedRootTask?.subTasks).toHaveLength(1);
    });

    it("should return error for multiple root tasks", async () => {
      const invalidMarkdown = `[/] UUID:${rootTaskId} NAME:Root Task 1 DESCRIPTION:First root task
[ ] UUID:NEW_UUID NAME:Root Task 2 DESCRIPTION:Second root task
-[ ] UUID:NEW_UUID NAME:Subtask DESCRIPTION:A subtask`;

      const tool = new ReorganizeTaskListTool(testEnv.manager);
      const result = await tool.call(
        { markdown: invalidMarkdown },
        mockChatHistory,
        mockAbortSignal,
      );

      expect(result.isError).toBe(true);
      expect(result.text).toContain("Multiple root tasks found");
      expect(result.text).toContain(
        "There can only be one root task per conversation",
      );
    });

    it("should return error for no root tasks", async () => {
      const invalidMarkdown = `-[ ] UUID:NEW_UUID NAME:Subtask 1 DESCRIPTION:First subtask
-[ ] UUID:NEW_UUID NAME:Subtask 2 DESCRIPTION:Second subtask`;

      const tool = new ReorganizeTaskListTool(testEnv.manager);
      const result = await tool.call(
        { markdown: invalidMarkdown },
        mockChatHistory,
        mockAbortSignal,
      );

      expect(result.isError).toBe(true);
      expect(result.text).toContain("No root task found");
    });
  });

  describe("UUID Conversion", () => {
    let rootTaskId: string;

    beforeEach(async () => {
      await testEnv.manager.initialize();
      rootTaskId = await testEnv.manager.createTask(
        "Root Task",
        "Root description",
      );
      testEnv.manager.setCurrentRootTaskUuid(rootTaskId);
    });

    it("should handle UUID conversion in UpdateTasksTool (integration test)", async () => {
      // Test that the tool works with actual UUIDs (the conversion happens in validation)
      const tool = new UpdateTasksTool(testEnv.manager);
      const result = await tool.call(
        { tasks: [{ task_id: rootTaskId, state: "IN_PROGRESS" }] },
        mockChatHistory,
        mockAbortSignal,
      );

      expect(result.isError).toBe(false);

      // Verify the task was actually updated
      const updatedTask = await testEnv.manager.getTask(rootTaskId);
      expect(updatedTask?.state).toBe(TaskState.IN_PROGRESS);
    });

    it("should handle UUID conversion in batch updates (integration test)", async () => {
      // Create a second task
      const task2Id = await testEnv.manager.createTask(
        "Task 2",
        "Task 2 description",
        rootTaskId,
      );

      const tool = new UpdateTasksTool(testEnv.manager);
      const result = await tool.call(
        {
          tasks: [
            { task_id: rootTaskId, state: "IN_PROGRESS" },
            { task_id: task2Id, state: "COMPLETE" },
          ],
        },
        mockChatHistory,
        mockAbortSignal,
      );

      expect(result.isError).toBe(false);

      // Verify both tasks were updated
      const updatedRoot = await testEnv.manager.getTask(rootTaskId);
      const updatedTask2 = await testEnv.manager.getTask(task2Id);
      expect(updatedRoot?.state).toBe(TaskState.IN_PROGRESS);
      expect(updatedTask2?.state).toBe(TaskState.COMPLETE);
    });

    it("should handle UUID conversion in AddTasksTool for parent_task_id (integration test)", async () => {
      const tool = new AddTasksTool(testEnv.manager);
      const result = await tool.call(
        {
          tasks: [{
            name: "New Subtask",
            description: "Subtask description",
            parent_task_id: rootTaskId,
          }],
        },
        mockChatHistory,
        mockAbortSignal,
      );

      expect(result.isError).toBe(false);

      // Verify the subtask was created under the correct parent
      const updatedRoot = await testEnv.manager.getHydratedTask(rootTaskId);
      expect(updatedRoot?.subTasks).toHaveLength(1);
    });
  });
});
