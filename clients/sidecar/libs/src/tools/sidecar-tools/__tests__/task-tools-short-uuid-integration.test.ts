/**
 * @file task-tools-short-uuid-integration.test.ts
 * Integration tests to verify that task tools use short UUIDs in markdown output.
 */

import { Exchange } from "../../../chat/chat-types";
import { TaskState } from "../../../agent/task/task-types";
import { createTaskTestEnvironment } from "../../../agent/task/__tests__/task-test-kit";
import {
  ViewTaskListTool,
  UpdateTasksTool,
  AddTasksTool,
} from "../task-tools";

// Mock crypto.randomUUID to generate predictable UUIDs for testing
let uuidCounter = 0;
const mockRandomUUID = jest.fn(() => `550e8400-e29b-41d4-a716-44665544000${uuidCounter++}`);
Object.defineProperty(global, "crypto", {
  value: {
    randomUUID: mockRandomUUID,
  },
  writable: true,
});

describe("Task Tools Short UUID Integration", () => {
  let testEnv: ReturnType<typeof createTaskTestEnvironment>;
  let rootTaskId: string;
  const mockChatHistory: Exchange[] = [];
  const mockAbortSignal = new AbortController().signal;

  beforeEach(async () => {
    uuidCounter = 0;
    mockRandomUUID.mockClear();
    testEnv = createTaskTestEnvironment();
    
    // Create a root task with a predictable UUID
    rootTaskId = await testEnv.manager.createTask(
      "Root Task",
      "This is the root task",
    );
    testEnv.manager.setCurrentRootTaskUuid(rootTaskId);
  });

  afterEach(async () => {
    await testEnv.cleanup();
  });

  describe("ViewTaskListTool", () => {
    it("should return markdown with short UUIDs by default", async () => {
      const tool = new ViewTaskListTool(testEnv.manager);
      const result = await tool.call({}, mockChatHistory, mockAbortSignal);

      expect(result.isError).toBe(false);
      expect(result.text).toContain("# Current Task List");
      expect(result.text).toContain("UUID:");
      
      // Should not contain the full UUID
      expect(result.text).not.toContain("550e8400-e29b-41d4-a716-************");
      
      // Should contain a short UUID (much shorter than 36 characters)
      const uuidMatch = result.text.match(/UUID:([^\s]+)/);
      expect(uuidMatch).toBeTruthy();
      expect(uuidMatch![1].length).toBeLessThan(36);
    });
  });

  describe("AddTasksTool", () => {
    it("should return markdown with short UUIDs in diff output", async () => {
      const tool = new AddTasksTool(testEnv.manager);
      const result = await tool.call(
        {
          tasks: [{
            name: "New Task",
            description: "New task description",
          }],
        },
        mockChatHistory,
        mockAbortSignal,
      );

      expect(result.isError).toBe(false);
      expect(result.text).toContain("Task list updated successfully");
      expect(result.text).toContain("Created: 1");
      expect(result.text).toContain("## Created Tasks");
      expect(result.text).toContain("UUID:");
      
      // Should not contain the full UUID
      expect(result.text).not.toContain("550e8400-e29b-41d4-a716-************");
      
      // Should contain short UUIDs
      const uuidMatches = result.text.match(/UUID:([^\s]+)/g);
      expect(uuidMatches).toBeTruthy();
      expect(uuidMatches!.length).toBeGreaterThan(0);
      
      // All UUIDs should be short
      for (const match of uuidMatches!) {
        const uuid = match.replace("UUID:", "");
        expect(uuid.length).toBeLessThan(36);
      }
    });
  });

  describe("UpdateTasksTool", () => {
    it("should return markdown with short UUIDs in diff output", async () => {
      const tool = new UpdateTasksTool(testEnv.manager);
      const result = await tool.call(
        {
          tasks: [{
            task_id: rootTaskId,
            state: "IN_PROGRESS",
          }],
        },
        mockChatHistory,
        mockAbortSignal,
      );

      expect(result.isError).toBe(false);
      expect(result.text).toContain("Task list updated successfully");
      expect(result.text).toContain("Updated: 1");
      expect(result.text).toContain("## Updated Tasks");
      expect(result.text).toContain("UUID:");
      
      // Should not contain the full UUID
      expect(result.text).not.toContain("550e8400-e29b-41d4-a716-************");
      
      // Should contain short UUIDs
      const uuidMatches = result.text.match(/UUID:([^\s]+)/g);
      expect(uuidMatches).toBeTruthy();
      expect(uuidMatches!.length).toBeGreaterThan(0);
      
      // All UUIDs should be short
      for (const match of uuidMatches!) {
        const uuid = match.replace("UUID:", "");
        expect(uuid.length).toBeLessThan(36);
      }
    });

    it("should accept short UUIDs as input and convert them properly", async () => {
      // First, get the short UUID representation of the root task
      const viewTool = new ViewTaskListTool(testEnv.manager);
      const viewResult = await viewTool.call({}, mockChatHistory, mockAbortSignal);
      
      const uuidMatch = viewResult.text.match(/UUID:([^\s]+)/);
      expect(uuidMatch).toBeTruthy();
      const shortUuid = uuidMatch![1];
      
      // Now use the short UUID to update the task
      const updateTool = new UpdateTasksTool(testEnv.manager);
      const result = await updateTool.call(
        {
          tasks: [{
            task_id: shortUuid, // Using short UUID as input
            state: "COMPLETE",
          }],
        },
        mockChatHistory,
        mockAbortSignal,
      );

      expect(result.isError).toBe(false);
      expect(result.text).toContain("Task list updated successfully");
      expect(result.text).toContain("Updated: 1");
      
      // Verify the task was actually updated
      const updatedTask = await testEnv.manager.getTask(rootTaskId);
      expect(updatedTask?.state).toBe(TaskState.COMPLETE);
    });
  });

  describe("Round-trip consistency", () => {
    it("should maintain UUID consistency when using short UUIDs", async () => {
      // Create a task
      const addTool = new AddTasksTool(testEnv.manager);
      const addResult = await addTool.call(
        {
          tasks: [{
            name: "Test Task",
            description: "Test description",
          }],
        },
        mockChatHistory,
        mockAbortSignal,
      );

      expect(addResult.isError).toBe(false);
      
      // Extract the short UUID from the creation response
      const createdUuidMatch = addResult.text.match(/## Created Tasks[\s\S]*?UUID:([^\s]+)/);
      expect(createdUuidMatch).toBeTruthy();
      const shortUuid = createdUuidMatch![1];
      
      // Use the short UUID to update the task
      const updateTool = new UpdateTasksTool(testEnv.manager);
      const updateResult = await updateTool.call(
        {
          tasks: [{
            task_id: shortUuid,
            state: "IN_PROGRESS",
          }],
        },
        mockChatHistory,
        mockAbortSignal,
      );

      expect(updateResult.isError).toBe(false);
      expect(updateResult.text).toContain("Updated: 1");
      
      // Verify the same short UUID appears in the update response
      const updatedUuidMatch = updateResult.text.match(/## Updated Tasks[\s\S]*?UUID:([^\s]+)/);
      expect(updatedUuidMatch).toBeTruthy();
      expect(updatedUuidMatch![1]).toBe(shortUuid);
    });
  });
});
