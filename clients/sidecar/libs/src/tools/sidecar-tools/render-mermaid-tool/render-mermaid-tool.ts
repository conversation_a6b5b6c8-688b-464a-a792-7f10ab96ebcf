import { Exchange } from "../../../chat/chat-types";
import { getLogger, type AugmentLogger } from "../../../logging";
import { ToolBase, ToolSafety, ToolUseResponse } from "../../tool-types";
import { SidecarToolType } from "../sidecar-tool-types";
import { structuredNodeResponse } from "../tool-use-response";
import {
  ToolResponseContentNode,
  ToolResponseContentNodeType,
} from "../../tool-types";
import { RenderMermaidToolDefinition } from "./tool-definition";

export class RenderMermaidTool extends ToolBase<SidecarToolType> {
  private readonly _logger: AugmentLogger;
  private readonly _toolDefinition: RenderMermaidToolDefinition;

  constructor() {
    super(SidecarToolType.renderMermaid, ToolSafety.Safe);
    this._toolDefinition = new RenderMermaidToolDefinition();
    this.description = this._toolDefinition.description;
    this.inputSchemaJson = this._toolDefinition.inputSchemaJson;

    this._logger = getLogger("RenderMermaidTool");
  }

  public readonly description: string;
  public readonly inputSchemaJson: string;

  public checkToolCallSafe(_toolInput: Record<string, unknown>): boolean {
    return true;
  }

  public async call(
    toolInput: Record<string, unknown>,
    _chatHistory: Exchange[],
    _abortSignal: AbortSignal,
  ): Promise<ToolUseResponse> {
    try {
      this._logger.debug(
        `Tool called with diagram_definition: ${String(toolInput.diagram_definition)} and title: ${String(toolInput.title)}`,
      );

      // Validate input
      if (typeof toolInput.diagram_definition !== "string") {
        throw new Error("diagram_definition must be a string");
      }

      const diagramDefinition = toolInput.diagram_definition.trim();
      if (!diagramDefinition) {
        throw new Error("diagram_definition cannot be empty");
      }

      const title =
        typeof toolInput.title === "string"
          ? toolInput.title
          : "Mermaid Diagram";

      // Create content nodes with the Mermaid source
      const contentNodes: ToolResponseContentNode[] = [
        {
          type: ToolResponseContentNodeType.ContentText,
          text_content: JSON.stringify({
            type: "mermaid_diagram",
            diagram_definition: diagramDefinition,
            title: title,
          }),
        },
      ];

      // Add a small delay to satisfy the async requirement
      await new Promise((resolve) => setTimeout(resolve, 0));

      // Return structured response
      return structuredNodeResponse(contentNodes, false);
    } catch (error: unknown) {
      this._logger.error(
        `Error in tool call: ${error instanceof Error ? error.message : String(error)}`,
      );
      if (error instanceof Error) {
        return structuredNodeResponse(
          [
            {
              type: ToolResponseContentNodeType.ContentText,
              text_content: `Error: ${error.message}`,
            },
          ],
          true,
        );
      } else {
        return structuredNodeResponse(
          [
            {
              type: ToolResponseContentNodeType.ContentText,
              text_content: `Unknown error: ${String(error)}`,
            },
          ],
          true,
        );
      }
    }
  }
}
