export class RenderMermaidToolDefinition {
  public readonly description: string =
    "Render a Mermaid diagram from the provided definition. This tool takes Mermaid diagram code and renders it as an interactive diagram with pan/zoom controls and copy functionality.";

  public readonly inputSchemaJson: string = JSON.stringify({
    type: "object",
    properties: {
      diagram_definition: {
        type: "string",
        description: "The Mermaid diagram definition code to render",
      },
      title: {
        type: "string",
        description: "Optional title for the diagram",
        default: "Mermaid Diagram",
      },
    },
    required: ["diagram_definition"],
  });
}
