import {
  ToolUseResponse,
  ToolResponseContentNode,
  ToolResponseContentNodeType,
} from "../tool-types";

/** Helper function to make a success text response. */
export function successToolResponse(
  message: string,
  requestId?: string,
): ToolUseResponse {
  return {
    text: message,
    isError: false,
    requestId: requestId,
  };
}

/** Helper function to make an error response. */
export function errorToolResponse(
  message: string,
  requestId?: string,
): ToolUseResponse {
  return {
    text: message,
    isError: true,
    requestId: requestId,
  };
}

/**
 * Helper function to create a structured response directly from content nodes.
 * This is used by the MCP host to convert content nodes to a tool use response.
 *
 * @param contentNodes Array of content nodes (text or images)
 * @param isError Whether this response represents an error
 * @param requestId Optional request ID
 * @returns A tool use response with the structured content
 */
export function structuredNodeResponse(
  contentNodes: ToolResponseContentNode[],
  isError: boolean = false,
  requestId?: string,
): ToolUseResponse {
  // If no content nodes, return an empty response
  if (contentNodes.length === 0) {
    return isError
      ? errorToolResponse("No content provided", requestId)
      : successToolResponse("", requestId);
  }

  // Extract text from the first text content node if available
  let firstText = "";
  for (const node of contentNodes) {
    if (
      node.type === ToolResponseContentNodeType.ContentText &&
      node.text_content
    ) {
      firstText = node.text_content;
      break;
    }
  }

  return {
    text: firstText,
    isError,
    requestId,
    contentNodes: contentNodes,
  };
}
