import { ToolBase, ToolSafety, ToolUseResponse } from "../../tool-types";
import { Exchange } from "../../../chat/chat-types";
import { UntruncatedContentManager } from "../../../utils/untruncated-content-manager";
import { SidecarToolType } from "../sidecar-tool-types";

/**
 * Tool for viewing a specific range of untruncated content
 */
export class ViewRangeUntruncatedTool extends ToolBase<SidecarToolType> {
  public description =
    "View a specific range of lines from untruncated content";

  public inputSchemaJson = JSON.stringify({
    type: "object",
    properties: {
      reference_id: {
        type: "string",
        description:
          "The reference ID of the truncated content (found in the truncation footer)",
      },
      start_line: {
        type: "integer",
        description: "The starting line number (1-based, inclusive)",
      },
      end_line: {
        type: "integer",
        description: "The ending line number (1-based, inclusive)",
      },
    },
    required: ["reference_id", "start_line", "end_line"],
  });

  constructor(private readonly _contentManager: UntruncatedContentManager) {
    super(SidecarToolType.viewRangeUntruncated, ToolSafety.Safe);
  }

  public checkToolCallSafe(): boolean {
    return true;
  }

  public async call(
    toolInput: Record<string, unknown>,
    _chatHistory: Exchange[],
    _abortSignal: AbortSignal,
    _toolUseId: string,
  ): Promise<ToolUseResponse> {
    try {
      const referenceId = toolInput.reference_id as string;
      const startLine = toolInput.start_line as number;
      const endLine = toolInput.end_line as number;

      if (startLine < 1) {
        return {
          text: "Error: start_line must be a positive integer",
          isError: true,
        };
      }
      if (endLine < startLine) {
        return {
          text: "Error: end_line must be greater than or equal to start_line",
          isError: true,
        };
      }

      // Get metadata to validate the reference ID
      const metadata = await this._contentManager.getMetadata(referenceId);
      if (!metadata) {
        return {
          text: `Error: Content with reference ID '${referenceId}' not found`,
          isError: true,
        };
      }

      // Get the requested range
      const content = await this._contentManager.getContentRange(
        referenceId,
        startLine,
        endLine,
      );
      if (!content) {
        return {
          text: `Error: Failed to retrieve content for reference ID '${referenceId}'`,
          isError: true,
        };
      }

      // Add line numbers to the output
      const lines = content.split("\n");
      const numberedLines = lines.map(
        (line, index) => `${startLine + index}: ${line}`,
      );

      // Add metadata about the range
      const rangeInfo = `Showing lines ${startLine}-${endLine} of ${metadata.totalLines} total lines`;

      // Include toolType and originalCommand metadata in the response for UI display purposes
      const toolTypeInfo = metadata.toolType
        ? `<!-- toolType: ${metadata.toolType} -->`
        : "";
      const originalCommandInfo = metadata.originalCommand
        ? `<!-- original command: "${metadata.originalCommand}" -->`
        : "";
      const headerInfo = toolTypeInfo + originalCommandInfo;

      return {
        text: `${headerInfo}${rangeInfo}\n\n${numberedLines.join("\n")}`,
        isError: false,
      };
    } catch (error) {
      return {
        text: `Error: ${error instanceof Error ? error.message : String(error)}`,
        isError: true,
      };
    }
  }
}
