import { LRUCache } from "lru-cache";
import { getLogger, AugmentLogger } from "../../logging";
import { IPluginFileStore } from "../../client-interfaces/plugin-file-store";

/**
 * Represents a single command execution record
 */
export interface CommandExecutionRecord {
    /** Original command as executed */
    originalCommand: string;
    /** Execution time in seconds (null if timed out) */
    executionTimeSeconds: number | null;
    /** Timeout value that was used */
    timeoutUsed: number;
    /** Timestamp when the command was executed */
    timestamp: number;
}

/**
 * Aggregated statistics for a command
 */
interface CommandStats {
    /** Number of successful executions */
    successCount: number;
    /** Number of timeouts */
    timeoutCount: number;
    /** Average execution time for successful runs (seconds) */
    avgExecutionTime: number;
    /** Maximum execution time observed (seconds) */
    maxExecutionTime: number;
    /** Last timeout value that failed */
    lastTimeoutThatFailed?: number;
    /** Last successful execution time */
    lastSuccessfulTime?: number;
    /** Timestamp of last execution */
    lastExecuted: number;
}

/**
 * Storage structure for command execution history
 */
interface CommandExecutionHistory {
    /** Individual execution records (limited to recent entries) */
    records: CommandExecutionRecord[];
    /** Aggregated statistics by exact command */
    commandStats: Record<string, CommandStats>;
    /** Version for future migration compatibility */
    version: number;
}

/**
 * Smart timeout predictor that learns from command execution history
 */
export class CommandTimeoutPredictor {
    private readonly _logger: AugmentLogger = getLogger("CommandTimeoutPredictor");
    private readonly _maxRecords = 1000; // Keep last 1000 command records
    private readonly _maxCommands = 500; // Keep stats for up to 500 unique commands
    private readonly _currentVersion = 2; // Incremented for schema change
    private readonly _storageKey = "commandExecutionHistory";

    private readonly _recordsCache = new LRUCache<string, CommandExecutionRecord>({
        max: this._maxRecords,
    });
    private readonly _commandStatsCache = new LRUCache<string, CommandStats>({
        max: this._maxCommands,
    });

    private _isHistoryLoaded = false;
    private _loadingPromise: Promise<void> | null = null;

    constructor(private readonly _storage: IPluginFileStore | null) {
        // Start loading immediately if storage is available
        if (this._storage) {
            this._loadingPromise = this._loadHistoryFromStorage();
        }
    }

    /**
     * Predict timeout for a command based on historical data
     * Returns null if no historical data is available
     */
    async _predictTimeout(command: string): Promise<number | null> {
        try {
            await this._ensureHistoryLoaded();

            // Check if we have statistics for this exact command
            const stats = this._commandStatsCache.get(command);
            if (stats) {
                return this._calculateTimeoutFromStats(stats, command);
            }

            // No historical data available
            return null;
        } catch (error) {
            this._logger.debug(
                `Error predicting timeout for command "${command}": ${String(error)}`
            );
            // If there's an error loading history, no prediction available
            return null;
        }
    }

    /**
     * Get the optimal timeout for a command, choosing between predicted and requested timeout
     * Returns the larger of the two values, or the requested timeout if no prediction is available
     */
    async getOptimalTimeout(command: string, requestedTimeout: number): Promise<number> {
        const predictedTimeout = await this._predictTimeout(command);

        if (predictedTimeout !== null) {
            const optimalTimeout = Math.max(requestedTimeout, predictedTimeout);
            if (optimalTimeout > requestedTimeout) {
                this._logger.debug(
                    `Using predicted timeout ${optimalTimeout}s instead of requested ${requestedTimeout}s for command: ${command}`
                );
            }
            return optimalTimeout;
        }

        return requestedTimeout;
    }

    /**
     * Record the execution of a command and its outcome
     */
    async recordExecution(
        command: string,
        executionTimeSeconds: number | null,
        timeoutUsed: number
    ): Promise<void> {
        try {
            await this._ensureHistoryLoaded();
            const timestamp = Date.now();

            // Add new record
            const record: CommandExecutionRecord = {
                originalCommand: command,
                executionTimeSeconds,
                timeoutUsed,
                timestamp,
            };

            const recordKey = `${command}-${timestamp}`;
            this._recordsCache.set(recordKey, record);

            // Update command statistics
            this._updateCommandStats(command, record);

            // Save updated history
            await this._saveHistory();

            this._logger.debug(
                `Recorded execution: ${command} (${executionTimeSeconds}s, timeout: ${executionTimeSeconds === null ? 'yes' : 'no'})`
            );
        } catch (error) {
            this._logger.debug(
                `Error recording execution for command "${command}": ${String(error)}`
            );
        }
    }


    /**
     * Calculate timeout based on historical statistics
     */
    private _calculateTimeoutFromStats(stats: CommandStats, command: string): number | null {
        // If we have successful executions, use actual measured performance (preferred over timeout history)
        if (stats.successCount > 0) {
            const bufferTime = Math.max(stats.maxExecutionTime * 1.2, stats.maxExecutionTime + 10);
            this._logger.debug(
                `Predicted timeout for "${command}": ${bufferTime}s (based on max: ${stats.maxExecutionTime}s, avg: ${stats.avgExecutionTime}s)`
            );
            return bufferTime;
        }

        // If we only have timeout history (no successful runs yet), increase 2X from last failed timeout
        if (stats.timeoutCount > 0 && stats.lastTimeoutThatFailed) {
            const increasedTimeout = Math.ceil(stats.lastTimeoutThatFailed * 2);
            this._logger.debug(
                `Command "${command}" previously timed out at ${stats.lastTimeoutThatFailed}s, ` +
                    `increasing to ${increasedTimeout}s (no successful runs yet)`
            );
            return increasedTimeout;
        }

        // No historical data to base prediction on
        return null;
    }

    /**
     * Update command statistics with new execution record
     */
    private _updateCommandStats(
        command: string,
        record: CommandExecutionRecord
    ): void {
        let stats = this._commandStatsCache.get(command);

        if (!stats) {
            stats = {
                successCount: 0,
                timeoutCount: 0,
                avgExecutionTime: 0,
                maxExecutionTime: 0,
                lastExecuted: record.timestamp,
            };
        }

        // Update counts and timing
        if (record.executionTimeSeconds === null) {
            stats.timeoutCount++;
            stats.lastTimeoutThatFailed = Math.max(stats.lastTimeoutThatFailed || 0, record.timeoutUsed);
        } else {
            stats.successCount++;
            stats.lastSuccessfulTime = record.executionTimeSeconds;

            // Update average execution time
            const totalTime =
                stats.avgExecutionTime * (stats.successCount - 1) + record.executionTimeSeconds;
            stats.avgExecutionTime = totalTime / stats.successCount;

            // Update max execution time
            stats.maxExecutionTime = Math.max(stats.maxExecutionTime, record.executionTimeSeconds);
        }

        stats.lastExecuted = record.timestamp;
        this._commandStatsCache.set(command, stats);
    }

    /**
     * Load command execution history from storage
     */
    private async _loadHistoryFromStorage(): Promise<void> {
        if (!this._storage) {
            this._isHistoryLoaded = true;
            return;
        }

        try {
            const storedBytes = await this._storage.loadAsset(this._storageKey);

            if (storedBytes) {
                const storedText = new TextDecoder().decode(storedBytes);
                const stored = JSON.parse(storedText) as CommandExecutionHistory;

                if (stored && stored.version === this._currentVersion) {
                    // Load records into cache
                    stored.records.forEach((record: CommandExecutionRecord) => {
                        const recordKey = `${record.originalCommand}-${record.timestamp}`;
                        this._recordsCache.set(recordKey, record);
                    });

                    // Load command stats into cache
                    Object.entries(stored.commandStats || {}).forEach(([command, stats]) => {
                        this._commandStatsCache.set(command, stats);
                    });
                }
            }
            // If version mismatch or no data, caches remain empty

            this._isHistoryLoaded = true;
        } catch (error) {
            this._logger.debug(`Error loading command history: ${String(error)}`);
            // Mark as loaded even on error to avoid repeated attempts
            this._isHistoryLoaded = true;
        }
    }

    /**
     * Ensure command execution history is loaded from storage
     */
    private async _ensureHistoryLoaded(): Promise<void> {
        if (this._isHistoryLoaded) {
            return;
        }

        // We should always have a loading promise if storage exists
        // since the constructor sets it immediately
        if (!this._loadingPromise) {
            this._logger.error("Unexpected state: storage exists but no loading promise found");
            return;
        }

        // Wait for the loading that started in constructor to complete
        await this._loadingPromise;
    }

    /**
     * Save command execution history to storage
     */
    private async _saveHistory(): Promise<void> {
        // If no storage is available, just return early
        if (!this._storage) {
            return;
        }

        const history: CommandExecutionHistory = {
            records: Array.from(this._recordsCache.values()),
            commandStats: Object.fromEntries(this._commandStatsCache.entries()),
            version: this._currentVersion,
        };
        const historyText = JSON.stringify(history);
        const historyBytes = new TextEncoder().encode(historyText);
        await this._storage.saveAsset(this._storageKey, historyBytes);
    }

}
