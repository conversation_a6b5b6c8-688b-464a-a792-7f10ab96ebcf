import { Exchange } from "../../chat/chat-types";
import { getClientWorkspaces } from "../../client-interfaces/client-workspaces";
import { AggregateCheckpointManager } from "../../agent/checkpoint/aggregate-checkpoint-manager";
import { DiffViewDocument } from "../../diff-view/document";
import { ToolBase, ToolSafety, ToolUseResponse } from "../tool-types";
import { SidecarToolType } from "./sidecar-tool-types";
import { errorToolResponse, successToolResponse } from "./tool-use-response";
import { createRequestId } from "../../utils/request-id";

/**
 * A tool that removes files.
 */
export class RemoveFilesTool extends ToolBase<SidecarToolType> {
  constructor(private readonly _checkpointManager: AggregateCheckpointManager) {
    super(SidecarToolType.removeFiles, ToolSafety.Safe);
  }

  public readonly description: string =
    "Remove files. ONLY use this tool to delete files in the user's workspace. This is the only safe tool to delete files in a way that the user can undo the change. Do NOT use the shell or launch-process tools to remove files.";

  public readonly inputSchemaJson: string = JSON.stringify({
    /* eslint-disable @typescript-eslint/naming-convention */
    type: "object",
    properties: {
      file_paths: {
        type: "array",
        description: "The paths of the files to remove.",
        items: {
          type: "string",
        },
      },
    },
    required: ["file_paths"],
    /* eslint-enable @typescript-eslint/naming-convention */
  });

  public checkToolCallSafe(_toolInput: Record<string, unknown>): boolean {
    return true;
  }

  public async call(
    toolInput: Record<string, unknown>,
    chatHistory: Exchange[],
    _abortSignal: AbortSignal,
    _toolUseId: string,
  ): Promise<ToolUseResponse> {
    const filePaths = toolInput.file_paths as string[];
    let numRemoved = 0;
    let error: string | undefined;
    try {
      for (const filePath of filePaths) {
        // Read the file details
        const fileDetails = await getClientWorkspaces().readFile(filePath);
        if (
          fileDetails === undefined ||
          fileDetails.contents === undefined ||
          fileDetails.filepath === undefined
        ) {
          error = `Cannot read file: ${filePath}`;
          break;
        }

        const qualifiedPathName = fileDetails.filepath;
        const fileContent = fileDetails.contents;

        // Create a checkpoint with the file content as original and empty string as modified
        // This represents the file being deleted
        const document = new DiffViewDocument(
          qualifiedPathName,
          fileContent,
          undefined, // undefined represents file deletion
          {},
        );

        // Get the exchange ID that resulted in this tool call
        const toolUseExchangeRequestId =
          chatHistory.at(-1)?.request_id ?? createRequestId();
        const conversationId =
          this._checkpointManager.currentConversationId ?? "";

        // Delete the file using the checkpointing interface
        try {
          await this._checkpointManager.addCheckpoint(
            {
              conversationId,
              path: qualifiedPathName,
            },
            {
              sourceToolCallRequestId: toolUseExchangeRequestId,
              timestamp: Date.now(),
              document,
              conversationId,
            },
          );
          numRemoved++;
        } catch (unlinkError) {
          error = `Failed to delete file ${filePath}: ${unlinkError instanceof Error ? unlinkError.message : String(unlinkError)}`;
          break;
        }
      }
    } catch (e) {
      error = `Failed to remove file(s): ${e instanceof Error ? e.message : String(e)}`;
    }
    if (numRemoved === filePaths.length && error === undefined) {
      return successToolResponse(
        `File(s) removed: ${filePaths.slice(0, numRemoved).join(", ")}`,
      );
    } else {
      return errorToolResponse(
        `Removed file(s) ${filePaths.slice(0, numRemoved).join(", ")} but failed to remove file(s): ${filePaths.slice(numRemoved).join(", ")}: ${error ?? ""}`,
      );
    }
  }
}
