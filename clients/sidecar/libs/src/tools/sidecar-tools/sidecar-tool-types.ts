export enum SidecarToolType {
  shell = "shell",
  webFetch = "web-fetch",
  strReplaceEditor = "str-replace-editor",
  codebaseRetrieval = "codebase-retrieval",
  removeFiles = "remove-files",
  remember = "remember",
  view = "view",
  saveFile = "save-file",
  viewTaskList = "view_tasklist",
  viewRangeUntruncated = "view-range-untruncated",
  searchUntruncated = "search-untruncated",
  reorganizeTaskList = "reorganize_tasklist",
  updateTasks = "update_tasks",
  addTasks = "add_tasks",
  renderMermaid = "render-mermaid",
  grepSearch = "grep-search",
}

export const AgentEditTools = {
  strReplaceEditor: "str_replace_editor_tool",
  backendEditTool: "backend_edit_tool",
} as const;
