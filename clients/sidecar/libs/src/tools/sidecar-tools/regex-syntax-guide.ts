export const REGEX_SYNTAX_GUIDE = `
# Regex Syntax Reference

Only the core regex feature common across JavaScript and Rust are supported.

## Supported regex syntax

* **Escaping** - Escape metacharacters with a backslash: \`\\.\` \`\\+\` \`\\?\` \`\\*\` \`\\|\` \`\\(\` \`\\)\` \`\\[\`.
* **Dot** \`.\` - matches any character **except newline** (\`\\n\`, \`\\r\`, \`\\u2028\`, \`\\u2029\`).
* **Character classes** - \`[abc]\`, ranges such as \`[a-z]\`, and negation \`[^…]\`. Use explicit ASCII ranges; avoid shorthand like \`\\d\`.
* **Alternation** - \`foo|bar\` chooses the leftmost successful branch.
* **Quantifiers** - \`*\`, \`+\`, \`?\`, \`{n}\`, \`{n,}\`, \`{n,m}\` (greedy). Add \`?\` after any of these for the lazy version.
* **Anchors** - \`^\` (start of line), \`$\` (end of line).
* **Special characters** - Use \`\\t\` for tab character

---

## Do **Not** Use (Unsupported)

* Newline character \`\\n\`. Only single line mode is supported.
* Look-ahead / look-behind \`(?= … )\`, \`(?<= … )\`.
* Back-references \`\\1\`, \`\\k<name>\`.
* Groups \`(?<name> … )\`, \`(?P<name> … )\`.
* Shorthand classes \`\\d\`, \`\\s\`, \`\\w\`, \`\\b\`, Unicode property escapes \`\\p{…}\`.
* Flags inside pattern \`(?i)\`, \`(?m)\`, etc.
* Recursion, conditionals, atomic groups, possessive quantifiers
* Unicode escapes like these \`\\u{1F60A}\` or \`\\u1F60A\`.
`;
