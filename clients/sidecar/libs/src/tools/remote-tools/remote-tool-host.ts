import { Exchange } from "../../chat/chat-types";
import { getAPIClient } from "../../client-interfaces/api-client";
import { getLogger } from "../../logging";
import { getValues } from "../../utils/enums";
import { createRequestId } from "../../utils/request-id";
import { IToolHost } from "../tool-host";
import { ITool } from "../tool-host-base";
import {
  RemoteToolId,
  ToolAvailabilityStatus,
  ToolDefinition,
  ToolDefinitionWithSettings,
  ToolHostName,
  ToolSafety,
  ToolType,
  ToolUseResponse,
} from "../tool-types";
import { CheckToolCallSafeRequestData } from "../../webview-messages/message-types/tool-messages";
import { GitHubToolTransformation } from "./transformations/github";

/**
 * A tool host for tools that run remotely on the server.
 */
export class RemoteToolHost implements IToolHost {
  private _abortController: AbortController | undefined = undefined;
  private _toolMap: Map<RemoteToolId, ToolDefinitionWithSettings> = new Map();
  private _logger = getLogger("RemoteToolHost");

  private readonly _transformationMap: Map<
    RemoteToolId,
    IRemoteToolTransformation
  >;

  // Cache for tool definitions to avoid frequent backend calls
  private _cachedToolDefinitions: ToolDefinitionWithSettings[] | null = null;
  private _lastCacheUpdateTime: number = 0;
  // Cache expiration time in milliseconds (default: 60 minutes)
  private readonly _cacheExpirationMs: number = 60 * 60 * 1000;

  constructor(private readonly _remoteInfoSource: RemoteInfoSource) {
    const githubTransform = new GitHubToolTransformation();
    this._transformationMap = new Map([
      [githubTransform.toolId, githubTransform],
    ]);
  }

  public async getToolDefinitions(
    useCache: boolean = true,
  ): Promise<ToolDefinitionWithSettings[]> {
    const tools = (await this._getToolDefinitions(useCache)).filter(
      (tool) => tool.isConfigured,
    );

    this._toolMap.clear();
    tools.forEach((tool) => {
      // Since we're in RemoteToolHost, we know toolId is a RemoteToolId
      this._toolMap.set(tool.identifier.toolId as RemoteToolId, tool);
    });
    return tools;
  }

  public async getAllToolDefinitions(
    useCache: boolean = true,
  ): Promise<ToolDefinitionWithSettings[]> {
    return await this._getToolDefinitions(useCache);
  }

  public getTool<T extends ToolType>(_toolName: string): ITool<T> | undefined {
    // We can't get the actual tool for remote tools.
    return undefined;
  }

  public getName(): ToolHostName {
    return ToolHostName.remoteToolHost;
  }

  private async _getToolDefinitions(
    useCache: boolean = true,
  ): Promise<ToolDefinitionWithSettings[]> {
    // Check if we have a valid cache and should use it
    if (
      useCache &&
      this._cachedToolDefinitions !== null &&
      Date.now() - this._lastCacheUpdateTime < this._cacheExpirationMs
    ) {
      return this._cachedToolDefinitions;
    }

    this._logger.debug("Fetching remote tool definitions from backend");
    try {
      const toolIDs = getValues<RemoteToolId>(RemoteToolId);
      const allTools =
        await this._remoteInfoSource.retrieveRemoteTools(toolIDs);
      const toolsNeedingExtraInput = allTools
        .filter((tool) => {
          return (
            tool.availabilityStatus ===
            ToolAvailabilityStatus.UserConfigRequired
          );
        })
        .map((tool) => tool.remoteToolId);

      const toolsWithExtraInput =
        await this._remoteInfoSource.filterToolsWithExtraInput(
          toolsNeedingExtraInput,
        );

      const isConfigured = (tool: RemoteToolDefinition) => {
        // If the tool is Available, it's already configured
        if (tool.availabilityStatus === ToolAvailabilityStatus.Available) {
          return true;
        }

        // If the tool requires user config, check if it's in the toolsWithExtraInput set
        if (
          tool.availabilityStatus === ToolAvailabilityStatus.UserConfigRequired
        ) {
          const hasExtraInput = toolsWithExtraInput.has(tool.remoteToolId);
          return hasExtraInput;
        }

        return false;
      };

      // Apply transformations and update tool map
      const transformedTools = await Promise.all(
        allTools.map(async (tool) => {
          const transform = this._transformationMap.get(tool.remoteToolId);
          if (transform) {
            this._logger.debug(
              `Applying transformation for ${tool.remoteToolId}`,
            );
            const transformedDefinition = await transform.transform(
              tool.toolDefinition,
            );
            return { ...tool, toolDefinition: transformedDefinition };
          }
          return tool;
        }),
      );

      const toolDefinitions = transformedTools.map(
        (tool): ToolDefinitionWithSettings => ({
          definition: tool.toolDefinition,
          identifier: {
            hostName: ToolHostName.remoteToolHost,
            toolId: tool.remoteToolId,
          },
          isConfigured: isConfigured(tool),
          enabled: true,
          toolSafety: tool.toolSafety,
          oauthUrl: tool.oauthUrl,
        }),
      );

      // Update the cache
      this._cachedToolDefinitions = toolDefinitions;
      this._lastCacheUpdateTime = Date.now();

      return toolDefinitions;
    } catch (e: unknown) {
      this._logger.error("Failed to list remote tools", e);
      // If we have a cache, use it even if it's expired when there's an error
      if (this._cachedToolDefinitions !== null) {
        this._logger.info(
          "Using expired cache due to error fetching remote tools",
        );
        return this._cachedToolDefinitions;
      }
      return [];
    }
  }

  private findToolIdByName(toolName: string): RemoteToolId {
    // Iterate through the map to find a tool with the matching name
    for (const [id, tool] of this._toolMap.entries()) {
      if (tool.definition.name.toString() === toolName) {
        return id;
      }
    }
    return RemoteToolId.Unknown;
  }

  public async callTool(
    _chatRequestId: string,
    _toolUseId: string,
    toolName: string,
    toolInput: Record<string, unknown>,
    _chatHistory: Exchange[],
  ): Promise<ToolUseResponse> {
    try {
      const abortController = (this._abortController = new AbortController());
      // Find the tool by name first
      const toolId = this.findToolIdByName(toolName);
      const tool = this._toolMap.get(toolId);

      if (!tool) {
        this._logger.error(`Tool not found: ${toolName}`);
        return {
          text: `Tool ${toolName} not found.`,
          isError: true,
        };
      }

      const toolRequestId = createRequestId();
      const result = await this._remoteInfoSource.runRemoteTool(
        toolRequestId,
        toolName,
        JSON.stringify(toolInput),
        toolId,
        abortController.signal,
      );
      if (result.status !== RemoteToolResponseStatus.ExecutionSuccess) {
        this._logger.error(
          `Failed to run remote tool ${toolName}: ${RemoteToolResponseStatus[result.status]}`,
        );
      }
      return {
        text: result.toolOutput,
        isError: result.status !== RemoteToolResponseStatus.ExecutionSuccess,
        requestId: toolRequestId,
      };
    } catch (err) {
      return {
        text: `Failed to run remote tool ${toolName} - ${err instanceof Error ? err.message : (err as string)}`,
        isError: true,
      };
    } finally {
      this._abortController = undefined;
    }
  }

  public async checkToolCallSafe(
    request: CheckToolCallSafeRequestData,
  ): Promise<boolean> {
    const toolId = this.findToolIdByName(request.toolName);
    const tool = this._toolMap.get(toolId);

    switch (toolId) {
      // We'll add special-cases for individual tools as they
      // add support for safety configuration.
      default:
        // Default behavior: check tool safety
        if (tool?.toolSafety === ToolSafety.Check) {
          return getAPIClient().checkToolSafety(
            toolId,
            JSON.stringify(request.input),
          );
        }
        return Promise.resolve(tool?.toolSafety === ToolSafety.Safe);
    }
  }

  public isRequestActive(_requestId: string, _toolUseId: string): boolean {
    return this._abortController !== undefined;
  }

  public close(_cancelledByUser: boolean = false): Promise<void> {
    this._abortController?.abort();
    return Promise.resolve();
  }

  public closeAllToolProcesses(): Promise<void> {
    return Promise.resolve();
  }

  factory(_preconditionWait: Promise<void>): IToolHost {
    // Create a completely new instance with a fresh cache
    return new RemoteToolHost(this._remoteInfoSource);
  }
}

export interface RemoteInfoSource {
  retrieveRemoteTools(
    supportedTools: RemoteToolId[],
  ): Promise<RemoteToolDefinition[]>;
  filterToolsWithExtraInput(
    toolIds: RemoteToolId[],
  ): Promise<Set<RemoteToolId>>;
  runRemoteTool(
    toolRequestId: string,
    toolName: string,
    toolInputJson: string,
    toolId: RemoteToolId,
    signal: AbortSignal,
  ): Promise<RunRemoteToolResult>;
}

export type RemoteToolDefinition = {
  toolDefinition: ToolDefinition;
  remoteToolId: RemoteToolId;
  availabilityStatus: ToolAvailabilityStatus;
  toolSafety: ToolSafety;
  oauthUrl: string;
};

export type RunRemoteToolResult = {
  toolOutput: string;
  toolResultMessage: string;
  status: RemoteToolResponseStatus;
};

export enum RemoteToolResponseStatus {
  // Unknown status
  ExecutionUnknownStatus = 0,
  // Tool executed successfully
  ExecutionSuccess = 1,
  // Tool not found
  NotFound = 2,
  // Invalid input that violates the tool's input schema
  InvalidInput = 3,
  // Tool execution failed
  ExecutionError = 4,
  // Tool is not available due to config
  NotAvailable = 5,
  // Auth failed
  AuthenticationError = 6,
}

/**
 * Interface for transforming remote tool definitions
 */
export interface IRemoteToolTransformation {
  toolId: RemoteToolId;
  transform(tool: ToolDefinition): Promise<ToolDefinition>;
}
