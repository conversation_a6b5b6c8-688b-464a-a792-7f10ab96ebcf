import { ExecOptions } from "child_process";
import { IRemoteToolTransformation } from "../remote-tool-host";
import { RemoteToolId, ToolDefinition } from "../../tool-types";
import { executeCommand } from "../../tool-utils";
import { getClientWorkspaces } from "@augment-internal/sidecar-libs/src/client-interfaces/client-workspaces";

/**
 * GitHub tool transformation implementation
 */
export class GitHubToolTransformation implements IRemoteToolTransformation {
  public readonly toolId = RemoteToolId.GitHubApi;
  constructor() {}

  async transform(tool: ToolDefinition): Promise<ToolDefinition> {
    const gitInfo = await this.getGitInfo();
    if (!gitInfo) {
      return tool;
    }

    return {
      ...tool,
      description: tool.description + "\n\n" + gitInfo,
    };
  }

  private async getGitInfo(): Promise<string | undefined> {
    const cwd = await getClientWorkspaces().getCwd();
    if (!cwd) {
      return undefined;
    }

    const execOptions: ExecOptions = {
      timeout: 1000,
      cwd: cwd,
    };

    try {
      const gitRoot = await executeCommand(
        "git rev-parse --show-toplevel",
        execOptions,
      );
      if (!gitRoot) {
        return undefined;
      }

      // Create new options object for subsequent commands
      const gitRootOptions: ExecOptions = {
        timeout: 1000,
        cwd: gitRoot,
      };

      const [remoteUrl, currentBranch, defaultBranch, userEmail] =
        await Promise.all([
          executeCommand("git config --get remote.origin.url", gitRootOptions),
          executeCommand("git rev-parse --abbrev-ref HEAD", gitRootOptions),
          executeCommand(
            "git rev-parse --abbrev-ref origin/HEAD",
            gitRootOptions,
          ),
          executeCommand("git config --get user.email", gitRootOptions),
        ]);

      // At last one of the info (gitRoot) must be non-empty
      // If some info is missing just skip it
      const mainBranch = defaultBranch?.split("/").pop();
      const infoLines = ["Local Git Repository Information:"];
      if (gitRoot) infoLines.push(`- Repository Root: ${gitRoot}`);
      if (remoteUrl) infoLines.push(`- Remote URL: ${remoteUrl}`);
      if (currentBranch) infoLines.push(`- Current Branch: ${currentBranch}`);
      if (mainBranch) infoLines.push(`- Default Branch: ${mainBranch}`);
      if (userEmail) infoLines.push(`- Git User Email: ${userEmail}`);
      infoLines.push("REPOSITORY SCOPE:");
      infoLines.push(
        "All queries MUST be limited to this repository only, unless explicitly requested otherwise. " +
          "Always indicate in text outside of the tool use that you are limiting to this repo if you are doing so.",
      );
      return infoLines.join("\n") + "\n";
    } catch (error) {
      return undefined;
    }
  }
}
