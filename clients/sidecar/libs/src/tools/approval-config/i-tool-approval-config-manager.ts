import { ToolIdentifier, ToolApprovalConfig } from "../tool-types";

/**
 * A ToolApprovalConfigManager acts as a proxy for saving and retrieving tool
 * approval configurations in a client-agnostic way.
 */
export interface IToolApprovalConfigManager {
  /**
   * Gets the tool approval config for the given tool ID, or undefined if none exists.
   */
  getToolApprovalConfig(
    input: GetToolApprovalConfigInput,
  ): Promise<ToolApprovalConfig | undefined>;
  /**
   * Sets the tool approval config for the given tool ID.
   */
  setToolApprovalConfig(input: SetToolApprovalConfigInput): Promise<void>;
}

export type GetToolApprovalConfigInput = {
  // The mode to get the config for. If mode is the empty string, we will return
  // undefined as if no config exists.
  mode: string;
  // The tool to get the config for.
  toolId: ToolIdentifier;
};

export type SetToolApprovalConfigInput = {
  // The mode to set the config for. Currently we only support configuring settings
  // for the "manual" mode.
  mode: "manual";
  // The tool to set the config for.
  toolId: ToolIdentifier;
  // The configuration for the tool, should be of the corresponding
  // type for the toolId.
  config: ToolApprovalConfig;
};
