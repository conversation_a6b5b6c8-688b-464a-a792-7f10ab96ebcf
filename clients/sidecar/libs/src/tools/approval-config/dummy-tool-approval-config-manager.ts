import {
  GetToolApprovalConfigInput,
  IToolApprovalConfigManager,
  SetToolApprovalConfigInput,
} from "./i-tool-approval-config-manager";
import { ToolApprovalConfig } from "../tool-types";

/**
 * Dummy tool approval config manager for testing or clients that don't need
 * to persist tool approval configs.
 */
export class DummyToolApprovalConfigManager
  implements IToolApprovalConfigManager
{
  async getToolApprovalConfig(
    _input: GetToolApprovalConfigInput,
  ): Promise<ToolApprovalConfig | undefined> {
    return Promise.resolve(undefined);
  }

  async setToolApprovalConfig(
    _input: SetToolApprovalConfigInput,
  ): Promise<void> {
    return Promise.resolve();
  }
}
