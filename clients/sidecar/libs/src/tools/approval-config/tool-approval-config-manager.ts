import { IPluginFileStore } from "../../client-interfaces/plugin-file-store";
import { getLogger } from "../../logging";
import { getErrmsg } from "../../exceptions";
import { ToolIdentifier, ToolApprovalConfig } from "../tool-types";
import {
  GetToolApprovalConfigInput,
  IToolApprovalConfigManager,
  SetToolApprovalConfigInput,
} from "./i-tool-approval-config-manager";

/**
 * ToolApprovalConfigManager acts as a proxy for saving and retrieving tool
 * approval configurations in a client-agnostic way.
 *
 * "Tool approval configs" are configurations that determine whether tool uses
 * by the agent need user approval. For ease of development we use generic
 * getter/setter methods that take a ToolIdentifier and a ToolApprovalConfig.
 * This does mean the return types are not as strongly typed. We could use conditional
 * typing to return the correct types based on the input, but then that also
 * runs into the issue of using type assertions (i.e.: the type checker wouldn't
 * actually be able to verify the types at compile time anyways).
 *
 * Uses IPluginFileStore to persist settings in files.
 */
export class ToolApprovalConfigManager implements IToolApprovalConfigManager {
  private _logger = getLogger("ToolApprovalConfigManager");

  constructor(private _pluginFileStore: IPluginFileStore) {}

  async getToolApprovalConfig(
    input: GetToolApprovalConfigInput,
  ): Promise<ToolApprovalConfig | undefined> {
    const { mode, toolId } = input;

    // If no mode is provided, we must return undefined as if no config exists.
    if (!mode) {
      return undefined;
    }

    // Use a reference to a IPluginFileStore to retrieve the
    // config from a file.
    try {
      const configPath = getToolApprovalConfigPath(mode, toolId);

      const configData = await this._pluginFileStore.loadAsset(configPath);
      if (!configData) {
        // Return default config if none exists
        return undefined;
      }

      const configText = new TextDecoder().decode(configData);
      const config = JSON.parse(configText) as ToolApprovalConfig;

      return config;
    } catch (error) {
      this._logger.error(
        `Failed to load tool approval config: ${getErrmsg(error, true)}`,
      );
      return undefined;
    }
  }

  async setToolApprovalConfig(
    input: SetToolApprovalConfigInput,
  ): Promise<void> {
    const { mode, toolId, config } = input;
    // Generically save the config to a file using the tool identifier
    try {
      const configPath = getToolApprovalConfigPath(mode, toolId);

      const configText = JSON.stringify(config, null, 2);
      const configData = new TextEncoder().encode(configText);

      await this._pluginFileStore.saveAsset(configPath, configData);
      this._logger.info(`Saved tool approval config: ${configPath}`);
    } catch (error) {
      this._logger.error(
        `Failed to save tool approval config: ${getErrmsg(error, true)}`,
      );
      throw error;
    }
  }
}

function toolIdentifierToString(identifier: ToolIdentifier): string {
  return `${identifier.hostName}-${String(identifier.toolId)}`;
}

function getToolApprovalConfigPath(
  mode: string,
  toolId: ToolIdentifier,
): string {
  return `tool-configs/approval/${mode}/${toolIdentifierToString(toolId)}.json`;
}
