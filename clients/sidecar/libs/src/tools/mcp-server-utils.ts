import {
  McpServerConfig,
  McpServerStdioConfig,
  McpServerHttpConfig,
  McpServerType,
} from "./tool-types";
import { z } from "zod";

// Re-export the type for convenience
export type { McpServerType };

// Error class for MCP server configuration errors
export class MCPServerError extends Error {
  constructor(message: string) {
    super(message);
    this.name = "MCPServerError";
    Object.setPrototypeOf(this, MCPServerError.prototype);
  }
}

// Type for parsed MCP server (with id, compatible with webview storage format)
export interface ParsedMCPServer {
  id: string;
  type: McpServerType;
  name: string;
  command?: string;
  arguments?: string;
  useShellInterpolation?: boolean;
  tools?: string[];
  disabledTools?: string[];
  env?: Record<string, string>;
  url?: string;
  disabled?: boolean;
}

// Type for normalized MCP server (without id, used during parsing)
export type NormalizedMCPServer = Omit<ParsedMCPServer, "id">;

// Base schema for server configuration properties
const baseServerConfigSchema = z
  .object({
    name: z.string().optional(),
    title: z.string().optional(),
    type: z.enum(["stdio", "http", "sse"] as const).optional(),
    // Stdio server properties
    command: z.string().optional(),
    args: z.array(z.union([z.string(), z.number(), z.boolean()])).optional(),
    env: z
      .record(
        z.union([z.string(), z.number(), z.boolean(), z.null(), z.undefined()]),
      )
      .optional(),
    // HTTP server properties
    url: z.string().optional(),
  })
  .passthrough(); // Allow additional properties

// Type for a single server config
type ImportedServerConfig = z.infer<typeof baseServerConfigSchema>;

/**
 * Type guard to check if a server is an HTTP/SSE server
 */
export function isStreamingServer(
  server: McpServerConfig | null | undefined,
): server is McpServerHttpConfig {
  return server?.type === "http" || server?.type === "sse";
}

/**
 * Type guard to check if a server is a stdio server
 */
export function isStdioServer(
  server: McpServerConfig | null | undefined,
): server is McpServerStdioConfig {
  return server?.type === "stdio";
}

/**
 * Get the command or URL from a server config
 */
export function getCommandOrUrl(server: McpServerConfig): string {
  if (isStreamingServer(server)) {
    return server.url;
  } else if (isStdioServer(server)) {
    return server.command;
  }
  return "";
}

// Schema definitions for different configuration formats

/**
 * Format 1: Array of server configs
 */
const arrayOfServersSchema = z.array(baseServerConfigSchema);

/**
 * Format 2: Object with 'servers' property as an array
 */
const serversArrayContainerSchema = z.object({
  servers: z.array(baseServerConfigSchema),
});

/**
 * Format 3: Object with 'mcpServers' property as an array
 */
const mcpServersArrayContainerSchema = z.object({
  mcpServers: z.array(baseServerConfigSchema),
});

/**
 * Format 4: Object with 'servers' property as a record
 */
const serversRecordContainerSchema = z.object({
  servers: z.record(z.unknown()),
});

/**
 * Format 5: Object with 'mcpServers' property as a record
 */
const mcpServersRecordContainerSchema = z.object({
  mcpServers: z.record(z.unknown()),
});

/**
 * Format 6: Claude Desktop format (direct key-value pairs of server configs)
 */
const claudeDesktopSchema = z.record(z.unknown());

/**
 * Format 7: Single server object
 * This schema is more restrictive to avoid matching Claude Desktop format
 */
const singleServerSchema = baseServerConfigSchema.refine(
  (data) => {
    // A single server object must have either command or url
    const hasCommand = data.command !== undefined;
    const hasUrl = data.url !== undefined;

    if (!hasCommand && !hasUrl) {
      return false;
    }

    // Check that all keys are valid server properties
    const validKeys = new Set([
      "name",
      "title",
      "type",
      "command",
      "args",
      "env",
      "url",
    ]);

    const allKeysValid = Object.keys(data).every((key) => validKeys.has(key));
    return allKeysValid;
  },
  {
    message: "Single server object must have valid server properties",
  },
);

/**
 * Normalizes a server configuration to ensure it has the required properties
 * @param server The server configuration to normalize
 * @returns A normalized server configuration
 */
export function normalizeServerConfig(
  server: ImportedServerConfig,
): NormalizedMCPServer {
  try {
    // Use the existing baseServerConfigSchema as the foundation
    const normalizer = baseServerConfigSchema
      .transform((data) => {
        // Determine server type - if type is explicitly set, use it
        // Otherwise, infer from available properties
        let serverType: McpServerType;
        if (data.type) {
          serverType = data.type;
        } else if (data.url) {
          serverType = "http";
        } else if (data.command) {
          serverType = "stdio";
        } else {
          throw new Error(
            "Server must have either 'command' (for stdio) or 'url' (for http/sse) property",
          );
        }

        if (serverType === "http" || serverType === "sse") {
          if (!data.url) {
            throw new Error(
              `${serverType.toUpperCase()} server must have a 'url' property`,
            );
          }

          const name = data.name || data.title || data.url;

          return {
            type: serverType,
            name,
            url: data.url,
          };
        } else {
          // Handle stdio server
          // 1. Determine the command
          const baseCommand = data.command || "";

          // 2. Process arguments if they exist
          const args = data.args
            ? data.args.map((arg: string | number | boolean) => String(arg))
            : [];

          // 3. Combine command and args
          // If we don't have a command, we can't proceed
          if (!baseCommand) {
            throw new Error("Stdio server must have a 'command' property");
          }

          const finalCommand =
            args.length > 0 ? `${baseCommand} ${args.join(" ")}` : baseCommand;

          // 4. Determine the name (name > title > command)
          const name =
            data.name ||
            data.title ||
            (baseCommand ? baseCommand.split(" ")[0] : "");

          // 5. Process environment variables
          const env = data.env
            ? Object.fromEntries(
                Object.entries(data.env)
                  .filter(
                    ([_, value]: [string, unknown]) =>
                      value !== null && value !== undefined,
                  )
                  .map(([key, value]: [string, unknown]) => [
                    key,
                    String(value),
                  ]),
              )
            : undefined;

          // 6. Return the normalized server
          return {
            type: "stdio" as const,
            name,
            command: finalCommand,
            arguments: "", // Empty since we've included the args in the command
            useShellInterpolation: true, // New servers use shell interpolation
            env: Object.keys(env || {}).length > 0 ? env : undefined,
          };
        }
      })
      .refine((data) => !!data.name, {
        message: "Server must have a name",
        path: ["name"],
      })
      .refine(
        (data) => {
          if (data.type === "http" || data.type === "sse") {
            return !!data.url;
          } else {
            return !!data.command;
          }
        },
        {
          message:
            "Server must have either 'command' (for stdio) or 'url' (for http/sse)",
          path: ["command", "url"],
        },
      );

    // Apply the normalization
    const result = normalizer.safeParse(server);
    if (!result.success) {
      throw new MCPServerError(result.error.message);
    }

    return result.data;
  } catch (error) {
    if (error instanceof Error) {
      throw new MCPServerError(
        `Invalid server configuration: ${error.message}`,
      );
    }
    throw new MCPServerError("Invalid server configuration");
  }
}

/**
 * Parses MCP server configurations from a JSON string
 * @param jsonString The JSON string containing MCP server configurations
 * @returns An array of normalized server configurations
 * @throws If the JSON is invalid or has an unsupported format
 */
export function parseServerConfigFromJSON(
  jsonString: string,
): NormalizedMCPServer[] {
  try {
    const parsedData: unknown = JSON.parse(jsonString);

    // Define a parser that tries all supported formats
    const serverConfigParser = z.union([
      // Format 1: Array of server configs
      arrayOfServersSchema.transform((configs: ImportedServerConfig[]) =>
        configs.map((config: ImportedServerConfig) =>
          normalizeServerConfig(config),
        ),
      ),

      // Format 2: Object with 'servers' property as an array
      serversArrayContainerSchema.transform(
        (container: { servers: ImportedServerConfig[] }) =>
          container.servers.map((config: ImportedServerConfig) =>
            normalizeServerConfig(config),
          ),
      ),

      // Format 3: Object with 'mcpServers' property as an array
      mcpServersArrayContainerSchema.transform(
        (container: { mcpServers: ImportedServerConfig[] }) =>
          container.mcpServers.map((config: ImportedServerConfig) =>
            normalizeServerConfig(config),
          ),
      ),

      // Format 4: Object with 'servers' property as a record
      serversRecordContainerSchema.transform(
        (container: { servers: Record<string, unknown> }) =>
          Object.entries(container.servers).map(
            ([serverName, configUnknown]) => {
              const config = baseServerConfigSchema.parse(configUnknown);
              return normalizeServerConfig({
                ...config,
                name: config.name || serverName,
              });
            },
          ),
      ),

      // Format 5: Object with 'mcpServers' property as a record
      mcpServersRecordContainerSchema.transform(
        (container: { mcpServers: Record<string, unknown> }) =>
          Object.entries(container.mcpServers).map(
            ([serverName, configUnknown]) => {
              const config = baseServerConfigSchema.parse(configUnknown);
              return normalizeServerConfig({
                ...config,
                name: config.name || serverName,
              });
            },
          ),
      ),

      // Format 7: Single server object (moved before Claude Desktop format)
      singleServerSchema.transform((config: ImportedServerConfig) => [
        normalizeServerConfig(config),
      ]),

      // Format 6: Claude Desktop format (direct key-value pairs of server configs)
      claudeDesktopSchema.transform((record: Record<string, unknown>) => {
        // Verify at least one value has command or url to confirm it's a server config
        const hasValidServer = Object.values(record).some((value) => {
          const parsed = baseServerConfigSchema.safeParse(value);
          return (
            parsed.success &&
            (parsed.data.command !== undefined || parsed.data.url !== undefined)
          );
        });

        if (!hasValidServer) {
          throw new Error(
            "No command or url property found in any server config",
          );
        }

        return Object.entries(record).map(([serverName, configUnknown]) => {
          const config = baseServerConfigSchema.parse(configUnknown);
          return normalizeServerConfig({
            ...config,
            name: config.name || serverName,
          });
        });
      }),
    ]);

    // Try to parse with the unified parser
    const result = serverConfigParser.safeParse(parsedData);

    if (result.success) {
      return result.data;
    }

    // If we get here, none of the schemas matched
    throw new MCPServerError(
      "Invalid JSON format. Expected an array of servers or an object with a 'servers' property.",
    );
  } catch (error) {
    if (error instanceof MCPServerError) {
      throw error;
    }
    throw new MCPServerError(
      "Failed to parse MCP servers from JSON. Please check the format.",
    );
  }
}
