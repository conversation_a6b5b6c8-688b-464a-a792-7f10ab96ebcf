import { Exchange } from "../chat/chat-types";
import { getLogger, type AugmentLogger } from "../logging";
import { SidecarToolType } from "./sidecar-tools/sidecar-tool-types";
import { IToolHost } from "./tool-host";
import {
  LocalToolType,
  MCPToolType,
  RemoteToolId,
  ToolDefinitionWithSettings,
  ToolHostName,
  ToolIdentifier,
  ToolSafety,
  ToolType,
  ToolUseResponse,
} from "./tool-types";
import { CheckToolCallSafeRequestData } from "../webview-messages/message-types/tool-messages";

// Define a mapping from tool type to host name
export type ToolTypeToHostName<T extends ToolType> = T extends RemoteToolId
  ? ToolHostName.remoteToolHost
  : T extends LocalToolType
    ? ToolHostName.localToolHost
    : T extends SidecarToolType
      ? ToolHostName.sidecarToolHost
      : T extends MCPToolType
        ? ToolHostName.mcpHost
        : never;

/**
 * A tool host for tools that run in the sidecar (i.e. a node environment).
 */
export abstract class ToolHostBase<T extends ToolType> implements IToolHost {
  /** Running tool, if any */
  protected _runningTool:
    | { requestId: string; toolUseId: string; tool: ITool<T> }
    | undefined = undefined;

  protected _abortController: AbortController | undefined = undefined;
  protected readonly _logger: AugmentLogger;

  constructor(
    protected readonly _tools: ITool<T>[],
    protected readonly hostName: ToolTypeToHostName<T>,
    private readonly _unsupportedTools?: Set<T>,
  ) {
    this._logger = getLogger(`ToolHostBase-${hostName}`);
  }

  public getToolDefinitions(): Promise<ToolDefinitionWithSettings[]> {
    return Promise.resolve(
      this._tools
        .filter((t) => {
          return !this._unsupportedTools?.has(t.name);
        })
        .map(
          (tool): ToolDefinitionWithSettings => ({
            definition: {
              name: tool.name,
              description: tool.description,
              input_schema_json: tool.inputSchemaJson,
              tool_safety: tool.toolSafety,
            },
            identifier: {
              hostName: this.hostName,
              toolId: tool.name,
            } as ToolIdentifier,
            isConfigured: true,
            enabled: true,
            toolSafety: tool.toolSafety,
          }),
        ),
    );
  }

  public getAllToolDefinitions(
    _useCache: boolean = true,
  ): Promise<ToolDefinitionWithSettings[]> {
    return this.getToolDefinitions();
  }

  public getTool<U extends ToolType>(toolName: string): ITool<U> | undefined {
    return this._tools.find((tool) => tool.name === toolName) as
      | ITool<U>
      | undefined;
  }

  public getName(): ToolHostName {
    return this.hostName;
  }

  public async callTool(
    requestId: string,
    toolUseId: string,
    toolName: string,
    toolInput: Record<string, unknown>,
    chatHistory: Exchange[],
  ): Promise<ToolUseResponse> {
    const tool = this._tools.find((tool) => tool.name.toString() === toolName);
    if (tool === undefined) {
      return {
        isError: true,
        text: `Tool ${toolName} not found.`,
      };
    }
    this._runningTool = {
      requestId: requestId,
      toolUseId: toolUseId,
      tool: tool,
    };
    try {
      const abortController = (this._abortController = new AbortController());
      return await tool.call(
        toolInput,
        chatHistory,
        abortController.signal,
        toolUseId,
      );
    } finally {
      this._runningTool = undefined;
      this._abortController = undefined;
    }
  }

  public async checkToolCallSafe(
    request: CheckToolCallSafeRequestData,
  ): Promise<boolean> {
    // Currently all local tools auto-run in auto mode.
    if (request.agentMode === "auto") {
      return true;
    }

    const tool = this._tools.find((tool) => tool.name === request.toolName);
    if (tool === undefined) {
      return Promise.resolve(false);
    }
    return tool.checkToolCallSafe(request.input);
  }

  public isRequestActive(requestId: string, toolUseId: string): boolean {
    return (
      this._runningTool?.requestId === requestId &&
      this._runningTool?.toolUseId === toolUseId
    );
  }

  public close(_cancelledByUser: boolean = false): Promise<void> {
    this._abortController?.abort();
    return Promise.resolve();
  }

  public closeAllToolProcesses(): Promise<void> {
    return Promise.resolve();
  }

  abstract factory(preconditionWait: Promise<void>): IToolHost;
}

export interface ITool<T extends ToolType> {
  name: T;
  toolSafety: ToolSafety;
  description: string;
  inputSchemaJson: string;

  /**
   * Returns true iff the tool call is safe to execute without user approval.
   */
  checkToolCallSafe(toolInput: Record<string, unknown>): boolean;

  call(
    toolInput: Record<string, unknown>,
    chatHistory: Exchange[],
    abortSignal: AbortSignal,
    toolUseId: string,
  ): Promise<ToolUseResponse>;
}
