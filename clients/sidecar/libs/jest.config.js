const { pathsToModuleNameMapper } = require("ts-jest");
const { compilerOptions } = require("../../tsconfig");

module.exports = {
  roots: ["<rootDir>"],
  transform: {
    "^.+\\.ts?$": ["ts-jest"],
    // This is needed to support using the generated protobuf js files
    // in our tests.
    "^.+\\.js$": "babel-jest",
  },
  testRegex: "(/__tests__/.*|(\\.|/)(test|spec))\\.test.ts?$",
  moduleFileExtensions: ["ts", "js", "json", "node"],
  collectCoverage: true,
  clearMocks: true,
  resetMocks: false,
  coverageDirectory: "coverage",
  testEnvironment: "node",
  setupFilesAfterEnv: ["<rootDir>/src/__mocks__/setup-jest.ts"],
  // Any local modules that we wish to mock need to be added to this list.
  moduleNameMapper: pathsToModuleNameMapper(compilerOptions.paths, {
    prefix: "<rootDir>/../../",
  }),
};
