/* eslint-disable @typescript-eslint/naming-convention */

module.exports = {
  extends: [
    "eslint:recommended",
    "plugin:@typescript-eslint/recommended-type-checked",
  ],
  env: {
    node: true,
  },
  parser: "@typescript-eslint/parser",
  parserOptions: {
    ecmaVersion: 6,
    sourceType: "module",
    project: true,
    tsconfigRootDir: __dirname,
  },
  plugins: ["@typescript-eslint"],
  rules: {
    // We cannot use console in this library since LSP client uses stdout/stderr
    // for communication with clients.
    "no-console": ["error"],
    "@typescript-eslint/no-unused-vars": ["error", { argsIgnorePattern: "^_" }],
    "@typescript-eslint/no-explicit-any": "off",
    "@typescript-eslint/ban-types": "off",
  },
  ignorePatterns: [
    ".eslintrc.cjs",
    "jest.config.js",
    "babel.config.js",
    "protos/",
  ],
  overrides: [
    {
      extends: ["plugin:jest/recommended"],
      files: ["**/__tests__/**/*.test.ts", "**/__mocks__/**/*"],
      env: {
        jest: true,
      },
      rules: {
        "no-console": "off",
      },
    },
  ],
};
