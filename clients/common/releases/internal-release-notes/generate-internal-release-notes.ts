#!/usr/bin/env node

import { Octokit } from "@octokit/rest";
import { Command } from "commander";

// Initialize the CLI program
const program = new Command();

program
  .name("generate-internal-release-notes")
  .description(
    "Generate internal release notes from GitHub PRs with specific labels",
  )
  .requiredOption(
    "-l, --labels <labels>",
    "GitHub labels to filter PRs by (comma separated)",
  )
  .requiredOption("-c, --commit <commit>", "Commit to release")
  .requiredOption(
    "--type <stable|prerelease>",
    "Release type (stable or prerelease)",
  )
  .requiredOption(
    "--tagPrefix <intellij|vscode|vim>",
    "Tag prefix (i.e. intellij for intellij@1.2.3)",
  )
  .option(
    "-r, --repo <repository>",
    "GitHub repository in format owner/repo",
    "augmentcode/augment",
  )
  .option(
    "-t, --token <token>",
    "GitHub token for API access",
    process.env.GITHUB_TOKEN,
  )
  .option(
    "--previous-release <previous-release>",
    "(Optional) Previous release to compare against. If not provided, will be looked up automatically.",
  )
  .parse(process.argv);

const options = program.opts();

// Main function
async function main() {
  // Validate inputs
  if (!options.token) {
    console.error(
      "Error: GitHub token is required. Set GITHUB_TOKEN environment variable or use --token option.",
    );
    return;
  }

  const [owner, repo] = options.repo.split("/");
  if (!owner || !repo) {
    console.error("Error: Invalid repository format. Use owner/repo format.");
    return;
  }

  // Initialize GitHub client
  const octokit = new Octokit({
    auth: options.token,
  });

  try {
    const labels = options.labels.split(",").map((l: string) => l.trim());
    await verifyLabels(octokit, owner, repo, labels);

    const prevReleaseTagOrCommit =
      options.prevRelease ||
      (await findPreviousReleaseOrOldCommit(
        octokit,
        owner,
        repo,
        options.type !== "stable",
        options.tagPrefix,
      ));
    console.error(`Previous release/commit: ${prevReleaseTagOrCommit}`);

    // prevRelease might be a tag, and we want the commit hash
    const prevCommitDetails = await octokit.rest.repos.getCommit({
      owner,
      repo,
      ref: prevReleaseTagOrCommit,
    });
    const prevReleaseSha = prevCommitDetails.data.sha;

    const commitRange = `${prevReleaseSha}...${options.commit}`;
    console.error(
      `Comparing: https://www.github.com/${owner}/${repo}/compare/${commitRange}`,
    );

    // Get commit comparison to find all commits in the range
    const commits = await getCommitsInRange(
      octokit,
      owner,
      repo,
      prevReleaseSha,
      options.commit,
    );
    if (commits.length === 0) {
      throw new Error(
        `No commits found between ${prevReleaseTagOrCommit} (${prevReleaseSha}) and ${options.commit}.`,
      );
    }

    console.error(
      `There are ${commits.length} commit(s) possibly related to this release.`,
    );
    console.error(`Examining ${commits.length} commit(s) for PRs...`);

    const prsInRelease = await getPRsInRelease(
      octokit,
      owner,
      repo,
      commits,
      labels,
    );
    if (prsInRelease.length === 0) {
      console.error(
        "No PRs found with the specified labels in the given commit range.",
      );
      return;
    }

    // Generate release notes
    generateReleaseNotes({
      prevRelease: prevReleaseTagOrCommit,
      commitRange,
      owner,
      repo,
      prsInRelease,
    });
  } catch (error) {
    console.error("Error:", error instanceof Error ? error.message : error);
  }
}

function generateReleaseNotes({
  prevRelease,
  commitRange,
  owner,
  repo,
  prsInRelease,
}: {
  prevRelease: string;
  commitRange: string;
  owner: string;
  repo: string;
  prsInRelease: PullRequest[];
}) {
  console.log(`### PRs\n`);
  for (const pr of prsInRelease) {
    console.log(
      `- [${pr.title}${pr.user?.login ? ` (@${pr.user?.login})` : ""}](${pr.html_url})`,
    );
  }
  console.log("\n");

  console.log(`### Links\n`);
  console.log(
    `- [All Commits: ${prevRelease.substring(0, 7)}...${options.commit.substring(0, 7)}](https://www.github.com/${owner}/${repo}/compare/${commitRange})\n`,
  );
}

async function verifyLabels(
  octokit: Octokit,
  owner: string,
  repo: string,
  labels: string[],
) {
  for (const label of labels) {
    const l = await octokit.rest.issues.getLabel({
      owner,
      repo,
      name: label,
    });
    if (l.status !== 200) {
      throw new Error(`Label ${label} does not exist.`);
    }
  }
}

async function findPreviousReleaseOrOldCommit(
  octokit: Octokit,
  owner: string,
  repo: string,
  isPrerelease: boolean,
  tagId: string,
): Promise<string> {
  let page = 1;
  let releases;
  const perPage = 30; // Fetch more releases per page to reduce API calls

  while (page === 1 || (releases && releases.data.length === perPage)) {
    releases = await octokit.rest.repos.listReleases({
      owner,
      repo,
      sort: "created",
      per_page: perPage,
      page: page,
    });

    // Look for a prerelease with the specified tag prefix
    for (const release of releases.data) {
      if (release.draft) {
        // Ignore any draft releases
        continue;
      }
      if (release.prerelease !== isPrerelease) {
        // Looking for a different release kind
        continue;
      }
      if (release.tag_name.startsWith(`${tagId}@`)) {
        // Make sure the tag name matches
        console.error(`Found previous release: ${release.tag_name}`);
        return release.target_commitish;
      }
    }

    // Move to the next page
    page++;
  }

  const commitsResponse = await octokit.rest.repos.listCommits({
    owner,
    repo,
    per_page: 100,
    until: new Date().toISOString(),
  });
  if (commitsResponse.data.length === 0) {
    throw new Error(
      `No previous release found with tag prefix ${tagId} and no commits found.`,
    );
  }
  const oldestCommit = commitsResponse.data[commitsResponse.data.length - 1];
  console.error(
    `No previous release found with tag prefix ${tagId}. Using first commit: ${oldestCommit.sha}`,
  );
  return oldestCommit.sha;
}

async function getPRsInRelease(
  octokit: Octokit,
  owner: string,
  repo: string,
  commits: Commit[],
  labels: string[],
) {
  const prsInRelease: PullRequest[] = [];
  const prNumbers: Set<number> = new Set();
  for (let i = 0; i < commits.length; i++) {
    const commit = commits[i];
    console.error(
      `    - (${i + 1} of ${commits.length}) ${commit.sha.substring(0, 7)}`,
    );
    const prsForCommit =
      await octokit.rest.repos.listPullRequestsAssociatedWithCommit({
        owner,
        repo,
        commit_sha: commit.sha,
      });
    for (const pr of prsForCommit.data) {
      if (prNumbers.has(pr.number)) {
        continue;
      }

      for (const label of pr.labels) {
        if (labels.includes(label.name)) {
          console.error(
            `            - ${pr.html_url} - ${pr.title}${pr.user?.login ? ` (@${pr.user?.login})` : ""}`,
          );
          prNumbers.add(pr.number);
          prsInRelease.push(pr);
          break;
        }
      }
    }
  }
  return prsInRelease;
}

/**
 * Retrieves all commits in the range between two commits.
 * Ensures that all commits are retrieved by handling pagination and API limits.
 */
async function getCommitsInRange(
  octokit: Octokit,
  owner: string,
  repo: string,
  baseCommit: string,
  headCommit: string,
): Promise<Commit[]> {
  const commitRange = `${baseCommit}...${headCommit}`;
  const comparison = await octokit.rest.repos.compareCommitsWithBasehead({
    owner,
    repo,
    basehead: commitRange,
  });

  // If the initial comparison has all the commits, return them
  if (comparison.data.total_commits === comparison.data.commits.length) {
    return comparison.data.commits;
  }

  // There are more commits to get (comparison API only returns the  first 250 commits)
  console.error(
    `First comparison returned ${comparison.data.commits.length} commits, but there are ${comparison.data.total_commits} total commits.`,
  );
  let commits = [...comparison.data.commits];
  const totalCommits = comparison.data.total_commits;
  let prevCommitCount = 0;

  while (commits.length < totalCommits && prevCommitCount != commits.length) {
    // Set the current commit count, so on the next loop it can check if the
    // commit count has changed.
    prevCommitCount = commits.length;

    const latestCommitInBatch = commits[0].sha;
    const commitRange = `${baseCommit}...${latestCommitInBatch}`;
    console.error(
      `Comparing: https://www.github.com/${owner}/${repo}/compare/${commitRange}`,
    );

    const latestComparison =
      await octokit.rest.repos.compareCommitsWithBasehead({
        owner,
        repo,
        basehead: commitRange,
      });

    if (latestComparison.data.commits.length > 0) {
      // The last commit will have been included from the previous comparison
      commits = [...latestComparison.data.commits.slice(0, -1), ...commits];
    } else if (latestComparison.data.commits.length === 0) {
      break;
    }
  }

  return commits;
}

main();

type PullRequest = {
  title: string;
  html_url: string;
  user: {
    login: string;
  } | null;
};

type Commit = {
  sha: string;
};
