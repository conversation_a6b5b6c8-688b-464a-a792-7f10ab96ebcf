# Internal Release Notes Generator

A Node.js script that generates internal release notes by fetching GitHub pull
requests with specific labels within a commit range. Uses GitHub's GraphQL
API for efficient querying.

## Installation

```bash
# Install dependencies
pnpm install

# Build TypeScript
pnpm run build
```

## Usage

You can run the script directly with Node.js:

```bash
# Set GitHub token as environment variable
export GITHUB_TOKEN=your_github_token

# Run the script
pnpm run gen-notes \
  --labels="clients,intellij" \
  --tagPrefix "intellij" \
  --type "stable" \
  --commit="b4f1910a5512d73f2cd7073c12ccc2f40be9857d"
```

If you want to specify a previous release to compare against, you can use the
`--previous-release` option:

```bash
pnpm run gen-notes \
  --labels="clients,intellij" \
  --tagPrefix "intellij" \
  --type "stable" \
  --commit="b4f1910a5512d73f2cd7073c12ccc2f40be9857d"
  --previous-release "intellij@0.170.0-stable"
```
