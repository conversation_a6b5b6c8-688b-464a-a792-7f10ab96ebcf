{"name": "internal-release-notes", "private": true, "version": "1.0.0", "description": "Generate internal release notes from GitHub PRs with specific labels", "main": "generate-internal-release-notes.js", "type": "module", "scripts": {"start": "node dist/generate-internal-release-notes.js", "build": "tsc", "gen-notes": "pnpm run build && pnpm run start", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"@octokit/rest": "^20.0.2", "commander": "^12.0.0"}, "devDependencies": {"@types/node": "^22.9.1", "typescript": "^5.6.3"}}