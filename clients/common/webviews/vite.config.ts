import { defineConfig, coverageConfigDefaults, Plugin } from "vitest/config";
import { svelte } from "@sveltejs/vite-plugin-svelte";
import { svelteTesting } from "@testing-library/svelte/vite";
import svg from "@poppanator/sveltekit-svg";

import replace from "@rollup/plugin-replace";
import { resolve, join } from "path";
import { randomBytes } from "crypto";
import terser from "@rollup/plugin-terser";
import json5 from "vite-plugin-json5";
import { inlineMonacoBootstrap } from "./scripts/vite-plugins/inline-monaco-bootstrap";

const isDev = process.argv.includes("--watch") || process.env.AUGMENT_JS_ENV === "development";

/**
 * Detects if running in a Bazel environment using environment variables.
 */
function isInBazel(): boolean {
  // We check for a few different environment variables set by <PERSON><PERSON> to detect
  // if we're running in a Bazel environment.

  // Based on logging actual Bazel executions, these environment variables are available:
  // - BAZEL_TARGET: The full Bazel target being executed (e.g., "//clients/common/webviews:webview-apps")
  // - BAZEL_WORKSPACE: The workspace name (e.g., "_main")
  // - BAZEL_PACKAGE: The package path (e.g., "clients/common/webviews")
  // - RUNFILES: Points to the runfiles directory (rules_js sets this instead of RUNFILES_DIR)
  // - JS_BINARY__RUNFILES: Alternative runfiles path set by js_binary rules
  // - BAZEL_TARGET_CPU: Target CPU architecture (e.g., "darwin_arm64")
  // - BAZEL_COMPILATION_MODE: Compilation mode (e.g., "fastbuild")
  // - BAZEL_BINDIR: Binary output directory
  return (
    !!(
      process.env.BAZEL_TARGET ||
      process.env.BAZEL_WORKSPACE ||
      process.env.RUNFILES ||
      process.env.JS_BINARY__RUNFILES
    ) && !isDev
  );
}

/**
 * Returns the path to the workspace directory ("this" directory),
 * handling both Bazel and development environments.
 *
 * When run through pnpm, uses relative path resolution from __dirname.
 *
 * In Bazel environments, uses process.cwd() which correctly resolves to the sandbox execroot.
 * (We can't use __dirname in this case as it escapes the sandbox and can cause confusing
 * non-hermetic behavior).
 *
 * @see https://github.com/augmentcode/augment/pull/30078
 */
function workspacePath(...pathSegments: string[]): string {
  if (isInBazel()) {
    // In Bazel, use cwd which points to the execroot containing the workspace structure
    return join(process.cwd(), ...pathSegments);
  } else {
    // In development, resolve relative to this file's directory
    return resolve(__dirname, ...pathSegments);
  }
}

const plugins = [
  /**
   * Some libraries provide testing utilities for backend and non-prod environments
   * using process.env.NODE_ENV. This is a common pattern in the JS community, but
   * we want to replace it with a string literal when bundling because most environments
   * will not have access to these variables at runtime.
   *
   * See this for details:
   * - https://atomiks.github.io/tippyjs/v6/faq/#i-m-getting-uncaught-referenceerror-process-is-not-defined
   */
  replace({
    // eslint-disable-next-line @typescript-eslint/naming-convention
    "process.env.NODE_ENV": JSON.stringify(process?.env?.NODE_ENV || "production"),
    preventAssignment: true,
  }),
];

if (!isDev) {
  plugins.push(
    terser({
      format: {
        comments: false,
      },
    }),
  );
}

// https://vitejs.dev/config/
export default defineConfig({
  // Needed so that paths are relative, allowing VSCodes base href to work
  base: "./",
  mode: isDev ? "development" : "production",
  // svelteTesting tries to dynamically resolve vitest in its cleanup --
  // this doesn't work well with bazel, so we disable it here and manually
  // add the vitest cleanup in vitest-setup.ts
  plugins: [
    svelte() as Plugin<any>[],
    json5(),
    svelteTesting({ autoCleanup: false }) as Plugin<any>,
    svg({
      // bazel requires absolute paths for this to work
      includePaths: [workspacePath("src", "design-system", "icons", "fontawesome", "svgs")],
      svgoOptions: {
        multipass: true,
        plugins: [
          {
            name: "preset-default",
            // by default svgo removes the viewBox which prevents svg icons from scaling
            // not a good idea! https://github.com/svg/svgo/pull/1461
            params: { overrides: { removeViewBox: false } },
          },
          { name: "removeAttrs", params: { attrs: "(fill|stroke)" } },
          {
            /**
             * This adds a attribute which we select to automatically style icons
             */
            name: "addAttributesToSVGElement",
            params: {
              attributes: [{ "data-ds-icon": "fa" }],
            },
          },
        ],
      },
    }),
    inlineMonacoBootstrap(),
  ],
  resolve: {
    // These should match the aliases in //clients/tsconfig.json
    alias: {
      /* eslint-disable @typescript-eslint/naming-convention */
      "$common-webviews": workspacePath(),
      $vscode: workspacePath("..", "..", "vscode"),
      //storybook was complaining about this, I think it was confused where to look.
      diff: workspacePath("node_modules", "diff"),
      "@augment-internal/sidecar-libs": workspacePath("..", "..", "sidecar", "libs"),
      $clients: workspacePath("..", ".."),
      /* eslint-enable @typescript-eslint/naming-convention */
    },
  },
  html: {
    cspNonce: `nonce-${randomBytes(16).toString("base64")}`,
  },
  build: {
    // you can specify minifiy:'terser' here but for some reason it was not working
    // for clients/vscode/webviews, so we will keep the same pattern here as well, and
    // we just continue to use the terser plugin. (even with terser installed)
    minify: isDev ? false : undefined,
    rollupOptions: {
      // This is needed to ensure rollup can build the webview when run by bazel
      preserveSymlinks: true,
      input: {
        /* eslint-disable @typescript-eslint/naming-convention */
        "diff-view": "diff-view.html",
        "main-panel": "main-panel.html",
        "next-edit-suggestions": "next-edit-suggestions.html",
        history: "history.html",
        "remote-agent-diff": "remote-agent-diff.html",
        "remote-agent-home": "remote-agent-home.html",
        preference: "preference.html",
        settings: "settings.html",
        rules: "rules.html",
        memories: "memories.html",
        index: "index.html",
        /* eslint-enable @typescript-eslint/naming-convention */
      },
      plugins,
    },
  },
  server: {
    cors: {
      origin: "*",
    },
  },
  test: {
    // Used for setting up the global environment for tests (expects, for example)
    globals: true,
    environment: "jsdom",
    // This file runs before each test
    setupFiles: "./vitest-setup.ts",
    coverage: {
      reporter: ["lcov", "text", "html"],
      exclude: ["**/mocks/**", ...coverageConfigDefaults.exclude],
      include: ["src/**/*.{js,ts,svelte}"],
      all: true,
    },
    // Configure reporters for different output formats
    reporters: [
      "default", // Keep the default console reporter
      // Use Bazel's XML_OUTPUT_FILE environment variable if available, otherwise fallback to test-results.xml
      ["junit", { outputFile: process.env.XML_OUTPUT_FILE }],
    ],
  },
  optimizeDeps: {
    force: true, // Force re-optimization to include fromJson
    include: ["@bufbuild/protobuf"],
  },
});
