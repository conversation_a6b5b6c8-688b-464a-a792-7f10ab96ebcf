<script lang="ts">
  import ColumnLayout from "$common-webviews/mocks/storybook/ColumnLayout.svelte";
  import Fieldset from "$common-webviews/mocks/storybook/Fieldset.svelte";
  import type {
    SeparatorOrientation,
    SeparatorSize,
  } from "$common-webviews/src/design-system/components/SeparatorAugment.svelte";
  import SeparatorAugment from "$common-webviews/src/design-system/components/SeparatorAugment.svelte";

  const sizeVariants: SeparatorSize[] = [1, 2, 3, 4];
  const orientationVariants: SeparatorOrientation[] = ["horizontal", "vertical"];
  const colorVariants = ["--indigo-a6", "--green-a6", "--amber-a6"];
</script>

<ColumnLayout>
  {#each orientationVariants as orientation}
    <Fieldset title="Orientation: '{orientation}'">
      <ColumnLayout>
        {#each sizeVariants as size}
          <SeparatorAugment {size} {orientation} />
        {/each}
      </ColumnLayout>
    </Fieldset>
  {/each}

  <Fieldset title="Use Current Color">
    <ColumnLayout>
      {#each colorVariants as color}
        <div style={`color: var(${color})`}>
          <SeparatorAugment size={3} orientation="horizontal" useCurrentColor={true} />
        </div>
      {/each}
    </ColumnLayout>
  </Fieldset>
</ColumnLayout>
