/* eslint-disable @typescript-eslint/naming-convention */
import type { Meta, StoryObj } from "@storybook/svelte-vite";
import component from "./ToggleButtonStory.svelte";

const meta = {
  title: "design system/ToggleButtonAugment",
  component,
  tags: ["autodocs"],
} satisfies Meta<component>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
};
