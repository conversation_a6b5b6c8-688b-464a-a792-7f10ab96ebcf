<script lang="ts">
  import ToggleButtonAugment from "$common-webviews/src/design-system/components/ToggleButtonAugment.svelte";
  import ColumnLayout from "$common-webviews/mocks/storybook/ColumnLayout.svelte";

  const sizeVariants = [0.5, 1, 2, 3, 4] as const;

  let value1 = "Option 1";
  let value2 = "Small";

  const handleSelect1 = (option: string) => {
    value1 = option;
    console.log("Selected value 1", value1);
    return true;
  };

  const handleSelect2 = (option: string) => {
    value2 = option;
    console.log("Selected value 2", value2);
    return true;
  };
</script>

<ColumnLayout>
  <section>
    <h3>Basic Usage</h3>
    <ToggleButtonAugment
      options={["Option 1", "Option 2", "Option 3"]}
      onSelectOption={handleSelect1}
    />
  </section>

  <section>
    <h3>Sizes</h3>
    <ColumnLayout>
      {#each sizeVariants as size}
        <h4>Size {size}</h4>
        <ToggleButtonAugment
          options={["Small", "Medium", "Large"]}
          onSelectOption={handleSelect2}
          {size}
        />
      {/each}
    </ColumnLayout>
  </section>

  <section>
    <h3>Disabled</h3>
    <ToggleButtonAugment options={["Option 1", "Option 2"]} onSelectOption={() => true} disabled />
  </section>
</ColumnLayout>
