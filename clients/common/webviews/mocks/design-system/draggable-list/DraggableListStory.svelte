<script lang="ts">
  import ColumnLayout from "$common-webviews/mocks/storybook/ColumnLayout.svelte";
  import Fieldset from "$common-webviews/mocks/storybook/Fieldset.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import { writable } from "svelte/store";
  import DraggableListComponent from "./DraggableListComponent.svelte";
  import type { SortableEvent } from "sortablejs";

  // Simple list example
  let simpleItems = writable([
    { uuid: "item-1", text: "Item 1" },
    { uuid: "item-2", text: "Item 2" },
    { uuid: "item-3", text: "Item 3" },
    { uuid: "item-4", text: "Item 4" },
    { uuid: "item-5", text: "Item 5" },
  ]);

  // Nested list example
  let nestedItems = writable([
    {
      uuid: "task-1",
      text: "Task 1",
      children: [
        { uuid: "subtask-1-1", text: "Subtask 1.1" },
        { uuid: "subtask-1-2", text: "Subtask 1.2" },
      ],
    },
    {
      uuid: "task-2",
      text: "Task 2",
      children: [
        { uuid: "subtask-2-1", text: "Subtask 2.1" },
        {
          uuid: "subtask-2-2",
          text: "Subtask 2.2",
          children: [
            { uuid: "subtask-2-2-1", text: "Subtask 2.2.1" },
            { uuid: "subtask-2-2-2", text: "Subtask 2.2.2" },
          ],
        },
      ],
    },
    { uuid: "task-3", text: "Task 3", children: [] },
  ]);

  function handleSimpleListEnd(event: SortableEvent) {
    console.log("Simple list item moved:", event);
    // In a real app, you would update your store here
  }
</script>

<ColumnLayout>
  <TextAugment size={3} weight="medium">Draggable List Examples</TextAugment>
  <TextAugment size={2}>
    These examples demonstrate the DraggableListAugment component with various configurations.
  </TextAugment>

  <Fieldset title="Simple List">
    <DraggableListComponent listId="simple-list" items={$simpleItems} onEnd={handleSimpleListEnd} />
  </Fieldset>

  <Fieldset title="Nested List">
    <DraggableListComponent listId="nested-list" items={$nestedItems} />
  </Fieldset>
</ColumnLayout>
