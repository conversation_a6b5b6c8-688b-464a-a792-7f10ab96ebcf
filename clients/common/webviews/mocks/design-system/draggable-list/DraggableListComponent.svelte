<script lang="ts" context="module">
  export type DraggableListItem = {
    uuid: string;
    text: string;
    children?: DraggableListItem[];
  };
</script>

<script lang="ts">
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import DraggableListAugment from "$common-webviews/src/design-system/components/DraggableListAugment/DraggableListAugment.svelte";
  import GripVertical from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/grip-vertical.svg?component";
  import Trash from "$common-webviews/src/design-system/icons/trash.svelte";
  import DraggableListItemAugment from "$common-webviews/src/design-system/components/DraggableListAugment/DraggableListItemAugment.svelte";
  import type { SortableEvent } from "sortablejs";

  export let listId: string = crypto.randomUUID();
  export let items: DraggableListItem[] = [];
  export let onEnd: (event: SortableEvent) => void = () => {};
</script>

<DraggableListAugment id={listId} {items} {onEnd}>
  <svelte:fragment slot="items" let:items let:onEnd>
    {#each items as item}
      <DraggableListItemAugment id={item.uuid} {item}>
        <GripVertical slot="handle" />
        <TextAugment slot="header-contents" size={2}>{item.text}</TextAugment>
        <IconButtonAugment slot="actions" size={1} variant="ghost" color="neutral">
          <Trash />
        </IconButtonAugment>
        {#if item.children && item.children.length > 0}
          <svelte:self items={item.children} {onEnd} />
        {/if}
      </DraggableListItemAugment>
    {/each}
  </svelte:fragment>
</DraggableListAugment>
