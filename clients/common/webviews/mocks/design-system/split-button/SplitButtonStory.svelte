<script lang="ts">
  import ColumnLayout from "$common-webviews/mocks/storybook/ColumnLayout.svelte";
  import SplitButtonAugment from "$common-webviews/src/design-system/components/SplitButtonAugment";
  import DropdownMenuAugment from "$common-webviews/src/design-system/components/DropdownMenuAugment";
  import Send from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/paper-plane-top.svg?component";
  import Plus from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/plus.svg?component";
  import type {
    ButtonSize,
    ButtonVariant,
    ButtonColor,
  } from "$common-webviews/src/design-system/_primitives/BaseButton.svelte";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";

  const sizes: ButtonSize[] = [1, 2, 3, 4];
  const variants: ButtonVariant[] = ["solid", "soft", "outline", "ghost"];
  const colors: ButtonColor[] = ["accent", "neutral", "error", "success"];

  let selectedModel = "GPT-4";
  const models = ["GPT-4", "GPT-3.5", "Claude", "Gemini"];

  function handleActionSelect(model: string) {
    selectedModel = model;
    console.log("Selected model:", model);
  }

  // Reference to split button for external control
  let splitButtonRef:
    | { requestCloseDropdown: () => void; requestOpenDropdown: () => void }
    | undefined;
</script>

<ColumnLayout>
  <section>
    <h3>Basic Split Button</h3>
    <p>Dropdown automatically closes when any item is clicked:</p>
    <SplitButtonAugment.Root>
      <Send slot="iconLeft" />
      Send Message

      <svelte:fragment slot="dropdown-content">
        <DropdownMenuAugment.Label>Models</DropdownMenuAugment.Label>
        {#each models as model}
          <DropdownMenuAugment.Item
            highlight={model === selectedModel}
            onSelect={() => handleActionSelect(model)}
          >
            {model}
          </DropdownMenuAugment.Item>
        {/each}
      </svelte:fragment>
    </SplitButtonAugment.Root>
  </section>

  <section>
    <h3>Without Dropdown (Single Button Mode)</h3>
    <p>When showDropdown=false, renders as a single button with normal styling:</p>
    <SplitButtonAugment.Root showDropdown={false}>
      <Send slot="iconLeft" />
      Send Message
    </SplitButtonAugment.Root>
  </section>

  <section>
    <h3>Custom Action Button</h3>
    <SplitButtonAugment.Root color="success" variant="soft">
      <Plus slot="iconLeft" />
      New

      <svelte:fragment slot="dropdown-content">
        <DropdownMenuAugment.Label>Actions</DropdownMenuAugment.Label>
        <DropdownMenuAugment.Item onSelect={() => handleActionSelect("file")}>
          New File
        </DropdownMenuAugment.Item>
        <DropdownMenuAugment.Item onSelect={() => handleActionSelect("folder")}>
          New Folder
        </DropdownMenuAugment.Item>
        <DropdownMenuAugment.Item onSelect={() => handleActionSelect("project")}>
          New Project
        </DropdownMenuAugment.Item>
      </svelte:fragment>
    </SplitButtonAugment.Root>
  </section>

  <section>
    <h3>Custom Action Button with custom tooltip</h3>
    <SplitButtonAugment.Root size={1}>
      <TextTooltipAugment slot="button">
        <div slot="content" style="display: flex; flex-direction: column; gap: 0.5rem;">
          <strong>Custom tooltip</strong>
          <span>This tooltip has custom HTML content with multiple lines.</span>
        </div>
        <ButtonAugment size={1}>
          <Plus slot="iconLeft" />
          Hello</ButtonAugment
        >
      </TextTooltipAugment>

      <svelte:fragment slot="dropdown-content">
        <DropdownMenuAugment.Label>Actions</DropdownMenuAugment.Label>
        <DropdownMenuAugment.Item onSelect={() => handleActionSelect("file")}>
          New File
        </DropdownMenuAugment.Item>
      </svelte:fragment>
    </SplitButtonAugment.Root>
  </section>

  <section>
    <h3>Sizes</h3>
    {#each sizes as size}
      <div style="margin-bottom: var(--ds-spacing-2);">
        <SplitButtonAugment.Root {size}>
          Size {size}

          <svelte:fragment slot="dropdown-content">
            <DropdownMenuAugment.Item>Option 1</DropdownMenuAugment.Item>
            <DropdownMenuAugment.Item>Option 2</DropdownMenuAugment.Item>
          </svelte:fragment>
        </SplitButtonAugment.Root>
      </div>
    {/each}
  </section>

  <section>
    <h3>Variants</h3>
    {#each variants as variant}
      <div style="margin-bottom: var(--ds-spacing-2);">
        <SplitButtonAugment.Root {variant}>
          {variant}

          <svelte:fragment slot="dropdown-content">
            <DropdownMenuAugment.Item>Option 1</DropdownMenuAugment.Item>
            <DropdownMenuAugment.Item>Option 2</DropdownMenuAugment.Item>
          </svelte:fragment>
        </SplitButtonAugment.Root>
      </div>
    {/each}
  </section>

  <section>
    <h3>Colors</h3>
    {#each colors as color}
      <div style="margin-bottom: var(--ds-spacing-2);">
        <SplitButtonAugment.Root {color}>
          {color}

          <svelte:fragment slot="dropdown-content">
            <DropdownMenuAugment.Item>Option 1</DropdownMenuAugment.Item>
            <DropdownMenuAugment.Item>Option 2</DropdownMenuAugment.Item>
          </svelte:fragment>
        </SplitButtonAugment.Root>
      </div>
    {/each}
  </section>

  <section>
    <h3>Disabled State</h3>
    <p>Boolean disabled (both button and dropdown disabled):</p>
    <SplitButtonAugment.Root disabled>
      <Send slot="iconLeft" />
      Disabled

      <svelte:fragment slot="dropdown-content">
        <DropdownMenuAugment.Item>Option 1</DropdownMenuAugment.Item>
        <DropdownMenuAugment.Item>Option 2</DropdownMenuAugment.Item>
      </svelte:fragment>
    </SplitButtonAugment.Root>
  </section>

  <section>
    <h3>Selective Disabled State</h3>
    <p>Object disabled (primary disabled, dropdown enabled):</p>
    <SplitButtonAugment.Root disabled={{ primaryDisabled: true, dropdownDisabled: false }}>
      <Send slot="iconLeft" />
      Primary Disabled

      <svelte:fragment slot="dropdown-content">
        <DropdownMenuAugment.Item>Option 1</DropdownMenuAugment.Item>
        <DropdownMenuAugment.Item>Option 2</DropdownMenuAugment.Item>
      </svelte:fragment>
    </SplitButtonAugment.Root>

    <p style="margin-top: var(--ds-spacing-3);">
      Object disabled (primary enabled, dropdown disabled):
    </p>
    <SplitButtonAugment.Root disabled={{ primaryDisabled: false, dropdownDisabled: true }}>
      <Send slot="iconLeft" />
      Dropdown Disabled

      <svelte:fragment slot="dropdown-content">
        <DropdownMenuAugment.Item>Option 1</DropdownMenuAugment.Item>
        <DropdownMenuAugment.Item>Option 2</DropdownMenuAugment.Item>
      </svelte:fragment>
    </SplitButtonAugment.Root>
  </section>

  <section>
    <h3>External Control & Auto-Close</h3>
    <p>
      Demonstrates programmatic control and automatic dropdown closing when any item is clicked:
    </p>
    <div style="margin-bottom: var(--ds-spacing-2);">
      <button on:click={() => splitButtonRef?.requestOpenDropdown()}>Open Dropdown</button>
      <button on:click={() => splitButtonRef?.requestCloseDropdown()}>Close Dropdown</button>
    </div>
    <SplitButtonAugment.Root bind:this={splitButtonRef}>
      <Send slot="iconLeft" />
      Controlled

      <svelte:fragment slot="dropdown-content">
        <DropdownMenuAugment.Label>Actions</DropdownMenuAugment.Label>
        <DropdownMenuAugment.Item onSelect={() => console.log("Action 1 selected")}>
          Action 1 (auto-closes)
        </DropdownMenuAugment.Item>
        <DropdownMenuAugment.Item onSelect={() => console.log("Action 2 selected")}>
          Action 2 (auto-closes)
        </DropdownMenuAugment.Item>
      </svelte:fragment>
    </SplitButtonAugment.Root>
  </section>
</ColumnLayout>
