/* eslint-disable @typescript-eslint/naming-convention */
import type { <PERSON><PERSON>, StoryObj } from "@storybook/svelte-vite";
import component from "./AnimatedNumberIndicatorStory.svelte";
import AnimatedNumberIndicatorVisibilityStory from "./AnimatedNumberIndicatorVisibilityStory.svelte";

const meta = {
  title: "design system/AnimatedNumberIndicator",
  component,
  tags: ["autodocs"],
  parameters: {
    layout: "centered",
    docs: {
      description: {
        component: `
The AnimatedNumberIndicator component displays a number that animates from 0 to the target value when it comes into view.

**Key Features:**
- Uses Intersection Observer to detect when component is visible
- Smooth animation using requestAnimationFrame
- Customizable duration, easing, and visibility thresholds
- Integrates with TextAugment for consistent styling
- One-time animation (won't re-animate on subsequent visibility changes)

**Animation Behavior:**
1. Component starts displaying 0
2. When 25% of component becomes visible (configurable)
3. Waits for user to stop scrolling (100ms by default)
4. Animates smoothly to target value over specified duration

**Storybook Usage:** Most stories use autoStart: true to immediately trigger the animation for demonstration purposes. The VisibilityBased story shows the normal behavior where you need to scroll the component out of view and back in to see the animation.
        `,
      },
    },
  },
  argTypes: {
    value: {
      control: { type: "number", min: 0, max: 10000, step: 1 },
      description: "The target number to animate to",
    },
    size: {
      control: { type: "select" },
      options: [1, 2, 3, 4, 5, 6],
      description: "Text size (matches TextAugment sizes)",
    },
    color: {
      control: { type: "select" },
      options: ["accent", "neutral", "success", "error", "primary", "secondary"],
      description: "Text color (matches TextAugment colors)",
    },
    maxDuration: {
      control: { type: "number", min: 100, max: 5000, step: 100 },
      description: "Maximum animation duration in milliseconds",
    },

    easingType: {
      control: { type: "select" },
      options: ["smoothStep", "easeInOut", "linear"],
      description: "Type of easing function to use for animation",
    },
    autoStart: {
      control: { type: "boolean" },
      description:
        "If true, starts animation immediately without waiting for visibility (useful for Storybook)",
    },
  },
} satisfies Meta<component>;

export default meta;

type Story = StoryObj<typeof meta>;

// Default story with typical values
export const Default: Story = {
  args: {
    value: 42,
    size: 1,
    color: "neutral",
    maxDuration: 1000,
    easingType: "smoothStep",
    autoStart: true,
  },
};

// Large number example
export const LargeNumber: Story = {
  args: {
    value: 1543,
    size: 2,
    color: "accent",
    maxDuration: 1500,
    easingType: "smoothStep",
    autoStart: true,
  },
};

// Fast animation
export const FastAnimation: Story = {
  args: {
    value: 100,
    size: 3,
    color: "success",
    maxDuration: 500,
    easingType: "easeInOut",
    autoStart: true,
  },
};

// Slow animation
export const SlowAnimation: Story = {
  args: {
    value: 25,
    size: 4,
    color: "secondary",
    maxDuration: 3000,
    easingType: "linear",
    autoStart: true,
  },
};

// Small number
export const SmallNumber: Story = {
  args: {
    value: 7,
    size: 5,
    color: "error",
    maxDuration: 800,
    easingType: "smoothStep",
    autoStart: true,
  },
};

// High threshold (needs more of element visible)
export const HighThreshold: Story = {
  args: {
    value: 88,
    size: 2,
    color: "accent",
    maxDuration: 1200,

    easingType: "easeInOut",
    autoStart: true,
  },
  parameters: {
    docs: {
      description: {
        story:
          "This story requires 80% of the component to be visible before animation starts (but autoStart overrides this in Storybook).",
      },
    },
  },
};

// High threshold (needs more of element visible)
export const NoIframeNoAutoStart: Story = {
  args: {
    value: 88,
    size: 2,
    color: "accent",
    maxDuration: 1200,

    easingType: "easeInOut",
    autoStart: false,
  },
  parameters: {
    docs: {
      description: {
        story:
          "This story requires 80% of the component to be visible before animation starts (but autoStart overrides this in Storybook).",
      },
    },
  },
};

// Visibility-based animation (no autoStart)
export const VisibilityBased: Story = {
  args: {
    value: 123,
    size: 2,
    color: "accent",
    maxDuration: 1000,

    easingType: "smoothStep",
    autoStart: false,
  },
  render: (args) => ({
    Component: AnimatedNumberIndicatorVisibilityStory,
    props: args,
  }),
  parameters: {
    layout: "fullscreen",
    docs: {
      description: {
        story:
          "This story uses the normal visibility-based animation. Try scrolling the component out of view and back in to see the animation trigger.",
      },
    },
  },
};
