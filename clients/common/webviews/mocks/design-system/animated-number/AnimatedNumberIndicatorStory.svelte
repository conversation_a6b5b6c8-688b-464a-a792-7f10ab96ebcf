<script lang="ts">
  import AnimatedNumberIndicatorAugment from "$common-webviews/src/design-system/components/AnimatedNumberIndicatorAugment.svelte";
  import type {
    TextSize,
    TextColor,
  } from "$common-webviews/src/design-system/components/TextAugment.svelte";

  export let value: number = 42;
  export let size: TextSize = 1;
  export let color: TextColor = "neutral";
  export let maxDuration: number = 1000;
  export let delay: number = 0;
  export let autoStart: boolean = true;

  // Custom easing function for demonstration
  const smoothStep = (t: number) => t * t * (3 - 2 * t);
  const easeInOut = (t: number) => (t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t);
  const linear = (t: number) => t;

  export let easingType: "smoothStep" | "easeInOut" | "linear" = "smoothStep";

  $: easing =
    easingType === "smoothStep" ? smoothStep : easingType === "easeInOut" ? easeInOut : linear;
</script>

<div
  style="padding: 2rem; display: flex; flex-direction: column; gap: 1rem; align-items: flex-start;"
>
  <div
    style="font-family: var(--font-family-mono); font-size: 0.875rem; color: var(--ds-color-neutral-a11);"
  >
    Target Value: {value}
  </div>

  <div style="font-size: 2rem; font-weight: bold;">
    <AnimatedNumberIndicatorAugment
      {value}
      {size}
      {color}
      {maxDuration}
      {delay}
      {autoStart}
      {easing}
    />
  </div>

  <div style="font-size: 0.75rem; color: var(--ds-color-neutral-a9); max-width: 400px;">
    <strong>Note:</strong> When autoStart is enabled, the animation starts immediately. When disabled,
    the animation triggers when the component comes into view. Try toggling autoStart or scrolling the
    component out of view and back in to see different behaviors.
  </div>
</div>
