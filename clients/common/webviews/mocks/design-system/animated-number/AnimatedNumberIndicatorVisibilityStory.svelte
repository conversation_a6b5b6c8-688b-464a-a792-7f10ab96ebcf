<script lang="ts">
  import AnimatedNumberIndicatorAugment from "$common-webviews/src/design-system/components/AnimatedNumberIndicatorAugment.svelte";
  import {
    type TextSize,
    type TextColor,
  } from "$common-webviews/src/design-system/components/TextAugment.svelte";

  export let value: number = 123;
  export let size: TextSize = 2;
  export let color: TextColor = "accent";
  export let maxDuration: number = 1000;
  export let delay: number = 0;
  export let autoStart: boolean = false;

  // Custom easing function for demonstration
  const smoothStep = (t: number) => t * t * (3 - 2 * t);
  const easeInOut = (t: number) => (t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t);
  const linear = (t: number) => t;

  // Map easing type to function
  export let easingType: "smoothStep" | "easeInOut" | "linear" = "smoothStep";
  const easingMap = { smoothStep, easeInOut, linear };
  $: easing = easingMap[easingType];

  // Reference to the scrolling container to use as IntersectionObserver root
  let scrollContainer: HTMLElement;
</script>

<!-- Create a scrolling container within the iframe -->
<div
  bind:this={scrollContainer}
  style="height: 50vh; width: 100%; overflow: auto; border: 2px solid var(--ds-color-accent-a6);"
>
  <div
    style="height: 300vh; display: flex; flex-direction: column; justify-content: space-around; align-items: center; padding: 50px;"
  >
    <div
      style="padding: 20px; background: rgba(255,255,255,0.1); border-radius: 8px; text-align: center;"
    >
      <p style="margin-bottom: 20px; color: var(--ds-color-neutral-a11);">
        <strong>🎯 Scroll within this container to test visibility!</strong>
      </p>
      <p style="color: var(--ds-color-neutral-a9); font-size: 0.875rem;">
        The component is positioned below. Scroll down to see it appear.
      </p>
    </div>

    <div
      style="padding: 40px; background: rgba(0,255,0,0.1); border: 2px dashed var(--ds-color-success-a6); border-radius: 8px; text-align: center;"
    >
      <p style="color: var(--ds-color-neutral-a9); margin-bottom: 20px;">
        🎬 Component should be here:
      </p>
      <div style="font-size: 2rem; font-weight: bold;">
        <AnimatedNumberIndicatorAugment
          {value}
          {size}
          {color}
          {maxDuration}
          {delay}
          {autoStart}
          {easing}
        />
      </div>
      <p style="margin-top: 20px; color: var(--ds-color-neutral-a9); font-size: 0.875rem;">
        Check console for intersection observer logs
      </p>
    </div>

    <div
      style="padding: 20px; background: rgba(255,255,255,0.1); border-radius: 8px; text-align: center;"
    >
      <p style="color: var(--ds-color-neutral-a9);">🏁 End of scrollable content</p>
    </div>
  </div>
</div>
