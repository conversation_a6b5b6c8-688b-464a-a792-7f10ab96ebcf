<script lang="ts">
  import AnimatedNumberIndicatorAugment from "$common-webviews/src/design-system/components/AnimatedNumberIndicatorAugment.svelte";

  // Test values to demonstrate the algorithm
  const testValues = [1, 2, 5, 10, 25, 50, 100, 250, 500, 1000];

  // Algorithm parameters
  const maxDuration = 500;
  const maxIncrementTime = 16;
  const minDuration = 16;

  // Calculate duration for each test value (same logic as in component)
  function calculateDuration(targetValue: number): number {
    if (targetValue === 0) return minDuration;

    const incrementBasedDuration = targetValue * maxIncrementTime;
    const cappedDuration = Math.min(incrementBasedDuration, maxDuration);

    return Math.max(minDuration, cappedDuration);
  }

  let currentTestIndex = 0;
  let key = 0; // Force re-render of components

  function nextTest() {
    currentTestIndex = (currentTestIndex + 1) % testValues.length;
    key++; // Force component re-render
  }

  function resetTest() {
    key++; // Force component re-render
  }
</script>

<div style="padding: 20px; max-width: 800px;">
  <h2>AnimatedNumberIndicator Duration Algorithm Demo</h2>

  <div
    style="margin: 20px 0; padding: 15px; background: var(--ds-color-neutral-a2); border-radius: 8px;"
  >
    <h3>Algorithm Explanation:</h3>
    <ul style="margin: 10px 0; padding-left: 20px;">
      <li><strong>Max Duration:</strong> {maxDuration}ms (cap for large numbers)</li>
      <li><strong>Max Increment Time:</strong> {maxIncrementTime}ms (time per number increment)</li>
      <li><strong>Min Duration:</strong> {minDuration}ms (absolute minimum)</li>
      <li>
        <strong>Formula:</strong> min(value × maxIncrementTime, maxDuration), but at least minDuration
      </li>
    </ul>
  </div>

  <div style="margin: 20px 0;">
    <button on:click={nextTest} style="margin-right: 10px; padding: 8px 16px;">
      Next Test Value
    </button>
    <button on:click={resetTest} style="padding: 8px 16px;"> Reset Current </button>
  </div>

  <div
    style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 20px; margin: 20px 0;"
  >
    {#each testValues as value, index (value)}
      <div
        style="padding: 15px; border: 2px solid {index === currentTestIndex
          ? 'var(--ds-color-accent-a9)'
          : 'var(--ds-color-neutral-a6)'}; border-radius: 8px; text-align: center;"
      >
        <div style="font-size: 0.875rem; color: var(--ds-color-neutral-a11); margin-bottom: 8px;">
          Value: {value}
        </div>
        <div style="font-size: 2rem; font-weight: bold; margin: 10px 0;">
          {#key key}
            <AnimatedNumberIndicatorAugment
              value={index === currentTestIndex ? value : 0}
              size={2}
              color={index === currentTestIndex ? "accent" : "neutral"}
              autoStart={true}
            />
          {/key}
        </div>
        <div style="font-size: 0.75rem; color: var(--ds-color-neutral-a10);">
          Duration: {calculateDuration(value)}ms
        </div>
        <div style="font-size: 0.75rem; color: var(--ds-color-neutral-a10);">
          Time/increment: {Math.round(calculateDuration(value) / Math.max(1, value))}ms
        </div>
      </div>
    {/each}
  </div>

  <div
    style="margin-top: 30px; padding: 15px; background: var(--ds-color-neutral-a2); border-radius: 8px;"
  >
    <h3>Benefits of this algorithm:</h3>
    <ul style="margin: 10px 0; padding-left: 20px;">
      <li>
        <strong>Small numbers animate very quickly:</strong> Value 2 takes only 32ms (2 × 16ms)
      </li>
      <li>
        <strong>Each increment is fast:</strong> Maximum 16ms per increment ensures snappy feel
      </li>
      <li>
        <strong>Large numbers are capped:</strong> Value 1000+ is capped at 500ms to prevent overly long
        animations
      </li>
      <li>
        <strong>Ultra-fast minimum:</strong> No animation is shorter than 16ms for instant feedback
      </li>
    </ul>
  </div>
</div>
