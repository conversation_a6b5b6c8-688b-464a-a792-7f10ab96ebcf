<script lang="ts">
  import FontAwesomeAugment from "./FontAwesomeAugment.svelte";
  import { icons } from "./fontawesome";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import Fieldset from "$common-webviews/mocks/storybook/Fieldset.svelte";

  // Import some Augment icons for comparison
  import AugmentPlay from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/play-augment.svg?component";
  import AugmentCircle from "$common-webviews/src/design-system/icons/augment/circle.svelte";
</script>

<div class="test-container">
  <Fieldset title="FontAwesome Icon Aspect Ratio Fix Test">
    <div class="test-section">
      <TextAugment size={3} weight="medium">Standalone Icons</TextAugment>
      <div class="icon-comparison">
        <div class="icon-group">
          <TextAugment size={2}>FontAwesome (Variable Aspect Ratios)</TextAugment>
          <div class="icon-row">
            <!-- Wide icon -->
            <div class="icon-container">
              <FontAwesomeAugment content={icons["regular/ellipsis"]} size={5} />
              <TextAugment size={1}>ellipsis (wide)</TextAugment>
            </div>
            <!-- Tall icon -->
            <div class="icon-container">
              <FontAwesomeAugment content={icons["regular/bars"]} size={5} />
              <TextAugment size={1}>bars (tall)</TextAugment>
            </div>
            <!-- Square-ish icon -->
            <div class="icon-container">
              <FontAwesomeAugment content={icons["regular/circle"]} size={5} />
              <TextAugment size={1}>circle (square)</TextAugment>
            </div>
          </div>
        </div>

        <div class="icon-group">
          <TextAugment size={2}>Augment Icons (Square)</TextAugment>
          <div class="icon-row">
            <div class="icon-container">
              <AugmentPlay />
              <TextAugment size={1}>play</TextAugment>
            </div>
            <div class="icon-container">
              <AugmentCircle />
              <TextAugment size={1}>circle</TextAugment>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="test-section">
      <TextAugment size={3} weight="medium">Icons in Buttons</TextAugment>
      <div class="button-comparison">
        <div class="button-group">
          <TextAugment size={2}>FontAwesome in Buttons</TextAugment>
          <div class="button-row">
            <ButtonAugment size={2}>
              <FontAwesomeAugment slot="iconLeft" content={icons["regular/ellipsis"]} size={2} />
              Wide Icon
            </ButtonAugment>
            <ButtonAugment size={2}>
              <FontAwesomeAugment slot="iconLeft" content={icons["regular/bars"]} size={2} />
              Tall Icon
            </ButtonAugment>
            <ButtonAugment size={2}>
              <FontAwesomeAugment slot="iconLeft" content={icons["regular/circle"]} size={2} />
              Square Icon
            </ButtonAugment>
          </div>
        </div>

        <div class="button-group">
          <TextAugment size={2}>Augment in Buttons</TextAugment>
          <div class="button-row">
            <ButtonAugment size={2}>
              <AugmentPlay slot="iconLeft" />
              Play
            </ButtonAugment>
            <ButtonAugment size={2}>
              <AugmentCircle slot="iconLeft" />
              Circle
            </ButtonAugment>
          </div>
        </div>
      </div>
    </div>
  </Fieldset>
</div>

<style>
  .test-container {
    padding: 1rem;
    color: var(--text-color);
  }

  .test-section {
    margin-bottom: 2rem;
  }

  .icon-comparison,
  .button-comparison {
    display: flex;
    gap: 2rem;
    margin-top: 1rem;
  }

  .icon-group,
  .button-group {
    flex: 1;
  }

  .icon-row,
  .button-row {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
    align-items: center;
  }

  .icon-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem;
    border: 1px solid var(--ds-color-n6, #e0e0e0);
    border-radius: 4px;
    min-width: 80px;
  }

  /* Visual debugging - add background to see the container bounds */
  .icon-container {
    background: rgba(0, 0, 255, 0.05);
  }
</style>
