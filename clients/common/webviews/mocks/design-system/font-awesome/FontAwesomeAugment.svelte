<script lang="ts" context="module">
  import { dsColorAttribute } from "$common-webviews/src/design-system/_libs/component-utils";

  export type IconSize = 0 | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9;
  export type IconColor =
    | "accent"
    | "neutral"
    | "error"
    | "success"
    | "primary"
    | "secondary"
    | "current";
</script>

<script lang="ts">
  import SpinnerAugment from "$common-webviews/src/design-system/components/SpinnerAugment.svelte";

  import type { SpinnerSize } from "$common-webviews/src/design-system/components/SpinnerAugment.svelte";
  import { type ComponentType, type SvelteComponent } from "svelte";
  let clazz = "";
  export { clazz as class };
  export let size: IconSize = 2;
  export let color: IconColor = "current";
  export let content: () => Promise<{ default: ComponentType<SvelteComponent> }>;

  // Convert IconSize to SpinnerSize
  function toSpinnerSize(iconSize: IconSize): SpinnerSize {
    if (iconSize <= 1) return 1;
    if (iconSize >= 3) return 3;
    return 2;
  }
</script>

<span class="fa-icon fa-icon--size--{size} {clazz}" {...dsColorAttribute(color)}>
  {#await content()}
    <SpinnerAugment size={toSpinnerSize(size)} />
  {:then { default: component }}
    <svelte:component this={component} />
  {/await}
</span>

<style>
  .fa-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    --icon-color: currentColor;
    & > :global(svg) {
      fill: var(--icon-color, currentColor);
    }

    &[data-ds-color] {
      --icon-color: var(--ds-color-a11, currentColor);
    }
  }

  .fa-icon :global(svg) {
    /* Scale to fit container height, width will scale proportionally */
    height: 100%;
    width: auto;
    max-width: 100%;
  }

  .fa-icon--size--0 {
    width: 12px;
    height: 12px;
  }
  .fa-icon--size--1 {
    width: 14px;
    height: 14px;
  }
  .fa-icon--size--2 {
    width: 16px;
    height: 16px;
  }
  .fa-icon--size--3 {
    width: 18px;
    height: 18px;
  }
  .fa-icon--size--4 {
    width: 20px;
    height: 20px;
  }
  .fa-icon--size--5 {
    width: 24px;
    height: 24px;
  }
  .fa-icon--size--6 {
    width: 28px;
    height: 28px;
  }
  .fa-icon--size--7 {
    width: 32px;
    height: 32px;
  }
  .fa-icon--size--8 {
    width: 36px;
    height: 36px;
  }
  .fa-icon--size--9 {
    width: 40px;
    height: 40px;
  }
</style>
