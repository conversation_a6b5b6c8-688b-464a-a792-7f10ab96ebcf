import type { ComponentType, SvelteComponent } from "svelte";

type IconComponentModule = () => Promise<{ default: ComponentType<SvelteComponent> }>;

export const icons = Object.fromEntries(
  Object.entries(
    import.meta.glob("$common-webviews/src/design-system/icons/fontawesome/svgs/**/*.svg", {
      query: "?component",
      eager: false,
    }),
  ).map(
    ([name, icon]) =>
      [name.replace(/.*\/fontawesome\/svgs\//, "").replace(".svg", ""), icon] as const,
  ),
) as Record<string, IconComponentModule>;

export const names = Object.keys(icons);
