<script lang="ts">
  import { icons, names } from "./fontawesome";
  import FontAwesomeAugment, { type IconSize, type IconColor } from "./FontAwesomeAugment.svelte";
  import Fieldset from "$common-webviews/mocks/storybook/Fieldset.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import CopyButton from "$common-webviews/src/common/components/CopyButton.svelte";
  import Button from "$common-webviews/src/design-system/components/ButtonAugment.svelte";

  console.log(icons);
  export let name: string = "regular/check";
  export let size: IconSize = 5;
  export let color: IconColor = "current";
  export let type: "all" | "size" | "color" | "icon" | "common" = "icon";
  export let count = 100;
  export let offset = 0;
  export let search = "";

  // Common icons to display in the Icons story
  const commonIcons = [
    "regular/check",
    "regular/xmark",
    "regular/circle-info",
    "regular/triangle-exclamation",
    "regular/circle-check",
    "regular/circle-xmark",
    "regular/star",
    "regular/heart",
    "regular/user",
    "regular/gear",
    "regular/code",
    "regular/magnifying-glass",
    "brands/github",
    "brands/twitter",
  ];

  // All available colors
  const allColors: IconColor[] = [
    "accent",
    "neutral",
    "error",
    "success",
    "primary",
    "secondary",
    "current",
  ];
  // All available sizes
  const allSizes: IconSize[] = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9];
  $: {
    //when search changes offset goes back to 0
    search;
    offset = 0;
  }
  $: se = new RegExp(search.replaceAll(/[*]/g, ".+?"), "i");
  $: filteredNames = names.filter((v) => se.test(v));
  $: showNames = filteredNames.slice(offset, offset + count);
  function fnameToImport(name: string) {
    return (
      name
        .split(/[-/]/)
        .map((v) => v[0].toLocaleUpperCase() + v.slice(1))
        .join("") + "Icon"
    );
  }
</script>

<div class="story-container">
  {#if type === "all"}
    <!-- All showcase -->
    <Fieldset title="All Icons">
      <div class="pagination">
        <input type="text" bind:value={search} placeholder="Search" />
        <Button
          disabled={offset === 0}
          size={1}
          on:click={() => (offset = Math.max(offset - count, 0))}
          >Prev {offset <= count ? "" : offset - count}</Button
        >
        <Button
          disabled={offset >= filteredNames.length - count}
          size={1}
          on:click={() => (offset = Math.min(offset + count, filteredNames.length - count))}
          >Next {offset + count}</Button
        >
        <select bind:value={size}>
          {#each allSizes as sizeOption}
            <option value={sizeOption}>{sizeOption}</option>
          {/each}
        </select>
        <select bind:value={color}>
          {#each allColors as colorOption}
            <option value={colorOption}>{colorOption}</option>
          {/each}
        </select>
      </div>
      <div class="all-grid">
        {#each showNames as name}
          <div class="item">
            <FontAwesomeAugment {size} {color} content={icons[name]} />
            <TextAugment size={1}>{name}</TextAugment>
            <CopyButton
              text={`import ${fnameToImport(name)} from "$common-webviews/src/design-system/icons/fontawesome/svgs/${name}.svg?component";`}
            />
          </div>
        {/each}
      </div>
    </Fieldset>
  {:else if type === "size"}
    <!-- Size showcase -->
    <Fieldset title="Icon Sizes">
      <div class="size-grid">
        {#each allSizes as sizeOption}
          <div class="item">
            <FontAwesomeAugment content={icons[name]} size={sizeOption} {color} />
            <TextAugment size={1}>Size {sizeOption}</TextAugment>
          </div>
        {/each}
      </div>
    </Fieldset>
  {:else if type === "color"}
    <!-- Color showcase -->
    <Fieldset title="Icon Colors">
      <div class="color-grid">
        {#each allColors as colorOption}
          <div class="item">
            <FontAwesomeAugment content={icons[name]} {size} color={colorOption} />
            <TextAugment size={1}>{colorOption}</TextAugment>
          </div>
        {/each}
      </div>
    </Fieldset>
  {:else if type === "common"}
    <!-- Icon showcase -->
    <Fieldset title="Common Icons">
      <div class="icon-grid">
        {#each commonIcons as iconName}
          <div class="item">
            <FontAwesomeAugment content={icons[name]} {size} {color} />
            <TextAugment size={1}>{iconName}</TextAugment>
          </div>
        {/each}
      </div>
    </Fieldset>
  {:else}
    <!-- Default single icon display -->
    <Fieldset title="FontAwesome Icon">
      <div class="single-icon">
        <FontAwesomeAugment content={icons[name]} {size} {color} />
        <div class="icon-details">
          <TextAugment size={2}>Name: {name}</TextAugment>
          <TextAugment size={2}>Size: {size}</TextAugment>
          <TextAugment size={2}>Color: {color}</TextAugment>
        </div>
      </div>
    </Fieldset>
  {/if}
</div>

<style>
  .story-container {
    padding: 1rem;
    color: var(--text-color);
  }

  .single-icon {
    display: flex;
    align-items: center;
    gap: 1rem;
  }

  .icon-details {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }
  .all-grid,
  .size-grid,
  .color-grid,
  .icon-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 1rem;
  }
  .item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem;
    border: 1px solid var(--ds-color-n6, #e0e0e0);
    border-radius: 4px;
    position: relative;
  }
  .item :global(.c-copy-button) {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    display: inline;
    opacity: 0;
    transition: opacity 0.3s ease;
  }
  .item:hover :global(.c-copy-button) {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    display: inline;
    opacity: 0.99;
  }

  .pagination {
    display: flex;
    gap: 1rem;
    padding: 1rem;
  }
</style>
