<script lang="ts">
  import TextAreaAugment, {
    type TextFieldVariant,
    type TextFieldSize,
    type TextFieldColor,
  } from "$common-webviews/src/design-system/components/TextAreaAugment.svelte";

  const variants: TextFieldVariant[] = ["classic", "surface", "soft"];
  const colors: TextFieldColor[] = ["accent", "neutral", "error"];
  const sizes: TextFieldSize[] = [1, 2, 3];

  // TextArea specific props
  const rows = [2, 4, 6];
  const resizeOptions = ["none", "both", "horizontal", "vertical"] as const;
</script>

<section>
  <h2>States</h2>
  <div class="l-table-wrapper">
    <table class="l-table">
      <thead>
        <tr>
          <th></th>
          <th>Empty</th>
          <th>With Value</th>
          <th>Disabled<br />(Empty)</th>
          <th>Disabled<br />(With Value)</th>
          <th>Read Only<br />(Empty)</th>
          <th>Read Only<br />(With Value)</th>
        </tr>
      </thead>

      <tbody>
        {#each variants as variant}
          <tr>
            <th>{variant}</th>
            <td>
              <TextAreaAugment {variant} placeholder="Placeholder" rows="3" />
            </td>
            <td>
              <TextAreaAugment {variant} placeholder="Placeholder" value="Example value" rows="3" />
            </td>
            <td>
              <TextAreaAugment {variant} placeholder="Placeholder" disabled rows="3" />
            </td>
            <td>
              <TextAreaAugment
                {variant}
                placeholder="Placeholder"
                value="Example value"
                disabled
                rows="3"
              />
            </td>
            <td>
              <TextAreaAugment {variant} placeholder="Placeholder" readonly rows="3" />
            </td>
            <td>
              <TextAreaAugment
                {variant}
                placeholder="Placeholder"
                value="Example value"
                readonly
                rows="3"
              />
            </td>
          </tr>
        {/each}
      </tbody>
    </table>
  </div>
</section>

<section>
  <h2>Colors</h2>
  <div class="l-table-wrapper">
    <table class="l-table">
      <thead>
        <tr>
          <th></th>
          {#each variants as variant}
            <th>{variant}</th>
          {/each}
        </tr>
      </thead>

      <tbody>
        {#each colors as color}
          <tr>
            <th>{color}</th>
            {#each variants as variant}
              <td>
                <TextAreaAugment {variant} {color} placeholder="Placeholder" rows="3" />
              </td>
            {/each}
          </tr>
        {/each}
      </tbody>
    </table>
  </div>
</section>

<section>
  <h2>Sizes</h2>
  <div class="l-table-wrapper">
    <table class="l-table">
      <thead>
        <tr>
          <th></th>
          {#each variants as variant}
            <th>{variant}</th>
          {/each}
        </tr>
      </thead>

      <tbody>
        {#each sizes as size}
          <tr>
            <th>{size}</th>
            {#each variants as variant}
              <td>
                <TextAreaAugment {variant} {size} placeholder="Placeholder" rows="3" />
              </td>
            {/each}
          </tr>
        {/each}
      </tbody>
    </table>
  </div>
</section>

<section>
  <h2>Rows</h2>
  <div class="l-table-wrapper">
    <table class="l-table">
      <thead>
        <tr>
          <th></th>
          {#each variants as variant}
            <th>{variant}</th>
          {/each}
        </tr>
      </thead>

      <tbody>
        {#each rows as row}
          <tr>
            <th>{row} rows</th>
            {#each variants as variant}
              <td>
                <TextAreaAugment {variant} placeholder="Placeholder" rows={row} />
              </td>
            {/each}
          </tr>
        {/each}
      </tbody>
    </table>
  </div>
</section>

<section>
  <h2>Resize Options</h2>
  <div class="l-table-wrapper">
    <table class="l-table">
      <thead>
        <tr>
          <th></th>
          {#each variants as variant}
            <th>{variant}</th>
          {/each}
        </tr>
      </thead>

      <tbody>
        {#each resizeOptions as resize}
          <tr>
            <th>{resize}</th>
            {#each variants as variant}
              <td>
                <TextAreaAugment {variant} placeholder="Placeholder" rows="3" {resize} />
              </td>
            {/each}
          </tr>
        {/each}
      </tbody>
    </table>
  </div>
</section>

<style>
  .l-table-wrapper {
    overflow-x: auto;
  }
  .l-table td {
    min-width: 120px;
  }

  section {
    padding: 12px;
  }
</style>
