import type { <PERSON>a, StoryObj } from "@storybook/svelte-vite";
import TooltipStory from "./TooltipStory.svelte";

const meta = {
  title: "design system/primitives/Tooltip",
  component: TooltipStory,
  tags: ["autodocs"],
  parameters: {
    layout: "centered",
  },
} satisfies Meta<TooltipStory>;

export default meta;
type Story = StoryObj<typeof meta>;

// eslint-disable-next-line @typescript-eslint/naming-convention
export const Example: Story = {
  args: {},
};
