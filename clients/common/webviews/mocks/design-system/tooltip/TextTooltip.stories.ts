/* eslint-disable @typescript-eslint/naming-convention */
import type { Meta, StoryObj } from "@storybook/svelte-vite";
import TextTooltipStory from "./TextTooltipStory.svelte";

const meta = {
  title: "design system/TextTooltipAugment",
  component: TextTooltipStory,
  tags: ["autodocs"],
  parameters: {
    layout: "centered",
  },
  argTypes: {
    side: {
      control: { type: "select" },
      options: ["top", "right", "bottom", "left"],
      description: "The side of the tooltip relative to the trigger element",
      defaultValue: "top",
    },
  },
} satisfies Meta<TextTooltipStory>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Top: Story = {
  args: {
    side: "top",
  },
};

export const Right: Story = {
  args: {
    side: "right",
  },
};
