<script lang="ts">
  import ColumnLayout from "$common-webviews/mocks/storybook/ColumnLayout.svelte";
  import Fieldset from "$common-webviews/mocks/storybook/Fieldset.svelte";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import type { TooltipContentSide } from "$common-webviews/src/design-system/_primitives/TooltipAugment/types";

  const tooltipConfigs = [
    { content: "Some short content" },
    {
      content:
        "This is a tooltip with a long content that should wrap. This is a tooltip with a long content that should wrap.",
    },
    {
      content: "This is a tooltip with a long content that should wrap with max width",
      maxWidth: "100px",
    },
    { content: "Custom width", width: "200px" },
    { content: "Custom max width", maxWidth: "100px" },
    { content: "Custom min width", minWidth: "150px" },
  ] as const;

  const delayDurations = [undefined, 10, 700];
  export let side: TooltipContentSide = "top";
</script>

{#each delayDurations as delayDurationMs}
  <Fieldset title={`Text Tooltip - delayDurationMs=${delayDurationMs ?? "default"}, side=${side}`}>
    <div style="padding: 2rem 10rem;">
      <ColumnLayout>
        {#each tooltipConfigs as config}
          <TextTooltipAugment {...config} {side} {delayDurationMs}>
            <ButtonAugment>Hover me</ButtonAugment>
          </TextTooltipAugment>
        {/each}
      </ColumnLayout>
    </div>
  </Fieldset>
{/each}
