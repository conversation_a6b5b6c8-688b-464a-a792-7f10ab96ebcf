<script lang="ts">
  import Tooltip from "$common-webviews/src/design-system/_primitives/TooltipAugment";
  import ColumnLayout from "$common-webviews/mocks/storybook/ColumnLayout.svelte";
  import Fieldset from "$common-webviews/mocks/storybook/Fieldset.svelte";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import CardAugment from "$common-webviews/src/design-system/components/CardAugment.svelte";
  import { TooltipTriggerOn } from "$common-webviews/src/design-system/_primitives/TooltipAugment/types";

  const tooltipConfigs = [
    {
      title: "Tooltip primitive -- default trigger types",
      triggerOn: undefined,
      buttonText: "Hover me or click me",
      contentText: "Default content",
    },
    {
      title: "Tooltip primitive -- only hover",
      triggerOn: [TooltipTriggerOn.Hover],
      buttonText: "Hover me",
      contentText: "Hover content",
    },
    {
      title: "Tooltip primitive -- only click",
      triggerOn: [TooltipTriggerOn.Click],
      buttonText: "Click me",
      contentText: "Click content",
    },
  ];

  const sides = ["top", "right", "bottom", "left"] as const;
  const aligns = ["start", "center", "end"] as const;
</script>

<ColumnLayout>
  {#each tooltipConfigs as config}
    <Fieldset title={config.title}>
      <table style="border-collapse: separate; border-spacing: 1rem;">
        <thead>
          <tr>
            <th></th>
            {#each aligns as align}
              <th>{align}</th>
            {/each}
          </tr>
        </thead>
        <tbody>
          {#each sides as side}
            <tr>
              <th>{side}</th>
              {#each aligns as align}
                <td>
                  <Tooltip.Root triggerOn={config.triggerOn}>
                    <Tooltip.Trigger>
                      <ButtonAugment>{config.buttonText}</ButtonAugment>
                    </Tooltip.Trigger>
                    <Tooltip.Content {side} {align}>
                      <CardAugment>{config.contentText}</CardAugment>
                    </Tooltip.Content>
                  </Tooltip.Root>
                </td>
              {/each}
            </tr>
          {/each}
        </tbody>
      </table>
    </Fieldset>
  {/each}
</ColumnLayout>
