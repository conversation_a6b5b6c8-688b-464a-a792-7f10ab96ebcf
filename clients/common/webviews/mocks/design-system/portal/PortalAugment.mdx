import { <PERSON><PERSON>, <PERSON><PERSON>, Story } from '@storybook/addon-docs/blocks';
import * as PortalStories from './PortalAugment.stories';

# PortalAugment

A Svelte component for rendering content outside of its parent component's DOM hierarchy. Useful for modals, tooltips, dropdowns, and other overlay content that needs to escape parent containers with overflow hidden or z-index stacking contexts.

## Basic Usage

The simplest way to use PortalAugment is without any props, which will render content to `document.body`:

```html
<PortalAugment>
  <div class="modal">Modal content here</div>
</PortalAugment>
```

<Canvas of={PortalStories.BasicPortal} />

The above example demonstrates the basic portal functionality where content is rendered to document.body by default.

## Portal to Custom Element

You can specify a custom target element by passing an HTMLElement reference:

```html
<script>
  let targetElement;
</script>

<div bind:this={targetElement}>Target container</div>

<PortalAugment target={targetElement}>
  <div>Content rendered to targetElement</div>
</PortalAugment>
```

<Canvas of={PortalStories.CustomTarget} />

## Portal to CSS Selector

You can also use a CSS selector string to specify the target:

```html
<div id="modal-root"></div>

<PortalAugment target="#modal-root">
  <div>Content rendered to #modal-root</div>
</PortalAugment>
```

## Multiple Portals

You can have multiple portals rendering to different targets simultaneously:

<Canvas of={PortalStories.MultiplePortals} />

## Using the Portal Action Directly

For more advanced use cases, you can use the portal action directly:

```html
<script>
  import { portal } from "$common-webviews/src/design-system/components/PortalAugment.svelte";
</script>

<div use:portal>Content rendered to body</div>
<div use:portal={'#custom-target'}>Content rendered to #custom-target</div>
<div use:portal={document.getElementById('target')}>Content rendered to element</div>
```

## Use Cases

- **Modals & Overlays:** Render modal content outside parent containers to avoid z-index issues
- **Tooltips:** Position tooltips at the document level for proper layering
- **Dropdowns:** Escape overflow:hidden containers for proper dropdown positioning
- **Notifications:** Render notifications at a consistent location in the DOM
- **Context Menus:** Position context menus outside component boundaries
