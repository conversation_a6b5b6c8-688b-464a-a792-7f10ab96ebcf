<script lang="ts">
  import PortalAugment from "$common-webviews/src/design-system/components/PortalAugment.svelte";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import ColumnLayout from "$common-webviews/mocks/storybook/ColumnLayout.svelte";
  import Fieldset from "$common-webviews/mocks/storybook/Fieldset.svelte";

  let showBodyPortal = false;
</script>

<ColumnLayout>
  <h1>PortalAugment - Body Portal</h1>

  <p>
    <TextAugment size={1} color="secondary">
      This example demonstrates the basic usage of PortalAugment, where content is rendered to
      document.body (default behavior).
    </TextAugment>
  </p>

  <Fieldset title="Basic Usage - Portal to Body">
    <p>Content is rendered to document.body (default behavior)</p>
    <ButtonAugment on:click={() => (showBodyPortal = !showBodyPortal)}>
      {showBodyPortal ? "Hide" : "Show"} Body Portal
    </ButtonAugment>
  </Fieldset>

  <Fieldset title="Portal Usage Notes">
    <ul>
      <li>Default target is "body" (document.body)</li>
      <li>Content is moved to the target element in the DOM</li>
      <li>Useful for modals, tooltips, and overlays that need to escape parent containers</li>
      <li>Content is automatically cleaned up when component is destroyed</li>
    </ul>
  </Fieldset>
</ColumnLayout>

<!-- Portal to body (default) -->
{#if showBodyPortal}
  <PortalAugment>
    <div class="portal-content body-portal">
      <TextAugment size={2} weight="bold">Portal to Body</TextAugment>
      <TextAugment size={1}>This content is rendered to document.body</TextAugment>
      <ButtonAugment on:click={() => (showBodyPortal = false)}>Close</ButtonAugment>
    </div>
  </PortalAugment>
{/if}

<style>
  .portal-content {
    padding: var(--ds-spacing-3);
    border-radius: var(--ds-radius-2);
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-2);
    max-width: 300px;
  }

  .body-portal {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: var(--ds-color-neutral-1);
    border: 1px solid var(--ds-color-neutral-6);
    box-shadow: var(--ds-shadow-4);
    z-index: 1000;
  }

  ul {
    margin: 0;
    padding-left: var(--ds-spacing-4);
  }

  li {
    margin-bottom: var(--ds-spacing-1);
  }
</style>
