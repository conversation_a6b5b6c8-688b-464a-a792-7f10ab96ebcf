/* eslint-disable @typescript-eslint/naming-convention */
import type { Meta, StoryObj } from "@storybook/svelte-vite";
import BodyPortalComponent from "./PortalBodyStory.svelte";
import CustomTargetComponent from "./PortalCustomTargetStory.svelte";
import MultiplePortalComponent from "./PortalMultipleStory.svelte";
import mdxDocs from "./PortalAugment.mdx";

const meta = {
  title: "design system/PortalAugment",
  component: BodyPortalComponent,
  tags: ["autodocs"],
  parameters: {
    docs: {
      page: mdxDocs,
    },
  },
} satisfies Meta<BodyPortalComponent>;

export default meta;

type Story = StoryObj<typeof meta>;

// Basic Portal Story - renders to document.body
export const BasicPortal: Story = {
  render: () => ({
    Component: BodyPortalComponent,
  }),
};

// Custom Target Portal Story
export const CustomTarget: Story = {
  render: () => ({
    Component: CustomTargetComponent,
  }),
};

// Multiple Portals Story
export const MultiplePortals: Story = {
  render: () => ({
    Component: MultiplePortalComponent,
  }),
};
