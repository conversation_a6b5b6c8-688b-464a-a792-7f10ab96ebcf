<script lang="ts">
  import PortalAugment from "$common-webviews/src/design-system/components/PortalAugment.svelte";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import ColumnLayout from "$common-webviews/mocks/storybook/ColumnLayout.svelte";
  import Fieldset from "$common-webviews/mocks/storybook/Fieldset.svelte";

  let showMultiplePortals = false;

  // Create custom target container
  let anotherTarget: HTMLElement;

  // Computed property to handle the target type
  $: anotherTargetForPortal = anotherTarget as any;
</script>

<ColumnLayout>
  <h1>PortalAugment - Multiple Portals</h1>

  <p>
    <TextAugment size={1} color="secondary">
      This example demonstrates how to use multiple PortalAugment components simultaneously,
      rendering content to different target elements.
    </TextAugment>
  </p>

  <Fieldset title="Multiple Portals">
    <p>Multiple portals can be rendered to different targets</p>
    <ButtonAugment on:click={() => (showMultiplePortals = !showMultiplePortals)}>
      {showMultiplePortals ? "Hide" : "Show"} Multiple Portals
    </ButtonAugment>

    <div class="targets-container">
      <div class="target-box" bind:this={anotherTarget}>
        <TextAugment size={1} color="neutral">Target A</TextAugment>
      </div>
      <div id="target-b" class="target-box">
        <TextAugment size={1} color="neutral">Target B (CSS Selector)</TextAugment>
      </div>
    </div>
  </Fieldset>

  <Fieldset title="Portal Usage Notes">
    <ul>
      <li>Multiple portals can be active simultaneously</li>
      <li>Each portal can target different elements</li>
      <li>Can mix HTMLElement references and CSS selector strings</li>
      <li>Content is automatically cleaned up when components are destroyed</li>
    </ul>
  </Fieldset>
</ColumnLayout>

<!-- Multiple portals -->
{#if showMultiplePortals}
  <PortalAugment target={anotherTargetForPortal}>
    <div class="portal-content multiple-portal">
      <TextAugment size={1} weight="bold">Portal A</TextAugment>
    </div>
  </PortalAugment>

  <PortalAugment target="#target-b">
    <div class="portal-content multiple-portal">
      <TextAugment size={1} weight="bold">Portal B</TextAugment>
    </div>
  </PortalAugment>
{/if}

<style>
  .targets-container {
    display: flex;
    gap: var(--ds-spacing-3);
    margin-top: var(--ds-spacing-2);
  }

  .target-box {
    border: 2px dashed var(--ds-color-accent-6);
    border-radius: var(--ds-radius-2);
    padding: var(--ds-spacing-2);
    min-height: 50px;
    min-width: 120px;
    background-color: var(--ds-color-accent-2);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
  }

  .portal-content {
    padding: var(--ds-spacing-3);
    border-radius: var(--ds-radius-2);
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-2);
    max-width: 300px;
  }

  .multiple-portal {
    background-color: var(--ds-color-info-3);
    border: 1px solid var(--ds-color-info-6);
    text-align: center;
  }

  ul {
    margin: 0;
    padding-left: var(--ds-spacing-4);
  }

  li {
    margin-bottom: var(--ds-spacing-1);
  }
</style>
