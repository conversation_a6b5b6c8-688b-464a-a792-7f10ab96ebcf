<script lang="ts">
  import PortalAugment from "$common-webviews/src/design-system/components/PortalAugment.svelte";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import ColumnLayout from "$common-webviews/mocks/storybook/ColumnLayout.svelte";
  import Fieldset from "$common-webviews/mocks/storybook/Fieldset.svelte";

  let showCustomPortal = false;

  // Create custom target container
  let customTarget: HTMLElement;

  // Computed property to handle the target type
  $: customTargetForPortal = customTarget as any;
</script>

<ColumnLayout>
  <h1>PortalAugment - Custom Target</h1>

  <p>
    <TextAugment size={1} color="secondary">
      This example demonstrates how to use PortalAugment with a custom target element, where content
      is rendered to a specific DOM element.
    </TextAugment>
  </p>

  <Fieldset title="Custom Target - Portal to Specific Element">
    <p>Content is rendered to a custom target element</p>
    <ButtonAugment on:click={() => (showCustomPortal = !showCustomPortal)}>
      {showCustomPortal ? "Hide" : "Show"} Custom Portal
    </ButtonAugment>

    <div class="custom-target" bind:this={customTarget}>
      <TextAugment size={1} color="neutral">Custom Portal Target Container</TextAugment>
    </div>
  </Fieldset>

  <Fieldset title="Portal Usage Notes">
    <ul>
      <li>Can accept HTMLElement reference or CSS selector string</li>
      <li>Content is moved to the target element in the DOM</li>
      <li>Useful for rendering content in specific containers</li>
      <li>Content is automatically cleaned up when component is destroyed</li>
    </ul>
  </Fieldset>
</ColumnLayout>

<!-- Portal to custom element -->
{#if showCustomPortal && customTarget}
  <PortalAugment target={customTargetForPortal}>
    <div class="portal-content custom-portal">
      <TextAugment size={1} weight="bold">Custom Portal</TextAugment>
      <TextAugment size={1}>Rendered to custom target</TextAugment>
    </div>
  </PortalAugment>
{/if}

<style>
  .custom-target {
    border: 2px dashed var(--ds-color-neutral-6);
    border-radius: var(--ds-radius-2);
    padding: var(--ds-spacing-3);
    margin-top: var(--ds-spacing-2);
    min-height: 60px;
    background-color: var(--ds-color-neutral-2);
    position: relative;
  }

  .portal-content {
    padding: var(--ds-spacing-3);
    border-radius: var(--ds-radius-2);
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-2);
    max-width: 300px;
  }

  .custom-portal {
    background-color: var(--ds-color-success-3);
    border: 1px solid var(--ds-color-success-6);
  }

  ul {
    margin: 0;
    padding-left: var(--ds-spacing-4);
  }

  li {
    margin-bottom: var(--ds-spacing-1);
  }
</style>
