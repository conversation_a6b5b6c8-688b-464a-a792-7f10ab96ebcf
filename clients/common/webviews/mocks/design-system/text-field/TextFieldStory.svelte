<script lang="ts">
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import TextFieldAugment from "$common-webviews/src/design-system/components/TextFieldAugment.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import {
    type TextFieldVariant,
    type TextFieldSize,
    type TextFieldColor,
  } from "$common-webviews/src/design-system/_primitives/BaseTextInput.svelte";
  import InfoCircled from "$common-webviews/src/design-system/icons/info-circled.svelte";
  import type { HTMLInputTypeAttribute } from "svelte/elements";

  const variants: TextFieldVariant[] = ["classic", "surface", "soft"];
  const colors: TextFieldColor[] = ["accent", "neutral", "error"];
  const sizes: TextFieldSize[] = [1, 2, 3];

  const types: HTMLInputTypeAttribute[] = ["text", "number", "password"];
</script>

<section>
  <h2>States</h2>
  <div class="l-table-wrapper">
    <table class="l-table">
      <thead>
        <tr>
          <th></th>
          <th>Empty</th>
          <th>With Value</th>
          <th>Disabled<br />(Empty)</th>
          <th>Disabled<br />(With Value)</th>
          <th>Read Only<br />(Empty)</th>
          <th>Read Only<br />(With Value)</th>
        </tr>
      </thead>

      <tbody>
        {#each variants as variant}
          <tr>
            <th>{variant}</th>
            <td>
              <TextFieldAugment {variant} placeholder="Placeholder" />
            </td>
            <td>
              <TextFieldAugment {variant} placeholder="Placeholder" value="Example value" />
            </td>
            <td>
              <TextFieldAugment {variant} placeholder="Placeholder" disabled />
            </td>
            <td>
              <TextFieldAugment
                {variant}
                placeholder="Placeholder"
                value="Example value"
                disabled
              />
            </td>
            <td>
              <TextFieldAugment {variant} placeholder="Placeholder" readonly />
            </td>
            <td>
              <TextFieldAugment
                {variant}
                placeholder="Placeholder"
                value="Example value"
                readonly
              />
            </td>
          </tr>
        {/each}
      </tbody>
    </table>
  </div>
</section>

<section>
  <h2>Colors</h2>
  <div class="l-table-wrapper">
    <table class="l-table">
      <thead>
        <tr>
          <th></th>
          {#each variants as variant}
            <th>{variant}</th>
          {/each}
        </tr>
      </thead>

      <tbody>
        {#each colors as color}
          <tr>
            <th>{color}</th>
            {#each variants as variant}
              <td>
                <TextFieldAugment {variant} {color} placeholder="Placeholder" />
              </td>
            {/each}
          </tr>
        {/each}
      </tbody>
    </table>
  </div>
</section>

<section>
  <h2>Sizes</h2>
  <div class="l-table-wrapper">
    <table class="l-table">
      <thead>
        <tr>
          <th></th>
          {#each variants as variant}
            <th>{variant}</th>
          {/each}
        </tr>
      </thead>

      <tbody>
        {#each sizes as size}
          <tr>
            <th>{size}</th>
            {#each variants as variant}
              <td>
                <TextFieldAugment {variant} {size} placeholder="Placeholder">
                  <InfoCircled slot="iconLeft" />
                  <svelte:fragment slot="iconRight">
                    {#if size !== 1}
                      <ButtonAugment variant="ghost" {size}>
                        <InfoCircled slot="iconLeft" />
                      </ButtonAugment>
                    {/if}
                  </svelte:fragment>
                </TextFieldAugment>
              </td>
            {/each}
          </tr>
        {/each}
      </tbody>
    </table>
  </div>
</section>

<section>
  <h2>Input Types</h2>
  <div class="l-table-wrapper">
    <table class="l-table">
      <thead>
        <tr>
          <th></th>
          {#each variants as variant}
            <th>{variant}</th>
          {/each}
        </tr>
      </thead>

      <tbody>
        {#each types as type}
          <tr>
            <th>{type}</th>
            {#each variants as variant}
              <td>
                <TextFieldAugment {variant} {type} placeholder="Placeholder" />
              </td>
            {/each}
          </tr>
        {/each}
      </tbody>
    </table>
  </div>
</section>

<section>
  <h2>Slots</h2>
  <div class="l-table-wrapper">
    <table class="l-table">
      <thead>
        <tr>
          <th></th>
          {#each variants as variant}
            <th>{variant}</th>
          {/each}
        </tr>
      </thead>

      <tbody>
        <tr>
          <th>Start Only</th>
          {#each variants as variant}
            <td>
              <TextFieldAugment {variant} placeholder="Placeholder">
                <InfoCircled slot="iconLeft" />
              </TextFieldAugment>
            </td>
          {/each}
        </tr>
        <tr>
          <th>End Only</th>
          {#each variants as variant}
            <td>
              <TextFieldAugment {variant} placeholder="Placeholder">
                <InfoCircled slot="iconRight" />
              </TextFieldAugment>
            </td>
          {/each}
        </tr>
        <tr>
          <th>Both</th>
          {#each variants as variant}
            <td>
              <TextFieldAugment {variant} placeholder="Placeholder">
                <InfoCircled slot="iconLeft" />
                <InfoCircled slot="iconRight" />
              </TextFieldAugment>
            </td>
          {/each}
        </tr>
      </tbody>
    </table>
  </div>
</section>

<section>
  <h2>With Label</h2>
  <div class="l-table-wrapper">
    <table class="l-table">
      <thead>
        <tr> </tr>
      </thead>

      <tbody>
        <tr>
          {#each variants as variant}
            <td>
              <TextFieldAugment {variant} placeholder="Placeholder">
                <TextAugment slot="label" size={1}>Field Label</TextAugment>
              </TextFieldAugment>
            </td>
          {/each}
        </tr>
      </tbody>
    </table>
  </div>
</section>

<style>
  .l-table-wrapper {
    overflow-x: auto;
  }
  .l-table td {
    min-width: 120px;
  }

  section {
    padding: 12px;
  }
</style>
