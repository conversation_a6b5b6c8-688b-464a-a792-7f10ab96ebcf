<script lang="ts">
  import Fieldset from "$common-webviews/mocks/storybook/Fieldset.svelte";
  import { dsColorAttribute } from "$common-webviews/src/design-system/_libs/component-utils";
  import SpinnerAugment from "$common-webviews/src/design-system/components/SpinnerAugment.svelte";
  import { type ComponentType, type SvelteComponent } from "svelte";

  type ComponentModule = () => Promise<{ default: ComponentType<SvelteComponent> }>;

  const toComponentMap = (obj: Record<string, ComponentModule>) =>
    new Map(
      Object.entries(obj)
        .map(([k, v]) => [k.split("/").at(-1) ?? "", v] as const)
        .sort(([a], [b]) => a.localeCompare(b)),
    );

  const groups = [
    {
      title: "Radix",
      icons: toComponentMap(
        import.meta.glob("$common-webviews/src/design-system/icons/*.svelte") as Record<
          string,
          ComponentModule
        >,
      ),
    },
    {
      title: "Augment",
      icons: toComponentMap(
        import.meta.glob("$common-webviews/src/design-system/icons/augment/*.svelte") as Record<
          string,
          ComponentModule
        >,
      ),
    },
    {
      title: "VSCode",
      icons: toComponentMap(
        import.meta.glob("$common-webviews/src/design-system/icons/vscode/*.svelte") as Record<
          string,
          ComponentModule
        >,
      ),
    },
  ];
</script>

{#each groups as group}
  <Fieldset title={group.title}>
    <table>
      <thead>
        <tr>
          <th>File</th>
          <th>No Color</th>
          <th>Accent Color</th>
        </tr>
      </thead>
      <tbody>
        {#each group.icons as [iconName, iconModule]}
          <tr>
            <td>{iconName}</td>
            {#await iconModule()}
              <td><SpinnerAugment size={1} /></td>
              <td><SpinnerAugment size={1} /></td>
            {:then c}
              <td><svelte:component this={c.default} /></td>
              <td {...dsColorAttribute("accent")} style={`color: var(--ds-color-a11);`}
                ><svelte:component this={c.default} /></td
              >
            {:catch e}
              <td colspan="2">{e.message}</td>
            {/await}
          </tr>
        {/each}
      </tbody>
    </table>
  </Fieldset>
{/each}
