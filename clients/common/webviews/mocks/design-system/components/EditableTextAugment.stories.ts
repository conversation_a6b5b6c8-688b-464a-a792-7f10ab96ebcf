/* eslint-disable @typescript-eslint/naming-convention */
import type { Meta, StoryObj } from "@storybook/svelte-vite";
import component from "./EditableTextAugmentStory.svelte";

const meta = {
  title: "design-system/components/EditableTextAugment",
  component,
  tags: ["autodocs"],
  argTypes: {
    // Add any argTypes if needed
  },
} satisfies Meta<component>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Examples: Story = {
  args: {},
};
