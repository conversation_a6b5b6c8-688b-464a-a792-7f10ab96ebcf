<script lang="ts">
  import ColumnLayout from "$common-webviews/mocks/storybook/ColumnLayout.svelte";
  import EditableTextAugment from "$common-webviews/src/design-system/components/EditableTextAugment.svelte";
  import CardAugment from "$common-webviews/src/design-system/components/CardAugment.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import Check from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/circle-check.svg?component";
  import Cross from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/circle-xmark.svg?component";

  // Example state
  let value1 = "Click to edit me";
  let value2 = "Default behavior (no click to edit)";
  let value3 = "Different size and variant";
  let value4 = "Custom controls";
  let value5 = "";

  let isEditing4 = false;

  let editableText4: EditableTextAugment;
</script>

<ColumnLayout>
  <CardAugment>
    <TextAugment size={3} weight="medium">EditableTextAugment Examples</TextAugment>

    <div class="section">
      <TextAugment size={2} weight="medium">Click to Edit (shows text cursor)</TextAugment>
      <div class="example">
        <EditableTextAugment bind:value={value1} placeholder="Enter some text" clickToEdit />
      </div>
    </div>

    <div class="section">
      <TextAugment size={2} weight="medium"
        >Default Behavior (no click to edit, default cursor)</TextAugment
      >
      <div class="example">
        <EditableTextAugment bind:value={value2} placeholder="Hover to see default cursor" />
      </div>
    </div>

    <div class="section">
      <TextAugment size={2} weight="medium">Different Sizes and Variants</TextAugment>
      <div class="example">
        <EditableTextAugment
          bind:value={value3}
          size={3}
          variant="soft"
          color="accent"
          placeholder="Enter some text"
          clickToEdit
        />
      </div>
    </div>

    <div class="section">
      <TextAugment size={2} weight="medium"
        >Custom Controls (no click outside to cancel)</TextAugment
      >
      <div class="example">
        <div class="custom-controls">
          <EditableTextAugment
            bind:this={editableText4}
            bind:value={value4}
            clickToEdit={false}
            placeholder="Enter some text"
          />

          {#if isEditing4}
            <div class="controls">
              <IconButtonAugment
                size={1}
                variant="soft"
                color="success"
                on:click={() => editableText4.acceptEdit()}
              >
                <Check />
              </IconButtonAugment>
              <IconButtonAugment
                size={1}
                variant="soft"
                color="neutral"
                on:click={() => editableText4.cancelEdit()}
              >
                <Cross />
              </IconButtonAugment>
            </div>
          {:else}
            <ButtonAugment size={1} variant="soft" on:click={() => editableText4.startEdit()}>
              Edit
            </ButtonAugment>
          {/if}
        </div>
      </div>
    </div>

    <div class="section">
      <TextAugment size={2} weight="medium">Custom Cancel Logic (function)</TextAugment>
      <div class="example example-card-to-detect">
        <EditableTextAugment
          bind:value={value5}
          placeholder="Click to edit - only cancels on clicks outside this card"
          clickToEdit
        />
      </div>
    </div>
  </CardAugment>
</ColumnLayout>

<style>
  .section {
    margin-top: var(--ds-spacing-4);
  }

  .example {
    margin-top: var(--ds-spacing-2);
    padding: var(--ds-spacing-2);
    border: 1px solid var(--gray-a6);
    border-radius: var(--ds-radius-2);
  }

  .custom-controls {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-2);
  }

  .controls {
    display: flex;
    gap: var(--ds-spacing-1);
  }
</style>
