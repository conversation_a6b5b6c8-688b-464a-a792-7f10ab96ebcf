<script lang="ts">
  import ColumnLayout from "$common-webviews/mocks/storybook/ColumnLayout.svelte";
  import Fieldset from "$common-webviews/mocks/storybook/Fieldset.svelte";
  import RadiusSwatch from "./RadiusSwatch.svelte";
</script>

<ColumnLayout>
  <p>Clicking a radius will copy the variable name to your clipboard.</p>

  <Fieldset title={`Radius`}>
    <div class="l-radiuses">
      {#each Array.from({ length: 6 }) as _, i}
        {@const varName = `--ds-radius-${i + 1}`}
        <RadiusSwatch {varName} />
      {/each}
    </div>
  </Fieldset>
</ColumnLayout>

<style>
  .l-radiuses {
    display: flex;
    flex-wrap: wrap;
    gap: var(--ds-spacing-1);
  }
</style>
