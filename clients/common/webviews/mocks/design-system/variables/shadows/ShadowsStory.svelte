<script lang="ts">
  import ColumnLayout from "$common-webviews/mocks/storybook/ColumnLayout.svelte";
  import Fieldset from "$common-webviews/mocks/storybook/Fieldset.svelte";
  import ShadowsSwatch from "./ShadowsSwatch.svelte";
</script>

<ColumnLayout>
  <p>Clicking a shadow will copy the variable name to your clipboard.</p>

  <Fieldset title={`Shadows (with border)`}>
    <div class="l-radiuses">
      {#each Array.from({ length: 6 }) as _, i}
        {@const varName = `--ds-shadow-${i + 1}`}
        <ShadowsSwatch {varName} />
      {/each}
    </div>
  </Fieldset>

  <Fieldset title={`Shadows (without border)`}>
    <div class="l-radiuses">
      {#each Array.from({ length: 6 }) as _, i}
        {@const varName = `--ds-shadow-no-border-${i + 1}`}
        <ShadowsSwatch {varName} />
      {/each}
    </div>
  </Fieldset>
</ColumnLayout>

<style>
  .l-radiuses {
    display: flex;
    flex-wrap: wrap;
    gap: var(--ds-spacing-1);
  }
</style>
