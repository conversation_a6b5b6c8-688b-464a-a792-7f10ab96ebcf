<script lang="ts">
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";

  export let varName: string;

  function onClick() {
    navigator.clipboard.writeText(`var(${varName})`);
  }
</script>

<div class="c-swatch">
  <!-- svelte-ignore a11y-click-events-have-key-events -->
  <!-- svelte-ignore a11y-no-static-element-interactions -->
  <div class="c-swatch__body" on:click={onClick}>
    <div class="c-swatch__demo" style="box-shadow: var({varName})"></div>
  </div>
  <footer class="c-swatch__footer">
    <ButtonAugment
      variant="ghost"
      color="info"
      size={1}
      style="color: inherit"
      title="Copy to clipboard"
      on:click={onClick}>{varName}</ButtonAugment
    >
  </footer>
</div>

<style>
  .c-swatch {
    width: 120px;
    border-radius: var(--ds-radius-2);
    overflow: hidden;
    border: var(--augment-border);
  }

  .c-swatch__body {
    height: 80px;
    cursor: pointer;

    display: flex;
    align-items: center;
    justify-content: center;
  }

  .c-swatch__demo {
    width: 40px;
    height: 40px;
    border-radius: var(--ds-radius-2);
    background-color: var(--ds-color-neutral-a4);
  }

  .c-swatch__footer {
    background: var(--ds-internal-vars-misc-dark-to-white);
    color: var(--ds-internal-vars-misc-white-to-dark);
    text-align: center;
  }

  .c-swatch__footer :global(button) {
    width: 100%;
    border-top-right-radius: 0;
    border-top-left-radius: 0;
  }
</style>
