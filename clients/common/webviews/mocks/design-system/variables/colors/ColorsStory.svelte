<script lang="ts">
  import { dsColorAttribute } from "$common-webviews/src/design-system/_libs/component-utils";
  import ColumnLayout from "$common-webviews/mocks/storybook/ColumnLayout.svelte";
  import Fieldset from "$common-webviews/mocks/storybook/Fieldset.svelte";
  import ColorSwatch from "./ColorSwatch.svelte";

  const aliases = ["accent", "neutral", "error", "success", "warning", "info"];
</script>

<ColumnLayout>
  <p>Clicking a color will copy the variable name to your clipboard.</p>

  {#each aliases as alias}
    <Fieldset title={`${alias} chroma`}>
      <div class="l-colors" {...dsColorAttribute(alias)}>
        {#each Array.from({ length: 12 }) as _, i}
          {@const varName = `--ds-color-${alias}-${i + 1}`}
          <ColorSwatch {varName} />
        {/each}
      </div>
    </Fieldset>

    <Fieldset title={`${alias} alpha`}>
      <div class="l-colors" {...dsColorAttribute(alias)}>
        {#each Array.from({ length: 12 }) as _, i}
          {@const varName = `--ds-color-${alias}-a${i + 1}`}
          <ColorSwatch {varName} />
        {/each}
      </div>
    </Fieldset>
  {/each}
</ColumnLayout>

<style>
  .l-colors {
    display: flex;
    flex-wrap: wrap;
    gap: var(--ds-spacing-1);
  }
</style>
