<script lang="ts">
  import ColumnLayout from "$common-webviews/mocks/storybook/ColumnLayout.svelte";
  import Fieldset from "$common-webviews/mocks/storybook/Fieldset.svelte";
  import SpacingSwatch from "./SpacingSwatch.svelte";
</script>

<ColumnLayout>
  <p>Clicking a space will copy the variable name to your clipboard.</p>

  <Fieldset title={`Spacing`}>
    <div class="l-spacing">
      {#each Array.from({ length: 9 }) as _, i}
        {@const varName = `--ds-spacing-${i + 1}`}
        <SpacingSwatch {varName} />
      {/each}
    </div>
  </Fieldset>
</ColumnLayout>

<style>
  .l-spacing {
    display: flex;
    flex-direction: column;
    align-items: start;
    gap: var(--ds-spacing-1);
  }
</style>
