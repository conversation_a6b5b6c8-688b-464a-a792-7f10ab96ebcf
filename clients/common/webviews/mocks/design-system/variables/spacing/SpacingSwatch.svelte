<script lang="ts">
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";

  export let varName: string;

  function onClick() {
    navigator.clipboard.writeText(`var(${varName})`);
  }
</script>

<div class="c-swatch">
  <div class="c-swatch__name">
    <ButtonAugment
      variant="ghost"
      color="info"
      size={1}
      style="color: inherit"
      title="Copy to clipboard"
      on:click={onClick}>{varName}</ButtonAugment
    >
  </div>

  <!-- svelte-ignore a11y-click-events-have-key-events -->
  <!-- svelte-ignore a11y-no-static-element-interactions -->
  <div class="c-swatch__body" style="background-color: var({varName})" on:click={onClick}>
    <div class="c-swatch__demo" style="width: var({varName})"></div>
  </div>
</div>

<style>
  .c-swatch {
    border-radius: var(--ds-radius-2);
    overflow: hidden;

    display: flex;
    flex-direction: row;
    align-items: stretch;
    border: var(--augment-border);
  }

  .c-swatch__name {
    width: 160px;
    background: var(--ds-internal-vars-misc-dark-to-white);
    color: var(--ds-internal-vars-misc-white-to-dark);
    text-align: center;
  }

  .c-swatch__name :global(button) {
    width: 100%;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }

  .c-swatch__body {
    display: flex;
    flex-direction: row;
    justify-content: start;
    align-items: center;
    width: 100px;
    cursor: pointer;
  }

  .c-swatch__demo {
    height: var(--ds-spacing-4);
    background-color: var(--ds-color-neutral-9);
    margin-left: var(--ds-spacing-2);
    border-radius: var(--ds-radius-1);
  }
</style>
