<script lang="ts">
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import ArrowRight from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/arrow-right.svg?component";
  import InfoCircled from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/circle-info.svg?component";
  import ColumnLayout from "$common-webviews/mocks/storybook/ColumnLayout.svelte";
  import type {
    ButtonColor,
    ButtonRadius,
    ButtonSize,
    ButtonVariant,
  } from "$common-webviews/src/design-system/_primitives/BaseButton.svelte";
  import Fieldset from "$common-webviews/mocks/storybook/Fieldset.svelte";

  const sizeVariants: ButtonSize[] = [0.5, 1, 2, 3, 4];
  const variantVariants: ButtonVariant[] = [
    "classic",
    "solid",
    "soft",
    "surface",
    "outline",
    "ghost-block",
    "ghost",
  ];
  const colorVariants: ButtonColor[] = ["accent", "neutral", "error", "success", "warning", "info"];
  const highContrastVariants: boolean[] = [false, true];
  const radiusVariants: ButtonRadius[] = ["medium", "full"];

  const onClick = () => {
    console.log("Button clicked");
  };
</script>

<ColumnLayout>
  <section>
    <table>
      <thead>
        <tr>
          <th></th>
          <th>Text</th>
          <th>Text + Left Icon</th>
          <th>Text + Right Icon</th>
          <th>Text + Both</th>
        </tr>
      </thead>
      <tbody>
        {#each variantVariants as variant}
          <tr>
            <td>{variant}</td>
            <td>
              <ButtonAugment {variant} on:click={onClick}>Example Button</ButtonAugment>
            </td>
            <td>
              <ButtonAugment {variant} on:click={onClick}>
                <InfoCircled slot="iconLeft" />
                Example Button
              </ButtonAugment>
            </td>
            <td>
              <ButtonAugment {variant} on:click={onClick}>
                Example Button
                <ArrowRight slot="iconRight" />
              </ButtonAugment>
            </td>
            <td>
              <ButtonAugment {variant} on:click={onClick}>
                <InfoCircled slot="iconLeft" />
                Example Button
                <ArrowRight slot="iconRight" />
              </ButtonAugment>
            </td>
          </tr>
        {/each}
      </tbody>
    </table>
  </section>
</ColumnLayout>

<ColumnLayout>
  <section>
    <table>
      <thead>
        <tr>
          <th></th>
          {#each colorVariants as color}
            <th>{color}</th>
          {/each}
        </tr>
      </thead>
      <tbody>
        {#each variantVariants as variant}
          <tr>
            <td>{variant}</td>
            {#each colorVariants as color}
              <td>
                <ButtonAugment {variant} {color} on:click={onClick}>Example Button</ButtonAugment>
              </td>
            {/each}
          </tr>{/each}
      </tbody>
    </table>
  </section>
</ColumnLayout>

<ColumnLayout>
  <section>
    <table>
      <thead>
        <tr>
          <th></th>
          <th>Enabled</th>
          <th>Disabled</th>
          <th>Disabled with Icon</th>
          <th>Disabled with Right Icon</th>
          <th>Loading</th>
        </tr>
      </thead>
      <tbody>
        {#each variantVariants as variant}
          <tr>
            <td>{variant}</td>
            <td>
              <ButtonAugment {variant} on:click={onClick}>Example Button</ButtonAugment>
            </td>
            <td>
              <ButtonAugment {variant} disabled on:click={onClick}>Example Button</ButtonAugment>
            </td>
            <td>
              <ButtonAugment {variant} disabled on:click={onClick}>
                <InfoCircled slot="iconLeft" />
                Example Button</ButtonAugment
              >
            </td>
            <td>
              <ButtonAugment {variant} disabled on:click={onClick}>
                Example Button
                <ArrowRight slot="iconRight" />
              </ButtonAugment>
            </td>
            <td>
              <ButtonAugment {variant} loading on:click={onClick}>Example Button</ButtonAugment>
            </td>
          </tr>
        {/each}
      </tbody>
    </table>
  </section>
</ColumnLayout>

<ColumnLayout>
  <section>
    <table>
      <thead>
        <tr>
          <th></th>
          {#each sizeVariants as size}
            <th>{size}</th>
          {/each}
        </tr>
      </thead>
      <tbody>
        {#each variantVariants as variant}
          <tr>
            <td>{variant}</td>
            {#each sizeVariants as size}
              <td>
                <ButtonAugment {variant} {size} on:click={onClick}>
                  Example Button
                  <ArrowRight slot="iconRight" />
                </ButtonAugment>
              </td>
            {/each}
          </tr>
        {/each}
      </tbody>
    </table>
  </section>
</ColumnLayout>

<ColumnLayout>
  <section>
    <table>
      <thead>
        <tr>
          <th></th>
          <th>Normal</th>
          <th>High Contrast</th>
        </tr>
      </thead>
      <tbody>
        {#each variantVariants as variant}
          <tr>
            <td>{variant}</td>
            {#each highContrastVariants as highContrast}
              <td>
                <ButtonAugment {variant} {highContrast} on:click={onClick}>
                  Example Button
                </ButtonAugment>
              </td>
            {/each}
          </tr>
        {/each}
      </tbody>
    </table>
  </section>
</ColumnLayout>

<ColumnLayout>
  <section>
    <table>
      <thead>
        <tr>
          <th></th>
          {#each sizeVariants as size}
            <th>{size}</th>
          {/each}
        </tr>
      </thead>
      <tbody>
        {#each radiusVariants as radius}
          <tr>
            <td>{radius}</td>
            {#each sizeVariants as size}
              <td>
                <ButtonAugment {size} {radius} on:click={onClick}>Example Button</ButtonAugment>
              </td>
            {/each}
          </tr>
        {/each}
      </tbody>
    </table>
  </section>
</ColumnLayout>

<ColumnLayout>
  <h1>Alignment</h1>

  <Fieldset title="Text Only">
    <ColumnLayout>
      <ButtonAugment alignment="left">Button - Left</ButtonAugment>
      <ButtonAugment alignment="center">Button - Center</ButtonAugment>
      <ButtonAugment alignment="right">Button - Right</ButtonAugment>
    </ColumnLayout>
  </Fieldset>

  <Fieldset title="With one icon">
    <ColumnLayout>
      <ButtonAugment alignment="left">
        <InfoCircled slot="iconLeft" />
        Button - Left
      </ButtonAugment>
      <ButtonAugment alignment="center">
        <InfoCircled slot="iconLeft" />
        Button - Center
      </ButtonAugment>
      <ButtonAugment alignment="right">
        <InfoCircled slot="iconLeft" />
        Button - Right
      </ButtonAugment>
    </ColumnLayout>
  </Fieldset>

  <Fieldset title="With both icons">
    <ColumnLayout>
      <ButtonAugment alignment="left">
        <InfoCircled slot="iconLeft" />
        Button - Left
        <ArrowRight slot="iconRight" />
      </ButtonAugment>
      <ButtonAugment alignment="center">
        <InfoCircled slot="iconLeft" />
        Button - Center
        <ArrowRight slot="iconRight" />
      </ButtonAugment>
      <ButtonAugment alignment="right">
        <InfoCircled slot="iconLeft" />
        Button - Right
        <ArrowRight slot="iconRight" />
      </ButtonAugment>
    </ColumnLayout>
  </Fieldset>
</ColumnLayout>

<ColumnLayout>
  <ButtonAugment>Button<br />across<br />multiple<br />lines</ButtonAugment>
</ColumnLayout>

<ColumnLayout>
  <h1>Additional Properties</h1>
  <div>
    <ButtonAugment title="Example Button with title">Button with title</ButtonAugment>
  </div>
</ColumnLayout>

<ColumnLayout></ColumnLayout>
