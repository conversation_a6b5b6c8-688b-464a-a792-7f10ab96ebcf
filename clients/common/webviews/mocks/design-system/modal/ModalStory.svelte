<script lang="ts">
  import ModalAugment from "$common-webviews/src/design-system/components/ModalAugment.svelte";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import TextFieldAugment from "$common-webviews/src/design-system/components/TextFieldAugment.svelte";
  import CalloutAugment from "$common-webviews/src/design-system/components/CalloutAugment.svelte";
  import ColumnLayout from "$common-webviews/mocks/storybook/ColumnLayout.svelte";
  import Fieldset from "$common-webviews/mocks/storybook/Fieldset.svelte";
  import ExclamationTriangle from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/triangle-exclamation.svg?component";
  import CheckCircle from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/circle-check.svg?component";

  // Modal states
  let showBasicModal = false;
  let showFormModal = false;
  let showConfirmModal = false;
  let showCustomModal = false;
  let showPreventCloseModal = false;
  let showLargeModal = false;

  // Form data
  let formName = "";
  let formEmail = "";

  function handleBasicModalCancel() {
    showBasicModal = false;
    console.log("Basic modal cancelled");
  }

  function handleFormSubmit() {
    console.log("Form submitted:", { name: formName, email: formEmail });
    showFormModal = false;
    formName = "";
    formEmail = "";
  }

  function handleFormCancel() {
    showFormModal = false;
    formName = "";
    formEmail = "";
  }

  function handleConfirm() {
    console.log("Action confirmed");
    showConfirmModal = false;
  }

  function handleConfirmCancel() {
    showConfirmModal = false;
  }

  function handleCustomCancel() {
    showCustomModal = false;
  }

  function handlePreventCloseCancel() {
    showPreventCloseModal = false;
  }

  function handleLargeModalCancel() {
    showLargeModal = false;
  }
</script>

<ColumnLayout>
  <h1>ModalAugment Examples</h1>

  <Fieldset title="Basic Modal">
    <p>Simple modal with title and basic content</p>
    <ButtonAugment on:click={() => (showBasicModal = true)}>Open Basic Modal</ButtonAugment>
  </Fieldset>

  <Fieldset title="Form Modal">
    <p>Modal with form inputs and validation</p>
    <ButtonAugment on:click={() => (showFormModal = true)}>Open Form Modal</ButtonAugment>
  </Fieldset>

  <Fieldset title="Confirmation Modal">
    <p>Modal for confirming destructive actions</p>
    <ButtonAugment color="error" on:click={() => (showConfirmModal = true)}
      >Open Confirmation Modal</ButtonAugment
    >
  </Fieldset>

  <Fieldset title="Custom Header Modal">
    <p>Modal with custom header content</p>
    <ButtonAugment on:click={() => (showCustomModal = true)}>Open Custom Modal</ButtonAugment>
  </Fieldset>

  <Fieldset title="Prevent Close Modal">
    <p>Modal that prevents backdrop and escape key closing</p>
    <ButtonAugment on:click={() => (showPreventCloseModal = true)}
      >Open Prevent Close Modal</ButtonAugment
    >
  </Fieldset>

  <Fieldset title="Large Modal">
    <p>Modal with custom width and lots of content</p>
    <ButtonAugment on:click={() => (showLargeModal = true)}>Open Large Modal</ButtonAugment>
  </Fieldset>
</ColumnLayout>

<!-- Basic Modal -->
<ModalAugment show={showBasicModal} title="Basic Modal" on:cancel={handleBasicModalCancel}>
  <div slot="body">
    <TextAugment size={2}>
      This is a basic modal with a title and some content. You can close it by clicking the
      backdrop, pressing Escape, or clicking the Close button.
    </TextAugment>
  </div>

  <div slot="footer">
    <ButtonAugment variant="solid" on:click={handleBasicModalCancel}>Close</ButtonAugment>
  </div>
</ModalAugment>

<!-- Form Modal -->
<ModalAugment show={showFormModal} title="User Information" on:cancel={handleFormCancel}>
  <svelte:fragment slot="body">
    <TextAugment size={2} color="secondary">Please enter your information below:</TextAugment>

    <TextFieldAugment bind:value={formName} placeholder="Enter your name" label="Name" />

    <TextFieldAugment
      bind:value={formEmail}
      placeholder="Enter your email"
      label="Email"
      type="email"
    />
  </svelte:fragment>

  <div slot="footer">
    <ButtonAugment variant="solid" color="neutral" on:click={handleFormCancel}>Cancel</ButtonAugment
    >
    <ButtonAugment
      variant="solid"
      disabled={!formName.trim() || !formEmail.trim()}
      on:click={handleFormSubmit}
    >
      Submit
    </ButtonAugment>
  </div>
</ModalAugment>

<!-- Confirmation Modal -->
<ModalAugment show={showConfirmModal} title="Confirm Action" on:cancel={handleConfirmCancel}>
  <div slot="body">
    <CalloutAugment variant="soft" color="error" size={1}>
      <ExclamationTriangle slot="icon" />
      This action cannot be undone. Are you sure you want to proceed?
    </CalloutAugment>
  </div>

  <div slot="footer">
    <ButtonAugment variant="solid" color="neutral" on:click={handleConfirmCancel}
      >Cancel</ButtonAugment
    >
    <ButtonAugment variant="solid" color="error" on:click={handleConfirm}>Confirm</ButtonAugment>
  </div>
</ModalAugment>

<!-- Custom Header Modal -->
<ModalAugment show={showCustomModal} on:cancel={handleCustomCancel}>
  <div slot="header">
    <div style="display: flex; align-items: center; gap: var(--ds-spacing-2);">
      <CheckCircle style="color: var(--ds-color-success-9);" />
      <TextAugment size={4} weight="medium" color="success">Success!</TextAugment>
    </div>
  </div>

  <div slot="body">
    <TextAugment size={2}>
      This modal uses a custom header with an icon and colored text instead of the default title.
    </TextAugment>
  </div>

  <div slot="footer">
    <ButtonAugment variant="solid" color="success" on:click={handleCustomCancel}
      >Great!</ButtonAugment
    >
  </div>
</ModalAugment>

<!-- Prevent Close Modal -->
<ModalAugment
  show={showPreventCloseModal}
  title="Processing..."
  preventBackdropClose={true}
  preventEscapeClose={true}
  on:cancel={handlePreventCloseCancel}
>
  <div slot="body">
    <TextAugment size={2}>
      This modal prevents closing via backdrop click or Escape key. You can only close it using the
      button below.
    </TextAugment>

    <CalloutAugment variant="soft" color="info" size={1}>
      <ExclamationTriangle slot="icon" />
      Backdrop and Escape key closing are disabled for this modal.
    </CalloutAugment>
  </div>

  <div slot="footer">
    <ButtonAugment variant="solid" on:click={handlePreventCloseCancel}>Close Modal</ButtonAugment>
  </div>
</ModalAugment>

<!-- Large Modal -->
<ModalAugment
  show={showLargeModal}
  title="Large Modal"
  maxWidth="600px"
  on:cancel={handleLargeModalCancel}
>
  <div slot="body">
    <TextAugment size={2}>
      This modal has a custom maximum width of 600px and contains more content to demonstrate how
      the modal handles larger amounts of text and content.
    </TextAugment>

    <TextAugment size={2}>
      Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut
      labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco
      laboris nisi ut aliquip ex ea commodo consequat.
    </TextAugment>

    <TextAugment size={2}>
      Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla
      pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt
      mollit anim id est laborum.
    </TextAugment>

    <CalloutAugment variant="soft" color="info" size={1}>
      This modal demonstrates custom width and scrollable content when needed.
    </CalloutAugment>
  </div>

  <div slot="footer">
    <ButtonAugment variant="solid" color="neutral" on:click={handleLargeModalCancel}
      >Close</ButtonAugment
    >
  </div>
</ModalAugment>
