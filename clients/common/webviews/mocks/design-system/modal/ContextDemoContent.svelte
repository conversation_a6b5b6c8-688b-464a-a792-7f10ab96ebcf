<script lang="ts">
  import { getFullSizeOverlayContext } from "$common-webviews/src/design-system/components/FullSizeOverlayAugment/context";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";

  // Get the overlay context
  const overlayContext = getFullSizeOverlayContext();
</script>

<div class="context-demo-content">
  <TextAugment size={2} weight="bold">Context Demo Modal</TextAugment>

  <TextAugment size={1}>This modal demonstrates the context functionality.</TextAugment>

  <div class="button-section">
    <TextAugment size={1} weight="bold">Context Actions:</TextAugment>
    <div class="button-group">
      <ButtonAugment variant="soft" color="accent" on:click={() => overlayContext?.close()}>
        Close via Context
      </ButtonAugment>

      <ButtonAugment variant="soft" color="neutral" on:click={() => overlayContext?.toggle()}>
        Toggle via Context
      </ButtonAugment>
    </div>
  </div>

  <div class="info-section">
    <TextAugment size={1}>
      The buttons above use the overlay context to control the modal state. Child components can
      access the context to interact with the overlay without needing direct prop passing.
    </TextAugment>
  </div>
</div>

<style>
  .context-demo-content {
    padding: var(--ds-spacing-4);
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-3);
    max-width: 600px;
    margin: 0 auto;
  }

  .button-section,
  .info-section {
    padding: var(--ds-spacing-3);
    border: 1px solid var(--ds-color-border-subtle);
    border-radius: var(--ds-border-radius-2);
    background-color: var(--ds-color-background-subtle);
  }

  .button-group {
    display: flex;
    gap: var(--ds-spacing-2);
    margin-top: var(--ds-spacing-2);
    flex-wrap: wrap;
  }
</style>
