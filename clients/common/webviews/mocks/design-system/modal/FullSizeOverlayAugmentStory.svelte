<script lang="ts">
  import FullSizeOverlayAugment, {
    FULL_SIZE_OVERLAY_PORTAL_ID,
  } from "$common-webviews/src/design-system/components/FullSizeOverlayAugment/FullSizeOverlayAugment.svelte";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import ColumnLayout from "$common-webviews/mocks/storybook/ColumnLayout.svelte";
  import Fieldset from "$common-webviews/mocks/storybook/Fieldset.svelte";
  import ContextDemoContent from "./ContextDemoContent.svelte";

  let showBasicModal = false;
  let showContextModal = false;
  let showMethodsModal = false;

  // Reference to the overlay component for method calls
  let overlayRef: FullSizeOverlayAugment;
</script>

<ColumnLayout>
  <h1>FullSizeOverlayAugment Examples</h1>

  <Fieldset title="Basic Usage">
    <p>Simple overlay with external state management</p>
    <ButtonAugment on:click={() => (showBasicModal = true)}>Open Basic Overlay</ButtonAugment>
  </Fieldset>

  <Fieldset title="Context Usage">
    <p>Overlay with context that child components can access</p>
    <ButtonAugment on:click={() => (showContextModal = true)}>Open Context Overlay</ButtonAugment>
  </Fieldset>

  <Fieldset title="Methods Usage">
    <p>Control overlay using component methods via reference</p>
    <div class="button-group">
      <ButtonAugment on:click={() => overlayRef?.showModal()}>Show Modal</ButtonAugment>
      <ButtonAugment on:click={() => overlayRef?.closeModal()}>Close Modal</ButtonAugment>
      <ButtonAugment on:click={() => overlayRef?.toggleModal()}>Toggle Modal</ButtonAugment>
    </div>
  </Fieldset>
</ColumnLayout>

<!-- Basic Modal -->
<FullSizeOverlayAugment open={showBasicModal} onClose={() => (showBasicModal = false)}>
  <div class="modal-content">
    <TextAugment size={2} weight="bold">Basic Modal</TextAugment>
    <TextAugment size={1}>This modal is controlled externally via props.</TextAugment>
    <ButtonAugment on:click={() => (showBasicModal = false)}>Close</ButtonAugment>
  </div>
</FullSizeOverlayAugment>

<!-- Context Modal -->
<FullSizeOverlayAugment open={showContextModal} onClose={() => (showContextModal = false)}>
  <ContextDemoContent />
</FullSizeOverlayAugment>

<!-- Methods Modal -->
<FullSizeOverlayAugment
  bind:this={overlayRef}
  open={showMethodsModal}
  onClose={() => (showMethodsModal = false)}
>
  <div class="modal-content">
    <TextAugment size={2} weight="bold">Methods Modal</TextAugment>
    <TextAugment size={1}>This modal is controlled via component methods.</TextAugment>
    <ButtonAugment on:click={() => overlayRef?.closeModal()}>Close via Method</ButtonAugment>
  </div>
</FullSizeOverlayAugment>

<!-- Container for modals (required by PortalAugment) -->
<div id={FULL_SIZE_OVERLAY_PORTAL_ID}></div>

<style>
  .modal-content {
    padding: var(--ds-spacing-4);
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-3);
    max-width: 600px;
    margin: 0 auto;
  }

  .button-group {
    display: flex;
    gap: var(--ds-spacing-2);
    margin-top: var(--ds-spacing-2);
    flex-wrap: wrap;
  }
</style>
