<script lang="ts">
  import MonacoProvider from "$common-webviews/src/design-system/components/MonacoProvider";
  import {
    MonacoContext,
    type MonacoType,
  } from "$common-webviews/src/design-system/components/MonacoProvider";
  import { onDestroy } from "svelte";

  export let showLoadingIndicator = true;

  let editorContainer: HTMLElement;
  let editorInstance: any;
  let unsubscribe: () => void;

  // This function will be called when the slot content is rendered
  function handleMonacoReady() {
    try {
      // Only initialize once
      if (unsubscribe !== undefined || editorInstance !== undefined) {
        return;
      }

      // Get the Monaco context after the provider is mounted
      const monacoContext = MonacoContext.getContext();
      const monaco = monacoContext.monaco;

      // Subscribe to the Monaco store
      unsubscribe = monaco.subscribe((monacoInstance: MonacoType | null) => {
        if (monacoInstance && editorContainer && !editorInstance) {
          console.log("Creating Monaco editor instance");
          // Create the editor
          editorInstance = monacoInstance.editor.create(editorContainer, {
            value:
              "// Example Monaco editor\nconsole.log('Hello, world!');\n\n// This editor is created using the MonacoProvider",
            language: "javascript",
            theme: "vs-dark",
            automaticLayout: true,
            minimap: { enabled: false },
          });
        }
      });
    } catch (error) {
      console.error("Error setting up Monaco editor:", error);
    }
  }

  // Clean up on component destroy
  onDestroy(() => {
    if (unsubscribe) {
      unsubscribe();
    }
    if (editorInstance) {
      editorInstance.dispose();
    }
  });
</script>

<div class="story-container">
  <h2>Monaco Provider Example</h2>
  <p>
    This example demonstrates how to use the MonacoProvider component to lazy-load Monaco editor.
  </p>

  <MonacoProvider.Root {showLoadingIndicator}>
    <div class="editor-container" bind:this={editorContainer}></div>
    <div class="editor-status">
      {#if editorContainer}
        {handleMonacoReady()}
        Monaco is ready and editor is initialized!
      {/if}
    </div>
  </MonacoProvider.Root>
</div>

<style>
  .story-container {
    padding: 1rem;
    max-width: 800px;
  }

  .editor-container {
    height: 200px;
    border: 1px solid var(--ds-color-neutral-a6);
    border-radius: var(--ds-radius-2);
    overflow: hidden;
  }

  .editor-status {
    margin-top: 0.5rem;
    font-size: 0.875rem;
    color: var(--ds-color-success-a11);
  }

  h2 {
    margin-top: 0;
    margin-bottom: 0.5rem;
  }

  p {
    margin-bottom: 1rem;
  }
</style>
