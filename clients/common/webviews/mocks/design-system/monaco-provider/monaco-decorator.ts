import "../../hosts/monaco-init";

/**
 * This decorator ensures that Monaco is loaded before the story is rendered.
 * It's used in the MonacoProvider story to make sure Monaco is available.
 */
export const withMonaco = (story: any, context: any) => {
  // Make sure Monaco is loaded
  console.log("Monaco decorator running, ensuring Monaco is available");

  // Ensure window.augmentDeps.monaco is available
  if (!window.augmentDeps?.monaco) {
    console.error("Monaco is not available in window.augmentDeps.monaco");
  } else {
    console.log("Monaco is available in window.augmentDeps.monaco");
  }

  // Return the story
  return story(context);
};
