/* eslint-disable @typescript-eslint/naming-convention */
import type { Meta, StoryObj } from "@storybook/svelte-vite";
import component from "./MonacoProviderStory.svelte";
import { withMonaco } from "./monaco-decorator";

const meta = {
  title: "design system/MonacoProvider",
  component,
  tags: ["autodocs"],
  argTypes: {
    showLoadingIndicator: {
      control: "boolean",
      description: "Whether to show a loading indicator while Monaco is loading",
      defaultValue: true,
    },
  },
  decorators: [withMonaco],
} satisfies Meta<component>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    showLoadingIndicator: true,
  },
};

export const WithoutLoadingIndicator: Story = {
  args: {
    showLoadingIndicator: false,
  },
};
