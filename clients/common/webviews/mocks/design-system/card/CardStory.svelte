<script lang="ts">
  import ColumnLayout from "$common-webviews/mocks/storybook/ColumnLayout.svelte";
  import Fieldset from "$common-webviews/mocks/storybook/Fieldset.svelte";
  import CardAugment, {
    type CardSize,
    type CardVariant,
  } from "$common-webviews/src/design-system/components/CardAugment.svelte";

  const variants: CardVariant[] = ["surface", "classic", "soft", "ghost"];
  const sizeVariants: CardSize[] = [1, 2, 3, 4, 5];
</script>

{#each variants as variant}
  <Fieldset title={variant}>
    <div style="position: relative;">
      <div class="background"></div>
      <ColumnLayout>
        <CardAugment {variant} size={2} insetContent={false} interactive={false}>
          <div>
            <h1>Hello World</h1>
            <p>This is an example card</p>
          </div>
        </CardAugment>

        <CardAugment {variant} size={2} insetContent={true} interactive={false}>
          <div>
            <h1>Hello World</h1>
            <p>This is an example card with its content inset</p>
          </div>
        </CardAugment>

        <CardAugment
          {variant}
          size={2}
          insetContent={false}
          interactive={true}
          on:click={() => console.log("clicked")}
        >
          <div>
            <h1>Hello World</h1>
            <p>This is an interactive example card</p>
          </div>
        </CardAugment>

        {#each sizeVariants as sizeVariant}
          <CardAugment {variant} size={sizeVariant} insetContent={false} interactive={false}>
            <div>
              <h1>Hello World</h1>
              <p>This is an example card of size {sizeVariant}</p>
            </div>
          </CardAugment>
        {/each}
      </ColumnLayout>
    </div>
  </Fieldset>
{/each}

<style>
  .background {
    background-color: var(--ds-color-neutral-a8);
    height: 50%;
    width: 50%;
    position: absolute;
    top: 0;
    left: 0;
    transform: rotate(45deg);
  }
</style>
