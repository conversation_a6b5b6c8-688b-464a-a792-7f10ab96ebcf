<script lang="ts">
  import TextAugment, {
    type TextType,
    type TextSize,
    type TextWeight,
    type TextColor,
  } from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import ColumnLayout from "$common-webviews/mocks/storybook/ColumnLayout.svelte";
  import CopyButton from "$common-webviews/src/common/components/CopyButton.svelte";

  const sizes: TextSize[] = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9];
  const types: TextType[] = ["default", "monospace"];
  const weights: TextWeight[] = ["light", "regular", "medium", "bold"];
  const colors: TextColor[] = ["accent", "neutral", "error", "success", "primary", "secondary"];
</script>

<ColumnLayout>
  <ColumnLayout>
    {#each sizes as size}
      <h3>Size {size} <CopyButton text={`<TextAugment size={${size}}></TextAugment>`} /></h3>
      <TextAugment {size}>Lorem ipsum dolor sit amet.</TextAugment>
    {/each}
  </ColumnLayout>

  <hr />

  <ColumnLayout>
    <h3>Colors</h3>

    {#each colors as color}
      <div>
        <p>Color {color} <CopyButton text={`<TextAugment color="${color}"></TextAugment>`} /></p>
        <TextAugment {color}>Lorem ipsum dolor sit amet.</TextAugment>
      </div>
    {/each}
  </ColumnLayout>

  <hr />

  <ColumnLayout>
    <h3>Combinations</h3>
    {#each weights as weight}
      {#each types as type}
        {#each sizes as size}
          <div>
            <p>
              <TextAugment size={2}>weight: {weight} type: {type} size: {size}</TextAugment>
              <CopyButton
                text={`<TextAugment weight="${weight}" type="${type}" size={${size}}></TextAugment>`}
              />
            </p>
            <TextAugment {weight} {type} {size}>Lorem ipsum dolor sit amet.</TextAugment>
          </div>
        {/each}
      {/each}
    {/each}
  </ColumnLayout>
</ColumnLayout>

<style>
  hr {
    width: 100%;
  }
</style>
