<script lang="ts">
  import ColumnLayout from "$common-webviews/mocks/storybook/ColumnLayout.svelte";
  import SpinnerAugment, {
    type SpinnerSize,
  } from "$common-webviews/src/design-system/components/SpinnerAugment.svelte";

  const sizeVariants: SpinnerSize[] = [1, 2, 3];
  const loadingVariants: boolean[] = [true, false];
  const colors = ["--indigo-11", "--green-11", "--amber-11"];
</script>

<ColumnLayout>
  <h3>Sizes</h3>
  {#each sizeVariants as size}
    <SpinnerAugment {size} />
  {/each}

  <h3>Loading</h3>
  {#each loadingVariants as loading}
    <SpinnerAugment {loading} />
  {/each}

  <h3>Colors</h3>
  {#each colors as color}
    <div style={`color: var(${color})`}>
      <SpinnerAugment useCurrentColor={true} />
    </div>
  {/each}
</ColumnLayout>
