<script lang="ts">
  import DropdownMenu from "$common-webviews/src/design-system/components/DropdownMenuAugment";
  import ColumnLayout from "$common-webviews/mocks/storybook/ColumnLayout.svelte";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import SpinnerAugment from "$common-webviews/src/design-system/components/SpinnerAugment.svelte";
  import RegularThumbtackIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/0.svg?component";

  import { type ContentSize } from "$common-webviews/src/design-system/components/DropdownMenuAugment/Content.svelte";
  import { type ItemColor } from "$common-webviews/src/design-system/components/DropdownMenuAugment/Item.svelte";

  const sizes: ContentSize[] = [1, 2];
  const colors: ItemColor[] = ["accent", "error", "neutral"];

  let openDropdownMenu = () => {};
  let closeDropdownMenu = () => {};
</script>

<div style="padding: 10rem; padding-top: 30rem;">
  <ColumnLayout>
    {#each sizes as size}
      <DropdownMenu.Root>
        <DropdownMenu.Trigger>
          <ButtonAugment>{size === 1 ? "Small" : "Large"} Size</ButtonAugment>
        </DropdownMenu.Trigger>
        <DropdownMenu.Content {size}>
          <DropdownMenu.Label>This is a Label</DropdownMenu.Label>
          <DropdownMenu.Item>Item 1</DropdownMenu.Item>
          <DropdownMenu.Item>Item 2, which is longer</DropdownMenu.Item>
          <DropdownMenu.Item>Item 3, which is even longer</DropdownMenu.Item>
          <DropdownMenu.Separator />
          <DropdownMenu.Item disabled>Item 4, which is disabled</DropdownMenu.Item>
          <DropdownMenu.Sub>
            <DropdownMenu.SubTrigger>Item 5, which is a submenu</DropdownMenu.SubTrigger>
            <DropdownMenu.SubContent>
              <DropdownMenu.Item>Submenu Item 1</DropdownMenu.Item>
              <DropdownMenu.Item>Submenu Item 2</DropdownMenu.Item>
              <DropdownMenu.Item>Submenu Item 3</DropdownMenu.Item>
              <DropdownMenu.Item>Submenu Item 4</DropdownMenu.Item>
              <DropdownMenu.Item>Submenu Item 5</DropdownMenu.Item>
            </DropdownMenu.SubContent>
          </DropdownMenu.Sub>
          <DropdownMenu.Separator />
          <DropdownMenu.BreadcrumbItem>Forward</DropdownMenu.BreadcrumbItem>
          <DropdownMenu.BreadcrumbBackItem>Back</DropdownMenu.BreadcrumbBackItem>
          <DropdownMenu.Separator />
          <DropdownMenu.Item>
            Loading... <SpinnerAugment slot="iconRight" useCurrentColor />
          </DropdownMenu.Item>
          <DropdownMenu.Item>
            Test a pin icon <RegularThumbtackIcon slot="iconRight" />
          </DropdownMenu.Item>
          <DropdownMenu.Separator />
          <DropdownMenu.Item>
            <SpinnerAugment slot="iconLeft" useCurrentColor /> Loading...
          </DropdownMenu.Item>
          <DropdownMenu.Item>
            <RegularThumbtackIcon slot="iconLeft" /> Test a pin icon
          </DropdownMenu.Item>
          <DropdownMenu.Item>
            <RegularThumbtackIcon slot="iconLeft" /> Test a pin icon loading <SpinnerAugment
              useCurrentColor
              slot="iconRight"
            />
          </DropdownMenu.Item>
          <DropdownMenu.Separator />
          <DropdownMenu.TextFieldItem placeholder="Search" />
        </DropdownMenu.Content>
      </DropdownMenu.Root>
    {/each}
  </ColumnLayout>
</div>

<div style="padding: 10rem; padding-top: 30rem;">
  <ColumnLayout>
    <div>
      <p>External Controls</p>
      <p>
        <ButtonAugment on:click={openDropdownMenu}>Open</ButtonAugment>
        <ButtonAugment on:click={closeDropdownMenu}>Close</ButtonAugment>
      </p>
    </div>
    <DropdownMenu.Root bind:requestOpen={openDropdownMenu} bind:requestClose={closeDropdownMenu}>
      <DropdownMenu.Trigger>
        <ButtonAugment>Button</ButtonAugment>
      </DropdownMenu.Trigger>
      <DropdownMenu.Content side="bottom">
        <DropdownMenu.Label>This is a Label</DropdownMenu.Label>
        <DropdownMenu.Item>Item 1</DropdownMenu.Item>
        <DropdownMenu.Item>Item 2, which is longer</DropdownMenu.Item>
        <DropdownMenu.Item>Item 3, which is even longer</DropdownMenu.Item>
      </DropdownMenu.Content>
    </DropdownMenu.Root>
  </ColumnLayout>
</div>

<div style="padding: 10rem; padding-top: 30rem;">
  <ColumnLayout>
    <div>
      <p>Forced open</p>
    </div>
    <DropdownMenu.Root open={true}>
      <DropdownMenu.Trigger></DropdownMenu.Trigger>
      <DropdownMenu.Content side="bottom">
        <DropdownMenu.Label>This is a Label</DropdownMenu.Label>
        <DropdownMenu.Item>Item 1</DropdownMenu.Item>
        <DropdownMenu.Item>Item 2, which is longer</DropdownMenu.Item>
        <DropdownMenu.Item>Item 3, which is even longer</DropdownMenu.Item>
      </DropdownMenu.Content>
    </DropdownMenu.Root>
  </ColumnLayout>
</div>

<div style="padding: 10rem; padding-top: 30rem;">
  <ColumnLayout>
    <p>Colors</p>
    <DropdownMenu.Root bind:requestOpen={openDropdownMenu} bind:requestClose={closeDropdownMenu}>
      <DropdownMenu.Trigger>
        <ButtonAugment>Button</ButtonAugment>
      </DropdownMenu.Trigger>
      <DropdownMenu.Content side="bottom">
        <DropdownMenu.Label>This is a Label</DropdownMenu.Label>

        <DropdownMenu.Separator />
        <DropdownMenu.Item>
          <SpinnerAugment slot="iconLeft" useCurrentColor /> Loading [No Color]
        </DropdownMenu.Item>
        <DropdownMenu.Item>
          <RegularThumbtackIcon slot="iconLeft" /> Pin icon [No Color]
        </DropdownMenu.Item>
        <DropdownMenu.Item>
          <RegularThumbtackIcon slot="iconLeft" /> Pin icon + loading [No Color] <SpinnerAugment
            useCurrentColor
            slot="iconRight"
          />
        </DropdownMenu.Item>

        {#each colors as color}
          <DropdownMenu.Separator />
          <DropdownMenu.Item {color}>
            <SpinnerAugment slot="iconLeft" useCurrentColor /> Loading [{color}]
          </DropdownMenu.Item>
          <DropdownMenu.Item {color}>
            <RegularThumbtackIcon slot="iconLeft" /> Pin icon [{color}]
          </DropdownMenu.Item>
          <DropdownMenu.Item {color}>
            <RegularThumbtackIcon slot="iconLeft" /> Pin icon + loading [{color}] <SpinnerAugment
              useCurrentColor
              slot="iconRight"
            />
          </DropdownMenu.Item>
        {/each}
      </DropdownMenu.Content>
    </DropdownMenu.Root>
  </ColumnLayout>
</div>
