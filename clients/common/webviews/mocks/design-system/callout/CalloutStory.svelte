<script lang="ts">
  import ColumnLayout from "$common-webviews/mocks/storybook/ColumnLayout.svelte";
  import Fieldset from "$common-webviews/mocks/storybook/Fieldset.svelte";
  import CalloutAugment, {
    type CalloutVariant,
    type CalloutColor,
    type CalloutSize,
  } from "$common-webviews/src/design-system/components/CalloutAugment.svelte";
  import InfoCircled from "$common-webviews/src/design-system/icons/info-circled.svelte";

  const variants: CalloutVariant[] = ["soft", "surface", "outline"];
  const colorVariants: CalloutColor[] = [
    "accent",
    "neutral",
    "error",
    "success",
    "warning",
    "info",
  ];
  const highContrastVariants: boolean[] = [false, true];
  const sizeVariants: CalloutSize[] = [1, 2, 3];
</script>

{#each colorVariants as colorVariant}
  <ColumnLayout>
    {#each variants as variant}
      {#each highContrastVariants as highContrastVariant}
        <Fieldset
          title="Variant: '{variant}' Color: '{colorVariant}' High Contrast: '{highContrastVariant}'"
        >
          <ColumnLayout>
            <CalloutAugment {variant} color={colorVariant} highContrast={highContrastVariant}>
              Example callout
            </CalloutAugment>

            <CalloutAugment {variant} color={colorVariant} highContrast={highContrastVariant}>
              <InfoCircled slot="icon" />
              Example callout with icon
            </CalloutAugment>

            {#each sizeVariants as sizeVariant}
              <CalloutAugment
                {variant}
                color={colorVariant}
                highContrast={highContrastVariant}
                size={sizeVariant}
              >
                <InfoCircled slot="icon" />
                Example callout with icon
              </CalloutAugment>
            {/each}
          </ColumnLayout>
        </Fieldset>
      {/each}
    {/each}

    <CalloutAugment variant="soft" color="info">
      <InfoCircled slot="icon" />
      Example callout with icon
      <br />
      Multiple
      <br />
      Lines
    </CalloutAugment>
  </ColumnLayout>
{/each}
