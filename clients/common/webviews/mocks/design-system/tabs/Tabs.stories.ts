/* eslint-disable @typescript-eslint/naming-convention */
import type { Meta, StoryObj } from "@storybook/svelte-vite";
import component from "./TabsStory.svelte";

const meta = {
  title: "design system/TabsAugment",
  component,
  tags: ["autodocs"],
  argTypes: {
    // Main component props
    variant: {
      control: { type: "select" },
      options: ["default", "pill"],
      description: "Visual style variant of the tabs",
    },
    size: {
      control: { type: "select" },
      options: [1, 2, 3],
      description: "Size of the tabs (affects padding, font size, and spacing)",
    },
    color: {
      control: { type: "select" },
      options: ["accent", "neutral"],
      description: "Color theme for the tabs",
    },
    loop: {
      control: { type: "boolean" },
      description: "Whether keyboard navigation should loop around",
    },
    nullable: {
      control: { type: "boolean" },
      description: "Whether active tab can be null/undefined (if false, falls back to first tab)",
    },
    scrollable: {
      control: { type: "boolean" },
      description: "Whether tabs can scroll horizontally when they overflow",
    },
    scrollShadow: {
      control: { type: "boolean" },
      description: "Whether to show scroll shadows when content is scrollable",
    },

    // Content and behavior props
    showLeftIcons: {
      control: { type: "boolean" },
      description: "Show left icons in tabs",
    },
    showRightIcons: {
      control: { type: "boolean" },
      description: "Show right icons in tabs",
    },
    showSlidingBackground: {
      control: { type: "boolean" },
      description: "Enable sliding background animation (works best with pill variant)",
    },
    outline: {
      control: { type: "boolean" },
      description: "Show outline border around tab list (like ToggleAugment)",
    },
    showDisabled: {
      control: { type: "boolean" },
      description: "Show disabled tab state",
    },
    startWithNoActiveTab: {
      control: { type: "boolean" },
      description: "Start with no active tab (user must click to select)",
    },
    manyTabs: {
      control: { type: "boolean" },
      description: "Show many tabs for overflow testing",
    },
  },
  parameters: {
    docs: {
      description: {
        component: `
# TabsAugment

A comprehensive tabs component system built for the Augment design system. Provides accessible, animated, and highly customizable tab navigation.

## Features

- **Accessible**: Full keyboard navigation and ARIA support
- **Animated**: Smooth transitions with View Transitions API
- **Responsive**: Container queries for adaptive behavior
- **Flexible**: Multiple variants (default, pill), sizes (1, 2, 3), and styling options
- **Outline styling**: Optional border styling similar to ToggleAugment
- **Compact size**: Size 1 for dense layouts with reduced padding and spacing
- **Composable**: Works with individual components or as a complete system

## Components

- \`TabsAugment\` - Main container component
- \`TabListAugment\` - Tab navigation list
- \`TabAugment\` - Individual tab button
- \`TabPanelAugment\` - Tab content panel

## Usage

\`\`\`svelte
<TabsAugment value="tab1" size={2} on:change={handleChange}>
  <TabListAugment outline={true}>
    <TabAugment value="tab1">
      <ChatIcon slot="leftIcon" />
      <TextAugment size={2} weight="medium">Chat</TextAugment>
      <ArrowRightIcon slot="rightIcon" />
    </TabAugment>
    <TabAugment value="tab2">
      <TaskIcon slot="leftIcon" />
      <TextAugment size={2} weight="medium">Tasks</TextAugment>
      <ChevronDownIcon slot="rightIcon" />
    </TabAugment>
  </TabListAugment>

  <TabPanelAugment value="tab1">
    Chat content here...
  </TabPanelAugment>

  <TabPanelAugment value="tab2">
    Tasks content here...
  </TabPanelAugment>
</TabsAugment>
\`\`\`

## Best Practices

- Always wrap text content in \`TextAugment\` for proper typography and spacing
- Use \`svelte:fragment\` for conditional slot content
- Prefer size 2 for most use cases (good balance of readability and space efficiency)
- Use sliding background with pill variant for enhanced visual feedback
        `,
      },
    },
  },
} satisfies Meta<component>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    variant: "default",
    size: 2,
    color: "accent",
    showLeftIcons: true,
    showRightIcons: false,
    showSlidingBackground: false,
    showDisabled: false,
    nullable: false,
    startWithNoActiveTab: false,
    manyTabs: false,
    scrollable: true,
    scrollShadow: true,
  },
};

export const PillWithSlidingBackground: Story = {
  args: {
    variant: "pill",
    size: 2,
    showLeftIcons: true,
    showRightIcons: false,
    showSlidingBackground: true,
    nullable: false,
    startWithNoActiveTab: false,
  },
  parameters: {
    docs: {
      description: {
        story: "Pill variant with sliding background animation for enhanced visual feedback.",
      },
    },
  },
};

export const NoActiveTab: Story = {
  args: {
    variant: "default",
    size: 2,
    showLeftIcons: true,
    showRightIcons: false,
    showSlidingBackground: false,
    showDisabled: false,
    nullable: true,
    startWithNoActiveTab: true,
    manyTabs: false,
  },
  parameters: {
    docs: {
      description: {
        story: "Demonstrates tabs with no initially active tab. Users must click to select a tab.",
      },
    },
  },
};

export const SlidingBackgroundNoActiveTab: Story = {
  args: {
    variant: "pill",
    size: 2,
    showLeftIcons: true,
    showRightIcons: false,
    showSlidingBackground: true,
    showDisabled: false,
    nullable: true,
    startWithNoActiveTab: true,
    manyTabs: false,
  },
  parameters: {
    docs: {
      description: {
        story:
          "Pill variant with sliding background that becomes invisible when no tab is active. The slider should be hidden initially and appear when a tab is selected.",
      },
    },
  },
};

export const WithOutline: Story = {
  args: {
    variant: "default",
    size: 2,
    showLeftIcons: true,
    showRightIcons: false,
    showSlidingBackground: false,
    outline: true,
    showDisabled: false,
    nullable: false,
    startWithNoActiveTab: false,
    manyTabs: false,
  },
  parameters: {
    docs: {
      description: {
        story: "Tabs with outline border styling, similar to ToggleAugment component.",
      },
    },
  },
};

export const Size1Compact: Story = {
  args: {
    variant: "default",
    size: 1,
    showLeftIcons: true,
    showRightIcons: false,
    showSlidingBackground: false,
    outline: false,
    showDisabled: false,
    nullable: false,
    startWithNoActiveTab: false,
    manyTabs: false,
  },
  parameters: {
    docs: {
      description: {
        story: "Compact size 1 tabs with reduced padding and spacing for dense layouts.",
      },
    },
  },
};

export const Size1WithOutline: Story = {
  args: {
    variant: "default",
    size: 1,
    showLeftIcons: true,
    showRightIcons: false,
    showSlidingBackground: false,
    outline: true,
    showDisabled: false,
    nullable: false,
    startWithNoActiveTab: false,
    manyTabs: false,
  },
  parameters: {
    docs: {
      description: {
        story: "Compact size 1 tabs with outline border - perfect for tight spaces.",
      },
    },
  },
};

export const AllFeatures: Story = {
  args: {
    variant: "pill",
    size: 2,
    showLeftIcons: true,
    showRightIcons: true,
    showSlidingBackground: true,
    showDisabled: true,
    nullable: false,
    startWithNoActiveTab: false,
    manyTabs: false,
  },
  parameters: {
    docs: {
      description: {
        story: "Comprehensive example showcasing all available features and states.",
      },
    },
  },
};

export const LeftAndRightIcons: Story = {
  args: {
    variant: "default",
    size: 2,
    showLeftIcons: true,
    showRightIcons: true,
    showSlidingBackground: false,
    showDisabled: false,
    nullable: false,
    startWithNoActiveTab: false,
    manyTabs: false,
  },
  parameters: {
    docs: {
      description: {
        story:
          "Demonstrates tabs with both left and right icons. Left icons typically represent the tab content type, while right icons can indicate actions or states.",
      },
    },
  },
};

export const NonNullable: Story = {
  args: {
    variant: "default",
    size: 2,
    showLeftIcons: true,
    showRightIcons: false,
    nullable: false,
    startWithNoActiveTab: true, // This will be overridden since nullable is false
  },
  parameters: {
    docs: {
      description: {
        story:
          "Example with nullable=false. The active tab cannot be undefined - it will always fall back to the first tab. Try pressing Escape - it won't clear the selection.",
      },
    },
  },
};

export const ScrollableOverflow: Story = {
  args: {
    variant: "default",
    size: 2,
    showLeftIcons: true,
    showRightIcons: false,
    showSlidingBackground: false,
    showDisabled: false,
    nullable: false,
    startWithNoActiveTab: false,
    manyTabs: true,
  },
  parameters: {
    docs: {
      description: {
        story:
          "Demonstrates horizontal scrolling behavior when tabs overflow the container. Try resizing the viewport to see scroll indicators and smooth scrolling to active tabs.",
      },
    },
  },
};

export const ScrollableWithSlidingBackground: Story = {
  args: {
    variant: "pill",
    size: 2,
    showLeftIcons: true,
    showRightIcons: false,
    showSlidingBackground: true,
    showDisabled: false,
    nullable: false,
    startWithNoActiveTab: false,
    manyTabs: true,
    scrollable: true,
    scrollShadow: true,
  },
  parameters: {
    docs: {
      description: {
        story:
          "Pill variant with sliding background and horizontal scrolling. The sliding background correctly follows the active tab even when scrolled.",
      },
    },
  },
};

export const ScrollableNoShadows: Story = {
  args: {
    variant: "default",
    size: 2,
    showLeftIcons: true,
    showRightIcons: false,
    showSlidingBackground: false,
    showDisabled: false,
    nullable: false,
    startWithNoActiveTab: false,
    manyTabs: true,
    scrollable: true,
    scrollShadow: false,
  },
  parameters: {
    docs: {
      description: {
        story:
          "Scrollable tabs without scroll shadows. Useful when you want scrolling but prefer a cleaner look without fade indicators.",
      },
    },
  },
};

export const NonScrollable: Story = {
  args: {
    variant: "default",
    size: 2,
    showLeftIcons: true,
    showRightIcons: false,
    showSlidingBackground: false,
    showDisabled: false,
    nullable: false,
    startWithNoActiveTab: false,
    manyTabs: true,
    scrollable: false,
    scrollShadow: false,
  },
  parameters: {
    docs: {
      description: {
        story:
          "Non-scrollable tabs that will overflow the container. Useful when you want to control overflow behavior manually or have a fixed layout.",
      },
    },
  },
};
