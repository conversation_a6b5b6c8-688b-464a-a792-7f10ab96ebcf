<script lang="ts">
  import {
    TabsAugment,
    TabListAugment,
    TabAugment,
    TabPanelAugment,
  } from "$common-webviews/src/design-system/components/TabsAugment";

  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";

  // Icons
  import ChatIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/comments.svg?component";
  import TaskIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/list-check.svg?component";
  import DiffIcon from "$common-webviews/src/design-system/icons/diff.svelte";
  import SettingsIcon from "$common-webviews/src/design-system/icons/gear.svelte";
  // Additional icons for right side
  import ArrowRightIcon from "$common-webviews/src/design-system/icons/arrow-right.svelte";
  import ChevronDownIcon from "$common-webviews/src/design-system/icons/chevron-down.svelte";
  import ExternalLinkIcon from "$common-webviews/src/design-system/icons/open-in-new-window.svelte";
  import InfoIcon from "$common-webviews/src/design-system/icons/info-circled.svelte";

  import ColumnLayout from "$common-webviews/mocks/storybook/ColumnLayout.svelte";
  import Fieldset from "$common-webviews/mocks/storybook/Fieldset.svelte";

  import type {
    TabsVariant,
    TabsSize,
  } from "$common-webviews/src/design-system/components/TabsAugment/types";

  // Story props - Main component props
  export let variant: TabsVariant = "default";
  export let size: TabsSize = 2;
  export let color: "accent" | "neutral" = "accent";
  export let loop: boolean = true;

  // Story props - Content and behavior
  export let showLeftIcons: boolean = true;
  export let showRightIcons: boolean = false;
  export let showSlidingBackground: boolean = false;
  export let outline: boolean = false;
  export let showDisabled: boolean = false;
  export let nullable: boolean = false;
  export let startWithNoActiveTab: boolean = false;
  export let manyTabs: boolean = false;
  export let scrollable: boolean = true;
  export let scrollShadow: boolean = true;

  // State
  let activeTab = startWithNoActiveTab && nullable ? undefined : "chat";

  // Sample data
  const baseTabs = [
    {
      value: "chat",
      label: "Chat",
      leftIcon: ChatIcon,
      rightIcon: ArrowRightIcon,
    },
    {
      value: "tasks",
      label: "Tasks",
      leftIcon: TaskIcon,
      rightIcon: ChevronDownIcon,
    },
    {
      value: "edits",
      label: "Edits",
      leftIcon: DiffIcon,
      rightIcon: ExternalLinkIcon,
    },
    {
      value: "settings",
      label: "Settings",
      leftIcon: SettingsIcon,
      rightIcon: InfoIcon,
      disabled: showDisabled,
    },
  ];

  // Additional tabs for overflow testing
  const manyTabsData = [
    ...baseTabs,
    {
      value: "files",
      label: "Files",
      leftIcon: DiffIcon,
      rightIcon: ArrowRightIcon,
      disabled: false,
    },
    {
      value: "terminal",
      label: "Terminal",
      leftIcon: TaskIcon,
      rightIcon: ExternalLinkIcon,
      disabled: false,
    },
    {
      value: "logs",
      label: "Logs",
      leftIcon: SettingsIcon,
      rightIcon: ArrowRightIcon,
      disabled: false,
    },
  ];

  $: tabs = manyTabs ? manyTabsData : baseTabs;

  function handleTabChange(event: CustomEvent<{ value: string }>) {
    activeTab = event.detail.value;
  }

  function clearActiveTab() {
    if (nullable) {
      activeTab = undefined;
    }
  }
</script>

<ColumnLayout>
  <Fieldset title="Interactive Tabs Demo">
    <TabsAugment
      value={activeTab}
      {nullable}
      {variant}
      {size}
      {color}
      {loop}
      {scrollable}
      {scrollShadow}
      on:change={handleTabChange}
    >
      <TabListAugment {showSlidingBackground} {outline}>
        {#each tabs as tab}
          <TabAugment value={tab.value} disabled={tab.disabled}>
            <svelte:fragment slot="leftIcon">
              {#if showLeftIcons && tab.leftIcon}
                <svelte:component this={tab.leftIcon} />
              {/if}
            </svelte:fragment>

            <TextAugment size={2} weight="medium">{tab.label}</TextAugment>

            <svelte:fragment slot="rightIcon">
              {#if showRightIcons && tab.rightIcon}
                <svelte:component this={tab.rightIcon} />
              {/if}
            </svelte:fragment>
          </TabAugment>
        {/each}
      </TabListAugment>

      <TabPanelAugment value="chat">
        <div
          style="padding: var(--ds-spacing-4); background: var(--ds-color-neutral-a2); border-radius: var(--ds-radius-2); margin-top: var(--ds-spacing-3);"
        >
          <h3>Chat Panel</h3>
          <p>Chat conversation content</p>
        </div>
      </TabPanelAugment>

      <TabPanelAugment value="tasks">
        <div
          style="padding: var(--ds-spacing-4); background: var(--ds-color-neutral-a2); border-radius: var(--ds-radius-2); margin-top: var(--ds-spacing-3);"
        >
          <h3>Tasks Panel</h3>
          <p>Task list and progress</p>
        </div>
      </TabPanelAugment>

      <TabPanelAugment value="edits">
        <div
          style="padding: var(--ds-spacing-4); background: var(--ds-color-neutral-a2); border-radius: var(--ds-radius-2); margin-top: var(--ds-spacing-3);"
        >
          <h3>Edits Panel</h3>
          <p>File changes and modifications</p>
        </div>
      </TabPanelAugment>

      <TabPanelAugment value="settings">
        <div
          style="padding: var(--ds-spacing-4); background: var(--ds-color-neutral-a2); border-radius: var(--ds-radius-2); margin-top: var(--ds-spacing-3);"
        >
          <h3>Settings Panel</h3>
          <p>Configuration and preferences</p>
          {#if showDisabled}
            <p><em>This tab is disabled</em></p>
          {/if}
        </div>
      </TabPanelAugment>
    </TabsAugment>

    {#if nullable}
      <div style="margin-top: var(--ds-spacing-3);">
        <button
          type="button"
          on:click={clearActiveTab}
          style="padding: var(--ds-spacing-2) var(--ds-spacing-3); background: var(--ds-color-neutral-a3); border: 1px solid var(--ds-color-neutral-a6); border-radius: var(--ds-radius-2); cursor: pointer;"
        >
          Clear Active Tab
        </button>
        <p
          style="margin-top: var(--ds-spacing-2); font-size: var(--ds-font-size-1); color: var(--ds-color-neutral-11);"
        >
          Current active tab: {activeTab || "none"}
        </p>
      </div>
    {/if}
  </Fieldset>
</ColumnLayout>
