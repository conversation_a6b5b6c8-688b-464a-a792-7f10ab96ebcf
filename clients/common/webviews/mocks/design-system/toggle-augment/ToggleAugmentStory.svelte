<script lang="ts">
  import ToggleAugment from "$common-webviews/src/design-system/components/ToggleAugment.svelte";
  import ColumnLayout from "$common-webviews/mocks/storybook/ColumnLayout.svelte";
  import Fieldset from "$common-webviews/mocks/storybook/Fieldset.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";

  type ToggleSize = 1 | 2 | 3;
  const sizeVariants: ToggleSize[] = [1, 2, 3];

  let basicChecked = false;
  let smallChecked = false;
  let mediumChecked = true;
  let largeChecked = false;

  // Text toggle states
  let textToggleChecked = false;
  let autoManualChecked = true;
  let onOffChecked = false;

  function handleChange() {
    console.log("Toggle triggered");
  }
</script>

<ColumnLayout>
  <Fieldset title="Basic Usage">
    <div class="story-item">
      <ToggleAugment
        bind:checked={basicChecked}
        on:change={handleChange}
        ariaLabel="Basic toggle switch"
      />
      <TextAugment size={2}>State: {basicChecked ? "On" : "Off"}</TextAugment>
    </div>
  </Fieldset>

  <Fieldset title="Sizes">
    {#each sizeVariants as size}
      <div class="story-item">
        <TextAugment size={2} weight="medium" style="min-width: 60px;">Size: {size}</TextAugment>
        <ToggleAugment
          {size}
          checked={size === 1 ? smallChecked : size === 2 ? mediumChecked : largeChecked}
          on:change={handleChange}
          ariaLabel={`Toggle switch size ${size}`}
        />
      </div>
    {/each}
  </Fieldset>

  <Fieldset title="Disabled States">
    {#each sizeVariants as currentSize}
      <div class="story-item">
        <TextAugment size={2} weight="medium" style="min-width: 150px;"
          >Size {currentSize} - Off - Disabled</TextAugment
        >
        <ToggleAugment
          size={currentSize}
          checked={false}
          disabled={true}
          ariaLabel="Toggle switch {currentSize} off disabled"
        />
      </div>
      <div class="story-item">
        <TextAugment size={2} weight="medium" style="min-width: 150px;"
          >Size {currentSize} - On - Disabled</TextAugment
        >
        <ToggleAugment
          size={currentSize}
          checked={true}
          disabled={true}
          ariaLabel="Toggle switch {currentSize} on disabled"
        />
      </div>
    {/each}
  </Fieldset>

  <Fieldset title="With Specific Aria Label">
    <div class="story-item">
      <ToggleAugment ariaLabel="Enable feature X" on:change={handleChange} />
      <TextAugment size={2}>Toggle with aria-label="Enable feature X"</TextAugment>
    </div>
  </Fieldset>

  <Fieldset title="With Text Inside Toggle">
    <div class="story-item">
      <ToggleAugment
        bind:checked={textToggleChecked}
        onText="On"
        offText="Off"
        size={2}
        ariaLabel="Toggle with ON/OFF text"
        on:change={handleChange}
      />
      <TextAugment size={2}>ON/OFF Toggle: {textToggleChecked ? "On" : "Off"}</TextAugment>
    </div>

    <div class="story-item">
      <ToggleAugment
        bind:checked={autoManualChecked}
        onText="Auto"
        offText="Auto"
        size={2}
        ariaLabel="Toggle with Auto text"
        on:change={handleChange}
      />
      <TextAugment size={2}>Auto Toggle: {autoManualChecked ? "Auto" : "Manual"}</TextAugment>
    </div>

    <div class="story-item">
      <ToggleAugment
        bind:checked={onOffChecked}
        onText="Yes"
        offText="No"
        size={3}
        ariaLabel="Toggle with Yes/No text"
        on:change={handleChange}
      />
      <TextAugment size={2}>Yes/No Toggle: {onOffChecked ? "Yes" : "No"}</TextAugment>
    </div>
  </Fieldset>

  <Fieldset title="Text Toggle Sizes">
    {#each sizeVariants as size}
      <div class="story-item">
        <TextAugment size={2} weight="medium" style="min-width: 60px;">Size: {size}</TextAugment>
        <ToggleAugment
          {size}
          checked={size === 2}
          onText="On"
          offText="Off"
          ariaLabel={`Text toggle size ${size}`}
          on:change={handleChange}
        />
      </div>
    {/each}
  </Fieldset>
</ColumnLayout>

<style>
  .story-item {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-3);
    margin-bottom: var(--ds-spacing-3);
    padding: var(--ds-spacing-2);
    border: 1px solid var(--ds-color-neutral-a3);
    border-radius: var(--ds-radius-2);
  }
</style>
