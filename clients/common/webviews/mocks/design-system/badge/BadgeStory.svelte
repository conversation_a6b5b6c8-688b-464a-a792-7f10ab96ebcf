<script lang="ts">
  import ColumnLayout from "$common-webviews/mocks/storybook/ColumnLayout.svelte";
  import type {
    BadgeColor,
    BadgeSize,
    BadgeVariant,
  } from "$common-webviews/src/design-system/components/BadgeAugment/badge-types";
  import Badge from "$common-webviews/src/design-system/components/BadgeAugment";
  import InfoCircled from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/circle-info.svg?component";
  import DotsHorizontal from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/ellipsis.svg?component";
  import DrawingPin from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/thumbtack.svg?component";

  const variants: BadgeVariant[] = ["solid", "soft", "surface", "outline", "ghost"];
  const colorVariants: BadgeColor[] = [
    "accent",
    "neutral",
    "error",
    "success",
    "warning",
    "info",
    "premium",
  ];
  const highContrastVariants: boolean[] = [false, true];
  const sizeVariants: BadgeSize[] = [0, 1, 2, 3];
</script>

<ColumnLayout>
  <section>
    <table>
      <thead>
        <tr>
          <th></th>
          <th>Text</th>
          <th>Text + Icon</th>
          <th>Text + Button</th>
        </tr>
      </thead>
      <tbody>
        {#each variants as variant}
          <tr>
            <td>{variant}</td>
            <td>
              <Badge.Root {variant}>Example badge</Badge.Root>
            </td>
            <td>
              <Badge.Root {variant}>
                <InfoCircled />
                Example badge
              </Badge.Root>
            </td>
            <td>
              <Badge.Root {variant}>
                Example badge
                <Badge.IconButton slot="rightButtons"><DotsHorizontal /></Badge.IconButton>
              </Badge.Root>
            </td>
            <td>
              <Badge.Root {variant}>
                <Badge.IconButton slot="leftButtons"><DrawingPin /></Badge.IconButton>
                Example badge
                <Badge.IconButton slot="rightButtons"><DotsHorizontal /></Badge.IconButton>
              </Badge.Root>
            </td>
          </tr>
        {/each}
      </tbody>
    </table>
  </section>
</ColumnLayout>

<ColumnLayout>
  <section class="l-table-wrapper">
    <table>
      <thead>
        <tr>
          <th></th>
          {#each colorVariants as color}
            <th>{color}</th>
          {/each}
        </tr>
      </thead>
      <tbody>
        {#each variants as variant}
          <tr>
            <td>{variant}</td>
            {#each colorVariants as color}
              <td>
                <Badge.Root {variant} {color}>
                  <Badge.IconButton slot="leftButtons"><DrawingPin /></Badge.IconButton>
                  Example badge
                  <Badge.IconButton slot="rightButtons"><DotsHorizontal /></Badge.IconButton>
                </Badge.Root>
              </td>
            {/each}
          </tr>
        {/each}
      </tbody>
    </table>
  </section>
</ColumnLayout>

<ColumnLayout>
  <section>
    <table>
      <thead>
        <tr>
          <th></th>
          <th>Normal</th>
          <th>High Contrast</th>
        </tr>
      </thead>
      <tbody>
        {#each variants as variant}
          <tr>
            <td>{variant}</td>
            {#each highContrastVariants as highContrast}
              <td>
                <Badge.Root {variant} {highContrast}>Example badge</Badge.Root>
              </td>
            {/each}
          </tr>
        {/each}
      </tbody>
    </table>
  </section>
</ColumnLayout>

<ColumnLayout>
  <section>
    <table>
      <thead>
        <tr>
          <th></th>
        {#each sizeVariants as size}
          <th>{size}</th>
        {/each}
        </tr>
      </thead>
      <tbody>
        {#each variants as variant}
          <tr>
            <td>{variant}</td>
            {#each sizeVariants as size}
              <td>
                <Badge.Root {variant} {size}>
                  Example badge
                  <Badge.IconButton slot="rightButtons"><DotsHorizontal /></Badge.IconButton>
                </Badge.Root>
              </td>
            {/each}
          </tr>
        {/each}
      </tbody>
    </table>
  </section>
</ColumnLayout>

<ColumnLayout>
  <section>
    <table>
      <thead>
        <tr>
          <th></th>
          <th>Chaser</th>
          <th>Chaser + Icon</th>
          <th>Chaser + Text</th>
          <th>Chaser + Icon + Text</th>
        </tr>
      </thead>
      <tbody>
        {#each variants as variant}
          <tr>
            <td>{variant}</td>
            <td>
              <Badge.Root {variant}>Example badge</Badge.Root>
            </td>
            <td>
              <Badge.Root {variant}>
                <InfoCircled />
                Example badge
              </Badge.Root>
            </td>
            <td>
              <Badge.Root {variant}>Example badge</Badge.Root>
            </td>
            <td>
              <Badge.Root {variant}>
                <InfoCircled />
                Example badge
              </Badge.Root>
            </td>
          </tr>
        {/each}
      </tbody>
    </table>
  </section>
</ColumnLayout>

<style>
  .l-table-wrapper {
    overflow-x: auto;
  }
</style>
