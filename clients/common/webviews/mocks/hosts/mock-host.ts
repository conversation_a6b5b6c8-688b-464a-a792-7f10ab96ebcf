import { HostClientType } from "$common-webviews/src/common/hosts/host-types";
import {
  setCategory,
  setIntensity,
  UserThemeCategory,
  UserThemeIntensity,
} from "$common-webviews/src/common/hosts/user-themes/augment-theme-attributes";
import {
  type AsyncWebViewMessage,
  type ReadFileRequest,
  WebViewMessageType,
  type WebViewMessage,
} from "$vscode/src/webview-providers/webview-messages";
import hostCss from "/mocks/hosts/styles/host.css?url";
import variablesCss from "/src/common/css/variables.css?url";
const AUGMENT_MOCK_STYLE_ID = "augment-mock-style";
const vscodeStylesRaw = Object.fromEntries(
  Object.entries(
    import.meta.glob("./styles/client-themes/vscode-*.css", {
      eager: true,
      query: "?raw",
    }) as Record<string, { default: string }>,
  ).map(([key, v]) => [key, v.default] as const),
) as Record<string, string>;
const intellijStylesUrl = Object.fromEntries(
  Object.entries(
    import.meta.glob("./styles/client-themes/intellij-*.css", {
      eager: true,
      query: "?url",
    }) as Record<string, { default: string }>,
  ).map(([key, v]) => [key, v.default] as const),
) as Record<string, string>;
class MockWebviewHost extends EventTarget {
  // We re-broadcast messages from the webview (i.e. via vscode.postMessage())
  // via the webview mock. This allows mocks loaded in pages to respond with
  // sample data.
  messageFromWebView(msg: any) {
    this.dispatchEvent(new CustomEvent("message-from-webview", { detail: msg }));
  }

  // This is a helper function that acts the same as if the extension
  // posted a message to the webview.
  sendMessageToWebView(msg: any) {
    window.postMessage(msg);
  }
}

console.warn("📢 Loading mock host API");
export const mockHost = new MockWebviewHost();

let state: any = undefined;

window["augment"] = window["augment"] || {};
window["augment"].host = window["augment"].host || {
  clientType: HostClientType.jetbrains,
  postMessage: (msg: any) => {
    mockHost.messageFromWebView(msg);
  },
  getState: () => {
    return state;
  },
  setState: (_state: any) => {
    state = _state;
  },
};

export function isCustomEvent(event: Event): event is CustomEvent {
  return "detail" in event;
}

export function assertCustomEvent(event: Event): asserts event is CustomEvent {
  if (!isCustomEvent(event)) {
    console.warn("Unexpected event type: ", event);
    throw new Error("Expected CustomEvent");
  }
}

export function assertWebviewMessage(event: any): asserts event is WebViewMessage {
  if (!("type" in event)) {
    console.warn("Unexpected event type: ", event);
    throw new Error("Expected WebViewMessage");
  }
}

export function assertAsyncWebviewReadFileRequestMessage(
  event: WebViewMessage,
): asserts event is AsyncWebViewMessage<ReadFileRequest> {
  if (!("type" in event && event.type === WebViewMessageType.asyncWrapper)) {
    throw new Error("Expected AsyncWebViewMessage<ReadFileRequest>");
  }
  if (
    !(
      "baseMsg" in event &&
      event.baseMsg &&
      event.baseMsg.type === WebViewMessageType.readFileRequest
    )
  ) {
    throw new Error("Expected AsyncWebViewMessage<ReadFileRequest>");
  }
}

/**
 * So this attribute will only every be these two values.  It may be they are loading
 * other themes, but we only care light/dark.
 *
 * @param theme
 */
export function setMockTheme(theme: MockTheme) {
  const category = mockThemeToCategory[theme];
  setCategory(category);

  const intensity = mockThemeToIntensity[theme];
  setIntensity(intensity);
}

// This class name is added to injected styles to make it easy to find them for removal.
const AUGMENT_MOCK_STYLE_CLASS = "augment-injected-mock-style";

/**
 * This function will inject the styles for the theme into the given
 * element.
 *
 * @param themeDetails
 * @returns
 */
export async function injectCSSStyles(theme: MockTheme, element: HTMLElement) {
  // Remove any previous styles.
  const existingStyles = element.querySelectorAll(`.${AUGMENT_MOCK_STYLE_CLASS}`);
  for (const s of existingStyles) {
    s.remove();
  }

  /**
   * So vscode  sets the css vars on the root html element.  This gives
   * them a higher precedence, than the precedence of a link tag, regardless of order.  Including the root tag as a style
   * element works well enough, as long as we do not do any other selectors.
   *
   * Perhaps we should split them out into some other kind of file vars?
   *
   * Intellij - includes the theme file in the top of the head section of the document.  So we attempt to replicate
   * that precedence for consistency.
   */

  function makeNode(content: string, type: "style" | "link"): HTMLStyleElement | HTMLLinkElement {
    document.getElementById(AUGMENT_MOCK_STYLE_ID)?.remove();
    const style = document.createElement(type);
    if (type === "link") {
      (style as HTMLLinkElement).href = content;
      (style as HTMLLinkElement).rel = "stylesheet";
    } else {
      (style as HTMLStyleElement).innerHTML = content;
    }
    style.id = AUGMENT_MOCK_STYLE_ID;
    return style;
  }
  const themeKey = `./styles/client-themes/${theme}.css`;

  let style: HTMLStyleElement | HTMLLinkElement;

  //Intellij uses a link tag.
  if (intellijStylesUrl[themeKey]) {
    // Intellij does not have a :root in the css.
    style = makeNode(intellijStylesUrl[themeKey], "link") as HTMLLinkElement;
    element.ownerDocument.documentElement.setAttribute("style", "");
  } else if (vscodeStylesRaw[themeKey]) {
    const cssText = vscodeStylesRaw[themeKey];
    /**
     * prelude - stuff before :root {}
     * rootVars - stuff in :root {}
     * postlude - stuff after :root {}
     */
    const [, prelude = "", rootVars = "", postlude = ""] =
      /([\S\s]*):root\s*{([^}]*)}([\s\S]*)/gm.exec(cssText) ?? [];

    //pluck the vars out.
    const [rootVarLines, styleCssLines] = rootVars.split(";\n").reduce(
      (ret, line: string) => {
        ret[line.trim().startsWith("--") ? 0 : 1].push(line);
        return ret;
      },
      [[] as string[], [] as string[]],
    ) as [vars: string[], root: string[]];

    element.ownerDocument.documentElement.setAttribute(
      "style",
      rootVarLines
        .map((v) => v.trim())
        .filter(Boolean)
        .join(";"),
    );
    style = makeNode(
      `${prelude}\n:root { ${styleCssLines.join(";\n")} }\n${postlude}`,
      "style",
    ) as HTMLStyleElement;
  } else {
    console.warn(`Unknown theme: ${theme}`);
  }

  // Order is important, determines the order of the styles.
  element.prepend(injectedStyleLink(variablesCss), injectedStyleLink(hostCss), style!);
}

/**
 * This function will create a link element that will be injected into the
 * document.
 *
 * @param url
 * @returns
 */
function injectedStyleLink(url: string): HTMLLinkElement {
  const link = document.createElement("link");
  link.rel = "stylesheet";
  link.href = url;
  link.className = AUGMENT_MOCK_STYLE_CLASS;
  return link;
}

// A list of themes we have CSS stylesheets for
export enum MockTheme {
  vscodeLightPlus = "vscode-light-plus",
  vscodeSolarized = "vscode-solarized",
  vscodeDracula = "vscode-dracula",
  vscodeCobalt = "vscode-cobalt",
  vscodeKimbieDark = "vscode-kimbie-dark",
  intellijLight = "intellij-light",
  intellijDarcula = "intellij-darcula",
  intellijMaterialLighter = "intellij-material-lighter",
  intellijMaterialOceanic = "intellij-material-oceanic",
  intellijHighContrast = "intellij-high-contrast",
}

// Mapping of client themes to their category (dark or light)
const mockThemeToCategory: { [key in MockTheme]: UserThemeCategory } = {
  [MockTheme.vscodeLightPlus]: UserThemeCategory.light,
  [MockTheme.vscodeSolarized]: UserThemeCategory.light,
  [MockTheme.vscodeDracula]: UserThemeCategory.dark,
  [MockTheme.vscodeCobalt]: UserThemeCategory.dark,
  [MockTheme.vscodeKimbieDark]: UserThemeCategory.dark,
  [MockTheme.intellijLight]: UserThemeCategory.light,
  [MockTheme.intellijDarcula]: UserThemeCategory.dark,
  [MockTheme.intellijMaterialLighter]: UserThemeCategory.light,
  [MockTheme.intellijMaterialOceanic]: UserThemeCategory.dark,
  [MockTheme.intellijHighContrast]: UserThemeCategory.dark,
};

// Mapping of client themes to their category (dark or light)
const mockThemeToIntensity: { [key in MockTheme]: UserThemeIntensity } = {
  [MockTheme.vscodeLightPlus]: UserThemeIntensity.regular,
  [MockTheme.vscodeSolarized]: UserThemeIntensity.regular,
  [MockTheme.vscodeDracula]: UserThemeIntensity.regular,
  [MockTheme.vscodeCobalt]: UserThemeIntensity.regular,
  [MockTheme.vscodeKimbieDark]: UserThemeIntensity.regular,
  [MockTheme.intellijLight]: UserThemeIntensity.regular,
  [MockTheme.intellijDarcula]: UserThemeIntensity.regular,
  [MockTheme.intellijMaterialLighter]: UserThemeIntensity.regular,
  [MockTheme.intellijMaterialOceanic]: UserThemeIntensity.regular,
  [MockTheme.intellijHighContrast]: UserThemeIntensity.highContrast,
};

export function mockThemeCategory(theme: MockTheme): UserThemeCategory {
  return mockThemeToCategory[theme];
}

export function mockThemeIntensity(theme: MockTheme): UserThemeIntensity {
  return mockThemeToIntensity[theme];
}
