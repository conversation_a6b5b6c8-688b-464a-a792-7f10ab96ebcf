// Import only the types from Monaco
import * as monaco from "monaco-editor";
// Use the actual Monaco instance from the window (loaded via CDN or other means)
// This allows us to use the types without bundling the entire Monaco editor
// Import the Monaco environment configuration
import "./monaco-env";

// Initialize window.augmentDeps if it doesn't exist
window.augmentDeps = window.augmentDeps || {};

window.augmentDeps.monaco = new Promise((resolve) => {
  resolve(monaco);
});

console.log("Monaco editor initialized and available via window.augmentDeps.monaco");
