:root {
  --intellij-actionButton-focusedBorderColor: rgba(26, 235, 255, 1);
  --intellij-actionButton-hoverBackground: rgba(0, 0, 0, 1);
  --intellij-actionButton-hoverBorderColor: rgba(26, 235, 255, 1);
  --intellij-actionButton-pressedBackground: rgba(0, 0, 0, 1);
  --intellij-actionButton-pressedBorderColor: rgba(26, 235, 255, 1);
  --intellij-activeCaption: rgba(67, 78, 96, 1);
  --intellij-activeCaptionBorder: rgba(255, 255, 255, 1);
  --intellij-activeCaptionText: rgba(0, 0, 0, 1);
  --intellij-appInspector-graphNode-background: rgba(0, 0, 0, 1);
  --intellij-bigSpinner-background: rgba(0, 0, 0, 1);
  --intellij-bookmark-iconBackground: rgba(224, 134, 31, 1);
  --intellij-bookmark-mnemonic-iconBackground: rgba(0, 0, 0, 1);
  --intellij-bookmark-mnemonic-iconBorderColor: rgba(224, 134, 31, 1);
  --intellij-bookmark-mnemonic-iconForeground: rgba(255, 255, 255, 1);
  --intellij-bookmarkMnemonicAssigned-background: rgba(255, 140, 33, 1);
  --intellij-bookmarkMnemonicAssigned-borderColor: rgba(255, 140, 33, 1);
  --intellij-bookmarkMnemonicAssigned-foreground: rgba(0, 0, 0, 1);
  --intellij-bookmarkMnemonicAvailable-background: rgba(0, 0, 0, 1);
  --intellij-bookmarkMnemonicAvailable-borderColor: rgba(255, 255, 255, 1);
  --intellij-bookmarkMnemonicAvailable-foreground: rgba(255, 255, 255, 1);
  --intellij-bookmarkMnemonicCurrent-background: rgba(51, 51, 255, 1);
  --intellij-bookmarkMnemonicCurrent-borderColor: rgba(51, 51, 255, 1);
  --intellij-bookmarkMnemonicCurrent-foreground: rgba(255, 255, 255, 1);
  --intellij-borders-color: rgba(179, 179, 179, 1);
  --intellij-borders-contrastBorderColor: rgba(179, 179, 179, 1);
  --intellij-button-background: rgba(0, 0, 0, 1);
  --intellij-button-darkShadow: rgba(0, 0, 0, 1);
  --intellij-button-default-endBackground: rgba(26, 235, 255, 1);
  --intellij-button-default-endBorderColor: rgba(26, 235, 255, 1);
  --intellij-button-default-focusColor: rgba(26, 235, 255, 1);
  --intellij-button-default-focusedBorderColor: rgba(0, 0, 0, 1);
  --intellij-button-default-foreground: rgba(0, 0, 0, 1);
  --intellij-button-default-startBackground: rgba(26, 235, 255, 1);
  --intellij-button-default-startBorderColor: rgba(26, 235, 255, 1);
  --intellij-button-disabledBorderColor: rgba(170, 110, 40, 1);
  --intellij-button-disabledText: rgba(224, 134, 31, 1);
  --intellij-button-endBackground: rgba(0, 0, 0, 1);
  --intellij-button-endBorderColor: rgba(255, 255, 255, 1);
  --intellij-button-focusedBorderColor: rgba(26, 235, 255, 1);
  --intellij-button-font-family: "Inter", system-ui, sans-serif;
  --intellij-button-font-size: 13px;
  --intellij-button-foreground: rgba(255, 255, 255, 1);
  --intellij-button-highlight: rgba(255, 255, 255, 1);
  --intellij-button-light: rgba(8, 74, 217, 1);
  --intellij-button-select: rgba(255, 102, 102, 1);
  --intellij-button-shadow: rgba(0, 0, 0, 0.27);
  --intellij-button-shadowColor: rgba(54, 54, 54, 0.5);
  --intellij-button-startBackground: rgba(0, 0, 0, 1);
  --intellij-button-startBorderColor: rgba(255, 255, 255, 1);
  --intellij-canvas-tooltip-background: rgba(0, 0, 0, 1);
  --intellij-checkBox-background: rgba(0, 0, 0, 1);
  --intellij-checkBox-disabledText: rgba(224, 134, 31, 1);
  --intellij-checkBox-font-family: "Inter", system-ui, sans-serif;
  --intellij-checkBox-font-size: 13px;
  --intellij-checkBox-foreground: rgba(255, 255, 255, 1);
  --intellij-checkBox-select: rgba(255, 102, 102, 1);
  --intellij-checkBoxMenuItem-acceleratorFont-family: "Lucida Grande", system-ui, sans-serif;
  --intellij-checkBoxMenuItem-acceleratorFont-size: 14px;
  --intellij-checkBoxMenuItem-acceleratorForeground: rgba(230, 230, 230, 1);
  --intellij-checkBoxMenuItem-acceleratorSelectionForeground: rgba(230, 230, 230, 1);
  --intellij-checkBoxMenuItem-background: rgba(0, 0, 0, 1);
  --intellij-checkBoxMenuItem-disabledBackground: rgba(51, 33, 12, 1);
  --intellij-checkBoxMenuItem-disabledForeground: rgba(224, 134, 31, 1);
  --intellij-checkBoxMenuItem-font-family: "Inter", system-ui, sans-serif;
  --intellij-checkBoxMenuItem-font-size: 13px;
  --intellij-checkBoxMenuItem-foreground: rgba(255, 255, 255, 1);
  --intellij-checkBoxMenuItem-selectionBackground: rgba(51, 51, 255, 1);
  --intellij-checkBoxMenuItem-selectionForeground: rgba(255, 255, 255, 1);
  --intellij-code-block-backgroundColor: rgba(0, 0, 0, 1);
  --intellij-code-block-borderColor: rgba(119, 119, 119, 1);
  --intellij-code-block-editorPane-backgroundColor: rgba(0, 0, 0, 1);
  --intellij-code-block-editorPane-borderColor: rgba(119, 119, 119, 1);
  --intellij-code-inline-backgroundColor: rgba(0, 0, 0, 1);
  --intellij-code-inline-borderColor: rgba(119, 119, 119, 1);
  --intellij-codeWithMe-avatar-foreground: rgba(255, 255, 255, 1);
  --intellij-colorChooser-background: rgba(0, 0, 0, 1);
  --intellij-colorChooser-font-family: "Inter", system-ui, sans-serif;
  --intellij-colorChooser-font-size: 13px;
  --intellij-colorChooser-foreground: rgba(255, 255, 255, 1);
  --intellij-colorChooser-swatchesDefaultRecentColor: rgba(255, 255, 255, 1);
  --intellij-combinedDiff-blockBorder-selectedActiveColor: rgba(26, 235, 255, 1);
  --intellij-combinedDiff-blockBorder-selectedInactiveColor: rgba(90, 93, 99, 1);
  --intellij-comboBox-arrowButton-background: rgba(0, 0, 0, 1);
  --intellij-comboBox-arrowButton-disabledIconColor: rgba(170, 110, 40, 1);
  --intellij-comboBox-arrowButton-iconColor: rgba(255, 255, 255, 1);
  --intellij-comboBox-arrowButton-nonEditableBackground: rgba(0, 0, 0, 1);
  --intellij-comboBox-background: rgba(0, 0, 0, 1);
  --intellij-comboBox-buttonBackground: rgba(255, 255, 255, 1);
  --intellij-comboBox-buttonDarkShadow: rgba(0, 0, 0, 1);
  --intellij-comboBox-buttonHighlight: rgba(255, 255, 255, 1);
  --intellij-comboBox-buttonShadow: rgba(0, 0, 0, 0.27);
  --intellij-comboBox-disabledBackground: rgba(51, 33, 12, 1);
  --intellij-comboBox-disabledForeground: rgba(224, 134, 31, 1);
  --intellij-comboBox-font-family: "Inter", system-ui, sans-serif;
  --intellij-comboBox-font-size: 13px;
  --intellij-comboBox-foreground: rgba(255, 255, 255, 1);
  --intellij-comboBox-modifiedItemForeground: rgba(79, 240, 255, 1);
  --intellij-comboBox-nonEditableBackground: rgba(0, 0, 0, 1);
  --intellij-comboBox-selectionBackground: rgba(51, 51, 255, 1);
  --intellij-comboBox-selectionForeground: rgba(255, 255, 255, 1);
  --intellij-comboBoxButton-background: rgba(0, 0, 0, 1);
  --intellij-completionPopup-advertiser-background: rgba(0, 0, 0, 1);
  --intellij-completionPopup-advertiser-foreground: rgba(255, 255, 255, 1);
  --intellij-completionPopup-foreground: rgba(255, 255, 255, 1);
  --intellij-completionPopup-matchForeground: rgba(237, 148, 255, 1);
  --intellij-completionPopup-matchSelectionForeground: rgba(237, 148, 255, 1);
  --intellij-completionPopup-selectionBackground: rgba(51, 51, 255, 1);
  --intellij-completionPopup-selectionInactiveBackground: rgba(66, 66, 79, 1);
  --intellij-complexPopup-header-background: rgba(0, 0, 0, 1);
  --intellij-component-borderColor: rgba(230, 230, 230, 1);
  --intellij-component-disabledBorderColor: rgba(170, 110, 40, 1);
  --intellij-component-errorFocusColor: rgba(230, 25, 75, 1);
  --intellij-component-focusColor: rgba(26, 235, 255, 1);
  --intellij-component-focusedBorderColor: rgba(0, 0, 0, 1);
  --intellij-component-hoverIconColor: rgba(255, 255, 255, 1);
  --intellij-component-iconColor: rgba(255, 255, 255, 1);
  --intellij-component-inactiveErrorFocusColor: rgba(128, 0, 2, 1);
  --intellij-component-inactiveWarningFocusColor: rgba(128, 70, 5, 1);
  --intellij-component-infoForeground: rgba(224, 134, 31, 1);
  --intellij-component-warningFocusColor: rgba(245, 130, 49, 1);
  --intellij-content-background: rgba(0, 0, 0, 1);
  --intellij-control: rgba(60, 63, 65, 1);
  --intellij-controlDkShadow: rgba(0, 0, 0, 1);
  --intellij-controlHighlight: rgba(8, 74, 217, 1);
  --intellij-controlLtHighlight: rgba(255, 255, 255, 1);
  --intellij-controlShadow: rgba(0, 0, 0, 0.27);
  --intellij-controlText: rgba(187, 187, 187, 1);
  --intellij-counter-background: rgba(255, 255, 255, 1);
  --intellij-counter-foreground: rgba(0, 0, 0, 1);
  --intellij-debugger-evaluateExpression-background: rgba(0, 0, 0, 1);
  --intellij-debuggerPopup-borderColor: rgba(230, 230, 230, 1);
  --intellij-debuggerTabs-selectedBackground: rgba(0, 0, 128, 1);
  --intellij-defaultTabs-background: rgba(0, 0, 0, 1);
  --intellij-defaultTabs-hoverBackground: rgba(15, 103, 128, 1);
  --intellij-defaultTabs-inactiveUnderlineColor: rgba(26, 235, 255, 1);
  --intellij-defaultTabs-underlineColor: rgba(26, 235, 255, 1);
  --intellij-desktop: rgba(34, 255, 6, 1);
  --intellij-desktop-background: rgba(0, 0, 0, 1);
  --intellij-desktopIcon-borderColor: rgba(230, 230, 230, 1);
  --intellij-desktopIcon-borderRimColor: rgba(192, 192, 192, 0.75);
  --intellij-desktopIcon-labelBackground: rgba(0, 0, 0, 0.39);
  --intellij-disclosureButton-defaultBackground: rgba(40, 26, 51, 1);
  --intellij-disclosureButton-hoverOverlay: rgba(69, 0, 115, 1);
  --intellij-disclosureButton-pressedOverlay: rgba(84, 0, 140, 1);
  --intellij-disclosureButton-successBackground: rgba(9, 46, 21, 1);
  --intellij-dragAndDrop-areaBackground: rgba(0, 234, 255, 0.5);
  --intellij-dragAndDrop-areaBorderColor: rgba(26, 235, 255, 1);
  --intellij-dragAndDrop-areaForeground: rgba(255, 255, 255, 1);
  --intellij-dragAndDrop-borderColor: rgba(51, 102, 255, 1);
  --intellij-dragAndDrop-rowBackground: rgba(51, 102, 255, 0.5);
  --intellij-editor-background: rgba(31, 31, 31, 1);
  --intellij-editor-foreground: rgba(204, 204, 204, 1);
  --intellij-editor-searchField-background: rgba(0, 0, 0, 1);
  --intellij-editor-shortcutForeground: rgba(210, 245, 60, 1);
  --intellij-editor-toolTip-background: rgba(0, 0, 0, 1);
  --intellij-editor-toolTip-foreground: rgba(255, 255, 255, 1);
  --intellij-editorPane-background: rgba(0, 0, 0, 1);
  --intellij-editorPane-caretForeground: rgba(187, 187, 187, 1);
  --intellij-editorPane-font-family: "Inter", monospace;
  --intellij-editorPane-font-size: 13px;
  --intellij-editorPane-foreground: rgba(255, 255, 255, 1);
  --intellij-editorPane-inactiveBackground: rgba(0, 0, 0, 1);
  --intellij-editorPane-inactiveForeground: rgba(224, 134, 31, 1);
  --intellij-editorPane-selectionBackground: rgba(51, 51, 255, 1);
  --intellij-editorPane-selectionForeground: rgba(255, 255, 255, 1);
  --intellij-editorTabs-background: rgba(0, 0, 0, 1);
  --intellij-editorTabs-inactiveColoredFileBackground: rgba(0, 0, 0, 0);
  --intellij-editorTabs-inactiveMaskColor: rgba(0, 0, 0, 1);
  --intellij-editorTabs-selectedBackground: rgba(14, 93, 115, 1);
  --intellij-editorTabs-selectedForeground: rgba(255, 255, 255, 1);
  --intellij-editorTabs-underlineColor: rgba(26, 235, 255, 1);
  --intellij-editorTabs-underlinedTabBackground: rgba(0, 0, 0, 1);
  --intellij-fileColor-blue: rgba(0, 0, 77, 1);
  --intellij-fileColor-gray: rgba(6, 35, 41, 1);
  --intellij-fileColor-green: rgba(9, 46, 21, 1);
  --intellij-fileColor-orange: rgba(115, 48, 0, 1);
  --intellij-fileColor-rose: rgba(77, 15, 34, 1);
  --intellij-fileColor-violet: rgba(71, 23, 71, 1);
  --intellij-fileColor-yellow: rgba(56, 28, 0, 1);
  --intellij-flameGraph-jVMBackground: rgba(255, 211, 51, 1);
  --intellij-flameGraph-jVMFocusBackground: rgba(51, 51, 255, 1);
  --intellij-flameGraph-jVMFocusSearchNotMatchedBackground: rgba(51, 51, 255, 1);
  --intellij-flameGraph-jVMFocusedFrameForeground: rgba(255, 255, 255, 1);
  --intellij-flameGraph-jVMFrameForeground: rgba(0, 0, 0, 1);
  --intellij-flameGraph-jVMSearchNotMatchedBackground: rgba(128, 105, 25, 1);
  --intellij-flameGraph-nativeBackground: rgba(26, 235, 255, 1);
  --intellij-flameGraph-nativeFocusBackground: rgba(51, 51, 255, 1);
  --intellij-flameGraph-nativeFocusSearchNotMatchedBackground: rgba(51, 51, 255, 1);
  --intellij-flameGraph-nativeFocusedFrameForeground: rgba(255, 255, 255, 1);
  --intellij-flameGraph-nativeFrameForeground: rgba(0, 0, 0, 1);
  --intellij-flameGraph-nativeSearchNotMatchedBackground: rgba(0, 131, 143, 1);
  --intellij-flameGraph-parentBackground: rgba(255, 255, 255, 1);
  --intellij-flameGraph-parentFocusBackground: rgba(51, 51, 255, 1);
  --intellij-flameGraph-parentFocusSearchNotMatchedBackground: rgba(51, 51, 255, 1);
  --intellij-flameGraph-parentFocusedFrameForeground: rgba(255, 255, 255, 1);
  --intellij-flameGraph-parentFrameForeground: rgba(0, 0, 0, 1);
  --intellij-flameGraph-parentSearchNotMatchedBackground: rgba(117, 117, 117, 1);
  --intellij-flameGraph-tooltip-foreground: rgba(255, 255, 255, 1);
  --intellij-flameGraph-tooltip-scaleBackground: rgba(85, 84, 84, 1);
  --intellij-flameGraph-tooltip-scaleColor: rgba(64, 47, 246, 1);
  --intellij-focus-color: rgba(255, 0, 0, 1);
  --intellij-formattedTextField-background: rgba(0, 0, 0, 1);
  --intellij-formattedTextField-caretForeground: rgba(187, 187, 187, 1);
  --intellij-formattedTextField-font-family: "Inter", system-ui, sans-serif;
  --intellij-formattedTextField-font-size: 13px;
  --intellij-formattedTextField-foreground: rgba(255, 255, 255, 1);
  --intellij-formattedTextField-inactiveBackground: rgba(51, 33, 12, 1);
  --intellij-formattedTextField-inactiveForeground: rgba(224, 134, 31, 1);
  --intellij-formattedTextField-selectionBackground: rgba(51, 51, 255, 1);
  --intellij-formattedTextField-selectionForeground: rgba(255, 255, 255, 1);
  --intellij-gotItTooltip-background: rgba(0, 0, 0, 1);
  --intellij-gotItTooltip-button-foreground: rgba(255, 255, 255, 1);
  --intellij-gotItTooltip-codeBackground: rgba(0, 0, 0, 1);
  --intellij-gotItTooltip-codeBorderColor: rgba(108, 112, 126, 1);
  --intellij-gotItTooltip-foreground: rgba(255, 255, 255, 1);
  --intellij-gotItTooltip-header-foreground: rgba(255, 255, 255, 1);
  --intellij-gotItTooltip-imageBorderColor: rgba(255, 255, 255, 0.3);
  --intellij-gotItTooltip-shortcutBackground: rgba(0, 0, 0, 1);
  --intellij-gotItTooltip-shortcutBorderColor: rgba(26, 235, 255, 1);
  --intellij-group-separatorColor: rgba(179, 179, 179, 1);
  --intellij-helpBrowser-aiEditor-background: rgba(0, 0, 0, 1);
  --intellij-helpBrowser-helpBrowserMessage-snippet-moreLines-background: rgba(0, 0, 0, 1);
  --intellij-helpBrowser-helpBrowserMessage-snippet-moreLines-foreground: rgba(255, 255, 255, 1);
  --intellij-helpBrowser-userMessage-background: rgba(0, 0, 0, 1);
  --intellij-helpBrowser-userMessage-snippet-moreLines-foreground: rgba(255, 255, 255, 1);
  --intellij-hyperlink-linkColor: rgba(88, 157, 246, 1);
  --intellij-iconButton-font-family: ".AppleSystemUIFont", system-ui, sans-serif;
  --intellij-iconButton-font-size: 11px;
  --intellij-ide-shadow-bottom0Color: rgba(0, 0, 0, 0);
  --intellij-ide-shadow-bottom1Color: rgba(0, 0, 0, 0.071);
  --intellij-ide-shadow-bottomLeft0Color: rgba(0, 0, 0, 0);
  --intellij-ide-shadow-bottomLeft1Color: rgba(0, 0, 0, 0.071);
  --intellij-ide-shadow-bottomRight0Color: rgba(0, 0, 0, 0);
  --intellij-ide-shadow-bottomRight1Color: rgba(0, 0, 0, 0.071);
  --intellij-ide-shadow-left0Color: rgba(0, 0, 0, 0);
  --intellij-ide-shadow-left1Color: rgba(0, 0, 0, 0.071);
  --intellij-ide-shadow-right0Color: rgba(0, 0, 0, 0);
  --intellij-ide-shadow-right1Color: rgba(0, 0, 0, 0.071);
  --intellij-ide-shadow-top0Color: rgba(0, 0, 0, 0);
  --intellij-ide-shadow-top1Color: rgba(0, 0, 0, 0.071);
  --intellij-ide-shadow-topLeft0Color: rgba(0, 0, 0, 0);
  --intellij-ide-shadow-topLeft1Color: rgba(0, 0, 0, 0.071);
  --intellij-ide-shadow-topRight0Color: rgba(0, 0, 0, 0);
  --intellij-ide-shadow-topRight1Color: rgba(0, 0, 0, 0.071);
  --intellij-inactiveCaption: rgba(57, 60, 61, 1);
  --intellij-inactiveCaptionBorder: rgba(108, 108, 108, 1);
  --intellij-inactiveCaptionText: rgba(108, 108, 108, 1);
  --intellij-info: rgba(255, 255, 255, 1);
  --intellij-infoText: rgba(187, 187, 187, 1);
  --intellij-inplaceRefactoringPopup-background: rgba(69, 0, 115, 1);
  --intellij-inplaceRefactoringPopup-borderColor: rgba(230, 230, 230, 1);
  --intellij-internalFrame-activeTitleBackground: rgba(238, 238, 238, 1);
  --intellij-internalFrame-activeTitleForeground: rgba(0, 0, 0, 1);
  --intellij-internalFrame-background: rgba(0, 0, 0, 1);
  --intellij-internalFrame-borderColor: rgba(230, 230, 230, 1);
  --intellij-internalFrame-borderDarkShadow: rgba(0, 255, 0, 1);
  --intellij-internalFrame-borderHighlight: rgba(0, 0, 255, 1);
  --intellij-internalFrame-borderLight: rgba(255, 255, 0, 1);
  --intellij-internalFrame-borderShadow: rgba(255, 0, 0, 1);
  --intellij-internalFrame-inactiveTitleBackground: rgba(238, 238, 238, 1);
  --intellij-internalFrame-inactiveTitleForeground: rgba(128, 128, 128, 1);
  --intellij-internalFrame-optionDialogBackground: rgba(238, 238, 238, 1);
  --intellij-internalFrame-optionDialogTitleFont-family: ".AppleSystemUIFont", system-ui, sans-serif;
  --intellij-internalFrame-optionDialogTitleFont-size: 14px;
  --intellij-internalFrame-paletteBackground: rgba(238, 238, 238, 1);
  --intellij-internalFrame-paletteTitleFont-family: ".AppleSystemUIFont", system-ui, sans-serif;
  --intellij-internalFrame-paletteTitleFont-size: 14px;
  --intellij-internalFrame-titleFont-family: ".AppleSystemUIFont", system-ui, sans-serif;
  --intellij-internalFrame-titleFont-size: 14px;
  --intellij-label-background: rgba(0, 0, 0, 1);
  --intellij-label-disabledForeground: rgba(224, 134, 31, 1);
  --intellij-label-disabledShadow: rgba(64, 64, 64, 1);
  --intellij-label-font-family: "Inter", system-ui, sans-serif;
  --intellij-label-font-size: 13px;
  --intellij-label-foreground: rgba(255, 255, 255, 1);
  --intellij-label-selectedForeground: rgba(187, 187, 187, 1);
  --intellij-lesson-badge-newLessonBackground: rgba(0, 230, 31, 1);
  --intellij-lesson-badge-newLessonForeground: rgba(0, 0, 0, 1);
  --intellij-lesson-shortcutBackground: rgba(51, 54, 56, 1);
  --intellij-lesson-stepNumberForeground: rgba(254, 254, 254, 1);
  --intellij-lineProfiler-hotLine-foreground: rgba(255, 82, 97, 1);
  --intellij-lineProfiler-hotLine-hoverBackground: rgba(112, 71, 69, 1);
  --intellij-lineProfiler-hotLine-labelBackground: rgba(89, 61, 65, 1);
  --intellij-lineProfiler-ignoredLine-foreground: rgba(95, 95, 95, 1);
  --intellij-lineProfiler-ignoredLine-labelBackground: rgba(67, 71, 74, 1);
  --intellij-lineProfiler-line-foreground: rgba(120, 120, 120, 1);
  --intellij-lineProfiler-line-hoverBackground: rgba(74, 78, 82, 1);
  --intellij-lineProfiler-line-labelBackground: rgba(67, 71, 74, 1);
  --intellij-link-activeForeground: rgba(210, 245, 60, 1);
  --intellij-link-background: rgba(0, 0, 0, 1);
  --intellij-link-foreground: rgba(255, 255, 255, 1);
  --intellij-link-hoverForeground: rgba(210, 245, 60, 1);
  --intellij-link-pressedForeground: rgba(210, 245, 60, 1);
  --intellij-link-secondaryForeground: rgba(210, 245, 60, 1);
  --intellij-link-tag-background: rgba(0, 0, 0, 1);
  --intellij-link-tag-foreground: rgba(255, 255, 255, 1);
  --intellij-link-visitedForeground: rgba(210, 245, 60, 1);
  --intellij-list-background: rgba(0, 0, 0, 1);
  --intellij-list-dropLineColor: rgba(0, 0, 0, 0.27);
  --intellij-list-font-family: "Inter", system-ui, sans-serif;
  --intellij-list-font-size: 13px;
  --intellij-list-foreground: rgba(255, 255, 255, 1);
  --intellij-list-selectionBackground: rgba(51, 51, 255, 1);
  --intellij-list-selectionForeground: rgba(255, 255, 255, 1);
  --intellij-list-selectionInactiveBackground: rgba(66, 66, 79, 1);
  --intellij-list-selectionInactiveForeground: rgba(187, 187, 187, 1);
  --intellij-mainMenu-background: rgba(0, 0, 0, 1);
  --intellij-mainMenu-foreground: rgba(255, 255, 255, 1);
  --intellij-mainToolbar-background: rgba(0, 0, 0, 1);
  --intellij-mainToolbar-dropdown-background: rgba(0, 0, 0, 1);
  --intellij-mainToolbar-dropdown-hoverBackground: rgba(51, 51, 255, 1);
  --intellij-mainToolbar-dropdown-pressedBackground: rgba(51, 51, 255, 1);
  --intellij-mainToolbar-dropdown-transparentHoverBackground: rgba(51, 51, 255, 1);
  --intellij-mainToolbar-foreground: rgba(255, 255, 255, 1);
  --intellij-mainToolbar-icon-background: rgba(0, 0, 0, 1);
  --intellij-mainToolbar-icon-hoverBackground: rgba(51, 51, 255, 1);
  --intellij-mainToolbar-icon-pressedBackground: rgba(51, 51, 255, 1);
  --intellij-mainWindow-fullScreeControl-background: rgba(87, 90, 92, 1);
  --intellij-mainWindow-tab-background: rgba(0, 0, 0, 1);
  --intellij-mainWindow-tab-borderColor: rgba(230, 230, 230, 1);
  --intellij-mainWindow-tab-foreground: rgba(255, 255, 255, 1);
  --intellij-mainWindow-tab-hoverBackground: rgba(26, 26, 27, 1);
  --intellij-mainWindow-tab-hoverForeground: rgba(206, 208, 214, 1);
  --intellij-mainWindow-tab-selectedBackground: rgba(60, 63, 65, 1);
  --intellij-mainWindow-tab-selectedForeground: rgba(206, 208, 214, 1);
  --intellij-mainWindow-tab-selectedInactiveBackground: rgba(60, 63, 65, 1);
  --intellij-mainWindow-tab-separatorColor: rgba(179, 179, 179, 1);
  --intellij-memoryIndicator-allocatedBackground: rgba(126, 0, 217, 1);
  --intellij-memoryIndicator-usedBackground: rgba(51, 51, 255, 1);
  --intellij-menu: rgba(255, 255, 255, 1);
  --intellij-menu-acceleratorFont-family: "Lucida Grande", system-ui, sans-serif;
  --intellij-menu-acceleratorFont-size: 14px;
  --intellij-menu-acceleratorForeground: rgba(230, 230, 230, 1);
  --intellij-menu-acceleratorSelectionForeground: rgba(230, 230, 230, 1);
  --intellij-menu-background: rgba(0, 0, 0, 1);
  --intellij-menu-borderColor: rgba(230, 230, 230, 1);
  --intellij-menu-disabledBackground: rgba(51, 33, 12, 1);
  --intellij-menu-disabledForeground: rgba(224, 134, 31, 1);
  --intellij-menu-font-family: "Inter", system-ui, sans-serif;
  --intellij-menu-font-size: 13px;
  --intellij-menu-foreground: rgba(255, 255, 255, 1);
  --intellij-menu-selectionBackground: rgba(51, 51, 255, 1);
  --intellij-menu-selectionForeground: rgba(255, 255, 255, 1);
  --intellij-menu-separatorColor: rgba(179, 179, 179, 1);
  --intellij-menuBar-background: rgba(0, 0, 0, 1);
  --intellij-menuBar-borderColor: rgba(230, 230, 230, 1);
  --intellij-menuBar-disabledBackground: rgba(51, 33, 12, 1);
  --intellij-menuBar-disabledForeground: rgba(224, 134, 31, 1);
  --intellij-menuBar-font-family: "Inter", system-ui, sans-serif;
  --intellij-menuBar-font-size: 13px;
  --intellij-menuBar-foreground: rgba(255, 255, 255, 1);
  --intellij-menuBar-highlight: rgba(255, 255, 255, 1);
  --intellij-menuBar-selectionBackground: rgba(51, 51, 255, 1);
  --intellij-menuBar-selectionForeground: rgba(255, 255, 255, 1);
  --intellij-menuBar-shadow: rgba(0, 0, 0, 0.27);
  --intellij-menuItem-acceleratorFont-family: "Inter", system-ui, sans-serif;
  --intellij-menuItem-acceleratorFont-size: 13px;
  --intellij-menuItem-acceleratorForeground: rgba(230, 230, 230, 1);
  --intellij-menuItem-acceleratorSelectionForeground: rgba(230, 230, 230, 1);
  --intellij-menuItem-background: rgba(0, 0, 0, 1);
  --intellij-menuItem-disabledBackground: rgba(51, 33, 12, 1);
  --intellij-menuItem-disabledForeground: rgba(224, 134, 31, 1);
  --intellij-menuItem-font-family: "Inter", system-ui, sans-serif;
  --intellij-menuItem-font-size: 13px;
  --intellij-menuItem-foreground: rgba(255, 255, 255, 1);
  --intellij-menuItem-selectionBackground: rgba(51, 51, 255, 1);
  --intellij-menuItem-selectionForeground: rgba(255, 255, 255, 1);
  --intellij-menuText: rgba(0, 0, 0, 0.85);
  --intellij-navBar-borderColor: rgba(179, 179, 179, 1);
  --intellij-newClass-panel-background: rgba(0, 0, 0, 1);
  --intellij-newClass-searchField-background: rgba(0, 0, 0, 1);
  --intellij-newUiOnboarding-dialog-background: rgba(0, 0, 0, 1);
  --intellij-notification-background: rgba(0, 0, 128, 1);
  --intellij-notification-errorBackground: rgba(128, 0, 2, 1);
  --intellij-notification-errorBorderColor: rgba(230, 25, 75, 1);
  --intellij-notification-errorForeground: rgba(255, 255, 255, 1);
  --intellij-notification-foreground: rgba(255, 255, 255, 1);
  --intellij-notification-iconHoverBackground: rgba(0, 0, 0, 1);
  --intellij-notification-moreButton-background: rgba(0, 0, 0, 1);
  --intellij-notification-moreButton-foreground: rgba(255, 255, 255, 1);
  --intellij-notification-moreButton-innerBorderColor: rgba(0, 0, 0, 1);
  --intellij-notification-shadow-bottom0Color: rgba(0, 0, 0, 0);
  --intellij-notification-shadow-bottom1Color: rgba(0, 0, 0, 0.063);
  --intellij-notification-shadow-bottomLeft0Color: rgba(0, 0, 0, 0);
  --intellij-notification-shadow-bottomLeft1Color: rgba(0, 0, 0, 0.035);
  --intellij-notification-shadow-bottomRight0Color: rgba(0, 0, 0, 0);
  --intellij-notification-shadow-bottomRight1Color: rgba(0, 0, 0, 0.035);
  --intellij-notification-shadow-left0Color: rgba(0, 0, 0, 0);
  --intellij-notification-shadow-left1Color: rgba(0, 0, 0, 0.063);
  --intellij-notification-shadow-right0Color: rgba(0, 0, 0, 0);
  --intellij-notification-shadow-right1Color: rgba(0, 0, 0, 0.063);
  --intellij-notification-shadow-top0Color: rgba(0, 0, 0, 0);
  --intellij-notification-shadow-top1Color: rgba(0, 0, 0, 0.063);
  --intellij-notification-shadow-topLeft0Color: rgba(0, 0, 0, 0);
  --intellij-notification-shadow-topLeft1Color: rgba(0, 0, 0, 0.035);
  --intellij-notification-shadow-topRight0Color: rgba(0, 0, 0, 0);
  --intellij-notification-shadow-topRight1Color: rgba(0, 0, 0, 0.035);
  --intellij-notification-toolWindow-errorBackground: rgba(128, 0, 2, 1);
  --intellij-notification-toolWindow-errorBorderColor: rgba(230, 25, 75, 1);
  --intellij-notification-toolWindow-errorForeground: rgba(255, 255, 255, 1);
  --intellij-notification-toolWindow-informativeBackground: rgba(69, 0, 115, 1);
  --intellij-notification-toolWindow-informativeBorderColor: rgba(230, 230, 230, 1);
  --intellij-notification-toolWindow-informativeForeground: rgba(255, 255, 255, 1);
  --intellij-notification-toolWindow-warningBackground: rgba(128, 70, 5, 1);
  --intellij-notification-toolWindow-warningBorderColor: rgba(245, 130, 49, 1);
  --intellij-notification-toolWindow-warningForeground: rgba(255, 255, 255, 1);
  --intellij-notification-welcomeScreen-separatorColor: rgba(179, 179, 179, 1);
  --intellij-notificationsToolwindow-newNotification-background: rgba(69, 0, 115, 1);
  --intellij-notificationsToolwindow-newNotification-hoverBackground: rgba(69, 0, 115, 1);
  --intellij-optionButton-default-separatorColor: rgba(0, 0, 0, 1);
  --intellij-optionButton-separatorColor: rgba(255, 255, 255, 1);
  --intellij-optionPane-background: rgba(0, 0, 0, 1);
  --intellij-optionPane-buttonFont-family: ".AppleSystemUIFont", system-ui, sans-serif;
  --intellij-optionPane-buttonFont-size: 13px;
  --intellij-optionPane-font-family: "Inter", system-ui, sans-serif;
  --intellij-optionPane-font-size: 13px;
  --intellij-optionPane-foreground: rgba(255, 255, 255, 1);
  --intellij-optionPane-messageFont-family: ".AppleSystemUIFont", system-ui, sans-serif;
  --intellij-optionPane-messageFont-size: 13px;
  --intellij-optionPane-messageForeground: rgba(187, 187, 187, 1);
  --intellij-packageSearch-packageTag-background: rgba(0, 0, 0, 1);
  --intellij-packageSearch-packageTag-foreground: rgba(255, 255, 255, 1);
  --intellij-packageSearch-packageTag-hoverBackground: rgba(85, 88, 91, 1);
  --intellij-packageSearch-packageTag-selectedBackground: rgba(47, 101, 202, 1);
  --intellij-packageSearch-packageTag-selectedForeground: rgba(187, 187, 187, 1);
  --intellij-packageSearch-packageTagSelected-background: rgba(0, 0, 0, 1);
  --intellij-packageSearch-packageTagSelected-foreground: rgba(255, 255, 255, 1);
  --intellij-packageSearch-searchResult-background: rgba(0, 0, 0, 1);
  --intellij-packageSearch-searchResult-hoverBackground: rgba(70, 74, 77, 1);
  --intellij-packageSearch-searchResult-packageTag-background: rgba(0, 0, 0, 1);
  --intellij-packageSearch-searchResult-packageTag-foreground: rgba(255, 255, 255, 1);
  --intellij-packageSearch-searchResult-packageTag-hoverBackground: rgba(85, 88, 91, 1);
  --intellij-packageSearch-searchResult-packageTag-selectedBackground: rgba(47, 101, 202, 1);
  --intellij-packageSearch-searchResult-packageTag-selectedForeground: rgba(187, 187, 187, 1);
  --intellij-panel-background: rgba(0, 0, 0, 1);
  --intellij-panel-font-family: "Inter", system-ui, sans-serif;
  --intellij-panel-font-size: 13px;
  --intellij-panel-foreground: rgba(255, 255, 255, 1);
  --intellij-panel-mouseShortcutBackground: rgba(0, 0, 0, 1);
  --intellij-parameterInfo-background: rgba(40, 26, 51, 1);
  --intellij-parameterInfo-currentOverloadBackground: rgba(69, 0, 115, 1);
  --intellij-parameterInfo-currentParameterForeground: rgba(255, 255, 255, 1);
  --intellij-parameterInfo-foreground: rgba(204, 204, 204, 1);
  --intellij-passwordField-background: rgba(0, 0, 0, 1);
  --intellij-passwordField-capsLockIconColor: rgba(0, 0, 0, 0.39);
  --intellij-passwordField-caretForeground: rgba(187, 187, 187, 1);
  --intellij-passwordField-font-family: "Inter", system-ui, sans-serif;
  --intellij-passwordField-font-size: 13px;
  --intellij-passwordField-foreground: rgba(255, 255, 255, 1);
  --intellij-passwordField-inactiveBackground: rgba(51, 33, 12, 1);
  --intellij-passwordField-inactiveForeground: rgba(224, 134, 31, 1);
  --intellij-passwordField-selectionBackground: rgba(51, 51, 255, 1);
  --intellij-passwordField-selectionForeground: rgba(255, 255, 255, 1);
  --intellij-plugins-background: rgba(0, 0, 0, 1);
  --intellij-plugins-borderColor: rgba(230, 230, 230, 1);
  --intellij-plugins-button-installBorderColor: rgba(26, 235, 255, 1);
  --intellij-plugins-button-installFillBackground: rgba(26, 235, 255, 1);
  --intellij-plugins-button-installFillForeground: rgba(0, 0, 0, 1);
  --intellij-plugins-button-installForeground: rgba(26, 235, 255, 1);
  --intellij-plugins-button-updateBackground: rgba(26, 235, 255, 1);
  --intellij-plugins-button-updateForeground: rgba(0, 0, 0, 1);
  --intellij-plugins-lightSelectionBackground: rgba(0, 0, 102, 1);
  --intellij-plugins-searchField-background: rgba(0, 0, 0, 1);
  --intellij-plugins-searchField-borderColor: rgba(179, 179, 179, 1);
  --intellij-plugins-sectionHeader-background: rgba(0, 0, 128, 1);
  --intellij-plugins-sectionHeader-foreground: rgba(255, 255, 255, 1);
  --intellij-plugins-tab-hoverBackground: rgba(51, 51, 255, 1);
  --intellij-plugins-tab-selectedBackground: rgba(51, 51, 255, 1);
  --intellij-plugins-tab-selectedForeground: rgba(255, 255, 255, 1);
  --intellij-plugins-tagBackground: rgba(0, 0, 128, 1);
  --intellij-plugins-tagForeground: rgba(255, 255, 255, 1);
  --intellij-popup-advertiser-background: rgba(0, 0, 128, 1);
  --intellij-popup-advertiser-borderColor: rgba(0, 0, 128, 1);
  --intellij-popup-background: rgba(0, 0, 0, 1);
  --intellij-popup-borderColor: rgba(230, 230, 230, 1);
  --intellij-popup-header-activeBackground: rgba(0, 0, 128, 1);
  --intellij-popup-header-inactiveBackground: rgba(0, 0, 128, 1);
  --intellij-popup-inactiveBorderColor: rgba(230, 230, 230, 1);
  --intellij-popup-separatorForeground: rgba(255, 255, 255, 1);
  --intellij-popup-toolbar-background: rgba(0, 0, 0, 1);
  --intellij-popup-toolbar-borderColor: rgba(179, 179, 179, 1);
  --intellij-popupMenu-background: rgba(0, 0, 0, 1);
  --intellij-popupMenu-font-family: "Inter", system-ui, sans-serif;
  --intellij-popupMenu-font-size: 13px;
  --intellij-popupMenu-foreground: rgba(255, 255, 255, 1);
  --intellij-popupMenu-selectionBackground: rgba(51, 51, 255, 1);
  --intellij-popupMenu-selectionForeground: rgba(255, 255, 255, 1);
  --intellij-popupMenu-translucentBackground: rgba(60, 63, 65, 1);
  --intellij-profiler-chartSlider-foreground: rgba(254, 254, 254, 1);
  --intellij-profiler-chartSlider-lineColor: rgba(26, 235, 255, 1);
  --intellij-profiler-cpuChart-background: rgba(98, 150, 85, 0.3);
  --intellij-profiler-cpuChart-borderColor: rgba(98, 150, 85, 1);
  --intellij-profiler-cpuChart-inactiveBackground: rgba(98, 150, 85, 0.2);
  --intellij-profiler-cpuChart-inactiveBorderColor: rgba(49, 75, 42, 1);
  --intellij-profiler-cpuChart-pointBackground: rgba(98, 150, 37, 1);
  --intellij-profiler-cpuChart-pointBorderColor: rgba(255, 255, 255, 1);
  --intellij-profiler-liveChart-horizontalAxisColor: rgba(230, 230, 230, 1);
  --intellij-profiler-memoryChart-background: rgba(88, 157, 246, 0.5);
  --intellij-profiler-memoryChart-borderColor: rgba(88, 157, 246, 1);
  --intellij-profiler-memoryChart-inactiveBackground: rgba(88, 157, 246, 0.2);
  --intellij-profiler-memoryChart-inactiveBorderColor: rgba(44, 78, 123, 1);
  --intellij-profiler-memoryChart-pointBackground: rgba(25, 106, 208, 1);
  --intellij-profiler-memoryChart-pointBorderColor: rgba(255, 255, 255, 1);
  --intellij-profiler-timer-background: rgba(50, 50, 50, 1);
  --intellij-profiler-timer-disabledForeground: rgba(110, 110, 110, 1);
  --intellij-profiler-timer-foreground: rgba(255, 255, 255, 1);
  --intellij-progressBar-background: rgba(0, 0, 0, 1);
  --intellij-progressBar-failedColor: rgba(230, 25, 75, 1);
  --intellij-progressBar-failedEndColor: rgba(67, 28, 39, 1);
  --intellij-progressBar-font-family: "Inter", system-ui, sans-serif;
  --intellij-progressBar-font-size: 13px;
  --intellij-progressBar-foreground: rgba(255, 255, 255, 1);
  --intellij-progressBar-indeterminateEndColor: rgba(26, 26, 26, 1);
  --intellij-progressBar-indeterminateStartColor: rgba(255, 255, 255, 1);
  --intellij-progressBar-passedColor: rgba(0, 230, 31, 1);
  --intellij-progressBar-passedEndColor: rgba(21, 69, 30, 1);
  --intellij-progressBar-progressColor: rgba(255, 255, 255, 1);
  --intellij-progressBar-selectionBackground: rgba(51, 51, 255, 1);
  --intellij-progressBar-selectionForeground: rgba(255, 255, 255, 1);
  --intellij-progressBar-trackColor: rgba(64, 64, 64, 1);
  --intellij-radioButton-background: rgba(0, 0, 0, 1);
  --intellij-radioButton-darcula-selectionDisabledColor: rgba(96, 96, 96, 1);
  --intellij-radioButton-darcula-selectionDisabledShadowColor: rgba(60, 60, 60, 1);
  --intellij-radioButton-darcula-selectionEnabledColor: rgba(170, 170, 170, 1);
  --intellij-radioButton-darcula-selectionEnabledShadowColor: rgba(30, 30, 30, 1);
  --intellij-radioButton-darkShadow: rgba(0, 0, 0, 1);
  --intellij-radioButton-disabledText: rgba(224, 134, 31, 1);
  --intellij-radioButton-font-family: "Inter", system-ui, sans-serif;
  --intellij-radioButton-font-size: 13px;
  --intellij-radioButton-foreground: rgba(255, 255, 255, 1);
  --intellij-radioButton-highlight: rgba(255, 255, 255, 1);
  --intellij-radioButton-light: rgba(8, 74, 217, 1);
  --intellij-radioButton-select: rgba(255, 102, 102, 1);
  --intellij-radioButton-shadow: rgba(0, 0, 0, 0.27);
  --intellij-radioButtonMenuItem-acceleratorFont-family: "Lucida Grande", system-ui, sans-serif;
  --intellij-radioButtonMenuItem-acceleratorFont-size: 14px;
  --intellij-radioButtonMenuItem-acceleratorForeground: rgba(230, 230, 230, 1);
  --intellij-radioButtonMenuItem-acceleratorSelectionForeground: rgba(230, 230, 230, 1);
  --intellij-radioButtonMenuItem-background: rgba(0, 0, 0, 1);
  --intellij-radioButtonMenuItem-disabledBackground: rgba(51, 33, 12, 1);
  --intellij-radioButtonMenuItem-disabledForeground: rgba(224, 134, 31, 1);
  --intellij-radioButtonMenuItem-font-family: "Inter", system-ui, sans-serif;
  --intellij-radioButtonMenuItem-font-size: 13px;
  --intellij-radioButtonMenuItem-foreground: rgba(255, 255, 255, 1);
  --intellij-radioButtonMenuItem-selectionBackground: rgba(51, 51, 255, 1);
  --intellij-radioButtonMenuItem-selectionForeground: rgba(255, 255, 255, 1);
  --intellij-recentProject-color1-avatar-end: rgba(233, 128, 111, 1);
  --intellij-recentProject-color1-avatar-start: rgba(224, 136, 85, 1);
  --intellij-recentProject-color1-mainToolbarGradientStart: rgba(101, 75, 64, 1);
  --intellij-recentProject-color2-avatar-end: rgba(187, 127, 25, 1);
  --intellij-recentProject-color2-avatar-start: rgba(176, 139, 20, 1);
  --intellij-recentProject-color2-mainToolbarGradientStart: rgba(83, 76, 51, 1);
  --intellij-recentProject-color3-avatar-end: rgba(135, 170, 89, 1);
  --intellij-recentProject-color3-avatar-start: rgba(161, 163, 89, 1);
  --intellij-recentProject-color3-mainToolbarGradientStart: rgba(69, 80, 56, 1);
  --intellij-recentProject-color4-avatar-end: rgba(97, 131, 236, 1);
  --intellij-recentProject-color4-avatar-start: rgba(59, 146, 184, 1);
  --intellij-recentProject-color4-mainToolbarGradientStart: rgba(49, 81, 95, 1);
  --intellij-recentProject-color5-avatar-end: rgba(122, 100, 240, 1);
  --intellij-recentProject-color5-avatar-start: rgba(53, 116, 240, 1);
  --intellij-recentProject-color5-mainToolbarGradientStart: rgba(52, 76, 125, 1);
  --intellij-recentProject-color6-avatar-end: rgba(169, 86, 207, 1);
  --intellij-recentProject-color6-avatar-start: rgba(200, 77, 143, 1);
  --intellij-recentProject-color6-mainToolbarGradientStart: rgba(93, 53, 74, 1);
  --intellij-recentProject-color7-avatar-end: rgba(168, 77, 224, 1);
  --intellij-recentProject-color7-avatar-start: rgba(149, 90, 224, 1);
  --intellij-recentProject-color7-mainToolbarGradientStart: rgba(79, 62, 101, 1);
  --intellij-recentProject-color8-avatar-end: rgba(39, 156, 205, 1);
  --intellij-recentProject-color8-avatar-start: rgba(36, 163, 148, 1);
  --intellij-recentProject-color8-mainToolbarGradientStart: rgba(29, 71, 68, 1);
  --intellij-recentProject-color9-avatar-end: rgba(61, 150, 139, 1);
  --intellij-recentProject-color9-avatar-start: rgba(95, 173, 101, 1);
  --intellij-recentProject-color9-mainToolbarGradientStart: rgba(62, 85, 64, 1);
  --intellij-review-branch-background: rgba(69, 73, 74, 1);
  --intellij-review-branch-background-hover: rgba(82, 87, 88, 1);
  --intellij-review-chatItem-hover: rgba(75, 75, 75, 0.2);
  --intellij-review-state-background: rgba(69, 73, 74, 1);
  --intellij-review-state-foreground: rgba(140, 140, 140, 1);
  --intellij-review-timeline-thread-diff-anchorLine: rgba(84, 75, 45, 1);
  --intellij-runWidget-foreground: rgba(255, 255, 255, 1);
  --intellij-runWidget-hoverBackground: rgba(0, 0, 0, 0.098);
  --intellij-runWidget-pressedBackground: rgba(0, 0, 0, 0.16);
  --intellij-runWidget-runIconColor: rgba(95, 173, 101, 1);
  --intellij-runWidget-runningBackground: rgba(89, 158, 94, 1);
  --intellij-runWidget-stopBackground: rgba(201, 79, 79, 1);
  --intellij-screenView-defaultBorderColor: rgba(0, 0, 0, 1);
  --intellij-screenView-hoveredBorderColor: rgba(84, 138, 247, 1);
  --intellij-screenView-selectedBorderColor: rgba(84, 138, 247, 1);
  --intellij-scrollBar-background: rgba(0, 0, 0, 1);
  --intellij-scrollBar-foreground: rgba(255, 255, 255, 1);
  --intellij-scrollBar-hoverThumbBorderColor: rgba(0, 0, 0, 0.78);
  --intellij-scrollBar-hoverThumbColor: rgba(230, 230, 230, 1);
  --intellij-scrollBar-hoverTrackColor: rgba(230, 230, 230, 0.35);
  --intellij-scrollBar-mac-hoverThumbBorderColor: rgba(0, 0, 0, 0.78);
  --intellij-scrollBar-mac-hoverThumbColor: rgba(230, 230, 230, 1);
  --intellij-scrollBar-mac-hoverTrackColor: rgba(230, 230, 230, 0.35);
  --intellij-scrollBar-mac-thumbBorderColor: rgba(0, 0, 0, 0.78);
  --intellij-scrollBar-mac-thumbColor: rgba(166, 166, 166, 1);
  --intellij-scrollBar-mac-trackColor: rgba(0, 0, 0, 1);
  --intellij-scrollBar-mac-transparent-hoverThumbBorderColor: rgba(0, 0, 0, 0.78);
  --intellij-scrollBar-mac-transparent-hoverThumbColor: rgba(230, 230, 230, 1);
  --intellij-scrollBar-mac-transparent-hoverTrackColor: rgba(230, 230, 230, 0.35);
  --intellij-scrollBar-mac-transparent-thumbBorderColor: rgba(0, 0, 0, 0.78);
  --intellij-scrollBar-mac-transparent-thumbColor: rgba(166, 166, 166, 1);
  --intellij-scrollBar-thumb: rgba(255, 255, 255, 1);
  --intellij-scrollBar-thumbBorderColor: rgba(0, 0, 0, 0.78);
  --intellij-scrollBar-thumbColor: rgba(179, 179, 179, 1);
  --intellij-scrollBar-thumbDarkShadow: rgba(0, 0, 0, 1);
  --intellij-scrollBar-thumbHighlight: rgba(255, 255, 255, 1);
  --intellij-scrollBar-thumbShadow: rgba(0, 0, 0, 0.27);
  --intellij-scrollBar-track: rgba(154, 154, 154, 1);
  --intellij-scrollBar-trackColor: rgba(0, 0, 0, 1);
  --intellij-scrollBar-trackHighlight: rgba(0, 0, 0, 1);
  --intellij-scrollBar-transparent-hoverThumbBorderColor: rgba(0, 0, 0, 0.78);
  --intellij-scrollBar-transparent-hoverThumbColor: rgba(230, 230, 230, 1);
  --intellij-scrollBar-transparent-hoverTrackColor: rgba(230, 230, 230, 0.35);
  --intellij-scrollBar-transparent-thumbBorderColor: rgba(0, 0, 0, 0.78);
  --intellij-scrollBar-transparent-thumbColor: rgba(179, 179, 179, 1);
  --intellij-scrollPane-background: rgba(0, 0, 0, 1);
  --intellij-scrollPane-font-family: "Inter", system-ui, sans-serif;
  --intellij-scrollPane-font-size: 13px;
  --intellij-scrollPane-foreground: rgba(255, 255, 255, 1);
  --intellij-scrollbar: rgba(154, 154, 154, 1);
  --intellij-searchEverywhere-advertiser-background: rgba(0, 0, 128, 1);
  --intellij-searchEverywhere-advertiser-foreground: rgba(255, 255, 255, 1);
  --intellij-searchEverywhere-header-background: rgba(0, 0, 0, 1);
  --intellij-searchEverywhere-list-separatorColor: rgba(179, 179, 179, 1);
  --intellij-searchEverywhere-searchField-background: rgba(0, 0, 0, 1);
  --intellij-searchEverywhere-searchField-borderColor: rgba(179, 179, 179, 1);
  --intellij-searchEverywhere-searchField-infoForeground: rgba(224, 134, 31, 1);
  --intellij-searchEverywhere-tab-selectedBackground: rgba(51, 51, 255, 1);
  --intellij-searchEverywhere-tab-selectedForeground: rgba(255, 255, 255, 1);
  --intellij-searchFieldWithExtension-background: rgba(0, 0, 0, 1);
  --intellij-searchMatch-endBackground: rgba(255, 211, 51, 1);
  --intellij-searchMatch-startBackground: rgba(255, 211, 51, 1);
  --intellij-segmentedButton-focusedSelectedButtonColor: rgba(15, 103, 128, 1);
  --intellij-segmentedButton-selectedButtonColor: rgba(0, 0, 0, 1);
  --intellij-segmentedButton-selectedEndBorderColor: rgba(26, 235, 255, 1);
  --intellij-segmentedButton-selectedStartBorderColor: rgba(26, 235, 255, 1);
  --intellij-separator-foreground: rgba(255, 255, 255, 1);
  --intellij-separator-highlight: rgba(255, 255, 255, 1);
  --intellij-separator-separatorColor: rgba(179, 179, 179, 1);
  --intellij-separator-shadow: rgba(0, 0, 0, 0.27);
  --intellij-shortcut-borderColor: rgba(230, 230, 230, 1);
  --intellij-sidePanel-background: rgba(0, 0, 0, 1);
  --intellij-slider-background: rgba(0, 0, 0, 1);
  --intellij-slider-buttonBorderColor: rgba(0, 0, 0, 1);
  --intellij-slider-buttonColor: rgba(255, 255, 255, 1);
  --intellij-slider-focus: rgba(0, 0, 0, 1);
  --intellij-slider-font-family: ".AppleSystemUIFont", system-ui, sans-serif;
  --intellij-slider-font-size: 11px;
  --intellij-slider-foreground: rgba(255, 255, 255, 1);
  --intellij-slider-highlight: rgba(255, 255, 255, 1);
  --intellij-slider-shadow: rgba(0, 0, 0, 0.27);
  --intellij-slider-tickColor: rgba(255, 255, 255, 1);
  --intellij-slider-trackColor: rgba(140, 140, 140, 1);
  --intellij-space-review-acceptedOutline: rgba(84, 181, 118, 1);
  --intellij-space-review-waitForResponseOutline: rgba(140, 211, 236, 1);
  --intellij-space-review-workingOutline: rgba(255, 148, 102, 1);
  --intellij-speedSearch-background: rgba(0, 0, 0, 1);
  --intellij-speedSearch-borderColor: rgba(230, 230, 230, 1);
  --intellij-speedSearch-errorForeground: rgba(250, 50, 50, 1);
  --intellij-speedSearch-foreground: rgba(255, 255, 255, 1);
  --intellij-spinner-background: rgba(0, 0, 0, 1);
  --intellij-spinner-font-family: "Inter", system-ui, sans-serif;
  --intellij-spinner-font-size: 13px;
  --intellij-spinner-foreground: rgba(255, 255, 255, 1);
  --intellij-splitPane-background: rgba(0, 0, 0, 1);
  --intellij-splitPane-darkShadow: rgba(0, 0, 0, 1);
  --intellij-splitPane-highlight: rgba(60, 63, 65, 1);
  --intellij-splitPane-shadow: rgba(0, 0, 0, 0.27);
  --intellij-splitPaneDivider-draggingColor: rgba(64, 64, 64, 1);
  --intellij-statusBar-background: rgba(0, 0, 0, 1);
  --intellij-statusBar-borderColor: rgba(179, 179, 179, 1);
  --intellij-statusBar-breadcrumbs-foreground: rgba(255, 255, 255, 1);
  --intellij-statusBar-hoverBackground: rgba(76, 80, 82, 1);
  --intellij-statusBar-lightEditBackground: rgba(47, 71, 94, 1);
  --intellij-statusBar-widget-foreground: rgba(255, 255, 255, 1);
  --intellij-tabbedPane-background: rgba(0, 0, 0, 1);
  --intellij-tabbedPane-contentAreaColor: rgba(179, 179, 179, 1);
  --intellij-tabbedPane-darkShadow: rgba(0, 0, 0, 1);
  --intellij-tabbedPane-disabledForeground: rgba(224, 134, 31, 1);
  --intellij-tabbedPane-disabledUnderlineColor: rgba(170, 110, 40, 1);
  --intellij-tabbedPane-focus: rgba(0, 0, 0, 0.85);
  --intellij-tabbedPane-focusColor: rgba(15, 103, 128, 1);
  --intellij-tabbedPane-font-family: "Inter", system-ui, sans-serif;
  --intellij-tabbedPane-font-size: 13px;
  --intellij-tabbedPane-foreground: rgba(255, 255, 255, 1);
  --intellij-tabbedPane-highlight: rgba(255, 255, 255, 1);
  --intellij-tabbedPane-hoverColor: rgba(15, 103, 128, 1);
  --intellij-tabbedPane-light: rgba(8, 74, 217, 1);
  --intellij-tabbedPane-nonSelectedTabTitleNormalColor: rgba(0, 0, 0, 1);
  --intellij-tabbedPane-selectedTabTitleDisabledColor: rgba(255, 255, 255, 0.55);
  --intellij-tabbedPane-selectedTabTitleNonFocusColor: rgba(0, 0, 0, 1);
  --intellij-tabbedPane-selectedTabTitleNormalColor: rgba(0, 0, 0, 0.85);
  --intellij-tabbedPane-selectedTabTitlePressedColor: rgba(0, 0, 0, 0.85);
  --intellij-tabbedPane-selectedTabTitleShadowDisabledColor: rgba(0, 0, 0, 0.25);
  --intellij-tabbedPane-selectedTabTitleShadowNormalColor: rgba(0, 0, 0, 0.39);
  --intellij-tabbedPane-shadow: rgba(0, 0, 0, 0.27);
  --intellij-tabbedPane-smallFont-family: ".AppleSystemUIFont", system-ui, sans-serif;
  --intellij-tabbedPane-smallFont-size: 11px;
  --intellij-tabbedPane-underlineColor: rgba(26, 235, 255, 1);
  --intellij-table-background: rgba(0, 0, 0, 1);
  --intellij-table-cellFocusRing: rgba(10, 96, 255, 1);
  --intellij-table-dropLineColor: rgba(0, 0, 0, 0.27);
  --intellij-table-dropLineShortColor: rgba(0, 0, 0, 1);
  --intellij-table-focusCellBackground: rgba(0, 0, 0, 1);
  --intellij-table-focusCellForeground: rgba(165, 205, 255, 1);
  --intellij-table-font-family: "Inter", system-ui, sans-serif;
  --intellij-table-font-size: 13px;
  --intellij-table-foreground: rgba(255, 255, 255, 1);
  --intellij-table-gridColor: rgba(79, 81, 82, 1);
  --intellij-table-lightSelectionBackground: rgba(0, 0, 102, 1);
  --intellij-table-lightSelectionForeground: rgba(255, 255, 255, 1);
  --intellij-table-lightSelectionInactiveBackground: rgba(56, 56, 56, 1);
  --intellij-table-lightSelectionInactiveForeground: rgba(255, 255, 255, 1);
  --intellij-table-selectionBackground: rgba(51, 51, 255, 1);
  --intellij-table-selectionForeground: rgba(255, 255, 255, 1);
  --intellij-table-selectionInactiveBackground: rgba(66, 66, 79, 1);
  --intellij-table-selectionInactiveForeground: rgba(187, 187, 187, 1);
  --intellij-table-sortIconColor: rgba(0, 0, 0, 0.27);
  --intellij-table-stripeColor: rgba(0, 0, 51, 1);
  --intellij-tableHeader-background: rgba(0, 0, 128, 1);
  --intellij-tableHeader-bottomSeparatorColor: rgba(51, 54, 56, 1);
  --intellij-tableHeader-focusCellBackground: rgba(255, 255, 255, 1);
  --intellij-tableHeader-font-family: "Inter", system-ui, sans-serif;
  --intellij-tableHeader-font-size: 13px;
  --intellij-tableHeader-foreground: rgba(255, 255, 255, 1);
  --intellij-tableHeader-separatorColor: rgba(179, 179, 179, 1);
  --intellij-tag-background: rgba(0, 0, 0, 1);
  --intellij-text: rgba(187, 187, 187, 1);
  --intellij-textArea-background: rgba(0, 0, 0, 1);
  --intellij-textArea-caretForeground: rgba(187, 187, 187, 1);
  --intellij-textArea-font-family: "Monospaced", system-ui, sans-serif;
  --intellij-textArea-font-size: 13px;
  --intellij-textArea-foreground: rgba(255, 255, 255, 1);
  --intellij-textArea-inactiveBackground: rgba(51, 33, 12, 1);
  --intellij-textArea-inactiveForeground: rgba(224, 134, 31, 1);
  --intellij-textArea-selectionBackground: rgba(51, 51, 255, 1);
  --intellij-textArea-selectionForeground: rgba(255, 255, 255, 1);
  --intellij-textComponent-selectionBackgroundInactive: rgba(66, 66, 79, 1);
  --intellij-textField-background: rgba(0, 0, 0, 1);
  --intellij-textField-caretForeground: rgba(187, 187, 187, 1);
  --intellij-textField-darkShadow: rgba(0, 0, 0, 1);
  --intellij-textField-disabledBackground: rgba(51, 33, 12, 1);
  --intellij-textField-font-family: "Inter", system-ui, sans-serif;
  --intellij-textField-font-size: 13px;
  --intellij-textField-foreground: rgba(255, 255, 255, 1);
  --intellij-textField-highlight: rgba(255, 255, 255, 1);
  --intellij-textField-inactiveBackground: rgba(51, 33, 12, 1);
  --intellij-textField-inactiveForeground: rgba(224, 134, 31, 1);
  --intellij-textField-light: rgba(8, 74, 217, 1);
  --intellij-textField-selectionBackground: rgba(51, 51, 255, 1);
  --intellij-textField-selectionForeground: rgba(255, 255, 255, 1);
  --intellij-textField-shadow: rgba(0, 0, 0, 0.27);
  --intellij-textHighlight: rgba(165, 205, 255, 1);
  --intellij-textHighlightText: rgba(0, 0, 0, 1);
  --intellij-textInactiveText: rgba(224, 134, 31, 1);
  --intellij-textPane-background: rgba(0, 0, 0, 1);
  --intellij-textPane-caretForeground: rgba(187, 187, 187, 1);
  --intellij-textPane-font-family: "Inter", system-ui, sans-serif;
  --intellij-textPane-font-size: 13px;
  --intellij-textPane-foreground: rgba(255, 255, 255, 1);
  --intellij-textPane-inactiveBackground: rgba(51, 33, 12, 1);
  --intellij-textPane-inactiveForeground: rgba(224, 134, 31, 1);
  --intellij-textPane-selectionBackground: rgba(51, 51, 255, 1);
  --intellij-textPane-selectionForeground: rgba(255, 255, 255, 1);
  --intellij-textText: rgba(187, 187, 187, 1);
  --intellij-tipOfTheDay-image-borderColor: rgba(230, 230, 230, 1);
  --intellij-titlePane-background: rgba(0, 0, 0, 1);
  --intellij-titlePane-button-hoverBackground: rgba(51, 51, 255, 1);
  --intellij-titledBorder-font-family: "Inter", system-ui, sans-serif;
  --intellij-titledBorder-font-size: 13px;
  --intellij-titledBorder-titleColor: rgba(187, 187, 187, 1);
  --intellij-toggleButton-background: rgba(0, 0, 0, 1);
  --intellij-toggleButton-borderColor: rgba(255, 255, 255, 1);
  --intellij-toggleButton-buttonColor: rgba(255, 255, 255, 1);
  --intellij-toggleButton-darkShadow: rgba(0, 0, 0, 1);
  --intellij-toggleButton-disabledText: rgba(224, 134, 31, 1);
  --intellij-toggleButton-font-family: "Inter", system-ui, sans-serif;
  --intellij-toggleButton-font-size: 13px;
  --intellij-toggleButton-foreground: rgba(255, 255, 255, 1);
  --intellij-toggleButton-highlight: rgba(255, 255, 255, 1);
  --intellij-toggleButton-light: rgba(8, 74, 217, 1);
  --intellij-toggleButton-offBackground: rgba(0, 0, 0, 1);
  --intellij-toggleButton-offForeground: rgba(255, 255, 255, 1);
  --intellij-toggleButton-onBackground: rgba(0, 0, 128, 1);
  --intellij-toggleButton-onForeground: rgba(255, 255, 255, 1);
  --intellij-toggleButton-shadow: rgba(0, 0, 0, 0.27);
  --intellij-toolBar-background: rgba(0, 0, 0, 1);
  --intellij-toolBar-borderHandleColor: rgba(140, 140, 140, 1);
  --intellij-toolBar-darkShadow: rgba(0, 0, 0, 1);
  --intellij-toolBar-dockingBackground: rgba(238, 238, 238, 1);
  --intellij-toolBar-dockingForeground: rgba(8, 74, 217, 1);
  --intellij-toolBar-floatingBackground: rgba(238, 238, 238, 1);
  --intellij-toolBar-floatingForeground: rgba(64, 64, 64, 1);
  --intellij-toolBar-font-family: "Inter", system-ui, sans-serif;
  --intellij-toolBar-font-size: 13px;
  --intellij-toolBar-foreground: rgba(255, 255, 255, 1);
  --intellij-toolBar-highlight: rgba(255, 255, 255, 1);
  --intellij-toolBar-light: rgba(8, 74, 217, 1);
  --intellij-toolBar-shadow: rgba(0, 0, 0, 0.27);
  --intellij-toolTip-actions-background: rgba(40, 26, 51, 1);
  --intellij-toolTip-background: rgba(69, 0, 115, 1);
  --intellij-toolTip-font-family: "Inter", system-ui, sans-serif;
  --intellij-toolTip-font-size: 13px;
  --intellij-toolTip-foreground: rgba(255, 255, 255, 1);
  --intellij-toolTip-infoForeground: rgba(224, 134, 31, 1);
  --intellij-toolWindow-button-hoverBackground: rgba(51, 51, 255, 1);
  --intellij-toolWindow-button-selectedBackground: rgba(51, 51, 255, 1);
  --intellij-toolWindow-button-selectedForeground: rgba(255, 255, 255, 1);
  --intellij-toolWindow-header-background: rgba(69, 0, 115, 1);
  --intellij-toolWindow-header-inactiveBackground: rgba(40, 26, 51, 1);
  --intellij-toolWindow-headerCloseButton-background: rgba(0, 0, 0, 1);
  --intellij-toolWindow-headerTab-hoverBackground: rgba(126, 0, 217, 1);
  --intellij-toolWindow-headerTab-hoverInactiveBackground: rgba(126, 0, 217, 1);
  --intellij-toolWindow-headerTab-inactiveUnderlineColor: rgba(26, 235, 255, 1);
  --intellij-toolWindow-headerTab-selectedBackground: rgba(156, 35, 217, 1);
  --intellij-toolWindow-headerTab-selectedInactiveBackground: rgba(77, 0, 128, 1);
  --intellij-toolWindow-headerTab-underlineColor: rgba(26, 235, 255, 1);
  --intellij-toolbar-floating-background: rgba(0, 0, 0, 1);
  --intellij-tooltip-actions-background: rgba(0, 0, 0, 1);
  --intellij-tooltip-background: rgba(0, 0, 0, 1);
  --intellij-tooltip-foreground: rgba(255, 255, 255, 1);
  --intellij-tooltip-learning-background: rgba(0, 0, 128, 1);
  --intellij-tooltip-learning-borderColor: rgba(255, 255, 255, 1);
  --intellij-tooltip-learning-foreground: rgba(255, 255, 255, 1);
  --intellij-tooltip-learning-iconBorderColor: rgba(255, 255, 255, 1);
  --intellij-tooltip-learning-iconFillColor: rgba(53, 83, 143, 1);
  --intellij-tooltip-learning-secondaryActionForeground: rgba(255, 255, 255, 1);
  --intellij-tooltip-learning-spanBackground: rgba(26, 235, 255, 1);
  --intellij-tooltip-learning-spanForeground: rgba(0, 0, 0, 1);
  --intellij-tooltip-learning-stepNumberForeground: rgba(255, 255, 255, 1);
  --intellij-tree-background: rgba(0, 0, 0, 1);
  --intellij-tree-dropLineColor: rgba(0, 0, 0, 0.27);
  --intellij-tree-font-family: "Inter", system-ui, sans-serif;
  --intellij-tree-font-size: 13px;
  --intellij-tree-foreground: rgba(255, 255, 255, 1);
  --intellij-tree-hash: rgba(80, 83, 85, 1);
  --intellij-tree-line: rgba(255, 255, 255, 1);
  --intellij-tree-modifiedItemForeground: rgba(79, 240, 255, 1);
  --intellij-tree-selectionBackground: rgba(51, 51, 255, 1);
  --intellij-tree-selectionBorderColor: rgba(8, 74, 217, 1);
  --intellij-tree-selectionForeground: rgba(255, 255, 255, 1);
  --intellij-tree-selectionInactiveBackground: rgba(66, 66, 79, 1);
  --intellij-tree-selectionInactiveForeground: rgba(187, 187, 187, 1);
  --intellij-tree-textBackground: rgba(60, 63, 65, 1);
  --intellij-tree-textForeground: rgba(187, 187, 187, 1);
  --intellij-uiDesigner-activity-borderColor: rgba(230, 230, 230, 1);
  --intellij-uiDesigner-canvas-background: rgba(0, 0, 0, 1);
  --intellij-uiDesigner-colorPicker-background: rgba(0, 0, 0, 1);
  --intellij-uiDesigner-colorPicker-foreground: rgba(255, 255, 255, 1);
  --intellij-uiDesigner-component-background: rgba(0, 0, 0, 1);
  --intellij-uiDesigner-component-borderColor: rgba(230, 230, 230, 1);
  --intellij-uiDesigner-component-foreground: rgba(255, 255, 255, 1);
  --intellij-uiDesigner-component-hoverBorderColor: rgba(26, 235, 255, 1);
  --intellij-uiDesigner-connector-borderColor: rgba(230, 230, 230, 1);
  --intellij-uiDesigner-connector-hoverBorderColor: rgba(26, 235, 255, 1);
  --intellij-uiDesigner-highStroke-foreground: rgba(255, 255, 255, 1);
  --intellij-uiDesigner-label-foreground: rgba(255, 255, 255, 1);
  --intellij-uiDesigner-list-selectionBackground: rgba(51, 51, 255, 1);
  --intellij-uiDesigner-panel-background: rgba(0, 0, 0, 1);
  --intellij-uiDesigner-percent-foreground: rgba(255, 255, 255, 1);
  --intellij-uiDesigner-placeholder-background: rgba(0, 0, 0, 1);
  --intellij-uiDesigner-placeholder-borderColor: rgba(230, 230, 230, 1);
  --intellij-uiDesigner-placeholder-foreground: rgba(255, 255, 255, 1);
  --intellij-uiDesigner-placeholder-selectedForeground: rgba(156, 205, 255, 1);
  --intellij-validationTooltip-errorBackground: rgba(128, 0, 2, 1);
  --intellij-validationTooltip-errorBorderColor: rgba(230, 25, 75, 1);
  --intellij-validationTooltip-warningBackground: rgba(128, 70, 5, 1);
  --intellij-validationTooltip-warningBorderColor: rgba(245, 130, 49, 1);
  --intellij-versionControl-fileHistory-commit-selectedBranchBackground: rgba(13, 13, 64, 1);
  --intellij-versionControl-hgLog-bookmarkIconColor: rgba(159, 121, 181, 1);
  --intellij-versionControl-hgLog-branchIconColor: rgba(60, 180, 92, 1);
  --intellij-versionControl-hgLog-closedBranchIconColor: rgba(255, 95, 111, 1);
  --intellij-versionControl-hgLog-headIconColor: rgba(195, 30, 140, 1);
  --intellij-versionControl-hgLog-localTagIconColor: rgba(0, 243, 243, 1);
  --intellij-versionControl-hgLog-mqTagIconColor: rgba(0, 85, 255, 1);
  --intellij-versionControl-hgLog-tagIconColor: rgba(153, 153, 153, 1);
  --intellij-versionControl-hgLog-tipIconColor: rgba(225, 199, 49, 1);
  --intellij-versionControl-log-commit-currentBranchBackground: rgba(13, 13, 64, 1);
  --intellij-versionControl-log-commit-reference-foreground: rgba(255, 255, 255, 1);
  --intellij-versionControl-log-commit-unmatchedForeground: rgba(224, 134, 31, 1);
  --intellij-versionControl-markerPopup-toolbar-background: rgba(0, 0, 0, 1);
  --intellij-versionControl-refLabel-backgroundBase: rgba(51, 51, 255, 1);
  --intellij-versionControl-refLabel-foreground: rgba(255, 255, 255, 1);
  --intellij-viewport-background: rgba(0, 0, 0, 1);
  --intellij-viewport-font-family: "Inter", system-ui, sans-serif;
  --intellij-viewport-font-size: 13px;
  --intellij-viewport-foreground: rgba(255, 255, 255, 1);
  --intellij-welcomeScreen-background: rgba(0, 0, 0, 1);
  --intellij-welcomeScreen-details-background: rgba(0, 0, 0, 1);
  --intellij-welcomeScreen-projects-actions-background: rgba(0, 0, 0, 1);
  --intellij-welcomeScreen-projects-actions-selectionBackground: rgba(51, 51, 255, 1);
  --intellij-welcomeScreen-projects-actions-selectionBorderColor: rgba(53, 116, 240, 1);
  --intellij-welcomeScreen-projects-selectionInactiveBackground: rgba(51, 51, 255, 1);
  --intellij-welcomeScreen-separatorColor: rgba(230, 230, 230, 1);
  --intellij-welcomeScreen-sidePanel-background: rgba(0, 0, 0, 1);
  --intellij-window: rgba(60, 63, 65, 1);
  --intellij-windowBorder: rgba(154, 154, 154, 1);
  --intellij-windowText: rgba(0, 0, 0, 0.85);
}
::-webkit-scrollbar {
  width: 14px;
  height: 14px;
  background-color: rgba(0, 0, 0, 0);
}
::-webkit-scrollbar-track {
  background-color: rgba(0, 0, 0, 0);
}
::-webkit-scrollbar-track:hover {
  background-color: rgba(0, 0, 0, 0);
}
::-webkit-scrollbar-thumb {
  background-color: rgba(255, 255, 255, 0.745098);
  border-radius: 7px;
  border-width: 3px;
  border-style: solid;
  border-color: rgba(0, 0, 0, 0);
  background-clip: padding-box;
  outline: 1px solid rgba(0, 0, 0, 1);
  outline-offset: -3px;
}
::-webkit-scrollbar-thumb:hover {
  background-color: rgba(255, 255, 255, 0.745098);
  border-radius: 7px;
  border-width: 3px;
  border-style: solid;
  border-color: rgba(0, 0, 0, 0);
  background-clip: padding-box;
  outline: 1px solid rgba(0, 0, 0, 1);
  outline-offset: -3px;
}
::-webkit-scrollbar-corner {
  background-color: rgba(0, 0, 0, 0);
}
::-webkit-scrollbar-button {
  display: none;
}
:root {
  background: var(--intellij-tree-background);
  color: var(--intellij-tree-foreground);
}
a {
  color: var(--intellij-hyperlink-linkColor);
}
