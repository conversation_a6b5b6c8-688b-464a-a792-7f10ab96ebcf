/**
 * These values were copied over by hand from a webview in VSCode.
 *
 * Find using the following:
 *     document.querySelector('#_defaultStyles').parentNode.parentNode.style.cssText
 */

:root {
  --text-link-decoration: none;
  --vscode-font-family: -apple-system, BlinkMacSystemFont, sans-serif;
  --vscode-font-weight: normal;
  --vscode-font-size: 13px;
  --vscode-editor-font-family: Operator Mono, "Droid Sans Mono", "monospace", monospace;
  --vscode-editor-font-weight: 360;
  --vscode-editor-font-size: 17px;
  --vscode-foreground: #616161;
  --vscode-disabledForeground: rgba(97, 97, 97, 0.5);
  --vscode-errorForeground: #a1260d;
  --vscode-descriptionForeground: #717171;
  --vscode-icon-foreground: #424242;
  --vscode-focusBorder: #b49471;
  --vscode-selection-background: rgba(135, 139, 145, 0.5);
  --vscode-textLink-foreground: #006ab1;
  --vscode-textLink-activeForeground: #006ab1;
  --vscode-textSeparator-foreground: rgba(0, 0, 0, 0.18);
  --vscode-textPreformat-foreground: #a31515;
  --vscode-textPreformat-background: rgba(0, 0, 0, 0.1);
  --vscode-textBlockQuote-background: #f2f2f2;
  --vscode-textBlockQuote-border: rgba(0, 122, 204, 0.5);
  --vscode-textCodeBlock-background: rgba(220, 220, 220, 0.4);
  --vscode-sash-hoverBorder: #b49471;
  --vscode-badge-background: rgba(181, 137, 0, 0.67);
  --vscode-badge-foreground: #333333;
  --vscode-activityWarningBadge-foreground: #ffffff;
  --vscode-activityWarningBadge-background: #bf8803;
  --vscode-activityErrorBadge-foreground: #ffffff;
  --vscode-activityErrorBadge-background: #e51400;
  --vscode-scrollbar-shadow: #dddddd;
  --vscode-scrollbarSlider-background: rgba(100, 100, 100, 0.4);
  --vscode-scrollbarSlider-hoverBackground: rgba(100, 100, 100, 0.7);
  --vscode-scrollbarSlider-activeBackground: rgba(0, 0, 0, 0.6);
  --vscode-progressBar-background: #b58900;
  --vscode-chart-line: #236b8e;
  --vscode-chart-axis: rgba(0, 0, 0, 0.6);
  --vscode-chart-guide: rgba(0, 0, 0, 0.2);
  --vscode-editor-background: #fdf6e3;
  --vscode-editor-foreground: #657b83;
  --vscode-editorStickyScroll-background: #fdf6e3;
  --vscode-editorStickyScrollHover-background: #f0f0f0;
  --vscode-editorStickyScroll-shadow: #dddddd;
  --vscode-editorWidget-background: #eee8d5;
  --vscode-editorWidget-foreground: #616161;
  --vscode-editorWidget-border: #c8c8c8;
  --vscode-editorError-foreground: #e51400;
  --vscode-editorWarning-foreground: #bf8803;
  --vscode-editorInfo-foreground: #1a85ff;
  --vscode-editorHint-foreground: #6c6c6c;
  --vscode-editorLink-activeForeground: #0000ff;
  --vscode-editor-selectionBackground: #eee8d5;
  --vscode-editor-inactiveSelectionBackground: rgba(238, 232, 213, 0.5);
  --vscode-editor-selectionHighlightBackground: rgba(243, 239, 225, 0.6);
  --vscode-editor-compositionBorder: #000000;
  --vscode-editor-findMatchBackground: #a8ac94;
  --vscode-editor-findMatchHighlightBackground: rgba(234, 92, 0, 0.33);
  --vscode-editor-findRangeHighlightBackground: rgba(180, 180, 180, 0.3);
  --vscode-editor-hoverHighlightBackground: rgba(173, 214, 255, 0.15);
  --vscode-editorHoverWidget-background: #ccc4b0;
  --vscode-editorHoverWidget-foreground: #616161;
  --vscode-editorHoverWidget-border: #c8c8c8;
  --vscode-editorHoverWidget-statusBarBackground: #c5bba5;
  --vscode-editorInlayHint-foreground: #969696;
  --vscode-editorInlayHint-background: rgba(181, 137, 0, 0.07);
  --vscode-editorInlayHint-typeForeground: #969696;
  --vscode-editorInlayHint-typeBackground: rgba(181, 137, 0, 0.07);
  --vscode-editorInlayHint-parameterForeground: #969696;
  --vscode-editorInlayHint-parameterBackground: rgba(181, 137, 0, 0.07);
  --vscode-editorLightBulb-foreground: #ddb100;
  --vscode-editorLightBulbAutoFix-foreground: #007acc;
  --vscode-editorLightBulbAi-foreground: #ddb100;
  --vscode-editor-snippetTabstopHighlightBackground: rgba(10, 50, 100, 0.2);
  --vscode-editor-snippetFinalTabstopHighlightBorder: rgba(10, 50, 100, 0.5);
  --vscode-diffEditor-insertedTextBackground: rgba(156, 204, 44, 0.25);
  --vscode-diffEditor-removedTextBackground: rgba(255, 0, 0, 0.2);
  --vscode-diffEditor-insertedLineBackground: rgba(155, 185, 85, 0.2);
  --vscode-diffEditor-removedLineBackground: rgba(255, 0, 0, 0.2);
  --vscode-diffEditor-diagonalFill: rgba(34, 34, 34, 0.2);
  --vscode-diffEditor-unchangedRegionBackground: #eee8d5;
  --vscode-diffEditor-unchangedRegionForeground: #616161;
  --vscode-diffEditor-unchangedCodeBackground: rgba(184, 184, 184, 0.16);
  --vscode-widget-shadow: rgba(0, 0, 0, 0.16);
  --vscode-toolbar-hoverBackground: rgba(184, 184, 184, 0.31);
  --vscode-toolbar-activeBackground: rgba(166, 166, 166, 0.31);
  --vscode-breadcrumb-foreground: rgba(97, 97, 97, 0.8);
  --vscode-breadcrumb-background: #fdf6e3;
  --vscode-breadcrumb-focusForeground: #4e4e4e;
  --vscode-breadcrumb-activeSelectionForeground: #4e4e4e;
  --vscode-breadcrumbPicker-background: #eee8d5;
  --vscode-merge-currentHeaderBackground: rgba(64, 200, 174, 0.5);
  --vscode-merge-currentContentBackground: rgba(64, 200, 174, 0.2);
  --vscode-merge-incomingHeaderBackground: rgba(64, 166, 255, 0.5);
  --vscode-merge-incomingContentBackground: rgba(64, 166, 255, 0.2);
  --vscode-merge-commonHeaderBackground: rgba(96, 96, 96, 0.4);
  --vscode-merge-commonContentBackground: rgba(96, 96, 96, 0.16);
  --vscode-editorOverviewRuler-currentContentForeground: rgba(64, 200, 174, 0.5);
  --vscode-editorOverviewRuler-incomingContentForeground: rgba(64, 166, 255, 0.5);
  --vscode-editorOverviewRuler-commonContentForeground: rgba(96, 96, 96, 0.4);
  --vscode-editorOverviewRuler-findMatchForeground: rgba(209, 134, 22, 0.49);
  --vscode-editorOverviewRuler-selectionHighlightForeground: rgba(160, 160, 160, 0.8);
  --vscode-problemsErrorIcon-foreground: #e51400;
  --vscode-problemsWarningIcon-foreground: #bf8803;
  --vscode-problemsInfoIcon-foreground: #1a85ff;
  --vscode-minimap-findMatchHighlight: #d18616;
  --vscode-minimap-selectionOccurrenceHighlight: #c9c9c9;
  --vscode-minimap-selectionHighlight: #eee8d5;
  --vscode-minimap-infoHighlight: #1a85ff;
  --vscode-minimap-warningHighlight: #bf8803;
  --vscode-minimap-errorHighlight: rgba(255, 18, 18, 0.7);
  --vscode-minimap-foregroundOpacity: #000000;
  --vscode-minimapSlider-background: rgba(100, 100, 100, 0.2);
  --vscode-minimapSlider-hoverBackground: rgba(100, 100, 100, 0.35);
  --vscode-minimapSlider-activeBackground: rgba(0, 0, 0, 0.3);
  --vscode-charts-foreground: #616161;
  --vscode-charts-lines: rgba(97, 97, 97, 0.5);
  --vscode-charts-red: #e51400;
  --vscode-charts-blue: #1a85ff;
  --vscode-charts-yellow: #bf8803;
  --vscode-charts-orange: #d18616;
  --vscode-charts-green: #388a34;
  --vscode-charts-purple: #652d90;
  --vscode-input-background: #ddd6c1;
  --vscode-input-foreground: #586e75;
  --vscode-inputOption-activeBorder: #d3af86;
  --vscode-inputOption-hoverBackground: rgba(184, 184, 184, 0.31);
  --vscode-inputOption-activeBackground: rgba(180, 148, 113, 0.2);
  --vscode-inputOption-activeForeground: #000000;
  --vscode-input-placeholderForeground: rgba(88, 110, 117, 0.67);
  --vscode-inputValidation-infoBackground: #d6ecf2;
  --vscode-inputValidation-infoBorder: #007acc;
  --vscode-inputValidation-warningBackground: #f6f5d2;
  --vscode-inputValidation-warningBorder: #b89500;
  --vscode-inputValidation-errorBackground: #f2dede;
  --vscode-inputValidation-errorBorder: #be1100;
  --vscode-dropdown-background: #eee8d5;
  --vscode-dropdown-foreground: #616161;
  --vscode-dropdown-border: #d3af86;
  --vscode-button-foreground: #ffffff;
  --vscode-button-separator: rgba(255, 255, 255, 0.4);
  --vscode-button-background: #ac9d57;
  --vscode-button-hoverBackground: #8b7e44;
  --vscode-button-secondaryForeground: #ffffff;
  --vscode-button-secondaryBackground: #5f6a79;
  --vscode-button-secondaryHoverBackground: #4c5561;
  --vscode-radio-activeForeground: #000000;
  --vscode-radio-activeBackground: rgba(180, 148, 113, 0.2);
  --vscode-radio-activeBorder: #d3af86;
  --vscode-radio-inactiveBorder: rgba(0, 0, 0, 0.2);
  --vscode-radio-inactiveHoverBackground: rgba(184, 184, 184, 0.31);
  --vscode-checkbox-background: #eee8d5;
  --vscode-checkbox-selectBackground: #eee8d5;
  --vscode-checkbox-foreground: #616161;
  --vscode-checkbox-border: #d3af86;
  --vscode-checkbox-selectBorder: #424242;
  --vscode-keybindingLabel-background: rgba(221, 221, 221, 0.4);
  --vscode-keybindingLabel-foreground: #555555;
  --vscode-keybindingLabel-border: rgba(204, 204, 204, 0.4);
  --vscode-keybindingLabel-bottomBorder: rgba(187, 187, 187, 0.4);
  --vscode-list-focusOutline: #b49471;
  --vscode-list-activeSelectionBackground: #dfca88;
  --vscode-list-activeSelectionForeground: #6c6c6c;
  --vscode-list-inactiveSelectionBackground: #d1cbb8;
  --vscode-list-hoverBackground: rgba(223, 202, 136, 0.27);
  --vscode-list-dropBackground: #d6ebff;
  --vscode-list-dropBetweenBackground: #424242;
  --vscode-list-highlightForeground: #b58900;
  --vscode-list-focusHighlightForeground: #b58900;
  --vscode-list-invalidItemForeground: #b89500;
  --vscode-list-errorForeground: #b01011;
  --vscode-list-warningForeground: #855f00;
  --vscode-listFilterWidget-background: #eee8d5;
  --vscode-listFilterWidget-outline: rgba(0, 0, 0, 0);
  --vscode-listFilterWidget-noMatchesOutline: #be1100;
  --vscode-listFilterWidget-shadow: rgba(0, 0, 0, 0.16);
  --vscode-list-filterMatchBackground: rgba(234, 92, 0, 0.33);
  --vscode-list-deemphasizedForeground: #8e8e90;
  --vscode-tree-indentGuidesStroke: #a9a9a9;
  --vscode-tree-inactiveIndentGuidesStroke: rgba(169, 169, 169, 0.4);
  --vscode-tree-tableColumnsBorder: rgba(97, 97, 97, 0.13);
  --vscode-tree-tableOddRowsBackground: rgba(97, 97, 97, 0.04);
  --vscode-editorActionList-background: #eee8d5;
  --vscode-editorActionList-foreground: #616161;
  --vscode-editorActionList-focusForeground: #6c6c6c;
  --vscode-editorActionList-focusBackground: #dfca88;
  --vscode-menu-foreground: #616161;
  --vscode-menu-background: #eee8d5;
  --vscode-menu-selectionForeground: #6c6c6c;
  --vscode-menu-selectionBackground: #dfca88;
  --vscode-menu-separatorBackground: #d4d4d4;
  --vscode-quickInput-background: #eee8d5;
  --vscode-quickInput-foreground: #616161;
  --vscode-quickInputTitle-background: rgba(0, 0, 0, 0.06);
  --vscode-pickerGroup-foreground: rgba(42, 161, 152, 0.6);
  --vscode-pickerGroup-border: rgba(42, 161, 152, 0.6);
  --vscode-quickInputList-focusForeground: #6c6c6c;
  --vscode-quickInputList-focusBackground: rgba(223, 202, 136, 0.4);
  --vscode-search-resultsInfoForeground: #616161;
  --vscode-searchEditor-findMatchBackground: rgba(234, 92, 0, 0.22);
  --vscode-editor-lineHighlightBackground: #eee8d5;
  --vscode-editor-lineHighlightBorder: #eeeeee;
  --vscode-editor-rangeHighlightBackground: rgba(253, 255, 0, 0.2);
  --vscode-editor-symbolHighlightBackground: rgba(234, 92, 0, 0.33);
  --vscode-editorCursor-foreground: #657b83;
  --vscode-editorMultiCursor-primary\.foreground: #657b83;
  --vscode-editorMultiCursor-secondary\.foreground: #657b83;
  --vscode-editorWhitespace-foreground: rgba(88, 110, 117, 0.5);
  --vscode-editorLineNumber-foreground: #237893;
  --vscode-editorIndentGuide-background: rgba(88, 110, 117, 0.5);
  --vscode-editorIndentGuide-activeBackground: rgba(8, 30, 37, 0.5);
  --vscode-editorIndentGuide-background1: rgba(88, 110, 117, 0.5);
  --vscode-editorIndentGuide-background2: rgba(0, 0, 0, 0);
  --vscode-editorIndentGuide-background3: rgba(0, 0, 0, 0);
  --vscode-editorIndentGuide-background4: rgba(0, 0, 0, 0);
  --vscode-editorIndentGuide-background5: rgba(0, 0, 0, 0);
  --vscode-editorIndentGuide-background6: rgba(0, 0, 0, 0);
  --vscode-editorIndentGuide-activeBackground1: rgba(8, 30, 37, 0.5);
  --vscode-editorIndentGuide-activeBackground2: rgba(0, 0, 0, 0);
  --vscode-editorIndentGuide-activeBackground3: rgba(0, 0, 0, 0);
  --vscode-editorIndentGuide-activeBackground4: rgba(0, 0, 0, 0);
  --vscode-editorIndentGuide-activeBackground5: rgba(0, 0, 0, 0);
  --vscode-editorIndentGuide-activeBackground6: rgba(0, 0, 0, 0);
  --vscode-editorActiveLineNumber-foreground: #0b216f;
  --vscode-editorLineNumber-activeForeground: #567983;
  --vscode-editorRuler-foreground: #d3d3d3;
  --vscode-editorCodeLens-foreground: #919191;
  --vscode-editorBracketMatch-background: rgba(0, 100, 0, 0.1);
  --vscode-editorBracketMatch-border: #b9b9b9;
  --vscode-editorOverviewRuler-border: rgba(127, 127, 127, 0.3);
  --vscode-editorGutter-background: #fdf6e3;
  --vscode-editorUnnecessaryCode-opacity: rgba(0, 0, 0, 0.47);
  --vscode-editorGhostText-foreground: rgba(0, 0, 0, 0.47);
  --vscode-editorOverviewRuler-rangeHighlightForeground: rgba(0, 122, 204, 0.6);
  --vscode-editorOverviewRuler-errorForeground: rgba(255, 18, 18, 0.7);
  --vscode-editorOverviewRuler-warningForeground: #bf8803;
  --vscode-editorOverviewRuler-infoForeground: #1a85ff;
  --vscode-editorBracketHighlight-foreground1: #0431fa;
  --vscode-editorBracketHighlight-foreground2: #319331;
  --vscode-editorBracketHighlight-foreground3: #7b3814;
  --vscode-editorBracketHighlight-foreground4: rgba(0, 0, 0, 0);
  --vscode-editorBracketHighlight-foreground5: rgba(0, 0, 0, 0);
  --vscode-editorBracketHighlight-foreground6: rgba(0, 0, 0, 0);
  --vscode-editorBracketHighlight-unexpectedBracket\.foreground: rgba(255, 18, 18, 0.8);
  --vscode-editorBracketPairGuide-background1: rgba(0, 0, 0, 0);
  --vscode-editorBracketPairGuide-background2: rgba(0, 0, 0, 0);
  --vscode-editorBracketPairGuide-background3: rgba(0, 0, 0, 0);
  --vscode-editorBracketPairGuide-background4: rgba(0, 0, 0, 0);
  --vscode-editorBracketPairGuide-background5: rgba(0, 0, 0, 0);
  --vscode-editorBracketPairGuide-background6: rgba(0, 0, 0, 0);
  --vscode-editorBracketPairGuide-activeBackground1: rgba(0, 0, 0, 0);
  --vscode-editorBracketPairGuide-activeBackground2: rgba(0, 0, 0, 0);
  --vscode-editorBracketPairGuide-activeBackground3: rgba(0, 0, 0, 0);
  --vscode-editorBracketPairGuide-activeBackground4: rgba(0, 0, 0, 0);
  --vscode-editorBracketPairGuide-activeBackground5: rgba(0, 0, 0, 0);
  --vscode-editorBracketPairGuide-activeBackground6: rgba(0, 0, 0, 0);
  --vscode-editorUnicodeHighlight-border: #bf8803;
  --vscode-diffEditor-move\.border: rgba(139, 139, 139, 0.61);
  --vscode-diffEditor-moveActive\.border: #ffa500;
  --vscode-diffEditor-unchangedRegionShadow: rgba(115, 115, 115, 0.75);
  --vscode-editorOverviewRuler-bracketMatchForeground: #a0a0a0;
  --vscode-actionBar-toggledBackground: rgba(180, 148, 113, 0.2);
  --vscode-symbolIcon-arrayForeground: #616161;
  --vscode-symbolIcon-booleanForeground: #616161;
  --vscode-symbolIcon-classForeground: #d67e00;
  --vscode-symbolIcon-colorForeground: #616161;
  --vscode-symbolIcon-constantForeground: #616161;
  --vscode-symbolIcon-constructorForeground: #652d90;
  --vscode-symbolIcon-enumeratorForeground: #d67e00;
  --vscode-symbolIcon-enumeratorMemberForeground: #007acc;
  --vscode-symbolIcon-eventForeground: #d67e00;
  --vscode-symbolIcon-fieldForeground: #007acc;
  --vscode-symbolIcon-fileForeground: #616161;
  --vscode-symbolIcon-folderForeground: #616161;
  --vscode-symbolIcon-functionForeground: #652d90;
  --vscode-symbolIcon-interfaceForeground: #007acc;
  --vscode-symbolIcon-keyForeground: #616161;
  --vscode-symbolIcon-keywordForeground: #616161;
  --vscode-symbolIcon-methodForeground: #652d90;
  --vscode-symbolIcon-moduleForeground: #616161;
  --vscode-symbolIcon-namespaceForeground: #616161;
  --vscode-symbolIcon-nullForeground: #616161;
  --vscode-symbolIcon-numberForeground: #616161;
  --vscode-symbolIcon-objectForeground: #616161;
  --vscode-symbolIcon-operatorForeground: #616161;
  --vscode-symbolIcon-packageForeground: #616161;
  --vscode-symbolIcon-propertyForeground: #616161;
  --vscode-symbolIcon-referenceForeground: #616161;
  --vscode-symbolIcon-snippetForeground: #616161;
  --vscode-symbolIcon-stringForeground: #616161;
  --vscode-symbolIcon-structForeground: #616161;
  --vscode-symbolIcon-textForeground: #616161;
  --vscode-symbolIcon-typeParameterForeground: #616161;
  --vscode-symbolIcon-unitForeground: #616161;
  --vscode-symbolIcon-variableForeground: #007acc;
  --vscode-peekViewTitle-background: #eee8d5;
  --vscode-peekViewTitleLabel-foreground: #000000;
  --vscode-peekViewTitleDescription-foreground: #616161;
  --vscode-peekView-border: #b58900;
  --vscode-peekViewResult-background: #eee8d5;
  --vscode-peekViewResult-lineForeground: #646465;
  --vscode-peekViewResult-fileForeground: #1e1e1e;
  --vscode-peekViewResult-selectionBackground: rgba(51, 153, 255, 0.2);
  --vscode-peekViewResult-selectionForeground: #6c6c6c;
  --vscode-peekViewEditor-background: #fffbf2;
  --vscode-peekViewEditorGutter-background: #fffbf2;
  --vscode-peekViewEditorStickyScroll-background: #fffbf2;
  --vscode-peekViewResult-matchHighlightBackground: rgba(234, 92, 0, 0.3);
  --vscode-peekViewEditor-matchHighlightBackground: rgba(119, 68, 170, 0.25);
  --vscode-editor-foldBackground: rgba(238, 232, 213, 0.3);
  --vscode-editor-foldPlaceholderForeground: #808080;
  --vscode-editorGutter-foldingControlForeground: #424242;
  --vscode-editorSuggestWidget-background: #eee8d5;
  --vscode-editorSuggestWidget-border: #c8c8c8;
  --vscode-editorSuggestWidget-foreground: #657b83;
  --vscode-editorSuggestWidget-selectedForeground: #6c6c6c;
  --vscode-editorSuggestWidget-selectedBackground: rgba(223, 202, 136, 0.4);
  --vscode-editorSuggestWidget-highlightForeground: #b58900;
  --vscode-editorSuggestWidget-focusHighlightForeground: #b58900;
  --vscode-editorSuggestWidgetStatus-foreground: rgba(101, 123, 131, 0.5);
  --vscode-inlineEdit-indicator\.foreground: #ffffff;
  --vscode-inlineEdit-indicator\.background: #ac9d57;
  --vscode-inlineEdit-indicator\.border: rgba(255, 255, 255, 0.4);
  --vscode-inlineEdit-originalBackground: rgba(255, 0, 0, 0.08);
  --vscode-inlineEdit-modifiedBackground: rgba(156, 204, 44, 0.1);
  --vscode-inlineEdit-originalChangedLineBackground: rgba(0, 0, 0, 0);
  --vscode-inlineEdit-originalChangedTextBackground: rgba(255, 0, 0, 0.2);
  --vscode-inlineEdit-modifiedChangedLineBackground: rgba(0, 0, 0, 0);
  --vscode-inlineEdit-modifiedChangedTextBackground: rgba(156, 204, 44, 0.25);
  --vscode-inlineEdit-border: #cacaca;
  --vscode-editorMarkerNavigationError-background: #e51400;
  --vscode-editorMarkerNavigationError-headerBackground: rgba(229, 20, 0, 0.1);
  --vscode-editorMarkerNavigationWarning-background: #bf8803;
  --vscode-editorMarkerNavigationWarning-headerBackground: rgba(191, 136, 3, 0.1);
  --vscode-editorMarkerNavigationInfo-background: #1a85ff;
  --vscode-editorMarkerNavigationInfo-headerBackground: rgba(26, 133, 255, 0.1);
  --vscode-editorMarkerNavigation-background: #fdf6e3;
  --vscode-editor-linkedEditingBackground: rgba(255, 0, 0, 0.3);
  --vscode-editor-wordHighlightBackground: rgba(87, 87, 87, 0.25);
  --vscode-editor-wordHighlightStrongBackground: rgba(14, 99, 156, 0.25);
  --vscode-editor-wordHighlightTextBackground: rgba(87, 87, 87, 0.25);
  --vscode-editorOverviewRuler-wordHighlightForeground: rgba(160, 160, 160, 0.8);
  --vscode-editorOverviewRuler-wordHighlightStrongForeground: rgba(192, 160, 192, 0.8);
  --vscode-editorOverviewRuler-wordHighlightTextForeground: rgba(160, 160, 160, 0.8);
  --vscode-editorHoverWidget-highlightForeground: #b58900;
  --vscode-editor-placeholder\.foreground: rgba(0, 0, 0, 0.47);
  --vscode-tab-activeBackground: #fdf6e3;
  --vscode-tab-unfocusedActiveBackground: #fdf6e3;
  --vscode-tab-inactiveBackground: #d3cbb7;
  --vscode-tab-unfocusedInactiveBackground: #d3cbb7;
  --vscode-tab-activeForeground: #333333;
  --vscode-tab-inactiveForeground: #586e75;
  --vscode-tab-unfocusedActiveForeground: rgba(51, 51, 51, 0.7);
  --vscode-tab-unfocusedInactiveForeground: rgba(88, 110, 117, 0.5);
  --vscode-tab-border: #ddd6c1;
  --vscode-tab-lastPinnedBorder: #fdf6e3;
  --vscode-tab-selectedBackground: #fdf6e3;
  --vscode-tab-selectedForeground: #333333;
  --vscode-tab-dragAndDropBorder: #333333;
  --vscode-tab-activeModifiedBorder: #cb4b16;
  --vscode-tab-inactiveModifiedBorder: rgba(203, 75, 22, 0.5);
  --vscode-tab-unfocusedActiveModifiedBorder: rgba(203, 75, 22, 0.7);
  --vscode-tab-unfocusedInactiveModifiedBorder: rgba(203, 75, 22, 0.25);
  --vscode-editorPane-background: #fdf6e3;
  --vscode-editorGroupHeader-tabsBackground: #d9d2c2;
  --vscode-editorGroupHeader-noTabsBackground: #fdf6e3;
  --vscode-editorGroup-border: #ddd6c1;
  --vscode-editorGroup-dropBackground: rgba(221, 214, 193, 0.67);
  --vscode-editorGroup-dropIntoPromptForeground: #616161;
  --vscode-editorGroup-dropIntoPromptBackground: #eee8d5;
  --vscode-sideBySideEditor-horizontalBorder: #ddd6c1;
  --vscode-sideBySideEditor-verticalBorder: #ddd6c1;
  --vscode-panel-background: #fdf6e3;
  --vscode-panel-border: #ddd6c1;
  --vscode-panelTitle-activeForeground: #424242;
  --vscode-panelTitle-inactiveForeground: rgba(66, 66, 66, 0.75);
  --vscode-panelTitle-activeBorder: #424242;
  --vscode-panelInput-border: #dddddd;
  --vscode-panel-dropBorder: #424242;
  --vscode-panelSection-dropBackground: rgba(221, 214, 193, 0.67);
  --vscode-panelSectionHeader-background: rgba(128, 128, 128, 0.2);
  --vscode-panelSection-border: #ddd6c1;
  --vscode-panelStickyScroll-background: #fdf6e3;
  --vscode-panelStickyScroll-shadow: #dddddd;
  --vscode-banner-background: #c6a435;
  --vscode-banner-foreground: #6c6c6c;
  --vscode-banner-iconForeground: #1a85ff;
  --vscode-statusBar-foreground: #586e75;
  --vscode-statusBar-noFolderForeground: #586e75;
  --vscode-statusBar-background: #eee8d5;
  --vscode-statusBar-noFolderBackground: #eee8d5;
  --vscode-statusBar-focusBorder: #586e75;
  --vscode-statusBarItem-activeBackground: rgba(255, 255, 255, 0.18);
  --vscode-statusBarItem-focusBorder: #586e75;
  --vscode-statusBarItem-hoverBackground: rgba(255, 255, 255, 0.12);
  --vscode-statusBarItem-hoverForeground: #586e75;
  --vscode-statusBarItem-compactHoverBackground: rgba(255, 255, 255, 0.2);
  --vscode-statusBarItem-prominentForeground: #586e75;
  --vscode-statusBarItem-prominentBackground: #ddd6c1;
  --vscode-statusBarItem-prominentHoverForeground: #586e75;
  --vscode-statusBarItem-prominentHoverBackground: rgba(221, 214, 193, 0.6);
  --vscode-statusBarItem-errorBackground: #611708;
  --vscode-statusBarItem-errorForeground: #ffffff;
  --vscode-statusBarItem-errorHoverForeground: #586e75;
  --vscode-statusBarItem-errorHoverBackground: rgba(255, 255, 255, 0.12);
  --vscode-statusBarItem-warningBackground: #725102;
  --vscode-statusBarItem-warningForeground: #ffffff;
  --vscode-statusBarItem-warningHoverForeground: #586e75;
  --vscode-statusBarItem-warningHoverBackground: rgba(255, 255, 255, 0.12);
  --vscode-activityBar-background: #ddd6c1;
  --vscode-activityBar-foreground: #584c27;
  --vscode-activityBar-inactiveForeground: rgba(88, 76, 39, 0.4);
  --vscode-activityBar-activeBorder: #584c27;
  --vscode-activityBar-dropBorder: #584c27;
  --vscode-activityBarBadge-background: #b58900;
  --vscode-activityBarBadge-foreground: #ffffff;
  --vscode-activityBarTop-foreground: #424242;
  --vscode-activityBarTop-activeBorder: #424242;
  --vscode-activityBarTop-inactiveForeground: rgba(66, 66, 66, 0.75);
  --vscode-activityBarTop-dropBorder: #424242;
  --vscode-profileBadge-background: #c4c4c4;
  --vscode-profileBadge-foreground: #333333;
  --vscode-statusBarItem-remoteBackground: #ac9d57;
  --vscode-statusBarItem-remoteForeground: #ffffff;
  --vscode-statusBarItem-remoteHoverForeground: #586e75;
  --vscode-statusBarItem-remoteHoverBackground: rgba(255, 255, 255, 0.12);
  --vscode-statusBarItem-offlineBackground: #6c1717;
  --vscode-statusBarItem-offlineForeground: #ffffff;
  --vscode-statusBarItem-offlineHoverForeground: #586e75;
  --vscode-statusBarItem-offlineHoverBackground: rgba(255, 255, 255, 0.12);
  --vscode-extensionBadge-remoteBackground: #b58900;
  --vscode-extensionBadge-remoteForeground: #ffffff;
  --vscode-sideBar-background: #eee8d5;
  --vscode-sideBarTitle-background: #eee8d5;
  --vscode-sideBarTitle-foreground: #586e75;
  --vscode-sideBar-dropBackground: rgba(221, 214, 193, 0.67);
  --vscode-sideBarSectionHeader-background: rgba(128, 128, 128, 0.2);
  --vscode-sideBarStickyScroll-background: #eee8d5;
  --vscode-sideBarStickyScroll-shadow: #dddddd;
  --vscode-titleBar-activeForeground: #333333;
  --vscode-titleBar-inactiveForeground: rgba(51, 51, 51, 0.6);
  --vscode-titleBar-activeBackground: #eee8d5;
  --vscode-titleBar-inactiveBackground: rgba(238, 232, 213, 0.6);
  --vscode-menubar-selectionForeground: #333333;
  --vscode-menubar-selectionBackground: rgba(184, 184, 184, 0.31);
  --vscode-commandCenter-foreground: #333333;
  --vscode-commandCenter-activeForeground: #333333;
  --vscode-commandCenter-inactiveForeground: rgba(51, 51, 51, 0.6);
  --vscode-commandCenter-background: rgba(0, 0, 0, 0.05);
  --vscode-commandCenter-activeBackground: rgba(0, 0, 0, 0.08);
  --vscode-commandCenter-border: rgba(51, 51, 51, 0.2);
  --vscode-commandCenter-activeBorder: rgba(51, 51, 51, 0.3);
  --vscode-commandCenter-inactiveBorder: rgba(51, 51, 51, 0.15);
  --vscode-notifications-foreground: #616161;
  --vscode-notifications-background: #eee8d5;
  --vscode-notificationLink-foreground: #006ab1;
  --vscode-notificationCenterHeader-background: #e7dfc5;
  --vscode-notifications-border: #e7dfc5;
  --vscode-notificationsErrorIcon-foreground: #e51400;
  --vscode-notificationsWarningIcon-foreground: #bf8803;
  --vscode-notificationsInfoIcon-foreground: #1a85ff;
  --vscode-editorWatermark-foreground: rgba(101, 123, 131, 0.68);
  --vscode-inlineChat-foreground: #616161;
  --vscode-inlineChat-background: #eee8d5;
  --vscode-inlineChat-border: #c8c8c8;
  --vscode-inlineChat-shadow: rgba(0, 0, 0, 0.16);
  --vscode-inlineChatInput-border: #c8c8c8;
  --vscode-inlineChatInput-focusBorder: #b49471;
  --vscode-inlineChatInput-placeholderForeground: rgba(88, 110, 117, 0.67);
  --vscode-inlineChatInput-background: #ddd6c1;
  --vscode-inlineChatDiff-inserted: rgba(156, 204, 44, 0.13);
  --vscode-editorOverviewRuler-inlineChatInserted: rgba(156, 204, 44, 0.2);
  --vscode-inlineChatDiff-removed: rgba(255, 0, 0, 0.1);
  --vscode-editorOverviewRuler-inlineChatRemoved: rgba(255, 0, 0, 0.16);
  --vscode-extensionButton-background: #ac9d57;
  --vscode-extensionButton-foreground: #ffffff;
  --vscode-extensionButton-hoverBackground: #8b7e44;
  --vscode-extensionButton-separator: rgba(255, 255, 255, 0.4);
  --vscode-extensionButton-prominentBackground: #b58900;
  --vscode-extensionButton-prominentForeground: #ffffff;
  --vscode-extensionButton-prominentHoverBackground: rgba(88, 76, 39, 0.67);
  --vscode-editorGutter-modifiedBackground: #2090d3;
  --vscode-editorGutter-addedBackground: #48985d;
  --vscode-editorGutter-deletedBackground: #e51400;
  --vscode-minimapGutter-modifiedBackground: #2090d3;
  --vscode-minimapGutter-addedBackground: #48985d;
  --vscode-minimapGutter-deletedBackground: #e51400;
  --vscode-editorOverviewRuler-modifiedForeground: rgba(32, 144, 211, 0.6);
  --vscode-editorOverviewRuler-addedForeground: rgba(72, 152, 93, 0.6);
  --vscode-editorOverviewRuler-deletedForeground: rgba(229, 20, 0, 0.6);
  --vscode-chat-requestBorder: rgba(0, 0, 0, 0.1);
  --vscode-chat-requestBackground: rgba(253, 246, 227, 0.62);
  --vscode-chat-slashCommandBackground: rgba(210, 236, 255, 0.6);
  --vscode-chat-slashCommandForeground: #306ca2;
  --vscode-chat-avatarBackground: #f2f2f2;
  --vscode-chat-avatarForeground: #616161;
  --vscode-chat-editedFileForeground: #895503;
  --vscode-terminal-background: #fdf6e3;
  --vscode-terminal-foreground: #333333;
  --vscode-terminal-selectionBackground: #eee8d5;
  --vscode-terminal-inactiveSelectionBackground: rgba(238, 232, 213, 0.5);
  --vscode-terminalCommandDecoration-defaultBackground: rgba(0, 0, 0, 0.25);
  --vscode-terminalCommandDecoration-successBackground: #2090d3;
  --vscode-terminalCommandDecoration-errorBackground: #e51400;
  --vscode-terminalOverviewRuler-cursorForeground: rgba(160, 160, 160, 0.8);
  --vscode-terminal-border: #ddd6c1;
  --vscode-terminalOverviewRuler-border: rgba(127, 127, 127, 0.3);
  --vscode-terminal-findMatchBackground: #a8ac94;
  --vscode-terminal-hoverHighlightBackground: rgba(173, 214, 255, 0.07);
  --vscode-terminal-findMatchHighlightBackground: rgba(234, 92, 0, 0.33);
  --vscode-terminalOverviewRuler-findMatchForeground: rgba(209, 134, 22, 0.49);
  --vscode-terminal-dropBackground: rgba(221, 214, 193, 0.67);
  --vscode-terminal-initialHintForeground: rgba(0, 0, 0, 0.47);
  --vscode-scmGraph-historyItemRefColor: #1a85ff;
  --vscode-scmGraph-historyItemRemoteRefColor: #652d90;
  --vscode-scmGraph-historyItemBaseRefColor: #ea5c00;
  --vscode-scmGraph-historyItemHoverDefaultLabelForeground: #616161;
  --vscode-scmGraph-historyItemHoverDefaultLabelBackground: rgba(181, 137, 0, 0.67);
  --vscode-scmGraph-historyItemHoverLabelForeground: #ffffff;
  --vscode-scmGraph-historyItemHoverAdditionsForeground: #587c0c;
  --vscode-scmGraph-historyItemHoverDeletionsForeground: #ad0707;
  --vscode-scmGraph-foreground1: #ffb000;
  --vscode-scmGraph-foreground2: #dc267f;
  --vscode-scmGraph-foreground3: #994f00;
  --vscode-scmGraph-foreground4: #40b0a6;
  --vscode-scmGraph-foreground5: #b66dff;
  --vscode-commentsView-resolvedIcon: rgba(97, 97, 97, 0.5);
  --vscode-commentsView-unresolvedIcon: #b49471;
  --vscode-editorCommentsWidget-replyInputBackground: #eee8d5;
  --vscode-editorCommentsWidget-resolvedBorder: rgba(97, 97, 97, 0.5);
  --vscode-editorCommentsWidget-unresolvedBorder: #b49471;
  --vscode-editorCommentsWidget-rangeBackground: rgba(180, 148, 113, 0.1);
  --vscode-editorCommentsWidget-rangeActiveBackground: rgba(180, 148, 113, 0.1);
  --vscode-editorGutter-commentRangeForeground: #c9c2ac;
  --vscode-editorOverviewRuler-commentForeground: #c9c2ac;
  --vscode-editorOverviewRuler-commentUnresolvedForeground: #c9c2ac;
  --vscode-editorGutter-commentGlyphForeground: #657b83;
  --vscode-editorGutter-commentUnresolvedGlyphForeground: #657b83;
  --vscode-ports-iconRunningProcessForeground: rgba(42, 161, 152, 0.6);
  --vscode-settings-headerForeground: #444444;
  --vscode-settings-settingsHeaderHoverForeground: rgba(68, 68, 68, 0.7);
  --vscode-settings-modifiedItemIndicator: #66afe0;
  --vscode-settings-headerBorder: #ddd6c1;
  --vscode-settings-sashBorder: #ddd6c1;
  --vscode-settings-dropdownBackground: #eee8d5;
  --vscode-settings-dropdownForeground: #616161;
  --vscode-settings-dropdownBorder: #d3af86;
  --vscode-settings-dropdownListBorder: #c8c8c8;
  --vscode-settings-checkboxBackground: #eee8d5;
  --vscode-settings-checkboxForeground: #616161;
  --vscode-settings-checkboxBorder: #d3af86;
  --vscode-settings-textInputBackground: #ddd6c1;
  --vscode-settings-textInputForeground: #586e75;
  --vscode-settings-numberInputBackground: #ddd6c1;
  --vscode-settings-numberInputForeground: #586e75;
  --vscode-settings-focusedRowBackground: rgba(223, 202, 136, 0.16);
  --vscode-settings-rowHoverBackground: rgba(223, 202, 136, 0.08);
  --vscode-settings-focusedRowBorder: #b49471;
  --vscode-keybindingTable-headerBackground: rgba(97, 97, 97, 0.04);
  --vscode-keybindingTable-rowsBackground: rgba(97, 97, 97, 0.04);
  --vscode-debugToolBar-background: #ddd6c1;
  --vscode-debugIcon-startForeground: #388a34;
  --vscode-notebook-cellBorderColor: #d1cbb8;
  --vscode-notebook-focusedEditorBorder: #b49471;
  --vscode-notebookStatusSuccessIcon-foreground: #388a34;
  --vscode-notebookEditorOverviewRuler-runningCellForeground: #388a34;
  --vscode-notebookStatusErrorIcon-foreground: #a1260d;
  --vscode-notebookStatusRunningIcon-foreground: #616161;
  --vscode-notebook-cellToolbarSeparator: rgba(128, 128, 128, 0.35);
  --vscode-notebook-selectedCellBackground: #d1cbb8;
  --vscode-notebook-selectedCellBorder: #d1cbb8;
  --vscode-notebook-focusedCellBorder: #b49471;
  --vscode-notebook-inactiveFocusedCellBorder: #d1cbb8;
  --vscode-notebook-cellStatusBarItemHoverBackground: rgba(0, 0, 0, 0.08);
  --vscode-notebook-cellInsertionIndicator: #b49471;
  --vscode-notebookScrollbarSlider-background: rgba(100, 100, 100, 0.4);
  --vscode-notebookScrollbarSlider-hoverBackground: rgba(100, 100, 100, 0.7);
  --vscode-notebookScrollbarSlider-activeBackground: rgba(0, 0, 0, 0.6);
  --vscode-notebook-symbolHighlightBackground: rgba(253, 255, 0, 0.2);
  --vscode-notebook-cellEditorBackground: #f7f0e0;
  --vscode-notebook-editorBackground: #fdf6e3;
  --vscode-debugIcon-breakpointForeground: #e51400;
  --vscode-debugIcon-breakpointDisabledForeground: #848484;
  --vscode-debugIcon-breakpointUnverifiedForeground: #848484;
  --vscode-debugIcon-breakpointCurrentStackframeForeground: #be8700;
  --vscode-debugIcon-breakpointStackframeForeground: #89d185;
  --vscode-editor-stackFrameHighlightBackground: rgba(255, 255, 102, 0.45);
  --vscode-editor-focusedStackFrameHighlightBackground: rgba(206, 231, 206, 0.45);
  --vscode-multiDiffEditor-headerBackground: #d3cbb7;
  --vscode-multiDiffEditor-background: #fdf6e3;
  --vscode-multiDiffEditor-border: #cccccc;
  --vscode-interactive-activeCodeBorder: #b58900;
  --vscode-interactive-inactiveCodeBorder: #d1cbb8;
  --vscode-testing-iconFailed: #f14c4c;
  --vscode-testing-iconErrored: #f14c4c;
  --vscode-testing-iconPassed: #73c991;
  --vscode-testing-runAction: #73c991;
  --vscode-testing-iconQueued: #cca700;
  --vscode-testing-iconUnset: #848484;
  --vscode-testing-iconSkipped: #848484;
  --vscode-testing-peekBorder: #e51400;
  --vscode-testing-messagePeekBorder: #1a85ff;
  --vscode-testing-peekHeaderBackground: rgba(229, 20, 0, 0.1);
  --vscode-testing-messagePeekHeaderBackground: rgba(26, 133, 255, 0.1);
  --vscode-testing-coveredBackground: rgba(156, 204, 44, 0.25);
  --vscode-testing-coveredBorder: rgba(156, 204, 44, 0.19);
  --vscode-testing-coveredGutterBackground: rgba(156, 204, 44, 0.15);
  --vscode-testing-uncoveredBranchBackground: #fd9388;
  --vscode-testing-uncoveredBackground: rgba(255, 0, 0, 0.2);
  --vscode-testing-uncoveredBorder: rgba(255, 0, 0, 0.15);
  --vscode-testing-uncoveredGutterBackground: rgba(255, 0, 0, 0.3);
  --vscode-testing-coverCountBadgeBackground: rgba(181, 137, 0, 0.67);
  --vscode-testing-coverCountBadgeForeground: #333333;
  --vscode-testing-message\.error\.badgeBackground: #e51400;
  --vscode-testing-message\.error\.badgeBorder: #e51400;
  --vscode-testing-message\.error\.badgeForeground: #ffffff;
  --vscode-testing-message\.info\.decorationForeground: rgba(101, 123, 131, 0.5);
  --vscode-testing-iconErrored\.retired: rgba(241, 76, 76, 0.7);
  --vscode-testing-iconFailed\.retired: rgba(241, 76, 76, 0.7);
  --vscode-testing-iconPassed\.retired: rgba(115, 201, 145, 0.7);
  --vscode-testing-iconQueued\.retired: rgba(204, 167, 0, 0.7);
  --vscode-testing-iconUnset\.retired: rgba(132, 132, 132, 0.7);
  --vscode-testing-iconSkipped\.retired: rgba(132, 132, 132, 0.7);
  --vscode-debugExceptionWidget-border: #ab395b;
  --vscode-debugExceptionWidget-background: #ddd6c1;
  --vscode-editor-inlineValuesForeground: rgba(0, 0, 0, 0.5);
  --vscode-editor-inlineValuesBackground: rgba(255, 200, 0, 0.2);
  --vscode-statusBar-debuggingBackground: #eee8d5;
  --vscode-statusBar-debuggingForeground: #586e75;
  --vscode-commandCenter-debuggingBackground: rgba(238, 232, 213, 0.26);
  --vscode-debugTokenExpression-name: #9b46b0;
  --vscode-debugTokenExpression-type: #4a90e2;
  --vscode-debugTokenExpression-value: rgba(108, 108, 108, 0.8);
  --vscode-debugTokenExpression-string: #a31515;
  --vscode-debugTokenExpression-boolean: #0000ff;
  --vscode-debugTokenExpression-number: #098658;
  --vscode-debugTokenExpression-error: #e51400;
  --vscode-debugView-exceptionLabelForeground: #ffffff;
  --vscode-debugView-exceptionLabelBackground: #a31515;
  --vscode-debugView-stateLabelForeground: #616161;
  --vscode-debugView-stateLabelBackground: rgba(136, 136, 136, 0.27);
  --vscode-debugView-valueChangedHighlight: #569cd6;
  --vscode-debugConsole-infoForeground: #1a85ff;
  --vscode-debugConsole-warningForeground: #bf8803;
  --vscode-debugConsole-errorForeground: #a1260d;
  --vscode-debugConsole-sourceForeground: #616161;
  --vscode-debugConsoleInputIcon-foreground: #616161;
  --vscode-debugIcon-pauseForeground: #007acc;
  --vscode-debugIcon-stopForeground: #a1260d;
  --vscode-debugIcon-disconnectForeground: #a1260d;
  --vscode-debugIcon-restartForeground: #388a34;
  --vscode-debugIcon-stepOverForeground: #007acc;
  --vscode-debugIcon-stepIntoForeground: #007acc;
  --vscode-debugIcon-stepOutForeground: #007acc;
  --vscode-debugIcon-continueForeground: #007acc;
  --vscode-debugIcon-stepBackForeground: #007acc;
  --vscode-mergeEditor-change\.background: rgba(155, 185, 85, 0.2);
  --vscode-mergeEditor-change\.word\.background: rgba(156, 204, 44, 0.4);
  --vscode-mergeEditor-changeBase\.background: #ffcccc;
  --vscode-mergeEditor-changeBase\.word\.background: #ffa3a3;
  --vscode-mergeEditor-conflict\.unhandledUnfocused\.border: #ffa600;
  --vscode-mergeEditor-conflict\.unhandledFocused\.border: #ffa600;
  --vscode-mergeEditor-conflict\.handledUnfocused\.border: rgba(134, 134, 134, 0.29);
  --vscode-mergeEditor-conflict\.handledFocused\.border: rgba(193, 193, 193, 0.8);
  --vscode-mergeEditor-conflict\.handled\.minimapOverViewRuler: rgba(173, 172, 168, 0.93);
  --vscode-mergeEditor-conflict\.unhandled\.minimapOverViewRuler: #fcba03;
  --vscode-mergeEditor-conflictingLines\.background: rgba(255, 234, 0, 0.28);
  --vscode-mergeEditor-conflict\.input1\.background: rgba(64, 200, 174, 0.2);
  --vscode-mergeEditor-conflict\.input2\.background: rgba(64, 166, 255, 0.2);
  --vscode-extensionIcon-starForeground: #df6100;
  --vscode-extensionIcon-verifiedForeground: #006ab1;
  --vscode-extensionIcon-preReleaseForeground: #1d9271;
  --vscode-extensionIcon-sponsorForeground: #b51e78;
  --vscode-terminal-ansiBlack: #073642;
  --vscode-terminal-ansiRed: #dc322f;
  --vscode-terminal-ansiGreen: #859900;
  --vscode-terminal-ansiYellow: #b58900;
  --vscode-terminal-ansiBlue: #268bd2;
  --vscode-terminal-ansiMagenta: #d33682;
  --vscode-terminal-ansiCyan: #2aa198;
  --vscode-terminal-ansiWhite: #eee8d5;
  --vscode-terminal-ansiBrightBlack: #002b36;
  --vscode-terminal-ansiBrightRed: #cb4b16;
  --vscode-terminal-ansiBrightGreen: #586e75;
  --vscode-terminal-ansiBrightYellow: #657b83;
  --vscode-terminal-ansiBrightBlue: #839496;
  --vscode-terminal-ansiBrightMagenta: #6c71c4;
  --vscode-terminal-ansiBrightCyan: #93a1a1;
  --vscode-terminal-ansiBrightWhite: #fdf6e3;
  --vscode-simpleFindWidget-sashBorder: #c8c8c8;
  --vscode-terminalStickyScrollHover-background: #f0f0f0;
  --vscode-terminalCommandGuide-foreground: #d1cbb8;
  --vscode-welcomePage-tileBackground: #eee8d5;
  --vscode-welcomePage-tileHoverBackground: #e1d7b5;
  --vscode-welcomePage-tileBorder: rgba(0, 0, 0, 0.1);
  --vscode-welcomePage-progress\.background: #ddd6c1;
  --vscode-welcomePage-progress\.foreground: #006ab1;
  --vscode-walkthrough-stepTitle\.foreground: #000000;
  --vscode-walkThrough-embeddedEditorBackground: rgba(0, 0, 0, 0.08);
  --vscode-profiles-sashBorder: #ddd6c1;
  --vscode-gitDecoration-addedResourceForeground: #587c0c;
  --vscode-gitDecoration-modifiedResourceForeground: #895503;
  --vscode-gitDecoration-deletedResourceForeground: #ad0707;
  --vscode-gitDecoration-renamedResourceForeground: #007100;
  --vscode-gitDecoration-untrackedResourceForeground: #007100;
  --vscode-gitDecoration-ignoredResourceForeground: #8e8e90;
  --vscode-gitDecoration-stageModifiedResourceForeground: #895503;
  --vscode-gitDecoration-stageDeletedResourceForeground: #ad0707;
  --vscode-gitDecoration-conflictingResourceForeground: #ad0707;
  --vscode-gitDecoration-submoduleResourceForeground: #1258a7;
  --vscode-git-blame\.editorDecorationForeground: #919191;
  --vscode-rust_analyzer-syntaxTreeBorder: #b700ff;
}
