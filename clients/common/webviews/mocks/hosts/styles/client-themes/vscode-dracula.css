/**
 * These values were copied over by hand from a webview in VSCode.
 *
 * Find using the following:
 *     document.querySelector('#_defaultStyles').parentNode.parentNode.style.cssText
 */

/* Add Ubuntu font is used as a default font*/
@import url("https://fonts.googleapis.com/css2?family=Ubuntu:ital,wght@0,300;0,400;0,500;0,700;1,300;1,400;1,500;1,700&display=swap");

:root {
  --vscode-font-family: -apple-system, BlinkMacSystemFont, sans-serif;
  --vscode-font-weight: normal;
  --vscode-font-size: 13px;
  --vscode-editor-font-family: Operator Mono, "Droid Sans Mono", "monospace", monospace;
  --vscode-editor-font-weight: 360;
  --vscode-editor-font-size: 17px;
  --vscode-foreground: #f8f8f2;
  --vscode-disabledForeground: rgba(204, 204, 204, 0.5);
  --vscode-errorForeground: #ff5555;
  --vscode-descriptionForeground: rgba(248, 248, 242, 0.7);
  --vscode-icon-foreground: #c5c5c5;
  --vscode-focusBorder: #6272a4;
  --vscode-selection-background: #bd93f9;
  --vscode-textSeparator-foreground: rgba(255, 255, 255, 0.18);
  --vscode-textLink-foreground: #3794ff;
  --vscode-textLink-activeForeground: #3794ff;
  --vscode-textPreformat-foreground: #d7ba7d;
  --vscode-textPreformat-background: rgba(255, 255, 255, 0.1);
  --vscode-textBlockQuote-background: #222222;
  --vscode-textBlockQuote-border: rgba(0, 122, 204, 0.5);
  --vscode-textCodeBlock-background: rgba(10, 10, 10, 0.4);
  --vscode-widget-shadow: rgba(0, 0, 0, 0.36);
  --vscode-input-background: #282a36;
  --vscode-input-foreground: #f8f8f2;
  --vscode-input-border: #191a21;
  --vscode-inputOption-activeBorder: #bd93f9;
  --vscode-inputOption-hoverBackground: rgba(90, 93, 94, 0.5);
  --vscode-inputOption-activeBackground: rgba(98, 114, 164, 0.4);
  --vscode-inputOption-activeForeground: #ffffff;
  --vscode-input-placeholderForeground: #6272a4;
  --vscode-inputValidation-infoBackground: #063b49;
  --vscode-inputValidation-infoBorder: #ff79c6;
  --vscode-inputValidation-warningBackground: #352a05;
  --vscode-inputValidation-warningBorder: #ffb86c;
  --vscode-inputValidation-errorBackground: #5a1d1d;
  --vscode-inputValidation-errorBorder: #ff5555;
  --vscode-dropdown-background: #343746;
  --vscode-dropdown-foreground: #f8f8f2;
  --vscode-dropdown-border: #191a21;
  --vscode-button-foreground: #f8f8f2;
  --vscode-button-separator: rgba(248, 248, 242, 0.4);
  --vscode-button-background: #44475a;
  --vscode-button-hoverBackground: #52556c;
  --vscode-button-secondaryForeground: #f8f8f2;
  --vscode-button-secondaryBackground: #282a36;
  --vscode-button-secondaryHoverBackground: #343746;
  --vscode-badge-background: #44475a;
  --vscode-badge-foreground: #f8f8f2;
  --vscode-scrollbar-shadow: #000000;
  --vscode-scrollbarSlider-background: rgba(121, 121, 121, 0.4);
  --vscode-scrollbarSlider-hoverBackground: rgba(100, 100, 100, 0.7);
  --vscode-scrollbarSlider-activeBackground: rgba(191, 191, 191, 0.4);
  --vscode-progressBar-background: #ff79c6;
  --vscode-editorError-foreground: #ff5555;
  --vscode-editorWarning-foreground: #8be9fd;
  --vscode-editorInfo-foreground: #3794ff;
  --vscode-editorHint-foreground: rgba(238, 238, 238, 0.7);
  --vscode-sash-hoverBorder: #6272a4;
  --vscode-editor-background: #282a36;
  --vscode-editor-foreground: #f8f8f2;
  --vscode-editorStickyScroll-background: #282a36;
  --vscode-editorStickyScrollHover-background: #2a2d2e;
  --vscode-editorWidget-background: #21222c;
  --vscode-editorWidget-foreground: #f8f8f2;
  --vscode-editorWidget-border: #454545;
  --vscode-quickInput-background: #21222c;
  --vscode-quickInput-foreground: #f8f8f2;
  --vscode-quickInputTitle-background: rgba(255, 255, 255, 0.1);
  --vscode-pickerGroup-foreground: #8be9fd;
  --vscode-pickerGroup-border: #bd93f9;
  --vscode-keybindingLabel-background: rgba(128, 128, 128, 0.17);
  --vscode-keybindingLabel-foreground: #cccccc;
  --vscode-keybindingLabel-border: rgba(51, 51, 51, 0.6);
  --vscode-keybindingLabel-bottomBorder: rgba(68, 68, 68, 0.6);
  --vscode-editor-selectionBackground: #44475a;
  --vscode-editor-inactiveSelectionBackground: rgba(68, 71, 90, 0.5);
  --vscode-editor-selectionHighlightBackground: #424450;
  --vscode-editor-findMatchBackground: rgba(255, 184, 108, 0.5);
  --vscode-editor-findMatchHighlightBackground: rgba(255, 255, 255, 0.25);
  --vscode-editor-findRangeHighlightBackground: rgba(68, 71, 90, 0.46);
  --vscode-searchEditor-findMatchBackground: rgba(255, 255, 255, 0.17);
  --vscode-search-resultsInfoForeground: rgba(248, 248, 242, 0.65);
  --vscode-editor-hoverHighlightBackground: rgba(139, 233, 253, 0.31);
  --vscode-editorHoverWidget-background: #282a36;
  --vscode-editorHoverWidget-foreground: #f8f8f2;
  --vscode-editorHoverWidget-border: #6272a4;
  --vscode-editorHoverWidget-statusBarBackground: #303241;
  --vscode-editorLink-activeForeground: #8be9fd;
  --vscode-editorInlayHint-foreground: #969696;
  --vscode-editorInlayHint-background: rgba(68, 71, 90, 0.1);
  --vscode-editorInlayHint-typeForeground: #969696;
  --vscode-editorInlayHint-typeBackground: rgba(68, 71, 90, 0.1);
  --vscode-editorInlayHint-parameterForeground: #969696;
  --vscode-editorInlayHint-parameterBackground: rgba(68, 71, 90, 0.1);
  --vscode-editorLightBulb-foreground: #ffcc00;
  --vscode-editorLightBulbAutoFix-foreground: #75beff;
  --vscode-editorLightBulbAi-foreground: #767676;
  --vscode-diffEditor-insertedTextBackground: rgba(80, 250, 123, 0.13);
  --vscode-diffEditor-removedTextBackground: rgba(255, 85, 85, 0.31);
  --vscode-diffEditor-insertedLineBackground: rgba(155, 185, 85, 0.2);
  --vscode-diffEditor-removedLineBackground: rgba(255, 0, 0, 0.2);
  --vscode-diffEditor-diagonalFill: rgba(204, 204, 204, 0.2);
  --vscode-diffEditor-unchangedRegionBackground: #21222c;
  --vscode-diffEditor-unchangedRegionForeground: #f8f8f2;
  --vscode-diffEditor-unchangedCodeBackground: rgba(116, 116, 116, 0.16);
  --vscode-list-focusBackground: rgba(68, 71, 90, 0.46);
  --vscode-list-focusOutline: #6272a4;
  --vscode-list-activeSelectionBackground: #44475a;
  --vscode-list-activeSelectionForeground: #f8f8f2;
  --vscode-list-inactiveSelectionBackground: rgba(68, 71, 90, 0.46);
  --vscode-list-hoverBackground: rgba(68, 71, 90, 0.46);
  --vscode-list-dropBackground: #44475a;
  --vscode-list-highlightForeground: #8be9fd;
  --vscode-list-focusHighlightForeground: #8be9fd;
  --vscode-list-invalidItemForeground: #b89500;
  --vscode-list-errorForeground: #ff5555;
  --vscode-list-warningForeground: #ffb86c;
  --vscode-listFilterWidget-background: #343746;
  --vscode-listFilterWidget-outline: #424450;
  --vscode-listFilterWidget-noMatchesOutline: #ff5555;
  --vscode-listFilterWidget-shadow: rgba(0, 0, 0, 0.36);
  --vscode-list-filterMatchBackground: rgba(255, 255, 255, 0.25);
  --vscode-tree-indentGuidesStroke: #585858;
  --vscode-tree-inactiveIndentGuidesStroke: rgba(88, 88, 88, 0.4);
  --vscode-tree-tableColumnsBorder: rgba(204, 204, 204, 0.13);
  --vscode-tree-tableOddRowsBackground: rgba(248, 248, 242, 0.04);
  --vscode-list-deemphasizedForeground: #8c8c8c;
  --vscode-checkbox-background: #343746;
  --vscode-checkbox-selectBackground: #21222c;
  --vscode-checkbox-foreground: #f8f8f2;
  --vscode-checkbox-border: #191a21;
  --vscode-checkbox-selectBorder: #c5c5c5;
  --vscode-quickInputList-focusForeground: #f8f8f2;
  --vscode-quickInputList-focusBackground: #44475a;
  --vscode-menu-foreground: #f8f8f2;
  --vscode-menu-background: #343746;
  --vscode-menu-selectionForeground: #f8f8f2;
  --vscode-menu-selectionBackground: #44475a;
  --vscode-menu-separatorBackground: #606060;
  --vscode-toolbar-hoverBackground: rgba(90, 93, 94, 0.31);
  --vscode-toolbar-activeBackground: rgba(99, 102, 103, 0.31);
  --vscode-editor-snippetTabstopHighlightBackground: #282a36;
  --vscode-editor-snippetTabstopHighlightBorder: #6272a4;
  --vscode-editor-snippetFinalTabstopHighlightBackground: #282a36;
  --vscode-editor-snippetFinalTabstopHighlightBorder: #50fa7b;
  --vscode-breadcrumb-foreground: #6272a4;
  --vscode-breadcrumb-background: #282a36;
  --vscode-breadcrumb-focusForeground: #f8f8f2;
  --vscode-breadcrumb-activeSelectionForeground: #f8f8f2;
  --vscode-breadcrumbPicker-background: #191a21;
  --vscode-merge-currentHeaderBackground: rgba(80, 250, 123, 0.56);
  --vscode-merge-currentContentBackground: rgba(80, 250, 123, 0.23);
  --vscode-merge-incomingHeaderBackground: rgba(189, 147, 249, 0.56);
  --vscode-merge-incomingContentBackground: rgba(189, 147, 249, 0.23);
  --vscode-merge-commonHeaderBackground: rgba(96, 96, 96, 0.4);
  --vscode-merge-commonContentBackground: rgba(96, 96, 96, 0.16);
  --vscode-editorOverviewRuler-currentContentForeground: #50fa7b;
  --vscode-editorOverviewRuler-incomingContentForeground: #bd93f9;
  --vscode-editorOverviewRuler-commonContentForeground: rgba(96, 96, 96, 0.4);
  --vscode-editorOverviewRuler-findMatchForeground: rgba(209, 134, 22, 0.49);
  --vscode-editorOverviewRuler-selectionHighlightForeground: #ffb86c;
  --vscode-minimap-findMatchHighlight: #d18616;
  --vscode-minimap-selectionOccurrenceHighlight: #676767;
  --vscode-minimap-selectionHighlight: #264f78;
  --vscode-minimap-infoHighlight: #3794ff;
  --vscode-minimap-warningHighlight: #8be9fd;
  --vscode-minimap-errorHighlight: rgba(255, 18, 18, 0.7);
  --vscode-minimap-foregroundOpacity: #000000;
  --vscode-minimapSlider-background: rgba(121, 121, 121, 0.2);
  --vscode-minimapSlider-hoverBackground: rgba(100, 100, 100, 0.35);
  --vscode-minimapSlider-activeBackground: rgba(191, 191, 191, 0.2);
  --vscode-problemsErrorIcon-foreground: #ff5555;
  --vscode-problemsWarningIcon-foreground: #8be9fd;
  --vscode-problemsInfoIcon-foreground: #3794ff;
  --vscode-charts-foreground: #f8f8f2;
  --vscode-charts-lines: rgba(248, 248, 242, 0.5);
  --vscode-charts-red: #ff5555;
  --vscode-charts-blue: #3794ff;
  --vscode-charts-yellow: #8be9fd;
  --vscode-charts-orange: #d18616;
  --vscode-charts-green: #89d185;
  --vscode-charts-purple: #b180d7;
  --vscode-diffEditor-move\.border: rgba(139, 139, 139, 0.61);
  --vscode-diffEditor-moveActive\.border: #ffa500;
  --vscode-diffEditor-unchangedRegionShadow: #000000;
  --vscode-multiDiffEditor-headerBackground: #808080;
  --vscode-symbolIcon-arrayForeground: #f8f8f2;
  --vscode-symbolIcon-booleanForeground: #f8f8f2;
  --vscode-symbolIcon-classForeground: #ee9d28;
  --vscode-symbolIcon-colorForeground: #f8f8f2;
  --vscode-symbolIcon-constantForeground: #f8f8f2;
  --vscode-symbolIcon-constructorForeground: #b180d7;
  --vscode-symbolIcon-enumeratorForeground: #ee9d28;
  --vscode-symbolIcon-enumeratorMemberForeground: #75beff;
  --vscode-symbolIcon-eventForeground: #ee9d28;
  --vscode-symbolIcon-fieldForeground: #75beff;
  --vscode-symbolIcon-fileForeground: #f8f8f2;
  --vscode-symbolIcon-folderForeground: #f8f8f2;
  --vscode-symbolIcon-functionForeground: #b180d7;
  --vscode-symbolIcon-interfaceForeground: #75beff;
  --vscode-symbolIcon-keyForeground: #f8f8f2;
  --vscode-symbolIcon-keywordForeground: #f8f8f2;
  --vscode-symbolIcon-methodForeground: #b180d7;
  --vscode-symbolIcon-moduleForeground: #f8f8f2;
  --vscode-symbolIcon-namespaceForeground: #f8f8f2;
  --vscode-symbolIcon-nullForeground: #f8f8f2;
  --vscode-symbolIcon-numberForeground: #f8f8f2;
  --vscode-symbolIcon-objectForeground: #f8f8f2;
  --vscode-symbolIcon-operatorForeground: #f8f8f2;
  --vscode-symbolIcon-packageForeground: #f8f8f2;
  --vscode-symbolIcon-propertyForeground: #f8f8f2;
  --vscode-symbolIcon-referenceForeground: #f8f8f2;
  --vscode-symbolIcon-snippetForeground: #f8f8f2;
  --vscode-symbolIcon-stringForeground: #f8f8f2;
  --vscode-symbolIcon-structForeground: #f8f8f2;
  --vscode-symbolIcon-textForeground: #f8f8f2;
  --vscode-symbolIcon-typeParameterForeground: #f8f8f2;
  --vscode-symbolIcon-unitForeground: #f8f8f2;
  --vscode-symbolIcon-variableForeground: #75beff;
  --vscode-actionBar-toggledBackground: rgba(98, 114, 164, 0.4);
  --vscode-editorHoverWidget-highlightForeground: #8be9fd;
  --vscode-editor-lineHighlightBorder: #44475a;
  --vscode-editor-rangeHighlightBackground: rgba(189, 147, 249, 0.08);
  --vscode-editor-symbolHighlightBackground: rgba(255, 255, 255, 0.25);
  --vscode-editorCursor-foreground: #aeafad;
  --vscode-editorWhitespace-foreground: rgba(255, 255, 255, 0.1);
  --vscode-editorLineNumber-foreground: #6272a4;
  --vscode-editorIndentGuide-background: rgba(255, 255, 255, 0.1);
  --vscode-editorIndentGuide-activeBackground: rgba(255, 255, 255, 0.27);
  --vscode-editorIndentGuide-background1: rgba(255, 255, 255, 0.1);
  --vscode-editorIndentGuide-background2: rgba(0, 0, 0, 0);
  --vscode-editorIndentGuide-background3: rgba(0, 0, 0, 0);
  --vscode-editorIndentGuide-background4: rgba(0, 0, 0, 0);
  --vscode-editorIndentGuide-background5: rgba(0, 0, 0, 0);
  --vscode-editorIndentGuide-background6: rgba(0, 0, 0, 0);
  --vscode-editorIndentGuide-activeBackground1: rgba(255, 255, 255, 0.27);
  --vscode-editorIndentGuide-activeBackground2: rgba(0, 0, 0, 0);
  --vscode-editorIndentGuide-activeBackground3: rgba(0, 0, 0, 0);
  --vscode-editorIndentGuide-activeBackground4: rgba(0, 0, 0, 0);
  --vscode-editorIndentGuide-activeBackground5: rgba(0, 0, 0, 0);
  --vscode-editorIndentGuide-activeBackground6: rgba(0, 0, 0, 0);
  --vscode-editorActiveLineNumber-foreground: #c6c6c6;
  --vscode-editorLineNumber-activeForeground: #c6c6c6;
  --vscode-editorRuler-foreground: rgba(255, 255, 255, 0.1);
  --vscode-editorCodeLens-foreground: #6272a4;
  --vscode-editorBracketMatch-background: rgba(0, 100, 0, 0.1);
  --vscode-editorBracketMatch-border: #888888;
  --vscode-editorOverviewRuler-border: #191a21;
  --vscode-editorGutter-background: #282a36;
  --vscode-editorUnnecessaryCode-opacity: rgba(0, 0, 0, 0.67);
  --vscode-editorGhostText-foreground: rgba(255, 255, 255, 0.34);
  --vscode-editorOverviewRuler-rangeHighlightForeground: rgba(0, 122, 204, 0.6);
  --vscode-editorOverviewRuler-errorForeground: rgba(255, 85, 85, 0.5);
  --vscode-editorOverviewRuler-warningForeground: rgba(255, 184, 108, 0.5);
  --vscode-editorOverviewRuler-infoForeground: rgba(139, 233, 253, 0.5);
  --vscode-editorBracketHighlight-foreground1: #f8f8f2;
  --vscode-editorBracketHighlight-foreground2: #ff79c6;
  --vscode-editorBracketHighlight-foreground3: #8be9fd;
  --vscode-editorBracketHighlight-foreground4: #50fa7b;
  --vscode-editorBracketHighlight-foreground5: #bd93f9;
  --vscode-editorBracketHighlight-foreground6: #ffb86c;
  --vscode-editorBracketHighlight-unexpectedBracket\.foreground: #ff5555;
  --vscode-editorBracketPairGuide-background1: rgba(0, 0, 0, 0);
  --vscode-editorBracketPairGuide-background2: rgba(0, 0, 0, 0);
  --vscode-editorBracketPairGuide-background3: rgba(0, 0, 0, 0);
  --vscode-editorBracketPairGuide-background4: rgba(0, 0, 0, 0);
  --vscode-editorBracketPairGuide-background5: rgba(0, 0, 0, 0);
  --vscode-editorBracketPairGuide-background6: rgba(0, 0, 0, 0);
  --vscode-editorBracketPairGuide-activeBackground1: rgba(0, 0, 0, 0);
  --vscode-editorBracketPairGuide-activeBackground2: rgba(0, 0, 0, 0);
  --vscode-editorBracketPairGuide-activeBackground3: rgba(0, 0, 0, 0);
  --vscode-editorBracketPairGuide-activeBackground4: rgba(0, 0, 0, 0);
  --vscode-editorBracketPairGuide-activeBackground5: rgba(0, 0, 0, 0);
  --vscode-editorBracketPairGuide-activeBackground6: rgba(0, 0, 0, 0);
  --vscode-editorUnicodeHighlight-border: #bd9b03;
  --vscode-editorUnicodeHighlight-background: rgba(189, 155, 3, 0.15);
  --vscode-editorOverviewRuler-bracketMatchForeground: #a0a0a0;
  --vscode-editor-foldBackground: rgba(33, 34, 44, 0.5);
  --vscode-editorGutter-foldingControlForeground: #c5c5c5;
  --vscode-editor-linkedEditingBackground: rgba(255, 0, 0, 0.3);
  --vscode-editor-wordHighlightBackground: rgba(139, 233, 253, 0.31);
  --vscode-editor-wordHighlightStrongBackground: rgba(80, 250, 123, 0.31);
  --vscode-editor-wordHighlightTextBackground: rgba(139, 233, 253, 0.31);
  --vscode-editorOverviewRuler-wordHighlightForeground: #8be9fd;
  --vscode-editorOverviewRuler-wordHighlightStrongForeground: #50fa7b;
  --vscode-editorOverviewRuler-wordHighlightTextForeground: #ffb86c;
  --vscode-peekViewTitle-background: #191a21;
  --vscode-peekViewTitleLabel-foreground: #f8f8f2;
  --vscode-peekViewTitleDescription-foreground: #6272a4;
  --vscode-peekView-border: #44475a;
  --vscode-peekViewResult-background: #21222c;
  --vscode-peekViewResult-lineForeground: #f8f8f2;
  --vscode-peekViewResult-fileForeground: #f8f8f2;
  --vscode-peekViewResult-selectionBackground: #44475a;
  --vscode-peekViewResult-selectionForeground: #f8f8f2;
  --vscode-peekViewEditor-background: #282a36;
  --vscode-peekViewEditorGutter-background: #282a36;
  --vscode-peekViewEditorStickyScroll-background: #282a36;
  --vscode-peekViewResult-matchHighlightBackground: rgba(241, 250, 140, 0.5);
  --vscode-peekViewEditor-matchHighlightBackground: rgba(241, 250, 140, 0.5);
  --vscode-editorMarkerNavigationError-background: #ff5555;
  --vscode-editorMarkerNavigationError-headerBackground: rgba(255, 85, 85, 0.1);
  --vscode-editorMarkerNavigationWarning-background: #8be9fd;
  --vscode-editorMarkerNavigationWarning-headerBackground: rgba(139, 233, 253, 0.1);
  --vscode-editorMarkerNavigationInfo-background: #3794ff;
  --vscode-editorMarkerNavigationInfo-headerBackground: rgba(55, 148, 255, 0.1);
  --vscode-editorMarkerNavigation-background: #21222c;
  --vscode-editorSuggestWidget-background: #21222c;
  --vscode-editorSuggestWidget-border: #454545;
  --vscode-editorSuggestWidget-foreground: #f8f8f2;
  --vscode-editorSuggestWidget-selectedForeground: #f8f8f2;
  --vscode-editorSuggestWidget-selectedBackground: #44475a;
  --vscode-editorSuggestWidget-highlightForeground: #8be9fd;
  --vscode-editorSuggestWidget-focusHighlightForeground: #8be9fd;
  --vscode-editorSuggestWidgetStatus-foreground: rgba(248, 248, 242, 0.5);
  --vscode-tab-activeBackground: #343746;
  --vscode-tab-unfocusedActiveBackground: #343746;
  --vscode-tab-inactiveBackground: #21222c;
  --vscode-tab-unfocusedInactiveBackground: #21222c;
  --vscode-tab-activeForeground: #f8f8f2;
  --vscode-tab-inactiveForeground: #6272a4;
  --vscode-tab-unfocusedActiveForeground: rgba(248, 248, 242, 0.5);
  --vscode-tab-unfocusedInactiveForeground: rgba(98, 114, 164, 0.5);
  --vscode-tab-border: #191a21;
  --vscode-tab-lastPinnedBorder: #585858;
  --vscode-tab-activeBorder: #ff79c6;
  --vscode-tab-unfocusedActiveBorder: rgba(255, 121, 198, 0.5);
  --vscode-tab-activeBorderTop: rgba(255, 121, 198, 0.5);
  --vscode-tab-unfocusedActiveBorderTop: rgba(255, 121, 198, 0.25);
  --vscode-tab-activeModifiedBorder: #3399cc;
  --vscode-tab-inactiveModifiedBorder: rgba(51, 153, 204, 0.5);
  --vscode-tab-unfocusedActiveModifiedBorder: rgba(51, 153, 204, 0.5);
  --vscode-tab-unfocusedInactiveModifiedBorder: rgba(51, 153, 204, 0.25);
  --vscode-editorPane-background: #282a36;
  --vscode-editorGroupHeader-tabsBackground: #191a21;
  --vscode-editorGroupHeader-noTabsBackground: #282a36;
  --vscode-editorGroup-border: #bd93f9;
  --vscode-editorGroup-dropBackground: rgba(68, 71, 90, 0.44);
  --vscode-editorGroup-dropIntoPromptForeground: #f8f8f2;
  --vscode-editorGroup-dropIntoPromptBackground: #21222c;
  --vscode-sideBySideEditor-horizontalBorder: #bd93f9;
  --vscode-sideBySideEditor-verticalBorder: #bd93f9;
  --vscode-panel-background: #282a36;
  --vscode-panel-border: #bd93f9;
  --vscode-panelTitle-activeForeground: #f8f8f2;
  --vscode-panelTitle-inactiveForeground: #6272a4;
  --vscode-panelTitle-activeBorder: #ff79c6;
  --vscode-panelInput-border: #191a21;
  --vscode-panel-dropBorder: #f8f8f2;
  --vscode-panelSection-dropBackground: rgba(68, 71, 90, 0.44);
  --vscode-panelSectionHeader-background: rgba(128, 128, 128, 0.2);
  --vscode-panelSection-border: #bd93f9;
  --vscode-banner-background: #44475a;
  --vscode-banner-foreground: #f8f8f2;
  --vscode-banner-iconForeground: #3794ff;
  --vscode-statusBar-foreground: #f8f8f2;
  --vscode-statusBar-noFolderForeground: #f8f8f2;
  --vscode-statusBar-background: #191a21;
  --vscode-statusBar-noFolderBackground: #191a21;
  --vscode-statusBar-focusBorder: #f8f8f2;
  --vscode-statusBarItem-activeBackground: rgba(255, 255, 255, 0.18);
  --vscode-statusBarItem-focusBorder: #f8f8f2;
  --vscode-statusBarItem-hoverBackground: rgba(255, 255, 255, 0.12);
  --vscode-statusBarItem-hoverForeground: #f8f8f2;
  --vscode-statusBarItem-compactHoverBackground: rgba(255, 255, 255, 0.2);
  --vscode-statusBarItem-prominentForeground: #f8f8f2;
  --vscode-statusBarItem-prominentBackground: #ff5555;
  --vscode-statusBarItem-prominentHoverForeground: #f8f8f2;
  --vscode-statusBarItem-prominentHoverBackground: #ffb86c;
  --vscode-statusBarItem-errorBackground: #cc0000;
  --vscode-statusBarItem-errorForeground: #ffffff;
  --vscode-statusBarItem-errorHoverForeground: #f8f8f2;
  --vscode-statusBarItem-errorHoverBackground: rgba(255, 255, 255, 0.12);
  --vscode-statusBarItem-warningBackground: #04bde7;
  --vscode-statusBarItem-warningForeground: #ffffff;
  --vscode-statusBarItem-warningHoverForeground: #f8f8f2;
  --vscode-statusBarItem-warningHoverBackground: rgba(255, 255, 255, 0.12);
  --vscode-activityBar-background: #343746;
  --vscode-activityBar-foreground: #f8f8f2;
  --vscode-activityBar-inactiveForeground: #6272a4;
  --vscode-activityBar-activeBorder: rgba(255, 121, 198, 0.5);
  --vscode-activityBar-activeBackground: rgba(189, 147, 249, 0.06);
  --vscode-activityBar-dropBorder: #f8f8f2;
  --vscode-activityBarBadge-background: #ff79c6;
  --vscode-activityBarBadge-foreground: #f8f8f2;
  --vscode-profileBadge-background: #4d4d4d;
  --vscode-profileBadge-foreground: #ffffff;
  --vscode-statusBarItem-remoteBackground: #bd93f9;
  --vscode-statusBarItem-remoteForeground: #282a36;
  --vscode-statusBarItem-remoteHoverForeground: #f8f8f2;
  --vscode-statusBarItem-remoteHoverBackground: rgba(255, 255, 255, 0.12);
  --vscode-statusBarItem-offlineBackground: #6c1717;
  --vscode-statusBarItem-offlineForeground: #282a36;
  --vscode-statusBarItem-offlineHoverForeground: #f8f8f2;
  --vscode-statusBarItem-offlineHoverBackground: rgba(255, 255, 255, 0.12);
  --vscode-extensionBadge-remoteBackground: #ff79c6;
  --vscode-extensionBadge-remoteForeground: #f8f8f2;
  --vscode-sideBar-background: #21222c;
  --vscode-sideBarTitle-foreground: #f8f8f2;
  --vscode-sideBar-dropBackground: rgba(68, 71, 90, 0.44);
  --vscode-sideBarSectionHeader-background: #282a36;
  --vscode-sideBarSectionHeader-border: #191a21;
  --vscode-titleBar-activeForeground: #f8f8f2;
  --vscode-titleBar-inactiveForeground: #6272a4;
  --vscode-titleBar-activeBackground: #21222c;
  --vscode-titleBar-inactiveBackground: #191a21;
  --vscode-menubar-selectionForeground: #f8f8f2;
  --vscode-menubar-selectionBackground: rgba(90, 93, 94, 0.31);
  --vscode-commandCenter-foreground: #f8f8f2;
  --vscode-commandCenter-activeForeground: #f8f8f2;
  --vscode-commandCenter-inactiveForeground: #6272a4;
  --vscode-commandCenter-background: rgba(255, 255, 255, 0.05);
  --vscode-commandCenter-activeBackground: rgba(255, 255, 255, 0.08);
  --vscode-commandCenter-border: rgba(248, 248, 242, 0.2);
  --vscode-commandCenter-activeBorder: rgba(248, 248, 242, 0.3);
  --vscode-commandCenter-inactiveBorder: rgba(98, 114, 164, 0.25);
  --vscode-notifications-foreground: #f8f8f2;
  --vscode-notifications-background: #21222c;
  --vscode-notificationLink-foreground: #3794ff;
  --vscode-notificationCenterHeader-background: #2b2c39;
  --vscode-notifications-border: #2b2c39;
  --vscode-notificationsErrorIcon-foreground: #ff5555;
  --vscode-notificationsWarningIcon-foreground: #8be9fd;
  --vscode-notificationsInfoIcon-foreground: #3794ff;
  --vscode-chat-requestBorder: rgba(255, 255, 255, 0.1);
  --vscode-chat-slashCommandBackground: #34414b;
  --vscode-chat-slashCommandForeground: #40a6ff;
  --vscode-chat-avatarBackground: #1f1f1f;
  --vscode-chat-avatarForeground: #f8f8f2;
  --vscode-simpleFindWidget-sashBorder: #454545;
  --vscode-commentsView-resolvedIcon: rgba(204, 204, 204, 0.5);
  --vscode-commentsView-unresolvedIcon: #6272a4;
  --vscode-editorCommentsWidget-replyInputBackground: #191a21;
  --vscode-editorCommentsWidget-resolvedBorder: rgba(204, 204, 204, 0.5);
  --vscode-editorCommentsWidget-unresolvedBorder: #6272a4;
  --vscode-editorCommentsWidget-rangeBackground: rgba(98, 114, 164, 0.1);
  --vscode-editorCommentsWidget-rangeActiveBackground: rgba(98, 114, 164, 0.1);
  --vscode-editorGutter-commentRangeForeground: #343746;
  --vscode-editorOverviewRuler-commentForeground: #343746;
  --vscode-editorOverviewRuler-commentUnresolvedForeground: #343746;
  --vscode-editorGutter-commentGlyphForeground: #f8f8f2;
  --vscode-editorGutter-commentUnresolvedGlyphForeground: #f8f8f2;
  --vscode-debugToolBar-background: #21222c;
  --vscode-debugIcon-startForeground: #89d185;
  --vscode-editor-stackFrameHighlightBackground: rgba(255, 255, 0, 0.2);
  --vscode-editor-focusedStackFrameHighlightBackground: rgba(122, 189, 122, 0.3);
  --vscode-mergeEditor-change\.background: rgba(155, 185, 85, 0.2);
  --vscode-mergeEditor-change\.word\.background: rgba(156, 204, 44, 0.2);
  --vscode-mergeEditor-changeBase\.background: #4b1818;
  --vscode-mergeEditor-changeBase\.word\.background: #6f1313;
  --vscode-mergeEditor-conflict\.unhandledUnfocused\.border: rgba(255, 166, 0, 0.48);
  --vscode-mergeEditor-conflict\.unhandledFocused\.border: #ffa600;
  --vscode-mergeEditor-conflict\.handledUnfocused\.border: rgba(134, 134, 134, 0.29);
  --vscode-mergeEditor-conflict\.handledFocused\.border: rgba(193, 193, 193, 0.8);
  --vscode-mergeEditor-conflict\.handled\.minimapOverViewRuler: rgba(173, 172, 168, 0.93);
  --vscode-mergeEditor-conflict\.unhandled\.minimapOverViewRuler: #fcba03;
  --vscode-mergeEditor-conflictingLines\.background: rgba(255, 234, 0, 0.28);
  --vscode-mergeEditor-conflict\.input1\.background: rgba(80, 250, 123, 0.23);
  --vscode-mergeEditor-conflict\.input2\.background: rgba(189, 147, 249, 0.23);
  --vscode-settings-headerForeground: #f8f8f2;
  --vscode-settings-settingsHeaderHoverForeground: rgba(248, 248, 242, 0.7);
  --vscode-settings-modifiedItemIndicator: #ffb86c;
  --vscode-settings-headerBorder: #bd93f9;
  --vscode-settings-sashBorder: #bd93f9;
  --vscode-settings-dropdownBackground: #21222c;
  --vscode-settings-dropdownForeground: #f8f8f2;
  --vscode-settings-dropdownBorder: #191a21;
  --vscode-settings-dropdownListBorder: #454545;
  --vscode-settings-checkboxBackground: #21222c;
  --vscode-settings-checkboxForeground: #f8f8f2;
  --vscode-settings-checkboxBorder: #191a21;
  --vscode-settings-textInputBackground: #21222c;
  --vscode-settings-textInputForeground: #f8f8f2;
  --vscode-settings-textInputBorder: #191a21;
  --vscode-settings-numberInputBackground: #21222c;
  --vscode-settings-numberInputForeground: #f8f8f2;
  --vscode-settings-numberInputBorder: #191a21;
  --vscode-settings-focusedRowBackground: rgba(68, 71, 90, 0.28);
  --vscode-settings-rowHoverBackground: rgba(68, 71, 90, 0.14);
  --vscode-settings-focusedRowBorder: #6272a4;
  --vscode-terminal-background: #282a36;
  --vscode-terminal-foreground: #f8f8f2;
  --vscode-terminal-selectionBackground: #44475a;
  --vscode-terminal-inactiveSelectionBackground: rgba(68, 71, 90, 0.5);
  --vscode-terminalCommandDecoration-defaultBackground: rgba(255, 255, 255, 0.25);
  --vscode-terminalCommandDecoration-successBackground: #1b81a8;
  --vscode-terminalCommandDecoration-errorBackground: #f14c4c;
  --vscode-terminalOverviewRuler-cursorForeground: rgba(160, 160, 160, 0.8);
  --vscode-terminal-border: #bd93f9;
  --vscode-terminal-findMatchBackground: rgba(255, 184, 108, 0.5);
  --vscode-terminal-hoverHighlightBackground: rgba(139, 233, 253, 0.16);
  --vscode-terminal-findMatchHighlightBackground: rgba(255, 255, 255, 0.25);
  --vscode-terminalOverviewRuler-findMatchForeground: rgba(209, 134, 22, 0.49);
  --vscode-terminal-dropBackground: rgba(68, 71, 90, 0.44);
  --vscode-terminal-tab\.activeBorder: #ff79c6;
  --vscode-terminalStickyScrollHover-background: #2a2d2e;
  --vscode-testing-iconFailed: #f14c4c;
  --vscode-testing-iconErrored: #f14c4c;
  --vscode-testing-iconPassed: #73c991;
  --vscode-testing-runAction: #73c991;
  --vscode-testing-iconQueued: #cca700;
  --vscode-testing-iconUnset: #848484;
  --vscode-testing-iconSkipped: #848484;
  --vscode-testing-peekBorder: #ff5555;
  --vscode-testing-peekHeaderBackground: rgba(255, 85, 85, 0.1);
  --vscode-testing-message\.error\.decorationForeground: #ff5555;
  --vscode-testing-message\.error\.lineBackground: rgba(255, 0, 0, 0.2);
  --vscode-testing-message\.info\.decorationForeground: rgba(248, 248, 242, 0.5);
  --vscode-welcomePage-tileBackground: #21222c;
  --vscode-welcomePage-tileHoverBackground: #282935;
  --vscode-welcomePage-tileBorder: rgba(255, 255, 255, 0.1);
  --vscode-welcomePage-progress\.background: #282a36;
  --vscode-welcomePage-progress\.foreground: #3794ff;
  --vscode-walkthrough-stepTitle\.foreground: #ffffff;
  --vscode-walkThrough-embeddedEditorBackground: #21222c;
  --vscode-inlineChat-background: #21222c;
  --vscode-inlineChat-border: #454545;
  --vscode-inlineChat-shadow: rgba(0, 0, 0, 0.36);
  --vscode-inlineChat-regionHighlight: #343746;
  --vscode-inlineChatInput-border: #454545;
  --vscode-inlineChatInput-focusBorder: #6272a4;
  --vscode-inlineChatInput-placeholderForeground: #6272a4;
  --vscode-inlineChatInput-background: #282a36;
  --vscode-inlineChatDiff-inserted: rgba(80, 250, 123, 0.06);
  --vscode-inlineChatDiff-removed: rgba(255, 85, 85, 0.16);
  --vscode-debugExceptionWidget-border: #a31515;
  --vscode-debugExceptionWidget-background: #420b0d;
  --vscode-ports-iconRunningProcessForeground: #bd93f9;
  --vscode-statusBar-debuggingBackground: #ff5555;
  --vscode-statusBar-debuggingForeground: #191a21;
  --vscode-commandCenter-debuggingBackground: rgba(255, 85, 85, 0.26);
  --vscode-editor-inlineValuesForeground: rgba(255, 255, 255, 0.5);
  --vscode-editor-inlineValuesBackground: rgba(255, 200, 0, 0.2);
  --vscode-editorGutter-modifiedBackground: rgba(139, 233, 253, 0.5);
  --vscode-editorGutter-addedBackground: rgba(80, 250, 123, 0.5);
  --vscode-editorGutter-deletedBackground: rgba(255, 85, 85, 0.5);
  --vscode-minimapGutter-modifiedBackground: rgba(139, 233, 253, 0.5);
  --vscode-minimapGutter-addedBackground: rgba(80, 250, 123, 0.5);
  --vscode-minimapGutter-deletedBackground: rgba(255, 85, 85, 0.5);
  --vscode-editorOverviewRuler-modifiedForeground: rgba(139, 233, 253, 0.5);
  --vscode-editorOverviewRuler-addedForeground: rgba(80, 250, 123, 0.5);
  --vscode-editorOverviewRuler-deletedForeground: rgba(255, 85, 85, 0.5);
  --vscode-debugIcon-breakpointForeground: #e51400;
  --vscode-debugIcon-breakpointDisabledForeground: #848484;
  --vscode-debugIcon-breakpointUnverifiedForeground: #848484;
  --vscode-debugIcon-breakpointCurrentStackframeForeground: #ffcc00;
  --vscode-debugIcon-breakpointStackframeForeground: #89d185;
  --vscode-keybindingTable-headerBackground: rgba(248, 248, 242, 0.04);
  --vscode-keybindingTable-rowsBackground: rgba(248, 248, 242, 0.04);
  --vscode-debugTokenExpression-name: #c586c0;
  --vscode-debugTokenExpression-value: rgba(204, 204, 204, 0.6);
  --vscode-debugTokenExpression-string: #ce9178;
  --vscode-debugTokenExpression-boolean: #4e94ce;
  --vscode-debugTokenExpression-number: #b5cea8;
  --vscode-debugTokenExpression-error: #f48771;
  --vscode-debugView-exceptionLabelForeground: #f8f8f2;
  --vscode-debugView-exceptionLabelBackground: #6c2022;
  --vscode-debugView-stateLabelForeground: #f8f8f2;
  --vscode-debugView-stateLabelBackground: rgba(136, 136, 136, 0.27);
  --vscode-debugView-valueChangedHighlight: #569cd6;
  --vscode-debugConsole-infoForeground: #3794ff;
  --vscode-debugConsole-warningForeground: #8be9fd;
  --vscode-debugConsole-errorForeground: #ff5555;
  --vscode-debugConsole-sourceForeground: #f8f8f2;
  --vscode-debugConsoleInputIcon-foreground: #f8f8f2;
  --vscode-debugIcon-pauseForeground: #75beff;
  --vscode-debugIcon-stopForeground: #f48771;
  --vscode-debugIcon-disconnectForeground: #f48771;
  --vscode-debugIcon-restartForeground: #89d185;
  --vscode-debugIcon-stepOverForeground: #75beff;
  --vscode-debugIcon-stepIntoForeground: #75beff;
  --vscode-debugIcon-stepOutForeground: #75beff;
  --vscode-debugIcon-continueForeground: #75beff;
  --vscode-debugIcon-stepBackForeground: #75beff;
  --vscode-notebook-cellBorderColor: rgba(68, 71, 90, 0.46);
  --vscode-notebook-focusedEditorBorder: #6272a4;
  --vscode-notebookStatusSuccessIcon-foreground: #89d185;
  --vscode-notebookEditorOverviewRuler-runningCellForeground: #89d185;
  --vscode-notebookStatusErrorIcon-foreground: #ff5555;
  --vscode-notebookStatusRunningIcon-foreground: #f8f8f2;
  --vscode-notebook-cellToolbarSeparator: rgba(128, 128, 128, 0.35);
  --vscode-notebook-selectedCellBackground: rgba(68, 71, 90, 0.46);
  --vscode-notebook-selectedCellBorder: rgba(68, 71, 90, 0.46);
  --vscode-notebook-focusedCellBorder: #6272a4;
  --vscode-notebook-inactiveFocusedCellBorder: rgba(68, 71, 90, 0.46);
  --vscode-notebook-cellStatusBarItemHoverBackground: rgba(255, 255, 255, 0.15);
  --vscode-notebook-cellInsertionIndicator: #6272a4;
  --vscode-notebookScrollbarSlider-background: rgba(121, 121, 121, 0.4);
  --vscode-notebookScrollbarSlider-hoverBackground: rgba(100, 100, 100, 0.7);
  --vscode-notebookScrollbarSlider-activeBackground: rgba(191, 191, 191, 0.4);
  --vscode-notebook-symbolHighlightBackground: rgba(255, 255, 255, 0.04);
  --vscode-notebook-cellEditorBackground: #21222c;
  --vscode-notebook-editorBackground: #282a36;
  --vscode-scm-historyItemAdditionsForeground: #81b88b;
  --vscode-scm-historyItemDeletionsForeground: #ff5555;
  --vscode-scm-historyItemStatisticsBorder: rgba(248, 248, 242, 0.2);
  --vscode-scm-historyItemSelectedStatisticsBorder: rgba(248, 248, 242, 0.2);
  --vscode-searchEditor-textInputBorder: #191a21;
  --vscode-extensionButton-background: #44475a;
  --vscode-extensionButton-foreground: #f8f8f2;
  --vscode-extensionButton-hoverBackground: #52556c;
  --vscode-extensionButton-separator: rgba(248, 248, 242, 0.4);
  --vscode-extensionButton-prominentBackground: rgba(80, 250, 123, 0.56);
  --vscode-extensionButton-prominentForeground: #f8f8f2;
  --vscode-extensionButton-prominentHoverBackground: rgba(80, 250, 123, 0.38);
  --vscode-extensionIcon-starForeground: #ff8e00;
  --vscode-extensionIcon-verifiedForeground: #3794ff;
  --vscode-extensionIcon-preReleaseForeground: #1d9271;
  --vscode-extensionIcon-sponsorForeground: #d758b3;
  --vscode-terminal-ansiBlack: #21222c;
  --vscode-terminal-ansiRed: #ff5555;
  --vscode-terminal-ansiGreen: #50fa7b;
  --vscode-terminal-ansiYellow: #f1fa8c;
  --vscode-terminal-ansiBlue: #bd93f9;
  --vscode-terminal-ansiMagenta: #ff79c6;
  --vscode-terminal-ansiCyan: #8be9fd;
  --vscode-terminal-ansiWhite: #f8f8f2;
  --vscode-terminal-ansiBrightBlack: #6272a4;
  --vscode-terminal-ansiBrightRed: #ff6e6e;
  --vscode-terminal-ansiBrightGreen: #69ff94;
  --vscode-terminal-ansiBrightYellow: #ffffa5;
  --vscode-terminal-ansiBrightBlue: #d6acff;
  --vscode-terminal-ansiBrightMagenta: #ff92df;
  --vscode-terminal-ansiBrightCyan: #a4ffff;
  --vscode-terminal-ansiBrightWhite: #ffffff;
  --vscode-interactive-activeCodeBorder: #44475a;
  --vscode-interactive-inactiveCodeBorder: rgba(68, 71, 90, 0.46);
  --vscode-gitDecoration-addedResourceForeground: #81b88b;
  --vscode-gitDecoration-modifiedResourceForeground: #8be9fd;
  --vscode-gitDecoration-deletedResourceForeground: #ff5555;
  --vscode-gitDecoration-renamedResourceForeground: #73c991;
  --vscode-gitDecoration-untrackedResourceForeground: #50fa7b;
  --vscode-gitDecoration-ignoredResourceForeground: #6272a4;
  --vscode-gitDecoration-stageModifiedResourceForeground: #e2c08d;
  --vscode-gitDecoration-stageDeletedResourceForeground: #c74e39;
  --vscode-gitDecoration-conflictingResourceForeground: #ffb86c;
  --vscode-gitDecoration-submoduleResourceForeground: #8db9e2;
}
