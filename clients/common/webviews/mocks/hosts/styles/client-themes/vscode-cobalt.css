/**
 * These values were copied over by hand from a webview in VSCode.
 *
 * Find using the following:
 *     document.querySelector('#_defaultStyles').parentNode.parentNode.style.cssText
 */

:root {
  --text-link-decoration: none;
  --vscode-font-family: -apple-system, BlinkMacSystemFont, sans-serif;
  --vscode-font-weight: normal;
  --vscode-font-size: 13px;
  --vscode-editor-font-family: Operator Mono, "Droid Sans Mono", "monospace", monospace;
  --vscode-editor-font-weight: 360;
  --vscode-editor-font-size: 17px;
  --vscode-foreground: #aaaaaa;
  --vscode-disabledForeground: rgba(204, 204, 204, 0.5);
  --vscode-errorForeground: #a22929;
  --vscode-descriptionForeground: #aaaaaa;
  --vscode-icon-foreground: #c5c5c5;
  --vscode-focusBorder: #0d3a58;
  --vscode-contrastBorder: rgba(255, 255, 255, 0);
  --vscode-selection-background: #027dff;
  --vscode-textLink-foreground: #0088ff;
  --vscode-textLink-activeForeground: #0088ff;
  --vscode-textSeparator-foreground: #0d3a58;
  --vscode-textPreformat-foreground: #ffc600;
  --vscode-textPreformat-background: rgba(255, 255, 255, 0.1);
  --vscode-textBlockQuote-background: #193549;
  --vscode-textBlockQuote-border: #0088ff;
  --vscode-textCodeBlock-background: #193549;
  --vscode-sash-hoverBorder: #0d3a58;
  --vscode-badge-background: #ffc600;
  --vscode-badge-foreground: #000000;
  --vscode-activityWarningBadge-foreground: #000000;
  --vscode-activityWarningBadge-background: #cca700;
  --vscode-activityErrorBadge-foreground: #000000;
  --vscode-activityErrorBadge-background: #f14c4c;
  --vscode-scrollbar-shadow: rgba(0, 0, 0, 0);
  --vscode-scrollbarSlider-background: rgba(64, 97, 121, 0.8);
  --vscode-scrollbarSlider-hoverBackground: rgba(67, 125, 163, 0.8);
  --vscode-scrollbarSlider-activeBackground: rgba(53, 81, 102, 0.8);
  --vscode-progressBar-background: #ffc600;
  --vscode-chart-line: #236b8e;
  --vscode-chart-axis: rgba(191, 191, 191, 0.4);
  --vscode-chart-guide: rgba(191, 191, 191, 0.2);
  --vscode-editor-background: #193549;
  --vscode-editor-foreground: #ffffff;
  --vscode-editorStickyScroll-background: #193549;
  --vscode-editorStickyScrollHover-background: #2a2d2e;
  --vscode-editorStickyScroll-shadow: rgba(0, 0, 0, 0);
  --vscode-editorWidget-background: #15232d;
  --vscode-editorWidget-foreground: #aaaaaa;
  --vscode-editorWidget-border: #0d3a58;
  --vscode-editorError-foreground: #a22929;
  --vscode-editorError-border: #0d3a58;
  --vscode-editorWarning-foreground: #ffc600;
  --vscode-editorWarning-border: rgba(255, 255, 255, 0);
  --vscode-editorInfo-foreground: #3794ff;
  --vscode-editorHint-foreground: #ffc600;
  --vscode-editorLink-activeForeground: #aaaaaa;
  --vscode-editor-selectionBackground: #0050a4;
  --vscode-editor-inactiveSelectionBackground: #003b8b;
  --vscode-editor-selectionHighlightBackground: rgba(0, 80, 164, 0.5);
  --vscode-editor-compositionBorder: #ffffff;
  --vscode-editor-findMatchBackground: rgba(255, 114, 0, 0.4);
  --vscode-editor-findMatchHighlightBackground: rgba(202, 212, 15, 0.4);
  --vscode-editor-findRangeHighlightBackground: #243e51;
  --vscode-editor-hoverHighlightBackground: rgba(255, 198, 0, 0.2);
  --vscode-editorHoverWidget-background: #15232d;
  --vscode-editorHoverWidget-foreground: #aaaaaa;
  --vscode-editorHoverWidget-border: #0d3a58;
  --vscode-editorHoverWidget-statusBarBackground: #192a36;
  --vscode-editorInlayHint-foreground: #ff68b8;
  --vscode-editorInlayHint-background: rgba(0, 0, 0, 0.1);
  --vscode-editorInlayHint-typeForeground: #ff68b8;
  --vscode-editorInlayHint-typeBackground: rgba(0, 0, 0, 0.1);
  --vscode-editorInlayHint-parameterForeground: #ff68b8;
  --vscode-editorInlayHint-parameterBackground: rgba(0, 0, 0, 0.1);
  --vscode-editorLightBulb-foreground: #ffcc00;
  --vscode-editorLightBulbAutoFix-foreground: #75beff;
  --vscode-editorLightBulbAi-foreground: #ffcc00;
  --vscode-editor-snippetTabstopHighlightBackground: rgba(124, 124, 124, 0.3);
  --vscode-editor-snippetFinalTabstopHighlightBorder: #525252;
  --vscode-diffEditor-insertedTextBackground: rgba(58, 217, 0, 0.2);
  --vscode-diffEditor-removedTextBackground: rgba(238, 58, 67, 0.2);
  --vscode-diffEditor-insertedLineBackground: rgba(155, 185, 85, 0.2);
  --vscode-diffEditor-removedLineBackground: rgba(255, 0, 0, 0.2);
  --vscode-diffEditor-insertedTextBorder: rgba(0, 0, 0, 0);
  --vscode-diffEditor-removedTextBorder: rgba(0, 0, 0, 0);
  --vscode-diffEditor-diagonalFill: rgba(204, 204, 204, 0.2);
  --vscode-diffEditor-unchangedRegionBackground: #15232d;
  --vscode-diffEditor-unchangedRegionForeground: #aaaaaa;
  --vscode-diffEditor-unchangedCodeBackground: rgba(116, 116, 116, 0.16);
  --vscode-widget-shadow: rgba(0, 0, 0, 0.15);
  --vscode-toolbar-hoverBackground: rgba(90, 93, 94, 0.31);
  --vscode-toolbar-activeBackground: rgba(99, 102, 103, 0.31);
  --vscode-breadcrumb-foreground: rgba(170, 170, 170, 0.8);
  --vscode-breadcrumb-background: #193549;
  --vscode-breadcrumb-focusForeground: #bbbbbb;
  --vscode-breadcrumb-activeSelectionForeground: #bbbbbb;
  --vscode-breadcrumbPicker-background: #15232d;
  --vscode-merge-currentHeaderBackground: #2f7366;
  --vscode-merge-currentContentBackground: #2f7366;
  --vscode-merge-incomingHeaderBackground: #185294;
  --vscode-merge-incomingContentBackground: #185294;
  --vscode-merge-commonHeaderBackground: #c97d0c;
  --vscode-merge-commonContentBackground: #c97d0c;
  --vscode-merge-border: rgba(255, 255, 255, 0);
  --vscode-editorOverviewRuler-currentContentForeground: rgba(238, 58, 67, 0.33);
  --vscode-editorOverviewRuler-incomingContentForeground: rgba(58, 217, 0, 0.33);
  --vscode-editorOverviewRuler-commonContentForeground: rgba(255, 198, 0, 0.33);
  --vscode-editorOverviewRuler-findMatchForeground: rgba(209, 134, 22, 0.49);
  --vscode-editorOverviewRuler-selectionHighlightForeground: rgba(160, 160, 160, 0.8);
  --vscode-problemsErrorIcon-foreground: #a22929;
  --vscode-problemsWarningIcon-foreground: #ffc600;
  --vscode-problemsInfoIcon-foreground: #3794ff;
  --vscode-minimap-findMatchHighlight: #d18616;
  --vscode-minimap-selectionOccurrenceHighlight: #676767;
  --vscode-minimap-selectionHighlight: #264f78;
  --vscode-minimap-infoHighlight: #3794ff;
  --vscode-minimap-warningHighlight: #ffc600;
  --vscode-minimap-errorHighlight: rgba(255, 18, 18, 0.7);
  --vscode-minimap-foregroundOpacity: #000000;
  --vscode-minimapSlider-background: rgba(64, 97, 121, 0.4);
  --vscode-minimapSlider-hoverBackground: rgba(67, 125, 163, 0.4);
  --vscode-minimapSlider-activeBackground: rgba(53, 81, 102, 0.4);
  --vscode-charts-foreground: #aaaaaa;
  --vscode-charts-lines: rgba(170, 170, 170, 0.5);
  --vscode-charts-red: #a22929;
  --vscode-charts-blue: #3794ff;
  --vscode-charts-yellow: #ffc600;
  --vscode-charts-orange: #d18616;
  --vscode-charts-green: #89d185;
  --vscode-charts-purple: #b180d7;
  --vscode-input-background: #193549;
  --vscode-input-foreground: #ffc600;
  --vscode-input-border: #0d3a58;
  --vscode-inputOption-activeBorder: #8dffff;
  --vscode-inputOption-hoverBackground: rgba(90, 93, 94, 0.5);
  --vscode-inputOption-activeBackground: rgba(13, 58, 88, 0.4);
  --vscode-inputOption-activeForeground: #ffffff;
  --vscode-input-placeholderForeground: #aaaaaa;
  --vscode-inputValidation-infoBackground: #193549;
  --vscode-inputValidation-infoBorder: #0d3a58;
  --vscode-inputValidation-warningBackground: #193549;
  --vscode-inputValidation-warningBorder: #ffc600;
  --vscode-inputValidation-errorBackground: #193549;
  --vscode-inputValidation-errorBorder: #ffc600;
  --vscode-dropdown-background: #193549;
  --vscode-dropdown-foreground: #ffffff;
  --vscode-dropdown-border: #15232d;
  --vscode-button-foreground: #ffffff;
  --vscode-button-separator: rgba(255, 255, 255, 0.4);
  --vscode-button-background: #0088ff;
  --vscode-button-hoverBackground: #ff9d00;
  --vscode-button-border: rgba(255, 255, 255, 0);
  --vscode-button-secondaryForeground: #ffffff;
  --vscode-button-secondaryBackground: #3a3d41;
  --vscode-button-secondaryHoverBackground: #45494e;
  --vscode-radio-activeForeground: #ffffff;
  --vscode-radio-activeBackground: rgba(13, 58, 88, 0.4);
  --vscode-radio-activeBorder: #8dffff;
  --vscode-radio-inactiveBorder: rgba(255, 255, 255, 0.2);
  --vscode-radio-inactiveHoverBackground: rgba(90, 93, 94, 0.5);
  --vscode-checkbox-background: #193549;
  --vscode-checkbox-selectBackground: #15232d;
  --vscode-checkbox-foreground: #ffffff;
  --vscode-checkbox-border: #15232d;
  --vscode-checkbox-selectBorder: #c5c5c5;
  --vscode-keybindingLabel-background: rgba(128, 128, 128, 0.17);
  --vscode-keybindingLabel-foreground: #cccccc;
  --vscode-keybindingLabel-border: rgba(51, 51, 51, 0.6);
  --vscode-keybindingLabel-bottomBorder: rgba(68, 68, 68, 0.6);
  --vscode-list-focusBackground: #0d3a58;
  --vscode-list-focusForeground: #aaaaaa;
  --vscode-list-focusOutline: #0d3a58;
  --vscode-list-activeSelectionBackground: #193549;
  --vscode-list-activeSelectionForeground: #aaaaaa;
  --vscode-list-inactiveSelectionBackground: #0d3a58;
  --vscode-list-inactiveSelectionForeground: #aaaaaa;
  --vscode-list-hoverBackground: #193549;
  --vscode-list-hoverForeground: #aaaaaa;
  --vscode-list-dropBackground: #0d3a58;
  --vscode-list-dropBetweenBackground: #c5c5c5;
  --vscode-list-highlightForeground: #ffc600;
  --vscode-list-focusHighlightForeground: #ffc600;
  --vscode-list-invalidItemForeground: #b89500;
  --vscode-list-errorForeground: #f88070;
  --vscode-list-warningForeground: #cca700;
  --vscode-listFilterWidget-background: #15232d;
  --vscode-listFilterWidget-outline: rgba(0, 0, 0, 0);
  --vscode-listFilterWidget-noMatchesOutline: #be1100;
  --vscode-listFilterWidget-shadow: rgba(0, 0, 0, 0.15);
  --vscode-list-filterMatchBackground: rgba(202, 212, 15, 0.4);
  --vscode-list-deemphasizedForeground: #8c8c8c;
  --vscode-tree-indentGuidesStroke: #585858;
  --vscode-tree-inactiveIndentGuidesStroke: rgba(88, 88, 88, 0.4);
  --vscode-tree-tableColumnsBorder: rgba(204, 204, 204, 0.13);
  --vscode-tree-tableOddRowsBackground: rgba(170, 170, 170, 0.04);
  --vscode-editorActionList-background: #15232d;
  --vscode-editorActionList-foreground: #aaaaaa;
  --vscode-editorActionList-focusForeground: #aaaaaa;
  --vscode-editorActionList-focusBackground: #193549;
  --vscode-menu-foreground: #ffffff;
  --vscode-menu-background: #122738;
  --vscode-menu-selectionForeground: #ffffff;
  --vscode-menu-selectionBackground: #193549;
  --vscode-menu-separatorBackground: #606060;
  --vscode-quickInput-background: #15232d;
  --vscode-quickInput-foreground: #aaaaaa;
  --vscode-quickInputTitle-background: rgba(255, 255, 255, 0.1);
  --vscode-pickerGroup-foreground: #aaaaaa;
  --vscode-pickerGroup-border: #0d3a58;
  --vscode-quickInputList-focusForeground: #aaaaaa;
  --vscode-quickInputList-focusBackground: #193549;
  --vscode-search-resultsInfoForeground: rgba(170, 170, 170, 0.65);
  --vscode-searchEditor-findMatchBackground: rgba(202, 212, 15, 0.26);
  --vscode-editor-lineHighlightBackground: #1f4662;
  --vscode-editor-lineHighlightBorder: #234e6d;
  --vscode-editor-rangeHighlightBackground: #1f4662;
  --vscode-editor-symbolHighlightBackground: rgba(202, 212, 15, 0.4);
  --vscode-editorCursor-foreground: #ffc600;
  --vscode-editorMultiCursor-primary\.foreground: #ffc600;
  --vscode-editorMultiCursor-secondary\.foreground: #ffc600;
  --vscode-editorWhitespace-foreground: rgba(255, 255, 255, 0.32);
  --vscode-editorLineNumber-foreground: #aaaaaa;
  --vscode-editorIndentGuide-background: #3b5364;
  --vscode-editorIndentGuide-activeBackground: rgba(255, 255, 255, 0.32);
  --vscode-editorIndentGuide-background1: #3b5364;
  --vscode-editorIndentGuide-background2: rgba(0, 0, 0, 0);
  --vscode-editorIndentGuide-background3: rgba(0, 0, 0, 0);
  --vscode-editorIndentGuide-background4: rgba(0, 0, 0, 0);
  --vscode-editorIndentGuide-background5: rgba(0, 0, 0, 0);
  --vscode-editorIndentGuide-background6: rgba(0, 0, 0, 0);
  --vscode-editorIndentGuide-activeBackground1: rgba(255, 255, 255, 0.32);
  --vscode-editorIndentGuide-activeBackground2: rgba(0, 0, 0, 0);
  --vscode-editorIndentGuide-activeBackground3: rgba(0, 0, 0, 0);
  --vscode-editorIndentGuide-activeBackground4: rgba(0, 0, 0, 0);
  --vscode-editorIndentGuide-activeBackground5: rgba(0, 0, 0, 0);
  --vscode-editorIndentGuide-activeBackground6: rgba(0, 0, 0, 0);
  --vscode-editorActiveLineNumber-foreground: #c6c6c6;
  --vscode-editorLineNumber-activeForeground: #c6c6c6;
  --vscode-editorRuler-foreground: #1f4662;
  --vscode-editorCodeLens-foreground: #aaaaaa;
  --vscode-editorBracketMatch-background: #0d3a58;
  --vscode-editorBracketMatch-border: rgba(255, 198, 0, 0.5);
  --vscode-editorOverviewRuler-border: #0d3a58;
  --vscode-editorGutter-background: rgba(18, 39, 56, 0.4);
  --vscode-editorUnnecessaryCode-opacity: rgba(0, 0, 0, 0.67);
  --vscode-editorGhostText-foreground: rgba(255, 255, 255, 0.34);
  --vscode-editorOverviewRuler-rangeHighlightForeground: rgba(0, 122, 204, 0.6);
  --vscode-editorOverviewRuler-errorForeground: rgba(255, 18, 18, 0.7);
  --vscode-editorOverviewRuler-warningForeground: #ffc600;
  --vscode-editorOverviewRuler-infoForeground: #3794ff;
  --vscode-editorBracketHighlight-foreground1: #ffd700;
  --vscode-editorBracketHighlight-foreground2: #da70d6;
  --vscode-editorBracketHighlight-foreground3: #179fff;
  --vscode-editorBracketHighlight-foreground4: rgba(0, 0, 0, 0);
  --vscode-editorBracketHighlight-foreground5: rgba(0, 0, 0, 0);
  --vscode-editorBracketHighlight-foreground6: rgba(0, 0, 0, 0);
  --vscode-editorBracketHighlight-unexpectedBracket\.foreground: rgba(255, 18, 18, 0.8);
  --vscode-editorBracketPairGuide-background1: rgba(0, 0, 0, 0);
  --vscode-editorBracketPairGuide-background2: rgba(0, 0, 0, 0);
  --vscode-editorBracketPairGuide-background3: rgba(0, 0, 0, 0);
  --vscode-editorBracketPairGuide-background4: rgba(0, 0, 0, 0);
  --vscode-editorBracketPairGuide-background5: rgba(0, 0, 0, 0);
  --vscode-editorBracketPairGuide-background6: rgba(0, 0, 0, 0);
  --vscode-editorBracketPairGuide-activeBackground1: rgba(0, 0, 0, 0);
  --vscode-editorBracketPairGuide-activeBackground2: rgba(0, 0, 0, 0);
  --vscode-editorBracketPairGuide-activeBackground3: rgba(0, 0, 0, 0);
  --vscode-editorBracketPairGuide-activeBackground4: rgba(0, 0, 0, 0);
  --vscode-editorBracketPairGuide-activeBackground5: rgba(0, 0, 0, 0);
  --vscode-editorBracketPairGuide-activeBackground6: rgba(0, 0, 0, 0);
  --vscode-editorUnicodeHighlight-border: #ffc600;
  --vscode-diffEditor-move\.border: rgba(139, 139, 139, 0.61);
  --vscode-diffEditor-moveActive\.border: #ffa500;
  --vscode-diffEditor-unchangedRegionShadow: #000000;
  --vscode-editorOverviewRuler-bracketMatchForeground: #a0a0a0;
  --vscode-actionBar-toggledBackground: rgba(13, 58, 88, 0.4);
  --vscode-symbolIcon-arrayForeground: #aaaaaa;
  --vscode-symbolIcon-booleanForeground: #aaaaaa;
  --vscode-symbolIcon-classForeground: #ee9d28;
  --vscode-symbolIcon-colorForeground: #aaaaaa;
  --vscode-symbolIcon-constantForeground: #aaaaaa;
  --vscode-symbolIcon-constructorForeground: #b180d7;
  --vscode-symbolIcon-enumeratorForeground: #ee9d28;
  --vscode-symbolIcon-enumeratorMemberForeground: #75beff;
  --vscode-symbolIcon-eventForeground: #ee9d28;
  --vscode-symbolIcon-fieldForeground: #75beff;
  --vscode-symbolIcon-fileForeground: #aaaaaa;
  --vscode-symbolIcon-folderForeground: #aaaaaa;
  --vscode-symbolIcon-functionForeground: #b180d7;
  --vscode-symbolIcon-interfaceForeground: #75beff;
  --vscode-symbolIcon-keyForeground: #aaaaaa;
  --vscode-symbolIcon-keywordForeground: #aaaaaa;
  --vscode-symbolIcon-methodForeground: #b180d7;
  --vscode-symbolIcon-moduleForeground: #aaaaaa;
  --vscode-symbolIcon-namespaceForeground: #aaaaaa;
  --vscode-symbolIcon-nullForeground: #aaaaaa;
  --vscode-symbolIcon-numberForeground: #aaaaaa;
  --vscode-symbolIcon-objectForeground: #aaaaaa;
  --vscode-symbolIcon-operatorForeground: #aaaaaa;
  --vscode-symbolIcon-packageForeground: #aaaaaa;
  --vscode-symbolIcon-propertyForeground: #aaaaaa;
  --vscode-symbolIcon-referenceForeground: #aaaaaa;
  --vscode-symbolIcon-snippetForeground: #aaaaaa;
  --vscode-symbolIcon-stringForeground: #aaaaaa;
  --vscode-symbolIcon-structForeground: #aaaaaa;
  --vscode-symbolIcon-textForeground: #aaaaaa;
  --vscode-symbolIcon-typeParameterForeground: #aaaaaa;
  --vscode-symbolIcon-unitForeground: #aaaaaa;
  --vscode-symbolIcon-variableForeground: #75beff;
  --vscode-peekViewTitle-background: #15232d;
  --vscode-peekViewTitleLabel-foreground: #ffc600;
  --vscode-peekViewTitleDescription-foreground: #aaaaaa;
  --vscode-peekView-border: #ffc600;
  --vscode-peekViewResult-background: #15232d;
  --vscode-peekViewResult-lineForeground: #ffffff;
  --vscode-peekViewResult-fileForeground: #aaaaaa;
  --vscode-peekViewResult-selectionBackground: #0d3a58;
  --vscode-peekViewResult-selectionForeground: #ffffff;
  --vscode-peekViewEditor-background: #193549;
  --vscode-peekViewEditorGutter-background: #122738;
  --vscode-peekViewEditorStickyScroll-background: #193549;
  --vscode-peekViewResult-matchHighlightBackground: #0d3a58;
  --vscode-peekViewEditor-matchHighlightBackground: rgba(25, 53, 73, 0);
  --vscode-editor-foldBackground: rgba(0, 80, 164, 0.3);
  --vscode-editor-foldPlaceholderForeground: #808080;
  --vscode-editorGutter-foldingControlForeground: #c5c5c5;
  --vscode-editorSuggestWidget-background: #15232d;
  --vscode-editorSuggestWidget-border: #15232d;
  --vscode-editorSuggestWidget-foreground: #aaaaaa;
  --vscode-editorSuggestWidget-selectedForeground: #aaaaaa;
  --vscode-editorSuggestWidget-selectedBackground: #193549;
  --vscode-editorSuggestWidget-highlightForeground: #ffc600;
  --vscode-editorSuggestWidget-focusHighlightForeground: #ffc600;
  --vscode-editorSuggestWidgetStatus-foreground: rgba(170, 170, 170, 0.5);
  --vscode-inlineEdit-indicator\.foreground: #ffffff;
  --vscode-inlineEdit-indicator\.background: #0088ff;
  --vscode-inlineEdit-indicator\.border: rgba(255, 255, 255, 0.4);
  --vscode-inlineEdit-originalBackground: rgba(238, 58, 67, 0.08);
  --vscode-inlineEdit-modifiedBackground: rgba(58, 217, 0, 0.08);
  --vscode-inlineEdit-originalChangedLineBackground: rgba(0, 0, 0, 0);
  --vscode-inlineEdit-originalChangedTextBackground: rgba(238, 58, 67, 0.2);
  --vscode-inlineEdit-modifiedChangedLineBackground: rgba(0, 0, 0, 0);
  --vscode-inlineEdit-modifiedChangedTextBackground: rgba(58, 217, 0, 0.2);
  --vscode-inlineEdit-border: #3475a3;
  --vscode-editorMarkerNavigationError-background: #a22929;
  --vscode-editorMarkerNavigationError-headerBackground: rgba(162, 41, 41, 0.1);
  --vscode-editorMarkerNavigationWarning-background: #ffc600;
  --vscode-editorMarkerNavigationWarning-headerBackground: rgba(255, 198, 0, 0.1);
  --vscode-editorMarkerNavigationInfo-background: #3794ff;
  --vscode-editorMarkerNavigationInfo-headerBackground: rgba(55, 148, 255, 0.1);
  --vscode-editorMarkerNavigation-background: rgba(59, 83, 100, 0.2);
  --vscode-editor-linkedEditingBackground: rgba(255, 0, 0, 0.3);
  --vscode-editor-wordHighlightBackground: rgba(255, 255, 255, 0.13);
  --vscode-editor-wordHighlightStrongBackground: rgba(255, 255, 255, 0.13);
  --vscode-editor-wordHighlightTextBackground: rgba(255, 255, 255, 0.13);
  --vscode-editorOverviewRuler-wordHighlightForeground: rgba(160, 160, 160, 0.8);
  --vscode-editorOverviewRuler-wordHighlightStrongForeground: rgba(192, 160, 192, 0.8);
  --vscode-editorOverviewRuler-wordHighlightTextForeground: rgba(160, 160, 160, 0.8);
  --vscode-editorHoverWidget-highlightForeground: #ffc600;
  --vscode-editor-placeholder\.foreground: rgba(255, 255, 255, 0.34);
  --vscode-tab-activeBackground: #193549;
  --vscode-tab-unfocusedActiveBackground: #193549;
  --vscode-tab-inactiveBackground: #122738;
  --vscode-tab-unfocusedInactiveBackground: #122738;
  --vscode-tab-activeForeground: #ffffff;
  --vscode-tab-inactiveForeground: #aaaaaa;
  --vscode-tab-unfocusedActiveForeground: #aaaaaa;
  --vscode-tab-unfocusedInactiveForeground: #aaaaaa;
  --vscode-tab-border: #15232d;
  --vscode-tab-lastPinnedBorder: #585858;
  --vscode-tab-activeBorder: #ffc600;
  --vscode-tab-unfocusedActiveBorder: rgba(255, 198, 0, 0.5);
  --vscode-tab-selectedBackground: #193549;
  --vscode-tab-selectedForeground: #ffffff;
  --vscode-tab-dragAndDropBorder: #ffffff;
  --vscode-tab-activeModifiedBorder: #3399cc;
  --vscode-tab-inactiveModifiedBorder: rgba(51, 153, 204, 0.5);
  --vscode-tab-unfocusedActiveModifiedBorder: rgba(51, 153, 204, 0.5);
  --vscode-tab-unfocusedInactiveModifiedBorder: rgba(51, 153, 204, 0.25);
  --vscode-editorPane-background: #193549;
  --vscode-editorGroupHeader-tabsBackground: #122738;
  --vscode-editorGroupHeader-tabsBorder: #15232d;
  --vscode-editorGroupHeader-noTabsBackground: #193549;
  --vscode-editorGroup-border: #122738;
  --vscode-editorGroup-dropBackground: rgba(18, 39, 56, 0.6);
  --vscode-editorGroup-dropIntoPromptForeground: #aaaaaa;
  --vscode-editorGroup-dropIntoPromptBackground: #15232d;
  --vscode-sideBySideEditor-horizontalBorder: #122738;
  --vscode-sideBySideEditor-verticalBorder: #122738;
  --vscode-panel-background: #122738;
  --vscode-panel-border: #ffc600;
  --vscode-panelTitle-activeForeground: #ffc600;
  --vscode-panelTitle-inactiveForeground: #aaaaaa;
  --vscode-panelTitle-activeBorder: #ffc600;
  --vscode-panelInput-border: #0d3a58;
  --vscode-panel-dropBorder: #ffc600;
  --vscode-panelSection-dropBackground: rgba(18, 39, 56, 0.6);
  --vscode-panelSectionHeader-background: rgba(128, 128, 128, 0.2);
  --vscode-panelSectionHeader-border: rgba(255, 255, 255, 0);
  --vscode-panelSection-border: #ffc600;
  --vscode-panelStickyScroll-background: #122738;
  --vscode-panelStickyScroll-shadow: rgba(0, 0, 0, 0);
  --vscode-banner-background: #193549;
  --vscode-banner-foreground: #aaaaaa;
  --vscode-banner-iconForeground: #3794ff;
  --vscode-statusBar-foreground: #aaaaaa;
  --vscode-statusBar-noFolderForeground: #aaaaaa;
  --vscode-statusBar-background: #15232d;
  --vscode-statusBar-noFolderBackground: #15232d;
  --vscode-statusBar-border: #0d3a58;
  --vscode-statusBar-focusBorder: #aaaaaa;
  --vscode-statusBar-noFolderBorder: #0d3a58;
  --vscode-statusBarItem-activeBackground: #0088ff;
  --vscode-statusBarItem-focusBorder: #aaaaaa;
  --vscode-statusBarItem-hoverBackground: #0d3a58;
  --vscode-statusBarItem-hoverForeground: #aaaaaa;
  --vscode-statusBarItem-compactHoverBackground: rgba(255, 255, 255, 0.2);
  --vscode-statusBarItem-prominentForeground: #aaaaaa;
  --vscode-statusBarItem-prominentBackground: #15232d;
  --vscode-statusBarItem-prominentHoverForeground: #aaaaaa;
  --vscode-statusBarItem-prominentHoverBackground: #0d3a58;
  --vscode-statusBarItem-errorBackground: #611919;
  --vscode-statusBarItem-errorForeground: #ffffff;
  --vscode-statusBarItem-errorHoverForeground: #aaaaaa;
  --vscode-statusBarItem-errorHoverBackground: #0d3a58;
  --vscode-statusBarItem-warningBackground: #997800;
  --vscode-statusBarItem-warningForeground: #ffffff;
  --vscode-statusBarItem-warningHoverForeground: #aaaaaa;
  --vscode-statusBarItem-warningHoverBackground: #0d3a58;
  --vscode-activityBar-background: #122738;
  --vscode-activityBar-foreground: #ffffff;
  --vscode-activityBar-inactiveForeground: rgba(255, 255, 255, 0.4);
  --vscode-activityBar-border: #0d3a58;
  --vscode-activityBar-activeBorder: #ffffff;
  --vscode-activityBar-dropBorder: #ffffff;
  --vscode-activityBarBadge-background: #ffc600;
  --vscode-activityBarBadge-foreground: #000000;
  --vscode-activityBarTop-foreground: #e7e7e7;
  --vscode-activityBarTop-activeBorder: #e7e7e7;
  --vscode-activityBarTop-inactiveForeground: rgba(231, 231, 231, 0.6);
  --vscode-activityBarTop-dropBorder: #e7e7e7;
  --vscode-profileBadge-background: #4d4d4d;
  --vscode-profileBadge-foreground: #ffffff;
  --vscode-statusBarItem-remoteBackground: #15232d;
  --vscode-statusBarItem-remoteForeground: #aaaaaa;
  --vscode-statusBarItem-remoteHoverForeground: #aaaaaa;
  --vscode-statusBarItem-remoteHoverBackground: #0d3a58;
  --vscode-statusBarItem-offlineBackground: #6c1717;
  --vscode-statusBarItem-offlineForeground: #aaaaaa;
  --vscode-statusBarItem-offlineHoverForeground: #aaaaaa;
  --vscode-statusBarItem-offlineHoverBackground: #0d3a58;
  --vscode-extensionBadge-remoteBackground: #ffc600;
  --vscode-extensionBadge-remoteForeground: #000000;
  --vscode-sideBar-background: #15232d;
  --vscode-sideBar-foreground: #aaaaaa;
  --vscode-sideBar-border: #0d3a58;
  --vscode-sideBarTitle-background: #15232d;
  --vscode-sideBarTitle-foreground: #aaaaaa;
  --vscode-sideBar-dropBackground: rgba(18, 39, 56, 0.6);
  --vscode-sideBarSectionHeader-background: #193549;
  --vscode-sideBarSectionHeader-foreground: #aaaaaa;
  --vscode-sideBarSectionHeader-border: rgba(255, 255, 255, 0);
  --vscode-sideBarActivityBarTop-border: rgba(255, 255, 255, 0);
  --vscode-sideBarStickyScroll-background: #15232d;
  --vscode-sideBarStickyScroll-shadow: rgba(0, 0, 0, 0);
  --vscode-titleBar-activeForeground: #ffffff;
  --vscode-titleBar-inactiveForeground: rgba(255, 255, 255, 0.2);
  --vscode-titleBar-activeBackground: #15232d;
  --vscode-titleBar-inactiveBackground: #193549;
  --vscode-menubar-selectionForeground: #ffffff;
  --vscode-menubar-selectionBackground: #0d3a58;
  --vscode-commandCenter-foreground: #ffffff;
  --vscode-commandCenter-activeForeground: #ffffff;
  --vscode-commandCenter-inactiveForeground: rgba(255, 255, 255, 0.2);
  --vscode-commandCenter-background: rgba(255, 255, 255, 0.05);
  --vscode-commandCenter-activeBackground: rgba(255, 255, 255, 0.08);
  --vscode-commandCenter-border: rgba(255, 255, 255, 0.2);
  --vscode-commandCenter-activeBorder: rgba(255, 255, 255, 0.3);
  --vscode-commandCenter-inactiveBorder: rgba(255, 255, 255, 0.05);
  --vscode-notificationCenter-border: #ffc600;
  --vscode-notificationToast-border: #ffc600;
  --vscode-notifications-foreground: #aaaaaa;
  --vscode-notifications-background: #122738;
  --vscode-notificationLink-foreground: #ffc600;
  --vscode-notificationCenterHeader-foreground: #aaaaaa;
  --vscode-notificationCenterHeader-background: #122738;
  --vscode-notifications-border: #ffc600;
  --vscode-notificationsErrorIcon-foreground: #a22929;
  --vscode-notificationsWarningIcon-foreground: #ffc600;
  --vscode-notificationsInfoIcon-foreground: #3794ff;
  --vscode-editorWatermark-foreground: rgba(255, 255, 255, 0.6);
  --vscode-inlineChat-foreground: #aaaaaa;
  --vscode-inlineChat-background: #15232d;
  --vscode-inlineChat-border: #0d3a58;
  --vscode-inlineChat-shadow: rgba(0, 0, 0, 0.15);
  --vscode-inlineChatInput-border: #0d3a58;
  --vscode-inlineChatInput-focusBorder: #0d3a58;
  --vscode-inlineChatInput-placeholderForeground: #aaaaaa;
  --vscode-inlineChatInput-background: #193549;
  --vscode-inlineChatDiff-inserted: rgba(58, 217, 0, 0.1);
  --vscode-editorOverviewRuler-inlineChatInserted: rgba(58, 217, 0, 0.12);
  --vscode-inlineChatDiff-removed: rgba(238, 58, 67, 0.1);
  --vscode-editorOverviewRuler-inlineChatRemoved: rgba(238, 58, 67, 0.12);
  --vscode-extensionButton-background: #0088ff;
  --vscode-extensionButton-foreground: #ffffff;
  --vscode-extensionButton-hoverBackground: #ff9d00;
  --vscode-extensionButton-separator: rgba(255, 255, 255, 0.4);
  --vscode-extensionButton-prominentBackground: #0088ff;
  --vscode-extensionButton-prominentForeground: #ffffff;
  --vscode-extensionButton-prominentHoverBackground: #ff9d00;
  --vscode-editorGutter-modifiedBackground: #26506d;
  --vscode-editorGutter-addedBackground: #3c9f4a;
  --vscode-editorGutter-deletedBackground: #a22929;
  --vscode-minimapGutter-modifiedBackground: #26506d;
  --vscode-minimapGutter-addedBackground: #3c9f4a;
  --vscode-minimapGutter-deletedBackground: #a22929;
  --vscode-editorOverviewRuler-modifiedForeground: rgba(38, 80, 109, 0.6);
  --vscode-editorOverviewRuler-addedForeground: rgba(60, 159, 74, 0.6);
  --vscode-editorOverviewRuler-deletedForeground: rgba(162, 41, 41, 0.6);
  --vscode-chat-requestBorder: rgba(255, 255, 255, 0.1);
  --vscode-chat-requestBackground: rgba(25, 53, 73, 0.62);
  --vscode-chat-slashCommandBackground: rgba(52, 65, 75, 0.56);
  --vscode-chat-slashCommandForeground: #40a6ff;
  --vscode-chat-avatarBackground: #1f1f1f;
  --vscode-chat-avatarForeground: #aaaaaa;
  --vscode-chat-editedFileForeground: #e2c08d;
  --vscode-terminal-background: #122738;
  --vscode-terminal-foreground: #ffffff;
  --vscode-terminalCursor-foreground: #ffc600;
  --vscode-terminalCursor-background: #122738;
  --vscode-terminal-selectionBackground: #0050a4;
  --vscode-terminal-inactiveSelectionBackground: rgba(0, 80, 164, 0.5);
  --vscode-terminalCommandDecoration-defaultBackground: rgba(255, 255, 255, 0.25);
  --vscode-terminalCommandDecoration-successBackground: #1b81a8;
  --vscode-terminalCommandDecoration-errorBackground: #f14c4c;
  --vscode-terminalOverviewRuler-cursorForeground: rgba(160, 160, 160, 0.8);
  --vscode-terminal-border: #ffc600;
  --vscode-terminalOverviewRuler-border: #0d3a58;
  --vscode-terminal-findMatchBackground: rgba(255, 114, 0, 0.4);
  --vscode-terminal-hoverHighlightBackground: rgba(255, 198, 0, 0.1);
  --vscode-terminal-findMatchHighlightBackground: rgba(202, 212, 15, 0.4);
  --vscode-terminalOverviewRuler-findMatchForeground: rgba(209, 134, 22, 0.49);
  --vscode-terminal-dropBackground: rgba(18, 39, 56, 0.6);
  --vscode-terminal-tab\.activeBorder: #ffc600;
  --vscode-terminal-initialHintForeground: rgba(255, 255, 255, 0.34);
  --vscode-scmGraph-historyItemRefColor: #3794ff;
  --vscode-scmGraph-historyItemRemoteRefColor: #b180d7;
  --vscode-scmGraph-historyItemBaseRefColor: #ea5c00;
  --vscode-scmGraph-historyItemHoverDefaultLabelForeground: #aaaaaa;
  --vscode-scmGraph-historyItemHoverDefaultLabelBackground: #ffc600;
  --vscode-scmGraph-historyItemHoverLabelForeground: #ffffff;
  --vscode-scmGraph-historyItemHoverAdditionsForeground: #81b88b;
  --vscode-scmGraph-historyItemHoverDeletionsForeground: #c74e39;
  --vscode-scmGraph-foreground1: #ffb000;
  --vscode-scmGraph-foreground2: #dc267f;
  --vscode-scmGraph-foreground3: #994f00;
  --vscode-scmGraph-foreground4: #40b0a6;
  --vscode-scmGraph-foreground5: #b66dff;
  --vscode-commentsView-resolvedIcon: rgba(204, 204, 204, 0.5);
  --vscode-commentsView-unresolvedIcon: #0d3a58;
  --vscode-editorCommentsWidget-replyInputBackground: #15232d;
  --vscode-editorCommentsWidget-resolvedBorder: rgba(204, 204, 204, 0.5);
  --vscode-editorCommentsWidget-unresolvedBorder: #0d3a58;
  --vscode-editorCommentsWidget-rangeBackground: rgba(13, 58, 88, 0.1);
  --vscode-editorCommentsWidget-rangeActiveBackground: rgba(13, 58, 88, 0.1);
  --vscode-editorGutter-commentRangeForeground: #0d3a58;
  --vscode-editorOverviewRuler-commentForeground: #0d3a58;
  --vscode-editorOverviewRuler-commentUnresolvedForeground: #0d3a58;
  --vscode-editorGutter-commentGlyphForeground: #ffffff;
  --vscode-editorGutter-commentUnresolvedGlyphForeground: #ffffff;
  --vscode-ports-iconRunningProcessForeground: #15232d;
  --vscode-settings-headerForeground: #e7e7e7;
  --vscode-settings-settingsHeaderHoverForeground: rgba(231, 231, 231, 0.7);
  --vscode-settings-modifiedItemIndicator: #0c7d9d;
  --vscode-settings-headerBorder: #ffc600;
  --vscode-settings-sashBorder: #ffc600;
  --vscode-settings-dropdownBackground: #193549;
  --vscode-settings-dropdownForeground: #ffffff;
  --vscode-settings-dropdownBorder: #15232d;
  --vscode-settings-dropdownListBorder: #0d3a58;
  --vscode-settings-checkboxBackground: #193549;
  --vscode-settings-checkboxForeground: #ffffff;
  --vscode-settings-checkboxBorder: #15232d;
  --vscode-settings-textInputBackground: #193549;
  --vscode-settings-textInputForeground: #ffc600;
  --vscode-settings-textInputBorder: #0d3a58;
  --vscode-settings-numberInputBackground: #193549;
  --vscode-settings-numberInputForeground: #ffc600;
  --vscode-settings-numberInputBorder: #0d3a58;
  --vscode-settings-focusedRowBackground: rgba(25, 53, 73, 0.6);
  --vscode-settings-rowHoverBackground: rgba(25, 53, 73, 0.3);
  --vscode-settings-focusedRowBorder: #0d3a58;
  --vscode-keybindingTable-headerBackground: rgba(170, 170, 170, 0.04);
  --vscode-keybindingTable-rowsBackground: rgba(170, 170, 170, 0.04);
  --vscode-debugToolBar-background: #193549;
  --vscode-debugIcon-startForeground: #89d185;
  --vscode-notebook-cellBorderColor: #0d3a58;
  --vscode-notebook-focusedEditorBorder: #0d3a58;
  --vscode-notebookStatusSuccessIcon-foreground: #89d185;
  --vscode-notebookEditorOverviewRuler-runningCellForeground: #89d185;
  --vscode-notebookStatusErrorIcon-foreground: #a22929;
  --vscode-notebookStatusRunningIcon-foreground: #aaaaaa;
  --vscode-notebook-cellToolbarSeparator: rgba(128, 128, 128, 0.35);
  --vscode-notebook-selectedCellBackground: #0d3a58;
  --vscode-notebook-selectedCellBorder: #0d3a58;
  --vscode-notebook-focusedCellBorder: #0d3a58;
  --vscode-notebook-inactiveFocusedCellBorder: #0d3a58;
  --vscode-notebook-cellStatusBarItemHoverBackground: rgba(255, 255, 255, 0.15);
  --vscode-notebook-cellInsertionIndicator: #0d3a58;
  --vscode-notebookScrollbarSlider-background: rgba(64, 97, 121, 0.8);
  --vscode-notebookScrollbarSlider-hoverBackground: rgba(67, 125, 163, 0.8);
  --vscode-notebookScrollbarSlider-activeBackground: rgba(53, 81, 102, 0.8);
  --vscode-notebook-symbolHighlightBackground: rgba(255, 255, 255, 0.04);
  --vscode-notebook-cellEditorBackground: #15232d;
  --vscode-notebook-editorBackground: #193549;
  --vscode-debugIcon-breakpointForeground: #e51400;
  --vscode-debugIcon-breakpointDisabledForeground: #848484;
  --vscode-debugIcon-breakpointUnverifiedForeground: #848484;
  --vscode-debugIcon-breakpointCurrentStackframeForeground: #ffcc00;
  --vscode-debugIcon-breakpointStackframeForeground: #89d185;
  --vscode-editor-stackFrameHighlightBackground: rgba(255, 255, 0, 0.2);
  --vscode-editor-focusedStackFrameHighlightBackground: rgba(122, 189, 122, 0.3);
  --vscode-multiDiffEditor-headerBackground: #262626;
  --vscode-multiDiffEditor-background: #193549;
  --vscode-multiDiffEditor-border: rgba(255, 255, 255, 0);
  --vscode-interactive-activeCodeBorder: #ffc600;
  --vscode-interactive-inactiveCodeBorder: #0d3a58;
  --vscode-testing-iconFailed: #f14c4c;
  --vscode-testing-iconErrored: #f14c4c;
  --vscode-testing-iconPassed: #73c991;
  --vscode-testing-runAction: #73c991;
  --vscode-testing-iconQueued: #cca700;
  --vscode-testing-iconUnset: #848484;
  --vscode-testing-iconSkipped: #848484;
  --vscode-testing-peekBorder: #a22929;
  --vscode-testing-messagePeekBorder: #3794ff;
  --vscode-testing-peekHeaderBackground: rgba(162, 41, 41, 0.1);
  --vscode-testing-messagePeekHeaderBackground: rgba(55, 148, 255, 0.1);
  --vscode-testing-coveredBackground: rgba(58, 217, 0, 0.2);
  --vscode-testing-coveredBorder: rgba(58, 217, 0, 0.15);
  --vscode-testing-coveredGutterBackground: rgba(58, 217, 0, 0.12);
  --vscode-testing-uncoveredBranchBackground: #6e3746;
  --vscode-testing-uncoveredBackground: rgba(238, 58, 67, 0.2);
  --vscode-testing-uncoveredBorder: rgba(238, 58, 67, 0.15);
  --vscode-testing-uncoveredGutterBackground: rgba(238, 58, 67, 0.3);
  --vscode-testing-coverCountBadgeBackground: #ffc600;
  --vscode-testing-coverCountBadgeForeground: #000000;
  --vscode-testing-message\.error\.badgeBackground: #f14c4c;
  --vscode-testing-message\.error\.badgeBorder: #f14c4c;
  --vscode-testing-message\.error\.badgeForeground: #000000;
  --vscode-testing-message\.info\.decorationForeground: rgba(255, 255, 255, 0.5);
  --vscode-testing-iconErrored\.retired: rgba(241, 76, 76, 0.7);
  --vscode-testing-iconFailed\.retired: rgba(241, 76, 76, 0.7);
  --vscode-testing-iconPassed\.retired: rgba(115, 201, 145, 0.7);
  --vscode-testing-iconQueued\.retired: rgba(204, 167, 0, 0.7);
  --vscode-testing-iconUnset\.retired: rgba(132, 132, 132, 0.7);
  --vscode-testing-iconSkipped\.retired: rgba(132, 132, 132, 0.7);
  --vscode-searchEditor-textInputBorder: #0d3a58;
  --vscode-debugExceptionWidget-border: #aaaaaa;
  --vscode-debugExceptionWidget-background: #193549;
  --vscode-editor-inlineValuesForeground: rgba(255, 255, 255, 0.5);
  --vscode-editor-inlineValuesBackground: rgba(255, 200, 0, 0.2);
  --vscode-statusBar-debuggingBackground: #15232d;
  --vscode-statusBar-debuggingForeground: #ffc600;
  --vscode-statusBar-debuggingBorder: #ffc600;
  --vscode-commandCenter-debuggingBackground: rgba(21, 35, 45, 0.26);
  --vscode-debugTokenExpression-name: #c586c0;
  --vscode-debugTokenExpression-type: #4a90e2;
  --vscode-debugTokenExpression-value: rgba(204, 204, 204, 0.6);
  --vscode-debugTokenExpression-string: #ce9178;
  --vscode-debugTokenExpression-boolean: #4e94ce;
  --vscode-debugTokenExpression-number: #b5cea8;
  --vscode-debugTokenExpression-error: #f48771;
  --vscode-debugView-exceptionLabelForeground: #aaaaaa;
  --vscode-debugView-exceptionLabelBackground: #6c2022;
  --vscode-debugView-stateLabelForeground: #aaaaaa;
  --vscode-debugView-stateLabelBackground: rgba(136, 136, 136, 0.27);
  --vscode-debugView-valueChangedHighlight: #569cd6;
  --vscode-debugConsole-infoForeground: #3794ff;
  --vscode-debugConsole-warningForeground: #ffc600;
  --vscode-debugConsole-errorForeground: #ff5630;
  --vscode-debugConsole-sourceForeground: #aaaaaa;
  --vscode-debugConsoleInputIcon-foreground: #aaaaaa;
  --vscode-debugIcon-pauseForeground: #75beff;
  --vscode-debugIcon-stopForeground: #f48771;
  --vscode-debugIcon-disconnectForeground: #f48771;
  --vscode-debugIcon-restartForeground: #89d185;
  --vscode-debugIcon-stepOverForeground: #75beff;
  --vscode-debugIcon-stepIntoForeground: #75beff;
  --vscode-debugIcon-stepOutForeground: #75beff;
  --vscode-debugIcon-continueForeground: #75beff;
  --vscode-debugIcon-stepBackForeground: #75beff;
  --vscode-mergeEditor-change\.background: rgba(155, 185, 85, 0.2);
  --vscode-mergeEditor-change\.word\.background: rgba(156, 204, 44, 0.2);
  --vscode-mergeEditor-changeBase\.background: #4b1818;
  --vscode-mergeEditor-changeBase\.word\.background: #6f1313;
  --vscode-mergeEditor-conflict\.unhandledUnfocused\.border: rgba(255, 166, 0, 0.48);
  --vscode-mergeEditor-conflict\.unhandledFocused\.border: #ffa600;
  --vscode-mergeEditor-conflict\.handledUnfocused\.border: rgba(134, 134, 134, 0.29);
  --vscode-mergeEditor-conflict\.handledFocused\.border: rgba(193, 193, 193, 0.8);
  --vscode-mergeEditor-conflict\.handled\.minimapOverViewRuler: rgba(173, 172, 168, 0.93);
  --vscode-mergeEditor-conflict\.unhandled\.minimapOverViewRuler: #fcba03;
  --vscode-mergeEditor-conflictingLines\.background: rgba(255, 234, 0, 0.28);
  --vscode-mergeEditor-conflict\.input1\.background: rgba(47, 115, 102, 0.4);
  --vscode-mergeEditor-conflict\.input2\.background: rgba(24, 82, 148, 0.4);
  --vscode-extensionIcon-starForeground: #ff8e00;
  --vscode-extensionIcon-verifiedForeground: #0088ff;
  --vscode-extensionIcon-preReleaseForeground: #1d9271;
  --vscode-extensionIcon-sponsorForeground: #d758b3;
  --vscode-terminal-ansiBlack: #000000;
  --vscode-terminal-ansiRed: #ff628c;
  --vscode-terminal-ansiGreen: #3ad900;
  --vscode-terminal-ansiYellow: #ffc600;
  --vscode-terminal-ansiBlue: #0088ff;
  --vscode-terminal-ansiMagenta: #fb94ff;
  --vscode-terminal-ansiCyan: #80fcff;
  --vscode-terminal-ansiWhite: #ffffff;
  --vscode-terminal-ansiBrightBlack: #0050a4;
  --vscode-terminal-ansiBrightRed: #ff628c;
  --vscode-terminal-ansiBrightGreen: #3ad900;
  --vscode-terminal-ansiBrightYellow: #ffc600;
  --vscode-terminal-ansiBrightBlue: #0088ff;
  --vscode-terminal-ansiBrightMagenta: #fb94ff;
  --vscode-terminal-ansiBrightCyan: #80fcff;
  --vscode-terminal-ansiBrightWhite: #ffffff;
  --vscode-simpleFindWidget-sashBorder: #454545;
  --vscode-terminalStickyScrollHover-background: #2a2d2e;
  --vscode-terminalCommandGuide-foreground: #0d3a58;
  --vscode-welcomePage-tileBackground: #15232d;
  --vscode-welcomePage-tileHoverBackground: #192a36;
  --vscode-welcomePage-tileBorder: rgba(255, 255, 255, 0.1);
  --vscode-welcomePage-progress\.background: #193549;
  --vscode-welcomePage-progress\.foreground: #0088ff;
  --vscode-walkthrough-stepTitle\.foreground: #ffffff;
  --vscode-walkThrough-embeddedEditorBackground: #0d3a58;
  --vscode-profiles-sashBorder: #ffc600;
  --vscode-gitDecoration-addedResourceForeground: #81b88b;
  --vscode-gitDecoration-modifiedResourceForeground: #ffc600;
  --vscode-gitDecoration-deletedResourceForeground: #ff628c;
  --vscode-gitDecoration-renamedResourceForeground: #73c991;
  --vscode-gitDecoration-untrackedResourceForeground: #3ad900;
  --vscode-gitDecoration-ignoredResourceForeground: #808080;
  --vscode-gitDecoration-stageModifiedResourceForeground: #e2c08d;
  --vscode-gitDecoration-stageDeletedResourceForeground: #c74e39;
  --vscode-gitDecoration-conflictingResourceForeground: #ff7200;
  --vscode-gitDecoration-submoduleResourceForeground: #8db9e2;
  --vscode-git-blame\.editorDecorationForeground: #aaaaaa;
  --vscode-rust_analyzer-syntaxTreeBorder: #ffffff;
}
