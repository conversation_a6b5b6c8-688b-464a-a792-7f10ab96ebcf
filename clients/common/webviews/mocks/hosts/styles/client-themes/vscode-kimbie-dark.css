/**
 * These values were copied over by hand from a webview in VSCode.
 *
 * Find using the following:
 *     document.querySelector('#_defaultStyles').parentNode.parentNode.style.cssText
 */

:root {
  --text-link-decoration: none;
  --vscode-font-family: -apple-system, BlinkMacSystemFont, sans-serif;
  --vscode-font-weight: normal;
  --vscode-font-size: 13px;
  --vscode-editor-font-family: Operator Mono, "Droid Sans Mono", "monospace", monospace;
  --vscode-editor-font-weight: 360;
  --vscode-editor-font-size: 17px;
  --vscode-foreground: #cccccc;
  --vscode-disabledForeground: rgba(204, 204, 204, 0.5);
  --vscode-errorForeground: #f48771;
  --vscode-descriptionForeground: rgba(204, 204, 204, 0.7);
  --vscode-icon-foreground: #c5c5c5;
  --vscode-focusBorder: #a57a4c;
  --vscode-selection-background: rgba(132, 97, 61, 0.67);
  --vscode-textLink-foreground: #3794ff;
  --vscode-textLink-activeForeground: #3794ff;
  --vscode-textSeparator-foreground: rgba(255, 255, 255, 0.18);
  --vscode-textPreformat-foreground: #d7ba7d;
  --vscode-textPreformat-background: rgba(255, 255, 255, 0.1);
  --vscode-textBlockQuote-background: #222222;
  --vscode-textBlockQuote-border: rgba(0, 122, 204, 0.5);
  --vscode-textCodeBlock-background: rgba(10, 10, 10, 0.4);
  --vscode-sash-hoverBorder: #a57a4c;
  --vscode-badge-background: #7f5d38;
  --vscode-badge-foreground: #ffffff;
  --vscode-activityWarningBadge-foreground: #000000;
  --vscode-activityWarningBadge-background: #cca700;
  --vscode-activityErrorBadge-foreground: #000000;
  --vscode-activityErrorBadge-background: #f14c4c;
  --vscode-scrollbar-shadow: #000000;
  --vscode-scrollbarSlider-background: rgba(121, 121, 121, 0.4);
  --vscode-scrollbarSlider-hoverBackground: rgba(100, 100, 100, 0.7);
  --vscode-scrollbarSlider-activeBackground: rgba(191, 191, 191, 0.4);
  --vscode-progressBar-background: #7f5d38;
  --vscode-chart-line: #236b8e;
  --vscode-chart-axis: rgba(191, 191, 191, 0.4);
  --vscode-chart-guide: rgba(191, 191, 191, 0.2);
  --vscode-editor-background: #221a0f;
  --vscode-editor-foreground: #d3af86;
  --vscode-editorStickyScroll-background: #221a0f;
  --vscode-editorStickyScrollHover-background: #2a2d2e;
  --vscode-editorStickyScroll-shadow: #000000;
  --vscode-editorWidget-background: #131510;
  --vscode-editorWidget-foreground: #cccccc;
  --vscode-editorWidget-border: #454545;
  --vscode-editorError-foreground: #f14c4c;
  --vscode-editorWarning-foreground: #cca700;
  --vscode-editorInfo-foreground: #3794ff;
  --vscode-editorHint-foreground: rgba(238, 238, 238, 0.7);
  --vscode-editorLink-activeForeground: #4e94ce;
  --vscode-editor-selectionBackground: rgba(132, 97, 61, 0.67);
  --vscode-editor-inactiveSelectionBackground: rgba(132, 97, 61, 0.33);
  --vscode-editor-selectionHighlightBackground: rgba(96, 70, 44, 0.4);
  --vscode-editor-compositionBorder: #ffffff;
  --vscode-editor-findMatchBackground: #515c6a;
  --vscode-editor-findMatchHighlightBackground: rgba(234, 92, 0, 0.33);
  --vscode-editor-findRangeHighlightBackground: rgba(58, 61, 65, 0.4);
  --vscode-editor-hoverHighlightBackground: rgba(38, 79, 120, 0.25);
  --vscode-editorHoverWidget-background: #221a14;
  --vscode-editorHoverWidget-foreground: #cccccc;
  --vscode-editorHoverWidget-border: #454545;
  --vscode-editorHoverWidget-statusBarBackground: #291f18;
  --vscode-editorInlayHint-foreground: #969696;
  --vscode-editorInlayHint-background: rgba(127, 93, 56, 0.1);
  --vscode-editorInlayHint-typeForeground: #969696;
  --vscode-editorInlayHint-typeBackground: rgba(127, 93, 56, 0.1);
  --vscode-editorInlayHint-parameterForeground: #969696;
  --vscode-editorInlayHint-parameterBackground: rgba(127, 93, 56, 0.1);
  --vscode-editorLightBulb-foreground: #ffcc00;
  --vscode-editorLightBulbAutoFix-foreground: #75beff;
  --vscode-editorLightBulbAi-foreground: #ffcc00;
  --vscode-editor-snippetTabstopHighlightBackground: rgba(124, 124, 124, 0.3);
  --vscode-editor-snippetFinalTabstopHighlightBorder: #525252;
  --vscode-diffEditor-insertedTextBackground: rgba(156, 204, 44, 0.2);
  --vscode-diffEditor-removedTextBackground: rgba(255, 0, 0, 0.2);
  --vscode-diffEditor-insertedLineBackground: rgba(155, 185, 85, 0.2);
  --vscode-diffEditor-removedLineBackground: rgba(255, 0, 0, 0.2);
  --vscode-diffEditor-diagonalFill: rgba(204, 204, 204, 0.2);
  --vscode-diffEditor-unchangedRegionBackground: #362712;
  --vscode-diffEditor-unchangedRegionForeground: #cccccc;
  --vscode-diffEditor-unchangedCodeBackground: rgba(116, 116, 116, 0.16);
  --vscode-widget-shadow: rgba(0, 0, 0, 0.36);
  --vscode-toolbar-hoverBackground: rgba(90, 93, 94, 0.31);
  --vscode-toolbar-activeBackground: rgba(99, 102, 103, 0.31);
  --vscode-breadcrumb-foreground: rgba(204, 204, 204, 0.8);
  --vscode-breadcrumb-background: #221a0f;
  --vscode-breadcrumb-focusForeground: #e0e0e0;
  --vscode-breadcrumb-activeSelectionForeground: #e0e0e0;
  --vscode-breadcrumbPicker-background: #131510;
  --vscode-merge-currentHeaderBackground: rgba(64, 200, 174, 0.5);
  --vscode-merge-currentContentBackground: rgba(64, 200, 174, 0.2);
  --vscode-merge-incomingHeaderBackground: rgba(64, 166, 255, 0.5);
  --vscode-merge-incomingContentBackground: rgba(64, 166, 255, 0.2);
  --vscode-merge-commonHeaderBackground: rgba(96, 96, 96, 0.4);
  --vscode-merge-commonContentBackground: rgba(96, 96, 96, 0.16);
  --vscode-editorOverviewRuler-currentContentForeground: rgba(64, 200, 174, 0.5);
  --vscode-editorOverviewRuler-incomingContentForeground: rgba(64, 166, 255, 0.5);
  --vscode-editorOverviewRuler-commonContentForeground: rgba(96, 96, 96, 0.4);
  --vscode-editorOverviewRuler-findMatchForeground: rgba(209, 134, 22, 0.49);
  --vscode-editorOverviewRuler-selectionHighlightForeground: rgba(160, 160, 160, 0.8);
  --vscode-problemsErrorIcon-foreground: #f14c4c;
  --vscode-problemsWarningIcon-foreground: #cca700;
  --vscode-problemsInfoIcon-foreground: #3794ff;
  --vscode-minimap-findMatchHighlight: #d18616;
  --vscode-minimap-selectionOccurrenceHighlight: #676767;
  --vscode-minimap-selectionHighlight: rgba(132, 97, 61, 0.67);
  --vscode-minimap-infoHighlight: #3794ff;
  --vscode-minimap-warningHighlight: #cca700;
  --vscode-minimap-errorHighlight: rgba(255, 18, 18, 0.7);
  --vscode-minimap-foregroundOpacity: #000000;
  --vscode-minimapSlider-background: rgba(121, 121, 121, 0.2);
  --vscode-minimapSlider-hoverBackground: rgba(100, 100, 100, 0.35);
  --vscode-minimapSlider-activeBackground: rgba(191, 191, 191, 0.2);
  --vscode-charts-foreground: #cccccc;
  --vscode-charts-lines: rgba(204, 204, 204, 0.5);
  --vscode-charts-red: #f14c4c;
  --vscode-charts-blue: #3794ff;
  --vscode-charts-yellow: #cca700;
  --vscode-charts-orange: #d18616;
  --vscode-charts-green: #89d185;
  --vscode-charts-purple: #b180d7;
  --vscode-input-background: #51412c;
  --vscode-input-foreground: #cccccc;
  --vscode-inputOption-activeBorder: #a57a4c;
  --vscode-inputOption-hoverBackground: rgba(90, 93, 94, 0.5);
  --vscode-inputOption-activeBackground: rgba(165, 122, 76, 0.4);
  --vscode-inputOption-activeForeground: #ffffff;
  --vscode-input-placeholderForeground: rgba(204, 204, 204, 0.5);
  --vscode-inputValidation-infoBackground: #2b2a42;
  --vscode-inputValidation-infoBorder: #1b60a5;
  --vscode-inputValidation-warningBackground: #51412c;
  --vscode-inputValidation-warningBorder: #b89500;
  --vscode-inputValidation-errorBackground: #5f0d0d;
  --vscode-inputValidation-errorBorder: #9d2f23;
  --vscode-dropdown-background: #51412c;
  --vscode-dropdown-foreground: #f0f0f0;
  --vscode-dropdown-border: #51412c;
  --vscode-button-foreground: #ffffff;
  --vscode-button-separator: rgba(255, 255, 255, 0.4);
  --vscode-button-background: #6e583b;
  --vscode-button-hoverBackground: #846947;
  --vscode-button-secondaryForeground: #ffffff;
  --vscode-button-secondaryBackground: #3a3d41;
  --vscode-button-secondaryHoverBackground: #45494e;
  --vscode-radio-activeForeground: #ffffff;
  --vscode-radio-activeBackground: rgba(165, 122, 76, 0.4);
  --vscode-radio-activeBorder: #a57a4c;
  --vscode-radio-inactiveBorder: rgba(255, 255, 255, 0.2);
  --vscode-radio-inactiveHoverBackground: rgba(90, 93, 94, 0.5);
  --vscode-checkbox-background: #51412c;
  --vscode-checkbox-selectBackground: #131510;
  --vscode-checkbox-foreground: #f0f0f0;
  --vscode-checkbox-border: #51412c;
  --vscode-checkbox-selectBorder: #c5c5c5;
  --vscode-keybindingLabel-background: rgba(128, 128, 128, 0.17);
  --vscode-keybindingLabel-foreground: #cccccc;
  --vscode-keybindingLabel-border: rgba(51, 51, 51, 0.6);
  --vscode-keybindingLabel-bottomBorder: rgba(68, 68, 68, 0.6);
  --vscode-list-focusOutline: #a57a4c;
  --vscode-list-activeSelectionBackground: #7c5021;
  --vscode-list-activeSelectionForeground: #ffffff;
  --vscode-list-inactiveSelectionBackground: #645342;
  --vscode-list-hoverBackground: rgba(124, 80, 33, 0.4);
  --vscode-list-dropBackground: #062f4a;
  --vscode-list-dropBetweenBackground: #c5c5c5;
  --vscode-list-highlightForeground: #e3b583;
  --vscode-list-focusHighlightForeground: #e3b583;
  --vscode-list-invalidItemForeground: #b89500;
  --vscode-list-errorForeground: #f88070;
  --vscode-list-warningForeground: #cca700;
  --vscode-listFilterWidget-background: #131510;
  --vscode-listFilterWidget-outline: rgba(0, 0, 0, 0);
  --vscode-listFilterWidget-noMatchesOutline: #be1100;
  --vscode-listFilterWidget-shadow: rgba(0, 0, 0, 0.36);
  --vscode-list-filterMatchBackground: rgba(234, 92, 0, 0.33);
  --vscode-list-deemphasizedForeground: #8c8c8c;
  --vscode-tree-indentGuidesStroke: #585858;
  --vscode-tree-inactiveIndentGuidesStroke: rgba(88, 88, 88, 0.4);
  --vscode-tree-tableColumnsBorder: rgba(204, 204, 204, 0.13);
  --vscode-tree-tableOddRowsBackground: rgba(204, 204, 204, 0.04);
  --vscode-editorActionList-background: #131510;
  --vscode-editorActionList-foreground: #cccccc;
  --vscode-editorActionList-focusForeground: #ffffff;
  --vscode-editorActionList-focusBackground: #7c5021;
  --vscode-menu-foreground: #cccccc;
  --vscode-menu-background: #362712;
  --vscode-menu-selectionForeground: #ffffff;
  --vscode-menu-selectionBackground: #7c5021;
  --vscode-menu-separatorBackground: #606060;
  --vscode-quickInput-background: #131510;
  --vscode-quickInput-foreground: #cccccc;
  --vscode-quickInputTitle-background: rgba(255, 255, 255, 0.1);
  --vscode-pickerGroup-foreground: #e3b583;
  --vscode-pickerGroup-border: #e3b583;
  --vscode-quickInputList-focusForeground: #ffffff;
  --vscode-quickInputList-focusBackground: rgba(124, 80, 33, 0.67);
  --vscode-search-resultsInfoForeground: rgba(204, 204, 204, 0.65);
  --vscode-searchEditor-findMatchBackground: rgba(234, 92, 0, 0.22);
  --vscode-editor-lineHighlightBackground: #5e452b;
  --vscode-editor-lineHighlightBorder: #282828;
  --vscode-editor-rangeHighlightBackground: rgba(255, 255, 255, 0.04);
  --vscode-editor-symbolHighlightBackground: rgba(234, 92, 0, 0.33);
  --vscode-editorCursor-foreground: #d3af86;
  --vscode-editorMultiCursor-primary\.foreground: #d3af86;
  --vscode-editorMultiCursor-secondary\.foreground: #d3af86;
  --vscode-editorWhitespace-foreground: #a57a4c;
  --vscode-editorLineNumber-foreground: #858585;
  --vscode-editorIndentGuide-background: #a57a4c;
  --vscode-editorIndentGuide-activeBackground: #a57a4c;
  --vscode-editorIndentGuide-background1: #a57a4c;
  --vscode-editorIndentGuide-background2: rgba(0, 0, 0, 0);
  --vscode-editorIndentGuide-background3: rgba(0, 0, 0, 0);
  --vscode-editorIndentGuide-background4: rgba(0, 0, 0, 0);
  --vscode-editorIndentGuide-background5: rgba(0, 0, 0, 0);
  --vscode-editorIndentGuide-background6: rgba(0, 0, 0, 0);
  --vscode-editorIndentGuide-activeBackground1: #a57a4c;
  --vscode-editorIndentGuide-activeBackground2: rgba(0, 0, 0, 0);
  --vscode-editorIndentGuide-activeBackground3: rgba(0, 0, 0, 0);
  --vscode-editorIndentGuide-activeBackground4: rgba(0, 0, 0, 0);
  --vscode-editorIndentGuide-activeBackground5: rgba(0, 0, 0, 0);
  --vscode-editorIndentGuide-activeBackground6: rgba(0, 0, 0, 0);
  --vscode-editorActiveLineNumber-foreground: #c6c6c6;
  --vscode-editorLineNumber-activeForeground: #adadad;
  --vscode-editorRuler-foreground: #5a5a5a;
  --vscode-editorCodeLens-foreground: #999999;
  --vscode-editorBracketMatch-background: rgba(0, 100, 0, 0.1);
  --vscode-editorBracketMatch-border: #888888;
  --vscode-editorOverviewRuler-border: rgba(127, 127, 127, 0.3);
  --vscode-editorGutter-background: #221a0f;
  --vscode-editorUnnecessaryCode-opacity: rgba(0, 0, 0, 0.67);
  --vscode-editorGhostText-foreground: rgba(255, 255, 255, 0.34);
  --vscode-editorOverviewRuler-rangeHighlightForeground: rgba(0, 122, 204, 0.6);
  --vscode-editorOverviewRuler-errorForeground: rgba(255, 18, 18, 0.7);
  --vscode-editorOverviewRuler-warningForeground: #cca700;
  --vscode-editorOverviewRuler-infoForeground: #3794ff;
  --vscode-editorBracketHighlight-foreground1: #ffd700;
  --vscode-editorBracketHighlight-foreground2: #da70d6;
  --vscode-editorBracketHighlight-foreground3: #179fff;
  --vscode-editorBracketHighlight-foreground4: rgba(0, 0, 0, 0);
  --vscode-editorBracketHighlight-foreground5: rgba(0, 0, 0, 0);
  --vscode-editorBracketHighlight-foreground6: rgba(0, 0, 0, 0);
  --vscode-editorBracketHighlight-unexpectedBracket\.foreground: rgba(255, 18, 18, 0.8);
  --vscode-editorBracketPairGuide-background1: rgba(0, 0, 0, 0);
  --vscode-editorBracketPairGuide-background2: rgba(0, 0, 0, 0);
  --vscode-editorBracketPairGuide-background3: rgba(0, 0, 0, 0);
  --vscode-editorBracketPairGuide-background4: rgba(0, 0, 0, 0);
  --vscode-editorBracketPairGuide-background5: rgba(0, 0, 0, 0);
  --vscode-editorBracketPairGuide-background6: rgba(0, 0, 0, 0);
  --vscode-editorBracketPairGuide-activeBackground1: rgba(0, 0, 0, 0);
  --vscode-editorBracketPairGuide-activeBackground2: rgba(0, 0, 0, 0);
  --vscode-editorBracketPairGuide-activeBackground3: rgba(0, 0, 0, 0);
  --vscode-editorBracketPairGuide-activeBackground4: rgba(0, 0, 0, 0);
  --vscode-editorBracketPairGuide-activeBackground5: rgba(0, 0, 0, 0);
  --vscode-editorBracketPairGuide-activeBackground6: rgba(0, 0, 0, 0);
  --vscode-editorUnicodeHighlight-border: #cca700;
  --vscode-diffEditor-move\.border: rgba(139, 139, 139, 0.61);
  --vscode-diffEditor-moveActive\.border: #ffa500;
  --vscode-diffEditor-unchangedRegionShadow: #000000;
  --vscode-editorOverviewRuler-bracketMatchForeground: #a0a0a0;
  --vscode-actionBar-toggledBackground: rgba(165, 122, 76, 0.4);
  --vscode-symbolIcon-arrayForeground: #cccccc;
  --vscode-symbolIcon-booleanForeground: #cccccc;
  --vscode-symbolIcon-classForeground: #ee9d28;
  --vscode-symbolIcon-colorForeground: #cccccc;
  --vscode-symbolIcon-constantForeground: #cccccc;
  --vscode-symbolIcon-constructorForeground: #b180d7;
  --vscode-symbolIcon-enumeratorForeground: #ee9d28;
  --vscode-symbolIcon-enumeratorMemberForeground: #75beff;
  --vscode-symbolIcon-eventForeground: #ee9d28;
  --vscode-symbolIcon-fieldForeground: #75beff;
  --vscode-symbolIcon-fileForeground: #cccccc;
  --vscode-symbolIcon-folderForeground: #cccccc;
  --vscode-symbolIcon-functionForeground: #b180d7;
  --vscode-symbolIcon-interfaceForeground: #75beff;
  --vscode-symbolIcon-keyForeground: #cccccc;
  --vscode-symbolIcon-keywordForeground: #cccccc;
  --vscode-symbolIcon-methodForeground: #b180d7;
  --vscode-symbolIcon-moduleForeground: #cccccc;
  --vscode-symbolIcon-namespaceForeground: #cccccc;
  --vscode-symbolIcon-nullForeground: #cccccc;
  --vscode-symbolIcon-numberForeground: #cccccc;
  --vscode-symbolIcon-objectForeground: #cccccc;
  --vscode-symbolIcon-operatorForeground: #cccccc;
  --vscode-symbolIcon-packageForeground: #cccccc;
  --vscode-symbolIcon-propertyForeground: #cccccc;
  --vscode-symbolIcon-referenceForeground: #cccccc;
  --vscode-symbolIcon-snippetForeground: #cccccc;
  --vscode-symbolIcon-stringForeground: #cccccc;
  --vscode-symbolIcon-structForeground: #cccccc;
  --vscode-symbolIcon-textForeground: #cccccc;
  --vscode-symbolIcon-typeParameterForeground: #cccccc;
  --vscode-symbolIcon-unitForeground: #cccccc;
  --vscode-symbolIcon-variableForeground: #75beff;
  --vscode-peekViewTitle-background: #362712;
  --vscode-peekViewTitleLabel-foreground: #ffffff;
  --vscode-peekViewTitleDescription-foreground: rgba(204, 204, 204, 0.7);
  --vscode-peekView-border: #5e452b;
  --vscode-peekViewResult-background: #362712;
  --vscode-peekViewResult-lineForeground: #bbbbbb;
  --vscode-peekViewResult-fileForeground: #ffffff;
  --vscode-peekViewResult-selectionBackground: rgba(51, 153, 255, 0.2);
  --vscode-peekViewResult-selectionForeground: #ffffff;
  --vscode-peekViewEditor-background: #221a14;
  --vscode-peekViewEditorGutter-background: #221a14;
  --vscode-peekViewEditorStickyScroll-background: #221a14;
  --vscode-peekViewResult-matchHighlightBackground: rgba(234, 92, 0, 0.3);
  --vscode-peekViewEditor-matchHighlightBackground: rgba(132, 97, 61, 0.67);
  --vscode-editor-foldBackground: rgba(132, 97, 61, 0.2);
  --vscode-editor-foldPlaceholderForeground: #808080;
  --vscode-editorGutter-foldingControlForeground: #c5c5c5;
  --vscode-editorSuggestWidget-background: #131510;
  --vscode-editorSuggestWidget-border: #454545;
  --vscode-editorSuggestWidget-foreground: #d3af86;
  --vscode-editorSuggestWidget-selectedForeground: #ffffff;
  --vscode-editorSuggestWidget-selectedBackground: rgba(124, 80, 33, 0.67);
  --vscode-editorSuggestWidget-highlightForeground: #e3b583;
  --vscode-editorSuggestWidget-focusHighlightForeground: #e3b583;
  --vscode-editorSuggestWidgetStatus-foreground: rgba(211, 175, 134, 0.5);
  --vscode-inlineEdit-indicator\.foreground: #ffffff;
  --vscode-inlineEdit-indicator\.background: #6e583b;
  --vscode-inlineEdit-indicator\.border: rgba(255, 255, 255, 0.4);
  --vscode-inlineEdit-originalBackground: rgba(255, 0, 0, 0.08);
  --vscode-inlineEdit-modifiedBackground: rgba(156, 204, 44, 0.08);
  --vscode-inlineEdit-originalChangedLineBackground: rgba(0, 0, 0, 0);
  --vscode-inlineEdit-originalChangedTextBackground: rgba(255, 0, 0, 0.2);
  --vscode-inlineEdit-modifiedChangedLineBackground: rgba(0, 0, 0, 0);
  --vscode-inlineEdit-modifiedChangedTextBackground: rgba(156, 204, 44, 0.2);
  --vscode-inlineEdit-border: #3c3c3c;
  --vscode-editorMarkerNavigationError-background: #f14c4c;
  --vscode-editorMarkerNavigationError-headerBackground: rgba(241, 76, 76, 0.1);
  --vscode-editorMarkerNavigationWarning-background: #cca700;
  --vscode-editorMarkerNavigationWarning-headerBackground: rgba(204, 167, 0, 0.1);
  --vscode-editorMarkerNavigationInfo-background: #3794ff;
  --vscode-editorMarkerNavigationInfo-headerBackground: rgba(55, 148, 255, 0.1);
  --vscode-editorMarkerNavigation-background: #221a0f;
  --vscode-editor-linkedEditingBackground: rgba(255, 0, 0, 0.3);
  --vscode-editor-wordHighlightBackground: rgba(87, 87, 87, 0.72);
  --vscode-editor-wordHighlightStrongBackground: rgba(0, 73, 114, 0.72);
  --vscode-editor-wordHighlightTextBackground: rgba(87, 87, 87, 0.72);
  --vscode-editorOverviewRuler-wordHighlightForeground: rgba(160, 160, 160, 0.8);
  --vscode-editorOverviewRuler-wordHighlightStrongForeground: rgba(192, 160, 192, 0.8);
  --vscode-editorOverviewRuler-wordHighlightTextForeground: rgba(160, 160, 160, 0.8);
  --vscode-editorHoverWidget-highlightForeground: #e3b583;
  --vscode-editor-placeholder\.foreground: rgba(255, 255, 255, 0.34);
  --vscode-tab-activeBackground: #221a0f;
  --vscode-tab-unfocusedActiveBackground: #221a0f;
  --vscode-tab-inactiveBackground: #131510;
  --vscode-tab-unfocusedInactiveBackground: #131510;
  --vscode-tab-activeForeground: #ffffff;
  --vscode-tab-inactiveForeground: rgba(255, 255, 255, 0.5);
  --vscode-tab-unfocusedActiveForeground: rgba(255, 255, 255, 0.5);
  --vscode-tab-unfocusedInactiveForeground: rgba(255, 255, 255, 0.25);
  --vscode-tab-border: #252526;
  --vscode-tab-lastPinnedBorder: #51412c;
  --vscode-tab-selectedBackground: #221a0f;
  --vscode-tab-selectedForeground: #ffffff;
  --vscode-tab-dragAndDropBorder: #ffffff;
  --vscode-tab-activeModifiedBorder: #3399cc;
  --vscode-tab-inactiveModifiedBorder: rgba(51, 153, 204, 0.5);
  --vscode-tab-unfocusedActiveModifiedBorder: rgba(51, 153, 204, 0.5);
  --vscode-tab-unfocusedInactiveModifiedBorder: rgba(51, 153, 204, 0.25);
  --vscode-editorPane-background: #221a0f;
  --vscode-editorGroupHeader-tabsBackground: #131510;
  --vscode-editorGroupHeader-noTabsBackground: #221a0f;
  --vscode-editorGroup-border: #444444;
  --vscode-editorGroup-dropBackground: rgba(83, 89, 93, 0.5);
  --vscode-editorGroup-dropIntoPromptForeground: #cccccc;
  --vscode-editorGroup-dropIntoPromptBackground: #131510;
  --vscode-sideBySideEditor-horizontalBorder: #444444;
  --vscode-sideBySideEditor-verticalBorder: #444444;
  --vscode-panel-background: #221a0f;
  --vscode-panel-border: rgba(128, 128, 128, 0.35);
  --vscode-panelTitle-activeForeground: #e7e7e7;
  --vscode-panelTitle-inactiveForeground: rgba(231, 231, 231, 0.6);
  --vscode-panelTitle-activeBorder: #e7e7e7;
  --vscode-panel-dropBorder: #e7e7e7;
  --vscode-panelSection-dropBackground: rgba(83, 89, 93, 0.5);
  --vscode-panelSectionHeader-background: rgba(128, 128, 128, 0.2);
  --vscode-panelSection-border: rgba(128, 128, 128, 0.35);
  --vscode-panelStickyScroll-background: #221a0f;
  --vscode-panelStickyScroll-shadow: #000000;
  --vscode-banner-background: #7c5021;
  --vscode-banner-foreground: #ffffff;
  --vscode-banner-iconForeground: #3794ff;
  --vscode-statusBar-foreground: #ffffff;
  --vscode-statusBar-noFolderForeground: #ffffff;
  --vscode-statusBar-background: #423523;
  --vscode-statusBar-noFolderBackground: #423523;
  --vscode-statusBar-focusBorder: #ffffff;
  --vscode-statusBarItem-activeBackground: rgba(255, 255, 255, 0.18);
  --vscode-statusBarItem-focusBorder: #ffffff;
  --vscode-statusBarItem-hoverBackground: rgba(255, 255, 255, 0.12);
  --vscode-statusBarItem-hoverForeground: #ffffff;
  --vscode-statusBarItem-compactHoverBackground: rgba(255, 255, 255, 0.2);
  --vscode-statusBarItem-prominentForeground: #ffffff;
  --vscode-statusBarItem-prominentBackground: rgba(0, 0, 0, 0.5);
  --vscode-statusBarItem-prominentHoverForeground: #ffffff;
  --vscode-statusBarItem-prominentHoverBackground: rgba(0, 0, 0, 0.3);
  --vscode-statusBarItem-errorBackground: #c72e0f;
  --vscode-statusBarItem-errorForeground: #ffffff;
  --vscode-statusBarItem-errorHoverForeground: #ffffff;
  --vscode-statusBarItem-errorHoverBackground: rgba(255, 255, 255, 0.12);
  --vscode-statusBarItem-warningBackground: #7a6400;
  --vscode-statusBarItem-warningForeground: #ffffff;
  --vscode-statusBarItem-warningHoverForeground: #ffffff;
  --vscode-statusBarItem-warningHoverBackground: rgba(255, 255, 255, 0.12);
  --vscode-activityBar-background: #221a0f;
  --vscode-activityBar-foreground: #d3af86;
  --vscode-activityBar-inactiveForeground: rgba(211, 175, 134, 0.4);
  --vscode-activityBar-activeBorder: #d3af86;
  --vscode-activityBar-dropBorder: #d3af86;
  --vscode-activityBarBadge-background: #007acc;
  --vscode-activityBarBadge-foreground: #ffffff;
  --vscode-activityBarTop-foreground: #e7e7e7;
  --vscode-activityBarTop-activeBorder: #e7e7e7;
  --vscode-activityBarTop-inactiveForeground: rgba(231, 231, 231, 0.6);
  --vscode-activityBarTop-dropBorder: #e7e7e7;
  --vscode-profileBadge-background: #4d4d4d;
  --vscode-profileBadge-foreground: #ffffff;
  --vscode-statusBarItem-remoteBackground: #6e583b;
  --vscode-statusBarItem-remoteForeground: #ffffff;
  --vscode-statusBarItem-remoteHoverForeground: #ffffff;
  --vscode-statusBarItem-remoteHoverBackground: rgba(255, 255, 255, 0.12);
  --vscode-statusBarItem-offlineBackground: #6c1717;
  --vscode-statusBarItem-offlineForeground: #ffffff;
  --vscode-statusBarItem-offlineHoverForeground: #ffffff;
  --vscode-statusBarItem-offlineHoverBackground: rgba(255, 255, 255, 0.12);
  --vscode-extensionBadge-remoteBackground: #007acc;
  --vscode-extensionBadge-remoteForeground: #ffffff;
  --vscode-sideBar-background: #362712;
  --vscode-sideBarTitle-background: #362712;
  --vscode-sideBar-dropBackground: rgba(83, 89, 93, 0.5);
  --vscode-sideBarSectionHeader-background: rgba(128, 128, 128, 0.2);
  --vscode-sideBarStickyScroll-background: #362712;
  --vscode-sideBarStickyScroll-shadow: #000000;
  --vscode-titleBar-activeForeground: #cccccc;
  --vscode-titleBar-inactiveForeground: rgba(204, 204, 204, 0.6);
  --vscode-titleBar-activeBackground: #423523;
  --vscode-titleBar-inactiveBackground: rgba(66, 53, 35, 0.6);
  --vscode-menubar-selectionForeground: #cccccc;
  --vscode-menubar-selectionBackground: rgba(90, 93, 94, 0.31);
  --vscode-commandCenter-foreground: #cccccc;
  --vscode-commandCenter-activeForeground: #cccccc;
  --vscode-commandCenter-inactiveForeground: rgba(204, 204, 204, 0.6);
  --vscode-commandCenter-background: rgba(255, 255, 255, 0.05);
  --vscode-commandCenter-activeBackground: rgba(255, 255, 255, 0.08);
  --vscode-commandCenter-border: rgba(204, 204, 204, 0.2);
  --vscode-commandCenter-activeBorder: rgba(204, 204, 204, 0.3);
  --vscode-commandCenter-inactiveBorder: rgba(204, 204, 204, 0.15);
  --vscode-notifications-foreground: #cccccc;
  --vscode-notifications-background: #131510;
  --vscode-notificationLink-foreground: #3794ff;
  --vscode-notificationCenterHeader-background: #191b15;
  --vscode-notifications-border: #191b15;
  --vscode-notificationsErrorIcon-foreground: #f14c4c;
  --vscode-notificationsWarningIcon-foreground: #cca700;
  --vscode-notificationsInfoIcon-foreground: #3794ff;
  --vscode-editorWatermark-foreground: rgba(211, 175, 134, 0.6);
  --vscode-inlineChat-foreground: #cccccc;
  --vscode-inlineChat-background: #131510;
  --vscode-inlineChat-border: #454545;
  --vscode-inlineChat-shadow: rgba(0, 0, 0, 0.36);
  --vscode-inlineChatInput-border: #454545;
  --vscode-inlineChatInput-focusBorder: #a57a4c;
  --vscode-inlineChatInput-placeholderForeground: rgba(204, 204, 204, 0.5);
  --vscode-inlineChatInput-background: #51412c;
  --vscode-inlineChatDiff-inserted: rgba(156, 204, 44, 0.1);
  --vscode-editorOverviewRuler-inlineChatInserted: rgba(156, 204, 44, 0.12);
  --vscode-inlineChatDiff-removed: rgba(255, 0, 0, 0.1);
  --vscode-editorOverviewRuler-inlineChatRemoved: rgba(255, 0, 0, 0.12);
  --vscode-extensionButton-background: #6e583b;
  --vscode-extensionButton-foreground: #ffffff;
  --vscode-extensionButton-hoverBackground: #846947;
  --vscode-extensionButton-separator: rgba(255, 255, 255, 0.4);
  --vscode-extensionButton-prominentBackground: #6e583b;
  --vscode-extensionButton-prominentForeground: #ffffff;
  --vscode-extensionButton-prominentHoverBackground: #846947;
  --vscode-editorGutter-modifiedBackground: #1b81a8;
  --vscode-editorGutter-addedBackground: #487e02;
  --vscode-editorGutter-deletedBackground: #f14c4c;
  --vscode-minimapGutter-modifiedBackground: #1b81a8;
  --vscode-minimapGutter-addedBackground: #487e02;
  --vscode-minimapGutter-deletedBackground: #f14c4c;
  --vscode-editorOverviewRuler-modifiedForeground: rgba(27, 129, 168, 0.6);
  --vscode-editorOverviewRuler-addedForeground: rgba(72, 126, 2, 0.6);
  --vscode-editorOverviewRuler-deletedForeground: rgba(241, 76, 76, 0.6);
  --vscode-chat-requestBorder: rgba(255, 255, 255, 0.1);
  --vscode-chat-requestBackground: rgba(34, 26, 15, 0.62);
  --vscode-chat-slashCommandBackground: rgba(52, 65, 75, 0.56);
  --vscode-chat-slashCommandForeground: #40a6ff;
  --vscode-chat-avatarBackground: #1f1f1f;
  --vscode-chat-avatarForeground: #cccccc;
  --vscode-chat-editedFileForeground: #e2c08d;
  --vscode-terminal-foreground: #cccccc;
  --vscode-terminal-selectionBackground: rgba(132, 97, 61, 0.67);
  --vscode-terminal-inactiveSelectionBackground: rgba(132, 97, 61, 0.33);
  --vscode-terminalCommandDecoration-defaultBackground: rgba(255, 255, 255, 0.25);
  --vscode-terminalCommandDecoration-successBackground: #1b81a8;
  --vscode-terminalCommandDecoration-errorBackground: #f14c4c;
  --vscode-terminalOverviewRuler-cursorForeground: rgba(160, 160, 160, 0.8);
  --vscode-terminal-border: rgba(128, 128, 128, 0.35);
  --vscode-terminalOverviewRuler-border: rgba(127, 127, 127, 0.3);
  --vscode-terminal-findMatchBackground: #515c6a;
  --vscode-terminal-hoverHighlightBackground: rgba(38, 79, 120, 0.13);
  --vscode-terminal-findMatchHighlightBackground: rgba(234, 92, 0, 0.33);
  --vscode-terminalOverviewRuler-findMatchForeground: rgba(209, 134, 22, 0.49);
  --vscode-terminal-dropBackground: rgba(83, 89, 93, 0.5);
  --vscode-terminal-initialHintForeground: rgba(255, 255, 255, 0.34);
  --vscode-scmGraph-historyItemRefColor: #3794ff;
  --vscode-scmGraph-historyItemRemoteRefColor: #b180d7;
  --vscode-scmGraph-historyItemBaseRefColor: #ea5c00;
  --vscode-scmGraph-historyItemHoverDefaultLabelForeground: #cccccc;
  --vscode-scmGraph-historyItemHoverDefaultLabelBackground: #7f5d38;
  --vscode-scmGraph-historyItemHoverLabelForeground: #ffffff;
  --vscode-scmGraph-historyItemHoverAdditionsForeground: #81b88b;
  --vscode-scmGraph-historyItemHoverDeletionsForeground: #c74e39;
  --vscode-scmGraph-foreground1: #ffb000;
  --vscode-scmGraph-foreground2: #dc267f;
  --vscode-scmGraph-foreground3: #994f00;
  --vscode-scmGraph-foreground4: #40b0a6;
  --vscode-scmGraph-foreground5: #b66dff;
  --vscode-commentsView-resolvedIcon: rgba(204, 204, 204, 0.5);
  --vscode-commentsView-unresolvedIcon: #a57a4c;
  --vscode-editorCommentsWidget-replyInputBackground: #362712;
  --vscode-editorCommentsWidget-resolvedBorder: rgba(204, 204, 204, 0.5);
  --vscode-editorCommentsWidget-unresolvedBorder: #a57a4c;
  --vscode-editorCommentsWidget-rangeBackground: rgba(165, 122, 76, 0.1);
  --vscode-editorCommentsWidget-rangeActiveBackground: rgba(165, 122, 76, 0.1);
  --vscode-editorGutter-commentRangeForeground: #645342;
  --vscode-editorOverviewRuler-commentForeground: #645342;
  --vscode-editorOverviewRuler-commentUnresolvedForeground: #645342;
  --vscode-editorGutter-commentGlyphForeground: #d3af86;
  --vscode-editorGutter-commentUnresolvedGlyphForeground: #d3af86;
  --vscode-ports-iconRunningProcessForeground: #369432;
  --vscode-settings-headerForeground: #e7e7e7;
  --vscode-settings-settingsHeaderHoverForeground: rgba(231, 231, 231, 0.7);
  --vscode-settings-modifiedItemIndicator: #0c7d9d;
  --vscode-settings-headerBorder: rgba(128, 128, 128, 0.35);
  --vscode-settings-sashBorder: rgba(128, 128, 128, 0.35);
  --vscode-settings-dropdownBackground: #51412c;
  --vscode-settings-dropdownForeground: #f0f0f0;
  --vscode-settings-dropdownBorder: #51412c;
  --vscode-settings-dropdownListBorder: #454545;
  --vscode-settings-checkboxBackground: #51412c;
  --vscode-settings-checkboxForeground: #f0f0f0;
  --vscode-settings-checkboxBorder: #51412c;
  --vscode-settings-textInputBackground: #51412c;
  --vscode-settings-textInputForeground: #cccccc;
  --vscode-settings-numberInputBackground: #51412c;
  --vscode-settings-numberInputForeground: #cccccc;
  --vscode-settings-focusedRowBackground: rgba(124, 80, 33, 0.24);
  --vscode-settings-rowHoverBackground: rgba(124, 80, 33, 0.12);
  --vscode-settings-focusedRowBorder: #a57a4c;
  --vscode-keybindingTable-headerBackground: rgba(204, 204, 204, 0.04);
  --vscode-keybindingTable-rowsBackground: rgba(204, 204, 204, 0.04);
  --vscode-debugToolBar-background: #333333;
  --vscode-debugIcon-startForeground: #89d185;
  --vscode-notebook-cellBorderColor: #645342;
  --vscode-notebook-focusedEditorBorder: #a57a4c;
  --vscode-notebookStatusSuccessIcon-foreground: #89d185;
  --vscode-notebookEditorOverviewRuler-runningCellForeground: #89d185;
  --vscode-notebookStatusErrorIcon-foreground: #f48771;
  --vscode-notebookStatusRunningIcon-foreground: #cccccc;
  --vscode-notebook-cellToolbarSeparator: rgba(128, 128, 128, 0.35);
  --vscode-notebook-selectedCellBackground: #645342;
  --vscode-notebook-selectedCellBorder: #645342;
  --vscode-notebook-focusedCellBorder: #a57a4c;
  --vscode-notebook-inactiveFocusedCellBorder: #645342;
  --vscode-notebook-cellStatusBarItemHoverBackground: rgba(255, 255, 255, 0.15);
  --vscode-notebook-cellInsertionIndicator: #a57a4c;
  --vscode-notebookScrollbarSlider-background: rgba(121, 121, 121, 0.4);
  --vscode-notebookScrollbarSlider-hoverBackground: rgba(100, 100, 100, 0.7);
  --vscode-notebookScrollbarSlider-activeBackground: rgba(191, 191, 191, 0.4);
  --vscode-notebook-symbolHighlightBackground: rgba(255, 255, 255, 0.04);
  --vscode-notebook-cellEditorBackground: #362712;
  --vscode-notebook-editorBackground: #221a0f;
  --vscode-debugIcon-breakpointForeground: #e51400;
  --vscode-debugIcon-breakpointDisabledForeground: #848484;
  --vscode-debugIcon-breakpointUnverifiedForeground: #848484;
  --vscode-debugIcon-breakpointCurrentStackframeForeground: #ffcc00;
  --vscode-debugIcon-breakpointStackframeForeground: #89d185;
  --vscode-editor-stackFrameHighlightBackground: rgba(255, 255, 0, 0.2);
  --vscode-editor-focusedStackFrameHighlightBackground: rgba(122, 189, 122, 0.3);
  --vscode-multiDiffEditor-headerBackground: #262626;
  --vscode-multiDiffEditor-background: #221a0f;
  --vscode-interactive-activeCodeBorder: #5e452b;
  --vscode-interactive-inactiveCodeBorder: #645342;
  --vscode-testing-iconFailed: #f14c4c;
  --vscode-testing-iconErrored: #f14c4c;
  --vscode-testing-iconPassed: #73c991;
  --vscode-testing-runAction: #73c991;
  --vscode-testing-iconQueued: #cca700;
  --vscode-testing-iconUnset: #848484;
  --vscode-testing-iconSkipped: #848484;
  --vscode-testing-peekBorder: #f14c4c;
  --vscode-testing-messagePeekBorder: #3794ff;
  --vscode-testing-peekHeaderBackground: rgba(241, 76, 76, 0.1);
  --vscode-testing-messagePeekHeaderBackground: rgba(55, 148, 255, 0.1);
  --vscode-testing-coveredBackground: rgba(156, 204, 44, 0.2);
  --vscode-testing-coveredBorder: rgba(156, 204, 44, 0.15);
  --vscode-testing-coveredGutterBackground: rgba(156, 204, 44, 0.12);
  --vscode-testing-uncoveredBranchBackground: #7a0f09;
  --vscode-testing-uncoveredBackground: rgba(255, 0, 0, 0.2);
  --vscode-testing-uncoveredBorder: rgba(255, 0, 0, 0.15);
  --vscode-testing-uncoveredGutterBackground: rgba(255, 0, 0, 0.3);
  --vscode-testing-coverCountBadgeBackground: #7f5d38;
  --vscode-testing-coverCountBadgeForeground: #ffffff;
  --vscode-testing-message\.error\.badgeBackground: #f14c4c;
  --vscode-testing-message\.error\.badgeBorder: #f14c4c;
  --vscode-testing-message\.error\.badgeForeground: #000000;
  --vscode-testing-message\.info\.decorationForeground: rgba(211, 175, 134, 0.5);
  --vscode-testing-iconErrored\.retired: rgba(241, 76, 76, 0.7);
  --vscode-testing-iconFailed\.retired: rgba(241, 76, 76, 0.7);
  --vscode-testing-iconPassed\.retired: rgba(115, 201, 145, 0.7);
  --vscode-testing-iconQueued\.retired: rgba(204, 167, 0, 0.7);
  --vscode-testing-iconUnset\.retired: rgba(132, 132, 132, 0.7);
  --vscode-testing-iconSkipped\.retired: rgba(132, 132, 132, 0.7);
  --vscode-debugExceptionWidget-border: #a31515;
  --vscode-debugExceptionWidget-background: #420b0d;
  --vscode-editor-inlineValuesForeground: rgba(255, 255, 255, 0.5);
  --vscode-editor-inlineValuesBackground: rgba(255, 200, 0, 0.2);
  --vscode-statusBar-debuggingBackground: #423523;
  --vscode-statusBar-debuggingForeground: #ffffff;
  --vscode-commandCenter-debuggingBackground: rgba(66, 53, 35, 0.26);
  --vscode-debugTokenExpression-name: #c586c0;
  --vscode-debugTokenExpression-type: #4a90e2;
  --vscode-debugTokenExpression-value: rgba(204, 204, 204, 0.6);
  --vscode-debugTokenExpression-string: #ce9178;
  --vscode-debugTokenExpression-boolean: #4e94ce;
  --vscode-debugTokenExpression-number: #b5cea8;
  --vscode-debugTokenExpression-error: #f48771;
  --vscode-debugView-exceptionLabelForeground: #cccccc;
  --vscode-debugView-exceptionLabelBackground: #6c2022;
  --vscode-debugView-stateLabelForeground: #cccccc;
  --vscode-debugView-stateLabelBackground: rgba(136, 136, 136, 0.27);
  --vscode-debugView-valueChangedHighlight: #569cd6;
  --vscode-debugConsole-infoForeground: #3794ff;
  --vscode-debugConsole-warningForeground: #cca700;
  --vscode-debugConsole-errorForeground: #f48771;
  --vscode-debugConsole-sourceForeground: #cccccc;
  --vscode-debugConsoleInputIcon-foreground: #cccccc;
  --vscode-debugIcon-pauseForeground: #75beff;
  --vscode-debugIcon-stopForeground: #f48771;
  --vscode-debugIcon-disconnectForeground: #f48771;
  --vscode-debugIcon-restartForeground: #89d185;
  --vscode-debugIcon-stepOverForeground: #75beff;
  --vscode-debugIcon-stepIntoForeground: #75beff;
  --vscode-debugIcon-stepOutForeground: #75beff;
  --vscode-debugIcon-continueForeground: #75beff;
  --vscode-debugIcon-stepBackForeground: #75beff;
  --vscode-mergeEditor-change\.background: rgba(155, 185, 85, 0.2);
  --vscode-mergeEditor-change\.word\.background: rgba(156, 204, 44, 0.2);
  --vscode-mergeEditor-changeBase\.background: #4b1818;
  --vscode-mergeEditor-changeBase\.word\.background: #6f1313;
  --vscode-mergeEditor-conflict\.unhandledUnfocused\.border: rgba(255, 166, 0, 0.48);
  --vscode-mergeEditor-conflict\.unhandledFocused\.border: #ffa600;
  --vscode-mergeEditor-conflict\.handledUnfocused\.border: rgba(134, 134, 134, 0.29);
  --vscode-mergeEditor-conflict\.handledFocused\.border: rgba(193, 193, 193, 0.8);
  --vscode-mergeEditor-conflict\.handled\.minimapOverViewRuler: rgba(173, 172, 168, 0.93);
  --vscode-mergeEditor-conflict\.unhandled\.minimapOverViewRuler: #fcba03;
  --vscode-mergeEditor-conflictingLines\.background: rgba(255, 234, 0, 0.28);
  --vscode-mergeEditor-conflict\.input1.background: rgba(64, 200, 174, 0.2);
  --vscode-mergeEditor-conflict\.input2.background: rgba(64, 166, 255, 0.2);
  --vscode-extensionIcon-starForeground: #ff8e00;
  --vscode-extensionIcon-verifiedForeground: #3794ff;
  --vscode-extensionIcon-preReleaseForeground: #1d9271;
  --vscode-extensionIcon-sponsorForeground: #d758b3;
  --vscode-terminal-ansiBlack: #000000;
  --vscode-terminal-ansiRed: #cd3131;
  --vscode-terminal-ansiGreen: #0dbc79;
  --vscode-terminal-ansiYellow: #e5e510;
  --vscode-terminal-ansiBlue: #2472c8;
  --vscode-terminal-ansiMagenta: #bc3fbc;
  --vscode-terminal-ansiCyan: #11a8cd;
  --vscode-terminal-ansiWhite: #e5e5e5;
  --vscode-terminal-ansiBrightBlack: #666666;
  --vscode-terminal-ansiBrightRed: #f14c4c;
  --vscode-terminal-ansiBrightGreen: #23d18b;
  --vscode-terminal-ansiBrightYellow: #f5f543;
  --vscode-terminal-ansiBrightBlue: #3b8eea;
  --vscode-terminal-ansiBrightMagenta: #d670d6;
  --vscode-terminal-ansiBrightCyan: #29b8db;
  --vscode-terminal-ansiBrightWhite: #e5e5e5;
  --vscode-simpleFindWidget-sashBorder: #454545;
  --vscode-terminalStickyScrollHover-background: #2a2d2e;
  --vscode-terminalCommandGuide-foreground: #645342;
  --vscode-welcomePage-tileBackground: #131510;
  --vscode-welcomePage-tileHoverBackground: #171913;
  --vscode-welcomePage-tileBorder: rgba(255, 255, 255, 0.1);
  --vscode-welcomePage-progress\.background: #51412c;
  --vscode-welcomePage-progress\.foreground: #3794ff;
  --vscode-walkthrough-stepTitle\.foreground: #ffffff;
  --vscode-walkThrough-embeddedEditorBackground: rgba(0, 0, 0, 0.4);
  --vscode-profiles-sashBorder: rgba(128, 128, 128, 0.35);
  --vscode-gitDecoration-addedResourceForeground: #81b88b;
  --vscode-gitDecoration-modifiedResourceForeground: #e2c08d;
  --vscode-gitDecoration-deletedResourceForeground: #c74e39;
  --vscode-gitDecoration-renamedResourceForeground: #73c991;
  --vscode-gitDecoration-untrackedResourceForeground: #73c991;
  --vscode-gitDecoration-ignoredResourceForeground: #8c8c8c;
  --vscode-gitDecoration-stageModifiedResourceForeground: #e2c08d;
  --vscode-gitDecoration-stageDeletedResourceForeground: #c74e39;
  --vscode-gitDecoration-conflictingResourceForeground: #e4676b;
  --vscode-gitDecoration-submoduleResourceForeground: #8db9e2;
  --vscode-git-blame\.editorDecorationForeground: #999999;
}
