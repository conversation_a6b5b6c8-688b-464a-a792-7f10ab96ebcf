:root {
  --intellij-actionButton-focusedBorderColor: rgba(53, 116, 240, 1);
  --intellij-actionButton-hoverBackground: rgba(255, 255, 255, 0.086);
  --intellij-actionButton-hoverBorderColor: rgba(255, 255, 255, 0);
  --intellij-actionButton-hoverSeparatorColor: rgba(90, 93, 99, 1);
  --intellij-actionButton-pressedBackground: rgba(255, 255, 255, 0.15);
  --intellij-actionButton-pressedBorderColor: rgba(255, 255, 255, 0);
  --intellij-activeCaption: rgba(67, 78, 96, 1);
  --intellij-activeCaptionBorder: rgba(255, 255, 255, 1);
  --intellij-activeCaptionText: rgba(0, 0, 0, 1);
  --intellij-appInspector-graphNode-background: rgba(43, 45, 48, 1);
  --intellij-bigSpinner-background: rgba(43, 45, 48, 1);
  --intellij-bookmark-iconBackground: rgba(170, 133, 66, 1);
  --intellij-bookmark-mnemonic-iconBackground: rgba(91, 83, 65, 1);
  --intellij-bookmark-mnemonic-iconBorderColor: rgba(217, 163, 67, 1);
  --intellij-bookmark-mnemonic-iconForeground: rgba(255, 255, 255, 1);
  --intellij-bookmark-mnemonicAssigned-background: rgba(224, 136, 85, 1);
  --intellij-bookmark-mnemonicAssigned-foreground: rgba(30, 31, 34, 1);
  --intellij-bookmark-mnemonicAvailable-borderColor: rgba(78, 81, 87, 1);
  --intellij-bookmark-mnemonicCurrent-background: rgba(70, 127, 242, 1);
  --intellij-bookmarkMnemonicAssigned-background: rgba(43, 45, 48, 1);
  --intellij-bookmarkMnemonicAssigned-borderColor: rgba(30, 31, 34, 1);
  --intellij-bookmarkMnemonicAssigned-foreground: rgba(223, 225, 229, 1);
  --intellij-bookmarkMnemonicAvailable-background: rgba(43, 45, 48, 1);
  --intellij-bookmarkMnemonicAvailable-borderColor: rgba(30, 31, 34, 1);
  --intellij-bookmarkMnemonicAvailable-foreground: rgba(223, 225, 229, 1);
  --intellij-bookmarkMnemonicCurrent-background: rgba(43, 45, 48, 1);
  --intellij-bookmarkMnemonicCurrent-borderColor: rgba(30, 31, 34, 1);
  --intellij-bookmarkMnemonicCurrent-foreground: rgba(223, 225, 229, 1);
  --intellij-borders-color: rgba(30, 31, 34, 1);
  --intellij-borders-contrastBorderColor: rgba(30, 31, 34, 1);
  --intellij-button-background: rgba(43, 45, 48, 1);
  --intellij-button-darkShadow: rgba(0, 0, 0, 1);
  --intellij-button-default-endBackground: rgba(53, 116, 240, 1);
  --intellij-button-default-endBorderColor: rgba(53, 116, 240, 1);
  --intellij-button-default-focusColor: rgba(53, 116, 240, 1);
  --intellij-button-default-focusedBorderColor: rgba(30, 31, 34, 1);
  --intellij-button-default-foreground: rgba(255, 255, 255, 1);
  --intellij-button-default-shadowColor: rgba(0, 0, 0, 0);
  --intellij-button-default-startBackground: rgba(53, 116, 240, 1);
  --intellij-button-default-startBorderColor: rgba(53, 116, 240, 1);
  --intellij-button-disabledBorderColor: rgba(67, 69, 74, 1);
  --intellij-button-disabledText: rgba(90, 93, 99, 1);
  --intellij-button-endBackground: rgba(43, 45, 48, 1);
  --intellij-button-endBorderColor: rgba(78, 81, 87, 1);
  --intellij-button-focusedBorderColor: rgba(43, 45, 48, 1);
  --intellij-button-font-family: "Inter", system-ui, sans-serif;
  --intellij-button-font-size: 13px;
  --intellij-button-foreground: rgba(223, 225, 229, 1);
  --intellij-button-highlight: rgba(255, 255, 255, 1);
  --intellij-button-light: rgba(6, 64, 198, 1);
  --intellij-button-select: rgba(255, 102, 102, 1);
  --intellij-button-shadow: rgba(0, 0, 0, 0.27);
  --intellij-button-shadowColor: rgba(0, 0, 0, 0);
  --intellij-button-split-default-iconColor: rgba(255, 255, 255, 1);
  --intellij-button-split-default-separatorColor: rgba(131, 172, 252, 1);
  --intellij-button-startBackground: rgba(43, 45, 48, 1);
  --intellij-button-startBorderColor: rgba(78, 81, 87, 1);
  --intellij-canvas-tooltip-background: rgba(43, 45, 48, 1);
  --intellij-checkBox-background: rgba(43, 45, 48, 1);
  --intellij-checkBox-disabledText: rgba(90, 93, 99, 1);
  --intellij-checkBox-font-family: "Inter", system-ui, sans-serif;
  --intellij-checkBox-font-size: 13px;
  --intellij-checkBox-foreground: rgba(223, 225, 229, 1);
  --intellij-checkBox-select: rgba(255, 102, 102, 1);
  --intellij-checkBoxMenuItem-acceleratorFont-family: "Lucida Grande", system-ui, sans-serif;
  --intellij-checkBoxMenuItem-acceleratorFont-size: 14px;
  --intellij-checkBoxMenuItem-acceleratorForeground: rgba(111, 115, 122, 1);
  --intellij-checkBoxMenuItem-acceleratorSelectionForeground: rgba(0, 0, 0, 1);
  --intellij-checkBoxMenuItem-background: rgba(43, 45, 48, 1);
  --intellij-checkBoxMenuItem-disabledBackground: rgba(43, 45, 48, 1);
  --intellij-checkBoxMenuItem-disabledForeground: rgba(90, 93, 99, 1);
  --intellij-checkBoxMenuItem-font-family: "Inter", system-ui, sans-serif;
  --intellij-checkBoxMenuItem-font-size: 13px;
  --intellij-checkBoxMenuItem-foreground: rgba(223, 225, 229, 1);
  --intellij-checkBoxMenuItem-selectionBackground: rgba(46, 67, 110, 1);
  --intellij-checkBoxMenuItem-selectionForeground: rgba(223, 225, 229, 1);
  --intellij-code-block-borderColor: rgba(57, 59, 64, 1);
  --intellij-code-block-editorPane-backgroundColor: rgba(78, 81, 87, 1);
  --intellij-code-block-editorPane-borderColor: rgba(57, 59, 64, 1);
  --intellij-code-inline-backgroundColor: rgba(180, 184, 191, 1);
  --intellij-codeWithMe-avatar-foreground: rgba(223, 225, 229, 1);
  --intellij-codeWithMe-buttons-badgeBackground: rgba(78, 81, 87, 1);
  --intellij-codeWithMe-buttons-linkCopied-background: rgba(95, 173, 101, 1);
  --intellij-codeWithMe-buttons-linkCopied-foreground: rgba(55, 82, 57, 1);
  --intellij-codeWithMe-buttons-redButton-background: rgba(43, 45, 48, 1);
  --intellij-codeWithMe-buttons-redButton-foreground: rgba(219, 92, 92, 1);
  --intellij-codeWithMe-buttons-redButton-hovered-background: rgba(219, 92, 92, 1);
  --intellij-codeWithMe-buttons-redButton-hovered-foreground: rgba(255, 255, 255, 1);
  --intellij-codeWithMe-endSessionPopup-background: rgba(57, 59, 64, 1);
  --intellij-codeWithMe-endSessionPopup-endButton-background: rgba(90, 93, 99, 1);
  --intellij-codeWithMe-endSessionPopup-endButton-foreground: rgba(255, 255, 255, 1);
  --intellij-codeWithMe-endSessionPopup-foreground: rgba(255, 255, 255, 1);
  --intellij-codeWithMe-endSessionPopup-info-foreground: rgba(180, 184, 191, 1);
  --intellij-codeWithMe-endSessionPopup-link: rgba(107, 155, 250, 1);
  --intellij-codeWithMe-endSessionPopup-timer-foreground: rgba(255, 255, 255, 1);
  --intellij-codeWithMe-mainToolbar-noConnectionLabelForeground: rgba(134, 138, 145, 1);
  --intellij-codeWithMe-users-1-background: rgba(95, 173, 101, 1);
  --intellij-codeWithMe-users-1-followingBorderTextForeground: rgba(255, 255, 255, 1);
  --intellij-codeWithMe-users-1-foreground: rgba(255, 255, 255, 1);
  --intellij-codeWithMe-users-1-stopFollowingLinkForeground: rgba(37, 50, 77, 1);
  --intellij-codeWithMe-users-2-background: rgba(219, 92, 92, 1);
  --intellij-codeWithMe-users-2-followingBorderTextForeground: rgba(255, 255, 255, 1);
  --intellij-codeWithMe-users-2-foreground: rgba(255, 255, 255, 1);
  --intellij-codeWithMe-users-2-stopFollowingLinkForeground: rgba(37, 50, 77, 1);
  --intellij-codeWithMe-users-3-background: rgba(149, 90, 224, 1);
  --intellij-codeWithMe-users-3-followingBorderTextForeground: rgba(255, 255, 255, 1);
  --intellij-codeWithMe-users-3-foreground: rgba(255, 255, 255, 1);
  --intellij-codeWithMe-users-3-stopFollowingLinkForeground: rgba(37, 50, 77, 1);
  --intellij-codeWithMe-users-4-background: rgba(224, 136, 85, 1);
  --intellij-codeWithMe-users-4-followingBorderTextForeground: rgba(255, 255, 255, 1);
  --intellij-codeWithMe-users-4-foreground: rgba(255, 255, 255, 1);
  --intellij-codeWithMe-users-4-stopFollowingLinkForeground: rgba(37, 50, 77, 1);
  --intellij-codeWithMe-users-5-background: rgba(36, 163, 148, 1);
  --intellij-codeWithMe-users-5-followingBorderTextForeground: rgba(255, 255, 255, 1);
  --intellij-codeWithMe-users-5-foreground: rgba(255, 255, 255, 1);
  --intellij-codeWithMe-users-5-stopFollowingLinkForeground: rgba(37, 50, 77, 1);
  --intellij-codeWithMe-users-6-background: rgba(214, 174, 88, 1);
  --intellij-codeWithMe-users-6-followingBorderTextForeground: rgba(30, 31, 34, 1);
  --intellij-codeWithMe-users-6-foreground: rgba(255, 255, 255, 1);
  --intellij-codeWithMe-users-6-stopFollowingLinkForeground: rgba(37, 50, 77, 1);
  --intellij-colorChooser-background: rgba(43, 45, 48, 1);
  --intellij-colorChooser-font-family: "Inter", system-ui, sans-serif;
  --intellij-colorChooser-font-size: 13px;
  --intellij-colorChooser-foreground: rgba(223, 225, 229, 1);
  --intellij-colorChooser-swatchesDefaultRecentColor: rgba(255, 255, 255, 0.25);
  --intellij-colorPalette-blue1: rgba(37, 50, 77, 1);
  --intellij-colorPalette-blue10: rgba(131, 172, 252, 1);
  --intellij-colorPalette-blue11: rgba(153, 187, 255, 1);
  --intellij-colorPalette-blue2: rgba(46, 67, 110, 1);
  --intellij-colorPalette-blue3: rgba(53, 83, 143, 1);
  --intellij-colorPalette-blue4: rgba(55, 95, 173, 1);
  --intellij-colorPalette-blue5: rgba(54, 106, 206, 1);
  --intellij-colorPalette-blue6: rgba(53, 116, 240, 1);
  --intellij-colorPalette-blue7: rgba(70, 127, 242, 1);
  --intellij-colorPalette-blue8: rgba(84, 138, 247, 1);
  --intellij-colorPalette-blue9: rgba(107, 155, 250, 1);
  --intellij-colorPalette-gray1: rgba(30, 31, 34, 1);
  --intellij-colorPalette-gray10: rgba(180, 184, 191, 1);
  --intellij-colorPalette-gray11: rgba(206, 208, 214, 1);
  --intellij-colorPalette-gray12: rgba(223, 225, 229, 1);
  --intellij-colorPalette-gray13: rgba(240, 241, 242, 1);
  --intellij-colorPalette-gray14: rgba(255, 255, 255, 1);
  --intellij-colorPalette-gray2: rgba(43, 45, 48, 1);
  --intellij-colorPalette-gray3: rgba(57, 59, 64, 1);
  --intellij-colorPalette-gray4: rgba(67, 69, 74, 1);
  --intellij-colorPalette-gray5: rgba(78, 81, 87, 1);
  --intellij-colorPalette-gray6: rgba(90, 93, 99, 1);
  --intellij-colorPalette-gray7: rgba(111, 115, 122, 1);
  --intellij-colorPalette-gray8: rgba(134, 138, 145, 1);
  --intellij-colorPalette-gray9: rgba(157, 160, 168, 1);
  --intellij-colorPalette-green1: rgba(37, 54, 39, 1);
  --intellij-colorPalette-green10: rgba(160, 219, 165, 1);
  --intellij-colorPalette-green11: rgba(185, 235, 189, 1);
  --intellij-colorPalette-green12: rgba(212, 250, 215, 1);
  --intellij-colorPalette-green2: rgba(39, 56, 40, 1);
  --intellij-colorPalette-green3: rgba(55, 82, 57, 1);
  --intellij-colorPalette-green4: rgba(67, 105, 70, 1);
  --intellij-colorPalette-green5: rgba(78, 128, 82, 1);
  --intellij-colorPalette-green6: rgba(87, 150, 92, 1);
  --intellij-colorPalette-green7: rgba(95, 173, 101, 1);
  --intellij-colorPalette-green8: rgba(115, 189, 121, 1);
  --intellij-colorPalette-green9: rgba(137, 204, 142, 1);
  --intellij-colorPalette-orange1: rgba(69, 50, 43, 1);
  --intellij-colorPalette-orange10: rgba(250, 206, 175, 1);
  --intellij-colorPalette-orange11: rgba(255, 223, 199, 1);
  --intellij-colorPalette-orange2: rgba(97, 68, 56, 1);
  --intellij-colorPalette-orange3: rgba(130, 88, 69, 1);
  --intellij-colorPalette-orange4: rgba(163, 107, 78, 1);
  --intellij-colorPalette-orange5: rgba(194, 122, 83, 1);
  --intellij-colorPalette-orange6: rgba(224, 136, 85, 1);
  --intellij-colorPalette-orange7: rgba(229, 152, 108, 1);
  --intellij-colorPalette-orange8: rgba(240, 172, 129, 1);
  --intellij-colorPalette-orange9: rgba(245, 189, 152, 1);
  --intellij-colorPalette-purple1: rgba(47, 41, 54, 1);
  --intellij-colorPalette-purple10: rgba(196, 160, 243, 1);
  --intellij-colorPalette-purple11: rgba(212, 184, 249, 1);
  --intellij-colorPalette-purple12: rgba(228, 206, 255, 1);
  --intellij-colorPalette-purple2: rgba(59, 49, 71, 1);
  --intellij-colorPalette-purple3: rgba(67, 51, 88, 1);
  --intellij-colorPalette-purple4: rgba(88, 61, 122, 1);
  --intellij-colorPalette-purple5: rgba(108, 70, 156, 1);
  --intellij-colorPalette-purple6: rgba(129, 80, 190, 1);
  --intellij-colorPalette-purple7: rgba(149, 90, 224, 1);
  --intellij-colorPalette-purple8: rgba(165, 113, 230, 1);
  --intellij-colorPalette-purple9: rgba(181, 137, 236, 1);
  --intellij-colorPalette-red1: rgba(64, 41, 41, 1);
  --intellij-colorPalette-red10: rgba(242, 177, 170, 1);
  --intellij-colorPalette-red11: rgba(247, 204, 198, 1);
  --intellij-colorPalette-red12: rgba(250, 227, 222, 1);
  --intellij-colorPalette-red2: rgba(71, 43, 43, 1);
  --intellij-colorPalette-red3: rgba(94, 56, 56, 1);
  --intellij-colorPalette-red4: rgba(122, 67, 67, 1);
  --intellij-colorPalette-red5: rgba(156, 78, 78, 1);
  --intellij-colorPalette-red6: rgba(189, 87, 87, 1);
  --intellij-colorPalette-red7: rgba(219, 92, 92, 1);
  --intellij-colorPalette-red8: rgba(227, 119, 116, 1);
  --intellij-colorPalette-red9: rgba(235, 147, 141, 1);
  --intellij-colorPalette-teal1: rgba(29, 56, 56, 1);
  --intellij-colorPalette-teal10: rgba(125, 206, 197, 1);
  --intellij-colorPalette-teal11: rgba(155, 221, 214, 1);
  --intellij-colorPalette-teal12: rgba(185, 235, 230, 1);
  --intellij-colorPalette-teal2: rgba(29, 61, 59, 1);
  --intellij-colorPalette-teal3: rgba(30, 77, 74, 1);
  --intellij-colorPalette-teal4: rgba(32, 99, 93, 1);
  --intellij-colorPalette-teal5: rgba(33, 120, 111, 1);
  --intellij-colorPalette-teal6: rgba(35, 142, 130, 1);
  --intellij-colorPalette-teal7: rgba(36, 163, 148, 1);
  --intellij-colorPalette-teal8: rgba(66, 177, 164, 1);
  --intellij-colorPalette-teal9: rgba(96, 192, 181, 1);
  --intellij-colorPalette-yellow1: rgba(61, 50, 35, 1);
  --intellij-colorPalette-yellow10: rgba(252, 235, 164, 1);
  --intellij-colorPalette-yellow11: rgba(255, 246, 189, 1);
  --intellij-colorPalette-yellow2: rgba(94, 77, 51, 1);
  --intellij-colorPalette-yellow3: rgba(130, 106, 65, 1);
  --intellij-colorPalette-yellow4: rgba(158, 129, 74, 1);
  --intellij-colorPalette-yellow5: rgba(186, 151, 82, 1);
  --intellij-colorPalette-yellow6: rgba(214, 174, 88, 1);
  --intellij-colorPalette-yellow7: rgba(242, 197, 92, 1);
  --intellij-colorPalette-yellow8: rgba(245, 210, 115, 1);
  --intellij-colorPalette-yellow9: rgba(247, 222, 139, 1);
  --intellij-combinedDiff-blockBorder-selectedActiveColor: rgba(55, 95, 173, 1);
  --intellij-combinedDiff-blockBorder-selectedInactiveColor: rgba(90, 93, 99, 1);
  --intellij-comboBox-arrowButton-background: rgba(43, 45, 48, 1);
  --intellij-comboBox-arrowButton-disabledIconColor: rgba(88, 88, 88, 1);
  --intellij-comboBox-arrowButton-iconColor: rgba(154, 157, 161, 1);
  --intellij-comboBox-arrowButton-nonEditableBackground: rgba(57, 59, 64, 1);
  --intellij-comboBox-background: rgba(43, 45, 48, 1);
  --intellij-comboBox-buttonBackground: rgba(255, 255, 255, 0.25);
  --intellij-comboBox-buttonDarkShadow: rgba(0, 0, 0, 1);
  --intellij-comboBox-buttonHighlight: rgba(255, 255, 255, 1);
  --intellij-comboBox-buttonShadow: rgba(0, 0, 0, 0.27);
  --intellij-comboBox-disabledBackground: rgba(43, 45, 48, 1);
  --intellij-comboBox-disabledForeground: rgba(90, 93, 99, 1);
  --intellij-comboBox-font-family: "Inter", system-ui, sans-serif;
  --intellij-comboBox-font-size: 13px;
  --intellij-comboBox-foreground: rgba(223, 225, 229, 1);
  --intellij-comboBox-nonEditableBackground: rgba(57, 59, 64, 1);
  --intellij-comboBox-selectionBackground: rgba(46, 67, 110, 1);
  --intellij-comboBox-selectionForeground: rgba(223, 225, 229, 1);
  --intellij-comboBoxButton-background: rgba(43, 45, 48, 1);
  --intellij-completionPopup-advertiser-background: rgba(57, 59, 64, 1);
  --intellij-completionPopup-advertiser-foreground: rgba(111, 115, 122, 1);
  --intellij-completionPopup-foreground: rgba(180, 184, 191, 1);
  --intellij-completionPopup-matchForeground: rgba(84, 138, 247, 1);
  --intellij-completionPopup-selectionBackground: rgba(46, 67, 110, 1);
  --intellij-completionPopup-selectionInactiveBackground: rgba(67, 69, 74, 1);
  --intellij-complexPopup-header-background: rgba(57, 59, 64, 1);
  --intellij-component-borderColor: rgba(78, 81, 87, 1);
  --intellij-component-disabledBorderColor: rgba(67, 69, 74, 1);
  --intellij-component-errorFocusColor: rgba(156, 78, 78, 1);
  --intellij-component-focusColor: rgba(53, 116, 240, 1);
  --intellij-component-focusedBorderColor: rgba(43, 45, 48, 1);
  --intellij-component-inactiveErrorFocusColor: rgba(94, 56, 56, 1);
  --intellij-component-inactiveWarningFocusColor: rgba(94, 77, 51, 1);
  --intellij-component-infoForeground: rgba(111, 115, 122, 1);
  --intellij-component-warningFocusColor: rgba(158, 129, 74, 1);
  --intellij-content-background: rgba(43, 45, 48, 1);
  --intellij-control: rgba(60, 63, 65, 1);
  --intellij-controlDkShadow: rgba(0, 0, 0, 1);
  --intellij-controlHighlight: rgba(6, 64, 198, 1);
  --intellij-controlLtHighlight: rgba(255, 255, 255, 1);
  --intellij-controlShadow: rgba(0, 0, 0, 0.27);
  --intellij-controlText: rgba(187, 187, 187, 1);
  --intellij-counter-background: rgba(134, 138, 145, 1);
  --intellij-counter-foreground: rgba(0, 0, 0, 1);
  --intellij-dataSummary-chart-barColor: rgba(53, 116, 240, 1);
  --intellij-debugger-evaluateExpression-background: rgba(57, 59, 64, 1);
  --intellij-debugger-variables-changedValueForeground: rgba(84, 138, 247, 1);
  --intellij-debugger-variables-collectingDataForeground: rgba(111, 115, 122, 1);
  --intellij-debugger-variables-errorMessageForeground: rgba(219, 92, 92, 1);
  --intellij-debugger-variables-evaluatingExpressionForeground: rgba(111, 115, 122, 1);
  --intellij-debugger-variables-exceptionForeground: rgba(219, 92, 92, 1);
  --intellij-debugger-variables-modifyingValueForeground: rgba(84, 138, 247, 1);
  --intellij-debugger-variables-typeForeground: rgba(111, 115, 122, 1);
  --intellij-debugger-variables-valueForeground: rgba(240, 172, 129, 1);
  --intellij-defaultTabs-background: rgba(43, 45, 48, 1);
  --intellij-desktop: rgba(34, 255, 6, 1);
  --intellij-desktop-background: rgba(43, 45, 48, 1);
  --intellij-desktopIcon-borderColor: rgba(30, 31, 34, 1);
  --intellij-desktopIcon-borderRimColor: rgba(192, 192, 192, 0.75);
  --intellij-desktopIcon-labelBackground: rgba(0, 0, 0, 0.39);
  --intellij-disclosureButton-defaultBackground: rgba(255, 255, 255, 0.071);
  --intellij-disclosureButton-hoverOverlay: rgba(255, 255, 255, 0.051);
  --intellij-disclosureButton-pressedOverlay: rgba(255, 255, 255, 0.1);
  --intellij-disclosureButton-successBackground: rgba(55, 82, 57, 1);
  --intellij-dragAndDrop-areaBackground: rgba(255, 255, 255, 0.2);
  --intellij-dragAndDrop-areaBorderColor: rgba(79, 115, 168, 1);
  --intellij-dragAndDrop-areaForeground: rgba(186, 186, 186, 1);
  --intellij-dragAndDrop-borderColor: rgba(53, 116, 240, 1);
  --intellij-dragAndDrop-rowBackground: rgba(53, 115, 240, 0.15);
  --intellij-editor-background: rgba(43, 45, 48, 1);
  --intellij-editor-foreground: rgba(223, 225, 229, 1);
  --intellij-editor-searchField-background: rgba(30, 31, 34, 1);
  --intellij-editor-toolTip-background: rgba(57, 59, 64, 1);
  --intellij-editor-toolTip-border: rgba(67, 69, 74, 1);
  --intellij-editor-toolTip-errorBackground: rgba(64, 41, 41, 1);
  --intellij-editor-toolTip-errorBorder: rgba(71, 43, 43, 1);
  --intellij-editor-toolTip-foreground: rgba(223, 225, 229, 1);
  --intellij-editor-toolTip-iconHoverBackground: rgba(255, 255, 255, 0.1);
  --intellij-editor-toolTip-selectionBackground: rgba(43, 45, 48, 1);
  --intellij-editor-toolTip-successBackground: rgba(37, 54, 39, 1);
  --intellij-editor-toolTip-successBorder: rgba(39, 56, 40, 1);
  --intellij-editor-toolTip-warningBackground: rgba(61, 50, 35, 1);
  --intellij-editor-toolTip-warningBorder: rgba(94, 77, 51, 1);
  --intellij-editor-toolbar-borderColor: rgba(57, 59, 64, 1);
  --intellij-editorPane-background: rgba(43, 45, 48, 1);
  --intellij-editorPane-caretForeground: rgba(187, 187, 187, 1);
  --intellij-editorPane-font-family: "Inter", monospace;
  --intellij-editorPane-font-size: 13px;
  --intellij-editorPane-foreground: rgba(223, 225, 229, 1);
  --intellij-editorPane-inactiveBackground: rgba(69, 73, 74, 1);
  --intellij-editorPane-inactiveForeground: rgba(90, 93, 99, 1);
  --intellij-editorPane-selectionBackground: rgba(46, 67, 110, 1);
  --intellij-editorPane-selectionForeground: rgba(223, 225, 229, 1);
  --intellij-editorPane-splitBorder: rgba(57, 59, 64, 1);
  --intellij-editorTabs-background: rgba(30, 31, 34, 1);
  --intellij-editorTabs-hoverBackground: rgba(30, 31, 34, 0);
  --intellij-editorTabs-hoverInactiveBackground: rgba(30, 31, 34, 0);
  --intellij-editorTabs-inactiveColoredFileBackground: rgba(30, 31, 34, 0.5);
  --intellij-editorTabs-underTabsBorderColor: rgba(57, 59, 64, 1);
  --intellij-editorTabs-underlinedTabBackground: rgba(30, 31, 34, 1);
  --intellij-fileColor-blue: rgba(29, 61, 59, 1);
  --intellij-fileColor-gray: rgba(53, 54, 59, 1);
  --intellij-fileColor-green: rgba(39, 56, 40, 1);
  --intellij-fileColor-orange: rgba(69, 50, 43, 1);
  --intellij-fileColor-rose: rgba(71, 43, 43, 1);
  --intellij-fileColor-violet: rgba(59, 49, 71, 1);
  --intellij-fileColor-yellow: rgba(61, 50, 35, 1);
  --intellij-flameGraph-tooltip-foreground: rgba(223, 225, 229, 1);
  --intellij-flameGraph-tooltip-scaleBackground: rgba(67, 69, 74, 1);
  --intellij-flameGraph-tooltip-scaleColor: rgba(55, 95, 173, 1);
  --intellij-focus-color: rgba(255, 0, 0, 1);
  --intellij-formattedTextField-background: rgba(43, 45, 48, 1);
  --intellij-formattedTextField-caretForeground: rgba(187, 187, 187, 1);
  --intellij-formattedTextField-font-family: "Inter", system-ui, sans-serif;
  --intellij-formattedTextField-font-size: 13px;
  --intellij-formattedTextField-foreground: rgba(223, 225, 229, 1);
  --intellij-formattedTextField-inactiveBackground: rgba(60, 63, 65, 1);
  --intellij-formattedTextField-inactiveForeground: rgba(90, 93, 99, 1);
  --intellij-formattedTextField-selectionBackground: rgba(46, 67, 110, 1);
  --intellij-formattedTextField-selectionForeground: rgba(223, 225, 229, 1);
  --intellij-gotItTooltip-animationBackground: rgba(30, 31, 34, 1);
  --intellij-gotItTooltip-background: rgba(55, 95, 173, 1);
  --intellij-gotItTooltip-borderColor: rgba(55, 95, 173, 1);
  --intellij-gotItTooltip-button-contrastBackground: rgba(46, 67, 110, 1);
  --intellij-gotItTooltip-button-endBackground: rgba(55, 95, 173, 1);
  --intellij-gotItTooltip-button-endBorderColor: rgba(255, 255, 255, 0.5);
  --intellij-gotItTooltip-button-foreground: rgba(255, 255, 255, 1);
  --intellij-gotItTooltip-button-startBackground: rgba(55, 95, 173, 1);
  --intellij-gotItTooltip-button-startBorderColor: rgba(255, 255, 255, 0.5);
  --intellij-gotItTooltip-codeBackground: rgba(55, 95, 173, 1);
  --intellij-gotItTooltip-codeBorderColor: rgba(255, 255, 255, 0.5);
  --intellij-gotItTooltip-codeForeground: rgba(255, 255, 255, 1);
  --intellij-gotItTooltip-foreground: rgba(255, 255, 255, 0.8);
  --intellij-gotItTooltip-header-foreground: rgba(255, 255, 255, 1);
  --intellij-gotItTooltip-iconBorderColor: rgba(255, 255, 255, 1);
  --intellij-gotItTooltip-iconFillColor: rgba(54, 106, 206, 1);
  --intellij-gotItTooltip-imageBorderColor: rgba(57, 59, 64, 1);
  --intellij-gotItTooltip-linkForeground: rgba(255, 255, 255, 0.8);
  --intellij-gotItTooltip-linkUnderlineDefaultColor: rgba(255, 255, 255, 0.4);
  --intellij-gotItTooltip-linkUnderlineHoveredColor: rgba(255, 255, 255, 0.4);
  --intellij-gotItTooltip-secondaryActionForeground: rgba(153, 187, 255, 1);
  --intellij-gotItTooltip-shortcutBackground: rgba(46, 67, 110, 1);
  --intellij-gotItTooltip-shortcutForeground: rgba(255, 255, 255, 1);
  --intellij-gotItTooltip-stepForeground: rgba(153, 187, 255, 1);
  --intellij-group-separatorColor: rgba(57, 59, 64, 1);
  --intellij-helpBrowser-aiEditor-background: rgba(43, 45, 48, 1);
  --intellij-helpBrowser-helpBrowserMessage-snippet-border: rgba(57, 59, 64, 1);
  --intellij-helpBrowser-helpBrowserMessage-snippet-moreLines-background: rgba(43, 45, 48, 1);
  --intellij-helpBrowser-helpBrowserMessage-snippet-moreLines-foreground: rgba(134, 138, 145, 1);
  --intellij-helpBrowser-helpBrowserMessage-snippet-moreLines-hoverBackground: rgba(57, 59, 64, 1);
  --intellij-helpBrowser-helpBrowserMessage-snippet-moreLines-hoverForeground: rgba(
    134,
    138,
    145,
    1
  );
  --intellij-helpBrowser-titleHighlightForeground: rgba(53, 116, 240, 1);
  --intellij-helpBrowser-userMessage-background: rgba(67, 69, 74, 1);
  --intellij-helpBrowser-userMessage-snippet-border: rgba(78, 81, 87, 1);
  --intellij-helpBrowser-userMessage-snippet-moreLines-background: rgba(43, 45, 48, 1);
  --intellij-helpBrowser-userMessage-snippet-moreLines-foreground: rgba(134, 138, 145, 1);
  --intellij-helpBrowser-userMessage-snippet-moreLines-hoverBackground: rgba(57, 59, 64, 1);
  --intellij-helpBrowser-userMessage-snippet-moreLines-hoverForeground: rgba(134, 138, 145, 1);
  --intellij-hyperlink-linkColor: rgba(88, 157, 246, 1);
  --intellij-iconButton-font-family: ".AppleSystemUIFont", system-ui, sans-serif;
  --intellij-iconButton-font-size: 11px;
  --intellij-ide-shadow-bottom0Color: rgba(0, 0, 0, 0);
  --intellij-ide-shadow-bottom1Color: rgba(0, 0, 0, 0.13);
  --intellij-ide-shadow-bottomLeft0Color: rgba(0, 0, 0, 0);
  --intellij-ide-shadow-bottomLeft1Color: rgba(0, 0, 0, 0.13);
  --intellij-ide-shadow-bottomRight0Color: rgba(0, 0, 0, 0);
  --intellij-ide-shadow-bottomRight1Color: rgba(0, 0, 0, 0.13);
  --intellij-ide-shadow-left0Color: rgba(0, 0, 0, 0);
  --intellij-ide-shadow-left1Color: rgba(0, 0, 0, 0.13);
  --intellij-ide-shadow-right0Color: rgba(0, 0, 0, 0);
  --intellij-ide-shadow-right1Color: rgba(0, 0, 0, 0.13);
  --intellij-ide-shadow-top0Color: rgba(0, 0, 0, 0);
  --intellij-ide-shadow-top1Color: rgba(0, 0, 0, 0.13);
  --intellij-ide-shadow-topLeft0Color: rgba(0, 0, 0, 0);
  --intellij-ide-shadow-topLeft1Color: rgba(0, 0, 0, 0.13);
  --intellij-ide-shadow-topRight0Color: rgba(0, 0, 0, 0);
  --intellij-ide-shadow-topRight1Color: rgba(0, 0, 0, 0.13);
  --intellij-inactiveCaption: rgba(57, 60, 61, 1);
  --intellij-inactiveCaptionBorder: rgba(108, 108, 108, 1);
  --intellij-inactiveCaptionText: rgba(108, 108, 108, 1);
  --intellij-info: rgba(23, 23, 23, 1);
  --intellij-infoText: rgba(187, 187, 187, 1);
  --intellij-inlineBanner-hoverBackground: rgba(255, 255, 255, 0.078);
  --intellij-inlineBanner-pressedBackground: rgba(255, 255, 255, 0.12);
  --intellij-internalFrame-activeTitleBackground: rgba(238, 238, 238, 1);
  --intellij-internalFrame-activeTitleForeground: rgba(0, 0, 0, 1);
  --intellij-internalFrame-background: rgba(43, 45, 48, 1);
  --intellij-internalFrame-borderColor: rgba(30, 31, 34, 1);
  --intellij-internalFrame-borderDarkShadow: rgba(0, 255, 0, 1);
  --intellij-internalFrame-borderHighlight: rgba(0, 0, 255, 1);
  --intellij-internalFrame-borderLight: rgba(255, 255, 0, 1);
  --intellij-internalFrame-borderShadow: rgba(255, 0, 0, 1);
  --intellij-internalFrame-inactiveTitleBackground: rgba(238, 238, 238, 1);
  --intellij-internalFrame-inactiveTitleForeground: rgba(128, 128, 128, 1);
  --intellij-internalFrame-optionDialogBackground: rgba(238, 238, 238, 1);
  --intellij-internalFrame-optionDialogTitleFont-family: ".AppleSystemUIFont", system-ui, sans-serif;
  --intellij-internalFrame-optionDialogTitleFont-size: 14px;
  --intellij-internalFrame-paletteBackground: rgba(238, 238, 238, 1);
  --intellij-internalFrame-paletteTitleFont-family: ".AppleSystemUIFont", system-ui, sans-serif;
  --intellij-internalFrame-paletteTitleFont-size: 14px;
  --intellij-internalFrame-titleFont-family: ".AppleSystemUIFont", system-ui, sans-serif;
  --intellij-internalFrame-titleFont-size: 14px;
  --intellij-label-background: rgba(43, 45, 48, 1);
  --intellij-label-disabledForeground: rgba(90, 93, 99, 1);
  --intellij-label-disabledShadow: rgba(64, 64, 64, 1);
  --intellij-label-errorForeground: rgba(219, 92, 92, 1);
  --intellij-label-font-family: "Inter", system-ui, sans-serif;
  --intellij-label-font-size: 13px;
  --intellij-label-foreground: rgba(223, 225, 229, 1);
  --intellij-label-selectedForeground: rgba(187, 187, 187, 1);
  --intellij-lesson-badge-newLessonBackground: rgba(73, 156, 84, 1);
  --intellij-lesson-badge-newLessonForeground: rgba(254, 254, 254, 1);
  --intellij-lesson-shortcutBackground: rgba(78, 81, 87, 1);
  --intellij-lesson-stepNumberForeground: rgba(254, 254, 254, 1);
  --intellij-licenseDialog-licenseList-licenseDetailsForeground: rgba(157, 160, 168, 1);
  --intellij-licenseDialog-licenseList-separatorColor: rgba(90, 93, 99, 1);
  --intellij-licenseDialog-separatorColor: rgba(57, 59, 64, 1);
  --intellij-licenseDialog-termsAndConditionsForeground: rgba(111, 115, 122, 1);
  --intellij-lineProfiler-hotLine-foreground: rgba(219, 92, 92, 1);
  --intellij-lineProfiler-hotLine-hoverBackground: rgba(94, 56, 56, 1);
  --intellij-lineProfiler-hotLine-labelBackground: rgba(64, 41, 41, 1);
  --intellij-lineProfiler-ignoredLine-foreground: rgba(78, 81, 87, 1);
  --intellij-lineProfiler-ignoredLine-labelBackground: rgba(43, 45, 48, 1);
  --intellij-lineProfiler-line-foreground: rgba(111, 115, 122, 1);
  --intellij-lineProfiler-line-hoverBackground: rgba(57, 59, 64, 1);
  --intellij-lineProfiler-line-labelBackground: rgba(43, 45, 48, 1);
  --intellij-link-activeForeground: rgba(107, 155, 250, 1);
  --intellij-link-background: rgba(43, 45, 48, 1);
  --intellij-link-focusedBorderColor: rgba(70, 127, 242, 1);
  --intellij-link-foreground: rgba(223, 225, 229, 1);
  --intellij-link-hoverForeground: rgba(107, 155, 250, 1);
  --intellij-link-pressedForeground: rgba(107, 155, 250, 1);
  --intellij-link-secondaryForeground: rgba(107, 155, 250, 1);
  --intellij-link-tag-background: rgba(43, 45, 48, 1);
  --intellij-link-tag-foreground: rgba(223, 225, 229, 1);
  --intellij-link-visitedForeground: rgba(107, 155, 250, 1);
  --intellij-list-background: rgba(43, 45, 48, 1);
  --intellij-list-button-hoverBackground: rgba(53, 83, 143, 1);
  --intellij-list-button-separatorColor: rgba(255, 255, 255, 0.2);
  --intellij-list-dropLineColor: rgba(0, 0, 0, 0.27);
  --intellij-list-font-family: "Inter", system-ui, sans-serif;
  --intellij-list-font-size: 13px;
  --intellij-list-foreground: rgba(223, 225, 229, 1);
  --intellij-list-selectionBackground: rgba(46, 67, 110, 1);
  --intellij-list-selectionForeground: rgba(223, 225, 229, 1);
  --intellij-list-selectionInactiveBackground: rgba(67, 69, 74, 1);
  --intellij-list-selectionInactiveForeground: rgba(223, 225, 229, 1);
  --intellij-list-tag-background: rgba(57, 59, 64, 1);
  --intellij-list-tag-foreground: rgba(134, 138, 145, 1);
  --intellij-mainMenu-background: rgba(43, 45, 48, 1);
  --intellij-mainMenu-foreground: rgba(223, 225, 229, 1);
  --intellij-mainMenu-selectionBackground: rgba(57, 59, 64, 1);
  --intellij-mainMenu-selectionForeground: rgba(223, 225, 229, 1);
  --intellij-mainMenu-transparentSelectionBackground: rgba(255, 255, 255, 0.1);
  --intellij-mainToolbar-background: rgba(43, 45, 48, 1);
  --intellij-mainToolbar-dropdown-background: rgba(43, 45, 48, 1);
  --intellij-mainToolbar-dropdown-hoverBackground: rgba(57, 59, 64, 1);
  --intellij-mainToolbar-dropdown-pressedBackground: rgba(57, 59, 64, 1);
  --intellij-mainToolbar-dropdown-transparentHoverBackground: rgba(255, 255, 255, 0.1);
  --intellij-mainToolbar-foreground: rgba(223, 225, 229, 1);
  --intellij-mainToolbar-icon-background: rgba(43, 45, 48, 1);
  --intellij-mainToolbar-icon-hoverBackground: rgba(57, 59, 64, 1);
  --intellij-mainToolbar-icon-pressedBackground: rgba(57, 59, 64, 1);
  --intellij-mainToolbar-separatorColor: rgba(67, 69, 74, 1);
  --intellij-mainWindow-fullScreeControl-background: rgba(87, 90, 92, 1);
  --intellij-mainWindow-tab-background: rgba(19, 19, 20, 1);
  --intellij-mainWindow-tab-borderColor: rgba(30, 31, 34, 1);
  --intellij-mainWindow-tab-foreground: rgba(180, 184, 191, 1);
  --intellij-mainWindow-tab-hoverBackground: rgba(26, 26, 27, 1);
  --intellij-mainWindow-tab-hoverForeground: rgba(206, 208, 214, 1);
  --intellij-mainWindow-tab-selectedBackground: rgba(43, 45, 48, 1);
  --intellij-mainWindow-tab-selectedForeground: rgba(206, 208, 214, 1);
  --intellij-mainWindow-tab-selectedInactiveBackground: rgba(57, 59, 64, 1);
  --intellij-mainWindow-tab-separatorColor: rgba(43, 45, 48, 1);
  --intellij-managedIdeBadgeBackground: rgba(57, 59, 64, 1);
  --intellij-managedIdeBadgeBackgroundHover: rgba(67, 69, 74, 1);
  --intellij-managedIdeBadgeBorder: rgba(78, 81, 87, 1);
  --intellij-managedIdeMenuItemHover: rgba(67, 69, 74, 1);
  --intellij-memoryIndicator-allocatedBackground: rgba(30, 31, 34, 1);
  --intellij-memoryIndicator-usedBackground: rgba(53, 83, 143, 0.5);
  --intellij-menu: rgba(23, 23, 23, 1);
  --intellij-menu-acceleratorFont-family: "Lucida Grande", system-ui, sans-serif;
  --intellij-menu-acceleratorFont-size: 14px;
  --intellij-menu-acceleratorForeground: rgba(111, 115, 122, 1);
  --intellij-menu-acceleratorSelectionForeground: rgba(0, 0, 0, 1);
  --intellij-menu-background: rgba(43, 45, 48, 1);
  --intellij-menu-borderColor: rgba(67, 69, 74, 1);
  --intellij-menu-disabledBackground: rgba(43, 45, 48, 1);
  --intellij-menu-disabledForeground: rgba(90, 93, 99, 1);
  --intellij-menu-font-family: "Inter", system-ui, sans-serif;
  --intellij-menu-font-size: 13px;
  --intellij-menu-foreground: rgba(223, 225, 229, 1);
  --intellij-menu-selectionBackground: rgba(46, 67, 110, 1);
  --intellij-menu-selectionForeground: rgba(223, 225, 229, 1);
  --intellij-menu-separatorColor: rgba(67, 69, 74, 1);
  --intellij-menuBar-background: rgba(43, 45, 48, 1);
  --intellij-menuBar-borderColor: rgba(30, 31, 34, 1);
  --intellij-menuBar-disabledBackground: rgba(43, 45, 48, 1);
  --intellij-menuBar-disabledForeground: rgba(90, 93, 99, 1);
  --intellij-menuBar-font-family: "Inter", system-ui, sans-serif;
  --intellij-menuBar-font-size: 13px;
  --intellij-menuBar-foreground: rgba(223, 225, 229, 1);
  --intellij-menuBar-highlight: rgba(255, 255, 255, 1);
  --intellij-menuBar-selectionBackground: rgba(46, 67, 110, 1);
  --intellij-menuBar-selectionForeground: rgba(223, 225, 229, 1);
  --intellij-menuBar-shadow: rgba(0, 0, 0, 0.27);
  --intellij-menuItem-acceleratorFont-family: "Inter", system-ui, sans-serif;
  --intellij-menuItem-acceleratorFont-size: 13px;
  --intellij-menuItem-acceleratorForeground: rgba(111, 115, 122, 1);
  --intellij-menuItem-acceleratorSelectionForeground: rgba(187, 187, 187, 1);
  --intellij-menuItem-background: rgba(43, 45, 48, 1);
  --intellij-menuItem-disabledBackground: rgba(43, 45, 48, 1);
  --intellij-menuItem-disabledForeground: rgba(90, 93, 99, 1);
  --intellij-menuItem-font-family: "Inter", system-ui, sans-serif;
  --intellij-menuItem-font-size: 13px;
  --intellij-menuItem-foreground: rgba(223, 225, 229, 1);
  --intellij-menuItem-selectionBackground: rgba(46, 67, 110, 1);
  --intellij-menuItem-selectionForeground: rgba(223, 225, 229, 1);
  --intellij-menuText: rgba(255, 255, 255, 0.85);
  --intellij-navBar-borderColor: rgba(30, 31, 34, 1);
  --intellij-newClass-panel-background: rgba(43, 45, 48, 1);
  --intellij-newClass-searchField-background: rgba(43, 45, 48, 1);
  --intellij-newUiOnboarding-dialog-background: rgba(43, 45, 48, 1);
  --intellij-notification-background: rgba(57, 59, 64, 1);
  --intellij-notification-borderColor: rgba(67, 69, 74, 1);
  --intellij-notification-button-background: rgba(57, 59, 64, 1);
  --intellij-notification-button-borderColor: rgba(111, 115, 122, 1);
  --intellij-notification-button-foreground: rgba(255, 255, 255, 1);
  --intellij-notification-foreground: rgba(240, 241, 242, 1);
  --intellij-notification-iconHoverBackground: rgba(255, 255, 255, 0.1);
  --intellij-notification-linkForeground: rgba(107, 155, 250, 1);
  --intellij-notification-moreButton-background: rgba(43, 45, 48, 1);
  --intellij-notification-moreButton-foreground: rgba(157, 160, 168, 1);
  --intellij-notification-moreButton-innerBorderColor: rgba(67, 69, 74, 1);
  --intellij-notification-shadow-bottom0Color: rgba(0, 0, 0, 0);
  --intellij-notification-shadow-bottom1Color: rgba(0, 0, 0, 0.063);
  --intellij-notification-shadow-bottomLeft0Color: rgba(0, 0, 0, 0);
  --intellij-notification-shadow-bottomLeft1Color: rgba(0, 0, 0, 0.063);
  --intellij-notification-shadow-bottomRight0Color: rgba(0, 0, 0, 0);
  --intellij-notification-shadow-bottomRight1Color: rgba(0, 0, 0, 0.063);
  --intellij-notification-shadow-left0Color: rgba(0, 0, 0, 0);
  --intellij-notification-shadow-left1Color: rgba(0, 0, 0, 0.063);
  --intellij-notification-shadow-right0Color: rgba(0, 0, 0, 0);
  --intellij-notification-shadow-right1Color: rgba(0, 0, 0, 0.063);
  --intellij-notification-shadow-top0Color: rgba(0, 0, 0, 0);
  --intellij-notification-shadow-top1Color: rgba(0, 0, 0, 0.063);
  --intellij-notification-shadow-topLeft0Color: rgba(0, 0, 0, 0);
  --intellij-notification-shadow-topLeft1Color: rgba(0, 0, 0, 0.063);
  --intellij-notification-shadow-topRight0Color: rgba(0, 0, 0, 0);
  --intellij-notification-shadow-topRight1Color: rgba(0, 0, 0, 0.063);
  --intellij-notification-toolWindow-errorBackground: rgba(94, 56, 56, 1);
  --intellij-notification-toolWindow-errorBorderColor: rgba(122, 67, 67, 1);
  --intellij-notification-toolWindow-errorForeground: rgba(223, 225, 229, 1);
  --intellij-notification-toolWindow-informativeBackground: rgba(37, 54, 39, 1);
  --intellij-notification-toolWindow-informativeBorderColor: rgba(55, 82, 57, 1);
  --intellij-notification-toolWindow-informativeForeground: rgba(223, 225, 229, 1);
  --intellij-notification-toolWindow-warningBackground: rgba(61, 50, 35, 1);
  --intellij-notification-toolWindow-warningBorderColor: rgba(94, 77, 51, 1);
  --intellij-notification-toolWindow-warningForeground: rgba(223, 225, 229, 1);
  --intellij-notification-welcomeScreen-separatorColor: rgba(78, 81, 87, 1);
  --intellij-notificationsToolwindow-newNotification-background: rgba(57, 59, 64, 1);
  --intellij-notificationsToolwindow-newNotification-hoverBackground: rgba(57, 59, 64, 1);
  --intellij-notificationsToolwindow-notification-hoverBackground: rgba(43, 45, 48, 1);
  --intellij-onePixelDivider-background: rgba(57, 59, 64, 1);
  --intellij-optionPane-background: rgba(43, 45, 48, 1);
  --intellij-optionPane-buttonFont-family: ".AppleSystemUIFont", system-ui, sans-serif;
  --intellij-optionPane-buttonFont-size: 13px;
  --intellij-optionPane-font-family: "Inter", system-ui, sans-serif;
  --intellij-optionPane-font-size: 13px;
  --intellij-optionPane-foreground: rgba(223, 225, 229, 1);
  --intellij-optionPane-messageFont-family: ".AppleSystemUIFont", system-ui, sans-serif;
  --intellij-optionPane-messageFont-size: 13px;
  --intellij-optionPane-messageForeground: rgba(187, 187, 187, 1);
  --intellij-packageSearch-packageTag-background: rgba(67, 69, 74, 1);
  --intellij-packageSearch-packageTag-foreground: rgba(157, 160, 168, 1);
  --intellij-packageSearch-packageTag-hoverBackground: rgba(74, 76, 78, 1);
  --intellij-packageSearch-packageTag-selectedBackground: rgba(67, 69, 74, 1);
  --intellij-packageSearch-packageTag-selectedForeground: rgba(157, 160, 168, 1);
  --intellij-packageSearch-packageTagSelected-background: rgba(55, 95, 173, 1);
  --intellij-packageSearch-packageTagSelected-foreground: rgba(180, 184, 191, 1);
  --intellij-packageSearch-searchResult-background: rgba(28, 36, 51, 1);
  --intellij-packageSearch-searchResult-hoverBackground: rgba(44, 51, 65, 1);
  --intellij-packageSearch-searchResult-packageTag-background: rgba(46, 54, 67, 1);
  --intellij-packageSearch-searchResult-packageTag-foreground: rgba(223, 225, 229, 1);
  --intellij-packageSearch-searchResult-packageTag-hoverBackground: rgba(61, 67, 80, 1);
  --intellij-packageSearch-searchResult-packageTag-selectedBackground: rgba(55, 95, 173, 1);
  --intellij-packageSearch-searchResult-packageTag-selectedForeground: rgba(223, 225, 229, 1);
  --intellij-panel-background: rgba(43, 45, 48, 1);
  --intellij-panel-font-family: "Inter", system-ui, sans-serif;
  --intellij-panel-font-size: 13px;
  --intellij-panel-foreground: rgba(223, 225, 229, 1);
  --intellij-parameterInfo-background: rgba(57, 59, 64, 1);
  --intellij-parameterInfo-borderColor: rgba(90, 93, 99, 1);
  --intellij-parameterInfo-currentOverloadBackground: rgba(53, 83, 143, 1);
  --intellij-parameterInfo-currentParameterForeground: rgba(223, 225, 229, 1);
  --intellij-parameterInfo-disabledForeground: rgba(111, 115, 122, 1);
  --intellij-parameterInfo-foreground: rgba(206, 208, 214, 1);
  --intellij-parameterInfo-infoForeground: rgba(206, 208, 214, 1);
  --intellij-parameterInfo-lineSeparatorColor: rgba(57, 59, 64, 1);
  --intellij-passwordField-background: rgba(43, 45, 48, 1);
  --intellij-passwordField-capsLockIconColor: rgba(0, 0, 0, 0.39);
  --intellij-passwordField-caretForeground: rgba(187, 187, 187, 1);
  --intellij-passwordField-font-family: "Inter", system-ui, sans-serif;
  --intellij-passwordField-font-size: 13px;
  --intellij-passwordField-foreground: rgba(223, 225, 229, 1);
  --intellij-passwordField-inactiveBackground: rgba(60, 63, 65, 1);
  --intellij-passwordField-inactiveForeground: rgba(90, 93, 99, 1);
  --intellij-passwordField-selectionBackground: rgba(46, 67, 110, 1);
  --intellij-passwordField-selectionForeground: rgba(223, 225, 229, 1);
  --intellij-plugins-background: rgba(43, 45, 48, 1);
  --intellij-plugins-borderColor: rgba(30, 31, 34, 1);
  --intellij-plugins-button-installBorderColor: rgba(78, 128, 82, 1);
  --intellij-plugins-button-installFillBackground: rgba(78, 128, 82, 1);
  --intellij-plugins-button-installFillForeground: rgba(255, 255, 255, 1);
  --intellij-plugins-button-installForeground: rgba(95, 173, 101, 1);
  --intellij-plugins-button-updateBackground: rgba(54, 106, 206, 1);
  --intellij-plugins-button-updateBorderColor: rgba(54, 106, 206, 1);
  --intellij-plugins-button-updateForeground: rgba(255, 255, 255, 1);
  --intellij-plugins-eapTagBackground: rgba(94, 56, 56, 1);
  --intellij-plugins-paidTagBackground: rgba(46, 67, 110, 1);
  --intellij-plugins-searchField-background: rgba(43, 45, 48, 1);
  --intellij-plugins-sectionHeader-background: rgba(57, 59, 64, 1);
  --intellij-plugins-sectionHeader-foreground: rgba(223, 225, 229, 1);
  --intellij-plugins-tagBackground: rgba(67, 69, 74, 1);
  --intellij-plugins-tagForeground: rgba(157, 160, 168, 1);
  --intellij-plugins-trialTagBackground: rgba(55, 82, 57, 1);
  --intellij-popup-advertiser-background: rgba(57, 59, 64, 1);
  --intellij-popup-advertiser-borderColor: rgba(57, 59, 64, 1);
  --intellij-popup-advertiser-foreground: rgba(134, 138, 145, 1);
  --intellij-popup-background: rgba(43, 45, 48, 1);
  --intellij-popup-borderColor: rgba(67, 69, 74, 1);
  --intellij-popup-header-activeBackground: rgba(43, 45, 48, 1);
  --intellij-popup-header-inactiveBackground: rgba(43, 45, 48, 1);
  --intellij-popup-header-inactiveForeground: rgba(111, 115, 122, 1);
  --intellij-popup-inactiveBorderColor: rgba(57, 59, 64, 1);
  --intellij-popup-toolbar-background: rgba(43, 45, 48, 1);
  --intellij-popup-toolbar-borderColor: rgba(30, 31, 34, 1);
  --intellij-popupMenu-background: rgba(43, 45, 48, 1);
  --intellij-popupMenu-font-family: "Inter", system-ui, sans-serif;
  --intellij-popupMenu-font-size: 13px;
  --intellij-popupMenu-foreground: rgba(223, 225, 229, 1);
  --intellij-popupMenu-selectionBackground: rgba(46, 67, 110, 1);
  --intellij-popupMenu-selectionForeground: rgba(223, 225, 229, 1);
  --intellij-popupMenu-translucentBackground: rgba(60, 63, 65, 1);
  --intellij-presentationAssistant-bright-keymapLabel: rgba(153, 187, 255, 1);
  --intellij-presentationAssistant-bright-popup-border: rgba(55, 95, 173, 1);
  --intellij-presentationAssistant-bright-popup-foreground: rgba(255, 255, 255, 1);
  --intellij-presentationAssistant-bright-popupBackground: rgba(55, 95, 173, 1);
  --intellij-presentationAssistant-pale-keymapLabel: rgba(134, 138, 145, 1);
  --intellij-presentationAssistant-pale-popup-border: rgba(57, 59, 64, 1);
  --intellij-presentationAssistant-pale-popup-foreground: rgba(255, 255, 255, 1);
  --intellij-presentationAssistant-pale-popupBackground: rgba(57, 59, 64, 1);
  --intellij-profiler-chartSlider-foreground: rgba(223, 225, 229, 1);
  --intellij-profiler-chartSlider-lineColor: rgba(78, 81, 87, 1);
  --intellij-profiler-cpuChart-background: rgba(55, 82, 57, 1);
  --intellij-profiler-cpuChart-borderColor: rgba(87, 150, 92, 1);
  --intellij-profiler-cpuChart-inactiveBackground: rgba(37, 54, 39, 1);
  --intellij-profiler-cpuChart-inactiveBorderColor: rgba(55, 82, 57, 1);
  --intellij-profiler-cpuChart-pointBackground: rgba(87, 150, 92, 1);
  --intellij-profiler-cpuChart-pointBorderColor: rgba(43, 45, 48, 1);
  --intellij-profiler-liveChart-horizontalAxisColor: rgba(67, 69, 74, 1);
  --intellij-profiler-memoryChart-background: rgba(46, 67, 110, 1);
  --intellij-profiler-memoryChart-borderColor: rgba(84, 138, 247, 1);
  --intellij-profiler-memoryChart-inactiveBackground: rgba(37, 50, 77, 1);
  --intellij-profiler-memoryChart-inactiveBorderColor: rgba(53, 83, 143, 1);
  --intellij-profiler-memoryChart-pointBackground: rgba(54, 106, 206, 1);
  --intellij-profiler-memoryChart-pointBorderColor: rgba(43, 45, 48, 1);
  --intellij-profiler-timer-background: rgba(43, 45, 48, 1);
  --intellij-profiler-timer-disabledForeground: rgba(111, 115, 122, 1);
  --intellij-profiler-timer-foreground: rgba(223, 225, 229, 1);
  --intellij-progressBar-background: rgba(43, 45, 48, 1);
  --intellij-progressBar-failedColor: rgba(189, 87, 87, 1);
  --intellij-progressBar-failedEndColor: rgba(235, 147, 141, 1);
  --intellij-progressBar-font-family: "Inter", system-ui, sans-serif;
  --intellij-progressBar-font-size: 13px;
  --intellij-progressBar-foreground: rgba(223, 225, 229, 1);
  --intellij-progressBar-indeterminateEndColor: rgba(54, 106, 206, 1);
  --intellij-progressBar-indeterminateStartColor: rgba(107, 155, 250, 1);
  --intellij-progressBar-passedColor: rgba(87, 150, 92, 1);
  --intellij-progressBar-passedEndColor: rgba(137, 204, 142, 1);
  --intellij-progressBar-progressColor: rgba(70, 127, 242, 1);
  --intellij-progressBar-selectionBackground: rgba(46, 67, 110, 1);
  --intellij-progressBar-selectionForeground: rgba(223, 225, 229, 1);
  --intellij-progressBar-trackColor: rgba(67, 69, 74, 1);
  --intellij-radioButton-background: rgba(43, 45, 48, 1);
  --intellij-radioButton-darcula-selectionDisabledColor: rgba(96, 96, 96, 1);
  --intellij-radioButton-darcula-selectionDisabledShadowColor: rgba(60, 60, 60, 1);
  --intellij-radioButton-darcula-selectionEnabledColor: rgba(170, 170, 170, 1);
  --intellij-radioButton-darcula-selectionEnabledShadowColor: rgba(30, 30, 30, 1);
  --intellij-radioButton-darkShadow: rgba(0, 0, 0, 1);
  --intellij-radioButton-disabledText: rgba(90, 93, 99, 1);
  --intellij-radioButton-font-family: "Inter", system-ui, sans-serif;
  --intellij-radioButton-font-size: 13px;
  --intellij-radioButton-foreground: rgba(223, 225, 229, 1);
  --intellij-radioButton-highlight: rgba(255, 255, 255, 1);
  --intellij-radioButton-light: rgba(6, 64, 198, 1);
  --intellij-radioButton-select: rgba(255, 102, 102, 1);
  --intellij-radioButton-shadow: rgba(0, 0, 0, 0.27);
  --intellij-radioButtonMenuItem-acceleratorFont-family: "Lucida Grande", system-ui, sans-serif;
  --intellij-radioButtonMenuItem-acceleratorFont-size: 14px;
  --intellij-radioButtonMenuItem-acceleratorForeground: rgba(111, 115, 122, 1);
  --intellij-radioButtonMenuItem-acceleratorSelectionForeground: rgba(0, 0, 0, 1);
  --intellij-radioButtonMenuItem-background: rgba(43, 45, 48, 1);
  --intellij-radioButtonMenuItem-disabledBackground: rgba(43, 45, 48, 1);
  --intellij-radioButtonMenuItem-disabledForeground: rgba(90, 93, 99, 1);
  --intellij-radioButtonMenuItem-font-family: "Inter", system-ui, sans-serif;
  --intellij-radioButtonMenuItem-font-size: 13px;
  --intellij-radioButtonMenuItem-foreground: rgba(223, 225, 229, 1);
  --intellij-radioButtonMenuItem-selectionBackground: rgba(46, 67, 110, 1);
  --intellij-radioButtonMenuItem-selectionForeground: rgba(223, 225, 229, 1);
  --intellij-recentProject-color1-avatar-end: rgba(233, 128, 111, 1);
  --intellij-recentProject-color1-avatar-start: rgba(224, 136, 85, 1);
  --intellij-recentProject-color1-mainToolbarGradientStart: rgba(101, 75, 64, 1);
  --intellij-recentProject-color2-avatar-end: rgba(187, 127, 25, 1);
  --intellij-recentProject-color2-avatar-start: rgba(176, 139, 20, 1);
  --intellij-recentProject-color2-mainToolbarGradientStart: rgba(83, 76, 51, 1);
  --intellij-recentProject-color3-avatar-end: rgba(135, 170, 89, 1);
  --intellij-recentProject-color3-avatar-start: rgba(161, 163, 89, 1);
  --intellij-recentProject-color3-mainToolbarGradientStart: rgba(69, 80, 56, 1);
  --intellij-recentProject-color4-avatar-end: rgba(97, 131, 236, 1);
  --intellij-recentProject-color4-avatar-start: rgba(59, 146, 184, 1);
  --intellij-recentProject-color4-mainToolbarGradientStart: rgba(49, 81, 95, 1);
  --intellij-recentProject-color5-avatar-end: rgba(122, 100, 240, 1);
  --intellij-recentProject-color5-avatar-start: rgba(53, 116, 240, 1);
  --intellij-recentProject-color5-mainToolbarGradientStart: rgba(52, 76, 125, 1);
  --intellij-recentProject-color6-avatar-end: rgba(169, 86, 207, 1);
  --intellij-recentProject-color6-avatar-start: rgba(200, 77, 143, 1);
  --intellij-recentProject-color6-mainToolbarGradientStart: rgba(93, 53, 74, 1);
  --intellij-recentProject-color7-avatar-end: rgba(168, 77, 224, 1);
  --intellij-recentProject-color7-avatar-start: rgba(149, 90, 224, 1);
  --intellij-recentProject-color7-mainToolbarGradientStart: rgba(79, 62, 101, 1);
  --intellij-recentProject-color8-avatar-end: rgba(39, 156, 205, 1);
  --intellij-recentProject-color8-avatar-start: rgba(36, 163, 148, 1);
  --intellij-recentProject-color8-mainToolbarGradientStart: rgba(29, 71, 68, 1);
  --intellij-recentProject-color9-avatar-end: rgba(61, 150, 139, 1);
  --intellij-recentProject-color9-avatar-start: rgba(95, 173, 101, 1);
  --intellij-recentProject-color9-mainToolbarGradientStart: rgba(62, 85, 64, 1);
  --intellij-review-branch-background: rgba(57, 59, 64, 1);
  --intellij-review-branch-background-hover: rgba(67, 69, 74, 1);
  --intellij-review-chatItem-hover: rgba(90, 93, 99, 0.2);
  --intellij-review-state-background: rgba(67, 69, 74, 1);
  --intellij-review-state-foreground: rgba(134, 138, 145, 1);
  --intellij-review-timeline-thread-diff-anchorLine: rgba(84, 75, 45, 1);
  --intellij-runWidget-foreground: rgba(223, 225, 229, 1);
  --intellij-runWidget-hoverBackground: rgba(0, 0, 0, 0.098);
  --intellij-runWidget-iconColor: rgba(223, 225, 229, 1);
  --intellij-runWidget-pressedBackground: rgba(0, 0, 0, 0.16);
  --intellij-runWidget-runIconColor: rgba(95, 173, 101, 1);
  --intellij-runWidget-runningBackground: rgba(87, 150, 92, 1);
  --intellij-runWidget-runningIconColor: rgba(223, 225, 229, 1);
  --intellij-runWidget-stopBackground: rgba(201, 79, 79, 1);
  --intellij-screenView-defaultBorderColor: rgba(0, 0, 0, 1);
  --intellij-screenView-hoveredBorderColor: rgba(84, 138, 247, 1);
  --intellij-screenView-selectedBorderColor: rgba(84, 138, 247, 1);
  --intellij-scrollBar-background: rgba(43, 45, 48, 1);
  --intellij-scrollBar-foreground: rgba(223, 225, 229, 1);
  --intellij-scrollBar-thumb: rgba(255, 255, 255, 0.25);
  --intellij-scrollBar-thumbDarkShadow: rgba(0, 0, 0, 1);
  --intellij-scrollBar-thumbHighlight: rgba(255, 255, 255, 1);
  --intellij-scrollBar-thumbShadow: rgba(0, 0, 0, 0.27);
  --intellij-scrollBar-track: rgba(154, 154, 154, 1);
  --intellij-scrollBar-trackHighlight: rgba(0, 0, 0, 1);
  --intellij-scrollPane-background: rgba(43, 45, 48, 1);
  --intellij-scrollPane-font-family: "Inter", system-ui, sans-serif;
  --intellij-scrollPane-font-size: 13px;
  --intellij-scrollPane-foreground: rgba(223, 225, 229, 1);
  --intellij-scrollbar: rgba(154, 154, 154, 1);
  --intellij-searchEverywhere-advertiser-background: rgba(57, 59, 64, 1);
  --intellij-searchEverywhere-advertiser-foreground: rgba(134, 138, 145, 1);
  --intellij-searchEverywhere-header-background: rgba(43, 45, 48, 1);
  --intellij-searchEverywhere-list-separatorColor: rgba(57, 59, 64, 1);
  --intellij-searchEverywhere-list-settingsBackground: rgba(134, 138, 145, 1);
  --intellij-searchEverywhere-searchField-background: rgba(43, 45, 48, 1);
  --intellij-searchEverywhere-searchField-borderColor: rgba(30, 31, 34, 1);
  --intellij-searchEverywhere-searchField-infoForeground: rgba(111, 115, 122, 1);
  --intellij-searchEverywhere-tab-selectedBackground: rgba(46, 67, 110, 1);
  --intellij-searchEverywhere-tab-selectedForeground: rgba(255, 255, 255, 1);
  --intellij-searchField-errorBackground: rgba(94, 56, 56, 1);
  --intellij-searchField-errorForeground: rgba(219, 92, 92, 1);
  --intellij-searchFieldWithExtension-background: rgba(43, 45, 48, 1);
  --intellij-searchMatch-endBackground: rgba(186, 151, 82, 1);
  --intellij-searchMatch-startBackground: rgba(186, 151, 82, 1);
  --intellij-searchOption-selectedBackground: rgba(53, 83, 143, 1);
  --intellij-searchOption-selectedHoveredBackground: rgba(55, 95, 173, 1);
  --intellij-searchOption-selectedPressedBackground: rgba(55, 95, 173, 1);
  --intellij-segmentedButton-focusedSelectedButtonColor: rgba(53, 83, 143, 1);
  --intellij-segmentedButton-selectedButtonColor: rgba(57, 59, 64, 1);
  --intellij-segmentedButton-selectedEndBorderColor: rgba(111, 115, 122, 1);
  --intellij-segmentedButton-selectedStartBorderColor: rgba(111, 115, 122, 1);
  --intellij-separator-foreground: rgba(223, 225, 229, 1);
  --intellij-separator-highlight: rgba(255, 255, 255, 1);
  --intellij-separator-separatorColor: rgba(57, 59, 64, 1);
  --intellij-separator-shadow: rgba(0, 0, 0, 0.27);
  --intellij-shortcut-borderColor: rgba(111, 115, 122, 1);
  --intellij-sidePanel-background: rgba(43, 45, 48, 1);
  --intellij-slider-background: rgba(43, 45, 48, 1);
  --intellij-slider-buttonBorderColor: rgba(43, 45, 48, 1);
  --intellij-slider-buttonColor: rgba(157, 160, 168, 1);
  --intellij-slider-focus: rgba(0, 0, 0, 1);
  --intellij-slider-font-family: ".AppleSystemUIFont", system-ui, sans-serif;
  --intellij-slider-font-size: 11px;
  --intellij-slider-foreground: rgba(223, 225, 229, 1);
  --intellij-slider-highlight: rgba(255, 255, 255, 1);
  --intellij-slider-shadow: rgba(0, 0, 0, 0.27);
  --intellij-slider-tickColor: rgba(134, 138, 145, 1);
  --intellij-slider-trackColor: rgba(90, 93, 99, 1);
  --intellij-space-review-acceptedOutline: rgba(84, 181, 118, 1);
  --intellij-space-review-waitForResponseOutline: rgba(140, 211, 236, 1);
  --intellij-space-review-workingOutline: rgba(255, 148, 102, 1);
  --intellij-speedSearch-background: rgba(30, 31, 34, 1);
  --intellij-speedSearch-borderColor: rgba(57, 59, 64, 1);
  --intellij-speedSearch-errorForeground: rgba(219, 92, 92, 1);
  --intellij-speedSearch-foreground: rgba(223, 225, 229, 1);
  --intellij-spinner-background: rgba(43, 45, 48, 1);
  --intellij-spinner-font-family: "Inter", system-ui, sans-serif;
  --intellij-spinner-font-size: 13px;
  --intellij-spinner-foreground: rgba(223, 225, 229, 1);
  --intellij-splitPane-background: rgba(43, 45, 48, 1);
  --intellij-splitPane-darkShadow: rgba(0, 0, 0, 1);
  --intellij-splitPane-highlight: rgba(60, 63, 65, 1);
  --intellij-splitPane-shadow: rgba(0, 0, 0, 0.27);
  --intellij-splitPaneDivider-draggingColor: rgba(64, 64, 64, 1);
  --intellij-statusBar-background: rgba(43, 45, 48, 1);
  --intellij-statusBar-borderColor: rgba(30, 31, 34, 1);
  --intellij-statusBar-breadcrumbs-foreground: rgba(157, 160, 168, 1);
  --intellij-statusBar-breadcrumbs-hoverForeground: rgba(223, 225, 229, 1);
  --intellij-statusBar-hoverBackground: rgba(57, 59, 64, 1);
  --intellij-statusBar-lightEditBackground: rgba(47, 71, 94, 1);
  --intellij-statusBar-widget-foreground: rgba(157, 160, 168, 1);
  --intellij-statusBar-widget-hoverForeground: rgba(223, 225, 229, 1);
  --intellij-statusBar-widget-pressedBackground: rgba(67, 69, 74, 1);
  --intellij-tabbedPane-background: rgba(43, 45, 48, 1);
  --intellij-tabbedPane-contentAreaColor: rgba(30, 31, 34, 1);
  --intellij-tabbedPane-darkShadow: rgba(0, 0, 0, 1);
  --intellij-tabbedPane-disabledForeground: rgba(90, 93, 99, 1);
  --intellij-tabbedPane-disabledUnderlineColor: rgba(122, 122, 122, 1);
  --intellij-tabbedPane-focus: rgba(255, 255, 255, 0.85);
  --intellij-tabbedPane-focusColor: rgba(46, 67, 110, 1);
  --intellij-tabbedPane-font-family: "Inter", system-ui, sans-serif;
  --intellij-tabbedPane-font-size: 13px;
  --intellij-tabbedPane-foreground: rgba(223, 225, 229, 1);
  --intellij-tabbedPane-highlight: rgba(255, 255, 255, 1);
  --intellij-tabbedPane-hoverColor: rgba(67, 69, 74, 1);
  --intellij-tabbedPane-light: rgba(6, 64, 198, 1);
  --intellij-tabbedPane-nonSelectedTabTitleNormalColor: rgba(0, 0, 0, 1);
  --intellij-tabbedPane-selectedTabTitleDisabledColor: rgba(255, 255, 255, 0.55);
  --intellij-tabbedPane-selectedTabTitleNonFocusColor: rgba(0, 0, 0, 1);
  --intellij-tabbedPane-selectedTabTitleNormalColor: rgba(255, 255, 255, 0.85);
  --intellij-tabbedPane-selectedTabTitlePressedColor: rgba(255, 255, 255, 0.85);
  --intellij-tabbedPane-selectedTabTitleShadowDisabledColor: rgba(0, 0, 0, 0.25);
  --intellij-tabbedPane-selectedTabTitleShadowNormalColor: rgba(0, 0, 0, 0.39);
  --intellij-tabbedPane-shadow: rgba(0, 0, 0, 0.27);
  --intellij-tabbedPane-smallFont-family: ".AppleSystemUIFont", system-ui, sans-serif;
  --intellij-tabbedPane-smallFont-size: 11px;
  --intellij-tabbedPane-underlineColor: rgba(53, 116, 240, 1);
  --intellij-table-background: rgba(43, 45, 48, 1);
  --intellij-table-cellFocusRing: rgba(10, 96, 255, 1);
  --intellij-table-dropLineColor: rgba(0, 0, 0, 0.27);
  --intellij-table-dropLineShortColor: rgba(0, 0, 0, 1);
  --intellij-table-focusCellBackground: rgba(255, 255, 255, 1);
  --intellij-table-focusCellForeground: rgba(49, 79, 120, 1);
  --intellij-table-font-family: "Inter", system-ui, sans-serif;
  --intellij-table-font-size: 13px;
  --intellij-table-foreground: rgba(223, 225, 229, 1);
  --intellij-table-gridColor: rgba(30, 31, 34, 1);
  --intellij-table-selectionBackground: rgba(46, 67, 110, 1);
  --intellij-table-selectionForeground: rgba(223, 225, 229, 1);
  --intellij-table-selectionInactiveBackground: rgba(67, 69, 74, 1);
  --intellij-table-selectionInactiveForeground: rgba(223, 225, 229, 1);
  --intellij-table-sortIconColor: rgba(0, 0, 0, 0.27);
  --intellij-table-stripeColor: rgba(43, 45, 48, 1);
  --intellij-tableHeader-background: rgba(43, 45, 48, 1);
  --intellij-tableHeader-bottomSeparatorColor: rgba(30, 31, 34, 1);
  --intellij-tableHeader-focusCellBackground: rgba(23, 23, 23, 1);
  --intellij-tableHeader-font-family: "Inter", system-ui, sans-serif;
  --intellij-tableHeader-font-size: 13px;
  --intellij-tableHeader-foreground: rgba(223, 225, 229, 1);
  --intellij-tableHeader-separatorColor: rgba(30, 31, 34, 1);
  --intellij-tag-background: rgba(57, 59, 64, 1);
  --intellij-text: rgba(187, 187, 187, 1);
  --intellij-textArea-background: rgba(43, 45, 48, 1);
  --intellij-textArea-caretForeground: rgba(187, 187, 187, 1);
  --intellij-textArea-font-family: "Monospaced", system-ui, sans-serif;
  --intellij-textArea-font-size: 13px;
  --intellij-textArea-foreground: rgba(223, 225, 229, 1);
  --intellij-textArea-inactiveBackground: rgba(60, 63, 65, 1);
  --intellij-textArea-inactiveForeground: rgba(90, 93, 99, 1);
  --intellij-textArea-selectionBackground: rgba(46, 67, 110, 1);
  --intellij-textArea-selectionForeground: rgba(223, 225, 229, 1);
  --intellij-textComponent-selectionBackgroundInactive: rgba(13, 41, 62, 1);
  --intellij-textField-background: rgba(43, 45, 48, 1);
  --intellij-textField-caretForeground: rgba(187, 187, 187, 1);
  --intellij-textField-darkShadow: rgba(0, 0, 0, 1);
  --intellij-textField-disabledBackground: rgba(43, 45, 48, 1);
  --intellij-textField-font-family: "Inter", system-ui, sans-serif;
  --intellij-textField-font-size: 13px;
  --intellij-textField-foreground: rgba(223, 225, 229, 1);
  --intellij-textField-highlight: rgba(255, 255, 255, 1);
  --intellij-textField-inactiveBackground: rgba(60, 63, 65, 1);
  --intellij-textField-inactiveForeground: rgba(90, 93, 99, 1);
  --intellij-textField-light: rgba(6, 64, 198, 1);
  --intellij-textField-selectionBackground: rgba(46, 67, 110, 1);
  --intellij-textField-selectionForeground: rgba(223, 225, 229, 1);
  --intellij-textField-shadow: rgba(0, 0, 0, 0.27);
  --intellij-textHighlight: rgba(49, 79, 120, 1);
  --intellij-textHighlightText: rgba(255, 255, 255, 1);
  --intellij-textInactiveText: rgba(128, 128, 128, 1);
  --intellij-textPane-background: rgba(43, 45, 48, 1);
  --intellij-textPane-caretForeground: rgba(187, 187, 187, 1);
  --intellij-textPane-font-family: "Inter", system-ui, sans-serif;
  --intellij-textPane-font-size: 13px;
  --intellij-textPane-foreground: rgba(223, 225, 229, 1);
  --intellij-textPane-inactiveBackground: rgba(60, 63, 65, 1);
  --intellij-textPane-inactiveForeground: rgba(90, 93, 99, 1);
  --intellij-textPane-selectionBackground: rgba(46, 67, 110, 1);
  --intellij-textPane-selectionForeground: rgba(223, 225, 229, 1);
  --intellij-textText: rgba(187, 187, 187, 1);
  --intellij-tipOfTheDay-image-borderColor: rgba(57, 59, 64, 1);
  --intellij-titlePane-background: rgba(43, 45, 48, 1);
  --intellij-titlePane-button-hoverBackground: rgba(255, 255, 255, 0.063);
  --intellij-titledBorder-font-family: "Inter", system-ui, sans-serif;
  --intellij-titledBorder-font-size: 13px;
  --intellij-titledBorder-titleColor: rgba(187, 187, 187, 1);
  --intellij-toggleButton-background: rgba(43, 45, 48, 1);
  --intellij-toggleButton-darkShadow: rgba(0, 0, 0, 1);
  --intellij-toggleButton-disabledText: rgba(90, 93, 99, 1);
  --intellij-toggleButton-font-family: "Inter", system-ui, sans-serif;
  --intellij-toggleButton-font-size: 13px;
  --intellij-toggleButton-foreground: rgba(223, 225, 229, 1);
  --intellij-toggleButton-highlight: rgba(255, 255, 255, 1);
  --intellij-toggleButton-light: rgba(6, 64, 198, 1);
  --intellij-toggleButton-shadow: rgba(0, 0, 0, 0.27);
  --intellij-toolBar-background: rgba(43, 45, 48, 1);
  --intellij-toolBar-borderHandleColor: rgba(140, 140, 140, 1);
  --intellij-toolBar-darkShadow: rgba(0, 0, 0, 1);
  --intellij-toolBar-dockingBackground: rgba(238, 238, 238, 1);
  --intellij-toolBar-dockingForeground: rgba(6, 64, 198, 1);
  --intellij-toolBar-floatingBackground: rgba(238, 238, 238, 1);
  --intellij-toolBar-floatingForeground: rgba(64, 64, 64, 1);
  --intellij-toolBar-font-family: "Inter", system-ui, sans-serif;
  --intellij-toolBar-font-size: 13px;
  --intellij-toolBar-foreground: rgba(223, 225, 229, 1);
  --intellij-toolBar-highlight: rgba(255, 255, 255, 1);
  --intellij-toolBar-light: rgba(6, 64, 198, 1);
  --intellij-toolBar-separatorColor: rgba(67, 69, 74, 1);
  --intellij-toolBar-shadow: rgba(0, 0, 0, 0.27);
  --intellij-toolTip-background: rgba(57, 59, 64, 1);
  --intellij-toolTip-borderColor: rgba(67, 69, 74, 1);
  --intellij-toolTip-font-family: "Inter", system-ui, sans-serif;
  --intellij-toolTip-font-size: 13px;
  --intellij-toolTip-foreground: rgba(240, 241, 242, 1);
  --intellij-toolTip-infoForeground: rgba(157, 160, 168, 1);
  --intellij-toolTip-shortcutForeground: rgba(157, 160, 168, 1);
  --intellij-toolWindow-button-dragAndDrop-buttonDropBackground: rgba(53, 83, 143, 0.6);
  --intellij-toolWindow-button-dragAndDrop-buttonDropBorderColor: rgba(84, 138, 247, 1);
  --intellij-toolWindow-button-dragAndDrop-buttonFloatingBackground: rgba(78, 81, 87, 1);
  --intellij-toolWindow-button-dragAndDrop-stripeBackground: rgba(43, 45, 48, 1);
  --intellij-toolWindow-button-foreground: rgba(157, 160, 168, 1);
  --intellij-toolWindow-button-selectedBackground: rgba(53, 116, 240, 1);
  --intellij-toolWindow-button-selectedForeground: rgba(255, 255, 255, 1);
  --intellij-toolWindow-dragAndDrop-areaBackground: rgba(54, 106, 207, 0.3);
  --intellij-toolWindow-header-background: rgba(43, 45, 48, 1);
  --intellij-toolWindow-header-inactiveBackground: rgba(43, 45, 48, 1);
  --intellij-toolWindow-headerCloseButton-background: rgba(43, 45, 48, 1);
  --intellij-toolWindow-headerTab-hoverInactiveBackground: rgba(57, 59, 64, 1);
  --intellij-toolWindow-headerTab-selectedBackground: rgba(49, 59, 69, 1);
  --intellij-toolWindow-headerTab-selectedInactiveBackground: rgba(52, 54, 56, 1);
  --intellij-toolWindow-stripe-dragAndDrop-separatorColor: rgba(84, 138, 247, 1);
  --intellij-toolWindow-stripe-separatorColor: rgba(67, 69, 74, 1);
  --intellij-toolbar-floating-background: rgba(43, 45, 48, 1);
  --intellij-tooltip-actions-background: rgba(43, 45, 48, 1);
  --intellij-tooltip-background: rgba(43, 45, 48, 1);
  --intellij-tooltip-foreground: rgba(223, 225, 229, 1);
  --intellij-tooltip-learning-background: rgba(53, 116, 240, 1);
  --intellij-tooltip-learning-borderColor: rgba(53, 116, 240, 1);
  --intellij-tooltip-learning-codeBorderColor: rgba(255, 255, 255, 0.5);
  --intellij-tooltip-learning-foreground: rgba(255, 255, 255, 1);
  --intellij-tooltip-learning-header-foreground: rgba(255, 255, 255, 1);
  --intellij-tooltip-learning-iconBorderColor: rgba(255, 255, 255, 1);
  --intellij-tooltip-learning-iconFillColor: rgba(84, 138, 247, 1);
  --intellij-tooltip-learning-linkForeground: rgba(255, 255, 255, 1);
  --intellij-tooltip-learning-linkUnderlineDefaultColor: rgba(255, 255, 255, 0.4);
  --intellij-tooltip-learning-linkUnderlineHoveredColor: rgba(255, 255, 255, 0.4);
  --intellij-tooltip-learning-secondaryActionForeground: rgba(153, 187, 255, 1);
  --intellij-tooltip-learning-spanBackground: rgba(55, 95, 173, 1);
  --intellij-tooltip-learning-spanForeground: rgba(255, 255, 255, 1);
  --intellij-tooltip-learning-stepNumberForeground: rgba(153, 187, 255, 1);
  --intellij-tree-background: rgba(43, 45, 48, 1);
  --intellij-tree-dropLineColor: rgba(0, 0, 0, 0.27);
  --intellij-tree-font-family: "Inter", system-ui, sans-serif;
  --intellij-tree-font-size: 13px;
  --intellij-tree-foreground: rgba(223, 225, 229, 1);
  --intellij-tree-hash: rgba(78, 81, 87, 1);
  --intellij-tree-line: rgba(255, 255, 255, 1);
  --intellij-tree-selectionBackground: rgba(46, 67, 110, 1);
  --intellij-tree-selectionBorderColor: rgba(6, 64, 198, 1);
  --intellij-tree-selectionForeground: rgba(223, 225, 229, 1);
  --intellij-tree-selectionInactiveBackground: rgba(67, 69, 74, 1);
  --intellij-tree-selectionInactiveForeground: rgba(223, 225, 229, 1);
  --intellij-tree-textBackground: rgba(60, 63, 65, 1);
  --intellij-tree-textForeground: rgba(187, 187, 187, 1);
  --intellij-uiDesigner-activity-borderColor: rgba(30, 31, 34, 1);
  --intellij-uiDesigner-canvas-background: rgba(43, 45, 48, 1);
  --intellij-uiDesigner-colorPicker-background: rgba(43, 45, 48, 1);
  --intellij-uiDesigner-colorPicker-foreground: rgba(223, 225, 229, 1);
  --intellij-uiDesigner-component-background: rgba(43, 45, 48, 1);
  --intellij-uiDesigner-component-borderColor: rgba(30, 31, 34, 1);
  --intellij-uiDesigner-component-foreground: rgba(223, 225, 229, 1);
  --intellij-uiDesigner-component-hoverBorderColor: rgba(161, 161, 161, 1);
  --intellij-uiDesigner-connector-borderColor: rgba(30, 31, 34, 1);
  --intellij-uiDesigner-connector-hoverBorderColor: rgba(136, 136, 136, 1);
  --intellij-uiDesigner-highStroke-foreground: rgba(223, 225, 229, 1);
  --intellij-uiDesigner-label-foreground: rgba(223, 225, 229, 1);
  --intellij-uiDesigner-list-selectionBackground: rgba(46, 67, 110, 1);
  --intellij-uiDesigner-panel-background: rgba(43, 45, 48, 1);
  --intellij-uiDesigner-percent-foreground: rgba(223, 225, 229, 1);
  --intellij-uiDesigner-placeholder-background: rgba(43, 45, 48, 1);
  --intellij-uiDesigner-placeholder-borderColor: rgba(30, 31, 34, 1);
  --intellij-uiDesigner-placeholder-foreground: rgba(223, 225, 229, 1);
  --intellij-uiDesigner-placeholder-selectedForeground: rgba(156, 205, 255, 1);
  --intellij-unattendedHostStatus-dangerBackground: rgba(219, 92, 92, 1);
  --intellij-unattendedHostStatus-warningBackground: rgba(242, 197, 92, 1);
  --intellij-unattendedHostStatus-warningForeground: rgba(57, 59, 64, 1);
  --intellij-validationTooltip-errorBackground: rgba(94, 56, 56, 1);
  --intellij-validationTooltip-errorBorderColor: rgba(189, 87, 87, 1);
  --intellij-validationTooltip-errorForeground: rgba(255, 255, 255, 1);
  --intellij-validationTooltip-warningBackground: rgba(94, 77, 51, 1);
  --intellij-validationTooltip-warningBorderColor: rgba(186, 151, 82, 1);
  --intellij-validationTooltip-warningForeground: rgba(255, 255, 255, 1);
  --intellij-versionControl-fileHistory-commit-selectedBranchBackground: rgba(28, 36, 51, 1);
  --intellij-versionControl-gitLog-headIconColor: rgba(245, 210, 115, 1);
  --intellij-versionControl-gitLog-localBranchIconColor: rgba(95, 173, 101, 1);
  --intellij-versionControl-gitLog-otherIconColor: rgba(134, 138, 145, 1);
  --intellij-versionControl-gitLog-remoteBranchIconColor: rgba(181, 137, 236, 1);
  --intellij-versionControl-gitLog-tagIconColor: rgba(134, 138, 145, 1);
  --intellij-versionControl-hgLog-bookmarkIconColor: rgba(159, 121, 181, 1);
  --intellij-versionControl-hgLog-branchIconColor: rgba(60, 180, 92, 1);
  --intellij-versionControl-hgLog-closedBranchIconColor: rgba(255, 95, 111, 1);
  --intellij-versionControl-hgLog-headIconColor: rgba(195, 30, 140, 1);
  --intellij-versionControl-hgLog-localTagIconColor: rgba(0, 243, 243, 1);
  --intellij-versionControl-hgLog-mqTagIconColor: rgba(0, 85, 255, 1);
  --intellij-versionControl-hgLog-tagIconColor: rgba(153, 153, 153, 1);
  --intellij-versionControl-hgLog-tipIconColor: rgba(225, 199, 49, 1);
  --intellij-versionControl-log-commit-currentBranchBackground: rgba(40, 48, 68, 1);
  --intellij-versionControl-log-commit-hoveredBackground: rgba(255, 255, 255, 0.93);
  --intellij-versionControl-log-commit-reference-foreground: rgba(111, 115, 122, 1);
  --intellij-versionControl-log-commit-unmatchedForeground: rgba(111, 115, 122, 1);
  --intellij-versionControl-markerPopup-borderColor: rgba(67, 69, 74, 1);
  --intellij-versionControl-markerPopup-toolbar-background: rgba(43, 45, 48, 1);
  --intellij-versionControl-merge-status-noConflicts-foreground: rgba(87, 150, 92, 1);
  --intellij-versionControl-refLabel-foreground: rgba(223, 225, 229, 1);
  --intellij-viewport-background: rgba(43, 45, 48, 1);
  --intellij-viewport-font-family: "Inter", system-ui, sans-serif;
  --intellij-viewport-font-size: 13px;
  --intellij-viewport-foreground: rgba(223, 225, 229, 1);
  --intellij-welcomeScreen-background: rgba(43, 45, 48, 1);
  --intellij-welcomeScreen-details-background: rgba(30, 31, 34, 1);
  --intellij-welcomeScreen-projects-actions-background: rgba(43, 45, 48, 1);
  --intellij-welcomeScreen-projects-actions-selectionBackground: rgba(46, 67, 110, 1);
  --intellij-welcomeScreen-projects-actions-selectionBorderColor: rgba(53, 116, 240, 1);
  --intellij-welcomeScreen-sidePanel-background: rgba(43, 45, 48, 1);
  --intellij-window: rgba(60, 63, 65, 1);
  --intellij-windowBorder: rgba(154, 154, 154, 1);
  --intellij-windowText: rgba(255, 255, 255, 0.85);
}
::-webkit-scrollbar {
  width: 14px;
  height: 14px;
  background-color: rgba(0, 0, 0, 0);
}
::-webkit-scrollbar-track {
  background-color: rgba(0, 0, 0, 0);
}
::-webkit-scrollbar-track:hover {
  background-color: rgba(0, 0, 0, 0);
}
::-webkit-scrollbar-thumb {
  background-color: rgba(255, 255, 255, 0.14902);
  border-radius: 7px;
  border-width: 3px;
  border-style: solid;
  border-color: rgba(0, 0, 0, 0);
  background-clip: padding-box;
  outline: 1px solid rgba(38, 38, 38, 0.34902);
  outline-offset: -3px;
}
::-webkit-scrollbar-thumb:hover {
  background-color: rgba(255, 255, 255, 0.301961);
  border-radius: 7px;
  border-width: 3px;
  border-style: solid;
  border-color: rgba(0, 0, 0, 0);
  background-clip: padding-box;
  outline: 1px solid rgba(38, 38, 38, 0.54902);
  outline-offset: -3px;
}
::-webkit-scrollbar-corner {
  background-color: rgba(0, 0, 0, 0);
}
::-webkit-scrollbar-button {
  display: none;
}
:root {
  background: var(--intellij-tree-background);
  color: var(--intellij-tree-foreground);
}
a {
  color: var(--intellij-hyperlink-linkColor);
}
