/* It might be worth removing this or making this more generic in the future */
@import url("./_vscode-webview.css");

/* This CSS file is intended to setup any generic styles that match behavior
of the client itself. For example, VSCode will set the body background to
transparent because the panel itself sets a background color. To match
that behavior in our mocks, we set the background color here. */

:root body {
  background: var(--vscode-sideBar-background, var(--intellij-panel-background));
  color: var(--vscode-foreground, var(--intellij-panel-foreground));
  font-family: var(--vscode-font-family, var(--intellij-panel-font-family));
}
