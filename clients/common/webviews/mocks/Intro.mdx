# Augment Storybook

Welcome to Augment's component library and design system documentation. This Storybook instance showcases our shared webview components used across different Augment clients (VSCode, IntelliJ, Web).

## Component Organization

Our components are organized into three main categories:

-   **Design System** (`design-system/`): Core UI components like TextAugment, SeparatorAugment, and CalloutAugment
-   **Apps** (`apps/`): Feature-specific components for Chat and NextEditSuggestions
-   **Components** (`components/`): Shared utility components used across different apps

## Getting Started

1. Run Storybook locally:

```bash
pnpm run dev
```

2. Navigate through components using the sidebar
3. View component documentation, props, and examples
4. Experiment with component variations using the Controls panel

## Development Guidelines

-   All new components should include Storybook documentation
-   Use the `.stories.ts` format for component stories
-   Include examples of different component states and variations
-   Provide proper TypeScript types and documentation

For more details on component development, see `clients/common/webviews/README.md`.
