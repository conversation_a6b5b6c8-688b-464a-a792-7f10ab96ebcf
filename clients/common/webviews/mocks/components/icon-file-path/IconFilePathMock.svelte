<script lang="ts">
  import IconFilePath from "$common-webviews/src/common/components/icon-file-path/IconFilePath.svelte";
</script>

<fieldset>
  <legend>Defaults</legend>
  <IconFilePath filepath="src/example.ts" onCodeAction={() => {}} value={[]} />
</fieldset>
<fieldset>
  <legend>Long file path</legend>
  <IconFilePath
    filepath="common-webviews/src/common/icon-file-path/that/should/show/example.py"
    onCodeAction={() => {}}
    value={[]}
  />
</fieldset>
<fieldset>
  <legend>Long file path in small container</legend>
  <div style="width: 200px;outline:1px solid pink">
    <IconFilePath
      filepath="common-webviews/src/common/icon-file-path/that/should/show/example.py"
      onCodeAction={() => {}}
      value={[]}
    />
  </div>
</fieldset>
