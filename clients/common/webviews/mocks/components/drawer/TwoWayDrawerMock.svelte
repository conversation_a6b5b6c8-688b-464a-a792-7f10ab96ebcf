<script lang="ts">
  import TwoWayDrawer from "$common-webviews/src/common/components/drawer/TwoWayDrawer.svelte";

  export let initialPercentage: number = 50;
  export let side: "left" | "right" | "both" = "left";
  export let minimized: boolean = false;
  export let showButton: boolean = true;
  export let columnLayoutThreshold: number = 600;
  export let layoutMode: "row" | "column" | undefined = undefined;
  export let minPercentage: number = 15;
  export let maxPercentage: number = 85;
  export let deadzone: number = 40;
  export let minimizedSide: "left" | "right" | null = null;
</script>

<div class="mock-container">
  <div class="controls">
    <label>
      <input type="checkbox" bind:checked={minimized} />
      Minimized
    </label>

    <label>
      Side to minimize:
      <select bind:value={side}>
        <option value="left">Left/Top</option>
        <option value="right">Right/Bottom</option>
        <option value="both">Both</option>
      </select>
    </label>

    <label>
      Layout Mode:
      <select bind:value={layoutMode}>
        <option value={undefined}>Auto</option>
        <option value="row">Row</option>
        <option value="column">Column</option>
      </select>
    </label>

    {#if side === "both" && minimized}
      <label>
        Minimized Side:
        <select bind:value={minimizedSide}>
          <option value="left">Left</option>
          <option value="right">Right</option>
        </select>
      </label>
    {/if}
  </div>

  <div class="drawer-container">
    <TwoWayDrawer
      {initialPercentage}
      {side}
      bind:minimized
      {showButton}
      {columnLayoutThreshold}
      {layoutMode}
      {minPercentage}
      {maxPercentage}
      {deadzone}
      bind:minimizedSide
    >
      <div class="panel panel-left" slot="left">
        <h3>Left Panel</h3>
        <p>This is the left panel content.</p>
        <ul>
          <li>Item 1</li>
          <li>Item 2</li>
          <li>Item 3</li>
          <li>Item 4</li>
        </ul>
      </div>

      <div class="panel panel-right" slot="right">
        <h3>Right Panel</h3>
        <p>This is the right panel content.</p>
        <div class="content-box">
          <p>You can resize this panel by dragging the handle between the panels.</p>
          <p>The panel will minimize when dragged beyond the threshold plus deadzone.</p>
        </div>
      </div>
    </TwoWayDrawer>
  </div>
</div>

<style>
  .mock-container {
    display: flex;
    flex-direction: column;
    height: 400px;
    width: 100%;
    gap: var(--ds-spacing-2);
  }

  .controls {
    display: flex;
    gap: var(--ds-spacing-4);
    padding: var(--ds-spacing-2);
    background-color: var(--ds-color-neutral-2);
    border-radius: var(--ds-radius-2);
  }

  .drawer-container {
    flex: 1;
    border: 1px solid var(--ds-color-neutral-4);
    border-radius: var(--ds-radius-2);
    overflow: hidden;
  }

  .panel {
    padding: var(--ds-spacing-3);
    height: 100%;
  }

  .panel-left {
    background-color: var(--ds-color-accent-1);
    color: var(--ds-color-accent-11);
  }

  .panel-right {
    background-color: var(--ds-color-neutral-1);
    color: var(--ds-color-neutral-11);
  }

  .content-box {
    margin-top: var(--ds-spacing-3);
    padding: var(--ds-spacing-3);
    background-color: var(--ds-color-neutral-2);
    border-radius: var(--ds-radius-2);
  }

  label {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-1);
  }

  select {
    padding: var(--ds-spacing-1);
    border-radius: var(--ds-radius-1);
  }
</style>
