<script lang="ts">
  import Drawer from "$common-webviews/src/common/components/drawer/Drawer.svelte";
  export let minimized: boolean = false;
  export let deadzone: number = 0;
  export let expandedMinWidth: number = 50;
  export let initialWidth: number = 300;
</script>

<input type="checkbox" bind:checked={minimized} />
<Drawer bind:minimized {deadzone} {expandedMinWidth} {initialWidth}>
  <div slot="right">
    <div>Summary</div>
    <div>Nested content 1</div>
    <div>Nested content 2</div>
    <div>Nested content 3</div>
    <div>Nested content 4</div>
  </div>
  <div class="c-drawer-mock__content" slot="left">
    <ul>
      <li>Nested content 1</li>
      <li>Nested content 2</li>
      <li>Nested content 3</li>
      <li>Nested content 4</li>
    </ul>
  </div>
</Drawer>

<style>
  .c-drawer-mock__content {
    padding: var(--ds-spacing-1);
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-1);
    background-color: rebeccapurple;
    height: 100%;
    width: 100%;
    color: white;
  }
</style>
