/* eslint-disable @typescript-eslint/naming-convention */
import type { <PERSON>a, StoryObj } from "@storybook/svelte-vite";
import component from "./TwoWayDrawerMock.svelte";

const meta = {
  title: "components/TwoWayDrawer",
  component,
  tags: ["autodocs"],
  // Add argTypes to define the controls
  argTypes: {
    initialPercentage: {
      control: { type: "range", min: 0, max: 100, step: 1 },
      description: "Initial percentage for the first panel (0-100)",
    },
    side: {
      control: { type: "select", options: ["left", "right", "both"] },
      description: "Which side should be minimizable: 'left', 'right', or 'both'",
    },
    minimized: {
      control: { type: "boolean" },
      description: "Whether the drawer is currently minimized",
    },
    showButton: {
      control: { type: "boolean" },
      description: "Whether to show the button to restore from minimized state",
    },
    columnLayoutThreshold: {
      control: { type: "number" },
      description: "Width threshold at which to switch to column layout",
    },
    layoutMode: {
      control: { type: "select", options: [undefined, "row", "column"] },
      description:
        "Force a specific layout mode. If undefined, layout mode is automatic based on width",
    },
    minPercentage: {
      control: { type: "range", min: 0, max: 50, step: 1 },
      description: "Minimum percentage for the resizable panel when expanded",
    },
    maxPercentage: {
      control: { type: "range", min: 50, max: 100, step: 1 },
      description: "Maximum percentage for the resizable panel when expanded",
    },
    deadzone: {
      control: { type: "number" },
      description: "Deadzone in pixels to ignore when minimizing",
    },
    minimizedSide: {
      control: { type: "select", options: [null, "left", "right"] },
      description: "Which side is currently minimized when using 'both' mode",
    },
  },
} satisfies Meta<component>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    initialPercentage: 50,
    side: "left",
    minimized: false,
    showButton: true,
    columnLayoutThreshold: 600,
    layoutMode: undefined,
    minPercentage: 15,
    maxPercentage: 85,
    deadzone: 40,
    minimizedSide: null,
  },
};

export const MinimizedLeft: Story = {
  args: {
    ...Default.args,
    minimized: true,
    side: "left",
  },
};

export const MinimizedRight: Story = {
  args: {
    ...Default.args,
    minimized: true,
    side: "right",
  },
};

export const ColumnLayout: Story = {
  args: {
    ...Default.args,
    layoutMode: "column",
  },
};

export const CustomSizing: Story = {
  args: {
    ...Default.args,
    initialPercentage: 30,
    minPercentage: 20,
    maxPercentage: 70,
  },
};

export const BothSidesMinimizable: Story = {
  args: {
    ...Default.args,
    side: "both",
  },
};

export const MinimizedLeftFromBoth: Story = {
  args: {
    ...Default.args,
    side: "both",
    minimized: true,
    minimizedSide: "left",
  },
};

export const MinimizedRightFromBoth: Story = {
  args: {
    ...Default.args,
    side: "both",
    minimized: true,
    minimizedSide: "right",
  },
};
