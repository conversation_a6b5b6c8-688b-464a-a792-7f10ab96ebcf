<script lang="ts">
  import { type IEditSuggestion } from "$vscode/src/next-edit/next-edit-types";
  import {
    type WebViewMessage,
    WebViewMessageType,
  } from "$vscode/src/webview-providers/webview-messages";
  import { host } from "$common-webviews/src/common/hosts/host";
  import CodeRoll from "$common-webviews/src/common/components/code-roll/CodeRoll.svelte";
  import {
    clearSuggestions,
    addSuggestion,
    setSuggestions,
    readFile,
    getMultiFile,
  } from "../../apps/next-edit-suggestions/next-edit-suggestions-mock";
  import { onMount } from "svelte";
  import type { OnCodeAction } from "$common-webviews/src/common/components/code-roll/types";
  import { setCodeRollSelection } from "$common-webviews/src/common/components/code-roll/code-roll-context";
  let isRefreshing = false;
  let suggestions: IEditSuggestion[] = [];
  let ctx = setCodeRollSelection({});

  setSuggestions(getMultiFile());

  onMount(() => {
    host.postMessage({
      type: WebViewMessageType.nextEditLoaded,
    });
  });

  export async function handleMessage(e: MessageEvent<WebViewMessage>) {
    const msg = e.data;
    switch (msg.type) {
      case WebViewMessageType.nextEditRefreshStarted:
        isRefreshing = true;
        break;
      case WebViewMessageType.nextEditRefreshFinished:
        isRefreshing = false;
        break;
      case WebViewMessageType.nextEditSuggestionsChanged:
        suggestions = msg.data.suggestions;
        $ctx = { ...$ctx };
        break;
      case WebViewMessageType.nextEditNextSuggestionChanged:
        $ctx = { ...$ctx, nextSuggestion: msg.data };
        break;
    }
  }

  function onItemSelected(suggestion: IEditSuggestion) {
    host.postMessage({
      type: WebViewMessageType.nextEditOpenSuggestion,
      data: suggestion,
    });
  }

  function onItemFocused(suggestion: IEditSuggestion) {
    host.postMessage({
      type: WebViewMessageType.nextEditNextSuggestionChanged,
      data: suggestion,
    });
  }
  const onCodeAction: OnCodeAction = (action, suggestion) => {
    switch (action) {
      case "accept":
        break;
      case "reject":
        break;
      case "open":
        break;
      case "union":
        break;
      case "select":
        if (!suggestion || Array.isArray(suggestion)) return;
        onItemSelected(suggestion);
        break;
      case "focus":
        if (!suggestion || Array.isArray(suggestion)) return;
        onItemFocused(suggestion);
        break;
    }
  };
</script>

<svelte:window on:message={handleMessage} />

<main>
  <div class="buttons">
    <button on:click={clearSuggestions}>Clear</button>
    <button on:click={addSuggestion}>Add</button>
  </div>
  <div>
    <h1>Code Roll</h1>
    <CodeRoll {suggestions} {onCodeAction} loading={isRefreshing} {readFile} />
  </div>
</main>

<style>
  :global(body) {
    padding: 0;
  }
  main {
    display: flex;
    flex-direction: column;
    justify-content: start;
    width: 100%;
  }
  .buttons {
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    flex-direction: row;
    gap: 1em;
    border-bottom: 1px solid #ccc;
    padding: 2em;
    margin: 0 0 1em 0;
  }
</style>
