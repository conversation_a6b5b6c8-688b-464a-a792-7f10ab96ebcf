import type { IEditSuggestion } from "$vscode/src/next-edit/next-edit-types";

const sampleGlob = import.meta.glob("./samples/*.ts", {
  eager: true,
  import: "default",
}) as Record<string, IEditSuggestion>;

const filesGlob = import.meta.glob("./samples/*.txt", {
  eager: true,
  import: "default",
  query: "?raw",
}) as Record<string, string>;

export const samples = () => Array.from(Object.values(sampleGlob));

export const files = (): Record<string, string> => {
  const sampleToNameMap = Object.fromEntries(
    Array.from(Object.entries(sampleGlob), ([k, v]) => [
      k.replace(".ts", ".txt"),
      v.qualifiedPathName.relPath,
    ]),
  );

  const ret = Object.fromEntries(
    Array.from(Object.entries(filesGlob), ([k, v]) => [sampleToNameMap[k], v]),
  );
  return ret;
};
