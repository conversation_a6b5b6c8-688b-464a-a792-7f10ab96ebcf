export default {
  requestId: "9eeaaecb-f37b-459d-acae-813460c44391",
  mode: "BACKGROUND",
  scope: "FILE",
  result: {
    suggestionId: "15707180-9a46-4586-a512-655bfcf9c4a7",
    path: "superset/views/alerts.py",
    blobName: "4c964f149146969703c84f092482df16d261ba837e2854c5c9f3e5cf8acee3cc",
    charStart: 1909,
    charEnd: 1909,
    existingCode: "",
    suggestedCode: '    display_name = "<PERSON>ert"\n',
    truncationChar: undefined,
    changeDescription: 'Add: display_name = "<PERSON>ert"',
    diffSpans: [
      {
        original: {
          start: 0,
          stop: 0,
        },
        updated: {
          start: 0,
          stop: 27,
        },
      },
    ],
    editingScore: 0.72442,
    localizationScore: 0.8266039,
    editingScoreThreshold: 1,
  },
  qualifiedPathName: {
    rootPath: "/example/samples/superset",
    relPath: "superset/views/alerts.py",
  },
  lineRange: {
    start: 54,
    stop: 54,
  },
  uriScheme: "file",
  occurredAt: "2024-11-18T22:01:41.533Z",
  state: "fresh",
  changeType: "insertion",
  highlightRange: {
    start: 53,
    stop: 54,
  },
};
