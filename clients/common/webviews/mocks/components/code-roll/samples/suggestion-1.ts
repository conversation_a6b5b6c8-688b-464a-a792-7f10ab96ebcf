export default {
  requestId: "1985d07c-bf5e-4903-9390-6119f2640d7f",
  mode: "BACKGROUND",
  scope: "FILE",
  result: {
    suggestionId: "d5f0569c-28cc-4f17-8ca7-c2985975d235",
    path: "superset-frontend/src/components/Form/LabeledErrorBoundInput.tsx",
    blobName: "9178863b25fb8c2bee5357892bed7fccc05a1ab4f825b24afe0324303aca27f0",
    charStart: 3252,
    charEnd: 3309,
    existingCode: "      <StyledFormLabel htmlFor={id} required={required}>\n",
    suggestedCode: "      <StyledFormLabel htmlFor={id} required={required} name={name}>\n",
    truncationChar: undefined,
    changeDescription: "Add: name={name}",
    diffSpans: [
      {
        original: {
          start: 0,
          stop: 55,
        },
        updated: {
          start: 0,
          stop: 55,
        },
      },
      {
        original: {
          start: 55,
          stop: 55,
        },
        updated: {
          start: 55,
          stop: 67,
        },
      },
      {
        original: {
          start: 55,
          stop: 57,
        },
        updated: {
          start: 67,
          stop: 69,
        },
      },
    ],
    editingScore: 0.8909306,
    localizationScore: 0.8355313,
    editingScoreThreshold: 1,
  },
  qualifiedPathName: {
    rootPath: "/example/samples/superset",
    relPath: "superset-frontend/src/components/Form/LabeledErrorBoundInput.tsx",
  },
  lineRange: {
    start: 118,
    stop: 119,
  },
  uriScheme: "file",
  occurredAt: "2024-11-18T22:00:13.309Z",
  state: "fresh",
  changeType: "modification",
  highlightRange: {
    start: 118,
    stop: 119,
  },
};
