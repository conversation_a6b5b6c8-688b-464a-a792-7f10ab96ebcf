export default {
  requestId: "bafab24c-5948-40d8-9719-603863443f36",
  mode: "BACKGROUND",
  scope: "WORKSPACE",
  result: {
    suggestionId: "9babd8c8-49a8-481f-887a-7d7fc85e949d",
    path: "superset/commands/annotation_layer/annotation/create.py",
    blobName: "76ba0a23a3acdd0391b54d599b76c3f097198fe8fe9cbf48daee1057f501ebb0",
    charStart: 1633,
    charEnd: 1633,
    existingCode: "",
    suggestedCode:
      '\n    def debug (self) -> None:\n        logger.debug("CreateAnnotationCommand: %s", self._properties)\n',
    truncationChar: undefined,
    changeDescription: "Add debug logging",
    diffSpans: [
      {
        original: {
          start: 0,
          stop: 0,
        },
        updated: {
          start: 0,
          stop: 101,
        },
      },
    ],
    editingScore: 0.7019872,
    localizationScore: 0.9069606,
    editingScoreThreshold: 1,
  },
  qualifiedPathName: {
    rootPath: "/example/samples/superset",
    relPath: "superset/commands/annotation_layer/annotation/create.py",
  },
  lineRange: {
    start: 41,
    stop: 41,
  },
  uriScheme: "file",
  occurredAt: "2024-11-18T22:07:54.133Z",
  state: "fresh",
  changeType: "insertion",
  highlightRange: {
    start: 40,
    stop: 41,
  },
};
