export default {
  requestId: "d859f856-8c66-421c-9e2a-1adcc77b11cd",
  mode: "BACKGROUND",
  scope: "WORKSPACE",
  result: {
    suggestionId: "73cdf6e9-0aa8-4e5d-86ec-b2af9a80714f",
    path: "superset/commands/annotation_layer/update.py",
    blobName: "4b12290503348ad0b8c031a37a6a48711a6dff2000e02082b4f7f0f2c1f1dc67",
    charStart: 1672,
    charEnd: 1672,
    existingCode: "",
    suggestedCode:
      '    def debug (self) -> None:\n        logger.debug("UpdateAnnotationLayerCommand: %s", self._properties)\n\n',
    truncationChar: undefined,
    changeDescription: "Add debug logging",
    diffSpans: [
      {
        original: {
          start: 0,
          stop: 0,
        },
        updated: {
          start: 0,
          stop: 106,
        },
      },
    ],
    editingScore: 0.7019872,
    localizationScore: 0.8928995,
    editingScoreThreshold: 1,
  },
  qualifiedPathName: {
    rootPath: "/example/samples/superset",
    relPath: "superset/commands/annotation_layer/update.py",
  },
  lineRange: {
    start: 43,
    stop: 43,
  },
  uriScheme: "file",
  occurredAt: "2024-11-18T22:03:28.834Z",
  state: "fresh",
  changeType: "insertion",
  highlightRange: {
    start: 42,
    stop: 43,
  },
};
