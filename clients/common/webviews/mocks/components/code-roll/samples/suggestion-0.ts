export default {
  requestId: "1985d07c-bf5e-4903-9390-6119f2640d7f",
  mode: "BACKGROUND",
  scope: "FILE",
  result: {
    suggestionId: "af00a4d0-addb-4be2-a0e3-83ec20ffdab4",
    path: "superset-frontend/src/components/Form/LabeledErrorBoundInput.tsx",
    blobName: "9178863b25fb8c2bee5357892bed7fccc05a1ab4f825b24afe0324303aca27f0",
    charStart: 2969,
    charEnd: 2969,
    existingCode: "",
    suggestedCode: "  name,\n",
    truncationChar: undefined,
    changeDescription: "Add: name,",
    diffSpans: [
      {
        original: {
          start: 0,
          stop: 0,
        },
        updated: {
          start: 0,
          stop: 8,
        },
      },
    ],
    editingScore: 0.8909306,
    localizationScore: 0.8355313,
    editingScoreThreshold: 1,
  },
  qualifiedPathName: {
    rootPath: "/example/samples/superset",
    relPath: "superset-frontend/src/components/Form/LabeledErrorBoundInput.tsx",
  },
  lineRange: {
    start: 103,
    stop: 103,
  },
  uriScheme: "file",
  occurredAt: "2024-11-18T21:58:02.046Z",
  state: "fresh",
  changeType: "insertion",
  highlightRange: {
    start: 102,
    stop: 103,
  },
};
