export default {
  requestId: "28881691-3b4b-4c97-9e92-3b5b0ddf2172",
  mode: "BACKGROUND",
  scope: "FILE",
  result: {
    suggestionId: "033d45bb-868e-456d-af77-bebac12ccc90",
    path: "superset/sqllab/validators.py",
    blobName: "6a82b439feaaad4f4f1ca055fc8b5733d6e159f30faa8dc33b3b0ece5f3aebb6",
    charStart: 1232,
    charEnd: 1237,
    existingCode: "    d",
    suggestedCode:
      "    def validate_query_id(self, query_id: int) -> None:\n        security_manager.raise_for_access(query_id=query_id)\n",
    changeDescription: "Add query ID validation",
    diffSpans: [
      {
        original: {
          start: 0,
          stop: 4,
        },
        updated: {
          start: 0,
          stop: 4,
        },
      },
      {
        original: {
          start: 4,
          stop: 5,
        },
        updated: {
          start: 4,
          stop: 117,
        },
      },
    ],
    editingScore: 0.7898819,
    localizationScore: 0.99942034,
    editingScoreThreshold: 1,
  },
  qualifiedPathName: {
    rootPath: "/example/samples/samples/superset",
    relPath: "superset/sqllab/validators.py",
  },
  lineRange: {
    start: 31,
    stop: 32,
  },
  uriScheme: "file",
  occurredAt: "2024-12-11T18:28:52.034Z",
  state: "fresh",
  changeType: "modification",
  highlightRange: {
    start: 31,
    stop: 32,
  },
};
