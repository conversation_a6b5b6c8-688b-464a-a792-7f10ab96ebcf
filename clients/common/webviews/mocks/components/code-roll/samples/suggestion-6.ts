export default {
  requestId: "7c05c72c-d80f-4333-8076-5d96a8996b69",
  mode: "BACKGROUND",
  scope: "FILE",
  result: {
    suggestionId: "9e81d3f0-d51b-480e-8adf-6bc79adde977",
    path: "packages/pbj-visualization/server/pbj.ts",
    blobName: "02c3d3a6faf78a11b5d947731c8e65bbcce597f916235a64faff87db96611fe4",
    charStart: 1403,
    charEnd: 1403,
    existingCode: "",
    suggestedCode: "  return app;\n",
    changeDescription: "Add: return app;",
    diffSpans: [
      {
        original: {
          start: 0,
          stop: 0,
        },
        updated: {
          start: 0,
          stop: 14,
        },
      },
    ],
    editingScore: 0.43080792,
    localizationScore: 0.91452044,
    editingScoreThreshold: 1,
  },
  qualifiedPathName: {
    rootPath: "/examples/github-debris/pbj",
    relPath: "packages/pbj-visualization/server/pbj.ts",
  },
  lineRange: {
    start: 55,
    stop: 55,
  },
  uriScheme: "file",
  occurredAt: "2024-12-16T16:39:30.397Z",
  state: "fresh",
  changeType: "insertion",
  highlightRange: {
    start: 54,
    stop: 55,
  },
};
