<script>
  import Detail from "$common-webviews/src/common/components/detail/Detail.svelte";

  let open = false;
  let checked = true;
</script>

<div>
  <Detail>
    <div slot="summary">Summary</div>
    <div class="c-detail-mock__content">
      <div>Nested content 1</div>
      <div>Nested content 2</div>
      <div>Nested content 3</div>
      <div>Nested content 4</div>
    </div>
  </Detail>
  <Detail open={false}>
    <div slot="summary">Summary is closed</div>
    <div class="c-detail-mock__content">
      <div>Nested content 1</div>
      <div>Nested content 2</div>
      <div>Nested content 3</div>
      <div>Nested content 4</div>
    </div>
  </Detail>
  <Detail bind:open>
    <div slot="summary">
      Bound to Open Toggles {open ? "open" : "closed"}
    </div>
    <div class="c-detail-mock__content">
      <div>Nested content 1</div>
      <div>Nested content 2</div>
      <div>Nested content 3</div>
      <div>Nested content 4</div>
    </div>
  </Detail>

  <Detail>
    <div slot="summary">Nested Summary</div>
    <div class="c-detail-mock__content">
      <Detail open={false}>
        <div slot="summary">Summary 1</div>
        <div class="c-detail-mock__content">
          <div>Nested content 1</div>
          <div>Nested content 2</div>
          <div>Nested content 3</div>
          <div>Nested content 4</div>
        </div>
      </Detail>
      <Detail open={false}>
        <div slot="summary">Summary 2</div>
        <div class="c-detail-mock__content">
          <div>Nested content 1</div>
          <div>Nested content 2</div>
          <div>Nested content 3</div>
          <div>Nested content 4</div>
        </div>
      </Detail>
    </div>
  </Detail>
  <fieldset>
    <legend> <label>Remote control <input type="checkbox" bind:checked /> </label></legend>
    <Detail bind:open={checked}>
      <div slot="summary">Remote control</div>
      <div class="content">
        <div>Nested content 1</div>
        <div>Nested content 2</div>
        <div>Nested content 3</div>
        <div>Nested content 4</div>
      </div>
    </Detail>
  </fieldset>
</div>

<style>
  .c-detail-mock__content {
    padding: var(--ds-spacing-1);
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-1);
  }
</style>
