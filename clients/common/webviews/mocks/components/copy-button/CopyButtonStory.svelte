<script lang="ts">
  import ColumnLayout from "$common-webviews/mocks/storybook/ColumnLayout.svelte";
  import Fieldset from "$common-webviews/mocks/storybook/Fieldset.svelte";
  import CopyButton from "$common-webviews/src/common/components/CopyButton.svelte";
  import type {
    ButtonVariant,
    ButtonColor,
  } from "$common-webviews/src/design-system/_primitives/BaseButton.svelte";

  const variants: Array<{
    text: string;
    variant: ButtonVariant;
    label: string;
  }> = [
    { text: "Classic variant", variant: "classic", label: "Classic" },
    { text: "Solid variant", variant: "solid", label: "Solid" },
    { text: "Ghost variant", variant: "ghost", label: "Ghost" },
    { text: "Outline variant", variant: "outline", label: "Outline" },
    { text: "Solid variant", variant: "solid", label: "Solid" },
  ];

  const colors: Array<{
    text: string;
    color: ButtonColor;
    label: string;
  }> = [
    { text: "Neutral color", color: "neutral", label: "Neutral" },
    { text: "Accent color", color: "accent", label: "Accent" },
    { text: "Success color", color: "success", label: "Success" },
    { text: "Error color", color: "error", label: "Error" },
  ];
</script>

<ColumnLayout>
  <Fieldset title="Default (Icon Only)">
    <CopyButton text="Text to copy" />
  </Fieldset>

  <Fieldset title="With Text Slot">
    <CopyButton text="Text to copy">
      <span slot="text">Copy to clipboard</span>
    </CopyButton>
  </Fieldset>

  <Fieldset title="With Styled Text Slot">
    <CopyButton text="Text to copy with custom styling" size={2} variant="solid" color="accent">
      <span slot="text">
        <span style="font-weight: bold;">Copy</span> <span style="font-style: italic;">text</span>
      </span>
    </CopyButton>
  </Fieldset>

  <Fieldset title="Different Variants">
    <div style="display: flex; gap: 8px;">
      {#each variants as { text, variant, label }}
        <CopyButton {text} {variant}>
          <span slot="text">{label}</span>
        </CopyButton>
      {/each}
    </div>
  </Fieldset>

  <Fieldset title="Different Colors">
    <div style="display: flex; gap: 8px;">
      {#each colors as { text, color, label }}
        <CopyButton {text} {color}>
          <span slot="text">{label}</span>
        </CopyButton>
      {/each}
    </div>
  </Fieldset>
</ColumnLayout>
