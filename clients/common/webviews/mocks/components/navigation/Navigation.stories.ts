/* eslint-disable @typescript-eslint/naming-convention */
import type { Meta } from "@storybook/svelte-vite";
import component from "./NavigationMock.svelte";
import { createNavigationItem } from "$common-webviews/src/common/components/navigation/Navigation.svelte";
import ToolIcon from "$common-webviews/src/design-system/icons/augment/tools.svelte";
import CompassIcon from "$common-webviews/src/design-system/icons/augment/compass.svelte";
import ContextDoc from "$common-webviews/src/design-system/icons/augment/context-doc.svelte";
import type { ComponentType } from "svelte";

const meta = {
  title: "components/Navigation",
  component,
  tags: ["autodocs"],
  argTypes: {
    mode: {
      control: { type: "select", options: ["tree", "flat"] },
      description: "Navigation display mode: tree or flat",
    },
    items: {
      control: false,
      description: "Array of navigation items",
    },
    group: {
      control: "text",
      description: "Default group name for items without a group",
    },
  },
} satisfies Meta<component>;

export default meta;

export const TreeMode = {
  args: {
    items: [
      createNavigationItem(
        "Tools",
        "Configure your tools",
        // SVELTE5_MIGRATION - AU-11877
        ToolIcon as unknown as ComponentType,
        "section-tools",
      ),
      createNavigationItem(
        "Orientation",
        "Codebase orientation settings",
        // SVELTE5_MIGRATION - AU-11877
        CompassIcon as unknown as ComponentType,
        "section-orientation",
      ),
      createNavigationItem(
        "Context",
        "Workspace context settings",
        // SVELTE5_MIGRATION - AU-11877
        ContextDoc as unknown as ComponentType,
        "section-context",
      ),
    ],
    mode: "tree",
  },
};

export const FlatMode = {
  args: {
    mode: "flat",
    items: [
      createNavigationItem(
        "Tools",
        "Configure your tools",
        // SVELTE5_MIGRATION - AU-11877
        ToolIcon as unknown as ComponentType,
        "section-tools",
      ),
      createNavigationItem(
        "Orientation",
        "Codebase orientation settings",
        // SVELTE5_MIGRATION - AU-11877
        CompassIcon as unknown as ComponentType,
        "section-orientation",
      ),
      createNavigationItem(
        "Context",
        "Workspace context settings",
        // SVELTE5_MIGRATION - AU-11877
        ContextDoc as unknown as ComponentType,
        "section-context",
      ),
    ],
  },
};

export const WithGroups = {
  args: {
    mode: "tree",
    items: [
      {
        ...createNavigationItem(
          "Tools",
          "Configure your tools",
          // SVELTE5_MIGRATION - AU-11877
          ToolIcon as unknown as ComponentType,
          "section-tools",
        ),
        group: "Settings",
      },
      {
        ...createNavigationItem(
          "Orientation",
          "Codebase orientation",
          // SVELTE5_MIGRATION - AU-11877
          CompassIcon as unknown as ComponentType,
          "section-orientation",
        ),
        group: "Settings",
      },
      {
        ...createNavigationItem(
          "Context",
          "Workspace context",
          // SVELTE5_MIGRATION - AU-11877
          ContextDoc as unknown as ComponentType,
          "section-context",
        ),
        group: "Workspace",
      },
    ],
  },
};

export const WithComponents = {
  args: {
    mode: "tree",
    items: [
      createNavigationItem(
        "Tools",
        "Configure your tools",
        // SVELTE5_MIGRATION - AU-11877
        ToolIcon as unknown as ComponentType,
        "section-tools",
        component,
        {
          mode: "tree",
        },
      ),
      createNavigationItem(
        "Orientation",
        "Codebase orientation",
        // SVELTE5_MIGRATION - AU-11877
        CompassIcon as unknown as ComponentType,
        "section-orientation",
      ),
    ],
  },
};
