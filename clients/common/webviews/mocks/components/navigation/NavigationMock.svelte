<script lang="ts" generics="T extends Props | undefined = undefined">
  import Navigation from "$common-webviews/src/common/components/navigation/Navigation.svelte";
  import {
    type NavigationItem,
    type Props,
    isNavigationItem,
    isWithComponent,
  } from "$common-webviews/src/common/components/navigation/navigation-types";

  export let items: NavigationItem<T>[] = [];
  export let mode: "tree" | "flat" = "tree";
  export let group = "Default Group";
</script>

<div class="navigation-container">
  <Navigation {items} {mode} {group}>
    <span slot="content" let:item>
      {#if isNavigationItem(item)}
        <div class="content-placeholder">
          <h3>{item.name} Content</h3>
          <p>{item.description}</p>
          {#if isWithComponent(item)}
            <div class="props-display">
              <h4>Component Props:</h4>
              <pre>{JSON.stringify(item.props, null, 2)}</pre>
            </div>
          {/if}
        </div>
      {/if}
    </span>
  </Navigation>
</div>

<style>
  .navigation-container {
    height: 500px;
    width: 100%;
    border: 1px solid var(--ds-color-border);
  }

  .content-placeholder {
    padding: var(--ds-spacing-4);
  }

  .props-display {
    margin-top: var(--ds-spacing-4);
    padding: var(--ds-spacing-3);
    background-color: var(--ds-color-surface-2);
    border-radius: var(--ds-border-radius-2);
  }

  :global(#storybook-root) {
    height: 100%;
  }
</style>
