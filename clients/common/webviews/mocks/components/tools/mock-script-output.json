{"script_result": {"command": "setup-script.sh", "output": "ing up fontconfig (2.13.1-4.2ubuntu5) ...\r\nRegenerating fonts cache... done.\r\nSetting up libxft2:amd64 (2.3.4-1) ...\r\nSetting up libncurses-dev:amd64 (6.3-2ubuntu0.1) ...\r\nSetting up libdrm-nouveau2:amd64 (2.4.113-2~ubuntu0.22.04.1) ...\r\nSetting up node-end-of-stream (1.4.4+~1.4.1-1) ...\r\nSetting up golang-1.18 (1.18.1-1ubuntu1.2) ...\r\nSetting up node-pump (3.0.0-5) ...\r\nSetting up dconf-gsettings-backend:amd64 (0.40.0-3) ...\r\nSetting up libnode-dev (12.22.9~dfsg-1ubuntu3.6) ...\r\nSetting up libhttp-cookies-perl (6.10-1) ...\r\nSetting up golang-go:amd64 (2:1.18~0ubuntu2) ...\r\nSetting up libdrm-radeon1:amd64 (2.4.113-2~ubuntu0.22.04.1) ...\r\nSetting up libhtml-tree-perl (5.07-2) ...\r\nSetting up libpango-1.0-0:amd64 (1.50.6+ds-2ubuntu1) ...\r\nSetting up libdrm-intel1:amd64 (2.4.113-2~ubuntu0.22.04.1) ...\r\nSetting up libgl1-mesa-dri:amd64 (23.2.1-1ubuntu3.1~22.04.3) ...\r\nSetting up libobjc-11-dev:amd64 (11.4.0-1ubuntu1~22.04) ...\r\nSetting up libhtml-format-perl (2.12-1.1) ...\r\nSetting up binutils (2.38-4ubuntu2.8) ...\r\nSetting up nodejs (12.22.9~dfsg-1ubuntu3.6) ...\r\nupdate-alternatives: using /usr/bin/nodejs to provide /usr/bin/js (js) in auto mode\r\nupdate-alternatives: warning: skip creation of /usr/share/man/man1/js.1.gz because associated file /usr/share/man/man1/nodejs.1.gz (of link group js) doesn't exist\r\nSetting up node-abab (2.0.5-2) ...\r\nSetting up libcairo2:amd64 (1.16.0-5ubuntu2) ...\r\nSetting up node-argparse (2.0.1-2) ...\r\nSetting up dpkg-dev (1.21.1ubuntu2.3) ...\r\nSetting up node-clone (2.1.2-3) ...\r\nSetting up node-mime (3.0.0+dfsg+~cs3.96.1-1) ...\r\nSetting up node-source-map-support (0.5.21+ds+~0.5.4-1) ...\r\nSetting up node-spdx-correct (3.1.1-2) ...\r\nSetting up libxml2-dev:amd64 (2.9.13+dfsg-1ubuntu0.6) ...\r\nSetting up node-combined-stream (1.0.8+~1.0.3-1) ...\r\nSetting up node-unique-filename (1.1.1+ds-1) ...\r\nSetting up libexpat1-dev:amd64 (2.4.7-1ubuntu0.6) ...\r\nSetting up libnet-smtp-ssl-perl (1.04-1) ...\r\nSetting up node-mime-types (2.1.33-1) ...\r\nSetting up node-glob (7.2.1+~cs7.6.15-1) ...\r\nSetting up libmailtools-perl (2.21-1) ...\r\nSetting up node-get-stream (6.0.1-1) ...\r\nSetting up node-lcov-parse (1.0.0+20170612git80d039574ed9-5) ...\r\nSetting up node-cssom (0.4.4-3) ...\r\nSetting up libgd3:amd64 (2.3.0-2ubuntu2.3) ...\r\nSetting up node-form-data (3.0.1-1) ...\r\nSetting up node-strip-ansi (6.0.1-1) ...\r\nSetting up node-chalk (4.1.2-1) ...\r\nSetting up node-which (2.0.2+~cs1.3.2-2) ...\r\nSetting up libxt6:amd64 (1:1.2.1-1) ...\r\nSetting up node-typedarray-to-buffer (4.0.0-2) ...\r\nSetting up node-punycode (2.1.1-5) ...\r\nSetting up libstdc++-11-dev:amd64 (11.4.0-1ubuntu1~22.04) ...\r\nSetting up libcups2:amd64 (2.4.1op1-1ubuntu4.11) ...\r\nSetting up node-defaults (1.0.3+~1.0.3-1) ...\r\nSetting up zlib1g-dev:amd64 (1:1.2.11.dfsg-2ubuntu9.2) ...\r\nSetting up libhttp-daemon-perl (6.13-1ubuntu0.1) ...\r\nSetting up gcc-11 (11.4.0-1ubuntu1~22.04) ...\r\nSetting up node-graceful-fs (4.2.4+repack-1) ...\r\nSetting up node-minipass (3.1.6+~cs8.7.18-1) ...\r\nSetting up node-aproba (2.0.0-2) ...\r\nSetting up node-esprima (4.0.1+ds+~4.0.3-2) ...\r\nSetting up libgdk-pixbuf-2.0-0:amd64 (2.42.8+dfsg-1ubuntu0.3) ...\r\nSetting up libcairo-gobject2:amd64 (1.16.0-5ubuntu2) ...\r\nSetting up node-mkdirp (1.0.4+~1.0.2-1) ...\r\nSetting up node-run-queue (2.0.0-2) ...\r\nSetting up node-opener (1.5.2+~1.4.0-1) ...\r\nSetting up node-archy (1.0.0-4) ...\r\nSetting up libpangoft2-1.0-0:amd64 (1.50.6+ds-2ubuntu1) ...\r\nSetting up node-js-yaml (4.1.0+dfsg+~4.0.5-6) ...\r\nSetting up golang-doc (2:1.18~0ubuntu2) ...\r\nSetting up node-nopt (5.0.0-2) ...\r\nSetting up libgtk-3-common (3.24.33-1ubuntu2.2) ...\r\nSetting up libpangocairo-1.0-0:amd64 (1.50.6+ds-2ubuntu1) ...\r\nSetting up llvm-14-dev (1:14.0.0-1ubuntu1.1) ...\r\nSetting up node-ms (2.1.3+~cs0.7.31-2) ...\r\nSetting up libc-devtools (2.35-0ubuntu3.9) ...\r\nSetting up node-rimraf (3.0.2-1) ...\r\nSetting up node-semver (7.3.5+~7.3.8-1) ...\r\nSetting up node-fs-write-stream-atomic (1.0.10-5) ...\r\nSetting up node-builtins (4.0.0-1) ...\r\nSetting up gsettings-desktop-schemas (42.0-1ubuntu1) ...\r\nSetting up node-validate-npm-package-license (3.0.4-2) ...\r\nSetting up libgl1-amber-dri:amd64 (21.3.9-0ubuntu1~22.04.1) ...\r\nSetting up node-colors (1.4.0-3) ...\r\nSetting up libtinfo-dev:amd64 (6.3-2ubuntu0.1) ...\r\nSetting up node-log-driver (1.2.7+git+20180219+bba1761737-7) ...\r\nSetting up gtk-update-icon-cache (3.24.33-1ubuntu2.2) ...\r\nSetting up golang:amd64 (2:1.18~0ubuntu2) ...\r\nSetting up node-ssri (8.0.1-2) ...\r\nSetting up node-object-assign (4.1.1-6) ...\r\nSetting up libxmu6:amd64 (2:1.1.3-3) ...\r\nSetting up libglx-mesa0:amd64 (23.2.1-1ubuntu3.1~22.04.3) ...\r\nSetting up clang-14 (1:14.0.0-1ubuntu1.1) ...\r\nSetting up libglx0:amd64 (1.4.0-1) ...\r\nSetting up node-write-file-atomic (3.0.3+~3.0.2-1) ...\r\nSetting up node-psl (1.8.0+ds-6) ...\r\nSetting up g++-11 (11.4.0-1ubuntu1~22.04) ...\r\nSetting up node-copy-concurrently (1.0.5-8) ...\r\nSetting up node-stack-utils (2.0.5+~2.0.1-1) ...\r\nSetting up node-move-concurrently (1.0.1-4) ...\r\nSetting up node-json-buffer (3.0.1-1) ...\r\nSetting up node-console-control-strings (1.1.0-2) ...\r\nSetting up node-debug (4.3.2+~cs4.1.7-1) ...\r\nSetting up libxaw7:amd64 (2:1.0.14-1) ...\r\nSetting up node-events (3.3.0+~3.0.0-2) ...\r\nSetting up node-agent-base (6.0.2+~cs5.4.2-1) ...\r\nSetting up x11-xserver-utils (7.7+9build1) ...\r\nSetting up gcc (4:11.2.0-1ubuntu1) ...\r\nSetting up node-validate-npm-package-name (3.0.0-4) ...\r\nSetting up librsvg2-2:amd64 (2.52.5+dfsg-3ubuntu0.2) ...\r\nSetting up node-wcwidth.js (1.0.2-1) ...\r\nSetting up node-cssstyle (2.3.0-2) ...\r\nSetting up libgl1:amd64 (1.4.0-1) ...\r\nSetting up node-normalize-package-data (3.0.3+~2.4.1-1) ...\r\nSetting up libnotify4:amd64 (0.7.9-3ubuntu5.22.04.1) ...\r\nSetting up clang (1:14.0-55~exp2) ...\r\nSetting up node-got (11.8.3+~cs58.7.37-1) ...\r\nSetting up libpython3.10-dev:amd64 (3.10.12-1~22.04.9) ...\r\nSetting up librsvg2-common:amd64 (2.52.5+dfsg-3ubuntu0.2) ...\r\nSetting up libnotify-bin (0.7.9-3ubuntu5.22.04.1) ...\r\nSetting up node-tap-parser (7.0.0+ds1-6) ...\r\nSetting up python3.10-dev (3.10.12-1~22.04.9) ...\r\nSetting up g++ (4:11.2.0-1ubuntu1) ...\r\nupdate-alternatives: using /usr/bin/g++ to provide /usr/bin/c++ (c++) in auto mode\r\nupdate-alternatives: warning: skip creation of /usr/share/man/man1/c++.1.gz because associated file /usr/share/man/man1/g++.1.gz (of link group c++) doesn't exist\r\nSetting up node-tar (6.1.11+ds1+~cs6.0.6-1) ...\r\nSetting up libgdk-pixbuf2.0-bin (2.42.8+dfsg-1ubuntu0.3) ...\r\nSetting up x11-utils (7.7+5build2) ...\r\nSetting up node-tough-cookie (4.0.0-2) ...\r\nSetting up build-essential (12.9ubuntu3) ...\r\nSetting up node-npm-package-arg (8.1.5-1) ...\r\nSetting up node-https-proxy-agent (5.0.0+~cs8.0.0-3) ...\r\nSetting up node-string-width (4.2.3+~cs13.2.3-1) ...\r\nSetting up node-cacache (15.0.5+~cs13.9.21-3) ...\r\nSetting up libpython3-dev:amd64 (3.10.6-1~22.04.1) ...\r\nSetting up node-columnify (1.5.4+~1.5.1-1) ...\r\nSetting up node-read-package-json (4.1.1-1) ...\r\nSetting up node-growl (1.10.5-4) ...\r\nSetting up node-tap-mocha-reporter (3.0.7+ds-2) ...\r\nSetting up node-ws (8.5.0+~cs13.3.3-2) ...\r\nSetting up python3-dev (3.10.6-1~22.04.1) ...\r\nSetting up node-cli-table (0.3.11+~cs0.13.3-1) ...\r\nSetting up node-jsdom (19.0.0+~cs90.11.27-1) ...\r\nSetting up node-wide-align (1.1.3-4) ...\r\nSetting up node-tap (12.0.1+ds-4) ...\r\nSetting up node-fetch (2.6.7+~2.5.12-1) ...\r\nSetting up node-gauge (4.0.2-1) ...\r\nSetting up node-npmlog (6.0.1+~4.1.4-1) ...\r\nSetting up node-coveralls (3.1.1-1) ...\r\nSetting up node-gyp (8.4.1-1) ...\r\nSetting up npm (8.5.1~ds-1) ...\r\nSetting up adwaita-icon-theme (41.0-1ubuntu1) ...\r\nupdate-alternatives: using /usr/share/icons/Adwaita/cursor.theme to provide /usr/share/icons/default/index.theme (x-cursor-theme) in auto mode\r\nSetting up liblwp-protocol-https-perl (6.10-1) ...\r\nSetting up libwww-perl (6.61-1) ...\r\nSetting up humanity-icon-theme (0.6.16) ...\r\nSetting up libxml-parser-perl:amd64 (2.46-3build1) ...\r\nSetting up ubuntu-mono (20.10-0ubuntu2) ...\r\nSetting up libxml-twig-perl (1:3.52-1) ...\r\nSetting up libnet-dbus-perl (1.2.0-1build3) ...\r\nProcessing triggers for libglib2.0-0:amd64 (2.72.4-0ubuntu2.4) ...\r\nSetting up libgtk-3-0:amd64 (3.24.33-1ubuntu2.2) ...\r\nProcessing triggers for libc-bin (2.35-0ubuntu3.9) ...\r\nSetting up libgtk-3-bin (3.24.33-1ubuntu2.2) ...\r\nSetting up libvte-2.91-0:amd64 (0.68.0-1ubuntu0.1) ...\r\nSetting up libvted-3-0:amd64 (3.10.0-1ubuntu1) ...\r\nSetting up at-spi2-core (2.44.0-3) ...\r\nSetting up libgtkd-3-0:amd64 (3.10.0-1ubuntu1) ...\r\nSetting up tilix (1.9.4-2build1) ...\r\nupdate-alternatives: using /usr/bin/tilix.wrapper to provide /usr/bin/x-terminal-emulator (x-terminal-emulator) in auto mode\r\nupdate-alternatives: warning: skip creation of /usr/share/man/man1/x-terminal-emulator.1.gz because associated file /usr/share/man/man1/tilix.1.gz (of link group x-terminal-emulator) doesn't exist\r\nProcessing triggers for libgdk-pixbuf-2.0-0:amd64 (2.42.8+dfsg-1ubuntu0.3) ...\r\nProcessing triggers for libc-bin (2.35-0ubuntu3.9) ...\r\n=== Installing Bazel ===\nnpm ERR! code EACCES\nnpm ERR! syscall mkdir\nnpm ERR! path /usr/local/lib/node_modules\nnpm ERR! errno -13\nnpm ERR! Error: EACCES: permission denied, mkdir '/usr/local/lib/node_modules'\nnpm ERR!  [Error: EACCES: permission denied, mkdir '/usr/local/lib/node_modules'] {\nnpm ERR!   errno: -13,\nnpm ERR!   code: 'EACCES',\nnpm ERR!   syscall: 'mkdir',\nnpm ERR!   path: '/usr/local/lib/node_modules'\nnpm ERR! }\nnpm ERR! \nnpm ERR! The operation was rejected by your operating system.\nnpm ERR! It is likely you do not have the permissions to access this file as the current user\nnpm ERR! \nnpm ERR! If you believe this might be a permissions issue, please double-check the\nnpm ERR! permissions of the file and its containing directories, or try running\nnpm ERR! the command again as root/Administrator.\n\nnpm ERR! A complete log of this run can be found in:\nnpm ERR!     /home/<USER>/.npm/_logs/2025-04-24T02_15_48_560Z-debug-0.log", "status": "FAILED", "exit_code": 243}, "test_results": [{"command": "bazel version", "output": "/tmp/augment-setup-DQjnts/combined-script.sh: line 10: bazel: command not found", "status": "FAILED", "exit_code": 127}, {"command": "pnpm --version", "output": "/tmp/augment-setup-DQjnts/combined-script.sh: line 15: pnpm: command not found", "status": "FAILED", "exit_code": 127}, {"command": "python3 --version", "output": "Python 3.10.12", "status": "SUCCESS", "exit_code": 0}, {"command": "go version", "output": "go version go1.18.1 linux/amd64", "status": "SUCCESS", "exit_code": 0}]}