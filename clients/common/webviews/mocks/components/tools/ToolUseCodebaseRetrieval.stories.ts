/* eslint-disable @typescript-eslint/naming-convention */
import type { Meta, StoryObj } from "@storybook/svelte-vite";
import component from "./ToolUseStory.svelte";
import ToolUse from "$common-webviews/src/apps/chat/components/conversation/blocks/tools/ToolUse.svelte";
import { SidecarToolType } from "@augment-internal/sidecar-libs/src/tools/sidecar-tools/sidecar-tool-types";

const meta = {
  title: "components/ToolUse",
  component,
} satisfies Meta<component>;

export default meta;

type Story = StoryObj<typeof ToolUse>;

export const ToolUseRetrieval = {
  name: "ToolUse Codebase Retrieval",
  render(props) {
    return { Component: ToolUse, props } as any;
  },
  args: {
    toolUse: {
      tool_use_id: "1",
      tool_name: SidecarToolType.codebaseRetrieval,
      input_json: '{"query": "hello world"}',
    },
    toolUseState: { phase: 5 },
    isLastTurn: true,
    requestId: "1",
  } as any,
} satisfies Story;

export const ToolUseRetrievalLong = {
  name: "ToolUse Codebase Retrieval - Long",
  render(props) {
    return { Component: ToolUse, props } as any;
  },
  args: {
    toolUse: {
      tool_use_id: "2",
      tool_name: SidecarToolType.codebaseRetrieval,
      input_json:
        '{"query": "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum"}',
    },
    toolUseState: { phase: 5 },
    isLastTurn: true,
    requestId: "1",
  } as any,
} satisfies Story;
