/* eslint-disable @typescript-eslint/naming-convention */
import type { Meta, StoryObj } from "@storybook/svelte-vite";
import ToolUse from "$common-webviews/src/apps/chat/components/conversation/blocks/tools/ToolUse.svelte";
import { SidecarToolType } from "@augment-internal/sidecar-libs/src/tools/sidecar-tools/sidecar-tool-types";
import { LocalToolType } from "$vscode/src/webview-providers/tool-types";
import { ToolUsePhase } from "$common-webviews/src/apps/chat/types/tool-use-state";

const meta = {
  title: "components/ToolUse/Approval",
  parameters: {
    docs: {
      description: {
        component:
          "Tool components showing the ToolApprovalPanel when in runnable state (phase 3). This demonstrates the approve/skip functionality that appears for tools requiring user permission.",
      },
    },
  },
} satisfies Meta<typeof ToolUse>;

export default meta;

type Story = StoryObj<typeof ToolUse>;

export const ShellCommandApproval = {
  name: "Shell Command - Awaiting Approval",
  parameters: {
    docs: {
      description: {
        story:
          "A shell command waiting for user approval. Shows the ToolApprovalPanel with Run and Skip buttons.",
      },
    },
  },
  render(props) {
    return { Component: ToolUse, props } as any;
  },
  args: {
    toolUse: {
      tool_use_id: "shell-approval-example",
      tool_name: SidecarToolType.shell,
      input_json: JSON.stringify({
        command: "rm -rf node_modules && npm install",
        cwd: "/Users/<USER>/project",
      }),
    },
    toolUseState: { phase: ToolUsePhase.runnable },
    isLastTurn: true,
    requestId: "shell-approval-req",
  } as any,
} satisfies Story;

export const WebFetchApproval = {
  name: "Web Fetch - Awaiting Approval",
  parameters: {
    docs: {
      description: {
        story:
          "A web fetch tool waiting for user approval. Demonstrates that the approval panel now works for web fetch tools.",
      },
    },
  },
  render(props) {
    return { Component: ToolUse, props } as any;
  },
  args: {
    toolUse: {
      tool_use_id: "web-fetch-approval",
      tool_name: SidecarToolType.webFetch,
      input_json: JSON.stringify({
        url: "https://api.github.com/repos/microsoft/vscode/releases/latest",
      }),
    },
    toolUseState: { phase: ToolUsePhase.runnable },
    isLastTurn: true,
    requestId: "web-fetch-approval-req",
  } as any,
} satisfies Story;

export const GitHubAPIApproval = {
  name: "GitHub API - Awaiting Approval",
  parameters: {
    docs: {
      description: {
        story:
          "A GitHub API tool waiting for user approval. Shows that git tools now have working approval functionality.",
      },
    },
  },
  render(props) {
    return { Component: ToolUse, props } as any;
  },
  args: {
    toolUse: {
      tool_use_id: "github-approval",
      tool_name: "github-api",
      input_json: JSON.stringify({
        path: "/repos/microsoft/vscode/issues",
        method: "GET",
        data: {
          state: "open",
          labels: "bug",
          per_page: 10,
        },
      }),
    },
    toolUseState: { phase: ToolUsePhase.runnable },
    isLastTurn: true,
    requestId: "github-approval-req",
  } as any,
} satisfies Story;

export const FileOperationApproval = {
  name: "File Operation - Awaiting Approval",
  parameters: {
    docs: {
      description: {
        story:
          "A file removal operation waiting for user approval. Shows approval panel for potentially destructive operations.",
      },
    },
  },
  render(props) {
    return { Component: ToolUse, props } as any;
  },
  args: {
    toolUse: {
      tool_use_id: "file-removal-approval",
      tool_name: SidecarToolType.removeFiles,
      input_json: JSON.stringify({
        file_paths: [
          "/Users/<USER>/project/temp/cache.json",
          "/Users/<USER>/project/temp/logs/debug.log",
          "/Users/<USER>/project/temp/build-artifacts/",
        ],
      }),
    },
    toolUseState: { phase: ToolUsePhase.runnable },
    isLastTurn: true,
    requestId: "file-removal-req",
  } as any,
} satisfies Story;

export const CodeEditApproval = {
  name: "Code Edit - Awaiting Approval",
  parameters: {
    docs: {
      description: {
        story:
          "A string replacement editor operation waiting for approval. Shows approval for code modification tools.",
      },
    },
  },
  render(props) {
    return { Component: ToolUse, props } as any;
  },
  args: {
    toolUse: {
      tool_use_id: "code-edit-approval",
      tool_name: LocalToolType.strReplaceEditor,
      input_json: JSON.stringify({
        command: "str_replace",
        path: "/Users/<USER>/project/src/components/Button.tsx",
        old_str: "export default function Button({ children, onClick }: ButtonProps) {",
        new_str:
          "export default function Button({ children, onClick, disabled = false }: ButtonProps) {",
      }),
    },
    toolUseState: { phase: ToolUsePhase.runnable },
    isLastTurn: true,
    requestId: "code-edit-req",
  } as any,
} satisfies Story;

export const TaskManagementApproval = {
  name: "Task Management - Awaiting Approval",
  parameters: {
    docs: {
      description: {
        story:
          "A task creation operation waiting for approval. Shows approval panel for task management tools.",
      },
    },
  },
  render(props) {
    return { Component: ToolUse, props } as any;
  },
  args: {
    toolUse: {
      tool_use_id: "task-creation-approval",
      tool_name: SidecarToolType.addTasks,
      input_json: JSON.stringify({
        tasks: [
          {
            name: "Implement user authentication",
            description: "Add login/logout functionality with JWT tokens",
          },
          {
            name: "Create user dashboard",
            description: "Build main dashboard with user stats and recent activity",
            parent_task_id: null,
          },
        ],
      }),
    },
    toolUseState: { phase: ToolUsePhase.runnable },
    isLastTurn: true,
    requestId: "task-creation-req",
  } as any,
} satisfies Story;

export const MultipleToolsApproval = {
  name: "Multiple Tools - Mixed States",
  parameters: {
    docs: {
      description: {
        story:
          "Shows multiple tools in different states, with one awaiting approval to demonstrate the approval panel only appears for runnable tools.",
      },
    },
  },
  render(props) {
    return { Component: ToolUse, props } as any;
  },
  args: {
    toolUse: {
      tool_use_id: "dangerous-operation",
      tool_name: SidecarToolType.shell,
      input_json: JSON.stringify({
        command: "sudo rm -rf /tmp/build-cache && sudo docker system prune -af",
        cwd: "/Users/<USER>/project",
      }),
    },
    toolUseState: { phase: ToolUsePhase.runnable },
    isLastTurn: true,
    requestId: "dangerous-op-req",
  } as any,
} satisfies Story;
