{"tool_use_id": "toolu_vrtx_016VqKEnR4PF6ykiaNUVqpf6", "tool_name": "setup-script", "input_json": "{\"script_content\": \"#!/bin/bash\\nset -e\\n\\necho \\\"Starting Augment development environment setup...\\\"\\n\\n# Function to check if a command exists\\ncommand_exists() {\\n    command -v \\\"$1\\\" >/dev/null 2>&1\\n}\\n\\n# Function to install system dependencies\\ninstall_system_deps() {\\n    echo \\\"Installing system dependencies...\\\"\\n    \\n    if command_exists apt-get; then\\n        sudo apt-get update\\n        sudo apt-get install -y curl git build-essential python3 python3-pip python3-venv \\\\\\n            clang cmake pkg-config libstdc++-9-dev patchelf wget\\n    elif command_exists yum; then\\n        sudo yum update -y\\n        sudo yum install -y curl git gcc gcc-c++ make python3 python3-pip python3-devel \\\\\\n            clang cmake patchelf wget\\n    elif command_exists brew; then\\n        brew update\\n        brew install curl git python@3.11 cmake bazel pnpm clang patchelf wget\\n    else\\n        echo \\\"Unsupported package manager. Please install dependencies manually.\\\"\\n        exit 1\\n    fi\\n    \\n    echo \\\"System dependencies installed.\\\"\\n}\\n\\n# Function to set up Python environment\\nsetup_python_env() {\\n    echo \\\"Setting up Python environment...\\\"\\n    \\n    # Create a virtual environment if it doesn't exist\\n    if [ ! -d \\\"venv\\\" ]; then\\n        python3 -m venv venv\\n    fi\\n    \\n    # Activate the virtual environment\\n    source venv/bin/activate\\n    \\n    # Upgrade pip\\n    pip install --upgrade pip\\n    \\n    # Install basic Python packages needed for build\\n    pip install wheel setuptools\\n\\n    # Install Python dependencies from requirements.txt\\n    if [ -f \\\"tools/python_deps/requirements.txt\\\" ]; then\\n        echo \\\"Installing Python dependencies from tools/python_deps/requirements.txt...\\\"\\n        # Install only essential packages to avoid compatibility issues\\n        grep -E \\\"^(numpy|protobuf|pybind11|pytest)\\\" tools/python_deps/requirements.txt > essential_requirements.txt\\n        pip install -r essential_requirements.txt\\n        rm essential_requirements.txt\\n    elif [ -f \\\"research/requirements.txt\\\" ]; then\\n        echo \\\"Installing Python dependencies from research/requirements.txt...\\\"\\n        # Install only essential packages to avoid compatibility issues\\n        grep -E \\\"^(numpy|protobuf|pybind11|pytest)\\\" research/requirements.txt > essential_requirements.txt\\n        pip install -r essential_requirements.txt\\n        rm essential_requirements.txt\\n    fi\\n    \\n    echo \\\"Python environment set up.\\\"\\n}\\n\\n# Function to set up Node.js and pnpm\\nsetup_node_env() {\\n    echo \\\"Setting up Node.js environment...\\\"\\n    \\n    # Install Node.js if not installed\\n    if ! command_exists node; then\\n        echo \\\"Installing Node.js...\\\"\\n        curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -\\n        sudo apt-get install -y nodejs\\n    fi\\n    \\n    # Install pnpm if not installed\\n    if ! command_exists pnpm; then\\n        echo \\\"Installing pnpm...\\\"\\n        # Use npm with user permissions to avoid permission issues\\n        mkdir -p ~/.npm-global\\n        npm config set prefix '~/.npm-global'\\n        export PATH=~/.npm-global/bin:$PATH\\n        echo 'export PATH=~/.npm-global/bin:$PATH' >> ~/.bashrc\\n        \\n        # Install pnpm\\n        npm install -g pnpm@9\\n    fi\\n    \\n    # Install Node.js dependencies if package.json exists\\n    if [ -f \\\"package.json\\\" ]; then\\n        echo \\\"Installing Node.js dependencies...\\\"\\n        pnpm install\\n    fi\\n    \\n    echo \\\"Node.js environment set up.\\\"\\n}\\n\\n# Function to set up Bazel\\nsetup_bazel() {\\n    echo \\\"Setting up Bazel...\\\"\\n    \\n    # Install Bazel if not installed\\n    if ! command_exists bazel; then\\n        echo \\\"Installing Bazel...\\\"\\n        \\n        # Create directory for Bazelisk\\n        mkdir -p ~/.local/bin\\n        export PATH=~/.local/bin:$PATH\\n        echo 'export PATH=~/.local/bin:$PATH' >> ~/.bashrc\\n        \\n        # Direct installation of Bazelisk\\n        BAZELISK_VERSION=\\\"v1.25.0\\\"\\n        BAZELISK_URL=\\\"https://github.com/bazelbuild/bazelisk/releases/download/${BAZELISK_VERSION}/bazelisk-linux-amd64\\\"\\n        \\n        curl -Lo ~/.local/bin/bazel \\\"${BAZELISK_URL}\\\"\\n        chmod +x ~/.local/bin/bazel\\n    fi\\n    \\n    echo \\\"Bazel set up.\\\"\\n}\\n\\n# Function to run initialization scripts\\nrun_init_scripts() {\\n    echo \\\"Running initialization scripts...\\\"\\n    \\n    # Run base installation script if it exists\\n    if [ -f \\\"base/install.sh\\\" ]; then\\n        echo \\\"Running base/install.sh...\\\"\\n        chmod +x base/install.sh\\n        # Try to run the script, but don't fail if it doesn't work\\n        bash base/install.sh || echo \\\"Warning: base/install.sh failed, but continuing setup\\\"\\n    fi\\n    \\n    echo \\\"Initialization scripts completed.\\\"\\n}\\n\\n# Function to configure Go environment if needed\\nsetup_go_env() {\\n    echo \\\"Setting up Go environment...\\\"\\n    \\n    # Check if go.mod exists\\n    if [ -f \\\"go.mod\\\" ]; then\\n        # Install Go if not installed\\n        if ! command_exists go; then\\n            echo \\\"Installing Go...\\\"\\n            GO_VERSION=\\\"1.23.1\\\"\\n            GO_URL=\\\"https://golang.org/dl/go${GO_VERSION}.linux-amd64.tar.gz\\\"\\n            \\n            wget -q -O go.tar.gz \\\"${GO_URL}\\\"\\n            sudo tar -C /usr/local -xzf go.tar.gz\\n            rm go.tar.gz\\n            \\n            # Add Go to PATH\\n            echo 'export PATH=$PATH:/usr/local/go/bin' >> ~/.bashrc\\n            export PATH=$PATH:/usr/local/go/bin\\n        fi\\n    else\\n        echo \\\"No go.mod found, skipping Go setup.\\\"\\n    fi\\n    \\n    echo \\\"Go environment set up.\\\"\\n}\\n\\n# Function to configure Rust environment if needed\\nsetup_rust_env() {\\n    echo \\\"Setting up Rust environment...\\\"\\n    \\n    # Check if Cargo.toml exists\\n    if [ -f \\\"Cargo.toml\\\" ]; then\\n        # Install Rust if not installed\\n        if ! command_exists rustc; then\\n            echo \\\"Installing Rust...\\\"\\n            curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y\\n            source \\\"$HOME/.cargo/env\\\"\\n        fi\\n    else\\n        echo \\\"No Cargo.toml found, skipping Rust setup.\\\"\\n    fi\\n    \\n    echo \\\"Rust environment set up.\\\"\\n}\\n\\n# Main setup function\\nmain() {\\n    install_system_deps\\n    setup_python_env\\n    setup_node_env\\n    setup_bazel\\n    setup_go_env\\n    setup_rust_env\\n    run_init_scripts\\n    \\n    echo \\\"Development environment setup completed successfully!\\\"\\n    echo \\\"You can now build and run the project.\\\"\\n    echo \\\"To activate the Python virtual environment, run: source venv/bin/activate\\\"\\n}\\n\\n# Run the main setup function\\nmain\", \"test_commands\": [\"python -c 'import sys; print(\\\"Python version:\\\", sys.version)'\", \"node --version\", \"~/.local/bin/bazel --version\"]}"}