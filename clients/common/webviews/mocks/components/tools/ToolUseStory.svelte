<script>
  import ToolUse from "$common-webviews/src/apps/chat/components/conversation/blocks/tools/ToolUse.svelte";
  import Fieldset from "$common-webviews/mocks/storybook/Fieldset.svelte";
  import { LocalToolType, RemoteToolType } from "$vscode/src/webview-providers/tool-types";
  import { SidecarToolType } from "@augment-internal/sidecar-libs/src/tools/sidecar-tools/sidecar-tool-types";
  import {
    LONG_HTML_FILE_CONTENT,
    LONG_JS_FILE_CONTENT,
    LONG_PYTHON_FILE_CONTENT,
  } from "./long_file_content";
  import mockScriptOutput from "./mock-script-output.json";
  import mockSetupScript from "./mock-setup-script.json";
  /* eslint-disable @typescript-eslint/naming-convention */
</script>

<Fieldset title="Sticky Header">
  <h1>Long File Examples</h1>
  <div class="scroll-container">
    <ToolUse
      toolUse={{
        tool_use_id: "js-file-example",
        tool_name: LocalToolType.saveFile,
        input_json: JSON.stringify({
          file_path: "/path/to/example-component.jsx",
          file_content: LONG_JS_FILE_CONTENT,
        }),
      }}
      toolUseState={{ phase: 5, requestId: "1", toolUseId: "js-file-example" }}
      isLastTurn={true}
      requestId="1"
    />

    <ToolUse
      toolUse={{
        tool_use_id: "python-file-example",
        tool_name: LocalToolType.saveFile,
        input_json: JSON.stringify({
          file_path: "/path/to/data_processor.py",
          file_content: LONG_PYTHON_FILE_CONTENT,
        }),
      }}
      toolUseState={{ phase: 5, requestId: "1", toolUseId: "python-file-example" }}
      isLastTurn={true}
      requestId="1"
    />

    <ToolUse
      toolUse={{
        tool_use_id: "html-file-example",
        tool_name: LocalToolType.saveFile,
        input_json: JSON.stringify({
          file_path: "/path/to/example.html",
          file_content: LONG_HTML_FILE_CONTENT,
        }),
      }}
      toolUseState={{ phase: 5, requestId: "1", toolUseId: "html-file-example" }}
      isLastTurn={true}
      requestId="1"
    />
  </div>
</Fieldset>

<div class="l-tooluse-list">
  <h1>Long File Examples</h1>
  <ToolUse
    toolUse={{
      tool_use_id: "js-file-example",
      tool_name: LocalToolType.saveFile,
      input_json: JSON.stringify({
        file_path: "/path/to/example-component.jsx",
        file_content: LONG_JS_FILE_CONTENT,
      }),
    }}
    toolUseState={{ phase: 5, requestId: "1", toolUseId: "js-file-example" }}
    isLastTurn={true}
    requestId="1"
  />

  <ToolUse
    toolUse={{
      tool_use_id: "python-file-example",
      tool_name: LocalToolType.saveFile,
      input_json: JSON.stringify({
        file_path: "/path/to/data_processor.py",
        file_content: LONG_PYTHON_FILE_CONTENT,
      }),
    }}
    toolUseState={{ phase: 5, requestId: "1", toolUseId: "python-file-example" }}
    isLastTurn={true}
    requestId="1"
  />

  <ToolUse
    toolUse={{
      tool_use_id: "html-file-example",
      tool_name: LocalToolType.saveFile,
      input_json: JSON.stringify({
        file_path: "/path/to/example.html",
        file_content: LONG_HTML_FILE_CONTENT,
      }),
    }}
    toolUseState={{ phase: 5, requestId: "1", toolUseId: "html-file-example" }}
    isLastTurn={true}
    requestId="1"
  />

  <h1>Other Tool Examples</h1>
  <ToolUse
    toolUse={{
      tool_use_id: "2",
      tool_name: SidecarToolType.shell,
      input_json: '{"command": "ls"}',
    }}
    toolUseState={{ phase: 5, requestId: "1", toolUseId: "2" }}
    isLastTurn={true}
    requestId="1"
  />
  <ToolUse
    toolUse={{
      tool_use_id: "2",
      tool_name: SidecarToolType.shell,
      input_json: '{"command_type": "simple", "simple_command": ["ls", "-l"]}',
    }}
    toolUseState={{ phase: 5, requestId: "1", toolUseId: "2" }}
    isLastTurn={true}
    requestId="1"
  />
  <ToolUse
    toolUse={{
      tool_use_id: "update-single-1",
      tool_name: SidecarToolType.updateTasks,
      input_json: JSON.stringify({
        task_id: "task-123",
        state: "COMPLETE",
        name: "Updated task name",
        description: "Updated task description",
      }),
    }}
    toolUseState={{ phase: 5, requestId: "1", toolUseId: "python-file-example" }}
    isLastTurn={true}
    requestId="1"
  />
  <ToolUse
    toolUse={{
      tool_use_id: "2",
      tool_name: SidecarToolType.shell,
      input_json: '{"command_type": "complex", "complex_command": "ls -l | grep foo | wc -l"}',
    }}
    toolUseState={{ phase: 5, requestId: "1", toolUseId: "2" }}
    isLastTurn={true}
    requestId="1"
  />
  <ToolUse
    toolUse={{
      tool_use_id: "4",
      tool_name: LocalToolType.readFile,
      input_json: '{"file_path": "/path/to/file"}',
    }}
    toolUseState={{ phase: 5, requestId: "1", toolUseId: "4" }}
    isLastTurn={true}
    requestId="1"
  />
  <ToolUse
    toolUse={{
      tool_use_id: "5",
      tool_name: LocalToolType.saveFile,
      input_json: '{"file_path": "/path/to/file"}',
    }}
    toolUseState={{ phase: 5, requestId: "1", toolUseId: "5" }}
    isLastTurn={true}
    requestId="1"
  />
  <ToolUse
    toolUse={{
      tool_use_id: "6",
      tool_name: LocalToolType.editFile,
      input_json: '{"file_path": "/path/to/file", "edit_summary": "summary of the edit"}',
    }}
    toolUseState={{ phase: 5, requestId: "1", toolUseId: "6" }}
    isLastTurn={true}
    requestId="1"
  />
  <ToolUse
    toolUse={{
      tool_use_id: "7",
      tool_name: SidecarToolType.codebaseRetrieval,
      input_json: '{"query": "hello world"}',
    }}
    toolUseState={{ phase: 5, requestId: "1", toolUseId: "7" }}
    isLastTurn={true}
    requestId="1"
  />
  <ToolUse
    toolUse={{
      tool_use_id: "8",
      tool_name: SidecarToolType.remember,
      input_json: '{"memory": "remember this memory"}',
    }}
    toolUseState={{ phase: 5, requestId: "1", toolUseId: "8" }}
    isLastTurn={true}
    requestId="1"
  />
  <ToolUse
    toolUse={{
      tool_use_id: "8",
      tool_name: "Made up tool",
      input_json: '{"hello": "world"}',
    }}
    toolUseState={{ phase: 5, requestId: "1", toolUseId: "9" }}
    isLastTurn={true}
    requestId="1"
  />
  <ToolUse
    toolUse={{
      tool_use_id: "2",
      tool_name: LocalToolType.diagnostics,
      input_json: '{"diagnostics": "Show diagnostics"}',
    }}
    toolUseState={{
      phase: 5,
      requestId: "1",
      toolUseId: "2",
      result: { text: "Diagnostics: No issues found", isError: false },
    }}
    isLastTurn={true}
    requestId="1"
  />
  <ToolUse
    toolUse={{
      tool_use_id: "6.5",
      tool_name: LocalToolType.openBrowser,
      input_json: '{"url": "https://example.com"}',
    }}
    toolUseState={{ phase: 5, requestId: "1", toolUseId: "6" }}
    isLastTurn={true}
    requestId="1"
  />

  <h1>Sidecar Tools</h1>
  <ToolUse
    toolUse={{
      tool_use_id: "3",
      tool_name: SidecarToolType.webFetch,
      input_json: '{"url": "https://example.com"}',
    }}
    toolUseState={{ phase: 5, requestId: "1", toolUseId: "3" }}
    isLastTurn={true}
    requestId="1"
  />

  <ToolUse
    toolUse={{
      tool_use_id: "2",
      tool_name: SidecarToolType.shell,
      input_json: '{"command_type": "simple", "simple_command": ["ls", "-l"]}',
    }}
    toolUseState={{ phase: 5, requestId: "1", toolUseId: "2" }}
    isLastTurn={true}
    requestId="1"
  />

  <h1>Setup Script Tool</h1>
  <ToolUse
    toolUse={mockSetupScript}
    toolUseState={{
      phase: 5,
      requestId: "1",
      toolUseId: mockSetupScript.tool_use_id,
      result: {
        text: JSON.stringify(mockScriptOutput),
        isError: false,
      },
    }}
    isLastTurn={true}
    requestId="1"
  />

  <h1>Remote Tools</h1>
  <ToolUse
    toolUse={{
      tool_use_id: "1",
      tool_name: RemoteToolType.webSearch,
      input_json: '{"query": "hello world"}',
    }}
    toolUseState={{ phase: 5, requestId: "1", toolUseId: "1" }}
    isLastTurn={true}
    requestId="1"
  />

  <ToolUse
    toolUse={{
      tool_use_id: "mcp-example",
      tool_name: "git",
      mcp_server_name: "MCP Server",
      mcp_tool_name: "MCP Example",
      input_json: '{"args": ["status"]}',
    }}
    toolUseState={{ phase: 5, requestId: "1", toolUseId: "mcp-example" }}
    isLastTurn={true}
    requestId="1"
  />
  <ToolUse
    toolUse={{
      tool_use_id: "2",
      tool_name: RemoteToolType.gitHub,
      input_json: '{"path": "/path/to/repo", "q": "is:pr is:open author:@me"}',
    }}
    toolUseState={{ phase: 5, requestId: "1", toolUseId: "2" }}
    isLastTurn={true}
    requestId="1"
  />
  <ToolUse
    toolUse={{
      tool_use_id: "2",
      tool_name: RemoteToolType.linear,
      input_json: '{"test": "What is my most recent ticket?"}',
    }}
    toolUseState={{ phase: 5, requestId: "1", toolUseId: "2" }}
    isLastTurn={true}
    requestId="1"
  />
  <ToolUse
    toolUse={{
      tool_use_id: "2",
      tool_name: RemoteToolType.notion,
      input_json:
        '{"method": "search_pages", "params": {"query": "How do I set up a development VM?"}}',
    }}
    toolUseState={{ phase: 5, requestId: "1", toolUseId: "2" }}
    isLastTurn={true}
    requestId="1"
  />
  <ToolUse
    toolUse={{
      tool_use_id: "17",
      tool_name: RemoteToolType.glean,
      input_json:
        '{\n  "summary": "Searching for information about why Augment is migrating to Orb for billing",\n  "call": {\n    "path": "/search",\n    "method": "POST",\n    "payload": {\n      "query": "why migrate to Orb for billing",\n      "pageSize": 5,\n      "maxSnippetSize": 500\n    }\n  }\n}',
    }}
    toolUseState={{ phase: 5, requestId: "1", toolUseId: "17" }}
    isLastTurn={true}
    requestId="1"
  />
  <ToolUse
    toolUse={{
      tool_use_id: "24",
      tool_name: "remove-files",
      input_json: '{"file_paths":["deltedfile.txt"]}',
    }}
    toolUseState={{ phase: 5, requestId: "1", toolUseId: "24" }}
    isLastTurn={true}
    requestId="1"
  />
</div>

<style>
  .scroll-container {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-2);
    height: 300px;
    overflow-y: auto;
  }
  .l-tooluse-list {
    display: flex;
    flex-direction: column;
    width: 100%;
    gap: var(--ds-spacing-2);
    padding: 0 var(--ds-spacing-2) var(--ds-spacing-2) var(--ds-spacing-2);
    min-height: 600px;
    overflow-y: scroll;
    resize: both;
  }

  h1 {
    margin-top: var(--ds-spacing-4);
    margin-bottom: var(--ds-spacing-2);
    font-size: var(--ds-font-size-3);
    font-weight: var(--ds-font-weight-bold);
  }

  /* Make the long file example more prominent */
  :global(.l-tooluse-list > :first-child + .c-tool-use) {
    margin-bottom: var(--ds-spacing-4);
  }
</style>
