export const LONG_JS_FILE_CONTENT = `// This is a long JavaScript file for demonstration purposes

// Imports
import React from "react";
import { useState, useEffect } from "react";
import axios from "axios";

/**
 * Example component with many methods
 */
class ExampleComponent {
  /**
   * Method 1 description
   * @param {string} param1 - First parameter
   * @param {number} param2 - Second parameter
   * @returns {boolean} - Return value
   */
  method1(param1, param2) {
    console.log("Method 1 called with " + param1 + " and " + param2);

    // Some complex logic
    if (param1.length > 10) {
      return param2 > 100;
    } else {
      return param1.startsWith("test") && param2 < 50;
    }
  }

  /**
   * Method 2 description
   * @param {string} param1 - First parameter
   * @param {number} param2 - Second parameter
   * @returns {boolean} - Return value
   */
  method2(param1, param2) {
    console.log("Method 2 called with " + param1 + " and " + param2);

    // Some complex logic
    if (param1.length > 10) {
      return param2 > 100;
    } else {
      return param1.startsWith("test") && param2 < 50;
    }
  }

  // ... many more methods would be here in a real long file
}

// React component example
const MyComponent = () => {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        const response = await axios.get("/api/data");
        setData(response.data);
      } catch (error) {
        console.error("Error fetching data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  return (
    <div className="container">
      <h1>Data Display</h1>
      {loading ? (
        <p>Loading...</p>
      ) : (
        <ul>
          {data.map((item) => (
            <li key={item.id}>{item.name}</li>
          ))}
        </ul>
      )}
    </div>
  );
};`;

export const LONG_PYTHON_FILE_CONTENT = `# This is a long Python file for demonstration purposes

import os
import sys
import json
import numpy as np
import pandas as pd
from typing import List, Dict, Optional, Union, Any

class DataProcessor:
    """A class for processing data with many methods."""

    def __init__(self, data_path: str, config: Optional[Dict[str, Any]] = None):
        """Initialize the data processor.

        Args:
            data_path: Path to the data file
            config: Optional configuration dictionary
        """
        self.data_path = data_path
        self.config = config or {}
        self.data = None

    def load_data(self) -> pd.DataFrame:
        """Load data from the specified path."""
        if self.data_path.endswith('.csv'):
            self.data = pd.read_csv(self.data_path)
        elif self.data_path.endswith('.json'):
            self.data = pd.read_json(self.data_path)
        else:
            raise ValueError(f"Unsupported file format: {self.data_path}")
        return self.data

    def preprocess(self) -> pd.DataFrame:
        """Preprocess the loaded data."""
        if self.data is None:
            self.load_data()

        # Fill missing values
        self.data = self.data.fillna(self.config.get('fill_value', 0))

        # Drop duplicates
        if self.config.get('drop_duplicates', True):
            self.data = self.data.drop_duplicates()

        return self.data`;

export const LONG_HTML_FILE_CONTENT = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Long HTML Example</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        header {
            background-color: #f4f4f4;
            padding: 20px;
            margin-bottom: 20px;
            border-radius: 5px;
        }
        nav ul {
            display: flex;
            list-style: none;
            padding: 0;
        }
        nav ul li {
            margin-right: 20px;
        }
        nav ul li a {
            text-decoration: none;
            color: #333;
        }
        section {
            margin-bottom: 30px;
            padding: 20px;
            background-color: #f9f9f9;
            border-radius: 5px;
        }
        footer {
            text-align: center;
            padding: 20px;
            background-color: #f4f4f4;
            margin-top: 30px;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <header>
        <h1>Long HTML Example</h1>
        <nav>
            <ul>
                <li><a href="#section1">Section 1</a></li>
                <li><a href="#section2">Section 2</a></li>
                <li><a href="#section3">Section 3</a></li>
            </ul>
        </nav>
    </header>

    <main>
        <section id="section1">
            <h2>Section 1: Introduction</h2>
            <p>This is a long HTML file example to demonstrate scrolling in the Monaco editor.</p>
            <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit.</p>
        </section>

        <section id="section2">
            <h2>Section 2: Features</h2>
            <p>Here are some features of our product:</p>
            <ul>
                <li>Feature 1: Lorem ipsum dolor sit amet</li>
                <li>Feature 2: Consectetur adipiscing elit</li>
                <li>Feature 3: Nullam auctor, nisl eget ultricies tincidunt</li>
            </ul>
        </section>

        <section id="section3">
            <h2>Section 3: Examples</h2>
            <p>Here are some examples of our product in action:</p>
            <div>
                <h3>Example 1</h3>
                <p>This is an example of our product in action.</p>
            </div>
        </section>
    </main>

    <footer>
        <p>&copy; 2023 Long HTML Example. All rights reserved.</p>
    </footer>
</body>
</html>`;
