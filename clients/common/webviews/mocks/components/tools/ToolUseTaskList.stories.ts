/* eslint-disable @typescript-eslint/naming-convention */
import type { <PERSON><PERSON>, StoryObj } from "@storybook/svelte-vite";
import TaskToolStory from "./TaskToolStory.svelte";
import ToolUse from "$common-webviews/src/apps/chat/components/conversation/blocks/tools/ToolUse.svelte";
import { SidecarToolType } from "@augment-internal/sidecar-libs/src/tools/sidecar-tools/sidecar-tool-types";
import { ToolUsePhase } from "$common-webviews/src/apps/chat/types/tool-use-state";

// Simple mock data - no complex generators needed
/* eslint-disable @typescript-eslint/naming-convention */

type Story = StoryObj<typeof ToolUse>;

// Task Tool Stories
export const ViewTaskList = {
  name: "Task Tool - View Task List",
  render(props) {
    return { Component: ToolUse, props } as any;
  },
  args: {
    toolUse: {
      tool_use_id: "task-view-1",
      tool_name: SidecarToolType.viewTaskList,
      input_json: JSON.stringify({}),
    },
    toolUseState: {
      phase: ToolUsePhase.completed,
      requestId: "task-req-1",
      toolUseId: "task-view-1",
      result: {
        text: `# Current Task List

[ ] UUID:root-1 NAME:Conversation: New Chat DESCRIPTION:Root task for conversation
-[x] UUID:task-1 NAME:Create ViewTaskListToolComponent DESCRIPTION:Create a component for rendering the view_tasklist tool
-[x] UUID:task-2 NAME:Create UpdateTasksToolComponent DESCRIPTION:Create a component for rendering the update_tasks tool
-[ ] UUID:task-3 NAME:Test the components DESCRIPTION:Test the new components to ensure they render correctly

To update this task list, use the update_tasks or reorganize_tasklist tools.`,
        isError: false,
      },
    },
    isLastTurn: true,
    requestId: "task-req-1",
  } as any,
} satisfies Story;

export const UpdateTaskList = {
  name: "Task Tool - Update Task List",
  render(props) {
    return { Component: ToolUse, props } as any;
  },
  args: {
    toolUse: {
      tool_use_id: "task-update-1",
      tool_name: SidecarToolType.reorganizeTaskList,
      input_json: JSON.stringify({
        markdown: `[ ] UUID:root-1 NAME:Conversation: New Chat DESCRIPTION:Root task for conversation
-[x] UUID:task-1 NAME:Create ViewTaskListToolComponent DESCRIPTION:Create a component for rendering the view_tasklist tool
-[x] UUID:task-2 NAME:Create UpdateTasksToolComponent DESCRIPTION:Create a component for rendering the update_tasks tool
-[x] UUID:task-3 NAME:Test the components DESCRIPTION:Test the new components to ensure they render correctly
-[ ] UUID:new-1 NAME:Add error handling DESCRIPTION:Implement proper error boundaries
-[/] UUID:new-2 NAME:Optimize performance DESCRIPTION:Profile and improve load times`,
      }),
    },
    toolUseState: {
      phase: ToolUsePhase.completed,
      requestId: "task-req-2",
      toolUseId: "task-update-1",
      result: {
        text: `Task list updated successfully. Created: 2, Updated: 1, Deleted: 0.

# Task Changes

## Created Tasks

[ ] UUID:new-1 NAME:Add error handling DESCRIPTION:Implement proper error boundaries
[/] UUID:new-2 NAME:Optimize performance DESCRIPTION:Profile and improve load times

## Updated Tasks

[x] UUID:task-3 NAME:Test the components DESCRIPTION:Test the new components to ensure they render correctly

New and Updated Tasks:

## Created Tasks

[ ] UUID:new-1 NAME:Add error handling DESCRIPTION:Implement proper error boundaries
[/] UUID:new-2 NAME:Optimize performance DESCRIPTION:Profile and improve load times

## Updated Tasks

[x] UUID:task-3 NAME:Test the components DESCRIPTION:Test the new components to ensure they render correctly

Remember: When updating the task list in the future:
- Mark tasks as in progress ([/]) when you start working on them
- Mark tasks as completed ([x]) when the user explicitly confirms they are done
- Mark tasks as cancelled ([-]) if they're no longer needed
- Use the correct UUID for existing tasks. Make sure to use the generated UUIDs for new tasks.`,
        isError: false,
      },
    },
    isLastTurn: true,
    requestId: "task-req-2",
  } as any,
} satisfies Story;

export const ViewTaskListLoading = {
  name: "Task Tool - View Task List Loading",
  render(props) {
    return { Component: ToolUse, props } as any;
  },
  args: {
    toolUse: {
      tool_use_id: "task-view-loading",
      tool_name: SidecarToolType.viewTaskList,
      input_json: "{}",
    },
    toolUseState: {
      phase: ToolUsePhase.running,
      requestId: "task-req-loading",
      toolUseId: "task-view-loading",
    },
    isLastTurn: true,
    requestId: "task-req-loading",
  } as any,
} satisfies Story;

export const UpdateTaskListError = {
  name: "Task Tool - Update Task List Error",
  render(props) {
    return { Component: ToolUse, props } as any;
  },
  args: {
    toolUse: {
      tool_use_id: "task-update-error",
      tool_name: SidecarToolType.reorganizeTaskList,
      input_json: JSON.stringify({
        markdown: "Invalid markdown format",
      }),
    },
    toolUseState: {
      phase: ToolUsePhase.completed,
      requestId: "task-req-error",
      toolUseId: "task-update-error",
      result: {
        text: "Failed to parse markdown: Invalid task format. Expected format: [x] UUID:... NAME:... DESCRIPTION:...",
        isError: true,
      },
    },
    isLastTurn: true,
    requestId: "task-req-error",
  } as any,
} satisfies Story;

// Task Tool Stories - Comprehensive showcase
export const TaskToolShowcase = {
  name: "Task Tools - Complete Showcase",
  render() {
    return { Component: TaskToolStory } as any;
  },
  args: {},
} satisfies StoryObj<typeof TaskToolStory>;

export default {
  title: "components/ToolUse/TaskList",
  component: TaskToolStory,
} satisfies Meta<TaskToolStory>;
