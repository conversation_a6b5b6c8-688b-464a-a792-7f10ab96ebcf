/* eslint-disable @typescript-eslint/naming-convention */
import type { Meta, StoryObj } from "@storybook/svelte-vite";
import component from "./ToolUseStory.svelte";

// Simple mock data - no complex generators needed
/* eslint-disable @typescript-eslint/naming-convention */
type DefaultStory = StoryObj<typeof meta>;

// More on writing stories with args: https://storybook.js.org/docs/writing-stories/args
export const Example: DefaultStory = {};

const meta = {
  title: "components/ToolUse",
  component,
} satisfies Meta<component>;

export default meta;
