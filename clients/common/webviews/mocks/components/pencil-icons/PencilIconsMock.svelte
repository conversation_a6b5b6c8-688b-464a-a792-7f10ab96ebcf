<script lang="ts">
  import PencilIcon from "$common-webviews/src/common/components/pencil-icons/PencilIcon.svelte";
  import { UserThemeCategory } from "$common-webviews/src/common/hosts/user-themes/augment-theme-attributes";
  import { ChangeType, SuggestionState } from "$vscode/src/next-edit/next-edit-types";
  export let isDarkMode = false;
  $: themeCategory = isDarkMode ? UserThemeCategory.dark : UserThemeCategory.light;
  export let isMask = false;
</script>

<main class="c-main" data-theme={themeCategory}>
  <fieldset>
    <legend>insertion</legend>
    <div>
      <PencilIcon
        suggestion={{ changeType: ChangeType.insertion }}
        {themeCategory}
        mask={isMask}
      />Insertion
    </div>
  </fieldset>
  <fieldset>
    <legend>deletion</legend>
    <div>
      <PencilIcon
        suggestion={{ changeType: ChangeType.deletion }}
        {themeCategory}
        mask={isMask}
      />Deletion
    </div>
  </fieldset>
  <fieldset>
    <legend>modification</legend>
    <div>
      <PencilIcon
        suggestion={{ changeType: ChangeType.modification }}
        {themeCategory}
        mask={isMask}
      />Modification
    </div>
  </fieldset>
  <fieldset>
    <legend>applied</legend>
    <div>
      <PencilIcon
        suggestion={{ changeType: ChangeType.insertion, state: SuggestionState.stale }}
        {themeCategory}
        mask={isMask}
      />Modification
    </div>
  </fieldset>
</main>

<style>
  .c-main {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    padding: 1rem;
  }

  /* Light/dark background and text color */
  .c-main[data-theme="light"] {
    background-color: #fff;
    color: #000;
  }

  .c-main[data-theme="dark"] {
    background-color: #333;
    color: #fff;
  }
</style>
