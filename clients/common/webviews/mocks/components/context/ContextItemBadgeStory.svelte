<script lang="ts">
  import ColumnLayout from "$common-webviews/mocks/storybook/ColumnLayout.svelte";
  import ContextItemBadge from "$common-webviews/src/apps/chat/components/context/ContextItemBadge.svelte";
  import { ContextStatus, type IContextInfo } from "$common-webviews/src/apps/chat/models/types";
  import { type IChatMentionable } from "$common-webviews/src/apps/chat/types/mention-option";

  const items: (IChatMentionable & IContextInfo)[] = [
    {
      label: "file",
      id: "file",
      file: {
        repoRoot: "/home/<USER>/example-repo-root",
        pathName: "file-name",
      },
      referenceCount: 1,
      status: ContextStatus.active,
      name: "file-name",
    },
    {
      label: "folder",
      id: "folder",
      folder: {
        repoRoot: "/home/<USER>/example-repo-root",
        pathName: "folder-name",
      },
      referenceCount: 1,
      status: ContextStatus.active,
      name: "folder-name",
    },
    {
      label: "source folder",
      id: "source-folder",
      sourceFolder: {
        folderRoot: "/home/<USER>/example-repo-root",
      },
      referenceCount: 1,
      status: ContextStatus.active,
      name: "example-repo-root",
    },
    {
      label: "external source",
      id: "external-source",
      externalSource: {
        id: "external-source",
        name: "Ext Source",
        title: "External Source",
        // eslint-disable-next-line @typescript-eslint/naming-convention
        source_type: 1,
      },
      referenceCount: 1,
      status: ContextStatus.active,
      name: "external-source",
    },
    {
      label: "selection",
      id: "selection",
      selection: {
        repoRoot: "/home/<USER>/example-repo-root",
        pathName: "selection-name",
        originalCode: "original-code",
      },
      referenceCount: 1,
      status: ContextStatus.active,
      name: "selection-name",
    },
    {
      label: "recent file",
      id: "recent-file",
      recentFile: {
        repoRoot: "/home/<USER>/example-repo-root",
        pathName: "recent-file-name",
      },
      referenceCount: 1,
      status: ContextStatus.active,
      name: "recent-file-name",
    },
    {
      label: "user guidelines",
      id: "user-guidelines",
      userGuidelines: {
        contents: "user guidelines",
        overLimit: false,
        lengthLimit: 2000,
      },
      referenceCount: 1,
      status: ContextStatus.active,
      name: "example-repo-root",
    },
  ];
</script>

<ColumnLayout>
  <div class="l-context-chips-container">
    {#each items as item}
      <ContextItemBadge {item} chatModel={undefined} />
    {/each}
  </div>
</ColumnLayout>

<style>
  .l-context-chips-container {
    display: flex;
    flex-direction: row;
    gap: var(--ds-spacing-1);
    flex-wrap: wrap;
  }
</style>
