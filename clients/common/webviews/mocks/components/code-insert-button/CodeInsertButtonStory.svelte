<script lang="ts">
  import CodeInsertButton from "$common-webviews/src/common/components/CodeInsertButton.svelte";
  import ColumnLayout from "$common-webviews/mocks/storybook/ColumnLayout.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import type {
    ButtonSize,
    ButtonColor,
  } from "$common-webviews/src/design-system/_primitives/BaseButton.svelte";
  import { CodeblockActionType } from "$common-webviews/src/apps/chat/components/markdown-ext/codeblock/types";
  import { type ButtonState } from "$common-webviews/src/apps/chat/components/buttons/types";

  // Props matching the CodeInsertButton component
  export let size: ButtonSize;
  export let text: string;
  export let color: ButtonColor;
  export let tooltip: string;
  export let successText: string;
  export let stickyColor: boolean;
  export let success: boolean;

  // Mock function to simulate insert operation
  const handleInsert = async (): Promise<ButtonState> => {
    await new Promise((resolve) => setTimeout(resolve, 800));
    return "success";
  };

  const handleInsertFail = async (): Promise<ButtonState> => {
    await new Promise((resolve) => setTimeout(resolve, 800));
    return "failure";
  };
</script>

<ColumnLayout>
  <h3>Insert Button</h3>
  <CodeInsertButton
    codeblockActionType={CodeblockActionType.insert}
    {size}
    variant="ghost-block"
    {color}
    {tooltip}
    {successText}
    {stickyColor}
    onClick={success ? handleInsert : handleInsertFail}
  >
    <TextAugment slot="text" size={1}>{text}</TextAugment>
  </CodeInsertButton>
  <h3>Smart Paste Button</h3>
  <CodeInsertButton
    codeblockActionType={CodeblockActionType.smartPaste}
    {size}
    variant="ghost-block"
    {color}
    {tooltip}
    {successText}
    {stickyColor}
    onClick={success ? handleInsert : handleInsertFail}
  >
    <TextAugment slot="text" size={1}>{text}</TextAugment>
  </CodeInsertButton>
  <h3>Direct Apply Button</h3>
  <CodeInsertButton
    codeblockActionType={CodeblockActionType.directApply}
    {size}
    variant="ghost-block"
    {color}
    {tooltip}
    {successText}
    {stickyColor}
    onClick={success ? handleInsert : handleInsertFail}
  >
    <TextAugment slot="text" size={1}>{text}</TextAugment>
  </CodeInsertButton>
</ColumnLayout>
