<script lang="ts">
  import TextCombo from "$common-webviews/src/common/components/TextCombo.svelte";
  import type { TextSize } from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import SewingPin from "$common-webviews/src/design-system/icons/sewing-pin.svelte";

  export let text: string = "";
  export let grayText: string = "";
  export let size: TextSize = 1;
  export let align: "left" | "right" = "left";
  export let greyTextTruncateDirection: "left" | "right" = "right";
  export let shrink: boolean = false;
  export let showIcon: boolean = false;
</script>

<TextCombo {size} {align} {shrink} {greyTextTruncateDirection}>
  <svelte:fragment slot="leftIcon">
    {#if showIcon}
      <SewingPin />
    {/if}
  </svelte:fragment>
  <svelte:fragment slot="text">{text}</svelte:fragment>
  <svelte:fragment slot="grayText">{grayText}</svelte:fragment>
</TextCombo>
