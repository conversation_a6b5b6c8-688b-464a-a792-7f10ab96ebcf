<script lang="ts">
  import OpenFileButton from "$common-webviews/src/apps/chat/components/conversation/blocks/tools/components/OpenFileButton.svelte";
  import ColumnLayout from "$common-webviews/mocks/storybook/ColumnLayout.svelte";
  import Fieldset from "$common-webviews/mocks/storybook/Fieldset.svelte";
  import {
    type ButtonColor,
    type ButtonSize,
    type ButtonVariant,
  } from "$common-webviews/src/design-system/_primitives/BaseButton.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";

  export let size: Exclude<ButtonSize, 0.5> = 1;
  export let color: ButtonColor = "neutral";
  export let variant: ButtonVariant = "ghost-block";
  export let stickyColor: boolean = false;
  // Props matching the OpenFileButton component
  let path: string | undefined = "example/path/file.txt";
  let start: number = 0;
  let stop: number = 0;

  const variants: Array<{
    variant: ButtonVariant;
    label: string;
  }> = [
    { variant: "classic", label: "Classic" },
    { variant: "solid", label: "Solid" },
    { variant: "ghost", label: "Ghost" },
    { variant: "outline", label: "Outline" },
    { variant: "soft", label: "Soft" },
    { variant: "ghost-block", label: "Ghost Block" },
  ];

  const colors: Array<{
    color: ButtonColor;
    label: string;
  }> = [
    { color: "neutral", label: "Neutral" },
    { color: "accent", label: "Accent" },
    { color: "success", label: "Success" },
    { color: "error", label: "Error" },
  ];
</script>

<ColumnLayout>
  <Fieldset title="Default (Icon Only)">
    <OpenFileButton {path} {start} {stop} {size} {color} {variant} {stickyColor} />
  </Fieldset>

  <Fieldset title="With Text">
    <OpenFileButton {path} {start} {stop} {size} {color} {variant} {stickyColor}>
      <span slot="text">
        <TextAugment {size}>Open File</TextAugment>
      </span>
    </OpenFileButton>
  </Fieldset>

  <Fieldset title="Different Variants">
    <div style="display: flex; gap: 8px;">
      {#each variants as { variant, label }}
        <OpenFileButton {path} {start} {stop} {size} {color} {variant} {stickyColor}>
          <span slot="text">{label}</span>
        </OpenFileButton>
      {/each}
    </div>
  </Fieldset>

  <Fieldset title="Different Colors">
    <div style="display: flex; gap: 8px;">
      {#each colors as { color, label }}
        <OpenFileButton {path} {start} {stop} {size} {color} {variant} {stickyColor}>
          <span slot="text">{label}</span>
        </OpenFileButton>
      {/each}
    </div>
  </Fieldset>
</ColumnLayout>
