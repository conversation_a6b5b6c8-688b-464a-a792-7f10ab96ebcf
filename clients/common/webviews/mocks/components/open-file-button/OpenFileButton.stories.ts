/* eslint-disable @typescript-eslint/naming-convention */
import type { Meta, StoryObj } from "@storybook/svelte-vite";
import OpenFileButtonStory from "./OpenFileButtonStory.svelte";

const meta = {
  title: "components/OpenFileButton",
  component: OpenFileButtonStory,
  tags: ["autodocs"],
  argTypes: {
    size: {
      control: { type: "select" },
      options: [1, 2, 3, 4],
      description: "Size of the button",
      defaultValue: 1,
    },
    variant: {
      control: { type: "select" },
      options: ["classic", "solid", "soft", "surface", "outline", "ghost", "ghost-block"],
      description: "Visual style variant of the button",
      defaultValue: "ghost-block",
    },
    color: {
      control: { type: "select" },
      options: ["neutral", "accent", "success", "error"],
      description: "Color of the button",
      defaultValue: "neutral",
    },
    stickyColor: {
      control: "boolean",
      description: "Whether the success/error color should persist after tooltip closes",
      defaultValue: false,
    },
  },
  parameters: {
    docs: {
      description: {
        component: "A button component that opens a file in the editor when clicked.",
      },
    },
  },
} satisfies Meta<OpenFileButtonStory>;

export default meta;

type Story = StoryObj<typeof meta>;

// Default configuration
export const Default: Story = {
  args: {},
};
