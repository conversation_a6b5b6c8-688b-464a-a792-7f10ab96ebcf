<script lang="ts">
  import ShowMore from "$common-webviews/src/common/components/ShowMore.svelte";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import ColumnLayout from "$common-webviews/mocks/storybook/ColumnLayout.svelte";
  import Fieldset from "$common-webviews/mocks/storybook/Fieldset.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";

  let paragraphs = 1;
  let expanded = false;

  const loremIpsum = `Lorem ipsum dolor sit amet, consectetur adipiscing elit. <PERSON><PERSON><PERSON> euismod, nisl eget aliquam ultricies, nunc nisl aliquet nunc, quis aliquam nisl nisl eu nisl. N<PERSON><PERSON> euismod, nisl eget aliquam ultricies, nunc nisl aliquet nunc, quis aliquam nisl nisl eu nisl.`;

  function addParagraph() {
    paragraphs++;
  }

  function removeParagraph() {
    if (paragraphs > 1) {
      paragraphs--;
    }
  }
</script>

<ColumnLayout>
  <Fieldset title="Dynamic Content Demo">
    <div class="controls">
      <ButtonAugment on:click={addParagraph}>Add Paragraph</ButtonAugment>
      <ButtonAugment on:click={removeParagraph} disabled={paragraphs <= 1}
        >Remove Paragraph</ButtonAugment
      >
      <TextAugment>Current paragraphs: {paragraphs}</TextAugment>
    </div>

    <ShowMore {expanded} maxHeight={400}>
      <div class="content">
        {#each Array(paragraphs) as _, i}
          <p>{i + 1}. {loremIpsum}</p>
        {/each}
      </div>
    </ShowMore>
  </Fieldset>
  <Fieldset title="Initially Expanded">
    <ShowMore expanded={true} maxHeight={200}>
      <div class="content">
        {#each Array(5) as _, i}
          <p>{i + 1}. {loremIpsum}</p>
        {/each}
      </div>
    </ShowMore>
  </Fieldset>

  <Fieldset title="Custom Height">
    <ShowMore maxHeight={100}>
      <div class="content">
        {#each Array(1) as _, i}
          <p>{i + 1}. {loremIpsum}</p>
        {/each}
      </div>
    </ShowMore>
  </Fieldset>
</ColumnLayout>

<style>
  .controls {
    display: flex;
    gap: var(--ds-spacing-2);
    margin-bottom: var(--ds-spacing-3);
    align-items: center;
  }

  .content {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-2);
  }

  p {
    margin: 0;
    padding: 0;
  }
</style>
