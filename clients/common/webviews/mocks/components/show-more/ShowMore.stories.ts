/* eslint-disable @typescript-eslint/naming-convention */
import type { Meta, StoryObj } from "@storybook/svelte-vite";
import component from "./ShowMoreStory.svelte";

const meta = {
  title: "components/ShowMore",
  component,
  render(props) {
    return {
      Component: component,
      props,
    };
  },
} satisfies Meta<component>;

export default meta;

type Story = StoryObj<typeof meta>;

// More on writing stories with args: https://storybook.js.org/docs/writing-stories/args
export const Example: Story = {};

export const CustomHeight: Story = {
  args: {
    maxHeight: 100,
  } as any,
};

export const InitiallyExpanded: Story = {
  args: {
    expanded: true,
  } as any,
};

export const CustomText: Story = {
  args: {
    expandText: "Expand content",
    collapseText: "Collapse content",
  } as any,
};

export const ShowOnHover: Story = {
  args: {
    showOnHover: true,
  } as any,
};
