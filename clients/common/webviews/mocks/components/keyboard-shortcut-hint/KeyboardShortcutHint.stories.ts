/* eslint-disable @typescript-eslint/naming-convention */
import type { Meta, StoryObj } from "@storybook/svelte-vite";
import component from "$common-webviews/src/common/components/keybindings/KeyboardShortcutHint.svelte";

const meta = {
  title: "components/KeyboardShortcutHint",
  component,
} satisfies Meta<component>;

export default meta;

type Story = StoryObj<typeof meta>;

// More on writing stories with args: https://storybook.js.org/docs/writing-stories/args
export const Example: Story = {
  args: {
    keybinding: "cmd-L",
  },
};
export const ExampleCmd: Story = {
  name: "⌘-L",
  args: {
    keybinding: "⌘-L",
  },
};
