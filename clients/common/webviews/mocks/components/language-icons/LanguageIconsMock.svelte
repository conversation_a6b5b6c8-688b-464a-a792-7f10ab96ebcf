<script lang="ts">
  import LanguageIcon from "$common-webviews/src/common/components/language-icons/LanguageIcon.svelte";
  import map from "$common-webviews/src/common/components/language-icons/extensions.json";

  const extensions = new Map(map as [string, string[]][]);
  let entries = Array.from(extensions.entries());
  let search: string = "";

  $: {
    if (search) {
      entries = Array.from(extensions.entries()).filter(
        ([lang, ext]) => lang?.includes(search) || ext?.some((v) => v.includes(search)),
      );
    } else {
      entries = Array.from(extensions.entries());
    }
  }

  function onSearchChange(e: Event) {
    search = (e.target as HTMLInputElement).value;
  }
</script>

<fieldset>
  <legend
    ><label>Search by language or extension: <input type="text" on:input={onSearchChange} /></label
    ></legend
  >

  <p>The following languages are supported by the language icon component.</p>

  <div class="c-language-icons__container">
    {#each entries as [lang, extensions]}
      <div class="c-language-icons__item">
        <LanguageIcon filename={`${lang}.${extensions?.[0] ?? lang}`}
          >{lang} ({extensions?.join(", ")})</LanguageIcon
        >
      </div>
    {/each}
  </div>
</fieldset>

<style>
  .c-language-icons__container {
    padding: var(--ds-spacing-1);
  }

  .c-language-icons__item {
    padding: calc(--ds-spacing-1 * 0.5) var(--ds-spacing-1);
  }
</style>
