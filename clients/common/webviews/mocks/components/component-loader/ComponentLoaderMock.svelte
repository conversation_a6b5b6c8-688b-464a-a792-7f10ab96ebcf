<script lang="ts">
  import ComponentLoader from "$common-webviews/src/common/components/component-loader/ComponentLoader.svelte";
  import ColumnLayout from "$common-webviews/mocks/storybook/ColumnLayout.svelte";
  import FakeComponent from "./FakeComponent.svelte";

  export let loadingMessages: string[] = [
    "Teaching AI to tell dad jokes...",
    "Reticulating splines...",
    "Brewing coffee for the AI...",
    "Untangling spaghetti code...",
    "Convincing AI not to take over the world...",
    "Downloading more RAM...",
    "Converting caffeine to code...",
    "Generating witty loading messages...",
    "Searching Stack Overflow...",
    "Asking AI for a better loading message...",
  ];
  export let errorMessages: string[] | undefined = undefined;
  export let title = "Augment";
  export let error: Error | undefined = undefined;
  export let fakeLoadTime = 500;
  export let fakeRetry: number | undefined = undefined;
  export let retryCount = 3;
  export let randomize = false;

  let currentRetry = 0;
  const loader = (): Promise<typeof FakeComponent> => {
    return new Promise((resolve, reject) => {
      if (error) {
        reject(error);
        return;
      }
      setTimeout(() => {
        if (fakeRetry && currentRetry < fakeRetry) {
          currentRetry++;
          reject(new Error("Fake error"));
        }
        resolve(FakeComponent);
      }, fakeLoadTime);
    });
  };
</script>

<ColumnLayout>
  <ComponentLoader
    {errorMessages}
    {loadingMessages}
    {title}
    {loader}
    {randomize}
    {retryCount}
    props={{}}
  />
</ColumnLayout>

<style>
  :global(.component-loader) {
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
</style>
