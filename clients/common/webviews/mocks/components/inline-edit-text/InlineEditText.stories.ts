/* eslint-disable @typescript-eslint/naming-convention */
import type { Meta, StoryObj } from "@storybook/svelte-vite";
import component from "./InlineEditTextStory.svelte";

const meta = {
  title: "design system/InlineEditTextAugment",
  component,
  tags: ["autodocs"],
  argTypes: {
    value: {
      control: "text",
      description: "The current text value",
    },
    placeholder: {
      control: "text",
      description: "Placeholder text shown when value is empty",
    },
    disabled: {
      control: "boolean",
      description: "Whether the component is disabled",
    },
    size: {
      control: { type: "select" },
      options: [1, 2, 3, 4],
      description: "Text size variant",
    },
    isEditable: {
      control: "boolean",
      description: "Whether the text can be edited",
    },
    multiline: {
      control: "boolean",
      description: "Whether to use textarea (multiline) or input (single-line)",
    },
    showCommitButton: {
      control: "boolean",
      description: "Whether to show the commit button",
    },
    commitOnBlur: {
      control: "boolean",
      description: "Whether to commit changes when input loses focus",
    },
    commitOnEnter: {
      control: "boolean",
      description: "Whether to commit changes when Enter is pressed",
    },
    allowCtrlEnterForNewline: {
      control: "boolean",
      description: "Whether Ctrl+Enter creates new lines in multiline mode",
    },
    doubleClickToEdit: {
      control: "boolean",
      description: "Whether double-click is required to start editing",
    },
    commitButtonVariant: {
      control: { type: "select" },
      options: ["classic", "ghost", "surface"],
      description: "Visual variant of the commit button",
    },
    commitButtonColor: {
      control: { type: "select" },
      options: ["accent", "neutral", "success", "warning", "error"],
      description: "Color theme of the commit button",
    },
  },
  args: {
    value: "Click to edit this text",
    placeholder: "Enter text here...",
    disabled: false,
    size: 2,
    isEditable: true,
    multiline: true,
    showCommitButton: true,
    commitOnBlur: true,
    commitOnEnter: true,
    allowCtrlEnterForNewline: true,
    doubleClickToEdit: true,
    commitButtonVariant: "classic",
    commitButtonColor: "accent",
  },
} satisfies Meta<component>;

export default meta;

type Story = StoryObj<typeof meta>;

// Default interactive story
export const Default: Story = {};

// Basic usage examples
export const SingleLine: Story = {
  args: {
    value: "Single-line text editing",
    multiline: false,
    placeholder: "Enter single line text...",
  },
};

export const MultilineText: Story = {
  args: {
    value:
      "This is a multiline text example.\nYou can use Ctrl+Enter to add new lines.\nPress Enter to commit changes.",
    multiline: true,
    placeholder: "Enter multiline text...",
  },
};

export const NoCommitButton: Story = {
  args: {
    value: "Edit without commit button",
    showCommitButton: false,
  },
};

export const ClickToEdit: Story = {
  args: {
    value: "Single click to start editing",
    doubleClickToEdit: false,
  },
};

// State examples
export const Disabled: Story = {
  args: {
    value: "This text cannot be edited",
    disabled: true,
  },
};

export const ReadOnly: Story = {
  args: {
    value: "This text is read-only",
    isEditable: false,
  },
};

export const EmptyState: Story = {
  args: {
    value: "",
    placeholder: "This is placeholder text...",
  },
};

// Size variants
export const SmallSize: Story = {
  args: {
    value: "Small text size",
    size: 1,
  },
};

export const LargeSize: Story = {
  args: {
    value: "Large text size",
    size: 4,
  },
};

// Button variants
export const GhostButton: Story = {
  args: {
    value: "Text with ghost commit button",
    commitButtonVariant: "ghost",
  },
};

export const SuccessButton: Story = {
  args: {
    value: "Text with success colored button",
    commitButtonColor: "success",
  },
};

export const ErrorButton: Story = {
  args: {
    value: "Text with error colored button",
    commitButtonColor: "error",
  },
};

// Behavior variants
export const NoBlurCommit: Story = {
  args: {
    value: "Must use Enter or button to commit",
    commitOnBlur: false,
  },
};

export const NoEnterCommit: Story = {
  args: {
    value: "Must use button or blur to commit",
    commitOnEnter: false,
  },
};
