<script lang="ts">
  import { setCodeRollSelection } from "$common-webviews/src/common/components/code-roll/code-roll-context";
  import { sortAndFilterSuggestions } from "$common-webviews/src/common/components/suggestion-tree/navigation-utils";
  import SuggestionTree from "$common-webviews/src/common/components/suggestion-tree/SuggestionTree.svelte";
  import { suggestions } from "../../apps/next-edit-suggestions/next-edit-suggestions-mock";
  const sortedPathSuggestionsMap = new Map<string, typeof suggestions>(
    sortAndFilterSuggestions(suggestions),
  );
  setCodeRollSelection({});
</script>

<SuggestionTree
  {sortedPathSuggestionsMap}
  onCodeAction={(action, suggestion) => console.log(action, suggestion)}
/>
