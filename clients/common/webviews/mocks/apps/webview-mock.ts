import type { WebViewMessage } from "$vscode/src/webview-providers/webview-messages";

class WebviewMock extends EventTarget {
  // We re-broadcast messages from the webview (i.e. via vscode.postMessage())
  // via the webview mock. This allows mocks loaded in pages to respond with
  // sample data.
  messageFromWebView(msg: WebViewMessage) {
    this.dispatchEvent(new CustomEvent("message-from-webview", { detail: msg }));
  }

  // This is a helper function that acts the same as if the extension
  // posted a message to the webview.
  sendMessageToWebView(msg: WebViewMessage) {
    window.postMessage(msg);
  }
}

export const webviewmock = new WebviewMock();

self.acquireVsCodeApi = function () {
  console.warn("📢 Loading mock vscode API");
  return {
    postMessage: (e) => webviewmock.messageFromWebView(e),
    getState: () => {
      const state = localStorage.getItem("vscode-state");
      if (state) {
        return JSON.parse(state);
      }
      return undefined;
    },
    setState: (state) => {
      localStorage.setItem("vscode-state", JSON.stringify(state));
    },
  };
};
