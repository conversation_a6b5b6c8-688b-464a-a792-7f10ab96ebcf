/* eslint-disable @typescript-eslint/naming-convention */
import type { Meta, StoryObj } from "@storybook/svelte-vite";
import component from "./NextEditSuggestionsMock.svelte";
import { withMonaco } from "$common-webviews/mocks/design-system/monaco-provider/monaco-decorator";

const meta = {
  title: "app/NextEditSuggestions",
  component,
} satisfies Meta<component>;

export default meta;

type Story = StoryObj<typeof meta>;

// More on writing stories with args: https://storybook.js.org/docs/writing-stories/args
export const Example: Story = {
  args: {},
  decorators: [withMonaco],
};

export const ExampleNoSuggestions: Story = {
  args: {
    noSuggestions: true,
  },
  decorators: [withMonaco],
};

export const Multi: Story = {
  args: {
    multiMock: true,
  },
  decorators: [withMonaco],
};
