<script lang="ts">
  import NextEditSuggestions from "$common-webviews/src/apps/next-edit-suggestions/NextEditSuggestions.svelte";
  import {
    clearSuggestions,
    addSuggestion,
    setSuggestions,
    loading,
    openSuggestionTree,
    onMockMulti,
  } from "./next-edit-suggestions-mock";
  import { samples } from "$common-webviews/mocks/components/code-roll/samples";
  export let noSuggestions = false;
  export let multiMock = false;
  if (noSuggestions) {
    clearSuggestions();
    setSuggestions([]);
  } else {
    if (multiMock) {
      clearSuggestions();
      onMockMulti();
    } else {
      setSuggestions(samples());
    }
  }
  let busy = loading();
  function toggle() {
    busy = loading();
  }
  let showTree = true;
  function toggleSuggestionTree() {
    showTree = openSuggestionTree();
  }
</script>

<div class="c-next-edit-suggestions-mock">
  <div class="c-next-edit-suggestions-mock__buttons">
    <button on:click={clearSuggestions}>Clear</button>
    <button on:click={addSuggestion}>Add</button>
    <button on:click={toggle}>{busy ? "Busy" : "Not Busy"}</button>
    <button on:click={onMockMulti}>Multi</button>
    <button on:click={toggleSuggestionTree}
      >{!showTree ? "Show Suggestion Tree" : "Hide Suggestion Tree"}</button
    >
  </div>
  <div class="c-next-edit-suggestions-mock__suggestions">
    <NextEditSuggestions />
  </div>
</div>

<style>
  .c-next-edit-suggestions-mock {
    padding: 0;
  }
  .c-next-edit-suggestions-mock__suggestions {
    display: flex;
    flex-direction: column;
    justify-content: start;
    width: 100%;
    position: relative;
    overflow: hidden;
    height: 240px;
  }
  .c-next-edit-suggestions-mock__buttons {
    display: flex;
    flex-direction: row;
    gap: 1em;
    border-bottom: 1px solid #ccc;
    padding: 2em;
  }
</style>
