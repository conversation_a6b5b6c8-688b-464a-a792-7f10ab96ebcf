[{"requestId": "e6cbb6a5-0f7e-47f8-87bb-0904671fae0c", "mode": "BACKGROUND", "scope": "FILE", "result": {"lineStart": 6, "lineEnd": 6, "existingCode": "", "suggestedCode": "    // Here are a bunch of pointless operations for this example.\n    x <<= 15;\n    x *= 17;\n    x >> 15;\n    x <<= 15;\n    x *= 17;\n    x >> 15;\n", "changeDescription": "Do a bunch of pointless operations", "path": "/home/<USER>/augment/clients/vscode/src/__mocks__/next-edit/large-changes.ts", "blobName": "", "suggestionId": "mock-suggestion--1", "charStart": 134, "charEnd": 134, "diffSpans": [{"original": {"start": 0, "stop": 0}, "updated": {"start": 0, "stop": 146}}], "editingScore": 1, "localizationScore": 1, "editingScoreThreshold": 1}, "qualifiedPathName": {"rootPath": "/home/<USER>/augment", "relPath": "clients/vscode/src/__mocks__/next-edit/large-changes.ts"}, "lineRange": {"start": 6, "stop": 6}, "uriScheme": "file", "occurredAt": "2025-01-29T20:42:59.403Z", "state": "fresh", "changeType": "insertion", "highlightRange": {"start": 5, "stop": 6}}, {"requestId": "e6cbb6a5-0f7e-47f8-87bb-0904671fae0c", "mode": "BACKGROUND", "scope": "FILE", "result": {"lineStart": 11, "lineEnd": 18, "existingCode": "    // Here are a bunch of pointless operations for this example.\n    x <<= 15;\n    x *= 17;\n    x >> 15;\n    x <<= 15;\n    x *= 17;\n    x >> 15;\n", "suggestedCode": "", "changeDescription": "Remove addition", "path": "/home/<USER>/augment/clients/vscode/src/__mocks__/next-edit/large-changes.ts", "blobName": "", "suggestionId": "mock-suggestion--3", "charStart": 205, "charEnd": 351, "diffSpans": [{"original": {"start": 0, "stop": 146}, "updated": {"start": 0, "stop": 0}}], "editingScore": 1, "localizationScore": 1, "editingScoreThreshold": 1}, "qualifiedPathName": {"rootPath": "/home/<USER>/augment", "relPath": "clients/vscode/src/__mocks__/next-edit/large-changes.ts"}, "lineRange": {"start": 11, "stop": 18}, "uriScheme": "file", "occurredAt": "2025-01-29T20:42:59.420Z", "state": "fresh", "changeType": "deletion", "highlightRange": {"start": 11, "stop": 18}}, {"requestId": "e6cbb6a5-0f7e-47f8-87bb-0904671fae0c", "mode": "BACKGROUND", "scope": "FILE", "result": {"lineStart": 23, "lineEnd": 29, "existingCode": "    // Here are a bunch of pointless operations for this example.\n    x <<= 15;\n    x *= 17;\n    x >> 15;\n    x <<= 15;\n    x *= 17;\n", "suggestedCode": "    // Here are a bunch of different pointless operations for this example.\n    x >> 15;\n    x <<= 25;\n    x <<= 15;\n    x *= 17;\n    x *= 27;\n", "changeDescription": "Do the same thing in a roundabout way", "path": "/home/<USER>/augment/clients/vscode/src/__mocks__/next-edit/large-changes.ts", "blobName": "", "suggestionId": "mock-suggestion--5", "charStart": 427, "charEnd": 560, "diffSpans": [{"original": {"start": 0, "stop": 27}, "updated": {"start": 0, "stop": 27}}, {"original": {"start": 27, "stop": 27}, "updated": {"start": 27, "stop": 37}}, {"original": {"start": 27, "stop": 66}, "updated": {"start": 37, "stop": 76}}, {"original": {"start": 66, "stop": 93}, "updated": {"start": 76, "stop": 76}}, {"original": {"start": 93, "stop": 116}, "updated": {"start": 76, "stop": 99}}, {"original": {"start": 116, "stop": 118}, "updated": {"start": 99, "stop": 101}}, {"original": {"start": 118, "stop": 120}, "updated": {"start": 101, "stop": 103}}, {"original": {"start": 120, "stop": 120}, "updated": {"start": 103, "stop": 130}}, {"original": {"start": 120, "stop": 129}, "updated": {"start": 130, "stop": 139}}, {"original": {"start": 129, "stop": 131}, "updated": {"start": 139, "stop": 141}}, {"original": {"start": 131, "stop": 133}, "updated": {"start": 141, "stop": 143}}], "editingScore": 1, "localizationScore": 1, "editingScoreThreshold": 1}, "qualifiedPathName": {"rootPath": "/home/<USER>/augment", "relPath": "clients/vscode/src/__mocks__/next-edit/large-changes.ts"}, "lineRange": {"start": 23, "stop": 29}, "uriScheme": "file", "occurredAt": "2025-01-29T20:42:59.433Z", "state": "fresh", "changeType": "modification", "highlightRange": {"start": 23, "stop": 29}}, {"requestId": "e6cbb6a5-0f7e-47f8-87bb-0904671fae0c", "mode": "BACKGROUND", "scope": "FILE", "result": {"lineStart": 34, "lineEnd": 36, "existingCode": "    let x = 0;\n    return x + 1;\n", "suggestedCode": "    // Here are a bunch of pointless operations for this example.\n    let x = 1;\n    x *= 17;\n    x >> 15;\n    x <<= 15;\n    x *= 17;\n    return x;\n", "changeDescription": "Add a variable", "path": "/home/<USER>/augment/clients/vscode/src/__mocks__/next-edit/large-changes.ts", "blobName": "", "suggestionId": "mock-suggestion--7", "charStart": 624, "charEnd": 657, "diffSpans": [{"original": {"start": 0, "stop": 33}, "updated": {"start": 0, "stop": 148}}], "editingScore": 1, "localizationScore": 1, "editingScoreThreshold": 1}, "qualifiedPathName": {"rootPath": "/home/<USER>/augment", "relPath": "clients/vscode/src/__mocks__/next-edit/large-changes.ts"}, "lineRange": {"start": 34, "stop": 36}, "uriScheme": "file", "occurredAt": "2025-01-29T20:42:59.456Z", "state": "fresh", "changeType": "modification", "highlightRange": {"start": 34, "stop": 36}}]