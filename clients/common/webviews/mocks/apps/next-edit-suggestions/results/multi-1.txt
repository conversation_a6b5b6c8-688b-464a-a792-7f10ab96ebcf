/**
 * This file contains a medley of examples to test how decorations are rendered.
 */

function _largeInsertion() {
    let x = 0;
    return x + 1;
}

function _largeDeletion(x: number) {
    x += 10;
    // Here are a bunch of pointless operations for this example.
    x <<= 15;
    x *= 17;
    x >> 15;
    x <<= 15;
    x *= 17;
    x >> 15;
    return x + 1;
}

function _largeModification() {
    let x: number = 0;
    // Here are a bunch of pointless operations for this example.
    x <<= 15;
    x *= 17;
    x >> 15;
    x <<= 15;
    x *= 17;
    x >> 15;
    return x + 1;
}

function _largeInsertion2() {
    let x = 0;
    return x + 1;
}
