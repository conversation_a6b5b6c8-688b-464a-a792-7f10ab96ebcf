<script lang="ts">
  import MemoriesMarkdownEditor from "$common-webviews/src/apps/memories/MemoriesMarkdownEditor.svelte";
  import { onMount } from "svelte";
  import { MessageBroker } from "$common-webviews/src/common/utils/message-broker";
  import { host } from "$common-webviews/src/common/hosts/host";
  import { ChatFlagsModel } from "$common-webviews/src/apps/chat/models/chat-flags-model";
  import { ExtensionClient } from "$common-webviews/src/apps/chat/extension-client";

  // Props that can be passed to the story
  export let initialText =
    "# Markdown Editor\n\nThis is a sample markdown content.\n\n## Features\n\n- Edit markdown content\n- Select text to move to guidelines\n- Auto-save content to file";
  export let filePath = "memories.md";

  // Create local state
  let text = initialText;
  let path = filePath;

  // Mock extension client functionality
  const msgBroker = new MessageBroker(host);
  const flagsModel = new ChatFlagsModel();
  const extensionClient = new ExtensionClient(host, msgBroker, flagsModel);

  // Mock the extension client methods
  extensionClient.saveFile = (file) => {
    console.log(`Mock saving file to ${file.pathName}:`, file.content);
    return Promise.resolve();
  };

  extensionClient.updateUserGuidelines = (content) => {
    console.log("Mock updating user guidelines:", content);
    return Promise.resolve();
  };

  extensionClient.updateWorkspaceGuidelines = (content) => {
    console.log("Mock updating workspace guidelines:", content);
    return Promise.resolve();
  };

  extensionClient.openFile = ({ pathName }) => {
    console.log("Mock opening file:", pathName);
    return Promise.resolve();
  };

  onMount(() => {
    // Any initialization needed
  });
</script>

<div class="markdown-editor-mock">
  <MemoriesMarkdownEditor bind:text bind:path />
</div>

<style>
  .markdown-editor-mock {
    width: 100%;
    height: 100%;
    min-height: 500px;
    display: flex;
    flex-direction: column;
  }
</style>
