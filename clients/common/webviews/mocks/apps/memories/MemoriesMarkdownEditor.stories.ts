/* eslint-disable @typescript-eslint/naming-convention */
import type { Meta, StoryObj } from "@storybook/svelte-vite";
import component from "./MemoriesMarkdownEditorStory.svelte";

const meta = {
  title: "app/Memories/MemoriesMarkdownEditor",
  component,
  tags: ["autodocs"],
  parameters: {
    docs: {
      description: {
        component: "Markdown editor component for editing and managing memory files.",
      },
    },
  },
} satisfies Meta<component>;

export default meta;

type Story = StoryObj<typeof meta>;

// Basic example with default content
export const Default: Story = {
  args: {},
};

// Example with empty content
export const Empty: Story = {
  args: {
    initialText: "",
  },
};

// Example with longer content
export const LongContent: Story = {
  args: {
    initialText: `# Markdown Editor with Long Content

## Introduction
This is an example of the Markdown Editor with longer content to demonstrate scrolling and editing capabilities.

## Features
- Edit markdown content
- Select text to move to guidelines
- Auto-save content to file

## Usage
1. Type or paste markdown content
2. Select text to move it to user or workspace guidelines
3. Content is automatically saved to the file

## Code Example
\`\`\`typescript
function example() {
  console.log("This is a code example");
  return true;
}
\`\`\`

## Lists
### Unordered List
- Item 1
- Item 2
- Item 3

### Ordered List
1. First item
2. Second item
3. Third item

## Tables
| Header 1 | Header 2 | Header 3 |
|----------|----------|----------|
| Cell 1   | Cell 2   | Cell 3   |
| Cell 4   | Cell 5   | Cell 6   |

## Images
![Example Image](https://example.com/image.jpg)

## Links
[Example Link](https://example.com)

## Blockquotes
> This is a blockquote example.
> It can span multiple lines.

## Horizontal Rule
---

## Conclusion
This is the end of the long content example.`,
  },
};

// Example with custom file path
export const CustomFilePath: Story = {
  args: {
    filePath: "custom/path/to/memories.md",
  },
};
