/* eslint-disable @typescript-eslint/naming-convention */
import type { <PERSON>a, StoryObj } from "@storybook/svelte-vite";
import "./chat-mock";
import component from "$common-webviews/src/apps/chat/ChatLoader.svelte";
import { ExchangeStatus, SeenState } from "$common-webviews/src/apps/chat/types/chat-message";
import {
  ChatRequestNodeType,
  ChatResultNodeType,
} from "@augment-internal/sidecar-libs/src/chat/chat-types";
import { withMonaco } from "$common-webviews/mocks/design-system/monaco-provider/monaco-decorator";

const meta = {
  title: "app/Chat",
  component,
} satisfies Meta<component>;

export default meta;

type Story = StoryObj<typeof meta>;

const FIXED_CREATED = "2024-01-02T12:00:00.000Z";
const FIXED_LAST = "2024-01-02T12:01:00.000Z";

export const ExampleMessage: Story = {
  name: "Chat History - Features",
  args: {
    initialConversation: {
      id: "example-conversation-id",
      createdAtIso: FIXED_CREATED,
      lastInteractedAtIso: FIXED_LAST,
      chatHistory: [
        {
          request_message: "Show me an example of various chat features",
          response_text:
            "# Chat Features Demonstration\n\nHere's a demonstration of various chat features:\n\n## Headings\n\nAs you can see, I can create headings of different levels (H1, H2, etc.)\n\n## Lists\n\n### Unordered Lists\n\n- Item 1\n- Item 2\n- Item 3\n  - Nested item 3.1\n  - Nested item 3.2\n\n### Ordered Lists\n\n1. First item\n2. Second item\n3. Third item\n   1. Nested item 3.1\n   2. Nested item 3.2\n\n## Code Blocks\n\nHere's a simple code block without metadata:\n\n```\nfunction helloWorld() {\n  console.log('Hello, world!');\n}\n```\n\nHere's a code block with metadata:\n\n```javascript path=src/example.js mode=EDIT\nfunction calculateSum(a, b) {\n  return a + b;\n}\n```\n\n## Inline Code\n\nYou can use `inline code` for short code snippets or variable names.\n\n## Block Quotes\n\n> This is a block quote.\n>\n> It can span multiple paragraphs.\n\n## Tables\n\n| Header 1 | Header 2 | Header 3 |\n|----------|----------|----------|\n| Cell 1   | Cell 2   | Cell 3   |\n| Cell 4   | Cell 5   | Cell 6   |\n\n## Tool Uses\n\nI can also demonstrate tool uses, like searching the codebase:\n",
          status: ExchangeStatus.success,
          request_id: "example-request-id-1",
          seen_state: SeenState.seen,
          structured_output_nodes: [
            {
              id: 0,
              type: ChatResultNodeType.RAW_RESPONSE,
              content:
                "# Chat Features Demonstration\n\nHere's a demonstration of various chat features:\n\n## Headings\n\nAs you can see, I can create headings of different levels (H1, H2, etc.)\n\n## Lists\n\n### Unordered Lists\n\n- Item 1\n- Item 2\n- Item 3\n  - Nested item 3.1\n  - Nested item 3.2\n\n### Ordered Lists\n\n1. First item\n2. Second item\n3. Third item\n   1. Nested item 3.1\n   2. Nested item 3.2\n\n## Code Blocks\n\nHere's a simple code block without metadata:\n\n```\nfunction helloWorld() {\n  console.log('Hello, world!');\n}\n```\n\nHere's a code block with metadata:\n\n```javascript path=src/example.js mode=EDIT\nfunction calculateSum(a, b) {\n  return a + b;\n}\n```\n\n## Inline Code\n\nYou can use `inline code` for short code snippets or variable names.\n\n## Block Quotes\n\n> This is a block quote.\n>\n> It can span multiple paragraphs.\n\n## Tables\n\n| Header 1 | Header 2 | Header 3 |\n|----------|----------|----------|\n| Cell 1   | Cell 2   | Cell 3   |\n| Cell 4   | Cell 5   | Cell 6   |\n\n## Tool Uses\n\nI can also demonstrate tool uses, like searching the codebase:",
            },
            {
              id: 1,
              type: ChatResultNodeType.TOOL_USE,
              content: "",
              tool_use: {
                tool_use_id: "tool-use-example-id-1",
                tool_name: "codebase-retrieval",
                input_json: JSON.stringify({
                  information_request: "Find examples of chat components",
                }),
              },
            },
            {
              id: 2,
              type: ChatResultNodeType.RAW_RESPONSE,
              content:
                "\n\nHere are the results from searching the codebase for chat components. I can use these results to provide more specific information about the codebase structure.\n\n## Tool Results\n\nTool results can be displayed in the chat, showing the output of various tools like codebase searches, web searches, or command executions.\n\n## Memory\n\nI can also remember important information for future reference.\n\nThis example demonstrates the rich formatting capabilities available in the chat interface!",
            },
          ],
          structured_request_nodes: [
            {
              id: 0,
              type: ChatRequestNodeType.TEXT,
              text_node: {
                content: "Show me an example of various chat features",
              },
            },
          ],
        },
        {
          request_message: "Can you show me how to use the codebase-retrieval tool?",
          response_text:
            "Sure! The codebase-retrieval tool is used to search for relevant code in your project. Here's how you can use it:\n\n1. First, you need to describe what you're looking for in natural language\n2. The tool will search across the codebase and return the most relevant files and code snippets\n3. You can then use this information to understand the code structure or make changes\n\nLet me demonstrate by searching for the ChatLoader component:",
          status: ExchangeStatus.success,
          request_id: "example-request-id-2",
          seen_state: SeenState.seen,
          structured_output_nodes: [
            {
              id: 0,
              type: ChatResultNodeType.RAW_RESPONSE,
              content:
                "Sure! The codebase-retrieval tool is used to search for relevant code in your project. Here's how you can use it:\n\n1. First, you need to describe what you're looking for in natural language\n2. The tool will search across the codebase and return the most relevant files and code snippets\n3. You can then use this information to understand the code structure or make changes\n\nLet me demonstrate by searching for the ChatLoader component:",
            },
            {
              id: 1,
              type: ChatResultNodeType.TOOL_USE,
              content: "",
              tool_use: {
                tool_use_id: "tool-use-example-id-2",
                tool_name: "codebase-retrieval",
                input_json: JSON.stringify({
                  information_request: "Find the ChatLoader component and how it's used",
                }),
              },
            },
            {
              id: 2,
              type: ChatResultNodeType.TOOL_USE,
              content: "",
              tool_use: {
                tool_use_id: "tool-use-example-id-3",
                tool_name: "web-search",
                input_json: JSON.stringify({
                  query: "Svelte component loader pattern",
                  num_results: 3,
                }),
              },
            },
            {
              id: 3,
              type: ChatResultNodeType.TOOL_USE,
              content: "",
              tool_use: {
                tool_use_id: "tool-use-example-id-4",
                tool_name: "str-replace-editor",
                input_json: JSON.stringify({
                  command: "view",
                  path: "clients/common/webviews/src/apps/chat/ChatLoader.svelte",
                }),
              },
            },
            {
              id: 4,
              type: ChatResultNodeType.RAW_RESPONSE,
              content:
                "\n\nNow I can see the ChatLoader component implementation. This component is responsible for loading the Chat application with the specified initial conversation and flags.\n\nThe key parameters it accepts are:\n\n- `initialConversation`: Optional parameter to initialize the chat with a specific conversation\n- `initialFlags`: Optional parameter to set specific feature flags\n\nThis is exactly what we're using in our storyboard to demonstrate different chat features!",
            },
          ],
          structured_request_nodes: [
            {
              id: 0,
              type: ChatRequestNodeType.TEXT,
              text_node: {
                content: "Can you show me how to use the codebase-retrieval tool?",
              },
            },
          ],
        },
      ],
      feedbackStates: {},
      requestIds: ["example-request-id-1", "example-request-id-2"],
      isPinned: false,
      isShareable: true,
    },
    initialFlags: {
      enableDebugFeatures: true,
      enableChatMultimodal: true,
    },
  },
  decorators: [withMonaco],
};
