/* eslint-disable @typescript-eslint/naming-convention */
import {
  ToolUsePhase,
  type ToolUseState,
} from "$common-webviews/src/apps/chat/types/tool-use-state";

// Mock tool use states for the stories
export const mockToolUseStates: Record<string, ToolUseState> = {
  "agent-request-id-1;tool-use-agent-id-1": {
    phase: ToolUsePhase.completed,
    result: {
      text: "File created successfully",
      isError: false,
    },
    requestId: "agent-request-id-1",
    toolUseId: "tool-use-agent-id-1",
  },
  "agent-request-id-2;tool-use-agent-id-2": {
    phase: ToolUsePhase.completed,
    result: {
      text: "File created successfully",
      isError: false,
    },
    requestId: "agent-request-id-2",
    toolUseId: "tool-use-agent-id-2",
  },
  "agent-request-id-3;tool-use-agent-id-3": {
    phase: ToolUsePhase.completed,
    result: {
      text: "File created successfully",
      isError: false,
    },
    requestId: "agent-request-id-3",
    toolUseId: "tool-use-agent-id-3",
  },
};
