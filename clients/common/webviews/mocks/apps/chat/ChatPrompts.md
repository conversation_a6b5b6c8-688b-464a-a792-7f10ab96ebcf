# Chat Prompts
## Motivation
As a short-term workaround for testing the input box before we have automated testing for our Svelte components, this file will serve as a list of manual tests to prompt chat with.

### Basic Q&A
See `./chat-mock.ts` for a series of single-word prompts that will test the markdown rendering for responses.

### Input Box
For all of the below prompts, please copy-paste them into the chat input box


#### HTML

Can you describe what is happening in this block:
```
<div>
  <p>This is a <span>test</span> paragraph.</p>
  <ul>
    <li>Item 1</li>
    <li>Item 2</li>
    <li>Item 3</li>
  </ul>
  <button onclick="alert('Hello World!')">Click me</button>
</div>
```

This should NOT render directly as HTML -- if it does, we have HTML
injection in the input box.
