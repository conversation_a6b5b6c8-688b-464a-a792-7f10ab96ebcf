/* eslint-disable @typescript-eslint/naming-convention */
import type { Meta, StoryObj } from "@storybook/svelte-vite";
import component from "./GitAgentEditTabStory.svelte";

const meta = {
  title: "app/Chat/components/git-agent-edit/GitAgentEditTab",
  component,
  tags: ["autodocs"],
  parameters: {
    docs: {
      description: {
        component: `
# GitAgentEditTab

The GitAgentEditTab component provides a git-aware interface for agent edit operations.
It replaces the traditional AgentEditListTab with a comprehensive git workflow interface.

## Features

- **Git Status Display**: Shows unstaged and staged changes
- **File Management**: Stage/unstage files individually or in bulk
- **Commit Interface**: Create commits with proper messaging
- **Repository Stats**: Display file counts and change summaries
- **Error Handling**: Graceful error states and loading indicators

## States

The component handles multiple states:
- Loading: While initializing git context
- Error: When git operations fail
- Empty: When no changes are present
- Active: When changes are available for staging/committing

## Integration

This component integrates with:
- Git RPC client for backend operations
- Agent conversation model for context
- Async messaging for real-time updates
        `,
      },
    },
  },
} satisfies Meta<component>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    scenario: "default",
  },
  parameters: {
    docs: {
      description: {
        story: "Default state with mixed unstaged and staged changes",
      },
    },
  },
};

export const Loading: Story = {
  args: {
    scenario: "loading",
  },
  parameters: {
    docs: {
      description: {
        story: "Loading state while initializing git context",
      },
    },
  },
};

export const Error: Story = {
  args: {
    scenario: "error",
  },
  parameters: {
    docs: {
      description: {
        story: "Error state when git operations fail",
      },
    },
  },
};

export const EmptyRepository: Story = {
  args: {
    scenario: "empty",
  },
  parameters: {
    docs: {
      description: {
        story: "Empty state when no changes are present",
      },
    },
  },
};

export const UnstagedOnly: Story = {
  args: {
    scenario: "unstaged-only",
  },
  parameters: {
    docs: {
      description: {
        story: "Repository with only unstaged changes",
      },
    },
  },
};

export const StagedOnly: Story = {
  args: {
    scenario: "staged-only",
  },
  parameters: {
    docs: {
      description: {
        story: "Repository with only staged changes ready for commit",
      },
    },
  },
};

export const LargeRepository: Story = {
  args: {
    scenario: "large-repo",
  },
  parameters: {
    docs: {
      description: {
        story: "Repository with many files to test performance and scrolling",
      },
    },
  },
};

export const ConflictState: Story = {
  args: {
    scenario: "conflicts",
  },
  parameters: {
    docs: {
      description: {
        story: "Repository with merge conflicts that need resolution",
      },
    },
  },
};

export const AllStatusTypes: Story = {
  args: {
    scenario: "all-status-types",
  },
  parameters: {
    docs: {
      description: {
        story: "Repository showing all different git file status types with colored icons",
      },
    },
  },
};

export const NonAgenticConversation: Story = {
  args: {
    scenario: "non-agentic",
  },
  parameters: {
    docs: {
      description: {
        story: "Component behavior in non-agentic conversation context",
      },
    },
  },
};
