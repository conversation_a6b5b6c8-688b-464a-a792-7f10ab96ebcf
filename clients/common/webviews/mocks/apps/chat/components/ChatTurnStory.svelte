<script lang="ts">
  import ChatTurn from "$common-webviews/src/apps/chat/components/conversation/ChatTurn.svelte";
  import { ChatModel } from "$common-webviews/src/apps/chat/models/chat-model";
  import { SpecialContextInputModel } from "$common-webviews/src/apps/chat/models/context-model";
  import {
    ExchangeStatus,
    SeenState,
    type ExchangeWithStatus,
  } from "$common-webviews/src/apps/chat/types/chat-message";
  import { AsyncMsgSender } from "$common-webviews/src/common/hosts/async-messaging";
  import { host } from "$common-webviews/src/common/hosts/host";

  const chatModel = new ChatModel(
    new AsyncMsgSender(() => {}),
    host,
    new SpecialContextInputModel(),
  );

  const failedTurns: ExchangeWithStatus[] = [
    {
      /* eslint-disable @typescript-eslint/naming-convention */
      request_message: "Hello",
      status: ExchangeStatus.failed,
      request_id: "1234",
      seen_state: SeenState.unseen,
      display_error_message: "Example error",
      /* eslint-enable @typescript-eslint/naming-convention */
    },
  ];
</script>

<h2>Failed Turns</h2>

{#each failedTurns as turn (turn.request_id)}
  <ChatTurn {chatModel} {turn} isLastTurn={false} group={[]} />
{/each}
