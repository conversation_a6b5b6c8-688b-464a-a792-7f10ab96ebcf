/* eslint-disable @typescript-eslint/naming-convention */
import type { Meta, StoryObj } from "@storybook/svelte-vite";
import ConversationHistoryWarning from "$common-webviews/src/apps/chat/components/warnings/ConversationHistoryWarning.svelte";

const meta = {
  title: "app/Chat/components/warnings/ConversationHistoryWarning",
  component: ConversationHistoryWarning,
  tags: ["autodocs"],
  argTypes: {
    onDeleteAllHistory: {
      description: "Callback when the delete all history button is clicked. Should be a function.",
    },
    onDismiss: {
      description:
        "Callback when the dismiss button is clicked (for current session only). Should be a function.",
    },
    onHideForSession: {
      description:
        "Callback when the user chooses to hide the warning for this session. Should be a function.",
    },
  },
} satisfies Meta<ConversationHistoryWarning>;

export default meta;

type Story = StoryObj<typeof meta>;

// Default conversation history warning
export const Default: Story = {
  name: "Default",
  args: {
    onDeleteAllHistory: () => {
      console.log("Delete all history clicked");
    },
    onDismiss: () => {
      console.log("Dismiss clicked");
    },
    onHideForSession: () => {
      console.log("Hide for this session clicked");
    },
  },
};

// Interactive example with action logging
export const Interactive: Story = {
  name: "Interactive (Check Console)",
  args: {
    onDeleteAllHistory: () => {
      alert("Delete all history button clicked! Check console for logs.");
      console.log("ConversationHistoryWarning: Delete all history button clicked");
    },
    onDismiss: () => {
      alert("Dismiss button clicked! Check console for logs.");
      console.log("ConversationHistoryWarning: Dismiss button clicked");
    },
    onHideForSession: () => {
      alert("Hide for this session clicked! Check console for logs.");
      console.log("ConversationHistoryWarning: Hide for this session clicked");
    },
  },
};
