<script lang="ts">
  import ColumnLayout from "$common-webviews/mocks/storybook/ColumnLayout.svelte";
  import SuccessfulButton from "$common-webviews/src/apps/chat/components/buttons/SuccessfulButton.svelte";
  import type { ButtonState } from "$common-webviews/src/apps/chat/components/buttons/types";
  import { type ButtonVariant } from "$common-webviews/src/design-system/_primitives/BaseButton.svelte";
  import Reload from "$common-webviews/src/design-system/icons/reload.svelte";

  const variantVariants: ButtonVariant[] = [
    "classic",
    "solid",
    "soft",
    "surface",
    "outline",
    "ghost-block",
    "ghost",
  ];

  const states: ButtonState[] = ["success", "failure", "neutral"];
  let buttonState = Array(variantVariants.length).fill("neutral");

  const tooltip = {
    success: "Success!",
    failure: "Failure!",
    neutral: "Click me!",
  };

  function onClick(index: number) {
    const newState = states[(states.indexOf(buttonState[index]) + 1) % states.length];
    buttonState = buttonState.map((state, i) => (i === index ? newState : state));
    return newState;
  }
</script>

<ColumnLayout>
  <section>
    <table class="l-table-container">
      <thead>
        <tr>
          <th>Success</th>
          <th>Fail</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td>
            <SuccessfulButton
              defaultColor="accent"
              {tooltip}
              onClick={() => "success"}
              stateVariant={{ neutral: "ghost-block", success: "soft" }}
            >
              <Reload slot="iconLeft" />
              Succeed
            </SuccessfulButton>
          </td>
          <td>
            <SuccessfulButton
              defaultColor="accent"
              {tooltip}
              onClick={() => "failure"}
              stateVariant={{ neutral: "ghost-block", failure: "soft" }}
            >
              Fail
              <Reload slot="iconRight" />
            </SuccessfulButton>
          </td>
          <td>
            <SuccessfulButton defaultColor="accent" {tooltip} onClick={() => "failure"}>
              Fail
              <Reload slot="iconRight" />
            </SuccessfulButton>
          </td>
        </tr>
      </tbody>
    </table>
  </section>
  <section>
    <table class="l-table-container">
      <thead>
        <tr>
          <th></th>
          <th>Text</th>
          <th>Icon</th>
        </tr>
      </thead>
      <tbody>
        {#each variantVariants as variant, index}
          <tr>
            <td>{variant}</td>
            <td>
              <SuccessfulButton
                size={1}
                {variant}
                {tooltip}
                defaultColor="neutral"
                onClick={() => onClick(index)}
              >
                Cycle states
              </SuccessfulButton>
            </td>
            <td>
              <SuccessfulButton
                size={1}
                {variant}
                {tooltip}
                defaultColor="neutral"
                icon
                onClick={() => onClick(index)}
              >
                <Reload />
              </SuccessfulButton>
            </td>
          </tr>
        {/each}
      </tbody>
    </table>
  </section>
</ColumnLayout>
