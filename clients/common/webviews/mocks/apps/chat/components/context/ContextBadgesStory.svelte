<script lang="ts">
  import ColumnLayout from "$common-webviews/mocks/storybook/ColumnLayout.svelte";
  import ContextBadge from "$common-webviews/src/apps/chat/components/context/ContextBadge.svelte";
  import MultiRepoChip from "$common-webviews/src/apps/chat/components/context/MultiRepoChip.svelte";
  import ChatMentionHoverContents from "$common-webviews/src/apps/chat/components/mentions/ChatMentionHoverContents.svelte";
  import PinButton from "$common-webviews/src/apps/chat/components/PinButton.svelte";
  import { ChatModel } from "$common-webviews/src/apps/chat/models/chat-model";
  import { SpecialContextInputModel } from "$common-webviews/src/apps/chat/models/context-model";
  import Filespan from "$common-webviews/src/common/components/Filespan.svelte";
  import { AsyncMsgSender } from "$common-webviews/src/common/hosts/async-messaging";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import { host } from "$common-webviews/src/common/hosts/host";
  import GuidelinesChip from "$common-webviews/src/apps/chat/components/context/GuidelinesChip.svelte";
  import SelectionChip from "$common-webviews/src/apps/chat/components/context/SelectionChip.svelte";
  import ContextIcon from "$common-webviews/src/apps/chat/components/context/ContextIcon.svelte";

  const multiRepoItem = {
    id: "sourceFoldersGroup",
    label: "Repos",
    status: 0,
    sourceFolderGroup: [
      {
        sourceFolder: {
          folderRoot: "/home/<USER>/another-repo-root",
          repoRoot: "/home/<USER>/another-repo-root",
          guidelinesOverLimit: false,
          guidelinesLengthLimit: 2000,
        },
        status: 0,
        label: "/home/<USER>/another-repo-root",
        showWarning: false,
        id: "/home/<USER>/another-repo-rootfalse",
        referenceCount: 1,
      },
      {
        sourceFolder: {
          folderRoot: "/home/<USER>/example-repo-root",
          repoRoot: "/home/<USER>/example-repo-root",
          guidelinesOverLimit: false,
          guidelinesLengthLimit: 2000,
        },
        status: 0,
        label: "/home/<USER>/example-repo-root",
        showWarning: false,
        id: "/home/<USER>/example-repo-rootfalse",
        referenceCount: 0,
      },
    ],
    referenceCount: 1,

    userGuidelines: {
      enabled: true,
      overLimit: false,
      contents: "This is the user guidelines",
      lengthLimit: 2000,
    },
    rulesAndGuidelinesState: {
      totalCharacterCount: 1500,
      overLimit: false,
      lengthLimit: 2000,
    },
  };

  const guidelinesWarningItem = {
    id: "userGuidelines",
    label: "User Guidelines",
    status: 0,
    showWarning: true,
    referenceCount: 1,
    userGuidelines: {
      enabled: true,
      overLimit: true,
      contents:
        "This is a very long user guidelines content that exceeds the character limit and should trigger a warning state in the guidelines chip component",
      lengthLimit: 100,
    },
    rulesAndGuidelinesState: {
      totalCharacterCount: 2500,
      overLimit: true,
      lengthLimit: 2000,
    },
  };

  const chatModel = new ChatModel(
    new AsyncMsgSender(() => {}),
    host,
    new SpecialContextInputModel(),
  );
</script>

<ColumnLayout>
  <ContextBadge
    on:click={() => console.log("Badge clicked!")}
    removeBtn={{ title: "Title", onRemove: () => {} }}
    showWarning={false}
  >
    <PinButton isPinned={false} onPin={() => {}} slot="leftButtons" />
    <TextTooltipAugment>
      <Filespan nopath filepath={"/example/path/to/file"} />
      <ChatMentionHoverContents
        option={{ label: "example", name: "example", id: "example" }}
        slot="content"
      />
    </TextTooltipAugment>
  </ContextBadge>

  <ContextBadge
    on:click={() => {}}
    removeBtn={{ title: "Title", onRemove: () => {} }}
    showWarning={false}
  >
    <PinButton isPinned={true} onPin={() => {}} slot="leftButtons" />
    <TextTooltipAugment>
      <Filespan nopath filepath={"/example/path/to/file"} />
      <ChatMentionHoverContents option={multiRepoItem} slot="content" />
    </TextTooltipAugment>
  </ContextBadge>

  <ContextBadge
    on:click={() => {}}
    removeBtn={{ title: "Title", onRemove: () => {} }}
    showWarning={false}
  >
    <ContextIcon icon={"book"} slot="leftIcon" />
    <TextTooltipAugment>
      <Filespan nopath filepath={"/example/path/to/file"} />
      <ChatMentionHoverContents option={multiRepoItem} slot="content" />
    </TextTooltipAugment>
  </ContextBadge>

  <ContextBadge
    on:click={() => {}}
    removeBtn={{ title: "Title", onRemove: () => {} }}
    showWarning={false}
  >
    <TextTooltipAugment>
      <Filespan nopath filepath={"/example/path/to/file"} />
      <ChatMentionHoverContents option={multiRepoItem} slot="content" />
    </TextTooltipAugment>
  </ContextBadge>

  <ContextBadge
    on:click={() => {}}
    removeBtn={{ title: "Title", onRemove: () => {} }}
    showWarning={true}
  >
    <TextTooltipAugment>
      <Filespan nopath filepath={"/example/path/to/file"} />
      <ChatMentionHoverContents option={multiRepoItem} slot="content" />
    </TextTooltipAugment>
  </ContextBadge>

  <MultiRepoChip item={multiRepoItem} {chatModel} />

  <GuidelinesChip item={multiRepoItem} />

  <GuidelinesChip item={guidelinesWarningItem} />

  <SelectionChip
    originalCode="This is the original code"
    pathName="example.js"
    range={{ startLineNumber: 10, endLineNumber: 15, startColumn: 0, endColumn: 10 }}
  />
</ColumnLayout>

<ColumnLayout>
  <h3>Alignment Test</h3>

  <div class="row">
    <ContextBadge
      on:click={() => {}}
      removeBtn={{ title: "Title", onRemove: () => {} }}
      showWarning={false}
    >
      <PinButton isPinned={true} onPin={() => {}} slot="leftButtons" />
      <TextTooltipAugment>
        <Filespan nopath filepath={"/example/path/to/file"} />
        <ChatMentionHoverContents option={multiRepoItem} slot="content" />
      </TextTooltipAugment>
    </ContextBadge>

    <ContextBadge
      on:click={() => {}}
      removeBtn={{ title: "Title", onRemove: () => {} }}
      showWarning={false}
    >
      <ContextIcon icon={"book"} slot="leftIcon" />
      <TextTooltipAugment>
        <Filespan nopath filepath={"/example/path/to/file"} />
        <ChatMentionHoverContents option={multiRepoItem} slot="content" />
      </TextTooltipAugment>
    </ContextBadge>

    <MultiRepoChip item={multiRepoItem} {chatModel} />

    <GuidelinesChip item={multiRepoItem} />

    <GuidelinesChip item={guidelinesWarningItem} />

    <SelectionChip
      originalCode="This is the original code"
      pathName="example.ts"
      range={{ startLineNumber: 5, endLineNumber: 8, startColumn: 0, endColumn: 20 }}
    />
  </div>
</ColumnLayout>

<style>
  .row {
    display: flex;
    flex-direction: row;
    align-items: center;
  }
</style>
