import type { Meta, StoryObj } from "@storybook/svelte-vite";
import TaskTreeItemStory from "./TaskTreeItemStory.svelte";

const meta = {
  title: "Chat/Components/Tasks/TaskTreeItem",
  component: TaskTreeItemStory,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
} satisfies Meta<TaskTreeItemStory>;

export default meta;
type Story = StoryObj<typeof meta>;

// eslint-disable-next-line @typescript-eslint/naming-convention
export const Default: Story = {
  args: {},
};
