/* eslint-disable @typescript-eslint/naming-convention */
import type { Meta, StoryObj } from "@storybook/svelte-vite";
import component from "$common-webviews/src/apps/chat/components/threads/ThreadsSkeletonLoader.svelte";

const meta = {
  title: "app/Threads/components/ThreadsSkeletonLoader",
  component,
  tags: ["autodocs"],
  argTypes: {
    rowCount: {
      control: { type: "number", min: 1, max: 10, step: 1 },
      description: "Number of skeleton rows to display while loading",
      defaultValue: 3,
    },
  },
  parameters: {
    layout: "centered",
    docs: {
      description: {
        component:
          "A skeleton loading component that displays animated placeholder rows while threads are loading.",
      },
    },
  },
} satisfies Meta<component>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    rowCount: 3,
  },
};
