<script lang="ts">
  /* eslint-disable @typescript-eslint/naming-convention */
  import { ChatModel } from "$common-webviews/src/apps/chat/models/chat-model";
  import { SpecialContextInputModel } from "$common-webviews/src/apps/chat/models/context-model";
  import { RemoteAgentsModel } from "$common-webviews/src/apps/remote-agent-manager/models/remote-agents-model";
  import { GitReferenceModel } from "$common-webviews/src/apps/remote-agent-manager/models/git-reference-model";
  import { ChatModeModel } from "$common-webviews/src/apps/chat/models/chat-mode-model";
  import { AsyncMsgSender } from "$common-webviews/src/common/hosts/async-messaging";
  import { MessageBroker } from "$common-webviews/src/common/utils/message-broker";
  import { host } from "$common-webviews/src/common/hosts/host";
  import { setContext } from "svelte";
  import { writable } from "svelte/store";
  import { SHARED_AGENT_STORE_CONTEXT_KEY } from "$vscode/src/webview-panels/remote-agents/common-webview-store";
  import type { IConversation } from "$common-webviews/src/apps/chat/models/types";
  import {
    RemoteAgentStatus,
    RemoteAgentWorkspaceStatus,
    type RemoteAgent,
  } from "@augment-internal/sidecar-libs/src/remote-agent/remote-agent-types";
  import { ChatItemType, ExchangeStatus } from "$common-webviews/src/apps/chat/types/chat-message";
  import { NEW_AGENT_KEY } from "$common-webviews/src/apps/chat/models/agent-constants";
  import ThreadsPanel from "$common-webviews/src/apps/chat/components/threads/ThreadsPanel.svelte";

  // Create mock models
  const mockAsyncMsgSender = new AsyncMsgSender(() => {});
  const mockMessageBroker = new MessageBroker(host);
  const chatModel = new ChatModel(mockAsyncMsgSender, host, new SpecialContextInputModel(), {
    initialFlags: {
      enableDebugFeatures: false,
      enableConversationDebugUtils: false,
      enableShareService: true,
    },
  });

  // Ensure flags are properly initialized
  chatModel.flags.update({
    enableDebugFeatures: false,
    enableConversationDebugUtils: false,
    enableShareService: true,
  });

  // Create proper mock model instances
  const mockGitReferenceModel = new GitReferenceModel(mockAsyncMsgSender);

  const mockRemoteAgentsModel = new RemoteAgentsModel({
    msgBroker: mockMessageBroker,
    isActive: false,
    flagsModel: chatModel.flags,
    host,
    gitRefModel: mockGitReferenceModel,
  });

  // Create a minimal mock AgentConversationModel
  const mockAgentConversationModel = {
    isCurrConversationAgentic: writable(false),
    agentExchangeStatus: writable("notRunning"),
  };

  const mockChatModeModel = new ChatModeModel(
    chatModel,
    mockAgentConversationModel as any,
    mockRemoteAgentsModel,
  );

  const mockSharedWebviewStore = writable({});

  // Set up contexts
  setContext("chatModel", chatModel);
  setContext(RemoteAgentsModel.key, mockRemoteAgentsModel);
  setContext(GitReferenceModel.key, mockGitReferenceModel);
  setContext(ChatModeModel.key, mockChatModeModel);
  setContext(SHARED_AGENT_STORE_CONTEXT_KEY, mockSharedWebviewStore);

  // Create comprehensive mock data with different thread types and dates
  const now = new Date();
  const fiveMinutesAgo = new Date(now.getTime() - 5 * 60 * 1000);
  const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
  const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);
  const lastWeek = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
  const lastMonth = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

  // Create mock conversations for different thread types
  const mockConversations: IConversation[] = [
    // Recent chat thread (pinned)
    {
      id: "recent-chat-id",
      name: "Debug TypeScript Error",
      createdAtIso: oneHourAgo.toISOString(),
      lastInteractedAtIso: oneHourAgo.toISOString(),
      chatHistory: [
        {
          chatItemType: ChatItemType.exchange,
          request_message: "Help me debug this TypeScript error in my React component",
          response_text: "I'll help you debug that TypeScript error. Let me analyze the code...",
          request_id: "req-1",
          status: ExchangeStatus.success,
          timestamp: oneHourAgo.toISOString(),
        },
      ],
      feedbackStates: {},
      requestIds: ["req-1"],
      isPinned: true,
      isShareable: true,
      extraData: {},
      toolUseStates: {},
    },
    // Yesterday's chat thread
    {
      id: "yesterday-chat-id",
      name: "React Authentication Setup",
      createdAtIso: yesterday.toISOString(),
      lastInteractedAtIso: yesterday.toISOString(),
      chatHistory: [
        {
          chatItemType: ChatItemType.exchange,
          request_message: "Help me set up authentication in my React app",
          response_text:
            "I'll help you set up authentication. Let's start with setting up the auth provider...",
          request_id: "req-2",
          status: ExchangeStatus.success,
          timestamp: yesterday.toISOString(),
        },
      ],
      feedbackStates: {},
      requestIds: ["req-2"],
      isPinned: false,
      isShareable: true,
      extraData: {},
      toolUseStates: {},
    },
    // Local agent thread (last week)
    {
      id: "local-agent-id",
      name: "Refactor Database Schema",
      createdAtIso: lastWeek.toISOString(),
      lastInteractedAtIso: lastWeek.toISOString(),
      chatHistory: [
        {
          chatItemType: ChatItemType.agentOnboarding,
          request_message: "Refactor the database schema to improve performance",
          response_text:
            "I'll help you refactor the database schema. Let me analyze the current structure...",
          request_id: "req-3",
          status: ExchangeStatus.success,
          timestamp: lastWeek.toISOString(),
        },
      ],
      feedbackStates: {},
      requestIds: ["req-3"],
      isPinned: false,
      isShareable: true,
      extraData: { isAgentConversation: true },
      toolUseStates: {},
    },
    // Older chat thread (last month)
    {
      id: "old-chat-id",
      name: "API Integration Guide",
      createdAtIso: lastMonth.toISOString(),
      lastInteractedAtIso: lastMonth.toISOString(),
      chatHistory: [
        {
          chatItemType: ChatItemType.exchange,
          /* eslint-disable @typescript-eslint/naming-convention */
          request_message: "How do I integrate with the REST API?",
          response_text: "Here's how to integrate with the REST API...",
          request_id: "req-4",
          /* eslint-enable @typescript-eslint/naming-convention */
          status: ExchangeStatus.success,
          timestamp: lastMonth.toISOString(),
        },
      ],
      feedbackStates: {},
      requestIds: ["req-4"],
      isPinned: false,
      isShareable: true,
      extraData: {},
      toolUseStates: {},
    },
  ];

  /* eslint-disable @typescript-eslint/naming-convention */
  const mockRemoteAgents: RemoteAgent[] = [
    // 1. PENDING - Agent created but not yet assigned to workspace
    {
      remote_agent_id: "remote-pending-id",
      status: RemoteAgentStatus.agentPending,
      workspace_status: RemoteAgentWorkspaceStatus.workspaceRunning,
      started_at: fiveMinutesAgo.toISOString(),
      updated_at: fiveMinutesAgo.toISOString(),
      session_summary: "Waiting for workspace assignment",
      turn_summaries: [],
      expires_at: new Date(now.getTime() + 15 * 24 * 60 * 60 * 1000).toISOString(),
      is_setup_script_agent: false,
      has_updates: false,
      title: "Pending Agent Assignment",
    },

    // 2. STARTING - Agent is starting and setting up environment
    {
      remote_agent_id: "remote-starting-id",
      status: RemoteAgentStatus.agentStarting,
      workspace_status: RemoteAgentWorkspaceStatus.workspaceRunning,
      started_at: fiveMinutesAgo.toISOString(),
      updated_at: fiveMinutesAgo.toISOString(),
      session_summary: "Setting up development environment",
      turn_summaries: [],
      expires_at: new Date(now.getTime() + 15 * 24 * 60 * 60 * 1000).toISOString(),
      is_setup_script_agent: true,
      has_updates: false,
      title: "Environment Setup",
    },

    // 3. RUNNING - Agent actively working on task
    {
      remote_agent_id: "remote-running-id",
      status: RemoteAgentStatus.agentRunning,
      workspace_status: RemoteAgentWorkspaceStatus.workspaceRunning,
      started_at: oneHourAgo.toISOString(),
      updated_at: fiveMinutesAgo.toISOString(),
      session_summary: "Implementing user authentication system",
      turn_summaries: ["Setting up auth middleware", "Configuring JWT tokens"],
      expires_at: new Date(now.getTime() + 2 * 60 * 60 * 1000).toISOString(), // Expires in 2 hours (shows "0 days remaining")
      is_setup_script_agent: false,
      has_updates: false,
      title: "Auth System Implementation",
      workspace_setup: {
        starting_files: {
          github_commit_ref: {
            repository_url: "https://github.com/bamboo-soy/test2",
            git_ref: "main",
          },
        },
      },
    },

    // 4. IDLE (without updates) - Agent completed task, awaiting instructions
    {
      remote_agent_id: "remote-idle-id",
      status: RemoteAgentStatus.agentIdle,
      workspace_status: RemoteAgentWorkspaceStatus.workspaceRunning,
      started_at: lastWeek.toISOString(),
      updated_at: lastWeek.toISOString(),
      session_summary: "Code refactoring completed",
      turn_summaries: ["Refactored components", "Updated tests", "Improved performance"],
      expires_at: new Date(now.getTime() + 20 * 24 * 60 * 60 * 1000).toISOString(),
      is_setup_script_agent: false,
      has_updates: false,
      title: "Code Refactoring",
    },

    // 5. IDLE (with updates) - Shows as "Unread" status
    {
      remote_agent_id: "remote-idle-updates-id",
      status: RemoteAgentStatus.agentIdle,
      workspace_status: RemoteAgentWorkspaceStatus.workspaceRunning,
      started_at: yesterday.toISOString(),
      updated_at: oneHourAgo.toISOString(),
      session_summary: "Database migration and optimization",
      turn_summaries: ["Created migration scripts", "Optimized queries", "Added indexes"],
      expires_at: new Date(now.getTime() + 8 * 24 * 60 * 60 * 1000).toISOString(),
      is_setup_script_agent: false,
      has_updates: true,
      title: "Database Migration (Unread)",
      workspace_setup: {
        starting_files: {
          github_commit_ref: {
            repository_url: "https://github.com/augmentcode/augment",
            git_ref: "feature/database-optimization",
          },
        },
      },
    },

    // 6. FAILED - Agent encountered error and cannot continue
    {
      remote_agent_id: "remote-failed-id",
      status: RemoteAgentStatus.agentFailed,
      workspace_status: RemoteAgentWorkspaceStatus.workspaceRunning,
      started_at: yesterday.toISOString(),
      updated_at: oneHourAgo.toISOString(),
      session_summary: "Failed to complete deployment setup",
      turn_summaries: ["Started deployment", "Encountered configuration error"],
      expires_at: new Date(now.getTime() + 10 * 24 * 60 * 60 * 1000).toISOString(),
      is_setup_script_agent: false,
      has_updates: false,
      title: "Deployment Setup (Failed)",
    },

    // 7. WORKSPACE PAUSING - Workspace is being paused
    {
      remote_agent_id: "remote-pausing-id",
      status: RemoteAgentStatus.agentIdle,
      workspace_status: RemoteAgentWorkspaceStatus.workspacePausing,
      started_at: oneHourAgo.toISOString(),
      updated_at: fiveMinutesAgo.toISOString(),
      session_summary: "Pausing workspace for maintenance",
      turn_summaries: ["Completed current task", "Preparing for pause"],
      expires_at: new Date(now.getTime() + 12 * 24 * 60 * 60 * 1000).toISOString(),
      is_setup_script_agent: false,
      has_updates: false,
      title: "Workspace Pausing",
    },

    // 8. WORKSPACE PAUSED - Workspace is paused
    {
      remote_agent_id: "remote-paused-id",
      status: RemoteAgentStatus.agentIdle,
      workspace_status: RemoteAgentWorkspaceStatus.workspacePaused,
      started_at: lastMonth.toISOString(),
      updated_at: lastWeek.toISOString(),
      session_summary: "API integration paused",
      turn_summaries: ["Started API integration", "Paused for review"],
      expires_at: new Date(now.getTime() + 14 * 24 * 60 * 60 * 1000).toISOString(),
      is_setup_script_agent: false,
      has_updates: false,
      title: "API Integration (Paused)",
    },

    // 9. WORKSPACE RESUMING - Workspace is resuming (overrides agent status)
    {
      remote_agent_id: "remote-resuming-id",
      status: RemoteAgentStatus.agentIdle, // This will be overridden by workspace status
      workspace_status: RemoteAgentWorkspaceStatus.workspaceResuming,
      started_at: lastWeek.toISOString(),
      updated_at: fiveMinutesAgo.toISOString(),
      session_summary: "Resuming after maintenance break",
      turn_summaries: ["Paused for maintenance", "Resuming operations"],
      expires_at: new Date(now.getTime() + 18 * 24 * 60 * 60 * 1000).toISOString(),
      is_setup_script_agent: false,
      has_updates: false,
      title: "Resuming After Maintenance",
    },

    // 10. EDGE CASE: Failed agent with paused workspace
    {
      remote_agent_id: "remote-failed-paused-id",
      status: RemoteAgentStatus.agentFailed,
      workspace_status: RemoteAgentWorkspaceStatus.workspacePaused,
      started_at: lastMonth.toISOString(),
      updated_at: lastWeek.toISOString(),
      session_summary: "Failed deployment, workspace paused",
      turn_summaries: ["Attempted deployment", "Failed with errors", "Workspace paused"],
      expires_at: new Date(now.getTime() + 5 * 24 * 60 * 60 * 1000).toISOString(),
      is_setup_script_agent: false,
      has_updates: false,
      title: "Failed Deployment (Paused)",
    },

    // 11. EDGE CASE: Running agent with updates (should show "Running", not "Unread")
    {
      remote_agent_id: "remote-running-updates-id",
      status: RemoteAgentStatus.agentRunning,
      workspace_status: RemoteAgentWorkspaceStatus.workspaceRunning,
      started_at: oneHourAgo.toISOString(),
      updated_at: fiveMinutesAgo.toISOString(),
      session_summary: "Active development with recent updates",
      turn_summaries: ["Started feature work", "Made progress", "Recent updates available"],
      expires_at: new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000).toISOString(),
      is_setup_script_agent: false,
      has_updates: true, // This should not affect "Running" status
      title: "Active Development (Running)",
    },

    // 12. EDGE CASE: Starting agent with pausing workspace
    {
      remote_agent_id: "remote-starting-pausing-id",
      status: RemoteAgentStatus.agentStarting,
      workspace_status: RemoteAgentWorkspaceStatus.workspacePausing,
      started_at: fiveMinutesAgo.toISOString(),
      updated_at: fiveMinutesAgo.toISOString(),
      session_summary: "Starting agent while workspace is pausing",
      turn_summaries: ["Agent initialization started"],
      expires_at: new Date(now.getTime() + 16 * 24 * 60 * 60 * 1000).toISOString(),
      is_setup_script_agent: true,
      has_updates: false,
      title: "Starting (Workspace Pausing)",
    },
  ];
  /* eslint-enable @typescript-eslint/naming-convention */

  // Add mock conversations to chatModel
  mockConversations.forEach((conversation) => {
    chatModel.saveConversation(conversation);
  });

  // Create a new conversation with NEW_AGENT_KEY to make it appear as "new"
  // This will be detected by ConversationModel.isNew() in ThreadsListView
  const newAgentConversation: IConversation = {
    id: NEW_AGENT_KEY, // This is the key that makes ConversationModel.isNew() return true
    name: "",
    createdAtIso: now.toISOString(),
    lastInteractedAtIso: now.toISOString(),
    chatHistory: [],
    feedbackStates: {},
    requestIds: [],
    isPinned: false,
    isShareable: true,
    extraData: {}, // Empty extraData means it's a regular chat thread
    toolUseStates: {},
  };

  // Save this conversation and set it as current
  chatModel.saveConversation(newAgentConversation);
  chatModel.setCurrentConversation(NEW_AGENT_KEY);

  // Add mock remote agents to the remote agents model
  // We need to simulate the remote agents being available by setting the internal state
  (mockRemoteAgentsModel as any)._state.agentOverviews = mockRemoteAgents;
  (mockRemoteAgentsModel as any)._state.hasFetchedOnce = true;

  // Set up notification settings - disable notifications for some agents
  const notificationSettings: Record<string, boolean> = {};
  notificationSettings["remote-pending-id"] = true; // Notifications enabled
  notificationSettings["remote-starting-id"] = false; // Notifications disabled
  notificationSettings["remote-running-id"] = true; // Notifications enabled
  notificationSettings["remote-idle-id"] = false; // Notifications disabled
  notificationSettings["remote-idle-updates-id"] = true; // Notifications enabled
  notificationSettings["remote-failed-id"] = false; // Notifications disabled
  notificationSettings["remote-pausing-id"] = true; // Notifications enabled
  notificationSettings["remote-paused-id"] = false; // Notifications disabled
  notificationSettings["remote-resuming-id"] = true; // Notifications enabled
  notificationSettings["remote-failed-paused-id"] = false; // Notifications disabled
  notificationSettings["remote-running-updates-id"] = true; // Notifications enabled
  notificationSettings["remote-starting-pausing-id"] = false; // Notifications disabled

  (mockRemoteAgentsModel as any)._state.notificationSettings = notificationSettings;
</script>

<div class="story-container">
  <ThreadsPanel {chatModel} onClose={() => console.log("ThreadsPanel closed")} />
</div>

<style>
  .story-container {
    max-width: 400px;
    max-height: 100%;
    background: var(--augment-window-background);
    border: 1px solid var(--ds-color-border);
    border-radius: 8px;
    overflow: hidden;
  }

  :global(body) {
    margin: 0;
    font-family: var(--ds-font-family);
  }
</style>
