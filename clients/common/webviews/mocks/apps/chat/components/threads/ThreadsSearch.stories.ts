/* eslint-disable @typescript-eslint/naming-convention */
import type { Meta, StoryObj } from "@storybook/svelte-vite";
import component from "$common-webviews/src/apps/chat/components/threads/ThreadsSearch.svelte";

const meta = {
  title: "app/Threads/components/ThreadsSearch",
  component,
  tags: ["autodocs"],
  argTypes: {
    searchQuery: {
      control: { type: "text" },
      description: "The current search query",
      defaultValue: "",
    },
    placeholder: {
      control: { type: "text" },
      description: "The placeholder text for the search input",
      defaultValue: "Search threads",
    },
    isSearchActive: {
      control: { type: "boolean" },
      description: "Whether the search is currently active",
      defaultValue: false,
    },
  },
  parameters: {
    layout: "centered",
    docs: {
      description: {
        component:
          "A search component for filtering threads with type-specific filters and search functionality.",
      },
    },
  },
} satisfies Meta<component>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    searchQuery: "",
    placeholder: "Search threads",
  },
};

export const WithSearchQuery: Story = {
  args: {
    searchQuery: "authentication setup",
    placeholder: "Search threads",
  },
};

export const CustomPlaceholder: Story = {
  args: {
    searchQuery: "",
    isSearchActive: true,
    placeholder: "Custom placeholder",
  },
};
