/* eslint-disable @typescript-eslint/naming-convention */
import type { Meta, StoryObj } from "@storybook/svelte-vite";
import component from "./ThreadsListRowItemWrapper.svelte";
import type { Thread } from "$common-webviews/src/apps/chat/components/threads/ThreadsPanel.svelte";

// Create simple mock objects to avoid circular references
const mockChatModel = {
  flags: {
    get enableDebugFeatures() {
      return false;
    },
    get enableConversationDebugUtils() {
      return false;
    },
    get enableShareService() {
      return true;
    },
    get remoteAgentsResumeHintAvailableTtlDays() {
      return 7;
    },
  },
  // Add any other properties that ThreadsListRowItem might need
  deleteConversation: async (id: string) => {
    console.log("Mock delete conversation:", id);
  },
  // Implement the Svelte store interface
  subscribe: (callback: (value: any) => void) => {
    // Call the callback immediately with the mock model
    callback(mockChatModel);
    // Return an unsubscribe function
    return () => {};
  },
} as any;

const mockRemoteAgentsModel = {
  // Properties that NotifyButton and other components need
  currentAgentId: null,
  newAgentDraft: null,
  notificationSettings: {},

  // Methods that components might call
  setNewAgentDraft: (draft: any) => {
    console.log("Mock setNewAgentDraft:", draft);
  },
  reportRemoteAgentSetupWindowEvent: async (action: any) => {
    console.log("Mock reportRemoteAgentSetupWindowEvent:", action);
  },
  setNotificationEnabled: (agentId: string, enabled: boolean) => {
    console.log("Mock setNotificationEnabled:", agentId, enabled);
  },
  deleteAgent: async (agentId: string) => {
    console.log("Mock deleteAgent:", agentId);
    return true;
  },

  // Implement the Svelte store interface
  subscribe: (callback: (value: any) => void) => {
    // Call the callback immediately with the mock model
    callback(mockRemoteAgentsModel);
    // Return an unsubscribe function
    return () => {};
  },
} as any;

// Mock thread data
const mockChatThread: Thread = {
  id: "chat-1",
  type: "chat",
  isNew: false,
  title: "React Authentication Setup",
  updated_at: "2024-01-15T14:45:00Z",
  started_at: "2024-01-15T10:30:00Z",
  isPinned: false,
  conversation: {
    id: "chat-1",
    name: "React Authentication Setup",
    createdAtIso: "2024-01-15T10:30:00Z",
    lastInteractedAtIso: "2024-01-15T14:45:00Z",
    isPinned: false,
    chatHistory: [],
    extraData: { isAgentConversation: false },
    feedbackStates: {},
    requestIds: [],
  },
  sortTimestamp: new Date("2024-01-15T14:45:00Z"),
};

const mockLocalAgentThread: Thread = {
  id: "local-1",
  type: "localAgent",
  isNew: false,
  title: "Database Migration Script",
  updated_at: "2024-01-14T16:20:00Z",
  started_at: "2024-01-14T09:15:00Z",
  isPinned: true,
  conversation: {
    id: "local-1",
    name: "Database Migration Script",
    createdAtIso: "2024-01-14T09:15:00Z",
    lastInteractedAtIso: "2024-01-14T16:20:00Z",
    isPinned: true,
    chatHistory: [],
    extraData: { isAgentConversation: true },
    feedbackStates: {},
    requestIds: [],
  },
  sortTimestamp: new Date("2024-01-14T16:20:00Z"),
};

const mockRemoteAgentThread: Thread = {
  id: "remote-1",
  type: "remoteAgent",
  isNew: false,
  title: "Stripe Payment Integration",
  updated_at: "2024-01-15T14:30:00Z",
  started_at: "2024-01-15T12:00:00Z",
  isPinned: false,
  status: 2,
  workspace_status: 1,
  is_setup_script_agent: false,
  workspace_setup: {
    starting_files: {
      github_commit_ref: {
        repository_url: "https://github.com/augmentcode/augment",
        git_ref: "main",
      },
    },
  },
  has_updates: true,
  agent: {
    remote_agent_id: "remote-1",
    session_summary: "Implementing Stripe payment integration",
    title: "Stripe Payment Integration",
    status: 2,
    workspace_status: 1,
    is_setup_script_agent: false,
    started_at: "2024-01-15T12:00:00Z",
    updated_at: "2024-01-15T14:30:00Z",
    turn_summaries: ["Set up Stripe client", "Created payment service"],
    workspace_setup: {
      starting_files: {
        github_commit_ref: {
          repository_url: "https://github.com/augmentcode/augment",
          git_ref: "main",
        },
      },
    },
    expires_at: "2024-01-16T12:00:00Z",
    has_updates: true,
  },
  sortTimestamp: new Date("2024-01-15T14:30:00Z"),
};

const meta = {
  title: "app/Threads/components/ThreadsListRowItem",
  component,
  tags: ["autodocs"],
  parameters: {
    layout: "padded",
    viewport: {
      defaultViewport: "responsive",
    },
  },
  args: {
    chatModel: mockChatModel,
    remoteAgentsModel: mockRemoteAgentsModel,
    containerWidth: 800,
    currentRepoUrl: "https://github.com/augmentcode/augment",
    enableShareService: true,
    isSelected: false,
    afterDelete: () => console.log("Delete thread"),
    onSelect: () => console.log("Select thread"),
    onTogglePinned: () => console.log("Toggle pin thread"),
  },
} satisfies Meta<component>;

export default meta;
type Story = StoryObj<typeof meta>;

export const ChatThread: Story = {
  args: {
    thread: mockChatThread,
  },
};

export const LocalAgentThread: Story = {
  args: {
    thread: mockLocalAgentThread,
  },
};

export const RemoteAgentThread: Story = {
  args: {
    thread: mockRemoteAgentThread,
  },
};

export const SelectedThread: Story = {
  args: {
    thread: mockChatThread,
    isSelected: true,
  },
};

export const NewThread: Story = {
  args: {
    thread: { ...mockChatThread, isNew: true, title: "New Chat Thread" },
  },
};
