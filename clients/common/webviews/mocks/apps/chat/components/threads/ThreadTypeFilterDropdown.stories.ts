/* eslint-disable @typescript-eslint/naming-convention */
import type { Meta, StoryObj } from "@storybook/svelte-vite";
import component from "$common-webviews/src/apps/chat/components/threads/ThreadTypeFilterDropdown.svelte";
import type { ThreadTypeFilterOption } from "$common-webviews/src/apps/chat/components/threads/ThreadTypeFilterDropdown.svelte";
import { writable } from "svelte/store";

// Mock data
const mockThreadTypeFilters: ThreadTypeFilterOption[] = [
  { value: "all", label: "All threads", count: 25 },
  { value: "chat", label: "Chats", count: 12 },
  { value: "localAgent", label: "Local agents", count: 8 },
  { value: "remoteAgent", label: "Remote agents", count: 5 },
];

const meta = {
  title: "app/Threads/components/ThreadTypeFilterDropdown",
  component,
  tags: ["autodocs"],
  argTypes: {
    threadTypeFilters: {
      control: { type: "object" },
      description: "Array of thread type filter options with counts",
      defaultValue: mockThreadTypeFilters,
    },
    activeThreadTypeFilter: {
      control: { type: "select" },
      options: ["all", "chat", "localAgent", "remoteAgent"],
      description: "The currently active thread type filter (as a Svelte store)",
      defaultValue: writable("all"),
    },
  },
  parameters: {
    layout: "centered",
    docs: {
      description: {
        component:
          "A dropdown component for filtering threads by type (all, chat, local agent, remote agent).",
      },
    },
  },
} satisfies Meta<component>;

export default meta;

type Story = StoryObj<typeof meta>;

export const AllThreads: Story = {
  args: {
    threadTypeFilters: mockThreadTypeFilters,
    activeThreadTypeFilter: writable("all"),
  },
};

export const ChatFilter: Story = {
  args: {
    threadTypeFilters: mockThreadTypeFilters,
    activeThreadTypeFilter: writable("chat"),
  },
};

export const LocalAgentFilter: Story = {
  args: {
    threadTypeFilters: mockThreadTypeFilters,
    activeThreadTypeFilter: writable("localAgent"),
  },
};

export const RemoteAgentFilter: Story = {
  args: {
    threadTypeFilters: mockThreadTypeFilters,
    activeThreadTypeFilter: writable("remoteAgent"),
  },
};

export const EmptyFilters: Story = {
  args: {
    threadTypeFilters: [],
    activeThreadTypeFilter: writable("all"),
  },
};

export const SingleFilter: Story = {
  args: {
    threadTypeFilters: [{ value: "chat", label: "Chats", count: 5 }],
    activeThreadTypeFilter: writable("chat"),
  },
};
