<script lang="ts">
  /* eslint-disable @typescript-eslint/naming-convention */
  import { setContext } from "svelte";
  import { writable } from "svelte/store";
  import { RemoteAgentsModel } from "$common-webviews/src/apps/remote-agent-manager/models/remote-agents-model";
  import NotificationsBadge from "$common-webviews/src/apps/chat/components/threads/NotificationsBadge.svelte";
  import {
    RemoteAgentStatus,
    type RemoteAgent,
  } from "@augment-internal/sidecar-libs/src/remote-agent/remote-agent-types";

  // Simple mock data with 5 agents that have updates
  const mockAgents = Array.from({ length: 5 }, (_, i) => ({
    remote_agent_id: `agent-${i + 1}`,
    status: RemoteAgentStatus.agentIdle,
    has_updates: true, // All agents have updates
  })) as RemoteAgent[];

  const mockStoreData = {
    isActive: true,
    currentAgentId: null,
    agentOverviews: mockAgents,
  };

  const mockRemoteAgentsModel = writable(mockStoreData);
  setContext(RemoteAgentsModel.key, mockRemoteAgentsModel);
</script>

<div>
  <NotificationsBadge />
</div>
