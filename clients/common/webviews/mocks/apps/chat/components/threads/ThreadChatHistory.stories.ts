/* eslint-disable @typescript-eslint/naming-convention */
import type { Meta, StoryObj } from "@storybook/svelte-vite";
import component from "$common-webviews/src/apps/chat/components/threads/ThreadChatHistory.svelte";

const meta = {
  title: "app/Threads/components/ThreadChatHistory",
  component,
  tags: ["autodocs"],
  argTypes: {
    userMessages: {
      control: { type: "object" },
      description: "Array of user messages to display as chat history",
      defaultValue: [],
    },
  },
  parameters: {
    layout: "centered",
    docs: {
      description: {
        component:
          "A component that displays a visual representation of chat history using vertical bars. Shows the last 9 messages with tooltips containing truncated message content.",
      },
    },
  },
} satisfies Meta<component>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    userMessages: [
      "How do I set up authentication in my React app?",
      "Can you help me debug this TypeScript error?",
      "What's the best way to handle state management?",
      "I need help with CSS Grid layout",
      "How do I optimize my database queries?",
    ],
  },
};

export const Empty: Story = {
  args: {
    userMessages: [],
  },
};

export const ManyMessages: Story = {
  args: {
    userMessages: [
      "First message about React setup",
      "Second message about TypeScript configuration",
      "Third message about testing framework",
      "Fourth message about deployment process",
      "Fifth message about performance optimization",
      "Sixth message about security best practices",
      "Seventh message about code review process",
      "Eighth message about documentation standards",
      "Ninth message about monitoring and logging",
      "Tenth message about error handling",
      "Eleventh message about API design",
      "Twelfth message about database schema",
      "This is a very long message that should be truncated when displayed in the tooltip. It contains a lot of text that goes beyond the normal length limit and should demonstrate how the truncation functionality works in the component.",
      "Medium length message that is not too long but not too short either",
      "Yet another incredibly long message that will definitely be truncated because it contains way more than 170 characters and is designed to test the edge cases of the truncation functionality implemented in the ThreadChatHistory component.",
    ],
  },
};
