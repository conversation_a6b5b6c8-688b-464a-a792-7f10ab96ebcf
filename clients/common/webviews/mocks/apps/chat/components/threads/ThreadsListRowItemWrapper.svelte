<script lang="ts">
  import ThreadsListRowItem from "$common-webviews/src/apps/chat/components/threads/ThreadsListRowItem.svelte";
  import { setContext } from "svelte";
  import type { Thread } from "$common-webviews/src/apps/chat/components/threads/ThreadsPanel.svelte";
  import type { ChatModel } from "$common-webviews/src/apps/chat/models/chat-model";
  import type { RemoteAgentsModel } from "$common-webviews/src/apps/remote-agent-manager/models/remote-agents-model";

  // Props that will be passed through to ThreadsListRowItem
  export let thread: Thread;
  export let chatModel: ChatModel;
  export let remoteAgentsModel: RemoteAgentsModel;
  export let containerWidth: number;
  export let currentRepoUrl: string;
  export let enableShareService: boolean;
  export let isSelected: boolean;
  export let afterDelete: (() => void) | undefined;
  export let onSelect: () => void;
  export let onTogglePinned: (event?: Event) => void;

  // Set the chat model and remote agents model in context so child components can access them
  setContext("chatModel", chatModel);
  setContext("remoteAgentsModel", remoteAgentsModel);
</script>

<ThreadsListRowItem
  {thread}
  {chatModel}
  {remoteAgentsModel}
  {containerWidth}
  {currentRepoUrl}
  {enableShareService}
  {isSelected}
  {afterDelete}
  {onSelect}
  {onTogglePinned}
/>
