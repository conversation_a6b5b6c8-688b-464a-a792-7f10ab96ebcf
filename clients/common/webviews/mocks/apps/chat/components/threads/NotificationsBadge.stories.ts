/* eslint-disable @typescript-eslint/naming-convention */
import type { Meta, StoryObj } from "@storybook/svelte-vite";
import component from "./NotificationsBadgeStory.svelte";

const meta = {
  title: "app/Threads/components/NotificationsBadge",
  component,
  tags: ["autodocs"],
  parameters: {
    layout: "centered",
    docs: {
      description: {
        component:
          "A notification badge that displays the count of remote agents with unread updates. Only shows idle agents with updates that are not currently selected.",
      },
    },
  },
} satisfies Meta<component>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
};
