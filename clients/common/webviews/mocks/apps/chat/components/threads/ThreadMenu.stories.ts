/* eslint-disable @typescript-eslint/naming-convention */
import type { <PERSON><PERSON>, StoryObj } from "@storybook/svelte-vite";
import component from "$common-webviews/src/apps/chat/components/threads/ThreadMenu.svelte";

// Mock thread data
const mockChatThread = {
  id: "chat-1",
  type: "chat" as const,
  title: "React Authentication Setup",
  isPinned: false,
  conversation: {
    id: "chat-1",
    isShareable: true,
  },
};

const mockLocalAgentThread = {
  id: "local-1",
  type: "localAgent" as const,
  title: "Database Migration Script",
  isPinned: true,
  conversation: {
    id: "local-1",
    isShareable: false,
  },
};

const mockRemoteAgentThread = {
  id: "remote-1",
  type: "remoteAgent" as const,
  title: "Stripe Payment Integration",
  isPinned: false,
  conversation: {
    id: "remote-1",
    isShareable: true,
  },
};

const meta = {
  title: "app/Threads/components/ThreadMenu",
  component,
  tags: ["autodocs"],
  argTypes: {
    thread: {
      control: { type: "object" },
      description: "Thread object with id, type, title, and other properties",
      defaultValue: mockChatThread,
    },
    type: {
      control: { type: "select" },
      options: ["chat", "localAgent", "remoteAgent"],
      description: "Type of the thread",
      defaultValue: "chat",
    },
    enableShareService: {
      control: { type: "boolean" },
      description: "Whether sharing functionality is enabled",
      defaultValue: false,
    },
    afterDelete: {
      control: false,
      description: "Callback function called after thread deletion",
    },
    onTogglePinned: {
      control: false,
      description: "Callback function called when pin status is toggled",
    },
    onRename: {
      control: false,
      description: "Callback function called when thread is renamed",
    },
  },
  parameters: {
    layout: "centered",
    docs: {
      description: {
        component:
          "A context menu component for thread actions like delete, pin/unpin, share, and rename.",
      },
    },
  },
} satisfies Meta<component>;

export default meta;

type Story = StoryObj<typeof meta>;

export const ChatThread: Story = {
  args: {
    thread: mockChatThread,
    type: "chat",
    enableShareService: true,
    afterDelete: () => console.log("Delete chat thread"),
    onTogglePinned: () => console.log("Toggle pin chat thread"),
    onRename: (id, title) => console.log("Rename chat thread", id, title),
  },
};

export const LocalAgentThread: Story = {
  args: {
    thread: mockLocalAgentThread,
    type: "localAgent",
    enableShareService: false,
    afterDelete: () => console.log("Delete local agent thread"),
    onTogglePinned: () => console.log("Toggle pin local agent thread"),
    onRename: (id, title) => console.log("Rename local agent thread", id, title),
  },
};

export const RemoteAgentThread: Story = {
  args: {
    thread: mockRemoteAgentThread,
    type: "remoteAgent",
    enableShareService: true,
    afterDelete: () => console.log("Delete remote agent thread"),
    onTogglePinned: () => console.log("Toggle pin remote agent thread"),
    onRename: (id, title) => console.log("Rename remote agent thread", id, title),
  },
};

export const PinnedThread: Story = {
  args: {
    thread: { ...mockChatThread, isPinned: true },
    type: "chat",
    enableShareService: true,
    afterDelete: () => console.log("Delete pinned thread"),
    onTogglePinned: () => console.log("Toggle pin pinned thread"),
    onRename: (id, title) => console.log("Rename pinned thread", id, title),
  },
};

export const NonShareableThread: Story = {
  args: {
    thread: {
      ...mockChatThread,
      conversation: { ...mockChatThread.conversation, isShareable: false },
    },
    type: "chat",
    enableShareService: true,
    afterDelete: () => console.log("Delete non-shareable thread"),
    onTogglePinned: () => console.log("Toggle pin non-shareable thread"),
    onRename: (id, title) => console.log("Rename non-shareable thread", id, title),
  },
};
