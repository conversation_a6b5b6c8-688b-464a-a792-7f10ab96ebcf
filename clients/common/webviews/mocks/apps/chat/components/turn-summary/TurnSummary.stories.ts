/* eslint-disable @typescript-eslint/naming-convention */
import type { Meta, StoryObj } from "@storybook/svelte";
import component from "./TurnSummaryStory.svelte";
import ReactiveDemo from "./TurnSummaryReactiveDemo.svelte";

const meta = {
  title: "app/Chat/components/turn-summary/TurnSummary",
  component,
  tags: ["autodocs"],
  argTypes: {
    entries: {
      control: { type: "object" },
      description: "Array of summary entries to display",
    },
    showFooter: {
      control: { type: "boolean" },
      description: "Whether to show the footer content",
    },
  },
} satisfies Meta<component>;

export default meta;

type Story = StoryObj<typeof meta>;

// Default story with typical values
export const Default: Story = {
  args: {
    entries: [
      {
        label: "Files Changed",
        value: 3,
        icon: "difference",
        callback: () => alert("Files changed clicked"),
      },
      { label: "Files Examined", value: 7, icon: "document_search" },
      { label: "Memories Retrieved", value: 2, icon: "inventory_2" },
      { label: "Tools Used", value: 5, icon: "design_services" },
    ],
    showFooter: false,
  },
};

// Story with zero values (should show "No activity to report")
export const Empty: Story = {
  args: {
    entries: [
      { label: "Files Changed", value: 0, icon: "difference" },
      { label: "Lines Added", value: 0, icon: "add_box" },
      { label: "Lines Removed", value: 0, icon: "indeterminate_check_box" },
      { label: "Files Examined", value: 0, icon: "document_search" },
      { label: "Memories Retrieved", value: 0, icon: "inventory_2" },
      { label: "Tools Used", value: 0, icon: "design_services" },
    ],
    showFooter: false,
  },
};

// Story with high values to test layout
export const FewItems: Story = {
  args: {
    entries: [
      { label: "Files Changed", value: 127, icon: "difference" },
      { label: "Lines Added", value: 1543, icon: "add_box" },
    ],
    showFooter: false,
  },
};

// Story with high values to test layout
export const HighValues: Story = {
  args: {
    entries: [
      { label: "Files Changed", value: 127, icon: "difference" },
      { label: "Lines Added", value: 1543, icon: "add_box" },
      { label: "Lines Removed", value: 892, icon: "indeterminate_check_box" },
      { label: "Files Examined", value: 45, icon: "document_search" },
      { label: "Memories Retrieved", value: 23, icon: "inventory_2" },
      { label: "Tools Used", value: 18, icon: "design_services" },
    ],
    showFooter: false,
  },
};

// Story with mixed values - some zero, some non-zero (zeros will be filtered out)
export const Mixed: Story = {
  args: {
    entries: [
      { label: "Files Changed", value: 5, icon: "difference" },
      { label: "Lines Added", value: 0, icon: "add_box" },
      { label: "Lines Removed", value: 12, icon: "indeterminate_check_box" },
      { label: "Files Examined", value: 0, icon: "document_search" },
      { label: "Memories Retrieved", value: 1, icon: "inventory_2" },
      { label: "Tools Used", value: 0, icon: "design_services" },
    ],
    showFooter: false,
  },
};

// Story demonstrating the footer slot with checkpoint-style actions
export const WithFooter: Story = {
  args: {
    entries: [
      { label: "Files Changed", value: 8, icon: "difference" },
      { label: "Lines Added", value: 156, icon: "add_box" },
      { label: "Lines Removed", value: 43, icon: "indeterminate_check_box" },
      { label: "Files Examined", value: 12, icon: "document_search" },
      { label: "Memories Retrieved", value: 3, icon: "inventory_2" },
      { label: "Tools Used", value: 7, icon: "design_services" },
    ],
    showFooter: true,
  },
};

// Story with single column layout (fewer items)
export const SingleColumn: Story = {
  args: {
    entries: [
      { label: "Files Changed", value: 2, icon: "difference" },
      { label: "Lines Added", value: 15, icon: "add_box" },
      { label: "Tools Used", value: 3, icon: "design_services" },
    ],
    showFooter: false,
  },
};

// Story with odd number of items to test grid layout
export const OddItems: Story = {
  args: {
    entries: [
      { label: "Files Changed", value: 4, icon: "difference" },
      { label: "Lines Added", value: 67, icon: "add_box" },
      { label: "Lines Removed", value: 23, icon: "indeterminate_check_box" },
      { label: "Files Examined", value: 9, icon: "document_search" },
      { label: "Memories Retrieved", value: 1, icon: "inventory_2" },
    ],
    showFooter: false,
  },
};

// Reactive animation demo story
export const ReactiveAnimationDemo: Story = {
  render: () => ({
    Component: ReactiveDemo,
  }),
  parameters: {
    docs: {
      description: {
        story:
          "Interactive demo showing the reactive animation orchestration system with replay functionality.",
      },
    },
  },
};
