<script lang="ts">
  import TurnSummary from "$common-webviews/src/apps/chat/components/turn-summary/TurnSummary.svelte";
  import type { SummaryEntry } from "$common-webviews/src/apps/chat/components/turn-summary/types";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import { TooltipTriggerOn } from "$common-webviews/src/design-system/_primitives/TooltipAugment/types";
  import Signpost from "$common-webviews/src/design-system/icons/signpost.svelte";
  import Revert from "$common-webviews/src/design-system/icons/revert.svelte";

  export let entries: SummaryEntry[] = [];
  export let showFooter: boolean = false;

  function handleCreateCheckpoint() {
    alert("Create checkpoint clicked");
  }

  function handleRevert() {
    alert("Revert clicked");
  }
</script>

<TurnSummary {entries}>
  <svelte:fragment slot="footer">
    {#if showFooter}
      <div class="footer-content">
        <div class="footer-section">
          <Signpost />
          <TextAugment size={1} color="secondary">Checkpoint 3 created</TextAugment>
        </div>
        <div class="footer-actions">
          <ButtonAugment size={1} variant="soft" color="neutral" on:click={handleCreateCheckpoint}>
            Create Checkpoint
          </ButtonAugment>
          <TextTooltipAugment triggerOn={[TooltipTriggerOn.Hover]}>
            <IconButtonAugment size={1} variant="ghost" color="neutral" on:click={handleRevert}>
              <Revert />
            </IconButtonAugment>
          </TextTooltipAugment>
        </div>
      </div>
    {/if}
  </svelte:fragment>
</TurnSummary>

<style>
  .footer-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--ds-spacing-2) var(--ds-spacing-3);
    gap: var(--ds-spacing-2);
  }

  .footer-section {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-1);
  }

  .footer-actions {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-1);
  }
</style>
