<!--
  GitAgentEditTabStory.svelte
  Storybook story component for GitAgentEditTab with mocked communications
-->
<script lang="ts">
  import { setContext, onMount } from "svelte";
  import { writable } from "svelte/store";
  import GitAgentEditTab from "$common-webviews/src/apps/chat/components/git-agent-edit/GitAgentEditTab.svelte";
  import { AsyncMsgSender } from "$common-webviews/src/common/hosts/async-messaging";
  import { ChatModel } from "$common-webviews/src/apps/chat/models/chat-model";
  import { SpecialContextInputModel } from "$common-webviews/src/apps/chat/models/context-model";

  import { host } from "$common-webviews/src/common/hosts/host";
  import { GitMessageType } from "@augment-internal/sidecar-libs/src/git/git-messages";
  import type {
    GitRepositoryState,
    GitFileState,
  } from "@augment-internal/sidecar-libs/src/git/git-types";

  // Story props
  export let scenario: string = "default";

  // Helper functions for StatusRow analysis

  // Create mock data based on scenario
  function createMockRepositoryState(scenario: string): GitRepositoryState {
    const baseState: GitRepositoryState = {
      headOid: "abc123def456",
      currentBranch: "main",
      workdir: "/mock/workspace",
      gitdir: "/mock/workspace/.git",
      files: [],
      isOperationInProgress: false,
      lastUpdated: Date.now(),
    };

    switch (scenario) {
      case "loading":
      case "error":
        return { ...baseState, files: [] };

      case "empty":
        return { ...baseState, files: [] };

      case "unstaged-only":
        return {
          ...baseState,
          files: [
            createMockFile("src/components/Button.tsx", "MODIFIED"),
            createMockFile("src/utils/helpers.ts", "ADDED"),
            createMockFile("README.md", "MODIFIED"),
          ],
        };

      case "staged-only":
        return {
          ...baseState,
          files: [
            createMockFile("src/components/Input.tsx", "MODIFIED", true),
            createMockFile("src/types/api.ts", "ADDED", true),
          ],
        };

      case "conflicts":
        return {
          ...baseState,
          files: [
            createMockFile("src/config.ts", "CONFLICTED"),
            createMockFile("package.json", "CONFLICTED"),
          ],
        };

      case "large-repo":
        return {
          ...baseState,
          files: Array.from({ length: 50 }, (_, i) =>
            createMockFile(
              `src/components/Component${i}.tsx`,
              i % 3 === 0 ? "ADDED" : "MODIFIED",
              i % 4 === 0,
            ),
          ),
        };

      case "all-status-types":
        return {
          ...baseState,
          files: [
            // Unstaged files
            createMockFile("src/components/NewComponent.tsx", "ADDED"),
            createMockFile("src/components/ModifiedComponent.tsx", "MODIFIED"),
            createMockFile("src/components/DeletedComponent.tsx", "DELETED"),
            createMockFile("src/components/RenamedComponent.tsx", "RENAMED"),
            createMockFile("src/components/CopiedComponent.tsx", "COPIED"),
            // Staged files
            createMockFile("src/utils/newUtil.ts", "ADDED", true),
            createMockFile("src/utils/modifiedUtil.ts", "MODIFIED", true),
            createMockFile("src/utils/deletedUtil.ts", "DELETED", true),
          ],
        };

      default: // "default"
        return {
          ...baseState,
          files: [
            createMockFile("src/components/Header.tsx", "MODIFIED"),
            createMockFile("src/components/Footer.tsx", "ADDED"),
            createMockFile("src/styles/main.css", "DELETED"),
            createMockFile("src/utils/auth.ts", "MODIFIED", true),
            createMockFile("src/types/user.ts", "ADDED", true),
          ],
        };
    }
  }

  function createMockFile(path: string, status: string, isStaged = false): GitFileState {
    // StatusRow format: [filepath, head, workdir, stage]
    // head: 0=absent, 1=present
    // workdir: 0=absent, 1=present, 2=modified
    // stage: 0=absent, 1=present, 2=modified, 3=added
    let statusRow: [string, 0 | 1, 0 | 1 | 2, 0 | 1 | 2 | 3];

    switch (status) {
      case "ADDED":
        statusRow = [path, 0, 2, isStaged ? 3 : 0];
        break;
      case "MODIFIED":
        statusRow = [path, 1, 2, isStaged ? 2 : 0];
        break;
      case "DELETED":
        statusRow = [path, 1, 0, isStaged ? 0 : 0];
        break;
      case "RENAMED":
        statusRow = [path, 1, 2, isStaged ? 2 : 0]; // Treat as modified for simplicity
        break;
      case "COPIED":
        statusRow = [path, 1, 2, isStaged ? 2 : 0]; // Treat as modified for simplicity
        break;
      case "CONFLICTED":
        statusRow = [path, 1, 2, 2]; // Conflicted state
        break;
      default:
        statusRow = [path, 1, 1, isStaged ? 1 : 0];
    }

    // Generate realistic change summaries based on file type and status
    let changesSummary;
    if (status !== "DELETED") {
      const isLargeFile = path.includes("Component") || path.includes("package.json");
      const baseAdded = isLargeFile
        ? Math.floor(Math.random() * 50) + 10
        : Math.floor(Math.random() * 20) + 1;
      const baseRemoved = status === "ADDED" ? 0 : Math.floor(Math.random() * 15);

      changesSummary = {
        totalAddedLines: baseAdded,
        totalRemovedLines: baseRemoved,
        unstagedChanges: {
          addedLines: baseAdded,
          removedLines: baseRemoved,
        },
        stagedChanges: isStaged
          ? {
              addedLines: baseAdded,
              removedLines: baseRemoved,
            }
          : undefined,
      };
    }

    return {
      qualifiedPathName: {
        rootPath: "/mock/workspace",
        relPath: path,
      },
      statusRow,
      changesSummary,
    };
  }

  // Create mock async message sender with git operations
  const mockAsyncMsgSender = new AsyncMsgSender((message) => {
    console.log("Mock message sent:", message);

    // Handle wrapped sidecar messages (from sendToSidecar)
    if (
      message.type === "async-wrapper" &&
      message.destination === "sidecar" &&
      message.baseMsg?.type?.startsWith?.("git.")
    ) {
      const gitMessage = message.baseMsg as any;
      const response = handleMockGitMessage(gitMessage);
      if (response) {
        // Simulate response from extension/sidecar immediately
        setTimeout(() => {
          window.postMessage({
            type: "async-wrapper",
            requestId: message.requestId,
            error: null,
            baseMsg: response,
            destination: "sidecar",
          });
        }, 10); // Very fast response to prevent timeouts
      }
    }
    // Handle direct git messages (fallback)
    else if (message.type?.startsWith?.("git.")) {
      const gitMessage = message as any;
      const response = handleMockGitMessage(gitMessage);
      if (response) {
        // Simulate response from extension/sidecar immediately
        setTimeout(() => {
          window.postMessage({
            ...response,
            requestId: message.requestId,
          });
        }, 10); // Very fast response to prevent timeouts
      }
    }
  });

  // State management for dynamic updates
  let currentMockState = createMockRepositoryState(scenario);

  // Helper function to update file status and broadcast changes
  function updateFileStatus(filePath: string, newStage: 0 | 1 | 2 | 3) {
    currentMockState.files = currentMockState.files.map((file) => {
      if (file.qualifiedPathName.relPath === filePath) {
        const [filepath, head, workdir] = file.statusRow;
        const newStatusRow: [string, 0 | 1, 0 | 1 | 2, 0 | 1 | 2 | 3] = [
          filepath,
          head,
          workdir,
          newStage,
        ];
        return { ...file, statusRow: newStatusRow };
      }
      return file;
    });

    // Update the lastUpdated timestamp
    currentMockState.lastUpdated = Date.now();

    // Broadcast state change
    setTimeout(() => {
      window.postMessage({
        type: "git.repositoryStateChanged",
        data: currentMockState,
      });
    }, 50);
  }

  function handleMockGitMessage(message: any) {
    console.log("Handling mock git message:", message.type, message.data);

    switch (message.type as GitMessageType) {
      case GitMessageType.REPO_STATE_REQUEST:
        // Simulate repository state response
        if (scenario === "error") {
          return {
            type: GitMessageType.REPO_STATE_RESPONSE,
            error: "Failed to access git repository",
          };
        } else {
          return {
            type: GitMessageType.REPO_STATE_RESPONSE,
            data: currentMockState,
          };
        }

      case GitMessageType.FILE_STATUS_REQUEST:
        // Simulate status matrix response
        return {
          type: GitMessageType.FILE_STATUS_RESPONSE,
          data: { statusMatrix: currentMockState.files },
        };

      case GitMessageType.FILE_STAGE_REQUEST: {
        console.log("Mock staging files:", message.data?.filePaths);
        const filesToStage = message.data?.filePaths || [];

        // Stage each file individually
        filesToStage.forEach((filePath: string) => {
          const file = currentMockState.files.find((f) => f.qualifiedPathName.relPath === filePath);
          if (file) {
            const [, head, workdir] = file.statusRow;
            // Determine appropriate stage value based on workdir status
            let newStage: 0 | 1 | 2 | 3;
            if (head === 0 && workdir === 2) {
              newStage = 3; // New file (added)
            } else if (head === 1 && workdir === 2) {
              newStage = 2; // Modified file
            } else if (head === 1 && workdir === 0) {
              newStage = 0; // Deleted file (special case)
            } else {
              newStage = 1; // Default staged
            }
            updateFileStatus(filePath, newStage);
          }
        });

        return {
          type: GitMessageType.FILE_STAGE_RESPONSE,
          data: { success: true, stagedFiles: filesToStage },
        };
      }

      case GitMessageType.FILE_UNSTAGE_REQUEST: {
        console.log("Mock unstaging files:", message.data?.filePaths);
        const filesToUnstage = message.data?.filePaths || [];

        // Unstage each file individually
        filesToUnstage.forEach((filePath: string) => {
          updateFileStatus(filePath, 0); // Set stage to 0 (unstaged)
        });

        return {
          type: GitMessageType.FILE_UNSTAGE_RESPONSE,
          data: { success: true, unstagedFiles: filesToUnstage },
        };
      }

      default:
        console.warn("Unhandled mock git message type:", message.type);
        return null;
    }
  }

  // Create mock models
  const chatModel = new ChatModel(mockAsyncMsgSender, host, new SpecialContextInputModel());

  // Create a simplified mock agent conversation model
  const mockAgentConversationModel = {
    isCurrConversationAgentic: writable(scenario !== "non-agentic"),
    // Add other required properties as needed
  };

  // Set contexts for the component
  setContext("chatModel", chatModel);
  setContext("agentConversationModel", mockAgentConversationModel);
  setContext("asyncMsgSender", mockAsyncMsgSender);
  setContext("flagsModel", chatModel.flags);

  // Simulate initialization delay for loading state
  let showComponent = scenario !== "loading";

  onMount(() => {
    if (scenario === "loading") {
      setTimeout(() => {
        showComponent = true;
      }, 2000);
    }
  });
</script>

<div class="story-container">
  <div class="story-header">
    <h3>GitAgentEditTab - {scenario}</h3>
    <p class="story-description">
      {#if scenario === "loading"}
        Demonstrating loading state while git context initializes
      {:else if scenario === "error"}
        Demonstrating error state when git operations fail
      {:else if scenario === "empty"}
        Demonstrating empty state with no changes
      {:else if scenario === "unstaged-only"}
        Repository with only unstaged changes
      {:else if scenario === "staged-only"}
        Repository with only staged changes ready for commit
      {:else if scenario === "conflicts"}
        Repository with merge conflicts requiring resolution
      {:else if scenario === "large-repo"}
        Large repository with many files to test performance
      {:else if scenario === "all-status-types"}
        Repository showing all git file status types with colored icons (added=green, deleted=red,
        moved=blue)
      {:else if scenario === "non-agentic"}
        Component behavior in non-agentic conversation
      {:else}
        Default state with mixed unstaged and staged changes
      {/if}
    </p>
  </div>

  <div class="story-content">
    {#if showComponent}
      <GitAgentEditTab />
    {:else}
      <div class="loading-placeholder">
        <p>Initializing git interface...</p>
      </div>
    {/if}
  </div>
</div>

<style>
  .story-container {
    height: 600px;
    display: flex;
    flex-direction: column;
    border: 1px solid var(--ds-color-neutral-a6);
    border-radius: var(--ds-radius-2);
    overflow: hidden;
  }

  .story-header {
    padding: var(--ds-spacing-3);
    background-color: var(--ds-color-neutral-a2);
    border-bottom: 1px solid var(--ds-color-neutral-a6);
  }

  .story-header h3 {
    margin: 0 0 var(--ds-spacing-1) 0;
    font-size: var(--ds-font-size-3);
    font-weight: var(--ds-font-weight-semibold);
  }

  .story-description {
    margin: 0;
    font-size: var(--ds-font-size-1);
    color: var(--ds-color-neutral-11);
  }

  .story-content {
    flex: 1;
    overflow: hidden;
  }

  .loading-placeholder {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    color: var(--ds-color-neutral-11);
  }
</style>
