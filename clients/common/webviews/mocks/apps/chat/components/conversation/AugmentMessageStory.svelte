<script lang="ts">
  import ColumnLayout from "$common-webviews/mocks/storybook/ColumnLayout.svelte";
  import AugmentMessage from "$common-webviews/src/apps/chat/components/conversation/AugmentMessage.svelte";
  import { ActionsModel } from "$common-webviews/src/apps/chat/models/actions-model";
  import { ChatModel } from "$common-webviews/src/apps/chat/models/chat-model";
  import { SpecialContextInputModel } from "$common-webviews/src/apps/chat/models/context-model";
  import {
    ExchangeStatus,
    type ExchangeWithStatus,
  } from "$common-webviews/src/apps/chat/types/chat-message";
  import { host } from "$common-webviews/src/common/hosts/host";
  import { MessageBroker } from "$common-webviews/src/common/utils/message-broker";
  import { ChatResultNodeType } from "@augment-internal/sidecar-libs/src/chat/chat-types";

  const markdown = `
This is an example markdown response.


    function hellowWorld() {
      return "Hello World";
    }

This is a second example markdown response.

- This is a list item
- This is another list item
  - This is a nested list item
  - This is another nested list item
- This is a third list item
- This is a fourth list item
`;

  const turnSent: ExchangeWithStatus = {
    status: ExchangeStatus.sent,
    /* eslint-disable @typescript-eslint/naming-convention */
    request_message: "Hello",
    request_id: "1234",
    /* eslint-enable @typescript-eslint/naming-convention */
  };

  const turnSuccess: ExchangeWithStatus = {
    status: ExchangeStatus.success,
    /* eslint-disable @typescript-eslint/naming-convention */
    request_message: "Hello",
    request_id: "1234",
    structured_output_nodes: [
      {
        id: 0,
        type: ChatResultNodeType.SUGGESTED_QUESTIONS,
        content: "This is a sample question\nThis is another question",
      },
    ],
    /* eslint-enable @typescript-eslint/naming-convention */
  };

  const msgBroker = new MessageBroker(host);
  let contextModel = new SpecialContextInputModel();
  msgBroker.registerConsumer(contextModel);
  let actionsModel = new ActionsModel();
  msgBroker.registerConsumer(actionsModel);
  let chatModel = new ChatModel(msgBroker, host, contextModel);
</script>

<ColumnLayout>
  <AugmentMessage markdown="This is a test message" />

  <AugmentMessage markdown={""} turn={turnSent} />

  <AugmentMessage {markdown} />

  <AugmentMessage {markdown} turn={turnSuccess} {chatModel} requestId="1234" isLastTurn={true} />
</ColumnLayout>
