/* eslint-disable @typescript-eslint/naming-convention */
import type { <PERSON><PERSON>, StoryObj } from "@storybook/svelte-vite";
import CheckpointVersionList from "$common-webviews/src/apps/chat/components/conversation/checkpoints/CheckpointVersionList.svelte";
import { setContext } from "svelte";
import { writable, derived, readable } from "svelte/store";
import {
  ChatItemType,
  ExchangeStatus,
  type AgenticCheckpointDelimiter,
  type AgenticRevertTargetInfo,
} from "$common-webviews/src/apps/chat/types/chat-message";
import type { ICheckpointStore } from "$common-webviews/src/apps/chat/models/checkpoint-store";
import type { ChatModel } from "$common-webviews/src/apps/chat/models/chat-model";
import type { ChatAgentEdit } from "@augment-internal/sidecar-libs/src/webview-messages/message-types/agent-messages";
import type { IQualifiedPathName } from "@augment-internal/sidecar-libs/src/workspace/workspace-types";

const meta = {
  title: "app/chat/components/CheckpointVersionList",
  component: CheckpointVersionList,
  parameters: {
    docs: {
      description: {
        component: `
CheckpointVersionList displays a collapsible list of file changes for a specific checkpoint in an agent conversation.

## Features
- Shows checkpoint number, timestamp, and file count
- Displays file changes with added/removed line counts
- Supports revert checkpoints with special messaging
- Collapsible interface with lazy loading of file details
- Interactive file and review buttons
- Target checkpoint highlighting

## Usage
This component is used within agent conversations to show users what changes were made at each checkpoint, allowing them to track progress and revert to previous states if needed.
        `,
      },
    },
  },
  argTypes: {
    turn: {
      control: false,
      description: "The checkpoint delimiter to display",
    },
  },
} satisfies Meta<CheckpointVersionList>;

export default meta;

type Story = StoryObj<typeof meta>;

// Mock data
const mockQualifiedPath1: IQualifiedPathName = {
  rootPath: "/Users/<USER>/project",
  relPath: "src/components/Button.tsx",
};

const mockQualifiedPath2: IQualifiedPathName = {
  rootPath: "/Users/<USER>/project",
  relPath: "src/utils/helpers.ts",
};

const mockQualifiedPath3: IQualifiedPathName = {
  rootPath: "/Users/<USER>/project",
  relPath: "README.md",
};

const mockCheckpointSummary: ChatAgentEdit[] = [
  {
    qualifiedPathName: mockQualifiedPath1,
    changesSummary: {
      totalAddedLines: 15,
      totalRemovedLines: 3,
    },
  },
  {
    qualifiedPathName: mockQualifiedPath2,
    changesSummary: {
      totalAddedLines: 8,
      totalRemovedLines: 12,
    },
  },
  {
    qualifiedPathName: mockQualifiedPath3,
    changesSummary: {
      totalAddedLines: 2,
      totalRemovedLines: 0,
    },
  },
];

const mockEmptyCheckpointSummary: ChatAgentEdit[] = [];

const createMockCheckpoint = (
  index: number,
  isRevert = false,
  revertTarget?: AgenticRevertTargetInfo,
): AgenticCheckpointDelimiter => ({
  uuid: `checkpoint-${index}`,
  chatItemType: ChatItemType.agenticCheckpointDelimiter,
  status: ExchangeStatus.success,
  // Use MAX_SAFE_INTEGER so the UI renders a stable "Now" timestamp
  fromTimestamp: Number.MAX_SAFE_INTEGER - 120000,
  toTimestamp: Number.MAX_SAFE_INTEGER,
  revertTarget: isRevert ? revertTarget : undefined,
});

function createMockContext(
  checkpoints: AgenticCheckpointDelimiter[],
  targetCheckpointIdx?: number,
  checkpointCount = checkpoints.length,
) {
  const checkpointsStore = readable(checkpoints);
  const totalCheckpointCount = readable(checkpointCount);
  const targetCheckpointIdxStore = writable(targetCheckpointIdx);

  const uuidToIdx = derived(checkpointsStore, ($checkpoints) => {
    const map = new Map<string, number>();
    $checkpoints.forEach((checkpoint, index) => {
      map.set(checkpoint.uuid, index);
    });
    return map;
  });

  const targetCheckpoint = derived(
    [checkpointsStore, targetCheckpointIdxStore],
    ([$checkpoints, $targetIdx]) => {
      if ($targetIdx === undefined) return $checkpoints[$checkpoints.length - 1];
      return $checkpoints[$targetIdx];
    },
  );

  const isTargetCheckpointLatest = derived(
    [totalCheckpointCount, targetCheckpointIdxStore],
    ([$total, $targetIdx]) => {
      return $targetIdx === undefined || $targetIdx === $total - 1;
    },
  );

  const targetCheckpointSummary = readable<ChatAgentEdit[]>([]);

  const mockCheckpointStore: ICheckpointStore = {
    checkpoints: checkpointsStore,
    totalCheckpointCount,
    targetCheckpointIdx: targetCheckpointIdxStore,
    uuidToIdx,
    targetCheckpoint,
    isTargetCheckpointLatest,
    targetCheckpointSummary,

    getCheckpointIdxForTimestamp(timestamp: number): number | undefined {
      for (let i = 0; i < checkpoints.length; i++) {
        const checkpoint = checkpoints[i];
        if (timestamp >= checkpoint.fromTimestamp && timestamp <= checkpoint.toTimestamp) {
          return i;
        }
      }
      return undefined;
    },

    getCheckpointAtIdx(idx: number): AgenticCheckpointDelimiter | undefined {
      return checkpoints[idx];
    },

    async getCheckpointSummary(checkpoint: AgenticCheckpointDelimiter): Promise<ChatAgentEdit[]> {
      // Resolve immediately to avoid timing flakiness in stories
      // (the real component lazy-load logic is tested elsewhere)
      // await new Promise((resolve) => setTimeout(resolve, 500));

      // Return empty summary for revert checkpoints, full summary for others
      const checkpointIndex = checkpoints.findIndex((c) => c.uuid === checkpoint.uuid);
      if (checkpoint.revertTarget || checkpointIndex === 0) {
        return mockEmptyCheckpointSummary;
      }
      return mockCheckpointSummary;
    },

    async getCheckpointIdxSummary(idx: number): Promise<ChatAgentEdit[]> {
      const checkpoint = checkpoints[idx];
      if (!checkpoint) return [];
      return this.getCheckpointSummary(checkpoint);
    },

    createNewCheckpoint(_options?: Partial<AgenticCheckpointDelimiter>): void {
      console.log("Creating new checkpoint");
    },

    async revertToCheckpoint(uuid: string): Promise<boolean> {
      console.log(`Reverting to checkpoint: ${uuid}`);
      return true;
    },
  };

  const mockChatModel: Partial<ChatModel> = {
    extensionClient: {
      openFile: (params: { repoRoot: string; pathName: string; allowOutOfWorkspace?: boolean }) => {
        console.log(`Opening file: ${params.pathName} in ${params.repoRoot}`);
      },
      showAgentReview: (
        qualifiedPathName: IQualifiedPathName,
        _fromTimestamp: number,
        _toTimestamp: number,
        _allowOutOfWorkspace: boolean,
      ) => {
        console.log(`Showing agent review for: ${qualifiedPathName.relPath}`);
      },
    },
  } as ChatModel;

  return { mockCheckpointStore, mockChatModel };
}

export const RegularCheckpoint: Story = {
  decorators: [
    (Story) => {
      const checkpoints = [createMockCheckpoint(1)];
      const { mockCheckpointStore, mockChatModel } = createMockContext(checkpoints, undefined, 2);

      setContext("checkpointStore", mockCheckpointStore);
      setContext("chatModel", mockChatModel);

      return Story();
    },
  ],
  args: {
    turn: createMockCheckpoint(1),
  },
};

export const CheckpointWithChanges: Story = {
  decorators: [
    (Story) => {
      const checkpoints = [createMockCheckpoint(0), createMockCheckpoint(1)];
      const { mockCheckpointStore, mockChatModel } = createMockContext(checkpoints, 1, 1);

      setContext("checkpointStore", mockCheckpointStore);
      setContext("chatModel", mockChatModel);

      return Story();
    },
  ],
  args: {
    turn: createMockCheckpoint(1),
  },
  parameters: {
    docs: {
      description: {
        story: "Shows a checkpoint with file changes that will load after expanding.",
      },
    },
  },
};

export const TargetCheckpoint: Story = {
  decorators: [
    (Story) => {
      const checkpoints = [
        createMockCheckpoint(0),
        createMockCheckpoint(1),
        createMockCheckpoint(2),
      ];
      const { mockCheckpointStore, mockChatModel } = createMockContext(checkpoints, 1);

      setContext("checkpointStore", mockCheckpointStore);
      setContext("chatModel", mockChatModel);

      return Story();
    },
  ],
  args: {
    turn: createMockCheckpoint(1),
  },
  parameters: {
    docs: {
      description: {
        story: "Shows a checkpoint highlighted as the target checkpoint.",
      },
    },
  },
};

export const RevertCheckpoint: Story = {
  decorators: [
    (Story) => {
      const checkpoints = [
        createMockCheckpoint(0),
        createMockCheckpoint(1),
        createMockCheckpoint(2, true, { uuid: "checkpoint-1" }),
      ];
      const { mockCheckpointStore, mockChatModel } = createMockContext(checkpoints, 2);

      setContext("checkpointStore", mockCheckpointStore);
      setContext("chatModel", mockChatModel);

      return Story();
    },
  ],
  args: {
    turn: createMockCheckpoint(2, true, { uuid: "checkpoint-1" }),
  },
  parameters: {
    docs: {
      description: {
        story:
          "Shows a revert checkpoint with special messaging indicating it was reverted from another checkpoint.",
      },
    },
  },
};

export const EmptyCheckpoint: Story = {
  decorators: [
    (Story) => {
      const checkpoints = [createMockCheckpoint(0)];
      const { mockCheckpointStore, mockChatModel } = createMockContext(checkpoints);

      setContext("checkpointStore", mockCheckpointStore);
      setContext("chatModel", mockChatModel);

      return Story();
    },
  ],
  args: {
    turn: createMockCheckpoint(0),
  },
  parameters: {
    docs: {
      description: {
        story: "Shows a checkpoint with no file changes (like the first checkpoint).",
      },
    },
  },
};
