<script lang="ts">
  import ColumnLayout from "$common-webviews/mocks/storybook/ColumnLayout.svelte";
  import UserMessage from "$common-webviews/src/apps/chat/components/conversation/UserMessage.svelte";
  import { ActionsModel } from "$common-webviews/src/apps/chat/models/actions-model";
  import { ChatModel } from "$common-webviews/src/apps/chat/models/chat-model";
  import { SpecialContextInputModel } from "$common-webviews/src/apps/chat/models/context-model";
  import { host } from "$common-webviews/src/common/hosts/host";
  import { MessageBroker } from "$common-webviews/src/common/utils/message-broker";

  const msgBroker = new MessageBroker(host);
  let contextModel = new SpecialContextInputModel();
  msgBroker.registerConsumer(contextModel);
  let actionsModel = new ActionsModel();
  msgBroker.registerConsumer(actionsModel);
  let chatModel = new ChatModel(msgBroker, host, contextModel, {
    initialFlags: { fullFeatured: true, enableEditableHistory: true },
  });
</script>

<ColumnLayout>
  <UserMessage {chatModel} msg="This is a test message" />

  <UserMessage requestId="1234" {chatModel} msg="This is a test message" />
</ColumnLayout>
