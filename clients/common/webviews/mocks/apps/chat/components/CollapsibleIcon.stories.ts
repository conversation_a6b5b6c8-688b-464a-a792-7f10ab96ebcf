/* eslint-disable @typescript-eslint/naming-convention */
import type { Meta, StoryObj } from "@storybook/svelte";
import component from "./CollapsibleIconStory.svelte";

/**
 * CollapsibleIcon is a component that displays an icon with optional expand/collapse functionality.
 *
 * ## Features:
 * - Shows a tooltip indicating "Expand" or "Collapse" when expandable
 * - Displays an expand icon overlay on hover when expandable
 * - Supports any icon through the "icon" slot
 * - Can be disabled (not expandable) for static display
 *
 * ## Usage:
 * Used in chat components and other UI elements where items can be expanded or collapsed,
 * providing visual feedback about the current state and available actions.
 */
const meta = {
  title: "app/Chat/components/CollapsibleIcon",
  component,
  tags: ["autodocs"],
  argTypes: {
    expandable: {
      control: "boolean",
      description: "Whether the icon can be expanded/collapsed",
      defaultValue: true,
    },
    collapsed: {
      control: "boolean",
      description: "Whether the icon is currently collapsed",
      defaultValue: false,
    },
    iconType: {
      control: "select",
      options: ["file", "folder", "gear", "code", "terminal"],
      description: "Type of icon to display in the slot",
      defaultValue: "file",
    },
  },
} satisfies Meta<component>;

export default meta;

type Story = StoryObj<typeof meta>;

// Default state - expandable and not collapsed
export const Default: Story = {
  args: {
    expandable: true,
    collapsed: false,
    iconType: "file",
  },
};

// Collapsed state
export const Collapsed: Story = {
  args: {
    expandable: true,
    collapsed: true,
    iconType: "file",
  },
};

// Not expandable - no expand/collapse functionality
export const NotExpandable: Story = {
  args: {
    expandable: false,
    collapsed: false,
    iconType: "gear",
  },
};

// Different icon types
export const WithFolderIcon: Story = {
  args: {
    expandable: true,
    collapsed: false,
    iconType: "folder",
  },
};

export const WithCodeIcon: Story = {
  args: {
    expandable: true,
    collapsed: true,
    iconType: "code",
  },
};

export const WithTerminalIcon: Story = {
  args: {
    expandable: false,
    collapsed: false,
    iconType: "terminal",
  },
};

// Interactive example showing all states
export const AllStates: Story = {
  args: {
    showAllStates: true,
  },
};
