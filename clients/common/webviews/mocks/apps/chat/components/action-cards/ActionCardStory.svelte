<script lang="ts">
  import ColumnLayout from "$common-webviews/mocks/storybook/ColumnLayout.svelte";
  import ActionCard from "$common-webviews/src/apps/chat/components/action-cards/ActionCard.svelte";
  import { ChatFlagsModel } from "$common-webviews/src/apps/chat/models/chat-flags-model";
  import { DerivedStateName, UIOnlyActionName } from "$vscode/src/main-panel/action-cards/types";
  import ButtonsActionCard from "$common-webviews/src/apps/chat/components/action-cards/ButtonsActionCard.svelte";
  const flagsModel = new ChatFlagsModel();

  const cards = [
    DerivedStateName.signInRequired,
    UIOnlyActionName.signInInProgress,
    DerivedStateName.disableCopilot,
    DerivedStateName.disableCodeium,
    DerivedStateName.workspaceNotPopulated,
    DerivedStateName.syncingPermissionNeeded,
    DerivedStateName.workspaceTooLarge,
    DerivedStateName.uploadingHomeDir,
  ];
</script>

<ColumnLayout>
  {#each cards as card}
    <h3>{card}</h3>
    <ActionCard {card} {flagsModel} />
  {/each}
</ColumnLayout>

<ColumnLayout>
  <h3>ButtonAction Component</h3>

  <ButtonsActionCard
    description="This is an example description of the card"
    color="accent"
    buttons={[
      {
        text: "Example Button",
        action: "example-action",
        appearance: "neutral",
      },
      {
        text: "Example Button 2",
        action: "example-action-2",
      },
    ]}
  />
</ColumnLayout>
