<script lang="ts">
  import ColumnLayout from "$common-webviews/mocks/storybook/ColumnLayout.svelte";
  import {
    createCopyToClipboardAction,
    createGoToFileAction,
    createInsertIntoNewFileAction,
  } from "$common-webviews/src/apps/chat/components/markdown-ext/codeblock/codeblockActions";
  import CodeblockSuccessfulButton from "$common-webviews/src/apps/chat/components/markdown-ext/codeblock/CodeblockSuccessfulButton.svelte";
  import { createSmartPasteAction } from "$common-webviews/src/apps/chat/components/markdown-ext/codeblock/smartPaste";
  import { ChatModel } from "$common-webviews/src/apps/chat/models/chat-model";
  import { SpecialContextInputModel } from "$common-webviews/src/apps/chat/models/context-model";
  import { AsyncMsgSender } from "$common-webviews/src/common/hosts/async-messaging";
  import { host } from "$common-webviews/src/common/hosts/host";

  // Create mock action buttons for each action type
  const copyButton = createCopyToClipboardAction(() => "code");
  const insertButton = createInsertIntoNewFileAction(() => "code", undefined, undefined);
  const goToButton = createGoToFileAction("code", false, undefined, undefined);
  const chatModel = new ChatModel(
    new AsyncMsgSender(() => {}),
    host,
    new SpecialContextInputModel(),
    {
      initialFlags: { enableSmartPaste: true, enableDirectApply: true },
    },
  );
  const { smartPasteButton, directApplyButton } = createSmartPasteAction(
    chatModel,
    undefined,
    {
      text: "code",
      type: "code",
      raw: "",
    },
    "requestId",
    true,
    true,
    true,
    true,
    () => false,
    () => {},
  );
</script>

<ColumnLayout>
  <h3>Copy Button</h3>
  <CodeblockSuccessfulButton primaryButton={copyButton} />

  <h3>Insert Button</h3>
  <CodeblockSuccessfulButton primaryButton={insertButton} />

  <h3>Smart Paste Button</h3>
  <CodeblockSuccessfulButton primaryButton={smartPasteButton} />

  <h3>Go To File Button</h3>
  <CodeblockSuccessfulButton primaryButton={goToButton} />

  {#if directApplyButton}
    <h3>Direct Apply Button</h3>
    <CodeblockSuccessfulButton primaryButton={directApplyButton} />
  {/if}
</ColumnLayout>
