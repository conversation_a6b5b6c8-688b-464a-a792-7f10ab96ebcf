<script lang="ts">
  import ColumnLayout from "$common-webviews/mocks/storybook/ColumnLayout.svelte";
  import Codeblock from "$common-webviews/src/apps/chat/components/markdown-ext/codeblock/Codeblock.svelte";
  import { type ICodeblockMetadata } from "$common-webviews/src/apps/chat/components/markdown-ext/utils";
  import { ChatModel } from "$common-webviews/src/apps/chat/models/chat-model";
  import { SpecialContextInputModel } from "$common-webviews/src/apps/chat/models/context-model";
  import { AsyncMsgSender } from "$common-webviews/src/common/hosts/async-messaging";
  import { host } from "$common-webviews/src/common/hosts/host";
  import { type Tokens } from "marked";
  import { setContext } from "svelte";

  const chatModel = new ChatModel(
    new AsyncMsgSender(() => {}),
    host,
    new SpecialContextInputModel(),
    {
      initialFlags: { enableSmartPaste: true, enableDirectApply: true },
    },
  );

  const VALID_PATHS = ["foobar.ts"];
  chatModel.extensionClient.resolvePath = (path) => {
    // Check if the relPath exists in any of the METADATA entries
    const isValidPath = VALID_PATHS.includes(path.relPath);

    // Return a valid FileDetails object for matching paths
    return Promise.resolve(
      isValidPath
        ? {
            repoRoot: "/mock-repo-root",
            pathName: path.relPath || "",
          }
        : undefined,
    );
  };

  // Set up the chatModel in the context for the Codeblock component to use
  setContext("chatModel", chatModel);

  const TOKEN = {
    text: `export function foobar() {
    console.log("hello world");
}`,
    lang: "typescript",
    type: "code",
    raw: '```typescript\nexport function foobar() {\n    console.log("hello world");\n}\n```',
  } as Tokens.Code;

  /* eslint-disable @typescript-eslint/naming-convention */
  const METADATA: Record<string, ICodeblockMetadata> = {
    "no path": {
      language: "typescript",
      relPath: null,
      mode: "EDIT",
    },
    "file name exists": {
      language: "typescript",
      relPath: "foobar.ts",
      mode: "EDIT",
    },
    "python file": {
      language: "python",
      relPath: "new_foobar.py",
      mode: "EDIT",
    },
    "file name new": {
      language: "typescript",
      relPath: "new_foobar.tsx",
      mode: "EDIT",
    },
    "file path": {
      language: "typescript",
      relPath: "src/utils/foobar.ts",
      mode: "EDIT",
    },
    "file name - long": {
      language: "typescript",
      relPath: "thisisareallylongfilenamethatgoesonseeminglyindefinitelyfoobar.ts",
      mode: "EDIT",
    },
    "file path - long": {
      language: "typescript",
      relPath:
        "clients/common/webviews/src/apps/chat/components/markdown-ext/this/is/a/really/long/path/that/goes/on/seemingly/indefinitely/foobar.ts",
      mode: "EDIT",
    },
    "file name and path - long": {
      language: "typescript",
      relPath:
        "clients/common/webviews/src/apps/chat/components/markdown-ext/this/is/a/really/long/path/that/goes/on/seemingly/indefinitely/thisisareallylongfilenamethatgoesonseeminglyindefinitelyfoobar.ts",
      mode: "EDIT",
    },
    "file name and path - short": {
      language: "typescript",
      relPath: "foo/bar.ts",
      mode: "EDIT",
    },
  };
  /* eslint-enable @typescript-eslint/naming-convention */
</script>

<ColumnLayout>
  <h1>Codeblock</h1>
  {#each Object.entries(METADATA) as [key, metadata]}
    <h3 class="codeblock">{key}</h3>
    <Codeblock token={TOKEN} codeblockMetadata={metadata}></Codeblock>
  {/each}
</ColumnLayout>
