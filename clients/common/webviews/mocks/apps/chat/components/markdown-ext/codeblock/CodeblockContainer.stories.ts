/* eslint-disable @typescript-eslint/naming-convention */
import type { Meta, StoryObj } from "@storybook/svelte-vite";
import component from "./CodeblockContainerStory.svelte";

const meta = {
  title: "app/Chat/components/markdown-ext/codeblock/CodeblockContainer",
  component,
  tags: ["autodocs"],
  argTypes: {
    primaryButtonType: {
      control: { type: "select" },
      options: ["copy", "insert", "goToFile", "smartPaste", "directApply"],
      description: "The type of primary button to display",
      defaultValue: "copy",
    },
    collapsed: {
      control: { type: "boolean" },
      description: "Whether the codeblock is collapsed",
      defaultValue: false,
    },
    isLoading: {
      control: { type: "boolean" },
      description: "Whether the codeblock is in loading state",
      defaultValue: false,
    },
    showExpandButton: {
      control: { type: "boolean" },
      description:
        "Whether to show the expand button demo (manually sets truncate=true to demonstrate the shared ExpandButton component)",
      defaultValue: false,
    },
  },
} satisfies Meta<component>;

export default meta;

type Story = StoryObj<typeof meta>;

// More on writing stories with args: https://storybook.js.org/docs/writing-stories/args
export const Example: Story = {
  args: {
    primaryButtonType: "copy",
    collapsed: false,
    isLoading: false,
  },
};

export const WithExpandButton: Story = {
  args: {
    primaryButtonType: "copy",
    collapsed: false,
    isLoading: false,
    showExpandButton: true,
  },
};
