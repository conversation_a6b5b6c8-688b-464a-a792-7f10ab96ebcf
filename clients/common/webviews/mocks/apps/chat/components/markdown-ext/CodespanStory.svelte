<script lang="ts">
  import ColumnLayout from "$common-webviews/mocks/storybook/ColumnLayout.svelte";
  import Codespan from "$common-webviews/src/apps/chat/components/markdown-ext/Codespan.svelte";
  import { ChatModel } from "$common-webviews/src/apps/chat/models/chat-model";
  import { SpecialContextInputModel } from "$common-webviews/src/apps/chat/models/context-model";
  import { AsyncMsgSender } from "$common-webviews/src/common/hosts/async-messaging";
  import CodespanPresentation from "$common-webviews/src/apps/chat/components/markdown-ext/CodespanPresentation.svelte";
  import type { Tokens } from "marked";
  import { host } from "$common-webviews/src/common/hosts/host";
  import File from "$common-webviews/src/design-system/icons/vscode/file.svelte";
  import FolderOpened from "$common-webviews/src/design-system/icons/vscode/folder-opened.svelte";
  import SymbolNamespace from "$common-webviews/src/design-system/icons/vscode/symbol-namespace.svelte";
  import { setChatModel } from "$common-webviews/src/apps/chat/chat-context";

  const chatModel = new ChatModel(
    new AsyncMsgSender(host.postMessage),
    host,
    new SpecialContextInputModel(),
  );
  setChatModel(chatModel);

  const unknownFileToken: Tokens.Codespan = {
    type: "codespan",
    raw: "`unknown-file.ts`",
    text: "unknown-file.ts",
  };
</script>

<ColumnLayout>
  <Codespan token={unknownFileToken} />
</ColumnLayout>

<ColumnLayout>
  <CodespanPresentation
    linkable={false}
    token={{
      type: "codespan",
      raw: "`unknown-file.ts`",
      text: "unknown-file.ts",
    }}
  >
    Contents
  </CodespanPresentation>

  <CodespanPresentation
    linkable={true}
    token={{
      type: "codespan",
      raw: "`known-file.ts`",
      text: "known-file.ts",
    }}
  >
    <File slot="icon" />
    known-file.ts
  </CodespanPresentation>

  <CodespanPresentation
    linkable={false}
    token={{
      type: "codespan",
      raw: "`example/known/dir`",
      text: "example/known/dir",
    }}
  >
    <FolderOpened slot="icon" />
    example/known/dir
  </CodespanPresentation>

  <CodespanPresentation
    linkable={true}
    token={{
      type: "codespan",
      raw: "`symbol()`",
      text: "symbol()",
    }}
  >
    <SymbolNamespace slot="icon" />
    symbol()
  </CodespanPresentation>

  <div class="limit-width">
    <CodespanPresentation
      linkable={true}
      token={{
        type: "codespan",
        raw: "`this is an example of a really really really really long codespan`",
        text: "this is an example of a really really really really long codespan",
      }}
    >
      <File slot="icon" />
      this is an example of a really really really really long codespan
    </CodespanPresentation>
  </div>
</ColumnLayout>

<style>
  .limit-width {
    max-width: 200px;
  }
</style>
