<script lang="ts">
  import ColumnLayout from "$common-webviews/mocks/storybook/ColumnLayout.svelte";
  import SyncProgressBar from "$common-webviews/src/apps/chat/components/status-bar/SyncProgressBar.svelte";
  import type { WorkspaceSyncingOverview } from "$common-webviews/src/apps/chat/models/types";
  import { SyncingStatus } from "$vscode/src/workspace/types";

  let progress: WorkspaceSyncingOverview | undefined;

  function startSync() {
    progress = {
      status: SyncingStatus.longRunning,
      totalFiles: 100,
      syncedCount: 0,
      backlogSize: 100,
      newlyTrackedFolders: [],
    };
    let intervalID = setInterval(() => {
      if (progress) {
        const amount = Math.ceil(Math.random() * 10);
        progress.syncedCount += amount;
        progress.backlogSize -= amount;

        if (progress.backlogSize <= 0) {
          progress.status = SyncingStatus.done;
          clearInterval(intervalID);
        }
      }
    }, 500);
  }
</script>

<ColumnLayout>
  <p><button on:click={startSync}>Trigger Sync</button></p>

  <SyncProgressBar {progress} />
</ColumnLayout>
