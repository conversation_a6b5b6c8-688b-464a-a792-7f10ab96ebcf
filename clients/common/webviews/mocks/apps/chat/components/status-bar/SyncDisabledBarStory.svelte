<script lang="ts">
  import ColumnLayout from "$common-webviews/mocks/storybook/ColumnLayout.svelte";
  import SyncDisabledBar from "$common-webviews/src/apps/chat/components/status-bar/SyncDisabledBar.svelte";
  import { SpecialContextInputModel } from "$common-webviews/src/apps/chat/models/context-model";
  import { SyncingEnabledState } from "$vscode/src/workspace/types";

  const contextModel = new SpecialContextInputModel();

  function toggleDisabled() {
    let newState =
      contextModel.syncEnabledState === SyncingEnabledState.disabled
        ? SyncingEnabledState.enabled
        : SyncingEnabledState.disabled;
    contextModel.onSyncEnabledStateUpdate(newState);
  }
</script>

<ColumnLayout>
  <p><button on:click={toggleDisabled}>Toggle Disabled</button></p>

  <SyncDisabledBar {contextModel} />
</ColumnLayout>
