<script lang="ts">
  import ColumnLayout from "$common-webviews/mocks/storybook/ColumnLayout.svelte";
  import Fieldset from "$common-webviews/mocks/storybook/Fieldset.svelte";
  import CollapsibleIcon from "$common-webviews/src/apps/chat/components/CollapsibleIcon.svelte";

  // Icons for demonstration
  import FileIcon from "$common-webviews/src/design-system/icons/vscode/file.svelte";
  import FolderIcon from "$common-webviews/src/design-system/icons/vscode/folder.svelte";
  import GearIcon from "$common-webviews/src/design-system/icons/gear.svelte";
  import CodeIcon from "$common-webviews/src/design-system/icons/code.svelte";
  import TerminalIcon from "$common-webviews/src/design-system/icons/augment/terminal.svelte";

  // Props from Storybook controls
  export let expandable: boolean = true;
  export let collapsed: boolean = false;
  export let iconType: string = "file";
  export let showAllStates: boolean = false;

  // Icon mapping
  const iconComponents = {
    file: FileIcon,
    folder: FolderIcon,
    gear: GearIcon,
    code: CodeIcon,
    terminal: TerminalIcon,
  };

  $: IconComponent = iconComponents[iconType as keyof typeof iconComponents] || FileIcon;
</script>

{#if showAllStates}
  <ColumnLayout>
    <Fieldset title="Expandable States">
      <div class="icon-row">
        <div class="icon-demo hoverable">
          <CollapsibleIcon expandable={true} collapsed={false}>
            <FileIcon slot="icon" />
          </CollapsibleIcon>
          <span>Expandable, Not Collapsed</span>
        </div>

        <div class="icon-demo hoverable">
          <CollapsibleIcon expandable={true} collapsed={true}>
            <FileIcon slot="icon" />
          </CollapsibleIcon>
          <span>Expandable, Collapsed</span>
        </div>

        <div class="icon-demo hoverable">
          <CollapsibleIcon expandable={false} collapsed={false}>
            <GearIcon slot="icon" />
          </CollapsibleIcon>
          <span>Not Expandable</span>
        </div>
      </div>
    </Fieldset>

    <Fieldset title="Different Icon Types">
      <div class="icon-row">
        <div class="icon-demo">
          <CollapsibleIcon expandable={true} collapsed={false}>
            <FileIcon slot="icon" />
          </CollapsibleIcon>
          <span>File Icon</span>
        </div>

        <div class="icon-demo">
          <CollapsibleIcon expandable={true} collapsed={false}>
            <FolderIcon slot="icon" />
          </CollapsibleIcon>
          <span>Folder Icon</span>
        </div>

        <div class="icon-demo">
          <CollapsibleIcon expandable={true} collapsed={false}>
            <CodeIcon slot="icon" />
          </CollapsibleIcon>
          <span>Code Icon</span>
        </div>

        <div class="icon-demo">
          <CollapsibleIcon expandable={false} collapsed={false}>
            <TerminalIcon slot="icon" />
          </CollapsibleIcon>
          <span>Terminal Icon</span>
        </div>
      </div>
    </Fieldset>

    <Fieldset title="Interactive Behavior">
      <div class="icon-demo">
        <CollapsibleIcon expandable={true} collapsed={false}>
          <FileIcon slot="icon" />
        </CollapsibleIcon>
        <span>Hover to see expand icon overlay</span>
      </div>
    </Fieldset>
  </ColumnLayout>
{:else}
  <div class="single-icon-demo">
    <CollapsibleIcon {expandable} {collapsed}>
      <svelte:component this={IconComponent} slot="icon" />
    </CollapsibleIcon>
    <div class="icon-info">
      <p><strong>Expandable:</strong> {expandable}</p>
      <p><strong>Collapsed:</strong> {collapsed}</p>
      <p><strong>Icon Type:</strong> {iconType}</p>
      {#if expandable}
        <p class="tooltip-info">
          <em>Tooltip: {collapsed ? "Expand" : "Collapse"}</em>
        </p>
      {:else}
        <p class="tooltip-info">
          <em>No tooltip (not expandable)</em>
        </p>
      {/if}
    </div>
  </div>
{/if}

<style>
  .icon-row {
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
    align-items: center;
  }

  .icon-demo {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem;
    border: 1px solid var(--ds-color-neutral-a6);
    border-radius: 4px;
    min-width: 120px;
  }

  .icon-demo span {
    font-size: 0.875rem;
    color: var(--ds-color-neutral-a11);
    text-align: center;
  }

  .single-icon-demo {
    display: flex;
    align-items: center;
    gap: 2rem;
    padding: 2rem;
  }

  .icon-info {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .icon-info p {
    margin: 0;
    font-size: 0.875rem;
  }

  .tooltip-info {
    color: var(--ds-color-neutral-a10);
    font-style: italic;
  }
  .hoverable {
    cursor: pointer;
    &:hover {
      --collapsible-icon-visibility: hidden;
      --collapsible-icon-expand-visibility: visible;
    }
  }
</style>
