/* eslint-disable @typescript-eslint/naming-convention */
import type { <PERSON>a, StoryObj } from "@storybook/svelte-vite";
import "./chat-mock";
import component from "$common-webviews/src/apps/chat/ChatLoader.svelte";
import { ExchangeStatus, SeenState } from "$common-webviews/src/apps/chat/types/chat-message";
import {
  ChatRequestNodeType,
  ChatResultNodeType,
} from "@augment-internal/sidecar-libs/src/chat/chat-types";
import { withMonaco } from "$common-webviews/mocks/design-system/monaco-provider/monaco-decorator";
import { SubscriptionType } from "$common-webviews/src/apps/chat/models/subscription-model";

const meta = {
  title: "app/Chat",
  component,
} satisfies Meta<component>;

export default meta;

type Story = StoryObj<typeof meta>;
const chatHistory = [
  {
    request_message: "How do I create a React component with TypeScript?",
    response_text:
      "Creating a React component with TypeScript is straightforward. Here's a basic example of a functional component with props typing:\n\n```tsx\nimport React from 'react';\n\n// Define the props interface\ninterface GreetingProps {\n  name: string;\n  age?: number; // Optional prop\n}\n\n// Functional component with typed props\nconst Greeting: React.FC<GreetingProps> = ({ name, age }) => {\n  return (\n    <div>\n      <h1>Hello, {name}!</h1>\n      {age !== undefined && <p>You are {age} years old.</p>}\n    </div>\n  );\n};\n\nexport default Greeting;\n```\n\nTo use this component:\n\n```tsx\nimport Greeting from './Greeting';\n\nfunction App() {\n  return (\n    <div>\n      <Greeting name=\"John\" age={30} />\n      <Greeting name=\"Jane\" />\n    </div>\n  );\n}\n```\n\nHere are some key points about React with TypeScript:\n\n1. Use interfaces or types to define the shape of your props\n2. React.FC is a type that represents a functional component\n3. You can specify optional props using the ? operator\n4. TypeScript will provide type checking for your props\n\nWould you like me to show you how to create a class component or how to use hooks with TypeScript?",
    status: ExchangeStatus.success,
    request_id: "realistic-request-id-1",
    seen_state: SeenState.seen,
    structured_output_nodes: [
      {
        id: 0,
        type: ChatResultNodeType.RAW_RESPONSE,
        content:
          "Creating a React component with TypeScript is straightforward. Here's a basic example of a functional component with props typing:\n\n```tsx\nimport React from 'react';\n\n// Define the props interface\ninterface GreetingProps {\n  name: string;\n  age?: number; // Optional prop\n}\n\n// Functional component with typed props\nconst Greeting: React.FC<GreetingProps> = ({ name, age }) => {\n  return (\n    <div>\n      <h1>Hello, {name}!</h1>\n      {age !== undefined && <p>You are {age} years old.</p>}\n    </div>\n  );\n};\n\nexport default Greeting;\n```\n\nTo use this component:\n\n```tsx\nimport Greeting from './Greeting';\n\nfunction App() {\n  return (\n    <div>\n      <Greeting name=\"John\" age={30} />\n      <Greeting name=\"Jane\" />\n    </div>\n  );\n}\n```\n\nHere are some key points about React with TypeScript:\n\n1. Use interfaces or types to define the shape of your props\n2. React.FC is a type that represents a functional component\n3. You can specify optional props using the ? operator\n4. TypeScript will provide type checking for your props\n\nWould you like me to show you how to create a class component or how to use hooks with TypeScript?",
      },
    ],
    structured_request_nodes: [
      {
        id: 0,
        type: ChatRequestNodeType.TEXT,
        text_node: {
          content: "How do I create a React component with TypeScript?",
        },
      },
    ],
  },
  {
    request_message: "Show me how to use useState and useEffect with TypeScript",
    response_text:
      "Here's how to use the `useState` and `useEffect` hooks with TypeScript in React:\n\n## useState with TypeScript\n\nThe `useState` hook can infer types automatically, but you can also explicitly type it:\n\n```tsx\nimport React, { useState } from 'react';\n\nconst Counter: React.FC = () => {\n  // Type inference (number)\n  const [count, setCount] = useState(0);\n  \n  // Explicit typing\n  const [text, setText] = useState<string>('');\n  \n  // With union types\n  const [status, setStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle');\n  \n  // With complex types\n  type User = { id: number; name: string; email: string };\n  const [user, setUser] = useState<User | null>(null);\n  \n  return (\n    <div>\n      <p>Count: {count}</p>\n      <button onClick={() => setCount(count + 1)}>Increment</button>\n    </div>\n  );\n};\n```\n\n## useEffect with TypeScript\n\nThe `useEffect` hook doesn't typically need explicit typing:\n\n```tsx\nimport React, { useState, useEffect } from 'react';\n\nconst UserProfile: React.FC<{ userId: number }> = ({ userId }) => {\n  interface User {\n    id: number;\n    name: string;\n    email: string;\n  }\n  \n  const [user, setUser] = useState<User | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<Error | null>(null);\n  \n  useEffect(() => {\n    const fetchUser = async () => {\n      try {\n        setLoading(true);\n        // Simulating API call\n        const response = await fetch(`https://api.example.com/users/${userId}`);\n        if (!response.ok) throw new Error('Failed to fetch user');\n        \n        const userData: User = await response.json();\n        setUser(userData);\n        setError(null);\n      } catch (err) {\n        setError(err instanceof Error ? err : new Error('Unknown error'));\n        setUser(null);\n      } finally {\n        setLoading(false);\n      }\n    };\n    \n    fetchUser();\n    \n    // Cleanup function\n    return () => {\n      // Cancel any pending requests or subscriptions\n    };\n  }, [userId]); // Dependency array\n  \n  if (loading) return <div>Loading...</div>;\n  if (error) return <div>Error: {error.message}</div>;\n  if (!user) return <div>No user found</div>;\n  \n  return (\n    <div>\n      <h1>{user.name}</h1>\n      <p>Email: {user.email}</p>\n    </div>\n  );\n};\n```\n\n## Custom Hook with TypeScript\n\nYou can also create custom hooks with TypeScript:\n\n```tsx\nimport { useState, useEffect } from 'react';\n\n// Custom hook for fetching data\nfunction useData<T>(url: string) {\n  const [data, setData] = useState<T | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<Error | null>(null);\n  \n  useEffect(() => {\n    let isMounted = true;\n    \n    const fetchData = async () => {\n      try {\n        setLoading(true);\n        const response = await fetch(url);\n        if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);\n        \n        const result = await response.json();\n        if (isMounted) {\n          setData(result as T);\n          setError(null);\n        }\n      } catch (err) {\n        if (isMounted) {\n          setError(err instanceof Error ? err : new Error('Unknown error'));\n          setData(null);\n        }\n      } finally {\n        if (isMounted) setLoading(false);\n      }\n    };\n    \n    fetchData();\n    \n    return () => {\n      isMounted = false;\n    };\n  }, [url]);\n  \n  return { data, loading, error };\n}\n\n// Usage\ninterface User {\n  id: number;\n  name: string;\n  email: string;\n}\n\nconst UserComponent: React.FC<{ userId: number }> = ({ userId }) => {\n  const { data: user, loading, error } = useData<User>(`https://api.example.com/users/${userId}`);\n  \n  // Rest of the component\n};\n```\n\nThese examples demonstrate how to use React hooks with TypeScript, providing type safety for your state and effects. The key benefits include better autocompletion, catching type errors during development, and making your code more self-documenting.",
    status: ExchangeStatus.success,
    request_id: "realistic-request-id-2",
    seen_state: SeenState.seen,
    structured_output_nodes: [
      {
        id: 0,
        type: ChatResultNodeType.RAW_RESPONSE,
        content:
          "Here's how to use the `useState` and `useEffect` hooks with TypeScript in React:\n\n## useState with TypeScript\n\nThe `useState` hook can infer types automatically, but you can also explicitly type it:\n\n```tsx\nimport React, { useState } from 'react';\n\nconst Counter: React.FC = () => {\n  // Type inference (number)\n  const [count, setCount] = useState(0);\n  \n  // Explicit typing\n  const [text, setText] = useState<string>('');\n  \n  // With union types\n  const [status, setStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle');\n  \n  // With complex types\n  type User = { id: number; name: string; email: string };\n  const [user, setUser] = useState<User | null>(null);\n  \n  return (\n    <div>\n      <p>Count: {count}</p>\n      <button onClick={() => setCount(count + 1)}>Increment</button>\n    </div>\n  );\n};\n```\n\n## useEffect with TypeScript\n\nThe `useEffect` hook doesn't typically need explicit typing:\n\n```tsx\nimport React, { useState, useEffect } from 'react';\n\nconst UserProfile: React.FC<{ userId: number }> = ({ userId }) => {\n  interface User {\n    id: number;\n    name: string;\n    email: string;\n  }\n  \n  const [user, setUser] = useState<User | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<Error | null>(null);\n  \n  useEffect(() => {\n    const fetchUser = async () => {\n      try {\n        setLoading(true);\n        // Simulating API call\n        const response = await fetch(`https://api.example.com/users/${userId}`);\n        if (!response.ok) throw new Error('Failed to fetch user');\n        \n        const userData: User = await response.json();\n        setUser(userData);\n        setError(null);\n      } catch (err) {\n        setError(err instanceof Error ? err : new Error('Unknown error'));\n        setUser(null);\n      } finally {\n        setLoading(false);\n      }\n    };\n    \n    fetchUser();\n    \n    // Cleanup function\n    return () => {\n      // Cancel any pending requests or subscriptions\n    };\n  }, [userId]); // Dependency array\n  \n  if (loading) return <div>Loading...</div>;\n  if (error) return <div>Error: {error.message}</div>;\n  if (!user) return <div>No user found</div>;\n  \n  return (\n    <div>\n      <h1>{user.name}</h1>\n      <p>Email: {user.email}</p>\n    </div>\n  );\n};\n```\n\n## Custom Hook with TypeScript\n\nYou can also create custom hooks with TypeScript:\n\n```tsx\nimport { useState, useEffect } from 'react';\n\n// Custom hook for fetching data\nfunction useData<T>(url: string) {\n  const [data, setData] = useState<T | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<Error | null>(null);\n  \n  useEffect(() => {\n    let isMounted = true;\n    \n    const fetchData = async () => {\n      try {\n        setLoading(true);\n        const response = await fetch(url);\n        if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);\n        \n        const result = await response.json();\n        if (isMounted) {\n          setData(result as T);\n          setError(null);\n        }\n      } catch (err) {\n        if (isMounted) {\n          setError(err instanceof Error ? err : new Error('Unknown error'));\n          setData(null);\n        }\n      } finally {\n        if (isMounted) setLoading(false);\n      }\n    };\n    \n    fetchData();\n    \n    return () => {\n      isMounted = false;\n    };\n  }, [url]);\n  \n  return { data, loading, error };\n}\n\n// Usage\ninterface User {\n  id: number;\n  name: string;\n  email: string;\n}\n\nconst UserComponent: React.FC<{ userId: number }> = ({ userId }) => {\n  const { data: user, loading, error } = useData<User>(`https://api.example.com/users/${userId}`);\n  \n  // Rest of the component\n};\n```\n\nThese examples demonstrate how to use React hooks with TypeScript, providing type safety for your state and effects. The key benefits include better autocompletion, catching type errors during development, and making your code more self-documenting.",
      },
    ],
    structured_request_nodes: [
      {
        id: 0,
        type: ChatRequestNodeType.TEXT,
        text_node: {
          content: "Show me how to use useState and useEffect with TypeScript",
        },
      },
    ],
  },
];

const FIXED_CREATED = "2024-01-02T12:00:00.000Z";
const FIXED_LAST = "2024-01-02T13:00:00.000Z";

export const ChatHistoryRealistic: Story = {
  name: "Chat History - Realistic",
  parameters: {
    layout: "fullscreen",
  },
  args: {
    minDisplayTime: 0,
    randomize: false,
    retryCount: 0,
    initialConversation: {
      id: "realistic-conversation-id",
      createdAtIso: FIXED_CREATED,
      lastInteractedAtIso: FIXED_LAST,
      chatHistory,
      feedbackStates: {},
      requestIds: ["realistic-request-id-1", "realistic-request-id-2"],
      isPinned: false,
      isShareable: true,
    },
  },
  decorators: [withMonaco],
};

export const ChatHistoryTest: Story = {
  name: "Chat History - Test Realistic",
  parameters: {
    layout: "fullscreen",
  },
  args: {
    minDisplayTime: 0,
    randomize: false,
    retryCount: 0,
    initialConversation: {
      id: "realistic-conversation-id",
      createdAtIso: "2025-07-22T17:00:52.443Z",
      lastInteractedAtIso: "2025-07-22T18:00:52.443Z",

      chatHistory: [
        {
          request_message: "How do I create a React component with TypeScript?",
          response_text:
            'Check! Perfect! I\'ve successfully added a comment to the Chat.svelte file. The comment I added is:\\n\\n````typescript path=clients/common/webviews/src/apps/chat/Chat.svelte mode=EXCERPT\\n<script lang="ts">\\n  // Main Chat component - handles the entire chat interface including message display,\\n  // input area, remote agent management, and various UI states\\n  import "./chat.css";\\n````\\n\\nThe comment provides a clear, concise description of what the Chat.svelte component does - it\'s the main chat component that manages the entire chat interface including message display, input handling, remote agent functionality, and various UI states. This helps other developers quickly understand the purpose and scope of this important component.\\n',
          status: ExchangeStatus.success,
          request_id: "realistic-request-id-1",
          seen_state: SeenState.seen,

          structured_output_nodes: [
            {
              id: 0,
              type: 0,
              content:
                'Perfect! I\'ve successfully added a comment to the Chat.svelte file. The comment I added is:\\n\\n````typescript path=clients/common/webviews/src/apps/chat/Chat.svelte mode=EXCERPT\\n<script lang="ts">\\n  // Main Chat component - handles the entire chat interface including message display,\\n  // input area, remote agent management, and various UI states\\n  import "./chat.css";\\n````\\n\\nThe comment provides a clear, concise description of what the Chat.svelte component does - it\'s the main chat component that manages the entire chat interface including message display, input handling, remote agent functionality, and various UI states. This helps other developers quickly understand the purpose and scope of this important component.\\n',
            },
          ],

          structured_request_nodes: [
            {
              id: 0,
              type: 0,

              text_node: {
                content: "How do I create a React component with TypeScript?",
              },
            },
          ],
        },
      ],

      feedbackStates: {},
      requestIds: ["realistic-request-id-1"],
      isPinned: false,
      isShareable: true,
    },
  },
  decorators: [withMonaco],
};

// Create a long chat history by duplicating the original
const longChatHistory = Array(100)
  .fill(0)
  .flatMap((_, requestSeq) => {
    // Create a deep copy of the chat history with unique request IDs for each copy
    return chatHistory.map((item, itemIdx) => {
      const randomId = `seq-${requestSeq}-${itemIdx}`;
      return {
        ...item,
        request_id: `${item.request_id}-${randomId}`,
      };
    });
  });

export const ChatHistoryLongEnableDebug: Story = {
  name: "Chat History Long - Realistic / Enable Debug Features",
  args: {
    minDisplayTime: 0,
    randomize: false,
    retryCount: 0,
    initialConversation: {
      id: "long-conversation-id",
      createdAtIso: FIXED_CREATED,
      lastInteractedAtIso: FIXED_LAST,
      chatHistory: longChatHistory,
      feedbackStates: {},
      requestIds: longChatHistory.map((item) => item.request_id),
      isPinned: false,
      isShareable: true,
    },
    initialFlags: {
      enableDebugFeatures: true,
    },
  },
};

// Subscription Warning Stories

export const ChatWithExpiringSubscription: Story = {
  name: "Chat with Expiring Subscription Warning",
  args: {
    minDisplayTime: 0,
    randomize: false,
    retryCount: 0,
    initialConversation: {
      id: "expiring-subscription-conversation-id",
      createdAtIso: FIXED_CREATED,
      lastInteractedAtIso: FIXED_LAST,
      chatHistory,
      feedbackStates: {},
      requestIds: ["realistic-request-id-1", "realistic-request-id-2"],
      isPinned: false,
      isShareable: true,
    },
    initialSubscriptionInfo: {
      type: SubscriptionType.active,
      daysRemaining: 7, // 7 days remaining
      usageBalanceDepleted: false,
    },
    initialSubscriptionDismissed: false,
  },
  decorators: [withMonaco],
};

export const ChatWithInactiveSubscription: Story = {
  name: "Chat with Inactive Subscription Warning",
  args: {
    minDisplayTime: 0,
    randomize: false,
    retryCount: 0,
    initialConversation: {
      id: "inactive-subscription-conversation-id",
      createdAtIso: FIXED_CREATED,
      lastInteractedAtIso: FIXED_LAST,
      chatHistory,
      feedbackStates: {},
      requestIds: ["realistic-request-id-1", "realistic-request-id-2"],
      isPinned: false,
      isShareable: true,
    },
    initialSubscriptionInfo: {
      type: SubscriptionType.inactive,
    },
    initialSubscriptionDismissed: false,
  },
  decorators: [withMonaco],
};

export const ChatWithDepletedUsageBalance: Story = {
  name: "Chat with Depleted Usage Balance Warning",
  args: {
    minDisplayTime: 0,
    randomize: false,
    retryCount: 0,
    initialConversation: {
      id: "depleted-usage-conversation-id",
      createdAtIso: FIXED_CREATED,
      lastInteractedAtIso: FIXED_LAST,
      chatHistory,
      feedbackStates: {},
      requestIds: ["realistic-request-id-1", "realistic-request-id-2"],
      isPinned: false,
      isShareable: true,
    },
    initialSubscriptionInfo: {
      type: SubscriptionType.active,
      daysRemaining: 30, // 30 days remaining
      usageBalanceDepleted: true, // Usage balance depleted
    },
    initialSubscriptionDismissed: false,
  },
  decorators: [withMonaco],
};
