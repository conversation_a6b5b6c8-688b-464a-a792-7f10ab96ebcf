/* eslint-disable @typescript-eslint/naming-convention */
import type { Meta, StoryObj } from "@storybook/svelte-vite";
import "./chat-mock";
import component from "$common-webviews/src/apps/chat/ChatLoader.svelte";
import { ExchangeStatus, SeenState } from "$common-webviews/src/apps/chat/types/chat-message";
import {
  ChatRequestNodeType,
  ChatResultNodeType,
} from "@augment-internal/sidecar-libs/src/chat/chat-types";
import { withMonaco } from "$common-webviews/mocks/design-system/monaco-provider/monaco-decorator";

const meta = {
  title: "app/Chat",
  component,
} satisfies Meta<component>;

export default meta;

type Story = StoryObj<typeof meta>;

const FIXED_CREATED = "2024-01-02T12:00:00.000Z";
const FIXED_LAST = "2024-01-02T12:01:00.000Z";

export const MemoryNodeDemo: Story = {
  name: "Chat with Memory Nodes",
  args: {
    initialConversation: {
      id: "memory-demo-conversation-id",
      createdAtIso: FIXED_CREATED,
      lastInteractedAtIso: FIXED_LAST,
      chatHistory: [
        {
          request_message:
            "Can you help me understand how to implement user authentication in my React app?",
          status: ExchangeStatus.success,
          request_id: "memory-demo-request-id-1",
          seen_state: SeenState.seen,
          structured_output_nodes: [
            {
              id: 1,
              type: ChatResultNodeType.AGENT_MEMORY,
              content: JSON.stringify({
                memoriesRequestId: "mem_auth_react_basics_2024_01_15_14_30_45_abc123",
                memory:
                  "User is implementing React authentication using JWT tokens. Provided AuthContext pattern with login/logout functionality and protected routes. Key concepts covered: JWT vs session-based auth, localStorage token storage, context API for state management, and route protection patterns.",
              }),
              agent_memory: {
                content:
                  "User is implementing React authentication using JWT tokens. Provided AuthContext pattern with login/logout functionality and protected routes. Key concepts covered: JWT vs session-based auth, localStorage token storage, context API for state management, and route protection patterns.",
                isFlushed: true,
              },
            },
          ],
          structured_request_nodes: [
            {
              id: 0,
              type: ChatRequestNodeType.TEXT,
              text_node: {
                content:
                  "Can you help me understand how to implement user authentication in my React app?",
              },
            },
          ],
        },
        {
          request_message:
            "Remember that I'm working on a React e-commerce project with Stripe integration",
          response_text:
            "I'll remember that you're working on a React e-commerce project with Stripe integration. This context will help me provide more relevant assistance in future conversations.",
          status: ExchangeStatus.success,
          request_id: "memory-demo-request-id-3",
          seen_state: SeenState.seen,
          structured_output_nodes: [
            {
              id: 0,
              type: ChatResultNodeType.RAW_RESPONSE,
              content:
                "I'll remember that you're working on a React e-commerce project with Stripe integration.",
            },
            {
              id: 1,
              type: ChatResultNodeType.TOOL_USE,
              content: "",
              tool_use: {
                tool_use_id: "remember-tool-use-id-1",
                tool_name: "remember",
                input_json: JSON.stringify({
                  memory:
                    "User is working on a React e-commerce project with Stripe payment integration",
                }),
              },
            },
            {
              id: 2,
              type: ChatResultNodeType.RAW_RESPONSE,
              content:
                " This context will help me provide more relevant assistance in future conversations.",
            },
          ],
          structured_request_nodes: [
            {
              id: 0,
              type: ChatRequestNodeType.TEXT,
              text_node: {
                content:
                  "Remember that I'm working on a React e-commerce project with Stripe integration",
              },
            },
          ],
        },
        {
          request_message: "What did I tell you about my current project earlier?",
          response_text: "Let me check what you've told me about your current project.",
          status: ExchangeStatus.success,
          request_id: "memory-demo-request-id-4",
          seen_state: SeenState.seen,
          structured_output_nodes: [
            {
              id: 0,
              type: ChatResultNodeType.RAW_RESPONSE,
              content: "Let me check what you've told me about your current project.",
            },
            {
              id: 1,
              type: ChatResultNodeType.TOOL_USE,
              content: "",
              tool_use: {
                tool_use_id: "memory-retrieval-tool-use-id-1",
                tool_name: "memory-retrieval",
                input_json: JSON.stringify({
                  query: "Get memories about current project React e-commerce Stripe",
                  max_results: 5,
                }),
              },
            },
            {
              id: 2,
              type: ChatResultNodeType.RAW_RESPONSE,
              content:
                "\n\nBased on our previous conversations, I can see that:\n\n1. **Current Project**: You're working on a React e-commerce project with Stripe payment integration\n2. **Authentication Focus**: We discussed implementing JWT-based authentication with token refresh patterns\n3. **Technical Stack**: React with Context API for state management, localStorage for token storage\n\nThis context helps me understand that your authentication implementation is likely for an e-commerce application where secure payment processing is crucial. Would you like me to provide any specific guidance on integrating authentication with Stripe payments or securing your e-commerce workflows?",
            },
            {
              id: 3,
              type: ChatResultNodeType.AGENT_MEMORY,
              content: JSON.stringify({
                memoriesRequestId: "mem_project_recall_2024_01_15_15_15_45_jkl012",
                memory:
                  "User asked about previous project context. Retrieved memories about React e-commerce project with Stripe integration and connected it with earlier authentication discussions. Demonstrated memory retrieval functionality.",
              }),
              agent_memory: {
                content:
                  "User asked about previous project context. Retrieved memories about React e-commerce project with Stripe integration and connected it with earlier authentication discussions. Demonstrated memory retrieval functionality.",
                isFlushed: true,
              },
            },
          ],
          structured_request_nodes: [
            {
              id: 0,
              type: ChatRequestNodeType.TEXT,
              text_node: {
                content: "What did I tell you about my current project earlier?",
              },
            },
          ],
        },
      ],
      feedbackStates: {},
      requestIds: [
        "memory-demo-request-id-1",
        "memory-demo-request-id-2",
        "memory-demo-request-id-3",
        "memory-demo-request-id-4",
      ],
      isPinned: false,
      isShareable: true,
    },
    initialFlags: {
      enableDebugFeatures: true,
      enableChatMultimodal: true,
      enableMemoryRetrieval: true,
    },
  },
  decorators: [withMonaco],
};
