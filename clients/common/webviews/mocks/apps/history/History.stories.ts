/* eslint-disable @typescript-eslint/naming-convention */
import type { Meta, StoryObj } from "@storybook/svelte-vite";
import "./history-mock";
import "../../hosts/monaco-init";
import component from "$common-webviews/src/apps/history/History.svelte";

const meta = {
  title: "app/History",
  component,
} satisfies Meta<component>;

export default meta;

type Story = StoryObj<typeof meta>;

// More on writing stories with args: https://storybook.js.org/docs/writing-stories/args
export const Example: Story = {
  args: {},
};
