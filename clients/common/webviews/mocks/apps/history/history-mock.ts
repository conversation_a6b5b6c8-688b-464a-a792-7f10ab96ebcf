import type { AugmentInstruction } from "$vscode/src/code-edit-types";
import {
  ChangeType,
  NextEditMode,
  NextEditScope,
  SuggestionState,
  type NextEditResultInfo,
} from "$vscode/src/next-edit/next-edit-types";
import {
  type HistoryCompletionRequest,
  WebViewMessageType,
} from "$vscode/src/webview-providers/webview-messages";
import { webviewmock } from "../webview-mock";

const CONFIG_AUTO_GROW_COMPLETIONS = false;
const CONFIG_MAX_GROW_COMPLETIONS = 24;

const FIXED_NOW = new Date("2024-01-02T12:00:00.000Z");
const FIXED_NOW_ISO = FIXED_NOW.toISOString();

const exampleInstructions: Array<AugmentInstruction> = [
  {
    occuredAt: FIXED_NOW,
    requestedAt: FIXED_NOW,
    sessionId: "5d8278db-dc68-4917-9098-3d271ccb47ca",
    requestId: "6d971902-5c70-438f-8aea-2782235437xz",
    suggestionId: "6d971902-5c70-438f-8aea-2782235437ac",
    repoRoot: "/Users/<USER>/workspace/LeetCode",
    pathName: "0002-add-two-numbers/0002-add-two-numbers.js",
    vscodeLanguageId: "javascript",
    modelName: "",
    prompt: "add comments",
    prefix:
      "/**\n * Definition for singly-linked list.\n * function ListNode(val, next) {\n *     this.val = (val===undefined ? 0 : val)\n *     this.next = (next===undefined ? null : next)\n * }\n */\n/**\n * @param {ListNode} l1\n * @param {ListNode} l2\n * @return {ListNode}\n */\n",
    selectedText:
      "var addTwoNumbers = function (l1, l2) {\n  const iterator = (n1, n2, rest = 0) => {\n    if (!n1 && !n2 && !rest) return null;\n    const newVal = (n1?.val || 0) + (n2?.val || 0) + rest;\n    const nextNode = iterator(n1?.next, n2?.next, Math.floor(newVal / 10));\n    return new ListNode(newVal % 10, nextNode);\n  };\n\n  return iterator(l1, l2);\n};\n",
    modifiedText:
      "// Define a function that takes in two linked lists\nvar addTwoNumbers = function (l1, l2) {\n  // Define a recursive function that takes in two nodes and a carry value\n  const iterator = (n1, n2, rest = 0) => {\n    // If both nodes are null and there's no carry, return null\n    if (!n1 && !n2 && !rest) return null;\n    // Calculate the new value by adding the values of the nodes and the carry\n    const newVal = (n1?.val || 0) + (n2?.val || 0) + rest;\n    // Recursively call the function with the next nodes and the new carry\n    const nextNode = iterator(n1?.next, n2?.next, Math.floor(newVal / 10));\n    // Return a new node with the remainder of the new value and the next node\n    return new ListNode(newVal % 10, nextNode);\n  };\n\n  // Call the recursive function with the two input lists\n  return iterator(l1, l2);\n};\n",
    suffix: "",
    selectionStartLine: 0,
    selectionStartColumn: 0,
    selectionEndLine: 0,
    selectionEndColumn: 0,
    userRequested: true,
  },
];

const exampleNextEdits: Array<NextEditResultInfo> = [
  {
    requestId: "6d971902-5c70-438f-8aea-2782235437ac",
    mode: NextEditMode.Foreground,
    scope: NextEditScope.File,
    qualifiedPathName: {
      rootPath: "/home/<USER>/projects",
      relPath: "/home/<USER>/projects/example.js",
    },
    apiResult: 0,
    suggestions: [
      {
        requestId: "6d971902-5c70-438f-8aea-2782235437ac",
        mode: NextEditMode.Foreground,
        scope: NextEditScope.File,
        result: {
          suggestionId: "6d971902-5c70-438f-8aea-2782235437ac",
          path: "/home/<USER>/projects/example.js",
          blobName: "42",
          charStart: 42,
          charEnd: 42,
          existingCode: "A",
          suggestedCode: "B",
          changeDescription: "A change",
          diffSpans: [],
          editingScore: 0,
          localizationScore: 0,
          editingScoreThreshold: 1.0,
        },
        qualifiedPathName: {
          rootPath: "/home/<USER>/projects",
          relPath: "/home/<USER>/projects/example.js",
        },
        lineRange: {
          start: 42,
          stop: 42,
        },
        uriScheme: "file",
        occurredAt: FIXED_NOW,
        state: SuggestionState.fresh,
        changeType: ChangeType.modification,
      },
    ],
    occurredAt: FIXED_NOW,
    requestTime: FIXED_NOW,
  },
];

const exampleCompletions: Array<HistoryCompletionRequest> = [
  {
    occuredAt: FIXED_NOW_ISO,
    requestId: "6d971902-5c70-438f-8aea-2782235437ab",
    repoRoot: "/Users/<USER>/Downloads/augment-icon-font",
    pathName: "example.js",
    prefix:
      "console.log(\"Hi Matt.\");\n\nfunction icomoonLiga(el) {\n  var ligas = el.querySelectorAll('.liga');\n  for (var i = 0; i < ligas.length; i++) {\n    ligas[i].value = ligas[i].value || ligas[i].getAttribute('data-liga');\n  }\n}\n\n",
    completions: [
      {
        text: "icomoonLiga(document.body);",
        skippedSuffix: "",
        suffixReplacementText: "",
      },
    ],
    suffix: "\n\n",
  },
  {
    occuredAt: FIXED_NOW_ISO,
    requestId: "3fc508d1-878a-4943-9d64-69ce537810b7",
    repoRoot: "/Users/<USER>/Projects/Code/augment-real",
    pathName: "this/is/a/really/long/file/path/clients/vscode/src/blob-manager.ts",
    prefix:
      'import { APIServer, CompletionLocation } from "./augment-api";\nimport { BlobNameCalculator } from "./blob-name-calculator";\nimport { BlobVersionObserver } from "./blobs-checkpoint-manager";\nimport { DisposableService } from "./disposable-service";\nimport { BlobWatcherTarget } from "./blob-watcher";\nimport { ChangeSummary, ChangeTrackerImpl } from "./change-tracker";\nimport { APIError, getErrmsg } from "./exceptions";\nimport { newFileReader } from "./file-reader";\nimport { getLogger } from "./logging";\nimport { MemoryManager, MemoryManagerStats, PathNameQualifier } from "./memory-manager";\nimport { MtimeCache } from "./mtime-cache";\nimport { PathMap } from "./path-map";\nimport { StatusReporter } from "./reporting";\nimport { ErrorHandler, WorkQueue } from "./work-queue";\n\nimport { assert } from "console";\nimport * as vscode from "vscode";\nimport { Logger } from "winston";\n\n',
    completions: [
      {
        text: "export interface BlobManagerStats {\n    memoryManager: MemoryManagerStats;\n    toUpload: number;\n}",
        skippedSuffix: "",
        suffixReplacementText: "",
      },
    ],
    suffix:
      '\n\n/**\n * BlobManager manages the state of a set of blobs. Managed blobs fall into two\n * categories:\n *  - "Tracked" blobs: blobs that are backed by the contents of a vscode.TextDocument.\n *    These blobs correspond to source files that are currently open in vscode and\n *    have been modified at some point in this editor session.\n *  - "Untracked" blobs: blobs that are backed by files in the filesystem. These blobs\n *    are source files that are either not open or are open but have never been\n *    modified in this session.\n *\n * Information about tracked blobs is maintained directly by BlobManager in its\n * _trackedBlobs map. Information about untracked blobs is maintained by its\n * MemoryManager (_memoryManager).\n *\n * For tracked blobs, the goal is to upload them to the back end as infrequently\n * as possible. To that end the BlobManager keeps a ChangeTracker for each tracked\n * blob to keep track of the range of the blob that has been modified. The client can\n * use this information to decide whether the state of the blob has diverged too much\n * from the last uploaded state. ("Too much" is defined by the client).\n *\n * A client can ask the BlobManager to upload the exact current state of a blob (via\n * the flush() method), or it can ask it to upload whatever state the blob is in when\n * it gets around to starting the upload (via the clean() method). Neither method waits\n * for the requested upload to complete; they merely enqueue a request that will later\n * run in the background. A flush or clean request may therefore arrive while there is\n * already an upload request queued for the blob. When this happens, a flush request\n * supersedes any previous clean or flush request for the blob, in that it updates the\n * state of the blob to the newly requested state. A clean request leaves the previous\n * request unmodified.\n */\n\n/**\n * TrackedBlob is a class that keeps the state of a blob that is being tracked\n * by its in-memory state.\n */\nclass TrackedBlob {\n    private static maxEmbargoCount = 100;\n\n    private _changeTracker = new ChangeTrackerImpl();\n\n    // blob name and corresponding text that is waiting to start uploading (upload\n    // has not yet begun)\n    private _stagedBlobName: string | undefined;\n    private _stagedText: string | undefined;\n\n    // blob name and corresponding text that are currently being uploaded (upload\n    // request is in flight)\n    private _uploadingBlobName: string | undefined;\n    private _uploadingText: string | undefined;\n\n    // indicates whether this TrackedBlob is in the _toUpload queue.\n    public uploadRequested = false;\n\n    // This blob is too large to upload, or an upload attempt failed with a permanent\n    // error. We keep it in our map of tracked blobs until we are told to stop tracking\n    // it (just like any other blob), but we don\'t track changes to it or attempt to\n    // upload it again. Doing so prevents us from repeatedly attempting uploads that\n    // are doomed to fail. We do not attempt to rehabilitate embargoed blobs, even if\n    // they shrink to a supported size.\n    private _embargoed = false;\n\n    constructor(\n        public readonly document: vscode.TextDocument,\n        public uploadedBlobName: string | undefined\n    ) {}\n\n    public get uploadingBlobName(): string | undefined {\n        return this._uploadingBlobName;\n    }\n\n    public get uploadingText(): string | undefined {\n        return this._uploadingText;\n    }\n\n    public get changeTracker(): ChangeTrackerImpl {\n        return this._changeTracker;\n    }\n\n    public get embargoed(): boolean {\n        return this._embargoed;\n    }\n\n    private _refreshChangeTracker() {\n        this._changeTracker = new ChangeTrackerImpl();\n    }\n\n    // stage() requests that the next upload of this blob upload the given blob name\n    // and text, unless they are superseded by a later call to this method.\n    public stage(blobName: string, text: string) {\n        this._stagedBlobName = blobName;\n        this._stagedText = text;\n        this._refreshChangeTracker();\n    }\n\n    // isStaged() indicates whether this blob has state staged for its next upload.\n    public get isStaged(): boolean {\n        assert((this._stagedBlobName === undefined) === (this._stagedText === undefined));\n        return this._stagedBlobName !== undefined;\n    }\n\n    // prepareToEnqueue attempts to ready this TrackedBlob to be enqueued for upload.\n    // The return value indicates whether the blob should actually be enqueued (for\n    // example, it might already be in the queue or it might be embargoed).\n    public prepareToEnqueue(): boole',
  },
  {
    occuredAt: FIXED_NOW_ISO,
    requestId: "dfd425cc-3d8f-41a6-8109-fa15590b41b2",
    repoRoot: "/Users/<USER>/Projects/Code/augment-for-debugging",
    pathName: "test.js",
    prefix:
      'console.log(\'OMG its dart\');\n\nfunction helloWorld() {\n  return \'hi world\';\n}\n\nimport test from "ava";\nimport { trim, trimToLength } from "./index.js";\n\ntest("trimToLength with 6 characters", (t) => {\n  t.is(trimToLength("thisislongerthantencharacters", 6), "thisis...");\n});\n\ntest("foo", (t) => {\n  t.pass();\n});\n\ntest("bar", async (t) => {\n  const bar = Promise.resolve("bar");\n  t.is(await bar, "bar");\n});\n\ntest("baz", async (t) => {\n  const baz = Promise.resolve("baz");\n  t.is(await baz, "baz");\n',
    completions: [
      {
        text: "",
        skippedSuffix: "})",
        suffixReplacementText: "});",
      },
    ],
    suffix:
      '})\n\n// Write a test for the trim function\ntest("trim", (t) => {\n  t.is(trim("foo"), "foo");\n  t.is(trim("foobar"), "foobar");\n});\n\ntest("check that elipsis is added and the length is correct", async (t) => {\n  t.is(trim("thisislongerthantencharacters"), "thisisl...");\n});\n',
  },
];

// This function allows TypeScript to narrow the type of the event to CustomEvent
function isCustomEvent(event: Event): event is CustomEvent {
  return "detail" in event;
}

webviewmock.addEventListener("message-from-webview", (e: Event) => {
  if (!isCustomEvent(e)) {
    console.error("Unexpected event type: ", e);
    throw new Error("Unexpected mock message");
  }

  switch (e.detail.type) {
    case WebViewMessageType.historyLoaded: {
      if (CONFIG_AUTO_GROW_COMPLETIONS) {
        webviewmock.sendMessageToWebView({
          type: WebViewMessageType.completions,
          data: [],
        });
        break;
      }
      webviewmock.sendMessageToWebView({
        type: WebViewMessageType.historyInitialize,
        data: {
          config: {
            enableDebugFeatures: true,
            enableReviewerWorkflows: false,
          },
          completionRequests: exampleCompletions,
          instructions: exampleInstructions,
          nextEdits: exampleNextEdits,
        },
      });
      break;
    }
    case WebViewMessageType.completionRating: {
      const timeout: number = 200;
      setTimeout(() => {
        webviewmock.sendMessageToWebView({
          type: WebViewMessageType.completionRatingDone,
          data: {
            requestId: e.detail.data.requestId,
            success: true,
          },
        });
      }, timeout);
      break;
    }
    default: {
      console.error(`Unknown message type: ${e.detail.type}`);
      break;
    }
  }
});

if (CONFIG_AUTO_GROW_COMPLETIONS) {
  let idx = 0;
  let completions: Array<HistoryCompletionRequest> = [];
  const intervalID = setInterval(() => {
    completions.push(
      Object.assign({}, exampleCompletions[idx], {
        requestId: `auto-${completions.length}`,
      }),
    );
    if (completions.length > CONFIG_MAX_GROW_COMPLETIONS) {
      clearInterval(intervalID);
      return;
    }
    webviewmock.sendMessageToWebView({
      type: WebViewMessageType.completions,
      data: completions,
    });
    idx++;
    if (idx >= exampleCompletions.length) {
      idx = 0;
    }
  }, 3000);
}
