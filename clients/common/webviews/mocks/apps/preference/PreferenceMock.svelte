<script lang="ts">
  import type { ChatPreferenceInput } from "$vscode/src/webview-panels/preference-panel-types";
  import PreferenceChat from "$common-webviews/src/apps/preference/PreferenceChat.svelte";
  import { createEventDispatcher } from "svelte";

  export let input: ChatPreferenceInput;

  const dispatch = createEventDispatcher();

  function handleSubmit(event: CustomEvent) {
    dispatch("message", {
      type: "submit",
      data: event.detail,
    });
  }
</script>

<div class="mock-container">
  <PreferenceChat inputData={input} on:message={handleSubmit} />
</div>

<style>
  .mock-container {
    width: 100%;
    max-width: 800px;
    margin: 0 auto;
    padding: 1rem;
  }
</style>
