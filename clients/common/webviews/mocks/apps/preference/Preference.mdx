import { Meta } from '@storybook/addon-docs/blocks';

<Meta title="app/Preference/Documentation" />

# Preference Component

The Preference component is used to collect user feedback comparing two different responses. It allows users to:

- Rate the overall quality of responses
- Rate specific aspects like formatting, hallucination, and instruction following
- Provide text feedback
- Submit their preferences

## Usage

```svelte
<Preference
  input={{
    type: "Chat",
    data: {
      a: firstResponse,
      b: secondResponse
    },
    enableRetrievalDataCollection: true
  }}
  on:message={handleMessage}
/>
```

## Props

- `input`: PreferenceInput - Contains the data to be compared
  - `type`: Currently supports "Chat" type
  - `data`: Contains two responses to compare (a and b)
  - `enableRetrievalDataCollection`: Boolean flag for data collection

## Events

- `message`: Emitted when the user submits their preference
  - `type`: "submit"
  - `data`: Contains the user's ratings and feedback
