<script lang="ts">
  import RemoteAgentDiffView from "$common-webviews/src/apps/remote-agent-manager/components/RemoteAgentDiffView.svelte";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import type { ChangedFile } from "@augment-internal/sidecar-libs/src/remote-agent/remote-agent-types";
  import { onMount } from "svelte";

  export let changedFiles: ChangedFile[];

  let apikey = "";

  // Load API key from localStorage on mount
  onMount(() => {
    const savedApiKey = localStorage.getItem("anthropic_apikey");
    if (savedApiKey) {
      apikey = savedApiKey;
    }
  });

  // Save API key to localStorage when it changes
  function handleApikeyChange(event: Event) {
    const value = (event.target as HTMLInputElement).value;
    apikey = value;
    localStorage.setItem("anthropic_apikey", value);
  }
</script>

<div class="min-h-screen">
  <div class="api-key-container" class:api-key-container--empty={!apikey}>
    <label for="anthropic-apikey">Anthropic API Key:</label>
    <input
      id="anthropic-apikey"
      type="password"
      placeholder="Enter your Anthropic API key"
      value={apikey}
      on:input={handleApikeyChange}
    />
    <ButtonAugment variant="ghost" size={1} on:click={() => (apikey = "")}>Clear</ButtonAugment>
  </div>

  {#if apikey}
    {#key apikey}
      <div class="view">
        <RemoteAgentDiffView {changedFiles} />
      </div>
    {/key}
  {/if}
</div>

<style>
  .min-h-screen {
    min-height: 100vh;
  }

  .api-key-container {
    padding: var(--ds-spacing-3) var(--ds-spacing-6);
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-2);
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    border-top: 1px solid var(--ds-color-neutral-5);
    background: var(--ds-surface);
    transition: all 0.3s ease-out;
    height: 50px;
    z-index: 150;
  }
  .api-key-container--empty {
    height: calc(100% + 1px);
  }

  input {
    padding: var(--ds-spacing-1) var(--ds-spacing-2);
    border: 1px solid var(--ds-color-neutral-5);
    border-radius: var(--ds-radius-small);
    background: var(--ds-surface-sunken);
    color: var(--ds-color-neutral-12);
    font-size: 0.9rem;
    width: 300px;
  }

  label {
    font-size: 0.9rem;
    color: var(--ds-color-neutral-11);
  }

  .view {
    padding: var(--ds-spacing-2) var(--ds-spacing-6) 50px var(--ds-spacing-6);
  }
</style>
