/* eslint-disable @typescript-eslint/naming-convention */
import type { <PERSON><PERSON>, StoryObj } from "@storybook/svelte-vite";
import Markdown from "$common-webviews/src/common/components/markdown/Markdown.svelte";
import { withMonaco } from "$common-webviews/mocks/design-system/monaco-provider/monaco-decorator";

const meta = {
  title: "app/Markdown",
  component: Markdown,
  tags: ["autodocs"],
} satisfies Meta<Markdown>;

export default meta;

type Story = StoryObj<typeof meta>;

const richMarkdownExample = `# Augment Markdown Example

This is a demonstration of the markdown rendering capabilities in Augment.

## Text Formatting

You can use **bold text** or *italic text* or even ***bold and italic*** text.
You can also use ~~strikethrough~~ for text that's no longer relevant.

## Lists

### Unordered Lists
- Item 1
- Item 2
  - Nested item 2.1
  - Nested item 2.2
- Item 3

### Ordered Lists
1. First item
2. Second item
   1. Nested item 2.1
   2. Nested item 2.2
3. Third item

## Code Examples

Inline code: \`const example = "Hello World";\`

Code block:
\`\`\`typescript
// A simple TypeScript function
function greet(name: string): string {
  return \`Hello, \${name}!\`;
}

// Call the function
const message = greet("Augment");
console.log(message);
\`\`\`

## Links and References

[Augment](https://augment.dev) is a powerful AI coding assistant.

## Blockquotes

> This is a blockquote.
> It can span multiple lines.
>
> And even have multiple paragraphs.

## Tables

| Feature | Description | Support |
|---------|-------------|---------|
| Headings | H1 through H6 | ✅ |
| Lists | Ordered and unordered | ✅ |
| Code | Inline and blocks | ✅ |
| Formatting | Bold, italic, etc. | ✅ |

## **Performance Results:**

1. **File Processing Speed**: The parallel processing completed in approximately **2.4 seconds** (from 04:01: 56.076 to 04:01: 58.498)

2. **Throughput**: Processing ~21,000 files in 2.4 seconds = **~8,750 files per second**

3. **Overall Improvement**: The workspace initialization went from ~15 seconds to ~5.8 seconds, which is approximately a **~60% improvement**

---

That's it for this markdown example!`;

export const Example: Story = {
  args: {
    markdown: richMarkdownExample,
  },
  decorators: [withMonaco],
};

export const SimpleExample: Story = {
  args: {
    markdown: "Example markdown content",
  },
  decorators: [withMonaco],
};

const advancedMarkdownExample = `# Advanced Markdown Features

## Code Highlighting

\`\`\`python
def fibonacci(n):
    """Generate fibonacci sequence up to n"""
    a, b = 0, 1
    while a < n:
        yield a
        a, b = b, a + b

# Print first 10 fibonacci numbers
for num in fibonacci(100):
    print(num)
\`\`\`

\`\`\`svelte
<script lang="ts">
  import { onMount } from 'svelte';

  export let name: string;
  let count = 0;

  function increment() {
    count += 1;
  }

  onMount(() => {
    console.log('Component mounted!');
  });
</script>

<h1>Hello {name}!</h1>
<p>You clicked {count} times</p>
<button on:click={increment}>Click me</button>
\`\`\`

## Mermaid Diagram (if supported)

\`\`\`mermaid
graph TD
    A[Start] --> B{Is it working?}
    B -->|Yes| C[Great!]
    B -->|No| D[Debug]
    D --> B
    C --> E[Deploy]
\`\`\`

## Task Lists

- [x] Implement basic markdown
- [x] Add code highlighting
- [ ] Support mermaid diagrams
- [ ] Add interactive elements

## Callouts and Admonitions

> **Note**
> This is a note callout that can be used to highlight information.

> **Warning**
> This is a warning callout that indicates potential issues.

## Math Equations (if supported)

Inline equation: $E = mc^2$

Block equation:

$$
\\frac{d}{dx}\\left( \\int_{a}^{x} f(u)\\,du\\right)=f(x)
$$

## Footnotes

Here's a sentence with a footnote reference[^1].

[^1]: This is the footnote content.

## Emoji Support

:smile: :rocket: :computer: :bulb:
`;

export const AdvancedExample: Story = {
  args: {
    markdown: advancedMarkdownExample,
  },
  decorators: [withMonaco],
};

const augmentCodeExampleMarkdown = `# Code Explanation Example

## Function Analysis

Let's analyze the following TypeScript function:

\`\`\`typescript
/**
 * Processes a list of items with a given transformer function
 * @param items The array of items to process
 * @param transformer A function that transforms each item
 * @returns A new array with transformed items
 */
function processItems<T, R>(
  items: T[],
  transformer: (item: T, index: number) => R
): R[] {
  return items.map((item, index) => transformer(item, index));
}
\`\`\`

### Key Components:

1. **Generic Types**:
   - \`T\`: The type of input items
   - \`R\`: The type of output items after transformation

2. **Parameters**:
   - \`items\`: An array of type \`T\`
   - \`transformer\`: A callback function that takes an item and its index, returning type \`R\`

3. **Implementation**:
   - Uses the built-in \`map\` method to apply the transformer to each item
   - Preserves the index information when calling the transformer

## Usage Example

\`\`\`typescript
// Example usage with numbers
const numbers = [1, 2, 3, 4, 5];
const doubled = processItems(numbers, (num) => num * 2);
console.log(doubled); // [2, 4, 6, 8, 10]

// Example with objects
interface User {
  id: number;
  name: string;
}

const users: User[] = [
  { id: 1, name: "Alice" },
  { id: 2, name: "Bob" },
  { id: 3, name: "Charlie" }
];

const userNames = processItems(users, (user) => user.name);
console.log(userNames); // ["Alice", "Bob", "Charlie"]
\`\`\`

## Performance Considerations

- Time Complexity: O(n) where n is the number of items
- Space Complexity: O(n) for the new array created

## Alternative Approaches

Instead of using \`map\`, we could use a traditional \`for\` loop:

\`\`\`typescript
function processItemsWithLoop<T, R>(
  items: T[],
  transformer: (item: T, index: number) => R
): R[] {
  const result: R[] = [];
  for (let i = 0; i < items.length; i++) {
    result.push(transformer(items[i], i));
  }
  return result;
}
\`\`\`

This approach might be slightly more performant in some cases but is less idiomatic in modern JavaScript/TypeScript.

## Related Documentation

- [Array.prototype.map()](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/map)
- [TypeScript Generics](https://www.typescriptlang.org/docs/handbook/2/generics.html)
`;

export const CodeExplanationExample: Story = {
  args: {
    markdown: augmentCodeExampleMarkdown,
  },
  decorators: [withMonaco],
};
