/* eslint-disable @typescript-eslint/naming-convention */
import type { Meta, StoryObj } from "@storybook/svelte-vite";

import component from "./SettingsMock.svelte";
import flatComponent from "./SettingsFlatMock.svelte";

const meta = {
  title: "app/Settings",
  component,
} satisfies Meta<component>;

export default meta;

type Story = StoryObj<typeof meta>;

// More on writing stories with args: https://storybook.js.org/docs/writing-stories/args
export const Example: Story = {
  args: {},
};

export const ExampleFlat: Story = {
  args: { mode: "flat" },
  render: (args) => ({
    Component: flatComponent,
    props: args,
  }),
};
