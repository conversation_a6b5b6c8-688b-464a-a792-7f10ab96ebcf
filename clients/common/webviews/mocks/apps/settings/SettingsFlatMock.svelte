<script lang="ts">
  import SettingsMock from "./SettingsMock.svelte";

  export let mode: "tree" | "flat" = "flat";
</script>

<div class="settings-flat-container">
  <SettingsMock {mode} />
</div>

<style>
  .settings-flat-container {
    width: 100%;
    height: auto;
    min-height: 100%;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
  }

  /* Override the global styles from SettingsMock.svelte */
  :global(.settings-flat-container #storybook-root) {
    height: auto !important;
    min-height: 100% !important;
    overflow: visible !important;
  }

  :global(.settings-flat-container .augment-story-wrapper) {
    height: auto !important;
    min-height: fit-content !important;
    overflow: visible !important;
  }

  :global(.settings-flat-container .c-navigation.c-navigation--mode__flat) {
    max-height: none !important;
    height: auto !important;
    overflow: visible !important;
  }
</style>
