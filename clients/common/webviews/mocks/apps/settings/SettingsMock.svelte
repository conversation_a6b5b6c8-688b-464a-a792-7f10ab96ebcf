<script lang="ts">
  import Navigation, {
    createNavigationItem,
  } from "$common-webviews/src/common/components/navigation/Navigation.svelte";
  import {
    type NavigationItem,
    isNavigationItem,
  } from "$common-webviews/src/common/components/navigation/navigation-types";
  import ToolIcon from "$common-webviews/src/design-system/icons/augment/tools.svelte";
  import ContextDoc from "$common-webviews/src/design-system/icons/augment/context-doc.svelte";
  import { RemoteToolId, ToolHostName } from "@augment-internal/sidecar-libs/src/tools/tool-types";
  import SettingsTools from "$common-webviews/src/apps/settings/views/SettingsTools.svelte";
  import Github from "$common-webviews/src/design-system/icons/github.svelte";
  import Notion from "$common-webviews/src/design-system/icons/notion.svelte";
  import WorkspaceContext from "$common-webviews/src/apps/workspace-context/WSContext.svelte";
  import type { MCPServer } from "$vscode/src/webview-providers/webview-messages";
  import RulesCategory from "$common-webviews/src/apps/settings/views/RulesCategory.svelte";
  import Ruler from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/ruler.svg?component";
  import { RulesModel } from "$common-webviews/src/apps/settings/models/rules-model";
  import { RulesController } from "$common-webviews/src/apps/settings/controllers/rules-controller";
  import { host } from "$common-webviews/src/common/hosts/host";
  import { MessageBroker } from "$common-webviews/src/common/utils/message-broker";

  export let mode: "tree" | "flat" = "tree";
  // Initialize minimal rules plumbing for mock
  const msgBroker = new MessageBroker(host);
  const rulesModel = new RulesModel(msgBroker);
  const rulesController = new RulesController(host, msgBroker, rulesModel);
  msgBroker.registerConsumer(rulesModel);

  const settingsProps = {
    onAdd: console.log,
    onSave: console.log,
    onDelete: console.log,
    onCancel: console.log,
    onEdit: console.log,
    isMCPEnabled: true,
    tools: [
      {
        name: "GitHub",
        displayName: "GitHub",
        identifier: { hostName: ToolHostName.remoteToolHost, toolId: RemoteToolId.GitHubApi },
        config: {},
        description: "Tool 1 description",
        isConfigured: true,
        requiresAuthentication: false,
        authUrl: "",
        configString: "",
        showStatus: false,
        statusMessage: "",
        statusType: "info",
        icon: Github,
      },
      {
        name: "Notion",
        displayName: "Notion",
        identifier: { hostName: ToolHostName.remoteToolHost, toolId: RemoteToolId.Notion },
        config: {},
        description: "Tool 1 description",
        isConfigured: false,
        requiresAuthentication: false,
        authUrl: "",
        configString: "",
        showStatus: false,
        statusMessage: "",
        statusType: "info",
        icon: Notion,
      },
    ],
    onAuthenticate: console.log,
    onRevokeAccess: console.log,
    // Mock MCP server data
    servers: [
      {
        id: "1",
        name: "Memory Server",
        command: "npx -y @modelcontextprotocol/server-memory",
        arguments: "",
        useShellInterpolation: true,
        env: {
          port: "3000",
        },
      },
      {
        id: "2",
        name: "Server 2",
        command: "uv blah",
        arguments: "",
        useShellInterpolation: true,
        env: {
          shell: "bash",
        },
      },
    ],
    // MCP callback functions
    onMCPServerAdd: (server: Omit<MCPServer, "id">) => console.log("Add MCP server", server),
    onMCPServerJSONImport: (server: Omit<MCPServer, "id">) =>
      console.log("Add JSON MCP server", server),
    onMCPServerSave: (server: MCPServer) => {
      if (server.id === "2") {
        throw new Error("Server name already exists");
      }
      console.log("Save MCP server", server);
    },
    onMCPServerDelete: (serverId: string) => console.log("Delete MCP server", serverId),
  } as any;

  let items: NavigationItem[] = [
    // SVELTE5_MIGRATION - AU-11876
    // @ts-expect-error - Svelte 5 component type compatibility issue
    createNavigationItem("Tools", "Tool 1 description", ToolIcon, "section-tools"),
    // SVELTE5_MIGRATION - AU-11876
    // @ts-expect-error - Svelte 5 component type compatibility issue
    createNavigationItem("Context", "Context 3 description", ContextDoc, "section-context"),
    createNavigationItem("Rules and User Guidelines", "", Ruler, "guidelines"),
  ];
</script>

<Navigation {items} {mode}>
  <span slot="content" let:item>
    {#if !isNavigationItem(item)}
      <!-- it should always be this so please ignore, this is mostly so it compiles nicely-->
    {:else if item?.id === "section-context"}
      <WorkspaceContext />
    {:else if item?.id === "guidelines"}
      <RulesCategory {rulesModel} {rulesController} />
    {:else}
      <SettingsTools {...settingsProps} />
    {/if}
  </span>
</Navigation>

<style>
  :global(#storybook-root) {
    width: 100%;
    height: 100%;
    min-height: 100%;
  }
  :global(:root body) {
    height: 100%;
  }
  :global(.augment-story-wrapper) {
    height: 100%;
  }
  :global(.c-navigation) {
    flex: 1;
    display: flex;
    flex-direction: column;
    --settings-section-background: var(--ds-color-neutral-a4);
  }
  :global(.c-settings-navigation .c-navigation__head) {
    width: 100%;
  }
  :global(.c-navigation) {
    --settings-section-background: var(--ds-color-neutral-a2);
  }
</style>
