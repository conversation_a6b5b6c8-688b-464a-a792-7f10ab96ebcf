<script lang="ts">
  import OnboardingLayout from "$common-webviews/src/apps/main-panel/components/OnboardingLayout.svelte";
  import AugmentLogoAnimated from "$common-webviews/src/apps/main-panel/components/AugmentLogoAnimated.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";

  export let showLogo = true;
</script>

{#if showLogo}
  <div class="mock-container">
    <OnboardingLayout>
      <div slot="logo">
        <AugmentLogoAnimated />
      </div>
      <div class="content">
        <TextAugment size={2} weight="bold">Welcome to Augment</TextAugment>
        <div class="description">
          <TextAugment size={1}>
            This is a sample content for the OnboardingLayout component.
          </TextAugment>
        </div>
        <ButtonAugment variant="solid" color="accent" size={2}>Continue</ButtonAugment>
      </div>
    </OnboardingLayout>
  </div>
{:else}
  <div class="mock-container">
    <OnboardingLayout>
      <div class="content">
        <TextAugment size={2} weight="bold">Welcome to Augment</TextAugment>
        <div class="description">
          <TextAugment size={1}>
            This is a sample content for the OnboardingLayout component.
          </TextAugment>
        </div>
        <ButtonAugment variant="solid" color="accent" size={2}>Continue</ButtonAugment>
      </div>
    </OnboardingLayout>
  </div>
{/if}

<style>
  .mock-container {
    width: 100%;
    height: 100vh;
  }

  .content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--ds-spacing-4);
    max-width: 300px;
  }

  .description {
    text-align: center;
    margin-bottom: var(--ds-spacing-4);
  }
</style>
