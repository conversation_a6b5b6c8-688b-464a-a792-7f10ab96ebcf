// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { mockHost } from "../../hosts/mock-host";
import {
  type AsyncWebViewMessage,
  type WebViewMessage,
  WebViewMessageType,
} from "$vscode/src/webview-providers/webview-messages";
import * as mockBlobNames from "./mock-blob-names";
import { KeyIcon, SmallKeyIcon } from "$common-webviews/src/common/components/keybindings/key-icons";

// This function allows TypeScript to narrow the type of the event to CustomEvent
function isCustomEvent(event: Event): event is CustomEvent {
  return "detail" in event;
}

function wrapAsyncMsg<ReqT extends WebViewMessage, ResT extends WebViewMessage>(
  request: AsyncWebViewMessage<ReqT>,
  baseResponse: ResT | null,
  error: string | null = null,
): AsyncWebViewMessage<ResT> {
  return {
    type: WebViewMessageType.asyncWrapper,
    requestId: request.requestId,
    error,
    baseMsg: baseResponse,
  };
}

const originalCode: string = mockBlobNames.fileContents;
const modifiedCode: string = mockBlobNames.fileContents;

mockHost.addEventListener("message-from-webview", async (e: Event) => {
  if (!isCustomEvent(e)) {
    console.error("Unexpected event type: ", e);
    throw new Error("Unexpected mock message");
  }

  switch (e.detail.type) {
    case WebViewMessageType.asyncWrapper: {
      const baseMsg = e.detail.baseMsg;
      switch (baseMsg.type) {
        case WebViewMessageType.diffViewLoaded: {
          mockHost.sendMessageToWebView(
            wrapAsyncMsg(e.detail, {
              type: WebViewMessageType.diffViewInitialize,
              data: {
                file: {
                  repoRoot: "/home/<USER>/example-repo-root",
                  pathName: "example/path/name",
                  originalCode,
                  modifiedCode,
                },
                keybindings: {
                  acceptFocusedChunk: [KeyIcon.cmd, KeyIcon.enter].join("-"),
                  rejectFocusedChunk: [KeyIcon.cmd, KeyIcon.shift, KeyIcon.enter].join("-"),
                  focusPrevChunk: [KeyIcon.cmd, KeyIcon.arrowUp].join("-"),
                  focusNextChunk: [KeyIcon.cmd, KeyIcon.arrowDown].join("-"),
                  acceptAllChunks: [SmallKeyIcon.ctrl, SmallKeyIcon.L].join("-"),
                  rejectAllChunks: [
                    SmallKeyIcon.forwardSlash,
                    SmallKeyIcon.leftBracket,
                    SmallKeyIcon.rightBracket,
                  ].join("-"),
                },
              },
            }),
          );
          await new Promise((resolve) => setTimeout(resolve, 500));
          const msgsToForward: any[] = mockBlobNames.rmDocstrings;
          for (const msg of msgsToForward) {
            await new Promise((resolve) => setTimeout(resolve, 10));
            mockHost.sendMessageToWebView(msg);
          }
          break;
        }
        default: {
          console.error(`Unknown async message type: ${baseMsg.type}`);
          break;
        }
      }
      break;
    }
    default: {
      console.error(`Unknown message type: ${e.detail.type}`);
      break;
    }
  }
});
