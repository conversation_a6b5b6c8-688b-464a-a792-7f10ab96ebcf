<script lang="ts">
  import {
    visibilityObserverOnce,
    type VisibilityObserverOptions,
  } from "$common-webviews/src/apps/chat/components/actions/trackOnScreen";
  import SpinnerAugment from "./SpinnerAugment.svelte";
  import TextAugment from "./TextAugment.svelte";

  export let minHeight: string = "var(--ds-spacing-4)";
  export let options: VisibilityObserverOptions = {
    scrollTarget: document.body,
    threshold: 0.01,
    visibilityDuration: 100,
    throttleDuration: 10,
    ignoreScroll: true,
  };

  let hasBeenVisible = false;
  function onVisible() {
    hasBeenVisible = true;
    options?.onVisible?.();
  }
</script>

<div
  class="c-deferred-loading-augment"
  class:c-deferred-loading-augment--visible={hasBeenVisible}
  style="--deferred-loading-augment-min-height: {minHeight}"
  use:visibilityObserverOnce={{ ...options, onVisible }}
>
  {#if hasBeenVisible}
    <slot />
  {:else}
    <span class="c-deferred-loading-augment__spinner">
      <SpinnerAugment />
      <TextAugment class="c-deferred-loading-augment__text" size={1}>Loading...</TextAugment>
    </span>
  {/if}
</div>

<style>
  .c-deferred-loading-augment {
    min-height: var(--deferred-loading-augment-min-height);
    display: flex;

    /* Center the spinner */
    flex-direction: column;
    position: relative;
    align-items: center;
    justify-content: center;

    &.c-deferred-loading-augment--visible {
      display: contents;
    }

    & :global(.c-deferred-loading-augment__spinner) {
      display: flex;
      height: 100%;
      width: 100%;
      padding: var(--ds-spacing-2);
      gap: var(--ds-spacing-1);

      /* Center the spinner */
      align-items: center;
      justify-content: center;
    }

    & :global(.c-deferred-loading-augment__text) {
      opacity: 0.5;
    }
  }
</style>
