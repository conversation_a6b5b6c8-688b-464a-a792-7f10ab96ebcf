<script lang="ts" context="module">
  import {
    formatTimestampForChatMessage,
    formatFullTimestamp,
  } from "$common-webviews/src/common/utils/time-utils";
  export function timeDate(timestamp: string | Date): string {
    const date = timestamp instanceof Date ? timestamp : new Date(timestamp);
    return date.toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      hour: "numeric",
      minute: "numeric",
    });
  }
  const FMT = {
    time: formatTimestampForChatMessage,
    timeDateFull: formatFullTimestamp,
    timeDate,
  } as const;
  export type TimestampFormat = keyof typeof FMT | ((timestamp: string | Date) => string);
</script>

<script lang="ts">
  import TextAugment, { type TextSize, type TextColor } from "./TextAugment.svelte";
  import TextTooltipAugment from "./TextTooltipAugment.svelte";
  import type { TooltipContentSide } from "../_primitives/TooltipAugment/types";
  export let timestamp: string | Date | undefined = undefined;
  export let showTooltip: boolean = true;
  export let tooltipSide: TooltipContentSide = "right";
  export let textColor: TextColor = "secondary";
  export let textSize: TextSize = 1;
  export let format: TimestampFormat = formatTimestampForChatMessage;
  let className: string = "";
  export { className as class };
  $: formatTimestamp = typeof format === "function" ? format : FMT[format];
</script>

{#if timestamp}
  {#if showTooltip}
    <TextTooltipAugment content={formatFullTimestamp(timestamp)} side={tooltipSide}>
      <div class="c-timestamp-display {className}">
        <TextAugment color={textColor} size={textSize}>
          {formatTimestamp(timestamp)}
        </TextAugment>
      </div>
    </TextTooltipAugment>
  {:else}
    <div class="c-timestamp-display {className}">
      <TextAugment color={textColor} size={textSize}>
        {formatTimestamp(timestamp)}
      </TextAugment>
    </div>
  {/if}
{/if}

<style>
  .c-timestamp-display {
    white-space: nowrap;
    padding: var(--ds-spacing-2);
  }
</style>
