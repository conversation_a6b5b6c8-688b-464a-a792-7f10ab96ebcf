import { render } from "@testing-library/svelte";
import IconButtonAugment from "./IconButtonAugment.svelte";
import IconButtonAugmentTest from "./IconButtonAugmentTest.svelte";
import { describe, it, expect, vi } from "vitest";
import userEvent from "@testing-library/user-event";

describe("IconButtonAugment", () => {
  it("renders with default props", () => {
    const { getByRole } = render(IconButtonAugment);
    const button = getByRole("button");
    expect(button).toBeTruthy();
  });

  it("renders with known props", () => {
    const { getByRole } = render(IconButtonAugment, {
      props: { size: 3, variant: "ghost", color: "neutral", class: "custom-class" },
    });
    const button = getByRole("button");
    expect(button).toBeTruthy();
    expect(button.classList.contains("c-base-btn--size-3")).toBe(true);
    expect(button.classList.contains("c-base-btn--ghost")).toBe(true);
    expect(button.classList.contains("c-base-btn--neutral")).toBe(true);
    expect(button.classList.contains("custom-class")).toBe(true);
  });

  it("renders with slot content", () => {
    render(IconButtonAugmentTest);
    const svgs = document.getElementsByTagName("svg");
    expect(svgs.length).toBeGreaterThan(0);
  });

  it("handles click events", async () => {
    const user = userEvent.setup();

    // Set event handlers
    const onClick = vi.fn();
    const onKeyup = vi.fn();
    const onKeydown = vi.fn();

    const { getByRole } = render(IconButtonAugment, {
      props: {
        onclick: onClick,
        onkeyup: onKeyup,
        onkeydown: onKeydown,
      },
    });
    const button = getByRole("button");

    // Click the button
    await user.click(button);
    expect(onClick).toHaveBeenCalled();

    // Focus the button and press enter
    button.focus();
    await user.keyboard("{Enter}");
    expect(onKeyup).toHaveBeenCalled();
    expect(onKeydown).toHaveBeenCalled();
  });

  it("disables the button when disabled prop is true", async () => {
    const user = userEvent.setup();

    // Set event handlers
    const onClick = vi.fn();
    const onKeyup = vi.fn();
    const onKeydown = vi.fn();

    const { getByRole } = render(IconButtonAugment, {
      props: {
        disabled: true,
        onclick: onClick,
        onkeyup: onKeyup,
        onkeydown: onKeydown,
      },
    });
    const button = getByRole("button");
    expect(button).toBeDisabled();

    // Click the button
    await user.click(button);
    expect(onClick).not.toHaveBeenCalled();

    // Focus the button and press enter
    button.focus();
    await user.keyboard("{Enter}");
    expect(onKeyup).not.toHaveBeenCalled();
    expect(onKeydown).not.toHaveBeenCalled();
  });

  it("shows loading state when loading prop is true", () => {
    const { getByTestId } = render(IconButtonAugment, { props: { loading: true } });
    const spinnter = getByTestId("spinner-augment");
    expect(spinnter).toBeTruthy();
  });

  it("renders with additional props", async () => {
    const { rerender, getByRole } = render(IconButtonAugment, {
      props: { title: "Example title" },
    });
    const button = getByRole("button");
    expect(button).toBeTruthy();
    expect(button.attributes.getNamedItem("title")?.value).toBe("Example title");

    await rerender({ title: "New title" });
    expect(button.attributes.getNamedItem("title")?.value).toBe("New title");
  });
});
