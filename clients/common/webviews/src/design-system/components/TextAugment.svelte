<script lang="ts" context="module">
  export type TextSize = 0 | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9;
  export type TextWeight = "light" | "regular" | "medium" | "bold";
  export type TextType = "monospace" | "default";
  export type TextColor = "accent" | "neutral" | "success" | "error" | "primary" | "secondary";
</script>

<script lang="ts">
  import { dsColorAttribute } from "../_libs/component-utils";

  export let size: TextSize = 3;
  export let weight: TextWeight = "regular";
  export let type: TextType = "default";
  export let color: TextColor | undefined = undefined;
  export let truncate = false;
  $: ({ class: className, ...restProps } = $$restProps);
</script>

<span
  {...color ? dsColorAttribute(color) : {}}
  class="c-text c-text--size-{size} c-text--weight-{weight} c-text--type-{type} c-text--color-{color} {className}"
  class:c-text--has-color={color !== undefined}
  class:c-text--truncate={truncate}
  {...restProps}><slot /></span
>

<style>
  .c-text {
    /* We  don't want the span itself to impact the layout of the slot */
    display: contents;

    font-size: var(--text-font-size);
    line-height: var(--text-line-height);
    letter-spacing: var(--text-letter-spacing);
  }

  .c-text--truncate {
    display: inline;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .c-text--has-color {
    --text-color: var(--ds-color-a12);
    color: var(--text-color);
  }

  /* Secondary colors for light & cark */
  .c-text--color-secondary {
    --text-color: var(--ds-color-a9);
  }

  :global(.dark) .c-text.c-text--color-secondary {
    --text-color: var(--ds-color-a8);
  }

  .c-text--type-monospace {
    font-family: var(--augment-monospace-font-family), monospace;
  }
  .c-text--size-0 {
    font-size: var(--ds-font-size-0); /* 10px */
    line-height: var(--ds-line-height-0); /* 14px */
    letter-spacing: 0;
  }
  .c-text--size-1 {
    font-size: var(--ds-font-size-1); /* 12px */
    line-height: var(--ds-line-height-1); /* 16px */
    letter-spacing: 0.0025em;
  }
  .c-text--size-2 {
    font-size: var(--ds-font-size-2); /* 14px */
    line-height: var(--ds-line-height-2); /* 20px */
    letter-spacing: 0em;
  }
  .c-text--size-3 {
    font-size: var(--ds-font-size-3); /* 16px */
    line-height: var(--ds-line-height-3); /* 24px */
    letter-spacing: 0em;
  }
  .c-text--size-4 {
    font-size: var(--ds-font-size-4); /* 18px */
    line-height: var(--ds-line-height-4); /* 26px */
    letter-spacing: -0.0025em;
  }
  .c-text--size-5 {
    font-size: var(--ds-font-size-5); /* 20px */
    line-height: var(--ds-line-height-5); /* 28px */
    letter-spacing: -0.005em;
  }
  .c-text--size-6 {
    font-size: var(--ds-font-size-6); /* 24px */
    line-height: var(--ds-line-height-6); /* 30px */
    letter-spacing: -0.00625em;
  }
  .c-text--size-7 {
    font-size: var(--ds-font-size-7); /* 30px */
    line-height: var(--ds-line-height-7); /* 36px */
    letter-spacing: -0.0075em;
  }
  .c-text--size-8 {
    font-size: var(--ds-font-size-8); /* 36px */
    line-height: var(--ds-line-height-8); /* 40px */
    letter-spacing: -0.01em;
  }
  .c-text--size-9 {
    font-size: var(--ds-font-size-9); /* 48px */
    line-height: var(--ds-line-height-9); /* 60px */
    letter-spacing: -0.025em;
  }
  .c-text--weight-light {
    font-weight: 300;
  }
  .c-text--weight-regular {
    font-weight: 400;
  }
  .c-text--weight-medium {
    font-weight: 500;
  }
  .c-text--weight-bold {
    font-weight: 700;
  }
</style>
