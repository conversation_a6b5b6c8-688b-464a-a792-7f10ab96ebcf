<script lang="ts">
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import type {
    TextSize,
    TextColor,
  } from "$common-webviews/src/design-system/components/TextAugment.svelte";

  const FRAME_DURATION = 16; // 60 FPS

  // Props that match TextAugment for consistency
  // SVELTE5_MIGRATION - Use $props runes style
  let {
    value = 0,
    size = 1,
    color = "neutral",
    maxDuration = 500, // Maximum animation duration in milliseconds
    easing = (t: number) => t * t * (3 - 2 * t), // Smooth step easing
    delay = 0, // Additional delay before animation starts in milliseconds
    autoStart = false, // If true, starts animation immediately when set to true
  }: {
    value?: number;
    size?: TextSize;
    color?: TextColor;
    maxDuration?: number;
    easing?: (t: number) => number;
    delay?: number;
    autoStart?: boolean;
  } = $props();

  const animationState = $state({
    value: 0,
  });

  const digits = $derived.by(() => String(animationState.value).length);

  function now(): number {
    // Prefer performance.now() when available
    return typeof performance !== "undefined" && typeof performance.now === "function"
      ? performance.now()
      : Date.now();
  }

  // Calculate optimal animation duration based on the target value
  function calculateDuration(targetValue: number): number {
    if (targetValue === 0) return FRAME_DURATION;

    // Calculate duration based on max increment time
    // This ensures small numbers animate quickly (each increment takes at most maxIncrementTime)
    const incrementBasedDuration = targetValue * FRAME_DURATION;

    // Cap the duration at maxDuration to prevent overly long animations for large numbers
    // Apply minimum duration to prevent animations that are too fast
    const cappedDuration = Math.min(incrementBasedDuration, maxDuration);

    return Math.max(FRAME_DURATION, cappedDuration);
  }

  // Animation function using requestAnimationFrame for smooth performance
  function animateToValue(startValue: number, targetValue: number) {
    if (targetValue === startValue) return;

    animationState.value = startValue; // Reset when starting animation

    // Calculate optimal duration for this specific value
    const animationDuration = calculateDuration(targetValue);
    let animationFrameRequest: number | undefined = undefined;

    // Apply delay before starting animation
    const timeout = setTimeout(() => {
      const startTime = now();

      function animate(currentTime: number) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / animationDuration, 1);

        // Apply easing function
        const easedProgress = easing(progress);

        // Calculate current value
        animationState.value = Math.round(startValue + (targetValue - startValue) * easedProgress);

        // Continue animation if not complete
        if (progress < 1) {
          animationFrameRequest = requestAnimationFrame(animate);
        } else {
          animationState.value = targetValue; // Ensure we end exactly at target
        }
      }

      animationFrameRequest = requestAnimationFrame(animate);
    }, delay);

    return () => {
      if (animationFrameRequest) {
        cancelAnimationFrame(animationFrameRequest);
      }
      clearTimeout(timeout);
    };
  };

  $effect(() => {
    if (!autoStart) {
      animationState.value = 0;
      return;
    }

    // Animate to value will animate only if value is changed.
    return animateToValue(animationState.value, value);
  });
</script>

<span class="c-animated-number-indicator" style="--digits:{digits}">
  <TextAugment {size} {color}>
    {animationState.value}
  </TextAugment>
</span>

<style>
  .c-animated-number-indicator :global(span) {
    display: block;
    font-variant-numeric: tabular-nums;
    /* This transition prevent jumping when digit count changes */
    transition: width 150ms;
    width: calc(var(--digits, 1) * 1ch);
    overflow: hidden;
  }
</style>
