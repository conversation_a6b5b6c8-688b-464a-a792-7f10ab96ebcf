<script lang="ts">
  import IconButtonAugment from "../IconButtonAugment.svelte";
  import Collapse from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/collapse.svg?component";
  import UpDownIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/angles-up-down.svg?component";
  import { getCollapsibleContext } from "./context";

  const { collapsed, setCollapsed } = getCollapsibleContext();

  function toggleCollapsed() {
    setCollapsed(!$collapsed);
  }
</script>

<IconButtonAugment
  variant="ghost-block"
  color="neutral"
  size={1}
  on:click={toggleCollapsed}
  {...$$restProps}
>
  <span class="c-collapse-button-augment__icon">
    <slot collapsed={$collapsed}>
      {#if $collapsed}
        <UpDownIcon />
      {:else}
        <Collapse />
      {/if}
    </slot>
  </span>
</IconButtonAugment>

<style>
  .c-collapse-button-augment__icon {
    display: flex;
    --icon-size: 12px;
  }
</style>
