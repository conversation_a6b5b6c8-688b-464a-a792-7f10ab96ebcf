import { getContext, setContext } from "svelte";
import type { Readable, Writable } from "svelte/store";

export interface CollapsibleContext {
  collapsed: Readable<boolean>;
  setCollapsed: (value: boolean) => void;
  toggle(): void;
  expandable: Writable<boolean>;
}

export const COLLAPSIBLE_CONTEXT_KEY = Symbol("collapsible");

export function setCollapsibleContext(context: CollapsibleContext) {
  setContext(COLLAPSIBLE_CONTEXT_KEY, context);
}

export function getCollapsibleContext(): CollapsibleContext {
  return getContext(COLLAPSIBLE_CONTEXT_KEY);
}
