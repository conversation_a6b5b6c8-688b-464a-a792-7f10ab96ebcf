<script lang="ts">
  import { STICKY_HEADER_TOP_PX } from "./CollapsibleAugment/constants";

  /**
   * GradientMask component applies a gradient mask to its children
   * Useful for creating fade effects at the edges of scrollable containers
   */

  // Direction of the gradient mask
  export let direction: "vertical" | "horizontal" = "vertical";

  // Size of the fade effect in pixels
  export let fadeSize: number = STICKY_HEADER_TOP_PX;

  // Custom class to apply to the container
  let className = "";
  export { className as class };
</script>

<div
  class="c-gradient-mask {className}"
  style="--fade-size: {fadeSize}px"
  class:is-horizontal={direction === "horizontal"}
>
  <slot />
</div>

<style>
  .c-gradient-mask {
    position: relative;
    height: 100%;
    width: 100%;
    overflow: hidden;
    mask-image: linear-gradient(
      to bottom,
      transparent 0%,
      black var(--fade-size),
      black calc(100% - var(--fade-size)),
      transparent 100%
    );
    -webkit-mask-image: linear-gradient(
      to bottom,
      transparent 0%,
      black var(--fade-size),
      black calc(100% - var(--fade-size)),
      transparent 100%
    );
  }

  /* Horizontal gradient mask (left and right fade) */
  .c-gradient-mask.is-horizontal {
    mask-image: linear-gradient(
      to right,
      transparent 0%,
      black var(--fade-size),
      black calc(100% - var(--fade-size)),
      transparent 100%
    );
    -webkit-mask-image: linear-gradient(
      to right,
      transparent 0%,
      black var(--fade-size),
      black calc(100% - var(--fade-size)),
      transparent 100%
    );
  }
</style>
