<script lang="ts">
  import Trigger from "../../_primitives/TooltipAugment/Trigger.svelte";
  import { getContext } from "svelte";
  import DropdownMenuFocusContext from "./focus-context";
  import { TooltipContext } from "../../_primitives/TooltipAugment/context";

  export let referenceClientRect: DOMRect | undefined = undefined;

  // If we use arrow keys while focused on the trigger, we should open the menu
  // if it is not already open and focus the first item
  const focusContext = getContext<DropdownMenuFocusContext>(DropdownMenuFocusContext.CONTEXT_KEY);
  const tooltipContext = getContext<TooltipContext>(TooltipContext.CONTEXT_KEY);
  const openState = tooltipContext.state;

  const onKeyDown = async (e: KeyboardEvent) => {
    switch (e.key) {
      case "ArrowUp":
        e.preventDefault(); // Prevent scroll
        e.stopPropagation();
        if (!$openState.open) {
          await focusContext.clickFocusedItem();
        }
        focusContext?.focusIdx(-1);
        break;
      case "ArrowDown":
        e.preventDefault(); // Prevent scroll
        e.stopPropagation();
        if (!$openState.open) {
          await focusContext.clickFocusedItem();
        }
        focusContext?.focusIdx(0);
        break;
      case "Enter":
        e.preventDefault();
        e.stopPropagation();
        focusContext?.clickFocusedItem();
        break;
    }
  };
</script>

<Trigger on:keydown={onKeyDown} {referenceClientRect}>
  <div use:focusContext.registerTrigger use:tooltipContext.registerTrigger>
    <slot />
  </div>
</Trigger>
