import { tick } from "svelte";
import { type Action } from "svelte/action";
import { writable, type Readable } from "svelte/store";
import { CloseTooltipRequestEvent } from "../../_primitives/TooltipAugment/types";
import { FocusTrapStack } from "../../../common/utils/focusTrapStack.js";

/**
 * Context for managing focus within a dropdown menu.
 * Manages focus state for focusable items within the dropdown menu.
 * Automatically sets the context on construction, and provides a static method to retrieve it.
 *
 * Usage:
 * 1. Create a new instance of DropdownMenuFocusContext in the root component of the dropdown menu.
 * 1.a. Make sure to register the context using setContext from svelte
 * 2. Register the container element for this focus context using registerRoot. This is the element
 *    we will search for focusable items within.
 * 3. If you need to access the context from a child component, use getContext from svelte.
 */
export default class DropdownMenuFocusContext {
  /* eslint-disable @typescript-eslint/naming-convention */
  public static CONTEXT_KEY = "augment-dropdown-menu-focus";
  public static ITEM_CLASS = "js-dropdown-menu__focusable-item";
  /* eslint-enable @typescript-eslint/naming-convention */

  /**
   * Computes the focus anchor of this context relative to the parent
   * context. This is computed by finding the index of the first item in this context,
   * and then finding the previous one in the parent context.
   */
  private _lastFocusAnchorElement: HTMLElement | undefined;

  /**
   * Store that tracks the currently focused index
   * This is a writable store internally, but exposed as a readable store
   */
  private _focusedIndexStore = writable<number | undefined>(undefined);

  /**
   * Readable store that consumers can subscribe to for tracking the focused index
   */
  public readonly focusedIndex: Readable<number | undefined> = this._focusedIndexStore;

  constructor(private _parentContext: DropdownMenuFocusContext | undefined = undefined) {}

  /**
   * Keep track of the root element of the dropdown menu.
   *
   * This is the element we will search for focusable items within. Some invariants:
   * - We can only have one root element per context.
   * - The only items we can focus using this context are elements inside the root element
   *   that have the focusable item class.
   * - If the root element is unregistered, we will no longer be able to focus any items.
   */
  private _rootElement: HTMLElement | undefined;
  public get rootElement(): HTMLElement | undefined {
    return this._rootElement;
  }

  /**
   * Keep track of the trigger element of the dropdown menu.
   *
   * This is the element that will be focused when the dropdown menu is closed.
   */
  private _triggerElement: HTMLElement | undefined;
  public get triggerElement(): HTMLElement | undefined {
    return this._triggerElement;
  }

  /**
   * The parent context of this context.
   * This is used to manage focus when we have nested dropdown menus.
   */
  public get parentContext(): DropdownMenuFocusContext | undefined {
    return this._parentContext;
  }

  /**
   * Retrieves all focusable items within the dropdown menu.
   * @returns An array of HTMLElements that are focusable within the dropdown menu.
   */
  private _getItems = (): HTMLElement[] => {
    const items = this._rootElement?.querySelectorAll(`.${DropdownMenuFocusContext.ITEM_CLASS}`);
    // Recompute last focus anchor
    const firstElement = items?.[0];
    if (firstElement instanceof HTMLElement) {
      this._recomputeFocusAnchor(firstElement);
    }

    return Array.from(items ?? []) as HTMLElement[];
  };

  private _recomputeFocusAnchor = (firstElement: HTMLElement) => {
    const parentItems = this._parentContext?._getItems();
    const firstElementRelativeIdx: number | undefined = parentItems?.indexOf(firstElement);
    if (firstElementRelativeIdx === undefined || parentItems === undefined) {
      return;
    }
    const anchorIdx = Math.max(firstElementRelativeIdx - 1, 0);
    this._lastFocusAnchorElement = parentItems[anchorIdx];
  };

  /**
   * Svelte action that registers the root element of the dropdown menu.
   * @param node The root element of the dropdown menu.
   */
  registerRoot: Action<HTMLElement> = (node: HTMLElement) => {
    this._rootElement = node;
    node.addEventListener("keydown", this._onKeyDown);

    // Set up focus/blur event listeners to track focus state
    const onFocusIn = () => {
      // When focus enters the dropdown, update the focused index
      this.getCurrentFocusedIdx();
    };

    const onFocusOut = (e: FocusEvent) => {
      // Only clear the focused index if focus is leaving the dropdown entirely
      // and not just moving to another element within it
      if (!node.contains(e.relatedTarget as Node)) {
        this._focusedIndexStore.set(undefined);
      }
    };

    node.addEventListener("focusin", onFocusIn);
    node.addEventListener("focusout", onFocusOut);

    this._getItems();

    // Cleanup
    return {
      destroy: () => {
        this._removeFromTrapStack();
        this._rootElement = undefined;
        node.removeEventListener("keydown", this._onKeyDown);
        node.removeEventListener("focusin", onFocusIn);
        node.removeEventListener("focusout", onFocusOut);
        this._focusedIndexStore.set(undefined);
      },
    };
  };

  /**
   * Svelte action that registers the trigger element of the dropdown menu.
   * @param node The trigger element of the dropdown menu.
   */
  registerTrigger: Action<HTMLElement> = (node: HTMLElement) => {
    // The trigger element will be focused when the dropdown menu is closed when the user
    // tabs out of the dropdown menu. We should not directly use the node element
    // of the trigger as it is a div that should not be focused. Instead, we should
    // find the first focusable element within the trigger element's slot.
    this._triggerElement =
      (node.querySelector(
        'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])',
      ) as HTMLElement) ?? node;

    // Cleanup
    return {
      destroy: () => {
        this._triggerElement = undefined;
      },
    };
  };

  private _onKeyDown = (e: KeyboardEvent) => {
    switch (e.key) {
      case "ArrowUp":
        e.preventDefault();
        this.focusPrev();
        break;
      case "ArrowDown":
        e.preventDefault();
        this.focusNext();
        break;
      case "ArrowLeft":
        this._requestClose();
        break;
      case "ArrowRight":
        void this.clickFocusedItem();
        break;
      case "Tab": {
        const index = this.getCurrentFocusedIdx();
        if (index === undefined || this.parentContext) {
          break;
        }
        // When Tippy is appended to the document body rather than the trigger
        // we need to manage focus manually. These conditions allow focus tabbing
        // to exit the dropdown menu and return to the trigger element when the
        // focus is on the first or last item.
        // https://atomiks.github.io/tippyjs/v6/faq/#my-tooltip-appears-cut-off-or-is-not-showing-at-all
        if ((!e.shiftKey && index === this._getItems().length - 1) || (e.shiftKey && index === 0)) {
          e.preventDefault();
          this._triggerElement?.focus();
        }
        break;
      }
    }
  };

  private _requestClose = () => {
    this._rootElement?.dispatchEvent(new CloseTooltipRequestEvent());
  };

  /**
   * Gets the index of the currently focused item.
   * @returns The index of the currently focused item, or undefined if there is no focused item.
   */
  getCurrentFocusedIdx = () => {
    const items = this._getItems();
    const currIdx = items.findIndex((item) => item === document.activeElement);
    const result = currIdx === -1 ? undefined : currIdx;

    // Update the store with the current focus index
    this._focusedIndexStore.set(result);

    return result;
  };

  /**
   * Updates the focused index without actually focusing the element.
   * This is useful for tracking hover state.
   * @param idx The index to set as focused. Handles overflow/underflow
   */
  setFocusedIdx = (idx: number) => {
    const items = this._getItems();
    if (items.length === 0) {
      this._focusedIndexStore.set(undefined);
      return;
    }

    const wrappedIdx = wrapIdx(idx, items.length);
    // Only update the store, don't actually focus the element
    this._focusedIndexStore.set(wrappedIdx);
  };

  /**
   * Focuses a specific item in the dropdown menu by index.
   * @param idx The index of the item to focus. Handles overflow/underflow
   */
  focusIdx = (idx: number) => {
    const items = this._getItems();
    if (items.length === 0) {
      this._focusedIndexStore.set(undefined);
      return;
    }

    const wrappedIdx = wrapIdx(idx, items.length);
    const targetItem: HTMLElement | undefined = items[wrappedIdx];
    targetItem?.focus();

    // Update the store with the new focus index
    this._focusedIndexStore.set(wrappedIdx);
  };

  /**
   * Pops the focus back to the nearest parent context with the focusable item class.
   * @return true if the focus was popped, false if there is no parent context.
   */
  popNestedFocus = (): boolean => {
    if (this._parentContext) {
      // Focus the first item in this context, then have the parent take control
      // If the context is as such:
      // - Top-level 1
      // - Top-level 2
      //   - Child 1
      //   - Child 2 <----
      // Then when we pop focus from Child 2:
      // - Top-level 1
      // - Top-level 2
      //   - Child 1
      //   - Child 2 <---- popFocus()
      // we first focus Child 1
      // - Top-level 1
      // - Top-level 2
      //   - Child 1 <---- after focusIdx(0)
      //   - Child 2 <---- before focusIdx(0)
      // then have the parent focus the previous element.
      // - Top-level 1
      // - Top-level 2 <---- after focusPrev()
      //   - Child 1 <---- before focusPrev()
      //   - Child 2

      // Clear the focused index in this context
      this._focusedIndexStore.set(undefined);

      // Focus the last focus anchor in the parent context
      const e = this._lastFocusAnchorElement;
      const lastFocusAnchorIdx = e ? this._parentContext._getItems().indexOf(e) : undefined;
      if (lastFocusAnchorIdx === undefined) {
        this._parentContext.focusIdx(0);
        return true;
      } else {
        this._parentContext.focusIdx(lastFocusAnchorIdx);
        return true;
      }
    }
    return false;
  };

  /**
   * Focuses the next item in the dropdown menu.
   * If the last item is currently focused, it wraps around to the first item.
   */
  focusNext = () => {
    const items = this._getItems();
    if (items.length === 0) return;

    const currIdx = items.findIndex((item) => item === document.activeElement);
    const nextIdx = wrapIdx(currIdx + 1, items.length);
    items[nextIdx].focus();

    // Update the store with the new focus index
    this._focusedIndexStore.set(nextIdx);
  };

  /**
   * Focuses the previous item in the dropdown menu.
   * If the first item is currently focused, it wraps around to the last item.
   */
  focusPrev = () => {
    const items = this._getItems();
    if (items.length === 0) return;

    const currIdx = items.findIndex((item) => item === document.activeElement);
    // Handle underflow case
    const prevIdx = wrapIdx(currIdx - 1, items.length);
    items[prevIdx]?.focus();

    // Update the store with the new focus index
    this._focusedIndexStore.set(prevIdx);
  };

  /**
   * Selects (clicks) the currently focused item in the dropdown menu.
   */
  clickFocusedItem = async () => {
    const currItem = document.activeElement as HTMLElement | null;
    if (currItem) {
      currItem.click();
      await tick();
    }
  };

  /**
   * Add this dropdown to the focus trap stack when it opens
   */
  private _addToTrapStack = () => {
    if (!this._rootElement) return;
    FocusTrapStack.add(this._rootElement);
  };

  /**
   * Remove this dropdown from the focus trap stack when it closes
   */
  private _removeFromTrapStack = () => {
    if (!this._rootElement) return;
    FocusTrapStack.remove(this._rootElement);
  };

  /**
   * Handle dropdown open/close state changes for focus trap coordination
   * Call this method when the dropdown opens or closes
   */
  handleOpenChange = (open: boolean) => {
    if (open) {
      this._addToTrapStack();
    } else {
      this._removeFromTrapStack();
    }
  };
}

// Handle underflow case by:
// - constraining to [-items.length, items.length]
// - adding items.length to ensure we have a non-negative number
// - taking modulo to constrain to [0, items.length)
function wrapIdx(idx: number, itemCount: number): number {
  return ((idx % itemCount) + itemCount) % itemCount;
}
