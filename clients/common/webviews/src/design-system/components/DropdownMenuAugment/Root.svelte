<script lang="ts">
  import TooltipRoot from "$common-webviews/src/design-system/_primitives/TooltipAugment/Root.svelte";
  import { TooltipTriggerOn } from "$common-webviews/src/design-system/_primitives/TooltipAugment/types";
  import { getContext, setContext } from "svelte";
  import type { Readable } from "svelte/store";
  import DropdownMenuFocusContext from "./focus-context";

  export let defaultOpen: boolean | undefined = undefined;
  export let open: boolean | undefined = undefined;
  export let onOpenChange: ((open: boolean) => void) | undefined = undefined;
  export let delayDurationMs: number | undefined = undefined;
  export let nested: boolean | undefined = undefined;
  export let onHoverStart = () => {};
  export let onHoverEnd = () => {};
  export let triggerOn: TooltipTriggerOn[] = [TooltipTriggerOn.Click];

  function handleOpenChange(open: boolean) {
    focusContext.handleOpenChange(open);
    onOpenChange?.(open);
  }

  // Public API for external controls
  let tooltipRoot: TooltipRoot;
  export const requestOpen = () => tooltipRoot?.requestOpen();
  export const requestClose = () => tooltipRoot?.requestClose();

  // Hook into focus
  export const focusIdx = (idx: number) => focusContext.focusIdx(idx);

  // Set the focused index without actually focusing the element
  export const setFocusedIdx = (idx: number) => focusContext.setFocusedIdx(idx);

  // Get the current focused index directly
  export const getCurrentFocusedIdx = () => focusContext.getCurrentFocusedIdx();

  // Expose the focused index as a readable store
  const maybeParentContext: DropdownMenuFocusContext | undefined =
    getContext<DropdownMenuFocusContext>(DropdownMenuFocusContext.CONTEXT_KEY);
  const focusContext = new DropdownMenuFocusContext(maybeParentContext);
  setContext<DropdownMenuFocusContext>(DropdownMenuFocusContext.CONTEXT_KEY, focusContext);

  // Export the focused index store for consumers to subscribe to
  export const focusedIndex: Readable<number | undefined> = focusContext.focusedIndex;
</script>

<TooltipRoot
  {defaultOpen}
  {open}
  onOpenChange={handleOpenChange}
  {delayDurationMs}
  {onHoverStart}
  {onHoverEnd}
  {triggerOn}
  {nested}
  bind:this={tooltipRoot}
  {...$$restProps}
>
  <slot />
</TooltipRoot>
