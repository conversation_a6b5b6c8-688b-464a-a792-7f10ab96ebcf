import { render } from "@testing-library/svelte";
import userEvent from "@testing-library/user-event";
import { expect, describe, test, afterEach, beforeEach, vi } from "vitest";

import TestDropdownMenu from "./index.test.svelte";
import { TooltipContext } from "../../_primitives/TooltipAugment/context";
import { tick } from "svelte";

const INTERACTION_DURATION_MS = 33; // Based on 30FPS

let component: ReturnType<typeof render<TestDropdownMenu>>;
beforeEach(async () => {
  vi.useFakeTimers();
  component = render(TestDropdownMenu);
  await tick();
  expectNoContent();
  expectNoSubContent();
});

afterEach(() => {
  vi.useRealTimers();
  vi.clearAllMocks();
  component?.unmount();
});

const expectNoContent = (): void => {
  const content = component.queryByText("Item 1");
  if (content) {
    expect(content).not.toBeVisible();
  }
};
const expectNoSubContent = () => {
  const subContent = component.queryByText("Submenu 1 Item 1");
  if (subContent) {
    expect(subContent).not.toBeVisible();
  }
};
const expectTrigger = () => {
  const trigger = component.queryByText("Trigger");
  expect(trigger).not.toBeNull();
  return trigger;
};
const expectSubTrigger = () => {
  const trigger = component.queryByText("Item 5, which is a submenu");
  expect(trigger).not.toBeNull();
  return trigger;
};
const expectContent = () => {
  const content = component.queryByText("Item 1");
  expect(content).not.toBeNull();
};
const expectSubContent = () => {
  const content = component.queryByText("Submenu 1 Item 1");
  expect(content).not.toBeNull();
};
const openSubMenuHover = async () => {
  const trigger = expectTrigger();
  await clickWithFakeTime(trigger!);
  expectContent();

  const subTrigger = expectSubTrigger();
  await hoverWithFakeTime(subTrigger!);
  await vi.advanceTimersByTimeAsync(TooltipContext.DEFAULT_DELAY_DURATION_MS);
  expectSubContent();

  return {
    trigger,
    subTrigger,
  };
};
const openSubMenuClick = async () => {
  const trigger = expectTrigger();
  await clickWithFakeTime(trigger!);
  expectContent();

  const subTrigger = expectSubTrigger();
  await clickWithFakeTime(subTrigger!);
  expectSubContent();

  return {
    trigger,
    subTrigger,
  };
};

describe("DropdownMenuAugment", () => {
  test("renders with default props", async () => {
    expect(component.getByText("Trigger").closest("button")).toBeInTheDocument();
  });

  test("clicking trigger opens menu", async () => {
    expect.hasAssertions();
    const trigger = expectTrigger();
    await clickWithFakeTime(trigger!);
    expectContent();
  });

  test("clicking outside closes menu", async () => {
    expect.hasAssertions();
    const trigger = expectTrigger();
    await clickWithFakeTime(trigger!);
    expectContent();

    await clickWithFakeTime(document.body);
    expectNoContent();
  });

  test("hovering sub-trigger opens menu", async () => {
    expect.hasAssertions();
    await openSubMenuHover();
    expectSubContent();
  });

  test("hovering sub-trigger opens menu and unhovering closes it", async () => {
    expect.hasAssertions();
    const { subTrigger } = await openSubMenuHover();
    await unhoverWithFakeTime(subTrigger!);
    await vi.advanceTimersByTimeAsync(TooltipContext.DEFAULT_DELAY_DURATION_MS);
    expectNoSubContent();
  });

  test("hovering sub-trigger opens menu and clicking sub-trigger closes it", async () => {
    expect.hasAssertions();
    const { subTrigger } = await openSubMenuHover();
    await clickWithFakeTime(subTrigger!);
    expectNoSubContent();
  });

  test("hovering sub-trigger opens menu and clicking outside closes it", async () => {
    expect.hasAssertions();
    await openSubMenuHover();

    await clickWithFakeTime(document.body);
    expectNoSubContent();
  });

  test("clicking sub-trigger opens menu", async () => {
    expect.hasAssertions();
    await openSubMenuClick();
    expectSubContent();
  });

  test("clicking sub-trigger opens menu and clicking outside closes it", async () => {
    expect.hasAssertions();
    await openSubMenuClick();

    await clickWithFakeTime(document.body);
    expectNoSubContent();
  });

  test("clicking sub-trigger opens menu and clicking sub-trigger closes it", async () => {
    expect.hasAssertions();
    const { subTrigger } = await openSubMenuClick();
    await clickWithFakeTime(subTrigger!);
    expectNoSubContent();
  });

  test("clicking sub-trigger opens menu and clicking trigger closes it", async () => {
    expect.hasAssertions();
    const { trigger } = await openSubMenuClick();
    await clickWithFakeTime(trigger!);
    expectNoSubContent();
  });
});

describe("data attributes", () => {
  test("disabled item has data-disabled attribute", async () => {
    const trigger = expectTrigger();
    await clickWithFakeTime(trigger!);
    expectContent();

    const disabledItem = component.queryByText("Item 4, which is disabled")?.closest("button");
    expect(disabledItem).toHaveAttribute("data-disabled");
  });

  test("highlighted item has data-highlighted attribute", async () => {
    const { subTrigger } = await openSubMenuHover();

    const highlightedItem = subTrigger?.closest("button");
    expect(highlightedItem).toHaveAttribute("data-highlighted");
  });
});

describe("focus management", () => {
  test("focus on trigger when menu opened on click", async () => {
    const trigger = expectTrigger();
    await clickWithFakeTime(trigger!);
    expectContent();

    expect(trigger?.closest("button")).toHaveFocus();
  });

  test("tab should focus into menu", async () => {
    const trigger = expectTrigger();
    await clickWithFakeTime(trigger!);
    expectContent();

    await keyboardWithFakeTime("{Tab}");
    expect(component.getByText("Item 1").closest("button")).toHaveFocus();
  });

  test("up arrow should focus into menu from trigger", async () => {
    const trigger = expectTrigger();
    await clickWithFakeTime(trigger!);
    expectContent();

    await keyboardWithFakeTime("{ArrowUp}");
    expect(component.getByText("Item 6, which is also a submenu").closest("button")).toHaveFocus();
  });

  test("up arrow should open menu and focus on last item", async () => {
    const trigger = expectTrigger();
    // Focus the trigger
    trigger?.closest("button")?.focus();
    await keyboardWithFakeTime("{ArrowUp}");

    expectContent();
    expect(component.getByText("Item 6, which is also a submenu").closest("button")).toHaveFocus();
  });

  test("down arrow should focus into menu from trigger", async () => {
    const trigger = expectTrigger();
    await clickWithFakeTime(trigger!);
    expectContent();

    await keyboardWithFakeTime("{ArrowDown}");
    expect(component.getByText("Item 1").closest("button")).toHaveFocus();
  });

  test("down arrow should open menu and focus on first item", async () => {
    const trigger = expectTrigger();
    // Focus the trigger
    trigger?.closest("button")?.focus();
    await keyboardWithFakeTime("{ArrowDown}");

    expectContent();
    expect(component.getByText("Item 1").closest("button")).toHaveFocus();
  });

  test("tab twice should focus on item 2", async () => {
    const trigger = expectTrigger();
    await clickWithFakeTime(trigger!);
    expectContent();

    await keyboardWithFakeTime("{Tab}");
    await keyboardWithFakeTime("{Tab}");
    expect(component.getByText("Item 2, which is longer").closest("button")).toHaveFocus();
  });

  const tabFromTriggerToSubTrigger = async () => {
    const trigger = expectTrigger();
    expect(trigger?.closest("button")).toHaveFocus();
    await keyboardWithFakeTime("{Tab}"); // Item 1
    await keyboardWithFakeTime("{Tab}"); // Item 2
    await keyboardWithFakeTime("{Tab}"); // Item 3, skip item 4 (disabled)
    await keyboardWithFakeTime("{Tab}"); // Item 5, SubTrigger
    expect(component.getByText("Item 5, which is a submenu").closest("button")).toHaveFocus();
  };

  test("tab on sub-trigger should focus next item, not sub-menu", async () => {
    const trigger = expectTrigger();
    await clickWithFakeTime(trigger!);
    expectContent();

    await tabFromTriggerToSubTrigger();
    await keyboardWithFakeTime("{Tab}"); // Item 6, SubTrigger
    expect(component.getByText("Item 6, which is also a submenu").closest("button")).toHaveFocus();
  });

  test("enter on sub-trigger should open sub-menu and focus on first item", async () => {
    const trigger = expectTrigger();
    await clickWithFakeTime(trigger!);
    expectContent();

    await tabFromTriggerToSubTrigger();
    await keyboardWithFakeTime("{Enter}");
    expectSubContent();
    expect(component.getByText("Submenu 1 Item 1").closest("button")).toHaveFocus();
  });

  test("tab on sub-menu should focus on next item", async () => {
    const trigger = expectTrigger();
    await clickWithFakeTime(trigger!);
    expectContent();

    await tabFromTriggerToSubTrigger();
    await keyboardWithFakeTime("{Enter}");
    expectSubContent();
    expect(component.getByText("Submenu 1 Item 1").closest("button")).toHaveFocus();
    await keyboardWithFakeTime("{Tab}");
    expect(component.getByText("Submenu 1 Item 2").closest("button")).toHaveFocus();
  });

  test("open sub-menu tabbing should continue back to main menu", async () => {
    const trigger = expectTrigger();
    await clickWithFakeTime(trigger!);
    expectContent();

    await tabFromTriggerToSubTrigger();
    await keyboardWithFakeTime("{Enter}");
    expectSubContent();
    // Expect first sub-menu item to be focused
    expect(component.getByText("Submenu 1 Item 1").closest("button")).toHaveFocus();
    // Tab through all sub-menu items
    for (let i = 0; i < 4; i++) {
      await keyboardWithFakeTime("{Tab}");
    }
    await keyboardWithFakeTime("{Tab}");
    expect(component.getByText("Item 6, which is also a submenu").closest("button")).toHaveFocus();
  });

  test("using up arrow focuses on previous item when in menu", async () => {
    const trigger = expectTrigger();
    await clickWithFakeTime(trigger!);
    expectContent();

    await keyboardWithFakeTime("{Tab}"); // Item 1
    await keyboardWithFakeTime("{ArrowUp}"); // Item 6
    expect(component.getByText("Item 6, which is also a submenu").closest("button")).toHaveFocus();
    await keyboardWithFakeTime("{ArrowUp}"); // Item 5
    expect(component.getByText("Item 5, which is a submenu").closest("button")).toHaveFocus();
    await keyboardWithFakeTime("{ArrowUp}"); // Item 3, skip Item 4 (disabled)
    expect(component.getByText("Item 3, which is even longer").closest("button")).toHaveFocus();
    await keyboardWithFakeTime("{ArrowUp}"); // Item 2
    expect(component.getByText("Item 2, which is longer").closest("button")).toHaveFocus();
    await keyboardWithFakeTime("{ArrowUp}"); // Item 1
    expect(component.getByText("Item 1").closest("button")).toHaveFocus();
  });

  test("using down arrow focuses on next item when in menu", async () => {
    const trigger = expectTrigger();
    await clickWithFakeTime(trigger!);
    expectContent();

    await keyboardWithFakeTime("{Tab}"); // Item 1
    await keyboardWithFakeTime("{ArrowDown}"); // Item 2
    expect(component.getByText("Item 2, which is longer").closest("button")).toHaveFocus();
    await keyboardWithFakeTime("{ArrowDown}"); // Item 3, skip Item 4 (disabled)
    expect(component.getByText("Item 3, which is even longer").closest("button")).toHaveFocus();
    await keyboardWithFakeTime("{ArrowDown}"); // Item 5
    expect(component.getByText("Item 5, which is a submenu").closest("button")).toHaveFocus();
    await keyboardWithFakeTime("{ArrowDown}"); // Item 6
    expect(component.getByText("Item 6, which is also a submenu").closest("button")).toHaveFocus();
  });

  test("when opening sub-menu, arrow keys should navigate through the sub-menu", async () => {
    const trigger = expectTrigger();
    await clickWithFakeTime(trigger!);
    expectContent();

    await tabFromTriggerToSubTrigger();
    await keyboardWithFakeTime("{Enter}");
    expectSubContent();
    expect(component.getByText("Submenu 1 Item 1").closest("button")).toHaveFocus();
    await keyboardWithFakeTime("{ArrowDown}"); // Submenu 1 Item 2
    expect(component.getByText("Submenu 1 Item 2").closest("button")).toHaveFocus();
    await keyboardWithFakeTime("{ArrowDown}"); // Submenu 1 Item 3
    expect(component.getByText("Submenu 1 Item 3").closest("button")).toHaveFocus();
    await keyboardWithFakeTime("{ArrowDown}"); // Submenu 1 Item 4
    expect(component.getByText("Submenu 1 Item 4").closest("button")).toHaveFocus();
    await keyboardWithFakeTime("{ArrowDown}"); // Submenu 1 Item 5
    expect(component.getByText("Submenu 1 Item 5").closest("button")).toHaveFocus();
    await keyboardWithFakeTime("{ArrowDown}"); // Rotates back to Submenu 1 Item 1
    expect(component.getByText("Submenu 1 Item 1").closest("button")).toHaveFocus();
  });

  test("using up arrow rotates through sub-menu", async () => {
    const trigger = expectTrigger();
    await clickWithFakeTime(trigger!);
    expectContent();

    await tabFromTriggerToSubTrigger();
    await keyboardWithFakeTime("{Enter}");
    expectSubContent();
    expect(component.getByText("Submenu 1 Item 1").closest("button")).toHaveFocus();
    await keyboardWithFakeTime("{ArrowUp}"); // Rotates back to Submenu 1 Item 5
    expect(component.getByText("Submenu 1 Item 5").closest("button")).toHaveFocus();
    await keyboardWithFakeTime("{ArrowUp}"); // Submenu 1 Item 4
    expect(component.getByText("Submenu 1 Item 4").closest("button")).toHaveFocus();
    await keyboardWithFakeTime("{ArrowUp}"); // Submenu 1 Item 3
    expect(component.getByText("Submenu 1 Item 3").closest("button")).toHaveFocus();
    await keyboardWithFakeTime("{ArrowUp}"); // Submenu 1 Item 2
    expect(component.getByText("Submenu 1 Item 2").closest("button")).toHaveFocus();
    await keyboardWithFakeTime("{ArrowUp}"); // Submenu 1 Item 1
    expect(component.getByText("Submenu 1 Item 1").closest("button")).toHaveFocus();
  });

  test("using right arrow opens sub-menu when on sub-trigger", async () => {
    const trigger = expectTrigger();
    await clickWithFakeTime(trigger!);
    expectContent();

    await tabFromTriggerToSubTrigger();
    await keyboardWithFakeTime("{ArrowRight}");
    expectSubContent();
    expect(component.getByText("Submenu 1 Item 1").closest("button")).toHaveFocus();
  });

  test("using left arrow closes sub-menu when on sub-menu item", async () => {
    const trigger = expectTrigger();
    await clickWithFakeTime(trigger!);
    expectContent();

    await tabFromTriggerToSubTrigger();
    await keyboardWithFakeTime("{ArrowRight}");
    expectSubContent();
    expect(component.getByText("Submenu 1 Item 1").closest("button")).toHaveFocus();
    await keyboardWithFakeTime("{ArrowDown}"); // Submenu 1 Item 2
    expect(component.getByText("Submenu 1 Item 2").closest("button")).toHaveFocus();

    await keyboardWithFakeTime("{ArrowLeft}");
    expect(component.getByText("Item 5, which is a submenu").closest("button")).toHaveFocus();
  });

  test("shift+tab on first item should focus trigger", async () => {
    const trigger = expectTrigger();
    await clickWithFakeTime(trigger!);
    expectContent();

    // Focus first item
    await keyboardWithFakeTime("{Tab}");
    expect(component.getByText("Item 1").closest("button")).toHaveFocus();

    // Focus up out of the dropdown menu
    await keyboardWithFakeTime("{Shift>}{Tab}{/Shift}");
    expect(trigger?.closest("button")).toHaveFocus();
  });

  test("tab on last item should focus trigger", async () => {
    const trigger = expectTrigger();
    await clickWithFakeTime(trigger!);
    expectContent();

    // Focus last item
    await keyboardWithFakeTime("{Tab}");
    await keyboardWithFakeTime("{Tab}");
    await keyboardWithFakeTime("{Tab}");
    await keyboardWithFakeTime("{Tab}");
    await keyboardWithFakeTime("{Tab}");
    expect(component.getByText("Item 6, which is also a submenu").closest("button")).toHaveFocus();

    // Focus up out of the dropdown menu
    await keyboardWithFakeTime("{Tab}");
    expect(trigger?.closest("button")).toHaveFocus();
  });

  test("tab should focus first item when no item is focused", async () => {
    const trigger = expectTrigger();
    await clickWithFakeTime(trigger!);
    expectContent();

    // Clear focus
    if (document.activeElement instanceof HTMLElement) {
      document.activeElement.blur();
    }
    expect(document.activeElement).toBe(document.body);

    await keyboardWithFakeTime("{Tab}");
    expect(component.getByText("Item 1").closest("button")).toHaveFocus();
  });
});

describe("event handling", () => {
  beforeEach(async () => {
    await tick();
    vi.useFakeTimers();
  });

  afterEach(() => {
    vi.useRealTimers();
    vi.clearAllMocks();
    component?.unmount();
  });

  test("Arrow navigation events should not make it to the document", async () => {
    const trigger = expectTrigger();
    await clickWithFakeTime(trigger!);
    expectContent();

    const keydownListener = vi.fn();
    const keypressListener = vi.fn();
    component.container.addEventListener("keydown", keydownListener);
    component.container.addEventListener("keypress", keypressListener);

    await keyboardWithFakeTime("{ArrowUp}");
    expect(component.getByText("Item 6, which is also a submenu").closest("button")).toHaveFocus();
    expect(keydownListener).not.toHaveBeenCalled();

    await keyboardWithFakeTime("{ArrowDown}");
    expect(component.getByText("Item 1").closest("button")).toHaveFocus();
    expect(keydownListener).not.toHaveBeenCalled();

    await keyboardWithFakeTime("{ArrowDown}");
    expect(component.getByText("Item 2, which is longer").closest("button")).toHaveFocus();
    expect(keydownListener).not.toHaveBeenCalled();

    await keyboardWithFakeTime("{ArrowDown}");
    expect(component.getByText("Item 3, which is even longer").closest("button")).toHaveFocus();
    expect(keydownListener).not.toHaveBeenCalled();

    await keyboardWithFakeTime("{ArrowDown}");
    expect(component.getByText("Item 5, which is a submenu").closest("button")).toHaveFocus();
    expect(keydownListener).not.toHaveBeenCalled();

    await keyboardWithFakeTime("{Enter}");
    expect(component.getByText("Submenu 1 Item 1").closest("button")).toHaveFocus();
    expect(keydownListener).not.toHaveBeenCalled();

    component.container.removeEventListener("keydown", keydownListener);
    component.container.removeEventListener("keypress", keypressListener);
  });
});

describe("request open/close api", () => {
  beforeEach(async () => {
    vi.useFakeTimers();
    await tick();
  });

  afterEach(() => {
    vi.useRealTimers();
    vi.clearAllMocks();
    component?.unmount();
  });

  test("should open and close using the API", async () => {
    expect.hasAssertions();

    // Create wrapper functions that include tick() automatically
    const requestOpen = async () => {
      component.component.getRequestOpen()();
      await tick();
    };
    const requestClose = async () => {
      component.component.getRequestClose()();
      await tick();
    };

    expectNoContent();

    // Open with callback
    await requestOpen();
    expectContent();

    // Close with callback
    await requestClose();
    expectNoContent();

    // Opening with callback after menu is open should do nothing
    const trigger = expectTrigger();
    await clickWithFakeTime(trigger!);
    expectContent();
    await requestOpen();
    expectContent();

    // Closing with callback after menu is closed should do nothing
    await clickWithFakeTime(trigger!);
    expectNoContent();
    await requestClose();
    expectNoContent();
  });

  test("open prop should take priority", async () => {
    expect.hasAssertions();

    // Create wrapper functions that include tick() automatically
    const requestOpen = async () => {
      component.component.getRequestOpen()();
      await tick();
    };
    const requestClose = async () => {
      component.component.getRequestClose()();
      await tick();
    };

    // Force open
    await component.rerender({ open: true });
    await tick(); // Wait for Svelte reactivity to complete

    expectContent();

    // Open with callback
    await requestOpen();
    expectContent();

    // Close with callback
    await requestClose();
    expectContent();

    // Force close
    await component.rerender({ open: false });
    await tick(); // Wait for Svelte reactivity to complete

    expectNoContent();

    // Open with callback
    await requestOpen();
    expectNoContent();

    // Close with callback
    await requestClose();
    expectNoContent();

    // Allow API
    await component.rerender({ open: undefined });
    await tick(); // Wait for Svelte reactivity to complete

    expectNoContent();

    // Open with callback
    await requestOpen();
    expectContent();

    // Close with callback
    await requestClose();
    expectNoContent();
  });
});

async function clickWithFakeTime(element: Element): Promise<void> {
  const clickPromise = userEvent.click(element);
  await vi.advanceTimersByTimeAsync(INTERACTION_DURATION_MS);
  await clickPromise;
}

async function hoverWithFakeTime(element: Element): Promise<void> {
  const hoverPromise = userEvent.hover(element);
  await vi.advanceTimersByTimeAsync(INTERACTION_DURATION_MS);
  await hoverPromise;
}

async function unhoverWithFakeTime(element: Element): Promise<void> {
  const unhoverPromise = userEvent.unhover(element);
  await vi.advanceTimersByTimeAsync(INTERACTION_DURATION_MS);
  await unhoverPromise;
}

async function keyboardWithFakeTime(key: string): Promise<void> {
  const keyDownPromise = userEvent.keyboard(key);
  await vi.advanceTimersByTimeAsync(INTERACTION_DURATION_MS);
  await keyDownPromise;
}
