<script lang="ts">
  import { getContext, tick } from "svelte";
  import { CONTENT_CONTEXT_KEY, type ContentContext } from "./Content.svelte";
  import Content from "./Content.svelte";
  import { TooltipContext } from "../../_primitives/TooltipAugment/context";
  import DropdownMenuFocusContext from "./focus-context";
  import { derived } from "svelte/store";

  const contentContext = getContext<ContentContext>(CONTENT_CONTEXT_KEY);
  const sizeState = contentContext.size;

  // When the tooltip is opened, we should focus the first item in the sub-menu
  const focusContext = getContext<DropdownMenuFocusContext>(DropdownMenuFocusContext.CONTEXT_KEY);
  const tooltipContext = getContext<TooltipContext>(TooltipContext.CONTEXT_KEY);

  // Create a derived store that tracks when the tooltip opens
  const didOpen = derived(tooltipContext.state, ($state) => $state.open);
  $: $didOpen && tick().then(() => focusContext?.focusIdx(0));
  $: !$didOpen && focusContext?.popNestedFocus();
</script>

<Content {...$$restProps} side="right" align="start" size={$sizeState}>
  <slot />
</Content>

<style>
  /* Selects a content card that is a child of another content card */
  /* These styles align the selected sub-menu trigger with the first item in the sub-menu */
  :global(
      .c-card
        > .l-dropdown-menu-augment__contents--size-1
        .c-card:has(> .l-dropdown-menu-augment__contents--size-1)
    ) {
    transform: translateX(calc(-1 * var(--ds-spacing-1))) translateY(calc(-1 * var(--ds-spacing-1)));
  }

  :global(
      .l-dropdown-menu-augment__contents--size-2
        .c-card:has(> .l-dropdown-menu-augment__contents--size-2)
    ) {
    transform: translateX(calc(-1 * var(--ds-spacing-1))) translateY(calc(-1 * var(--ds-spacing-2)));
  }
</style>
