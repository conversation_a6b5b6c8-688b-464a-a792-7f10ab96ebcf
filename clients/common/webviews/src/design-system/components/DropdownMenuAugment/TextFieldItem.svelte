<script lang="ts">
  import { getContext } from "svelte";
  import { CONTENT_CONTEXT_KEY, type ContentContext } from "./Content.svelte";
  import TextFieldAugment from "../TextFieldAugment.svelte";
  import DropdownMenuFocusContext from "./focus-context";

  export let value: string = "";

  const contentContext = getContext<ContentContext>(CONTENT_CONTEXT_KEY);
  const sizeState = contentContext.size;

  $: className = [
    "c-dropdown-menu-augment__text-field-item",
    `c-dropdown-menu-augment__text-field-item--size-${$sizeState}`,
  ].join(" ");
</script>

<div class={className}>
  <TextFieldAugment
    class={DropdownMenuFocusContext.ITEM_CLASS}
    size={$sizeState}
    bind:value
    {...$$restProps}
  />
</div>

<style>
  .c-dropdown-menu-augment__text-field-item {
    width: 100%;
    padding: var(--dropdown-menu__text-field-item-padding-vertical)
      var(--dropdown-menu__text-field-item-padding-horizontal);

    &.c-dropdown-menu-augment__text-field-item--size-1 {
      --dropdown-menu__text-field-item-padding-vertical: var(--ds-spacing-1);
      --dropdown-menu__text-field-item-padding-horizontal: var(--ds-spacing-2);
    }

    &.c-dropdown-menu-augment__text-field-item--size-2 {
      --dropdown-menu__text-field-item-padding-vertical: var(--ds-spacing-1);
      --dropdown-menu__text-field-item-padding-horizontal: var(--ds-spacing-2);
    }
  }
</style>
