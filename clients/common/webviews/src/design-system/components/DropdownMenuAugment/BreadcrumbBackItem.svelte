<script lang="ts">
  import ChevronLeft from "../../icons/chevron-left.svelte";

  import Item from "./Item.svelte";
</script>

<Item class="c-dropdown-menu-augment__breadcrumb-back-chevron" {...$$restProps}>
  <ChevronLeft slot="iconLeft" />
  <slot />
</Item>

<style>
  /* Remove a bit of spacing around the chevron for the breadcrumb list items */
  :global(.c-dropdown-menu-augment__item.c-dropdown-menu-augment__breadcrumb-back-chevron svg) {
    margin: 0 0 0 calc(-1 * var(--ds-spacing-1));
  }
</style>
