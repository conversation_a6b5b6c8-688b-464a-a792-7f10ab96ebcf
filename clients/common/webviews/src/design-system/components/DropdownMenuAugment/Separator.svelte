<script lang="ts">
  import { getContext } from "svelte";
  import { CONTENT_CONTEXT_KEY, type ContentContext } from "./Content.svelte";
  import SeparatorAugment from "../SeparatorAugment.svelte";

  const contentContext = getContext<ContentContext>(CONTENT_CONTEXT_KEY);
  const sizeState = contentContext.size;
</script>

<div
  class={`c-dropdown-menu-augment__separator c-dropdown-menu-augment__separator--size-${$sizeState}`}
>
  <SeparatorAugment size={4} orientation="horizontal" />
</div>

<style>
  .c-dropdown-menu-augment__separator {
    margin: 0 var(--ds-spacing-3);
  }

  .c-dropdown-menu-augment__separator--size-1 {
    margin: var(--ds-spacing-1) var(--ds-spacing-2);
  }

  .c-dropdown-menu-augment__separator--size-2 {
    margin: var(--ds-spacing-2) var(--ds-spacing-3);
  }
</style>
