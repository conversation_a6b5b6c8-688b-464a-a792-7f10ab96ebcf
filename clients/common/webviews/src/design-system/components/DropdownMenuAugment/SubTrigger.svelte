<script lang="ts">
  import { getContext } from "svelte";

  import Trigger from "$common-webviews/src/design-system/_primitives/TooltipAugment/Trigger.svelte";
  import BreadcrumbItem from "./BreadcrumbItem.svelte";
  import { TooltipContext } from "../../_primitives/TooltipAugment/context";

  const context = getContext<TooltipContext>(TooltipContext.CONTEXT_KEY);
  const stateStore = context.state;
</script>

<Trigger>
  <BreadcrumbItem highlight={$stateStore.open}>
    <slot />
  </BreadcrumbItem>
</Trigger>
