<script lang="ts">
  import { getContext } from "svelte";
  import { CONTENT_CONTEXT_KEY, type ContentContext } from "./Content.svelte";
  import TextAugment from "../TextAugment.svelte";

  const contentContext = getContext<ContentContext>(CONTENT_CONTEXT_KEY);
  const sizeState = contentContext.size;

  $: className = [
    "c-dropdown-menu-augment__label-item",
    `c-dropdown-menu-augment__label-item--size-${$sizeState}`,
  ].join(" ");
</script>

<div class={className}>
  <TextAugment size={$sizeState} weight="regular">
    <slot />
  </TextAugment>
</div>

<style>
  .c-dropdown-menu-augment__label-item {
    width: 100%;
    padding: 0 var(--ds-spacing-2);

    display: flex;
    align-items: center;
    color: var(--gray-a10);
    user-select: none;
    cursor: default;
  }
</style>
