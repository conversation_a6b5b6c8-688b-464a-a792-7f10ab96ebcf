<script lang="ts" context="module">
  export type ItemColor = "accent" | "error" | "neutral" | "success";
</script>

<script lang="ts">
  import { getContext } from "svelte";
  import { CONTENT_CONTEXT_KEY, type ContentContext } from "./Content.svelte";
  import BaseButton from "../../_primitives/BaseButton.svelte";
  import TextAugment from "../TextAugment.svelte";
  import { dsPublicBooleanAttribute } from "$common-webviews/src/design-system/_libs/component-utils";
  import DropdownMenuFocusContext from "./focus-context";

  export let highlight: boolean | undefined = undefined;
  export let disabled: boolean | undefined = undefined;
  export let color: ItemColor | undefined = undefined;
  export let onSelect: <T extends Event>(e: T) => void = () => {};

  const contentContext = getContext<ContentContext>(CONTENT_CONTEXT_KEY);
  const focusContext = getContext<DropdownMenuFocusContext>(DropdownMenuFocusContext.CONTEXT_KEY);
  const sizeState = contentContext.size;

  // Function to update the focused index when this item is hovered or selected
  function updateFocusedIndex(element: HTMLElement) {
    if (disabled) return;

    // Find this item's index in the list of focusable items
    const items = focusContext.rootElement?.querySelectorAll(
      `.${DropdownMenuFocusContext.ITEM_CLASS}`,
    );
    if (!items) return;

    const itemsArray = Array.from(items) as HTMLElement[];
    const thisIndex = itemsArray.findIndex((item) => item === element);

    if (thisIndex !== -1) {
      focusContext.setFocusedIdx(thisIndex);
    }
  }

  $: ({ class: restClassName, ...restProps } = $$restProps);
  $: className = [
    disabled ? "" : DropdownMenuFocusContext.ITEM_CLASS,
    "c-dropdown-menu-augment__item",
    highlight ? "c-dropdown-menu-augment__item--highlighted" : "",
    restClassName,
  ].join(" ");
</script>

<BaseButton
  class={className}
  size={$sizeState}
  variant="ghost"
  color={color ?? "neutral"}
  highContrast={!color}
  alignment="left"
  {disabled}
  on:click={(e) => {
    if (e.currentTarget instanceof HTMLElement) {
      updateFocusedIndex(e.currentTarget);
    }
    onSelect(e);
  }}
  on:mouseover={(e) => {
    if (e.currentTarget instanceof HTMLElement) {
      updateFocusedIndex(e.currentTarget);
    }
  }}
  on:mousedown={(e) => {
    e.preventDefault();
    e.stopPropagation();
  }}
  {...dsPublicBooleanAttribute("dropdown-menu-item", "highlighted", highlight)}
  {...dsPublicBooleanAttribute("dropdown-menu-item", "disabled", disabled)}
  {...restProps}
>
  {#if $$slots.iconLeft}
    <div class="c-dropdown-menu-augment__item-icon">
      <slot name="iconLeft" />
    </div>
  {/if}
  <TextAugment size={$sizeState}>
    <slot />
  </TextAugment>
  {#if $$slots.iconRight}
    <div class="c-dropdown-menu-augment__item-icon">
      <slot name="iconRight" />
    </div>
  {/if}
</BaseButton>

<style>
  :global(button.c-base-btn.c-dropdown-menu-augment__item) {
    /* Distribute items */
    border-radius: var(--ds-radius-2);
    justify-content: space-between;
    gap: var(--base-btn-gap);
    padding: var(--ds-spacing-1) var(--ds-spacing-2);

    &.c-dropdown-menu-augment__item--highlighted,
    &.c-base-btn--highContrast.c-dropdown-menu-augment__item--highlighted {
      background-color: var(--gray-a4);
    }

    &.c-base-btn.c-base-btn--size-1 {
      --base-btn-classic-padding-top: 1px;
      --base-btn-gap: var(--ds-spacing-1);
    }

    &.c-base-btn.c-base-btn--size-2 {
      --base-btn-classic-padding-top: 1px;
      --base-btn-gap: var(--ds-spacing-1);
    }

    & > .c-text {
      width: 100%;
      overflow: hidden;
      display: flex;
      flex-grow: 1;
    }

    &:hover svg {
      opacity: 1;
    }
  }

  .c-dropdown-menu-augment__item-icon :global(svg) {
    display: block;
    width: var(--button-icon-size);
    height: var(--button-icon-size);
  }

  .c-dropdown-menu-augment__item-icon {
    --button-icon-size: 16px;
    height: var(--button-icon-size);

    /* Hide empty icon containers */
    &:not(:has(> *)) {
      display: none;
    }
  }
</style>
