<script lang="ts" context="module">
  export type SpinnerSize = 1 | 2 | 3;
</script>

<script lang="ts">
  export let size: SpinnerSize = 2;
  export let loading: boolean = true;
  export let useCurrentColor: boolean = false;
  let className: string = "";
  export { className as class };
</script>

{#if loading}
  <div
    class="c-spinner c-spinner--size-{size} {className}"
    class:c-spinner--current-color={useCurrentColor}
    data-testid="spinner-augment"
  >
    <div class="c-spinner__leaf"></div>
    <div class="c-spinner__leaf"></div>
    <div class="c-spinner__leaf"></div>
    <div class="c-spinner__leaf"></div>
    <div class="c-spinner__leaf"></div>
    <div class="c-spinner__leaf"></div>
    <div class="c-spinner__leaf"></div>
    <div class="c-spinner__leaf"></div>
  </div>
{/if}

<style>
  /*

  This component creates 8 "leaf" nodes, which are all of the UI elements
  that make up the spinner. These elements are rotated in a circle. The
  animation on each leaf is an opacity change, but the animation is delayed
  for each leaf, creating a spinning effect.

  */

  .c-spinner {
    --spinner-size: var(--ds-spacing-3);
    --spinner-animation-duration: 800ms;
    --spinner-opacity: 0.65;
    --spinner-color: var(--slate-11);

    display: inline-block;
    position: relative;
    opacity: var(--spinner-opacity);

    width: var(--spinner-size);
    height: var(--spinner-size);
  }

  .c-spinner--current-color {
    --spinner-color: currentColor;
  }

  /* Size */
  .c-spinner--size-2 {
    --spinner-size: var(--ds-spacing-4);
  }

  .c-spinner--size-3 {
    --spinner-size: calc(var(--ds-spacing-4) * 1.25);
  }

  .c-spinner__leaf {
    --leaf-width: 12.5%;

    position: absolute;
    top: 0;
    left: calc(50% - (var(--leaf-width) / 2));
    width: var(--leaf-width);
    height: 100%;
    animation: spinner-leaf-fade var(--spinner-animation-duration) linear infinite;

    &::before {
      content: "";
      display: block;
      width: 100%;
      height: 30%;
      border-radius: var(--ds-radius-1);
      background-color: var(--spinner-color);
    }

    &:nth-child(1) {
      transform: rotate(0deg);
      animation-delay: calc(-8 / 8 * var(--spinner-animation-duration));
    }
    &:nth-child(2) {
      transform: rotate(45deg);
      animation-delay: calc(-7 / 8 * var(--spinner-animation-duration));
    }
    &:nth-child(3) {
      transform: rotate(90deg);
      animation-delay: calc(-6 / 8 * var(--spinner-animation-duration));
    }
    &:nth-child(4) {
      transform: rotate(135deg);
      animation-delay: calc(-5 / 8 * var(--spinner-animation-duration));
    }
    &:nth-child(5) {
      transform: rotate(180deg);
      animation-delay: calc(-4 / 8 * var(--spinner-animation-duration));
    }
    &:nth-child(6) {
      transform: rotate(225deg);
      animation-delay: calc(-3 / 8 * var(--spinner-animation-duration));
    }
    &:nth-child(7) {
      transform: rotate(270deg);
      animation-delay: calc(-2 / 8 * var(--spinner-animation-duration));
    }
    &:nth-child(8) {
      transform: rotate(315deg);
      animation-delay: calc(-1 / 8 * var(--spinner-animation-duration));
    }
  }

  @keyframes spinner-leaf-fade {
    from {
      opacity: 1;
    }
    to {
      opacity: 0.25;
    }
  }
</style>
