import { render } from "@testing-library/svelte";
import GradientMask from "./GradientMask.svelte";
import { describe, expect, it } from "vitest";

describe("GradientMask", () => {
  it("renders with default props", () => {
    const { container } = render(GradientMask, {});

    const gradientMask = container.querySelector(".c-gradient-mask");
    expect(gradientMask).not.toBeNull();
    expect(gradientMask?.classList.contains("is-horizontal")).toBe(false);
  });

  it("applies horizontal class when direction is horizontal", () => {
    const { container } = render(GradientMask, {
      props: {
        direction: "horizontal",
      },
    });

    const gradientMask = container.querySelector(".c-gradient-mask");
    expect(gradientMask?.classList.contains("is-horizontal")).toBe(true);
  });

  it("applies custom class", () => {
    const { container } = render(GradientMask, {
      props: {
        class: "custom-class",
      },
    });

    const gradientMask = container.querySelector(".c-gradient-mask");
    expect(gradientMask?.classList.contains("custom-class")).toBe(true);
  });

  it("sets fade size as a CSS variable", () => {
    const { container } = render(GradientMask, {
      props: {
        fadeSize: 50,
      },
    });

    const gradientMask = container.querySelector(".c-gradient-mask");
    expect(gradientMask?.getAttribute("style")).toContain("--fade-size: 50px");
  });
});
