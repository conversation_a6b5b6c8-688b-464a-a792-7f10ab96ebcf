<script lang="ts">
  import RegularBellRingIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/bell.svg?component";
  import RegularBellSlashIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/bell-slash.svg?component";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import { createEventDispatcher } from "svelte";
  import { type ButtonColor } from "../_primitives/BaseButton.svelte";
  import IconButtonAugment from "./IconButtonAugment.svelte";
  import { TooltipTriggerOn } from "../_primitives/TooltipAugment/types";

  /** Whether notifications are enabled */
  export let enabled: boolean = false;
  /** Whether there are unread updates */
  export let isUnread: boolean = false;
  /** Number of unread notifications to display next to bell, default to hardcoded 1 */
  export let unreadCount = 1;
  /** Status color for the button when unread */
  export let statusColor: ButtonColor = "neutral";
  /** Tooltip text to display */
  export let tooltipText: string = "";

  const dispatch = createEventDispatcher<{
    toggle: void;
  }>();

  function handleToggle(e: MouseEvent) {
    e.stopPropagation();
    dispatch("toggle");
  }
</script>

<div class="c-notify-button {enabled ? 'c-notify-button--enabled' : ''}">
  <TextTooltipAugment
    content={tooltipText}
    hasPointerEvents={false}
    triggerOn={[TooltipTriggerOn.Hover]}
  >
    <IconButtonAugment
      color={isUnread ? statusColor : "neutral"}
      size={1}
      variant={isUnread ? "soft" : "ghost-block"}
      on:click={handleToggle}
      on:keydown={(e) => {
        if (e.key === "Enter" || e.key === " ") {
          e.stopPropagation();
        }
      }}
      radius="full"
    >
      {#if enabled}
        <RegularBellRingIcon />
      {:else}
        <RegularBellSlashIcon />
      {/if}
      {#if isUnread && unreadCount}
        {unreadCount}
      {/if}
    </IconButtonAugment>
  </TextTooltipAugment>
</div>

<style>
  .c-notify-button :global(svg) {
    --notify-button-icon-size: 16px;
    width: var(--notify-button-icon-size);
    height: var(--notify-button-icon-size);
    color: var(--ds-color-neutral-9);
  }
  .c-notify-button--enabled :global(svg) {
    --notify-button-icon-size: 14px;
    margin: 0 1.5px;
    color: var(--ds-color-neutral-10);
  }
</style>
