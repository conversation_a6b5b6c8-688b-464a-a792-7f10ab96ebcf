<script lang="ts" context="module">
  export type {
    TextFieldColor,
    TextFieldSize,
    TextFieldVariant,
  } from "../_primitives/BaseTextInput.svelte";
</script>

<script lang="ts">
  import "@radix-ui/colors/gray.css";
  import "@radix-ui/colors/gray-alpha.css";
  import "@radix-ui/colors/black-alpha.css";

  import {
    type TextFieldColor,
    type TextFieldSize,
    type TextFieldVariant,
  } from "../_primitives/BaseTextInput.svelte";
  import TextAreaAugment from "./TextAreaAugment.svelte";
  import { onDestroy } from "svelte";
  import { debounce } from "lodash";
  import TextAugment from "./TextAugment.svelte";

  export let variant: TextFieldVariant = "surface";
  export let size: TextFieldSize = 2;
  export let color: TextFieldColor | undefined = undefined;
  export let resize: "none" | "both" | "horizontal" | "vertical" = "none";

  export let textInput: HTMLTextAreaElement | undefined = undefined;
  export let value: string = "";

  export let selectedText = "";
  export let selectionStart = 0;
  export let selectionEnd = 0;
  export let saveFunction: () => void;
  export let debounceValue = 2500;
  let successTimeout: ReturnType<typeof setTimeout> | undefined = undefined;

  let success = false;
  let error: string | undefined;

  const onSave = async () => {
    try {
      saveFunction();
      success = true;
      clearTimeout(successTimeout);
      successTimeout = setTimeout(() => {
        success = false;
      }, 1500);
    } catch (err) {
      error = err instanceof Error ? err.message : String(err);
    }
  };

  function handleSelectionChange() {
    if (textInput) {
      selectionStart = textInput.selectionStart;
      selectionEnd = textInput.selectionEnd;

      if (selectionStart !== selectionEnd) {
        selectedText = value.substring(selectionStart, selectionEnd);
      } else {
        selectedText = "";
      }
    }
  }
  const onSaveDebounced = debounce(onSave, debounceValue);

  onDestroy(() => {
    onSave();
  });
</script>

<div class="l-markdown-editor">
  <div class="c-markdown-editor__header">
    <slot name="header" />
  </div>
  <slot />
  <div class="c-markdown-editor__content">
    <slot name="title" />
    <TextAreaAugment
      {variant}
      {size}
      {color}
      {resize}
      bind:textInput
      bind:value
      placeholder="Enter markdown content..."
      on:select={handleSelectionChange}
      on:mouseup={handleSelectionChange}
      on:keyup={() => {
        handleSelectionChange();
      }}
      on:input={onSaveDebounced}
      on:keydown={(e) => {
        if (e.key === "Escape") {
          e.preventDefault();
          onSave();
        } else if ((e.metaKey || e.ctrlKey) && e.key === "s") {
          e.preventDefault();
          onSave();
        }
      }}
      {...$$restProps}
    />
  </div>
</div>
<div class="c-markdown-editor__status">
  {#if !!error}
    <TextAugment size={1} weight="light" color="error">{error}</TextAugment>
  {/if}
  {#if success}
    <TextAugment size={1} weight="light" color="success">Saved</TextAugment>
  {/if}
</div>

<style>
  .l-markdown-editor {
    display: flex;
    flex-direction: column;
    padding: var(--ds-spacing-1) var(--ds-spacing-2);
    background-color: var(--ds-color-neutral-a4);
    border-radius: var(--ds-radius-2);
    overflow: hidden; /* Hide overflow to prevent the entire component from scrolling */
  }

  .c-markdown-editor__header {
    position: sticky;
    top: 0;
  }

  .c-markdown-editor__content {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-1);
  }

  .c-markdown-editor__status {
    align-self: flex-end;
    display: flex;
  }
</style>
