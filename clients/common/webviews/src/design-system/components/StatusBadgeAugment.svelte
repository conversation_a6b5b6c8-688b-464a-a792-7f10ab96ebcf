<script lang="ts">
  import type { TextWeight, TextSize } from "./TextAugment.svelte";

  /** Color variant of the status badge */
  export let color: "neutral" | "info" | "warning" | "error" | "success" = "neutral";

  /** Size of the status badge (1-4) */
  export let size: Extract<TextSize, 1 | 2 | 3 | 4> = 1;

  /** Font weight of the status badge text */
  export let weight: TextWeight = "medium";

  $: classes = [
    "c-status-badge",
    `c-status-badge--${color}`,
    `c-status-badge--size-${size}`,
    `c-text--size-${size}`,
    `c-text--weight-${weight}`,
  ].join(" ");
</script>

<div class={classes} role="status" aria-label={color}>
  <slot />
</div>

<style>
  .c-status-badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0 var(--ds-spacing-1);
    border-radius: var(--ds-radius-1);
  }

  /* Size variants - only height needs to be customized */
  .c-status-badge--size-1 {
    height: 16px;
  }

  .c-status-badge--size-2 {
    height: 20px;
  }

  .c-status-badge--size-3 {
    height: 24px;
  }

  .c-status-badge--size-4 {
    height: 28px;
  }

  /* Color variants */
  .c-status-badge--neutral {
    background-color: var(--ds-color-neutral-a3);
    color: var(--ds-color-neutral-11);
  }

  .c-status-badge--info {
    background-color: var(--ds-color-info-a3);
    color: var(--ds-color-info-11);
  }

  .c-status-badge--warning {
    background-color: var(--ds-color-warning-a3);
    color: var(--ds-color-warning-11);
  }

  .c-status-badge--error {
    background-color: var(--ds-color-error-a3);
    color: var(--ds-color-error-11);
  }

  .c-status-badge--success {
    background-color: var(--ds-color-success-a3);
    color: var(--ds-color-success-11);
  }
</style>
