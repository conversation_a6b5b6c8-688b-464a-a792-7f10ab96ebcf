import { render, screen } from "@testing-library/svelte";
import { vi, describe, it, expect, beforeEach, afterEach } from "vitest";

// Mock the visibility observer before importing the component
vi.mock("$common-webviews/src/apps/chat/components/actions/trackOnScreen", () => ({
  visibilityObserverOnce: vi.fn(() => ({ destroy: vi.fn() })),
}));

import AnimatedNumberIndicator from "./AnimatedNumberIndicatorAugment.svelte";

describe("AnimatedNumberIndicator", () => {
  let mockVisibilityObserverOnce: ReturnType<typeof vi.fn>;

  beforeEach(async () => {
    vi.clearAllMocks();

    // Get the mocked function
    const trackOnScreen = await import(
      "$common-webviews/src/apps/chat/components/actions/trackOnScreen"
    );
    mockVisibilityObserverOnce = trackOnScreen.visibilityObserverOnce as ReturnType<typeof vi.fn>;

    // Mock requestAnimationFrame
    vi.spyOn(window, "requestAnimationFrame").mockImplementation((cb) => {
      setTimeout(cb, 16); // Simulate 60fps
      return 1;
    });
    vi.spyOn(performance, "now").mockReturnValue(0);
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it("renders with initial value of 0", () => {
    render(AnimatedNumberIndicator, { props: { value: 42 } });

    // Should start at 0
    expect(screen.getByText("0")).toBeInTheDocument();
  });

  it("does not set up visibility observer on mount", () => {
    render(AnimatedNumberIndicator, { props: { value: 42 } });

    // The component no longer uses a visibility observer; ensure it was not called
    expect(mockVisibilityObserverOnce).not.toHaveBeenCalled();
  });

  it("passes through TextAugment props", () => {
    render(AnimatedNumberIndicator, {
      props: {
        value: 42,
        size: 3,
        color: "accent",
      },
    });

    // The TextAugment component should receive the size and color props
    const textElement = screen.getByText("0");
    expect(textElement).toBeInTheDocument();
  });
});
