<!--
  CheckboxAugment - Design System Checkbox Component

  A styled checkbox component using font icons for consistent appearance.
  Provides a binary state toggle with proper accessibility.
-->
<script lang="ts">
  import { createEventDispatcher } from "svelte";
  import IconButtonAugment from "./IconButtonAugment.svelte";
  import Check from "../icons/fontawesome/svgs/regular/square-check.svg?component";
  import Unchecked from "../icons/fontawesome/svgs/regular/square.svg?component";

  // Props
  export let checked: boolean = false;
  export let disabled: boolean = false;
  export let size: 1 | 2 | 3 = 1;
  export let ariaLabel: string = "";
  export let title: string = "";

  // Event dispatcher
  const dispatch = createEventDispatcher<{
    change: { checked: boolean };
  }>();

  // Handle click
  function handleClick() {
    if (disabled) return;

    checked = !checked;
    dispatch("change", { checked });
  }

  // Handle keyboard events
  function handleKeydown(event: KeyboardEvent) {
    if (disabled) return;

    if (event.key === " " || event.key === "Enter") {
      event.preventDefault();
      handleClick();
    }
  }
</script>

<!-- Checkbox Component -->
<div
  class="c-checkbox-augment__container"
  class:c-checkbox-augment--checked={checked}
  class:c-checkbox-augment--disabled={disabled}
>
  <IconButtonAugment
    variant="ghost"
    color="neutral"
    {size}
    {disabled}
    {title}
    aria-label={ariaLabel}
    aria-checked={checked}
    role="checkbox"
    tabindex={disabled ? -1 : 0}
    on:click={handleClick}
    on:keydown={handleKeydown}
  >
    {#if checked}
      <Check />
    {:else}
      <Unchecked />
    {/if}
  </IconButtonAugment>
</div>

<style>
  .c-checkbox-augment__container {
    display: contents;
  }

  .c-checkbox-augment__container.c-checkbox-augment--checked {
    color: var(--ds-color-accent-9);
  }

  .c-checkbox-augment__container.c-checkbox-augment--disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .c-checkbox-augment__container :global(svg) {
    width: 16px;
    height: 16px;
  }
</style>
