import { getContext, setContext } from "svelte";
import { derived, writable, type Readable } from "svelte/store";
import type { MonacoType } from "./types";

/**
 * Context for managing Monaco editor instance
 * This context provides access to the Monaco editor instance and loading state
 */
export class MonacoContext {
  /* eslint-disable @typescript-eslint/naming-convention */
  public static CONTEXT_KEY = "augment-monaco-provider";
  /* eslint-enable @typescript-eslint/naming-convention */

  // Monaco instance store
  private _monaco = writable<MonacoType | null>(null);

  // Loading state store
  private _isLoading = writable<boolean>(true);

  // Error state store
  private _error = writable<Error | null>(null);

  /**
   * Creates a new Monaco context
   * Automatically sets the context on construction
   */
  constructor() {
    setContext(MonacoContext.CONTEXT_KEY, this);
    void this._initializeMonaco();
  }

  /**
   * Initialize Monaco by loading it from the window.augmentDeps.monaco promise
   */
  private async _initializeMonaco(): Promise<void> {
    try {
      // Set loading state
      this._isLoading.set(true);

      // Wait for Monaco to load
      if (!window.augmentDeps?.monaco) {
        throw new Error(
          "Monaco is not available. Make sure monaco-bootstrap.js is included in your HTML.",
        );
      }

      const monaco = await window.augmentDeps.monaco;

      // Set Monaco instance
      this._monaco.set(monaco);

      // Clear loading state
      this._isLoading.set(false);
    } catch (err) {
      // Set error state
      this._error.set(err instanceof Error ? err : new Error(String(err)));
      this._isLoading.set(false);
      console.error("Failed to load Monaco:", err);
    }
  }

  /**
   * Get the Monaco instance
   * @returns A readable store containing the Monaco instance
   */
  public get monaco(): Readable<MonacoType | null> {
    return this._monaco;
  }

  /**
   * Get the loading state
   * @returns A readable store containing the loading state
   */
  public get isLoading(): Readable<boolean> {
    return this._isLoading;
  }

  /**
   * Get the error state
   * @returns A readable store containing the error state
   */
  public get error(): Readable<Error | null> {
    return this._error;
  }

  /**
   * Get a derived store that indicates if Monaco is ready to use
   * @returns A readable store that is true when Monaco is loaded and ready to use
   */
  public isReady = derived(
    [this._monaco, this._isLoading, this._error],
    ([$monaco, $isLoading, $error]) => $monaco !== null && !$isLoading && $error === null,
  );

  /**
   * Get the Monaco context from the current component
   * @returns The Monaco context
   * @throws Error if the context is not found
   */
  public static getContext(): MonacoContext {
    const context = getContext<MonacoContext>(MonacoContext.CONTEXT_KEY);
    if (!context) {
      throw new Error(
        "Monaco context not found. Make sure you're using MonacoProvider.Root as a parent component.",
      );
    }
    return context;
  }
}
