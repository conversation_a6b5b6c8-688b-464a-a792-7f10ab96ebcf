# MonacoProvider

This component provides a context for Monaco editor instances. It lazy-loads Monaco from the `window.augmentDeps.monaco` Promise and provides a Svelte context that can be used by child components.

## Problem

Monaco editor is a large dependency that should be lazy-loaded to improve initial page load performance. Currently, Monaco is loaded in various ways across the codebase:

1. Direct imports from `monaco-editor` which bundle the entire editor
2. Manual handling of the `window.augmentDeps.monaco` Promise in each component
3. Inconsistent loading patterns leading to potential duplicate loading

## Solution

The `MonacoProvider` component:

1. Provides a consistent way to lazy-load Monaco
2. Creates a Svelte context that can be accessed by child components
3. Defers rendering of children until Monaco is loaded
4. Provides loading and error states

## Usage

### Basic Usage

```svelte
<script>
  import MonacoProvider from "$common-webviews/src/design-system/components/MonacoProvider";
  import SimpleMonaco from "$common-webviews/src/common/components/SimpleMonaco.svelte";
</script>

<MonacoProvider.Root>
  <!-- Monaco is guaranteed to be loaded when this content renders -->
  <SimpleMonaco text="console.log('Hello, world!');" lang="javascript" />
</MonacoProvider.Root>
```

### Using the Built-in Monaco Components

The MonacoProvider package includes its own Monaco and SimpleMonaco components that are designed to work with the provider:

```svelte
<script>
  import MonacoProvider from "$common-webviews/src/design-system/components/MonacoProvider";
</script>

<MonacoProvider.Root>
  <MonacoProvider.SimpleMonaco
    text="console.log('Hello, world!');"
    lang="javascript"
  />
</MonacoProvider.Root>
```

## Accessing Monaco in Child Components

Child components can access the Monaco instance using the `MonacoContext`:

```svelte
<script>
  import { MonacoContext } from "$common-webviews/src/design-system/components/MonacoProvider";
  import { onMount } from "svelte";
  import { get } from "svelte/store";

  // Get the Monaco context
  const monacoContext = MonacoContext.getContext();

  // Get the Monaco instance store
  const monaco = monacoContext.monaco;

  let editorInstance;
  let editorContainer;

  // Method 1: Using the subscribe method
  const unsubscribe = monaco.subscribe((monacoInstance) => {
    if (monacoInstance && editorContainer) {
      editorInstance = monacoInstance.editor.create(editorContainer, {
        value: "// Your code here",
        language: "javascript",
      });
    }
  });

  // Don't forget to unsubscribe when the component is destroyed
  onDestroy(() => {
    unsubscribe();
    if (editorInstance) {
      editorInstance.dispose();
    }
  });

  // Method 2: Using the get function from svelte/store
  function createEditor() {
    const monacoInstance = get(monaco);
    if (monacoInstance && editorContainer) {
      editorInstance = monacoInstance.editor.create(editorContainer, {
        value: "// Your code here",
        language: "javascript",
      });
    }
  }
</script>

<div bind:this={editorContainer}></div>
```

### Checking if Monaco is Ready

The MonacoContext provides an `isReady` store that you can use to check if Monaco is ready to use:

```svelte
<script>
  import { MonacoContext } from "$common-webviews/src/design-system/components/MonacoProvider";

  const monacoContext = MonacoContext.getContext();
  const isReady = monacoContext.isReady;
</script>

{#if $isReady}
  <!-- Monaco is ready to use -->
  <div>Monaco is ready!</div>
{:else}
  <!-- Monaco is still loading -->
  <div>Loading Monaco...</div>
{/if}
```

## Components

### Root

The `Root` component is the main entry point for the MonacoProvider. It creates the Monaco context and defers rendering of children until Monaco is loaded.

#### Props

- `showLoadingIndicator` (boolean, default: `true`): Whether to show a loading indicator while Monaco is loading

### Monaco

The `Monaco` component is a wrapper around the Monaco editor that uses the MonacoProvider context.

#### Props

- `options` (object): Options to pass to the Monaco editor
- `model` (object): Monaco text model to use
- `decorations` (array): Decorations to apply to the editor
- `height` (number): Height of the editor in pixels
- `editorInstance` (object): Reference to the editor instance

### SimpleMonaco

The `SimpleMonaco` component is a higher-level wrapper around the Monaco editor that handles common tasks like language detection and model creation.

#### Props

- `text` (string): Text content to display in the editor
- `lang` (string): Language to use for syntax highlighting
- `pathName` (string): Path name to use for the editor URI
- `options` (object): Options to pass to the Monaco editor
- `editorInstance` (object): Reference to the editor instance
- `height` (number): Height of the editor in pixels
