<script lang="ts">
  import { MonacoContext } from "./context";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";

  /**
   * Whether to show a loading indicator while Monaco is loading
   * @default true
   */
  export let showLoadingIndicator: boolean = true;

  // Create the Monaco context
  const monacoContext = new MonacoContext();

  // Get the stores from the context
  const isLoading = monacoContext.isLoading;
  const error = monacoContext.error;
  const isReady = monacoContext.isReady;
</script>

{#if $isLoading && showLoadingIndicator}
  <div class="c-monaco-provider__loading">
    <TextAugment size={1} color="neutral">Loading Monaco Editor...</TextAugment>
  </div>
{:else if $error}
  <div class="c-monaco-provider__error">
    <TextAugment size={1} color="error">Failed to load Monaco Editor: {$error.message}</TextAugment>
  </div>
{:else if $isReady}
  <slot />
{/if}

<style>
  .c-monaco-provider__loading,
  .c-monaco-provider__error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: var(--ds-spacing-2);
    padding: var(--ds-spacing-4);
    min-height: 100px;
  }
</style>
