<script lang="ts">
  import MonacoProvider from "$common-webviews/src/design-system/components/MonacoProvider";
  import SimpleMonaco from "$common-webviews/src/design-system/components/MonacoProvider/SimpleMonaco.svelte";

  export let code: string = "// Example code\nconsole.log('Hello, world!');";
  export let language: string = "javascript";
</script>

<div class="example-container">
  <h3>Using MonacoProvider with Existing Components</h3>
  <p>
    This example demonstrates how to use the MonacoProvider with existing Monaco-based components.
  </p>

  <MonacoProvider.Root>
    <div class="editor-container">
      <SimpleMonaco text={code} lang={language} />
    </div>
  </MonacoProvider.Root>
</div>

<style>
  .example-container {
    padding: 1rem;
    max-width: 800px;
  }

  .editor-container {
    height: 200px;
    border: 1px solid var(--ds-color-neutral-a6);
    border-radius: var(--ds-radius-2);
    overflow: hidden;
  }

  h3 {
    margin-top: 0;
    margin-bottom: 0.5rem;
  }

  p {
    margin-bottom: 1rem;
  }
</style>
