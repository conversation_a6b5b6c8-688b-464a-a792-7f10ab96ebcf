<script lang="ts">
  import type * as Monaco from "monaco-editor/esm/vs/editor/editor.api";
  import { onD<PERSON><PERSON> } from "svelte";
  import { MonacoContext } from "./context";
  import hljs from "highlight.js";
  import MonacoComponent from "./Monaco.svelte";

  export let text: string;
  export let lang: string | undefined = undefined;
  export let pathName: string | undefined = undefined;
  export let options: Record<string, any> = {};
  export let editorInstance: any | undefined = undefined;
  export let height: number | undefined = undefined;

  // Get the Monaco context
  const monacoContext = MonacoContext.getContext();
  const monaco = monacoContext.monaco;

  let model: Monaco.editor.ITextModel | undefined;
  let supportedLanguages: string[] = [];
  let composedLang: string | undefined;

  // Update the language when lang or supportedLanguages change
  function updateLanguage() {
    if (!lang || !supportedLanguages.includes(lang)) {
      // Use HLJS to guess the language if not provided or not supported
      const result = hljs.highlightAuto(text, supportedLanguages);
      composedLang = result.language;
    } else {
      composedLang = lang;
    }

    // Update the model language if it exists
    if (model && composedLang && $monaco) {
      $monaco.editor.setModelLanguage(model, composedLang);
    }
  }

  // Update the URI and model when pathName changes
  function updateUri(pathName: string | undefined) {
    if (!$monaco) return;

    // Create a new URI and model for Monaco
    const uri = pathName
      ? $monaco.Uri.parse(`file://${pathName}#${crypto.randomUUID()}`)
      : $monaco.Uri.parse(`file://#${crypto.randomUUID()}`);

    // Dispose of the old model if it exists, then create a new one
    model?.dispose();
    model = $monaco.editor.createModel(text, composedLang, uri);
  }

  // Update the model text when text changes
  $: if (model) {
    model.setValue(text);
  }

  $: supportedLanguages = $monaco?.languages.getLanguages().map((lang) => lang.id) || [];
  $: lang !== undefined && $monaco && updateLanguage();
  $: $monaco && updateUri(pathName);

  // Clean up on component destroy
  onDestroy(() => {
    model?.dispose();
  });
</script>

{#if model}
  <div class="c-monaco-simple">
    <MonacoComponent {options} {model} bind:editorInstance {height} />
  </div>
{/if}

<style>
  .c-monaco-simple {
    width: 100%;
    height: 100%;
  }
</style>
