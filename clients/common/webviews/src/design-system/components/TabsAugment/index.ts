// Export all tab components
export { default as TabsAugment } from "./TabsAugment.svelte";
export { default as TabListAugment } from "./TabListAugment.svelte";
export { default as TabAugment } from "./TabAugment.svelte";
export { default as TabPanelAugment } from "./TabPanelAugment.svelte";

// Export types
export type { TabsSize, TabsVariant, TabsColor, TabSize, TabVariant, TabColor } from "./types";

// Export context utilities
export {
  createTabGroupContext,
  setTabGroupContext,
  getTabGroupContext,
  // Backwards compatibility aliases
  createTabsContext,
  setTabsContext,
  getTabsContext,
} from "./context";
