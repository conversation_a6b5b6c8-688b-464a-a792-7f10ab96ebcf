# TabsAugment Components

Accessible, animated tabs component system for the Augment design system.

## Features

- **Accessibility**: ARIA compliant with keyboard navigation
- **Reactive State**: Svelte stores for consistent state management
- **Animations**: View Transitions API with sliding background support
- **Responsive**: Container queries and horizontal scrolling for overflow
- **Variants**: Default and pill styles with multiple sizes

## Components

### TabsAugment
Main container that manages tab state and provides context.

**Props:**
- `value?: string` - Active tab value
- `nullable?: boolean` - Allow no active tab (default: false)
- `size?: 1 | 2 | 3` - Size variant (default: 2)
- `variant?: "default" | "pill"` - Visual style (default: "default")
- `color?: "accent" | "neutral"` - Color theme (default: "accent")
- `loop?: boolean` - Wrap keyboard navigation (default: true)

**Events:**
- `change: { value: string }` - Fired when active tab changes

### TabListAugment
Container for tab buttons with keyboard navigation.

**Props:**
- `showSlidingBackground?: boolean` - Enable sliding background animation (default: false)

### TabAugment
Individual tab button.

**Props:**
- `value: string` - Unique identifier
- `disabled?: boolean` - Disabled state (default: false)
- `hideLabelOnNarrow?: boolean` - Hide label in narrow containers (default: false)

**Slots:**
- `leftIcon` - Left icon content
- `rightIcon` - Right icon content
- `default` - Tab label text

### TabPanelAugment
Content panel for each tab.

**Props:**
- `value: string` - Corresponding tab value
- `forceMount?: boolean` - Keep mounted when inactive (default: false)

## Usage

### Basic Example

```svelte
<script>
  import { TabsAugment, TabListAugment, TabAugment, TabPanelAugment } from "$design-system/components/TabsAugment";
  let activeTab = "chat";
</script>

<TabsAugment bind:value={activeTab}>
  <TabListAugment>
    <TabAugment value="chat">Chat</TabAugment>
    <TabAugment value="tasks">Tasks</TabAugment>
  </TabListAugment>

  <TabPanelAugment value="chat">Chat content</TabPanelAugment>
  <TabPanelAugment value="tasks">Tasks content</TabPanelAugment>
</TabsAugment>
```

### With Icons

```svelte
<TabAugment value="tasks">
  <TaskIcon slot="leftIcon" />
  Tasks
  <ChevronIcon slot="rightIcon" />
</TabAugment>
```

### External Control

```svelte
<script>
  let activeTab = "overview";
  function goToSettings() { activeTab = "settings"; }
</script>

<button on:click={goToSettings}>Jump to Settings</button>
<TabsAugment bind:value={activeTab} on:change={handleChange}>
  <!-- tabs and panels -->
</TabsAugment>
```

## Key Features

### Responsive Scrolling
Automatically handles tab overflow with horizontal scrolling, fade indicators, and scroll snap behavior.

### Sliding Background Animation
Optional animated background that smoothly transitions between active tabs.

### Nullable Tabs
Support for no active tab state using the `nullable` prop.

### Keyboard Navigation
Full keyboard support with arrow keys, Home/End, and automatic tab activation on focus.

## Accessibility

- **ARIA Compliance**: Proper roles, states, and properties
- **Keyboard Navigation**: Arrow keys, Home/End, Enter/Space
- **Focus Management**: Automatic focus handling and visual indicators
- **Screen Reader Support**: Descriptive labels and announcements


  </TabListAugment>

  <TabPanelAugment value="personal">
    <form>
      <input bind:value={formData.personal.name} placeholder="Name" />
      <input bind:value={formData.personal.email} placeholder="Email" type="email" />
      {#each errors.personal || [] as error}
        <div class="error">{error}</div>
      {/each}
    </form>
  </TabPanelAugment>

  <TabPanelAugment value="address">
    <form>
      <input bind:value={formData.address.street} placeholder="Street" />
      <input bind:value={formData.address.city} placeholder="City" />
      {#each errors.address || [] as error}
        <div class="error">{error}</div>
      {/each}
    </form>
  </TabPanelAugment>

  <TabPanelAugment value="payment">
    <form>
      <input bind:value={formData.payment.cardNumber} placeholder="Card Number" />
      <input bind:value={formData.payment.expiry} placeholder="MM/YY" />
      {#each errors.payment || [] as error}
        <div class="error">{error}</div>
      {/each}
    </form>
  </TabPanelAugment>
</TabsAugment>

<div class="form-controls">
  <button on:click={nextStep}>Next Step</button>
  <button on:click={goToFirstError}>Go to First Error</button>
</div>
```

### URL Routing Integration

Synchronize tab state with URL parameters for bookmarkable and shareable tab states.

```svelte
<script lang="ts">
  import { TabsAugment, TabListAugment, TabAugment, TabPanelAugment } from "$design-system/components/TabsAugment";
  import { page } from "$app/stores";
  import { goto } from "$app/navigation";

  let activeTab: string;

  // Sync tab with URL parameter
  $: activeTab = $page.url.searchParams.get("tab") || "home";

  function handleTabChange(event: CustomEvent<{ value: string }>) {
    const url = new URL($page.url);
    url.searchParams.set("tab", event.detail.value);
    goto(url.toString(), { replaceState: true, noScroll: true });
  }
</script>

<TabsAugment bind:value={activeTab} on:change={handleTabChange}>
  <TabListAugment>
    <TabAugment value="home">Home</TabAugment>
    <TabAugment value="projects">Projects</TabAugment>
    <TabAugment value="settings">Settings</TabAugment>
  </TabListAugment>

  <TabPanelAugment value="home">
    Home content - shareable via URL
  </TabPanelAugment>

  <TabPanelAugment value="projects">
    Projects content - bookmarkable
  </TabPanelAugment>

  <TabPanelAugment value="settings">
    Settings content - URL preserved
  </TabPanelAugment>
</TabsAugment>
```

### Nullable Tab Control

Use the `nullable` prop to allow programmatically clearing the active tab, useful for collapsible interfaces or reset functionality.

```svelte
<script lang="ts">
  import { TabsAugment, TabListAugment, TabAugment, TabPanelAugment } from "$design-system/components/TabsAugment";

  let activeTab: string | undefined = "details";
  let showDetails: boolean = true;

  // Clear tab when hiding details
  $: if (!showDetails) {
    activeTab = undefined;
  } else if (activeTab === undefined) {
    activeTab = "details";
  }

  function toggleDetails() {
    showDetails = !showDetails;
  }

  function clearSelection() {
    activeTab = undefined;
  }
</script>

<div class="controls">
  <button on:click={toggleDetails}>
    {showDetails ? "Hide" : "Show"} Details
  </button>
  <button on:click={clearSelection}>Clear Selection</button>
</div>

<TabsAugment bind:value={activeTab} nullable={true}>
  <TabListAugment>
    <TabAugment value="details">Details</TabAugment>
    <TabAugment value="specs">Specifications</TabAugment>
    <TabAugment value="reviews">Reviews</TabAugment>
  </TabListAugment>

  <TabPanelAugment value="details">
    Product details content
  </TabPanelAugment>

  <TabPanelAugment value="specs">
    Technical specifications
  </TabPanelAugment>

  <TabPanelAugment value="reviews">
    Customer reviews
  </TabPanelAugment>
</TabsAugment>

{#if activeTab === undefined}
  <div class="no-selection">
    No tab selected. Click a tab to view content.
  </div>
{/if}
```

### External Validation and Tab Switching Prevention

Prevent tab switching based on external validation, such as unsaved changes or incomplete forms.

```svelte
<script lang="ts">
  import { TabsAugment, TabListAugment, TabAugment, TabPanelAugment } from "$design-system/components/TabsAugment";

  let activeTab: string = "editor";
  let pendingTab: string | null = null;
  let hasUnsavedChanges: boolean = false;
  let editorContent: string = "";

  // Track unsaved changes
  $: hasUnsavedChanges = editorContent.trim().length > 0;

  function handleTabChange(event: CustomEvent<{ value: string }>) {
    const newTab = event.detail.value;

    // If there are unsaved changes and we're leaving the editor
    if (hasUnsavedChanges && activeTab === "editor" && newTab !== "editor") {
      pendingTab = newTab;
      // Show confirmation dialog or prevent the change
      if (!confirm("You have unsaved changes. Are you sure you want to leave?")) {
        // Prevent the tab change by not updating activeTab
        return;
      } else {
        // User confirmed, allow the change
        hasUnsavedChanges = false;
        editorContent = "";
      }
    }

    activeTab = newTab;
    pendingTab = null;
  }

  function saveChanges() {
    // Simulate saving
    console.log("Saving:", editorContent);
    hasUnsavedChanges = false;

    // If there was a pending tab change, execute it now
    if (pendingTab) {
      activeTab = pendingTab;
      pendingTab = null;
    }
  }

  function discardChanges() {
    editorContent = "";
    hasUnsavedChanges = false;

    if (pendingTab) {
      activeTab = pendingTab;
      pendingTab = null;
    }
  }
</script>

<TabsAugment bind:value={activeTab} on:change={handleTabChange}>
  <TabListAugment>
    <TabAugment value="editor">
      Editor
      {#if hasUnsavedChanges}
        <span slot="rightIcon" class="unsaved-indicator">•</span>
      {/if}
    </TabAugment>
    <TabAugment value="preview">Preview</TabAugment>
    <TabAugment value="settings">Settings</TabAugment>
  </TabListAugment>

  <TabPanelAugment value="editor">
    <textarea
      bind:value={editorContent}
      placeholder="Start typing..."
      rows="10"
      cols="50"
    ></textarea>

    {#if hasUnsavedChanges}
      <div class="unsaved-warning">
        <p>You have unsaved changes</p>
        <button on:click={saveChanges}>Save</button>
        <button on:click={discardChanges}>Discard</button>
      </div>
    {/if}
  </TabPanelAugment>

  <TabPanelAugment value="preview">
    <div class="preview">
      {editorContent || "Nothing to preview"}
    </div>
  </TabPanelAugment>

  <TabPanelAugment value="settings">
    Settings panel content
  </TabPanelAugment>
</TabsAugment>
```

### API State Synchronization

Synchronize tab state with external API data, automatically switching tabs based on data availability or user permissions from server responses.

```svelte
<script lang="ts">
  import { TabsAugment, TabListAugment, TabAugment, TabPanelAugment } from "$design-system/components/TabsAugment";
  import { onMount } from "svelte";

  let activeTab: string = "loading";
  let userData: any = null;
  let permissions: string[] = [];
  let isLoading: boolean = true;

  interface ApiResponse {
    user: any;
    permissions: string[];
    defaultView: string;
  }

  onMount(async () => {
    try {
      // Simulate API call
      const response = await fetch("/api/user-profile");
      const data: ApiResponse = await response.json();

      userData = data.user;
      permissions = data.permissions;

      // Set active tab based on API response
      if (data.defaultView && hasPermission(data.defaultView)) {
        activeTab = data.defaultView;
      } else if (hasPermission("dashboard")) {
        activeTab = "dashboard";
      } else {
        activeTab = "profile";
      }
    } catch (error) {
      console.error("Failed to load user data:", error);
      activeTab = "error";
    } finally {
      isLoading = false;
    }
  });

  function hasPermission(permission: string): boolean {
    return permissions.includes(permission);
  }

  function handleTabChange(event: CustomEvent<{ value: string }>) {
    // Sync tab changes back to server for user preferences
    if (!isLoading && event.detail.value !== "loading" && event.detail.value !== "error") {
      fetch("/api/user-preferences", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ defaultView: event.detail.value })
      }).catch(console.error);
    }
  }

  async function refreshData() {
    isLoading = true;
    activeTab = "loading";
    // Trigger data reload...
  }
</script>

<div class="api-controls">
  <button on:click={refreshData} disabled={isLoading}>
    Refresh Data
  </button>
  {#if userData}
    <span>Welcome, {userData.name}!</span>
  {/if}
</div>

<TabsAugment bind:value={activeTab} on:change={handleTabChange}>
  <TabListAugment>
    <TabAugment value="dashboard" disabled={!hasPermission("dashboard") || isLoading}>
      Dashboard
    </TabAugment>
    <TabAugment value="analytics" disabled={!hasPermission("analytics") || isLoading}>
      Analytics
    </TabAugment>
    <TabAugment value="admin" disabled={!hasPermission("admin") || isLoading}>
      Admin
    </TabAugment>
    <TabAugment value="profile" disabled={isLoading}>
      Profile
    </TabAugment>
    <TabAugment value="loading" disabled={!isLoading}>
      Loading...
    </TabAugment>
    <TabAugment value="error">
      Error
    </TabAugment>
  </TabListAugment>

  <TabPanelAugment value="dashboard">
    <div class="dashboard">
      Dashboard content for {userData?.name}
      <p>Permissions: {permissions.join(", ")}</p>
    </div>
  </TabPanelAugment>

  <TabPanelAugment value="analytics">
    <div class="analytics">
      Analytics data and charts
    </div>
  </TabPanelAugment>

  <TabPanelAugment value="admin">
    <div class="admin">
      Admin panel - restricted access
    </div>
  </TabPanelAugment>

  <TabPanelAugment value="profile">
    <div class="profile">
      User profile information
      {#if userData}
        <p>Email: {userData.email}</p>
        <p>Role: {userData.role}</p>
      {/if}
    </div>
  </TabPanelAugment>

  <TabPanelAugment value="loading">
    <div class="loading">
      <div class="spinner">Loading user data...</div>
    </div>
  </TabPanelAugment>

  <TabPanelAugment value="error">
    <div class="error">
      <p>Failed to load user data. Please try again.</p>
      <button on:click={refreshData}>Retry</button>
    </div>
  </TabPanelAugment>
</TabsAugment>
```

These external control examples demonstrate the flexibility and power of the TabsAugment system for building complex, interactive interfaces that respond to external state, user actions, and API data. The key patterns include:

- **Two-way binding** with `bind:value` for reactive control
- **Event handling** with `on:change` for external state synchronization
- **Conditional logic** for dynamic tab availability and switching
- **Integration patterns** with forms, routing, APIs, and validation
- **Nullable tabs** for clearable/collapsible interfaces
- **Permission-based** tab visibility and access control

### Pill Variant with Sliding Background

```svelte
<TabsAugment variant="pill">
  <TabListAugment showSlidingBackground={true}>
    <TabAugment value="tab1">First</TabAugment>
    <TabAugment value="tab2">Second</TabAugment>
  </TabListAugment>

  <!-- panels... -->
</TabsAugment>
```

### Icon Combinations

```svelte
<!-- Left icon only -->
<TabAugment value="chat">
  <ChatIcon slot="leftIcon" />
  Chat
</TabAugment>

<!-- Right icon only -->
<TabAugment value="settings">
  Settings
  <ExternalLinkIcon slot="rightIcon" />
</TabAugment>

<!-- Both left and right icons -->
<TabAugment value="tasks">
  <TaskIcon slot="leftIcon" />
  Tasks
  <ChevronDownIcon slot="rightIcon" />
</TabAugment>

<!-- No icons -->
<TabAugment value="about">
  About
</TabAugment>
```

### Responsive Behavior

```svelte
<TabAugment
  value="tasks"
  hideLabelOnNarrow={true}
>
  <TaskIcon slot="leftIcon" />
  Tasks
  <ChevronDownIcon slot="rightIcon" />
</TabAugment>
```

## Accessibility

- Full ARIA support with proper roles and attributes
- Keyboard navigation (Arrow keys, Home, End)
- Focus management with proper tab order
- Screen reader announcements for state changes
- High contrast mode support

## Animations

The TabsAugment system uses a layered animation approach:

### Tab Panel Animations (TabPanelAugment)
- **Entering Animation**: `ds-tab-panel-reveal-from-bottom` - Complex reveal with opacity, transform, and clip-path
- **Leaving Animation**: `ds-tab-panel-fade-out` - Simple fade out with upward translation
- **Base Transition**: CSS transition for smooth opacity/transform changes
- **View Transitions API**: For enhanced browser-native tab switching

### Sliding Background Animation (TabListAugment)
- **Background Slider**: CSS transition `all 0.15s ease-in-out` for smooth position/size changes
- **Resize Handling**: Transitions disabled during resize operations for performance
- **Responsive Updates**: Automatically repositions based on active tab changes

### Animation Architecture Decisions
- **Custom Keyframes**: TabPanelAugment uses custom animations instead of ds-animations.css utilities because:
  - Requires both entering AND leaving animations (utilities only provide entering)
  - Needs complex clip-path reveals not available in utility classes
  - Provides fine-tuned timing and easing specific to tab panel behavior
- **Accessibility**: All animations respect `prefers-reduced-motion` setting
- **Performance**: Sliding background transitions are disabled during resize for smooth interaction

## Styling

Uses design system tokens throughout:
- `--ds-color-*` for colors
- `--ds-spacing-*` for spacing
- `--ds-radius-*` for border radius
- `--ds-font-*` for typography
- `--ds-duration-*` and `--ds-easing-*` for animations

## Architecture

The TabsAugment system uses a **model/controller pattern** with Svelte stores for reactive state management:

- **TabsModel**: Manages state using individual readable stores for each property
- **TabsController**: Provides methods to update state, handles business logic, and manages sliding background animations
- **Context System**: Shares model and controller instances across all tab components
- **Sliding Background Management**: Controller automatically handles element registration, positioning, and responsive updates

This architecture ensures:
- **Consistent State**: All components react to the same state changes
- **Centralized Animation Logic**: Sliding background is managed entirely by the controller
- **Automatic Cleanup**: Controller handles all resource cleanup (observers, timeouts, subscriptions)
- **Type Safety**: Full TypeScript support with proper interfaces
- **Separation of Concerns**: Clear distinction between state management and UI logic
- **Testability**: Model and controller can be tested independently

## Browser Support

- Modern browsers with CSS Container Queries support
- View Transitions API (with graceful fallback)
- CSS Grid and Flexbox
