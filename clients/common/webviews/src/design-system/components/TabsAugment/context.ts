import { getContext, setContext } from "svelte";
import type { ITabsModel, ITabsController, TabsSize, TabsVariant, TabsColor } from "./types";
import { TabsModel, TabsController } from "./model";

/**
 * Tab group context containing both model and controller.
 */
export interface TabGroupContext {
  model: ITabsModel;
  controller: ITabsController;
}

/**
 * Context key for tab group.
 */
const TAB_GROUP_CONTEXT_KEY = Symbol("tab-group");

/**
 * Create a new tab group context with model and controller.
 */
export function createTabGroupContext(options?: {
  activeTab?: string | undefined;
  nullable?: boolean;
  size?: TabsSize;
  variant?: TabsVariant;
  color?: TabsColor;
  loop?: boolean;
  scrollable?: boolean;
  scrollShadow?: boolean;
  hideLabelOnNarrow?: boolean;
}): TabGroupContext {
  const model = new TabsModel(options);
  const controller = new TabsController(model);
  return { model, controller };
}

/**
 * Set the tab group context for child components.
 */
export function setTabGroupContext(context: TabGroupContext): void {
  setContext(TAB_GROUP_CONTEXT_KEY, context);
}

/**
 * Get the tab group context from parent components.
 */
export function getTabGroupContext(): TabGroupContext | undefined {
  return getContext<TabGroupContext>(TAB_GROUP_CONTEXT_KEY);
}

// Backwards compatibility aliases
export const createTabsContext = createTabGroupContext;
export const setTabsContext = setTabGroupContext;
export const getTabsContext = getTabGroupContext;
export type TabsContext = TabGroupContext;
