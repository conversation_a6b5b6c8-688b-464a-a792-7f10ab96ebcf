<script lang="ts" context="module">
  export type TabSize = 1 | 2 | 3;
  export type TabVariant = "default" | "pill";
  export type TabColor = "accent" | "neutral";
</script>

<script lang="ts">
  import type { ButtonSize, ButtonVariant, ButtonColor } from "../../_primitives/BaseButton.svelte";
  import BaseButton from "../../_primitives/BaseButton.svelte";

  // Tab-specific props
  export let size: TabSize = 2;
  export let variant: TabVariant = "default";
  export let color: TabColor = "accent";
  export let active: boolean = false;
  export let disabled: boolean = false;

  let className: string = "";
  export { className as class };
  $: buttonSize = size as ButtonSize;
  $: buttonVariant = mapTabVariantToButtonVariant(variant, active);
  $: buttonColor = color as ButtonColor;

  function mapTabVariantToButtonVariant(tabVariant: TabVariant, isActive: boolean): ButtonVariant {
    switch (tabVariant) {
      case "pill":
        return isActive ? "soft" : "ghost-block";
      case "default":
      default:
        return isActive ? "soft" : "ghost-block";
    }
  }
  $: tabClasses = [
    "c-tab-button",
    `c-tab-button--variant-${variant}`,
    active && "c-tab-button--active",
    disabled && "c-tab-button--disabled",
    className,
  ]
    .filter(Boolean)
    .join(" ");
</script>

<!-- DOM wrapper with display: contents to enable CSS selectors without affecting layout -->
<div class="c-tab-button-wrapper">
  <BaseButton
    size={buttonSize}
    variant={buttonVariant}
    color={buttonColor}
    {disabled}
    highContrast={false}
    loading={false}
    alignment="center"
    radius={variant === "pill" ? "full" : "medium"}
    class={tabClasses}
    role="tab"
    aria-selected={active}
    tabindex={active ? 0 : -1}
    on:click
    on:keydown
    on:keyup
    on:mousedown
    on:mouseover
    on:focus
    on:mouseleave
    on:blur
    on:contextmenu
    {...$$restProps}
  >
    <!-- Apply padding like ButtonAugment does -->
    <div class="c-tab-button__content">
      <slot />
    </div>
  </BaseButton>
</div>

<style>
  /* DOM wrapper with display: contents - doesn't affect layout but enables CSS selectors */
  .c-tab-button-wrapper {
    display: contents;

    /* Apply flex to the BaseButton when parent sets the custom property */
    & > :global(.c-base-btn) {
      flex: var(--tab-button-flex, unset);
    }
  }

  /* Content wrapper - applies padding like ButtonAugment */
  .c-tab-button__content {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    user-select: none;
    vertical-align: top;

    /* Apply BaseButton padding and gap */
    padding: var(--base-btn-padding-vertical) var(--base-btn-padding-horizontal);
    gap: var(--base-btn-gap);
  }
</style>
