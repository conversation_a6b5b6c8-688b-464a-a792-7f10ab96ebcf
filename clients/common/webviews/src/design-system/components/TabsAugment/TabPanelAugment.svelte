<script lang="ts">
  import { getTabGroupContext } from "./context";

  // Props
  export let value: string;
  export let className: string = "";
  export let forceMount: boolean = false;

  const tabsContext = getTabGroupContext();

  if (!tabsContext) {
    throw new Error("TabPanelAugment must be used within a TabsAugment component");
  }

  const { activeTab } = tabsContext.model;

  $: isActive = $activeTab === value;
  $: shouldRender = forceMount || isActive;

  let isEntering = false;
  let isLeaving = false;
  let previousActiveState = isActive;
  $: {
    if (isActive !== previousActiveState) {
      if (isActive) {
        isEntering = true;
        isLeaving = false;
      } else {
        isEntering = false;
        isLeaving = true;
      }
      previousActiveState = isActive;
    }
  }

  function handleAnimationEnd() {
    isEntering = false;
    isLeaving = false;
  }
  $: tabPanelClasses = [
    "ds-tab-panel",
    isActive && "ds-tab-panel--active",
    isEntering && "ds-tab-panel--entering",
    isLeaving && "ds-tab-panel--leaving",
    className,
  ]
    .filter(Boolean)
    .join(" ");
</script>

{#if shouldRender}
  <div
    class={tabPanelClasses}
    role="tabpanel"
    aria-labelledby="tab-{value}"
    tabindex={isActive ? 0 : -1}
    hidden={!isActive}
    data-state={isActive ? "active" : "inactive"}
    on:animationend={handleAnimationEnd}
  >
    <slot />
  </div>
{/if}

<style>
  .ds-tab-panel {
    /* Base panel styling */
    outline: none;

    /* Ensure panel takes available space */
    flex: 1;
    min-height: 0;

    /* Focus styles */
    &:focus-visible {
      outline: 2px solid var(--ds-color-accent-8);
      outline-offset: 2px;
      border-radius: var(--ds-radius-1);
    }
  }

  .ds-tab-panel--active {
    display: block;
  }

  .ds-tab-panel[hidden] {
    display: none;
  }

  /* Enhanced panel transitions */
  .ds-tab-panel {
    opacity: 0;
  }

  .ds-tab-panel--active {
    opacity: 1;
  }
</style>
