import type { Readable } from "svelte/store";

export type TabsSize = 1 | 2 | 3;
export type TabsVariant = "default" | "pill";
export type TabsColor = "accent" | "neutral";

// Individual tab types (aliases for consistency)
export type TabSize = TabsSize;
export type TabVariant = TabsVariant;
export type TabColor = TabsColor;

/**
 * Interface for a tabs model.
 * Provides individual readable stores for each tabs property.
 */
export interface ITabsModel {
  /** Readable store for the currently active tab value */
  readonly activeTab: Readable<string | undefined>;
  /** Readable store for whether active tab can be null/undefined */
  readonly nullable: Readable<boolean>;
  /** Readable store for tab size */
  readonly size: Readable<TabsSize>;
  /** Readable store for tab visual variant */
  readonly variant: Readable<TabsVariant>;
  /** Readable store for tab color theme */
  readonly color: Readable<TabsColor>;
  /** Readable store for whether tab navigation should loop around */
  readonly loop: Readable<boolean>;
  /** Readable store for whether tabs are currently resizing */
  readonly isResizing: Readable<boolean>;
  /** Readable store for whether scrolling is enabled */
  readonly scrollable: Readable<boolean>;
  /** Readable store for whether scroll shadows are enabled */
  readonly scrollShadow: Readable<boolean>;
  /** Readable store for whether tab labels should hide on narrow containers */
  readonly hideLabelOnNarrow: Readable<boolean>;
}

/**
 * Interface for tabs controller.
 * Provides methods to update tabs state and manage sliding background.
 */
export interface ITabsController {
  /** Set the active tab */
  setActiveTab(value: string | undefined): void;
  /** Set the nullable setting */
  setNullable(nullable: boolean): void;
  /** Set the size */
  setSize(size: TabsSize): void;
  /** Set the variant */
  setVariant(variant: TabsVariant): void;
  /** Set the color */
  setColor(color: TabsColor): void;
  /** Set the loop setting */
  setLoop(loop: boolean): void;
  /** Set the scrollable setting */
  setScrollable(scrollable: boolean): void;
  /** Set the scroll shadow setting */
  setScrollShadow(scrollShadow: boolean): void;
  /** Set the hide label on narrow setting */
  setHideLabelOnNarrow(hideLabelOnNarrow: boolean): void;

  // Sliding background management
  /** Register tab list element for sliding background management */
  registerTabList(element: HTMLElement): () => void;
  /** Register sliding background element */
  registerSlidingBackground(element: HTMLElement): () => void;
  /** Svelte action factory: Returns action for tab list element registration */
  withTabList(): (node: HTMLElement) => { destroy: () => void };
  /** Svelte action factory: Returns action for sliding background element registration */
  withSlidingBackground(): (node: HTMLElement) => { destroy: () => void };
  /** Update sliding background position and size */
  updateSlidingBackground(): void;
  /** Scroll active tab into view */
  scrollActiveTabIntoView(): void;

  // Keyboard navigation
  /** Update tab elements for keyboard navigation */
  updateTabElements(): void;
  /** Handle keyboard navigation events */
  handleKeyboardNavigation(event: KeyboardEvent): boolean;

  /** Destroy and cleanup all resources */
  destroy(): void;
}
