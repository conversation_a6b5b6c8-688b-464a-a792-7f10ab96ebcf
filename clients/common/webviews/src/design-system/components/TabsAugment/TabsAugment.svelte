<script lang="ts">
  import { createEventDispatcher, onDestroy } from "svelte";
  import { createTabGroupContext, setTabGroupContext } from "./context";
  import type { TabsSize, TabsVariant, TabsColor } from "./types";

  export let value: string | undefined = undefined;
  export let nullable: boolean = false;
  export let size: TabsSize = 2;
  export let variant: TabsVariant = "default";
  export let color: TabsColor = "accent";
  export let loop: boolean = true;
  export let scrollable: boolean = true;
  export let scrollShadow: boolean = true;
  export let hideLabelOnNarrow: boolean = false;
  export let className: string = "";
  const tabsContext = createTabGroupContext({
    activeTab: value,
    nullable,
    size,
    variant,
    color,
    loop,
    scrollable,
    scrollShadow,
    hideLabelOnNarrow,
  });

  const { model, controller } = tabsContext;

  const {
    activeTab: activeTabStore,
    size: sizeStore,
    variant: variantStore,
    color: colorStore,
  } = model;

  const dispatch = createEventDispatcher<{
    change: { value: string };
  }>();

  setTabGroupContext(tabsContext);

  onDestroy(() => {
    controller.destroy();
  });
  $: controller.setActiveTab(value);
  $: controller.setNullable(nullable);
  $: controller.setSize(size);
  $: controller.setVariant(variant);
  $: controller.setColor(color);
  $: controller.setLoop(loop);
  $: controller.setScrollable(scrollable);
  $: controller.setScrollShadow(scrollShadow);
  $: controller.setHideLabelOnNarrow(hideLabelOnNarrow);

  $: if ($activeTabStore !== undefined) {
    dispatch("change", { value: $activeTabStore });
  }
  $: tabsClasses = [
    "ds-tabs",
    `ds-tabs--size-${$sizeStore}`,
    `ds-tabs--variant-${$variantStore}`,
    `ds-tabs--color-${$colorStore}`,
    className,
  ]
    .filter(Boolean)
    .join(" ");
</script>

<div class={tabsClasses} role="tablist">
  <slot />
</div>

<style>
  .ds-tabs {
    /* Container query setup for responsive behavior */
    container-type: inline-size;
    container-name: tabs;
    display: flex;
    flex-direction: column;

    & :global(.ds-tab-list) {
      display: flex;
      flex-direction: row;
      align-items: center;
    }
  }

  /* Variant-specific container styles - these will be overridden by TabListAugment when needed */
  .ds-tabs--variant-default {
    & :global(.ds-tab-list:not(.ds-tab-list--with-sliding-background)) {
      gap: var(--ds-spacing-1);
    }
  }

  /* Pill variant styling is now handled in TabListAugment.svelte */

  /* Size-specific spacing - only apply when not using sliding background */
  .ds-tabs--size-1 {
    & :global(.ds-tab-list:not(.ds-tab-list--with-sliding-background)) {
      gap: var(--ds-spacing-1);
    }
  }

  .ds-tabs--size-2 {
    & :global(.ds-tab-list:not(.ds-tab-list--with-sliding-background)) {
      gap: var(--ds-spacing-2);
    }
  }

  .ds-tabs--size-3 {
    & :global(.ds-tab-list:not(.ds-tab-list--with-sliding-background)) {
      gap: var(--ds-spacing-3);
    }
  }
</style>
