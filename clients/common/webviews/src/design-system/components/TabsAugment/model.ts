import { writable, readonly, derived, get, type Readable, type Writable } from "svelte/store";
import type { ITabsModel, ITabsController, TabsSize, TabsVariant, TabsColor } from "./types";

/**
 * Implementation of the ITabsModel interface.
 * Manages tabs state using individual writable stores for each property.
 */
export class TabsModel implements ITabsModel {
  private _activeTabValue: Writable<string | undefined>;
  private _nullable: Writable<boolean>;
  private _size: Writable<TabsSize>;
  private _variant: Writable<TabsVariant>;
  private _color: Writable<TabsColor>;
  private _loop: Writable<boolean>;
  private _isResizing: Writable<boolean>;
  private _scrollable: Writable<boolean>;
  private _scrollShadow: Writable<boolean>;
  private _hideLabelOnNarrow: Writable<boolean>;
  private _tabElements: HTMLElement[] = [];

  public readonly activeTab: Readable<string | undefined>;
  public readonly nullable: Readable<boolean>;
  public readonly size: Readable<TabsSize>;
  public readonly variant: Readable<TabsVariant>;
  public readonly color: Readable<TabsColor>;
  public readonly loop: Readable<boolean>;
  public readonly isResizing: Readable<boolean>;
  public readonly scrollable: Readable<boolean>;
  public readonly scrollShadow: Readable<boolean>;
  public readonly hideLabelOnNarrow: Readable<boolean>;

  /**
   * Creates a new TabsModel instance.
   * @param initialData Initial tabs data (optional).
   */
  constructor(initialData?: {
    activeTab?: string | undefined;
    nullable?: boolean;
    size?: TabsSize;
    variant?: TabsVariant;
    color?: TabsColor;
    loop?: boolean;
    scrollable?: boolean;
    scrollShadow?: boolean;
    hideLabelOnNarrow?: boolean;
  }) {
    this._activeTabValue = writable(initialData?.activeTab ?? undefined);
    this._nullable = writable(initialData?.nullable ?? false);
    this._size = writable(initialData?.size ?? 2);
    this._variant = writable(initialData?.variant ?? "default");
    this._color = writable(initialData?.color ?? "accent");
    this._loop = writable(initialData?.loop ?? true);
    this._isResizing = writable(false);
    this._scrollable = writable(initialData?.scrollable ?? true);
    this._scrollShadow = writable(initialData?.scrollShadow ?? true);
    this._hideLabelOnNarrow = writable(initialData?.hideLabelOnNarrow ?? false);

    this.activeTab = derived(
      [this._activeTabValue, this._nullable],
      ([activeTabValue, nullable]) => {
        if (nullable) {
          return activeTabValue;
        }

        if (activeTabValue !== undefined) {
          return activeTabValue;
        }

        const enabledTabs = this._tabElements.filter(
          (tab) => !tab.hasAttribute("disabled") && tab.getAttribute("aria-disabled") !== "true",
        );
        const firstEnabledTabValue = enabledTabs[0]?.getAttribute("data-value");
        return firstEnabledTabValue || undefined;
      },
    );

    this.nullable = readonly(this._nullable);
    this.size = readonly(this._size);
    this.variant = readonly(this._variant);
    this.color = readonly(this._color);
    this.loop = readonly(this._loop);
    this.isResizing = readonly(this._isResizing);
    this.scrollable = readonly(this._scrollable);
    this.scrollShadow = readonly(this._scrollShadow);
    this.hideLabelOnNarrow = readonly(this._hideLabelOnNarrow);
  }

  /**
   * Internal method to get writable stores for the controller.
   * @internal
   */
  _getWritableStores() {
    return {
      activeTabValue: this._activeTabValue,
      nullable: this._nullable,
      size: this._size,
      variant: this._variant,
      color: this._color,
      loop: this._loop,
      isResizing: this._isResizing,
      scrollable: this._scrollable,
      scrollShadow: this._scrollShadow,
      hideLabelOnNarrow: this._hideLabelOnNarrow,
    };
  }

  /**
   * Internal method to update tab elements for fallback logic.
   * @internal
   */
  _updateTabElements(elements: HTMLElement[]) {
    this._tabElements = elements;
    this._activeTabValue.update((value) => value);
  }
}

/**
 * Implementation of the ITabsController interface.
 * Provides methods to update tabs state and manage sliding background.
 */
export class TabsController implements ITabsController {
  private _stores: ReturnType<TabsModel["_getWritableStores"]>;
  private _tabListElement: HTMLElement | undefined;
  private _slidingBackgroundElement: HTMLElement | undefined;
  private _resizeObserver: ResizeObserver | undefined;
  private _mutationObserver: MutationObserver | undefined;
  private _resizeTimeout: NodeJS.Timeout | undefined;
  private _scrollRafId: number | undefined;
  private _activeTabUnsubscribe: (() => void) | undefined;
  private _tabElements: HTMLElement[] = [];

  constructor(private _model: TabsModel) {
    this._stores = this._model._getWritableStores();
    this._activeTabUnsubscribe = this._model.activeTab.subscribe(() => {
      this.updateSlidingBackground();
    });
  }

  /** Set the active tab */
  setActiveTab(value: string | undefined): void {
    const nullable = get(this._stores.nullable);

    if (!nullable && value === undefined) {
      const enabledTabs = this._tabElements.filter(
        (tab) => !tab.hasAttribute("disabled") && tab.getAttribute("aria-disabled") !== "true",
      );
      const firstEnabledTabValue = enabledTabs[0]?.getAttribute("data-value");
      if (firstEnabledTabValue) {
        this._stores.activeTabValue.set(firstEnabledTabValue);
        return;
      }
      return;
    }

    this._stores.activeTabValue.set(value);
  }

  /** Set the nullable setting */
  setNullable(nullable: boolean): void {
    this._stores.nullable.set(nullable);
  }

  /** Set the size */
  setSize(size: TabsSize): void {
    this._stores.size.set(size);
  }

  /** Set the variant */
  setVariant(variant: TabsVariant): void {
    this._stores.variant.set(variant);
  }

  /** Set the color */
  setColor(color: TabsColor): void {
    this._stores.color.set(color);
  }

  /** Set the loop setting */
  setLoop(loop: boolean): void {
    this._stores.loop.set(loop);
  }

  /** Set the scrollable setting */
  setScrollable(scrollable: boolean): void {
    this._stores.scrollable.set(scrollable);
  }

  /** Set the scroll shadow setting */
  setScrollShadow(scrollShadow: boolean): void {
    this._stores.scrollShadow.set(scrollShadow);
  }

  /** Set the hide label on narrow setting */
  setHideLabelOnNarrow(hideLabelOnNarrow: boolean): void {
    this._stores.hideLabelOnNarrow.set(hideLabelOnNarrow);
  }

  /** Register tab list element for sliding background management */
  registerTabList(element: HTMLElement): () => void {
    if (this._tabListElement && this._tabListElement !== element) {
      this._cleanupObservers();
    }

    this._tabListElement = element;
    this._setupObservers();

    const cleanup = () => {
      if (this._tabListElement === element) {
        this._tabListElement = undefined;
        this._cleanupObservers();
      }
    };

    return cleanup;
  }

  /** Register sliding background element */
  registerSlidingBackground(element: HTMLElement): () => void {
    this._slidingBackgroundElement = element;
    this.updateSlidingBackground();

    const cleanup = () => {
      if (this._slidingBackgroundElement === element) {
        this._slidingBackgroundElement = undefined;
      }
    };

    return cleanup;
  }

  withTabList() {
    return (node: HTMLElement) => {
      const cleanup = this.registerTabList(node);
      this.updateTabElements();

      return {
        destroy: cleanup,
      };
    };
  }

  withSlidingBackground() {
    return (node: HTMLElement) => {
      const cleanup = this.registerSlidingBackground(node);

      return {
        destroy: cleanup,
      };
    };
  }

  /** Update tab elements for keyboard navigation */
  updateTabElements(): void {
    if (!this._tabListElement) return;
    this._tabElements = Array.from(this._tabListElement.querySelectorAll('[role="tab"]'));
    // Also update the model's tab elements for fallback logic
    this._model._updateTabElements(this._tabElements);
  }

  /** Get enabled tab elements only */
  private getEnabledTabElements(): HTMLElement[] {
    return this._tabElements.filter(
      (tab) => !tab.hasAttribute("disabled") && tab.getAttribute("aria-disabled") !== "true",
    );
  }

  /** Find next enabled tab index */
  private findNextEnabledTabIndex(currentIndex: number, direction: 1 | -1, loop: boolean): number {
    const enabledTabs = this.getEnabledTabElements();
    if (enabledTabs.length === 0) return -1;

    // Find current tab in enabled tabs list
    const currentTab = this._tabElements[currentIndex];
    const currentEnabledIndex = enabledTabs.indexOf(currentTab);

    if (currentEnabledIndex === -1) {
      // Current tab is disabled or not found, return first enabled tab
      return this._tabElements.indexOf(enabledTabs[0]);
    }

    let nextEnabledIndex = currentEnabledIndex + direction;

    if (loop) {
      // Loop around
      if (nextEnabledIndex >= enabledTabs.length) {
        nextEnabledIndex = 0;
      } else if (nextEnabledIndex < 0) {
        nextEnabledIndex = enabledTabs.length - 1;
      }
    } else {
      // Clamp to bounds
      nextEnabledIndex = Math.max(0, Math.min(enabledTabs.length - 1, nextEnabledIndex));
    }

    // Return the index in the original tab elements array
    return this._tabElements.indexOf(enabledTabs[nextEnabledIndex]);
  }

  /** Handle keyboard navigation */
  handleKeyboardNavigation(event: KeyboardEvent): boolean {
    if (!this._tabElements.length) return false;

    const currentIndex = this._tabElements.findIndex((tab) => tab === event.target);
    if (currentIndex === -1) return false;

    // Check if no enabled tabs are available
    const enabledTabs = this.getEnabledTabElements();
    if (enabledTabs.length === 0) return false;

    let nextIndex = currentIndex;
    let handled = false;

    // Get current store values synchronously
    const currentLoop = get(this._stores.loop);

    switch (event.key) {
      case "ArrowLeft":
        event.preventDefault();
        nextIndex = this.findNextEnabledTabIndex(currentIndex, -1, currentLoop);
        handled = true;
        break;
      case "ArrowRight":
        event.preventDefault();
        nextIndex = this.findNextEnabledTabIndex(currentIndex, 1, currentLoop);
        handled = true;
        break;

      case "Home":
        event.preventDefault();
        // Go to first enabled tab
        nextIndex = this._tabElements.indexOf(enabledTabs[0]);
        handled = true;
        break;
      case "End":
        event.preventDefault();
        // Go to last enabled tab
        nextIndex = this._tabElements.indexOf(enabledTabs[enabledTabs.length - 1]);
        handled = true;
        break;
      case "Escape": {
        // Clear active tab selection only if nullable is true
        event.preventDefault();
        const nullable = get(this._stores.nullable);
        if (nullable) {
          this.setActiveTab(undefined);
        }
        handled = true;
        break;
      }
    }

    if (handled && nextIndex !== currentIndex) {
      this._tabElements[nextIndex]?.focus();

      // Always use automatic activation mode (activate tab on focus)
      const tabValue = this._tabElements[nextIndex]?.getAttribute("data-value");
      if (tabValue) {
        this.setActiveTab(tabValue);
      }
    }

    return handled;
  }

  /** Update sliding background position and size (with scroll offset support) */
  updateSlidingBackground(): void {
    const currentActiveTab = get(this._model.activeTab);

    // Get current active tab value
    // Find active button using selector (more efficient)
    const activeButton = this._tabListElement?.querySelector(
      `.c-tab-button-wrapper > button[data-value="${currentActiveTab}"]`,
    );

    if (!this._tabListElement || !this._slidingBackgroundElement) {
      return;
    }

    // If no active button found (no active tab), hide the slider
    if (!activeButton) {
      this._slidingBackgroundElement.style.setProperty("--slider-opacity", "0");
      this._slidingBackgroundElement.style.setProperty("--slider-visibility", "hidden");
      return;
    }

    // Batch DOM reads first to avoid reflows
    const activeRect = activeButton.getBoundingClientRect();
    const containerRect = this._tabListElement.getBoundingClientRect();
    const scrollOffset = this._tabListElement.scrollLeft || 0;

    // Calculate position values
    const relativeLeft = activeRect.left - containerRect.left + scrollOffset;
    const width = activeRect.width;
    const height = activeRect.height;

    // Batch style updates using CSS variables and transform
    this._slidingBackgroundElement.style.setProperty("--slider-opacity", "1");
    this._slidingBackgroundElement.style.setProperty("--slider-visibility", "visible");
    this._slidingBackgroundElement.style.setProperty("--slider-width", `${width}px`);
    this._slidingBackgroundElement.style.setProperty("--slider-height", `${height}px`);
    this._slidingBackgroundElement.style.setProperty(
      "--slider-transform",
      `translateX(${relativeLeft}px)`,
    );
  }

  /** Scroll active tab into view */
  scrollActiveTabIntoView(): void {
    if (!this._tabListElement) return;

    const activeTabValue = get(this._model.activeTab);
    if (!activeTabValue) return;

    const activeTab = this._tabListElement.querySelector(`[data-value="${activeTabValue}"]`);
    if (activeTab) {
      activeTab.scrollIntoView({
        behavior: "smooth",
        block: "nearest",
        inline: "center",
      });
    }
  }

  /** Setup observers for responsive updates */
  private _setupObservers(): void {
    if (!this._tabListElement) return;

    // Clean up previous observers
    this._cleanupObservers();

    // Add scroll event listener for sliding background updates (passive for better performance)
    this._tabListElement.addEventListener("scroll", this._handleScroll, { passive: true });

    // Setup ResizeObserver for responsive updates (similar to ToggleButtonAugment)
    this._resizeObserver = new ResizeObserver(() => {
      this._stores.isResizing.set(true);
      this.updateSlidingBackground();

      if (this._resizeTimeout) {
        clearTimeout(this._resizeTimeout);
      }

      // Turn off resizing state after 100ms of no resize events
      this._resizeTimeout = setTimeout(() => {
        this._stores.isResizing.set(false);
      }, 100);
    });
    if (this._tabListElement) {
      this._resizeObserver.observe(this._tabListElement);
    }

    // Setup MutationObserver to watch for tab changes
    this._mutationObserver = new MutationObserver(() => {
      this.updateTabElements();
      this.updateSlidingBackground();
    });
    if (this._tabListElement) {
      this._mutationObserver.observe(this._tabListElement, {
        childList: true,
        subtree: true,
      });
    }
  }

  /** Handle scroll events to update sliding background (throttled with RAF) */
  private _handleScroll = (): void => {
    // Cancel any pending animation frame to avoid duplicate work
    if (this._scrollRafId !== undefined) {
      cancelAnimationFrame(this._scrollRafId);
    }

    // Schedule the sliding background update for the next frame
    this._scrollRafId = requestAnimationFrame(() => {
      this.updateSlidingBackground();
      this._scrollRafId = undefined;
    });
  };

  /** Clean up observers and timers */
  private _cleanupObservers(): void {
    this._resizeObserver?.disconnect();
    this._mutationObserver?.disconnect();
    this._resizeObserver = undefined;
    this._mutationObserver = undefined;

    if (this._resizeTimeout) {
      clearTimeout(this._resizeTimeout);
      this._resizeTimeout = undefined;
    }

    // Cancel any pending scroll animation frame
    if (this._scrollRafId !== undefined) {
      cancelAnimationFrame(this._scrollRafId);
      this._scrollRafId = undefined;
    }

    // Remove scroll event listener
    if (this._tabListElement) {
      this._tabListElement.removeEventListener("scroll", this._handleScroll);
    }

    // Reset resizing state
    this._stores.isResizing.set(false);
  }

  /** Destroy and cleanup all resources */
  destroy(): void {
    // Cleanup observers and timers
    this._cleanupObservers();

    // Cleanup subscriptions
    this._activeTabUnsubscribe?.();

    // Clear all references
    this._tabListElement = undefined;
    this._slidingBackgroundElement = undefined;
    this._activeTabUnsubscribe = undefined;
  }
}
