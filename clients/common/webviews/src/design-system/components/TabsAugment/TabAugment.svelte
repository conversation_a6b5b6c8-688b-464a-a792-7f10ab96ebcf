<script lang="ts">
  import { createEventDispatcher } from "svelte";
  import { getTabGroupContext } from "./context";
  import TabButton, { type TabSize, type TabVariant, type TabColor } from "./TabButton.svelte";

  export let value: string;
  export let disabled: boolean = false;
  export let className: string = "";

  const tabsContext = getTabGroupContext();

  const {
    size: sizeStore,
    variant: variantStore,
    color: colorStore,
    activeTab: activeTabStore,
    hideLabelOnNarrow: hideLabelOnNarrowStore,
  } = tabsContext?.model || {};
  $: size = sizeStore ? ($sizeStore as TabSize) : 2;
  $: variant = variantStore ? ($variantStore as TabVariant) : "default";
  $: color = colorStore ? ($colorStore as TabColor) : "accent";
  $: active = activeTabStore ? $activeTabStore === value : false;
  $: hideLabelOnNarrow = hideLabelOnNarrowStore ? $hideLabelOnNarrowStore : false;

  const dispatch = createEventDispatcher<{
    click: MouseEvent;
    keydown: KeyboardEvent;
  }>();

  function handleClick(event: MouseEvent): void {
    if (!disabled && tabsContext) {
      tabsContext.controller.setActiveTab(value);
    }
    dispatch("click", event);
  }

  function handleKeydown(event: KeyboardEvent) {
    if ((event.key === "Enter" || event.key === " ") && !disabled) {
      event.preventDefault();
      handleClick(event as any);
    }
    dispatch("keydown", event);
  }
  $: additionalClasses = ["ds-tab", className].filter(Boolean).join(" ");
</script>

<TabButton
  {size}
  {variant}
  {color}
  {active}
  {disabled}
  class={additionalClasses}
  aria-controls="panel-{value}"
  id="tab-{value}"
  data-value={value}
  on:click={handleClick}
  on:keydown={handleKeydown}
>
  <div class="ds-tab__content ds-tab--size-{size}">
    {#if $$slots.leftIcon}
      <div class="ds-tab__left-icon">
        <slot name="leftIcon" />
      </div>
    {/if}

    {#if $$slots.default}
      <div class="ds-tab__label" class:ds-tab__label--hide-on-narrow={hideLabelOnNarrow}>
        <slot />
      </div>
    {/if}

    {#if $$slots.rightIcon}
      <div class="ds-tab__right-icon">
        <slot name="rightIcon" />
      </div>
    {/if}
  </div>
</TabButton>

<style>
  /* Tab content layout - TabButton handles the button styling */
  .ds-tab__content {
    display: flex;
    align-items: center;
    gap: var(--base-btn-gap); /* Use the same gap as BaseButton for consistency */
    justify-content: center;

    --tab-button-font-size: var(--ds-font-size-2);
    --tab-button-line-height: var(--ds-line-height-2);

    font-size: var(--tab-button-font-size);
    line-height: var(--tab-button-line-height);

    & .ds-tab__right-icon,
    & .ds-tab__left-icon,
    & .ds-tab__label {
      font-size: var(--tab-button-font-size);
      line-height: var(--tab-button-line-height);
    }
  }

  .ds-tab__left-icon,
  .ds-tab__right-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    line-height: 1;

    --icon-size: var(--ds-icon-size-2);

    & :global(svg) {
      width: var(--icon-size);
      height: var(--icon-size);
      max-width: var(--icon-size);
      max-height: var(--icon-size);
    }
  }

  .ds-tab__label {
    display: flex;
    align-items: center;
    min-width: 0; /* Allow text to truncate */
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .ds-tab--size-1 {
    & .ds-tab__left-icon,
    & .ds-tab__right-icon {
      --icon-size: var(--ds-icon-size-0);
    }

    &.ds-tab__content {
      --tab-button-font-size: var(--ds-font-size-1);
      --tab-button-line-height: var(--ds-line-height-1);
    }
  }

  .ds-tab__label--hide-on-narrow {
    @media (max-width: 240px) {
      display: none;
    }
  }

  /* Responsive label hiding */
  @container (max-width: 240px) {
    .ds-tab__label--hide-on-narrow {
      width: 0;
      overflow: hidden;
    }
  }
</style>
