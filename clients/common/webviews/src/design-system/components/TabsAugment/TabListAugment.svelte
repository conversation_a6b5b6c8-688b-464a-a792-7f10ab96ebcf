<script lang="ts">
  import { onMount, onDestroy } from "svelte";
  import { getTabGroupContext } from "./context";

  export let className: string = "";
  export let showSlidingBackground: boolean = false;
  export let outline: boolean = false;
  export let expandToFit: boolean = false;
  const tabsContext = getTabGroupContext();

  if (!tabsContext) {
    throw new Error("TabListAugment must be used within a TabsAugment component");
  }

  const { model, controller } = tabsContext;

  const {
    activeTab: activeTabStore,
    size: sizeStore,
    variant: variantStore,
    color: colorStore,
    isResizing: isResizingStore,
    scrollable: scrollableStore,
    scrollShadow: scrollShadowStore,
  } = model;

  let scrollContainer: HTMLElement;
  let showLeftMask = false;
  let showRightMask = false;
  let rafId: number | null = null;

  const tabListAction = controller.withTabList();
  const slidingBackgroundAction = controller.withSlidingBackground();

  function handleKeydown(event: KeyboardEvent) {
    controller.handleKeyboardNavigation(event);
  }

  /**
   * Updates mask visibility based on scroll position
   * This is the actual work function that gets called by the throttled handler
   */
  function updateMaskVisibility() {
    if (!scrollContainer || !$scrollableStore || !$scrollShadowStore) {
      showLeftMask = false;
      showRightMask = false;
      return;
    }

    const { scrollLeft, scrollWidth, clientWidth } = scrollContainer;
    showLeftMask = scrollLeft > 0;
    showRightMask = scrollLeft < scrollWidth - clientWidth - 1;
  }

  /**
   * Throttled scroll handler using requestAnimationFrame for optimal performance
   * This ensures mask updates happen at most once per frame
   */
  function handleScroll() {
    // Cancel any pending animation frame to avoid duplicate work
    if (rafId !== null) {
      cancelAnimationFrame(rafId);
    }

    // Schedule the mask update for the next frame
    rafId = requestAnimationFrame(() => {
      updateMaskVisibility();
      rafId = null;
    });
  }

  $: if ($activeTabStore && $scrollableStore) {
    controller.scrollActiveTabIntoView();
  }

  // Update scroll shadows when scroll settings change
  $: if (scrollContainer) {
    updateMaskVisibility();
  }

  onMount(() => {
    if (scrollContainer) {
      updateMaskVisibility();
    }
  });

  onDestroy(() => {
    // Clean up any pending animation frame
    if (rafId !== null) {
      cancelAnimationFrame(rafId);
      rafId = null;
    }
  });

  $: containerClasses = ["ds-tab-list-container", outline && "ds-tab-list-container--outline"]
    .filter(Boolean)
    .join(" ");

  $: tabListClasses = [
    "ds-tab-list",
    `ds-tab-list--variant-${$variantStore}`,
    `ds-tab-list--color-${$colorStore}`,
    `ds-tabs--size-${$sizeStore}`,
    showSlidingBackground && "ds-tab-list--with-sliding-background",
    outline && "ds-tab-list--outline",
    $isResizingStore && "ds-tab-list--resizing",
    $scrollableStore && "ds-tab-list--scrollable",
    $scrollableStore && $scrollShadowStore && showLeftMask && "ds-tab-list--show-left-mask",
    $scrollableStore && $scrollShadowStore && showRightMask && "ds-tab-list--show-right-mask",
    expandToFit && "ds-tab-list--expand-to-fit",
    className,
  ]
    .filter(Boolean)
    .join(" ");
</script>

<div class={containerClasses}>
  <!-- Scrollable tab list with mask-based fade -->
  <div
    bind:this={scrollContainer}
    use:tabListAction
    class={tabListClasses}
    data-ds-color={$colorStore}
    role="tablist"
    aria-label="Tab navigation"
    tabindex="-1"
    on:keydown={handleKeydown}
    on:scroll={handleScroll}
    data-active-tab={$activeTabStore}
  >
    {#if showSlidingBackground}
      <div class="background-slider" use:slidingBackgroundAction aria-hidden="true"></div>
    {/if}

    <slot />
  </div>
</div>

<style>
  /* Container for the scrollable tab list */
  .ds-tab-list-container {
    position: relative;
    display: flex;
    align-items: center;
    width: 100%;
    max-width: 100%;
  }

  /* Outline styling on container - similar to ToggleAugment border */
  .ds-tab-list-container--outline {
    border: 1px solid var(--ds-color-neutral-a6);
    border-radius: var(--ds-radius-2);
    background-color: var(--ds-color-neutral-a2);
    box-sizing: border-box; /* Include border in width calculation */
  }

  /* Main tab list */
  .ds-tab-list {
    display: flex;
    align-items: center;
    overflow: visible; /* Default: no scrolling, minimal styling */
  }

  /* Expand to fit option - minimum 100% width with content expansion */
  .ds-tab-list--expand-to-fit {
    min-width: 100%; /* Always take at least full width */
    width: 100%; /* Take full width to distribute tabs */
    gap: 0; /* Remove gaps between tabs when expanding to fit */
    box-sizing: border-box; /* Include border in width calculation */

    /* Set CSS custom property for child tab buttons to use flex */
    --tab-button-flex: 1;
  }

  /* Outline styling - padding adjustment for outlined container */
  .ds-tab-list--outline {
    padding: 0;
  }

  /* Scrollable tab list */
  .ds-tab-list--scrollable {
    --tab-list-padding-vertical: var(--ds-spacing-1);
    --tab-list-padding-horizontal: var(--ds-spacing-2);
    --fade-size: var(--ds-spacing-3); /* 12px fade width */

    position: relative;
    overflow-x: auto;
    overflow-y: hidden;
    scroll-behavior: smooth;
    scroll-snap-type: x mandatory;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE/Edge */

    /* Hide scrollbar for WebKit browsers */
    &::-webkit-scrollbar {
      display: none;
    }

    /* Enable momentum scrolling on iOS */
    -webkit-overflow-scrolling: touch;

    /* Mask-based fade effects - similar to ContextBar implementation */
    &.ds-tab-list--show-right-mask {
      mask-image: linear-gradient(
        to right,
        black 0%,
        black calc(100% - var(--fade-size)),
        transparent 100%
      );
      -webkit-mask-image: linear-gradient(
        to right,
        black 0%,
        black calc(100% - var(--fade-size)),
        transparent 100%
      );
    }

    &.ds-tab-list--show-left-mask {
      mask-image: linear-gradient(to right, transparent 0%, black var(--fade-size), black 100%);
      -webkit-mask-image: linear-gradient(
        to right,
        transparent 0%,
        black var(--fade-size),
        black 100%
      );
    }

    &.ds-tab-list--show-left-mask.ds-tab-list--show-right-mask {
      mask-image: linear-gradient(
        to right,
        transparent 0%,
        black var(--fade-size),
        black calc(100% - var(--fade-size)),
        transparent 100%
      );
      -webkit-mask-image: linear-gradient(
        to right,
        transparent 0%,
        black var(--fade-size),
        black calc(100% - var(--fade-size)),
        transparent 100%
      );
    }
  }

  /* Scroll snap for individual tabs - only when scrollable */
  .ds-tab-list--scrollable > :global(.c-tab-button-wrapper) {
    scroll-snap-align: center;
    flex-shrink: 0;
  }

  /* Override gap for better icon-to-text spacing in tabs */
  /* Set CSS custom properties that cascade down to child components */
  .ds-tab-list.ds-tabs--size-1 {
    --base-btn-gap: var(--ds-spacing-1); /* 4px for size 1 */

    /* Smaller gap between tabs for size 1 */
    &:not(.ds-tab-list--with-sliding-background) {
      gap: calc(var(--ds-spacing-1) * 0.5); /* 2px gap for size 1 */
    }
  }

  .ds-tab-list.ds-tabs--size-2 {
    --base-btn-gap: var(--ds-spacing-2); /* 8px for size 2 - override ghost reduction */
  }

  .ds-tab-list.ds-tabs--size-3 {
    --base-btn-gap: var(--ds-spacing-3); /* 12px for size 3 - override ghost reduction */
  }

  /* Pill variant styling when NOT using sliding background */
  .ds-tab-list:not(.ds-tab-list--with-sliding-background) {
    /* Default gap */
    gap: var(--ds-spacing-1);
  }

  /* Pill variant container styling */
  .ds-tab-list.ds-tab-list--variant-pill:not(.ds-tab-list--with-sliding-background) {
    gap: var(--ds-spacing-1);
    /* No padding or background for pill mode without sliding background */
    /* Individual buttons provide their own styling and spacing */
  }

  /* Container styling when sliding background is enabled */
  .ds-tab-list--with-sliding-background {
    /* Same gap as normal mode for consistency */
    gap: var(--ds-spacing-1);

    /* Tab button styling - transparent buttons, slider provides visual feedback */
    & > :global(.c-tab-button-wrapper > button.c-base-btn.c-tab-button) {
      /* Remove background styling - slider provides visual feedback */
      --base-btn-bg-color: transparent;
      --base-btn-active-bg-color: transparent;

      /* Subtle hover states that work with sliding background */
      /* Note: border-radius is already set by BaseButton based on the radius prop */
      &:hover:not(:disabled) {
        --base-btn-hover-bg-color: var(--ds-color-neutral-a2);
      }

      /* Text color based on active state */
      &.c-tab-button--active {
        --base-btn-color: var(--ds-color-11);

        /* Active tab hover - slightly different color for better contrast */
        &:hover:not(:disabled) {
          --base-btn-hover-bg-color: var(--ds-color-a2);
        }
      }

      &:not(.c-tab-button--active) {
        --base-btn-color: var(--ds-color-neutral-11);
      }
    }

    /* Background slider - clean styling using CSS variables */
    & > .background-slider {
      position: absolute;
      background-color: var(--ds-color-neutral-a1);
      transition:
        transform 0.15s ease-in-out,
        opacity 0.15s ease-in-out,
        visibility 0.15s ease-in-out;
      border-radius: var(--ds-radius-2);
      pointer-events: none;

      /* Use CSS variables for positioning and sizing */
      opacity: var(--slider-opacity, 1);
      visibility: var(--slider-visibility, visible);
      width: var(--slider-width, auto);
      height: var(--slider-height, auto);
      transform: var(--slider-transform, translateX(0));
    }

    /* Disable transitions during resize (applies to all variants) */
    &.ds-tab-list--resizing > .background-slider {
      transition:
        opacity 0.15s ease-in-out,
        visibility 0.15s ease-in-out; /* Keep opacity/visibility transitions, disable transform */
    }
  }

  /* Color-specific slider styling */
  .ds-tab-list--with-sliding-background[data-ds-color="accent"] > .background-slider {
    background-color: var(--ds-color-a4);
  }

  .ds-tab-list--with-sliding-background[data-ds-color="neutral"] > .background-slider {
    background-color: var(--ds-color-a3);
  }

  /* Variant-specific container and slider styling */
  .ds-tab-list--with-sliding-background.ds-tab-list--variant-pill {
    border-radius: var(--ds-radius-full); /* Full pill shape for container */
    /* No background color for pill mode with sliding background */

    & > .background-slider {
      border-radius: 9999px;
    }
  }

  /* Pill variant button styling - border radius is handled by BaseButton through dsRadiusAttribute */
  /* No additional styling needed for pill variant with sliding background */

  /* Default styling when no sliding background (regular tab list) */
  .ds-tab-list:not(.ds-tab-list--with-sliding-background) {
    /* Style tab buttons in regular mode using CSS variables */
    & > :global(.c-tab-button-wrapper > button.c-base-btn.c-tab-button) {
      /* Ensure consistent hover states for inactive tabs */
      &:hover:not(.c-tab-button--active):not(:disabled) {
        --base-btn-hover-bg-color: var(--ds-color-neutral-a3);
      }

      /* Active state styling - higher specificity to override BaseButton defaults */
      &.c-tab-button--active.c-tab-button--variant-default {
        --base-btn-bg-color: var(--ds-color-a3);
        --base-btn-hover-bg-color: var(--ds-color-a4);
        --base-btn-active-bg-color: var(--ds-color-a5);
        --base-btn-color: var(--ds-color-11);
      }

      /* Pill variant active state - use solid background for better contrast */
      &.c-tab-button--active.c-tab-button--variant-pill {
        --base-btn-bg-color: var(--ds-color-a9);
        --base-btn-hover-bg-color: var(--ds-color-a10);
        --base-btn-active-bg-color: var(--ds-color-a11);
        --base-btn-color: var(--ds-color-contrast);
      }
    }
  }
</style>
