import { describe, it, expect } from "vitest";
import { get } from "svelte/store";
import { TabsModel, TabsController } from "./model";
import { createTabGroupContext } from "./context";

describe("TabsAugment Store Model", () => {
  describe("TabsModel", () => {
    it("should create model with default values", () => {
      const model = new TabsModel();

      expect(get(model.activeTab)).toBeUndefined();
      expect(get(model.size)).toBe(2);
      expect(get(model.variant)).toBe("default");
      expect(get(model.color)).toBe("accent");
      expect(get(model.loop)).toBe(true);
      expect(get(model.scrollable)).toBe(true);
      expect(get(model.scrollShadow)).toBe(true);
    });

    it("should create model with custom initial values", () => {
      const model = new TabsModel({
        activeTab: "custom-tab",
        size: 3,
        variant: "pill",
        color: "neutral",
        loop: false,
        scrollable: false,
        scrollShadow: false,
      });

      expect(get(model.activeTab)).toBe("custom-tab");
      expect(get(model.size)).toBe(3);
      expect(get(model.variant)).toBe("pill");
      expect(get(model.color)).toBe("neutral");
      expect(get(model.loop)).toBe(false);
      expect(get(model.scrollable)).toBe(false);
      expect(get(model.scrollShadow)).toBe(false);
    });

    it("should provide readonly stores", () => {
      const model = new TabsModel();

      // Stores should be readable
      expect(model.activeTab.subscribe).toBeDefined();
      expect(model.size.subscribe).toBeDefined();
      expect(model.variant.subscribe).toBeDefined();
      expect(model.color.subscribe).toBeDefined();
      expect(model.loop.subscribe).toBeDefined();

      // Stores should not have set method (readonly)
      /* eslint-disable @typescript-eslint/no-explicit-any */
      expect((model.activeTab as any).set).toBeUndefined();
      expect((model.size as any).set).toBeUndefined();
      expect((model.variant as any).set).toBeUndefined();
      expect((model.color as any).set).toBeUndefined();
      expect((model.loop as any).set).toBeUndefined();
      /* eslint-enable @typescript-eslint/no-explicit-any */
    });
  });

  describe("TabsController", () => {
    it("should update model values through controller", () => {
      const model = new TabsModel();
      const controller = new TabsController(model);

      controller.setActiveTab("new-tab");
      expect(get(model.activeTab)).toBe("new-tab");

      controller.setSize(3);
      expect(get(model.size)).toBe(3);

      controller.setVariant("pill");
      expect(get(model.variant)).toBe("pill");

      controller.setColor("neutral");
      expect(get(model.color)).toBe("neutral");

      controller.setLoop(false);
      expect(get(model.loop)).toBe(false);

      controller.setScrollable(false);
      expect(get(model.scrollable)).toBe(false);

      controller.setScrollShadow(false);
      expect(get(model.scrollShadow)).toBe(false);
    });

    it("should handle undefined activeTab when nullable is true", () => {
      const model = new TabsModel({ activeTab: "initial", nullable: true });
      const controller = new TabsController(model);

      expect(get(model.activeTab)).toBe("initial");

      controller.setActiveTab(undefined);
      expect(get(model.activeTab)).toBeUndefined();
    });

    it("should provide sliding background management methods", () => {
      const model = new TabsModel();
      const controller = new TabsController(model);

      // Methods should exist
      expect(typeof controller.registerTabList).toBe("function");
      expect(typeof controller.registerSlidingBackground).toBe("function");
      expect(typeof controller.updateSlidingBackground).toBe("function");
      expect(typeof controller.destroy).toBe("function");
    });

    it("should have reactive isResizing store", () => {
      const model = new TabsModel();

      // Should have isResizing store
      expect(model.isResizing).toBeDefined();
      expect(typeof model.isResizing.subscribe).toBe("function");
    });

    it("should handle destroy cleanup", () => {
      const model = new TabsModel();
      const controller = new TabsController(model);

      // Should not throw when called
      expect(() => controller.destroy()).not.toThrow();

      // Should be safe to call multiple times
      expect(() => controller.destroy()).not.toThrow();
    });

    it("should handle keyboard navigation", () => {
      const model = new TabsModel({ activeTab: "tab1", nullable: true });
      const controller = new TabsController(model);

      // Create mock tab elements
      const mockTabList = document.createElement("div");
      const tab1 = document.createElement("button");
      tab1.setAttribute("role", "tab");
      tab1.setAttribute("data-value", "tab1");
      const tab2 = document.createElement("button");
      tab2.setAttribute("role", "tab");
      tab2.setAttribute("data-value", "tab2");

      mockTabList.appendChild(tab1);
      mockTabList.appendChild(tab2);

      controller.registerTabList(mockTabList);
      controller.updateTabElements();

      // Test Escape key clears active tab
      const escapeEvent = new KeyboardEvent("keydown", { key: "Escape" });
      Object.defineProperty(escapeEvent, "target", { value: tab1 });

      const handled = controller.handleKeyboardNavigation(escapeEvent);
      expect(handled).toBe(true);

      // Check that active tab is now undefined
      let currentActiveTab: string | undefined;
      model.activeTab.subscribe((value) => (currentActiveTab = value))();
      expect(currentActiveTab).toBeUndefined();
    });

    it("should not clear active tab with Escape when nullable is false", () => {
      const model = new TabsModel({ nullable: false });
      const controller = new TabsController(model);

      // Create mock tab elements
      const tab1 = document.createElement("button");
      tab1.setAttribute("data-value", "tab1");
      tab1.setAttribute("role", "tab");

      const mockTabList = document.createElement("div");
      mockTabList.appendChild(tab1);

      controller.registerTabList(mockTabList);
      controller.updateTabElements();
      controller.setActiveTab("tab1");

      // Test Escape key does not clear active tab when nullable is false
      const escapeEvent = new KeyboardEvent("keydown", { key: "Escape" });
      Object.defineProperty(escapeEvent, "target", { value: tab1 });

      const handled = controller.handleKeyboardNavigation(escapeEvent);
      expect(handled).toBe(true);

      // Check that active tab remains unchanged
      let currentActiveTab: string | undefined;
      model.activeTab.subscribe((value) => (currentActiveTab = value))();
      expect(currentActiveTab).toBe("tab1");
    });

    it("should skip disabled tabs in keyboard navigation", () => {
      const model = new TabsModel();
      const controller = new TabsController(model);

      // Create mock tab elements with one disabled
      const tab1 = document.createElement("button");
      tab1.setAttribute("data-value", "tab1");
      tab1.setAttribute("role", "tab");

      const tab2 = document.createElement("button");
      tab2.setAttribute("data-value", "tab2");
      tab2.setAttribute("role", "tab");
      tab2.setAttribute("disabled", "true"); // Disabled tab

      const tab3 = document.createElement("button");
      tab3.setAttribute("data-value", "tab3");
      tab3.setAttribute("role", "tab");

      const mockTabList = document.createElement("div");
      mockTabList.appendChild(tab1);
      mockTabList.appendChild(tab2);
      mockTabList.appendChild(tab3);

      controller.registerTabList(mockTabList);
      controller.updateTabElements();

      // Test that getEnabledTabElements skips disabled tab
      const enabledTabs = controller["getEnabledTabElements"]();
      expect(enabledTabs).toHaveLength(2);
      expect(enabledTabs[0]).toBe(tab1);
      expect(enabledTabs[1]).toBe(tab3);
    });
  });

  describe("createTabGroupContext", () => {
    it("should create context with model and controller", () => {
      const context = createTabGroupContext();

      expect(context.model).toBeInstanceOf(TabsModel);
      expect(context.controller).toBeInstanceOf(TabsController);
    });

    it("should create context with custom options", () => {
      const context = createTabGroupContext({
        activeTab: "test-tab",
        size: 1,
        variant: "pill",
        color: "neutral",
        loop: false,
      });

      expect(get(context.model.activeTab)).toBe("test-tab");
      expect(get(context.model.size)).toBe(1);
      expect(get(context.model.variant)).toBe("pill");
      expect(get(context.model.color)).toBe("neutral");
      expect(get(context.model.loop)).toBe(false);
    });

    it("should allow controller to update model in context", () => {
      const context = createTabGroupContext({ activeTab: "initial" });

      expect(get(context.model.activeTab)).toBe("initial");

      context.controller.setActiveTab("updated");
      expect(get(context.model.activeTab)).toBe("updated");
    });
  });

  describe("Store reactivity", () => {
    it("should notify subscribers when values change", () => {
      const model = new TabsModel();
      const controller = new TabsController(model);

      let activeTabValue: string | undefined;

      const unsubscribeActiveTab = model.activeTab.subscribe((value) => {
        activeTabValue = value;
      });

      // Initial values
      expect(activeTabValue).toBeUndefined();

      // Update values
      controller.setActiveTab("new-tab");

      // Check updated values
      expect(activeTabValue).toBe("new-tab");

      // Cleanup
      unsubscribeActiveTab();
    });

    it("should handle null/undefined active tab values", () => {
      const model = new TabsModel({ activeTab: "initial-tab", nullable: true });
      const controller = new TabsController(model);

      // Start with a tab selected
      expect(get(model.activeTab)).toBe("initial-tab");

      // Set to undefined (no active tab)
      controller.setActiveTab(undefined);
      expect(get(model.activeTab)).toBeUndefined();

      // Set back to a specific tab
      controller.setActiveTab("new-tab");
      expect(get(model.activeTab)).toBe("new-tab");

      // Set to undefined again
      controller.setActiveTab(undefined);
      expect(get(model.activeTab)).toBeUndefined();
    });
  });
});
