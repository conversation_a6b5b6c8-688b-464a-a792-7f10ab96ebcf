<script lang="ts">
  import { createEventDispatcher } from "svelte";
  import CardAugment from "./CardAugment.svelte";
  import TextAugment from "./TextAugment.svelte";
  import { trapFocus } from "$common-webviews/src/common/actions/trapFocus";

  const dispatch = createEventDispatcher<{
    cancel: void;
    backdropClick: void;
    keydown: KeyboardEvent;
  }>();

  export let show = false;
  export let title = "";
  export let maxWidth = "400px";
  export let preventBackdropClose = false;
  export let preventEscapeClose = false;
  export let ariaLabelledBy = "modal-title";

  // Event handler props for Svelte 5 compatibility
  export let oncancel: (() => void) | undefined = undefined;
  export let onbackdropClick: (() => void) | undefined = undefined;
  export let onkeydown: ((event: KeyboardEvent) => void) | undefined = undefined;

  function handleBackdropClick() {
    if (!preventBackdropClose) {
      dispatch("cancel");
      oncancel?.();
    }
    dispatch("backdropClick");
    onbackdropClick?.();
  }

  function handleKeydown(event: KeyboardEvent) {
    if (event.key === "Escape" && !preventEscapeClose) {
      event.preventDefault();
      dispatch("cancel");
      oncancel?.();
    }
    dispatch("keydown", event);
    onkeydown?.(event);
  }
</script>

{#if show}
  <!-- Backdrop -->
  <div
    class="c-modal-backdrop"
    role="presentation"
    on:click={handleBackdropClick}
    on:keydown={handleKeydown}
  >
    <!-- Modal -->
    <div
      class="c-modal"
      role="dialog"
      aria-modal="true"
      aria-labelledby={ariaLabelledBy}
      style="max-width: {maxWidth}"
      tabindex="0"
      on:click|stopPropagation
      on:keydown|stopPropagation
      use:trapFocus={{ enabled: show }}
    >
      <CardAugment variant="soft" size={3}>
        <div class="c-modal-content">
          {#if title || $$slots.header}
            <div class="c-modal-header">
              {#if $$slots.header}
                <slot name="header" />
              {:else if title}
                <TextAugment id={ariaLabelledBy} size={3} weight="bold" color="primary"
                  >{title}</TextAugment
                >
              {/if}
            </div>
          {/if}

          {#if $$slots.body || $$slots.default}
            <div class="c-modal-body">
              <slot name="body">
                <slot />
              </slot>
            </div>
          {/if}

          {#if $$slots.footer}
            <div class="c-modal-footer">
              <slot name="footer" />
            </div>
          {/if}
        </div>
      </CardAugment>
    </div>
  </div>
{/if}

<style>
  .c-modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
  }

  .c-modal {
    width: 90%;
    max-height: 90vh;
    overflow: auto;
  }

  .c-modal-content {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-4);
  }

  .c-modal-header {
    text-align: left;
  }

  .c-modal-body {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-3);
  }

  .c-modal-footer {
    display: flex;
    gap: var(--ds-spacing-2);
    justify-content: flex-end;
  }
</style>
