import { render } from "@testing-library/svelte";
import AugmentFullSizeModal, { FULL_SIZE_OVERLAY_PORTAL_ID } from "./FullSizeOverlayAugment.svelte";
import { describe, it, expect, beforeEach, afterEach } from "vitest";
import { tick } from "svelte";

beforeEach(() => {
  // Reset document body styles before each test
  document.body.style.overflow = "";

  // Create the portal target element that the component expects
  const portalContainer = document.createElement("div");
  portalContainer.id = FULL_SIZE_OVERLAY_PORTAL_ID as string;
  document.body.appendChild(portalContainer);
});

afterEach(() => {
  // Clean up the portal container
  const portalContainer = document.getElementById(FULL_SIZE_OVERLAY_PORTAL_ID as string);
  if (portalContainer) {
    portalContainer.remove();
  }
});

describe("AugmentFullSizeModal", () => {
  it("renders when show is true", async () => {
    render(AugmentFullSizeModal, {
      props: { open: true },
    });

    // Wait for <PERSON>vel<PERSON> to complete all DOM updates
    await tick();

    // The dialog is rendered in the portal container, not in the component container
    const portalContainer = document.getElementById(FULL_SIZE_OVERLAY_PORTAL_ID as string);
    const modal = portalContainer?.querySelector("dialog");
    expect(modal).toBeTruthy();
    expect(modal).toHaveClass("c-augment-full-size-overlay");
  });

  it("does not render when show is false", () => {
    render(AugmentFullSizeModal, {
      props: { open: false },
    });
    // The dialog should not be rendered in the portal container
    const portalContainer = document.getElementById(FULL_SIZE_OVERLAY_PORTAL_ID as string);
    const modal = portalContainer?.querySelector("dialog");
    expect(modal).toBeNull();
  });
});
