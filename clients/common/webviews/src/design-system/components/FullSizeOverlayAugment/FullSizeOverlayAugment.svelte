<script lang="ts" context="module">
  export const FULL_SIZE_OVERLAY_PORTAL_ID = "augment-modals-container";
</script>

<script lang="ts">
  import { onMount, tick } from "svelte";
  import PortalAugment from "../PortalAugment.svelte";
  import { setFullSizeOverlayContext } from "./context";
  import { fade, fly } from "svelte/transition";
  import { trapFocus } from "$common-webviews/src/common/actions/trapFocus";

  export let open = false;
  export let onClose: (() => void) | undefined = undefined;

  // Animation direction prop
  export let animationDirection: "up" | "down" | "left" | "right" = "down";

  // Convert flyDirection to x/y values for the fly transition
  $: flyParams = (() => {
    switch (animationDirection) {
      case "up":
        return { x: 0, y: -5 };
      case "down":
        return { x: 0, y: 5 };
      case "left":
        return { x: -5, y: 0 };
      case "right":
        return { x: 5, y: 0 };
      default:
        return { x: 0, y: 5 };
    }
  })();

  // Native dialog element
  let dialogElement: HTMLDialogElement;

  export async function showModal() {
    open = true;
    await tick(); // Wait for dialogElement to be created
    if (dialogElement && typeof dialogElement.show === "function" && !dialogElement.open) {
      dialogElement.show(); // Use show() instead of showModal() to avoid top layer
    }
  }
  export async function closeModal() {
    open = false;
    if (dialogElement && dialogElement.open) {
      dialogElement.close();
    }
    onClose?.();
  }
  export function toggleModal() {
    if (open) {
      closeModal();
    } else {
      showModal();
    }
  }

  onMount(() => {
    if (open && dialogElement && !dialogElement.open) {
      dialogElement.show(); // Use show() instead of showModal()
    }
  });

  $: if (open) {
    showModal();
  } else {
    closeModal();
  }

  // Set the context for child components
  setFullSizeOverlayContext({
    open: showModal,
    close: closeModal,
    toggle: toggleModal,
  });
</script>

{#if open}
  <PortalAugment target="#{FULL_SIZE_OVERLAY_PORTAL_ID}">
    <dialog
      bind:this={dialogElement}
      class="c-augment-full-size-overlay"
      on:close={closeModal}
      in:fade={{ duration: 100 }}
      out:fade={{ duration: 200 }}
      use:trapFocus={{ enabled: open, restoreFocusOnClose: false }}
    >
      <div
        class="c-augment-full-size-overlay__content"
        transition:fly={{ duration: 200, ...flyParams }}
      >
        <slot />
      </div>
    </dialog>
  </PortalAugment>
{/if}

<style>
  .c-augment-full-size-overlay {
    /* Reset default dialog styles */
    border: none;
    padding: 0;
    margin: 0;
    max-width: none;
    max-height: none;

    /* Full-size positioning */
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    outline: none;

    /* Z-index to be below tooltips but above other content */
    z-index: var(--z-fullscreen-overlay);

    /* Make this match the sidebar color and text color */
    background-color: var(--user-theme-sidebar-background);
    color: var(--user-theme-sidebar-foreground);
    overflow: hidden;
    /* Descrete transition allows animation display property */
    transition:
      display 0.3s allow-discrete,
      opacity 0.3s ease;
  }

  .c-augment-full-size-overlay::backdrop {
    /* Hide the default backdrop since we're using full-size dialog */
    background: transparent;
  }

  .c-augment-full-size-overlay__content {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
  }
</style>
