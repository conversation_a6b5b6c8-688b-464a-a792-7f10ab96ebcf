import { getContext, setContext } from "svelte";

/**
 * Full size overlay context interface
 * Provides methods to show and close modals from within modal content
 */
export interface FullSizeOverlayContext {
  open: () => void;
  close: () => void;
  toggle: () => void;
}

/**
 * Context key for full size overlay
 */
const FULL_SIZE_OVERLAY_CONTEXT_KEY = Symbol("full-size-overlay");

/**
 * Set the full size overlay context for child components
 * @param context - The overlay context to set
 */
export function setFullSizeOverlayContext(context: FullSizeOverlayContext): void {
  setContext(FULL_SIZE_OVERLAY_CONTEXT_KEY, context);
}

/**
 * Get the full size overlay context from parent components
 * @returns The overlay context or undefined if not found
 */
export function getFullSizeOverlayContext() {
  return getContext<FullSizeOverlayContext>(FULL_SIZE_OVERLAY_CONTEXT_KEY);
}
