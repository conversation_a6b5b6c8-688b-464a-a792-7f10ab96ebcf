import type {
  But<PERSON>Size,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>tonColor,
  ButtonRadius,
} from "$common-webviews/src/design-system/_primitives/BaseButton.svelte";

export type SplitButtonDisableState =
  | boolean
  | {
      primaryDisabled: boolean;
      dropdownDisabled: boolean;
    };

export interface SplitButtonProps {
  size?: ButtonSize;
  variant?: ButtonVariant;
  color?: ButtonColor;
  radius?: ButtonRadius;
  disabled?: SplitButtonDisableState;
  highContrast?: boolean;
  showDropdown?: boolean;
}

export interface DropdownProps {
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  side?: "top" | "bottom" | "left" | "right";
  align?: "start" | "center" | "end";
  onClickOutside?: () => void;
  onEscapeKeyDown?: () => void;
}
