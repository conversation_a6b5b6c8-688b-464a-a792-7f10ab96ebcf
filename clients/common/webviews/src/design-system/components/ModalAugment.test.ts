import { render, fireEvent } from "@testing-library/svelte";
import ModalAugment from "./ModalAugment.svelte";
import { describe, it, expect, vi } from "vitest";

describe("ModalAugment", () => {
  it("renders when show is true", () => {
    const { getByRole } = render(ModalAugment, {
      props: { show: true, title: "Test Modal" },
    });
    const modal = getByRole("dialog");
    expect(modal).toBeTruthy();
  });

  it("does not render when show is false", () => {
    const { queryByRole } = render(ModalAugment, {
      props: { show: false, title: "Test Modal" },
    });
    const modal = queryByRole("dialog");
    expect(modal).toBeNull();
  });

  it("renders title correctly", () => {
    const { getByText } = render(ModalAugment, {
      props: { show: true, title: "Test Modal Title" },
    });
    expect(getByText("Test Modal Title")).toBeTruthy();
  });

  it("dispatches cancel event on backdrop click", async () => {
    const cancelHandler = vi.fn();

    const { container } = render(ModalAugment, {
      props: {
        show: true,
        title: "Test Modal",
        oncancel: cancelHandler,
      },
    });

    const backdrop = container.querySelector(".c-modal-backdrop");
    await fireEvent.click(backdrop!);

    expect(cancelHandler).toHaveBeenCalled();
  });

  it("dispatches cancel event on Escape key", async () => {
    const cancelHandler = vi.fn();

    const { container } = render(ModalAugment, {
      props: {
        show: true,
        title: "Test Modal",
        oncancel: cancelHandler,
      },
    });

    const backdrop = container.querySelector(".c-modal-backdrop");
    await fireEvent.keyDown(backdrop!, { key: "Escape" });

    expect(cancelHandler).toHaveBeenCalled();
  });

  it("prevents backdrop close when preventBackdropClose is true", async () => {
    const cancelHandler = vi.fn();

    const { container } = render(ModalAugment, {
      props: {
        show: true,
        title: "Test Modal",
        preventBackdropClose: true,
        oncancel: cancelHandler,
      },
    });

    const backdrop = container.querySelector(".c-modal-backdrop");
    await fireEvent.click(backdrop!);

    expect(cancelHandler).not.toHaveBeenCalled();
  });

  it("prevents escape close when preventEscapeClose is true", async () => {
    const cancelHandler = vi.fn();

    const { container } = render(ModalAugment, {
      props: {
        show: true,
        title: "Test Modal",
        preventEscapeClose: true,
        oncancel: cancelHandler,
      },
    });

    await fireEvent.keyDown(container, { key: "Escape" });

    expect(cancelHandler).not.toHaveBeenCalled();
  });
});
