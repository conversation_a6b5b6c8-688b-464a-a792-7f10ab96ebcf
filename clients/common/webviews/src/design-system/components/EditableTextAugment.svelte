<script lang="ts">
  import TextFieldAugment from "./TextFieldAugment.svelte";
  import { tick, createEventDispatcher } from "svelte";
  import type {
    TextFieldColor,
    TextFieldSize,
    TextFieldVariant,
  } from "../_primitives/BaseTextInput.svelte";

  // Event dispatcher
  const dispatch = createEventDispatcher<{
    startEdit: { value: string };
    acceptEdit: { oldValue: string; newValue: string };
    cancelEdit: { value: string };
  }>();

  // Props for component behavior
  export let value: string = "";
  export let disabled: boolean = false;
  export let placeholder: string = "";
  export let clickToEdit: boolean = false;

  // Props for styling (matching TextFieldAugment)
  export let variant: TextFieldVariant = "surface";
  export let size: TextFieldSize = 2;
  export let color: TextFieldColor | undefined = undefined;
  let className: string = "";
  export { className as class };

  // Expose variables for binding
  export let editing: boolean = false;

  // Internal state
  let draftValue: string = value;
  let textInput: HTMLInputElement | undefined;
  let containerElement: HTMLElement;

  // Editing lifecycle functions
  export async function startEdit(options?: { selectAll?: boolean }) {
    if (disabled || !textInput || editing) {
      return;
    }
    draftValue = value;
    dispatch("startEdit", { value });
    textInput.focus();

    // Wait a tick, then select the entire text
    if (options?.selectAll) {
      await tick();
      textInput?.select();
    }
    editing = true;
  }

  export function acceptEdit() {
    const oldValue = value;
    const newValue = draftValue.trim();
    if (oldValue === newValue) {
      cancelEdit();
      return;
    }

    value = newValue;
    dispatch("acceptEdit", { oldValue, newValue });
    if (document.activeElement === textInput) {
      textInput?.blur();
    }
    editing = false;
  }

  export function cancelEdit() {
    draftValue = value; // Reset to original value
    dispatch("cancelEdit", { value });
    if (document.activeElement === textInput) {
      textInput?.blur();
    }
    editing = false;
  }

  function onFocus() {
    if (clickToEdit && !disabled && !editing) {
      startEdit();
    }
  }

  function handleKeyDown(e: KeyboardEvent) {
    if (e.key === "Enter") {
      acceptEdit();
    } else if (e.key === "Escape") {
      cancelEdit();
    }
  }
</script>

<div bind:this={containerElement} class="c-editable-text {className}">
  <TextFieldAugment
    bind:textInput
    {size}
    {variant}
    {color}
    {placeholder}
    {...$$restProps}
    bind:value={draftValue}
    on:keydown={handleKeyDown}
    on:focus={onFocus}
    on:blur={() => acceptEdit()}
    on:keydown
    on:click
    on:blur
    on:focus
  />
</div>

<style>
  .c-editable-text {
    width: 100%;
    position: relative;

    /* If the input is not focused, show no styles */
    & :global(*:has(.c-base-text-input__input:where(:not(:focus)))) {
      --base-text-field-height: var(--ds-spacing-5);
      --base-text-field-box-shadow: none;
      background-image: none;
      background-color: transparent;
    }
  }

  .c-editable-text__display {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    cursor: default;
    padding: var(--ds-spacing-1) 0;
    min-height: calc(var(--ds-spacing-5) - var(--ds-spacing-2));
  }

  .c-editable-text__display--clickable {
    cursor: text;
  }

  .c-editable-text__display--empty {
    min-width: 4em;
  }

  .c-editable-text__placeholder {
    opacity: 0.6;
    font-style: italic;
  }
</style>
