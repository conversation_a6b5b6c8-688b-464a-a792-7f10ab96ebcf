# RichTextEditorAugment

This component is a wrapper around the TipTap/ProseMirror editor. It is used to provide a consistent API for the editor, and expose it
in a Svelte-y way for consumers of this common component.

See https://tiptap.dev/ and https://prosemirror.net/ for more details.

## Problem
TipTap (build on top of ProseMirror) is a very powerful editor, but comes with a few key issues.
The root of the issues generally stem from a combination of its wide (and relatively flat) API with little namespacing, its imperative structure, and its async initialization.
This means it is very difficult to use in a Svelte-y way, and requires a lot of boilerplate to use in a Svelte-y way.

Specifically, the API has the following characteristics:
- All extensions must already be defined + initialized on `Editor` construction
    - This means that we need to set up all our reactive state/flow/contexts *on initialization*
    - This pushes complexity of lifecycle management onto component consumers, rather than being hidden
- The editor API is extremely flat. It has a single higher-level entry point (`Editor`) from which *everything* is accessed
    - The lack of functional groupings, namespacing, etc. makes it very difficult to reason about the state of the editor, and what is responsible for what
    - Example: if you're working with @mentions, you can insert it by calling `editor.commands.insertContent({ type: "mention", attrs: { id: "123" } })`
    - Other than `type`, which is not type-safe, there is no way to know what the `attrs` should be, or what the `type` should be, and no indication that
        this command will insert a mention node into the document. It is very easy to hold this in the wrong way
- Use of callbacks to notify about state in combination with the flat API makes the state turn into callback hell
- Async initialization means no guarantees on effect of calling hooks/methods/etc. before initialization
    - This is a common problem with async initialization, but it is especially problematic with the flat API
    - Example: if you call `editor.commands.insertContent` it is not guaranteed to work

## Solution
The goal of this component is to provide a declarative, Svelte-y API for the TipTap/ProseMirror editor.
It should support the following things:
- Async initialization of the editor
- Modifying editor and extension options/parameters at runtime
- A declarative Svelte component + prop based API
- A composable, non-monolithic API that namespaces behavior to different components and plugins

## API Structure
The API is split into 3 main components:
- `Root`: The root component of the editor. This initializes the editor, sets up the Svelte context, and exposes hooks for plugins to register themselves
- `Content`: The content of the editor. This is the entrypoint for controlled content in the editor.
- `plugins/*`: Plugins for the editor
    - Each plugin is responsible for registering itself with the `Root` component using the `registerPlugin` method.
    - Each plugin is responsible for managing its own state relative to the TipTap/ProseMirror state.
    - Public APIs and interactions for the behavior encompassed by a plugin is *entirely owned* by the plugin.

The controllers/context in the `RichTextEditorAugment` have a few different categories of public APIs:
- TipTap hooks: exposed to TipTap by registering them in TipTap extension initialization
- Public controls: functions to control some aspect of the editor (e.g. focus, blur, scrolling, dropdown triggers, etc.)
- Public state: reactive Svelte stores that represent some aspect of the editor state. Generally only consumed
    internally by the `RichTextEditorAugment` components.
- Public events: These are publicly exposed for the purpose of subscribing to events from the editor. We generally
    expose hooks to `Public state (above)` through events like `onFocusChanged` or `onContentChanged` rather than
    exposing the reactive state directly

### Usage

```svelte
<script lang="ts">
  import RichTextEditorAugment from "@/design-system/components/RichTextEditorAugment";
  import MentionPlugin from "@/design-system/components/RichTextEditorAugment/plugins/MentionPlugin";
  import TooltipPlugin from "@/design-system/components/RichTextEditorAugment/plugins/TooltipPlugin";
  import Placeholder from "@/design-system/components/RichTextEditorAugment/plugins/Placeholder";
</script>

<RichTextEditorAugment.Root>
  <RichTextEditorAugment.Content content="Hello world!" />
  <Placeholder placeholder="Type something..." />
  <MentionPlugin>
    <!-- Use the ChipTooltip when you want to display hover-text for a mention chip -->
    <MentionPlugin.ChipTooltip>
      <svelte:fragment let:mentionable>
        {mentionable.name}
      </svelte:fragment>
    </MentionPlugin.ChipTooltip>
    <!-- Use the SuggestionsMenu when you want to display a dropdown menu for mentions -->
    <MentionPlugin.SuggestionsMenu.Root {suggestions} {onSelectItem}>
      <svelte:fragment>
        {#each suggestions as suggestion}
          <MentionPlugin.SuggestionsMenu.Item onSelect={() => onSelectItem(suggestion)}>
            {suggestion.label}
          </MentionPlugin.SuggestionsMenu.Item>
        {/each}
      </svelte:fragment>
    </MentionPlugin.SuggestionsMenu.Root>
  </MentionPlugin>
</RichTextEditorAugment.Root>
```

## Implementation Structure
A useful mental model for the implementation is the following:
- The API is component-based and declarative, so every logical piece of the system (@mentions, tooltips, etc.) will have their own component
- Each component set (such as @mentions, and the base editor) has its own controller (svelte context), which is used to coordinate between
  the Svelte components and the TipTap/ProseMirror editor
    - Events reported by TipTap/ProseMirror are captured by the controller, and forwarded to our own components
    - State/controls invoked on the controller are formatted and forwarded to TipTap/ProseMirror
- Each plugin, aside from the initial registration process, can do this entirely independently, mirroring the root
- More complex svelte contexts are broken up generally as follows:
    - The `index.ts` contains the main wiring/bootstrapping between TipTap/ProseMirror and all other parts of the context
    - Other parts of the contxt, which may store some of their own controls/reactive state, expose hooks to the main context
        such that it can track + register + maintain the lifecycle of the plugin
