<script lang="ts">
  /**
   * Test wrapper component for AskModeBadge plugin tests
   */
  import { type JSONContent } from "@tiptap/core";
  import RichTextEditorAugment from "..";
  import AskModeBadge from "../plugins/AskModeBadge/AskModeBadge.svelte";
  import type { ContentData } from "../types";

  export let isAskMode: boolean = false;
  export let onAskModeChange: ((isAskMode: boolean) => void) | undefined = undefined;
  export let onAskModeToggleComplete: (() => void) | undefined = undefined;
  export let content: string | JSONContent | JSONContent[] = "";

  let askModeBadgeComponent: AskModeBadge;

  // Expose methods for testing
  export const getAskModePrompt = () => askModeBadgeComponent?.getAskModePrompt();
  export const getIsAskMode = () => askModeBadgeComponent?.getIsAskMode();

  const onContentChanged = (_data: ContentData) => {
    // Handle content changes if needed
  };
</script>

<RichTextEditorAugment.Root>
  <AskModeBadge
    bind:this={askModeBadgeComponent}
    {isAskMode}
    {onAskModeChange}
    {onAskModeToggleComplete}
  />
  <RichTextEditorAugment.Content {content} {onContentChanged} />
</RichTextEditorAugment.Root>
