<script lang="ts">
  import type { IMentionable } from "../plugins/Mention/types";

  import RichTextEditorAugment from "..";
  import Mention from "../plugins/Mention";

  export let triggerCharacter: string = "@";
  export let allowedPrefixes: string[] | undefined = undefined;
  export let onQueryUpdate: (query: string | undefined) => void;
  export let mentionables: IMentionable[];
  export let onMentionItemsUpdated: (data: {
    added: IMentionable[];
    removed: IMentionable[];
    current: IMentionable[];
  }) => void;
</script>

<RichTextEditorAugment.Root>
  <Mention.Root {triggerCharacter} {allowedPrefixes} {onMentionItemsUpdated}>
    <Mention.ChipTooltip>
      <svelte:fragment let:mentionable>
        {mentionable.name}
      </svelte:fragment>
    </Mention.ChipTooltip>
    <Mention.Menu.Root {mentionables} {onQueryUpdate}>
      {#each mentionables as mentionable}
        <Mention.Menu.Item {mentionable}>
          <div data-testid="mention-menu-item">
            {mentionable.label}
          </div>
        </Mention.Menu.Item>
      {/each}
    </Mention.Menu.Root>
  </Mention.Root>
</RichTextEditorAugment.Root>
