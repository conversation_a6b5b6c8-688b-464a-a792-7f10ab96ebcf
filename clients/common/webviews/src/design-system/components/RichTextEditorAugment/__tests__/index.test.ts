import { render, waitFor } from "@testing-library/svelte";
import IndexTest from "./Index.test.svelte";
import { expect, describe, it, beforeEach, afterEach, vi } from "vitest";
import userEvent from "@testing-library/user-event";
import type { ContentData } from "../types";
import { EditorTestKit } from "./test-kit";

let component: ReturnType<typeof render<IndexTest>>;
const onContentChanged = vi.fn();

beforeEach(async () => {
  EditorTestKit.mockTipTapDOMFunctions();
  component = render(IndexTest, {
    content: "Initial content",
    onContentChanged,
  });
  const editor = await EditorTestKit.waitForRichTextInput(component);
  await waitFor(() => expect(editor.textContent).toBe("Initial content"));
});

afterEach(() => {
  component.unmount();
  vi.clearAllMocks();
});

describe("RichTextEditorAugment", () => {
  it("renders with initial content", async () => {
    const editor = await EditorTestKit.waitForRichTextInput(component);
    await waitFor(() => expect(editor.textContent).toBe("Initial content"));
  });

  it("updates content when prop changes", async () => {
    const editor = await EditorTestKit.waitForRichTextInput(component);
    await waitFor(() => expect(editor.textContent).toBe("Initial content"));

    await component.rerender({ content: "Updated content" });
    await waitFor(() => expect(editor.textContent).toBe("Updated content"));
  });

  it("calls onContentChanged on init", async () => {
    await waitFor(() =>
      expect(onContentChanged).toBeCalledWith(
        expect.objectContaining({
          rawText: "Initial content",
        }),
      ),
    );
  });

  it("calls onContentChanged when content changes", async () => {
    const editor = await EditorTestKit.waitForRichTextInput(component);
    await waitFor(() => expect(editor.textContent).toBe("Initial content"));

    editor.textContent = "Updated content";
    await waitFor(() =>
      expect(onContentChanged).toBeCalledWith(
        expect.objectContaining({
          rawText: "Updated content",
        }),
      ),
    );
  });

  it("calls onContentChanged when onContentChanged prop changes", async () => {
    await waitFor(() =>
      expect(onContentChanged).toBeCalledWith(
        expect.objectContaining({
          rawText: "Initial content",
        }),
      ),
    );

    const newOnContentChanged = vi.fn();
    await component.rerender({ onContentChanged: newOnContentChanged });
    await waitFor(() =>
      expect(newOnContentChanged).toBeCalledWith(
        expect.objectContaining({
          rawText: "Initial content",
        }),
      ),
    );
  });

  it("calls onContentChanged when content changes and onContentChanged prop changes", async () => {
    const editor = await EditorTestKit.waitForRichTextInput(component);
    await waitFor(() => expect(editor.textContent).toBe("Initial content"));

    // Original onContentChanged is called with updated content
    editor.textContent = "Updated content";
    await waitFor(() =>
      expect(onContentChanged).toBeCalledWith(
        expect.objectContaining({
          rawText: "Updated content",
        }),
      ),
    );

    // New onContentChanged is called with updated content
    const newOnContentChanged = vi.fn();
    await component.rerender({ onContentChanged: newOnContentChanged });
    await waitFor(() =>
      expect(newOnContentChanged).toBeCalledWith(
        expect.objectContaining({
          rawText: "Updated content",
        }),
      ),
    );
  });

  it("Setting content to an empty string clears the content", async () => {
    const editor = await EditorTestKit.waitForRichTextInput(component);
    await waitFor(() => expect(editor.textContent).toBe("Initial content"));

    await component.rerender({ content: "" });
    await waitFor(() => expect(editor.textContent).toBe(""));
  });

  it("onContentChanged is only called once per update when content changes on controlled prop", async () => {
    const editor = await EditorTestKit.waitForRichTextInput(component);

    // Fake "controlled" prop that echoes the reported change back in
    const controlledOnContentChanged = vi.fn((data: ContentData) => {
      // Update the content prop to match the new content
      void component.rerender({ content: data.richTextJsonRepr });
    });

    // Clear the content + set up "controlled" prop
    // SVELTE5_MIGRATION - AU-12015
    await component.rerender({ content: "" });
    await component.rerender({ onContentChanged: controlledOnContentChanged });
    await waitFor(() => expect(editor.textContent).toBe(""));

    // Focus initial editor
    await userEvent.click(editor);

    // Reset all mocks, make sure onContentChanged is only called once per keystroke
    vi.resetAllMocks();
    const newText = "Updated content";
    await userEvent.keyboard(newText);

    // Wait for new text to render
    await waitFor(() => expect(editor.textContent).toBe(newText));
    expect(controlledOnContentChanged).toBeCalledTimes(newText.length);
  });
});
