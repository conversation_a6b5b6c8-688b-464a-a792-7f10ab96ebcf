<script lang="ts">
  import Keybindings from "../plugins/Keybindings";
  import RichTextEditorAugment from "..";

  export let keybindings: Record<string, () => boolean> = {};

  let content = "Initial content";
</script>

<RichTextEditorAugment.Root>
  <RichTextEditorAugment.Content {content} />
  <Keybindings
    shortcuts={{
      // eslint-disable-next-line @typescript-eslint/naming-convention
      "Mod-Enter": () => {
        // When mod + enter is pressed, update the content to "Pressed Mod-Enter"
        content = "Pressed Mod-Enter";
        return true;
      },
      // eslint-disable-next-line @typescript-eslint/naming-convention
      "\\": () => {
        // When backslash is pressed, update the content to "Pressed Backslash"
        content = "Pressed Backslash";
        return true;
      },
    }}
  />
  <!-- A second one, to test duplicate functionality -->
  <Keybindings
    shortcuts={{
      // eslint-disable-next-line @typescript-eslint/naming-convention
      q: () => {
        // When backslash is pressed, update the content to "Pressed Space"
        content = "Pressed q";
        return true;
      },
      ...keybindings,
    }}
  />
</RichTextEditorAugment.Root>
