import { render, waitFor } from "@testing-library/svelte";
import KeybindingsTest from "./Keybindings.test.svelte";
import { expect, describe, it, beforeEach, afterEach, vi } from "vitest";
import userEvent from "@testing-library/user-event";
import { EditorTestKit } from "./test-kit";

let component: ReturnType<typeof render<KeybindingsTest>>;

beforeEach(async () => {
  EditorTestKit.mockTipTapDOMFunctions();
  component = render(KeybindingsTest);
  const editor = await EditorTestKit.waitForRichTextInput(component);
  await waitFor(() => expect(editor.textContent).toBe("Initial content"));
});

afterEach(() => {
  component.unmount();
  vi.clearAllMocks();
});

describe("Keybindings", () => {
  it("handles keybindings", async () => {
    const editor = await EditorTestKit.waitForRichTextInput(component);
    await waitFor(() => expect(editor.textContent).toBe("Initial content"));
    await userEvent.click(editor);
    await userEvent.keyboard("{Control>}{Enter}{/Control}");
    await waitFor(() => expect(editor.textContent).toBe("Pressed Mod-Enter"));
  });

  it("handles keybindings with no modifiers", async () => {
    const editor = await EditorTestKit.waitForRichTextInput(component);
    await waitFor(() => expect(editor.textContent).toBe("Initial content"));
    await userEvent.click(editor);
    await userEvent.keyboard("\\");
    await waitFor(() => expect(editor.textContent).toBe("Pressed Backslash"));
  });

  it("does not handle keybindings when the editor is not focused", async () => {
    const editor = await EditorTestKit.waitForRichTextInput(component);
    await waitFor(() => expect(editor.textContent).toBe("Initial content"));
    await userEvent.keyboard("{Control>}{Enter}{/Control}");
    await waitFor(() => expect(editor.textContent).toBe("Initial content"));
  });

  it("second keybinding plugin *also* works", async () => {
    const editor = await EditorTestKit.waitForRichTextInput(component);
    await waitFor(() => expect(editor.textContent).toBe("Initial content"));
    await userEvent.click(editor);
    await userEvent.keyboard("q");
    await waitFor(() => expect(editor.textContent).toBe("Pressed q"));
  });

  it("dynamic keybindings work", async () => {
    const editor = await EditorTestKit.waitForRichTextInput(component);
    await waitFor(() => expect(editor.textContent).toBe("Initial content"));
    await userEvent.click(editor);

    await component.rerender({
      keybindings: {
        // eslint-disable-next-line @typescript-eslint/naming-convention
        Backslash: () => {
          editor.textContent = "Pressed Backslash 2";
          return true;
        },
      },
    });
    await userEvent.keyboard("{Backslash}");
    await waitFor(() => expect(editor.textContent).toBe("Pressed Backslash 2"));
  });
});
