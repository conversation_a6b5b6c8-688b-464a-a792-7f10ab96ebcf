import { render, type RenderResult, waitFor } from "@testing-library/svelte";
import userEvent from "@testing-library/user-event";
import { expect, describe, test, afterEach, beforeEach, vi } from "vitest";

import TestAskModeBadge from "./AskModeBadge.test.svelte";
import { EditorTestKit } from "./test-kit";
import { ASK_MODE_PROMPT, AskModeBadgeController } from "../plugins/AskModeBadge/controller";

let component: RenderResult<TestAskModeBadge>;

const mockOnAskModeChange = vi.fn();
const mockOnToggleComplete = vi.fn();

// Base props objects for reuse
const baseProps = {
  onAskModeChange: mockOnAskModeChange,
  onAskModeToggleComplete: mockOnToggleComplete,
  content: "",
};

const propsWithAskModeOff = {
  ...baseProps,
  isAskMode: false,
};

const propsWithAskModeOn = {
  ...baseProps,
  isAskMode: true,
};

beforeEach(async () => {
  EditorTestKit.mockTipTapDOMFunctions();
  component = render(TestAskModeBadge, propsWithAskModeOff);
});

afterEach(() => {
  component.unmount();
  vi.clearAllMocks();
});

describe("AskModeBadge Plugin Integration", () => {
  test("should initialize with ask mode disabled", async () => {
    // Wait for the rich text editor to be ready
    await EditorTestKit.waitForRichTextInput(component);

    // The ask mode badge plugin should be registered and working
    expect(component.component.getIsAskMode()).toBe(false);
    expect(component.component.getAskModePrompt()).toBe(ASK_MODE_PROMPT);
  });

  test("should enable ask mode and insert badge into editor", async () => {
    // Wait for the rich text editor to be ready
    const editor = await EditorTestKit.waitForRichTextInput(component);

    // Enable ask mode
    await component.rerender(propsWithAskModeOn);

    // Wait for ask mode to be enabled
    await waitFor(() => {
      expect(component.component.getIsAskMode()).toBe(true);
    });

    expect(mockOnToggleComplete).toHaveBeenCalled();

    // The editor should contain the ask mode badge node
    // This tests the actual TipTap integration
    expect(editor).toBeDefined();
  });

  test("should toggle ask mode and manage badge in editor content", async () => {
    // Wait for the rich text editor to be ready
    const editor = await EditorTestKit.waitForRichTextInput(component);

    // Enable ask mode
    await component.rerender(propsWithAskModeOn);
    await waitFor(() => {
      expect(component.component.getIsAskMode()).toBe(true);
    });

    // Disable ask mode
    await component.rerender(propsWithAskModeOff);
    await waitFor(() => {
      expect(component.component.getIsAskMode()).toBe(false);
    });

    expect(mockOnToggleComplete).toHaveBeenCalledTimes(2);
    expect(editor).toBeDefined();
  });

  test("should maintain ask mode state with editor interactions", async () => {
    // Wait for the rich text editor to be ready
    const editor = await EditorTestKit.waitForRichTextInput(component);

    // Enable ask mode
    await component.rerender(propsWithAskModeOn);
    await waitFor(() => {
      expect(component.component.getIsAskMode()).toBe(true);
    });

    // Type some text in the editor
    await userEvent.type(editor, "Hello world");

    // Ask mode should still be enabled
    expect(component.component.getIsAskMode()).toBe(true);
    expect(component.component.getAskModePrompt()).toBe(ASK_MODE_PROMPT);
  });

  test("should handle ask mode with existing content", async () => {
    // Start with some content
    const propsWithContent = {
      ...propsWithAskModeOff,
      content: "Existing content",
    };
    await component.rerender(propsWithContent);

    // Wait for the rich text editor to be ready
    const editor = await EditorTestKit.waitForRichTextInput(component);

    // Enable ask mode
    const propsWithContentAndAskMode = {
      ...propsWithAskModeOn,
      content: "Existing content",
    };
    await component.rerender(propsWithContentAndAskMode);

    await waitFor(() => {
      expect(component.component.getIsAskMode()).toBe(true);
      expect(mockOnToggleComplete).toHaveBeenCalled();
    });

    // Content should be preserved and badge should be added
    expect(editor).toBeDefined();
  });

  test("should call onAskModeChange callback when ask mode changes", async () => {
    // Wait for the rich text editor to be ready
    await EditorTestKit.waitForRichTextInput(component);

    // Enable ask mode
    await component.rerender(propsWithAskModeOn);

    await waitFor(() => {
      expect(mockOnAskModeChange).toHaveBeenCalledWith(true);
    });

    // Disable ask mode
    await component.rerender(propsWithAskModeOff);

    await waitFor(() => {
      expect(mockOnAskModeChange).toHaveBeenCalledWith(false);
    });

    expect(mockOnAskModeChange).toHaveBeenCalledTimes(2);
  });

  test("should handle rapid state changes without errors", async () => {
    // Wait for the rich text editor to be ready
    await EditorTestKit.waitForRichTextInput(component);

    // Rapidly toggle ask mode
    await component.rerender(propsWithAskModeOn);
    await component.rerender(propsWithAskModeOff);
    await component.rerender(propsWithAskModeOn);
    await component.rerender(propsWithAskModeOff);

    // Final state should be stable
    await waitFor(() => {
      expect(component.component.getIsAskMode()).toBe(false);
    });
  });

  test("should maintain plugin state across editor focus changes", async () => {
    // Wait for the rich text editor to be ready
    const editor = await EditorTestKit.waitForRichTextInput(component);

    // Enable ask mode
    await component.rerender(propsWithAskModeOn);

    await waitFor(() => {
      expect(component.component.getIsAskMode()).toBe(true);
    });

    // Simulate focus/blur events
    editor.focus();
    editor.blur();
    editor.focus();

    // Ask mode should still be enabled
    expect(component.component.getIsAskMode()).toBe(true);
  });

  test("should handle editor content changes while in ask mode", async () => {
    // Wait for the rich text editor to be ready
    const editor = await EditorTestKit.waitForRichTextInput(component);

    // Enable ask mode
    await component.rerender(propsWithAskModeOn);

    await waitFor(() => {
      expect(component.component.getIsAskMode()).toBe(true);
    });

    // Add content to the editor
    await userEvent.type(editor, "New content added");

    // Ask mode should remain active
    expect(component.component.getIsAskMode()).toBe(true);
    expect(component.component.getAskModePrompt()).toBe(ASK_MODE_PROMPT);
  });

  test("should provide consistent API methods", async () => {
    // Wait for the rich text editor to be ready
    await EditorTestKit.waitForRichTextInput(component);

    // API methods should be available immediately
    expect(typeof component.component.getIsAskMode).toBe("function");
    expect(typeof component.component.getAskModePrompt).toBe("function");

    // Values should be consistent
    expect(component.component.getIsAskMode()).toBe(false);
    expect(component.component.getAskModePrompt()).toBe(ASK_MODE_PROMPT);

    // After enabling ask mode
    await component.rerender(propsWithAskModeOn);
    await waitFor(() => {
      expect(component.component.getIsAskMode()).toBe(true);
    });

    // API should still work consistently
    expect(component.component.getAskModePrompt()).toBe(ASK_MODE_PROMPT);
  });

  test("should handle component cleanup without errors", async () => {
    // Wait for the rich text editor to be ready
    await EditorTestKit.waitForRichTextInput(component);

    // Enable ask mode
    await component.rerender(propsWithAskModeOn);

    await waitFor(() => {
      expect(component.component.getIsAskMode()).toBe(true);
    });

    // Component cleanup should not throw errors
    expect(() => component.unmount()).not.toThrow();
  });

  test("should work with different content types", async () => {
    // Test with JSON content
    const jsonContent = {
      type: "doc",
      content: [
        {
          type: "paragraph",
          content: [{ type: "text", text: "JSON content" }],
        },
      ],
    };

    const propsWithJsonContent = {
      ...propsWithAskModeOff,
      content: jsonContent,
    };
    await component.rerender(propsWithJsonContent);

    // Wait for the rich text editor to be ready
    await EditorTestKit.waitForRichTextInput(component);

    // Enable ask mode
    const propsWithJsonContentAndAskMode = {
      ...propsWithAskModeOn,
      content: jsonContent,
    };
    await component.rerender(propsWithJsonContentAndAskMode);

    await waitFor(() => {
      expect(component.component.getIsAskMode()).toBe(true);
    });

    expect(component.component.getAskModePrompt()).toBe(ASK_MODE_PROMPT);
  });

  test("should detect ask mode badge removal and update state", async () => {
    // Wait for the rich text editor to be ready
    await EditorTestKit.waitForRichTextInput(component);

    // Enable ask mode to insert badge
    await component.rerender(propsWithAskModeOn);
    await waitFor(() => {
      expect(component.component.getIsAskMode()).toBe(true);
    });

    // Simulate badge removal by setting ask mode to false
    // The new implementation automatically syncs state with document content
    await component.rerender(propsWithAskModeOff);

    await waitFor(() => {
      expect(mockOnAskModeChange).toHaveBeenCalledWith(false);
    });
  });

  test("should detect ask mode from initial content", () => {
    // Test content without ask mode badge
    const contentWithoutBadge = {
      type: "doc",
      content: [
        {
          type: "paragraph",
          content: [{ type: "text", text: "Regular content" }],
        },
      ],
    };
    expect(AskModeBadgeController.detectAskModeInContent(contentWithoutBadge)).toBe(false);

    // Test content with ask mode badge
    const contentWithBadge = {
      type: "doc",
      content: [
        {
          type: "askMode",
          attrs: { prompt: ASK_MODE_PROMPT },
        },
        {
          type: "paragraph",
          content: [{ type: "text", text: "Content with badge" }],
        },
      ],
    };
    expect(AskModeBadgeController.detectAskModeInContent(contentWithBadge)).toBe(true);

    // Test nested content with ask mode badge
    const nestedContentWithBadge = {
      type: "doc",
      content: [
        {
          type: "paragraph",
          content: [
            { type: "askMode", attrs: { prompt: ASK_MODE_PROMPT } },
            { type: "text", text: " Some text" },
          ],
        },
      ],
    };
    expect(AskModeBadgeController.detectAskModeInContent(nestedContentWithBadge)).toBe(true);

    // Test invalid content
    expect(AskModeBadgeController.detectAskModeInContent(null)).toBe(false);
    expect(AskModeBadgeController.detectAskModeInContent(undefined)).toBe(false);
    expect(AskModeBadgeController.detectAskModeInContent("string")).toBe(false);
  });
});
