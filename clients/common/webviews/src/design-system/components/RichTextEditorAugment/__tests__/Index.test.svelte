<script lang="ts">
  import { type JSONContent } from "@tiptap/core";
  import RichTextEditorAugment from "..";
  import type { ContentData } from "../types";

  export let content: string | JSONContent | JSONContent[];
  export let onContentChanged: (data: ContentData) => void;
</script>

<RichTextEditorAugment.Root>
  <RichTextEditorAugment.Content {content} {onContentChanged} />
</RichTextEditorAugment.Root>
