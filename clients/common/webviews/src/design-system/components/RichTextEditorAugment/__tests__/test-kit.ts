import { type render, waitFor } from "@testing-library/svelte";
import { expect } from "vitest";
import type { SvelteComponent } from "svelte";

export class EditorTestKit {
  static waitForRichTextInput = async (
    c: ReturnType<typeof render<SvelteComponent>>,
  ): Promise<HTMLElement> => {
    await waitFor(
      () => expect(c.getByTestId("design-system-rich-text-editor-tiptap")).toBeInTheDocument(),
      {
        timeout: 3000,
      },
    );
    return c.getByTestId("design-system-rich-text-editor-tiptap");
  };

  static waitForAllRichTextInputs = async (
    c: ReturnType<typeof render<SvelteComponent>>,
    expectedCount?: number,
  ): Promise<HTMLElement[]> => {
    await waitFor(
      () => {
        if (expectedCount === undefined) {
          expect(c.getAllByTestId("design-system-rich-text-editor-tiptap")).not.toHaveLength(0);
        } else {
          expect(c.getAllByTestId("design-system-rich-text-editor-tiptap")).toHaveLength(
            expectedCount,
          );
        }
      },
      {
        timeout: 3000,
      },
    );
    return c.getAllByTestId("design-system-rich-text-editor-tiptap");
  };

  /**
   * This is a workaround for the following issue:
   * https://github.com/ueberdosis/tiptap/discussions/4008#discussioncomment-7623655
   *
   * We plug in fake layout functions such that we can mock things effectively
   */
  static mockTipTapDOMFunctions = () => {
    function getBoundingClientRect(): DOMRect {
      const rec = {
        x: 0,
        y: 0,
        bottom: 0,
        height: 0,
        left: 0,
        right: 0,
        top: 0,
        width: 0,
      };
      return { ...rec, toJSON: () => rec };
    }

    // Inline class
    class FakeDOMRectList extends Array<DOMRect> implements DOMRectList {
      item(index: number): DOMRect | null {
        return this[index];
      }
    }

    document.elementFromPoint = (): null => null;
    HTMLElement.prototype.getBoundingClientRect = getBoundingClientRect;
    HTMLElement.prototype.getClientRects = (): DOMRectList => new FakeDOMRectList();
    Range.prototype.getBoundingClientRect = getBoundingClientRect;
    Range.prototype.getClientRects = (): DOMRectList => new FakeDOMRectList();
  };
}
