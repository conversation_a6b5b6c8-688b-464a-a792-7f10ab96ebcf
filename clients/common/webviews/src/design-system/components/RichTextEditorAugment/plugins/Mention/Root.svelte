<script lang="ts" generics="TOption extends IMentionable">
  /**
   * Root component for the Mention plugin in RichTextEditorAugment
   *
   * This component initializes and manages the mention functionality within the rich text editor.
   * It sets up the necessary context, registers the plugin with the editor, and handles cleanup.
   *
   * Key responsibilities:
   * 1. Initialize the MentionPluginContext
   * 2. Set the context for child components
   * 3. Register the plugin with the editor
   * 4. Handle cleanup on component destruction
   *
   * Events API:
   * - onMentionItemsUpdated: Called when mention items are added or removed from the editor
   *
   * Controls API:
   * - insertMention: Inserts a mention into the editor
   *
   */

  import { getContext, onDestroy, setContext } from "svelte";
  import { RichTextEditorContext } from "../../context";
  import { MentionPluginContext } from "./context";
  import type { IMentionable, MentionsUpdatedCallbackFn } from "./types";

  /**
   * Callback function triggered when mention items are updated.
   * @param added - Array of mention items that were added.
   * @param removed - Array of mention items that were removed.
   */
  export let onMentionItemsUpdated: MentionsUpdatedCallbackFn<TOption> | undefined = undefined;
  export let triggerCharacter: string | undefined = undefined;
  export let allowedPrefixes: string[] | undefined = undefined;
  export let pluginId: string | undefined = undefined;
  export let renderText: ((mentionable: TOption) => string) | undefined = undefined;

  /**
   * Function to insert a mention into the editor.
   * @param mention - The mention item to be inserted.
   */
  export const insertMention = (mention: TOption) => {
    mentionContext.insertMentionNode({
      id: mention.id,
      label: mention.label,
      data: mention,
    });
  };

  // Initialize + set mention context
  const mentionContext = new MentionPluginContext<TOption>({
    onMentionItemsUpdated: (data) => onMentionItemsUpdated?.(data),
    triggerCharacter,
    allowedPrefixes,
    pluginId,
    renderText,
  });
  setContext<MentionPluginContext<TOption>>(MentionPluginContext.CONTEXT_KEY, mentionContext);

  // Register the context as a plugin with the editor
  const editorContext = getContext<RichTextEditorContext>(RichTextEditorContext.CONTEXT_KEY);
  const unregister = editorContext.pluginManager.registerPlugin(mentionContext);

  // Schedule the cleanup function to be called when the component is destroyed.
  onDestroy(unregister);
</script>

<slot />
