import { expect, describe, test, beforeEach, afterEach } from "vitest";
import { Chip<PERSON><PERSON>roller } from "./chip-controller";
import type { IMentionable } from "../types";
import type { DOMOutputSpec, Node as ProseMirrorNode } from "prosemirror-model";
import type { MentionOptions } from "@tiptap/extension-mention";
import userEvent from "@testing-library/user-event";
import { get } from "svelte/store";
import type { Editor } from "@tiptap/core";

interface TestMentionable extends IMentionable {}
const testPluginId: string = "test-plugin-name";
let fakeDomRoot: HTMLElement;
let chipController: ChipController<TestMentionable>;

beforeEach(() => {
  fakeDomRoot = document.createElement("div");
  chipController = new ChipController<TestMentionable>(testPluginId);
  chipController.onCreate({ view: { dom: fakeDomRoot } } as unknown as Editor);
});

afterEach(() => {
  chipController.onDispose();
});

describe("ChipController", () => {
  test("createMentionChip throws if node is not a mention", () => {
    const node = {
      type: { name: "not-a-mention" },
      attrs: { label: "test-label", data: { id: "test-id" } },
    } as unknown as ProseMirrorNode;
    // eslint-disable-next-line @typescript-eslint/naming-convention
    expect(() => chipController.createMentionChip({ options: {} as MentionOptions, node })).toThrow(
      "Expected a mention node",
    );
  });

  test("createMentionChip", () => {
    // eslint-disable-next-line @typescript-eslint/naming-convention
    const options = { HTMLAttributes: { class: "test-class" } } as unknown as MentionOptions;
    const node = {
      type: { name: testPluginId },
      attrs: { label: "test-label", data: { id: "test-id" } },
    } as unknown as ProseMirrorNode;
    const chip = chipController.createMentionChip({ options, node });

    if (expectToBeHTMLSpan(chip)) {
      expect(chip.innerText).toBe("@test-label");
      // Make sure passed in options are applied
      expect(chip.classList.contains("test-class")).toBe(true);
    }
  });

  test("hover sets tooltip data", async () => {
    /* eslint-disable @typescript-eslint/naming-convention */
    const chip = chipController.createMentionChip({
      options: { HTMLAttributes: { class: "test-class" } } as unknown as MentionOptions,
      node: {
        type: { name: testPluginId },
        attrs: { label: "test-label", data: { id: "test-id" } },
      } as unknown as ProseMirrorNode,
    });
    /* eslint-enable @typescript-eslint/naming-convention */
    // Attach chip to DOM
    fakeDomRoot.appendChild(chip as HTMLSpanElement);

    // When we start, there should be no tooltip data
    expect(get(chipController.tooltipData)).toBeUndefined();

    // After we hover, there should be tooltip data
    if (expectToBeHTMLSpan(chip)) {
      await userEvent.hover(chip);
      expect(get(chipController.tooltipData)).not.toBeUndefined();
    }
  });

  test("unhover clears tooltip data", async () => {
    /* eslint-disable @typescript-eslint/naming-convention */
    const chip = chipController.createMentionChip({
      options: { HTMLAttributes: { class: "test-class" } } as unknown as MentionOptions,
      node: {
        type: { name: testPluginId },
        attrs: { label: "test-label", data: { id: "test-id" } },
      } as unknown as ProseMirrorNode,
    });
    /* eslint-enable @typescript-eslint/naming-convention */
    // Attach chip to DOM
    fakeDomRoot.appendChild(chip as HTMLSpanElement);

    // After we hover, there should be tooltip data
    if (expectToBeHTMLSpan(chip)) {
      await userEvent.hover(chip);
      expect(get(chipController.tooltipData)).not.toBeUndefined();

      // After we unhover, there should be no tooltip data
      await userEvent.unhover(chip);
      expect(get(chipController.tooltipData)).toBeUndefined();
    }
  });

  test("chip HTML text to be prefixed with the trigger character", () => {
    /* eslint-disable @typescript-eslint/naming-convention */
    const chip = chipController.createMentionChip({
      options: {
        suggestion: { char: "#" },
        HTMLAttributes: { class: "test-class" },
      } as unknown as MentionOptions,
      node: {
        type: { name: testPluginId },
        attrs: { label: "test-label", data: { id: "test-id" } },
      } as unknown as ProseMirrorNode,
    });
    /* eslint-enable @typescript-eslint/naming-convention */
    // Attach chip to DOM
    fakeDomRoot.appendChild(chip as HTMLSpanElement);

    if (expectToBeHTMLSpan(chip)) {
      expect(chip.innerText).toBe("#test-label");
    }
  });

  test("multiple chips use the same underlying tooltip store", async () => {
    const createTestChip = (idx: number = 0) =>
      chipController.createMentionChip({
        // eslint-disable-next-line @typescript-eslint/naming-convention
        options: { HTMLAttributes: { class: "test-class" } } as unknown as MentionOptions,
        node: {
          type: { name: testPluginId },
          attrs: {
            label: `test-label-${idx}`,
            id: `test-id-${idx}`,
            data: { id: `test-id-${idx}`, label: `test-label-${idx}` },
          },
        } as unknown as ProseMirrorNode,
      });

    const chip1 = createTestChip(0);
    const chip2 = createTestChip(1);
    fakeDomRoot.appendChild(chip1 as HTMLSpanElement);
    fakeDomRoot.appendChild(chip2 as HTMLSpanElement);

    if (expectToBeHTMLSpan(chip1) && expectToBeHTMLSpan(chip2)) {
      // Hovering over each chip should update the tooltip data
      await userEvent.hover(chip1);
      expect(get(chipController.tooltipData)).not.toBeUndefined();
      expect(get(chipController.tooltipData)?.data).toEqual({
        id: "test-id-0",
        label: "test-label-0",
      });

      // Unhovering should clear the tooltip data
      await userEvent.unhover(chip1);
      expect(get(chipController.tooltipData)).toBeUndefined();

      // Hovering over the second chip should update the tooltip data again, with the data from the second chip
      await userEvent.hover(chip2);
      expect(get(chipController.tooltipData)).not.toBeUndefined();
      expect(get(chipController.tooltipData)?.data).toEqual({
        id: "test-id-1",
        label: "test-label-1",
      });

      // Unhovering should clear the tooltip data again
      await userEvent.unhover(chip2);
      expect(get(chipController.tooltipData)).toBeUndefined();
    }
  });

  test("createMentionChip with empty label", () => {
    const chip = chipController.createMentionChip({
      // eslint-disable-next-line @typescript-eslint/naming-convention
      options: { HTMLAttributes: { class: "test-class" } } as unknown as MentionOptions,
      node: {
        type: { name: testPluginId },
        attrs: { label: "", data: { id: "test-id" } },
      } as unknown as ProseMirrorNode,
    });

    if (expectToBeHTMLSpan(chip)) {
      expect(chip.innerText).toBe("@");
    }
  });

  // This tests that createMentionChip always adds specified HTMLAttributes onto
  // the actual DOM node. This is important for cross-compatibility with other
  // plugins/extensions.
  test("createMentionChip preserves additional HTML attributes", () => {
    const chip = chipController.createMentionChip({
      // eslint-disable-next-line @typescript-eslint/naming-convention
      options: {
        // eslint-disable-next-line @typescript-eslint/naming-convention
        HTMLAttributes: {
          class: "test-class",
          // eslint-disable-next-line @typescript-eslint/naming-convention
          "data-testid": "mention-chip",
          title: "test-title",
        },
      } as unknown as MentionOptions,
      node: {
        type: { name: testPluginId },
        attrs: { label: "test-label", data: { id: "test-id" } },
      } as unknown as ProseMirrorNode,
    });

    if (expectToBeHTMLSpan(chip)) {
      expect(chip.getAttribute("data-testid")).toBe("mention-chip");
      expect(chip.getAttribute("title")).toBe("test-title");
    }
  });
});

function expectToBeHTMLSpan(chip: DOMOutputSpec): chip is HTMLSpanElement {
  expect(chip).toBeInstanceOf(HTMLSpanElement);
  return chip instanceof HTMLSpanElement;
}
