import { describe, it, expect } from "vitest";
import {
  getFileExtension,
  getFileExtensionLowercase,
  isImageFile,
  fileNameToImageFormat,
  ImageFormatType,
  getMimeType,
  isKnownBinary,
  isTooLargeForDiff,
  MAX_FILE_SIZE_FOR_DIFF_BYTES,
} from "./file-type-utils";

describe("file-type-utils", () => {
  describe("getFileExtension", () => {
    it("should extract uppercase file extension", () => {
      expect(getFileExtension("test.jpg")).toBe("JPG");
      expect(getFileExtension("document.pdf")).toBe("PDF");
      expect(getFileExtension("image.PNG")).toBe("PNG");
    });

    it("should return empty string for files without extension", () => {
      expect(getFileExtension("filename")).toBe("");
      expect(getFileExtension("filename.")).toBe("");
    });

    it("should handle edge cases", () => {
      expect(getFileExtension("")).toBe("");
      expect(getFileExtension(".")).toBe("");
      expect(getFileExtension("..")).toBe("");
    });
  });

  describe("getFileExtensionLowercase", () => {
    it("should extract lowercase file extension", () => {
      expect(getFileExtensionLowercase("test.JPG")).toBe("jpg");
      expect(getFileExtensionLowercase("document.PDF")).toBe("pdf");
      expect(getFileExtensionLowercase("image.png")).toBe("png");
    });

    it("should return empty string for files without extension", () => {
      expect(getFileExtensionLowercase("filename")).toBe("");
    });
  });

  describe("isImageFile", () => {
    it("should return true for common image formats", () => {
      expect(isImageFile("photo.jpg")).toBe(true);
      expect(isImageFile("photo.jpeg")).toBe(true);
      expect(isImageFile("photo.png")).toBe(true);
      expect(isImageFile("photo.gif")).toBe(true);
      expect(isImageFile("photo.webp")).toBe(true);
      expect(isImageFile("photo.svg")).toBe(true);
      expect(isImageFile("photo.bmp")).toBe(true);
      expect(isImageFile("photo.ico")).toBe(true);
    });

    it("should be case insensitive", () => {
      expect(isImageFile("photo.JPG")).toBe(true);
      expect(isImageFile("photo.PNG")).toBe(true);
      expect(isImageFile("photo.GIF")).toBe(true);
    });

    it("should return false for non-image formats", () => {
      expect(isImageFile("document.pdf")).toBe(false);
      expect(isImageFile("document.txt")).toBe(false);
      expect(isImageFile("document.doc")).toBe(false);
      expect(isImageFile("filename")).toBe(false);
    });
  });

  describe("fileNameToImageFormat", () => {
    it("should return correct ImageFormatType for supported formats", () => {
      expect(fileNameToImageFormat("photo.jpg")).toBe(ImageFormatType.JPEG);
      expect(fileNameToImageFormat("photo.jpeg")).toBe(ImageFormatType.JPEG);
      expect(fileNameToImageFormat("photo.png")).toBe(ImageFormatType.PNG);
      expect(fileNameToImageFormat("photo.gif")).toBe(ImageFormatType.GIF);
      expect(fileNameToImageFormat("photo.webp")).toBe(ImageFormatType.WEBP);
    });

    it("should be case insensitive", () => {
      expect(fileNameToImageFormat("photo.JPG")).toBe(ImageFormatType.JPEG);
      expect(fileNameToImageFormat("photo.PNG")).toBe(ImageFormatType.PNG);
    });

    it("should return IMAGE_FORMAT_UNSPECIFIED for unsupported formats", () => {
      expect(fileNameToImageFormat("photo.svg")).toBe(ImageFormatType.IMAGE_FORMAT_UNSPECIFIED);
      expect(fileNameToImageFormat("document.pdf")).toBe(ImageFormatType.IMAGE_FORMAT_UNSPECIFIED);
      expect(fileNameToImageFormat("filename")).toBe(ImageFormatType.IMAGE_FORMAT_UNSPECIFIED);
    });
  });

  describe("getMimeType", () => {
    it("should return correct MIME types for image formats", () => {
      expect(getMimeType("photo.png")).toBe("image/png");
      expect(getMimeType("photo.jpg")).toBe("image/jpeg");
      expect(getMimeType("photo.jpeg")).toBe("image/jpeg");
      expect(getMimeType("photo.gif")).toBe("image/gif");
      expect(getMimeType("photo.svg")).toBe("image/svg+xml");
      expect(getMimeType("photo.webp")).toBe("image/webp");
      expect(getMimeType("photo.bmp")).toBe("image/bmp");
      expect(getMimeType("photo.ico")).toBe("image/x-icon");
    });

    it("should return generic binary type for unknown formats", () => {
      expect(getMimeType("document.pdf")).toBe("application/octet-stream");
      expect(getMimeType("file.txt")).toBe("application/octet-stream");
    });

    it("should be case insensitive", () => {
      expect(getMimeType("photo.PNG")).toBe("image/png");
      expect(getMimeType("photo.JPG")).toBe("image/jpeg");
    });
  });

  describe("isKnownBinary", () => {
    it("should return true for binary file types", () => {
      expect(isKnownBinary("archive.zip")).toBe(true);
      expect(isKnownBinary("document.pdf")).toBe(true);
      expect(isKnownBinary("program.exe")).toBe(true);
      expect(isKnownBinary("library.dll")).toBe(true);
      expect(isKnownBinary("video.mp4")).toBe(true);
    });

    it("should return false for image files", () => {
      expect(isKnownBinary("photo.jpg")).toBe(false);
      expect(isKnownBinary("photo.png")).toBe(false);
      expect(isKnownBinary("photo.gif")).toBe(false);
    });

    it("should return false for unknown file types", () => {
      expect(isKnownBinary("file.txt")).toBe(false);
      expect(isKnownBinary("script.js")).toBe(false);
      expect(isKnownBinary("style.css")).toBe(false);
    });
  });

  describe("isTooLargeForDiff", () => {
    it("should return false for files under the threshold", () => {
      expect(isTooLargeForDiff(1024)).toBe(false); // 1KB
      expect(isTooLargeForDiff(512 * 1024)).toBe(false); // 512KB
      expect(isTooLargeForDiff(MAX_FILE_SIZE_FOR_DIFF_BYTES - 1)).toBe(false);
    });

    it("should return true for files over the threshold", () => {
      expect(isTooLargeForDiff(MAX_FILE_SIZE_FOR_DIFF_BYTES + 1)).toBe(true);
      expect(isTooLargeForDiff(2 * 1024 * 1024)).toBe(true); // 2MB
    });

    it("should return false for files exactly at the threshold", () => {
      expect(isTooLargeForDiff(MAX_FILE_SIZE_FOR_DIFF_BYTES)).toBe(false);
    });
  });
});
