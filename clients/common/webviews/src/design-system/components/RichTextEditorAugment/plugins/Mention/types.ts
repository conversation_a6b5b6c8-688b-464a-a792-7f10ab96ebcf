import type { Editor, Range } from "@tiptap/core";
import { type SuggestionProps } from "@tiptap/suggestion";

export interface IMentionable {
  label: string;
  id: string;
  name?: string;
}

export interface IMentionOptionData<TOption extends IMentionable> {
  id: string;
  label: string;
  data: TOption;
}

export interface CommandArgs<TOption extends IMentionable> {
  editor: Editor;
  range: Range;
  props: TOption;
}

export interface IMentionableMenuData<TOption extends IMentionable> {
  // Trigger positioning DOM rect for the dropdown menu
  referenceClientRect: DOMRect;
  // All the props reported by the mentionable plugin
  tiptapExtensionProps: SuggestionProps<TOption>;
  // Active index for the dropdown menu
  activeIdx: number;
}

export interface IMentionableMenuOptions<TOption extends IMentionable> {
  onSelectMentionable?: (mentionable: TOption) => boolean;
}

export interface IMentionChipTooltipData<TOption extends IMentionable> {
  data: TOption;
  anchorElement: HTMLElement;
}

/**
 * The data passed to the onMentionItemsUpdated callback
 */
export type MentionsUpdatedData<TOption extends IMentionable> = {
  added: TOption[];
  removed: TOption[];
  current: TOption[]; // All the current mentions in the editor
};

/**
 * The callback function for the onMentionItemsUpdated event
 */
export type MentionsUpdatedCallbackFn<TOption extends IMentionable> = (
  data: MentionsUpdatedData<TOption>,
) => void;

export interface IMentionContextOptions<TOption extends IMentionable> {
  pluginId?: string;
  // This plugin key is used in case an API consumer wants to use multiple
  // mention plugins in the same editor. This is useful to support things like
  // @mentions and /commands in the same editor.
  triggerCharacter?: string;
  allowedPrefixes?: string[];
  onMentionItemsUpdated?: MentionsUpdatedCallbackFn<TOption>;
  // A function that computes the user-visible string representation of a mentionable item
  renderText?: (mentionable: TOption) => string;
}
