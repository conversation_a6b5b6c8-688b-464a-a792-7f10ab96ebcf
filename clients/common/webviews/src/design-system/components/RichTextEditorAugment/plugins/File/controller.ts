import { type J<PERSON><PERSON><PERSON><PERSON>, type Editor, type Node } from "@tiptap/core";
import Image, { type ImageOptions } from "@tiptap/extension-image";

import type { IRichTextEditorPlugin } from "../../types";
import { FileError, validateFile, isImage, type ValidateFileOptions } from "./errors";
import { Plugin, PluginKey } from "prosemirror-state";
import FileRenderer from "./FileRenderer.svelte";
import { type Attrs, type Node as ProseMirrorNode } from "prosemirror-model";
import { readImageAsBlob } from "./read-image-util";
import { ImageRendererMode } from "./image-renderer-mode";
import { mount, unmount } from "svelte";

/**
 * If this plugin is being used, add this command to the TipTap type system.
 */
declare module "@tiptap/core" {
  interface Commands<ReturnType> {
    insertFilesIntoEditor: {
      insertFilesIntoEditor?: (files: File[]) => ReturnType;
    };
  }
}
declare module "@tiptap/extension-image" {
  interface ImageOptions {
    defaultRenderMode?: ImageRendererMode;
    renderMode?: ImageRendererMode;
    isLoading?: boolean;
  }
}

export type FileControllerOptions = {
  changeImageMode?: (updatedContent?: JSONContent) => void;
  maxImages?: number;
  /**
   * Save the image locally
   *
   * @returns a key to retrieve the saved image
   */
  saveImage?: (file: File) => Promise<string>;
  /**
   * Save an attachment locally
   *
   * @param file - The file to save
   * @returns a key to retrieve the saved attachment
   */
  saveAttachment?: (file: File) => Promise<string>;
  /**
   * Delete the image from storage
   *
   * @param imageId - The key of the image to delete
   */
  deleteImage?: (imageId: string) => Promise<void>;
  /**
   * Render the image in the editor
   *
   * @param file - The key to retrieve the image
   *
   * @returns base64-encoded data URL or undefined if not found
   */
  renderImage?: (file: string) => Promise<string | undefined>;
  /** Whether the editor is editable */
  isEditable?: () => boolean;
  /**
   * Callback for validation errors
   *
   * @param message - The error message to display
   */
  onValidationError?: (message: string) => void;
} & ValidateFileOptions;

/**
 * FileController class
 *
 * This class implements the IRichTextEditorPlugin interface and provides functionality
 * for handling image drops and pastes in a rich text editor.
 */
export class FileController implements IRichTextEditorPlugin {
  // eslint-disable-next-line @typescript-eslint/naming-convention
  private static IMAGE_PASTE_HANDLER_PLUGIN_KEY = new PluginKey(
    "augment-prosemirror-image-paste-handler",
  );
  private _tipTapExtension: Node<ImageOptions>;
  private _editor: Editor | undefined;

  /**
   * Constructor for the FileController class.
   * Initializes the TipTap extension for handling image drops and pastes.
   */
  constructor(private _options: FileControllerOptions) {
    this._tipTapExtension = this._createTipTapExtension();
  }

  private _createTipTapExtension = (): Node<ImageOptions> => {
    const registerEditor = this.registerEditor;
    const unregisterEditor = this.unregisterEditor;
    const insertFilesIntoEditor = this.insertFilesIntoEditor;

    return Image.extend({
      // TODO: change this to a File node instead of Image
      // Temporarily overwriting the image node name to "file" to allow for handling other file types
      name: "file",

      addOptions() {
        return {
          ...this.parent?.(),
          allowBase64: true,
          preserveOnSplit: false,
          defaultRenderMode: ImageRendererMode.collapsed,
        };
      },

      addAttributes() {
        return {
          ...this.parent?.(),
          renderMode: {
            default: ImageRendererMode.collapsed,
          },
          title: { default: "" },
          isLoading: { default: false },
          fileSizeBytes: { default: 0 },
          mimeType: { default: "" },
        };
      },

      onCreate() {
        this.parent?.();
        registerEditor(this.editor);
      },

      onDestroy() {
        this.parent?.();
        unregisterEditor();
      },

      addCommands() {
        return {
          ...this.parent?.(),
          insertFilesIntoEditor:
            (files: File[]) =>
            ({ chain }: { chain: Editor["chain"] }) => {
              return chain()
                .command(() => !!insertFilesIntoEditor(files))
                .focus("end")
                .run();
            },
        };
      },

      addNodeView: () => this._createNodeView(),
      addProseMirrorPlugins() {
        return [
          ...(this.parent?.() ?? []),
          new Plugin({
            key: FileController.IMAGE_PASTE_HANDLER_PLUGIN_KEY,
            props: {
              handlePaste: (_, event) => {
                if (!event.clipboardData?.files?.length) {
                  return false;
                }
                const result = insertFilesIntoEditor([...event.clipboardData.files]);
                if (result.okFiles.length > 0) {
                  event.preventDefault();
                  return true;
                }
                return false;
              },
            },
          }),
        ];
      },
    });
  };

  private _createNodeView = () => {
    return ({
      node,
    }: {
      node: ProseMirrorNode;
    }): {
      dom: HTMLElement;
      destroy: () => void;
      update: (node: ProseMirrorNode) => boolean;
    } => {
      const target = document.createElement("div");
      const mountComponent = (
        node: ProseMirrorNode, 
        currentComponent: ReturnType<typeof mount> | undefined,
      ) => {
        if (currentComponent) {
          unmount(currentComponent);
        }
        return mount(FileRenderer, {
          target,
          props: {
            title: String(node.attrs.title),
            src: String(node.attrs.src),
            loadImage: this._options?.renderImage ?? (() => Promise.resolve(undefined)),
            isLoading: !!node.attrs.isLoading,
            fileSizeBytes: Number(node.attrs.fileSizeBytes) || 0,
            isOverLimit: this._calculateIsOverLimit(
              Number(node.attrs.fileSizeBytes) || 0,
              String(node.attrs.mimeType || "").startsWith("image/"),
            ),
            removeImage: async () => {
              if (!this._editor) {
                return;
              }

              // Delete the image from storage if deleteImage is provided
              try {
                if (this._options?.deleteImage) {
                  await this._options.deleteImage(String(node.attrs.src));
                }
              } catch (error) {
                console.error("Failed to delete image:", error);
              }

              // Remove the node from the editor
              this._editor
                .chain()
                .command(({ tr, state }) => {
                  state.doc.descendants((currNode, pos) => {
                    if (node === currNode) {
                      tr.delete(pos, pos + currNode.nodeSize);
                      return false;
                    }
                  });
                  return true;
                })
                .focus("end")
                .run();
            },
            isEditable: this._options?.isEditable ?? (() => true),
          },
        });
      };

      let component = mountComponent(node, undefined);

      return {
        dom: target,
        destroy: () => {
          // eslint-disable-next-line @typescript-eslint/no-floating-promises
          void unmount(component);
        },
        update: (updatedNode: ProseMirrorNode): boolean => {
          if (updatedNode.type.name !== "file") {
            return false;
          }
          node = updatedNode;
          component = mountComponent(updatedNode, component);

          return true;
        },
      };
    };
  };

  public registerEditor = (editor: Editor): void => {
    this._editor = editor;
  };

  public unregisterEditor = (): void => {
    this._editor = undefined;
  };

  /**
   * Counts the number of image nodes in the editor.
   * @returns {number | undefined} The count of image nodes, or undefined if the editor is not available.
   */
  private _getImageNodeCount = (): number | undefined => {
    if (!this._editor) {
      return undefined;
    }

    let imageCount = 0;
    this._editor.state.doc.descendants((node) => {
      if (node.type.name === "file") {
        imageCount++;
      }
    });

    return imageCount;
  };

  /**
   * Calculates whether a file size exceeds the maximum allowed size
   * @param fileSizeBytes - The file size in bytes
   * @param isImage - Whether the file is an image
   * @returns true if the file size exceeds the limit, false otherwise
   */
  private _calculateIsOverLimit = (fileSizeBytes: number, isImage: boolean = true): boolean => {
    if (fileSizeBytes === 0) {
      return false;
    }

    const maxSizeKb = isImage ? this._options?.maxImageSizeKb : this._options?.maxTextSizeKb;
    if (!maxSizeKb) {
      return false;
    }

    const maxSizeBytes = maxSizeKb * 1024;
    return fileSizeBytes > maxSizeBytes;
  };

  public updateOptions = (options: FileControllerOptions) => {
    this._options = { ...options };
  };

  /**
   * Validates an array of files and separates them into valid images and errors.
   * @param files - Array of files to validate
   * @returns Object containing valid images and validation errors
   */
  private validateFiles(files: File[]): {
    validFiles: File[];
    errors: FileError[];
  } {
    const errors: FileError[] = [];
    const validImages: File[] = [];

    for (const file of files) {
      const result = validateFile(file, this._options);
      if (result instanceof FileError) {
        errors.push(result);
      } else {
        validImages.push(result);
      }
    }

    return { validFiles: validImages, errors };
  }

  /**
   * Process a single file
   *
   * For images: Scale the image down, save the scaled image, and replace the loading node with the actual image
   * For non-images: Save the file directly without scaling
   *
   * @param file - The file to process
   */
  private async _processImage(file: File, uniqId: string): Promise<void> {
    try {
      let processedFile: File;

      if (isImage(file)) {
        // Scale the image
        const scale = await readImageAsBlob(file);
        if (!scale || !this._editor) {
          return;
        }
        // Create a new file with the scaled image
        processedFile = new File([scale], file.name, { type: file.type });
      } else {
        // For non-image files, use the original file
        processedFile = file;
      }

      // Save the file using the appropriate method based on file type
      let fileKey: string;
      if (isImage(file)) {
        fileKey = (await this._options?.saveImage?.(processedFile)) ?? processedFile.name;
      } else {
        fileKey = (await this._options?.saveAttachment?.(processedFile)) ?? processedFile.name;
      }

      // Update the placeholder loading node with the actual file
      this._updateImageNode(uniqId, {
        src: fileKey,
        title: processedFile.name,
        isLoading: false,
        fileSizeBytes: processedFile.size,
        mimeType: processedFile.type,
      });
    } catch (error) {
      console.error("Could not process image: ", error);

      // Remove the placeholder on error
      if (this._editor) {
        this._removeImageNode(uniqId);
      }

      throw error;
    }
  }

  /**
   * Inserts multiple files into the editor, handling validation and limits.
   *
   * @param files - An array of File objects representing the files to insert.
   *
   * @returns Object containing errors encountered and successfully inserted files.
   */
  public insertFilesIntoEditor = (
    files: File[],
  ): {
    errors: FileError[];
    okFiles: File[];
  } => {
    // Validate files and check limits
    const { validFiles, errors } = this.validateFiles(files);
    const insertionErrors: FileError[] = [];

    // Show consolidated error notification for validation failures
    if (errors.length > 0 && this._options?.onValidationError) {
      if (errors.length === 1) {
        this._options.onValidationError(errors[0].message);
      } else {
        const fileNames = errors
          .map((error) => error.files.map((f) => f.name))
          .flatMap((names) => names);
        this._options.onValidationError(`Errors when attaching files: ${fileNames.toString()}`);
      }
    }

    // Insert placeholder files with loading state immediately
    for (const file of validFiles) {
      if (!this._editor) {
        continue;
      }

      // Insert a placeholder with loading state
      const uniqId = this._insertLoadingImage(this._editor, file.name);

      // Process the file asynchronously
      this._processImage(file, uniqId).catch((error) => {
        const fileError = new FileError([file], String(error));
        insertionErrors.push(fileError);

        // Show error notification for processing failures
        if (this._options?.onValidationError) {
          this._options.onValidationError(fileError.message);
        }
      });
    }

    return {
      errors: [...errors, ...insertionErrors],
      okFiles: validFiles,
    };
  };

  /**
   * Getter for the TipTap extension
   * @returns {Node<ImageOptions>} The configured TipTap extension
   */
  public get tipTapExtension(): Node<ImageOptions> {
    return this._tipTapExtension;
  }

  /**
   * Find an image node by its src
   *
   * @param id - The ID to search for
   *
   * @returns Object with node and position if found
   */
  private _findImageNodeBySrc(id: string): { node: ProseMirrorNode; pos: number } | null {
    if (!this._editor || !id) {
      return null;
    }

    let result = null;
    this._editor.state.doc.descendants((node, pos) => {
      if (node.type.name === "file" && node.attrs.src === id) {
        result = { node, pos };
        return false;
      }
      return true;
    });

    return result;
  }

  /**
   * Inserts an image node with loading state
   *
   * @param editor - The editor instance
   * @param imageName - The name of the image
   */
  private _insertLoadingImage(editor: Editor, imageName: string) {
    const { from } = editor.state.selection;
    // Generate a unique ID for this image instance
    const uniqueId = `${imageName}-${Date.now()}-${Math.random().toString(36).substring(2)}`;

    editor
      .chain()
      .command(({ tr }) => {
        // Insert the image at the cursor position
        tr.insert(from, [
          editor.schema.nodes.file.create({
            src: uniqueId, // Use the unique ID as src for the loading state
            originalName: imageName, // Store the original name for later use
            title: imageName,
            renderMode: ImageRendererMode.collapsed,
            isLoading: true,
          }),
          editor.schema.nodes.paragraph.create({ text: "" }),
        ]);
        return true;
      })
      .run();

    return uniqueId; // Return the unique ID for tracking
  }

  /**
   * Updates an existing image node with new attributes
   *
   * @param imageName - The name of the loading image
   * @param updates - The attributes to update
   */
  private _updateImageNode(imageName: string, updates: Attrs): void {
    if (!this._editor) {
      return;
    }

    const found = this._findImageNodeBySrc(imageName);
    if (found) {
      this._editor
        .chain()
        .command(({ tr }) => {
          tr.setNodeMarkup(found.pos, undefined, { ...found.node.attrs, ...updates });
          return true;
        })
        .run();
    }
  }

  /**
   * Removes an image node from the editor
   *
   * @param imageName - The name of the image that is loading
   */
  private _removeImageNode(imageName: string): void {
    if (!this._editor) {
      return;
    }

    const found = this._findImageNodeBySrc(imageName);
    if (found) {
      this._editor
        .chain()
        .command(({ tr }) => {
          tr.delete(found.pos, found.pos + found.node.nodeSize);
          return true;
        })
        .run();
    }
  }

  /**
   * Checks if the rich text editor JSON contains any image nodes that are still loading
   *
   * @param json - The rich text editor JSON content to check
   *
   * @returns true if the JSON contains any image nodes that are still loading
   */
  static hasLoadingImages(json: JSONContent | null | undefined): boolean {
    if (!json) {
      return false;
    }

    const traverse = (node: JSONContent): boolean => {
      if (!node) {
        return false;
      }
      if (node.type === "file" && node.attrs?.isLoading) {
        return true;
      }
      if (node.content) {
        if (!Array.isArray(node.content)) {
          return false;
        }
        for (const contentNode of node.content) {
          if (traverse(contentNode)) {
            return true;
          }
        }
      }
      return false;
    };

    return traverse(json);
  }
}
