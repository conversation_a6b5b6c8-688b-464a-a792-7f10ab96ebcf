# Mention Plugin for RichTextEditorAugment

The Mention plugin for RichTextEditorAugment provides a way to insert mentions into the editor. It supports both chip tooltips and a dropdown menu for selecting mentions.
This plugin is designed to be used with the `RichTextEditorAugment` component.
It is an example of one of the more complex plugins that can be built on top of the `RichTextEditorAugment` component.

## Structure

The Mention plugin is structured into several conceptual sub-systems:
- The root component and context, which manage the lifecycle of the plugin
- The chip tooltip, which is a singleton controlling the hover tooltip on mention chips
- The mentionable menu, which is a dropdown menu for selecting mentions

### Root Component and Context
The root component and context manage the lifecycle of the plugin. The context is used to coordinate between the Svelte components and the TipTap/ProseMirror editor. It also exposes the public root-level API for the plugin.

### Chip Tooltip
The chip tooltip shows a tooltip when a mention chip is hovered. It is a singleton per editor instance, and is responsible for rendering the tooltip with the appropriate data.

### Mentionable Menu
The mentionable menu is a dropdown menu for selecting mentions when a user is searching for a mentionable. It is responsible for displaying the dropdown menu when appropriate, providing the necessary data for slotted components to render mentionable items, and handling user interactions with the mentionables menu.

## Usage

To use the Mention plugin, you need to import it and include it within the `RichTextEditorAugment` component.

```svelte
<RichTextEditorAugment.Root>
  <RichTextEditorAugment.Content content="Hello world!" />
  <Placeholder placeholder="Type something..." />
  <Mention.Root triggerCharacter="@">
    <!-- Use the ChipTooltip when you want to display hover-text for a mention chip -->
    <Mention.ChipTooltip>
      <svelte:fragment let:mentionable>
        {mentionable.name}
      </svelte:fragment>
    </Mention.ChipTooltip>
    <!-- Use the SuggestionsMenu when you want to display a dropdown menu for mentions.
     Only useful with an editable editor -->
    <Mention.Menu.Root {mentionables} {onQueryUpdate}>
      {#each mentionables as mentionable}
        <Mention.Menu.Item {mentionable}>
          {mentionable.label}
        </Mention.Menu.Item>
      {/each}
    </Mention.Menu.Root>
  </Mention.Root>
</RichTextEditorAugment.Root>
```
