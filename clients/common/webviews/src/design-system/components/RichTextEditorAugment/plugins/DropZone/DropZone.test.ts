import { describe, it, expect, beforeEach, vi } from "vitest";
import { render, fireEvent } from "@testing-library/svelte";
import { isDraggingStore } from "./state";
import TestWrapper from "./DropZone.test.svelte";

describe("DropZone", () => {
  beforeEach(() => {
    // Reset the store before each test
    isDraggingStore.set(false);
  });

  it("should sync dragging state across multiple instances", async () => {
    // Render two instances of DropZone
    const dropZone1 = render(TestWrapper);
    const dropZone2 = render(TestWrapper);

    // Get the dropzone elements
    const dropZoneElement1 = dropZone1.container.querySelector(".c-rich-text-editor-dropzone");
    const dropZoneElement2 = dropZone2.container.querySelector(".c-rich-text-editor-dropzone");

    // Create a mock file for the drag event
    const file = new File(["test content"], "test.png", { type: "image/png" });

    // Create a mock DataTransfer object with files
    const dataTransfer = {
      files: [file],
      types: ["Files"],
      setData: vi.fn(),
      getData: vi.fn(),
    };

    // Simulate drag enter on window with files
    await fireEvent.dragEnter(window, { dataTransfer });

    // Check that both dropzones show active state
    expect(dropZoneElement1?.classList.contains("is-active")).toBe(true);
    expect(dropZoneElement2?.classList.contains("is-active")).toBe(true);

    // Simulate drop on first dropzone with the same dataTransfer object
    await fireEvent.drop(dropZoneElement1!, { dataTransfer });

    // Check that both dropzones are inactive
    expect(dropZoneElement1?.classList.contains("is-active")).toBe(false);
    expect(dropZoneElement2?.classList.contains("is-active")).toBe(false);
  });
});
