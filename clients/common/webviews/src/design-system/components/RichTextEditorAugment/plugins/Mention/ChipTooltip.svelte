<script lang="ts" generics="TOption extends IMentionable">
  /**
   * ChipTooltip Component
   *
   * This component is a *singleton* per editor instance. It is responsible for rendering the tooltip
   * for mention chips in the rich text editor.
   *
   * Note that multiple mention chips in the editor will use the same instance of this tooltip.
   * The way this works is each chip, when a user interacts with it, reports the data to the singleton.
   * The singleton then updates its state, and this tooltip reactively renders the updated data.
   *
   * Data flow for singleton:
   * - [ChipController] Tooltip data is undefined
   * - [Chip<PERSON>ontroller -> User] User hovers a chip component (created by `<PERSON><PERSON>ontroll<PERSON>`)
   * - [User -> ChipController] ChipController sets the tooltip data for the singleton
   * - [ChipController -> This component] This component renders the tooltip with the data
   *
   * Key responsibilities:
   * 1. Display a tooltip when a mention chip is hovered
   * 2. Provide the necessary data for slotted components to render tooltip content
   * 3. Handle user interactions with the tooltip
   *
   * Slot Props API:
   * - mentionable: The mentionable item being displayed in the tooltip
   *
   * Controls API:
   * - requestOpen: Opens the tooltip
   * - requestClose: Closes the tooltip
   */
  import { getContext } from "svelte";
  import type { IMentionable } from "./types";
  import { MentionPluginContext } from "./context";
  import TextTooltipAugment from "../../../TextTooltipAugment.svelte";

  // SVELTE5_MIGRATION - AU-11878
  // Define props interface for Svelte 5 compatibility
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  interface $$Props {
    children?: any;
  }

  const mentionContext = getContext<MentionPluginContext<TOption>>(
    MentionPluginContext.CONTEXT_KEY,
  );

  const chipTooltipData = mentionContext.chipController.tooltipData;

  // Bind controls to the tooltip
  let tooltip: TextTooltipAugment | undefined = undefined;
  // React to changes in the tooltip data. If we have data, show the tooltip. Otherwise, hide it.
  $: if ($chipTooltipData === undefined) {
    tooltip?.requestClose();
  } else {
    tooltip?.requestOpen();
  }
</script>

<!-- triggerOn must be `[]` to disable the default tooltip behavior, as we manage the tooltip as a singleton -->
<TextTooltipAugment
  bind:this={tooltip}
  triggerOn={[]}
  referenceClientRect={$chipTooltipData?.anchorElement.getBoundingClientRect()}
>
  <!--
    Expose a default slot that plugs into the TextTooltipAugment content slot and exposes
    the mentionable data to the slot.

    Usage:

    <MentionPlugin.ChipTooltip>
      <svelte:fragment let:mentionable>  <- API consumer accesses the mentionable data here
        {mentionable.name}  <- API consumer specifies how to render the mentionable data here
      </svelte:fragment>
    </MentionPlugin.ChipTooltip>
  -->
  <svelte:fragment slot="content">
    <!-- Use a named slot to more ergonomically bind let:mentionable directly to the slot -->
    {#if $chipTooltipData && $$slots.mentionable}
      <slot name="mentionable" mentionable={$chipTooltipData.data} />
    {/if}
    <!-- Use a default slot for a simpler API -->
    {#if $chipTooltipData && $$slots.default}
      <slot mentionable={$chipTooltipData.data} />
    {/if}
  </svelte:fragment>
</TextTooltipAugment>

<style>
  /* Style mention chips in the rich text editor */
  :global(.tiptap .c-mention-chip) {
    display: inline-block;
    padding: 0 var(--ds-spacing-1);
    margin: var(--ds-spacing-1) 0;
    border-radius: var(--augment-border-radius);
    background-color: var(--ds-color-neutral-9);
    color: var(--ds-white-contrast);

    /* Enable full line wrapping */
    word-break: break-all;
    white-space: pre-wrap;

    /* Hover on chips should not be a text selector */
    cursor: default;
  }
</style>
