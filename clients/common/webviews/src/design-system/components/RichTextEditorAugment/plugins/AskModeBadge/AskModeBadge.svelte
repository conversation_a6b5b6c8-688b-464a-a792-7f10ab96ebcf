<script lang="ts">
  /**
   * AskModeBadge plugin component for RichTextEditorAugment
   *
   * This component manages the ask mode badge functionality within the rich text editor.
   * It creates a TipTap node that represents the ask mode badge and automatically
   * inserts/removes it when ask mode is toggled.
   *
   * The badge appears as a node in the editor content, making it part of the message
   * and visible in both the editor and rendered user messages.
   */
  import { getContext, onDestroy } from "svelte";
  import { RichTextEditorContext } from "../../context";
  import { AskModeBadgeController } from "./controller";

  // Props
  export let isAskMode: boolean = false;
  export let onAskModeChange: ((isAskMode: boolean) => void) | undefined = undefined;
  export let onAskModeToggleComplete: (() => void) | undefined = undefined;

  // Initialize the controller
  const askModeBadgeController = new AskModeBadgeController();

  $: if (onAskModeChange) {
    askModeBadgeController.setOnAskModeChange(onAskModeChange);
  }

  // React to ask mode changes from parent
  $: askModeBadgeController.setAskMode(isAskMode, onAskModeToggleComplete);

  // Register the plugin with the editor context
  const editorContext = getContext<RichTextEditorContext>(RichTextEditorContext.CONTEXT_KEY);
  const unregister = editorContext.pluginManager.registerPlugin(askModeBadgeController);

  // Public API methods for parent components
  export const getAskModePrompt = () => askModeBadgeController.askModePrompt;
  export const getIsAskMode = () => askModeBadgeController.isAskMode;

  // Cleanup on component destruction
  onDestroy(unregister);
</script>

<!-- No template needed - the badge is rendered as a TipTap node in the editor content -->
