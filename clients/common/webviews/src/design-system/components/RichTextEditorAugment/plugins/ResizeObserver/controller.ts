import { type Editor, Extension } from "@tiptap/core";
import type { IRichTextEditorPlugin } from "../../types";

export interface ResizeObserverOptions {
  onResize?: (height: number) => void;
  getCollapsedHeight?: () => number;
}

export class ResizeObserverController implements IRichTextEditorPlugin {
  /* eslint-disable @typescript-eslint/naming-convention */
  private static _sequenceId = 0;
  private static RESIZE_OBSERVER_PLUGIN_KEY_BASE = "augment-resize-observer-plugin-{}";
  private static _getSequenceId = () => ResizeObserverController._sequenceId++;
  private static _getNextPluginId = () => {
    const seqId = ResizeObserverController._getSequenceId().toString();
    return ResizeObserverController.RESIZE_OBSERVER_PLUGIN_KEY_BASE.replace("{}", seqId);
  };
  /* eslint-enable @typescript-eslint/naming-convention */

  private _tipTapExtension: Extension;
  private _resizeObserver: ResizeObserver | undefined;

  constructor(private _options: ResizeObserverOptions) {
    const name = ResizeObserverController._getNextPluginId();
    const setResizeObserver = this._setResizeObserver;
    const clearResizeObserver = this._clearResizeObserver;
    const checkHeight = this._checkHeight;

    this._tipTapExtension = Extension.create({
      name,
      onCreate: function (this: { editor: Editor }) {
        const proseMirrorElement = this.editor.view?.dom;
        if (!proseMirrorElement) {
          return;
        }

        setResizeObserver(this.editor);
        this.editor.on("destroy", clearResizeObserver);
      },
      onUpdate: function (this: { editor: Editor }) {
        const proseMirrorElement = this.editor.view?.dom;
        if (!proseMirrorElement) {
          return;
        }

        // Check height on content update
        checkHeight(proseMirrorElement);
      },
      onDestroy: () => {
        this._resizeObserver?.disconnect();
        this._resizeObserver = undefined;
      },
    });
  }

  private _checkHeight = (element: HTMLElement) => {
    const height = element.getBoundingClientRect().height;
    this._options.onResize?.(height);
  };

  private _setResizeObserver = (editor: Editor) => {
    const proseMirrorElement = editor.view?.dom;
    if (!proseMirrorElement) {
      return;
    }

    this._resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        this._checkHeight(entry.target as HTMLElement);
      }
    });

    this._resizeObserver.observe(proseMirrorElement);
    // Trigger onResize with initial height
    this._checkHeight(proseMirrorElement);
  };

  private _clearResizeObserver = () => {
    this._resizeObserver?.disconnect();
    this._resizeObserver = undefined;
  };

  // ==== Public API ====
  public updateOptions = (opts: Partial<ResizeObserverOptions>) => {
    this._options = { ...this._options, ...opts };
  };

  public get tipTapExtension() {
    return this._tipTapExtension;
  }
}
