import { expect, describe, test, vi, beforeEach, afterEach } from "vitest";
import {
  createOneShotMutationObserver,
  attachOneShotMountObserver,
  attachOneShotUnmountObserver,
} from "./utils";

let container: HTMLElement;
let target: HTMLElement;

beforeEach(() => {
  container = document.createElement("div");
  target = document.createElement("div");
  vi.useFakeTimers();
});

afterEach(() => {
  vi.restoreAllMocks();
});

describe("Mutation Observer Utils", () => {
  describe("createOneShotMutationObserver", () => {
    test("calls callback when shouldTrigger returns true", async () => {
      const callback = vi.fn();
      const shouldTrigger = vi.fn(() => true);

      createOneShotMutationObserver(container, {
        shouldTrigger,
        callback,
      });

      // Simulate a mutation
      container.appendChild(target);
      // Wait for the next tick
      await vi.advanceTimersByTimeAsync(1);

      expect(shouldTrigger).toHaveBeenCalledOnce();
      expect(callback).toHaveBeenCalledOnce();
    });

    test("disconnects after timeout", async () => {
      const callback = vi.fn();
      const disconnect = vi.fn();
      vi.spyOn(window, "MutationObserver").mockImplementation(
        () => ({ observe: vi.fn(), disconnect }) as unknown as MutationObserver,
      );

      expect(disconnect).not.toHaveBeenCalled();
      createOneShotMutationObserver(container, {
        shouldTrigger: () => true,
        callback,
        timeout: 1000,
      });
      // Wait for the timeout to elapse
      await vi.advanceTimersByTimeAsync(1000);
      expect(disconnect).toHaveBeenCalled();
    });

    test("returns disconnect function", () => {
      const disconnect = vi.fn();
      vi.spyOn(window, "MutationObserver").mockImplementation(
        () => ({ observe: vi.fn(), disconnect }) as unknown as MutationObserver,
      );
      expect(disconnect).not.toHaveBeenCalled();

      const dispose = createOneShotMutationObserver(container, {
        shouldTrigger: () => true,
        callback: vi.fn(),
      });

      dispose();
      expect(disconnect).toHaveBeenCalled();
    });

    test("uses custom mutationObserverInit options", () => {
      const observe = vi.fn();
      vi.spyOn(window, "MutationObserver").mockImplementation(
        () => ({ observe, disconnect: vi.fn() }) as unknown as MutationObserver,
      );

      // Override `childList`
      const customInit = { attributes: true, characterData: true, childList: false };
      createOneShotMutationObserver(container, {
        shouldTrigger: () => true,
        callback: vi.fn(),
        mutationObserverInit: customInit,
      });

      expect(observe).toHaveBeenCalledWith(
        container,
        expect.objectContaining({
          subtree: true,
          ...customInit,
        }),
      );
    });
  });

  describe("attachOneShotMountObserver", () => {
    test("triggers callback when target is mounted", async () => {
      const callback = vi.fn();

      attachOneShotMountObserver(container, target, callback);
      container.appendChild(target);

      await vi.advanceTimersByTimeAsync(1);
      expect(callback).toHaveBeenCalledTimes(1);
    });

    test("does not trigger callback for other elements", async () => {
      const callback = vi.fn();
      const otherElement = document.createElement("div");

      attachOneShotMountObserver(container, target, callback);
      container.appendChild(otherElement);

      await vi.advanceTimersByTimeAsync(1);
      expect(callback).not.toHaveBeenCalled();
    });
  });

  describe("attachOneShotUnmountObserver", () => {
    test("triggers callback when target is unmounted", async () => {
      const callback = vi.fn();
      container.appendChild(target);

      attachOneShotUnmountObserver(container, target, callback);
      container.removeChild(target);

      await vi.advanceTimersByTimeAsync(1);
      expect(callback).toHaveBeenCalledTimes(1);
    });

    test("does not trigger callback for other elements", async () => {
      const callback = vi.fn();
      const otherElement = document.createElement("div");
      container.appendChild(otherElement);

      attachOneShotUnmountObserver(container, target, callback);
      container.removeChild(otherElement);

      await vi.advanceTimersByTimeAsync(1);
      expect(callback).not.toHaveBeenCalled();
    });
  });
});
