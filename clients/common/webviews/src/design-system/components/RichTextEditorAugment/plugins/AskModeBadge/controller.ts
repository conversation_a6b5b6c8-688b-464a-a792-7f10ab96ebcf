/**
 * This module defines the AskModeBadgeController class, which implements the IRichTextEditorPlugin interface.
 * It's responsible for managing the ask mode badge functionality in the rich text editor.
 *
 * The ask mode badge is a visual indicator that appears when the user is in "ask a question" mode,
 * which provides a specific prompt for information gathering and retrieval only.
 */
import { Node } from "@tiptap/core";
import type { IRichTextEditorPlugin } from "../../types";
import type { Editor } from "@tiptap/core";
import { type Node as ProseMirrorNode } from "prosemirror-model";

import AskModeBadgeRenderer from "./AskModeBadgeRenderer.svelte";
import { mount, unmount } from "svelte";

/**
 * The prompt that should be used when ask mode is active.
 * This prompt instructs the AI to focus on information gathering only.
 */
export const ASK_MODE_PROMPT = `# For this specific question, follow these ask mode guidelines:
- Focus on providing clear, accurate information
- Use code examples when helpful
- ONLY use retrieval tools (web-fetch, codebase-retrieval, grep-search) to gather information
- Do NOT use any tools that modify files (str-replace-editor, save-file, remove-files, etc.)
- Do NOT make any changes to the codebase - this is for information gathering only
- If the question is unclear, ask for clarification
- If you need to search for information, use the available retrieval tools extensively

User message:
`;

/**
 * A plugin that manages the ask mode badge as a node in the editor content.
 *
 * This plugin creates a TipTap node that represents the ask mode badge and
 * automatically inserts/removes it when ask mode is toggled.
 */
export class AskModeBadgeController implements IRichTextEditorPlugin {
  private _isAskMode = false;
  private _editor: Editor | undefined;
  private static readonly nodeName = "askMode";
  private _onAskModeChange?: (isAskMode: boolean) => void;
  private _lastKnownBadgeState = false;

  public setAskMode(isAskMode: boolean, onComplete?: () => void): void {
    if (this._isAskMode === isAskMode) {
      // No state change needed, don't call onComplete
      return;
    }

    this._isAskMode = isAskMode;

    // Always notify about state changes
    this._onAskModeChange?.(isAskMode);

    if (!this._editor) {
      onComplete?.();
      return;
    }

    if (isAskMode) {
      this._insertBadgeNode(onComplete);
    } else {
      this._removeBadgeNode();
      onComplete?.();
    }

    // Update our tracking state after programmatic changes
    this._lastKnownBadgeState = isAskMode;
  }

  public get isAskMode(): boolean {
    return this._isAskMode;
  }

  public get askModePrompt(): string {
    return ASK_MODE_PROMPT;
  }

  public setOnAskModeChange(callback: (isAskMode: boolean) => void): void {
    this._onAskModeChange = callback;
  }

  /**
   * Detects if the given content contains an ask mode badge
   * @param content - JSONContent to check for ask mode badge
   * @returns true if content contains an ask mode badge
   */
  public static detectAskModeInContent(content: unknown): boolean {
    if (!content || typeof content !== "object") {
      return false;
    }

    const contentObj = content as Record<string, unknown>;

    // Check if this node is an ask mode badge
    if (contentObj.type === AskModeBadgeController.nodeName) {
      return true;
    }

    // Recursively check content array
    if (Array.isArray(contentObj.content)) {
      return contentObj.content.some((node: unknown) =>
        AskModeBadgeController.detectAskModeInContent(node),
      );
    }

    return false;
  }

  private _insertBadgeNode(onComplete?: () => void): void {
    if (!this._editor) {
      onComplete?.();
      return;
    }

    // Check if badge already exists
    const existingBadge = this._findBadgeNode();
    if (existingBadge) {
      onComplete?.();
      return;
    }

    // Store current cursor position
    const currentPos = this._editor.state.selection.from;

    // Get all current content as text
    const currentContent = this._editor.getText();

    // Clear the editor and insert badge + space + content together
    this._editor.commands.clearContent();
    this._editor.commands.insertContent([
      {
        type: AskModeBadgeController.nodeName,
        attrs: { prompt: ASK_MODE_PROMPT },
      },
      { type: "text", text: " " },
      ...(currentContent ? [{ type: "text", text: currentContent }] : []),
    ]);

    // Move cursor to appropriate position after the badge and space
    const badgeNode = this._findBadgeNode();
    if (badgeNode) {
      // If there was existing content, try to restore cursor position (adjusted for badge + space)
      const newCursorPos = currentContent
        ? Math.min(currentPos + 2, this._editor.state.doc.content.size)
        : badgeNode.pos + 2; // +2 to account for badge node + space
      this._editor.commands.setTextSelection(newCursorPos);
    }

    // Call completion callback after DOM updates
    setTimeout(() => {
      onComplete?.();
    }, 0);
  }

  private _removeBadgeNode(): void {
    if (!this._editor) return;

    const badgeNode = this._findBadgeNode();
    if (!badgeNode) return;

    // Remove the badge node and the trailing space
    const endPos = badgeNode.pos + badgeNode.node.nodeSize;
    // Check if there's a space after the badge and include it in deletion
    const nextChar = this._editor.state.doc.textBetween(endPos, endPos + 1);
    const deleteEnd = nextChar === " " ? endPos + 1 : endPos;

    this._editor.commands.deleteRange({
      from: badgeNode.pos,
      to: deleteEnd,
    });
  }

  private _findBadgeNode(): { pos: number; node: ProseMirrorNode } | null {
    if (!this._editor) return null;

    let badgeNode: { pos: number; node: ProseMirrorNode } | null = null;

    this._editor.state.doc.descendants((node, pos) => {
      if (node.type.name === AskModeBadgeController.nodeName) {
        badgeNode = { pos, node };
        return false; // Stop traversing
      }
    });

    return badgeNode;
  }

  public get tipTapExtension() {
    // eslint-disable-next-line @typescript-eslint/no-this-alias
    const controller = this;

    return Node.create({
      name: AskModeBadgeController.nodeName,
      group: "inline",
      inline: true,
      atom: true,
      selectable: false,

      // Add attributes to store the ask mode prompt
      addAttributes() {
        return {
          prompt: {
            default: ASK_MODE_PROMPT,
            keepOnSplit: false,
            // How to parse HTML into the prompt attribute (for WYSIWYG editing)
            parseHTML: (element: HTMLElement) => {
              const promptAttr = element.getAttribute("data-ask-mode-prompt");
              return promptAttr || ASK_MODE_PROMPT;
            },
            // How to render the prompt attribute as HTML (for WYSIWYG editing)
            renderHTML: (attributes: Record<string, unknown>) => {
              return {
                // eslint-disable-next-line @typescript-eslint/naming-convention
                "data-ask-mode-prompt": attributes.prompt || ASK_MODE_PROMPT,
              };
            },
          },
        };
      },

      onCreate() {
        controller._editor = this.editor;
        // Initialize tracking state with current document state
        controller._lastKnownBadgeState = controller._findBadgeNode() !== null;
      },

      onDestroy() {
        controller._editor = undefined;
      },

      onUpdate() {
        // Check if badge still exists in the document
        const badgeExists = controller._findBadgeNode() !== null;

        // Only sync state if the badge state changed unexpectedly
        // This detects user-initiated changes (like backspace) while avoiding
        // interference with programmatic state changes
        if (badgeExists !== controller._lastKnownBadgeState) {
          controller._lastKnownBadgeState = badgeExists;

          // Only update internal state and notify if this represents a user-initiated change
          // (i.e., the badge was removed but our internal state still thinks it should be there)
          if (controller._isAskMode !== badgeExists) {
            controller._isAskMode = badgeExists;
            controller._onAskModeChange?.(badgeExists);
          }
        }
      },

      parseHTML() {
        return [
          {
            tag: `span[data-type="${AskModeBadgeController.nodeName}"]`,
          },
        ];
      },

      addNodeView() {
        return ({ node: _node }: { node: ProseMirrorNode }) => {
          const target = document.createElement("span");
          target.setAttribute("data-type", AskModeBadgeController.nodeName);

          const component = mount(AskModeBadgeRenderer, {
            target,
            props: {
              isEditable: controller._editor?.isEditable ?? true,
            },
          });

          return {
            dom: target,
            destroy: () => {
              unmount(component);
            },
            update: (updatedNode: ProseMirrorNode): boolean => {
              return updatedNode.type.name === AskModeBadgeController.nodeName;
            },
          };
        };
      },

      renderText() {
        return "Ask a Question";
      },
    });
  }
}
