import { SUPPORTED_FILE_EXTENSIONS } from "$common-webviews/src/apps/chat/utils/file-utils";

/**
 * Custom error class for file-related errors.
 */
export class FileError extends Error {
  public readonly files: File[];

  /**
   * Creates a new FileError instance.
   * @param file - The File object that caused the error.
   * @param message - The error message.
   */
  constructor(files: File[], message: string) {
    super(message);
    this.files = files;
    this.name = "FileError";
  }
}

export class FileSizeError extends FileError {
  constructor(f: File, maxSizeKb: number) {
    const fileSizeKb = Math.round(f.size / 1024);
    super([f], `File "${f.name}" exceeds size limit of ${maxSizeKb}KB. Size: ${fileSizeKb}KB `);
  }
}

export class FileTypeError extends FileError {
  constructor(f: File, supportedFileTypes: ("image" | "text")[]) {
    const supportedTypesText = supportedFileTypes.join(" and ");
    super([f], `File "${f.name}" not supported, only attach ${supportedTypesText} files.`);
  }
}

export type ValidateFileOptions = {
  maxImageSizeKb?: number;
  maxTextSizeKb?: number;
  supportedFileTypes: ("image" | "text")[];
};
/**
 * Validates a file against specified options.
 * @param f - The File object to validate.
 * @param options - Optional validation options.
 * @returns A FileError if validation fails, undefined otherwise.
 */
export function validateFile(f: File, options: ValidateFileOptions): FileError | File {
  // Check if it's a supported file type based on the supportedFileTypes option
  if (!isSupportedFileType(f, options.supportedFileTypes)) {
    return new FileTypeError(f, options.supportedFileTypes);
  }

  return f;
}

/**
 * Checks if a file is an image based on its MIME type.
 * @param f - The File object to check.
 * @returns True if the file is an image, false otherwise.
 */
export function isImage(f: File): boolean {
  return f.type.startsWith("image/");
}

/**
 * Get text file extensions from SUPPORTED_FILE_EXTENSIONS (excluding image extensions)
 */
function getTextFileExtensions(): string[] {
  const imageExtensions = [".png", ".jpg", ".jpeg", ".gif", ".webp", ".svg", ".bmp"];
  return SUPPORTED_FILE_EXTENSIONS.filter((ext) => !imageExtensions.includes(ext));
}

/**
 * Checks if a file is a supported file type (image, PDF, or any text-based file).
 * @param f - The File object to check.
 * @param supportedTypes - Optional array of supported file types. Defaults to ["image", "text"].
 * @returns True if the file is a supported type, false otherwise.
 */
export function isSupportedFileType(f: File, supportedTypes: ("image" | "text")[]): boolean {
  // Check for images if image type is supported
  if (supportedTypes.includes("image") && f.type.startsWith("image/")) {
    return true;
  }

  // Only check for text-based files if text type is supported
  if (!supportedTypes.includes("text")) {
    return false;
  }

  // Check for any text-based MIME types
  if (f.type.startsWith("text/")) {
    return true;
  }

  // Check for other common text-based MIME types
  const textBasedMimeTypes = [
    "application/json",
    "application/xml",
    "application/javascript",
    "application/typescript",
    "application/x-sh",
    "application/x-shellscript",
    "application/x-yaml",
    "application/yaml",
  ];

  if (textBasedMimeTypes.includes(f.type)) {
    return true;
  }

  // Use SUPPORTED_FILE_EXTENSIONS for file extension validation
  const fileName = f.name.toLowerCase();
  const textFileExtensions = getTextFileExtensions();

  // Check if file extension is in our supported list
  for (const ext of textFileExtensions) {
    if (fileName.endsWith(ext.toLowerCase())) {
      return true;
    }
  }

  // Handle common configuration files and files without extensions that are typically text-based
  const commonTextFiles = [
    "dockerfile",
    ".gitignore",
    ".gitattributes",
    "makefile",
    "readme",
    "license",
    "changelog",
    "authors",
    "contributors",
    "copying",
    "install",
    "news",
    "todo",
    ".env.example",
    ".env.local",
    ".env.development",
    ".env.production",
  ];

  if (commonTextFiles.includes(fileName)) {
    return true;
  }

  return false;
}
