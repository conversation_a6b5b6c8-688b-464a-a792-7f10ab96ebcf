/** Options for creating a one-shot mutation observer. */
export type OneShotMutationObserverOptions = {
  /** A function that determines whether the mutation should trigger the callback.
   * @param mutation The MutationRecord to evaluate.
   * @returns True if the callback should be triggered, false otherwise. */
  shouldTrigger: (mutation: MutationRecord) => boolean;
  /** The callback function to be executed when the mutation is triggered. */
  callback: () => void;
  /** Optional timeout in milliseconds. If provided, the observer will disconnect after this time. */
  timeout?: number;
  /** Optional MutationObserverInit object to customize the observer's configuration. */
  mutationObserverInit?: MutationObserverInit;
};

/**
 * Creates a one-shot mutation observer that will call the callback once and then disconnect itself.
 *
 * Supports the following features:
 * - If a timeout is provided, the observer will disconnect itself after the timeout has elapsed
 * - If the returned disposal function is called, the observer will disconnect itself immediately
 *
 * @param target The target element to observe
 * @param options The options for the observer
 * @returns A function that can be called to disconnect the observer immediately
 */
export function createOneShotMutationObserver(
  target: HTMLElement,
  options: OneShotMutationObserverOptions,
): () => void {
  // Create an observer that, when triggered, will call the callback and then disconnect itself
  const observer = new MutationObserver((mutations) => {
    for (const mutation of mutations) {
      if (options.shouldTrigger(mutation)) {
        options.callback();
        observer.disconnect();
        return;
      }
    }
  });

  // Connect the observer to the container
  observer.observe(target, { childList: true, subtree: true, ...options.mutationObserverInit });

  // If a timeout is provided, we disconnect the observer after the timeout has elapsed
  if (options.timeout) {
    setTimeout(() => observer.disconnect(), options.timeout);
  }

  // Return a function that can be called to disconnect the observer immediately
  return () => observer.disconnect();
}

export function attachOneShotMountObserver(
  container: HTMLElement,
  target: HTMLElement,
  callback: () => void,
  timeout?: number,
): () => void {
  return createOneShotMutationObserver(container, {
    shouldTrigger: (mutation) => new Set(mutation.addedNodes).has(target),
    callback,
    timeout,
  });
}

export function attachOneShotUnmountObserver(
  container: HTMLElement,
  target: HTMLElement,
  callback: () => void,
  timeout?: number,
): () => void {
  return createOneShotMutationObserver(container, {
    shouldTrigger: (mutation) => new Set(mutation.removedNodes).has(target),
    callback,
    timeout,
  });
}
