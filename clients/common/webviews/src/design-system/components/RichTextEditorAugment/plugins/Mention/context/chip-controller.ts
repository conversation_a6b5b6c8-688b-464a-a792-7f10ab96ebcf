import type { MentionOptions } from "@tiptap/extension-mention";
import { type Editor } from "@tiptap/core";
import type { DOMOutputSpec, Node as ProseMirrorNode } from "prosemirror-model";
import { type Readable, writable, type Writable } from "svelte/store";

import type { IMentionable } from "../types";
import type { IMentionChipTooltipData } from "../types";
import { attachOneShotMountObserver, attachOneShotUnmountObserver } from "./utils";
import { type IDisposeFn } from "../../../types";

/**
 * ChipController class
 *
 * This class manages the tooltip functionality for mention chips in the rich text editor.
 * It provides methods to create mention chips and handle hover events to show and hide tooltips.
 *
 * Public controls API:
 * - createMentionChip: Creates a mention chip element with hover event listeners
 * - hideTooltip: Hides the tooltip
 *
 * Public state API:
 * - tooltipData: The data for the currently displayed tooltip. Read by the actual
 *   tooltip component to render the tooltip content.
 */
export class ChipController<TOption extends IMentionable> {
  /* eslint-disable @typescript-eslint/naming-convention */
  public static readonly CHIP_CLASS_NAME = "c-mention-chip";
  public static readonly CHIP_DATA_ATTR_KEY = "data-augment-mention-chip-tooltip";
  /* eslint-enable @typescript-eslint/naming-convention */

  private _disposers: IDisposeFn[] = [];
  private _editor: Editor | undefined;
  private _tooltipData: Writable<IMentionChipTooltipData<TOption> | undefined> =
    writable(undefined);
  public get tooltipData(): Readable<IMentionChipTooltipData<TOption> | undefined> {
    return this._tooltipData;
  }

  constructor(private _mentionPluginId: string) {}

  // Call this from TipTap hooks, whenever the editor is first created
  public onCreate = (editor: Editor) => {
    this._editor = editor;
  };

  // Call this from TipTap hooks, whenever the editor is destroyed
  public onDispose = () => {
    this._editor = undefined;
    this._disposers.forEach((dispose) => dispose());
    this._disposers = [];
  };

  // ==== Mention item chip callbacks ====

  // Attach the appropriate event listeners to a span element
  private _attachChipEventListeners(span: HTMLSpanElement, showChipTooltip: () => void) {
    span.addEventListener("mouseenter", showChipTooltip);
    span.addEventListener("mouseover", showChipTooltip);
    span.addEventListener("mouseleave", this.hideTooltip);
  }

  // Detach the appropriate event listeners from a span element
  private _detachChipEventListeners(span: HTMLSpanElement, showChipTooltip: () => void) {
    span.removeEventListener("mouseenter", showChipTooltip);
    span.removeEventListener("mouseover", showChipTooltip);
    span.removeEventListener("mouseleave", this.hideTooltip);
  }

  private _removeFromDisposers(disposeFn: IDisposeFn | undefined) {
    if (disposeFn) {
      this._disposers = this._disposers.filter((d) => d !== disposeFn);
    }
  }

  /**
   * Sets up the mount/unmount cycle for a mention chip span element.
   *
   * This method manages the lifecycle of a mention chip, handling its mounting and unmounting
   * from the DOM. It uses one-shot observers to detect these events and responds accordingly.
   *
   * Lifecycle stages:
   * 1. Initial Mount:
   *    - The span is initially mounted to the DOM.
   *    - A one-shot mount observer is attached to detect this event.
   *
   * 2. On Mount:
   *    - The mount observer triggers the onMount callback.
   *    - Event listeners for tooltip functionality are attached to the span.
   *    - A one-shot unmount observer is set up to detect future removal.
   *
   * 3. On Unmount:
   *    - The unmount observer triggers the onUnmount callback when the span is removed.
   *    - Event listeners are detached from the span.
   *    - A new one-shot mount observer is set up to detect if the span is re-added.
   *
   * 4. Potential Re-mount:
   *    - If the span is re-added (e.g., due to undo), the cycle restarts from step 2.
   *
   * Throughout this cycle, disposer functions are managed to ensure proper cleanup
   * and prevent memory leaks.
   *
   * @param editorContainer - The container element of the editor.
   * @param span - The span element representing the mention chip.
   * @param showChipTooltip - Function to show the chip tooltip.
   */
  private _setupMountUnmountCycle(
    editorContainer: HTMLElement,
    span: HTMLSpanElement,
    showChipTooltip: () => void,
  ) {
    let disposeUnmountListener: IDisposeFn | undefined;
    let disposeMountListener: IDisposeFn | undefined;

    // When we unmount, we need to:
    // - Unregister the mount disposer, since we no longer need it
    // - Detach the event listeners, since the span is no longer in the DOM
    // - Attach a new mount listener, since the span may be re-added to the DOM
    //   (e.g. if the user undoes the deletion of the mention)
    const onUnmount = () => {
      this._removeFromDisposers(disposeUnmountListener);
      this._detachChipEventListeners(span, showChipTooltip);

      disposeMountListener = attachOneShotMountObserver(editorContainer, span, onMount);
      this._disposers.push(disposeMountListener);
    };

    // When we mount, we need to:
    // - Unregister the mount disposer, since we no longer need it
    // - Attach the event listeners, since the span is now in the DOM
    // - Attach a new unmount listener, since the span may be removed from the DOM
    //   (e.g. if the user deletes the mention)
    const onMount = () => {
      this._removeFromDisposers(disposeMountListener);
      this._attachChipEventListeners(span, showChipTooltip);

      disposeUnmountListener = attachOneShotUnmountObserver(editorContainer, span, onUnmount);
      this._disposers.push(disposeUnmountListener);
    };

    // Start the cycle by attaching the initial mount observer
    attachOneShotMountObserver(editorContainer, span, onMount);
  }

  /**
   * Creates a mention chip element with hover event listeners.
   *
   * @param options - The MentionOptions object containing suggestion and HTMLAttributes.
   * @param node - The ProseMirrorNode representing the mention.
   * @returns A DOMOutputSpec representing the created mention chip.
   * @throws Error if the node type doesn't match the mention plugin name.
   */
  public createMentionChip = ({
    options,
    node,
  }: {
    options: MentionOptions;
    node: ProseMirrorNode;
  }): DOMOutputSpec => {
    if (node.type.name !== this._mentionPluginId) {
      throw new Error("Expected a mention node");
    }

    // Create the span element for the mention chip
    const span = document.createElement("span");
    span.innerText = `${options.suggestion?.char ?? "@"}${node.attrs.label}`;

    // Set HTML attributes on the span
    for (const [key, value] of Object.entries(options.HTMLAttributes)) {
      if (typeof value === "string") {
        span.setAttribute(key, value.toString());
      } else {
        console.warn(`Unexpected HTML attribute value type: [${key}] = ${value}`);
      }
    }

    // Define the function to show the chip tooltip
    const showChipTooltip = () => {
      this._tooltipData.set({
        data: node.attrs.data as TOption,
        anchorElement: span,
      });
    };

    // Get the editor container and set up the mount/unmount cycle
    const editorContainer = this._editor?.view.dom;
    if (!editorContainer) {
      return span;
    }

    this._setupMountUnmountCycle(editorContainer, span, showChipTooltip);
    return span;
  };

  public hideTooltip = () => {
    this._tooltipData.set(undefined);
  };
}
