<script lang="ts">
  /**
   * This script handles the component logic, including initialization of the placeholder controller
   * and reactivity for updating the placeholder text.
   */
  import { getContext, onDestroy } from "svelte";
  import { RichTextEditorContext } from "../../context";
  import { PlaceholderController } from "./controller";

  // The placeholder text can be set via prop, and reactively updates the context
  export let placeholder: string = "Type something...";
  $: placeholderController.setPlaceholder(placeholder);

  // Initialize the placeholder controller with the provided placeholder text
  const placeholderController = new PlaceholderController(placeholder);

  // Register the placeholder plugin and ensure it's unregistered when the component is destroyed
  const editorContext = getContext<RichTextEditorContext>(RichTextEditorContext.CONTEXT_KEY);
  onDestroy(editorContext.pluginManager.registerPlugin(placeholderController));
</script>

<style>
  /**
   * This style block defines the appearance of the placeholder text.
   * It uses a pseudo-element to display the placeholder when the editor is empty.
   *
   * See https://tiptap.dev/docs/editor/api/extensions/placeholder for more details.
   */
  :global(.tiptap p.is-editor-empty:first-child::before) {
    opacity: 0.5;
    color: var(--ds-color-neutral-a11);
    content: attr(data-placeholder);
    float: left;
    height: 0;
    pointer-events: none;
  }
</style>
