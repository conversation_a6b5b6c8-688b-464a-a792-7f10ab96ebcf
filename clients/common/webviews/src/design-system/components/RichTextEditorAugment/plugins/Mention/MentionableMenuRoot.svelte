<script lang="ts" generics="TOption extends IMentionable">
  import { RichTextEditorContext } from "../../context";

  /**
   * MentionablesMenuRoot Component
   *
   * This component is responsible for rendering the mentionables menu for the mention plugin
   * in the RichTextEditorAugment. It manages the display of mentionables based on user input
   * and provides the necessary context for child components to render mentionable items.
   *
   * Key responsibilities:
   * 1. Display the mentionables menu when appropriate
   * 2. Provide the necessary data for slotted components to render mentionable items
   * 3. Handle user interactions with the mentionables menu
   *
   * Events API:
   * - mentionables: The list of mentionable items being displayed
   * - onQueryUpdate: Called when the query for mentionables changes
   * - onSelectMentionable: Called when a mentionable item is selected
   *
   * Controls API:
   * - requestOpen: Opens the mentionables menu
   * - requestClose: Closes the mentionables menu
   * - focusIdx: Focuses on a specific mentionable item by index
   */
  import { getContext } from "svelte";

  import type { IMentionable } from "./types";
  import { MentionPluginContext } from "./context";
  import DropdownMenuAugment from "../../../DropdownMenuAugment";
  import type DropdownMenuRoot from "../../../DropdownMenuAugment/Root.svelte";

  export let mentionables: TOption[];
  export let onQueryUpdate: (query: string | undefined) => void;
  // Default behavior is to insert the mention node when an option is selected.
  // If there are more complex behaviors needed, the API consumer can override this behavior.
  export let onSelectMentionable: (mentionable: TOption) => boolean = (mentionable: TOption) => {
    mentionContext.insertMentionNode({ ...mentionable, data: mentionable });
    return true;
  };

  // Public API for external controls
  let dropdownRoot: DropdownMenuRoot;
  export const requestOpen = () => dropdownRoot?.requestOpen();
  export const requestClose = () => dropdownRoot?.requestClose();
  export const focusIdx = (idx: number) => dropdownRoot?.focusIdx(idx);

  // Extract reactive state
  const editorContext = getContext<RichTextEditorContext>(RichTextEditorContext.CONTEXT_KEY);
  const mentionContext = getContext<MentionPluginContext<TOption>>(
    MentionPluginContext.CONTEXT_KEY,
  );
  const mentionableMenuContext = mentionContext.mentionableMenuContext;
  const { referenceClientRect, query, activeItem, isMenuActive, exitMenu } = mentionableMenuContext;

  // Update the query callback when the query changes
  $: onQueryUpdate($query);
  // Update context when props change
  $: mentionableMenuContext.updateOptions({ onSelectMentionable });
  $: mentionableMenuContext.updateMentionables(mentionables);

  // If the menu has closed, we should try and focus the editor
  const onOpenChange = async (open: boolean) => {
    if (!open) {
      editorContext.commandManager.requestFocus();
    }
  };
</script>

<!-- Only show the menu if we have mentionable menu data -->
<DropdownMenuAugment.Root bind:this={dropdownRoot} open={$isMenuActive} {onOpenChange}>
  <DropdownMenuAugment.Trigger referenceClientRect={$referenceClientRect} />
  <DropdownMenuAugment.Content
    side="top"
    align="start"
    size={1}
    onClickOutside={exitMenu}
    onEscapeKeyDown={exitMenu}
  >
    <slot activeItem={$activeItem} query={$query} />
  </DropdownMenuAugment.Content>
</DropdownMenuAugment.Root>
