<script lang="ts">
  import ModalAugment from "../../../ModalAugment.svelte";
  import SpinnerAugment from "../../../SpinnerAugment.svelte";
  import { getFileExtension, isImageFile } from "./file-type-utils";
  /**
   * The name of the original image file. This is user facing text.
   */
  export let title: string;
  /** The name of the image in the asset manager */
  export let src: string;
  export let loadImage: (fileName: string) => Promise<string | undefined>;
  export let removeImage: () => void;
  /** If true, a loading spinner is displayed with the title instead of the image */
  export let isLoading: boolean = false;
  export let isOverLimit: boolean = false;
  /** File size in bytes for display */
  export let fileSizeBytes: number = 0;
  /**
   * Get whether the editor is editable
   *
   * If true, then the image can be removed from the editor
   */
  export let isEditable: () => boolean;

  let showModal = false;

  function toggleImagePreview() {
    if (!imgElement) return;
    showModal = !showModal;
  }

  function handleKeyDown(e: KeyboardEvent) {
    // Handle arrow keys for navigation and activation
    if (
      e.key === "ArrowUp" ||
      e.key === "ArrowDown" ||
      e.key === "ArrowLeft" ||
      e.key === "ArrowRight"
    ) {
      e.preventDefault();
      e.stopPropagation();
    }
  }

  let imgElement: HTMLImageElement | undefined;
</script>

<svelte:window />
<!-- {@debug fileSizeBytes, maxImageSizeKb, isOverLimit} -->
<div
  class="c-image-chip"
  class:is-over-limit={isOverLimit}
  on:click={(e) => {
    e.preventDefault();
  }}
  on:keydown={(e) => {
    handleKeyDown(e);
  }}
  role="button"
  tabindex="0"
>
  <!-- Close button -->
  {#if isEditable()}
    <button
      class="c-image-chip__close"
      on:click|stopPropagation={removeImage}
      aria-label="Remove image"
    >
      ×
    </button>
  {/if}

  <!-- Image preview -->
  <div class="c-image-render__chip-preview">
    {#if isLoading}
      <div class="c-image-chip__loading">
        <SpinnerAugment size={1} />
      </div>
    {:else if !isImageFile(title)}
      <div class="c-image-chip__file-extension">
        {getFileExtension(title) || title}
      </div>
    {:else}
      {#await loadImage(src)}
        <div class="c-image-chip__loading">
          <SpinnerAugment size={1} />
        </div>
      {:then base64}
        {#if base64}
          <div 
            class="c-image-chip__image-container" 
            role="button" 
            tabindex="0" 
            on:click={toggleImagePreview} 
            on:keydown={toggleImagePreview}>
            <img bind:this={imgElement} src={base64} alt="" class="c-image-chip__image" />
          </div>
          <ModalAugment
            show={showModal}
            on:click={toggleImagePreview}
            on:backdropClick={toggleImagePreview}
          >
            <div 
              class="c-image-chip__image-modal-container">
              <img src={base64} alt="" class="c-image-chip__image" />
            </div>
          </ModalAugment>
        {:else}
          <!-- Fallback file icon -->
          <div class="c-image-chip__file-icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
              <path
                d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"
              />
            </svg>
          </div>
        {/if}
      {/await}
    {/if}
  </div>

  <!-- File info -->
  <div class="c-image-chip__info">
    <div class="c-image-chip__filename" {title}>
      {title}
    </div>
    <div class="c-image-chip__size">
      {#if isLoading}
        <div class="c-image-chip__size-loading">
          <SpinnerAugment size={1} />
        </div>
      {:else}
        {#if isOverLimit}
          <span class="c-image-chip__warning-icon"> ⚠️ </span>
        {/if}
        {fileSizeBytes ? `${Math.round(fileSizeBytes / 1024)}KB` : ""}
      {/if}
    </div>
  </div>
</div>

<style>
  .c-image-chip {
    position: relative;
    flex-direction: column;
    width: 80px;
    background-color: var(--ds-color-neutral-3);
    border-radius: var(--ds-spacing-2);
    padding: var(--ds-spacing-2);
    margin: var(--ds-spacing-1) var(--ds-spacing-1) var(--ds-spacing-1) 0;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid var(--ds-color-neutral-5);
    /* Reserve space for selection highlight to prevent layout shift */
    margin-left: 2px;

    &:hover {
      background-color: var(--ds-color-neutral-4);
      border-color: var(--ds-color-neutral-6);
    }

    &.is-over-limit {
      background-color: var(--ds-color-warning-3);
      border-color: var(--ds-color-warning-5);

      &:hover {
        background-color: var(--ds-color-warning-3);
        border-color: var(--ds-color-warning-7);
      }
    }
  }

  .c-image-chip__image-container {
    display: contents;
  }

  .c-image-chip__image-modal-container {
    display: flex;
    align-items: center;
    flex-direction: column;
  }

  .c-image-chip__close {
    position: absolute;
    top: 1px;
    right: 1px;
    width: 15px;
    height: 15px;
    border-radius: var(--ds-radius-2);
    background-color: transparent;
    color: var(--ds-color-neutral-11);
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    /* font-weight: bold; */
    line-height: 1;
    z-index: 1;
    transition: all 0.2s ease;

    &:hover {
      background-color: var(--ds-color-neutral-8);
      /* transform: scale(1.1); */
    }
  }

  .c-image-render__chip-preview {
    width: 100%;
    height: 48px;
    border-radius: var(--ds-spacing-1);
    background-color: var(--ds-color-neutral-2);
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    margin-bottom: var(--ds-spacing-1);
  }

  .c-image-chip__image {
    width: 90%;
    height: 90%;
    object-fit: cover;
    border-radius: var(--ds-spacing-1);
  }

  .c-image-chip__file-icon {
    color: var(--ds-color-neutral-7);
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .c-image-chip__file-extension {
    color: var(--ds-color-neutral-9);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    font-weight: 600;
    text-align: center;
    line-height: 1;
    padding: 2px;
    background-color: var(--ds-color-neutral-4);
    border-radius: var(--ds-spacing-1);
    border: 1px solid var(--ds-color-neutral-6);
    width: 90%;
    height: 90%;
    max-width: 40px;
    max-height: 32px;
  }

  .c-image-chip__loading {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
  }

  .c-image-chip__info {
    text-align: center;
    width: 100%;
  }

  .c-image-chip__filename {
    font-size: 11px;
    font-weight: 500;
    color: var(--ds-color-neutral-11);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-bottom: 2px;
    line-height: 1.2;
  }

  .c-image-chip__size {
    font-size: 10px;
    color: var(--ds-color-neutral-9);
    font-weight: 400;
    line-height: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 2px;
  }

  .c-image-chip__size-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 12px;
  }

  .c-image-chip__warning-icon {
    font-size: 8px;
    opacity: 0.8;
  }

  /* Selection styling */
  :global(.ProseMirror-selectednode) .c-image-chip {
    box-shadow: 0 0 0 2px var(--ds-color-accent-8);
  }
</style>
