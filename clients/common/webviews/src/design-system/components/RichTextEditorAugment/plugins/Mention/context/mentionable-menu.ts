import type { SuggestionProps } from "@tiptap/suggestion";
import { derived, get, type Readable, writable, type Writable } from "svelte/store";

import type { IMentionable, IMentionableMenuData, IMentionableMenuOptions } from "../types";

/**
 * MentionableMenuContext Class
 *
 * This class manages the state and behavior of the dropdown menu for @mentions in the rich text editor.
 * Specifically, it manages the query for the @mention, and stores positioning information for the dropdown menu
 *  via `referenceClientRect`.
 *
 * Public controls API:
 * - replaceQueryWithMentionNode: Replaces the current query with a mention node
 * - selectItem: Selects a particular item in the dropdown menu
 *
 * Public state API:
 * - query: The current query for the @mention
 * - activeIdx: The currently active index in the dropdown menu
 * - referenceClientRect: The client rectangle of the reference element for the dropdown menu
 * - tiptapExtensionProps: The properties provided by the TipTap mentionable plugin
 * - isMenuActive: Whether the dropdown menu is currently active
 *
 * TipTap hooks (used internally within RichTextEditorAugment)
 * - onUpdateMentionable: Callback for the TipTap mentionable plugin to update the mentionable menu state
 * - onExitMentionable: Callback for the TipTap mentionable plugin when the mentionable menu should be closed
 * - onArrowUp: Key shortcut handler for moving up in the dropdown menu
 * - onArrowDown: Key shortcut handler for moving down in the dropdown menu
 */
export class MentionableMenuContext<TOption extends IMentionable> {
  // Internal Svelte store for the current state of the mentionable menu
  private _menuData: Writable<IMentionableMenuData<TOption> | undefined> = writable(undefined);
  private _mentionables: Writable<TOption[]> = writable([]);

  // Derived stores for convenient access to specific parts of the state
  public query = derived(this._menuData, ($data) => $data?.tiptapExtensionProps.query);
  public activeIdx = derived([this._mentionables, this._menuData], _getActiveIdx);
  public activeItem = derived([this._mentionables, this.activeIdx], _getActiveItem);
  public referenceClientRect = derived(this._menuData, ($data) => $data?.referenceClientRect);
  public tiptapExtensionProps = derived(this._menuData, ($data) => $data?.tiptapExtensionProps);
  public isMenuActive = derived(this._menuData, ($data) => $data !== undefined);
  public readonly mentionables: Readable<TOption[]> = this._mentionables;

  // Constructor for MentionableMenuContext
  constructor(private _opts: IMentionableMenuOptions<TOption> = {}) {}

  // ==== Public controls API ====
  public updateOptions = (opts: Partial<IMentionableMenuOptions<TOption>>) => {
    this._opts = { ...this._opts, ...opts };
  };

  public updateMentionables = (mentionables: TOption[]) => {
    this._mentionables.set(mentionables);
  };

  /**
   * Replaces the current query with a mention node.
   * This is used when we want to insert a mention directly from the menu.
   * We need to clear everything up to and including the trigger character,
   * and then insert the mention node.
   *
   * @param item - The mentionable item to replace the query with
   * @returns true if the operation was successful, false otherwise
   */
  public replaceQueryWithMentionNode = (item: TOption): boolean => {
    const isActive = get(this.isMenuActive);

    // `command` is defined by TipTap and, in essence, replaces the entire trigger
    // for the @mention search process with a mention node. Specifically, it:
    // - Clears the query string
    // - Clears the trigger character (default `@`)
    // - Inserts the mention node with the given item as its data
    //
    // This command is defined by TipTap and passed to us whenever the mention  menu props are updated via `onUpdateMentionable`.
    // See https://github.com/ueberdosis/tiptap/blob/9a6fde0fc78faea14f5491370d1934e2a2e2bd2b/packages/extension-mention/src/mention.ts#L99
    // for more details
    const command = get(this.tiptapExtensionProps)?.command;
    if (!isActive || !command) {
      return false;
    }

    command(item);
    return true;
  };

  /**
   * Select a particular item. Used when the user clicks on an item.
   * Since there *is* a specific target, we can directly select that item.
   *
   * @param item - The item to select
   * @returns true if the operation was successful, false otherwise
   */
  public selectItem = (item: TOption): boolean => {
    // Get index of item that was selected
    const idx = get(this._mentionables).findIndex((i) => i.id === item.id);
    if (idx === -1 || !this._opts.onSelectMentionable) {
      return false;
    }

    this._opts.onSelectMentionable?.(item);
    return true;
  };

  /**
   * ==== Private state API ====
   *
   * Private state derivations used by our `derived` stores above.
   */

  // Methods to control active index
  // If uninitialized, sets the active index to the first item
  private _incrementActiveIdx = () => {
    const currIdx = get(this.activeIdx) ?? 0;
    this._setActiveIdx(currIdx + 1);
  };

  // If uninitialized, sets the active index to the last item
  private _decrementActiveIdx = () => {
    const currIdx = get(this.activeIdx) ?? 0;
    this._setActiveIdx(currIdx - 1);
  };

  // Sets the active index to the given index
  private _setActiveIdx = (idx: number) => {
    this._menuData.update((data) => {
      return (
        data && {
          ...data,
          activeIdx: idx,
        }
      );
    });
  };

  // ==== TipTap hooks ====
  // These are called by TipTap whenever it needs to notify us of something (like a keypress)
  // and allow us to update the state here accordingly.

  /**
   * Callback for the TipTap suggestion plugin to update the mentionable menu state
   * @param props - The properties provided by the TipTap mentionable plugin
   */
  public onUpdateSuggestion = (props: SuggestionProps<TOption>): void => {
    const clientRect = props.clientRect?.();
    if (!clientRect) {
      return;
    }

    this._menuData.update((data) => {
      return {
        referenceClientRect: clientRect,
        tiptapExtensionProps: props,
        activeIdx: data?.activeIdx ?? 0,
      };
    });
  };

  /**
   * Callback for the TipTap suggestion plugin when the mentionable menu should be closed
   */
  public exitMenu = (): void => {
    this._menuData.set(undefined);
    this._mentionables.set([]);
  };

  private _handleKeyIfActive = (keyHandler: () => void): (() => boolean) => {
    return () => {
      if (get(this.isMenuActive)) {
        keyHandler();
        return true;
      }
      return false;
    };
  };

  // Keyboard shortcut handlers that are exposed to `MentionPluginContext`
  public onArrowUp = this._handleKeyIfActive(this._decrementActiveIdx);
  public onArrowDown = this._handleKeyIfActive(this._incrementActiveIdx);
  public selectActiveItem = this._handleKeyIfActive(() => {
    const activeItem = get(this.activeItem);
    if (!activeItem) {
      return false;
    }
    return this.selectItem(activeItem);
  });
}

/**
 * Helper function to get the active item from the mentionables and active index.
 * Used for derived stores.
 */
function _getActiveItem<TOption extends IMentionable>([mentionables, activeIdx]: [
  TOption[],
  number | undefined,
]): TOption | undefined {
  if (activeIdx === undefined || mentionables.length === 0) {
    return undefined;
  }
  return mentionables[activeIdx];
}

/**
 * Helper function to get the active index from the mentionables and data.
 * Used for derived stores.
 *
 */
function _getActiveIdx<TOption extends IMentionable>([mentionables, data]: [
  TOption[],
  IMentionableMenuData<TOption> | undefined,
]): number | undefined {
  if (data?.activeIdx === undefined || mentionables.length === 0) {
    return undefined;
  }
  // Bound to (-mentionables.length, mentionables.length)
  const activeIdxBounded = data.activeIdx % mentionables.length;
  // Bound to [0, mentionables.length)
  const activeIdxNonNegative = (activeIdxBounded + mentionables.length) % mentionables.length;
  return activeIdxNonNegative % mentionables.length;
}
