<script lang="ts">
  import DropZone from "./DropZone.svelte";
  import RichTextEditorAugment from "../..";
  import { ChatFlagsModel } from "$common-webviews/src/apps/chat/models/chat-flags-model";

  // Create a ChatFlagsModel with enableDebugFeatures set to true
  const flagsModel = new ChatFlagsModel({ enableDebugFeatures: true });
</script>

<RichTextEditorAugment.Root>
  <DropZone flags={flagsModel} />
</RichTextEditorAugment.Root>
