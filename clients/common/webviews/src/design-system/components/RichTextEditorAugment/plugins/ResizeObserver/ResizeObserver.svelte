<script lang="ts">
  import { ResizeObserverController } from "./controller";
  import { getContext, onDestroy } from "svelte";
  import { RichTextEditorContext } from "../../context";

  /**
   * This component handles the observation of size changes in the editor.
   * It does not render anything by itself, but instead provides a declarative API
   * for handling resize events.
   */

  export let height: number = 0;
  const onResize = (newHeight: number) => {
    height = newHeight;
  };

  // Register the resize observer controller with the editor context
  const editorContext = getContext<RichTextEditorContext>(RichTextEditorContext.CONTEXT_KEY);
  const resizeObserverController = new ResizeObserverController({ onResize });
  const cleanupPlugin = editorContext.pluginManager.registerPlugin(resizeObserverController);
  onDestroy(cleanupPlugin);

  // Update the resize observer controller when the props change
  $: resizeObserverController.updateOptions({ onResize });
</script>
