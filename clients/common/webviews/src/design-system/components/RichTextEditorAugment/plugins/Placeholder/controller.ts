/**
 * This module script defines the PlaceholderController class, which implements the IRichTextEditorPlugin interface.
 * It's responsible for managing the placeholder functionality in the rich text editor.
 */
import { Placeholder, type PlaceholderOptions } from "@tiptap/extension-placeholder";
import { type Editor, type Extension } from "@tiptap/core";
import type { IRichTextEditorPlugin } from "../../types";

/**
 * A basic plugin.
 *
 * Demonstrates the following capabilities:
 * - Connecting to TipTap's extension API with `Placeholder.extend` and registering our own functions
 * - Exposing an external hook for API consumers to update state
 */
export class PlaceholderController implements IRichTextEditorPlugin {
  private _placeholderExtension: Extension<PlaceholderOptions, Record<string, never>>;
  private _editor: Editor | undefined;

  /**
   * Creates a new PlaceholderController instance.
   * @param placeholder - The initial placeholder text.
   */
  constructor(placeholder: string) {
    const onUpdate = this._onUpdate.bind(this);
    const onDestroy = this._onDestroy.bind(this);

    // Extend the Placeholder extension with custom update and destroy handlers
    this._placeholderExtension = Placeholder.extend({
      placeholder,
      onUpdate() {
        this.parent?.();
        onUpdate(this.editor);
      },
      onDestroy() {
        this.parent?.();
        onDestroy();
      },
    });
  }

  /**
   * Updates the placeholder text and refreshes the editor view.
   * @param placeholder - The new placeholder text.
   */
  public setPlaceholder = (placeholder: string) => {
    this._placeholderExtension.options.placeholder = placeholder;
    this._editor?.view.updateState(this._editor.view.state);
  };

  /**
   * Updates the internal editor reference when the editor is updated.
   * @param editor - The updated Editor instance.
   */
  private _onUpdate = (editor: Editor) => {
    this._editor = editor;
  };

  /** Clears editor reference on destroy */
  private _onDestroy = () => {
    this._editor = undefined;
  };

  /** Returns TipTap placeholder extension */
  public get tipTapExtension() {
    return this._placeholderExtension;
  }
}
