import { Extension } from "@tiptap/core";
import type { IRichTextEditorPlugin } from "../../types";
import { type Command, Plugin, PluginKey } from "@tiptap/pm/state";
import { keydownHandler } from "prosemirror-keymap";
import type { EditorView } from "@tiptap/pm/view";

type KeybindingsControllerOptions = {
  shortcuts: Record<string, Command>;
};

export class KeybindingsController implements IRichTextEditorPlugin {
  /* eslint-disable @typescript-eslint/naming-convention */
  // We want to support multiple instances of the keybindings plugin, so we need to generate unique keys for each instance
  private static _sequenceId = 0;
  private static KEYBINDINGS_PLUGIN_KEY_BASE = "augment-keybindings-plugin-{}";
  private static _getSequenceId = () => KeybindingsController._sequenceId++;
  private static _getNextPluginId = () => {
    const seqId = KeybindingsController._getSequenceId().toString();
    return KeybindingsController.KEYBINDINGS_PLUGIN_KEY_BASE.replace("{}", seqId);
  };
  /* eslint-enable @typescript-eslint/naming-convention */

  private _tipTapExtension: Extension<KeybindingsControllerOptions, Record<string, never>>;
  private _keydownHandler: ReturnType<typeof keydownHandler> = () => false;

  constructor(private _options: KeybindingsControllerOptions) {
    this.updateOptions(this._options);
    const handleKeyDown = this._handleKeyDown;
    const name = KeybindingsController._getNextPluginId();
    const key = new PluginKey(name);

    this._tipTapExtension = Extension.create({
      name,
      addProseMirrorPlugins() {
        return [
          new Plugin({
            key,
            props: { handleKeyDown },
          }),
        ];
      },
    });
  }

  // ==== Public API ====
  public updateOptions = (opts: Partial<KeybindingsControllerOptions>) => {
    this._options = { ...this._options, ...opts };
    this._keydownHandler = keydownHandler(this._options.shortcuts);
  };

  public get tipTapExtension() {
    return this._tipTapExtension;
  }

  // ==== Private helpers ====
  private _handleKeyDown = (view: EditorView, event: KeyboardEvent) => {
    return this._keydownHandler(view, event);
  };
}
