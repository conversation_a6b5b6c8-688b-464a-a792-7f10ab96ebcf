import { expect, describe, test, beforeEach, afterEach, vi, type Mock, it } from "vitest";
import { FileController } from "./controller";
import type { Editor, ChainedCommands, JSONContent } from "@tiptap/core";
import { EditorTestKit } from "../../__tests__/test-kit";
import { waitFor } from "@testing-library/dom";
import * as readImageUtil from "./read-image-util";

let fileController: FileController;
let editor: Editor;
let chainedCommands: ChainedCommands;
let mockSaveImage: Mock;
let mockSaveAttachment: Mock;

beforeEach(() => {
  // Mock the DOM functions needed by TipTap
  EditorTestKit.mockTipTapDOMFunctions();
  editor = createMockEditor();
  mockSaveImage = vi.fn().mockResolvedValue("test-image-name");
  mockSaveAttachment = vi.fn().mockResolvedValue("test-attachment-name");
  fileController = new FileController({
    saveImage: mockSaveImage,
    saveAttachment: mockSaveAttachment,
    supportedFileTypes: ["image", "text"],
  });
  fileController.registerEditor(editor);
  URL.createObjectURL = vi.fn().mockReturnValue("blob:mock-url");

  // Mock readImageAsDataURL
  vi.spyOn(readImageUtil, "readImageAsBlob").mockImplementation(() =>
    Promise.resolve(new File([Buffer.from("data:image/jpeg;base64,mockdata")], "test.jpg")),
  );
});

afterEach(() => {
  vi.clearAllMocks();
});

describe("FileController", () => {
  test("insertFilesIntoEditor handles valid image files", async () => {
    const validImageFile = new File([""], "test.jpg", { type: "image/jpeg" });
    const result = fileController.insertFilesIntoEditor([validImageFile]);

    expect(result.errors).toHaveLength(0);
    expect(result.okFiles).toHaveLength(1);
    expect(result.okFiles[0]).toBe(validImageFile);

    await waitFor(() => {
      expect(mockSaveImage).toHaveBeenCalledWith(validImageFile);
      // eslint-disable-next-line @typescript-eslint/unbound-method
      expect(editor.chain).toHaveBeenCalled();
    });
  });

  test("insertFilesIntoEditor accepts text files", async () => {
    const textFile = new File([""], "test.txt", { type: "text/plain" });
    const result = fileController.insertFilesIntoEditor([textFile]);

    expect(result.errors).toHaveLength(0);
    expect(result.okFiles).toHaveLength(1);
    expect(result.okFiles[0]).toBe(textFile);

    await waitFor(() => {
      expect(mockSaveAttachment).toHaveBeenCalledOnce();
    });
  });

  test("insertFilesIntoEditor rejects unsupported binary files", () => {
    const binaryFile = new File([""], "test.exe", { type: "application/octet-stream" });
    const result = fileController.insertFilesIntoEditor([binaryFile]);

    expect(result.errors).toHaveLength(1);
    expect(result.okFiles).toHaveLength(0);
  });

  test("insertFilesIntoEditor respects supportedFileTypes option - image only", () => {
    const imageOnlyController = new FileController({
      saveImage: mockSaveImage,
      saveAttachment: mockSaveAttachment,
      supportedFileTypes: ["image"],
    });
    imageOnlyController.registerEditor(editor);

    const imageFile = new File([""], "test.jpg", { type: "image/jpeg" });
    const textFile = new File([""], "test.txt", { type: "text/plain" });

    const result = imageOnlyController.insertFilesIntoEditor([imageFile, textFile]);

    expect(result.errors).toHaveLength(1); // text file should be rejected
    expect(result.okFiles).toHaveLength(1); // only image file should be accepted
    expect(result.okFiles[0]).toBe(imageFile);
  });

  test("insertFilesIntoEditor respects supportedFileTypes option - text only", () => {
    const textOnlyController = new FileController({
      saveImage: mockSaveImage,
      saveAttachment: mockSaveAttachment,
      supportedFileTypes: ["text"],
    });
    textOnlyController.registerEditor(editor);

    const imageFile = new File([""], "test.jpg", { type: "image/jpeg" });
    const textFile = new File([""], "test.txt", { type: "text/plain" });

    const result = textOnlyController.insertFilesIntoEditor([imageFile, textFile]);

    expect(result.errors).toHaveLength(1); // image file should be rejected
    expect(result.okFiles).toHaveLength(1); // only text file should be accepted
    expect(result.okFiles[0]).toBe(textFile);
  });

  test("insertFilesIntoEditor handles multiple files", async () => {
    const validImage1 = new File([""], "test1.jpg", { type: "image/jpeg" });
    const validImage2 = new File([""], "test2.png", { type: "image/png" });
    const validTextFile = new File([""], "test.txt", { type: "text/plain" });
    const invalidFile = new File([""], "test.exe", { type: "application/octet-stream" });

    const result = fileController.insertFilesIntoEditor([
      validImage1,
      validTextFile,
      invalidFile,
      validImage2,
    ]);

    expect(result.errors).toHaveLength(1);
    expect(result.okFiles).toHaveLength(3);
    expect(result.okFiles).toContain(validImage1);
    expect(result.okFiles).toContain(validImage2);
    expect(result.okFiles).toContain(validTextFile);

    await waitFor(() => {
      expect(mockSaveImage).toHaveBeenCalledTimes(2); // 2 images
      expect(mockSaveAttachment).toHaveBeenCalledTimes(1); // 1 text file
      // eslint-disable-next-line @typescript-eslint/unbound-method
      expect(editor.chain).toHaveBeenCalledTimes(3);
    });
    await waitFor(() => {
      expect(chainedCommands.run).toHaveBeenCalledTimes(3);
    });
  });

  test("insertFilesIntoEditor handles empty file array", () => {
    const result = fileController.insertFilesIntoEditor([]);

    expect(result.errors).toHaveLength(0);
    expect(result.okFiles).toHaveLength(0);
  });

  test("insertFilesIntoEditor handles undefined editor", () => {
    fileController.unregisterEditor();
    const result = fileController.insertFilesIntoEditor([
      new File([""], "test.jpg", { type: "image/jpeg" }),
    ]);

    expect(result.errors).toHaveLength(0);
    //due to the way the mock works
    expect(result.okFiles).toHaveLength(1);
  });

  test("insertFilesIntoEditor allows oversized images (warnings shown in UI)", () => {
    fileController.updateOptions({
      maxImageSizeKb: 1,
      saveImage: mockSaveImage,
      saveAttachment: mockSaveAttachment,
      supportedFileTypes: ["image", "text"],
    });
    const validImage = new File([""], "test1.jpg", { type: "image/jpeg" });
    const oversizedImage = new File(["x".repeat(2 * 1024)], "test2.png", {
      type: "image/png",
    });

    const result = fileController.insertFilesIntoEditor([validImage, oversizedImage]);

    // Both images should be accepted now, with warnings shown in the FileRenderer
    expect(result.errors).toHaveLength(0);
    expect(result.okFiles).toHaveLength(2);
    expect(result.okFiles).toContain(validImage);
    expect(result.okFiles).toContain(oversizedImage);
  });

  describe("hasLoadingImages (improved version)", () => {
    it("should return false for null or undefined input", () => {
      expect(FileController.hasLoadingImages(null)).toBe(false);
      expect(FileController.hasLoadingImages(undefined)).toBe(false);
    });

    it("should return false for empty content", () => {
      const emptyContent: JSONContent = { type: "doc", content: [] };
      expect(FileController.hasLoadingImages(emptyContent)).toBe(false);
    });

    it("should return false when no images are present", () => {
      const contentWithoutImages: JSONContent = {
        type: "doc",
        content: [
          {
            type: "paragraph",
            content: [{ type: "text", text: "This is a paragraph without images" }],
          },
        ],
      };
      expect(FileController.hasLoadingImages(contentWithoutImages)).toBe(false);
    });

    it("should return false when images are present but not loading", () => {
      const contentWithNonLoadingImages: JSONContent = {
        type: "doc",
        content: [
          {
            type: "paragraph",
            content: [{ type: "text", text: "This is a paragraph with an image: " }],
          },
          {
            type: "file",
            attrs: { src: "image.jpg", isLoading: false },
          },
        ],
      };
      expect(FileController.hasLoadingImages(contentWithNonLoadingImages)).toBe(false);
    });

    it("should return true when at least one image is loading", () => {
      const contentWithLoadingImage: JSONContent = {
        type: "doc",
        content: [
          {
            type: "paragraph",
            content: [{ type: "text", text: "This is a paragraph with a loading image: " }],
          },
          {
            type: "file",
            attrs: { src: "image.jpg", isLoading: true },
          },
        ],
      };
      expect(FileController.hasLoadingImages(contentWithLoadingImage)).toBe(true);
    });

    it("should handle content with missing attrs property", () => {
      const contentWithMissingAttrs: JSONContent = {
        type: "doc",
        content: [
          {
            type: "paragraph",
            content: [{ type: "text", text: "This is a paragraph" }],
          },
          {
            type: "file", // Missing attrs property
          },
        ],
      };
      expect(FileController.hasLoadingImages(contentWithMissingAttrs)).toBe(false);
    });

    it("should handle content with empty attrs object", () => {
      const contentWithEmptyAttrs: JSONContent = {
        type: "doc",
        content: [
          {
            type: "paragraph",
            content: [{ type: "text", text: "Paragraph" }],
          },
          {
            type: "file",
            attrs: {}, // Empty attrs object
          },
        ],
      };
      expect(FileController.hasLoadingImages(contentWithEmptyAttrs)).toBe(false);
    });

    it("should handle malformed content gracefully", () => {
      // @ts-expect-error - Testing with invalid input structure
      expect(FileController.hasLoadingImages({ type: "doc", content: null })).toBe(false);
      // @ts-expect-error - Testing with invalid input structure
      expect(FileController.hasLoadingImages({ type: "doc", content: "not an array" })).toBe(false);
    });
  });

  test("processImage sets fileSizeBytes attribute", async () => {
    const validImageFile = new File(["test content"], "test.jpg", { type: "image/jpeg" });
    const mockScaledBlob = new Blob(["scaled content"], { type: "image/jpeg" });

    // Mock readImageAsBlob to return a blob with known size
    vi.spyOn(readImageUtil, "readImageAsBlob").mockImplementation(() =>
      Promise.resolve(mockScaledBlob),
    );

    const result = fileController.insertFilesIntoEditor([validImageFile]);
    expect(result.errors).toHaveLength(0);
    expect(result.okFiles).toHaveLength(1);

    // Wait for the image processing to complete
    await waitFor(() => {
      expect(mockSaveImage).toHaveBeenCalled();
    });

    // Verify that the editor command was called with fileSizeBytes
    await waitFor(() => {
      // eslint-disable-next-line @typescript-eslint/unbound-method
      expect(editor.chain).toHaveBeenCalled();
      // The command should have been called with the file size
      const commandCalls = (editor.chain as Mock).mock.calls;
      expect(commandCalls.length).toBeGreaterThan(0);
    });
  });
});

function createMockEditor(): Editor {
  const editor = {
    commands: {
      setImage: vi.fn((): ChainedCommands => chainedCommands),
    },
    state: {
      doc: {
        descendants: vi.fn(),
        content: { size: 0 },
      },
      selection: { from: 0 },
    },
    schema: {
      nodes: {
        file: { create: vi.fn() },
        paragraph: { create: vi.fn() }, // Add the missing paragraph node
      },
    },
    chain: vi.fn((): ChainedCommands => chainedCommands),
    destroy: vi.fn(),
  };
  chainedCommands = {
    ...editor.commands,
    run: vi.fn(),
    command: vi.fn((cb) => {
      cb({
        tr: { insert: vi.fn(), doc: editor.state.doc },
        state: editor.state,
      });
      return chainedCommands;
    }),
    focus: vi.fn().mockReturnThis(),
  } as unknown as ChainedCommands;
  return editor as unknown as Editor;
}
