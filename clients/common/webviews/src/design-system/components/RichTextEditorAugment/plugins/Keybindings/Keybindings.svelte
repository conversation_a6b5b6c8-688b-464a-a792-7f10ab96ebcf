<script lang="ts">
  import { KeybindingsController } from "./controller";
  import { getContext, onDestroy } from "svelte";
  import { RichTextEditorContext } from "../../context";
  import type { Command } from "prosemirror-state";

  /**
   * This component handles the registration and management of keyboard shortcuts for the editor.
   * It does not render anything by itself, but instead exposes a declarative, component-like
   * API for defining keyboard shortcuts.
   *
   * ********************************
   * *** GOTCHA FOR API CONSUMERS ***
   * ********************************
   * The keybindings here *must* conform to the Keyboard Event Key Values
   * https://developer.mozilla.org/en-US/docs/Web/API/KeyboardEvent/key/Key_Values
   * If you are adding a key, make sure to only use keys defined here!
   * Otherwise, please use the *literal* key value as defined on a standard English
   * keyboard.
   *
   * For example, the backslash key is represented as "\\" here, not "Backslash".
   * This is because "Backslash" is not a valid key value for the KeyboardEvent.key property.
   */

  export let shortcuts: Record<string, Command> = {};

  // Register the keybindings controller with the editor context
  const editorContext = getContext<RichTextEditorContext>(RichTextEditorContext.CONTEXT_KEY);
  const keybindingsController = new KeybindingsController({ shortcuts });
  const cleanupPlugin = editorContext.pluginManager.registerPlugin(keybindingsController);
  onDestroy(cleanupPlugin);

  // Update the keybindings controller when the keyboardShortcuts prop changes
  $: keybindingsController.updateOptions({ shortcuts });
</script>
