<script lang="ts" generics="TOption extends IMentionable">
  import { getContext } from "svelte";
  import type { IMentionable } from "./types";
  import { MentionPluginContext } from "./context";
  import DropdownMenuItem from "../../../DropdownMenuAugment/Item.svelte";

  /**
   * This component is responsible for rendering a single mentionable item in the dropdown menu.
   * It provides the necessary context and event handling for the mentionable item.
   *
   * The API consumer is expected to:
   * - Provide the mentionable item data via the `mentionable` prop
   * - Provide the content to render for the mentionable item via the default slot
   *
   * Usage:
   * <Mention.Menu.Item {mentionable}>
   *   {mentionable.label}
   * </Mention.Menu.Item>
   */

  const mentionContext = getContext<MentionPluginContext<TOption>>(
    MentionPluginContext.CONTEXT_KEY,
  );
  const { replaceQueryWithMentionNode, activeItem } = mentionContext.mentionableMenuContext;

  export let mentionable: TOption;
</script>

<DropdownMenuItem
  onSelect={() => replaceQueryWithMentionNode(mentionable)}
  highlight={$activeItem === mentionable}
>
  <slot />
</DropdownMenuItem>
