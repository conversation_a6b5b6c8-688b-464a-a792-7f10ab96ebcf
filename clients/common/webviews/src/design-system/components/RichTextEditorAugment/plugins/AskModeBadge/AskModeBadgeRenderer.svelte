<script lang="ts">
  /**
   * AskModeBadgeRenderer Component
   *
   * This component renders the visual representation of the ask mode badge
   * using the proper BadgeAugment.Root component structure.
   */
  import BadgeAugment from "$common-webviews/src/design-system/components/BadgeAugment";
  import MessageIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/message-lines.svg?component";

  // Props
  export let isEditable: boolean = true;
</script>

<div class="c-ask-mode-badge" class:c-ask-mode-badge--block={!isEditable}>
  <BadgeAugment.Root color="accent" size={1} variant="soft">
    <MessageIcon />
    Ask a Question
  </BadgeAugment.Root>
</div>

<style>
  .c-ask-mode-badge {
    display: inline-block;
    position: relative;
    margin-bottom: var(--ds-spacing-1);
    bottom: -1px;
    user-select: none;
  }

  .c-ask-mode-badge--block {
    display: block;
    margin-right: 0;
    margin-bottom: var(--ds-spacing-2);
  }
</style>
