/**
 * Utility functions for file type detection and manipulation
 */

import { ImageFormatType } from "@augment-internal/sidecar-libs/src/chat/chat-types";
export { ImageFormatType };

/**
 * Known image file extensions
 */
export const IMAGE_EXTENSIONS = [
  "jpg",
  "jpeg",
  "png",
  "gif",
  "webp",
  "svg",
  "bmp",
  "tiff",
  "ico",
] as const;

/**
 * Common binary file extensions that should not be processed as text
 */
export const COMMON_BINARY_EXTENSIONS = [
  // Archives
  "zip",
  "tar",
  "gz",
  "7z",
  "rar",
  // Documents
  "pdf",
  "doc",
  "docx",
  "ppt",
  "pptx",
  "xls",
  "xlsx",
  "odt",
  "odp",
  "ods",
  // Executables & Libraries
  "exe",
  "dll",
  "so",
  "dylib",
  "app",
  "msi",
  "deb",
  "rpm",
  // Compiled code / Intermediates
  "o",
  "a",
  "class",
  "jar",
  "pyc",
  "wasm",
  // Media (non-image)
  "mp3",
  "mp4",
  "avi",
  "mov",
  "wav",
  "mkv",
  // Other
  "DS_Store",
  "db",
  "sqlite",
  "dat",
] as const;

/**
 * 1MB threshold for diff display
 */
export const MAX_FILE_SIZE_FOR_DIFF_BYTES = 1024 * 1024;

/**
 * Extract file extension from filename
 *
 * @param filename The filename to extract extension from
 * @returns The file extension in uppercase, or empty string if no extension
 */
export function getFileExtension(filename: string): string {
  const lastDot = filename.lastIndexOf(".");
  if (lastDot === -1 || lastDot === filename.length - 1) {
    return "";
  }
  return filename.substring(lastDot + 1).toUpperCase();
}

/**
 * Get the lowercase file extension from a filename
 *
 * @param filename The filename to extract extension from
 * @returns The file extension in lowercase, or empty string if no extension
 */
export function getFileExtensionLowercase(filename: string): string {
  const lastDot = filename.lastIndexOf(".");
  if (lastDot === -1 || lastDot === filename.length - 1) {
    return "";
  }
  return filename.substring(lastDot + 1).toLowerCase();
}

/**
 * Determine if a file is likely an image based on its filename extension
 *
 * @param filename The filename to check
 * @returns True if the file appears to be an image
 */
export function isImageFile(filename: string): boolean {
  const ext = getFileExtensionLowercase(filename);
  return IMAGE_EXTENSIONS.includes(ext as (typeof IMAGE_EXTENSIONS)[number]);
}

/**
 * Convert a filename to an ImageFormatType enum value
 *
 * @param filename The filename including its extension
 * @returns The corresponding ImageFormatType enum value
 */
export function fileNameToImageFormat(filename: string): ImageFormatType {
  const extension = getFileExtensionLowercase(filename);
  switch (extension) {
    case "jpeg":
    case "jpg":
      return ImageFormatType.JPEG;
    case "png":
      return ImageFormatType.PNG;
    case "gif":
      return ImageFormatType.GIF;
    case "webp":
      return ImageFormatType.WEBP;
    default:
      return ImageFormatType.IMAGE_FORMAT_UNSPECIFIED;
  }
}

/**
 * Get MIME type for a file based on its extension
 *
 * @param filePath The file path to get MIME type for
 * @returns The MIME type string
 */
export function getMimeType(filePath: string): string {
  const extension = getFileExtensionLowercase(filePath);
  switch (extension) {
    case "png":
      return "image/png";
    case "jpg":
    case "jpeg":
      return "image/jpeg";
    case "gif":
      return "image/gif";
    case "svg":
      return "image/svg+xml";
    case "webp":
      return "image/webp";
    case "bmp":
      return "image/bmp";
    case "ico":
      return "image/x-icon";
    // Add more common image types if needed
    default:
      return "application/octet-stream"; // Generic binary type
  }
}

/**
 * Check if a file is a known binary file type
 *
 * @param filePath The file path to check
 * @returns True if the file is a known binary type (excluding images)
 */
export function isKnownBinary(filePath: string): boolean {
  if (isImageFile(filePath)) {
    return false; // Images are handled separately
  }
  const extension = getFileExtensionLowercase(filePath);
  return COMMON_BINARY_EXTENSIONS.includes(extension as (typeof COMMON_BINARY_EXTENSIONS)[number]);
}

/**
 * Check if a file is too large for diff display
 *
 * @param contentLength The content length in bytes
 * @returns True if the file is too large for diff display
 */
export function isTooLargeForDiff(contentLength: number): boolean {
  return contentLength > MAX_FILE_SIZE_FOR_DIFF_BYTES;
}
