import type { AnyExtension, J<PERSON>NContent, Node } from "@tiptap/core";
import type { KeyboardShortcutCommand } from "@tiptap/core";

/**
 * A plugin for the RichTextEditorAugment component.
 *
 * Specifically, a plugin should expose a `tipTapExtension` property. This, when registered
 * using `registerPlugin`, will be passed to the TipTap editor and is the preferred way for
 * our plugins to interact with TipTap and ProseMirror.
 *
 * Each of our plugins is responsible for managing its own state relative to the TipTap/ProseMirror state.
 * To do so, the `registerPlugin` function has a set of hooks that a plugin can register.
 */
export interface IRichTextEditorPlugin {
  tipTapExtension: AnyExtension;
  textSerializers?: { [key: string]: ({ node }: { node: Node }) => string };
}

export interface IRichTextEditorOptions {
  editable?: boolean;
  focusOnInit?: boolean;
  /** Custom keyboard shortcuts to add to the editor */
  keyboardShortcuts?: Record<string, KeyboardShortcutCommand>;
  onFocus?: () => void;
  onBlur?: () => void;
}

export type ISetupFn = () => unknown;
export type IDisposeFn = () => void;

export interface ICursorPrefixData {
  prefix: string;
  offsetStart: number;
  offsetEnd: number;
}

export interface ICursorSuffixData {
  suffix: string;
  offsetStart: number;
  offsetEnd: number;
}

export type ContentData = {
  richTextJsonRepr: JSONContent | JSONContent[];
  rawText: string;
};
