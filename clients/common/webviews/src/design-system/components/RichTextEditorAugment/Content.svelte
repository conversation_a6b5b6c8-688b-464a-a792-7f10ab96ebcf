<script lang="ts">
  import { getContext, onDestroy, onMount } from "svelte";
  import { RichTextEditorContext } from "./context";
  import { type JSONContent } from "@tiptap/core";
  import { type ContentData } from "./types";
  import { setupPasteSizeLimit } from "$common-webviews/src/common/utils/paste-limits";
  import { writable } from "svelte/store";
  import CalloutAugment from "$common-webviews/src/design-system/components/CalloutAugment.svelte";
  import ExclamationTriangleIcon from "$common-webviews/src/design-system/icons/exclamation-triangle.svelte";

  /**
   * This component is the entrypoint for content in the rich text editor.
   * - It does not render anything itself, but exposes a component-based declarative API
   *   to control the content of the editor
   * - This component updates the rich text editor's content whenever the `content` prop changes
   * - It also exposes an event for when the content of the editor changes
   *
   * Events API:
   * - onContentChanged: Called when the content of the editor changes
   *
   * Props API:
   * - content: The content to display in the editor
   *    - If `content === undefined`, the editor is uncontrolled
   *    - If `content !== undefined`, the editor is controlled
   *    - Do not use this prop unless you intend to control the content of the editor
   *
   */

  export let onContentChanged: (content: ContentData) => void = () => {};
  export let content: string | JSONContent | JSONContent[] = "";

  // State for paste error handling
  const showPasteError = writable(false);
  const pasteErrorMessage = writable("");
  let pasteErrorTimeout: number | undefined;

  // Get the RichTextEditorContext
  const context = getContext<RichTextEditorContext>(RichTextEditorContext.CONTEXT_KEY);

  /**
   * Forward content changes to the `onContentChanged` prop
   *
   * We store the dispose function in this way for cleanup in the following situation
   * - The `onContentChanged` prop changes
   * - Clean up the previous subscription, since it is no longer needed
   * - Register a new subscription with the new callback
   * - This will notify the new subscription *immediately* of the current content state,
   *    which allows the subscriber to get the current state without waiting for a change
   */
  let dispose: (() => void) | undefined = undefined;
  $: {
    dispose?.();
    dispose = context.eventManager.onContentChanged(onContentChanged);
  }

  // Forward content prop changes to the editor
  $: context.commandManager.setContent(content);

  // Set up paste size limit
  let cleanupPasteSizeLimit: (() => void) | undefined;

  onMount(() => {
    cleanupPasteSizeLimit = setupPasteSizeLimit({
      onExceedsPasteLimit: (pasteSize, maxSize) => {
        $pasteErrorMessage = `Cannot paste: Content exceeds ${maxSize.toLocaleString()} character limit (${pasteSize.toLocaleString()} characters)`;
        $showPasteError = true;

        // Auto-hide after 3 seconds
        if (pasteErrorTimeout) {
          clearTimeout(pasteErrorTimeout);
        }

        pasteErrorTimeout = window.setTimeout(() => {
          $showPasteError = false;
        }, 3000);
      },
    });
  });

  onDestroy(() => {
    if (cleanupPasteSizeLimit) {
      cleanupPasteSizeLimit();
    }

    clearTimeout(pasteErrorTimeout);
  });
</script>

{#if $showPasteError}
  <div class="paste-error-container">
    <CalloutAugment variant="soft" color="error" size={1}>
      <ExclamationTriangleIcon slot="icon" />
      {$pasteErrorMessage}
    </CalloutAugment>
  </div>
{/if}

<style>
  /* Remove top and bottom spacing from the first and last child */
  :global(.tiptap > *:first-child) {
    margin-top: 0;
    padding-top: 0;
  }

  :global(.tiptap > *:last-child) {
    margin-bottom: 0;
    padding-bottom: 0;
  }

  .paste-error-container {
    position: absolute;
    bottom: 100%; /* Position it just above the chat input */
    left: 50%;
    margin-bottom: var(--ds-spacing-2);
    transform: translateX(-50%);
    z-index: var(--z-floating);
    width: 100%;
  }
</style>
