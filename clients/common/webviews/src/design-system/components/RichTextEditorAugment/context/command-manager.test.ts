import { CommandManager } from "./command-manager";
import type { Editor, JSONContent } from "@tiptap/core";
import { expect, it, describe, vi, beforeEach, afterEach } from "vitest";
import { get, type Writable, writable } from "svelte/store";
import { type ContentData } from "../types";

/**
 * This test suite covers the CommandManager, which is responsible for managing
 * editor commands in a TipTap-based rich text editor. The CommandManager allows
 * for late initialization of the editor, queuing commands until the editor is
 * ready. This approach enables a more flexible setup process for the editor.
 *
 * The tests are structured to verify three main scenarios:
 * 1. Commands are queued when the editor is not yet registered
 * 2. Queued commands are executed when the editor is registered late
 * 3. Commands are executed immediately when the editor is already registered
 * 4. Specific command tests
 *    - If setContent is called with the same content as the current content, it should not be called again
 *
 * These tests ensure that the CommandManager behaves correctly in various
 * initialization scenarios, providing a robust interface for editor interactions.
 */

let kit: CommandManagerTestKit;

beforeEach(() => {
  kit = new CommandManagerTestKit();
});

afterEach(() => {
  vi.resetAllMocks();
});

describe("CommandManager", () => {
  it("nothing is called when editor is not registered", () => {
    kit.commandManager.focus();
    kit.commandManager.blur();
    kit.commandManager.scrollToCursor();
    kit.commandManager.setEditable(true);
    kit.commandManager.setContent("test");
    kit.commandManager.insertContent("test");
    kit.commandManager.clearContent();

    expect(kit.commandManager["_setupFns"]).toHaveLength(7);
  });

  it("commands are called when editor is registered late", () => {
    // Call commands
    kit.commandManager.focus();
    kit.commandManager.blur();
    kit.commandManager.scrollToCursor();
    kit.commandManager.setEditable(true);
    kit.commandManager.setContent("test");
    kit.commandManager.insertContent("test");
    kit.commandManager.clearContent();

    // Register editor
    kit.commandManager.registerEditor(kit.editor);

    // Verify all commands were called
    expect(kit.commandManager["_setupFns"]).toHaveLength(0);
    expect(kit.editor.commands.focus).toHaveBeenCalled();
    expect(kit.editor.commands.blur).toHaveBeenCalled();
    expect(kit.editor.commands.scrollIntoView).toHaveBeenCalled();
    expect(kit.editor["setEditable"]).toHaveBeenCalledWith(true);
    expect(kit.editor.commands.setContent).toHaveBeenCalled();
    expect(kit.editor.commands.insertContent).toHaveBeenCalled();
    expect(kit.editor.commands.clearContent).toHaveBeenCalled();
  });

  it("commands are called when editor is registered early", () => {
    // Register editor
    kit.commandManager.registerEditor(kit.editor);

    // Call commands
    kit.commandManager.focus();
    kit.commandManager.blur();
    kit.commandManager.scrollToCursor();
    kit.commandManager.setEditable(true);
    kit.commandManager.setContent("test");
    kit.commandManager.insertContent("test");
    kit.commandManager.clearContent();

    // Verify all commands were called
    expect(kit.editor.commands.focus).toHaveBeenCalled();
    expect(kit.editor.commands.blur).toHaveBeenCalled();
    expect(kit.editor.commands.scrollIntoView).toHaveBeenCalled();
    expect(kit.editor["setEditable"]).toHaveBeenCalledWith(true);
    expect(kit.editor.commands.setContent).toHaveBeenCalled();
    expect(kit.editor.commands.insertContent).toHaveBeenCalled();
    expect(kit.editor.commands.clearContent).toHaveBeenCalled();
  });

  it("handles different content types correctly", () => {
    kit.commandManager.registerEditor(kit.editor);

    const jsonContent = { type: "paragraph", content: [{ type: "text", text: "test" }] };
    const jsonArray = [jsonContent];

    kit.commandManager.setContent("string content");
    kit.commandManager.setContent(jsonContent);
    kit.commandManager.setContent(jsonArray);

    kit.commandManager.insertContent("string content");
    kit.commandManager.insertContent(jsonContent);
    kit.commandManager.insertContent(jsonArray);

    expect(kit.editor.commands.setContent).toHaveBeenCalledTimes(3);
    expect(kit.editor.commands.insertContent).toHaveBeenCalledTimes(3);
  });

  it("does not clear content if content is already empty", () => {
    kit.commandManager.registerEditor(kit.editor);

    // Content should be empty at the start
    expect(kit.editor.commands.clearContent).not.toHaveBeenCalled();
    expect(kit.editor.commands.setContent).not.toHaveBeenCalled();

    // Clear content, should not be called
    kit.commandManager.clearContent();
    expect(kit.editor.commands.clearContent).not.toHaveBeenCalled();
    expect(kit.editor.commands.setContent).not.toHaveBeenCalled();

    // Set content to empty, should also not be called
    kit.commandManager.setContent("");
    expect(kit.editor.commands.clearContent).not.toHaveBeenCalled();
    expect(kit.editor.commands.setContent).not.toHaveBeenCalled();
  });

  it("does not set content if content is the same as the current content", () => {
    kit.commandManager.registerEditor(kit.editor);
    kit.commandManager.setContent("test");

    // Expect content to have been set
    expect(kit.editor.commands.setContent).toHaveBeenCalledTimes(1);
    expect(kit.richTextJsonRepr).toBeDefined();

    // Set the content to the current content
    kit.commandManager.setContent(kit.richTextJsonRepr!);
    expect(kit.editor.commands.setContent).toHaveBeenCalledTimes(1);
  });
});

/**
 * TestKit for CommandManager
 */
class CommandManagerTestKit {
  public readonly commandManager: CommandManager;
  public readonly editor: Editor;
  public readonly contentStore: Writable<ContentData | undefined> = writable(undefined);

  constructor() {
    this.editor = this._getMockEditor();
    this.commandManager = new CommandManager({
      content: this.contentStore,
    });
  }

  public get richTextJsonRepr(): JSONContent | undefined {
    return get(this.contentStore)?.richTextJsonRepr;
  }

  private _getMockEditor(): Editor {
    const editor = {
      chain: vi.fn(() => ({
        setContent: vi.fn((...args: Parameters<Editor["commands"]["setContent"]>) => {
          editor.commands.setContent(...args);
          return editor.chain();
        }),
        setTextSelection: vi.fn((...args: Parameters<Editor["commands"]["setTextSelection"]>) => {
          editor.commands.setTextSelection(...args);
          return editor.chain();
        }),
        run: vi.fn(),
      })),
      run: vi.fn(),
      commands: {
        focus: vi.fn(),
        blur: vi.fn(),
        scrollIntoView: vi.fn(),
        clearContent: vi.fn(() => this.contentStore.set(undefined)),
        setContent: vi.fn((content: JSONContent | JSONContent[]) =>
          this.contentStore.set({
            richTextJsonRepr: content,
            rawText: "SOMETHING DIFFERENT", // This is unused
          }),
        ),
        setTextSelection: vi.fn(),
        insertContent: vi.fn(),
      },
      setEditable: vi.fn(),
      state: { doc: { content: { size: 10 } } },
    } as unknown as Editor;
    return editor;
  }
}
