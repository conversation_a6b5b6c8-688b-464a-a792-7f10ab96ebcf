import type { <PERSON><PERSON><PERSON>ontent } from "@tiptap/core";
import type { ICursorPrefixData, ICursorSuffixData } from "../types";
import type { EditorState } from "prosemirror-state";

/**
 * Gets all the text before (to the left of) the cursor.
 *
 * @param editor The TipTap editor instance
 * @returns An object containing the text and its position
 */
export function getPrefix(editor: EditorState): ICursorPrefixData {
  // Check the reference position before the current selection
  let referencePos: number;
  if (editor.selection.empty) {
    referencePos = editor.selection.anchor;
  } else {
    // This gives us the left-most position of the selection, regardless of anchor/head order
    // (since selections can go backwards)
    referencePos = Math.min(editor.selection.anchor, editor.selection.head);
  }
  return {
    prefix: editor.doc.textBetween(0, referencePos),
    offsetStart: 0,
    offsetEnd: referencePos,
  };
}

/**
 * Gets all the text after (to the right of) the cursor.
 *
 * @param editor The TipTap editor instance
 * @returns An object containing the text and its position
 */
export function getSuffix(editor: EditorState): ICursorSuffixData {
  // Check the reference position before the current selection
  let referencePos: number;
  if (editor.selection.empty) {
    referencePos = editor.selection.anchor;
  } else {
    // This gives us the left-most position of the selection, regardless of anchor/head order
    // (since selections can go backwards)
    referencePos = Math.min(editor.selection.anchor, editor.selection.head);
  }
  return {
    // Subtract 2 from nodeSize to exclude ProseMirror's document node boundaries
    // (every doc has opening and closing positions that we don't include in size calculations)
    // `nodeSize = contentSize + 2` for all non-leaf nodes, so to get `contentSize` we subtract 2
    suffix: editor.doc.textBetween(referencePos, editor.doc.nodeSize - 2),
    offsetStart: referencePos,
    offsetEnd: editor.doc.nodeSize - 2,
  };
}

/**
 * Converts raw text to JSONContent array.
 * @param rawText - The input string to convert.
 * @returns An array of JSONContent objects or an empty array if input is empty.
 */
function _convertStringToJSONContent(rawText: string): JSONContent[] {
  return rawText.length === 0 ? [] : [{ type: "text", text: rawText }];
}

/**
 * Formats raw text into JSONContent array, preserving line breaks.
 * @param rawText - The input string to format.
 * @returns An array of JSONContent objects representing the formatted text.
 */
function _formatRawText(rawText: string): JSONContent[] {
  return rawText
    .split("\n")
    .map((line: string, index: number) => {
      // First line does not need a break
      if (index === 0) {
        return _convertStringToJSONContent(line);
      }

      // Subsequent lines need a break before them
      return [{ type: "hardBreak" }, ..._convertStringToJSONContent(line)];
    })
    .flat();
}

/**
 * Normalizes content to a consistent format.
 * @param content - The input content to normalize.
 * @returns Normalized content as string, JSONContent, or undefined.
 */
export function normalizeContent(content: undefined): undefined;
export function normalizeContent(content: string): JSONContent[] | undefined;
export function normalizeContent(content: JSONContent): JSONContent | undefined;
export function normalizeContent(content: JSONContent[]): JSONContent[] | undefined;
// Catch-all signature, which does not narrow the type of the argument => return mapping
export function normalizeContent(
  content: string | JSONContent | JSONContent[] | undefined,
): JSONContent | JSONContent[] | undefined;
export function normalizeContent(
  content: string | JSONContent | JSONContent[] | undefined,
): JSONContent | JSONContent[] | undefined {
  if (content === undefined) {
    return undefined;
  } else if (typeof content === "string") {
    const contentArray = _formatRawText(content);
    return contentArray.length > 0 ? contentArray : undefined;
  } else if (!Array.isArray(content)) {
    return content;
  }

  return content.length > 0 ? content : undefined;
}

// Expose these for testing
// eslint-disable-next-line @typescript-eslint/naming-convention
export const TEST_ONLY_FNS = {
  _convertStringToJSONContent,
  _formatRawText,
};
