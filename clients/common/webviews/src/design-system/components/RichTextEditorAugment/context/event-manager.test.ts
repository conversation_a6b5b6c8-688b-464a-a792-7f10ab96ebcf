import type { Editor } from "@tiptap/core";
import { EventManager } from "./event-manager";
import { expect, it, describe, vi, beforeEach, type Mock } from "vitest";

let eventManager: EventManager;
beforeEach(() => {
  eventManager = new EventManager();
});

describe("EventManager", () => {
  it("focus and blur events are tracked if registered before editor", () => {
    const onFocus = vi.fn();
    const onBlur = vi.fn();

    // Register listeners before editor
    eventManager.onFocus(onFocus);
    eventManager.onBlur(onBlur);

    // Expect initial state to notify blur but not focus, as initial state is not focused
    expect(onFocus).not.toHaveBeenCalled();
    expect(onBlur).toHaveBeenCalled();

    // Register editor, now focus is true
    vi.resetAllMocks();
    const mockEditor = getMockEditor({ isFocused: true, isEditable: true });
    eventManager.registerEditor(mockEditor as unknown as Editor);
    expect(onFocus).toHaveBeenCalled();
    expect(onBlur).not.toHaveBeenCalled();

    // Unregister editor, now focus is false
    eventManager.unregisterEditor();
    expect(onFocus).toHaveBeenCalledTimes(1);
    expect(onBlur).toHaveBeenCalled();
  });

  it("editable events are tracked if registered before editor", () => {
    const onEditable = vi.fn();

    // Register listener before editor
    eventManager.onEditableChanged(onEditable);

    // Expect initial state to notify editable is false
    expect(onEditable).toHaveBeenCalledWith(false);

    // Register editor, now editable is true
    vi.resetAllMocks();
    const mockEditor = getMockEditor({ isFocused: true, isEditable: true });
    eventManager.registerEditor(mockEditor as unknown as Editor);
    expect(onEditable).toHaveBeenCalledWith(true);
  });

  it("focus and blur events are tracked if registered after editor", () => {
    const onFocus = vi.fn();
    const onBlur = vi.fn();

    // Register editor, now focus is true
    const mockEditor = getMockEditor({ isFocused: true, isEditable: true });
    eventManager.registerEditor(mockEditor as unknown as Editor);
    expect(onFocus).not.toHaveBeenCalled();
    expect(onBlur).not.toHaveBeenCalled();

    // Register listeners after editor
    eventManager.onFocus(onFocus);
    eventManager.onBlur(onBlur);

    // Expect initial state to notify focus but not blur, as initial state is focused
    expect(onFocus).toHaveBeenCalled();
    expect(onBlur).not.toHaveBeenCalled();
  });

  it("editable events are tracked if registered after editor", () => {
    const onEditable = vi.fn();

    // Register editor, now editable is true
    vi.resetAllMocks();
    const mockEditor = getMockEditor({ isFocused: true, isEditable: true });
    eventManager.registerEditor(mockEditor as unknown as Editor);
    expect(onEditable).not.toHaveBeenCalled();

    // Register listener after editor
    eventManager.onEditableChanged(onEditable);

    // Expect initial state to notify editable is true
    expect(onEditable).toHaveBeenCalledWith(true);
  });

  it("cleanup is called on unregister", () => {
    const onEditable = vi.fn();
    const onFocus = vi.fn();
    const onBlur = vi.fn();

    // Register editor, now editable is true
    vi.resetAllMocks();
    const mockEditor = getMockEditor({ isFocused: true, isEditable: true });
    eventManager.registerEditor(mockEditor as unknown as Editor);

    // Get disposers and attach mocks to them
    const mockDisposers = (eventManager as unknown as EventManager)["_disposers"].map(
      (fn: () => void) => vi.fn(fn),
    );
    (eventManager as unknown as EventManager)["_disposers"] = mockDisposers;

    // Register listeners
    eventManager.onFocus(onFocus);
    eventManager.onBlur(onBlur);
    eventManager.onEditableChanged(onEditable);

    // Unregister editor
    eventManager.unregisterEditor();

    // Expect disposers to be called on all disposers, and for them to be cleared
    mockDisposers.forEach((fn: Mock) => expect(fn).toHaveBeenCalled());
    expect((eventManager as unknown as EventManager)["_disposers"]).toHaveLength(0);
  });
});

function getMockEditor(override: Record<string, unknown> = {}) {
  return {
    isFocused: false,
    isEditable: false,
    on: vi.fn(),
    off: vi.fn(),
    getJSON: vi.fn(),
    getText: vi.fn(),
    ...override,
  };
}
