import { describe, it, expect, beforeEach } from "vitest";
import { RichTextEditorContext } from "./index";
import type { IRichTextEditorOptions } from "../types";
import { get } from "svelte/store";

describe("RichTextEditorContext", () => {
  let context: RichTextEditorContext;
  let mockRootNode: HTMLElement;

  beforeEach(() => {
    context = new RichTextEditorContext();
    mockRootNode = document.createElement("div");
  });

  describe("Static Properties", () => {
    it("should have correct CONTEXT_KEY", () => {
      expect(RichTextEditorContext.CONTEXT_KEY).toBe("augment-rich-text-editor");
    });
  });

  describe("Subsystem Access", () => {
    it("should provide access to plugin manager", () => {
      expect(context.pluginManager).toBeDefined();
    });

    it("should provide access to command manager", () => {
      expect(context.commandManager).toBeDefined();
    });

    it("should provide access to event manager", () => {
      expect(context.eventManager).toBeDefined();
    });
  });

  describe("Root Registration", () => {
    it("should register root and return action handlers", () => {
      const opts: IRichTextEditorOptions = { editable: true };
      const action = context.registerRoot(mockRootNode, opts);

      expect(action).toBeDefined();
      expect(action?.update).toBeDefined();
      expect(action?.destroy).toBeDefined();
    });

    it("should handle option updates through action handler", () => {
      const initialOpts: IRichTextEditorOptions = { editable: true };
      const action = context.registerRoot(mockRootNode, initialOpts);

      const newOpts: IRichTextEditorOptions = { editable: false };
      expect(() => action?.update?.(newOpts)).not.toThrow();
      expect(get(context["_opts"])).toEqual(newOpts);
    });

    it("should handle cleanup through action handler", () => {
      const opts: IRichTextEditorOptions = { editable: true };
      const action = context.registerRoot(mockRootNode, opts);

      expect(() => action?.destroy?.()).not.toThrow();

      // Root node should *not* be cleared -- always default to the last node
      // registered, since we may sometimes re-initialize
      expect(context["_rootNode"]).toBe(mockRootNode);
      expect(context["_editor"]).toBeUndefined();
    });
  });

  describe("Plugin Manager Integration", () => {
    it("should allow access to plugin manager after initialization", () => {
      const opts: IRichTextEditorOptions = { editable: true };
      context.registerRoot(mockRootNode, opts);

      expect(context.pluginManager).toBeDefined();
      expect(context.pluginManager.tipTapExtensions).toBeDefined();
      expect(context.pluginManager.tipTapExtensions.length).toBeGreaterThan(0);
    });
  });

  describe("Command Manager Integration", () => {
    it("should allow access to command manager after initialization", () => {
      const opts: IRichTextEditorOptions = { editable: true };
      context.registerRoot(mockRootNode, opts);

      expect(context.commandManager).toBeDefined();
    });
  });

  describe("Event Manager Integration", () => {
    it("should allow access to event manager after initialization", () => {
      const opts: IRichTextEditorOptions = { editable: true };
      context.registerRoot(mockRootNode, opts);

      expect(context.eventManager).toBeDefined();
    });
  });
});
