import type { J<PERSON><PERSON>ontent } from "@tiptap/core";
import { getPrefix, getSuffix, normalizeContent, TEST_ONLY_FNS } from "./json-content-utils";
import { expect, it, describe } from "vitest";
import type { EditorState } from "prosemirror-state";

describe("convertStringToJSONContent", () => {
  it("should return an empty array for empty input", () => {
    expect(TEST_ONLY_FNS._convertStringToJSONContent("")).toEqual([]);
  });

  it("should return a JSONContent array for non-empty input", () => {
    expect(TEST_ONLY_FNS._convertStringToJSONContent("Hello, world!")).toEqual([
      { type: "text", text: "Hello, world!" },
    ]);
  });
});

// A parameterized test suite for formatRawText.
// Each test case is defined by the `FormatRawTextTestCase` interface.
describe("formatRawText", () => {
  type FormatRawTextTestCase = {
    name: string;
    input: string;
    expected: JSONContent[];
  };

  const testCases: FormatRawTextTestCase[] = [
    {
      name: "empty input",
      input: "",
      expected: [],
    },
    {
      name: "single line input",
      input: "Hello, world!",
      expected: [{ type: "text", text: "Hello, world!" }],
    },
    {
      name: "multiple line input",
      input: "Hello,\nworld!",
      expected: [
        { type: "text", text: "Hello," },
        { type: "hardBreak" },
        { type: "text", text: "world!" },
      ],
    },
    {
      name: "leading newline",
      input: "\nHello, world!",
      expected: [{ type: "hardBreak" }, { type: "text", text: "Hello, world!" }],
    },
    {
      name: "trailing newline",
      input: "Hello, world!\n",
      expected: [{ type: "text", text: "Hello, world!" }, { type: "hardBreak" }],
    },
    {
      name: "multiple newlines",
      input: "Hello,\n\nworld!",
      expected: [
        { type: "text", text: "Hello," },
        { type: "hardBreak" },
        { type: "hardBreak" },
        { type: "text", text: "world!" },
      ],
    },
    {
      name: "multiple newlines with leading and trailing",
      input: "\nHello,\n\nworld!\n",
      expected: [
        { type: "hardBreak" },
        { type: "text", text: "Hello," },
        { type: "hardBreak" },
        { type: "hardBreak" },
        { type: "text", text: "world!" },
        { type: "hardBreak" },
      ],
    },
  ];

  testCases.forEach((testCase) => {
    it(`should format ${testCase.name}`, () => {
      expect(TEST_ONLY_FNS._formatRawText(testCase.input)).toEqual(testCase.expected);
    });
  });
});

// A parameterized test suite for normalizeContent.
// Each test case is defined by the `NormalizeContentTestCase` interface.
describe("normalizeContent", () => {
  type NormalizeContentTestCase = {
    name: string;
    input: string | JSONContent | JSONContent[] | undefined;
    expected: string | JSONContent | undefined;
  };

  // Define test cases
  const testCases: NormalizeContentTestCase[] = [
    {
      name: "undefined input",
      input: undefined,
      expected: undefined,
    },
    {
      name: "empty string input",
      input: "",
      expected: undefined,
    },
    {
      name: "non-empty string input",
      input: "Hello, world!",
      expected: [{ type: "text", text: "Hello, world!" }],
    },
    {
      name: "empty JSONContent array input",
      input: [],
      expected: undefined,
    },
    {
      name: "non-empty JSONContent array input",
      input: [{ type: "text", text: "Hello, world!" }],
      expected: [{ type: "text", text: "Hello, world!" }],
    },
  ];

  testCases.forEach((testCase) => {
    it(`should normalize ${testCase.name}`, () => {
      expect(normalizeContent(testCase.input)).toEqual(testCase.expected);
    });
  });
});

describe("getPrefix", () => {
  it("should return an object with the character before the cursor and its position", () => {
    // Mock the editor object
    const editor = {
      selection: {
        empty: true,
        anchor: 5,
        head: 5,
      },
      doc: {
        textBetween: (_start: number, _end: number) => "a",
      },
    };

    // Call the function and check the result
    const result = getPrefix(editor as unknown as EditorState);
    expect(result).toEqual({ prefix: "a", offsetStart: 0, offsetEnd: 5 });
  });
});

describe("getSuffix", () => {
  it("should return an object with the character after the cursor and its position", () => {
    // Mock the editor object
    const editor = {
      selection: {
        empty: true,
        anchor: 5,
        head: 5,
      },
      doc: {
        textBetween: (_start: number, _end: number) => "abc",
        nodeSize: 10,
      },
    };

    // Call the function and check the result
    const result = getSuffix(editor as unknown as EditorState);
    expect(result).toEqual({ suffix: "abc", offsetStart: 5, offsetEnd: 8 });
  });
});
