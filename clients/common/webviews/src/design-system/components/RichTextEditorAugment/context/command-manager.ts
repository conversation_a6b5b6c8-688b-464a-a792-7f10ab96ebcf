import type { Editor, JSONContent } from "@tiptap/core";
import { normalizeContent } from "./json-content-utils";
import type { ISetupFn } from "../types";
import { get } from "svelte/store";
import type { CommandManagerOptions } from "./types";
import { tick } from "svelte";

/**
 * CommandManager: A wrapper for TipTap editor commands
 *
 * Key features:
 * 1. Supports late-initialized commands
 * 2. Ensures commands run after editor initialization
 * 3. Provides a curated set of supported commands
 */
export class CommandManager {
  private _setupFns: ISetupFn[] = [];
  private _editor: Editor | undefined;

  constructor(private _opts: CommandManagerOptions) {}

  /**
   * Register an editor instance for the command manager to control.
   * @param editor - The editor instance to register
   */
  public registerEditor = (editor: Editor) => {
    this._editor = editor;
    this._runSetupFns();
  };

  // Unregister the editor instance. This will prevent any further commands from being executed on
  // the editor by this command manager.
  public unregisterEditor = () => {
    this._editor = undefined;
  };

  public can = () => this._editor?.can();

  public chain = () => this._editor?.chain();

  public get commands() {
    return this._editor?.commands;
  }

  /**
   * Queue or immediately run a function based on editor initialization status
   * @param fn - Function to execute (should be argument-free; use closures for parameterization)
   */
  private _queueOrRun = (fn: ISetupFn) => {
    this._editor ? void fn() : this._setupFns.push(fn);
  };

  // Run all queued setup functions and clear the queue
  private _runSetupFns = () => {
    this._setupFns.forEach((fn) => void fn());
    this._setupFns = [];
  };

  // ==== Public Editor Commands ====
  //
  // These can be called before the editor is initialized.
  // If the editor is not initialized, the function will be queued to run when the editor is initialized.
  // If the editor is already initialized, the function will be run immediately.
  //
  public hide = () => this._queueOrRun(this._hide);
  public show = () => this._queueOrRun(this._show);
  public focus = () => this._queueOrRun(this._focus);
  // Focus the editor if either there is no active element, or the active element is inside the editor.
  // We should not steal focus from other elements if the user is actively interacting with them.
  public requestFocus = () => this._queueOrRun(this._requestFocus);
  public blur = () => this._queueOrRun(this._blur);
  public scrollToCursor = () => this._queueOrRun(this._scrollToCursor);
  public setEditable = (editable: boolean) => this._queueOrRun(() => this._setEditable(editable));
  public clearContent = () => this._queueOrRun(this._clearContent);
  public setContent = (
    content: string | JSONContent | JSONContent[],
    options?: { cursorPosition?: "start" | "end" },
  ) => this._queueOrRun(() => this._setContent(content, options));
  public insertContent = (content: string | JSONContent | JSONContent[]) =>
    this._queueOrRun(() => this._insertContent(content));

  // ==== Private Editor Commands ====
  //
  // If the editor is not initialized, these will have no effect.
  // For a guaranteed effect that will be queued, use the public functions.
  //
  private _hide = () => {
    if (this._editor) {
      this._editor.view.dom.style.display = "none";
    }
  };

  private _show = () => {
    if (this._editor) {
      this._editor.view.dom.style.removeProperty("display");
    }
  };

  private _focus = () => this._editor?.commands.focus();
  private _requestFocus = async () => {
    await tick(); // Allow other events to run first
    if (!this._editor) {
      return;
    }

    // Always focus the editor when requested, regardless of current focus
    this._editor.commands.focus();
  };
  private _blur = () => this._editor?.commands.blur();
  private _scrollToCursor = () => this._editor?.commands.scrollIntoView();
  private _setEditable = (editable: boolean) => {
    this._editor?.setEditable(editable);
    if (editable) {
      // Position cursor at the end of content
      this._editor?.commands.setTextSelection(this._editor.state.doc.content.size);
    }
  };
  private _clearContent = () => {
    const contentData = get(this._opts.content);
    if (!contentData || contentData.rawText === "") {
      return;
    }
    this._editor?.commands.clearContent(true);
  };
  private _setContent = (
    content: string | JSONContent | JSONContent[] | undefined,
    options?: { cursorPosition?: "start" | "end" },
  ) => {
    const contentData = get(this._opts.content);
    // Skip the update if the content is the same as the current content
    if (content === contentData?.rawText || content === contentData?.richTextJsonRepr) {
      return;
    }

    // Normalize content to a consistent format
    content = normalizeContent(content ?? []);
    if (content === undefined) {
      this._clearContent();
      return;
    }

    // Set the content
    this._editor
      ?.chain()
      .setContent(content, true, {
        preserveWhitespace: true,
      })
      .setTextSelection(options?.cursorPosition === "start" ? 0 : 1e20)
      .run();
  };
  private _insertContent = (content: string | JSONContent | JSONContent[]) => {
    const normalizedContent = normalizeContent(content);
    if (normalizedContent === undefined) {
      return;
    }

    this._editor?.commands.insertContent(normalizedContent, {
      parseOptions: { preserveWhitespace: true },
    });
  };
}
