import { PluginManager } from "./plugin-manager";
import { writable } from "svelte/store";
import { expect, it, describe, beforeEach, vi } from "vitest";
import { type IRichTextEditorPlugin } from "../types";

let pluginManager: PluginManager;
beforeEach(() => {
  pluginManager = new PluginManager(writable({}));
});

describe("PluginManager", () => {
  it("should register and dispose plugins", () => {
    const plugin1 = { tipTapExtension: {} } as IRichTextEditorPlugin;
    const plugin2 = { tipTapExtension: {} } as IRichTextEditorPlugin;

    const numInitPlugins = pluginManager.tipTapExtensions.length;
    const dispose1 = pluginManager.registerPlugin(plugin1);
    const dispose2 = pluginManager.registerPlugin(plugin2);
    expect(pluginManager.tipTapExtensions.length).toEqual(numInitPlugins + 2);
    expect(pluginManager.tipTapExtensions).toEqual(
      expect.arrayContaining([plugin1.tipTapExtension, plugin2.tipTapExtension]),
    );

    dispose1();

    expect(pluginManager.tipTapExtensions.length).toEqual(numInitPlugins + 1);
    expect(pluginManager.tipTapExtensions).toEqual(
      expect.arrayContaining([plugin2.tipTapExtension]),
    );

    dispose2();

    expect(pluginManager.tipTapExtensions.length).toEqual(numInitPlugins);
  });

  it("should notify on plugins change", () => {
    const plugin1 = { tipTapExtension: {} } as IRichTextEditorPlugin;
    const plugin2 = { tipTapExtension: {} } as IRichTextEditorPlugin;

    const callback = vi.fn();
    const unsubscribe = pluginManager.onPluginsChanged(callback);

    vi.resetAllMocks();
    const numInitPlugins = pluginManager.tipTapExtensions.length;
    const dispose1 = pluginManager.registerPlugin(plugin1);
    expect(callback).toHaveBeenCalledTimes(1);
    expect(pluginManager.tipTapExtensions.length).toEqual(numInitPlugins + 1);

    const dispose2 = pluginManager.registerPlugin(plugin2);
    expect(callback).toHaveBeenCalledTimes(2);
    expect(pluginManager.tipTapExtensions.length).toEqual(numInitPlugins + 2);

    dispose1();
    expect(callback).toHaveBeenCalledTimes(3);
    expect(pluginManager.tipTapExtensions.length).toEqual(numInitPlugins + 1);

    unsubscribe();
    dispose2();
    expect(callback).toHaveBeenCalledTimes(3);
    expect(pluginManager.tipTapExtensions.length).toEqual(numInitPlugins);
  });
});
