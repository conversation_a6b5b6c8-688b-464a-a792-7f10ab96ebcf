<script lang="ts">
  import TextAugment, {
    type TextSize,
  } from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import {
    dsColorAttribute,
    dsRadiusAttribute,
  } from "$common-webviews/src/design-system/_libs/component-utils";
  import { BadgeContext } from "./context";
  import { setContext } from "svelte";
  import type {
    BadgeColor,
    BadgeRadius,
    BadgeSize,
    BadgeVariant,
  } from "$common-webviews/src/design-system/components/BadgeAugment/badge-types";

  export let color: BadgeColor = "accent";
  export let variant: BadgeVariant = "soft";
  export let size: BadgeSize = 1;
  export let radius: BadgeRadius = "medium";
  export let highContrast: boolean = false;
  export let inset: boolean = false;

  const textSize: TextSize = size === 0 ? 0 : size === 3 ? 2 : 1;

  const context = new BadgeContext({
    color,
    size,
    variant,
  });
  setContext<BadgeContext>(BadgeContext.CONTEXT_KEY, context);
</script>

<div
  {...dsColorAttribute(color)}
  {...dsRadiusAttribute(radius)}
  class={`c-badge c-badge--${color} c-badge--${variant} c-badge--size-${size}`}
  class:c-badge--highContrast={highContrast}
  class:c-badge--inset={inset}
  on:click
  on:keydown
  on:keyup
  on:mousedown
  on:mouseover
  on:focus
  on:mouseleave
  on:blur
  role="button"
  tabindex="0"
>
  <slot name="chaser" />
  {#if $$slots.leftButtons}
    <div class="c-badge__left-buttons">
      <slot name="leftButtons" />
    </div>
  {/if}

  {#if $$slots.default}
    <TextAugment size={textSize} weight="medium">
      <div class="c-badge-body">
        <slot />
      </div>
    </TextAugment>
  {/if}

  {#if $$slots.rightButtons}
    <div class="c-badge__right-buttons"><slot name="rightButtons" /></div>
  {/if}
</div>

<style>
  .c-badge {
    /* Colors */
    --badge-text-color: var(--ds-color-a11);
    --badge-high-contrast-text-color: var(--ds-color-12);

    /* Sizes */
    --badge-border-radius: max(var(--ds-radius-1), var(--ds-internal-vars-radius-full));
    /* Figma uses *.5 levels for spacing in badges */
    --badge-padding-vertical: calc(var(--ds-spacing-1) * 0.5);
    --badge-padding-horizontal: calc(var(--ds-spacing-1) * 1.5);
    --badge-gap: calc(var(--ds-spacing-1) * 0.75);
    --badge-icon-size: 12px;

    display: inline-flex;
    flex-direction: row;
    align-items: center;
    gap: var(--badge-gap);
    border-radius: max(var(--badge-border-radius), var(--ds-internal-vars-radius-full));
    padding: var(--badge-padding-vertical) var(--badge-padding-horizontal);
    color: var(--badge-text-color);
    background: var(--badge-bg-color);
    cursor: pointer;

    /* For chaser effect */
    &:global(:has(.c-chaser)) {
      position: relative;
      overflow: hidden;
      z-index: 0;
    }
  }

  /* Variant */
  .c-badge--soft {
    --badge-bg-color: var(--ds-color-a3);
  }

  .c-badge--solid {
    --badge-text-color: var(--ds-white-contrast);
    --badge-high-contrast-text-color: var(--ds-color-1);
    --badge-high-contrast-bg-color: var(--ds-color-12);
    --badge-bg-color: var(--ds-color-9);
  }

  .c-badge--surface {
    --badge-bg-color: var(--ds-color-a2);
    --badge-border-color: var(--ds-color-a7);
  }

  .c-badge--outline {
    --badge-border-color: var(--ds-color-a8);
    --badge-high-contrast-border-color: var(--ds-color-12);
  }

  .c-badge--outline,
  .c-badge--surface {
    border: 1px solid var(--badge-border-color);
  }

  .c-badge--ghost {
    --badge-bg-color: transparent;
  }
  .c-badge--size-0 {
    --badge-padding-vertical: var(--ds-spacing-0_5);
    --badge-padding-horizontal: var(--ds-spacing-1);
    --badge-gap: calc(var(--ds-spacing-1) * 0.25);
  }

  /* Size */
  .c-badge--size-2 {
    --badge-padding-vertical: var(--ds-spacing-1);
    --badge-padding-horizontal: var(--ds-spacing-2);
  }

  .c-badge--size-3 {
    --badge-padding-vertical: var(--ds-spacing-1);
    --badge-padding-horizontal: calc(var(--ds-spacing-1) * 2.5);
    --badge-gap: var(--ds-spacing-2);
  }

  .c-badge--size-2,
  .c-badge--size-3 {
    --badge-icon-size: 16px;
    --badge-border-radius: var(--ds-radius-2);
  }

  /* High contrast */
  .c-badge.c-badge--highContrast,
  :global([data-augment-theme-intensity="high-contrast"]) .c-badge {
    color: var(--badge-high-contrast-text-color);
    background: var(--badge-high-contrast-bg-color, var(--badge-bg-color));
    border-color: var(--badge-high-contrast-border-color, var(--badge-border-color));
  }

  /* Inset - uses negative margin to pull badge inward by its padding amount */
  .c-badge--inset {
    margin: calc(var(--badge-padding-vertical) * -0.5) calc(var(--badge-padding-horizontal) * -0.5);
  }

  /* Color */
  .c-badge--solid.c-badge--info,
  .c-badge--solid.c-badge--warning {
    --badge-text-color: var(--ds-black-contrast);
  }

  .c-badge-body {
    display: inline-flex;
    flex-direction: row;
    align-items: center;
    gap: var(--ds-spacing-1);
  }

  /* Icon styles */
  .c-badge :global(svg) {
    /* Remove descenders space below svg */
    display: block;
    width: var(--badge-icon-size);
    height: var(--badge-icon-size);
    fill: var(--icon-color, currentColor);
  }

  /* Move left/right icons to the left margins */
  .c-badge__left-buttons {
    margin-left: calc(var(--badge-padding-horizontal) * -1);

    &:empty {
      display: none;
    }
  }
  .c-badge__right-buttons {
    margin-right: calc(var(--badge-padding-horizontal) * -1);

    &:empty {
      display: none;
    }
  }
</style>
