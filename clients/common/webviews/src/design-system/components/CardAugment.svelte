<script lang="ts" context="module">
  export type CardSize = 0 | 1 | 2 | 3 | 4 | 5;
  export type CardVariant = "surface" | "classic" | "ghost" | "soft";
</script>

<script lang="ts">
  import { dsColorAttribute } from "$common-webviews/src/design-system/_libs/component-utils";
  import "@radix-ui/colors/gray-alpha.css";
  import "@radix-ui/colors/gray-dark-alpha.css";
  import "@radix-ui/colors/black-alpha.css";

  export let size: CardSize = 1;
  export let insetContent: boolean = false;
  export let variant: CardVariant = "surface";
  export let interactive: boolean = false;
  export let includeBackground: boolean = true;
  export let borderless: boolean = false;

  $: ({ class: className } = $$restProps);
  $: classes = [
    "c-card",
    `c-card--size-${size}`,
    `c-card--${variant}`,
    insetContent ? "c-card--insetContent" : "",
    interactive ? "c-card--interactive" : "",
    includeBackground ? "c-card--with-background" : "",
    borderless ? "c-card--borderless" : "",
    className,
  ];
  $: properties = {
    ...dsColorAttribute("accent"),
    class: classes.join(" "),
  };
</script>

{#if interactive}
  <div
    {...properties}
    role="button"
    tabindex="0"
    on:click
    on:keyup
    on:keydown
    on:mousedown
    on:mouseover
    on:focus
    on:mouseleave
    on:blur
    on:contextmenu
  >
    <slot />
  </div>
{:else}
  <div {...properties}>
    <slot />
  </div>
{/if}

<style>
  .c-card {
    padding: var(--padding);
    border-radius: var(--border-radius);

    display: block;
    position: relative;
    overflow: hidden;

    box-shadow: var(--box-shadow);

    /**
     * The ::before element sets the background color and the ::after
     * element sets the inner border that goes on top of the children
     */
    &::before,
    &::after {
      content: "";
      position: absolute;
      pointer-events: none;
      transition: inherit;
      border-radius: calc(var(--border-radius) - var(--border-width));
      inset: var(--border-width);
    }

    &.c-card--with-background::before {
      /** z-index places the ::before element below the children */
      z-index: -1;
      background-color: var(--before-background-color);
      backdrop-filter: var(--before-backdrop-filter);
    }
    & {
      /**
       * `isolation: isolate;` creates a new stacking context so that ::before doesn't go
       * below the card's sibling elements
       */
      isolation: isolate;
    }
    &::after {
      box-shadow: var(--after-box-shadow);
    }

    &.c-card--insetContent {
      padding: 0;
    }

    &.c-card--interactive {
      &:hover {
        background-color: var(--hover-background-color);
        box-shadow: var(--hover-box-shadow);
        cursor: pointer;

        &:focus-visible {
          background-color: var(--hover-focus-background-color);
        }

        &::after {
          box-shadow: var(--after-hover-box-shadow);
        }
      }

      &:active {
        background-color: var(--active-background-color);
        box-shadow: var(--active-box-shadow);

        &:focus-visible {
          background-color: var(--active-focus-background-color);
        }

        &::after {
          box-shadow: var(--after-active-box-shadow);
        }
      }

      &:focus-visible {
        outline: 2px solid var(--ds-color-8);
        outline-offset: -1px;

        &:active {
          &::before {
            background-image: linear-gradient(var(--ds-color-a2), var(--ds-color-a2));
          }
        }
      }
    }
  }

  /* Variants */
  .c-card--borderless {
    --card-border-width: 0px;
  }

  .c-card--soft {
    --border-width: var(--card-border-width, 1px);
    --hover-box-shadow: 0 0 0 1px var(--gray-a7);
    --active-box-shadow: 0 0 0 1px var(--gray-a6);
    --before-background-color: var(--ds-panel-translucent);
    --before-backdrop-filter: blur(24px);
    --after-box-shadow: 0 0 0 1px var(--gray-a5);
  }

  .c-card--surface {
    --border-width: var(--card-border-width, 1px);
    --hover-box-shadow: 0 0 0 1px var(--gray-a7);
    --active-box-shadow: 0 0 0 1px var(--gray-a6);
    --before-background-color: var(--ds-panel-translucent);
    --before-backdrop-filter: blur(64px);
    --after-box-shadow: 0 0 0 1px var(--gray-a5);
  }

  .c-card--classic {
    --border-width: var(--card-border-width, 1px);
    --box-shadow: var(--box-shadow-outer);
    --before-background-color: var(--ds-panel-translucent);
    --before-backdrop-filter: blur(64px);
    --after-box-shadow: var(--box-shadow-inner);
    --hover-box-shadow: var(--hover-box-shadow-outer);
    --after-hover-box-shadow: var(--hover-box-shadow-inner);
    --active-box-shadow: var(--active-box-shadow-outer);
    --after-active-box-shadow: var(--active-box-shadow-inner);

    --border-color: var(--gray-a3);
    --hover-border-color: var(--border-color);
    --active-border-color: var(--gray-a4);
    --box-shadow-inner: (
      0 0 0 1px var(--border-color),
      0 0 0 1px transparent,
      0 0 0 0.5px var(--black-a1),
      0 1px 1px 0 var(--gray-a2),
      0 2px 1px -1px var(--black-a1),
      0 1px 3px 0 var(--black-a1)
    );
    --box-shadow-outer: (
      0 0 0 0 var(--border-color),
      0 0 0 0 transparent,
      0 0 0 0 var(--black-a1),
      0 1px 1px -1px var(--gray-a2),
      0 2px 1px -2px var(--black-a1),
      0 1px 3px -1px var(--black-a1)
    );
    --hover-box-shadow-inner: (
      0 0 0 1px var(--hover-border-color),
      0 1px 1px 1px var(--black-a1),
      0 2px 1px -1px var(--gray-a3),
      0 2px 3px -2px var(--black-a1),
      0 3px 12px -4px var(--gray-a3),
      0 4px 16px -8px var(--black-a1)
    );
    --hover-box-shadow-outer: (
      0 0 0 0 var(--hover-border-color),
      0 1px 1px 0 var(--black-a1),
      0 2px 1px -2px var(--gray-a3),
      0 2px 3px -3px var(--black-a1),
      0 3px 12px -5px var(--gray-a3),
      0 4px 16px -9px var(--black-a1)
    );
    --active-box-shadow-inner: (
      0 0 0 1px var(--active-border-color),
      0 0 0 1px transparent,
      0 0 0 0.5px var(--black-a1),
      0 1px 1px 0 var(--gray-a4),
      0 2px 1px -1px var(--black-a1),
      0 1px 3px 0 var(--black-a1)
    );
    --active-box-shadow-outer: (
      0 0 0 0 var(--active-border-color),
      0 0 0 0 transparent,
      0 0 0 0 var(--black-a1),
      0 1px 1px -1px var(--gray-a4),
      0 2px 1px -2px var(--black-a1),
      0 1px 3px -1px var(--black-a1)
    );
    transition: box-shadow 120ms;
    &.c-card--interactive {
      &:hover {
        transition-duration: 40ms;
      }
      &:active {
        transition-duration: 40ms;
      }
    }
  }

  :global(.dark) .c-card--classic {
    --border-color: var(--gray-a6);
    --hover-border-color: var(--border-color);
    --active-border-color: var(--border-color);
    --box-shadow-inner: (
      0 0 0 1px var(--border-color),
      0 0 0 1px transparent,
      0 0 0 0.5px var(--black-a3),
      0 1px 1px 0 var(--black-a6),
      0 2px 1px -1px var(--black-a6),
      0 1px 3px 0 var(--black-a5)
    );
    --box-shadow-outer: (
      0 0 0 0 var(--border-color),
      0 0 0 0 transparent,
      0 0 0 0 var(--black-a3),
      0 1px 1px -1px var(--black-a6),
      0 2px 1px -2px var(--black-a6),
      0 1px 3px -1px var(--black-a5)
    );
    --hover-box-shadow-inner: (
      0 0 0 1px var(--hover-border-color),
      0 0 1px 1px var(--gray-a4),
      0 0 1px -1px var(--gray-a4),
      0 0 3px -2px var(--gray-a3),
      0 0 12px -2px var(--gray-a3),
      0 0 16px -8px var(--gray-a7)
    );
    --hover-box-shadow-outer: (
      0 0 0 0 var(--hover-border-color),
      0 0 1px 0 var(--gray-a4),
      0 0 1px -2px var(--gray-a4),
      0 0 3px -3px var(--gray-a3),
      0 0 12px -3px var(--gray-a3),
      0 0 16px -9px var(--gray-a7)
    );
    --active-box-shadow-inner: (
      0 0 0 1px var(--active-border-color),
      0 0 0 1px transparent,
      0 0 0 0.5px var(--black-a3),
      0 1px 1px 0 var(--black-a6),
      0 2px 1px -1px var(--black-a6),
      0 1px 3px 0 var(--black-a5)
    );
    --active-box-shadow-outer: (
      0 0 0 0 var(--active-border-color),
      0 0 0 0 transparent,
      0 0 0 0 var(--black-a3),
      0 1px 1px -1px var(--black-a6),
      0 2px 1px -2px var(--black-a6),
      0 1px 3px -1px var(--black-a5)
    );
  }

  .c-card--ghost {
    --border-width: 0px;
    --hover-background-color: var(--gray-a3);
    --hover-focus-background-color: var(--ds-color-a2);
    --active-backgorund-color: var(--gray-a4);
    --active-focus-background-color: var(--ds-color-a2);
  }

  /* Sizes */
  .c-card--size-0 {
    --padding: var(--ds-spacing-2);
    --border-radius: var(--ds-radius-2);
  }
  .c-card--size-1 {
    --padding: var(--ds-spacing-3);
    --border-radius: var(--ds-radius-4);
  }

  .c-card--size-2 {
    --padding: var(--ds-spacing-4);
    --border-radius: var(--ds-radius-4);
  }

  .c-card--size-3 {
    --padding: var(--ds-spacing-5);
    --border-radius: var(--ds-radius-5);
  }

  .c-card--size-4 {
    --padding: var(--ds-spacing-6);
    --border-radius: var(--ds-radius-5);
  }

  .c-card--size-5 {
    --padding: var(--ds-spacing-8);
    --border-radius: var(--ds-radius-6);
  }
</style>
