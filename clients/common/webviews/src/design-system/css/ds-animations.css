/*

This file contains animation utilities and keyframes for the design system.

These utilities provide consistent animations across components and can be
used by applying the appropriate CSS classes.

*/

/* Shimmer Animation Utility */
.ds-shimmer {
  position: relative;
  overflow: hidden;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, var(--ds-color-neutral-a4), transparent);
    animation: ds-shimmer-slide 2s infinite;
  }
}

@keyframes ds-shimmer-slide {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* Pulse Animation Utility */
.ds-pulse {
  animation: ds-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes ds-pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Fade In Animation */
.ds-fade-in {
  animation: ds-fade-in 0.3s ease-in-out;
}

@keyframes ds-fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Fade Out Animation */
.ds-fade-out {
  animation: ds-fade-out 0.3s ease-in-out;
}

@keyframes ds-fade-out {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

/* Slide Up Animation */
.ds-slide-up {
  animation: ds-slide-up 0.3s ease-out;
}

@keyframes ds-slide-up {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Slide Down Animation */
.ds-slide-down {
  animation: ds-slide-down 0.3s ease-out;
}

@keyframes ds-slide-down {
  from {
    transform: translateY(-20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Scale In Animation */
.ds-scale-in {
  animation: ds-scale-in 0.2s ease-out;
}

@keyframes ds-scale-in {
  from {
    transform: scale(0.95);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

/* Bounce Animation */
.ds-bounce {
  animation: ds-bounce 1s infinite;
}

@keyframes ds-bounce {
  0%,
  20%,
  53%,
  80%,
  100% {
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    transform: translate3d(0, 0, 0);
  }
  40%,
  43% {
    animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    transform: translate3d(0, -30px, 0);
  }
  70% {
    animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    transform: translate3d(0, -15px, 0);
  }
  90% {
    transform: translate3d(0, -4px, 0);
  }
}

/* Spin Animation */
.ds-spin {
  animation: ds-spin 1s linear infinite;
}

@keyframes ds-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .ds-shimmer::before,
  .ds-pulse,
  .ds-fade-in,
  .ds-fade-out,
  .ds-slide-up,
  .ds-slide-down,
  .ds-scale-in,
  .ds-bounce,
  .ds-spin {
    animation: none;
  }

  .ds-shimmer::before {
    display: none;
  }
}

/* Animation Duration Utilities */
.ds-animate-fast {
  animation-duration: 0.15s;
}

.ds-animate-normal {
  animation-duration: 0.3s;
}

.ds-animate-slow {
  animation-duration: 0.5s;
}

/* Animation Delay Utilities */
.ds-animate-delay-75 {
  animation-delay: 75ms;
}

.ds-animate-delay-100 {
  animation-delay: 100ms;
}

.ds-animate-delay-150 {
  animation-delay: 150ms;
}

.ds-animate-delay-200 {
  animation-delay: 200ms;
}

.ds-animate-delay-300 {
  animation-delay: 300ms;
}

.ds-animate-delay-500 {
  animation-delay: 500ms;
}

.ds-animate-delay-700 {
  animation-delay: 700ms;
}

.ds-animate-delay-1000 {
  animation-delay: 1000ms;
}

/* Enhanced Tab Transition Utilities */
.ds-tab-transition-fade {
  animation: ds-tab-fade-transition 0.25s cubic-bezier(0.4, 0, 0.2, 1);
}

.ds-tab-transition-slide {
  animation: ds-tab-slide-transition 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.ds-tab-transition-reveal {
  animation: ds-tab-reveal-transition 0.35s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes ds-tab-fade-transition {
  from {
    opacity: 0;
    transform: translateY(8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes ds-tab-slide-transition {
  from {
    opacity: 0;
    transform: translateX(20px) scale(0.98);
  }
  to {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

@keyframes ds-tab-reveal-transition {
  from {
    opacity: 0;
    transform: translateY(16px) scale(0.95);
    clip-path: polygon(0 100%, 100% 100%, 100% 100%, 0 100%);
  }
  50% {
    opacity: 0.7;
    transform: translateY(8px) scale(0.98);
    clip-path: polygon(0 70%, 100% 70%, 100% 100%, 0 100%);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
    clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);
  }
}

/* Spring-like easing utilities */
.ds-animate-spring {
  animation-timing-function: cubic-bezier(0.34, 1.56, 0.64, 1);
}

.ds-animate-smooth {
  animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.ds-animate-bounce-in {
  animation-timing-function: cubic-bezier(0.68, -0.55, 0.265, 1.55);
}
