/*

This file is a utility that simplifies using colors in components.

Instead of using --accent-12, --info-12, --warning-12 etc for each color option,
components set `data-ds-color="<color>"` and use `--ds-color-<color>`
and this file will assign the correct color to that variable based on the data
attribute.

*/

@import "./color-aliases/ds-accent-colors.css";
@import "./color-aliases/ds-neutral-colors.css";
@import "./color-aliases/ds-error-colors.css";
@import "./color-aliases/ds-success-colors.css";
@import "./color-aliases/ds-warning-colors.css";
@import "./color-aliases/ds-info-colors.css";
@import "./color-aliases/ds-primary-colors.css";
@import "./color-aliases/ds-secondary-colors.css";
@import "./color-aliases/ds-premium-colors.css";
@import "./color-aliases/ds-iris-colors.css";
@import "./color-aliases/ds-text-colors.css";

[data-ds-color="accent"] {
  --ds-color-1: var(--ds-color-accent-1);
  --ds-color-2: var(--ds-color-accent-2);
  --ds-color-3: var(--ds-color-accent-3);
  --ds-color-4: var(--ds-color-accent-4);
  --ds-color-5: var(--ds-color-accent-5);
  --ds-color-6: var(--ds-color-accent-6);
  --ds-color-7: var(--ds-color-accent-7);
  --ds-color-8: var(--ds-color-accent-8);
  --ds-color-9: var(--ds-color-accent-9);
  --ds-color-10: var(--ds-color-accent-10);
  --ds-color-11: var(--ds-color-accent-11);
  --ds-color-12: var(--ds-color-accent-12);

  --ds-color-a1: var(--ds-color-accent-a1);
  --ds-color-a2: var(--ds-color-accent-a2);
  --ds-color-a3: var(--ds-color-accent-a3);
  --ds-color-a4: var(--ds-color-accent-a4);
  --ds-color-a5: var(--ds-color-accent-a5);
  --ds-color-a6: var(--ds-color-accent-a6);
  --ds-color-a7: var(--ds-color-accent-a7);
  --ds-color-a8: var(--ds-color-accent-a8);
  --ds-color-a9: var(--ds-color-accent-a9);
  --ds-color-a10: var(--ds-color-accent-a10);
  --ds-color-a11: var(--ds-color-accent-a11);
  --ds-color-a12: var(--ds-color-accent-a12);
}

[data-ds-color="neutral"] {
  --ds-color-1: var(--ds-color-neutral-1);
  --ds-color-2: var(--ds-color-neutral-2);
  --ds-color-3: var(--ds-color-neutral-3);
  --ds-color-4: var(--ds-color-neutral-4);
  --ds-color-5: var(--ds-color-neutral-5);
  --ds-color-6: var(--ds-color-neutral-6);
  --ds-color-7: var(--ds-color-neutral-7);
  --ds-color-8: var(--ds-color-neutral-8);
  --ds-color-9: var(--ds-color-neutral-9);
  --ds-color-10: var(--ds-color-neutral-10);
  --ds-color-11: var(--ds-color-neutral-11);
  --ds-color-12: var(--ds-color-neutral-12);

  --ds-color-a1: var(--ds-color-neutral-a1);
  --ds-color-a2: var(--ds-color-neutral-a2);
  --ds-color-a3: var(--ds-color-neutral-a3);
  --ds-color-a4: var(--ds-color-neutral-a4);
  --ds-color-a5: var(--ds-color-neutral-a5);
  --ds-color-a6: var(--ds-color-neutral-a6);
  --ds-color-a7: var(--ds-color-neutral-a7);
  --ds-color-a8: var(--ds-color-neutral-a8);
  --ds-color-a9: var(--ds-color-neutral-a9);
  --ds-color-a10: var(--ds-color-neutral-a10);
  --ds-color-a11: var(--ds-color-neutral-a11);
  --ds-color-a12: var(--ds-color-neutral-a12);
}

[data-ds-color="error"] {
  --ds-color-1: var(--ds-color-error-1);
  --ds-color-2: var(--ds-color-error-2);
  --ds-color-3: var(--ds-color-error-3);
  --ds-color-4: var(--ds-color-error-4);
  --ds-color-5: var(--ds-color-error-5);
  --ds-color-6: var(--ds-color-error-6);
  --ds-color-7: var(--ds-color-error-7);
  --ds-color-8: var(--ds-color-error-8);
  --ds-color-9: var(--ds-color-error-9);
  --ds-color-10: var(--ds-color-error-10);
  --ds-color-11: var(--ds-color-error-11);
  --ds-color-12: var(--ds-color-error-12);

  --ds-color-a1: var(--ds-color-error-a1);
  --ds-color-a2: var(--ds-color-error-a2);
  --ds-color-a3: var(--ds-color-error-a3);
  --ds-color-a4: var(--ds-color-error-a4);
  --ds-color-a5: var(--ds-color-error-a5);
  --ds-color-a6: var(--ds-color-error-a6);
  --ds-color-a7: var(--ds-color-error-a7);
  --ds-color-a8: var(--ds-color-error-a8);
  --ds-color-a9: var(--ds-color-error-a9);
  --ds-color-a10: var(--ds-color-error-a10);
  --ds-color-a11: var(--ds-color-error-a11);
  --ds-color-a12: var(--ds-color-error-a12);
}

[data-ds-color="success"] {
  --ds-color-1: var(--ds-color-success-1);
  --ds-color-2: var(--ds-color-success-2);
  --ds-color-3: var(--ds-color-success-3);
  --ds-color-4: var(--ds-color-success-4);
  --ds-color-5: var(--ds-color-success-5);
  --ds-color-6: var(--ds-color-success-6);
  --ds-color-7: var(--ds-color-success-7);
  --ds-color-8: var(--ds-color-success-8);
  --ds-color-9: var(--ds-color-success-9);
  --ds-color-10: var(--ds-color-success-10);
  --ds-color-11: var(--ds-color-success-11);
  --ds-color-12: var(--ds-color-success-12);

  --ds-color-a1: var(--ds-color-success-a1);
  --ds-color-a2: var(--ds-color-success-a2);
  --ds-color-a3: var(--ds-color-success-a3);
  --ds-color-a4: var(--ds-color-success-a4);
  --ds-color-a5: var(--ds-color-success-a5);
  --ds-color-a6: var(--ds-color-success-a6);
  --ds-color-a7: var(--ds-color-success-a7);
  --ds-color-a8: var(--ds-color-success-a8);
  --ds-color-a9: var(--ds-color-success-a9);
  --ds-color-a10: var(--ds-color-success-a10);
  --ds-color-a11: var(--ds-color-success-a11);
  --ds-color-a12: var(--ds-color-success-a12);
}

[data-ds-color="warning"] {
  --ds-color-1: var(--ds-color-warning-1);
  --ds-color-2: var(--ds-color-warning-2);
  --ds-color-3: var(--ds-color-warning-3);
  --ds-color-4: var(--ds-color-warning-4);
  --ds-color-5: var(--ds-color-warning-5);
  --ds-color-6: var(--ds-color-warning-6);
  --ds-color-7: var(--ds-color-warning-7);
  --ds-color-8: var(--ds-color-warning-8);
  --ds-color-9: var(--ds-color-warning-9);
  --ds-color-10: var(--ds-color-warning-10);
  --ds-color-11: var(--ds-color-warning-11);
  --ds-color-12: var(--ds-color-warning-12);

  --ds-color-a1: var(--ds-color-warning-a1);
  --ds-color-a2: var(--ds-color-warning-a2);
  --ds-color-a3: var(--ds-color-warning-a3);
  --ds-color-a4: var(--ds-color-warning-a4);
  --ds-color-a5: var(--ds-color-warning-a5);
  --ds-color-a6: var(--ds-color-warning-a6);
  --ds-color-a7: var(--ds-color-warning-a7);
  --ds-color-a8: var(--ds-color-warning-a8);
  --ds-color-a9: var(--ds-color-warning-a9);
  --ds-color-a10: var(--ds-color-warning-a10);
  --ds-color-a11: var(--ds-color-warning-a11);
  --ds-color-a12: var(--ds-color-warning-a12);
}

[data-ds-color="info"] {
  --ds-color-1: var(--ds-color-info-1);
  --ds-color-2: var(--ds-color-info-2);
  --ds-color-3: var(--ds-color-info-3);
  --ds-color-4: var(--ds-color-info-4);
  --ds-color-5: var(--ds-color-info-5);
  --ds-color-6: var(--ds-color-info-6);
  --ds-color-7: var(--ds-color-info-7);
  --ds-color-8: var(--ds-color-info-8);
  --ds-color-9: var(--ds-color-info-9);
  --ds-color-10: var(--ds-color-info-10);
  --ds-color-11: var(--ds-color-info-11);
  --ds-color-12: var(--ds-color-info-12);

  --ds-color-a1: var(--ds-color-info-a1);
  --ds-color-a2: var(--ds-color-info-a2);
  --ds-color-a3: var(--ds-color-info-a3);
  --ds-color-a4: var(--ds-color-info-a4);
  --ds-color-a5: var(--ds-color-info-a5);
  --ds-color-a6: var(--ds-color-info-a6);
  --ds-color-a7: var(--ds-color-info-a7);
  --ds-color-a8: var(--ds-color-info-a8);
  --ds-color-a9: var(--ds-color-info-a9);
  --ds-color-a10: var(--ds-color-info-a10);
  --ds-color-a11: var(--ds-color-info-a11);
  --ds-color-a12: var(--ds-color-info-a12);
}

[data-ds-color="primary"] {
  --ds-color-1: var(--ds-color-primary-1);
  --ds-color-2: var(--ds-color-primary-2);
  --ds-color-3: var(--ds-color-primary-3);
  --ds-color-4: var(--ds-color-primary-4);
  --ds-color-5: var(--ds-color-primary-5);
  --ds-color-6: var(--ds-color-primary-6);
  --ds-color-7: var(--ds-color-primary-7);
  --ds-color-8: var(--ds-color-primary-8);
  --ds-color-9: var(--ds-color-primary-9);
  --ds-color-10: var(--ds-color-primary-10);
  --ds-color-11: var(--ds-color-primary-11);
  --ds-color-12: var(--ds-color-primary-12);

  --ds-color-a1: var(--ds-color-primary-a1);
  --ds-color-a2: var(--ds-color-primary-a2);
  --ds-color-a3: var(--ds-color-primary-a3);
  --ds-color-a4: var(--ds-color-primary-a4);
  --ds-color-a5: var(--ds-color-primary-a5);
  --ds-color-a6: var(--ds-color-primary-a6);
  --ds-color-a7: var(--ds-color-primary-a7);
  --ds-color-a8: var(--ds-color-primary-a8);
  --ds-color-a9: var(--ds-color-primary-a9);
  --ds-color-a10: var(--ds-color-primary-a10);
  --ds-color-a11: var(--ds-color-primary-a11);
  --ds-color-a12: var(--ds-color-primary-a12);
}

[data-ds-color="secondary"] {
  --ds-color-1: var(--ds-color-secondary-1);
  --ds-color-2: var(--ds-color-secondary-2);
  --ds-color-3: var(--ds-color-secondary-3);
  --ds-color-4: var(--ds-color-secondary-4);
  --ds-color-5: var(--ds-color-secondary-5);
  --ds-color-6: var(--ds-color-secondary-6);
  --ds-color-7: var(--ds-color-secondary-7);
  --ds-color-8: var(--ds-color-secondary-8);
  --ds-color-9: var(--ds-color-secondary-9);
  --ds-color-10: var(--ds-color-secondary-10);
  --ds-color-11: var(--ds-color-secondary-11);
  --ds-color-12: var(--ds-color-secondary-12);

  --ds-color-a1: var(--ds-color-secondary-a1);
  --ds-color-a2: var(--ds-color-secondary-a2);
  --ds-color-a3: var(--ds-color-secondary-a3);
  --ds-color-a4: var(--ds-color-secondary-a4);
  --ds-color-a5: var(--ds-color-secondary-a5);
  --ds-color-a6: var(--ds-color-secondary-a6);
  --ds-color-a7: var(--ds-color-secondary-a7);
  --ds-color-a8: var(--ds-color-secondary-a8);
  --ds-color-a9: var(--ds-color-secondary-a9);
  --ds-color-a10: var(--ds-color-secondary-a10);
  --ds-color-a11: var(--ds-color-secondary-a11);
  --ds-color-a12: var(--ds-color-secondary-a12);
}

[data-ds-color="premium"] {
  --ds-color-1: var(--ds-color-premium-1);
  --ds-color-2: var(--ds-color-premium-2);
  --ds-color-3: var(--ds-color-premium-3);
  --ds-color-4: var(--ds-color-premium-4);
  --ds-color-5: var(--ds-color-premium-5);
  --ds-color-6: var(--ds-color-premium-6);
  --ds-color-7: var(--ds-color-premium-7);
  --ds-color-8: var(--ds-color-premium-8);
  --ds-color-9: var(--ds-color-premium-9);
  --ds-color-10: var(--ds-color-premium-10);
  --ds-color-11: var(--ds-color-premium-11);
  --ds-color-12: var(--ds-color-premium-12);

  --ds-color-a1: var(--ds-color-premium-a1);
  --ds-color-a2: var(--ds-color-premium-a2);
  --ds-color-a3: var(--ds-color-premium-a3);
  --ds-color-a4: var(--ds-color-premium-a4);
  --ds-color-a5: var(--ds-color-premium-a5);
  --ds-color-a6: var(--ds-color-premium-a6);
  --ds-color-a7: var(--ds-color-premium-a7);
  --ds-color-a8: var(--ds-color-premium-a8);
  --ds-color-a9: var(--ds-color-premium-a9);
  --ds-color-a10: var(--ds-color-premium-a10);
  --ds-color-a11: var(--ds-color-premium-a11);
  --ds-color-a12: var(--ds-color-premium-a12);
}

[data-ds-color="iris"] {
  --ds-color-1: var(--ds-color-iris-1);
  --ds-color-2: var(--ds-color-iris-2);
  --ds-color-3: var(--ds-color-iris-3);
  --ds-color-4: var(--ds-color-iris-4);
  --ds-color-5: var(--ds-color-iris-5);
  --ds-color-6: var(--ds-color-iris-6);
  --ds-color-7: var(--ds-color-iris-7);
  --ds-color-8: var(--ds-color-iris-8);
  --ds-color-9: var(--ds-color-iris-9);
  --ds-color-10: var(--ds-color-iris-10);
  --ds-color-11: var(--ds-color-iris-11);
  --ds-color-12: var(--ds-color-iris-12);

  --ds-color-a1: var(--ds-color-iris-a1);
  --ds-color-a2: var(--ds-color-iris-a2);
  --ds-color-a3: var(--ds-color-iris-a3);
  --ds-color-a4: var(--ds-color-iris-a4);
  --ds-color-a5: var(--ds-color-iris-a5);
  --ds-color-a6: var(--ds-color-iris-a6);
  --ds-color-a7: var(--ds-color-iris-a7);
  --ds-color-a8: var(--ds-color-iris-a8);
  --ds-color-a9: var(--ds-color-iris-a9);
  --ds-color-a10: var(--ds-color-iris-a10);
  --ds-color-a11: var(--ds-color-iris-a11);
  --ds-color-a12: var(--ds-color-iris-a12);
}
