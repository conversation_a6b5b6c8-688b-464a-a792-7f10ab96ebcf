/**
* This file contains global styles for icons in the design system.
*
* Icons should have a `data-ds-icon` attribute set to the icon library
* they come from. For example, fontawesome icons should have
* `data-ds-icon="fa"`.  This will set the fill to `currentColor` by default.
*
* Future icons should use a `data-ds-icon` attribute and be added to this file.
* The svgo plugin in vite.config.ts should be updated to add the attribute
* automatically to the SVG element.
**/
svg[data-ds-icon="fa"] {
  fill: var(--icon-color, currentColor);
  height: var(--icon-size, var(--ds-icon-size-2));
  opacity: 0.7;
}
:root {
  --ds-icon-size-0: 12px;
  --ds-icon-size-1: 14px;
  --ds-icon-size-2: 16px;
}
