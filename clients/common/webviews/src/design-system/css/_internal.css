/*

This file contains variables that are defined in the Figma radix library
but aren't using colors from the @radix-ui/colors node module.

These variables are used by other variables we expose in design-system.css.
Users should not use these variables directly, instead use the variables that
the the variables exposed as --*-augment.

For example, the Figma library defined `Variables/Effects/translucent`,
which it maps to a "theme variable `Panel/translucent`.

This file will be the definition of `Variables/Effects/translucent` as
`--ds-internal-vars-effects-translucent` and it's mapped to
`--ds-panel-translucent` in design-system.css.

Note: `--ds-internal-*` refers to "design-system internal" and is used
to discourage usage of these variables directly.

*/

/* Colors - Effects */
:root {
  --ds-internal-vars-effects-translucent: rgba(255, 255, 255, 80%);
  --ds-internal-vars-effects-solid: var(--ds-default-white);
}

.dark {
  --ds-internal-vars-effects-translucent: var(--ds-color-neutral-a4);
  --ds-internal-vars-effects-solid: var(--ds-color-neutral-2);
}

/* Colors - Misc */
:root {
  --ds-internal-vars-misc-white-to-dark: var(--ds-default-white);
  --ds-internal-vars-misc-white-to-dark-2: rgba(255, 255, 255, 90%);
  --ds-internal-vars-misc-dark-to-white: var(--ds-color-neutral-12);
  --ds-internal-vars-misc-backdrop: var(--ds-color-neutral-a8);
  --ds-internal-vars-misc-secondary: rgba(0, 0, 0, 0.6);
  --ds-internal-vars-misc-tertiary: rgba(0, 0, 0, 0.3);
}

.dark {
  --ds-internal-vars-misc-white-to-dark: var(--ds-color-neutral-1);
  --ds-internal-vars-misc-white-to-dark-2: rgba(0, 0, 0, 25%);
  --ds-internal-vars-misc-dark-to-white: var(--ds-default-white);
  --ds-internal-vars-misc-backdrop: rgba(0, 0, 0, 75%);
  --ds-internal-vars-misc-secondary: rgba(255, 255, 255, 0.6);
  --ds-internal-vars-misc-tertiary: rgba(255, 255, 255, 0.3);
}
