/* Semantic colors - error */
@import "@radix-ui/colors/red.css";
@import "@radix-ui/colors/red-alpha.css";
@import "@radix-ui/colors/red-dark.css";
@import "@radix-ui/colors/red-dark-alpha.css";

:root {
  --ds-color-error-1: var(--red-1);
  --ds-color-error-2: var(--red-2);
  --ds-color-error-3: var(--red-3);
  --ds-color-error-4: var(--red-4);
  --ds-color-error-5: var(--red-5);
  --ds-color-error-6: var(--red-6);
  --ds-color-error-7: var(--red-7);
  --ds-color-error-8: var(--red-8);
  --ds-color-error-9: var(--red-9);
  --ds-color-error-10: var(--red-10);
  --ds-color-error-11: var(--red-11);
  --ds-color-error-12: var(--red-12);

  --ds-color-error-a1: var(--red-a1);
  --ds-color-error-a2: var(--red-a2);
  --ds-color-error-a3: var(--red-a3);
  --ds-color-error-a4: var(--red-a4);
  --ds-color-error-a5: var(--red-a5);
  --ds-color-error-a6: var(--red-a6);
  --ds-color-error-a7: var(--red-a7);
  --ds-color-error-a8: var(--red-a8);
  --ds-color-error-a9: var(--red-a9);
  --ds-color-error-a10: var(--red-a10);
  --ds-color-error-a11: var(--red-a11);
  --ds-color-error-a12: var(--red-a12);
}
