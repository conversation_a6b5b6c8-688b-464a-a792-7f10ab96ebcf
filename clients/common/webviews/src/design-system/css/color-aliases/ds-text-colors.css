:root {
  /* Primary text color - uses neutral-12 */
  --ds-color-text-default: var(--ds-color-neutral-12);

  /* Subtle text color - light theme */
  --ds-color-text-subtle: #8d9092;
}

:root.dark {
  /* Subtle text color - dark theme */
  --ds-color-text-subtle: #0e1012;
}

/* Text color aliases for different contexts */
:root {
  /* Default text colors */
  --ds-text-default: var(--ds-color-text);
  --ds-text-subtle: var(--ds-color-text-subtle);

  /* Interactive text states */
  --ds-text-link: var(--ds-color-accent-9);
  --ds-text-link-hover: var(--ds-color-accent-10);

  /* Semantic text colors */
  --ds-text-error: var(--ds-color-error-11);
  --ds-text-success: var(--ds-color-success-11);
  --ds-text-warning: var(--ds-color-warning-11);
  --ds-text-info: var(--ds-color-info-11);

  /* Disabled state */
  --ds-text-disabled: var(--ds-color-neutral-8);
}
