/* Semantic colors - success */
@import "@radix-ui/colors/green.css";
@import "@radix-ui/colors/green-alpha.css";
@import "@radix-ui/colors/green-dark.css";
@import "@radix-ui/colors/green-dark-alpha.css";

:root {
  --ds-color-success-1: var(--green-1);
  --ds-color-success-2: var(--green-2);
  --ds-color-success-3: var(--green-3);
  --ds-color-success-4: var(--green-4);
  --ds-color-success-5: var(--green-5);
  --ds-color-success-6: var(--green-6);
  --ds-color-success-7: var(--green-7);
  --ds-color-success-8: var(--green-8);
  --ds-color-success-9: var(--green-9);
  --ds-color-success-10: var(--green-10);
  --ds-color-success-11: var(--green-11);
  --ds-color-success-12: var(--green-12);

  --ds-color-success-a1: var(--green-a1);
  --ds-color-success-a2: var(--green-a2);
  --ds-color-success-a3: var(--green-a3);
  --ds-color-success-a4: var(--green-a4);
  --ds-color-success-a5: var(--green-a5);
  --ds-color-success-a6: var(--green-a6);
  --ds-color-success-a7: var(--green-a7);
  --ds-color-success-a8: var(--green-a8);
  --ds-color-success-a9: var(--green-a9);
  --ds-color-success-a10: var(--green-a10);
  --ds-color-success-a11: var(--green-a11);
  --ds-color-success-a12: var(--green-a12);
}
