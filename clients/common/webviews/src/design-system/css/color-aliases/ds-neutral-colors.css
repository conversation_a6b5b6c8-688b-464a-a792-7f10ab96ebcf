@import "@radix-ui/colors/slate.css";
@import "@radix-ui/colors/slate-alpha.css";
@import "@radix-ui/colors/slate-dark.css";
@import "@radix-ui/colors/slate-dark-alpha.css";

:root {
  --ds-color-neutral-1: var(--slate-1);
  --ds-color-neutral-2: var(--slate-2);
  --ds-color-neutral-3: var(--slate-3);
  --ds-color-neutral-4: var(--slate-4);
  --ds-color-neutral-5: var(--slate-5);
  --ds-color-neutral-6: var(--slate-6);
  --ds-color-neutral-7: var(--slate-7);
  --ds-color-neutral-8: var(--slate-8);
  --ds-color-neutral-9: var(--slate-9);
  --ds-color-neutral-10: var(--slate-10);
  --ds-color-neutral-11: var(--slate-11);
  --ds-color-neutral-12: var(--slate-12);

  --ds-color-neutral-a1: var(--slate-a1);
  --ds-color-neutral-a2: var(--slate-a2);
  --ds-color-neutral-a3: var(--slate-a3);
  --ds-color-neutral-a4: var(--slate-a4);
  --ds-color-neutral-a5: var(--slate-a5);
  --ds-color-neutral-a6: var(--slate-a6);
  --ds-color-neutral-a7: var(--slate-a7);
  --ds-color-neutral-a8: var(--slate-a8);
  --ds-color-neutral-a9: var(--slate-a9);
  --ds-color-neutral-a10: var(--slate-a10);
  --ds-color-neutral-a11: var(--slate-a11);
  --ds-color-neutral-a12: var(--slate-a12);
}
