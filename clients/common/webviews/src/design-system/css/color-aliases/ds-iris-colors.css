/* Semantic colors - iris */
@import "@radix-ui/colors/iris.css";
@import "@radix-ui/colors/iris-alpha.css";
@import "@radix-ui/colors/iris-dark.css";
@import "@radix-ui/colors/iris-dark-alpha.css";

:root {
  --ds-color-iris-1: var(--iris-1);
  --ds-color-iris-2: var(--iris-2);
  --ds-color-iris-3: var(--iris-3);
  --ds-color-iris-4: var(--iris-4);
  --ds-color-iris-5: var(--iris-5);
  --ds-color-iris-6: var(--iris-6);
  --ds-color-iris-7: var(--iris-7);
  --ds-color-iris-8: var(--iris-8);
  --ds-color-iris-9: var(--iris-9);
  --ds-color-iris-10: var(--iris-10);
  --ds-color-iris-11: var(--iris-11);
  --ds-color-iris-12: var(--iris-12);

  --ds-color-iris-a1: var(--iris-a1);
  --ds-color-iris-a2: var(--iris-a2);
  --ds-color-iris-a3: var(--iris-a3);
  --ds-color-iris-a4: var(--iris-a4);
  --ds-color-iris-a5: var(--iris-a5);
  --ds-color-iris-a6: var(--iris-a6);
  --ds-color-iris-a7: var(--iris-a7);
  --ds-color-iris-a8: var(--iris-a8);
  --ds-color-iris-a9: var(--iris-a9);
  --ds-color-iris-a10: var(--iris-a10);
  --ds-color-iris-a11: var(--iris-a11);
  --ds-color-iris-a12: var(--iris-a12);
}
