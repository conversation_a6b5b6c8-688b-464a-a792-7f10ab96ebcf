/* Custom primary colors based on --augment-text-color */
/* Primary colors are brighter than secondary colors */

:root.dark {
  /* Dark theme (default) - white text */
  --ds-color-primary-1: hsl(0, 0%, 10%);
  --ds-color-primary-2: hsl(0, 0%, 15%);
  --ds-color-primary-3: hsl(0, 0%, 20%);
  --ds-color-primary-4: hsl(0, 0%, 30%);
  --ds-color-primary-5: hsl(0, 0%, 40%);
  --ds-color-primary-6: hsl(0, 0%, 50%);
  --ds-color-primary-7: hsl(0, 0%, 60%);
  --ds-color-primary-8: hsl(0, 0%, 70%);
  --ds-color-primary-9: hsl(0, 0%, 80%);
  --ds-color-primary-10: hsl(0, 0%, 90%);
  --ds-color-primary-11: hsl(0, 0%, 95%);
  --ds-color-primary-12: hsl(0, 0%, 100%);

  --ds-color-primary-a1: hsla(0, 0%, 100%, 0.05);
  --ds-color-primary-a2: hsla(0, 0%, 100%, 0.1);
  --ds-color-primary-a3: hsla(0, 0%, 100%, 0.15);
  --ds-color-primary-a4: hsla(0, 0%, 100%, 0.2);
  --ds-color-primary-a5: hsla(0, 0%, 100%, 0.3);
  --ds-color-primary-a6: hsla(0, 0%, 100%, 0.4);
  --ds-color-primary-a7: hsla(0, 0%, 100%, 0.5);
  --ds-color-primary-a8: hsla(0, 0%, 100%, 0.6);
  --ds-color-primary-a9: hsla(0, 0%, 100%, 0.7);
  --ds-color-primary-a10: hsla(0, 0%, 100%, 0.8);
  --ds-color-primary-a11: hsla(0, 0%, 100%, 0.9);
  --ds-color-primary-a12: hsla(0, 0%, 100%, 1);
}

/* Light theme - black text */
:root {
  --ds-color-primary-1: hsl(0, 0%, 100%);
  --ds-color-primary-2: hsl(0, 0%, 95%);
  --ds-color-primary-3: hsl(0, 0%, 90%);
  --ds-color-primary-4: hsl(0, 0%, 80%);
  --ds-color-primary-5: hsl(0, 0%, 70%);
  --ds-color-primary-6: hsl(0, 0%, 60%);
  --ds-color-primary-7: hsl(0, 0%, 50%);
  --ds-color-primary-8: hsl(0, 0%, 40%);
  --ds-color-primary-9: hsl(0, 0%, 30%);
  --ds-color-primary-10: hsl(0, 0%, 20%);
  --ds-color-primary-11: hsl(0, 0%, 15%);
  --ds-color-primary-12: hsl(0, 0%, 10%);

  --ds-color-primary-a1: hsla(0, 0%, 0%, 0.05);
  --ds-color-primary-a2: hsla(0, 0%, 0%, 0.1);
  --ds-color-primary-a3: hsla(0, 0%, 0%, 0.15);
  --ds-color-primary-a4: hsla(0, 0%, 0%, 0.2);
  --ds-color-primary-a5: hsla(0, 0%, 0%, 0.3);
  --ds-color-primary-a6: hsla(0, 0%, 0%, 0.4);
  --ds-color-primary-a7: hsla(0, 0%, 0%, 0.5);
  --ds-color-primary-a8: hsla(0, 0%, 0%, 0.6);
  --ds-color-primary-a9: hsla(0, 0%, 0%, 0.7);
  --ds-color-primary-a10: hsla(0, 0%, 0%, 0.8);
  --ds-color-primary-a11: hsla(0, 0%, 0%, 0.85);
  --ds-color-primary-a12: hsla(0, 0%, 0%, 1);
}
