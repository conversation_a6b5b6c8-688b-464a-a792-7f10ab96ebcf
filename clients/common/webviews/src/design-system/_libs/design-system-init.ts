/*

This file should be imported by any app wishing to use a component from the
design system. It will ensure that the correct colors are applied for the
current theme.

*/
import "@poppanator/sveltekit-svg/dist/svg.d.ts";
import "$common-webviews/src/design-system/css/design-system.css";
import { attachAugmentThemeObserver } from "$common-webviews/src/common/hosts/user-themes/theme-store";
import {
  getCategory,
  UserThemeCategory,
} from "$common-webviews/src/common/hosts/user-themes/augment-theme-attributes";

const RADIX_DARK_THEME_CLASS = "dark";
const RADIX_LIGHT_THEME_CLASS = "light";

/**
 * This function will add a class name to the document root that changes
 * the assigned colors for a radix CSS variable.
 *
 * For example, `--red-12` has two values from the @radix-ui/colors module,
 * one value is set with the selector `:root, .light` and one value set with
 * the selector `.dark`.
 *
 * The function maps the VSCode theme name to the radix class name
 *
 * @param theme A users theme
 */
function applyRadixClass(category: UserThemeCategory | undefined) {
  if (category === UserThemeCategory.dark) {
    document.documentElement.classList.add(RADIX_DARK_THEME_CLASS);
    document.documentElement.classList.remove(RADIX_LIGHT_THEME_CLASS);
  } else {
    document.documentElement.classList.add(RADIX_LIGHT_THEME_CLASS);
    document.documentElement.classList.remove(RADIX_DARK_THEME_CLASS);
  }
}

function designSystemInit() {
  const mutationObserver = attachAugmentThemeObserver((category) => {
    applyRadixClass(category);
  });
  applyRadixClass(getCategory());
  return mutationObserver;
}

let mutationObserver: MutationObserver | undefined = undefined;

// This function is exported for testing
export function init() {
  if (mutationObserver === undefined) {
    mutationObserver = designSystemInit();
  }
}

export function reset() {
  mutationObserver?.disconnect();
  mutationObserver = undefined;
}

// If this module is  imported, setup the design system automatically.
init();
