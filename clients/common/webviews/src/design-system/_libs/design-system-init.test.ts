import { expect, test, describe, vi, beforeEach, type MockInstance } from "vitest";
import { init, reset } from "./design-system-init";
import * as themeStore from "$common-webviews/src/common/hosts/user-themes/theme-store";
import { waitFor } from "@testing-library/dom";
import { AUGMENT_THEME_CATEGORY_ATTR } from "$common-webviews/src/common/hosts/user-themes/augment-theme-attributes";

describe("Design System Init", () => {
  let observerSpy: MockInstance;
  let addClassSpy: MockInstance;
  let removeClassSpy: MockInstance;

  beforeEach(() => {
    reset();
    observerSpy = vi.spyOn(themeStore, "attachAugmentThemeObserver");
    addClassSpy = vi.spyOn(document.documentElement.classList, "add");
    removeClassSpy = vi.spyOn(document.documentElement.classList, "remove");
  });

  test("should register only 1 observer", () => {
    expect(observerSpy).toHaveBeenCalledTimes(0);

    init();
    expect(observerSpy).toHaveBeenCalledTimes(1);
    init();
    expect(observerSpy).toHaveBeenCalledTimes(1);
  });

  test("should toggle radix class starting with light theme", async () => {
    expect(observerSpy).toHaveBeenCalledTimes(0);
    expect(addClassSpy).toHaveBeenCalledTimes(0);
    expect(removeClassSpy).toHaveBeenCalledTimes(0);

    init();
    expect(observerSpy).toHaveBeenCalledTimes(1);
    expect(addClassSpy).toHaveBeenCalledTimes(1);
    expect(removeClassSpy).toHaveBeenCalledTimes(1);

    expect(document.documentElement.classList.contains("dark")).toBe(false);
    expect(document.documentElement.classList.contains("light")).toBe(true);

    document.documentElement.setAttribute(AUGMENT_THEME_CATEGORY_ATTR, "dark");
    await waitFor(() => expect(document.documentElement.classList.contains("dark")).toBe(true));
    await waitFor(() => expect(document.documentElement.classList.contains("light")).toBe(false));

    document.documentElement.setAttribute(AUGMENT_THEME_CATEGORY_ATTR, "light");
    await waitFor(() => expect(document.documentElement.classList.contains("dark")).toBe(false));
    await waitFor(() => expect(document.documentElement.classList.contains("light")).toBe(true));

    document.documentElement.setAttribute(AUGMENT_THEME_CATEGORY_ATTR, "dark");
    await waitFor(() => expect(document.documentElement.classList.contains("dark")).toBe(true));
    await waitFor(() => expect(document.documentElement.classList.contains("light")).toBe(false));

    expect(addClassSpy).toHaveBeenCalledTimes(4);
    expect(removeClassSpy).toHaveBeenCalledTimes(4);
  });

  test("should toggle radix class starting with dark theme", async () => {
    expect(observerSpy).toHaveBeenCalledTimes(0);
    expect(addClassSpy).toHaveBeenCalledTimes(0);
    expect(removeClassSpy).toHaveBeenCalledTimes(0);

    document.documentElement.setAttribute(AUGMENT_THEME_CATEGORY_ATTR, "dark");

    init();
    expect(observerSpy).toHaveBeenCalledTimes(1);
    expect(addClassSpy).toHaveBeenCalledTimes(1);
    expect(removeClassSpy).toHaveBeenCalledTimes(1);

    expect(document.documentElement.classList.contains("dark")).toBe(true);
    expect(document.documentElement.classList.contains("light")).toBe(false);

    document.documentElement.setAttribute(AUGMENT_THEME_CATEGORY_ATTR, "dark");
    await waitFor(() => expect(document.documentElement.classList.contains("dark")).toBe(true));
    await waitFor(() => expect(document.documentElement.classList.contains("light")).toBe(false));

    document.documentElement.setAttribute(AUGMENT_THEME_CATEGORY_ATTR, "light");
    await waitFor(() => {
      expect(document.documentElement.classList.contains("dark")).toBe(false);
    });
    await waitFor(() => expect(document.documentElement.classList.contains("light")).toBe(true));

    document.documentElement.setAttribute(AUGMENT_THEME_CATEGORY_ATTR, "dark");
    await waitFor(() => expect(document.documentElement.classList.contains("dark")).toBe(true));
    await waitFor(() => expect(document.documentElement.classList.contains("light")).toBe(false));

    expect(addClassSpy).toHaveBeenCalledTimes(4);
    expect(removeClassSpy).toHaveBeenCalledTimes(4);
  });
});
