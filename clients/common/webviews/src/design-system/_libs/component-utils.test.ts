import { expect, test, describe } from "vitest";
import { dsColorAttribute } from "./component-utils";

describe("Component Utils", () => {
  test("return attribute with color", () => {
    expect(dsColorAttribute("accent")).toEqual({
      // eslint-disable-next-line @typescript-eslint/naming-convention
      "data-ds-color": "accent",
    });

    expect(dsColorAttribute("info")).toEqual({
      // eslint-disable-next-line @typescript-eslint/naming-convention
      "data-ds-color": "info",
    });

    expect(dsColorAttribute("error")).toEqual({
      // eslint-disable-next-line @typescript-eslint/naming-convention
      "data-ds-color": "error",
    });
  });
});
