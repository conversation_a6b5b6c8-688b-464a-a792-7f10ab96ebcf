# Using the Design System

The design system has components and CSS variables you can use in your webview apps.

The following sections describe some of the
useful conventions and how to use the design system.

## Initialize the Design System

Before you can use the design system, you need to initialize it. This is done in the HTML
file of your webview app.

```html
<script type="module" src="/src/design-system/_libs/design-system-init.ts"></script>
```

This initialization script will add the CSS styles
and setup the required JS. **Without this,
the design system will not work.**

## Using Components

Simple components can be imported and used like any other Svelte component.

```html
<script lang="ts">
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
</script>

<ButtonAugment>Example</ButtonAugment>
```

There are some common properties you'll see
across the design system components.

For example:
- **Size**: A number that alters the size of the component and it's text.
    - These are numbers 1+. These are not pixel
    values.
- **Variant**: A string that alters the style of the component.
    - These are strings like "soft", "surface", "solid", etc.
- **Color**: A string that alters the color of the component.
    - These are strings like "accent", "neutral", "error", etc. which get mapped to a color.

### More Complex Components

There are some components which will consist of
child components. Dropdowns are a good example of this.

```html
<script lang="ts">
  import DropdownMenu from "$common-webviews/src/design-system/components/DropdownMenuAugment";
</script>

<DropdownMenu.Root>
  <DropdownMenu.Trigger>
    <ButtonAugment>Trigger</ButtonAugment>
  </DropdownMenu.Trigger>
  <DropdownMenu.Content>
    <DropdownMenu.Item>Item 1</DropdownMenu.Item>
    <DropdownMenu.Item>Item 2</DropdownMenu.Item>
    <DropdownMenu.Item>Item 3</DropdownMenu.Item>
  </DropdownMenu.Content>
</DropdownMenu.Root>
```

These components work together to create a functional UI.

## View Components

You can view the components in the design system
in our Storybook.

In a terminal run `pnpm run dev` from the `clients/common/webviews` directory to start the Storybook.

```shell
$ cd ./clients/common/webviews
$ pnpm run dev
```

## CSS Variables

The design system provides CSS variables that you can use in your webview app. these include
variables for:

- **Colors**
  - `--ds-color-*`
- **Spacing**
  - `--ds-spacing-*`
- **Radius**
  - `--ds-radius-*`
- **Shadow**
  - `--ds-shadow-*`
