# Design System Development

This document describes how the design system
is built. This will include some conventions
and how to build components.

Before going into too much detail, some guiding principles:

- The design system should not implement any business logic.
    - This is the job of the webview apps.
- Components should be generic, not tied to a specific webview app.

## General Structure

The design system largely consists of CSS
variables and Svelte components.

The components expect the CSS variables to be
defined globally, which is added
via the `design-system-init.ts` file, which
is manually added to each html file.

```html
<script type="module" src="/src/design-system/_libs/design-system-init.ts"></script>
```

## CSS Variables

The CSS variables used by the design system follow the naming
convention:

```css
--ds-<group>-<name>-<additional info>
```

The goal is to name variables from least to most specific.

For example, `--ds-color-accent-1` is broken down into:

- `ds`: Design System Namespace
- `color`: Group
- `accent`: Name of the color alias
- `1`: Specific color index

## Colors

We use `@radix-ui/colors` to define the colors
used in the design system.

These css files typically consist of a light and dark variant.

Light colors are added to the `:root` selector and dark colors
are added to the `.dark` selector.

```css
:root {
  --red-1: #fff;
}

.dark {
  --red-1: #000;
}
```

The design-system init library adds the `.dark` class to the document root
when the user is using a dark theme. This is done by listening to the
`augment-theme-category` attribute on the document root, which clients are responsible for setting.

- Intellij sets `augment-theme-category` before rendering the HTML.
- In VSCode we map the VSCode theme kind to the augment theme category.

### Color Aliases

To match up with the Figma library, we alias
the radix colors to "color groups" the match the Figma colors.

For example, the "accent" color group is an
alias of the "indigo" radix color.

```css
:root {
  --ds-color-accent-1: var(--indigo-1);
}
```

Users of the design system are encouraged
to use the color aliases directly.

You can see all the aliases in the `design-system/css/color-aliases/` directory.

#### Component Color Props

Many components have a `color` prop which is
one of the supported color aliases.

```html
<ButtonAugment color="accent">Example</ButtonAugment>
```

To simplify building components, we set the `data-ds-color` attribute on the component
which maps the color alias to a generic CSS variable.

```css
[data-ds-color="accent"] {
  --ds-color-1: var(--ds-color-accent-1);
  --ds-color-2: var(--ds-color-accent-2);
  --ds-color-3: var(--ds-color-accent-3);
  ...
}
```

A component will use these generic variables to define
the colors used in the component.

This allows us to write components that can be used
with any color alias.

A simplified example is show below:

```html
<script lang="ts">
  export let color: "accent" | "info" = "accent";
</script>

<!-- Set the data-ds-color attribute to the color prop -->
<button {...dsColorAttribute(color)}>

<style>
.c-button {
  --button-text-color: var(--ds-color-a11);
  --button-bg-color: var(--ds-color-a2);
  ...
}
</style>
```

### High Contrast

The design system also supports a `data-augment-theme-intensity`
attribute which is set by the client (similar to `data-augment-theme-category`). This attribute is used to
define high contrast styles.

For example, a component can define high contrast styles
via the attribute.

```html
<style>
  .c-button {
    --button-text-color: var(--ds-color-a11);
    --button-high-contrast-text-color: var(--ds-color-12);

    color: var(--button-text-color);
  }

  /* High contrast styles via attribute */
  :global([data-augment-theme-intensity="high-contrast"]) .c-button {
    color: var(--button-high-contrast-text-color);
  }
</style>
```

You'll often see a prop for high contrast as well, but this
is just a convenience prop that allows us view the high
contrast styles in storybook.

## Components

The main considerations when working with components are:

1. Use CSS variables for colors and sizes.
    - This makes it easier to alter the styles of the component
    based on the variant and size props.
2. For components that need to work together, export the
components via an index.ts file and use the `setContent()` and
`getContext()` functions to share state between components.
    - The `design-system/_primitives/TooltipAugment` directory
    is a good example of this.

## Icons

We have a set of SVG icons that we add to the design system.

Icons in `design-system/icons/*.svelte` are Radix components,
you can download these from https://www.radix-ui.com/icons. The
easiest way to get the icons is to click "Download SVG".

Icons in `design-system/icons/vscode/*.svelte` are VSCode icons,
you can download these from https://github.com/microsoft/vscode-codicons.

Once you've added your icon to the project please add the
icon to the storybook story `design-system/mocks/design-system/icons/IconsStory.svelte`.

The storybook ensures the SVG has a fill of "currentColor" which
allows us to change the color of the icon via CSS with the `color` property.
