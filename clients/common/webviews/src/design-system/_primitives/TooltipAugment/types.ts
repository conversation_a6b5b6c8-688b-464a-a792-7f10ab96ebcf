export type TooltipOptions = {
  defaultOpen: boolean | undefined;
  open: boolean | undefined;
  onOpenChange: ((open: boolean) => void) | undefined;
  delayDurationMs: number | undefined;
  /**
   * Whether this tooltip is nested inside another tooltip's content.
   *
   * When true, the Tippy instance will append to the trigger element rather than
   * the document body. This is useful for manually managing focus states for
   * nested interactive tooltips (such as dropdowns).
   */
  nested: boolean | undefined;

  onHoverStart: () => void;
  onHoverEnd: () => void;
  triggerOn: TooltipTriggerOn[];
  tippyTheme: string | undefined;
  hasPointerEvents?: boolean;
  offset?: [number, number];
};

export type TooltipState = {
  open: boolean;
};

/* eslint-disable @typescript-eslint/naming-convention */
export enum TooltipTriggerOn {
  Hover = "hover",
  Click = "click",
}
/* eslint-enable @typescript-eslint/naming-convention */

export type TooltipContentSide = "top" | "bottom" | "left" | "right";
export type TooltipContentAlign = "start" | "center" | "end";
export type TooltipContentProps = {
  side: TooltipContentSide;
  align: TooltipContentAlign;
  onRequestClose?: (e: CloseTooltipRequestEvent) => void;
};

export class CloseTooltipRequestEvent extends Event {
  public static eventType = "augment-ds-event__close-tooltip-request";

  constructor() {
    super(CloseTooltipRequestEvent.eventType, { bubbles: true });
  }

  public static isEvent(e: Event): e is CloseTooltipRequestEvent {
    return e.type === CloseTooltipRequestEvent.eventType;
  }
}
