import userEvent from "@testing-library/user-event";
import { render } from "@testing-library/svelte";
import { expect, describe, test, afterEach, beforeEach, vi } from "vitest";
import TestTooltip from "./multiple.test.svelte";

const INTERACTION_DURATION_MS = 33; // Based on 30FPS
let component: ReturnType<typeof render>;
const expectNoContent = async () => {
  await expectContents(0);
};
const expectTriggers = async (count: number = 2) => {
  const triggers = await component.queryAllByText(/Click me \d/);
  expect(triggers ?? []).toHaveLength(count);
  return triggers;
};
const expectContents = async (count: number = 2) => {
  const content = await component.queryAllByText(/These are the tooltip contents \d/);
  expect(content ?? []).toHaveLength(count);
  return content;
};

beforeEach(() => {
  vi.useFakeTimers();
});

afterEach(() => {
  component.unmount();
  vi.useRealTimers();
});

describe("TooltipAugment.svelte", () => {
  test("multiple tooltips can be used", async () => {
    expect.hasAssertions();
    component = render(TestTooltip);
    await expectTriggers();
    await expectNoContent();
  });

  test("clicking one trigger opens only that tooltip", async () => {
    expect.hasAssertions();
    component = render(TestTooltip);
    const triggers = await expectTriggers();
    await expectNoContent();

    const [firstTrigger] = triggers as HTMLElement[];
    await clickWithFakeTime(firstTrigger);
    await expectContents(1);
  });

  test("opening one trigger and then clicking the other closes the first", async () => {
    component = render(TestTooltip);
    const triggers = await expectTriggers();
    await expectNoContent();

    const [firstTrigger, secondTrigger] = triggers as HTMLElement[];
    expect(firstTrigger).toHaveTextContent("Click me 1");
    expect(secondTrigger).toHaveTextContent("Click me 2");

    await clickWithFakeTime(firstTrigger);
    const [firstContent] = (await expectContents(1)) as HTMLElement[];
    expect(firstContent).toHaveTextContent("These are the tooltip contents 1");

    await clickWithFakeTime(secondTrigger);
    const [secondContent] = (await expectContents(1)) as HTMLElement[];
    expect(secondContent).toHaveTextContent("These are the tooltip contents 2");
  });

  test("clicking one trigger and then clicking outside closes the tooltip", async () => {
    expect.hasAssertions();
    component = render(TestTooltip);
    const triggers = await expectTriggers();
    await expectNoContent();

    const [firstTrigger] = triggers as HTMLElement[];
    await clickWithFakeTime(firstTrigger);
    await expectContents(1);

    await clickWithFakeTime(document.body);
    await expectNoContent();
  });
});

async function clickWithFakeTime(element: Element): Promise<void> {
  const clickPromise = userEvent.click(element);
  await vi.advanceTimersByTimeAsync(INTERACTION_DURATION_MS);
  await clickPromise;
}
