<script lang="ts">
  import { getContext, onDestroy } from "svelte";
  import { TooltipContext } from "./context";
  import {
    CloseTooltipRequestEvent,
    type TooltipContentAlign,
    type TooltipContentSide,
  } from "./types";
  import { derived } from "svelte/store";

  export let onEscapeKeyDown: (event: KeyboardEvent) => void = () => {};
  export let onClickOutside: (event: MouseEvent) => void = () => {};
  export let onRequestClose: (e: CloseTooltipRequestEvent) => void = () => {};
  export let side: TooltipContentSide = "top";
  export let align: TooltipContentAlign = "center";

  const context: TooltipContext = getContext<TooltipContext>(TooltipContext.CONTEXT_KEY);
  const state = context.state;

  // This is a purely passive listener -- it should never modify
  // the event itself. It is only used to detect out-of-bounds clicks
  // during the CAPTURE phase and close the tooltip if necessary.
  const _onClickWindow = (event: MouseEvent) => {
    // Make sure we have a valid event, and that the tooltip is open
    if (
      event.target === null ||
      !(event.target instanceof Node) ||
      !context.contentElement ||
      !context.triggerElement ||
      !$state.open
    ) {
      return;
    }

    // If the click is inside the tooltip contents or trigger, ignore it
    if (
      event.composedPath().includes(context.contentElement) ||
      event.composedPath().includes(context.triggerElement)
    ) {
      return;
    }

    // Otherwise, notify about a click outside and try to close the tooltip.
    context.closeTooltip();
    onClickOutside(event);
  };

  const _onKeyDownWindow = (event: KeyboardEvent) => {
    // Make sure we have a valid event, and that the tooltip is open
    if (
      event.target === null ||
      !(event.target instanceof Node) ||
      !context.contentElement ||
      !$state.open
    ) {
      return;
    }

    // An escape from anywhere should close the tooltip
    if (event.key === "Escape") {
      context.closeTooltip();
      onEscapeKeyDown(event);
    }
  };

  /**
   * Close requests are handled in the BUBBLING phase, to go from a more nested
   * element to a less nested element.
   *
   * This allows for nested contents to behave as expected, with each instance of the left
   * arrow properly closing the nested content and then stopping propagation to avoid
   * the parent context closing.
   *
   * @param e
   */
  const _onRequestClose = (e: Event) => {
    if (
      CloseTooltipRequestEvent.isEvent(e) &&
      e.target &&
      context.contentElement?.contains(e.target as HTMLElement)
    ) {
      context.closeTooltip();
      onRequestClose(e);
      e.stopPropagation();
      return;
    }
  };

  const _onBlurWindow = (e: FocusEvent) => {
    // Check to make sure the entire window is blurred, not just the tooltip
    if (e.target !== window) {
      return;
    }
    context.requestClose();
  };

  // Attach event listeners on the content element when it is open. Should be removed when closed
  const openState = derived(state, ($state) => $state.open);
  $: if (context.contentElement) {
    if ($openState) {
      context.contentElement.addEventListener(CloseTooltipRequestEvent.eventType, _onRequestClose);
    } else {
      context.contentElement.removeEventListener(
        CloseTooltipRequestEvent.eventType,
        _onRequestClose,
      );
    }
  }

  onDestroy(() => {
    context.contentElement?.removeEventListener(
      CloseTooltipRequestEvent.eventType,
      _onRequestClose,
    );
  });
</script>

<!-- Attach a listener to the window to close the tooltip when it is open -->
<svelte:window
  on:click|capture={$state.open ? _onClickWindow : undefined}
  on:keydown|capture={$state.open ? _onKeyDownWindow : undefined}
  on:blur|capture={$state.open ? _onBlurWindow : undefined}
/>
<!-- Note: if we have any interactions inside the dropdown, we should
 capture it to avoid the event propagating to the rest of the page -->
<div
  class="l-tooltip-contents"
  class:l-tooltip-contents--open={$state.open}
  use:context.registerContents={{ side, align }}
  on:click|stopPropagation
  on:keydown|stopPropagation
  role="button"
  tabindex="-1"
  data-position-side={side}
  data-position-align={align}
>
  <slot />
</div>

<style>
  .l-tooltip-contents {
    display: none;
    padding: 0;
    margin: 0;
  }

  .l-tooltip-contents.l-tooltip-contents--open {
    display: block;
  }
</style>
