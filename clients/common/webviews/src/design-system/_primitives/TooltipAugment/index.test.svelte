<script lang="ts">
  import { type ComponentProps } from "svelte";
  import Tooltip from ".";
  import type Root from "./Root.svelte";
  import type Content from "./Content.svelte";

  export let rootProps: ComponentProps<Root>;
  export let contentProps: ComponentProps<Content>;
  export let testHasTrigger: boolean;
  export let testHasContent: boolean;
  export let onClickControlledTrigger: (event: MouseEvent) => void = () => {};
</script>

<Tooltip.Root {...rootProps}>
  {#if testHasTrigger}
    <Tooltip.Trigger>
      <button on:click={onClickControlledTrigger} data-testid="test-trigger-button">
        Click me!
      </button>
    </Tooltip.Trigger>
  {/if}
  {#if testHasContent}
    <Tooltip.Content {...contentProps}>
      <div data-testid="test-content-div">These are the tooltip contents</div>
    </Tooltip.Content>
  {/if}
</Tooltip.Root>
