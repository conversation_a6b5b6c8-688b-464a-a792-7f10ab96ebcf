/**
 * This file contains the default theme for tippy.js with the data-theme="default"
 *
 * It is possible to import their css from their node_module, however the style
 * would be applied to all instances of Tippy. Instead, the styles are copied
 * here with a data-theme selector.
 *
 * https://github.com/atomiks/tippyjs/blob/master/src/scss/index.scss
 */
.tippy-box[data-animation="fade"][data-state="hidden"] {
  opacity: 0;
}

.tippy-box {
  z-index: var(--z-dropdown-menu, 150);
}

[data-tippy-root] {
  max-width: 100vw;
  z-index: var(--z-dropdown-menu, 150);
}

.tippy-box[data-theme~="default"] {
  /* spacing between the tooltip and the edge of the screen, this matches with chat padding */
  margin-inline: var(--ds-spacing-3);
  position: relative;
  background-color: #333;
  color: #fff;
  border-radius: 4px;
  font-size: 14px;
  line-height: 1.4;
  white-space: normal;
  outline: 0;
  transition-property: transform, visibility, opacity;

  &[data-placement^="top"] > .tippy-arrow {
    bottom: 0;

    &:before {
      bottom: -7px;
      left: 0;
      border-width: 8px 8px 0;
      border-top-color: initial;
      transform-origin: center top;
    }
  }

  &[data-placement^="bottom"] > .tippy-arrow {
    top: 0;

    &:before {
      top: -7px;
      left: 0;
      border-width: 0 8px 8px;
      border-bottom-color: initial;
      transform-origin: center bottom;
    }
  }

  &[data-placement^="left"] > .tippy-arrow {
    right: 0;

    &:before {
      border-width: 8px 0 8px 8px;
      border-left-color: initial;
      right: -7px;
      transform-origin: center left;
    }
  }

  &[data-placement^="right"] > .tippy-arrow {
    left: 0;

    &:before {
      left: -7px;
      border-width: 8px 8px 8px 0;
      border-right-color: initial;
      transform-origin: center right;
    }
  }

  &[data-inertia][data-state="visible"] {
    transition-timing-function: cubic-bezier(0.54, 1.5, 0.38, 1.11);
  }

  & .tippy-arrow {
    width: 16px;
    height: 16px;
    color: #333;

    &:before {
      content: "";
      position: absolute;
      border-color: transparent;
      border-style: solid;
    }
  }

  & .tippy-content {
    position: relative;
    padding: 5px 9px;
    z-index: 1;
  }
}
