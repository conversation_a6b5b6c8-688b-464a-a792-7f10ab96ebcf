import { get, type Readable, writable, type Writable } from "svelte/store";
import tippy, {
  type Instance as TippyInstance,
  type Props as Tippy<PERSON>rops,
  type Placement as TippyPlacement,
} from "tippy.js";
import {
  CloseTooltipRequestEvent,
  type TooltipContentProps,
  TooltipTriggerOn,
  type TooltipOptions,
  type TooltipState,
} from "./types";
import { type Action } from "svelte/action";
import { HoverContext, onHover } from "$common-webviews/src/common/actions/onHoverAction";

/**
 * State Management:
 * 1. User interactions will trigger calls to open or close the tooltip
 * 2.a. If the component is controlled, the internal state machine stops here.
 *      The parent component is responsible for updating the state by updating
 *      the `open` prop, which will propagate to the internal `open` state.
 * 2.b. If the component is not controlled, the internal state machine will
 *      update the open state.
 * 3. The changing of the internal `open` state will then trigger a change in the
 *      tippy instance.
 */
export class TooltipContext {
  /* eslint-disable @typescript-eslint/naming-convention */
  public static CONTEXT_KEY: string = "augment-tooltip-context";
  public static DEFAULT_DELAY_DURATION_MS = 250;
  /* eslint-enable @typescript-eslint/naming-convention */

  // The source of truth for whether the tooltip should be open or closed.
  // If the tooltip is externally controlled, this will be updated by the parent.
  // If the tooltip is not, this will be updated directly by `open`
  private _state: Writable<TooltipState>;
  private _tippy: TippyInstance<TippyProps> | undefined;
  private _triggerElement: HTMLElement | undefined;
  private _contentElement: HTMLElement | undefined;
  private _contentProps: TooltipContentProps | undefined;
  private _hoverContext: HoverContext | undefined;
  private _referenceClientRect: DOMRect | undefined;
  private _hasPointerEvents?: boolean = true;

  constructor(private _opts: TooltipOptions) {
    this._state = writable({
      open: this._opts.open ?? this._opts.defaultOpen ?? false,
    });

    if (this.supportsHover) {
      this._hoverContext = new HoverContext({
        hoverTriggerDuration: this.delayDurationMs,
        onHoverStart: () => {
          this.openTooltip();
          this._opts.onHoverStart();
        },
        onHoverEnd: () => {
          this.closeTooltip();
          this._opts.onHoverEnd();
        },
      });
    }

    this._hasPointerEvents = this._opts.hasPointerEvents ?? true;
  }

  public get supportsHover(): boolean {
    return this._opts.triggerOn.includes(TooltipTriggerOn.Hover);
  }

  public get supportsClick(): boolean {
    return this._opts.triggerOn.includes(TooltipTriggerOn.Click);
  }

  public get triggerElement(): HTMLElement | undefined {
    return this._triggerElement;
  }

  public get contentElement(): HTMLElement | undefined {
    return this._contentElement;
  }

  public get state(): Readable<TooltipState> {
    return this._state;
  }

  // The length that the mouse must hover over the trigger before the tooltip is shown
  public get delayDurationMs(): number {
    return this._opts.delayDurationMs ?? TooltipContext.DEFAULT_DELAY_DURATION_MS;
  }

  private get _isExternallyControlled(): boolean {
    const { defaultOpen, open } = this._opts;
    if (open === undefined) {
      return false;
    }

    // Open is provided -- we are in controlled props land
    // We should check that unnecessary props aren't being passed in
    if (defaultOpen !== undefined) {
      console.warn("`defaultOpen` has no effect when `open` is provided");
    }
    return true;
  }

  private get _isOpen(): boolean {
    return get(this._state).open;
  }

  private _setOpen = (open: boolean): void => {
    if (this._isOpen === open) {
      return;
    }

    this._state.update((state) => ({ ...state, open }));
    this._opts.onOpenChange?.(open);
  };

  public openTooltip = (): void => {
    this.internalControlSetOpen(true);
  };

  public closeTooltip = (): void => {
    this.internalControlSetOpen(false);
  };

  public toggleTooltip = (): void => {
    this.internalControlSetOpen(!this._isOpen);
  };

  public externalControlSetOpen = (open: boolean | undefined) => {
    this._opts.open = open;
    if (open === undefined) {
      return;
    }
    this._setOpen(open);
  };

  public updateTippyTheme = (theme: string | undefined) => {
    if (this._opts.tippyTheme === theme) {
      return;
    }
    this._opts.tippyTheme = theme;
    // Call the full update method to ensure all props are properly updated
    // This will handle both existing instances and create new ones if needed
    this._updateTippy();
  };

  private internalControlSetOpen = (open: boolean): void => {
    if (this._isExternallyControlled) {
      return;
    }

    this._setOpen(open);
  };

  /**
   * Propagates updates from the context to tippy.
   * Updates the tippy instance with the current state and elements.
   * - If an instance exists, it will update the props.
   * - If an instance does not exist, it will create a new instance.
   * - If the trigger or content elements are not defined, it will destroy the instance.
   */
  private _updateTippy = (): void => {
    if (!this._triggerElement || !this._contentElement || !this._contentProps) {
      this._tippy?.destroy();
      this._tippy = undefined;
      return;
    }

    const props: Partial<TippyProps> = {
      trigger: "manual",
      showOnCreate: this._isOpen,
      offset: this._opts.offset ?? [0, 2],
      interactive: this._hasPointerEvents,
      content: this._contentElement,
      popperOptions: {
        strategy: "fixed",
        modifiers: [
          {
            name: "preventOverflow",
            options: {
              // Add some padding to the tooltip when it is nested to avoid clipping by container
              padding: this._opts.nested ? 12 : 0,
            },
          },
        ],
      },
      duration: 0,
      delay: 0,
      placement: contentPropsToPlacement(this._contentProps),
      hideOnClick: true,
      // https://atomiks.github.io/tippyjs/v6/faq/#my-tooltip-appears-cut-off-or-is-not-showing-at-all
      // Tippy must be appended to the body to avoid clipping issues
      // If the tooltip requires interactivity, make sure to implement proper tab behavior.
      // If the tooltip is nested, we should append to the trigger element instead to
      // make the tab focus behavior easier.
      appendTo: this._opts.nested ? this._triggerElement : document.body,
      theme: this._opts.tippyTheme,
    };

    // If the reference client rect is defined, we should use it to position the tooltip
    if (this._referenceClientRect !== undefined) {
      const referenceClientRect = this._referenceClientRect;
      props.getReferenceClientRect = () => referenceClientRect;
    }

    if (this._tippy !== undefined) {
      this._tippy.setProps(props);
    } else {
      // Whenever the state is modified, we should update the actual
      // tippy state to match the state that we are tracking.
      const onTippyDestroy = this._state.subscribe((state) => {
        if (state.open) {
          this._tippy?.show();
        } else {
          this._tippy?.hide();
        }
      });
      this._tippy = tippy(this._triggerElement, {
        ...props,
        onDestroy: onTippyDestroy,
      });
    }
  };

  /**
   * Repositions the tooltip based on the current state of the content element.
   */
  update = () => {
    void this._tippy?.popperInstance?.update();
  };

  /**
   * Action to register the trigger element.
   * This will allow the context to handle the lifecycle of the Tippy instance
   * based on the state of the Svelte elements.
   */
  registerTrigger: Action<HTMLDivElement, DOMRect | undefined> = (
    node: HTMLDivElement,
    referenceClientRect: DOMRect | undefined,
  ) => {
    this._triggerElement = node;
    this._referenceClientRect = referenceClientRect;

    const hoverAction = this._hoverContext && onHover(this._triggerElement, this._hoverContext);
    this._updateTippy();

    return {
      update: (newReferenceClientRect: DOMRect | undefined) => {
        this._referenceClientRect = newReferenceClientRect;
        this._updateTippy();
      },
      destroy: () => {
        hoverAction?.destroy();
        this._triggerElement = undefined;
        this._updateTippy();
      },
    };
  };

  /**
   * Action to register the content element.
   * This will allow the context to handle the lifecycle of the Tippy instance
   * based on the state of the Svelte elements.
   */
  registerContents: Action<HTMLDivElement, TooltipContentProps> = (
    node: HTMLDivElement,
    props: TooltipContentProps,
  ) => {
    node.remove();
    this._contentElement = node;
    this._contentProps = props;
    const hoverAction = this._hoverContext && onHover(this._contentElement, this._hoverContext);
    this._updateTippy();

    // Whenever the tooltip resizes, we should reposition it
    const disconnectResizeObserver = attachResizeHandler(node, this.update);
    return {
      destroy: () => {
        hoverAction?.destroy();
        this._contentElement = undefined;
        this._updateTippy();
        disconnectResizeObserver();
      },
      update: (newProps: Partial<TooltipContentProps>) => {
        props = {
          ...props,
          ...newProps,
        };
        this._contentProps = props;
        this._updateTippy();
      },
    };
  };

  requestClose = () => {
    this._contentElement?.dispatchEvent(new CloseTooltipRequestEvent());
  };
}

function contentPropsToPlacement(props: TooltipContentProps): TippyPlacement {
  if (props.align === "center") {
    return props.side;
  } else {
    return `${props.side}-${props.align}`;
  }
}

function attachResizeHandler(element: HTMLElement, onResize: () => void): () => void {
  const resizeObserver = new ResizeObserver(() => onResize());
  resizeObserver.observe(element);
  return () => resizeObserver.disconnect();
}
