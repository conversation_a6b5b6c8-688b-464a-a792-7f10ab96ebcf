<script lang="ts">
  import Tooltip from ".";
  import ButtonAugment from "../../components/ButtonAugment.svelte";
  import { TooltipTriggerOn } from "./types";
</script>

<Tooltip.Root triggerOn={[TooltipTriggerOn.Click]}>
  <Tooltip.Trigger>
    <ButtonAugment>Click me 1</ButtonAugment>
  </Tooltip.Trigger>
  <Tooltip.Content>
    <div>These are the tooltip contents 1</div>
  </Tooltip.Content>
</Tooltip.Root>

<Tooltip.Root triggerOn={[TooltipTriggerOn.Click]}>
  <Tooltip.Trigger>
    <ButtonAugment>Click me 2</ButtonAugment>
  </Tooltip.Trigger>
  <Tooltip.Content>
    <div>These are the tooltip contents 2</div>
  </Tooltip.Content>
</Tooltip.Root>
