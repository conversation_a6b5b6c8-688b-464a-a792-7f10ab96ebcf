<script lang="ts" context="module">
  export type ButtonSize = 0 | 0.5 | 1 | 2 | 3 | 4;
  export type ButtonVariant =
    | "classic"
    | "solid"
    | "soft"
    | "surface"
    | "outline"
    | "ghost"
    | "ghost-block";
  export type ButtonColor = "accent" | "neutral" | "error" | "success" | "warning" | "info";
  export type ButtonAlignment = "left" | "center" | "right";
  // We have no clear use case for supporting other radius values
  // yet, so limiting to these two for now
  // See DesignSystemRadius for all supported values
  export type ButtonRadius = "medium" | "full";

  export function sizeToClassSize(buttonSize: number) {
    return String(buttonSize).replace(".", "_");
  }
</script>

<script lang="ts">
  import "@radix-ui/colors/black-alpha.css";
  import "@radix-ui/colors/white-alpha.css";
  import "@radix-ui/colors/gray.css";
  import "@radix-ui/colors/gray-alpha.css";
  import "@radix-ui/colors/gray-dark-alpha.css";
  import "@radix-ui/colors/gray-dark.css";

  import { dsColorAttribute, dsRadiusAttribute } from "../_libs/component-utils";
  import SpinnerAugment from "../components/SpinnerAugment.svelte";

  export let size: ButtonSize = 2;
  export let variant: ButtonVariant = "solid";
  export let color: ButtonColor = "accent";
  export let disabled: boolean = false;
  export let highContrast: boolean = false;
  export let loading: boolean = false;
  export let alignment: ButtonAlignment = "center";
  export let radius: ButtonRadius = "medium";

  $: ({ class: className, ...restProps } = $$restProps);
  function buttonToSpinnerSize(buttonSize: ButtonSize) {
    switch (buttonSize) {
      case 0:
      case 0.5:
      case 1:
        return 1;
      case 2:
      case 3:
        return 2;
      case 4:
        return 3;
    }
  }
</script>

<button
  {...dsColorAttribute(color)}
  {...dsRadiusAttribute(radius)}
  class={`c-base-btn c-base-btn--size-${sizeToClassSize(size)} c-base-btn--${variant} c-base-btn--${color} ${className} c-base-btn--alignment-${alignment}`}
  class:c-base-btn--highContrast={highContrast}
  class:c-base-btn--loading={loading}
  disabled={disabled || loading}
  on:click
  on:keyup
  on:keydown
  on:mousedown
  on:mouseover
  on:focus
  on:mouseleave
  on:blur
  on:contextmenu
  {...restProps}
>
  {#if loading}
    <div class="c-base-btn__loading">
      <SpinnerAugment size={buttonToSpinnerSize(size)} />
    </div>
    <span class="c-base-btn__hidden-content">
      <slot />
    </span>
  {:else}
    <slot />
  {/if}
</button>

<style>
  .c-base-btn {
    /* Unset default button styles */
    all: unset;

    --base-btn-width: unset;
    --base-btn-padding-vertical: 0px;
    --base-btn-hover-filter: unset;
    --base-btn-active-filter: unset;
    --base-btn-outline-box-shadow: inset 0 0 0 1px var(--ds-color-a8);
    --base-btn-surface-box-shadow: inset 0 0 0 1px var(--ds-color-a7);
    --base-btn-disabled-color: var(--gray-a8);
    --base-btn-disabled-bg-color: var(--gray-a3);
    --base-btn-focus-outline-offset: 2px;
    --base-btn-flex-default: 0 0 auto;

    display: inline-flex;
    align-items: center;
    justify-content: center;
    flex: var(--base-btn-flex, var(--base-btn-flex-default));
    user-select: none;
    vertical-align: top;
    box-sizing: border-box;
    line-height: initial;

    width: var(--base-btn-width);
    border-radius: var(--base-btn-border-radius);

    color: var(--base-btn-color);
    background-color: var(--base-btn-bg-color);
    box-shadow: var(--base-btn-box-shadow);
    background-image: var(--base-btn-background-image);

    /* For chaser effect */
    &:global(:has(.c-chaser)) {
      position: relative;
      overflow: hidden;
      z-index: 0;
    }

    &:disabled {
      color: var(--base-btn-disabled-color);
      background-color: var(--base-btn-disabled-bg-color);
      box-shadow: var(--base-btn-disabled-box-shadow);
      cursor: not-allowed;
    }

    &:not(:disabled) {
      &:hover {
        filter: var(--base-btn-hover-filter);
        background-color: var(--base-btn-hover-bg-color);
        box-shadow: var(--base-btn-hover-box-shadow);
        background-image: var(--base-btn-hover-background-image);
        cursor: pointer;
      }

      &:active {
        filter: var(--base-btn-active-filter);
        background-color: var(--base-btn-active-bg-color);
        box-shadow: var(--base-btn-active-box-shadow);
      }

      &:focus-visible {
        outline: 2px solid var(--ds-color-8);
        outline-offset: var(--base-btn-focus-outline-offset);
      }
    }
  }

  .c-base-btn.c-base-btn--highContrast:not(:disabled),
  :global([data-augment-theme-intensity="high-contrast"]) .c-base-btn:not(:disabled) {
    color: var(--base-btn-high-contrast-color);
    background-color: var(--base-btn-high-contrast-bg-color);
    box-shadow: var(--base-btn-high-contrast-box-shadow);
    background-image: var(--base-btn-high-contrast-background-image);

    &:hover {
      background-color: var(--base-btn-high-contrast-hover-bg-color);
      filter: var(--base-btn-high-contrast-hover-filter);
      box-shadow: var(--base-btn-high-contrast-hover-box-shadow);
      background-image: var(--base-btn-high-constrast-hover-background-image);
    }

    &:active {
      background-color: var(--base-btn-high-contrast-active-bg-color);
      filter: var(--base-btn-high-contrast-active-filter);
      box-shadow: var(--base-btn-high-contrast-active-box-shadow);
    }
  }

  .c-base-btn--alignment-left {
    justify-content: left;
  }
  .c-base-btn--alignment-right {
    justify-content: right;
  }

  .c-base-btn--loading {
    position: relative;
  }

  .c-base-btn__hidden-content {
    display: contents;
    visibility: hidden;
  }

  .c-base-btn__loading {
    width: calc(100% - var(--base-btn-padding-horizontal) * 2);
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    top: 0;

    & :global(svg.c-spinner-svg path) {
      fill: var(--base-btn-disabled-color);
    }
  }

  /** Variants */

  .c-base-btn--solid {
    --base-btn-hover-bg-color: var(--ds-color-10);
    --base-btn-active-bg-color: var(--ds-color-10);
    --base-btn-active-filter: brightness(0.92) saturate(1.1);
    --base-btn-high-contrast-hover-bg-color: var(--ds-color-12);
    --base-btn-high-contrast-active-bg-color: var(--ds-color-12);
    --base-btn-high-contrast-hover-filter: opacity(0.88);
    --base-btn-high-contrast-active-filter: opacity(0.82);
  }

  :global(.dark) .c-base-btn--solid,
  :global([data-augment-theme-mode="dark"]) .c-base-btn--solid {
    --base-btn-active-filter: brightness(1.08);
    --base-btn-high-contrast-hover-filter: contrast(0.88) saturate(1.3) brightness(1.18);
    --base-btn-high-contrast-active-filter: brightness(0.95) saturate(1.2);
  }

  .c-base-btn--soft {
    --base-btn-bg-color: var(--ds-color-a3);
    --base-btn-hover-bg-color: var(--ds-color-a4);
    --base-btn-active-bg-color: var(--ds-color-a5);
  }

  .c-base-btn--ghost,
  .c-base-btn--ghost-block {
    --base-btn-bg-color: unset;
    --base-btn-hover-bg-color: var(--ds-color-a3);
    --base-btn-active-bg-color: var(--ds-color-a4);
    --base-btn-disabled-bg-color: transparent;
  }

  .c-base-btn--soft,
  .c-base-btn--ghost,
  .c-base-btn--ghost-block {
    --base-btn-high-contrast-bg-color: var(--base-btn-bg-color);
    --base-btn-high-contrast-hover-bg-color: var(--base-btn-hover-bg-color);
    --base-btn-high-contrast-active-bg-color: var(--base-btn-active-bg-color);
  }

  .c-base-btn--outline {
    --base-btn-box-shadow: var(--base-btn-outline-box-shadow);
    --base-btn-hover-bg-color: var(--ds-color-a2);
    --base-btn-hover-box-shadow: var(--base-btn-box-shadow);
    --base-btn-active-bg-color: var(--ds-color-a3);
    --base-btn-active-box-shadow: var(--base-btn-box-shadow);
    --base-btn-high-contrast-box-shadow: var(--base-btn-surface-box-shadow),
      inset 0 0 0 1px var(--gray-a11);
    --base-btn-high-contrast-hover-bg-color: var(--base-btn-hover-bg-color);
    --base-btn-high-contrast-active-bg-color: var(--base-btn-active-bg-color);
    --base-btn-high-contrast-hover-box-shadow: var(--base-btn-high-contrast-box-shadow);
    --base-btn-high-contrast-active-box-shadow: var(--base-btn-high-contrast-box-shadow);
    --base-btn-disabled-bg-color: transparent;
  }

  .c-base-btn--ghost,
  .c-base-btn--ghost-block,
  .c-base-btn--outline {
    --base-btn-focus-outline-offset: -1px;
  }

  .c-base-btn--surface {
    --base-btn-bg-color: var(--ds-color-a2);
    --base-btn-box-shadow: var(--base-btn-surface-box-shadow);
    --base-btn-hover-bg-color: var(--base-btn-bg-color);
    --base-btn-hover-box-shadow: var(--base-btn-outline-box-shadow);
    --base-btn-active-box-shadow: var(--base-btn-outline-box-shadow);
    --base-btn-active-bg-color: var(--ds-color-a3);
    --base-btn-focus-outline-offset: -1px;
    --base-btn-disabled-bg-color: var(--gray-a2);
    --base-btn-high-contrast-bg-color: var(--base-btn-bg-color);
    --base-btn-high-contrast-box-shadow: var(--base-btn-box-shadow);
    --base-btn-high-contrast-hover-bg-color: var(--base-btn-bg-color);
    --base-btn-high-contrast-active-bg-color: var(--base-btn-active-bg-color);
    --base-btn-high-contrast-hover-box-shadow: var(--base-btn-hover-box-shadow);
    --base-btn-high-contrast-active-box-shadow: var(--base-btn-active-box-shadow);
  }

  .c-base-btn--outline,
  .c-base-btn--surface {
    --base-btn-disabled-box-shadow: inset 0 0 0 1px var(--gray-a6);
  }

  .c-base-btn--outline,
  .c-base-btn--surface,
  .c-base-btn--soft,
  .c-base-btn--ghost,
  .c-base-btn--ghost-block {
    --base-btn-color: var(--ds-color-a11);
    --base-btn-high-contrast-color: var(--ds-color-12);
  }

  .c-base-btn--classic {
    --base-btn-box-shadow-top: inset 0 0 0 1px var(--gray-a4), inset 0 -2px 1px var(--gray-a3);
    --base-btn-box-shadow-bottom: (
      inset 0 4px 2px -2px var(--white-a9),
      inset 0 2px 1px -1px var(--white-a9)
    );
    --base-btn-background-image-top: linear-gradient(to bottom, transparent 50%, var(--gray-a4));
    --base-btn-after-border-width: 2px;

    --base-btn-box-shadow: (
      var(--base-btn-box-shadow-top),
      inset 0 0 0 1px var(--base-btn-bg-color),
      var(--base-btn-box-shadow-bottom)
    );
    --base-btn-background-image: (
      var(--base-btn-background-image-top),
      linear-gradient(to bottom, transparent 50%, var(--base-btn-bg-color) 80%)
    );
    --base-btn-hover-bg-color: var(--base-btn-bg-color);
    --base-btn-hover-background-image: var(--base-btn-background-image);
    --base-btn-hover-box-shadow: var(--base-btn-box-shadow);
    --base-btn-active-bg-color: var(--base-btn-bg-color);
    --base-btn-active-box-shadow: (
      inset 0 4px 2px -2px var(--gray-a4),
      inset 0 1px 1px var(--gray-a7),
      inset 0 0 0 1px var(--gray-a5),
      inset 0 0 0 1px var(--base-btn-bg-color),
      inset 0 3px 2px var(--gray-a3),
      inset 0 0 0 1px var(--white-a7),
      inset 0 -2px 1px var(--white-a5)
    );
    --base-btn-high-contrast-box-shadow: (
      var(--base-btn-box-shadow-top),
      inset 0 0 0 1px var(--base-btn-high-contrast-bg-color),
      var(--base-btn-box-shadow-bottom)
    );
    --base-btn-high-contrast-background-image: (
      var(--base-btn-background-image-top),
      linear-gradient(to bottom, transparent 50%, var(--base-btn-high-contrast-bg-color) 80%)
    );
    --base-btn-high-contrast-hover-bg-color: var(--base-btn-high-contrast-bg-color);
    --base-btn-high-contrast-hover-filter: contrast(0.88) saturate(1.1) brightness(1.1);
    --base-btn-high-contrast-hover-box-shadow: var(--base-btn-high-contrast-box-shadow);
    --base-btn-high-contrast-hover-background-image: var(--base-btn-high-contrast-background-image);
    --base-btn-high-contrast-active-bg-color: var(--base-btn-high-contrast-bg-color);
    --base-btn-high-contrast-active-filter: contrast(0.82) saturate(1.2) brightness(1.16);
    --base-btn-high-contrast-active-box-shadow: inset 0 0 0 1px
      var(--base-btn-high-contrast-bg-color);
    --base-btn-disabled-bg-color: var(--gray-2);
    --base-btn-disabled-box-shadow: (
      inset 0 0 0 1px var(--gray-a5),
      inset 0 4px 2px -2px var(--gray-a2),
      inset 0 1px 1px var(--gray-a5),
      inset 0 -1px 1px var(--black-a3),
      inset 0 0 0 1px var(--gray-a2)
    );

    position: relative;
    z-index: 0;

    &::after {
      content: "";
      position: absolute;
      border-radius: inherit;
      pointer-events: none;
      inset: 0;
      z-index: -1;
      border: var(--base-btn-after-border-width) solid transparent;
      background-clip: content-box;
      background-color: inherit;
      background-image: linear-gradient(var(--black-a1), transparent, var(--white-a2));
      box-shadow: inset 0 2px 3px -1px var(--white-a4);
    }

    &:disabled {
      background-image: none;

      &::after {
        box-shadow: none;
        background-color: var(--slate-a2);
        background-image: linear-gradient(var(--black-a1) -20%, transparent, var(--white-a1));
      }
    }

    &:hover:not(:disabled)::after {
      background-color: var(--ds-color-10);
      background-image: linear-gradient(var(--black-a2) -15%, transparent, var(--white-a3));
    }

    &:active:not(:disabled) {
      background-image: linear-gradient(var(--black-a1), transparent);

      /* Add padding-top to the button (the root) and reduce padding on the content bottom */
      padding-top: var(--base-btn-classic-padding-top);
      & :global(.c-button--content) {
        padding-block: calc(
          var(--base-btn-padding-vertical) - (var(--base-btn-classic-padding-top) / 2)
        );
      }

      &::after {
        background-color: inherit;
        box-shadow: none;
        background-image: linear-gradient(var(--black-a2), transparent, var(--white-a3));
      }
    }
  }

  .c-base-btn--classic.c-base-btn--highContrast:not(:disabled),
  :global([data-augment-theme-intensity="high-contrast"]) .c-base-btn--classic:not(:disabled) {
    &::after {
      background-image: linear-gradient(var(--black-a3), transparent, var(--white-a2));
    }

    &:hover:not(:disabled)::after {
      background-color: var(--ds-color-12);
      background-image: linear-gradient(var(--black-a5), transparent, var(--white-a2));
    }

    &:active:not(:disabled)::after {
      background-image: linear-gradient(var(--black-a5), transparent, var(--white-a3));
    }
  }

  :global(.dark) .c-base-btn--classic,
  :global([data-augment-theme-mode="dark"]) .c-base-btn--classic {
    --base-btn-after-border-width: 1px;
    --base-btn-box-shadow-top: (
      inset 0 0 0 1px var(--white-a2),
      inset 0 4px 2px -2px var(--white-a3),
      inset 0 1px 1px var(--white-a6),
      inset 0 -1px 1px var(--black-a6)
    );
    --base-btn-high-contrast-hover-filter: contrast(0.88) saturate(1.3) brightness(1.14);
    --base-btn-high-contrast-active-filter: brightness(0.95) saturate(1.2);
  }

  .c-base-btn--solid,
  .c-base-btn--classic {
    --base-btn-bg-color: var(--ds-color-9);
    --base-btn-color: var(--ds-accent-contrast);

    --base-btn-high-contrast-color: var(--gray-1);
    --base-btn-high-contrast-bg-color: var(--ds-color-12);
  }

  /** Sizes */
  .c-base-btn--size-0 {
    --base-btn-border-radius: max(var(--ds-radius-1), var(--ds-internal-vars-radius-full));
    /** 2px*/
    --base-btn-gap: var(--ds-spacing-1);
    /** 6px horizontal padding */
    --base-btn-padding-horizontal: calc(var(--ds-spacing-1) * 0.5);
    /** 2px vertical padding */
    --base-btn-padding-vertical: calc(var(--ds-spacing-1) * 0.5);
    --base-btn-classic-padding-top: 1px;

    &.c-base-btn--ghost {
      --base-btn-padding-vertical: var(--ds-spacing-1);
    }
  }
  .c-base-btn--size-0_5 {
    --base-btn-border-radius: max(var(--ds-radius-1), var(--ds-internal-vars-radius-full));
    /** 2px*/
    --base-btn-gap: var(--ds-spacing-1);
    /** 6px horizontal padding */
    --base-btn-padding-horizontal: calc(var(--ds-spacing-1) * 1.5);
    /** 2px vertical padding */
    --base-btn-padding-vertical: calc(var(--ds-spacing-1) * 0.5);
    --base-btn-classic-padding-top: 1px;

    &.c-base-btn--ghost {
      --base-btn-padding-vertical: var(--ds-spacing-1);
    }
  }

  .c-base-btn--size-1 {
    --base-btn-border-radius: max(var(--ds-radius-1), var(--ds-internal-vars-radius-full));
    --base-btn-gap: var(--ds-spacing-1);
    --base-btn-padding-horizontal: var(--ds-spacing-1);
    --base-btn-padding-vertical: var(--ds-spacing-1);
    --base-btn-classic-padding-top: 1px;

    &.c-base-btn--ghost {
      --base-btn-padding-vertical: calc(var(--ds-spacing-1) * 0.75);
    }
  }

  .c-base-btn--size-2 {
    --base-btn-border-radius: max(var(--ds-radius-2), var(--ds-internal-vars-radius-full));
    --base-btn-gap: var(--ds-spacing-2);
    --base-btn-padding-horizontal: var(--ds-spacing-3);
    --base-btn-padding-vertical: var(--ds-spacing-2);
    --base-btn-classic-padding-top: 2px;

    &.c-base-btn--ghost {
      --base-btn-gap: var(--ds-spacing-1);
      --base-btn-padding-vertical: var(--ds-spacing-1);
      --base-btn-padding-horizontal: var(--ds-spacing-2);
    }
  }

  .c-base-btn--size-3 {
    --base-btn-border-radius: max(var(--ds-radius-3), var(--ds-internal-vars-radius-full));
    --base-btn-gap: var(--ds-spacing-3);
    --base-btn-padding-horizontal: var(--ds-spacing-4);
    --base-btn-padding-vertical: var(--ds-spacing-2);
    --base-btn-classic-padding-top: 2px;

    &.c-base-btn--ghost {
      --base-btn-gap: var(--ds-spacing-2);
      --base-btn-padding-vertical: calc(var(--ds-spacing-1) * 1.5);
      --base-btn-padding-horizontal: var(--ds-spacing-3);
    }
  }

  .c-base-btn--size-4 {
    --base-btn-border-radius: max(var(--ds-radius-4), var(--ds-internal-vars-radius-full));
    --base-btn-gap: var(--ds-spacing-4);
    --base-btn-padding-horizontal: var(--ds-spacing-5);

    /* This results in 48px height for single line of text and matches icon btn */
    --base-btn-padding-vertical: 11px;
    --base-btn-classic-padding-top: 2px;

    &.c-base-btn--ghost {
      --base-btn-gap: var(--ds-spacing-2);
      --base-btn-padding-vertical: var(--ds-spacing-2);
      --base-btn-padding-horizontal: var(--ds-spacing-4);
    }
  }
</style>
