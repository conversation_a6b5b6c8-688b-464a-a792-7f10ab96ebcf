<script lang="ts">
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import OpenFileButton from "../chat/components/conversation/blocks/tools/components/OpenFileButton.svelte";
  import { ExtensionClient } from "../chat/extension-client";
  import { host } from "$common-webviews/src/common/hosts/host";
  import { MessageBroker } from "$common-webviews/src/common/utils/message-broker";
  import { ChatFlagsModel } from "../chat/models/chat-flags-model";
  import MarkdownEditor from "$common-webviews/src/design-system/components/MarkdownEditor.svelte";
  import { type Rule, RuleType } from "@augment-internal/sidecar-libs/src/chat/chat-types";
  import RulesModeSelector from "./RulesModeSelector.svelte";
  import ChevronLeft from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/chevron-left.svg?component";
  import { WebViewMessageType } from "$vscode/src/webview-providers/webview-messages";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import TextFieldAugment from "$common-webviews/src/design-system/components/TextFieldAugment.svelte";
  import { debounce } from "lodash";
  import { RulesModel } from "../settings/models/rules-model";

  export let rule: Rule;
  $: path = rule.path;
  $: ruleType = rule.type;
  // Making these mutable because of form state
  let text = rule.content;
  let description = rule.description;

  // Create a stable rule object for the RulesModeSelector
  $: currentRule = {
    path,
    type: ruleType,
    content: text,
    description: description,
  };

  // Create extension client directly
  const msgBroker = new MessageBroker(host);
  const flagsModel = new ChatFlagsModel();
  const extensionClient = new ExtensionClient(host, msgBroker, flagsModel);
  const rulesModel = new RulesModel(msgBroker);

  const onSave = async (newRuleType: RuleType, newDescription?: string) => {
    // Update local state immediately for UI responsiveness
    rule = {
      ...rule,
      type: newRuleType,
      description: newDescription || description,
    };

    // Update the description if it was provided
    if (newDescription !== undefined) {
      description = newDescription;
    }

    // Save to the model
    try {
      await rulesModel.updateRuleContent({
        type: newRuleType,
        path,
        content: text,
        description: newDescription || description,
      });
    } catch (error) {
      console.error("RulesMarkdownEditor: Error in rulesModel.updateRuleContent:", error);
    }
  };

  // Create a debounced version of the save function for description changes
  const debouncedSaveDescription = debounce(onSave, 500);

  const navigateToGuidelines = () => {
    host.postMessage({
      type: WebViewMessageType.openSettingsPage,
      data: "guidelines",
    });
  };
</script>

<MarkdownEditor
  bind:value={text}
  saveFunction={() => onSave(ruleType, description)}
  variant="surface"
  size={2}
  resize="vertical"
  class="markdown-editor"
>
  <div class="l-file-controls" slot="header">
    <div class="l-file-controls-left">
      <div class="c-trigger-section">
        <TextTooltipAugment content="Navigate back to all Rules & Guidelines">
          <ButtonAugment
            size={1}
            variant="ghost-block"
            color="neutral"
            class="c-back-button"
            on:click={navigateToGuidelines}
          >
            <ChevronLeft slot="iconLeft" />
          </ButtonAugment>
        </TextTooltipAugment>
        <TextAugment size={1} class="c-field-label">Trigger:</TextAugment>
        <RulesModeSelector {onSave} rule={currentRule} />
      </div>
    </div>
    <OpenFileButton
      size={1}
      {path}
      onOpenLocalFile={async () => {
        extensionClient.openFile({
          repoRoot: "",
          pathName: path,
        });
        return "success";
      }}
    >
      <TextAugment slot="text" size={1}>Open file</TextAugment>
    </OpenFileButton>
  </div>
  {#if ruleType === RuleType.AGENT_REQUESTED}
    <div class="c-rule-config">
      <div class="c-rule-field c-rule-field-full-width">
        <TextAugment size={1} class="c-field-label">Description</TextAugment>
        <TextFieldAugment
          bind:value={description}
          on:input={() => debouncedSaveDescription(ruleType, description)}
          placeholder="When should this rules file be fetched by the Agent?"
          size={1}
        />
      </div>
    </div>
  {/if}
</MarkdownEditor>

<style>
  .l-file-controls {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--ds-spacing-2);
    flex-shrink: 0; /* Prevent the controls from shrinking */
    padding: var(--ds-spacing-2) 0;
  }
  .l-file-controls :global(svg) {
    --button-icon-size: var(--ds-icon-size-1);
  }
  .l-file-controls-left {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: var(--ds-spacing-3);
    flex-wrap: wrap; /* Allow wrapping on smaller screens */
  }

  .c-trigger-section {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: var(--ds-spacing-2);
  }
  .c-rule-config {
    display: flex;
    flex-direction: column;
    width: 100%;
    gap: var(--ds-spacing-2);
    padding: var(--ds-spacing-2) 0;
  }
  .c-rule-field {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-2);
  }
  .c-rule-field-full-width {
    width: 100%;
  }
  :global(.c-back-button) {
    margin-right: var(--ds-spacing-3);
  }

  /* Responsive design for smaller screens */
  @media (max-width: 600px) {
    .l-file-controls {
      flex-direction: column;
      align-items: flex-start;
      gap: var(--ds-spacing-2);
    }

    .l-file-controls-left {
      flex-direction: column;
      align-items: flex-start;
      width: 100%;
    }

    .c-trigger-section {
      width: 100%;
    }
  }
</style>
