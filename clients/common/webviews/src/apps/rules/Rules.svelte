<script lang="ts">
  import { host } from "$common-webviews/src/common/hosts/host";
  import {
    WebViewMessageType,
    type WebViewMessage,
  } from "$vscode/src/webview-providers/webview-messages";
  import RulesMarkdownEditor from "./RulesMarkdownEditor.svelte";
  import { writable } from "svelte/store";
  import { onMount } from "svelte";
  import { MessageBroker } from "$common-webviews/src/common/utils/message-broker";
  import { RulesParser } from "@augment-internal/sidecar-libs/src/utils/rules-parser";
  import { type Rule } from "@augment-internal/sidecar-libs/src/chat/chat-types";

  const msgBroker = new MessageBroker(host);
  const rule = writable<Rule | null>(null);

  const rulesConsumer = {
    handleMessageFromExtension(event: MessageEvent<WebViewMessage>) {
      const message = event.data;

      // Type guard to check if the message is a LoadFileMessage
      if (message && message.type === WebViewMessageType.loadFile) {
        if (message) {
          // Handle content - check for both content and text for backward compatibility
          const fileContent = message.data.content;
          if (fileContent !== undefined) {
            // Strip beginning newlines from the text
            const trimmedText = fileContent.replace(/^\n+/, "");
            // Parse the description from the frontmatter
            const ruleDescription = RulesParser.getDescriptionFrontmatterKey(trimmedText);
            // Determine the rule type
            const currentRuleType = RulesParser.getRuleTypeFromContent(trimmedText);
            const markdownContent = RulesParser.extractContent(trimmedText);
            rule.set({
              path: message.data.pathName,
              content: markdownContent,
              type: currentRuleType,
              description: ruleDescription,
            });
          }
        }
      }
      return true;
    },
  };

  onMount(() => {
    // Set up message listener
    msgBroker.registerConsumer(rulesConsumer);

    // Signal to the extension that the webview is ready to receive content
    host.postMessage({
      type: WebViewMessageType.rulesLoaded,
    });
  });
</script>

<svelte:window on:message={msgBroker.onMessageFromExtension} />
<div class="c-rules-container">
  {#if $rule !== null}
    <RulesMarkdownEditor rule={$rule} />
  {:else}
    <div>Loading...</div>
  {/if}
</div>

<style>
  .c-rules-container {
    display: flex;
    flex-direction: column;
    padding: var(--ds-spacing-4);
    background: var(--ds-surface);
    width: 100%;
    height: 100vh;
    box-sizing: border-box;
    overflow: auto;
    flex: 1 1 auto;
  }
  :global(.c-rules-navigation) {
    height: 100%;
  }
  :global(.c-detail) {
    width: 100%;
  }
  .c-rules-container :global(.c-detail__summary) {
    padding: var(--ds-spacing-1);
  }
  :global(.c-detail__content) {
    padding-top: var(--ds-spacing-2);
    width: 100%;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: stretch;
  }
  :global(.markdown-editor-container) {
    width: 100%;
    max-width: 100%;
    flex: 1 1 auto;
    display: flex;
    flex-direction: column;
  }
  :global(.editor-content),
  :global(.markdown-textarea) {
    width: 100%;
    box-sizing: border-box;
    flex: 1 1 auto;
    min-height: 80vh;
  }
  :global(.c-markdown) {
    width: 100%;
  }
</style>
