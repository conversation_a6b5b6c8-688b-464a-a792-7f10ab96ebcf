<script lang="ts">
  import { type Readable } from "svelte/store";
  import DropdownMenuAugment from "$common-webviews/src/design-system/components/DropdownMenuAugment";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import ChevronDown from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/chevron-down.svg?component";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import { AUGMENT_GUIDELINES_FILE } from "@augment-internal/sidecar-libs/src/utils/rules-parser";
  import { type Rule, RuleType } from "@augment-internal/sidecar-libs/src/chat/chat-types";

  export let onSave: (ruleType: RuleType, description?: string) => Promise<void>;
  export let rule: Rule;

  $: path = rule.path;
  $: ruleType = rule.type;

  // Dropdown options
  const alwaysOption = {
    label: "Always",
    value: RuleType.ALWAYS_ATTACHED,
    description: "These Rules will be included in every message you send to the agent.",
  };
  const manualOption = {
    label: "Manual",
    value: RuleType.MANUAL,
    description:
      "These Rules will be included when manually tagged in your message. You can tag Rules by @-mentioning them.",
  };
  const agentRequestedOption = {
    label: "Auto",
    value: RuleType.AGENT_REQUESTED,
    description:
      "These Rules will be included when the Agent decides to fetch them based on this file's description.",
  };

  const dropdownOptions = [alwaysOption, manualOption, agentRequestedOption] as const;

  $: selectedOption = dropdownOptions.find((option) => option.value === ruleType)!;

  type DropdownOption = (typeof dropdownOptions)[number];
  // Track text selection state
  let focusedIndex: Readable<number | undefined> | undefined = undefined;
  let requestClose: () => void = () => {};

  // Handle dropdown selection
  async function handleDropdownSelect(option: DropdownOption) {
    // Close the dropdown immediately when user clicks
    requestClose();

    // Then save in the background
    try {
      await onSave(
        option.value,
        option.value === RuleType.AGENT_REQUESTED && !rule.description
          ? "Example description"
          : rule.description,
      );
    } catch (error) {
      console.error("RulesModeSelector: Error in onSave:", error);
    }
  }
  // Helper function to check if a file is a guidelines file
  function isGuidelinesFile(filePath: string): boolean {
    return filePath === AUGMENT_GUIDELINES_FILE;
  }
</script>

{#if isGuidelinesFile(path)}
  <!-- Guidelines files are always "Always" and cannot be changed -->
  <TextTooltipAugment content="Workspace guidelines are always applied">
    <ButtonAugment color="accent" size={1} disabled>Always</ButtonAugment>
  </TextTooltipAugment>
{:else}
  <DropdownMenuAugment.Root bind:requestClose bind:focusedIndex>
    <DropdownMenuAugment.Trigger>
      <div class="c-dropdown-label">
        <ButtonAugment color="neutral" size={1} variant="soft"
          >{selectedOption.label}
          <ChevronDown slot="iconRight" />
        </ButtonAugment>
      </div>
    </DropdownMenuAugment.Trigger>
    <DropdownMenuAugment.Content side="bottom" align="start">
      {#each dropdownOptions as option}
        <DropdownMenuAugment.Item
          onSelect={() => handleDropdownSelect(option)}
          highlight={selectedOption.label === option.label}
        >
          {option.label}
        </DropdownMenuAugment.Item>
      {/each}
      {#if $focusedIndex !== undefined || selectedOption}
        <DropdownMenuAugment.Separator />
        <DropdownMenuAugment.Label>
          {$focusedIndex !== undefined
            ? dropdownOptions[$focusedIndex].description
            : selectedOption.description}
        </DropdownMenuAugment.Label>
      {/if}
    </DropdownMenuAugment.Content>
  </DropdownMenuAugment.Root>
{/if}

<style>
  .c-dropdown-label :global(.c-text) {
    white-space: nowrap;
  }
</style>
