<script lang="ts">
  import type { ICodeChunkLeaf } from "../types/chunk";
  import type { DiffViewModel } from "../models/diff-view-model";
  import KeyboardShortcutHint from "$common-webviews/src/common/components/keybindings/KeyboardShortcutHint.svelte";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";

  // Style props
  export let align: "left" | "right" = "right";
  export let isFocused: boolean;
  export let heightInPx: number = 1;
  export let disableApply = false;

  // Callbacks
  export let onAccept: () => void;
  export let onReject: () => void;

  // Data props
  export let diffViewModel: DiffViewModel;
  export let leaf: ICodeChunkLeaf | undefined;

  const keybindingsStore = diffViewModel.keybindings;

  let top: number = 0;
  const onDomNodeTop = (newTop: number) => {
    top = newTop;
  };

  let isTransparent = false;
  let hoverTimeout: ReturnType<typeof setTimeout> | undefined = undefined;

  // This function starts a timer to make the action panel transparent
  // It's triggered when the user hovers over the panel area (but not the buttons)
  function startHoverTimer() {
    clearHoverTimer();
    hoverTimeout = setTimeout(() => {
      isTransparent = true;
    }, 400);
  }

  // This function cancels the transparency timer and makes the panel opaque
  // It's used when the user moves away from the panel or interacts with the buttons
  function clearHoverTimer() {
    if (hoverTimeout) {
      clearTimeout(hoverTimeout);
      hoverTimeout = undefined;
    }
    isTransparent = false;
  }

  // This function handles all mouse events on the action panel
  // It manages the panel's transparency based on user interaction
  function handleMouseEvent(event: MouseEvent) {
    const target = event.target as HTMLElement;
    if (!target.closest(".c-button-container")) {
      // If the mouse is over the panel but not the buttons:
      // - On enter or move, start the timer to make the panel transparent
      // - On leave, clear the timer and make the panel opaque
      if (event.type === "mouseenter" || event.type === "mousemove") {
        startHoverTimer();
      } else if (event.type === "mouseleave") {
        clearHoverTimer();
      }
    } else {
      // If the mouse is over the buttons, keep the panel opaque
      clearHoverTimer();
    }
  }
</script>

<!-- This reserves space in Monaco for the action panel -->
<div
  use:diffViewModel.renderActionsViewZone={{
    chunk: leaf,
    heightInPx,
    onDomNodeTop,
  }}
  class:c-chunk-diff-border--focused={!!leaf && isFocused}
></div>

<!-- This is the actual action panel, which is positioned on top of the reserved space -->
<!-- Acts as a hitbox for dimming the action panel as well -->
<!-- svelte-ignore a11y-no-static-element-interactions -->
<div
  class="c-chunk-action-panel-anchor"
  class:c-chunk-action-panel-anchor--left={align === "left"}
  class:c-chunk-action-panel-anchor--right={align === "right"}
  class:c-chunk-action-panel-anchor--focused={isFocused}
  style="top: {top}px;"
  on:mouseenter={handleMouseEvent}
  on:mousemove={handleMouseEvent}
  on:mouseleave={handleMouseEvent}
>
  <div
    class="c-button-container"
    class:c-button-container--focused={isFocused}
    class:c-button-container--transparent={isTransparent}
  >
    {#if !disableApply}
      <ButtonAugment size={1} variant="ghost" color="success" on:click={onAccept}>
        <KeyboardShortcutHint keybinding={$keybindingsStore.acceptFocusedChunk} />
        Accept
      </ButtonAugment>
    {/if}
    <ButtonAugment size={1} variant="ghost" color="error" on:click={onReject}>
      <KeyboardShortcutHint keybinding={$keybindingsStore.rejectFocusedChunk} />
      Reject
    </ButtonAugment>
  </div>
</div>

<style>
  .c-chunk-action-panel-anchor {
    display: none;
    position: absolute;
    cursor: text;
  }

  .c-chunk-action-panel-anchor--left {
    left: var(--ds-spacing-2);
  }

  .c-chunk-action-panel-anchor--right {
    right: var(--ds-spacing-2);
  }

  .c-chunk-diff-border--focused {
    background-color: var(--augment-focus-border-color);
  }

  .c-chunk-action-panel-anchor--focused {
    display: block;
  }

  .c-button-container {
    transform: translateY(-50%);

    width: fit-content;
    gap: var(--ds-spacing-1);
    position: relative;
    background-color: var(--augment-window-background);
    display: flex;
    flex-direction: row;

    /* Padding to allow for button outline to be visible */
    padding: 2px;

    /* Rounded corners */
    border: var(--augment-border);
    border-radius: var(--ds-radius-1);
    box-shadow: var(--ds-shadow-no-border-4);
    overflow: hidden;
    transition: opacity 0.3s ease;
  }

  .c-button-container--focused {
    border-color: var(--augment-focus-border-color);
  }

  .c-button-container--transparent {
    opacity: 0.3;
  }

  .c-button-container :global(.c-action-btn) {
    gap: var(--ds-spacing-1);
    font-weight: 500;
  }
</style>
