<script lang="ts">
  import type * as monaco from "monaco-editor";
  import { onD<PERSON>roy, onMount, tick } from "svelte";
  import { get } from "svelte/store";

  import { DiffViewMode } from "../models/types";
  import { type DiffViewModel } from "../models/diff-view-model";
  import KeyboardShortcutHint from "$common-webviews/src/common/components/keybindings/KeyboardShortcutHint.svelte";
  import { host } from "$common-webviews/src/common/hosts/host";
  import { MessageBroker } from "$common-webviews/src/common/utils/message-broker";
  import { ChatModel } from "../../chat/models/chat-model";
  import { SpecialContextInputModel } from "../../chat/models/context-model";
  import type { IConversation, IChatFlags } from "../../chat/models/types";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import Send from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/paper-plane-top.svg?component";
  import RichTextEditorAugment from "$common-webviews/src/design-system/components/RichTextEditorAugment";
  import type RichTextEditorRoot from "$common-webviews/src/design-system/components/RichTextEditorAugment/Root.svelte";
  import AtMentions from "../../chat/components/ChatInput/AtMentions.svelte";
  import type { MentionsUpdatedData } from "$common-webviews/src/design-system/components/RichTextEditorAugment/plugins/Mention/types";
  import type { IChatMentionable } from "../../chat/types/mention-option";
  import Placeholder from "$common-webviews/src/design-system/components/RichTextEditorAugment/plugins/Placeholder/Placeholder.svelte";
  import Keybindings from "$common-webviews/src/design-system/components/RichTextEditorAugment/plugins/Keybindings/Keybindings.svelte";
  import type { ContentData } from "$common-webviews/src/design-system/components/RichTextEditorAugment/types";
  import { setChatModel } from "../../chat/chat-context";
  import { MonacoContext } from "$common-webviews/src/design-system/components/MonacoProvider";

  export let diffViewModel: DiffViewModel;

  export let initialConversation: IConversation | undefined = undefined;
  export let initialFlags: Partial<IChatFlags> | undefined = undefined;

  const monacoContext = MonacoContext.getContext();
  const monacoStore = monacoContext.monaco;

  const editAreaDecorationType = {
    isWholeLine: true,
    marginClassName: "instruction-edit-area-margin",
  };

  const msgBroker = new MessageBroker(host);
  let contextModel = new SpecialContextInputModel();
  msgBroker.registerConsumer(contextModel);
  let chatModel = new ChatModel(msgBroker, host, contextModel, {
    initialConversation,
    initialFlags,
  });
  const conversationModel = chatModel.currentConversationModel;
  msgBroker.registerConsumer(chatModel);

  // Make the chat model available to subcomponents
  setChatModel(chatModel);

  let topDrawerAction: { update: (props: any) => void; destroy: () => void } | undefined;
  let inputElement: HTMLElement;
  let inputText = "";
  let decorationCollection: monaco.editor.IEditorDecorationsCollection | undefined;
  const modeStore = diffViewModel.mode;
  const selectionLinesStore = diffViewModel.selectionLines;

  $: placeholderText = `${$modeStore === DiffViewMode.instruction ? "Instruct" : "Edit with"} Augment... @ to focus on files or docs`;

  function updateEditAreaDecoration() {
    const editor = diffViewModel.getModifiedEditor();
    const monacoApi = get(monacoStore);
    if (!editor || !monacoApi) {
      return;
    }
    // Clear existing decorations
    decorationCollection?.clear();

    // If selectionLinesStore is not set, we just clear the decorations and return
    if (!$selectionLinesStore) {
      return;
    }

    const startLine = $selectionLinesStore.start;
    const endLine = $selectionLinesStore.end;
    const range = new monacoApi.Range(startLine + 1, 1, endLine + 1, 1);
    const decoration = {
      range: range,
      options: editAreaDecorationType,
    };
    if (!decorationCollection) {
      decorationCollection = editor.createDecorationsCollection();
    }
    decorationCollection.set([decoration]);
  }

  function handleSubmit(): boolean {
    if (!inputText?.trim()) {
      return false;
    }

    diffViewModel.handleInstructionSubmit(inputText);
    return true;
  }
  onMount(async () => {
    await tick();
    forceEditorFocus();
    topPanelTop = diffViewModel.editorOffset;
  });

  onDestroy(() => {
    topDrawerAction?.destroy();
    decorationCollection?.clear();
  });

  let topPanelTop: number = 0; // set onMount
  let topPanelHeight: number = 57; // content padding (8px) + button container (26px) + drawer margin (6px) + 1 line (17px)

  $: isLoadingStore = $diffViewModel.isLoading;
  $: if (inputElement) {
    if ($selectionLinesStore == null) {
      topPanelHeight = 0;
    } else {
      // inputHeight min and max defined in CSS
      const inputHeight = inputElement.scrollHeight;
      topPanelHeight = Math.min(8 + 26 + 6 + inputHeight, 108);
    }
    updateTopDrawer();
  }

  function handleTopDrawerAction(node: HTMLElement) {
    if (node) {
      const line = $selectionLinesStore ? $selectionLinesStore.start : 1;
      topDrawerAction = diffViewModel.renderInstructionsDrawerViewZone(node, {
        line,
        heightInPx: topPanelHeight,
        onDomNodeTop: (top: number) => {
          topPanelTop = diffViewModel.editorOffset + top;
        },
        autoFocus: true,
      });

      updateEditAreaDecoration();
    }
  }

  function updateTopDrawer() {
    topDrawerAction?.update({
      heightInPx: topPanelHeight,
    });
    updateEditAreaDecoration();
  }

  let richTextEditorRoot: RichTextEditorRoot | undefined = undefined;
  let atMentionsComponent: AtMentions | undefined = undefined;
  const requestEditorFocus = () => richTextEditorRoot?.requestFocus();
  const forceEditorFocus = () => richTextEditorRoot?.forceFocus();
  const onMentionItemsUpdated = (data: MentionsUpdatedData<IChatMentionable>) => {
    conversationModel.saveDraftMentions(data.current);
  };
  function onContentChanged(data: ContentData) {
    inputText = data.rawText;
  }
</script>

{#if $selectionLinesStore}
  <div use:handleTopDrawerAction></div>
  <div class="instruction-drawer-panel" style="top: {topPanelTop}px; height: {topPanelHeight}px;">
    <div
      class="instruction-drawer-panel__contents"
      on:click={forceEditorFocus}
      on:keydown={(e) => {
        if (e.key === "Enter") {
          forceEditorFocus();
          e.stopPropagation();
          e.preventDefault();
        }
      }}
      tabindex="0"
      role="button"
    >
      <div class="l-input-area__input" bind:this={inputElement}>
        <RichTextEditorAugment.Root focusOnInit bind:this={richTextEditorRoot}>
          <Keybindings
            shortcuts={{
              // eslint-disable-next-line @typescript-eslint/naming-convention
              Enter: () => handleSubmit(),
            }}
          />
          <AtMentions
            bind:this={atMentionsComponent}
            {requestEditorFocus}
            {onMentionItemsUpdated}
          />
          <RichTextEditorAugment.Content content={inputText} {onContentChanged} />
          <Placeholder placeholder={placeholderText} />
        </RichTextEditorAugment.Root>
      </div>
      <div class="c-instruction-drawer-panel__btn-container">
        <ButtonAugment
          id="close"
          size={1}
          variant="soft"
          color="neutral"
          title="Close"
          on:click={diffViewModel.disposeDiffViewPanel}
        >
          Close
          <KeyboardShortcutHint keybinding="esc" />
        </ButtonAugment>
        <ButtonAugment
          id="send"
          size={1}
          variant="solid"
          color="accent"
          title={$modeStore === DiffViewMode.instruction ? "Instruct Augment" : "Edit with Augment"}
          disabled={!inputText.trim() || $isLoadingStore}
          on:click={handleSubmit}
        >
          {$modeStore === DiffViewMode.instruction ? "Instruct" : "Edit"}
          <Send slot="iconRight" />
        </ButtonAugment>
      </div>
    </div>
  </div>
{/if}

<style>
  .instruction-drawer-panel {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    display: flex;
    justify-content: flex-start;
    padding-left: 46px;
    box-sizing: border-box;
    z-index: var(--z-diff-view-instructions-panel);

    /* Allow scrolling on the drawer */
    pointer-events: none;
  }

  .instruction-drawer-panel__contents {
    width: 100%;
    max-width: 350px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    margin-top: 3px;
    margin-bottom: 3px;
    padding: var(--ds-spacing-1);
    background-color: var(--augment-window-background);

    border-radius: var(--ds-spacing-1);
    box-shadow: var(--ds-shadow-6);

    overflow: hidden;
    pointer-events: all;
  }

  .l-input-area__input {
    width: 100%;
    height: 100%;
    min-height: 48px;
    max-height: 48px;
    overflow-y: auto;
  }

  .l-input-area__input :global(.ProseMirror) :global(.c-context-chip) {
    margin: 0;
  }

  .c-instruction-drawer-panel__btn-container {
    width: 100%;
    height: 22px;
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    gap: var(--ds-spacing-1);
    margin-top: 3px;
  }

  .instruction-drawer-panel__contents :global(.c-btn) {
    gap: var(--ds-spacing-1);
  }

  .instruction-drawer-panel__contents :global(.c-btn#send) {
    padding: 5px 6px;
  }

  :global(.instruction-edit-area-margin) {
    border-left: 3px solid var(--ds-color-accent-9);
  }
</style>
