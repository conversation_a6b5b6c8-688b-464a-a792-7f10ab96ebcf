import type {
  DiffViewDiffStreamChunk,
  DiffViewDiffStreamChunkData,
} from "$vscode/src/webview-providers/webview-messages";
import type * as MonacoEditor from "monaco-editor";

type MonacoEditOp = MonacoEditor.editor.IIdentifiedSingleEditOperation;
export interface DeltaDiff {
  resetOriginal: MonacoEditOp[];
  original: MonacoEditOp[];
  modified: MonacoEditOp[];
}

export class StreamContext {
  private _originalModel: MonacoEditor.editor.ITextModel;
  private _modifiedModel: MonacoEditor.editor.ITextModel;
  private _fullEdits: MonacoEditOp[] = [];

  private _currEdit: MonacoEditOp | undefined;
  private _currOriginalEdit: MonacoEditOp | undefined;

  constructor(
    public id: string,
    public originalCode: string,
    private _monaco: typeof MonacoEditor,
  ) {
    this._originalModel = this._monaco.editor.createModel(originalCode);
    this._modifiedModel = this._monaco.editor.createModel(originalCode);
  }

  public swapBaseModel = (newCode: string) => {
    this._originalModel.setValue(newCode);
    this._modifiedModel.setValue(newCode);
    this._fullEdits.forEach((e: MonacoEditOp) => {
      this._modifiedModel.applyEdits([e]);
    });
    if (this._currEdit) {
      this._modifiedModel.applyEdits([this._currEdit]);
    }
    if (this._currOriginalEdit) {
      this._originalModel.applyEdits([this._currOriginalEdit]);
    }
  };

  public finish = (): DeltaDiff => {
    return this._completeCurrEdit();
  };

  // Returns the line number to scroll to
  public onReceiveChunk = (msg: DiffViewDiffStreamChunk): DeltaDiff | undefined => {
    if (msg.data.newChunkStart) {
      return this._startNewEdit(msg.data.newChunkStart);
    } else if (msg.data.chunkContinue && this._currEdit) {
      return this._continueEdit(msg.data.chunkContinue);
    } else if (msg.data.chunkEnd && this._currEdit) {
      return this._completeCurrEdit(msg.data.chunkEnd);
    }
  };

  get hasReceivedFirstChunk(): boolean {
    return this._currEdit !== undefined || this._fullEdits.length > 0;
  }

  get originalValue(): string {
    return this._originalModel.getValue();
  }

  get modifiedValue(): string {
    return this._modifiedModel.getValue();
  }

  get currEdit(): MonacoEditOp | undefined {
    return this._currEdit;
  }

  private _completeCurrEdit = (
    chunkEnd?: NonNullable<DiffViewDiffStreamChunkData["chunkEnd"]>,
  ): DeltaDiff => {
    // Update the delta diff
    const deltaDiff: DeltaDiff = {
      resetOriginal: [],
      original: [],
      modified: [],
    };
    if (!chunkEnd) {
      return deltaDiff;
    }

    // Delete the range *after* the current edit, since we inserted
    // before deleting

    // Update curr edit
    if (this._currEdit) {
      this._currEdit.range = new this._monaco.Range(
        chunkEnd.stagedStartLine,
        0,
        chunkEnd.stagedEndLine,
        0,
      );

      // Calculate the range of the original text that we now need to delete
      const offset = this._nextModifiedInsertPosition();
      const numLinesToDelete = chunkEnd.stagedEndLine - chunkEnd.stagedStartLine;
      const deleteOp = {
        range: new this._monaco.Range(
          offset.lineNumber,
          0,
          offset.lineNumber + numLinesToDelete,
          0,
        ),
        text: "",
      };
      deltaDiff.modified.push(deleteOp);
      this._modifiedModel.applyEdits([deleteOp]);

      // Reset the state
      this._fullEdits.push(this._currEdit);
      this._currEdit = undefined;
    }

    return deltaDiff;
  };

  /**
   * Starts a new edit chunk. Does so by:
   * - Deleting the specified range from both models
   * - Setting the undo edits for the original model
   * - Setting the current edit
   * - Setting the original edit
   */
  private _startNewEdit = (
    newChunkStart: NonNullable<DiffViewDiffStreamChunkData["newChunkStart"]>,
  ) => {
    const deltaDiff: DeltaDiff = {
      resetOriginal: [],
      original: [],
      modified: [],
    };

    // Create the current original edit. Should never change.
    this._currOriginalEdit = undefined;

    // Add the current edit
    this._currEdit = {
      range: new this._monaco.Range(
        newChunkStart.stagedStartLine,
        0,
        newChunkStart.stagedStartLine,
        0,
      ),
      text: "",
    };

    deltaDiff.modified.push(this._currEdit);
    this._modifiedModel.applyEdits([this._currEdit]);
    return deltaDiff;
  };

  private _continueEdit = (
    chunkContinue: NonNullable<DiffViewDiffStreamChunkData["chunkContinue"]>,
  ) => {
    if (!this._currEdit) {
      throw new Error("No current edit");
    }

    const insertPosition = this._nextModifiedInsertPosition();

    // The diff between the last state and the current state
    const deltaDiff = {
      ...this._currEdit,
      text: chunkContinue.newText,
      range: new this._monaco.Range(
        insertPosition.lineNumber,
        insertPosition.column,
        insertPosition.lineNumber,
        insertPosition.column,
      ),
    };
    this._modifiedModel.applyEdits([deltaDiff]);
    this._currEdit.text += chunkContinue.newText;
    return {
      resetOriginal: [],
      original: [],
      modified: chunkContinue.newText.length > 0 ? [deltaDiff] : [],
    };
  };

  private _nextModifiedInsertPosition = (): MonacoEditor.Position => {
    if (!this._currEdit) {
      throw new Error("No current edit");
    }
    const startOffset = this._modifiedModel.getOffsetAt({
      lineNumber: this._currEdit.range.startLineNumber,
      column: this._currEdit.range.startColumn,
    });

    const offset = startOffset + (this._currEdit.text?.length ?? 0);
    return this._modifiedModel.getPositionAt(offset);
  };
}
