import type { IOverlayWidgetProps, IViewZoneProps } from "./types";
import type * as MonacoEditor from "monaco-editor";

/**
 * A svelte action that renders a view zone in the diff view editor.
 *
 * @param element: element to bind to the view zone
 * @param props: props to pass to the view zone
 * @returns: a svelte action
 */
export function renderViewZone(element: HTMLElement, props: IViewZoneProps) {
  let currProps: IViewZoneProps = props;
  let zone: MonacoEditor.editor.IViewZone | undefined = undefined;
  let zoneId: string | undefined = undefined;

  const getCurrEditor = () => {
    return currProps.editor.getModifiedEditor();
  };

  // Update the view zone based on triggered changes coming from Svelte
  const updateViewZone = () => {
    const { afterLineNumber } = currProps;
    const currEditor = getCurrEditor();
    if (afterLineNumber === undefined) {
      // Remove zone, if any
      currEditor.changeViewZones((accessor: MonacoEditor.editor.IViewZoneChangeAccessor) => {
        if (!zone || !currEditor || !zoneId) {
          return;
        }
        accessor.removeZone(zoneId);
      });
      return;
    }

    // Create new zone
    const newZone = {
      ...currProps,
      afterLineNumber,
      domNode: element,
      suppressMouseDown: true,
    };

    // Remove old zone and add new zone
    currEditor?.changeViewZones((accessor: MonacoEditor.editor.IViewZoneChangeAccessor) => {
      if (zone && zoneId) {
        accessor.removeZone(zoneId);
      }
      zoneId = accessor.addZone(newZone);
      zone = newZone;
    });
  };
  updateViewZone();

  return {
    update: (props: IViewZoneProps) => {
      currProps = props;
      updateViewZone();
    },
    destroy: () => {
      const currEditor = getCurrEditor();
      currEditor.changeViewZones((accessor: MonacoEditor.editor.IViewZoneChangeAccessor) => {
        if (!zone || !currEditor || !zoneId) {
          return;
        }
        try {
          accessor.removeZone(zoneId);
        } catch (err) {
          if (err instanceof Error) {
            if (err.message.includes("Cannot read properties of null (reading 'removeChild')")) {
              // This error happens when the editor has unmounted our zone already -- we can ignore it
              return;
            }
          } else {
            console.warn(`Failed to remove view zone: ${err}`);
          }
        }
      });
    },
  };
}

/**
 * A svelte action that renders an overlay widget in the diff view editor.
 *
 * @param element: element to bind to the overlay widget
 * @param props: props to pass to the overlay widget
 */
export function renderOverlayWidget(
  element: HTMLElement,
  props: IOverlayWidgetProps,
  deps: {
    monaco: typeof MonacoEditor;
  },
) {
  let currProps: IOverlayWidgetProps = props;
  let widget: MonacoEditor.editor.IOverlayWidget | undefined = undefined;

  const getCurrEditor = () => {
    return currProps.editor.getModifiedEditor();
  };

  // An updater function that gets called when we need to refresh Monaco
  const updateOverlayWidget = () => {
    const currEditor = getCurrEditor();
    if (!currEditor) {
      return;
    }

    // Create new widget
    const newWidget: MonacoEditor.editor.IOverlayWidget = {
      getDomNode: () => element,
      getId: () => currProps.id,
      getPosition: () => {
        return {
          preference: deps.monaco.editor.OverlayWidgetPositionPreference.TOP_CENTER,
        };
      },
    };

    // Remove old widget and add new widget
    if (widget) {
      currEditor.removeOverlayWidget(widget);
    }
    currEditor.addOverlayWidget(newWidget);
    widget = newWidget;
  };
  updateOverlayWidget();

  // Construct the action object to return
  return {
    update: (props: IOverlayWidgetProps) => {
      currProps = props;
      updateOverlayWidget();
    },
    destroy: () => {
      const currEditor = getCurrEditor();
      if (!currEditor || !widget) {
        return;
      }
      currEditor.removeOverlayWidget(widget);
    },
  };
}
