import type { ICodeChunkLeaf } from "../types/chunk";
import type { editor as monacoEditor } from "monaco-editor";

export enum DiffViewMode {
  edit = "edit",
  instruction = "instruction",
}

export enum ViewZoneOrdinals {
  instructionDrawer = 0,
  chunkActionPanel = 1,
}

export interface IDiffViewModel {
  currFocusedChunkIdx: number | undefined;
}

export interface IOverlayWidgetProps {
  editor: monacoEditor.IStandaloneDiffEditor;
  id: string;
}

export interface IViewZoneProps
  extends Omit<Omit<monacoEditor.IViewZone, "domNode">, "afterLineNumber"> {
  editor: monacoEditor.IStandaloneDiffEditor;
  afterLineNumber?: number;
}

export interface IActionsViewZoneProps {
  chunk?: ICodeChunkLeaf | undefined;
  heightInLines?: number;
  heightInPx?: number;
  onDomNodeTop: (top: number) => void;
}

export interface IInstructionsDrawerViewZoneProps {
  line: number;
  heightInPx?: number;
  heightInLines?: number;
  autoFocus?: boolean;
  onDomNodeTop: (top: number) => void;
  onComputedHeight?: (height: number) => void;
}
