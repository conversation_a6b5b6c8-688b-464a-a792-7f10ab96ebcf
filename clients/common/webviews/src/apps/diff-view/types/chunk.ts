import type { FileDetails } from "$vscode/src/webview-providers/webview-messages";
import { type IQualifiedPathName } from "@augment-internal/sidecar-libs/src/workspace/workspace-types";

export enum CodeChunkActionType {
  accept = "accept",
  reject = "reject",
}

interface ICodeChunkBase {
  id: string; // UUID
  name: string; // Short display name
  title: string; // Short description
  description: string; // Long description
  generationSource: string; // User-visible name for where the generation came from
  supportedActions: CodeChunkActionType[];
}

export interface ICodeChunkLeaf extends ICodeChunkBase {
  unitOfCodeWork: FileDetails; // The actual concrete diff to be made
}

interface ICodeChunkBranch extends ICodeChunkBase {
  childIds: string[]; // Ordered list of UUIDs
}

// Union type for ICodeChunk, has to either be a leaf or a branch
export type ICodeChunk = ICodeChunkBranch | ICodeChunkLeaf;

export type IHydratedCodeChunk = ICodeChunk & {
  children: IHydratedCodeChunk[];
};

/**
 * Checks if a chunk is a branch (has children)
 * Should NOT be a unit of work
 *
 * @param chunk: ICodeChunk
 * @returns: whether the chunk is a branch
 */
export function hasChildren<T extends IHydratedCodeChunk>(chunk: T): chunk is ICodeChunkBranch & T {
  const hasChildren = chunk.children.length > 0 && "childIds" in chunk;
  return hasChildren;
}
export const isBranch = hasChildren;

/**
 * Checks if a chunk is a leaf (has no children)
 * Should have a unit of code work
 *
 * @param chunk
 * @returns
 */
export function isLeaf<T extends IHydratedCodeChunk>(chunk: T): chunk is ICodeChunkLeaf & T {
  return "unitOfCodeWork" in chunk && !hasChildren<T>(chunk);
}

/**
 * A recursive function that gets all leaves of a chunk
 *
 * @param chunk
 * @returns
 */
export function getAllLeaves(chunk: IHydratedCodeChunk): ICodeChunkLeaf[] {
  if (isLeaf(chunk)) {
    return [chunk];
  } else {
    return chunk.children.flatMap(getAllLeaves);
  }
}

export function fromFileDiff(
  pathName: IQualifiedPathName,
  before: string,
  after: string,
): ICodeChunkLeaf {
  return {
    id: "",
    name: "",
    title: "",
    description: "",
    generationSource: "",
    supportedActions: [],
    unitOfCodeWork: {
      repoRoot: pathName.rootPath,
      pathName: pathName.relPath,
      originalCode: before,
      modifiedCode: after,
    },
  };
}
