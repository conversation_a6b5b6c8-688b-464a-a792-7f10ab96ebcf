<script lang="ts">
  export let selected: string | null = null;
  export let question: string | null = null;

  function select(option: string) {
    selected = option;
  }
</script>

<div class="container">
  {#if question}
    <div class="header">{question}</div>
  {/if}
  <div class="buttons">
    <button
      type="button"
      class="button large"
      class:highlighted={selected === "A3"}
      on:click={() => select("A3")}>A</button
    >
    <button
      type="button"
      class="button medium"
      class:highlighted={selected === "A2"}
      on:click={() => select("A2")}>A</button
    >
    <button
      type="button"
      class="button small"
      class:highlighted={selected === "A1"}
      on:click={() => select("A1")}>A</button
    >
    <button
      type="button"
      class="button equal"
      class:highlighted={selected === "="}
      on:click={() => select("=")}>=</button
    >
    <button
      type="button"
      class="button small"
      class:highlighted={selected === "B1"}
      on:click={() => select("B1")}>B</button
    >
    <button
      type="button"
      class="button medium"
      class:highlighted={selected === "B2"}
      on:click={() => select("B2")}>B</button
    >
    <button
      type="button"
      class="button large"
      class:highlighted={selected === "B3"}
      on:click={() => select("B3")}>B</button
    >
  </div>
</div>

<style>
  .container {
    display: inline-flex;
    flex-direction: column;
    gap: 10px;
  }
  .header {
    font-size: 14px;
  }
  .buttons {
    display: flex;
    gap: 5px;
    align-items: center;
  }
  .button {
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s;
    font-size: 14px;
    color: white;
    background-color: #4a47f5;
  }
  .button:hover {
    opacity: 0.9;
  }
  .button.highlighted {
    background-color: white;
    color: #4a47f5;
    border: 2px solid #4a47f5;
  }
  .large {
    width: 50px;
    height: 50px;
  }
  .medium {
    width: 40px;
    height: 40px;
  }
  .small {
    width: 30px;
    height: 30px;
  }
  .equal {
    width: 30px;
    height: 30px;
  }
</style>
