<script lang="ts">
  export let label: string = "Submit";
  export let onClick: () => void;
</script>

<button class="button" on:click={onClick}>
  {label}
</button>

<style>
  .button {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 10px;
    background-color: #4a47f5;
    color: white;
    border: 2px solid white;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s;
    font-size: 16px;
    outline: none;
  }
  .button:hover {
    background-color: white;
    color: #4a47f5;
    border-color: #4a47f5;
  }
  .button:focus {
    box-shadow: 0 0 0 2px rgba(74, 71, 245, 0.5);
  }
</style>
