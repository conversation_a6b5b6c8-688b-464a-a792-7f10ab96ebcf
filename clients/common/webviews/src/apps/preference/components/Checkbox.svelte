<script lang="ts">
  export let isChecked: boolean = false;
  export let question: string | null = null;
</script>

<div class="container">
  {#if question}
    <div>{question}</div>
  {/if}
  <label class="custom-checkbox">
    <input type="checkbox" bind:checked={isChecked} />
    <span></span>
  </label>
</div>

<style>
  .container {
    display: inline-flex;
    align-items: center;
    background-color: #4a47f5;
    padding: 10px 15px;
    border-radius: 8px;
    color: white;
  }
  .custom-checkbox {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 10px;
  }
  input[type="checkbox"] {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
  }
  span {
    position: relative;
    height: 20px;
    width: 20px;
    background-color: white;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s;
  }
  span::after {
    content: "";
    position: absolute;
    display: none;
    left: 7px;
    top: 3px;
    width: 5px;
    height: 10px;
    border: solid #4a47f5;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
  }
  input[type="checkbox"]:checked ~ span::after {
    display: block;
  }
  input[type="checkbox"]:focus ~ span {
    box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.5);
  }
</style>
