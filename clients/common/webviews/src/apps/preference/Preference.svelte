<script lang="ts">
  import type {
    PreferenceInput,
    PreferenceResult,
  } from "$vscode/src/webview-panels/preference-panel-types";
  import PreferenceChat from "$common-webviews/src/apps/preference/PreferenceChat.svelte";
  import {
    WebViewMessageType,
    type WebViewMessage,
  } from "$vscode/src/webview-providers/webview-messages";
  import { host } from "$common-webviews/src/common/hosts/host";
  import MonacoProvider from "$common-webviews/src/design-system/components/MonacoProvider";

  let inputData: PreferenceInput;

  function handleMessage(e: MessageEvent<WebViewMessage>): void {
    const msg = e.data;
    switch (msg.type) {
      case WebViewMessageType.preferenceInit:
        inputData = msg.data;
        break;
    }
  }

  function handleResult(event: CustomEvent<PreferenceResult>): void {
    const result = event.detail as PreferenceResult;
    host.postMessage({
      type: WebViewMessageType.preferenceResultMessage,
      data: result,
    });
  }

  function handleNotify(event: CustomEvent<string>) {
    host.postMessage({
      type: WebViewMessageType.preferenceNotify,
      data: event.detail,
    });
  }

  host.postMessage({
    type: WebViewMessageType.preferencePanelLoaded,
  });
</script>

<svelte:window on:message={handleMessage} />

<MonacoProvider.Root>
  <main>
    {#if inputData}
      {#if inputData.type === "Chat"}
        <PreferenceChat {inputData} on:result={handleResult} on:notify={handleNotify} />
      {/if}
    {/if}
  </main>
</MonacoProvider.Root>
