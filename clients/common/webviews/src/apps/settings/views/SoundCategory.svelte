<script lang="ts">
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import ToggleAugment from "$common-webviews/src/design-system/components/ToggleAugment.svelte";
  import { TooltipTriggerOn } from "$common-webviews/src/design-system/components/types";
  import { getContext } from "svelte";
  import SettingsCard from "../components/SettingsCard.svelte";
  import { SoundModel } from "../models/sound-model";
  import SuccessfulButton from "../../chat/components/buttons/SuccessfulButton.svelte";
  import type { ButtonState } from "../../chat/components/buttons/types";

  const soundModel = getContext<SoundModel>(SoundModel.key);
  $: currentSettings = soundModel.getCurrentSettings;
  $: enabled = $currentSettings.enabled;

  async function handlePlaySound(): Promise<ButtonState> {
    await soundModel.playAgentComplete();
    return "success";
  }
</script>

<TextAugment size={1} weight="regular" color="secondary">
  <div class="section-heading-text">Sound Settings</div>
</TextAugment>
<div class="c-sound-settings">
  <SettingsCard>
    <div class="c-sound-setting__info" slot="header-left">
      <div>
        <TextAugment size={2} weight="medium">Enable Sound Effects</TextAugment>
      </div>
      <div>
        <TextAugment size={1} weight="medium"
          >Play a sound when an agent completes a task</TextAugment
        >
      </div>
    </div>
    <div slot="header-right">
      <ToggleAugment
        size={1}
        checked={enabled}
        on:change={() => soundModel.updateEnabled(!enabled)}
      />
    </div>
  </SettingsCard>
  {#if enabled}
    <SettingsCard>
      <div class="c-sound-setting__info" slot="header-left">
        <div>
          <TextAugment size={2} weight="medium">Test Sound</TextAugment>
        </div>
        <div>
          <TextAugment size={1} weight="medium"
            >Play a sample of the agent completion sound</TextAugment
          >
        </div>
      </div>
      <div slot="header-right">
        <TextTooltipAugment
          content={!enabled ? "Enable sound effects to test" : ""}
          triggerOn={[TooltipTriggerOn.Hover]}
        >
          <SuccessfulButton
            size={1}
            defaultColor="neutral"
            {enabled}
            stickyColor={false}
            disabled={!enabled}
            onClick={handlePlaySound}
            tooltip={{
              neutral: "Play a sample of the agent completion sound",
              success: "Played!",
            }}
          >
            Play
          </SuccessfulButton>
        </TextTooltipAugment>
      </div>
    </SettingsCard>
  {/if}
</div>

<style>
  .c-sound-settings {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-4);
  }

  .c-sound-settings :global(.settings-card-content) {
    padding: var(--ds-spacing-1);
  }

  .c-sound-setting__info {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-1);
  }
</style>
