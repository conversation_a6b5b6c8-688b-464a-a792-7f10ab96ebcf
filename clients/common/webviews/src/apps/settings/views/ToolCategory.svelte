<script lang="ts">
  import type { ConfigBlock } from "../models/tool-config-model";
  import { ToolConfigModel } from "../models/tool-config-model";
  import ConfigCompact from "../components/ConfigCompact.svelte";
  import SettingsCategory from "./SettingsCategory.svelte";
  import type { OnToolApprovalConfigChange } from "../components/types";

  import RemoteMCP from "../components/RemoteMCP.svelte";
  import { getContext } from "svelte";
  import { getMCPServerModelContext } from "../models/mcp-server-model-context";
  import { filterPartnerMCPTools } from "../components/partner-mcp-utils";

  export let title: string;
  export let tools: ConfigBlock[] = [];
  export let onAuthenticate: (url: string) => void;
  export let onRevokeAccess: (config: ConfigBlock) => void;
  export let onToolApprovalConfigChange: OnToolApprovalConfigChange = () => {};

  const toolConfigModel = getContext<ToolConfigModel>(ToolConfigModel.key);
  const mcpServerModel = getMCPServerModelContext();

  const pretendNativeToolDefs = toolConfigModel.getPretendNativeToolDefs();
  const allServers = mcpServerModel.getServers();

  $: partnerMCPservers = toolConfigModel.getEnableNativeRemoteMcp()
    ? filterPartnerMCPTools($allServers)
    : [];
</script>

<div>
  <SettingsCategory {title} loading={tools.length === 0}>
    <div class="tool-category-list">
      {#each tools as config (config.name)}
        <ConfigCompact {config} {onAuthenticate} {onRevokeAccess} {onToolApprovalConfigChange} />
      {/each}
      {#each $pretendNativeToolDefs as config (config.name)}
        <RemoteMCP
          mcpTool={partnerMCPservers.find((server) => server.name === config.name)}
          {config}
        />
      {/each}
    </div>
  </SettingsCategory>
</div>

<style>
  .tool-category-list {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-1);
    padding: var(--ds-spacing-2) 0;
  }
</style>
