<script lang="ts">
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import SpinnerAugment from "$common-webviews/src/design-system/components/SpinnerAugment.svelte";

  export let title: string;
  export let loading: boolean = false;
</script>

<div class="category">
  <div class="category-heading">
    <TextAugment size={1} color="secondary" weight="regular">{title}</TextAugment>
  </div>

  {#if loading}
    <div class="loading-container">
      <SpinnerAugment size={1} />
      <TextAugment size={1} color="secondary">Loading...</TextAugment>
    </div>
  {:else}
    <div class="category-content">
      <slot />
    </div>
  {/if}
</div>

<style>
  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: var(--ds-spacing-3);
  }
</style>
