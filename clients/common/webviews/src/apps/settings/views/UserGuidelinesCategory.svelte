<script lang="ts">
  import { onDestroy, onMount } from "svelte";
  import { writable } from "svelte/store";
  import { createEventDispatcher } from "svelte";
  import MarkdownEditor from "$common-webviews/src/design-system/components/MarkdownEditor.svelte";

  const dispatch = createEventDispatcher();

  export let userGuidelines = "";
  export let userGuidelinesLengthLimit: number | undefined = undefined;
  export let updateUserGuideline: (value: string) => boolean = () => false;

  const originalValue = writable<string | undefined>(undefined);

  // Initialize originalValue when the component is mounted
  onMount(() => {
    $originalValue = userGuidelines.trim();
  });

  function onSave() {
    const value = userGuidelines.trim();
    if ($originalValue === value) {
      return;
    }

    if (updateUserGuideline(value)) {
      // Update was successful, update the originalValue
      $originalValue = value;
      return;
    } else {
      if (!!userGuidelinesLengthLimit && value.length > userGuidelinesLengthLimit) {
        throw `The user guideline must be less than ${userGuidelinesLengthLimit} character long`;
      } else {
        throw "An error occurred updating the user";
      }
    }
  }

  onDestroy(() => {
    onSave();
  });
</script>

<div class="c-user-guidelines-category__input">
  <MarkdownEditor
    bind:value={userGuidelines}
    placeholder="Add your guidelines for Augment Chat..."
    resize="vertical"
    saveFunction={onSave}
    on:focus={(e) => {
      dispatch("focus", e);
    }}
  />
</div>

<style>
  .c-user-guidelines-category__input {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-1);
  }

  .c-user-guidelines-category__input :global(.c-card) {
    --before-background-color: var(--ds-surface);
    min-height: 6em;
  }
</style>
