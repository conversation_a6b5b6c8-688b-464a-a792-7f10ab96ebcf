<script lang="ts">
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import RegularFileIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/file.svg?component";
  import RegularArrowUpRightFromSquareIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/arrow-up-right-from-square.svg?component";
  import RegularPlusIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/plus.svg?component";
  import Download from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/download.svg?component";
  import Trash from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/trash.svg?component";
  import ExclamationTriangle from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/triangle-exclamation.svg?component";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import type { RulesModel } from "../models/rules-model";
  import type { RulesController } from "../controllers/rules-controller";

  import { type Readable } from "svelte/store";
  import DropdownMenuAugment from "$common-webviews/src/design-system/components/DropdownMenuAugment";
  import UserGuidelinesCategory from "./UserGuidelinesCategory.svelte";
  import SettingsCard from "../components/SettingsCard.svelte";
  import RulesModeSelector from "../../rules/RulesModeSelector.svelte";
  import ChevronDown from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/chevron-down.svg?component";
  import CreateRuleDialog from "../components/CreateRuleDialog.svelte";
  import AutoImportRulesDialog from "../components/AutoImportRulesDialog.svelte";
  import type { AutoImportRulesOption } from "@augment-internal/sidecar-libs/src/webview-messages/message-types/rules-messages";
  import { RuleType } from "@augment-internal/sidecar-libs/src/chat/chat-types";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import { calculateRulesAndGuidelinesCharacterCount } from "../../chat/utils/rules-utils";
  import CalloutAugment from "$common-webviews/src/design-system/components/CalloutAugment.svelte";
  import SpinnerAugment from "$common-webviews/src/design-system/components/SpinnerAugment.svelte";

  export let userGuidelines = "";
  export let userGuidelinesLengthLimit: number | undefined = undefined;
  export let workspaceGuidelinesLengthLimit: number | undefined = undefined;
  export let workspaceGuidelinesContent = "";
  export let updateUserGuideline: (value: string) => boolean = () => false;

  // Injected from parent (Settings.svelte)
  export let rulesModel: RulesModel;
  export let rulesController: RulesController;

  const rulesFiles = rulesModel.getCachedRules();
  const showCreateRuleDialog = rulesController.getShowCreateRuleDialog();
  const createRuleError = rulesController.getCreateRuleError();

  $: isRulesLoading = rulesModel.getLoading();
  $: rulesAndGuidelinesLimit = workspaceGuidelinesLengthLimit;
  $: ({ isOverLimit: showRulesAndGuidelinesWarning, warningMessage: rulesWarningMessage } =
    calculateRulesAndGuidelinesCharacterCount({
      rules: $rulesFiles,
      workspaceGuidelinesContent,
      rulesAndGuidelinesLimit,
    }));

  // Auto-import dialog state
  let showAutoImportDialog = false;
  let autoImportOptions: AutoImportRulesOption[] = [];
  let autoImportLoading = false;
  let autoImportError = "";
  let autoImportSuccess = "";

  // Import options
  const importOptions = [
    {
      label: "Auto import existing rules in the workspace",
      id: "auto_import",
      description: "Choose existing rules in your workspace to auto import to Augment.",
    },
    {
      label: "Select file(s) or directory to import",
      id: "select_file_or_directory",
      description: "Select an existing directory or list of markdown files to import to Augment.",
    },
  ] as const;
  type ImportOption = (typeof importOptions)[number];
  let importFocusedIndex: Readable<number | undefined> | undefined = undefined;
  let requestImportDropdownClose: () => void = () => {};

  async function handleImportSelect(option: ImportOption): Promise<void> {
    try {
      if (option.id === "select_file_or_directory") {
        await rulesController.selectFileToImport();
      } else if (option.id === "auto_import") {
        await handleAutoImportRules();
      }
    } catch (e) {
      console.error("Failed to handle import select:", e);
    }

    if (requestImportDropdownClose) requestImportDropdownClose();
  }

  async function handleAutoImportRules(): Promise<void> {
    try {
      autoImportError = "";
      autoImportSuccess = "";

      // Get available options from controller
      const optionsResponse = await rulesController.getAutoImportOptions();
      autoImportOptions = optionsResponse.data.options;

      // Show the modal
      showAutoImportDialog = true;
    } catch (error) {
      console.error("Failed to get auto-import options:", error);
      autoImportError = "Failed to detect existing rules in workspace.";
    }
  }

  async function handleAutoImportSelection(
    event: CustomEvent<AutoImportRulesOption>,
  ): Promise<void> {
    const selectedOption = event.detail;

    try {
      autoImportLoading = true;
      autoImportError = "";

      // Process the selection through the controller
      const result = await rulesController.processAutoImportSelection(selectedOption);

      // Show detailed success message
      let successMessage = `Successfully imported ${result.importedRulesCount} rule${result.importedRulesCount !== 1 ? "s" : ""} from ${selectedOption.label}`;

      if (result.duplicatesCount > 0) {
        successMessage += `, ${result.duplicatesCount} duplicate${result.duplicatesCount !== 1 ? "s" : ""} skipped`;
      }

      if (result.totalAttempted > result.importedRulesCount + result.duplicatesCount) {
        const failed = result.totalAttempted - result.importedRulesCount - result.duplicatesCount;
        successMessage += `, ${failed} failed`;
      }

      autoImportSuccess = successMessage;

      // Close dialog after a short delay
      setTimeout(() => {
        showAutoImportDialog = false;
        autoImportSuccess = "";
      }, 500);
    } catch (error) {
      console.error("Failed to process auto-import selection:", error);
      autoImportError = "Failed to import rules. Please try again.";
    } finally {
      autoImportLoading = false;
    }
  }

  function handleAutoImportCancel(): void {
    showAutoImportDialog = false;
    autoImportError = "";
    autoImportSuccess = "";
  }

  // Dialog handlers
  function handleCreateRule(event: CustomEvent<string>) {
    rulesController.handleCreateRuleWithName(event.detail);
  }

  function handleCancelCreateRule() {
    rulesController.hideCreateRuleDialog();
  }
</script>

<div class="c-rules-category">
  <!-- Project Rules Section -->
  <div class="c-rules-section">
    <TextAugment class="c-section-header" size={3} color="primary">Rules</TextAugment>
    <!-- Rules description field -->
    <div>
      Rules are instructions for Augment Chat and Agent that can be applied automatically across all
      conversations or referenced in specific conversations using @mentions (e.g., @rule-file.md) <a
        href="https://docs.augmentcode.com/setup-augment/guidelines#workspace-guidelines"
        target="_blank"
      >
        <TextAugment size={1} weight="regular">Learn more</TextAugment>
      </a>
    </div>
    {#if showRulesAndGuidelinesWarning}
      <CalloutAugment variant="soft" color="warning" size={1}>
        <ExclamationTriangle slot="icon" />
        {rulesWarningMessage}
      </CalloutAugment>
    {/if}
    <div class="c-rules-list">
      <!-- Show loading state only if there are no current rules so that users don't see spinner while fetching -->
      {#if $isRulesLoading && $rulesFiles.length === 0}
        <div class="loading-container">
          <SpinnerAugment size={1} />
          <TextAugment size={1} color="secondary">Loading rules...</TextAugment>
        </div>
      {:else if $rulesFiles.length === 0}
        <div class="c-rules-list-empty">
          <TextAugment size={1} color="neutral">No rules files found</TextAugment>
        </div>
      {:else}
        {#each $rulesFiles as rule (rule.path)}
          <SettingsCard isClickable on:click={() => rulesController.openRule(rule.path)}>
            <div class="c-rule-item-info" slot="header-left">
              <div class="l-icon-wrapper">
                {#if rule.type === RuleType.AGENT_REQUESTED && !rule.description}
                  <TextTooltipAugment content="No description found">
                    <ExclamationTriangle />
                  </TextTooltipAugment>
                {:else}
                  <RegularFileIcon />
                {/if}
              </div>
              <div class="c-rule-item-path">
                <TextAugment size={1} color="neutral">{rule.path}</TextAugment>
              </div>
            </div>
            <div class="server-actions" slot="header-right">
              <div class="status-controls">
                <div class="c-rules-dropdown">
                  <!-- Regular rules files with dropdown -->
                  <RulesModeSelector
                    {rule}
                    onSave={async (ruleType, description) => {
                      await rulesModel.updateRuleContent({
                        type: ruleType,
                        path: rule.path,
                        content: rule.content,
                        description: description,
                      });
                    }}
                  />
                </div>
                <ButtonAugment
                  size={1}
                  variant="ghost-block"
                  color="neutral"
                  class="c-rule-item-button"
                  on:click={(e) => {
                    e.stopPropagation();
                    rulesController.openRule(rule.path);
                  }}
                >
                  <RegularArrowUpRightFromSquareIcon slot="iconRight" />
                </ButtonAugment>
                <ButtonAugment
                  size={1}
                  variant="ghost-block"
                  color="neutral"
                  class="c-rule-item-button"
                  on:click={(e) => {
                    e.stopPropagation();
                    rulesController.deleteRule(rule.path);
                  }}
                >
                  <Trash slot="iconRight" />
                </ButtonAugment>
              </div>
            </div>
          </SettingsCard>
        {/each}
      {/if}
    </div>
    <div class="c-rules-actions-container">
      <ButtonAugment
        size={1}
        variant="soft"
        color="neutral"
        class="c-rules-action-button"
        on:click={() => rulesController.createRule()}
      >
        <div class="c-rules-actions-button-content">
          <RegularPlusIcon slot="iconLeft" />
          Create new rule file
        </div>
      </ButtonAugment>

      <DropdownMenuAugment.Root
        bind:requestClose={requestImportDropdownClose}
        bind:focusedIndex={importFocusedIndex}
      >
        <DropdownMenuAugment.Trigger>
          <ButtonAugment size={1} variant="soft" color="neutral" class="c-rules-action-button">
            <div class="c-rules-actions-button-content">
              <Download slot="iconLeft" />
              Import rules
              <ChevronDown slot="iconRight" />
            </div>
          </ButtonAugment>
        </DropdownMenuAugment.Trigger>
        <DropdownMenuAugment.Content align="start" side="bottom">
          {#each importOptions as option (option.id)}
            <DropdownMenuAugment.Item onSelect={() => handleImportSelect(option)}>
              {option.label}
            </DropdownMenuAugment.Item>
          {/each}
          {#if $importFocusedIndex !== undefined}
            <DropdownMenuAugment.Separator />
            <DropdownMenuAugment.Label>
              {$importFocusedIndex !== undefined
                ? importOptions[$importFocusedIndex].description
                : importOptions[0]}
            </DropdownMenuAugment.Label>
          {/if}
        </DropdownMenuAugment.Content>
      </DropdownMenuAugment.Root>
    </div>
  </div>
  <!-- User Guidelines Section -->
  <div class="c-user-guidelines-section">
    <TextAugment class="c-section-header" size={3} color="primary">User Guidelines</TextAugment>
    <!-- User Guidelines description field -->
    <div>
      User Guidelines allow you to control Augment's behavior through natural language instructions.
      These guidelines are applied globally to all Chat and Agent interactions. <a
        href="https://docs.augmentcode.com/setup-augment/guidelines#workspace-guidelines"
        target="_blank"
      >
        <TextAugment size={1} weight="regular">Learn more</TextAugment>
      </a>
    </div>
    <UserGuidelinesCategory {userGuidelines} {userGuidelinesLengthLimit} {updateUserGuideline} />
  </div>
</div>

<!-- Create Rule Dialog -->
<CreateRuleDialog
  show={$showCreateRuleDialog}
  errorMessage={$createRuleError}
  on:create={handleCreateRule}
  on:cancel={handleCancelCreateRule}
/>

<!-- Auto Import Rules Dialog -->
<AutoImportRulesDialog
  show={showAutoImportDialog}
  options={autoImportOptions}
  isLoading={autoImportLoading}
  errorMessage={autoImportError}
  successMessage={autoImportSuccess}
  on:select={handleAutoImportSelection}
  on:cancel={handleAutoImportCancel}
/>

<style>
  .c-rules-category {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-5);
    position: relative;
  }

  .c-user-guidelines-section {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-2);
  }

  .c-rules-section {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-2);
  }

  .c-rules-list {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-1);
    padding: var(--ds-spacing-2) 0;
  }

  .c-rules-list-empty {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: var(--ds-spacing-3);
    opacity: 0.7;
    gap: var(--ds-spacing-4);
  }

  .c-rules-list :global(.c-button--content) {
    justify-content: space-between;
    width: 100%;
  }

  .c-rule-item-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: var(--ds-spacing-1);
  }
  .l-icon-wrapper {
    padding-left: var(--ds-spacing-1);
    display: flex;
    align-items: center;
    --icon-size: var(--ds-icon-size-1);
  }

  .c-rule-item-path {
    padding-left: var(--ds-spacing-1);
    font-size: 0.85em;
    opacity: 0.7;
  }

  .c-rules-actions-button-content {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-1);
  }

  .c-rules-actions-container {
    display: flex;
    flex-wrap: wrap;
    gap: var(--ds-spacing-2); /* Add gap between buttons */
    align-items: center;
  }

  .status-controls {
    display: flex;
    gap: var(--ds-spacing-2);
    align-items: center;
  }
  .status-controls :global(.c-button--content) {
    padding: var(--base-btn-padding-vertical) var(--ds-spacing-1);
  }
  .status-controls :global(svg) {
    --button-icon-size: 14px;
  }
</style>
