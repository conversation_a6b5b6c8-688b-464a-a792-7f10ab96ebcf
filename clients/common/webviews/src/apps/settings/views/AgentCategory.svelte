<script lang="ts">
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import ToggleAugment from "$common-webviews/src/design-system/components/ToggleAugment.svelte";
  import { getContext } from "svelte";
  import SettingsCard from "../components/SettingsCard.svelte";
  import { SwarmModeModel } from "../models/swarm-mode-model";

  export let isSwarmModeEnabled = false;
  export let hasEverUsedRemoteAgent = false;

  const swarmModeModel = getContext<SwarmModeModel>(SwarmModeModel.key);
  $: currentSettings = swarmModeModel.getCurrentSettings;
  $: enabled = $currentSettings.enabled;
</script>

{#if isSwarmModeEnabled && hasEverUsedRemoteAgent}
  <TextAugment size={1} weight="regular" color="secondary">
    <div class="section-heading-text">Agent Settings</div>
  </TextAugment>
  <div class="c-agent-settings">
    <SettingsCard>
      <div class="c-agent-setting__info" slot="header-left">
        <div>
          <TextAugment size={2} weight="medium">Enable Swarm Mode</TextAugment>
        </div>
        <div>
          <TextAugment size={1} weight="medium"
            >Allow agents to coordinate and work together on complex tasks</TextAugment
          >
        </div>
        <div class="c-agent-setting__education">
          <TextAugment size={1} weight="regular" color="secondary">
            Sub-agents run in isolated remote environments and communicate via git repositories.
            Each remote agent consumes credits from your account. Changes are reviewed before being
            applied to your local workspace.
          </TextAugment>
        </div>
      </div>
      <div slot="header-right">
        <ToggleAugment
          size={1}
          checked={enabled}
          on:change={() => swarmModeModel.updateEnabled(!enabled)}
        />
      </div>
    </SettingsCard>
  </div>
{/if}

<style>
  .c-agent-settings {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-4);
  }

  .c-agent-settings :global(.settings-card-content) {
    padding: var(--ds-spacing-1);
  }

  .c-agent-setting__info {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-1);
  }

  .c-agent-setting__education {
    margin-top: var(--ds-spacing-2);
    padding: var(--ds-spacing-2);
    background-color: var(--ds-color-background-secondary);
    border-radius: var(--ds-border-radius-1);
    border-left: 3px solid var(--ds-color-border-accent);
  }
</style>
