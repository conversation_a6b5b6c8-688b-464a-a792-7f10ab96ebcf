<script lang="ts">
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import Logout from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/right-from-bracket.svg?component";
  import SettingsCategory from "./SettingsCategory.svelte";
  import { ToolConfigModel } from "$common-webviews/src/apps/settings/models/tool-config-model";
  import { getContext } from "svelte";

  export let onSignOut: () => void;
  let loading = false;
  const toolConfigModel = getContext<ToolConfigModel>(ToolConfigModel.key as any);
  const userEmailStore = toolConfigModel.getUserEmail();
  $: userEmail = $userEmailStore;

  function handleSignOut() {
    onSignOut();
    loading = true;
  }
</script>

<SettingsCategory title="" loading={false}>
  {#if userEmail}
    <div class="account-email">
      <TextAugment size={1} color="secondary">Signed in as</TextAugment>
      <TextAugment size={1} weight="medium">{userEmail}</TextAugment>
    </div>
  {/if}
  <ButtonAugment {loading} variant="soft" on:click={handleSignOut} data-testid="sign-out-button">
    <Logout slot="iconLeft" />
    Sign Out
  </ButtonAugment>
</SettingsCategory>

<style>
  .account-email {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-1);
    margin-bottom: var(--ds-spacing-3);
  }
</style>
