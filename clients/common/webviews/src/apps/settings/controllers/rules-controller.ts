import { writable, type Writable } from "svelte/store";
import type { HostInterface } from "$common-webviews/src/common/hosts/host-types";
import type { MessageBroker } from "$common-webviews/src/common/utils/message-broker";
import { ExtensionClient } from "../../chat/extension-client";
import { ChatFlagsModel } from "../../chat/models/chat-flags-model";
import {
  AgentSessionEventName,
  RulesImportedType,
} from "@augment-internal/sidecar-libs/src/metrics/types";
import {
  AUGMENT_DIRECTORY_ROOT,
  AUGMENT_GUIDELINES_FILE,
  AUGMENT_RULES_FOLDER,
} from "@augment-internal/sidecar-libs/src/utils/rules-parser";
import { WebViewMessageType } from "$vscode/src/webview-providers/webview-messages";
import type {
  AutoImportRulesOption,
  AutoImportRulesOptionsResponse,
} from "@augment-internal/sidecar-libs/src/webview-messages/message-types/rules-messages";
import type { RulesModel } from "../models/rules-model";

/**
 * Controller for handling rules UI interactions and orchestrating between model and view
 */
export class RulesController {
  private _showCreateRuleDialog: Writable<boolean> = writable(false);
  private _createRuleError: Writable<string> = writable("");
  private _extensionClient: ExtensionClient;

  constructor(
    private readonly _host: HostInterface,
    private readonly _msgBroker: MessageBroker,
    private readonly _rulesModel: RulesModel,
  ) {
    // Initialize extension client for UI interactions
    const flagsModel = new ChatFlagsModel();
    this._extensionClient = new ExtensionClient(_host, _msgBroker, flagsModel);
  }

  /**
   * Show the create rule dialog
   */
  async createRule() {
    this._showCreateRuleDialog.set(true);
  }

  /**
   * Handle the actual rule creation with the provided name
   */
  async handleCreateRuleWithName(ruleName: string) {
    if (!ruleName || !ruleName.trim()) {
      this.hideCreateRuleDialog();
      return;
    }

    // Clear any previous error
    this._createRuleError.set("");

    try {
      const createdRule = await this._rulesModel.createRule(ruleName.trim());

      // If a rule was created and we have the file info, open it
      if (createdRule && createdRule.path) {
        await this.openRule(createdRule.path);
      }

      // Report the event
      this._extensionClient.reportAgentSessionEvent({
        eventName: AgentSessionEventName.rulesImported,
        conversationId: "",
        eventData: {
          rulesImportedData: {
            type: RulesImportedType.manuallyCreated,
            numFiles: 1,
            source: "",
          },
        },
      });

      // Hide the dialog on success
      this.hideCreateRuleDialog();
    } catch (error) {
      // Set the error message and keep the dialog open
      const errorMessage = `Failed to create rule "${ruleName.trim()}"`;
      this._createRuleError.set(errorMessage);
    }
  }

  /**
   * Open a rule in the editor
   */
  async openRule(path: string) {
    try {
      // Get the workspace root from model
      const workspaceRoot = await this._rulesModel.getWorkspaceRoot();

      // Check if this is a guidelines file
      if (path === AUGMENT_GUIDELINES_FILE) {
        // Use extension client to open guidelines file
        this._extensionClient.openFile({
          repoRoot: workspaceRoot,
          pathName: AUGMENT_GUIDELINES_FILE,
        });
      } else {
        // Use extension client to open rule file
        this._extensionClient.openFile({
          repoRoot: workspaceRoot,
          pathName: `${AUGMENT_DIRECTORY_ROOT}/${AUGMENT_RULES_FOLDER}/${path}`,
        });
      }
    } catch (error) {
      console.error("Failed to open rule:", error);
    }
  }

  /**
   * Delete a rule with confirmation
   */
  async deleteRule(path: string) {
    try {
      const confirmed = await this._extensionClient.openConfirmationModal({
        title: "Delete Rule",
        message: `Are you sure you want to delete this rule?`,
        confirmButtonText: "Delete",
        cancelButtonText: "Cancel",
      });

      if (confirmed) {
        await this._rulesModel.deleteRule(path);
      }
    } catch (error) {
      console.error("Failed to delete rule:", error);
    }
  }

  /**
   * Handle file import selection
   */
  async selectFileToImport() {
    try {
      // Get selected paths from VSCode file picker
      const response = await this._msgBroker.send<
        { type: WebViewMessageType.triggerImportDialogRequest },
        {
          type: WebViewMessageType.triggerImportDialogResponse;
          data: { selectedPaths: string[] };
        }
      >(
        {
          type: WebViewMessageType.triggerImportDialogRequest,
        },
        100_000, // set long timeout because we're opening a file picker
      );

      // Process the selected paths through model
      if (response.data.selectedPaths && response.data.selectedPaths.length > 0) {
        const result = await this._rulesModel.processSelectedPaths(response.data.selectedPaths);

        // Handle UI feedback
        this._showImportNotification(result);
        this._reportSelectedImportMetrics(result);
      }
    } catch (error) {
      console.error("Failed to import files:", error);
    }
  }

  /**
   * Get available auto-import options
   */
  async getAutoImportOptions(): Promise<AutoImportRulesOptionsResponse> {
    return await this._rulesModel.getAutoImportOptions();
  }

  /**
   * Process auto-import rules selection and perform import
   */
  async processAutoImportSelection(selectedOption: AutoImportRulesOption): Promise<{
    importedRulesCount: number;
    duplicatesCount: number;
    totalAttempted: number;
    source: string;
  }> {
    const result = await this._rulesModel.processAutoImportSelection(selectedOption);

    // Handle UI feedback
    this._showImportNotification(result);
    this._reportAutoImportMetrics(result);

    return result;
  }

  /**
   * Show auto-import notification
   */
  private _showImportNotification(result: {
    importedRulesCount: number;
    duplicatesCount?: number;
    source?: string;
  }) {
    let notificationMessage;
    if (result.importedRulesCount === 0) {
      notificationMessage = result.source
        ? `No new rules imported from ${result.source}`
        : `No new rules imported`;
    } else {
      notificationMessage = `Successfully imported ${result.importedRulesCount} rule${result.importedRulesCount !== 1 ? "s" : ""}`;
      if (result.duplicatesCount && result.duplicatesCount > 0) {
        notificationMessage += ` and skipped ${result.duplicatesCount} duplicate${result.duplicatesCount !== 1 ? "s" : ""}`;
      }
    }
    this._extensionClient.showNotification({
      message: notificationMessage,
      type: result.importedRulesCount > 0 ? "info" : "warning",
    });
  }

  /**
   * Report import metrics
   */
  private _reportSelectedImportMetrics(result: {
    importedRulesCount: number;
    directoryOrFile?: string;
  }) {
    const ruleImportType =
      result.directoryOrFile === "directory"
        ? RulesImportedType.selectedDirectory
        : result.directoryOrFile === "file"
          ? RulesImportedType.selectedFile
          : RulesImportedType.selectedFile; // default for "mixed"

    this._extensionClient.reportAgentSessionEvent({
      eventName: AgentSessionEventName.rulesImported,
      conversationId: "",
      eventData: {
        rulesImportedData: {
          type: ruleImportType,
          numFiles: result.importedRulesCount,
          source: "",
        },
      },
    });
  }

  /**
   * Report auto-import metrics
   */
  private _reportAutoImportMetrics(result: { importedRulesCount: number; source: string }) {
    this._extensionClient.reportAgentSessionEvent({
      eventName: AgentSessionEventName.rulesImported,
      conversationId: "",
      eventData: {
        rulesImportedData: {
          type: RulesImportedType.auto,
          numFiles: result.importedRulesCount,
          source: result.source,
        },
      },
    });
  }

  /**
   * Get the create rule dialog state store
   */
  getShowCreateRuleDialog() {
    return this._showCreateRuleDialog;
  }

  /**
   * Get the create rule error store
   */
  getCreateRuleError() {
    return this._createRuleError;
  }

  /**
   * Hide the create rule dialog
   */
  hideCreateRuleDialog() {
    this._showCreateRuleDialog.set(false);
    this._createRuleError.set(""); // Clear error when hiding dialog
  }
}
