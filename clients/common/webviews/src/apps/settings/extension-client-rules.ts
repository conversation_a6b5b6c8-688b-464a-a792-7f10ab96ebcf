/**
 * @file extension-client-rules.ts
 * This file contains the extension client interface for rules-related operations.
 */

import type { AsyncMsgSender } from "$common-webviews/src/common/hosts/async-messaging";
import type { Rule } from "@augment-internal/sidecar-libs/src/chat/chat-types";
import {
  RulesWebViewMessageType,
  type GetRulesListRequest,
  type GetRulesListResponse,
  type CreateRuleRequest,
  type CreateRuleResponse,
  type DeleteRuleRequest,
  type DeleteRuleResponse,
  type UpdateRuleFileRequest,
  type UpdateRuleFileResponse,
  type GetWorkspaceRootRequest,
  type GetWorkspaceRootResponse,
  type AutoImportRulesRequest,
  type AutoImportRulesOptionsResponse,
  type AutoImportRulesSelectionRequest,
  type AutoImportRulesResponse,
  type ProcessSelectedPathsRequest,
  type ProcessSelectedPathsResponse,
} from "@augment-internal/sidecar-libs/src/webview-messages/message-types/rules-messages";

/**
 * Extension client interface for rules-related operations.
 * Provides methods for creating, updating, deleting and retrieving rules.
 */
export class ExtensionClientRules {
  /* eslint-disable-next-line @typescript-eslint/naming-convention */
  private readonly SIDECAR_TIMEOUT_MS = 5000; // 5 seconds - chosen to balance responsiveness with reliability for file system operations

  constructor(private readonly _asyncMsgSender: AsyncMsgSender) {}

  /**
   * Gets the list of rules from the sidecar.
   * @param includeGuidelines - Whether to include guidelines in the response
   * @returns A promise that resolves to the list of rules
   */
  getRulesList = async (includeGuidelines: boolean = true): Promise<Rule[]> => {
    type ReqT = GetRulesListRequest;
    type ResT = GetRulesListResponse;
    const request: ReqT = {
      type: RulesWebViewMessageType.getRulesListRequest,
      data: { includeGuidelines },
    };
    const msg = await this._asyncMsgSender.sendToSidecar<ReqT, ResT>(
      request,
      this.SIDECAR_TIMEOUT_MS,
    );
    return msg.data.rules;
  };

  /**
   * Creates a new rule.
   * @param ruleName - The name of the rule to create
   * @returns A promise that resolves to the created rule info (path and repoRoot) or null
   */
  createRule = async (ruleName: string): Promise<{ path: string; repoRoot: string } | null> => {
    type ReqT = CreateRuleRequest;
    type ResT = CreateRuleResponse;
    const request: ReqT = {
      type: RulesWebViewMessageType.createRule,
      data: { ruleName: ruleName.trim() },
    };
    const msg = await this._asyncMsgSender.sendToSidecar<ReqT, ResT>(
      request,
      this.SIDECAR_TIMEOUT_MS,
    );
    return msg.data.createdRule || null;
  };

  /**
   * Gets the workspace root path.
   * @returns A promise that resolves to the workspace root path
   */
  getWorkspaceRoot = async (): Promise<string> => {
    type ReqT = GetWorkspaceRootRequest;
    type ResT = GetWorkspaceRootResponse;
    const request: ReqT = {
      type: RulesWebViewMessageType.getWorkspaceRoot,
    };
    const msg = await this._asyncMsgSender.sendToSidecar<ReqT, ResT>(
      request,
      this.SIDECAR_TIMEOUT_MS,
    );
    return msg.data.workspaceRoot || "";
  };

  /**
   * Updates the content of a rule file.
   * @param path - The path of the rule file to update
   * @param content - The new content for the rule file
   * @returns A promise that resolves when the update is complete
   */
  updateRuleFile = async (path: string, content: string): Promise<void> => {
    type ReqT = UpdateRuleFileRequest;
    type ResT = UpdateRuleFileResponse;
    const request: ReqT = {
      type: RulesWebViewMessageType.updateRuleFile,
      data: { path, content },
    };
    await this._asyncMsgSender.sendToSidecar<ReqT, ResT>(request, this.SIDECAR_TIMEOUT_MS);
  };

  /**
   * Deletes a rule.
   * @param path - The path of the rule to delete
   * @param confirmed - Whether the deletion is confirmed
   * @returns A promise that resolves when the deletion is complete
   */
  deleteRule = async (path: string, confirmed: boolean = true): Promise<void> => {
    type ReqT = DeleteRuleRequest;
    type ResT = DeleteRuleResponse;
    const request: ReqT = {
      type: RulesWebViewMessageType.deleteRule,
      data: { path, confirmed },
    };
    await this._asyncMsgSender.sendToSidecar<ReqT, ResT>(request, this.SIDECAR_TIMEOUT_MS);
  };

  /**
   * Processes selected paths for rule import.
   * @param selectedPaths - The paths to process
   * @param autoImport - Whether to auto-import the rules
   * @returns A promise that resolves to the import results
   */
  processSelectedPaths = async (
    selectedPaths: string[],
    autoImport: boolean = true,
  ): Promise<{
    importedRulesCount: number;
    directoryOrFile?: string;
    errors?: string[];
  }> => {
    type ReqT = ProcessSelectedPathsRequest;
    type ResT = ProcessSelectedPathsResponse;
    const request: ReqT = {
      type: RulesWebViewMessageType.processSelectedPathsRequest,
      data: { selectedPaths, autoImport },
    };
    const msg = await this._asyncMsgSender.sendToSidecar<ReqT, ResT>(
      request,
      this.SIDECAR_TIMEOUT_MS,
    );
    return {
      importedRulesCount: msg.data.importedRulesCount,
      directoryOrFile: msg.data.directoryOrFile,
      errors: msg.data.errors,
    };
  };

  /**
   * Gets available auto-import options.
   * @returns A promise that resolves to the auto-import options response
   */
  getAutoImportOptions = async (): Promise<AutoImportRulesOptionsResponse> => {
    type ReqT = AutoImportRulesRequest;
    type ResT = AutoImportRulesOptionsResponse;
    const request: ReqT = {
      type: RulesWebViewMessageType.autoImportRules,
    };
    return await this._asyncMsgSender.sendToSidecar<ReqT, ResT>(request, this.SIDECAR_TIMEOUT_MS);
  };

  /**
   * Processes auto-import rules selection.
   * @param selectedLabel - The label of the selected option
   * @returns A promise that resolves to the import results
   */
  processAutoImportSelection = async (
    selectedLabel: string,
  ): Promise<{
    importedRulesCount: number;
    duplicatesCount: number;
    totalAttempted: number;
    source: string;
  }> => {
    type ReqT = AutoImportRulesSelectionRequest;
    type ResT = AutoImportRulesResponse;
    const request: ReqT = {
      type: RulesWebViewMessageType.autoImportRulesSelectionRequest,
      data: { selectedLabel },
    };
    const msg = await this._asyncMsgSender.sendToSidecar<ReqT, ResT>(
      request,
      this.SIDECAR_TIMEOUT_MS,
    );
    return {
      importedRulesCount: msg.data.importedRulesCount,
      duplicatesCount: msg.data.duplicatesCount,
      totalAttempted: msg.data.totalAttempted,
      source: msg.data.source,
    };
  };
}
