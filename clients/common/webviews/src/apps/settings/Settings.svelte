<script lang="ts">
  import { host } from "$common-webviews/src/common/hosts/host";
  import { MessageBroker } from "$common-webviews/src/common/utils/message-broker";
  import { ToolConfigModel, type ConfigBlock } from "./models/tool-config-model";
  import { MCPServerModel } from "./models/mcp-server-model";
  import { TerminalModel } from "./models/terminal-model";
  import { SoundModel } from "./models/sound-model";
  import { SwarmModeModel } from "./models/swarm-mode-model";
  import { onDestroy, setContext } from "svelte";
  import {
    WebViewMessageType,
    type WebViewMessage,
  } from "$vscode/src/webview-providers/webview-messages";
  import WorkspaceContext from "../workspace-context/WSContext.svelte";
  import Navigation, {
    createNavigationItem,
  } from "$common-webviews/src/common/components/navigation/Navigation.svelte";
  import { isNavigationItem } from "$common-webviews/src/common/components/navigation/navigation-types";
  import ToolIcon from "$common-webviews/src/design-system/icons/augment/tools.svelte";
  import ContextDoc from "$common-webviews/src/design-system/icons/augment/context-doc.svelte";
  import SettingsTools from "$common-webviews/src/apps/settings/views/SettingsTools.svelte";
  import MonacoProvider from "$common-webviews/src/design-system/components/MonacoProvider";
  import Stack from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/layer-group.svg?component";
  import Ruler from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/ruler.svg?component";
  import Account from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/user.svg?component";
  import UserGuidelinesCategory from "./views/UserGuidelinesCategory.svelte";
  import RulesCategory from "./views/RulesCategory.svelte";
  import AccountCategory from "./views/AccountCategory.svelte";
  import { ExtensionClient } from "$common-webviews/src/apps/chat/extension-client";
  import { setExtensionClientContext } from "$common-webviews/src/apps/chat/extension-client-context";
  import { setMCPServerModelContext } from "./models/mcp-server-model-context";
  import { ChatFlagsModel } from "$common-webviews/src/apps/chat/models/chat-flags-model";
  import {
    type ToolApprovalConfig,
    type ToolIdentifier,
  } from "@augment-internal/sidecar-libs/src/tools/tool-types";
  import { RulesModel } from "./models/rules-model";
  import { RulesController } from "./controllers/rules-controller";

  const toolConfigModel = new ToolConfigModel(host);
  const mcpServerModel = new MCPServerModel(host);
  const terminalModel = new TerminalModel(host);
  const msgBroker = new MessageBroker(host);

  // Create extension client for confirmation modals
  const flagsModel = new ChatFlagsModel();
  const extensionClient = new ExtensionClient(host, msgBroker, flagsModel);
  const soundModel = new SoundModel(msgBroker);
  const swarmModeModel = new SwarmModeModel(msgBroker);
  // Initialize Rules model/controller here and pass to RulesCategory
  const rulesModel = new RulesModel(msgBroker);
  const rulesController = new RulesController(host, msgBroker, rulesModel);
  msgBroker.registerConsumer(rulesModel);

  setContext(SoundModel.key, soundModel);
  setContext(SwarmModeModel.key, swarmModeModel);
  setContext(ToolConfigModel.key, toolConfigModel);
  setExtensionClientContext(extensionClient);
  setMCPServerModelContext(mcpServerModel);

  const settingsComponentSupported = toolConfigModel.getSettingsComponentSupported();
  const enableAgentMode = toolConfigModel.getEnableAgentMode();
  const enableAgentSwarmMode = toolConfigModel.getEnableAgentSwarmMode();
  const hasEverUsedRemoteAgent = toolConfigModel.getHasEverUsedRemoteAgent();
  msgBroker.registerConsumer(toolConfigModel);
  msgBroker.registerConsumer(mcpServerModel);
  msgBroker.registerConsumer(terminalModel);
  const terminalSettingsStore = terminalModel.getTerminalSettings();

  let selectedId: string | undefined;

  // Create a message consumer for orientation status updates and section navigation
  const settingsConsumer = {
    handleMessageFromExtension(e: MessageEvent<WebViewMessage>) {
      if (e.data && e.data.type === WebViewMessageType.navigateToSettingsSection) {
        // Scroll to the section based on the message data
        if (e.data.data && typeof e.data.data === "string") {
          selectedId = e.data.data;
        }
        return true;
      }
      return false;
    },
  };
  // Register the consumer
  msgBroker.registerConsumer(settingsConsumer);

  // Get filtered, sorted, and deduplicated tools
  const displayableTools = toolConfigModel.getDisplayableTools();

  const guidelines = toolConfigModel.getGuidelines();
  $: userGuidelines = $guidelines.userGuidelines?.contents;
  $: userGuidelinesLimit = $guidelines.userGuidelines?.lengthLimit;
  $: workspaceGuidelinesLimit = $guidelines.workspaceGuidelines?.[0]?.lengthLimit;
  $: workspaceGuidelinesContent = $guidelines.workspaceGuidelines?.[0]?.contents || "";

  function updateUserGuideline(value: string) {
    const guideline = value.trim();
    if (!!userGuidelinesLimit && guideline.length > userGuidelinesLimit) {
      return false;
    }

    // Update the local state immediately to ensure it's available when switching tabs
    toolConfigModel.updateLocalUserGuidelines(guideline);

    // Send the update to the extension
    host.postMessage({
      type: WebViewMessageType.updateUserGuidelines,
      data: value,
    });

    return true;
  }

  function handleAuthenticate(url: string) {
    // Use the tool-call message type to call the openBrowser tool
    host.postMessage({
      type: WebViewMessageType.toolConfigStartOAuth,
      data: {
        authUrl: url,
      },
    });

    // Start polling after authentication
    toolConfigModel.startPolling();
  }

  async function handleRevokeAccess(config: ConfigBlock) {
    // Show confirmation modal before revoking access
    const confirmed = await extensionClient.openConfirmationModal({
      title: "Revoke Access",
      message: `Are you sure you want to revoke access for ${config.displayName}? This will disconnect the tool and you'll need to reconnect it to use it again.`,
      confirmButtonText: "Revoke Access",
      cancelButtonText: "Cancel",
    });

    if (!confirmed) {
      return; // User cancelled
    }

    // Proceed with revoking access
    host.postMessage({
      type: WebViewMessageType.toolConfigRevokeAccess,
      data: {
        toolId: config.identifier,
      },
    });
  }

  function handleShellSelect(friendlyShellName: string) {
    terminalModel.updateSelectedShell(friendlyShellName);
  }

  function handleStartupScriptChange(script: string) {
    terminalModel.updateStartupScript(script);
  }

  function handleToolApprovalConfigChange(
    toolId: ToolIdentifier,
    approvalConfig: ToolApprovalConfig,
  ) {
    // Send the approval level change to the extension
    host.postMessage({
      type: WebViewMessageType.toolApprovalConfigSetRequest,
      data: {
        toolId: toolId,
        approvalConfig: approvalConfig,
      },
    });

    // The update to the UI state should then be completed when the extension host sends us
    // an updated tool configuration in a webview message.
  }
  // Clean up resources when the component is destroyed
  onDestroy(() => {
    toolConfigModel.dispose();
    soundModel.dispose();
    swarmModeModel.dispose();
  });

  toolConfigModel.notifyLoaded();

  // Request the current orientation status when settings panel initializes
  host.postMessage({
    type: WebViewMessageType.getOrientationStatus,
  });

  function handleSignOut() {
    host.postMessage({
      type: WebViewMessageType.signOut,
    });
  }

  $: items = [
    $settingsComponentSupported.remoteTools
      ? // SVELTE5_MIGRATION - AU-11864
        // @ts-expect-error - Svelte 5 component type compatibility issue
        createNavigationItem("Tools", "", ToolIcon, "section-tools")
      : undefined,
    $settingsComponentSupported.userGuidelines && !$settingsComponentSupported.rules
      ? createNavigationItem(
          "User Guidelines",
          "Guidelines for Augment Chat to follow.",
          Stack,
          "guidelines",
        )
      : undefined,
    $settingsComponentSupported.rules
      ? createNavigationItem("Rules and User Guidelines", "", Ruler, "guidelines")
      : undefined,
    $settingsComponentSupported.workspaceContext
      ? // SVELTE5_MIGRATION - AU-11864
        // @ts-expect-error - Svelte 5 component type compatibility issue
        createNavigationItem("Context", "", ContextDoc, "context")
      : undefined,
    createNavigationItem("Account", "Manage your Augment account settings.", Account, "account"),
  ].filter(Boolean);

  $: {
    //make it select the first tool, if loaded async... the account should not be the first
    // thing shown.
    if (items.length > 1 && !selectedId) {
      selectedId = items[0]?.id;
    }
  }

  host.postMessage({
    type: WebViewMessageType.settingsPanelLoaded,
  });
</script>

<svelte:window on:message={msgBroker.onMessageFromExtension} />
<MonacoProvider.Root>
  <!-- SVELTE5_MIGRATION - AU-11864 -->
  <!-- @ts-expect-error - Svelte 5 component type compatibility issue -->
  <Navigation {items} mode="tree" class="c-settings-navigation" {selectedId}>
    <span slot="content" let:item>
      {#if !isNavigationItem(item)}
        <!-- it should always be this so please ignore, this is mostly so it compiles nicely-->
      {:else if item?.id === "context"}
        <WorkspaceContext />
      {:else if item?.id === "guidelines"}
        {#if $settingsComponentSupported.rules}
          <RulesCategory
            {userGuidelines}
            userGuidelinesLengthLimit={userGuidelinesLimit}
            workspaceGuidelinesLengthLimit={workspaceGuidelinesLimit}
            {workspaceGuidelinesContent}
            {updateUserGuideline}
            {rulesModel}
            {rulesController}
          />
        {:else}
          <UserGuidelinesCategory
            {userGuidelines}
            userGuidelinesLengthLimit={userGuidelinesLimit}
            {updateUserGuideline}
          />
        {/if}
      {:else if item?.id === "account"}
        <AccountCategory onSignOut={handleSignOut} />
      {:else}
        <SettingsTools
          tools={$displayableTools}
          onAuthenticate={handleAuthenticate}
          onRevokeAccess={handleRevokeAccess}
          onToolApprovalConfigChange={handleToolApprovalConfigChange}
          onMCPServerAdd={(server) => mcpServerModel.addServer(server)}
          onMCPServerSave={(server) => mcpServerModel.updateServer(server)}
          onMCPServerDelete={(server) => mcpServerModel.deleteServer(server)}
          onMCPServerToggleDisable={(server) => mcpServerModel.toggleDisabledServer(server)}
          onMCPServerJSONImport={(jsonString) => mcpServerModel.importServersFromJSON(jsonString)}
          isMCPEnabled={$enableAgentMode && $settingsComponentSupported.mcpServerList}
          isMCPImportEnabled={$enableAgentMode && $settingsComponentSupported.mcpServerImport}
          supportedShells={$terminalSettingsStore.supportedShells}
          selectedShell={$terminalSettingsStore.selectedShell}
          startupScript={$terminalSettingsStore.startupScript}
          onShellSelect={handleShellSelect}
          onStartupScriptChange={handleStartupScriptChange}
          isTerminalEnabled={$settingsComponentSupported.terminal}
          isSoundCategoryEnabled={true}
          isAgentCategoryEnabled={$enableAgentMode}
          isSwarmModeFeatureFlagEnabled={$enableAgentSwarmMode}
          hasEverUsedRemoteAgent={$hasEverUsedRemoteAgent}
        />
      {/if}
    </span>
  </Navigation>
</MonacoProvider.Root>

<style>
  :global(.c-settings-navigation) {
    flex: 1;
    display: flex;
    flex-direction: column;
    --settings-section-background: var(--ds-color-neutral-a4);
  }
  :global(.c-settings-navigation .c-navigation__head) {
    width: 100%;
  }
  :global(.dark .c-navigation) {
    --settings-section-background: var(--ds-color-neutral-a2);
  }
</style>
