import { writable, type Writable } from "svelte/store";
import { type MessageBroker } from "$common-webviews/src/common/utils/message-broker";
import {
  AgentWebViewMessageType,
  type GetSwarmModeSettingsRequest,
  type GetSwarmModeSettingsResponse,
  type UpdateSwarmModeSettingsRequest,
} from "@augment-internal/sidecar-libs/src/webview-messages/message-types/agent-messages";
import {
  DEFAULT_SWARM_MODE_SETTINGS,
  type SwarmModeSettings,
} from "@augment-internal/sidecar-libs/src/agent/agent-edit-types";

/**
 * Model for managing swarm mode settings via sidecar
 */
export class SwarmModeModel {
  public static key = "swarmModeModel";
  /* eslint-disable-next-line @typescript-eslint/naming-convention */
  private static readonly POLLING_INTERVAL_MS = 5000;
  private _swarmModeSettings: Writable<SwarmModeSettings> = writable(DEFAULT_SWARM_MODE_SETTINGS);
  private _isLoaded = false;
  private _pollInterval: ReturnType<typeof setInterval> | null = null;
  private _lastKnownSettingsHash: string = "";

  constructor(private readonly _msgBroker: MessageBroker) {
    // Initialize swarm mode settings when the model is created
    this.initialize();

    // Start polling for changes from other webviews
    this.startPolling();
  }

  /**
   * Get the current swarm mode settings value
   */
  get getCurrentSettings(): Writable<SwarmModeSettings> {
    return this._swarmModeSettings;
  }

  /**
   * Initialize the swarm mode settings by loading from the sidecar
   */
  async initialize(): Promise<void> {
    if (this._isLoaded) {
      return;
    }

    try {
      const response = await this._msgBroker.sendToSidecar<
        GetSwarmModeSettingsRequest,
        GetSwarmModeSettingsResponse
      >({
        type: AgentWebViewMessageType.getSwarmModeSettings,
      });

      if (response.data) {
        this._swarmModeSettings.set(response.data);
        this._lastKnownSettingsHash = JSON.stringify(response.data);
      }
      this._isLoaded = true;
    } catch (error) {
      console.warn("Failed to load swarm mode settings, using defaults:", error);
      this._swarmModeSettings.set(DEFAULT_SWARM_MODE_SETTINGS);
      this._lastKnownSettingsHash = JSON.stringify(DEFAULT_SWARM_MODE_SETTINGS);
      this._isLoaded = true;
    }
  }

  /**
   * Update swarm mode settings via sidecar
   */
  async updateSettings(updates: Partial<SwarmModeSettings>): Promise<void> {
    try {
      // Send update to sidecar and get the updated settings back
      const response = await this._msgBroker.sendToSidecar<
        UpdateSwarmModeSettingsRequest,
        GetSwarmModeSettingsResponse
      >({
        type: AgentWebViewMessageType.updateSwarmModeSettings,
        data: updates,
      } as UpdateSwarmModeSettingsRequest);

      // Update local store with the response from sidecar
      if (response.data) {
        this._swarmModeSettings.set(response.data);
        this._lastKnownSettingsHash = JSON.stringify(response.data);
      }
    } catch (error) {
      console.error("Failed to update swarm mode settings:", error);
      throw error;
    }
  }

  /**
   * Enable or disable swarm mode
   */
  async setEnabled(enabled: boolean): Promise<void> {
    await this.updateSettings({ enabled });
  }

  /**
   * Reset to default settings
   */
  async resetToDefaults(): Promise<void> {
    await this.updateSettings(DEFAULT_SWARM_MODE_SETTINGS);
  }

  /**
   * Update whether swarm mode is enabled (legacy method for compatibility)
   * @param enabled Whether swarm mode should be enabled
   */
  updateEnabled(enabled: boolean) {
    this.setEnabled(enabled).catch((error) => {
      console.error("Failed to update enabled setting:", error);
    });
  }

  /**
   * Start polling for swarm mode settings changes from other webviews
   */
  private startPolling(): void {
    // Poll every 2 seconds for changes
    this._pollInterval = setInterval(() => {
      this.checkForUpdates();
    }, SwarmModeModel.POLLING_INTERVAL_MS);
  }

  /**
   * Stop polling for changes
   */
  private stopPolling(): void {
    if (this._pollInterval !== null) {
      clearInterval(this._pollInterval);
      this._pollInterval = null;
    }
  }

  /**
   * Check if settings have been updated by another webview
   */
  private async checkForUpdates(): Promise<void> {
    try {
      const response = await this._msgBroker.sendToSidecar<
        GetSwarmModeSettingsRequest,
        GetSwarmModeSettingsResponse
      >({
        type: AgentWebViewMessageType.getSwarmModeSettings,
      });

      const settingsHash = JSON.stringify(response.data);
      if (
        this._lastKnownSettingsHash &&
        settingsHash !== this._lastKnownSettingsHash &&
        response.data
      ) {
        // Settings have changed, update local state
        this._swarmModeSettings.set(response.data);
      }
      this._lastKnownSettingsHash = settingsHash;
    } catch (error) {
      console.warn("Failed to check for swarm mode settings updates:", error);
    }
  }

  /**
   * Cleanup method to stop polling when the model is destroyed
   */
  dispose = (): void => {
    this.stopPolling();
  };
}
