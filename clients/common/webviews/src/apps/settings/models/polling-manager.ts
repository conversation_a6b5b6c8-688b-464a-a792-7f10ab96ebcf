/**
 * Configuration parameters for the PollingManager.
 */
export interface PollingParams {
  /**
   * Base polling interval in milliseconds (default polling rate).
   * If set to 0, polling will stop after maxSteps executions.
   * If greater than 0, polling will continue indefinitely at the maxMS interval after maxSteps.
   */
  maxMS: number;
  /** Accelerated polling interval in milliseconds (faster polling after user interaction) */
  initialMS: number;
  /** Backoff multiplier (how much to increase the interval at each step) */
  mult: number;
  /** Maximum number of backoff steps before returning to base interval */
  maxSteps?: number;
}

/**
 * Default polling parameters.
 */
const defaultPollingParams: PollingParams = {
  maxMS: 15 * 60 * 1000, // 15 minutes (base interval)
  initialMS: 60 * 1000, // 1 minute (accelerated interval)
  mult: 2, // Double the interval each time
  maxSteps: 4, // Number of steps to reach base interval
};

/**
 * Manages polling with backoff strategy.
 * Polls more frequently after user interactions, then gradually backs off to a base interval.
 */
export class PollingManager {
  private timerId: number | null = null;
  private currentMS: number;
  private step = 0;
  private readonly params: PollingParams;

  constructor(
    private readonly callback: () => void | Promise<void>,
    inputParams: PollingParams = defaultPollingParams,
  ) {
    // Create validated params from input
    const safeParams: PollingParams = { ...inputParams };

    // Guard against negative values
    if (safeParams.maxMS < 0) {
      console.warn("PollingManager: Negative maxMS detected, using default value of 15 minutes");
      safeParams.maxMS = defaultPollingParams.maxMS;
    }

    // Guard against negative or zero initialMS
    if (safeParams.initialMS <= 0) {
      console.warn(
        "PollingManager: Negative or zero initialMS detected, using default value of 1 minute",
      );
      safeParams.initialMS = defaultPollingParams.initialMS;
    }

    // Guard against negative or zero multiplier
    if (safeParams.mult <= 0) {
      console.warn(
        "PollingManager: Negative or zero multiplier detected, using default value of 2",
      );
      safeParams.mult = defaultPollingParams.mult;
    }

    // Guard against negative maxSteps
    if (safeParams.maxSteps !== undefined && safeParams.maxSteps < 0) {
      console.warn("PollingManager: Negative maxSteps detected, using default value of 4");
      safeParams.maxSteps = defaultPollingParams.maxSteps;
    }

    // Set the validated params as a class member
    this.params = safeParams;
    this.currentMS = this.params.maxMS;
  }

  /**
   * Start or restart polling with the initial interval
   */
  startPolling(): void {
    // Reset to initial state
    this.stopPolling();
    this.currentMS = this.params.initialMS;
    this.step = 0;

    // Execute immediately
    this.safeExecute();

    // Schedule next execution
    this.scheduleNext();
  }

  /**
   * Stop polling
   */
  stopPolling(): void {
    if (this.timerId !== null) {
      window.clearTimeout(this.timerId);
      this.timerId = null;
    }
  }

  /**
   * Clean up resources
   */
  dispose(): void {
    this.stopPolling();
  }

  /**
   * Schedule the next execution with the current interval
   */
  private scheduleNext(): void {
    this.timerId = window.setTimeout(() => {
      this.safeExecute();

      // Apply backoff if needed
      if (this.params.maxMS === 0) {
        // If maxMS is 0, check if we've reached the max steps
        this.step++;
        if (this.params.maxSteps !== undefined && this.step >= this.params.maxSteps) {
          // Stop polling completely after reaching max steps
          this.stopPolling();
          return;
        }
      } else if (this.currentMS < this.params.maxMS) {
        // Normal backoff logic when maxMS is defined
        this.step++;
        if (this.params.maxSteps !== undefined && this.step >= this.params.maxSteps) {
          // Reset to base interval
          this.currentMS = this.params.maxMS;
          this.step = 0;
        } else {
          // Increase interval by backoff factor
          this.currentMS = Math.min(this.currentMS * this.params.mult, this.params.maxMS);
        }
      }

      // Schedule next execution
      this.scheduleNext();
    }, this.currentMS);
  }

  /**
   * Execute the callback safely, handling any errors
   */
  private safeExecute(): void {
    try {
      const result = this.callback();
      if (result instanceof Promise) {
        result.catch((error) => console.error("Error in polling callback:", error));
      }
    } catch (error) {
      console.error("Error in polling callback:", error);
    }
  }
}
