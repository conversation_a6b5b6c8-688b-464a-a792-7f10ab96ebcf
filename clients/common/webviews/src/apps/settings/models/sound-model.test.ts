import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import { get } from "svelte/store";
import { SoundModel } from "./sound-model";
import { type MessageBroker } from "$common-webviews/src/common/utils/message-broker";
import { AgentWebViewMessageType } from "@augment-internal/sidecar-libs/src/webview-messages/message-types/agent-messages";
import { DEFAULT_SOUND_SETTINGS } from "@augment-internal/sidecar-libs/src/agent/agent-edit-types";

// Mock the message broker
const mockMsgBroker = {
  registerConsumer: vi.fn(),
  sendToSidecar: vi.fn(),
} as unknown as MessageBroker;

describe("SoundModel", () => {
  let soundModel: SoundModel;

  beforeEach(() => {
    vi.useFakeTimers();
    vi.clearAllMocks();

    // Mock successful sidecar responses
    (mockMsgBroker.sendToSidecar as any).mockResolvedValue({
      data: DEFAULT_SOUND_SETTINGS,
    });
  });

  afterEach(() => {
    soundModel?.dispose();
    vi.useRealTimers();
  });

  it("should initialize with default settings", async () => {
    soundModel = new SoundModel(mockMsgBroker);

    // Wait for initialization (just run pending timers once)
    await vi.runOnlyPendingTimersAsync();

    expect(get(soundModel.getCurrentSettings)).toEqual(DEFAULT_SOUND_SETTINGS);
  });

  it("should update settings via sidecar", async () => {
    soundModel = new SoundModel(mockMsgBroker);

    const updates = { enabled: false };
    await soundModel.updateSettings(updates);

    expect(mockMsgBroker.sendToSidecar).toHaveBeenCalledWith({
      type: AgentWebViewMessageType.updateSoundSettings,
      data: updates,
    });
  });
});
