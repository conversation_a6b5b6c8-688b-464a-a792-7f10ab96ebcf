/**
 * Context utilities for MCPServerModel
 *
 * This module provides getter/setter functions for managing MCPServerModel context
 * without exposing the context key directly, avoiding coupling between the class
 * and the interface.
 */

import { getContext, setContext } from "svelte";
import type { MCPServerModel } from "./mcp-server-model";

/**
 * Private context key for MCPServerModel.
 * This key is not exported to prevent direct usage.
 */
const MCP_SERVER_MODEL_CONTEXT_KEY = "mcpServerModel";

/**
 * Sets the MCPServerModel context for child components.
 *
 * @param mcpServerModel - The MCPServerModel instance to set in context
 */
export function setMCPServerModelContext(mcpServerModel: MCPServerModel): void {
  setContext(MCP_SERVER_MODEL_CONTEXT_KEY, mcpServerModel);
}

/**
 * Gets the MCPServerModel from the current component context.
 *
 * @returns The MCPServerModel instance from context
 * @throws Error if MCPServerModel context is not found
 */
export function getMCPServerModelContext(): MCPServerModel {
  const mcpServerModel = getContext<MCPServerModel>(MCP_SERVER_MODEL_CONTEXT_KEY);
  if (!mcpServerModel) {
    throw new Error(
      "MCPServerModel context not found. Make sure setMCPServerModelContext() was called in a parent component.",
    );
  }
  return mcpServerModel;
}

/**
 * Gets the MCPServerModel from the current component context, returning undefined if not found.
 *
 * @returns The MCPServerModel instance from context, or undefined if not found
 */
export function getMCPServerModelContextOptional(): MCPServerModel | undefined {
  return getContext<MCPServerModel>(MCP_SERVER_MODEL_CONTEXT_KEY);
}
