import { writable, type Writable } from "svelte/store";
import type {
  MessageBroker,
  MessageConsumer,
} from "$common-webviews/src/common/utils/message-broker";
import throttle from "lodash/throttle";
import {
  WebViewMessageType,
  type WebViewMessage,
} from "$vscode/src/webview-providers/webview-messages";
import type { Rule } from "@augment-internal/sidecar-libs/src/chat/chat-types";
import { RulesParser } from "@augment-internal/sidecar-libs/src/utils/rules-parser";
import type {
  AutoImportRulesOption,
  AutoImportRulesOptionsResponse,
} from "@augment-internal/sidecar-libs/src/webview-messages/message-types/rules-messages";
import { ExtensionClientRules } from "../extension-client-rules";

/**
 * Model for managing rules data
 */
export class RulesModel implements MessageConsumer {
  private _rulesFiles: Writable<Rule[]> = writable([]);
  private _loading: Writable<boolean> = writable(true);
  private _extensionClientRules: ExtensionClientRules;

  constructor(
    private readonly _msgBroker: MessageBroker,
    private readonly includeGuidelines: boolean = true,
  ) {
    this._extensionClientRules = new ExtensionClientRules(this._msgBroker);
    this.requestRules();
  }

  /**
   * Handle messages from the extension
   */
  handleMessageFromExtension(e: MessageEvent<WebViewMessage>): boolean {
    // TODO: deprecate this when we get the sidecar filewatcher to work
    if (e.data && e.data.type === WebViewMessageType.getRulesListResponse) {
      this._rulesFiles.set(e.data.data);
      this._loading.set(false);
      return true;
    }
    return false;
  }

  // Throttled version of _requestRulesInternal to prevent excessive API calls
  private _requestRulesThrottled = throttle(
    async () => {
      this._loading.set(true);
      try {
        const rules = await this._extensionClientRules.getRulesList(this.includeGuidelines);
        this._rulesFiles.set(rules);
      } catch (error) {
        console.error("Failed to get rules list:", error);
      } finally {
        this._loading.set(false);
      }
    },
    250, // 250ms throttle
    { leading: true, trailing: true },
  );

  /**
   * Request the list of rules from the extension (throttled to prevent excessive calls)
   */
  async requestRules(): Promise<void> {
    return this._requestRulesThrottled();
  }

  /**
   * Create a new rule via sidecar
   */
  async createRule(ruleName: string): Promise<{ path: string; repoRoot: string } | null> {
    try {
      const createdRule = await this._extensionClientRules.createRule(ruleName);

      // Refresh the rules list after successful creation
      await this.requestRules();

      return createdRule;
    } catch (error) {
      console.error("Failed to create rule:", error);
      throw error;
    }
  }

  /**
   * Get workspace root from sidecar
   */
  async getWorkspaceRoot(): Promise<string> {
    try {
      return await this._extensionClientRules.getWorkspaceRoot();
    } catch (error) {
      console.error("Failed to get workspace root:", error);
      return "";
    }
  }

  async updateRuleContent(rule: Rule) {
    const newText = RulesParser.formatRuleFileForMarkdown(rule);
    try {
      await this._extensionClientRules.updateRuleFile(rule.path, newText);
    } catch (error) {
      console.error("Failed to update rule file:", error);
    }
    // TODO: remove this when sidecar file watcher works
    await this.requestRules();
  }

  async deleteRule(path: string): Promise<void> {
    try {
      await this._extensionClientRules.deleteRule(path, true);
      // TODO: remove this when sidecar file watcher works
      await this.requestRules();
    } catch (error) {
      console.error("Failed to delete rule:", error);
      throw error;
    }
  }

  /**
   * Process selected paths by sending them to sidecar
   */
  async processSelectedPaths(selectedPaths: string[]): Promise<{
    importedRulesCount: number;
    directoryOrFile?: string;
    errors?: string[];
  }> {
    try {
      const result = await this._extensionClientRules.processSelectedPaths(selectedPaths, true);

      // TODO: remove this when sidecar file watcher works
      await this.requestRules();

      return result;
    } catch (error) {
      console.error("Failed to process selected paths:", error);
      throw error;
    }
  }

  /**
   * Get available auto-import options from sidecar
   */
  async getAutoImportOptions(): Promise<AutoImportRulesOptionsResponse> {
    return await this._extensionClientRules.getAutoImportOptions();
  }

  /**
   * Process auto-import rules selection and perform import
   */
  async processAutoImportSelection(selectedOption: AutoImportRulesOption): Promise<{
    importedRulesCount: number;
    duplicatesCount: number;
    totalAttempted: number;
    source: string;
  }> {
    try {
      // Send selection to sidecar and get results
      const result = await this._extensionClientRules.processAutoImportSelection(
        selectedOption.label,
      );

      // TODO: remove this when sidecar file watcher works
      await this.requestRules();

      // Return the detailed import information
      return result;
    } catch (error) {
      console.error("Failed to process auto-import selection:", error);
      throw error;
    }
  }

  /**
   * Get the rules files store
   */
  getCachedRules() {
    return this._rulesFiles;
  }

  /**
   * Get the loading state store
   */
  getLoading() {
    return this._loading;
  }
}
