import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import { get } from "svelte/store";
import { SwarmModeModel } from "./swarm-mode-model";
import { type MessageBroker } from "$common-webviews/src/common/utils/message-broker";
import { AgentWebViewMessageType } from "@augment-internal/sidecar-libs/src/webview-messages/message-types/agent-messages";
import { DEFAULT_SWARM_MODE_SETTINGS } from "@augment-internal/sidecar-libs/src/agent/agent-edit-types";

// Mock the message broker
const mockMsgBroker = {
  registerConsumer: vi.fn(),
  sendToSidecar: vi.fn(),
} as unknown as MessageBroker;

describe("SwarmModeModel", () => {
  let swarmModeModel: SwarmModeModel;

  beforeEach(() => {
    vi.useFakeTimers();
    vi.clearAllMocks();

    // Mock successful sidecar responses
    (mockMsgBroker.sendToSidecar as any).mockResolvedValue({
      data: DEFAULT_SWARM_MODE_SETTINGS,
    });
  });

  afterEach(() => {
    swarmModeModel?.dispose();
    vi.useRealTimers();
  });

  it("should initialize with default settings", async () => {
    swarmModeModel = new SwarmModeModel(mockMsgBroker);

    // Wait for initialization (just run pending timers once)
    await vi.runOnlyPendingTimersAsync();

    expect(get(swarmModeModel.getCurrentSettings)).toEqual(DEFAULT_SWARM_MODE_SETTINGS);
  });

  it("should start polling for updates", () => {
    swarmModeModel = new SwarmModeModel(mockMsgBroker);

    // Fast-forward time to trigger polling
    vi.advanceTimersByTime(2000);

    // Should have called sendToSidecar for polling (in addition to initialization)
    expect(mockMsgBroker.sendToSidecar).toHaveBeenCalledWith({
      type: AgentWebViewMessageType.getSwarmModeSettings,
    });
  });

  it("should update settings when polling detects changes", async () => {
    swarmModeModel = new SwarmModeModel(mockMsgBroker);

    // Wait for initialization
    await vi.runOnlyPendingTimersAsync();

    // Clear previous calls
    vi.clearAllMocks();

    // Mock a different response for the next poll
    const updatedSettings = { ...DEFAULT_SWARM_MODE_SETTINGS, enabled: false };
    (mockMsgBroker.sendToSidecar as any).mockResolvedValue({
      data: updatedSettings,
    });

    // Trigger polling
    vi.advanceTimersByTime(2000);
    await vi.runOnlyPendingTimersAsync();

    // Settings should be updated
    expect(get(swarmModeModel.getCurrentSettings)).toEqual(updatedSettings);
  });

  it("should stop polling when disposed", () => {
    const clearIntervalSpy = vi.spyOn(global, "clearInterval");

    swarmModeModel = new SwarmModeModel(mockMsgBroker);
    swarmModeModel.dispose();

    expect(clearIntervalSpy).toHaveBeenCalled();
  });

  it("should update settings via sidecar", async () => {
    swarmModeModel = new SwarmModeModel(mockMsgBroker);

    const updates = { enabled: false };
    await swarmModeModel.updateSettings(updates);

    expect(mockMsgBroker.sendToSidecar).toHaveBeenCalledWith({
      type: AgentWebViewMessageType.updateSwarmModeSettings,
      data: updates,
    });
  });

  it("should handle polling errors gracefully", async () => {
    const consoleSpy = vi.spyOn(console, "warn").mockImplementation(() => {});

    swarmModeModel = new SwarmModeModel(mockMsgBroker);

    // Wait for initialization
    await vi.runOnlyPendingTimersAsync();

    // Mock an error for the next poll
    (mockMsgBroker.sendToSidecar as any).mockRejectedValue(new Error("Network error"));

    // Trigger polling
    vi.advanceTimersByTime(2000);
    await vi.runOnlyPendingTimersAsync();

    // Should log warning but not crash
    expect(consoleSpy).toHaveBeenCalledWith(
      "Failed to check for swarm mode settings updates:",
      expect.any(Error),
    );

    consoleSpy.mockRestore();
  });

  it("should enable swarm mode", async () => {
    swarmModeModel = new SwarmModeModel(mockMsgBroker);

    await swarmModeModel.setEnabled(true);

    expect(mockMsgBroker.sendToSidecar).toHaveBeenCalledWith({
      type: AgentWebViewMessageType.updateSwarmModeSettings,
      data: { enabled: true },
    });
  });

  it("should disable swarm mode", async () => {
    swarmModeModel = new SwarmModeModel(mockMsgBroker);

    await swarmModeModel.setEnabled(false);

    expect(mockMsgBroker.sendToSidecar).toHaveBeenCalledWith({
      type: AgentWebViewMessageType.updateSwarmModeSettings,
      data: { enabled: false },
    });
  });

  it("should reset to default settings", async () => {
    swarmModeModel = new SwarmModeModel(mockMsgBroker);

    await swarmModeModel.resetToDefaults();

    expect(mockMsgBroker.sendToSidecar).toHaveBeenCalledWith({
      type: AgentWebViewMessageType.updateSwarmModeSettings,
      data: DEFAULT_SWARM_MODE_SETTINGS,
    });
  });
});
