import { get, writable, type Writable } from "svelte/store";
import { type MessageBroker } from "$common-webviews/src/common/utils/message-broker";
import {
  AgentWebViewMessageType,
  type GetSoundSettingsRequest,
  type GetSoundSettingsResponse,
  type UpdateSoundSettingsRequest,
} from "@augment-internal/sidecar-libs/src/webview-messages/message-types/agent-messages";
import {
  DEFAULT_SOUND_SETTINGS,
  type SoundSettings,
} from "@augment-internal/sidecar-libs/src/agent/agent-edit-types";

import { SoundEffect, soundUtils } from "$common-webviews/src/common/utils/sound-utils";

/**
 * Model for managing sound settings via sidecar
 */
export class SoundModel {
  public static key = "soundModel";
  private _soundSettings: Writable<SoundSettings> = writable(DEFAULT_SOUND_SETTINGS);
  private _isLoaded = false;

  constructor(private readonly _msgBroker: MessageBroker) {
    // Initialize sound settings when the model is created
    this.initialize();
  }

  /**
   * Refresh sound settings from the sidecar
   */
  async refreshSettings(): Promise<void> {
    try {
      const response = await this._msgBroker.sendToSidecar<
        GetSoundSettingsRequest,
        GetSoundSettingsResponse
      >({
        type: AgentWebViewMessageType.getSoundSettings,
      });

      if (response.data) {
        this._soundSettings.set(response.data);
      }
    } catch (error) {
      console.warn("Failed to refresh sound settings:", error);
      // Keep existing settings on error
    }
  }

  /*
   * Unlock audio by playing a sound at 0 volume
   * This satisfies browser autoplay policies that require user interaction
   */
  async unlockSound(): Promise<void> {
    if (!get(this._soundSettings).enabled) {
      return;
    }
    soundUtils.unlockSoundForConfig(get(this._soundSettings));
  }

  /**
   * Play the agent completion sound
   */
  async playAgentComplete(): Promise<void> {
    const currentSettings = get(this._soundSettings);
    await soundUtils.playSound(SoundEffect.AGENT_COMPLETE, currentSettings);
  }

  /**
   * Get the current sound settings value
   */
  get getCurrentSettings(): Writable<SoundSettings> {
    return this._soundSettings;
  }

  /**
   * Initialize the sound settings by loading from the sidecar
   */
  async initialize(): Promise<void> {
    if (this._isLoaded) {
      return;
    }

    try {
      const response = await this._msgBroker.sendToSidecar<
        GetSoundSettingsRequest,
        GetSoundSettingsResponse
      >({
        type: AgentWebViewMessageType.getSoundSettings,
      });

      if (response.data) {
        this._soundSettings.set(response.data);
      }
      this._isLoaded = true;
    } catch (error) {
      console.warn("Failed to load sound settings, using defaults:", error);
      this._soundSettings.set(DEFAULT_SOUND_SETTINGS);
      this._isLoaded = true;
    }
  }

  /**
   * Update sound settings via sidecar
   */
  async updateSettings(updates: Partial<SoundSettings>): Promise<void> {
    try {
      // Send update to sidecar (no response expected)
      await this._msgBroker.sendToSidecar<UpdateSoundSettingsRequest, any>({
        type: AgentWebViewMessageType.updateSoundSettings,
        data: updates,
      } as UpdateSoundSettingsRequest);

      // Update local store
      this._soundSettings.update((current) => {
        const newSettings = { ...current, ...updates };
        return newSettings;
      });
    } catch (error) {
      console.error("Failed to update sound settings:", error);
      throw error;
    }
  }

  /**
   * Enable or disable sound effects
   */
  async setEnabled(enabled: boolean): Promise<void> {
    await this.updateSettings({ enabled });
  }

  /**
   * Set the volume level (0.0 to 1.0)
   */
  async setVolume(volume: number): Promise<void> {
    const clampedVolume = Math.max(0, Math.min(1, volume));
    await this.updateSettings({ volume: clampedVolume });
  }

  /**
   * Reset to default settings
   */
  async resetToDefaults(): Promise<void> {
    await this.updateSettings(DEFAULT_SOUND_SETTINGS);
  }

  /**
   * Update whether sound effects are enabled (legacy method for compatibility)
   * @param enabled Whether sound effects should be enabled
   */
  updateEnabled(enabled: boolean) {
    this.setEnabled(enabled).catch((error) => {
      console.error("Failed to update enabled setting:", error);
    });
  }

  /**
   * Update the volume level (legacy method for compatibility)
   * @param volume The volume level (0.0 to 1.0)
   */
  updateVolume(volume: number) {
    this.setVolume(volume).catch((error) => {
      console.error("Failed to update volume setting:", error);
    });
  }

  dispose = (): void => {
    soundUtils.disposeSounds();
  };
}
