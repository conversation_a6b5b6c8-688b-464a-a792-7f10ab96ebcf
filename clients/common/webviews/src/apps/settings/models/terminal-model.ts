import type { HostInterface } from "$common-webviews/src/common/hosts/host-types";
import type { MessageConsumer } from "$common-webviews/src/common/utils/message-broker";
import {
  WebViewMessageType,
  type WebViewMessage,
} from "$vscode/src/webview-providers/webview-messages";
import { writable, type Writable } from "svelte/store";

import type { TerminalSettings } from "@augment-internal/sidecar-libs/src/tools/tool-types";

/**
 * Model for managing terminal settings
 */
export class TerminalModel implements MessageConsumer {
  private _terminalSettings: Writable<TerminalSettings> = writable({
    supportedShells: [],
    selectedShell: undefined,
    startupScript: undefined,
  });

  constructor(private readonly _host: HostInterface) {
    // Request terminal settings when the model is created
    this.requestTerminalSettings();
  }

  /**
   * Handle messages from the extension
   */
  handleMessageFromExtension(e: MessageEvent<WebViewMessage>): boolean {
    const msg = e.data;
    switch (msg.type) {
      case WebViewMessageType.terminalSettingsResponse:
        // Update terminal settings with data from the extension
        this._terminalSettings.set(msg.data);
        return true;
    }
    return false;
  }

  /**
   * Get the terminal settings store
   */
  getTerminalSettings() {
    return this._terminalSettings;
  }

  /**
   * Request terminal settings from the extension
   */
  requestTerminalSettings() {
    this._host.postMessage({
      type: WebViewMessageType.getTerminalSettings,
    });
  }

  /**
   * Update the selected shell
   * @param shellName The name of the shell to select
   */
  updateSelectedShell(friendlyShellName: string) {
    // Update the local store
    this._terminalSettings.update((settings) => ({
      ...settings,
      selectedShell: friendlyShellName,
    }));

    // Send the update to the extension
    this._host.postMessage({
      type: WebViewMessageType.updateTerminalSettings,
      data: {
        selectedShell: friendlyShellName,
      },
    });
  }

  /**
   * Update the startup script
   * @param script The startup script content
   */
  updateStartupScript(script: string) {
    // Update the local store
    this._terminalSettings.update((settings) => ({
      ...settings,
      startupScript: script,
    }));

    // Send the update to the extension
    this._host.postMessage({
      type: WebViewMessageType.updateTerminalSettings,
      data: {
        startupScript: script,
      },
    });
  }
}
