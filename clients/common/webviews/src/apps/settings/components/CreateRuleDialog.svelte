<script lang="ts">
  import { createEventDispatcher } from "svelte";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import TextFieldAugment from "$common-webviews/src/design-system/components/TextFieldAugment.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import CalloutAugment from "$common-webviews/src/design-system/components/CalloutAugment.svelte";
  import ModalAugment from "$common-webviews/src/design-system/components/ModalAugment.svelte";
  import ExclamationTriangle from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/triangle-exclamation.svg?component";

  const dispatch = createEventDispatcher<{
    create: string;
    cancel: void;
  }>();

  export let show = false;
  export let errorMessage = "";
  let ruleName = "";
  let inputElement: HTMLInputElement | undefined = undefined;
  let isLoading = false;

  // Focus the input when dialog is shown
  $: if (show && inputElement) {
    setTimeout(() => inputElement?.focus(), 100);
  }

  function handleSubmit() {
    if (ruleName.trim() && !isLoading) {
      isLoading = true;
      dispatch("create", ruleName.trim());
      // Note: isLoading will be reset when the dialog is hidden or error is set
    }
  }

  function handleCancel() {
    if (!isLoading) {
      dispatch("cancel");
      ruleName = "";
    }
  }

  // Reset loading state when dialog is hidden or when there's an error
  $: if (!show || errorMessage) {
    isLoading = false;
  }

  // Reset form when dialog is hidden successfully (no error)
  $: if (!show && !errorMessage) {
    ruleName = "";
  }

  function handleModalKeydown(event: CustomEvent<KeyboardEvent>) {
    if (isLoading) return; // Prevent actions while loading

    if (event.detail.key === "Enter") {
      event.detail.preventDefault();
      handleSubmit();
    }
  }

  function handleInputKeydown(event: KeyboardEvent) {
    if (isLoading) return; // Prevent actions while loading

    if (event.key === "Enter") {
      event.preventDefault();
      handleSubmit();
    } else if (event.key === "Escape") {
      event.preventDefault();
      handleCancel();
    }
  }
</script>

<ModalAugment
  {show}
  title="Create New Rule"
  ariaLabelledBy="dialog-title"
  preventBackdropClose={isLoading}
  preventEscapeClose={isLoading}
  on:cancel={handleCancel}
  on:keydown={handleModalKeydown}
>
  <svelte:fragment slot="body">
    <TextAugment size={2} color="secondary">
      Enter a name for the new rule file (e.g., architecture.md):
    </TextAugment>

    <TextFieldAugment
      bind:value={ruleName}
      bind:textInput={inputElement}
      placeholder="rule-name.md"
      disabled={isLoading}
      on:keydown={handleInputKeydown}
    />

    {#if errorMessage}
      <CalloutAugment variant="soft" color="error" size={1}>
        <ExclamationTriangle slot="icon" />
        {errorMessage}
      </CalloutAugment>
    {/if}
  </svelte:fragment>

  <div slot="footer">
    <ButtonAugment variant="solid" color="neutral" disabled={isLoading} on:click={handleCancel}>
      Cancel
    </ButtonAugment>
    <ButtonAugment
      variant="solid"
      color="accent"
      disabled={!ruleName.trim() || isLoading}
      loading={isLoading}
      on:click={handleSubmit}
    >
      {isLoading ? "Creating..." : "Create"}
    </ButtonAugment>
  </div>
</ModalAugment>
