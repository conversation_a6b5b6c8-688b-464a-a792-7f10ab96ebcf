<script lang="ts">
  import { createEventDispatcher } from "svelte";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import CalloutAugment from "$common-webviews/src/design-system/components/CalloutAugment.svelte";
  import ModalAugment from "$common-webviews/src/design-system/components/ModalAugment.svelte";
  import DropdownMenuAugment from "$common-webviews/src/design-system/components/DropdownMenuAugment";

  import ExclamationTriangle from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/triangle-exclamation.svg?component";
  import CheckCircle from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/circle-check.svg?component";
  import ChevronDown from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/chevron-down.svg?component";
  import type { AutoImportRulesOption } from "@augment-internal/sidecar-libs/src/webview-messages/message-types/rules-messages";
  import type { Readable } from "svelte/store";

  const dispatch = createEventDispatcher<{
    select: AutoImportRulesOption;
    cancel: void;
  }>();

  export let show = false;
  export let options: AutoImportRulesOption[] = [];
  export let isLoading = false;
  export let errorMessage = "";
  export let successMessage = "";

  $: defaultOption = options.length > 0 ? options[0] : null;

  let selectedOption: AutoImportRulesOption | null = defaultOption;
  let focusedIndex: Readable<number | undefined> | undefined = undefined;
  let requestClose: () => void = () => {};

  function handleSelect(option: AutoImportRulesOption) {
    selectedOption = option;
    requestClose();
  }

  function handleConfirm() {
    if (selectedOption && !isLoading) {
      dispatch("select", selectedOption);
    }
  }

  function handleCancel() {
    if (!isLoading) {
      dispatch("cancel");
      selectedOption = defaultOption;
    }
  }

  // Reset selection when dialog is hidden
  $: if (show) {
    selectedOption = defaultOption;
  }

  function handleWindowKeydown(event: KeyboardEvent) {
    if (!show || isLoading) return; // Only handle when dialog is open and not loading

    if (event.key === "Escape") {
      event.preventDefault();
      handleCancel();
    } else if (event.key === "Enter" && selectedOption) {
      event.preventDefault();
      handleConfirm();
    }
  }
</script>

<svelte:window on:keydown={handleWindowKeydown} />

<ModalAugment
  {show}
  title="Auto Import Rules"
  ariaLabelledBy="dialog-title"
  preventBackdropClose={isLoading}
  preventEscapeClose={isLoading}
  on:cancel={handleCancel}
>
  <div slot="body" class="c-auto-import-rules-dialog">
    {#if options.length === 0}
      <input type="text" value="No existing rules found" readonly class="c-dropdown-input" />
    {:else}
      <TextAugment size={2} color="secondary"
        >Select existing rules to auto import to .augment/rules</TextAugment
      >

      <DropdownMenuAugment.Root
        bind:requestClose
        bind:focusedIndex
        triggerOn={options.length === 0 ? [] : undefined}
      >
        <DropdownMenuAugment.Trigger>
          <div class="c-dropdown-trigger">
            <input
              type="text"
              value={selectedOption ? selectedOption.label : "Existing rules"}
              readonly
              class="c-dropdown-input"
            />
            <ChevronDown class="c-dropdown-chevron" />
          </div>
        </DropdownMenuAugment.Trigger>
        <DropdownMenuAugment.Content align="start" side="bottom">
          {#each options as option}
            <DropdownMenuAugment.Item
              onSelect={() => handleSelect(option)}
              highlight={selectedOption?.label === option.label}
            >
              {option.label}
            </DropdownMenuAugment.Item>
          {/each}
          {#if $focusedIndex !== undefined || selectedOption}
            <DropdownMenuAugment.Separator />
            <DropdownMenuAugment.Label>
              {$focusedIndex !== undefined
                ? options[$focusedIndex].description
                : selectedOption?.description}
            </DropdownMenuAugment.Label>
          {/if}
        </DropdownMenuAugment.Content>
      </DropdownMenuAugment.Root>

      {#if errorMessage}
        <CalloutAugment variant="soft" color="error" size={1}>
          <ExclamationTriangle slot="icon" />
          {errorMessage}
        </CalloutAugment>
      {/if}

      {#if successMessage}
        <CalloutAugment variant="soft" color="success" size={1}>
          <CheckCircle slot="icon" />
          {successMessage}
        </CalloutAugment>
      {/if}
    {/if}
  </div>

  <div slot="footer">
    <ButtonAugment variant="solid" color="neutral" disabled={isLoading} on:click={handleCancel}>
      Cancel
    </ButtonAugment>
    {#if options.length > 0}
      <ButtonAugment
        color="accent"
        variant="solid"
        disabled={!selectedOption || isLoading}
        loading={isLoading}
        on:click={handleConfirm}
      >
        {isLoading ? "Importing..." : "Import "}
      </ButtonAugment>
    {/if}
  </div>
</ModalAugment>

<style>
  .c-dropdown-trigger {
    position: relative;
    width: 100%;
    display: flex;
    align-items: center;
  }

  .c-dropdown-input {
    width: 100%;
    min-width: 350px;
    padding: var(--ds-spacing-1) var(--ds-spacing-1);
    padding-right: var(--ds-spacing-6);
    background-color: var(--vscode-input-background);
    border: 1px solid var(--augment-border-color);
    border-radius: var(--ds-radius-2);
    color: var(--vscode-input-foreground);
    font-size: var(--ds-font-size-1);
    cursor: pointer;
    outline: none;
  }

  .c-dropdown-trigger :global(.c-dropdown-chevron) {
    position: absolute;
    right: var(--ds-spacing-2);
    pointer-events: none;
    color: var(--vscode-input-foreground);
    opacity: 0.7;
  }

  .c-dropdown-input:disabled {
    cursor: not-allowed;
    opacity: 0.5;
    background-color: var(--vscode-input-background);
  }

  .c-auto-import-rules-dialog {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-2);
  }
  /* Set minimum width on dropdown content to prevent width changes when label + separator appear */
  .c-auto-import-rules-dialog :global(.l-dropdown-menu-augment__contents) {
    min-width: 350px;
  }
</style>
