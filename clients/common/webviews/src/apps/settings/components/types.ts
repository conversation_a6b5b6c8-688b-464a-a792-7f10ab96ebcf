import type {
  ToolIdentifier,
  ToolApprovalConfig,
} from "@augment-internal/sidecar-libs/src/tools/tool-types";

export type MCPEnvVarEntry = {
  id: string;
  key: string;
  value: string;
};
export type MCPCardModeType = "add" | "addJson" | "addRemote" | "edit" | "view";

// Callback type for when tool approval config change is made in UI.
export type OnToolApprovalConfigChange = (
  toolId: ToolIdentifier,
  approvalConfig: ToolApprovalConfig,
) => void;
