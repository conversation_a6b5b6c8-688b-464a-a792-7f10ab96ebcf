<script lang="ts">
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import CollapsibleAugment from "$common-webviews/src/design-system/components/CollapsibleAugment/CollapsibleAugment.svelte";
  import CollapseButtonAugment from "$common-webviews/src/design-system/components/CollapsibleAugment/CollapseButtonAugment.svelte";
  import SettingsCard from "./SettingsCard.svelte";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import TextFieldAugment from "$common-webviews/src/design-system/components/TextFieldAugment.svelte";
  import BadgeAugment from "$common-webviews/src/design-system/components/BadgeAugment";
  import Plus from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/plus.svg?component";
  export let onMCPServerAdd: ((server: Omit<MCPServer, "id">) => void) | undefined = undefined;
  export let servers: MCPServer[] = [];

  // Import MCPServer type
  import type { MCPServer, MCPServerStdio } from "$vscode/src/webview-providers/webview-messages";
  import { PARTNER_MCP_SERVERS } from "@augment-internal/sidecar-libs/src/tools/partner-remote-mcp";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";

  // Helper type for creating new stdio servers
  type MCPServerStdioWithoutId = Omit<MCPServerStdio, "id">;

  // Type for user input configuration
  type UserInputConfig = {
    label: string;
    description?: string;
    placeholder?: string;
    correspondingArg?: string;
    type: "argument" | "environmentVariable";
    envVarName?: string;
    defaultValue?: string;
  };
  type MCPInstallOption = {
    label: string;
    description?: string;
    command: string;
    args?: string[];
    requiresApiKey?: boolean;
    userInput?: UserInputConfig[];
  };

  // MCP options data
  const mcpOptions: Array<MCPInstallOption> = [
    {
      label: PARTNER_MCP_SERVERS.REDIS,
      description: "Real-time data platform for building fast apps",
      command: "uvx",
      args: ["--from", "git+https://github.com/redis/mcp-redis.git", "redis-mcp-server", "--url"],
      userInput: [
        {
          label: "Redis connection URL",
          description: "Enter your connection URL (redis://localhost:6379/0)",
          placeholder:
            "rediss://<USERNAME>:<PASSWORD>@<HOST>:<PORT>?ssl_cert_reqs=required&ssl_ca_certs=<PATH_TO_CERT>", // pragma: allowlist secret
          correspondingArg: "--url",
          type: "argument",
        },
      ],
    },
    {
      label: PARTNER_MCP_SERVERS.MONGODB,
      description: "Optimize database queries and performance.",
      command: "npx",
      args: ["-y", "mongodb-mcp-server", "--connectionString"],
      userInput: [
        {
          label: "MongoDB Connection String",
          description: "Enter your MongoDB connection string",
          placeholder: "********************************:port/database", // pragma: allowlist secret
          correspondingArg: "--connectionString",
          type: "argument",
        },
      ],
    },
    {
      label: PARTNER_MCP_SERVERS.CIRCLECI,
      description: "Debug builds and improve CI/CD pipelines.",
      command: "npx",
      args: ["-y", "@circleci/mcp-server-circleci"],
      userInput: [
        {
          label: "CircleCI Token",
          description: "Enter your CircleCI token",
          placeholder: "YOUR_CIRCLE_CI_TOKEN",
          type: "environmentVariable",
          envVarName: "CIRCLECI_TOKEN",
        },
        {
          label: "Base URL",
          description: "Enter the base URL for your CircleCI instance",
          placeholder: "https://circleci.com",
          defaultValue: "https://circleci.com",
          type: "environmentVariable",
          envVarName: "CIRCLECI_BASE_URL",
        },
      ],
    },
    // Disabling Twilio for now as they 197 tools.
    // They are iterating on it rapidly and it seems they'll reduce the number of tools soon.
    // {
    //   value: "twilio",
    //   label: "Twilio",
    //   description: "Send and receive SMS messages.",
    //   command: "npx",
    //   args: ["-y", "@twilio-alpha/mcp"],
    //   userInput: [
    //     {
    //       label: "Twilio connection string",
    //       description: "Account SID, API Key, and API Secret",
    //       placeholder: "YOUR_ACCOUNT_SID/YOUR_API_KEY:YOUR_API_SECRET",
    //       type: "argument",
    //     },
    //   ],
    // },
    {
      label: "Context 7",
      description: "Package documentation",
      command: "npx -y @upstash/context7-mcp@latest",
    },
    {
      label: "Playwright",
      description: "Browser automation",
      command: "npx -y @playwright/mcp@latest",
    },
    {
      label: "Sequential thinking",
      description: "Think through complex problems step-by-step.",
      command: "npx -y @modelcontextprotocol/server-sequential-thinking",
    },
  ];

  const COLLAPSED_STATE_KEY = "easyMCPInstall.collapsed";

  let collapsed: boolean = false;
  let isInitialized = false; // Flag to prevent saving during initialization
  // Load collapsed state from localStorage on mount
  $: {
    const savedState = localStorage.getItem(COLLAPSED_STATE_KEY);

    if (savedState !== null) {
      try {
        collapsed = JSON.parse(savedState);
      } catch (error) {
        // Invalid JSON, reset to default
        localStorage.removeItem(COLLAPSED_STATE_KEY);
      }
    }
    isInitialized = true; // Mark as initialized after loading
  }

  $: if (typeof window !== "undefined" && isInitialized) {
    localStorage.setItem(COLLAPSED_STATE_KEY, JSON.stringify(collapsed));
  }

  let showingUserInput: string | null = null;
  let userInputValues: Record<string, string> = {};
  let userInputRefs: Record<string, HTMLInputElement> = {};

  // Function to handle individual MCP installation
  function handleIndividualInstall(option: MCPInstallOption) {
    const isAlreadyInstalled = servers.some((server) => server.name === option.label);
    if (isAlreadyInstalled) return;

    if (option.userInput && option.userInput.length > 0) {
      userInputValues = {};
      option.userInput.forEach((input, index) => {
        let inputKey: string;
        if (input.type === "environmentVariable" && input.envVarName) {
          inputKey = input.envVarName;
        } else if (input.correspondingArg) {
          inputKey = input.correspondingArg;
        } else {
          inputKey = `input_${index}`;
        }
        userInputValues[inputKey] = input.defaultValue || "";
      });

      showingUserInput = option.label;
      return;
    }

    const mcpServer: MCPServerStdioWithoutId = {
      type: "stdio",
      name: option.label,
      command: option.command,
      arguments: "", // Keep empty for backward compatibility
      useShellInterpolation: true, // New servers use shell interpolation
    };

    // Install the individual server
    if (onMCPServerAdd) onMCPServerAdd(mcpServer);
  }

  function handleUserInputSubmit(option: MCPInstallOption) {
    if (!option.userInput) return;

    // Validate all required inputs are filled
    for (let i = 0; i < option.userInput.length; i++) {
      const input = option.userInput[i];
      let inputKey: string;
      if (input.type === "environmentVariable" && input.envVarName) {
        inputKey = input.envVarName;
      } else if (input.correspondingArg) {
        inputKey = input.correspondingArg;
      } else {
        inputKey = `input_${i}`;
      }

      const value = userInputValues[inputKey]?.trim();
      if (!value) {
        // Focus the first empty input
        const inputRef = userInputRefs[inputKey];
        if (inputRef) {
          inputRef.focus();
        }
        return;
      }
    }

    // Build command with user inputs
    let commandParts: string[] = [option.command];
    let envVars: Record<string, string> = {};

    if (option.args) {
      commandParts.push(...option.args);
    }

    // Process user inputs based on their type
    for (let i = 0; i < option.userInput.length; i++) {
      const input = option.userInput[i];
      let inputKey: string;
      if (input.type === "environmentVariable" && input.envVarName) {
        inputKey = input.envVarName;
      } else if (input.correspondingArg) {
        inputKey = input.correspondingArg;
      } else {
        inputKey = `input_${i}`;
      }

      const value = userInputValues[inputKey].trim();
      const quotedValue = `"${value}"`; // Add double quotes around user input

      if (input.type === "environmentVariable" && input.envVarName) {
        // Add to environment variables
        envVars[input.envVarName] = value;
      } else if (input.correspondingArg) {
        // Handle as command argument
        const argIndex = commandParts.indexOf(input.correspondingArg);
        if (argIndex !== -1) {
          // Insert the quoted value right after the corresponding argument
          commandParts.splice(argIndex + 1, 0, quotedValue);
        } else {
          // If argument not found in existing parts, append both arg and quoted value
          commandParts.push(input.correspondingArg, quotedValue);
        }
      } else {
        // If no correspondingArg, append at the end
        commandParts.push(quotedValue);
      }
    }

    // Create the MCP server configuration
    const mcpServer: MCPServerStdioWithoutId = {
      type: "stdio",
      name: option.label,
      command: commandParts.join(" "),
      arguments: "",
      useShellInterpolation: true,
      env: Object.keys(envVars).length > 0 ? envVars : undefined,
    };

    // Install the server
    if (onMCPServerAdd) {
      onMCPServerAdd(mcpServer);
    }

    // Reset the user input state
    showingUserInput = null;
    userInputValues = {};
  }

  // Function to cancel user input
  function cancelUserInput() {
    showingUserInput = null;
    userInputValues = {};
  }
</script>

<div class="mcp-install-wrapper">
  <CollapsibleAugment bind:collapsed>
    <div slot="header" class="mcp-install-header">
      <SettingsCard>
        <div slot="header-left" class="mcp-install-left">
          <CollapseButtonAugment />
          <TextAugment color="neutral" size={1} weight="light" class="card-title">
            Easy MCP Installation
          </TextAugment>
        </div>
      </SettingsCard>
    </div>

    <div class="mcp-install-content">
      <div class="mcp-list-container">
        {#each mcpOptions as option}
          <div class="mcp-service-item">
            <SettingsCard>
              <div slot="header-left" class="mcp-service-info">
                <div class="mcp-service-title">
                  <TextAugment size={1} weight="medium">{option.label}</TextAugment>
                </div>
                {#if option.description}
                  <TextAugment size={1} color="secondary">{option.description}</TextAugment>
                {/if}

                <!-- User Input - shown when this MCP is selected for user input -->
                {#if showingUserInput === option.label && option.userInput}
                  <div class="user-input-container">
                    {#each option.userInput as input, index}
                      {@const inputKey =
                        input.type === "environmentVariable" && input.envVarName
                          ? input.envVarName
                          : input.correspondingArg || `input_${index}`}
                      <div class="user-input-field">
                        <TextAugment size={1} weight="medium" color="neutral">
                          {input.label}
                        </TextAugment>
                        {#if input.description}
                          <TextAugment size={1} color="secondary">
                            {input.description}
                          </TextAugment>
                        {/if}
                        <TextFieldAugment
                          bind:value={userInputValues[inputKey]}
                          bind:textInput={userInputRefs[inputKey]}
                          placeholder={input.placeholder || ""}
                          size={1}
                          variant="surface"
                          on:keydown={(e) => {
                            if (e.key === "Enter") {
                              handleUserInputSubmit(option);
                            } else if (e.key === "Escape") {
                              cancelUserInput();
                            }
                          }}
                        />
                      </div>
                    {/each}
                    <div class="user-input-actions">
                      <ButtonAugment
                        variant="ghost-block"
                        color="accent"
                        size={1}
                        on:click={() => handleUserInputSubmit(option)}
                      >
                        Install
                      </ButtonAugment>
                      <ButtonAugment
                        variant="ghost-block"
                        color="neutral"
                        size={1}
                        on:click={cancelUserInput}
                      >
                        Cancel
                      </ButtonAugment>
                    </div>
                  </div>
                {/if}
              </div>
              <div slot="header-right" class="mcp-service-actions">
                {#if servers.some((server) => server.name === option.label)}
                  <div class="installed-indicator">
                    <BadgeAugment.Root color="success" size={1} variant="soft"
                      >Installed</BadgeAugment.Root
                    >
                  </div>
                {:else if showingUserInput !== option.label}
                  <IconButtonAugment
                    variant="ghost-block"
                    color="accent"
                    size={1}
                    on:click={() => handleIndividualInstall(option)}
                  >
                    <Plus />
                  </IconButtonAugment>
                {/if}
              </div>
            </SettingsCard>
          </div>
        {/each}
      </div>
    </div>
  </CollapsibleAugment>
</div>

<style>
  .mcp-install-wrapper {
    display: flex;
    flex-direction: column;
    width: 100%;
    max-width: 100%;
    overflow: hidden;
  }

  .mcp-install-header {
    width: 100%;
  }

  .mcp-install-left {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-2);
  }

  .mcp-install-content {
    display: flex;
    flex-direction: column;
    padding: var(--ds-spacing-2);
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
  }

  .mcp-list-container {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-1);
    height: 225px; /* Fixed height for 4 MCP items - prevents layout shift */
    overflow-y: auto;

    /* Custom scrollbar styling */
    scrollbar-width: thin;
    scrollbar-color: var(--ds-scrollbar-thumb, rgba(255, 255, 255, 0.2))
      var(--ds-scrollbar-track, transparent);
  }

  /* Webkit scrollbar styling */
  .mcp-list-container::-webkit-scrollbar {
    width: 8px;
  }

  .mcp-list-container::-webkit-scrollbar-track {
    background: var(--ds-scrollbar-track, transparent);
  }

  .mcp-list-container::-webkit-scrollbar-thumb {
    background-color: var(--ds-scrollbar-thumb, rgba(255, 255, 255, 0.2));
    border-radius: 4px;
  }

  .mcp-list-container::-webkit-scrollbar-thumb:hover {
    background-color: var(--ds-scrollbar-thumb-hover, rgba(255, 255, 255, 0.3));
  }

  .mcp-service-info {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-1);
    overflow: hidden;
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
  }

  .mcp-service-title {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-1);
  }

  .mcp-service-actions {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-2);
  }

  .installed-indicator {
    display: flex;
    align-items: center;
    cursor: default;
  }

  .user-input-container {
    margin-top: var(--ds-spacing-2);
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-2);
  }

  .user-input-field {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-1);
  }

  .user-input-actions {
    display: flex;
    gap: var(--ds-spacing-1);
    align-items: center;
  }
</style>
