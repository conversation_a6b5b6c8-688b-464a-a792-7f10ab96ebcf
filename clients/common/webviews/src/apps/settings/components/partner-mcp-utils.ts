import { PARTNER_MCP_SERVERS } from "@augment-internal/sidecar-libs/src/tools/partner-remote-mcp";
import { type ConfigBlock } from "../models/tool-config-model";
import Stripe<PERSON>ogo from "$common-webviews/src/design-system/icons/fontawesome/svgs/brands/stripe-s.svg?component";
import Sentry<PERSON>ogo from "$common-webviews/src/design-system/icons/fontawesome/svgs/brands/sentry.svg?component";
import RedisLogo from "$common-webviews/src/design-system/icons/fontawesome/svgs/brands/redis.svg?component";
import MongoD<PERSON>ogo from "$common-webviews/src/design-system/icons/fontawesome/svgs/brands/mongodb.svg?component";
import CircleCILogo from "$common-webviews/src/design-system/icons/fontawesome/svgs/brands/circleci.svg";
import { type MCPServer } from "$vscode/src/webview-providers/webview-messages";

export function getPartnerMCPDisplayDetails(tool: PARTNER_MCP_SERVERS) {
  switch (tool) {
    case PARTNER_MCP_SERVERS.STRIPE:
      return {
        displayName: "Stripe",
        icon: StripeLogo,
      };
    case PARTNER_MCP_SERVERS.SENTRY:
      return {
        displayName: "Sentry",
        icon: SentryLogo,
      };
    case PARTNER_MCP_SERVERS.REDIS:
      return {
        displayName: "Redis",
        icon: RedisLogo,
      };
    case PARTNER_MCP_SERVERS.MONGODB:
      return {
        displayName: "MongoDB",
        icon: MongoDBLogo,
      };
    case PARTNER_MCP_SERVERS.CIRCLECI:
      return {
        displayName: "CircleCI",
        icon: CircleCILogo,
      };
    default:
      return undefined;
  }
}

export function addPartnerMCPDisplayDetails(tool: ConfigBlock) {
  const details = getPartnerMCPDisplayDetails(tool.name as PARTNER_MCP_SERVERS);
  if (details) {
    tool.displayName = details.displayName;
    tool.icon = details.icon;
  }
  return tool;
}

export function filterOutPartnerMCPTools(servers: MCPServer[]) {
  return servers.filter(
    (server) =>
      server.name !== PARTNER_MCP_SERVERS.STRIPE && server.name !== PARTNER_MCP_SERVERS.SENTRY,
  );
}

export function filterPartnerMCPTools<T extends MCPServer | ConfigBlock>(servers: T[]): T[] {
  return servers.filter(
    (server) =>
      server.name === PARTNER_MCP_SERVERS.STRIPE || server.name === PARTNER_MCP_SERVERS.SENTRY,
  );
}
