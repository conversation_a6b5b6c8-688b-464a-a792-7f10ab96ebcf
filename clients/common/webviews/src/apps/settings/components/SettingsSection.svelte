<script lang="ts">
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import InfoCircledIcon from "$common-webviews/src/design-system/icons/info-circled.svelte";
  import { TooltipTriggerOn } from "$common-webviews/src/design-system/_primitives/TooltipAugment/types";

  export let title: string;
  export let tooltip: string | undefined = undefined;
</script>

<div class="section">
  <div class="section-heading">
    <div class="heading-with-tooltip">
      <TextAugment color="secondary" size={2} weight="medium">{title}</TextAugment>
      {#if tooltip}
        <div class="tooltip-icon">
          <TextTooltipAugment triggerOn={[TooltipTriggerOn.Hover]} content={tooltip}>
            <InfoCircledIcon />
          </TextTooltipAugment>
        </div>
      {/if}
    </div>
  </div>

  <slot />
</div>

<style>
  .section-heading {
    opacity: 0.5;
    border-bottom: 1px solid var(--ds-color-neutral-8);
    padding-bottom: var(--ds-spacing-2);
    margin-bottom: var(--ds-spacing-4);
  }

  .heading-with-tooltip {
    display: flex;
    align-items: center;
  }

  .tooltip-icon {
    display: inline-flex;
    align-items: center;
    margin-left: var(--ds-spacing-1);
    cursor: help;
    /* Improve vertical alignment */
    position: relative;
    top: 2px; /* Fine-tune vertical alignment */
    line-height: 0; /* Remove extra space around the icon */
  }
</style>
