<script lang="ts">
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import TextFieldAugment from "$common-webviews/src/design-system/components/TextFieldAugment.svelte";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import type { MCPEnvVarEntry } from "./types";
  import Plus from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/plus.svg?component";
  import TrashCan from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/trash-can.svg?component";

  // Props
  export let handleEnterEditMode: () => unknown;
  export let envVarEntries: MCPEnvVarEntry[] = [];

  // Environment variables management
  function addEmptyEnvironmentVariable() {
    handleEnterEditMode();
    envVarEntries = [
      ...envVarEntries,
      {
        id: crypto.randomUUID(),
        key: "",
        value: "",
      },
    ];
  }

  function removeEnvironmentVariable(id: string) {
    handleEnterEditMode();
    envVarEntries = envVarEntries.filter((entry) => entry.id !== id);
  }

  // Update the entry when a key or value changes
  function handleKeyChange(id: string, newKey: string) {
    const entryIndex = envVarEntries.findIndex((entry) => entry.id === id);
    if (entryIndex !== -1) {
      envVarEntries[entryIndex].key = newKey;
      envVarEntries = envVarEntries; // Trigger reactivity
    }
  }

  function handleValueChange(id: string, newValue: string) {
    const entryIndex = envVarEntries.findIndex((entry) => entry.id === id);
    if (entryIndex !== -1) {
      envVarEntries[entryIndex].value = newValue;
      envVarEntries = envVarEntries; // Trigger reactivity
    }
  }
</script>

<TextAugment size={1} weight="medium">Environment Variables</TextAugment>
<table class="env-vars-table">
  <tbody>
    {#if envVarEntries.length > 0}
      {#each envVarEntries as entry (entry.id)}
        <tr class="env-var-row">
          <td class="name-cell">
            <TextFieldAugment
              size={1}
              placeholder="Name"
              bind:value={entry.key}
              on:focus={handleEnterEditMode}
              on:change={() => handleKeyChange(entry.id, entry.key)}
              class="full-width"
            />
          </td>
          <td class="value-cell">
            <TextFieldAugment
              size={1}
              bind:value={entry.value}
              on:focus={handleEnterEditMode}
              on:change={() => handleValueChange(entry.id, entry.value)}
              placeholder="Value"
              class="full-width"
            />
          </td>
          <td class="action-cell">
            <TextTooltipAugment content="Remove">
              <ButtonAugment
                variant="ghost"
                color="neutral"
                on:focus={handleEnterEditMode}
                on:click={() => removeEnvironmentVariable(entry.id)}
                type="button"
                size={1}
              >
                <TrashCan slot="iconLeft" />
              </ButtonAugment>
            </TextTooltipAugment>
          </td>
        </tr>
      {/each}
    {/if}
  </tbody>
</table>

<div class="new-var-button-container">
  <ButtonAugment
    size={1}
    variant="soft"
    color="neutral"
    on:click={addEmptyEnvironmentVariable}
    type="button"
  >
    <Plus slot="iconLeft" />
    Variable
  </ButtonAugment>
</div>

<style>
  /* Table Styles */
  .env-vars-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0 var(--ds-spacing-2);
    table-layout: fixed; /* Ensures fixed table layout */
  }

  .env-var-row {
    vertical-align: middle;
    height: 100%;
  }

  /* Make TextFields take up full width of their cells */
  :global(.full-width) {
    width: 100%;
  }

  .name-cell {
    width: 50%;
    padding-right: var(--ds-spacing-1);
  }

  .value-cell {
    width: 50%;
    padding-right: var(--ds-spacing-1);
  }

  .action-cell {
    width: 40px; /* Fixed width for the action cell */
    vertical-align: middle;
    height: 100%;
  }

  .new-var-button-container {
    margin-top: var(--ds-spacing-2);
    margin-bottom: var(--ds-spacing-2);
    display: inline-flex;
    justify-content: center;
  }
  :global(.new-var-button-container .c-text) {
    display: flex;
    align-items: center;
  }
  .new-var-button-container :global(svg) {
    width: var(--ds-icon-size-1);
    height: var(--ds-icon-size-1);
    opacity: 0.8;
    fill: var(--icon-color, currentColor);
  }
</style>
