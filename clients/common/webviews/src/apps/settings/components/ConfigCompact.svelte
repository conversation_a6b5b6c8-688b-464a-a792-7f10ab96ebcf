<script lang="ts">
  import type { ConfigBlock } from "../models/tool-config-model";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";

  import SpinnerAugment from "$common-webviews/src/design-system/components/SpinnerAugment.svelte";

  import SettingsCard from "./SettingsCard.svelte";
  import BadgeAugment from "$common-webviews/src/design-system/components/BadgeAugment";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import { TooltipTriggerOn } from "$common-webviews/src/design-system/_primitives/TooltipAugment/types";
  import Disconnect from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/disconnect.svg?component";

  import type { OnToolApprovalConfigChange } from "./types";

  export let config: ConfigBlock;
  export let onAuthenticate: (url: string) => void;
  export let onRevokeAccess: (config: ConfigBlock) => void;
  // This will be used by follow-up PRs that set tool approval config, at which point this
  // can be converted to export let.
  export const onToolApprovalConfigChange: OnToolApprovalConfigChange = () => {};

  let isConnecting = false;
  let connectingTimeout: ReturnType<typeof setTimeout> | null = null;
  let disconnectButtonActive = false;

  // Reset connecting state when config changes (e.g., when tool becomes configured)
  $: if (config.isConfigured && isConnecting) {
    // Immediately reset the connecting state when the tool becomes configured
    isConnecting = false;
    if (connectingTimeout) {
      clearTimeout(connectingTimeout);
      connectingTimeout = null;
    }
  }

  // Handle connect button click - toggles between connecting and canceling states
  function handleConnectClick() {
    if (isConnecting) {
      // Cancel the connecting state
      isConnecting = false;
      if (connectingTimeout) {
        clearTimeout(connectingTimeout);
        connectingTimeout = null;
      }
    } else {
      // Set connecting state
      isConnecting = true;

      // The if condition above ensures authUrl is defined
      const authUrl = config.authUrl || "";
      onAuthenticate(authUrl);

      // Don't immediately update the status - let the polling handle it
      // This allows the spinner to remain visible until authentication completes

      // Reset connecting state after a timeout (60 seconds)
      // This is a fallback in case the user abandons the oauth flow.
      connectingTimeout = setTimeout(() => {
        isConnecting = false;
        connectingTimeout = null;
      }, 60000);
    }
  }
</script>

<div
  class="config-wrapper"
  role="group"
  aria-label="Connection status controls"
  on:mouseenter={() => (disconnectButtonActive = true)}
  on:mouseleave={() => (disconnectButtonActive = false)}
>
  <SettingsCard icon={config.icon} title={config.displayName}>
    <div slot="header-right">
      {#if !config.isConfigured && config.authUrl}
        <ButtonAugment
          variant="ghost-block"
          color={isConnecting ? "neutral" : "accent"}
          size={1}
          on:click={handleConnectClick}
        >
          <div class="connect-button-content">
            {#if isConnecting}
              <div class="connect-button-spinner">
                <SpinnerAugment size={1} useCurrentColor={true} />
              </div>
              <span>Cancel</span>
            {:else}
              <span>Connect</span>
            {/if}
          </div>
        </ButtonAugment>
      {:else if config.isConfigured}
        <div class="status-controls">
          <div class="icon-container">
            <div class="connection-status">
              <div class="icon-button-wrapper" class:active={disconnectButtonActive}>
                <TextTooltipAugment triggerOn={[TooltipTriggerOn.Hover]} content="Revoke Access">
                  <IconButtonAugment
                    color="neutral"
                    variant="ghost"
                    size={1}
                    on:click={() => onRevokeAccess(config)}
                  >
                    <Disconnect />
                  </IconButtonAugment>
                </TextTooltipAugment>
              </div>
            </div>
            <BadgeAugment.Root color="success" size={1} variant="soft">Connected</BadgeAugment.Root>
          </div>
        </div>
      {/if}
    </div>
  </SettingsCard>

  {#if config.showStatus}
    <div class="status-message {config.statusType}">
      {config.statusMessage}
    </div>
  {/if}
</div>

<style>
  .icon-container :global(svg) {
    fill: var(--icon-color, currentColor);
    height: 16px;
    aspect-ratio: 1/1;
  }
  .connect-button-content {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-2);
  }

  .connect-button-spinner {
    display: flex;
    align-items: center;
  }

  .status-controls {
    display: flex;
    gap: var(--ds-spacing-2);
    align-items: center;
  }

  .icon-container {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .icon-container--success {
    color: var(--ds-color-success-11);
  }

  .connection-status {
    display: flex;
    align-items: center;
    position: relative;
  }

  .icon-button-wrapper {
    opacity: 0;
    transition: opacity 0.2s ease;
    padding-right: var(--ds-spacing-2);
  }

  .connection-status:hover .icon-button-wrapper,
  .icon-button-wrapper.active {
    opacity: 1;
  }

  .status-message {
    margin: var(--ds-spacing-2) 0;
    padding: var(--ds-spacing-2);
    border-radius: var(--ds-radius-2);
  }

  .status-message.error {
    background-color: var(--ds-color-error-3);
    color: var(--ds-color-error-11);
  }

  .status-message.info {
    background-color: var(--ds-color-info-3);
    color: var(--ds-color-info-11);
  }
</style>
