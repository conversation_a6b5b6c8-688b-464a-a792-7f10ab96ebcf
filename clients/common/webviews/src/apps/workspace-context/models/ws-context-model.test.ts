import { expect, describe, test, vi, beforeEach, afterEach } from "vitest";
import { WSContextModel, type SourceNodeFolder } from "./ws-context-model";
import { AsyncMsgSender } from "$common-webviews/src/common/hosts/async-messaging";
import {
  WebViewMessageType,
  WSContextFileInclusionState,
  type WebViewMessage,
} from "$vscode/src/webview-providers/webview-messages";
import { get } from "svelte/store";
import { HostClientType, type HostInterface } from "$common-webviews/src/common/hosts/host-types";

describe("WSContextModel", () => {
  const mockHost: HostInterface = {
    clientType: HostClientType.jetbrains,
    postMessage: vi.fn(),
    getState: vi.fn(),
    setState: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });
  describe("constructor", () => {
    test("should initialize and request source folders", async () => {
      const asyncMsgSender = new AsyncMsgSender(mockHost.postMessage);
      const asyncMsgSendSpy = vi.spyOn(asyncMsgSender, "send");
      asyncMsgSendSpy.mockImplementation(async (msg) => {
        expect(msg.type).toBe(WebViewMessageType.wsContextGetSourceFoldersRequest);
        return GET_SOURCE_FOLDERS_RESPONSE;
      });

      const model = new WSContextModel(mockHost, asyncMsgSender);

      await waitUntil(() => get(model).sourceFolders.length > 0);

      expect(get(model).sourceFolders.length).toBe(1);
      expect(get(model).sourceFolders[0].name).toBe("asdf");
      expect(get(model).sourceTree.length).toBe(1);
      expect(get(model).sourceTree[0].name).toBe("asdf");
    });
  });

  describe("expandNode", () => {
    test("should request children and update state", async () => {
      const asyncMsgSender = new AsyncMsgSender(mockHost.postMessage);
      const asyncMsgSendSpy = vi.spyOn(asyncMsgSender, "send");
      asyncMsgSendSpy.mockImplementation(mockMessageResponder);

      const model = new WSContextModel(mockHost, asyncMsgSender);

      await waitUntil(() => get(model).sourceFolders.length > 0);

      expect(get(model).sourceTree.length).toBe(1);
      expect(get(model).sourceTree[0].name).toBe("asdf");

      expect((get(model).sourceTree[0] as SourceNodeFolder).children.length).toBe(0);
      expect((get(model).sourceTree[0] as SourceNodeFolder).expanded).toBe(false);

      model.expandNode(get(model).sourceTree[0] as SourceNodeFolder);

      await waitUntil(() => (get(model).sourceTree[0] as SourceNodeFolder).children.length > 0);

      expect((get(model).sourceTree[0] as SourceNodeFolder).children.length).toBe(1);
      expect((get(model).sourceTree[0] as SourceNodeFolder).expanded).toBe(true);
      expect((get(model).sourceTree[0] as SourceNodeFolder).children[0].name).toBe("asdfChild");
    });
  });

  describe("collapseNode", () => {
    test("should collapse node", async () => {
      const asyncMsgSender = new AsyncMsgSender(mockHost.postMessage);
      const asyncMsgSendSpy = vi.spyOn(asyncMsgSender, "send");
      asyncMsgSendSpy.mockImplementation(mockMessageResponder);

      const model = new WSContextModel(mockHost, asyncMsgSender);
      await waitUntil(() => get(model).sourceFolders.length > 0);

      model.expandNode(get(model).sourceTree[0] as SourceNodeFolder);
      await waitUntil(() => (get(model).sourceTree[0] as SourceNodeFolder).children.length > 0);
      expect((get(model).sourceTree[0] as SourceNodeFolder).children.length).toBe(1);
      expect((get(model).sourceTree[0] as SourceNodeFolder).expanded).toBe(true);

      model.collapseNode(get(model).sourceTree[0] as SourceNodeFolder);
      await waitUntil(() => (get(model).sourceTree[0] as SourceNodeFolder).children.length === 0);
      expect((get(model).sourceTree[0] as SourceNodeFolder).children.length).toBe(0);
      expect((get(model).sourceTree[0] as SourceNodeFolder).expanded).toBe(false);
    });
  });

  describe("addMoreSourceFolders", () => {
    test("should post message", async () => {
      const asyncMsgSender = new AsyncMsgSender(mockHost.postMessage);
      const hostPostMessageSpy = vi.spyOn(mockHost, "postMessage");
      const model = new WSContextModel(mockHost, asyncMsgSender);

      model.addMoreSourceFolders();

      expect(hostPostMessageSpy).toHaveBeenCalled();
      expect(hostPostMessageSpy).toHaveBeenCalledWith({
        type: WebViewMessageType.wsContextAddMoreSourceFolders,
      });
    });
  });

  describe("removeSourceFolder", () => {
    test("should post message", async () => {
      const asyncMsgSender = new AsyncMsgSender(mockHost.postMessage);
      const hostPostMessageSpy = vi.spyOn(mockHost, "postMessage");
      const model = new WSContextModel(mockHost, asyncMsgSender);

      model.removeSourceFolder("asdf");

      expect(hostPostMessageSpy).toHaveBeenCalled();
      expect(hostPostMessageSpy).toHaveBeenCalledWith({
        type: WebViewMessageType.wsContextRemoveSourceFolder,
        data: "asdf",
      });
    });
  });

  describe("requestRefresh", () => {
    test("should post message", async () => {
      const asyncMsgSender = new AsyncMsgSender(mockHost.postMessage);
      const hostPostMessageSpy = vi.spyOn(mockHost, "postMessage");
      const model = new WSContextModel(mockHost, asyncMsgSender);

      model.requestRefresh();

      expect(hostPostMessageSpy).toHaveBeenCalled();
      expect(hostPostMessageSpy).toHaveBeenCalledWith({
        type: WebViewMessageType.wsContextUserRequestedRefresh,
      });
    });
  });

  // TODO - a more detailed test that exercises the handlers in
  // handleMessageFromExtension including the getRefreshedSourceTree
  // recursion.
});

async function waitUntil(condition: () => boolean) {
  while (!condition()) {
    await new Promise((resolve) => setTimeout(resolve, 10)); // wait 10ms
  }
}

const GET_SOURCE_FOLDERS_RESPONSE: WebViewMessage = {
  type: WebViewMessageType.wsContextGetSourceFoldersResponse,
  data: {
    workspaceFolders: [
      {
        name: "asdf",
        fileId: {
          folderRoot: "asdf",
          relPath: "",
        },
        inclusionState: WSContextFileInclusionState.included,
        isWorkspaceFolder: true,
        isNestedFolder: false,
        isPending: false,
      },
    ],
  },
};

function mockMessageResponder(msg: WebViewMessage): Promise<WebViewMessage> {
  switch (msg.type) {
    case WebViewMessageType.wsContextGetSourceFoldersRequest: {
      return Promise.resolve(GET_SOURCE_FOLDERS_RESPONSE);
    }
    case WebViewMessageType.wsContextGetChildrenRequest: {
      return Promise.resolve({
        type: WebViewMessageType.wsContextGetChildrenResponse,
        data: {
          children: [
            {
              name: "asdfChild",
              fileId: {
                folderRoot: "asdf",
                relPath: "asdf",
              },
              type: "file",
              inclusionState: WSContextFileInclusionState.included,
              reason: "asdf",
            },
          ],
        },
      });
    }
    default: {
      Promise.reject(new Error("Unexpected message type"));
    }
  }
  return Promise.reject(new Error("Unexpected message type"));
}
