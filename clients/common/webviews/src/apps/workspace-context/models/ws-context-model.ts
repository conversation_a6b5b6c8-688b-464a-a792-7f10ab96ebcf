import {
  writable,
  get,
  type Invalidator,
  type Subscriber,
  type Unsubscriber,
  type Updater,
} from "svelte/store";
import { type AsyncMsgSender } from "$common-webviews/src/common/hosts/async-messaging";
import {
  type WSContextGetChildrenRequest,
  WebViewMessageType,
  type WSContextGetChildrenResponse,
  WSContextFileInclusionState,
  type WSContextGetSourceFoldersRequest,
  type WSContextGetSourceFoldersResponse,
  type WSContextFileItemId,
  type WSContextSourceFolder,
  type WebViewMessage,
  type SourceFoldersSyncStatus,
} from "$vscode/src/webview-providers/webview-messages";
import { SyncingStatus } from "$vscode/src/workspace/types";
import type { HostInterface } from "$common-webviews/src/common/hosts/host-types";

export enum NodeType {
  file = "file",
  folder = "folder",
}

type SourceNodeBase = {
  name: string;
  fileId: WSContextFileItemId;
  type: NodeType;
  reason: string;
};

export type SourceNodeFile = SourceNodeBase & {
  type: NodeType.file;
  inclusionState: WSContextFileInclusionState.included | WSContextFileInclusionState.excluded;
};

export type SourceNodeFolder = SourceNodeBase & {
  children: SourceNode[];
  expanded: boolean;
  type: NodeType.folder;
  inclusionState: WSContextFileInclusionState;
  trackedFileCount?: number;
};

export type SourceNode = SourceNodeFile | SourceNodeFolder;

interface StoredState {
  sourceFolders: WSContextSourceFolder[];
  sourceTree: SourceNode[];
  syncStatus: SyncingStatus;
}

export class WSContextModel {
  subscribe: (
    this: void,
    run: Subscriber<StoredState>,
    invalidate?: Invalidator<StoredState> | undefined,
  ) => Unsubscriber;
  private set: (this: void, value: StoredState) => void;
  private update: (this: void, updater: Updater<StoredState>) => void;

  constructor(
    private host: HostInterface,
    private asyncMsgSender: AsyncMsgSender,
  ) {
    const { subscribe, set, update } = writable<StoredState>({
      sourceFolders: [],
      sourceTree: [],
      syncStatus: SyncingStatus.done,
    });
    this.subscribe = subscribe;
    this.set = set;
    this.update = update;

    this.getSourceFolders().then((sourceFolders) => {
      this.update((value) => ({
        ...value,
        sourceFolders,
        sourceTree: WSContextModel.sourceFoldersToSourceNodes(sourceFolders),
      }));
    });
  }

  async expandNode(node: SourceNodeFolder) {
    node.children = await this.getChildren(node.fileId);
    node.expanded = true;
    this.update((value) => {
      return value;
    });
  }

  collapseNode(node: SourceNodeFolder) {
    this.update((value) => {
      node.children = [];
      node.expanded = false;
      return value;
    });
  }

  toggleNode(data: SourceNode) {
    if (
      data.type === NodeType.folder &&
      data.inclusionState !== WSContextFileInclusionState.excluded
    ) {
      if (data.expanded) {
        this.collapseNode(data);
      } else {
        this.expandNode(data);
      }
    }
  }

  addMoreSourceFolders() {
    this.host.postMessage({
      type: WebViewMessageType.wsContextAddMoreSourceFolders,
    });
  }

  removeSourceFolder(folderRoot: string) {
    this.host.postMessage({
      type: WebViewMessageType.wsContextRemoveSourceFolder,
      data: folderRoot,
    });
  }

  requestRefresh() {
    this.host.postMessage({
      type: WebViewMessageType.wsContextUserRequestedRefresh,
    });
  }

  handleMessageFromExtension = async (e: MessageEvent<WebViewMessage>) => {
    const msg = e.data;
    switch (msg.type) {
      case WebViewMessageType.wsContextSourceFoldersChanged:
      case WebViewMessageType.wsContextFolderContentsChanged: {
        // we update the source folders first to make sure we have
        // accurate counts of files in the sourcefolder list
        void this.updateSourceFolders(await this.getSourceFolders());

        break;
      }
      case WebViewMessageType.sourceFoldersSyncStatus: {
        this.update((value) => {
          return {
            ...value,
            syncStatus: (msg as SourceFoldersSyncStatus).data.status,
          };
        });
        break;
      }
    }
  };

  private async updateSourceFolders(folders: WSContextSourceFolder[]) {
    let startState = get(this);

    const newSourceTree = await this.getRefreshedSourceTree(startState.sourceTree, folders);

    this.update((value) => {
      return {
        ...value,
        sourceFolders: folders,
        sourceTree: newSourceTree,
      };
    });
  }

  // walk the source tree and compare existing versus new
  // we want to see what is currently open and maintain that
  private async getRefreshedSourceTree(
    currentTree: SourceNode[],
    folders: WSContextSourceFolder[],
  ) {
    const newSourceTree = WSContextModel.sourceFoldersToSourceNodes(folders);
    return this.getRefreshedSourceTreeRecurse(currentTree, newSourceTree);
  }

  // these awaits should not be long-running operations since it's just messaging to the extension
  private async getRefreshedSourceTreeRecurse(
    currentTree: SourceNode[],
    newTree: SourceNode[],
  ): Promise<SourceNode[]> {
    const currentMap = new Map(
      currentTree.map((item) => [
        JSON.stringify([item.fileId.folderRoot, item.fileId.relPath]),
        item,
      ]),
    );
    for (let node of newTree) {
      const key = WSContextModel.fileIdToString(node.fileId);
      if (node.type === NodeType.folder) {
        const current = currentMap.get(key);
        if (current) {
          node.expanded = current.type === NodeType.folder && current.expanded;
          if (node.expanded) {
            node.children = await this.getChildren(node.fileId);
            node.children = await this.getRefreshedSourceTreeRecurse(
              (current as SourceNodeFolder).children,
              node.children,
            );
          }
        }
      }
    }
    return newTree;
  }

  public static fileIdToString(fileId: WSContextFileItemId) {
    return JSON.stringify([fileId.folderRoot, fileId.relPath]);
  }

  private static sourceFoldersToSourceNodes(folders: WSContextSourceFolder[]): SourceNodeFolder[] {
    return folders
      .filter((folder) => !folder.isNestedFolder && !folder.isPending)
      .sort((a, b) => {
        return a.name.localeCompare(b.name);
      })
      .map((folder) => {
        return {
          name: folder.name,
          fileId: folder.fileId,
          children: [],
          expanded: false,
          type: NodeType.folder,
          inclusionState: folder.inclusionState,
          reason: "",
          trackedFileCount: folder.trackedFileCount,
        } as SourceNodeFolder;
      });
  }

  private getSourceFolders = async (): Promise<WSContextSourceFolder[]> => {
    const response = await this.asyncMsgSender.send<
      WSContextGetSourceFoldersRequest,
      WSContextGetSourceFoldersResponse
    >(
      {
        type: WebViewMessageType.wsContextGetSourceFoldersRequest,
      },
      // 10s timeout -- really we dont expect these to timeout ever,
      // but if something is taking this long there is a problem
      10000,
    );
    return response.data.workspaceFolders;
  };

  private getChildren = async (fileId: WSContextFileItemId): Promise<SourceNode[]> => {
    const response = await this.asyncMsgSender.send<
      WSContextGetChildrenRequest,
      WSContextGetChildrenResponse
    >(
      {
        type: WebViewMessageType.wsContextGetChildrenRequest,
        data: {
          fileId,
        },
      },
      // 10s timeout -- really we dont expect these to timeout ever,
      // but if something is taking this long there is a problem
      10000,
    );

    return response.data.children
      .map((child) => {
        if (child.type === NodeType.folder) {
          return {
            ...child,
            children: [],
            expanded: false,
          } as SourceNodeFolder;
        } else {
          return { ...child } as SourceNodeFile;
        }
      })
      .sort((a, b) => {
        if (a.type === b.type) {
          return a.name.localeCompare(b.name);
        } else if (a.type === NodeType.folder) {
          return -1;
        } else {
          return 1;
        }
      });
  };
}
