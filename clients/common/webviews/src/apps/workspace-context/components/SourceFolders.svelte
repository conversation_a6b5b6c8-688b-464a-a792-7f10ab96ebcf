<script lang="ts">
  import { onKey } from "../../../common/utils/keypress";
  import type { WSContextSourceFolder } from "$vscode/src/webview-providers/webview-messages";
  import VSCodeCodicon from "$common-webviews/src/common/components/vscode/VSCodeCodicon.svelte";
  import { WSContextModel } from "../models/ws-context-model";
  import Plus from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/plus.svg?component";
  import Cross from "$common-webviews/src/design-system/icons/cross-2.svelte";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";

  export let folders: WSContextSourceFolder[] = [];
  export let onAddMore: () => void;
  export let onRemove: (folderRoot: string) => void;

  const icon = (folder: WSContextSourceFolder) => {
    if (folder.isWorkspaceFolder) {
      return "root-folder";
    } else {
      return "folder";
    }
  };
  // TODO deal with gap and spacing
</script>

<div class="source-folder">
  {#each folders as folder (WSContextModel.fileIdToString(folder.fileId))}
    <div class="item" class:workspace-folder={folder.isWorkspaceFolder}>
      {#if !folder.isWorkspaceFolder}
        <IconButtonAugment
          title="Remove source folder from Augment context"
          on:click={() => onRemove(folder.fileId.folderRoot)}
          on:keyup={onKey("Enter", () => onRemove(folder.fileId.folderRoot))}
          variant="ghost"
          color="neutral"
          size={1}
          class="source-folder-v-adjust"
        >
          <Cross />
        </IconButtonAugment>
      {/if}
      <VSCodeCodicon class="source-folder-v-adjust" icon={icon(folder)} />
      <span class="name"
        >{folder.name}
        <span class="folderRoot">{folder.isPending ? "(pending)" : folder.fileId.folderRoot}</span
        ></span
      >
      {#if folder.trackedFileCount}
        <TextAugment size={1} class="file-count">
          {folder.trackedFileCount.toLocaleString()}
        </TextAugment>
      {/if}
    </div>
  {/each}
  <div
    role="button"
    tabindex="0"
    on:keyup={onKey("Enter", onAddMore)}
    on:click={onAddMore}
    class="add-more"
  >
    <Plus /> Add more...
  </div>
</div>

<style>
  .source-folder {
    /* 16px to match the size of the vscode codicons */
    --ws-source-folders-icon-size: 16px;
  }

  .item {
    cursor: default;
    line-height: var(--ws-list-item-line-height);
    height: var(--ws-list-item-line-height);
    display: flex;
    gap: var(--ws-list-item-gap);
    color: var(--ds-color-neutral-11);
  }

  .source-folder :global(.c-icon-btn svg),
  .add-more :global(svg) {
    width: var(--ws-source-folders-icon-size);
    height: var(--ws-source-folders-icon-size);
  }

  .source-folder .workspace-folder {
    /* Account for button and gap that is not shown for workspace folders */
    margin-left: calc(var(--ws-source-folders-icon-size) + var(--ws-list-item-gap));
  }

  .add-more {
    cursor: pointer;
    line-height: var(--ws-list-item-line-height);

    /* Allow hover background to be full width */
    margin-left: calc(0px - var(--ws-left-margin));
    margin-right: calc(0px - var(--ws-right-margin));

    padding-left: var(--ws-left-margin);
    padding-right: var(--ws-right-margin);

    color: var(--ds-color-neutral-10);
  }

  /* Add some padding to top of icons to fit with the text baseline */
  .source-folder :global(.source-folder-v-adjust),
  .add-more :global(svg) {
    position: relative;
    top: var(--ws-icon-v-offset);
  }

  .add-more:hover {
    background-color: var(--ws-list-item-hoverBackground);
  }
  .item .name {
    flex-grow: 1;
    min-width: 0;
    white-space: pre;
    text-wrap: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }
  .item .name .folderRoot {
    font-style: italic;
    color: var(--ds-color-neutral-10);
    font-size: 0.9em;
  }
  .item :global(.file-count) {
    color: var(--ds-color-neutral-10);
  }
</style>
