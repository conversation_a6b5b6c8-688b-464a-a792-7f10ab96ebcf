<script lang="ts">
  import { onKey } from "../../../common/utils/keypress";
  import includedIcon from "../assets/wsc-included.svg";
  import excludedIcon from "../assets/wsc-excluded.svg";
  import partiallyIncludedIcon from "../assets/wsc-partially-included.svg";

  import { NodeType, WSContextModel, type SourceNode } from "../models/ws-context-model";
  import { WSContextFileInclusionState } from "$vscode/src/webview-providers/webview-messages";
  import VSCodeCodicon from "$common-webviews/src/common/components/vscode/VSCodeCodicon.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";

  export let data: SourceNode;
  export let wsContextModel: WSContextModel;
  export let indentLevel: number;

  const toggleNode = () => {
    wsContextModel.toggleNode(data);
  };

  const getLeftIcon = (node: SourceNode) => {
    if (
      node.type === NodeType.folder &&
      node.inclusionState !== WSContextFileInclusionState.excluded
    ) {
      if (node.expanded) {
        return "chevron-down";
      } else {
        return "chevron-right";
      }
    } else if (node.type === NodeType.folder) {
      return "folder";
    } else {
      return "file";
    }
  };

  const rightIcons = {
    [WSContextFileInclusionState.included]: includedIcon,
    [WSContextFileInclusionState.excluded]: excludedIcon,
    [WSContextFileInclusionState.partial]: partiallyIncludedIcon,
  };

  const rightAltTexts = {
    [WSContextFileInclusionState.included]: "included",
    [WSContextFileInclusionState.excluded]: "excluded",
    [WSContextFileInclusionState.partial]: "partially included",
  };

  let isIncludedFolder;
  let leftIcon;
  let dataAsExpandedFolder;
  $: leftIcon = getLeftIcon(data);
  $: isIncludedFolder =
    data.type === NodeType.folder && data.inclusionState !== WSContextFileInclusionState.excluded;
  $: dataAsExpandedFolder = // non-null only if data is an expanded folder
    data.type === NodeType.folder && data.expanded && data.children && data.children.length > 0
      ? data
      : null;
</script>

<div>
  <div
    class="tree-item"
    class:included-folder={isIncludedFolder}
    role="treeitem"
    aria-selected="false"
    tabindex="0"
    title={data.reason}
    aria-expanded={data.type === NodeType.folder ? data.expanded : false}
    aria-level={indentLevel}
    on:click={toggleNode}
    on:keyup={onKey("Enter", toggleNode)}
    style={`padding-left: ${indentLevel * 10 + 20}px;`}
  >
    <VSCodeCodicon icon={leftIcon} />
    <span class="name">{data.name}</span>
    {#if data.type === NodeType.folder && data.inclusionState !== WSContextFileInclusionState.excluded && typeof data.trackedFileCount === "number"}
      <TextAugment size={1} class="file-count">
        {data.trackedFileCount.toLocaleString()}
      </TextAugment>
    {/if}
    <img src={rightIcons[data.inclusionState]} alt={rightAltTexts[data.inclusionState]} />
  </div>
  {#if dataAsExpandedFolder}
    <div class="children-container">
      {#each dataAsExpandedFolder.children as child (WSContextModel.fileIdToString(child.fileId))}
        <svelte:self data={child} {wsContextModel} indentLevel={indentLevel + 1} />
      {/each}
    </div>
  {/if}
</div>

<style>
  .tree-item {
    display: flex;
    gap: var(--ws-list-item-gap);
    cursor: default;

    padding-right: var(--ws-right-margin);
    line-height: var(--ws-list-item-line-height);
    color: var(--ds-color-neutral-11);
    align-items: center;
  }
  .tree-item.included-folder {
    cursor: pointer;
  }
  .tree-item:hover {
    background-color: var(--ws-list-item-hoverBackground);
  }
  .tree-item .name {
    flex-grow: 1;
    min-width: 0;
    white-space: pre;
    text-wrap: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }

  .tree-item :global(.file-count) {
    color: var(--ds-color-neutral-10);
  }
</style>
