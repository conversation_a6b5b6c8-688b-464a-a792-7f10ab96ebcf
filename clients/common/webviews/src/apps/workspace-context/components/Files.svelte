<script lang="ts">
  import FileTreeItem from "./FileTreeItem.svelte";
  import { WSContextModel, type SourceNode } from "../models/ws-context-model";
  export let wsContextModel: WSContextModel;
  let nodes: SourceNode[];
  $: nodes = $wsContextModel.sourceTree;
</script>

<div class="files-container">
  {#each nodes as node (WSContextModel.fileIdToString(node.fileId))}
    <FileTreeItem {wsContextModel} data={node} indentLevel={0} />
  {/each}
</div>

<style>
  /* we put the full margin here so that we can put a hover that goes all the way to the left edge
     just like vs code does in the native tree. */
  .files-container {
    margin-left: calc(0px - var(--ws-left-margin));
    margin-right: calc(0px - var(--ws-right-margin));
  }
</style>
