<script lang="ts">
  import "./workspace-context.css";

  import SourceFolders from "./components/SourceFolders.svelte";
  import Files from "./components/Files.svelte";
  import { WSContextModel } from "./models/ws-context-model";
  import { SyncingStatus } from "$vscode/src/workspace/types";
  import { onKey } from "$common-webviews/src/common/utils/keypress";
  import { host } from "$common-webviews/src/common/hosts/host";
  import { AsyncMsgSender } from "$common-webviews/src/common/hosts/async-messaging";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import ContextIcon from "$common-webviews/src/design-system/icons/augment/context.svelte";
  import Reload from "$common-webviews/src/design-system/icons/reload.svelte";
  import SettingsCard from "../settings/components/SettingsCard.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import type { ComponentType } from "svelte";

  let wsContextModel = new WSContextModel(host, new AsyncMsgSender(host.postMessage));
  let workspaceFolders;
  $: workspaceFolders = $wsContextModel.sourceFolders.sort((a, b) => {
    if (a.isWorkspaceFolder !== b.isWorkspaceFolder) {
      return a.isWorkspaceFolder ? -1 : 1;
    }
    return a.fileId.folderRoot.localeCompare(b.fileId.folderRoot);
  });
  let syncStatus;
  $: syncStatus = $wsContextModel.syncStatus;
  $: totalFilesCount = workspaceFolders.reduce((acc, curr) => {
    return acc + (curr.trackedFileCount ?? 0);
  }, 0);
</script>

<svelte:window on:message={wsContextModel.handleMessageFromExtension} />

<!-- disable right click context since we disabled select to
    match the native VS code file tree -->
<!-- SVELTE5_MIGRATION - AU-11869 -->
<SettingsCard
  icon={ContextIcon as unknown as ComponentType}
  title="Context"
  on:contextmenu={(e) => e.preventDefault()}
>
  <div slot="header-right">
    {#if syncStatus === SyncingStatus.done}
      <IconButtonAugment
        title="Refresh"
        on:click={() => wsContextModel.requestRefresh()}
        on:keyup={onKey("Enter", () => wsContextModel.requestRefresh())}
        variant="ghost-block"
        color="neutral"
        size={1}
      >
        <Reload />
      </IconButtonAugment>
    {/if}
  </div>
  <div class="context-list">
    <div>
      <TextAugment size={1} weight="medium" class="context-section-header"
        >SOURCE FOLDERS</TextAugment
      >
      <SourceFolders
        folders={workspaceFolders}
        onRemove={(folderRoot) => wsContextModel.removeSourceFolder(folderRoot)}
        onAddMore={() => wsContextModel.addMoreSourceFolders()}
      />
    </div>
    <div>
      <div class="files-header">
        <TextAugment size={1} weight="medium" class="context-section-header">FILES</TextAugment>
        <TextAugment size={1} class="file-count">
          {totalFilesCount.toLocaleString()}
        </TextAugment>
      </div>
      <Files {wsContextModel} />
    </div>
  </div>
</SettingsCard>

<style>
  .context-list {
    /* disable text selection to match the native VS code file tree */
    user-select: none;

    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-2);
    padding: var(--ds-spacing-2);
  }

  .context-list :global(.context-section-header) {
    color: var(--ds-color-neutral-12);
  }

  .files-header {
    display: flex;
    gap: var(--ds-spacing-2);
    align-items: center;
  }

  .files-header :global(.file-count) {
    color: var(--ds-color-neutral-10);
    padding-left: var(--ds-spacing-3);
    display: block; /* override from `contents` so that padding applies */
  }
</style>
