<script lang="ts">
  import { format } from "date-fns";
  import { host } from "$common-webviews/src/common/hosts/host";
  import { WebViewMessageType } from "$vscode/src/webview-providers/webview-messages";
  import CopyButton from "$common-webviews/src/common/components/CopyButton.svelte";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";

  export let occuredAt: Date;
  export let requestID: string;
  export let pathName: string = "";
  export let repoRoot: string;
  export let prompt: string = "";
  export let others: string[] = [];

  function onFileClick() {
    host.postMessage({
      type: WebViewMessageType.openFile,
      data: {
        repoRoot: repoRoot,
        pathName: pathName,
      },
    });
  }
</script>

<div class="c-history-header">
  <div class="c-history-header__timestamp">
    {format(occuredAt, "p 'on' P")}
  </div>

  <div class="c-history-header__metadata">
    <div class="c-history-header__item">
      <span class="c-history-header__label">Request ID:</span>
      <CopyButton
        text={requestID}
        variant="ghost-block"
        color="neutral"
        size={1}
        tooltip="Copy Request ID"
      >
        <span slot="text" class="c-history-header--ellipsis">{requestID}</span>
      </CopyButton>
    </div>

    {#if pathName}
      <div class="c-history-header__item">
        <span class="c-history-header__label">File:</span>
        <ButtonAugment
          variant="ghost-block"
          color="neutral"
          size={1}
          on:click={onFileClick}
          title="Click to open file"
        >
          <span class="c-history-header--ellipsis-left">&lrm;{pathName}</span>
        </ButtonAugment>
      </div>
    {/if}

    {#if prompt}
      <div class="c-history-header__item">
        <span class="c-history-header__label">Instruction:</span>
        <span class="c-history-header--ellipsis">{prompt}</span>
      </div>
    {/if}

    {#each others as other}
      <div class="c-history-header__item">
        <span class="c-history-header--ellipsis">{other}</span>
      </div>
    {/each}
  </div>
</div>

<style>
  .c-history-header {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-2);
  }

  .c-history-header__timestamp {
    font-size: 1.1em;
    font-weight: 600;
    color: var(--vscode-foreground);
    margin-bottom: var(--ds-spacing-1);
  }

  .c-history-header__metadata {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-1);
    font-family: var(--monaco-monospace-font);
    font-size: 0.9em;
    line-height: 1.4;
  }

  .c-history-header__item {
    display: flex;
    align-items: flex-start;
    gap: var(--ds-spacing-2);
    min-height: 1.4em;
  }

  .c-history-header__label {
    color: var(--vscode-descriptionForeground);
    font-weight: 500;
    flex-shrink: 0;
    min-width: 80px;
  }

  .c-history-header--ellipsis,
  .c-history-header--ellipsis-left {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    flex: 1;
    min-width: 0;
  }

  .c-history-header--ellipsis-left {
    direction: rtl;
    text-align: left;
  }
</style>
