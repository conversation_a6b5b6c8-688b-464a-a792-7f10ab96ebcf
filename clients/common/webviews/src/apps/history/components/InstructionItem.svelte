<script lang="ts">
  import type { AugmentInstruction } from "$vscode/src/code-edit-types";
  import HistoryHeader from "./HistoryHeader.svelte";
  import SimpleMonaco from "$common-webviews/src/design-system/components/MonacoProvider/SimpleMonaco.svelte";
  import { MONACO_OPTIONS } from "../utils/monacto-opts";

  export let instruction: AugmentInstruction;
  // Svelte reuses components, so if we store this as a boolean, it will
  // stay the same when we get a new instruction.  We thus have it store
  // the request id so we know when it gets out of date.
  let expandedRequestId: string | undefined = undefined;

  function onHiddenItemClicked() {
    expandedRequestId = instruction.requestId;
  }

  // trimSuffix removes any empty lines at the end of the suffix.
  function trimSuffix(suffix: string): string {
    const lines = suffix.split("\n");
    for (let i = lines.length - 1; i >= 0; i--) {
      if (lines[i].trim().length > 0) {
        return lines.slice(0, i + 1).join("\n");
      }
    }
    // All lines were empty
    return "";
  }

  $: selectedText = trimSuffix(instruction.selectedText);
  $: modifiedText = trimSuffix(instruction.modifiedText);
</script>

<div class="c-instruction-item">
  <HistoryHeader
    occuredAt={instruction.occuredAt}
    requestID={instruction.requestId}
    pathName={instruction.pathName}
    repoRoot={instruction.repoRoot}
    prompt={instruction.prompt}
  />

  {#if instruction.selectedText === instruction.modifiedText}
    <div class="c-instruction-item__no-modifications">No modification to original code</div>
  {:else if instruction.userRequested || expandedRequestId === instruction.requestId}
    <section>
      original:
      <SimpleMonaco options={MONACO_OPTIONS} text={selectedText} pathName={instruction.pathName} />
      {#if selectedText !== modifiedText}
        modified:
        <SimpleMonaco
          options={MONACO_OPTIONS}
          text={modifiedText}
          pathName={instruction.pathName}
        />
      {:else}
        <div class="c-instruction-item__no-modifications">No modification to original code</div>
      {/if}
    </section>
  {:else}
    <div
      class="c-instruction-item__no-modifications"
      role="button"
      tabindex="0"
      on:keyup={onHiddenItemClicked}
      on:click={onHiddenItemClicked}
    >
      Click to view diff
    </div>
  {/if}
</div>

<style>
  .c-instruction-item {
    display: flex;
    flex-direction: column;
    color: var(--vscode-descriptionForeground);
    gap: var(--p-2);
    margin: var(--p-4) 0;
  }

  .c-instruction-item__no-modifications {
    text-align: center;
    font-weight: bold;
    font-style: italic;
  }
</style>
