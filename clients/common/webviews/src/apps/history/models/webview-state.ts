import { FeedbackRating } from "$vscode/src/types/feedback-rating";
import { host } from "$common-webviews/src/common/hosts/host";
import { type FeedbackState } from "../types/feedback-state";
import type { LocalCompletionRequest } from "../types/local-history-completion";

class WebViewState {
  private _state: StoredState;

  constructor() {
    this._state = host.getState() || {};
    this._state.feedback = this._state.feedback || {};
  }

  getFeedback(requestID: string): FeedbackState {
    if (this._state.feedback[requestID]) {
      return this._state.feedback[requestID];
    }
    return {
      selectedRating: FeedbackRating.unset,
      feedbackNote: "",
    };
  }

  setFeedback(requestID: string, feedback: FeedbackState) {
    this._state.feedback[requestID] = feedback;
    host.setState(this._state);
  }

  // This function is used to remove any feedback that is no longer in
  // the completions ringbuffer.
  cleanupFeedback(completions: { [requestID: string]: LocalCompletionRequest }) {
    for (const key of Object.keys(this._state.feedback)) {
      if (!completions[key]) {
        delete this._state.feedback[key];
      }
    }
    host.setState(this._state);
  }
}

interface StoredState {
  feedback: FeedbackMap;
}

type FeedbackMap = { [requestId: string]: FeedbackState };

export const webviewState = new WebViewState();
