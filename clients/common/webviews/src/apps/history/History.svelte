<script lang="ts">
  import { host } from "$common-webviews/src/common/hosts/host";
  import {
    WebViewMessageType,
    type WebViewMessage,
    type HistoryCompletionRequest,
  } from "$vscode/src/webview-providers/webview-messages";
  import { parseISO } from "date-fns";
  import NoItems from "./components/NoItems.svelte";
  import InstructionItem from "./components/InstructionItem.svelte";
  import { webviewState } from "./models/webview-state";
  import type { LocalCompletionRequest } from "./types/local-history-completion";
  import UnifiedHistoryItem from "./components/UnifiedHistoryItem.svelte";
  import ToggleButtonAugment from "$common-webviews/src/design-system/components/ToggleButtonAugment.svelte";
  import type { NextEditResultInfo } from "$vscode/src/next-edit/next-edit-types";
  import type { AugmentInstruction } from "$vscode/src/code-edit-types";
  import MonacoProvider from "$common-webviews/src/design-system/components/MonacoProvider";

  type InternalEditSuggestions = {
    requestId: string; // We need a request id at the top-level.
    occuredAt: Date; // We need to insert the typo back.
    result: NextEditResultInfo;
  };

  let completions: { [requestId: string]: LocalCompletionRequest } = {};
  let instructions: { [requestId: string]: AugmentInstruction } = {};
  let nextEdits: { [requestId: string]: InternalEditSuggestions } = {};

  function appendCompletions(newCompletions: HistoryCompletionRequest[]) {
    for (const c of newCompletions) {
      if (completions[c.requestId]) {
        continue;
      }

      completions[c.requestId] = {
        ...c,
        occuredAt: parseISO(c.occuredAt),
      };
    }
    webviewState.cleanupFeedback(completions);
  }

  function appendInstructions(newInstructions: any[]) {
    for (const e of newInstructions) {
      if (instructions[e.requestId]) {
        continue;
      }

      // When the date is posted to the webview, it's converted
      // to a string, so we need to convert it back to a date.
      if (typeof e.occuredAt === "string") {
        const dateString = e.occuredAt as unknown as string;
        e.occuredAt = parseISO(dateString);
      }

      instructions[e.requestId] = {
        ...e,
      };
    }
  }

  function appendNextEdits(allResults: NextEditResultInfo[]) {
    for (const result of allResults) {
      if (result.suggestions.length === 0) {
        continue;
      }
      // Overwrite previous entries with the same request id for streaming.
      nextEdits[result.requestId] = {
        requestId: result.requestId,
        occuredAt: parseISO(result.occurredAt as unknown as string),
        result,
      };
    }
  }

  function handleMessage(e: MessageEvent<WebViewMessage>) {
    const msg = e.data;
    switch (msg.type) {
      case WebViewMessageType.historyInitialize:
        appendInstructions(msg.data.instructions);
        appendCompletions(msg.data.completionRequests);
        appendNextEdits(msg.data.nextEdits);
        break;
      case WebViewMessageType.completions:
        appendCompletions(msg.data);
        break;
      case WebViewMessageType.instructions:
        appendInstructions(msg.data);
        break;
      case WebViewMessageType.nextEditSuggestions:
        appendNextEdits([msg.data]);
        break;
    }
  }

  host.postMessage({
    type: WebViewMessageType.historyLoaded,
  });

  let sortedItems: (LocalCompletionRequest | AugmentInstruction | InternalEditSuggestions)[] = [];
  $: sortedItems = [
    ...Object.values(instructions),
    ...Object.values(completions),
    ...Object.values(nextEdits),
  ].sort((a, b) => b.occuredAt.getTime() - a.occuredAt.getTime());

  // Panel state
  let activePanel = "Completions";

  // Separate items by type
  $: completionItems = sortedItems.filter((item) => "completions" in item);
  $: nextEditItems = sortedItems.filter((item) => "result" in item);
  $: instructionItems = sortedItems.filter((item) => "prompt" in item);
  $: panelOptions = [
    { value: "Completions", label: `Completions ${completionItems.length}` },
    {
      value: "Next Edits",
      label: `Next Edits ${nextEditItems.length}`,
    },
  ];
  // Get items for active panel
  $: activePanelItems = activePanel === "Completions" ? completionItems : nextEditItems;

  function handlePanelSelect(option: string): boolean {
    activePanel = option;
    return true;
  }

  function asAugmentInstruction(
    itm: LocalCompletionRequest | AugmentInstruction | InternalEditSuggestions,
  ): AugmentInstruction {
    if (!("prompt" in itm)) {
      throw new Error(`wrong type`);
    }
    if ("completions" in itm) {
      throw new Error(`wrong type`);
    }
    return itm;
  }
</script>

<svelte:window on:message={handleMessage} />

<MonacoProvider.Root>
  <main class="l-items-list">
    {#if !sortedItems.length}
      <div class="l-items-list__empty">
        <NoItems />
      </div>
    {:else}
      <!-- Panel Navigation -->
      <ToggleButtonAugment
        options={panelOptions}
        onSelectOption={handlePanelSelect}
        activeOption={activePanel}
        size={2}
      />

      <!-- Instructions (always shown if present) -->
      {#if instructionItems.length > 0}
        <div class="l-items-list__instructions-section">
          <h3 class="l-items-list__section-title">Instructions</h3>
          <div class="l-items-list__content">
            {#each instructionItems as item, index (item.requestId)}
              <div class="l-items-list__item">
                <InstructionItem instruction={asAugmentInstruction(item)} />
              </div>
              {#if index < instructionItems.length - 1}
                <div class="l-items-list__divider"></div>
              {/if}
            {/each}
          </div>
        </div>
      {/if}

      <!-- Active Panel Content -->
      <div class="l-items-list__panel-content">
        {#if activePanelItems.length === 0}
          <div class="l-items-list__empty-panel">
            <p>No {activePanel.toLowerCase()} found.</p>
          </div>
        {:else}
          <div class="l-items-list__content">
            {#each activePanelItems as item, index (item.requestId)}
              <div class="l-items-list__item">
                {#if "completions" in item}
                  <UnifiedHistoryItem completion={item} />
                {:else if "result" in item}
                  <UnifiedHistoryItem nextEdit={item.result} />
                {/if}
              </div>

              {#if index < activePanelItems.length - 1}
                <div class="l-items-list__divider"></div>
              {/if}
            {/each}
          </div>
        {/if}
      </div>
    {/if}
  </main>
</MonacoProvider.Root>

<style>
  .l-items-list {
    display: flex;
    flex-direction: column;
    max-width: 800px;
    width: 100%;
    margin: 0 auto;
    padding: var(--ds-spacing-4);
    min-height: 100vh;
    gap: var(--ds-spacing-3);
  }

  .l-items-list__empty {
    display: flex;
    justify-content: center;
    align-items: center;
    flex: 1;
    min-height: 400px;
  }

  .l-items-list__content {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-3);
  }

  .l-items-list__item {
    position: relative;
  }

  .l-items-list__divider {
    height: 1px;
    background: linear-gradient(
      90deg,
      transparent 0%,
      var(--vscode-panel-border) 20%,
      var(--vscode-panel-border) 80%,
      transparent 100%
    );
    margin: var(--ds-spacing-2) 0;
    opacity: 0.6;
  }

  /* Subtle fade-in for items */
  .l-items-list__item {
    animation: fadeIn 0.4s ease-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  /* Panel navigation styles */

  :global(.l-items-list__panel-nav-card) {
    border: 1px solid var(--vscode-panel-border);
    background: var(--vscode-editor-background);
  }
  .l-items-list__instructions-section {
    margin-bottom: var(--ds-spacing-4);
  }

  .l-items-list__section-title {
    font-size: 1.1em;
    font-weight: 600;
    color: var(--vscode-foreground);
    margin-bottom: var(--ds-spacing-3);
    padding-left: var(--ds-spacing-2);
  }

  .l-items-list__empty-panel {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: var(--ds-spacing-8);
    color: var(--vscode-descriptionForeground);
    font-style: italic;
  }
</style>
