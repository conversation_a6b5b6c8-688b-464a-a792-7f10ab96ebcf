<script lang="ts">
  import ComponentLoader from "$common-webviews/src/common/components/component-loader/ComponentLoader.svelte";
  import MonacoProvider from "$common-webviews/src/design-system/components/MonacoProvider";
  const loader = async () => {
    await import("./next-edit-suggestions.css");
    return (await import("./NextEditSuggestions.svelte")).default as any;
  };
</script>

<MonacoProvider.Root>
  <ComponentLoader {loader} props={{}} />
</MonacoProvider.Root>
