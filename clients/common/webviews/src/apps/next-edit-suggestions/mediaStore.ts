import { readable } from "svelte/store";

export const createMediaQueryStore = (mediaQueryString: string) =>
  readable(false, (set) => {
    //we match the media query
    const m = window.matchMedia(mediaQueryString);
    //we set the value of the reader to the matches property
    set(m?.matches ?? false);
    //we create the event listener that will set the new value on change
    const el = (e: MediaQueryListEvent) => set(e.matches);
    //we add the new event listener
    m.addEventListener("change", el);

    return () => void m.removeEventListener("change", el);
  });
