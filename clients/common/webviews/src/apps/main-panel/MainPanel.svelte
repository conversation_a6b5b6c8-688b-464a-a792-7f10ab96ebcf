<script lang="ts">
  import { host } from "$common-webviews/src/common/hosts/host";
  import {
    MainPanelApp,
    WebViewMessageType,
    type WebViewMessage,
  } from "$vscode/src/webview-providers/webview-messages";
  import Chat from "$common-webviews/src/apps/chat/Chat.svelte";
  import SignIn from "./SignIn.svelte";
  import AwaitingSyncingPermission from "./AwaitingSyncingPermission.svelte";
  import FolderSelection from "./FolderSelection.svelte";
  import MonacoProvider from "$common-webviews/src/design-system/components/MonacoProvider";
  import Loading from "./Loading.svelte";
  import { FULL_SIZE_OVERLAY_PORTAL_ID } from "$common-webviews/src/design-system/components/FullSizeOverlayAugment/FullSizeOverlayAugment.svelte";
  import { catchUnhandledAugmentLinks } from "$common-webviews/src/common/actions/catchUnhandledAugmentLinks";

  let visibleApp: MainPanelApp | undefined;

  const extensionMessageHandler = (e: MessageEvent<WebViewMessage>) => {
    const msg = e.data;
    switch (msg.type) {
      case WebViewMessageType.mainPanelDisplayApp: {
        visibleApp = msg.data;
        break;
      }
    }
  };

  host.postMessage({
    type: WebViewMessageType.mainPanelLoaded,
  });
</script>

<svelte:window on:message={extensionMessageHandler} />

<MonacoProvider.Root>
  <main use:catchUnhandledAugmentLinks>
    {#if visibleApp === MainPanelApp.loading}
      <Loading />
    {:else if visibleApp === MainPanelApp.signIn}
      <SignIn />
    {:else if visibleApp === MainPanelApp.chat}
      <Chat />
    {:else if visibleApp === MainPanelApp.awaitingSyncingPermission}
      <AwaitingSyncingPermission />
    {:else if visibleApp === MainPanelApp.folderSelection}
      <FolderSelection />
    {/if}

    <div id={FULL_SIZE_OVERLAY_PORTAL_ID}></div>
  </main>
</MonacoProvider.Root>
