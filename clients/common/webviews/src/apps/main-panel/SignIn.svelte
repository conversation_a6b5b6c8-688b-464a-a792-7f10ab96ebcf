<script lang="ts">
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import AugmentFeatures from "./components/AugmentFeatures.svelte";
  import { host } from "$common-webviews/src/common/hosts/host";
  import {
    type WebViewMessage,
    WebViewMessageType,
  } from "$vscode/src/webview-providers/webview-messages";
  import AugmentLogoAnimated from "./components/AugmentLogoAnimated.svelte";
  import OnboardingLayout from "./components/OnboardingLayout.svelte";
  import WelcomeTitle from "./components/WelcomeTitle.svelte";
  import Account from "$common-webviews/src/design-system/icons/vscode/account.svelte";

  function onSignIn() {
    host.postMessage({
      type: WebViewMessageType.mainPanelPerformAction,
      data: "sign-in",
    });
  }

  let client: string | undefined = undefined;

  const extensionMessageHandler = (e: MessageEvent<WebViewMessage>) => {
    const msg = e.data;
    switch (msg.type) {
      case WebViewMessageType.signInLoadedResponse: {
        client = msg.data.client;
        break;
      }
    }
  };

  host.postMessage({
    type: WebViewMessageType.signInLoaded,
  });
</script>

<svelte:window on:message={extensionMessageHandler} />
<OnboardingLayout>
  <AugmentLogoAnimated slot="logo" />
  <div class="c-sign-in">
    <WelcomeTitle title="Welcome!" subtitle="Please sign in below." />
    <ButtonAugment variant="solid" color="accent" on:click={onSignIn} data-testid="sign-in-button">
      <Account slot="iconLeft" />
      Sign In
    </ButtonAugment>
    <AugmentFeatures {client} />
  </div>
</OnboardingLayout>

<style>
  .c-sign-in {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .c-sign-in {
    gap: var(--ds-spacing-4);
  }
</style>
