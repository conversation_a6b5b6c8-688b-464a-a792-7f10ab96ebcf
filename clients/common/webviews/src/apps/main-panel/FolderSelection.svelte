<script lang="ts">
  import { host } from "$common-webviews/src/common/hosts/host";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import ArrowRight from "$common-webviews/src/design-system/icons/arrow-right.svelte";
  import CardStackPlus from "$common-webviews/src/design-system/icons/card-stack-plus.svelte";
  import Pencil2 from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/pen-to-square.svg?component";
  import FolderOpened from "$common-webviews/src/design-system/icons/vscode/folder-opened.svelte";
  import AugmentFeatures from "./components/AugmentFeatures.svelte";
  import AugmentLogoAnimated from "./components/AugmentLogoAnimated.svelte";
  import OnboardingLayout from "./components/OnboardingLayout.svelte";
  import WelcomeTitle from "./components/WelcomeTitle.svelte";
  import TextFieldAugment from "$common-webviews/src/design-system/components/TextFieldAugment.svelte";
  import { MessageBroker } from "$common-webviews/src/common/utils/message-broker";
  import { ExtensionClient } from "../chat/extension-client";
  import { ChatFlagsModel } from "../chat/models/chat-flags-model";
  import CalloutAugment from "$common-webviews/src/design-system/components/CalloutAugment.svelte";
  import ExclamationTriangle from "$common-webviews/src/design-system/icons/exclamation-triangle.svelte";

  const extensionClient = new ExtensionClient(host, new MessageBroker(host), new ChatFlagsModel());

  function onCreateProject(projectName: string) {
    extensionClient.createProject(projectName);
  }

  function onOpenFolder() {
    extensionClient.openProjectFolder();
  }

  function onCloneRepository() {
    extensionClient.cloneRepository();
  }

  function onOpenNameClick() {
    if (!projectNameInputOpen) {
      projectNameInputOpen = true;
      setTimeout(() => {
        projectNameInput?.focus();
      }, 0);
    } else {
      projectNameInputOpen = false;
    }
  }

  let projectNameInputOpen = false;
  let projectName = "";
  let projectNameInput: HTMLInputElement | undefined = undefined;
  let projectNameDisabled = true;
  let isProjectNameInvalid = false;

  $: {
    // Show error text is project name is non-empty and contains non-alphanumeric characters
    isProjectNameInvalid = !/^[a-zA-Z0-9\-_ ]+$/.test(projectName) && projectName.trim().length > 0;
    // Do not let the user make a project if the input is not open and the project name is empty
    projectNameDisabled =
      !projectNameInputOpen || !projectName.trim().length || isProjectNameInvalid;
  }
</script>

<OnboardingLayout>
  <AugmentLogoAnimated slot="logo" />
  <div class="c-folder-selection">
    <WelcomeTitle title="Welcome back!" subtitle="Please open a project folder to begin." />
    <div class="c-folder-selection__buttons">
      <ButtonAugment
        variant="outline"
        color="neutral"
        size={2}
        on:click={onOpenNameClick}
        alignment="left"
      >
        <Pencil2 slot="iconLeft" />
        Create a new project
      </ButtonAugment>
      {#if projectNameInputOpen}
        <div class="c-folder-selection__project-name-input">
          <TextFieldAugment
            color={isProjectNameInvalid ? "error" : "neutral"}
            placeholder="Project Name"
            bind:value={projectName}
            bind:textInput={projectNameInput}
            on:keydown={(e) => {
              if (e.key === "Enter" && !projectNameDisabled) {
                e.preventDefault();
                onCreateProject(projectName.trim());
              }
            }}
          />
          <IconButtonAugment
            on:click={() => onCreateProject(projectName.trim())}
            disabled={projectNameDisabled}
          >
            <ArrowRight />
          </IconButtonAugment>
        </div>
      {/if}
      <ButtonAugment
        variant="outline"
        color="neutral"
        size={2}
        on:click={onOpenFolder}
        alignment="left"
      >
        <FolderOpened slot="iconLeft" />
        Open a local folder
      </ButtonAugment>
      <ButtonAugment
        variant="outline"
        color="neutral"
        size={2}
        on:click={onCloneRepository}
        alignment="left"
      >
        <CardStackPlus slot="iconLeft" />
        Clone a repository
      </ButtonAugment>
    </div>
    <AugmentFeatures />
  </div>
  <div slot="error">
    {#if isProjectNameInvalid}
      <CalloutAugment variant="soft" color="error" size={1}>
        <ExclamationTriangle slot="icon" />
        Must contain only letters, numbers, hyphens, underscores, and spaces
      </CalloutAugment>
    {/if}
  </div>
</OnboardingLayout>

<style>
  .c-folder-selection,
  .c-folder-selection__buttons {
    --c-folder-selection__button-width: 185px;

    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .c-folder-selection {
    gap: var(--ds-spacing-4);
  }

  .c-folder-selection__buttons {
    gap: var(--ds-spacing-3);
  }

  .c-folder-selection__buttons :global(.c-base-btn:not(.c-icon-btn .c-base-btn)) {
    min-width: var(--c-folder-selection__button-width);
  }

  .c-folder-selection__project-name-input {
    display: flex;
    flex-direction: row;
    gap: var(--ds-spacing-1);
    width: var(--c-folder-selection__button-width);
  }
</style>
