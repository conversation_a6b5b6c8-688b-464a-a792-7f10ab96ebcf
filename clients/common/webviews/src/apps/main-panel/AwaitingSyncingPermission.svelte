<script lang="ts">
  import { host } from "$common-webviews/src/common/hosts/host";
  import { isVSCodeHost } from "$common-webviews/src/common/hosts/vscode/vscode";
  import { MessageBroker } from "$common-webviews/src/common/utils/message-broker";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import Folder from "$common-webviews/src/design-system/icons/vscode/folder.svelte";
  import ChevronLeft from "$common-webviews/src/design-system/icons/chevron-left.svelte";
  import { DerivedStateName } from "$vscode/src/main-panel/action-cards/types";
  import {
    type WebViewMessage,
    WebViewMessageType,
  } from "$vscode/src/webview-providers/webview-messages";
  import ActionCard from "../chat/components/action-cards/ActionCard.svelte";
  import { ExtensionClient } from "../chat/extension-client";
  import { ActionsModel } from "../chat/models/actions-model";
  import { ChatFlagsModel } from "../chat/models/chat-flags-model";
  import OnboardingLayout from "./components/OnboardingLayout.svelte";
  import { filterActions } from "./util/awaitingSyncing";

  const msgBroker = new MessageBroker(host);
  let actionsModel = new ActionsModel();
  msgBroker.registerConsumer(actionsModel);
  const flagsModel = new ChatFlagsModel();
  const extensionClient = new ExtensionClient(host, msgBroker, flagsModel);

  // Display a loading spinner while we wait for the app to switch
  let loadingAppSwitch = false;
  function onEnableIndexing() {
    loadingAppSwitch = true;
    extensionClient.grantSyncPermission();
  }

  function onOpenFolder() {
    extensionClient.openProjectFolder();
  }

  function onBackClick() {
    extensionClient.closeProjectFolder();
  }

  // If we don't receive a workspace name, we should prompt the user to open a folder
  // This is unlikely to happen as this app should not be active if no workspace
  // is selected. However, if it does happen, we ask the user to open a folder
  const openFolderPrompt = "Open a folder";
  let workspaceName: string | undefined;

  let disableIndexing = false;
  $: {
    disableIndexing =
      $actionsModel.currentCard === DerivedStateName.workspaceTooLarge ||
      $actionsModel.currentCard === DerivedStateName.uploadingHomeDir;
  }

  function extensionMessageHandler(e: MessageEvent<WebViewMessage>) {
    const msg = e.data;
    switch (msg.type) {
      case WebViewMessageType.awaitingSyncingPermissionInitialize: {
        const { enableDebugFeatures, maxTrackableFileCount, userTier } = msg.data;
        workspaceName = msg.data.workspaceName;
        flagsModel.update({
          enableDebugFeatures,
          maxTrackableFileCount,
          userTier,
        });
        break;
      }
      case WebViewMessageType.mainPanelActions: {
        actionsModel.onActionsUpdates(filterActions(msg.data));
        break;
      }
    }
  }

  host.postMessage({
    type: WebViewMessageType.awaitingSyncingPermissionLoaded,
  });
</script>

<svelte:window on:message={extensionMessageHandler} />
<OnboardingLayout>
  {#if isVSCodeHost()}
    <div class="c-back-button-container">
      <ButtonAugment variant="ghost" color="neutral" size={1} on:click={onBackClick}>
        <ChevronLeft />
        Back
      </ButtonAugment>
    </div>
  {/if}
  <div class="c-awaiting-syncing-permission">
    <TextAugment size={2} weight="bold" class="c-awaiting-syncing-permission__title"
      >Index Codebase</TextAugment
    >
    <div class="c-awaiting-syncing-permission__text">
      <TextAugment size={1}>
        Indexing allows Augment to make tailored code suggestions and explain common practices or
        patterns.
      </TextAugment>
      <TextAugment size={1}
        >Your data always stays secure, private and anonymized.
        <TextAugment size={1}
          ><a href="https://docs.augmentcode.com/setup-augment/workspace-indexing">Learn More</a
          ></TextAugment
        >
      </TextAugment>
    </div>
    <TextTooltipAugment content="Open a new project">
      <ButtonAugment variant="outline" color="neutral" size={2} on:click={onOpenFolder}>
        <Folder slot="iconLeft" />
        {workspaceName ?? openFolderPrompt}
      </ButtonAugment>
    </TextTooltipAugment>
    <ButtonAugment
      variant="solid"
      color="accent"
      size={2}
      on:click={onEnableIndexing}
      disabled={!workspaceName || disableIndexing}
      loading={loadingAppSwitch}
    >
      Index Codebase
    </ButtonAugment>
  </div>
  <div slot="error">
    <ActionCard card={$actionsModel.currentCard} {flagsModel} />
  </div>
</OnboardingLayout>

<style>
  .c-back-button-container {
    position: absolute;
    top: var(--ds-spacing-1);
    left: var(--ds-spacing-4);
    z-index: 10;
  }

  .c-back-button-container :global(button) {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-1);
  }

  .c-awaiting-syncing-permission,
  .c-awaiting-syncing-permission__text {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .c-awaiting-syncing-permission {
    gap: var(--ds-spacing-4);
  }

  .c-awaiting-syncing-permission :global(.c-awaiting-syncing-permission__title) {
    color: var(--ds-color-neutral-12);
  }

  .c-awaiting-syncing-permission__text {
    max-width: 200px;
    gap: var(--ds-spacing-4);
  }

  .c-awaiting-syncing-permission__text :global(.c-text) {
    text-align: center;
    display: block;
  }

  .c-awaiting-syncing-permission__text a {
    color: var(--ds-color-accent-a11);
    text-decoration: none;
  }

  .c-awaiting-syncing-permission__text a:hover {
    text-decoration: underline;
  }
</style>
