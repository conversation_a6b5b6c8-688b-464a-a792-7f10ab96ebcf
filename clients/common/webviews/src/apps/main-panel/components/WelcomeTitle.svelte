<script lang="ts">
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";

  export let title: string;
  export let subtitle: string;
</script>

<div class="c-sign-in__title">
  <TextAugment size={2} weight="bold" class="c-sign-in__welcome">{title}</TextAugment>
  <TextAugment size={1}>{subtitle}</TextAugment>
</div>

<style>
  .c-sign-in__title {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--ds-spacing-3);
  }

  .c-sign-in__title :global(.c-text) {
    display: block;
    text-align: center;
  }

  .c-sign-in__title :global(.c-sign-in__welcome) {
    color: var(--ds-color-neutral-12);
  }
</style>
