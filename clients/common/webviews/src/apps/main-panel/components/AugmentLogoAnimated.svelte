<script lang="ts">
  import AugmentLogo from "$common-webviews/src/design-system/icons/augment/augment-logo.svelte";
  import Shadow from "./Shadow.svelte";

  export let heightPx = 160;
  export let floatHeight = 20;
  export let animationDuration = 3; // Duration in seconds
  export let showShadow = true;
  export let animated = true;

  // Calculate icon size based on height (default is 140px for 160px height)
  $: iconSizePx = Math.round(heightPx * 0.875);

  // Create style string with CSS variables
  $: style = `
    --augment-logo-height: ${heightPx}px;
    --augment-logo-icon-size: ${iconSizePx}px;
    --augment-logo-float-height: ${floatHeight}px;
    --animation-duration: ${animationDuration}s;
  `;
</script>

<div class="c-augment-logo-animated" class:c-augment-logo-animated--animated={animated} {style}>
  <div class="c-augment-logo-animated__icon">
    <slot>
      <AugmentLogo />
    </slot>
  </div>
  {#if showShadow}
    <div class="c-augment-logo-animated__shadow">
      <Shadow />
    </div>
  {/if}
</div>

<style>
  @keyframes augment-icon-float {
    0% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(var(--augment-logo-float-height, 20px));
    }
    100% {
      transform: translateY(0px);
    }
  }

  @keyframes augment-shadow-pulse {
    0% {
      transform: scale(0.85);
      opacity: 0.3;
    }
    50% {
      transform: scale(1);
      opacity: 0.5;
    }
    100% {
      transform: scale(0.85);
      opacity: 0.3;
    }
  }

  .c-augment-logo-animated {
    --animation-duration: var(--animation-duration, 3s);
    --augment-logo-height: var(--augment-logo-height, 160px);
    --augment-logo-icon-size: var(--augment-logo-icon-size, 140px);
    --augment-logo-float-height: var(--augment-logo-float-height, 20px);
    height: var(--augment-logo-height);
    gap: calc(var(--augment-logo-height) * 0.125);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
  }

  .c-augment-logo-animated__icon :global(svg) {
    width: var(--augment-logo-icon-size);
    height: var(--augment-logo-icon-size);
    color: var(--ds-color-neutral-12);
  }

  .c-augment-logo-animated--animated .c-augment-logo-animated__icon :global(svg) {
    animation: augment-icon-float var(--animation-duration) ease-in-out infinite;
  }

  .c-augment-logo-animated__shadow :global(svg) {
    color: var(--ds-color-neutral-a7);
  }

  .c-augment-logo-animated--animated .c-augment-logo-animated__shadow :global(svg) {
    animation: augment-shadow-pulse var(--animation-duration) ease-in-out infinite;
  }
</style>
