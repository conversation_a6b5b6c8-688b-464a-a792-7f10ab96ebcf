<div class="c-onboarding-layout" class:c-onboarding-layout__has-logo={$$slots.logo}>
  {#if $$slots.logo}
    <div class="c-onboarding-layout__logo"><slot name="logo" /></div>
  {/if}
  <div class="c-onboarding-layout__content">
    <slot />
  </div>
  {#if $$slots.error}
    <div class="c-onboarding-layout__error">
      <slot name="error" />
    </div>
  {/if}
</div>

<style>
  .c-onboarding-layout {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100vh;
    gap: var(--ds-spacing-6);
    padding: 0 var(--ds-spacing-4);
  }

  .c-onboarding-layout__logo {
    height: 40%;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
  }

  .c-onboarding-layout__has-logo .c-onboarding-layout__content {
    height: 60%;
  }
  .c-onboarding-layout__error {
    word-wrap: break-word;
    position: fixed;
    bottom: var(--ds-spacing-4);
    margin: var(--ds-spacing-2);
  }
</style>
