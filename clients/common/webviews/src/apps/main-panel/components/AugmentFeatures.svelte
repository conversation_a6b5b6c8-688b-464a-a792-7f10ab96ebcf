<script lang="ts">
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";

  export let client: string | undefined = undefined;
</script>

{#if client !== undefined}
  <!-- TODO: add links -->
  <div class="c-augment-features">
    <TextAugment size={1}>Augment Features:</TextAugment>
    <TextAugment size={1}>
      <a href="https://docs.augmentcode.com/using-augment/chat">Chat</a> &
      <a href="https://docs.augmentcode.com/using-augment/agent">Agent,</a>
    </TextAugment>
    <TextAugment size={1}>
      <a href="https://docs.augmentcode.com/using-augment/completions">Code Completions</a
      >{#if client !== "intellij"},
        <a href="https://docs.augmentcode.com/using-augment/instructions">Instructions</a>,
      {/if}
    </TextAugment>
    {#if client !== "intellij"}
      <TextAugment size={1}>
        <a href="https://docs.augmentcode.com/using-augment/next-edit">Next Edit</a>
      </TextAugment>
    {/if}
  </div>
{/if}

<style>
  .c-augment-features {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .c-augment-features :global(.c-text) {
    display: block;
    text-align: center;
  }

  .c-augment-features a {
    color: var(--ds-color-accent-a11);
    text-decoration: none;
  }

  .c-augment-features a:hover {
    text-decoration: underline;
  }
</style>
