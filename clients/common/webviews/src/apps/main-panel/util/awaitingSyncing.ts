import { type AnyActionName, DerivedStateName } from "$vscode/src/main-panel/action-cards/types";

/**
 * Filters the list of actions to only include actions that are relevant to the
 * AwaitingSyncingPermission app.
 *
 * @param actions List of incoming actions from the extension
 * @returns List of actions that should be displayed in the AwaitingSyncingPermission app
 */
export function filterActions(actions: Array<AnyActionName>) {
  const visibleActions = new Set<AnyActionName>([
    DerivedStateName.workspaceNotSelected,
    DerivedStateName.workspaceTooLarge,
    DerivedStateName.uploadingHomeDir,
  ]);
  return actions.filter((action) => visibleActions.has(action));
}
