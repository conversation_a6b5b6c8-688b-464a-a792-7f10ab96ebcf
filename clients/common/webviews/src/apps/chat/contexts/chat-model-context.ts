import { getContext, setContext } from "svelte";
import { type ChatModel } from "../models/chat-model";

export function getChatModel(): ChatModel {
  const chatModel = getContext<ChatModel>("chatModel");
  if (!chatModel) {
    console.warn("ChatModel not found in context");
  }
  return chatModel;
}

export function setChatModel(chatModel: ChatModel) {
  setContext("chatModel", chatModel);
  return chatModel;
}
