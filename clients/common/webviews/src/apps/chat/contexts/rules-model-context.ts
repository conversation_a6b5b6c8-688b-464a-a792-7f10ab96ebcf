import { getContext, setContext } from "svelte";
import { type RulesModel } from "../../settings/models/rules-model";

export function getRulesModel(): RulesModel {
  const rulesModel = getContext<RulesModel>("rulesModel");
  if (!rulesModel) {
    console.warn("RulesModel not found in context");
  }
  return rulesModel;
}

export function setRulesModel(rulesModel: RulesModel) {
  setContext("rulesModel", rulesModel);
  return rulesModel;
}
