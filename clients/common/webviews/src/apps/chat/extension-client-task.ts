/**
 * @file extension-client-task.ts
 * This file contains the extension client interface for task-related operations.
 */

import type { AsyncMsgSender } from "$common-webviews/src/common/hosts/async-messaging";
import {
  TaskWebViewMessageType,
  type GetHydratedTaskRequest,
  type GetHydratedTaskResponse,
  type CreateTaskRequest,
  type CreateTaskResponse,
  type UpdateTaskRequest,
  type UpdateTaskResponse,
  type UpdateHydratedTaskRequest,
  type UpdateHydratedTaskResponse,
  type SetCurrentRootTaskUuid,
} from "@augment-internal/sidecar-libs/src/webview-messages/message-types/task-messages";
import type {
  HydratedTask,
  SerializedTask,
  TaskUpdatedBy,
} from "@augment-internal/sidecar-libs/src/agent/task/task-types";

/**
 * Extension client interface for task-related operations.
 * Provides methods for creating, updating and retrieving hydrated tasks.
 */
export class ExtensionClientTask {
  constructor(private readonly _asyncMsgSender: AsyncMsgSender) {}

  /**
   * Gets an hydrated task.
   * @param uuid - The UUID of the task to get
   * @returns A promise that resolves to the hydrated task, or undefined if not found
   */
  getHydratedTask = async (uuid: string): Promise<HydratedTask | undefined> => {
    type ReqT = GetHydratedTaskRequest;
    type ResT = GetHydratedTaskResponse;
    const request: ReqT = {
      type: TaskWebViewMessageType.getHydratedTaskRequest,
      data: { uuid },
    };
    const msg = await this._asyncMsgSender.sendToSidecar<ReqT, ResT>(request, 30000);
    return msg.data.task;
  };

  /**
   * Creates a task.
   * @param name - The name of the task
   * @param description - The description of the task
   * @param parentTaskUuid - The UUID of the parent task, if any
   * @returns A promise that resolves to the UUID of the created task
   */
  createTask = async (
    name: string,
    description: string,
    parentTaskUuid?: string,
  ): Promise<string> => {
    type ReqT = CreateTaskRequest;
    type ResT = CreateTaskResponse;
    const request: ReqT = {
      type: TaskWebViewMessageType.createTaskRequest,
      data: { name, description, parentTaskUuid },
    };
    const msg = await this._asyncMsgSender.sendToSidecar<ReqT, ResT>(request, 30000);
    return msg.data.uuid;
  };

  /**
   * Updates a task.
   * @param uuid - The UUID of the task to update
   * @param updates - The updates to apply to the task
   * @param updatedBy - Who is updating the task
   * @returns A promise that resolves when the update is complete
   */
  updateTask = async (
    uuid: string,
    updates: Partial<SerializedTask>,
    updatedBy: TaskUpdatedBy,
  ): Promise<void> => {
    type ReqT = UpdateTaskRequest;
    type ResT = UpdateTaskResponse;
    const request: ReqT = {
      type: TaskWebViewMessageType.updateTaskRequest,
      data: { uuid, updates, updatedBy },
    };
    await this._asyncMsgSender.sendToSidecar<ReqT, ResT>(request, 30000);
  };

  /**
   * Sets the current root task UUID.
   * This is used to notify the sidecar about the current root task UUID.
   * @param uuid - The UUID of the current root task
   */
  setCurrentRootTaskUuid = (uuid: string): void => {
    type ReqT = SetCurrentRootTaskUuid;
    const request: ReqT = {
      type: TaskWebViewMessageType.setCurrentRootTaskUuid,
      data: { uuid },
    };
    // We don't need to wait for a response, so we don't use await
    this._asyncMsgSender.sendToSidecar(request);
  };

  /**
   * Updates a hydrated task tree by diffing it against the existing tree.
   * This allows updating an entire task tree at once.
   * @param task - The hydrated task tree to update
   * @param updatedBy - Who is updating the task
   * @returns A promise that resolves to an object containing counts of created, updated, and deleted tasks
   */
  updateHydratedTask = async (
    task: HydratedTask,
    updatedBy: TaskUpdatedBy,
  ): Promise<{ created: number; updated: number; deleted: number }> => {
    type ReqT = UpdateHydratedTaskRequest;
    type ResT = UpdateHydratedTaskResponse;
    const request: ReqT = {
      type: TaskWebViewMessageType.updateHydratedTaskRequest,
      data: { task, updatedBy },
    };
    const msg = await this._asyncMsgSender.sendToSidecar<ReqT, ResT>(request, 30000);
    return msg.data;
  };
}
