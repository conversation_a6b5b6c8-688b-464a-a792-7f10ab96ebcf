import { writable } from "svelte/store";
import { describe, it, expect, beforeEach, afterEach, vi } from "vitest";
import { EventTracker, type EventTrackerDependencies } from "./event-tracker";
import { ANALYTICS_EVENTS } from "@augment-internal/sidecar-libs/src/client-interfaces/analytics";
import { type ChatModeType } from "@augment-internal/sidecar-libs/src/chat/chat-types";
import { SendMode } from "../types/send-mode";
import { AgentExecutionMode } from "../models/chat-model";

// Mock extension client
const mockExtensionClient = {
  trackEventWithTypes: vi.fn(),
};

// Mock dependencies
const createMockDependencies = (): EventTrackerDependencies => ({
  extensionClient: mockExtensionClient as any,
  chatModeType: writable<ChatModeType>("localAgent"),
  currentSendMode: writable(SendMode.send),
  agentExecutionMode: writable(AgentExecutionMode.manual),
});

describe("EventTracker", () => {
  let eventTracker: EventTracker;
  let mockDependencies: EventTrackerDependencies;

  beforeEach(() => {
    vi.clearAllMocks();
    mockDependencies = createMockDependencies();
    eventTracker = new EventTracker(mockDependencies);
  });

  describe("Event track", () => {
    const requestId = "test-request-id";
    const modelId = "test-model";

    beforeEach(() => {
      vi.spyOn(performance, "now").mockReturnValue(2000);
    });

    afterEach(() => {
      vi.restoreAllMocks();
    });

    it("should track first token latency correctly", () => {
      eventTracker.trackEvent(ANALYTICS_EVENTS.MESSAGE_SEND_TIMING, {
        requestId,
        timeToFirstTokenMs: 300,
        timeToLastTokenMs: 500,
        responseLength: 100,
        modelId,
      });

      expect(mockExtensionClient.trackEventWithTypes).toHaveBeenCalledWith(
        ANALYTICS_EVENTS.MESSAGE_SEND_TIMING,
        {
          requestId,
          chatMode: "localAgent",
          timeToFirstTokenMs: 300,
          timeToLastTokenMs: 500,
          responseLength: 100,
          modelId,
          agentExecutionMode: AgentExecutionMode.manual,
          sendMode: SendMode.send,
        },
      );
    });
  });
});
