<script lang="ts">
  import CollapsibleAugment from "$common-webviews/src/design-system/components/CollapsibleAugment/CollapsibleAugment.svelte";
  import ChevronDown from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/chevron-down.svg?component";
  import ChevronRight from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/chevron-right.svg?component";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import { removeScopeFromContent } from "@augment-internal/sidecar-libs/src/agent/memory/memory-parser";

  export let memoryContent: string;
  export let headerText: string;
  export let collapsed: boolean = false;

  $: parsedMemoryContent = removeScopeFromContent(memoryContent);

  // Get collapsible context after component is mounted
  let toggle: (() => void) | undefined = undefined;
</script>

<CollapsibleAugment bind:collapsed bind:toggle>
  <!-- svelte-ignore a11y-click-events-have-key-events -->
  <div
    slot="header"
    role="button"
    tabindex="0"
    class="c-memory-save-card__header"
    on:click={toggle || (() => {})}
  >
    {#if collapsed}
      <ChevronRight />
    {:else}
      <ChevronDown />
    {/if}
    <div class="c-memory-save-card__header-content">
      <TextAugment size={1} weight="medium" color="secondary">{headerText}</TextAugment>
      <TextAugment size={1}>
        {parsedMemoryContent}
      </TextAugment>
    </div>
  </div>

  <slot />
</CollapsibleAugment>

<style>
  .c-memory-save-card__header {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-2);
    padding: var(--ds-spacing-2) var(--ds-spacing-3);
    cursor: pointer;
    transition: background-color 0.2s ease;
    min-width: 0;
    overflow: hidden;
  }

  .c-memory-save-card__header:hover {
    background-color: var(--ds-color-neutral-9);
  }

  .c-memory-save-card__header :global(.c-memory-save-card__header-content) {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    min-width: 0;
    max-width: 100%;
  }
</style>
