<script lang="ts">
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import MemoryFeedbackPanel from "../MemoryAugment/MemoryFeedbackPanel.svelte";
  import { type AgentMemoryData } from "../../models/memory-utils";
  import {
    extractScopeFromContent,
    removeScopeFromContent,
  } from "@augment-internal/sidecar-libs/src/agent/memory/memory-parser";
  import { getChatModel } from "../../chat-context";

  export let memoryData: AgentMemoryData;
  export let requestId: string | undefined;

  const chatModel = getChatModel();
  $: parsedMemoryScope = memoryData ? extractScopeFromContent(memoryData.memory) : undefined;
  $: parsedMemoryContent = memoryData ? removeScopeFromContent(memoryData.memory) : undefined;
</script>

<div class="memory-content">
  {#if parsedMemoryScope}
    <div>
      <TextAugment size={1} weight="light" color="secondary">Scope</TextAugment>
    </div>
    <div>
      <TextAugment size={1} weight="light">
        {parsedMemoryScope}
      </TextAugment>
    </div>
  {/if}
  <div>
    <TextAugment size={1} weight="light" color="secondary">Content</TextAugment>
  </div>
  <div>
    <TextAugment size={1} weight="light">{parsedMemoryContent}</TextAugment>
  </div>
  {#if requestId}
    <MemoryFeedbackPanel {memoryData} {chatModel} {requestId} />
  {/if}
</div>

<style>
  .memory-content {
    display: flex;
    flex-direction: column;
    background: var(--ds-color-surface-primary);
    border: 1px solid var(--ds-color-border-primary);
    border-radius: var(--ds-border-radius-1);
    padding: var(--ds-spacing-2);
    gap: var(--ds-spacing-2);
  }
</style>
