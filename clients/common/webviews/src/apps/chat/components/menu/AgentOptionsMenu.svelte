<script lang="ts">
  import DropdownMenuAugment from "$common-webviews/src/design-system/components/DropdownMenuAugment/index";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import DotsHorizontal from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/ellipsis.svg?component";
  import ClipboardCopy from "$common-webviews/src/design-system/icons/clipboard-copy.svelte";
  import { writable } from "svelte/store";
  import Thumbtack from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/thumbtack.svg?component";
  import { RemoteAgentsModel } from "$common-webviews/src/apps/remote-agent-manager/models/remote-agents-model";
  import { getContext } from "svelte";
  import PencilIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/pen-to-square.svg?component";
  import Share from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/share-nodes.svg?component";
  import Trash from "$common-webviews/src/design-system/icons/trash.svelte";

  export let agentId: string;
  export let selectedBranch: string | undefined = undefined;
  export let isPinned: boolean = false;
  export let togglePinned: (() => void) | undefined = undefined;
  export let enableRename: boolean = false;
  export let isEditing: boolean = false;
  export let onDelete: (() => Promise<void>) | undefined = undefined;

  let requestClose: () => void = () => {};
  const agentIdCopyTextMap = writable(new Map<string, string>());
  const shareLinkCopyTextMap = writable(new Map<string, string>());

  const remoteAgentsModel = getContext<RemoteAgentsModel>(RemoteAgentsModel.key);

  function copyAgentId() {
    navigator.clipboard.writeText(agentId);
    agentIdCopyTextMap.update((map) => {
      map.set(agentId, "Copied!");
      return map;
    });
    setTimeout(() => {
      agentIdCopyTextMap.update((map) => {
        map.delete(agentId);
        return map;
      });
    }, 1000);
  }

  async function copyShareLink() {
    try {
      const url = await remoteAgentsModel?.getConversationUrl(agentId);
      if (url) {
        navigator.clipboard.writeText(url);
      }
      shareLinkCopyTextMap.update((map) => {
        map.set(agentId, "Link copied!");
        return map;
      });
      setTimeout(() => {
        shareLinkCopyTextMap.update((map) => {
          map.delete(agentId);
          return map;
        });
      }, 1000);
    } catch (err) {
      console.error("Failed to get conversation URL: ", err);
    }
  }
</script>

<DropdownMenuAugment.Root nested={false} bind:requestClose>
  <DropdownMenuAugment.Trigger>
    <IconButtonAugment variant="ghost-block" color="neutral" size={1} title="Agent options">
      <DotsHorizontal />
    </IconButtonAugment>
  </DropdownMenuAugment.Trigger>
  <DropdownMenuAugment.Content size={1} side="bottom" align="end">
    {#if enableRename}
      <DropdownMenuAugment.Item
        onSelect={() => {
          setTimeout(() => {
            isEditing = true;
          }, 20);
        }}
      >
        <PencilIcon slot="iconLeft" />
        Rename
      </DropdownMenuAugment.Item>
    {/if}
    <!-- Pin/Unpin option -->
    {#if togglePinned}
      <DropdownMenuAugment.Item
        title="Pin or unpin agent"
        onSelect={() => {
          togglePinned();
        }}
      >
        <svelte:fragment slot="iconLeft">
          <Thumbtack class="pin-icon" />
        </svelte:fragment>
        {isPinned ? "Unpin" : "Pin"}
      </DropdownMenuAugment.Item>
    {/if}

    <!-- copy agent id -->
    <DropdownMenuAugment.Item
      onSelect={() => {
        copyAgentId();
      }}
    >
      <ClipboardCopy slot="iconLeft" />
      {#if $agentIdCopyTextMap.get(agentId)}
        {$agentIdCopyTextMap.get(agentId)}
      {:else}
        Copy ID
      {/if}
    </DropdownMenuAugment.Item>

    <!-- copy share link -->
    <DropdownMenuAugment.Item
      onSelect={() => {
        copyShareLink();
        requestClose();
      }}
    >
      <Share slot="iconLeft" />
      {#if $shareLinkCopyTextMap.get(agentId)}
        {$shareLinkCopyTextMap.get(agentId)}
      {:else}
        Share
      {/if}
    </DropdownMenuAugment.Item>

    {#if onDelete}
      <DropdownMenuAugment.Item
        color="error"
        onSelect={() => {
          onDelete?.();
        }}
      >
        <svelte:fragment slot="iconLeft">
          <div class="trash-icon">
            <Trash />
          </div>
        </svelte:fragment>
        Delete
      </DropdownMenuAugment.Item>
    {/if}

    {#if selectedBranch}
      <DropdownMenuAugment.Separator></DropdownMenuAugment.Separator>
      <DropdownMenuAugment.Label>Branch</DropdownMenuAugment.Label>
      <DropdownMenuAugment.Item disabled>{selectedBranch}</DropdownMenuAugment.Item>
    {/if}
  </DropdownMenuAugment.Content>
</DropdownMenuAugment.Root>
