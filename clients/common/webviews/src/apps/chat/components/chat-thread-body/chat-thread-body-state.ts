import { derived, type Readable } from "svelte/store";
import type { ChatThreadBodyState } from "./chat-thread-body-model";
import type { RemoteAgentsModel } from "../../../remote-agent-manager/models/remote-agents-model";
import type { ChatAgentEdit } from "@augment-internal/sidecar-libs/src/webview-messages/message-types/agent-messages";

/**
 * Dependencies for creating ChatThreadBodyState reactive store.
 * Uses options-object pattern for explicit, minimal constructor.
 */
export interface ChatThreadBodyStateDeps {
  /** Whether current conversation is agentic */
  isCurrConversationAgentic: Readable<boolean>;
  /** Remote agents model */
  remoteAgentsModel: Readable<RemoteAgentsModel>;
  /** Whether task list can be shown */
  canShowTaskList: Readable<boolean>;
  /** Target checkpoint summary */
  targetCheckpointSummary: Readable<ChatAgentEdit[]>;
  /** Total checkpoint count */
  totalCheckpointCount: Readable<number>;
}

/**
 * Creates reactive state controlling chat thread body tab visibility.
 * @param deps - Dependencies for creating the state
 * @returns Readable store with chat thread body state
 */
export function createChatThreadBodyState({
  isCurrConversationAgentic,
  remoteAgentsModel,
  canShowTaskList,
  targetCheckpointSummary,
  totalCheckpointCount,
}: ChatThreadBodyStateDeps): Readable<ChatThreadBodyState> {
  return derived(
    [
      isCurrConversationAgentic,
      remoteAgentsModel,
      canShowTaskList,
      targetCheckpointSummary,
      totalCheckpointCount,
    ],
    ([isAgentic, remoteState, canShowTasks, checkpointSummary, checkpointCount]: [
      boolean,
      RemoteAgentsModel,
      boolean,
      ChatAgentEdit[],
      number,
    ]): ChatThreadBodyState => {
      const isRemoteActive = remoteState?.isActive || false;
      const isNormalAgent = isAgentic && !isRemoteActive;

      // Check if there are actual agent edits available (non-zero line deltas)
      const filesWithEdits = (checkpointSummary || []).filter((file) => {
        const summary = file.changesSummary;
        return (
          !!summary && ((summary.totalAddedLines ?? 0) > 0 || (summary.totalRemovedLines ?? 0) > 0)
        );
      });

      const hasAgentEdits =
        isNormalAgent && isAgentic && checkpointCount > 0 && filesWithEdits.length > 0;

      return {
        showMessageHistory: isNormalAgent,
        showTasks: isNormalAgent && canShowTasks,
        showAgentEdits: hasAgentEdits,
      };
    },
  );
}
