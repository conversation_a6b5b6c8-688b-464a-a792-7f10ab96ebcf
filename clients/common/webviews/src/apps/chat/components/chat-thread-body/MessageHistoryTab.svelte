<script lang="ts">
  import { type ChatModel } from "../../models/chat-model";
  import { type OnboardingWorkspaceModel } from "../../models/onboarding-workspace-model";
  import { type RemoteAgentsModel } from "../../../remote-agent-manager/models/remote-agents-model";
  import MessageList from "../conversation/MessageList.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";

  export let chatModel: ChatModel;
  export let onboardingWorkspaceModel: OnboardingWorkspaceModel;
  export let remoteAgentsModel: RemoteAgentsModel;

  $: conversationModel = $chatModel.currentConversationModel;
  $: remoteAgentId = $remoteAgentsModel.currentAgentId;
  $: isRemoteAgent = $remoteAgentsModel.isActive;
  $: hasRemoteAgentChatHistory =
    isRemoteAgent && $remoteAgentsModel.getCurrentChatHistory()?.length;
  $: hasLogs = isRemoteAgent && $remoteAgentsModel.agentSetupLogs?.steps.length !== 0;
  $: isEmptyConversation = isRemoteAgent
    ? !hasRemoteAgentChatHistory && !hasLogs
    : $conversationModel.chatHistory.length === 0;
  $: key = `${$conversationModel.id}-${isRemoteAgent ? remoteAgentId : ""}`;
</script>

<!-- Message History Tab - displays full conversation history -->
<div class="c-message-history-tab">
  {#if isEmptyConversation}
    <div class="c-message-history-tab__empty">
      <TextAugment size={1} color="neutral">No messages in this conversation yet</TextAugment>
    </div>
  {:else}
    {#key key}
      <div class="c-message-history-tab__message-list">
        <MessageList {chatModel} {onboardingWorkspaceModel} />
      </div>
    {/key}
  {/if}
</div>

<style>
  .c-message-history-tab {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
  }

  .c-message-history-tab__empty {
    display: flex;
    align-items: center;
    justify-content: center;
    flex: 1;
    padding: var(--ds-spacing-4);
  }

  .c-message-history-tab__message-list {
    flex: 1;
    overflow: hidden;
  }

  /* Override MessageList styles for tab view */
  .c-message-history-tab__message-list :global(.c-msg-list-container) {
    height: 100%;
  }

  .c-message-history-tab__message-list :global(.c-msg-list) {
    max-height: 100%;
    overflow-y: auto;
  }
</style>
