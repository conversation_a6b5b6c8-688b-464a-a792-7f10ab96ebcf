<script lang="ts">
  import { getContext } from "svelte";
  import {
    CurrentConversationTaskStore,
    type ICurrentConversationTaskStore,
  } from "../../models/task-store";
  import { type AgentConversationModel } from "../../models/agent-conversation-model";
  import type { ChatModel } from "../../models/chat-model";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import { TooltipTriggerOn } from "$common-webviews/src/design-system/_primitives/TooltipAugment/types";
  import { TaskState } from "@augment-internal/sidecar-libs/src/agent/task/task-types";
  import TaskTree from "../tasks/task-tree/TaskTree.svelte";
  import { preprocessTaskTreeVisibility } from "../tasks/utils/task-visibility-utils";
  import TaskListHeaderActions from "../tasks/task-list/TaskListHeaderActions.svelte";
  import TaskFilterDropdown, {
    type TaskFilter,
    type TaskFilterOption,
  } from "../tasks/task-filters/TaskFilterDropdown.svelte";
  import { getTaskProgress, getTaskStatusLabel } from "../tasks/utils/task-status-utils";
  import {
    TaskListAction,
    TaskListActionTrigger,
    AgentSessionEventName,
  } from "@augment-internal/sidecar-libs/src/metrics/types";
  import Play from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/play-augment.svg?component";
  import Plus from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/plus.svg?component";

  // Get required context models
  const agentConversationModel = getContext<AgentConversationModel>("agentConversationModel");
  const taskStore = getContext<ICurrentConversationTaskStore>(CurrentConversationTaskStore.key);
  const chatModel = getContext<ChatModel>("chatModel");

  // Extract required reactive properties
  const { isCurrConversationAgentic } = agentConversationModel;
  const { rootTask } = taskStore;

  // Filter state - default to showing all task states
  let activeTaskFilter: Set<TaskState> = new Set(Object.values(TaskState));

  // Count tasks by state (similar to TaskList.svelte)
  function countAllTasks(task: any) {
    if (!task) return { notStarted: 0, inProgress: 0, completed: 0, cancelled: 0 };

    let counts = { notStarted: 0, inProgress: 0, completed: 0, cancelled: 0 };

    function countTask(t: any) {
      if (t.state === TaskState.NOT_STARTED) counts.notStarted++;
      else if (t.state === TaskState.IN_PROGRESS) counts.inProgress++;
      else if (t.state === TaskState.COMPLETE) counts.completed++;
      else if (t.state === TaskState.CANCELLED) counts.cancelled++;

      if (t.subTasksData) {
        t.subTasksData.forEach(countTask);
      }
    }

    if (task.subTasksData) {
      task.subTasksData.forEach(countTask);
    }

    return counts;
  }

  // Count tasks by state
  $: allTasks = countAllTasks($rootTask);
  $: notStartedTasksCount = allTasks.notStarted;
  $: inProgressTasksCount = allTasks.inProgress;
  $: completedTasksCount = allTasks.completed;
  $: cancelledTasksCount = allTasks.cancelled;

  // Check if there are any tasks to determine if "Play all" button should be shown
  $: hasAnyTasks = $rootTask?.subTasksData && $rootTask.subTasksData.length > 0;

  // Create filter options based on task counts
  $: taskFilters = [
    {
      value: TaskState.NOT_STARTED as TaskFilter,
      label: getTaskStatusLabel(TaskState.NOT_STARTED),
      count: notStartedTasksCount,
    },
    {
      value: TaskState.IN_PROGRESS as TaskFilter,
      label: getTaskStatusLabel(TaskState.IN_PROGRESS),
      count: inProgressTasksCount,
    },
    {
      value: TaskState.COMPLETE as TaskFilter,
      label: getTaskStatusLabel(TaskState.COMPLETE),
      count: completedTasksCount,
    },
    {
      value: TaskState.CANCELLED as TaskFilter,
      label: getTaskStatusLabel(TaskState.CANCELLED),
      count: cancelledTasksCount,
    },
  ] as TaskFilterOption[];

  // Preprocess the task tree with visibility flags
  $: preprocessedRootTask = $rootTask
    ? preprocessTaskTreeVisibility($rootTask, activeTaskFilter)
    : undefined;

  // Handler for adding new task
  async function handleAddNewTask() {
    if ($rootTask) {
      // Emit user-triggered addSubtask event
      const taskProgress = getTaskProgress($rootTask);
      const taskListUsageData = {
        action: TaskListAction.addSubtask,
        totalTasksCount: taskProgress.total,
        triggeredBy: TaskListActionTrigger.user,
      };

      chatModel.currentConversationModel.extensionClient.reportAgentSessionEvent({
        eventName: AgentSessionEventName.taskListUsage,
        conversationId: chatModel.currentConversationModel.id,
        eventData: {
          taskListUsageData,
        },
      });

      const uuid = await taskStore.createTask("", "", $rootTask.uuid);
      // Focus the new task
      setTimeout(() => {
        const taskItem = document.getElementById(`task-${uuid}`);
        const taskName = taskItem?.querySelector(".c-task-tree-item__name-editable");
        const input = taskName?.querySelector("input");
        input?.focus();
      }, 100);
    }
  }
</script>

<!-- Task List Tab - core content with header actions -->
{#if $isCurrConversationAgentic && $rootTask}
  <div class="c-task-list-tab">
    <!-- Header with title and actions -->
    <div class="c-task-list-tab__header">
      <div class="c-task-list-tab__title">
        <TextAugment size={2} weight="medium" color="primary">Task List</TextAugment>
      </div>
      <div class="c-task-list-tab__actions">
        {#if hasAnyTasks}
          <TextTooltipAugment content="Run All Tasks" triggerOn={[TooltipTriggerOn.Hover]}>
            <IconButtonAugment
              size={1}
              variant="ghost-block"
              color="neutral"
              disabled={false}
              on:click={() => {
                // Emit user-triggered runAllTasks event
                const taskProgress = getTaskProgress($rootTask);
                const taskListUsageData = {
                  action: TaskListAction.runAllTasks,
                  totalTasksCount: taskProgress.total,
                  triggeredBy: TaskListActionTrigger.user,
                };

                chatModel.currentConversationModel.extensionClient.reportAgentSessionEvent({
                  eventName: AgentSessionEventName.taskListUsage,
                  conversationId: chatModel.currentConversationModel.id,
                  eventData: {
                    taskListUsageData,
                  },
                });

                taskStore.runAllTasks();
              }}
            >
              <Play />
            </IconButtonAugment>
          </TextTooltipAugment>
        {/if}
        <TextTooltipAugment content="Add New Task" triggerOn={[TooltipTriggerOn.Hover]}>
          <IconButtonAugment
            size={1}
            variant="ghost-block"
            color="neutral"
            disabled={!$rootTask}
            on:click={handleAddNewTask}
          >
            <Plus />
          </IconButtonAugment>
        </TextTooltipAugment>
        <TaskFilterDropdown {taskFilters} bind:activeTaskFilter />
        <TaskListHeaderActions />
      </div>
    </div>

    <!-- Task Content -->
    {#if preprocessedRootTask?.isVisible && preprocessedRootTask.subTasksData?.length}
      <div class="c-tasks-list">
        <div class="c-tasks-section">
          <TaskTree task={preprocessedRootTask} {taskStore} editable={true} />
        </div>
      </div>
    {:else}
      <div class="c-tasks-list c-tasks-list--empty">
        <TextAugment size={1} color="neutral">No tasks to show</TextAugment>
      </div>
    {/if}
  </div>
{/if}

<style>
  .c-task-list-tab {
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .c-task-list-tab__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid var(--ds-color-neutral-a3);
    flex-shrink: 0;
  }

  .c-task-list-tab__title {
    flex-shrink: 0;
  }

  .c-task-list-tab__actions {
    display: flex;
    align-items: center;
  }

  .c-tasks-list {
    flex: 1;
    overflow-y: auto;
    min-height: 0;
  }

  .c-tasks-section {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-1);
  }

  .c-tasks-list--empty {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: var(--ds-spacing-4);
  }
</style>
