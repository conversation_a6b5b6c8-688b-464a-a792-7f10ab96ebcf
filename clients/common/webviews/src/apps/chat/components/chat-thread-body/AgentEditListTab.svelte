<script lang="ts">
  import { getContext } from "svelte";
  import type { ChatModel } from "../../models/chat-model";
  import { type IQualifiedPathName } from "@augment-internal/sidecar-libs/src/workspace/workspace-types";
  import { type AgentConversationModel } from "../../models/agent-conversation-model";
  import AgentEditBottomControls from "../agent-edits/AgentEditBottomControls.svelte";
  import AgentEditListItem from "../agent-edits/AgentEditListItem.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import type { CheckpointStore } from "../../models/checkpoint-store";

  // Get required context models
  const chatModel = getContext<ChatModel>("chatModel");
  const agentConversationModel = getContext<AgentConversationModel>("agentConversationModel");
  const checkpointStore = getContext<CheckpointStore>("checkpointStore");

  // Extract required reactive properties
  const { targetCheckpointSummary, totalCheckpointCount } = checkpointStore;
  const { isCurrConversationAgentic } = agentConversationModel;

  // Reactive state for files with edits - filter for meaningful changes only
  $: filesWithEditsToShow = ($targetCheckpointSummary || []).filter(
    (file) =>
      file.changesSummary &&
      (file.changesSummary.totalAddedLines > 0 || file.changesSummary.totalRemovedLines > 0),
  );

  // Event handlers
  function handleFileClick(qualifiedPathName: IQualifiedPathName) {
    chatModel.extensionClient.openFile({
      repoRoot: qualifiedPathName.rootPath,
      pathName: qualifiedPathName.relPath,
      allowOutOfWorkspace: true,
    });
  }

  function handleReviewClick(qualifiedPathName: IQualifiedPathName) {
    // Use the conversation's baseline timestamp from extraData instead of 0
    const baselineTimestamp = agentConversationModel.getBaselineTimestamp();

    // Use enhanced showAgentReview with native diff viewer support
    void chatModel.extensionClient.showAgentReview(
      qualifiedPathName,
      baselineTimestamp,
      undefined,
      false, // explicitly set retainFocus to false for View Diff button
      true, // use native diff viewer when in VSCode environment
    );
  }

  function handleRevertClick(qualifiedPathName: IQualifiedPathName) {
    // Use the conversation's baseline timestamp from extraData instead of 0
    const baselineTimestamp = agentConversationModel.getBaselineTimestamp();
    void checkpointStore.revertDocumentToTimestamp(qualifiedPathName, baselineTimestamp);
  }
</script>

<!-- Agent Edit List Tab - core content without collapsible wrapper -->
<!-- TODO: Replace with GitAgentEditTab.svelte when git-based system is ready -->
{#if $isCurrConversationAgentic && $totalCheckpointCount > 0 && filesWithEditsToShow.length > 0}
  <div class="c-agent-edit-list-tab">
    {#if filesWithEditsToShow.length > 0}
      <div class="c-edits-list">
        <div class="c-edits-section">
          {#each filesWithEditsToShow as file}
            <AgentEditListItem
              qualifiedPathName={file.qualifiedPathName}
              lineChanges={file.changesSummary}
              onClickFile={() => handleFileClick(file.qualifiedPathName)}
              onClickReview={() => handleReviewClick(file.qualifiedPathName)}
              onClickRevert={() => handleRevertClick(file.qualifiedPathName)}
            />
          {/each}
        </div>
      </div>
    {:else}
      <div class="c-edits-list c-edits-list--empty">
        <TextAugment size={1} color="neutral">No changes to show</TextAugment>
      </div>
    {/if}

    <div class="c-agent-edit-list-tab__footer">
      <AgentEditBottomControls />
    </div>
  </div>
{/if}

<style>
  .c-agent-edit-list-tab {
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .c-edits-list {
    flex: 1;
    overflow-y: auto;
    min-height: 0;
  }

  .c-edits-section {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-1);
  }

  .c-edits-list--empty {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: var(--ds-spacing-4);
  }

  .c-agent-edit-list-tab__footer {
    flex-shrink: 0;
    border-top: 1px solid var(--ds-color-neutral-a6);

    & :global(.c-bottom-controls) {
      padding: 0;
      padding-top: var(--ds-spacing-1);
    }
  }
</style>
