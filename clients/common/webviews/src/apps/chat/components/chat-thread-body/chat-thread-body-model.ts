import { writable, derived, type Readable, type Writable, readonly } from "svelte/store";
import { getContext, setContext } from "svelte";
import type { IChatFlags } from "../../models/types";

/**
 * Available tabs in the chat thread body.
 */
export enum ChatThreadBodyTab {
  agentEdits = "agent-edits",
  tasks = "tasks",
  messageHistory = "message-history",
  agentGitEdits = "agent-git-edits",
  memory = "memory",
}

/**
 * Configuration for tab persistence and defaults.
 */
export interface ChatThreadBodyConfig {
  defaultTab?: ChatThreadBodyTab;
}

/**
 * External state that determines tab availability.
 */
export interface ChatThreadBodyState {
  showAgentEdits: boolean;
  showTasks: boolean;
  showMessageHistory: boolean;
}

/**
 * Model for managing chat thread body tab state.
 */
export class ChatThreadBodyModel {
  // Static context key kept local (symbol avoids collisions)
  // eslint-disable-next-line @typescript-eslint/naming-convention
  private static readonly CTX_KEY: unique symbol = Symbol("chat-thread-body-model");

  static setInContext(model: ChatThreadBodyModel): ChatThreadBodyModel {
    setContext(ChatThreadBodyModel.CTX_KEY, model);
    return model;
  }
  static getFromContext(): ChatThreadBodyModel {
    const m = getContext<ChatThreadBodyModel>(ChatThreadBodyModel.CTX_KEY);
    if (!m) {
      throw new Error("ChatThreadBodyModel not found in context. Did you forget to set it?");
    }
    return m;
  }
  static tryGetFromContext(): ChatThreadBodyModel | undefined {
    try {
      return getContext<ChatThreadBodyModel>(ChatThreadBodyModel.CTX_KEY);
    } catch {
      return undefined;
    }
  }
  private _activeTab: Writable<ChatThreadBodyTab | undefined>;
  private _config: Writable<ChatThreadBodyConfig>;

  public readonly activeTab: Readable<ChatThreadBodyTab | undefined>;
  public readonly config: Readable<ChatThreadBodyConfig>;
  public readonly availableTabs: Readable<ChatThreadBodyTab[]>;
  public readonly shouldShowTabs: Readable<boolean>;

  constructor(deps: ChatThreadBodyModelDeps) {
    const { initialConfig, chatFlags, externalState, isAgentConversation } = deps;
    this._config = writable(initialConfig);

    this._activeTab = writable(initialConfig.defaultTab);

    this.activeTab = readonly(this._activeTab);
    this.config = readonly(this._config);

    this.availableTabs = derived(
      [externalState, chatFlags, isAgentConversation],
      ([state, flags, $isAgentConversation]) => {
        const tabs: ChatThreadBodyTab[] = [];
        if (state.showMessageHistory) tabs.push(ChatThreadBodyTab.messageHistory);
        if (state.showTasks) tabs.push(ChatThreadBodyTab.tasks);
        if (state.showAgentEdits) tabs.push(ChatThreadBodyTab.agentEdits);
        if ($isAgentConversation && flags.enableAgentGitTracker)
          tabs.push(ChatThreadBodyTab.agentGitEdits);
        return tabs;
      },
    );

    this.shouldShowTabs = derived(this.availableTabs, (tabs) => tabs.length > 0);
    this.setupTabAutoSelection();
  }

  /**
   * Set the active tab
   */
  setActiveTab(tab: ChatThreadBodyTab | undefined): void {
    this._activeTab.set(tab);
  }

  /**
   * Get the current active tab value synchronously
   */
  getCurrentActiveTab(): ChatThreadBodyTab | undefined {
    let current: ChatThreadBodyTab | undefined;
    this.activeTab.subscribe((tab) => (current = tab))();
    return current;
  }

  /**
   * Get the current available tabs synchronously
   */
  getCurrentAvailableTabs(): ChatThreadBodyTab[] {
    let current: ChatThreadBodyTab[] = [];
    this.availableTabs.subscribe((tabs) => (current = tabs))();
    return current;
  }

  /**
   * Switch to the next available tab
   */
  switchToNextTab(): void {
    const currentTab = this.getCurrentActiveTab();
    const availableTabs = this.getCurrentAvailableTabs();

    if (availableTabs.length === 0) {
      this.setActiveTab(undefined);
      return;
    }

    const currentIndex = currentTab ? availableTabs.indexOf(currentTab) : -1;
    const nextIndex = (currentIndex + 1) % availableTabs.length;
    this.setActiveTab(availableTabs[nextIndex]);
  }

  /**
   * Switch to the previous available tab
   */
  switchToPreviousTab(): void {
    const currentTab = this.getCurrentActiveTab();
    const availableTabs = this.getCurrentAvailableTabs();

    if (availableTabs.length === 0) {
      this.setActiveTab(undefined);
      return;
    }

    const currentIndex = currentTab ? availableTabs.indexOf(currentTab) : -1;
    const prevIndex = currentIndex <= 0 ? availableTabs.length - 1 : currentIndex - 1;
    this.setActiveTab(availableTabs[prevIndex]);
  }

  /**
   * Setup automatic tab selection when availability changes
   */
  private setupTabAutoSelection(): void {
    // Subscribe to changes in available tabs and active tab
    derived([this.availableTabs, this.activeTab], ([available, active]) => ({
      available,
      active,
    })).subscribe(({ available, active }) => {
      // If current tab is no longer available, switch to first available
      if (active && !available.includes(active)) {
        if (available.length > 0) {
          this.setActiveTab(available[0]);
        } else {
          this.setActiveTab(undefined);
        }
      }
      // If no active tab but tabs are available, select first one
      else if (!active && available.length > 0) {
        this.setActiveTab(available[0]);
      }
    });
  }

  /**
   * Destroy the model and cleanup subscriptions
   */
  destroy(): void {
    // Svelte stores automatically cleanup subscriptions when components are destroyed
    // No explicit cleanup needed for derived stores
  }
}

/**
 * Constructor dependencies for ChatThreadBodyModel.
 */
export interface ChatThreadBodyModelDeps {
  initialConfig: ChatThreadBodyConfig;
  chatFlags: Readable<IChatFlags>;
  externalState: Readable<ChatThreadBodyState>;
  isAgentConversation: Readable<boolean>;
}

/**
 * Creates a ChatThreadBodyModel with sensible defaults.
 */
export function createChatThreadBodyModel(
  chatFlags: Readable<IChatFlags>,
  externalState: Readable<ChatThreadBodyState>,
  isAgentConversation: Readable<boolean>,
  overrides: Partial<ChatThreadBodyConfig> = {},
): ChatThreadBodyModel {
  const defaultConfig: ChatThreadBodyConfig = {
    defaultTab: ChatThreadBodyTab.messageHistory,
    ...overrides,
  };

  return new ChatThreadBodyModel({
    initialConfig: defaultConfig,
    chatFlags,
    externalState,
    isAgentConversation,
  });
}
