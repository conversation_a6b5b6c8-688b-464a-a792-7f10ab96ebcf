<script lang="ts">
  import { getRemoteAgentsAction } from "../utils/chat-input-context";
  import { TooltipTriggerOn } from "$common-webviews/src/design-system/_primitives/TooltipAugment/types";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import { getContext } from "svelte";
  import { RemoteAgentsModel } from "../../remote-agent-manager/models/remote-agents-model";
  import { GitReferenceModel } from "../../remote-agent-manager/models/git-reference-model";
  import { RemoteAgentWorkspaceStatus } from "@augment-internal/sidecar-libs/src/remote-agent/remote-agent-types";
  import { RemoteAgentSetupWindowAction } from "@augment-internal/sidecar-libs/src/metrics/types";
  import type { ChatModel } from "../models/chat-model";

  // Get required models from context
  const remoteAgentsModel = getContext<RemoteAgentsModel>(RemoteAgentsModel.key);
  const gitRefModel = getContext<GitReferenceModel>(GitReferenceModel.key);
  const chatModel = getContext<ChatModel>("chatModel");

  // Reactive state from remote agents model
  $: commitRef = $remoteAgentsModel.newAgentDraft?.commitRef ?? null;
  $: selectedBranch = $remoteAgentsModel.newAgentDraft?.selectedBranch ?? null;
  $: selectedSetupScript = $remoteAgentsModel.newAgentDraft?.setupScript ?? null;
  $: isCreatingAgent = $remoteAgentsModel.isCreatingAgent;

  // Additional state for comprehensive validation (from RemoteAgentSetup.svelte)
  let commitRefErrorMessage = "";
  let isCommitRefLoading = false;

  // Agent limit validation (similar to AgentLimitCard logic)
  $: totalAgentLimitReached =
    !!$remoteAgentsModel.maxRemoteAgents &&
    $remoteAgentsModel.agentOverviews.length >= $remoteAgentsModel.maxRemoteAgents;
  $: activeAgentLimitReached =
    !!$remoteAgentsModel.maxActiveRemoteAgents &&
    $remoteAgentsModel.agentOverviews.filter(
      (a) => a.workspace_status === RemoteAgentWorkspaceStatus.workspaceRunning,
    ).length >= $remoteAgentsModel.maxActiveRemoteAgents;

  $: agentLimitErrorMessage = (() => {
    if (totalAgentLimitReached) {
      return `You have reached the maximum number of ${$remoteAgentsModel.maxRemoteAgents} remote agents.`;
    } else if (activeAgentLimitReached) {
      return `You have reached the maximum number of ${$remoteAgentsModel.maxActiveRemoteAgents} active remote agents.`;
    }
    return "";
  })();

  // Error message and disabled state for create button
  // This mirrors the comprehensive logic from RemoteAgentSetup.svelte
  $: createButtonErrorMessage = (() => {
    const hasValidRepo = commitRef?.github_commit_ref?.repository_url;
    const hasValidBranch = selectedBranch?.name;

    // Check for remote agent creation errors
    const creationError = $remoteAgentsModel.remoteAgentCreationError;

    // Additional check: ensure we're not in a loading state where branch data might be incomplete
    const isDataComplete = !isCommitRefLoading && hasValidRepo && hasValidBranch;

    return (
      commitRefErrorMessage ||
      agentLimitErrorMessage ||
      creationError ||
      (isCommitRefLoading ? "Loading repos and branches..." : "") ||
      (!hasValidRepo && "Please select a repository") ||
      (!hasValidBranch && "Please select a branch") ||
      (!isDataComplete && hasValidRepo && hasValidBranch ? "Loading branch data..." : "") ||
      ""
    );
  })();

  $: isCreateButtonDisabled = !!createButtonErrorMessage;

  // Create agent action
  const createAgent = getRemoteAgentsAction(
    remoteAgentsModel,
    chatModel.currentConversationModel,
    gitRefModel,
  );

  async function handleCreateAgent() {
    // Log click create agent event (don't let this block the rest of the logic)
    try {
      await remoteAgentsModel.reportRemoteAgentSetupWindowEvent(
        RemoteAgentSetupWindowAction.clickCreateAgent,
      );
    } catch (error) {
      console.error("Failed to report click create agent event:", error);
    }

    try {
      // Clear any previous creation errors
      remoteAgentsModel.setRemoteAgentCreationError(null);

      createAgent();

      remoteAgentsModel.saveLastRemoteAgentSetup(
        commitRef?.github_commit_ref.repository_url || null,
        selectedBranch?.name || null,
        selectedSetupScript?.path || null,
      );
    } catch (error) {
      console.error("Failed to create agent:", error);
    }
  }

  // Visibility logic
  $: isInRemoteAgentSetupMode = $remoteAgentsModel.isActive && !$remoteAgentsModel.currentAgentId;
  $: shouldShowCreateButton = isInRemoteAgentSetupMode;
</script>

{#if shouldShowCreateButton}
  <div class="create-agent-footer">
    <TextTooltipAugment
      class="full-width-button"
      content={createButtonErrorMessage}
      triggerOn={[TooltipTriggerOn.Hover]}
    >
      <ButtonAugment
        variant="solid"
        color="accent"
        size={2}
        loading={isCreatingAgent}
        on:click={handleCreateAgent}
        disabled={isCreateButtonDisabled}
      >
        Create agent
      </ButtonAugment>
    </TextTooltipAugment>
  </div>
{/if}

<style>
  .create-agent-footer {
    display: flex;
    justify-content: center;
    width: 100%;
  }

  .create-agent-footer :global(.full-width-button) {
    width: 100%;
  }

  .create-agent-footer :global(.full-width-button .c-base-btn) {
    width: 100%;
  }
</style>
