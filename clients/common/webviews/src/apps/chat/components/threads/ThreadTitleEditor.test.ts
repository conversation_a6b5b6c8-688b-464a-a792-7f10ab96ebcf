import { render, fireEvent } from "@testing-library/svelte";
import { expect, describe, it, vi } from "vitest";
import ThreadTitleEditor from "./ThreadTitleEditor.svelte";

describe("ThreadTitleEditor", () => {
  it("renders with initial value", () => {
    const { getByDisplayValue } = render(ThreadTitleEditor, {
      value: "Test Title",
    });

    expect(getByDisplayValue("Test Title")).toBeInTheDocument();
  });

  it("calls onRename callback on Enter key", async () => {
    const mockRename = vi.fn();
    const { getByDisplayValue } = render(ThreadTitleEditor, {
      value: "Test Title",
      onRename: mockRename,
    });

    const input = getByDisplayValue("Test Title") as HTMLInputElement;
    await fireEvent.input(input, { target: { value: "New Title" } });
    await fireEvent.keyDown(input, { key: "Enter" });

    expect(mockRename).toHaveBeenCalledWith("New Title");
  });

  it("calls onCancel callback on Escape key and resets value", async () => {
    const mockCancel = vi.fn();
    const { getByDisplayValue } = render(ThreadTitleEditor, {
      value: "Test Title",
      onCancel: mockCancel,
    });

    const input = getByDisplayValue("Test Title") as HTMLInputElement;
    await fireEvent.input(input, { target: { value: "Modified Title" } });
    await fireEvent.keyDown(input, { key: "Escape" });

    expect(mockCancel).toHaveBeenCalled();
    // Value should be reset to original
    expect(input.value).toBe("Test Title");
  });

  it("calls onRename callback on blur when not selecting", async () => {
    const mockRename = vi.fn();
    const { getByDisplayValue } = render(ThreadTitleEditor, {
      value: "Test Title",
      onRename: mockRename,
    });

    const input = getByDisplayValue("Test Title") as HTMLInputElement;
    await fireEvent.input(input, { target: { value: "New Title" } });
    await fireEvent.blur(input);

    expect(mockRename).toHaveBeenCalledWith("New Title");
  });

  it("stops propagation on keydown events", async () => {
    const { getByDisplayValue } = render(ThreadTitleEditor, {
      value: "Test Title",
    });

    const input = getByDisplayValue("Test Title") as HTMLInputElement;
    const event = new KeyboardEvent("keydown", { key: "Enter" });
    const stopPropagationSpy = vi.spyOn(event, "stopPropagation");

    await fireEvent(input, event);

    expect(stopPropagationSpy).toHaveBeenCalled();
  });

  it("stops propagation on click events", async () => {
    const { getByDisplayValue } = render(ThreadTitleEditor, {
      value: "Test Title",
    });

    const input = getByDisplayValue("Test Title") as HTMLInputElement;
    const event = new MouseEvent("click");
    const stopPropagationSpy = vi.spyOn(event, "stopPropagation");

    await fireEvent(input, event);

    expect(stopPropagationSpy).toHaveBeenCalled();
  });
});
