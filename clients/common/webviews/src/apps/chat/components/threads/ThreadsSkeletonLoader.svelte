<script lang="ts">
  /**
   * Skeleton loading component for thread lists
   * Displays animated skeleton rows while threads are loading
   */

  // Number of skeleton rows to show when loading
  export let rowCount: number = 3;

  // Create an array of skeleton rows
  $: skeletonRows = Array(rowCount).fill(null);
</script>

<div class="loading-state">
  {#each skeletonRows as _, i (i)}
    <div class="skeleton-row">
      <div class="skeleton-icon"></div>
      <div class="skeleton-text"></div>
    </div>
  {/each}
</div>

<style>
  .loading-state {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-1);
  }

  .skeleton-row {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-2);
    height: 25px;
    padding: 0 var(--ds-spacing-3);
    pointer-events: none;
  }

  .skeleton-icon {
    width: 14px;
    height: 14px;
    border-radius: 3px;
    background-color: var(--ds-color-neutral-8);
    animation: pulse 1.5s infinite;
    flex-shrink: 0;
  }

  .skeleton-text {
    height: 14px;
    background-color: var(--ds-color-neutral-8);
    border-radius: 3px;
    animation: pulse 1.5s infinite;
    flex: 1;
  }

  .skeleton-row:first-child .skeleton-text {
    max-width: 70%;
  }

  .skeleton-row:nth-child(2) .skeleton-text {
    max-width: 60%;
  }

  .skeleton-row:nth-child(3) .skeleton-text {
    max-width: 90%;
  }

  @keyframes pulse {
    0% {
      opacity: 0.6;
    }
    50% {
      opacity: 0.3;
    }
    100% {
      opacity: 0.6;
    }
  }
</style>
