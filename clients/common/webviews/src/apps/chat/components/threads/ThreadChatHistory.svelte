<script lang="ts">
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import { TooltipTriggerOn } from "$common-webviews/src/design-system/_primitives/TooltipAugment/types";

  export let userMessages: string[];

  function truncate(str = "", maxLength: number) {
    if (str.length <= maxLength) {
      return str;
    }
    return str.slice(0, maxLength - 3) + "...";
  }
</script>

{#if userMessages.length > 0}
  {#key JSON.stringify(userMessages.slice(-9))}
    <div class="agent-chat-history">
      {#each userMessages.slice(-9) as message, index (message + "-" + index)}
        <TextTooltipAugment
          delayDurationMs={0}
          content={truncate(message || "", 170)}
          side="bottom"
          nested={false}
          hasPointerEvents={false}
          triggerOn={[TooltipTriggerOn.Hover]}
        >
          <div class="agent-chat-history__item">|</div>
        </TextTooltipAugment>
      {/each}
    </div>
  {/key}
{/if}

<style>
  .agent-chat-history {
    display: flex;
    flex-direction: row;
    flex-shrink: 0;
    align-items: baseline;
    color: var(--ds-color-neutral-9);
    height: 16px;
    margin-left: var(--ds-spacing-2);
    margin-right: var(--ds-spacing-1);
  }

  .agent-chat-history__item {
    padding-inline: 0.5px;
    line-height: 14px;
    color: var(--ds-color-neutral-7);
  }
</style>
