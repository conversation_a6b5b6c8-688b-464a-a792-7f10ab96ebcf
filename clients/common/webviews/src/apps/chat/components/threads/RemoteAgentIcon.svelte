<script lang="ts">
  import RegularCloudIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/cloud-arrow-up.svg?component";
  import { type RemoteAgentThread } from "./ThreadsPanel.svelte";
  import { shouldWarnOfRemoteAgentDeletion } from "$common-webviews/src/apps/remote-agent-manager/utils/agent-deletion";
  import RegularTriangleExclamationIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/triangle-exclamation.svg?component";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import { differenceInDays } from "$common-webviews/src/common/utils/time-utils";
  import { getChatModel } from "../../chat-context";

  export let remoteAgent: RemoteAgentThread;

  const chatModel = getChatModel();

  $: showDeletionWarning = shouldWarnOfRemoteAgentDeletion(
    remoteAgent.agent,
    $chatModel.flags.remoteAgentsResumeHintAvailableTtlDays,
  );
  $: daysLeft = remoteAgent.agent?.expires_at
    ? differenceInDays(new Date(remoteAgent.agent.expires_at), new Date())
    : 0;

  $: tooltipText =
    daysLeft < 0
      ? "This remote agent will be deleted soon due to inactivity."
      : `${daysLeft} day${daysLeft === 1 ? "" : "s"} remaining`;
</script>

<div class="remote-agent-icon">
  {#if showDeletionWarning}
    <TextTooltipAugment content={tooltipText}>
      <RegularTriangleExclamationIcon />
    </TextTooltipAugment>
  {:else}
    <RegularCloudIcon />
  {/if}
</div>

<style>
  .remote-agent-icon {
    position: relative;
    flex: none;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 14px;
  }
</style>
