<script lang="ts">
  import DropdownMenuAugment from "$common-webviews/src/design-system/components/DropdownMenuAugment";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import RegularEllipsisIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/ellipsis.svg?component";
  import Trash from "$common-webviews/src/design-system/icons/trash.svelte";
  import ArrowUpRightIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/arrow-up-right-from-square.svg?component";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import { pluralize } from "$clients/sidecar/libs/src/utils/pluralize";

  import type { ChatModel } from "$common-webviews/src/apps/chat/models/chat-model";
  import { type RemoteAgentsModel } from "$common-webviews/src/apps/remote-agent-manager/models/remote-agents-model";
  import type { Thread } from "./ThreadsPanel.svelte";
  import ThreadsListRowItem from "./ThreadsListRowItem.svelte";

  // Props
  export let recentRemoteAgents: Thread[] = [];
  export let displayedGroups: Array<{ groupTitle: string; threads: Thread[] }> = [];
  export let selectedThreadId: string | null = null;
  export let chatModel: ChatModel;
  export let remoteAgentsModel: RemoteAgentsModel;
  export let currentRepoUrl: string = "";
  export let enableShareService: boolean = false;

  // Event handlers - these will be passed from parent
  export let onThreadDelete: (thread: Thread, threads: Thread[], index: number) => void;
  export let onThreadSelect: (thread: Thread) => void;
  export let onTogglePinned: (thread: Thread, event?: Event) => void;
  export let onDeleteAllThreadsInGroup: (threads: Thread[]) => void;

  function onShowRemoteAgentHomePanel() {
    remoteAgentsModel.showRemoteAgentHomePanel();
  }

  $: recentRemoteAgentCount = recentRemoteAgents.length;
  let containerWidth = 0;
</script>

<div class="c-threads-list" bind:clientWidth={containerWidth}>
  <!-- Active Remote Agents Section -->
  {#if recentRemoteAgentCount > 0}
    <div class="c-threads-list__group">
      <!-- Header -->
      <div class="c-threads-list__header">
        <TextAugment size={1} weight="light" color="secondary">
          Recent remote {pluralize(recentRemoteAgentCount, "agent")}
        </TextAugment>

        <div class="c-threads-list__actions">
          <!-- show home panel button -->
          <div class="c-threads-list__home-button">
            <TextTooltipAugment
              content="Expand remote agents dashboard"
              nested={false}
              hasPointerEvents={false}
            >
              <IconButtonAugment
                variant="ghost-block"
                color="neutral"
                size={0.5}
                on:click={onShowRemoteAgentHomePanel}
              >
                <ArrowUpRightIcon />
              </IconButtonAugment>
            </TextTooltipAugment>
          </div>
        </div>
      </div>

      <!-- Active Remote Agents List -->
      <div class="c-threads-list__items">
        {#each recentRemoteAgents as thread, index (thread.id)}
          <ThreadsListRowItem
            {chatModel}
            {remoteAgentsModel}
            {thread}
            {containerWidth}
            {currentRepoUrl}
            {enableShareService}
            isSelected={selectedThreadId === thread.id}
            afterDelete={() => onThreadDelete(thread, recentRemoteAgents, index)}
            onSelect={() => onThreadSelect(thread)}
            onTogglePinned={(event) => onTogglePinned(thread, event)}
          />
        {/each}
      </div>
    </div>
  {/if}

  {#each displayedGroups as group (`${group.groupTitle}`)}
    <div class="c-threads-list__group">
      <div class="c-threads-list__header">
        <TextAugment size={1} weight="light" color="secondary">
          {group.groupTitle}
        </TextAugment>
        <div class="c-threads-list__options">
          <DropdownMenuAugment.Root>
            <DropdownMenuAugment.Trigger>
              <IconButtonAugment
                variant="ghost-block"
                color="neutral"
                size={0.5}
                title="Group options"
              >
                <RegularEllipsisIcon />
              </IconButtonAugment>
            </DropdownMenuAugment.Trigger>
            <DropdownMenuAugment.Content size={1} side="bottom" align="end">
              <DropdownMenuAugment.Item
                color="error"
                onSelect={() => onDeleteAllThreadsInGroup(group.threads)}
              >
                <Trash slot="iconLeft" />
                Delete {group.threads.length}
                {pluralize(group.threads.length, "thread")}
              </DropdownMenuAugment.Item>
            </DropdownMenuAugment.Content>
          </DropdownMenuAugment.Root>
        </div>
      </div>

      <!-- Group threads -->
      <div class="c-threads-list__items">
        {#each group.threads as thread, index (`${thread.id}-${thread.isPinned}-${index}`)}
          <ThreadsListRowItem
            {chatModel}
            {remoteAgentsModel}
            {thread}
            {containerWidth}
            showIfPinned
            {currentRepoUrl}
            {enableShareService}
            isSelected={selectedThreadId === thread.id}
            afterDelete={() => onThreadDelete(thread, group.threads, index)}
            onSelect={() => onThreadSelect(thread)}
            onTogglePinned={(event) => onTogglePinned(thread, event)}
          />
        {/each}
      </div>
    </div>
  {/each}
</div>

<style>
  .c-threads-list {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-1);
  }

  .c-threads-list__group {
    margin-bottom: var(--ds-spacing-2);
  }

  .c-threads-list__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-block: var(--ds-spacing-0_5);
    padding-inline: var(--ds-spacing-1);
    position: sticky;
    top: calc(var(--ds-spacing-1) * -1);
    background: var(--augment-window-background);
    z-index: 1;
    margin-bottom: var(--ds-spacing-1);
  }

  .c-threads-list__options {
    display: flex;
    align-items: center;
    visibility: hidden;
  }

  .c-threads-list__header:hover .c-threads-list__options {
    visibility: visible;
  }

  .c-threads-list__actions {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-1);
  }

  .c-threads-list__home-button {
    display: flex;
    align-items: center;
  }

  .c-threads-list__items {
    display: flex;
    flex-direction: column;
  }
</style>
