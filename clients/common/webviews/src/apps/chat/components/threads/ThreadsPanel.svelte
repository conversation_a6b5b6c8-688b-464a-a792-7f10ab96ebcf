<script lang="ts" context="module">
  // Define thread types for type safety
  /* eslint-disable @typescript-eslint/naming-convention */
  export type ChatThread = {
    id: string;
    type: "chat";
    isNew?: boolean;
    title: string;
    updated_at: string;
    started_at: string;
    isPinned: boolean | undefined;
    conversation: IConversation;
    sortTimestamp: Date;
  };

  export type LocalAgentThread = {
    id: string;
    type: "localAgent";
    isNew?: boolean;
    title: string;
    updated_at: string;
    started_at: string;
    isPinned: boolean | undefined;
    conversation: IConversation;
    sortTimestamp: Date;
  };

  export type RemoteAgentThread = {
    id: string;
    type: "remoteAgent";
    isNew?: boolean;
    title: string;
    updated_at: string;
    started_at: string;
    status: RemoteAgentStatus;
    workspace_status: RemoteAgentWorkspaceStatus;
    is_setup_script_agent: boolean | undefined;
    workspace_setup: RemoteAgentWorkspaceSetup;
    has_updates?: boolean;
    agent: RemoteAgent;
    sortTimestamp: Date;
    isPinned?: boolean | undefined;
    conversation?: IConversation;
  };

  export type Thread = ChatThread | LocalAgentThread | RemoteAgentThread;
  /* eslint-enable @typescript-eslint/naming-convention */

  // Helper function to check if a remote agent thread is recent
  export function isRecentRemoteAgent(thread: Thread): boolean {
    if (thread.id === NEW_AGENT_KEY) return false;
    // our definition is: updated within the last week
    if (thread.type !== "remoteAgent") {
      return false;
    }
    const remoteThread = thread as RemoteAgentThread;
    const updatedAt = new Date(remoteThread.updated_at);
    const now = new Date();
    const diffMs = now.getTime() - updatedAt.getTime();
    const diffHours = Math.floor(diffMs / 1000 / 60 / 60);
    return diffHours < 7 * 24;
  }
</script>

<script lang="ts">
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import { ChatModeModel } from "$common-webviews/src/apps/chat/models/chat-mode-model";
  import type {
    ChatModel,
    SortableConversationFieldType,
  } from "$common-webviews/src/apps/chat/models/chat-model";
  import { ConversationModel } from "$common-webviews/src/apps/chat/models/conversation-model";
  import type { IConversation } from "$common-webviews/src/apps/chat/models/types";
  import { groupThreadsByAgeWithActive } from "$common-webviews/src/apps/chat/utils/thread-grouping";
  import { getThreadsWithSearchQuery } from "$common-webviews/src/apps/chat/utils/thread-search";
  import { GitReferenceModel } from "$common-webviews/src/apps/remote-agent-manager/models/git-reference-model";
  import { RemoteAgentsModel } from "$common-webviews/src/apps/remote-agent-manager/models/remote-agents-model";
  import { type SharedWebviewStoreModel } from "$common-webviews/src/common/models/shared-webview-store-model";
  import {
    type RemoteAgent,
    type RemoteAgentStatus,
    type RemoteAgentWorkspaceSetup,
    type RemoteAgentWorkspaceStatus,
  } from "@augment-internal/sidecar-libs/src/remote-agent/remote-agent-types";
  import {
    type ChatHomeWebviewState,
    SHARED_AGENT_STORE_CONTEXT_KEY,
  } from "$vscode/src/webview-panels/remote-agents/common-webview-store";
  import { RemoteAgentThreadListAction } from "@augment-internal/sidecar-libs/src/metrics/types";

  import { getContext, onMount } from "svelte";
  import { writable } from "svelte/store";
  import ThreadsList from "./ThreadsList.svelte";
  import ThreadTypeFilterDropdown, {
    type ThreadTypeFilterOptions,
    type ThreadTypeFilter,
  } from "./ThreadTypeFilterDropdown.svelte";
  import ThreadsSearch from "./ThreadsSearch.svelte";
  import ThreadsSkeletonLoader from "./ThreadsSkeletonLoader.svelte";

  import { NEW_AGENT_KEY } from "../../models/agent-constants";
  import ChevronLeft from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/chevron-left.svg?component";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import Reload from "$common-webviews/src/design-system/icons/reload.svelte";
  import NewThreadDropdown from "./NewThreadDropdown.svelte";
  import { listFocusNavigation } from "./list-focus-navigation";
  import RegisterCommand from "$common-webviews/src/common/components/keybindings/RegisterCommand.svelte";
  import ShortcutHint from "$common-webviews/src/common/components/keybindings/ShortcutHint.svelte";

  /**
   * This is a threads menu that shows all threads (chat, local agents, and remote agents).
   */

  //#region Context and states

  const NEW_REMOTE_AGENT_THREAD_ID = NEW_AGENT_KEY;

  const remoteAgentsModel = getContext<RemoteAgentsModel>(RemoteAgentsModel.key);
  const sharedWebviewStore = getContext<SharedWebviewStoreModel<ChatHomeWebviewState>>(
    SHARED_AGENT_STORE_CONTEXT_KEY,
  );
  const gitReferenceModel = getContext<GitReferenceModel>(GitReferenceModel.key);
  const chatModeModel = getContext<ChatModeModel>(ChatModeModel.key);

  // Get the chat model from props or context
  export let chatModel: ChatModel = getContext<ChatModel>("chatModel");
  export let onClose: () => void = () => {};

  // Debug features detection
  $: enableDebugFeatures = $chatModel?.flags?.enableDebugFeatures ?? false;

  const currentRepoUrl = gitReferenceModel.currentRepositoryUrl;

  // Variables for chat thread menu
  const enableShareService = writable(false);

  // Fetch the current repository URL on mount
  onMount(async () => {
    try {
      void gitReferenceModel.getRemoteUrl();

      // Set enableShareService based on chat model flags
      if (chatModel && chatModel.flags) {
        enableShareService.update(() => chatModel.flags.enableShareService || false);
      }
    } catch (error) {
      console.error("Failed to get current repository URL:", error);
    }
  });

  $: currentAgentId = $remoteAgentsModel.currentAgentId;
  $: remoteAgents = $remoteAgentsModel.agentOverviews || [];

  // Get conversations directly from the chat model if available
  $: allChatModelConversations = chatModel ? Object.values($chatModel.orderedConversations()) : [];
  $: newLocalConversation = allChatModelConversations.find(ConversationModel.isNew);
  $: chatConversations = chatModel
    ? allChatModelConversations.filter(
        (c) => !c.extraData?.isAgentConversation && c !== newLocalConversation,
      )
    : [];
  $: localAgentConversations = chatModel
    ? allChatModelConversations.filter(
        (c) => c.extraData?.isAgentConversation === true && c !== newLocalConversation,
      )
    : [];

  $: threadsError = $remoteAgentsModel.agentThreadsError; // Use the specific error state for agent threads
  $: isLoading = $remoteAgentsModel.isLoading;
  $: hasFetchedOnce = $remoteAgentsModel.hasFetchedOnce;

  // Thread counts are displayed in the filter dropdown

  // Get the current sort preference from the chat model
  $: sortConversationsBy = chatModel
    ? chatModel.sortConversationsBy
    : writable<SortableConversationFieldType>("lastMessageTimestamp");
  $: currentSortPreference = $sortConversationsBy || "lastMessageTimestamp";
  $: agentExecutionMode = $chatModel.agentExecutionMode;

  // Helper function to get the correct timestamp for sorting based on user preference
  function getConversationTimestamp(conversation: IConversation | undefined): Date {
    if (!conversation) {
      return new Date(0);
    }
    const sortBy = currentSortPreference;
    return ConversationModel.getTime(conversation, sortBy);
  }

  // Track the currently selected thread ID
  $: selectedThreadId = $remoteAgentsModel.isActive
    ? (currentAgentId ?? NEW_REMOTE_AGENT_THREAD_ID)
    : $chatModel.currentConversationId;

  //#region Threads

  /**
   * Toggles the pinned status of a thread
   * @param thread The thread to toggle pinned status for
   */
  async function toggleThreadPinned(thread: Thread, event?: Event) {
    if (event) {
      event.stopPropagation(); // Prevent triggering thread selection
    }

    if (thread.isNew) {
      return;
    } else if (thread.type === "remoteAgent") {
      const wasPinned = thread.isPinned || false;

      // Get the updated pinnedAgents from the toggleAgentPinned method
      const pinnedAgents = await remoteAgentsModel.toggleAgentPinned(thread.id, wasPinned);

      // Update the shared store with the returned pinnedAgents
      sharedWebviewStore.update((state) => {
        if (!state) return;
        return {
          ...state,
          pinnedAgents,
        };
      });

      // Report the pin/unpin event
      remoteAgentsModel.reportRemoteAgentThreadListEvent(
        wasPinned ? RemoteAgentThreadListAction.unpinAgent : RemoteAgentThreadListAction.pinAgent,
        recentRemoteAgents.length,
        thread.id,
      );
    } else if (["chat", "localAgent"].includes(thread.type)) {
      // For chat and local agent threads, use the chat model's pinning functionality
      if (chatModel) {
        chatModel.toggleConversationPinned(thread.id);
      }
    }
  }
  // Create thread objects for chat and local agent conversations
  /* eslint-disable @typescript-eslint/naming-convention */
  $: newThread = (() => {
    if ($remoteAgentsModel.isActive) {
      if ($remoteAgentsModel.isCreatingAgent) {
        return undefined;
      }
      const draft = $chatModel.currentConversationModel.draftExchange?.request_message;
      return {
        id: NEW_REMOTE_AGENT_THREAD_ID,
        isNew: true,
        type: "remoteAgent",
        title: draft,
        hasDraft: !!draft,
        updated_at: new Date().toISOString(),
        started_at: new Date().toISOString(),
        isPinned: false,
        conversation: undefined,
        sortTimestamp: new Date(),
      };
    }

    // newConversation is the draft local agent or chat conversation
    if (newLocalConversation) {
      return {
        id: newLocalConversation.id,
        isNew: true,
        type: newLocalConversation.extraData?.isAgentConversation ? "localAgent" : "chat",
        title: "",
        updated_at: newLocalConversation.lastInteractedAtIso,
        started_at: newLocalConversation.createdAtIso,
        isPinned: false,
        conversation: newLocalConversation,
        sortTimestamp: getConversationTimestamp(newLocalConversation),
      };
    }
  })();

  $: chatThreads = chatConversations.map(
    (conversation: IConversation): ChatThread => ({
      id: conversation.id,
      isNew: false,
      type: "chat",
      title: conversation.name || "",
      updated_at: conversation.lastInteractedAtIso,
      started_at: conversation.createdAtIso,
      isPinned: conversation.isPinned,
      conversation: conversation,
      sortTimestamp: getConversationTimestamp(conversation),
    }),
  );

  $: localAgentThreads = localAgentConversations.map(
    (conversation: IConversation): LocalAgentThread => ({
      id: conversation.id,
      isNew: false,
      type: "localAgent",
      title: conversation.name || "",
      updated_at: conversation.lastInteractedAtIso,
      started_at: conversation.createdAtIso,
      isPinned: conversation.isPinned,
      conversation: conversation,
      sortTimestamp: getConversationTimestamp(conversation),
    }),
  );

  // Get pinned agents from both the model and the shared store to ensure they're in sync
  $: pinnedAgents = $sharedWebviewStore.state?.pinnedAgents || {};

  // Convert remote agents to thread objects
  $: remoteAgentThreads = remoteAgents.map(
    (agent: RemoteAgent): RemoteAgentThread => ({
      id: agent.remote_agent_id,
      isNew: false,
      type: "remoteAgent",
      title: agent.title || agent.session_summary,
      updated_at: agent.updated_at,
      started_at: agent.started_at,
      status: agent.status,
      workspace_status: agent.workspace_status,
      is_setup_script_agent: agent.is_setup_script_agent,
      workspace_setup: agent.workspace_setup || {},
      agent: agent,
      // For remote agents, use the updated_at timestamp for sorting
      sortTimestamp: new Date(agent.updated_at || agent.started_at),
      // Get pinned status from the global store
      isPinned: pinnedAgents[agent.remote_agent_id] || false,
      has_updates: agent.has_updates && agent.remote_agent_id !== selectedThreadId,
    }),
  );
  /* eslint-enable @typescript-eslint/naming-convention */

  $: doShowNewThread = newThread && selectedThreadId === newThread.id;

  // Combine all thread types
  $: allThreads = [
    doShowNewThread && newThread,
    ...chatThreads,
    ...localAgentThreads,
    ...remoteAgentThreads,
  ].filter(Boolean) as Thread[];

  // Thread type filter
  $: threadTypeFilters = [
    { value: "all", label: "All threads", count: allThreads.length },
    { value: "chat", label: "Chats", count: chatThreads.length },
    { value: "localAgent", label: "Agents", count: localAgentThreads.length },
    { value: "remoteAgent", label: "Remote agents", count: remoteAgentThreads.length },
  ] satisfies ThreadTypeFilterOptions;
  const activeThreadTypeFilter = writable<ThreadTypeFilter>("all");

  // Filter threads by type and search query
  $: filteredThreads = allThreads.filter((thread) => {
    // First filter by type
    if ($activeThreadTypeFilter !== "all" && thread.type !== $activeThreadTypeFilter) {
      return false;
    }
    // Then filter by search query if active
    return getThreadsWithSearchQuery(
      thread,
      searchQuery,
      $remoteAgentsModel.currentAgentId,
      $remoteAgentsModel.currentConversation,
    );
  });

  /**
   * Switches to the appropriate mode based on the thread type and updates all necessary state.
   * This function centralizes the mode switching logic to ensure consistency across the application.
   *
   * @param thread The thread to switch to
   * @returns A boolean indicating whether the switch was successful
   */
  function switchToThread(thread: Thread): boolean {
    if (!thread) return false;

    // Optimistically clear has_updates for remote agent threads
    if (thread.type === "remoteAgent" && thread.has_updates) {
      remoteAgentsModel.optimisticallyClearAgentUpdates(thread.id);
    }
    // Set the active state based on thread type
    remoteAgentsModel.setIsActive(thread.type === "remoteAgent");
    // Use the ChatModeModel's switchToThread method to handle the mode switching
    const success = chatModeModel.switchToThread(thread.type, thread.id, $agentExecutionMode);
    if (success) {
      onClose();
    }

    return success;
  }

  // Override the actual state with debug state when in debug mode
  $: overviewError = threadsError?.errorMessage;
  $: effectiveIsLoading = isLoading && !hasFetchedOnce;
  $: effectiveHasError = !!overviewError && hasFetchedOnce;
  $: effectiveIsEmpty = hasFetchedOnce && filteredThreads.length === 0;

  // Calculate displayed groups - only show threads when expanded
  $: displayedGroups = groupThreadsByAgeWithActive(filteredThreads);

  //#region Active remote agents
  // Calculate active remote agents (remote workspaces that are not paused)
  $: recentRemoteAgents = filteredThreads.filter(isRecentRemoteAgent);

  let searchQuery = "";
  // Calculate search placeholder based on active filter
  $: searchPlaceholder = `Search ${
    $activeThreadTypeFilter === "all"
      ? "threads"
      : threadTypeFilters.find((f) => f.value === $activeThreadTypeFilter)?.label?.toLowerCase()
  }`;

  function refreshThreads() {
    $remoteAgentsModel.refreshAgentThreads();
  }

  /**
   * Deletes all threads in a group
   * @param threads - Array of threads to delete
   */
  async function deleteAllThreadsInGroup(threads: Thread[]) {
    // Separate conversation IDs from remote agent IDs
    const conversationIds: string[] = [];
    const remoteAgentIds: string[] = [];

    for (const thread of threads) {
      if (thread.type === "remoteAgent") {
        remoteAgentIds.push(thread.id);
      } else {
        conversationIds.push(thread.id);
      }
    }

    await chatModel.deleteConversations(
      conversationIds,
      undefined, // nextConvoId
      remoteAgentIds, // Remote agent IDs to delete
      remoteAgentsModel, // Pass the remote agents model for handling remote agent deletions
    );
  }

  function onThreadDelete(thread: Thread, threads: Thread[], index: number) {
    // Report delete event for remote agent threads
    if (thread.type === "remoteAgent") {
      remoteAgentsModel.reportRemoteAgentThreadListEvent(
        RemoteAgentThreadListAction.deleteAgent,
        recentRemoteAgents.length,
        thread.id,
      );
    }

    if (selectedThreadId !== thread.id) return;
    const nextThread = threads[index + 1] || threads[index - 1];
    if (nextThread) {
      switchToThread(nextThread);
    }
  }

  function onThreadSelect(thread: Thread) {
    switchToThread(thread);
    // Log agent selection for remote agents
    if (thread.type === "remoteAgent") {
      remoteAgentsModel.reportRemoteAgentThreadListEvent(
        RemoteAgentThreadListAction.selectAgent,
        remoteAgentThreads.length,
        thread.id,
      );
    }
  }
  let containerWidth = 0;
</script>

<div class="c-threads-panel" bind:clientWidth={containerWidth} use:listFocusNavigation>
  <!-- Header -->
  <div class="c-threads-panel__header">
    {#snippet backButton()}
      <IconButtonAugment
        title="Go Back"
        variant="ghost-block"
        color="neutral"
        size={0.5}
        on:click={onClose}
      >
        <ChevronLeft />
      </IconButtonAugment>
    {/snippet}

    {#if enableDebugFeatures}
      <RegisterCommand name="closeThreadsPanel" handler={onClose} />
      <TextTooltipAugment side="bottom" nested={false}>
        <div slot="content" class="c-threads-panel__tooltip">
          Go back
          <ShortcutHint commandName="closeThreadsPanel" keysOnly />
        </div>
        {@render backButton()}
      </TextTooltipAugment>
    {:else}
      {@render backButton()}
    {/if}

    <div class="c-threads-panel__header-right">
      {#if effectiveHasError}
        <div class="reload-button">
          <TextTooltipAugment
            content="Failed to reload remote agents. Click to retry. {overviewError}"
            nested={false}
            hasPointerEvents={false}
          >
            <IconButtonAugment
              variant="ghost-block"
              color="neutral"
              size={0.5}
              on:click={(event) => {
                event.stopPropagation();
                refreshThreads();
              }}
            >
              <Reload />
            </IconButtonAugment>
          </TextTooltipAugment>
        </div>
      {/if}

      <NewThreadDropdown showLabel={containerWidth > 300} onNewThreadCreated={onClose} />
    </div>
  </div>

  <!-- Filters -->
  <div class="c-threads-panel__filters-panel">
    <div class="c-threads-panel__filters">
      <ThreadTypeFilterDropdown
        {threadTypeFilters}
        {activeThreadTypeFilter}
        showLabel={containerWidth > 300}
      />
      <ThreadsSearch bind:searchQuery placeholder={searchPlaceholder} />
    </div>
  </div>

  <!-- List -->
  <div class="c-threads-panel__list">
    <ThreadsList
      {recentRemoteAgents}
      {displayedGroups}
      {selectedThreadId}
      {chatModel}
      {remoteAgentsModel}
      currentRepoUrl={$currentRepoUrl}
      enableShareService={$enableShareService}
      {onThreadDelete}
      {onThreadSelect}
      onTogglePinned={toggleThreadPinned}
      onDeleteAllThreadsInGroup={deleteAllThreadsInGroup}
    />

    {#if effectiveIsLoading}
      <ThreadsSkeletonLoader rowCount={3} />
    {:else if effectiveIsEmpty}
      <!-- Empty state -->
      <div class="empty-message">
        {#if searchQuery.trim() !== ""}
          <TextAugment size={1} color="secondary">
            No threads with "{searchQuery}" in the title.
          </TextAugment>
        {:else}
          <TextAugment size={1} color="secondary">Create a new thread to get started.</TextAugment>
        {/if}
      </div>
    {/if}
  </div>
</div>

<style>
  .c-threads-panel {
    --threads-panel-padding: var(--ds-spacing-3);

    display: flex;
    flex-direction: column;
    max-height: 100%;
  }

  .c-threads-panel__header {
    display: flex;
    gap: var(--ds-spacing-1);
    align-items: center;
    border-bottom: 1px solid var(--augment-border-color);
    padding-inline: var(--threads-panel-padding);
    padding-block: var(--ds-spacing-1);

    & > *::first-letter {
      text-transform: uppercase;
    }
  }
  .c-threads-panel__header-right {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-1);
    margin-left: auto;
  }

  .c-threads-panel__filters-panel {
    padding-block: var(--ds-spacing-2);
    padding-inline: var(--threads-panel-padding);
  }

  .c-threads-panel__filters {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-2);
    justify-content: space-between;
    height: var(--ds-spacing-5);
  }

  .c-threads-panel__list {
    padding-inline: var(--ds-spacing-2);
    overflow: auto;
    flex: 1;
  }

  .c-threads-panel__tooltip {
    --keyboard-shortcut-hint-border: currentColor;
    --keyboard-shortcut-hint-color: currentColor;

    display: flex;
    align-items: center;
    gap: var(--ds-spacing-1);
  }
</style>
