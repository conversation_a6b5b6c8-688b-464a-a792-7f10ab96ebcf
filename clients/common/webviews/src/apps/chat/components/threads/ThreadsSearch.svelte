<script lang="ts">
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import TextFieldAugment from "$common-webviews/src/design-system/components/TextFieldAugment.svelte";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import SearchIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/magnifying-glass.svg?component";
  import XMarkIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/xmark.svg?component";
  import { fly } from "svelte/transition";

  // Props
  export let searchQuery = "";
  export let placeholder = "Search threads";
  export let isSearchActive = !!searchQuery;

  function onToggleSearch() {
    searchQuery = "";
    isSearchActive = !isSearchActive;
  }
  function onClearSearch() {
    searchQuery = "";
    isSearchActive = false;
  }

  // Internal state
  let searchInputElement: HTMLInputElement | undefined = undefined;

  // Focus the input when search becomes active
  $: if (isSearchActive && searchInputElement) {
    setTimeout(() => {
      searchInputElement?.focus();
    }, 200);
  }

  function handleKeydown(event: KeyboardEvent) {
    if (event.key === "Escape") {
      onClearSearch();
    }
  }

  function handleFocus() {
    // Search is already active when input is focused
  }

  function handleBlur() {
    if (!searchQuery.trim()) {
      // Only close search if there's no query
      onClearSearch();
    }
  }
</script>

<div class="c-threads-search">
  {#if isSearchActive}
    <div class="c-threads-search__field-container" transition:fly={{ x: 6, duration: 150 }}>
      <TextFieldAugment
        bind:value={searchQuery}
        {placeholder}
        size={1}
        variant="surface"
        class="search-input"
        bind:textInput={searchInputElement}
        on:keydown={handleKeydown}
        on:focus={handleFocus}
        on:blur={handleBlur}
      >
        <div class="search-field-container__icon" slot="iconLeft">
          <SearchIcon />
        </div>
      </TextFieldAugment>
    </div>
  {/if}

  {#if isSearchActive}
    <div class="c-threads-search__icon-button">
      <TextTooltipAugment content="Close" nested={false}>
        <IconButtonAugment
          variant="ghost-block"
          color="neutral"
          size={0.5}
          on:click={onClearSearch}
        >
          <XMarkIcon />
        </IconButtonAugment>
      </TextTooltipAugment>
    </div>
  {:else}
    <div class="c-threads-search__icon-button">
      <TextTooltipAugment content="Search threads" nested={false} hasPointerEvents={false}>
        <IconButtonAugment
          variant="ghost-block"
          color="neutral"
          size={0.5}
          on:click={onToggleSearch}
        >
          <SearchIcon />
        </IconButtonAugment>
      </TextTooltipAugment>
    </div>
  {/if}
</div>

<style>
  .c-threads-search {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: var(--ds-spacing-1);
    flex: 1;
  }

  .c-threads-search__field-container {
    display: flex;
    flex-direction: column;
    flex: 1;
  }

  .c-threads-search__field-container :global(.c-base-text-input) {
    --base-text-field-bg-color: transparent;
  }

  .search-field-container__icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 12px;
    margin-inline: var(--ds-spacing-0_5);
  }
</style>
