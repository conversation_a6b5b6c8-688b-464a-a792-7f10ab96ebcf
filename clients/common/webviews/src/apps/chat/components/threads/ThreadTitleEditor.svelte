<script lang="ts">
  import TextFieldAugment from "$common-webviews/src/design-system/components/TextFieldAugment.svelte";
  import { onMount } from "svelte";
  import type {
    TextFieldSize,
    TextFieldVariant,
  } from "$common-webviews/src/design-system/_primitives/BaseTextInput.svelte";

  // Props
  export let value: string = "";
  export let size: TextFieldSize = 1;
  export let variant: TextFieldVariant = "surface";
  export let className: string = "";
  export let onRename: (newTitle: string) => void = () => {};
  export let onCancel: () => void = () => {};

  // Internal state
  let textInput: HTMLInputElement | undefined;
  let isSelectingInput = false;
  let editedTitle = value;

  // Update editedTitle when value prop changes
  $: editedTitle = value;

  // Handle key events in the input field
  function handleKeyDown(e: KeyboardEvent) {
    if (e.key === "Enter") {
      e.preventDefault();
      handleRename();
    } else if (e.key === "Escape") {
      e.preventDefault();
      handleCancel();
    }
    e.stopPropagation();
  }

  // Handle rename action
  function handleRename() {
    onRename(editedTitle);
  }

  // Handle cancel action
  function handleCancel() {
    editedTitle = value; // Reset to original value
    onCancel();
  }

  // Handle document click to commit changes
  function handleDocumentClick() {
    if (textInput && !isSelectingInput) {
      handleRename();
    }
    isSelectingInput = false;
  }

  onMount(() => {
    setTimeout(() => textInput?.focus(), 0);
  });
</script>

<svelte:window on:click={handleDocumentClick} />

<div class="c-thread-title-editor">
  <TextFieldAugment
    bind:textInput
    bind:value={editedTitle}
    {size}
    {variant}
    on:keydown={handleKeyDown}
    on:click={(e) => e.stopPropagation()}
    on:blur={() => {
      if (!isSelectingInput) {
        handleRename();
      }
    }}
    on:selectionchange={() => {
      // Selection might end outside of the input field,
      // we don't want to trigger a rename in that case.
      isSelectingInput = !!document.getSelection()?.toString().length;
    }}
    on:mouseup={() => {
      isSelectingInput = false;
    }}
    class="c-thread-title-editor__input {className}"
  />
</div>

<style>
  .c-thread-title-editor {
    flex: 1;
    min-width: 0;
    max-width: 100%;
    /* align with the left edge of the text */
    margin-left: calc(var(--ds-spacing-1) * -1);
    margin-top: 0.5px;
  }

  .c-thread-title-editor :global(.c-base-text-input) {
    display: block;
  }

  .c-thread-title-editor :global(.c-thread-title-editor__input) {
    width: 100%;
    padding: 3.5px 0px;
    text-indent: var(--ds-spacing-1);
  }
</style>
