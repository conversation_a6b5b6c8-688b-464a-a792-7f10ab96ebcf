<script lang="ts" context="module">
  export const NEW_THREADS_BUTTON_TEST_ID = "new-thread-button";
  export const NEW_THREAD_TYPE_BUTTON_CHAT_TEST_ID = "new-chat-thread-button";
  export const NEW_THREAD_TYPE_BUTTON_LOCAL_AGENT_TEST_ID = "new-local-agent-thread-button";
  export const NEW_THREAD_TYPE_BUTTON_REMOTE_AGENT_TEST_ID = "new-remote-agent-thread-button";
</script>

<script lang="ts">
  import Plus from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/plus.svg?component";
  import ModeDisplayDropdownItem from "$common-webviews/src/apps/chat/components/buttons/ModeDisplayDropdownItem.svelte";
  import { ChatModeModel } from "$common-webviews/src/apps/chat/models/chat-mode-model";
  import { type ChatModel } from "$common-webviews/src/apps/chat/models/chat-model";
  import { RemoteAgentsModel } from "$common-webviews/src/apps/remote-agent-manager/models/remote-agents-model";
  import { getContext } from "svelte";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import { RemoteAgentNewThreadButtonAction } from "@augment-internal/sidecar-libs/src/metrics/types";
  import DropdownMenu from "$common-webviews/src/design-system/components/DropdownMenuAugment";
  import type DropdownMenuRoot from "$common-webviews/src/design-system/components/DropdownMenuAugment/Root.svelte";
  import { getThreadTypeFromMode } from "./utils";
  import { type ChatModeType } from "@augment-internal/sidecar-libs/src/chat";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import { ANALYTICS_EVENTS } from "@augment-internal/sidecar-libs/src/client-interfaces/analytics";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import ShortcutHint from "$common-webviews/src/common/components/keybindings/ShortcutHint.svelte";

  export let isDropdownOpen: boolean = false;
  export let showLabel: boolean = true;
  export let onNewThreadCreated: (type: ChatModeType) => void = () => {};

  // Get models from context
  const remoteAgentsModel = getContext<RemoteAgentsModel>(RemoteAgentsModel.key);
  const chatModeModel = getContext<ChatModeModel>(ChatModeModel.key);
  const chatModel = getContext<ChatModel>("chatModel");

  // Determine if background agents are enabled
  $: enableBackgroundAgents = chatModel ? $chatModel.flags.enableBackgroundAgents : false;
  $: enableAgentMode = chatModel ? $chatModel.flags.enableAgentMode : false;

  async function createNewThread(type: ChatModeType) {
    await remoteAgentsModel.reportRemoteAgentNewThreadButtonEvent(
      RemoteAgentNewThreadButtonAction.click,
      getThreadTypeFromMode(type),
    );
    chatModeModel.createNewThread(type);
    chatModel.eventTracker?.trackEvent(ANALYTICS_EVENTS.THREAD_CREATION_ATTEMPTED, {
      source: "button",
    });
    dropdownRoot?.requestClose();
    onNewThreadCreated(type);
  }

  let dropdownRoot: DropdownMenuRoot;
  function onOpenChange(open: boolean) {
    isDropdownOpen = open;
  }
</script>

<DropdownMenu.Root bind:this={dropdownRoot} {onOpenChange} nested={false}>
  <DropdownMenu.Trigger
    on:click={(e) => {
      e.stopPropagation();
    }}
  >
    <TextTooltipAugment side="top" nested={false}>
      <div slot="content" class="c-new-thread-dropdown__tooltip">
        New Thread
        <ShortcutHint commandName="newConversation" keysOnly />
      </div>
      <div class="c-new-thread-dropdown__button-container">
        <ButtonAugment
          size={0.5}
          variant="soft"
          color="neutral"
          data-testid={NEW_THREADS_BUTTON_TEST_ID}
          --icon-size="12px"
          class="c-new-thread-dropdown__button"
        >
          <Plus slot="iconLeft" />
          {#if showLabel}
            <TextAugment size={1} truncate>New Thread</TextAugment>
          {/if}
        </ButtonAugment>
      </div>
    </TextTooltipAugment>
  </DropdownMenu.Trigger>
  <DropdownMenu.Content size={1} side="bottom" align="start">
    <div class="c-new-thread-dropdown__content">
      <ModeDisplayDropdownItem
        onSelect={() => createNewThread("chat")}
        isConversationAgentic={false}
        description="Ask questions, collaborate on projects."
      />
      {#if enableAgentMode}
        <ModeDisplayDropdownItem
          onSelect={() => createNewThread("localAgent")}
          isConversationAgentic={true}
          description="Complete coding tasks autonomously."
        />
      {/if}
      <DropdownMenu.Separator />
      {#if enableBackgroundAgents}
        <ModeDisplayDropdownItem
          onSelect={() => createNewThread("remoteAgent")}
          isBackgroundAgent={true}
          description="Run agents remotely."
        />
      {/if}
    </div>
  </DropdownMenu.Content>
</DropdownMenu.Root>

<style>
  .c-new-thread-dropdown__button-container :global(.c-new-thread-dropdown__button) {
    --base-btn-padding-vertical: var(--ds-spacing-1);
  }

  .c-new-thread-dropdown__tooltip {
    --keyboard-shortcut-hint-border: currentColor;
    --keyboard-shortcut-hint-color: currentColor;
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-1);
  }
</style>
