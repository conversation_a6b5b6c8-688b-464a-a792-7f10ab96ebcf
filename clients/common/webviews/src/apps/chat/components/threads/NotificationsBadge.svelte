<script lang="ts">
  import { getContext } from "svelte";
  import { RemoteAgentsModel } from "$common-webviews/src/apps/remote-agent-manager/models/remote-agents-model";
  import { RemoteAgentStatus } from "@augment-internal/sidecar-libs/src/remote-agent/remote-agent-types";
  import RegularBellRingIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/bell.svg?component";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import NumberFlow from "@number-flow/svelte";
  import { scale } from "svelte/transition";

  const remoteAgentsModel = getContext<RemoteAgentsModel>(RemoteAgentsModel.key);
  $: selectedThreadId = $remoteAgentsModel.isActive ? $remoteAgentsModel.currentAgentId : null;
  $: agentsWithChangesCount = ($remoteAgentsModel.agentOverviews || []).filter(
    (thread) =>
      !!thread.has_updates &&
      thread.remote_agent_id !== selectedThreadId &&
      thread.status === RemoteAgentStatus.agentIdle,
  ).length;
</script>

{#if agentsWithChangesCount > 0}
  <div transition:scale={{ duration: 150, start: 0.8 }}>
    <ButtonAugment variant="soft" color="accent" size={0.5} radius="full" on:click>
      <div class="c-notifications-badge">
        <RegularBellRingIcon />
        <NumberFlow value={agentsWithChangesCount} willChange />
      </div>
    </ButtonAugment>
  </div>
{/if}

<style>
  .c-notifications-badge {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-1);
    padding-inline: var(--ds-spacing-0_5);
    --number-flow-char-height: var(--ds-font-size-0);
  }
  .c-notifications-badge :global(svg) {
    width: var(--ds-icon-size-0);
    height: var(--ds-icon-size-0);
  }
</style>
