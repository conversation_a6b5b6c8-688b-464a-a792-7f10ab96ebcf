<script context="module" lang="ts">
  // Define thread types for type safety
  export type ThreadTypeFilter = "all" | "chat" | "localAgent" | "remoteAgent";
  export interface ThreadTypeFilterOption {
    value: ThreadTypeFilter;
    label: string;
    count: number;
  }
  export type ThreadTypeFilterOptions = Array<ThreadTypeFilterOption>;
</script>

<script lang="ts">
  import DropdownMenuAugment from "$common-webviews/src/design-system/components/DropdownMenuAugment/index";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import ChevronDown from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/chevron-down.svg?component";
  import ChevronUp from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/chevron-up.svg?component";
  import FilterIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/bars-filter.svg?component";
  import { writable, type Writable } from "svelte/store";
  import { MODE_DISPLAY_INFO } from "../buttons/mode-display-helper";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";

  // Export props
  export let threadTypeFilters: Array<ThreadTypeFilterOption> = [];
  export let showLabel: boolean = true;

  // Create a store for the active filter
  export let activeThreadTypeFilter: Writable<ThreadTypeFilter> = writable<ThreadTypeFilter>("all");
  let isDropdownOpen = false;

  $: selection =
    $activeThreadTypeFilter !== "all"
      ? `${threadTypeFilters.find((f) => f.value === $activeThreadTypeFilter)?.label || ""}`
      : "All Threads";
  // Function to close the dropdown
  let requestCloseFilterDropdown: () => void;
</script>

<div class="c-thread-type-filter-dropdown">
  <DropdownMenuAugment.Root
    bind:requestClose={requestCloseFilterDropdown}
    onOpenChange={(o) => (isDropdownOpen = o)}
  >
    <DropdownMenuAugment.Trigger>
      <ButtonAugment
        size={1}
        variant="soft"
        color="neutral"
        class="c-thread-type-filter-dropdown__button"
      >
        {#if showLabel}
          <div class="c-thread-type-filter-dropdown__text">
            {selection}
          </div>
        {:else}
          <div class="c-thread-type-filter-dropdown__icon">
            <FilterIcon />
          </div>
        {/if}
        <svelte:fragment slot="iconRight">
          {#if isDropdownOpen}
            <ChevronUp />
          {:else}
            <ChevronDown />
          {/if}
        </svelte:fragment>
      </ButtonAugment>
    </DropdownMenuAugment.Trigger>
    <DropdownMenuAugment.Content side="bottom" align="start" size={1}>
      <div class="filters-list">
        {#each threadTypeFilters as filter}
          <DropdownMenuAugment.Item
            highlight={$activeThreadTypeFilter === filter.value}
            onSelect={() => {
              activeThreadTypeFilter.set(filter.value);
              requestCloseFilterDropdown();
            }}
          >
            <div class="filter-item">
              {#if filter.value !== "all"}
                <svelte:component this={MODE_DISPLAY_INFO[filter.value].icon} />
              {/if}
              <span class="filter-label">
                <TextAugment size={1}>
                  {filter.label}
                </TextAugment>
              </span>
              <span class="filter-count">{filter.count}</span>
            </div>
          </DropdownMenuAugment.Item>
        {/each}
      </div>
    </DropdownMenuAugment.Content>
  </DropdownMenuAugment.Root>
</div>

<style>
  .c-thread-type-filter-dropdown :global(.c-thread-type-filter-dropdown__button) {
    --base-btn-padding-horizontal: var(--ds-spacing-2);
    --icon-size: var(--ds-icon-size-0);
  }

  .c-thread-type-filter-dropdown__icon :global(svg) {
    opacity: 1;
  }

  /* Filter dropdown styles */
  .filters-list {
    display: flex;
    flex-direction: column;
  }

  .filter-item {
    --icon-size: var(--ds-icon-size-1);
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-2);
    width: 100%;
  }

  .filter-label {
    flex: 1;
  }

  .filter-count {
    opacity: 0.6;
    font-size: 0.9em;
    margin-left: auto;
    transition: color 0.05s ease-out;
  }
</style>
