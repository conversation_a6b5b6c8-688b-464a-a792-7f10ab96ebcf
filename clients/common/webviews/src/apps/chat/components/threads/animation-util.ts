import { cubicOut } from "svelte/easing";
import { type TransitionConfig } from "svelte/transition";

export interface SlideScaleParams {
  delay?: number;
  duration?: number;
  easing?: (t: number) => number;
  axis?: "x" | "y";
  scale?: number;
}

/**
 * Slides an element in and out while scaling its contents.
 *
 * Based on Svelte's built-in slide transition but adds content scaling.
 * The element slides in/out along the specified axis while the contents
 * scale from/to the specified scale value.
 *
 * @param node - The element to animate
 * @param params - Animation parameters
 * @returns TransitionConfig for Svelte
 */
export function slideScale(
  node: Element,
  { delay = 0, duration = 400, easing = cubicOut, axis = "y", scale = 0 }: SlideScaleParams = {},
): TransitionConfig {
  const style = getComputedStyle(node);
  const opacity = +style.opacity;
  /* eslint-disable @typescript-eslint/naming-convention */
  const primary_property = axis === "y" ? "height" : "width";
  const primary_property_value = parseFloat(style[primary_property]);
  const scale_property = axis === "y" ? "scaleY" : "scaleX";
  const secondary_properties = axis === "y" ? ["top", "bottom"] : ["left", "right"];
  const capitalized_secondary_properties = secondary_properties.map(
    (e) => `${e[0].toUpperCase()}${e.slice(1)}`,
  );
  const padding_start_value = parseFloat(
    (style[
      `padding${capitalized_secondary_properties[0]}` as keyof CSSStyleDeclaration
    ] as string) || "0",
  );
  const padding_end_value = parseFloat(
    (style[
      `padding${capitalized_secondary_properties[1]}` as keyof CSSStyleDeclaration
    ] as string) || "0",
  );
  const margin_start_value = parseFloat(
    (style[
      `margin${capitalized_secondary_properties[0]}` as keyof CSSStyleDeclaration
    ] as string) || "0",
  );
  const margin_end_value = parseFloat(
    (style[
      `margin${capitalized_secondary_properties[1]}` as keyof CSSStyleDeclaration
    ] as string) || "0",
  );
  const border_width_start_value = parseFloat(
    (style[
      `border${capitalized_secondary_properties[0]}Width` as keyof CSSStyleDeclaration
    ] as string) || "0",
  );
  const border_width_end_value = parseFloat(
    (style[
      `border${capitalized_secondary_properties[1]}Width` as keyof CSSStyleDeclaration
    ] as string) || "0",
  );
  /* eslint-enable @typescript-eslint/naming-convention */

  return {
    delay,
    duration,
    easing,
    css: (t) => {
      const currentScale = scale + (1 - scale) * t;
      /* eslint-disable @typescript-eslint/naming-convention */
      return (
        "overflow: hidden;" +
        `opacity: ${t * opacity};` +
        `${primary_property}: ${t * primary_property_value}px;` +
        `padding-${secondary_properties[0]}: ${t * padding_start_value}px;` +
        `padding-${secondary_properties[1]}: ${t * padding_end_value}px;` +
        `margin-${secondary_properties[0]}: ${t * margin_start_value}px;` +
        `margin-${secondary_properties[1]}: ${t * margin_end_value}px;` +
        `border-${secondary_properties[0]}-width: ${t * border_width_start_value}px;` +
        `border-${secondary_properties[1]}-width: ${t * border_width_end_value}px;` +
        `transform: ${scale_property}(${currentScale});` +
        `transform-origin: center;`
      );
      /* eslint-enable @typescript-eslint/naming-convention */
    },
  };
}
