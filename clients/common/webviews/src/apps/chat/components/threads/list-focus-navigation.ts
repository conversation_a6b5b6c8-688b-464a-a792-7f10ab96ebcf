/* eslint-disable @typescript-eslint/naming-convention */
export const FOCUS_LIST_ITEM_CLASS = "focusable-item";
/* eslint-enable @typescript-eslint/naming-convention */

/**
 * Get all focusable items within a container element
 */
function getFocusableItems(container: HTMLElement): HTMLElement[] {
  return Array.from(container.querySelectorAll(`.${FOCUS_LIST_ITEM_CLASS}`)) as HTMLElement[];
}

/**
 * Wrap index within bounds (handles negative indices)
 */
function wrapIndex(index: number, itemCount: number): number {
  return ((index % itemCount) + itemCount) % itemCount;
}

/**
 * Get the index of the currently focused item within a container
 * @param container The container element
 * @returns The index of the currently focused item, or -1 if no item is focused
 */
export function getCurrentIndex(items: HTMLElement[]): number {
  const activeItem = document.activeElement?.closest(`.${FOCUS_LIST_ITEM_CLASS}`);
  return items.findIndex((item) => item === activeItem);
}

/**
 * Focus a specific item by index
 * @param container The container element
 * @param index The index to focus, or -1 to focus the last item
 */
export function focusItemAtIndex(items: HTMLElement[], index: number) {
  let targetIndex = index;
  if (index === -1) {
    targetIndex = items.length - 1;
  } else if (index >= items.length) {
    targetIndex = items.length - 1;
  } else if (index < 0) {
    targetIndex = 0;
  }

  items[targetIndex]?.focus();
}

/**
 * Focus the next item (wraps to first if at end)
 */
export function focusNextItem(items: HTMLElement[]) {
  const nextIndex = wrapIndex(getCurrentIndex(items) + 1, items.length);
  items[nextIndex]?.focus();
}

/**
 * Focus the previous item (wraps to last if at beginning)
 */
export function focusPreviousItem(items: HTMLElement[]) {
  const previousIndex = wrapIndex(getCurrentIndex(items) - 1, items.length);
  items[previousIndex]?.focus();
}

/**
 * Svelte action for list keyboard navigation
 * Usage: <div use:listNavigation>
 */
export function listFocusNavigation(container: HTMLElement) {
  const handleKeyDown = (e: KeyboardEvent) => {
    // Only handle arrow keys if the focus is on a focusable item
    const activeElement = document.activeElement as HTMLElement;
    if (!activeElement.closest(`.${FOCUS_LIST_ITEM_CLASS}`) && !container.contains(activeElement)) {
      return;
    }
    const items = getFocusableItems(container);
    if (items.length === 0) return;

    switch (e.key) {
      case "ArrowUp":
        e.preventDefault();
        focusPreviousItem(items);
        break;
      case "ArrowDown":
        e.preventDefault();
        focusNextItem(items);
        break;
      case "Home":
        e.preventDefault();
        focusItemAtIndex(items, 0);
        break;
      case "End":
        e.preventDefault();
        focusItemAtIndex(items, -1);
        break;
    }
  };

  document.addEventListener("keydown", handleKeyDown);

  return {
    destroy() {
      document.removeEventListener("keydown", handleKeyDown);
    },
  };
}
