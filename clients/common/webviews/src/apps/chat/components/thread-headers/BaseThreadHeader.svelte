<script lang="ts">
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import InlineEditTextAugment from "$common-webviews/src/common/components/InlineEditTextAugment.svelte";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";

  // SVELTE5_MIGRATION - AU-11875
  // Define props interface for Svelte 5 compatibility
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  interface $$Props {
    title?: string;
    isSquished?: boolean;
    isEditing?: boolean;
    isEditable?: boolean;
    hideTitle?: boolean;
    showEditIcon?: boolean;
    onRename?: (_newValue: string) => void;
    children?: any;
  }

  // Props
  export let title: string = "";
  export let isSquished: boolean = false;
  export let isEditing: boolean = false;
  export let isEditable: boolean = true;
  export let hideTitle: boolean = false;
  /**
   * Controls whether the edit icon is displayed in the header
   * @default true
   */
  export let showEditIcon: boolean = true;
  export let onRename = (_newValue: string) => {};

  // Expose window height binding to parent components
  let windowHeight = 0;

  $: effectiveIsSquished = isSquished || windowHeight < 700;

  function onEditClick() {
    isEditing = isEditable && true;
  }
  //The node for which we will ignore click events i they happen.
  let outsideNode: HTMLElement | undefined;
</script>

<svelte:window bind:innerHeight={windowHeight} />

<div
  class="c-thread-header"
  class:c-thread-header--is-editing={isEditable && isEditing}
  class:c-thread-header--is-editable={isEditable}
  bind:this={outsideNode}
>
  <!-- Banner slot for status information (e.g., "Running in the cloud") -->
  <div class="c-thread-header__banner">
    <slot name="banner">
      <!-- Default banner content -->
    </slot>
  </div>
  <!-- Title area -->
  <div class="c-thread-header__title">
    {#if showEditIcon}
      <IconButtonAugment
        size={0.5}
        class="c-thread-header__title-icon"
        variant="ghost-block"
        disabled={!isEditable}
        color="neutral"
        on:click={onEditClick}
        title="Edit Thread Title"
      >
        <span class="c-thread-header__title-icon-content">
          <slot name="icon" />
        </span>
      </IconButtonAugment>
    {/if}

    <div class="c-thread-header__title-main">
      <slot name="title-notice" />
      {#if !hideTitle}
        <slot name="title">
          <TextTooltipAugment
            content={"Rename"}
            nested={false}
            hasPointerEvents={false}
            class="c-thread-header__title-text"
          >
            <InlineEditTextAugment
              {outsideNode}
              size={1}
              weight="bold"
              value={title || "New Thread"}
              color="primary"
              {isEditable}
              bind:isEditing
              showCommitButton={false}
              singleClickToEdit
              onChange={onRename}
            ></InlineEditTextAugment>
          </TextTooltipAugment>
        </slot>
      {/if}
    </div>
    {#if !isEditing}
      <slot name="title-actions" />
    {/if}
  </div>

  <!-- Info row for metadata (e.g., repository, branch) -->
  {#if !effectiveIsSquished}
    <div class="c-thread-header__info-row">
      <slot name="info-row">
        <!-- Default info row content -->
      </slot>
    </div>
  {/if}

  <!-- Status area for file changes summary or other status information -->
  <div class="c-thread-header__status">
    <slot name="status">
      <!-- Default status content -->
    </slot>
  </div>
</div>

<style>
  .c-thread-header {
    position: relative;
    display: flex;
    flex-direction: column;
    margin-left: calc(0px - var(--chat-padding));
    margin-right: calc(0px - var(--chat-padding));
    border-top: 1px solid var(--augment-border-color);
    padding: var(--ds-spacing-2) var(--ds-spacing-3);
    width: var(--augment-chat-max-width);
    transition: background-color 0.15s ease;
    flex: none;
  }
  .c-thread-header:not(.c-thread-header--is-editable) {
    padding-top: 0;
  }
  /** hide edit icon when editing*/
  .c-thread-header--is-editing :global(.c-thread-header__title-icon) {
    display: none;
  }
  .c-thread-header__banner {
    display: inline-flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    background-color: var(--ds-color-neutral-2);
    padding: 0.5px var(--ds-spacing-3) 0.5px var(--ds-spacing-3);
    color: var(--vscode-descriptionForeground);
    font-size: 0.66rem;
    gap: var(--ds-spacing-2);
    transition: background-color 0.15s ease;
    &:empty {
      display: none;
    }
  }

  .c-thread-header__title {
    margin-bottom: 4px;
    display: flex;
    flex-direction: row;
    gap: var(--ds-spacing-1);
    align-items: center;
    & :global(.c-thread-header__title-icon-content) {
      display: unset;
    }
    & :global(.c-thread-header__title-icon-pencil) {
      display: none;
    }
  }
  .c-thread-header--is-editable {
    & :global(.c-thread-header__title-icon) {
      cursor: pointer;
    }
  }
  .c-thread-header__title :global(.c-thread-header__title-text) {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    line-clamp: 1;
    -webkit-box-orient: vertical;
    flex: 1;
    width: 100%;
    overflow: hidden;
  }

  .c-thread-header__title-main {
    flex: 1;
    min-width: 0;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .c-thread-header__info-row:not(:empty) {
    width: 100%;
    padding: 4px var(--ds-spacing-2) 0;
    font-size: 0.7rem;
    color: var(--vscode-descriptionForeground);
    margin-bottom: var(--ds-spacing-2);
  }

  .c-thread-header__status {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-2);
  }

  .c-thread-header__status:empty {
    margin-bottom: calc(0px - var(--ds-spacing-2));
  }

  .c-thread-header__status:not(:empty) {
    padding: 0 var(--ds-spacing-2);
  }
</style>
