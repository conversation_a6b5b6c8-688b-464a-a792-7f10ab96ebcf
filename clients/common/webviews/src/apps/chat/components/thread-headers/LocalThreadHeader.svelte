<script lang="ts">
  import { getContext } from "svelte";
  import { type ChatModel } from "../../models/chat-model";
  import BaseThreadHeader from "./BaseThreadHeader.svelte";
  import { type ExchangeWithStatus } from "../../types/chat-message";
  import ThreadMenu from "$common-webviews/src/apps/chat/components/threads/ThreadMenu.svelte";
  import type { ChatThread } from "$common-webviews/src/apps/chat/components/threads/ThreadsPanel.svelte";
  import { ConversationModel } from "../../models/conversation-model";
  import AgentIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/wand-magic-sparkles.svg?component";
  import RegularMessageIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/message.svg?component";
  import ConversationNavigation from "../conversation/navigation";
  import SeparatorAugment from "$common-webviews/src/design-system/components/SeparatorAugment.svelte";

  // Get the chat model from context
  const chatModel = getContext<ChatModel>("chatModel");
  $: conversationModel = $chatModel.currentConversationModel;
  $: isNew = ConversationModel.isNew(conversationModel);

  $: conversationId = $chatModel.currentConversationId || "";
  $: isLocalAgent = conversationModel?.extraData?.isAgentConversation === true;

  // Get the current conversation
  $: conversation = $chatModel.conversations[conversationId];
  $: title = conversation?.name || getFirstMessage() || `New ${isLocalAgent ? "Agent" : "Chat"}`;

  $: threadForMenu = {
    id: conversationId,
    type: isLocalAgent ? "localAgent" : "chat",
    title: title,
    isPinned: conversation?.isPinned || false,
    conversation: {
      id: conversationId,
      isShareable: conversation?.isShareable || false,
    },
  } as ChatThread;

  // Function to handle thread renaming
  function onRename(newTitle: string) {
    chatModel.renameConversation(conversationId, newTitle);
  }
  let isEditing = false;
  function onRenameThread() {
    setTimeout(() => {
      //this should ignore the mouse event that would otherwise cancel this event, because of the portal.
      isEditing = true;
    }, 10);
  }
  const getFirstMessage = () => {
    return (
      (
        (conversation?.chatHistory || []).find(
          (item) => (item as ExchangeWithStatus).request_message,
        ) as ExchangeWithStatus
      )?.request_message || ""
    );
  };
</script>

<BaseThreadHeader {onRename} {title} bind:isEditing>
  <svelte:fragment slot="icon">
    {#if isLocalAgent}
      <AgentIcon />
    {:else}
      <RegularMessageIcon />
    {/if}
  </svelte:fragment>
  <div slot="title-actions" class="c-thread-header__menu">
    {#if !isNew}
      <ThreadMenu
        thread={threadForMenu}
        type={isLocalAgent ? "localAgent" : "chat"}
        enableShareService={$chatModel.flags?.enableShareService}
        onRename={onRenameThread}
      />
      <ConversationNavigation.Controls>
        <SeparatorAugment size={1} orientation="vertical" />
      </ConversationNavigation.Controls>
    {/if}
  </div>
</BaseThreadHeader>

<style>
  .c-thread-header__menu {
    display: flex;
  }
</style>
