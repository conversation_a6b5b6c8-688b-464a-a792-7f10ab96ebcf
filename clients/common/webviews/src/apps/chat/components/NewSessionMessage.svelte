<script lang="ts">
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import { getContext } from "svelte";
  import { fade } from "svelte/transition";
  import { type ChatModel } from "../models/chat-model";
  import CalloutAugment from "$common-webviews/src/design-system/components/CalloutAugment.svelte";
  import InfoCircled from "$common-webviews/src/design-system/icons/info-circled.svelte";

  export let chatModel = getContext<ChatModel>("chatModel");
  export let onNewConversation = () => {
    chatModel.setCurrentConversation();
  };
  const idleStatus = chatModel.idleMessageModel.idleStatus;
  const focusAfterIdle = chatModel.idleMessageModel.focusAfterIdle;
</script>

{#if $chatModel.currentConversationModel?.chatHistory?.length > 0 && $idleStatus !== "busy" && $focusAfterIdle && chatModel.idleMessageModel.messageEnabled}
  {#if $idleStatus === "idle-message"}
    <div transition:fade={{ duration: 200 }}>
      <CalloutAugment variant="soft" color="info">
        <InfoCircled slot="icon" />
        This session is getting long. You can create as many sessions as you want and return to them
        in the dropdown.
      </CalloutAugment>
    </div>
  {/if}
  <div transition:fade={{ duration: 200 }} class="c-new-session-message__button">
    <ButtonAugment size={1} on:click={onNewConversation} variant="soft"
      >+ Create new session</ButtonAugment
    >
  </div>
{/if}

<style>
  .c-new-session-message__button {
    display: flex;
    justify-content: center;
    & :global(button) {
      flex: 1;
    }
  }
</style>
