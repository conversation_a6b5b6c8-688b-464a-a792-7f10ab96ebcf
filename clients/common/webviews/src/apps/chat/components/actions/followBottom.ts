import { throttle } from "lodash";
import { scrollTo, type ScrollToOptions } from "../../utils/scroll-utils";

export interface FollowBottomProps extends ScrollToOptions {
  follow: boolean;
  scrollContainer?: HTMLElement | undefined;
}

/**
 * A svelte action that follows an element as it changes size.
 *
 * @param element
 * @param props
 * @returns
 */
export function followElement(element: HTMLElement, props: FollowBottomProps) {
  let currProps: FollowBottomProps = props;
  let cancelCurrAutoScroll: (() => void) | undefined = undefined;

  // Tries to scroll to the current element, reading from `currProps`
  const tryScrollToElement = throttle(
    () => {
      if (currProps.follow && currProps.scrollContainer) {
        cancelCurrAutoScroll?.();
        cancelCurrAutoScroll = scrollTo(currProps.scrollContainer, element, currProps);
      }
    },
    300,
    { leading: true, trailing: true },
  );

  // Starts following the element immediately
  const follow = () => {
    resizeObserver.observe(element);
    tryScrollToElement();
  };

  // Stops following the element immediately
  const unfollow = () => {
    cancelCurrAutoScroll?.();
    resizeObserver.disconnect();
  };

  // Create a ResizeObserver to track the current element as it changes size
  const resizeObserver: ResizeObserver = new ResizeObserver(tryScrollToElement);
  if (currProps.follow) {
    resizeObserver.observe(element);
    tryScrollToElement();
  }

  return {
    update: (props: FollowBottomProps) => {
      const prevProps = currProps;
      currProps = props;
      if (prevProps.follow !== currProps.follow) {
        props.follow ? follow() : unfollow();
      }
    },
    destroy: unfollow,
  };
}
