import throttle from "lodash.throttle";

export interface TrackSeenProps {
  onSeen: () => void;
  track: boolean;
}

/**
 * Tracks whether an element has been visually seen by a user or not
 *
 * @param element
 * @param props
 * @returns
 */
export function trackSeen(element: HTMLElement, props: TrackSeenProps) {
  let currProps: TrackSeenProps = props;
  const markSeen = throttle(
    () => {
      currProps.onSeen();
    },
    1000,
    { leading: true, trailing: true },
  );

  // Observe the element to see if it is intersecting the viewport
  const intersectionObserver: IntersectionObserver = new IntersectionObserver(
    (entries: IntersectionObserverEntry[]) => {
      // We should only be observing one element at a time
      if (entries.length !== 1) {
        console.warn("Unexpected number of intersection entries: ", entries);
        return;
      }

      // If the element is intersecting our viewport, then we have been seen
      if (entries[0].isIntersecting) {
        markSeen();
      }
    },
    { threshold: 0.5 },
  );

  // Maybe track the element
  const trackOrUntrackElement = () => {
    intersectionObserver.disconnect();
    if (currProps.track) {
      intersectionObserver.observe(element);
    }
  };
  trackOrUntrackElement();

  return {
    update(props: TrackSeenProps) {
      const prevProps = currProps;
      currProps = props;
      if (currProps.track !== prevProps.track) {
        trackOrUntrackElement();
      }
    },
    destroy: () => {
      intersectionObserver.disconnect();
      currProps.onSeen();
    },
  };
}
