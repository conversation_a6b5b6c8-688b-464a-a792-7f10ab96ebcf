/**
 * A Svelte action that detects when a sticky header becomes "stuck" at the top of the viewport.
 *
 * This uses an Intersection Observer to detect when a sentinel element placed just above
 * the sticky header leaves the viewport, which indicates the header is now sticky.
 *
 * @param node - The sticky header element to observe
 * @param options - Configuration options
 * @param options.onStuck - Callback function when the header becomes sticky
 * @param options.onUnstuck - Callback function when the header is no longer sticky
 * @param options.offset - Offset for detecting intersections with the sentinel element
 * @returns An object with a destroy method to clean up resources
 */
export function detectStickyHeader(
  node: HTMLElement,
  options: {
    onStuck?: () => void;
    onUnstuck?: () => void;
    offset?: number; // Add an offset parameter
  },
) {
  const { onStuck, onUnstuck, offset = 0 } = options;

  // Create a sentinel element that will be placed above the sticky element
  const sentinel = document.createElement("div");
  sentinel.style.position = "absolute";
  sentinel.style.top = offset ? `${offset}px` : "0"; // Apply offset if provided
  sentinel.style.height = "1px";
  sentinel.style.width = "100%";
  sentinel.style.pointerEvents = "none";
  sentinel.style.opacity = "0";
  sentinel.style.zIndex = "-1";

  // Get the parent container to properly position the sentinel
  const container = node.parentNode;
  if (!container) {
    return { update: () => {}, destroy: () => {} };
  }

  // Make sure the container has position relative for absolute positioning to work
  if (window.getComputedStyle(container as Element).position === "static") {
    (container as HTMLElement).style.position = "relative";
  }

  // Insert the sentinel at the top of the container, before the header
  container.insertBefore(sentinel, node);

  // Create an intersection observer to watch the sentinel
  const observer = new IntersectionObserver(
    ([entry]) => {
      // When the sentinel is not intersecting, the header is sticky
      if (!entry.isIntersecting) {
        onStuck?.();
      } else {
        onUnstuck?.();
      }
    },
    { threshold: 0, rootMargin: "-1px 0px 0px 0px" },
  );

  // Start observing the sentinel
  observer.observe(sentinel);

  return {
    update(newOptions: { onStuck?: () => void; onUnstuck?: () => void; offset?: number }) {
      // Update callbacks if options change
      options.onStuck = newOptions.onStuck;
      options.onUnstuck = newOptions.onUnstuck;

      // Update offset if it changes
      if (newOptions.offset !== undefined && newOptions.offset !== offset) {
        sentinel.style.top = `${newOptions.offset}px`;
      }
    },
    destroy() {
      // Clean up
      observer.disconnect();
      sentinel.remove();
    },
  };
}
