import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import { detectStickyHeader } from "./detectStickyHeader";

describe("detectStickyHeader", () => {
  let container: HTMLDivElement;
  let header: HTMLDivElement;
  let observer: { observe: ReturnType<typeof vi.fn>; disconnect: ReturnType<typeof vi.fn> };

  beforeEach(() => {
    // Set up DOM elements
    container = document.createElement("div");
    // Set position to relative to test our fix
    container.style.position = "relative";
    header = document.createElement("div");
    container.appendChild(header);
    document.body.appendChild(container);

    // Mock IntersectionObserver
    observer = {
      observe: vi.fn(),
      disconnect: vi.fn(),
    };

    vi.spyOn(window, "IntersectionObserver").mockImplementation((callback) => {
      setTimeout(() => {
        // Simulate the header becoming sticky (sentinel not intersecting)
        callback(
          [{ isIntersecting: false } as IntersectionObserverEntry],
          {} as IntersectionObserver,
        );
      }, 0);

      return observer as unknown as IntersectionObserver;
    });

    // Mock getComputedStyle
    vi.spyOn(window, "getComputedStyle").mockReturnValue({
      position: "relative",
    } as CSSStyleDeclaration);
  });

  afterEach(() => {
    document.body.removeChild(container);
    vi.restoreAllMocks();
  });

  it("should call onStuck when the header becomes sticky", () => {
    const onStuck = vi.fn();
    const onUnstuck = vi.fn();

    const action = detectStickyHeader(header, { onStuck, onUnstuck });

    // Verify the sentinel was created and observer was called
    expect(observer.observe).toHaveBeenCalled();

    // Wait for the mock IntersectionObserver callback to be called
    return new Promise<void>((resolve) => {
      setTimeout(() => {
        expect(onStuck).toHaveBeenCalled();
        expect(onUnstuck).not.toHaveBeenCalled();

        // Now simulate the header becoming unstuck
        vi.spyOn(window, "IntersectionObserver").mockImplementation((callback) => {
          setTimeout(() => {
            callback(
              [{ isIntersecting: true } as IntersectionObserverEntry],
              {} as IntersectionObserver,
            );
          }, 0);

          return observer as unknown as IntersectionObserver;
        });

        // Call destroy to clean up
        action.destroy();
        expect(observer.disconnect).toHaveBeenCalled();

        resolve();
      }, 10);
    });
  });

  it("should handle container with static position", () => {
    // Mock getComputedStyle to return static position
    vi.spyOn(window, "getComputedStyle").mockReturnValue({
      position: "static",
    } as CSSStyleDeclaration);

    const onStuck = vi.fn();
    const action = detectStickyHeader(header, { onStuck });

    // Verify the sentinel was created and observer was called
    expect(observer.observe).toHaveBeenCalled();

    // Clean up
    action.destroy();
  });

  it("should handle missing parent container", () => {
    // Create a detached element
    const detachedHeader = document.createElement("div");

    const onStuck = vi.fn();
    const action = detectStickyHeader(detachedHeader, { onStuck });

    // No error should be thrown
    expect(action).toBeDefined();

    // Clean up
    action.destroy();
  });
});
