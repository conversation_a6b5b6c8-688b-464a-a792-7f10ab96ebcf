export interface TrackHeightProps {
  onHeightChange?: (height: number) => void;
}

/**
 * Tracks the height of the element and calls the onHeightChange callback when the height changes.
 *
 * @param element
 * @param props
 * @returns
 */
export function trackHeight(element: HTMLElement, props: TrackHeightProps) {
  let prevHeight = element.offsetHeight;
  let currProps: TrackHeightProps = props;

  const resizeObserver: ResizeObserver = new ResizeObserver((entries: ResizeObserverEntry[]) => {
    const height = entries[0].contentRect.height;
    if (entries.length !== 1) {
      console.warn("Unexpected number of resize entries: ", entries);
      return;
    }
    if (height !== prevHeight) {
      currProps.onHeightChange?.(height);
      prevHeight = height;
    }
  });
  resizeObserver.observe(element);
  currProps?.onHeightChange?.(prevHeight);

  return {
    update(props: TrackHeightProps) {
      currProps = props;
    },
    destroy: () => resizeObserver.disconnect(),
  };
}
