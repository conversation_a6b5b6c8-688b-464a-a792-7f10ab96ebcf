<script lang="ts">
  import type { ChatModel } from "$common-webviews/src/apps/chat/models/chat-model";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import ExclamationTriangle from "$common-webviews/src/design-system/icons/exclamation-triangle.svelte";
  import type { ISourceFolderInfo } from "$vscode/src/workspace/types";
  import "@radix-ui/colors/yellow.css";
  import "@radix-ui/colors/yellow-dark.css";
  import { getContext } from "svelte";
  import PencilOne from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/pen-to-square.svg?component";

  let clazz = "";
  export { clazz as class };
  export let sourceFolder: ISourceFolderInfo;
  const chatModel: ChatModel = getContext("chatModel");

  const handleClick = () => {
    chatModel.extensionClient.openGuidelines(sourceFolder.folderRoot);
  };

  $: guidelinesText = !sourceFolder.guidelinesOverLimit
    ? "Edit workspace guidelines"
    : `Workspace guidelines exceeded ${sourceFolder.guidelinesLengthLimit} character limit`;
</script>

<TextAugment size={1}>
  <div class={`c-guidelines-filespan ${clazz}`}>
    {#if !sourceFolder.guidelinesOverLimit}
      <PencilOne />
    {:else}
      <ExclamationTriangle slot="leftIcon" class="c-guidelines-filespan-warning-icon" />
    {/if}
    <span
      class="c-guidelines-filespan__text"
      on:click={handleClick}
      on:keydown={() => {}}
      role="button"
      tabindex="0"
    >
      {guidelinesText}
    </span>
  </div>
</TextAugment>

<style>
  .c-guidelines-filespan {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-1);
    width: 100%;
  }
  .c-guidelines-filespan > :global(svg) {
    fill: var(--icon-color, currentColor);
    width: 16px;
    height: 16px;
  }

  .c-guidelines-filespan__text {
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .c-guidelines-filespan__text:hover {
    text-decoration: underline;
  }

  :global(.c-guidelines-filespan-warning-icon) {
    color: var(--yellow-7);
    width: 0.9rem;
    height: 0.9rem;
  }
</style>
