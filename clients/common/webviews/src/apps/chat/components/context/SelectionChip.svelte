<script lang="ts">
  import CursorTextIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/i-cursor.svg?component";
  import SelectionHoverContents from "../mentions/SelectionHoverContents.svelte";
  import ContextBadge from "./ContextBadge.svelte";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import BadgeAugment from "$common-webviews/src/design-system/components/BadgeAugment";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import { type FileDetails } from "$vscode/src/webview-providers/webview-messages";

  export let originalCode: string | undefined;
  export let pathName: string | undefined;
  export let range: FileDetails["fullRange"];
</script>

<div>
  <TextTooltipAugment maxWidth="100%" theme={originalCode ? "selection-chip-code" : undefined}>
    <ContextBadge
      color={originalCode ? "info" : "neutral"}
      variant={originalCode ? "soft" : "ghost"}
      hideLeftIcon
    >
      <span class="c-selection-chip__icon">
        <BadgeAugment.IconButton disabled>
          <CursorTextIcon />
        </BadgeAugment.IconButton>
      </span>
    </ContextBadge>

    <svelte:fragment slot="content">
      {#if originalCode}
        <SelectionHoverContents {originalCode} {pathName} {range} />
      {:else}
        <TextAugment size={1} class="tooltip-text">No Code Selected</TextAugment>
      {/if}
    </svelte:fragment>
  </TextTooltipAugment>
</div>

<style>
  .c-selection-chip__icon {
    margin-inline: calc(var(--ds-spacing-1) * -1);
  }
  .c-selection-chip__icon :global(.c-base-btn:disabled) {
    cursor: initial;
    color: inherit;
  }
  .c-selection-chip__icon :global(svg) {
    opacity: 1;
  }

  /* Make tooltip background match code editor background */
  :global(.tippy-box[data-theme~="selection-chip-code"]:has(.l-tooltip-contents)) {
    max-width: unset !important; /** Tippy sets a max-width of 350px by default on the element */
    --text-tooltip-background-color: var(--vscode-editor-background, var(--gray-12));
    --text-tooltip-text-color: var(--vscode-editor-foreground, var(--gray-1));
    border: solid 1px var(--ds-color-neutral-a5);

    & .tippy-content {
      padding: 0;
    }
  }
</style>
