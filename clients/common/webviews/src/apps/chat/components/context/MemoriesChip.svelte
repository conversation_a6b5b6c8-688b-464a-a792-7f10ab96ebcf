<script lang="ts">
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import MemoriesIconRegular from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/box-archive.svg?component";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
</script>

<TextTooltipAugment
  content="Agent Memories"
>
  <IconButtonAugment size={0.5} variant="ghost-block" color="neutral" on:click on:keydown>
    <MemoriesIconRegular />
  </IconButtonAugment>
</TextTooltipAugment>
