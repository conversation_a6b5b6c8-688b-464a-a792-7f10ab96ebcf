<script lang="ts">
  import RegularBookOpenIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/book-open.svg?component";
  import RegularFileIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/file.svg?component";
  import RegularFolderOpenIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/folder-open.svg?component";
  import RegularFolderGearIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/folder-gear.svg?component";
  import RegularRulerIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/ruler-vertical.svg?component";

  export let icon: string;
</script>

{#if icon === "file"}
  <RegularFileIcon />
{:else if icon === "folder-opened"}
  <RegularFolderOpenIcon />
{:else if icon === "root-folder"}
  <RegularFolderGearIcon />
{:else if icon === "book"}
  <RegularBookOpenIcon />
{:else if icon === "rule"}
  <RegularRulerIcon />
{/if}
