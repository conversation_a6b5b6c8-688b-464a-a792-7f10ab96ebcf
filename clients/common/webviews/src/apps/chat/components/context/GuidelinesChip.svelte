<script lang="ts">
  import { onKey } from "$common-webviews/src/common/utils/keypress";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import ExclamationTriangle from "$common-webviews/src/design-system/icons/exclamation-triangle.svelte";
  import "@radix-ui/colors/yellow.css";
  import "@radix-ui/colors/yellow-dark.css";
  import type { IChatMentionableItem } from "../../types/mention-option";
  import { getChatModel } from "../../contexts/chat-model-context";
  import { getRulesModel } from "../../contexts/rules-model-context";
  import RegularBookSparklesIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/book-sparkles.svg?component";
  import { type IContextInfo } from "../../models/types";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import { calculateRulesAndGuidelinesCharacterCount } from "../../utils/rules-utils";
  export let item: IChatMentionableItem<"userGuidelines"> & IContextInfo;

  const chatModel = getChatModel();
  const rulesModel = getRulesModel();

  // Get workspace guidelines content from the context model and calculate total character count
  // to determine warning text
  $: contextModel = $chatModel?.specialContextInputModel;
  $: workspaceGuidelines = $contextModel?.workspaceGuidelines
    ? $contextModel?.workspaceGuidelines[0]
    : undefined;
  $: rulesAndGuidelinesLimit = workspaceGuidelines?.lengthLimit;
  $: userGuidelinesLimit = item.userGuidelines.lengthLimit;
  $: rules = rulesModel.getCachedRules();
  $: contextRules = contextModel.activeRules.map((r) => r.rule);
  $: ({
    isOverLimit: showRulesAndGuidelinesWarning,
    warningMessage: rulesAndGuidelinesWarningMessage,
  } = calculateRulesAndGuidelinesCharacterCount({
    rules: $rules,
    workspaceGuidelinesContent: workspaceGuidelines?.contents || "",
    contextRules,
    rulesAndGuidelinesLimit,
  }));

  $: enableRules = $chatModel?.flags.enableRules ?? false;
  $: showUserGuidelinesWarning = item.userGuidelines.overLimit;
  $: isOverLimit = showUserGuidelinesWarning || showRulesAndGuidelinesWarning;

  $: displayText = enableRules ? "Rules & Guidelines" : "Guidelines";

  function getGuidelinesErrorMessage() {
    if (showRulesAndGuidelinesWarning) {
      return rulesAndGuidelinesWarningMessage;
    } else if (showUserGuidelinesWarning) {
      return `Guidelines exceeded length limit of ${userGuidelinesLimit} characters`;
    }
  }

  function openSettingsPanel() {
    // Open the settings panel to access user guidelines
    if ($chatModel) {
      // Pass 'guidelines' as the section to navigate to
      $chatModel.extensionClient.openSettingsPage("guidelines");
    }
  }
</script>

<TextTooltipAugment content={isOverLimit ? getGuidelinesErrorMessage() : `${displayText}`}>
  <IconButtonAugment
    size={0.5}
    variant="ghost-block"
    color={isOverLimit ? "warning" : "neutral"}
    on:click={openSettingsPanel}
    on:keyup={onKey("Enter", openSettingsPanel)}
  >
    {#if isOverLimit}
      <ExclamationTriangle />
    {:else}
      <RegularBookSparklesIcon />
    {/if}
  </IconButtonAugment>
</TextTooltipAugment>
