<script lang="ts">
  import { type BadgeSize } from "$common-webviews/src/design-system/components/BadgeAugment/badge-types";
  import BadgeRoot from "$common-webviews/src/design-system/components/BadgeAugment/BadgeRoot.svelte";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";

  export let loading: boolean = false;
  export let totalAddedLines: number = 0;
  export let totalRemovedLines: number = 0;
  export let size: BadgeSize = 1;
  export let tooltips: boolean = false;

  function createMessage(type: "added" | "removed", count: number) {
    if (count === 0) return "";
    return `${count} line${count === 1 ? "" : "s"} ${type}`;
  }
</script>

{#if loading}
  <!-- Do nothing -->
{:else if totalAddedLines > 0 || totalRemovedLines > 0}
  <div class="edit-item__changes">
    {#if totalAddedLines > 0}
      {#if tooltips}
        <TextTooltipAugment content={createMessage("added", totalAddedLines)}>
          <BadgeRoot color="success" {size} variant="soft">+{totalAddedLines}</BadgeRoot>
        </TextTooltipAugment>
      {:else}
        <BadgeRoot color="success" {size} variant="soft">+{totalAddedLines}</BadgeRoot>
      {/if}
    {/if}
    {#if totalRemovedLines > 0}
      {#if tooltips}
        <TextTooltipAugment content={createMessage("removed", totalRemovedLines)}>
          <BadgeRoot color="error" {size} variant="soft">-{totalRemovedLines}</BadgeRoot>
        </TextTooltipAugment>
      {:else}
        <BadgeRoot color="error" {size} variant="soft">-{totalRemovedLines}</BadgeRoot>
      {/if}
    {/if}
  </div>
{:else}
  <div class="edit-item__changes">
    <BadgeRoot color="neutral" {size} variant="soft">No changes</BadgeRoot>
  </div>
{/if}

<style>
  .edit-item__changes {
    display: flex;
    flex-shrink: 0;
    align-items: center;
    min-width: fit-content;
    gap: var(--ds-spacing-0_5);

    & :global(.edit-item__added-lines) {
      color: var(--ds-color-success-7);
    }

    & :global(.edit-item__removed-lines) {
      color: var(--ds-color-error-7);
    }
  }
</style>
