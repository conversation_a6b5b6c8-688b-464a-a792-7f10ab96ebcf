<script lang="ts">
  import { TooltipTriggerOn } from "$common-webviews/src/design-system/_primitives/TooltipAugment/types";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import KeepAll from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/check-double.svg?component";
  import DiscardAll from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/arrow-turn-left.svg?component";
  import type { CheckpointStore } from "../../models/checkpoint-store";
  import { getContext } from "svelte";

  const checkpointStore = getContext<CheckpointStore>("checkpointStore");
  const { targetCheckpointHasChanges } = checkpointStore;

  async function handleRevert() {
    await checkpointStore.rejectAll();
  }

  async function handleKeep() {
    await checkpointStore.acceptAll();
  }
</script>

<div class="c-bottom-controls">
  <ButtonAugment
    class="c-bottom-controls__revert-btn"
    variant="soft"
    color="neutral"
    size={1}
    disabled={!$targetCheckpointHasChanges}
    on:click={handleRevert}
  >
    <TextTooltipAugment
      triggerOn={[TooltipTriggerOn.Hover]}
      content={$targetCheckpointHasChanges ? "Discard all changes" : "No changes to undo"}
    >
      <DiscardAll />
      Discard All
    </TextTooltipAugment>
  </ButtonAugment>
  <ButtonAugment
    class="c-bottom-controls__accept-btn"
    variant="soft"
    color="success"
    size={1}
    disabled={!$targetCheckpointHasChanges}
    on:click={handleKeep}
  >
    <TextTooltipAugment
      triggerOn={[TooltipTriggerOn.Hover]}
      content={$targetCheckpointHasChanges
        ? "Keep all changes and clear checkpoints"
        : "No changes to keep"}
    >
      <KeepAll />
      Keep All
    </TextTooltipAugment>
  </ButtonAugment>
</div>

<style>
  .c-bottom-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--ds-spacing-1);
    gap: var(--ds-spacing-1);

    & :global(.l-tooltip-trigger) {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: var(--ds-spacing-1);
    }

    & :global(.c-bottom-controls__revert-btn),
    & :global(.c-bottom-controls__accept-btn) {
      & :global(.c-button--content) {
        width: 100%;
      }
      flex: 1;
    }
  }
</style>
