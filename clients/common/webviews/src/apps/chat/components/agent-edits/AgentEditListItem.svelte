<!-- A component that displays a single file edit entry with path, changes, and controls -->
<script lang="ts">
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import Filespan from "$common-webviews/src/common/components/Filespan.svelte";
  import { type IQualifiedPathName } from "@augment-internal/sidecar-libs/src/workspace/workspace-types";
  import { type ChatAgentFileChangeSummary } from "$vscode/src/webview-providers/webview-messages";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import { TooltipTriggerOn } from "$common-webviews/src/design-system/_primitives/TooltipAugment/types";
  import EditChangeSummary from "./EditChangeSummary.svelte";
  import OpenInNewWindow from "$common-webviews/src/design-system/icons/open-in-new-window.svelte";
  import Trash from "$common-webviews/src/design-system/icons/trash.svelte";
  import Diff from "$common-webviews/src/design-system/icons/diff.svelte";
  import Check from "$common-webviews/src/design-system/icons/check.svelte";
  import DotsHorizontal from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/ellipsis.svg?component";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import Rocket from "$common-webviews/src/design-system/icons/rocket.svelte";
  import { noop } from "lodash";

  export let qualifiedPathName: IQualifiedPathName;
  export let lineChanges: ChatAgentFileChangeSummary | undefined;
  export let isApplyPending: boolean = false;
  export let hasApplied: boolean = false;
  export let isFocused: boolean = false;

  export let onClickReview: (() => void) | undefined = undefined;
  export let onClickFile: (() => void) | undefined = undefined;
  export let onClickRevert: (() => void) | undefined = undefined;
  export let onApplyChanges: (() => void) | undefined = undefined;
</script>

<div class="edit-item__container" class:edit-item__container--focused={isFocused}>
  <div class="edit-item__info-container">
    <ButtonAugment
      class="edit-item__file-button"
      size={0}
      variant={isFocused ? "soft" : "ghost"}
      color={isFocused ? "accent" : "neutral"}
      on:click={onClickReview || noop}
    >
      <TextTooltipAugment
        delayDurationMs={1500}
        triggerOn={[TooltipTriggerOn.Hover]}
        content={qualifiedPathName.relPath}
      >
        <Filespan class="edit-item__filespan" size={1} filepath={qualifiedPathName.relPath}>
          <Diff slot="rightIcon" />
        </Filespan>
      </TextTooltipAugment>
    </ButtonAugment>
  </div>

  <slot name="actions" />

  {#if lineChanges}
    <ButtonAugment size={0} variant="ghost" color="neutral" on:click={onClickReview || noop}>
      <TextTooltipAugment triggerOn={[TooltipTriggerOn.Hover]} content="View Diff">
        <EditChangeSummary
          size={0}
          totalAddedLines={lineChanges.totalAddedLines}
          totalRemovedLines={lineChanges.totalRemovedLines}
        />
      </TextTooltipAugment>
    </ButtonAugment>
  {/if}
  {#if onApplyChanges || onClickFile || onClickRevert}
    <div class="edit-item__controls-container">
      {#if onApplyChanges}
        {#if hasApplied}
          <TextTooltipAugment
            triggerOn={[TooltipTriggerOn.Hover]}
            content="Changes applied"
            class="edit-item__controls-container__applied"
          >
            <TextAugment size={1}>
              Applied
              <Check />
            </TextAugment>
          </TextTooltipAugment>
        {:else}
          <IconButtonAugment size={0} variant="ghost" color="neutral" on:click={onApplyChanges}>
            <TextTooltipAugment
              triggerOn={[TooltipTriggerOn.Hover]}
              content={isApplyPending ? "Applying changes..." : "Apply changes"}
            >
              {#if isApplyPending}
                <DotsHorizontal />
              {:else}
                <Rocket />
              {/if}
            </TextTooltipAugment>
          </IconButtonAugment>
        {/if}
      {/if}

      {#if onClickFile}
        <IconButtonAugment size={0} variant="ghost" color="neutral" on:click={onClickFile}>
          <TextTooltipAugment triggerOn={[TooltipTriggerOn.Hover]} content="Open File">
            <OpenInNewWindow />
          </TextTooltipAugment>
        </IconButtonAugment>
      {/if}
      {#if onClickRevert}
        <IconButtonAugment size={0} variant="ghost" color="neutral" on:click={onClickRevert}>
          <TextTooltipAugment triggerOn={[TooltipTriggerOn.Hover]} content="Undo Changes">
            <Trash />
          </TextTooltipAugment>
        </IconButtonAugment>
      {/if}
    </div>
  {/if}
</div>

<style>
  .edit-item__container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 100%;
    gap: var(--ds-spacing-1);
    transition: background-color 0.2s ease-in-out;

    &.edit-item__container--focused {
      background-color: var(--ds-color-surface-accent-subtle);
    }

    & :global(.edit-item__controls-container) {
      display: flex;
      align-items: center;
      gap: var(--ds-spacing-2);
      padding: var(--ds-spacing-1);

      & :global(.edit-item__controls-container__applied) {
        display: flex;
        align-items: center;
        gap: var(--ds-spacing-1);
        opacity: 0.5;
      }
    }

    & :global(.edit-item__info-container) {
      display: flex;
      flex-direction: row;
      gap: var(--ds-spacing-1);
      min-width: 0; /* Allow container to shrink */
      flex: 1; /* Take up available space */
      overflow: hidden; /* Ensure content doesn't overflow */

      /* Make sure nothing overflow between the filespan and the changes */
      max-width: 100%;
      & :global(:has(.edit-item__filespan)) {
        max-width: 100%;
        width: 100%;
      }
    }

    & :global(.edit-item__file-button) {
      /* Hide the icon by default, show on hover */
      & :global(.edit-item__filespan > svg) {
        opacity: 0;
      }
      &:hover :global(.edit-item__filespan > svg) {
        opacity: 1;
      }
    }

    & :global(.edit-item__filespan) {
      flex-grow: 1;
      min-width: 0;
      overflow: hidden;
    }
  }
</style>
