/* eslint-disable @typescript-eslint/naming-convention */
import { render, screen } from "@testing-library/svelte";
import { vi, describe, it, expect, beforeEach, afterEach } from "vitest";
import { get } from "svelte/store";
import { writable } from "svelte/store";
import ModelPicker from "./ModelPicker.svelte";
import { ModelRegistry, DEFAULT_MODEL } from "../../models/model-registry";

// Mock the ChatModel that implements Svelte store pattern
const createMockChatModel = (agentChatModel: string = "") => {
  const mockExtensionClient = {
    reportAgentSessionEvent: vi.fn(),
  };

  const mockCurrentConversationModel = {
    id: "test-conversation-id",
    selectedModelId: null as string | null | undefined,
    setSelectedModelId: vi.fn(),
    extensionClient: mockExtensionClient,
  };

  const mockFlags = {
    agentChatModel,
    modelRegistry: {
      "gpt5-reasoning-medium-200k-v7-c4-p2-agent": "GPT-5 Reasoning",
      "claude-3-sonnet": "Claude 3 Sonnet",
    },
  };

  const store = writable({
    currentConversationModel: mockCurrentConversationModel,
    flags: mockFlags,
  });

  return {
    subscribe: store.subscribe,
    currentConversationModel: mockCurrentConversationModel,
    flags: mockFlags,
  } as any;
};

// Create a test wrapper component that provides the necessary context
const TestWrapper = (Component: any, props: any = {}) => {
  const mockChatModel = createMockChatModel();
  return {
    Component,
    props,
    context: new Map<string, any>([
      ["modelRegistry", new ModelRegistry(mockChatModel)],
      ["chatModel", mockChatModel],
    ]),
  };
};

describe("ModelPicker", () => {
  let modelRegistry: ModelRegistry;
  let mockChatModel: any;

  beforeEach(() => {
    mockChatModel = createMockChatModel();
    modelRegistry = new ModelRegistry(mockChatModel);
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it("should display default model initially", () => {
    const mockChatModel = createMockChatModel("test-default-model");
    // Create a new ModelRegistry with the mock ChatModel
    const testModelRegistry = new ModelRegistry(mockChatModel);

    const wrapper = TestWrapper(ModelPicker);
    wrapper.context.set("modelRegistry", testModelRegistry);
    wrapper.context.set("chatModel", mockChatModel);

    // Register the default model so it can be found
    testModelRegistry.registerModels([
      { id: "test-default-model", displayName: "Test Default Model" },
    ]);

    render(wrapper.Component, {
      props: wrapper.props,
      context: wrapper.context,
    });

    // The default model name should be hidden in the button (empty text)
    // when it's the selected model
    const modelPickerContainer = document.querySelector(".c-model-picker");
    expect(modelPickerContainer).toBeInTheDocument();

    // The dropdown should show the default model name, not "Default"
    expect(screen.queryByText(DEFAULT_MODEL.displayName)).toBeNull();
  });

  it("should update when models are registered", async () => {
    const wrapper = TestWrapper(ModelPicker);
    wrapper.context.set("modelRegistry", modelRegistry);

    render(wrapper.Component, {
      props: wrapper.props,
      context: wrapper.context,
    });

    // Initially should only have the default model
    expect(get(modelRegistry)).toHaveLength(1);
    expect(get(modelRegistry)[0].displayName).toBe(DEFAULT_MODEL.displayName);

    // Register new models
    const testModels = [
      { id: "gpt-4", displayName: "GPT-4" },
      { id: "claude-3", displayName: "Claude 3" },
    ];

    modelRegistry.registerModels(testModels);

    // Wait for the component to update
    await new Promise((resolve) => setTimeout(resolve, 10));

    // Should now show the registered models in the registry
    expect(get(modelRegistry)).toHaveLength(2); // 2 test models
    expect(get(modelRegistry).some((m) => m.displayName === "GPT-4")).toBe(true);
    expect(get(modelRegistry).some((m) => m.displayName === "Claude 3")).toBe(true);

    // The component should still render without errors after the update
    // The default model name should still be hidden in the button
    expect(screen.queryByText(DEFAULT_MODEL.displayName)).toBeNull();

    // But the component should still be present
    const modelPickerContainer = document.querySelector(".c-model-picker");
    expect(modelPickerContainer).toBeInTheDocument();
  });

  it("should handle model selection", () => {
    const mockChatModel = createMockChatModel("test-default-model");
    const testModelRegistry = new ModelRegistry(mockChatModel);
    const wrapper = TestWrapper(ModelPicker);
    wrapper.context.set("modelRegistry", testModelRegistry);
    wrapper.context.set("chatModel", mockChatModel);

    // Register the default model so it can be found
    testModelRegistry.registerModels([
      { id: "test-default-model", displayName: "Test Default Model" },
    ]);

    render(wrapper.Component, {
      props: wrapper.props,
      context: wrapper.context,
    });

    // This test verifies the component renders without errors
    const modelPickerContainer = document.querySelector(".c-model-picker");
    expect(modelPickerContainer).toBeInTheDocument();

    // Should not show "Default" when a proper default model is configured
    expect(screen.queryByText(DEFAULT_MODEL.displayName)).toBeNull();
  });

  describe("fallback behavior", () => {
    it("should fall back to defaultModelId when selectedModelId is null", async () => {
      const defaultModelId = "gpt5-reasoning-medium-200k-v7-c4-p2-agent";
      const mockChatModel = createMockChatModel(defaultModelId);
      const testModelRegistry = new ModelRegistry(mockChatModel);
      const wrapper = TestWrapper(ModelPicker);

      // Set up the context with our mock chat model
      wrapper.context.set("modelRegistry", testModelRegistry);
      wrapper.context.set("chatModel", mockChatModel);

      // Register the default model in the registry so it can be found
      testModelRegistry.registerModels([{ id: defaultModelId, displayName: "GPT-5 Reasoning" }]);

      // Set selectedModelId to null (should fall back to defaultModelId)
      mockChatModel.currentConversationModel.selectedModelId = null;

      render(wrapper.Component, {
        props: wrapper.props,
        context: wrapper.context,
      });

      // Wait for reactive updates
      await new Promise((resolve) => setTimeout(resolve, 10));

      // Test the reactive store behavior directly
      const selectedModelStore = testModelRegistry.getModelStore(defaultModelId);
      const selectedModel = get(selectedModelStore);

      expect(selectedModel.id).toBe(defaultModelId);
      expect(selectedModel.displayName).toBe("GPT-5 Reasoning");
    });

    it("should fall back to defaultModelId when selectedModelId is undefined", async () => {
      const defaultModelId = "gpt5-reasoning-medium-200k-v7-c4-p2-agent";
      const mockChatModel = createMockChatModel(defaultModelId);
      const testModelRegistry = new ModelRegistry(mockChatModel);
      const wrapper = TestWrapper(ModelPicker);

      wrapper.context.set("modelRegistry", testModelRegistry);
      wrapper.context.set("chatModel", mockChatModel);

      testModelRegistry.registerModels([{ id: defaultModelId, displayName: "GPT-5 Reasoning" }]);

      // Set selectedModelId to undefined (should fall back to defaultModelId)
      mockChatModel.currentConversationModel.selectedModelId = undefined;

      render(wrapper.Component, {
        props: wrapper.props,
        context: wrapper.context,
      });

      await new Promise((resolve) => setTimeout(resolve, 10));

      // Test the reactive store behavior
      const selectedModelStore = testModelRegistry.getModelStore(defaultModelId);
      const selectedModel = get(selectedModelStore);

      expect(selectedModel.id).toBe(defaultModelId);
      expect(selectedModel.displayName).toBe("GPT-5 Reasoning");
    });

    it("should use selectedModelId when it has a valid value", async () => {
      const defaultModelId = "gpt5-reasoning-medium-200k-v7-c4-p2-agent";
      const selectedModelId = "claude-3-sonnet";
      const mockChatModel = createMockChatModel(defaultModelId);
      const testModelRegistry = new ModelRegistry(mockChatModel);
      const wrapper = TestWrapper(ModelPicker);

      wrapper.context.set("modelRegistry", testModelRegistry);
      wrapper.context.set("chatModel", mockChatModel);

      testModelRegistry.registerModels([
        { id: defaultModelId, displayName: "GPT-5 Reasoning" },
        { id: selectedModelId, displayName: "Claude 3 Sonnet" },
      ]);

      // Set selectedModelId to a valid value (should NOT fall back)
      mockChatModel.currentConversationModel.selectedModelId = selectedModelId;

      render(wrapper.Component, {
        props: wrapper.props,
        context: wrapper.context,
      });

      await new Promise((resolve) => setTimeout(resolve, 10));

      // Test the reactive store behavior
      const selectedModelStore = testModelRegistry.getModelStore(selectedModelId);
      const selectedModel = get(selectedModelStore);

      expect(selectedModel.id).toBe(selectedModelId);
      expect(selectedModel.displayName).toBe("Claude 3 Sonnet");
    });

    it("should reactively update when model registry changes", async () => {
      const defaultModelId = "gpt5-reasoning-medium-200k-v7-c4-p2-agent";

      // Start with no models registered
      const selectedModelStore = modelRegistry.getModelStore(defaultModelId);

      // Initially should return the default model since the ID isn't found
      let selectedModel = get(selectedModelStore);

      expect(selectedModel.id).toBe(null);
      expect(selectedModel.displayName).toBe("Default");

      // Now register the model
      modelRegistry.registerModels([{ id: defaultModelId, displayName: "GPT-5 Reasoning" }]);

      // The store should reactively update to return the actual model
      selectedModel = get(selectedModelStore);

      expect(selectedModel.id).toBe(defaultModelId);
      expect(selectedModel.displayName).toBe("GPT-5 Reasoning");
    });
  });

  describe("disabled models", () => {
    it("should render disabled models with disabled state", () => {
      const mockChatModel = createMockChatModel("test-default-model");
      const testModelRegistry = new ModelRegistry(mockChatModel);
      const wrapper = TestWrapper(ModelPicker);
      wrapper.context.set("modelRegistry", testModelRegistry);
      wrapper.context.set("chatModel", mockChatModel);

      // Register models with one disabled
      testModelRegistry.registerModels([
        { id: "test-default-model", displayName: "Test Default Model" },
        { id: "disabled-model", displayName: "Disabled Model", disabled: true },
      ]);

      render(wrapper.Component, {
        props: wrapper.props,
        context: wrapper.context,
      });

      // This test verifies the component renders without errors with disabled models
      const modelPickerContainer = document.querySelector(".c-model-picker");
      expect(modelPickerContainer).toBeInTheDocument();
    });

    it("should not allow selection of disabled models", () => {
      const mockChatModel = createMockChatModel("test-default-model");
      const testModelRegistry = new ModelRegistry(mockChatModel);
      const wrapper = TestWrapper(ModelPicker);
      wrapper.context.set("modelRegistry", testModelRegistry);
      wrapper.context.set("chatModel", mockChatModel);

      // Register models with one disabled
      testModelRegistry.registerModels([
        { id: "test-default-model", displayName: "Test Default Model" },
        { id: "disabled-model", displayName: "Disabled Model", disabled: true },
      ]);

      render(wrapper.Component, {
        props: wrapper.props,
        context: wrapper.context,
      });

      // Verify that setSelectedModelId is not called for disabled models
      expect(mockChatModel.currentConversationModel.setSelectedModelId).not.toHaveBeenCalledWith(
        "disabled-model",
      );
    });

    it("should show tooltip for disabled models with disabled_reason", () => {
      const mockChatModel = createMockChatModel("test-default-model");
      const testModelRegistry = new ModelRegistry(mockChatModel);
      const wrapper = TestWrapper(ModelPicker);
      wrapper.context.set("modelRegistry", testModelRegistry);
      wrapper.context.set("chatModel", mockChatModel);

      // Register models with one disabled that has a disabled_reason
      testModelRegistry.registerModels([
        { id: "test-default-model", displayName: "Test Default Model" },
        {
          id: "disabled-model",
          displayName: "Disabled Model",
          disabled: true,
          disabled_reason: "This model is currently unavailable",
        },
      ]);

      render(wrapper.Component, {
        props: wrapper.props,
        context: wrapper.context,
      });

      // This test verifies the component renders without errors with disabled models that have tooltips
      const modelPickerContainer = document.querySelector(".c-model-picker");
      expect(modelPickerContainer).toBeInTheDocument();
    });
  });
});
