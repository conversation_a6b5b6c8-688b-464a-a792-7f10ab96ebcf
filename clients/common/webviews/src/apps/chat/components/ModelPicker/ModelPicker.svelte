<script lang="ts">
  import { getContext } from "svelte";
  import DropdownMenuAugment from "$common-webviews/src/design-system/components/DropdownMenuAugment";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import type DropdownMenuRoot from "$common-webviews/src/design-system/components/DropdownMenuAugment/Root.svelte";
  import type { ChatModel } from "../../models/chat-model";
  import { DEFAULT_NAME, type ModelRegistry } from "../../models/model-registry";
  import ModelIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/sliders-simple.svg?component";
  import CheckIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/check.svg?component";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import CommandContent from "../InputActionBar/CommandContent.svelte";
  import { AgentSessionEventName } from "@augment-internal/sidecar-libs/src/metrics/types";
  import { TooltipTriggerOn } from "$common-webviews/src/design-system/_primitives/TooltipAugment/types";
  import { get } from "svelte/store";
  import type { IModel } from "../../models/model-registry";

  const modelRegistry: ModelRegistry = getContext("modelRegistry");
  const chatModel: ChatModel = getContext("chatModel");
  $: conversationModel = $chatModel.currentConversationModel;

  let dropdownRoot: DropdownMenuRoot;

  // Use ModelRegistry's built-in derived stores
  const { formattedModels, selectedModel } = modelRegistry;

  // Function to select a model
  function selectModel(model: IModel) {
    // Don't allow selection of disabled models
    if (model.disabled) {
      return;
    }

    if (conversationModel) {
      // Get the current model before making the change
      const previousModelId = $selectedModel.id ?? null;
      const previousModel = modelRegistry.getModelStore(previousModelId);

      // Only proceed if the model is actually changing
      if (previousModelId !== model.id) {
        // Get the new model information
        const newModel = modelRegistry.getModelStore(model.id);

        // Update the selected model
        conversationModel.setSelectedModelId(model.id);
        const modelSelectionChangeData = {
          previousModelId: get(previousModel).id,
          previousModelName: get(previousModel).displayName,
          newModelId: get(newModel).id,
          newModelName: get(newModel).displayName,
        };

        // Report the model selection change event
        conversationModel.extensionClient.reportAgentSessionEvent({
          eventName: AgentSessionEventName.modelSelectionChange,
          conversationId: conversationModel.id,
          eventData: {
            modelSelectionChangeData,
          },
        });
      }
    }
    dropdownRoot?.requestClose();
  }
</script>

{#snippet dropdownItem(model: IModel)}
  {@const selected =
    $selectedModel.id === model.id && $selectedModel.displayName === model.displayName}
  <DropdownMenuAugment.Item
    highlight={selected}
    disabled={model.disabled}
    onSelect={() => {
      selectModel(model);
    }}
  >
    <span class="c-model-picker__item-text">
      {model.displayName}
    </span>
    <svelte:fragment slot="iconRight">
      {#if selected}
        <CheckIcon />
      {/if}
    </svelte:fragment>
  </DropdownMenuAugment.Item>
{/snippet}

<div class="c-model-picker">
  <DropdownMenuAugment.Root bind:this={dropdownRoot} nested={false}>
    <DropdownMenuAugment.Trigger>
      <CommandContent>
        <ButtonAugment size={1} variant="ghost-block" color="neutral">
          <ModelIcon slot="iconLeft" />
          <!-- Hide the name if default -->
          <!-- Red dot indicator for disabled selected model -->
          {#if $selectedModel.disabled}
            <TextTooltipAugment
              content={$selectedModel.disabled_reason}
              triggerOn={[TooltipTriggerOn.Hover]}
              side="right"
              nested={true}
            >
              <span class="c-model-picker__text-wrapper">
                <TextAugment size={1}
                  >{$selectedModel.displayName === DEFAULT_NAME
                    ? ""
                    : $selectedModel.displayName}</TextAugment
                >
                <span class="c-model-picker__disabled-indicator"></span>
              </span>
            </TextTooltipAugment>
          {:else}
            <TextAugment size={1}
              >{$selectedModel.displayName === DEFAULT_NAME
                ? ""
                : $selectedModel.displayName}</TextAugment
            >
          {/if}
        </ButtonAugment>
      </CommandContent>
    </DropdownMenuAugment.Trigger>
    <DropdownMenuAugment.Content side="top" align="start">
      {#each $formattedModels as model}
        {#if model.disabled && model.disabled_reason}
          <!-- Wrap disabled items with tooltip if they have a disabled reason -->
          <TextTooltipAugment
            content={model.disabled_reason}
            triggerOn={[TooltipTriggerOn.Hover]}
            side="right"
            nested={true}
          >
            {@render dropdownItem(model)}
          </TextTooltipAugment>
        {:else}
          <!-- Regular items without tooltip -->
          {@render dropdownItem(model)}
        {/if}
      {/each}
    </DropdownMenuAugment.Content>
  </DropdownMenuAugment.Root>
</div>

<style>
  .c-model-picker__item-text {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-1);
  }

  .c-model-picker {
    display: inline-flex;
    /* Allow the component to shrink when space is limited */
    min-width: 0;
    flex-shrink: 1;
    /* Prevent text wrapping and ensure proper alignment */
    white-space: nowrap;
    align-items: center;
  }

  .c-model-picker__text-wrapper {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-2);
  }

  .c-model-picker__disabled-indicator {
    display: inline-block;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: var(--ds-color-error-a11);
    flex-shrink: 0;
  }
</style>
