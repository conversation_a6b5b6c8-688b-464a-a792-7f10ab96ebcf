<script lang="ts">
  import { host } from "$common-webviews/src/common/hosts/host";
  import { MessageBroker } from "$common-webviews/src/common/utils/message-broker";
  import CalloutAugment, {
    type CalloutColor,
  } from "$common-webviews/src/design-system/components/CalloutAugment.svelte";
  import CheckIcon from "$common-webviews/src/design-system/icons/check.svelte";
  import ExclamationTriangle from "$common-webviews/src/design-system/icons/exclamation-triangle.svelte";
  import UpdateIcon from "$common-webviews/src/design-system/icons/update.svelte";
  import {
    OrientationState,
    type OrientationStatus,
    type WebViewMessage,
    WebViewMessageType,
  } from "$vscode/src/webview-providers/webview-messages";
  import { onMount } from "svelte";
  import { derived, writable } from "svelte/store";

  const msgBroker = new MessageBroker(host);

  // Store to track orientation status
  const orientationStatus = writable<OrientationStatus>({ state: OrientationState.idle });
  // Derived store to check if orientation is in progress
  const orientationState = derived(orientationStatus, ($status) => $status.state);

  // Create a message consumer for orientation status updates
  const orientationStatusConsumer = {
    handleMessageFromExtension(e: MessageEvent<WebViewMessage>) {
      if (e.data && e.data.type === WebViewMessageType.orientationStatusUpdate) {
        orientationStatus.set(e.data.data);
        return true;
      }
      return false;
    },
  };

  onMount(() => {
    // Register the consumer
    msgBroker.registerConsumer(orientationStatusConsumer);

    // Request the current orientation status when the component is mounted
    host.postMessage({
      type: WebViewMessageType.getOrientationStatus,
    });
  });

  let color: CalloutColor;
  let msg: string;
  let displayingOrientationProgress = false;

  function getColorForState(state: OrientationState): CalloutColor {
    if (state === OrientationState.succeeded) return "success";
    if (state === OrientationState.failed) return "error";
    if (state === OrientationState.aborted) return "error";
    // user yellow color for orientation progress bar
    return "warning";
  }

  function getMessageForState(state: OrientationState, status: OrientationStatus): string {
    if (state === OrientationState.succeeded)
      return "Orientation complete! Check out `.augment/rules` folder.";

    if (state === OrientationState.failed) {
      if (status.errorMessage) {
        return `Orientation failed: ${status.errorMessage}`;
      }
      return "Orientation failed";
    }

    if (state === OrientationState.aborted) {
      if (status.errorMessage) {
        return `Orientation aborted: ${status.errorMessage}`;
      }
      return "Orientation aborted";
    }

    return "Orienting to codebase and creating rules in `.augment/rules` folder... ";
  }

  $: color = getColorForState($orientationState);
  $: msg = getMessageForState($orientationState, $orientationStatus);
  $: {
    if (displayingOrientationProgress) {
      if (
        $orientationState === OrientationState.succeeded ||
        $orientationState === OrientationState.failed ||
        $orientationState === OrientationState.aborted
      ) {
        // Animate the status bar out after the next UI update
        // (otherwise the animation will be start before we show 100%)
        setTimeout(() => {
          // The width transition is 1s and add 1s to let the user see it
          // complete
          displayingOrientationProgress = false;
        }, 3000);
      }
    } else if ($orientationState === OrientationState.inProgress) {
      // Show the status bar when orientation is running
      displayingOrientationProgress = true;
    }
  }
</script>

<svelte:window on:message={msgBroker.onMessageFromExtension} />

{#if displayingOrientationProgress}
  <CalloutAugment {color} size={1}>
    <svelte:fragment slot="icon">
      {#if $orientationState === OrientationState.succeeded}
        <CheckIcon />
      {:else if $orientationState === OrientationState.failed}
        <ExclamationTriangle />
      {:else}
        <div class="c-orientation-status--spin-icon">
          <UpdateIcon />
        </div>
      {/if}
    </svelte:fragment>
    {msg}{$orientationState === OrientationState.inProgress
      ? ` ${$orientationStatus.progress}%`
      : ""}
  </CalloutAugment>
{/if}

<style>
  .c-orientation-status--spin-icon {
    animation: spin 3s linear infinite;
  }
  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
</style>
