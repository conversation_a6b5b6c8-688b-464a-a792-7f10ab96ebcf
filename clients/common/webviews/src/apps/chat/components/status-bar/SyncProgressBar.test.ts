import { render } from "@testing-library/svelte";
import { expect, describe, test } from "vitest";

import SyncProgressBar from "./SyncProgressBar.svelte";
import { SyncingStatus } from "$vscode/src/workspace/types";

describe("SyncProgressBar.svelte", async () => {
  test("should render in progress index for long running index", async () => {
    const component = render(SyncProgressBar, {
      props: {
        progress: {
          status: SyncingStatus.longRunning,
          totalFiles: 100,
          syncedCount: 77,
          backlogSize: 23,
          newlyTrackedFolders: [],
        },
      },
    });
    expect(component.baseElement.textContent).toContain("Indexing codebase...");
    expect(component.baseElement.textContent).toContain("77%");
  });

  test("should render in progress index for new folder", async () => {
    const component = render(SyncProgressBar, {
      props: {
        progress: {
          status: SyncingStatus.running,
          totalFiles: 100,
          syncedCount: 77,
          backlogSize: 23,
          newlyTrackedFolders: ["./example/folder"],
        },
      },
    });
    expect(component.baseElement.textContent).toContain("Indexing codebase...");
    expect(component.baseElement.textContent).toContain("77%");
  });

  test("should not render complete index", async () => {
    const component = render(SyncProgressBar, {
      props: {
        progress: {
          status: SyncingStatus.done,
          totalFiles: 100,
          syncedCount: 100,
          backlogSize: 0,
          newlyTrackedFolders: ["./example/folder"],
        },
      },
    });
    expect(component.baseElement).toHaveTextContent("");
  });

  test("should render previously indexed folders", async () => {
    const component = render(SyncProgressBar, {
      props: {
        progress: {
          status: SyncingStatus.longRunning,
          totalFiles: 100,
          syncedCount: 77,
          backlogSize: 0,
          newlyTrackedFolders: [],
        },
      },
    });
    expect(component.baseElement).toBeInTheDocument();
    expect(component.baseElement.textContent).toContain("Indexing codebase...");
    expect(component.baseElement.textContent).toContain("77%");
  });

  test("render nothing for empty index progress", async () => {
    const component = render(SyncProgressBar, {
      props: {
        progress: {
          status: SyncingStatus.longRunning,
          totalFiles: 0,
          syncedCount: 100,
          backlogSize: 0,
          newlyTrackedFolders: ["./example/folder"],
        },
      },
    });
    expect(component.baseElement).toHaveTextContent("");
  });

  test("render nothing for undefined index progress", async () => {
    const component = render(SyncProgressBar, {
      props: {
        progress: undefined,
      },
    });
    expect(component.baseElement).toHaveTextContent("");
  });
});
