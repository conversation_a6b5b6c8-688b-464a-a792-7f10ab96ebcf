<script lang="ts">
  import type { WorkspaceSyncingOverview } from "../../models/types";
  import { SyncingStatus } from "$vscode/src/workspace/types";
  import CalloutAugment, {
    type CalloutColor,
  } from "$common-webviews/src/design-system/components/CalloutAugment.svelte";
  import UpdateIcon from "$common-webviews/src/design-system/icons/update.svelte";
  import CheckIcon from "$common-webviews/src/design-system/icons/check.svelte";

  export let progress: WorkspaceSyncingOverview | undefined;

  function getPercentage(progress: WorkspaceSyncingOverview | undefined): number {
    if (!progress || progress.totalFiles <= 0) {
      return NaN;
    }
    if (progress.status === SyncingStatus.done) {
      return 100;
    }
    return Math.floor((progress.syncedCount / progress.totalFiles) * 100);
  }

  let color: CalloutColor;
  $: percentage = getPercentage(progress);
  $: color = progress?.status === SyncingStatus.done ? "success" : "info";
  $: msg = progress?.status === SyncingStatus.done ? "Indexing complete." : "Indexing codebase...";
  $: displayingSyncProgress = false;

  $: {
    if (displayingSyncProgress) {
      if (progress?.status === SyncingStatus.done) {
        // Animate the status bar out after the next UI update
        // (otherwise the animation will be start before we show 100%)
        setTimeout(() => {
          // The width transition is 1s and add 1s to let the user see it
          // complete
          displayingSyncProgress = false;
        }, 3000);
      }
    } else if (progress && progress.status !== SyncingStatus.done) {
      if (
        progress.status === SyncingStatus.longRunning ||
        progress.newlyTrackedFolders.length > 0
      ) {
        // Show the status bar
        displayingSyncProgress = true;
      }
    }
  }
</script>

{#if !isNaN(percentage) && displayingSyncProgress}
  <CalloutAugment {color} size={1}>
    <svelte:fragment slot="icon">
      {#if progress?.status === SyncingStatus.done}
        <CheckIcon />
      {:else}
        <div class="c-sync-status--spin-icon">
          <UpdateIcon />
        </div>
      {/if}
    </svelte:fragment>
    {msg}{progress?.status === SyncingStatus.done ? "" : ` ${percentage}%`}
  </CalloutAugment>
{/if}

<style>
  .c-sync-status--spin-icon {
    animation: spin 3s linear infinite;
  }

  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
</style>
