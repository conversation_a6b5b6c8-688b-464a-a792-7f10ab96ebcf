<script lang="ts">
  import { type SpecialContextInputModel } from "../../models/context-model";
  import OrientationProgressBar from "./OrientationProgressBar.svelte";
  import SyncDisabledBar from "./SyncDisabledBar.svelte";
  import SyncProgressBar from "./SyncProgressBar.svelte";

  export let contextModel: SpecialContextInputModel;
</script>

<SyncDisabledBar {contextModel} />
<SyncProgressBar progress={$contextModel.syncProgress} />
<OrientationProgressBar />
