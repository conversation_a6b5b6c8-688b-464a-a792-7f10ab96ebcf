<script lang="ts">
  import CircleBackslashIcon from "$common-webviews/src/design-system/icons/circle-backslash.svelte";
  import CalloutAugment from "$common-webviews/src/design-system/components/CalloutAugment.svelte";
  import { type SpecialContextInputModel } from "../../models/context-model";

  export let contextModel: SpecialContextInputModel;
</script>

{#if $contextModel.syncEnabledState === "disabled"}
  <CalloutAugment color="error" size={1}>
    <svelte:fragment slot="icon">
      <CircleBackslashIcon />
    </svelte:fragment>
    Syncing disabled
  </CalloutAugment>
{/if}
