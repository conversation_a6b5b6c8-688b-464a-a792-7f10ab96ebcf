<script lang="ts">
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import SpinnerAugment from "$common-webviews/src/design-system/components/SpinnerAugment.svelte";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import { getContext } from "svelte";
  import { writable } from "svelte/store";
  import type { ChatModel } from "../../models/chat-model";
  import Sparkles from "$common-webviews/src/design-system/icons/sparkles.svelte";
  import { RemoteAgentsModel } from "$common-webviews/src/apps/remote-agent-manager/models/remote-agents-model";
  import { RemoteAgentSetupWindowAction } from "@augment-internal/sidecar-libs/src/metrics/types";
  import RegisterCommand from "$common-webviews/src/common/components/keybindings/RegisterCommand.svelte";
  import ShortcutHint from "$common-webviews/src/common/components/keybindings/ShortcutHint.svelte";
  import { TooltipTriggerOn } from "$common-webviews/src/design-system/components/types";

  export let onRewrite: () => void;
  export let isDisabled: boolean = false;
  export let disabledReason: string | undefined = undefined;

  const chatModel = getContext<ChatModel>("chatModel");
  if (!chatModel) {
    throw new Error("ChatModel not found in context");
  }

  const remoteAgentsModel = getContext<RemoteAgentsModel | undefined>(RemoteAgentsModel.key);

  // We'll use chatModel.flags.enableDebugFeatures to conditionally show the button

  // Loading state for the spinner
  const isEnhancing = writable(false);

  // Check if we're in remote agent setup context (creating setup scripts)
  $: isInRemoteAgentSetupContext =
    $remoteAgentsModel?.isActive && !$remoteAgentsModel?.currentAgentId;

  async function enhancePropmt() {
    // Set loading state to true
    $isEnhancing = true;
    // Log rewrite prompt button click in remote agent setup context
    if (isInRemoteAgentSetupContext && remoteAgentsModel) {
      try {
        await remoteAgentsModel.reportRemoteAgentSetupWindowEvent(
          RemoteAgentSetupWindowAction.clickRewritePrompt,
        );
      } catch (error) {
        console.error("Failed to report rewrite prompt button click event:", error);
      }
    }
    try {
      // Call the provided onRewrite function and handle as Promise
      Promise.resolve(onRewrite()).finally(() => {
        // Set loading state back to false when done
        $isEnhancing = false;
      });
    } catch (error) {
      // Set loading state back to false on error
      $isEnhancing = false;
    }
  }
</script>

{#if $chatModel.flags.enablePromptEnhancer}
  <div class="c-rewrite-prompt-button">
    <RegisterCommand name="enhancePrompt" handler={enhancePropmt} />
    <TextTooltipAugment triggerOn={[TooltipTriggerOn.Hover]} delayDurationMs={300}>
      <div slot="content">
        {#if isDisabled && disabledReason}
          {disabledReason}
        {:else}
          {"Enhance prompt "}
          <ShortcutHint commandName="enhancePrompt" keysOnly />
        {/if}
      </div>
      <IconButtonAugment
        disabled={isDisabled}
        size={1}
        variant="ghost-block"
        color="neutral"
        on:click={enhancePropmt}
      >
        {#if $isEnhancing}
          <SpinnerAugment size={1} useCurrentColor={true} />
        {:else}
          <Sparkles />
        {/if}
      </IconButtonAugment>
    </TextTooltipAugment>
  </div>
{/if}

<style>
  .c-rewrite-prompt-button {
    margin-right: var(--ds-spacing-1);
  }
</style>
