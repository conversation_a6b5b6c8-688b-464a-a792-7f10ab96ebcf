<script lang="ts">
  import {
    isItemAllDefaultContext,
    isItemClearContext,
    isItemExternalSource,
    isItemFile,
    isItemFolder,
    isItemGroup,
    isItemPersonality,
    isItemRecentFile,
    isItemRule,
    isItemSelection,
    isItemSourceFolder,
    isItemGuidelines,
  } from "../../types/mention-option";
  import { getPersonalityIconComponent } from "../../utils/personality-icon-mapper";
  import Filespan from "$common-webviews/src/common/components/Filespan.svelte";
  import DropdownMenuAugment from "$common-webviews/src/design-system/components/DropdownMenuAugment";
  import MaterialIcon from "$common-webviews/src/common/components/MaterialIcon.svelte";
  import { type ChatMentionableDropdownData } from "./context-menu-controller";
  import { type ComponentType } from "svelte";
  import TextCombo from "$common-webviews/src/common/components/TextCombo.svelte";
  import Ruler from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/ruler-vertical.svg?component";

  export let item: ChatMentionableDropdownData;
  export let onSelect: () => void;
  export let highlight: boolean | undefined = undefined;

  let component: ComponentType | undefined;
  $: {
    if (item.type === "breadcrumb-back") {
      component = DropdownMenuAugment.BreadcrumbBackItem;
    } else if (item.type === "breadcrumb" && isItemGroup(item)) {
      component = DropdownMenuAugment.BreadcrumbItem;
    } else if (item.type === "item" && !isItemGroup(item)) {
      component = DropdownMenuAugment.Item;
    }
  }
</script>

<svelte:component this={component} {highlight} {onSelect}>
  {#if isItemFile(item)}
    <Filespan filepath={item.file.pathName}>
      <MaterialIcon slot="leftIcon" iconName="description" />
    </Filespan>
  {:else if isItemFolder(item)}
    <Filespan filepath={item.folder.pathName}>
      <MaterialIcon slot="leftIcon" iconName="folder_open" />
    </Filespan>
  {:else if isItemExternalSource(item)}
    <Filespan filepath={item.externalSource.name}>
      <MaterialIcon slot="leftIcon" iconName="import_contacts" />
    </Filespan>
  {:else if isItemSourceFolder(item)}
    <Filespan filepath={item.sourceFolder.folderRoot}>
      <MaterialIcon slot="leftIcon" iconName="folder_managed" />
    </Filespan>
  {:else if isItemSelection(item)}
    <Filespan filepath={item.selection.pathName}>
      <MaterialIcon slot="leftIcon" iconName="text_select_start" />
    </Filespan>
  {:else if isItemRecentFile(item)}
    <Filespan filepath={item.recentFile.pathName}>
      <MaterialIcon slot="leftIcon" iconName="description" />
    </Filespan>
  {:else if isItemRule(item)}
    <Filespan filepath={item.rule.path}>
      <Ruler slot="leftIcon" iconName="rule" />
    </Filespan>
  {:else if isItemGroup(item)}
    {item.label}
  {:else if isItemPersonality(item)}
    <TextCombo>
      <span slot="leftIcon" class="c-context-menu-item__icon">
        <svelte:component this={getPersonalityIconComponent(item.personality.type)} />
      </span>
      <span slot="text">{item.label}</span>
    </TextCombo>
  {:else if isItemAllDefaultContext(item) || isItemClearContext(item) || isItemGuidelines(item)}
    <span class="c-mentionable-group-label">
      <span class="c-mentionable-group-label__text right">{item.label}</span>
    </span>
  {/if}
</svelte:component>

<style>
  .c-context-menu-item__icon > :global(svg) {
    height: var(--ds-icon-size-1);
    width: var(--ds-icon-size-1);
  }
</style>
