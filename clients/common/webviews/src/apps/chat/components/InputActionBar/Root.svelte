<div class="c-input-action-bar">
  <div class="c-input-action-bar__left-align">
    <slot name="leftAlign" />
  </div>
  <div class="c-input-action-bar__right-align">
    <slot name="rightAlign" />
  </div>
</div>

<style>
  :global(.c-input-action-bar svg) {
    --icon-size: var(--ds-icon-size-1);
    --button-icon-size: var(--ds-icon-size-1);
  }
  .c-input-action-bar {
    display: flex;
    flex-direction: row;
    align-items: center;
    container-type: inline-size;
    container-name: c-input-action-bar;
    /* Layout */
    width: 100%;

    & > .c-input-action-bar__left-align {
      display: flex;
      flex-direction: row;
      gap: var(--ds-spacing-1);
      align-items: center;
      flex-wrap: wrap;
    }

    & > .c-input-action-bar__right-align {
      display: flex;
      flex-direction: row;
      gap: var(--ds-spacing-1);
      margin-left: auto;
      align-items: center;
    }
  }
</style>
