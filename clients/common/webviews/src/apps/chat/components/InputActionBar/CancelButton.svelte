<script lang="ts">
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import StopFilled from "$common-webviews/src/design-system/icons/augment/stop-filled.svelte";

  export let cancelAction: () => unknown;
  export let onAnalyticsTrack: (source: "button") => void;

  function handleClick(event: MouseEvent) {
    // Stop event propagation to prevent the richtexteditor from being focused
    event.stopPropagation();
    // Track analytics
    onAnalyticsTrack("button");
    // Call the original cancel action
    cancelAction();
  }
</script>

<div class="c-cancel-button">
  <TextTooltipAugment content="Cancel">
    <ButtonAugment size={1} variant="soft" color="error" on:click={handleClick}>
      <StopFilled />
    </ButtonAugment>
  </TextTooltipAugment>
</div>

<style>
  .c-cancel-button {
    --cancel-stop-icon-size: 15px;
  }
  .c-cancel-button :global(svg) {
    color: var(--ds-color-error-9);
    height: var(--cancel-stop-icon-size);
    width: var(--cancel-stop-icon-size);
  }
</style>
