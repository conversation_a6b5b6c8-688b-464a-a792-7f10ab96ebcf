<script lang="ts">
  import DropdownMenuAugment from "$common-webviews/src/design-system/components/DropdownMenuAugment";
  import { getContext, onDestroy, tick } from "svelte";
  import type { ChatModel } from "../../models/chat-model";
  import type DropdownMenuRoot from "$common-webviews/src/design-system/components/DropdownMenuAugment/Root.svelte";
  import { ContextMenuController } from "./context-menu-controller";
  import ContextMenuItem from "./ContextMenuItem.svelte";
  import { type IChatMentionable } from "../../types/mention-option";
  import RegularAtIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/at.svg?component";
  import CommandContent from "./CommandContent.svelte";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  export let onCloseDropdown: () => void = () => {};
  export let onInsertMentionable: ((mentionable: IChatMentionable) => boolean) | undefined =
    undefined;

  let dropdownRoot: DropdownMenuRoot | undefined;

  const chatModel = getContext<ChatModel>("chatModel");
  if (!chatModel) {
    throw new Error("ChatModel not found in context");
  }

  export const closeDropdown = async () => {
    contextMenuController.closeDropdown();
  };
  export const openDropdown = async () => {
    void contextMenuController.openDropdown();
    await tick();
    focusOnSearchBox();
  };

  export const toggleDropdown = async () => {
    if (contextMenuController.toggleDropdown()) {
      await tick();
      focusOnSearchBox();
    }
  };

  const onKeyDown = async (e: KeyboardEvent) => {
    switch (e.key) {
      case "ArrowLeft":
        e.preventDefault();
        contextMenuController.popBreadcrumb();
        await tick();
        dropdownRoot?.focusIdx(0);
        break;
      case "ArrowRight":
      case "ArrowDown":
      case "ArrowUp":
      case "Enter":
        break;
      default:
        if (!e.defaultPrevented) {
          focusOnSearchBox();
        }
    }
  };

  const focusOnSearchBox = () => {
    dropdownRoot?.focusIdx(-1);
  };

  const focusFirstItem = () => {
    dropdownRoot?.focusIdx(0);
  };

  // Track the state of the search query
  const contextMenuController = new ContextMenuController(chatModel, (mentionable) =>
    onInsertMentionable ? onInsertMentionable(mentionable) : false,
  );
  const { displayItems, active, userQuery } = contextMenuController;
  onDestroy(contextMenuController.dispose);
</script>

<DropdownMenuAugment.Root
  bind:this={dropdownRoot}
  open={$active}
  onOpenChange={(open) => {
    if (!open) {
      onCloseDropdown();
    }
  }}
  nested={false}
>
  <DropdownMenuAugment.Trigger>
    <CommandContent>
      <IconButtonAugment size={0.5} variant="ghost-block" color="neutral" on:click={toggleDropdown}>
        <RegularAtIcon />
      </IconButtonAugment>
    </CommandContent>
  </DropdownMenuAugment.Trigger>
  <DropdownMenuAugment.Content
    size={1}
    side="top"
    align="start"
    onClickOutside={closeDropdown}
    onEscapeKeyDown={closeDropdown}
    onRequestClose={closeDropdown}
    on:keydown={onKeyDown}
  >
    <div class="c-context-menu__items">
      {#each $displayItems as item}
        <ContextMenuItem
          {item}
          onSelect={async () => {
            contextMenuController.selectMentionable(item);
            await tick();
            focusFirstItem();
          }}
        />
      {/each}
    </div>
    {#if $userQuery.length > 0 && $displayItems.length === 0}
      <DropdownMenuAugment.Label>No results found</DropdownMenuAugment.Label>
    {/if}
    <DropdownMenuAugment.Separator />
    <DropdownMenuAugment.TextFieldItem placeholder="Focus context" bind:value={$userQuery} />
  </DropdownMenuAugment.Content>
</DropdownMenuAugment.Root>

<style>
  .c-context-menu__items {
    max-height: 50vh;
    overflow-y: scroll;

    display: flex;
    flex-direction: column;

    /* No scrollbar */
    scrollbar-width: none;
  }
</style>
