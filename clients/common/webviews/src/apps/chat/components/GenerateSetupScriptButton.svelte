<script lang="ts">
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import MagicWand from "$common-webviews/src/design-system/icons/magic-wand.svelte";
  import { RemoteAgentSetupWindowAction } from "@augment-internal/sidecar-libs/src/metrics/types";
  import type { RemoteAgentsModel } from "../../remote-agent-manager/models/remote-agents-model";

  export let remoteAgentsModel: RemoteAgentsModel;
  let loading = false;

  // Starts a new agent with the SETUP_MODE prompt
  const createNewScript = async () => {
    loading = true;
    try {
      // Log auto-generate setup script event
      try {
        await remoteAgentsModel.reportRemoteAgentSetupWindowEvent(
          RemoteAgentSetupWindowAction.autoGenerateSetupScript,
        );
      } catch (error) {
        console.error("Failed to auto-generate setup script event:", error);
      }

      // Set the is_setup_script_agent flag to true when creating a setup script agent
      const draft = remoteAgentsModel.newAgentDraft;
      if (draft) {
        remoteAgentsModel.setNewAgentDraft({
          ...draft,
          isSetupScriptAgent: true,
        });
      }

      const agentId = await remoteAgentsModel.createRemoteAgentFromDraft("SETUP_MODE");
      if (agentId) {
        remoteAgentsModel.setCurrentAgent(agentId);
      }
    } catch (error) {
      console.error("Failed to select setup script generation:", error);
    } finally {
      loading = false;
    }
  };
</script>

<ButtonAugment size={1} variant="solid" color="accent" on:click={createNewScript} {loading}>
  <MagicWand slot="iconLeft" />
  Start Generating Setup Script
</ButtonAugment>
