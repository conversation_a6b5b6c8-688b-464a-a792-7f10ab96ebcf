<script lang="ts">
  import { SELECTED_TURN_INDEX_CONTEXT_KEY } from "$common-webviews/src/apps/remote-agent-manager/models/message-render-options";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import MagnifyingGlass from "$common-webviews/src/design-system/icons/magnifying-glass.svelte";
  import type { ChangedFile } from "@augment-internal/sidecar-libs/src/remote-agent/remote-agent-types";
  import { getContext, onMount } from "svelte";
  import { writable, type Writable } from "svelte/store";
  import { GitReferenceModel } from "../../remote-agent-manager/models/git-reference-model";
  import { RemoteAgentsModel } from "../../remote-agent-manager/models/remote-agents-model";
  import {
    getAggregateChanges,
    getUserMessagePrecedingTurn,
  } from "../../remote-agent-manager/utils";
  import {
    DiffOperations,
    MAX_FILES_EXCEEDED_MESSAGE,
    MAX_FILES_IN_DIFF_WEBVIEW,
  } from "../../remote-agent-manager/utils/diff";
  import { isAgentFromDifferentRepoStore } from "../../remote-agent-manager/utils/repository-utils";
  import EditChangeSummary from "./agent-edits/EditChangeSummary.svelte";

  export let changedFiles: ChangedFile[] = [];
  export let suffix: string = "";
  export let turnIndex: number | undefined = -1;

  const remoteAgentsModel = getContext<RemoteAgentsModel>(RemoteAgentsModel.key);
  const selectedTurnIndex = getContext<Writable<number>>(SELECTED_TURN_INDEX_CONTEXT_KEY);
  const gitReferenceModel = getContext<GitReferenceModel>(GitReferenceModel.key);

  const currentRepoUrl = writable<string>("");
  const isAgentFromDifferentRepo = isAgentFromDifferentRepoStore(currentRepoUrl);

  onMount(async () => {
    try {
      const { remoteUrl, error: remoteUrlError } = await gitReferenceModel.getRemoteUrl();
      if (remoteUrlError) {
        console.error("Failed to get remote url:", remoteUrlError);
        return;
      }
      currentRepoUrl.set(remoteUrl);
    } catch (error) {
      console.error("Failed to get current repository URL:", error);
    }
  });

  $: isDisabled = changedFiles.length > MAX_FILES_IN_DIFF_WEBVIEW;

  $: isActive =
    $remoteAgentsModel.isDiffPanelOpen &&
    // Only show active if the diff panel is open for the current agent
    $remoteAgentsModel.currentAgentId === $remoteAgentsModel.diffPanelAgentId &&
    // For aggregate view (no turnIndex specified)
    ((turnIndex === undefined && $selectedTurnIndex === -1) ||
      // For specific turn view
      (turnIndex !== undefined && $selectedTurnIndex === turnIndex));

  function handleViewChanges(): void {
    if (isDisabled) return;

    if (isActive) {
      $remoteAgentsModel.closeRemoteAgentDiffPanel();
      // Reset the selected turn index when closing the panel
      selectedTurnIndex.set(-1);
      return;
    }

    let filesToShow: ChangedFile[] = [];
    let userPrompt = "";
    let isAggregate = true;
    let turnIdx = -1;

    // Use provided changedFiles if available, otherwise get aggregate changes
    if (changedFiles) {
      filesToShow = changedFiles;

      // Use the provided turnIndex if available
      if (turnIndex !== undefined) {
        turnIdx = turnIndex;
        // If we're showing specific turn changes, we're not showing aggregate changes
        isAggregate = false;
        // Update the selected turn index in the context
        selectedTurnIndex.set(turnIndex);
        userPrompt = getUserMessagePrecedingTurn(
          $remoteAgentsModel.currentConversation?.exchanges ?? [],
          turnIndex,
        );
      } else {
        // For changes without a turnIndex, we're still showing aggregate changes
        isAggregate = true;
        // For aggregate changes, set the selected turn index to -1
        selectedTurnIndex.set(-1);
        // For specific turn changes without a turnIndex, use the most recent user prompt
        userPrompt = getUserMessagePrecedingTurn(
          $remoteAgentsModel.currentConversation?.exchanges ?? [],
          1,
        );
      }
    } else {
      filesToShow = getAggregateChanges($remoteAgentsModel.currentConversation?.exchanges ?? []);
      // We're showing aggregate changes
      isAggregate = true;
      // Set the selected turn index to -1 for aggregate changes
      selectedTurnIndex.set(-1);
      // Get the most recent user prompt for aggregate view
      userPrompt = getUserMessagePrecedingTurn(
        $remoteAgentsModel.currentConversation?.exchanges ?? [],
        1,
      );
    }

    // Make sure we're using the correct turn index
    const currentTurnIdx = turnIdx;

    // Show the remote agent diff panel with the changes
    $remoteAgentsModel.showRemoteAgentDiffPanel({
      turnIdx: currentTurnIdx,
      changedFiles: filesToShow,
      userPrompt: userPrompt,
      sessionSummary: $remoteAgentsModel.currentAgent?.session_summary ?? "",
      isShowingAggregateChanges: isAggregate,
      isAgentFromDifferentRepo:
        !!$remoteAgentsModel.currentAgent &&
        $isAgentFromDifferentRepo($remoteAgentsModel.currentAgent),
    });
  }

  // Calculate summary statistics
  $: filesToShow =
    changedFiles || getAggregateChanges($remoteAgentsModel.currentConversation?.exchanges ?? []);

  $: totalFiles = filesToShow.length;

  // Calculate diff stats for all files
  $: diffStats = filesToShow.map((file) => {
    // Generate a diff for the file
    const diff = DiffOperations.generateDiff(
      file.old_path,
      file.new_path,
      file.old_contents || "",
      file.new_contents || "",
    );
    // Get the diff stats
    return DiffOperations.getDiffObjectStats(diff);
  });

  // Calculate total additions
  $: totalAddedLines = diffStats.reduce((sum, stats) => sum + stats.additions, 0);

  // Calculate total deletions
  $: totalRemovedLines = diffStats.reduce((sum, stats) => sum + stats.deletions, 0);

  $: hasChanges = totalFiles > 0;
</script>

{#if hasChanges}
  <div class="file-changes-summary">
    <div class="file-changes-header">
      <TextTooltipAugment content={isDisabled ? MAX_FILES_EXCEEDED_MESSAGE : "View Diff"}>
        <div
          class="file-changes-info"
          on:click={handleViewChanges}
          on:keydown={(e) => e.key === "Enter" && handleViewChanges()}
          role="button"
          tabindex="0"
          class:active={isActive}
          class:disabled={isDisabled}
        >
          <div class="file-changes-info__text">
            <TextAugment size={1} weight="medium" truncate>
              {totalFiles} file{totalFiles === 1 ? "" : "s"} changed{suffix}
            </TextAugment>
            <EditChangeSummary {totalAddedLines} {totalRemovedLines} />
          </div>
          <MagnifyingGlass />
        </div>
      </TextTooltipAugment>
    </div>
  </div>
{/if}

<style>
  .file-changes-summary {
    display: flex;
    flex-direction: column;
    background-color: var(--ds-color-neutral-1);
    border-radius: var(--ds-radius-4);
    padding: 0;
    gap: 2px;
    width: 100%;
    --l-tooltip-trigger-width: 100%;
  }
  .file-changes-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: var(--ds-color-neutral-a2);
    border-radius: var(--ds-radius-3);
  }

  :global(.light) .file-changes-summary {
    background-color: var(--user-theme-sidebar-background);
  }

  :global(.light) .file-changes-header {
    background-color: var(--ds-color-neutral-2);
  }

  .file-changes-info {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    gap: var(--ds-spacing-2);
    cursor: pointer;
    border: 1px solid transparent;
    border-radius: var(--ds-radius-3);
    padding: calc(var(--ds-spacing-1) * 1.3) var(--ds-spacing-2);

    &:hover:not(.disabled) {
      border-color: var(--ds-color-accent-a6);
      background-color: var(--ds-color-accent-a2);
    }

    &:focus-visible {
      outline: none;
      border-color: var(--ds-color-accent-a8);
      box-shadow: 0 0 0 2px var(--ds-color-accent-a4);
    }
  }

  .file-changes-info__text {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-1);
  }

  .file-changes-info.active {
    border-color: var(--ds-color-accent-a6);
    background-color: var(--ds-color-accent-a2);
  }

  .file-changes-info.disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  @media (max-width: 250px) {
    .file-changes-header {
      padding: var(--ds-spacing-2);
    }

    .file-changes-info {
      flex-direction: column;
      align-items: flex-start;
    }
  }
</style>
