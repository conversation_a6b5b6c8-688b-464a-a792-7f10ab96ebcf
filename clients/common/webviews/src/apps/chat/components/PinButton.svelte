<script lang="ts">
  import BadgeAugment from "$common-webviews/src/design-system/components/BadgeAugment";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import RegularThumbtackIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/thumbtack.svg?component";
  import SolidThumbtackIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/solid/thumbtack.svg?component";

  export let isPinned: boolean = false;
  export let onPin: () => void;
  export let title: string = isPinned ? "Unpin" : "Pin to context";
</script>

<TextTooltipAugment content={title}>
  <BadgeAugment.IconButton
    color="neutral"
    on:click={(e) => {
      e.stopPropagation();
      onPin();
    }}
  >
    <span class="c-pin-button">
      {#if isPinned}
        <SolidThumbtackIcon />
      {:else}
        <RegularThumbtackIcon />
      {/if}
    </span>
  </BadgeAugment.IconButton>
</TextTooltipAugment>

<style>
  .c-pin-button :global(svg) {
    fill: currentColor;
    height: 10px;
    aspect-ratio: 1 / 1;
  }
</style>
