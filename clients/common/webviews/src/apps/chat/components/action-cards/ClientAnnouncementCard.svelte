<script lang="ts">
  import InfoCircled from "$common-webviews/src/design-system/icons/info-circled.svelte";
  import { getContext } from "svelte";
  import BaseCard from "./BaseCard.svelte";
  import { type ChatModel } from "../../models/chat-model";

  const chatModel = getContext<ChatModel>("chatModel");

  // Feature flag for client announcement (string type)
  $: flagsModel = $chatModel.flags;
  $: clientAnnouncement = $flagsModel.clientAnnouncement;
  $: displayedAnnouncements = $chatModel.displayedAnnouncements;
  $: shouldShowBanner =
    !!clientAnnouncement &&
    clientAnnouncement.length > 0 &&
    !$displayedAnnouncements.includes(clientAnnouncement);
</script>

<!-- Callout for Client Announcement at the top -->
{#if shouldShowBanner}
  <BaseCard
    color="accent"
    description={clientAnnouncement}
    onClose={async () => {
      // Close the banner by updating our local store
      shouldShowBanner = false;
      chatModel.displayedAnnouncements.update((announcements) => {
        announcements.push(clientAnnouncement);
        return announcements;
      });
      chatModel.saveImmediate();
    }}
  >
    <InfoCircled slot="icon" />
  </BaseCard>
{/if}
