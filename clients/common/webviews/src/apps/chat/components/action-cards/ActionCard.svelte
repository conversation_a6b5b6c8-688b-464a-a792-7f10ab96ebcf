<script lang="ts">
  import {
    DerivedStateName,
    UIOnlyActionName,
    type AnyActionName,
  } from "$vscode/src/main-panel/action-cards/types";
  import ButtonsActionCard from "./ButtonsActionCard.svelte";
  import BaseCard from "./BaseCard.svelte";
  import type { ChatFlagsModel } from "../../models/chat-flags-model";
  import InfoCircled from "$common-webviews/src/design-system/icons/info-circled.svelte";
  import ExclamationTriangle from "$common-webviews/src/design-system/icons/exclamation-triangle.svelte";

  export let card: AnyActionName;
  export let flagsModel: ChatFlagsModel;
</script>

{#if card === DerivedStateName.signInRequired}
  <ButtonsActionCard
    color="neutral"
    description={"Welcome to Augment! Sign in to your existing account or create a new one to get started."}
    buttons={[
      {
        text: "Start using Augment",
        action: "sign-in",
      },
    ]}
  >
    <InfoCircled slot="icon" />
  </ButtonsActionCard>
{:else if card === UIOnlyActionName.signInInProgress}
  <ButtonsActionCard
    color={"neutral"}
    description={"Please complete the sign-in process in your browser."}
    buttons={[
      {
        text: "Cancel",
        action: "cancel-sign-in",
        appearance: "neutral",
      },
    ]}
    inProgress={true}
  >
    <InfoCircled slot="icon" />
  </ButtonsActionCard>
{:else if card === DerivedStateName.disableCopilot}
  <ButtonsActionCard
    color="warning"
    description={"GitHub Copilot suggestions will collide with Augment's suggestions. Turn off Copilot to fix this."}
    buttons={[
      {
        text: "Disable Github Copilot",
        action: "disable-github-copilot",
      },
    ]}
  >
    <ExclamationTriangle slot="icon" />
  </ButtonsActionCard>
{:else if card === DerivedStateName.disableCodeium}
  <ButtonsActionCard
    color="warning"
    description={"Codeium suggestions will collide with Augment's suggestions. Turn off Codeium to fix this."}
    buttons={[
      {
        text: "Disable Codeium",
        action: "disable-codeium",
      },
    ]}
  >
    <ExclamationTriangle slot="icon" />
  </ButtonsActionCard>
{:else if card === DerivedStateName.syncingPermissionNeeded}
  <ButtonsActionCard
    color="neutral"
    description={"To provide the best quality responses, Augment will index your code in our secure cloud. Your data always stays secure and private. [Learn More](https://docs.augmentcode.com/setup-augment/workspace-indexing)"}
    callout={$flagsModel.userTier === "community"
      ? "You are subscribed to the Community plan, we may use any code you share to train our AI models."
      : undefined}
    buttons={[
      {
        text: "Enable Indexing",
        action: "grant-sync-permission",
      },
    ]}
  >
    <InfoCircled slot="icon" />
  </ButtonsActionCard>
{:else if card === DerivedStateName.workspaceTooLarge}
  <BaseCard
    color="warning"
    description={`The workspace you have opened exceeds Augment's ${$flagsModel.maxTrackableFileCount} file upload limit. Please use a .augmentignore or .gitignore to exclude files from being uploaded, or open a smaller workspace.`}
  >
    <ExclamationTriangle slot="icon" />
  </BaseCard>
{:else if card === DerivedStateName.uploadingHomeDir}
  <BaseCard
    color="warning"
    description={"Current workspace contains your home directory. Augment cannot upload your home directory to the cloud. Please open a different workspace"}
  >
    <ExclamationTriangle slot="icon" />
  </BaseCard>
{:else if card === DerivedStateName.allActionsComplete}
  <!-- Do nothing -->
{/if}
