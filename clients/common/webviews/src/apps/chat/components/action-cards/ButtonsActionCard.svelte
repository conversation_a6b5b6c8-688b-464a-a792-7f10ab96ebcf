<script lang="ts">
  import BaseCard from "./BaseCard.svelte";
  import IndeterminateProgressBar from "$common-webviews/src/common/components/IndeterminateProgressBar.svelte";
  import { host } from "$common-webviews/src/common/hosts/host";
  import { WebViewMessageType } from "$vscode/src/webview-providers/webview-messages";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import type { ButtonColor } from "$common-webviews/src/design-system/_primitives/BaseButton.svelte";
  import type { CalloutColor } from "$common-webviews/src/design-system/components/CalloutAugment.svelte";

  export let description: string;
  export let color: CalloutColor;
  export let buttons: Array<{ text: string; action: string; appearance?: ButtonColor }> = [];
  export let inProgress: boolean = false;
  export let callout: string | undefined = undefined;

  function onBtnClick(action: string) {
    host.postMessage({
      type: WebViewMessageType.mainPanelPerformAction,
      data: action,
    });
  }
</script>

<BaseCard {color} {description} {callout}>
  <slot name="icon" slot="icon" />
  <div class="c-btns-card__buttons">
    {#each buttons as { text, action, appearance }}
      <ButtonAugment
        size={2}
        variant="solid"
        color={appearance ? appearance : "accent"}
        class="c-btns-card__button"
        on:click={() => onBtnClick(action)}
        title={text}
      >
        {text}
      </ButtonAugment>
    {/each}
  </div>
  {#if inProgress}
    <IndeterminateProgressBar />
  {/if}
</BaseCard>

<style>
  .c-btns-card__buttons {
    width: 100%;
    display: flex;
    flex-direction: row;
    gap: var(--ds-spacing-1);
    justify-content: center;
  }

  .c-btns-card__buttons :global(.c-btns-card__button) {
    flex: 1;
  }
</style>
