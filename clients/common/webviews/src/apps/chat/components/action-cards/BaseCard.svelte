<script lang="ts">
  import CalloutAugment, {
    type CalloutColor,
  } from "$common-webviews/src/design-system/components/CalloutAugment.svelte";
  import Markdown from "$common-webviews/src/common/components/markdown/Markdown.svelte";
  import Cross2 from "$common-webviews/src/design-system/icons/cross-2.svelte";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";

  export let color: CalloutColor;
  export let description: string;
  export let callout: string | undefined = undefined;
  export let onClose: (() => void) | undefined = undefined;
</script>

<div class="c-base-card">
  {#if callout}
    <CalloutAugment variant="soft" size={1} {color}>
      <Markdown markdown={description} />
    </CalloutAugment>
    <CalloutAugment variant="soft" size={1} color="info">
      <slot name="icon" slot="icon" />
      <Markdown markdown={callout} />
    </CalloutAugment>
  {:else}
    <CalloutAugment variant="soft" size={1} {color}>
      <slot name="icon" slot="icon" />
      <div class="c-callout-wrapper">
        <Markdown markdown={description} />
        {#if !!onClose}
          <IconButtonAugment variant="ghost" color="neutral" size={1} on:click={() => onClose()}>
            <Cross2 />
          </IconButtonAugment>
        {/if}
      </div>
    </CalloutAugment>
  {/if}
  <slot />
</div>

<style>
  .c-base-card {
    height: auto;
    display: flex;
    flex-direction: column;
    align-items: stretch;
    gap: var(--ds-spacing-1);
  }
  :global(.c-base-card a) {
    color: var(--ds-color-accent-11);
    text-decoration: none;
  }
  :global(.c-base-card a:hover) {
    text-decoration: underline;
  }
  .c-base-card {
    --callout-width: 100%;
  }

  .c-callout-wrapper {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    width: 100%;
  }
</style>
