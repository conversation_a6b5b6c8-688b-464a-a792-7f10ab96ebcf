<script lang="ts">
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import DropdownMenuAugment from "$common-webviews/src/design-system/components/DropdownMenuAugment";
  import ChevronDown from "$common-webviews/src/design-system/icons/chevron-down.svelte";
  import BranchIcon from "$common-webviews/src/design-system/icons/branch.svelte";
  import type { GitBranch } from "$vscode/src/remote-agent-manager/commit-ref-types";

  export let branches: GitBranch[] = [];
  export let selectedBranch: GitBranch | undefined;
  export let isLoading: boolean = false;
  export let isReadOnly: boolean = false;

  export function selectBranch(branch: GitBranch) {
    selectedBranch = branch;
    dispatch("select", branch);
  }

  function formatBranchName(name: string): string {
    return name.replace("origin/", "");
  }

  import { createEventDispatcher } from "svelte";
  const dispatch = createEventDispatcher();
</script>

<div class="c-branch-selector">
  {#if isReadOnly}
    <div class="c-branch-selector__display c-branch-selector__display--readonly">
      <div class="c-branch-selector__icon">
        <BranchIcon />
      </div>
      <div class="c-branch-selector__name">
        <TextAugment size={1} weight="medium">
          {selectedBranch
            ? formatBranchName(selectedBranch.name)
            : isLoading
              ? "Loading..."
              : "No branch selected"}
        </TextAugment>
      </div>
    </div>
  {:else}
    <DropdownMenuAugment.Root>
      <DropdownMenuAugment.Trigger>
        <div class="c-branch-selector__display">
          <div class="c-branch-selector__icon">
            <BranchIcon />
          </div>
          <div class="c-branch-selector__name">
            <TextAugment size={1}>
              {selectedBranch
                ? formatBranchName(selectedBranch.name)
                : isLoading
                  ? "Loading..."
                  : "Select branch"}
            </TextAugment>
          </div>
          <div class="c-branch-selector__dropdown">
            <ChevronDown />
          </div>
        </div>
      </DropdownMenuAugment.Trigger>
      <DropdownMenuAugment.Content side="bottom" align="start">
        {#if branches.length > 0}
          {#each branches as branch (branch.name)}
            <DropdownMenuAugment.Item
              onSelect={() => selectBranch(branch)}
              highlight={selectedBranch?.name === branch.name}
            >
              {formatBranchName(branch.name)}
            </DropdownMenuAugment.Item>
          {/each}
        {:else}
          <DropdownMenuAugment.Label>No branches found</DropdownMenuAugment.Label>
        {/if}
      </DropdownMenuAugment.Content>
    </DropdownMenuAugment.Root>
  {/if}
</div>

<style>
  .c-branch-selector {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
  }

  .c-branch-selector__display {
    cursor: pointer;
    display: flex;
    align-items: stretch;
    width: 100%;
    border: 1px solid var(--ds-color-neutral-1);
    border-radius: 8px;
    overflow: hidden;
  }

  .c-branch-selector__display--readonly {
    cursor: default;
  }

  .c-branch-selector__dropdown {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--ds-spacing-1) var(--ds-spacing-2);
  }

  .c-branch-selector__icon {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--ds-color-neutral-1);
    padding: 6px 6px 6px var(--ds-spacing-2);
    color: var(--ds-color-neutral-8);
    border-radius: 6px 0 0 6px;
  }

  .c-branch-selector__name {
    padding: var(--ds-spacing-1) var(--ds-spacing-2);
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 1.6em;
  }
</style>
