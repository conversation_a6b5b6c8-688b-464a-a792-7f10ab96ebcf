import WebSearchToolComponent from "./WebSearchToolComponent.svelte";
import GitHubToolComponent from "./GitHubToolComponent.svelte";
import LinearToolComponent from "./LinearToolComponent.svelte";
import NotionToolComponent from "./NotionToolComponent.svelte";
import JiraToolComponent from "./JiraToolComponent.svelte";
import ConfluenceToolComponent from "./ConfluenceToolComponent.svelte";
import SupabaseToolComponent from "./SupabaseToolComponent.svelte";
import GleanToolComponent from "./GleanToolComponent.svelte";
import { RemoteToolType } from "$vscode/src/webview-providers/tool-types";

// Map of tool types to their components
export const remoteToolComponents = {
  [RemoteToolType.webSearch]: WebSearchToolComponent,
  [RemoteToolType.webSearchV1]: WebSearchToolComponent,
  [RemoteToolType.gitHub]: GitHubToolComponent,
  [RemoteToolType.linear]: LinearToolComponent,
  [RemoteToolType.notion]: NotionToolComponent,
  [RemoteToolType.jira]: JiraToolComponent,
  [RemoteToolType.confluence]: ConfluenceToolComponent,
  [RemoteToolType.supabase]: SupabaseToolComponent,
  [RemoteToolType.glean]: GleanToolComponent,
  // Add more remote tool components as needed
} as const;

/**
 * Returns the appropriate tool component for a given tool type.
 * @param toolName RemoteToolType
 * @returns
 */
export function resolveRemoteTool(toolName: RemoteToolType) {
  return toolName in remoteToolComponents
    ? remoteToolComponents[toolName as keyof typeof remoteToolComponents]
    : undefined;
}
export {
  WebSearchToolComponent,
  GitHubToolComponent,
  LinearToolComponent,
  NotionToolComponent,
  JiraToolComponent,
  ConfluenceToolComponent,
  SupabaseToolComponent,
  GleanToolComponent,
};
