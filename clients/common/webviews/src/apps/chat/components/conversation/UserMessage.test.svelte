<script lang="ts">
  import { setContext } from "svelte";
  import UserMessage from "./UserMessage.svelte";
  import type { ChatModel } from "../../models/chat-model";
  import { writable } from "svelte/store";
  import {
    CurrentConversationTaskStore,
    type ICurrentConversationTaskStore,
  } from "../../models/task-store";

  export let chatModel: ChatModel;
  export let msg: string;
  export let requestId: string | undefined = undefined;
  export let richTextJsonRepr: any = undefined;
  export let onStartEdit: () => void = () => {};
  export let onAcceptEdit: () => void = () => {};
  export let onCancelEdit: () => void = () => {};

  const mockAgentConversationModel = {
    isCurrConversationAgentic: writable<boolean>(false),
    distillMemory: () => {},
  };

  // Create a mock task store to prevent context errors
  const mockTaskStore: ICurrentConversationTaskStore = {
    rootTaskUuid: writable(undefined),
    rootTask: writable(undefined),
    activeTask: writable(undefined),
    taskProgress: writable({ completed: 0, total: 0 }),
    isImportingExporting: writable(false),
    canShowTaskList: writable(false),
    uuidToTask: writable(new Map()),
    onTaskAdded: () => () => {},
    createTask: async () => "mock-uuid",
    updateTask: async () => {},
    getHydratedTask: async () => undefined,
    cloneHydratedTask: async () => undefined,
    refreshTasks: async () => {},
    refreshTasksThrottled: () => {},
    updateTaskListStatuses: async () => {},
    syncTaskListWithConversation: async () => {},
    deleteTask: async () => {},
    getParentTask: () => undefined,
    addNewTaskAfter: async () => undefined,
    saveHydratedTask: async () => {},
    runHydratedTask: async () => {},
    dispose: () => {},
    handleMessageFromExtension: () => false,
    runAllTasks: async () => {},
    exportTask: async () => {},
    exportTasksToMarkdown: async () => {},
    importTasksFromMarkdown: async () => {},
    runTaskInSubAgent: async () => {},
  };

  // Get the test kit from the parent component
  export let testKit: any = undefined;

  setContext("chatModel", chatModel);
  setContext("agentConversationModel", mockAgentConversationModel);
  setContext(CurrentConversationTaskStore.key, mockTaskStore);

  // Add missing contexts if testKit is provided
  if (testKit) {
    setContext("chatModeModel", testKit.chatModeModel);
    setContext("remoteAgentsModel", testKit.remoteAgentsModel);
    setContext("gitReferenceModel", testKit.gitReferenceModel);
  }
</script>

<UserMessage
  {chatModel}
  {msg}
  {requestId}
  {richTextJsonRepr}
  {onStartEdit}
  {onAcceptEdit}
  {onCancelEdit}
/>
