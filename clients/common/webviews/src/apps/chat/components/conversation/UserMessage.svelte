<script lang="ts">
  import type { JSONContent } from "@tiptap/core";
  import ChatMessage from "../ChatMessage.svelte";
  import { type ChatModel } from "../../models/chat-model";
  import { onKey } from "$common-webviews/src/common/utils/keypress";
  import { ExchangeStatus } from "../../types/chat-message";
  import ChatEditMessageInput from "../ChatInput/ChatEditMessageInput.svelte";
  import { type ContentData } from "$common-webviews/src/design-system/components/RichTextEditorAugment/types";
  import { type IChatMentionable } from "../../types/mention-option";
  import MessageActions from "./MessageActions.svelte";
  import Edit from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/pencil.svg?component";
  import { tick } from "svelte";
  import ResizeObserver from "$common-webviews/src/design-system/components/RichTextEditorAugment/plugins/ResizeObserver/ResizeObserver.svelte";
  import { getContext } from "svelte";
  import { RemoteAgentsModel } from "$common-webviews/src/apps/remote-agent-manager/models/remote-agents-model";
  import CopyIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/copy.svg?component";


  export let chatModel: ChatModel;
  export let msg: string;
  export let requestId: string | undefined = undefined;
  export let richTextJsonRepr: JSONContent | JSONContent[] | undefined = undefined;
  export let timestamp: string | undefined = undefined;

  // Lifecycle callbacks
  export let onStartEdit: () => void = () => {};
  export let onAcceptEdit: () => void = () => {};
  export let onCancelEdit: () => void = () => {};

  const remoteAgentModel = getContext<RemoteAgentsModel>(RemoteAgentsModel.key);
  $: isRemoteAgent = !!remoteAgentModel?.isActive;

  const COLLAPSED_MAX_HEIGHT = 120;
  $: isLastMessage =
    requestId === undefined ||
    requestId === $chatModel?.currentConversationModel?.lastExchange?.request_id;

  // Only collapse if not the last message and message is long enough
  let isCollapsed = false;
  let userExpanded = false;

  async function setIsCollapsed(c: boolean) {
    await tick();
    isCollapsed = c && canCollapse && !userExpanded;
  }
  $: canCollapse = !isLastMessage && !isEditing;
  $: editorHeight = 0;

  $: canEdit =
    requestId !== undefined &&
    $chatModel.flags.fullFeatured &&
    $chatModel.flags.enableEditableHistory &&
    !isRemoteAgent;
  let isEditing = false;

  const startEdit = (e: Event) => {
    if (!canEdit || !requestId) {
      return;
    }

    // Always show full content when entering edit mode
    setIsCollapsed(false);
    dsEditor?.requestStartEdit();
    onStartEdit();
    e?.stopPropagation();
  };

  const copyRequestId = async (e: Event) => {
    if (!requestId) {
      return;
    }
    await navigator.clipboard.writeText(requestId);
    e.stopPropagation();
  };


  const cancelEdit = () => {
    onCancelEdit();
  };

  function onSubmitEdit(data: ContentData, mentions: IChatMentionable[]) {
    if (!requestId) {
      return;
    }

    chatModel.currentConversationModel.clearHistoryFrom(requestId);

    const structuredRequestNodes =
      $chatModel.flags.enableChatMultimodal && data.richTextJsonRepr
        ? chatModel.currentConversationModel.createStructuredRequestNodes(data.richTextJsonRepr)
        : undefined;

    // Send a new exchange to the real conversation model
    chatModel.currentConversationModel.sendExchange({
      /* eslint-disable @typescript-eslint/naming-convention */
      request_message: data.rawText,
      rich_text_json_repr: data.richTextJsonRepr,
      status: ExchangeStatus.draft,
      mentioned_items: mentions,
      structured_request_nodes: structuredRequestNodes,
      /* eslint-enable @typescript-eslint/naming-convention */
    });
    onAcceptEdit();
  }

  $: tooltipItems = [
    ...(canEdit && !isEditing
      ? [
          ...($chatModel.flags.enableDebugFeatures
            ? [
                {
                  label: "Copy request ID",
                  action: copyRequestId,
                  id: "copy-request-id",
                  disabled: false,
                  icon: CopyIcon,
                  successMessage: "Copied!",
                },
              ]
            : []),
          {
            label: "Edit message",
            action: startEdit,
            id: "edit-message",
            disabled: false,
            icon: Edit,
          },
        ]
      : []),
  ];

  let dsEditor: ChatEditMessageInput | undefined = undefined;
  let showExpandButton = false;

  function handleEditorHeightChange() {
    if (!dsEditor?.getEditorContainer() || !editorHeight || !canCollapse) return;
    if (isCollapsed && editorHeight < COLLAPSED_MAX_HEIGHT) {
      setIsCollapsed(false);
    } else {
      setIsCollapsed(editorHeight > COLLAPSED_MAX_HEIGHT);
    }
    showExpandButton = editorHeight > COLLAPSED_MAX_HEIGHT;
  }

  $: editorHeight && canCollapse && handleEditorHeightChange();
</script>

<ChatMessage bind:isEditing {timestamp}>
  <svelte:fragment slot="content">
    <ChatEditMessageInput
      bind:this={dsEditor}
      bind:isEditing
      collapsed={isCollapsed}
      {showExpandButton}
      content={richTextJsonRepr ?? msg}
      collapsedMaxHeight={COLLAPSED_MAX_HEIGHT}
      {requestId}
      {onSubmitEdit}
      onCancelEdit={cancelEdit}
      {setIsCollapsed}
      bind:userExpanded
    >
      <ResizeObserver bind:height={editorHeight} />
    </ChatEditMessageInput>
  </svelte:fragment>

  <div slot="user-actions" class="c-user-msg__actions">
    {#if tooltipItems.length > 0}
      <MessageActions items={tooltipItems} />
    {/if}
  </div>
</ChatMessage>

<style>
  .c-user-msg__actions {
    opacity: 0;
    transition: opacity 0.2s ease-in-out;
    display: flex;
    justify-content: flex-end;
    width: 100%;
  }

  /* Also show actions when they're being interacted with */
  :global(.c-chat-message):hover .c-user-msg__actions {
    opacity: 1;
  }

  .c-user-msg__actions:hover,
  .c-user-msg__actions:focus-within {
    opacity: 1;
  }
</style>
