<script lang="ts">
  import { onD<PERSON>roy } from "svelte";
  import { getChatModel } from "$common-webviews/src/apps/chat/chat-context";
  import {
    type ButtonVariant,
    type ButtonColor,
    type ButtonSize,
  } from "$common-webviews/src/design-system/_primitives/BaseButton.svelte";
  import OpenInNewWindow from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/arrow-up-right-from-square.svg?component";
  import SuccessfulButton from "../../../../buttons/SuccessfulButton.svelte";
  import type { ButtonState } from "../../../../buttons/types";
  import { ToolUsePhase } from "$common-webviews/src/apps/chat/types/tool-use-state";

  export let terminalId: number | undefined = undefined;
  export let toolUseInput: Record<string, unknown> = {};
  export let toolResult: string | undefined = undefined;
  export let phase: ToolUsePhase;
  export let size: 0 | ButtonSize = 0;
  export let color: ButtonColor = "neutral";
  export let variant: ButtonVariant = "ghost-block";
  export let stickyColor: boolean = false;

  const chat = getChatModel();

  // Check if the terminal can actually be shown
  let canShowTerminal = false;

  // Whether we have done the delay check
  let hasDoneDelayCheck = false;

  // Store timeout ID for cleanup
  let delayCheckTimeoutId: ReturnType<typeof setTimeout> | undefined;

  // Extract command from tool input if available
  $: command = toolUseInput.command as string | undefined;

  // Check if the tool is currently running
  $: isRunning = phase === ToolUsePhase.running;

  // Make it reactive to terminalId, command, phase, and toolResult changes
  // This is needed since we don't initially know the terminal ID for launch-process
  $: if (terminalId !== undefined || command !== undefined || isRunning) {
    // Include toolResult in the reactive dependency so it re-runs when tool output changes
    void toolResult;
    checkTerminalAvailability();

    // Check again after a delay in case the process just started and we don't yet have a pid.
    if (terminalId === undefined && isRunning) {
      if (delayCheckTimeoutId !== undefined) {
        clearTimeout(delayCheckTimeoutId);
      }
      delayCheckTimeoutId = setTimeout(() => {
        if (!canShowTerminal && !hasDoneDelayCheck) {
          hasDoneDelayCheck = true;
          checkTerminalAvailability();
        }
        delayCheckTimeoutId = undefined;
      }, 1000);
    }
  }

  // Clean up timeout on component unmount
  onDestroy(() => {
    clearTimeout(delayCheckTimeoutId);
    delayCheckTimeoutId = undefined;
  });

  async function checkTerminalAvailability() {
    const canShow = await chat.extensionClient.canShowTerminal(terminalId, command);
    canShowTerminal = canShow;
  }

  async function onOpenTerminal(e: MouseEvent): Promise<ButtonState | undefined> {
    e?.stopPropagation?.();
    e?.preventDefault?.();

    const success = await chat.extensionClient.showTerminal(terminalId, command);
    if (!success) {
      canShowTerminal = false;
      return "failure";
    }
    return "success";
  }
</script>

{#if canShowTerminal}
  <span class="c-open-terminal-button-container c-open-terminal-button__size--{size}">
    <SuccessfulButton
      defaultColor={color}
      {stickyColor}
      {size}
      {variant}
      tooltip={{
        neutral: `Open Terminal`,
        success: "Opening terminal...",
      }}
      stateVariant={{ success: "soft" }}
      onClick={onOpenTerminal}
      icon={!$$slots.text}
    >
      <OpenInNewWindow slot="iconLeft" />
      <slot name="text" />
    </SuccessfulButton>
  </span>
{/if}

<style>
  .c-open-terminal-button-container {
    display: contents;
  }

  .c-open-terminal-button__size--0 :global(.c-open-terminal-button) {
    --base-btn-width: 16px;
    --base-btn-height: 16px;
    height: 16px;
    width: 16px;
  }
</style>
