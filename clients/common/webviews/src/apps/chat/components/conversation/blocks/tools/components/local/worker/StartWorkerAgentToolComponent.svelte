<script lang="ts">
  import BaseToolComponent from "../../BaseToolComponent.svelte";
  import ToolUseHeader from "../../ToolUseHeader.svelte";
  import UserGroupIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/user-group.svg?component";
  import {
    ToolUsePhase,
    type ToolUseState,
  } from "$common-webviews/src/apps/chat/types/tool-use-state";
  import ShellError from "../../sidecar/ShellError.svelte";
  import StatusBadgeAugment from "$common-webviews/src/design-system/components/StatusBadgeAugment.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import CardAugment from "$common-webviews/src/design-system/components/CardAugment.svelte";
  import ToolResultFields from "./ToolResultFields.svelte";

  export let toolUseInput: Record<string, unknown> = {};
  export let toolUseState: ToolUseState;
  let collapsed = true;

  // Extract workers array from input
  $: workers = Array.isArray(toolUseInput.workers) ? toolUseInput.workers : [];

  // Format tool arguments for display - show count of workers in collapsed state
  $: formattedArgs = (() => {
    const args: string[] = [];
    if (workers.length > 0) {
      args.push(`${workers.length} worker${workers.length === 1 ? "" : "s"}`);
    }
    return args;
  })();

  // Parse the tool result to get worker results

  $: workerResults = (() => {
    if (
      toolUseState.phase === ToolUsePhase.completed &&
      toolUseState.result?.text &&
      !toolUseState.result.isError
    ) {
      try {
        const response = JSON.parse(toolUseState.result.text);
        // Combine successful and failed workers into a single array
        const successful = Array.isArray(response.startedWorkers) ? response.startedWorkers : [];
        const failed = Array.isArray(response.failedWorkers) ? response.failedWorkers : [];
        return [...successful, ...failed];
      } catch (e) {
        console.error("Failed to parse start worker agent tool response:", e);
      }
    }
    return [];
  })();

  // Count successful and failed workers
  $: successCount = workerResults.filter((result: any) => result.success).length;
  $: failureCount = workerResults.filter((result: any) => !result.success).length;
  $: hasResults = workerResults.length > 0;
</script>

<BaseToolComponent bind:collapsed>
  <ToolUseHeader slot="header" toolName="Start Worker Agents" formattedToolArgs={formattedArgs}>
    <UserGroupIcon slot="icon" />
    <svelte:fragment slot="secondary">
      {#if hasResults && collapsed}
        <div class="c-start-worker-tool__counts">
          {#if successCount > 0}
            <StatusBadgeAugment color="success" size={1}>
              {successCount} started
            </StatusBadgeAugment>
          {/if}
          {#if failureCount > 0}
            <StatusBadgeAugment color="error" size={1}>
              {failureCount} failed
            </StatusBadgeAugment>
          {/if}
        </div>
      {/if}
    </svelte:fragment>
  </ToolUseHeader>

  <div slot="details" class="c-start-worker-tool__details">
    {#if toolUseState.phase === ToolUsePhase.running}
      <div class="c-start-worker-tool__running">
        <TextAugment size={1} color="neutral">Starting worker agents...</TextAugment>
      </div>
    {:else if toolUseState.phase === ToolUsePhase.completed && hasResults}
      <div class="c-start-worker-tool__results">
        {#each workerResults as result}
          {@const worker = workers[result.index] || {}}
          <CardAugment class="c-start-worker-tool__worker-card">
            <div class="c-start-worker-tool__worker-header">
              <div class="c-start-worker-tool__worker-title">
                <TextAugment size={1} weight="medium">
                  Worker {result.index + 1}
                  {#if worker.summary}
                    - {worker.summary}
                  {/if}
                </TextAugment>
                <StatusBadgeAugment color={result.success ? "success" : "error"} size={1}>
                  {result.success ? "Started" : "Failed"}
                </StatusBadgeAugment>
              </div>
            </div>

            <div class="c-start-worker-tool__worker-details">
              {#if result.success}
                <div class="c-start-worker-tool__success-details">
                  <TextAugment size={1} color="neutral">
                    <strong>Agent ID:</strong>
                    {result.workerAgentId}
                  </TextAugment>
                  {#if result.status}
                    <TextAugment size={1} color="neutral">
                      <strong>Status:</strong>
                      {result.status}
                    </TextAugment>
                  {/if}
                </div>
              {:else}
                <div class="c-start-worker-tool__error-details">
                  <TextAugment size={1} color="error">
                    <strong>Error:</strong>
                    {result.error || "Unknown error"}
                  </TextAugment>
                </div>
              {/if}

              <ToolResultFields
                data={worker}
                fields={[
                  {
                    key: "initial_prompt",
                    label: "Initial Prompt",
                    type: "monospace",
                    collapsible: true,
                  },
                ]}
              />
            </div>
          </CardAugment>
        {/each}
      </div>
    {:else if toolUseState.phase === ToolUsePhase.completed && !hasResults}
      <div class="c-start-worker-tool__no-results">
        <TextAugment size={1} color="neutral">No worker agents were started.</TextAugment>
      </div>
    {/if}
  </div>

  {#if toolUseState.phase === ToolUsePhase.error}
    <ShellError slot="error" {...toolUseState.result ?? {}} />
  {/if}
</BaseToolComponent>

<style>
  .c-start-worker-tool__counts {
    display: flex;
    gap: var(--ds-spacing-2);
    align-items: center;
    flex-wrap: wrap;
  }

  .c-start-worker-tool__details {
    padding: var(--ds-spacing-2);
  }

  .c-start-worker-tool__results {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-2);
  }

  .c-start-worker-tool__worker-header {
    margin-bottom: var(--ds-spacing-2);
  }

  .c-start-worker-tool__worker-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: var(--ds-spacing-2);
  }

  .c-start-worker-tool__worker-details {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-2);
  }
</style>
