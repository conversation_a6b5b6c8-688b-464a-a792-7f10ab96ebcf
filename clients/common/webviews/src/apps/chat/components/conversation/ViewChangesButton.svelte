<script lang="ts">
  import { RemoteAgentsModel } from "$common-webviews/src/apps/remote-agent-manager/models/remote-agents-model";
  import { getAllChangesBetweenUserMessages } from "$common-webviews/src/apps/remote-agent-manager/utils";
  import { getContext } from "svelte";
  import FileChangesSummary from "../FileChangesSummary.svelte";

  export let turnIndex: number = 0;
  export let isLastTurn = false;

  const remoteAgentsModel = getContext<RemoteAgentsModel>(RemoteAgentsModel.key);

  $: turnChanges = getAllChangesBetweenUserMessages(
    $remoteAgentsModel.currentConversation?.exchanges ?? [],
    turnIndex,
  );
  $: hasChanges = turnChanges.length > 0;
</script>

{#if hasChanges}
  <div class="c-message-actions" class:c-message-actions--last={isLastTurn}>
    <FileChangesSummary changedFiles={turnChanges} suffix=" in this turn" {turnIndex} />
  </div>
{/if}

<style>
  .c-message-actions {
    width: 100%;
    margin-top: var(--ds-spacing-2);
    padding: var(--ds-spacing-3) var(--ds-spacing-2);
    text-align: center;
  }
  .c-message-actions--last {
    border-bottom: none;
  }
</style>
