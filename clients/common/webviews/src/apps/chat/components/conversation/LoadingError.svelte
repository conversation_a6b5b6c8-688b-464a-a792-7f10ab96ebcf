<script lang="ts">
  import CalloutAugment from "$common-webviews/src/design-system/components/CalloutAugment.svelte";
  import ExclamationTriangle from "$common-webviews/src/design-system/icons/exclamation-triangle.svelte";

  export let errorMessage: string;
</script>

<div class="l-error-message">
  <CalloutAugment variant="soft" color="error">
    <ExclamationTriangle slot="icon" />
    Error loading message list: {errorMessage}
    <br />
    Please consider restarting your extension.
  </CalloutAugment>
</div>

<style>
  .l-error-message {
    padding: 1rem;
  }
</style>
