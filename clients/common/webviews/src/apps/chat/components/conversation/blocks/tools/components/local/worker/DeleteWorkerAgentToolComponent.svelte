<script lang="ts">
  import BaseToolComponent from "../../BaseToolComponent.svelte";
  import ToolUseHeader from "../../ToolUseHeader.svelte";
  import TrashIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/trash.svg?component";
  import {
    ToolUsePhase,
    type ToolUseState,
  } from "$common-webviews/src/apps/chat/types/tool-use-state";
  import ShellError from "../../sidecar/ShellError.svelte";
  import StatusBadgeAugment from "$common-webviews/src/design-system/components/StatusBadgeAugment.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import CardAugment from "$common-webviews/src/design-system/components/CardAugment.svelte";

  export let toolUseInput: Record<string, unknown> = {};
  export let toolUseState: ToolUseState;
  let collapsed = true;

  // Extract worker agent IDs array from input
  $: workerAgentIds = Array.isArray(toolUseInput.worker_agent_ids)
    ? toolUseInput.worker_agent_ids
    : [];

  // Format tool arguments for display - show count of agents in collapsed state
  $: formattedArgs = (() => {
    const args: string[] = [];
    if (workerAgentIds.length > 0) {
      args.push(`${workerAgentIds.length} agent${workerAgentIds.length === 1 ? "" : "s"}`);
    } else {
      args.push("all agents");
    }
    return args;
  })();

  // Parse the tool result to get delete results
  $: deleteResults = (() => {
    if (
      toolUseState.phase === ToolUsePhase.completed &&
      toolUseState.result?.text &&
      !toolUseState.result.isError
    ) {
      try {
        const response = JSON.parse(toolUseState.result.text);
        // Combine successful and failed deletes into a single array
        const successful = Array.isArray(response.deleteResults) ? response.deleteResults : [];
        const failed = Array.isArray(response.failedDeletes) ? response.failedDeletes : [];
        return [...successful, ...failed];
      } catch (e) {
        console.error("Failed to parse delete worker agent tool response:", e);
      }
    }
    return [];
  })();

  // Count successful and failed deletions
  $: successCount = deleteResults.filter((result: any) => result.success).length;
  $: failureCount = deleteResults.filter((result: any) => !result.success).length;
  $: hasResults = deleteResults.length > 0;
</script>

<BaseToolComponent bind:collapsed>
  <ToolUseHeader slot="header" toolName="Delete Worker Agents" formattedToolArgs={formattedArgs}>
    <TrashIcon slot="icon" />
    <svelte:fragment slot="secondary">
      {#if hasResults && collapsed}
        <div class="c-delete-worker-tool__counts">
          {#if successCount > 0}
            <StatusBadgeAugment color="success" size={1}>
              {successCount} deleted
            </StatusBadgeAugment>
          {/if}
          {#if failureCount > 0}
            <StatusBadgeAugment color="error" size={1}>
              {failureCount} failed
            </StatusBadgeAugment>
          {/if}
        </div>
      {/if}
    </svelte:fragment>
  </ToolUseHeader>

  <div slot="details" class="c-delete-worker-tool__details">
    {#if toolUseState.phase === ToolUsePhase.running}
      <div class="c-delete-worker-tool__running">
        <TextAugment size={1} color="neutral">Deleting worker agents...</TextAugment>
      </div>
    {:else if toolUseState.phase === ToolUsePhase.completed && hasResults}
      <div class="c-delete-worker-tool__results">
        {#each deleteResults as result, index}
          <CardAugment class="c-delete-worker-tool__agent-card">
            <div class="c-delete-worker-tool__agent-header">
              <div class="c-delete-worker-tool__agent-title">
                <TextAugment size={1} weight="medium">
                  Agent {result.agentId || `${index + 1}`}
                </TextAugment>
                <StatusBadgeAugment color={result.success ? "success" : "error"} size={1}>
                  {result.success ? "Deleted" : "Failed"}
                </StatusBadgeAugment>
              </div>
            </div>

            <div class="c-delete-worker-tool__agent-details">
              {#if result.success}
                <div class="c-delete-worker-tool__success-details">
                  <TextAugment size={1} color="neutral">
                    <strong>Agent ID:</strong>
                    {result.agentId}
                  </TextAugment>
                  <TextAugment size={1} color="success">
                    Worker agent and its resources have been successfully deleted.
                  </TextAugment>
                </div>
              {:else}
                <div class="c-delete-worker-tool__error-details">
                  <TextAugment size={1} color="error">
                    <strong>Error:</strong>
                    {result.error || "Unknown error"}
                  </TextAugment>
                  {#if result.agentId}
                    <TextAugment size={1} color="neutral">
                      <strong>Agent ID:</strong>
                      {result.agentId}
                    </TextAugment>
                  {/if}
                </div>
              {/if}
            </div>
          </CardAugment>
        {/each}
      </div>
    {:else if toolUseState.phase === ToolUsePhase.completed && !hasResults}
      <div class="c-delete-worker-tool__no-results">
        <TextAugment size={1} color="neutral">No worker agents were deleted.</TextAugment>
      </div>
    {/if}
  </div>

  {#if toolUseState.phase === ToolUsePhase.error}
    <ShellError slot="error" {...toolUseState.result ?? {}} />
  {/if}
</BaseToolComponent>

<style>
  .c-delete-worker-tool__counts {
    display: flex;
    gap: var(--ds-spacing-2);
    align-items: center;
    flex-wrap: wrap;
  }

  .c-delete-worker-tool__details {
    padding: var(--ds-spacing-2);
  }

  .c-delete-worker-tool__results {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-2);
  }

  .c-delete-worker-tool__agent-header {
    margin-bottom: var(--ds-spacing-2);
  }

  .c-delete-worker-tool__agent-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: var(--ds-spacing-2);
  }

  .c-delete-worker-tool__agent-details {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-2);
  }
</style>
