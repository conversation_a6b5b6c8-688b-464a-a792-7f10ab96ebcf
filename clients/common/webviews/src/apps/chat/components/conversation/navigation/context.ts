import { getContext, setContext } from "svelte";
import { writable, type Writable } from "svelte/store";

export interface ConversationNavigationItem {
  id: string;
  offsetTop: number;
}

export interface ConversationNavigationItems {
  all: string[];
  next?: ConversationNavigationItem; // Pre-calculated next item with offset position
  prev?: ConversationNavigationItem; // Pre-calculated previous item with offset position
  scrollTo?: (offsetTop: number) => void; // Function to scroll to a specific position
  scrollToBottom?: () => void; // Function to scroll to the bottom of the container
  canScrollToBottom?: boolean; // Whether we can scroll to bottom (not already at bottom)
}

export type ConversationNavigationItemsStore = Writable<ConversationNavigationItems>;

const CONVERSATION_NAVIGATION_ITEMS_KEY = Symbol("conversationNavigationItems");

/**
 * Creates and sets the conversation navigation items context
 * @param initialValue - Initial value for the context
 * @returns The writable store
 */
export function setConversationNavigationItemsContext(
  initialValue: ConversationNavigationItems = { all: [] },
): ConversationNavigationItemsStore {
  const store = writable<ConversationNavigationItems>(initialValue);
  setContext(CONVERSATION_NAVIGATION_ITEMS_KEY, store);
  return store;
}

/**
 * Gets the conversation navigation items context
 * @returns The writable store from context
 */
export function getConversationNavigationItemsContext(): ConversationNavigationItemsStore {
  return getContext<ConversationNavigationItemsStore>(CONVERSATION_NAVIGATION_ITEMS_KEY);
}
