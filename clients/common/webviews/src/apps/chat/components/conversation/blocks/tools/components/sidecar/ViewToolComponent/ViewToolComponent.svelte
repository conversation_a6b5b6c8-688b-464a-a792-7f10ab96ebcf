<script lang="ts" context="module">
  function viewRange(toolUseInput: Record<string, unknown>): { start: number; stop: number } {
    if (toolUseInput.view_range && Array.isArray(toolUseInput.view_range)) {
      return { start: toolUseInput.view_range[0], stop: toolUseInput.view_range[1] } as any;
    }
    return { start: 0, stop: 0 };
  }

  function getSearchQueryRegex(toolUseInput: Record<string, unknown>): string | undefined {
    return typeof toolUseInput.search_query_regex === "string"
      ? toolUseInput.search_query_regex
      : undefined;
  }

  function getCaseSensitive(toolUseInput: Record<string, unknown>): boolean {
    return typeof toolUseInput.case_sensitive === "boolean" ? toolUseInput.case_sensitive : false;
  }
</script>

<script lang="ts">
  import BaseToolComponent from "../../BaseToolComponent.svelte";
  import ToolUseHeader from "../../ToolUseHeader.svelte";
  import FileTextIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/file.svg?component";
  import FolderIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/folder.svg?component";
  import SearchIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/magnifying-glass.svg?component";
  import {
    stringOrDefault,
    ToolUsePhase,
    type ToolUseState,
  } from "$common-webviews/src/apps/chat/types/tool-use-state";
  import ViewSecondary from "./ViewSecondary.svelte";
  import ViewToolUseDetails from "./ViewToolUseDetails.svelte";
  import ShellError from "../ShellError.svelte";
  import {
    extractRuleFileContentFromFormattedOutput,
    isRulesFile,
  } from "@augment-internal/sidecar-libs/src/tools/sidecar-tools/file-utils";
  import Codeblock from "$common-webviews/src/common/components/markdown/Codeblock.svelte";
  import ShowMore from "$common-webviews/src/common/components/ShowMore.svelte";
  import FilespanOpen from "../../../FilespanOpen.svelte";
  import Filespan from "$common-webviews/src/common/components/Filespan.svelte";

  export let toolUseInput: Record<string, unknown> = {};
  export let toolUseState: ToolUseState;
  let collapsed = true;

  $: filepath = stringOrDefault(toolUseInput.path || toolUseInput.file_path, "");

  $: isDirectory = toolUseInput.type === "directory";

  $: hasSearchQuery = getSearchQueryRegex(toolUseInput) !== undefined;

  $: isRulesFilePreview =
    !isDirectory &&
    !hasSearchQuery &&
    isRulesFile(filepath) &&
    toolUseState.phase === ToolUsePhase.completed;

  $: extractedContent =
    isRulesFilePreview && toolUseState.result?.text
      ? extractRuleFileContentFromFormattedOutput(toolUseState.result.text)
      : "";
</script>

<BaseToolComponent bind:collapsed showToolOutput={hasSearchQuery || isRulesFilePreview}>
  <ToolUseHeader slot="header">
    <svelte:fragment slot="icon">
      {#if isDirectory}
        <FolderIcon />
      {:else if hasSearchQuery}
        <SearchIcon />
      {:else}
        <FileTextIcon />
      {/if}
    </svelte:fragment>
    <ViewSecondary
      slot="toolName"
      {...viewRange(toolUseInput)}
      {isDirectory}
      searchQueryRegex={getSearchQueryRegex(toolUseInput)}
      caseSensitive={getCaseSensitive(toolUseInput)}
      isRulesFile={isRulesFilePreview}
    />
    <svelte:fragment slot="secondary">
      {#if isDirectory}
        <!-- directories can not be opened-->
        <Filespan {filepath} {...viewRange(toolUseInput)} />
      {:else}
        <FilespanOpen {filepath} {...viewRange(toolUseInput)} />
      {/if}
    </svelte:fragment>
  </ToolUseHeader>
  <svelte:fragment slot="details">
    {#if isRulesFilePreview && toolUseState.result?.text}
      <ShowMore maxHeight={90}>
        <Codeblock
          token={{
            text: extractedContent,
            lang: "text",
            type: "code",
            raw: extractedContent,
          }}
        />
      </ShowMore>
    {:else}
      <ViewToolUseDetails {toolUseInput} />
    {/if}
  </svelte:fragment>

  {#if toolUseState.phase === ToolUsePhase.error}
    <ShellError slot="error" {...toolUseState.result ?? {}} />
  {/if}
</BaseToolComponent>
