# Tool Components

This directory contains components for rendering different types of tools in the chat interface.

## Structure

- `BaseToolComponent.svelte`: Base component that all tool-specific components extend
- `sidecar/`: Components for sidecar tools (shell, web-fetch, etc.)
- `local/`: Components for local tools (edit-file, codebase-retrieval, etc.)
- `remote/`: Components for remote tools (web-search, github, etc.)
- `index.ts`: Exports all tool components and provides a function to get the appropriate component for a tool type

## Tool selection.
All tool index's should have `resolve{ToolType}Tool` function that returns the appropriate component for a tool type.  Logic
for choosing tool (based on type) should be in this function.  Otherwise a BaseToolComponent is returned.


## Usage

To use a tool component, import it from the components directory:

```typescript
import { ShellToolComponent } from "./components";
```

Or use the `resolveTool` function to get the appropriate component for a tool type:

```typescript
import { resolveTool } from "./components";

const ToolComponent = resolveTool(toolUse.tool_name);
```

Then use it in your Svelte component:

```svelte
<svelte:component
  this={ToolComponent}
  {toolUse}
  {toolUseState}
  {isLastTurn}
  {requestId}
  {onToolRun}
  {onToolCancel}
  {onToolSkip}
/>
```

## Adding a New Tool Component

To add a new tool component:

1. Create a new component in the appropriate directory (sidecar, local, or remote)
2. Extend the `BaseToolComponent` by using it in your component
3. Add the component to the appropriate index file
4. Update the mapping in the index file to include the new tool type

Example:

```svelte
<!-- NewToolComponent.svelte -->
<script lang="ts">
  import BaseToolComponent from "../BaseToolComponent.svelte";
  import type { ChatResultToolUse } from "@augment-internal/sidecar-libs/src/chat/chat-types";
  import type { ToolUseState } from "../../../../../../types/tool-use-state";

  export let toolUse: ChatResultToolUse;
  export let toolUseState: ToolUseState;
  export let isLastTurn: boolean;
  export let requestId: string;
  export let onToolRun: (
    requestId: string,
    toolUse: ChatResultToolUse,
    toolUseInput: any,
  ) => void = () => {};
  export let onToolCancel: (requestId: string, toolUseId: string) => void = () => {};
  export let onToolSkip: (requestId: string, toolUseId: string) => void = () => {};
</script>

<BaseToolComponent
  {toolUse}
  {toolUseState}
  {isLastTurn}
  {requestId}
  {onToolRun}
  {onToolCancel}
  {onToolSkip}
>
  <!-- Custom content for this tool type -->
  <div slot="details">
    <!-- Custom details for this tool type -->
  </div>
</BaseToolComponent>
```

Then update the index file:

```typescript
// local/index.ts
import NewToolComponent from './NewToolComponent.svelte';
import { LocalToolType } from '$vscode/src/webview-providers/tool-types';

export const localToolComponents = {
  // ...existing components
  [LocalToolType.newTool]: NewToolComponent,
};

export {
  // ...existing exports
  NewToolComponent,
};
```

## Storybook

Each tool component has a corresponding story in the `clients/common/webviews/mocks/components/tools/components` directory. To view the stories, run:

```bash
pnpm run dev
```

And navigate to the "components/Tools" section in the Storybook UI.
