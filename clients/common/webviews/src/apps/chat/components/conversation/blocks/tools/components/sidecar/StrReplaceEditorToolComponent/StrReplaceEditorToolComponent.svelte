<script lang="ts" context="module">
  function viewRange(toolUseInput: Record<string, unknown>): { start: number; stop: number } {
    if (toolUseInput.view_range && Array.isArray(toolUseInput.view_range)) {
      return { start: toolUseInput.view_range[0], stop: toolUseInput.view_range[1] } as any;
    }
    return { start: 0, stop: 0 };
  }
</script>

<script lang="ts">
  import BaseToolComponent from "../../BaseToolComponent.svelte";
  import ToolUseHeader from "../../ToolUseHeader.svelte";
  import FileEditIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/file-pen.svg?component";
  import FileTextIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/file.svg?component";
  import EditFileToolUseDetails from "./EditFileToolUseDetails.svelte";
  import {
    stringOrDefault,
    ToolUsePhase,
    type ToolUseState,
  } from "$common-webviews/src/apps/chat/types/tool-use-state";
  import StrReplaceSecondary from "./StrReplaceSecondary.svelte";
  import ToolUseStatus from "../../../ToolUseStatus.svelte";
  import ShellError from "../ShellError.svelte";
  import LineDiffCount from "./LineDiffCount.svelte";
  import { getToolUseContext } from "../../../tool-context";
  import FilespanOpen from "../../../FilespanOpen.svelte";
  const ctx = getToolUseContext();
  export let toolUseInput: Record<string, unknown> = {};
  export let toolUseState: ToolUseState;
  let collapsed = true;
  $: filepath = stringOrDefault(toolUseInput.path || toolUseInput.file_path, "");
  $: isStrReplace = toolUseInput.command === "str_replace";
</script>

<BaseToolComponent bind:collapsed showToolOutput={isStrReplace}>
  <ToolUseHeader slot="header">
    <svelte:fragment slot="icon">
      {#if isStrReplace}
        <FileEditIcon />
      {:else}
        <FileTextIcon />
      {/if}
    </svelte:fragment>
    <StrReplaceSecondary slot="toolName" {...viewRange(toolUseInput)} />
    <FilespanOpen slot="secondary" {filepath} {...viewRange(toolUseInput)} />
    <svelte:fragment slot="toolStatus">
      <LineDiffCount requestId={$ctx.requestId} />
      <ToolUseStatus />
    </svelte:fragment>
  </ToolUseHeader>

  <div slot="details" class="c-str-replace__details">
    {#if toolUseState.phase === ToolUsePhase.completed}
      <EditFileToolUseDetails />
    {/if}
  </div>
  {#if toolUseState.phase === ToolUsePhase.error}
    <ShellError slot="error" {...toolUseState.result ?? {}} />
  {/if}
</BaseToolComponent>

<style>
  :global(.c-str-replace__header__directory) {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    direction: rtl;
    text-align: left;
    flex: 1;
    color: var(--augment-text-color-secondary);
  }
</style>
