import { readImageAsBlob } from "$common-webviews/src/design-system/components/RichTextEditorAugment/plugins/File/read-image-util";
import {
  type ToolResponseContentNode,
  ToolResponseContentNodeType,
  type ToolUseResponse,
} from "@augment-internal/sidecar-libs/src/tools/tool-types";

/**
 * Result of scaling an image data URL
 */
export interface ScaledImageResult {
  /** The scaled image data URL */
  dataUrl: string;
  /** The MIME type of the scaled image */
  mimeType: string;
}

/**
 * Convert base64-encoded image data to a File object
 * @param base64Data The base64-encoded image data
 * @param filename The name to give the file
 * @param mimeType The MIME type of the image
 * @returns A File object containing the image data
 */
export function base64ToFile(base64Data: string, filename: string, mimeType: string): File {
  // Decode the base64 data
  const bstr = atob(base64Data);

  // Convert base64 to binary
  let n = bstr.length;
  const u8arr = new Uint8Array(n);
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }

  // Create and return a File object
  return new File([u8arr], filename, { type: mimeType });
}

/**
 * Scale an image from base64-encoded data using the existing readImageAsBlob utility
 * @param base64Data The base64-encoded image data
 * @param filename The name of the image file
 * @param mediaType The MIME type of the image
 * @param maxSize Maximum dimension (width or height) in pixels
 * @returns A Promise that resolves to a scaled image result containing the data URL and MIME type
 */
export async function scaleImageData(
  base64Data: string,
  filename: string,
  mediaType: string,
  maxSize = 1024,
): Promise<ScaledImageResult> {
  try {
    // Convert base64 data to File
    const file = base64ToFile(base64Data, filename, mediaType);

    // Use the existing readImageAsBlob function to scale the image
    const scaledBlob = await readImageAsBlob(file, maxSize);

    // Convert the scaled blob back to a data URL
    return new Promise<ScaledImageResult>((resolve, reject) => {
      const reader = new FileReader();
      reader.onloadend = () => {
        if (typeof reader.result === "string") {
          // Extract the data part from the data URL (remove the prefix)
          const dataUrlParts = reader.result.split(",");
          const base64Result = dataUrlParts.length > 1 ? dataUrlParts[1] : reader.result;

          // Canvas.toBlob() always uses image/png by default
          resolve({
            dataUrl: base64Result,
            mimeType: "image/png",
          });
        } else {
          reject(new Error("Failed to convert blob to data URL"));
        }
      };
      reader.onerror = (_event) => {
        // Handle the error event and reject with an appropriate error
        reject(new Error("FileReader error"));
      };
      reader.readAsDataURL(scaledBlob);
    });
  } catch (error) {
    console.error("Error scaling image:", error);
    // Return the original data if scaling fails
    return {
      dataUrl: base64Data,
      mimeType: mediaType, // Keep the original media type
    };
  }
}

/**
 * Process image content nodes in a tool result, scaling them to a reasonable size
 * before sending them to the server.
 *
 * @param contentNodes The content nodes to process
 * @param enableImageToolResults Whether to process image nodes (if false, image nodes will be skipped)
 * @returns A Promise that resolves to the processed content nodes
 */
export async function processImageContentNodes(
  contentNodes: ToolResponseContentNode[],
  enableImageToolResults = true,
): Promise<ToolResponseContentNode[]> {
  const processedNodes: ToolResponseContentNode[] = [];

  for (const node of contentNodes) {
    if (
      node.type === ToolResponseContentNodeType.ContentImage &&
      node.image_content &&
      node.image_content.image_data
    ) {
      // Skip image nodes if image tool results are disabled
      if (!enableImageToolResults) {
        continue;
      }

      // Process image data
      const imageData = node.image_content.image_data;
      const mediaType = node.image_content.media_type || "image/png";
      const fileExtension = mediaType.split("/")[1] || "png";
      const filename = `image.${fileExtension}`;

      // Scale the image
      const scaledImageResult = await scaleImageData(imageData, filename, mediaType);

      // Create a new node with the scaled image
      processedNodes.push({
        /* eslint-disable @typescript-eslint/naming-convention */
        type: ToolResponseContentNodeType.ContentImage,
        image_content: {
          image_data: scaledImageResult.dataUrl,
          media_type: scaledImageResult.mimeType,
        },
        /* eslint-enable @typescript-eslint/naming-convention */
      });
    } else {
      // Keep other nodes as they are
      processedNodes.push(node);
    }
  }

  return processedNodes;
}

/**
 * Process a tool result with images, scaling them to a reasonable size
 * before sending them to the server.
 *
 * @param response The tool use response to process
 * @param enableImageToolResults Whether to process image nodes (if false, image nodes will be skipped)
 * @returns A Promise that resolves to the processed tool use response
 */
export async function processToolResultImages(
  response: ToolUseResponse,
  enableImageToolResults = true,
): Promise<ToolUseResponse> {
  // If there are no content nodes, return the original response
  if (!response.contentNodes || response.contentNodes.length === 0) {
    return response;
  }

  // Process the content nodes, passing the enableImageToolResults flag
  const processedContentNodes = await processImageContentNodes(
    response.contentNodes,
    enableImageToolResults,
  );

  // Return a new response with the processed content nodes
  return {
    ...response,
    contentNodes: processedContentNodes,
  };
}
