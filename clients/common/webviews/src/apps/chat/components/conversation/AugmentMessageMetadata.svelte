<script lang="ts">
  import Filespan from "$common-webviews/src/common/components/Filespan.svelte";
  import {
    type AbsPathFileDetails,
    type FileDetails,
  } from "$vscode/src/webview-providers/webview-messages";
  import { type WorkspaceFileChunk } from "$vscode/src/webview-providers/webview-messages";
  import { onKey } from "$common-webviews/src/common/utils/keypress";
  import { getFileDetailAbsPath } from "$vscode/src/workspace/types";
  import { onMount } from "svelte";
  import SlimButton from "../buttons/SlimButton.svelte";
  import ExclamationTriangle from "$common-webviews/src/design-system/icons/exclamation-triangle.svelte";

  export let resolveChunk: (chunk: WorkspaceFileChunk) => Promise<FileDetails | undefined>;
  export let collapsed: boolean = true;
  export let workspaceFileChunks: WorkspaceFileChunk[];
  export let onLocalFileClick: (details: FileDetails) => void;

  // We always want to render only what is known about the workspace at the time of rendering,
  // so we can't just cache the workpace files information.
  // Instead, we need to fetch the workspace files information on demand.
  // This means whenever a response is rendered, we check the filesystem for its
  // referenced workspace files, and if they are not found, then they are outdated.
  let workspaceFiles: AbsPathFileDetails[] = [];
  let numUnknownWorkspaceChunks = 0;

  // Sets the workspace files given the file details
  // If undefined, adds to unknown files
  const setWorkspaceFiles = (files: (FileDetails | undefined)[]) => {
    let newWorkspaceFiles: AbsPathFileDetails[] = [];
    let newWorkspaceFileNames = new Set<string>();
    let newNumUnknownWorkspaceChunks: number = 0;
    for (const file of files) {
      if (file) {
        const absPath = getFileDetailAbsPath(file);

        if (newWorkspaceFileNames.has(absPath)) {
          continue;
        }

        // Don't copy in ranges because we only want to store
        // file-level information, since sources will be visualized
        // on a per-file, not per-chunk basis.
        newWorkspaceFiles.push({
          repoRoot: file.repoRoot,
          pathName: file.pathName,
          absPath: absPath,
        });
        newWorkspaceFileNames.add(absPath);
      } else {
        newNumUnknownWorkspaceChunks += 1;
      }
    }
    workspaceFiles = Array.from(newWorkspaceFiles);
    numUnknownWorkspaceChunks = newNumUnknownWorkspaceChunks;
  };

  const chunksToWorkspaceFiles = async (chunks: WorkspaceFileChunk[]): Promise<void> => {
    const initFiles = chunks.map((chunk: WorkspaceFileChunk) => chunk.file);
    setWorkspaceFiles(initFiles);
    const maybeFiles = await Promise.all(chunks.map(resolveChunk));
    setWorkspaceFiles(maybeFiles);
  };

  // Only resolve chunksToWorkspaceFiles once, on initialization
  onMount(async () => {
    await chunksToWorkspaceFiles(workspaceFileChunks);
  });
  $: hasSources = workspaceFiles.length > 0 || numUnknownWorkspaceChunks > 0;

  // Scroll the sources into view. If the sources will already visible without scroll
  // then the "nearest" block policy will *not* scroll the page, for visual stability.
  const scrollOnShow = (e: HTMLElement, props: { collapsed: boolean }) => {
    if (!props.collapsed) {
      e.scrollIntoView({ behavior: "smooth", block: "nearest" });
    }
    return {
      update(props: { collapsed: boolean }) {
        if (!props.collapsed) {
          e.scrollIntoView({ behavior: "smooth", block: "nearest" });
        }
      },
    };
  };
</script>

<div class="c-aug-msg-metadata" use:scrollOnShow={{ collapsed }}>
  {#if hasSources && !collapsed}
    <!-- Show a warning if any outdated blobs exist -->
    {#if numUnknownWorkspaceChunks > 0}
      <SlimButton color="error">
        <ExclamationTriangle />
        {numUnknownWorkspaceChunks} outdated sources hidden
      </SlimButton>
    {/if}
    {#each workspaceFiles as file}
      {@const filepath = file.pathName}
      <SlimButton
        on:click={() => onLocalFileClick(file)}
        on:keydown={onKey("Enter", () => onLocalFileClick(file))}
      >
        <Filespan {filepath} />
      </SlimButton>
    {/each}
  {/if}
</div>
