<script lang="ts">
  import { type RemoteAgentsModel } from "$common-webviews/src/apps/remote-agent-manager/models/remote-agents-model";
  import type {
    ChatResultNode,
    ChatResultToolUse,
  } from "@augment-internal/sidecar-libs/src/chat/chat-types";
  import { type ConversationModel, formatHistory } from "../../../models/conversation-model";
  import { type ToolsWebviewModel } from "../../../models/tools-webview-model";
  import {
    type ExchangeWithStatus,
    isChatItemSuccessfulExchange,
  } from "../../../types/chat-message";
  import ToolUse from "./tools/ToolUse.svelte";
  import { onlyDisplayableToolNodes } from "../../../utils/tool-utils";
  import type { ChatFlagsModel } from "../../../models/chat-flags-model";

  export let conversationModel: ConversationModel;
  export let toolsWebviewModel: ToolsWebviewModel;
  export let remoteAgentsModel: RemoteAgentsModel | undefined = undefined;
  export let requestId: string;
  export let toolUseNodes: ChatResultNode[];
  export let isLastTurn: boolean;
  export let turnIndex: number = 0;
  export let messageListContainer: HTMLElement | undefined = undefined;
  export let flagsModel: ChatFlagsModel | undefined = undefined;

  $: displayableToolUseNodes = flagsModel?.enableParallelTools
    ? onlyDisplayableToolNodes(requestId, toolUseNodes, conversationModel, remoteAgentsModel)
    : atMostOneNode(toolUseNodes);

  function onToolRun(requestId: string, toolUse: ChatResultToolUse, toolUseInput: any) {
    toolsWebviewModel.approveTool(
      requestId,
      toolUse.tool_use_id,
      toolUse.tool_name,
      toolUseInput,
      conversationModel.chatHistory
        .filter((m): m is ExchangeWithStatus => isChatItemSuccessfulExchange(m))
        .map(formatHistory),
      conversationModel.id,
    );
  }

  function onToolCancel(requestId: string, toolUseId: string) {
    toolsWebviewModel.cancelToolRun(requestId, toolUseId);
  }

  function onToolSkip(requestId: string, toolUseId: string) {
    toolsWebviewModel.skipToolRun(requestId, toolUseId);
  }

  function atMostOneNode(nodes: ChatResultNode[]): ChatResultNode[] {
    if (nodes.length <= 1) {
      return nodes;
    }
    console.error("Only one tool use is supported, got", nodes.length);
    return nodes.slice(0, 1);
  }
</script>

{#if toolUseNodes !== undefined && toolUseNodes.length > 0}
  <div class="l-tooluse-list">
    {#each displayableToolUseNodes as toolUseNode}
      {@const toolUse = toolUseNode.tool_use}
      {#if toolUse}
        {@const toolUseState = $remoteAgentsModel?.isActive
          ? $remoteAgentsModel.getToolUseState(toolUse.tool_use_id)
          : conversationModel.getToolUseState(requestId, toolUse.tool_use_id)}
        <ToolUse
          {toolUse}
          {toolUseState}
          {isLastTurn}
          {requestId}
          {onToolRun}
          {onToolCancel}
          {onToolSkip}
          {turnIndex}
          {messageListContainer}
          isGroupListItem={true}
        />
      {/if}
    {/each}
  </div>
{/if}

<style>
  .l-tooluse-list {
    display: flex;
    flex-direction: column;
    width: 100%;
    gap: var(--ds-spacing-2);
  }
</style>
