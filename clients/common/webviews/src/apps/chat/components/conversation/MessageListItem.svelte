<script lang="ts">
  export let dataRequestId: string | undefined = undefined;
  export let minHeight: number;
  let className: string = "";
  export { className as class };
</script>

<div
  class={`c-msg-list__item ${className}`}
  style={`--msg-list-item-min-height: ${minHeight}px`}
  data-request-id={dataRequestId}
>
  <slot />
</div>

<style>
  .c-msg-list__item {
    --msg-list-item-min-height: 0px;
    min-height: calc(var(--msg-list-item-min-height) - var(--ds-spacing-2) * 2);
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-1);
    flex: 1;
  }
</style>
