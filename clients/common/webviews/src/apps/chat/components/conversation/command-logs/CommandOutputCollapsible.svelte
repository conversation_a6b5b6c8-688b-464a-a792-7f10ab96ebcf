<script lang="ts">
  import { TooltipTriggerOn } from "$common-webviews/src/design-system/_primitives/TooltipAugment/types";
  import CollapseButtonAugment from "$common-webviews/src/design-system/components/CollapsibleAugment/CollapseButtonAugment.svelte";
  import CollapsibleAugment from "$common-webviews/src/design-system/components/CollapsibleAugment/CollapsibleAugment.svelte";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import MonacoProvider from "$common-webviews/src/design-system/components/MonacoProvider";
  import SimpleMonaco from "$common-webviews/src/design-system/components/MonacoProvider/SimpleMonaco.svelte";
  import SpinnerAugment from "$common-webviews/src/design-system/components/SpinnerAugment.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import Check from "$common-webviews/src/design-system/icons/check.svelte";
  import CircleBackslash from "$common-webviews/src/design-system/icons/circle-backslash.svelte";
  import OpenInNewWindow from "$common-webviews/src/design-system/icons/open-in-new-window.svelte";
  import { type CommandOutputStatus, getStatusText } from "./types";

  export let title: string = "";
  export let command: string = "";
  export let output: string | null = null;
  export let status: CommandOutputStatus | null = null;
  export let collapsed: boolean = false;
  export let useMonaco: boolean = false;
  export let monacoLang: string = "bash";
  export let viewButtonTooltip: string = "View full output in editor";
  export let showCollapseButton: boolean = true;
  export let isLoading: boolean = false;

  /**
   * Event handler for the view output button
   */
  export let onViewOutput: (e: Event, output: string) => void;

  /**
   * Render the status icon based on command status
   */
  function renderStatusIcon(status: CommandOutputStatus) {
    if (status === "success" || status === "skipped") {
      return Check;
    } else if (status === "error") {
      return CircleBackslash;
    } else {
      return CircleBackslash;
    }
  }
</script>

<div class="c-command-output__container">
  <CollapsibleAugment {collapsed}>
    <div slot="header" class="c-command-output__collapsible-header">
      {#if showCollapseButton && status !== "skipped"}
        <CollapseButtonAugment />
      {:else}
        <div class="c-command-output__collapsible-header__spacer"></div>
      {/if}

      {#if title}
        <TextAugment size={1} weight="medium">{title}</TextAugment>
      {:else}
        <TextAugment size={1} type="monospace">
          {command}
        </TextAugment>
      {/if}

      <div class="c-command-output__status-indicator">
        {#if isLoading}
          <div class="c-command-output__status-icon c-command-output__status-icon--loading">
            <SpinnerAugment size={1} />
          </div>
        {:else if status !== null}
          {#if status === "skipped"}
            <TextAugment size={1}>Skipped</TextAugment>
          {/if}
          <TextTooltipAugment content={getStatusText(status)} triggerOn={[TooltipTriggerOn.Hover]}>
            <div
              class="c-command-output__status-icon"
              class:c-command-output__status-icon--success={status === "success"}
              class:c-command-output__status-icon--error={status === "error"}
              class:c-command-output__status-icon--warning={status === "skipped"}
            >
              <svelte:component this={renderStatusIcon(status)} />
            </div>
          </TextTooltipAugment>
        {/if}

        {#if output && (!status || status !== "skipped") && !isLoading}
          <TextTooltipAugment content={viewButtonTooltip} align="end">
            <IconButtonAugment
              variant="ghost-block"
              color="neutral"
              size={1}
              on:click={(e) => onViewOutput(e, output)}
            >
              <OpenInNewWindow />
            </IconButtonAugment>
          </TextTooltipAugment>
        {/if}
      </div>
    </div>

    <div class="c-command-output__command-details">
      {#if command && !title}
        <div class="c-command-output__code-block">
          <TextAugment size={1} type="monospace">
            {command}
          </TextAugment>
        </div>
      {/if}

      {#if output && (!status || status !== "skipped")}
        <div class="c-command-output__code-block c-command-output__code-block--output">
          {#if useMonaco}
            <MonacoProvider.Root>
              <SimpleMonaco text={output} lang={monacoLang} />
            </MonacoProvider.Root>
          {:else}
            <TextAugment size={1} type="monospace">
              {output}
            </TextAugment>
          {/if}
        </div>
      {/if}

      <slot></slot>
    </div>
  </CollapsibleAugment>
</div>

<style>
  .c-command-output__container {
    border-radius: var(--ds-radius-2);
    overflow: hidden;
  }

  .c-command-output__collapsible-header__spacer {
    width: 24px;
  }

  .c-command-output__collapsible-header {
    display: flex;
    align-items: center;
    padding: var(--ds-spacing-1);
    flex: 1;
  }

  .c-command-output__status-indicator {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-1);
    margin-left: auto;
  }

  .c-command-output__status-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--ds-spacing-1);
    color: var(--ds-color-neutral-11);
  }

  .c-command-output__status-icon--success {
    color: var(--ds-color-success-9);
  }

  .c-command-output__status-icon--error {
    color: var(--ds-color-error-9);
  }

  .c-command-output__status-icon--warning {
    color: var(--ds-color-warning-9);
  }

  .c-command-output__status-icon--loading {
    padding-right: var(--ds-spacing-1);
    color: var(--ds-color-accent-9);
  }

  .c-command-output__container :global(.c-collapsible__content) {
    border-radius: 0 0 var(--collapsible-panel-radius) var(--collapsible-panel-radius);
  }

  .c-command-output__code-block {
    overflow: hidden;
    background-color: var(--ds-color-neutral-a2);
    border-radius: 0 0 var(--collapsible-panel-radius) var(--collapsible-panel-radius);
    padding: var(--ds-spacing-3);
  }

  .c-command-output__code-block--output {
    overflow: auto;
    max-height: 400px;
  }

  .c-command-output__container :global(.c-text--type-monospace) {
    white-space: pre;
  }

  /** The parent component resets the padding,
  * so we need to override it with higher specificity */
  .c-command-output__container :global(.c-collapsible__header-inner) {
    padding: var(--ds-spacing-1) 0;
  }
</style>
