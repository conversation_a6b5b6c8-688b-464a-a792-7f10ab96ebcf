/**
 * @file ConversationNavigationControls.test.ts
 * Tests for the conversation navigation controls functionality
 */

import { describe, test, expect, vi, beforeEach } from "vitest";
import { scrollToBottom, scrollToY } from "../../../utils/scroll-utils";

// Mock the followBottom actions
vi.mock("../../../utils/scroll-utils", () => ({
  scrollToBottom: vi.fn(),
  scrollToY: vi.fn(),
}));

describe("Navigation scroll functions", () => {
  let mockContainer: HTMLElement;
  let mockScrollToBottom: any;
  let mockScrollToY: any;

  beforeEach(() => {
    vi.clearAllMocks();

    mockScrollToBottom = vi.mocked(scrollToBottom);
    mockScrollToY = vi.mocked(scrollToY);

    // Create mock container
    mockContainer = {
      scrollTop: 100,
      scrollTo: vi.fn(),
    } as any;

    // Define read-only properties
    Object.defineProperty(mockContainer, "clientHeight", {
      value: 200,
      writable: true,
      configurable: true,
    });
    Object.defineProperty(mockContainer, "scrollHeight", {
      value: 500,
      writable: true,
      configurable: true,
    });
  });

  test("should call scrollToY with correct parameters", () => {
    const offsetTop = 300;

    // Simulate the scrollTo function from observeConversationItems
    scrollToY(mockContainer, offsetTop, { smooth: true });

    expect(mockScrollToY).toHaveBeenCalledWith(mockContainer, offsetTop, { smooth: true });
  });

  test("should call scrollToBottom with correct parameters", () => {
    // Simulate the scrollToBottom function from observeConversationItems
    scrollToBottom(mockContainer, { smooth: true });

    expect(mockScrollToBottom).toHaveBeenCalledWith(mockContainer, { smooth: true });
  });

  test("should handle scroll to bottom when not at bottom", () => {
    // Mock container not at bottom
    mockContainer.scrollTop = 100;
    Object.defineProperty(mockContainer, "scrollHeight", { value: 500, configurable: true });
    Object.defineProperty(mockContainer, "clientHeight", { value: 200, configurable: true });

    scrollToBottom(mockContainer, { smooth: true });

    expect(mockScrollToBottom).toHaveBeenCalledWith(mockContainer, { smooth: true });
  });

  test("should handle scroll to specific position", () => {
    const targetPosition = 250;

    scrollToY(mockContainer, targetPosition, { smooth: true });

    expect(mockScrollToY).toHaveBeenCalledWith(mockContainer, targetPosition, { smooth: true });
  });

  test("should handle scroll with different options", () => {
    scrollToBottom(mockContainer, { smooth: false });

    expect(mockScrollToBottom).toHaveBeenCalledWith(mockContainer, { smooth: false });
  });
});
