<script lang="ts">
  import { ToolUsePhase } from "$common-webviews/src/apps/chat/types/tool-use-state";
  import { getToolUseContext } from "../../../tool-context";
  export let start: number | undefined = undefined;
  export let stop: number | undefined = undefined;

  const ctx = getToolUseContext();
</script>

{#if $ctx.toolUseInput.command === "view"}
  {($ctx.toolUseInput.edit_summary ?? $ctx.toolUseState.phase === ToolUsePhase.running)
    ? "Reading file..."
    : !(start && stop)
      ? `Read file`
      : `Read lines ${start}-${stop}`}
{:else}
  {$ctx.toolUseState.phase === ToolUsePhase.running ? "Editing file..." : "Edited file"}
{/if}
