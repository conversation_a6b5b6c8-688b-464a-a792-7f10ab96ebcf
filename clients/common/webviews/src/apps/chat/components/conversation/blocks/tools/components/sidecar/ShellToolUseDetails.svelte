<script lang="ts">
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import {
    ToolUsePhase,
    type ToolUseState,
  } from "$common-webviews/src/apps/chat/types/tool-use-state";
  import type { parseShellOutput } from "./shell-util";
  import ShowMore from "$common-webviews/src/common/components/ShowMore.svelte";
  import CopyButton from "$common-webviews/src/common/components/CopyButton.svelte";

  export let toolUseInput: Record<string, unknown>;
  export let result: ReturnType<typeof parseShellOutput>;
  export let toolUseState: ToolUseState;

  let entries: [string, string[]][] = [];
  if (typeof toolUseInput.command_type === "string") {
    if (
      toolUseInput.command_type === "simple" &&
      Array.isArray(toolUseInput.simple_command) &&
      toolUseInput.simple_command.length > 0
    ) {
      entries = [
        ["Command", [toolUseInput.simple_command[0]]],
        ["Args", toolUseInput.simple_command.slice(1)],
      ];
    } else if (
      toolUseInput.command_type === "complex" &&
      typeof toolUseInput.complex_command === "string"
    ) {
      entries = [["Command", [toolUseInput.complex_command]]];
    }
  } else if (typeof toolUseInput.command === "string") {
    entries = [["Command", [toolUseInput.command]]];
  }
  import { getContext } from "svelte";
  import { type ChatModel } from "$common-webviews/src/apps/chat/models/chat-model";

  const chatModel = getContext<ChatModel>("chatModel");
  $: flagsModel = chatModel?.flags;
</script>

<div class="c-shell-tooluse__details">
  {#each entries as [key, values]}
    <div class="c-shell-tooluse__details__group">
      <div class="c-shell-tooluse__details__heading">
        <TextAugment size={1} weight="medium" color="primary">
          {key}
        </TextAugment>
        <CopyButton text={values.join("\n")} />
      </div>
      {#each values as value}
        <div class="c-shell-tooluse__content">
          <ShowMore maxHeight={90}>
            <TextAugment size={1}>
              <pre class="c-shell-tooluse__value">$ {value}</pre>
            </TextAugment>
          </ShowMore>
        </div>
      {/each}
    </div>
  {/each}

  {#if result.output && toolUseState.phase !== ToolUsePhase.error}
    <div class="c-shell-tooluse__details__group">
      <div class="c-shell-tooluse__details__heading">
        <TextAugment size={1} weight="medium" color="primary">Output</TextAugment>
        <CopyButton text={result.output} />
      </div>
      <div class="c-shell-tooluse__content">
        <ShowMore maxHeight={90}>
          <TextAugment size={$flagsModel?.enableErgonomicsUpdate ? 1 : 2} type="monospace">
            <pre class="c-shell-tooluse__value">{result.output?.trim()}</pre>
          </TextAugment>
        </ShowMore>
      </div>
    </div>
  {/if}
</div>

<style>
  :global(.c-shell-tooluse__details .c-copy-button) {
    visibility: hidden;
  }
  :global(.c-shell-tooluse__details:hover .c-copy-button) {
    visibility: unset;
  }
  .c-shell-tooluse__details {
    /*
    Setting up local variable for padding, so we can use it in calc
    to set negative margin on the show more button container
    */
    --tool-use-padding-left: var(--ds-spacing-4_5);
    --tool-use-padding-right: var(--ds-spacing-2);
    --tool-use-padding-vertical: var(--ds-spacing-1);
    --tool-use-border-color: var(--collapsible-panel-border-color);

    display: flex;
    flex-direction: column;
    background: var(--ds-color-neutral-a2);
    border: 0;

    & :global(.c-shell-tooluse__value),
    & :global(.c-shell-tooluse__value pre .c-shell-tooluse__value),
    & :global(.c-shell-tooluse__value pre) {
      font-family: var(--augment-monospace-font-family, monospace);
      white-space: pre-wrap;
      word-break: break-word;
      gap: var(--ds-spacing-1);
      border: 0;
      padding: var(--ds-spacing-2) 0;
    }

    & :global(.c-show-more__content:last-child) {
      /* Show more content is the last child ONLY
      when there is not a show more button. When that is the case,
      we want to add some bottom margin to the last child,
      so that it does not bump up against the bottom of the
      parent container.
      */
      margin-bottom: calc(2 * var(--tool-use-padding-vertical));
    }

    & :global(.c-shell-tooluse__details__heading) {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      gap: var(--ds-spacing-1);
      flex: 1;
      padding: 0;
    }

    & :global(.c-shell-tooluse__content .c-show-more__button-container) {
      /*
      The show more button as designed today should be edge-to-edge
      with the parent container. However, we want to maintain the padding
      on the parent container. So we use negative margins and increased width
      to achieve this.
      */
      margin-left: calc(-1 * var(--tool-use-padding-left));
      margin-right: calc(-1 * var(--tool-use-padding-right));
      width: calc(100% + var(--tool-use-padding-left) + var(--tool-use-padding-right));
      border-bottom-left-radius: var(--ds-radius-2);
      border-bottom-right-radius: var(--ds-radius-2);
    }

    & :global(.c-shell-tooluse__details__group) {
      display: flex;
      flex-direction: column;
      gap: 0;
      border-radius: var(--ds-radius-2);
      padding: var(--tool-use-padding-vertical) var(--tool-use-padding-right) 0
        var(--tool-use-padding-left);
      display: flex;
      flex-direction: column;
      border-radius: var(--ds-radius-2);

      & :global(pre) {
        margin: 0;
        font-family: var(--augment-monospace-font-family, monospace);
        white-space: pre-wrap;
        word-break: break-word;
        gap: var(--ds-spacing-1);
        border: 0;
        padding: 0;
      }

      &:not(:last-child) {
        border-bottom-left-radius: 0;
        border-bottom-right-radius: 0;
        border-bottom: 1px dotted var(--tool-use-border-color); /* Divider between command, args, and output */
      }
    }
  }
</style>
