<script lang="ts">
  import BaseToolComponent from "../../BaseToolComponent.svelte";
  import ToolUseHeader from "../../ToolUseHeader.svelte";
  import MessageIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/message.svg?component";
  import {
    ToolUsePhase,
    type ToolUseState,
  } from "$common-webviews/src/apps/chat/types/tool-use-state";
  import ShellError from "../../sidecar/ShellError.svelte";
  import StatusBadgeAugment from "$common-webviews/src/design-system/components/StatusBadgeAugment.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import CardAugment from "$common-webviews/src/design-system/components/CardAugment.svelte";
  import ToolResultFields from "./ToolResultFields.svelte";

  export let toolUseInput: Record<string, unknown> = {};
  export let toolUseState: ToolUseState;
  let collapsed = true;

  // Extract instruction operations array from input
  $: instructionOperations = Array.isArray(toolUseInput.instruction_operations)
    ? toolUseInput.instruction_operations
    : [];

  // Format tool arguments for display - show count of instructions in collapsed state
  $: formattedArgs = (() => {
    const args: string[] = [];
    if (instructionOperations.length > 0) {
      args.push(
        `${instructionOperations.length} instruction${instructionOperations.length === 1 ? "" : "s"}`,
      );
    }
    return args;
  })();

  // Parse the tool result to get instruction results
  $: instructionResults = (() => {
    if (
      toolUseState.phase === ToolUsePhase.completed &&
      toolUseState.result?.text &&
      !toolUseState.result.isError
    ) {
      try {
        const response = JSON.parse(toolUseState.result.text);
        // Combine successful and failed instructions into a single array
        const successful = Array.isArray(response.instructionResults)
          ? response.instructionResults
          : [];
        const failed = Array.isArray(response.failedInstructions)
          ? response.failedInstructions
          : [];
        return [...successful, ...failed];
      } catch (e) {
        console.error("Failed to parse send instruction tool response:", e);
      }
    }
    return [];
  })();

  // Count successful and failed instructions
  $: successCount = instructionResults.filter((result: any) => result.success).length;
  $: failureCount = instructionResults.filter((result: any) => !result.success).length;
  $: hasResults = instructionResults.length > 0;
</script>

<BaseToolComponent bind:collapsed>
  <ToolUseHeader slot="header" toolName="Send Instructions" formattedToolArgs={formattedArgs}>
    <MessageIcon slot="icon" />
    <svelte:fragment slot="secondary">
      {#if hasResults && collapsed}
        <div class="c-send-instruction-tool__counts">
          {#if successCount > 0}
            <StatusBadgeAugment color="success" size={1}>
              {successCount} sent
            </StatusBadgeAugment>
          {/if}
          {#if failureCount > 0}
            <StatusBadgeAugment color="error" size={1}>
              {failureCount} failed
            </StatusBadgeAugment>
          {/if}
        </div>
      {/if}
    </svelte:fragment>
  </ToolUseHeader>

  <div slot="details" class="c-send-instruction-tool__details">
    {#if toolUseState.phase === ToolUsePhase.running}
      <div class="c-send-instruction-tool__running">
        <TextAugment size={1} color="neutral">Sending instructions to worker agents...</TextAugment>
      </div>
    {:else if toolUseState.phase === ToolUsePhase.completed && hasResults}
      <div class="c-send-instruction-tool__results">
        {#each instructionResults as result}
          {@const operation = instructionOperations[result.index] || {}}
          <CardAugment class="c-send-instruction-tool__instruction-card">
            <div class="c-send-instruction-tool__instruction-header">
              <div class="c-send-instruction-tool__instruction-title">
                <TextAugment size={1} weight="medium">
                  Instruction {result.index + 1}
                  {#if operation.summary}
                    - {operation.summary}
                  {/if}
                </TextAugment>
                <StatusBadgeAugment color={result.success ? "success" : "error"} size={1}>
                  {result.success ? "Sent" : "Failed"}
                </StatusBadgeAugment>
              </div>
            </div>

            <div class="c-send-instruction-tool__instruction-details">
              {#if result.success}
                <div class="c-send-instruction-tool__success-details">
                  <TextAugment size={1} color="neutral">
                    <strong>Agent ID:</strong>
                    {result.agentId}
                  </TextAugment>
                  <ToolResultFields
                    data={result}
                    fields={[
                      {
                        key: "instructionSent",
                        label: "Instruction",
                        type: "monospace",
                        collapsible: true,
                      },
                    ]}
                  />
                </div>
              {:else}
                <div class="c-send-instruction-tool__error-details">
                  <TextAugment size={1} color="error">
                    <strong>Error:</strong>
                    {result.error || "Unknown error"}
                  </TextAugment>
                  {#if operation.worker_agent_id}
                    <TextAugment size={1} color="neutral">
                      <strong>Target Agent ID:</strong>
                      {operation.worker_agent_id}
                    </TextAugment>
                  {/if}
                </div>
              {/if}
            </div>
          </CardAugment>
        {/each}
      </div>
    {:else if toolUseState.phase === ToolUsePhase.completed && !hasResults}
      <div class="c-send-instruction-tool__no-results">
        <TextAugment size={1} color="neutral">No instructions were sent.</TextAugment>
      </div>
    {/if}
  </div>

  {#if toolUseState.phase === ToolUsePhase.error}
    <ShellError slot="error" {...toolUseState.result ?? {}} />
  {/if}
</BaseToolComponent>

<style>
  .c-send-instruction-tool__counts {
    display: flex;
    gap: var(--ds-spacing-2);
    align-items: center;
    flex-wrap: wrap;
  }

  .c-send-instruction-tool__details {
    padding: var(--ds-spacing-2);
  }

  .c-send-instruction-tool__results {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-2);
  }

  .c-send-instruction-tool__instruction-header {
    margin-bottom: var(--ds-spacing-2);
  }

  .c-send-instruction-tool__instruction-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: var(--ds-spacing-2);
  }

  .c-send-instruction-tool__instruction-details {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-2);
  }
</style>
