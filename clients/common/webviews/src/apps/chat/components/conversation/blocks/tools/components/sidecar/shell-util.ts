import { getLogger } from "@augment-internal/sidecar-libs/src/logging";

// Process the input to extract command arguments
export function processToolInput(input: Record<string, unknown> = {}): string[] {
  if (!input) {
    return [];
  }
  if (typeof input.command_type === "string") {
    if (input.command_type === "simple" && Array.isArray(input.simple_command)) {
      return input.simple_command;
    } else if (input.command_type === "complex" && typeof input.complex_command === "string") {
      return [input.complex_command];
    } else {
      //eslint-disable-next-line no-console
      getLogger("ShellUtil").warn("Unknown shell command type", input);
      return [];
    }
  } else if (typeof input.command === "string") {
    return [input.command];
  }
  return [];
}

/**
 * Parse the output of a shell command.
 * It's limited to about 200 lines.  This should
 * help prevent the ide from blowing up with long output.
 */
const MAX_SIZE = 80 * 200; // The last 200 lines or so.
export function parseShellOutput(input: string) {
  if (!input) {
    return { reason: "", returnCode: 0, output: "" };
  }

  if (input.length > MAX_SIZE) {
    return {
      reason: "Output too large",
      returnCode: 0,
      output: input.slice(MAX_SIZE * -1),
    };
  }

  // Quick check if tags exist before running regex
  const hasReturnCode = input.includes("<return-code>");
  const hasOutput = input.includes("<output>");

  if (!hasReturnCode && !hasOutput) {
    return { reason: "", returnCode: 0, output: input };
  }

  // More efficient regex - avoid catastrophic backtracking
  const returnCodeMatch = hasReturnCode
    ? /<return-code>\s*(-?\d+)\s*<\/return-code>/.exec(input)
    : null;
  const outputMatch = hasOutput ? /<output>\s*(.*?)\s*<\/output>/s.exec(input) : null;

  let reason = "";
  let returnCode = "0";
  let output = input; // fallback to full input

  if (returnCodeMatch) {
    returnCode = returnCodeMatch[1];
    reason = input.substring(0, returnCodeMatch.index);
  }

  if (outputMatch) {
    output = outputMatch[1];
    if (!returnCodeMatch) {
      reason = input.substring(0, outputMatch.index);
    }
  }

  return {
    reason,
    returnCode: Number(returnCode),
    output,
  };
}
