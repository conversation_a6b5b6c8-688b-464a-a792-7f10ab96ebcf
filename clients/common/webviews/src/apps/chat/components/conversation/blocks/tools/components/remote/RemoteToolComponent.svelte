<script lang="ts">
  import BaseToolComponent from "../BaseToolComponent.svelte";
  import ToolUseHeader from "../ToolUseHeader.svelte";
  import ToolUseDetails from "../../ToolUseDetails.svelte";
  import { extractHref, extractSummary } from "./remote-util";
  import { getToolUseContext } from "../../tool-context";
  import OpenLinkButton from "../OpenLinkButton.svelte";

  export let toolName: string;

  const ctx = getToolUseContext();
  let { formattedToolArgs, toolUseInput } = extractSummary($ctx.toolUseInput);
  $: {
    const result = extractSummary($ctx.toolUseInput);
    formattedToolArgs = result.formattedToolArgs;
    toolUseInput = result.toolUseInput;
  }
</script>

<BaseToolComponent showToolOutput={true}>
  <ToolUseHeader slot="header" {toolName} {formattedToolArgs}>
    <slot name="icon" slot="icon" />
    <OpenLinkButton slot="toolAction" href={extractHref(toolUseInput)} />
  </ToolUseHeader>
  <ToolUseDetails slot="details" {toolUseInput} toolUseState={$ctx.toolUseState} />
</BaseToolComponent>
