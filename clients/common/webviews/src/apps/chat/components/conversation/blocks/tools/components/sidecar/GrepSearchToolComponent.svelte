<script lang="ts">
  import BaseToolComponent from "../BaseToolComponent.svelte";
  import ToolUseHeader from "../ToolUseHeader.svelte";
  import Codeblock from "$common-webviews/src/common/components/markdown/Codeblock.svelte";
  import MagnifyingGlass from "$common-webviews/src/design-system/icons/magnifying-glass.svelte";

  export const toolUseInput: Record<string, unknown> = {};
  export let toolUseState: any;
</script>

<BaseToolComponent>
  <ToolUseHeader slot="header" toolName="Grep Search">
    <MagnifyingGlass slot="icon" />
  </ToolUseHeader>

  <div slot="details" class="tool-content">
    {#if toolUseState?.result?.text}
      <Codeblock
        token={{
          text: toolUseState.result.text,
          lang: "text",
          type: "code",
          raw: toolUseState.result.text,
        }}
      />
    {:else if toolUseState?.phase === "running"}
      <div class="searching">Searching...</div>
    {:else}
      <div class="no-results">No search results available</div>
    {/if}
  </div>
</BaseToolComponent>

<style>
  .tool-content {
    padding: 0.5rem;
    overflow: auto;
  }

  .searching,
  .no-results {
    padding: 0.5rem;
    font-style: italic;
    color: var(--vscode-descriptionForeground);
  }
</style>
