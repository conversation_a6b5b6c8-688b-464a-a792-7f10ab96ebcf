<script lang="ts">
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import { dsColorAttribute } from "$common-webviews/src/design-system/_libs/component-utils";
  import CircleColorFill from "$common-webviews/src/design-system/icons/augment/circle-color-fill.svelte";

  export let status: "error" | "success" | undefined = undefined;
  export let content: string;
</script>

<TextTooltipAugment {content}>
  <div class="c-status-indicator" {...dsColorAttribute(status)}>
    <slot>
      <CircleColorFill />
    </slot>
  </div>
</TextTooltipAugment>

<style>
  .c-status-indicator {
    color: var(--ds-color-a11);
    --indicator-size: 6px;
    display: flex;
    /** need to center it in a 16px art board where the circle is 16px*/
    padding: 0 5px;
  }
  .c-status-indicator > :global(svg) {
    /** circle should be 6px this gets it there do not ask to many questions*/
    width: calc(var(--indicator-size) * 1.5);
    aspect-ratio: 1 / 1;
  }
</style>
