<!-- A component that displays a single file change in a checkpoint -->
<script lang="ts">
  import { type IQualifiedPathName } from "@augment-internal/sidecar-libs/src/workspace/workspace-types";
  import { type ChatAgentFileChangeSummary } from "$vscode/src/webview-providers/webview-messages";
  import AgentEditListItem from "../../agent-edits/AgentEditListItem.svelte";

  export let qualifiedPathName: IQualifiedPathName;
  export let lineChanges: ChatAgentFileChangeSummary | undefined;

  export let onFileClick: () => void;
  export let onReviewClick: () => void;
  export let onRevertClick: () => void;
</script>

<!-- Use the AgentEditListItem component directly -->
<AgentEditListItem
  {qualifiedPathName}
  {lineChanges}
  onClickFile={onFileClick}
  onClickReview={onReviewClick}
  onClickRevert={onRevertClick}
/>
