<script lang="ts">
  import MaterialIcon from "$common-webviews/src/common/components/MaterialIcon.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import ContextEngineToolComponent from "../ContextEngineToolComponent.svelte";

  export let toolUseInput: Record<string, unknown> = {};
</script>

<ContextEngineToolComponent retrieval="Commit History" {toolUseInput}>
  <TextAugment size={1} color="neutral" slot="retrieval">
    <MaterialIcon iconName="commit" />Commit History
  </TextAugment>
</ContextEngineToolComponent>
