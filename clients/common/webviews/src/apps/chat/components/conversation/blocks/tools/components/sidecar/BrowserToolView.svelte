<script lang="ts">
  import BaseToolComponent from "../BaseToolComponent.svelte";
  import Globe from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/globe.svg?component";
  import ToolUseHeader from "../ToolUseHeader.svelte";
  import SpinnerAugment from "$common-webviews/src/design-system/components/SpinnerAugment.svelte";
  import {
    stringOrDefault,
    ToolUsePhase,
  } from "$common-webviews/src/apps/chat/types/tool-use-state";
  import OpenLinkButton from "../OpenLinkButton.svelte";

  export let toolUseInput: Record<string, unknown> = {};
  export let toolUseState: any;
  export let toolName: string;

  $: safeUrl = stringOrDefault(toolUseInput.url, "");
  let domain = safeParseDomain(safeUrl);
  $: domain = safeParseDomain(safeUrl);

  // Extract domain from URL for favicon
  function safeParseDomain(url: string): string {
    try {
      return new URL(url).hostname;
    } catch (e) {
      return "";
    }
  }

  async function loadImage(dom: string): Promise<string> {
    return new Promise((res, rej) => {
      const img = new Image();
      const favUrl = `https://www.google.com/s2/favicons?domain=${dom}&sz=12`;
      if (!favUrl) {
        rej(new Error("No favicon URL"));
        return;
      }
      img.onload = () => {
        res(favUrl);
      };
      img.onerror = (e) => {
        console.warn(e);
        rej(e);
      };
      img.src = favUrl;
    });
  }

  function stateMessage(phase: ToolUsePhase, url: string) {
    switch (phase) {
      case ToolUsePhase.running:
        return `Getting information from ${url}...`;
      case ToolUsePhase.error:
        return "Error";
      case ToolUsePhase.cancelled:
        return "Cancelled";
      case ToolUsePhase.runnable:
        return `Waiting for user approval to get ${url}`;
      case ToolUsePhase.completed:
        return "";
      default:
        return "";
    }
  }
</script>

<BaseToolComponent>
  <ToolUseHeader
    slot="header"
    {toolName}
    formattedToolArgs={[stateMessage(toolUseState.phase, domain)]}
  >
    <Globe slot="icon" />
    <OpenLinkButton slot="toolAction" href={safeUrl} />
  </ToolUseHeader>
  <div class="c-web-fetch-container" slot="details">
    <span class="c-web-fetch-favicon-container">
      {#if domain}
        {#await loadImage(domain || "")}
          <SpinnerAugment size={1} class="c-web-fetch-favicon" />
        {:then faviconUrl}
          <img
            src={faviconUrl}
            alt="Website favicon {faviconUrl}"
            on:load={onload}
            on:error={onerror}
          />
        {/await}
      {:else}
        <SpinnerAugment size={1} class="c-web-fetch-favicon" />
      {/if}
      <span class="c-web-fetch-url">{toolUseInput.url || ""}</span>
    </span>
  </div>
</BaseToolComponent>

<style>
  .c-web-fetch-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--ds-spacing-1);
    overflow: hidden;
    max-width: 100%;
    flex: 1;
    padding: var(--ds-spacing-1) var(--ds-spacing-1) var(--ds-spacing-1) var(--ds-spacing-4_5);
    background-color: var(--ds-color-neutral-a2);
    border-radius: var(--ds-radius-1);

    & :global(.c-web-fetch-favicon),
    & :global(.c-web-fetch-favicon) {
      width: 12px;
      height: 12px;
      object-fit: contain;
      flex: 0;
      transition: opacity 0.2s ease;
    }
  }

  .c-web-fetch-favicon-container {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-1);
  }

  .c-web-fetch-url {
    display: flex;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    flex: 1;
  }
</style>
