<script lang="ts">
  import { scrollToBottom } from "../../utils/scroll-utils";
  import MessageListFloating from "./MessageListFloating.svelte";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import ArrowDown from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/arrow-down.svg?component";
  import { fade } from "svelte/transition";

  // Props for controlling visibility of different buttons
  export let showScrollDown: boolean = false;
  // The message list element to scroll
  export let messageListElement: HTMLElement | null = null;

  // Handle scroll to bottom click
  const handleScrollToBottom = () => {
    if (messageListElement) {
      scrollToBottom(messageListElement, { smooth: true });
    }
  };
</script>

<MessageListFloating position="bottom">
  {#if showScrollDown}
    <div class="c-msg-list-bottom-button" transition:fade={{ duration: 150 }}>
      <div transition:fade={{ duration: 150 }}>
        <IconButtonAugment
          class="c-chat-floating-button"
          variant="outline"
          color="neutral"
          size={1}
          radius="full"
          on:click={handleScrollToBottom}
        >
          <ArrowDown />
        </IconButtonAugment>
      </div>
    </div>
  {/if}
</MessageListFloating>

<style>
  .c-msg-list-bottom-button {
    padding-bottom: var(--ds-spacing-2);
  }

  .c-msg-list-bottom-button :global(.c-chat-floating-button) {
    background-color: var(--user-theme-panel-background);

    &:hover {
      background-color: var(--user-theme-panel-background);
    }
  }

  /* Style the SVG to use currentColor and size to the button */
  :global(.c-chat-floating-button svg) {
    fill: var(--icon-color, currentColor);
    height: 14px;
    aspect-ratio: 1/1;
    opacity: 1;
  }
</style>
