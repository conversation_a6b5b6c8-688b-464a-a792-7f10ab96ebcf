<script lang="ts">
  import BaseToolComponent from "../BaseToolComponent.svelte";
  import FileMinusIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/file-minus.svg?component";
  import ToolUseHeader from "../ToolUseHeader.svelte";
  import Filespan from "$common-webviews/src/common/components/Filespan.svelte";

  export let toolUseInput: Record<string, unknown> = {};

  $: filePaths = Array.isArray(toolUseInput.file_paths)
    ? toolUseInput.file_paths.filter((x) => typeof x === "string")
    : [];
</script>

<BaseToolComponent showToolOutput={false}>
  <ToolUseHeader slot="header" toolName="Remove">
    <FileMinusIcon slot="icon" />
    <span slot="secondary">
      {#each filePaths as filePath (filePath)}
        <Filespan filepath={filePath} />
      {/each}
    </span>
  </ToolUseHeader>
</BaseToolComponent>
