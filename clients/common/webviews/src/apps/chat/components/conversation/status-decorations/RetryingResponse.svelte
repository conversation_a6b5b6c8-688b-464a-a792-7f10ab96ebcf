<script lang="ts">
  import SpinnerAugment from "$common-webviews/src/design-system/components/SpinnerAugment.svelte";

  export let message: string = "Retrying...";
</script>

<span class="c-retry-response">
  <SpinnerAugment size={1} />
  {message}
</span>

<style>
  .c-retry-response {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: var(--ds-spacing-1);
    padding: var(--ds-spacing-1) var(--ds-spacing-2);
    opacity: 50%;
  }
</style>
