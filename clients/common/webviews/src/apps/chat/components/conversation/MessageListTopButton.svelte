<script lang="ts">
  import { scrollToTop } from "../../utils/scroll-utils";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import MessageListFloating from "./MessageListFloating.svelte";
  import ArrowUp from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/arrow-up.svg?component";

  // Props for controlling visibility
  export let showScrollUp: boolean = false;

  // The message list element to scroll
  export let messageListElement: HTMLElement | null = null;

  // Optional callback for custom scroll to top behavior
  export let onScrollToTop: (() => void) | null = null;

  // Handle scroll to top click
  const handleScrollToTop = () => {
    if (onScrollToTop) {
      onScrollToTop();
    } else if (messageListElement) {
      scrollToTop(messageListElement, { smooth: true });
    }
  };
</script>

<MessageListFloating position="top">
  {#if showScrollUp}
    <IconButtonAugment
      class="c-chat-floating-button"
      variant="solid"
      color="neutral"
      size={1}
      radius="full"
      on:click={handleScrollToTop}
    >
      <ArrowUp />
    </IconButtonAugment>
  {/if}
</MessageListFloating>

<style>
  /* Style the SVG to use currentColor and size to the button */
  :global(.c-chat-floating-button svg) {
    fill: var(--icon-color, currentColor);
    height: 14px;
    aspect-ratio: 1/1;
  }
</style>
