<script lang="ts">
  import { type SummaryResponseMessage } from "../../../types/chat-message";
  import type { ConversationModel } from "../../../models/conversation-model";
  import { onKey } from "$common-webviews/src/common/utils/keypress";
  import SpinnerAugment from "$common-webviews/src/design-system/components/SpinnerAugment.svelte";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";

  export let conversationModel: ConversationModel;
  export let turn: SummaryResponseMessage;
  export let showIntroText: boolean = true;

  function onQuestionClick(question: string) {
    conversationModel.sendSuggestedQuestion(question);
  }

  $: questions = turn.questions || [];
</script>

{#if turn.questionsLoaded && questions.length > 0}
  {#if showIntroText}
    <p><strong>Here are some questions you can ask Augment:</strong></p>
  {/if}
  <div class="l-questions">
    {#each questions as question}
      <ButtonAugment
        variant="ghost"
        size={1}
        color="accent"
        alignment="left"
        on:keyup={onKey("Enter", () => onQuestionClick(question))}
        on:click={() => onQuestionClick(question)}
      >
        {question}
      </ButtonAugment>
    {/each}
  </div>
{:else if !turn.questionsLoaded}
  <p><SpinnerAugment size={1} /> Checking for example questions...</p>
{/if}

<style>
  .l-questions {
    display: flex;
    flex-direction: column;
    text-align: left;
    gap: var(--ds-spacing-3);
  }
</style>
