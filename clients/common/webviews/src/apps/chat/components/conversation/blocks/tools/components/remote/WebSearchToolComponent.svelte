<script lang="ts">
  import BaseToolComponent from "../BaseToolComponent.svelte";
  import GlobeIcon from "$common-webviews/src/design-system/icons/globe.svelte";
  import ToolUseHeader from "../ToolUseHeader.svelte";
  import { stringOrDefault } from "$common-webviews/src/apps/chat/types/tool-use-state";

  export let toolUseInput: Record<string, unknown> = {};
</script>

<BaseToolComponent showToolOutput={false}>
  <ToolUseHeader
    slot="header"
    toolName="Web"
    formattedToolArgs={[stringOrDefault(toolUseInput.query, "")]}
  >
    <GlobeIcon slot="icon" />
  </ToolUseHeader>
</BaseToolComponent>
