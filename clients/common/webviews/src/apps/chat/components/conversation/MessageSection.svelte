<script lang="ts">
  import { type RemoteAgentsModel } from "$common-webviews/src/apps/remote-agent-manager/models/remote-agents-model";
  import { type ChatModel } from "../../models/chat-model";
  import { type OnboardingWorkspaceModel } from "../../models/onboarding-workspace-model";
  import MessageList from "./MessageList.svelte";

  export let chatModel: ChatModel;
  export let onboardingWorkspaceModel: OnboardingWorkspaceModel;
  export let remoteAgentsModel: RemoteAgentsModel;

  $: isCreateRemoteAgentView = $remoteAgentsModel.isActive && !$remoteAgentsModel.currentAgentId;

  $: shouldShowMessageList = !isCreateRemoteAgentView;

  $: conversationModel = $chatModel.currentConversationModel;
  $: remoteAgentId = $remoteAgentsModel.currentAgentId;
  $: isRemoteAgent = $remoteAgentsModel.isActive;
  $: hasRemoteAgentChatHistory =
    isRemoteAgent && $remoteAgentsModel.getCurrentChatHistory()?.length;
  $: hasLogs = isRemoteAgent && $remoteAgentsModel.agentSetupLogs?.steps.length !== 0;
  $: isEmptyConversation = isRemoteAgent
    ? !hasRemoteAgentChatHistory && !hasLogs
    : $conversationModel.chatHistory.length === 0;
  $: key = `${$conversationModel.id}-${isRemoteAgent ? remoteAgentId : ""}`;
</script>

{#if shouldShowMessageList}
  {#key key}
    {#if isEmptyConversation}
      <div class="l-center-contents" data-testid="l-lazy-message-list-center-contents"></div>
    {:else}
      <MessageList {chatModel} {onboardingWorkspaceModel} />
    {/if}
  {/key}
{/if}

<style>
  .l-center-contents {
    max-width: 100%;
    overflow-x: hidden;
    overflow-y: auto;
    /* Center the content */
    display: flex;
    justify-content: center;
    align-items: center;
    flex: 1;
  }
</style>
