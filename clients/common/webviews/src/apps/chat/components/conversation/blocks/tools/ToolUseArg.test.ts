import { render, screen } from "@testing-library/svelte";
import { expect, describe, test, vi, beforeEach } from "vitest";
import userEvent from "@testing-library/user-event";

import ToolUseArg from "./ToolUseArg.svelte";
import type { ChatModel } from "../../../../models/chat-model";

describe("ToolUseArg.svelte", () => {
  const mockHandles = {
    extensionClient: {
      resolvePath: vi.fn(),
      openFile: vi.fn(),
    },
  };
  const mockChatModel = mockHandles as unknown as ChatModel;

  beforeEach(() => {
    vi.clearAllMocks();
  });

  test("renders non-existent file path as plain text", async () => {
    mockHandles.extensionClient.resolvePath.mockResolvedValue(undefined);

    const { container } = render(ToolUseArg, {
      props: { value: "non/existent/file.txt" },
      context: new Map([["chatModel", mockChatModel]]),
    });

    // Wait for the component to render and update
    await vi.waitFor(() => {
      expect(container.textContent).toContain("non/existent/file.txt");
    });

    // Verify no button is present
    expect(screen.queryByTestId("file-link")).toBeNull();
  });

  test("renders existing file path as clickable link", async () => {
    const fileDetails = {
      repoRoot: "",
      pathName: "existing/file.txt",
    };
    mockHandles.extensionClient.resolvePath.mockResolvedValue(fileDetails);

    render(ToolUseArg, {
      props: { value: "existing/file.txt" },
      context: new Map([["chatModel", mockChatModel]]),
    });

    const fileLink = await screen.findByTestId("file-link");
    expect(fileLink).toBeInTheDocument();
    // The Filespan component formats the path as "filename directory"
    expect(fileLink.textContent).toContain("file.txt");
    expect(fileLink.textContent).toContain("existing");

    const user = userEvent.setup();
    await user.click(fileLink);

    expect(mockChatModel.extensionClient.openFile).toHaveBeenCalledWith(fileDetails);
  });

  test("handles error in file path resolution", async () => {
    mockHandles.extensionClient.resolvePath.mockRejectedValue(new Error("Failed to resolve"));

    const { container } = render(ToolUseArg, {
      props: { value: "error/file.txt" },
      context: new Map([["chatModel", mockChatModel]]),
    });

    // Wait for the component to render and update
    await vi.waitFor(() => {
      expect(container.textContent).toContain("error/file.txt");
    });

    // Verify no button is present
    expect(screen.queryByTestId("file-link")).toBeNull();
  });
});
