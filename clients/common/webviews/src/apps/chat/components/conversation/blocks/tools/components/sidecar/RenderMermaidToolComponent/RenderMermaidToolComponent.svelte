<script lang="ts">
  import BaseToolComponent from "../../BaseToolComponent.svelte";
  import ToolUseHeader from "../../ToolUseHeader.svelte";
  import DiagramIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/diagram-nested.svg?component";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import MermaidBlock from "$common-webviews/src/apps/chat/components/markdown-ext/MermaidBlock.svelte";
  import {
    ToolUsePhase,
    type ToolUseState,
  } from "$common-webviews/src/apps/chat/types/tool-use-state";
  import type { Tokens } from "marked";

  export let toolUseInput: Record<string, unknown>;
  export let toolUseState: ToolUseState;
  let collapsed = false; // Start expanded by default

  // Parse the Mermaid diagram data from the tool response
  let diagramTitle = "Mermaid Diagram";
  let mermaidToken: Tokens.Code | undefined = undefined;

  $: {
    if (toolUseInput.diagram_definition) {
      const text = toolUseInput.diagram_definition + "";
      // Create a token that Mermaid<PERSON><PERSON> can use
      mermaidToken = {
        type: "code",
        lang: "mermaid",
        text,
        raw: "```mermaid\n" + text + "\n```",
      };

      // Set the title
      diagramTitle = (toolUseInput.title as string) || "Mermaid Diagram";
    }
  }
</script>

<BaseToolComponent showToolOutput={true} bind:collapsed>
  <ToolUseHeader slot="header">
    <DiagramIcon slot="icon" />
    <span slot="toolName">Render Mermaid</span>
    <span slot="secondary">{diagramTitle}</span>
  </ToolUseHeader>

  <div slot="details" class="c-mermaid-tool">
    {#if toolUseState.phase === ToolUsePhase.completed && mermaidToken}
      <div class="c-mermaid-wrapper">
        <MermaidBlock token={mermaidToken} codeblockMetadata={undefined} />
      </div>
    {:else if toolUseState.phase === ToolUsePhase.running}
      <div class="c-mermaid-loading">
        <TextAugment>Rendering Mermaid diagram...</TextAugment>
      </div>
    {:else if toolUseState.phase === ToolUsePhase.error}
      <div class="c-mermaid-error">
        <TextAugment color="error">
          Error rendering Mermaid diagram: {toolUseState.result?.text || "Unknown error"}
        </TextAugment>
      </div>
    {/if}
  </div>
</BaseToolComponent>

<style>
  .c-mermaid-tool {
    padding: var(--ds-spacing-3);
  }

  .c-mermaid-wrapper {
    /* Remove any default padding/margin from MermaidBlock */
    margin: 0;
    padding: 0;
  }

  /* Override MermaidBlock styles to remove extra padding */
  .c-mermaid-wrapper :global(.c-mermaid-block) {
    padding: 0;
  }

  .c-mermaid-loading,
  .c-mermaid-error {
    padding: var(--ds-spacing-4);
    text-align: center;
  }
</style>
