<script lang="ts">
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import {
    type ToolUseState,
    ToolUsePhase,
  } from "$common-webviews/src/apps/chat/types/tool-use-state";
  import ToolUseArg from "./ToolUseArg.svelte";
  import CardAugment from "$common-webviews/src/design-system/components/CardAugment.svelte";
  import {
    ToolResponseContentNodeType,
    type ToolResponseImageContent,
  } from "@augment-internal/sidecar-libs/src/tools/tool-types";

  export let toolUseInput: Record<string, unknown>;
  export let toolUseState: ToolUseState;
  export let showOutput: boolean = true;

  // Format the input arguments for display
  let inputArgs: Record<string, string[]> = {};
  $: inputArgs = Object.entries(toolUseInput).reduce(
    (acc, [key, value]) => {
      if (Array.isArray(value)) {
        acc[key] = value.map((v) => JSON.stringify(v));
      } else {
        acc[key] = [JSON.stringify(value)];
      }
      return acc;
    },
    {} as Record<string, string[]>,
  );

  // Check if the tool result contains content nodes (modern approach)
  $: hasContentNodes =
    toolUseState?.result?.contentNodes && toolUseState.result.contentNodes.length > 0;

  // Helper function to get image source from a node
  function getImageSource(imageContent: ToolResponseImageContent): string | null {
    if (imageContent.image_data) {
      // Use the media type directly from the image content
      return `data:${imageContent.media_type};base64,${imageContent.image_data}`;
    }
    return null;
  }
</script>

<div class="c-tooluse__details">
  {#each Object.entries(inputArgs) as [key, values]}
    <div class="c-tooluse__details__heading">
      <TextAugment size={1} weight="medium">
        {key}
      </TextAugment>
    </div>
    {#each values as value}
      <div class="c-tooluse__value">
        <ToolUseArg {value} />
      </div>
    {/each}
  {/each}
  {#if showOutput && (toolUseState.phase === ToolUsePhase.completed || toolUseState.phase === ToolUsePhase.error)}
    <div class="tool-output">
      <div class="c-tooluse__details__heading">
        <TextAugment size={1} weight="medium">output</TextAugment>
      </div>

      {#if hasContentNodes && toolUseState.result?.contentNodes}
        <!-- Display structured content with content nodes -->
        <div class="c-tooluse__structured-content">
          {#each toolUseState.result.contentNodes as contentNode}
            {#if contentNode.type === ToolResponseContentNodeType.ContentText && contentNode.text_content}
              <!-- Display text content -->
              <TextAugment size={0} type="monospace">
                <pre class="c-tooluse__value-monospace">{contentNode.text_content.trim()}</pre>
              </TextAugment>
            {:else if contentNode.type === ToolResponseContentNodeType.ContentImage && contentNode.image_content}
              <!-- Display image content -->
              {#if getImageSource(contentNode.image_content)}
                <div class="c-tooluse__image-container">
                  <CardAugment insetContent size={1} class="c-image-card">
                    <img
                      src={getImageSource(contentNode.image_content)}
                      alt="Tool result"
                      class="c-tooluse__image"
                    />
                  </CardAugment>
                </div>
              {:else}
                <TextAugment size={1} color="error">
                  Failed to load image: Image data not found
                </TextAugment>
              {/if}
            {/if}
          {/each}
        </div>
      {:else}
        <TextAugment size={1} type="monospace">
          <pre class="c-tooluse__value-monospace">{toolUseState.result?.text?.trim()}</pre>
        </TextAugment>
      {/if}
    </div>
  {/if}
</div>

<style>
  .c-tooluse__details {
    padding: var(--ds-spacing-2);
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-2);
    border: 0;
    border-radius: var(--ds-radius-1);
  }

  .c-tooluse__image {
    margin-top: var(--ds-spacing-2);
  }

  .c-tooluse__details__heading {
    opacity: 50%;
    text-transform: uppercase;
  }

  .c-tooluse__structured-content {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-3);
  }

  .tool-output pre {
    white-space: pre-wrap;
    word-break: break-all;
  }

  .c-tooluse__value {
    white-space: pre-wrap;
    word-break: break-word;
  }

  .c-tooluse__image-container {
    max-width: 100%;
    margin-top: var(--ds-spacing-1);
  }

  .c-tooluse__image {
    max-width: 100%;
    height: auto;
    display: block;
  }
</style>
