import ShellToolComponent from "./ShellToolComponent.svelte";
import StrReplaceEditorToolComponent from "./StrReplaceEditorToolComponent/StrReplaceEditorToolComponent.svelte";
import WebFetchToolComponent from "./WebFetchToolComponent.svelte";
import OpenInBrowserToolComponent from "./OpenInBrowserToolComponent.svelte";
import CodebaseRetrievalToolComponent from "./CodebaseRetrievalToolComponent.svelte";
import RemoveFilesToolComponent from "./RemoveFilesToolComponent.svelte";
import ViewToolComponent from "./ViewToolComponent/ViewToolComponent.svelte";
import { RenderMermaidToolComponent } from "./RenderMermaidToolComponent";
import { SidecarToolType } from "@augment-internal/sidecar-libs/src/tools/sidecar-tools/sidecar-tool-types";
import UpdateTaskToolComponent from "./TaskToolComponent/UpdateTaskToolComponent.svelte";
import ViewTaskListToolComponent from "./TaskToolComponent/ViewTaskListToolComponent.svelte";
import ReorganizeTaskListToolComponent from "./TaskToolComponent/ReorganizeTaskListToolComponent.svelte";
import AddTasksToolComponent from "./TaskToolComponent/AddTasksToolComponent.svelte";
import GrepSearchToolComponent from "./GrepSearchToolComponent.svelte";
import ViewRangeUntruncatedToolComponent from "./ViewRangeUntruncatedToolComponent.svelte";
import SearchUntruncatedToolComponent from "./SearchUntruncatedToolComponent.svelte";

// Map of tool types to their components
export const sidecarToolComponents = {
  [SidecarToolType.shell]: ShellToolComponent,
  [SidecarToolType.webFetch]: WebFetchToolComponent,
  [SidecarToolType.strReplaceEditor]: StrReplaceEditorToolComponent,
  [SidecarToolType.codebaseRetrieval]: CodebaseRetrievalToolComponent,
  [SidecarToolType.removeFiles]: RemoveFilesToolComponent,
  [SidecarToolType.view]: ViewToolComponent,
  [SidecarToolType.viewTaskList]: ViewTaskListToolComponent,
  [SidecarToolType.reorganizeTaskList]: ReorganizeTaskListToolComponent,
  [SidecarToolType.updateTasks]: UpdateTaskToolComponent,
  [SidecarToolType.addTasks]: AddTasksToolComponent,
  [SidecarToolType.renderMermaid]: RenderMermaidToolComponent,
  [SidecarToolType.grepSearch]: GrepSearchToolComponent,
  [SidecarToolType.viewRangeUntruncated]: ViewRangeUntruncatedToolComponent,
  [SidecarToolType.searchUntruncated]: SearchUntruncatedToolComponent,
} as const;

/**
 * Returns the appropriate tool component for a given tool type.
 * @param toolName SidecarToolType
 * @returns
 */
export function resolveSidecarTool(toolName: SidecarToolType) {
  return toolName in sidecarToolComponents
    ? sidecarToolComponents[toolName as keyof typeof sidecarToolComponents]
    : undefined;
}

export {
  ShellToolComponent,
  WebFetchToolComponent,
  OpenInBrowserToolComponent,
  CodebaseRetrievalToolComponent,
  StrReplaceEditorToolComponent,
  RemoveFilesToolComponent,
  ViewToolComponent,
  ViewTaskListToolComponent,
  UpdateTaskToolComponent,
  ReorganizeTaskListToolComponent,
  AddTasksToolComponent,
  RenderMermaidToolComponent,
  GrepSearchToolComponent,
  ViewRangeUntruncatedToolComponent,
  SearchUntruncatedToolComponent,
};
