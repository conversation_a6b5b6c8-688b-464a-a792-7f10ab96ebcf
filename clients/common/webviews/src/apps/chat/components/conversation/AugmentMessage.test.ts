import { render } from "@testing-library/svelte";
import { expect, describe, test, vi, beforeEach, afterEach } from "vitest";

import AugmentMessage from "./AugmentMessage.svelte";
import { ChatModel } from "../../models/chat-model";
import { SpecialContextInputModel } from "../../models/context-model";
import { AsyncMsgSender } from "$common-webviews/src/common/hosts/async-messaging";
import { HostClientType, type HostInterface } from "$common-webviews/src/common/hosts/host-types";
import { ChatResultNodeType } from "@augment-internal/sidecar-libs/src/chat/chat-types";
import { ExchangeStatus } from "../../types/chat-message";
import { ChatMetricName } from "$vscode/src/metrics/types";

describe("AugmentMessage.svelte", () => {
  let testKit: ChatModelTestKit;

  beforeEach(async () => {
    testKit = new ChatModelTestKit();
  });

  afterEach(() => {
    testKit.reset();
    vi.clearAllMocks();
  });

  test("renders augment message correctly", async () => {
    const component = testKit.createComponent({
      markdown: "Example message",
    });
    expect(component.getByText("Example message")).toBeInTheDocument();
  });

  test("renders augment message with follow up questions", async () => {
    const props = {
      markdown: "Example message",
      isLastTurn: true,
      turn: {
        status: ExchangeStatus.success,
        /* eslint-disable @typescript-eslint/naming-convention */
        request_message: "Hello",
        request_id: "1234",
        structured_output_nodes: [
          {
            id: 0,
            type: ChatResultNodeType.SUGGESTED_QUESTIONS,
            content: "This is a sample question\nThis is another question",
          },
        ],
        /* eslint-enable @typescript-eslint/naming-convention */
      },
    };

    const metricsSpy = vi.spyOn(testKit.chatModel.extensionClient, "reportWebviewClientEvent");

    const component = testKit.createComponent(props);
    expect(component.getByText("Example message")).toBeInTheDocument();
    expect(component.getByText("This is a sample question")).toBeInTheDocument();
    expect(component.getByText("This is another question")).toBeInTheDocument();
    expect(metricsSpy).toHaveBeenCalledTimes(1);
    expect(metricsSpy).toHaveBeenCalledWith(ChatMetricName.chatDisplaySuggestedQuestions);

    // Ensure metrics are only reported once
    await component.rerender(props);
    expect(component.getByText("Example message")).toBeInTheDocument();
    expect(component.getByText("This is a sample question")).toBeInTheDocument();
    expect(component.getByText("This is another question")).toBeInTheDocument();
    expect(metricsSpy).toHaveBeenCalledTimes(1);
    expect(metricsSpy).toHaveBeenCalledWith(ChatMetricName.chatDisplaySuggestedQuestions);

    // Ensure the component is reactive
    await component.rerender({
      ...props,
      isLastTurn: false,
    });
    expect(component.getByText("Example message")).toBeInTheDocument();
    expect(component.queryByText("This is a sample question")).not.toBeInTheDocument();
    expect(component.queryByText("This is another question")).not.toBeInTheDocument();
  });
});

class ChatModelTestKit {
  host: HostInterface;
  contextModel: SpecialContextInputModel;
  chatModel: ChatModel;
  uuidCounter: number;

  private components: ReturnType<typeof render<AugmentMessage>>[] = [];

  constructor() {
    this.host = {
      clientType: HostClientType.vscode,
      postMessage: vi.fn(),
      getState: vi.fn(),
      setState: vi.fn(),
    };
    this.contextModel = new SpecialContextInputModel();
    this.uuidCounter = 0;

    vi.spyOn(crypto, "randomUUID").mockImplementation(() => {
      return this.mockUUID(this.uuidCounter++);
    });

    this.chatModel = new ChatModel(new AsyncMsgSender(vi.fn()), this.host, this.contextModel, {
      initialFlags: { fullFeatured: true, enableEditableHistory: true },
    });
  }

  mockUUID(id: number | string): `${string}-${string}-${string}-${string}-${string}` {
    return `mocked-uuid-a-b-${id}`;
  }

  reset() {
    this.uuidCounter = 0;
    for (const component of this.components) {
      component.unmount();
    }
    vi.clearAllMocks();
  }

  createComponent(props: Partial<AugmentMessage>): ReturnType<typeof render<AugmentMessage>> {
    const component = render(AugmentMessage, {
      props: {
        chatModel: this.chatModel,
        markdown: "",
        turn: undefined,
        requestId: "123",
        isLastTurn: true,
        ...props,
      },
    });
    this.components.push(component);
    return component;
  }
}
