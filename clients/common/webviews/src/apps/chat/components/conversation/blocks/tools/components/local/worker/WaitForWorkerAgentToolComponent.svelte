<script lang="ts">
  import BaseToolComponent from "../../BaseToolComponent.svelte";
  import ToolUseHeader from "../../ToolUseHeader.svelte";
  import ClockIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/clock.svg?component";
  import {
    ToolUsePhase,
    type ToolUseState,
  } from "$common-webviews/src/apps/chat/types/tool-use-state";
  import ShellError from "../../sidecar/ShellError.svelte";
  import StatusBadgeAugment from "$common-webviews/src/design-system/components/StatusBadgeAugment.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import CardAugment from "$common-webviews/src/design-system/components/CardAugment.svelte";

  export let toolUseInput: Record<string, unknown> = {};
  export let toolUseState: ToolUseState;
  let collapsed = true;

  // Extract wait operations array from input
  $: waitOperations = Array.isArray(toolUseInput.wait_operations)
    ? toolUseInput.wait_operations
    : [];

  // Format tool arguments for display - show count of wait operations in collapsed state
  $: formattedArgs = (() => {
    const args: string[] = [];
    if (waitOperations.length > 0) {
      args.push(`${waitOperations.length} agent${waitOperations.length === 1 ? "" : "s"}`);
    }
    return args;
  })();

  // Parse the tool result to get wait results
  $: waitResults = (() => {
    if (
      toolUseState.phase === ToolUsePhase.completed &&
      toolUseState.result?.text &&
      !toolUseState.result.isError
    ) {
      try {
        const response = JSON.parse(toolUseState.result.text);
        // Combine successful and failed waits into a single array
        const successful = Array.isArray(response.waitResults) ? response.waitResults : [];
        const failed = Array.isArray(response.failedWaits) ? response.failedWaits : [];
        return [...successful, ...failed];
      } catch (e) {
        console.error("Failed to parse wait for worker agent tool response:", e);
      }
    }
    return [];
  })();

  // Count successful and failed waits
  $: successCount = waitResults.filter((result: any) => result.success).length;
  $: failureCount = waitResults.filter((result: any) => !result.success).length;
  $: hasResults = waitResults.length > 0;

  // Helper function to get status color
  function getStatusColor(status: string): "success" | "info" | "warning" | "error" | "neutral" {
    switch (status?.toLowerCase()) {
      case "idle":
        return "success";
      case "failed":
        return "error";
      case "running":
      case "active":
        return "info";
      default:
        return "neutral";
    }
  }

  // Helper function to format wait time
  function formatWaitTime(seconds: number): string {
    if (seconds < 60) {
      return `${seconds}s`;
    } else if (seconds < 3600) {
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = seconds % 60;
      return remainingSeconds > 0 ? `${minutes}m ${remainingSeconds}s` : `${minutes}m`;
    } else {
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      return minutes > 0 ? `${hours}h ${minutes}m` : `${hours}h`;
    }
  }
</script>

<BaseToolComponent bind:collapsed>
  <ToolUseHeader slot="header" toolName="Wait for Worker Agents" formattedToolArgs={formattedArgs}>
    <ClockIcon slot="icon" />
    <svelte:fragment slot="secondary">
      {#if hasResults && collapsed}
        <div class="c-wait-worker-tool__counts">
          {#if successCount > 0}
            <StatusBadgeAugment color="success" size={1}>
              {successCount} completed
            </StatusBadgeAugment>
          {/if}
          {#if failureCount > 0}
            <StatusBadgeAugment color="error" size={1}>
              {failureCount} failed
            </StatusBadgeAugment>
          {/if}
        </div>
      {/if}
    </svelte:fragment>
  </ToolUseHeader>

  <div slot="details" class="c-wait-worker-tool__details">
    {#if toolUseState.phase === ToolUsePhase.running}
      <div class="c-wait-worker-tool__running">
        <TextAugment size={1} color="neutral">Waiting for worker agents...</TextAugment>
      </div>
    {:else if toolUseState.phase === ToolUsePhase.completed && hasResults}
      <div class="c-wait-worker-tool__results">
        {#each waitResults as result}
          {@const operation = waitOperations[result.index] || {}}
          <CardAugment class="c-wait-worker-tool__wait-card">
            <div class="c-wait-worker-tool__wait-header">
              <div class="c-wait-worker-tool__wait-title">
                <TextAugment size={1} weight="medium">
                  Wait {result.index + 1} - Agent {result.agentId}
                </TextAugment>
                <StatusBadgeAugment color={result.success ? "success" : "error"} size={1}>
                  {result.success ? "Completed" : "Failed"}
                </StatusBadgeAugment>
              </div>
            </div>

            <div class="c-wait-worker-tool__wait-details">
              {#if result.success}
                <div class="c-wait-worker-tool__success-details">
                  <div class="c-wait-worker-tool__status-row">
                    <TextAugment size={1} color="neutral">
                      <strong>Target Status:</strong>
                    </TextAugment>
                    <StatusBadgeAugment color={getStatusColor(result.targetStatus)} size={1}>
                      {result.targetStatus}
                    </StatusBadgeAugment>
                  </div>

                  <div class="c-wait-worker-tool__status-row">
                    <TextAugment size={1} color="neutral">
                      <strong>Final Status:</strong>
                    </TextAugment>
                    <StatusBadgeAugment color={getStatusColor(result.status)} size={1}>
                      {result.status}
                    </StatusBadgeAugment>
                  </div>

                  {#if result.waitedSeconds !== undefined}
                    <TextAugment size={1} color="neutral">
                      <strong>Wait Time:</strong>
                      {formatWaitTime(result.waitedSeconds)}
                    </TextAugment>
                  {/if}
                </div>
              {:else}
                <div class="c-wait-worker-tool__error-details">
                  <TextAugment size={1} color="error">
                    <strong>Error:</strong>
                    {result.error || "Unknown error"}
                  </TextAugment>
                  {#if operation.worker_agent_id}
                    <TextAugment size={1} color="neutral">
                      <strong>Agent ID:</strong>
                      {operation.worker_agent_id}
                    </TextAugment>
                  {/if}
                  {#if operation.target_status}
                    <TextAugment size={1} color="neutral">
                      <strong>Target Status:</strong>
                      {operation.target_status}
                    </TextAugment>
                  {/if}
                  {#if operation.timeout_seconds}
                    <TextAugment size={1} color="neutral">
                      <strong>Timeout:</strong>
                      {formatWaitTime(operation.timeout_seconds)}
                    </TextAugment>
                  {/if}
                </div>
              {/if}
            </div>
          </CardAugment>
        {/each}
      </div>
    {:else if toolUseState.phase === ToolUsePhase.completed && !hasResults}
      <div class="c-wait-worker-tool__no-results">
        <TextAugment size={1} color="neutral">No wait operations were completed.</TextAugment>
      </div>
    {/if}
  </div>

  {#if toolUseState.phase === ToolUsePhase.error}
    <ShellError slot="error" {...toolUseState.result ?? {}} />
  {/if}
</BaseToolComponent>

<style>
  .c-wait-worker-tool__counts {
    display: flex;
    gap: var(--ds-spacing-2);
    align-items: center;
    flex-wrap: wrap;
  }

  .c-wait-worker-tool__details {
    padding: var(--ds-spacing-2);
  }

  .c-wait-worker-tool__results {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-2);
  }

  .c-wait-worker-tool__wait-header {
    margin-bottom: var(--ds-spacing-2);
  }

  .c-wait-worker-tool__wait-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: var(--ds-spacing-2);
  }

  .c-wait-worker-tool__wait-details {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-2);
  }

  .c-wait-worker-tool__status-row {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-2);
  }
</style>
