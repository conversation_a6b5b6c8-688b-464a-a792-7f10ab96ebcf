import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import {
  base64ToFile,
  scaleImageData,
  processImageContentNodes,
  processToolResultImages,
} from "./image-scaling-util";
import { ToolResponseContentNodeType } from "@augment-internal/sidecar-libs/src/tools/tool-types";
import { readImageAsBlob } from "$common-webviews/src/design-system/components/RichTextEditorAugment/plugins/File/read-image-util";

// Mock the readImageAsBlob function
vi.mock(
  "$common-webviews/src/design-system/components/RichTextEditorAugment/plugins/File/read-image-util",
  () => ({
    readImageAsBlob: vi.fn(),
  }),
);

describe("image-scaling-util", () => {
  // Sample base64 data URL for testing
  const sampleDataUrl =
    "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8z8BQDwAEhQGAhKmMIQAAAABJRU5ErkJggg==";

  // Mock for FileReader
  let fileReaderMock: any;

  beforeEach(() => {
    // Reset mocks before each test
    vi.resetAllMocks();

    // Mock FileReader
    fileReaderMock = {
      readAsDataURL: vi.fn(),
      onload: null,
      onerror: null,
      result: null,
    };

    global.FileReader = vi.fn(() => fileReaderMock) as any;
    global.Blob = vi.fn((content, options) => ({ content, options })) as any;

    // Mock readImageAsBlob to return a Blob
    vi.mocked(readImageAsBlob).mockResolvedValue(new Blob(["mocked-blob"], { type: "image/png" }));
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe("base64ToFile", () => {
    it("should convert base64 data to a File object", () => {
      // Extract the base64 data from the data URL
      const base64Data = sampleDataUrl.split(",")[1];
      const mimeType = "image/png";

      const result = base64ToFile(base64Data, "test.png", mimeType);

      expect(result).toBeInstanceOf(File);
      expect(result.name).toBe("test.png");
      expect(result.type).toBe("image/png");
    });
  });

  describe("scaleImageData", () => {
    it("should scale an image and return a scaled image result", async () => {
      // Setup FileReader mock to simulate successful read
      fileReaderMock.result = "data:image/png;base64,scaledImageData";
      fileReaderMock.readAsDataURL = vi.fn().mockImplementation(() => {
        setTimeout(() => {
          if (fileReaderMock.onloadend) {
            fileReaderMock.onloadend();
          }
        }, 0);
      });

      // Extract the base64 data from the data URL
      const base64Data = sampleDataUrl.split(",")[1];
      const mimeType = "image/png";

      const result = await scaleImageData(base64Data, "test.png", mimeType);

      expect(readImageAsBlob).toHaveBeenCalled();
      expect(fileReaderMock.readAsDataURL).toHaveBeenCalled();
      expect(result.dataUrl).toBe("scaledImageData");
      expect(result.mimeType).toBe("image/png");
    });

    it("should return the original data if scaling fails", async () => {
      // Setup readImageAsBlob to throw an error
      vi.mocked(readImageAsBlob).mockRejectedValue(new Error("Scaling failed"));

      // Extract the base64 data from the data URL
      const base64Data = sampleDataUrl.split(",")[1];
      const mimeType = "image/png";

      const result = await scaleImageData(base64Data, "test.png", mimeType);

      expect(result.dataUrl).toBe(base64Data);
      expect(result.mimeType).toBe(mimeType);
    });

    it("should handle FileReader errors", async () => {
      // Setup FileReader mock to simulate an error
      fileReaderMock.readAsDataURL = vi.fn().mockImplementation(() => {
        setTimeout(() => {
          if (fileReaderMock.onerror) {
            // Create a mock error event instead of passing an Error object directly
            const errorEvent = { type: "error", target: fileReaderMock };
            fileReaderMock.onerror(errorEvent);
          }
        }, 0);
      });

      // Extract the base64 data from the data URL
      const base64Data = sampleDataUrl.split(",")[1];
      const mimeType = "image/png";

      try {
        await scaleImageData(base64Data, "test.png", mimeType);
        // If we get here, the test should fail because we expect an error
        expect(true).toBe(false); // This will fail if no error is thrown
      } catch (error) {
        // We expect an error to be thrown
        expect((error as Error).message).toBe("FileReader error");
      }
    });
  });

  describe("processImageContentNodes", () => {
    it("should process image content nodes and scale them", async () => {
      // Setup FileReader mock to simulate successful read
      fileReaderMock.result = "data:image/png;base64,scaledImageData";
      fileReaderMock.readAsDataURL = vi.fn().mockImplementation(() => {
        setTimeout(() => {
          if (fileReaderMock.onloadend) {
            fileReaderMock.onloadend();
          }
        }, 0);
      });

      // Mock base64ToFile to avoid atob error
      vi.spyOn(global, "atob").mockImplementation(() => "mocked-binary-data");

      const contentNodes = [
        {
          /* eslint-disable @typescript-eslint/naming-convention */
          type: ToolResponseContentNodeType.ContentImage,
          image_content: {
            image_data: sampleDataUrl.split(",")[1], // Just the base64 part
            media_type: "image/png",
          },
          /* eslint-enable @typescript-eslint/naming-convention */
        },
        {
          /* eslint-disable @typescript-eslint/naming-convention */
          type: ToolResponseContentNodeType.ContentText,
          text_content: "This is a text node",
          /* eslint-enable @typescript-eslint/naming-convention */
        },
      ];

      const result = await processImageContentNodes(contentNodes);

      expect(result.length).toBe(2);
      expect(result[0].type).toBe(ToolResponseContentNodeType.ContentImage);
      expect(result[0].image_content?.image_data).toBe("scaledImageData");
      expect(result[0].image_content?.media_type).toBe("image/png");
      expect(result[1].type).toBe(ToolResponseContentNodeType.ContentText);
      expect(result[1].text_content).toBe("This is a text node");
    });

    it("should skip image nodes when enableImageToolResults is false", async () => {
      const contentNodes = [
        {
          /* eslint-disable @typescript-eslint/naming-convention */
          type: ToolResponseContentNodeType.ContentImage,
          image_content: {
            image_data: sampleDataUrl.split(",")[1], // Just the base64 part
            media_type: "image/png",
          },
          /* eslint-enable @typescript-eslint/naming-convention */
        },
        {
          /* eslint-disable @typescript-eslint/naming-convention */
          type: ToolResponseContentNodeType.ContentText,
          text_content: "This is a text node",
          /* eslint-enable @typescript-eslint/naming-convention */
        },
      ];

      const result = await processImageContentNodes(contentNodes, false);

      expect(result.length).toBe(1);
      expect(result[0].type).toBe(ToolResponseContentNodeType.ContentText);
      expect(result[0].text_content).toBe("This is a text node");
    });

    it("should handle nodes without image_content", async () => {
      const contentNodes = [
        {
          type: ToolResponseContentNodeType.ContentImage,
          // No image_content property
        },
      ];

      const result = await processImageContentNodes(contentNodes);

      expect(result.length).toBe(1);
      expect(result[0]).toEqual(contentNodes[0]);
    });

    it("should handle nodes with image_content but no image_data", async () => {
      const contentNodes = [
        {
          /* eslint-disable @typescript-eslint/naming-convention */
          type: ToolResponseContentNodeType.ContentImage,
          image_content: {
            // No image_data property
            media_type: "image/png",
            image_data: "", // Add empty image_data to satisfy type check
          },
          /* eslint-enable @typescript-eslint/naming-convention */
        },
      ];

      const result = await processImageContentNodes(contentNodes);

      expect(result.length).toBe(1);
      expect(result[0]).toEqual(contentNodes[0]);
    });

    it("should use default media_type if not provided", async () => {
      // Setup FileReader mock to simulate successful read
      fileReaderMock.result = "data:image/png;base64,scaledImageData";
      fileReaderMock.readAsDataURL = vi.fn().mockImplementation(() => {
        setTimeout(() => {
          if (fileReaderMock.onloadend) {
            fileReaderMock.onloadend();
          }
        }, 0);
      });

      // Mock base64ToFile to avoid atob error
      vi.spyOn(global, "atob").mockImplementation(() => "mocked-binary-data");

      const contentNodes = [
        {
          /* eslint-disable @typescript-eslint/naming-convention */
          type: ToolResponseContentNodeType.ContentImage,
          image_content: {
            image_data: sampleDataUrl.split(",")[1], // Just the base64 part
            // No media_type property
            media_type: "", // Add empty media_type to satisfy type check
          },
          /* eslint-enable @typescript-eslint/naming-convention */
        },
      ];

      const result = await processImageContentNodes(contentNodes);

      expect(result.length).toBe(1);
      expect(result[0].type).toBe(ToolResponseContentNodeType.ContentImage);
      expect(result[0].image_content?.image_data).toBe("scaledImageData");
      expect(result[0].image_content?.media_type).toBe("image/png");
    });
  });

  describe("processToolResultImages", () => {
    it("should process images in a tool result", async () => {
      // Setup FileReader mock to simulate successful read
      fileReaderMock.result = "data:image/png;base64,scaledImageData";
      fileReaderMock.readAsDataURL = vi.fn().mockImplementation(() => {
        setTimeout(() => {
          if (fileReaderMock.onloadend) {
            fileReaderMock.onloadend();
          }
        }, 0);
      });

      // Mock base64ToFile to avoid atob error
      vi.spyOn(global, "atob").mockImplementation(() => "mocked-binary-data");

      const toolResponse = {
        text: "Tool response with images",
        isError: false,
        contentNodes: [
          {
            /* eslint-disable @typescript-eslint/naming-convention */
            type: ToolResponseContentNodeType.ContentImage,
            image_content: {
              image_data: sampleDataUrl.split(",")[1], // Just the base64 part
              media_type: "image/png",
            },
            /* eslint-enable @typescript-eslint/naming-convention */
          },
        ],
      };

      const result = await processToolResultImages(toolResponse);

      expect(result.text).toBe("Tool response with images");
      expect(result.isError).toBe(false);
      expect(result.contentNodes?.length).toBe(1);
      expect(result.contentNodes?.[0].type).toBe(ToolResponseContentNodeType.ContentImage);
      expect(result.contentNodes?.[0].image_content?.image_data).toBe("scaledImageData");
      expect(result.contentNodes?.[0].image_content?.media_type).toBe("image/png");
    });

    it("should skip image nodes when enableImageToolResults is false", async () => {
      const toolResponse = {
        text: "Tool response with images and text",
        isError: false,
        contentNodes: [
          {
            /* eslint-disable @typescript-eslint/naming-convention */
            type: ToolResponseContentNodeType.ContentImage,
            image_content: {
              image_data: sampleDataUrl.split(",")[1],
              media_type: "image/png",
            },
            /* eslint-enable @typescript-eslint/naming-convention */
          },
          {
            /* eslint-disable @typescript-eslint/naming-convention */
            type: ToolResponseContentNodeType.ContentText,
            text_content: "This is a text node",
            /* eslint-enable @typescript-eslint/naming-convention */
          },
        ],
      };

      const result = await processToolResultImages(toolResponse, false);

      expect(result.text).toBe("Tool response with images and text");
      expect(result.isError).toBe(false);
      expect(result.contentNodes?.length).toBe(1);
      expect(result.contentNodes?.[0].type).toBe(ToolResponseContentNodeType.ContentText);
      expect(result.contentNodes?.[0].text_content).toBe("This is a text node");
    });

    it("should return the original response if there are no content nodes", async () => {
      const toolResponse = {
        text: "Tool response without images",
        isError: false,
      };

      const result = await processToolResultImages(toolResponse);

      expect(result).toEqual(toolResponse);
    });

    it("should return the original response if content nodes array is empty", async () => {
      const toolResponse = {
        text: "Tool response with empty content nodes",
        isError: false,
        contentNodes: [],
      };

      const result = await processToolResultImages(toolResponse);

      expect(result).toEqual(toolResponse);
    });
  });
});
