<script lang="ts">
  import Floating from "$common-webviews/src/common/components/Floating.svelte";

  // Props for positioning
  export let position: "top" | "bottom" = "bottom";

  // Props for the Floating component
  export let xPos: "left" | "middle" | "right" = "middle";
  export let yPos: "top" | "bottom" = position === "top" ? "top" : "bottom";
</script>

<Floating class="c-chat-floating-container c-chat-floating-container--{position}" {xPos} {yPos}>
  <slot />
</Floating>

<style>
  :global(.c-chat-floating-container) {
    z-index: var(--z-conversation-scroll-btn);
    pointer-events: none;

    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    gap: var(--ds-spacing-1);
  }

  :global(.c-chat-floating-container--top) {
    margin-top: var(--ds-spacing-2);
  }

  :global(.c-chat-floating-container .c-chat-floating-button) {
    pointer-events: auto;
  }

  :global(.c-chat-floating-button.error) {
    color: var(--ds-color-error-11);
    background-color: var(--ds-color-neutral-2);
    border: 1px solid var(--ds-color-neutral-a3);
  }
</style>
