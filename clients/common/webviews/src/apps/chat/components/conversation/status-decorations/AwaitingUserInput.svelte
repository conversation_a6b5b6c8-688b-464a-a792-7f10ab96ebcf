<script lang="ts">
  import Pause from "$common-webviews/src/design-system/icons/pause.svelte";
  import { onMount } from "svelte";

  let ellipses = ".";

  onMount(() => {
    const interval = setInterval(() => {
      ellipses = ellipses.length >= 3 ? "." : ellipses + ".";
    }, 500);

    return () => clearInterval(interval);
  });
</script>

<span class="c-gen-response">
  <Pause />
  Waiting for user input{ellipses}
</span>

<style>
  .c-gen-response {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: var(--ds-spacing-1);
    padding: var(--ds-spacing-1) var(--ds-spacing-2);
    opacity: 50%;
  }
</style>
