export function safeJsonParse(v: string): Record<string, unknown> {
  try {
    return JSON.parse(v) ?? {};
  } catch (e) {
    console.error("Failed to parse tool input JSON", e);
    return {};
  }
}
function formatValue(value: any): string {
  if (value === undefined || value === null) {
    return "";
  }

  // Handle arrays
  if (Array.isArray(value)) {
    return value.map((v) => formatValue(v)).join(", ");
  }

  // Handle objects (including nested objects)
  if (typeof value === "object") {
    // Format object as key-value pairs
    const entries = Object.entries(value);
    if (entries.length === 0) {
      return "{}";
    }

    return entries.map(([k, v]) => `${k}: ${formatValue(v)}`).join(", ");
  }

  return String(value);
}
export function formatInputArgs(records: Record<string, unknown>): string {
  let result: string[] = [];
  for (const value of Object.values(records)) {
    if (Array.isArray(value)) {
      result = result.concat(value.map((v) => formatValue(v)));
    } else {
      result.push(formatValue(value));
    }
  }
  return result.join(" ");
}

export function formatShellCommand(toolUseInput: Record<string, unknown>): string[] {
  let entries: [string, string[]][] = [];
  if (typeof toolUseInput.command_type === "string") {
    if (
      toolUseInput.command_type === "simple" &&
      Array.isArray(toolUseInput.simple_command) &&
      toolUseInput.simple_command.length > 0
    ) {
      entries = [
        ["command", [toolUseInput.simple_command[0]]],

        ["args", toolUseInput.simple_command.slice(1)],
      ];
    } else if (
      toolUseInput.command_type === "complex" &&
      typeof toolUseInput.complex_command === "string"
    ) {
      entries = [["command", [toolUseInput.complex_command]]];
    }
  } else if (typeof toolUseInput.command === "string") {
    entries = [["command", [toolUseInput.command]]];
  }
  return entries.flatMap(([, values]) => values);
}
export function formatShellCommandLine(toolUseInput: Record<string, unknown>): string {
  if (typeof toolUseInput.command_type === "string") {
    if (
      toolUseInput.command_type === "simple" &&
      Array.isArray(toolUseInput.simple_command) &&
      toolUseInput.simple_command.length > 0
    ) {
      return toolUseInput.simple_command.join(" ");
    } else if (
      toolUseInput.command_type === "complex" &&
      typeof toolUseInput.complex_command === "string"
    ) {
      return toolUseInput.complex_command;
    }
  } else if (typeof toolUseInput.command === "string") {
    return toolUseInput.command;
  }
  return "";
}
