<script lang="ts">
  import BadgeAugment from "$common-webviews/src/design-system/components/BadgeAugment";
  import SpinnerAugment from "$common-webviews/src/design-system/components/SpinnerAugment.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import { onMount } from "svelte";

  export let timeToTimerMs = 5_000;

  let timerSeconds = 0;

  let timeToTimerMsId: ReturnType<typeof setTimeout> | undefined = undefined;
  let timerId: ReturnType<typeof setTimeout> | undefined = undefined;
  let time: number = Date.now();
  let showTimer = false;

  function onTimeTorMsChange() {
    timeToTimerMsId = setTimeout(startTimer, timeToTimerMs);
    time = Date.now();
    return () => {
      timerSeconds = 0;
      showTimer = false;
      clearTimeout(timeToTimerMsId);
      clearInterval(timerId);
    };
  }
  function setTime() {
    timerSeconds = Math.floor((Date.now() - time) / 1000);
  }
  function startTimer() {
    showTimer = true;
    //prevents 0s from showing.
    setTime();
    timerId = setInterval(setTime, 1000);
  }
  function formatSeconds(seconds: number) {
    if (seconds >= 60) {
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = seconds % 60;
      return `${minutes}:${String(remainingSeconds).padStart(2, "0")}`;
    }
    return `0:${String(seconds).padStart(2, "0")}`;
  }
  $: displayTime = formatSeconds(timerSeconds);
  onMount(onTimeTorMsChange);
</script>

<div class="c-gen-response">
  <span class="c-gen-response__text">
    <SpinnerAugment size={1} />
    <TextAugment size={1} weight="light" color="secondary">Generating response...</TextAugment>
  </span>
  {#if showTimer}
    <BadgeAugment.Root color="neutral" size={1}>
      <TextAugment type="monospace" size={1} weight="light" color="secondary">
        <span class="c-gen-response__timer" class:is_minutes={timerSeconds >= 60}
          >{displayTime}</span
        >
      </TextAugment>
    </BadgeAugment.Root>
  {/if}
</div>

<style>
  .c-gen-response {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    gap: var(--ds-spacing-1);
    padding: var(--ds-spacing-1) var(--ds-spacing-2);
    width: 100%;
  }
  .c-gen-response__text {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: var(--ds-spacing-1);
    line-height: 1;
  }
  .c-gen-response__timer {
    line-height: 1;
    min-width: 3.5ch;
    text-align: left;
  }
</style>
