<script lang="ts">
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import StatusBadgeAugment from "$common-webviews/src/design-system/components/StatusBadgeAugment.svelte";
  import CollapsibleContent from "./CollapsibleContent.svelte";

  export let data: Record<string, any>;
  export let fields: FieldConfig[];

  interface FieldConfig {
    key: string;
    label: string;
    type?: "text" | "monospace" | "status" | "custom";
    collapsible?: boolean;
    color?: "accent" | "neutral" | "success" | "error" | "primary" | "secondary";
    statusColor?: "success" | "error" | "warning" | "info" | "neutral";
    condition?: (data: any) => boolean;
    transform?: (value: any) => string;
    maxHeight?: string;
  }

  // Filter fields based on conditions and data availability
  $: visibleFields = fields.filter((field) => {
    const value = getNestedValue(data, field.key);
    if (!value && value !== 0 && value !== false) return false;
    if (field.condition && !field.condition(data)) return false;
    return true;
  });

  function getNestedValue(obj: any, path: string): any {
    return path.split(".").reduce((current, key) => current?.[key], obj);
  }

  function getDisplayValue(field: FieldConfig): string {
    const value = getNestedValue(data, field.key);
    if (field.transform) {
      return field.transform(value);
    }
    return String(value || "");
  }
</script>

<div class="c-tool-result-fields">
  {#each visibleFields as field}
    {@const value = getDisplayValue(field)}

    {#if field.collapsible}
      <CollapsibleContent label={field.label} maxHeight={field.maxHeight || "300px"}>
        {#if field.type === "monospace"}
          <TextAugment size={1} type="monospace" color={field.color || "neutral"}>
            {value}
          </TextAugment>
        {:else}
          <TextAugment size={1} color={field.color || "neutral"}>
            {value}
          </TextAugment>
        {/if}
      </CollapsibleContent>
    {:else}
      <div class="c-tool-result-fields__field">
        {#if field.type === "status"}
          <div class="c-tool-result-fields__status-row">
            <TextAugment size={1} color="neutral">
              <strong>{field.label}:</strong>
            </TextAugment>
            <StatusBadgeAugment color={field.statusColor || "neutral"} size={1}>
              {value}
            </StatusBadgeAugment>
          </div>
        {:else if field.type === "monospace"}
          <TextAugment size={1} color="neutral">
            <strong>{field.label}:</strong>
          </TextAugment>
          <TextAugment size={1} type="monospace" color={field.color || "neutral"}>
            {value}
          </TextAugment>
        {:else}
          <TextAugment size={1} color={field.color || "neutral"}>
            <strong>{field.label}:</strong>
            {value}
          </TextAugment>
        {/if}
      </div>
    {/if}
  {/each}
</div>

<style>
  .c-tool-result-fields {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-2);
  }

  .c-tool-result-fields__field {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-1);
  }

  .c-tool-result-fields__status-row {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-2);
  }
</style>
