/* eslint-disable @typescript-eslint/naming-convention */
import { describe, test, expect } from "vitest";
import {
  ExchangeStatus,
  shouldShowFooter,
  type ExchangeWithStatus,
} from "../../types/chat-message";
import { ChatResultNodeType } from "@augment-internal/sidecar-libs/src/chat/chat-types";
import { TEMP_EXHANGE_ID_PREFIX } from "../../models/conversation-model";

describe("ChatTurn showFooter logic", () => {
  test("showFooter is true when turn is the last non-temp turn in group and is cancelled, even with tool uses", () => {
    const turnWithToolUse: ExchangeWithStatus = {
      request_message: "Test message",
      response_text: "Test response",
      request_id: "test-123",
      status: ExchangeStatus.cancelled,
      structured_output_nodes: [
        {
          id: 1,
          type: ChatResultNodeType.TOOL_USE,
          content: "",
          tool_use: {
            tool_use_id: "tool-1",
            tool_name: "test-tool",
            input_json: "{}",
          },
        },
      ],
    };

    const group = [{ turn: turnWithToolUse, idx: 0 }];
    const showFooter = shouldShowFooter(group, turnWithToolUse);

    // Should be true because it's the last non-temp turn in group and status is cancelled
    expect(showFooter).toBe(true);
  });

  test("showFooter is true when turn is the last non-temp turn in group but has tool uses and is not cancelled", () => {
    const turnWithToolUse: ExchangeWithStatus = {
      request_message: "Test message",
      response_text: "Test response",
      request_id: "test-123",
      status: ExchangeStatus.success,
      structured_output_nodes: [
        {
          id: 1,
          type: ChatResultNodeType.TOOL_USE,
          content: "",
          tool_use: {
            tool_use_id: "tool-1",
            tool_name: "test-tool",
            input_json: "{}",
          },
        },
      ],
    };

    const group = [{ turn: turnWithToolUse, idx: 0 }];
    const showFooter = shouldShowFooter(group, turnWithToolUse);

    // Should be true because it's the last valid turn in a terminal group (tool use doesn't affect footer visibility)
    expect(showFooter).toBe(true);
  });

  test("showFooter is true when turn is the last non-temp turn in group and has no tool uses", () => {
    const turnWithoutToolUse: ExchangeWithStatus = {
      request_message: "Test message",
      response_text: "Test response",
      request_id: "test-123",
      status: ExchangeStatus.success,
      structured_output_nodes: [],
    };

    const group = [{ turn: turnWithoutToolUse, idx: 0 }];
    const showFooter = shouldShowFooter(group, turnWithoutToolUse);

    // Should be true because it's the last non-temp turn in group and !hasToolUse(turn) is true
    expect(showFooter).toBe(true);
  });

  test("showFooter is true when turn is the last non-temp turn in group, is cancelled and has no tool uses", () => {
    const turnCancelledNoTools: ExchangeWithStatus = {
      request_message: "Test message",
      response_text: "Test response",
      request_id: "test-123",
      status: ExchangeStatus.cancelled,
      structured_output_nodes: [],
    };

    const group = [{ turn: turnCancelledNoTools, idx: 0 }];
    const showFooter = shouldShowFooter(group, turnCancelledNoTools);

    // Should be true because it's the last non-temp turn in group and both conditions are true
    expect(showFooter).toBe(true);
  });

  test("showFooter is false when turn is not the last non-temp turn in group", () => {
    const firstTurn: ExchangeWithStatus = {
      request_message: "First message",
      response_text: "First response",
      request_id: "test-123",
      status: ExchangeStatus.success,
      structured_output_nodes: [],
    };

    const lastTurn: ExchangeWithStatus = {
      request_message: "Last message",
      response_text: "Last response",
      request_id: "test-456",
      status: ExchangeStatus.success,
      structured_output_nodes: [],
    };

    const group = [
      { turn: firstTurn, idx: 0 },
      { turn: lastTurn, idx: 1 },
    ];
    const showFooter = shouldShowFooter(group, firstTurn);

    // Should be false because firstTurn is not the last non-temp turn in the group
    expect(showFooter).toBe(false);
  });

  test("showFooter is false when turn has temp ID", () => {
    const tempTurn: ExchangeWithStatus = {
      request_message: "Temp message",
      response_text: "Temp response",
      request_id: `${TEMP_EXHANGE_ID_PREFIX}-123`,
      status: ExchangeStatus.success,
      structured_output_nodes: [],
    };

    const regularTurn: ExchangeWithStatus = {
      request_message: "Regular message",
      response_text: "Regular response",
      request_id: "test-456",
      status: ExchangeStatus.success,
      structured_output_nodes: [],
    };

    const group = [
      { turn: regularTurn, idx: 0 },
      { turn: tempTurn, idx: 1 },
    ];
    const showFooter = shouldShowFooter(group, tempTurn);

    // Should be false because tempTurn has a temp ID, so regularTurn is the last non-temp turn
    expect(showFooter).toBe(false);
  });
});
