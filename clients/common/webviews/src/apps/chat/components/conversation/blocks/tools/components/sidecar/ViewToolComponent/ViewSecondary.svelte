<script lang="ts">
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import { ToolUsePhase } from "$common-webviews/src/apps/chat/types/tool-use-state";
  import { getToolUseContext } from "../../../tool-context";

  const ctx = getToolUseContext();
  export let start: number | undefined = undefined;
  export let stop: number | undefined = undefined;
  export let isDirectory: boolean = false;
  export let searchQueryRegex: string | undefined = undefined;
  export let caseSensitive: boolean = false;
  export let isRulesFile: boolean = false;
</script>

{#if $ctx.toolUseState.phase <= ToolUsePhase.running}
  {#if searchQueryRegex}
    Searching for pattern...
  {:else if isDirectory}
    Reading directory...
  {:else if isRulesFile}
    Reading rules file...
  {:else}
    Reading file...
  {/if}
{:else if isDirectory}
  Read directory
{:else if searchQueryRegex}
  Pattern Search <TextAugment size={1} color="secondary" type="monospace"
    ><code>{searchQueryRegex}</code></TextAugment
  >
  {#if caseSensitive}
    <TextAugment size={1} color="secondary">(case-sensitive)</TextAugment>
  {/if}
{:else if isRulesFile}
  Read rules file
{:else if !(start && stop)}
  Read file
{:else}
  Read lines {start}-{stop}
{/if}

<style>
  code {
    background-color: var(--ds-color-neutral-100);
    padding: 0 var(--ds-spacing-1);
    border-radius: var(--ds-radius-1);
  }
</style>
