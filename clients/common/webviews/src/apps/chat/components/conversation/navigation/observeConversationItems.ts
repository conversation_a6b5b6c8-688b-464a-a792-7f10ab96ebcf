import throttle from "lodash.throttle";
import type { GroupedChatItem } from "../../../utils/message-list-context";
import { getConversationNavigationItemsContext } from "./context";
import type { ConversationNavigationItem, ConversationNavigationItemsStore } from "./context";
import { isScrollNearBottom, scrollToBottom, scrollToY } from "../../../utils/scroll-utils";

export function extractConversationNavigationItems(groupedChatHistory: GroupedChatItem[]) {
  return (
    (groupedChatHistory.map((groups) => groups[0]?.turn.timestamp).filter(Boolean) as string[]) ??
    []
  );
}

export interface ObserveTimestampElementsProps {
  items?: string[];
  conversationNavigationItems: ConversationNavigationItemsStore;
}

interface TimestampElementPosition {
  id: string;
  element: HTMLElement;
  offsetTop: number;
}

/**
 * Tracks timestamp elements within the root element by their offsetTop positions.
 * Updates coordinates on container resize and determines current item based on scroll position.
 *
 * @param container - The root element containing timestamp elements
 * @param props - Configuration props
 * @returns Svelte action object
 */
export function observeConversationItems(
  container: HTMLElement,
  props: ObserveTimestampElementsProps = {
    items: [],
    conversationNavigationItems: getConversationNavigationItemsContext(),
  },
) {
  let currProps: ObserveTimestampElementsProps = props;
  let timestampPositions: TimestampElementPosition[] = [];
  let resizeObserver: ResizeObserver | null = null;

  const { conversationNavigationItems } = props;

  function updateCurrentProps(newProps: ObserveTimestampElementsProps) {
    currProps = newProps;
    conversationNavigationItems.update((items) => ({ ...items, all: newProps.items ?? [] }));
  }

  function updateTimestampPositions() {
    if ((currProps.items?.length ?? 0) <= 1) return;
    const itemsSet = new Set(currProps.items);
    const timestampElements = container.querySelectorAll<HTMLElement>("[data-timestamp-id]");
    timestampPositions = [];

    for (const timestampElement of timestampElements) {
      const timestampId = timestampElement.getAttribute("data-timestamp-id");
      if (timestampId && itemsSet.has(timestampId)) {
        const offsetTop = timestampElement.offsetTop;
        timestampPositions.push({
          id: timestampId,
          element: timestampElement,
          offsetTop,
        });
      }
    }

    timestampPositions.sort((a, b) => a.offsetTop - b.offsetTop);
    updateNavigationItems();
  }

  function updateNavigationItems(scrollTop?: number) {
    if (timestampPositions.length === 0) return;

    const currentScrollTop = scrollTop ?? container.scrollTop;
    // Add buffer to avoid navigating to items that are barely out of view
    const NAVIGATION_BUFFER_PX = 80;

    // Calculate next item: closest item below current scroll position + buffer
    let nextItem: ConversationNavigationItem | undefined;
    let prevItem: ConversationNavigationItem | undefined;
    let nextMinDistance = Infinity;
    let prevMinDistance = Infinity;

    for (const position of timestampPositions) {
      if (position.offsetTop > currentScrollTop + NAVIGATION_BUFFER_PX) {
        const distance = position.offsetTop - currentScrollTop;
        if (distance < nextMinDistance) {
          nextMinDistance = distance;
          nextItem = { id: position.id, offsetTop: position.offsetTop };
        }
      }
      if (position.offsetTop < currentScrollTop - NAVIGATION_BUFFER_PX) {
        const distance = currentScrollTop - position.offsetTop;
        if (distance < prevMinDistance) {
          prevMinDistance = distance;
          prevItem = { id: position.id, offsetTop: position.offsetTop };
        }
      }
    }

    const canScrollToBottom = !isScrollNearBottom(container);

    conversationNavigationItems.update((state) => {
      if (
        state.next?.id === nextItem?.id &&
        state.next?.offsetTop === nextItem?.offsetTop &&
        state.prev?.id === prevItem?.id &&
        state.prev?.offsetTop === prevItem?.offsetTop &&
        state.canScrollToBottom === canScrollToBottom
      ) {
        return state;
      }
      return { ...state, next: nextItem, prev: prevItem, canScrollToBottom };
    });
  }

  const handleScroll = throttle((event: Event) => {
    const target = event.target as HTMLElement;
    updateNavigationItems(target.scrollTop);
  }, 100);

  const handleResize = throttle(updateTimestampPositions, 300);

  const onScrollTo = (offsetTop: number) => {
    scrollToY(container, offsetTop, { smooth: true });
  };

  const onScrollToBottom = () => {
    scrollToBottom(container, { smooth: true });
  };

  function initializeTracking() {
    updateTimestampPositions();

    conversationNavigationItems.update((state) => ({
      ...state,
      scrollTo: onScrollTo,
      scrollToBottom: onScrollToBottom,
    }));

    container.addEventListener("scroll", handleScroll, { passive: true });
    resizeObserver = new ResizeObserver(handleResize);
    resizeObserver.observe(container);
  }

  initializeTracking();
  updateCurrentProps(props);

  return {
    update(props: ObserveTimestampElementsProps) {
      updateCurrentProps(props);
      updateTimestampPositions();
    },
    destroy() {
      container.removeEventListener("scroll", handleScroll);
      resizeObserver?.disconnect();
      timestampPositions = [];
      conversationNavigationItems.update(() => ({ all: [] }));
    },
  };
}
