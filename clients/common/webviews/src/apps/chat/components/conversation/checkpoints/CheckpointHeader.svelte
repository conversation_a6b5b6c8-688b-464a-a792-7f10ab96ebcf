<!-- A component that displays the checkpoint header with title and summary -->
<script lang="ts">
  import TextCombo from "$common-webviews/src/common/components/TextCombo.svelte";
  import Revert from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/arrow-turn-left.svg?component";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import { TooltipTriggerOn } from "$common-webviews/src/design-system/_primitives/TooltipAugment/types";
  import Signpost from "$common-webviews/src/design-system/icons/signpost.svelte";
  import EditChangeSummary from "../../agent-edits/EditChangeSummary.svelte";
  import CollapsableIcon from "../../CollapsibleIcon.svelte";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";

  export let displayCheckpointIdx: number;
  export let filesCount: number = 0;
  export let timestamp: string = "";
  export let revertMessage: string | undefined = undefined;
  export let diffSummary: { totalAddedLines: number; totalRemovedLines: number } = {
    totalAddedLines: 0,
    totalRemovedLines: 0,
  };
  export let hasChanges: boolean = false;
  export let isTarget: boolean = false;
  export let onRevertClick: () => void;

  function handleRevert(e: Event) {
    e.stopPropagation();
    e.preventDefault();
    onRevertClick();
  }
</script>

<div class="c-checkpoint-header">
  <div class="c-checkpoint-tag">
    <CollapsableIcon>
      <Signpost slot="icon" />
    </CollapsableIcon>
    <TextCombo size={1} shrink align="left">
      <svelte:fragment slot="text">
        Checkpoint {displayCheckpointIdx}
        {#if filesCount > 0}
          <span class="c-checkpoint-files-count">
            ({filesCount} file{filesCount === 1 ? "" : "s"})
          </span>
        {/if}
      </svelte:fragment>
      <svelte:fragment slot="grayText">
        {#if revertMessage}
          {revertMessage}
        {:else if timestamp}
          {timestamp}
        {/if}
      </svelte:fragment>
    </TextCombo>
  </div>

  {#if hasChanges}
    <div class="c-checkpoint-summary">
      <EditChangeSummary
        size={0}
        totalAddedLines={diffSummary.totalAddedLines}
        totalRemovedLines={diffSummary.totalRemovedLines}
      />
    </div>
  {/if}

  {#if !isTarget}
    <TextTooltipAugment triggerOn={[TooltipTriggerOn.Hover]} content="Revert to this Checkpoint">
      <ButtonAugment
        variant="ghost-block"
        color="neutral"
        size={0}
        on:click={handleRevert}
        class="c-revert-button"
        data-testid="revert-button"
        data-collapsible-button="false"
      >
        <Revert slot="iconLeft" />
        Revert
      </ButtonAugment>
    </TextTooltipAugment>
  {/if}
</div>

<style>
  .c-checkpoint-header {
    padding: var(--ds-spacing-0_5) var(--ds-spacing-2);
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-1);
    width: 100%;

    /* Spread out all items */
    justify-content: space-between;

    cursor: pointer;

    & :global(.c-revert-button) {
      flex-shrink: 0;
    }

    & :global(.c-checkpoint-tag) {
      max-width: 100%;
      min-width: 0;
      display: flex;
      align-items: center;
      gap: var(--ds-spacing-1);

      user-select: none;
      flex: 1;

      & :global(.c-text-combo) {
        width: 100%;
      }
    }

    & :global(.c-checkpoint-summary) {
      flex-shrink: 0;
    }

    & :global(.c-checkpoint-files-count) {
      font-weight: normal;
      opacity: 0.8;
    }
  }
</style>
