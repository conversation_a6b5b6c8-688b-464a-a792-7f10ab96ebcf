export function extractSummary(toolUseInput: Record<string, unknown> = {}) {
  const { summary, ...rest } = toolUseInput;

  //try to maintain compatibility
  let queryOrSummary = (summary ?? rest.query) as string;
  return {
    formattedToolArgs: queryOrSummary ? [queryOrSummary] : undefined,
    toolUseInput: rest,
  } as {
    formattedToolArgs: [string] | undefined;
    toolUseInput: Record<string, unknown>;
  };
}

export function extractHref(toolUseInput: Record<string, unknown> = {}): string | undefined {
  const { href } = toolUseInput;
  return typeof href === "string" ? href : undefined;
}
