<script lang="ts">
  import { ToolUsePhase } from "$common-webviews/src/apps/chat/types/tool-use-state";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import CardAugment from "$common-webviews/src/design-system/components/CardAugment.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import Play from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/play-augment.svg?component";
  import { fly } from "svelte/transition";
  import { getToolUseContext } from "../tool-context";
  import RegisterCommand from "$common-webviews/src/common/components/keybindings/RegisterCommand.svelte";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import ShortcutHint from "$common-webviews/src/common/components/keybindings/ShortcutHint.svelte";
  import { TooltipTriggerOn } from "$common-webviews/src/design-system/components/types";

  const ctx = getToolUseContext();

  function onToolSkipSelect() {
    $ctx.onToolSkip($ctx.requestId, $ctx.toolUse.tool_use_id);
  }

  function runTool() {
    $ctx.onToolRun($ctx.requestId, $ctx.toolUse, $ctx.toolUseInput);
  }

  function handleToolRun(e: Event) {
    e.stopPropagation();
    runTool();
  }
</script>

{#if $ctx.toolUseState.phase === ToolUsePhase.runnable && $ctx.isLastTurn}
  <div class="c-tool-approval-panel" transition:fly={{ y: -2, duration: 150 }}>
    <RegisterCommand name="allowTool" handler={runTool} />
    <div class="tooltip-pointer"></div>
    <CardAugment borderless --after-box-shadow="none">
      <div class="c-tool-approval-panel__content">
        <TextAugment size={1}>Waiting for user input</TextAugment>

        <div class="c-tool-approval-panel__actions">
          <ButtonAugment size={1} variant="solid" color="neutral" on:click={onToolSkipSelect}>
            Skip
          </ButtonAugment>
          <TextTooltipAugment triggerOn={[TooltipTriggerOn.Hover]} delayDurationMs={300}>
            <div slot="content">
              <ShortcutHint commandName="allowTool" keysOnly />
            </div>
            <ButtonAugment
              size={1}
              variant="solid"
              color="accent"
              class="c-tool-approval-panel__approve-btn-pulse"
              on:click={handleToolRun}
            >
              <Play slot="iconLeft" />
              <span class="c-tool-approval-panel__approve-btn-text">Approve</span>
            </ButtonAugment>
          </TextTooltipAugment>
        </div>
      </div>
    </CardAugment>
  </div>
{/if}

<style>
  .c-tool-approval-panel {
    cursor: default;
  }

  .tooltip-pointer {
    position: relative;
    height: 8px;
    color: var(--ds-panel-translucent);
    display: flex;
    justify-content: center;
    width: 100%;

    &:before {
      content: "";
      position: absolute;
      border-color: transparent;
      border-style: solid;
      bottom: 0;
      border-width: 0 8px 8px;
      border-bottom-color: initial;
      transform-origin: center bottom;
    }
  }

  .c-tool-approval-panel__content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: var(--ds-spacing-2);
  }

  @container (max-width: 275px) {
    .c-tool-approval-panel__approve-btn-text {
      display: none;
    }
  }

  @container (max-width: 230px) {
    .c-tool-approval-panel__content {
      flex-direction: column;
      align-items: flex-start;
      flex-wrap: wrap;
    }
    .c-tool-approval-panel__approve-btn-text {
      display: inline-block;
    }
    .c-tool-approval-panel__actions {
      width: 100%;
      flex: 1;
      justify-content: stretch;
      --base-btn-flex: 1;
    }
  }

  .c-tool-approval-panel__actions {
    display: flex;
    gap: var(--ds-spacing-2);
  }

  .c-tool-approval-panel__actions :global(.c-tool-approval-panel__approve-btn-pulse) {
    --base-btn-box-shadow: 0 0 0 0 var(--ds-color-accent-9);
    animation: pulse 1s infinite;
  }

  @keyframes pulse {
    100% {
      box-shadow: 0 0 0 var(--ds-spacing-2) transparent;
    }
  }
</style>
