<script lang="ts">
  import BaseToolComponent from "../../BaseToolComponent.svelte";
  import ToolUseHeader from "../../ToolUseHeader.svelte";
  import StopIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/stop.svg?component";
  import {
    ToolUsePhase,
    type ToolUseState,
  } from "$common-webviews/src/apps/chat/types/tool-use-state";
  import ShellError from "../../sidecar/ShellError.svelte";
  import StatusBadgeAugment from "$common-webviews/src/design-system/components/StatusBadgeAugment.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import CardAugment from "$common-webviews/src/design-system/components/CardAugment.svelte";

  export let toolUseInput: Record<string, unknown> = {};
  export let toolUseState: ToolUseState;
  let collapsed = true;

  // Extract worker agent IDs array from input
  $: workerAgentIds = Array.isArray(toolUseInput.worker_agent_ids)
    ? toolUseInput.worker_agent_ids
    : [];

  // Format tool arguments for display - show count of agents in collapsed state
  $: formattedArgs = (() => {
    const args: string[] = [];
    if (workerAgentIds.length > 0) {
      args.push(`${workerAgentIds.length} agent${workerAgentIds.length === 1 ? "" : "s"}`);
    } else {
      args.push("all agents");
    }
    return args;
  })();

  // Parse the tool result to get stop results
  $: stopResults = (() => {
    if (
      toolUseState.phase === ToolUsePhase.completed &&
      toolUseState.result?.text &&
      !toolUseState.result.isError
    ) {
      try {
        const response = JSON.parse(toolUseState.result.text);
        // Combine successful and failed stops into a single array
        const successful = Array.isArray(response.stopResults) ? response.stopResults : [];
        const failed = Array.isArray(response.failedStops) ? response.failedStops : [];
        return [...successful, ...failed];
      } catch (e) {
        console.error("Failed to parse stop worker agent tool response:", e);
      }
    }
    return [];
  })();

  // Count successful and failed stops
  $: successCount = stopResults.filter((result: any) => result.success).length;
  $: failureCount = stopResults.filter((result: any) => !result.success).length;
  $: hasResults = stopResults.length > 0;
</script>

<BaseToolComponent bind:collapsed>
  <ToolUseHeader slot="header" toolName="Stop Worker Agents" formattedToolArgs={formattedArgs}>
    <StopIcon slot="icon" />
    <svelte:fragment slot="secondary">
      {#if hasResults && collapsed}
        <div class="c-stop-worker-tool__counts">
          {#if successCount > 0}
            <StatusBadgeAugment color="success" size={1}>
              {successCount} stopped
            </StatusBadgeAugment>
          {/if}
          {#if failureCount > 0}
            <StatusBadgeAugment color="error" size={1}>
              {failureCount} failed
            </StatusBadgeAugment>
          {/if}
        </div>
      {/if}
    </svelte:fragment>
  </ToolUseHeader>

  <div slot="details" class="c-stop-worker-tool__details">
    {#if toolUseState.phase === ToolUsePhase.running}
      <div class="c-stop-worker-tool__running">
        <TextAugment size={1} color="neutral">Stopping worker agents...</TextAugment>
      </div>
    {:else if toolUseState.phase === ToolUsePhase.completed && hasResults}
      <div class="c-stop-worker-tool__results">
        {#each stopResults as result, index}
          <CardAugment class="c-stop-worker-tool__agent-card">
            <div class="c-stop-worker-tool__agent-header">
              <div class="c-stop-worker-tool__agent-title">
                <TextAugment size={1} weight="medium">
                  Agent {result.agentId || `${index + 1}`}
                </TextAugment>
                <StatusBadgeAugment color={result.success ? "success" : "error"} size={1}>
                  {result.success ? "Stopped" : "Failed"}
                </StatusBadgeAugment>
              </div>
            </div>

            <div class="c-stop-worker-tool__agent-details">
              {#if result.success}
                <div class="c-stop-worker-tool__success-details">
                  <TextAugment size={1} color="neutral">
                    <strong>Agent ID:</strong>
                    {result.agentId}
                  </TextAugment>
                  <TextAugment size={1} color="success">
                    Worker agent has been successfully stopped.
                  </TextAugment>
                </div>
              {:else}
                <div class="c-stop-worker-tool__error-details">
                  <TextAugment size={1} color="error">
                    <strong>Error:</strong>
                    {result.error || "Unknown error"}
                  </TextAugment>
                  {#if result.agentId}
                    <TextAugment size={1} color="neutral">
                      <strong>Agent ID:</strong>
                      {result.agentId}
                    </TextAugment>
                  {/if}
                </div>
              {/if}
            </div>
          </CardAugment>
        {/each}
      </div>
    {:else if toolUseState.phase === ToolUsePhase.completed && !hasResults}
      <div class="c-stop-worker-tool__no-results">
        <TextAugment size={1} color="neutral">No worker agents were stopped.</TextAugment>
      </div>
    {/if}
  </div>

  {#if toolUseState.phase === ToolUsePhase.error}
    <ShellError slot="error" {...toolUseState.result ?? {}} />
  {/if}
</BaseToolComponent>

<style>
  .c-stop-worker-tool__counts {
    display: flex;
    gap: var(--ds-spacing-2);
    align-items: center;
    flex-wrap: wrap;
  }

  .c-stop-worker-tool__details {
    padding: var(--ds-spacing-2);
  }

  .c-stop-worker-tool__results {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-2);
  }

  .c-stop-worker-tool__agent-header {
    margin-bottom: var(--ds-spacing-2);
  }

  .c-stop-worker-tool__agent-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: var(--ds-spacing-2);
  }

  .c-stop-worker-tool__agent-details {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-2);
  }
</style>
