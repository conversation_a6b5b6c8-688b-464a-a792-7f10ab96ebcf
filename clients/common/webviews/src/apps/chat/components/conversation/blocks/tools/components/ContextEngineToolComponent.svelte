<script lang="ts">
  import BaseToolComponent from "./BaseToolComponent.svelte";
  import AugmentIcon from "$common-webviews/src/design-system/icons/augment/augment-logo.svelte";
  import ToolUseHeader from "./ToolUseHeader.svelte";
  import ShowMore from "$common-webviews/src/common/components/ShowMore.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";

  export let toolUseInput: Record<string, unknown> = {};
  export let retrieval: string | undefined = undefined;
  export let noRetrieval = false;
</script>

<BaseToolComponent>
  <ToolUseHeader
    slot="header"
    class="c-tool-memory-retrieval__header"
    toolName="Augment Context Engine"
  >
    <span slot="icon" class="c-tool-memory-retrieval__header__icon"><AugmentIcon /></span>
    <div slot="secondary" class="c-tool-memory-retrieval__secondary">
      {#if !noRetrieval}
        <TextAugment size={1} color="secondary">Retrieving from:</TextAugment>
        <span class="c-tool-memory-retrieval__metadata__source">
          <slot name="retrieval">
            <TextAugment size={1} color="neutral">{retrieval}</TextAugment>
          </slot>
        </span>
      {/if}
    </div>
  </ToolUseHeader>
  <ShowMore maxHeight={40} showOnHover={true} overflowThreshold={0} slot="details">
    <div class="c-tool-memory-retrieval__details">
      {toolUseInput.information_request || toolUseInput.query || ""}
    </div>
  </ShowMore>
</BaseToolComponent>

<style>
  .c-tool-memory-retrieval__header__icon {
    width: var(--ds-icon-size-2);
    height: var(--ds-icon-size-2);
    /*  Hack to fix icon positioning */
    position: relative;
    top: -1px;
  }
  .c-tool-memory-retrieval__metadata__source {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-1);
  }
  .c-tool-memory-retrieval__secondary {
    display: flex;
    gap: var(--ds-spacing-1);
    align-items: center;
  }

  .c-tool-memory-retrieval__details {
    display: flex;
    flex: 1;
    padding: var(--ds-spacing-2) var(--ds-spacing-4);
  }
  /** Remove padding from show-more because we're in a ToolUse container */
  .c-tool-memory-retrieval__secondary :global(.c-show-more__button-container) {
    background-color: var(--user-theme-sidebar-background);
    padding: 0;
  }

  /** Remove bottom padding when expanded, otherwise extraneous black space is added */
  .c-tool-memory-retrieval__secondary :global(.c-show-more--expanded .c-show-more__content) {
    padding-bottom: 0;
  }

  .c-tool-memory-retrieval__metadata__source {
    color: var(--ds-color-neutral-11);
  }

  .c-tool-memory-retrieval__metadata__source :global(.material-symbols-outlined) {
    line-height: 1;
    vertical-align: middle;
  }
</style>
