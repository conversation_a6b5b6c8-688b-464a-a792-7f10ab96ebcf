<script lang="ts">
  import ChatMessage from "../../ChatMessage.svelte";
  import type { JSONContent } from "@tiptap/core";
  import { ExchangeStatus, type EducateFeatures } from "../../../types/chat-message";
  import type { ChatFlagsModel } from "../../../models/chat-flags-model";
  import StreamingResponseBlock from "./StreamingResponseBlock.svelte";
  import {
    MarkdownPlainText,
    MarkdownSpecialBlock,
  } from "$common-webviews/src/common/components/markdown/MarkdownTypes";
  import RichTextEditorAugment from "$common-webviews/src/design-system/components/RichTextEditorAugment";
  import type RichTextEditorRoot from "$common-webviews/src/design-system/components/RichTextEditorAugment/Root.svelte";
  import AtMentions from "../../ChatInput/AtMentions.svelte";

  export let flagsModel: ChatFlagsModel;
  export let turn: EducateFeatures;

  const passedTurn = {
    // eslint-disable-next-line @typescript-eslint/naming-convention
    seen_state: turn.seen_state,
    status: ExchangeStatus.success,
  };

  let richTextJsonRepr: JSONContent = {
    type: "doc",
    content: [
      {
        type: "paragraph",
        content: [
          { type: "text", text: "What can " },
          {
            type: "mention",
            attrs: {
              id: "Augment",
              label: "Augment",
              data: { id: "Augment", label: "Augment" },
            },
          },
          { type: "text", text: " do?" },
        ],
      },
    ],
  };

  const features = [
    [
      new MarkdownSpecialBlock("[**Chat**](https://docs.augmentcode.com/using-augment/chat)"),
      new MarkdownPlainText(
        ": Explore your codebase, get up to speed on unfamiliar code, and work through technical problems using natural language.",
      ),
    ],
    [
      new MarkdownSpecialBlock(
        "[**Code Completions**](https://docs.augmentcode.com/using-augment/completions)",
      ),
      new MarkdownPlainText(
        ": Receive intelligent code suggestions that take your entire codebase into account as you type.",
      ),
    ],
    [
      new MarkdownSpecialBlock(
        "[**Instructions**](https://docs.augmentcode.com/using-augment/instructions)",
      ),
      new MarkdownPlainText(
        ": Use natural language prompts to write or modify code, applied as a diff for your review.",
      ),
    ],
  ];

  if (flagsModel.suggestedEditsAvailable) {
    features.push([
      new MarkdownSpecialBlock(
        "[**Suggested Edits**](https://docs.augmentcode.com/using-augment/suggested-edits)",
      ),
      new MarkdownPlainText(": Take your completions beyond the cursor and across your workspace."),
    ]);
  }

  let augmentResponse = [
    new MarkdownPlainText(
      "Welcome to Augment!\n\nAugment can help you understand code, debug issues, and ship faster with its deep understanding of your codebase. Here is what Augment can do for you:\n\n",
    ),
    ...features.flatMap((feature, index) => [
      new MarkdownPlainText(`${index + 1}. `),
      ...feature,
      new MarkdownPlainText("\n\n"),
    ]),
    new MarkdownPlainText(
      "Ask questions to learn more! Just remember to tag **@Augment** when asking about Augment's capabilities.",
    ),
  ];

  let richTextEditorRoot: RichTextEditorRoot | undefined = undefined;
  function requestEditorFocus() {
    richTextEditorRoot?.requestFocus();
  }
</script>

<ChatMessage>
  <RichTextEditorAugment.Root slot="content" bind:this={richTextEditorRoot}>
    <RichTextEditorAugment.Content content={richTextJsonRepr} />
    <AtMentions {requestEditorFocus} />
  </RichTextEditorAugment.Root>
</ChatMessage>

<StreamingResponseBlock turn={passedTurn} markdownBlocks={augmentResponse} />
