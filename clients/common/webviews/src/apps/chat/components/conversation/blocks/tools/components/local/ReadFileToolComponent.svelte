<script lang="ts">
  import BaseToolComponent from "../BaseToolComponent.svelte";
  import FileTextIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/file.svg?component";
  import ToolUseHeader from "../ToolUseHeader.svelte";
  import { stringOrDefault } from "$common-webviews/src/apps/chat/types/tool-use-state";
  import FilespanOpen from "../../FilespanOpen.svelte";

  export let toolUseInput: Record<string, unknown> = {};
  $: filepath = stringOrDefault(toolUseInput.file_path, "");
</script>

<BaseToolComponent>
  <ToolUseHeader slot="header" toolName="Read" formattedToolArgs={[filepath]}>
    <FileTextIcon slot="icon" />

    <FilespanOpen slot="secondary" {filepath} />
  </ToolUseHeader>
</BaseToolComponent>
