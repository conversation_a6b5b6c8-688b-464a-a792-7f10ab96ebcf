<script lang="ts">
  import BaseToolComponent from "../../BaseToolComponent.svelte";
  import ToolUseHeader from "../../ToolUseHeader.svelte";
  import ToolResultFields from "./ToolResultFields.svelte";
  import ApplyIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/circle-check.svg?component";
  import ConflictIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/triangle-exclamation.svg?component";
  import ErrorIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/circle-xmark.svg?component";
  import {
    ToolUsePhase,
    type ToolUseState,
  } from "$common-webviews/src/apps/chat/types/tool-use-state";
  import StatusBadgeAugment from "$common-webviews/src/design-system/components/StatusBadgeAugment.svelte";

  export let toolUseInput: Record<string, unknown> = {};
  export let toolUseState: ToolUseState;
  let collapsed = true;

  // Extract worker agent IDs array from input
  $: workerAgentIds = Array.isArray(toolUseInput.worker_agent_ids)
    ? toolUseInput.worker_agent_ids
    : [];

  // Format tool arguments for display - show count of agents in collapsed state
  $: formattedArgs = (() => {
    const args: string[] = [];
    if (workerAgentIds.length > 0) {
      args.push(`${workerAgentIds.length} agent${workerAgentIds.length === 1 ? "" : "s"}`);
    }
    return args;
  })();

  // Parse the result data
  $: parsedResult = (() => {
    if (toolUseState.phase === ToolUsePhase.completed && toolUseState.result?.text) {
      try {
        return JSON.parse(toolUseState.result.text);
      } catch {
        return null;
      }
    }
    return null;
  })();

  // Check if we have results to display
  $: hasResults = toolUseState.phase === ToolUsePhase.completed && parsedResult;

  // Count results for display
  $: appliedCount = parsedResult?.appliedFiles?.length || 0;
  $: conflictCount = parsedResult?.conflictFiles?.length || 0;
  $: errorCount = parsedResult?.errors?.length || 0;

  // Determine the icon and status based on result
  $: statusInfo = (() => {
    if (toolUseState.phase === ToolUsePhase.error) {
      return { icon: ErrorIcon, color: "error", text: "Failed" };
    }

    if (parsedResult) {
      const hasErrors = parsedResult.errors && parsedResult.errors.length > 0;
      const hasConflicts = parsedResult.conflictFiles && parsedResult.conflictFiles.length > 0;

      if (hasErrors) {
        return { icon: ErrorIcon, color: "error", text: "Errors" };
      } else if (hasConflicts) {
        return { icon: ConflictIcon, color: "warning", text: "Conflicts" };
      } else if (parsedResult.success) {
        return { icon: ApplyIcon, color: "success", text: "Success" };
      }
    }

    return { icon: ApplyIcon, color: "neutral", text: "Apply" };
  })();

  // Create display data for the tool result
  $: resultData = (() => {
    const data: Record<string, any> = {};

    // Add input parameters
    if (workerAgentIds.length > 0) {
      data.workerAgentIds = workerAgentIds.join(", ");
    }

    const applicationMethod = (toolUseInput.application_method as string) || "direct_files";
    data.applicationMethod = applicationMethod;

    const targetFiles = (toolUseInput.target_files as string[]) || [];
    if (targetFiles.length > 0) {
      data.targetFiles = targetFiles.join(", ");
    }

    const targetBranch = (toolUseInput.target_branch as string) || "current";
    if (targetBranch !== "current") {
      data.targetBranch = targetBranch;
    }

    // Add result information if completed
    if (parsedResult) {
      if (parsedResult.message) {
        data.status = parsedResult.message;
      }
      if (
        parsedResult.appliedFiles &&
        Array.isArray(parsedResult.appliedFiles) &&
        parsedResult.appliedFiles.length > 0
      ) {
        data.appliedFiles = parsedResult.appliedFiles.join(", ");
      }
      if (
        parsedResult.conflictFiles &&
        Array.isArray(parsedResult.conflictFiles) &&
        parsedResult.conflictFiles.length > 0
      ) {
        data.conflictFiles = parsedResult.conflictFiles.join(", ");
      }
      if (
        parsedResult.errors &&
        Array.isArray(parsedResult.errors) &&
        parsedResult.errors.length > 0
      ) {
        data.errors = parsedResult.errors.join("; ");
      }
      if (parsedResult.method) {
        data.method = parsedResult.method;
      }
    } else if (toolUseState.result?.text) {
      // If parsing fails, show raw result
      data.result = toolUseState.result.text;
    }

    return data;
  })();

  // Create field configuration
  $: resultFields = [
    {
      key: "workerAgentIds",
      label: "Worker Agent IDs",
      type: "text" as const,
      color: "neutral" as const,
    },
    {
      key: "applicationMethod",
      label: "Application Method",
      type: "text" as const,
      color: "neutral" as const,
    },
    {
      key: "method",
      label: "Applied Method",
      type: "text" as const,
      color: "accent" as const,
      condition: () => resultData.method && resultData.method !== resultData.applicationMethod,
    },
    {
      key: "targetFiles",
      label: "Target Files",
      type: "text" as const,
      color: "neutral" as const,
      condition: () => resultData.targetFiles,
    },
    {
      key: "targetBranch",
      label: "Target Branch",
      type: "text" as const,
      color: "neutral" as const,
      condition: () => resultData.targetBranch,
    },
    {
      key: "status",
      label: "Status",
      type: "text" as const,
      color: parsedResult?.success ? ("success" as const) : ("neutral" as const),
    },
    {
      key: "appliedFiles",
      label: "Applied Files",
      type: "text" as const,
      color: "success" as const,
      condition: () => resultData.appliedFiles,
    },
    {
      key: "conflictFiles",
      label: "Conflict Files",
      type: "text" as const,
      color: "error" as const,
      condition: () => resultData.conflictFiles,
    },
    {
      key: "errors",
      label: "Errors",
      type: "text" as const,
      color: "error" as const,
      condition: () => resultData.errors,
      collapsible: true,
    },
    {
      key: "result",
      label: "Raw Result",
      type: "monospace" as const,
      color: "neutral" as const,
      condition: () => resultData.result,
      collapsible: true,
    },
  ];
</script>

<BaseToolComponent showToolOutput={true} bind:collapsed>
  <ToolUseHeader slot="header" toolName="Apply Worker Edits" formattedToolArgs={formattedArgs}>
    <svelte:component this={statusInfo.icon} slot="icon" />
    <svelte:fragment slot="secondary">
      {#if hasResults && collapsed}
        <div class="c-apply-edits-tool__counts">
          {#if appliedCount > 0}
            <StatusBadgeAugment color="success" size={1}>
              {appliedCount} applied
            </StatusBadgeAugment>
          {/if}
          {#if conflictCount > 0}
            <StatusBadgeAugment color="warning" size={1}>
              {conflictCount} conflicts
            </StatusBadgeAugment>
          {/if}
          {#if errorCount > 0}
            <StatusBadgeAugment color="error" size={1}>
              {errorCount} errors
            </StatusBadgeAugment>
          {/if}
        </div>
      {/if}
    </svelte:fragment>
  </ToolUseHeader>

  <ToolResultFields slot="details" data={resultData} fields={resultFields} />
</BaseToolComponent>

<style>
  .c-apply-edits-tool__counts {
    display: flex;
    gap: var(--ds-spacing-1);
    align-items: center;
  }
</style>
