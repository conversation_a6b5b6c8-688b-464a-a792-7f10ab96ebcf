<script lang="ts">
  import SpinnerAugment from "$common-webviews/src/design-system/components/SpinnerAugment.svelte";
  import CrossCircled from "$common-webviews/src/design-system/icons/augment/cross-circled.svelte";
  import CircleBackslash from "$common-webviews/src/design-system/icons/circle-backslash.svelte";
  import CheckIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/circle-check.svg?component";

  export let commandResult: "success" | "error" | "skipped" | "loading" | "none";
</script>

<div class="c-setup-script-command-status c-setup-script-command-status--{commandResult}">
  {#if commandResult === "loading"}
    <SpinnerAugment size={1} />
  {:else if commandResult === "success"}
    <CheckIcon />
  {:else if commandResult === "error"}
    <CrossCircled />
  {:else if commandResult === "skipped"}
    <CircleBackslash />
  {/if}
</div>

<style>
  .c-setup-script-command-status {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--ds-spacing-1);
    color: var(--ds-color-neutral-11);
  }

  .c-setup-script-command-status--loading {
    animation: spin 3s linear infinite;
  }

  .c-setup-script-command-status--success {
    color: var(--ds-color-success-9);
  }

  .c-setup-script-command-status--error {
    color: var(--ds-color-error-9);
  }

  .c-setup-script-command-status--skipped {
    color: var(--ds-color-neutral-9);
  }

  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
</style>
