<script lang="ts">
  import { onD<PERSON>roy, onMount } from "svelte";
  import { editor, <PERSON><PERSON> } from "monaco-editor";
  import type * as Monaco from "monaco-editor/esm/vs/editor/editor.api";
  import { themeStore } from "$common-webviews/src/common/hosts/user-themes/theme-store";
  import { getMonacoTheme } from "$common-webviews/src/common/utils/monaco-theme";

  /**
   * Much complexity in this component comes down to the fact that we try to show a
   * simple compact diff, without gutters, actions, etc.
   *
   * Monaco diff is rendered by overlapping two fully rendered editors:
   * - Original editor
   * - Modified editor
   *
   * While the "modified" editor shows all the code, the purpose of the "original"
   * editor seems to be to show gutter icons from the original code as well. Despite
   * its purpose, other elements of the original editor can shine through in various
   * scenarios, showing extra scrollbars or underlying code.
   *
   * Changing some settings into "less expected" values can break the layout. In
   * particular, setting `lineDecorationsWidth: 0` results in the scrollbar and even
   * text of the underlying original editor becoming visible.
   *
   * Newer versions of Monaco have support for compactMode, which would likely be a
   * better starting point for the purposes of this component.
   */

  // Inputs: original code and modified code, whose diff will be displayed
  export let originalCode: string | undefined;
  export let modifiedCode: string | undefined;
  export let path: string | undefined;

  let diffEditor: Monaco.editor.IDiffEditor | undefined;
  let originalModel: Monaco.editor.ITextModel | undefined;
  let modifiedModel: Monaco.editor.ITextModel | undefined;

  let editorContainer: HTMLElement;
  let heightInPx = 200;

  onDestroy(() => {
    diffEditor?.dispose();
    originalModel?.dispose();
    modifiedModel?.dispose();
  });

  onMount(() => {
    diffEditor = editor.createDiffEditor(editorContainer, {
      lineNumbers: "off",
      hideUnchangedRegions: {
        enabled: true,
        revealLineCount: 3,
        minimumLineCount: 3,
        contextLineCount: 3,
      },
      scrollBeyondLastLine: false,
      readOnly: true,
      minimap: {
        enabled: false,
      },
      lightbulb: {
        enabled: editor.ShowLightbulbIconMode.Off,
      },
      automaticLayout: true,
      enableSplitViewResizing: false,
      diffCodeLens: false,
      renderOverviewRuler: false,
      renderSideBySide: false,

      // Hide the gutter
      // https://stackoverflow.com/questions/53448735/is-there-a-way-to-completely-hide-the-gutter-of-monaco-editor
      //
      // Note that we are compressing the gutter starts to cause the layout to
      // break and reveal that there are two overlapping editors being rendered.
      //
      // In particular, `lineDecorationsWidth: 0` alone is enough to cause
      // trouble. Even just leaving lineDecorationsWidth at the default value,
      // the layout starts to get strained: e.g., the `codicon-unfold` icon
      // for revealing hidden lines starts to look cramped if the gutter isn't
      // wide enough.
      glyphMargin: false,
      folding: false,
      showFoldingControls: "never",
      overviewRulerLanes: 0,
      hideCursorInOverviewRuler: true,
      overviewRulerBorder: false,
      lineDecorationsWidth: 0,
    });
    diffEditor.getOriginalEditor().updateOptions({
      // Line numbers must be disabled on the underlying editors as well
      lineNumbers: "off",
    });
    diffEditor.getModifiedEditor().updateOptions({
      // Line numbers must be disabled on the underlying editors as well
      lineNumbers: "off",
    });
    updateModels(path, originalCode, modifiedCode);
  });

  function updateModels(
    path: string | undefined,
    originalCode: string | undefined,
    modifiedCode: string | undefined,
  ) {
    originalModel?.dispose();
    modifiedModel?.dispose();

    originalCode = originalCode || "";
    modifiedCode = modifiedCode || "";

    // Monaco requires all URIs passed here to be unique, so we append identifiers below
    originalModel = editor.createModel(
      originalCode,
      undefined,
      path !== undefined ? Uri.parse("file://" + path + `#${crypto.randomUUID()}`) : undefined,
    );
    modifiedModel = editor.createModel(
      modifiedCode,
      undefined,
      path !== undefined ? Uri.parse("file://" + path + `#${crypto.randomUUID()}`) : undefined,
    );

    if (diffEditor) {
      diffEditor.setModel({
        original: originalModel,
        modified: modifiedModel,
      });
    }
  }
  $: {
    // We must reference the properties in this reactivity block,
    // otherwise svelte will not know when to trigger UI updates
    // and will not run this block when we want it to.
    updateModels(path, originalCode, modifiedCode);
  }

  $: {
    if (diffEditor) {
      const themeDetails = $themeStore;
      const monacoTheme = getMonacoTheme(themeDetails?.category, themeDetails?.intensity);
      const themeOptions: Monaco.editor.IStandaloneEditorConstructionOptions = {
        theme: monacoTheme,
      };

      editor.setTheme(monacoTheme);
      diffEditor.getModifiedEditor().updateOptions(themeOptions);
      diffEditor.getOriginalEditor().updateOptions(themeOptions);
      diffEditor.layout();
    }
  }
</script>

<div>
  <div
    bind:this={editorContainer}
    style={"height:" + heightInPx + "px"}
    class="editor-container"
  ></div>
</div>

<style>
  .editor-container {
    height: 200px;
    width: 100%;
    overflow: auto;
    resize: vertical;
  }

  /*
     * Hide the "original" editor completely. We don't need gutter icons. This avoids
     * elements of the "original" editor shining through in various scenarios.
     */
  .editor-container :global(.editor.original) {
    display: none;
  }

  /*
     * Hide the lightbulb icon
     * Workaround for https://github.com/microsoft/monaco-editor/issues/3873
     */
  .editor-container :global(.codicon-light-bulb)::before {
    display: none;
  }

  /*
     * Hide the background color of the hidden lines to avoid overlapping the scrollbar.
     */
  .editor-container :global(.monaco-editor) :global(.diff-hidden-lines) :global(.center) {
    background-color: transparent;
  }

  /*
     * Hide the hidden-lines icon. The default one is pretty ugly. The user can still
     * double-click the "hidden lines" message to expand. We can add it back if we
     * have a better icon.
     */
  .editor-container :global(.diff-hidden-lines > div > div:has(.codicon-unfold)) {
    display: none !important;
  }
</style>
