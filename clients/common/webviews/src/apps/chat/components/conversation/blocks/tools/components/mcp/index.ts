import { type ChatResultToolUse } from "@augment-internal/sidecar-libs/src/chat/chat-types";
import McpToolComponent from "./McpToolComponent.svelte";

/**
 * Returns the appropriate tool component for a given tool type.
 * @param toolType MCPToolType or string
 * @returns The MCP tool component if the tool is of type MCP, otherwise undefined
 */
export function resolveMCPTool(toolType: string | ChatResultToolUse) {
  // Check if the tool is an MCP tool
  if (typeof toolType === "object") {
    // If we have a ChatResultToolUse object, check for MCP-specific properties
    if (toolType.mcp_server_name || toolType.mcp_tool_name) {
      return McpToolComponent;
    }
  }
  return undefined;
}
export { McpToolComponent };
