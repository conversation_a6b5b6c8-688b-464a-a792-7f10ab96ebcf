<script lang="ts">
  import BaseToolComponent from "../BaseToolComponent.svelte";
  import McpIcon from "$common-webviews/src/design-system/icons/mcp-logo.svelte";
  import ToolUseHeader from "../ToolUseHeader.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import { getToolUseContext } from "../../tool-context";
  import { getPartnerMCPDisplayDetails } from "$common-webviews/src/apps/settings/components/partner-mcp-utils";
  import { type PARTNER_MCP_SERVERS } from "@augment-internal/sidecar-libs/src/tools/partner-remote-mcp";

  export let toolUseInput: Record<string, unknown> = {};

  const ctx = getToolUseContext();

  // Extract tool name from the tool use object
  $: toolName = $ctx.toolUse.tool_name;
  $: mcpServerName = $ctx.toolUse.mcp_server_name;
  $: mcpToolName = $ctx.toolUse.mcp_tool_name;

  let partnerDetails = getPartnerMCPDisplayDetails(mcpServerName as PARTNER_MCP_SERVERS);

  // Create a display name that includes server name if available
  let primaryName = partnerDetails ? partnerDetails.displayName : mcpServerName;
  let secondaryName = mcpToolName;

  $: {
    if (mcpServerName) {
      primaryName = mcpServerName;
      secondaryName = mcpToolName || "";
      partnerDetails = getPartnerMCPDisplayDetails(mcpServerName as PARTNER_MCP_SERVERS);
      if (partnerDetails) {
        primaryName = partnerDetails.displayName;
      }
    } else {
      primaryName = mcpToolName || toolName || "MCP";
      secondaryName = "";
    }
  }
</script>

<BaseToolComponent showToolOutput={true}>
  <ToolUseHeader slot="header" toolName={primaryName} {toolUseInput}>
    <svelte:component this={partnerDetails?.icon ?? McpIcon} slot="icon" />
    <span slot="secondary" class="c-mcp-tool__summary">
      <TextAugment size={1}>{secondaryName}</TextAugment>
    </span>
  </ToolUseHeader>
</BaseToolComponent>

<style>
  .c-mcp-tool__summary {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
</style>
