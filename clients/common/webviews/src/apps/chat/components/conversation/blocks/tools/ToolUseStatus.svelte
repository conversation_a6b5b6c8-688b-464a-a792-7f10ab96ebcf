<script lang="ts">
  import SpinnerAugment from "$common-webviews/src/design-system/components/SpinnerAugment.svelte";
  import { ToolUsePhase } from "../../../../types/tool-use-state";
  import CancelHover from "./CancelHover.svelte";
  import { getToolUseContext } from "./tool-context";
  import StatusIndicator from "./StatusIndicator.svelte";
  import Pause from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/pause-custom.svg?component";

  const ctx = getToolUseContext();

  export let isCommandError = false;
  export let toolUseState = $ctx.toolUseState;
  $: toolUseState = $ctx.toolUseState;

  function handleToolCancel(e: Event) {
    e.stopPropagation();
    $ctx.onToolCancel($ctx.requestId, $ctx.toolUse.tool_use_id);
  }
</script>

<div class="c-tooluse-status" data-tool-use-state={toolUseState.phase}>
  {#if toolUseState.phase === ToolUsePhase.runnable}
    {#if $ctx.isLastTurn}
      <Pause />
    {/if}
  {:else if isCommandError}
    <!-- tool command did not exit 0-->
    <StatusIndicator status="error" content="Command failed" />
  {:else if toolUseState.phase === ToolUsePhase.new}
    <StatusIndicator content="Waiting for end of message...">
      <SpinnerAugment size={1} />
    </StatusIndicator>
  {:else if toolUseState.phase === ToolUsePhase.checkingSafety}
    <StatusIndicator content="Looking up tool...">
      <SpinnerAugment size={1} />
    </StatusIndicator>
  {:else if toolUseState.phase === ToolUsePhase.running}
    <CancelHover onClick={handleToolCancel} />
  {:else if toolUseState.phase === ToolUsePhase.cancelled}
    <StatusIndicator status="error" content="Cancelled" />
  {:else if toolUseState.phase === ToolUsePhase.error}
    <StatusIndicator status="error" content="Failed" />
  {:else if toolUseState.phase === ToolUsePhase.cancelling}
    <StatusIndicator status="error" content="Cancelling...">
      <SpinnerAugment size={1} />
    </StatusIndicator>
  {:else}
    <StatusIndicator status="success" content="Completed" />
  {/if}
</div>

<style>
  .c-tooluse-status {
    display: flex;
    gap: var(--ds-spacing-1);
    align-items: center;
    justify-self: flex-end;
    justify-content: flex-end;
    flex: var(--toolusestatus-flex, 0 0 auto);
    flex-shrink: 0;
    & :global(.c-tooluse__main-vertical-icon) {
      width: 16px;
    }
  }
</style>
