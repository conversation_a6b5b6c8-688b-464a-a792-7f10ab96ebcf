<script lang="ts">
  import { onKey } from "$common-webviews/src/common/utils/keypress";
  import type { FileDetails } from "$vscode/src/webview-providers/webview-messages";
  import Filespan from "$common-webviews/src/common/components/Filespan.svelte";
  import File from "$common-webviews/src/design-system/icons/vscode/file.svelte";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import { TooltipTriggerOn } from "$common-webviews/src/design-system/_primitives/TooltipAugment/types";

  export let fileDetails: FileDetails;
  export let onOpenLocalFile: (details: FileDetails) => void;
</script>

<TextTooltipAugment content={fileDetails.pathName} triggerOn={[TooltipTriggerOn.Hover]}>
  <div
    class="c-tooluse-filepath"
    on:click={() => onOpenLocalFile(fileDetails)}
    on:keydown={onKey("Enter", () => onOpenLocalFile(fileDetails))}
    role="button"
    tabindex="0"
    data-testid="file-link"
  >
    <Filespan filepath={fileDetails.pathName}>
      <File slot="leftIcon" />
    </Filespan>
  </div>
</TextTooltipAugment>

<style>
  .c-tooluse-filepath {
    cursor: pointer;
    color: var(--ds-color-accent-a11);
  }

  .c-tooluse-filepath:hover {
    color: var(--ds-color-accent-a12);
  }
</style>
