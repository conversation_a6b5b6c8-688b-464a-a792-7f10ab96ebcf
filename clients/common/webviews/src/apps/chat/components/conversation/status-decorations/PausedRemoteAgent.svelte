<script lang="ts">
  // Component to display a message when a remote agent is paused
</script>

<div class="c-paused-remote-agent">
  <span class="c-paused-remote-agent__text">
    This agent is paused and will resume when you send a new message
  </span>
</div>

<style>
  .c-paused-remote-agent {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    padding: var(--ds-spacing-2) var(--ds-spacing-3);
    width: 100%;
    opacity: 70%;
  }

  .c-paused-remote-agent__text {
    font-size: var(--ds-font-size-1);
    width: 100%;
    color: var(--ds-color-neutral-11);
    font-style: italic;
  }
</style>
