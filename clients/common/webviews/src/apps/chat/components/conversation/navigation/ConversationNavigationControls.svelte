<script lang="ts">
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import RegularUpIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/arrow-up-long.svg?component";
  import RegularDownIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/arrow-down-long.svg?component";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";

  import { getConversationNavigationItemsContext } from "./context";
  import { isFocusWithinTextboxElement } from "../../../chat-keybindings";
  import RegisterCommand from "$common-webviews/src/common/components/keybindings/RegisterCommand.svelte";
  import ShortcutHint from "$common-webviews/src/common/components/keybindings/ShortcutHint.svelte";

  const conversationNavigationItems = getConversationNavigationItemsContext();
  $: prev = $conversationNavigationItems.prev;
  $: next = $conversationNavigationItems.next;
  $: scrollTo = $conversationNavigationItems.scrollTo;
  $: scrollToBottom = $conversationNavigationItems.scrollToBottom;
  $: canScrollToBottom = $conversationNavigationItems.canScrollToBottom;
  $: canScrollDown = canScrollToBottom;

  function onNext() {
    if (!canScrollDown) return;
    if (isFocusWithinTextboxElement()) return;

    if (next && scrollTo) {
      scrollTo(next.offsetTop);
    } else if (canScrollToBottom && scrollToBottom) {
      scrollToBottom();
    }
  }
  function onPrev() {
    if (isFocusWithinTextboxElement()) return;
    if (prev && scrollTo) scrollTo(prev.offsetTop);
  }
</script>

<!-- <svelte:window on:keydown={handleKeydown} /> -->

{#if prev || canScrollToBottom}
  <div class="c-conversation-navigation">
    <slot />
    <div class="c-conversation-navigation__buttons">
      <TextTooltipAugment side="top" nested={false}>
        <div slot="content" class="c-conversation-navigation__tooltip">
          Up
          <ShortcutHint commandName="scrollConversationUp" keysOnly />
        </div>
        <RegisterCommand name="scrollConversationUp" handler={onPrev} />
        <IconButtonAugment
          variant="ghost-block"
          color="neutral"
          size={0.5}
          on:click={onPrev}
          disabled={!prev}
        >
          <RegularUpIcon />
        </IconButtonAugment>
      </TextTooltipAugment>
      <TextTooltipAugment side="top" nested={false}>
        <div slot="content" class="c-conversation-navigation__tooltip">
          Down
          <ShortcutHint commandName="scrollConversationDown" keysOnly />
        </div>
        <RegisterCommand name="scrollConversationDown" handler={onNext} />
        <IconButtonAugment
          variant="ghost-block"
          color="neutral"
          size={0.5}
          on:click={onNext}
          disabled={!canScrollDown}
        >
          <RegularDownIcon />
        </IconButtonAugment>
      </TextTooltipAugment>
    </div>
  </div>
{/if}

<style>
  .c-conversation-navigation {
    display: flex;
    flex: 1;
    align-items: center;
    gap: var(--ds-spacing-1);
    margin-left: var(--ds-spacing-1);
  }
  .c-conversation-navigation :global(.c-icon-btn) {
    --icon-btn-size: var(--ds-spacing-4_5);
  }
  .c-conversation-navigation__buttons {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: var(--ds-spacing-1);
  }
  .c-conversation-navigation__buttons {
    color: var(--ds-color-neutral-11);
    /* --base-btn-color: var(--ds-color-neutral-11); */
  }

  .c-conversation-navigation__tooltip {
    --keyboard-shortcut-hint-border: currentColor;
    --keyboard-shortcut-hint-color: currentColor;

    display: flex;
    align-items: center;
    gap: var(--ds-spacing-2);
  }
</style>
