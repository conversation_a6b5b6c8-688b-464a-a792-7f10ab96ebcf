/**
 * UserMessage component tests with feature flag utilities
 *
 * This test file demonstrates how to use the ChatModelTestKit utility methods
 * for testing different feature flag configurations:
 *
 * - ChatModelTestKit.withFlags(flags) - Create test kit with specific flags
 * - ChatModelTestKit.withLimitedFeatures() - Create test kit with fullFeatured: false
 * - ChatModelTestKit.withDebugFeatures() - Create test kit with debug features enabled
 *
 * Each utility method creates a properly configured ChatModel with the specified
 * flags set during initialization, ensuring proper timing for Svelte's reactivity.
 */

import { render, waitFor } from "@testing-library/svelte";
import { expect, describe, test, vi, beforeEach, afterEach } from "vitest";
import userEvent from "@testing-library/user-event";

import UserMessageTest from "./UserMessage.test.svelte";
import { ChatModel } from "../../models/chat-model";
import { SpecialContextInputModel } from "../../models/context-model";
import { AsyncMsgSender } from "$common-webviews/src/common/hosts/async-messaging";
import { HostClientType, type HostInterface } from "$common-webviews/src/common/hosts/host-types";
import { MessageBroker } from "$common-webviews/src/common/utils/message-broker";
import { EditorTestKit } from "$common-webviews/src/design-system/components/RichTextEditorAugment/__tests__/test-kit";
import { type AsyncWebViewMessage } from "$vscode/src/webview-providers/webview-messages";
import { type WebViewMessageTypes } from "$vscode/src/utils/webviews/types";
import { ChatModeModel } from "../../models/chat-mode-model";
import { RemoteAgentsModel } from "$common-webviews/src/apps/remote-agent-manager/models/remote-agents-model";
import { GitReferenceModel } from "$common-webviews/src/apps/remote-agent-manager/models/git-reference-model";
import { type AgentConversationModel } from "../../models/agent-conversation-model";
import { writable } from "svelte/store";
import { type IChatFlags } from "../../models/types";
import { AgentWebViewMessageType } from "@augment-internal/sidecar-libs/src/webview-messages/message-types/agent-messages";
import { WebViewMessageType } from "$vscode/src/webview-providers/webview-messages";

class ChatModelTestKit {
  host: HostInterface;
  contextModel: SpecialContextInputModel;
  chatModel: ChatModel;
  chatModeModel: ChatModeModel;
  remoteAgentsModel: RemoteAgentsModel;
  gitReferenceModel: GitReferenceModel;
  agentConversationModel: AgentConversationModel;
  uuidCounter: number;

  constructor(initialFlags?: Partial<IChatFlags>) {
    this.host = {
      clientType: HostClientType.vscode,
      postMessage: vi.fn().mockImplementation((msg) => {
        // Handle async messages by dispatching responses
        if (msg.type === "async-wrapper" && msg.baseMsg) {
          setTimeout(() => {
            switch (msg.baseMsg.type) {
              case AgentWebViewMessageType.checkHasEverUsedRemoteAgent:
                window.dispatchEvent(
                  new MessageEvent("message", {
                    data: {
                      type: "async-wrapper",
                      requestId: msg.requestId,
                      error: null,
                      baseMsg: {
                        type: AgentWebViewMessageType.checkHasEverUsedRemoteAgentResponse,
                        data: false,
                      },
                      streamCtx: undefined,
                    },
                  }),
                );
                break;
              case AgentWebViewMessageType.setHasEverUsedRemoteAgent:
                window.dispatchEvent(
                  new MessageEvent("message", {
                    data: {
                      type: "async-wrapper",
                      requestId: msg.requestId,
                      error: null,
                      baseMsg: {
                        type: "empty",
                      },
                      streamCtx: undefined,
                    },
                  }),
                );
                break;
              case WebViewMessageType.chatLoaded:
                window.dispatchEvent(
                  new MessageEvent("message", {
                    data: {
                      type: "async-wrapper",
                      requestId: msg.requestId,
                      error: null,
                      baseMsg: {
                        type: WebViewMessageType.chatInitialize,
                        data: {
                          enableDebugFeatures: false,
                          enableEditableHistory: true,
                          fullFeatured: true,
                          enablePreferenceCollection: false,
                          useRichTextHistory: true,
                          modelDisplayNameToId: {},
                          enableExternalSourcesInChat: false,
                          enableShareService: false,
                          useNewThreadsMenu: false,
                          enableSmartPaste: false,
                          retryChatStreamTimeouts: false,
                        },
                      },
                      streamCtx: undefined,
                    },
                  }),
                );
                break;
              case WebViewMessageType.getRemoteAgentStatus:
                window.dispatchEvent(
                  new MessageEvent("message", {
                    data: {
                      type: "async-wrapper",
                      requestId: msg.requestId,
                      error: null,
                      baseMsg: {
                        type: WebViewMessageType.remoteAgentStatusResponse,
                        data: {
                          isRemoteAgentSshWindow: false,
                          remoteAgentId: undefined,
                        },
                      },
                      streamCtx: undefined,
                    },
                  }),
                );
                break;
              default:
                // For other message types, just dispatch an empty response
                window.dispatchEvent(
                  new MessageEvent("message", {
                    data: {
                      type: "async-wrapper",
                      requestId: msg.requestId,
                      error: null,
                      baseMsg: {
                        type: "empty",
                      },
                      streamCtx: undefined,
                    },
                  }),
                );
                break;
            }
          }, 0);
        }
      }),
      getState: vi.fn(),
      setState: vi.fn(),
    };
    this.contextModel = new SpecialContextInputModel();
    this.uuidCounter = 0;

    vi.spyOn(crypto, "randomUUID").mockImplementation(() => {
      return this.mockUUID(this.uuidCounter++);
    });

    // Default flags for testing
    const defaultFlags: Partial<IChatFlags> = {
      fullFeatured: true,
      enableEditableHistory: true,
      ...initialFlags, // Override with provided flags
    };

    this.chatModel = new ChatModel(
      new AsyncMsgSender((message: AsyncWebViewMessage<WebViewMessageTypes>) => {
        this.host.postMessage(message);
      }),
      this.host,
      this.contextModel,
      { initialFlags: defaultFlags },
    );

    // Create mock AgentConversationModel
    this.agentConversationModel = {
      isCurrConversationAgentic: writable(false),
      distillMemory: vi.fn(),
    } as any;

    // Create mock GitReferenceModel
    this.gitReferenceModel = new GitReferenceModel(
      new AsyncMsgSender((message: AsyncWebViewMessage<WebViewMessageTypes>) => {
        this.host.postMessage(message);
      }),
    );

    // Create mock RemoteAgentsModel
    this.remoteAgentsModel = new RemoteAgentsModel({
      msgBroker: new MessageBroker(this.host),
      isActive: false,
      flagsModel: this.chatModel.flags,
      host: this.host,
      gitRefModel: this.gitReferenceModel,
    });

    // Create ChatModeModel
    this.chatModeModel = new ChatModeModel(
      this.chatModel,
      this.agentConversationModel,
      this.remoteAgentsModel,
    );
  }

  mockUUID(id: number | string): `${string}-${string}-${string}-${string}-${string}` {
    return `mocked-uuid-a-b-${id}`;
  }

  reset() {
    this.uuidCounter = 0;
    vi.clearAllMocks();
  }

  dispose() {
    this.remoteAgentsModel.dispose();
  }

  /**
   * Creates a test kit with specific feature flags for testing different scenarios
   */
  static withFlags(flags: Partial<IChatFlags>): ChatModelTestKit {
    return new ChatModelTestKit(flags);
  }

  /**
   * Creates a test kit with fullFeatured disabled for testing limited functionality
   */
  static withLimitedFeatures(): ChatModelTestKit {
    return new ChatModelTestKit({ fullFeatured: false });
  }

  /**
   * Creates a test kit with debug features enabled
   */
  static withDebugFeatures(): ChatModelTestKit {
    return new ChatModelTestKit({
      enableDebugFeatures: true,
      fullFeatured: true,
      enableEditableHistory: true,
    });
  }
}

describe("UserMessage.svelte", () => {
  let testKit: ChatModelTestKit;
  let onStartEdit = vi.fn();
  let onAcceptEdit = vi.fn();
  let onCancelEdit = vi.fn();
  let component: ReturnType<typeof render<UserMessageTest>>;

  beforeEach(async () => {
    EditorTestKit.mockTipTapDOMFunctions();
    testKit = new ChatModelTestKit();
    onStartEdit = vi.fn();
    onAcceptEdit = vi.fn();
    onCancelEdit = vi.fn();
    component = render(UserMessageTest, {
      props: {
        chatModel: testKit.chatModel,
        msg: "Original message",
        requestId: "123",
        richTextJsonRepr: undefined,
        onStartEdit,
        onAcceptEdit,
        onCancelEdit,
        testKit, // Pass the testKit to provide missing contexts
      },
    });
    await EditorTestKit.waitForRichTextInput(component);
  });

  afterEach(() => {
    if (component) {
      component.unmount();
    }
    if (testKit) {
      testKit.reset();
      testKit.dispose();
    }
    vi.clearAllMocks();
  });

  test("renders user message correctly", async () => {
    const input = await EditorTestKit.waitForRichTextInput(component);
    expect(input).toBeInTheDocument();

    // Wait for the content to be set in the editor
    await waitFor(() => {
      expect(input?.textContent).toBe("Original message");
    });
  });

  test("edit functionality works", async () => {
    const user = userEvent.setup();

    // Give extra time for async initialization
    await new Promise((resolve) => setTimeout(resolve, 500));

    // Wait for component to be fully rendered and async initialization to complete
    await waitFor(() => {
      const editButton = component.queryByTestId("edit-message-button");
      expect(editButton).toBeInTheDocument();
    });

    // Click edit button to start editing
    const editButton = component.getByTestId("edit-message-button");
    expect(editButton).toBeInTheDocument();
    expect(editButton).not.toBeDisabled();

    // Try a direct click event instead of user.click
    editButton.click();

    // Wait for the callback to be called
    await waitFor(
      () => {
        expect(onStartEdit).toHaveBeenCalled();
      },
      { timeout: 2000 },
    );

    // Wait for editor to be ready and focused
    const editor = await EditorTestKit.waitForRichTextInput(component);
    await waitFor(() => {
      expect(editor.getAttribute("contenteditable")).toBe("true");
    });

    // Simulate editing by clearing and typing new content
    await user.clear(editor);
    await user.type(editor, "Original message Edited");

    // Wait for content to be updated
    await waitFor(() => {
      expect(editor.textContent).toBe("Original messageOriginal message Edited");
    });

    // Press Enter to submit the edit
    await user.keyboard("{Enter}");

    // Wait for the edit to be accepted
    await waitFor(() => {
      expect(onAcceptEdit).toHaveBeenCalled();
    });
  });

  test("cancel edit functionality works", async () => {
    const user = userEvent.setup();
    const { getByText } = component;

    // Wait for component to be fully rendered
    await waitFor(() => {
      const editButton = component.queryByTestId("edit-message-button");
      expect(editButton).toBeInTheDocument();
    });

    // Give extra time for async initialization
    await new Promise((resolve) => setTimeout(resolve, 500));

    // Click edit button to start editing
    const editButton = component.getByTestId("edit-message-button");
    expect(editButton).toBeInTheDocument();
    expect(editButton).not.toBeDisabled();

    editButton.click();

    // Wait for the callback to be called
    await waitFor(() => {
      expect(onStartEdit).toHaveBeenCalled();
    });

    // Wait for editor to be ready and focused
    const editor = await EditorTestKit.waitForRichTextInput(component);
    await waitFor(() => {
      expect(editor.getAttribute("contenteditable")).toBe("true");
    });

    // Simulate editing
    await user.type(editor, " Edited");

    // Cancel edit
    await user.keyboard("{Escape}");

    // Wait for cancel to be processed
    await waitFor(() => {
      expect(onCancelEdit).toHaveBeenCalled();
    });

    // Verify the original message is still displayed
    expect(getByText("Original message")).toBeInTheDocument();
  });

  test("edit button is not visible when fullFeatured flag is false", async () => {
    if (component) {
      component.unmount();
    }

    // Create a test kit with limited features
    const limitedTestKit = ChatModelTestKit.withLimitedFeatures();

    component = render(UserMessageTest, {
      props: {
        chatModel: limitedTestKit.chatModel,
        msg: "Hello",
        requestId: "123",
        richTextJsonRepr: undefined,
        testKit: limitedTestKit,
      },
    });

    await waitFor(() => {
      const editButton = component.queryByTestId("edit-message-button");
      expect(editButton).toBeNull();
    });

    const contextBar = component.queryByTestId("user-msg-context");
    expect(contextBar).not.toBeInTheDocument();

    // Clean up the limited test kit
    limitedTestKit.dispose();
  });

  test("copy request ID button is visible when debug features are enabled", async () => {
    component.unmount();

    // Create a test kit with debug features enabled
    const debugTestKit = ChatModelTestKit.withDebugFeatures();

    component = render(UserMessageTest, {
      props: {
        chatModel: debugTestKit.chatModel,
        msg: "Hello",
        requestId: "123",
        richTextJsonRepr: undefined,
        onStartEdit,
        onAcceptEdit,
        onCancelEdit,
        testKit: debugTestKit,
      },
    });

    await waitFor(() => {
      const copyButton = component.queryByTestId("copy-request-id-button");
      expect(copyButton).toBeInTheDocument();
    });

    // Clean up
    debugTestKit.dispose();
  });

  test("copy request ID button is not visible when debug features are disabled", async () => {
    component.unmount();

    // Create a test kit with debug features explicitly disabled
    const noDebugTestKit = ChatModelTestKit.withFlags({
      enableDebugFeatures: false,
      fullFeatured: true,
      enableEditableHistory: true,
    });

    component = render(UserMessageTest, {
      props: {
        chatModel: noDebugTestKit.chatModel,
        msg: "Hello",
        requestId: "123",
        richTextJsonRepr: undefined,
        onStartEdit,
        onAcceptEdit,
        onCancelEdit,
        testKit: noDebugTestKit,
      },
    });

    await waitFor(() => {
      const copyButton = component.queryByTestId("copy-request-id-button");
      expect(copyButton).not.toBeInTheDocument();
    });

    // Clean up
    noDebugTestKit.dispose();
  });
});
