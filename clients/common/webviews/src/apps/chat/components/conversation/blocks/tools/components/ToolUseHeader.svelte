<script lang="ts">
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import { getCollapsibleContext } from "$common-webviews/src/design-system/components/CollapsibleAugment/context";
  import { formatInputArgs } from "./tool-util";
  import { getToolUseContext } from "../tool-context";
  import ToolUseStatus from "../ToolUseStatus.svelte";
  import CollapsibleIcon from "../../../../CollapsibleIcon.svelte";

  const ctx = getToolUseContext();
  export let toolName: string | undefined = undefined;
  export let toolUseInput: Record<string, unknown> = $ctx.toolUseInput;
  export let formattedToolArgs: string[] | undefined = undefined;
  let className: string = "";
  export { className as class };

  // If formattedToolArgs is provided, create a toolUseInput with args
  $: if (formattedToolArgs) {
    toolUseInput = { args: formattedToolArgs };
  }
  const collapseCtx = getCollapsibleContext();
  const collapsed = collapseCtx.collapsed;
  const expandable = collapseCtx.expandable;
</script>

<div
  class="c-tooluse__main-bar {className}"
  class:is-collapsed={$collapsed}
  class:is-expandable={$expandable}
>
  <div class="c-tooluse-header__label">
    {#if $$slots.icon}
      <CollapsibleIcon expandable={$expandable} collapsed={$collapsed}>
        <slot name="icon" slot="icon" />
      </CollapsibleIcon>
    {/if}

    <TextAugment size={1} weight="medium" color="primary" class="c-tooluse__content-btn-name">
      <slot name="toolName">
        {#if toolName}
          <span class="c-tooluse__tool-name-text">{toolName}</span>
        {/if}
      </slot>
    </TextAugment>
    {#if $$slots.secondary}
      <div class="c-tooluse__content-secondary-2">
        <slot name="secondary" {toolName} {formattedToolArgs} />
      </div>
    {:else if formatInputArgs(toolUseInput)}
      <div class="c-tooluse__content-secondary-2">
        <TextAugment size={1} class="c-tooluse__content-btn-args">
          {formatInputArgs(toolUseInput)}
        </TextAugment>
      </div>
    {/if}
    <slot name="toolAction"><span></span></slot>
    <div class="c-tooluse__main-bar__tool-status">
      <div class="c-tooluse__header-actions">
        <slot name="header-actions"></slot>
      </div>
      <slot name="toolStatus">
        <ToolUseStatus />
      </slot>
    </div>
  </div>
</div>

<style>
  .c-tooluse__main-bar {
    max-width: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    white-space: nowrap;
    color: var(--augment-text-color);
    flex: 1;
    min-width: 0;
    /** rely on secondary spacing to make alignment work nicely*/
    gap: 0;
    justify-content: center;
    & .c-tooluse-header__label {
      max-width: 100%;
      display: flex;
      align-items: center;
      gap: var(--ds-spacing-1);
      position: relative;
      min-width: 0;
      /** fix labels text overflow so it ellipsis correctly   */
      --l-tooltip-trigger-width: none;

      /** empty elements can cause unnecessary spacing */
      & > *:empty {
        display: none;
      }
    }

    & :global(.c-tooluse__content-btn-name) {
      display: inline-flex;
      align-items: center;
      height: 100%;
    }
    &.is_expandable :global(.c-tooluse__content-btn),
    &.is_expandable :global(.c-tooluse__content-btn:hover),
    &.is_expandable :global(.c-tooluse__content-btn:focus),
    &.is_expandable :global(.c-tooluse__content-btn:active) {
      pointer-events: unset;
    }
    /* The content button should take up the remaining space */
    & :global(.c-tooluse__content-btn),
    :global(.c-tooluse__content-btn:hover),
    :global(.c-tooluse__content-btn:focus),
    :global(.c-tooluse__content-btn:active) {
      pointer-events: none;
      flex: 1;
      overflow: hidden;
      display: flex;
      align-items: center;
      gap: var(--ds-spacing-1);
      color: var(--ds-color-neutral-12);
      background-color: transparent;
      border: none;
      padding: 0;
      cursor: default;
      outline: none;

      /* Needed to ensure the content button doesn't overflow */
      & :global(.c-button--content) {
        max-width: 100%;
        justify-content: flex-start;
        flex: 1;
      }

      & :global(.c-tooluse__content-btn-args) {
        display: block;
        overflow: hidden;
        text-overflow: ellipsis;
        color: var(--augment-text-color-secondary);
      }
    }
    & .c-tooluse__content-secondary-2 {
      min-width: 0;
      overflow: hidden;
      color: var(--augment-text-color-secondary);
      line-clamp: 1;
      line-height: 100%;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
      text-overflow: ellipsis;
    }
    & :global(.c-tooluse__content-secondary) {
      flex: 1;
      min-width: 0;
      overflow: hidden;
      color: var(--augment-text-color-secondary);
      line-clamp: 2;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      display: -webkit-box;
      padding-left: calc(var(--ds-spacing-4) + var(--ds-spacing-1));
      padding-top: var(--ds-spacing-1);
    }
  }
  .c-tooluse__main-bar__tool-status {
    flex: 0 0 auto;
    display: flex;
    justify-content: flex-end;
    flex-shrink: 0;
    align-items: center;
    flex-wrap: nowrap;
    gap: var(--ds-spacing-2);
    margin-left: auto;
  }

  .c-tooluse__header-actions {
    display: flex;
    gap: var(--ds-spacing-2);
    align-items: center;
    width: max-content;
    &:empty {
      display: none;
    }
  }

  .c-tooluse__tool-name {
    display: flex;
    align-items: center;
    height: 100%;
    min-width: 0;
    overflow: hidden;
    flex: 1;
    & :global(.c-tooluse__content-btn-name) {
      overflow: hidden;
      width: 100%;
      min-width: 0;
    }
    & :global(.c-tooluse__tool-name-text) {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      display: inline-block;
      width: 100%;
    }
  }
  .c-tooluse__tool-name-2 {
    display: flex;
    align-items: center;
    height: 100%;
    min-width: 0;
    overflow: hidden;
    & :global(.c-tooluse__content-btn-name) {
      overflow: hidden;
      width: 100%;
      min-width: 0;
    }
    & :global(.c-tooluse__tool-name-text) {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      display: block;
      width: 100%;
    }
  }

  .c-tooluse__icon-wrapper-circle {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
  }

  .c-tooluse__icon-slot {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
  }
</style>
