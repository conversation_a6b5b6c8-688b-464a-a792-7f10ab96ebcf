<script lang="ts">
  import BaseToolComponent from "../BaseToolComponent.svelte";
  import SupabaseIcon from "$common-webviews/src/design-system/icons/supabase.svelte";
  import ToolUseHeader from "../ToolUseHeader.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";

  export let toolUseInput: Record<string, unknown> = {};

  // Extract only the summary field for display
  $: summary = toolUseInput.summary || "";
</script>

<BaseToolComponent showToolOutput={false}>
  <ToolUseHeader slot="header" toolName="Supabase" {toolUseInput}>
    <SupabaseIcon slot="icon" />
    <span slot="secondary" class="c-supabase-tool__summary">
      <TextAugment size={1}>{summary}</TextAugment>
    </span>
  </ToolUseHeader>
</BaseToolComponent>

<style>
  .c-supabase-tool__summary {
    display: contents;
  }
</style>
