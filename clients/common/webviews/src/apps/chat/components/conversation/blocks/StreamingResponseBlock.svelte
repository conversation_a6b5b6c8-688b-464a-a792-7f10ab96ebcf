<script lang="ts">
  import {
    ExchangeStatus,
    SeenState,
    type IChatItemResponseMessage,
    type IChatItemSeenState,
  } from "../../../types/chat-message";
  import ChatMessage from "../../ChatMessage.svelte";
  import { onMount } from "svelte";
  import Markdown from "$common-webviews/src/common/components/markdown/Markdown.svelte";
  import Codeblock from "$common-webviews/src/common/components/markdown/Codeblock.svelte";
  import Codespan from "$common-webviews/src/common/components/markdown/Codespan.svelte";
  import Link from "$common-webviews/src/apps/chat/components/markdown-ext/Link.svelte";
  import GeneratingResponse from "../status-decorations/GeneratingResponse.svelte";
  import type { Renderers } from "@magidoc/plugin-svelte-marked";
  import {
    type MarkdownBlock,
    flattenMarkdownBlocks,
    markdownBlocksIterator,
    MarkdownPlainText,
  } from "$common-webviews/src/common/components/markdown/MarkdownTypes";

  export let turn: IChatItemResponseMessage & IChatItemSeenState;
  export let preamble: string = "";
  export let resendTurn: (() => void) | undefined = undefined;
  export let markdownBlocks: MarkdownBlock[] = [];

  let renderers: Renderers = {
    code: Codeblock,
    codespan: Codespan,
    link: Link,
  };

  $: turn.response_text = turn.response_text ?? "";
  $: responseBlocks = turn.response_text
    ? [new MarkdownPlainText(turn.response_text)]
    : markdownBlocks;
  $: displayedText = turn.response_text ?? "";

  onMount(() => {
    if (turn.seen_state === SeenState.seen) {
      turn.response_text = flattenMarkdownBlocks(responseBlocks);
      return;
    }

    let start = Date.now();
    const iterator = markdownBlocksIterator(responseBlocks);
    let currentBlock = iterator.next();

    const updateText = () => {
      let now = Date.now();
      const chars = Math.round((now - start) / 8);
      let newText = "";

      for (let i = 0; i < chars; i++) {
        if (currentBlock.done) {
          break;
        }
        newText += currentBlock.value;
        currentBlock = iterator.next();
      }

      turn.response_text += newText;
      start = now;

      if (!currentBlock.done) {
        requestAnimationFrame(updateText);
      }
    };
    updateText();
  });
</script>

<ChatMessage isAugment>
  <svelte:fragment slot="content">
    {#if (!turn.status || turn.status === ExchangeStatus.sent) && displayedText.length <= 0}
      <GeneratingResponse />
    {:else if turn.status === ExchangeStatus.failed}
      <div class="c-msg-failure">
        {#if turn.display_error_message}
          {turn.display_error_message}
        {:else if turn.response_text && turn.response_text.length > 0}
          Connection lost.{#if resendTurn}
            Please
            <button on:click|preventDefault={resendTurn}>try again</button>.{/if}
        {:else}
          We encountered an issue sending your message.{#if resendTurn}
            Please
            <button on:click|preventDefault={resendTurn}>try again</button>{/if}.
        {/if}
      </div>
    {:else}
      <Markdown {renderers} markdown={preamble + displayedText} />
      <!-- Wait until the response is successfully received and fully displayed before we render the slot -->
      {#if (!turn.status || turn.status === ExchangeStatus.success) && displayedText === flattenMarkdownBlocks(responseBlocks)}
        <slot />
      {/if}
    {/if}
  </svelte:fragment>
</ChatMessage>

<style>
  .c-msg-failure {
    font-style: italic;
  }
  .c-msg-failure > button {
    all: unset;
    color: var(--augment-chat-message-note-color);
    text-decoration: underline;
    cursor: pointer;
  }
</style>
