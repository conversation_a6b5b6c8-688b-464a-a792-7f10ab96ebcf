<script lang="ts">
  import type { FileDetails } from "$vscode/src/webview-providers/webview-messages";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import ToolUseArgFilePath from "./ToolUseArgFilePath.svelte";
  import { getChatModel } from "$common-webviews/src/apps/chat/chat-context";

  export let value: any;

  const chatModel = getChatModel();

  const onOpenLocalFile = (details: FileDetails) => {
    chatModel?.extensionClient.openFile(details);
  };
</script>

{#if typeof value === "string"}
  {#await chatModel?.extensionClient.resolvePath?.({ rootPath: "", relPath: value })}
    <TextAugment size={1}>{value}</TextAugment>
  {:then maybeFileDetails}
    {#if maybeFileDetails}
      <ToolUseArgFilePath fileDetails={maybeFileDetails} {onOpenLocalFile} />
    {:else}
      <TextAugment size={1}>{value}</TextAugment>
    {/if}
  {:catch}
    <TextAugment size={1}>{value}</TextAugment>
  {/await}
{:else}
  <TextAugment size={1}>{value}</TextAugment>
{/if}
