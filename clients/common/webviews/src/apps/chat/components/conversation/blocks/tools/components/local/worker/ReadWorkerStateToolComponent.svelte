<script lang="ts">
  import BaseToolComponent from "../../BaseToolComponent.svelte";
  import ToolUseHeader from "../../ToolUseHeader.svelte";
  import InfoIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/circle-info.svg?component";
  import {
    ToolUsePhase,
    type ToolUseState,
  } from "$common-webviews/src/apps/chat/types/tool-use-state";
  import ShellError from "../../sidecar/ShellError.svelte";
  import StatusBadgeAugment from "$common-webviews/src/design-system/components/StatusBadgeAugment.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import CardAugment from "$common-webviews/src/design-system/components/CardAugment.svelte";
  import ToolResultFields from "./ToolResultFields.svelte";

  export let toolUseInput: Record<string, unknown> = {};
  export let toolUseState: ToolUseState;
  let collapsed = true;

  // Extract worker agent IDs array from input
  $: workerAgentIds = Array.isArray(toolUseInput.worker_agent_ids)
    ? toolUseInput.worker_agent_ids
    : [];

  // Format tool arguments for display - show count of agents in collapsed state
  $: formattedArgs = (() => {
    const args: string[] = [];
    if (workerAgentIds.length > 0) {
      args.push(`${workerAgentIds.length} agent${workerAgentIds.length === 1 ? "" : "s"}`);
    } else {
      args.push("all agents");
    }
    return args;
  })();

  // Parse the tool result to get worker state results
  $: workerStates = (() => {
    if (
      toolUseState.phase === ToolUsePhase.completed &&
      toolUseState.result?.text &&
      !toolUseState.result.isError
    ) {
      try {
        const response = JSON.parse(toolUseState.result.text);
        // Combine successful and failed reads into a single array
        const successful = Array.isArray(response.workerStates) ? response.workerStates : [];
        const failed = Array.isArray(response.failedReads) ? response.failedReads : [];
        return [...successful, ...failed];
      } catch (e) {
        console.error("Failed to parse read worker state tool response:", e);
      }
    }
    return [];
  })();

  // Count successful and failed reads
  $: successCount = workerStates.filter((result: any) => result.success).length;
  $: failureCount = workerStates.filter((result: any) => !result.success).length;
  $: hasResults = workerStates.length > 0;

  // Helper function to get status color
  function getStatusColor(status: string): "success" | "info" | "warning" | "error" | "neutral" {
    switch (status?.toLowerCase()) {
      case "running":
      case "active":
        return "success";
      case "idle":
      case "waiting":
        return "info";
      case "stopped":
      case "paused":
        return "warning";
      case "error":
      case "failed":
        return "error";
      default:
        return "neutral";
    }
  }
</script>

<BaseToolComponent bind:collapsed>
  <ToolUseHeader slot="header" toolName="Read Worker States" formattedToolArgs={formattedArgs}>
    <InfoIcon slot="icon" />
    <svelte:fragment slot="secondary">
      {#if hasResults && collapsed}
        <div class="c-read-worker-state-tool__counts">
          {#if successCount > 0}
            <StatusBadgeAugment color="success" size={1}>
              {successCount} read
            </StatusBadgeAugment>
          {/if}
          {#if failureCount > 0}
            <StatusBadgeAugment color="error" size={1}>
              {failureCount} failed
            </StatusBadgeAugment>
          {/if}
        </div>
      {/if}
    </svelte:fragment>
  </ToolUseHeader>

  <div slot="details" class="c-read-worker-state-tool__details">
    {#if toolUseState.phase === ToolUsePhase.running}
      <div class="c-read-worker-state-tool__running">
        <TextAugment size={1} color="neutral">Reading worker agent states...</TextAugment>
      </div>
    {:else if toolUseState.phase === ToolUsePhase.completed && hasResults}
      <div class="c-read-worker-state-tool__results">
        {#each workerStates as result, index}
          <CardAugment class="c-read-worker-state-tool__worker-card">
            <div class="c-read-worker-state-tool__worker-header">
              <div class="c-read-worker-state-tool__worker-title">
                <TextAugment size={1} weight="medium">
                  Agent {result.agentId || `${index + 1}`}
                </TextAugment>
                <StatusBadgeAugment color={result.success ? "success" : "error"} size={1}>
                  {result.success ? "Active" : "Error"}
                </StatusBadgeAugment>
              </div>
            </div>

            <div class="c-read-worker-state-tool__worker-details">
              {#if result.success}
                <div class="c-read-worker-state-tool__success-details">
                  {#if result.status}
                    <div class="c-read-worker-state-tool__status-row">
                      <TextAugment size={1} color="neutral">
                        <strong>Status:</strong>
                      </TextAugment>
                      <StatusBadgeAugment color={getStatusColor(result.status)} size={1}>
                        {result.status}
                      </StatusBadgeAugment>
                    </div>
                  {/if}

                  <ToolResultFields
                    data={result}
                    fields={[
                      {
                        key: "lastExchangeSummary",
                        label: "Latest Exchange",
                        type: "monospace",
                        collapsible: true,
                      },
                    ]}
                  />

                  <ToolResultFields
                    data={result}
                    fields={[
                      {
                        key: "sessionSummary",
                        label: "Session Summary",
                        type: "monospace",
                        collapsible: true,
                      },
                    ]}
                  />
                </div>
              {:else}
                <div class="c-read-worker-state-tool__error-details">
                  <TextAugment size={1} color="error">
                    <strong>Error:</strong>
                    {result.error || "Unknown error"}
                  </TextAugment>
                </div>
              {/if}
            </div>
          </CardAugment>
        {/each}
      </div>
    {:else if toolUseState.phase === ToolUsePhase.completed && !hasResults}
      <div class="c-read-worker-state-tool__no-results">
        <TextAugment size={1} color="neutral">No worker agent states were read.</TextAugment>
      </div>
    {/if}
  </div>

  {#if toolUseState.phase === ToolUsePhase.error}
    <ShellError slot="error" {...toolUseState.result ?? {}} />
  {/if}
</BaseToolComponent>

<style>
  .c-read-worker-state-tool__counts {
    display: flex;
    gap: var(--ds-spacing-2);
    align-items: center;
    flex-wrap: wrap;
  }

  .c-read-worker-state-tool__details {
    padding: var(--ds-spacing-2);
  }

  .c-read-worker-state-tool__results {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-2);
  }

  .c-read-worker-state-tool__worker-header {
    margin-bottom: var(--ds-spacing-2);
  }

  .c-read-worker-state-tool__worker-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: var(--ds-spacing-2);
  }

  .c-read-worker-state-tool__worker-details {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-2);
  }

  .c-read-worker-state-tool__status-row {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-2);
  }
</style>
