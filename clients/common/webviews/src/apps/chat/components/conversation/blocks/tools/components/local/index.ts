import ReadFileToolComponent from "./ReadFileToolComponent.svelte";
import SaveFileToolComponent from "./SaveFileToolComponent.svelte";
import RememberToolComponent from "./RememberToolComponent.svelte";
import DiagnosticsToolComponent from "./DiagnosticsToolComponent.svelte";
import GitCommitRetrievalToolComponent from "./GitCommitRetrievalToolComponent.svelte";
import MemoryRetrievalToolComponent from "./MemoryRetrievalToolComponent.svelte";
import { LocalToolType } from "$vscode/src/webview-providers/tool-types";
import ShellToolComponent from "../sidecar/ShellToolComponent.svelte";
import { StrReplaceEditorToolComponent, OpenInBrowserToolComponent } from "../sidecar";
import SetupScriptToolComponent from "./SetupScriptToolComponent.svelte";
import StartWorkerAgentToolComponent from "./worker/StartWorkerAgentToolComponent.svelte";
import SendInstructionToWorkerAgentToolComponent from "./worker/SendInstructionToWorkerAgentToolComponent.svelte";
import ReadWorkerStateToolComponent from "./worker/ReadWorkerStateToolComponent.svelte";
import WaitForWorkerAgentToolComponent from "./worker/WaitForWorkerAgentToolComponent.svelte";
import StopWorkerAgentToolComponent from "./worker/StopWorkerAgentToolComponent.svelte";
import DeleteWorkerAgentToolComponent from "./worker/DeleteWorkerAgentToolComponent.svelte";
import ReadWorkerAgentEditsToolComponent from "./worker/ReadWorkerAgentEditsToolComponent.svelte";
import ApplyWorkerAgentEditsToolComponent from "./worker/ApplyWorkerAgentEditsToolComponent.svelte";

// Map of tool types to their components
export const localToolComponents = {
  [LocalToolType.editFile]: StrReplaceEditorToolComponent,
  [LocalToolType.readFile]: ReadFileToolComponent,
  [LocalToolType.saveFile]: SaveFileToolComponent,
  [LocalToolType.launchProcess]: ShellToolComponent,
  [LocalToolType.killProcess]: ShellToolComponent,
  [LocalToolType.readProcess]: ShellToolComponent,
  [LocalToolType.writeProcess]: ShellToolComponent,
  [LocalToolType.listProcesses]: ShellToolComponent,
  [LocalToolType.waitProcess]: ShellToolComponent,
  [LocalToolType.openBrowser]: OpenInBrowserToolComponent,
  [LocalToolType.remember]: RememberToolComponent,
  [LocalToolType.diagnostics]: DiagnosticsToolComponent,
  [LocalToolType.setupScript]: SetupScriptToolComponent,
  [LocalToolType.readTerminal]: ShellToolComponent,
  [LocalToolType.gitCommitRetrieval]: GitCommitRetrievalToolComponent,
  [LocalToolType.memoryRetrieval]: MemoryRetrievalToolComponent,
  // Agent orchestration tools
  [LocalToolType.startWorkerAgent]: StartWorkerAgentToolComponent,
  [LocalToolType.sendInstructionToWorkerAgent]: SendInstructionToWorkerAgentToolComponent,
  [LocalToolType.readWorkerState]: ReadWorkerStateToolComponent,
  [LocalToolType.waitForWorkerAgent]: WaitForWorkerAgentToolComponent,
  [LocalToolType.stopWorkerAgent]: StopWorkerAgentToolComponent,
  [LocalToolType.deleteWorkerAgent]: DeleteWorkerAgentToolComponent,
  [LocalToolType.readWorkerAgentEdits]: ReadWorkerAgentEditsToolComponent,
  [LocalToolType.applyWorkerAgentEdits]: ApplyWorkerAgentEditsToolComponent,
  // Add more local tool components as needed
} as const;

/**
 * Returns the appropriate tool component for a given tool type.
 * @param toolName LocalToolType
 * @returns
 */
export function resolveLocalTool(toolName: LocalToolType) {
  return toolName in localToolComponents
    ? localToolComponents[toolName as keyof typeof localToolComponents]
    : undefined;
}

export { ReadFileToolComponent, SaveFileToolComponent, RememberToolComponent };
