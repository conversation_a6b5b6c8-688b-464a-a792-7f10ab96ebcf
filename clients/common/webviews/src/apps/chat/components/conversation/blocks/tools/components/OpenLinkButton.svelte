<script lang="ts">
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import OpenInNewWindow from "$common-webviews/src/design-system/icons/open-in-new-window.svelte";
  import { host } from "$common-webviews/src/common/hosts/host";
  import { WebViewMessageType } from "$vscode/src/webview-providers/webview-messages";

  export let size = 0;
  export let href: string | undefined = undefined;

  function onOpen(e: Event | undefined) {
    e?.preventDefault();
    e?.stopPropagation();
    // Use the host to send a message to open the URL in an external browser
    // This is more reliable than window.open() which might be blocked by sandbox restrictions
    host.postMessage({
      type: WebViewMessageType.augmentLink,
      data: href,
    });
  }
</script>

<span class="c-open-link-button c-open-link-button__size--{size}">
  {#if href}
    <IconButtonAugment variant="ghost-block" color="neutral" size={1} on:click={onOpen}>
      <OpenInNewWindow />
    </IconButtonAugment>
  {/if}
</span>

<style>
  .c-open-link-button {
    display: contents;
  }

  .c-open-link-button__size--0 :global(.c-open-link-button) {
    --base-btn-width: 16px;
    --base-btn-height: 16px;
    height: 16px;
    width: 16px;
  }
</style>
