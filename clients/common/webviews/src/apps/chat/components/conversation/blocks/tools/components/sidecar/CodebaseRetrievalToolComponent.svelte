<script lang="ts">
  import MaterialIcon from "$common-webviews/src/common/components/MaterialIcon.svelte";
  import { getContext } from "svelte";
  import { type ChatModel } from "$common-webviews/src/apps/chat/models/chat-model";
  import ContextEngineToolComponent from "../ContextEngineToolComponent.svelte";

  export let toolUseInput: Record<string, unknown> = {};

  const chatModel = getContext<ChatModel>("chatModel");
  $: flagsModel = chatModel?.flags;
</script>

<ContextEngineToolComponent {toolUseInput} noRetrieval={!$flagsModel?.enableCommitIndexing}>
  <svelte:fragment slot="retrieval">
    {#if $flagsModel?.enableCommitIndexing}
      <MaterialIcon iconName="code" /> Codebase
    {/if}
  </svelte:fragment>
</ContextEngineToolComponent>
