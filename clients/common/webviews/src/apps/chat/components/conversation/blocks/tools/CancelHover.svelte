<script lang="ts">
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import Cross2Icon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/xmark.svg?component";
  import { onHover, HoverContext } from "$common-webviews/src/common/actions/onHoverAction";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import { TooltipTriggerOn } from "$common-webviews/src/design-system/_primitives/TooltipAugment/types";

  export let onClick: (e: Event) => void;
  let loading = true;
  const ctx = new HoverContext({
    onHoverStart() {
      loading = false;
    },
    onHoverEnd() {
      loading = true;
    },
    hoverTriggerDuration: 0,
  });
</script>

<div class="c-cancel-hover" use:onHover={ctx}>
  <TextTooltipAugment triggerOn={[TooltipTriggerOn.Hover]} content="Cancel">
    <IconButtonAugment
      size={0}
      color="neutral"
      on:click={onClick}
      variant="ghost-block"
      class="c-cancel-hover__btn"
      {loading}
    >
      <Cross2Icon />
    </IconButtonAugment>
  </TextTooltipAugment>
</div>

<style>
  .c-cancel-hover {
    display: contents;
    /** the button is disabled when loading, but we want to show the cursor so it doesn't flash when hovering */
    & :global(.c-base-btn:disabled) {
      cursor: pointer;
    }
  }
</style>
