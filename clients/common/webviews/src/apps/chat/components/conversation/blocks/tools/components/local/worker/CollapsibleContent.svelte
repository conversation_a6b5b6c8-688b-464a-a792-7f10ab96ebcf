<script lang="ts">
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";

  export let label: string;
  export let expanded: boolean = false;
  export let maxHeight: string = "300px";
  export let showBorder: boolean = true;
  export let contentClass: string = "";

  // Unique ID for this collapsible section
  const sectionId = `collapsible-${Math.random().toString(36).substring(2, 11)}`;

  function toggle() {
    expanded = !expanded;
  }
</script>

<div class="c-collapsible-content">
  <button
    class="c-collapsible-content__toggle"
    on:click={toggle}
    aria-expanded={expanded}
    aria-controls={sectionId}
  >
    <TextAugment size={1} color="neutral">
      <strong>{label}:</strong>
      <span class="c-collapsible-content__toggle-icon">
        {expanded ? "▼" : "▶"}
      </span>
    </TextAugment>
  </button>

  {#if expanded}
    <div
      class="c-collapsible-content__display {contentClass}"
      id={sectionId}
      style="max-height: {maxHeight};"
    >
      <div class="c-collapsible-content__inner" class:bordered={showBorder}>
        <slot />
      </div>
    </div>
  {/if}
</div>

<style>
  .c-collapsible-content__toggle {
    background: none;
    border: none;
    padding: 0;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-2);
    width: 100%;
    text-align: left;
  }

  .c-collapsible-content__toggle:hover {
    opacity: 0.8;
  }

  .c-collapsible-content__toggle-icon {
    font-size: 0.8em;
    color: var(--ds-color-neutral-500);
    margin-left: var(--ds-spacing-2);
  }

  .c-collapsible-content__display {
    margin-top: var(--ds-spacing-2);
    overflow-y: auto;
  }

  .c-collapsible-content__inner {
    padding: var(--ds-spacing-2);
    background-color: var(--ds-color-neutral-100);
    border-radius: var(--ds-border-radius-1);
    white-space: pre-wrap;
    word-break: break-word;
  }

  .c-collapsible-content__inner.bordered {
    border: 1px solid var(--ds-color-neutral-200);
  }
</style>
