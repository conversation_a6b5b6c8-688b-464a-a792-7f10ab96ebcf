<script lang="ts">
  import {
    type IRemoteAgentsError,
    type ISendMessageError,
    SendMessageErrorType,
  } from "$common-webviews/src/apps/remote-agent-manager/models/remote-agents-state-model/remote-agents-state-model";
  import { startCountdown } from "$common-webviews/src/apps/remote-agent-manager/utils";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import { onDestroy, onMount } from "svelte";

  export let error: IRemoteAgentsError | ISendMessageError;
  export let onRetry: (() => void) | undefined = undefined;
  export let onDelete: (() => void) | undefined = undefined;

  // Check if this is a sendMessage error or regular remote agent error
  $: isSendMessageError = "type" in error;
  $: isAgentFailedError =
    isSendMessageError && (error as ISendMessageError).type === SendMessageErrorType.agentFailed;

  $: errorMessage = error.errorMessage;
  $: retryAt = isSendMessageError ? undefined : (error as IRemoteAgentsError).retryAt;
  $: showTimer = retryAt !== undefined;
  $: showDeleteButton = isAgentFailedError && onDelete;
  $: showRetryButton = onRetry;

  let displayTime: string | undefined = undefined;
  let stopCountdown: (() => void) | undefined;

  function restartCountdown() {
    if (stopCountdown) {
      stopCountdown();
      stopCountdown = undefined;
    }

    if (!retryAt) {
      return;
    }

    stopCountdown = startCountdown(retryAt, (countdown) => {
      displayTime = countdown;
    });
  }

  $: if (retryAt) {
    restartCountdown();
  }

  onMount(restartCountdown);
  onDestroy(() => {
    stopCountdown?.();
  });
</script>

<div class="c-remote-agent-error" class:c-remote-agent-error--unrecoverable={isAgentFailedError}>
  <div class="c-remote-agent-error__content">
    <span class="c-remote-agent-error__message">
      {errorMessage}
      {#if showTimer && displayTime}
        Retrying in {displayTime}
      {/if}
    </span>
    <div class="c-remote-agent-error__actions">
      {#if showRetryButton}
        <ButtonAugment
          class="c-remote-agent-error__button c-remote-agent-error__button--retry"
          variant="soft"
          color="neutral"
          size={1}
          on:click={(e) => {
            e.preventDefault();
            onRetry?.();
          }}
        >
          Retry{showTimer ? " now" : ""}
        </ButtonAugment>
      {/if}
      {#if showDeleteButton}
        <ButtonAugment
          class="c-remote-agent-error__button c-remote-agent-error__button--delete"
          variant="soft"
          color="error"
          size={1}
          on:click={(e) => {
            e.preventDefault();
            onDelete?.();
          }}
        >
          Delete Agent
        </ButtonAugment>
      {/if}
    </div>
  </div>
</div>

<style>
  .c-remote-agent-error {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: var(--ds-spacing-1);
    padding: var(--ds-spacing-1) var(--ds-spacing-2);
  }

  .c-remote-agent-error__message {
    opacity: 50%;
  }

  .c-remote-agent-error__content {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    gap: var(--ds-spacing-2);
    flex-wrap: wrap;
    flex: 1;
  }
</style>
