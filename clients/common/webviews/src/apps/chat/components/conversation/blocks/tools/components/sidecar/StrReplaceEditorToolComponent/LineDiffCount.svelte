<script lang="ts">
  import { getChatModel } from "$common-webviews/src/apps/chat/chat-context";
  import { getToolUseContext } from "../../../tool-context";
  import { ToolUsePhase } from "$common-webviews/src/apps/chat/types/tool-use-state";
  import EditChangeSummary from "$common-webviews/src/apps/chat/components/agent-edits/EditChangeSummary.svelte";
  import { visibilityObserverOnce } from "$common-webviews/src/apps/chat/components/actions/trackOnScreen";

  const ctx = getToolUseContext();
  const chatModel = getChatModel();

  export let requestId: string;
  let hasLoaded = false;
  // Should only *request* to load a single time, gated on it having been visible not loaded, and in a terminal state.
  let hasBeenVisible = false;
  $: toolUseInput = $ctx.toolUseInput;
  $: toolUseState = $ctx.toolUseState;
  $: totalAddedLines = toolUseInput.added as number;
  $: totalRemovedLines = toolUseInput.removed as number;

  $: canLoad = toolUseState.phase === ToolUsePhase.completed && !hasLoaded && hasBeenVisible;
  // Watch for phase transitions to completed
  $: if (canLoad) {
    void loadChanges(requestId);
  }

  async function loadChanges(rid: string) {
    try {
      const changes = await chatModel?.extensionClient?.getAgentEditChangesByRequestId(rid);
      if (changes) {
        totalAddedLines = changes.totalAddedLines ?? 0;
        totalRemovedLines = changes.totalRemovedLines ?? 0;
        hasLoaded = true;
      }
    } catch (e) {
      console.warn(`Loading changes failed for ${rid}: `, e);
    }
  }

  $: {
    // When any of these change, reset the loaded state
    requestId, toolUseInput, toolUseState;
    hasLoaded = false;
  }
</script>

<div
  class="c-line-diff-count"
  use:visibilityObserverOnce={{
    onVisible: () => (hasBeenVisible = true),
    scrollTarget: document.body,
  }}
>
  <EditChangeSummary
    loading={!hasLoaded}
    {totalAddedLines}
    {totalRemovedLines}
    size={0}
    tooltips={true}
  />
</div>

<style>
  .c-line-diff-count {
    display: flex;
    flex-direction: row;
    gap: var(--ds-spacing-2);
    align-items: center;
  }
</style>
