<script lang="ts">
  import BaseToolComponent from "../../BaseToolComponent.svelte";
  import ToolUseHeader from "../../ToolUseHeader.svelte";
  import DiagramNextIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/diagram-next.svg?component";
  import { type ToolUseState } from "$common-webviews/src/apps/chat/types/tool-use-state";
  import ShellError from "../ShellError.svelte";
  import StatusBadgeAugment from "$common-webviews/src/design-system/components/StatusBadgeAugment.svelte";
  import TaskDiffRenderer from "./TaskDiffRenderer.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import { getTaskDiffCounts } from "@augment-internal/sidecar-libs/src/agent/task/task-utils";
  import { getContext } from "svelte";
  import {
    CurrentConversationTaskStore,
    type ICurrentConversationTaskStore,
  } from "$common-webviews/src/apps/chat/models/task-store";
  import { createTaskRefreshOnCompletion } from "./useTaskRefreshOnCompletion";

  // Required by the component API but not used in this component
  export const toolUseInput: Record<string, unknown> = {};
  export let toolUseState: ToolUseState;
  let collapsed = true;

  // Refresh task store when tool reaches terminal state
  const taskStore = getContext<ICurrentConversationTaskStore>(CurrentConversationTaskStore.key);
  const refreshOnCompletion = createTaskRefreshOnCompletion(taskStore);
  $: refreshOnCompletion(toolUseState);

  // Check if we have task result text
  $: hasTaskResult = toolUseState.result?.text && !toolUseState.result.isError;
  $: resultText = toolUseState.result?.text || "";

  // Extract individual counts for display using utils
  $: taskCounts = hasTaskResult
    ? getTaskDiffCounts(resultText)
    : { created: 0, updated: 0, deleted: 0 };

  // Check if we have any changes to show
  $: hasChanges = taskCounts.created > 0 || taskCounts.updated > 0 || taskCounts.deleted > 0;
</script>

<BaseToolComponent bind:collapsed>
  <ToolUseHeader slot="header" toolName="Add Tasks">
    <DiagramNextIcon slot="icon" />
    <svelte:fragment slot="secondary">
      {#if hasTaskResult && collapsed}
        <div class="c-add-tasks-tool__counts">
          {#if taskCounts.created > 0}
            <StatusBadgeAugment color="success" size={1}>
              {taskCounts.created} created
            </StatusBadgeAugment>
          {/if}
          {#if taskCounts.updated > 0}
            <StatusBadgeAugment color="info" size={1}>
              {taskCounts.updated} updated
            </StatusBadgeAugment>
          {/if}
          {#if taskCounts.deleted > 0}
            <StatusBadgeAugment color="warning" size={1}>
              {taskCounts.deleted} deleted
            </StatusBadgeAugment>
          {/if}
          {#if taskCounts.created === 0 && taskCounts.updated === 0 && taskCounts.deleted === 0}
            <StatusBadgeAugment color="info" size={1}>No changes</StatusBadgeAugment>
          {/if}
        </div>
      {/if}
    </svelte:fragment>
  </ToolUseHeader>

  <!-- Show task diff when available -->
  <div slot="details" class="c-add-tasks-tool__details">
    {#if hasTaskResult && hasChanges}
      <TaskDiffRenderer text={resultText} />
    {:else if hasTaskResult && !hasChanges}
      <div class="c-add-tasks-tool__no-diff">
        <TextAugment size={1} color="neutral">No task changes to display</TextAugment>
      </div>
    {/if}
  </div>
  <ShellError slot="error" {...toolUseState.result ?? {}} hideDetailedMessage={true} />
</BaseToolComponent>

<style>
  .c-add-tasks-tool__details {
    padding: var(--ds-spacing-2);
  }

  .c-add-tasks-tool__counts {
    display: flex;
    gap: var(--ds-spacing-2);
    align-items: center;
    flex-wrap: wrap;
  }

  .c-add-tasks-tool__no-diff {
    padding: var(--ds-spacing-2);
    text-align: center;
    background-color: var(--ds-color-neutral-a1);
    border-radius: var(--ds-radius-2);
    border: 1px solid var(--ds-color-neutral-a4);
  }
</style>
