<script lang="ts">
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import { TooltipTriggerOn } from "$common-webviews/src/design-system/_primitives/TooltipAugment/types";
  import ArrowUpLong from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/arrow-up-long.svg?component";
  import Floating from "$common-webviews/src/common/components/Floating.svelte";

  export let onScrollToTop: () => void;
  export let isVisible: boolean = false;
</script>

{#if isVisible}
  <Floating xPos="right" yPos="below">
    <div class="c-scroll-to-top-pill">
      <TextTooltipAugment content="Scroll to top" triggerOn={[TooltipTriggerOn.Hover]}>
        <IconButtonAugment
          class="c-scroll-to-top-button"
          variant="solid"
          color="neutral"
          size={1}
          radius="full"
          on:click={onScrollToTop}
        >
          <ArrowUpLong />
        </IconButtonAugment>
      </TextTooltipAugment>
    </div>
  </Floating>
{/if}

<style>
  .c-scroll-to-top-pill {
    border-radius: 0 0 var(--ds-radius-2) var(--ds-radius-2); /* Rounded bottom corners */
    padding: var(--ds-spacing-4);
    display: flex;
    justify-content: center;
    width: auto;
    pointer-events: auto; /* Make the pill clickable */
  }

  /* Style the SVG to use currentColor and size to the button */
  :global(.c-scroll-to-top-button svg) {
    fill: var(--icon-color, currentColor);
    height: 14px;
    aspect-ratio: 1/1;
  }
</style>
