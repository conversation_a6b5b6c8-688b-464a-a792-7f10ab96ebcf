<script lang="ts">
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import DotsHorizontalIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/ellipsis.svg?component";
  import DropdownMenu from "$common-webviews/src/design-system/components/DropdownMenuAugment";
  import { type ButtonColor } from "$common-webviews/src/design-system/_primitives/BaseButton.svelte";
  import type { ItemColor } from "$common-webviews/src/design-system/components/DropdownMenuAugment/Item.svelte";

  export let name: string;
  export let buttonColor: ButtonColor;
  export let tooltipItems: {
    label: string;
    id: string;
    action: () => void;
    color?: ItemColor;
    disabled?: boolean;
  }[] = [];
  export let reverse: boolean;

  let isOpen = false;
  let requestClose: () => void = () => {};
  const handleItemClick = (item: {
    label: string;
    id: string;
    action: () => void;
    color?: ItemColor;
  }) => {
    item.action();
    requestClose();
  };
</script>

<div class="c-name" class:c-name--reversed={reverse} class:c-name--menu-open={isOpen}>
  {name}
  {#if tooltipItems.length > 0}
    <div class="c-name__menu">
      <DropdownMenu.Root bind:requestClose onOpenChange={(o) => (isOpen = o)}>
        <DropdownMenu.Trigger>
          <div class="c-name__menu-btn" data-testid="name-badge-menu-btn">
            <IconButtonAugment variant="ghost" color={buttonColor} size={1}>
              <DotsHorizontalIcon />
            </IconButtonAugment>
          </div>
        </DropdownMenu.Trigger>
        <DropdownMenu.Content size={1} side="bottom" align="end">
          {#each tooltipItems as item}
            <DropdownMenu.Item
              onSelect={() => handleItemClick(item)}
              color={item.color}
              disabled={item.disabled}
            >
              {item.label}
            </DropdownMenu.Item>
          {/each}
        </DropdownMenu.Content>
      </DropdownMenu.Root>
    </div>
  {/if}
</div>

<style>
  .c-name {
    display: inline-flex;
    align-items: center;
    flex-direction: row;
  }

  .c-name--reversed {
    flex-direction: row-reverse;
  }

  .c-name__menu {
    display: inline-block;
  }

  .c-name.c-name--menu-open,
  :global(.c-chat-message__badge) .c-name {
    gap: var(--ds-spacing-1);
  }

  .c-name__menu-btn :global(svg) {
    width: 1rem;
    height: 1rem;
  }
</style>
