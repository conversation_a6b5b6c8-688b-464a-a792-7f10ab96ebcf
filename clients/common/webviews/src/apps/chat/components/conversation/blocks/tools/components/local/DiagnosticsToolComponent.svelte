<script lang="ts">
  import BaseToolComponent from "../BaseToolComponent.svelte";
  import Warning from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/triangle-exclamation.svg?component";
  import ToolUseHeader from "../ToolUseHeader.svelte";
</script>

<BaseToolComponent>
  <ToolUseHeader slot="header" toolName="Diagnostics">
    <span slot="icon" class="c-diagnostics__header__icon"><Warning /></span>
  </ToolUseHeader>
</BaseToolComponent>

<style>
  .c-diagnostics__header__icon {
    width: var(--ds-icon-size-2);
    aspect-ratio: 1 / 1;
    & :global(svg) {
      width: 100%;
    }
  }
</style>
