<script lang="ts">
  import BaseToolComponent from "../BaseToolComponent.svelte";
  import ShellToolUseDetails from "./ShellToolUseDetails.svelte";
  import {
    ToolUsePhase,
    type ToolUseState,
  } from "$common-webviews/src/apps/chat/types/tool-use-state";
  import TerminalRectangle from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/rectangle-terminal.svg?component";
  import ToolUseHeader from "../ToolUseHeader.svelte";
  import { formatShellCommandLine } from "../tool-util";
  import { parseShellOutput } from "./shell-util";
  import ToolUseStatus from "../../ToolUseStatus.svelte";
  import ShellError from "./ShellError.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import { type ChatResultToolUse } from "@augment-internal/sidecar-libs/src/chat/chat-types";
  import OpenTerminalButton from "../OpenTerminalButton.svelte";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";

  export let toolUseState: ToolUseState;
  export let toolUseInput: Record<string, unknown> = {};
  export let toolUse: ChatResultToolUse;

  let code = formatShellCommandLine(toolUseInput);
  $: code = formatShellCommandLine(toolUseInput);
  let result = parseShellOutput(toolUseState.result?.text ?? "");
  $: result = parseShellOutput(toolUseState.result?.text ?? "");
  // Check if we have a terminal ID from the tool input or output
  $: terminalId = getTerminalId(toolUse.tool_name, toolUseInput, toolUseState.result?.text);
  function getTerminalId(
    toolName: string,
    input: Record<string, unknown>,
    outputText?: string,
  ): number | undefined {
    // For most tools, terminal_id is in the input
    if (input.terminal_id !== null && input.terminal_id !== undefined) {
      return input.terminal_id as number;
    }

    // For launch-process, parse terminal ID from output
    if (toolName === "launch-process" && outputText) {
      const match = outputText.match(/[Tt]erminal ID (\d+)/);
      if (match) {
        return parseInt(match[1], 10);
      }
    }

    return undefined;
  }

  function makeName(toolName: string, phase: ToolUsePhase) {
    const isRunning = !(phase === ToolUsePhase.completed || phase === ToolUsePhase.error);
    switch (toolName) {
      case "launch-process":
        return isRunning ? "Launching Process..." : "Launched Process";
      case "kill-process":
        return isRunning ? "Killing Process..." : "Killed Process";
      case "read-process":
        return isRunning ? "Reading from Process..." : "Read from Process";
      case "write-process":
        return isRunning ? "Writing to Process..." : "Wrote to Process";
      case "list-processes":
        return isRunning ? "Listing Processes" : "Listed Processes";
      case "wait-process":
        return isRunning ? "Waiting for Process..." : "Waited for Process";
      case "read-terminal":
        return isRunning ? "Reading from Terminal..." : "Read from Terminal";
    }
    return "";
  }
</script>

<BaseToolComponent>
  <ToolUseHeader slot="header" toolName="Terminal">
    <TerminalRectangle slot="icon" />

    <svelte:fragment slot="secondary">
      {#if code}
        <TextTooltipAugment content={code} class="c-shell-tool-component__secondary">
          <TextAugment size={1} type="monospace" color="secondary">
            $&nbsp;{code.replace(/^\s*\$/, "").trim()}
          </TextAugment>
        </TextTooltipAugment>
      {:else}
        <TextAugment size={1} color="secondary"
          >{makeName(toolUse.tool_name, toolUseState.phase)}</TextAugment
        >
      {/if}
    </svelte:fragment>
    <OpenTerminalButton
      slot="toolAction"
      {terminalId}
      {toolUseInput}
      toolResult={toolUseState.result?.text}
      phase={toolUseState.phase}
    />
    <ToolUseStatus
      slot="toolStatus"
      isCommandError={result.returnCode ? result.returnCode !== 0 : false}
    />
  </ToolUseHeader>

  <ShellToolUseDetails slot="details" {toolUseInput} {result} {toolUseState} />
  <ShellError
    slot="error"
    isError={result.returnCode ? false : toolUseState.phase === ToolUsePhase.error}
    text={toolUseState.result?.text}
  />
</BaseToolComponent>

<style>
  :global(.c-shell-tool-component__secondary) {
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
</style>
