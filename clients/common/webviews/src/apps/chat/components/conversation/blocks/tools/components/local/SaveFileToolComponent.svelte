<script lang="ts">
  import BaseToolComponent from "../BaseToolComponent.svelte";
  import FilePlusIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/file-plus.svg?component";
  import ToolUseHeader from "../ToolUseHeader.svelte";
  import {
    stringOrDefault,
    ToolUsePhase,
    type ToolUseState,
  } from "$common-webviews/src/apps/chat/types/tool-use-state";
  import SimpleMonaco from "$common-webviews/src/design-system/components/MonacoProvider/SimpleMonaco.svelte";
  import FilespanOpen from "../../FilespanOpen.svelte";

  export let toolUseInput: Record<string, unknown> = {};
  export let toolUseState: ToolUseState;

  $: filepath = stringOrDefault(toolUseInput.path, stringOrDefault(toolUseInput.file_path, ""));
</script>

<BaseToolComponent showToolOutput={toolUseState.phase === ToolUsePhase.completed}>
  <ToolUseHeader slot="header">
    <FilePlusIcon slot="icon" />
    <FilespanOpen slot="secondary" {filepath} />
    <svelte:fragment slot="toolName">
      {#if toolUseState.phase === ToolUsePhase.cancelled}
        File creation cancelled
      {:else if toolUseState.phase === ToolUsePhase.cancelling}
        Cancelling creation of file
      {:else if toolUseState.phase === ToolUsePhase.completed}
        Created file
      {:else if toolUseState.phase === ToolUsePhase.error}
        File creation failed.
      {:else}
        Creating file...
      {/if}
    </svelte:fragment>
  </ToolUseHeader>

  <div slot="details">
    <SimpleMonaco
      text={stringOrDefault(toolUseInput.file_content, "")}
      pathName={stringOrDefault(toolUseInput.path, "")}
    />
  </div>
</BaseToolComponent>
