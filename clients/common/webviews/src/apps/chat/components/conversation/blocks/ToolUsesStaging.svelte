<script context="module" lang="ts">
  function shouldIncludeInLineCount(toolUse: ChatResultToolUse): boolean {
    const editingTools: Array<string> = [LocalToolType.strReplaceEditor, LocalToolType.editFile];
    return editingTools.includes(toolUse.tool_name);
  }
</script>

<script lang="ts">
  import { type RemoteAgentsModel } from "$common-webviews/src/apps/remote-agent-manager/models/remote-agents-model";
  import type {
    ChatResultNode,
    ChatResultToolUse,
  } from "@augment-internal/sidecar-libs/src/chat/chat-types";
  import { type ConversationModel, formatHistory } from "../../../models/conversation-model";
  import { type ToolsWebviewModel } from "../../../models/tools-webview-model";
  import {
    type ExchangeWithStatus,
    isChatItemSuccessfulExchange,
  } from "../../../types/chat-message";
  import ToolUse from "./tools/ToolUse.svelte";
  import CollapsibleAugment from "$common-webviews/src/design-system/components/CollapsibleAugment/CollapsibleAugment.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import CollapseButtonAugment from "$common-webviews/src/design-system/components/CollapsibleAugment/CollapseButtonAugment.svelte";
  import { getChatModel } from "$common-webviews/src/apps/chat/chat-context";
  import { ToolUsePhase } from "$common-webviews/src/apps/chat/types/tool-use-state";
  import { visibilityObserverOnce } from "$common-webviews/src/apps/chat/components/actions/trackOnScreen";
  import BadgeRoot from "$common-webviews/src/design-system/components/BadgeAugment/BadgeRoot.svelte";
  import EditChangeSummary from "../../agent-edits/EditChangeSummary.svelte";
  import { LocalToolType } from "$vscode/src/webview-providers/tool-types";
  import { type ChatFlagsModel } from "../../../models/chat-flags-model";
  import { onlyDisplayableToolNodes } from "../../../utils/tool-utils";

  export let conversationModel: ConversationModel;
  export let toolsWebviewModel: ToolsWebviewModel;
  export let remoteAgentsModel: RemoteAgentsModel | undefined = undefined;
  export let requestId: string;
  export let toolUseNodes: ChatResultNode[];
  export let isLastTurn: boolean;
  export let turnIndex: number = 0;
  export let messageListContainer: HTMLElement | undefined = undefined;
  export let flagsModel: ChatFlagsModel | undefined = undefined;

  $: displayableToolUseNodes = flagsModel?.enableParallelTools
    ? onlyDisplayableToolNodes(requestId, toolUseNodes, conversationModel, remoteAgentsModel)
    : toolUseNodes;

  function onToolRun(requestId: string, toolUse: ChatResultToolUse, toolUseInput: any) {
    toolsWebviewModel.approveTool(
      requestId,
      toolUse.tool_use_id,
      toolUse.tool_name,
      toolUseInput,
      conversationModel.chatHistory
        .filter((m): m is ExchangeWithStatus => isChatItemSuccessfulExchange(m))
        .map(formatHistory),
      conversationModel.id,
    );
  }

  function onToolCancel(requestId: string, toolUseId: string) {
    toolsWebviewModel.cancelToolRun(requestId, toolUseId);
  }

  function onToolSkip(requestId: string, toolUseId: string) {
    toolsWebviewModel.skipToolRun(requestId, toolUseId);
  }

  const app = getChatModel();
  let hasBeenVisible = false;
  let totalAddedLines = 0;
  let totalRemovedLines = 0;
  let hasLoaded = false;

  async function loadTotalChanges() {
    if (hasLoaded || !hasBeenVisible) return;

    let addedTotal = 0;
    let removedTotal = 0;

    // Get all completed tool use nodes that should be included in line count
    const toolUseNodesToCount = toolUseNodes
      .filter((node) => node.tool_use !== undefined && shouldIncludeInLineCount(node.tool_use))
      .filter((node) => {
        const toolUse = node.tool_use!;
        const nodeRequestId = node.requestId ?? requestId;
        const toolUseState = $remoteAgentsModel?.isActive
          ? $remoteAgentsModel.getToolUseState(toolUse.tool_use_id)
          : conversationModel.getToolUseState(nodeRequestId, toolUse.tool_use_id);
        return toolUseState.phase === ToolUsePhase.completed;
      });

    // Load changes for each tool use node
    for (const node of toolUseNodesToCount) {
      try {
        const nodeRequestId = node.requestId ?? requestId;
        const changes = await app.extensionClient?.getAgentEditChangesByRequestId(nodeRequestId);
        if (changes) {
          addedTotal += changes.totalAddedLines ?? 0;
          removedTotal += changes.totalRemovedLines ?? 0;
        }
      } catch (e) {
        console.warn("Failed to load changes for tool use:", node.tool_use?.tool_use_id, e);
      }
    }

    totalAddedLines = addedTotal;
    totalRemovedLines = removedTotal;
    hasLoaded = true;
  }

  // Watch for all tool uses to be completed and visibility
  $: relevantNodes = toolUseNodes.filter(
    (node) => node.tool_use !== undefined && shouldIncludeInLineCount(node.tool_use),
  );

  $: allCompleted = relevantNodes.every((node) => {
    const toolUse = node.tool_use!;
    const nodeRequestId = node.requestId ?? requestId;
    const toolUseState = $remoteAgentsModel?.isActive
      ? $remoteAgentsModel.getToolUseState(toolUse.tool_use_id)
      : conversationModel.getToolUseState(nodeRequestId, toolUse.tool_use_id);
    const isCompleted = toolUseState.phase === ToolUsePhase.completed;
    return isCompleted;
  });

  $: {
    if (allCompleted && hasBeenVisible && !hasLoaded) {
      void loadTotalChanges();
    }
  }
</script>

{#if displayableToolUseNodes?.length}
  {#if displayableToolUseNodes.length === 1}
    <!-- Single tool use - render directly without CollapsibleAugment wrapper -->
    <div class="l-tooluse-list">
      {#each displayableToolUseNodes as toolUseNode}
        {@const toolUse = toolUseNode.tool_use}
        {#if toolUse}
          {@const toolUseState = $remoteAgentsModel?.isActive
            ? $remoteAgentsModel.getToolUseState(toolUse.tool_use_id)
            : conversationModel.getToolUseState(
                toolUseNode.requestId ?? requestId,
                toolUse.tool_use_id,
              )}
          <ToolUse
            {toolUse}
            {toolUseState}
            {isLastTurn}
            requestId={toolUseNode.requestId ?? requestId}
            {onToolRun}
            {onToolCancel}
            {onToolSkip}
            {turnIndex}
            {messageListContainer}
          />
        {/if}
      {/each}
    </div>
  {:else}
    <!-- Multiple tool uses - render with CollapsibleAugment wrapper -->
    <CollapsibleAugment stickyHeader={false} stickyHeaderTop={0}>
      <div slot="header">
        <div
          class="l-tooluse-list__container"
          use:visibilityObserverOnce={{
            onVisible: () => (hasBeenVisible = true),
            scrollTarget: document.body,
          }}
        >
          <div class="l-tooluse-list__container__left">
            <BadgeRoot color="neutral" size={1} variant="soft">{toolUseNodes.length}</BadgeRoot>
            <TextAugment size={1} weight="medium" color="secondary">Tools used</TextAugment>
          </div>
          <div class="l-tooluse-list__container__right">
            <EditChangeSummary {totalAddedLines} {totalRemovedLines} />
            <CollapseButtonAugment size={0.5} />
          </div>
        </div>
      </div>
      <div class="l-tooluse-list">
        {#each toolUseNodes as toolUseNode}
          {@const toolUse = toolUseNode.tool_use}
          {#if toolUse}
            {@const toolUseState = $remoteAgentsModel?.isActive
              ? $remoteAgentsModel.getToolUseState(toolUse.tool_use_id)
              : conversationModel.getToolUseState(
                  toolUseNode.requestId ?? requestId,
                  toolUse.tool_use_id,
                )}
            <ToolUse
              {toolUse}
              {toolUseState}
              {isLastTurn}
              requestId={toolUseNode.requestId ?? requestId}
              {onToolRun}
              {onToolCancel}
              {onToolSkip}
              {turnIndex}
              {messageListContainer}
              isGroupListItem={true}
            />
          {/if}
        {/each}
      </div>
    </CollapsibleAugment>
  {/if}
{/if}

<style>
  .l-tooluse-list {
    display: flex;
    flex-direction: column;
    width: 100%;
    gap: var(--ds-spacing-2);
  }
  .l-tooluse-list__container {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    height: 34px;
    padding-inline: var(--ds-spacing-2);
  }
  .l-tooluse-list__container__left {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: var(--ds-spacing-1);
  }
  .l-tooluse-list__container__right {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: var(--ds-spacing-1);
  }
</style>
