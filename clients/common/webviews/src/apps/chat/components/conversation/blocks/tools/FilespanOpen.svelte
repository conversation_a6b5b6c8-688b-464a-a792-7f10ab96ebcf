<script lang="ts">
  import OpenFileButton from "./components/OpenFileButton.svelte";
  import Filespan from "$common-webviews/src/common/components/Filespan.svelte";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import { type ChatMetricName } from "$vscode/src/metrics/types";

  export let filepath: string;
  export let start: number | undefined = undefined;
  export let stop: number | undefined = undefined;
  export let metric: ChatMetricName | undefined = undefined;

  let openFile: OpenFileButton;
  function onClick(e: Event) {
    e.stopPropagation();
    e.preventDefault();
    openFile.openFile();
  }
</script>

<TextTooltipAugment content="Open {filepath}" class="c-collapsible-button-false">
  <Filespan {filepath} growname={false} {onClick} size={1}>
    <OpenFileButton
      bind:this={openFile}
      slot="rightIcon"
      path={filepath}
      {start}
      {stop}
      {metric}
      tooltip={false}
    />
  </Filespan>
</TextTooltipAugment>
