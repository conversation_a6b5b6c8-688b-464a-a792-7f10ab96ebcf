<script lang="ts" context="module">
  export enum PanelType {
    agentEdits = "agent_edits",
    tasks = "tasks",
  }

  // Create a context key for the swappable panel
  export const SWAPPABLE_PANEL_CONTEXT_KEY = Symbol("swappable-panel-context");
  export type SwappablePanelContext = {
    activePanel: Writable<PanelType>;
    togglePanel: () => void;
  };
</script>

<script lang="ts">
  import { setContext, getContext } from "svelte";
  import { writable, type Writable } from "svelte/store";
  import AgentEditList from "./agent-edits/AgentEditList.svelte";
  import TaskList from "./tasks/task-list/TaskList.svelte";
  import {
    CurrentConversationTaskStore,
    type ICurrentConversationTaskStore,
  } from "../models/task-store";
  import { RemoteAgentsModel } from "../../remote-agent-manager/models/remote-agents-model";

  const taskStore = getContext<ICurrentConversationTaskStore>(CurrentConversationTaskStore.key);
  const { canShowTaskList } = taskStore;

  const remoteAgentsModel = getContext<RemoteAgentsModel>(RemoteAgentsModel.key);

  // Create a writable store for the active panel
  const activePanelStore = writable<PanelType>(PanelType.agentEdits);

  // Set up the context for child components
  setContext<SwappablePanelContext>(SWAPPABLE_PANEL_CONTEXT_KEY, {
    activePanel: activePanelStore,
    togglePanel: () => {
      $activePanelStore =
        $activePanelStore === PanelType.agentEdits ? PanelType.tasks : PanelType.agentEdits;
    },
  });
</script>

<div class="c-swappable-panel">
  <div class="c-swappable-panel__content">
    {#if $activePanelStore === PanelType.agentEdits || !$canShowTaskList || $remoteAgentsModel.isActive}
      <AgentEditList />
    {:else if $activePanelStore === PanelType.tasks}
      <TaskList />
    {/if}
  </div>
</div>

<style>
  .c-swappable-panel {
    display: flex;
    flex-direction: column;
    width: 100%;
  }

  .c-swappable-panel__content {
    width: 100%;
  }
</style>
