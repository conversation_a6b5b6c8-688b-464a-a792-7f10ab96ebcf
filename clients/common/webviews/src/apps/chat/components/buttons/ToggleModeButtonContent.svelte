<script lang="ts">
  import { type ButtonColor } from "$common-webviews/src/design-system/_primitives/BaseButton.svelte";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import ChevronDown from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/chevron-down.svg?component";
  import ChevronUp from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/chevron-up.svg?component";
  import { type AgentExecutionMode } from "../../models/chat-model";
  import { getModeDisplayInfo } from "./mode-display-helper";

  export let isDropdownOpen: boolean;
  export let isCurrConversationAgentic: boolean;
  export let agentExecutionMode: AgentExecutionMode | undefined = undefined;
  export let isBackgroundAgent: boolean | undefined;
  export let variant: "soft" | "ghost" = "ghost";
  export let color: ButtonColor = "neutral";
  export let disabled: boolean = false;
  $: modeInfo = getModeDisplayInfo({
    isConversationAgentic: isCurrConversationAgentic,
    agentExecutionMode,
    isBackgroundAgent: !!isBackgroundAgent,
  });
</script>

<ButtonAugment size={0.5} {variant} {color} {disabled} {...$$restProps}>
  <div class="c-toggle-mode-button__icon">
    <svelte:component this={modeInfo.icon} slot="iconLeft" />
  </div>
  <div class="c-toggle-mode-button__text">
    {modeInfo.primaryText}
  </div>
  <svelte:fragment slot="iconRight">
    <div class="c-toggle-mode-button__chevron">
      {#if isDropdownOpen}
        <ChevronUp />
      {:else}
        <ChevronDown />
      {/if}
    </div>
  </svelte:fragment>
</ButtonAugment>

<style>
  .c-toggle-mode-button__text {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    line-height: normal;
  }
  .c-toggle-mode-button__icon :global(svg) {
    padding: var(--ds-spacing-0_5);
    margin-right: var(--ds-spacing-1);
  }
  .c-toggle-mode-button__chevron :global(svg) {
    padding: var(--ds-spacing-0_5);
  }
</style>
