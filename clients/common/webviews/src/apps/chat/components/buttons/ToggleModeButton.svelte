<script lang="ts">
  import { RemoteAgentsModel } from "$common-webviews/src/apps/remote-agent-manager/models/remote-agents-model";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import ToggleAugment from "$common-webviews/src/design-system/components/ToggleAugment.svelte";
  import { getContext, onMount } from "svelte";
  import type { AgentConversationModel } from "../../models/agent-conversation-model";
  import { ChatModeModel } from "../../models/chat-mode-model";
  import { AgentExecutionMode, type ChatModel } from "../../models/chat-model";
  import { TooltipTriggerOn } from "$common-webviews/src/design-system/_primitives/TooltipAugment/types";
  import RegisterCommand from "$common-webviews/src/common/components/keybindings/RegisterCommand.svelte";
  import ShortcutHint from "$common-webviews/src/common/components/keybindings/ShortcutHint.svelte";

  export let chatModel = getContext<ChatModel>("chatModel");
  export let agentConversationModel = getContext<AgentConversationModel>("agentConversationModel");
  export let disabled: boolean = false;

  const remoteAgentsModel = getContext<RemoteAgentsModel>(RemoteAgentsModel.key);

  const chatModeModel = getContext<ChatModeModel>(ChatModeModel.key);

  $: enableAgentAutoMode = $chatModel.flags.enableAgentAutoMode;
  $: isCurrConversationAgentic = agentConversationModel.isCurrConversationAgentic;
  $: agentExecutionMode = $chatModel.agentExecutionMode;

  // Determine if we should show the auto/manual toggle
  $: showAgentToggle =
    $isCurrConversationAgentic && !$remoteAgentsModel.isActive && enableAgentAutoMode;

  // Reactive variable for the toggle state (true = auto, false = manual)
  $: isAutoMode = $agentExecutionMode === AgentExecutionMode.auto;

  // Dynamic tooltip content based on current mode
  $: tooltipContent = isAutoMode
    ? "Auto ON - Run most commands automatically"
    : "Auto OFF - Require approval for most commands";

  // Handle toggle change
  async function handleToggleChange() {
    await chatModeModel.toggleChatAgentAutoMode("button");
  }

  // Report init event when component is mounted
  onMount(() => {
    chatModeModel.reportModeSelectorInit();
  });
</script>

<!-- Auto/Manual toggle for agent modes -->
{#if showAgentToggle}
  <TextTooltipAugment triggerOn={[TooltipTriggerOn.Hover]} delayDurationMs={300}>
    <div slot="content" class="c-toggle-mode-button-tooltip">
      <div class="c-toggle-mode-button-tooltip__content">
        {tooltipContent}
        <ShortcutHint commandName="toggleAgentMode" keysOnly />
      </div>
    </div>
    <RegisterCommand name="toggleAgentMode" handler={handleToggleChange} />
    <ToggleAugment
      checked={isAutoMode}
      {disabled}
      size={2}
      onText="Auto"
      offText="Auto"
      ariaLabel="Toggle between manual and auto agent mode"
      on:change={handleToggleChange}
    />
  </TextTooltipAugment>
{/if}

<style>
  .c-toggle-mode-button-tooltip {
    text-align: center;
  }

  .c-toggle-mode-button-tooltip__content {
    --keyboard-shortcut-hint-border: currentColor;
    --keyboard-shortcut-hint-color: currentColor;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--ds-spacing-1);
  }
</style>
