<script lang="ts">
  import DropdownMenuAugment from "$common-webviews/src/design-system/components/DropdownMenuAugment";
  import ChevronDown from "$common-webviews/src/design-system/icons/chevron-down.svelte";
  import { createEventDispatcher } from "svelte";
  import Base from "./Base.svelte";

  export let type: "dropdown" | "button" = "button";
  export let title: string = "Select an option";
  export let subtitle: string = "";
  export let onClick = () => {};
  export let items: any[] = [];
  export let selectedItem: any = undefined;
  export let formatItemLabel: (item: any) => string = (item) => item?.toString() || "";
  export let noItemsLabel: string = "No items found";
  export let onDropdownOpenChange: (open: boolean) => void = () => {};
  export let requestClose = () => {};
  export let disabled: boolean = false;

  export function selectItem(item: any) {
    selectedItem = item;
    dispatch("select", item);
  }

  const dispatch = createEventDispatcher();
</script>

<div class="c-card-button">
  {#if type === "dropdown"}
    <DropdownMenuAugment.Root onOpenChange={onDropdownOpenChange} bind:requestClose>
      <DropdownMenuAugment.Trigger>
        <div
          class="c-card-button__display"
          class:disabled
          role="button"
          tabindex={disabled ? -1 : 0}
        >
          <Base {subtitle}>
            <slot name="iconLeft" slot="iconLeft" />
            <slot name="title" slot="title">
              {title}
            </slot>
            <slot name="iconRight" slot="iconRight">
              <ChevronDown />
            </slot>
          </Base>
        </div>
      </DropdownMenuAugment.Trigger>
      <DropdownMenuAugment.Content align="start" side="bottom">
        <div class="c-card__dropdown-contents">
          <div class="c-card-button__dropdown-top">
            <slot name="dropdown-top"></slot>
          </div>
          <div class="c-card-button__dropdown-content">
            <slot name="dropdown-content">
              {#if items.length > 0}
                {#each items as item (formatItemLabel(item))}
                  <DropdownMenuAugment.Item
                    onSelect={() => selectItem(item)}
                    highlight={selectedItem === item}
                  >
                    {formatItemLabel(item)}
                  </DropdownMenuAugment.Item>
                {/each}
              {:else}
                <DropdownMenuAugment.Label>{noItemsLabel}</DropdownMenuAugment.Label>
              {/if}
            </slot>
          </div>
        </div>
      </DropdownMenuAugment.Content>
    </DropdownMenuAugment.Root>
  {:else}
    <button
      class="c-card-button__display"
      on:click={() => {
        onClick();
        dispatch("click");
      }}
      on:keydown={(e) => {
        if (e.key === "Enter" || e.key === " ") {
          onClick();
          dispatch("click");
        }
      }}
      type="button"
      {disabled}
    >
      <Base {title} {subtitle}>
        <slot name="iconLeft" slot="iconLeft" />
        <slot name="iconRight" slot="iconRight" />
      </Base>
    </button>
  {/if}
</div>

<style>
  .c-card-button {
    width: 100%;
  }

  .c-card-button__display {
    --card-button-accent-color: var(--augment-border-color);
    cursor: pointer;
    display: flex;
    align-items: stretch;
    width: 100%;
    border: 1px solid var(--card-button-accent-color);
    transition: border-color 0.2s ease;
    background-color: var(--vscode-input-background);
    color: var(--vscode-panelInput-border, var(--intellij-editor-searchField-borderColor));
    border-radius: 6px;
    min-height: 2.5rem;
    font-family: inherit;
    font-size: inherit;
    color: inherit;
    padding: 0;
    margin: 0;
    text-align: left;
    max-width: 100%;
    box-shadow: 0 1px 0 0 var(--vscode-editorStickyScroll-shadow);
  }

  .c-card-button__display:not(.disabled):hover {
    --card-button-accent-color: var(--vscode-checkbox-selectBorder);
  }

  .c-card-button :global(.l-tooltip-trigger) {
    width: 100%;
  }

  .c-card-button :global(> .l-tooltip-trigger > div) {
    width: 100%;
  }

  .c-card-button {
    max-width: 100%;
    color: var(--vscode-input-foreground);
  }

  .c-card-button__display:not(.disabled):focus {
    outline: none;
    border-color: var(--ds-color-accent-7);
    box-shadow: 0 0 0 2px var(--ds-color-accent-3);
  }

  .c-card-button__display.disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  .c-card-button :global(.l-dropdown-menu-augment__contents) {
    padding: 0;
  }

  .c-card__dropdown-contents {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-1);
    max-height: 42vh;
  }

  .c-card-button__dropdown-top {
    flex: none;
    padding: var(--ds-spacing-1);
  }

  .c-card-button__dropdown-content {
    overflow: auto;
    flex: 1;
    min-height: 0;
    padding: 0 var(--ds-spacing-1);
  }
</style>
