<script lang="ts">
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";

  export let index: number = 0;
  export let selected: boolean = false;
  export let color: "neutral" | "error" = "neutral";
</script>

<div class={`c-slim-btn c-slim-btn--${color}`} class:c-slim-btn--selected={selected}>
  <ButtonAugment
    alignment="left"
    size={1}
    variant="ghost-block"
    {color}
    tabindex={index}
    on:click
    on:keydown
  >
    <div class="c-slim-btn__content"><slot /></div>
  </ButtonAugment>
</div>

<style>
  .c-slim-btn {
    width: 100%;
  }

  .c-slim-btn :global(.c-base-btn) {
    width: 100%;

    --base-btn-padding-vertical: 2px;
    --base-btn-border-radius: 0;
  }

  .c-slim-btn--neutral :global(.c-base-btn) {
    --base-btn-color: var(--ds-color-a12);
  }

  .c-slim-btn__content {
    display: inline-flex;
    flex-direction: row;
    align-items: center;
    gap: var(--ds-spacing-1);
    font-size: 12px;
  }

  .c-slim-btn__content :global(svg) {
    width: 12px;
    height: 12px;
  }

  .c-slim-btn--selected :global(.c-base-btn) {
    --base-btn-bg-color: var(--ds-color-a6);
  }
</style>
