import RegularChatIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/message.svg?component";
import RegularCloudIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/cloud.svg?component";
import AgentIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/wand-magic-sparkles.svg?component";
import { AgentExecutionMode } from "../../models/chat-model";
import { type ChatModeType } from "@augment-internal/sidecar-libs/src/chat";

export interface ModeDisplayParams {
  isConversationAgentic: boolean;
  agentExecutionMode: AgentExecutionMode | undefined;
  isBackgroundAgent: boolean;
}

export interface ModeDisplayInfo {
  icon: any; // Svelte component
  type: ChatModeType;
  primaryText: string;
  secondaryText?: string;
  bannerText: string;
}

export const MODE_DISPLAY_INFO = {
  chat: {
    icon: RegularChatIcon,
    type: "chat",
    primaryText: "Chat",
    bannerText: "Running locally",
  },
  localAgent: {
    icon: AgentIcon,
    type: "localAgent",
    primaryText: "Agent",
    bannerText: "Running locally",
  },
  remoteAgent: {
    icon: RegularCloudIcon,
    type: "remoteAgent",
    primaryText: "Remote Agent",
    bannerText: "Running in the cloud",
  },
} as const;

/**
 * Helper function to determine the appropriate icon and text for a given mode configuration.
 * This centralizes the logic that was previously duplicated across components.
 */
export function getModeDisplayInfo(params: ModeDisplayParams): ModeDisplayInfo {
  const { isConversationAgentic, agentExecutionMode, isBackgroundAgent } = params;

  if (isBackgroundAgent) {
    return MODE_DISPLAY_INFO.remoteAgent;
  }

  if (!isConversationAgentic) {
    return MODE_DISPLAY_INFO.chat;
  }

  if (
    agentExecutionMode === AgentExecutionMode.manual ||
    agentExecutionMode === AgentExecutionMode.auto
  ) {
    return MODE_DISPLAY_INFO.localAgent;
  }

  return MODE_DISPLAY_INFO.localAgent;
}
