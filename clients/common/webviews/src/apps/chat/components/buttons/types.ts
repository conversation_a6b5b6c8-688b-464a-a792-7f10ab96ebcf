import { type ButtonVariant } from "$common-webviews/src/design-system/_primitives/BaseButton.svelte";

// SuccessfulButton types

export type ButtonState = "success" | "failure" | "neutral";
export type ButtonStateText = {
  success?: string;
  failure?: string;
  neutral?: string;
};
export type ButtonStateVariant = {
  success?: ButtonVariant;
  failure?: ButtonVariant;
  neutral?: ButtonVariant;
};
