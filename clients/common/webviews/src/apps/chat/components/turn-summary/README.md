# TurnSummary Component

A generic, animated summary component that displays an array of summary entries with icons, values, and optional callbacks.

## Usage

```svelte
<script>
  import TurnSummary from "./TurnSummary.svelte";
  import type { SummaryEntry } from "./types";

  const entries: SummaryEntry[] = [
    {
      label: "Files Changed",
      value: 5,
      icon: "difference",
      callback: () => console.log("Files clicked!")
    },
    { label: "Lines Added", value: 42, icon: "add_box" },
    { label: "Lines Removed", value: 18, icon: "indeterminate_check_box" },
  ];
</script>

<TurnSummary {entries} turnIndex={0}>
  <svelte:fragment slot="header-actions">
    <!-- Optional header actions -->
  </svelte:fragment>

  <svelte:fragment slot="footer">
    <!-- Optional footer content -->
  </svelte:fragment>
</TurnSummary>
```

## Props

- `entries: SummaryEntry[]` - Array of summary entries to display
- `turnIndex: number = 0` - Turn index for the title display

## SummaryEntry Interface

```typescript
interface SummaryEntry {
  label: string;        // Display label
  value: number;        // Numeric value to animate
  icon: string;         // Material icon name
  callback?: () => void; // Optional click handler
}
```

## Features

- **Automatic filtering**: Entries with `value: 0` are automatically filtered out
- **Responsive layout**: Two-column layout on wider screens
- **Animated appearance**: Staggered fade-in animations when component comes into view
- **Interactive entries**: Entries with callbacks become clickable buttons
- **Accessible**: Proper button semantics for interactive entries
- **Customizable**: Header actions and footer slots for additional content

## Animation

The component uses intersection observer to trigger animations when it comes into view:
- Each entry fades in with a staggered delay
- Number values animate from 0 to their target value
- Footer content slides in after all entries have animated
