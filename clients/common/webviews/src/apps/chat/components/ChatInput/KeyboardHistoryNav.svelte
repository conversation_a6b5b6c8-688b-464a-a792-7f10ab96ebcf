<script lang="ts">
  import { getContext } from "svelte";
  import type { ChatModel } from "../../models/chat-model";
  import type { EditorState } from "prosemirror-state";
  import {
    getPrefix,
    getSuffix,
  } from "$common-webviews/src/design-system/components/RichTextEditorAugment/context/json-content-utils";
  import { isChatItemExchangeWithStatus } from "../../types/chat-message";
  import type { ContentData } from "$common-webviews/src/design-system/components/RichTextEditorAugment/types";
  import { RichTextEditorContext } from "$common-webviews/src/design-system/components/RichTextEditorAugment/context";
  import Keybindings from "$common-webviews/src/design-system/components/RichTextEditorAugment/plugins/Keybindings/Keybindings.svelte";

  const chatModel = getContext<ChatModel>("chatModel");
  const { currentConversationModel } = chatModel;
  const editorContext = getContext<RichTextEditorContext>(RichTextEditorContext.CONTEXT_KEY);

  /**
   * General structure of the history navigation:
   * - At any moment in time, we are logically modifying an "index" in history
   * - When we switch to a new index (i => i+1)
   *    - Start editing index i
   *    - Store the current content at the current index (i)
   *    - Increment the index
   *    - Load the draft at the new index (i+1) into the editor
   *    - We are now editing index i+1
   */

  // Track the history index
  let historyIndex = 0;
  let bufferContent: ContentData = { rawText: "", richTextJsonRepr: [] };

  // Store all the user drafts
  $: userDrafts = $currentConversationModel.chatHistory
    .filter(isChatItemExchangeWithStatus)
    .filter((m) => m.request_message.trim() !== "")
    .map(
      (m): ContentData => ({
        rawText: m.request_message,
        richTextJsonRepr: m.rich_text_json_repr ?? [],
      }),
    )
    .reverse(); // Most recent first
  $: allDrafts = [bufferContent, ...userDrafts];

  function saveDraftIntoHistory() {
    const currDraft = allDrafts[historyIndex];
    currDraft.rawText = currentConversationModel.draftExchange?.request_message ?? "";
    currDraft.richTextJsonRepr = currentConversationModel.draftExchange?.rich_text_json_repr ?? [];
  }

  function onArrowUp(editor: EditorState): boolean {
    saveDraftIntoHistory(); // Dump draft into the history buffer

    // If we're not at the start of the document, do nothing
    if (getPrefix(editor).prefix !== "") {
      return false;
    }

    // Check if we can go further up in history
    if (historyIndex + 1 >= allDrafts.length) {
      return true; // Stop at the end of history
    }

    saveDraftIntoHistory(); // Dump draft into the history buffer
    historyIndex += 1;
    const currDraft = allDrafts[historyIndex];
    editorContext.commandManager.setContent(currDraft.richTextJsonRepr, {
      cursorPosition: "start",
    });
    return true;
  }

  function onArrowDown(editor: EditorState): boolean {
    saveDraftIntoHistory(); // Dump draft into the history buffer

    // If we're at the end of the document, load in the next message
    if (getSuffix(editor).suffix !== "") {
      return false;
    }

    // Check if we can go further down in history
    if (historyIndex <= 0) {
      return true; // Stop at the beginning of history
    }

    saveDraftIntoHistory(); // Dump draft into the history buffer
    historyIndex -= 1;
    const currDraft = allDrafts[historyIndex];
    editorContext.commandManager.setContent(currDraft.richTextJsonRepr, {
      cursorPosition: "end",
    });
    return true;
  }
</script>

<Keybindings
  shortcuts={{
    /* eslint-disable @typescript-eslint/naming-convention */
    ArrowUp: onArrowUp,
    ArrowDown: onArrowDown,
    /* eslint-enable @typescript-eslint/naming-convention */
  }}
/>
