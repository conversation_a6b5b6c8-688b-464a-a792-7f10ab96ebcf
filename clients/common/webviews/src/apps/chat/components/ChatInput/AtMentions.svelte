<script lang="ts">
  import MentionPlugin from "$common-webviews/src/design-system/components/RichTextEditorAugment/plugins/Mention";
  import type MentionPluginRoot from "$common-webviews/src/design-system/components/RichTextEditorAugment/plugins/Mention/Root.svelte";
  import { getContext, onDestroy } from "svelte";
  import type { ChatModel } from "../../models/chat-model";
  import {
    type ChatMentionableDropdownData,
    ContextMenuController,
  } from "../InputActionBar/context-menu-controller";
  import type { IChatMentionable } from "../../types/mention-option";
  import ContextMenuItem from "../InputActionBar/ContextMenuItem.svelte";
  import ChatMentionHoverContents from "../mentions/ChatMentionHoverContents.svelte";

  // A hook to focus the editor
  export let requestEditorFocus: () => void;
  export let onMentionItemsUpdated:
    | ((data: {
        added: IChatMentionable[];
        removed: IChatMentionable[];
        current: IChatMentionable[];
      }) => void)
    | undefined = undefined;

  // This is the public API to share @ context insertion with other components
  // Note: don't bind to this function directly, as the reference becomes undefined
  // when re-rendering the component, leading to a no-op
  export const insertMentionNode = (mentionable: IChatMentionable) => {
    return _insertMentionNode(mentionable);
  };

  const chatModel = getContext<ChatModel>("chatModel");
  if (!chatModel) {
    throw new Error("ChatModel not found in context");
  }

  // Initialize the context menu controller + extract reactive state
  const contextMenuController = new ContextMenuController(chatModel, _insertMentionNode);
  const displayItems = contextMenuController.displayItems;
  onDestroy(() => {
    contextMenuController.dispose();
  });

  // Handle when the controller wants to insert a mention node
  let mentionRoot: MentionPluginRoot<IChatMentionable> | undefined;
  function _insertMentionNode(mentionable: IChatMentionable): boolean {
    if (!mentionRoot) return false;
    mentionRoot.insertMention(mentionable);
    contextMenuController.closeDropdown();
    return true;
  }

  // If the query is undefined, close the dropdown. Otherwise, open it and update the query.
  function onQueryUpdate(query: string | undefined) {
    if (query === undefined) {
      contextMenuController.closeDropdown();
    } else {
      contextMenuController.openDropdown();
      contextMenuController.userQuery.set(query);
    }
  }

  function onSelectMentionable(mentionable: ChatMentionableDropdownData): boolean {
    const returnValue = contextMenuController.selectMentionable(mentionable);
    requestEditorFocus();
    return returnValue;
  }
</script>

<!-- @ Mention Plugin -->
<MentionPlugin.Root triggerCharacter="@" bind:this={mentionRoot} {onMentionItemsUpdated}>
  <!-- Suggestion Menu Dropdown -->
  <MentionPlugin.Menu.Root mentionables={$displayItems} {onQueryUpdate} {onSelectMentionable}>
    <svelte:fragment let:activeItem>
      {#each $displayItems as item}
        <ContextMenuItem
          {item}
          highlight={item === activeItem}
          onSelect={() => onSelectMentionable(item)}
        />
      {/each}
    </svelte:fragment>
  </MentionPlugin.Menu.Root>
  <!-- Hover Tooltip for @mention chips -->
  <MentionPlugin.ChipTooltip>
    <ChatMentionHoverContents slot="mentionable" let:mentionable option={mentionable} />
  </MentionPlugin.ChipTooltip>
</MentionPlugin.Root>
