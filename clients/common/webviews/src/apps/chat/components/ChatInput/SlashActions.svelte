<script lang="ts">
  import { getContext } from "svelte";
  import MentionPlugin from "$common-webviews/src/design-system/components/RichTextEditorAugment/plugins/Mention";
  import type { ChatModel } from "../../models/chat-model";
  import {
    getVisibleSlashCommands,
    type ISlashCommandOptionData,
  } from "../../types/slash-command-list";
  import { RichTextEditorContext } from "$common-webviews/src/design-system/components/RichTextEditorAugment/context";
  import { type SlashCommandModel } from "../../models/slash-command-model";

  // Extract reactive state/context
  const chatModel: ChatModel = getContext("chatModel");
  const slashCommandModel = getContext<SlashCommandModel>("slashCommandModel");
  const { activeCommand } = slashCommandModel;
  const editorContext = getContext<RichTextEditorContext>(RichTextEditorContext.CONTEXT_KEY);
  const flagsModel = $chatModel.flags;

  // When query changes, update the commands the user can have
  let query: string | undefined;
  $: filteredCommands = getActionsFromQuery(query, $flagsModel.enableGenerateCommitMessage);
  function getActionsFromQuery(
    query: string | undefined,
    enableGenerateCommitMessage: boolean,
  ): ISlashCommandOptionData[] {
    let newCommands = getVisibleSlashCommands();
    if (!enableGenerateCommitMessage) {
      newCommands = newCommands.filter((command) => command.id !== "generate-commit-message");
    }

    if (query === undefined) {
      return newCommands;
    }

    return newCommands.filter((command) =>
      command.label.toLowerCase().includes(query.toLowerCase()),
    );
  }

  function onQueryUpdate(newQuery: string | undefined) {
    query = newQuery;
  }

  $: {
    if ($activeCommand) {
      // Hide base editor when active command
      editorContext.commandManager.hide();
      editorContext.commandManager.clearContent();
    } else {
      // Show base editor when no active command
      editorContext.commandManager.show();
      editorContext.commandManager.clearContent();
      editorContext.commandManager.requestFocus();
    }
  }
</script>

<MentionPlugin.Root
  pluginId="augment-slash-actions"
  triggerCharacter="/"
  allowedPrefixes={[]}
  renderText={() => ""}
>
  <MentionPlugin.Menu.Root
    mentionables={filteredCommands}
    {onQueryUpdate}
    onSelectMentionable={(command) => {
      slashCommandModel.setActiveCommand(command.id);
      return true;
    }}
  >
    {#each filteredCommands as command}
      <MentionPlugin.Menu.Item mentionable={command}>
        /{command.label}
      </MentionPlugin.Menu.Item>
    {/each}
  </MentionPlugin.Menu.Root>
  <MentionPlugin.ChipTooltip>
    <span slot="mentionable" let:mentionable> {mentionable.label} </span>
  </MentionPlugin.ChipTooltip>
</MentionPlugin.Root>
