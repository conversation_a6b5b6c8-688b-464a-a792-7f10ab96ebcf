<script lang="ts">
  import RichTextEditorAugment from "$common-webviews/src/design-system/components/RichTextEditorAugment";
  import Placeholder from "$common-webviews/src/design-system/components/RichTextEditorAugment/plugins/Placeholder";
  import { getContext, tick } from "svelte";
  import { readable } from "svelte/store";

  import type { ChatModel } from "../../models/chat-model";
  import type { IChatMentionable } from "../../types/mention-option";
  import ContextBar from "../context/ContextBar.svelte";
  import InputActionBar from "../InputActionBar";
  import Keybindings from "$common-webviews/src/design-system/components/RichTextEditorAugment/plugins/Keybindings/Keybindings.svelte";
  import AtMentions from "./AtMentions.svelte";
  import type { JSONContent } from "@tiptap/core";
  import type { ContentData } from "$common-webviews/src/design-system/components/RichTextEditorAugment/types";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";

  import type RichTextEditorRoot from "$common-webviews/src/design-system/components/RichTextEditorAugment/Root.svelte";
  import ExpandIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/chevron-down.svg?component";
  import CollapseIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/chevron-up.svg?component";

  import Image from "$common-webviews/src/design-system/components/RichTextEditorAugment/plugins/File";
  import DropZone from "$common-webviews/src/design-system/components/RichTextEditorAugment/plugins/DropZone/DropZone.svelte";
  import { FileController } from "$common-webviews/src/design-system/components/RichTextEditorAugment/plugins/File/controller";
  import { RemoteAgentsModel } from "$common-webviews/src/apps/remote-agent-manager/models/remote-agents-model";
  import SendButton from "../InputActionBar/SendButton.svelte";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import MessageIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/message-lines.svg?component";
  import SeparatorAugment from "$common-webviews/src/design-system/components/SeparatorAugment.svelte";
  import { getMessageRenderOptions } from "$common-webviews/src/apps/remote-agent-manager/models/message-render-options";
  import { DEFAULT_MAX_CHAT_INPUT_CHARS } from "../../models/chat-flags-model";
  import ModelPicker from "../ModelPicker/ModelPicker.svelte";
  import type { AgentConversationModel } from "../../models/agent-conversation-model";
  import { getPromptEnhancerOptions } from "../../utils/chat-input-context";
  import { GitReferenceModel } from "$common-webviews/src/apps/remote-agent-manager/models/git-reference-model";
  import type { Exchange } from "@augment-internal/sidecar-libs/src/chat/chat-types";
  import { AgentSessionEventName } from "@augment-internal/sidecar-libs/src/metrics/types";
  import tippy from "tippy.js";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import { getInputWithEnchancePrompt } from "../../utils/prompts";
  import ToggleModeButton from "../buttons/ToggleModeButton.svelte";
  import AskModeBadge from "$common-webviews/src/design-system/components/RichTextEditorAugment/plugins/AskModeBadge/AskModeBadge.svelte";
  import { AskModeBadgeController } from "$common-webviews/src/design-system/components/RichTextEditorAugment/plugins/AskModeBadge/controller";
  import { slideScale } from "$common-webviews/src/apps/chat/components/threads/animation-util";
  import { ChatModeModel } from "../../models/chat-mode-model";
  import type { ModelRegistry } from "../../models/model-registry";
  import { DEFAULT_MODEL } from "../../models/model-registry";

  // Get chat model from context
  const chatModel: ChatModel = getContext("chatModel");
  const chatModeModel = getContext<ChatModeModel>(ChatModeModel.key);
  const flagsModel = chatModel.flags;
  const agentConversationModel = getContext<AgentConversationModel>("agentConversationModel");
  const remoteAgentModel: RemoteAgentsModel = getContext(RemoteAgentsModel.key);
  const gitRefModel = getContext<GitReferenceModel>(GitReferenceModel.key);
  const modelRegistry = getContext<ModelRegistry>("modelRegistry");

  // Props
  export let collapsedMaxHeight = 120;
  export let requestId: string | undefined = undefined;

  export let placeholder = "Edit your message...";
  export let content: string | JSONContent | JSONContent[];
  export let collapsed: boolean = false;
  export let onSubmitEdit: (newContent: ContentData, mentions: IChatMentionable[]) => void;
  export let onCancelEdit: () => void = () => {};
  export let setIsCollapsed: (isCollapsed: boolean) => void;
  export let userExpanded: boolean;
  export let showExpandButton: boolean = false;
  export let isEditing: boolean = false;

  // Controls API
  export const requestStartEdit = () => void startEdit();

  // ==== Reactive state ====
  // Reported content from the editor. If editable is false,
  // draftContents should always be undefined
  let draftContents: ContentData | undefined = undefined;
  let editedContents: ContentData | undefined = undefined; // state that is edited, but not submitted. To preseve draft state when closing editing mode
  let mentionNodes: IChatMentionable[] = [];
  let isAskMode = false;

  $: currentConversationModel = $chatModel.currentConversationModel;
  $: isRemoteAgent = !!$remoteAgentModel?.isActive;
  $: messageRenderOptions = getMessageRenderOptions($remoteAgentModel);

  $: conversationType = chatModeModel.chatModeType;
  $: ({ isCurrConversationAgentic } = agentConversationModel);

  $: displayContent = draftContents?.richTextJsonRepr ?? content;

  // Detect ask mode from initial content when editing starts
  $: {
    if ($conversationType === "localAgent" && !isEditing && content) {
      // Only detect from initial content, not from draft changes
      const hasAskModeBadge = AskModeBadgeController.detectAskModeInContent(content);
      if (hasAskModeBadge !== isAskMode) {
        isAskMode = hasAskModeBadge;
      }
    }
  }

  function toggleAskMode() {
    isAskMode = !isAskMode;
  }

  function handleAskModeToggleComplete() {
    if (!isEditing) return;
    setTimeout(() => {
      richTextEditorRoot?.requestFocus();
    }, 100);
  }

  $: messageLength = draftContents?.rawText.trim()?.length ?? 0;

  $: canUserEdit = $chatModel.flags.enableEditableHistory && !isRemoteAgent;

  // Ensure tests that do not set a modelRegistry context still work by falling back to a default store
  $: selectedModel = modelRegistry?.selectedModel ?? readable(DEFAULT_MODEL);
  $: isModelDisabled = $selectedModel.disabled;
  $: modelDisabledReason = $selectedModel.disabled_reason;

  $: canSubmitEdit =
    isEditing &&
    canUserEdit &&
    !isModelDisabled &&
    draftContents !== undefined &&
    draftContents.rawText.trim() !== "" &&
    draftContents.rawText !== content &&
    draftContents.richTextJsonRepr !== content &&
    !$currentConversationModel.awaitingReply &&
    !FileController.hasLoadingImages(draftContents.richTextJsonRepr);
  // Any time controlled content changes, cancel the edit

  const currentRepoUrl = gitRefModel?.currentRepositoryUrl;
  $: promptEnhancerOptions = getPromptEnhancerOptions(
    $remoteAgentModel,
    $currentRepoUrl,
    !canSubmitEdit,
  );

  // Function to rewrite the prompt using the current conversation model
  async function rewritePrompt() {
    // Handle enhance prompt button click

    try {
      // Get the current prompt text
      /* eslint-disable @typescript-eslint/naming-convention */
      const currentPrompt = draftContents?.rawText || "";
      /* eslint-enable @typescript-eslint/naming-convention */

      // Check if there's a prompt to enhance

      // If there's no text, don't do anything
      if (!currentPrompt || currentPrompt.trim() === "") {
        // Exit if there's no prompt to enhance
        return;
      }
      // Generate an enhanced version of this prompt. Reply only with the enhanced prompt, no explanations or other commentary.";

      // Store the original content in case we need to restore it
      const originalContent: ContentData | undefined = draftContents;

      try {
        let chatHistory: Exchange[] | undefined = undefined;

        // Create a temporary exchange to send to the model
        const { responseText } = await $currentConversationModel.sendSilentExchange({
          /* eslint-disable @typescript-eslint/naming-convention */
          request_message: getInputWithEnchancePrompt(currentPrompt),
          /* eslint-enable @typescript-eslint/naming-convention */
          disableRetrieval: false,
          chatHistory,
        });

        // Report the metric
        $currentConversationModel.extensionClient.reportAgentSessionEvent({
          eventName: AgentSessionEventName.enhancedPrompt,
          conversationId: $currentConversationModel.id,
        });

        // Process the enhanced prompt from the LLM

        // Extract the enhanced prompt from between the tags
        let enhancedPrompt = "";
        const tagPattern = /<augment-enhanced-prompt>([\s\S]*?)<\/augment-enhanced-prompt>/;

        const match = responseText.match(tagPattern);

        if (match && match[1]) {
          // If we found the tags, use the content between them
          enhancedPrompt = match[1].trim();
        } else {
          // Fallback: use the original prompt if tags aren't found
          enhancedPrompt = currentPrompt;
          console.warn("Enhanced prompt tags not found in response, using original prompt");

          // Show a popup notification that enhancement failed
          const errorTooltip = document.createElement("div");
          errorTooltip.className = "enhance-prompt-error-tooltip";
          errorTooltip.textContent = "Failed to enhance prompt, sorry about that";
          document.body.appendChild(errorTooltip);

          // Create a tooltip instance
          const tooltip = tippy(errorTooltip, {
            content: errorTooltip,
            trigger: "manual",
            placement: "top",
            theme: "error",
            duration: [300, 2000], // Show quickly, hide after 2 seconds
            onHidden: () => {
              errorTooltip.remove(); // Clean up the DOM
            },
          });

          // Position near the rewrite button and show
          const rewriteButton = editorContainer.querySelector(".c-rewrite-prompt-button");
          if (rewriteButton) {
            tooltip.setProps({
              getReferenceClientRect: () => rewriteButton.getBoundingClientRect(),
            });
            tooltip.show();

            // Auto-hide after 3 seconds
            setTimeout(() => {
              tooltip.hide();
            }, 3000);
          } else {
            errorTooltip.remove(); // Clean up if button not found
          }
        }

        // Create enhanced content for the editor

        if (enhancedPrompt && enhancedPrompt.trim() !== "") {
          // Create a new enhanced content that preserves all non-paragraph nodes
          let enhancedContent: JSONContent;

          if (
            originalContent?.richTextJsonRepr &&
            typeof originalContent?.richTextJsonRepr === "object"
          ) {
            // Create a copy of the original content to avoid mutating it
            enhancedContent = { ...originalContent.richTextJsonRepr };

            if (enhancedContent.content && Array.isArray(enhancedContent.content)) {
              // Create a new content array
              const newContent: JSONContent[] = [];

              // Flag to track if we've already processed the first paragraph
              let firstParagraphReplaced = false;

              // Process each top-level node
              for (const node of enhancedContent.content) {
                if (node.type === "paragraph" && !firstParagraphReplaced) {
                  // Replace the first paragraph with the enhanced prompt
                  // Split the enhanced prompt into lines
                  const lines = enhancedPrompt.split("\n");

                  // Create a new paragraph with the enhanced prompt text
                  const newParagraph: JSONContent = {
                    type: "paragraph",
                    content: [],
                  };

                  // Add each line with hardBreak nodes between them
                  lines.forEach((line, index) => {
                    // Add the text node for this line
                    if (line.length > 0) {
                      newParagraph.content?.push({
                        type: "text",
                        text: line,
                      });
                    }

                    // Add a hardBreak after each line except the last one
                    if (index < lines.length - 1) {
                      newParagraph.content?.push({
                        type: "hardBreak",
                      });
                    }
                  });

                  // Add the new paragraph to the content
                  newContent.push(newParagraph);

                  // Mark that we've replaced the first paragraph
                  firstParagraphReplaced = true;
                } else if (node.type !== "paragraph") {
                  // Preserve all non-paragraph nodes
                  newContent.push(node);
                }
                // Skip all other paragraph nodes (they will be deleted)
              }

              // Update the content with our new array
              enhancedContent.content = newContent;
            }
          } else {
            // If there's no original content or it's not an object, create simple content
            enhancedContent = {
              type: "doc",
              content: [
                {
                  type: "paragraph",
                  content: [{ type: "text", text: enhancedPrompt }],
                },
              ],
            };
          }

          draftContents = {
            rawText: enhancedPrompt,
            richTextJsonRepr: enhancedContent,
          };
          // Position cursor at beginning after content update

          // We already set the content above, no need to do it again

          // Focus the editor and position cursor at beginning
          setTimeout(() => {
            // First focus the editor
            forceEditorFocus();

            // Use the Selection API to position cursor at beginning
            try {
              // Find the editor element
              const editorElement = editorContainer.querySelector(".ProseMirror");
              if (editorElement) {
                // Find the first paragraph and text node
                const firstParagraph = editorElement.querySelector("p");
                if (firstParagraph) {
                  // Create a selection at the beginning
                  const range = document.createRange();
                  const selection = window.getSelection();

                  // Set selection at the beginning of the paragraph
                  range.setStart(firstParagraph, 0);
                  range.collapse(true);

                  // Apply the selection
                  if (selection) {
                    selection.removeAllRanges();
                    selection.addRange(range);
                  }
                }
              }
            } catch (e) {
              // Error occurred while positioning cursor
            }
          }, 200); // TODO(guy) make this more robust, don't rely on magic timeout value
        } else {
          // If we didn't get a valid response, restore the original content
          draftContents = originalContent;

          // Original content restored due to invalid response
        }
      } catch (error) {
        // Error occurred while calling LLM
        // Restore the original content on error
        draftContents = originalContent;
      }

      // Focus the editor
      forceEditorFocus();
    } catch (error) {
      // Error occurred in rewritePrompt function
    }
  }

  // ==== Hooks to modify state ====
  async function startEdit(): Promise<void> {
    if (canUserEdit) {
      userExpanded = true;
      if (collapsed) {
        setIsCollapsed(false);
      }

      if (editedContents) {
        draftContents = editedContents;
      }

      isEditing = true;
      await tick();
      forceEditorFocus();
    }
  }

  function submitEdit(): boolean {
    if (!canSubmitEdit || !draftContents) {
      return false;
    }
    editedContents = undefined;
    onSubmitEdit(draftContents, mentionNodes);
    return true;
  }

  function turnOffEditMode() {
    isEditing = false;
    userExpanded = false;
    isEditing = false;
  }

  // close edit mode is when user clicks outside,
  function closeEditMode() {
    turnOffEditMode();
    editedContents = draftContents;
    draftContents = undefined;
    return true;
  }

  // cancel edit is when user hits escape
  function cancelEdit(): boolean {
    turnOffEditMode();
    editedContents = undefined;
    draftContents = undefined;
    onCancelEdit();
    isAskMode = false; // Reset ask mode on cancel
    return true;
  }

  function onContentChanged(data: ContentData) {
    if (data !== draftContents) {
      draftContents = data;
    }
  }

  function onMentionItemsUpdated(data: {
    added: IChatMentionable[];
    removed: IChatMentionable[];
    current: IChatMentionable[];
  }) {
    mentionNodes = data.current;
  }

  // Define keyboard shortcuts active during editing
  $: shortcuts = {
    /* eslint-disable @typescript-eslint/naming-convention */
    Enter: submitEdit,
    Escape: cancelEdit,
    /* eslint-enable @typescript-eslint/naming-convention */
  };

  // Hooks provided by children that we bind to
  let atMentionsComponent: AtMentions | undefined = undefined;
  let richTextEditorRoot: RichTextEditorRoot | undefined = undefined;
  const requestEditorFocus = () => richTextEditorRoot?.requestFocus();
  const forceEditorFocus = () => richTextEditorRoot?.forceFocus();

  let editorContainer: HTMLElement;

  export function getEditorContainer(): HTMLElement {
    return editorContainer;
  }
</script>

<!-- This also triggered when user clicks an edit button, so we check for editing mode -->
<svelte:window on:mousedown={isEditing ? closeEditMode : undefined} />
<div
  class="c-chat-input"
  role="button"
  tabindex="-1"
  class:is-collapsed={collapsed}
  class:is-editing={isEditing}
  bind:this={editorContainer}
  on:mousedown|stopPropagation
  style="--chat-input-collapsed-height: {collapsedMaxHeight}px;"
>
  <RichTextEditorAugment.Root
    editable={isEditing}
    variant={!isEditing || (isEditing && isAskMode) ? "accent" : "default"}
    bind:this={richTextEditorRoot}
    on:click={(e) => e.stopPropagation()}
    on:dblclick={startEdit}
  >
    {#if $chatModel.flags.enableChatMultimodal}
      {#if isEditing}
        <!-- Do not allow the user to drop images into the user message unless
         the user is editing this message -->
        <DropZone flags={$chatModel.flags} />
      {/if}
      <Image
        changeImageMode={(updatedContent) => {
          if (requestId && updatedContent) {
            $currentConversationModel.updateChatItem(requestId, {
              // eslint-disable-next-line @typescript-eslint/naming-convention
              rich_text_json_repr: updatedContent,
            });
          }
        }}
        saveImage={$chatModel.saveImage}
        saveAttachment={$chatModel.saveAttachment}
        deleteImage={$chatModel.deleteImage}
        renderImage={$chatModel.renderImage}
        isEditable={() => isEditing}
        supportedFileTypes={$chatModel.flags.enableDebugFeatures ? ["image", "text"] : ["image"]}
      />
    {/if}
    <!-- Main content and plugins -->
    <Keybindings {shortcuts} />
    <AtMentions bind:this={atMentionsComponent} {requestEditorFocus} {onMentionItemsUpdated} />
    <AskModeBadge
      {isAskMode}
      onAskModeChange={(newIsAskMode) => (isAskMode = newIsAskMode)}
      onAskModeToggleComplete={handleAskModeToggleComplete}
    />
    <Placeholder {placeholder} />
    <RichTextEditorAugment.Content content={displayContent} {onContentChanged} />

    <!-- Footer -->
    <svelte:fragment slot="footer"
      >{#if isEditing}
        <InputActionBar.Root>
          <svelte:fragment slot="leftAlign">
            {#if $conversationType === "localAgent"}
              <ToggleModeButton {chatModel} {agentConversationModel} disabled={isAskMode} />

              <TextTooltipAugment content="Ask a Question">
                <IconButtonAugment
                  class="c-chat-input__ask-mode-btn"
                  size={1}
                  variant={isAskMode ? "soft" : "ghost-block"}
                  color={isAskMode ? "accent" : "neutral"}
                  on:click={toggleAskMode}
                >
                  <MessageIcon />
                </IconButtonAugment>
              </TextTooltipAugment>
              <SeparatorAugment orientation="vertical" class="c-chat-input__ask-separator" />
            {/if}
            <!-- Character counter -->
            {#if messageLength > DEFAULT_MAX_CHAT_INPUT_CHARS}
              <span class="character-counter">
                {messageLength}/{DEFAULT_MAX_CHAT_INPUT_CHARS}
              </span>
            {/if}

            {#if $isCurrConversationAgentic && $flagsModel.enableModelRegistry}
              <ModelPicker />
            {/if}
          </svelte:fragment>
          <svelte:fragment slot="rightAlign">
            {#if $chatModel.flags.enableChatMultimodal && !messageRenderOptions.doHideMultimodalActions}
              <InputActionBar.AddFileButton accept=".png,.jpg,.jpeg" />
            {/if}

            {#if !promptEnhancerOptions.isHidden}
              <div transition:slideScale={{ axis: "x", duration: 200, scale: 0.4 }}>
                <InputActionBar.RewritePromptButton
                  onRewrite={rewritePrompt}
                  isDisabled={promptEnhancerOptions.isDisabled}
                  disabledReason={promptEnhancerOptions.disabledReason}
                />
              </div>
            {/if}

            <SendButton
              isDisabled={!canSubmitEdit}
              disabledReason={isModelDisabled ? modelDisabledReason : ""}
              primaryAction={submitEdit}
              showOnlySend={true}
            />
          </svelte:fragment>
        </InputActionBar.Root>

        <div class="c-chat-input__context-bar">
          <ContextBar {chatModel}>
            <InputActionBar.ContextMenu
              slot="prepend"
              onCloseDropdown={requestEditorFocus}
              onInsertMentionable={atMentionsComponent?.insertMentionNode}
            />
          </ContextBar>
        </div>
      {:else if showExpandButton}
        <div class="c-chat-input__collapse-button">
          <ButtonAugment
            variant="soft"
            color={isAskMode ? "accent" : "neutral"}
            size={0.5}
            on:click={() => {
              userExpanded = !userExpanded;
              setIsCollapsed(!collapsed);
            }}
          >
            {#if collapsed}
              <ExpandIcon slot="iconLeft" />
            {:else}
              <CollapseIcon slot="iconLeft" />
            {/if}
          </ButtonAugment>
        </div>
      {/if}
    </svelte:fragment>

    <slot />
  </RichTextEditorAugment.Root>
</div>

<style>
  .c-chat-input__collapse-button :global(.c-base-btn) {
    width: 100%;
  }

  .c-chat-input__collapse-button :global(svg) {
    padding: var(--ds-spacing-0_5);
    opacity: 1;
  }

  .c-chat-input :global(.c-chat-input__ask-mode-btn) {
    margin-inline: var(--ds-spacing-0_5);
  }

  .c-chat-input {
    display: flex;
    flex-direction: column;
  }

  .c-chat-input__context-bar {
    --bar-margin: calc(var(--ds-spacing-2) * -1);
    margin: var(--bar-margin);
    margin-top: var(--ds-spacing-2);
    padding: var(--ds-spacing-2);
    background-color: var(--ds-color-neutral-a2);

    &:empty {
      display: none;
    }
  }

  .c-chat-input.is-editing {
    min-width: 120px;
    cursor: auto;
  }

  .c-chat-input {
    transition: height 0.2s ease-in-out;
    height: calc-size(max-content, size);
  }

  .c-chat-input.is-collapsed {
    height: var(--chat-input-collapsed-height);
  }

  /* Hide the ask separator when it's the last child in the leftAlign slot */
  .c-chat-input :global(.c-chat-input__ask-separator:last-child) {
    display: none;
  }
</style>
