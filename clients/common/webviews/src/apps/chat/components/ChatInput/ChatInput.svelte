<script lang="ts">
  import RichTextEditorAugment from "$common-webviews/src/design-system/components/RichTextEditorAugment";
  import AskModeBadge from "$common-webviews/src/design-system/components/RichTextEditorAugment/plugins/AskModeBadge";
  import DropZone from "$common-webviews/src/design-system/components/RichTextEditorAugment/plugins/DropZone/DropZone.svelte";
  import File from "$common-webviews/src/design-system/components/RichTextEditorAugment/plugins/File";
  import Keybindings from "$common-webviews/src/design-system/components/RichTextEditorAugment/plugins/Keybindings/Keybindings.svelte";
  import type { MentionsUpdatedData } from "$common-webviews/src/design-system/components/RichTextEditorAugment/plugins/Mention/types";
  import Placeholder from "$common-webviews/src/design-system/components/RichTextEditorAugment/plugins/Placeholder";
  import type RichTextEditorRoot from "$common-webviews/src/design-system/components/RichTextEditorAugment/Root.svelte";
  import type { ContentData } from "$common-webviews/src/design-system/components/RichTextEditorAugment/types";
  import type { JSONContent } from "@tiptap/core";

  import { GitReferenceModel } from "$common-webviews/src/apps/remote-agent-manager/models/git-reference-model";
  import { getMessageRenderOptions } from "$common-webviews/src/apps/remote-agent-manager/models/message-render-options";
  import { RemoteAgentsModel } from "$common-webviews/src/apps/remote-agent-manager/models/remote-agents-model";
  import BadgeRoot from "$common-webviews/src/design-system/components/BadgeAugment/BadgeRoot.svelte";
  import { RemoteAgentStatus } from "@augment-internal/sidecar-libs/src/remote-agent/remote-agent-types";
  import type { Exchange } from "@augment-internal/sidecar-libs/src/chat/chat-types";

  import {
    AgentRequestEventName,
    AgentSessionEventName,
    RemoteAgentSetupWindowAction,
  } from "@augment-internal/sidecar-libs/src/metrics/types";
  import { getContext, onDestroy, tick } from "svelte";
  import tippy from "tippy.js";
  import { debounce } from "lodash";
  import { type AgentConversationModel } from "../../models/agent-conversation-model";
  import { DEFAULT_MAX_CHAT_INPUT_CHARS } from "../../models/chat-flags-model";
  import { type ChatModel } from "../../models/chat-model";
  import { ConversationModel } from "../../models/conversation-model";
  import { type SlashCommandModel } from "../../models/slash-command-model";
  import {
    CurrentConversationTaskStore,
    type ICurrentConversationTaskStore,
  } from "../../models/task-store";
  import { type ToolsWebviewModel } from "../../models/tools-webview-model";
  import { AgentExchangeStatus, ExchangeStatus } from "../../types/chat-message";
  import type { IChatMentionable } from "../../types/mention-option";
  import { ToolUsePhase } from "../../types/tool-use-state";
  import { getChatInputContext, getPromptEnhancerOptions } from "../../utils/chat-input-context";
  import ToggleModeButton from "../buttons/ToggleModeButton.svelte";
  import ContextBar from "../context/ContextBar.svelte";
  import InputActionBar from "../InputActionBar";
  import ModelPicker from "../ModelPicker";
  import AtMentions from "./AtMentions.svelte";
  import { MAX_IMAGES_COUNT, getFileAcceptAttribute } from "../../utils/file-utils";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import MessageIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/message-lines.svg?component";
  import SeparatorAugment from "$common-webviews/src/design-system/components/SeparatorAugment.svelte";

  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import { getInputWithEnchancePrompt } from "../../utils/prompts";
  import { isFocusWithinInteractiveElement } from "../../chat-keybindings";
  import { slideScale } from "$common-webviews/src/apps/chat/components/threads/animation-util";
  import { SoundModel } from "$common-webviews/src/apps/settings/models/sound-model";
  import { ChatModeModel } from "../../models/chat-mode-model";
  import { ANALYTICS_EVENTS } from "@augment-internal/sidecar-libs/src/client-interfaces/analytics";
  import { type ModelRegistry } from "../../models/model-registry";

  // Get chat model from context
  const chatModel = getContext<ChatModel>("chatModel");
  const chatModeModel = getContext<ChatModeModel>(ChatModeModel.key);
  const toolsWebviewModel = getContext<ToolsWebviewModel>("toolsWebviewModel");
  const agentConversationModel = getContext<AgentConversationModel>("agentConversationModel");
  const remoteAgentsModel = getContext<RemoteAgentsModel | undefined>(RemoteAgentsModel.key);
  const gitRefModel = getContext<GitReferenceModel>(GitReferenceModel.key);
  const slashCommandModel = getContext<SlashCommandModel>("slashCommandModel");
  const taskStore = getContext<ICurrentConversationTaskStore | undefined>(
    CurrentConversationTaskStore.key,
  );
  const soundModel = getContext<SoundModel>(SoundModel.key);
  const modelRegistry = getContext<ModelRegistry>("modelRegistry");
  const flagsModel = chatModel.flags;

  // Function to show error notifications for file attachment failures
  const showFileErrorNotification = (message: string) => {
    chatModel.extensionClient.showNotification({
      message,
      type: "error",
    });
  };

  $: isRemoteAgent = !!$remoteAgentsModel?.isActive;
  $: messageRenderOptions = getMessageRenderOptions($remoteAgentsModel);
  const { currentConversationModel } = chatModel;

  // Props
  export let editable = true;
  export let placeholder = "Ask or instruct Augment";
  export let hasSendButton = true;
  export let richTextEditorRoot: RichTextEditorRoot | undefined = undefined;

  let editorContainer: HTMLDivElement;
  // Telemetry tracking for prompt typing in remote agent setup context
  let hasLoggedTypingInSession = false;

  // Check if we're in remote agent setup context (creating setup scripts)
  $: isInRemoteAgentSetupContext =
    $remoteAgentsModel?.isActive && !$remoteAgentsModel?.currentAgentId;

  // Debounced function to log typing event (1.5 second delay)
  const debouncedLogTyping = debounce(async () => {
    if (!hasLoggedTypingInSession && isInRemoteAgentSetupContext && remoteAgentsModel) {
      try {
        await remoteAgentsModel.reportRemoteAgentSetupWindowEvent(
          RemoteAgentSetupWindowAction.typeInPromptWindow,
        );
        hasLoggedTypingInSession = true;
      } catch (error) {
        console.error("Failed to report type in prompt window event:", error);
      }
    }
  }, 1500);

  // we need to split the reactive storedContent statement into two lines
  // to make sure it's properly updated
  // eslint-disable-next-line @typescript-eslint/naming-convention
  $: draftExchange = $currentConversationModel.draftExchange || { rich_text_json_repr: undefined };
  $: storedContent = draftExchange.rich_text_json_repr || {
    type: "doc",
    content: [{ type: "paragraph" }],
  };

  function onContentChanged(data: ContentData) {
    $currentConversationModel.saveDraftExchange(data.rawText, data.richTextJsonRepr);

    // Trigger telemetry logging for typing in remote agent setup context
    if (data.rawText && data.rawText.trim().length > 0) {
      debouncedLogTyping();
    }
  }
  function onMentionItemsUpdated(data: MentionsUpdatedData<IChatMentionable>) {
    $currentConversationModel.saveDraftMentions(data.current);
  }
  let lastConversationType: string | undefined = undefined;
  $: conversationType = chatModeModel.chatModeType;

  let isAskMode = false;

  $: {
    if ($conversationType !== lastConversationType) {
      // Reset ask mode when switching away from local agent
      if ($conversationType !== "localAgent") {
        isAskMode = false;
      }
    }
    if (
      $chatModel.conversations[$currentConversationModel.id] &&
      ConversationModel.isNew($chatModel.conversations[$currentConversationModel.id]) &&
      lastConversationType !== $conversationType
    ) {
      // don't force focus the editor when we switch to remote agent and on the first load
      if (lastConversationType && $conversationType !== "remoteAgent") {
        tick().then(() => forceEditorFocus());
      }
    }
    lastConversationType = $conversationType;
  }
  let lastThreadId: string | undefined = undefined;
  $: {
    if (lastThreadId !== $currentConversationModel.id) {
      // don't force focus the editor when we switch to remote agent and on the first load
      if (lastThreadId && $conversationType !== "remoteAgent") {
        tick().then(() => forceEditorFocus());
      }
      lastThreadId = $currentConversationModel.id;
    }
  }

  // Whenever a new conversation is created, we need to focus the editor
  const cleanupNewConvoListener = currentConversationModel.onNewConversation(() =>
    forceEditorFocus(),
  );
  onDestroy(cleanupNewConvoListener);

  // Focus the editor when remote agent transitions from starting to running/idle
  let lastRemoteAgentStatus: RemoteAgentStatus | undefined = undefined;
  $: {
    const currentStatus = $remoteAgentsModel?.currentAgent?.status;
    if (
      lastRemoteAgentStatus === RemoteAgentStatus.agentStarting &&
      (currentStatus === RemoteAgentStatus.agentRunning ||
        currentStatus === RemoteAgentStatus.agentIdle)
    ) {
      tick().then(() => forceEditorFocus());
    }
    lastRemoteAgentStatus = currentStatus;
  }

  // When agentic, hide / commands
  $: ({ isCurrConversationAgentic, agentExchangeStatus: ideAgentExchangeStatus } =
    agentConversationModel);

  $: isAgentConversation = $isCurrConversationAgentic || $remoteAgentsModel?.isActive;
  $: showModelPicker = isAgentConversation && $flagsModel.enableModelRegistry;

  $: currentSendMode = $chatModel.sendModeModel.mode;
  $: _chatInputContext = getChatInputContext(
    $chatModel,
    agentConversationModel,
    toolsWebviewModel,
    slashCommandModel,
    $currentSendMode,
    taskStore,
    $remoteAgentsModel,
    gitRefModel,
    richTextEditorRoot,
    modelRegistry,
  );
  $: showCancelButton = _chatInputContext.showCancelButton;
  $: messageLength = $currentConversationModel.draftExchange?.request_message?.length || 0;
  $: isDisabled = _chatInputContext.isDisabled;
  $: disabledReason = _chatInputContext.disabledReason;

  // Function to wrap an action and turn off ask mode after successful execution
  function wrapSendAction(actionFn: () => boolean) {
    return () => {
      const result = actionFn();

      // Reset ask mode after successfully sending a message in local agent mode
      if (result && $conversationType === "localAgent" && isAskMode) {
        isAskMode = false;
        // Wait for the next tick to allow the editor to clear the content
        // and keep the ask mode badge
        tick().then(() => {
          isAskMode = true;
        });
      }
      if (result && soundModel) {
        // Attempt to play sound with 0 volume to unlock the sound with a user event
        void soundModel.unlockSound();
      }
      return result;
    };
  }

  $: sendAction = wrapSendAction(_chatInputContext.action);
  $: cancelAction = _chatInputContext.cancelAction;

  /** Key for the RichTextEditorAugment.Root to force it to be recreated */
  $: richTextEditorKey = `${$isCurrConversationAgentic}-${$remoteAgentsModel?.isActive}-${placeholder}`;

  // Create conditional handlers for keyboard shortcuts
  const handleEnterKey = () => {
    // Don't do anything if input is disabled (e.g., agent is running, no message, etc.)
    if (isDisabled) {
      return false;
    }

    $chatModel.eventTracker?.trackEvent(ANALYTICS_EVENTS.SEND_ACTION_TRIGGERED, {
      source: "keybinding",
    });

    // Call the action (send message, create agent, etc.)
    return sendAction();
  };

  // Analytics helper functions
  const trackCancelAction = (source: "button" | "keybinding") => {
    $chatModel.eventTracker?.trackEvent(ANALYTICS_EVENTS.CANCEL_ACTION_TRIGGERED, { source });
  };

  const trackResendAction = () => {
    $chatModel.eventTracker?.trackEvent(ANALYTICS_EVENTS.RESEND_ACTION_TRIGGERED, {
      source: "button",
    });
  };

  const handleEscapeKey = () => {
    // If remote agent is running and can be cancelled, stop it
    if (showCancelButton) {
      trackCancelAction("keybinding");
      return cancelAction();
    }
    // If local/IDE agent is running and can be stopped, stop it
    if (showStopAgent) {
      trackCancelAction("keybinding");
      agentConversationModel.interruptAgent().catch((error) => {
        console.error("Failed to interrupt agent:", error);
      });
      return true;
    }
    // Otherwise, unfocus the editor (default behavior)
    return unfocusEditor();
  };

  $: shortcuts = {
    /* eslint-disable @typescript-eslint/naming-convention */
    Enter: handleEnterKey,
    /* eslint-disable @typescript-eslint/naming-convention */
    Escape: handleEscapeKey,
  };

  // Hooks provided by children that we bind to
  let atMentionsComponent: AtMentions | undefined = undefined;
  const requestEditorFocus = () => richTextEditorRoot?.requestFocus();
  const forceEditorFocus = () => richTextEditorRoot?.forceFocus?.();
  const unfocusEditor = () => {
    richTextEditorRoot?.blur();
    return true;
  };

  $: conversationModel = $chatModel.currentConversationModel;
  let lastMessageCancelled = false;
  $: {
    const lastExchange = $conversationModel?.lastExchange?.status;
    const lastExchangeCancelled = lastExchange === ExchangeStatus.cancelled;
    const lastToolPhase = $conversationModel?.getLastToolUseState().phase;
    const lastToolCancelled = lastToolPhase === ToolUsePhase.cancelled;
    lastMessageCancelled = lastExchangeCancelled || lastToolCancelled;
  }
  $: showStopAgent =
    !$remoteAgentsModel?.isActive &&
    $isCurrConversationAgentic &&
    $ideAgentExchangeStatus !== AgentExchangeStatus.notRunning;
  // only show resend if textbox is empty
  $: showResend =
    !$remoteAgentsModel?.isActive &&
    lastMessageCancelled &&
    $currentConversationModel.draftExchange?.request_message === "";
  // Change placeholder text if resending

  $: placeholderText = showResend ? "Resend to continue" : placeholder;

  const currentRepoUrl = gitRefModel?.currentRepositoryUrl;

  $: promptEnhancerOptions = getPromptEnhancerOptions(
    $remoteAgentsModel,
    $currentRepoUrl,
    isDisabled,
  );

  $: showActionButton = $remoteAgentsModel && $remoteAgentsModel.generateSetupScriptSelected;

  function toggleAskMode() {
    isAskMode = !isAskMode;
  }

  function handleAskModeToggleComplete() {
    setTimeout(() => {
      richTextEditorRoot?.requestFocus();
    }, 100);
  }

  // Function to rewrite the prompt using the current conversation model
  async function rewritePrompt() {
    // Handle enhance prompt button click

    try {
      // Get the current prompt text
      /* eslint-disable @typescript-eslint/naming-convention */
      const currentPrompt = $currentConversationModel.draftExchange?.request_message || "";
      /* eslint-enable @typescript-eslint/naming-convention */

      // Check if there's a prompt to enhance

      // If there's no text, don't do anything
      if (!currentPrompt || currentPrompt.trim() === "") {
        // Exit if there's no prompt to enhance
        return;
      }

      // Store the original content in case we need to restore it
      const originalContent = $currentConversationModel.draftExchange?.rich_text_json_repr;

      try {
        let chatHistory: Exchange[] | undefined = undefined;
        if ($remoteAgentsModel?.isActive) {
          // The remote agents model active conversation is different fom the current conversation model, so we need to inject the remote
          // agent conversation to sendSilentExchange if the user is in a remote agent conversation
          // Convert from ExchangeWithStatus to Exchange
          chatHistory = $remoteAgentsModel.getCurrentChatHistory().map((exchange) => ({
            /* eslint-disable @typescript-eslint/naming-convention */
            request_id: exchange.request_id || "",
            request_message: exchange.request_message,
            response_text: exchange.response_text || "",
            /* eslint-enable @typescript-eslint/naming-convention */
          }));
        }

        // Create a temporary exchange to send to the model
        let { responseText, requestId } = await $currentConversationModel.sendSilentExchange({
          /* eslint-disable @typescript-eslint/naming-convention */
          request_message: getInputWithEnchancePrompt(currentPrompt),
          /* eslint-enable @typescript-eslint/naming-convention */
          disableRetrieval: false,
          chatHistory,
        });

        // Report the metric associated with the enhance prompt request
        $currentConversationModel.extensionClient.reportAgentRequestEvent({
          eventName: AgentRequestEventName.enhancedPrompt,
          conversationId: $currentConversationModel.id,
          requestId: requestId!,
          chatHistoryLength: $currentConversationModel.chatHistory.length,
        });

        // Keep session level metric for backwards compatibility
        $currentConversationModel.extensionClient.reportAgentSessionEvent({
          eventName: AgentSessionEventName.enhancedPrompt,
          conversationId: $currentConversationModel.id,
        });

        // Process the enhanced prompt from the LLM

        // Extract the enhanced prompt from between the tags
        let enhancedPrompt = "";
        const tagPattern = /<augment-enhanced-prompt>([\s\S]*?)<\/augment-enhanced-prompt>/;

        const match = responseText.match(tagPattern);

        if (match && match[1]) {
          // If we found the tags, use the content between them
          enhancedPrompt = match[1].trim();
        } else {
          // Fallback: use the original prompt if tags aren't found
          enhancedPrompt = currentPrompt;
          console.warn("Enhanced prompt tags not found in response, using original prompt");

          // Show a popup notification that enhancement failed
          const errorTooltip = document.createElement("div");
          errorTooltip.className = "enhance-prompt-error-tooltip";
          errorTooltip.textContent = "Failed to enhance prompt, sorry about that";
          document.body.appendChild(errorTooltip);

          // Create a tooltip instance
          const tooltip = tippy(errorTooltip, {
            content: errorTooltip,
            trigger: "manual",
            placement: "top",
            theme: "error",
            duration: [300, 2000], // Show quickly, hide after 2 seconds
            onHidden: () => {
              errorTooltip.remove(); // Clean up the DOM
            },
          });

          // Position near the rewrite button and show
          const rewriteButton = editorContainer.querySelector(".c-rewrite-prompt-button");
          if (rewriteButton) {
            tooltip.setProps({
              getReferenceClientRect: () => rewriteButton.getBoundingClientRect(),
            });
            tooltip.show();

            // Auto-hide after 3 seconds
            setTimeout(() => {
              tooltip.hide();
            }, 3000);
          } else {
            errorTooltip.remove(); // Clean up if button not found
          }
        }

        // Create enhanced content for the editor

        if (enhancedPrompt && enhancedPrompt.trim() !== "") {
          // Get the original content to preserve all non-text nodes
          const originalContent = $currentConversationModel.draftExchange?.rich_text_json_repr;

          // Create a new enhanced content that preserves all non-paragraph nodes
          let enhancedContent: JSONContent;

          if (originalContent && typeof originalContent === "object") {
            // Create a copy of the original content to avoid mutating it
            enhancedContent = { ...originalContent };

            if (enhancedContent.content && Array.isArray(enhancedContent.content)) {
              // Create a new content array
              const newContent: JSONContent[] = [];

              // Flag to track if we've already processed the first paragraph
              let firstParagraphReplaced = false;

              // Process each top-level node
              for (const node of enhancedContent.content) {
                if (node.type === "paragraph" && !firstParagraphReplaced) {
                  // Replace the first paragraph with the enhanced prompt
                  // Split the enhanced prompt into lines
                  const lines = enhancedPrompt.split("\n");

                  // Create a new paragraph with the enhanced prompt text
                  const newParagraph: JSONContent = {
                    type: "paragraph",
                    content: [],
                  };

                  // Add each line with hardBreak nodes between them
                  lines.forEach((line, index) => {
                    // Add the text node for this line
                    if (line.length > 0) {
                      newParagraph.content?.push({
                        type: "text",
                        text: line,
                      });
                    }

                    // Add a hardBreak after each line except the last one
                    if (index < lines.length - 1) {
                      newParagraph.content?.push({
                        type: "hardBreak",
                      });
                    }
                  });

                  // Add the new paragraph to the content
                  newContent.push(newParagraph);

                  // Mark that we've replaced the first paragraph
                  firstParagraphReplaced = true;
                } else if (node.type !== "paragraph") {
                  // Preserve all non-paragraph nodes
                  newContent.push(node);
                }
                // Skip all other paragraph nodes (they will be deleted)
              }

              // Update the content with our new array
              enhancedContent.content = newContent;
            }
          } else {
            // If there's no original content or it's not an object, create simple content
            enhancedContent = {
              type: "doc",
              content: [
                {
                  type: "paragraph",
                  content: [{ type: "text", text: enhancedPrompt }],
                },
              ],
            };
          }

          // Set the content in a single operation
          $currentConversationModel.saveDraftExchange(enhancedPrompt, enhancedContent);
          // Position cursor at beginning after content update

          // We already set the content above, no need to do it again

          // Focus the editor and position cursor at beginning
          setTimeout(() => {
            // First focus the editor
            forceEditorFocus();

            // Use the Selection API to position cursor at beginning
            try {
              // Find the editor element
              const editorElement = editorContainer.querySelector(".ProseMirror");
              if (editorElement) {
                // Find the first paragraph and text node
                const firstParagraph = editorElement.querySelector("p");
                if (firstParagraph) {
                  // Create a selection at the beginning
                  const range = document.createRange();
                  const selection = window.getSelection();

                  // Set selection at the beginning of the paragraph
                  range.setStart(firstParagraph, 0);
                  range.collapse(true);

                  // Apply the selection
                  if (selection) {
                    selection.removeAllRanges();
                    selection.addRange(range);
                  }
                }
              }
            } catch (e) {
              // Error occurred while positioning cursor
            }
          }, 200); // TODO(guy) make this more robust, don't rely on magic timeout value
        } else {
          // If we didn't get a valid response, restore the original content
          $currentConversationModel.saveDraftExchange(
            /* eslint-disable @typescript-eslint/naming-convention */
            originalContent ? $currentConversationModel.draftExchange?.request_message || "" : "",
            /* eslint-enable @typescript-eslint/naming-convention */
            originalContent || { type: "doc", content: [] },
          );
          // Original content restored due to invalid response
        }
      } catch (error) {
        // Error occurred while calling LLM
        // Restore the original content on error
        $currentConversationModel.saveDraftExchange(
          /* eslint-disable @typescript-eslint/naming-convention */
          originalContent ? $currentConversationModel.draftExchange?.request_message || "" : "",
          /* eslint-enable @typescript-eslint/naming-convention */
          originalContent || { type: "doc", content: [] },
        );
      }

      // Focus the editor
      forceEditorFocus();
    } catch (error) {
      // Error occurred in rewritePrompt function
    }
  }
</script>

<svelte:window
  on:focus={() => {
    if (isRemoteAgent || isFocusWithinInteractiveElement()) return;
    requestEditorFocus();
  }}
  on:blur={$chatModel.saveImmediate}
  on:beforeunload={$chatModel.saveImmediate}
/>

<div class="c-chat-input" data-testid="design-system-chat-input" bind:this={editorContainer}>
  <!--
  We need to recreate the editor when the conversation type changes.

  More details: https://app.staging.augmentcode.com/share/mDoyO1Sk1kk
  -->

  {#key richTextEditorKey}
    <RichTextEditorAugment.Root
      {editable}
      variant={isAskMode ? "accent" : "default"}
      bind:this={richTextEditorRoot}
      {showActionButton}
    >
      <slot name="banner" slot="banner" />
      <slot name="activeButton" slot="activeButton" />
      <!-- Main content and plugins -->
      <Keybindings {shortcuts} />
      {#if !messageRenderOptions.doHideAtMentions}
        <AtMentions bind:this={atMentionsComponent} {requestEditorFocus} {onMentionItemsUpdated} />
      {/if}
      <AskModeBadge
        {isAskMode}
        onAskModeChange={(newIsAskMode) => {
          isAskMode = newIsAskMode;
        }}
        onAskModeToggleComplete={handleAskModeToggleComplete}
      />
      <Placeholder placeholder={placeholderText} />
      <RichTextEditorAugment.Content content={storedContent} {onContentChanged} />
      {#if $chatModel.flags.enableChatMultimodal && !messageRenderOptions.doHideMultimodalActions}
        <File
          saveImage={$chatModel.saveImage}
          saveAttachment={$chatModel.saveAttachment}
          deleteImage={$chatModel.deleteImage}
          renderImage={$chatModel.renderImage}
          maxImages={MAX_IMAGES_COUNT}
          supportedFileTypes={$flagsModel.enableDebugFeatures ? ["image", "text"] : ["image"]}
          onValidationError={showFileErrorNotification}
        />
        <DropZone flags={$chatModel.flags} />
      {/if}

      <!-- Footer -->
      <div slot="footer">
        <InputActionBar.Root>
          <svelte:fragment slot="leftAlign">
            {#if isRemoteAgent}
              <InputActionBar.NotifyButton agentId={$remoteAgentsModel?.currentAgentId} />
            {/if}
            {#if $flagsModel.enableAgentMode}
              <ToggleModeButton {chatModel} {agentConversationModel} disabled={isAskMode} />
            {/if}
            {#if $conversationType === "localAgent"}
              <TextTooltipAugment content="Ask a Question">
                <IconButtonAugment
                  size={1}
                  variant={isAskMode ? "soft" : "ghost-block"}
                  color={isAskMode ? "accent" : "neutral"}
                  class="c-chat-input__ask-mode-btn"
                  on:click={toggleAskMode}
                >
                  <MessageIcon />
                </IconButtonAugment>
              </TextTooltipAugment>
              <SeparatorAugment orientation="vertical" class="c-chat-input__ask-separator" />
            {/if}

            <!-- Character counter -->
            {#if messageLength > DEFAULT_MAX_CHAT_INPUT_CHARS}
              <span class="character-counter">
                {messageLength}/{DEFAULT_MAX_CHAT_INPUT_CHARS}
              </span>
            {/if}

            <!-- Model Picker -->
            {#if showModelPicker}
              <ModelPicker />
            {/if}
          </svelte:fragment>
          <svelte:fragment slot="rightAlign">
            {#if $chatModel.flags.enableChatMultimodal && !messageRenderOptions.doHideMultimodalActions}
              <InputActionBar.AddFileButton
                accept={getFileAcceptAttribute($flagsModel.enableDebugFeatures)}
              />
            {/if}
            {#if !showCancelButton && !showStopAgent && !showResend && !promptEnhancerOptions.isHidden}
              <div transition:slideScale={{ axis: "x", duration: 200, scale: 0.4 }}>
                <InputActionBar.RewritePromptButton
                  onRewrite={rewritePrompt}
                  isDisabled={promptEnhancerOptions.isDisabled}
                  disabledReason={promptEnhancerOptions.disabledReason}
                />
              </div>
            {/if}
            {#if remoteAgentsModel?.isActive}
              <BadgeRoot color="premium" size={1} variant="soft">Beta</BadgeRoot>
            {/if}
            {#if showCancelButton}
              <InputActionBar.CancelButton {cancelAction} onAnalyticsTrack={trackCancelAction} />
            {:else if showStopAgent}
              <InputActionBar.CancelButton
                cancelAction={agentConversationModel.interruptAgent}
                onAnalyticsTrack={trackCancelAction}
              />
            {:else if showResend}
              <InputActionBar.ResendButton
                resendAction={$conversationModel?.resendLastExchange}
                onAnalyticsTrack={trackResendAction}
              />
            {:else if hasSendButton}
              <InputActionBar.SendButton {isDisabled} {disabledReason} primaryAction={sendAction} />
            {/if}
          </svelte:fragment>
        </InputActionBar.Root>

        <div class="c-chat-input__context-bar">
          {#if !messageRenderOptions.doHideContextBar}
            <ContextBar {chatModel}>
              <slot slot="prepend">
                {#if !messageRenderOptions.doHideAtMentions}
                  <InputActionBar.ContextMenu
                    onCloseDropdown={requestEditorFocus}
                    onInsertMentionable={atMentionsComponent?.insertMentionNode}
                  />
                {/if}
              </slot>
            </ContextBar>
          {/if}
        </div>
      </div>
    </RichTextEditorAugment.Root>
  {/key}
</div>

<style>
  .c-chat-input {
    width: 100%;
    height: 100%;

    max-width: 100%;
    max-height: 100%;

    gap: var(--ds-spacing-2);
    display: flex;
    flex-direction: column;
    /* to prevent the toggle mode tooltip from being overlapped */
    position: relative;
    /* Higher z-index to ensure dropdowns appear above ThreadsList */
    z-index: var(--z-chat-input);
  }

  .c-chat-input :global(.c-chat-input__ask-mode-btn) {
    margin-inline: var(--ds-spacing-0_5);
  }
  .c-chat-input :global(.c-chat-input__ask-mode-btn svg) {
    opacity: 1;
  }

  /* Hide the ask separator when it's the last child in the leftAlign slot */
  .c-chat-input :global(.c-chat-input__ask-separator:last-child) {
    display: none;
  }

  .c-chat-input__context-bar {
    --bar-margin: calc(var(--ds-spacing-2) * -1);
    margin: var(--bar-margin);
    margin-top: var(--ds-spacing-2);
    padding: var(--ds-spacing-2);
    background-color: var(--ds-color-neutral-a2);

    &:empty {
      display: none;
    }
  }

  .character-counter {
    font-size: var(--ds-font-size-1);
    margin-left: var(--ds-spacing-2);
    color: var(--ds-color-error-a11);
    font-weight: var(--ds-font-weight-bold);
  }

  :global(.enhance-prompt-error-tooltip) {
    background-color: var(--ds-color-error-a3);
    color: var(--ds-color-error-a11);
    padding: var(--ds-spacing-2);
    border-radius: var(--ds-radius-2);
    font-size: var(--ds-font-size-1);
    box-shadow: var(--ds-shadow-2);
    max-width: 250px;
    text-align: center;
  }

  :global(.tippy-box[data-theme~="error"]) {
    background-color: var(--ds-color-error-a3);
    color: var(--ds-color-error-a11);
  }

  :global(.tippy-box[data-theme~="error"] .tippy-arrow) {
    color: var(--ds-color-error-a3);
  }
</style>
