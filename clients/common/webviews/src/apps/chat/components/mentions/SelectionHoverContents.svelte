<script lang="ts">
  import type { editor } from "monaco-editor";
  import { resolveMonacoLanguage } from "$common-webviews/src/common/utils/monaco-language";
  import { type FileDetails } from "$vscode/src/webview-providers/webview-messages";
  import IconFilePath from "$common-webviews/src/common/components/icon-file-path/IconFilePath.svelte";
  import { getContext } from "svelte";
  import { type ChatModel } from "../../models/chat-model";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import { getFileDirectory, getFilename } from "$common-webviews/src/common/utils/file-paths";

  export let originalCode: string | undefined;
  export let pathName: string | undefined = undefined;
  export let range: FileDetails["fullRange"];

  // We only want to import this in production/development, not in tests. This is because
  // monaco-based components are unhappy in jsdom environments, so we only import this when needed.
  // eslint-disable-next-line no-undef
  const isBuild = process.env.NODE_ENV === "production" || process.env.NODE_ENV === "development";
  const c = isBuild
    ? import("$common-webviews/src/design-system/components/MonacoProvider/SimpleMonaco.svelte")
    : undefined;

  // Use the shared language detection logic from language-icon utility
  $: language = resolveMonacoLanguage(pathName ?? "");

  let editorInstance: editor.IStandaloneCodeEditor | undefined;

  let style: string = "";
  let initializedMonacoSize = false;
  const updateSize = async (widthPx: number, heightPx: number): Promise<void> => {
    initializedMonacoSize = true;
    style = `
         --editor-width: ${widthPx}px;
         --editor-height: min(${heightPx}px, 30vh);
    `;
  };
  $: editorInstance?.onDidContentSizeChange((e: editor.IContentSizeChangedEvent) =>
    updateSize(e.contentWidth, e.contentHeight),
  );

  $: {
    if (editorInstance && !initializedMonacoSize) {
      updateSize(editorInstance?.getContentWidth(), editorInstance?.getContentHeight());
    }
  }

  const chatModel: ChatModel | undefined = getContext("chatModel");
  const openLocalFile = () => {
    if (!pathName) return;
    chatModel?.extensionClient.openFile({
      repoRoot: "",
      pathName: pathName,
      fullRange: range,
    });
  };

  // Monaco editor options with conditional line numbers
  $: monacoOptions = {
    automaticLayout: true,
    lineNumbers: range ? (lineNumber: number) => `${range.startLineNumber + lineNumber}` : "off",
    lineDecorationsWidth: 0,
    wrappingIndent: "same",
    padding: { top: 0, bottom: 0 },
    wordWrap: "off",
    contextmenu: false,
    wordBasedSuggestions: "off",
    renderLineHighlight: "none",
    occurrencesHighlight: "off",
    selectionHighlight: false,
    codeLens: false,
    links: false,
    hover: { enabled: false },
    hideCursorInOverviewRuler: true,
    stickyScroll: { enabled: false },
    renderWhitespace: "none",
    renderFinalNewline: "on",
  } as const satisfies editor.IStandaloneEditorConstructionOptions;
</script>

<div class="c-selection-hover-contents" {style}>
  {#if pathName}
    <div class="c-selection-hover-contents__path">
      <ButtonAugment variant="ghost-block" color="neutral" size={1} on:click={openLocalFile}>
        <IconFilePath value={[]} filepath={getFilename(pathName)} onCodeAction={openLocalFile} />
      </ButtonAugment>

      {#if getFileDirectory(pathName)}
        <span class="c-selection-hover-contents__directory">
          {getFileDirectory(pathName)}
        </span>
      {/if}
    </div>
  {/if}

  <!-- We only want to import this in production/development, not in tests -->
  {#if c}
    {#await c}
      <pre class="c-selection-preview__code"><code>{originalCode ?? ""}</code></pre>
    {:then component}
      <div class="c-selection-hover-contents__monaco">
        <svelte:component
          this={component.default}
          bind:editorInstance
          text={originalCode ?? ""}
          lang={language}
          {pathName}
          options={monacoOptions}
        />
      </div>
    {:catch}
      <pre class="c-selection-preview__code"><code>{originalCode ?? ""}</code></pre>
    {/await}
  {/if}
</div>

<style>
  .c-selection-hover-contents {
    max-width: 100%;
    width: var(--editor-width);
    font-size: var(--augment-font-size);
    overflow: hidden;
  }

  .c-selection-hover-contents__path {
    padding: var(--ds-spacing-1);
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: var(--ds-spacing-1);
    background-color: var(--ds-color-neutral-a2);
  }

  .c-selection-hover-contents__directory {
    color: var(--ds-color-neutral-10);
    overflow: hidden;
    direction: rtl;
    text-wrap: nowrap;
    text-overflow: ellipsis;
  }

  .c-selection-hover-contents__monaco {
    overflow: hidden;
    width: var(--editor-width);
    height: var(--editor-height);
    max-width: 100%;
    --augment-border: solid 1px transparent;
    --augment-border-radius: 0;
  }

  .c-selection-preview__code {
    overflow: scroll;
  }
</style>
