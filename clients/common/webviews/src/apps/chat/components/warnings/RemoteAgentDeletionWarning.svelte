<script lang="ts">
  import { RemoteAgentsModel } from "$common-webviews/src/apps/remote-agent-manager/models/remote-agents-model";
  import { getContext } from "svelte";
  import ChatInputBanner from "./ChatInputBanner.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import {
    differenceInDays,
    formatFullTimestamp,
  } from "$common-webviews/src/common/utils/time-utils";

  const remoteAgentModel = getContext<RemoteAgentsModel>(RemoteAgentsModel.key);

  $: currentAgent = $remoteAgentModel.currentAgent;
  $: daysLeft = currentAgent ? differenceInDays(new Date(currentAgent.expires_at), new Date()) : 0;
  $: dateTooltip = currentAgent ? formatFullTimestamp(currentAgent.expires_at) : undefined;

  let remainingTime: string = "";
  $: {
    if (daysLeft === 0) {
      remainingTime = "today";
    } else if (daysLeft < 0) {
      remainingTime = "soon";
    } else {
      remainingTime = `in ${daysLeft} day${daysLeft === 1 ? "" : "s"}`;
    }
  }
</script>

<ChatInputBanner color="warning">
  <div class="l-remote-agent-deletion-warning__left" slot="left-content">
    <TextAugment size={1} color="neutral">
      This remote agent will be deleted <TextTooltipAugment content={dateTooltip}
        >{remainingTime}</TextTooltipAugment
      > due to inactivity.
    </TextAugment>
  </div>
</ChatInputBanner>

<style>
  .l-remote-agent-deletion-warning__left :global(.l-tooltip-trigger) {
    display: inline;
  }
</style>
