<script lang="ts">
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import { TooltipTriggerOn } from "$common-webviews/src/design-system/_primitives/TooltipAugment/types";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import XMark from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/xmark.svg?component";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import { host } from "$common-webviews/src/common/hosts/host";
  import { WebViewMessageType } from "$vscode/src/webview-providers/webview-messages";
  import type {
    BannerNotificationActionItem,
    ShowBannerNotificationData,
  } from "$vscode/src/webview-providers/webview-messages";

  import ChatInputBanner from "./ChatInputBanner.svelte";
  import { notificationBannerStore } from "../../models/notification-banner-store";

  export let notificationId: string;
  export let message: string;
  export let level: ShowBannerNotificationData["level"] = "info";
  export let actionItems: BannerNotificationActionItem[] = [];

  function dismissNotification(actionItemTitle?: string) {
    const id = notificationId;
    // Remove from local store immediately for responsive UI
    notificationBannerStore.removeNotification(id);

    // Send dismissal message to extension
    host.postMessage({
      type: WebViewMessageType.dismissBannerNotification,
      data: {
        notificationId: id,
        actionItemTitle,
      },
    });
  }

  function handleActionClick(actionItem: BannerNotificationActionItem) {
    if (actionItem.url) {
      host.postMessage({
        type: WebViewMessageType.augmentLink,
        data: actionItem.url,
      });
    }
    dismissNotification(actionItem.title);
  }

  // Map notification level to banner color
  const levelToColor: Record<ShowBannerNotificationData["level"], "accent" | "warning" | "error"> =
    {
      info: "accent",
      warning: "warning",
      error: "error",
    };
  let bannerColor: "accent" | "warning" | "error";
  $: bannerColor = levelToColor[level];
</script>

<ChatInputBanner text={message} color={bannerColor}>
  <div
    class="l-notification-banner__right"
    class:is-accent={bannerColor === "accent"}
    class:is-warning={bannerColor === "warning"}
    slot="right-content"
  >
    {#each actionItems as actionItem}
      <div class="l-notification-banner__action-btn">
        <ButtonAugment
          size={1}
          variant="solid"
          color={bannerColor}
          on:click={() => handleActionClick(actionItem)}
          data-testid="notification-action-button"
        >
          {actionItem.title}
        </ButtonAugment>
      </div>
    {/each}
    <TextTooltipAugment content="Dismiss" triggerOn={[TooltipTriggerOn.Hover]}>
      <IconButtonAugment
        size={1}
        variant="ghost-block"
        color="neutral"
        on:click={() => dismissNotification()}
        data-testid="dismiss-notification-button"
      >
        <XMark />
      </IconButtonAugment>
    </TextTooltipAugment>
  </div>
</ChatInputBanner>

<style>
  .l-notification-banner__right {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: var(--ds-spacing-1);
  }

  .l-notification-banner__right :global(svg) {
    --icon-size: 14px;
  }

  /* Override button color to lighter tints for non-error banners */
  .l-notification-banner__right.is-accent .l-notification-banner__action-btn :global(.c-base-btn) {
    --base-btn-bg-color: var(--ds-color-accent-a8);
    --base-btn-hover-bg-color: var(--ds-color-accent-a9);
  }

  .l-notification-banner__right.is-warning .l-notification-banner__action-btn :global(.c-base-btn) {
    --base-btn-bg-color: var(--ds-color-warning-a8);
    --base-btn-hover-bg-color: var(--ds-color-warning-a9);
  }
</style>
