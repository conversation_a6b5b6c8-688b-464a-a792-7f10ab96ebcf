<script lang="ts">
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import { TooltipTriggerOn } from "$common-webviews/src/design-system/_primitives/TooltipAugment/types";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import TrashCan from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/trash-can.svg?component";
  import XMark from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/xmark.svg?component";
  import Check from "$common-webviews/src/design-system/icons/check.svelte";
  import ChatInputBanner from "./ChatInputBanner.svelte";

  export let onDeleteAllHistory: () => void;
  export let onDismiss: () => void;
  export let onHideForSession: () => void;

  // State for the two-step dismiss process
  let showConfirmation = false;

  function handleDismissClick() {
    showConfirmation = true;
  }

  function handleHideForSession() {
    onHideForSession();
  }

  function handleCancelConfirmation() {
    onDismiss();
  }
</script>

<ChatInputBanner text="Large conversation histories can lead to worse performance." color="warning">
  <div class="l-conversation-history-warning__buttons" slot="right-content">
    {#if !showConfirmation}
      <!-- Initial state: Delete All History and Dismiss buttons -->
      <TextTooltipAugment
        content="Delete all conversations"
        triggerOn={[TooltipTriggerOn.Hover, TooltipTriggerOn.Click]}
      >
        <IconButtonAugment
          size={1}
          variant="ghost-block"
          color="neutral"
          on:click={onDeleteAllHistory}
          data-testid="delete-all-history-warning-button"
        >
          <TrashCan />
        </IconButtonAugment>
      </TextTooltipAugment>
      <TextTooltipAugment content="Dismiss" triggerOn={[TooltipTriggerOn.Hover]}>
        <IconButtonAugment
          size={1}
          variant="ghost-block"
          color="neutral"
          on:click={handleDismissClick}
          data-testid="dismiss-conversation-history-warning-button"
        >
          <XMark />
        </IconButtonAugment>
      </TextTooltipAugment>
    {:else}
      <!-- Confirmation state: Hide for this session text and buttons -->
      <span class="l-conversation-history-warning__confirmation-text">Hide for this session?</span>
      <div class="l-conversation-history-warning__confirmation-buttons">
        <TextTooltipAugment
          content="Yes, hide for this session"
          triggerOn={[TooltipTriggerOn.Hover]}
        >
          <IconButtonAugment
            size={1}
            variant="ghost-block"
            color="neutral"
            on:click={handleHideForSession}
            data-testid="hide-conversation-history-warning-for-session-button"
          >
            <Check />
          </IconButtonAugment>
        </TextTooltipAugment>
        <TextTooltipAugment content="No, dismiss once" triggerOn={[TooltipTriggerOn.Hover]}>
          <IconButtonAugment
            size={1}
            variant="ghost-block"
            color="neutral"
            on:click={handleCancelConfirmation}
            data-testid="cancel-conversation-history-confirmation-button"
          >
            <XMark />
          </IconButtonAugment>
        </TextTooltipAugment>
      </div>
    {/if}
  </div>
</ChatInputBanner>

<style>
  .l-conversation-history-warning__buttons {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: var(--ds-spacing-1);
  }

  .l-conversation-history-warning__confirmation-text {
    font-size: var(--ds-font-size-1);
    color: var(--ds-color-neutral-11);
    margin-right: var(--ds-spacing-2);
    white-space: nowrap;
  }

  .l-conversation-history-warning__confirmation-buttons {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: var(--ds-spacing-1);
  }

  .l-conversation-history-warning__buttons :global(svg),
  .l-conversation-history-warning__confirmation-buttons :global(svg) {
    width: 14px;
    height: 14px;
    fill: var(--icon-color, currentColor);
  }
</style>
