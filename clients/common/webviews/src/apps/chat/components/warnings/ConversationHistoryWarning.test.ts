import { describe, it, expect, vi } from "vitest";
import { render, screen, fireEvent } from "@testing-library/svelte";
import ConversationHistoryWarning from "./ConversationHistoryWarning.svelte";

describe("ConversationHistoryWarning", () => {
  it("renders the warning message", () => {
    const mockOnDeleteAllHistory = vi.fn();
    const mockOnDismiss = vi.fn();
    const mockOnHideForSession = vi.fn();
    render(ConversationHistoryWarning, {
      onDeleteAllHistory: mockOnDeleteAllHistory,
      onDismiss: mockOnDismiss,
      onHideForSession: mockOnHideForSession,
    });

    expect(
      screen.getByText("Large conversation histories can lead to worse performance."),
    ).toBeInTheDocument();
  });

  it("calls onDeleteAllHistory when the Delete All History button is clicked", async () => {
    const mockOnDeleteAllHistory = vi.fn();
    const mockOnDismiss = vi.fn();
    const mockOnHideForSession = vi.fn();
    render(ConversationHistoryWarning, {
      onDeleteAllHistory: mockOnDeleteAllHistory,
      onDismiss: mockOnDismiss,
      onHideForSession: mockOnHideForSession,
    });

    const deleteAllHistoryButton = screen.getByTestId("delete-all-history-warning-button");
    await fireEvent.click(deleteAllHistoryButton);

    expect(mockOnDeleteAllHistory).toHaveBeenCalledTimes(1);
  });

  it("shows confirmation state when dismiss button is clicked", async () => {
    const mockOnDeleteAllHistory = vi.fn();
    const mockOnDismiss = vi.fn();
    const mockOnHideForSession = vi.fn();
    render(ConversationHistoryWarning, {
      onDeleteAllHistory: mockOnDeleteAllHistory,
      onDismiss: mockOnDismiss,
      onHideForSession: mockOnHideForSession,
    });

    // Initially should show the dismiss button
    expect(screen.getByTestId("dismiss-conversation-history-warning-button")).toBeInTheDocument();
    expect(screen.queryByText("Hide for this session?")).not.toBeInTheDocument();

    // Click dismiss button
    const dismissButton = screen.getByTestId("dismiss-conversation-history-warning-button");
    await fireEvent.click(dismissButton);

    // Should now show confirmation state
    expect(screen.getByText("Hide for this session?")).toBeInTheDocument();
    expect(
      screen.getByTestId("hide-conversation-history-warning-for-session-button"),
    ).toBeInTheDocument();
    expect(
      screen.getByTestId("cancel-conversation-history-confirmation-button"),
    ).toBeInTheDocument();
    expect(
      screen.queryByTestId("dismiss-conversation-history-warning-button"),
    ).not.toBeInTheDocument();
  });

  it("calls onHideForSession when checkmark button is clicked", async () => {
    const mockOnDeleteAllHistory = vi.fn();
    const mockOnDismiss = vi.fn();
    const mockOnHideForSession = vi.fn();
    render(ConversationHistoryWarning, {
      onDeleteAllHistory: mockOnDeleteAllHistory,
      onDismiss: mockOnDismiss,
      onHideForSession: mockOnHideForSession,
    });

    // Click dismiss to enter confirmation state
    const dismissButton = screen.getByTestId("dismiss-conversation-history-warning-button");
    await fireEvent.click(dismissButton);

    // Click the checkmark button
    const hideForSessionButton = screen.getByTestId(
      "hide-conversation-history-warning-for-session-button",
    );
    await fireEvent.click(hideForSessionButton);

    expect(mockOnHideForSession).toHaveBeenCalledTimes(1);
  });

  it("calls onDismiss when cancel button is clicked in confirmation state", async () => {
    const mockOnDeleteAllHistory = vi.fn();
    const mockOnDismiss = vi.fn();
    const mockOnHideForSession = vi.fn();
    render(ConversationHistoryWarning, {
      onDeleteAllHistory: mockOnDeleteAllHistory,
      onDismiss: mockOnDismiss,
      onHideForSession: mockOnHideForSession,
    });

    // Click dismiss to enter confirmation state
    const dismissButton = screen.getByTestId("dismiss-conversation-history-warning-button");
    await fireEvent.click(dismissButton);

    // Click the cancel button
    const cancelButton = screen.getByTestId("cancel-conversation-history-confirmation-button");
    await fireEvent.click(cancelButton);

    expect(mockOnDismiss).toHaveBeenCalledTimes(1);
  });

  it("shows delete all history button with correct tooltip", () => {
    const mockOnDeleteAllHistory = vi.fn();
    const mockOnDismiss = vi.fn();
    const mockOnHideForSession = vi.fn();
    render(ConversationHistoryWarning, {
      onDeleteAllHistory: mockOnDeleteAllHistory,
      onDismiss: mockOnDismiss,
      onHideForSession: mockOnHideForSession,
    });

    const deleteButton = screen.getByTestId("delete-all-history-warning-button");
    expect(deleteButton).toBeInTheDocument();

    // The tooltip content is rendered by TextTooltipAugment, so we check for the button
    expect(deleteButton).toHaveAttribute("data-testid", "delete-all-history-warning-button");
  });

  it("has the correct initial button state", () => {
    const mockOnDeleteAllHistory = vi.fn();
    const mockOnDismiss = vi.fn();
    const mockHideForSession = vi.fn();
    render(ConversationHistoryWarning, {
      onDeleteAllHistory: mockOnDeleteAllHistory,
      onDismiss: mockOnDismiss,
      onHideForSession: mockHideForSession,
    });

    // Should show initial buttons
    expect(screen.getByTestId("delete-all-history-warning-button")).toBeInTheDocument();
    expect(screen.getByTestId("dismiss-conversation-history-warning-button")).toBeInTheDocument();

    // Should not show confirmation buttons
    expect(
      screen.queryByTestId("never-show-conversation-history-warning-again-button"),
    ).not.toBeInTheDocument();
    expect(
      screen.queryByTestId("cancel-conversation-history-confirmation-button"),
    ).not.toBeInTheDocument();
  });
});
