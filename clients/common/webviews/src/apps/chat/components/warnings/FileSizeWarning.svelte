<script lang="ts">
  import CalloutAugment from "$common-webviews/src/design-system/components/CalloutAugment.svelte";
  import ExclamationTriangleIcon from "$common-webviews/src/design-system/icons/exclamation-triangle.svelte";
  import { calculateFileWarnings } from "../../utils/file-utils";
  import type { JSONContent } from "@tiptap/core";

  export let richTextContent: JSONContent | null | undefined;

  $: fileWarnings = calculateFileWarnings(richTextContent);
  $: ({ hasWarning, warningMessages } = fileWarnings);
</script>

{#if hasWarning}
  <div class="c-file-warning">
    <CalloutAugment variant="soft" color="warning" size={1}>
      <ExclamationTriangleIcon slot="icon" />
      <div class="c-file-warning__message">
        {#each warningMessages as message}
          <div class="c-file-warning__line">{message}</div>
        {/each}
      </div>
    </CalloutAugment>
  </div>
{/if}

<style>
  .c-file-warning {
    position: relative;
    width: 100%;
  }

  .c-file-warning__message {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
  }

  .c-file-warning__line {
    line-height: 1.4;
  }
</style>
