<script lang="ts">
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import { TooltipTriggerOn } from "$common-webviews/src/design-system/_primitives/TooltipAugment/types";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import XMark from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/xmark.svg?component";
  import Check from "$common-webviews/src/design-system/icons/check.svelte";
  import ChatInputBanner from "./ChatInputBanner.svelte";

  export let onDismiss: () => void;
  export let onHideForSession: () => void;
  export let minVersion: string;

  // State for the two-step dismiss process
  let showConfirmation = false;

  function handleDismissClick() {
    showConfirmation = true;
  }

  function handleHideForSession() {
    onHideForSession();
  }

  function handleCancelConfirmation() {
    onDismiss();
  }
</script>

<ChatInputBanner text="VS Code {minVersion}+ required. Some features may not work." color="warning">
  <div class="l-vscode-version-warning__buttons" slot="right-content">
    {#if !showConfirmation}
      <!-- Initial state: Dismiss button -->
      <TextTooltipAugment content="Dismiss" triggerOn={[TooltipTriggerOn.Hover]}>
        <IconButtonAugment
          size={1}
          variant="ghost-block"
          color="neutral"
          on:click={handleDismissClick}
          data-testid="dismiss-vscode-version-warning-button"
        >
          <XMark />
        </IconButtonAugment>
      </TextTooltipAugment>
    {:else}
      <!-- Confirmation state: Hide for this session text and buttons -->
      <span class="l-vscode-version-warning__confirmation-text">Hide for this session?</span>
      <div class="l-vscode-version-warning__confirmation-buttons">
        <TextTooltipAugment
          content="Yes, hide for this session"
          triggerOn={[TooltipTriggerOn.Hover]}
        >
          <IconButtonAugment
            size={1}
            variant="ghost-block"
            color="neutral"
            on:click={handleHideForSession}
            data-testid="hide-vscode-version-warning-for-session-button"
          >
            <Check />
          </IconButtonAugment>
        </TextTooltipAugment>
        <TextTooltipAugment content="No, dismiss once" triggerOn={[TooltipTriggerOn.Hover]}>
          <IconButtonAugment
            size={1}
            variant="ghost-block"
            color="neutral"
            on:click={handleCancelConfirmation}
            data-testid="cancel-vscode-version-confirmation-button"
          >
            <XMark />
          </IconButtonAugment>
        </TextTooltipAugment>
      </div>
    {/if}
  </div>
</ChatInputBanner>

<style>
  .l-vscode-version-warning__buttons {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: var(--ds-spacing-1);
  }

  .l-vscode-version-warning__confirmation-text {
    font-size: var(--ds-font-size-1);
    color: var(--ds-color-neutral-11);
    margin-right: var(--ds-spacing-2);
    white-space: nowrap;
  }

  .l-vscode-version-warning__confirmation-buttons {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: var(--ds-spacing-1);
  }

  .l-vscode-version-warning__buttons :global(svg),
  .l-vscode-version-warning__confirmation-buttons :global(svg) {
    width: 14px;
    height: 14px;
    fill: var(--icon-color, currentColor);
  }
</style>
