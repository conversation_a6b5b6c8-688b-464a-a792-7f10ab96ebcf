<script lang="ts">
  import { ChatInputWarningBannerContext } from "../../utils/chat-input-warning-banner-context";
  import ConversationHistoryWarning from "./ConversationHistoryWarning.svelte";
  import SubscriptionWarning from "./SubscriptionWarning.svelte";
  import VscodeVersionWarning from "./VscodeVersionWarning.svelte";
  import { getContext } from "svelte";
  import { SubscriptionModel } from "../../models/subscription-model";
  import { getChatModel } from "../../chat-context";
  import { setHideConversationHistoryWarningForSession } from "../../utils/conversation-history-warning-preferences";
  import { setHideVscodeVersionWarningForSession } from "../../utils/vscode-version-warning-preferences";
  import { RemoteAgentsModel } from "$common-webviews/src/apps/remote-agent-manager/models/remote-agents-model";
  import RemoteAgentDeletionWarning from "./RemoteAgentDeletionWarning.svelte";
  import NotificationBanner from "./NotificationBanner.svelte";
  import { notificationBannerStore } from "../../models/notification-banner-store";

  const notifications = notificationBannerStore.notifications;

  const chatModel = getChatModel();
  const flagsModel = chatModel.flags;
  const subscriptionModel = getContext<SubscriptionModel>(SubscriptionModel.key);
  const subscriptionInfo = subscriptionModel.info;
  const remoteAgentsModel = getContext<RemoteAgentsModel>(RemoteAgentsModel.key);

  const chatInputWarningBannerContext = new ChatInputWarningBannerContext(
    chatModel,
    subscriptionModel,
    remoteAgentsModel,
  );
  const {
    showSubscriptionWarning,
    showVSCodeVersionWarning,
    showConversationHistoryWarning,
    showRemoteAgentDeletionWarning,
  } = chatInputWarningBannerContext;

  function handleConversationHistoryWarningDismiss() {
    chatInputWarningBannerContext.dismissConversationHistoryWarning();
  }

  function handleConversationHistoryWarningHideForSession() {
    setHideConversationHistoryWarningForSession();
    chatInputWarningBannerContext.dismissConversationHistoryWarning();
  }

  function handleVscodeVersionWarningDismiss() {
    chatInputWarningBannerContext.dismissVSCodeVersionWarning();
  }

  function handleVscodeVersionWarningHideForSession() {
    setHideVscodeVersionWarningForSession();
  }

  async function handleDeleteAllHistory() {
    // Get all conversation IDs
    const allConversationIds = Object.keys(chatModel.conversations);

    if (allConversationIds.length > 0) {
      // Delete all conversations using the chat model's method
      await chatModel.deleteConversations(allConversationIds);
    }
  }
</script>

{#if $showSubscriptionWarning && $subscriptionInfo}
  <SubscriptionWarning
    daysRemaining={$subscriptionInfo.daysRemaining ?? null}
    usageBalanceDepleted={$subscriptionInfo.usageBalanceDepleted || false}
    isInactive={$subscriptionInfo.type === "inactive_subscription"}
    onDismiss={() => subscriptionModel.dismiss()}
  />
{:else if $notifications.length > 0}
  <!-- Show only the most recent notification (last in array) -->
  {@const notification = $notifications[$notifications.length - 1]}
  <NotificationBanner
    notificationId={notification.notificationId}
    message={notification.message}
    level={notification.level}
    actionItems={notification.actionItems || []}
  />
{:else if $showVSCodeVersionWarning}
  <VscodeVersionWarning
    onDismiss={handleVscodeVersionWarningDismiss}
    onHideForSession={handleVscodeVersionWarningHideForSession}
    minVersion={$flagsModel.vscodeMinVersion}
  />
{:else if $showConversationHistoryWarning}
  <ConversationHistoryWarning
    onDeleteAllHistory={handleDeleteAllHistory}
    onDismiss={handleConversationHistoryWarningDismiss}
    onHideForSession={handleConversationHistoryWarningHideForSession}
  />
{:else if $showRemoteAgentDeletionWarning}
  <RemoteAgentDeletionWarning />
{/if}
