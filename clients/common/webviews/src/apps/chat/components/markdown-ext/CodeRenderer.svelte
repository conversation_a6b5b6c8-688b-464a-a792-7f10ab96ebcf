<script lang="ts">
  import type { Tokens } from "marked";
  import { getContext } from "svelte";
  import { type ChatModel } from "../../models/chat-model";
  import Codeblock from "./codeblock/Codeblock.svelte";
  import MermaidBlock from "./MermaidBlock.svelte";
  import { getFirstLine, parseCodeblockMetadata } from "./utils";

  export let token: Tokens.Code;

  const chatModel: ChatModel | undefined = getContext("chatModel");

  $: firstLine = getFirstLine(token.raw);
  $: codeblockMetadata = firstLine ? parseCodeblockMetadata(firstLine) : undefined;

  $: language = codeblockMetadata?.language ?? token.lang;

  $: isMermaidEnabled =
    chatModel?.flags.enableChatMermaidDiagrams ||
    chatModel?.flags.enableChatMermaidDiagramsMinVersion;
</script>

{#if language === "mermaid" && isMermaidEnabled}
  <MermaidBlock {token} {codeblockMetadata} />
{:else}
  <Codeblock {token} {codeblockMetadata} />
{/if}
