<script lang="ts">
  import { getContext } from "svelte";
  import type { Tokens } from "marked";

  import { onKey } from "$common-webviews/src/common/utils/keypress";
  import type { ChatModel } from "../../models/chat-model";
  import {
    WebViewMessageType,
    type FileDetails,
  } from "$vscode/src/webview-providers/webview-messages";
  import { host } from "$common-webviews/src/common/hosts/host";

  export let token: Tokens.Link;

  const chatModel: ChatModel | undefined = getContext("chatModel");
  if (!chatModel) {
    console.warn("ChatModel not found in context -- should you be using this component?");
  }

  const onOpenLocalFile = (details: FileDetails) => {
    chatModel?.extensionClient.openFile(details);
  };

  const onAugmentLink = (linkRef: string) => {
    host.postMessage({
      type: WebViewMessageType.augmentLink,
      data: linkRef,
    });
  };

  const onCommandLink = (commandId: string) => {
    host.postMessage({
      type: WebViewMessageType.executeCommand,
      data: commandId,
    });
  };

  let displayText = token.text.replace(/`/g, "");
</script>

{#if /^https?:\/\//.test(token.href)}
  <a class="markdown-active-link" href={token.href} title={token.title}>
    <span class="markdown-base-link">
      <slot>{displayText}</slot>
    </span>
  </a>
{:else if /^augment:\/\//.test(token.href)}
  <span
    class="markdown-active-link"
    on:click={() => onAugmentLink(token.href)}
    on:keydown={onKey("Enter", () => onAugmentLink(token.href))}
    on:cut={() => navigator.clipboard.writeText(displayText)}
    on:paste={(e) => e.preventDefault()}
    role="button"
    tabindex="0"
  >
    <span class="markdown-base-link">
      <slot>{displayText}</slot>
    </span>
  </span>
{:else if /^command:/.test(token.href)}
  <span
    class="markdown-active-link"
    on:click={() => onCommandLink(token.href.substring(8))}
    on:keydown={onKey("Enter", () => onCommandLink(token.href.substring(8)))}
    on:cut={() => navigator.clipboard.writeText(displayText)}
    on:paste={(e) => e.preventDefault()}
    role="button"
    tabindex="0"
  >
    <span class="markdown-base-link">
      <slot>{displayText}</slot>
    </span>
  </span>
{:else}
  {#await chatModel?.extensionClient.resolvePath?.({ rootPath: "", relPath: token.href })}
    <span class="markdown-base-link">
      <slot>{displayText}</slot>
    </span>
  {:then maybeFileDetails}
    {#if maybeFileDetails}
      <span
        class="markdown-active-link"
        on:click={() => onOpenLocalFile(maybeFileDetails)}
        on:keydown={onKey("Enter", () => onOpenLocalFile(maybeFileDetails))}
        on:cut={() => navigator.clipboard.writeText(displayText)}
        on:paste={(e) => e.preventDefault()}
        role="button"
        tabindex="0"
      >
        <span class="markdown-base-link">
          <slot>{displayText}</slot>
        </span>
      </span>
    {:else}
      <span class="markdown-base-link">
        <slot>{displayText}</slot>
      </span>
    {/if}
  {:catch}
    <span class="markdown-base-link">
      <slot>{displayText}</slot>
    </span>
  {/await}
{/if}

<style>
  .markdown-active-link {
    cursor: pointer;
    text-decoration: underline;
    user-select: all;
    text-decoration-color: var(--ds-color-accent-a11);
  }

  .markdown-base-link {
    color: var(--ds-color-accent-a11);

    /* Fixes line wrapping */
    word-break: break-all;
    display: inline;
  }
</style>
