import { UserThemeCategory } from "$common-webviews/src/common/hosts/user-themes/augment-theme-attributes";
import debounce from "lodash.debounce";
import mermaid, { type MermaidConfig } from "mermaid";
import { type Action } from "svelte/action";
import panzoom from "svg-pan-zoom";

type MermaidTheme = MermaidConfig["theme"];

const THEMES: Map<UserThemeCategory, MermaidTheme> = new Map([
  [UserThemeCategory.light, "default"],
  [UserThemeCategory.dark, "dark"],
]);

export const MERMAID_CONTAINER_HEIGHT = 500;

export interface IMermaidDiagramOptions {
  /** The Mermaid code to render */
  text: string;
  /** Returns true if the diagram can be rendered */
  canRender: () => boolean;
  /** Set the error state of the diagram */
  setError: (hasError: boolean) => void;
  /** Set the top position of the diagram container */
  setContainerPosition: (distance: number) => void;
  /** Set the pan zoom controller */
  setPanZoomController: (controller: typeof panzoom) => void;
  /** Set the SVG */
  setSvg: (value: string) => void;
  /** The user's current theme category */
  themeCategory?: UserThemeCategory;
}

/**
 * @param matrix - assumes matrix is an affine transformation matrix
 *
 * @returns true if the matrix is invertible
 */
function isInvertible(matrix: SVGMatrix) {
  const determinant = matrix.a * matrix.d - matrix.c * matrix.b;
  return determinant !== 0;
}

export const mermaidDiagram: Action<HTMLDivElement, IMermaidDiagramOptions> = (
  element: HTMLDivElement,
  options: IMermaidDiagramOptions,
) => {
  // CSS selectors cannot query IDs that begin with a number, so we append
  // a word to the ID
  const diagramId = `mermaid-${crypto.randomUUID()}`;

  let panZoomController: typeof panzoom | undefined;
  let matrixNotInvertible = false;

  /**
   * Resets the Pan Zoom Controller by destroying the existing controller and
   * setting it to undefined
   *
   * Note: If the controller's transformation matrix is not invertible, the
   * controller will not be destroyed. This may leak memory.
   */
  const resetPanZoomController = () => {
    try {
      panZoomController?.destroy();
    } catch (error) {
      if (matrixNotInvertible) {
        // When the controller is destroyed, the controller resets the zoom. However,
        // if the matrix is not invertible, the destroy function will throw an error.
        // For this case, the controller will not be destroyed.
        console.warn("Pan controller matrix is not invertible, controller will not be destroyed");
      } else {
        console.error("Failed to destroy pan controller: ", error);
      }
    }
    panZoomController = undefined;
  };

  async function renderDiagram(value: string) {
    const mermaidTheme: MermaidTheme =
      (options.themeCategory && THEMES.get(options.themeCategory)) ?? "default";
    mermaid.initialize({ startOnLoad: false, theme: mermaidTheme });

    try {
      const { svg, bindFunctions } = await mermaid.render(diagramId, value, element);
      element.innerHTML = svg;
      options.setSvg(svg);
      bindFunctions?.(element);
      const diagramSvg = document.querySelector<SVGSVGElement>(`#${diagramId}`);
      if (diagramSvg === null) {
        throw new Error("Diagram element not found");
      }
      const codeblockBar = document.querySelector(".c-codeblock__top-bar-anchor");
      const barHeight = codeblockBar?.getBoundingClientRect().height || 0;
      options.setContainerPosition(barHeight);
      diagramSvg.style.height = `${MERMAID_CONTAINER_HEIGHT - barHeight}px`;
      diagramSvg.style.maxWidth = "100%";
      resetPanZoomController();
      panZoomController = panzoom(diagramSvg, {
        fit: true,
        center: true,
        mouseWheelZoomEnabled: false,
      });
      panZoomController.setOnUpdatedCTM((newCTM) => {
        // If the user quickly collapses and expands the Augment panel, the
        // state of the controller may enter a degenerate state where the matrix
        // is not invertible.
        if (!isInvertible(newCTM)) {
          matrixNotInvertible = true;
          console.warn("Pan controller matrix is not invertible");
        }
      });
      options.setPanZoomController(panZoomController);
    } catch (err) {
      // It is possible for the model to return broken Mermaid code.
      // Mermaid will render an ugly error so need to hide it.
      console.error("Failed to parse mermaid diagram: ", err);
      options.setError(true);
    }
  }

  // Rerender the SVG whenever the chat panel resizes
  let lastWidth = 0;
  // Mermaid takes a couple milliseconds to render. During this time, events
  // may trigger more renders so we need to debounce the rendering.
  const debounceRender = debounce((value: string) => {
    renderDiagram(value);
  }, 150);

  const resizeObserver = new ResizeObserver((event) => {
    const width = event[0].contentRect.width;
    if (width !== lastWidth && options.text !== "" && options.canRender()) {
      lastWidth = width;
      debounceRender(options.text);
    }
  });
  resizeObserver.observe(document.documentElement);

  return {
    update: (newOptions: IMermaidDiagramOptions) => {
      if (
        (newOptions.text !== "" && options.text !== newOptions.text) ||
        newOptions.themeCategory !== options.themeCategory
      ) {
        options = { ...options, ...newOptions };
        debounceRender(options.text);
      }
    },
    destroy: () => {
      element.innerHTML = "";
      resizeObserver.disconnect();
      resetPanZoomController();
    },
  };
};
