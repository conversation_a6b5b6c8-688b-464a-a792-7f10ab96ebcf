import { render, screen } from "@testing-library/svelte";
import { expect, describe, test, vi, type MockInstance, beforeEach, afterEach } from "vitest";

import Link from "./Link.svelte";
import userEvent from "@testing-library/user-event";
import { WebViewMessageType } from "$vscode/src/webview-providers/webview-messages";
import Markdown from "@magidoc/plugin-svelte-marked";
import { host } from "$common-webviews/src/common/hosts/__mocks__/host";

describe("Link.svelte", async () => {
  let consoleWarningSpy: MockInstance;

  beforeEach(() => {
    vi.clearAllMocks();
    consoleWarningSpy = vi.spyOn(console, "warn").mockImplementation(vi.fn());
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  test("should render http link", async () => {
    render(Link, {
      props: {
        token: {
          type: "link",
          raw: "",
          href: "https://augmentcode.com",
          text: "augmentcode.com",
          title: "Link to augmentcode home page",
          tokens: [],
        },
      },
    });
    const link = screen.getByRole("link");
    expect(link).toBeInTheDocument();
    expect(link.getAttribute("href")).toEqual("https://augmentcode.com");
    expect(link.textContent).toEqual("augmentcode.com");
    expect(link.title).toEqual("Link to augmentcode home page");
  });

  test("should render augment link", async () => {
    host.postMessage.mockImplementation((msg) => {
      switch (msg.type) {
        case WebViewMessageType.augmentLink: {
          break;
        }
        default: {
          throw new Error(`Unexpected message type: ${msg.type}`);
        }
      }
    });

    const user = userEvent.setup();
    render(Link, {
      props: {
        token: {
          type: "link",
          raw: "",
          href: "augment://test",
          text: "example text",
          title: "example title",
          tokens: [],
        },
      },
    });
    const link = screen.getByRole("button");
    expect(link).toBeInTheDocument();
    expect(link.textContent).toEqual("example text");

    await user.click(link);
    expect(host.postMessage).toBeCalledTimes(1);
    expect(host.postMessage.mock.calls[0][0]).toEqual({
      type: WebViewMessageType.augmentLink,
      data: "augment://test",
    });
  });

  test("should handle error due to no chat model when showing a file path", async () => {
    const user = userEvent.setup();
    render(Link, {
      props: {
        token: {
          type: "link",
          raw: "",
          href: "./example/file.ts",
          text: "file.ts",
          title: "typescript file",
          tokens: [],
        },
      },
    });
    const link = screen.getByText("file.ts");
    expect(link).toBeInTheDocument();
    expect(link.textContent).toEqual("file.ts");
    expect(link.hasAttribute("href")).toEqual(false);
    expect(link.hasAttribute("title")).toEqual(false);
    expect(consoleWarningSpy).toBeCalledTimes(1);

    // Clicking shouldn't do anything
    await user.click(link);
    expect(host.postMessage).toBeCalledTimes(0);
  });

  test("should handle bold text in link text", async () => {
    render(Markdown, {
      props: {
        source: "[**Chat**](https://docs.augmentcode.com/using-augment/chat)",
        renderers: {
          link: Link,
        },
      },
    });
    const link = screen.getByRole("link");
    expect(link).toBeInTheDocument();
    expect(link.getAttribute("href")).toEqual("https://docs.augmentcode.com/using-augment/chat");

    // should contain span with bold text
    expect(link.querySelector("span > strong")).toBeInTheDocument();
    expect(link.querySelector("span > strong")?.textContent).toEqual("Chat");
  });
});
