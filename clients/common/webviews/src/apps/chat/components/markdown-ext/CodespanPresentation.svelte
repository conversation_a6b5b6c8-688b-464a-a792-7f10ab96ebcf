<script lang="ts">
  import Codespan from "$common-webviews/src/common/components/markdown/Codespan.svelte";
  import type { Tokens } from "marked";

  export let token: Tokens.Codespan;
  export let linkable: boolean;
  export let element: HTMLSpanElement | undefined = undefined;
</script>

<div class="c-chat-codespan" class:c-chat-codespan--linkable={linkable}>
  <Codespan {token} bind:element>
    <span
      class="c-chat-codespan__content"
      on:click
      on:keydown
      on:cut
      on:paste
      role="button"
      tabindex="0"
    >
      <slot name="icon" />
      <slot />
    </span>
  </Codespan>
</div>

<style>
  /* We don't want this element to affect the baseline of text.
      It's used to set make the codespan appear clickable */
  .c-chat-codespan {
    display: contents;
  }

  .c-chat-codespan--linkable :global(.markdown-codespan) {
    cursor: pointer;
    user-select: all;
  }

  .c-chat-codespan--linkable :global(.markdown-codespan:hover) {
    --codepsan-border-color: var(--ds-color-neutral-a8);
    --codepsan-bg-color: var(--ds-color-neutral-4);
    --codespan-fg-color: var(--ds-color-accent-a11);
  }

  .c-chat-codespan__content {
    position: relative;
  }

  .c-chat-codespan__content :global(svg) {
    width: var(--ds-spacing-3);
    height: var(--ds-spacing-3);

    color: var(--ds-color-neutral-a11);

    position: relative;
    top: 2px;
  }
</style>
