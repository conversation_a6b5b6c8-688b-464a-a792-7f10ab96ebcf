import { render, waitFor } from "@testing-library/svelte";
import { afterEach, beforeEach, describe, expect, type MockInstance, test, vi } from "vitest";

import { host } from "$common-webviews/src/common/hosts/__mocks__/host";
import CodespanTest from "./Codespan.test.svelte";
import type Codespan from "./Codespan.svelte";
import { ChatModel } from "../../models/chat-model";
import { SpecialContextInputModel } from "../../models/context-model";
import { AsyncMsgSender } from "$common-webviews/src/common/hosts/async-messaging";
import { type ComponentProps } from "svelte";
import {
  type FileDetails,
  WebViewMessageType,
} from "$vscode/src/webview-providers/webview-messages";
import * as trackOnSeen from "$common-webviews/src/apps/chat/components/actions/trackOnScreen";
import {
  ExchangeStatus,
  type ExchangeWithStatus,
} from "$common-webviews/src/apps/chat/types/chat-message";
import { type VisibilityObserverOptions } from "$common-webviews/src/apps/chat/components/actions/trackOnScreen";
import { FileType } from "$vscode/src/utils/types";

describe("Codespan.svelte", () => {
  let testKit: CodespanTestKit;

  beforeEach(async () => {
    testKit = new CodespanTestKit();
  });

  afterEach(() => {
    testKit.reset();
    vi.clearAllMocks();
  });

  test("renders non path code", async () => {
    const component = testKit.createComponent({
      token: {
        type: "codespan",
        raw: "`example code`",
        text: "example code",
      },
    });
    const codespan = component.container;
    expect(component.getByText("example code")).toBeInTheDocument();
    expect(codespan.querySelector(".c-chat-codespan--linkable")).toBeFalsy();
    expect(codespan.querySelectorAll("svg").length).toBe(0);
  });

  test("renders file path without file type", async () => {
    testKit.mockResolvePathMsg({
      repoRoot: "/home/<USER>/example-repo-root",
      pathName: "example/dir/file.txt",
    });
    const component = testKit.createComponent({
      token: {
        type: "codespan",
        raw: "`example/dir/file.txt`",
        text: "example/dir/file.txt",
      },
    });
    await testKit.emulateScrollIntoView();
    await waitForLinkable(component);

    expect(component.getByText("example/dir/file.txt")).toBeInTheDocument();
    const codespan = component.container;
    expect(codespan.querySelector(".c-chat-codespan--linkable")).toBeTruthy();
    expect(codespan.querySelectorAll("svg").length).toBe(0);
    expect(host.postMessage).toHaveBeenCalled();
  });

  test("renders file path with file type", async () => {
    testKit.mockResolvePathMsg({
      repoRoot: "/home/<USER>/example-repo-root",
      pathName: "example/dir/file.txt",
      fileType: FileType.file,
    });
    const component = testKit.createComponent({
      token: {
        type: "codespan",
        raw: "`example/dir/file.txt`",
        text: "example/dir/file.txt",
      },
    });
    await testKit.emulateScrollIntoView();
    await waitForLinkable(component);

    expect(component.getByText("example/dir/file.txt")).toBeInTheDocument();
    const codespan = component.container;
    expect(codespan.querySelector(".c-chat-codespan--linkable")).toBeTruthy();
    expect(codespan.querySelectorAll("svg").length).toBe(1);
    expect(host.postMessage).toHaveBeenCalled();
  });

  test("renders file path with directory type", async () => {
    testKit.mockResolvePathMsg({
      repoRoot: "/home/<USER>/example-repo-root",
      pathName: "example/dir",
      fileType: FileType.directory,
    });
    const component = testKit.createComponent({
      token: {
        type: "codespan",
        raw: "`example/dir`",
        text: "example/dir",
      },
    });
    await testKit.emulateScrollIntoView();
    await waitForSVG(component);

    expect(component.getByText("example/dir")).toBeInTheDocument();
    const codespan = component.container;
    expect(codespan.querySelector(".c-chat-codespan--linkable")).toBeFalsy();
    expect(codespan.querySelectorAll("svg").length).toBe(1);
    expect(host.postMessage).toHaveBeenCalled();
  });
});

async function waitForLinkable(component: ReturnType<typeof render<CodespanTest>>): Promise<void> {
  await waitFor(() => {
    const container = component.container;
    expect(container).toBeInTheDocument();
    expect(container.querySelector(".c-chat-codespan--linkable")).toBeInTheDocument();
  });
}

async function waitForSVG(component: ReturnType<typeof render<CodespanTest>>): Promise<void> {
  await waitFor(() => {
    const container = component.container;
    expect(container).toBeInTheDocument();
    expect(container.querySelector("svg")).toBeTruthy();
  });
}

class CodespanTestKit {
  contextModel: SpecialContextInputModel;
  chatModel: ChatModel;
  uuidCounter: number;
  visibilityObserverSpy: MockInstance<
    (node: HTMLElement, options: VisibilityObserverOptions) => { destroy(): void }
  >;

  private components: ReturnType<typeof render<CodespanTest>>[] = [];

  constructor() {
    this.contextModel = new SpecialContextInputModel();
    this.uuidCounter = 0;

    this.visibilityObserverSpy = vi.spyOn(trackOnSeen, "visibilityObserver");

    vi.spyOn(crypto, "randomUUID").mockImplementation(() => {
      return this.mockUUID(this.uuidCounter++);
    });

    this.chatModel = new ChatModel(new AsyncMsgSender(host.postMessage), host, this.contextModel, {
      initialFlags: { fullFeatured: true, enableEditableHistory: true },
    });
    vi.spyOn(this.chatModel.currentConversationModel, "exchangeWithRequestId").mockImplementation(
      (): ExchangeWithStatus | null => {
        return {
          status: ExchangeStatus.success,
          // eslint-disable-next-line @typescript-eslint/naming-convention
          request_message: "",
          // eslint-disable-next-line @typescript-eslint/naming-convention
          workspace_file_chunks: [
            {
              charStart: 0,
              charEnd: 10,
              blobName: "blob-name-123",
              file: {
                repoRoot: "/home/<USER>/example-repo-root",
                pathName: "example/dir/file.txt",
              },
            },
          ],
        };
      },
    );
  }

  mockUUID(id: number | string): `${string}-${string}-${string}-${string}-${string}` {
    return `mocked-uuid-a-b-${id}`;
  }

  reset() {
    this.uuidCounter = 0;
    for (const component of this.components) {
      component.unmount();
    }
    vi.clearAllMocks();
  }

  createComponent(props: ComponentProps<Codespan>): ReturnType<typeof render<CodespanTest>> {
    const component = render(CodespanTest, {
      props: {
        chatModel: this.chatModel,
        props,
      },
    });
    this.components.push(component);
    return component;
  }

  emulateScrollIntoView = async () => {
    await waitFor(() => {
      expect(this.visibilityObserverSpy.mock.calls.length).toBe(1);
    });
    const callArgs = this.visibilityObserverSpy.mock.calls[0];
    const options = callArgs[1];
    expect(options.onVisible).toBeDefined();
    if (options.onVisible) {
      options.onVisible();
    }
  };

  mockResolvePathMsg(data: FileDetails) {
    host.postMessage.mockImplementation((msg) => {
      switch (msg.type) {
        case WebViewMessageType.asyncWrapper: {
          switch (msg.baseMsg?.type) {
            case WebViewMessageType.resolveFileRequest: {
              expect(msg.baseMsg.data.relPath).toEqual(data.pathName);
              window.dispatchEvent(
                new MessageEvent("message", {
                  data: {
                    type: WebViewMessageType.asyncWrapper,
                    requestId: msg.requestId,
                    error: null,
                    baseMsg: {
                      type: WebViewMessageType.resolveFileResponse,
                      data,
                    },
                    streamCtx: undefined,
                  },
                }),
              );
              break;
            }
          }
          break;
        }
        default: {
          throw new Error(`Unexpected message type: ${msg.type}`);
        }
      }
    });
  }
}
