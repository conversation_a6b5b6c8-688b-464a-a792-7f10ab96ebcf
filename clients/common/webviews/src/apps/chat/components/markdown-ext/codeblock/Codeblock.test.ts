import { AsyncMsgSender } from "$common-webviews/src/common/hosts/async-messaging";
import { render } from "@testing-library/svelte";
import { beforeEach, describe, expect, test, vi } from "vitest";
import { ChatModel } from "../../../models/chat-model";
import { SpecialContextInputModel } from "../../../models/context-model";
import { ExchangeStatus } from "../../../types/chat-message";
import Codeblock from "./Codeblock.test.svelte";
import { HostClientType } from "$common-webviews/src/common/hosts/host-types";

describe("Codeblock loading behavior", () => {
  let testKit: CodeblockTestKit;

  beforeEach(async () => {
    testKit = new CodeblockTestKit();
  });

  test("loading state", async () => {
    testKit.setStatus(ExchangeStatus.sent);
    const component = render(Codeblock, {
      props: {
        codeblockCount: 1,
        chatModel: testKit.chatModel,
      },
    });
    expect(component.container.querySelector(`.c-codeblock__loading`)).toBeInTheDocument();
  });

  test("not loading state", async () => {
    testKit.setStatus(ExchangeStatus.success);
    const component = render(Codeblock, {
      props: {
        codeblockCount: 1,
        chatModel: testKit.chatModel,
      },
    });
    expect(component.container.querySelector(`.c-codeblock__loading`)).not.toBeInTheDocument();
  });
});

class CodeblockTestKit {
  chatModel: ChatModel;

  constructor() {
    this.chatModel = new ChatModel(
      new AsyncMsgSender(vi.fn()),
      {
        clientType: HostClientType.vscode,
        postMessage: vi.fn(),
        getState: vi.fn(),
        setState: vi.fn(),
      },
      new SpecialContextInputModel(),
    );
    this.setStatus(ExchangeStatus.sent);
  }

  setStatus(status: ExchangeStatus) {
    this.chatModel.currentConversationModel.exchangeWithRequestId = () => {
      return {
        status,
        /* eslint-disable @typescript-eslint/naming-convention */
        request_id: "test-id",
        request_message: "",
        /* eslint-enable @typescript-eslint/naming-convention */
      };
    };
  }
}
