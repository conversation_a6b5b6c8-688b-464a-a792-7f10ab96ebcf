<script lang="ts">
  import { type Tokens } from "marked";
  import { type ChatModel } from "../../../models/chat-model";
  import Codeblock from "./Codeblock.svelte";
  import { setChatModel } from "../../../chat-context";

  export let codeblockCount: number = 1;
  export let chatModel: ChatModel | undefined = undefined;

  if (chatModel) {
    setChatModel(chatModel);
  }

  const TOKEN = {
    text: `export function foobar() {
    console.log("hello world");
}`,
    lang: "typescript",
    type: "code",
    raw: '```typescript\nexport function foobar() {\n    console.log("hello world");\n}\n```',
  } as Tokens.Code;
  const METADATA = {
    language: "typescript",
    relPath: "clients/common/webviews/src/apps/chat/components/markdown-ext/foobar.ts",
    mode: "EDIT",
  };
</script>

{#each { length: codeblockCount } as _, i}
  <Codeblock token={TOKEN} codeblockMetadata={METADATA}>
    <span class={`codeblock-${i}`}>codeblock contents</span>
  </Codeblock>
{/each}
