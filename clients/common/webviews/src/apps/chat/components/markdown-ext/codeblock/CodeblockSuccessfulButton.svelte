<script lang="ts">
  import CopyButton from "$common-webviews/src/common/components/CopyButton.svelte";
  import CodeInsertButton from "$common-webviews/src/common/components/CodeInsertButton.svelte";
  import OpenFileButton from "../../conversation/blocks/tools/components/OpenFileButton.svelte";
  import type { ICodeblockActionButton } from "../utils";
  import { CodeblockActionType } from "./types";

  export let primaryButton: ICodeblockActionButton;

  export const onClickForSuccessButton = async () => {
    primaryButton.onClick();
    return "success" as const;
  };
</script>

<span class="c-collapsible-button-false">
  {#if primaryButton.codeblockActionType === CodeblockActionType.copy}
    <CopyButton stickyColor size={0.5} onCopy={onClickForSuccessButton}></CopyButton>
  {:else if primaryButton.codeblockActionType === CodeblockActionType.goTo}
    <OpenFileButton stickyColor size={0.5} onOpenLocalFile={onClickForSuccessButton} />
  {:else}
    <CodeInsertButton
      codeblockActionType={primaryButton.codeblockActionType}
      size={0.5}
      onClick={onClickForSuccessButton}
      successText={primaryButton.onSuccessMessage}
    />
  {/if}
</span>

<style>
  /** c-collapsible-button-false is set to prevent the icon from changing on hover */
  .c-collapsible-button-false {
    display: contents;
  }
</style>
