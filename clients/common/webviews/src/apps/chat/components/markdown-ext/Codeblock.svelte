<script lang="ts">
  import { getContext, onDestroy } from "svelte";
  import { writable, type Writable } from "svelte/store";
  import type { Tokens } from "marked";

  import Codeblock from "$common-webviews/src/common/components/markdown/Codeblock.svelte";
  import CodeblockActions from "$common-webviews/src/common/components/markdown/CodeblockActions.svelte";
  import CodeblockButton from "$common-webviews/src/common/components/markdown/CodeblockButton.svelte";
  import type { ChatModel } from "../../models/chat-model";
  import { isNotebook, type ICodeblockActionButton, type ICodeblockMetadata } from "./utils";
  import Filespan from "$common-webviews/src/common/components/Filespan.svelte";
  import { type FileDetails } from "$vscode/src/webview-providers/webview-messages";
  import { ExchangeStatus } from "../../types/chat-message";

  import CodeblockDropdownItem from "./CodeblockDropdownItem.svelte";
  import DropdownMenuAugment from "$common-webviews/src/design-system/components/DropdownMenuAugment";
  import DotsVertical from "$common-webviews/src/design-system/icons/dots-vertical.svelte";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import {
    createCopyToClipboardAction,
    createGoToFileAction,
    createInsertIntoNewFileAction,
  } from "./codeblock/codeblockActions";
  import { createSmartPasteAction, smartPasteObserver } from "./codeblock/smartPaste";
  import { CodeblockActionType } from "./codeblock/types";

  // Top-level state used as entry points/data across the rest of codeblock logic
  export let token: Tokens.Code;
  /**
   * List of action buttons that are rendered in the top right corner. These
   * buttons will be rendered before the default buttons of copy, create, and
   * apply.
   */
  export let actionButtons: ICodeblockActionButton[] | undefined = undefined;
  export let removeDefaultButtons: boolean = false;
  export let codeblockMetadata: ICodeblockMetadata | undefined;
  export let height: number | undefined = undefined;
  let codeblock: Codeblock | undefined = undefined;
  const chatModel: ChatModel | undefined = getContext("chatModel");
  const requestIdModel: Writable<string> = getContext("requestId");
  $: requestId = $requestIdModel;
  $: conversationModel = $chatModel?.currentConversationModel;

  if (!chatModel) {
    console.warn("ChatModel not found in context -- should you be using this component?");
  }

  // Used to check if the codeblock target path exists. Null means
  // a resolution was attempted, but failed.
  let resolvedTargetPath: FileDetails | undefined = undefined;
  let targetPathResolutionAttempted = false;
  async function resolveCodeblockTarget() {
    if (codeblockMetadata?.relPath) {
      resolvedTargetPath = await chatModel?.extensionClient.resolvePath({
        rootPath: "",
        relPath: codeblockMetadata?.relPath,
      });
      targetPathResolutionAttempted = true;
    }
  }
  $: codeblockMetadata?.relPath && resolveCodeblockTarget();

  $: showPropAsPrimary = actionButtons?.find((button) => button.primary);
  $: showCreateAsPrimary =
    !showPropAsPrimary && hasCodepath && targetPathResolutionAttempted && !resolvedTargetPath;
  $: showApplyAsPrimary = !showPropAsPrimary && canTriggerSmartPaste && !isFromNotebook;
  $: hasPrimaryAction = showCreateAsPrimary || showApplyAsPrimary || showPropAsPrimary;

  $: getCodeContents = () => codeblock?.getContents();

  // =====================================================
  // Actions: correspond to clickable buttons on codeblock
  // =====================================================
  $: copyToClipboardButton = createCopyToClipboardAction(getCodeContents, !hasPrimaryAction);

  // Insert code into a new file with the given path or with a generic name
  $: insertIntoNewFileButton = createInsertIntoNewFileAction(
    getCodeContents,
    codeblockMetadata,
    chatModel,
    showCreateAsPrimary,
  );

  // Goes to the file based on the state of the codeblock.
  $: showGoToButton = hasCodepath && resolvedTargetPath;
  $: goToFileButton = createGoToFileAction(token.text, isExcerpt, codeblockMetadata, chatModel);

  const getHasClickedSmartPaste = () => $hasClickedSmartPaste;
  // Smart paste (apply button)
  $: ({
    smartPaste,
    canTriggerSmartPaste,
    canPrecomputeSmartPaste,
    smartPasteButton,
    directApplyButton,
  } = createSmartPasteAction(
    chatModel,
    codeblockMetadata,
    token,
    requestId,
    isSuccessful,
    !!resolvedTargetPath,
    isVisibleAndStable,
    isHoveredStable,
    getHasClickedSmartPaste,
    (val: boolean) => ($hasClickedSmartPaste = val),
  ));

  const hasClickedSmartPaste = writable(false);
  $: if (canPrecomputeSmartPaste) {
    smartPaste({ dryRun: true });
  }

  // Compute boolean flags for whether or not different actions are available
  $: hasCodepath = !!codeblockMetadata?.relPath;
  $: isFromNotebook = isNotebook(codeblockMetadata?.relPath ?? "");
  $: hasMode = !!codeblockMetadata?.mode;
  $: isExcerpt = hasMode && codeblockMetadata?.mode === "EXCERPT";
  $: isSuccessful =
    $conversationModel?.exchangeWithRequestId(requestId)?.status === ExchangeStatus.success;

  // Create action buttons
  $: defaultActionButtons = [
    copyToClipboardButton,
    insertIntoNewFileButton,
    // Add direct apply button if available
    ...(directApplyButton ? [{ ...directApplyButton, primary: showApplyAsPrimary }] : []),
    // Always add the review changes button
    { ...smartPasteButton, primary: !directApplyButton && showApplyAsPrimary },
  ] as ICodeblockActionButton[];
  $: allButtons = [
    ...(actionButtons ?? []),
    ...(showGoToButton ? [goToFileButton] : []),
    ...(removeDefaultButtons ? [] : defaultActionButtons),
  ];
  $: [primaryButtons, secondaryButtons] = allButtons.reduce(
    ([primary, buttons], button) => {
      if (button.primary) {
        return [[...primary, button], buttons];
      }
      return [primary, [...buttons, button]];
    },
    [[], []] as [ICodeblockActionButton[], ICodeblockActionButton[]],
  );

  // Compute codeblock props
  $: padding = { top: 30, bottom: 8 };
  $: language = codeblockMetadata?.language ?? token.lang;

  // Add these lines for visibility tracking;
  let element: HTMLDivElement | undefined;
  let isVisibleAndStable = false;
  let isHoveredStable = false;

  let destroySmartPasteObservers: () => void;
  // Will only attach an observer if the element is already mounted, and the exchange is successful
  $: if (element && isSuccessful) {
    destroySmartPasteObservers = smartPasteObserver(
      element,
      (val: boolean) => (isVisibleAndStable = val),
      (val: boolean) => (isHoveredStable = val),
    );
  }
  onDestroy(() => {
    destroySmartPasteObservers?.();
  });

  let requestDropdownClose = () => {};
</script>

<Codeblock bind:this={codeblock} {token} {padding} {language} {height} bind:element>
  <CodeblockActions location="topBar" slot="topBarLeft">
    <div class="c-codeblock__buttons-container">
      <!-- If we have a filepath, render a button to go to the file -->
      {#if !!codeblockMetadata?.relPath}
        <CodeblockButton
          data={{
            codeblockActionType: CodeblockActionType.goTo,
            title: `Go to ${codeblockMetadata.relPath}`,
            onClick: goToFileButton.onClick,
            onSuccessMessage: "Opening file...",
          }}
        >
          <Filespan filepath={codeblockMetadata.relPath}>
            <span slot="leftIcon"></span>
          </Filespan>
        </CodeblockButton>
      {/if}
    </div>
  </CodeblockActions>
  <CodeblockActions location="topBar" slot="topBarRight" align="left">
    <div class="c-codeblock__buttons-container">
      {#each primaryButtons as button}
        <CodeblockButton data={button} />
      {/each}
      {#if secondaryButtons.length}
        <DropdownMenuAugment.Root nested={false} bind:requestClose={requestDropdownClose}>
          <DropdownMenuAugment.Trigger>
            <IconButtonAugment variant="ghost-block" color="neutral" size={1}>
              <DotsVertical />
            </IconButtonAugment>
          </DropdownMenuAugment.Trigger>
          <DropdownMenuAugment.Content side="bottom" align="end">
            {#each secondaryButtons as button}
              <CodeblockDropdownItem data={button} closeDropdown={requestDropdownClose} />
            {/each}
          </DropdownMenuAugment.Content>
        </DropdownMenuAugment.Root>
      {/if}
    </div>
  </CodeblockActions>
</Codeblock>

<style>
  .c-codeblock__buttons-container {
    text-wrap: nowrap;
    display: flex;
    flex-direction: row;

    /* Vertical center */
    align-items: center;
    vertical-align: middle;
  }
</style>
