<script lang="ts">
  import type { ICodeblockActionButton } from "$common-webviews/src/apps/chat/components/markdown-ext/utils";
  import MaterialIcon from "$common-webviews/src/common/components/MaterialIcon.svelte";
  import DropdownMenuAugment from "$common-webviews/src/design-system/components/DropdownMenuAugment";

  export let data: ICodeblockActionButton;
  export let closeDropdown: () => void;

  $: ({
    onClick,
    onSuccessMessage,
    iconName,
    buttonText,
    onMouseDown,
    onMouseLeave,
    onMouseOver,
    onBlur,
    onFocus,
  } = data);

  let displaySuccessText: string | undefined = undefined;
  let successTimer: ReturnType<typeof setTimeout> | undefined = undefined;

  function onClickWrapper() {
    // If timer is running, do nothing
    if (successTimer) {
      return;
    }

    onClick();
    displaySuccessText = onSuccessMessage;

    // Set a new timer
    successTimer = setTimeout(() => {
      displaySuccessText = undefined;
      successTimer = undefined;
      closeDropdown();
    }, 1500);
  }
</script>

<DropdownMenuAugment.Item
  onSelect={onClickWrapper}
  on:mouseover={(e) => onMouseOver?.(e.detail)}
  on:mouseleave={(e) => onMouseLeave?.(e.detail)}
  on:blur={(e) => onBlur?.(e.detail)}
  on:focus={(e) => onFocus?.(e.detail)}
  on:mousedown={(e) => onMouseDown?.(e.detail)}
>
  <MaterialIcon {iconName} slot="iconLeft" />
  {displaySuccessText ?? buttonText}
</DropdownMenuAugment.Item>
