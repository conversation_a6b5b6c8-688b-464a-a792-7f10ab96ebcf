import { describe, it, expect } from "vitest";
import { parseCodeblockMetadata } from "./utils";

describe("parseCodeblockMetadata", () => {
  describe("basic parsing", () => {
    it("should handle invalid input", () => {
      expect(parseCodeblockMetadata("This is not a codeblock")).toEqual({
        language: null,
        relPath: null,
        mode: null,
      });
    });

    it("should handle multiple backticks", () => {
      expect(parseCodeblockMetadata("`````typescript")).toEqual({
        language: "typescript",
        relPath: null,
        mode: null,
      });
    });
  });

  describe("language parsing", () => {
    it("should parse language only", () => {
      expect(parseCodeblockMetadata("```typescript")).toEqual({
        language: "typescript",
        relPath: null,
        mode: null,
      });
    });

    it("should handle missing language", () => {
      expect(parseCodeblockMetadata("```")).toEqual({
        language: null,
        relPath: null,
        mode: null,
      });
    });
  });

  describe("path parsing", () => {
    it("should parse simple path", () => {
      expect(parseCodeblockMetadata("``` path=src/file.ts")).toEqual({
        language: null,
        relPath: "src/file.ts",
        mode: null,
      });
    });

    describe("paths with spaces", () => {
      it("should parse path with spaces in directory name", () => {
        expect(parseCodeblockMetadata("``` path=src/my folder/file.ts")).toEqual({
          language: null,
          relPath: "src/my folder/file.ts",
          mode: null,
        });
      });

      it("should parse path with spaces in file name", () => {
        expect(parseCodeblockMetadata("``` path=src/my file.ts")).toEqual({
          language: null,
          relPath: "src/my file.ts",
          mode: null,
        });
      });

      it("should parse path with multiple spaces", () => {
        expect(parseCodeblockMetadata("``` path=src/my folder/my file.ts")).toEqual({
          language: null,
          relPath: "src/my folder/my file.ts",
          mode: null,
        });
      });

      it("should parse path with spaces in multiple directories", () => {
        expect(parseCodeblockMetadata("``` path=src/my folder/sub folder/file.ts")).toEqual({
          language: null,
          relPath: "src/my folder/sub folder/file.ts",
          mode: null,
        });
      });
    });

    it("should parse path with special characters", () => {
      expect(parseCodeblockMetadata("``` path=src/file-name_123.ts")).toEqual({
        language: null,
        relPath: "src/file-name_123.ts",
        mode: null,
      });
    });

    it("should parse path with dots", () => {
      expect(parseCodeblockMetadata("``` path=src/file.name.ts")).toEqual({
        language: null,
        relPath: "src/file.name.ts",
        mode: null,
      });
    });
  });

  describe("mode parsing", () => {
    it("should parse mode only", () => {
      expect(parseCodeblockMetadata("``` mode=EDIT")).toEqual({
        language: null,
        relPath: null,
        mode: "EDIT",
      });
    });
  });

  describe("combination parsing", () => {
    it("should parse language and path", () => {
      expect(parseCodeblockMetadata("```typescript path=src/file.ts")).toEqual({
        language: "typescript",
        relPath: "src/file.ts",
        mode: null,
      });
    });

    it("should parse language and mode", () => {
      expect(parseCodeblockMetadata("```typescript mode=EDIT")).toEqual({
        language: "typescript",
        relPath: null,
        mode: "EDIT",
      });
    });

    it("should parse path and mode", () => {
      expect(parseCodeblockMetadata("``` path=src/file.ts mode=EDIT")).toEqual({
        language: null,
        relPath: "src/file.ts",
        mode: "EDIT",
      });
    });

    describe("with spaces in paths", () => {
      it("should parse language, path with spaces, and mode", () => {
        expect(
          parseCodeblockMetadata("```typescript path=src/my folder/file.ts mode=EDIT"),
        ).toEqual({
          language: "typescript",
          relPath: "src/my folder/file.ts",
          mode: "EDIT",
        });
      });

      it("should parse language and path with multiple spaces", () => {
        expect(
          parseCodeblockMetadata("```typescript path=src/my folder/sub folder/my file.ts"),
        ).toEqual({
          language: "typescript",
          relPath: "src/my folder/sub folder/my file.ts",
          mode: null,
        });
      });
    });

    it("should parse all components", () => {
      expect(parseCodeblockMetadata("```typescript path=src/file.ts mode=EDIT")).toEqual({
        language: "typescript",
        relPath: "src/file.ts",
        mode: "EDIT",
      });
    });
  });
});
