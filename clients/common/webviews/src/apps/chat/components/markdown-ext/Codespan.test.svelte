<script lang="ts">
  import { setContext } from "svelte";
  import { writable } from "svelte/store";
  import Codespan from "$common-webviews/src/apps/chat/components/markdown-ext/Codespan.svelte";
  import { type ChatModel } from "$common-webviews/src/apps/chat/models/chat-model";
  import { type ComponentProps } from "svelte";
  import { setChatModel } from "../../chat-context";

  export let chatModel: ChatModel;
  export let props: ComponentProps<Codespan>;

  setChatModel(chatModel);
  setContext("requestId", writable("1234"));
</script>

<Codespan {...props} />
