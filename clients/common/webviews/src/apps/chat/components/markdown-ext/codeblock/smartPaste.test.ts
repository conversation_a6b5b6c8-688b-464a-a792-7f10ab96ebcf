import { AsyncMsgSender } from "$common-webviews/src/common/hosts/async-messaging";
import { type Tokens } from "marked";
import { beforeEach, describe, expect, type MockInstance, test, vi } from "vitest";
import { createSmartPasteAction } from "../../../components/markdown-ext/codeblock/smartPaste";
import { ChatModel } from "../../../models/chat-model";
import { SpecialContextInputModel } from "../../../models/context-model";
import { HostClientType } from "$common-webviews/src/common/hosts/host-types";

const TEST_METADATA = {
  language: "typescript",
  relPath: "util/foobar.ts",
  mode: "EDIT",
};

describe("createSmartPasteAction", () => {
  let testKit: SmartPasteTestKit;

  beforeEach(() => {
    testKit = new SmartPasteTestKit();
  });

  test("canTriggerSmartPaste returns false when smart paste is disabled", () => {
    const disabledTestKit = new SmartPasteTestKit(false);
    const result = createSmartPasteAction(
      disabledTestKit.chatModel,
      TEST_METADATA,
      SmartPasteTestKit.token,
      SmartPasteTestKit.requestId,
      true,
      true,
      true,
      true,
      () => disabledTestKit.hasClicked,
      disabledTestKit.setHasClicked,
    );

    expect(result.canTriggerSmartPaste).toBe(false);
  });

  test("canTriggerSmartPaste returns true for valid edit scenario", () => {
    const result = createSmartPasteAction(
      testKit.chatModel,
      TEST_METADATA,
      SmartPasteTestKit.token,
      SmartPasteTestKit.requestId,
      true,
      true,
      true,
      true,
      () => testKit.hasClicked,
      testKit.setHasClicked,
    );

    expect(result.canTriggerSmartPaste).toBe(true);
  });

  test("smartPaste calls chatModel.smartPaste with correct parameters", () => {
    const result = createSmartPasteAction(
      testKit.chatModel,
      TEST_METADATA,
      SmartPasteTestKit.token,
      SmartPasteTestKit.requestId,
      true,
      true,
      true,
      true,
      () => testKit.hasClicked,
      testKit.setHasClicked,
    );

    result.smartPaste({});
    expect(testKit.chatModel.smartPaste).toHaveBeenCalledWith(
      SmartPasteTestKit.requestId,
      SmartPasteTestKit.token.text,
      TEST_METADATA.relPath,
      { requireFileConfirmation: !TEST_METADATA.relPath },
    );

    result.smartPasteButton.onClick();
    expect(testKit.chatModel.smartPaste).toHaveBeenCalledWith(
      SmartPasteTestKit.requestId,
      SmartPasteTestKit.token.text,
      TEST_METADATA.relPath,
      { requireFileConfirmation: !TEST_METADATA.relPath },
    );
    expect(testKit.hasClicked).toBe(true);
  });

  test("smartPasteButton triggers correct actions on hover", () => {
    const result = createSmartPasteAction(
      testKit.chatModel,
      TEST_METADATA,
      SmartPasteTestKit.token,
      SmartPasteTestKit.requestId,
      true,
      true,
      true,
      true,
      () => testKit.hasClicked,
      testKit.setHasClicked,
    );

    result.smartPasteButton.onMouseOver!({} as MouseEvent);
    expect(testKit.chatModel.smartPaste).toHaveBeenCalledWith(
      SmartPasteTestKit.requestId,
      SmartPasteTestKit.token.text,
      TEST_METADATA.relPath,
      { dryRun: true, requireFileConfirmation: !TEST_METADATA.relPath },
    );
  });
});

class SmartPasteTestKit {
  private _chatModel: ChatModel;

  public hasClicked = false;
  public smartPasteSpy!: MockInstance<typeof ChatModel.prototype.smartPaste>;

  public static token: Tokens.Code = {
    text: `export function foobar() {
        console.log("hello world");
    }`,
    lang: "typescript",
    type: "code",
    raw: '```typescript\nexport function foobar() {\n    console.log("hello world");\n}\n```',
  };

  public static requestId = "request-1";

  constructor(enabled: boolean = true) {
    this._chatModel = new ChatModel(
      new AsyncMsgSender(vi.fn()),
      {
        clientType: HostClientType.vscode,
        postMessage: vi.fn(),
        getState: vi.fn(),
        setState: vi.fn(),
      },
      new SpecialContextInputModel(),
      {
        initialFlags: { enableSmartPaste: enabled },
      },
    );
    this.smartPasteSpy = vi.spyOn(this._chatModel, "smartPaste");
  }

  get chatModel(): ChatModel {
    return this._chatModel;
  }

  setHasClicked = (val: boolean) => {
    this.hasClicked = val;
  };
}
