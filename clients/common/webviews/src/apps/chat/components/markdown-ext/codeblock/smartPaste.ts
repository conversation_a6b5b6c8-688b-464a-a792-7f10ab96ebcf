import { SmartPastePrecomputeMode } from "$vscode/src/webview-providers/webview-messages";
import { type Tokens } from "marked";
import { type ChatModel } from "../../../models/chat-model";
import { type ICodeblockActionButton, type ICodeblockMetadata } from "../utils";
import { visibilityObserver } from "../../actions/trackOnScreen";
import { HoverContext, onHover } from "$common-webviews/src/common/actions/onHoverAction";
import { CodeblockActionType } from "./types";

export interface CreateSmartPasteResult {
  /** Smart pastes the code from the token into the file from the codeblock metadata */
  smartPaste: (options: { dryRun?: boolean; instantApply?: boolean }) => void;
  /** Whether smart paste can be triggered */
  canTriggerSmartPaste: boolean;
  /** Whether smart paste can be precomputed */
  canPrecomputeSmartPaste: boolean;
  /** The action button for reviewing changes (previous "Apply" button) */
  smartPasteButton: ICodeblockActionButton;
  /** The action button for direct apply (only populated when enableDirectApply is true) */
  directApplyButton?: ICodeblockActionButton;
}

/**
 * Creates the smart paste button and the current state of smart paste
 */
export function createSmartPasteAction(
  chatModel: ChatModel | undefined,
  codeblockMetadata: ICodeblockMetadata | undefined,
  token: Tokens.Code,
  requestId: string,
  isExchangeSuccessful: boolean,
  resolvedTargetPath: boolean,
  isVisibleAndStable: boolean,
  isHoveredStable: boolean,
  hasClickedSmartPaste: () => boolean,
  setHasClickedSmartPaste: (val: boolean) => void,
): CreateSmartPasteResult {
  const hasCodepath = !!codeblockMetadata?.relPath;
  const hasMode = !!codeblockMetadata?.mode;
  const isEdit = hasMode && codeblockMetadata?.mode === "EDIT";
  const hasActiveFile = !!chatModel?.specialContextInputModel?.recentFiles.length;

  function canTriggerSmartPaste() {
    // Smart paste is disabled
    if (!chatModel?.flags.enableSmartPaste) {
      return false;
      // Current exchange is not finished
    } else if (!isExchangeSuccessful) {
      return false;
      // Codepath exists, this is an edit, and we have a resolved file
    } else if (hasCodepath && isEdit && resolvedTargetPath) {
      return true;
      // Codepath doesn't exist, but we have an active file
    } else if (!hasCodepath && hasActiveFile) {
      return false;
    } else {
      return false;
    }
  }

  function canPrecomputeSmartPaste() {
    // Smart paste is disabled or this is not an edit
    if (!canTriggerSmartPaste() || !isEdit) {
      return false;
    } else {
      // Depending on the mode we will have different behavior
      switch (chatModel?.flags.smartPastePrecomputeMode) {
        case SmartPastePrecomputeMode.off:
          return false;
        case SmartPastePrecomputeMode.visible:
          return isVisibleAndStable;
        case SmartPastePrecomputeMode.visibleHover:
          return isVisibleAndStable && isHoveredStable;
        case SmartPastePrecomputeMode.on:
          return true;
        default:
          return false;
      }
    }
  }

  function smartPaste(options: { dryRun?: boolean; instantApply?: boolean }) {
    if (options.dryRun && !canPrecomputeSmartPaste()) {
      return;
    }

    if (token.text && requestId) {
      chatModel?.smartPaste(requestId, token.text, codeblockMetadata?.relPath, {
        ...options,
        requireFileConfirmation: !hasCodepath,
      });
    }
  }

  // Create common button properties
  const createButton = (options: {
    title: string;
    stateTitle: { neutral: string; success: string; failure: string };
    onClick: () => void;
    onSuccessMessage: string;
    buttonText: string;
    codeblockActionType: CodeblockActionType;
  }): ICodeblockActionButton => ({
    ...options,
    iconName: "check",
    // Mouse down prevent default prevents blurs caused by button press
    onMouseDown: (e: MouseEvent) => e.preventDefault(),
    onMouseOver: () => smartPaste({ dryRun: true }),
    onFocus: () => smartPaste({ dryRun: true }),
  });

  // Create the review changes button (always available)
  // Create the direct apply button (only when enableDirectApply is true)
  let reviewChangesButton: ICodeblockActionButton | undefined;
  let directApplyButton: ICodeblockActionButton | undefined;
  if (chatModel?.flags.enableDirectApply) {
    reviewChangesButton = createButton({
      codeblockActionType: CodeblockActionType.smartPaste,
      title: "Review changes before applying",
      stateTitle: {
        neutral: "Review changes before applying",
        success: "Opened",
        failure: "Failed to apply",
      },
      onClick: () => {
        setHasClickedSmartPaste(true);
        smartPaste({});
      },
      onSuccessMessage: "Opening diff view...",
      buttonText: "Review",
    });
    directApplyButton = createButton({
      codeblockActionType: CodeblockActionType.directApply,
      title: "Apply changes directly to file",
      stateTitle: {
        neutral: "Apply changes directly to file",
        success: "Applied",
        failure: "Failed to apply",
      },
      onClick: () => {
        setHasClickedSmartPaste(true);
        smartPaste({ instantApply: true });
      },
      onSuccessMessage: "Applying changes directly...",
      buttonText: "Apply",
    });
  } else {
    reviewChangesButton = createButton({
      codeblockActionType: CodeblockActionType.smartPaste,
      title: "Apply changes",
      stateTitle: {
        neutral: "Apply changes",
        success: "Applied",
        failure: "Failed to apply",
      },
      onClick: () => {
        setHasClickedSmartPaste(true);
        smartPaste({});
      },
      onSuccessMessage: "Opening diff view...",
      buttonText: "Apply",
    });
  }

  return {
    smartPaste,
    canTriggerSmartPaste: canTriggerSmartPaste(),
    canPrecomputeSmartPaste: canPrecomputeSmartPaste(),
    smartPasteButton: reviewChangesButton,
    directApplyButton,
  };
}

/**
 * Create observers for determining whether the element is 10% visible and hovered over for 600ms
 *
 * This is used for determining whether we should precompute smart paste
 */
export function smartPasteObserver(
  element: HTMLElement,
  setIsVisible: (val: boolean) => void,
  setIsHovered: (val: boolean) => void,
) {
  let visilibtyObserver: ReturnType<typeof visibilityObserver> | undefined;
  let hoverAction: ReturnType<typeof onHover> | undefined;

  visilibtyObserver?.destroy();
  visilibtyObserver = visibilityObserver(element, {
    onVisible: () => setIsVisible(true),
    onHidden: () => setIsVisible(false),
    scrollTarget: document.body,
    threshold: 0.1, // 10% of the element needs to be visible to trigger the observer
  });
  hoverAction?.destroy();
  hoverAction = onHover(
    element,
    new HoverContext({
      onHoverStart: () => setIsHovered(true),
      onHoverEnd: () => {},
      // Need to be stably hovered over codeblock for 600ms
      hoverTriggerDuration: 600,
    }),
  );

  return () => {
    visilibtyObserver?.destroy();
    hoverAction?.destroy();
  };
}
