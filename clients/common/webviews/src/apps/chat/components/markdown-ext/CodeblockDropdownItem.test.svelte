<script lang="ts">
  import { TooltipContext } from "$common-webviews/src/design-system/_primitives/TooltipAugment/context";
  import {
    CONTENT_CONTEXT_KEY,
    type ContentContext,
  } from "$common-webviews/src/design-system/components/DropdownMenuAugment/Content.svelte";
  import { writable } from "svelte/store";
  import CodeblockDropdownItem from "./CodeblockDropdownItem.svelte";
  import type { ICodeblockActionButton } from "./utils";
  import { setContext } from "svelte";
  import DropdownMenuFocusContext from "$common-webviews/src/design-system/components/DropdownMenuAugment/focus-context";

  export let data: ICodeblockActionButton;
  export let closeDropdown: () => void;

  const tooltipCtx = new TooltipContext({
    defaultOpen: undefined,
    open: undefined,
    onOpenChange: undefined,
    delayDurationMs: undefined,
    onHoverStart: () => {},
    onHoverEnd: () => {},
    triggerOn: [],
    tippyTheme: undefined,
    nested: false,
  });
  setContext<TooltipContext>(TooltipContext.CONTEXT_KEY, tooltipCtx);

  const contentCtx: ContentContext = { size: writable(2) };
  setContext<ContentContext>(CONTENT_CONTEXT_KEY, contentCtx);

  // Create and set up the focus context
  const focusContext = new DropdownMenuFocusContext();
  setContext<DropdownMenuFocusContext>(DropdownMenuFocusContext.CONTEXT_KEY, focusContext);

  // Create a container div that will be registered as the root element
  function handleRootMount(node: HTMLElement) {
    // Register the root element with the focus context
    focusContext.registerRoot(node);
  }
</script>

<div use:handleRootMount>
  <CodeblockDropdownItem {data} {closeDropdown} />
</div>
