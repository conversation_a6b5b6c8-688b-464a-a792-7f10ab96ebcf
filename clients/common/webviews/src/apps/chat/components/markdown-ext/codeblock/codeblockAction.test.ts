import { AsyncMsgSender } from "$common-webviews/src/common/hosts/async-messaging";
import { ChatMetricName } from "$vscode/src/metrics/types";
import { afterEach, beforeEach, describe, expect, type MockInstance, test, vi } from "vitest";
import { ChatModel } from "../../../models/chat-model";
import { SpecialContextInputModel } from "../../../models/context-model";
import { createNewFile, writeToClipboard } from "../utils";
import { CodeblockActionType } from "./types";
import {
  createCopyToClipboardAction,
  createGoToFileAction,
  createInsertIntoNewFileAction,
  getPrimaryButtonType,
} from "./codeblockActions";
import { HostClientType } from "$common-webviews/src/common/hosts/host-types";

const TEST_METADATA = { relPath: "test/path.ts", language: "typescript", mode: "EDIT" };
const TEST_CODE = `export function foobar() {
    console.log("hello world");
}`;

function getCode() {
  return TEST_CODE;
}

// Mock the utils
vi.mock("../utils", () => ({
  writeToClipboard: vi.fn(),
  createNewFile: vi.fn(),
  isNotebook: vi.fn((path) => {
    const notebookExtensions = [".ipynb", ".rmd", ".qmd", ".jl", ".zmd"];
    return notebookExtensions.some((ext) => path.toLowerCase().endsWith(ext));
  }),
}));

describe("CodeblockActions", () => {
  let testKit: CodeblockActionsTestKit;

  beforeEach(async () => {
    testKit = new CodeblockActionsTestKit();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe("createCopyToClipboardAction", () => {
    test("copies code to clipboard when clicked", () => {
      const action = createCopyToClipboardAction(getCode);

      action.onClick();
      expect(writeToClipboard).toHaveBeenCalledWith(TEST_CODE);
    });

    test("does not copy code to clipboard when code is undefined", () => {
      const action = createCopyToClipboardAction(() => undefined);

      action.onClick();
      expect(writeToClipboard).not.toHaveBeenCalled();
    });
  });

  describe("createInsertIntoNewFileAction", () => {
    test("creates new file when clicked", () => {
      const action = createInsertIntoNewFileAction(getCode, TEST_METADATA, testKit.chatModel);
      action.onClick();

      expect(createNewFile).toHaveBeenCalledWith(
        TEST_CODE,
        testKit.chatModel.extensionClient,
        TEST_METADATA.relPath,
      );
    });
  });

  describe("createGoToFileAction", () => {
    test("opens file when clicked", () => {
      const action = createGoToFileAction(TEST_CODE, true, TEST_METADATA, testKit.chatModel);
      action.onClick();

      expect(testKit.metricsSpy).toHaveBeenCalledWith(ChatMetricName.chatCodeblockGoToFile);
      expect(testKit.openFileSpy).toHaveBeenCalledWith({
        repoRoot: "",
        pathName: TEST_METADATA.relPath,
        snippet: TEST_CODE,
      });
    });

    test("opens file without snippet when not an excerpt", () => {
      const action = createGoToFileAction(TEST_CODE, false, TEST_METADATA, testKit.chatModel);
      action.onClick();

      expect(testKit.openFileSpy).toHaveBeenCalledWith({
        repoRoot: "",
        pathName: TEST_METADATA.relPath,
        snippet: undefined,
      });
    });
  });
});

describe("getPrimaryButtonType", () => {
  test("returns copy when hasCodepath is true", () => {
    const result = getPrimaryButtonType(
      TEST_METADATA,
      false, // canTriggerSmartPaste
      false, // targetPathAttemptedAndFailed
    );

    expect(result).toBe(CodeblockActionType.copy);
  });

  test("returns copy when hasCodepath is false", () => {
    const result = getPrimaryButtonType(
      { ...TEST_METADATA, relPath: null },
      false, // canTriggerSmartPaste
      false, // targetPathAttemptedAndFailed
    );

    expect(result).toBe(CodeblockActionType.copy);
  });

  test("returns insert when hasCodepath is true and targetPathAttemptedAndFailed is true", () => {
    const result = getPrimaryButtonType(
      TEST_METADATA,
      false, // canTriggerSmartPaste
      true, // targetPathAttemptedAndFailed
    );

    expect(result).toBe(CodeblockActionType.insert);
  });

  test("returns smartPaste when canTriggerSmartPaste is true and not a notebook", () => {
    const result = getPrimaryButtonType(
      TEST_METADATA,
      true, // canTriggerSmartPaste
      false, // targetPathAttemptedAndFailed
    );

    expect(result).toBe(CodeblockActionType.smartPaste);
  });

  test("returns directApply when enableDirectApply is true, canTriggerSmartPaste is true, and not a notebook", () => {
    const result = getPrimaryButtonType(
      TEST_METADATA,
      true, // canTriggerSmartPaste
      false, // targetPathAttemptedAndFailed
      true, // enableDirectApply
    );

    expect(result).toBe(CodeblockActionType.directApply);
  });
});

class CodeblockActionsTestKit {
  private _chatModel: ChatModel;

  public metricsSpy: MockInstance<(eventName: ChatMetricName) => void>;
  public openFileSpy: MockInstance<
    (params: { repoRoot: string; pathName: string; snippet?: string }) => void
  >;

  constructor() {
    this._chatModel = new ChatModel(
      new AsyncMsgSender(vi.fn()),
      {
        clientType: HostClientType.vscode,
        postMessage: vi.fn(),
        getState: vi.fn(),
        setState: vi.fn(),
      },
      new SpecialContextInputModel(),
    );
    this.metricsSpy = vi.spyOn(this._chatModel.extensionClient, "reportWebviewClientEvent");
    this.openFileSpy = vi.spyOn(this._chatModel.extensionClient, "openFile");
  }

  get chatModel(): ChatModel {
    return this._chatModel;
  }
}
