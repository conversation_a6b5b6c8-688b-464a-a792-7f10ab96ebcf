<script lang="ts">
  import RemoteAgentHeader from "./thread-headers/RemoteAgentHeader.svelte";
  import LocalThreadHeader from "./thread-headers/LocalThreadHeader.svelte";
  import { getContext } from "svelte";
  import { RemoteAgentsModel } from "$common-webviews/src/apps/remote-agent-manager/models/remote-agents-model";
  import { type ChatModel } from "$common-webviews/src/apps/chat/models/chat-model";

  const remoteAgentsModel = getContext<RemoteAgentsModel>(RemoteAgentsModel.key);
  const chatModel = getContext<ChatModel>("chatModel");

  $: conversationId = $chatModel.currentConversationId;
</script>

{#if $remoteAgentsModel?.isActive}
  <RemoteAgentHeader />
{:else if conversationId}
  <LocalThreadHeader />
{/if}
