<script lang="ts">
  import { getContext } from "svelte";
  import TaskList from "./TaskList.svelte";
  import {
    type ICurrentConversationTaskStore,
    CurrentConversationTaskStore,
  } from "../../../models/task-store";

  const taskStore = getContext<ICurrentConversationTaskStore>(CurrentConversationTaskStore.key);
  const { canShowTaskList } = taskStore;
</script>

<!-- Wrapper component that removes container styling from TaskList for tab usage -->
<div class="c-task-list-tab">
  {#if $canShowTaskList}
    <TaskList />
  {/if}
</div>

<style>
  .c-task-list-tab {
    /* Remove any container styling to let the tab panel handle layout */
    width: 100%;
    height: 100%;
    overflow: auto;
  }

  /* Override TaskList container styles when used in tab */
  .c-task-list-tab :global(.c-task-list-container) {
    /* Remove margins and padding that are handled by the tab panel */
    margin: 0;
    padding: 0;
  }

  .c-task-list-tab :global(.c-task-list-container__block-container) {
    /* Ensure the collapsible container fits within the tab */
    height: 100%;
  }
</style>
