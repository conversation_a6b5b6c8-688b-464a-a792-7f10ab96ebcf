import { describe, it, expect } from "vitest";
import {
  TaskState,
  type HydratedTask,
  TaskUpdatedBy,
} from "@augment-internal/sidecar-libs/src/agent/task/task-types";
import {
  getTaskStatusColor,
  getTaskStatusLabel,
  getTaskStatusIcon,
  findActiveTask,
  getTaskProgress,
} from "./task-status-utils";

describe("task-status-utils", () => {
  describe("getTaskStatusColor", () => {
    it("should return correct colors for all task states", () => {
      expect(getTaskStatusColor(TaskState.NOT_STARTED)).toBe("neutral");
      expect(getTaskStatusColor(TaskState.IN_PROGRESS)).toBe("info");
      expect(getTaskStatusColor(TaskState.COMPLETE)).toBe("success");
      expect(getTaskStatusColor(TaskState.CANCELLED)).toBe("error");
    });

    it("should return neutral for unknown task states", () => {
      // Test with an invalid task state (cast to TaskState for testing)
      expect(getTaskStatusColor("UNKNOWN_STATE" as TaskState)).toBe("neutral");
    });

    it("should handle all enum values", () => {
      // Ensure we test all possible TaskState enum values
      const allTaskStates = Object.values(TaskState);
      expect(allTaskStates).toHaveLength(4);

      allTaskStates.forEach((state) => {
        const color = getTaskStatusColor(state);
        expect(["neutral", "info", "success", "error", "warning"]).toContain(color);
      });
    });
  });

  describe("getTaskStatusLabel", () => {
    it("should return correct labels for all task states", () => {
      expect(getTaskStatusLabel(TaskState.NOT_STARTED)).toBe("Not Started");
      expect(getTaskStatusLabel(TaskState.IN_PROGRESS)).toBe("In Progress");
      expect(getTaskStatusLabel(TaskState.COMPLETE)).toBe("Completed");
      expect(getTaskStatusLabel(TaskState.CANCELLED)).toBe("Cancelled");
    });

    it("should return 'Not Started' for unknown task states", () => {
      // Test with an invalid task state (cast to TaskState for testing)
      expect(getTaskStatusLabel("UNKNOWN_STATE" as TaskState)).toBe("Not Started");
    });

    it("should return human-readable strings", () => {
      const allTaskStates = Object.values(TaskState);

      allTaskStates.forEach((state) => {
        const label = getTaskStatusLabel(state);
        expect(typeof label).toBe("string");
        expect(label.length).toBeGreaterThan(0);
        // Labels should be properly capitalized
        expect(label[0]).toBe(label[0].toUpperCase());
      });
    });

    it("should have consistent label format", () => {
      // All labels should be title case and contain only letters and spaces
      const allTaskStates = Object.values(TaskState);

      allTaskStates.forEach((state) => {
        const label = getTaskStatusLabel(state);
        expect(label).toMatch(/^[A-Z][a-z]*(\s[A-Z][a-z]*)*$/);
      });
    });
  });

  describe("getTaskStatusIcon", () => {
    it("should return valid Svelte components for all task states", () => {
      expect(getTaskStatusIcon(TaskState.NOT_STARTED)).toBeDefined();
      expect(getTaskStatusIcon(TaskState.IN_PROGRESS)).toBeDefined();
      expect(getTaskStatusIcon(TaskState.COMPLETE)).toBeDefined();
      expect(getTaskStatusIcon(TaskState.CANCELLED)).toBeDefined();
    });

    it("should return different icons for different states", () => {
      const notStartedIcon = getTaskStatusIcon(TaskState.NOT_STARTED);
      const inProgressIcon = getTaskStatusIcon(TaskState.IN_PROGRESS);
      const completeIcon = getTaskStatusIcon(TaskState.COMPLETE);
      const cancelledIcon = getTaskStatusIcon(TaskState.CANCELLED);

      // Each state should have a unique icon component
      const icons = [notStartedIcon, inProgressIcon, completeIcon, cancelledIcon];
      const uniqueIcons = new Set(icons);
      expect(uniqueIcons.size).toBe(4);
    });

    it("should return function/component type", () => {
      const allTaskStates = Object.values(TaskState);

      allTaskStates.forEach((state) => {
        const icon = getTaskStatusIcon(state);
        expect(typeof icon).toBe("function");
      });
    });

    it("should handle unknown task states gracefully", () => {
      // Should return the default icon (same as NOT_STARTED)
      const unknownIcon = getTaskStatusIcon("UNKNOWN_STATE" as TaskState);
      const notStartedIcon = getTaskStatusIcon(TaskState.NOT_STARTED);
      expect(unknownIcon).toBe(notStartedIcon);
    });
  });

  describe("findActiveTask", () => {
    // Helper function to create a test task
    const createTask = (
      uuid: string,
      name: string,
      state: TaskState,
      subTasks: HydratedTask[] = [],
    ): HydratedTask => ({
      uuid,
      name,
      description: `Description for ${name}`,
      state,
      subTasks: subTasks.map((task) => task.uuid),
      subTasksData: subTasks,
      lastUpdated: Date.now(),
      lastUpdatedBy: TaskUpdatedBy.USER,
    });

    it("should return undefined for undefined task", () => {
      expect(findActiveTask(undefined)).toBeUndefined();
    });

    it("should return undefined when no tasks are in progress", () => {
      const rootTask = createTask("root", "Root Task", TaskState.NOT_STARTED, [
        createTask("task1", "Task 1", TaskState.NOT_STARTED),
        createTask("task2", "Task 2", TaskState.COMPLETE),
        createTask("task3", "Task 3", TaskState.CANCELLED),
      ]);

      expect(findActiveTask(rootTask)).toBeUndefined();
    });

    it("should return the last leaf task that is in progress", () => {
      const leafTask1 = createTask("leaf1", "Leaf Task 1", TaskState.IN_PROGRESS);
      const leafTask2 = createTask("leaf2", "Leaf Task 2", TaskState.IN_PROGRESS);
      const leafTask3 = createTask("leaf3", "Leaf Task 3", TaskState.IN_PROGRESS);

      const rootTask = createTask("root", "Root Task", TaskState.NOT_STARTED, [
        createTask("task1", "Task 1", TaskState.IN_PROGRESS, [leafTask1]),
        createTask("task2", "Task 2", TaskState.IN_PROGRESS, [leafTask2]),
        createTask("task3", "Task 3", TaskState.IN_PROGRESS, [leafTask3]),
      ]);

      // Should return the last leaf task in progress (leafTask3)
      const activeTask = findActiveTask(rootTask);
      expect(activeTask).toBeDefined();
      expect(activeTask?.uuid).toBe("leaf3");
      expect(activeTask?.name).toBe("Leaf Task 3");
    });

    it("should return the last leaf task even with mixed states", () => {
      const leafTask1 = createTask("leaf1", "Leaf Task 1", TaskState.IN_PROGRESS);
      const leafTask2 = createTask("leaf2", "Leaf Task 2", TaskState.COMPLETE);
      const leafTask3 = createTask("leaf3", "Leaf Task 3", TaskState.IN_PROGRESS);
      const leafTask4 = createTask("leaf4", "Leaf Task 4", TaskState.NOT_STARTED);

      const rootTask = createTask("root", "Root Task", TaskState.NOT_STARTED, [
        createTask("task1", "Task 1", TaskState.IN_PROGRESS, [leafTask1, leafTask2]),
        createTask("task2", "Task 2", TaskState.IN_PROGRESS, [leafTask3, leafTask4]),
      ]);

      // Should return leafTask3 (the last leaf task in progress)
      const activeTask = findActiveTask(rootTask);
      expect(activeTask).toBeDefined();
      expect(activeTask?.uuid).toBe("leaf3");
    });

    it("should return deepest non-root task when no leaf tasks are in progress", () => {
      const deepTask = createTask("deep", "Deep Task", TaskState.IN_PROGRESS, [
        createTask("leaf1", "Leaf Task 1", TaskState.COMPLETE),
        createTask("leaf2", "Leaf Task 2", TaskState.NOT_STARTED),
      ]);

      const rootTask = createTask("root", "Root Task", TaskState.NOT_STARTED, [
        createTask("task1", "Task 1", TaskState.IN_PROGRESS, [
          createTask("nested", "Nested Task", TaskState.COMPLETE),
        ]),
        deepTask,
      ]);

      // Should return deepTask since no leaf tasks are in progress
      const activeTask = findActiveTask(rootTask);
      expect(activeTask).toBeDefined();
      expect(activeTask?.uuid).toBe("deep");
    });

    it("should skip root task from consideration", () => {
      const rootTask = createTask("root", "Root Task", TaskState.IN_PROGRESS, [
        createTask("task1", "Task 1", TaskState.NOT_STARTED),
        createTask("task2", "Task 2", TaskState.COMPLETE),
      ]);

      // Root task is in progress but should be ignored
      expect(findActiveTask(rootTask)).toBeUndefined();
    });

    it("should handle deeply nested task structures", () => {
      const deepLeaf = createTask("deep-leaf", "Deep Leaf", TaskState.IN_PROGRESS);

      const rootTask = createTask("root", "Root Task", TaskState.NOT_STARTED, [
        createTask("level1", "Level 1", TaskState.IN_PROGRESS, [
          createTask("level2", "Level 2", TaskState.IN_PROGRESS, [
            createTask("level3", "Level 3", TaskState.IN_PROGRESS, [deepLeaf]),
          ]),
        ]),
      ]);

      const activeTask = findActiveTask(rootTask);
      expect(activeTask).toBeDefined();
      expect(activeTask?.uuid).toBe("deep-leaf");
    });

    it("should prefer leaf tasks over non-leaf tasks", () => {
      const leafTask = createTask("leaf", "Leaf Task", TaskState.IN_PROGRESS);
      const nonLeafTask = createTask("non-leaf", "Non-Leaf Task", TaskState.IN_PROGRESS, [
        createTask("child", "Child Task", TaskState.COMPLETE),
      ]);

      const rootTask = createTask("root", "Root Task", TaskState.NOT_STARTED, [
        nonLeafTask,
        createTask("parent", "Parent Task", TaskState.NOT_STARTED, [leafTask]),
      ]);

      // Should prefer the leaf task over the non-leaf task
      const activeTask = findActiveTask(rootTask);
      expect(activeTask).toBeDefined();
      expect(activeTask?.uuid).toBe("leaf");
    });
  });

  describe("getTaskProgress", () => {
    // Helper function to create a test task
    const createTask = (
      uuid: string,
      name: string,
      state: TaskState,
      subTasks: HydratedTask[] = [],
    ): HydratedTask => ({
      uuid,
      name,
      description: `Description for ${name}`,
      state,
      subTasks: subTasks.map((task) => task.uuid),
      subTasksData: subTasks,
      lastUpdated: Date.now(),
      lastUpdatedBy: TaskUpdatedBy.USER,
    });

    it("should return zero progress for undefined task", () => {
      const progress = getTaskProgress(undefined);
      expect(progress).toEqual({ completed: 0, total: 0 });
    });

    it("should exclude root task from progress calculation", () => {
      const rootTask = createTask("root", "Root Task", TaskState.COMPLETE, [
        createTask("task1", "Task 1", TaskState.COMPLETE),
        createTask("task2", "Task 2", TaskState.NOT_STARTED),
      ]);

      const progress = getTaskProgress(rootTask);
      expect(progress).toEqual({ completed: 1, total: 2 });
    });

    it("should count all non-root tasks correctly", () => {
      const rootTask = createTask("root", "Root Task", TaskState.NOT_STARTED, [
        createTask("task1", "Task 1", TaskState.COMPLETE),
        createTask("task2", "Task 2", TaskState.IN_PROGRESS),
        createTask("task3", "Task 3", TaskState.CANCELLED),
        createTask("task4", "Task 4", TaskState.NOT_STARTED),
      ]);

      const progress = getTaskProgress(rootTask);
      expect(progress).toEqual({ completed: 1, total: 4 });
    });

    it("should handle nested tasks correctly", () => {
      const rootTask = createTask("root", "Root Task", TaskState.NOT_STARTED, [
        createTask("task1", "Task 1", TaskState.COMPLETE, [
          createTask("subtask1", "Subtask 1", TaskState.COMPLETE),
          createTask("subtask2", "Subtask 2", TaskState.IN_PROGRESS),
        ]),
        createTask("task2", "Task 2", TaskState.NOT_STARTED),
      ]);

      const progress = getTaskProgress(rootTask);
      expect(progress).toEqual({ completed: 2, total: 4 }); // task1, subtask1 are complete; task2, subtask2 are not
    });
  });

  describe("integration tests", () => {
    it("should have consistent mappings across all functions", () => {
      const allTaskStates = Object.values(TaskState);

      allTaskStates.forEach((state) => {
        // All functions should handle the same task state without throwing
        expect(() => getTaskStatusColor(state)).not.toThrow();
        expect(() => getTaskStatusLabel(state)).not.toThrow();
        expect(() => getTaskStatusIcon(state)).not.toThrow();
      });
    });

    it("should provide complete coverage of TaskState enum", () => {
      // Verify that we have explicit test cases for all enum values
      const testedStates = [
        TaskState.NOT_STARTED,
        TaskState.IN_PROGRESS,
        TaskState.COMPLETE,
        TaskState.CANCELLED,
      ];

      const allTaskStates = Object.values(TaskState);
      expect(testedStates.sort()).toEqual(allTaskStates.sort());
    });
  });
});
