<script lang="ts">
  import {
    TaskState,
    TaskUpdatedBy,
  } from "@augment-internal/sidecar-libs/src/agent/task/task-types";
  import {
    TaskListAction,
    TaskListActionTrigger,
    AgentSessionEventName,
  } from "@augment-internal/sidecar-libs/src/metrics/types";
  import { getTaskProgress } from "../utils/task-status-utils";

  import DraggableListItemAugment from "$common-webviews/src/design-system/components/DraggableListAugment/DraggableListItemAugment.svelte";
  import {
    CurrentConversationTaskStore,
    type ICurrentConversationTaskStore,
  } from "../../../models/task-store";
  import type { ChatModel } from "../../../models/chat-model";

  import { getContext, tick } from "svelte";
  import { get } from "svelte/store";
  import TaskDetails from "./TaskDetails.svelte";
  import TaskIconButton from "../task-status/TaskIconButton.svelte";
  import EditableTextAugment from "$common-webviews/src/design-system/components/EditableTextAugment.svelte";
  import TaskActionButtons from "./TaskActionButtons.svelte";
  import { getVisibleSubTasks, type VisibleHydratedTask } from "../utils/task-visibility-utils";

  // Props
  export let task: VisibleHydratedTask;
  export let taskStore: ICurrentConversationTaskStore = getContext<ICurrentConversationTaskStore>(
    CurrentConversationTaskStore.key,
  );
  export let editable: boolean = true;
  export let isRootTask: boolean = true;

  // Get chat model from context
  const chatModel = getContext<ChatModel>("chatModel");

  // Local state
  let editableTextComponent: EditableTextAugment | undefined;
  let taskItemElement: HTMLElement | undefined;
  let childrenExpanded: boolean | undefined = undefined;

  // Computed properties for UI display
  $: isCancelled = task.state === TaskState.CANCELLED;

  // Get visible subtasks using the preprocessed visibility flags
  $: visibleSubTasks = getVisibleSubTasks(task);

  // Name editing function
  async function handleNameEdit(newName: string) {
    const trimmedName = newName.trim();
    if (trimmedName !== task.name && trimmedName) {
      // Emit user-triggered updateTaskName event
      const rootTaskValue = get(taskStore.rootTask);
      const taskProgress = getTaskProgress(rootTaskValue);
      const taskListUsageData = {
        action: TaskListAction.updateTaskName,
        totalTasksCount: taskProgress.total,
        triggeredBy: TaskListActionTrigger.user,
      };

      // Get conversation ID from the latest exchange, fallback to conversation model ID
      const chatHistory = chatModel.currentConversationModel.chatHistory;
      const conversationId =
        chatHistory.length > 0
          ? chatHistory[chatHistory.length - 1].request_id || chatModel.currentConversationModel.id
          : chatModel.currentConversationModel.id;

      chatModel.currentConversationModel.extensionClient.reportAgentSessionEvent({
        eventName: AgentSessionEventName.taskListUsage,
        conversationId,
        eventData: {
          taskListUsageData,
        },
      });

      await taskStore.updateTask(task.uuid, { name: trimmedName }, TaskUpdatedBy.USER);
    }
  }

  async function onBlur(e: FocusEvent) {
    const newName = (e.target as HTMLInputElement)?.value;
    await handleNameEdit(newName || "");
  }

  async function onKeyDown(e: KeyboardEvent) {
    if (e.shiftKey || e.ctrlKey || e.metaKey) {
      return;
    }
    switch (e.key) {
      case "Enter": {
        // Save the task
        const newName = (e.target as HTMLInputElement)?.value;
        await handleNameEdit(newName || "");
        const newTask = await taskStore.addNewTaskAfter(task.uuid, {
          uuid: "new-task",
          name: "",
          description: "",
          state: TaskState.NOT_STARTED,
          subTasks: [],
          lastUpdated: Date.now(),
          lastUpdatedBy: TaskUpdatedBy.USER,
        });
        if (!newTask) return;
        await tick();

        // Select the new task component, and get the editable text field
        const newTaskElement = document.getElementById(
          `task-${newTask.uuid}`,
        ) as HTMLElement | null;
        if (!newTaskElement) return;
        const editableTextWrapper = newTaskElement.querySelector(
          ".c-task-tree-item__name-editable",
        ) as HTMLElement | null;
        if (!editableTextWrapper) return;
        const editableTextElement = editableTextWrapper.querySelector(
          "input",
        ) as HTMLInputElement | null;
        if (!editableTextElement) return;

        // Focus and select the text
        editableTextElement.focus();
        editableTextElement.select();
        break;
      }
      case "Tab": {
        const newName = (e.target as HTMLInputElement)?.value;
        await handleNameEdit(newName || "");
        break;
      }
    }
  }
</script>

{#if isRootTask}
  <!-- If this is the root task, only render its children -->
  <div class="c-task-tree-root-children">
    {#each visibleSubTasks as subtask (subtask.uuid)}
      <svelte:self {taskStore} task={subtask} {editable} isRootTask={false} />
    {/each}
  </div>
{:else if task.isVisible}
  <!-- Regular task rendering for non-root tasks -->
  <DraggableListItemAugment
    class="c-task-tree-item"
    item={task}
    id={`task-${task.uuid}`}
    hasNestedItems={!!task.subTasksData && task.subTasksData.length > 0}
    disabled={!editable}
    bind:element={taskItemElement}
    bind:expanded={childrenExpanded}
  >
    <!-- Header contents -->
    <div slot="header-contents" class="c-task-tree-item__header">
      <!-- Status icon on the left -->
      <div class="c-task-tree-item__status-cell">
        <TaskIconButton
          taskState={task.state}
          taskUuid={task.uuid}
          {taskStore}
          disabled={!editable}
          size={1}
        />
      </div>

      <div class="c-task-tree-item__name" class:c-task-tree-item__text--cancelled={isCancelled}>
        <EditableTextAugment
          bind:this={editableTextComponent}
          class="c-task-tree-item__name-editable"
          value={task.name}
          placeholder="Name this task..."
          size={1}
          disabled={!editable}
          clickToEdit={true}
          on:keydown={onKeyDown}
          on:blur={onBlur}
        />
      </div>
    </div>

    <!-- Group all action buttons together -->
    <div class="c-task-tree-item__action-buttons" slot="actions">
      <TaskActionButtons taskUuid={task.uuid} {taskStore} {editable} />
    </div>

    <!-- Task details section -->
    <svelte:fragment slot="contents">
      <div class="c-task-tree-item__details">
        <TaskDetails {task} {taskStore} {editable} />
      </div>
      {#if visibleSubTasks && visibleSubTasks.length > 0 && childrenExpanded}
        <div class="c-task-tree-item__subtasks">
          {#each visibleSubTasks as subtask (subtask.uuid)}
            <svelte:self {taskStore} task={subtask} {editable} isRootTask={false} />
          {/each}
        </div>
      {/if}
    </svelte:fragment>
  </DraggableListItemAugment>
{/if}

<style>
  .c-task-tree-root-children {
    width: 100%;
  }

  .c-task-tree-item__header {
    display: flex;
    align-items: center;
    padding: 0 var(--ds-spacing-1);
    min-height: var(--ds-spacing-2); /* Ensure consistent height for all task items */
    width: 100%; /* Ensure the header takes full width */

    & :global(.c-task-tree-item__name) {
      flex: 1 1 auto;
      min-width: 0; /* Ensure text truncation works */
      display: flex;
      align-items: center;
      overflow: hidden;
      max-width: 100%;
      width: 100%;
      margin-left: calc(-1 * var(--ds-spacing-1));
    }

    & :global(.c-task-tree-item__name-editable) {
      padding: 0 var(--ds-spacing-1);

      & :global(input) {
        flex: 1 1 auto;
        min-width: 0;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        --base-text-field-input-padding: var(--ds-spacing-1);
      }
    }
  }

  /* Status icon styling */
  .c-task-tree-item__status-cell {
    flex-shrink: 0;
    display: flex;
    justify-content: flex-start;
    margin-right: var(--ds-spacing-1); /* Add spacing between icon and task name */
  }

  /* Styling for cancelled tasks */
  .c-task-tree-item__text--cancelled {
    text-decoration: line-through;
    opacity: 0.7;
  }

  /* Action buttons container - hidden by default */
  .c-task-tree-item__action-buttons {
    display: none; /* Use display: none to prevent space reservation */
    align-items: center;
    gap: var(--ds-spacing-1);
  }

  :global(.c-draggable-list-item) {
    /* Make sure that this focus state only applies to the top level, not the action buttons or sub-actions */
    &:not(:focus-within),
    &:global(:has(.c-task-tree-item__action-buttons:focus-within)),
    &:global(:has(.c-task-tree-item__status-cell:focus-within)),
    &:global(:has(.c-draggable-list-item:focus-within)),
    &:global(:has(.c-draggable-list-item__handle:focus-within)),
    &:global(:has(.c-draggable-list-item__expand-collapse-button:focus-within)) {
      & > &:global(.c-draggable-list-item__contents > .c-task-tree-item__details) {
        height: 0;
        overflow: hidden;
      }
    }

    /* If focusing within, we can show the details */
    &:focus-within:not(:global(:has(.c-draggable-list-item:focus-within))):not(
        :has(.c-task-tree-item__action-buttons:focus-within):not(
            :has(.c-task-tree-item__status-cell:focus-within)
          ):not(:has(.c-draggable-list-item__handle:focus-within)):not(
            :has(.c-draggable-list-item__expand-collapse-button:focus-within)
          )
      ) {
      & > .c-task-tree-item__details {
        height: auto;
      }
    }

    /* Only show action buttons on direct hover, not nested items */
    &:hover:not(:global(:has(.c-draggable-list-item:hover))) {
      & > :global(.c-draggable-list-item__content .c-task-tree-item__action-buttons) {
        display: flex;
      }
    }
  }
</style>
