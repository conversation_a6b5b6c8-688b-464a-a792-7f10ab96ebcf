<script lang="ts">
  import { getContext } from "svelte";
  import { get } from "svelte/store";
  import DotsVertical from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/ellipsis-vertical.svg?component";
  import FileImport from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/file-import.svg?component";
  import FileExport from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/file-export.svg?component";
  import MessagePlus from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/message-plus.svg?component";

  import DropdownMenuAugment from "$common-webviews/src/design-system/components/DropdownMenuAugment";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import SpinnerAugment from "$common-webviews/src/design-system/components/SpinnerAugment.svelte";

  import {
    CurrentConversationTaskStore,
    type ICurrentConversationTaskStore,
  } from "../../../models/task-store";
  import {
    TaskListAction,
    TaskListActionTrigger,
    AgentSessionEventName,
  } from "@augment-internal/sidecar-libs/src/metrics/types";
  import { getTaskProgress } from "../utils/task-status-utils";
  import type { ChatModel } from "../../../models/chat-model";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import { TooltipTriggerOn } from "$common-webviews/src/design-system/_primitives/TooltipAugment/types";

  const taskStore = getContext<ICurrentConversationTaskStore>(CurrentConversationTaskStore.key);
  const { isImportingExporting, rootTask } = taskStore;
  const chatModel = getContext<ChatModel>("chatModel");

  let requestCloseDropdown: (() => void) | undefined = undefined;

  async function exportTasksToMarkdown() {
    if ($isImportingExporting) return;

    // Emit user-triggered exportTaskList event
    const rootTaskValue = get(rootTask);
    const taskProgress = getTaskProgress(rootTaskValue);
    const taskListUsageData = {
      action: TaskListAction.exportTaskList,
      totalTasksCount: taskProgress.total,
      triggeredBy: TaskListActionTrigger.user,
    };

    // Get conversation ID from the latest exchange, fallback to conversation model ID
    const chatHistory = chatModel.currentConversationModel.chatHistory;
    const conversationId =
      chatHistory.length > 0
        ? chatHistory[chatHistory.length - 1].request_id || chatModel.currentConversationModel.id
        : chatModel.currentConversationModel.id;

    chatModel.currentConversationModel.extensionClient.reportAgentSessionEvent({
      eventName: AgentSessionEventName.taskListUsage,
      conversationId,
      eventData: {
        taskListUsageData,
      },
    });

    await taskStore.exportTasksToMarkdown();
    requestCloseDropdown?.();
  }

  async function importTasksFromMarkdown() {
    if ($isImportingExporting) return;

    // Emit user-triggered importTaskList event
    const rootTaskValue = get(rootTask);
    const taskProgress = getTaskProgress(rootTaskValue);
    const taskListUsageData = {
      action: TaskListAction.importTaskList,
      totalTasksCount: taskProgress.total,
      triggeredBy: TaskListActionTrigger.user,
    };

    // Get conversation ID from the latest exchange, fallback to conversation model ID
    const chatHistory2 = chatModel.currentConversationModel.chatHistory;
    const conversationId2 =
      chatHistory2.length > 0
        ? chatHistory2[chatHistory2.length - 1].request_id || chatModel.currentConversationModel.id
        : chatModel.currentConversationModel.id;

    chatModel.currentConversationModel.extensionClient.reportAgentSessionEvent({
      eventName: AgentSessionEventName.taskListUsage,
      conversationId: conversationId2,
      eventData: {
        taskListUsageData,
      },
    });

    await taskStore.importTasksFromMarkdown();
    requestCloseDropdown?.();
  }

  async function continueInNewChat() {
    const rootTaskValue = get(rootTask);
    if (!rootTaskValue) {
      return;
    }

    const currConversationId = chatModel.currentConversationId;
    const newTaskTree = await taskStore.cloneHydratedTask(rootTaskValue);

    // If the conversation has not changed, let's create a new conversation
    if (currConversationId === chatModel.currentConversationId && newTaskTree) {
      await chatModel.setCurrentConversation(undefined, true, { newTaskUuid: newTaskTree.uuid });
    }
    requestCloseDropdown?.();
  }
</script>

<!-- Task list menu dropdown -->
<DropdownMenuAugment.Root bind:requestClose={requestCloseDropdown}>
  <DropdownMenuAugment.Trigger>
    <TextTooltipAugment content="More Actions" triggerOn={[TooltipTriggerOn.Hover]}>
      <IconButtonAugment
        size={1}
        variant="ghost-block"
        color="neutral"
        disabled={$isImportingExporting}
      >
        {#if $isImportingExporting}
          <SpinnerAugment size={1} />
        {:else}
          <DotsVertical />
        {/if}
      </IconButtonAugment>
    </TextTooltipAugment>
  </DropdownMenuAugment.Trigger>
  <DropdownMenuAugment.Content size={1} side="bottom" align="end">
    <!-- Import/Export Section -->
    <DropdownMenuAugment.Label>Import/Export</DropdownMenuAugment.Label>
    <DropdownMenuAugment.Item onSelect={exportTasksToMarkdown} disabled={$isImportingExporting}>
      <FileExport slot="iconLeft" />
      Export to Markdown
    </DropdownMenuAugment.Item>
    <DropdownMenuAugment.Item onSelect={importTasksFromMarkdown} disabled={$isImportingExporting}>
      <FileImport slot="iconLeft" />
      Import from Markdown
    </DropdownMenuAugment.Item>
    <DropdownMenuAugment.Item onSelect={continueInNewChat}>
      <MessagePlus slot="iconLeft" />
      Continue in New Chat
    </DropdownMenuAugment.Item>
  </DropdownMenuAugment.Content>
</DropdownMenuAugment.Root>
