<script lang="ts">
  import { getContext } from "svelte";
  import { get } from "svelte/store";
  import {
    TaskState,
    TaskUpdatedBy,
  } from "@augment-internal/sidecar-libs/src/agent/task/task-types";
  import {
    TaskListAction,
    TaskListActionTrigger,
    AgentSessionEventName,
  } from "@augment-internal/sidecar-libs/src/metrics/types";
  import { getTaskProgress } from "../utils/task-status-utils";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import {
    getTaskStatusColor,
    getTaskStatusIcon,
    getTaskStatusLabel,
  } from "../utils/task-status-utils";
  import type {
    ButtonSize,
    ButtonColor,
  } from "$common-webviews/src/design-system/_primitives/BaseButton.svelte";
  import type { ICurrentConversationTaskStore } from "../../../models/task-store";
  import type { ChatModel } from "../../../models/chat-model";

  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import DropdownMenuAugment from "$common-webviews/src/design-system/components/DropdownMenuAugment";
  import { TooltipTriggerOn } from "$common-webviews/src/design-system/_primitives/TooltipAugment/types";
  import TaskIcon from "./TaskIcon.svelte";

  /** Current state of the task */
  export let taskState: TaskState;

  /** Size of the button */
  export let size: ButtonSize = 1;

  /** Task UUID for interactive functionality */
  export let taskUuid: string | undefined = undefined;

  /** Task store for interactive functionality */
  export let taskStore: ICurrentConversationTaskStore | undefined = undefined;

  /** Whether to enable interactive state transitions */
  export let interactive: boolean = true;

  /** Whether the button is disabled */
  export let disabled: boolean = false;

  // Get chat model from context
  const chatModel = getContext<ChatModel>("chatModel");

  // Derive display properties from the current state
  $: currentIcon = getTaskStatusIcon(taskState);
  $: currentColor = getTaskStatusColor(taskState) as ButtonColor;
  $: tooltip = "Change Status";

  let dropdownOpen = false;

  // Task state change handler
  async function changeTaskState(newState: TaskState) {
    if (!taskUuid || !taskStore || disabled || newState === taskState) return;

    // Emit user-triggered updateTaskStatus event
    const rootTaskValue = get(taskStore.rootTask);
    const taskProgress = getTaskProgress(rootTaskValue);
    const taskListUsageData = {
      action: TaskListAction.updateTaskStatus,
      totalTasksCount: taskProgress.total,
      triggeredBy: TaskListActionTrigger.user,
    };

    // Get conversation ID from the latest exchange, fallback to conversation model ID
    const chatHistory = chatModel.currentConversationModel.chatHistory;
    const conversationId =
      chatHistory.length > 0
        ? chatHistory[chatHistory.length - 1].request_id || chatModel.currentConversationModel.id
        : chatModel.currentConversationModel.id;

    chatModel.currentConversationModel.extensionClient.reportAgentSessionEvent({
      eventName: AgentSessionEventName.taskListUsage,
      conversationId,
      eventData: {
        taskListUsageData,
      },
    });

    await taskStore.updateTask(taskUuid, { state: newState }, TaskUpdatedBy.USER);
    dropdownOpen = false;
  }

  // Get all available task states for the dropdown
  $: taskStates = Object.values(TaskState);
</script>

{#if interactive && taskUuid && taskStore && !disabled}
  <!-- Status dropdown -->
  <DropdownMenuAugment.Root
    triggerOn={[TooltipTriggerOn.Click]}
    open={dropdownOpen}
    onOpenChange={(open) => (dropdownOpen = open)}
  >
    <DropdownMenuAugment.Trigger>
      <TextTooltipAugment content={tooltip} triggerOn={[TooltipTriggerOn.Hover]}>
        <IconButtonAugment
          {size}
          variant="ghost"
          color={currentColor}
          {disabled}
          {...$$restProps}
          on:click={() => (dropdownOpen = !dropdownOpen)}
        >
          <svelte:component this={currentIcon} {size} />
        </IconButtonAugment>
      </TextTooltipAugment>
    </DropdownMenuAugment.Trigger>
    <DropdownMenuAugment.Content size={1} side="bottom" align="start">
      {#each taskStates as state}
        <DropdownMenuAugment.Item
          onSelect={() => changeTaskState(state)}
          highlight={state === taskState}
          disabled={!interactive || state === taskState}
        >
          <TaskIcon slot="iconLeft" taskState={state} size={1} />
          {getTaskStatusLabel(state)}
        </DropdownMenuAugment.Item>
      {/each}
    </DropdownMenuAugment.Content>
  </DropdownMenuAugment.Root>
{:else}
  <IconButtonAugment
    {size}
    variant="ghost"
    color={currentColor}
    {...$$restProps}
    on:click
    on:keyup
    on:keydown
    on:mousedown
    on:mouseover
    on:focus
    on:mouseleave
    on:blur
    on:contextmenu
  >
    <svelte:component this={currentIcon} {size} />
  </IconButtonAugment>
{/if}
