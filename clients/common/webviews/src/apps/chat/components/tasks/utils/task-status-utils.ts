/**
 * @file task-status-utils.ts
 * Utility functions for task status handling
 */

import {
  TaskState,
  type HydratedTask,
} from "@augment-internal/sidecar-libs/src/agent/task/task-types";
import CircleBlank from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/circle.svg?component";
import CircleCheck from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/circle-check.svg?component";
import TimesCircle from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/circle-xmark.svg?component";
import CircleHalfStroke from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/circle-half-stroke.svg?component";

import type { ComponentType, SvelteComponent } from "svelte";

/**
 * Maps a task state to a color for UI components
 * @param taskState The current state of the task
 * @returns A color string that can be used with design system components
 */
export function getTaskStatusColor(
  taskState: TaskState,
): "neutral" | "info" | "success" | "error" | "warning" {
  switch (taskState) {
    case TaskState.IN_PROGRESS:
      return "info";
    case TaskState.COMPLETE:
      return "success";
    case TaskState.CANCELLED:
      return "error";
    case TaskState.NOT_STARTED:
    default:
      return "neutral";
  }
}

/**
 * Maps a task state to a human-readable label
 * @param taskState The current state of the task
 * @returns A string representing the task state
 */
export function getTaskStatusLabel(taskState: TaskState): string {
  switch (taskState) {
    case TaskState.IN_PROGRESS:
      return "In Progress";
    case TaskState.COMPLETE:
      return "Completed";
    case TaskState.CANCELLED:
      return "Cancelled";
    case TaskState.NOT_STARTED:
    default:
      return "Not Started";
  }
}

/**
 * Returns the appropriate SVG component for a given task state
 * @param taskState The current state of the task
 * @returns A Svelte component representing the task state icon
 */
export function getTaskStatusIcon(taskState: TaskState): ComponentType<SvelteComponent> {
  switch (taskState) {
    case TaskState.IN_PROGRESS:
      return CircleHalfStroke;
    case TaskState.COMPLETE:
      return CircleCheck;
    case TaskState.CANCELLED:
      return TimesCircle;
    case TaskState.NOT_STARTED:
    default:
      return CircleBlank;
  }
}

/**
 * Finds the active task using DFS priority:
 * 1. Last leaf task that is in progress
 * 2. If no leaves are in progress, the deepest non-root task that is in progress
 * @param task The root task to search from
 * @returns The active task found, or undefined if none
 */
export function findActiveTask(task: HydratedTask | undefined): HydratedTask | undefined {
  if (!task) {
    return undefined;
  }

  let leafInProgress: HydratedTask | undefined = undefined;
  let deepestInProgress: HydratedTask | undefined = undefined;
  let maxDepth = -1;

  function dfs(currentTask: HydratedTask, depth: number): void {
    // Skip root task (depth 0)
    if (depth > 0 && currentTask.state === TaskState.IN_PROGRESS) {
      const isLeaf = !currentTask.subTasksData || currentTask.subTasksData.length === 0;

      if (isLeaf) {
        // Keep updating to find the last leaf in progress
        leafInProgress = currentTask;
      }

      if (depth >= maxDepth) {
        // Track deepest in-progress task (use >= to get the last one at the same depth)
        deepestInProgress = currentTask;
        maxDepth = depth;
      }
    }

    // Continue DFS to search all subtasks
    if (currentTask.subTasksData && currentTask.subTasksData.length > 0) {
      for (const subtask of currentTask.subTasksData) {
        dfs(subtask, depth + 1);
      }
    }
  }

  dfs(task, 0);

  // Return leaf in progress if found, otherwise deepest in progress
  return leafInProgress || deepestInProgress;
}

/**
 * Calculates task progress as completed/total ratio
 * @param task The root task to calculate progress for
 * @returns Object with completed count and total count (excluding root task)
 */
export function getTaskProgress(task: HydratedTask | undefined): {
  completed: number;
  total: number;
} {
  if (!task) {
    return { completed: 0, total: 0 };
  }

  let completed = 0;
  let total = 0;

  function countTasks(currentTask: HydratedTask, level = 0): void {
    // Skip root task (level 0) from counts
    if (level > 0) {
      total++;
      if (currentTask.state === TaskState.COMPLETE) {
        completed++;
      }
    }

    // Recursively count subtasks
    if (currentTask.subTasksData && currentTask.subTasksData.length > 0) {
      for (const subtask of currentTask.subTasksData) {
        countTasks(subtask, level + 1);
      }
    }
  }

  countTasks(task);
  return { completed, total };
}
