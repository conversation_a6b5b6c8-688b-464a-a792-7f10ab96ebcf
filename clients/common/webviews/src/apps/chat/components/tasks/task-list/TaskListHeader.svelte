<script lang="ts">
  import { getContext } from "svelte";
  import CollapseButtonAugment from "$common-webviews/src/design-system/components/CollapsibleAugment/CollapseButtonAugment.svelte";
  import SwitchModeButton from "../../buttons/SwitchModeButton.svelte";
  import TaskListHeaderActions from "./TaskListHeaderActions.svelte";
  import {
    CurrentConversationTaskStore,
    type ICurrentConversationTaskStore,
  } from "../../../models/task-store";
  import type { ChatModel } from "../../../models/chat-model";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import { TaskState } from "@augment-internal/sidecar-libs/src/agent/task/task-types";
  import {
    TaskListAction,
    TaskListActionTrigger,
    AgentSessionEventName,
  } from "@augment-internal/sidecar-libs/src/metrics/types";
  import { getTaskProgress } from "../utils/task-status-utils";

  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import { TooltipTriggerOn } from "$common-webviews/src/design-system/_primitives/TooltipAugment/types";
  import Play from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/play-augment.svg?component";
  import Plus from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/plus.svg?component";
  import TaskFilterDropdown, {
    type TaskFilter,
    type TaskFilterOption,
  } from "../task-filters/TaskFilterDropdown.svelte";
  import { getTaskStatusLabel } from "../utils/task-status-utils";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import StatusBadgeAugment from "$common-webviews/src/design-system/components/StatusBadgeAugment.svelte";

  // Props
  export let notStartedTasksCount: number = 0;
  export let inProgressTasksCount: number = 0;
  export let completedTasksCount: number = 0;
  export let cancelledTasksCount: number = 0;
  export let collapsed: boolean = false;

  const taskStore = getContext<ICurrentConversationTaskStore>(CurrentConversationTaskStore.key);
  const chatModel = getContext<ChatModel>("chatModel");
  const { rootTask, activeTask, taskProgress } = taskStore;

  async function handleAddNewTask() {
    if ($rootTask) {
      // Emit user-triggered addSubtask event
      const taskProgress = getTaskProgress($rootTask);
      const taskListUsageData = {
        action: TaskListAction.addSubtask,
        totalTasksCount: taskProgress.total,
        triggeredBy: TaskListActionTrigger.user,
      };

      chatModel.currentConversationModel.extensionClient.reportAgentSessionEvent({
        eventName: AgentSessionEventName.taskListUsage,
        conversationId: chatModel.currentConversationModel.id,
        eventData: {
          taskListUsageData,
        },
      });

      const uuid = await taskStore.createTask("", "", $rootTask.uuid);
      // Focus the new task
      setTimeout(() => {
        const taskItem = document.getElementById(`task-${uuid}`);
        const taskName = taskItem?.querySelector(".c-task-tree-item__name-editable");
        const input = taskName?.querySelector("input");
        input?.focus();
      }, 100);
    }
  }

  // Filter state - make it available to parent components - now supports multiple selections
  export let activeTaskFilter: Set<TaskState> = new Set();

  // Check if there are any tasks to determine if "Play all" button should be shown
  $: hasAnyTasks = $rootTask?.subTasksData && $rootTask.subTasksData.length > 0;

  // Create filter options based on task counts (removed "all" option)
  $: taskFilters = [
    {
      value: TaskState.NOT_STARTED as TaskFilter,
      label: getTaskStatusLabel(TaskState.NOT_STARTED),
      count: notStartedTasksCount,
    },
    {
      value: TaskState.IN_PROGRESS as TaskFilter,
      label: getTaskStatusLabel(TaskState.IN_PROGRESS),
      count: inProgressTasksCount,
    },
    {
      value: TaskState.COMPLETE as TaskFilter,
      label: getTaskStatusLabel(TaskState.COMPLETE),
      count: completedTasksCount,
    },
    {
      value: TaskState.CANCELLED as TaskFilter,
      label: getTaskStatusLabel(TaskState.CANCELLED),
      count: cancelledTasksCount,
    },
  ] as TaskFilterOption[];
</script>

<div class="c-task-list-header">
  <div class="c-task-list-header__content">
    <div class="c-task-list-header__collapse-btn">
      <CollapseButtonAugment />
    </div>
    <div class="c-task-list-header__switch-btn">
      <SwitchModeButton />
    </div>
    {#if collapsed}
      <div class="c-task-list-header__status">
        {#if $taskProgress && $taskProgress.total > 0}
          <TextTooltipAugment
            triggerOn={[TooltipTriggerOn.Hover]}
            content="Completed {$taskProgress.completed} of {$taskProgress.total} tasks"
          >
            <StatusBadgeAugment
              color={$taskProgress.completed === $taskProgress.total ? "success" : "info"}
              size={1}
            >
              {$taskProgress.completed}/{$taskProgress.total}
            </StatusBadgeAugment>
          </TextTooltipAugment>
        {/if}
        {#if $activeTask}
          <TextAugment size={1} color="primary" class="c-task-list-header__task-name">
            {$activeTask.name || "Untitled Task"}
          </TextAugment>
        {:else if $taskProgress && $taskProgress.completed === $taskProgress.total && $taskProgress.total > 0}
          <TextAugment size={1} color="primary" class="c-task-list-header__task-name">
            All tasks complete
          </TextAugment>
        {:else if $rootTask && $rootTask.name}
          <TextAugment size={1} color="neutral" class="c-task-list-header__task-name">
            {$rootTask.name}
          </TextAugment>
        {/if}
      </div>
    {/if}
  </div>
  <div class="c-task-list-header__actions">
    {#if hasAnyTasks}
      <TextTooltipAugment content="Run All Tasks" triggerOn={[TooltipTriggerOn.Hover]}>
        <IconButtonAugment
          size={1}
          variant="ghost-block"
          color="neutral"
          disabled={false}
          on:click={() => {
            // Emit user-triggered runAllTasks event
            const taskProgress = getTaskProgress($rootTask);
            const taskListUsageData = {
              action: TaskListAction.runAllTasks,
              totalTasksCount: taskProgress.total,
              triggeredBy: TaskListActionTrigger.user,
            };

            chatModel.currentConversationModel.extensionClient.reportAgentSessionEvent({
              eventName: AgentSessionEventName.taskListUsage,
              conversationId: chatModel.currentConversationModel.id,
              eventData: {
                taskListUsageData,
              },
            });

            taskStore.runAllTasks();
          }}
        >
          <Play />
        </IconButtonAugment>
      </TextTooltipAugment>
    {/if}
    <TextTooltipAugment content="Add New Task" triggerOn={[TooltipTriggerOn.Hover]}>
      <IconButtonAugment
        size={1}
        variant="ghost-block"
        color="neutral"
        disabled={!$rootTask}
        on:click={handleAddNewTask}
      >
        <Plus />
      </IconButtonAugment>
    </TextTooltipAugment>
    <TaskFilterDropdown {taskFilters} bind:activeTaskFilter />

    <TaskListHeaderActions />
  </div>
</div>

<style>
  .c-task-list-header {
    display: flex;
    align-items: center;
    width: 100%;
    min-width: 0;
    max-width: 100%;
    overflow: hidden;
    padding: var(--ds-spacing-1);
    gap: var(--ds-spacing-2);
    justify-content: space-between;

    & :global(.c-task-list-header__task-name) {
      flex: 1;
      min-width: 0;
      max-width: 100%;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      cursor: default;
    }
  }

  .c-task-list-header__content {
    display: flex;
    gap: var(--ds-spacing-1);
    min-width: 0;
    max-width: 100%;
    overflow: hidden;
    flex: 1 1 auto;
    align-items: center;
    justify-content: flex-start;
  }

  .c-task-list-header__collapse-btn,
  .c-task-list-header__switch-btn {
    flex-shrink: 0;
  }

  .c-task-list-header__status {
    display: flex;
    gap: var(--ds-spacing-1);
    align-items: center;
    flex: 1;
    min-width: 0;
    white-space: nowrap;
    overflow: hidden;
    cursor: pointer;
  }

  .c-task-list-header__actions {
    display: flex;
    align-items: center;
    min-width: max-content;
    flex-shrink: 0;
  }
</style>
