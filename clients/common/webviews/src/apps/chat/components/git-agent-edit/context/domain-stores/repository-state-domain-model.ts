/**
 * Repository State Domain Model
 *
 * Slices GitRepositoryState into stable, independently subscribable leaf stores
 * to minimize re-renders. Components observe only the fragments they need.
 *
 * Composition layers:
 *  - metadata: branch, head oid, directories
 *  - files: raw file state list
 *  - operationState: repository-level operation snapshot
 *  - timing: freshness and latency metrics
 */

import { derived, type Readable } from "svelte/store";
import type {
  GitRepositoryState,
  GitFileState,
  GitOperation,
} from "@augment-internal/sidecar-libs/src/git/git-types";

/**
 * Repository metadata information derived from GitRepositoryState
 */
export interface RepositoryMetadata {
  /** Current HEAD commit OID */
  readonly headOid: string;
  /** Current branch name */
  readonly currentBranch: string;
  /** Working directory path */
  readonly workdir: string;
  /** Git directory path */
  readonly gitdir: string;
}

/**
 * Reactive repository metadata domain model interface.
 * Provides individual leaf stores for repository metadata.
 */
export interface IRepositoryMetadataDomainModel {
  /** HEAD commit OID store */
  readonly headOid: Readable<string>;
  /** Current branch name store */
  readonly currentBranch: Readable<string>;
  /** Working directory path store */
  readonly workdir: Readable<string>;
  /** Git directory path store */
  readonly gitdir: Readable<string>;
  /** Combined repository info store */
  readonly repositoryInfo: Readable<RepositoryMetadata>;
}

/**
 * Reactive repository files domain model interface.
 * Provides leaf store for files array from GitRepositoryState.
 */
export interface IRepositoryFilesDomainModel {
  /** Files array store */
  readonly files: Readable<GitFileState[]>;
}

/**
 * Reactive repository operation state domain model interface.
 * Provides leaf stores for repository-level operation state.
 */
export interface IRepositoryOperationStateDomainModel {
  /** Whether any operation is in progress */
  readonly isOperationInProgress: Readable<boolean>;
  /** Current operation details */
  readonly currentOperation: Readable<GitOperation | undefined>;
  /** Repository-level error message */
  readonly repositoryLastError: Readable<string | undefined>;
}

/**
 * Reactive repository timing domain model interface.
 * Provides leaf stores for timing information and derived calculations.
 */
export interface IRepositoryTimingDomainModel {
  /** Last update timestamp */
  readonly lastUpdated: Readable<number>;
  /** Time elapsed since last update */
  readonly timeSinceUpdate: Readable<number>;
  /** Whether data is considered stale */
  readonly isStale: Readable<boolean>;
}

/**
 * Combined interface for all repository state domain models.
 * Provides access to all leaf stores and derived stores.
 */
export interface IRepositoryStateDomainModel {
  readonly isInitialized: Readable<boolean>;
  readonly metadata: IRepositoryMetadataDomainModel;
  readonly files: IRepositoryFilesDomainModel;
  readonly operationState: IRepositoryOperationStateDomainModel;
  readonly timing: IRepositoryTimingDomainModel;
}

/**
 * Creates a reactive repository metadata domain model from repository state.
 * Provides individual leaf stores for each metadata property.
 *
 * @param repositoryState - Readable store containing the git repository state
 * @returns Domain model with individual reactive metadata stores
 */
export function createRepositoryMetadataDomainModel(
  repositoryState: Readable<GitRepositoryState | undefined>,
): IRepositoryMetadataDomainModel {
  // Individual leaf stores for each metadata property
  const headOid = derived(repositoryState, (state) => state?.headOid ?? "");
  const currentBranch = derived(repositoryState, (state) => state?.currentBranch ?? "");
  const workdir = derived(repositoryState, (state) => state?.workdir ?? "");
  const gitdir = derived(repositoryState, (state) => state?.gitdir ?? "");

  // Derived store combining all metadata
  const repositoryInfo = derived(
    [headOid, currentBranch, workdir, gitdir],
    ([headOid, currentBranch, workdir, gitdir]): RepositoryMetadata => ({
      headOid,
      currentBranch,
      workdir,
      gitdir,
    }),
  );

  return {
    headOid,
    currentBranch,
    workdir,
    gitdir,
    repositoryInfo,
  };
}

/**
 * Creates a reactive repository files domain model from repository state.
 * Provides leaf store for the files array.
 *
 * @param repositoryState - Readable store containing the git repository state
 * @returns Domain model with files leaf store
 */
export function createRepositoryFilesDomainModel(
  repositoryState: Readable<GitRepositoryState | undefined>,
): IRepositoryFilesDomainModel {
  // Leaf store for files array
  const files = derived(repositoryState, (state) => state?.files ?? []);

  return {
    files,
  };
}

/**
 * Creates a reactive repository operation state domain model from repository state.
 * Provides individual leaf stores for repository-level operation state.
 *
 * @param repositoryState - Readable store containing the git repository state
 * @returns Domain model with individual reactive operation state stores
 */
export function createRepositoryOperationStateDomainModel(
  repositoryState: Readable<GitRepositoryState | undefined>,
): IRepositoryOperationStateDomainModel {
  // Individual leaf stores for operation state
  const isOperationInProgress = derived(
    repositoryState,
    (state) => state?.isOperationInProgress ?? false,
  );
  const currentOperation = derived(repositoryState, (state) => state?.currentOperation);
  const repositoryLastError = derived(repositoryState, (state) => state?.lastError);

  return {
    isOperationInProgress,
    currentOperation,
    repositoryLastError,
  };
}

/**
 * Creates a reactive repository timing domain model from repository state.
 * Provides leaf stores for timing information and derived calculations.
 *
 * @param repositoryState - Readable store containing the git repository state
 * @returns Domain model with individual reactive timing stores
 */
export function createRepositoryTimingDomainModel(
  repositoryState: Readable<GitRepositoryState | undefined>,
): IRepositoryTimingDomainModel {
  // Leaf store for last updated timestamp
  const lastUpdated = derived(repositoryState, (state) => state?.lastUpdated ?? 0);

  // Derived store for time since last update (in milliseconds)
  const timeSinceUpdate = derived(lastUpdated, (timestamp) => {
    if (timestamp === 0) return 0;
    return Date.now() - timestamp;
  });

  // Derived store for staleness check (consider stale after 5 minutes)
  const isStale = derived(timeSinceUpdate, (timeSince) => timeSince > 5 * 60 * 1000);

  return {
    lastUpdated,
    timeSinceUpdate,
    isStale,
  };
}

/**
 * Creates a comprehensive reactive repository state domain model from repository state.
 * Combines all sub-domain models into a unified interface with leaf stores.
 *
 * @param repositoryState - Readable store containing the git repository state
 * @returns Combined domain model with all repository state leaf stores
 */
export function createRepositoryStateDomainModel(
  repositoryState: Readable<GitRepositoryState | undefined>,
): IRepositoryStateDomainModel {
  // Create sub-domain models
  const metadata = createRepositoryMetadataDomainModel(repositoryState);
  const files = createRepositoryFilesDomainModel(repositoryState);
  const operationState = createRepositoryOperationStateDomainModel(repositoryState);
  const timing = createRepositoryTimingDomainModel(repositoryState);

  // Derived store for initialization status
  const isInitialized = derived(repositoryState, (state) => state !== undefined);

  return {
    isInitialized,
    metadata,
    files,
    operationState,
    timing,
  };
}
