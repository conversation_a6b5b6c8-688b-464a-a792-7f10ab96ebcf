<!--
  Git Repository Card

  Component that displays repository information in a collapsible section.
  Shows repository chips (branch, commit, file counts) and contains sections
  for Unstaged and Staged changes with inline toggles.

  Architecture:
  - Accepts single-repo model and controller as props for composition
  - Uses static context setting for child components
  - Uses CollapsibleAugment for main repository section
  - Uses GitRepoChips for repository metadata display
  - Contains GitSectionChanges and GitSectionStaged with inline toggles
-->
<script lang="ts">
  import { GitAgentEditModel, type IGitAgentEditModel } from "../context/model";
  import type { IGitAgentEditController } from "../context/controller";
  import { MultiRepoGitModel } from "../context/multi-repo";
  import GitRepoChips from "./GitRepoChips.svelte";
  import GitSectionChanges from "./GitSectionChanges.svelte";
  import GitSectionStaged from "./GitSectionStaged.svelte";
  import CollapsibleAugment from "$common-webviews/src/design-system/components/CollapsibleAugment/CollapsibleAugment.svelte";
  import CollapseButtonAugment from "$common-webviews/src/design-system/components/CollapsibleAugment/CollapseButtonAugment.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import { TooltipTriggerOn } from "$common-webviews/src/design-system/_primitives/TooltipAugment/types";
  import RefreshIcon from "$common-webviews/src/design-system/icons/update.svelte";
  import FilePlusMinusIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/file-plus-minus.svg?component";

  // Props - receive the single-repo model and controller directly
  export let model: IGitAgentEditModel;
  export let controller: IGitAgentEditController;
  export let repositoryPath: string;
  export let defaultOpen = true;

  // Set single-repo context statically for child components
  GitAgentEditModel.setInContext(model, controller);

  // Multi-repo context for highlighting and focus handling
  const multiRepoContext = MultiRepoGitModel.getContext();
  $: multiRepoModel = multiRepoContext?.model;
  $: multiRepoController = multiRepoContext?.controller;
  $: activeRepository = multiRepoModel?.activeRepository;
  $: availableRepositories = multiRepoModel?.availableRepositories;

  // Reactive state from the provided single-repo model
  $: repositoryState = model.repositoryState;
  $: unstagedFiles = model.fileCollectionsModel.unstagedFiles;
  $: stagedFiles = model.fileCollectionsModel.stagedFiles;
  $: isGitInitialized = model.repositoryStateModel.isInitialized;
  $: lastError = model.lastError;
  $: isOperationInProgress = model.operationStatusModel.isOperationInProgress;

  // Extract repository info from current state
  $: currentBranch = $repositoryState?.currentBranch;
  $: stagedCount = $stagedFiles?.length || 0;
  $: unstagedCount = $unstagedFiles?.length || 0;
  $: isValid = $isGitInitialized && !$lastError;
  $: error = $lastError;
  $: hasFiles = stagedCount > 0 || unstagedCount > 0;

  // Force expand when there is only one repository
  $: forceExpand = $availableRepositories && $availableRepositories.length === 1;
  $: {
    if (forceExpand && isCollapsed) {
      isCollapsed = false;
    }
  }

  // Track collapsed state for conditional badge display
  let isCollapsed = !defaultOpen;

  // Extract repository name from path
  $: repositoryName = repositoryPath
    ? repositoryPath.split("/").pop() || repositoryPath
    : undefined;

  // Highlighting logic: highlight if more than one repo and this is the active one
  $: shouldHighlight =
    $availableRepositories &&
    $availableRepositories.length > 1 &&
    $activeRepository === repositoryPath;

  // Clickable logic: only make clickable when there are multiple repositories
  $: isClickable = $availableRepositories && $availableRepositories.length > 1;

  // Focus/click handler: switch to this repository when focus enters or clicked
  function handleFocusIn() {
    if (multiRepoController && repositoryPath !== $activeRepository) {
      multiRepoController.selectRepository(repositoryPath);
    }
  }

  function handleClick() {
    if (multiRepoController && repositoryPath !== $activeRepository) {
      multiRepoController.selectRepository(repositoryPath);
    }
  }

  function handleKeyDown(event: KeyboardEvent) {
    if (
      (event.key === "Enter" || event.key === " ") &&
      multiRepoController &&
      repositoryPath !== $activeRepository
    ) {
      event.preventDefault();
      multiRepoController.selectRepository(repositoryPath);
    }
  }

  // Refresh handler
  async function handleRefresh() {
    if (!$isOperationInProgress) {
      try {
        await controller.refreshRepository();
      } catch (error) {
        console.error("Failed to refresh repository:", error);
      }
    }
  }
</script>

<!-- Repository Section -->
<div
  class="c-git-repository-card"
  class:c-git-repository-card--can-highlight={$availableRepositories &&
    $availableRepositories.length > 1}
  class:c-git-repository-card--highlighted={shouldHighlight}
  on:focusin={handleFocusIn}
  on:click={isClickable ? handleClick : undefined}
  on:keydown={isClickable ? handleKeyDown : undefined}
  role={isClickable ? "button" : undefined}
  tabindex="-1"
  aria-label={isClickable ? `Repository ${repositoryName || repositoryPath}` : undefined}
>
  <CollapsibleAugment
    class="c-git-repository-section"
    stickyHeader
    bind:collapsed={isCollapsed}
    {defaultOpen}
  >
    <svelte:fragment slot="header">
      <div class="c-git-repository-section__header">
        <div class="c-git-repository-section__header-left">
          {#if !forceExpand}
            <CollapseButtonAugment />
          {:else}
            <span class="c-git-repository-section__expand-placeholder">
              <FilePlusMinusIcon />
            </span>
          {/if}
          {#if isValid}
            <GitRepoChips
              {repositoryName}
              {currentBranch}
              {stagedCount}
              {unstagedCount}
              {isValid}
              {isCollapsed}
            />
          {:else if error}
            <div class="c-git-repository-section__error">
              <TextAugment size={1} color="error">{error}</TextAugment>
            </div>
          {:else}
            <div class="c-git-repository-section__loading">
              <TextAugment size={1} color="neutral">Loading repository info...</TextAugment>
            </div>
          {/if}
        </div>
        <div class="c-git-repository-section__header-right">
          <TextTooltipAugment
            triggerOn={[TooltipTriggerOn.Hover]}
            content="Sync git state - refresh repository status, branches, and file changes"
          >
            <IconButtonAugment
              variant="ghost-block"
              color="neutral"
              size={1}
              disabled={$isOperationInProgress}
              on:click={handleRefresh}
            >
              <RefreshIcon />
            </IconButtonAugment>
          </TextTooltipAugment>
        </div>
      </div>
    </svelte:fragment>

    <svelte:fragment slot="default">
      {#if isValid && hasFiles}
        <div class="c-git-repository-section__content">
          <!-- Unstaged Changes Section -->
          <GitSectionChanges />

          <!-- Staged Changes Section -->
          <GitSectionStaged />
        </div>
      {:else if isValid && !hasFiles}
        <div class="c-git-repository-section__empty">
          <TextAugment size={1} color="neutral">No changes to review</TextAugment>
        </div>
      {/if}
    </svelte:fragment>
  </CollapsibleAugment>
</div>

<style>
  .c-git-repository-card {
    display: contents;
  }

  /* Highlighted state when active and multiple repos - extracted from nested selector for Svelte 5 compatibility */
  :global(
      .c-git-repository-card.c-git-repository-card--can-highlight.c-git-repository-card--highlighted
        .c-git-repository-section
    ) {
    --collapsible-panel-border-color: var(--ds-color-accent-a5);
    --collapsible-panel-background: var(--ds-color-accent-a3);
  }

  :global(.c-git-repository-card.c-git-repository-card--can-highlight .c-git-repository-section) {
    --collapsible-panel-border-color: var(--ds-color-neutral-a4);
    --collapsible-panel-background: var(--ds-color-neutral-a2);
  }

  /* Clickable cursor when multiple repos are available */
  .c-git-repository-card[role="button"] {
    cursor: pointer;
  }

  .c-git-repository-section__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--ds-spacing-1);
    padding: var(--ds-spacing-1);
  }

  .c-git-repository-section__header-left {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-1);
    flex: 1;
    min-width: 0;
  }

  .c-git-repository-section__header-right {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-1);
    flex-shrink: 0;
  }

  .c-git-repository-section__error,
  .c-git-repository-section__loading {
    display: flex;
    align-items: center;
  }

  .c-git-repository-section__content {
    display: flex;
    flex-direction: column;
  }

  .c-git-repository-section__empty {
    padding: var(--ds-spacing-4);
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .c-git-repository-section__expand-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 24px;
    width: 24px;
  }
</style>
