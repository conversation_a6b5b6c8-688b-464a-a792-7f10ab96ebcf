<!--
  Git Repository Chips

  Component that renders chips for repository information:
  - Current branch
  - Head commit (short)
  - Staged/unstaged file counts
-->
<script lang="ts">
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import BadgeAugment from "$common-webviews/src/design-system/components/BadgeAugment";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import { TooltipTriggerOn } from "$common-webviews/src/design-system/_primitives/TooltipAugment/types";

  import BranchIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/code-branch.svg?component";
  import CheckIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/check.svg?component";
  import MagnifyingGlassIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/magnifying-glass.svg?component";
  import FolderIcon from "$common-webviews/src/design-system/icons/vscode/folder.svelte";

  // Props - repository information
  export let repositoryName: string | undefined = undefined;
  export let currentBranch: string | undefined = undefined;
  export let stagedCount: number = 0;
  export let unstagedCount: number = 0;
  export let isValid: boolean = true;
  export let isCollapsed: boolean = false;

  // Computed values
  $: displayRepositoryName = repositoryName
    ? repositoryName.split("/").pop() || repositoryName
    : undefined;
  $: displayBranch = currentBranch || "unknown";
  $: totalChanges = stagedCount + unstagedCount;
  $: hasChanges = totalChanges > 0;
</script>

<!-- Repository Chips -->
<div class="c-git-repo-chips">
  {#if isValid}
    <!-- Repository Name Chip -->
    {#if displayRepositoryName}
      <TextTooltipAugment
        triggerOn={[TooltipTriggerOn.Hover]}
        content="Repository: {repositoryName || displayRepositoryName}"
      >
        <BadgeAugment.Root variant="soft" color="accent" size={1}>
          <BadgeAugment.IconButton slot="leftButtons">
            <FolderIcon />
          </BadgeAugment.IconButton>
          <TextAugment size={1} weight="medium">
            {displayRepositoryName}
          </TextAugment>
        </BadgeAugment.Root>
      </TextTooltipAugment>
    {/if}

    <!-- Branch Chip -->
    <TextTooltipAugment
      triggerOn={[TooltipTriggerOn.Hover]}
      content="Current branch: {currentBranch || 'unknown'}"
    >
      <BadgeAugment.Root variant="soft" color="neutral" size={1}>
        <BadgeAugment.IconButton slot="leftButtons">
          <BranchIcon />
        </BadgeAugment.IconButton>
        <TextAugment size={1} weight="medium">
          {displayBranch}
        </TextAugment>
      </BadgeAugment.Root>
    </TextTooltipAugment>

    <!-- Status badges - only show when collapsed and there are changes -->
    {#if isCollapsed && hasChanges}
      <!-- Unstaged Changes -->
      {#if unstagedCount > 0}
        <TextTooltipAugment
          triggerOn={[TooltipTriggerOn.Hover]}
          content="{unstagedCount} unstaged {unstagedCount === 1
            ? 'file'
            : 'files'} with changes not yet reviewed"
        >
          <BadgeAugment.Root variant="soft" color="warning" size={1}>
            <BadgeAugment.IconButton slot="leftButtons">
              <MagnifyingGlassIcon />
            </BadgeAugment.IconButton>
            {unstagedCount}
          </BadgeAugment.Root>
        </TextTooltipAugment>
      {/if}

      <!-- Staged Changes -->
      {#if stagedCount > 0}
        <TextTooltipAugment
          triggerOn={[TooltipTriggerOn.Hover]}
          content="{stagedCount} staged {stagedCount === 1
            ? 'file'
            : 'files'} reviewed and ready to commit"
        >
          <BadgeAugment.Root variant="soft" color="success" size={1}>
            <BadgeAugment.IconButton slot="leftButtons">
              <CheckIcon />
            </BadgeAugment.IconButton>
            {stagedCount}
          </BadgeAugment.Root>
        </TextTooltipAugment>
      {/if}
    {:else if !hasChanges}
      <!-- No Changes Display (only show when no changes) -->
      <TextTooltipAugment
        triggerOn={[TooltipTriggerOn.Hover]}
        content="Repository is clean with no staged or unstaged changes"
      >
        <BadgeAugment.Root variant="soft" color="neutral" size={1}>
          <TextAugment size={1} color="neutral">No changes</TextAugment>
        </BadgeAugment.Root>
      </TextTooltipAugment>
    {/if}
  {:else}
    <!-- Invalid Repository -->
    <TextTooltipAugment
      triggerOn={[TooltipTriggerOn.Hover]}
      content="Repository path is invalid or not a git repository"
    >
      <BadgeAugment.Root variant="soft" color="error" size={1}>
        <TextAugment size={1} color="error">Invalid repository</TextAugment>
      </BadgeAugment.Root>
    </TextTooltipAugment>
  {/if}
</div>

<style>
  .c-git-repo-chips {
    display: flex;
    flex-wrap: wrap;
    gap: var(--ds-spacing-1);
    align-items: center;
  }
</style>
