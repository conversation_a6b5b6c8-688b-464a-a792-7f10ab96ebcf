/**
 * Operation Status Domain Model
 *
 * Provides lightweight derivations of GitOperationStatus for UI logic (spinners, disabling, placeholders).
 * Uses const enum for inlineable string values and minimal metadata tracking.
 */

import { derived, type Readable } from "svelte/store";

/**
 * Git operation status enumeration.
 */
export const enum GitOperationStatus {
  idle = "idle",
  initializing = "initializing",
  refreshing = "refreshing",
  staging = "staging",
  unstaging = "unstaging",
  committing = "committing",
  checkingOut = "checkingOut",
}

/**
 * Metadata about the current git operation.
 */
export interface GitOperationMetadata {
  /** Current operation status */
  readonly status: GitOperationStatus;
  /** Timestamp when operation started (undefined for idle) */
  readonly startedAt?: number;
}

/**
 * Reactive operation status domain model interface.
 * Provides individual stores for operation status and metadata.
 */
export interface IOperationStatusDomainModel {
  /** Operation metadata with timing info */
  readonly operationMetadata: Readable<GitOperationMetadata>;
  /** Whether git is idle */
  readonly isIdle: Readable<boolean>;
  /** Whether any operation is in progress */
  readonly isOperationInProgress: Readable<boolean>;
}

/**
 * Creates a reactive operation status domain model from current status.
 * @param currStatus - Readable store containing the current git operation status
 * @returns Domain model with individual reactive operation status stores
 */
export function createOperationStatusDomainModel(
  currStatus: Readable<GitOperationStatus>,
): IOperationStatusDomainModel {
  // Derived store for operation metadata
  const operationMetadata = derived(
    currStatus,
    (status): GitOperationMetadata => ({
      status,
      startedAt: status !== GitOperationStatus.idle ? Date.now() : undefined,
    }),
  );

  return {
    operationMetadata,
    isIdle: derived(currStatus, (status) => status === GitOperationStatus.idle),
    isOperationInProgress: derived(currStatus, (status) => status !== GitOperationStatus.idle),
  };
}
