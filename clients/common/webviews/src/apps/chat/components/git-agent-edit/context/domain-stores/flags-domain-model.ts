/**
 * Git UI Flags Domain Model
 *
 * Creates a reactive domain model for git UI flags that derives from the git operation status.
 * This provides individual reactive stores for each flag, allowing components to subscribe
 * to specific flags without re-rendering when unrelated flags change.
 */

import { derived, type Readable } from "svelte/store";
import { GitOperationStatus } from "./operation-status-domain-model";

/**
 * Reactive git UI flags domain model interface.
 * Each flag is an individually subscribable store.
 */
export interface IGitUIFlagsDomainModel {
  /** Whether files can be staged */
  readonly canStageFiles: Readable<boolean>;
  /** Whether files can be unstaged */
  readonly canUnstageFiles: Readable<boolean>;
  /** Whether individual file operations are allowed */
  readonly canModifyIndividualFiles: Readable<boolean>;
  /** Whether commits can be made */
  readonly canCommit: Readable<boolean>;
  /** Whether repository can be refreshed */
  readonly canRefresh: Readable<boolean>;
  /** Whether files can be reverted */
  readonly canRevertFiles: Readable<boolean>;
}

/**
 * Creates reactive git UI flags domain model from operation status store.
 * @param currStatus - Readable store containing the current git operation status
 * @returns Domain model with individual reactive flag stores
 */
export function createGitUIFlagsDomainModel(
  currStatus: Readable<GitOperationStatus>,
): IGitUIFlagsDomainModel {
  // Derived stores for individual flags
  const canStageFiles = derived(currStatus, (status) => {
    const isIdle = status === GitOperationStatus.idle;
    const isUnstaging = status === GitOperationStatus.unstaging;
    // Allow staging unless we're already staging or doing conflicting operations
    return isIdle || isUnstaging;
  });

  const canUnstageFiles = derived(currStatus, (status) => {
    const isIdle = status === GitOperationStatus.idle;
    const isStaging = status === GitOperationStatus.staging;
    // Allow unstaging unless we're already unstaging or doing conflicting operations
    return isIdle || isStaging;
  });

  const canModifyIndividualFiles = derived(currStatus, (status) => {
    // Allow individual file operations when not doing bulk operations
    return status === GitOperationStatus.idle;
  });

  const canCommit = derived(currStatus, (status) => {
    // Allow commits only when idle
    return status === GitOperationStatus.idle;
  });

  const canRefresh = derived(currStatus, (status) => {
    // Allow refresh when not doing file operations
    return status === GitOperationStatus.idle;
  });

  const canRevertFiles = derived(currStatus, (status) => {
    // Allow reverts when idle
    return status === GitOperationStatus.idle;
  });

  return {
    canStageFiles,
    canUnstageFiles,
    canModifyIndividualFiles,
    canCommit,
    canRefresh,
    canRevertFiles,
  };
}
