# Git Agent Edit Domain Stores & Architecture

This directory implements the git-aware agent edit subsystem using a lean domain‑model pattern and leaf store architecture. It intentionally avoids a monolithic state object. Instead each concern (flags, repository, operations, ui state, file collections) is isolated in a focused domain model that exposes only the minimal reactive surface needed by UI components.

## Goals
- Fast reactive UI updates with minimal re-computation
- Strict separation between read (model) and write (controller) concerns
- Deterministic state transitions guarded by an operation status state machine
- Ergonomic Svelte context access via static helpers on the model class
- Easy testability (each domain model can be unit tested independently)

## Key Concepts
### Leaf Store Architecture
Rather than maintaining a single large `GitRepositoryState` blob plus numerous derived stores, we decompose into leaf stores at creation time (see `createRepositoryStateDomainModel`). Components subscribe only to the specific leafs they need (e.g. `repositoryStateModel.files.files`). This reduces unnecessary invalidations and aligns with the broader architecture guidelines for chat & git features.

### Domain Models
Each domain model is a thin functional wrapper around Svelte stores providing a narrow, intention‑revealing API.
- `flagsModel` – UI enablement booleans derived from operation status (e.g. `canCommit`)
- `operationStatusModel` – guards and helpers around the current `GitOperationStatus`
- `uiStateModel` – transient UI hints, placeholders (commit message placeholder, etc.)
- `repositoryStateModel` – normalized repository state + leaf stores for subsets (files, timestamps, etc.)
- `fileCollectionsModel` – categorized views over repository files (staged / unstaged / with UI metadata)

### Operation Status State Machine
All mutating git actions run through `_executeOperation` which sets a status (e.g. `staging`, `committing`) and reverts to `idle` on completion or error. This guarantees:
1. Serialized operations (no overlapping commits & staging)
2. Consistent enable / disable flags for UI buttons
3. Contextual error messages (prefixing failures with operation context)

### Model vs Controller Split
`GitAgentEditModel` holds reactive state; `GitAgentEditController` (extracted into `controller.ts`) performs side effects & orchestrates mutations via RPC.

Benefits:
- Clear dependency direction (controller depends on model; not vice versa)
- Simplified testing (mock controller or RPC client without touching model internals)
- Smaller files & improved scanability

### Context Access Pattern
Static helpers on `GitAgentEditModel`:
```ts
GitAgentEditModel.setInContext(model, controller);
GitAgentEditModel.requireContext(); // throws if missing
GitAgentEditModel.getContext(); // optional
```
Consumers use `GitAgentEditModel.requireContext()` instead of ad‑hoc exported functions. This centralizes the context key (a unique symbol) and prevents accidental mismatch.

### Commit Flow (UI)
1. User types commit message (always enabled editor, even during init; button disabled via flags)
2. Button / Enter triggers controller `commit()` guarded by status machine
3. Optimistic clear of editor content
4. Controller performs commit RPC then refreshes only tracked file paths
5. State leafs update → UI reflects new staged/unstaged sets

### File Refresh Strategy
We avoid full repository refresh after every operation. Instead `refreshSpecificFiles` updates only the subset touched, filtering out files that no longer exhibit any change (workdir & stage match head). This keeps large repositories responsive.

### Error Handling
Errors are captured, contextualized (`Failed to stage files: <message>`) and stored in `lastError`. UI surfaces the message; user can call `clearError()` which resets `lastError` and (if necessary) forces status back to `idle`.

## Adding New Operations
1. Add enum case to `GitOperationStatus` if it represents a distinct long‑running phase.
2. Implement a controller method that wraps work in `_executeOperation(newStatus, async () => { ... })`.
3. Derive any new enable/disable logic in `createGitUIFlagsDomainModel` instead of scattering boolean checks.

## Extending Domain Models
Prefer adding new derived leafs inside the relevant domain model factory rather than computing them ad hoc inside Svelte components. This keeps presentation components dumb and predictable.

## Testing Recommendations
- Unit test each domain model factory with synthetic store inputs.
- Test controller operations with a mock `IGitRPCClient` capturing parameters & simulating responses.
- For UI tests, assert enablement states by setting operation status store values directly.

## Pending Cleanup
A later task will rename `context/context.ts` to `context/index.ts`. After that rename, ensure all imports are updated and no stale paths remain.

## Summary
This directory embodies a pragmatic, decomposed design: small focused domain models + a status-guarded controller, surfaced via a single model context. This yields predictable UI behavior, simpler tests, and an extensible path for future git features (diff viewing, partial staging, etc.).
