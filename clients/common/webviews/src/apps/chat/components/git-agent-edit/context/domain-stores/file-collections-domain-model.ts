/**
 * File Collections Domain Model
 *
 * Reactive domain model for git file collections and transformations.
 * Provides derived stores for different file states and UI-enhanced collections.
 * Uses leaf stores from repository files domain model for maximum reactivity.
 */

import { derived, type Readable } from "svelte/store";
import type { GitFileState } from "@augment-internal/sidecar-libs/src/git/git-types";
import { GitFileStatus } from "@augment-internal/sidecar-libs/src/git/git-types";
import type { GitFileStateWithUI } from "../../git-repository/GitFileItem.svelte";

/**
 * Reactive file collections domain model interface.
 * Provides individual stores for different file collections.
 */
export interface IFileCollectionsDomainModel {
  /** All files in repository */
  readonly allFiles: Readable<GitFileState[]>;
  /** Files with unstaged changes */
  readonly unstagedFiles: Readable<GitFileState[]>;
  /** Files with staged changes */
  readonly stagedFiles: Readable<GitFileState[]>;
  /** Unstaged files with UI enhancements */
  readonly unstagedFilesWithUI: Readable<GitFileStateWithUI[]>;
  /** Staged files with UI enhancements */
  readonly stagedFilesWithUI: Readable<GitFileStateWithUI[]>;
}

/**
 * Creates reactive file collections domain model from files leaf store.
 * @param files - Readable store containing the git files array (leaf store)
 * @returns Domain model with individual reactive file collection stores
 */
export function createFileCollectionsDomainModel(
  files: Readable<GitFileState[]>,
): IFileCollectionsDomainModel {
  // Base file collections derived from files leaf store
  const allFiles = files;

  const unstagedFiles = derived(allFiles, (files) =>
    files.filter((file) => GitFileStatus.hasUnstaged(file.statusRow)),
  );

  const stagedFiles = derived(allFiles, (files) =>
    files.filter((file) => GitFileStatus.hasStaged(file.statusRow)),
  );

  // UI-enhanced file collections
  const unstagedFilesWithUI = derived(unstagedFiles, (files) =>
    files.map(
      (file): GitFileStateWithUI => ({
        qualifiedPathName: file.qualifiedPathName,
        statusRow: file.statusRow,
        changesSummary: file.changesSummary,
      }),
    ),
  );

  const stagedFilesWithUI = derived(stagedFiles, (files) =>
    files.map(
      (file): GitFileStateWithUI => ({
        qualifiedPathName: file.qualifiedPathName,
        statusRow: file.statusRow,
        changesSummary: file.changesSummary,
      }),
    ),
  );

  return {
    allFiles,
    unstagedFiles,
    stagedFiles,
    unstagedFilesWithUI,
    stagedFilesWithUI,
  };
}
