# Git Agent Edit System

A comprehensive git interface for the Augment chat application, providing real-time git operations with a modern, reactive UI.

## Overview

The git-agent-edit system enables users to manage git repositories directly within the chat interface, offering:

- **Real-time Repository State**: Live updates of file changes, staging status, and repository state
- **Interactive File Management**: Stage and unstage files with visual feedback
- **Bulk Operations**: Stage all or unstage all files with single actions
- **Error Handling**: Handle repository errors gracefully
- **Performance Optimized**: Efficient handling of large repositories with thousands of files

## Architecture

### Component Hierarchy

```
GitAgentEditTab (Main Container)
├── GitSectionChanges (Unstaged Files)
│   ├── GitFileList
│   │   └── GitFileItem (per file)
│   │       └── GitStatusBadge
│   └── GitBulkActionControls
├── GitSectionStaged (Staged Files)
│   ├── GitFileList
│   │   └── GitFileItem (per file)
│   │       └── GitStatusBadge
└── GitBulkActionControls (Global Actions)
```

### Data Flow

```
RPC Client ↔ Sidecar ↔ Git Repository
     ↓
GitAgentEditModel (Svelte Stores)
     ↓
GitAgentEditController (Business Logic)
     ↓
Svelte Components (UI)
```

## Core Components

### GitAgentEditTab
**Purpose**: Main container component that orchestrates the entire git interface
**Features**:
- Context provider for child components
- Layout management and responsive design
- Error boundary and loading states

### GitSectionChanges / GitSectionStaged
**Purpose**: Display and manage unstaged/staged file sections
**Features**:
- File list rendering with virtual scrolling for performance
- Bulk operations (stage all, unstage all)
- Section-specific actions (stage all, unstage all)
- Empty state handling

### GitFileItem
**Purpose**: Individual file display with actions
**Features**:
- File status visualization with color-coded badges
- Individual file actions (stage, unstage, view diff, open)
- File action management
- Accessibility support with proper ARIA labels



### GitStatusBadge
**Purpose**: Visual file status indicator
**Features**:
- Color-coded status display (M, A, D, U, ??, etc.)
- Configurable size and styling
- Tooltip support for status explanations

## State Management

### GitAgentEditModel
Reactive state management using Svelte stores:

```typescript
interface GitAgentEditModel {
  // Core state
  repositoryState: Writable<GitRepositoryState | null>;
  unstagedFiles: Readable<GitFileState[]>;
  stagedFiles: Readable<GitFileState[]>;
  // Computed state
  repositoryStats: Readable<RepositoryStats>;

  // Operation state
  isOperationInProgress: Writable<boolean>;
  lastError: Writable<string | null>;
}
```

### GitAgentEditController
Business logic and operations:

```typescript
interface GitAgentEditController {
  // File operations
  stageFile(filePath: string): Promise<void>;
  unstageFile(filePath: string): Promise<void>;


  // Repository operations
  refreshRepository(): Promise<void>;
}
```

## RPC Communication

### GitRPCClient
Handles communication with the sidecar process:

```typescript
interface IGitRPCClient {
  // Repository queries
  getRepositoryState(): Promise<GitRepositoryState>;
  getStatusMatrix(options?: {
    filepaths?: string[];
    includeStatistics?: boolean;
    filterChangedOnly?: boolean;
  }): Promise<GitFileState[]>;

  // File operations
  stageFiles(request: GitStageFilesRequest): Promise<void>;
  unstageFiles(request: GitUnstageFilesRequest): Promise<void>;
}
```

## Usage Examples

### Basic Integration

```typescript
import { createGitAgentEditContext } from './context';
import GitAgentEditTab from './GitAgentEditTab.svelte';

// Create context with async messaging
const gitContext = createGitAgentEditContext(asyncMsgSender, {
  repositoryPath: '/workspace',
});

// Use in Svelte component
<GitAgentEditTab />
```

### Custom File Actions

```typescript
// Extend controller with custom actions
class CustomGitController extends GitAgentEditController {
  async stageMultipleFiles(filePaths: string[]) {
    await this.stageFiles(filePaths);
    await this.refreshRepository();
  }
}
```

## Performance Considerations

### Large Repositories
- **Virtual Scrolling**: File lists use virtual scrolling for 1000+ files
- **Debounced Updates**: State changes are debounced to prevent excessive re-renders
- **Selective Updates**: Only changed files trigger re-renders

### Memory Management
- **Weak References**: Event listeners use weak references where possible
- **Cleanup**: Proper cleanup of subscriptions and timers
- **Lazy Loading**: Components load data on-demand

### Network Optimization
- **Batched Operations**: Multiple file operations are batched into single RPC calls
- **Caching**: Repository state is cached and updated incrementally
- **Compression**: Large payloads are compressed for network efficiency

## Error Handling

### User-Facing Errors
- **Validation Errors**: Input validation with helpful error messages
- **Operation Failures**: Clear error messages with suggested actions
- **Network Issues**: Retry mechanisms with exponential backoff

### Developer Errors
- **Type Safety**: Full TypeScript coverage prevents runtime type errors
- **Boundary Components**: Error boundaries catch and display component errors
- **Logging**: Comprehensive logging for debugging and monitoring

## Testing

The system includes comprehensive test coverage:

- **Unit Tests**: Business logic and utilities (100% coverage)
- **Component Tests**: Svelte component behavior (100% coverage)
- **Integration Tests**: End-to-end workflows
- **Performance Tests**: Large dataset handling
- **Accessibility Tests**: ARIA compliance and keyboard navigation

See [__tests__/README.md](./__tests__/README.md) for detailed testing documentation.

## Development

### Adding New Components

1. Create component in appropriate directory
2. Add comprehensive tests in `__tests__/components/`
3. Update type definitions in `types.ts`
4. Add to main export in `index.ts`

### Extending Functionality

1. Add new RPC methods to `IGitRPCClient`
2. Implement in `git-rpc-client.ts`
3. Add controller methods in `GitAgentEditController`
4. Update model state if needed
5. Create UI components for new features

### Performance Optimization

1. Profile with large repositories (1000+ files)
2. Use React DevTools Profiler for component analysis
3. Monitor memory usage during long sessions
4. Test network performance with slow connections

## Configuration

### Feature Flags
- `enableAdvancedGitFeatures`: Enable experimental git operations
- `enablePerformanceMode`: Optimize for large repositories
- `enableDebugMode`: Show additional debugging information

### Customization
- **Themes**: Support for light/dark themes
- **Keyboard Shortcuts**: Customizable key bindings
- **File Filters**: Custom file filtering and grouping
- **Action Buttons**: Configurable action button visibility

## Troubleshooting

### Common Issues

**Files not updating**: Check RPC connection and repository permissions
**Performance issues**: Enable performance mode for large repositories
**File actions not working**: Verify file paths match exactly
**Repository errors**: Check git configuration and repository state

### Debug Mode
Enable debug mode to see:
- RPC message logs
- State change events
- Performance metrics
- Error stack traces

## Contributing

1. Follow the established patterns in existing components
2. Add comprehensive tests for new functionality
3. Update documentation for API changes
4. Ensure accessibility compliance
5. Test with large repositories for performance

## Related Documentation

- [Testing Guide](./__tests__/README.md)
- [RPC Protocol](./git-rpc-client.ts)
- [State Management](./model.ts)
- [Component API](./types.ts)
