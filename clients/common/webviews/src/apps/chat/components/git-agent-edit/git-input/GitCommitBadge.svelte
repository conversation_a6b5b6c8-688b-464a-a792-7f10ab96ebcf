<script lang="ts">
  import BadgeAugment from "$common-webviews/src/design-system/components/BadgeAugment";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import CommitIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/code-commit.svg?component";

  // Props
  export let shortCommit: string | undefined = undefined;

  // Computed values
  $: displayCommit = shortCommit || "unknown";
</script>

{#if shortCommit}
  <BadgeAugment.Root variant="soft" color="neutral" size={1}>
    <BadgeAugment.IconButton slot="leftButtons">
      <CommitIcon />
    </BadgeAugment.IconButton>
    <TextAugment size={1} family="mono">
      {displayCommit}
    </TextAugment>
  </BadgeAugment.Root>
{/if}
