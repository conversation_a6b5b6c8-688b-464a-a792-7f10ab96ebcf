/**
 * UI State Domain Model
 *
 * Translates git operation status into human-friendly micro-copy for inputs and status labels.
 */

import { derived, type Readable } from "svelte/store";
import { GitOperationStatus } from "./operation-status-domain-model";

/**
 * UI presentation state derived from GitOperationStatus.
 */
export interface GitUIState {
  /** Placeholder text for commit input field */
  readonly commitPlaceholder: string;
  /** Human-readable description of current operation */
  readonly operationDescription: string;
}

/**
 * Reactive UI state domain model interface.
 * Provides individual stores for UI presentation elements.
 */
export interface IUIStateDomainModel {
  /** Combined UI state store */
  readonly uiState: Readable<GitUIState>;
  /** Commit input placeholder text store */
  readonly commitPlaceholder: Readable<string>;
  /** Operation description text store */
  readonly operationDescription: Readable<string>;
}

/**
 * Creates a reactive UI state domain model from current status.
 * @param currStatus - Readable store containing the current git operation status
 * @returns Domain model with individual reactive UI state stores
 */
export function createUIStateDomainModel(
  currStatus: Readable<GitOperationStatus>,
): IUIStateDomainModel {
  // Derived store for commit placeholder text
  const commitPlaceholder = derived(currStatus, (status): string => {
    switch (status) {
      case GitOperationStatus.committing:
        return "Committing changes...";
      case GitOperationStatus.staging:
        return "Staging files...";
      case GitOperationStatus.unstaging:
        return "Unstaging files...";
      case GitOperationStatus.refreshing:
        return "Refreshing repository...";
      case GitOperationStatus.initializing:
        return "Initializing repository...";
      case GitOperationStatus.checkingOut:
        return "Checking out files...";
      case GitOperationStatus.idle:
      default:
        return "Enter commit message";
    }
  });

  // Derived store for operation description
  const operationDescription = derived(currStatus, (status): string => {
    switch (status) {
      case GitOperationStatus.committing:
        return "Committing changes";
      case GitOperationStatus.staging:
        return "Staging files";
      case GitOperationStatus.unstaging:
        return "Unstaging files";
      case GitOperationStatus.refreshing:
        return "Refreshing repository";
      case GitOperationStatus.initializing:
        return "Initializing repository";
      case GitOperationStatus.checkingOut:
        return "Checking out files";
      case GitOperationStatus.idle:
      default:
        return "Ready";
    }
  });

  // Combined UI state
  const uiState = derived(
    [commitPlaceholder, operationDescription],
    ([placeholder, description]): GitUIState => ({
      commitPlaceholder: placeholder,
      operationDescription: description,
    }),
  );

  return {
    uiState,
    commitPlaceholder,
    operationDescription,
  };
}
