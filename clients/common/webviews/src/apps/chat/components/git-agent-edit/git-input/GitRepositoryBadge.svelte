<script lang="ts">
  import BadgeAugment from "$common-webviews/src/design-system/components/BadgeAugment";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import FolderIcon from "$common-webviews/src/design-system/icons/vscode/folder.svelte";
  import BranchIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/code-branch.svg?component";
  import CommitIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/code-commit.svg?component";

  // Props
  export let repositoryName: string | undefined = undefined;
  export let currentBranch: string | undefined = undefined;
  export let shortCommit: string | undefined = undefined;
  export let onClick: (() => void) | undefined = undefined;

  // Computed values
  $: displayRepositoryName = repositoryName
    ? repositoryName.split("/").pop() || repositoryName
    : "No repository";
  $: displayBranch = currentBranch || "unknown";
  $: displayCommit = shortCommit || "unknown";
</script>

<div class="c-git-repository-badge">
  <div class="c-git-repository-badge__chips">
    <!-- Repository Name Chip -->
    <button
      class="c-git-repository-badge__button"
      class:c-git-repository-badge__button--clickable={!!onClick}
      on:click={onClick}
      disabled={!onClick}
    >
      <BadgeAugment.Root variant="soft" color="accent" size={1}>
        <BadgeAugment.IconButton slot="leftButtons">
          <FolderIcon />
        </BadgeAugment.IconButton>
        <TextAugment size={1} weight="medium">
          {displayRepositoryName}
        </TextAugment>
      </BadgeAugment.Root>
    </button>

    <!-- Branch Chip -->
    {#if currentBranch}
      <BadgeAugment.Root variant="soft" color="neutral" size={1}>
        <BadgeAugment.IconButton slot="leftButtons">
          <BranchIcon />
        </BadgeAugment.IconButton>
        <TextAugment size={1} weight="medium">
          {displayBranch}
        </TextAugment>
      </BadgeAugment.Root>
    {/if}

    <!-- Commit Chip -->
    {#if shortCommit}
      <BadgeAugment.Root variant="soft" color="neutral" size={1}>
        <BadgeAugment.IconButton slot="leftButtons">
          <CommitIcon />
        </BadgeAugment.IconButton>
        <TextAugment size={1} family="mono">
          {displayCommit}
        </TextAugment>
      </BadgeAugment.Root>
    {/if}
  </div>
</div>

<style>
  .c-git-repository-badge {
    display: flex;
    align-items: center;
  }

  .c-git-repository-badge__chips {
    display: flex;
    flex-wrap: wrap;
    gap: var(--ds-spacing-1);
    align-items: center;
  }

  .c-git-repository-badge__button {
    border: none;
    background: none;
    padding: 0;
    cursor: default;
  }

  .c-git-repository-badge__button--clickable {
    cursor: pointer;
  }

  .c-git-repository-badge__button:disabled {
    cursor: default;
  }
</style>
