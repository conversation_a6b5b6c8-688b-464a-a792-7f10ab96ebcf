/**
 * Git Agent Edit Model
 *
 * Read-only reactive state aggregation for git operations in the Chat webview.
 * Provides domain-specific stores and Svelte context management.
 *
 * Architecture:
 *  - Model: Pure reactive read surface (no side-effects)
 *  - Controller: Handles mutations and RPC calls
 *  - Domain models: Focused leaf-store groupings
 *
 * Core responsibilities:
 *  - Owns writable sources (_repositoryState, _lastError, _currStatus)
 *  - Exposes readonly wrappers for components
 *  - Composes domain model factories
 *  - Provides Svelte context access via static methods
 *
 * Extension guidance:
 *  - Add new concerns via domain model factories, not class expansion
 *  - Keep constructor minimal
 *  - Extend _getWritableStores() for new controller write access
 */
import { writable, readonly, type Readable, type Writable } from "svelte/store";
import { getContext, setContext } from "svelte";
import type { GitRepositoryState } from "@augment-internal/sidecar-libs/src/git/git-types";
import {
  createGitUIFlagsDomainModel,
  createFileCollectionsDomainModel,
  createOperationStatusDomainModel,
  createUIStateDomainModel,
  createRepositoryStateDomainModel,
  type IGitUIFlagsDomainModel,
  type IFileCollectionsDomainModel,
  type IOperationStatusDomainModel,
  type IUIStateDomainModel,
  type IRepositoryStateDomainModel,
  GitOperationStatus,
} from "./domain-stores";
import type { IGitAgentEditController } from "./controller";
export { GitOperationStatus } from "./domain-stores";

export interface IGitAgentEditModel {
  /** Current repository state */
  readonly repositoryState: Readable<GitRepositoryState | undefined>;
  /** Last error message */
  readonly lastError: Readable<string | undefined>;
  /** Current operation status */
  readonly currStatus: Readable<GitOperationStatus>;
  /** UI flags domain model */
  readonly flagsModel: IGitUIFlagsDomainModel;
  /** File collections domain model */
  readonly fileCollectionsModel: IFileCollectionsDomainModel;
  /** Operation status domain model */
  readonly operationStatusModel: IOperationStatusDomainModel;
  /** UI state domain model */
  readonly uiStateModel: IUIStateDomainModel;
  /** Repository state domain model */
  readonly repositoryStateModel: IRepositoryStateDomainModel;
}

export class GitAgentEditModel implements IGitAgentEditModel {
  // eslint-disable-next-line @typescript-eslint/naming-convention
  private static readonly CTX_KEY: unique symbol = Symbol("git-agent-edit-context");
  /** Place the model + controller in Svelte context (provider side). */
  static setInContext(model: IGitAgentEditModel, controller: IGitAgentEditController): void {
    setContext(GitAgentEditModel.CTX_KEY, { model, controller });
  }

  /** Retrieve context if inside provider scope; returns undefined otherwise. */
  static getContext():
    | { model: GitAgentEditModel; controller: IGitAgentEditController }
    | undefined {
    return getContext(GitAgentEditModel.CTX_KEY);
  }

  /** Fail-fast accessor for call sites that require the context to exist. */
  static requireContext(): { model: GitAgentEditModel; controller: IGitAgentEditController } {
    const ctx = GitAgentEditModel.getContext();
    if (!ctx) throw new Error("GitAgentEditModel context missing");
    return ctx;
  }

  private _repositoryState: Writable<GitRepositoryState | undefined> = writable(undefined);
  private _lastError: Writable<string | undefined> = writable(undefined);
  private _currStatus: Writable<GitOperationStatus> = writable(GitOperationStatus.idle);

  readonly repositoryState = readonly(this._repositoryState);
  readonly lastError = readonly(this._lastError);
  readonly currStatus = readonly(this._currStatus);

  readonly flagsModel = createGitUIFlagsDomainModel(this.currStatus);
  readonly operationStatusModel = createOperationStatusDomainModel(this.currStatus);
  readonly uiStateModel = createUIStateDomainModel(this.currStatus);
  readonly repositoryStateModel = createRepositoryStateDomainModel(this.repositoryState);
  readonly fileCollectionsModel = createFileCollectionsDomainModel(
    this.repositoryStateModel.files.files,
  );

  /**
   * Internal exposure of writable sources for the controller layer only.
   * Pattern: Keep writes centralized; if you need a new mutation path add it here rather than
   * leaking specific writable references individually.
   */
  _getWritableStores() {
    return {
      repositoryState: this._repositoryState,
      lastError: this._lastError,
      currStatus: this._currStatus,
    };
  }
}
