<!--
  Git File List

  Component for displaying a list of git files with actions.
  Used by both GitSectionChanges and GitSectionStaged.
-->
<script lang="ts">
  import GitFileItem, { type GitFileStateWithUI } from "./GitFileItem.svelte";

  // Props
  export let files: GitFileStateWithUI[] = [];
  export const fileType: "unstaged" | "staged" = "unstaged"; // Used by parent components for identification
  export let showStageAction: boolean = false;
  export let showUnstageAction: boolean = false;
  export let showRevertAction: boolean = false;
  export let onViewDiff: (file: GitFileStateWithUI) => void;
</script>

<!-- Git File List -->
<div class="c-git-file-list">
  {#if files && files.length > 0}
    {#each files as file (`${fileType}-${file.qualifiedPathName.relPath}+${file.changesSummary?.unstagedChanges?.addedLines}-${file.changesSummary?.unstagedChanges?.removedLines}`)}
      <GitFileItem
        {file}
        {showStageAction}
        {showUnstageAction}
        {showRevertAction}
        onViewDiff={() => onViewDiff(file)}
      >
        <slot slot="changeSummary" name="changeSummary" {file} />
      </GitFileItem>
    {/each}
  {:else}
    <div class="c-git-file-list__empty">
      <slot name="empty">
        <!-- Default empty state -->
      </slot>
    </div>
  {/if}
</div>

<style>
  .c-git-file-list {
    display: flex;
    flex-direction: column;
  }

  .c-git-file-list__empty {
    opacity: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: var(--ds-spacing-2);
    /* Disable selection */
    user-select: none;
  }
</style>
