/**
 * Git Agent Edit Context Factory
 *
 * Assembly layer that wires together GitRPCClient, GitAgentEditModel, and GitAgentEditController.
 * Automatically stores both model and controller in Svelte context.
 *
 * Usage:
 *   const { model, controller } = createGitAgentEditContext(extensionClient);
 *   // Components access via GitAgentEditModel.requireContext()
 *
 * Maintains clear dependency direction: view -> (model, controller) -> rpc client
 */
import { GitAgentEditModel } from "./model";
import { GitAgentEditController } from "./controller";
import { GitRPCClient } from "./git-rpc-client";
import type { IExtensionClient } from "../../../extension-client";

export interface GitAgentEditContext {
  /** Reactive state model */
  model: GitAgentEditModel;
  /** Mutation controller */
  controller: GitAgentEditController;
}

/**
 * Creates git agent edit context with model and controller.
 * @param extensionClient - Extension client for RPC operations
 * @returns Context with model and controller
 */
export function createGitAgentEditContext(extensionClient: IExtensionClient): GitAgentEditContext {
  const gitRPCClient = new GitRPCClient(extensionClient);
  const model = new GitAgentEditModel();
  const controller = new GitAgentEditController(model, gitRPCClient);
  GitAgentEditModel.setInContext(model, controller);
  return { model, controller };
}

export { GitAgentEditController };
