<!--
  Git File Item

  Component for displaying individual git file with status and actions.
  Supports staging/unstaging and file operations.
  Redesigned to match the design system with better file path display and change indicators.
-->
<script lang="ts" context="module">
  export type GitFileStateWithUI = {
    qualifiedPathName: {
      rootPath: string;
      relPath: string;
    };
    statusRow: StatusRow;
    changesSummary: ChatAgentFileChangeSummary | undefined;
  };
</script>

<script lang="ts">
  import { GitAgentEditModel } from "../context/model";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";

  import TextCombo from "$common-webviews/src/common/components/TextCombo.svelte";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import { TooltipTriggerOn } from "$common-webviews/src/design-system/_primitives/TooltipAugment/types";
  import { getFileDirectory, getFilename } from "$common-webviews/src/common/utils/file-paths";

  import PlusIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/plus.svg?component";
  import MinusIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/minus.svg?component";
  import TrashIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/trash.svg?component";
  import FilePlusIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/file-plus.svg?component";
  import FileMinusIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/file-minus.svg?component";
  import FilePlusMinusIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/file-plus-minus.svg?component";
  import FileBanIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/file-circle-xmark.svg?component";
  import ArrowUpRightFromSquare from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/arrow-up-right-from-square.svg?component";

  import type { ChatAgentFileChangeSummary } from "$vscode/src/webview-providers/webview-messages";
  import { GitFileStatus } from "@augment-internal/sidecar-libs/src/git/git-types";
  import type { StatusRow } from "@augment-internal/sidecar-libs/src/vcs/iso-git/status-matrix/types";

  // Props
  export let file: GitFileStateWithUI;
  export let onViewDiff: () => void;
  export let showStageAction: boolean = false;
  export let showUnstageAction: boolean = false;
  export let showRevertAction: boolean = false;

  // Get contexts
  const { controller } = GitAgentEditModel.requireContext();

  // Reactive state
  $: filePath = file.qualifiedPathName.relPath;

  // File path parsing
  $: fileName = getFilename(filePath);
  $: directoryPath = getFileDirectory(filePath);

  // Status icon determination based on status row
  function getStatusIcon(statusRow: StatusRow) {
    if (GitFileStatus.isUntracked(statusRow)) {
      return FileBanIcon; // Untracked file - not yet added to git
    }
    if (GitFileStatus.isNew(statusRow) && GitFileStatus.hasStaged(statusRow)) {
      return FilePlusIcon; // New file added
    }
    if (GitFileStatus.isDeleted(statusRow)) {
      return FileMinusIcon; // File deleted
    }
    // Default for modified files
    return FilePlusMinusIcon;
  }

  // Status icon color determination based on status row
  function getStatusIconColor(statusRow: StatusRow): string {
    if (GitFileStatus.isUntracked(statusRow)) {
      return "var(--ds-color-success-9)"; // Green color for untracked (matches VSCode)
    }
    if (GitFileStatus.isNew(statusRow) && GitFileStatus.hasStaged(statusRow)) {
      return "var(--ds-color-success-9)"; // Green for added
    }
    if (GitFileStatus.isDeleted(statusRow)) {
      return "var(--ds-color-error-9)"; // Red for deleted
    }
    return "var(--ds-color-neutral-11)"; // Default for modified
  }

  // Status tooltip content based on file state
  function getStatusTooltip(statusRow: StatusRow): string {
    if (GitFileStatus.isUntracked(statusRow)) {
      if (GitFileStatus.hasUnstaged(statusRow) && showStageAction) {
        return "Untracked file - unstaged changes";
      }
      if (GitFileStatus.hasStaged(statusRow) && showUnstageAction) {
        return "Untracked file - staged for commit";
      }
    }
    if (GitFileStatus.isNew(statusRow)) {
      if (GitFileStatus.hasStaged(statusRow) && showUnstageAction) {
        return "New file - staged for commit";
      }
      if (GitFileStatus.hasUnstaged(statusRow) && showStageAction) {
        return "New file - unstaged changes";
      }
    }
    if (GitFileStatus.isDeleted(statusRow)) {
      if (GitFileStatus.hasStaged(statusRow) && showUnstageAction) {
        return "File deleted - staged for commit";
      }
      if (GitFileStatus.hasUnstaged(statusRow) && showStageAction) {
        return "File deleted - unstaged";
      }
    }
    if (GitFileStatus.hasStaged(statusRow)) {
      return "File modified - staged for commit";
    }
    if (GitFileStatus.hasUnstaged(statusRow)) {
      return "File modified - unstaged changes";
    }
    return "File status";
  }

  $: statusIcon = getStatusIcon(file.statusRow);
  $: statusIconColor = getStatusIconColor(file.statusRow);
  $: statusTooltip = getStatusTooltip(file.statusRow);
  $: isUntracked = GitFileStatus.isUntracked(file.statusRow);

  // Event handlers

  async function handleStageFile() {
    try {
      await controller.stageFile(filePath);
    } catch (error) {
      console.error(`Failed to stage file ${filePath}:`, error);
    }
  }

  async function handleUnstageFile() {
    try {
      await controller.unstageFile(filePath);
    } catch (error) {
      console.error(`Failed to unstage file ${filePath}:`, error);
    }
  }

  function handleOpenFile() {
    void controller.gitRPCClient.openFile(filePath);
  }

  async function handleRevertFile() {
    try {
      await controller.resetToIndexFile(filePath);
    } catch (error) {
      console.error(`Failed to reset file ${filePath}:`, error);
    }
  }
</script>

<!-- Git File Item -->
<div class="c-git-file-item" class:c-git-file-item--untracked={isUntracked}>
  <ButtonAugment
    variant="ghost"
    color="neutral"
    size={1}
    alignment="left"
    class="c-git-file-item__file-button"
    on:click={onViewDiff}
  >
    <!-- Status icon as left icon with tooltip -->
    <svelte:fragment slot="iconLeft">
      {#if statusIcon}
        <TextTooltipAugment triggerOn={[TooltipTriggerOn.Hover]} content={statusTooltip}>
          <span style="color: {statusIconColor}">
            <svelte:component this={statusIcon} />
          </span>
        </TextTooltipAugment>
      {/if}
    </svelte:fragment>

    <!-- File path container -->
    <TextTooltipAugment
      class="c-git-file-item__file-tooltip"
      triggerOn={[TooltipTriggerOn.Hover]}
      content={isUntracked ? "View Diff • Untracked file" : "View Diff"}
    >
      <TextCombo shrink size={1} greyTextTruncateDirection="left">
        <svelte:fragment slot="text">{fileName}</svelte:fragment>
        <!-- Directory path (not clickable) -->
        <svelte:fragment slot="grayText">{directoryPath || ""}</svelte:fragment>
      </TextCombo>
    </TextTooltipAugment>

    <!-- Change summary badges -->
    <slot name="changeSummary" />
  </ButtonAugment>

  <!-- All action buttons grouped together on the right -->
  <div class="c-git-file-item__buttons">
    <!-- Go to file action -->
    <TextTooltipAugment triggerOn={[TooltipTriggerOn.Hover]} content="Open file">
      <IconButtonAugment variant="ghost" size={1} on:click={handleOpenFile}>
        <ArrowUpRightFromSquare />
      </IconButtonAugment>
    </TextTooltipAugment>

    <!-- Revert action (discard changes) -->
    {#if showRevertAction}
      <TextTooltipAugment
        triggerOn={[TooltipTriggerOn.Hover]}
        content="Reset to staged state (or HEAD if not staged)"
      >
        <IconButtonAugment variant="ghost" size={1} on:click={handleRevertFile}>
          <TrashIcon />
        </IconButtonAugment>
      </TextTooltipAugment>
    {/if}

    <!-- Stage action -->
    {#if showStageAction}
      <TextTooltipAugment
        triggerOn={[TooltipTriggerOn.Hover]}
        content="Stage file: Mark changes as reviewed, approved, and ready to commit"
      >
        <IconButtonAugment variant="ghost" size={1} on:click={handleStageFile}>
          <PlusIcon />
        </IconButtonAugment>
      </TextTooltipAugment>
    {/if}

    <!-- Unstage action -->
    {#if showUnstageAction}
      <TextTooltipAugment
        triggerOn={[TooltipTriggerOn.Hover]}
        content="Unstage file: Mark changes as not ready to commit"
      >
        <IconButtonAugment variant="ghost" size={1} on:click={handleUnstageFile}>
          <MinusIcon />
        </IconButtonAugment>
      </TextTooltipAugment>
    {/if}
  </div>
</div>

<style>
  .c-git-file-item {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-1);
    border-radius: var(--ds-radius-1);
    transition:
      background-color 0.1s ease,
      opacity 0.1s ease;
    min-width: 0; /* Allow shrinking */
  }

  .c-git-file-item:hover {
    background-color: var(--ds-color-surface-neutral-subtle);
  }

  /* Untracked files styling - lower opacity for visual distinction while still clickable */
  .c-git-file-item--untracked {
    opacity: 0.7;
  }

  .c-git-file-item--untracked:hover {
    opacity: 0.85;
  }

  /* File button styling - extracted from nested selector for Svelte 5 compatibility */
  :global(.c-git-file-item .c-git-file-item__file-button) {
    min-width: 0; /* Allow shrinking */
    overflow-x: hidden;
    flex-grow: 1;
    flex-shrink: 1;
  }

  /* Button content styling - extracted from deeply nested selector for Svelte 5 compatibility */
  :global(.c-git-file-item .c-git-file-item__file-button .c-button--content),
  :global(.c-git-file-item .c-git-file-item__file-button .c-button--text),
  :global(.c-git-file-item .c-git-file-item__file-button .c-git-file-item__file-tooltip),
  :global(.c-git-file-item .c-git-file-item__file-button .c-text) {
    display: flex;
    align-items: center;
    min-width: 0; /* Allow shrinking */
    flex-shrink: 1;
  }

  /* GitChangeSummary - fixed width badges */
  :global(.c-git-file-item .c-git-change-summary) {
    margin-left: var(--ds-spacing-1);
    flex-shrink: 0;
  }

  /* Action buttons container - fixed width on right */
  .c-git-file-item__buttons {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-1);
    flex-shrink: 0;
    margin-left: auto; /* Push to right side */
  }
</style>
