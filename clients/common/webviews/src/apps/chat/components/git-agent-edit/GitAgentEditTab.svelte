<!--
  Git Agent Edit Tab

  Main tab component for git-based agent edit system.
  Replaces AgentEditListTab with git-aware interface.
-->
<script lang="ts">
  import { getContext, onMount } from "svelte";
  import { MultiRepoGitModel } from "./context/multi-repo";
  import GitRepositoryCard from "./git-repository/GitRepositoryCard.svelte";

  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import type { AgentConversationModel } from "../../models/agent-conversation-model";
  import type { ChatFlagsModel } from "../../models/chat-flags-model";

  // Get required context models
  const agentConversationModel = getContext<AgentConversationModel>("agentConversationModel");
  const flagsModel = getContext<ChatFlagsModel>("flagsModel");

  // Extract required reactive properties
  const { isCurrConversationAgentic } = agentConversationModel;

  // Get git context that was created at the Chat level
  const gitContext = MultiRepoGitModel.requireContext();

  // Reactive state from git model
  $: multiRepoModel = gitContext.model;
  $: multiRepoController = gitContext.controller;
  $: availableRepositories = multiRepoModel?.availableRepositories;
  $: firstRepository = $availableRepositories?.[0];
  $: repositoryStateModel = firstRepository
    ? multiRepoModel?.getRepositoryStateModel(firstRepository)
    : undefined;
  $: isGitInitialized = repositoryStateModel?.isInitialized || { subscribe: () => () => {} };
  $: lastError = multiRepoModel?.lastError;
  $: repoStates = multiRepoModel?.repositories;

  // Check if git functionality is enabled
  $: isGitEnabled = flagsModel.enableAgentGitTracker;

  // Determine if we should show the git interface
  $: shouldShowGitInterface = isGitEnabled && $isCurrConversationAgentic && $isGitInitialized;

  // List repositories when tab becomes active
  onMount(async () => {
    if (isGitEnabled && $isCurrConversationAgentic) {
      try {
        await multiRepoController.listRepositories();
      } catch (error) {
        console.error("Failed to list repositories:", error);
      }
    }
  });
</script>

<!-- Git Agent Edit Tab - git-based interface -->
{#if !isGitEnabled}
  <div class="c-git-agent-edit-tab c-git-agent-edit-tab--disabled">
    <TextAugment size={1} color="neutral">Git tracking is disabled</TextAugment>
  </div>
{:else if $isCurrConversationAgentic}
  {#if !$isGitInitialized}
    <div class="c-git-agent-edit-tab c-git-agent-edit-tab--loading">
      <TextAugment size={1} color="neutral">Initializing git interface...</TextAugment>
    </div>
  {:else if $lastError}
    <div class="c-git-agent-edit-tab c-git-agent-edit-tab--error">
      <TextAugment size={1} color="error">Error: {$lastError}</TextAugment>
    </div>
  {:else if shouldShowGitInterface && $availableRepositories && $availableRepositories.length > 0}
    <div class="c-git-agent-edit-tab">
      <div class="c-git-repository-container">
        {#each $availableRepositories as repositoryPath (repositoryPath)}
          {@const singleRepoModel = $repoStates?.get(repositoryPath)}
          {@const singleRepoController =
            multiRepoController?.getRepositoryController(repositoryPath)}
          {#if singleRepoModel && singleRepoController}
            {#key repositoryPath}
              <GitRepositoryCard
                model={singleRepoModel}
                controller={singleRepoController}
                {repositoryPath}
                defaultOpen={repositoryPath === $availableRepositories[0]}
              />
            {/key}
          {/if}
        {/each}
      </div>
    </div>
  {:else}
    <div class="c-git-agent-edit-tab c-git-agent-edit-tab--unavailable">
      <TextAugment size={1} color="neutral">Git interface not available</TextAugment>
    </div>
  {/if}
{:else}
  <div class="c-git-agent-edit-tab c-git-agent-edit-tab--unavailable">
    <TextAugment size={1} color="neutral"
      >Git interface only available in agent conversations</TextAugment
    >
  </div>
{/if}

<style>
  .c-git-agent-edit-tab {
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: auto;
    min-width: 0; /* Allow shrinking */
  }

  .c-git-agent-edit-tab--loading,
  .c-git-agent-edit-tab--error,
  .c-git-agent-edit-tab--unavailable {
    opacity: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: var(--ds-spacing-2);
    /* Disable selection */
    user-select: none;
  }

  .c-git-repository-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-2);
    overflow: auto;
  }
</style>
