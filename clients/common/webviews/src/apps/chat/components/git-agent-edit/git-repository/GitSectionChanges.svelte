<!--
  Git Section Changes

  Component for displaying unstaged changes section.
  Shows files that have been modified but not yet staged.
  Redesigned to use CollapsibleAugment for better UX.
-->
<script lang="ts">
  import { GitAgentEditModel } from "../context/model";
  import GitFileList from "./GitFileList.svelte";
  import GitSectionSummary from "./GitSectionSummary.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";

  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import { TooltipTriggerOn } from "$common-webviews/src/design-system/_primitives/TooltipAugment/types";
  import CollapsibleAugment from "$common-webviews/src/design-system/components/CollapsibleAugment/CollapsibleAugment.svelte";
  import CollapseButtonAugment from "$common-webviews/src/design-system/components/CollapsibleAugment/CollapseButtonAugment.svelte";
  import PlusIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/plus.svg?component";
  import TrashIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/trash.svg?component";
  import ChevronDown from "$common-webviews/src/design-system/icons/chevron-down.svelte";
  import GitChangeSummary from "./GitChangeSummary.svelte";

  // Get single-repo git context (set by GitRepositoryCard)
  const { model, controller } = GitAgentEditModel.requireContext();

  // Reactive state from single-repo model
  $: unstagedFiles = model.fileCollectionsModel.unstagedFiles;
  $: unstagedFilesWithUI = model.fileCollectionsModel.unstagedFilesWithUI;

  // Event handlers
  async function handleStageAll() {
    if (!controller) return;
    try {
      await controller.stageAllUnstaged();
    } catch (error) {
      console.error("Failed to stage all unstaged files:", error);
    }
  }

  async function handleRevertAll() {
    if (!controller || !$unstagedFiles) return;
    try {
      const filePaths = $unstagedFiles.map((file: any) => file.qualifiedPathName.relPath);
      await controller.resetToIndexFiles(filePaths);
    } catch (error) {
      console.error("Failed to reset all unstaged files:", error);
    }
  }

  // Determine if we have unstaged files
  $: hasUnstagedFiles = ($unstagedFiles?.length ?? 0) > 0;
</script>

<!-- Unstaged Changes Section -->
{#if hasUnstagedFiles}
  <div class="c-git-section-changes c-git-section-changes__wrapper">
    <CollapsibleAugment>
      <svelte:fragment slot="header">
        <div class="c-git-section-changes__header">
          <div class="c-git-section-changes__header-left">
            <CollapseButtonAugment>
              <svelte:fragment let:collapsed>
                <div
                  class="c-expand-toggle-button"
                  class:c-expand-toggle-button--active={!collapsed}
                >
                  <ChevronDown />
                </div>
              </svelte:fragment>
            </CollapseButtonAugment>
            <TextTooltipAugment
              triggerOn={[TooltipTriggerOn.Hover]}
              content="Unstaged files: Files with changes that are not queued to commit to git"
            >
              <TextAugment size={1} weight="bold" class="c-git-section-changes__title">
                Unreviewed
              </TextAugment>
            </TextTooltipAugment>

            <!-- Summary of changes -->
            <GitSectionSummary files={$unstagedFiles} changeType="unstaged" />
          </div>
          <div class="c-git-section-changes__controls">
            <TextTooltipAugment
              triggerOn={[TooltipTriggerOn.Hover]}
              content="Reset all to staged state (or HEAD if not staged)"
            >
              <IconButtonAugment variant="ghost" size={1} on:click={handleRevertAll}>
                <TrashIcon />
              </IconButtonAugment>
            </TextTooltipAugment>
            <TextTooltipAugment
              triggerOn={[TooltipTriggerOn.Hover]}
              content="Stage files: Mark all changes as reviewed, approved, and ready to commit"
            >
              <IconButtonAugment variant="ghost" size={1} on:click={handleStageAll}>
                <PlusIcon />
              </IconButtonAugment>
            </TextTooltipAugment>
          </div>
        </div>
      </svelte:fragment>

      <!-- File List Content -->
      <div class="c-git-section-changes__content">
        <GitFileList
          files={$unstagedFilesWithUI}
          showStageAction={true}
          showUnstageAction={false}
          showRevertAction={true}
          onViewDiff={(file) => controller?.openGitDiff(file.qualifiedPathName.relPath, false)}
        >
          <GitChangeSummary
            slot="changeSummary"
            let:file
            addedLines={file.changesSummary?.unstagedChanges?.addedLines || 0}
            removedLines={file.changesSummary?.unstagedChanges?.removedLines || 0}
          />
        </GitFileList>
      </div>
    </CollapsibleAugment>
  </div>
{/if}

<style>
  .c-git-section-changes.c-git-section-changes__wrapper {
    display: contents;
  }

  /* Collapsible styling - extracted from nested selector for Svelte 5 compatibility */
  :global(.c-git-section-changes.c-git-section-changes__wrapper .c-collapsible) {
    --collapsible-panel-background: transparent;
    --collapsible-panel-border: none;
    --collapsible-panel-radius: 0;
    --collapsible-panel-border-color: none;
  }

  /* Expand toggle button styling - extracted from nested selector for Svelte 5 compatibility */
  :global(.c-git-section-changes .c-expand-toggle-button svg) {
    width: 14px;
    height: 14px;
    transform: rotate(-90deg);
    transition: transform 0.2s ease;
  }

  :global(.c-git-section-changes .c-expand-toggle-button--active svg) {
    transform: rotate(0deg);
  }

  .c-git-section-changes__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--ds-spacing-1);
    padding: var(--ds-spacing-1);
    width: 100%;
  }

  .c-git-section-changes__header-left {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-1);
    flex: 1;
    min-width: 0;
  }

  .c-git-section-changes__header :global(.c-git-section-changes__title) {
    flex: 1;
    min-width: 0;
  }

  .c-git-section-changes__controls {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-1);
    flex-shrink: 0;
  }

  .c-git-section-changes__content {
    padding: var(--ds-spacing-1);
  }
</style>
