<!--
  Git Section Summary

  Component for displaying aggregated statistics for a git section (staged/unstaged).
  Shows total files, lines added, and lines removed in a badge-like display with detailed tooltip.
-->
<script lang="ts">
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import BadgeAugment from "$common-webviews/src/design-system/components/BadgeAugment";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import { TooltipTriggerOn } from "$common-webviews/src/design-system/_primitives/TooltipAugment/types";
  import CheckIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/check.svg?component";
  import MagnifyingGlassIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/magnifying-glass.svg?component";

  import type { GitFileState } from "@augment-internal/sidecar-libs/src/git/git-types";

  // Props
  export let files: GitFileState[] | undefined = undefined;
  export let changeType: "staged" | "unstaged" = "unstaged";

  // Computed totals
  $: totalFiles = files?.length || 0;
  $: totalAddedLines =
    files?.reduce((total, file) => {
      const changes =
        changeType === "staged"
          ? file.changesSummary?.stagedChanges
          : file.changesSummary?.unstagedChanges;
      return total + (changes?.addedLines || 0);
    }, 0) || 0;
  $: totalRemovedLines =
    files?.reduce((total, file) => {
      const changes =
        changeType === "staged"
          ? file.changesSummary?.stagedChanges
          : file.changesSummary?.unstagedChanges;
      return total + (changes?.removedLines || 0);
    }, 0) || 0;

  $: fileText = totalFiles === 1 ? "file" : "files";

  // Icon and color based on change type
  $: sectionIcon = changeType === "staged" ? CheckIcon : MagnifyingGlassIcon;
  $: sectionColor = changeType === "staged" ? ("success" as const) : ("warning" as const);
  $: sectionTooltipText =
    changeType === "staged" ? "reviewed and ready to commit" : "with changes not yet reviewed";
</script>

<!-- Git Section Summary -->
{#if totalFiles > 0}
  <div class="c-git-section-summary">
    <!-- File Count Badge -->
    <TextTooltipAugment
      triggerOn={[TooltipTriggerOn.Hover]}
      content="{totalFiles} {fileText} {sectionTooltipText}"
    >
      <BadgeAugment.Root variant="soft" color={sectionColor} size={1}>
        <BadgeAugment.IconButton slot="leftButtons">
          <svelte:component this={sectionIcon} />
        </BadgeAugment.IconButton>
        <TextAugment size={1} weight="medium">
          {totalFiles}
        </TextAugment>
      </BadgeAugment.Root>
    </TextTooltipAugment>

    <!-- Lines Added Badge -->
    {#if totalAddedLines > 0}
      <TextTooltipAugment
        triggerOn={[TooltipTriggerOn.Hover]}
        content="{totalAddedLines} lines added across all files"
      >
        <BadgeAugment.Root variant="soft" color="success" size={1}>
          <TextAugment size={1} weight="medium">
            +{totalAddedLines}
          </TextAugment>
        </BadgeAugment.Root>
      </TextTooltipAugment>
    {/if}

    <!-- Lines Removed Badge -->
    {#if totalRemovedLines > 0}
      <TextTooltipAugment
        triggerOn={[TooltipTriggerOn.Hover]}
        content="{totalRemovedLines} lines removed across all files"
      >
        <BadgeAugment.Root variant="soft" color="error" size={1}>
          <TextAugment size={1} weight="medium">
            -{totalRemovedLines}
          </TextAugment>
        </BadgeAugment.Root>
      </TextTooltipAugment>
    {/if}
  </div>
{/if}

<style>
  .c-git-section-summary {
    display: flex;
    flex-wrap: wrap;
    gap: var(--ds-spacing-1);
    align-items: center;
  }
</style>
