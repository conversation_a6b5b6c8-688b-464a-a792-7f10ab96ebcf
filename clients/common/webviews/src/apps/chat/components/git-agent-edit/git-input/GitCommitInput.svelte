<script lang="ts">
  import RichTextEditorAugment from "$common-webviews/src/design-system/components/RichTextEditorAugment";
  import Placeholder from "$common-webviews/src/design-system/components/RichTextEditorAugment/plugins/Placeholder";
  import Keybindings from "$common-webviews/src/design-system/components/RichTextEditorAugment/plugins/Keybindings/Keybindings.svelte";
  import type RichTextEditorRoot from "$common-webviews/src/design-system/components/RichTextEditorAugment/Root.svelte";
  import type { ContentData } from "$common-webviews/src/design-system/components/RichTextEditorAugment/types";

  import type { Command } from "prosemirror-state";
  import { GitAgentEditModel, GitOperationStatus } from "../context/model";
  import { MultiRepoGitModel } from "../context/multi-repo";
  import GitRepositorySelector from "./GitRepositorySelector.svelte";
  import GitBranchBadge from "./GitBranchBadge.svelte";
  import GitCommitActions from "./GitCommitActions.svelte";
  import InputActionBar from "$common-webviews/src/apps/chat/components/InputActionBar";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import SparklesIcon from "$common-webviews/src/design-system/icons/sparkles.svelte";
  import { getExtensionClientContext } from "../../../extension-client-context";

  // Props
  export let onCommit: (message: string) => void | Promise<void>;
  export let variant: "solid" | "soft" = "solid";
  export let stagedFilesCount: number = 0;
  export let isCommitting: boolean = false;
  export let canCommit: boolean = true;
  export let placeholder: string = "Write or generate a commit message...";

  // Extension client for commit message generation
  const extensionClient = getExtensionClientContext();

  // Try to get multi-repo context first, then fall back to single-repo context
  let contextModel: any = undefined;
  let multiRepoContext: any = undefined;

  try {
    multiRepoContext = MultiRepoGitModel.getContext();
  } catch {
    // No multi-repo context available
  }

  if (!multiRepoContext) {
    try {
      const context = GitAgentEditModel.getContext();
      contextModel = context?.model;
    } catch {
      // No context available - use props only
    }
  }

  // Use context values if available, otherwise fall back to props
  $: activeRepoModel = multiRepoContext?.model?.activeRepositoryModel;
  $: actualContextModel = $activeRepoModel || contextModel;
  $: currStatus = actualContextModel?.currStatus;
  $: stagedFiles = actualContextModel?.fileCollectionsModel.stagedFiles;
  $: canCommitFromContext = actualContextModel?.flagsModel.canCommit;

  // Determine final values
  $: finalStagedFilesCount = $stagedFiles?.length ?? stagedFilesCount;
  $: finalCanCommit = $canCommitFromContext ?? canCommit;
  $: finalIsCommitting = $currStatus === GitOperationStatus.committing || isCommitting;

  // Dynamic placeholder text
  $: dynamicPlaceholder = isGenerating ? "Generating commit..." : placeholder;

  // Multi-repo specific state
  $: multiRepoModel = multiRepoContext?.model;
  $: activeRepository = multiRepoModel?.activeRepository;
  $: availableRepositories = multiRepoModel?.availableRepositories;
  $: repositories = multiRepoModel?.repositories;
  $: activeRepositoryMetadata = multiRepoModel?.activeRepositoryMetadata;
  $: controller = multiRepoContext?.controller;

  // Editor state
  let editorRoot: RichTextEditorRoot;
  let editorContent: ContentData = { richTextJsonRepr: { type: "doc", content: [] }, rawText: "" };
  let contentValue: string = ""; // Content value for the RichTextEditorAugment.Content component

  // Generate commit message state
  let isGenerating = false;

  // Handle content changes from the editor
  function handleContentChanged(newContent: ContentData) {
    editorContent = newContent;
    // Keep contentValue in sync with the editor content
    contentValue = newContent.rawText;
  }

  // Handle commit
  async function handleCommit() {
    // Use the same gating logic as the button and keyboard shortcut
    if (!canActuallyCommit) return;

    const message = extractTextFromContent(editorContent);
    try {
      await onCommit(message);
      // Clear editor on successful commit by clearing the content value
      contentValue = "";
    } catch (error) {
      console.error("Commit failed:", error);
    }
  }

  // Handle repository selection
  let isSelectingRepository = false;
  async function handleSelectRepository(repositoryPath: string) {
    if (!controller || isSelectingRepository) return;

    isSelectingRepository = true;
    try {
      await controller.selectRepository(repositoryPath);
    } catch (error) {
      console.error("Failed to select repository:", error);
    } finally {
      isSelectingRepository = false;
    }
  }

  // Handle generate commit message
  async function handleGenerateCommitMessage() {
    // Gate the operation: don't generate if already generating or git is busy
    if (isGenerating || $currStatus !== GitOperationStatus.idle) return;

    isGenerating = true;
    try {
      let accumulatedMessage = "";

      // Use the extension client's generateCommitMessage async generator
      for await (const chunk of extensionClient.generateCommitMessage()) {
        if (chunk.response_text) {
          accumulatedMessage += chunk.response_text;
        }
      }

      // Clean and set the message if we got content
      const cleanMessage = accumulatedMessage.trim();
      if (cleanMessage) {
        // Set the content in the rich text editor
        contentValue = cleanMessage;
      }
    } catch (error) {
      console.error("Failed to generate commit message:", error);
    } finally {
      isGenerating = false;
    }
  }

  // Extract text from editor content
  function extractTextFromContent(content: ContentData): string {
    return content.rawText || "";
  }

  // Computed enablement state (consistent with GitCommitActions)
  $: hasCommitMessage = extractTextFromContent(editorContent).trim().length > 0;
  $: canActuallyCommit =
    finalCanCommit && finalStagedFilesCount > 0 && hasCommitMessage && !finalIsCommitting;

  // Keyboard shortcuts
  function createCommitCommand(): Command {
    return () => {
      if (canActuallyCommit) {
        handleCommit();
        return true;
      }
      return false;
    };
  }
</script>

<div class="c-chat-input">
  <RichTextEditorAugment.Root bind:this={editorRoot} editable>
    <RichTextEditorAugment.Content content={contentValue} onContentChanged={handleContentChanged} />
    <Placeholder placeholder={dynamicPlaceholder} />
    <Keybindings
      shortcuts={{
        // eslint-disable-next-line @typescript-eslint/naming-convention
        "Mod-Enter": createCommitCommand(),
      }}
    />

    <div slot="footer">
      <InputActionBar.Root>
        <svelte:fragment slot="leftAlign">
          {#if multiRepoContext && $availableRepositories && $availableRepositories.length > 0}
            <div class="c-git-commit-input__badges">
              <GitRepositorySelector
                activeRepository={$activeRepository}
                availableRepositories={$availableRepositories}
                repositoryStates={$repositories || new Map()}
                onSelectRepository={handleSelectRepository}
              />
              <GitBranchBadge currentBranch={$activeRepositoryMetadata?.currentBranch} />
            </div>
          {/if}
        </svelte:fragment>
        <svelte:fragment slot="rightAlign">
          <TextTooltipAugment content="Generate commit message">
            <IconButtonAugment
              size={1}
              variant="ghost-block"
              color="neutral"
              disabled={isGenerating || $currStatus !== GitOperationStatus.idle}
              on:click={handleGenerateCommitMessage}
            >
              <SparklesIcon />
            </IconButtonAugment>
          </TextTooltipAugment>
          <GitCommitActions
            onCommit={handleCommit}
            {variant}
            stagedFilesCount={finalStagedFilesCount}
            isCommitting={finalIsCommitting}
            canCommit={finalCanCommit}
            disabled={!hasCommitMessage}
            {isGenerating}
          />
        </svelte:fragment>
      </InputActionBar.Root>
    </div>
  </RichTextEditorAugment.Root>
</div>

<style>
  .c-chat-input {
    width: 100%;
    height: 100%;

    max-width: 100%;
    max-height: 100%;

    gap: var(--ds-spacing-2);
    display: flex;
    flex-direction: column;
    /* to prevent the toggle mode tooltip from being overlapped */
    position: relative;
    /* Higher z-index to ensure dropdowns appear above ThreadsList */
    z-index: var(--z-chat-input);
  }

  .c-git-commit-input__badges {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-1);
  }
</style>
