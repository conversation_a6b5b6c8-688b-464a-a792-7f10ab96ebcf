/**
 * Multi-Repository Git Composition Layer
 *
 * Composes multiple single-repository git contexts into a unified multi-repo interface.
 * Uses the "store of stores" pattern for reactive state composition with state machine guards.
 *
 * Architecture:
 * - Keeps existing single-repo system unchanged (composition over inheritance)
 * - Creates higher-level orchestration that delegates to existing implementations
 * - Uses static context methods following established patterns
 * - Implements state machine with transaction guards for repository operations
 * - Idempotent ensureRepo pattern for repository initialization
 */

import { writable, derived, readonly, get, type Readable, type Writable } from "svelte/store";
import { getContext, setContext } from "svelte";
import type { IExtensionClient } from "../../../extension-client";
import type {
  GitRepositoryState,
  GitRepositoryInfo,
} from "@augment-internal/sidecar-libs/src/git/git-types";
import { GitRPCClient, type IGitRPCClient } from "./git-rpc-client";
import { GitAgentEditModel, type IGitAgentEditModel } from "./model";
import { GitAgentEditController, type IGitAgentEditController } from "./controller";
import type { IFileCollectionsDomainModel, IRepositoryStateDomainModel } from "./domain-stores";
import type { ChatFlagsModel } from "../../../models/chat-flags-model";

// ============================================================================
// Multi-Repository Operation Status
// ============================================================================

export enum MultiRepoOperationStatus {
  idle = "idle",
  discovering = "discovering",
  refreshing = "refreshing",
  tracking = "tracking",
  selecting = "selecting",
}

// ============================================================================
// Multi-Repository Model Interface
// ============================================================================

export interface IMultiRepoGitModel {
  /** Map of repository root paths to their models */
  readonly repositories: Readable<Map<string, IGitAgentEditModel>>;
  /** Currently active repository root path */
  readonly activeRepository: Readable<string | undefined>;
  /** Model for the currently active repository */
  readonly activeRepositoryModel: Readable<IGitAgentEditModel | undefined>;
  /** List of available repository root paths */
  readonly availableRepositories: Readable<string[]>;
  /** Current multi-repo operation status */
  readonly operationStatus: Readable<MultiRepoOperationStatus>;
  /** Last error message */
  readonly lastError: Readable<string | undefined>;
  /** State of the currently active repository */
  readonly activeRepositoryState: Readable<GitRepositoryState | undefined>;
  /** Metadata of the currently active repository for UI badges */
  readonly activeRepositoryMetadata: Readable<{ currentBranch?: string; shortCommit?: string }>;

  /** Get file collections model for a specific repository */
  getFileCollectionsModel(repoPath: string): IFileCollectionsDomainModel | undefined;
  /** Get repository state model for a specific repository */
  getRepositoryStateModel(repoPath: string): IRepositoryStateDomainModel | undefined;
}

// ============================================================================
// Multi-Repository Controller Interface
// ============================================================================

export interface IMultiRepoGitController {
  /** Clear the last error message */
  clearError(): void;
  /** List currently tracked repositories */
  listRepositories(): Promise<GitRepositoryInfo[]>;
  /** Refresh all tracked repositories */
  refreshRepositories(): Promise<void>;
  /** Add/track a repository for git operations (idempotent) */
  ensureRepository(repoRoot: string): Promise<void>;
  /** Select the active repository */
  selectRepository(repoRoot: string): Promise<void>;
  /** Get controller for the currently active repository */
  getActiveRepositoryController(): IGitAgentEditController | undefined;
  /** Get controller for a specific repository */
  getRepositoryController(repoRoot: string): IGitAgentEditController | undefined;
}

// ============================================================================
// Multi-Repository Model Implementation
// ============================================================================

export class MultiRepoGitModel implements IMultiRepoGitModel {
  // eslint-disable-next-line @typescript-eslint/naming-convention
  private static readonly CTX_KEY: unique symbol = Symbol("multi-repo-git-context");

  private readonly _repositories: Writable<Map<string, IGitAgentEditModel>>;
  private readonly _activeRepository: Writable<string | undefined>;
  private readonly _operationStatus: Writable<MultiRepoOperationStatus>;
  private readonly _lastError: Writable<string | undefined>;

  // Reactive derived stores
  readonly repositories: Readable<Map<string, IGitAgentEditModel>>;
  readonly activeRepository: Readable<string | undefined>;
  readonly operationStatus: Readable<MultiRepoOperationStatus>;
  readonly lastError: Readable<string | undefined>;
  readonly availableRepositories: Readable<string[]>;
  readonly activeRepositoryModel: Readable<IGitAgentEditModel | undefined>;
  readonly activeRepositoryState: Readable<GitRepositoryState | undefined>;
  readonly activeRepositoryMetadata: Readable<{ currentBranch?: string; shortCommit?: string }>;

  constructor() {
    this._repositories = writable(new Map());
    this._activeRepository = writable(undefined);
    this._operationStatus = writable(MultiRepoOperationStatus.idle);
    this._lastError = writable(undefined);

    // Initialize readonly stores after writable stores
    this.repositories = readonly(this._repositories);
    this.activeRepository = readonly(this._activeRepository);
    this.operationStatus = readonly(this._operationStatus);
    this.lastError = readonly(this._lastError);

    this.availableRepositories = derived(this._repositories, ($repositories) =>
      Array.from($repositories.keys()).sort(),
    );

    this.activeRepositoryModel = derived(
      [this._repositories, this._activeRepository],
      ([$repositories, $activeRepository]) => {
        return $activeRepository ? $repositories.get($activeRepository) : undefined;
      },
    );

    this.activeRepositoryState = derived(this.activeRepositoryModel, ($activeModel) => {
      if ($activeModel) {
        return get($activeModel.repositoryState);
      } else {
        return undefined;
      }
    });

    this.activeRepositoryMetadata = derived(this.activeRepositoryModel, ($activeModel) => {
      if (!$activeModel?.repositoryStateModel?.metadata) {
        return { currentBranch: undefined, shortCommit: undefined };
      }

      const metadata = $activeModel.repositoryStateModel.metadata;
      const currentBranch = get(metadata.currentBranch);
      const headOid = get(metadata.headOid);
      const shortCommit = headOid ? headOid.substring(0, 7) : undefined;

      return { currentBranch, shortCommit };
    });
  }

  /** Get file collections model for a specific repository */
  getFileCollectionsModel(repoPath: string): IFileCollectionsDomainModel | undefined {
    const repositories = get(this._repositories);
    const model = repositories.get(repoPath);
    return model?.fileCollectionsModel;
  }

  /** Get repository state model for a specific repository */
  getRepositoryStateModel(repoPath: string): IRepositoryStateDomainModel | undefined {
    const repositories = get(this._repositories);
    const model = repositories.get(repoPath);
    return model?.repositoryStateModel;
  }

  /** Place the model + controller in Svelte context (provider side). */
  static setInContext(model: MultiRepoGitModel, controller: IMultiRepoGitController): void {
    setContext(MultiRepoGitModel.CTX_KEY, { model, controller });
  }

  /** Retrieve context if inside provider scope; returns undefined otherwise. */
  static getContext():
    | { model: MultiRepoGitModel; controller: IMultiRepoGitController }
    | undefined {
    return getContext(MultiRepoGitModel.CTX_KEY);
  }

  /** Fail-fast accessor for call sites that require the context to exist. */
  static requireContext(): { model: MultiRepoGitModel; controller: IMultiRepoGitController } {
    const ctx = MultiRepoGitModel.getContext();
    if (!ctx) throw new Error("MultiRepoGitModel context missing");
    return ctx;
  }

  /**
   * Internal exposure of writable sources for the controller layer only.
   * Pattern: Keep writes centralized; if you need a new mutation path add it here rather than
   * leaking specific writable references individually.
   */
  _getWritableStores() {
    return {
      repositories: this._repositories,
      activeRepository: this._activeRepository,
      operationStatus: this._operationStatus,
      lastError: this._lastError,
    };
  }
}

// ============================================================================
// Multi-Repository Controller Implementation
// ============================================================================

export class MultiRepoGitController implements IMultiRepoGitController {
  private readonly _controllers = new Map<string, IGitAgentEditController>();
  private _stores: ReturnType<MultiRepoGitModel["_getWritableStores"]>;

  constructor(
    private readonly _model: MultiRepoGitModel,
    private readonly _rpcClient: IGitRPCClient,
    private readonly _flagsModel: ChatFlagsModel,
  ) {
    this._stores = this._model._getWritableStores();
  }

  /**
   * Guard: Ensures only one operation runs at a time. All public mutators are serialized.
   * Throws immediately if a caller attempts to start a second operation while another is active.
   */
  private _ensureIdle(): void {
    const currentStatus = get(this._stores.operationStatus);
    if (currentStatus !== MultiRepoOperationStatus.idle) {
      throw new Error(`Cannot start operation: ${currentStatus} is already in progress`);
    }
  }

  /**
   * Standard operation wrapper implementing the state machine transition contract.
   * NEVER mutate writable stores outside this function in public methods.
   */
  private async _executeOperation<T>(
    status: MultiRepoOperationStatus,
    operation: () => Promise<T>,
  ): Promise<T> {
    // No-op if git tracking is disabled
    if (!this._flagsModel.enableAgentGitTracker) {
      throw new Error("Git tracking is disabled");
    }

    this._ensureIdle();
    try {
      this._stores.operationStatus.set(status);
      this._stores.lastError.set(undefined);
      const result = await operation();
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Unknown error";
      this._stores.lastError.set(errorMessage);
      throw error;
    } finally {
      this._stores.operationStatus.set(MultiRepoOperationStatus.idle);
    }
  }

  /** Clear the last error message */
  clearError(): void {
    this._stores.lastError.set(undefined);
  }

  async listRepositories(): Promise<GitRepositoryInfo[]> {
    // No-op if git tracking is disabled
    if (!this._flagsModel.enableAgentGitTracker) {
      return [];
    }

    return this._executeOperation(MultiRepoOperationStatus.discovering, async () => {
      const trackedRepos = await this._rpcClient.listRepositories();

      // Ensure all tracked repositories have models and controllers
      for (const repoInfo of trackedRepos) {
        const repoRoot = repoInfo.repoRoot;

        // Check if repository model already exists
        const currentRepos = get(this._stores.repositories);
        if (currentRepos.has(repoRoot)) {
          continue; // Already exists, skip
        }

        // Create model and controller for this repository
        const model = new GitAgentEditModel();
        const controller = new GitAgentEditController(model, this._rpcClient, repoRoot);

        // Store the model and controller
        this._stores.repositories.update((repos) => {
          const newRepos = new Map(repos);
          newRepos.set(repoRoot, model);
          return newRepos;
        });
        this._controllers.set(repoRoot, controller);

        // Initialize the controller
        await controller.initialize();
      }

      // Auto-select first repository if no active repository is set
      const currentActiveRepo = get(this._stores.activeRepository);
      if (!currentActiveRepo && trackedRepos.length > 0) {
        this._stores.activeRepository.set(trackedRepos[0].repoRoot);
      }

      return trackedRepos;
    });
  }

  async refreshRepositories(): Promise<void> {
    return this._executeOperation(MultiRepoOperationStatus.refreshing, async () => {
      const currentRepos = get(this._stores.repositories);

      // Refresh all tracked repositories
      const refreshPromises = Array.from(currentRepos.keys()).map(async (repoRoot) => {
        const controller = this._controllers.get(repoRoot);
        if (controller) {
          try {
            await controller.refreshRepository();
          } catch (error) {
            console.error(`Failed to refresh repository ${repoRoot}:`, error);
            // Continue with other repositories even if one fails
          }
        }
      });

      await Promise.all(refreshPromises);
    });
  }

  /** Add/track a repository for git operations (idempotent) */
  async ensureRepository(repoRoot: string): Promise<void> {
    return this._executeOperation(MultiRepoOperationStatus.tracking, async () => {
      await this._ensureRepositoryInternal(repoRoot);
    });
  }

  /** Internal helper for ensuring repository without status mutation (for use within other operations) */
  private async _ensureRepositoryInternal(repoRoot: string): Promise<void> {
    // Check if repository is already tracked
    const currentRepos = get(this._stores.repositories);
    if (currentRepos.has(repoRoot)) {
      return; // Already exists, idempotent
    }

    // Track repository in backend
    await this._rpcClient.trackRepository(repoRoot);

    // Create model and controller for this repository
    const model = new GitAgentEditModel();
    const controller = new GitAgentEditController(model, this._rpcClient, repoRoot);

    // Store them
    this._stores.repositories.update(($repos) => {
      const newRepos = new Map($repos);
      newRepos.set(repoRoot, model);
      return newRepos;
    });
    this._controllers.set(repoRoot, controller);

    // Initialize the repository
    await controller.initialize();
  }

  async selectRepository(repoRoot: string): Promise<void> {
    return this._executeOperation(MultiRepoOperationStatus.selecting, async () => {
      // Ensure repository exists before selecting (internal version to avoid nested operations)
      await this._ensureRepositoryInternal(repoRoot);
      this._stores.activeRepository.set(repoRoot);
    });
  }

  getActiveRepositoryController(): IGitAgentEditController | undefined {
    const currentRepo = get(this._stores.activeRepository);
    return currentRepo ? this._controllers.get(currentRepo) : undefined;
  }

  getRepositoryController(repoRoot: string): IGitAgentEditController | undefined {
    return this._controllers.get(repoRoot);
  }
}

// ============================================================================
// Context Factory and Assembly
// ============================================================================

export interface MultiRepoGitContext {
  /** Reactive state model */
  model: MultiRepoGitModel;
  /** Mutation controller */
  controller: MultiRepoGitController;
}

/**
 * Creates multi-repository git context with model and controller.
 * Automatically stores both model and controller in Svelte context.
 * @param extensionClient - Extension client for RPC operations
 * @param flagsModel - Chat flags model for feature flag checks
 * @returns Context with model and controller
 */
export function createMultiRepoGitContext(
  extensionClient: IExtensionClient,
  flagsModel: ChatFlagsModel,
): MultiRepoGitContext {
  const rpcClient = new GitRPCClient(extensionClient);
  const model = new MultiRepoGitModel();
  const controller = new MultiRepoGitController(model, rpcClient, flagsModel);
  MultiRepoGitModel.setInContext(model, controller);
  return { model, controller };
}
