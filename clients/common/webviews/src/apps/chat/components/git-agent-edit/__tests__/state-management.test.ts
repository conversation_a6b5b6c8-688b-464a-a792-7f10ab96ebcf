/**
 * Tests for Git Agent Edit State Management
 */

import { describe, it, expect, beforeEach, vi } from "vitest";
import { get } from "svelte/store";
import { GitAgentEditModel, GitOperationStatus } from "../context/model";
import { GitAgentEditController } from "../context/controller";
import type { IGitRPCClient } from "../context/git-rpc-client";
import type { GitRepositoryState } from "@augment-internal/sidecar-libs/src/git/git-types";

/**
 * Creates a mock git RPC client for testing.
 * @returns Mock implementation of IGitRPCClient
 */
const createMockGitRPCClient = (): IGitRPCClient => ({
  // Repository discovery and management
  listRepositories: vi.fn(),
  trackRepository: vi.fn(),
  getRepositoryInfo: vi.fn(),
  getRepositoryState: vi.fn(),
  refreshRepository: vi.fn(),
  validateRepository: vi.fn(),
  // File operations
  getStatusMatrix: vi.fn(),
  stageFiles: vi.fn(),
  unstageFiles: vi.fn(),
  checkoutFiles: vi.fn(),
  resetToIndex: vi.fn(),
  // Commit operations
  commit: vi.fn(),
  // Diff operations
  openGitDiff: vi.fn(),
  // File viewing operations
  openFile: vi.fn(),
});

// Mock repository state
const createMockRepositoryState = (): GitRepositoryState => ({
  headOid: "abc123",
  currentBranch: "main",
  workdir: "/test/repo",
  gitdir: "/test/repo/.git",
  files: [],
  isOperationInProgress: false,
  lastUpdated: Date.now(),
});

describe("GitAgentEditModel", () => {
  let model: GitAgentEditModel;
  let mockRPCClient: IGitRPCClient;
  let controller: GitAgentEditController;

  beforeEach(() => {
    model = new GitAgentEditModel();
    mockRPCClient = createMockGitRPCClient();
    controller = new GitAgentEditController(model, mockRPCClient);
  });

  it("should initialize with IDLE status", () => {
    expect(get(model.currStatus)).toBe(GitOperationStatus.idle);
  });

  it("should derive UI state correctly from status", () => {
    const uiState = get(model.uiStateModel.uiState);
    const flagsModel = model.flagsModel;

    expect(uiState.commitPlaceholder).toBe("Enter commit message");
    expect(uiState.operationDescription).toBe("Ready");

    // Test reactive flags domain model
    expect(get(flagsModel.canCommit)).toBe(true);
    expect(get(flagsModel.canStageFiles)).toBe(true);
    expect(get(flagsModel.canUnstageFiles)).toBe(true);
    expect(get(flagsModel.canRefresh)).toBe(true);
  });

  it("should update status and derive UI state correctly", () => {
    // Update status to COMMITTING using controller's writable stores
    const stores = controller.getWritableStores();
    stores.currStatus.set(GitOperationStatus.committing);

    const uiState = get(model.uiStateModel.uiState);
    const flagsModel = model.flagsModel;

    expect(get(model.currStatus)).toBe(GitOperationStatus.committing);
    expect(uiState.commitPlaceholder).toBe("Committing changes...");
    expect(uiState.operationDescription).toBe("Committing changes");

    expect(get(flagsModel.canCommit)).toBe(false);
    expect(get(flagsModel.canStageFiles)).toBe(false);
    expect(get(flagsModel.canUnstageFiles)).toBe(false);
  });

  it("should track operation metadata", () => {
    const stores = controller.getWritableStores();
    stores.currStatus.set(GitOperationStatus.staging);

    const metadata = get(model.operationStatusModel.operationMetadata);
    expect(metadata.status).toBe(GitOperationStatus.staging);
    expect(metadata.startedAt).toBeTypeOf("number");
  });

  it("should provide reactive flags domain model", () => {
    const flagsModel = model.flagsModel;
    const stores = controller.getWritableStores();

    // Reset to idle first
    stores.currStatus.set(GitOperationStatus.idle);

    // Initially all flags should be true (IDLE state)
    expect(get(flagsModel.canCommit)).toBe(true);
    expect(get(flagsModel.canStageFiles)).toBe(true);
    expect(get(flagsModel.canUnstageFiles)).toBe(true);
    expect(get(flagsModel.canRefresh)).toBe(true);
    expect(get(flagsModel.canModifyIndividualFiles)).toBe(true);
    expect(get(flagsModel.canRevertFiles)).toBe(true);

    // Update status to COMMITTING
    stores.currStatus.set(GitOperationStatus.committing);

    // Only canCommit should be false, others should be false too (not idle)
    expect(get(flagsModel.canCommit)).toBe(false);
    expect(get(flagsModel.canStageFiles)).toBe(false);
    expect(get(flagsModel.canUnstageFiles)).toBe(false);
    expect(get(flagsModel.canRefresh)).toBe(false);
    expect(get(flagsModel.canModifyIndividualFiles)).toBe(false);
    expect(get(flagsModel.canRevertFiles)).toBe(false);

    // Test staging state - should allow unstaging but not staging
    stores.currStatus.set(GitOperationStatus.staging);
    expect(get(flagsModel.canStageFiles)).toBe(false);
    expect(get(flagsModel.canUnstageFiles)).toBe(true);

    // Test unstaging state - should allow staging but not unstaging
    stores.currStatus.set(GitOperationStatus.unstaging);
    expect(get(flagsModel.canStageFiles)).toBe(true);
    expect(get(flagsModel.canUnstageFiles)).toBe(false);

    // Reset to idle
    stores.currStatus.set(GitOperationStatus.idle);
    expect(get(flagsModel.canCommit)).toBe(true);
  });
});

describe("GitAgentEditController", () => {
  let model: GitAgentEditModel;
  let controller: GitAgentEditController;
  let mockRPCClient: IGitRPCClient;

  beforeEach(() => {
    model = new GitAgentEditModel();
    mockRPCClient = createMockGitRPCClient();

    // Mock the initialize method to avoid actual RPC calls
    vi.spyOn(mockRPCClient, "getRepositoryState").mockResolvedValue(createMockRepositoryState());

    controller = new GitAgentEditController(model, mockRPCClient);
  });

  it("should prevent concurrent operations", async () => {
    // Set status to STAGING to simulate ongoing operation
    const stores = controller.getWritableStores();
    stores.currStatus.set(GitOperationStatus.staging);

    // Try to start another operation
    await expect(controller.stageFile("test.txt")).rejects.toThrow(
      "Cannot start operation: staging is already in progress",
    );
  });

  it("should handle operation lifecycle correctly", async () => {
    vi.spyOn(mockRPCClient, "stageFiles").mockResolvedValue();
    vi.spyOn(controller, "refreshSpecificFiles").mockImplementation(async () => {});

    // Start with IDLE
    expect(get(model.currStatus)).toBe(GitOperationStatus.idle);

    // Execute operation
    const stagePromise = controller.stageFile("test.txt");

    // Should be in STAGING state during operation
    expect(get(model.currStatus)).toBe(GitOperationStatus.staging);

    await stagePromise;

    // Should return to IDLE after completion
    expect(get(model.currStatus)).toBe(GitOperationStatus.idle);
    expect(get(model.lastError)).toBeUndefined();
  });

  it("should handle errors correctly", async () => {
    const testError = new Error("Test error");
    vi.spyOn(mockRPCClient, "stageFiles").mockRejectedValue(testError);

    // Execute operation that will fail
    await expect(controller.stageFile("test.txt")).rejects.toThrow("Test error");

    // Should return to IDLE and set error
    expect(get(model.currStatus)).toBe(GitOperationStatus.idle);
    expect(get(model.lastError)).toBe("Failed to stage files: Test error");
  });

  it("should clear errors correctly", () => {
    // Set an error
    const stores = controller.getWritableStores();
    stores.lastError.set("Test error");
    expect(get(model.lastError)).toBe("Test error");

    // Clear error
    controller.clearError();
    expect(get(model.lastError)).toBeUndefined();
  });
});

describe("UI State Derivation", () => {
  let model: GitAgentEditModel;
  let mockRPCClient: IGitRPCClient;
  let controller: GitAgentEditController;

  beforeEach(() => {
    model = new GitAgentEditModel();
    mockRPCClient = createMockGitRPCClient();
    controller = new GitAgentEditController(model, mockRPCClient);
  });

  it("should provide correct placeholders for each operation", () => {
    const testCases = [
      { status: GitOperationStatus.idle, placeholder: "Enter commit message" },
      { status: GitOperationStatus.committing, placeholder: "Committing changes..." },
      { status: GitOperationStatus.staging, placeholder: "Staging files..." },
      { status: GitOperationStatus.unstaging, placeholder: "Unstaging files..." },
      { status: GitOperationStatus.checkingOut, placeholder: "Checking out files..." },
      { status: GitOperationStatus.refreshing, placeholder: "Refreshing repository..." },
      { status: GitOperationStatus.initializing, placeholder: "Initializing repository..." },
    ];

    const stores = controller.getWritableStores();
    testCases.forEach(({ status, placeholder }) => {
      stores.currStatus.set(status);
      const uiState = get(model.uiStateModel.uiState);
      expect(uiState.commitPlaceholder).toBe(placeholder);
    });
  });

  it("should disable operations correctly during loading", () => {
    const stores = controller.getWritableStores();
    // Test IDLE state
    stores.currStatus.set(GitOperationStatus.idle);
    let canCommit = get(model.flagsModel.canCommit);
    let canStageFiles = get(model.flagsModel.canStageFiles);
    let canUnstageFiles = get(model.flagsModel.canUnstageFiles);
    let canRefresh = get(model.flagsModel.canRefresh);

    expect(canCommit).toBe(true);
    expect(canStageFiles).toBe(true);
    expect(canUnstageFiles).toBe(true);
    expect(canRefresh).toBe(true);

    // Test loading state
    stores.currStatus.set(GitOperationStatus.committing);
    canCommit = get(model.flagsModel.canCommit);
    canStageFiles = get(model.flagsModel.canStageFiles);
    canUnstageFiles = get(model.flagsModel.canUnstageFiles);
    canRefresh = get(model.flagsModel.canRefresh);

    expect(canCommit).toBe(false);
    expect(canStageFiles).toBe(false);
    expect(canUnstageFiles).toBe(false);
    expect(canRefresh).toBe(false);
  });
});
