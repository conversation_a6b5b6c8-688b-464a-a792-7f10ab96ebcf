/**
 * Git Agent Edit Controller
 *
 * Mutation orchestration layer for git operations with state machine serialization.
 * Exclusively mutates GitAgentEditModel's writable stores.
 *
 * State machine (single-flight):
 *   idle -> [initializing|refreshing|staging|unstaging|checkingOut|committing] -> idle
 *
 * Operation pattern:
 *   1. Assert idle state (fail fast on concurrent calls)
 *   2. Set status and clear previous error
 *   3. Perform RPC/local mutations
 *   4. On error: capture contextual message, rethrow
 *   5. Always: revert to idle status
 *
 * Error handling:
 *   - Surfaces user-actionable context with original error message
 *   - Preserves raw error object for dev tools
 *
 * Performance strategy:
 *   - Prefers granular `refreshSpecificFiles` over full repository refresh
 *   - Falls back to full refresh when baseline state is missing
 *
 * Extension guidance:
 *   - Wrap new mutations in `_executeOperation(status, async () => {...})`
 *   - Avoid new status values unless distinct UI gating needed
 *   - Keep transformation logic in domain stores, not controller
 */
import { get } from "svelte/store";
import type { IGitRPCClient } from "./git-rpc-client";
import type { StatusRow } from "isomorphic-git";
import type { GitFileState, GitReference } from "@augment-internal/sidecar-libs/src/git/git-types";
import { GitSpecialRef } from "@augment-internal/sidecar-libs/src/git/git-types";
import { GitOperationStatus } from "./domain-stores";
import type { GitAgentEditModel } from "./model";

export interface IGitAgentEditController {
  // Core state management
  /** Clear the last error message */
  clearError(): void;
  /** Get writable stores for internal use */
  getWritableStores(): ReturnType<GitAgentEditModel["_getWritableStores"]>;

  // Repository operations
  /** Initialize the git repository */
  initialize(): Promise<void>;
  /** Refresh the entire repository state */
  refreshRepository(): Promise<void>;
  /** Refresh specific files only */
  refreshSpecificFiles(filePaths: string[]): Promise<void>;
  /** Validate repository is properly initialized */
  validateRepository(): Promise<boolean>;

  // File staging operations
  /** Stage a single file */
  stageFile(filePath: string): Promise<void>;
  /** Stage multiple files */
  stageFiles(filePaths: string[]): Promise<void>;
  /** Unstage a single file */
  unstageFile(filePath: string): Promise<void>;
  /** Unstage multiple files */
  unstageFiles(filePaths: string[]): Promise<void>;
  /** Stage all unstaged files */
  stageAllUnstaged(): Promise<void>;
  /** Unstage all staged files */
  unstageAllStaged(): Promise<void>;
  /** Clear all staged changes */
  clearStagedChanges(): void;

  // File modification operations
  /** Checkout a single file to HEAD */
  checkoutFileToHead(filePath: string): Promise<void>;
  /** Checkout multiple files to HEAD */
  checkoutFilesToHead(filePaths: string[]): Promise<void>;
  /** Checkout a single file to a specific reference */
  checkoutFileToReference(filePath: string, reference: GitReference): Promise<void>;
  /** Checkout multiple files to a specific reference */
  checkoutFilesToReference(filePaths: string[], reference: GitReference): Promise<void>;
  /** Reset a single file to index state */
  resetToIndexFile(filePath: string): Promise<void>;
  /** Reset multiple files to index state */
  resetToIndexFiles(filePaths: string[]): Promise<void>;

  // Commit operations
  /** Commit staged changes with message */
  commit(message: string): Promise<string>;

  // File viewing operations
  /** Open file in editor */
  openFile(filePath: string): Promise<void>;
  /** Open git diff for file */
  openGitDiff(filePath: string, staged?: boolean): Promise<void>;

  /** Git RPC client for operations */
  gitRPCClient: IGitRPCClient;
}

export class GitAgentEditController implements IGitAgentEditController {
  private _stores: ReturnType<GitAgentEditModel["_getWritableStores"]>;
  constructor(
    private _model: GitAgentEditModel,
    public readonly gitRPCClient: IGitRPCClient,
    private readonly _repoRoot?: string, // Repository root for multi-repo support
  ) {
    this._stores = this._model._getWritableStores();
    // Don't auto-initialize in multi-repo mode - let the multi-repo controller handle it
    if (!this._repoRoot) {
      void this.initialize();
    }
  }

  /**
   * Guard: Ensures only one operation runs at a time. All public mutators are serialized.
   * Throws immediately if a caller attempts to start a second operation while another is active.
   */
  private _ensureIdle(): void {
    const currentStatus = get(this._stores.currStatus);
    if (currentStatus !== GitOperationStatus.idle) {
      throw new Error(`Cannot start operation: ${currentStatus} is already in progress`);
    }
  }

  /**
   * Standard operation wrapper implementing the state machine transition contract.
   * NEVER mutate writable stores outside this function in public methods (except for
   * ultra-simple synchronous adjustments that do not warrant a status phase).
   */
  private async _executeOperation<T>(
    status: GitOperationStatus,
    operation: () => Promise<T>,
  ): Promise<T> {
    this._ensureIdle();
    try {
      this._stores.currStatus.set(status);
      this._stores.lastError.set(undefined);
      const result = await operation();
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Unknown error";
      const contextualError = this._getContextualErrorMessage(status, errorMessage);
      this._stores.lastError.set(contextualError);
      throw error;
    } finally {
      this._stores.currStatus.set(GitOperationStatus.idle);
    }
  }

  private _getContextualErrorMessage(status: GitOperationStatus, originalError: string): string {
    const operationContext = this._getOperationContext(status);
    return `${operationContext}: ${originalError}`;
  }

  private _getOperationContext(status: GitOperationStatus): string {
    switch (status) {
      case GitOperationStatus.initializing:
        return "Failed to initialize git repository";
      case GitOperationStatus.refreshing:
        return "Failed to refresh repository state";
      case GitOperationStatus.staging:
        return "Failed to stage files";
      case GitOperationStatus.unstaging:
        return "Failed to unstage files";
      case GitOperationStatus.checkingOut:
        return "Failed to checkout files";
      case GitOperationStatus.committing:
        return "Failed to commit changes";
      default:
        return "Operation failed";
    }
  }

  /** Clear current error and, if in a transient non-idle state due to an early failure, reset to idle. */
  clearError(): void {
    this._stores.lastError.set(undefined);
    const currentStatus = get(this._stores.currStatus);
    if (currentStatus !== GitOperationStatus.idle) {
      this._stores.currStatus.set(GitOperationStatus.idle);
    }
  }

  /** Primarily for testing & future controller composition – exposes writable handles. */
  getWritableStores() {
    return this._stores;
  }

  /**
   * Initialize controller + model state.
   * Semantics:
   *  - Clears any existing repository snapshot (ensures consumers treat data as loading)
   *  - Clears prior error surface
   *  - Fetches a fresh repository state snapshot
   * Rationale: Avoids partial reuse of possibly stale state when re-mounting or re-attaching.
   */
  async initialize(): Promise<void> {
    return this._executeOperation(GitOperationStatus.initializing, async () => {
      this._stores.repositoryState.set(undefined);
      this._stores.lastError.set(undefined);
      await this._refreshRepositoryInternal();
    });
  }

  /**
   * Full repository refresh (broad fetch of authoritative state).
   * Used when: initial load completed, user explicitly requests refresh, or targeted refresh cannot safely apply.
   * Prefer refreshSpecificFiles for small deltas.
   */
  async refreshRepository(): Promise<void> {
    return this._executeOperation(GitOperationStatus.refreshing, async () => {
      await this._refreshRepositoryInternal();
    });
  }

  /** Internal helper performing the actual repository state retrieval (no status mutation here). */
  private async _refreshRepositoryInternal(): Promise<void> {
    const repoRoot = this._repoRoot || ""; // Default for single-repo mode
    const repositoryState = await this.gitRPCClient.getRepositoryState(repoRoot);
    this._stores.repositoryState.set(repositoryState);
  }

  /**
   * Targeted refresh for a subset of files.
   * Strategy:
   *  - If repository state missing, defer to full refresh (cold start scenario)
   *  - Query status matrix only for requested files (with stats + changed filter)
   *  - Replace entries for those files while preserving untouched ones (O(n) merge)
   *  - Update lastUpdated timestamp to assist any staleness heuristics
   *
   * Notes:
   *  - Filtering to changed-only keeps payload minimal while still updating modifications
   *  - We intentionally keep removed files that are not in filePaths untouched; a future
   *    enhancement could prune if HEAD/workdir/stage all equal, but we defer for clarity
   *  - Special handling for deleted files that might not appear in status matrix results
   */
  async refreshSpecificFiles(filePaths: string[]): Promise<void> {
    try {
      const currentState = get(this._stores.repositoryState);
      if (!currentState) {
        await this.refreshRepository();
        return;
      }
      const repoRoot = this._repoRoot || "";

      // Get updated status matrix for the specific files
      const updatedStatusMatrix = await this.gitRPCClient.getStatusMatrix(repoRoot, {
        filepaths: filePaths,
        includeStatistics: true,
        filterChangedOnly: true,
      } as any); // Type assertion to work around interface issue

      // Filter for files that actually have changes
      const filesWithChanges = updatedStatusMatrix.filter((file) => {
        const [, head, workdir, stage] = file.statusRow;
        return head !== workdir || head !== stage;
      });

      // Build the new file list:
      // 1. Keep all files that weren't in the refresh request
      // 2. Add all files that were returned with changes
      // Note: Files that were requested but not returned (e.g., staged for deletion)
      // are automatically excluded since they're not in filesWithChanges
      const updatedFiles = [
        ...currentState.files.filter((file) => !filePaths.includes(file.qualifiedPathName.relPath)),
        ...filesWithChanges,
      ];

      this._stores.repositoryState.set({
        ...currentState,
        files: updatedFiles,
        lastUpdated: Date.now(),
      });
      this._stores.lastError.set(undefined);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Unknown error";
      this._stores.lastError.set(`Failed to refresh specific files: ${errorMessage}`);
      throw error;
    }
  }

  /** Stage exactly one file (thin wrapper for UX convenience). */
  async stageFile(filePath: string): Promise<void> {
    return this.stageFiles([filePath]);
  }

  /** Stage a list of files; refresh only those touched. */
  async stageFiles(filePaths: string[]): Promise<void> {
    return this._executeOperation(GitOperationStatus.staging, async () => {
      const repoRoot = this._repoRoot || "";
      await this.gitRPCClient.stageFiles(repoRoot, { filePaths });
      await this.refreshSpecificFiles(filePaths);
    });
  }

  /** Unstage exactly one file. */
  async unstageFile(filePath: string): Promise<void> {
    return this.unstageFiles([filePath]);
  }

  /** Unstage multiple files at once. */
  async unstageFiles(filePaths: string[]): Promise<void> {
    return this._executeOperation(GitOperationStatus.unstaging, async () => {
      const repoRoot = this._repoRoot || "";
      await this.gitRPCClient.unstageFiles(repoRoot, { filePaths });
      await this.refreshSpecificFiles(filePaths);
    });
  }

  /** Checkout a single file to HEAD (discard local changes). */
  async checkoutFileToHead(filePath: string): Promise<void> {
    return this.checkoutFilesToHead([filePath]);
  }

  /** Checkout multiple files to HEAD. */
  async checkoutFilesToHead(filePaths: string[]): Promise<void> {
    return this._executeOperation(GitOperationStatus.checkingOut, async () => {
      const repoRoot = this._repoRoot || "";
      await this.gitRPCClient.checkoutFiles(repoRoot, {
        filePaths,
        reference: GitSpecialRef.HEAD,
      });
      await this.refreshSpecificFiles(filePaths);
    });
  }

  /** Checkout a single file to a specific reference. */
  async checkoutFileToReference(filePath: string, reference: GitReference): Promise<void> {
    return this.checkoutFilesToReference([filePath], reference);
  }

  /** Checkout multiple files to a specific reference. */
  async checkoutFilesToReference(filePaths: string[], reference: GitReference): Promise<void> {
    return this._executeOperation(GitOperationStatus.checkingOut, async () => {
      const repoRoot = this._repoRoot || "";
      await this.gitRPCClient.checkoutFiles(repoRoot, {
        filePaths,
        reference,
      });
      await this.refreshSpecificFiles(filePaths);
    });
  }

  /** Reset exactly one file to index state (thin wrapper for UX convenience). */
  async resetToIndexFile(filePath: string): Promise<void> {
    return this.resetToIndexFiles([filePath]);
  }

  /** Reset multiple files to index state; refresh only those touched. */
  async resetToIndexFiles(filePaths: string[]): Promise<void> {
    return this._executeOperation(GitOperationStatus.checkingOut, async () => {
      const repoRoot = this._repoRoot || "";
      await this.gitRPCClient.resetToIndex(repoRoot, { filePaths });
      await this.refreshSpecificFiles(filePaths);
    });
  }

  /** Convenience bulk stage (derives list from unstaged subset). */
  async stageAllUnstaged(): Promise<void> {
    const unstaged = get(this._model.fileCollectionsModel.unstagedFiles);
    const filePaths = unstaged.map((f) => f.qualifiedPathName.relPath);
    await this.stageFiles(filePaths);
  }

  /** Convenience bulk unstage counterpart. */
  async unstageAllStaged(): Promise<void> {
    const staged = get(this._model.fileCollectionsModel.stagedFiles);
    const filePaths = staged.map((f) => f.qualifiedPathName.relPath);
    await this.unstageFiles(filePaths);
  }

  /**
   * Commit all currently staged changes.
   * After committing we conservatively refresh every tracked file (tracked = previously known files list).
   * Rationale: staging set may shrink drastically; minimal per-file queries risk drift.
   */
  async commit(message: string): Promise<string> {
    return this._executeOperation(GitOperationStatus.committing, async () => {
      const repoRoot = this._repoRoot || "";
      const commitOid = await this.gitRPCClient.commit(repoRoot, { message });
      const currentState = get(this._stores.repositoryState);
      if (currentState) {
        const tracked = currentState.files.map((f) => f.qualifiedPathName.relPath);
        await this.refreshSpecificFiles(tracked);
      }
      return commitOid;
    });
  }

  /**
   * Forcefully clear staged bits in local snapshot without RPC (UI quick reset).
   * Use case: optimistic reset or preparing for a fresh status retrieval.
   */
  clearStagedChanges(): void {
    const currentState = get(this._stores.repositoryState);
    if (!currentState) return;
    const cleared = currentState.files.map((file: GitFileState) => {
      const [filepath, head, workdir] = file.statusRow;
      const clearedStatusRow: StatusRow = [filepath, head, workdir, head];
      return { ...file, statusRow: clearedStatusRow, changesSummary: undefined };
    });
    this._stores.repositoryState.set({ ...currentState, files: cleared });
  }

  /** Validate repository validity (existence, integrity). Non-throwing: returns false on failure. */
  async validateRepository(): Promise<boolean> {
    try {
      const repoRoot = this._repoRoot || "";
      return await this.gitRPCClient.validateRepository(repoRoot);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Unknown error";
      this._stores.lastError.set(`Failed to validate repository: ${errorMessage}`);
      return false;
    }
  }

  /** Open file in editor via extension (path resolved in RPC layer). */
  async openFile(filePath: string): Promise<void> {
    return this.gitRPCClient.openFile(filePath);
  }

  /** Open diff view for a file (staged or unstaged variant). */
  async openGitDiff(filePath: string, staged: boolean = false): Promise<void> {
    const repoRoot = this._repoRoot || "";
    return this.gitRPCClient.openGitDiff(repoRoot, { filePath, staged });
  }
}
