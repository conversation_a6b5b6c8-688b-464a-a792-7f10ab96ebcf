<script lang="ts">
  import { get } from "svelte/store";
  import DropdownMenuAugment from "$common-webviews/src/design-system/components/DropdownMenuAugment";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import GitRepositoryBadge from "./GitRepositoryBadge.svelte";

  // Props
  export let activeRepository: string | undefined = undefined;
  export let availableRepositories: string[] = [];
  export let repositoryStates: Map<string, any> = new Map();
  export let onSelectRepository: (repositoryPath: string) => void | Promise<void>;

  // Get repository display info
  function getRepositoryInfo(repoPath: string) {
    const repoModel = repositoryStates.get(repoPath);
    const repositoryName = repoPath.split("/").pop() || repoPath;

    let currentBranch: string | undefined;
    let shortCommit: string | undefined;

    if (repoModel?.repositoryStateModel?.metadata) {
      const metadata = repoModel.repositoryStateModel.metadata;
      currentBranch = get(metadata.currentBranch) as string;
      const headOid = get(metadata.headOid) as string;
      shortCommit = headOid ? headOid.substring(0, 7) : undefined;
    }

    return { repositoryName, currentBranch, shortCommit };
  }

  // Get current repository info
  $: currentRepoInfo = activeRepository
    ? getRepositoryInfo(activeRepository)
    : {
        repositoryName: undefined,
        currentBranch: undefined,
        shortCommit: undefined,
      };

  // Dropdown control
  let dropdownRoot: { requestClose: () => void; requestOpen: () => void } | undefined;

  // Handle repository selection
  async function selectRepository(repositoryPath: string) {
    try {
      // Always close dropdown first for immediate UI feedback
      dropdownRoot?.requestClose();

      // Only call onSelectRepository if it's a different repository
      if (repositoryPath !== activeRepository) {
        await onSelectRepository(repositoryPath);
      }
    } catch (error) {
      console.error("Failed to select repository:", error);
      // Error handling - the dropdown is already closed, which is fine
    }
  }
</script>

<div class="c-git-repository-selector">
  <DropdownMenuAugment.Root bind:this={dropdownRoot}>
    <DropdownMenuAugment.Trigger>
      <GitRepositoryBadge
        repositoryName={currentRepoInfo.repositoryName}
        currentBranch={undefined}
        shortCommit={undefined}
        onClick={() => {}}
      />
    </DropdownMenuAugment.Trigger>

    <DropdownMenuAugment.Content side="top" align="start">
      <DropdownMenuAugment.Label>Select Repository</DropdownMenuAugment.Label>

      {#each availableRepositories as repoPath (repoPath)}
        {@const repoInfo = getRepositoryInfo(repoPath)}
        {@const isActive = repoPath === activeRepository}

        <DropdownMenuAugment.Item onSelect={() => selectRepository(repoPath)} highlight={isActive}>
          <div class="c-git-repository-selector__item-content">
            <TextAugment size={1} weight="medium">
              {repoInfo.repositoryName}
            </TextAugment>
            {#if repoInfo.currentBranch || repoInfo.shortCommit}
              <TextAugment size={1} class="c-git-repository-selector__item-details">
                {#if repoInfo.currentBranch}
                  {repoInfo.currentBranch}
                {/if}
                {#if repoInfo.shortCommit}
                  {#if repoInfo.currentBranch}@{/if}{repoInfo.shortCommit}
                {/if}
              </TextAugment>
            {/if}
          </div>
        </DropdownMenuAugment.Item>
      {/each}
    </DropdownMenuAugment.Content>
  </DropdownMenuAugment.Root>
</div>

<style>
  .c-git-repository-selector {
    display: flex;
    align-items: center;
  }

  .c-git-repository-selector__item-content {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-0_5);
  }
</style>
