<script lang="ts">
  import BadgeAugment from "$common-webviews/src/design-system/components/BadgeAugment";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import BranchIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/code-branch.svg?component";

  // Props
  export let currentBranch: string | undefined = undefined;

  // Computed values
  $: displayBranch = currentBranch || "unknown";
</script>

{#if currentBranch}
  <BadgeAugment.Root variant="soft" color="neutral" size={1}>
    <BadgeAugment.IconButton slot="leftButtons">
      <BranchIcon />
    </BadgeAugment.IconButton>
    <TextAugment size={1} weight="medium">
      {displayBranch}
    </TextAugment>
  </BadgeAugment.Root>
{/if}
