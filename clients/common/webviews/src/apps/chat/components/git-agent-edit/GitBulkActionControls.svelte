<!--
  Git Bulk Action Controls

  Component for bulk git operations like staging/unstaging all files.
  Provides bulk operations without selection management.
-->
<script lang="ts">
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import PlusIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/plus.svg?component";
  import MinusIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/minus.svg?component";
  import { GitAgentEditModel } from "./context/model";

  // Get git context
  const { model, controller } = GitAgentEditModel.requireContext();

  // Reactive state from domain models
  $: unstagedFiles = model?.fileCollectionsModel.unstagedFiles;
  $: stagedFiles = model?.fileCollectionsModel.stagedFiles;

  // Calculate file counts
  $: unstagedCount = $unstagedFiles?.length || 0;
  $: stagedCount = $stagedFiles?.length || 0;

  // Get reactive flags from domain model
  $: flagsModel = model?.flagsModel;
  $: canStageFiles = flagsModel?.canStageFiles;
  $: canUnstageFiles = flagsModel?.canUnstageFiles;

  // Event handlers
  async function handleStageAll() {
    try {
      await controller.stageAllUnstaged();
    } catch (error) {
      console.error("Failed to stage all files:", error);
    }
  }

  async function handleUnstageAll() {
    try {
      await controller.unstageAllStaged();
    } catch (error) {
      console.error("Failed to unstage all files:", error);
    }
  }
</script>

<!-- Git Bulk Action Controls -->
<div class="c-git-bulk-actions">
  <div class="c-git-bulk-actions__top-row">
    <div class="c-git-bulk-actions__controls">
      <!-- Bulk git operations -->
      {#if unstagedCount > 0}
        <ButtonAugment
          variant="ghost"
          size={1}
          disabled={!$canStageFiles}
          on:click={handleStageAll}
          title={$canStageFiles
            ? "Stage all unstaged files"
            : "Cannot stage files during operation"}
        >
          <PlusIcon slot="iconLeft" />
          Stage All ({unstagedCount})
        </ButtonAugment>
      {/if}

      {#if stagedCount > 0}
        <ButtonAugment
          variant="ghost"
          size={1}
          disabled={!$canUnstageFiles}
          on:click={handleUnstageAll}
          title={$canUnstageFiles
            ? "Unstage all staged files"
            : "Cannot unstage files during operation"}
        >
          <MinusIcon slot="iconLeft" />
          Unstage All ({stagedCount})
        </ButtonAugment>
      {/if}
    </div>
  </div>
</div>

<style>
  .c-git-bulk-actions {
    padding: var(--ds-spacing-1);
    background-color: var(--ds-color-neutral-a3);
    border: 1px solid var(--ds-color-neutral-6);
    border-radius: var(--ds-radius-2);
    min-width: 0; /* Allow shrinking */
  }

  .c-git-bulk-actions__top-row {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-2);
    flex-wrap: wrap;
  }

  .c-git-bulk-actions__controls {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-1);
    min-width: min-content;
    /* Push to right when on same line */
    margin-left: auto;
    flex-shrink: 0;
  }

  /* When screen is narrow enough to cause wrapping, align buttons left */
  @media (max-width: 480px) {
    .c-git-bulk-actions__controls {
      margin-left: 0;
      flex-basis: 100%;
    }
  }
</style>
