<!--
  Git Status Badge

  Component for displaying git status indicators and counts.
  Used for file status and section counts.
-->
<script lang="ts">
  import BadgeAugment from "$common-webviews/src/design-system/components/BadgeAugment";
  import type { BadgeColor } from "$common-webviews/src/design-system/components/BadgeAugment/badge-types";

  // Props
  export let label: string | undefined = undefined;
  export let count: number | undefined = undefined;
  export let color: BadgeColor = "neutral";
  export let variant: "solid" | "soft" | "outline" = "soft";
  export let size: 1 | 2 | 3 = 1;
  export let title: string | undefined = undefined;

  // Determine what to display
  $: displayText = label !== undefined ? label : count !== undefined ? count.toString() : "";
  $: shouldShow = displayText !== "" && (count === undefined || count > 0);
</script>

<!-- Git Status Badge -->
{#if shouldShow}
  <div class="c-git-status-badge" {title}>
    <BadgeAugment.Root {color} {variant} {size}>
      {displayText}
    </BadgeAugment.Root>
  </div>
{/if}

<style>
  :global(.c-git-status-badge) {
    font-family: var(--ds-font-mono);
    font-weight: 600;
    min-width: 20px;
    text-align: center;
  }
</style>
