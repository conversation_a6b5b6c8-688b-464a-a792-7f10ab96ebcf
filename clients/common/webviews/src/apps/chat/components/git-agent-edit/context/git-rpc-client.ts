/**
 * Git RPC Client
 *
 * Provides webview-side RPC client for multi-repository git operations.
 * Uses the new namespaced message types that require repoRoot parameter.
 * Supports both single-repo (with default repoRoot) and multi-repo usage.
 */

import type { IExtensionClient } from "../../../extension-client";
import type {
  GitRepositoryState,
  GitStageFilesRequest,
  GitUnstageFilesRequest,
  GitCheckoutFilesRequest,
  GitResetToIndexFilesRequest,
  GitCommitRequest,
  GitFileState,
  GitRepositoryInfo,
  GitFileStatusOptions,
  GitOpenDiffRequest,
} from "@augment-internal/sidecar-libs/src/git/git-types";
import {
  GitMessageType,
  type GitRepoListRequest,
  type GitRepoListResponse,
  type GitRepoTrackRequest,
  type GitRepoTrackResponse,
  type GitRepoInfoRequest,
  type GitRepoInfoResponse,
  type GitRepoStateRequest,
  type GitRepoStateResponse,
  type GitRepoRefreshRequest,
  type GitRepoRefreshResponse,
  type GitRepoValidateRequest,
  type GitRepoValidateResponse,
  type GitFileStatusRequest,
  type GitFileStatusResponse,
  type GitFileStageRequest,
  type GitFileStageResponse,
  type GitFileUnstageRequest,
  type GitFileUnstageResponse,
  type GitFileCheckoutRequest,
  type GitFileCheckoutResponse,
  type GitFileResetToIndexRequest,
  type GitFileResetToIndexResponse,
  type GitCommitCreateRequest,
  type GitCommitCreateResponse,
  type GitDiffOpenRequest,
  type GitDiffOpenResponse,
} from "@augment-internal/sidecar-libs/src/git/git-messages";

/**
 * Git RPC operations interface for multi-repository support.
 * All operations require repoRoot parameter for repository identification.
 */
export interface IGitRPCClient {
  // Repository management
  /** List currently tracked repositories */
  listRepositories(): Promise<GitRepositoryInfo[]>;
  /** Track a repository for git operations */
  trackRepository(repoRoot: string): Promise<GitRepositoryInfo>;
  /** Get repository information */
  getRepositoryInfo(repoRoot: string): Promise<GitRepositoryInfo>;

  // Repository state operations
  /** Get repository state for specific repo */
  getRepositoryState(repoRoot: string): Promise<GitRepositoryState>;
  /** Refresh repository state for specific repo */
  refreshRepository(repoRoot: string): Promise<void>;
  /** Validate repository is properly initialized */
  validateRepository(repoRoot: string): Promise<boolean>;

  // File operations
  /** Get git status matrix with optional filtering */
  getStatusMatrix(repoRoot: string, options?: GitFileStatusOptions): Promise<GitFileState[]>;
  /** Stage multiple files */
  stageFiles(repoRoot: string, request: GitStageFilesRequest): Promise<void>;
  /** Unstage multiple files */
  unstageFiles(repoRoot: string, request: GitUnstageFilesRequest): Promise<void>;
  /** Checkout multiple files to a specific reference */
  checkoutFiles(repoRoot: string, request: GitCheckoutFilesRequest): Promise<void>;
  /** Reset multiple files to index state */
  resetToIndex(repoRoot: string, request: GitResetToIndexFilesRequest): Promise<void>;

  // Commit operations
  /** Commit staged changes */
  commit(repoRoot: string, request: GitCommitRequest): Promise<string>;

  // Diff operations
  /** Open git diff for file */
  openGitDiff(repoRoot: string, request: GitOpenDiffRequest): Promise<void>;

  // File operations
  /** Open file in editor */
  openFile(filePath: string): void;
}

/**
 * Implementation of Git RPC Client with multi-repository support.
 * Uses new namespaced message types with repoRoot parameter.
 */
export class GitRPCClient implements IGitRPCClient {
  constructor(private readonly _extensionClient: IExtensionClient) {
    if (!_extensionClient) {
      throw new Error("ExtensionClient is required for MultiRepoGitRPCClient");
    }
  }

  // Repository management
  async listRepositories(): Promise<GitRepositoryInfo[]> {
    const response = await this._extensionClient.sendGitMessage<
      GitRepoListRequest,
      GitRepoListResponse
    >({
      type: GitMessageType.REPO_LIST_REQUEST,
      data: {},
    });
    return response.data.repositories;
  }

  async trackRepository(repoRoot: string): Promise<GitRepositoryInfo> {
    const response = await this._extensionClient.sendGitMessage<
      GitRepoTrackRequest,
      GitRepoTrackResponse
    >({
      type: GitMessageType.REPO_TRACK_REQUEST,
      data: { repoRoot },
    });
    return response.data;
  }

  async getRepositoryInfo(repoRoot: string): Promise<GitRepositoryInfo> {
    const response = await this._extensionClient.sendGitMessage<
      GitRepoInfoRequest,
      GitRepoInfoResponse
    >({
      type: GitMessageType.REPO_INFO_REQUEST,
      data: { repoRoot },
    });
    return response.data;
  }

  // Repository state operations
  async getRepositoryState(repoRoot: string): Promise<GitRepositoryState> {
    const response = await this._extensionClient.sendGitMessage<
      GitRepoStateRequest,
      GitRepoStateResponse
    >({
      type: GitMessageType.REPO_STATE_REQUEST,
      data: { repoRoot },
    });
    return response.data;
  }

  async refreshRepository(repoRoot: string): Promise<void> {
    await this._extensionClient.sendGitMessage<GitRepoRefreshRequest, GitRepoRefreshResponse>({
      type: GitMessageType.REPO_REFRESH_REQUEST,
      data: { repoRoot },
    });
  }

  async validateRepository(repoRoot: string): Promise<boolean> {
    const response = await this._extensionClient.sendGitMessage<
      GitRepoValidateRequest,
      GitRepoValidateResponse
    >({
      type: GitMessageType.REPO_VALIDATE_REQUEST,
      data: { repoRoot },
    });
    return response.data.isValid;
  }

  // File operations
  async getStatusMatrix(repoRoot: string, options?: GitFileStatusOptions): Promise<GitFileState[]> {
    const response = await this._extensionClient.sendGitMessage<
      GitFileStatusRequest,
      GitFileStatusResponse
    >({
      type: GitMessageType.FILE_STATUS_REQUEST,
      data: { repoRoot, ...options },
    });
    return response.data.statusMatrix;
  }

  async stageFiles(repoRoot: string, request: GitStageFilesRequest): Promise<void> {
    await this._extensionClient.sendGitMessage<GitFileStageRequest, GitFileStageResponse>({
      type: GitMessageType.FILE_STAGE_REQUEST,
      data: { repoRoot, ...request },
    });
  }

  async unstageFiles(repoRoot: string, request: GitUnstageFilesRequest): Promise<void> {
    await this._extensionClient.sendGitMessage<GitFileUnstageRequest, GitFileUnstageResponse>({
      type: GitMessageType.FILE_UNSTAGE_REQUEST,
      data: { repoRoot, ...request },
    });
  }

  async checkoutFiles(repoRoot: string, request: GitCheckoutFilesRequest): Promise<void> {
    await this._extensionClient.sendGitMessage<GitFileCheckoutRequest, GitFileCheckoutResponse>({
      type: GitMessageType.FILE_CHECKOUT_REQUEST,
      data: { repoRoot, ...request },
    });
  }

  async resetToIndex(repoRoot: string, request: GitResetToIndexFilesRequest): Promise<void> {
    await this._extensionClient.sendGitMessage<
      GitFileResetToIndexRequest,
      GitFileResetToIndexResponse
    >({
      type: GitMessageType.FILE_RESET_TO_INDEX_REQUEST,
      data: { repoRoot, ...request },
    });
  }

  // Commit operations
  async commit(repoRoot: string, request: GitCommitRequest): Promise<string> {
    const response = await this._extensionClient.sendGitMessage<
      GitCommitCreateRequest,
      GitCommitCreateResponse
    >({
      type: GitMessageType.COMMIT_CREATE_REQUEST,
      data: { repoRoot, ...request },
    });
    return response.data.commitOid;
  }

  // Diff operations
  async openGitDiff(repoRoot: string, request: GitOpenDiffRequest): Promise<void> {
    await this._extensionClient.sendGitMessage<GitDiffOpenRequest, GitDiffOpenResponse>({
      type: GitMessageType.DIFF_OPEN_REQUEST,
      data: { repoRoot, ...request },
    });
  }

  // File operations
  openFile(filePath: string): void {
    // File opening doesn't require repository context
    // This delegates to the extension's file opening mechanism
    this._extensionClient.openFile({
      repoRoot: "", // Will be resolved by the extension
      pathName: filePath,
    });
  }
}
