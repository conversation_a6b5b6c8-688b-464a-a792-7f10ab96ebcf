/**
 * Domain Stores Index
 *
 * Exports all domain store models and their interfaces.
 */

// Flags domain model
export { createGitUIFlagsDomainModel, type IGitUIFlagsDomainModel } from "./flags-domain-model";

// File collections domain model
export {
  createFileCollectionsDomainModel,
  type IFileCollectionsDomainModel,
} from "./file-collections-domain-model";

// Operation status domain model
export {
  createOperationStatusDomainModel,
  type IOperationStatusDomainModel,
  GitOperationStatus,
  type GitOperationMetadata,
} from "./operation-status-domain-model";

// UI state domain model
export {
  createUIStateDomainModel,
  type IUIStateDomainModel,
  type GitUIState,
} from "./ui-state-domain-model";

// Repository state domain model
export {
  createRepositoryStateDomainModel,
  createRepositoryMetadataDomainModel,
  createRepositoryFilesDomainModel,
  createRepositoryOperationStateDomainModel,
  createRepositoryTimingDomainModel,
  type IRepositoryStateDomainModel,
  type IRepositoryMetadataDomainModel,
  type IRepositoryFilesDomainModel,
  type IRepositoryOperationStateDomainModel,
  type IRepositoryTimingDomainModel,
  type RepositoryMetadata,
} from "./repository-state-domain-model";
