<!--
  Repository Selector Component

  Dropdown selector for choosing the active repository in multi-repo git interface.
  Shows repository status and provides refresh functionality.
-->
<script lang="ts">
  import { createEventDispatcher } from "svelte";
  import type { MultiRepoOperationStatus } from "./context/multi-repo";

  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import DropdownMenuAugment from "$common-webviews/src/design-system/components/DropdownMenuAugment";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import ChevronDown from "$common-webviews/src/design-system/icons/chevron-down.svelte";
  import FolderIcon from "$common-webviews/src/design-system/icons/vscode/folder.svelte";
  import RefreshIcon from "$common-webviews/src/design-system/icons/update.svelte";
  import CheckIcon from "$common-webviews/src/design-system/icons/check.svelte";

  export let repositories: string[] = [];
  export let activeRepository: string | undefined = undefined;
  export let operationStatus: MultiRepoOperationStatus;
  export let onSelectRepository: (repoRoot: string) => void;
  export let onDiscoverRepositories: () => void;

  const dispatch = createEventDispatcher<{
    selectRepository: string;
    discoverRepositories: void;
  }>();

  // Helper function to get repository display name
  function getRepositoryDisplayName(repoRoot: string): string {
    const parts = repoRoot.split("/");
    return parts[parts.length - 1] || repoRoot;
  }

  // Helper function to get repository path for display
  function getRepositoryPath(repoRoot: string): string {
    return repoRoot;
  }

  // Check if operations are in progress
  $: isOperationInProgress = operationStatus !== "idle";
  $: isDiscovering = operationStatus === "discovering";
  $: isSelecting = operationStatus === "selecting";

  function handleSelectRepository(repoRoot: string) {
    if (!isOperationInProgress) {
      onSelectRepository(repoRoot);
      dispatch("selectRepository", repoRoot);
    }
  }

  function handleDiscoverRepositories() {
    if (!isOperationInProgress) {
      onDiscoverRepositories();
      dispatch("discoverRepositories");
    }
  }
</script>

<div class="c-repository-selector">
  <div class="c-repository-selector__main">
    <!-- Repository Dropdown -->
    <DropdownMenuAugment.Root>
      <DropdownMenuAugment.Trigger>
        <div
          class="c-repository-selector__trigger"
          class:c-repository-selector__trigger--disabled={isOperationInProgress}
        >
          <div class="c-repository-selector__icon">
            <FolderIcon />
          </div>
          <div class="c-repository-selector__content">
            <div class="c-repository-selector__name">
              <TextAugment size={1} weight="medium">
                {#if activeRepository}
                  {getRepositoryDisplayName(activeRepository)}
                {:else if isSelecting}
                  Selecting repository...
                {:else}
                  Select repository
                {/if}
              </TextAugment>
            </div>
            {#if activeRepository}
              <div class="c-repository-selector__path">
                <TextAugment size={0} color="neutral">
                  {getRepositoryPath(activeRepository)}
                </TextAugment>
              </div>
            {/if}
          </div>
          <div class="c-repository-selector__dropdown">
            <ChevronDown />
          </div>
        </div>
      </DropdownMenuAugment.Trigger>

      <DropdownMenuAugment.Content side="bottom" align="start">
        {#if repositories.length > 0}
          {#each repositories as repoRoot (repoRoot)}
            <DropdownMenuAugment.Item
              onSelect={() => handleSelectRepository(repoRoot)}
              highlight={activeRepository === repoRoot}
              class="c-repository-selector__dropdown-item"
            >
              <div class="c-repository-selector__dropdown-item-content">
                <div class="c-repository-selector__dropdown-item-name">
                  <TextAugment size={1}>
                    {getRepositoryDisplayName(repoRoot)}
                  </TextAugment>
                </div>
                <div class="c-repository-selector__dropdown-item-path">
                  <TextAugment size={0} color="neutral">
                    {getRepositoryPath(repoRoot)}
                  </TextAugment>
                </div>
              </div>
              {#if activeRepository === repoRoot}
                <div class="c-repository-selector__dropdown-item-check">
                  <CheckIcon />
                </div>
              {/if}
            </DropdownMenuAugment.Item>
          {/each}

          <DropdownMenuAugment.Separator />

          <DropdownMenuAugment.Item
            onSelect={handleDiscoverRepositories}
            disabled={isDiscovering}
            class="c-repository-selector__dropdown-refresh"
          >
            <div class="c-repository-selector__dropdown-refresh-content">
              <RefreshIcon />
              <TextAugment size={1}>
                {isDiscovering ? "Discovering..." : "Refresh repositories"}
              </TextAugment>
            </div>
          </DropdownMenuAugment.Item>
        {:else}
          <DropdownMenuAugment.Label>No repositories found</DropdownMenuAugment.Label>
          <DropdownMenuAugment.Item
            onSelect={handleDiscoverRepositories}
            disabled={isDiscovering}
            class="c-repository-selector__dropdown-refresh"
          >
            <div class="c-repository-selector__dropdown-refresh-content">
              <RefreshIcon />
              <TextAugment size={1}>
                {isDiscovering ? "Discovering..." : "Discover repositories"}
              </TextAugment>
            </div>
          </DropdownMenuAugment.Item>
        {/if}
      </DropdownMenuAugment.Content>
    </DropdownMenuAugment.Root>
  </div>

  <!-- Refresh Button -->
  <div class="c-repository-selector__actions">
    <IconButtonAugment
      title="Refresh repositories"
      variant="ghost"
      color="neutral"
      size={1}
      disabled={isOperationInProgress}
      on:click={handleDiscoverRepositories}
    >
      <RefreshIcon />
    </IconButtonAugment>
  </div>
</div>

<style>
  .c-repository-selector {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-2);
    width: 100%;
  }

  .c-repository-selector__main {
    flex: 1;
    min-width: 0;
  }

  .c-repository-selector__trigger {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-2);
    padding: var(--ds-spacing-2) var(--ds-spacing-3);
    border: 1px solid var(--ds-color-neutral-1);
    border-radius: 6px;
    background: var(--ds-color-neutral-0);
    cursor: pointer;
    transition: border-color 0.2s ease;
    width: 100%;
  }

  .c-repository-selector__trigger:hover:not(.c-repository-selector__trigger--disabled) {
    border-color: var(--ds-color-neutral-2);
  }

  .c-repository-selector__trigger--disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  .c-repository-selector__icon {
    flex-shrink: 0;
    color: var(--ds-color-neutral-3);
  }

  .c-repository-selector__content {
    flex: 1;
    min-width: 0;
    text-align: left;
  }

  .c-repository-selector__name {
    line-height: 1.2;
  }

  .c-repository-selector__path {
    line-height: 1.2;
    margin-top: 1px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .c-repository-selector__dropdown {
    flex-shrink: 0;
    color: var(--ds-color-neutral-3);
  }

  .c-repository-selector__actions {
    flex-shrink: 0;
  }

  :global(.c-repository-selector__dropdown-item) {
    padding: var(--ds-spacing-2) var(--ds-spacing-3) !important;
  }

  .c-repository-selector__dropdown-item-content {
    flex: 1;
    min-width: 0;
  }

  .c-repository-selector__dropdown-item-name {
    line-height: 1.2;
  }

  .c-repository-selector__dropdown-item-path {
    line-height: 1.2;
    margin-top: 1px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .c-repository-selector__dropdown-item-check {
    flex-shrink: 0;
    color: var(--ds-color-accent-3);
    margin-left: var(--ds-spacing-2);
  }

  :global(.c-repository-selector__dropdown-refresh) {
    border-top: 1px solid var(--ds-color-neutral-1);
  }

  .c-repository-selector__dropdown-refresh-content {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-2);
  }
</style>
