<!--
  Git Section Staged

  Component for displaying staged changes section.
  Shows files that have been staged.
  Redesigned to use CollapsibleAugment for better UX.
-->
<script lang="ts">
  import { GitAgentEditModel } from "../context/model";
  import GitFileList from "./GitFileList.svelte";
  import GitSectionSummary from "./GitSectionSummary.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import { TooltipTriggerOn } from "$common-webviews/src/design-system/_primitives/TooltipAugment/types";
  import CollapsibleAugment from "$common-webviews/src/design-system/components/CollapsibleAugment/CollapsibleAugment.svelte";
  import CollapseButtonAugment from "$common-webviews/src/design-system/components/CollapsibleAugment/CollapseButtonAugment.svelte";

  import MinusIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/minus.svg?component";
  import ChevronDown from "$common-webviews/src/design-system/icons/chevron-down.svelte";
  import GitChangeSummary from "./GitChangeSummary.svelte";

  // Get single-repo git context (set by GitRepositoryCard)
  const { model, controller } = GitAgentEditModel.requireContext();

  // Reactive state from single-repo model
  $: stagedFiles = model.fileCollectionsModel.stagedFiles;
  $: stagedFilesWithUI = model.fileCollectionsModel.stagedFilesWithUI;

  // Event handlers
  async function handleUnstageAll() {
    if (!controller) return;
    try {
      await controller.unstageAllStaged();
    } catch (error) {
      console.error("Failed to unstage all staged files:", error);
    }
  }

  // Determine if we have staged files
  $: hasStagedFiles = ($stagedFiles?.length ?? 0) > 0;
</script>

<!-- Staged Changes Section -->
{#if hasStagedFiles}
  <div class="c-git-section-staged c-git-section-staged__wrapper">
    <CollapsibleAugment>
      <svelte:fragment slot="header">
        <div class="c-git-section-staged__header">
          <div class="c-git-section-staged__header-left">
            <CollapseButtonAugment>
              <svelte:fragment let:collapsed>
                <div
                  class="c-expand-toggle-button"
                  class:c-expand-toggle-button--active={!collapsed}
                >
                  <ChevronDown />
                </div>
              </svelte:fragment>
            </CollapseButtonAugment>
            <TextTooltipAugment
              triggerOn={[TooltipTriggerOn.Hover]}
              content="Staged files: Files that are reviewed, approved, and ready to commit to git"
            >
              <TextAugment size={1} weight="bold" class="c-git-section-staged__title">
                Reviewed and Approved
              </TextAugment>
            </TextTooltipAugment>

            <!-- Summary of changes -->
            <GitSectionSummary files={$stagedFiles} changeType="staged" />
          </div>
          <div class="c-git-section-staged__controls">
            <TextTooltipAugment
              triggerOn={[TooltipTriggerOn.Hover]}
              content="Mark all changes as unapproved and not ready to commit"
            >
              <IconButtonAugment variant="ghost" size={1} on:click={handleUnstageAll}>
                <MinusIcon />
              </IconButtonAugment>
            </TextTooltipAugment>
          </div>
        </div>
      </svelte:fragment>

      <!-- File List Content -->
      <div class="c-git-section-staged__content">
        <GitFileList
          files={$stagedFilesWithUI}
          showStageAction={false}
          showUnstageAction={true}
          onViewDiff={(file) => controller?.openGitDiff(file.qualifiedPathName.relPath, true)}
        >
          <GitChangeSummary
            slot="changeSummary"
            let:file
            addedLines={file.changesSummary?.stagedChanges?.addedLines || 0}
            removedLines={file.changesSummary?.stagedChanges?.removedLines || 0}
          />
        </GitFileList>
      </div>
    </CollapsibleAugment>
  </div>
{/if}

<style>
  .c-git-section-staged.c-git-section-staged__wrapper {
    display: contents;
  }

  /* Collapsible styling - extracted from nested selector for Svelte 5 compatibility */
  :global(.c-git-section-staged.c-git-section-staged__wrapper .c-collapsible) {
    --collapsible-panel-background: transparent;
    --collapsible-panel-border: none;
    --collapsible-panel-radius: 0;
    --collapsible-panel-border-color: none;
  }

  /* Expand toggle button styling - extracted from nested selector for Svelte 5 compatibility */
  :global(.c-git-section-staged .c-expand-toggle-button svg) {
    width: 14px;
    height: 14px;
    transform: rotate(-90deg);
    transition: transform 0.2s ease;
  }

  :global(.c-git-section-staged .c-expand-toggle-button--active svg) {
    transform: rotate(0deg);
  }

  .c-git-section-staged__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--ds-spacing-1);
    padding: var(--ds-spacing-1);
    width: 100%;
  }

  .c-git-section-staged__header-left {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-1);
    flex: 1;
    min-width: 0;
  }

  .c-git-section-staged__header :global(.c-git-section-staged__title) {
    flex: 1;
    min-width: 0;
  }

  .c-git-section-staged__controls {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-1);
    flex-shrink: 0;
  }

  .c-git-section-staged__content {
    padding: var(--ds-spacing-1);
  }
</style>
