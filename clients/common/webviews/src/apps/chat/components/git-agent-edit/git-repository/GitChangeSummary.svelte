<!--
  Git Change Summary

  Simple component for displaying change statistics (lines added/removed).
  Direct API mapping: addedLines and removedLines map to green and red badges.
-->
<script lang="ts">
  import StatusBadgeAugment from "$common-webviews/src/design-system/components/StatusBadgeAugment.svelte";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import { TooltipTriggerOn } from "$common-webviews/src/design-system/_primitives/TooltipAugment/types";

  // Simple API: just pass the numbers directly
  export let addedLines: number = 0;
  export let removedLines: number = 0;

  $: hasChanges = addedLines > 0 || removedLines > 0;
</script>

<!-- Git Change Summary -->
{#if hasChanges}
  <div class="c-git-change-summary">
    {#if addedLines > 0}
      <TextTooltipAugment triggerOn={[TooltipTriggerOn.Hover]} content="{addedLines} lines added">
        <StatusBadgeAugment color="success" size={1}>
          +{addedLines}
        </StatusBadgeAugment>
      </TextTooltipAugment>
    {/if}
    {#if removedLines > 0}
      <TextTooltipAugment
        triggerOn={[TooltipTriggerOn.Hover]}
        content="{removedLines} lines removed"
      >
        <StatusBadgeAugment color="error" size={1}>
          -{removedLines}
        </StatusBadgeAugment>
      </TextTooltipAugment>
    {/if}
  </div>
{/if}

<style>
  .c-git-change-summary {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-1);
    flex-shrink: 0;
  }
</style>
