<script lang="ts">
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import { TooltipTriggerOn } from "$common-webviews/src/design-system/components/types";
  import Expand from "$common-webviews/src/design-system/icons/augment/expand.svelte";
  /**
   * This component is used to display an icon that can be expanded or collapsed. It can be controlled by the expandable and collapsed props.
   * In addition its icon can change when hovered if expandable, by setting the css variables.
   *```css
   * --collapsible-icon-visibility: hidden;
   * --collapsible-icon-expand-visibility: visible;
   *```
   * To prevent the icon from changing on hover, set data-no-hover on the parent element. and use a rule like.
   * ```css
   *  :global(.c-checkpoint-header:has([data-no-hover]:hover)) {
   *  --collapsible-icon-visibility: visible;
   *  --collapsible-icon-expand-visibility: hidden;
   * }
   * ```
   *
   * this should allow components to control the visibility of the icons and the hover state.
   *
   *
   * @Anton -- suggests using position absolute * var(--collapsible-icon) so that if its 0 the supplied is visible and 1 the expand icon is visible. This would
   * reduce it to 1 variable.  This would make it 1 variable, however it would require overflow and positioning changes.
   */
  export let expandable: boolean = true;
  export let collapsed: boolean = false;
</script>

<TextTooltipAugment
  content={expandable ? (collapsed ? "Expand" : "Collapse") : ""}
  triggerOn={[TooltipTriggerOn.Hover]}
>
  <div class="c-collapsible-icon" class:is-not-expandable={!expandable}>
    <span class="c-collapsible-icon-wrapper c-collapsible-icon-expand">
      <Expand />
    </span>
    <span class="c-collapsible-icon-wrapper">
      <slot name="icon" />
    </span>
  </div>
</TextTooltipAugment>

<style>
  .c-collapsible-icon {
    display: grid;
    grid-template-areas: "stack";
    color: var(--ds-color-neutral-a11);
    align-items: center;
    justify-content: center;
    flex: 0;
    &:hover {
      /**
        these can be set down stream to indicate expand/collapse
      **/
      --collapsible-icon-visibility: hidden;
      --collapsible-icon-expand-visibility: visible;
    }
    /**
    if not expandable than we will not switch beween icons.
    */
    &.is-not-expandable,
    &.is-not-expandable:hover {
      --collapsible-icon-visibility: visible;
      --collapsible-icon-expand-visibility: hidden;
      --collapsible-icon-cursor: default;
    }
  }

  .c-collapsible-icon-wrapper {
    --icon-size: 100%;
    grid-area: stack;
    padding: 1px;
    width: 16px;
    height: 16px;
    aspect-ratio: 1/1;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    visibility: var(--collapsible-icon-visibility, visible);
    cursor: var(--collapsible-icon-cursor, pointer);
    &.c-collapsible-icon-expand {
      visibility: var(--collapsible-icon-expand-visibility, hidden);
    }
  }
</style>
