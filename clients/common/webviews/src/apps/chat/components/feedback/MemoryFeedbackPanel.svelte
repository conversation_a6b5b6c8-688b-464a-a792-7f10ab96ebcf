<script lang="ts">
  import { onKey } from "../../../../common/utils/keypress";
  import { FeedbackRating } from "../../types/feedback-rating";
  import type { FeedbackState } from "../../types/feedback-state";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import CardAugment from "$common-webviews/src/design-system/components/CardAugment.svelte";
  import CopyButton from "$common-webviews/src/common/components/CopyButton.svelte";
  import SuccessfulDropdownItem from "$common-webviews/src/common/components/SuccessfulDropdownItem.svelte";
  import Clipboard from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/clipboard.svg?component";
  import ThumbsDown from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/thumbs-down.svg?component";
  import ThumbsUp from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/thumbs-up.svg?component";
  import StarIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/star.svg?component";
  import MenuIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/ellipsis.svg?component";
  import DropdownMenuAugment from "$common-webviews/src/design-system/components/DropdownMenuAugment";
  import { TooltipTriggerOn } from "$common-webviews/src/design-system/_primitives/TooltipAugment/types";
  import { isVSCodeHost } from "$common-webviews/src/common/hosts/vscode/vscode";
  import { host } from "$common-webviews/src/common/hosts/host";
  import { WebViewMessageType } from "$vscode/src/webview-providers/webview-messages";
  import { type TooltipItem } from "../conversation/MessageActions.svelte";
  import TextAreaAugment from "$common-webviews/src/design-system/components/TextAreaAugment.svelte";
  import { tick } from "svelte";

  const MARKETPLACE_REVIEW_CLICKED_KEY = "augment-marketplace-review-clicked";

  export let requestId: string;
  export let feedbackState: FeedbackState | undefined; // Current state in the backend
  export let onSendUserRating: (
    requestId: string,
    rating: FeedbackRating,
    note?: string,
  ) => Promise<void>;
  export let showFeedbackInput: boolean = false;
  export let reponseText: string | undefined;
  export let tooltipItems: TooltipItem[] = [];
  let feedbackToSend: FeedbackState = {
    selectedRating: feedbackState?.selectedRating ?? FeedbackRating.unset,
    feedbackNote: feedbackState?.feedbackNote ?? "",
  };
  // Persistent state to make button appear after clicking review
  let shouldShowReviewButton = false;
  let requestClose: () => void = () => {};
  let textAreaElement: HTMLTextAreaElement;

  // Check if user has clicked the marketplace review button before
  function hasClickedMarketplaceReview(): boolean {
    try {
      return localStorage.getItem(MARKETPLACE_REVIEW_CLICKED_KEY) === "true";
    } catch {
      // Fallback if localStorage is not available
      return true;
    }
  }

  // Set localStorage state when user clicks the review button
  function setMarketplaceReviewClicked(): void {
    try {
      localStorage.setItem(MARKETPLACE_REVIEW_CLICKED_KEY, "true");
    } catch {
      // Ignore if localStorage is not available
    }
  }

  $: reviewLink = isVSCodeHost()
    ? "https://marketplace.visualstudio.com/items?itemName=augment.vscode-augment&ssr=false#review-details"
    : "https://plugins.jetbrains.com/plugin/24072-augment/reviews";

  function handleSendFeedback() {
    showFeedbackInput = false;

    onSendUserRating(requestId, feedbackToSend.selectedRating, feedbackToSend.feedbackNote);
    showThankYouNote();
  }

  async function onFeedbackClick(rating: FeedbackRating) {
    feedbackToSend.selectedRating = rating;
    showFeedbackInput = true;
    // Focus the text area after it's rendered
    await tick();
    textAreaElement?.focus();
  }

  function cancelFeedback() {
    showFeedbackInput = false;

    // Reset feedback to the last known state
    if (feedbackState) {
      feedbackToSend = { ...feedbackState };
    } else {
      feedbackToSend.selectedRating = FeedbackRating.unset;
    }
  }

  function handleTextAreaKeydown(event: KeyboardEvent) {
    if (event.key === "Escape") {
      event.preventDefault();
      cancelFeedback();
    }
  }

  let thankYouNote = "";
  let thankYouTimeout: ReturnType<typeof setTimeout> | undefined;
  function showThankYouNote() {
    thankYouNote = "Thanks for the feedback!";
    clearTimeout(thankYouTimeout);
    thankYouTimeout = setTimeout(() => {
      thankYouNote = "";
    }, 3000);
  }

  function scrollOnShow(node: HTMLElement) {
    node.scrollIntoView({
      block: "end",
      behavior: "smooth",
    });
  }
</script>

<div class="c-feedback">
  <div class="c-feedback__ratings">
    <div class="c-feedback__buttons">
      <TextTooltipAugment content="Give feedback">
        <IconButtonAugment
          class={`c-feedback__positive ${feedbackToSend.selectedRating === FeedbackRating.positive ? "c-feedback--selected" : ""}`}
          title="Feedback Positive"
          variant="ghost-block"
          size={1}
          color={feedbackToSend.selectedRating === FeedbackRating.positive ? "success" : "neutral"}
          on:keyup={onKey("Enter", () => onFeedbackClick(FeedbackRating.positive))}
          on:click={() => onFeedbackClick(FeedbackRating.positive)}
        >
          <ThumbsUp />
        </IconButtonAugment>
      </TextTooltipAugment>
      <TextTooltipAugment content="Give feedback">
        <IconButtonAugment
          class={`c-feedback__negative ${feedbackToSend.selectedRating === FeedbackRating.negative ? "c-feedback--selected" : ""}`}
          title="Feedback Negative"
          variant="ghost-block"
          size={1}
          color={feedbackToSend.selectedRating === FeedbackRating.negative ? "error" : "neutral"}
          on:keyup={onKey("Enter", () => onFeedbackClick(FeedbackRating.negative))}
          on:click={() => onFeedbackClick(FeedbackRating.negative)}
        >
          <ThumbsDown />
        </IconButtonAugment>
      </TextTooltipAugment>
      {#if reponseText}
        <CopyButton text={reponseText} tooltip="Copy response" />
      {/if}
      <!-- Dropdown menu for additional options -->
      <DropdownMenuAugment.Root bind:requestClose>
        <DropdownMenuAugment.Trigger>
          <TextTooltipAugment content="More options" triggerOn={[TooltipTriggerOn.Hover]}>
            <IconButtonAugment title="More options" variant="ghost-block" size={1} color="neutral">
              <MenuIcon />
            </IconButtonAugment>
          </TextTooltipAugment>
        </DropdownMenuAugment.Trigger>

        <DropdownMenuAugment.Content size={1} side="right" align="end">
          {#if tooltipItems.length > 0}
            {#each tooltipItems as item}
              <SuccessfulDropdownItem
                icon={item.icon}
                idleLabel={item.label}
                successLabel={item.successMessage ?? `${item.label} completed`}
                disabled={item.disabled}
                onSelect={async () => {
                  item.action(new Event("click"));
                }}
              />
            {/each}
          {/if}
          <SuccessfulDropdownItem
            icon={Clipboard}
            idleLabel="Copy request ID"
            successLabel="Copied request ID"
            onSelect={async () => {
              await navigator.clipboard?.writeText(requestId);
            }}
          />
        </DropdownMenuAugment.Content>
      </DropdownMenuAugment.Root>
    </div>
    {#if feedbackToSend.selectedRating === FeedbackRating.positive && (shouldShowReviewButton || !hasClickedMarketplaceReview())}
      <ButtonAugment
        size={1}
        on:click={() => {
          // Persist the review button in local state after clicking in case user wants to go back to it
          shouldShowReviewButton = true;
          // Set localStorage state to indicate user has clicked the review button
          setMarketplaceReviewClicked();
          host.postMessage({
            type: WebViewMessageType.augmentLink,
            data: reviewLink,
          });
        }}
        variant="ghost"
        color="neutral"
      >
        <StarIcon slot="iconLeft" />
        Leave a review for Augment!
      </ButtonAugment>
    {:else}
      <span class="c-feedback__thankyou">{thankYouNote}</span>
    {/if}
  </div>
  {#if showFeedbackInput}
    <div class="c-feedback__input-container">
      <CardAugment size={2}>
        <TextAreaAugment
          class="c-feedback__textarea"
          rows={3}
          placeholder="Enter your feedback..."
          color="neutral"
          variant="soft"
          bind:value={feedbackToSend.feedbackNote}
          bind:textInput={textAreaElement}
          on:keydown={handleTextAreaKeydown}
        />
        <div class="l-feedback__send" use:scrollOnShow>
          <CopyButton text={requestId} tooltip="Copy request ID">
            <svelte:fragment slot="text">Request ID</svelte:fragment>
          </CopyButton>
          <div class="l-feedback__send-btns">
            <ButtonAugment
              title="Cancel"
              variant="soft"
              color="neutral"
              on:keyup={onKey("Enter", cancelFeedback)}
              on:click={cancelFeedback}
              size={1}
            >
              Cancel
            </ButtonAugment>
            <ButtonAugment
              title="Share "
              color="accent"
              disabled={!feedbackToSend.feedbackNote.trim()}
              on:keyup={onKey("Enter", handleSendFeedback)}
              on:click={handleSendFeedback}
              size={1}
            >
              {feedbackState ? "Update feedback" : "Share feedback"}
            </ButtonAugment>
          </div>
        </div>
      </CardAugment>
    </div>
  {/if}
</div>

<style>
  .c-feedback {
    grid-area: 1 / 1 / 4 / 4;
    display: flex;
    flex-direction: column;
    width: 100%;
  }

  .c-feedback__input-container {
    margin-top: var(--ds-spacing-2);
  }

  .c-feedback :global(.c-feedback__textarea) {
    width: 100%;
    min-height: 75px;
    resize: none;
    font-size: inherit;
    color: inherit;
    margin-top: 5px;
    margin-bottom: 5px;
    font-size: 12px;
  }

  .c-feedback__buttons {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: var(--ds-spacing-2);
  }

  .c-feedback__buttons :global(.c-icon-btn) {
    --icon-btn-size: var(--ds-spacing-4_5);
  }

  .l-feedback__send {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: var(--ds-spacing-2);
    padding-top: var(--ds-spacing-2);
  }

  /* Fix text wrapping in CopyButton (Request ID button) */
  .l-feedback__send :global(.c-base-btn) {
    flex: 1 1 auto;
  }

  .l-feedback__send-btns {
    display: flex;
    flex-direction: row;
    gap: var(--ds-spacing-1);
  }
  .c-feedback__thankyou {
    flex: 1 1 auto;
    text-align: right;
  }

  .c-feedback__ratings {
    display: flex;
    justify-content: space-between;
  }

  .c-feedback__ratings :global(.c-feedback__positive:not([disabled]):hover),
  .c-feedback__ratings :global(.c-feedback__positive.c-feedback--selected) {
    color: var(
      --vscode-gitDecoration-addedResourceForeground,
      var(--intellij-progressBar-passedColor)
    );
  }

  .c-feedback__ratings :global(.c-feedback__negative:not([disabled]):hover),
  .c-feedback__ratings :global(.c-feedback__negative.c-feedback--selected) {
    color: var(
      --vscode-gitDecoration-deletedResourceForeground,
      var(--intellij-progressBar-failedColor)
    );
  }
</style>
