<script lang="ts">
  import { onKey } from "../../../../common/utils/keypress";
  import XMarkIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/xmark.svg?component";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import CardAugment from "$common-webviews/src/design-system/components/CardAugment.svelte";
  import CopyButton from "$common-webviews/src/common/components/CopyButton.svelte";
  import TextAreaAugment from "$common-webviews/src/design-system/components/TextAreaAugment.svelte";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import type { FeedbackState } from "../../types/feedback-state";
  import { scrollOnShow } from "$common-webviews/src/common/actions/scrollOnShow";

  export let requestId: string;
  export let onSubmit: (feedback: string) => void;
  export let showFeedbackForm: boolean = false;
  export let feedbackState: FeedbackState | undefined;

  let feedback = feedbackState?.feedbackNote ?? "";

  function sendFeedback() {
    onSubmit(`\nFeedback: ${feedback}\n`);
  }

  function cancel() {
    showFeedbackForm = false;
  }

</script>

<div class="c-feedback-form-container">
  <CardAugment size={0} variant="surface">
    <div class="c-feedback-form">
      <TextAreaAugment
        name="explanation"
        rows={3}
        placeholder="Tell us what you think of Augment..."
        color="neutral"
        variant="soft"
        label="Give Feedback"
        required
        bind:value={feedback}
      >
        <IconButtonAugment
          slot="topRightAction"
          title="Cancel"
          variant="ghost-block"
          color="neutral"
          on:keyup={onKey("Enter", cancel)}
          on:click={cancel}
          size={0.5}
        >
          <XMarkIcon/>
        </IconButtonAugment>
      </TextAreaAugment>
      <div class="l-feedback__send" use:scrollOnShow>
        <CopyButton text={requestId} tooltip="Copy request ID" />
        <div class="l-feedback__send-btns">
          <ButtonAugment
            type="submit"
            title="Share "
            color="accent"
            disabled={feedback.trim().length === 0}
            size={1}
            on:keyup={onKey("Enter", sendFeedback)}
            on:click={sendFeedback}
          >
            Submit
          </ButtonAugment>
        </div>
      </div>
    </div>
  </CardAugment>
</div>

<style>
  .c-feedback-form-container {
    margin-top: var(--ds-spacing-2);
  }
  .c-feedback-form {
    grid-gap: var(--ds-spacing-2);
    display: flex;
    flex-direction: column;
    width: 100%;
    --text-area-label-font-size: var(--ds-font-size-1);
    --text-area-font-size: var(--ds-font-size-1);
  }

  .l-feedback__send {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: var(--ds-spacing-2);
  }

  .l-feedback__send-btns {
    display: flex;
    flex-direction: row;
    gap: var(--ds-spacing-1);
  }
</style>
