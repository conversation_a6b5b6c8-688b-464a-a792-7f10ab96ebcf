<script lang="ts">
  import { onKey } from "$common-webviews/src/common/utils/keypress";
  import { FeedbackRating } from "../../types/feedback-rating";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import CopyButton from "$common-webviews/src/common/components/CopyButton.svelte";
  import SeparatorAugment from "$common-webviews/src/design-system/components/SeparatorAugment.svelte";
  import Clipboard from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/clipboard.svg?component";
  import ThumbsDown from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/thumbs-down.svg?component";
  import ThumbsUp from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/thumbs-up.svg?component";
  import MessageIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/message-lines.svg?component";
  import BugIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/bug.svg?component";
  import StarIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/star.svg?component";
  import DropdownMenuAugment from "$common-webviews/src/design-system/components/DropdownMenuAugment";
  import { isVSCodeHost } from "$common-webviews/src/common/hosts/vscode/vscode";
  import { host } from "$common-webviews/src/common/hosts/host";
  import { WebViewMessageType } from "$vscode/src/webview-providers/webview-messages";
  import ShareButton from "$common-webviews/src/common/components/ShareButton.svelte";
  import BugReportForm from "./BugReportForm.svelte";
  import FeedbackForm from "./FeedbackForm.svelte";
  import { ChatMode } from "@augment-internal/sidecar-libs/src/chat/chat-types";
  import { getChatModel } from "../../chat-context";
  import type { FeedbackState } from "../../types/feedback-state";

  const MARKETPLACE_REVIEW_CLICKED_KEY = "augment-marketplace-review-clicked";

  export let requestId: string;
  export let showFeedbackInput: boolean = false;
  export let reponseText: string | undefined;

  export let feedbackMetadata: (requestId: string, note?: string) => { chatMode: ChatMode; note: string; } = (_requestId, note = "") => ({ chatMode: ChatMode.chat, note });
  
  let feedbackState: FeedbackState | undefined;
  let feedbackToSend: FeedbackState = {
    selectedRating: FeedbackRating.unset,
    feedbackNote: "",
  };

  $: conversationModel = $chatModel?.currentConversationModel;
  $: feedbackState = $conversationModel?.feedbackStates[requestId]
  $: feedbackToSend = {
    selectedRating: feedbackState?.selectedRating ?? FeedbackRating.unset,
    feedbackNote: feedbackState?.feedbackNote ?? "",
  }

  let isFeedbackPositive = false;
  // Persistent state to make button appear after clicking review
  let showBugreportForm = false;
  let showFeedbackForm = false;
  let shouldShowReviewButton = false;

  

  const chatModel = getChatModel();

  let note = "";
  let thankYouTimeout: ReturnType<typeof setTimeout> | undefined = undefined;
  const thankyouNote = 'Thanks for the feedback!';
  const issueNote = 'Your issue was reported!';

  // Check if user has clicked the marketplace review button before
  function hasClickedMarketplaceReview(): boolean {
    try {
      return localStorage.getItem(MARKETPLACE_REVIEW_CLICKED_KEY) === "true";
    } catch {
      // Fallback if locaˆlStorage is not available
      return true;
    }
  }

  // Set localStorage state when user clicks the review button
  function setMarketplaceReviewClicked(): void {
    try {
      localStorage.setItem(MARKETPLACE_REVIEW_CLICKED_KEY, "true");
    } catch {
      // Ignore if localStorage is not available
    }
  }

  function sendFeedback(rating: FeedbackRating, feedback?: string) {
    showBugreportForm = false;
    showFeedbackForm = false;
    showFeedbackInput = false;

    const { chatMode, note } = feedbackMetadata(requestId, feedback);
    return $chatModel.extensionClient.sendUserRating(
        requestId,
        chatMode,
        rating,
        note,
      );
  }

  function reportIssue() {
    showBugreportForm = true;
    showFeedbackForm = false;
    showFeedbackInput = true;
  }

  async function giveFeedback() {
    showBugreportForm = false;
    showFeedbackForm = true;
    showFeedbackInput = true;
  }

  function leaveReview() {
    showBugreportForm = false;
    showFeedbackForm = false;
    showFeedbackInput = false;
    shouldShowReviewButton = true;
    // Set localStorage state to indicate user has clicked the review button
    setMarketplaceReviewClicked();
    host.postMessage({
      type: WebViewMessageType.augmentLink,
      data: reviewLink,
    });
  }

  async function positiveFeedbackClick() {
    feedbackToSend.selectedRating = FeedbackRating.positive;
    return sendFeedback(feedbackToSend.selectedRating);
  }

  async function negativeFeedbackClick() {
    feedbackToSend.selectedRating = FeedbackRating.negative;
    return sendFeedback(feedbackToSend.selectedRating);
  }

  async function onSubmitBugreportForm(bugreport: string) {
    showBugreportForm = false;
    await sendFeedback(FeedbackRating.negative, bugreport);
    showFeedbackNote(FeedbackRating.negative)
  }

  async function onSubmitFeedbackForm(feedback: string) {
    showFeedbackForm = false;
    await sendFeedback(FeedbackRating.unset, feedback);
    showFeedbackNote(FeedbackRating.unset);
  }

  function showFeedbackNote(feedback: FeedbackRating) {
    note = feedback === FeedbackRating.negative ? issueNote : thankyouNote;
    clearTimeout(thankYouTimeout);
    thankYouTimeout = setTimeout(() => {
      note = "";
    }, 3000);
  }

  $: isFeedbackPositive = feedbackToSend.selectedRating === FeedbackRating.positive;
  $: isFeedbackNegative = feedbackToSend.selectedRating === FeedbackRating.negative;
  $: reviewLink = isVSCodeHost()
    ? "https://marketplace.visualstudio.com/items?itemName=augment.vscode-augment&ssr=false#review-details"
    : "https://plugins.jetbrains.com/plugin/24072-augment/reviews";
</script>

<div class="c-feedback-panel">
  <div class="c-feedback__ratings">
    <div class="c-feedback__buttons">
      <ShareButton tooltip="Share"/>
      {#if reponseText}
        <CopyButton text={reponseText} tooltip="Copy response" />
      {/if}
      <SeparatorAugment orientation="vertical" />
      <IconButtonAugment
        class={`c-feedback__positive ${isFeedbackPositive ? "c-feedback--selected" : ""}`}
        title="Feedback Positive"
        variant="ghost-block"
        size={1}
        color={isFeedbackPositive ? "success" : "neutral"}
        on:keyup={onKey("Enter", positiveFeedbackClick)}
        on:click={positiveFeedbackClick}
      >
        <ThumbsUp />
      </IconButtonAugment>
      <DropdownMenuAugment.Root open={showBugreportForm ? false : undefined} nested={false}>
        <DropdownMenuAugment.Trigger>
          <IconButtonAugment
            class={`c-feedback__negative ${isFeedbackNegative ? "c-feedback--selected" : ""}`}
            title="Feedback Negative"
            variant="ghost-block"
            size={1}
            color={isFeedbackNegative ? "error" : "neutral"}
            on:keyup={onKey("Enter", negativeFeedbackClick)}
            on:click={negativeFeedbackClick}
          >
            <ThumbsDown />
          </IconButtonAugment>
        </DropdownMenuAugment.Trigger>

        <DropdownMenuAugment.Content size={1} side="bottom" align="start">
          <DropdownMenuAugment.Item
            onSelect={reportIssue}
          >
            <svelte:component this={Clipboard} slot="iconLeft" />
            Report an issue
          </DropdownMenuAugment.Item>
        </DropdownMenuAugment.Content>
      </DropdownMenuAugment.Root>
      <DropdownMenuAugment.Root nested={false}>
        <DropdownMenuAugment.Trigger>
          <ButtonAugment size={0.5} variant="ghost-block">
            <svelte:component this={MessageIcon} slot="iconLeft" />
            {#if note}
              {note}
            {:else}
              Feedback
            {/if}
          </ButtonAugment>
        </DropdownMenuAugment.Trigger>
        <DropdownMenuAugment.Content size={1} side="bottom" align="center">
          <DropdownMenuAugment.Item
            onSelect={giveFeedback}
          >
            <svelte:component this={ThumbsUp} slot="iconLeft" />
            {#if feedbackState?.feedbackNote}
              Update feedback
            {:else}
              Give feedback
            {/if}
          </DropdownMenuAugment.Item>
          <DropdownMenuAugment.Item
            onSelect={reportIssue}
          >
            <svelte:component this={BugIcon} slot="iconLeft" />
            Report an issue
          </DropdownMenuAugment.Item>
          {#if shouldShowReviewButton || !hasClickedMarketplaceReview()}
            <DropdownMenuAugment.Separator/>
            <DropdownMenuAugment.Item
              onSelect={leaveReview}
            >
              <svelte:component this={StarIcon} slot="iconLeft" />
              Leave a Review
            </DropdownMenuAugment.Item>
          {/if}
        </DropdownMenuAugment.Content>
      </DropdownMenuAugment.Root>
    </div>
  </div>
  {#if showBugreportForm}
    <BugReportForm
      bind:showBugreportForm
      {requestId}
      onSubmit={onSubmitBugreportForm}
    />
  {/if}
  {#if showFeedbackForm}
    <FeedbackForm
      bind:showFeedbackForm
      {requestId}
      {feedbackState}
      onSubmit={onSubmitFeedbackForm}
    />
  {/if}
</div>

<style>
  .c-feedback-panel {
    display: flex;
    flex-direction: column;
    width: 100%;
    --icon-size: var(--ds-icon-size-0);
  }

  .c-feedback__buttons {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: var(--ds-spacing-2);
  }

  .c-feedback__buttons :global(.c-icon-btn) {
    --icon-btn-size: var(--ds-spacing-4_5);
  }

  :global(.c-feedback__negative svg) {
    transform: scale(-1, 1);
  }

  .c-feedback__ratings {
    display: flex;
    justify-content: space-between;
  }

  .c-feedback__ratings :global(.c-feedback__positive:not([disabled]):hover),
  .c-feedback__ratings :global(.c-feedback__positive.c-feedback--selected) {
    color: var(
      --vscode-gitDecoration-addedResourceForeground,
      var(--intellij-progressBar-passedColor)
    );
  }

  .c-feedback__ratings :global(.c-feedback__negative:not([disabled]):hover),
  .c-feedback__ratings :global(.c-feedback__negative.c-feedback--selected) {
    color: var(
      --vscode-gitDecoration-deletedResourceForeground,
      var(--intellij-progressBar-failedColor)
    );
  }
</style>
