<script lang="ts">
  import { onKey } from "../../../../common/utils/keypress";
  import XMarkIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/xmark.svg?component";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import CardAugment from "$common-webviews/src/design-system/components/CardAugment.svelte";
  import CopyButton from "$common-webviews/src/common/components/CopyButton.svelte";
  import TextAreaAugment from "$common-webviews/src/design-system/components/TextAreaAugment.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import { scrollOnShow } from "$common-webviews/src/common/actions/scrollOnShow";

  export let requestId: string;
  export let onSubmit: (
    report: string
  ) => void;

  export let showBugreportForm: boolean = false;

  let bugreport = {
    explanation: "",
    stepsToReproduce: "",
  };

  function sendBugreport() {
    onSubmit(`\nExplanation: ${bugreport.explanation}\nSteps to reproduce: ${bugreport.stepsToReproduce}\n`);
  }

  function cancel() {
    showBugreportForm = false;
  }
  
</script>

<div class="c-bugreport-form-container">
  <CardAugment size={0} variant="surface">
    <div class="c-bugreport-form">
      <TextAreaAugment
        name="explanation"
        rows={3}
        placeholder="Explanation of the time issue was encountered here..."
        color="neutral"
        variant="soft"
        label="Report an issue*"
        required
        bind:value={bugreport.explanation}
      >
        <IconButtonAugment
          slot="topRightAction"
          title="Cancel"
          variant="ghost-block"
          color="neutral"
          on:keyup={onKey("Enter", cancel)}
          on:click={cancel}
          size={0.5}
        >
          <XMarkIcon/>
        </IconButtonAugment>
      </TextAreaAugment>
      <div>
        <TextAreaAugment
          name="stepsToReproduce"
          rows={3}
          placeholder="Steps to reproduce the issue..."
          color="neutral"
          variant="soft"
          label="How to reproduce*"
          required
          bind:value={bugreport.stepsToReproduce}
        />
        <TextAugment size={0} weight="light" color="secondary">
          * required fields
        </TextAugment>
      </div>
      <div class="l-bugreport__send" use:scrollOnShow>
        <CopyButton text={requestId} tooltip="Copy request ID" />
        <div class="l-bugreport__send-btns">
          <ButtonAugment
            type="submit"
            title="Share "
            color="accent"
            disabled={bugreport.explanation.trim().length === 0 || bugreport.stepsToReproduce.trim().length === 0}
            size={1}
            on:keyup={onKey("Enter", sendBugreport)}
            on:click={sendBugreport}
          >
            Submit
          </ButtonAugment>
        </div>
      </div>
    </div>
  </CardAugment>
</div>

<style>
  .c-bugreport-form-container {
    margin-top: var(--ds-spacing-2);
  }
  .c-bugreport-form {
    grid-gap: var(--ds-spacing-2);
    display: flex;
    flex-direction: column;
    width: 100%;
    --text-area-label-font-size: var(--ds-font-size-1);
    --text-area-font-size: var(--ds-font-size-1);
  }

  .l-bugreport__send {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: var(--ds-spacing-2);
  }

  .l-bugreport__send-btns {
    display: flex;
    flex-direction: row;
    gap: var(--ds-spacing-1);
  }
</style>
