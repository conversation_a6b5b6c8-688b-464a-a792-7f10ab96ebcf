<script lang="ts">
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import BarsIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/bars.svg?component";
  import SelectModeDropdown from "$common-webviews/src/apps/chat/components/threads/SelectModeDropdown.svelte";
  import NotificationsBadge from "$common-webviews/src/apps/chat/components/threads/NotificationsBadge.svelte";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import { getChatModel } from "$common-webviews/src/apps/chat/chat-context";
  import ShortcutHint from "$common-webviews/src/common/components/keybindings/ShortcutHint.svelte";

  export let shouldShowThreadsList: boolean = false;
  export let onOpenThreads: () => void = () => {};

  const chatModel = getChatModel();
  $: enableDebugFeatures = $chatModel?.flags?.enableDebugFeatures ?? false;
</script>

<div class="c-chat-header">
  <div class="c-chat-header__left">
    {#if shouldShowThreadsList}
      {#snippet threadsButton()}
        <IconButtonAugment
          title="Open menu"
          variant="ghost-block"
          color="neutral"
          size={0.5}
          on:click={onOpenThreads}
        >
          <BarsIcon />
        </IconButtonAugment>
      {/snippet}

      {#if enableDebugFeatures}
        <TextTooltipAugment side="bottom" nested={false}>
          <div slot="content" class="c-chat-header__tooltip">
            Toggle threads menu
            <ShortcutHint commandName="toggleThreadsPanel" keysOnly />
          </div>
          {@render threadsButton()}
        </TextTooltipAugment>
      {:else}
        {@render threadsButton()}
      {/if}
      <NotificationsBadge on:click={onOpenThreads} />
    {/if}
  </div>
  <SelectModeDropdown />
</div>

<style>
  .c-chat-header {
    display: flex;
    gap: var(--ds-spacing-2);
    padding-block: var(--ds-spacing-1);
    border-bottom: 1px solid var(--augment-border-color);
  }
  .c-chat-header__left {
    flex: 1;
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-1);
  }

  .c-chat-header__tooltip {
    --keyboard-shortcut-hint-border: currentColor;
    --keyboard-shortcut-hint-color: currentColor;

    display: flex;
    align-items: center;
    gap: var(--ds-spacing-1);
  }
</style>
