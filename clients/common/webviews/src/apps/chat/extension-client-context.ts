/**
 * Context utilities for ExtensionClient
 *
 * This module provides getter/setter functions for managing ExtensionClient context
 * without exposing the context key directly, avoiding coupling between the class
 * and the interface.
 */

import { getContext, setContext } from "svelte";
import type { IExtensionClient } from "./extension-client";

/**
 * Private context key for ExtensionClient.
 * This key is not exported to prevent direct usage.
 */
const EXTENSION_CLIENT_CONTEXT_KEY = "extensionClient";

/**
 * Sets the ExtensionClient context for child components.
 *
 * @param extensionClient - The ExtensionClient instance to set in context
 */
export function setExtensionClientContext(extensionClient: IExtensionClient): void {
  setContext(EXTENSION_CLIENT_CONTEXT_KEY, extensionClient);
}

/**
 * Gets the ExtensionClient from the current component context.
 *
 * @returns The ExtensionClient instance from context
 * @throws Error if ExtensionClient context is not found
 */
export function getExtensionClientContext(): IExtensionClient {
  const extensionClient = getContext<IExtensionClient>(EXTENSION_CLIENT_CONTEXT_KEY);
  if (!extensionClient) {
    throw new Error(
      "ExtensionClient context not found. Make sure setExtensionClientContext() was called in a parent component.",
    );
  }
  return extensionClient;
}
