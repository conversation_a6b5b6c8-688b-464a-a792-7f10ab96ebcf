/**
 * Utility module for managing VSCode version warning preferences
 * Handles sessionStorage operations for the "hide for this session" functionality
 */

interface VscodeVersionWarningPreference {
  hideForSession: boolean;
  timestamp: string;
}

const STORAGE_KEY = "vscode-version-warning-hide-for-session";

/**
 * Checks if the user has chosen to hide VSCode version warnings for this session
 * @returns true if the user has dismissed the warnings for this session
 */
export function shouldHideVscodeVersionWarningForSession(): boolean {
  try {
    const stored = sessionStorage.getItem(STORAGE_KEY);
    if (!stored) {
      return false;
    }

    const preference: VscodeVersionWarningPreference = JSON.parse(stored);
    return preference.hideForSession === true;
  } catch (error) {
    console.error("Failed to read VSCode version warning preference from sessionStorage:", error);
    return false;
  }
}

/**
 * Sets the user preference to hide VSCode version warnings for this session
 */
export function setHideVscodeVersionWarningForSession(): void {
  try {
    const preference: VscodeVersionWarningPreference = {
      hideForSession: true,
      timestamp: new Date().toISOString(),
    };
    sessionStorage.setItem(STORAGE_KEY, JSON.stringify(preference));
  } catch (error) {
    console.error("Failed to save VSCode version warning preference to sessionStorage:", error);
  }
}

/**
 * Clears the "hide for session" preference (useful for testing or user preference reset)
 */
export function clearVscodeVersionWarningPreference(): void {
  try {
    sessionStorage.removeItem(STORAGE_KEY);
  } catch (error) {
    console.error("Failed to clear VSCode version warning preference from sessionStorage:", error);
  }
}
