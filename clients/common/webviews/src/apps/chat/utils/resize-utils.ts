/**
 * Utility functions for handling resize functionality in the ThreadsList component
 */

export interface ResizeState {
  isDragging: boolean;
  startY: number;
  startHeight: number;
  currentHeight: number;
  hasUserResized: boolean;
  hasDraggedThisClick: boolean;
}

export interface ResizeConfig {
  threadItemHeight: number;
  minHeight: number;
  defaultHeight: number;
  windowHeight: number;
  maxContentHeight: number;
}

/**
 * Creates initial resize state
 */
export function createResizeState(defaultHeight: number): ResizeState {
  return {
    isDragging: false,
    startY: 0,
    startHeight: 0,
    currentHeight: defaultHeight,
    hasUserResized: false,
    hasDraggedThisClick: false,
  };
}

/**
 * Calculates the maximum allowed height for the resize container
 */
export function calculateMaxHeight(windowHeight: number, contentHeight: number): number {
  const availableSpace = Math.floor(windowHeight - 300); // Reserve space for other UI elements
  return Math.max(Math.min(contentHeight, availableSpace), 0);
}

/**
 * Starts a drag operation
 */
export function startDrag(event: MouseEvent, state: ResizeState, isExpanded: boolean): ResizeState {
  if (!isExpanded) return state;

  event.preventDefault();

  return {
    ...state,
    isDragging: true,
    hasDraggedThisClick: false,
    startY: event.clientY,
    startHeight: state.currentHeight,
  };
}

/**
 * Handles drag movement
 */
export function doDrag(
  event: MouseEvent,
  state: ResizeState,
  config: ResizeConfig,
  isExpanded: boolean,
): ResizeState {
  if (!isExpanded || !state.isDragging) return state;

  const deltaY = event.clientY - state.startY;
  const newHeight = state.startHeight + deltaY;
  const maxHeight = calculateMaxHeight(config.windowHeight, config.maxContentHeight);

  // Constrain height within min and max bounds
  const constrainedHeight = Math.max(config.minHeight, Math.min(maxHeight, newHeight));

  return {
    ...state,
    hasDraggedThisClick: true,
    currentHeight: constrainedHeight,
  };
}

/**
 * Stops a drag operation
 */
export function stopDrag(state: ResizeState): ResizeState {
  const wasResized = state.isDragging;

  return {
    ...state,
    isDragging: false,
    hasUserResized: state.hasUserResized || wasResized,
    // Reset hasDraggedThisClick after a brief delay to prevent click events
  };
}

/**
 * Resets the hasDraggedThisClick flag after a delay
 */
export function resetDragClick(state: ResizeState): ResizeState {
  return {
    ...state,
    hasDraggedThisClick: false,
  };
}

/**
 * Constrains the current height to ensure it doesn't exceed the maximum content height
 * This should be called reactively when content changes (e.g., when filtering changes the number of items)
 */
export function constrainHeightToContent(state: ResizeState, config: ResizeConfig): ResizeState {
  const maxHeight = calculateMaxHeight(config.windowHeight, config.maxContentHeight);

  // Only constrain if current height exceeds the new maximum
  if (state.currentHeight > maxHeight) {
    const constrainedHeight = Math.max(config.minHeight, Math.min(maxHeight, state.currentHeight));

    return {
      ...state,
      currentHeight: constrainedHeight,
    };
  }

  return state;
}
