/**
 * A scheduler that can be used to schedule callbacks to be executed at a specific time.
 * It can be once every interval or once at a specific date.  Stopping the scheduler will
 * prevent any scheduled callbacks from being executed.  Starting the scheduler will cause all scheduled callbacks to be executed at their scheduled times.
 *
 *
 * Note:  if a once or interval is registered while the scheduler is stopped, it will not be executed until the scheduler is started again.
 * and it will wait for the interval to pass before executing.  If the desire is to execute at a specific time, use the at method.
 */
export class Scheduler {
  constructor(
    //Started state, if started every listener will be scheduled immediately.
    private _started = true,
    //mostly for testing, but also could be useful for logging.
    private _setTimeout = setTimeout,
  ) {}

  private _notify = new Set<NotifyObject>();
  /**
   * Start the scheduler, causing all registered callbacks to be executed at their scheduled times.
   * - Start/Stop can be called multiple times.
   * - It acts like a debouncer, that can handle multiple requests.
   *
   * @returns The scheduler instance
   */
  public start() {
    if (!this._started) {
      this._started = true;
      this._notify.forEach(this._schedule);
    }
    return this;
  }
  /**
   * Stop the scheduler, preventing any scheduled callbacks from being executed.
   * - Start/Stop can be called multiple times.
   * - It acts like a debouncer, that can handle multiple requests.
   *
   * @returns The scheduler instance
   */
  public stop() {
    this._started = false;
    this._notify.forEach(this._clearTimeout);
    return this;
  }

  private _clearTimeout = (n: NotifyObject) => {
    if (n.timeoutId) {
      clearTimeout(n.timeoutId);
    }
  };
  /**
   * Get the started state of the scheduler.
   *
   * @returns The started state of the scheduler
   */
  get isStarted() {
    return this._started;
  }
  /**
   * Set the started state of the scheduler.
   *
   * @param value - The started state of the scheduler
   */
  set isStarted(value: boolean) {
    value ? this.start() : this.stop();
  }

  /**
   * Register a callback to be executed once, after the specified number of milliseconds. If
   * the scheduler is stopped, the callback will not be executed until the scheduler is started again.
   *
   * Note: A callback maybe called more than once if the scheduler is stopped and then started again.
   * If this is not desired, use the at method, or call the cancel function returned by this method.
   * @param timeout - The number of milliseconds to wait before executing the callback
   * @param notify - The callback to execute
   * @returns A function that can be called to cancel the scheduled callback
   */
  public once(timeout: number, notify: NotifyFn): CancelFn {
    return this._register(timeout, notify, true);
  }

  /**
   * Register a callback to be executexd repeatedly, after the specified number of milliseconds
   * @param timeout - The number of milliseconds to wait between each execution of the callback
   * @param notify - The callback to execute
   * @returns A function that can be called to cancel the scheduled callback
   */
  public interval(timeout: number, notify: NotifyFn): CancelFn {
    return this._register(timeout, notify, false);
  }

  /**
   * Register a callback to be executed once at a specific date.  this will only be called one time.  If
   * the scheduler is stopped when the time is reached, the callback will not be executed.
   *
   *
   * @param date - The date at which to execute the callback, or a number of milliseconds from now.
   * @param notify - The callback to execute
   * @returns A function that can be called to cancel the scheduled callback
   */
  public at(date: Date | number, notify: NotifyFn): CancelFn {
    return this._register(
      0,
      notify,
      false,
      typeof date === "number" ? new Date(Date.now() + date) : date,
    );
  }
  /**
   * Reschedule all callbacks.  This is useful if the scheduler is stopped and then started again.
   *
   * Note: if the scheduler is stopped, the callbacks will not be executed until the scheduler is started again.
   *
   * @returns The scheduler instance
   */

  public reschedule() {
    this._notify.forEach((v) => {
      this._clearTimeout(v);
      this._schedule(v);
    });
  }

  private _register(timeout: number, notify: NotifyFn, once: boolean, date?: Date) {
    if (!(timeout || date)) {
      return () => {};
    }
    const notifyObject: NotifyObject = { timeout, notify, once, date };
    this._notify.add(notifyObject);
    this._schedule(notifyObject);
    return () => {
      this._clearTimeout(notifyObject);
      this._notify.delete(notifyObject);
    };
  }

  private _schedule = (v: NotifyObject) => {
    if (!this._started) {
      return;
    }
    if (v.date) {
      //if we have a date, we need to recalculate the timeout
      v.timeout = v.date.getTime() - Date.now();
      if (v.timeout < 0) {
        //time has expired, will not notify
        return;
      }
    }
    //setTimeout really does not want to called with a context.
    //so we need to call it with undefined.
    const st = this._setTimeout;
    v.timeoutId = st(this._handle, v.timeout, v);
  };

  private _handle = (v: NotifyObject) => {
    v.notify();
    if (v.date) {
      //once an 'at' is reached, we remove it from the list, as it will not be executed again.
      this._notify.delete(v);
      return;
    }
    if (!v.once) {
      this._schedule(v);
    }
  };

  /**
   * Clear all scheduled callbacks and stop the scheduler. It does not
   * stop the scheduler, but it does clear all scheduled callbacks. Whatever
   * state the scheduler was in before this call, will continue afterward.
   */
  public dispose = () => {
    this._notify.forEach(this._clearTimeout);
    this._notify.clear();
  };
}

interface NotifyObject {
  timeout: number;
  notify: NotifyFn;
  timeoutId?: ReturnType<typeof setTimeout> | undefined;
  once?: boolean;
  date?: Date;
}

/**
 * A function that can be called to notify of an event
 */
export interface NotifyFn {
  (): void;
}

/**
 * A function that can be called to cancel a scheduled callback
 */
export interface CancelFn {
  (): void;
}
