import { derived, type Readable, writable } from "svelte/store";
import { type SubscriptionModel } from "../models/subscription-model";
import { type ChatModel } from "../models/chat-model";
import { shouldHideVscodeVersionWarningForSession } from "./vscode-version-warning-preferences";
import { isIntelliJHost } from "$common-webviews/src/common/hosts/intellij";
import {
  CONVERSATION_HISTORY_WARNING_REAPPEAR_DELAY_MS,
  shouldHideConversationHistoryWarningForSession,
} from "./conversation-history-warning-preferences";
import { type RemoteAgentsModel } from "../../remote-agent-manager/models/remote-agents-model";
import { shouldWarnOfRemoteAgentDeletion } from "../../remote-agent-manager/utils/agent-deletion";

export class ChatInputWarningBannerContext {
  /** Whether the subscription warning banner should be shown */
  private _showSubscriptionWarning: Readable<boolean>;

  /** Whether the VSCode version warning banner should be shown */
  private _showVSCodeVersionWarning: Readable<boolean>;
  /** Whether the VSCode version warning has been dismissed */
  private _vscodeVersionWarningDismissed = writable(false);

  /** Whether the conversation history warning banner should be shown */
  private _showConversationHistoryWarning: Readable<boolean>;
  /** Timestamp when the conversation history warning was last dismissed */
  private _conversationHistoryBannerDismissedAt = writable(0);

  private _showRemoteAgentDeletionWarning: Readable<boolean>;

  constructor(
    private readonly _chatModel: ChatModel,
    private readonly _subscriptionModel: SubscriptionModel,
    private readonly _remoteAgentsModel: RemoteAgentsModel,
  ) {
    this._showSubscriptionWarning = derived(
      [this._subscriptionModel.info, this._subscriptionModel.dismissed],
      ([$subscriptionInfo, $dismissed]) => {
        return (
          !!$subscriptionInfo &&
          !$dismissed &&
          this._subscriptionModel.shouldShowWarning($subscriptionInfo)
        );
      },
    );

    this._showVSCodeVersionWarning = derived(
      [this._chatModel.flags, this._vscodeVersionWarningDismissed],
      ([$flags, $dismissed]) => {
        return (
          $flags.isVscodeVersionOutdated &&
          !$dismissed &&
          !shouldHideVscodeVersionWarningForSession()
        );
      },
    );

    this._showConversationHistoryWarning = derived(
      [
        this._chatModel.currentConversationModel.totalCharactersStore,
        this._remoteAgentsModel,
        this._conversationHistoryBannerDismissedAt,
      ],
      ([$totalCharacters, $remoteAgentsModel, $conversationHistoryBannerDismissedAt]) => {
        // IntelliJ only for now
        if (!isIntelliJHost()) return false;

        // Don't show if user chose "hide for this session"
        if (shouldHideConversationHistoryWarningForSession()) return false;

        // Don't show during remote agent sessions.
        // Since remote agent conversations aren't stored locally,
        // there isn't much need to nuke chat history in the name of performance
        if ($remoteAgentsModel.isActive) return false;

        // Check if recently dismissed
        if ($conversationHistoryBannerDismissedAt > 0) {
          const timeSinceDismissed = Date.now() - $conversationHistoryBannerDismissedAt;
          if (timeSinceDismissed < CONVERSATION_HISTORY_WARNING_REAPPEAR_DELAY_MS) {
            return false; // Still in dismiss period, no need to check threshold
          }
        }

        // Only check threshold if we might actually show the banner.
        // Get a ballpark estimate of history size in bytes, without
        // actually serializing the history, which could be expensive and blocking.
        // We assume roughly 2 bytes/char
        const charThreshold = _chatModel.flags.conversationHistorySizeThresholdBytes / 2;
        return $totalCharacters > charThreshold;
      },
    );

    this._showRemoteAgentDeletionWarning = derived(
      [this._remoteAgentsModel, this._chatModel.flags],
      ([$remoteAgentsModel, flagsModel]) => {
        if (!$remoteAgentsModel.isActive) {
          return false;
        }
        const currentAgent = $remoteAgentsModel.currentAgent;
        if (!currentAgent) {
          return false;
        }
        return shouldWarnOfRemoteAgentDeletion(
          currentAgent,
          flagsModel.remoteAgentsResumeHintAvailableTtlDays,
        );
      },
    );
  }

  get showSubscriptionWarning() {
    return this._showSubscriptionWarning;
  }

  get showVSCodeVersionWarning() {
    return this._showVSCodeVersionWarning;
  }

  get showConversationHistoryWarning() {
    return this._showConversationHistoryWarning;
  }

  get showRemoteAgentDeletionWarning() {
    return this._showRemoteAgentDeletionWarning;
  }

  dismissVSCodeVersionWarning() {
    this._vscodeVersionWarningDismissed.set(true);
  }

  dismissConversationHistoryWarning() {
    this._conversationHistoryBannerDismissedAt.set(Date.now());
  }
}
