import { ConversationAge } from "../types/conversation-age";
import {
  type ChatThread,
  type LocalAgentThread,
  type RemoteAgentThread,
  type Thread,
} from "$common-webviews/src/apps/chat/components/threads/ThreadsPanel.svelte";

export const MAX_THREADS_TO_DISPLAY = 1_000;

// Re-export types and enums for convenience
export { ConversationAge };
export type {
  ChatThread,
  LocalAgentThread,
  RemoteAgentThread,
} from "$common-webviews/src/apps/chat/components/threads/ThreadsPanel.svelte";

/**
 * The display order for conversation age groups.
 * This determines the order in which groups appear in the UI.
 */
export const displayOrder: ConversationAge[] = [
  ConversationAge.New,
  ConversationAge.Pinned,
  ConversationAge.Active,
  ConversationAge.Today,
  ConversationAge.ThisWeek,
  ConversationAge.ThisMonth,
  ConversationAge.Older,
];

/**
 * Represents a group of threads organized by age.
 */
export interface ThreadGroup {
  groupTitle: ConversationAge;
  threads: Thread[];
}

function sortThreads(threads: Thread[]): Thread[] {
  return threads.sort((a, b) => b.sortTimestamp.getTime() - a.sortTimestamp.getTime());
}

/**
 * Groups threads by their age category, with special handling for active remote agents.
 * Active remote agents (running or has_updates) are placed in the Active category.
 * Active remote agents are always included, even if they don't match other filters.
 *
 * @param threads - Array of threads to group (filtered threads)
 * @param maxThreadsToDisplay - Maximum number of threads to display (default: MAX_THREADS_TO_DISPLAY)
 * If there are more threads than the limit, the oldest threads are dropped.
 * @returns Array of thread groups in display order
 */
export function groupThreadsByAgeWithActive(
  threads: (ChatThread | LocalAgentThread | RemoteAgentThread)[],
  maxThreadsToDisplay: number = MAX_THREADS_TO_DISPLAY,
): ThreadGroup[] {
  const newGroups: Map<ConversationAge, Thread[]> = new Map();
  const now = new Date();
  const today = new Date(now.setHours(0, 0, 0, 0));
  const timeRanges = [
    { age: ConversationAge.Today, date: today },
    { age: ConversationAge.ThisWeek, date: new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000) },
    {
      age: ConversationAge.ThisMonth,
      date: new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000),
    },
  ];

  for (const thread of threads) {
    const date = thread.sortTimestamp;
    let age = timeRanges.find((range) => date >= range.date)?.age || ConversationAge.Older;

    // Override with special categories
    if (thread.isPinned) {
      age = ConversationAge.Pinned;
    }
    if (thread.isNew) {
      age = ConversationAge.New;
    }

    newGroups.set(age, [...(newGroups.get(age) || []), thread]);
  }

  const sortedGroups = displayOrder
    .map((age) => ({
      groupTitle: age,
      threads: sortThreads(newGroups.get(age) || []),
    }))
    .filter((group) => group.threads.length > 0);

  // Limit the number of threads displayed to MAX_THREADS_TO_DISPLAY
  let count = 0;
  const finalGroups = [];
  for (const group of sortedGroups) {
    const currentGroup = [];
    for (const thread of group.threads) {
      if (count >= maxThreadsToDisplay) {
        break;
      }
      currentGroup.push(thread);
      count++;
    }
    finalGroups.push({
      groupTitle: group.groupTitle,
      threads: currentGroup,
    });
  }
  return finalGroups;
}
