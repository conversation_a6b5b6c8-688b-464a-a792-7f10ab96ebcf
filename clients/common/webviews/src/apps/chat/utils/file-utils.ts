import type { JSONContent } from "@tiptap/core";

export const MAX_IMAGES_COUNT = 10;
export const MAX_IMAGE_SIZE_KB = 1024;
export const MAX_TEXT_SIZE_KB = 24;

/**
 * Supported file extensions for file uploads
 * Includes image files and common text-based files
 */
export const SUPPORTED_FILE_EXTENSIONS = [
  // Image files
  ".png",
  ".jpg",
  ".jpeg",
  ".gif",
  ".webp",
  ".svg",
  ".bmp",
  // Programming languages
  ".js",
  ".jsx",
  ".ts",
  ".tsx",
  ".py",
  ".java",
  ".c",
  ".cpp",
  ".h",
  ".hpp",
  ".cs",
  ".go",
  ".rs",
  ".swift",
  ".kt",
  ".scala",
  ".rb",
  ".php",
  ".html",
  ".css",
  ".scss",
  ".sass",
  ".less",
  ".svelte",
  ".vue",
  // Configuration and data files
  ".json",
  ".yaml",
  ".yml",
  ".toml",
  ".ini",
  ".cfg",
  ".conf",
  ".xml",
  ".plist",
  ".properties",
  ".env",
  // Documentation and text files
  ".md",
  ".txt",
  ".rst",
  ".tex",
  // Shell scripts
  ".sh",
  ".bash",
  ".zsh",
  ".fish",
  // Database and query files
  ".sql",
  ".graphql",
  // Build and project files
  ".gradle",
  ".pom",
  ".bazel",
  ".bzl",
  ".dockerfile",
  ".makefile",
  // Other common text files
  ".proto",
  ".mod",
  ".hs",
  ".lua",
  ".dart",
  ".r",
  ".m",
  ".pl",
  ".ps1",
  ".csv",
  ".tsv",
] as const;

/**
 * Get the accept attribute value for file inputs
 * @param enableDebugFeatures Whether debug features are enabled (allows all files)
 * @returns The accept attribute value
 */
export function getFileAcceptAttribute(enableDebugFeatures: boolean): string {
  return enableDebugFeatures ? SUPPORTED_FILE_EXTENSIONS.join(",") : ".png,.jpg,.jpeg";
}

export interface ImageInfo {
  src: string;
  title: string;
  fileSizeBytes: number;
  isLoading: boolean;
}

/**
 * Extract image information from rich text JSON content
 * @param json The rich text JSON content
 * @returns Array of image information
 */
export function extractImagesFromContent(json: JSONContent | null | undefined): ImageInfo[] {
  if (!json) {
    return [];
  }

  const images: ImageInfo[] = [];

  const traverse = (node: JSONContent) => {
    if (!node) {
      return;
    }

    if (node.type === "file" && node.attrs) {
      images.push({
        src: node.attrs.src || "",
        title: node.attrs.title || "",
        fileSizeBytes: Number(node.attrs.fileSizeBytes) || 0,
        isLoading: Boolean(node.attrs.isLoading),
      });
    }

    if (node.content && Array.isArray(node.content)) {
      for (const contentNode of node.content) {
        traverse(contentNode);
      }
    }
  };

  traverse(json);
  return images;
}

/**
 * Calculate total image size in KB and count from rich text content
 * @param json The rich text JSON content
 * @returns Object with imageCount and imageSizeSumKb
 */
export function calculateImageStats(json: JSONContent | null | undefined): {
  imageCount: number;
  imageSizeSumKb: number;
} {
  const images = extractImagesFromContent(json);

  // Only count non-loading images for accurate stats
  const processedImages = images.filter((img) => !img.isLoading);

  const imageCount = processedImages.length;
  const imageSizeSumKb = Math.round(
    processedImages.reduce((sum, img) => sum + img.fileSizeBytes, 0) / 1024,
  );

  return {
    imageCount,
    imageSizeSumKb,
  };
}

/**
 * Extract text file information from rich text JSON content
 * @param json The rich text JSON content
 * @returns Array of text file information
 */
export function extractTextFilesFromContent(json: JSONContent | null | undefined): ImageInfo[] {
  if (!json) {
    return [];
  }

  const textFiles: ImageInfo[] = [];

  const traverse = (node: JSONContent) => {
    if (!node) {
      return;
    }

    if (node.type === "file" && node.attrs) {
      const mimeType = node.attrs.mimeType || "";
      // Check if it's not an image file
      if (!mimeType.startsWith("image/")) {
        textFiles.push({
          src: node.attrs.src || "",
          title: node.attrs.title || "",
          fileSizeBytes: Number(node.attrs.fileSizeBytes) || 0,
          isLoading: Boolean(node.attrs.isLoading),
        });
      }
    }

    if (node.content && Array.isArray(node.content)) {
      for (const contentNode of node.content) {
        traverse(contentNode);
      }
    }
  };

  traverse(json);
  return textFiles;
}

/**
 * Calculate total text file size in KB from rich text content
 * @param json The rich text JSON content
 * @returns Object with textSizeSumKb
 */
export function calculateTextStats(json: JSONContent | null | undefined): {
  textSizeSumKb: number;
} {
  const textFiles = extractTextFilesFromContent(json);

  // Only count non-loading text files for accurate stats
  const processedTextFiles = textFiles.filter((file) => !file.isLoading);

  const textSizeSumKb = Math.round(
    processedTextFiles.reduce((sum, file) => sum + file.fileSizeBytes, 0) / 1024,
  );

  return {
    textSizeSumKb,
  };
}

export interface FileWarningResult {
  hasWarning: boolean;
  warningMessage: string;
  warningMessages: string[];
}

/**
 * Calculate file size warnings and disabled status based on image and text statistics
 * @param richTextContent The rich text JSON content
 * @returns Object with warning status, disabled status, and warning messages
 */
export function calculateFileWarnings(
  richTextContent: JSONContent | null | undefined,
): FileWarningResult {
  const imageStats = calculateImageStats(richTextContent);
  const textStats = calculateTextStats(richTextContent);

  const { imageCount, imageSizeSumKb } = imageStats;
  const { textSizeSumKb } = textStats;

  // Check for warnings
  const hasImageCountWarning = imageCount > MAX_IMAGES_COUNT && MAX_IMAGES_COUNT > 0;
  const hasImageSizeWarning = imageSizeSumKb > MAX_IMAGE_SIZE_KB && MAX_IMAGE_SIZE_KB > 0;
  const hasTextSizeWarning = textSizeSumKb > MAX_TEXT_SIZE_KB && MAX_TEXT_SIZE_KB > 0;
  const hasAnyWarning = hasImageCountWarning || hasImageSizeWarning || hasTextSizeWarning;

  // Generate warning messages
  const warningMessages = [
    hasImageCountWarning ? `Up to ${MAX_IMAGES_COUNT} images are supported.` : null,
    hasImageSizeWarning
      ? `Total image size exceeds the ${MAX_IMAGE_SIZE_KB}KB limit. Current size: ${imageSizeSumKb}KB`
      : null,
    hasTextSizeWarning
      ? `Total text file size exceeds the ${MAX_TEXT_SIZE_KB}KB limit. Current size: ${textSizeSumKb}KB`
      : null,
  ].filter(Boolean) as string[];

  const firstWarningMessage = warningMessages.length > 0 ? warningMessages[0] : "";

  return {
    hasWarning: hasAnyWarning,
    // Just display the first warning if there are multiple
    warningMessage: firstWarningMessage,
    warningMessages,
  };
}
