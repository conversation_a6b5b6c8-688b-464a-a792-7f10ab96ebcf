/* eslint-disable @typescript-eslint/naming-convention */

import { describe, it, expect, beforeEach, vi, afterEach } from "vitest";
import { get, writable } from "svelte/store";
import { ChatInputWarningBannerContext } from "./chat-input-warning-banner-context";
import type { ChatModel } from "../models/chat-model";
import type { SubscriptionModel, SubscriptionInfo } from "../models/subscription-model";
import { SubscriptionType } from "../models/subscription-model";
import type { RemoteAgentsModel } from "../../remote-agent-manager/models/remote-agents-model";
import { CONVERSATION_HISTORY_WARNING_REAPPEAR_DELAY_MS } from "./conversation-history-warning-preferences";

// Mock the dependencies
vi.mock("./vscode-version-warning-preferences", () => ({
  shouldHideVscodeVersionWarningForSession: vi.fn(),
}));

vi.mock("./conversation-history-warning-preferences", () => ({
  shouldHideConversationHistoryWarningForSession: vi.fn(),
  CONVERSATION_HISTORY_WARNING_REAPPEAR_DELAY_MS: 60 * 60 * 1000, // 1 hour
}));

vi.mock("$common-webviews/src/common/hosts/intellij", () => ({
  isIntelliJHost: vi.fn(),
}));

// Import the mocked functions
import { shouldHideVscodeVersionWarningForSession } from "./vscode-version-warning-preferences";
import { shouldHideConversationHistoryWarningForSession } from "./conversation-history-warning-preferences";
import { isIntelliJHost } from "$common-webviews/src/common/hosts/intellij";

describe("ChatInputWarningBannerContext", () => {
  let chatModel: ChatModel;
  let subscriptionModel: SubscriptionModel;
  let remoteAgentsModel: RemoteAgentsModel;
  let context: ChatInputWarningBannerContext;
  let mockConversationModel: any;
  let mockFlags: any;

  // Mock functions
  const mockShouldHideVscodeVersionWarningForSession = vi.fn();
  const mockShouldHideConversationHistoryWarningForSession = vi.fn();
  const mockIsIntelliJHost = vi.fn();

  beforeEach(() => {
    // Reset all mocks
    vi.resetAllMocks();

    // Setup mock implementations
    vi.mocked(shouldHideVscodeVersionWarningForSession).mockImplementation(
      mockShouldHideVscodeVersionWarningForSession,
    );

    vi.mocked(shouldHideConversationHistoryWarningForSession).mockImplementation(
      mockShouldHideConversationHistoryWarningForSession,
    );

    vi.mocked(isIntelliJHost).mockImplementation(mockIsIntelliJHost);

    // Default mock return values
    mockShouldHideVscodeVersionWarningForSession.mockReturnValue(false);
    mockShouldHideConversationHistoryWarningForSession.mockReturnValue(false);
    mockIsIntelliJHost.mockReturnValue(true);

    // Create mock models
    mockConversationModel = {
      totalCharactersStore: writable(1000),
    };

    // Create a mock flags store that behaves like ChatFlagsModel
    mockFlags = writable({
      isVscodeVersionOutdated: false,
      conversationHistorySizeThresholdBytes: 10000, // 10KB threshold
    });

    // Add the property access for the direct access in the derived store
    (mockFlags as any).conversationHistorySizeThresholdBytes = 10000;

    chatModel = {
      currentConversationModel: mockConversationModel,
      flags: mockFlags,
    } as unknown as ChatModel;

    subscriptionModel = {
      info: writable(null),
      dismissed: writable(false),
      shouldShowWarning: vi.fn().mockReturnValue(false),
    } as unknown as SubscriptionModel;

    // Create a mock remote agents model store
    remoteAgentsModel = writable({
      isActive: false,
    }) as unknown as RemoteAgentsModel;

    // Create context instance
    context = new ChatInputWarningBannerContext(chatModel, subscriptionModel, remoteAgentsModel);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe("showSubscriptionWarning derived store", () => {
    it("should return false when subscription info is null", () => {
      const result = get(context.showSubscriptionWarning);
      expect(result).toBe(false);
    });

    it("should return false when subscription is dismissed", () => {
      const mockSubscriptionInfo: SubscriptionInfo = {
        type: SubscriptionType.inactive,
        daysRemaining: 5,
        usageBalanceDepleted: false,
      };
      subscriptionModel.info.set(mockSubscriptionInfo);
      subscriptionModel.dismissed.set(true);
      vi.mocked(subscriptionModel.shouldShowWarning).mockReturnValue(true);

      const result = get(context.showSubscriptionWarning);
      expect(result).toBe(false);
    });

    it("should return false when shouldShowWarning returns false", () => {
      const mockSubscriptionInfo: SubscriptionInfo = {
        type: SubscriptionType.active,
        daysRemaining: 30,
        usageBalanceDepleted: false,
      };
      subscriptionModel.info.set(mockSubscriptionInfo);
      subscriptionModel.dismissed.set(false);
      vi.mocked(subscriptionModel.shouldShowWarning).mockReturnValue(false);

      const result = get(context.showSubscriptionWarning);
      expect(result).toBe(false);
    });

    it("should return true when all conditions are met", () => {
      const mockSubscriptionInfo: SubscriptionInfo = {
        type: SubscriptionType.inactive,
        usageBalanceDepleted: true,
      };
      subscriptionModel.info.set(mockSubscriptionInfo);
      subscriptionModel.dismissed.set(false);
      vi.mocked(subscriptionModel.shouldShowWarning).mockReturnValue(true);

      const result = get(context.showSubscriptionWarning);
      expect(result).toBe(true);
      expect(subscriptionModel.shouldShowWarning).toHaveBeenCalledWith(mockSubscriptionInfo);
    });
  });

  describe("showVSCodeVersionWarning derived store", () => {
    it("should return false when VSCode version is not outdated", () => {
      mockFlags.set({
        isVscodeVersionOutdated: false,
        conversationHistorySizeThresholdBytes: 10000,
      });

      const result = get(context.showVSCodeVersionWarning);
      expect(result).toBe(false);
    });

    it("should return false when warning is dismissed", () => {
      mockFlags.set({
        isVscodeVersionOutdated: true,
        conversationHistorySizeThresholdBytes: 10000,
      });
      context.dismissVSCodeVersionWarning();

      const result = get(context.showVSCodeVersionWarning);
      expect(result).toBe(false);
    });

    it("should return false when session preference is to hide", () => {
      mockFlags.set({
        isVscodeVersionOutdated: true,
        conversationHistorySizeThresholdBytes: 10000,
      });
      mockShouldHideVscodeVersionWarningForSession.mockReturnValue(true);

      const result = get(context.showVSCodeVersionWarning);
      expect(result).toBe(false);
    });

    it("should return true when all conditions are met", () => {
      mockFlags.set({
        isVscodeVersionOutdated: true,
        conversationHistorySizeThresholdBytes: 10000,
      });
      mockShouldHideVscodeVersionWarningForSession.mockReturnValue(false);

      const result = get(context.showVSCodeVersionWarning);
      expect(result).toBe(true);
    });
  });

  describe("showConversationHistoryWarning derived store", () => {
    beforeEach(() => {
      // Setup for conversation history tests
      mockConversationModel.totalCharactersStore.set(10000); // Above threshold
      mockIsIntelliJHost.mockReturnValue(true);
      mockShouldHideConversationHistoryWarningForSession.mockReturnValue(false);
      (remoteAgentsModel as any).set({ isActive: false });
    });

    it("should return false when not in IntelliJ host", () => {
      mockIsIntelliJHost.mockReturnValue(false);

      const result = get(context.showConversationHistoryWarning);
      expect(result).toBe(false);
    });

    it("should return false when session preference is to hide", () => {
      mockShouldHideConversationHistoryWarningForSession.mockReturnValue(true);

      const result = get(context.showConversationHistoryWarning);
      expect(result).toBe(false);
    });

    it("should return false when remote agent is active", () => {
      (remoteAgentsModel as any).set({ isActive: true });

      const result = get(context.showConversationHistoryWarning);
      expect(result).toBe(false);
    });

    it("should return false when total characters is below threshold", () => {
      mockConversationModel.totalCharactersStore.set(1000); // Below threshold (5000 chars for 10KB)

      const result = get(context.showConversationHistoryWarning);
      expect(result).toBe(false);
    });

    it("should return true when total characters is above threshold", () => {
      mockConversationModel.totalCharactersStore.set(10000); // Above threshold

      const result = get(context.showConversationHistoryWarning);
      expect(result).toBe(true);
    });

    it("should return false when recently dismissed (within delay period)", () => {
      const now = Date.now();
      const recentDismissTime = now - CONVERSATION_HISTORY_WARNING_REAPPEAR_DELAY_MS / 2; // Half the delay period ago

      vi.spyOn(Date, "now").mockReturnValue(now);
      context.dismissConversationHistoryWarning();

      // Manually set the dismiss time to simulate recent dismissal
      (context as any)._conversationHistoryBannerDismissedAt.set(recentDismissTime);

      const result = get(context.showConversationHistoryWarning);
      expect(result).toBe(false);
    });

    it("should return true when dismissed but delay period has passed", () => {
      const now = Date.now();
      const oldDismissTime = now - (CONVERSATION_HISTORY_WARNING_REAPPEAR_DELAY_MS + 1000); // Past the delay period

      vi.spyOn(Date, "now").mockReturnValue(now);

      // Manually set the dismiss time to simulate old dismissal
      (context as any)._conversationHistoryBannerDismissedAt.set(oldDismissTime);

      const result = get(context.showConversationHistoryWarning);
      expect(result).toBe(true);
    });
  });

  describe("dismissal methods", () => {
    it("should dismiss VSCode version warning", () => {
      mockFlags.set({
        isVscodeVersionOutdated: true,
        conversationHistorySizeThresholdBytes: 10000,
      });
      mockShouldHideVscodeVersionWarningForSession.mockReturnValue(false);

      // Initially should show warning
      expect(get(context.showVSCodeVersionWarning)).toBe(true);

      // Dismiss the warning
      context.dismissVSCodeVersionWarning();

      // Should no longer show warning
      expect(get(context.showVSCodeVersionWarning)).toBe(false);
    });

    it("should dismiss conversation history warning with current timestamp", () => {
      const mockNow = 1234567890000;
      vi.spyOn(Date, "now").mockReturnValue(mockNow);

      context.dismissConversationHistoryWarning();

      // Check that the timestamp was set correctly
      const dismissedAt = get((context as any)._conversationHistoryBannerDismissedAt);
      expect(dismissedAt).toBe(mockNow);
    });
  });
});
