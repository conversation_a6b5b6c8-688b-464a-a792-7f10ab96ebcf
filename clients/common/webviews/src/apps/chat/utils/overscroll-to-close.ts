/**
 * Overscroll-to-close utility for implementing scroll-to-close behavior
 * Uses wheel events to detect upward scroll attempts when at the top of a scrollable element
 */

export interface OverscrollToCloseOptions {
  /** Function to call when overscroll threshold is exceeded */
  onClose: () => void;
  /** Function to check if the action should be active */
  isActive: () => boolean;
  /** Threshold in pixels of accumulated upward scroll needed to trigger close */
  threshold?: number;
  /** Timeout in milliseconds after which to reset tracking state */
  resetTimeout?: number;
}

interface OverscrollState {
  isTracking: boolean;
  startedAtTop: boolean;
  initialScrollTop: number;
  overscrollAmount: number;
}

/**
 * Svelte action that implements overscroll-to-close behavior
 * Only triggers when scroll gesture starts at the top and user scrolls upward
 *
 * @param element - The scrollable element to attach the behavior to
 * @param options - Configuration options
 * @returns Svelte action object with destroy method
 */
export function overscrollToClose(element: HTMLElement, options: OverscrollToCloseOptions) {
  const { onClose, isActive, threshold = 200, resetTimeout = 150 } = options;

  let accumulatedDelta = 0;
  let wheelTimeout: ReturnType<typeof setTimeout> | undefined;
  let isScrolling = false;
  let scrollStartedAtTop = false;
  let overscrollState: OverscrollState = {
    isTracking: false,
    startedAtTop: false,
    initialScrollTop: 0,
    overscrollAmount: 0,
  };

  const handleWheel = (e: WheelEvent) => {
    if (!isActive()) return;

    const { scrollTop } = element;

    // Start tracking if this is the beginning of a scroll gesture
    if (!isScrolling) {
      isScrolling = true;
      scrollStartedAtTop = scrollTop === 0;
      accumulatedDelta = 0;

      overscrollState = {
        isTracking: scrollStartedAtTop,
        startedAtTop: scrollStartedAtTop,
        initialScrollTop: scrollTop,
        overscrollAmount: 0,
      };
    }

    // Only accumulate if the scroll started at the top and we're trying to scroll up
    if (scrollStartedAtTop && scrollTop === 0 && e.deltaY < 0) {
      // Accumulate upward scroll attempts (negative deltaY)
      accumulatedDelta += Math.abs(e.deltaY);

      // Update overscroll state
      overscrollState.overscrollAmount = accumulatedDelta;

      // Check if we've exceeded the threshold
      if (accumulatedDelta >= threshold) {
        // Trigger close callback
        onClose();
        resetTracking();
        return;
      }
    }

    // If the scroll started at the top but we're now scrolled down,
    // don't reset immediately - wait for the timeout to handle it
    if (scrollStartedAtTop && scrollTop > 0) {
      // Don't reset immediately, let the timeout handle it
      clearTimeout(wheelTimeout);
      wheelTimeout = setTimeout(() => {
        resetTracking();
      }, resetTimeout);
      return;
    }

    // If we're scrolling down from the top (breaking the overscroll gesture),
    // reset immediately
    if (scrollStartedAtTop && e.deltaY > 0) {
      resetTracking();
      return;
    }

    // If diff < abs(7), don't reset because it's probably residual physics
    if (Math.abs(e.deltaY) < 7) return;

    // Reset scroll tracking after a delay if no more wheel events
    clearTimeout(wheelTimeout);
    wheelTimeout = setTimeout(() => {
      resetTracking();
    }, resetTimeout);
  };

  const resetTracking = () => {
    isScrolling = false;
    scrollStartedAtTop = false;
    accumulatedDelta = 0;
    clearTimeout(wheelTimeout);
    overscrollState = {
      isTracking: false,
      startedAtTop: false,
      initialScrollTop: 0,
      overscrollAmount: 0,
    };
  };

  // Scroll event to reset when moving away from top
  const handleScroll = () => {
    // Only reset if we were tracking and have moved significantly away from top
    if (isScrolling && scrollStartedAtTop && element.scrollTop > 10) {
      resetTracking();
    }
  };

  // Add event listeners
  element.addEventListener("wheel", handleWheel, { passive: true });
  element.addEventListener("scroll", handleScroll, { passive: true });

  return {
    destroy() {
      element.removeEventListener("wheel", handleWheel);
      element.removeEventListener("scroll", handleScroll);
      clearTimeout(wheelTimeout);
    },
    update(newOptions: OverscrollToCloseOptions) {
      // Update options if needed
      Object.assign(options, newOptions);
    },
  };
}

/**
 * Helper function to create overscroll-to-close action with common defaults
 *
 * @param onClose - Function to call when overscroll threshold is exceeded
 * @param isActive - Function to check if the action should be active
 * @param threshold - Optional threshold in pixels (default: 50)
 * @returns Configured overscroll action
 */
export function createOverscrollToClose(
  onClose: () => void,
  isActive: () => boolean,
  threshold = 200,
) {
  return (element: HTMLElement) =>
    overscrollToClose(element, {
      onClose,
      isActive,
      threshold,
    });
}
