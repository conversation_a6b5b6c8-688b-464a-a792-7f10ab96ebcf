/**
 * Locking Chain Module
 *
 * This module provides a synchronization mechanism for ensuring sequential execution
 * of asynchronous operations within specific contexts. It's designed to prevent race
 * conditions and ensure that operations complete in the correct order.
 *
 * Key Concepts:
 * - Context: A string identifier that groups related operations together
 * - Chain: A sequence of linked operations that must execute sequentially
 * - Link: An individual operation within a chain, identified by an ID
 *
 * Use Cases:
 * - Ensuring chat messages are processed in order
 * - Preventing concurrent modifications to shared resources
 * - Coordinating async operations that depend on each other
 *
 * Example Usage:
 * ```typescript
 * // Start a new link in the "chat" context
 * await startLink("chat", "message-1");
 * // ... perform async operation ...
 * endLink("chat", "message-1");
 *
 * // Next operation will wait for the previous one to complete
 * await startLink("chat", "message-2");
 * // ... perform async operation ...
 * endLink("chat", "message-2");
 * ```
 * OR
 *
 * ```typescript
 * // Start a new link in the "chat" context
 * const chain = await startLink("chat", "message-1");
 * // ... perform async operation ...
 * chain.end("message-1");
 *
 * // Next operation will wait for the previous one to complete
 * const chain2 = await startLink("chat", "message-2");
 * // ... perform async operation ...
 * chain2.end("message-2");
 * ```
 *
 * With timeout support:
 * ```typescript
 * try {
 *   // Start with a 5-second timeout
 *   const chain = await startLink("chat", "message-1", 5000);
 *   // ... perform async operation ...
 *   chain.end("message-1");
 * } catch (error) {
 *   // Handle timeout or other errors
 *   console.error("Operation timed out or failed:", error);
 * }
 * ```
 */




/**
 * Creates a locked promise pair for synchronization with optional timeout
 *
 * This function creates a promise that can be manually resolved via the unlock function
 * or automatically rejected after a timeout period. The promise is designed to be used
 * as a synchronization point in the locking chain.
 *
 * @param timeoutMs - Timeout in milliseconds (0 or negative means no timeout)
 * @returns An object containing:
 *   - promise: A promise that resolves when unlock() is called or rejects on timeout
 *   - unlock: A function to resolve the promise and release the lock
 */
const createLockedPromise = (timeoutMs?: number) => {
  let unlock = () => {};
  let rejectLink: (reason?: any) => void = () => {};
  let withClearTimeout = (fn: () => void, timeout: NodeJS.Timeout | undefined) => () => {
    if (timeout) {
      clearTimeout(timeout);
    }
    fn();
  }

  // Simple promise that resolves when unlock() is called
  // This acts as the synchronization point for the chain
  const promise = new Promise<void>((resolve, reject) => {
    let timeout: NodeJS.Timeout | undefined;
    let rejectCb = () => {
      reject("Chain was reset");
    };
    
    if (timeoutMs && timeoutMs > 0) {
      timeout = setTimeout(rejectCb, timeoutMs);
    }
    rejectLink = withClearTimeout(rejectCb, timeout);
    unlock = withClearTimeout(resolve, timeout);
  });

  return {
    promise,
    unlock,
    reject: rejectLink
  }
}

/**
 * Type definition for a locking chain that manages sequential execution of operations
 *
 * A Chain object provides methods to control the flow of operations within a specific context.
 * It ensures that operations execute one at a time in the order they were started.
 */
export type Chain = {
  /**
   * Ends the current link in the chain, releasing the lock
   *
   * This method should be called when an operation completes (successfully or with error).
   * It releases the lock held by the operation, allowing the next operation in the queue
   * to proceed. Always call this method, preferably in a finally block.
   *
   * @param id - Identifier for the operation being ended (used for logging and cleanup)
   * @returns The chain object for method chaining
   */
  end: (id: string) => Chain;

  /**
   * Starts a new link in the chain, waiting for the previous operation to complete
   *
   * This method will block until all previous operations in the chain have completed.
   * If a timeout is specified, the promise will reject if the wait exceeds the timeout.
   *
   * @param nextId - Identifier for the operation being started (used for logging)
   * @param timeoutMs - Optional timeout in milliseconds (0 or negative means no timeout)
   * @returns Promise that resolves when it's this operation's turn to execute
   */
  start: (nextId: string, timeoutMs?: number) => Promise<Chain>;

  /**
   * Rejects all pending operations in the chain
   *
   * This method is used internally by resetChain() to abort all operations that are
   * currently waiting to start. It does not affect operations that have already started.
   * This is primarily used for error recovery and cleanup scenarios.
   */
  rejectAll: () => void;
  unlockAll: () => void;
}

/**
 * Global registry of locking chains, keyed by context string
 * Each context maintains its own independent chain of operations
 */
const chains = new Map<string, Chain>();

/**
 * Creates a synchronous locking chain for a specific context
 *
 * A locking chain ensures that operations execute sequentially within a context.
 * Each chain maintains:
 * - currentPromise: The promise that the next operation must wait for
 * - locks: A Map storing unlock functions keyed by operation ID
 * - chainId: A unique identifier for debugging and logging
 *
 * The chain works by:
 * 1. start() waits for the previous operation to complete, then sets up a new lock
 * 2. end() finds and calls the unlock function for the specific operation ID
 *
 * @returns A chain object with start() and end() methods
 */
const createSyncLockChain = (): Chain => {
  let currentPromise = Promise.resolve();
  const locks = new Map<string, () => void>(); // Maps operation IDs to their unlock functions
  const rejectors = new Map<string, (reason?: any) => void>(); // Maps operation IDs to their reject functions
  const chainId = crypto.randomUUID();

  const chain: Chain = {
    /**
     * Ends the current link in the chain
     *
     * This method finds the unlock function for the specified operation ID and calls it,
     * releasing the lock held by that operation. This allows the next operation in the
     * chain to proceed. The id parameter is used both for finding the correct unlock
     * function and for logging to help debug the sequence of operations.
     *
     * @param id - Identifier for the operation being ended (used to find the unlock function)
     * @returns The chain object for method chaining
     */
    end: (id: string): Chain => {
      const unlock = locks.get(id); // Find the unlock function for this operation
      console.debug("END LINK: ", id, chainId);
      unlock?.(); // Call the unlock function if it exists
      return chain;
    },

    /**
     * Starts a new link in the chain
     *
     * This method:
     * 1. Creates a new locked promise for this operation (with optional timeout)
     * 2. Waits for the previous operation to complete
     * 3. Sets up the new lock for the next operation to wait on
     * 4. Stores the unlock function in the locks Map for later retrieval
     *
     * The await ensures operations execute sequentially - each start() call
     * will block until the previous operation calls end(). If a timeout is specified,
     * the promise will reject if the operation doesn't complete within the timeout period.
     *
     * @param nextId - Identifier for the operation being started (for logging and unlock lookup)
     * @param timeoutMs - Optional timeout in milliseconds (0 or negative means no timeout)
     * @returns Promise that resolves when it's this operation's turn to execute
     */
    start: async (nextId: string, timeoutMs?: number): Promise<Chain> => {
      const { promise, unlock, reject } = createLockedPromise(timeoutMs);
      const currentLockingPromise = currentPromise; // Capture the promise we need to wait for

      // Update the chain state for the next operation
      currentPromise = promise.finally(() => {
        locks.delete(nextId);
        rejectors.delete(nextId);
      }); // Next operation will wait for this promise

      locks.set(nextId, () => {
        unlock();
        locks.delete(nextId);
      }); // Store unlock function for this operation ID
      rejectors.set(nextId, () => {
        reject();
        rejectors.delete(nextId);
      }); // Store reject function for this operation ID

      // Wait for the previous operation to complete
      await currentLockingPromise;

      console.debug("START LINK: ", nextId, chainId);
      return chain;
    },

    /**
     * Rejects all pending operations in this chain
     *
     * This method iterates through all stored reject functions and calls them,
     * effectively cancelling all operations that are currently waiting to start.
     * After rejecting all operations, it clears the rejectors map to prevent
     * memory leaks and ensure a clean state for future operations.
     *
     * This is used by resetChain() to implement chain reset functionality.
     */
    rejectAll: () => {
      currentPromise = Promise.resolve();
      try {
        rejectors.forEach((reject) => {
          reject(new Error("Chain was reset"));
        });
      } finally {
        locks.clear();
        rejectors.clear();
      }
    },

    unlockAll: () => {
      currentPromise = Promise.resolve();
      try {
        locks.forEach((unlock) => {
          unlock();
        });
      } finally {
        locks.clear();
        rejectors.clear();
      }
    }
  };

  return chain;
}

/**
 * Starts a new link in the locking chain for the given context
 *
 * This function:
 * 1. Gets or creates a locking chain for the specified context
 * 2. Calls start() on the chain, which will wait for any previous operation to complete
 * 3. Returns a promise that resolves when it's this operation's turn to execute
 *
 * Usage patterns:
 * ```typescript
 * // Option 1: Using the module-level functions
 * await startLink("chat", "message-123");
 * // ... perform your async operation ...
 * endLink("chat", "message-123");
 *
 * // Option 2: Using the returned chain object
 * const chain = await startLink("chat", "message-123");
 * // ... perform your async operation ...
 * chain.end("message-123");
 *
 * // Option 3: With timeout (will reject if operation takes too long)
 * try {
 *   const chain = await startLink("chat", "message-123", 5000); // 5 second timeout
 *   // ... perform your async operation ...
 *   chain.end("message-123");
 * } catch (error) {
 *   // Handle timeout or other errors
 * }
 * ```
 *
 * @param context - The context identifier (e.g., "chat", "file-operations")
 * @param id - Unique identifier for this operation (used for logging)
 * @param timeoutMs - Optional timeout in milliseconds (0 or negative means no timeout)
 * @returns Promise that resolves when the operation can proceed
 */
export const startLink = (context: string, id: string, timeoutMs?: number): Promise<Chain> => {
  const chain = chains.get(context) ?? createSyncLockChain();
  if (!chains.has(context)) {
    chains.set(context, chain);
  }
  return chain.start(id, timeoutMs);
}

/**
 * Ends the current link in the locking chain for the given context
 *
 * This function releases the lock held by the current operation, allowing the next
 * operation in the chain to proceed. Always call this after your operation completes,
 * preferably in a finally block to ensure it's called even if errors occur.
 *
 * Note: You can also call chain.end(id) directly on the chain object returned by startLink()
 * instead of using this module-level function.
 *
 * @param context - The context identifier (must match the one used in startLink)
 * @param id - Unique identifier for this operation (used for logging and unlock lookup)
 * @returns The chain object (for potential method chaining)
 */
export const endLink = (context: string, id: string): Chain => {
  const chain = chains.get(context) ?? createSyncLockChain();
  if (!chains.has(context)) {
    chains.set(context, chain);
  }
  return chain.end(id);
}


/**
 * Resets the locking chain for a specific context, rejecting all pending operations
 *
 * This function is useful for error recovery scenarios where you need to abort
 * all pending operations in a chain. It will:
 * 1. Find the chain for the specified context
 * 2. Reject all pending operations (those waiting in startLink calls)
 * 3. Allow new operations to start fresh
 *
 * Use cases:
 * - Error recovery when an operation fails and you want to clear the queue
 * - Cancelling all pending operations when a user navigates away
 * - Resetting state when switching contexts or modes
 *
 * Note: This only affects operations that are waiting to start. Operations that
 * have already started (returned from startLink) will continue running and should
 * still call end() to clean up properly.
 *
 * @param context - The context identifier for the chain to reset
 *
 * @example
 * ```typescript
 * // Start some operations
 * const chain1 = await startLink("chat", "msg-1");
 * const promise2 = startLink("chat", "msg-2"); // This will be waiting
 * const promise3 = startLink("chat", "msg-3"); // This will be waiting
 *
 * // Something goes wrong, reset the chain
 * resetChain("chat"); // promise2 and promise3 will be rejected
 *
 * // Clean up the running operation
 * chain1.end("msg-1");
 *
 * // Can start fresh operations
 * const newChain = await startLink("chat", "msg-4"); // Works immediately
 * ```
 */
export const resetChain = (context: string) => {
  try {
    const chain = chains.get(context);
    if (chain) {
      chain.rejectAll();
    }
  } finally {
    chains.delete(context);
  }
}

export const unlockChain = (context: string) => {
  const chain = chains.get(context);
  if (chain) {
    chain.unlockAll();
  }
}

export const _DO_NOT_USE_IN_APP_CODE_unlockAllChains = () => {
  try {
    chains.forEach((chain) => {
      chain.unlockAll();
    });
  } finally {
    chains.clear();
  }
}
