import { type Rule, RuleType } from "@augment-internal/sidecar-libs/src/chat/chat-types";

/**
 * Parameters for calculating rules and guidelines character count
 */
interface CalculateRulesCharacterCountParams {
  rules: Rule[];
  workspaceGuidelinesContent: string;
  contextRules?: Rule[];
  rulesAndGuidelinesLimit?: number;
}

/**
 * Approximates the total character count of all rules and workspace guidelines
 * that would be included in the chat context. This is approximated as number of characters
 * in the rule content and path, plus some amount of boilerplate for each rule, actual calculation is
 * done in the prompt on the backend in python
 *
 * @param params - Object containing rules, workspace guidelines content, and optional context rules
 * @returns Total character count of rules and guidelines
 */
export function calculateRulesAndGuidelinesCharacterCount(
  params: CalculateRulesCharacterCountParams,
) {
  const { rules, workspaceGuidelinesContent, contextRules = [] } = params;
  const alwaysRules = rules.filter((r) => r.type === RuleType.ALWAYS_ATTACHED);
  // Calculate character count from rules content
  const alwaysRulesCharCount = alwaysRules.reduce(
    (total, rule) => total + rule.content.length + rule.path.length,
    0,
  );

  const AUTO_RULE_BOILERPLATE_LENGTH = 100;
  const autoRequestedRules = rules.filter((r) => r.type === RuleType.AGENT_REQUESTED);
  const autoRequestedCharCount = autoRequestedRules.reduce(
    (total, rule) =>
      total + AUTO_RULE_BOILERPLATE_LENGTH + (rule.description?.length ?? 0) + rule.path.length,
    0,
  );

  // Only include manual rules in context if available
  const manualRules = rules
    .filter((r) => r.type === RuleType.MANUAL)
    .filter((r) => contextRules.some((rule) => rule.path === r.path));
  const manualRulesCharCount = manualRules.reduce(
    (total, rule) => total + rule.content.length + rule.path.length,
    0,
  );

  // Add workspace guidelines character count
  const workspaceGuidelinesCharCount = workspaceGuidelinesContent.length;

  const totalCharacterCount =
    alwaysRulesCharCount +
    manualRulesCharCount +
    autoRequestedCharCount +
    workspaceGuidelinesCharCount;
  const isOverLimit =
    params.rulesAndGuidelinesLimit && totalCharacterCount > params.rulesAndGuidelinesLimit;

  return {
    totalCharacterCount,
    isOverLimit,
    warningMessage:
      isOverLimit && params.rulesAndGuidelinesLimit
        ? `Total number of characters in included rules and workspace guidelines (${totalCharacterCount} chars)
        exceeds the limit of ${params.rulesAndGuidelinesLimit} characters, remove some rules
        or reduce the length of your guidelines.`
        : undefined,
  };
}
