import {
  ChatItemType,
  ExchangeStatus,
  type ExchangeWithStatus,
  type ExchangePointer,
} from "../types/chat-message";

// Minimal webview utilities for ExchangePointer operations

/**
 * Creates an ExchangePointer from a full ExchangeWithStatus.
 * This function extracts minimal metadata needed for UI rendering
 * without loading the full exchange data.
 */
export function createExchangePointer(exchange: ExchangeWithStatus): ExchangePointer {
  const exchangeUuid = exchange.request_id || crypto.randomUUID();

  return {
    chatItemType: ChatItemType.exchangePointer,
    exchangeUuid,
    // eslint-disable-next-line @typescript-eslint/naming-convention
    request_id: exchange.request_id,
    timestamp: exchange.timestamp,
    status: exchange.status,
    hasResponse: !!exchange.response_text,
    isStreaming: exchange.status === ExchangeStatus.sent,
  };
}
