// TODO (jw): move most of the logic in AugmentMessage.svelte here

import { type RemoteAgentsModel } from "../../remote-agent-manager/models/remote-agents-model";
import { type ChatModel } from "../models/chat-model";
import { type ConversationModel } from "../models/conversation-model";

export type MessageItemName = "share" | "retry";

interface TooltipItem {
  label: string;
  action: () => void;
  id: string;
  disabled: boolean;
  icon: MessageItemName;
  successMessage: string;
}

export interface AugmentMessageContext {
  tooltipItems: TooltipItem[];
}

function getShareAction(
  chatModel?: ChatModel,
  conversationModel?: ConversationModel,
  remoteAgentsModel?: RemoteAgentsModel,
) {
  if (remoteAgentsModel?.isActive) {
    return async () => {
      if (!remoteAgentsModel.currentAgentId) {
        return;
      }
      try {
        const url = await remoteAgentsModel.getConversationUrl(remoteAgentsModel.currentAgentId);
        if (url) {
          await navigator.clipboard.writeText(url);
        }
      } catch (err) {
        console.error("Failed to get conversation URL: ", err);
      }
    };
  }

  return async () => {
    if (!conversationModel?.id) {
      return;
    }
    try {
      const url = await chatModel?.getConversationUrl(conversationModel.id);
      if (url) {
        await navigator.clipboard.writeText(url);
      }
    } catch (err) {
      console.error("Failed to get conversation URL: ", err);
    }
  };
}

export function getAugmentMessageContext(
  chatModel?: ChatModel,
  conversationModel?: ConversationModel,
  remoteAgentsModel?: RemoteAgentsModel,
): AugmentMessageContext {
  return {
    tooltipItems: [
      {
        label: "Share",
        action: getShareAction(chatModel, conversationModel, remoteAgentsModel),
        id: "share-message",
        disabled: !chatModel?.flags.enableShareService,
        icon: "share",
        successMessage: "Link copied!",
      },
    ],
  };
}
