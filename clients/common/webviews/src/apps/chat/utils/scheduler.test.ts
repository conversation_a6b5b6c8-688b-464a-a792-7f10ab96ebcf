import {
  describe,
  expect,
  test,
  beforeEach,
  afterEach,
  vi as jest,
  type MockInstance,
} from "vitest";
import { Scheduler } from "./scheduler";

describe("Scheduler", () => {
  let scheduler: Scheduler;
  let mockSetTimeout: MockInstance<typeof setTimeout>;

  beforeEach(() => {
    jest.useFakeTimers();
    mockSetTimeout = jest.fn().mockImplementation((...rest: Parameters<typeof setTimeout>) => {
      return setTimeout(...rest);
    });
    scheduler = new Scheduler(true, mockSetTimeout as any);
  });

  afterEach(() => {
    scheduler?.dispose();
    jest.useRealTimers();
  });

  describe("once", () => {
    test("executes callback once after specified timeout", () => {
      const callback = jest.fn();
      scheduler.once(1000, callback);

      expect(callback).not.toHaveBeenCalled();
      jest.advanceTimersByTime(1000);
      expect(callback).toHaveBeenCalledTimes(1);

      jest.advanceTimersByTime(1000);
      expect(callback).toHaveBeenCalledTimes(1);
    });

    test("can be cancelled", () => {
      const callback = jest.fn();
      const cancel = scheduler.once(1000, callback);

      cancel();
      jest.advanceTimersByTime(1000);
      expect(callback).not.toHaveBeenCalled();
    });
    test("it will execute again if stopped and started again", () => {
      const callback = jest.fn();
      scheduler.once(1000, callback);
      jest.advanceTimersByTime(1000);
      expect(callback).toHaveBeenCalledTimes(1);

      scheduler.stop();
      scheduler.start();
      jest.advanceTimersByTime(1000);
      expect(callback).toHaveBeenCalledTimes(2);
    });
  });

  describe("interval", () => {
    test("executes callback repeatedly at specified interval", async () => {
      const callback = jest.fn();
      scheduler.interval(1000, callback);

      expect(callback).not.toHaveBeenCalled();
      jest.advanceTimersByTime(1000);

      expect(callback).toHaveBeenCalledTimes(1);
      jest.advanceTimersByTime(1000);

      expect(callback).toHaveBeenCalledTimes(2);
    });

    test("can be cancelled", () => {
      const callback = jest.fn();
      const cancel = scheduler.interval(1000, callback);

      jest.advanceTimersByTime(1000);
      expect(callback).toHaveBeenCalledTimes(1);

      cancel();
      jest.advanceTimersByTime(1000);
      expect(callback).toHaveBeenCalledTimes(1);
    });
  });

  describe("at", () => {
    test("executes callback at specified date", () => {
      const callback = jest.fn();
      const futureDate = new Date(Date.now() + 1000);
      scheduler.at(futureDate, callback);

      expect(callback).not.toHaveBeenCalled();
      jest.advanceTimersByTime(1000);
      expect(callback).toHaveBeenCalledTimes(1);
      //Should not execute again if stopped and started again
      scheduler.stop();
      scheduler.start();
      jest.advanceTimersByTime(1000);
      expect(callback).toHaveBeenCalledTimes(1);
    });

    test("executes callback at specified offset", () => {
      const callback = jest.fn();
      scheduler.at(1000, callback);

      expect(callback).not.toHaveBeenCalled();
      jest.advanceTimersByTime(1000);
      expect(callback).toHaveBeenCalledTimes(1);
      //Should not execute again if stopped and started again
      scheduler.stop();
      scheduler.start();
      jest.advanceTimersByTime(1000);
      expect(callback).toHaveBeenCalledTimes(1);
    });

    test("does not execute if date is in the past", () => {
      const callback = jest.fn();
      const pastDate = new Date(Date.now() - 1000);
      scheduler.at(pastDate, callback);

      jest.advanceTimersByTime(1000);
      expect(callback).not.toHaveBeenCalled();
    });
  });

  describe("start/stop", () => {
    test("stops executing callbacks when stopped", () => {
      const callback = jest.fn();
      scheduler.interval(1000, callback);

      jest.advanceTimersByTime(1000);
      expect(callback).toHaveBeenCalledTimes(1);

      scheduler.stop();
      jest.advanceTimersByTime(1000);
      expect(callback).toHaveBeenCalledTimes(1);
    });

    test("resumes executing callbacks when started", () => {
      const callback = jest.fn();
      scheduler.interval(1000, callback);

      scheduler.isStarted = false;
      jest.advanceTimersByTime(1000);
      expect(callback).not.toHaveBeenCalled();

      scheduler.isStarted = true;
      jest.advanceTimersByTime(1000);
      expect(callback).toHaveBeenCalledTimes(1);
    });
  });

  describe("dispose", () => {
    test("clears all scheduled callbacks", () => {
      const callback1 = jest.fn();
      const callback2 = jest.fn();

      scheduler.once(1000, callback1);
      scheduler.interval(1000, callback2);

      scheduler.dispose();
      jest.advanceTimersByTime(2000);

      expect(callback1).not.toHaveBeenCalled();
      expect(callback2).not.toHaveBeenCalled();
    });
  });
  describe("reschedule", () => {
    test("reschedules all callbacks", () => {
      const callback1 = jest.fn();
      const callback2 = jest.fn();

      scheduler.once(1000, callback1);
      scheduler.interval(1000, callback2);
      jest.advanceTimersByTime(500);
      expect(callback1).not.toHaveBeenCalled();
      expect(callback2).not.toHaveBeenCalled();

      scheduler.stop();
      scheduler.reschedule();
      scheduler.start();
      jest.advanceTimersByTime(2000);

      expect(callback1).toHaveBeenCalledTimes(1);
      expect(callback2).toHaveBeenCalledTimes(2);
    });
  });
});
