import { describe, it, expect } from "vitest";
import {
  extractImagesFromContent,
  calculateImageStats,
  extractTextFilesFromContent,
  calculateTextStats,
} from "./file-utils";
import type { JSONContent } from "@tiptap/core";

describe("image-utils", () => {
  describe("extractImagesFromContent", () => {
    it("should return empty array for null/undefined content", () => {
      expect(extractImagesFromContent(null)).toEqual([]);
      expect(extractImagesFromContent(undefined)).toEqual([]);
    });

    it("should extract image information from rich text content", () => {
      const content: JSONContent = {
        type: "doc",
        content: [
          {
            type: "paragraph",
            content: [
              {
                type: "file",
                attrs: {
                  src: "image1.jpg",
                  title: "Test Image 1",
                  fileSizeBytes: 1024,
                  isLoading: false,
                },
              },
            ],
          },
          {
            type: "paragraph",
            content: [
              {
                type: "file",
                attrs: {
                  src: "image2.png",
                  title: "Test Image 2",
                  fileSizeBytes: 2048,
                  isLoading: true,
                },
              },
            ],
          },
        ],
      };

      const images = extractImagesFromContent(content);
      expect(images).toHaveLength(2);
      expect(images[0]).toEqual({
        src: "image1.jpg",
        title: "Test Image 1",
        fileSizeBytes: 1024,
        isLoading: false,
      });
      expect(images[1]).toEqual({
        src: "image2.png",
        title: "Test Image 2",
        fileSizeBytes: 2048,
        isLoading: true,
      });
    });

    it("should handle content without images", () => {
      const content: JSONContent = {
        type: "doc",
        content: [
          {
            type: "paragraph",
            content: [
              {
                type: "text",
                text: "Just some text",
              },
            ],
          },
        ],
      };

      const images = extractImagesFromContent(content);
      expect(images).toEqual([]);
    });
  });

  describe("calculateImageStats", () => {
    it("should return zero stats for null/undefined content", () => {
      expect(calculateImageStats(null)).toEqual({
        imageCount: 0,
        imageSizeSumKb: 0,
      });
      expect(calculateImageStats(undefined)).toEqual({
        imageCount: 0,
        imageSizeSumKb: 0,
      });
    });

    it("should calculate correct stats excluding loading images", () => {
      const content: JSONContent = {
        type: "doc",
        content: [
          {
            type: "paragraph",
            content: [
              {
                type: "file",
                attrs: {
                  src: "image1.jpg",
                  title: "Test Image 1",
                  fileSizeBytes: 1024, // 1KB
                  isLoading: false,
                },
              },
              {
                type: "file",
                attrs: {
                  src: "image2.png",
                  title: "Test Image 2",
                  fileSizeBytes: 2048, // 2KB
                  isLoading: true, // This should be excluded
                },
              },
              {
                type: "file",
                attrs: {
                  src: "image3.gif",
                  title: "Test Image 3",
                  fileSizeBytes: 3072, // 3KB
                  isLoading: false,
                },
              },
            ],
          },
        ],
      };

      const stats = calculateImageStats(content);
      expect(stats).toEqual({
        imageCount: 2, // Only non-loading images
        imageSizeSumKb: 4, // (1024 + 3072) / 1024 = 4KB
      });
    });

    it("should handle content without images", () => {
      const content: JSONContent = {
        type: "doc",
        content: [
          {
            type: "paragraph",
            content: [
              {
                type: "text",
                text: "Just some text",
              },
            ],
          },
        ],
      };

      const stats = calculateImageStats(content);
      expect(stats).toEqual({
        imageCount: 0,
        imageSizeSumKb: 0,
      });
    });
  });

  describe("extractTextFilesFromContent", () => {
    it("should return empty array for null/undefined content", () => {
      expect(extractTextFilesFromContent(null)).toEqual([]);
      expect(extractTextFilesFromContent(undefined)).toEqual([]);
    });

    it("should extract text file information from rich text content", () => {
      const content: JSONContent = {
        type: "doc",
        content: [
          {
            type: "paragraph",
            content: [
              {
                type: "file",
                attrs: {
                  src: "document.txt",
                  title: "Test Document",
                  fileSizeBytes: 1024,
                  isLoading: false,
                  mimeType: "text/plain",
                },
              },
              {
                type: "file",
                attrs: {
                  src: "image.jpg",
                  title: "Test Image",
                  fileSizeBytes: 2048,
                  isLoading: false,
                  mimeType: "image/jpeg",
                },
              },
              {
                type: "file",
                attrs: {
                  src: "data.csv",
                  title: "Test CSV",
                  fileSizeBytes: 512,
                  isLoading: true,
                  mimeType: "text/csv",
                },
              },
            ],
          },
        ],
      };

      const textFiles = extractTextFilesFromContent(content);
      expect(textFiles).toHaveLength(2); // Should exclude the image file
      expect(textFiles[0]).toEqual({
        src: "document.txt",
        title: "Test Document",
        fileSizeBytes: 1024,
        isLoading: false,
      });
      expect(textFiles[1]).toEqual({
        src: "data.csv",
        title: "Test CSV",
        fileSizeBytes: 512,
        isLoading: true,
      });
    });

    it("should handle content without text files", () => {
      const content: JSONContent = {
        type: "doc",
        content: [
          {
            type: "paragraph",
            content: [
              {
                type: "file",
                attrs: {
                  src: "image.jpg",
                  title: "Test Image",
                  fileSizeBytes: 2048,
                  isLoading: false,
                  mimeType: "image/jpeg",
                },
              },
            ],
          },
        ],
      };

      const textFiles = extractTextFilesFromContent(content);
      expect(textFiles).toEqual([]);
    });
  });

  describe("calculateTextStats", () => {
    it("should return zero stats for null/undefined content", () => {
      expect(calculateTextStats(null)).toEqual({
        textSizeSumKb: 0,
      });
      expect(calculateTextStats(undefined)).toEqual({
        textSizeSumKb: 0,
      });
    });

    it("should calculate correct stats excluding loading text files", () => {
      const content: JSONContent = {
        type: "doc",
        content: [
          {
            type: "paragraph",
            content: [
              {
                type: "file",
                attrs: {
                  src: "document1.txt",
                  title: "Test Document 1",
                  fileSizeBytes: 1024, // 1KB
                  isLoading: false,
                  mimeType: "text/plain",
                },
              },
              {
                type: "file",
                attrs: {
                  src: "document2.txt",
                  title: "Test Document 2",
                  fileSizeBytes: 2048, // 2KB
                  isLoading: true, // This should be excluded
                  mimeType: "text/plain",
                },
              },
              {
                type: "file",
                attrs: {
                  src: "image.jpg",
                  title: "Test Image",
                  fileSizeBytes: 4096, // 4KB - should be excluded (image)
                  isLoading: false,
                  mimeType: "image/jpeg",
                },
              },
              {
                type: "file",
                attrs: {
                  src: "data.csv",
                  title: "Test CSV",
                  fileSizeBytes: 3072, // 3KB
                  isLoading: false,
                  mimeType: "text/csv",
                },
              },
            ],
          },
        ],
      };

      const stats = calculateTextStats(content);
      expect(stats).toEqual({
        textSizeSumKb: 4, // (1024 + 3072) / 1024 = 4KB (excludes loading and image files)
      });
    });

    it("should handle content without text files", () => {
      const content: JSONContent = {
        type: "doc",
        content: [
          {
            type: "paragraph",
            content: [
              {
                type: "file",
                attrs: {
                  src: "image.jpg",
                  title: "Test Image",
                  fileSizeBytes: 2048,
                  isLoading: false,
                  mimeType: "image/jpeg",
                },
              },
            ],
          },
        ],
      };

      const stats = calculateTextStats(content);
      expect(stats).toEqual({
        textSizeSumKb: 0,
      });
    });
  });
});
