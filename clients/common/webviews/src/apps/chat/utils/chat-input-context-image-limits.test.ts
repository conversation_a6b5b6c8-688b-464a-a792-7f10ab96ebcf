/* eslint-disable @typescript-eslint/naming-convention */
import { describe, it, expect, vi, beforeEach } from "vitest";
import { getChatInputContext } from "./chat-input-context";
import type { ChatModel } from "../models/chat-model";
import type { AgentConversationModel } from "../models/agent-conversation-model";
import type { ToolsWebviewModel } from "../models/tools-webview-model";
import type { SlashCommandModel } from "../models/slash-command-model";
import { SendMode } from "../types/send-mode";
import type { JSONContent } from "@tiptap/core";
import { get } from "svelte/store";
import { FileController } from "$common-webviews/src/design-system/components/RichTextEditorAugment/plugins/File/controller";

// Mock the svelte/store get function
vi.mock("svelte/store", () => ({
  get: vi.fn(),
}));

// Mock the FileController
vi.mock(
  "$common-webviews/src/design-system/components/RichTextEditorAugment/plugins/File/controller",
  () => ({
    FileController: {
      hasLoadingImages: vi.fn(),
    },
  }),
);

describe("getChatInputContext - Image Limits", () => {
  let chatModel: ChatModel;
  let agentConversationModel: AgentConversationModel;
  let toolsWebviewModel: ToolsWebviewModel;
  let slashCommandModel: SlashCommandModel;
  let mockConversationModel: any;

  beforeEach(() => {
    // Reset mocks
    vi.resetAllMocks();

    // Mock the get function to return false for stores
    vi.mocked(get).mockImplementation((store: any) => {
      if (store === agentConversationModel?.isCurrConversationAgentic) {
        return false;
      }
      if (store === slashCommandModel?.activeCommand) {
        return undefined;
      }
      return false;
    });

    // Mock FileController.hasLoadingImages to return false by default
    vi.mocked(FileController.hasLoadingImages).mockReturnValue(false);
  });

  const createMockChatModel = (richTextContent?: JSONContent) => {
    mockConversationModel = {
      draftExchange: (() => {
        const exchange: Record<string, any> = {};
        exchange["request_message"] = "Test message";
        exchange["rich_text_json_repr"] = richTextContent;
        return exchange;
      })(),
      hasDraft: true,
      canSendDraft: true,
      canCancelMessage: false,
    };

    return {
      flags: { enableChatMultimodal: true },
      currentConversationModel: mockConversationModel,
    } as unknown as ChatModel;
  };

  const createMockAgentConversationModel = () => {
    return {
      isCurrConversationAgentic: {},
    } as unknown as AgentConversationModel;
  };

  const createMockToolsWebviewModel = () => {
    return {} as unknown as ToolsWebviewModel;
  };

  const createMockSlashCommandModel = () => {
    return {
      activeCommand: {},
    } as unknown as SlashCommandModel;
  };

  it("should disable send button when image count exceeds limit", () => {
    // Create content with 12 images (exceeds limit of 10)
    const richTextContent: JSONContent = {
      type: "doc",
      content: Array.from({ length: 12 }, (_, i) => ({
        type: "paragraph",
        content: [
          {
            type: "file",
            attrs: {
              src: `image${i}.jpg`,
              title: `Image ${i}`,
              fileSizeBytes: 100 * 1024, // 100KB each
              isLoading: false,
              mimeType: "image/jpeg", // Add mimeType to identify as image
            },
          },
        ],
      })),
    };

    chatModel = createMockChatModel(richTextContent);
    agentConversationModel = createMockAgentConversationModel();
    toolsWebviewModel = createMockToolsWebviewModel();
    slashCommandModel = createMockSlashCommandModel();

    const context = getChatInputContext(
      chatModel,
      agentConversationModel,
      toolsWebviewModel,
      slashCommandModel,
      SendMode.send,
    );

    expect(context.stateName).toBe("overImageLimit");
    expect(context.isDisabled).toBe(true);
    expect(context.disabledReason).toBe("Up to 10 images are supported.");
    expect(context.showCancelButton).toBe(false);
  });

  it("should disable send button when total image size exceeds limit", () => {
    // Create content with 1 large image (exceeds 1024KB limit)
    const richTextContent: JSONContent = {
      type: "doc",
      content: [
        {
          type: "paragraph",
          content: [
            {
              type: "file",
              attrs: {
                src: "large-image.jpg",
                title: "Large Image",
                fileSizeBytes: 2048 * 1024, // 2MB
                isLoading: false,
                mimeType: "image/jpeg", // Add mimeType to identify as image
              },
            },
          ],
        },
      ],
    };

    chatModel = createMockChatModel(richTextContent);
    agentConversationModel = createMockAgentConversationModel();
    toolsWebviewModel = createMockToolsWebviewModel();
    slashCommandModel = createMockSlashCommandModel();

    const context = getChatInputContext(
      chatModel,
      agentConversationModel,
      toolsWebviewModel,
      slashCommandModel,
      SendMode.send,
    );

    expect(context.stateName).toBe("overImageLimit");
    expect(context.isDisabled).toBe(true);
    expect(context.disabledReason).toBe(
      "Total image size exceeds the 1024KB limit. Current size: 2048KB",
    );
    expect(context.showCancelButton).toBe(false);
  });

  it("should not disable send button when images are within limits", () => {
    // Create content with 2 small images (within limits)
    const richTextContent: JSONContent = {
      type: "doc",
      content: [
        {
          type: "paragraph",
          content: [
            {
              type: "file",
              attrs: {
                src: "small-image1.jpg",
                title: "Small Image 1",
                fileSizeBytes: 100 * 1024, // 100KB
                isLoading: false,
                mimeType: "image/jpeg", // Add mimeType to identify as image
              },
            },
            {
              type: "file",
              attrs: {
                src: "small-image2.jpg",
                title: "Small Image 2",
                fileSizeBytes: 200 * 1024, // 200KB
                isLoading: false,
                mimeType: "image/jpeg", // Add mimeType to identify as image
              },
            },
          ],
        },
      ],
    };

    chatModel = createMockChatModel(richTextContent);
    agentConversationModel = createMockAgentConversationModel();
    toolsWebviewModel = createMockToolsWebviewModel();
    slashCommandModel = createMockSlashCommandModel();

    const context = getChatInputContext(
      chatModel,
      agentConversationModel,
      toolsWebviewModel,
      slashCommandModel,
      SendMode.send,
    );

    // Should not be disabled due to image limits
    expect(context.stateName).not.toBe("overImageLimit");
    expect(context.isDisabled).toBe(false);
  });

  it("should ignore loading images when calculating limits", () => {
    // Create content with loading images that would exceed limits if counted
    const richTextContent: JSONContent = {
      type: "doc",
      content: Array.from({ length: 15 }, (_, i) => ({
        type: "paragraph",
        content: [
          {
            type: "file",
            attrs: {
              src: `image${i}.jpg`,
              title: `Image ${i}`,
              fileSizeBytes: 100 * 1024, // 100KB each
              isLoading: i >= 5, // First 5 are loaded, rest are loading
              mimeType: "image/jpeg", // Add mimeType to identify as image
            },
          },
        ],
      })),
    };

    chatModel = createMockChatModel(richTextContent);
    agentConversationModel = createMockAgentConversationModel();
    toolsWebviewModel = createMockToolsWebviewModel();
    slashCommandModel = createMockSlashCommandModel();

    const context = getChatInputContext(
      chatModel,
      agentConversationModel,
      toolsWebviewModel,
      slashCommandModel,
      SendMode.send,
    );

    // Should not be disabled due to image limits since only 5 loaded images
    expect(context.stateName).not.toBe("overImageLimit");
    expect(context.isDisabled).toBe(false);
  });
});
