/* eslint-disable @typescript-eslint/naming-convention */

import { describe, it, expect, beforeEach, vi } from "vitest";
import { getMessageListContext, getLastGroupConfig } from "./message-list-context";
import type { ChatModel } from "../models/chat-model";
import type { ConversationModel } from "../models/conversation-model";
import type { RemoteAgentsModel } from "$common-webviews/src/apps/remote-agent-manager/models/remote-agents-model";
import {
  AgentExchangeStatus,
  ExchangeStatus,
  SeenState,
  ChatItemType,
  type ChatItem,
} from "../types/chat-message";
import { ToolUsePhase } from "../types/tool-use-state";
import { SendMessageErrorType } from "../../remote-agent-manager/models/remote-agents-state-model/remote-agents-state-model";
import { RemoteAgentWorkspaceStatus } from "@augment-internal/sidecar-libs/src/remote-agent/remote-agent-types";

describe("message-list-context", () => {
  let chatModel: ChatModel;
  let conversationModel: ConversationModel;
  let remoteAgentsModel: RemoteAgentsModel;
  let mockChatHistory: ChatItem[];

  beforeEach(() => {
    // Reset mocks
    vi.resetAllMocks();

    // Create mock chat history
    mockChatHistory = [
      {
        chatItemType: ChatItemType.exchange,
        request_id: "request-1",
        request_message: "Hello",
        response_text: "Hi there",
        status: ExchangeStatus.success,
        seen_state: SeenState.seen,
        timestamp: new Date().toISOString(),
      },
      {
        chatItemType: ChatItemType.exchange,
        request_id: "request-2",
        request_message: "How are you?",
        response_text: "I'm doing well",
        status: ExchangeStatus.success,
        seen_state: SeenState.seen,
        timestamp: new Date().toISOString(),
      },
    ] as unknown as ChatItem[];

    // Create mock conversation model
    conversationModel = {
      chatHistory: mockChatHistory,
      lastExchange: mockChatHistory[1],
      getLastToolUseState: vi.fn().mockReturnValue({
        phase: ToolUsePhase.unknown,
      }),
      markSeen: vi.fn(),
    } as unknown as ConversationModel;

    // Create mock chat model
    chatModel = {
      currentConversationModel: conversationModel,
    } as unknown as ChatModel;

    // Create mock remote agents model
    remoteAgentsModel = {
      isActive: false,
      isCurrentAgentRunning: false,
      getCurrentChatHistory: vi.fn().mockReturnValue(mockChatHistory),
      getLastToolUseState: vi.fn().mockReturnValue({
        phase: ToolUsePhase.unknown,
      }),
    } as unknown as RemoteAgentsModel;
  });

  describe("getLastGroupConfig", () => {
    it("should show generating response when agent is running and not agentic", () => {
      const config = getLastGroupConfig(
        conversationModel,
        AgentExchangeStatus.running,
        false, // not agentic conversation
        remoteAgentsModel,
      );

      expect(config.showGeneratingResponse).toBe(true);
      expect(config.showRunningSpacer).toBe(false);
      expect(config.showAwaitingUserInput).toBe(false);
      expect(config.showStopped).toBe(false);
      expect(config.retryMessage).toBeUndefined();
    });

    it("should show generating response when agent is running and agentic but tool is not running", () => {
      const config = getLastGroupConfig(
        conversationModel,
        AgentExchangeStatus.running,
        true, // agentic conversation
        remoteAgentsModel,
      );

      expect(config.showGeneratingResponse).toBe(true);
      expect(config.showRunningSpacer).toBe(false);
    });

    it("should show running spacer when tool is running", () => {
      conversationModel.getLastToolUseState = vi.fn().mockReturnValue({
        phase: ToolUsePhase.running,
      });

      const config = getLastGroupConfig(
        conversationModel,
        AgentExchangeStatus.running,
        true, // agentic conversation
        remoteAgentsModel,
      );

      expect(config.showGeneratingResponse).toBe(false);
      expect(config.showRunningSpacer).toBe(true);
    });

    it("should show awaiting user input when agent is waiting", () => {
      const config = getLastGroupConfig(
        conversationModel,
        AgentExchangeStatus.awaitingUserAction,
        false,
        remoteAgentsModel,
      );

      expect(config.showAwaitingUserInput).toBe(true);
      expect(config.showRunningSpacer).toBe(true);
    });

    it("should show stopped when last message was cancelled", () => {
      // Create a new mock with a cancelled exchange
      const mockConversationWithCancelledExchange = {
        ...conversationModel,
        lastExchange: {
          ...mockChatHistory[1],
          status: ExchangeStatus.cancelled,
        },
      };

      const config = getLastGroupConfig(
        mockConversationWithCancelledExchange as ConversationModel,
        AgentExchangeStatus.notRunning,
        false,
        remoteAgentsModel,
      );

      expect(config.showStopped).toBe(true);
    });

    it("should show stopped when last tool was cancelled", () => {
      conversationModel.getLastToolUseState = vi.fn().mockReturnValue({
        phase: ToolUsePhase.cancelled,
      });

      const config = getLastGroupConfig(
        conversationModel,
        AgentExchangeStatus.notRunning,
        false,
        remoteAgentsModel,
      );

      expect(config.showStopped).toBe(true);
    });

    it("should show retry message when exchange is retriable and has error message", () => {
      // Create a new mock with a retriable exchange
      const mockConversationWithRetriableExchange = {
        ...conversationModel,
        lastExchange: {
          ...mockChatHistory[1],
          isRetriable: true,
          display_error_message: "Error occurred, please retry",
        },
      };

      const config = getLastGroupConfig(
        mockConversationWithRetriableExchange as ConversationModel,
        AgentExchangeStatus.running,
        false,
        remoteAgentsModel,
      );

      expect(config.retryMessage).toBe("Error occurred, please retry");
    });

    it("should use remote agent tool state when remote agent is active", () => {
      // Create a new mock with active remote agent
      const activeRemoteAgentsModel = {
        ...remoteAgentsModel,
        isActive: true,
        getLastToolUseState: vi.fn().mockReturnValue({
          phase: ToolUsePhase.running,
        }),
      };

      const config = getLastGroupConfig(
        conversationModel,
        AgentExchangeStatus.running,
        true,
        activeRemoteAgentsModel as unknown as RemoteAgentsModel,
      );

      expect(config.showRunningSpacer).toBe(true);
      expect(activeRemoteAgentsModel.getLastToolUseState).toHaveBeenCalled();
    });

    it("should return default config when agent is not running", () => {
      const config = getLastGroupConfig(
        conversationModel,
        AgentExchangeStatus.notRunning,
        false,
        remoteAgentsModel,
      );

      // Default config should have all flags set to false
      expect(config.showGeneratingResponse).toBe(false);
      expect(config.showRunningSpacer).toBe(false);
      expect(config.showAwaitingUserInput).toBe(false);
      expect(config.showStopped).toBe(false);
      expect(config.retryMessage).toBeUndefined();
    });

    it("should prioritize retry message over other UI states", () => {
      // Create a mock with a retriable exchange
      const mockConversationWithRetriableExchange = {
        ...conversationModel,
        lastExchange: {
          ...mockChatHistory[1],
          isRetriable: true,
          display_error_message: "Error occurred, please retry",
        },
        getLastToolUseState: vi.fn().mockReturnValue({
          phase: ToolUsePhase.running,
        }),
      };

      const config = getLastGroupConfig(
        mockConversationWithRetriableExchange as unknown as ConversationModel,
        AgentExchangeStatus.running,
        true, // agentic conversation
        remoteAgentsModel,
      );

      // Retry message should take precedence over other UI states
      expect(config.retryMessage).toBe("Error occurred, please retry");
      expect(config.showGeneratingResponse).toBe(false);
      expect(config.showRunningSpacer).toBe(false);
    });

    it("should show resuming remote agent when workspace status is resuming", () => {
      const activeRemoteAgentsModel = {
        ...remoteAgentsModel,
        isActive: true,
        currentAgent: {
          workspace_status: RemoteAgentWorkspaceStatus.workspaceResuming,
        },
        getLastToolUseState: vi.fn().mockReturnValue({
          phase: ToolUsePhase.unknown,
        }),
      } as unknown as RemoteAgentsModel;

      const config = getLastGroupConfig(
        conversationModel,
        AgentExchangeStatus.running,
        true,
        activeRemoteAgentsModel,
      );

      expect(config.showResumingRemoteAgent).toBe(true);
      expect(config.showGeneratingResponse).toBe(false);
      expect(config.showRunningSpacer).toBe(false);
    });
  });

  describe("getMessageListContext", () => {
    it("should return correct context when remote agent is not active", () => {
      // We need to mock the filter function behavior for non-remote agent case
      const filteredChatHistory = [...mockChatHistory];

      // Create a new mock with a custom implementation for the filter method
      const mockConversationWithFilter = {
        ...conversationModel,
        chatHistory: {
          filter: vi.fn().mockReturnValue(filteredChatHistory),
        },
      };

      const mockChatModelWithFilter = {
        ...chatModel,
        currentConversationModel: mockConversationWithFilter,
      };

      const context = getMessageListContext(
        mockChatModelWithFilter as unknown as ChatModel,
        AgentExchangeStatus.notRunning,
        false,
        remoteAgentsModel,
      );

      expect(context.chatHistory).toEqual(filteredChatHistory);
      expect(context.doShowFloatingButtons).toBe(true);
      expect(context.doShowAgentSetupLogs).toBe(false);
      expect(remoteAgentsModel.getCurrentChatHistory).not.toHaveBeenCalled();
    });

    it("should return correct context when remote agent is active", () => {
      const activeRemoteAgentsModel = {
        ...remoteAgentsModel,
        isActive: true,
      } as unknown as RemoteAgentsModel;

      const context = getMessageListContext(
        chatModel,
        AgentExchangeStatus.notRunning,
        false,
        activeRemoteAgentsModel,
      );

      expect(context.chatHistory).toEqual(mockChatHistory);
      expect(context.doShowFloatingButtons).toBe(false);
      expect(context.doShowAgentSetupLogs).toBe(true);
      expect(activeRemoteAgentsModel.getCurrentChatHistory).toHaveBeenCalled();
    });

    it("should use agent exchange status from remote agent when active", () => {
      const activeRemoteAgentsModel = {
        ...remoteAgentsModel,
        isActive: true,
        isCurrentAgentRunning: true,
      } as unknown as RemoteAgentsModel;

      const context = getMessageListContext(
        chatModel,
        AgentExchangeStatus.notRunning, // This should be overridden
        false,
        activeRemoteAgentsModel,
      );

      // The last group config should reflect the running state from the remote agent
      expect(context.lastGroupConfig.showGeneratingResponse).toBe(true);
    });

    it("should create a grouped chat history", () => {
      // Create a simple chat history
      const simpleChatHistory = [
        {
          chatItemType: ChatItemType.exchange,
          request_id: "request-1",
          request_message: "Hello",
          response_text: "Hi there",
          status: ExchangeStatus.success,
          seen_state: SeenState.seen,
          timestamp: new Date().toISOString(),
        },
        {
          chatItemType: ChatItemType.exchange,
          request_id: "request-2",
          request_message: "How are you?",
          response_text: "I'm doing well",
          status: ExchangeStatus.success,
          seen_state: SeenState.seen,
          timestamp: new Date().toISOString(),
        },
      ] as unknown as ChatItem[];

      // Create a mock with the simple chat history and proper filter implementation
      const mockConversationWithSimpleHistory = {
        ...conversationModel,
        chatHistory: {
          filter: vi.fn().mockReturnValue(simpleChatHistory),
        },
      };

      const mockChatModelWithSimpleHistory = {
        ...chatModel,
        currentConversationModel: mockConversationWithSimpleHistory,
      };

      const context = getMessageListContext(
        mockChatModelWithSimpleHistory as unknown as ChatModel,
        AgentExchangeStatus.notRunning,
        false,
        remoteAgentsModel,
      );

      // Verify that the chat history is correct
      expect(context.chatHistory).toEqual(simpleChatHistory);

      // Verify that groupedChatHistory is an array
      expect(Array.isArray(context.groupedChatHistory)).toBe(true);

      // Verify that groupedChatHistory has the correct number of groups
      // Each item should be in its own group since there are no tool results
      expect(context.groupedChatHistory.length).toBe(2);
    });

    it("should handle empty chat history", () => {
      // Create a mock with empty chat history
      const mockConversationWithEmptyHistory = {
        ...conversationModel,
        chatHistory: [],
      };

      const mockChatModelWithEmptyHistory = {
        ...chatModel,
        currentConversationModel: mockConversationWithEmptyHistory,
      };

      const context = getMessageListContext(
        mockChatModelWithEmptyHistory as unknown as ChatModel,
        AgentExchangeStatus.notRunning,
        false,
        remoteAgentsModel,
      );

      expect(context.chatHistory).toEqual([]);
      expect(context.groupedChatHistory).toEqual([]);
    });

    it("should handle different agent exchange statuses", () => {
      // Test with awaiting user action status
      const contextWithAwaitingStatus = getMessageListContext(
        chatModel,
        AgentExchangeStatus.awaitingUserAction,
        false,
        remoteAgentsModel,
      );

      expect(contextWithAwaitingStatus.lastGroupConfig.showAwaitingUserInput).toBe(true);
      expect(contextWithAwaitingStatus.lastGroupConfig.showRunningSpacer).toBe(true);
    });

    it("should group tool result messages with their parent messages", () => {
      // Create a chat history with tool results
      const chatHistoryWithTools = [
        {
          chatItemType: ChatItemType.exchange,
          request_id: "request-1",
          request_message: "Run a tool",
          response_text: "Running tool",
          status: ExchangeStatus.success,
          seen_state: SeenState.seen,
          timestamp: new Date().toISOString(),
        },
        {
          chatItemType: ChatItemType.exchange,
          request_id: "request-1", // Same request ID as previous message
          request_message: "Tool result",
          response_text: "Tool completed",
          status: ExchangeStatus.success,
          seen_state: SeenState.seen,
          timestamp: new Date().toISOString(),
          // Add the property that the groupChatHistory function checks for
          structured_request_nodes: [
            {
              type: 1, // ChatRequestNodeType.TOOL_RESULT
              id: 1,
              tool_result_node: {
                tool_use_id: "tool-1",
                content: "Tool result content",
                is_error: false,
              },
            },
          ],
        },
        {
          chatItemType: ChatItemType.exchange,
          request_id: "request-2",
          request_message: "Another question",
          response_text: "Another answer",
          status: ExchangeStatus.success,
          seen_state: SeenState.seen,
          timestamp: new Date().toISOString(),
        },
      ] as unknown as ChatItem[];

      // Create a mock with the tool history and proper filter implementation
      const mockConversationWithToolHistory = {
        ...conversationModel,
        chatHistory: {
          filter: vi.fn().mockReturnValue(chatHistoryWithTools),
        },
      };

      const mockChatModelWithToolHistory = {
        ...chatModel,
        currentConversationModel: mockConversationWithToolHistory,
      };

      const context = getMessageListContext(
        mockChatModelWithToolHistory as unknown as ChatModel,
        AgentExchangeStatus.notRunning,
        false,
        remoteAgentsModel,
      );

      // Verify that the chat history is correct
      expect(context.chatHistory).toEqual(chatHistoryWithTools);

      // Verify that groupedChatHistory has the correct structure
      expect(context.groupedChatHistory.length).toBe(3);

      // Check that the tool result is in the correct group
      // Find the group with the tool result
      const toolResultGroup = context.groupedChatHistory.find((group) =>
        group.some((item) => {
          // Check if this is the tool result message
          if (item.turn.request_id !== "request-1") return false;

          // Need to use type assertion since TypeScript doesn't know about structured_request_nodes
          const turn = item.turn as any;
          return turn.structured_request_nodes && turn.structured_request_nodes[0].type === 1;
        }),
      );

      // Verify that we found the tool result group
      expect(toolResultGroup).toBeDefined();

      // Verify that the last group has the correct request ID
      const lastGroup = context.groupedChatHistory[context.groupedChatHistory.length - 1];
      expect(lastGroup[0].turn.request_id).toBe("request-2");
    });
  });

  describe("sendMessage error handling", () => {
    it("should show sendMessage error config when there is a chat request failed error", () => {
      const mockRetryFn = vi.fn();
      const activeRemoteAgentsModel = {
        ...remoteAgentsModel,
        isActive: true,
        currentAgentId: "agent-1",
        sendMessageError: {
          type: SendMessageErrorType.chatRequestFailed,
          errorMessage: "Network error occurred",
          canRetry: true,
          failedExchangeId: "pending-123",
        },
        retryFailedMessage: mockRetryFn,
      } as unknown as RemoteAgentsModel;

      const context = getMessageListContext(
        chatModel,
        AgentExchangeStatus.notRunning,
        false,
        activeRemoteAgentsModel,
      );

      expect(context.lastGroupConfig.remoteAgentErrorConfig).toBeDefined();
      expect((context.lastGroupConfig.remoteAgentErrorConfig?.error as any).type).toBe(
        SendMessageErrorType.chatRequestFailed,
      );
      expect(context.lastGroupConfig.remoteAgentErrorConfig?.error.errorMessage).toBe(
        "Network error occurred",
      );
      expect(context.lastGroupConfig.remoteAgentErrorConfig?.onRetry).toBeDefined();
      expect(context.lastGroupConfig.remoteAgentErrorConfig?.onDelete).toBeUndefined();

      // Test retry functionality
      context.lastGroupConfig.remoteAgentErrorConfig?.onRetry?.();
      expect(mockRetryFn).toHaveBeenCalledWith("agent-1", "pending-123");
    });

    it("should show sendMessage error config when there is a message timeout error", () => {
      const mockRetryFn = vi.fn();
      const activeRemoteAgentsModel = {
        ...remoteAgentsModel,
        isActive: true,
        currentAgentId: "agent-1",
        sendMessageError: {
          type: SendMessageErrorType.messageTimeout,
          errorMessage: "Message timed out waiting for response",
          canRetry: true,
          failedExchangeId: "pending-456",
        },
        retryFailedMessage: mockRetryFn,
      } as unknown as RemoteAgentsModel;

      const context = getMessageListContext(
        chatModel,
        AgentExchangeStatus.notRunning,
        false,
        activeRemoteAgentsModel,
      );

      expect(context.lastGroupConfig.remoteAgentErrorConfig).toBeDefined();
      expect((context.lastGroupConfig.remoteAgentErrorConfig?.error as any).type).toBe(
        SendMessageErrorType.messageTimeout,
      );
      expect(context.lastGroupConfig.remoteAgentErrorConfig?.onRetry).toBeDefined();
      expect(context.lastGroupConfig.remoteAgentErrorConfig?.onDelete).toBeUndefined();
    });

    it("should show sendMessage error config with delete action when agent is failed", () => {
      const mockDeleteFn = vi.fn();
      const activeRemoteAgentsModel = {
        ...remoteAgentsModel,
        isActive: true,
        currentAgentId: "agent-1",
        sendMessageError: {
          type: SendMessageErrorType.agentFailed,
          errorMessage: "Agent is in failed state and cannot accept messages",
          canRetry: false,
        },
        deleteAgent: mockDeleteFn,
      } as unknown as RemoteAgentsModel;

      const context = getMessageListContext(
        chatModel,
        AgentExchangeStatus.notRunning,
        false,
        activeRemoteAgentsModel,
      );

      expect(context.lastGroupConfig.remoteAgentErrorConfig).toBeDefined();
      expect((context.lastGroupConfig.remoteAgentErrorConfig?.error as any).type).toBe(
        SendMessageErrorType.agentFailed,
      );
      expect(context.lastGroupConfig.remoteAgentErrorConfig?.onRetry).toBeUndefined();
      expect(context.lastGroupConfig.remoteAgentErrorConfig?.onDelete).toBeDefined();

      // Test delete functionality
      context.lastGroupConfig.remoteAgentErrorConfig?.onDelete?.();
      expect(mockDeleteFn).toHaveBeenCalledWith("agent-1");
    });

    it("should not show sendMessage error config when remote agent is not active", () => {
      const inactiveRemoteAgentsModel = {
        ...remoteAgentsModel,
        isActive: false,
        sendMessageError: {
          type: SendMessageErrorType.chatRequestFailed,
          errorMessage: "Network error occurred",
          canRetry: true,
        },
      } as unknown as RemoteAgentsModel;

      const context = getMessageListContext(
        chatModel,
        AgentExchangeStatus.notRunning,
        false,
        inactiveRemoteAgentsModel,
      );

      expect(context.lastGroupConfig.remoteAgentErrorConfig).toBeUndefined();
    });

    it("should not show sendMessage error config when there is no current agent", () => {
      const activeRemoteAgentsModel = {
        ...remoteAgentsModel,
        isActive: true,
        currentAgentId: undefined,
        sendMessageError: {
          type: SendMessageErrorType.chatRequestFailed,
          errorMessage: "Network error occurred",
          canRetry: true,
        },
      } as unknown as RemoteAgentsModel;

      const context = getMessageListContext(
        chatModel,
        AgentExchangeStatus.notRunning,
        false,
        activeRemoteAgentsModel,
      );

      expect(context.lastGroupConfig.remoteAgentErrorConfig).toBeUndefined();
    });

    it("should not show sendMessage error config when there is no sendMessage error", () => {
      const activeRemoteAgentsModel = {
        ...remoteAgentsModel,
        isActive: true,
        currentAgentId: "agent-1",
        sendMessageError: undefined,
      } as unknown as RemoteAgentsModel;

      const context = getMessageListContext(
        chatModel,
        AgentExchangeStatus.notRunning,
        false,
        activeRemoteAgentsModel,
      );

      expect(context.lastGroupConfig.remoteAgentErrorConfig).toBeUndefined();
    });

    it("should not show retry action when error is not retryable", () => {
      const activeRemoteAgentsModel = {
        ...remoteAgentsModel,
        isActive: true,
        currentAgentId: "agent-1",
        sendMessageError: {
          type: SendMessageErrorType.chatRequestFailed,
          errorMessage: "Non-retryable error",
          canRetry: false,
        },
      } as unknown as RemoteAgentsModel;

      const context = getMessageListContext(
        chatModel,
        AgentExchangeStatus.notRunning,
        false,
        activeRemoteAgentsModel,
      );

      expect(context.lastGroupConfig.remoteAgentErrorConfig).toBeDefined();
      expect(context.lastGroupConfig.remoteAgentErrorConfig?.onRetry).toBeUndefined();
    });

    it("should not show retry action when there is no failed exchange ID", () => {
      const activeRemoteAgentsModel = {
        ...remoteAgentsModel,
        isActive: true,
        currentAgentId: "agent-1",
        sendMessageError: {
          type: SendMessageErrorType.chatRequestFailed,
          errorMessage: "Error without exchange ID",
          canRetry: true,
          // No failedExchangeId
        },
      } as unknown as RemoteAgentsModel;

      const context = getMessageListContext(
        chatModel,
        AgentExchangeStatus.notRunning,
        false,
        activeRemoteAgentsModel,
      );

      expect(context.lastGroupConfig.remoteAgentErrorConfig).toBeDefined();
      expect(context.lastGroupConfig.remoteAgentErrorConfig?.onRetry).toBeUndefined();
    });
  });
});
