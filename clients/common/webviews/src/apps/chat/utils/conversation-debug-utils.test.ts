import { describe, it, expect, vi, beforeEach } from "vitest";
import {
  exportConversationToJson,
  importConversationFromJsonString,
  validateConversationExportData,
  type ConversationExportData,
} from "./conversation-debug-utils";
import type { ConversationModel } from "../models/conversation-model";
import type { ChatModel } from "../models/chat-model";
import type { IConversation } from "../models/types";

// Mock conversation data
const mockConversation: IConversation = {
  id: "test-conversation-id",
  name: "Test Conversation",
  createdAtIso: "2023-01-01T00:00:00.000Z",
  lastInteractedAtIso: "2023-01-01T12:00:00.000Z",
  chatHistory: [],
  feedbackStates: {},
  requestIds: [],
  isPinned: false,
  isShareable: true,
};

describe("conversation-debug-utils", () => {
  let mockConversationModel: ConversationModel;
  let mockChatModel: ChatModel;

  beforeEach(() => {
    // Mock ConversationModel with proper getters
    mockConversationModel = {
      id: mockConversation.id,
      name: mockConversation.name,
      createdAtIso: mockConversation.createdAtIso,
      lastInteractedAtIso: mockConversation.lastInteractedAtIso,
      chatHistory: mockConversation.chatHistory,
      feedbackStates: mockConversation.feedbackStates,
      toolUseStates: {},
      draftExchange: undefined,
      draftActiveContextIds: [],
      requestIds: mockConversation.requestIds,
      isPinned: mockConversation.isPinned,
      extraData: undefined,
      personaType: undefined,
      rootTaskUuid: undefined,
      selectedModelId: undefined,
    } as unknown as ConversationModel;

    // Mock ChatModel
    mockChatModel = {
      saveConversation: vi.fn(),
      saveImmediate: vi.fn(),
      state: {
        conversations: {},
      },
    } as unknown as ChatModel;
  });

  describe("exportConversationToJson", () => {
    it("should export conversation to JSON string", async () => {
      const result = await exportConversationToJson(mockConversationModel);

      expect(result).toBeDefined();
      const parsed = JSON.parse(result);
      expect(parsed.version).toBe("1.0.0");
      expect(parsed.conversation.id).toBe("test-conversation-id");
      expect(parsed.conversation.name).toBe("Test Conversation");
    });

    it("should include metadata when requested", async () => {
      const result = await exportConversationToJson(mockConversationModel, {
        includeMetadata: true,
        notes: "Test export",
      });

      const parsed = JSON.parse(result);
      expect(parsed.metadata).toBeDefined();
      expect(parsed.metadata.exportedBy).toBe("Augment Debug Utils");
      expect(parsed.metadata.notes).toBe("Test export");
    });

    it("should pretty print when requested", async () => {
      const result = await exportConversationToJson(mockConversationModel, {
        prettyPrint: true,
      });

      // Pretty printed JSON should contain newlines and indentation
      expect(result).toContain("\n");
      expect(result).toContain("  ");
    });
  });

  describe("validateConversationExportData", () => {
    it("should validate correct export data", () => {
      const validData: ConversationExportData = {
        version: "1.0.0",
        exportedAt: "2023-01-01T00:00:00.000Z",
        conversation: mockConversation,
      };

      expect(validateConversationExportData(validData)).toBe(true);
    });

    it("should reject invalid data", () => {
      expect(validateConversationExportData(null)).toBe(false);
      expect(validateConversationExportData({})).toBe(false);
      expect(validateConversationExportData({ version: "1.0.0" })).toBe(false);
    });

    it("should reject data with invalid conversation", () => {
      const invalidData = {
        version: "1.0.0",
        exportedAt: "2023-01-01T00:00:00.000Z",
        conversation: {
          // Missing required fields
          name: "Test",
        },
      };

      expect(validateConversationExportData(invalidData)).toBe(false);
    });
  });

  describe("importConversationFromJsonString", () => {
    it("should import conversation from valid JSON string", async () => {
      const exportData: ConversationExportData = {
        version: "1.0.0",
        exportedAt: "2023-01-01T00:00:00.000Z",
        conversation: mockConversation,
      };

      const jsonString = JSON.stringify(exportData);

      const result = await importConversationFromJsonString(jsonString, mockChatModel);

      expect(result).toBeDefined();
      expect(mockChatModel.saveConversation).toHaveBeenCalled();
    });

    it("should generate new ID when requested", async () => {
      const exportData: ConversationExportData = {
        version: "1.0.0",
        exportedAt: "2023-01-01T00:00:00.000Z",
        conversation: mockConversation,
      };

      const jsonString = JSON.stringify(exportData);

      await importConversationFromJsonString(jsonString, mockChatModel, {
        generateNewId: true,
      });

      const saveCall = (mockChatModel.saveConversation as any).mock.calls[0];
      const savedConversation = saveCall[0];

      // Should have a different ID than the original
      expect(savedConversation.id).not.toBe(mockConversation.id);
    });

    it("should add name prefix when requested", async () => {
      const exportData: ConversationExportData = {
        version: "1.0.0",
        exportedAt: "2023-01-01T00:00:00.000Z",
        conversation: mockConversation,
      };

      const jsonString = JSON.stringify(exportData);

      await importConversationFromJsonString(jsonString, mockChatModel, {
        namePrefix: "[Imported] ",
      });

      const saveCall = (mockChatModel.saveConversation as any).mock.calls[0];
      const savedConversation = saveCall[0];

      expect(savedConversation.name).toBe("[Imported] Test Conversation");
    });

    it("should mark as imported when requested", async () => {
      const exportData: ConversationExportData = {
        version: "1.0.0",
        exportedAt: "2023-01-01T00:00:00.000Z",
        conversation: mockConversation,
      };

      const jsonString = JSON.stringify(exportData);

      await importConversationFromJsonString(jsonString, mockChatModel, {
        markAsImported: true,
      });

      const saveCall = (mockChatModel.saveConversation as any).mock.calls[0];
      const savedConversation = saveCall[0];

      expect(savedConversation.extraData?.isImported).toBe(true);
      expect(savedConversation.extraData?.importedAt).toBeDefined();
      expect(savedConversation.extraData?.originalExportVersion).toBe("1.0.0");
    });

    it("should throw error for invalid JSON", async () => {
      await expect(
        importConversationFromJsonString("invalid json", mockChatModel),
      ).rejects.toThrow();
    });

    it("should throw error for invalid export data", async () => {
      const invalidData = { invalid: "data" };
      const jsonString = JSON.stringify(invalidData);

      await expect(importConversationFromJsonString(jsonString, mockChatModel)).rejects.toThrow(
        "Invalid conversation export data format",
      );
    });
  });
});
