import { PersonaType } from "@augment-internal/sidecar-libs/src/chat/chat-types";
import type { ComponentType } from "svelte";
import AgentAuggie from "$common-webviews/src/design-system/icons/augment/auggie/agentAuggie.svelte";
import BrainstormAuggie from "$common-webviews/src/design-system/icons/augment/auggie/brainstormAuggie.svelte";
import PrototyperAuggie from "$common-webviews/src/design-system/icons/augment/auggie/prototyperAuggie.svelte";
import ReviewerAuggie from "$common-webviews/src/design-system/icons/augment/auggie/reviewerAuggie.svelte";

/**
 * Maps a PersonaType to its corresponding icon component
 * @param personaType The personality type
 * @returns The Svelte component for the icon
 */
export function getPersonalityIconComponent(personaType: PersonaType): ComponentType {
  switch (personaType) {
    case PersonaType.DEFAULT:
      // SVELTE5_MIGRATION - AU-11862
      // @ts-expect-error - Svelte 5 component type compatibility issue
      return AgentAuggie;
    case PersonaType.PROTOTYPER:
      // SVELTE5_MIGRATION - AU-11862
      // @ts-expect-error - Svelte 5 component type compatibility issue
      return PrototyperAuggie;
    case PersonaType.BRAINSTORM:
      // SVELTE5_MIGRATION - AU-11862
      // @ts-expect-error - Svelte 5 component type compatibility issue
      return BrainstormAuggie;
    case PersonaType.REVIEWER:
      // SVELTE5_MIGRATION - AU-11862
      // @ts-expect-error - Svelte 5 component type compatibility issue
      return ReviewerAuggie;
    default:
      // Default to AgentAuggie for unknown types
      // SVELTE5_MIGRATION - AU-11862
      // @ts-expect-error - Svelte 5 component type compatibility issue
      return AgentAuggie;
  }
}
