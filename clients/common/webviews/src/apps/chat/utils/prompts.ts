export const getInputWithEnchancePrompt = (input: string) =>
  "Here is an instruction that I'd like to give you, but it needs to be improved. " +
  "Rewrite and enhance this instruction to make it clearer, more specific, " +
  "less ambiguous, and correct any mistakes. " +
  "Do not use any tools: reply immediately with your answer, even if you're not sure. " +
  "Consider the context of our conversation history when enhancing the prompt. " +
  "If there is code in triple backticks (```) consider whether it is a code sample and should remain unchanged." +
  "Reply with the following format:\n\n" +
  "### BEGIN RESPONSE ###\n" +
  "Here is an enhanced version of the original instruction that is more specific and clear:\n" +
  "<augment-enhanced-prompt>enhanced prompt goes here</augment-enhanced-prompt>\n\n" +
  "### END RESPONSE ###\n\n" +
  "Here is my original instruction:\n\n" +
  input;
