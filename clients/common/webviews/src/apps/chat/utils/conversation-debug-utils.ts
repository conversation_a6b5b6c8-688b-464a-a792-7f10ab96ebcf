import type { ConversationModel } from "../models/conversation-model";
import type { ChatModel } from "../models/chat-model";
import type { IConversation, ISerializedConversation } from "../models/types";

/**
 * Conversation Debug Utilities
 *
 * This module provides utilities for importing and exporting conversations to/from JSON
 * for debugging purposes. It follows the same patterns used for task list import/export.
 *
 * Usage Examples:
 *
 * // Export current conversation to JSON string
 * const jsonString = await exportConversationToJson(conversationModel, {
 *   includeMetadata: true,
 *   notes: "Debug export for issue #123",
 *   prettyPrint: true
 * });
 *
 * // Download conversation as JSON file
 * await downloadConversationAsJson(conversationModel, {
 *   filename: "debug-conversation",
 *   includeMetadata: true,
 *   prettyPrint: true
 * });
 *
 * // Import conversation from file picker
 * const importedId = await importConversationFromFile(chatModel, {
 *   generateNewId: true,
 *   namePrefix: "[Debug] ",
 *   markAsImported: true
 * });
 *
 * // Import conversation from JSON string
 * const importedId2 = await importConversationFromJsonString(jsonString, chatModel);
 *
 * // Import multiple copies with indexed names from file picker (returns first ID)
 * const firstImportedId = await importConversationFromFile(chatModel, {
 *   repetitions: 5,
 *   namePrefix: "[Test] ",
 *   generateNewId: true,
 *   markAsImported: true
 * });
 * // Creates: "[Test] 1 - Original Name", "[Test] 2 - Original Name", etc.
 * // Returns the ID of the first conversation ("[Test] 1 - Original Name")
 *
 * // Import multiple copies from JSON string (returns first ID)
 * const firstImportedId2 = await importConversationFromJsonString(jsonString, chatModel, {
 *   repetitions: 3,
 *   namePrefix: "[Batch] "
 * });
 * // Creates: "[Batch] 1 - Original Name", "[Batch] 2 - Original Name", "[Batch] 3 - Original Name"
 */

/**
 * Helper function to extract conversation data from ConversationModel
 * Since ConversationModel doesn't expose the full state directly, we reconstruct it
 * from the individual getters.
 */
function extractConversationFromModel(conversationModel: ConversationModel): IConversation {
  return {
    id: conversationModel.id,
    name: conversationModel.name,
    createdAtIso: conversationModel.createdAtIso,
    lastInteractedAtIso: conversationModel.lastInteractedAtIso,
    chatHistory: conversationModel.chatHistory,
    feedbackStates: conversationModel.feedbackStates,
    toolUseStates: conversationModel.toolUseStates,
    draftExchange: conversationModel.draftExchange,
    draftActiveContextIds: conversationModel.draftActiveContextIds,
    requestIds: conversationModel.requestIds,
    isPinned: conversationModel.isPinned,
    // These properties don't have getters, so we'll set them to undefined
    // The actual values will be preserved when importing/exporting through the chat model
    lastUrl: undefined,
    isShareable: undefined,
    extraData: conversationModel.extraData,
    personaType: conversationModel.personaType,
    rootTaskUuid: conversationModel.rootTaskUuid,
    selectedModelId: conversationModel.selectedModelId,
  };
}

/**
 * Interface for conversation export data
 */
export interface ConversationExportData {
  /** Version of the export format for future compatibility */
  version: string;
  /** Timestamp when the export was created */
  exportedAt: string;
  /** The conversation data */
  conversation: ISerializedConversation;
  /** Additional metadata */
  metadata?: {
    /** Name of the client that exported this conversation */
    exportedBy?: string;
    /** Any additional notes or context */
    notes?: string;
  };
}

/**
 * Exports a conversation to JSON format for debugging purposes.
 * This creates a complete snapshot of the conversation state that can be
 * saved to a file or shared for debugging.
 *
 * @param conversationModel - The conversation model to export
 * @param options - Optional export configuration
 * @returns Promise that resolves to the JSON string representation
 */
export async function exportConversationToJson(
  conversationModel: ConversationModel,
  options?: {
    /** Include additional metadata in the export */
    includeMetadata?: boolean;
    /** Custom notes to include in the export */
    notes?: string;
    /** Pretty print the JSON output */
    prettyPrint?: boolean;
  },
): Promise<string> {
  const conversation = extractConversationFromModel(conversationModel);

  // Create a serialized version of the conversation
  const serializedConversation: ISerializedConversation = {
    id: conversation.id,
    name: conversation.name,
    createdAtIso: conversation.createdAtIso,
    lastInteractedAtIso: conversation.lastInteractedAtIso,
    chatHistory: conversation.chatHistory,
    feedbackStates: conversation.feedbackStates,
    toolUseStates: conversation.toolUseStates,
    draftExchange: conversation.draftExchange,
    draftActiveContextIds: conversation.draftActiveContextIds,
    requestIds: conversation.requestIds,
    isPinned: conversation.isPinned,
    lastUrl: conversation.lastUrl,
    isShareable: conversation.isShareable,
    extraData: conversation.extraData,
    personaType: conversation.personaType,
    rootTaskUuid: conversation.rootTaskUuid,
  };

  const exportData: ConversationExportData = {
    version: "1.0.0",
    exportedAt: new Date().toISOString(),
    conversation: serializedConversation,
  };

  if (options?.includeMetadata) {
    exportData.metadata = {
      exportedBy: "Augment Debug Utils",
      notes: options.notes,
    };
  }

  const indent = options?.prettyPrint ? 2 : undefined;
  return JSON.stringify(exportData, null, indent);
}

/**
 * Exports a conversation to a JSON string directly from IConversation data.
 *
 * @param conversation - The conversation data to export
 * @param options - Export options
 * @returns A JSON string representation of the conversation
 */
export async function exportConversationFromDataToJson(
  conversation: IConversation,
  options?: {
    /** Include additional metadata in the export */
    includeMetadata?: boolean;
    /** Custom notes to include in the export */
    notes?: string;
    /** Pretty print the JSON output */
    prettyPrint?: boolean;
  },
): Promise<string> {
  const serializedConversation: ISerializedConversation = {
    id: conversation.id,
    name: conversation.name,
    createdAtIso: conversation.createdAtIso,
    lastInteractedAtIso: conversation.lastInteractedAtIso,
    chatHistory: conversation.chatHistory,
    feedbackStates: conversation.feedbackStates,
    toolUseStates: conversation.toolUseStates,
    draftExchange: conversation.draftExchange,
    draftActiveContextIds: conversation.draftActiveContextIds,
    requestIds: conversation.requestIds,
    isPinned: conversation.isPinned,
    lastUrl: conversation.lastUrl,
    isShareable: conversation.isShareable,
    extraData: conversation.extraData,
    personaType: conversation.personaType,
    rootTaskUuid: conversation.rootTaskUuid,
  };

  const exportData: ConversationExportData = {
    version: "1.0.0",
    exportedAt: new Date().toISOString(),
    conversation: serializedConversation,
  };

  if (options?.includeMetadata) {
    exportData.metadata = {
      exportedBy: "Augment Debug Utils",
      notes: options.notes,
    };
  }

  const indent = options?.prettyPrint ? 2 : undefined;
  return JSON.stringify(exportData, null, indent);
}

/**
 * Downloads a conversation as a JSON file.
 * This is a convenience function that exports the conversation and triggers
 * a browser download.
 *
 * @param conversationModel - The conversation model to export
 * @param options - Optional export and download configuration
 */
export async function downloadConversationAsJson(
  conversationModel: ConversationModel,
  options?: {
    /** Custom filename (without extension) */
    filename?: string;
    /** Include additional metadata in the export */
    includeMetadata?: boolean;
    /** Custom notes to include in the export */
    notes?: string;
    /** Pretty print the JSON output */
    prettyPrint?: boolean;
  },
): Promise<void> {
  try {
    const jsonString = await exportConversationToJson(conversationModel, {
      includeMetadata: options?.includeMetadata ?? true,
      notes: options?.notes,
      prettyPrint: options?.prettyPrint ?? true,
    });

    // Create a blob with the JSON data
    const blob = new Blob([jsonString], { type: "application/json" });

    // Create a download link
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;

    // Generate filename
    const conversation = extractConversationFromModel(conversationModel);
    const baseName = options?.filename || conversation.name || "conversation";
    const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, "-");
    link.download = `${baseName}_${timestamp}.json`;

    // Trigger download
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // Clean up the URL
    URL.revokeObjectURL(url);
  } catch (error) {
    console.error("Error downloading conversation as JSON:", error);
    throw error;
  }
}

/**
 * Downloads a conversation as a JSON file directly from IConversation data.
 * This is a convenience function that doesn't require a ConversationModel.
 *
 * @param conversation - The conversation data to export
 * @param options - Export options
 */
export async function downloadConversationFromDataAsJson(
  conversation: IConversation,
  options?: {
    /** Custom filename (without extension) */
    filename?: string;
    /** Include additional metadata in the export */
    includeMetadata?: boolean;
    /** Custom notes to include in the export */
    notes?: string;
    /** Pretty print the JSON output */
    prettyPrint?: boolean;
    /** Extension client for creating files (preferred method) */
    extensionClient?: { createFile: (content: string, fileName: string) => void };
  },
): Promise<void> {
  try {
    const jsonString = await exportConversationFromDataToJson(conversation, {
      includeMetadata: options?.includeMetadata ?? true,
      notes: options?.notes,
      prettyPrint: options?.prettyPrint ?? true,
    });

    // Generate filename
    const baseName = options?.filename || conversation.name || "conversation";
    const sanitizedBaseName = baseName.replace(/[/:*?"<>|\s]/g, "_");
    const timestamp = new Date().toISOString().replace(/[:.]/g, "-").slice(0, 19);
    const fileName = `${sanitizedBaseName}_${timestamp}.json`;

    // Use extension client if available (preferred method), otherwise fall back to browser download
    if (options?.extensionClient) {
      options.extensionClient.createFile(jsonString, fileName);
    } else {
      // Fallback to browser download API
      const blob = new Blob([jsonString], { type: "application/json" });
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = fileName;

      // Trigger download
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Clean up the URL
      URL.revokeObjectURL(url);
    }
  } catch (error) {
    console.error("Error downloading conversation as JSON:", error);
    throw error;
  }
}

/**
 * Validates that an imported conversation export has the expected structure.
 *
 * @param data - The parsed JSON data to validate
 * @returns True if the data is valid, false otherwise
 */
export function validateConversationExportData(data: unknown): data is ConversationExportData {
  if (!data || typeof data !== "object") {
    return false;
  }

  const exportData = data as Record<string, unknown>;

  // Check required fields
  if (typeof exportData.version !== "string") {
    return false;
  }

  if (typeof exportData.exportedAt !== "string") {
    return false;
  }

  if (!exportData.conversation || typeof exportData.conversation !== "object") {
    return false;
  }

  const conversation = exportData.conversation as Record<string, unknown>;

  // Check required conversation fields
  if (typeof conversation.id !== "string") {
    return false;
  }

  if (typeof conversation.createdAtIso !== "string") {
    return false;
  }

  if (typeof conversation.lastInteractedAtIso !== "string") {
    return false;
  }

  if (!Array.isArray(conversation.chatHistory)) {
    return false;
  }

  if (!conversation.feedbackStates || typeof conversation.feedbackStates !== "object") {
    return false;
  }

  if (!Array.isArray(conversation.requestIds)) {
    return false;
  }

  return true;
}

/**
 * Imports a conversation from JSON export data.
 * This creates a new conversation in the chat model with the imported data.
 *
 * @param exportData - The validated conversation export data
 * @param chatModel - The chat model for managing conversations
 * @param options - Optional import configuration
 * @returns Promise that resolves to the ID of the imported conversation
 */
export async function importConversationFromExportData(
  exportData: ConversationExportData,
  chatModel: ChatModel,
  options?: {
    /** Generate a new ID for the imported conversation */
    generateNewId?: boolean;
    /** Prefix to add to the conversation name */
    namePrefix?: string;
    /** Whether to mark the conversation as imported in extraData */
    markAsImported?: boolean;
  },
): Promise<string> {
  const { conversation } = exportData;

  // Create a new conversation object
  const importedConversation: IConversation = {
    ...conversation,
    // Always generate new ID for imports (as requested)
    id: options?.generateNewId !== false ? crypto.randomUUID() : conversation.id,
    // Add prefix to name if provided
    name: options?.namePrefix
      ? `${options.namePrefix}${conversation.name || "Imported Conversation"}`
      : conversation.name,
    // Update timestamps to current time
    lastInteractedAtIso: new Date().toISOString(),
  };

  // Mark as imported if requested
  if (options?.markAsImported) {
    importedConversation.extraData = {
      ...importedConversation.extraData,
      isImported: true,
      importedAt: new Date().toISOString(),
      originalExportVersion: exportData.version,
    };
  }

  // Add the conversation to the chat model's state
  try {
    // Use the chat model's saveConversation method to properly add the conversation
    chatModel.saveConversation(importedConversation);
    chatModel.saveImmediate();

    return importedConversation.id;
  } catch (error) {
    console.error("Error creating imported conversation:", error);
    throw new Error(`Failed to import conversation: ${error}`);
  }
}

/**
 * Imports a conversation from a JSON file.
 * This opens a file picker dialog and imports the selected conversation file.
 *
 * @param chatModel - The chat model for managing conversations
 * @param options - Optional import configuration
 * @returns Promise that resolves to the ID of the imported conversation (first one if multiple), or null if cancelled
 */
export async function importConversationFromFile(
  chatModel: ChatModel,
  options?: {
    /** Generate a new ID for the imported conversation */
    generateNewId?: boolean;
    /** Prefix to add to the conversation name */
    namePrefix?: string;
    /** Whether to mark the conversation as imported in extraData */
    markAsImported?: boolean;
    /** Number of repetitions to create (defaults to 1) */
    repetitions?: number;
  },
): Promise<string | null> {
  try {
    // Create a hidden file input element
    const input = document.createElement("input");
    input.type = "file";
    input.accept = ".json,application/json";
    input.style.display = "none";

    // Create a promise that resolves when the file is selected
    const filePromise = new Promise<File | null>((resolve) => {
      input.onchange = () => {
        resolve(input.files ? input.files[0] : null);
      };

      // Handle the case where user cancels the dialog
      input.oncancel = () => {
        resolve(null);
      };
    });

    // Add to DOM and trigger the file picker
    document.body.appendChild(input);
    input.click();

    // Wait for the file to be selected
    const file = await filePromise;

    // Clean up the input element
    document.body.removeChild(input);

    if (!file) {
      return null; // User cancelled
    }

    // Read and parse the file content
    const fileContent = await file.text();
    const parsedData = JSON.parse(fileContent);

    // Validate the data
    if (!validateConversationExportData(parsedData)) {
      throw new Error("Invalid conversation export file format");
    }

    // Import the conversation(s)
    const repetitions = options?.repetitions ?? 1;

    if (repetitions === 1) {
      // Single import - maintain backward compatibility
      return await importConversationFromExportData(parsedData, chatModel, {
        generateNewId: options?.generateNewId ?? true, // Default to generating new ID
        namePrefix: options?.namePrefix ?? "[Imported] ",
        markAsImported: options?.markAsImported ?? true,
      });
    } else {
      // Multiple imports with indexed names - return the first imported ID
      let firstImportedId: string | null = null;
      for (let i = 1; i <= repetitions; i++) {
        const indexedNamePrefix = options?.namePrefix
          ? `${options.namePrefix}${i} - `
          : `[Imported ${i}] `;

        const conversationId = await importConversationFromExportData(parsedData, chatModel, {
          generateNewId: options?.generateNewId ?? true,
          namePrefix: indexedNamePrefix,
          markAsImported: options?.markAsImported ?? true,
        });

        if (i === 1) {
          firstImportedId = conversationId;
        }
      }
      return firstImportedId;
    }
  } catch (error) {
    console.error("Error importing conversation from file:", error);
    throw error;
  }
}

/**
 * Imports a conversation from a JSON string.
 * This is useful for programmatic imports or when you already have the JSON data.
 *
 * @param jsonString - The JSON string containing the conversation export data
 * @param chatModel - The chat model for managing conversations
 * @param options - Optional import configuration
 * @returns Promise that resolves to the ID of the imported conversation (first one if multiple)
 */
export async function importConversationFromJsonString(
  jsonString: string,
  chatModel: ChatModel,
  options?: {
    /** Generate a new ID for the imported conversation */
    generateNewId?: boolean;
    /** Prefix to add to the conversation name */
    namePrefix?: string;
    /** Whether to mark the conversation as imported in extraData */
    markAsImported?: boolean;
    /** Number of repetitions to create (defaults to 1) */
    repetitions?: number;
  },
): Promise<string> {
  try {
    // Parse the JSON string
    const parsedData = JSON.parse(jsonString);

    // Validate the data
    if (!validateConversationExportData(parsedData)) {
      throw new Error("Invalid conversation export data format");
    }

    // Import the conversation(s)
    const repetitions = options?.repetitions ?? 1;

    if (repetitions === 1) {
      // Single import - maintain backward compatibility
      return await importConversationFromExportData(parsedData, chatModel, options);
    } else {
      // Multiple imports with indexed names - return the first imported ID
      let firstImportedId: string | null = null;
      for (let i = 1; i <= repetitions; i++) {
        const indexedNamePrefix = options?.namePrefix
          ? `${options.namePrefix}${i} - `
          : `[Imported ${i}] `;

        const conversationId = await importConversationFromExportData(parsedData, chatModel, {
          generateNewId: options?.generateNewId ?? true,
          namePrefix: indexedNamePrefix,
          markAsImported: options?.markAsImported ?? true,
        });

        if (i === 1) {
          firstImportedId = conversationId;
        }
      }
      return firstImportedId!; // We know this will be set since repetitions > 1
    }
  } catch (error) {
    console.error("Error importing conversation from JSON string:", error);
    throw error;
  }
}
