import { describe, it, expect, beforeEach, vi } from "vitest";
import {
  shouldHideVscodeVersionWarningForSession,
  setHideVscodeVersionWarningForSession,
  clearVscodeVersionWarningPreference,
} from "./vscode-version-warning-preferences";

// Mock sessionStorage
const sessionStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};

Object.defineProperty(window, "sessionStorage", {
  value: sessionStorageMock,
});

describe("vscode-version-warning-preferences", () => {
  beforeEach(() => {
    vi.clearAllMocks();
    vi.useRealTimers();
  });

  describe("shouldHideVscodeVersionWarningForSession", () => {
    it("returns false when no preference is stored", () => {
      sessionStorageMock.getItem.mockReturnValue(null);

      const result = shouldHideVscodeVersionWarningForSession();

      expect(result).toBe(false);
      expect(sessionStorageMock.getItem).toHaveBeenCalledWith(
        "vscode-version-warning-hide-for-session",
      );
    });

    it("returns true when hideForSession is true", () => {
      const preference = {
        hideForSession: true,
        timestamp: "2023-01-01T12:00:00.000Z",
      };
      sessionStorageMock.getItem.mockReturnValue(JSON.stringify(preference));

      const result = shouldHideVscodeVersionWarningForSession();

      expect(result).toBe(true);
    });

    it("returns false when hideForSession is false", () => {
      const preference = {
        hideForSession: false,
        timestamp: "2023-01-01T12:00:00.000Z",
      };
      sessionStorageMock.getItem.mockReturnValue(JSON.stringify(preference));

      const result = shouldHideVscodeVersionWarningForSession();

      expect(result).toBe(false);
    });

    it("returns false and logs error when sessionStorage throws", () => {
      sessionStorageMock.getItem.mockImplementation(() => {
        throw new Error("sessionStorage error");
      });

      const consoleSpy = vi.spyOn(console, "error").mockImplementation(() => {});

      const result = shouldHideVscodeVersionWarningForSession();

      expect(result).toBe(false);
      expect(consoleSpy).toHaveBeenCalledWith(
        "Failed to read VSCode version warning preference from sessionStorage:",
        expect.any(Error),
      );

      consoleSpy.mockRestore();
    });

    it("returns false and logs error when JSON parsing fails", () => {
      sessionStorageMock.getItem.mockReturnValue("invalid json");

      const consoleSpy = vi.spyOn(console, "error").mockImplementation(() => {});

      const result = shouldHideVscodeVersionWarningForSession();

      expect(result).toBe(false);
      expect(consoleSpy).toHaveBeenCalledWith(
        "Failed to read VSCode version warning preference from sessionStorage:",
        expect.any(Error),
      );

      consoleSpy.mockRestore();
    });
  });

  describe("setHideVscodeVersionWarningForSession", () => {
    it("stores the preference with current timestamp", () => {
      const mockDate = new Date("2023-01-01T12:00:00.000Z");
      vi.setSystemTime(mockDate);

      setHideVscodeVersionWarningForSession();

      expect(sessionStorageMock.setItem).toHaveBeenCalledWith(
        "vscode-version-warning-hide-for-session",
        JSON.stringify({
          hideForSession: true,
          timestamp: "2023-01-01T12:00:00.000Z",
        }),
      );
    });

    it("handles sessionStorage errors gracefully", () => {
      sessionStorageMock.setItem.mockImplementation(() => {
        throw new Error("sessionStorage error");
      });

      const consoleSpy = vi.spyOn(console, "error").mockImplementation(() => {});

      expect(() => setHideVscodeVersionWarningForSession()).not.toThrow();
      expect(consoleSpy).toHaveBeenCalledWith(
        "Failed to save VSCode version warning preference to sessionStorage:",
        expect.any(Error),
      );

      consoleSpy.mockRestore();
    });
  });

  describe("clearVscodeVersionWarningPreference", () => {
    it("removes the preference from sessionStorage", () => {
      clearVscodeVersionWarningPreference();

      expect(sessionStorageMock.removeItem).toHaveBeenCalledWith(
        "vscode-version-warning-hide-for-session",
      );
    });

    it("handles sessionStorage errors gracefully", () => {
      sessionStorageMock.removeItem.mockImplementation(() => {
        throw new Error("sessionStorage error");
      });

      const consoleSpy = vi.spyOn(console, "error").mockImplementation(() => {});

      expect(() => clearVscodeVersionWarningPreference()).not.toThrow();
      expect(consoleSpy).toHaveBeenCalledWith(
        "Failed to clear VSCode version warning preference from sessionStorage:",
        expect.any(Error),
      );

      consoleSpy.mockRestore();
    });
  });
});
