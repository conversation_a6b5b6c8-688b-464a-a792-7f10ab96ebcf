import {
  type Chat<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ult,
  type Cha<PERSON><PERSON><PERSON><PERSON>N<PERSON>,
  ChatResultNodeType,
  ImageFormatType,
  type ChatR<PERSON>questN<PERSON>,
  ChatRequestNodeType,
} from "@augment-internal/sidecar-libs/src/chat/chat-types";
import {
  type ToolResponseContentNode,
  ToolResponseContentNodeType,
  type ToolUseResponse,
} from "@augment-internal/sidecar-libs/src/tools/tool-types";
import {
  type ChatRequestContentNode,
  ChatRequestContentNodeType,
} from "@augment-internal/sidecar-libs/src/chat/chat-types";
import type { ConversationModel } from "../models/conversation-model";
import { ToolUsePhase, type ToolUseState } from "../types/tool-use-state";
import { truncateMiddle } from "@augment-internal/sidecar-libs/src/utils/strings";
import { type RemoteAgentsModel } from "../../remote-agent-manager/models/remote-agents-model";

/**
 * Convert a media type string to an ImageFormatType enum value
 *
 * @param mediaType The media type string (e.g., "image/png", "image/jpeg")
 * @returns The corresponding ImageFormatType enum value
 */
export function getImageFormatFromMediaType(mediaType?: string): ImageFormatType {
  if (!mediaType) {
    return ImageFormatType.IMAGE_FORMAT_UNSPECIFIED;
  }

  const format = mediaType.split("/")[1]?.toLowerCase();
  switch (format) {
    case "jpeg":
    case "jpg":
      return ImageFormatType.JPEG;
    case "png":
      return ImageFormatType.PNG;
    default:
      return ImageFormatType.IMAGE_FORMAT_UNSPECIFIED;
  }
}

/**
 * Convert tool response content nodes to chat request content nodes
 *
 * @param contentNodes The tool response content nodes to convert
 * @param enableDebugFeatures Whether debug features are enabled (required for image support)
 * @returns The converted chat request content nodes
 */
export function convertToolResponseToRequestContentNodes(
  contentNodes: ToolResponseContentNode[],
  enableDebugFeatures: boolean,
): ChatRequestContentNode[] {
  return contentNodes.map((content) => {
    if (content.type === ToolResponseContentNodeType.ContentText) {
      return {
        /* eslint-disable @typescript-eslint/naming-convention */
        type: ChatRequestContentNodeType.CONTENT_TEXT,
        text_content: content.text_content,
        /* eslint-enable @typescript-eslint/naming-convention */
      };
    } else if (
      content.type === ToolResponseContentNodeType.ContentImage &&
      content.image_content &&
      enableDebugFeatures
    ) {
      return {
        /* eslint-disable @typescript-eslint/naming-convention */
        type: ChatRequestContentNodeType.CONTENT_IMAGE,
        image_content: {
          image_data: content.image_content.image_data,
          format: getImageFormatFromMediaType(content.image_content.media_type),
        },
        /* eslint-enable @typescript-eslint/naming-convention */
      };
    }
    return {
      /* eslint-disable @typescript-eslint/naming-convention */
      type: ChatRequestContentNodeType.CONTENT_TEXT,
      text_content: "[Error: Invalid content node]",
      /* eslint-enable @typescript-eslint/naming-convention */
    };
  });
}

export function generateToolResultNodeFromState(
  toolState: ToolUseState,
  toolUseId: string,
  enableDebugFeatures: boolean,
): ChatRequestToolResult | undefined {
  if (
    toolState.phase !== ToolUsePhase.cancelled &&
    toolState.phase !== ToolUsePhase.completed &&
    toolState.phase !== ToolUsePhase.error
  ) {
    return undefined;
  }
  let toolResultContentNodes: ChatRequestContentNode[] | undefined = undefined;
  if (toolState.result?.contentNodes) {
    // Convert the content nodes to the format expected by the chat API
    toolResultContentNodes = convertToolResponseToRequestContentNodes(
      toolState.result.contentNodes,
      enableDebugFeatures,
    );

    return {
      /* eslint-disable @typescript-eslint/naming-convention */
      content: "", // Only for backward compatibility
      is_error: toolState.result.isError,
      request_id: toolState.result.requestId,
      tool_use_id: toolUseId,
      content_nodes: toolResultContentNodes,
      /* eslint-enable @typescript-eslint/naming-convention */
    };
  } else if (toolState.result?.text !== undefined) {
    return {
      /* eslint-disable @typescript-eslint/naming-convention */
      content: toolState.result.text,
      is_error: toolState.result.isError,
      request_id: toolState.result.requestId,
      tool_use_id: toolUseId,
      /* eslint-enable @typescript-eslint/naming-convention */
    };
  } else {
    return;
  }
}

/**
 * The first tool wins.  Otherwise the first tool_use_start wins, otherwise its undefined.
 * @param nodes
 * @returns
 */

export function toolOrToolStart(nodes: ChatResultNode[] = []): ChatResultNode | undefined {
  let toolStart: ChatResultNode | undefined;
  for (const node of nodes) {
    if (node.type === ChatResultNodeType.TOOL_USE) {
      return node;
    } else if (node.type === ChatResultNodeType.TOOL_USE_START) {
      toolStart = node;
    }
  }

  return toolStart;
}

/**
 * If the are tool_use_start nodes, and tool_use nodes, remove the tool_use_start nodes.
 * Otherwise return the nodes unchanged.
 * @param nodes
 * @returns
 */
export function normalizeToolUse(nodes: ChatResultNode[] = []): ChatResultNode[] {
  const tool = toolOrToolStart(nodes);
  if (!tool) {
    return nodes;
  }
  //There might be a tool_use_start and a tool_use.  In this case, we want to remove the tool_use_start.
  if (tool.type === ChatResultNodeType.TOOL_USE) {
    return nodes.filter((node) => node.type !== ChatResultNodeType.TOOL_USE_START);
  }
  return nodes;
}

export function onlyDisplayableToolNodes(
  requestId: string | undefined,
  nodes: ChatResultNode[] | undefined,
  conversationModel: ConversationModel,
  remoteAgentsModel?: RemoteAgentsModel,
): ChatResultNode[] {
  if (!requestId || !nodes) {
    return [];
  }
  let renderingBreak = false;
  return nodes.filter((node) => {
    const toolUseState =
      remoteAgentsModel?.isActive && node.tool_use
        ? remoteAgentsModel.getToolUseState(node.tool_use.tool_use_id)
        : conversationModel.getToolUseState(
            node.requestId ?? requestId,
            node.tool_use?.tool_use_id,
          );
    if (
      renderingBreak === false &&
      toolUseState.phase !== ToolUsePhase.new &&
      toolUseState.phase !== ToolUsePhase.unknown &&
      toolUseState.phase !== ToolUsePhase.checkingSafety &&
      node.tool_use !== undefined
    ) {
      return true;
    }
    if (toolUseState.phase === ToolUsePhase.runnable) {
      renderingBreak = true;
    }
    return false;
  });
}

/**
 * Helper to create a tool result node based on tool state
 * Moved from conversation-model.ts to reduce file size and improve organization
 */
export function createMissingToolResultNode(
  toolUseId: string,
  toolState: ToolUseState,
  id: number,
  enableDebugFeatures: boolean,
): ChatRequestNode {
  const requestNode = generateToolResultNodeFromState(toolState, toolUseId, enableDebugFeatures);

  /* eslint-disable @typescript-eslint/naming-convention */
  let tool_result_node: ChatRequestToolResult;
  /* eslint-enable @typescript-eslint/naming-convention */

  if (requestNode !== undefined) {
    /* eslint-disable @typescript-eslint/naming-convention */
    tool_result_node = requestNode;
    /* eslint-enable @typescript-eslint/naming-convention */
  } else {
    // Handle all possible tool state phases for error cases
    let content: string;

    switch (toolState.phase) {
      case ToolUsePhase.runnable:
        content = "Tool was cancelled before running.";
        break;
      case ToolUsePhase.new:
        // For backward compatibility with existing tests, treat 'new' phase as cancelled by user
        content = "Cancelled by user.";
        break;
      case ToolUsePhase.checkingSafety:
        content = "Tool was cancelled during safety check.";
        break;
      case ToolUsePhase.running:
        content = "Tool was cancelled while running.";
        break;
      case ToolUsePhase.cancelling:
        content = "Tool cancellation was interrupted.";
        break;
      case ToolUsePhase.cancelled:
        content = "Cancelled by user.";
        break;
      case ToolUsePhase.error:
        content = "Tool execution failed.";
        break;
      case ToolUsePhase.completed:
        content = "Tool completed but result was unavailable.";
        break;
      case ToolUsePhase.unknown:
      default:
        // For unknown phases and default case, use the original behavior
        // This maintains backward compatibility with existing tests
        content = "Cancelled by user.";
        if (toolState.phase !== ToolUsePhase.unknown) {
          console.error(`Unexpected tool state phase: ${toolState.phase}`);
        }
        break;
    }

    tool_result_node = {
      /* eslint-disable @typescript-eslint/naming-convention */
      tool_use_id: toolUseId,
      content,
      is_error: true,
      /* eslint-enable @typescript-eslint/naming-convention */
    };
  }

  return {
    /* eslint-disable @typescript-eslint/naming-convention */
    id: id,
    type: ChatRequestNodeType.TOOL_RESULT,
    tool_result_node: tool_result_node,
    /* eslint-enable @typescript-eslint/naming-convention */
  };
}

/**
 * Process a tool result and shorten its output if it is too large.
 *
 * If the result is too large, returns a new response with the middle of
 * the output removed. The error status and other fields are preserved.
 *
 * @param response the tool use response being processed
 * @param maxResultBytes maximum tool output desired, in bytes
 */
export function clipResultIfLarge(
  response: ToolUseResponse,
  maxResultBytes: number,
): ToolUseResponse {
  // Handle structured responses
  if (response.contentNodes && response.contentNodes.length > 0) {
    // Only truncate text content items, leave images as is
    const clippedContents = response.contentNodes.map((content) => {
      if (content.type === ToolResponseContentNodeType.ContentText) {
        let truncTextContent = "";
        if (content.text_content) {
          truncTextContent = truncateMiddle(
            content.text_content,
            maxResultBytes / response.contentNodes!.length,
          );
        }

        return {
          ...content,
          /* eslint-disable @typescript-eslint/naming-convention */
          text_content: truncTextContent,
          /* eslint-enable @typescript-eslint/naming-convention */
        };
      }
      return content;
    });

    return {
      ...response,
      contentNodes: clippedContents,
    };
  }

  // Handle regular text responses
  return {
    ...response,
    text: truncateMiddle(response.text, maxResultBytes),
  };
}
