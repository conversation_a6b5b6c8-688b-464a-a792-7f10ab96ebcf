import { describe, test, expect, vi, beforeEach } from "vitest";
import {
  distanceFromBottom,
  isScrollNearBottom,
  isScrollNearTop,
  canScrollTo,
  scrollTo,
  scrollToTop,
  scrollToBottom,
  scrollToY,
} from "./scroll-utils";

// Mock the scroll library
vi.mock("scroll", () => ({
  default: {
    top: vi.fn(() => vi.fn()), // Returns a cancel function
  },
}));

describe("scroll-utils", () => {
  let mockElement: HTMLElement;
  let mockScrollContainer: HTMLElement;

  beforeEach(async () => {
    vi.clearAllMocks();

    // Create mock element
    mockElement = {
      offsetTop: 200,
      offsetHeight: 50,
    } as any;

    // Create mock scroll container
    mockScrollContainer = {
      scroll: vi.fn(),
      scrollTop: 100,
    } as any;

    // Define read-only properties
    Object.defineProperty(mockScrollContainer, "clientHeight", {
      value: 200,
      writable: true,
      configurable: true,
    });
    Object.defineProperty(mockScrollContainer, "scrollHeight", {
      value: 500,
      writable: true,
      configurable: true,
    });
  });

  describe("distanceFromBottom", () => {
    test("should calculate distance from bottom in normal mode", () => {
      const element = {
        scrollTop: 50,
        clientHeight: 100,
        scrollHeight: 200,
      } as HTMLElement;

      expect(distanceFromBottom(element, { reversed: false })).toBe(50); // 200 - 50 - 100
    });

    test("should calculate distance from bottom in reversed mode", () => {
      const element = {
        scrollTop: -20, // Negative in reversed mode
        clientHeight: 100,
        scrollHeight: 200,
      } as HTMLElement;

      expect(distanceFromBottom(element, { reversed: true })).toBe(20); // Math.abs(-20)
    });

    test("should default to reversed mode", () => {
      const element = {
        scrollTop: -30,
        clientHeight: 100,
        scrollHeight: 200,
      } as HTMLElement;

      expect(distanceFromBottom(element)).toBe(30); // Default reversed: true
    });
  });

  describe("isScrollNearBottom", () => {
    test("should return true when near bottom in reversed mode", () => {
      const element = {
        scrollTop: -10, // Close to bottom in reversed mode
        clientHeight: 100,
        scrollHeight: 270,
      } as HTMLElement;

      // Distance from bottom: Math.abs(-10) = 10, which is < default buffer (40)
      expect(isScrollNearBottom(element)).toBe(true);
    });

    test("should return false when not near bottom in reversed mode", () => {
      const element = {
        scrollTop: -60, // Far from bottom in reversed mode
        clientHeight: 100,
        scrollHeight: 200,
      } as HTMLElement;

      // Distance from bottom: Math.abs(-60) = 60, which is > default buffer (40)
      expect(isScrollNearBottom(element)).toBe(false);
    });

    test("should work in normal mode", () => {
      const element = {
        scrollTop: 150,
        clientHeight: 100,
        scrollHeight: 270,
      } as HTMLElement;

      // Distance from bottom: 270 - 150 - 100 = 20, which is < default buffer (40)
      expect(isScrollNearBottom(element, { reversed: false })).toBe(true);
    });

    test("should use custom buffer", () => {
      const element = {
        scrollTop: -50, // Reversed mode
        clientHeight: 100,
        scrollHeight: 200,
      } as HTMLElement;

      // Distance from bottom: Math.abs(-50) = 50
      expect(isScrollNearBottom(element, { buffer: 60 })).toBe(true); // Buffer 60 > 50
      expect(isScrollNearBottom(element, { buffer: 30 })).toBe(false); // Buffer 30 < 50
    });
  });

  describe("isScrollNearTop", () => {
    test("should return true when near top", () => {
      const element = { scrollTop: 20 } as HTMLElement;

      // 20 < default buffer (40)
      expect(isScrollNearTop(element)).toBe(true);
    });

    test("should return false when not near top", () => {
      const element = { scrollTop: 50 } as HTMLElement;

      // 50 > default buffer (40)
      expect(isScrollNearTop(element)).toBe(false);
    });

    test("should use custom buffer", () => {
      const element = { scrollTop: 50 } as HTMLElement;

      expect(isScrollNearTop(element, 60)).toBe(true); // Buffer 60 > 50
      expect(isScrollNearTop(element, 30)).toBe(false); // Buffer 30 < 50
    });
  });

  describe("canScrollTo", () => {
    test("should return true when scroll is allowed", () => {
      const result = canScrollTo(mockScrollContainer, mockElement);
      expect(result).toBe(true);
    });

    test("should return false when scroll up is disabled and target is above", () => {
      // Target element at offsetTop 200, container scrollTop 300 (target is above)
      mockScrollContainer.scrollTop = 300;

      const result = canScrollTo(mockScrollContainer, mockElement, { disableScrollUp: true });
      expect(result).toBe(false);
    });

    test("should return false when scroll down is disabled and target is below", () => {
      // Target element at offsetTop 200, container scrollTop 100 (target is below)
      mockScrollContainer.scrollTop = 100;

      const result = canScrollTo(mockScrollContainer, mockElement, { disableScrollDown: true });
      expect(result).toBe(false);
    });

    test("should consider topBuffer in calculations", () => {
      mockScrollContainer.scrollTop = 150;

      // With topBuffer 100, target position becomes 200 + 100 = 300
      // Since 300 > 150, this would be scrolling down
      const result = canScrollTo(mockScrollContainer, mockElement, {
        topBuffer: 100,
        disableScrollDown: true,
      });
      expect(result).toBe(false);
    });
  });

  describe("scrollToY", () => {
    test("should use scroll library when smooth is true", async () => {
      const mockScrollLib = await import("scroll");
      const targetY = 250;

      scrollToY(mockScrollContainer, targetY, { smooth: true });

      expect(mockScrollLib.default.top).toHaveBeenCalledWith(
        mockScrollContainer,
        targetY,
        { duration: expect.any(Number) },
        undefined,
      );
    });

    test("should call element.scroll when smooth is false", () => {
      const targetY = 350;

      scrollToY(mockScrollContainer, targetY, { smooth: false });

      expect(mockScrollContainer.scroll).toHaveBeenCalledWith({
        top: targetY,
        behavior: "instant",
      });
    });

    test("should default to non-smooth behavior", () => {
      const targetY = 300;

      scrollToY(mockScrollContainer, targetY);

      expect(mockScrollContainer.scroll).toHaveBeenCalledWith({
        top: targetY,
        behavior: "instant",
      });
    });
  });

  describe("scrollToBottom", () => {
    test("should scroll to scrollHeight when smooth is false", () => {
      scrollToBottom(mockScrollContainer, { smooth: false });

      expect(mockScrollContainer.scroll).toHaveBeenCalledWith({
        top: 500, // scrollHeight
        behavior: "instant",
      });
    });

    test("should use scroll library when smooth is true", async () => {
      const mockScrollLib = await import("scroll");

      scrollToBottom(mockScrollContainer, { smooth: true });

      expect(mockScrollLib.default.top).toHaveBeenCalledWith(
        mockScrollContainer,
        500, // scrollHeight
        { duration: expect.any(Number) },
        undefined,
      );
    });
  });

  describe("scrollToTop", () => {
    test("should scroll to position 0 when smooth is false", () => {
      scrollToTop(mockScrollContainer, { smooth: false });

      expect(mockScrollContainer.scroll).toHaveBeenCalledWith({
        top: 0,
        behavior: "instant",
      });
    });

    test("should use scroll library when smooth is true", async () => {
      const mockScrollLib = await import("scroll");

      scrollToTop(mockScrollContainer, { smooth: true });

      expect(mockScrollLib.default.top).toHaveBeenCalledWith(
        mockScrollContainer,
        0,
        { duration: expect.any(Number) },
        undefined,
      );
    });
  });

  describe("scrollTo", () => {
    test("should scroll to element position", () => {
      scrollTo(mockScrollContainer, mockElement, { smooth: false });

      expect(mockScrollContainer.scroll).toHaveBeenCalledWith({
        top: 200, // mockElement.offsetTop
        behavior: "instant",
      });
    });

    test("should respect topBuffer option", () => {
      scrollTo(mockScrollContainer, mockElement, { smooth: false, topBuffer: 50 });

      expect(mockScrollContainer.scroll).toHaveBeenCalledWith({
        top: 250, // mockElement.offsetTop + topBuffer
        behavior: "instant",
      });
    });

    test("should scroll to bottom of element when bottom option is true", () => {
      scrollTo(mockScrollContainer, mockElement, { smooth: false, bottom: true });

      expect(mockScrollContainer.scroll).toHaveBeenCalledWith({
        top: 250, // mockElement.offsetTop + mockElement.offsetHeight
        behavior: "instant",
      });
    });

    test("should not scroll when canScrollTo returns false", () => {
      // Set up conditions where scrolling is not allowed
      mockScrollContainer.scrollTop = 300;

      const result = scrollTo(mockScrollContainer, mockElement, {
        smooth: false,
        disableScrollUp: true,
      });

      expect(result).toBeUndefined();
      expect(mockScrollContainer.scroll).not.toHaveBeenCalled();
    });
  });
});
