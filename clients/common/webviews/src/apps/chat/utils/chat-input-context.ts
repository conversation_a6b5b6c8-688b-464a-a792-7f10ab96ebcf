import type { RemoteAgentsModel } from "$common-webviews/src/apps/remote-agent-manager/models/remote-agents-model";
import type RichTextEditorRoot from "$common-webviews/src/design-system/components/RichTextEditorAugment/Root.svelte";
import { get } from "svelte/store";
import type { AgentConversationModel } from "../models/agent-conversation-model";
import type { ChatModel } from "../models/chat-model";
import type { ToolsWebviewModel } from "../models/tools-webview-model";

import { DEFAULT_MAX_CHAT_INPUT_CHARS } from "$common-webviews/src/apps/chat/models/chat-flags-model";
import { FileController } from "$common-webviews/src/design-system/components/RichTextEditorAugment/plugins/File/controller";
import { RemoteAgentStatus } from "@augment-internal/sidecar-libs/src/remote-agent/remote-agent-types";
import { RemoteAgentSetupWindowAction } from "@augment-internal/sidecar-libs/src/metrics/types";
import { AGENT_LIMIT_MSG } from "../../remote-agent-manager/constants";
import { type GitReferenceModel } from "../../remote-agent-manager/models/git-reference-model";
import { type ConversationModel } from "../models/conversation-model";
import { type SlashCommandModel } from "../models/slash-command-model";
import { type ICurrentConversationTaskStore } from "../models/task-store";
import { SendMode } from "../types/send-mode";
import { calculateFileWarnings } from "./file-utils";
import type { ModelRegistry } from "../models/model-registry";

export interface ChatInputContext {
  /** Name of the current state (for debugging purposes only) */
  stateName: string;

  /** Whether to show the cancel button instead of send button */
  showCancelButton: boolean;

  /** Whether the action button should be disabled (does not apply to Cancel) */
  isDisabled: boolean;

  /** Action to perform when the send button is clicked */
  action: () => boolean;

  /** Action to perform when the cancel button is clicked */
  cancelAction: () => boolean;

  /** Reason to show for why the button is disabled */
  disabledReason?: string;
}

/**
 * Returns the action for the Remote Agents mode.
 */
export const getRemoteAgentsAction = (
  remoteAgentsModel: RemoteAgentsModel,
  conversationModel: ConversationModel,
  gitRefModel?: GitReferenceModel,
  richTextEditorRoot?: RichTextEditorRoot,
  isInputDisabled?: boolean,
) => {
  return () => {
    if (isInputDisabled) {
      // If the input is already disabled, we should not allow the user to send a message
      // with the Enter key
      return false;
    }
    // Store the message before potentially clearing it
    const message = conversationModel.draftExchange?.request_message ?? "";
    const richTextJson = conversationModel.draftExchange?.rich_text_json_repr;

    // If the message fails to send, we'll restore the draft
    const handleSendFailure = () => {
      // Only restore if there's no current draft (to avoid overwriting a new message)
      if (!conversationModel.draftExchange) {
        conversationModel.saveDraftExchange(message, richTextJson);
      }
    };

    if (!remoteAgentsModel.currentAgentId) {
      // If we're already creating an agent, don't allow another creation
      if (remoteAgentsModel.isCreatingAgent) {
        return false;
      }

      // Set creating agent flag to true
      remoteAgentsModel.setIsCreatingAgent(true);

      // Clear the draft exchange immediately
      conversationModel.clearDraftExchange();

      const createRemoteAgentLogging = async () => {
        // Log the remote agent creation event
        try {
          await remoteAgentsModel.reportRemoteAgentSetupWindowEvent(
            RemoteAgentSetupWindowAction.clickCreateAgent,
          );
        } catch (error) {
          console.error("Failed to report click create agent event:", error);
        }
      };

      const createRemoteAgent = async () => {
        try {
          void createRemoteAgentLogging();
          const agentId = await remoteAgentsModel.createRemoteAgentFromDraft(
            message,
            conversationModel.selectedModelId ?? undefined,
          );
          if (agentId) {
            remoteAgentsModel?.setCurrentAgent(agentId);
          } else {
            // Agent creation failed, restore the draft
            handleSendFailure();
          }
        } catch (error) {
          console.error("Failed to create remote agent: ", error);
          // Agent creation failed with an error, restore the draft
          handleSendFailure();
        } finally {
          // Reset creating agent flag regardless of success/failure
          remoteAgentsModel.setIsCreatingAgent(false);
        }
      };

      // If no gitRefModel, we can't check for auth so just assume it's fine
      if (!gitRefModel) {
        createRemoteAgent();
        return true;
      }

      // Check if the user is authenticated with GitHub, if not, we don't create the agent
      gitRefModel
        ?.isGithubAuthenticated()
        .then((isAuthenticated) => {
          if (!isAuthenticated) {
            // Reset creating agent flag if not authenticated
            remoteAgentsModel.setIsCreatingAgent(false);
            // Restore the draft since we're not creating an agent
            handleSendFailure();
            return;
          }
          createRemoteAgent();
        })
        .catch((error) => {
          console.error("Failed to check GitHub authentication status:", error);
          // Reset creating agent flag on error
          remoteAgentsModel.setIsCreatingAgent(false);
          // Restore the draft since we failed to check authentication
          handleSendFailure();
        });
      return true;
    }

    const draftExchange = conversationModel.draftExchange;
    if (draftExchange && remoteAgentsModel) {
      // Clear the draft exchange immediately
      conversationModel.clearDraftExchange();

      const selectedModelId = conversationModel.selectedModelId;
      remoteAgentsModel
        .sendMessage(draftExchange.request_message, selectedModelId ?? undefined)
        .then((success) => {
          if (!success) {
            // Message failed to send, restore the draft
            handleSendFailure();
          }
        })
        .catch(() => {
          // Exception occurred, restore the draft
          handleSendFailure();
        });
    }

    // Blur the editor after sending a message
    // Use setTimeout to ensure the blur happens after the action completes
    setTimeout(() => richTextEditorRoot?.blur(), 50);

    return true;
  };
};

export function getChatInputRemoteAgentContext(
  remoteAgentsModel: RemoteAgentsModel,
  conversationModel: ConversationModel,
  gitRefModel?: GitReferenceModel,
  richTextEditorRoot?: RichTextEditorRoot,
) {
  // We are in the Remote Agent mode
  // Check if agent limit has been reached
  let agentLimitReached = false;
  if (remoteAgentsModel.agentOverviews) {
    agentLimitReached =
      !!remoteAgentsModel.maxRemoteAgents &&
      remoteAgentsModel.agentOverviews.length >= remoteAgentsModel.maxRemoteAgents;
  }
  /** True if the action button should be disabled */
  let isDisabled = false;
  const isRemoteAgentStarting =
    remoteAgentsModel.isCreatingAgent ||
    (remoteAgentsModel?.isActive &&
      remoteAgentsModel?.currentAgent?.status === RemoteAgentStatus.agentStarting);

  const isRemoteAgentFailed =
    remoteAgentsModel?.currentAgent?.status === RemoteAgentStatus.agentFailed;

  if (!remoteAgentsModel.currentAgentId) {
    const newDraft = remoteAgentsModel.newAgentDraft;
    isDisabled =
      !conversationModel.draftExchange?.request_message ||
      remoteAgentsModel.isCreatingAgent ||
      agentLimitReached ||
      !newDraft?.commitRef?.github_commit_ref?.repository_url ||
      !newDraft.selectedBranch?.name;
  } else {
    isDisabled =
      !conversationModel.draftExchange?.request_message ||
      isRemoteAgentStarting ||
      isRemoteAgentFailed;
  }
  const showCancelButton = remoteAgentsModel.isCurrentAgentRunning && !isRemoteAgentStarting;

  let disabledReason = undefined;
  if (isDisabled) {
    if (!conversationModel.draftExchange?.request_message) {
      disabledReason = "Please enter a message";
    } else if (remoteAgentsModel.isCreatingAgent) {
      disabledReason = "Creating agent...";
    } else if (isRemoteAgentStarting) {
      disabledReason = "Agent is starting...";
    } else if (isRemoteAgentFailed) {
      disabledReason = "Agent has failed and cannot accept messages";
    } else if (agentLimitReached) {
      disabledReason = AGENT_LIMIT_MSG.replace(
        "%MAX_AGENTS%",
        remoteAgentsModel.maxRemoteAgents.toString(),
      );
    }
  }
  return {
    stateName: "remoteAgentMode",
    showCancelButton,
    isDisabled,
    disabledReason,
    // Send message to remote agent
    action: getRemoteAgentsAction(
      remoteAgentsModel,
      conversationModel,
      gitRefModel,
      richTextEditorRoot,
      isDisabled,
    ),
    // Interrupt the remote agent
    cancelAction: () => {
      remoteAgentsModel.interruptAgent();
      return true;
    },
  };
}

// Helper function to safely interrupt agent and send message
async function interruptAndSend(
  agentConversationModel: AgentConversationModel,
  conversationModel: ConversationModel,
): Promise<void> {
  await agentConversationModel.interruptAgent();
  await conversationModel.sendDraftExchange();
}

/**
 * Creates a mode-aware action function for agent mode (supports task creation)
 * @param mode - The current send mode (send or addTask)
 * @param conversationModel - The conversation model for managing drafts
 * @param agentConversationModel - The agent conversation model for interrupting agents
 * @param chatModel - The chat model for accessing extension client and flags
 * @returns A function that executes the appropriate action based on the send mode
 */
async function runAgentAction(
  mode: SendMode,
  conversationModel: ConversationModel,
  agentConversationModel: AgentConversationModel,
  chatModel: ChatModel,
  taskStore?: ICurrentConversationTaskStore,
): Promise<void> {
  switch (mode) {
    case SendMode.send: {
      await interruptAndSend(agentConversationModel, conversationModel);
      return;
    }
    case SendMode.addTask: {
      // Check if task list is enabled
      if (!chatModel.flags?.enableTaskList || !taskStore || !conversationModel.rootTaskUuid) {
        await interruptAndSend(agentConversationModel, conversationModel);
        return;
      }
      const messageContent = conversationModel.draftExchange?.request_message?.trim() || "";
      if (!messageContent) return;

      try {
        const newUuid = await createTaskFromMessage(
          taskStore,
          messageContent,
          conversationModel.rootTaskUuid,
        );
        // Clear the draft exchange if task creation was successful
        if (newUuid) {
          conversationModel.clearDraftExchange();
        }
      } catch (error) {
        console.error("Task creation failed, falling back to regular send:", error);
        // Fall back to regular send behavior when task creation fails
        await interruptAndSend(agentConversationModel, conversationModel);
      }
      return;
    }
    default: {
      await interruptAndSend(agentConversationModel, conversationModel);
      return;
    }
  }
}

/**
 * Creates a task from a message content and adds it to the current conversation's task list
 * @param taskStore - The task store to use for creating the task
 * @param messageContent - The message content to use for the task description
 * @param rootTaskUuid - The root task UUID to use as parent
 * @throws {Error} When no root task is found for the current conversation
 */
async function createTaskFromMessage(
  taskStore: ICurrentConversationTaskStore,
  messageContent: string,
  rootTaskUuid: string,
): Promise<string | undefined> {
  if (!rootTaskUuid) {
    throw new Error("No root task found for current conversation");
  }

  // Extract a meaningful task name from the message content
  const taskDetails = getTaskDetailsFromMessage(messageContent);
  if (!taskDetails) {
    return;
  }

  // Create the task as a child of the root task using the task store
  // This will automatically refresh the UI and provide immediate feedback
  const { name: taskName, description: taskDescription } = taskDetails;
  return await taskStore.createTask(taskName, taskDescription, rootTaskUuid);
}

/**
 * Extracts a meaningful task name and description from message content
 * @param messageContent - The full message content to extract a name from
 * @returns A task name and description, or undefined if no valid content is found
 */
function getTaskDetailsFromMessage(
  messageContent: string,
): { name: string; description: string } | undefined {
  messageContent = messageContent.trim();
  if (!messageContent) return undefined;

  // First non-trivial line is task name
  const firstLineEndIdx = messageContent.indexOf("\n");
  const taskName = messageContent.substring(
    0,
    firstLineEndIdx === -1 ? undefined : firstLineEndIdx,
  );
  // Rest is task description. Collapse newlines to spaces
  const taskDescription = messageContent.substring(firstLineEndIdx).trim().replace(/\n/g, " ");

  return { name: taskName, description: taskDescription };
}

/**
 * Returns the complete chat input context directly based on the application state
 */
export function getChatInputContext(
  chatModel: ChatModel,
  agentConversationModel: AgentConversationModel,
  toolsWebviewModel: ToolsWebviewModel,
  slashCommandModel: SlashCommandModel,
  sendMode: SendMode,
  taskStore?: ICurrentConversationTaskStore,
  remoteAgentsModel?: RemoteAgentsModel,
  gitRefModel?: GitReferenceModel,
  richTextEditorRoot?: RichTextEditorRoot,
  modelRegistry?: ModelRegistry,
): ChatInputContext {
  // Collect the properties we need from the models
  const conversationModel = chatModel.currentConversationModel;

  // Handle case where conversation model is missing
  if (!conversationModel) {
    return {
      stateName: "noConversation",
      showCancelButton: false,
      isDisabled: true,
      disabledReason: "No active conversation",
      action: () => false,
      cancelAction: () => false,
    };
  }

  const activeSlashCommand = get(slashCommandModel.activeCommand);
  const isCurrConversationAgentic = get(agentConversationModel.isCurrConversationAgentic);
  const messageLength = conversationModel.draftExchange?.request_message?.length || 0;
  const isOverCharLimit = messageLength > DEFAULT_MAX_CHAT_INPUT_CHARS;

  const hasLoadingImages = (() => {
    if (chatModel.flags?.enableChatMultimodal) {
      const json = conversationModel.draftExchange?.rich_text_json_repr;
      if (!json) {
        return false;
      }
      return FileController.hasLoadingImages(json);
    }
    return false;
  })();

  const { hasFileWarning, fileWarningMessage } = (() => {
    if (chatModel.flags?.enableChatMultimodal) {
      const json = conversationModel.draftExchange?.rich_text_json_repr;
      if (!json) {
        return { hasFileWarning: false, fileWarningMessage: "" };
      }
      const { hasWarning: hasFileWarning, warningMessage: fileWarningMessage } =
        calculateFileWarnings(json);
      return { hasFileWarning, fileWarningMessage };
    }
    return { hasFileWarning: false, fileWarningMessage: "" };
  })();

  if (isOverCharLimit) {
    return {
      stateName: "overCharLimit",
      showCancelButton: false,
      isDisabled: true,
      disabledReason: `Message exceeds the ${DEFAULT_MAX_CHAT_INPUT_CHARS} character limit`,
      action: () => false,
      cancelAction: () => false,
    };
  } else if (hasFileWarning) {
    return {
      stateName: "overImageLimit",
      showCancelButton: false,
      isDisabled: true,
      disabledReason: fileWarningMessage,
      action: () => false,
      cancelAction: () => false,
    };
  } else if (remoteAgentsModel?.isActive) {
    return getChatInputRemoteAgentContext(
      remoteAgentsModel,
      conversationModel,
      gitRefModel,
      richTextEditorRoot,
    );
  } else if (hasLoadingImages) {
    // Images are still loading - disable input until they're loaded
    return {
      stateName: "imagesLoading",
      showCancelButton: false,
      isDisabled: true, // Button should be disabled while images load
      // Can't send while images are loading
      action: () => false,
      // Default cancel action
      cancelAction: () => {
        if (isCurrConversationAgentic) {
          agentConversationModel.interruptAgent().catch((error) => {
            console.error("Failed to interrupt agent:", error);
          });
        } else {
          toolsWebviewModel.interruptToolsConversation();
        }
        return true;
      },
    };
  }

  // Check if the selected model is disabled (only when model registry is available)
  if (modelRegistry) {
    const selectedModel = get(modelRegistry.selectedModel);
    if (selectedModel.disabled) {
      return {
        stateName: "modelDisabled",
        showCancelButton: false,
        isDisabled: true,
        disabledReason: selectedModel.disabled_reason || "Selected model is disabled",
        action: () => false,
        cancelAction: () => false,
      };
    }
  }

  if (activeSlashCommand) {
    // A slash command is active
    return {
      stateName: "slashCommandActive",
      showCancelButton: false, // Show send button
      isDisabled: false, // Button should be enabled
      // Run the active slash command
      action: () => {
        slashCommandModel.runActiveCommand();
        conversationModel.clearDraftExchange();
        return true;
      },
      // No cancel action
      cancelAction: () => false,
    };
  } else if (isCurrConversationAgentic) {
    // We are in the Agent mode and there is no special circumstance
    return {
      stateName: "agentMode",
      showCancelButton: false, // Always show send button
      isDisabled: !conversationModel.hasDraft,
      // Use mode-aware action for agent mode (supports task creation)
      action: () => {
        void runAgentAction(
          sendMode,
          conversationModel,
          agentConversationModel,
          chatModel,
          taskStore,
        );
        return true;
      },
      // Interrupt the agent
      cancelAction: () => {
        agentConversationModel.interruptAgent().catch((error) => {
          console.error("Failed to interrupt agent:", error);
        });
        return true;
      },
    };
  } else {
    // We are in the Chat mode and there is no special circumstance
    return {
      stateName: "chatMode",
      showCancelButton: conversationModel.canCancelMessage,
      isDisabled: !conversationModel.canSendDraft,
      // In chat mode, always send as regular message (no task creation)
      action: () => {
        void conversationModel.sendDraftExchange();
        return true;
      },
      // Interrupt the conversation
      cancelAction: getDefaultCancelAction(agentConversationModel, toolsWebviewModel),
    };
  }
}

function getDefaultCancelAction(
  agentConversationModel: AgentConversationModel,
  toolsWebviewModel: ToolsWebviewModel,
) {
  return () => {
    if (get(agentConversationModel.isCurrConversationAgentic)) {
      agentConversationModel.interruptAgent().catch((error) => {
        console.error("Failed to interrupt agent:", error);
      });
    } else {
      toolsWebviewModel.interruptToolsConversation();
    }
    return true;
  };
}

export interface PromptEnhancerOptions {
  isDisabled: boolean;
  isHidden: boolean;
  disabledReason?: string;
}

export function getPromptEnhancerOptions(
  remoteAgentsModel: RemoteAgentsModel | undefined,
  currentRepositoryUrl: string | undefined,
  isInputDisabled: boolean,
): PromptEnhancerOptions {
  if (isInputDisabled) {
    return {
      isDisabled: true,
      isHidden: false,
      disabledReason: undefined,
    };
  }

  if (!remoteAgentsModel?.isActive) {
    return {
      isDisabled: false,
      isHidden: false,
      disabledReason: undefined,
    };
  }

  const selectedRepoURl =
    remoteAgentsModel.newAgentDraft?.commitRef?.github_commit_ref?.repository_url;
  if (!selectedRepoURl) {
    return {
      isDisabled: false,
      isHidden: false,
      disabledReason: undefined,
    };
  }

  const currentRepoUrl = currentRepositoryUrl || "";
  const isDifferentRepoSelected = currentRepoUrl && selectedRepoURl !== currentRepoUrl;
  if (isDifferentRepoSelected) {
    return {
      isDisabled: true,
      isHidden: false,
      disabledReason: "Prompt enhancer is only available when the current repository is selected",
    };
  }

  return {
    isDisabled: false,
    isHidden: false,
    disabledReason: undefined,
  };
}
