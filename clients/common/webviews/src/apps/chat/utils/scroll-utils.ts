import scroll from "scroll";

export interface ScrollToOptions {
  topBuffer?: number;
  smooth?: boolean;
  scrollDuration?: number;
  bottom?: boolean;
  disableScrollUp?: boolean;
  disableScrollDown?: boolean;
  reversed?: boolean;
  onScrollFinish?: () => void;
}

/**
 * Shared utilities for scroll-related operations.
 *
 * Includes:
 * - Distance calculations
 * - Performance optimized scroll operations
 */

/**
 * Calculates the distance from the bottom of a scrollable element
 *
 * @param element Scrollable element
 * @param reversed Whether the element uses flex-direction: column-reverse (default: true)
 * @returns Distance from bottom in pixels
 */
export function distanceFromBottom(element: HTMLElement, { reversed = true } = {}): number {
  const { scrollTop, clientHeight, scrollHeight } = element;
  if (reversed) {
    // In reversed mode with flex-direction: column-reverse
    // scrollTop is actually the distance from the bottom
    // When scrolling up, scrollTop decreases to negative values
    return Math.abs(scrollTop);
  }
  return scrollHeight - scrollTop - clientHeight;
}

/**
 * Determines if the scroll position is near the bottom of the element
 *
 * @param element Scrollable element
 * @param buffer Buffer distance in pixels (default: 40)
 * @param reversed Whether the element uses flex-direction: column-reverse (default: true)
 * @returns True if scroll is near bottom
 */
export function isScrollNearBottom(
  element: HTMLElement,
  { buffer = 40, reversed = true } = {},
): boolean {
  return distanceFromBottom(element, { reversed }) <= buffer;
}

function bottomPosition(element: HTMLElement, { reversed = true } = {}): number {
  return reversed ? 0 : element.scrollHeight;
}

/**
 * Determines if the scroll position is near the top of the element
 *
 * @param element Scrollable element
 * @param buffer Buffer distance in pixels (default: 40)
 * @returns True if scroll is near top
 */
export function isScrollNearTop(element: HTMLElement, buffer = 40): boolean {
  return element.scrollTop <= buffer;
}

/**
 * Computes the scrollY value needed to scroll the element into view
 *
 * @param element: target element to scroll to
 * @param options: scroll options
 * @returns: the scrollY value to scroll the element into view
 */
const SCROLL_DURATION = 350;
function computeScrollToY(element: HTMLElement, options: ScrollToOptions = {}): number {
  const { bottom, topBuffer } = options;
  const offsetBuffer = topBuffer ?? 0;
  if (bottom) {
    return element.offsetTop + element.offsetHeight + offsetBuffer;
  } else {
    return element.offsetTop + offsetBuffer;
  }
}

/**
 * Computes whether or not it is possible to scroll to a particular element
 * given the constraints
 *
 * @param scrollContainer
 * @param element
 * @param options
 * @returns
 */
export function canScrollTo(
  scrollContainer: HTMLElement,
  element: HTMLElement,
  options: ScrollToOptions = {},
): boolean {
  const { disableScrollUp, disableScrollDown } = options;
  const scrollToY = computeScrollToY(element, options);

  if (disableScrollUp && scrollToY < scrollContainer.scrollTop) {
    return false;
  } else if (disableScrollDown && scrollToY > scrollContainer.scrollTop) {
    return false;
  }

  return true;
}

/**
 * Scrolls to the last turn in the scrollContainer element.
 * If smooth is true, the scroll will be animated and return a cancel function.
 *
 * @param scrollContainer: The scrollable element containing the target element
 * @param element: The target element to scroll to
 * @param options: Configuration options for the scroll
 * @returns: A cancel function if smooth is true
 */
export function scrollTo(
  scrollContainer: HTMLElement,
  element: HTMLElement,
  options: ScrollToOptions = {},
): (() => void) | undefined {
  if (!canScrollTo(scrollContainer, element, options)) {
    return;
  }

  const scrollY = computeScrollToY(element, options);
  return scrollToY(scrollContainer, scrollY, options);
}

export function scrollToTop(scrollContainer: HTMLElement, options: ScrollToOptions = {}) {
  return scrollToY(scrollContainer, 0, options);
}

export function scrollToBottom(scrollContainer: HTMLElement, options: ScrollToOptions = {}) {
  const top = bottomPosition(scrollContainer, { reversed: options.reversed ?? false });
  return scrollToY(scrollContainer, top, options);
}

export function scrollToY(
  scrollContainer: HTMLElement,
  scrollY: number,
  options: ScrollToOptions = {},
): (() => void) | undefined {
  // If smooth is true, return the cancel function
  if (options.smooth) {
    return scroll.top(
      scrollContainer,
      scrollY,
      { duration: options.scrollDuration ?? SCROLL_DURATION },
      options.onScrollFinish,
    );
  } else {
    scrollContainer.scroll({ top: scrollY, behavior: "instant" });
    options.onScrollFinish?.();
  }
}
