import type { RemoteAgentsModel } from "$common-webviews/src/apps/remote-agent-manager/models/remote-agents-model";
import type {
  IRemoteAgentsError,
  ISendMessageError,
} from "../../remote-agent-manager/models/remote-agents-state-model/remote-agents-state-model";
import { SendMessageErrorType } from "../../remote-agent-manager/models/remote-agents-state-model/remote-agents-state-model";
import { RemoteAgentWorkspaceStatus } from "@augment-internal/sidecar-libs/src/remote-agent/remote-agent-types";
import type { ChatModel } from "../models/chat-model";
import { type ConversationModel } from "../models/conversation-model";
import {
  AgentExchangeStatus,
  type ChatItem,
  ExchangeStatus,
  type ExchangeWithStatus,
  hasToolResult,
  hasToolUse,
  isChatItemAgenticCheckpointDelimiter,
  isChatItemAgentOnboarding,
  isChatItemEducateFeatures,
  isChatItemExchangeWithStatus,
  isChatItemGenerateCommitMessage,
  isChatItemSignInWelcome,
  isChatItemSummaryResponse,
} from "../types/chat-message";
import { ToolUsePhase } from "../types/tool-use-state";

export type GroupedChatItem = { turn: ChatItem; idx: number }[];

export type LastGroupConfig = {
  /**
   * The message to display if the last exchange is retriable. If undefined, no retry message will be shown
   */
  retryMessage: string | undefined;
  /**
   * Whether to show the GeneratingResponse component
   */
  showGeneratingResponse: boolean;
  /**
   * Whether to show the ResumingRemoteAgent component
   */
  showResumingRemoteAgent: boolean;
  /**
   * Whether to show the AwaitingUserInput component
   */
  showAwaitingUserInput: boolean;
  /**
   * Whether to show the RunningSpacer component
   */
  showRunningSpacer: boolean;
  /**
   * Whether to show the Stopped component
   */
  showStopped: boolean;
  /**
   * Whether to show the remote agent error message
   */
  remoteAgentErrorConfig?: {
    error: IRemoteAgentsError | ISendMessageError;
    onRetry?: () => void;
    onDelete?: () => void;
  };
  /**
   * Whether to show the "Paused" component for remote agents
   */
  showPaused: boolean;
};

export interface MessageListContext {
  /**
   * The raw chat history
   */
  chatHistory: ChatItem[];
  /**
   * Grouped chat history
   */
  groupedChatHistory: GroupedChatItem[];
  /**
   * The options for what to render at the bottom of the message list
   */
  lastGroupConfig: LastGroupConfig;
  /**
   * Whether to show the floating buttons
   */
  doShowFloatingButtons: boolean;
  /**
   * Whether to show the agent setup logs
   */
  doShowAgentSetupLogs: boolean;
}

const getChatHistory = (
  conversationModel: ConversationModel,
  remoteAgentsModel: RemoteAgentsModel,
) => {
  return remoteAgentsModel.isActive
    ? remoteAgentsModel.getCurrentChatHistory() // If we have a remote agents model, the UI is driven by the remote state
    : conversationModel.chatHistory.filter((m) => {
        return (
          isChatItemExchangeWithStatus(m) ||
          isChatItemSignInWelcome(m) ||
          isChatItemGenerateCommitMessage(m) ||
          isChatItemEducateFeatures(m) ||
          isChatItemSummaryResponse(m) ||
          isChatItemAgentOnboarding(m) ||
          isChatItemAgenticCheckpointDelimiter(m)
        );
      });
};

const pushOrCreateGroup = (acc: GroupedChatItem[], turn: ChatItem, idx: number) => {
  const turnHasToolResult = isChatItemExchangeWithStatus(turn) && hasToolResult(turn);

  if (turnHasToolResult && acc.length > 0) {
    // Add to the current group if it has a tool result
    acc[acc.length - 1].push({ turn, idx });
  } else if (isChatItemAgenticCheckpointDelimiter(turn) && acc.length > 0) {
    // Add to the current group if it's a checkpoint delimiter
    acc[acc.length - 1].push({ turn, idx });
  } else {
    // Start a new group
    acc.push([{ turn, idx }]);
  }

  return acc;
};

/* We group tool use chains together. E.g., consider an interaction like this:
 *
 * - User: "What are my pending changes?"
 * - Augment: "Let's run `git status`," [Tool Use of `git status`]
 * - User: [Tool Result for `git status`]
 * - Augment: "Great. Now let's run `git diff`," [Tool Use of `git diff`]
 * - User: [Tool Result for `git diff`]
 *
 * This entire sequence of turns is a single "group", which is represented
 * as a single MessageListItem. This impacts scrolling behavior: when streaming,
 * we force scroll to keep the group in view. The grouping allows the user to
 * view any part of the grouped exchange during streaming.
 */
const groupChatHistory = (chatHistory: ChatItem[]) => {
  return chatHistory.reduce(pushOrCreateGroup, []);
};

/* Chat history is sequence of messages and it can be either from agent or user:
 * Below function groups the chat history into "user exchanges". A user exchange is defined as:
 *  - A sequence of messages starting with a user message
 *  - Followed by 0 or more agent messages
 *  - Followed by 0 or 1 tool result message
 *  - Followed by 0 or more agent messages
 *  - Followed by an optional checkpoint delimiter
 */

const groupByToolUses = (chatHistory: ChatItem[]) => {
  /*
    for now we set the character limit 150
    figure out a better way to do this...
   */
  const CHARACTER_LIMIT = 100;

  // Defensive check to ensure we have a valid array
  if (!Array.isArray(chatHistory)) {
    console.warn("groupByToolUses received non-array input:", chatHistory);
    return [];
  }

  const clonedChatHistory = structuredClone(chatHistory);

  const groupedChatHistory: GroupedChatItem[] = [];

  // The true index of the exchange relative to the chat history array
  // that will be used by the remote agent message list
  let exchangeIndex = 0;
  // Process tool use merging first
  for (let i = 0; i < clonedChatHistory.length; i++) {
    const turn = clonedChatHistory[i] as ExchangeWithStatus;
    const startingIndex = exchangeIndex;

    // this turn is actually ExchangeWithStatus
    // but it comes here as ChatItem. Need to figure out where the type is being lost...
    if (hasToolUse(turn) && turn) {
      turn.structured_output_nodes = turn.structured_output_nodes?.map((node) => ({
        ...node,
        requestId: turn.request_id,
      }));

      for (let j = i + 1; j < clonedChatHistory.length; j++) {
        const nextTurn = clonedChatHistory[j] as ExchangeWithStatus;
        const noResponseText = !nextTurn.response_text;
        const responseTextCriteriaMet =
          nextTurn.response_text && nextTurn.response_text.length < CHARACTER_LIMIT;

        if ((responseTextCriteriaMet || noResponseText) && hasToolUse(nextTurn)) {
          // merge the tool result into the tool use
          const nextTurnNodes = (nextTurn as ExchangeWithStatus).structured_output_nodes?.map(
            (node) => ({
              ...node,
              requestId: nextTurn.request_id,
            }),
          );

          (turn as ExchangeWithStatus).structured_output_nodes?.push(...(nextTurnNodes ?? []));

          // remove the tool result
          exchangeIndex++;
          clonedChatHistory.splice(j, 1);
          j--;
        } else {
          break;
        }
      }
    }
    pushOrCreateGroup(groupedChatHistory, turn, startingIndex);
    exchangeIndex++;
  }

  return groupedChatHistory;
};

const wasLastMessageCancelled = (
  conversationModel: ConversationModel,
  remoteAgentsModel: RemoteAgentsModel,
) => {
  const lastExchange = conversationModel?.lastExchange?.status;
  const lastExchangeCancelled = lastExchange === ExchangeStatus.cancelled;
  const lastToolPhase = conversationModel?.getLastToolUseState().phase;
  const lastToolCancelled = lastToolPhase === ToolUsePhase.cancelled;
  const isRemoteAgent = remoteAgentsModel.isActive;

  return !isRemoteAgent && (lastExchangeCancelled || lastToolCancelled);
};

const getAgentExchangeStatus = (
  ideAgentExchangeStatus: AgentExchangeStatus,
  remoteAgentsModel: RemoteAgentsModel,
) => {
  if (remoteAgentsModel.isActive) {
    const currentAgent = remoteAgentsModel.currentAgent;
    if (currentAgent?.workspace_status === RemoteAgentWorkspaceStatus.workspaceResuming) {
      return AgentExchangeStatus.running;
    }
    return remoteAgentsModel.isCurrentAgentRunning
      ? AgentExchangeStatus.running
      : AgentExchangeStatus.notRunning;
  }
  return ideAgentExchangeStatus;
};

export const getLastGroupConfig = (
  conversationModel: ConversationModel,
  agentExchangeStatus: AgentExchangeStatus,
  isCurrConversationAgentic: boolean,
  remoteAgentsModel: RemoteAgentsModel,
) => {
  const config: LastGroupConfig = {
    retryMessage: undefined,
    showGeneratingResponse: false,
    showResumingRemoteAgent: false,
    showAwaitingUserInput: false,
    showRunningSpacer: false,
    showStopped: false,
    remoteAgentErrorConfig: undefined,
    showPaused: false,
  };

  if (agentExchangeStatus === AgentExchangeStatus.running) {
    const lastExchange = conversationModel?.lastExchange;
    if (lastExchange?.isRetriable && lastExchange?.display_error_message) {
      config.retryMessage = lastExchange.display_error_message;
    } else if (isCurrConversationAgentic || remoteAgentsModel.isActive) {
      const lastToolState = remoteAgentsModel.isActive
        ? remoteAgentsModel.getLastToolUseState()
        : conversationModel.getLastToolUseState();

      // For remote agents, check if we should show "resuming" instead of "generating"
      if (remoteAgentsModel.isActive) {
        const currentAgent = remoteAgentsModel.currentAgent;

        // Show "resuming" if workspace is resuming
        if (currentAgent?.workspace_status === RemoteAgentWorkspaceStatus.workspaceResuming) {
          config.showResumingRemoteAgent = true;
        } else if (lastToolState.phase !== ToolUsePhase.running) {
          config.showGeneratingResponse = true;
        } else {
          config.showRunningSpacer = true;
        }
      } else if (lastToolState.phase !== ToolUsePhase.running) {
        config.showGeneratingResponse = true;
      } else {
        config.showRunningSpacer = true;
      }
    } else {
      config.showGeneratingResponse = true;
    }
  } else if (agentExchangeStatus === AgentExchangeStatus.awaitingUserAction) {
    config.showAwaitingUserInput = true;
    config.showRunningSpacer = true;
  } else if (wasLastMessageCancelled(conversationModel, remoteAgentsModel)) {
    config.showStopped = true;
  }

  if (remoteAgentsModel.isActive) {
    const currentAgent = remoteAgentsModel.currentAgent;
    if (currentAgent?.workspace_status === RemoteAgentWorkspaceStatus.workspacePaused) {
      config.showPaused = true;
    }
  }

  return config;
};

export const getMessageListContext = (
  chatModel: ChatModel,
  ideAgentExchangeStatus: AgentExchangeStatus,
  isCurrConversationAgentic: boolean,
  remoteAgentsModel: RemoteAgentsModel,
  enableGroupedTools: boolean = false,
): MessageListContext => {
  const conversationModel = chatModel.currentConversationModel;
  const chatHistory = getChatHistory(conversationModel, remoteAgentsModel);
  const groupedChatHistory = enableGroupedTools
    ? groupByToolUses(chatHistory)
    : groupChatHistory(chatHistory);
  const agentExchangeStatus = getAgentExchangeStatus(ideAgentExchangeStatus, remoteAgentsModel);
  const lastGroupConfig = getLastGroupConfig(
    conversationModel,
    agentExchangeStatus,
    isCurrConversationAgentic,
    remoteAgentsModel,
  );
  const doShowFloatingButtons = !remoteAgentsModel.isActive;
  const doShowAgentSetupLogs = !!remoteAgentsModel.isActive;

  if (remoteAgentsModel.isActive) {
    // Handle sendMessage errors first (higher priority)
    if (remoteAgentsModel.sendMessageError && remoteAgentsModel.currentAgentId) {
      const agentId = remoteAgentsModel.currentAgentId;
      const sendMessageError = remoteAgentsModel.sendMessageError;

      lastGroupConfig.remoteAgentErrorConfig = {
        error: sendMessageError,
        onRetry:
          sendMessageError.canRetry && sendMessageError.failedExchangeId
            ? () =>
                remoteAgentsModel.retryFailedMessage(agentId, sendMessageError.failedExchangeId!)
            : undefined,
        onDelete:
          sendMessageError.type === SendMessageErrorType.agentFailed
            ? () => remoteAgentsModel.deleteAgent(agentId)
            : undefined,
      };
    } else if (remoteAgentsModel.agentChatHistoryError && remoteAgentsModel.currentAgentId) {
      const agentId = remoteAgentsModel.currentAgentId;
      lastGroupConfig.remoteAgentErrorConfig = {
        error: remoteAgentsModel.agentChatHistoryError,
        onRetry: () => remoteAgentsModel.refreshAgentChatHistory(agentId),
      };
    }
  }

  return {
    chatHistory: Array.isArray(chatHistory) ? chatHistory : [],
    groupedChatHistory,
    lastGroupConfig,
    doShowFloatingButtons,
    doShowAgentSetupLogs,
  };
};
