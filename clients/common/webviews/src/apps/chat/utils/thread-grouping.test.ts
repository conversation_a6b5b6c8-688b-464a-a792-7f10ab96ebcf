/* eslint-disable @typescript-eslint/naming-convention */
import { describe, it, expect, beforeEach } from "vitest";
import {
  groupThreadsByAgeWithActive,
  displayOrder,
  ConversationAge,
  MAX_THREADS_TO_DISPLAY,
  type ChatThread,
  type LocalAgentThread,
  type RemoteAgentThread,
} from "./thread-grouping";
import type { Thread } from "$common-webviews/src/apps/chat/components/threads/ThreadsPanel.svelte";

// Mock conversation object
const mockConversation = {
  id: "mock-conversation",
  name: "Mock Conversation",
  createdAtIso: new Date().toISOString(),
  lastInteractedAtIso: new Date().toISOString(),
  chatHistory: [],
  feedbackStates: {},
  requestIds: [],
} as any;

// Helper function to create mock threads
function createChatThread(
  id: string,
  title: string,
  sortTimestamp: Date,
  options: {
    isNew?: boolean;
    isPinned?: boolean;
  } = {},
): ChatThread {
  return {
    id,
    type: "chat",
    title,
    updated_at: sortTimestamp.toISOString(),
    sortTimestamp,
    isNew: options.isNew,
    isPinned: options.isPinned,
    conversation: mockConversation,
  } as ChatThread;
}

function createLocalAgentThread(
  id: string,
  title: string,
  sortTimestamp: Date,
  options: {
    isNew?: boolean;
    isPinned?: boolean;
  } = {},
): LocalAgentThread {
  return {
    id,
    type: "localAgent",
    title,
    updated_at: sortTimestamp.toISOString(),
    sortTimestamp,
    isNew: options.isNew,
    isPinned: options.isPinned,
    conversation: mockConversation,
  } as LocalAgentThread;
}

function createRemoteAgentThread(
  id: string,
  title: string,
  sortTimestamp: Date,
  options: {
    isNew?: boolean;
    isPinned?: boolean;
  } = {},
): RemoteAgentThread {
  return {
    id,
    type: "remoteAgent",
    title,
    updated_at: sortTimestamp.toISOString(),
    sortTimestamp,
    isNew: options.isNew,
    isPinned: options.isPinned,
    conversation: mockConversation,
  } as RemoteAgentThread;
}

describe("thread-grouping", () => {
  let now: Date;
  let today: Date;
  let lastWeek: Date;
  let lastMonth: Date;
  let older: Date;

  beforeEach(() => {
    // Use current time for consistent testing
    now = new Date();
    const todayStart = new Date(now);
    todayStart.setHours(0, 0, 0, 0);

    today = new Date(todayStart.getTime() + 8 * 60 * 60 * 1000); // 8 AM today
    lastWeek = new Date(todayStart.getTime() - 8 * 24 * 60 * 60 * 1000); // 8 days ago
    lastMonth = new Date(todayStart.getTime() - 31 * 24 * 60 * 60 * 1000); // 31 days ago
    older = new Date(todayStart.getTime() - 60 * 24 * 60 * 60 * 1000); // 60 days ago
  });

  describe("constants", () => {
    it("should export MAX_THREADS_TO_DISPLAY constant", () => {
      expect(MAX_THREADS_TO_DISPLAY).toBe(1_000);
    });

    it("should export displayOrder array in correct order", () => {
      expect(displayOrder).toEqual([
        ConversationAge.New,
        ConversationAge.Pinned,
        ConversationAge.Active,
        ConversationAge.Today,
        ConversationAge.ThisWeek,
        ConversationAge.ThisMonth,
        ConversationAge.Older,
      ]);
    });
  });

  describe("groupThreadsByAgeWithActive", () => {
    it("should return empty array when no threads provided", () => {
      const result = groupThreadsByAgeWithActive([]);
      expect(result).toEqual([]);
    });

    it("should group threads by age categories", () => {
      const threads: Thread[] = [
        createChatThread("today1", "Today Thread 1", today),
        createChatThread("today2", "Today Thread 2", today),
        createChatThread("week1", "Week Thread 1", lastWeek),
        createChatThread("month1", "Month Thread 1", lastMonth),
        createChatThread("older1", "Older Thread 1", older),
      ];

      const result = groupThreadsByAgeWithActive(threads);

      expect(result.length).toBeGreaterThanOrEqual(3);

      // Find the groups by title instead of assuming order
      const todayGroup = result.find((g) => g.groupTitle === ConversationAge.Today);
      const monthGroup = result.find((g) => g.groupTitle === ConversationAge.ThisMonth);
      const olderGroup = result.find((g) => g.groupTitle === ConversationAge.Older);

      expect(todayGroup).toBeDefined();
      expect(todayGroup!.threads).toHaveLength(2);

      // Month group should have the month thread
      expect(monthGroup).toBeDefined();
      expect(monthGroup!.threads).toHaveLength(1);

      // Older group should have the week and older threads (since week is 8 days ago, it falls into older)
      expect(olderGroup).toBeDefined();
      expect(olderGroup!.threads).toHaveLength(2);
    });

    it("should prioritize New category over time-based categories", () => {
      const threads: Thread[] = [
        createChatThread("new1", "New Thread", today, { isNew: true }),
        createChatThread("today1", "Today Thread", today),
      ];

      const result = groupThreadsByAgeWithActive(threads);

      expect(result).toHaveLength(2);
      expect(result[0].groupTitle).toBe(ConversationAge.New);
      expect(result[0].threads).toHaveLength(1);
      expect(result[0].threads[0].id).toBe("new1");
      expect(result[1].groupTitle).toBe(ConversationAge.Today);
      expect(result[1].threads).toHaveLength(1);
      expect(result[1].threads[0].id).toBe("today1");
    });

    it("should prioritize Pinned category over time-based categories", () => {
      const threads: Thread[] = [
        createChatThread("pinned1", "Pinned Thread", older, { isPinned: true }),
        createChatThread("today1", "Today Thread", today),
      ];

      const result = groupThreadsByAgeWithActive(threads);

      expect(result).toHaveLength(2);
      expect(result[0].groupTitle).toBe(ConversationAge.Pinned);
      expect(result[0].threads).toHaveLength(1);
      expect(result[0].threads[0].id).toBe("pinned1");
      expect(result[1].groupTitle).toBe(ConversationAge.Today);
      expect(result[1].threads).toHaveLength(1);
      expect(result[1].threads[0].id).toBe("today1");
    });

    it("should prioritize New over Pinned category", () => {
      const threads: Thread[] = [
        createChatThread("both", "Both New and Pinned", today, { isNew: true, isPinned: true }),
      ];

      const result = groupThreadsByAgeWithActive(threads);

      expect(result).toHaveLength(1);
      expect(result[0].groupTitle).toBe(ConversationAge.New);
      expect(result[0].threads).toHaveLength(1);
      expect(result[0].threads[0].id).toBe("both");
    });

    it("should sort threads within groups by sortTimestamp descending", () => {
      const earlierToday = new Date(today.getTime() - 2 * 60 * 60 * 1000); // 2 hours earlier
      const laterToday = new Date(today.getTime() + 2 * 60 * 60 * 1000); // 2 hours later

      const threads: Thread[] = [
        createChatThread("early", "Early Thread", earlierToday),
        createChatThread("late", "Late Thread", laterToday),
      ];

      const result = groupThreadsByAgeWithActive(threads);

      expect(result).toHaveLength(1);
      expect(result[0].groupTitle).toBe(ConversationAge.Today);
      expect(result[0].threads).toHaveLength(2);
      // Should be sorted with latest first
      expect(result[0].threads[0].id).toBe("late");
      expect(result[0].threads[1].id).toBe("early");
    });

    it("should handle mixed thread types", () => {
      const threads: Thread[] = [
        createChatThread("chat1", "Chat Thread", today),
        createLocalAgentThread("local1", "Local Agent Thread", today),
        createRemoteAgentThread("remote1", "Remote Agent Thread", today),
      ];

      const result = groupThreadsByAgeWithActive(threads);

      expect(result).toHaveLength(1);
      expect(result[0].groupTitle).toBe(ConversationAge.Today);
      expect(result[0].threads).toHaveLength(3);

      const threadTypes = result[0].threads.map((t) => t.type);
      expect(threadTypes).toContain("chat");
      expect(threadTypes).toContain("localAgent");
      expect(threadTypes).toContain("remoteAgent");
    });

    it("should respect maxThreadsToDisplay parameter", () => {
      const threads: Thread[] = [
        createChatThread("thread1", "Thread 1", today),
        createChatThread("thread2", "Thread 2", today),
        createChatThread("thread3", "Thread 3", today),
        createChatThread("thread4", "Thread 4", today),
        createChatThread("thread5", "Thread 5", today),
      ];

      const result = groupThreadsByAgeWithActive(threads, 3);

      expect(result).toHaveLength(1);
      expect(result[0].groupTitle).toBe(ConversationAge.Today);
      expect(result[0].threads).toHaveLength(3);
    });

    it("should respect default MAX_THREADS_TO_DISPLAY when no limit specified", () => {
      // Create more threads than the default limit
      const threads: Thread[] = [];
      for (let i = 0; i < MAX_THREADS_TO_DISPLAY + 10; i++) {
        threads.push(createChatThread(`thread${i}`, `Thread ${i}`, today));
      }

      const result = groupThreadsByAgeWithActive(threads);

      const totalThreads = result.reduce((sum, group) => sum + group.threads.length, 0);
      expect(totalThreads).toBe(MAX_THREADS_TO_DISPLAY);
    });

    it("should distribute thread limit across multiple groups", () => {
      const threads: Thread[] = [
        createChatThread("new1", "New 1", today, { isNew: true }),
        createChatThread("new2", "New 2", today, { isNew: true }),
        createChatThread("pinned1", "Pinned 1", today, { isPinned: true }),
        createChatThread("pinned2", "Pinned 2", today, { isPinned: true }),
        createChatThread("today1", "Today 1", today),
      ];

      const result = groupThreadsByAgeWithActive(threads, 4);

      expect(result).toHaveLength(3);
      expect(result[0].groupTitle).toBe(ConversationAge.New);
      expect(result[0].threads).toHaveLength(2);
      expect(result[1].groupTitle).toBe(ConversationAge.Pinned);
      expect(result[1].threads).toHaveLength(2);
      expect(result[2].groupTitle).toBe(ConversationAge.Today);
      expect(result[2].threads).toHaveLength(0); // No threads left due to limit
    });

    it("should filter out empty groups", () => {
      const threads: Thread[] = [createChatThread("today1", "Today Thread", today)];

      const result = groupThreadsByAgeWithActive(threads);

      // Should only have Today group, not other empty groups
      expect(result).toHaveLength(1);
      expect(result[0].groupTitle).toBe(ConversationAge.Today);
    });

    it("should handle edge case dates correctly", () => {
      // Test boundary conditions for date categorization
      const todayStart = new Date(now);
      todayStart.setHours(0, 0, 0, 0);

      const weekBoundary = new Date(todayStart.getTime() - 7 * 24 * 60 * 60 * 1000); // Exactly 7 days ago
      const monthBoundary = new Date(todayStart.getTime() - 30 * 24 * 60 * 60 * 1000); // Exactly 30 days ago

      const threads: Thread[] = [
        createChatThread("todayStart", "Today Start", todayStart),
        createChatThread("weekBoundary", "Week Boundary", weekBoundary),
        createChatThread("monthBoundary", "Month Boundary", monthBoundary),
      ];

      const result = groupThreadsByAgeWithActive(threads);

      expect(result).toHaveLength(3);
      expect(result[0].groupTitle).toBe(ConversationAge.Today);
      expect(result[1].groupTitle).toBe(ConversationAge.ThisWeek);
      expect(result[2].groupTitle).toBe(ConversationAge.ThisMonth);
    });
  });
});
