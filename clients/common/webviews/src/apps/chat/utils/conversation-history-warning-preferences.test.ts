import { describe, it, expect, beforeEach, vi } from "vitest";
import {
  shouldHideConversationHistoryWarningForSession,
  setHideConversationHistoryWarningForSession,
  clearConversationHistoryWarningPreference,
  CONVERSATION_HISTORY_WARNING_REAPPEAR_DELAY_MS,
} from "./conversation-history-warning-preferences";

// Mock sessionStorage
const sessionStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};

Object.defineProperty(window, "sessionStorage", {
  value: sessionStorageMock,
});

describe("conversation-history-warning-preferences", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe("constants", () => {
    it("has correct reappear delay", () => {
      expect(CONVERSATION_HISTORY_WARNING_REAPPEAR_DELAY_MS).toBe(60 * 60 * 1000); // 1 hour
    });
  });

  describe("shouldHideConversationHistoryWarningForSession", () => {
    it("returns false when no preference is stored", () => {
      sessionStorageMock.getItem.mockReturnValue(null);

      const result = shouldHideConversationHistoryWarningForSession();

      expect(result).toBe(false);
      expect(sessionStorageMock.getItem).toHaveBeenCalledWith(
        "conversation-history-warning-hide-for-session",
      );
    });

    it("returns true when hideForSession preference is set to true", () => {
      const preference = {
        hideForSession: true,
        timestamp: "2023-01-01T00:00:00.000Z",
      };
      sessionStorageMock.getItem.mockReturnValue(JSON.stringify(preference));

      const result = shouldHideConversationHistoryWarningForSession();

      expect(result).toBe(true);
    });

    it("returns false when hideForSession preference is set to false", () => {
      const preference = {
        hideForSession: false,
        timestamp: "2023-01-01T00:00:00.000Z",
      };
      sessionStorageMock.getItem.mockReturnValue(JSON.stringify(preference));

      const result = shouldHideConversationHistoryWarningForSession();

      expect(result).toBe(false);
    });

    it("returns false when stored preference is invalid JSON", () => {
      sessionStorageMock.getItem.mockReturnValue("invalid json");

      const consoleSpy = vi.spyOn(console, "error").mockImplementation(() => {});

      const result = shouldHideConversationHistoryWarningForSession();

      expect(result).toBe(false);
      expect(consoleSpy).toHaveBeenCalledWith(
        "Failed to read conversation history warning preference from sessionStorage:",
        expect.any(Error),
      );

      consoleSpy.mockRestore();
    });

    it("handles sessionStorage errors gracefully", () => {
      sessionStorageMock.getItem.mockImplementation(() => {
        throw new Error("sessionStorage error");
      });

      const consoleSpy = vi.spyOn(console, "error").mockImplementation(() => {});

      const result = shouldHideConversationHistoryWarningForSession();

      expect(result).toBe(false);
      expect(consoleSpy).toHaveBeenCalledWith(
        "Failed to read conversation history warning preference from sessionStorage:",
        expect.any(Error),
      );

      consoleSpy.mockRestore();
    });
  });

  describe("setHideConversationHistoryWarningForSession", () => {
    it("stores the preference with current timestamp", () => {
      const mockDate = new Date("2023-01-01T12:00:00.000Z");
      vi.setSystemTime(mockDate);

      setHideConversationHistoryWarningForSession();

      expect(sessionStorageMock.setItem).toHaveBeenCalledWith(
        "conversation-history-warning-hide-for-session",
        JSON.stringify({
          hideForSession: true,
          timestamp: "2023-01-01T12:00:00.000Z",
        }),
      );

      vi.useRealTimers();
    });

    it("handles sessionStorage errors gracefully", () => {
      sessionStorageMock.setItem.mockImplementation(() => {
        throw new Error("sessionStorage error");
      });

      const consoleSpy = vi.spyOn(console, "error").mockImplementation(() => {});

      expect(() => setHideConversationHistoryWarningForSession()).not.toThrow();
      expect(consoleSpy).toHaveBeenCalledWith(
        "Failed to save conversation history warning preference to sessionStorage:",
        expect.any(Error),
      );

      consoleSpy.mockRestore();
    });
  });

  describe("clearConversationHistoryWarningPreference", () => {
    it("removes the preference from sessionStorage", () => {
      clearConversationHistoryWarningPreference();

      expect(sessionStorageMock.removeItem).toHaveBeenCalledWith(
        "conversation-history-warning-hide-for-session",
      );
    });

    it("handles sessionStorage errors gracefully", () => {
      sessionStorageMock.removeItem.mockImplementation(() => {
        throw new Error("sessionStorage error");
      });

      const consoleSpy = vi.spyOn(console, "error").mockImplementation(() => {});

      expect(() => clearConversationHistoryWarningPreference()).not.toThrow();
      expect(consoleSpy).toHaveBeenCalledWith(
        "Failed to clear conversation history warning preference from sessionStorage:",
        expect.any(Error),
      );

      consoleSpy.mockRestore();
    });
  });
});
