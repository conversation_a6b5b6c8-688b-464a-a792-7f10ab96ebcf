/* eslint-disable @typescript-eslint/naming-convention */
import { describe, test, expect, vi } from "vitest";
import { ExchangeStatus, type ExchangeWithStatus } from "../types/chat-message";
import { createExchangePointer } from "./exchange-pointer-utils";

describe("createExchangePointer", () => {
  test("uses request_id when available, falls back to generated UUID", () => {
    const exchangeWithRequestId: ExchangeWithStatus = {
      request_message: "Test",
      status: ExchangeStatus.success,
      request_id: "request-123",
    };

    expect(createExchangePointer(exchangeWithRequestId).exchangeUuid).toBe("request-123");

    const exchangeWithoutIds = {
      request_message: "Test",
      status: ExchangeStatus.success,
    } as ExchangeWithStatus;

    vi.stubGlobal("crypto", { randomUUID: vi.fn().mockReturnValue("generated-uuid") });
    expect(createExchangePointer(exchangeWithoutIds).exchangeUuid).toBe("generated-uuid");
    vi.unstubAllGlobals();
  });

  test("correctly determines hasResponse and isStreaming", () => {
    const withResponse = {
      request_message: "Test",
      response_text: "Response",
      status: ExchangeStatus.success,
      request_id: "123",
    };

    const pointer = createExchangePointer(withResponse);
    expect(pointer.hasResponse).toBe(true);
    expect(pointer.isStreaming).toBe(false);

    const streaming = {
      request_message: "Test",
      status: ExchangeStatus.sent,
      request_id: "123",
    };

    const streamingPointer = createExchangePointer(streaming);
    expect(streamingPointer.hasResponse).toBe(false);
    expect(streamingPointer.isStreaming).toBe(true);
  });
});
