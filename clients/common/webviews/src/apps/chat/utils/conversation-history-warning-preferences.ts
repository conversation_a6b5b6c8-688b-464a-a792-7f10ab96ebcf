/**
 * Utility module for managing conversation history warning preferences
 * Handles sessionStorage operations for the "hide for this session" functionality
 */

/**
 * Time in milliseconds after which a dismissed conversation history warning can reappear.
 * After dismissing the banner, it will reappear after this duration if the conversation
 * is still above the size threshold.
 */
export const CONVERSATION_HISTORY_WARNING_REAPPEAR_DELAY_MS = 60 * 60 * 1000; // 1 hour

interface ConversationHistoryWarningPreference {
  hideForSession: boolean;
  timestamp: string;
}

const STORAGE_KEY = "conversation-history-warning-hide-for-session";

/**
 * Checks if the user has chosen to hide conversation history warnings for this session
 * @returns true if the user has dismissed the warnings for this session
 */
export function shouldHideConversationHistoryWarningForSession(): boolean {
  try {
    const stored = sessionStorage.getItem(STORAGE_KEY);
    if (!stored) {
      return false;
    }

    const preference: ConversationHistoryWarningPreference = JSON.parse(stored);
    return preference.hideForSession === true;
  } catch (error) {
    console.error(
      "Failed to read conversation history warning preference from sessionStorage:",
      error,
    );
    return false;
  }
}

/**
 * Sets the user preference to hide conversation history warnings for this session
 */
export function setHideConversationHistoryWarningForSession(): void {
  try {
    const preference: ConversationHistoryWarningPreference = {
      hideForSession: true,
      timestamp: new Date().toISOString(),
    };
    sessionStorage.setItem(STORAGE_KEY, JSON.stringify(preference));
  } catch (error) {
    console.error(
      "Failed to save conversation history warning preference to sessionStorage:",
      error,
    );
  }
}

/**
 * Clears the "hide for session" preference (useful for testing or user preference reset)
 */
export function clearConversationHistoryWarningPreference(): void {
  try {
    sessionStorage.removeItem(STORAGE_KEY);
  } catch (error) {
    console.error(
      "Failed to clear conversation history warning preference from sessionStorage:",
      error,
    );
  }
}
