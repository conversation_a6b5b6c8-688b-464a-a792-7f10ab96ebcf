import { Chat<PERSON>etricName } from "$vscode/src/metrics/types";
import { type RemoteAgentsModel } from "../remote-agent-manager/models/remote-agents-model";
import type { ChatModeModel } from "./models/chat-mode-model";
import type { ChatModel } from "./models/chat-model";
import { type ChatFlagsModel } from "$common-webviews/src/apps/chat/models/chat-flags-model";
import type RichTextEditorRoot from "$common-webviews/src/design-system/components/RichTextEditorAugment/Root.svelte";
import { get } from "svelte/store";
import { ANALYTICS_EVENTS } from "@augment-internal/sidecar-libs/src/client-interfaces/analytics";
import { type IHotkeysCofiguration } from "$common-webviews/src/common/keybindings/types";

export function isFocusWithinTextboxElement(activeElement = document.activeElement): boolean {
  if (!activeElement) {
    return false;
  }

  // Check for elements with textbox role (ARIA) - this covers custom text inputs
  const role = activeElement.getAttribute("role");
  if (role === "textbox") {
    return true;
  }

  const tagName = activeElement.tagName.toLowerCase();

  // Check for textarea elements (always text-editable)
  if (tagName === "textarea") {
    return true;
  }

  // Check for standard HTML input elements that accept text
  if (tagName === "input") {
    const inputType = (activeElement as HTMLInputElement).type.toLowerCase();
    // Text-based input types (exclude non-text inputs like checkbox, radio, etc.)
    if (["text", "password", "email", "search", "tel", "url", "number"].includes(inputType)) {
      return true;
    }
  }

  // Check for contenteditable elements (rich text editors, etc.)
  if (activeElement.hasAttribute("contenteditable") || activeElement.closest("[contenteditable]")) {
    return true;
  }

  return false;
}

/**
 * Checks if the currently focused element is an interactive element that should retain focus.
 * This prevents the global Enter key handler from stealing focus from input fields, editors, etc.
 */
export function isFocusWithinInteractiveElement(): boolean {
  const activeElement = document.activeElement;

  if (!activeElement) {
    return false;
  }

  // Check for direct interactive elements
  const tagName = activeElement.tagName.toLowerCase();
  if (tagName === "select" || tagName === "button") {
    return true;
  }

  // Check for any input element (including non-text inputs like checkbox, radio, etc.)
  if (tagName === "input") {
    return true;
  }

  // Check for text-editable elements (textarea, contenteditable, textbox role, text inputs)
  if (isFocusWithinTextboxElement(activeElement)) {
    return true;
  }

  // Check for elements with tabindex (focusable custom elements)
  if (activeElement.hasAttribute("tabindex") && activeElement.getAttribute("tabindex") !== "-1") {
    return true;
  }

  // Check for elements with role that indicates interactivity
  const role = activeElement.getAttribute("role");
  if (
    role === "textbox" ||
    role === "combobox" ||
    role === "listbox" ||
    role === "button" ||
    role === "menuitem" ||
    role === "option"
  ) {
    return true;
  }

  return false;
}

export interface Context {
  richTextEditorRoot: RichTextEditorRoot;
  chatModeModel: ChatModeModel;
  remoteAgentsModel: RemoteAgentsModel;
  chatModel: ChatModel;
  openThreadsDialog?: () => void;
}

type KeybindingHandler = (e: KeyboardEvent, handler: any, context: Context) => void;


const ifChatModelFlagEnabled =
  (flag: keyof ChatFlagsModel, eventHandler: KeybindingHandler): KeybindingHandler =>
  (e, handler, context) => {
    const { chatModel } = context;
    if (chatModel.flags[flag]) {
      return eventHandler(e, handler, context);
    }
    return false;
  };

const ifRemoveAgentModeInactive =
  (eventHandler: KeybindingHandler): KeybindingHandler =>
  (e, handler, context) => {
    const { remoteAgentsModel } = context;
    if (!remoteAgentsModel?.isActive) {
      return eventHandler(e, handler, context);
    }
    return false;
  };

const focusEditor = (richTextEditor: RichTextEditorRoot) => {
  if (richTextEditor?.isFocused !== undefined && !get(richTextEditor.isFocused())) {
    if (!isFocusWithinInteractiveElement()) {
      richTextEditor.forceFocus();
    }
  }
};

// Keys are in mac format by default and mapped for windows/linux later
/* eslint-disable @typescript-eslint/naming-convention */
export const config: IHotkeysCofiguration = {
  enter: {
    name: "Focus Chat Input",
    description: "Focus the main chat input if not already in an interactive element",
    handler: (_e: KeyboardEvent, _handler: any, { richTextEditorRoot }: Context) => {
      focusEditor(richTextEditorRoot);
    },
  },

  "command+.": {
    name: "Toggle Agent Chat Auto Mode",
    description: "Toggle chat agent mode and focus input",
    commandName: "toggleAgentMode",
  },

  "command+l": {
    name: "New Conversation",
    description: "Create a new conversation thread",
    commandName: "newConversation",
    handler: (e, _handler, { chatModeModel, remoteAgentsModel, chatModel }: Context) => {
      if (chatModeModel) {
        void chatModeModel.createThreadOfCurrentType();
        chatModel.eventTracker?.trackEvent(ANALYTICS_EVENTS.THREAD_CREATION_ATTEMPTED, {
          source: "keybinding",
        });
      } else {
        if (remoteAgentsModel.isActive) {
          remoteAgentsModel.setCurrentAgent(undefined);
        } else {
          chatModel.setCurrentConversation();
          chatModel.eventTracker?.trackEvent(ANALYTICS_EVENTS.THREAD_CREATION_ATTEMPTED, {
            source: "keybinding",
          });
          chatModel.extensionClient.reportWebviewClientEvent(ChatMetricName.chatNewConversation);
        }
      }
      e.preventDefault();
    },
  },

  "command+up": {
    name: "Scroll Conversation Up",
    description: "Scrolls conversation to previous message",
    commandName: "scrollConversationUp",
  },

  "command+down": {
    name: "Scroll Conversation Down",
    description: "Scrolls conversation to next message",
    commandName: "scrollConversationDown",
  },

  "command+option+'": {
    name: "Toggle Threads",
    description: "Toggle the threads panel open/closed (debug feature)",
    commandName: "toggleThreadsPanel",
  },

  "option+'": {
    name: "Close Threads Panel",
    description: "Close the threads panel (debug feature)",
    commandName: "closeThreadsPanel",
  },

  "command+shift+l": {
    name: "Pop Conversation",
    description: "Remove current conversation (debug feature)",
    handler: ifChatModelFlagEnabled(
      "enableDebugFeatures",
      ifRemoveAgentModeInactive((e: KeyboardEvent, _handler: any, { chatModel }: Context) => {
        void chatModel.popCurrentConversation();
        e.preventDefault();
      }),
    ),
  },

  "option+]": {
    name: "Next Conversation",
    description: "Switch to next conversation (debug feature)",
    handler: ifChatModelFlagEnabled(
      "enableDebugFeatures",
      ifRemoveAgentModeInactive((e: KeyboardEvent, _handler: any, { chatModel }: Context) => {
        chatModel.setCurrentConversation(chatModel.nextConversation?.id, false);
        e.preventDefault();
      }),
    ),
  },

  "option+[": {
    name: "Previous Conversation",
    description: "Switch to previous conversation (debug feature)",
    handler: ifChatModelFlagEnabled(
      "enableDebugFeatures",
      ifRemoveAgentModeInactive((e: KeyboardEvent, _handler: any, { chatModel }: Context) => {
        chatModel.setCurrentConversation(chatModel.previousConversation?.id, false);
        e.preventDefault();
      }),
    ),
  },

  "command+]": {
    name: "Focus Next",
    description: "Focus next element in conversation (debug feature)",
    handler: ifChatModelFlagEnabled(
      "enableDebugFeatures",
      ifRemoveAgentModeInactive((e: KeyboardEvent, _handler: any, { chatModel }: Context) => {
        chatModel.currentConversationModel.focusModel.focusNext();
        e.preventDefault();
      }),
    ),
  },

  "command+[": {
    name: "Focus Previous",
    description: "Focus previous element in conversation (debug feature)",
    handler: ifChatModelFlagEnabled(
      "enableDebugFeatures",
      ifRemoveAgentModeInactive((e: KeyboardEvent, _handler: any, { chatModel }: Context) => {
        chatModel.currentConversationModel.focusModel.focusPrev();
        e.preventDefault();
      }),
    ),
  },

  "command+/": {
    name: "Enhance Prompt",
    description: "Enhance the current prompt",
    commandName: "enhancePrompt",
  },

  "command+shift+enter": {
    name: "Allow tool",
    description: "Allow tool to run",
    commandName: "allowTool",
  },

  escape: {
    name: "Clear Focus",
    description: "Clear current focus in conversation (debug feature)",
    handler: ifChatModelFlagEnabled(
      "enableDebugFeatures",
      ifRemoveAgentModeInactive((e: KeyboardEvent, _handler: any, { chatModel }: Context) => {
        chatModel.currentConversationModel.focusModel.setFocusIdx(undefined);
        e.preventDefault();
      }),
    ),
  },
};
