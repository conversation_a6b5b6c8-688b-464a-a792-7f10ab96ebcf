<script lang="ts">
  import type { IChatFlags, IConversation } from "./models/types";
  import type { SubscriptionInfo } from "./models/subscription-model";
  import ComponentLoader from "$common-webviews/src/common/components/component-loader/ComponentLoader.svelte";
  import MonacoProvider from "$common-webviews/src/design-system/components/MonacoProvider";
  import { FULL_SIZE_OVERLAY_PORTAL_ID } from "$common-webviews/src/design-system/components/FullSizeOverlayAugment/FullSizeOverlayAugment.svelte";

  export let initialConversation: IConversation | undefined = undefined;
  export let initialFlags: Partial<IChatFlags> | undefined = undefined;
  export let initialSubscriptionInfo: SubscriptionInfo | undefined = undefined;
  export let initialSubscriptionDismissed: boolean | undefined = undefined;

  export let loader = async () => {
    await import("./chat.css");
    return (await import("./Chat.svelte")).default as any;
  };

  // Props passed to <ComponentLoader /> for deterministic story loading
  export let minDisplayTime: number = 0;
  export let randomize: boolean = false;
  export let retryCount: number = 0;
  const props = {
    initialConversation,
    initialFlags,
    initialSubscriptionInfo,
    initialSubscriptionDismissed,
  };
</script>

<MonacoProvider.Root>
  <div id={FULL_SIZE_OVERLAY_PORTAL_ID}></div>
  <ComponentLoader {loader} {props} {minDisplayTime} {randomize} {retryCount} />
</MonacoProvider.Root>
