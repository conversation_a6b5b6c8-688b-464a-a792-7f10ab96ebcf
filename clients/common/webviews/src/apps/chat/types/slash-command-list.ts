import type { Diagnostic, DiagnosticFileLocation } from "$vscode/src/utils/types";
import { type ChatModel } from "../models/chat-model";
import type { IMentionable } from "$common-webviews/src/common/components/inputs/types";

export interface ISlashCommandOptionData extends IMentionable {
  id: string;
  label: string;
  description: string;
}

async function getDiagnostics(chatModel: ChatModel): Promise<Diagnostic[]> {
  let diagnostics: Diagnostic[] = [];
  try {
    diagnostics = await chatModel.extensionClient.getDiagnostics();
  } catch (error) {
    console.error("Failed to get diagnostic messages:", error);
  }
  return diagnostics;
}

function formatLineRange(location: DiagnosticFileLocation): string {
  if (location.line_start === location.line_end) {
    return `L${location.line_start + 1}`;
  } else {
    return `L${location.line_start + 1}-${location.line_end + 1}`;
  }
}

function formatDiagnostics(diagnostics: Diagnostic[]): string {
  if (diagnostics.length === 0) {
    return " The IDE does not report any issues on the selected code.";
  } else if (diagnostics.length === 1) {
    return ` The IDE reports the following issue:\n\n• ${formatLineRange(diagnostics[0].location)}: ${diagnostics[0].message}`;
  } else {
    const diagnosticGroups = diagnostics
      .sort((a, b) => a.location.line_start - b.location.line_start)
      .reduce(
        (acc, diagnostic) => {
          // eslint-disable-next-line @typescript-eslint/naming-convention
          const lineRange = `${diagnostic.location.line_start},${diagnostic.location.line_end}`;
          if (!acc[lineRange]) {
            acc[lineRange] = [];
          }
          acc[lineRange].push(diagnostic);
          return acc;
        },
        {} as Record<string, Diagnostic[]>,
      );

    const formattedDiagnostics = Object.entries(diagnosticGroups)
      .map(([lineRange, group]) => {
        const [start, end] = lineRange.split(",").map(Number);
        // eslint-disable-next-line @typescript-eslint/naming-convention
        const rangeString = formatLineRange({
          path: "",
          /* eslint-disable @typescript-eslint/naming-convention */
          line_start: start,
          line_end: end,
          /* eslint-enable @typescript-eslint/naming-convention */
        });

        if (group.length === 1) {
          return `• ${rangeString}: ${group[0].message}`;
        } else {
          const messages = group
            .map((diagnostic, index) => `    ${index + 1}. ${diagnostic.message}`)
            .join("\n");
          return `• ${rangeString}:\n${messages}`;
        }
      })
      .join("\n");

    const totalStart = Math.min(...diagnostics.map((d) => d.location.line_start));
    const totalEnd = Math.max(...diagnostics.map((d) => d.location.line_end));
    const totalRangeString =
      totalStart === totalEnd ? `L${totalStart + 1}` : `L${totalStart + 1}-${totalEnd + 1}`;

    return ` The IDE reports the following issues on ${totalRangeString}:\n\n${formattedDiagnostics}`;
  }
}

async function formatFixPrompt(_input: string, chatModel: ChatModel): Promise<string> {
  const diagnostics = await getDiagnostics(chatModel);
  let prompt = "Fix the selected code.";
  prompt += formatDiagnostics(diagnostics);
  return prompt;
}

export interface SlashCommand extends ISlashCommandOptionData {
  requiresInput: boolean;
  requiresEditorSelection: boolean;
  placeholderText: string;
  selectionReminderMessage: string;
  visible: boolean;
  promptFactory: (input: string, chatModel: ChatModel) => Promise<string>;
}

export const slashCommandList: SlashCommand[] = [
  {
    id: "find",
    label: "find",
    description: "things in your code base",
    requiresInput: true,
    requiresEditorSelection: false,
    placeholderText: "What code are you looking for?",
    selectionReminderMessage: "Select code in the editor to find.",
    visible: true,
    promptFactory: async (input: string) => `Find ${input}`,
  },
  {
    id: "explain",
    label: "explain",
    description: "how selected code works",
    requiresInput: false,
    requiresEditorSelection: true,
    placeholderText: "",
    selectionReminderMessage: "Select code in the editor to explain.",
    visible: true,
    promptFactory: async () => `Explain the selected code`,
  },
  {
    id: "fix",
    label: "fix",
    description: "issues in selected code",
    requiresInput: false,
    requiresEditorSelection: true,
    placeholderText: "",
    selectionReminderMessage: "Select code in the editor to fix.",
    visible: true,
    promptFactory: formatFixPrompt,
  },
  {
    id: "write-test",
    label: "write-test",
    description: "add tests for selected code",
    requiresInput: false,
    requiresEditorSelection: true,
    placeholderText: "",
    selectionReminderMessage: "Select code in the editor to test.",
    visible: true,
    promptFactory: async () => `Write a test for the selected code`,
  },
  {
    id: "document",
    label: "document",
    description: "write documentation for selected code",
    requiresInput: false,
    requiresEditorSelection: true,
    placeholderText: "",
    selectionReminderMessage: "Select code in the editor to document.",
    visible: true,
    promptFactory: async () => `Document selected code. Do not make unnecessary changes.`,
  },
  {
    id: "generate-commit-message",
    label: "generate-commit-message",
    description: "generate a commit message",
    requiresInput: false,
    requiresEditorSelection: false,
    placeholderText: "",
    selectionReminderMessage: "Hit enter or send to generate a commit message",
    visible: true,
    promptFactory: async () => ``,
  },
];

export function getSlashCommand(commandId: string | undefined): SlashCommand | undefined {
  return slashCommandList.find((slashCommand) => slashCommand.id === commandId);
}

export function getVisibleSlashCommands(): SlashCommand[] {
  return slashCommandList.filter((slashCommand) => slashCommand.visible);
}
