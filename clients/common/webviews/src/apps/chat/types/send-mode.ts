/**
 * @file send-mode.ts
 * Types and enums for chat send mode functionality
 */

import SendIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/solid/paper-plane-top.svg?component";
import DiagramNext from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/diagram-next.svg?component";

/**
 * Enum representing different send modes for chat messages
 */
export enum SendMode {
  /** Standard send mode - just sends the message */
  send = "send",
  /** Add task with the message content */
  addTask = "addTask",
}

/**
 * Interface for send mode option data used in UI components
 */
export interface SendModeOption {
  /** The send mode value */
  id: SendMode;
  /** Display label for the mode */
  label: string;
  /** Icon component for the mode */
  icon?: any;
  /** Optional description for tooltips */
  description?: string;
}

export const SEND_MODE = {
  id: SendMode.send,
  label: "Send to Agent",
  icon: SendIcon,
  description: "Send message to agent",
} as const;

/**
 * Default send mode options for the UI
 */
export const DEFAULT_SEND_MODE_OPTIONS: SendModeOption[] = [
  SEND_MODE,
  {
    id: SendMode.addTask,
    label: "Add Task",
    icon: DiagramNext,
    description: "Add task with the message content",
  },
];

/**
 * Gets the display text for a send mode
 */
export function getSendModeDisplayText(mode: SendMode): string {
  const option = DEFAULT_SEND_MODE_OPTIONS.find((opt) => opt.id === mode);
  return option?.label || "Send to Agent";
}
