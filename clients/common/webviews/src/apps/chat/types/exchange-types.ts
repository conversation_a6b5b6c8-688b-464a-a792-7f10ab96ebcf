import type { ExchangeWithStatus } from "./chat-message";

// Exchange-related types for webview layer
// These types define what the webview knows about exchanges

export interface ExchangeLoadRequest {
  exchangeUuids: string[];
}

export interface ExchangeUpdateRequest {
  exchangeUuid: string;
  updates: Partial<ExchangeWithStatus>;
}

export interface BatchExchangeUpdate {
  operations: ExchangeUpdateRequest[];
}
