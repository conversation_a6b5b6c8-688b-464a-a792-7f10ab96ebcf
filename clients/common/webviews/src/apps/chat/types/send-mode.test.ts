/**
 * @file send-mode.test.ts
 * Tests for send mode types and utility functions
 */

import { describe, it, expect } from "vitest";
import {
  SendMode,
  DEFAULT_SEND_MODE_OPTIONS,
  getSendModeDisplayText,
  type SendModeOption,
} from "./send-mode";

describe("SendMode", () => {
  describe("enum values", () => {
    it("should have correct send mode value", () => {
      expect(SendMode.send).toBe("send");
    });

    it("should have correct addTask mode value", () => {
      expect(SendMode.addTask).toBe("addTask");
    });

    it("should have exactly two modes", () => {
      const modes = Object.values(SendMode);
      expect(modes).toHaveLength(2);
      expect(modes).toContain("send");
      expect(modes).toContain("addTask");
    });
  });

  describe("DEFAULT_SEND_MODE_OPTIONS", () => {
    it("should have correct number of options", () => {
      expect(DEFAULT_SEND_MODE_OPTIONS).toHaveLength(2);
    });

    it("should have send mode option", () => {
      const sendOption = DEFAULT_SEND_MODE_OPTIONS.find((opt) => opt.id === SendMode.send);
      expect(sendOption).toBeDefined();
      expect(sendOption?.label).toBe("Send to Agent");
      expect(sendOption?.description).toBe("Send message to agent");
    });

    it("should have addTask mode option", () => {
      const addTaskOption = DEFAULT_SEND_MODE_OPTIONS.find((opt) => opt.id === SendMode.addTask);
      expect(addTaskOption).toBeDefined();
      expect(addTaskOption?.label).toBe("Add Task");
      expect(addTaskOption?.description).toBe("Add task with the message content");
    });

    it("should have valid SendModeOption structure", () => {
      DEFAULT_SEND_MODE_OPTIONS.forEach((option: SendModeOption) => {
        expect(option).toHaveProperty("id");
        expect(option).toHaveProperty("label");
        expect(option).toHaveProperty("description");
        expect(Object.values(SendMode)).toContain(option.id);
        expect(typeof option.label).toBe("string");
        expect(typeof option.description).toBe("string");
      });
    });
  });

  describe("getSendModeDisplayText", () => {
    it("should return correct display text for send mode", () => {
      expect(getSendModeDisplayText(SendMode.send)).toBe("Send to Agent");
    });

    it("should return correct display text for addTask mode", () => {
      expect(getSendModeDisplayText(SendMode.addTask)).toBe("Add Task");
    });

    it("should return default text for unknown mode", () => {
      expect(getSendModeDisplayText("unknown" as SendMode)).toBe("Send to Agent");
    });

    it("should handle null/undefined gracefully", () => {
      expect(getSendModeDisplayText(null as any)).toBe("Send to Agent");
      expect(getSendModeDisplayText(undefined as any)).toBe("Send to Agent");
    });
  });

  describe("type safety", () => {
    it("should ensure SendModeOption has required properties", () => {
      const option: SendModeOption = {
        id: SendMode.send,
        label: "Test",
        description: "Test description",
      };

      expect(option.id).toBe(SendMode.send);
      expect(option.label).toBe("Test");
      expect(option.description).toBe("Test description");
    });

    it("should allow optional icon property", () => {
      const optionWithIcon: SendModeOption = {
        id: SendMode.addTask,
        label: "Test",
        description: "Test description",
        icon: "test-icon",
      };

      expect(optionWithIcon.icon).toBe("test-icon");
    });
  });
});
