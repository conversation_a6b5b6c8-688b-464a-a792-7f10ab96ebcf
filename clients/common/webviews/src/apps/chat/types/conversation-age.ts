/**
 * Enum representing different age categories for conversations.
 * Used for grouping threads in both the old ThreadsMenu and new ThreadsList components.
 */
export enum ConversationAge {
  /* eslint-disable @typescript-eslint/naming-convention */
  Pinned = "Pinned",
  Active = "Active remote agents",
  Today = "Today",
  New = "New",
  ThisWeek = "Last 7 days",
  ThisMonth = "Last 30 days",
  Older = "Older",
  /* eslint-enable @typescript-eslint/naming-convention */
}
