import { describe, expect, test } from "vitest";
import {
  ChatItemType,
  ExchangeStatus,
  SeenState,
  isIChatItemSeenState,
  isChatItemExchangePointer,
  getAgentResponseFromGroup,
  shouldShow<PERSON>ooter,
  type ExchangePointer,
  type ExchangeWithStatus,
  type ChatItem,
} from "./chat-message";
import { ChatResultNodeType } from "@augment-internal/sidecar-libs/src/chat/chat-types";
import { type GroupedChatItem } from "../utils/message-list-context";
import { TEMP_EXHANGE_ID_PREFIX } from "../models/conversation-model";

describe("isIChatItemSeenState", () => {
  test("should return true for valid IChatItemSeenState with both properties", () => {
    /* eslint-disable @typescript-eslint/naming-convention */
    const validState = {
      request_id: "123",
      seen_state: SeenState.seen,
    };
    /* eslint-enable @typescript-eslint/naming-convention */
    expect(isIChatItemSeenState(validState)).toBe(true);
  });

  test("should return true for valid IChatItemSeenState with only request_id", () => {
    /* eslint-disable @typescript-eslint/naming-convention */
    const validState = {
      request_id: "123",
    };
    /* eslint-enable @typescript-eslint/naming-convention */
    expect(isIChatItemSeenState(validState)).toBe(true);
  });

  test("should return true for valid IChatItemSeenState with only seen_state", () => {
    /* eslint-disable @typescript-eslint/naming-convention */
    const validState = {
      seen_state: SeenState.unseen,
    };
    /* eslint-enable @typescript-eslint/naming-convention */
    expect(isIChatItemSeenState(validState)).toBe(true);
  });

  test("should return true for empty object", () => {
    expect(isIChatItemSeenState({})).toBe(true);
  });

  test("should return false for null", () => {
    expect(isIChatItemSeenState(null)).toBe(false);
  });

  test("should return false for undefined", () => {
    expect(isIChatItemSeenState(undefined)).toBe(false);
  });

  test("should return false for non-object types", () => {
    expect(isIChatItemSeenState("string")).toBe(false);
    expect(isIChatItemSeenState(123)).toBe(false);
    expect(isIChatItemSeenState(true)).toBe(false);
  });

  test("should return false when request_id is not a string", () => {
    /* eslint-disable @typescript-eslint/naming-convention */
    const invalidState = {
      request_id: 123,
    };
    /* eslint-enable @typescript-eslint/naming-convention */
    expect(isIChatItemSeenState(invalidState)).toBe(false);
  });

  test("should return false when seen_state is invalid", () => {
    /* eslint-disable @typescript-eslint/naming-convention */
    const invalidState = {
      seen_state: "invalid",
    };
    /* eslint-enable @typescript-eslint/naming-convention */
    expect(isIChatItemSeenState(invalidState)).toBe(false);
  });

  test("should return true for valid state with additional properties", () => {
    /* eslint-disable @typescript-eslint/naming-convention */
    const validState = {
      request_id: "123",
      seen_state: SeenState.seen,
      extraProp: "extra",
    };
    /* eslint-enable @typescript-eslint/naming-convention */
    expect(isIChatItemSeenState(validState)).toBe(true);
  });
});

describe("isChatItemExchangePointer", () => {
  test("returns true for ExchangePointer", () => {
    const pointer: ExchangePointer = {
      chatItemType: ChatItemType.exchangePointer,
      exchangeUuid: "test-uuid-123",
    };

    expect(isChatItemExchangePointer(pointer)).toBe(true);
  });

  test("returns false for ExchangeWithStatus", () => {
    /* eslint-disable @typescript-eslint/naming-convention */
    const exchange: ExchangeWithStatus = {
      request_message: "Test message",
      status: ExchangeStatus.success,
    };
    /* eslint-enable @typescript-eslint/naming-convention */

    expect(isChatItemExchangePointer(exchange)).toBe(false);
  });

  test("returns false for null/undefined", () => {
    expect(isChatItemExchangePointer(null)).toBe(false);
    expect(isChatItemExchangePointer(undefined)).toBe(false);
  });

  test("provides correct type narrowing", () => {
    const item: ChatItem = {
      chatItemType: ChatItemType.exchangePointer,
      exchangeUuid: "test-uuid-123",
    };

    if (isChatItemExchangePointer(item)) {
      // TypeScript should know this is an ExchangePointer
      expect(item.exchangeUuid).toBe("test-uuid-123");
    } else {
      throw new Error("Type guard should have returned true");
    }
  });
});

describe("getAgentResponseFromGroup", () => {
  test("returns undefined for undefined group", () => {
    expect(getAgentResponseFromGroup(undefined)).toBeUndefined();
  });

  test("returns undefined for empty group", () => {
    const emptyGroup: GroupedChatItem = [];
    expect(getAgentResponseFromGroup(emptyGroup)).toBeUndefined();
  });

  test("returns single response text from ExchangeWithStatus", () => {
    /* eslint-disable @typescript-eslint/naming-convention */
    const exchange: ExchangeWithStatus = {
      request_message: "Test request",
      response_text: "Test response",
      status: ExchangeStatus.success,
    };
    /* eslint-enable @typescript-eslint/naming-convention */

    const group: GroupedChatItem = [{ turn: exchange, idx: 0 }];
    expect(getAgentResponseFromGroup(group)).toBe("Test response");
  });

  test("returns joined response texts from multiple ExchangeWithStatus items", () => {
    /* eslint-disable @typescript-eslint/naming-convention */
    const exchange1: ExchangeWithStatus = {
      request_message: "First request",
      response_text: "First response",
      status: ExchangeStatus.success,
    };
    const exchange2: ExchangeWithStatus = {
      request_message: "Second request",
      response_text: "Second response",
      status: ExchangeStatus.success,
    };
    /* eslint-enable @typescript-eslint/naming-convention */

    const group: GroupedChatItem = [
      { turn: exchange1, idx: 0 },
      { turn: exchange2, idx: 1 },
    ];
    expect(getAgentResponseFromGroup(group)).toBe("First response\nSecond response");
  });

  test("filters out empty response texts", () => {
    /* eslint-disable @typescript-eslint/naming-convention */
    const exchangeWithResponse: ExchangeWithStatus = {
      request_message: "Request with response",
      response_text: "Valid response",
      status: ExchangeStatus.success,
    };
    const exchangeWithEmptyResponse: ExchangeWithStatus = {
      request_message: "Request without response",
      response_text: "",
      status: ExchangeStatus.success,
    };
    const exchangeWithNullResponse: ExchangeWithStatus = {
      request_message: "Request with null response",
      response_text: undefined,
      status: ExchangeStatus.success,
    };
    /* eslint-enable @typescript-eslint/naming-convention */

    const group: GroupedChatItem = [
      { turn: exchangeWithResponse, idx: 0 },
      { turn: exchangeWithEmptyResponse, idx: 1 },
      { turn: exchangeWithNullResponse, idx: 2 },
    ];
    expect(getAgentResponseFromGroup(group)).toBe("Valid response");
  });

  test("filters out non-ExchangeWithStatus items", () => {
    /* eslint-disable @typescript-eslint/naming-convention */
    const exchange: ExchangeWithStatus = {
      request_message: "Valid request",
      response_text: "Valid response",
      status: ExchangeStatus.success,
    };
    const pointer: ExchangePointer = {
      chatItemType: ChatItemType.exchangePointer,
      exchangeUuid: "test-uuid",
    };
    /* eslint-enable @typescript-eslint/naming-convention */

    const group: GroupedChatItem = [
      { turn: exchange, idx: 0 },
      { turn: pointer, idx: 1 },
    ];
    expect(getAgentResponseFromGroup(group)).toBe("Valid response");
  });

  test("returns undefined when group contains only non-ExchangeWithStatus items", () => {
    const pointer1: ExchangePointer = {
      chatItemType: ChatItemType.exchangePointer,
      exchangeUuid: "test-uuid-1",
    };
    const pointer2: ExchangePointer = {
      chatItemType: ChatItemType.exchangePointer,
      exchangeUuid: "test-uuid-2",
    };

    const group: GroupedChatItem = [
      { turn: pointer1, idx: 0 },
      { turn: pointer2, idx: 1 },
    ];
    expect(getAgentResponseFromGroup(group)).toBeUndefined();
  });

  test("handles mixed status ExchangeWithStatus items", () => {
    /* eslint-disable @typescript-eslint/naming-convention */
    const successExchange: ExchangeWithStatus = {
      request_message: "Success request",
      response_text: "Success response",
      status: ExchangeStatus.success,
    };
    const failedExchange: ExchangeWithStatus = {
      request_message: "Failed request",
      response_text: "Failed response",
      status: ExchangeStatus.failed,
    };
    const sentExchange: ExchangeWithStatus = {
      request_message: "Sent request",
      response_text: "Sent response",
      status: ExchangeStatus.sent,
    };
    /* eslint-enable @typescript-eslint/naming-convention */

    const group: GroupedChatItem = [
      { turn: successExchange, idx: 0 },
      { turn: failedExchange, idx: 1 },
      { turn: sentExchange, idx: 2 },
    ];
    expect(getAgentResponseFromGroup(group)).toBe(
      "Success response\nFailed response\nSent response",
    );
  });
});

describe("shouldShowFooter", () => {
  const createExchange = (requestId: string, status: ExchangeStatus): ExchangeWithStatus => ({
    /* eslint-disable @typescript-eslint/naming-convention */
    request_id: requestId,
    request_message: "Test message",
    status,
    /* eslint-enable @typescript-eslint/naming-convention */
  });
  const createGroup = (exchanges: ExchangeWithStatus[]): GroupedChatItem =>
    exchanges.map((exchange, idx) => ({ turn: exchange, idx }));
  test("returns true when group is terminal and exchange is the last valid turn", () => {
    const exchange = createExchange("test-123", ExchangeStatus.success);
    const group = createGroup([
      createExchange(`${TEMP_EXHANGE_ID_PREFIX}-456`, ExchangeStatus.draft), // temporary, should be ignored
      exchange,
    ]);
    expect(shouldShowFooter(group, exchange)).toBe(true);
  });
  test("returns false when group is not terminal (draft status)", () => {
    const exchange = createExchange("test-123", ExchangeStatus.draft);
    const group = createGroup([exchange]);
    expect(shouldShowFooter(group, exchange)).toBe(false);
  });
  test("returns false when group is not terminal (sent status)", () => {
    const exchange = createExchange("test-123", ExchangeStatus.sent);
    const group = createGroup([exchange]);
    expect(shouldShowFooter(group, exchange)).toBe(false);
  });
  test("returns true when group has failed status", () => {
    const exchange = createExchange("test-123", ExchangeStatus.failed);
    const group = createGroup([exchange]);
    expect(shouldShowFooter(group, exchange)).toBe(true);
  });
  test("returns true when group has cancelled status", () => {
    const exchange = createExchange("test-123", ExchangeStatus.cancelled);
    const group = createGroup([exchange]);
    expect(shouldShowFooter(group, exchange)).toBe(true);
  });
  test("returns false when exchange is not the last valid turn", () => {
    const exchange1 = createExchange("test-123", ExchangeStatus.success);
    const exchange2 = createExchange("test-456", ExchangeStatus.success);
    const group = createGroup([exchange1, exchange2]);
    expect(shouldShowFooter(group, exchange1)).toBe(false);
  });
  test("returns false when group is undefined", () => {
    const exchange = createExchange("test-123", ExchangeStatus.success);
    expect(shouldShowFooter(undefined, exchange)).toBe(false);
  });
  test("ignores temporary frontend IDs when finding last valid turn", () => {
    const exchange = createExchange("test-123", ExchangeStatus.success);
    const tempExchange = createExchange(`${TEMP_EXHANGE_ID_PREFIX}-456`, ExchangeStatus.success);
    const group = createGroup([exchange, tempExchange]);
    expect(shouldShowFooter(group, exchange)).toBe(true);
  });
  test("returns true when exchange has tool uses and is not cancelled", () => {
    const exchange: ExchangeWithStatus = {
      /* eslint-disable @typescript-eslint/naming-convention */
      request_id: "test-123",
      request_message: "Test message",
      status: ExchangeStatus.success,
      structured_output_nodes: [
        {
          id: 1,
          type: ChatResultNodeType.TOOL_USE,
          content: "",
          tool_use: {
            tool_use_id: "tool-1",
            tool_name: "test-tool",
            input_json: "{}",
          },
        },
      ],
    };
    const group = createGroup([exchange]);
    expect(shouldShowFooter(group, exchange)).toBe(true);
  });
});
