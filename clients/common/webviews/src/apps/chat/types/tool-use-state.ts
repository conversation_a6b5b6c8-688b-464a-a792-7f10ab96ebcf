// Re-export types from the authoritative sidecar source
export {
  type Too<PERSON>U<PERSON><PERSON>tate,
  type <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ToolUsePhase,
  getTool<PERSON><PERSON><PERSON><PERSON>,
  getToolUseKeyData,
} from "@augment-internal/sidecar-libs/src/tooluse-storage/types";

/*
State transitions
-----------------
-> new:
    Initial state when tool use block is returned by the model.

new -> checkingSafety: [automatic]
    Triggered once the chat turn containing the tool use has finished streaming.
    We wait for the streaming to finish so that the chat turn ("exchange") is
    ready to be sent as a part of the chat history in a subsequent turn, which
    would typically be the tool result sent from the user to the model.

    Checking safety involves a call out to the Augment extension to determine
    if the tool is safe to run without user approval based on the tool name and input.

checkingSafety -> runnable: [automatic]
    If the safety check concluded that the tool needs user approval,
    we mark the tool as runnable.

checkingSafety -> running: [automatic]
    If the safety check concluded that the tool does not need user approval,
    we run the tool right away.

runnable -> running: [user action]
    User clicks the "Run" button to run the tool.

    This calls out to the Augment extension to run the tool.

running -> completed: [automatic] [terminal state]
    <PERSON><PERSON> has completed successfully.

running -> error: [automatic] [terminal state]
    Too<PERSON> has failed.

running -> cancelling: [user action]
    User cancels the operation by clicking "Cancel", sending an alternate message in
    the conversation (e.g., "Actually, do [something else] instead"), or switches
    conversations.

{new, checkingSafety, runnable, cancelling} -> cancelled: [automatic] [terminal state]
    Tool run has been cancelled.
 */

export function stringOrDefault(value: unknown, defaultValue: string): string {
  if (value === undefined || value === null) {
    return defaultValue;
  }
  if (typeof value === "string") {
    return value;
  }
  return defaultValue;
}

export function numberOrDefault(value: unknown, defaultValue: number): number {
  if (value === undefined || value === null) {
    return defaultValue;
  }
  if (typeof value === "number") {
    return value;
  }
  return defaultValue;
}

export function arrayOrDefault<T>(value: unknown, defaultValue: T[]): T[] {
  if (value === undefined || value === null) {
    return defaultValue;
  }
  if (Array.isArray(value)) {
    return value;
  }
  return defaultValue;
}
