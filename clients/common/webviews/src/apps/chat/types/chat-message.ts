import type {
  ExternalSource,
  WorkspaceFileChunk,
} from "$vscode/src/webview-providers/webview-messages";
import {
  ChatRequestNodeType,
  ChatResultNodeType,
  type ChatRequestNode,
  type ChatResultNode,
  type Exchange,
  type MemoriesInfo,
} from "@augment-internal/sidecar-libs/src/chat/chat-types";
import { type IQualifiedPathName } from "@augment-internal/sidecar-libs/src/workspace/workspace-types";
import type { JSONContent } from "@tiptap/core";
import type { IChatMentionable } from "./mention-option";
import { type ChatStopReason } from "@augment-internal/sidecar-libs/src/api/types";
import { type GroupedChatItem } from "../utils/message-list-context";
import { TEMP_EXHANGE_ID_PREFIX } from "../models/conversation-model";

export const signInConversationId = "augment-welcome";

export const enum ExchangeStatus {
  draft = "draft",
  sent = "sent",
  failed = "failed",
  success = "success",
  /** The user cancelled the exchange */
  cancelled = "cancelled",
}

/**
 * An agent exchange is a virtual exchange, consisting of a list of normal exchanges.
 * The delimiter between agent exchanges is an exchange with a present user message.
 * In other words, this is how it would look:
 *
 * - [1] User: "Hello"
 * - [1] Agent: "Hi there"
 * - [1] Agent: [Tool Use]
 * - [1] User: [Tool Result]
 * - [1] Agent: [Tool Use]
 * - [1] User: [Tool Result]
 * - [1] Agent: "Here are your pending changes"
 * - [2] User: "Thanks"
 * - [2] Agent: "You're welcome"
 *
 * The first [1] agent exchange is started by a user message. It is stopped when there is
 * another user message, which then creates the start of another agent exchange.
 */
export enum AgentExchangeStatus {
  running = "running",
  awaitingUserAction = "awaiting-user-action",
  notRunning = "not-running",
}

export enum SeenState {
  seen = "seen",
  unseen = "unseen",
}

export const enum ChatItemType {
  signInWelcome = "sign-in-welcome",
  generateCommitMessage = "generate-commit-message",
  summaryResponse = "summary-response",
  summaryTitle = "summary-title",
  educateFeatures = "educate-features",

  agentOnboarding = "agent-onboarding",
  agenticTurnDelimiter = "agentic-turn-delimiter",
  agenticRevertDelimiter = "agentic-revert-delimiter",
  agenticCheckpointDelimiter = "agentic-checkpoint-delimiter",
  exchange = "exchange",
  exchangePointer = "exchange-pointer",
  historySummary = "history-summary",
}

export type ChatItem = ChatItemCommon & OneOfChatItem;
export interface ChatItemCommon {
  chatItemType?: ChatItemType;
  // eslint-disable-next-line @typescript-eslint/naming-convention
  request_id?: string;
  timestamp?: string; // Timestamp in ISO format
  // eslint-disable-next-line @typescript-eslint/naming-convention
  structured_output_nodes?: ChatResultNode[];
}

export interface ExchangePointer extends ChatItemCommon {
  chatItemType: ChatItemType.exchangePointer;
  exchangeUuid: string; // Points to exchange in separate storage
  timestamp?: string; // For ordering in chat history
  // Minimal metadata for quick access without loading full exchange
  status?: ExchangeStatus;
  hasResponse?: boolean;
  isStreaming?: boolean;
}

export interface ExchangePointer extends ChatItemCommon {
  /* eslint-disable @typescript-eslint/naming-convention */
  chatItemType: ChatItemType.exchangePointer;
  exchangeUuid: string; // Points to exchange in separate storage
  timestamp?: string; // For ordering in chat history
  // Minimal metadata for quick access without loading full exchange
  request_message?: string; // For display in chat history
  status?: ExchangeStatus;
  hasResponse?: boolean;
  isStreaming?: boolean;
  seen_state?: SeenState;
  /* eslint-enable @typescript-eslint/naming-convention */
}

export type OneOfChatItem =
  | ExchangeWithStatus
  | ExchangePointer
  | SignInWelcomeMessage
  | EducateFeatures
  | GenerateCommitMessage
  | SummaryResponseMessage
  | AgenticTurnDelimiter
  | AgenticRevertDelimiter
  | AgenticCheckpointDelimiter
  | HistorySummaryMessage;

export function isChatTurn(item: ChatItem): item is ExchangeWithStatus {
  return (
    isChatItemExchangeWithStatus(item) ||
    isChatItemGenerateCommitMessage(item) ||
    isChatItemAgentOnboarding(item)
  );
}

export function isChatItemExchangeWithStatus(
  item: ChatItem | null | undefined,
): item is ExchangeWithStatus {
  if (!item) {
    return false;
  }
  return item.chatItemType === undefined || item.chatItemType === ChatItemType.agentOnboarding;
}

export function isChatItemSuccessfulExchange(
  item: ChatItem | null | undefined,
): item is ExchangeWithStatus {
  return isChatItemExchangeWithStatus(item) && item.status === ExchangeStatus.success;
}

export function isChatItemExchangePointer(
  item: ChatItem | null | undefined,
): item is ExchangePointer {
  if (!item) {
    return false;
  }
  return item.chatItemType === ChatItemType.exchangePointer;
}
export function isChatItemSignInWelcome(item: ChatItem): item is SignInWelcomeMessage {
  return item.chatItemType === ChatItemType.signInWelcome;
}

export function isChatItemGenerateCommitMessage(item: ChatItem): item is GenerateCommitMessage {
  return item.chatItemType === ChatItemType.generateCommitMessage;
}

export function isChatItemSummaryResponse(item: ChatItem): item is SummaryResponseMessage {
  return item.chatItemType === ChatItemType.summaryResponse;
}

export function isChatItemEducateFeatures(item: ChatItem): item is EducateFeatures {
  return item.chatItemType === ChatItemType.educateFeatures;
}

export function noChatItemExchangeWithStatus(items: ChatItem[]): boolean {
  return items.every((item) => !isChatItemExchangeWithStatus(item));
}

export function isChatItemAgentOnboarding(item: ChatItem): item is AgentOnboardingMessage {
  return item.chatItemType === ChatItemType.agentOnboarding;
}

export function isChatItemAgenticTurnDelimiter(item: ChatItem): item is AgenticTurnDelimiter {
  return item.chatItemType === ChatItemType.agenticTurnDelimiter;
}

export function isChatItemAgenticCheckpointDelimiter(
  item: ChatItem,
): item is AgenticCheckpointDelimiter {
  return item.chatItemType === ChatItemType.agenticCheckpointDelimiter;
}

export function isChatItemHistorySummary(item: ChatItem): item is HistorySummaryMessage {
  return item.chatItemType === ChatItemType.historySummary;
}

export type ExchangeWithStatus = ChatItemCommon &
  IChatItemSeenState &
  IChatItemResponseMessage & {
    chatItemType?: ChatItemType;
    /* eslint-disable @typescript-eslint/naming-convention */
    request_message: string;
    request_id?: string;
    timestamp?: string; // Timestamp in ISO format
    rich_text_json_repr?: JSONContent | JSONContent[];
    workspace_file_chunks?: WorkspaceFileChunk[];
    external_sources?: ExternalSource[];
    mentioned_items?: IChatMentionable[];
    structured_output_nodes?: ChatResultNode[];
    model_id?: string | undefined;
    disableRetrieval?: boolean;
    disableSelectedCodeDetails?: boolean;
    response_text?: string;
    hidden?: boolean;
    lastChunkId?: number;
    /**
     * Override the exchange's history. If empty array, there is no chat history.
     * If undefined, use the original chat history. */
    chatHistory?: Exchange[];
    structured_request_nodes?: ChatRequestNode[];
    memoriesInfo?: MemoriesInfo;
    stop_reason?: ChatStopReason;
    /* eslint-enable @typescript-eslint/naming-convention */
  };

export type SignInWelcomeMessage = ChatItemCommon &
  IChatItemSeenState &
  IChatItemResponseMessage & {
    chatItemType: ChatItemType.signInWelcome;
  };

export type GenerateCommitMessage = ExchangeWithStatus & {
  chatItemType: ChatItemType.generateCommitMessage;
};

export type SummaryResponseMessage = ExchangeWithStatus & {
  chatItemType: ChatItemType.summaryResponse;
  questions?: string[];
  questionsLoaded?: boolean;
};

export type EducateFeatures = ChatItemCommon &
  IChatItemSeenState & {
    chatItemType: ChatItemType.educateFeatures;
  };

export type AgentOnboardingMessage = ExchangeWithStatus & {
  chatItemType: ChatItemType.agentOnboarding;
};

export type AgenticTurnDelimiter = ChatItemCommon & {
  chatItemType: ChatItemType.agenticTurnDelimiter;
};

export type AgenticRevertDelimiter = ChatItemCommon & {
  chatItemType: ChatItemType.agenticRevertDelimiter;
  status: ExchangeStatus;
  revertTarget: number;
};

export type AgenticRevertTargetInfo = {
  uuid?: string; // The UUID of the checkpoint that was reverted to
  filePath?: IQualifiedPathName; // The file path of the checkpoint that was reverted to
};

export type AgenticCheckpointDelimiter = ChatItemCommon & {
  uuid: string;
  chatItemType: ChatItemType.agenticCheckpointDelimiter;

  /**
   * The status of the checkpoint. States have the following semantic meaning:
   * - Draft: the "current" checkpoint, but it is not yet required to track any changes.
   *     This is the state before a user message is sent and there is work to track.
   *     Should not be rendered.
   * - Sent: the "current" checkpoint, and is actively tracking changes. This is the
   *     state after a user message is sent. Should not be rendered, but should actively
   *     affect checkpoint tracking state. All current changes in the current agentic turn
   *     will coalesce into this checkpoint.
   * - Success: the checkpoint is completed and is no longer the "current" checkpoint.
   *      When an agentic turn finishes (either through success or cancellation) and
   *     there are changes tracked in this checkpoint, it will be added to the conversation
   *     history. Should be rendered.
   */
  status: ExchangeStatus;

  // If this checkpoint is the result of a revert, this is the UUID of the checkpoint that
  // was reverted to. If we cannot find this in the list, we will not show metadata for it.
  revertTarget?: AgenticRevertTargetInfo;

  // The timestamp range for this checkpoint
  fromTimestamp: number;
  toTimestamp: number;
};

export type HistorySummaryMessage = ExchangeWithStatus & {
  chatItemType: ChatItemType.historySummary;
  // The version of the summary. This is used to determine if we need to update
  // the summary.
  summaryVersion?: number;
};

export function isCheckpointRevert(
  checkpoint: AgenticCheckpointDelimiter,
): checkpoint is AgenticCheckpointDelimiter & { revertTarget: AgenticRevertTargetInfo } {
  return checkpoint.revertTarget !== undefined;
}

export interface IChatItemSeenState {
  // eslint-disable-next-line @typescript-eslint/naming-convention
  request_id?: string;
  // eslint-disable-next-line @typescript-eslint/naming-convention
  seen_state?: SeenState;
}

export function shouldShowFooter(
  group: GroupedChatItem | undefined,
  exchange: ExchangeWithStatus,
): boolean {
  const lastChatTurnInGroup = lastChatTurn(group);
  // Show footer only if the group is at terminal state so that we don't show and hide the footer
  // as the group is being updated.
  const isGroupTerminal =
    lastChatTurnInGroup?.status === ExchangeStatus.success ||
    lastChatTurnInGroup?.status === ExchangeStatus.failed ||
    lastChatTurnInGroup?.status === ExchangeStatus.cancelled;

  if (!isGroupTerminal) {
    return false;
  }

  const lastValidTurn = lastValidTurnInGroup(group);
  return lastValidTurn?.turn.request_id === exchange.request_id;
}

function lastChatTurn(group: GroupedChatItem | undefined) {
  if (!group) {
    return undefined;
  }
  const foundTurn = group.findLast((groupTurn) => isChatTurn(groupTurn.turn));
  return foundTurn ? (foundTurn.turn as ExchangeWithStatus) : undefined;
}

/**
  Identifies the last turn in the group that is a "valid" turn.
  A valid turn is classified as a turn that is displayed in the message list and not a temporary id
 */
function lastValidTurnInGroup(group: GroupedChatItem | undefined) {
  const lastValidTurn = group
    ? group.findLast((groupTurn) => {
        return (
          !groupTurn.turn.request_id?.startsWith(TEMP_EXHANGE_ID_PREFIX) &&
          isChatTurn(groupTurn.turn)
        );
      })
    : undefined;
  return lastValidTurn;
}

export function hasToolUse(exchange: ExchangeWithStatus): boolean {
  return (
    exchange.structured_output_nodes?.some((node) => node.type === ChatResultNodeType.TOOL_USE) ??
    false
  );
}

export function hasToolResult(exchange: ExchangeWithStatus): boolean {
  return (
    exchange.structured_request_nodes?.some(
      (node) => node.type === ChatRequestNodeType.TOOL_RESULT,
    ) ?? false
  );
}

export function isIChatItemSeenState(value: unknown): value is IChatItemSeenState {
  if (!value || typeof value !== "object") {
    return false;
  }
  if ("request_id" in value && typeof value.request_id !== "string") {
    return false;
  }
  if (
    "seen_state" in value &&
    !(value.seen_state === SeenState.seen || value.seen_state === SeenState.unseen)
  ) {
    return false;
  }
  return true;
}

export function isTurnCompleted(turn?: { status: ExchangeStatus }) {
  return (
    turn?.status === ExchangeStatus.success ||
    turn?.status === ExchangeStatus.failed ||
    turn?.status === ExchangeStatus.cancelled
  );
}

export interface IChatItemResponseMessage {
  status: ExchangeStatus;
  // eslint-disable-next-line @typescript-eslint/naming-convention
  response_text?: string;
  // eslint-disable-next-line @typescript-eslint/naming-convention
  display_error_message?: string;
  isRetriable?: boolean;
  shouldBackoff?: boolean;
}

// Helper function to check if a turn has response_text property and get it safely
function getResponseText(turn: ChatItem): string {
  return "response_text" in turn ? (turn.response_text ?? "") : "";
}

export function getAgentResponseFromGroup(group: GroupedChatItem | undefined): string | undefined {
  if (!group) {
    return undefined;
  }

  const responseTexts = group
    .filter((item) => isChatItemExchangeWithStatus(item.turn))
    .map((item) => getResponseText(item.turn))
    .filter((text) => text.length > 0);

  return responseTexts.length > 0 ? responseTexts.join("\n") : undefined;
}

export function flattenGroupIntoNodes(group: GroupedChatItem | undefined): ChatResultNode[] {
  let nodes: ChatResultNode[] = [];
  if (!group) {
    return nodes;
  }
  for (const item of group) {
    const chatItem = item.turn;
    if (isChatItemExchangeWithStatus(chatItem) && chatItem.structured_output_nodes) {
      nodes = nodes.concat(chatItem.structured_output_nodes);
    }
  }
  return nodes;
}

export function countUniquePaths(nodes: ChatResultNode[]): number {
  let uniquePaths = new Set();

  for (const node of nodes) {
    if (node.type === ChatResultNodeType.TOOL_USE && node.tool_use?.input_json) {
      // Parse out the path and ignore if not formatted properly
      try {
        const path = JSON.parse(node.tool_use.input_json).path;
        uniquePaths.add(path);
      } catch (e) {
        console.error("Failed to parse tool input JSON:", e);
      }
    }
  }

  return uniquePaths.size;
}

export function isMemoryNode(node: ChatResultNode): boolean {
  return (
    node.type === ChatResultNodeType.AGENT_MEMORY ||
    (node.type === ChatResultNodeType.TOOL_USE && node.tool_use?.tool_name === "remember")
  );
}

export function isToolUseNode(node: ChatResultNode): boolean {
  return node.type === ChatResultNodeType.TOOL_USE;
}

export function isViewToolNode(node: ChatResultNode): boolean {
  return node.type === ChatResultNodeType.TOOL_USE && node.tool_use?.tool_name === "view";
}

export function isStrReplaceToolNode(node: ChatResultNode): boolean {
  return (
    node.type === ChatResultNodeType.TOOL_USE && node.tool_use?.tool_name === "str-replace-editor"
  );
}
