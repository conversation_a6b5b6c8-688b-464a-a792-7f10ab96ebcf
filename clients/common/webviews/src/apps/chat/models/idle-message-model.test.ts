import { get } from "svelte/store";
import {
  IdleMessageModel,
  DEFAULT_IDLE_MESSAGE_TIMEOUT_MS,
  DEFAULT_IDLE_NOTIFICATION_TIMEOUT_MS,
} from "./idle-message-model";
import { vi, describe, test, expect, beforeEach, afterEach } from "vitest";

describe("IdleMessageModel", () => {
  let model: IdleMessageModel;

  beforeEach(() => {
    vi.useFakeTimers();
    model = new IdleMessageModel();
  });

  afterEach(() => {
    vi.clearAllTimers();
    vi.useRealTimers();
    model.dispose();
  });

  test("initializes with default busy state", () => {
    expect(get(model.idleStatus)).toBe("busy");
    expect(get(model.focusAfterIdle)).toBe(false);
  });

  test("sets idle notification timeout", () => {
    model.idleNotifyTimeout = DEFAULT_IDLE_NOTIFICATION_TIMEOUT_MS;
    expect(model.idleNotifyTimeout).toBe(DEFAULT_IDLE_NOTIFICATION_TIMEOUT_MS);
    expect(model.notifyEnabled).toBe(true);
  });

  test("sets idle message timeout", () => {
    model.idleMessageTimeout = DEFAULT_IDLE_MESSAGE_TIMEOUT_MS;
    expect(model.idleMessageTimeout).toBe(DEFAULT_IDLE_MESSAGE_TIMEOUT_MS);
    expect(model.messageEnabled).toBe(true);
  });

  test("transitions to idle-notify state after notify timeout", () => {
    model.idleNotifyTimeout = 1000; // 1 second
    expect(get(model.idleStatus)).toBe("busy");

    vi.advanceTimersByTime(1000);
    expect(get(model.idleStatus)).toBe("idle-notify");
  });

  test("transitions to idle-message state after message timeout", () => {
    model.idleMessageTimeout = 1000; // 1 second
    expect(get(model.idleStatus)).toBe("busy");

    vi.advanceTimersByTime(1000);
    expect(get(model.idleStatus)).toBe("idle-message");
  });

  test("activity resets to busy state and reschedules timeouts", () => {
    model.idleNotifyTimeout = 1000;
    model.idleMessageTimeout = 2000;

    vi.advanceTimersByTime(500);
    model.activity();
    expect(get(model.idleStatus)).toBe("busy");

    vi.advanceTimersByTime(500);
    expect(get(model.idleStatus)).toBe("busy");

    vi.advanceTimersByTime(500);
    expect(get(model.idleStatus)).toBe("idle-notify");
  });

  test("focus updates focusAfterIdle state", () => {
    expect(get(model.focusAfterIdle)).toBe(false);

    model.focus(true);
    expect(get(model.focusAfterIdle)).toBe(true);

    model.focus(false);
    expect(get(model.focusAfterIdle)).toBe(false);
  });

  test("dispose cleans up state and subscriptions", () => {
    model.idleNotifyTimeout = 1000;
    model.idleMessageTimeout = 2000;
    model.focus(true);

    model.dispose();

    expect(get(model.idleStatus)).toBe("busy");
    expect(get(model.focusAfterIdle)).toBe(false);

    vi.advanceTimersByTime(2000);
    expect(get(model.idleStatus)).toBe("busy"); // Should remain busy as timers are cleaned up
  });

  test("changing timeouts updates existing schedules", () => {
    model.idleNotifyTimeout = 1000;
    vi.advanceTimersByTime(500);

    model.idleNotifyTimeout = 2000; // Reset timeout
    vi.advanceTimersByTime(1000);
    expect(get(model.idleStatus)).toBe("busy"); // Should not transition yet

    vi.advanceTimersByTime(1000);
    expect(get(model.idleStatus)).toBe("idle-notify"); // Should transition after new timeout
  });
});
