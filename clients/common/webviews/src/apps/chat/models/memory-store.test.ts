import { memoryStore } from "./memory-store";
import { describe, test, expect, beforeEach, afterEach, vi } from "vitest";

// Mock setTimeout and clearTimeout
vi.useFakeTimers();

// Create a helper function to reset the memory store state
function resetMemoryStore() {
  // Clear any existing notifications by advancing past the clear delay
  vi.advanceTimersByTime(20000);

  // Force the memory store to have no active timeout
  // This is a hack to reset the internal state of the memory store
  // We need to do this because the memory store is a singleton
  if (memoryStore["clearTimeout"]) {
    clearTimeout(memoryStore["clearTimeout"]);
    memoryStore["clearTimeout"] = undefined;
  }
}

describe("MemoryStore", () => {
  let callback: ReturnType<typeof vi.fn>;
  let unsubscribe: () => void;

  beforeEach(() => {
    // Reset the memory store state
    resetMemoryStore();

    // Create a mock callback for subscription
    callback = vi.fn();

    // Subscribe to memory updates
    unsubscribe = memoryStore.subscribe(callback);

    // Clear initial call
    callback.mockClear();
  });

  afterEach(() => {
    // Unsubscribe to prevent memory leaks
    unsubscribe();

    // Clear all timers
    vi.clearAllTimers();
  });

  test("notifyMemoryUpdated sets a notification with current timestamp", () => {
    // Mock Date.now to return a fixed timestamp
    const mockTimestamp = 1234567890;
    vi.spyOn(Date, "now").mockReturnValue(mockTimestamp);

    // Notify of memory update
    memoryStore.notifyMemoryUpdated();

    // Verify callback was called with the notification
    expect(callback).toHaveBeenCalledTimes(1);
    expect(callback).toHaveBeenCalledWith({
      timestamp: mockTimestamp,
    });

    // Restore Date.now
    vi.spyOn(Date, "now").mockRestore();
  });

  test("notification is cleared after the specified delay", () => {
    // Reset the memory store state
    resetMemoryStore();
    callback.mockClear();

    // Notify of memory update
    memoryStore.notifyMemoryUpdated();

    // Verify callback was called with the notification
    expect(callback).toHaveBeenCalledTimes(1);
    expect(callback.mock.calls[0][0]).not.toBeNull();

    // Reset mock to track next call
    callback.mockClear();

    // Advance timers by less than the clear delay
    vi.advanceTimersByTime(5000);

    // Verify callback was not called again
    expect(callback).not.toHaveBeenCalled();

    // Advance timers past the clear delay
    vi.advanceTimersByTime(5001);

    // Verify callback was called with null
    expect(callback).toHaveBeenCalledTimes(1);
    expect(callback).toHaveBeenCalledWith(null);
  });

  test("subsequent notifications are ignored if timeout is active", () => {
    // Reset the memory store state
    resetMemoryStore();
    callback.mockClear();

    // Notify of first memory update
    memoryStore.notifyMemoryUpdated();

    // Verify callback was called with the notification
    expect(callback).toHaveBeenCalledTimes(1);
    expect(callback.mock.calls[0][0]).not.toBeNull();

    // Reset mock to track next call
    callback.mockClear();

    // Advance timers by half the clear delay
    vi.advanceTimersByTime(5000);

    // Mock Date.now to return a different timestamp
    const mockTimestamp = Date.now() + 1000;
    vi.spyOn(Date, "now").mockReturnValue(mockTimestamp);

    // Notify of second memory update - should be ignored due to active timeout
    memoryStore.notifyMemoryUpdated();

    // Verify callback was NOT called (second notification ignored)
    expect(callback).not.toHaveBeenCalled();

    // Advance timers past the full clear delay
    vi.advanceTimersByTime(5001);

    // Verify callback was called with null (from the first notification's timeout)
    expect(callback).toHaveBeenCalledTimes(1);
    expect(callback).toHaveBeenCalledWith(null);

    // Restore Date.now
    vi.spyOn(Date, "now").mockRestore();
  });

  test("subscribe returns an unsubscribe function", () => {
    // Reset the memory store state
    resetMemoryStore();

    // Create a new callback
    const newCallback = vi.fn();

    // Subscribe to memory updates
    const newUnsubscribe = memoryStore.subscribe(newCallback);

    // Initial call should happen with current value (null in this case)
    expect(newCallback).toHaveBeenCalledWith(null);
    newCallback.mockClear();

    // Notify of memory update
    memoryStore.notifyMemoryUpdated();

    // Verify callback was called
    expect(newCallback).toHaveBeenCalledTimes(1);
    expect(newCallback.mock.calls[0][0]).not.toBeNull();

    // Reset mock
    newCallback.mockClear();

    // Unsubscribe
    newUnsubscribe();

    // Notify of another memory update
    memoryStore.notifyMemoryUpdated();

    // Verify callback was not called after unsubscribing
    expect(newCallback).not.toHaveBeenCalled();
  });
});
