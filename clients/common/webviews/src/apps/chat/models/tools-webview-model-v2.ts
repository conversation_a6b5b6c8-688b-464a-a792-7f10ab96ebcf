import { ToolUsePhase } from "../types/tool-use-state";
import { type ConversationModel } from "./conversation-model";
import { type IExtensionClient } from "../extension-client";
import {
  ChatItemType,
  ExchangeStatus,
  type ExchangeWithStatus,
  hasToolUse,
  isChatItemAgenticTurnDelimiter,
  isChatItemSuccessfulExchange,
} from "../types/chat-message";
import {
  ChatResultNodeType,
  ChatRequestNodeType,
  type Exchange,
  type ChatRequestToolResult,
  type ChatResultNode,
} from "@augment-internal/sidecar-libs/src/chat/chat-types";
import { ToolHostName } from "@augment-internal/sidecar-libs/src/tools/tool-types";
import { type IDisposable } from "monaco-editor";
import { formatHistory } from "./conversation-model";
import { type ChatModel } from "./chat-model";
import { get } from "svelte/store";
import { cancelLastRunnableToolUse } from "./agent-conversation-utils";
import {
  generateToolResultNodeFromState,
  toolOrToolStart,
  clipResultIfLarge,
  onlyDisplayableToolNodes,
} from "../utils/tool-utils";
import { processToolResultImages } from "../components/conversation/blocks/tools/components/sidecar/image-scaling-util";
import type { RemoteAgentsModel } from "../../remote-agent-manager/models/remote-agents-model";
import { ChatStopReason } from "@augment-internal/sidecar-libs/src/api/types";
import { retryPrompt, MAX_TOKEN_RETRY_ATTEMPTS } from "./retry-prompts";
import { type CheckToolCallSafeRequestData } from "@augment-internal/sidecar-libs/src/webview-messages/message-types/tool-messages";
import { type SoundModel } from "../../settings/models/sound-model";

/**
 * Manages the lifecycle and state of tool executions in the chat webview.
 *
 * This class is responsible for:
 * - Processing tool use requests from the conversation model
 * - Managing the safety checks and approval flow for tool executions
 * - Handling tool execution state transitions (new → checking safety → running/runnable → completed/error)
 * - Cancelling and terminating tool executions when needed (e.g., conversation switches)
 * - Tracking files that have been modified by tool executions
 *
 * The class maintains an active tool state and ensures only one tool can be active at a time.
 * It also enforces size limits on tool results to prevent oversized responses from overwhelming
 * the UI or context window.
 *
 * Tool execution follows this general flow:
 * 1. New tool use detected in conversation
 * 2. Safety check performed
 * 3. If safe, execute immediately; if unsafe, mark as requiring approval
 * 4. On completion/error, update conversation state and send result
 *
 * @implements {IDisposable} - Cleanup of subscriptions and resources
 * @implements {MessageConsumer} - Handles messages from the extension
 */
export class ToolsWebviewModelV2 implements IDisposable {
  private _disposables: IDisposable[] = [];

  /**
   * Maximum allowed size of the result text in bytes.
   */
  private static readonly maxResultBytes = 64 * 1024; // 64KiB
  /**
   * Maximum allowed size of the result text in bytes for all tools in a single request.
   */
  private static readonly maxAllResultBytes = 2 * ToolsWebviewModelV2.maxResultBytes;

  constructor(
    private _conversationModel: ConversationModel,
    private _extensionClient: IExtensionClient,
    private _chatModel: ChatModel,
    private _soundModel: SoundModel,
    private _remoteAgentsModel?: RemoteAgentsModel,
  ) {
    // Update the tool use state when the last exchange changes.
    const unsub = this._conversationModel.subscribe((updatedModel: ConversationModel) => {
      const lastExchange = updatedModel.lastExchange;
      if (lastExchange) {
        const requestId = lastExchange.request_id;
        this._processLastExchange(requestId!);
      }
    });

    // Cancel any tool runs when switching conversations.
    const unsubOnNewConversation = this._conversationModel.onNewConversation(() => {
      void this._extensionClient.closeAllToolProcesses();
      this._enteredNewConversation = true;
      // Clear sent exchanges tracking when switching conversations
      this._sentExchanges.clear();
    });

    const unsubHistoryDelete = this._conversationModel.onHistoryDelete(() => {
      void this.cancelActiveToolRun();
    });

    const unsubOnSendExchange = this._conversationModel.onSendExchange(() => {
      this._enteredNewConversation = false;
    });

    this._disposables.push(
      { dispose: unsub },
      { dispose: unsubOnNewConversation },
      { dispose: unsubHistoryDelete },
      { dispose: unsubOnSendExchange },
    );
  }

  private _activeTool: { id: string; requestId: string; phase: ToolUsePhase } | undefined =
    undefined;
  private _activelyProcessingExchanges = new Set<string>();
  private _queuedProcessingRequests = new Set<string>();
  private _enteredNewConversation = true;
  private _sentExchanges = new Set<string>();

  /**
   * Process the most recent exchange, updating the tool use state if necessary.
   * Algorithm:
   *
   * 1. Check if processing is already active for this requestId. If so, queue a subsequent run.
   * 2. Start iterating through each tool use. For each tool use,
   *  a. Validate that it makes sense to run this tool, still. If the conversation has moved on, or the conversation has changed,
   *     then we should not run the tool.
   *  b. If the tool use is new, validate it exists and check if it is safe to run without user
   *     approval. This will trigger the next phase: either running the tool or marking it
   *     as runnable, giving the user the opportunity to approve (and exiting hence).
   *  c. If the tool is in an active state but there is no ongoing operation, mark it as
   *     cancelled. This happens on reloads or restarts.
   *
   * The goal is that as long as there are new tools streaming in, we will ensure that this function is called at least once.
   * Additionally, for any events that are user-driven (permission, cancellation etc.), we will ensure that this function is called to continue processing the exchange.
   */
  private _processLastExchange = async (requestId: string) => {
    // If already processing this requestId, enqueue a subsequent run.
    // This is to make sure that we don't miss any updates that will likely
    // happen as a result of changes during an existing run.
    if (this._activelyProcessingExchanges.has(requestId)) {
      this._queuedProcessingRequests.add(requestId);
      return;
    }

    // Start processing and continue until no more queued requests
    do {
      this._queuedProcessingRequests.delete(requestId);
      this._activelyProcessingExchanges.add(requestId);

      try {
        await this._processLastExchangeInternal(requestId);
      } finally {
        this._activelyProcessingExchanges.delete(requestId);
      }
    } while (this._queuedProcessingRequests.has(requestId));
  };

  /**
   * The main workhorse that actually processes tool executions for an exchange. This is where all the
   * real logic lives - it's basically a big state machine that figures out what to do with each tool
   * in the current exchange and makes sure everything gets executed in the right order.
   *
   * Here's what it does: First, it validates the exchange is still valid and handles edge cases like
   * hitting token limits. Then it walks through all the tool use nodes to find the next one that needs
   * processing, or figures out if we're blocked waiting for something. It also has to deal with resource
   * limits - if we're about to blow past the max result size, it'll cut off the remaining tools. For
   * each tool it processes, it runs through validation, safety checks, and actual execution.
   *
   * Other stuff it handles: When you switch conversations mid-execution, it marks tools as runnable
   * instead of just auto-running them (which would be annoying). It cleans up stale tool states that
   * get left behind after restarts. And it makes sure we don't send the exchange back until every
   * single tool has finished - either successfully, with an error, or cancelled.
   */
  private _processLastExchangeInternal = async (requestId: string) => {
    let exchange = this._conversationModel.lastExchange;
    if (exchange == null || exchange.request_id !== requestId) {
      return;
    }

    let currToolUseNodes =
      this._conversationModel.lastExchange?.structured_output_nodes?.filter(
        (node) => node.type === ChatResultNodeType.TOOL_USE,
      ) ?? [];
    let latestRequestId = this._conversationModel.lastExchange?.request_id;

    // Check for MAX_TOKENS stop reason and handle retry logic
    if (exchange.stop_reason === ChatStopReason.MAX_TOKENS) {
      // In the event that there's a tool use start, drop into the handleMaxTokensRetry flow.
      // Else if there is at least one tool use, we want to just send the next exchange, since we likely failed
      // mid creation of a tool block.
      if (this._handleMaxTokensRetry(requestId, exchange)) {
        return;
      }
    }

    // Get the next tool use that we haven't processed yet (in the new phase). Returns undefined if blocked
    // by an active tool/awaiting permission/other intermediate states.
    const nextToolUseNode = this._tryFindNextToolUseNodeToRun(currToolUseNodes, requestId);

    // If running one more tool after this puts us at risk of exceeding the max result bytes,
    // mark remaining tools as omitted and send the exchange.
    const toolIdxToRemove = this._mustRemoveToolsForMaxBytes(requestId, currToolUseNodes);
    if (toolIdxToRemove !== undefined) {
      this._removeRemainingTools(requestId, currToolUseNodes, toolIdxToRemove);

      // Check if we can now send the exchange
      if (this._shouldSendToolResultExchange(requestId)) {
        this._sendToolResultExchange(requestId);
      }
      return;
    }

    // Attempt to run the next tool use. If it fails for any reason (fail to run, wait for permission, etc.), exit.
    if (nextToolUseNode !== undefined && requestId === latestRequestId) {
      const toolUseContent = nextToolUseNode.tool_use;
      if (toolUseContent !== undefined && toolUseContent !== null) {
        // If we just switched into a conversation after a restart, or swapping nodes, and we're in the middle
        // of running tools. Let's not automatically just start running tools. Instead, let's mark the next tool as
        // runnable and let the user decide whether to continue or not.
        if (this._enteredNewConversation) {
          this._conversationModel.updateToolUseState({
            requestId,
            toolUseId: toolUseContent.tool_use_id,
            phase: ToolUsePhase.runnable,
          });
          this._enteredNewConversation = false;
          return;
        }

        if (
          !(await this._validateAndCheckTool(
            requestId,
            toolUseContent.tool_use_id,
            toolUseContent.tool_name,
            JSON.parse(toolUseContent.input_json),
            this._conversationModel.chatHistory.reduce((ret, m) => {
              if (isChatItemSuccessfulExchange(m)) {
                ret.push(formatHistory(m));
              }
              return ret;
            }, [] as Exchange[]),
          ))
        ) {
          return;
        }
      }
    }

    // If there are any tools in an active state but there is no ongoing operation, mark it as
    // cancelled. This happens on reloads or restarts.
    for (const toolUseNode of onlyDisplayableToolNodes(
      requestId,
      currToolUseNodes,
      this._conversationModel,
      this._remoteAgentsModel,
    )) {
      const toolUseContent = toolUseNode.tool_use;
      if (toolUseContent === undefined || toolUseContent === null) {
        continue;
      }
      const toolUseState = this._conversationModel.getToolUseState(
        requestId,
        toolUseContent.tool_use_id,
      );
      this._markCancelledIfStale(requestId, toolUseContent.tool_use_id, toolUseState.phase);
    }

    // Check if the conditions for sending the exchange have been met, and then send it.
    // The exchange must be successful and all tool calls must have resolved to cancelled or succeeded.
    if (this._shouldSendToolResultExchange(requestId)) {
      this._sendToolResultExchange(requestId);
    }
  };

  /**
   * Find the next tool use node to process.
   * Returns the next tool use in 'new' phase, or undefined if blocked by an active tool.
   */
  private _tryFindNextToolUseNodeToRun(
    currToolUseNodes: ChatResultNode[],
    requestId: string,
  ): ChatResultNode | undefined {
    for (const toolUseNode of currToolUseNodes) {
      const toolUseContent = toolUseNode.tool_use;
      if (toolUseContent === undefined || toolUseContent === null) {
        continue;
      }
      const toolUseState = this._conversationModel.getToolUseState(
        requestId,
        toolUseContent.tool_use_id,
      );
      if (toolUseState.phase === ToolUsePhase.new) {
        return toolUseNode;
      } else if (
        toolUseState.phase === ToolUsePhase.runnable ||
        toolUseState.phase === ToolUsePhase.checkingSafety ||
        toolUseState.phase === ToolUsePhase.cancelling ||
        toolUseState.phase === ToolUsePhase.running
      ) {
        return undefined;
      }
    }
    return undefined;
  }

  /**
   * Check if the conditions for sending the exchange have been met.
   * The exchange must be successful and all tool calls must have resolved to cancelled or succeeded.
   * Also ensures that the exchange hasn't already been sent to prevent duplicate sends.
   */
  private _shouldSendToolResultExchange(requestId: string): boolean {
    const hasSentExchange = this._sentExchanges.has(requestId);
    const isSuccessfulExchange = isChatItemSuccessfulExchange(this._conversationModel.lastExchange);
    const noActiveTool = this._activeTool === undefined;
    const exchangeHasTools =
      this._conversationModel.lastExchange !== null &&
      hasToolUse(this._conversationModel.lastExchange);
    const allToolsResolved = this.allToolUsesResolved(requestId);

    return (
      !hasSentExchange &&
      isSuccessfulExchange &&
      noActiveTool &&
      exchangeHasTools &&
      allToolsResolved
    );
  }

  // Check whether all of the tool uses have resolved to either completed, cancelled, or error.
  public allToolUsesResolved(requestId: string): boolean {
    let currToolUseNodes =
      this._conversationModel.lastExchange?.structured_output_nodes?.filter(
        (node) => node.type === ChatResultNodeType.TOOL_USE,
      ) ?? [];
    for (const toolUseNode of currToolUseNodes) {
      const toolUseContent = toolUseNode.tool_use;
      if (toolUseContent === undefined || toolUseContent === null) {
        continue;
      }
      const toolUseState = this._conversationModel.getToolUseState(
        requestId,
        toolUseContent.tool_use_id,
      );
      if (
        toolUseState.phase !== ToolUsePhase.completed &&
        toolUseState.phase !== ToolUsePhase.cancelled &&
        toolUseState.phase !== ToolUsePhase.error
      ) {
        return false;
      }
    }
    return true;
  }

  /**
   * Check if we must remove tools to stay under the max result bytes.
   * Returns the index of the first tool that must be removed, or undefined if none.
   */
  private _mustRemoveToolsForMaxBytes(
    requestId: string,
    currToolUseNodes: ChatResultNode[],
  ): number | undefined {
    let totalBytes = 0;
    for (let i = 0; i < currToolUseNodes.length; i++) {
      const toolUseContent = currToolUseNodes[i].tool_use;
      if (toolUseContent === undefined || toolUseContent === null) {
        continue;
      }
      const toolUseState = this._conversationModel.getToolUseState(
        requestId,
        toolUseContent.tool_use_id,
      );
      if (toolUseState.result?.text) {
        totalBytes += new TextEncoder().encode(toolUseState.result.text).length;
        if (totalBytes > ToolsWebviewModelV2.maxAllResultBytes) {
          return i;
        }
      }
    }
    return undefined;
  }

  /**
   * Mark remaining unprocessed tools as omitted due to exceeding max result bytes.
   */
  private _removeRemainingTools(
    requestId: string,
    currToolUseNodes: ChatResultNode[],
    startIdx: number,
  ): void {
    // update the exchage to prune tool use nodes after the startIdx
    this._conversationModel.updateChatItem(requestId, {
      ...this._conversationModel.lastExchange,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      structured_output_nodes: currToolUseNodes.slice(0, startIdx),
    });
  }

  /**
   * Handle MAX_TOKENS retry logic for chat responses that hit the token limit.
   */
  private _handleMaxTokensRetry = (requestId: string, exchange: ExchangeWithStatus): boolean => {
    const toolUseNode = toolOrToolStart(exchange.structured_output_nodes);
    const toolUse = toolUseNode?.tool_use;
    //we only handle TOOL_USE_START  if it was a TOOL_USE it would have been successful, Also it  could be undefined.
    if (!toolUse || toolUseNode?.type !== ChatResultNodeType.TOOL_USE_START) {
      //we only reprompt when there are tool uses.
      return false;
    }

    const converstaionMaxTokens = this._conversationModel.chatHistory
      //find all the success.
      .filter(isChatItemSuccessfulExchange)
      //look for a max retry in the last 6 or so, we don't disable for the rest of the chat.
      .slice(-3 * MAX_TOKEN_RETRY_ATTEMPTS)
      //if it is both stop_reason and TOOL_USE_START (max_tokens without TOOL_USE_START does not retry)
      .filter(
        (v) =>
          v.stop_reason === ChatStopReason.MAX_TOKENS &&
          v.structured_output_nodes?.some((v) => v.type === ChatResultNodeType.TOOL_USE_START),
      );
    if (converstaionMaxTokens.length > MAX_TOKEN_RETRY_ATTEMPTS) {
      this._conversationModel.updateChatItem(
        requestId,
        // eslint-disable-next-line @typescript-eslint/naming-convention
        {
          ...exchange,
          /* eslint-disable @typescript-eslint/naming-convention */
          response_text: "",
          display_error_message: `We encountered difficulties running the ${toolUse.tool_name} tool after attempting ${converstaionMaxTokens.length} different approaches.`,
          status: ExchangeStatus.failed,
          /* eslint-enable @typescript-eslint/naming-convention */
        },
      );
      this._conversationModel.updateToolUseState({
        /* eslint-disable @typescript-eslint/naming-convention */

        requestId,
        toolUseId: toolUse.tool_use_id,
        phase: ToolUsePhase.error,

        /* eslint-enable @typescript-eslint/naming-convention */
      });
      return true;
    }

    const toolUseId = toolUse.tool_use_id;
    const state = this._conversationModel.getToolUseState(requestId, toolUseId);

    //If we get the state and its in error we already failed.
    if (!state.result?.isError) {
      const text = retryPrompt(toolUse.tool_name, converstaionMaxTokens.length);

      this._conversationModel.updateToolUseState({
        /* eslint-disable @typescript-eslint/naming-convention */

        requestId,
        toolUseId,
        phase: ToolUsePhase.error,
        result: {
          text,
          isError: true,
        },
        /* eslint-enable @typescript-eslint/naming-convention */
      });
      this._sendToolResultExchange(requestId);
    }
    return true;
  };

  /**
   * Validate that a tool exists and check if it's safe to run.
   * This handles the full validation flow for a new tool use.
   */
  private _validateAndCheckTool = async (
    requestId: string,
    toolUseId: string,
    toolName: string,
    toolInput: any,
    chatHistory: Exchange[],
  ) => {
    if (this._activeTool !== undefined) {
      console.error("_validateAndCheckTool called while another tool is active", this._activeTool);
      return false;
    }

    // Mark the tool as active and checking safety
    this._activeTool = {
      id: toolUseId,
      requestId,
      phase: ToolUsePhase.checkingSafety,
    };
    this._conversationModel.updateToolUseState({
      requestId,
      toolUseId,
      phase: ToolUsePhase.checkingSafety,
    });

    const toolIdentifierResult = await this._extensionClient.getToolIdentifier(toolName);
    if (!toolIdentifierResult.found) {
      // Mark tool as no longer active before reporting error
      this._activeTool = undefined;
      this._reportToolError(requestId, toolUseId, `Tool "${toolName}" does not exist`);
      return false;
    }

    // If this is an MCP tool, set the mcp_server_name and mcp_tool_name from the response
    if (toolIdentifierResult.toolIdentifier.hostName === ToolHostName.mcpHost) {
      // Find the tool use node in the conversation model
      const lastExchange = this._conversationModel.lastExchange;
      if (lastExchange?.structured_output_nodes) {
        const toolUseNode = lastExchange.structured_output_nodes.find(
          (node) =>
            node.type === ChatResultNodeType.TOOL_USE && node.tool_use?.tool_use_id === toolUseId,
        );

        if (
          toolUseNode?.tool_use &&
          toolIdentifierResult.mcpToolName &&
          toolIdentifierResult.mcpServerName
        ) {
          // Update the tool use content with the values from the response
          toolUseNode.tool_use.mcp_server_name = toolIdentifierResult.mcpServerName;
          toolUseNode.tool_use.mcp_tool_name = toolIdentifierResult.mcpToolName;
        }
      }
    }

    // Dispatch tool safety check to the individual tool.
    const checkSafeRequest: CheckToolCallSafeRequestData = {
      toolName: toolName,
      input: toolInput,
      agentMode: get(this._chatModel.agentExecutionMode),
    };
    const checkSafeResult = await this._extensionClient.checkSafe(checkSafeRequest);
    const isSafe = checkSafeResult.isSafe;

    // Mark the tool as no longer active
    this._activeTool = undefined;

    // Transition to the next state: run the tool or mark it as runnable
    if (isSafe) {
      // This tool call does not need user approval, so we run it right away
      return await this.callTool(
        requestId,
        toolUseId,
        toolName,
        toolInput,
        chatHistory,
        this._conversationModel.id,
      );
    } else {
      // This tool call needs user approval, so we mark it as runnable
      this._conversationModel.updateToolUseState({
        requestId,
        toolUseId,
        phase: ToolUsePhase.runnable,
      });

      // Play sound to notify user that approval is required
      try {
        await this._soundModel.refreshSettings();
        await this._soundModel.playAgentComplete();
      } catch (error) {
        console.warn("Failed to play tool approval sound:", error);
      }

      return false;
    }
  };

  /**
   * Report a tool error by updating the tool state.
   */
  private _reportToolError = (requestId: string, toolUseId: string, errorMessage: string) => {
    this._conversationModel.updateToolUseState({
      requestId,
      toolUseId,
      phase: ToolUsePhase.error,
      result: {
        isError: true,
        text: errorMessage,
      },
    });
  };

  private _generateToolResultNodesFromState(
    requestId: string,
  ): Array<{ toolResult: ChatRequestToolResult; toolUseNodeId: number }> | undefined {
    let toolUses =
      this._conversationModel.lastExchange?.structured_output_nodes?.filter(
        (node) => node.type === ChatResultNodeType.TOOL_USE,
      ) ?? [];
    let toolStates = toolUses.map((node) => {
      return this._conversationModel.getToolUseState(requestId, node.tool_use?.tool_use_id);
    });
    let toolResults: Array<{ toolResult: ChatRequestToolResult; toolUseNodeId: number }> = [];
    for (let i = 0; i < toolStates.length; i++) {
      const toolState = toolStates[i];
      const toolUseId = toolUses[i].tool_use?.tool_use_id;
      const toolUseNodeId = toolUses[i].id;
      if (toolUseId === undefined) {
        continue;
      }
      if (
        !(
          toolState.phase === ToolUsePhase.cancelled ||
          toolState.phase === ToolUsePhase.completed ||
          toolState.phase === ToolUsePhase.error
        )
      ) {
        continue;
      }
      const toolResult = generateToolResultNodeFromState(
        toolState,
        toolUseId,
        this._chatModel.flags.enableDebugFeatures,
      );
      if (toolResult) {
        toolResults.push({ toolResult, toolUseNodeId });
      }
    }
    return toolResults;
  }

  private _sendToolResultExchange = (requestId: string) => {
    // Mark this exchange as sent to prevent duplicate sends
    this._sentExchanges.add(requestId);

    // This condition is a catch-all for any remaining races and is a stronger
    // check than just confirming that we're still in the same conversation.
    // If the most recent exchange in the conversation does not contain the tool
    // use, then sending the tool result will result in at best one failed
    // request, and at worst a permanently corrupted conversation.
    const lastExchangeRequestId = this._conversationModel.lastExchange?.request_id;
    const toolResults = this._generateToolResultNodesFromState(requestId);
    if (lastExchangeRequestId === requestId && toolResults && toolResults.length > 0) {
      this._conversationModel.sendExchange({
        /* eslint-disable @typescript-eslint/naming-convention */
        request_message: "",
        status: ExchangeStatus.draft,
        structured_request_nodes: toolResults.map(({ toolResult, toolUseNodeId }) => {
          return {
            id: toolUseNodeId,
            type: ChatRequestNodeType.TOOL_RESULT,
            tool_result_node: toolResult,
          };
        }),
        model_id: this._conversationModel.selectedModelId ?? undefined,
        /* eslint-enable @typescript-eslint/naming-convention */
      });
    } else if (!toolResults) {
      this._extensionClient.reportError({
        originalRequestId: requestId,
        sanitizedMessage: `Tried to send tool result, but no tool result found`,
        stackTrace: "",
        diagnostics: [
          {
            key: "tool_use_id",
            value: "",
          },
          {
            key: "request_id",
            value: requestId,
          },
        ],
      });
    } else {
      this._extensionClient.reportError({
        originalRequestId: requestId,
        sanitizedMessage: `Tried to send tool result after conversation advanced`,
        stackTrace: "",
        diagnostics: [
          {
            key: "tool_use_id",
            value: "",
          },
        ],
      });
    }
  };

  /**
   *
   * Call the tool and run it. Once we've tried running it, send an event to the exchange
   * processor to decide what to do next with this exchange.
   */
  approveTool = async (
    requestId: string,
    toolUseId: string,
    toolName: string,
    toolInput: any,
    chatHistory: Exchange[],
    conversationId: string,
  ) => {
    if (!this._conversationModel.lastExchange) {
      console.error("Tried to run a tool for an exchange that is no longer available");
    }
    await this.callTool(requestId, toolUseId, toolName, toolInput, chatHistory, conversationId);

    this._processLastExchange(requestId);
  };

  /**
   * Call a tool. Returns true if the tool ran successfully. Else, false.
   */
  callTool = async (
    requestId: string,
    toolUseId: string,
    toolName: string,
    toolInput: any,
    chatHistory: Exchange[],
    conversationId: string,
  ) => {
    if (this._activeTool !== undefined) {
      console.error(
        "callTool called while another tool is active",
        "tried to run:",
        toolUseId,
        "active tool:",
        this._activeTool,
      );
      return false;
    }

    // Mark the tool as running
    this._activeTool = { id: toolUseId, requestId, phase: ToolUsePhase.running };
    this._conversationModel.updateToolUseState({
      requestId,
      toolUseId,
      phase: ToolUsePhase.running,
    });
    try {
      // Call the tool
      const resultClipped = clipResultIfLarge(
        await this._extensionClient.callTool(
          requestId,
          toolUseId,
          toolName,
          toolInput,
          chatHistory,
          conversationId,
        ),
        ToolsWebviewModelV2.maxResultBytes,
      );
      const resultProcessed = await processToolResultImages(
        resultClipped,
        this._chatModel.flags.enableDebugFeatures,
      );

      const toolState = this._conversationModel.getToolUseState(requestId, toolUseId);
      // For the tool to be in this state literally makes no sense unless we are in a new conversation.
      // If we are, we drop the result on the floor & exit early.
      // Also covers the case where the tool was cancelled before we even got here.
      if (toolState.phase === ToolUsePhase.new || toolState.phase === ToolUsePhase.cancelled) {
        return false;
      }
      // In the event that the active tool was cancelled while it was running (user may have hit cancel, etc)
      if (toolState.phase === ToolUsePhase.cancelling) {
        this._conversationModel.updateToolUseState({
          requestId,
          toolUseId,
          phase: ToolUsePhase.cancelled,
          result: resultProcessed,
        });
        return false;
      }

      // Update the tool use state based on the result
      let nextPhase = resultProcessed.isError ? ToolUsePhase.error : ToolUsePhase.completed;
      this._conversationModel.updateToolUseState({
        requestId,
        toolUseId,
        phase: nextPhase,
        result: resultProcessed,
      });
      return true;
    } catch (error) {
      const toolState = this._conversationModel.getToolUseState(requestId, toolUseId);
      if (toolState.phase === ToolUsePhase.new || toolState.phase === ToolUsePhase.cancelled) {
        return false;
      }
      const errorMessage = error instanceof Error ? error.message : String(error);
      if (toolState.phase === ToolUsePhase.cancelling) {
        this._conversationModel.updateToolUseState({
          requestId,
          toolUseId,
          phase: ToolUsePhase.cancelled,
          result: {
            isError: true,
            text: errorMessage,
          },
        });
        return false;
      }

      // If we weren't able to invoke the tool (e.g., the tool doesn't exist or we can't
      // run it for some other reason), mark it as an error.
      this._conversationModel.updateToolUseState({
        requestId,
        toolUseId,
        phase: ToolUsePhase.error,
        result: {
          isError: true,
          text: errorMessage,
        },
      });
      return false;
    } finally {
      if (this._activeTool?.id === toolUseId && this._activeTool?.requestId === requestId) {
        this._activeTool = undefined;
      }
    }
  };

  /**
   * Cancel a tool run.
   */
  cancelToolRun = async (requestId: string, toolUseId: string) => {
    // Interrupt remote agent if available to ensure proper handling for remote agents case
    if (this._remoteAgentsModel && this._remoteAgentsModel.isActive) {
      await this._remoteAgentsModel.interruptAgent();
    }

    if (
      this._activeTool === undefined ||
      this._activeTool.requestId !== requestId ||
      this._activeTool.id !== toolUseId
    ) {
      console.error(
        "cancelToolRun called while another tool is active",
        "tried to cancel:",
        toolUseId,
        "active tool:",
        this._activeTool,
      );
      return;
    }
    this._activeTool = {
      id: toolUseId,
      requestId,
      phase: ToolUsePhase.cancelling,
    };

    this._conversationModel.updateToolUseState({
      requestId,
      toolUseId,
      phase: ToolUsePhase.cancelling,
    });

    await this._extensionClient.cancelToolRun(requestId, toolUseId);

    // Mark the tool as no longer active; test IDs again to avoid blowing away a new tool that started
    // while we were async (the running tool will clear _activeTool when leaving callTool)
    if (this._activeTool?.id === toolUseId && this._activeTool?.requestId === requestId) {
      this._activeTool = undefined;
    }

    this._processLastExchange(requestId);
  };

  skipToolRun = async (requestId: string, toolUseId: string) => {
    if (this._activeTool !== undefined) {
      console.error(
        "skipToolRun called while another tool is active",
        "tried to cancel:",
        toolUseId,
        "active tool:",
        this._activeTool,
      );
      return;
    }

    this._conversationModel.updateToolUseState({
      requestId,
      toolUseId,
      phase: ToolUsePhase.cancelled,
      result: {
        isError: true,
        text: "Tool did not run. User clicked `Skip` to cancel.",
      },
    });
    this._processLastExchange(requestId);
  };

  /**
   * Mark a tool as cancelled if it is in an active state but there is no ongoing operation.
   *
   * This should only happen in scenarios like restart or extension reload.
   */
  private _markCancelledIfStale = (requestId: string, toolUseId: string, phase: ToolUsePhase) => {
    // If there is an ongoing operation for this tool, nothing to do
    if (
      this._activeTool !== undefined &&
      this._activeTool.id === toolUseId &&
      this._activeTool.requestId === requestId
    ) {
      return;
    }

    // If the tool has no ongoing operation but it is in an active state, mark it as cancelled
    if (
      phase === ToolUsePhase.new ||
      phase === ToolUsePhase.checkingSafety ||
      phase === ToolUsePhase.cancelling ||
      phase === ToolUsePhase.running
    ) {
      this._conversationModel.updateToolUseState({
        requestId,
        toolUseId,
        phase: ToolUsePhase.cancelled,
      });
    }
  };

  cancelActiveToolRun = async () => {
    if (this._activeTool !== undefined) {
      await this.cancelToolRun(this._activeTool.requestId, this._activeTool.id);
    }
  };

  /**
   * Interrupt all tools, including the active tool and any runnable tools.
   */
  interruptAllTools = async () => {
    cancelLastRunnableToolUse(this._conversationModel);
    await this.cancelActiveToolRun();

    // Get last exchange. If it is an agentic turn delimiter, we don't need to do anything.
    const lastExchange = this._conversationModel.lastExchange;
    if (!lastExchange || isChatItemAgenticTurnDelimiter(lastExchange)) {
      return;
    }

    // Add in a fake, cancelled exchange to the end
    this._conversationModel.addChatItem({
      /* eslint-disable @typescript-eslint/naming-convention */
      request_id: crypto.randomUUID(),
      status: ExchangeStatus.cancelled,
      chatItemType: ChatItemType.agenticTurnDelimiter,
      /* eslint-enable @typescript-eslint/naming-convention */
    });
  };

  /**
   * Interrupt all tools and the current message.
   *
   * This is used when the user wants to interrupt the agent's current task
   * or switch to a different conversation.
   */
  interruptToolsConversation = async () => {
    await this._conversationModel.cancelMessage();
    await this.interruptAllTools();
  };

  /**
   * Gets the checkpoint number created by a specific tool call
   * @param requestId The request ID
   * @param toolUseId The tool use ID
   * @returns The checkpoint number or undefined if not found
   */
  public async getToolCallCheckpoint(requestId: string): Promise<number | undefined> {
    try {
      const response = await this._extensionClient.getToolCallCheckpoint(requestId);
      return response;
    } catch (e) {
      console.error("Failed to get tool call checkpoint:", e);
      return undefined;
    }
  }

  public dispose = (): void => {
    this._disposables.forEach((disposable) => disposable.dispose());
  };
}
