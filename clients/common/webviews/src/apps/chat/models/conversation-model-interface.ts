import type { Exchange, MemoriesInfo } from "@augment-internal/sidecar-libs/src/chat/chat-types";
import type { ExchangeWithStatus } from "../types/chat-message";
import type { ChatItemType } from "../types/chat-message";
import type { ChatItem } from "../types/chat-message";

export interface IConversationModel {
  get chatHistory(): ChatItem[];
  get conversationId(): string;

  insertChatItem(idx: number, item: ChatItem): void;

  sendSilentExchange(exchange: {
    /* eslint-disable @typescript-eslint/naming-convention */
    request_message: string;
    model_id?: string;
    /* eslint-enable @typescript-eslint/naming-convention */
    chatItemType?: ChatItemType;
    disableRetrieval?: boolean;
    disableSelectedCodeDetails?: boolean;
    chatHistory?: Exchange[];
    // Special type of message for memories
    memoriesInfo?: MemoriesInfo;
  }): Promise<{ responseText: string; requestId: string | undefined }>;

  convertHistoryToExchanges(history: ChatItem[]): Exchange[];

  // events
  onSendExchange(fn: (exchange: ExchangeWithStatus) => void): () => void;
  onNewConversation(fn: () => void): () => void;
  onHistoryDelete(fn: (item: ChatItem) => void): () => void;
}
