import type { Readable } from "svelte/store";
import {
  WebViewMessageType,
  type WebViewMessage,
} from "$vscode/src/webview-providers/webview-messages";
import type { MessageConsumer } from "$common-webviews/src/common/utils/message-broker";
import { DerivedStateName, type AnyActionName } from "$vscode/src/main-panel/action-cards/types";

export class ActionsModel implements MessageConsumer, Readable<ActionsModelStore> {
  private subscribers: Set<(model: ActionsModel) => void> = new Set();
  private _actions: Array<AnyActionName> = [];
  private _currentIndex = 0;

  get currentCard(): AnyActionName {
    return this.actions[this._currentIndex];
  }

  private notifySubscribers() {
    this.subscribers.forEach((sub) => sub(this));
  }

  subscribe = (sub: (model: ActionsModel) => void): (() => void) => {
    this.subscribers.add(sub);
    sub(this);
    return () => {
      this.subscribers.delete(sub);
    };
  };

  get actions(): Array<AnyActionName> {
    return this._actions;
  }

  handleMessageFromExtension(e: MessageEvent<WebViewMessage>): boolean {
    const msg = e.data;
    switch (msg.type) {
      case WebViewMessageType.mainPanelActions: {
        this.onActionsUpdates(msg.data);
        return true;
      }
    }
    return false;
  }

  onActionsUpdates(actionCards: Array<AnyActionName>) {
    this._actions = actionCards;
    this._currentIndex = 0;
    this.notifySubscribers();
  }

  nextAction() {
    this._currentIndex = (this._currentIndex + 1) % this._actions.length;
    this.notifySubscribers();
  }

  prevAction() {
    this._currentIndex = (this._currentIndex - 1 + this._actions.length) % this._actions.length;
    this.notifySubscribers();
  }

  isCollapsible(card: AnyActionName): boolean {
    return card === DerivedStateName.allActionsComplete;
  }
}

interface ActionsModelStore {}
