import { expect, describe, test } from "vitest";
import { SpecialContextInputModel } from "./context-model";
import { ContextStatus, type IContextInfo } from "./types";
import type { FileDetails } from "$vscode/src/webview-providers/webview-messages";
import { AGENT_MEMORIES, type IChatMentionableItem } from "../types/mention-option";
import {
  type ISourceFolderInfo,
  SyncingStatus,
  SyncingEnabledState,
  type SyncingStatusEvent,
} from "$vscode/src/workspace/types";

// ============
// TEST DATA
// ============
const TEST_FILE_1: FileDetails = {
  repoRoot: "/path/to/repo",
  pathName: "test/rel/path",
};
const TEST_FILE_1_CONTEXT: IChatMentionableItem<"file"> & IContextInfo = {
  file: TEST_FILE_1,
  referenceCount: 1,
  status: ContextStatus.active,
  label: "path",
  name: "/path/to/repo/test/rel/path",
  id: "/path/to/repo/test/rel/path",
};
const TEST_FILE_2: FileDetails = {
  repoRoot: "/path/to/repo",
  pathName: "test/rel/path2",
};
const TEST_FILE_2_CONTEXT: IChatMentionableItem<"file"> & IContextInfo = {
  file: TEST_FILE_2,
  referenceCount: 1,
  status: ContextStatus.active,
  label: "path2",
  name: "/path/to/repo/test/rel/path2",
  id: "/path/to/repo/test/rel/path2",
};

const TEST_OPEN_FILE_1: FileDetails = {
  repoRoot: "/path/to/repo",
  pathName: "test/rel/path",
};
const TEST_OPEN_FILE_1_MANUALLY_ADDED: FileDetails = {
  repoRoot: "/path/to/repo",
  pathName: "test/rel/path",
};
const TEST_OPEN_FILE_1_CONTEXT: IChatMentionableItem<"recentFile"> & IContextInfo = {
  recentFile: TEST_OPEN_FILE_1,
  referenceCount: 1,
  status: ContextStatus.active,
  label: "path",
  name: "/path/to/repo/test/rel/path",
  id: "/path/to/repo/test/rel/path",
};

const TEST_SOURCE_FOLDER_1: ISourceFolderInfo = {
  folderRoot: "/path/to/repo1",
  guidelinesOverLimit: false,
  guidelinesLengthLimit: 2000, // Add this property to match the actual object
};
const TEST_SOURCE_FOLDER_1_CONTEXT: IChatMentionableItem<"sourceFolder"> & IContextInfo = {
  sourceFolder: TEST_SOURCE_FOLDER_1,
  referenceCount: 1,
  status: ContextStatus.active,
  label: "/path/to/repo1",
  id: "/path/to/repo1false",
  showWarning: false,
  pinned: undefined, // Add this property to match the actual object
};

const TEST_SOURCE_FOLDER_2: ISourceFolderInfo = {
  folderRoot: "/path/to/repo2",
  guidelinesOverLimit: false,
  guidelinesLengthLimit: 2000, // Add this property to match the actual object
};
const TEST_SOURCE_FOLDER_2_CONTEXT: IChatMentionableItem<"sourceFolder"> & IContextInfo = {
  sourceFolder: TEST_SOURCE_FOLDER_2,
  referenceCount: 1,
  status: ContextStatus.active,
  label: "/path/to/repo2",
  id: "/path/to/repo2false",
  showWarning: false,
  pinned: undefined, // Add this property to match the actual object
};

const TEST_SELECTION_1: FileDetails = {
  repoRoot: "/path/to/repo",
  pathName: "test/rel/path",
  fullRange: {
    startLineNumber: 1,
    startColumn: 1,
    endLineNumber: 2,
    endColumn: 2,
  },
};

const TEST_SELECTION_1_CONTEXT: IChatMentionableItem<"selection"> & IContextInfo = {
  selection: TEST_SELECTION_1,
  referenceCount: 1,
  status: ContextStatus.active,
  label: "path:L1-2",
  name: "/path/to/repo/test/rel/path:L1-2",
  id: "/path/to/repo/test/rel/path:L1-2",
};

const TEST_SELECTION_2: FileDetails = {
  repoRoot: "/path/to/repo",
  pathName: "test/rel/path2",
  fullRange: {
    startLineNumber: 1,
    startColumn: 1,
    endLineNumber: 2,
    endColumn: 2,
  },
};

const TEST_SELECTION_2_CONTEXT: IChatMentionableItem<"selection"> & IContextInfo = {
  selection: TEST_SELECTION_2,
  referenceCount: 1,
  status: ContextStatus.active,
  label: "path2:L1-2",
  name: "/path/to/repo/test/rel/path2:L1-2",
  id: "/path/to/repo/test/rel/path2:L1-2",
};

// ============
// Tests
// ============

describe("SpecialContextInputModel", () => {
  describe("file management", () => {
    test("addFile", () => {
      const model = new SpecialContextInputModel();
      model.addFile(TEST_FILE_1);
      expect(model.files).toEqual([TEST_FILE_1_CONTEXT]);
    });

    test("addFiles", () => {
      const model = new SpecialContextInputModel();
      model.addFiles([TEST_FILE_1, TEST_FILE_2]);
      expect(model.files).toEqual([TEST_FILE_2_CONTEXT, TEST_FILE_1_CONTEXT]);
    });

    test("addFiles with duplicates", () => {
      const model = new SpecialContextInputModel();
      model.addFiles([TEST_FILE_1, TEST_FILE_1]);
      expect(model.files).toEqual([
        {
          ...TEST_FILE_1_CONTEXT,
          referenceCount: 2,
        },
      ]);
    });

    test("removeFile", () => {
      const model = new SpecialContextInputModel();
      model.addFile(TEST_FILE_1);
      expect(Object.entries(model.files)).toHaveLength(1);
      model.removeFile(TEST_FILE_1);
      expect(model.files).toEqual([]);
    });

    test("removeFile that does not exist does nothing", () => {
      const model = new SpecialContextInputModel();
      model.removeFile(TEST_FILE_1);
      expect(model.files).toEqual([]);
    });

    test("removeFiles", () => {
      const model = new SpecialContextInputModel();
      model.addFiles([TEST_FILE_1, TEST_FILE_2]);
      expect(Object.entries(model.files)).toHaveLength(2);
      model.removeFiles([TEST_FILE_1, TEST_FILE_2]);
      expect(model.files).toEqual([]);
    });

    test("add and remove files", () => {
      const model = new SpecialContextInputModel();
      model.addFile(TEST_FILE_1);
      expect(Object.entries(model.files)).toHaveLength(1);
      model.addFiles([TEST_FILE_1, TEST_FILE_1]);
      expect(model.files).toEqual([
        {
          ...TEST_FILE_1_CONTEXT,
          referenceCount: 3,
        },
      ]);
      // Remove files and ensure referenceCount is decremented
      model.removeFiles([TEST_FILE_1, TEST_FILE_1]);
      expect(model.files).toEqual([TEST_FILE_1_CONTEXT]);
      model.addFile(TEST_FILE_2);
      expect(model.files).toEqual([TEST_FILE_2_CONTEXT, TEST_FILE_1_CONTEXT]);
      model.removeFiles([TEST_FILE_1, TEST_FILE_2]);
      expect(model.files).toEqual([]);
    });

    test("clearFiles", () => {
      const model = new SpecialContextInputModel();
      model.addFile(TEST_FILE_1);
      expect(Object.entries(model.files)).toHaveLength(1);
      model.clearFiles();
      expect(model.files).toEqual([]);
    });

    test("updateFiles", () => {
      const model = new SpecialContextInputModel();
      // Add a file and remove another that does not exist
      model.updateFiles([TEST_FILE_1], [TEST_FILE_2]);
      expect(model.files).toEqual([TEST_FILE_1_CONTEXT]);
      // Add the second file and remove the first
      model.updateFiles([TEST_FILE_2], [TEST_FILE_1]);
      expect(model.files).toEqual([TEST_FILE_2_CONTEXT]);
    });

    test("toggleStatus", () => {
      const model = new SpecialContextInputModel();
      model.addFile(TEST_FILE_1);
      expect(model.files).toEqual([TEST_FILE_1_CONTEXT]);
      model.toggleStatus(TEST_FILE_1_CONTEXT);
      expect(model.files).toEqual([
        {
          ...TEST_FILE_1_CONTEXT,
          status: ContextStatus.inactive,
        },
      ]);
    });
  });

  describe("open file management", () => {
    test("setCurrentlyOpenFiles", () => {
      const model = new SpecialContextInputModel();
      model.setCurrentlyOpenFiles([TEST_OPEN_FILE_1]);
      expect(model.recentFiles).toEqual([TEST_OPEN_FILE_1_CONTEXT]);
    });

    test("setCurrentlyOpenFiles with duplicates has two references", () => {
      const model = new SpecialContextInputModel();
      model.setCurrentlyOpenFiles([TEST_OPEN_FILE_1, TEST_OPEN_FILE_1]);
      expect(model.recentFiles).toEqual([
        {
          ...TEST_OPEN_FILE_1_CONTEXT,
          referenceCount: 2,
        },
      ]);
    });

    test("unset CurrentlyOpenFiles", () => {
      const model = new SpecialContextInputModel();
      model.setCurrentlyOpenFiles([TEST_OPEN_FILE_1]);
      expect(model.recentFiles).toEqual([TEST_OPEN_FILE_1_CONTEXT]);
      model.setCurrentlyOpenFiles([]);
      expect(model.files).toEqual([]);
    });
  });

  describe("SpecialContextInputModel source folder management", () => {
    test("onSourceFoldersUpdated", () => {
      const model = new SpecialContextInputModel();
      model.onSourceFoldersUpdated([TEST_SOURCE_FOLDER_1]);
      expect(model.sourceFolders).toEqual([
        {
          ...TEST_SOURCE_FOLDER_1_CONTEXT,
          referenceCount: 1,
          status: ContextStatus.active,
        },
      ]);
    });

    test("onSourceFoldersUpdated with duplicates has two references", () => {
      const model = new SpecialContextInputModel();
      model.onSourceFoldersUpdated([TEST_SOURCE_FOLDER_1, TEST_SOURCE_FOLDER_1]);
      expect(model.sourceFolders).toEqual([
        {
          ...TEST_SOURCE_FOLDER_1_CONTEXT,
          referenceCount: 2,
          status: ContextStatus.active,
        },
      ]);
    });

    test("onSourceFoldersUpdated with multiple folders", () => {
      const model = new SpecialContextInputModel();
      model.onSourceFoldersUpdated([TEST_SOURCE_FOLDER_1, TEST_SOURCE_FOLDER_2]);
      expect(model.sourceFolders).toEqual([
        {
          ...TEST_SOURCE_FOLDER_2_CONTEXT,
          referenceCount: 1,
          status: ContextStatus.active,
        },
        {
          ...TEST_SOURCE_FOLDER_1_CONTEXT,
          referenceCount: 1,
          status: ContextStatus.active,
        },
      ]);
    });
  });

  describe("selection management", () => {
    test("updateselections", () => {
      const model = new SpecialContextInputModel();
      model.updateSelections([TEST_SELECTION_1]);
      expect(model.selections).toEqual([TEST_SELECTION_1_CONTEXT]);
    });

    test("updateselections with multiple selections", () => {
      const model = new SpecialContextInputModel();
      model.updateSelections([TEST_SELECTION_1, TEST_SELECTION_2]);
      expect(model.selections).toEqual([TEST_SELECTION_2_CONTEXT, TEST_SELECTION_1_CONTEXT]);
    });

    test("removeSelection", () => {
      const model = new SpecialContextInputModel();
      model.updateSelections([TEST_SELECTION_1]);
      expect(model.selections).toEqual([TEST_SELECTION_1_CONTEXT]);
      model.updateSelections([]);
      expect(model.selections).toEqual([]);
    });
  });

  describe("open files with tagged files", () => {
    test("remove open file and keep tagged file", () => {
      const model = new SpecialContextInputModel();
      model.setCurrentlyOpenFiles([TEST_OPEN_FILE_1]);
      model.addFile(TEST_OPEN_FILE_1_MANUALLY_ADDED);
      expect(model.recentFiles).toEqual([
        {
          ...TEST_OPEN_FILE_1_CONTEXT,
          referenceCount: 2,
        },
      ]);
      model.setCurrentlyOpenFiles([]);
      expect(model.files).toEqual([
        {
          ...TEST_FILE_1_CONTEXT,
          referenceCount: 1,
        },
      ]);
    });

    test("remove tagged file and keep open file", () => {
      const model = new SpecialContextInputModel();
      model.setCurrentlyOpenFiles([TEST_OPEN_FILE_1]);
      model.addFile(TEST_OPEN_FILE_1_MANUALLY_ADDED);
      expect(model.recentFiles).toEqual([
        {
          ...TEST_OPEN_FILE_1_CONTEXT,
          referenceCount: 2,
        },
      ]);
      model.removeFile(TEST_OPEN_FILE_1_MANUALLY_ADDED);
      expect(model.recentFiles).toEqual([
        {
          ...TEST_OPEN_FILE_1_CONTEXT,
          referenceCount: 1,
        },
      ]);
    });
  });

  test("toggle item status", () => {
    const model = new SpecialContextInputModel();
    model.setCurrentlyOpenFiles([TEST_OPEN_FILE_1]);
    model.addFile(TEST_OPEN_FILE_1_MANUALLY_ADDED);
    model.toggleStatus(TEST_OPEN_FILE_1_CONTEXT);
    expect(model.recentFiles).toEqual([
      {
        ...TEST_OPEN_FILE_1_CONTEXT,
        referenceCount: 2,
        status: ContextStatus.inactive,
      },
    ]);
    model.toggleStatus(TEST_OPEN_FILE_1_CONTEXT);
    expect(model.recentFiles).toEqual([
      {
        ...TEST_OPEN_FILE_1_CONTEXT,
        referenceCount: 2,
        status: ContextStatus.active,
      },
    ]);
  });

  test("toggle file status on non-existent file does nothing", () => {
    const model = new SpecialContextInputModel();
    model.toggleStatus(TEST_OPEN_FILE_1_CONTEXT);
    expect(model.files).toEqual([]);
  });

  describe("merging open files and tagged files", () => {
    test("same open and tagged files", () => {
      const model = new SpecialContextInputModel();
      model.setCurrentlyOpenFiles([TEST_OPEN_FILE_1]);
      model.addFile(TEST_OPEN_FILE_1_MANUALLY_ADDED);
      expect(model.recentFiles).toEqual([
        {
          ...TEST_OPEN_FILE_1_CONTEXT,
          referenceCount: 2,
        },
      ]);
    });

    test("same open and tagged files with duplicates", () => {
      const model = new SpecialContextInputModel();
      model.setCurrentlyOpenFiles([TEST_OPEN_FILE_1, TEST_OPEN_FILE_1]);
      model.addFile(TEST_OPEN_FILE_1_MANUALLY_ADDED);
      expect(model.recentFiles).toEqual([
        {
          ...TEST_OPEN_FILE_1_CONTEXT,
          referenceCount: 3,
        },
      ]);
      expect(model.files).toEqual([]);
    });

    test("remove open file and keep tagged file", () => {
      const model = new SpecialContextInputModel();
      model.setCurrentlyOpenFiles([TEST_OPEN_FILE_1]);
      model.addFile(TEST_OPEN_FILE_1_MANUALLY_ADDED);
      expect(model.recentFiles).toEqual([
        {
          ...TEST_OPEN_FILE_1_CONTEXT,
          referenceCount: 2,
        },
      ]);
      expect(model.files).toEqual([]);
      model.setCurrentlyOpenFiles([]);
      expect(model.recentFiles).toEqual([]);
      expect(model.files).toEqual([
        {
          ...TEST_FILE_1_CONTEXT,
          referenceCount: 1,
        },
      ]);
    });

    test("remove tagged file and keep open file", () => {
      const model = new SpecialContextInputModel();
      model.setCurrentlyOpenFiles([TEST_OPEN_FILE_1]);
      model.addFile(TEST_OPEN_FILE_1_MANUALLY_ADDED);
      expect(model.recentFiles).toEqual([
        {
          ...TEST_OPEN_FILE_1_CONTEXT,
          referenceCount: 2,
        },
      ]);
      model.removeFile(TEST_OPEN_FILE_1_MANUALLY_ADDED);
      expect(model.recentFiles).toEqual([
        {
          ...TEST_OPEN_FILE_1_CONTEXT,
          referenceCount: 1,
        },
      ]);
      expect(model.files).toEqual([]);
    });

    describe("maintain status over time", () => {
      test("open file is active", () => {
        const model = new SpecialContextInputModel();
        model.setCurrentlyOpenFiles([TEST_OPEN_FILE_1]);
        model.addFile(TEST_OPEN_FILE_1_MANUALLY_ADDED);
        expect(model.recentFiles).toEqual([
          {
            ...TEST_OPEN_FILE_1_CONTEXT,
            referenceCount: 2,
          },
        ]);
      });

      test("open file is inactive", () => {
        const model = new SpecialContextInputModel();
        model.setCurrentlyOpenFiles([TEST_OPEN_FILE_1]);
        model.addFile(TEST_OPEN_FILE_1_MANUALLY_ADDED);
        model.toggleStatus(TEST_OPEN_FILE_1_CONTEXT);
        expect(model.recentFiles).toEqual([
          {
            ...TEST_OPEN_FILE_1_CONTEXT,
            referenceCount: 2,
            status: ContextStatus.inactive,
          },
        ]);
      });
    });
  });

  describe("integration between different interaction types", () => {
    test("@mention toggle state stays consistent", () => {
      const model = new SpecialContextInputModel();

      // Add a file and toggle it off
      model.addFile(TEST_FILE_1);
      model.toggleStatus(TEST_FILE_1_CONTEXT);
      expect(model.files).toEqual([
        {
          ...TEST_FILE_1_CONTEXT,
          referenceCount: 1,
          status: ContextStatus.inactive,
        },
      ]);

      // Open the file, it should still be off
      model.setCurrentlyOpenFiles([TEST_OPEN_FILE_1]);
      expect(model.recentFiles).toEqual([
        {
          ...TEST_OPEN_FILE_1_CONTEXT,
          referenceCount: 2,
          status: ContextStatus.inactive,
        },
      ]);
      expect(model.files).toEqual([]);

      // Toggle it back on, it should be on now
      model.toggleStatus(TEST_OPEN_FILE_1_CONTEXT);
      expect(model.recentFiles).toEqual([
        {
          ...TEST_OPEN_FILE_1_CONTEXT,
          referenceCount: 2,
          status: ContextStatus.active,
        },
      ]);

      // Close the file, it should still be on
      model.setCurrentlyOpenFiles([]);
      expect(model.files).toEqual([
        {
          ...TEST_FILE_1_CONTEXT,
          referenceCount: 1,
          status: ContextStatus.active,
        },
      ]);
    });
  });

  describe("sync progress", () => {
    test("return undefined for total files lte 0", () => {
      const model = new SpecialContextInputModel();
      expect(model.syncProgress).toBeUndefined();

      model.onSyncStatusUpdated({
        status: SyncingStatus.done,
        foldersProgress: [
          {
            folderRoot: "example/sync-repo-root-1",
            progress: undefined,
          },
        ],
      });
      expect(model.syncProgress).toBeUndefined();

      model.onSyncStatusUpdated({
        status: SyncingStatus.done,
        foldersProgress: [
          {
            folderRoot: "example/sync-repo-root-1",
            progress: {
              newlyTracked: false,
              trackedFiles: 0,
              backlogSize: 0,
            },
          },
        ],
      });
      expect(model.syncProgress).toEqual({
        backlogSize: 0,
        status: SyncingStatus.done,
        newlyTrackedFolders: [],
        syncedCount: 0,
        totalFiles: 0,
      });

      model.onSyncStatusUpdated({
        status: SyncingStatus.done,
        foldersProgress: [
          {
            folderRoot: "example/sync-repo-root-1",
            progress: {
              newlyTracked: false,
              trackedFiles: -1,
              backlogSize: 0,
            },
          },
        ],
      });
      expect(model.syncProgress).toEqual({
        status: SyncingStatus.done,
        newlyTrackedFolders: [],
        backlogSize: 0,
        syncedCount: 0,
        totalFiles: 0,
      });
    });
  });

  describe("pinning items", () => {
    test("pin and unpin an item", () => {
      const model = new SpecialContextInputModel();
      model.addFile(TEST_FILE_1);
      expect(model.files).toEqual([TEST_FILE_1_CONTEXT]);
      model.togglePinned(TEST_FILE_1_CONTEXT);
      expect(model.files).toEqual([
        {
          ...TEST_FILE_1_CONTEXT,
          pinned: true,
          referenceCount: 2,
        },
      ]);
      model.togglePinned(TEST_FILE_1_CONTEXT);
      expect(model.files).toEqual([{ ...TEST_FILE_1_CONTEXT, pinned: false }]);
    });

    test("unpin removes item if no other references", () => {
      const model = new SpecialContextInputModel();
      model.addFile(TEST_FILE_1);
      model.togglePinned(TEST_FILE_1_CONTEXT);
      model.removeFile(TEST_FILE_1);
      expect(model.files).toEqual([{ ...TEST_FILE_1_CONTEXT, pinned: true }]);
      model.unpin(TEST_FILE_1_CONTEXT);
      expect(model.files).toEqual([]);
    });

    test("pinned item persists through other operations", () => {
      const model = new SpecialContextInputModel();
      model.addFile(TEST_FILE_1);
      model.togglePinned(TEST_FILE_1_CONTEXT);
      model.addFile(TEST_FILE_2);
      expect(model.files).toEqual([
        TEST_FILE_2_CONTEXT,
        {
          ...TEST_FILE_1_CONTEXT,
          pinned: true,
          referenceCount: 2,
        },
      ]);
      model.removeFile(TEST_FILE_2);
      expect(model.files).toEqual([
        {
          ...TEST_FILE_1_CONTEXT,
          pinned: true,
          referenceCount: 2,
        },
      ]);
    });

    test("unpinning an already unpinned item does nothing", () => {
      const model = new SpecialContextInputModel();

      // Should not be pinned, with reference count 1 (adding)
      model.addFile(TEST_FILE_1);
      expect(model.files).toEqual([
        expect.objectContaining({
          referenceCount: 1,
          pinned: undefined, // Not yet interacted with
        }),
      ]);

      // Should be pinned now, with reference count 2 (pinning and adding)
      model.togglePinned(TEST_FILE_1_CONTEXT);
      expect(model.files).toEqual([
        expect.objectContaining({
          referenceCount: 2,
          pinned: true,
        }),
      ]);

      // Unpin once, should be unpinned, with reference count 1 (unpinning)
      model.unpin(TEST_FILE_1_CONTEXT);
      expect(model.files).toEqual([
        expect.objectContaining({
          referenceCount: 1,
          pinned: false,
        }),
      ]);

      // Unpin again, should be unpinned, with reference count 1 (no change, repeat)
      model.unpin(TEST_FILE_1_CONTEXT);
      expect(model.files).toEqual([
        expect.objectContaining({
          referenceCount: 1,
          pinned: false,
        }),
      ]);
    });
  });

  describe("context disabling", () => {
    test("disableContext sets flag and affects getters", () => {
      const model = new SpecialContextInputModel();
      model.addFile(TEST_FILE_2);
      model.updateUserGuidelines({
        contents: "test guidelines",
        overLimit: false,
        lengthLimit: 2000,
      });
      expect(model.files).toEqual([TEST_FILE_2_CONTEXT]);
      expect(model.isContextDisabled).toBe(false);

      model.disableContext();
      expect(model.isContextDisabled).toBe(true);
      expect(model.files).toEqual([]);
      expect(model.recentFiles).toEqual([]);
      expect(model.selections).toEqual([]);
      expect(model.folders).toEqual([]);
      expect(model.sourceFolders).toEqual([]);
      expect(model.externalSources).toEqual([]);
      // User guidelines should still be available when context is disabled
      expect(model.userGuidelines).toHaveLength(1);
    });

    test("enableContext re-enables context access", () => {
      const model = new SpecialContextInputModel();
      model.addFile(TEST_FILE_1);
      model.disableContext();
      expect(model.files).toEqual([]);

      model.enableContext();
      expect(model.isContextDisabled).toBe(false);
      expect(model.files).toEqual([TEST_FILE_1_CONTEXT]);
    });

    test("recentItems only shows userGuidelines when context disabled", () => {
      const model = new SpecialContextInputModel();
      model.addFile(TEST_FILE_1);

      // Add user guidelines
      model.updateUserGuidelines({
        contents: "test guidelines",
        overLimit: false,
        lengthLimit: 2000,
      });

      expect(model.recentItems).toContainEqual(
        expect.objectContaining({
          id: "userGuidelines",
          label: "User Guidelines",
        }),
      );
      expect(model.recentItems).toContainEqual(TEST_FILE_1_CONTEXT);

      model.disableContext();
      // Should only contain user guidelines when disabled
      expect(model.recentItems).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            id: "userGuidelines",
            label: "User Guidelines",
          }),
        ]),
      );
      expect(model.recentItems).toHaveLength(1);
    });

    test("active getters respect disabled context", () => {
      const model = new SpecialContextInputModel();
      model.addFile(TEST_FILE_1);

      expect(model.activeFiles).toEqual([TEST_FILE_1_CONTEXT]);
      expect(model.recentActiveItems).toContainEqual(TEST_FILE_1_CONTEXT);

      model.disableContext();
      expect(model.activeFiles).toEqual([]);
      expect(model.activeRecentFiles).toEqual([]);
      expect(model.activeExternalSources).toEqual([]);
      expect(model.activeSelections).toEqual([]);
      expect(model.activeSourceFolders).toEqual([]);
      // Recent active items should only contain user guidelines if present
      expect(model.recentActiveItems).toEqual([]);
    });
  });

  describe("subscription management", () => {
    test("subscribe and unsubscribe", () => {
      const model = new SpecialContextInputModel();
      let notifyCount = 0;

      const unsubscribe = model.subscribe(() => {
        notifyCount++;
      });

      model.addFile(TEST_FILE_1);
      expect(notifyCount).toBe(2); // Once for initial subscribe, once for addFile

      unsubscribe();
      model.addFile(TEST_FILE_2);
      expect(notifyCount).toBe(2); // Should not have increased after unsubscribe
    });
  });

  describe("sync status management", () => {
    test("onSyncStatusUpdated updates sync status", () => {
      const model = new SpecialContextInputModel();
      const syncStatus: SyncingStatusEvent = {
        status: SyncingStatus.running,
        foldersProgress: [
          {
            folderRoot: "/test/folder",
            progress: {
              newlyTracked: false,
              trackedFiles: 100,
              backlogSize: 50,
            },
          },
        ],
      };

      model.onSyncStatusUpdated(syncStatus);
      expect(model.syncProgress).toEqual({
        status: SyncingStatus.running,
        newlyTrackedFolders: [],
        backlogSize: 50,
        syncedCount: 50,
        totalFiles: 100,
      });
    });

    test("onSyncEnabledStateUpdate updates sync enabled state", () => {
      const model = new SpecialContextInputModel();

      model.onSyncEnabledStateUpdate(SyncingEnabledState.enabled);
      expect(model.syncEnabledState).toBe(SyncingEnabledState.enabled);
    });
  });

  describe("user guidelines management", () => {
    test("updateUserGuidelines adds and updates guidelines", () => {
      const model = new SpecialContextInputModel();
      const guidelines = {
        contents: "Test guidelines",
        overLimit: false,
        lengthLimit: 2000,
      };

      model.updateUserGuidelines(guidelines);
      expect(model.userGuidelines).toHaveLength(1);
      expect(model.userGuidelines[0]).toMatchObject({
        id: "userGuidelines",
        label: "User Guidelines",
        userGuidelines: guidelines,
        status: ContextStatus.active,
        showWarning: false,
      });

      // Update with new guidelines
      const updatedGuidelines = {
        contents: "Updated guidelines",
        overLimit: true,
        lengthLimit: 2000,
      };

      model.updateUserGuidelines(updatedGuidelines);
      expect(model.userGuidelines).toHaveLength(1);
      expect(model.userGuidelines[0]).toMatchObject({
        userGuidelines: updatedGuidelines,
        showWarning: true,
      });
    });
  });

  describe("context enabling/disabling", () => {
    test("disableContext/enableContext affects all getters", async () => {
      const model = new SpecialContextInputModel();

      // Add various types of context and wait for them to be processed
      await Promise.all([
        model.addFile(TEST_FILE_2),
        model.setCurrentlyOpenFiles([TEST_OPEN_FILE_1]),
        model.updateSelections([TEST_SELECTION_1]),
        model.onSourceFoldersUpdated([TEST_SOURCE_FOLDER_1]),
      ]);

      // Verify items exist
      expect(model.files).toHaveLength(1);
      expect(model.recentFiles).toHaveLength(1);
      expect(model.selections).toHaveLength(1);
      expect(model.sourceFolders).toHaveLength(1);

      // Disable context
      model.disableContext();

      // Verify all getters return empty arrays except userGuidelines
      expect(model.files).toHaveLength(0);
      expect(model.recentFiles).toHaveLength(0);
      expect(model.selections).toHaveLength(0);
      expect(model.sourceFolders).toHaveLength(0);

      // Re-enable context
      model.enableContext();

      // Verify items are restored
      expect(model.files).toHaveLength(1);
      expect(model.recentFiles).toHaveLength(1);
      expect(model.selections).toHaveLength(1);
      expect(model.sourceFolders).toHaveLength(1);
    });
  });

  describe("item status management", () => {
    test("markItemsActive updates item status", () => {
      const model = new SpecialContextInputModel();
      model.addFile(TEST_FILE_1);

      // Set status to inactive first
      model.toggleStatus(TEST_FILE_1_CONTEXT);
      expect(model.files[0].status).toBe(ContextStatus.inactive);

      // Mark active
      model.markItemsActive([TEST_FILE_1_CONTEXT]);
      expect(model.files[0].status).toBe(ContextStatus.active);
    });

    test("markAllActive activates all inactive items", () => {
      const model = new SpecialContextInputModel();
      model.addFiles([TEST_FILE_1, TEST_FILE_2]);

      // Set both files to inactive
      model.toggleStatus(TEST_FILE_1_CONTEXT);
      model.toggleStatus(TEST_FILE_2_CONTEXT);

      expect(model.files[0].status).toBe(ContextStatus.inactive);
      expect(model.files[1].status).toBe(ContextStatus.inactive);

      model.markAllActive();

      expect(model.files[0].status).toBe(ContextStatus.active);
      expect(model.files[1].status).toBe(ContextStatus.active);
    });

    test("pin management", () => {
      const model = new SpecialContextInputModel();
      model.addFile(TEST_FILE_1);

      model.togglePinned(TEST_FILE_1_CONTEXT);
      expect(model.files[0].pinned).toBe(true);

      model.unpin(TEST_FILE_1_CONTEXT);
      expect(model.files[0].pinned).toBe(false);
    });
  });

  describe("recentItems management", () => {
    test("recentItems returns correct items in order", async () => {
      const model = new SpecialContextInputModel();

      await Promise.all([
        model.updateUserGuidelines({
          contents: "Test guidelines",
          overLimit: false,
          lengthLimit: 2000,
        }),
        model.addFile(TEST_FILE_2),
        model.setCurrentlyOpenFiles([TEST_OPEN_FILE_1]),
        model.updateSelections([TEST_SELECTION_1]),
      ]);

      const recentItems = model.recentItems;

      expect(recentItems).toHaveLength(5);
      expect(recentItems[0]).toMatchObject({
        ...TEST_SELECTION_1_CONTEXT,
        referenceCount: 1,
      });
      expect(recentItems[1]).toMatchObject({
        ...TEST_OPEN_FILE_1_CONTEXT,
        referenceCount: 1,
      });
      expect(recentItems[2]).toMatchObject({
        ...TEST_FILE_2_CONTEXT,
        referenceCount: 1,
      });
      expect(recentItems[3]).toMatchObject({
        id: "userGuidelines",
        label: "User Guidelines",
      });
      expect(recentItems[4]).toMatchObject({
        ...AGENT_MEMORIES,
        referenceCount: 1,
      });
    });

    test("recentActiveItems returns only active items", () => {
      const model = new SpecialContextInputModel();
      model.addFiles([TEST_FILE_1, TEST_FILE_2]);

      model.toggleStatus(TEST_FILE_1_CONTEXT);

      const activeItems = model.recentActiveItems;
      expect(activeItems).toHaveLength(1);
      expect(activeItems[0]).toMatchObject(TEST_FILE_2_CONTEXT);
    });

    test("recentInactiveItems returns only inactive items", async () => {
      const model = new SpecialContextInputModel();
      model.addFiles([TEST_FILE_1, TEST_FILE_2]);

      // Ensure we set the status to inactive
      model.toggleStatus(TEST_FILE_1_CONTEXT);

      const inactiveItems = model.recentInactiveItems;
      expect(inactiveItems).toHaveLength(2);
      expect(inactiveItems[0]).toMatchObject({
        ...TEST_FILE_1_CONTEXT,
        status: ContextStatus.inactive,
      });
      expect(inactiveItems[1]).toMatchObject({
        ...AGENT_MEMORIES,
        status: ContextStatus.inactive,
      });
    });
  });
});
