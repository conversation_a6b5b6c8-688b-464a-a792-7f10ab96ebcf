import { host } from "$common-webviews/src/common/hosts/__mocks__/host";
import { AsyncMsgSender } from "$common-webviews/src/common/hosts/async-messaging";
import { HostClientType, type HostInterface } from "$common-webviews/src/common/hosts/host-types";
import {
  type AsyncWebViewMessage,
  WebViewMessageType,
  type WebViewMessage,
} from "$vscode/src/webview-providers/webview-messages";
import { AgentWebViewMessageType } from "@augment-internal/sidecar-libs/src/webview-messages/message-types/agent-messages";
import { ExchangeWebViewMessageType } from "@augment-internal/sidecar-libs/src/webview-messages/message-types/exchange-messages";
import { RulesWebViewMessageType } from "@augment-internal/sidecar-libs/src/webview-messages/message-types/rules-messages";
import { ToolUseStateWebViewMessageType } from "@augment-internal/sidecar-libs/src/webview-messages/message-types/tooluse-messages";
import {
  ChatRequestNodeType,
  PersonaType,
} from "@augment-internal/sidecar-libs/src/chat/chat-types";
import { afterEach, beforeEach, describe, expect, test, vi, type MockInstance } from "vitest";
import { waitFor } from "@testing-library/dom";
import { ExchangeStatus } from "../types/chat-message";
import { ChatModel } from "./chat-model";
import { SpecialContextInputModel } from "./context-model";
import { ConversationModel } from "./conversation-model";
import type { IConversation } from "./types";
import { type NewThread, type NewThreadData } from "$vscode/src/webview-providers/webview-messages";
import { type ChatModeModel } from "./chat-mode-model";
import { RulesModel } from "../../settings/models/rules-model";
import { type MessageBroker } from "$common-webviews/src/common/utils/message-broker";
import { _DO_NOT_USE_IN_APP_CODE_unlockAllChains } from "../utils/locking-chain";

// Set up transport-level mocking with proper async behavior
const mockPostMessage = (msg: WebViewMessage | AsyncWebViewMessage<WebViewMessage>) => {
  // Give up main thread to force some asynchronousity
  setTimeout(() => {
    // Handle both message structures: direct async wrapper and nested data structure
    const messageType = msg.type;
    const isAsyncWrapper = "baseMsg" in msg;
    const hasData = "data" in msg;
    const baseMsg = isAsyncWrapper
      ? msg.baseMsg
      : hasData && msg.data && typeof msg.data === "object" && "type" in msg.data
        ? { type: (msg.data as any).type }
        : null;
    const requestId = isAsyncWrapper
      ? msg.requestId
      : hasData && msg.data && typeof msg.data === "object" && "requestId" in msg.data
        ? (msg.data as any).requestId
        : undefined;

    // Helper function to create response based on message structure
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const createResponse = (
      responseType: string,
      responseData: any,
      useAsyncWrapper: boolean = true,
    ) => {
      if (hasData && !isAsyncWrapper) {
        // Handle nested data structure (second format)
        if (useAsyncWrapper) {
          return {
            type: WebViewMessageType.asyncWrapper,
            data: {
              type: responseType,
              data: responseData,
              requestId: requestId,
            },
          };
        } else {
          return {
            type: responseType,
            data: responseData,
          };
        }
      } else {
        // Handle standard async wrapper structure (first format)
        return {
          type: WebViewMessageType.asyncWrapper,
          requestId: requestId,
          error: null,
          baseMsg: {
            type: responseType,
            data: responseData,
          },
          streamCtx: undefined,
        };
      }
    };

    switch (messageType) {
      case WebViewMessageType.asyncWrapper: {
        if (!baseMsg) {
          throw new Error("No base message given with async wrapper");
        }

        switch (baseMsg.type) {
          case WebViewMessageType.chatLoaded: {
            const responseData =
              hasData && !isAsyncWrapper
                ? { enableDebugFeatures: false, trackedFileCount: 0 }
                : {
                    enableAgentMode: true,
                    enableDebugFeatures: false,
                    memoryClassificationOnFirstToken: false,
                    enableRules: true,
                  };

            window.dispatchEvent(
              new MessageEvent("message", {
                data: createResponse(
                  WebViewMessageType.chatInitialize,
                  responseData,
                  hasData && !isAsyncWrapper ? false : true,
                ),
              }),
            );
            break;
          }
          case WebViewMessageType.getWorkspaceInfoRequest: {
            const responseData =
              hasData && !isAsyncWrapper
                ? { enableDebugFeatures: false, trackedFileCount: 0 }
                : { trackedFileCount: [1000, 500] };

            window.dispatchEvent(
              new MessageEvent("message", {
                data: createResponse(
                  WebViewMessageType.getWorkspaceInfoResponse,
                  responseData,
                  hasData && !isAsyncWrapper ? false : true,
                ),
              }),
            );
            break;
          }
          case ExchangeWebViewMessageType.loadConversationExchangesRequest: {
            const responseData = hasData && !isAsyncWrapper ? [] : { exchanges: [] };

            window.dispatchEvent(
              new MessageEvent("message", {
                data: createResponse(
                  ExchangeWebViewMessageType.loadConversationExchangesResponse,
                  responseData,
                ),
              }),
            );
            break;
          }
          case ExchangeWebViewMessageType.saveExchangesRequest: {
            const responseData = hasData && !isAsyncWrapper ? undefined : {};

            window.dispatchEvent(
              new MessageEvent("message", {
                data: createResponse(
                  ExchangeWebViewMessageType.saveExchangesResponse,
                  responseData,
                ),
              }),
            );
            break;
          }
          case ExchangeWebViewMessageType.deleteConversationExchangesRequest: {
            const responseData = hasData && !isAsyncWrapper ? undefined : {};

            window.dispatchEvent(
              new MessageEvent("message", {
                data: createResponse(
                  ExchangeWebViewMessageType.deleteConversationExchangesResponse,
                  responseData,
                ),
              }),
            );
            break;
          }
          case ToolUseStateWebViewMessageType.deleteConversationToolUseStatesRequest: {
            window.dispatchEvent(
              new MessageEvent("message", {
                data: createResponse(
                  ToolUseStateWebViewMessageType.deleteConversationToolUseStatesResponse,
                  { success: true },
                ),
              }),
            );
            break;
          }
          case RulesWebViewMessageType.getRulesListRequest: {
            // Mock rules response - return some ALWAYS_ATTACHED rules for testing
            const mockRules = [
              {
                type: 0, // RuleType.ALWAYS_ATTACHED
                path: ".augment/rules/coding-standards.md",
                content: "Follow TypeScript best practices",
                description: "Coding Standards",
              },
              {
                type: 1, // RuleType.MANUAL
                path: ".augment/rules/manual-rule.md",
                content: "Manual rule content",
                description: "Manual Rule",
              },
            ];
            // Add a small delay to ensure async handling
            setTimeout(() => {
              window.dispatchEvent(
                new MessageEvent("message", {
                  data: createResponse(
                    RulesWebViewMessageType.getRulesListResponse,
                    { rules: mockRules },
                    true, // Use async wrapper
                  ),
                }),
              );
            }, 0);
            break;
          }
          case AgentWebViewMessageType.checkHasEverUsedAgent: {
            window.dispatchEvent(
              new MessageEvent("message", {
                data: createResponse(AgentWebViewMessageType.checkHasEverUsedAgentResponse, false),
              }),
            );
            break;
          }
          case AgentWebViewMessageType.setHasEverUsedAgent: {
            window.dispatchEvent(
              new MessageEvent("message", {
                data: createResponse(WebViewMessageType.empty, undefined),
              }),
            );
            break;
          }
          case AgentWebViewMessageType.checkHasEverUsedRemoteAgent: {
            window.dispatchEvent(
              new MessageEvent("message", {
                data: createResponse(
                  AgentWebViewMessageType.checkHasEverUsedRemoteAgentResponse,
                  false,
                ),
              }),
            );
            break;
          }
          case AgentWebViewMessageType.setHasEverUsedRemoteAgent: {
            window.dispatchEvent(
              new MessageEvent("message", {
                data: createResponse(WebViewMessageType.empty, undefined),
              }),
            );
            break;
          }

          case ToolUseStateWebViewMessageType.loadConversationToolUseStatesRequest: {
            window.dispatchEvent(
              new MessageEvent("message", {
                data: createResponse(
                  ToolUseStateWebViewMessageType.loadConversationToolUseStatesResponse,
                  { toolUseStates: {} },
                ),
              }),
            );
            break;
          }
          case ToolUseStateWebViewMessageType.saveToolUseStatesRequest: {
            window.dispatchEvent(
              new MessageEvent("message", {
                data: createResponse(ToolUseStateWebViewMessageType.saveToolUseStatesResponse, {}),
              }),
            );
            break;
          }
          case WebViewMessageType.openConfirmationModal: {
            window.dispatchEvent(
              new MessageEvent("message", {
                data: createResponse(WebViewMessageType.confirmationModalResponse, { ok: true }),
              }),
            );
            break;
          }
          default:
            // For any unhandled message types, just return empty response
            window.dispatchEvent(
              new MessageEvent("message", {
                data: createResponse(WebViewMessageType.empty, undefined),
              }),
            );
            break;
        }
        break;
      }
      default:
        // Handle non-async wrapper messages if needed
        break;
    }
  }, 0);
};

describe("ChatModel", () => {
  let contextModel: SpecialContextInputModel;
  let mockRulesModel: any;

  // Mock the message broker
  const mockMsgBroker = {
    registerConsumer: vi.fn(),
    sendToSidecar: vi.fn(),
    postMessage: vi.fn(),
    onMessageFromExtension: vi.fn(),
    dispose: vi.fn(),
  } as unknown as MessageBroker;

  function mockUUID(id: number | string): `${string}-${string}-${string}-${string}-${string}` {
    return `mocked-uuid-a-b-${id}`;
  }
  function setupExtensionClientMocks(chatModel: ChatModel) {
    // Mock the clearMetadataFor method since it's an arrow function property
    chatModel.extensionClient.clearMetadataFor = vi.fn();
    chatModel.extensionClient.deleteConversationExchanges = vi.fn().mockResolvedValue(undefined);
    chatModel.extensionClient.deleteConversationToolUseStates = vi
      .fn()
      .mockResolvedValue(undefined);
    // Mock the openConfirmationModal method to return true (user confirms deletion)
    chatModel.extensionClient.openConfirmationModal = vi.fn().mockResolvedValue(true);
  }

  beforeEach(() => {
    _DO_NOT_USE_IN_APP_CODE_unlockAllChains();
    contextModel = new SpecialContextInputModel();

    let counter = 0;
    vi.spyOn(crypto, "randomUUID").mockImplementation(() => {
      return mockUUID(counter++);
    });
  });

  test("should initialize", async () => {
    // Mock the decidePersonaType method to return a synchronous result
    vi.spyOn(ConversationModel.prototype, "decidePersonaType").mockResolvedValue(
      PersonaType.DEFAULT,
    );

    // Create a local host instance like the working tests
    const testHost: HostInterface = {
      clientType: HostClientType.vscode,
      postMessage: mockPostMessage,
      getState: vi.fn(),
      setState: vi.fn(),
    };

    // Create a promise that resolves when onLoaded is called
    let onLoadedResolve: () => void;
    const onLoadedPromise = new Promise<void>((resolve) => {
      onLoadedResolve = resolve;
    });

    const chatModel = new ChatModel(
      new AsyncMsgSender((message) => testHost.postMessage(message)),
      testHost,
      contextModel,
      {
        onLoaded: () => {
          onLoadedResolve();
        },
      },
    );

    // Wait for the full initialization to complete
    await onLoadedPromise;

    expect(chatModel).toBeDefined();
    expect(chatModel.currentConversationId).not.toBeUndefined();
    expect(chatModel.flags).toBeDefined();
    //  Ensure the model passed in is the one used by the chat model
    expect(chatModel.specialContextInputModel).toBe(contextModel);
  });

  describe("initial conversation", () => {
    let host: HostInterface;

    beforeEach(() => {
      // Mock the decidePersonaType method for all tests in this describe block
      vi.spyOn(ConversationModel.prototype, "decidePersonaType").mockResolvedValue(
        PersonaType.DEFAULT,
      );

      host = {
        clientType: HostClientType.vscode,
        postMessage: mockPostMessage,
        getState: vi.fn(),
        setState: vi.fn(),
      };
    });

    test("should initialize with initial conversation", async () => {
      // Create a promise that resolves when onLoaded is called
      let onLoadedResolve: () => void;
      const onLoadedPromise = new Promise<void>((resolve) => {
        onLoadedResolve = resolve;
      });

      const chatModel = new ChatModel(
        new AsyncMsgSender((message) => host.postMessage(message)),
        host,
        contextModel,
        {
          onLoaded: () => {
            onLoadedResolve();
          },
        },
      );

      // Wait for the full initialization to complete
      await onLoadedPromise;

      // Verify we have a new conversation that's not equal to the very first "empty"
      // conversation.
      expect(chatModel.currentConversationId).not.toEqual(mockUUID(0));
      expect(chatModel.conversations).toEqual({
        // eslint-disable-next-line @typescript-eslint/naming-convention
        [chatModel.currentConversationId!]: {
          id: chatModel.currentConversationId,
          name: undefined,
          createdAtIso: expect.any(String),
          lastInteractedAtIso: expect.any(String),
          chatHistory: [],
          feedbackStates: {},
          draftExchange: undefined,
          draftActiveContextIds: undefined,
          selectedModelId: undefined,
          requestIds: [],
          isPinned: false,
          lastUrl: undefined,
          isShareable: false,
          toolUseStates: {},
          extraData: {},
          personaType: PersonaType.DEFAULT,
        },
      });
      expect(ConversationModel.prototype.decidePersonaType).toHaveBeenCalled();
    });

    test("should load previous conversation", async () => {
      const prevConvo = {
        id: mockUUID("prev"),
        name: "Example",
        createdAtIso: expect.any(String),
        lastInteractedAtIso: expect.any(String),
        chatHistory: [{}],
        feedbackStates: {},
        toolUseStates: {},
        draftExchange: undefined,
        requestIds: [],
        isShareable: false,
        extraData: {},
        personaType: PersonaType.DEFAULT,
      };
      (host.getState as any as MockInstance).mockReturnValue({
        currentConversationId: prevConvo.id,
        conversations: {
          // eslint-disable-next-line @typescript-eslint/naming-convention
          [prevConvo.id]: prevConvo,
        },
      });
      const chatModel = new ChatModel(
        new AsyncMsgSender((message) => host.postMessage(message)),
        host,
        contextModel,
      );

      // Wait for any pending promises to resolve
      await new Promise(process.nextTick);

      expect(chatModel.currentConversationId).toEqual(prevConvo.id);
      expect(chatModel.conversations).toEqual({
        // eslint-disable-next-line @typescript-eslint/naming-convention
        [prevConvo.id]: prevConvo,
      });
      expect(ConversationModel.prototype.decidePersonaType).not.toHaveBeenCalled();
    });

    test("should ignore invalid previous conversation", async () => {
      const invalidConvo = {
        id: mockUUID("prev"),
        name: undefined,
        createdAtIso: expect.any(String),
        lastInteractedAtIso: expect.any(String),
        chatHistory: [],
        feedbackStates: {},
        draftExchange: undefined,
        selectedModelId: undefined,
        requestIds: [],
        isPinned: false,
        lastUrl: undefined,
        isShareable: false,
        extraData: {},
      };
      (host.getState as any as MockInstance).mockReturnValue({
        currentConversationId: invalidConvo.id,
        conversations: {
          // eslint-disable-next-line @typescript-eslint/naming-convention
          [invalidConvo.id]: invalidConvo,
        },
      });
      // Create a promise that resolves when onLoaded is called
      let onLoadedResolve: () => void;
      const onLoadedPromise = new Promise<void>((resolve) => {
        onLoadedResolve = resolve;
      });

      const chatModel = new ChatModel(
        new AsyncMsgSender((message) => host.postMessage(message)),
        host,
        contextModel,
        {
          onLoaded: () => {
            onLoadedResolve();
          },
        },
      );

      // Wait for the full initialization to complete
      await onLoadedPromise;

      expect(chatModel.currentConversationId).not.toEqual(invalidConvo.id);
      expect(chatModel.conversations).toEqual({
        // eslint-disable-next-line @typescript-eslint/naming-convention
        [chatModel.currentConversationId!]: {
          id: chatModel.currentConversationId,
          name: undefined,
          createdAtIso: expect.any(String),
          lastInteractedAtIso: expect.any(String),
          chatHistory: [],
          feedbackStates: {},
          draftExchange: undefined,
          draftActiveContextIds: undefined,
          selectedModelId: undefined,
          requestIds: [],
          isPinned: false,
          lastUrl: undefined,
          isShareable: false,
          toolUseStates: {},
          extraData: {},
          personaType: PersonaType.DEFAULT,
          rootTaskUuid: undefined,
        },
      });
      expect(ConversationModel.prototype.decidePersonaType).toHaveBeenCalled();
    });

    test("should use initial conversation when provided", async () => {
      const prevConvo = {
        id: mockUUID("prev"),
        name: "Example",
        createdAtIso: expect.any(String),
        lastInteractedAtIso: expect.any(String),
        chatHistory: [],
        feedbackStates: {},
        draftExchange: undefined,
        requestIds: [],
        isPinned: false,
        lastUrl: undefined,
        isShareable: false,
        toolUseStates: {},
        extraData: {},
        personaType: PersonaType.DEFAULT,
      };
      const initialConvo: IConversation = {
        id: mockUUID("initial"),
        name: "Initial",
        createdAtIso: expect.any(String),
        lastInteractedAtIso: expect.any(String),
        chatHistory: [
          {
            /* eslint-disable @typescript-eslint/naming-convention */
            request_message: "Exmaple",
            response_text: "Example",
            /* eslint-enable @typescript-eslint/naming-convention */
            status: ExchangeStatus.success,
          },
        ],
        feedbackStates: {},
        draftExchange: undefined,
        requestIds: [],
        isPinned: false,
        lastUrl: undefined,
        // There is a shareable message here.
        isShareable: true,
        extraData: {},
        personaType: PersonaType.DEFAULT,
      };
      (host.getState as any as MockInstance).mockReturnValue({
        currentConversationId: prevConvo.id,
        conversations: {
          // eslint-disable-next-line @typescript-eslint/naming-convention
          [prevConvo.id]: prevConvo,
          // eslint-disable-next-line @typescript-eslint/naming-convention
          [initialConvo.id]: initialConvo,
        },
      });
      const chatModel = new ChatModel(
        new AsyncMsgSender((message) => host.postMessage(message)),
        host,
        contextModel,
        {
          initialConversation: initialConvo,
        },
      );

      // Wait for the async setCurrentConversation to complete
      await waitFor(() => {
        expect(chatModel.currentConversationId).toEqual(initialConvo.id);
      });

      // Check that both conversations are present
      expect(Object.keys(chatModel.conversations)).toContain(prevConvo.id);
      expect(Object.keys(chatModel.conversations)).toContain(initialConvo.id);

      // Check that the conversations have the expected properties
      expect(chatModel.conversations[prevConvo.id]).toMatchObject({
        id: prevConvo.id,
        name: prevConvo.name,
        chatHistory: [],
      });
      expect(chatModel.conversations[initialConvo.id]).toMatchObject({
        id: initialConvo.id,
        name: initialConvo.name,
        chatHistory: expect.arrayContaining([
          expect.objectContaining({
            /* eslint-disable @typescript-eslint/naming-convention */
            request_message: "Exmaple",
            response_text: "Example",
            /* eslint-enable @typescript-eslint/naming-convention */
          }),
        ]),
      });
      expect(ConversationModel.prototype.decidePersonaType).not.toHaveBeenCalled();
    });

    test("should preserve extraData from initial conversation", async () => {
      const initialConvo: IConversation = {
        id: mockUUID("agent-initial"),
        name: "Agent Conversation",
        createdAtIso: expect.any(String),
        lastInteractedAtIso: expect.any(String),
        chatHistory: [],
        feedbackStates: {},
        draftExchange: undefined,
        requestIds: [],
        isPinned: false,
        lastUrl: undefined,
        isShareable: false,
        toolUseStates: {},
        extraData: { isAgentConversation: true },
        personaType: PersonaType.DEFAULT,
      };

      (host.getState as any as MockInstance).mockReturnValue({
        currentConversationId: undefined,
        conversations: {},
      });

      const chatModel = new ChatModel(
        new AsyncMsgSender((message) => host.postMessage(message)),
        host,
        contextModel,
        {
          initialConversation: initialConvo,
        },
      );

      // Wait for the async setCurrentConversation to complete
      await waitFor(() => {
        expect(chatModel.currentConversationId).toEqual(initialConvo.id);
      });

      // Check that the conversation is present with correct extraData
      expect(chatModel.conversations[initialConvo.id]).toMatchObject({
        id: initialConvo.id,
        name: initialConvo.name,
        extraData: { isAgentConversation: true },
      });

      // Verify that the current conversation model also has the correct extraData
      expect(chatModel.currentConversationModel.extraData).toEqual({ isAgentConversation: true });
    });

    test("should sync conversation model during initializeSync when currentConversationId differs from conversation model id", async () => {
      const storedConvo: IConversation = {
        id: mockUUID(1),
        name: "Stored Conversation",
        createdAtIso: new Date().toISOString(),
        lastInteractedAtIso: new Date().toISOString(),
        chatHistory: [],
        feedbackStates: {},
        draftExchange: undefined,
        draftActiveContextIds: undefined,
        selectedModelId: undefined,
        requestIds: [],
        isPinned: false,
        lastUrl: undefined,
        isShareable: false,
        toolUseStates: {},
        extraData: {},
        personaType: PersonaType.DEFAULT,
      };

      // Mock the host to return state with a different currentConversationId
      // than what the conversation model will initially have
      (host.getState as any as MockInstance).mockReturnValue({
        currentConversationId: storedConvo.id,
        conversations: {
          // eslint-disable-next-line @typescript-eslint/naming-convention
          [storedConvo.id]: storedConvo,
        },
      });

      const chatModel = new ChatModel(
        new AsyncMsgSender((message) => host.postMessage(message)),
        host,
        contextModel,
      );

      // Immediately after construction (during initializeSync), the conversation model
      // should be synced with the stored state
      expect(chatModel.currentConversationId).toEqual(storedConvo.id);
      expect(chatModel.currentConversationModel.id).toEqual(storedConvo.id);
      expect(chatModel.conversations).toEqual({
        // eslint-disable-next-line @typescript-eslint/naming-convention
        [storedConvo.id]: storedConvo,
      });

      // Create a promise that resolves when onLoaded is called
      const onLoadedPromise = new Promise<void>((resolve) => {
        chatModel.options.onLoaded = resolve;
      });

      // Wait for the full initialization to complete
      await onLoadedPromise;

      // After full initialization, the sync should still be maintained
      expect(chatModel.currentConversationId).toEqual(storedConvo.id);
      expect(chatModel.currentConversationModel.id).toEqual(storedConvo.id);
    });

    test("should not sync during initializeSync when currentConversationId exists but conversation is not found", async () => {
      const missingConvoId = mockUUID(99);

      // Mock the host to return state with a currentConversationId that doesn't exist in conversations
      (host.getState as any as MockInstance).mockReturnValue({
        currentConversationId: missingConvoId,
        conversations: {}, // Empty conversations object
      });

      const chatModel = new ChatModel(
        new AsyncMsgSender((message) => host.postMessage(message)),
        host,
        contextModel,
      );

      // Immediately after construction (during initializeSync), the conversation model
      // should not be synced since the conversation doesn't exist in the conversations object
      // The currentConversationId should still be set from the stored state
      expect(chatModel.currentConversationId).toEqual(missingConvoId);
      // But the conversation model should maintain its original ID (which would be different)
      expect(chatModel.currentConversationModel.id).not.toEqual(missingConvoId);
      expect(chatModel.conversations).toEqual({});

      // Create a promise that resolves when onLoaded is called
      const onLoadedPromise = new Promise<void>((resolve) => {
        chatModel.options.onLoaded = resolve;
      });

      // Wait for the full initialization to complete
      await onLoadedPromise;

      // After full initialization, a new conversation should be created
      expect(chatModel.currentConversationId).not.toEqual(missingConvoId);
      expect(chatModel.currentConversationModel.id).toEqual(chatModel.currentConversationId);
    });
  });

  describe("conversation navigation", () => {
    let host: HostInterface;
    let contextModel: SpecialContextInputModel;
    let chatModel: ChatModel;

    beforeEach(async () => {
      host = {
        clientType: HostClientType.vscode,
        postMessage: mockPostMessage,
        getState: vi.fn(),
        setState: vi.fn(),
      };
      contextModel = new SpecialContextInputModel();

      // Mock the decidePersonaType method
      vi.spyOn(ConversationModel.prototype, "decidePersonaType").mockResolvedValue(
        PersonaType.DEFAULT,
      );

      // Mock conversations with different timestamps
      const now = new Date();
      const conversations = {
        [mockUUID(0)]: {
          id: mockUUID(0),
          name: "First",
          createdAtIso: new Date(now.getTime() - 3000).toISOString(), // Created 3 seconds ago
          lastInteractedAtIso: new Date(now.getTime() - 1000).toISOString(), // Interacted 1 second ago
          chatHistory: [],
          feedbackStates: {},
          draftExchange: undefined,
          requestIds: [],
          isPinned: false,
          lastUrl: undefined,
          isShareable: false,
          extraData: {},
          personaType: PersonaType.DEFAULT,
        },
        [mockUUID(1)]: {
          id: mockUUID(1),
          name: "Second",
          createdAtIso: new Date(now.getTime() - 2000).toISOString(), // Created 2 seconds ago
          lastInteractedAtIso: new Date(now.getTime() - 3000).toISOString(), // Interacted 3 seconds ago
          chatHistory: [],
          feedbackStates: {},
          draftExchange: undefined,
          requestIds: [],
          isPinned: false,
          lastUrl: undefined,
          isShareable: false,
          extraData: {},
          personaType: PersonaType.DEFAULT,
        },
        [mockUUID(2)]: {
          id: mockUUID(2),
          name: "Third",
          createdAtIso: new Date(now.getTime() - 1000).toISOString(), // Created 1 second ago
          lastInteractedAtIso: new Date(now.getTime() - 2000).toISOString(), // Interacted 2 seconds ago
          chatHistory: [],
          feedbackStates: {},
          draftExchange: undefined,
          requestIds: [],
          isPinned: false,
          lastUrl: undefined,
          isShareable: false,
          extraData: {},
          personaType: PersonaType.DEFAULT,
        },
      };

      (host.getState as any as MockInstance).mockReturnValue({
        currentConversationId: mockUUID(1),
        conversations,
      });

      chatModel = new ChatModel(
        new AsyncMsgSender((message) => host.postMessage(message)),
        host,
        contextModel,
      );

      // Wait for any pending promises to resolve
      await new Promise(process.nextTick);
    });

    describe("conversation ordering", () => {
      test("orderedConversations returns conversations ordered by creation time", () => {
        const ordered = chatModel.orderedConversations("createdAt", "desc");
        expect(ordered.map((c) => c.id)).toEqual([mockUUID(2), mockUUID(1), mockUUID(0)]);
      });

      test("orderedConversations with lastMessageTimestamp returns conversations ordered by last message time", () => {
        // Current conversation ID is 1, since we set it in the beforeEach
        expect(chatModel.currentConversationId).toBe(mockUUID(1));

        // Add message timestamps to the conversations
        const now = new Date();
        const conversations = chatModel.conversations;

        // Add a message with timestamp to conversation 0 (3 seconds ago)
        const convo0 = { ...conversations[mockUUID(0)] };
        convo0.chatHistory = [
          {
            /* eslint-disable @typescript-eslint/naming-convention */
            request_message: "Hello",
            response_text: "Hi",
            request_id: "msg-0",
            timestamp: new Date(now.getTime() - 3000).toISOString(),
            /* eslint-enable @typescript-eslint/naming-convention */
            status: ExchangeStatus.success,
          },
        ];

        // Add a message with timestamp to conversation 1 (1 second ago)
        const convo1 = { ...conversations[mockUUID(1)] };
        convo1.chatHistory = [
          {
            /* eslint-disable @typescript-eslint/naming-convention */
            request_message: "Hello",
            response_text: "Hi",
            request_id: "msg-1",
            timestamp: new Date(now.getTime() - 1000).toISOString(),
            /* eslint-enable @typescript-eslint/naming-convention */
            status: ExchangeStatus.success,
          },
        ];

        // Add a message with timestamp to conversation 2 (2 seconds ago)
        const convo2 = { ...conversations[mockUUID(2)] };
        convo2.chatHistory = [
          {
            /* eslint-disable @typescript-eslint/naming-convention */
            request_message: "Hello",
            response_text: "Hi",
            request_id: "msg-2",
            timestamp: new Date(now.getTime() - 2000).toISOString(),
            /* eslint-enable @typescript-eslint/naming-convention */
            status: ExchangeStatus.success,
          },
        ];

        // Update the conversations in the chat model
        (chatModel as any)._state.conversations = {
          [mockUUID(0)]: convo0,
          [mockUUID(1)]: convo1,
          [mockUUID(2)]: convo2,
        };

        // Order should be: 1 (1 second ago), 2 (2 seconds ago), 0 (3 seconds ago)
        const ordered = chatModel.orderedConversations("lastMessageTimestamp", "desc");
        expect(ordered.map((c: IConversation) => c.id)).toEqual([
          mockUUID(1),
          mockUUID(2),
          mockUUID(0),
        ]);
      });

      test("orderedConversations allows custom sorting parameters", () => {
        // Test sorting by createdAt in ascending order (oldest first)
        const orderedByCreatedAsc = chatModel.orderedConversations("createdAt", "asc");
        expect(orderedByCreatedAsc.map((c: IConversation) => c.id)).toEqual([
          mockUUID(0),
          mockUUID(1),
          mockUUID(2),
        ]);

        // Test sorting by createdAt in descending order (newest first)
        const orderedByCreatedDesc = chatModel.orderedConversations("createdAt", "desc");
        expect(orderedByCreatedDesc.map((c: IConversation) => c.id)).toEqual([
          mockUUID(2),
          mockUUID(1),
          mockUUID(0),
        ]);

        // Test with filter function
        const filterFn = (conversation: IConversation) => conversation.id !== mockUUID(1);
        const filteredConversations = chatModel.orderedConversations("createdAt", "asc", filterFn);
        expect(filteredConversations.map((c: IConversation) => c.id)).toEqual([
          mockUUID(0),
          mockUUID(2),
        ]);
      });

      test("orderedConversations sorts by lastMessageTimestamp", () => {
        // Add message timestamps to the conversations
        const now = new Date();
        const conversations = chatModel.conversations;

        // Add a message with timestamp to conversation 0 (3 seconds ago)
        const convo0 = { ...conversations[mockUUID(0)] };
        convo0.chatHistory = [
          {
            /* eslint-disable @typescript-eslint/naming-convention */
            request_message: "Hello",
            response_text: "Hi",
            request_id: "msg-0",
            timestamp: new Date(now.getTime() - 3000).toISOString(),
            /* eslint-enable @typescript-eslint/naming-convention */
            status: ExchangeStatus.success,
          },
        ];

        // Add a message with timestamp to conversation 1 (1 second ago)
        const convo1 = { ...conversations[mockUUID(1)] };
        convo1.chatHistory = [
          {
            /* eslint-disable @typescript-eslint/naming-convention */
            request_message: "Hello",
            response_text: "Hi",
            request_id: "msg-1",
            timestamp: new Date(now.getTime() - 1000).toISOString(),
            /* eslint-enable @typescript-eslint/naming-convention */
            status: ExchangeStatus.success,
          },
        ];

        // Add a message with timestamp to conversation 2 (2 seconds ago)
        const convo2 = { ...conversations[mockUUID(2)] };
        convo2.chatHistory = [
          {
            /* eslint-disable @typescript-eslint/naming-convention */
            request_message: "Hello",
            response_text: "Hi",
            request_id: "msg-2",
            timestamp: new Date(now.getTime() - 2000).toISOString(),
            /* eslint-enable @typescript-eslint/naming-convention */
            status: ExchangeStatus.success,
          },
        ];

        // Update the conversations in the chat model
        (chatModel as any)._state.conversations = {
          [mockUUID(0)]: convo0,
          [mockUUID(1)]: convo1,
          [mockUUID(2)]: convo2,
        };

        // Test sorting by lastMessageTimestamp in descending order (most recent message first)
        const orderedByMessageDesc = chatModel.orderedConversations("lastMessageTimestamp", "desc");
        expect(orderedByMessageDesc.map((c: IConversation) => c.id)).toEqual([
          mockUUID(1), // 1 second ago
          mockUUID(2), // 2 seconds ago
          mockUUID(0), // 3 seconds ago
        ]);

        // Test sorting by lastMessageTimestamp in ascending order (oldest message first)
        const orderedByMessageAsc = chatModel.orderedConversations("lastMessageTimestamp", "asc");
        expect(orderedByMessageAsc.map((c: IConversation) => c.id)).toEqual([
          mockUUID(0), // 3 seconds ago
          mockUUID(2), // 2 seconds ago
          mockUUID(1), // 1 second ago
        ]);
      });

      test("next and previous conversation use lastMessageTimestamp order", () => {
        // By default, orderedConversations sorts by lastMessageTimestamp in descending order
        // In the test setup, the current conversation is mockUUID(1)
        // We need to add message timestamps to test this properly

        // Add message timestamps to the conversations
        const now = new Date();
        const conversations = chatModel.conversations;

        // Add a message with timestamp to conversation 0 (3 seconds ago)
        const convo0 = { ...conversations[mockUUID(0)] };
        convo0.chatHistory = [
          {
            /* eslint-disable @typescript-eslint/naming-convention */
            request_message: "Hello",
            response_text: "Hi",
            request_id: "msg-0",
            timestamp: new Date(now.getTime() - 3000).toISOString(),
            /* eslint-enable @typescript-eslint/naming-convention */
            status: ExchangeStatus.success,
          },
        ];

        // Add a message with timestamp to conversation 1 (1 second ago)
        const convo1 = { ...conversations[mockUUID(1)] };
        convo1.chatHistory = [
          {
            /* eslint-disable @typescript-eslint/naming-convention */
            request_message: "Hello",
            response_text: "Hi",
            request_id: "msg-1",
            timestamp: new Date(now.getTime() - 1000).toISOString(),
            /* eslint-enable @typescript-eslint/naming-convention */
            status: ExchangeStatus.success,
          },
        ];

        // Add a message with timestamp to conversation 2 (2 seconds ago)
        const convo2 = { ...conversations[mockUUID(2)] };
        convo2.chatHistory = [
          {
            /* eslint-disable @typescript-eslint/naming-convention */
            request_message: "Hello",
            response_text: "Hi",
            request_id: "msg-2",
            timestamp: new Date(now.getTime() - 2000).toISOString(),
            /* eslint-enable @typescript-eslint/naming-convention */
            status: ExchangeStatus.success,
          },
        ];

        // Update the conversations in the chat model
        (chatModel as any)._state.conversations = {
          [mockUUID(0)]: convo0,
          [mockUUID(1)]: convo1,
          [mockUUID(2)]: convo2,
        };

        // Now the next conversation should be mockUUID(2) (2 seconds ago)
        // since we're sorting by lastMessageTimestamp and the current conversation is mockUUID(1) (1 second ago)
        expect(chatModel.nextConversation?.id).toBe(mockUUID(2));
        expect(chatModel.previousConversation?.id).toBe(undefined);
      });
    });

    describe("no conversations", () => {
      test("returns undefined for both when there are no conversations", () => {
        (host.getState as any as MockInstance).mockReturnValue({
          currentConversationId: undefined,
          conversations: [],
        });
        chatModel = new ChatModel(
          new AsyncMsgSender((message) => host.postMessage(message)),
          host,
          contextModel,
        );
        expect(chatModel.nextConversation).toBeUndefined();
        expect(chatModel.previousConversation).toBeUndefined();
      });
    });
  });

  describe("lastMessageTimestamp", () => {
    let host: HostInterface;
    let contextModel: SpecialContextInputModel;
    let chatModel: ChatModel;

    beforeEach(async () => {
      host = {
        clientType: HostClientType.vscode,
        postMessage: mockPostMessage,
        getState: vi.fn(),
        setState: vi.fn(),
      };
      contextModel = new SpecialContextInputModel();

      // Mock the decidePersonaType method
      vi.spyOn(ConversationModel.prototype, "decidePersonaType").mockResolvedValue(
        PersonaType.DEFAULT,
      );

      // Create a mock timestamp for testing
      const mockTimestamp = "2023-05-15T10:30:00.000Z";

      // Create a conversation with a message that has a timestamp
      const conversations = {
        [mockUUID(0)]: {
          id: mockUUID(0),
          name: "Conversation with timestamp",
          createdAtIso: new Date().toISOString(),
          lastInteractedAtIso: new Date().toISOString(),
          chatHistory: [
            {
              /* eslint-disable @typescript-eslint/naming-convention */
              request_message: "Hello",
              response_text: "Hi there",
              request_id: "test-request-id",
              timestamp: mockTimestamp,
              /* eslint-enable @typescript-eslint/naming-convention */
              status: ExchangeStatus.success,
            },
          ],
          feedbackStates: {},
          draftExchange: undefined,
          requestIds: [],
          isPinned: false,
          lastUrl: undefined,
          isShareable: true,
          extraData: {},
          personaType: PersonaType.DEFAULT,
        },
      };

      (host.getState as any as MockInstance).mockReturnValue({
        currentConversationId: mockUUID(0),
        conversations,
      });

      chatModel = new ChatModel(
        new AsyncMsgSender((message) => host.postMessage(message)),
        host,
        contextModel,
      );

      // Wait for the conversation to be loaded
      await waitFor(() => {
        expect(chatModel.currentConversationId).toBe(mockUUID(0));
      });
    });

    test("returns the timestamp of the last message", async () => {
      await waitFor(() => {
        expect(chatModel.lastMessageTimestamp).toBe("2023-05-15T10:30:00.000Z");
      });
    });

    test("returns undefined when there are no messages", async () => {
      // Create a new chat model with an empty conversation
      const emptyConversations = {
        [mockUUID(1)]: {
          id: mockUUID(1),
          name: "Empty conversation",
          createdAtIso: new Date().toISOString(),
          lastInteractedAtIso: new Date().toISOString(),
          chatHistory: [],
          feedbackStates: {},
          draftExchange: undefined,
          requestIds: [],
          isPinned: false,
          lastUrl: undefined,
          isShareable: false,
          extraData: {},
          personaType: PersonaType.DEFAULT,
        },
      };

      (host.getState as any as MockInstance).mockReturnValue({
        currentConversationId: mockUUID(1),
        conversations: emptyConversations,
      });

      const emptyChatModel = new ChatModel(
        new AsyncMsgSender((message) => host.postMessage(message)),
        host,
        contextModel,
        mockRulesModel,
      );

      // Wait for any pending promises to resolve
      await new Promise(process.nextTick);

      expect(emptyChatModel.lastMessageTimestamp).toBeUndefined();
    });
  });

  describe("popConversation", () => {
    let host: HostInterface;
    let contextModel: SpecialContextInputModel;
    let chatModel: ChatModel;
    beforeEach(async () => {
      host = {
        clientType: HostClientType.vscode,
        postMessage: mockPostMessage,
        getState: vi.fn(),
        setState: vi.fn(),
      };
      contextModel = new SpecialContextInputModel();
      mockRulesModel = new RulesModel(mockMsgBroker, false);

      // Reset the UUID counter for consistent test UUIDs
      let counter = 0;
      vi.spyOn(crypto, "randomUUID").mockImplementation(() => {
        return mockUUID(counter++);
      });

      // Mock the decidePersonaType method
      vi.spyOn(ConversationModel.prototype, "decidePersonaType").mockResolvedValue(
        PersonaType.DEFAULT,
      );

      // Mock conversations with different timestamps
      const conversations = {
        [mockUUID(0)]: {
          id: mockUUID(0),
          name: "First",
          createdAtIso: new Date("2023-01-01").toISOString(),
          lastInteractedAtIso: new Date("2023-01-01").toISOString(),
          chatHistory: [],
          feedbackStates: {},
          draftExchange: undefined,
          requestIds: [],
          isPinned: false,
          lastUrl: undefined,
          isShareable: false,
          extraData: {},
          personaType: PersonaType.DEFAULT,
        },
        [mockUUID(1)]: {
          id: mockUUID(1),
          name: "Second",
          createdAtIso: new Date("2023-01-02").toISOString(),
          lastInteractedAtIso: new Date("2023-01-02").toISOString(),
          chatHistory: [],
          feedbackStates: {},
          draftExchange: undefined,
          requestIds: [],
          isPinned: false,
          lastUrl: undefined,
          isShareable: false,
          extraData: {},
          personaType: PersonaType.DEFAULT,
        },
        [mockUUID(2)]: {
          id: mockUUID(2),
          name: "Third",
          createdAtIso: new Date("2023-01-03").toISOString(),
          lastInteractedAtIso: new Date("2023-01-03").toISOString(),
          chatHistory: [],
          feedbackStates: {},
          draftExchange: undefined,
          requestIds: [],
          isPinned: false,
          lastUrl: undefined,
          isShareable: false,
          extraData: {},
          personaType: PersonaType.DEFAULT,
        },
      };

      (host.getState as any as MockInstance).mockReturnValue({
        currentConversationId: mockUUID(1),
        conversations,
      });

      // Create a promise that resolves when onLoaded is called
      let onLoadedResolve: () => void;
      const onLoadedPromise = new Promise<void>((resolve) => {
        onLoadedResolve = resolve;
      });

      chatModel = new ChatModel(
        new AsyncMsgSender((message) => host.postMessage(message)),
        host,
        contextModel,
        {
          onLoaded: () => {
            onLoadedResolve();
          },
          debounceConfig: {
            wait: 100, // 100ms for tests
            maxWait: 200, // 200ms max wait for tests
          },
        },
      );

      // Wait for the full initialization to complete
      await onLoadedPromise;
    });

    test("removes current conversation and updates state", async () => {
      // Setup extension client mocks
      setupExtensionClientMocks(chatModel);

      const initialConversations = chatModel.conversations;
      const currentId = chatModel.currentConversationId;

      await chatModel.popCurrentConversation();

      // Wait for async operations to complete
      await new Promise((resolve) => setTimeout(resolve, 10));

      // Verify conversation was removed
      expect(currentId).toBe(mockUUID(1));
      expect(chatModel.conversations).not.toHaveProperty(currentId!);
      expect(Object.keys(chatModel.conversations).length).toBe(
        Object.keys(initialConversations).length - 1,
      );
      // Wait for debounce to trigger state update (debounce is 100ms for tests)
      await new Promise((resolve) => setTimeout(resolve, 300));
      expect(host.setState).toHaveBeenCalled();
    });

    test("selects next conversation when available", async () => {
      // Add message timestamps to the conversations to test with lastMessageTimestamp sorting
      const now = new Date();
      const conversations = chatModel.conversations;

      // Add a message with timestamp to conversation 0 (3 seconds ago)
      const convo0 = { ...conversations[mockUUID(0)] };
      convo0.chatHistory = [
        {
          /* eslint-disable @typescript-eslint/naming-convention */
          request_message: "Hello",
          response_text: "Hi",
          request_id: "msg-0",
          timestamp: new Date(now.getTime() - 3000).toISOString(),
          /* eslint-enable @typescript-eslint/naming-convention */
          status: ExchangeStatus.success,
        },
      ];

      // Add a message with timestamp to conversation 1 (1 second ago)
      const convo1 = { ...conversations[mockUUID(1)] };
      convo1.chatHistory = [
        {
          /* eslint-disable @typescript-eslint/naming-convention */
          request_message: "Hello",
          response_text: "Hi",
          request_id: "msg-1",
          timestamp: new Date(now.getTime() - 1000).toISOString(),
          /* eslint-enable @typescript-eslint/naming-convention */
          status: ExchangeStatus.success,
        },
      ];

      // Add a message with timestamp to conversation 2 (2 seconds ago)
      const convo2 = { ...conversations[mockUUID(2)] };
      convo2.chatHistory = [
        {
          /* eslint-disable @typescript-eslint/naming-convention */
          request_message: "Hello",
          response_text: "Hi",
          request_id: "msg-2",
          timestamp: new Date(now.getTime() - 2000).toISOString(),
          /* eslint-enable @typescript-eslint/naming-convention */
          status: ExchangeStatus.success,
        },
      ];

      // Update the conversations in the chat model
      (chatModel as any)._state.conversations = {
        [mockUUID(0)]: convo0,
        [mockUUID(1)]: convo1,
        [mockUUID(2)]: convo2,
      };

      // Setup extension client mocks
      setupExtensionClientMocks(chatModel);

      // In the orderedConversations by lastMessageTimestamp, mockUUID(2) is the next one after mockUUID(1)
      await chatModel.popCurrentConversation();

      // Wait for async operations to complete
      await new Promise((resolve) => setTimeout(resolve, 10));

      // Wait for the conversation to be updated
      await waitFor(() => {
        expect(chatModel.currentConversationId).toBe(mockUUID(2));
      });
    });

    test("selects previous conversation when no next available", async () => {
      // Create fresh conversations without chat history to ensure predictable ordering
      const freshConversations = {
        [mockUUID(0)]: {
          id: mockUUID(0),
          name: "First",
          createdAtIso: new Date("2023-01-01").toISOString(),
          lastInteractedAtIso: new Date("2023-01-01").toISOString(),
          chatHistory: [],
          feedbackStates: {},
          draftExchange: undefined,
          requestIds: [],
          isPinned: false,
          lastUrl: undefined,
          isShareable: false,
          extraData: {},
          personaType: PersonaType.DEFAULT,
        },
        [mockUUID(1)]: {
          id: mockUUID(1),
          name: "Second",
          createdAtIso: new Date("2023-01-02").toISOString(),
          lastInteractedAtIso: new Date("2023-01-02").toISOString(),
          chatHistory: [],
          feedbackStates: {},
          draftExchange: undefined,
          requestIds: [],
          isPinned: false,
          lastUrl: undefined,
          isShareable: false,
          extraData: {},
          personaType: PersonaType.DEFAULT,
        },
        [mockUUID(2)]: {
          id: mockUUID(2),
          name: "Third",
          createdAtIso: new Date("2023-01-03").toISOString(),
          lastInteractedAtIso: new Date("2023-01-03").toISOString(),
          chatHistory: [],
          feedbackStates: {},
          draftExchange: undefined,
          requestIds: [],
          isPinned: false,
          lastUrl: undefined,
          isShareable: false,
          extraData: {},
          personaType: PersonaType.DEFAULT,
        },
      };

      // Set current conversation to the last one in the ordered list (mockUUID(0))
      // When ordered by lastMessageTimestamp (desc), since there are no messages, it falls back to createdAt
      // Order by createdAt (desc): mockUUID(2), mockUUID(1), mockUUID(0)
      // So mockUUID(0) is the last one, and the previous one should be mockUUID(1)
      (host.getState as any as MockInstance).mockReturnValue({
        currentConversationId: mockUUID(0),
        conversations: freshConversations,
      });
      chatModel = new ChatModel(
        new AsyncMsgSender((message) => host.postMessage(message)),
        host,
        contextModel,
        mockRulesModel,
      );

      await chatModel.popCurrentConversation();

      // Wait for the conversation to be updated
      await waitFor(() => {
        expect(chatModel.currentConversationId).toBe(mockUUID(1));
      });
    });

    test("creates new conversation when removing last one", async () => {
      // Setup single conversation state
      const singleConversation = {
        [mockUUID(0)]: chatModel.conversations[mockUUID(0)],
      };
      (host.getState as any as MockInstance).mockReturnValue({
        currentConversationId: mockUUID(0),
        conversations: singleConversation,
      });
      chatModel = new ChatModel(
        new AsyncMsgSender((message) => host.postMessage(message)),
        host,
        contextModel,
        mockRulesModel,
      );

      // Setup extension client mocks
      setupExtensionClientMocks(chatModel);

      // Wait for any pending promises to resolve
      await new Promise(process.nextTick);

      await chatModel.popCurrentConversation();

      // Wait for any pending promises to resolve after popCurrentConversation
      await new Promise(process.nextTick);

      // Should create a new conversation
      expect(Object.keys(chatModel.conversations)).toHaveLength(1);
      expect(chatModel.currentConversationId).not.toBe(mockUUID(0));
    });
  });

  describe("image handling", () => {
    let host: HostInterface;
    let contextModel: SpecialContextInputModel;
    let chatModel: ChatModel;

    beforeEach(() => {
      host = {
        clientType: HostClientType.vscode,
        postMessage: mockPostMessage,
        getState: vi.fn(),
        setState: vi.fn(),
      };
      contextModel = new SpecialContextInputModel();
      chatModel = new ChatModel(
        new AsyncMsgSender((message) => host.postMessage(message)),
        host,
        contextModel,
        mockRulesModel,
      );
    });

    describe("findImagesInJson", () => {
      test("finds images in simple JSON", () => {
        const json = {
          type: "doc",
          content: [
            {
              type: "file",
              attrs: { src: "image1.png" },
            },
          ],
        };
        const images = (chatModel as any).findImagesInJson(json);
        expect(images).toEqual(["image1.png"]);
      });

      test("finds images in nested JSON", () => {
        const json = {
          type: "doc",
          content: [
            {
              type: "paragraph",
              content: [
                {
                  type: "file",
                  attrs: { src: "image1.png" },
                },
                {
                  type: "paragraph",
                  content: [
                    {
                      type: "file",
                      attrs: { src: "image2.png" },
                    },
                  ],
                },
              ],
            },
          ],
        };
        const images = (chatModel as any).findImagesInJson(json);
        expect(images).toEqual(["image1.png", "image2.png"]);
      });

      test("handles JSON with no images", () => {
        const json = {
          type: "doc",
          content: [
            {
              type: "paragraph",
              content: [{ type: "text", text: "Hello" }],
            },
          ],
        };
        const images = (chatModel as any).findImagesInJson(json);
        expect(images).toEqual([]);
      });
    });

    describe("findImagesInStructuredRequest", () => {
      test("finds image IDs in structured request", () => {
        const nodes = [
          // eslint-disable-next-line @typescript-eslint/naming-convention
          { type: ChatRequestNodeType.IMAGE_ID, image_id_node: { image_id: "image1.png" } },
          { type: ChatRequestNodeType.TEXT, text: "some text" },
          // eslint-disable-next-line @typescript-eslint/naming-convention
          { type: ChatRequestNodeType.IMAGE_ID, image_id_node: { image_id: "image2.png" } },
        ];
        const images = (chatModel as any).findImagesInStructuredRequest(nodes);
        expect(images).toEqual(["image1.png", "image2.png"]);
      });

      test("handles empty node list", () => {
        const nodes: any[] = [];
        const images = (chatModel as any).findImagesInStructuredRequest(nodes);
        expect(images).toEqual([]);
      });

      test("handles nodes with no images", () => {
        const nodes = [
          { type: ChatRequestNodeType.TEXT, text: "text1" },
          { type: ChatRequestNodeType.TEXT, text: "text2" },
        ];
        const images = (chatModel as any).findImagesInStructuredRequest(nodes);
        expect(images).toEqual([]);
      });
    });

    describe("deleteImagesInExchange", () => {
      test("deletes images from both rich text and structured request", async () => {
        const deleteImageSpy = vi.spyOn(chatModel, "deleteImage").mockResolvedValue(undefined);
        const exchange = {
          // eslint-disable-next-line @typescript-eslint/naming-convention
          rich_text_json_repr: {
            type: "doc",
            content: [
              {
                type: "file",
                attrs: { src: "image1.png" },
              },
            ],
          },
          // eslint-disable-next-line @typescript-eslint/naming-convention
          structured_request_nodes: [
            // eslint-disable-next-line @typescript-eslint/naming-convention
            { type: ChatRequestNodeType.IMAGE_ID, image_id_node: { image_id: "image2.png" } },
          ],
        };

        await (chatModel as any).deleteImagesInExchange(exchange);

        expect(deleteImageSpy).toHaveBeenCalledTimes(2);
        expect(deleteImageSpy).toHaveBeenCalledWith("image1.png");
        expect(deleteImageSpy).toHaveBeenCalledWith("image2.png");
      });

      test("handles exchange with no images", async () => {
        const deleteImageSpy = vi.spyOn(chatModel, "deleteImage").mockResolvedValue(undefined);
        const exchange = {
          // eslint-disable-next-line @typescript-eslint/naming-convention
          rich_text_json_repr: {
            type: "doc",
            content: [{ type: "text", text: "Hello" }],
          },
          // eslint-disable-next-line @typescript-eslint/naming-convention
          structured_request_nodes: [{ type: ChatRequestNodeType.TEXT, text: "text" }],
        };

        await (chatModel as any).deleteImagesInExchange(exchange);

        expect(deleteImageSpy).not.toHaveBeenCalled();
      });

      test("handles exchange with missing fields", async () => {
        const deleteImageSpy = vi.spyOn(chatModel, "deleteImage").mockResolvedValue(undefined);
        const exchange = {};

        await (chatModel as any).deleteImagesInExchange(exchange);

        expect(deleteImageSpy).not.toHaveBeenCalled();
      });
    });
  });

  describe("handleMessageFromExtension", () => {
    let chatModel: ChatModel;
    let mockChatModeModel: ChatModeModel;
    let setCurrentConversationSpy: MockInstance;
    let consoleWarnSpy: MockInstance;

    beforeEach(async () => {
      // Basic setup for ChatModel, similar to other test blocks
      // host and contextModel are available from the outer describe's beforeEach
      chatModel = new ChatModel(
        new AsyncMsgSender((message) => host.postMessage(message)),
        host,
        contextModel,
        mockRulesModel,
      );
      // Wait for initial conversation to be set up
      await new Promise(process.nextTick);

      // Mock ChatModeModel
      mockChatModeModel = {
        setToLocal: vi.fn().mockResolvedValue(undefined),
        handleSetToThreadType: vi.fn().mockResolvedValue(undefined),
      } as any;

      setCurrentConversationSpy = vi
        .spyOn(chatModel, "setCurrentConversation")
        .mockResolvedValue(undefined as any);
      consoleWarnSpy = vi.spyOn(console, "warn").mockImplementation(() => {});
    });

    afterEach(() => {
      consoleWarnSpy.mockRestore();
    });

    const simulateNewThreadMessage = (messageData?: NewThreadData) => {
      const message: NewThread = {
        type: WebViewMessageType.newThread,
        data: messageData ?? {}, // Ensure data is always an object
      };

      chatModel.handleMessageFromExtension({ data: message } as MessageEvent<NewThread>);
      // Wait for async operations within handleMessageFromExtension to complete
      return new Promise(process.nextTick);
    };

    test("should handle newThread message with mode 'agent'", async () => {
      chatModel.setChatModeModel(mockChatModeModel);
      await simulateNewThreadMessage({ mode: "agent" });

      expect(setCurrentConversationSpy).toHaveBeenCalledTimes(1);
      expect(consoleWarnSpy).not.toHaveBeenCalled();
    });

    test("should handle newThread message with mode 'chat'", async () => {
      chatModel.setChatModeModel(mockChatModeModel);
      await simulateNewThreadMessage({ mode: "chat" });

      expect(setCurrentConversationSpy).toHaveBeenCalledTimes(1);
      expect(consoleWarnSpy).not.toHaveBeenCalled();
    });

    test("should handle newThread message with an invalid mode", async () => {
      chatModel.setChatModeModel(mockChatModeModel);
      await simulateNewThreadMessage({ mode: "unknown" });

      expect(setCurrentConversationSpy).toHaveBeenCalledTimes(1);
      expect(consoleWarnSpy).toHaveBeenCalledWith("Unknown chat mode:", "unknown");
    });

    test("should handle newThread message without mode in data", async () => {
      chatModel.setChatModeModel(mockChatModeModel);
      await simulateNewThreadMessage({}); // Data is an empty object, so mode is undefined

      expect(setCurrentConversationSpy).toHaveBeenCalledTimes(1);
      expect(consoleWarnSpy).not.toHaveBeenCalled();
    });

    test("should handle newThread message without data payload", async () => {
      chatModel.setChatModeModel(mockChatModeModel);
      await simulateNewThreadMessage(undefined); // No data payload, simulateNewThreadMessage ensures data: {}

      expect(setCurrentConversationSpy).toHaveBeenCalledTimes(1);
      expect(consoleWarnSpy).not.toHaveBeenCalled();
    });

    test("should handle newThread message with mode when ChatModeModel is not set", async () => {
      // DO NOT call chatModel.setChatModeModel(mockChatModeModel);
      await simulateNewThreadMessage({ mode: "agent" });

      expect(setCurrentConversationSpy).toHaveBeenCalledTimes(1);
      expect(consoleWarnSpy).toHaveBeenCalledWith(
        "ChatModeModel not available, cannot set mode:",
        "agent",
      );
    });
  });
});
