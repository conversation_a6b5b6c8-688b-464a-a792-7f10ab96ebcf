import { isVSCodeHost } from "$common-webviews/src/common/hosts/vscode/vscode";
import { ChatMode } from "@augment-internal/sidecar-libs/src/chat/chat-types";
import {
  AgentSessionEventName,
  FlushMemoriesData,
  FlushMemoriesDebugFlag,
  RememberToolCaller,
} from "@augment-internal/sidecar-libs/src/metrics/types";
import throttle from "lodash/throttle";
import type { Readable, Writable } from "svelte/store";
import { derived, get, writable } from "svelte/store";
import {
  AgentExchangeStatus,
  ChatItemType,
  ExchangeStatus,
  type ExchangeWithStatus,
  isChatItemExchangeWithStatus,
  isChatItemSuccessfulExchange,
} from "../types/chat-message";
import { computeAgentExchangeStatus, getLastUserExchange } from "./agent-conversation-utils";
import { MemoryUtils } from "./memory-utils";
import { AgentExecutionMode, type ChatModel } from "./chat-model";
import type { CheckpointStore } from "./checkpoint-store";
import { ConversationModel, formatHistory } from "./conversation-model";
import { type ToolsWebviewModel } from "./tools-webview-model";

import { isAgentConversation } from "./types";
import { type SoundModel } from "../../settings/models/sound-model";

export interface LastConversationModel {
  /** If the previous conversation was in chat mode, this is the ID */
  chat: string | undefined;
  /** If the previous conversation was in agent mode, this is the ID */
  agent: string | undefined;
}

/**
 * AgentConversationModel manages the lifecycle and state of agent-based conversations.
 *
 * An agent conversation is a special type of chat that:
 * - Can make direct changes to the codebase
 * - Maintains state about pending/dirty edits
 * - Has special handling for conversation switching
 * - Can only have one active instance at a time
 *
 * Key features:
 * - Tracks whether the current conversation is agentic via a Svelte store
 * - Handles switching between agent and regular chat modes
 * - Manages dirty state when code edits are pending
 * - Enforces single active agent conversation rule
 * - Handles conversation switching with pending edits
 *
 * The model integrates with:
 * - ChatModel for conversation management
 * - SpecialContextInputModel for context control
 * - ExtensionClient for VS Code integration
 */
export class AgentConversationModel {
  private _agenticExchangeStatus: Writable<AgentExchangeStatus> = writable(
    AgentExchangeStatus.notRunning,
  );
  private _isCurrConversationAgentic = writable(false);
  // Undefined signals the value has not been initialized yet
  private _hasEverUsedAgent: Writable<boolean | undefined> = writable(undefined);

  // Map to track which exchanges have had their memories classified
  private _classifiedMemoriesMap: Map<string, boolean> = new Map();

  // Flag to track if the current turn was interrupted
  private _wasInterrupted = writable(false);

  // Flag to track if the model is still being initialized to prevent spurious sound effects
  private _isInitializing = true;

  private _disposers: Array<() => void> = [];
  public readonly isAgenticTurnComplete: Readable<boolean> = derived(
    this._agenticExchangeStatus,
    ($status) => {
      return $status === AgentExchangeStatus.notRunning;
    },
  );

  /**
   * Creates a new AgentConversationModel instance.
   * Sets up event listeners for conversation changes and mode switches.
   *
   * @param _chatModel The ChatModel instance to integrate with
   * @param _toolsModel The ToolsWebviewModel instance
   * @param _checkpointStore The CheckpointStore instance
   * @param _soundModel The SoundModel instance for playing sound effects
   */
  constructor(
    private _chatModel: ChatModel,
    private _toolsModel: ToolsWebviewModel,
    private _checkpointStore: CheckpointStore,
    private _soundModel: SoundModel,
  ) {
    this._checkpointStore.registerAgentConversationModel(this);

    // Set up callback for when tools are skipped to mark the turn as interrupted
    this._toolsModel.addOnToolSkippedCallback(() => {
      this._wasInterrupted.set(true);
    });

    const hasAgentConversation = Object.values(this._chatModel.conversations).some(
      (conversation) => {
        return conversation.extraData?.isAgentConversation;
      },
    );
    const isCurrentConversationAgent =
      this._chatModel.currentConversationModel.extraData?.isAgentConversation === true;
    const hasUserUsedAgent = hasAgentConversation || isCurrentConversationAgent;

    if (hasUserUsedAgent) {
      this.setHasEverUsedAgent(true);
    }
    this.refreshHasEverUsedAgent();

    // Store disposer for conversation change subscription
    this._disposers.push(
      this._chatModel.currentConversationModel.subscribe(() => {
        const conversation = this._chatModel.currentConversationModel;
        const isAgentic = isAgentConversation(conversation);
        this._isCurrConversationAgentic.set(isAgentic);
      }),
    );

    // Store disposer for agentic turn completion subscription
    // This is used to determine when to an agentic turn is complete, and by extension,
    // when we can trigger things like recording memories
    this._disposers.push(
      this._chatModel.currentConversationModel.subscribe(
        throttle(
          () => {
            const status = computeAgentExchangeStatus(
              this._chatModel.currentConversationModel,
              this._chatModel.flags,
            );
            this._agenticExchangeStatus.set(status);
          },
          1000,
          { leading: true, trailing: true },
        ),
      ),
    );

    // Reset interruption flag when a new agent turn starts
    this._disposers.push(
      this._agenticExchangeStatus.subscribe((status) => {
        if (status === AgentExchangeStatus.running) {
          this._wasInterrupted.set(false);
        }
      }),
    );

    // Store disposer for chat mode changes subscription
    this._disposers.push(
      this._isCurrConversationAgentic.subscribe(($isAgentic) => {
        if ($isAgentic) {
          this._chatModel.extensionClient.setChatMode(ChatMode.agent);
          this._chatModel.specialContextInputModel.enableAgentMemories();
        } else {
          this._chatModel.extensionClient.setChatMode(ChatMode.chat);
          this._chatModel.specialContextInputModel.disableAgentMemories();
        }
        this._agenticExchangeStatus.set(AgentExchangeStatus.notRunning);
      }),
    );

    // Store disposer for agent execution mode changes subscription
    this._disposers.push(
      this._chatModel.agentExecutionMode.subscribe((_mode) => {
        // Only update chat mode if we're currently in an agentic conversation
        if (get(this._isCurrConversationAgentic)) {
          this._chatModel.extensionClient.setChatMode(ChatMode.agent);
        }
      }),
    );

    // Store disposer for before conversation change handler
    this._disposers.push(
      this._chatModel.currentConversationModel.onBeforeChangeConversation((current, next) => {
        // If the current conversation is not agentic, we don't need to do anything
        if (!isAgentConversation(current)) {
          return;
        }

        // If the next conversation is new (doesn't exist yet), we need to mark it as agentic
        // Also, if the next conversation is *explicitly* marked as non-agent conversation,
        // we should not mark it as agentic
        if (
          ConversationModel.isEmpty(next) &&
          next.extraData?.isAgentConversation !== false &&
          this._chatModel.conversations[next.id] === undefined
        ) {
          next = {
            ...next,
            extraData: { ...next.extraData, isAgentConversation: true },
          };
        }

        return next;
      }),
    );

    // Store disposer for conversation model subscription to detect first response token
    this._disposers.push(
      this._chatModel.currentConversationModel.subscribe((conversationModel) => {
        const lastExchange = conversationModel.lastExchange;
        const classifyOnFirstToken = this._chatModel.flags.memoryClassificationOnFirstToken;
        if (!lastExchange || !lastExchange.request_id || !classifyOnFirstToken) {
          return;
        }

        // Only process exchanges that:
        // 1. Have a status of "sent" (meaning they're receiving a response)
        // 2. Have at least some response text (indicating first token received)
        // 3. Haven't already been processed for memory classification
        if (
          lastExchange.status === ExchangeStatus.sent &&
          lastExchange.response_text &&
          lastExchange.response_text.length > 0 &&
          !this._classifiedMemoriesMap.has(lastExchange.request_id)
        ) {
          // Mark this exchange as processed
          this._classifiedMemoriesMap.set(lastExchange.request_id, true);

          // Now call the memory classification function
          void MemoryUtils.classifyAndDistillMemories(conversationModel, lastExchange);
        }

        // Clean up the map when an exchange completes or fails
        if (
          lastExchange.request_id &&
          (lastExchange.status === ExchangeStatus.success ||
            lastExchange.status === ExchangeStatus.failed ||
            lastExchange.status === ExchangeStatus.cancelled)
        ) {
          this._classifiedMemoriesMap.delete(lastExchange.request_id);
        }
      }),
    );

    this._disposers.push(
      this._chatModel.currentConversationModel.onSendExchange((exchange: ExchangeWithStatus) => {
        const classifyOnFirstToken = this._chatModel.flags.memoryClassificationOnFirstToken;
        if (classifyOnFirstToken) return;
        void MemoryUtils.classifyAndDistillMemories(
          this._chatModel.currentConversationModel,
          exchange,
        );
      }),
    );

    // Store disposer for flags model subscription to switch tools model to V2 when parallel tools is enabled
    this._disposers.push(
      this._chatModel.flags.subscribe((flags) => {
        if (flags.enableParallelTools) {
          this._toolsModel.switchToV2();
        }
      }),
    );

    // Store disposer for when a new conversation is created, we need to tell the extension
    // about it so it can correctly track all edits
    this._disposers.push(
      this._chatModel.currentConversationModel.onNewConversation(() => {
        const conversationId = this._chatModel.currentConversationModel.id;
        this._chatModel.extensionClient.setCurrentConversation(conversationId);

        // Create an original checkpoint if needed
        // The checkpoint store will handle all the necessary checks internally
        this._checkpointStore.maybeCreateOriginalCheckpoint();
      }),
    );
    this._chatModel.extensionClient.setCurrentConversation(
      this._chatModel.currentConversationModel.id,
    );

    // Store disposer for tool use state change handler
    this._disposers.push(
      this.isAgenticTurnComplete.subscribe(async ($isComplete) => {
        if (!$isComplete || !isAgentConversation(this._chatModel.currentConversationModel)) {
          return;
        }
        const currentConversation = this._chatModel.currentConversationModel;

        // Play sound effect when agent loop completes successfully (but not if interrupted)
        // Don't play sound during initialization to prevent spurious sounds on startup
        if (!get(this._wasInterrupted) && !this._isInitializing) {
          try {
            // Updates to the sound settings can occur from a different webview, so we need to
            // refresh sound settings from sidecar before playing so we don't have stale data if an update occurred
            await this._soundModel.refreshSettings();
            await this._soundModel.playAgentComplete();
          } catch (error) {
            console.warn("Failed to play agent complete sound:", error);
          }
        }

        // Reset the interruption flag for the next turn
        this._wasInterrupted.set(false);
        const memoryData = MemoryUtils.getLastUserExchangeMemory(currentConversation);
        if (!memoryData) {
          return;
        }

        const trace = FlushMemoriesData.create();
        trace.setFlag(FlushMemoriesDebugFlag.start);

        try {
          const lastExchangeRequestId = getLastUserExchange(currentConversation)?.request_id;
          if (lastExchangeRequestId) {
            trace.setRequestId(
              FlushMemoriesDebugFlag.lastUserExchangeRequestId,
              lastExchangeRequestId,
            );
          }
          const { memoriesRequestId, memory, isFlushed } = memoryData;
          trace.setRequestId(FlushMemoriesDebugFlag.memoriesRequestId, memoriesRequestId);
          // Early return if memory is empty or already flushed
          if (!memory || isFlushed) {
            trace.setFlag(FlushMemoriesDebugFlag.emptyMemory);
            return;
          }
          if (MemoryUtils.currentAgenticTurnHasRemember(currentConversation)) {
            trace.setFlag(FlushMemoriesDebugFlag.agenticTurnHasRememberToolCall);
            return;
          }

          this._chatModel.extensionClient.callTool(
            currentConversation.lastExchange?.request_id ?? "",
            "remember",
            "remember",
            {
              memory,
              caller: RememberToolCaller.classify_and_distill,
              isComplexNewMemory: false,
              memoriesRequestId,
            },
            currentConversation.chatHistory
              .filter((m): m is ExchangeWithStatus => isChatItemSuccessfulExchange(m))
              .map(formatHistory),
            currentConversation.id,
          );
          if (lastExchangeRequestId) {
            // Mark the memory as completed so it doesn't get double written on reload
            const successRemove = MemoryUtils.markUserExchangeMemoryComplete(
              currentConversation,
              lastExchangeRequestId,
            );
            trace.setFlag(FlushMemoriesDebugFlag.removeUserExchangeMemoryFailed, !successRemove);
          }
        } catch (e) {
          trace.setFlag(FlushMemoriesDebugFlag.exceptionThrown);
        } finally {
          trace.setFlag(FlushMemoriesDebugFlag.end);
          this._chatModel.extensionClient.reportAgentSessionEvent({
            eventName: AgentSessionEventName.flushMemories,
            conversationId: this._chatModel.currentConversationModel.id,
            eventData: {
              flushMemoriesData: trace,
            },
          });
        }
      }),
    );

    // When the agentic turn finishes, see if there have been any changes, and if so, create a checkpoint
    this._disposers.push(
      this.isAgenticTurnComplete.subscribe(async ($isComplete) => {
        if ($isComplete) {
          // Does the checkpoint representing the current conversation have any changes?
          const currCheckpoint = get(this._checkpointStore.currentCheckpoint);
          const currConversationId = get(this._chatModel.currentConversationModel).id;
          const hasChanges = await this._chatModel.extensionClient.hasChangesSince(
            currCheckpoint?.fromTimestamp ?? this.getBaselineTimestamp(),
          );

          // If conversation ID has changed in the meantime, don't create a checkpoint
          if (
            hasChanges &&
            currConversationId === get(this._chatModel.currentConversationModel).id
          ) {
            this._checkpointStore.createNewCheckpoint();
          }
        }
      }),
    );

    this._disposers.push(
      this.isAgenticTurnComplete.subscribe((isComplete) => {
        if (isComplete) {
          this._chatModel.currentConversationModel.checkAndGenerateAgentTitle();
        }
      }),
    );

    // When the tracked checkpoints changes change, mark the conversation as dirty
    this._disposers.push(
      this._checkpointStore.targetCheckpointSummary.subscribe(() => {
        // If there are any non-trivial changes
        if (get(this._checkpointStore.targetCheckpointHasChanges)) {
          this.markDirty();
        } else {
          this.markClean();
        }
      }),
    );

    // Mark initialization as complete after all subscriptions are set up
    // This prevents spurious sound effects from playing during startup
    this._isInitializing = false;
  }

  private _maybeTriggerWelcomeMessage = async (conversationId: string): Promise<boolean> => {
    const currentConversation = this._chatModel.currentConversationModel;

    if (currentConversation.id !== conversationId) {
      return false;
    }
    await this.refreshHasEverUsedAgent();
    if (
      !ConversationModel.isEmpty(currentConversation) ||
      ConversationModel.isNamed(currentConversation) ||
      currentConversation.extraData?.hasAgentOnboarded ||
      get(this._hasEverUsedAgent)
    ) {
      return true;
    }

    // Set the hasEverUsedAgent state to true when the user switches to agentic mode
    this.setHasEverUsedAgent(true);

    currentConversation.setName("Welcome to the Augment Agent");
    currentConversation.extraData = { isAgentConversation: true, hasAgentOnboarded: true };

    // TODO: Move the onboarding prompt to sidecar
    if (!isVSCodeHost()) {
      return true;
    }

    // Get the onboarding prompt from the extension client
    const onboardingPrompt = await this._chatModel.extensionClient.getAgentOnboardingPrompt();

    await currentConversation.sendExchange({
      chatItemType: ChatItemType.agentOnboarding,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      request_message: onboardingPrompt,
      status: ExchangeStatus.draft,
    });

    return true;
  };

  /**
   * Marks the current agent conversation as having pending/dirty edits.
   * This affects conversation switching behavior - users will be prompted
   * to accept changes before switching away.
   */
  public markDirty = () => {
    const conversationModel = this._chatModel.currentConversationModel;
    conversationModel.extraData = { ...conversationModel.extraData, hasDirtyEdits: true };
  };

  /**
   * Marks the current agent conversation as clean (no pending edits).
   * Called after edits are accepted or discarded.
   */
  public markClean = () => {
    const conversationModel = this._chatModel.currentConversationModel;
    conversationModel.extraData = { ...conversationModel.extraData, hasDirtyEdits: false };
  };

  /**
   * Updates the baseline timestamp for this conversation.
   * Checkpoints before this timestamp are considered "accepted" and not shown in the agent edit list.
   *
   * @param timestamp - The new baseline timestamp
   */
  public updateBaselineTimestamp = (timestamp: number): void => {
    // If the timestamp is > current timestamp, cap it to the current timestamp
    const cappedTimestamp = Math.min(timestamp, Date.now());
    const conversation = this._chatModel.currentConversationModel;
    if (isAgentConversation(conversation)) {
      conversation.extraData = { ...conversation.extraData, baselineTimestamp: cappedTimestamp };
    }
  };

  /**
   * Gets the baseline timestamp for the current conversation.
   * If the conversation is an agent conversation, returns the baseline timestamp from extraData.
   * Otherwise, returns 0.
   *
   * @returns The baseline timestamp
   */
  public getBaselineTimestamp = (): number => {
    const conversation = this._chatModel.currentConversationModel;
    return isAgentConversation(conversation) ? (conversation.extraData.baselineTimestamp ?? 0) : 0;
  };

  /**
   * Gets the conversation ID for the current conversation.
   *
   * @returns The conversation ID
   */
  public getConversationId = (): string => {
    return this._chatModel.currentConversationModel.id;
  };

  /**
   * A readable Svelte store that indicates whether the current conversation
   * is an agent conversation.
   */
  public get isCurrConversationAgentic(): Readable<boolean> {
    return this._isCurrConversationAgentic;
  }

  /**
   * A readable Svelte store that indicates the current status of the agent exchange.
   */
  public get agentExchangeStatus(): Readable<AgentExchangeStatus> {
    return this._agenticExchangeStatus;
  }

  /**
   * Interrupts the agent by canceling any active tool runs and unfinished exchanges.
   * This is used when the user wants to either interrupt the agent's current task
   * or switch to a different conversation.
   */
  public interruptAgent = async () => {
    // If it is not running, no-op
    if (get(this._agenticExchangeStatus) === AgentExchangeStatus.notRunning) {
      return;
    }

    // Mark that this turn was interrupted
    this._wasInterrupted.set(true);

    await this._toolsModel.interruptAllTools();
    await this._chatModel.currentConversationModel.cancelMessage();

    // Clear the classified memories map for the current conversation's last exchange
    const lastExchange = this._chatModel.currentConversationModel.lastExchange;
    if (lastExchange?.request_id) {
      this._classifiedMemoriesMap.delete(lastExchange.request_id);
    }
  };

  /**
   * Distills a memory from a specific message and saves it.
   * @param requestId The ID of the message to distill memory from
   */
  public async distillMemory(requestId: string): Promise<void> {
    const exchange = this._chatModel.currentConversationModel.chatHistory.find(
      (m): m is ExchangeWithStatus => {
        return isChatItemExchangeWithStatus(m) && m.request_id === requestId;
      },
    );
    if (!exchange?.request_message) {
      return;
    }

    // Send a silent exchange with the distill prompt
    const { responseText: rawResponse } =
      await this._chatModel.currentConversationModel.sendSilentExchange({
        /* eslint-disable @typescript-eslint/naming-convention */
        request_message: exchange.request_message,
        disableRetrieval: true,
        disableSelectedCodeDetails: true,
        memoriesInfo: {
          isDistill: true,
        },
        /* eslint-enable @typescript-eslint/naming-convention */
      });

    // Parse response and save memory if valid
    try {
      const json = JSON.parse(rawResponse);
      if (json.content) {
        await this._chatModel.extensionClient.callTool(
          requestId,
          "remember",
          "remember",
          {
            memory: json.content,
            caller: RememberToolCaller.classify_and_distill,
            isComplexNewMemory: false,
          },
          this._chatModel.currentConversationModel.chatHistory
            .filter(isChatItemSuccessfulExchange)
            .map(formatHistory),
          this._chatModel.currentConversationModel.id,
        );
      }
    } catch (e) {
      console.warn("Failed to parse JSON from distill memory response", e);
    }
  }

  /**
   * Private helper to reset the agent store to default values
   * for developer debugging purposes
   */
  _resetStateToDefaultValues = () => {
    this.setHasEverUsedAgent(false);

    // Reset remote agent usage state (now managed by RemoteAgentsModel)
    this._chatModel.extensionClient.setHasEverUsedRemoteAgent(false);

    // Reset agent execution mode to manual
    this._chatModel.agentExecutionMode.set(AgentExecutionMode.manual);
  };

  /**
   * Resets the agent onboarding state, allowing the welcome message to be shown again.
   *
   * This will reset the hasEverUsedAgent state so that the welcome message is shown again
   * next time the user switches to agent mode.
   *
   * If the current conversation is an agent conversation, then the welcome message
   * is shown again.
   *
   * @returns Promise that resolves when the onboarding has been reset
   */
  public async resetOnboarding(): Promise<void> {
    this._resetStateToDefaultValues();

    const currentConversation = this._chatModel.currentConversationModel;
    if (!isAgentConversation(currentConversation)) {
      return;
    }

    // Clear the onboarding flag
    currentConversation.extraData = {
      ...currentConversation.extraData,
      hasAgentOnboarded: false,
    };

    // Trigger welcome message
    await this._maybeTriggerWelcomeMessage(currentConversation.id);
  }

  public setHasEverUsedAgent = (hasUsed: boolean) => {
    this._chatModel.extensionClient.setHasEverUsedAgent(hasUsed);
    this._hasEverUsedAgent.set(hasUsed);
  };

  public get hasEverUsedAgent(): Readable<boolean | undefined> {
    return this._hasEverUsedAgent;
  }

  /**
   * Checks if the user has ever used the agent and updates the state.
   * This is useful when initializing the UI to show appropriate indicators.
   */
  public refreshHasEverUsedAgent = async () => {
    if (get(this.hasEverUsedAgent) !== undefined) {
      return;
    }
    const hasEverUsed = await this._chatModel.extensionClient.checkHasEverUsedAgent();
    if (get(this.hasEverUsedAgent) === undefined) {
      this._hasEverUsedAgent.set(hasEverUsed);
    }
  };

  /**
   * Cleans up all subscriptions and handlers
   */
  public dispose(): void {
    this._disposers.forEach((dispose) => dispose());
    this._disposers = [];

    // Clear the classified memories map
    this._classifiedMemoriesMap.clear();
  }
}
