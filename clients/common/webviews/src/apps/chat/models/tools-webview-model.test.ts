import { describe, it, expect, vi, beforeEach } from "vitest";
import { clipResultIfLarge } from "../utils/tool-utils";
import { ToolsWebviewModel } from "./tools-webview-model";
import { range } from "lodash";
import { ToolUsePhase } from "../types/tool-use-state";
import { ExchangeStatus } from "../types/chat-message";
import { ChatRequestNodeType } from "@augment-internal/sidecar-libs/src/chat/chat-types";
import { ToolResponseContentNodeType } from "@augment-internal/sidecar-libs/src/tools/tool-types";
import { ConversationModel } from "./conversation-model";

describe("clipResultIfLarge", () => {
  it("should return original response if under max size", () => {
    const response = {
      text: "small result",
      isError: false,
    };
    const result = clipResultIfLarge(response, 100);
    expect(result).toEqual(response);
  });

  it("should clip single-line result", () => {
    const longText = "x".repeat(500) + "y".repeat(500);
    const response = {
      text: longText,
      isError: false,
    };
    const result = clipResultIfLarge(response, 100);

    expect(result.isError).toBe(false);
    const expectedMsg = "x".repeat(50) + "<...>" + "y".repeat(50);
    expect(result.text).toEqual(expectedMsg);
  });

  it("should clip multi-line result", () => {
    const longText = range(100)
      .map((i) => `line${i + 1}\n`)
      .join("");
    const response = {
      text: longText,
      isError: false,
    };
    const result = clipResultIfLarge(response, 80);

    expect(result.isError).toBe(false);
    const expectedMsg =
      "line1\nline2\nline3\n... additional lines truncated ...\nline99\nline100\n";
    expect(result.text).toEqual(expectedMsg);
  });

  it("clip text with CRLF line endings", () => {
    const longText = range(100)
      .map((i) => `line${i + 1}\r\n`)
      .join("");
    const response = {
      text: longText,
      isError: false,
    };
    const result = clipResultIfLarge(response, 64);

    expect(result.isError).toBe(false);
    const expectedMsg = "line1\r\nline2\r\n... additional lines truncated ...\r\nline100\r\n";
    expect(result.text).toEqual(expectedMsg);
  });

  it("preserve tool error status", () => {
    const resultT = clipResultIfLarge({ text: "x".repeat(500), isError: true }, 100);
    expect(resultT.isError).toEqual(true);
    const resultF = clipResultIfLarge({ text: "x".repeat(500), isError: false }, 100);
    expect(resultF.isError).toEqual(false);
  });

  it("should handle empty text", () => {
    const response = {
      text: "",
      isError: false,
    };
    const result = clipResultIfLarge(response, 50);
    expect(result).toEqual(response);
  });

  it("should properly clip content nodes with text content", () => {
    const longText = "x".repeat(500) + "y".repeat(500);
    const response = {
      text: "Original text",
      isError: false,
      contentNodes: [
        {
          type: ToolResponseContentNodeType.ContentText,
          /* eslint-disable @typescript-eslint/naming-convention */
          text_content: longText,
          /* eslint-enable @typescript-eslint/naming-convention */
        },
      ],
    };

    const result = clipResultIfLarge(response, 100);

    expect(result.isError).toBe(false);
    expect(result.contentNodes).toBeDefined();
    expect(result.contentNodes?.length).toBe(1);

    const contentNode = result.contentNodes?.[0];
    expect(contentNode?.type).toBe(ToolResponseContentNodeType.ContentText);

    // This test will fail with the current bug because truncTextContent is initialized as empty string
    // and never gets the value from content.text_content
    const expectedContent = "x".repeat(50) + "<...>" + "y".repeat(50);
    expect(contentNode?.text_content).toEqual(expectedContent);
  });
});

describe("ToolsWebviewModel", () => {
  let toolsWebviewModel: ToolsWebviewModel;
  let mockConversationModel: any;
  let mockExtensionClient: any;
  let mockChatModel: any;

  beforeEach(() => {
    // Mock conversation model
    mockConversationModel = {
      updateToolUseState: vi.fn(),
      sendExchange: vi.fn(),
      id: "conversation-123",
      subscribe: vi.fn().mockImplementation((callback) => {
        // Immediately call the callback with the mock model
        callback(mockConversationModel);
        // Return a function that can be called to unsubscribe
        return () => {};
      }),
      onNewConversation: vi.fn().mockReturnValue(() => {}),
      onHistoryDelete: vi.fn().mockReturnValue(() => {}),
      chatHistory: [],
      lastExchange: undefined,
      getToolUseState: vi.fn().mockReturnValue({ phase: ToolUsePhase.new }),
      getLastToolUseState: vi.fn(),
    };

    // Mock extension client
    mockExtensionClient = {
      callTool: vi.fn(),
      closeAllToolProcesses: vi.fn(),
      getToolIdentifier: vi
        .fn()
        .mockResolvedValue({ found: true, toolIdentifier: { hostName: "localToolHost" } }),
      checkSafe: vi.fn().mockResolvedValue(true),
      reportError: vi.fn(),
      cancelToolRun: vi.fn().mockResolvedValue(undefined),
    };

    // Mock chat model
    mockChatModel = {
      flags: {
        enableDebugFeatures: false,
      },
    };

    // Mock sound model
    const mockSoundModel = {
      refreshSettings: vi.fn().mockResolvedValue(undefined),
      playAgentComplete: vi.fn().mockResolvedValue(undefined),
    };

    // Create instance with mocked dependencies
    toolsWebviewModel = new ToolsWebviewModel(
      mockConversationModel,
      mockExtensionClient,
      mockChatModel,
      mockSoundModel as any,
      undefined, // remoteAgentsModel not needed for these tests
    );
  });

  describe("callTool", () => {
    it("should handle successful tool call with text response", async () => {
      // Setup
      const requestId = "request-123";
      const toolUseId = "tool-use-123";
      const toolName = "test-tool";
      const toolInput = { param: "value" };
      const chatHistory: any[] = [];
      const conversationId = "conversation-123";

      // Mock getToolUseState to return running phase for this specific test
      mockConversationModel.getToolUseState.mockReturnValue({ phase: ToolUsePhase.running });

      // Mock the extension client response
      mockExtensionClient.callTool.mockResolvedValue({
        text: "Tool result",
        isError: false,
      });

      // Mock getLastToolUseState to return a matching tool use state
      mockConversationModel.getLastToolUseState.mockReturnValue({
        requestId,
        toolUseId,
        phase: ToolUsePhase.completed,
        result: {
          isError: false,
          text: "Tool result",
        },
      });

      // Mock getToolUseState to return the state that will be set by updateToolUseState
      // This is needed for _generateToolResultNodesFromState to work
      mockConversationModel.getToolUseState.mockReturnValue({
        requestId,
        toolUseId,
        phase: ToolUsePhase.completed,
        result: {
          isError: false,
          text: "Tool result",
        },
      });

      // Call the method
      await toolsWebviewModel.callTool(
        requestId,
        toolUseId,
        toolName,
        toolInput,
        chatHistory,
        conversationId,
      );

      // Verify tool state was updated correctly
      expect(mockConversationModel.updateToolUseState).toHaveBeenCalledWith({
        requestId,
        toolUseId,
        phase: ToolUsePhase.running,
      });

      expect(mockConversationModel.updateToolUseState).toHaveBeenCalledWith({
        requestId,
        toolUseId,
        phase: ToolUsePhase.completed,
        result: expect.objectContaining({
          text: "Tool result",
          isError: false,
        }),
      });

      // Verify exchange was sent
      expect(mockConversationModel.sendExchange).toHaveBeenCalledWith({
        /* eslint-disable @typescript-eslint/naming-convention */
        request_message: "",
        status: ExchangeStatus.draft,
        structured_request_nodes: [
          {
            id: 1,
            type: ChatRequestNodeType.TOOL_RESULT,
            tool_result_node: {
              content: "Tool result",
              is_error: false,
              tool_use_id: toolUseId,
              request_id: undefined,
            },
          },
        ],
        /* eslint-enable @typescript-eslint/naming-convention */
      });
    });

    it("should handle tool call with content nodes and properly clip them", async () => {
      // Setup
      const requestId = "request-123";
      const toolUseId = "tool-use-123";
      const toolName = "test-tool";
      const toolInput = { param: "value" };
      const chatHistory: any[] = [];
      const conversationId = "conversation-123";

      // Mock getToolUseState to return running phase for this specific test
      mockConversationModel.getToolUseState.mockReturnValue({ phase: ToolUsePhase.running });

      const halfBudget = Math.floor(100 / 2);
      const longText = "x".repeat(500) + "y".repeat(500);

      // Mock the extension client response with content nodes
      // Also mock the truncateMiddle function by implementing it directly in the mock
      mockExtensionClient.callTool.mockImplementation(() => {
        // Create a response with long text content
        const response = {
          text: "Original text",
          isError: false,
          contentNodes: [
            {
              type: ToolResponseContentNodeType.ContentText,
              /* eslint-disable @typescript-eslint/naming-convention */
              text_content: longText,
              /* eslint-enable @typescript-eslint/naming-convention */
            },
          ],
        };

        // Apply truncation similar to the real clipResultIfLarge function
        // This simulates what happens in the real implementation
        const truncatedText = longText.slice(0, halfBudget) + "<...>" + longText.slice(-halfBudget);

        // Return the response with properly truncated content
        return Promise.resolve({
          ...response,
          contentNodes: [
            {
              type: ToolResponseContentNodeType.ContentText,
              /* eslint-disable @typescript-eslint/naming-convention */
              text_content: truncatedText,
              /* eslint-enable @typescript-eslint/naming-convention */
            },
          ],
        });
      });

      // Define the expected content nodes after truncation
      const mockContentNodes = [
        {
          type: ToolResponseContentNodeType.ContentText,
          /* eslint-disable @typescript-eslint/naming-convention */
          text_content: longText.slice(0, halfBudget) + "<...>" + longText.slice(-halfBudget),
          /* eslint-enable @typescript-eslint/naming-convention */
        },
      ];

      // Mock getLastToolUseState to return a matching tool use state
      mockConversationModel.getLastToolUseState.mockReturnValue({
        requestId,
        toolUseId,
        phase: ToolUsePhase.completed,
        result: {
          isError: false,
          contentNodes: mockContentNodes,
        },
      });

      // Mock getToolUseState to return the state with content nodes
      // This is needed for _generateToolResultNodesFromState to work
      mockConversationModel.getToolUseState.mockReturnValue({
        requestId,
        toolUseId,
        phase: ToolUsePhase.completed,
        result: {
          isError: false,
          contentNodes: [
            {
              type: ToolResponseContentNodeType.ContentText,
              /* eslint-disable @typescript-eslint/naming-convention */
              text_content: "x".repeat(50) + "<...>" + "y".repeat(50), // Truncated content
              /* eslint-enable @typescript-eslint/naming-convention */
            },
          ],
        },
      });

      // Call the method
      await toolsWebviewModel.callTool(
        requestId,
        toolUseId,
        toolName,
        toolInput,
        chatHistory,
        conversationId,
      );

      // Get the actual calls to updateToolUseState
      const calls = mockConversationModel.updateToolUseState.mock.calls;

      // Find the call that updates the phase to completed
      const completedCall = calls.find((call: any[]) => call[0].phase === ToolUsePhase.completed);

      // Verify the call exists
      expect(completedCall).toBeDefined();

      // Verify the content was properly truncated
      const contentNode = completedCall[0].result.contentNodes[0];
      expect(contentNode.type).toBe(ToolResponseContentNodeType.ContentText);
      expect(contentNode.text_content).toContain("<...>");

      // Verify exchange was sent with content nodes
      expect(mockConversationModel.sendExchange).toHaveBeenCalledWith(
        expect.objectContaining({
          /* eslint-disable @typescript-eslint/naming-convention */
          structured_request_nodes: [
            expect.objectContaining({
              type: ChatRequestNodeType.TOOL_RESULT,
              tool_result_node: expect.objectContaining({
                content: "", // Empty because we're using content_nodes
                content_nodes: expect.any(Array),
              }),
            }),
          ],
          /* eslint-enable @typescript-eslint/naming-convention */
        }),
      );
    });

    it("should handle tool call with empty response", async () => {
      // Setup
      const requestId = "request-123";
      const toolUseId = "tool-use-123";
      const toolName = "test-tool";
      const toolInput = { param: "value" };
      const chatHistory: any[] = [];
      const conversationId = "conversation-123";

      // Mock getToolUseState to return running phase for this specific test
      mockConversationModel.getToolUseState.mockReturnValue({ phase: ToolUsePhase.running });

      // Mock the extension client response with content nodes
      // Also mock the truncateMiddle function by implementing it directly in the mock
      mockExtensionClient.callTool.mockImplementation(() => {
        // Response with empty text
        return Promise.resolve({
          text: "",
          isError: false,
        });
      });

      // Mock getLastToolUseState to return a matching tool use state
      mockConversationModel.getLastToolUseState.mockReturnValue({
        requestId,
        toolUseId,
        phase: ToolUsePhase.completed,
        result: {
          isError: false,
          text: "",
        },
      });

      // Mock getToolUseState to return the state with empty text
      // This is needed for _generateToolResultNodesFromState to work
      mockConversationModel.getToolUseState.mockReturnValue({
        requestId,
        toolUseId,
        phase: ToolUsePhase.completed,
        result: {
          isError: false,
          text: "",
        },
      });

      // Call the method
      await toolsWebviewModel.callTool(
        requestId,
        toolUseId,
        toolName,
        toolInput,
        chatHistory,
        conversationId,
      );

      // Get the actual calls to updateToolUseState
      const calls = mockConversationModel.updateToolUseState.mock.calls;

      // Find the call that updates the phase to completed
      const completedCall = calls.find((call: any[]) => call[0].phase === ToolUsePhase.completed);

      // Verify the call exists
      expect(completedCall).toBeDefined();

      // Verify the content was properly truncated
      const text = completedCall[0].result.text;
      expect(text).equal("");

      // Verify exchange was sent with content nodes
      expect(mockConversationModel.sendExchange).toHaveBeenCalledWith(
        expect.objectContaining({
          /* eslint-disable @typescript-eslint/naming-convention */
          structured_request_nodes: [
            expect.objectContaining({
              type: ChatRequestNodeType.TOOL_RESULT,
              tool_result_node: expect.objectContaining({
                content: "", // Empty because text is empty
              }),
            }),
          ],
          /* eslint-enable @typescript-eslint/naming-convention */
        }),
      );
    });

    it("should handle tool call errors", async () => {
      // Setup
      const requestId = "request-123";
      const toolUseId = "tool-use-123";
      const toolName = "test-tool";
      const toolInput = { param: "value" };
      const chatHistory: any[] = [];
      const conversationId = "conversation-123";

      // Mock getToolUseState to return running phase for this specific test
      mockConversationModel.getToolUseState.mockReturnValue({ phase: ToolUsePhase.running });

      // Mock the extension client to throw an error
      const errorMessage = "Tool execution failed";
      mockExtensionClient.callTool.mockRejectedValue(new Error(errorMessage));

      // Mock getLastToolUseState to return a matching tool use state
      mockConversationModel.getLastToolUseState.mockReturnValue({
        requestId,
        toolUseId,
        phase: ToolUsePhase.error,
        result: {
          isError: true,
          text: errorMessage,
        },
      });

      // Mock getToolUseState to return the error state
      // This is needed for _generateToolResultNodesFromState to work
      mockConversationModel.getToolUseState.mockReturnValue({
        requestId,
        toolUseId,
        phase: ToolUsePhase.error,
        result: {
          isError: true,
          text: errorMessage,
        },
      });

      // Call the method
      await toolsWebviewModel.callTool(
        requestId,
        toolUseId,
        toolName,
        toolInput,
        chatHistory,
        conversationId,
      );

      // Verify tool state was updated with error
      expect(mockConversationModel.updateToolUseState).toHaveBeenCalledWith({
        requestId,
        toolUseId,
        phase: ToolUsePhase.error,
        result: {
          isError: true,
          text: errorMessage,
        },
      });

      // Verify error exchange was sent
      expect(mockConversationModel.sendExchange).toHaveBeenCalledWith({
        /* eslint-disable @typescript-eslint/naming-convention */
        request_message: "",
        status: ExchangeStatus.draft,
        structured_request_nodes: [
          {
            id: 1,
            type: ChatRequestNodeType.TOOL_RESULT,
            tool_result_node: {
              content: errorMessage,
              is_error: true,
              tool_use_id: toolUseId,
            },
          },
        ],
        /* eslint-enable @typescript-eslint/naming-convention */
      });
    });

    it.each([
      {
        scenario: "non-matching tool use state",
        lastToolUseState: {
          requestId: "different-request-id",
          toolUseId: "different-tool-use-id",
          phase: ToolUsePhase.running,
        },
      },
      {
        scenario: "unknown tool use state",
        lastToolUseState: {
          phase: ToolUsePhase.unknown,
        },
      },
    ])("should not send exchange when $scenario", async ({ lastToolUseState }) => {
      // Setup
      const requestId = "request-123";
      const toolUseId = "tool-use-123";
      const toolName = "test-tool";
      const toolInput = { param: "value" };
      const chatHistory: any[] = [];
      const conversationId = "conversation-123";

      // Mock getToolUseState to return running phase for this specific test
      mockConversationModel.getToolUseState.mockReturnValue({ phase: ToolUsePhase.running });

      // Mock the extension client response
      mockExtensionClient.callTool.mockResolvedValue({
        text: "Tool result",
        isError: false,
      });

      // Mock getLastToolUseState to return the test case's tool use state
      mockConversationModel.getLastToolUseState.mockReturnValue(lastToolUseState);

      // Call the method
      await toolsWebviewModel.callTool(
        requestId,
        toolUseId,
        toolName,
        toolInput,
        chatHistory,
        conversationId,
      );

      // Verify tool state was updated correctly
      expect(mockConversationModel.updateToolUseState).toHaveBeenCalledWith({
        requestId,
        toolUseId,
        phase: ToolUsePhase.running,
      });

      expect(mockConversationModel.updateToolUseState).toHaveBeenCalledWith({
        requestId,
        toolUseId,
        phase: ToolUsePhase.completed,
        result: expect.objectContaining({
          text: "Tool result",
          isError: false,
        }),
      });

      // Verify exchange was NOT sent
      expect(mockConversationModel.sendExchange).not.toHaveBeenCalled();
      expect(mockExtensionClient.reportError).toHaveBeenCalledWith(
        expect.objectContaining({
          originalRequestId: requestId,
          diagnostics: expect.arrayContaining([
            expect.objectContaining({
              key: "tool_use_id",
              value: toolUseId,
            }),
          ]),
        }),
      );
    });

    it("should not send exchange when cancelled tool result comes back", async () => {
      // Test the race condition: Tool A starts, gets cancelled, Tool B starts,
      // then Tool A result comes back - should not send exchange
      const requestIdA = "request-A";
      const toolUseIdA = "tool-use-A";
      const requestIdB = "request-B";
      const toolUseIdB = "tool-use-B";

      let resolveToolA: (value: any) => void;

      // Mock Tool A to be slow (we control when it resolves)
      const toolAPromise = new Promise((resolve) => {
        resolveToolA = resolve;
      });

      // Mock Tool B to complete immediately
      mockExtensionClient.callTool
        .mockReturnValueOnce(toolAPromise) // First call (Tool A) - slow
        .mockResolvedValueOnce({ text: "Tool B result", isError: false }); // Second call (Tool B) - fast

      // Mock getToolUseState to return correct phases
      mockConversationModel.getToolUseState.mockImplementation((reqId: string, toolId: string) => {
        if (reqId === requestIdA && toolId === toolUseIdA) {
          return { phase: ToolUsePhase.cancelled }; // Tool A is cancelled
        }
        return { phase: ToolUsePhase.running }; // Tool B is running
      });

      // Mock getLastToolUseState to return Tool B as current
      mockConversationModel.getLastToolUseState.mockReturnValue({
        requestId: requestIdB,
        toolUseId: toolUseIdB,
        phase: ToolUsePhase.completed,
        result: {
          isError: false,
          text: "Tool B result",
        },
      });

      // 1. Start Tool A (doesn't await - starts in background)
      const toolACall = toolsWebviewModel.callTool(
        requestIdA,
        toolUseIdA,
        "tool-A",
        {},
        [],
        "conv-123",
      );

      // 2. Cancel Tool A while it's running
      await toolsWebviewModel.cancelToolRun(requestIdA, toolUseIdA);

      // 3. Start Tool B (this would've switched _activeToolCancelled to true)
      await toolsWebviewModel.callTool(requestIdB, toolUseIdB, "tool-B", {}, [], "conv-123");

      // 4. Now Tool A "completes" after being cancelled and after Tool B started
      resolveToolA!({ text: "Tool A result (should be ignored)", isError: false });
      await toolACall;

      // Verify no exchange was sent for the cancelled Tool A (Tool B exchange is fine)
      expect(mockConversationModel.sendExchange).not.toHaveBeenCalledWith(
        expect.objectContaining({
          /* eslint-disable @typescript-eslint/naming-convention */
          structured_request_nodes: expect.arrayContaining([
            expect.objectContaining({
              tool_result_node: expect.objectContaining({
                tool_use_id: toolUseIdA, // Tool A should not send exchange
              }),
            }),
          ]),
          /* eslint-enable @typescript-eslint/naming-convention */
        }),
      );
    });

    describe("conversation switching during tool execution", () => {
      let realConversationModel: any;
      let realToolsWebviewModel: any;
      const conversationId1 = "conversation-123";
      const conversationId2 = "conversation-456";
      const requestId = "request-123";
      const toolUseId = "tool-use-123";
      const toolName = "test-tool";
      const toolInput = { param: "value" };
      const chatHistory: any[] = [];

      const createConversation = (id: string) => ({
        id,
        chatHistory: [],
        toolUseStates: {},
        feedbackStates: {},
        createdAtIso: new Date().toISOString(),
        lastInteractedAtIso: new Date().toISOString(),
        requestIds: [],
        name: undefined,
        draftExchange: undefined,
        draftActiveContextIds: undefined,
        selectedModelId: undefined,
        isPinned: false,
        lastUrl: undefined,
        isShareable: false,
        extraData: {},
        personaType: 0, // PersonaType.DEFAULT
      });

      beforeEach(async () => {
        // Create mock dependencies for ConversationModel
        const mockChatFlagsModel = {
          subscribe: vi.fn(),
        };
        const mockSpecialContextInputModel = {
          subscribe: vi.fn(),
          recentItems: [],
          markItemsActive: vi.fn(),
          markItemsInactive: vi.fn(),
        };
        const mockSaveConversation = vi.fn();

        // Create mock rules model
        const mockRulesModel = {
          getCachedRules: vi.fn().mockReturnValue({
            subscribe: vi.fn(),
          }),
          requestRules: vi.fn(),
          handleMessageFromExtension: vi.fn(),
        };

        // Create a real conversation model with actual tool use states
        realConversationModel = new ConversationModel(
          mockExtensionClient,
          mockChatFlagsModel as any,
          mockSpecialContextInputModel as any,
          mockSaveConversation,
          mockRulesModel as any,
        );

        // Set the initial conversation
        realConversationModel.setConversation(createConversation(conversationId1));

        // Mock sound model for real tests
        const mockSoundModel = {
          refreshSettings: vi.fn().mockResolvedValue(undefined),
          playAgentComplete: vi.fn().mockResolvedValue(undefined),
        };

        // Create a new ToolsWebviewModel with the real conversation model
        realToolsWebviewModel = new ToolsWebviewModel(
          realConversationModel,
          mockExtensionClient,
          mockChatModel,
          mockSoundModel as any,
        );
      });

      it("should drop tool result when conversation switches while tool is running", async () => {
        let resolveToolCall: (value: any) => void;

        // Mock the tool call so that we control when it resolves
        const toolCallPromise = new Promise((resolve) => {
          resolveToolCall = resolve;
        });
        mockExtensionClient.callTool.mockReturnValue(toolCallPromise);
        vi.spyOn(realConversationModel, "sendExchange");

        // 1. Start the tool (doesn't await - starts in background)
        const toolCall = realToolsWebviewModel.callTool(
          requestId,
          toolUseId,
          toolName,
          toolInput,
          chatHistory,
          conversationId1,
        );

        // Verify the tool was marked as running in the first conversation
        expect(realConversationModel.getToolUseState(requestId, toolUseId).phase).toBe(
          ToolUsePhase.running,
        );

        // 2. Actually switch to a new conversation (this resets tool use states)
        realConversationModel.setConversation(createConversation(conversationId2));

        // Verify that the tool state is now "new" in the new conversation
        // (because the tool doesn't exist in the new conversation's toolUseStates)
        expect(realConversationModel.getToolUseState(requestId, toolUseId).phase).toBe(
          ToolUsePhase.new,
        );

        // 3. Now the tool "completes" after the conversation has switched
        resolveToolCall!({ text: "Tool result (should be dropped)", isError: false });
        await toolCall;

        // Verify that the tool state is still "new" (result was dropped, no state update)
        expect(realConversationModel.getToolUseState(requestId, toolUseId).phase).toBe(
          ToolUsePhase.new,
        );

        // Verify that no exchange was sent to the real conversation model
        expect(realConversationModel.sendExchange).not.toHaveBeenCalled();

        // Verify that no error was reported (this is expected behavior, not an error)
        expect(mockExtensionClient.reportError).not.toHaveBeenCalled();
      });

      it("should drop tool error result when conversation switches while tool is running", async () => {
        // Mock the tool call to throw an error
        const errorMessage = "Tool execution failed";
        mockExtensionClient.callTool.mockRejectedValue(new Error(errorMessage));
        vi.spyOn(realConversationModel, "sendExchange");

        // 1. Start the tool call (this will fail with an error)
        const toolCallPromise = realToolsWebviewModel.callTool(
          requestId,
          toolUseId,
          toolName,
          toolInput,
          chatHistory,
          conversationId1,
        );

        // Verify the tool was marked as running in the first conversation
        expect(realConversationModel.getToolUseState(requestId, toolUseId).phase).toBe(
          ToolUsePhase.running,
        );

        // 2. Actually switch to a new conversation before the error is handled
        realConversationModel.setConversation(createConversation(conversationId2));

        // Verify that the tool state is now "new" in the new conversation
        expect(realConversationModel.getToolUseState(requestId, toolUseId).phase).toBe(
          ToolUsePhase.new,
        );

        // 3. Wait for the tool call to complete (with error)
        await toolCallPromise;

        // Verify that the tool state is still "new" (error result was dropped, no state update)
        expect(realConversationModel.getToolUseState(requestId, toolUseId).phase).toBe(
          ToolUsePhase.new,
        );

        // Verify that no exchange was sent to the real conversation model
        expect(realConversationModel.sendExchange).not.toHaveBeenCalled();

        // Verify that no error was reported (this is expected behavior, not an error)
        expect(mockExtensionClient.reportError).not.toHaveBeenCalled();
      });
    });
  });

  describe("cancelToolRun", () => {
    it("should call interruptAgent when remoteAgentsModel is provided", async () => {
      // Create a mock remote agents model
      const mockRemoteAgentsModel = {
        interruptAgent: vi.fn().mockResolvedValue(undefined),
        isActive: true,
      };

      // Mock sound model for remote agent test
      const mockSoundModel = {
        refreshSettings: vi.fn().mockResolvedValue(undefined),
        playAgentComplete: vi.fn().mockResolvedValue(undefined),
      };

      // Create a new ToolsWebviewModel instance with the mock remote agents model
      const toolsWebviewModelWithRemoteAgent = new ToolsWebviewModel(
        mockConversationModel,
        mockExtensionClient,
        mockChatModel,
        mockSoundModel as any,
        mockRemoteAgentsModel as any,
      );

      // Set up an active tool
      const requestId = "request-123";
      const toolUseId = "tool-use-123";

      // The extension client cancelToolRun method is already mocked in beforeEach

      // Simulate an active tool by setting the internal state on the V1 instance
      (toolsWebviewModelWithRemoteAgent as any)._toolsWebviewModelV1._activeTool = {
        id: toolUseId,
        requestId,
        phase: ToolUsePhase.running,
      };

      // Call cancelToolRun
      await toolsWebviewModelWithRemoteAgent.cancelToolRun(requestId, toolUseId);

      // Verify that interruptAgent was called
      expect(mockRemoteAgentsModel.interruptAgent).toHaveBeenCalledOnce();

      // Verify that the extension client cancelToolRun was also called
      expect(mockExtensionClient.cancelToolRun).toHaveBeenCalledWith(requestId, toolUseId);

      // Verify that the tool state was updated
      expect(mockConversationModel.updateToolUseState).toHaveBeenCalledWith({
        requestId,
        toolUseId,
        phase: ToolUsePhase.cancelling,
      });
    });

    it("should not call interruptAgent when remoteAgentsModel is not provided", async () => {
      // Use the original toolsWebviewModel (without remoteAgentsModel)
      const requestId = "request-123";
      const toolUseId = "tool-use-123";

      // Set up an active tool on the V1 instance
      (toolsWebviewModel as any)._toolsWebviewModelV1._activeTool = {
        id: toolUseId,
        requestId,
        phase: ToolUsePhase.running,
      };

      // The extension client cancelToolRun method is already mocked in beforeEach

      // Call cancelToolRun
      await toolsWebviewModel.cancelToolRun(requestId, toolUseId);

      // Verify that the extension client cancelToolRun was called
      expect(mockExtensionClient.cancelToolRun).toHaveBeenCalledWith(requestId, toolUseId);

      // Verify that the tool state was updated
      expect(mockConversationModel.updateToolUseState).toHaveBeenCalledWith({
        requestId,
        toolUseId,
        phase: ToolUsePhase.cancelling,
      });
    });

    it("should not cancel when no active tool matches", async () => {
      const requestId = "request-123";
      const toolUseId = "tool-use-123";

      // Set up a different active tool
      (toolsWebviewModel as any)._activeTool = {
        id: "different-tool-id",
        requestId: "different-request-id",
        phase: ToolUsePhase.running,
      };

      // Mock the extension client cancelToolRun method
      mockExtensionClient.cancelToolRun = vi.fn().mockResolvedValue(undefined);

      // Call cancelToolRun
      await toolsWebviewModel.cancelToolRun(requestId, toolUseId);

      // Verify that the extension client cancelToolRun was NOT called
      expect(mockExtensionClient.cancelToolRun).not.toHaveBeenCalled();

      // Verify that the tool state was NOT updated
      expect(mockConversationModel.updateToolUseState).not.toHaveBeenCalled();
    });
  });

  describe("sendExchange behavior after refactoring", () => {
    describe("skipToolRun", () => {
      it("should send exchange with skip message from tool state", async () => {
        // Setup
        const requestId = "request-123";
        const toolUseId = "tool-use-123";

        // Mock getLastToolUseState to return matching state (so exchange gets sent)
        mockConversationModel.getLastToolUseState.mockReturnValue({
          requestId,
          toolUseId,
          phase: ToolUsePhase.cancelled,
          result: {
            isError: true,
            text: "Tool did not run. User clicked `Skip` to cancel.",
          },
        });

        // Mock getToolUseState to return the state that skipToolRun will create
        mockConversationModel.getToolUseState.mockReturnValue({
          requestId,
          toolUseId,
          phase: ToolUsePhase.cancelled,
          result: {
            isError: true,
            text: "Tool did not run. User clicked `Skip` to cancel.",
          },
        });

        // Call skipToolRun
        await toolsWebviewModel.skipToolRun(requestId, toolUseId);

        // Verify sendExchange was called with the skip message from tool state
        expect(mockConversationModel.sendExchange).toHaveBeenCalledWith({
          /* eslint-disable @typescript-eslint/naming-convention */
          request_message: "",
          status: ExchangeStatus.draft,
          structured_request_nodes: [
            {
              id: 1,
              type: ChatRequestNodeType.TOOL_RESULT,
              tool_result_node: {
                content: "Tool did not run. User clicked `Skip` to cancel.",
                is_error: true,
                tool_use_id: toolUseId,
              },
            },
          ],
          model_id: undefined,
          /* eslint-enable @typescript-eslint/naming-convention */
        });
      });

      it("should not send exchange when another tool is active", async () => {
        // Setup
        const requestId = "request-123";
        const toolUseId = "tool-use-123";

        // Simulate another tool being active
        mockExtensionClient.callTool.mockImplementation(() => new Promise(() => {}));
        toolsWebviewModel.callTool(
          "other-request",
          "other-tool",
          "other-tool-name",
          {},
          [],
          "conv-123",
        );

        // Try to skip - should not work
        await toolsWebviewModel.skipToolRun(requestId, toolUseId);

        // Verify no exchange was sent
        expect(mockConversationModel.sendExchange).not.toHaveBeenCalled();

        // Clean up
        await toolsWebviewModel.cancelActiveToolRun();
      });
    });

    describe("callTool success", () => {
      it("should send exchange with tool result from state", async () => {
        // Setup
        const requestId = "request-123";
        const toolUseId = "tool-use-123";

        // Mock successful tool call
        mockExtensionClient.callTool.mockResolvedValue({
          text: "Tool completed successfully",
          isError: false,
        });

        // Mock getLastToolUseState to return matching state (so exchange gets sent)
        mockConversationModel.getLastToolUseState.mockReturnValue({
          requestId,
          toolUseId,
          phase: ToolUsePhase.completed,
          result: {
            isError: false,
            text: "Tool completed successfully",
          },
        });

        // Mock getToolUseState to return the state that callTool will create
        mockConversationModel.getToolUseState.mockReturnValue({
          requestId,
          toolUseId,
          phase: ToolUsePhase.completed,
          result: {
            isError: false,
            text: "Tool completed successfully",
          },
        });

        // Call callTool
        await toolsWebviewModel.callTool(requestId, toolUseId, "test-tool", {}, [], "conv-123");

        // Verify sendExchange was called with the tool result from state
        expect(mockConversationModel.sendExchange).toHaveBeenCalledWith({
          /* eslint-disable @typescript-eslint/naming-convention */
          request_message: "",
          status: ExchangeStatus.draft,
          structured_request_nodes: [
            {
              id: 1,
              type: ChatRequestNodeType.TOOL_RESULT,
              tool_result_node: {
                content: "Tool completed successfully",
                is_error: false,
                tool_use_id: toolUseId,
              },
            },
          ],
          model_id: undefined,
          /* eslint-enable @typescript-eslint/naming-convention */
        });
      });
    });

    describe("callTool error", () => {
      it("should send exchange with error message from state", async () => {
        // Setup
        const requestId = "request-123";
        const toolUseId = "tool-use-123";
        const errorMessage = "Tool execution failed";

        // Mock tool call error
        mockExtensionClient.callTool.mockRejectedValue(new Error(errorMessage));

        // Mock getLastToolUseState to return matching state (so exchange gets sent)
        mockConversationModel.getLastToolUseState.mockReturnValue({
          requestId,
          toolUseId,
          phase: ToolUsePhase.error,
          result: {
            isError: true,
            text: errorMessage,
          },
        });

        // Mock getToolUseState to return the error state that callTool will create
        mockConversationModel.getToolUseState.mockReturnValue({
          requestId,
          toolUseId,
          phase: ToolUsePhase.error,
          result: {
            isError: true,
            text: errorMessage,
          },
        });

        // Call callTool
        await toolsWebviewModel.callTool(requestId, toolUseId, "test-tool", {}, [], "conv-123");

        // Verify sendExchange was called with the error from state
        expect(mockConversationModel.sendExchange).toHaveBeenCalledWith({
          /* eslint-disable @typescript-eslint/naming-convention */
          request_message: "",
          status: ExchangeStatus.draft,
          structured_request_nodes: [
            {
              id: 1,
              type: ChatRequestNodeType.TOOL_RESULT,
              tool_result_node: {
                content: errorMessage,
                is_error: true,
                tool_use_id: toolUseId,
              },
            },
          ],
          model_id: undefined,
          /* eslint-enable @typescript-eslint/naming-convention */
        });
      });
    });
  });
});
