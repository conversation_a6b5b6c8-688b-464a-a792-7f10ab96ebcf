import { writable } from "svelte/store";

/**
 * Interface for the memory update notification
 */
export interface MemoryUpdateNotification {
  /** Timestamp when the update occurred */
  timestamp: number;
}

/**
 * Store for handling memory updates and animations
 */
class MemoryStore {
  // Delay before clearing the memory update notification
  // eslint-disable-next-line @typescript-eslint/naming-convention
  private static CLEAR_DELAY_MS = 10000; // 10 seconds

  // Store for memory update notifications
  private memoryUpdates = writable<MemoryUpdateNotification | null>(null);
  private clearTimeout: ReturnType<typeof setTimeout> | undefined = undefined;

  /**
   * Notify that memories have been updated
   */
  notifyMemoryUpdated(): void {
    // If we have recently notified/updated, don't update
    if (this.clearTimeout) {
      return;
    }

    // Update the store with a new notification
    this.memoryUpdates.set({
      timestamp: Date.now(),
    });

    clearTimeout(this.clearTimeout);
    this.clearTimeout = undefined;

    this.clearTimeout = setTimeout(() => {
      this.memoryUpdates.set(null);
      this.clearTimeout = undefined;
    }, MemoryStore.CLEAR_DELAY_MS);
  }

  /**
   * Subscribe to memory updates
   */
  subscribe(callback: (notification: MemoryUpdateNotification | null) => void) {
    return this.memoryUpdates.subscribe(callback);
  }
}

// Export a singleton instance
export const memoryStore = new MemoryStore();
