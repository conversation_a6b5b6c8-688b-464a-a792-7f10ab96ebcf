/**
 * @file send-mode-model.ts
 * Model for managing send mode state and persistence
 */

import { writable, type Readable } from "svelte/store";
import { SendMode } from "../types/send-mode";

/**
 * Interface for the SendModeModel
 */
export interface ISendModeModel {
  /** Readable store for the current send mode */
  mode: Readable<SendMode>;
  /** Set the current send mode */
  setMode(mode: SendMode): void;
  /** Get the current send mode value */
  getCurrentMode(): SendMode;
  /** Initialize the mode from stored state */
  initializeFromState(initialMode?: SendMode): void;
}

/**
 * Model for managing send mode state
 */
export class SendModeModel implements ISendModeModel {
  private _mode = writable<SendMode>(SendMode.send);
  private _currentMode: SendMode = SendMode.send;

  constructor() {
    // Subscribe to mode changes to keep current value in sync
    this._mode.subscribe((mode) => {
      this._currentMode = mode;
    });
  }

  /**
   * Readable store for the current send mode
   */
  get mode(): Readable<SendMode> {
    return this._mode;
  }

  /**
   * Set the current send mode
   */
  setMode(mode: SendMode): void {
    this._mode.set(mode);
  }

  /**
   * Get the current send mode value
   */
  getCurrentMode(): SendMode {
    return this._currentMode;
  }

  /**
   * Initialize the mode from stored state
   */
  initializeFromState(initialMode?: SendMode): void {
    if (initialMode && Object.values(SendMode).includes(initialMode)) {
      this._mode.set(initialMode);
    }
  }
}
