/**
 * @file send-mode-model.test.ts
 * Tests for the SendModeModel class
 */

import { describe, it, expect, beforeEach } from "vitest";
import { get } from "svelte/store";
import { SendModeModel, type ISendModeModel } from "./send-mode-model";
import { SendMode } from "../types/send-mode";

describe("SendModeModel", () => {
  let sendModeModel: ISendModeModel;

  beforeEach(() => {
    sendModeModel = new SendModeModel();
  });

  describe("initialization", () => {
    it("should initialize with default send mode", () => {
      expect(sendModeModel.getCurrentMode()).toBe(SendMode.send);
      expect(get(sendModeModel.mode)).toBe(SendMode.send);
    });

    it("should keep current mode in sync with store", () => {
      sendModeModel.setMode(SendMode.addTask);
      expect(sendModeModel.getCurrentMode()).toBe(SendMode.addTask);
      expect(get(sendModeModel.mode)).toBe(SendMode.addTask);
    });
  });

  describe("setMode", () => {
    it("should update mode to send", () => {
      sendModeModel.setMode(SendMode.send);
      expect(sendModeModel.getCurrentMode()).toBe(SendMode.send);
      expect(get(sendModeModel.mode)).toBe(SendMode.send);
    });

    it("should update mode to addTask", () => {
      sendModeModel.setMode(SendMode.addTask);
      expect(sendModeModel.getCurrentMode()).toBe(SendMode.addTask);
      expect(get(sendModeModel.mode)).toBe(SendMode.addTask);
    });

    it("should allow switching between modes", () => {
      // Start with send mode
      sendModeModel.setMode(SendMode.send);
      expect(sendModeModel.getCurrentMode()).toBe(SendMode.send);

      // Switch to addTask mode
      sendModeModel.setMode(SendMode.addTask);
      expect(sendModeModel.getCurrentMode()).toBe(SendMode.addTask);

      // Switch back to send mode
      sendModeModel.setMode(SendMode.send);
      expect(sendModeModel.getCurrentMode()).toBe(SendMode.send);
    });
  });

  describe("getCurrentMode", () => {
    it("should return current mode value", () => {
      expect(sendModeModel.getCurrentMode()).toBe(SendMode.send);

      sendModeModel.setMode(SendMode.addTask);
      expect(sendModeModel.getCurrentMode()).toBe(SendMode.addTask);
    });

    it("should be consistent with store value", () => {
      sendModeModel.setMode(SendMode.addTask);
      expect(sendModeModel.getCurrentMode()).toBe(get(sendModeModel.mode));

      sendModeModel.setMode(SendMode.send);
      expect(sendModeModel.getCurrentMode()).toBe(get(sendModeModel.mode));
    });
  });

  describe("initializeFromState", () => {
    it("should initialize with valid send mode", () => {
      sendModeModel.initializeFromState(SendMode.addTask);
      expect(sendModeModel.getCurrentMode()).toBe(SendMode.addTask);
      expect(get(sendModeModel.mode)).toBe(SendMode.addTask);
    });

    it("should initialize with send mode", () => {
      sendModeModel.setMode(SendMode.addTask); // Change from default
      sendModeModel.initializeFromState(SendMode.send);
      expect(sendModeModel.getCurrentMode()).toBe(SendMode.send);
      expect(get(sendModeModel.mode)).toBe(SendMode.send);
    });

    it("should ignore undefined initial mode", () => {
      sendModeModel.setMode(SendMode.addTask); // Change from default
      sendModeModel.initializeFromState(undefined);
      expect(sendModeModel.getCurrentMode()).toBe(SendMode.addTask); // Should remain unchanged
    });

    it("should ignore invalid initial mode", () => {
      sendModeModel.setMode(SendMode.addTask); // Change from default
      sendModeModel.initializeFromState("invalid" as SendMode);
      expect(sendModeModel.getCurrentMode()).toBe(SendMode.addTask); // Should remain unchanged
    });

    it("should handle null initial mode", () => {
      sendModeModel.setMode(SendMode.addTask); // Change from default
      sendModeModel.initializeFromState(null as any);
      expect(sendModeModel.getCurrentMode()).toBe(SendMode.addTask); // Should remain unchanged
    });
  });

  describe("mode store reactivity", () => {
    it("should notify subscribers when mode changes", () => {
      let notifiedValue: SendMode | undefined;
      const unsubscribe = sendModeModel.mode.subscribe((mode) => {
        notifiedValue = mode;
      });

      // Initial value should be send
      expect(notifiedValue).toBe(SendMode.send);

      // Change to addTask
      sendModeModel.setMode(SendMode.addTask);
      expect(notifiedValue).toBe(SendMode.addTask);

      // Change back to send
      sendModeModel.setMode(SendMode.send);
      expect(notifiedValue).toBe(SendMode.send);

      unsubscribe();
    });

    it("should handle multiple subscribers", () => {
      let subscriber1Value: SendMode | undefined;
      let subscriber2Value: SendMode | undefined;

      const unsubscribe1 = sendModeModel.mode.subscribe((mode) => {
        subscriber1Value = mode;
      });

      const unsubscribe2 = sendModeModel.mode.subscribe((mode) => {
        subscriber2Value = mode;
      });

      sendModeModel.setMode(SendMode.addTask);

      expect(subscriber1Value).toBe(SendMode.addTask);
      expect(subscriber2Value).toBe(SendMode.addTask);

      unsubscribe1();
      unsubscribe2();
    });
  });
});
