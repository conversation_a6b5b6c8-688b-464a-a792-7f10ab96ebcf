import { describe, it, expect, beforeEach, vi, afterEach } from "vitest";
import {
  OnboardingWorkspaceModel,
  SUMMARY_PROMPT,
  NEW_QUESTIONS_PROMPT,
} from "./onboarding-workspace-model";
import { type ChatModel } from "./chat-model";
import { ChatFlagsModel } from "./chat-flags-model";
import { WebViewMessageType } from "$vscode/src/webview-providers/webview-messages";
import { ExchangeStatus, ChatItemType } from "../types/chat-message";

describe("OnboardingWorkspaceModel", () => {
  let onboardingModel: OnboardingWorkspaceModel;
  let mockChatModel: ChatModel;
  let mockFlagsModel: ChatFlagsModel;

  beforeEach(() => {
    mockChatModel = {
      currentConversationModel: {
        sendSilentExchange: vi.fn(),
        sendExchange: vi.fn(),
        updateChatItem: vi.fn(),
        resendTurn: vi.fn(),
        chatHistory: {
          findLast: vi.fn(),
        },
      },
      extensionClient: {
        showAugmentPanel: vi.fn(),
      },
    } as unknown as ChatModel;

    mockFlagsModel = new ChatFlagsModel({
      modelDisplayNameToId: { overview: "overview-model-id" },
    });

    onboardingModel = new OnboardingWorkspaceModel(mockChatModel, mockFlagsModel);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it("should generate project summary and questions", async () => {
    const mockSummaryResponse = {
      id: "summary-id",
      /* eslint-disable @typescript-eslint/naming-convention */
      request_id: "summary-request-id",
      request_message: "Summary prompt",
      /* eslint-enable @typescript-eslint/naming-convention */
      status: ExchangeStatus.draft,
      type: ChatItemType.summaryResponse,
      content: "Project summary",
    };

    const mockSendSilentExchange = vi.fn().mockResolvedValue({
      responseText: "Question 1\nQuestion 2\nQuestion 3",
      requestId: "questions-request-id",
    });

    const mockSendExchange = vi.fn().mockResolvedValue({
      status: ExchangeStatus.draft,
      type: ChatItemType.summaryResponse,
      content: "Project summary",
    });

    mockChatModel.currentConversationModel.sendSilentExchange = mockSendSilentExchange;
    mockChatModel.currentConversationModel.sendExchange = mockSendExchange;

    // Trigger the showProjectSummary method
    const event = new MessageEvent("message", {
      data: { type: WebViewMessageType.shouldShowSummary },
    });

    mockChatModel.currentConversationModel.chatHistory.findLast = vi
      .fn()
      .mockReturnValue(mockSummaryResponse);

    await onboardingModel.handleMessageFromExtension(event);

    // Verify that sendSilentExchange was called with the questions prompt
    expect(mockSendSilentExchange).toHaveBeenCalledWith(
      expect.objectContaining({
        /* eslint-disable @typescript-eslint/naming-convention */
        request_message: NEW_QUESTIONS_PROMPT,
        disableSelectedCodeDetails: true,
        model_id: undefined,
        /* eslint-enable @typescript-eslint/naming-convention */
      }),
    );

    // Verify that sendExchange was called with the summary prompt
    expect(mockSendExchange).toHaveBeenCalledWith(
      expect.objectContaining({
        /* eslint-disable @typescript-eslint/naming-convention */
        request_message: SUMMARY_PROMPT,
        chatItemType: ChatItemType.summaryResponse,
        status: ExchangeStatus.draft,
        disableSelectedCodeDetails: true,
        /* eslint-enable @typescript-eslint/naming-convention */
      }),
      true,
    );

    // Verify that the chat model's showAugmentPanel was called to show the augment panel
    expect(mockChatModel.extensionClient.showAugmentPanel).toHaveBeenCalled();

    // Verify that the summary response was updated with the questions
    vi.waitFor(
      () => {
        expect(mockChatModel.currentConversationModel.updateChatItem).toHaveBeenCalledWith(
          mockSummaryResponse.request_id,
          {
            questionsLoaded: true,
            questions: ["Question 1", "Question 2", "Question 3"],
          },
        );
      },
      { timeout: 1000 },
    );
  });
});
