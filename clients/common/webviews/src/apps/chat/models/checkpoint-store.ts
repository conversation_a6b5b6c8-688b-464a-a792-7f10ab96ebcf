import { writable, type Readable, derived, get, readonly, type Writable } from "svelte/store";
import {
  WebViewMessageType,
  type WebViewMessage,
} from "$vscode/src/webview-providers/webview-messages";
import { memoryStore } from "./memory-store";
import { type ChatAgentEdit } from "@augment-internal/sidecar-libs/src/webview-messages/message-types/agent-messages";
import {
  ChatItemType,
  ExchangeStatus,
  isChatItemAgenticCheckpointDelimiter,
  type AgenticCheckpointDelimiter,
} from "$common-webviews/src/apps/chat/types/chat-message";
import { ConversationModel } from "./conversation-model";
import type { AgentConversationModel } from "./agent-conversation-model";
import type { IExtensionClient } from "../extension-client";
import type { IDisposable } from "monaco-editor";
import type { MessageConsumer } from "$common-webviews/src/common/utils/message-broker";
import throttle from "lodash/throttle";
import type { Chat<PERSON>lagsModel } from "./chat-flags-model";
import type { IQualifiedPathName } from "@augment-internal/sidecar-libs/src/workspace/workspace-types";
import { isAgentConversation } from "./types";

/**
 * Core interface for checkpoint data access and operations.
 * Provides reactive stores and methods for managing code checkpoints in agent conversations.
 *
 * Checkpoints represent snapshots of code at specific points in time during an agent conversation.
 * They allow users to track, view, and revert to previous states of their codebase.
 */
export interface ICheckpointStore {
  /**
   * Core data stores for checkpoints
   */

  /**
   * All checkpoints in the conversation.
   */
  checkpoints: Readable<AgenticCheckpointDelimiter[]>;

  /**
   * Total number of checkpoints in the conversation.
   */
  totalCheckpointCount: Readable<number>;

  /**
   * Checkpoint indexing and lookup methods
   */

  /**
   * Maps checkpoint UUIDs to their index in the checkpoints array.
   */
  uuidToIdx: Readable<Map<string, number>>;

  /**
   * Gets the index of the checkpoint that contains the given timestamp.
   * @param timestamp The timestamp to search for
   * @returns The index of the checkpoint that contains the timestamp, or undefined if not found
   */
  getCheckpointIdxForTimestamp(timestamp: number): number | undefined;

  /**
   * Gets the checkpoint at the given index.
   * @param idx The index of the checkpoint to get
   * @returns The checkpoint at the given index, or undefined if not found
   */
  getCheckpointAtIdx(idx: number): AgenticCheckpointDelimiter | undefined;

  /**
   * Target checkpoint state properties
   */

  /**
   * The index of the currently selected checkpoint.
   * Can be set by UI components to select a different checkpoint.
   */
  targetCheckpointIdx: Writable<number | undefined>;

  /**
   * The currently selected checkpoint object.
   */
  targetCheckpoint: Readable<AgenticCheckpointDelimiter | undefined>;

  /**
   * Whether the currently selected checkpoint is the latest one.
   */
  isTargetCheckpointLatest: Readable<boolean>;

  /**
   * Checkpoint content and operations
   */

  /**
   * The summary of changes for the target checkpoint.
   */
  targetCheckpointSummary: Readable<ChatAgentEdit[]>;

  /**
   * Gets the summary of changes for the given checkpoint.
   * @param ckpt The checkpoint to get the summary for
   * @returns Promise resolving to an array of ChatAgentEdit objects
   */
  getCheckpointSummary(ckpt: AgenticCheckpointDelimiter): Promise<ChatAgentEdit[]>;

  /**
   * Gets the summary of changes for the checkpoint at the given index.
   * @param idx The index of the checkpoint to get the summary for
   * @returns Promise resolving to an array of ChatAgentEdit objects
   */
  getCheckpointIdxSummary(idx: number): Promise<ChatAgentEdit[]>;

  /**
   * Checkpoint creation and modification methods
   */

  /**
   * Creates a new checkpoint at the current time.
   * This finalizes the current checkpoint and creates a new one for future changes.
   */
  createNewCheckpoint(options?: Partial<AgenticCheckpointDelimiter>): void;

  /**
   * Reverts the conversation to the checkpoint with the given UUID.
   * This will restore all tracked files to their state at the time of the checkpoint.
   * After a successful revert, a new checkpoint is created to track future changes.
   *
   * @param uuid The UUID of the checkpoint to revert to
   * @returns Promise resolving to true if the revert was successful, false otherwise
   */
  revertToCheckpoint(uuid: string): Promise<boolean>;
}

/**
 * Implementation of the ICheckpointStore interface that manages code checkpoints in agent conversations.
 * Provides reactive stores and methods for tracking, viewing, and reverting to previous states of code.
 *
 * Checkpoint Lifecycle:
 *
 * 1. Initial State: [User, Agent, Tool Response, Agent]
 *    - When an agentic run finishes, a checkpoint is created with a timestamp
 *    - Current checkpoint status changes from "draft" to "success"
 *    - A new draft checkpoint is created for future changes
 *
 * 2. After Checkpoint: [User, Agent, Tool Response, Agent, Checkpoint]
 *    - The checkpoint captures the state of code at this point in time
 *    - When user sends a new message, the current draft checkpoint's status is set to "sent"
 *
 * 3. New Message: [User, Agent, Tool Response, Agent, Checkpoint, User]
 *    - The conversation continues with the new checkpoint in place
 *    - This allows for tracking and reverting to specific points in the conversation
 *
 * Checkpoint Status Flow:
 *    - Draft: Initial state, not yet tracking changes (not rendered in UI)
 *    - Sent: Actively tracking changes after user message (not rendered in UI)
 *    - Success: Completed checkpoint with tracked changes (rendered in UI)
 */
export class CheckpointStore implements ICheckpointStore, IDisposable, MessageConsumer {
  private _disposables: IDisposable[] = [];

  /**
   * Filters all of the chat items in the current conversation to only include checkpoints.
   * This is a derived store from the conversation model that tracks historical checkpoints.
   */
  private _chatHistoryCheckpoints: Readable<AgenticCheckpointDelimiter[]>;
  /**
   * The current active checkpoint that is tracking changes.
   * This checkpoint aggregates all "current" changes that are not part of the last-stored
   * checkpoint in the conversation. As is, this checkpoint is not part of the conversation
   * history yet -- it is merely used to aggregate all changes since the *last* stored
   * checkpoint in conversation history
   */
  private _currentCheckpoint: Writable<AgenticCheckpointDelimiter> = writable({
    // eslint-disable-next-line @typescript-eslint/naming-convention
    request_id: crypto.randomUUID(),
    uuid: crypto.randomUUID(),
    chatItemType: ChatItemType.agenticCheckpointDelimiter,
    status: ExchangeStatus.draft,
    fromTimestamp: 0,
    toTimestamp: Number.MAX_SAFE_INTEGER,
  });

  /**
   * All checkpoints in the conversation, including the current one.
   * This is a derived store that combines historical checkpoints with the current one.
   */
  public checkpoints: Readable<AgenticCheckpointDelimiter[]>;

  /**
   * Maps checkpoint UUIDs to their index in the checkpoints array.
   * Used for efficient lookup of checkpoints by UUID.
   */
  public uuidToIdx: Readable<Map<string, number>>;

  /**
   * The index of the currently selected checkpoint in the checkpoints array.
   * This is the checkpoint that is being viewed or manipulated by the user.
   */
  private _targetCheckpointIdx = writable<number | undefined>(undefined);

  /**
   * The currently selected checkpoint object.
   * Derived from _targetCheckpointIdx and checkpoints.
   */
  public targetCheckpoint: Readable<AgenticCheckpointDelimiter | undefined>;

  /**
   * Indicates whether the currently selected checkpoint is the latest one.
   * Used to determine if certain operations (like squash) should be available.
   */
  public isTargetCheckpointLatest: Readable<boolean>;

  /**
   * The total number of checkpoints in the conversation.
   * Used for UI display and validation.
   */
  public totalCheckpointCount: Readable<number>;

  /**
   * The summary of changes for the target checkpoint.
   * Contains the list of file edits made between this checkpoint and the previous one.
   */
  private _targetCheckpointSummary = writable<ChatAgentEdit[]>([]);

  /**
   * Public readable store for the target checkpoint summary.
   * Used by UI components to display the changes in the selected checkpoint.
   */
  public targetCheckpointSummary = readonly(this._targetCheckpointSummary);

  /**
   * Indicates whether the target checkpoint has any changes.
   * Derived from targetCheckpointSummary to check if there are any non-trivial changes.
   */
  public targetCheckpointHasChanges: Readable<boolean> = derived(
    this.targetCheckpointSummary,
    ($targetCheckpointSummary) => {
      return $targetCheckpointSummary.some(
        (file) =>
          (file.changesSummary?.totalAddedLines ?? 0 > 0) ||
          (file.changesSummary?.totalRemovedLines ?? 0 > 0),
      );
    },
  );

  constructor(
    private _flagsModel: ChatFlagsModel,
    private _conversationModel: ConversationModel,
    private _extensionClient: IExtensionClient,
    private _agentConversationModel?: AgentConversationModel,
  ) {
    // All checkpoints that are historical. If the last item in the conversation is a checkpoint,
    // it is not included in this store, since it is a "current" checkpoint.
    this._chatHistoryCheckpoints = derived(
      [this._conversationModel],
      ([$conversationModel], set) => {
        const checkpoints = $conversationModel.chatHistory.filter(
          isChatItemAgenticCheckpointDelimiter,
        );
        // If the last 2 UUIDs are the same, assume we are still in the same checkpoint. This is not
        // fully fool-proof, but since the UUIDs are randomly generated, the chance of a collision is
        // negligibly low.
        if (
          checkpoints?.length === get(this._chatHistoryCheckpoints)?.length &&
          checkpoints?.at(-1)?.uuid === get(this._chatHistoryCheckpoints)?.at(-1)?.uuid
        ) {
          return;
        }

        this._currentCheckpoint.update((ckpt) => ({
          ...ckpt,
          fromTimestamp: checkpoints.at(-1)?.toTimestamp ?? 0,
          toTimestamp: Number.MAX_SAFE_INTEGER,
        }));
        set(checkpoints);
      },
    );

    this.checkpoints = derived(
      [this._chatHistoryCheckpoints, this._currentCheckpoint],
      ([$chatHistoryCheckpoints, $currentCheckpoint]) => {
        return [...$chatHistoryCheckpoints, $currentCheckpoint].filter(
          (item) => item.status !== ExchangeStatus.draft,
        );
      },
    );

    // Total number of checkpoints
    this.totalCheckpointCount = derived(this.checkpoints, ($checkpoints) => {
      return $checkpoints.length;
    });

    // Map from UUID to index, for quick lookup
    this.uuidToIdx = derived(this.checkpoints, ($checkpoints) => {
      return new Map($checkpoints.map((c, idx) => [c.uuid, idx]));
    });

    // The target checkpoint
    this.targetCheckpoint = derived(
      [this.targetCheckpointIdx, this.checkpoints],
      ([$targetCheckpointIdx, $checkpoints]) => {
        return $checkpoints.at($targetCheckpointIdx ?? -1);
      },
    );

    // Is the target checkpoint the latest one known?
    this.isTargetCheckpointLatest = derived(
      [this.targetCheckpointIdx, this.checkpoints],
      ([$targetCheckpointIdx, $checkpoints]) => {
        return (
          $targetCheckpointIdx === $checkpoints.length - 1 || $targetCheckpointIdx === undefined
        );
      },
    );

    // Collect all dispose functions first
    const disposeFunctions = [
      this._conversationModel.onSendExchange(
        this._doNothingOnNotAgentConversation(() => {
          // When a user sends a message, we should mark the current checkpoint as sent
          if (get(this._currentCheckpoint).status === ExchangeStatus.draft) {
            this._currentCheckpoint.update((ckpt) => ({
              ...ckpt,
              status: ExchangeStatus.sent,
            }));
          }
        }),
      ),

      // Refresh agent edits when target checkpoint changes
      this.targetCheckpoint.subscribe(
        this._doNothingOnAgentDisabled(() => {
          this._refreshAgentEdits();
        }),
      ),

      // Refresh agent edits when checkpoint count changes and target is latest
      this.checkpoints.subscribe(
        this._doNothingOnAgentDisabled(() => {
          if (get(this.isTargetCheckpointLatest)) {
            this._refreshAgentEdits();
          }
        }),
      ),

      // Reset state on new conversation
      this._conversationModel.onNewConversation(() => {
        this.targetCheckpointIdx.set(undefined);
        this._targetCheckpointSummary.set([]);
        this._refreshAgentEdits();
      }),

      // Scroll to target checkpoint when it changes
      // TODO(Eric): re-enable this
      // this.targetCheckpointIdx.subscribe(
      //   this._doNothingOnAgentDisabled(($targetIdx) => {
      //     if ($targetIdx !== undefined) {
      //       const targetElement = document.querySelector(
      //         `[data-checkpoint-number="${$targetIdx}"]`,
      //       );
      //       targetElement?.scrollIntoView({ behavior: "smooth", block: "center" });
      //     }
      //   }),
      // ),

      // Unset target checkpoint when user sends a message in agent mode
      this._conversationModel.onSendExchange(
        this._doNothingOnAgentDisabled(() => this.targetCheckpointIdx.set(undefined)),
      ),
    ];

    // Add all dispose functions to the disposables collection
    disposeFunctions.forEach((disposeFunction) => {
      this._disposables.push({ dispose: disposeFunction });
    });
  }

  public registerAgentConversationModel(agentConversationModel: AgentConversationModel): void {
    this._agentConversationModel = agentConversationModel;
  }

  /**
   * Creates a function that does nothing if agent mode is disabled.
   * This is a utility method to prevent checkpoint operations when agent mode is disabled.
   *
   * @param fn The function to wrap
   * @returns A function that calls fn only if agent mode is enabled
   */
  private _doNothingOnAgentDisabled = <TArgs extends any[]>(
    fn: (...args: TArgs) => void,
  ): ((...args: TArgs) => void) => {
    return (...args: TArgs) => {
      if (this._flagsModel.enableAgentMode) {
        fn(...args);
      }
    };
  };

  /**
   * Creates a function that does nothing if the current conversation is not an agent conversation.
   * This is a stricter version of _doNothingOnAgentDisabled that also checks if the conversation is an agent conversation.
   *
   * @param fn The function to wrap
   * @returns A function that calls fn only if agent mode is enabled and the conversation is an agent conversation
   */
  private _doNothingOnNotAgentConversation = <TArgs extends any[], TResult>(
    fn: (...args: TArgs) => TResult,
  ): ((...args: TArgs) => TResult | undefined) => {
    return (...args: TArgs) => {
      if (this._flagsModel.enableAgentMode && isAgentConversation(get(this._conversationModel))) {
        return fn(...args);
      }
    };
  };

  /**
   * Gets the writable store for the target checkpoint index.
   * @returns The writable store for the target checkpoint index
   */
  public get targetCheckpointIdx(): Writable<number | undefined> {
    // TODO(Eric): do not allow for setting target checkpoint idx at the moment,
    // it should *always* be undefined. Eventually, we should just return the base store.
    return {
      subscribe: this._targetCheckpointIdx.subscribe,
      set: () => this._targetCheckpointIdx.set(undefined),
      update: () => this._targetCheckpointIdx.set(undefined),
    };
  }

  public get currentCheckpoint(): Readable<AgenticCheckpointDelimiter> {
    return this._currentCheckpoint;
  }

  /**
   * Gets the checkpoints as an array. This is a convenience method to avoid
   * having to call `get` on the `checkpoints` store every time we need to access it.
   * @returns The checkpoints as an array
   */
  private get _checkpoints(): AgenticCheckpointDelimiter[] {
    return get(this.checkpoints);
  }

  /**
   * Gets the target checkpoint. This is a convenience method to avoid
   * having to call `get` on the `targetCheckpoint` store every time we need to access it.
   * @returns The target checkpoint, or undefined if not found
   */
  private get _targetCheckpoint(): AgenticCheckpointDelimiter | undefined {
    return get(this.targetCheckpoint);
  }

  /**
   * Refreshes the agent edits for the given checkpoint.
   * @param checkpoint The checkpoint to refresh the agent edits for
   */
  private _refreshAgentEditsImmediate = async () => {
    const targetCheckpoint = this._targetCheckpoint;
    if (!targetCheckpoint) {
      return;
    }

    // Use the conversation's baseline timestamp from extraData instead of 0
    const baselineTimestamp = this._agentConversationModel
      ? this._agentConversationModel.getBaselineTimestamp()
      : 0;

    const edits = await this.getCheckpointSummary({
      ...targetCheckpoint,
      fromTimestamp: baselineTimestamp,
      toTimestamp: Number.MAX_SAFE_INTEGER,
    });
    this._targetCheckpointSummary.set(edits);
  };

  // Throttle refreshes to 2 per second
  private _refreshAgentEdits = throttle(this._refreshAgentEditsImmediate, 500, {
    leading: true,
    trailing: true,
  });

  /**
   * Gets the index of the checkpoint that contains the given timestamp.
   * @param timestamp The timestamp to search for
   * @returns The index of the checkpoint that contains the timestamp, or undefined if not found
   */
  public getCheckpointIdxForTimestamp(timestamp: number): number | undefined {
    return this._checkpoints.findIndex(
      (c) => c.fromTimestamp <= timestamp && c.toTimestamp >= timestamp,
    );
  }

  /**
   * Gets the checkpoint at the given index in the checkpoints array.
   *
   * @param idx The index of the checkpoint to get
   * @returns The checkpoint at the given index, or undefined if not found
   */
  public getCheckpointAtIdx(idx: number): AgenticCheckpointDelimiter | undefined {
    return this._checkpoints.at(idx);
  }

  /**
   * Creates a new checkpoint at the current time.
   * Does so by taking the "current" checkpoint, updating it with the current time,
   * and then adding it to the conversation.
   *
   * Checkpoint Creation Flow:
   * 1. When an agentic run completes, this method is called
   * 2. The current draft checkpoint is finalized with:
   *    - Status changed from "draft" to "success"
   *    - toTimestamp set to current time
   *    - Added to conversation history
   * 3. A new draft checkpoint is created with:
   *    - New UUID
   *    - Status set to "draft"
   *    - fromTimestamp set to current time
   *    - toTimestamp set to MAX_SAFE_INTEGER (representing "now")
   *
   * This method is typically called:
   * - When an agent completes a turn with changes
   * - After a revert operation
   * - After accepting or rejecting all changes
   */
  public createNewCheckpoint(options: Partial<AgenticCheckpointDelimiter> = {}): void {
    const currentCheckpoint = get(this._currentCheckpoint);
    const timestamp = Date.now();

    const newChatItem = {
      ...currentCheckpoint,
      status: ExchangeStatus.success,
      toTimestamp: timestamp,
      ...options,
    };

    // Create a new "current" checkpoint
    this._currentCheckpoint.set({
      // eslint-disable-next-line @typescript-eslint/naming-convention
      request_id: crypto.randomUUID(),
      uuid: crypto.randomUUID(),
      chatItemType: ChatItemType.agenticCheckpointDelimiter,
      status: ExchangeStatus.draft,
      fromTimestamp: timestamp,
      toTimestamp: Number.MAX_SAFE_INTEGER,
    });

    // Add the finalized checkpoint to the conversation
    this._conversationModel.addChatItem(newChatItem);
  }

  /**
   * Gets the summary of changes for the given checkpoint.
   * Retrieves the list of file edits made between the checkpoint's fromTimestamp and toTimestamp.
   *
   * @param ckpt The checkpoint to get the summary for
   * @returns Promise resolving to an array of ChatAgentEdit objects representing the changes
   */
  public async getCheckpointSummary(ckpt: AgenticCheckpointDelimiter): Promise<ChatAgentEdit[]> {
    const response = await this._extensionClient.getAgentEditList(
      ckpt.fromTimestamp,
      ckpt.toTimestamp,
    );
    return response.edits;
  }

  /**
   * A utility method that gets the summary of changes for the checkpoint at the given index.
   * Convenience method that combines getCheckpointAtIdx and getCheckpointSummary.
   *
   * @param idx The index of the checkpoint to get the summary for
   * @returns Promise resolving to an array of ChatAgentEdit objects, or empty array if checkpoint not found
   */
  public async getCheckpointIdxSummary(idx: number): Promise<ChatAgentEdit[]> {
    const checkpoint = this._checkpoints.at(idx);
    if (!checkpoint) {
      return [];
    }
    return this.getCheckpointSummary(checkpoint);
  }

  /**
   * Reverts the conversation to the checkpoint with the given UUID.
   * This will restore all tracked files to their state at the time of the checkpoint.
   * After a successful revert, a new checkpoint is created to track future changes.
   *
   * @param uuid The UUID of the checkpoint to revert to
   * @returns Promise resolving to true if the revert was successful, false otherwise
   */
  public async revertToCheckpoint(uuid: string): Promise<boolean> {
    const idx = get(this.uuidToIdx).get(uuid);
    if (idx === undefined) {
      return false;
    }
    const checkpoint = this._checkpoints.at(idx);
    if (!checkpoint) {
      return false;
    }
    const currConversationId = get(this._conversationModel).id;
    const ok = await this._extensionClient.revertToTimestamp(checkpoint.toTimestamp);
    if (ok || get(this._conversationModel).id === currConversationId) {
      this.createNewCheckpoint({ revertTarget: { uuid } });
    }
    return ok;
  }

  /**
   * Reverts a specific document to its state at the given timestamp.
   * Unlike revertToTimestamp, this only affects a single file rather than the entire codebase.
   * After a successful revert, a new checkpoint is created to track future changes.
   *
   * @param pathName The qualified path name of the document to revert
   * @param timestamp The timestamp to revert to
   * @returns Promise resolving to true if the revert was successful, false otherwise
   */
  public async revertDocumentToTimestamp(
    pathName: IQualifiedPathName,
    timestamp: number,
  ): Promise<boolean> {
    const currConversationId = get(this._conversationModel).id;
    const ok = await this._extensionClient.revertToTimestamp(timestamp, [pathName]);

    // Since this is an async call, we should only add this reversion marker if we are still on the same conversation.
    if (ok && get(this._conversationModel).id === currConversationId) {
      this.createNewCheckpoint({ revertTarget: { filePath: pathName } });
    }
    return ok;
  }

  private _revertToTimestamp = this._doNothingOnNotAgentConversation(async (timestamp: number) => {
    await this._extensionClient.revertToTimestamp(timestamp);
    this.createNewCheckpoint();
    this._refreshAgentEdits();
  });

  /**
   * Accepts all pending agent edits.
   * This updates the conversation's baseline timestamp to the latest checkpoint's timestamp
   * instead of clearing the checkpoint history.
   * After accepting, a new checkpoint is created to track future changes.
   *
   * @returns Promise resolving to true if the accept operation was successful, false otherwise
   */
  public async acceptAll(): Promise<boolean> {
    const latestCheckpoint = this._checkpoints.at(-1);
    if (latestCheckpoint) {
      // Update the conversation's baseline timestamp instead of clearing checkpoints
      const timestamp = latestCheckpoint.toTimestamp;

      // Only update if this is an agent conversation and we have an agent conversation model
      if (isAgentConversation(get(this._conversationModel)) && this._agentConversationModel) {
        this._agentConversationModel.updateBaselineTimestamp(timestamp);
      }

      // Notify the extension client that we've accepted all edits
      await this._extensionClient.acceptAllAgentEdits();
      this._refreshAgentEdits();
      return true;
    }
    return false;
  }

  /**
   * Rejects all pending agent edits.
   * This resets the conversation's baseline timestamp to 0 instead of clearing the checkpoint history.
   * After rejecting, a new checkpoint is created to track future changes.
   *
   * @returns Promise resolving to true if the reject operation was successful, false otherwise
   */
  public async rejectAll(): Promise<boolean> {
    const ok = await this._extensionClient.openConfirmationModal({
      title: "Discard All Changes",
      message:
        "Are you sure you want to discard all changes? This will apply to all changes whether made by the agent or yourself.",
      confirmButtonText: "Discard",
      cancelButtonText: "Cancel",
    });

    if (ok) {
      // We want to reject everything *since* the baseline checkpoint's current time.
      // This is because we've already accepted everything else -- so "Undo" should not take
      // into account "out-of-scope" changes that were already accepted.
      const timestamp = this._agentConversationModel?.getBaselineTimestamp() ?? 0;

      // Only update if this is an agent conversation and we have an agent conversation model
      if (isAgentConversation(get(this._conversationModel)) && this._agentConversationModel) {
        // Update the baseline timestamp
        this._agentConversationModel.updateBaselineTimestamp(timestamp);
      }

      // Revert to the state at the baseline
      await this._revertToTimestamp(timestamp);
      this._refreshAgentEdits();
    }
    return ok;
  }

  /**
   * MessageConsumer implementation.
   * Handles messages from the extension to update the checkpoint state.
   *
   * @param e The message event from the extension
   * @returns true if the message was handled, false otherwise
   */
  public handleMessageFromExtension(e: MessageEvent<WebViewMessage>): boolean {
    const msg = e.data;
    switch (msg.type) {
      case WebViewMessageType.chatAgentEditListHasUpdates:
        this._refreshAgentEdits();
        break;
      case WebViewMessageType.chatMemoryHasUpdates:
        // Notify the memory store that memories have been updated
        memoryStore.notifyMemoryUpdated();
        break;
    }
    return true;
  }

  /**
   * Creates an original checkpoint if one does not already exist.
   * This is called when an agent conversation is started to ensure there's a baseline checkpoint.
   * Only creates a checkpoint if:
   * - The conversation is agentic
   * - There are no existing checkpoints in the conversation
   * - The conversation is truly new (empty or uses the special NEW_AGENT_KEY)
   */
  public maybeCreateOriginalCheckpoint = this._doNothingOnNotAgentConversation(() => {
    const conversation = get(this._conversationModel);

    // Check if there are no checkpoints in the conversation
    const hasNoCheckpoints = !conversation.chatHistory.some(isChatItemAgenticCheckpointDelimiter);
    if (!hasNoCheckpoints) {
      return;
    }

    // Check if the conversation is truly new (empty or uses the special NEW_AGENT_KEY)
    const isNewConversation =
      ConversationModel.isEmpty(conversation) || ConversationModel.isNew(conversation);
    if (!isNewConversation) {
      return;
    }

    // Check if there are no checkpoints in the store
    const hasNoStoreCheckpoints = get(this.checkpoints).length === 0;
    if (!hasNoStoreCheckpoints) {
      return;
    }

    // Only create a checkpoint if all conditions are met
    this.createNewCheckpoint();
  });

  /**
   * Updates the baseline timestamp for the conversation.
   * This changes which checkpoints are considered "accepted" and not shown in the agent edit list.
   *
   * @param timestamp - The new baseline timestamp
   */
  public async updateBaselineTimestamp(timestamp: number): Promise<void> {
    if (isAgentConversation(get(this._conversationModel)) && this._agentConversationModel) {
      // Update the baseline timestamp
      this._agentConversationModel.updateBaselineTimestamp(timestamp);
      this._refreshAgentEdits();
    }
  }

  /**
   * IDisposable implementation.
   * Cleans up all disposable resources when the store is no longer needed.
   */
  public dispose = (): void => {
    this._disposables.forEach((disposable) => disposable.dispose());
  };
}
