/* eslint-disable @typescript-eslint/naming-convention */
import { describe, it, expect, beforeEach } from "vitest";
import { get, writable } from "svelte/store";
import { ModelRegistry, DEFAULT_MODEL, type IModel } from "../model-registry";

// Create a minimal mock ChatModel for testing
const createMockChatModel = () => {
  return writable({
    flags: {
      agentChatModel: "test-default-model",
      modelRegistry: {
        "test-default-model": "Test Default Model",
        "gpt-4": "GPT-4",
        "claude-3": "Claude 3",
      },
    },
    currentConversationModel: { selectedModelId: null },
  }) as any;
};

describe("ModelRegistry", () => {
  let registry: ModelRegistry;
  let mockChatModel: any;

  beforeEach(() => {
    mockChatModel = createMockChatModel();
    registry = new ModelRegistry(mockChatModel);
  });

  describe("Svelte store interface", () => {
    it("should initialize with default model", () => {
      expect(get(registry)).toEqual([DEFAULT_MODEL]);
    });

    it("should be subscribable as a Svelte store", () => {
      const values: IModel[][] = [];
      const unsubscribe = registry.subscribe((models) => {
        values.push(models);
      });

      // Initial value should be captured
      expect(values).toHaveLength(1);
      expect(values[0]).toEqual([DEFAULT_MODEL]);

      // Register new models
      const newModels: IModel[] = [
        { id: "gpt-4", displayName: "GPT-4" },
        { id: "claude-3", displayName: "Claude 3" },
      ];

      registry.registerModels(newModels);

      // Should have captured the new value
      expect(values).toHaveLength(2);
      expect(values[1]).toEqual(newModels);

      unsubscribe();
    });

    it("should work with reactive statements", () => {
      // Simulate how Svelte would use the store
      let reactiveValue: IModel[] = [];
      const unsubscribe = registry.subscribe((models) => {
        reactiveValue = models;
      });

      expect(reactiveValue).toEqual([DEFAULT_MODEL]);

      const testModels: IModel[] = [{ id: "test-model", displayName: "Test Model" }];
      registry.registerModels(testModels);

      expect(reactiveValue).toEqual(testModels);

      unsubscribe();
    });
  });

  describe("store access", () => {
    it("should provide access to current models via store subscription", () => {
      expect(get(registry)).toEqual([DEFAULT_MODEL]);

      const newModels: IModel[] = [
        { id: "gpt-4", displayName: "GPT-4" },
        { id: "claude-3", displayName: "Claude 3" },
      ];

      registry.registerModels(newModels);
      expect(get(registry)).toEqual(newModels);
    });
  });

  describe("derived stores", () => {
    beforeEach(() => {
      const testModels: IModel[] = [
        { id: "test-default-model", displayName: "Test Default Model" },
        { id: "gpt-4", displayName: "GPT-4" },
        { id: "claude-3", displayName: "Claude 3" },
      ];
      registry.registerModels(testModels);
    });

    it("should create defaultModel that reacts to default model ID changes", () => {
      // Should return the default model based on mock with id: null
      expect(get(registry.defaultModel)).toEqual({
        id: null,
        displayName: "Test Default Model",
      });

      // Should react to changes in default model ID by updating the mock
      mockChatModel.update((data: any) => ({
        ...data,
        flags: {
          ...data.flags,
          agentChatModel: "gpt-4",
        },
      }));
      expect(get(registry.defaultModel)).toEqual({ id: null, displayName: "GPT-4" });

      // Should fallback to DEFAULT_MODEL if ID not found
      mockChatModel.update((data: any) => ({
        ...data,
        flags: {
          ...data.flags,
          agentChatModel: "non-existent",
        },
      }));
      expect(get(registry.defaultModel)).toEqual(DEFAULT_MODEL);
    });

    it("should create formattedModels that includes default model as first entry with null ID", () => {
      // Should include the default model as first entry with null ID and exclude the actual default model ID
      const formatted = get(registry.formattedModels);
      expect(formatted).toHaveLength(3); // Default (null) + gpt-4 + claude-3 (test-default-model excluded as it's the default)
      expect(formatted[0]).toEqual({ id: null, displayName: "Test Default Model" });
      expect(formatted.some((m) => m.id === "gpt-4")).toBe(true);
      expect(formatted.some((m) => m.id === "claude-3")).toBe(true);
      expect(formatted.some((m) => m.id === "test-default-model")).toBe(false); // Excluded because it's the default model

      // Should react to changes in default model ID
      mockChatModel.update((data: any) => ({
        ...data,
        flags: {
          ...data.flags,
          agentChatModel: "gpt-4",
        },
      }));
      const formattedAfterChange = get(registry.formattedModels);
      expect(formattedAfterChange).toHaveLength(3);
      expect(formattedAfterChange[0]).toEqual({ id: null, displayName: "GPT-4" });
      expect(formattedAfterChange.some((m) => m.id === "claude-3")).toBe(true);
      expect(formattedAfterChange.some((m) => m.id === "test-default-model")).toBe(true);
      expect(formattedAfterChange.some((m) => m.id === "gpt-4")).toBe(false); // Should be filtered out
    });

    it("should fallback to default model when selectedModelId is null", () => {
      // Should fallback to default model when selected is null (returns the formatted default with null ID)
      expect(get(registry.selectedModel)).toEqual({
        id: null,
        displayName: "Test Default Model",
      });
    });

    it("should use selected model when selectedModelId is valid", () => {
      // Should use selected model when available
      mockChatModel.update((data: any) => ({
        ...data,
        currentConversationModel: { selectedModelId: "gpt-4" },
      }));
      expect(get(registry.selectedModel)).toEqual({ id: "gpt-4", displayName: "GPT-4" });
    });

    it("should fallback to default model when selectedModelId is explicitly set to null", () => {
      // First set to a valid model, then back to null
      mockChatModel.update((data: any) => ({
        ...data,
        currentConversationModel: { selectedModelId: "gpt-4" },
      }));

      // Should fallback to default when selected is null
      mockChatModel.update((data: any) => ({
        ...data,
        currentConversationModel: { selectedModelId: null },
      }));
      expect(get(registry.selectedModel)).toEqual({
        id: null,
        displayName: "Test Default Model",
      });
    });

    it("should fallback to DEFAULT_MODEL when neither selected nor default models are found", () => {
      // Should fallback to DEFAULT_MODEL if neither selected nor default are found
      mockChatModel.update((data: any) => ({
        ...data,
        currentConversationModel: { selectedModelId: "non-existent" },
        flags: { ...data.flags, agentChatModel: "also-non-existent" },
      }));
      expect(get(registry.selectedModel)).toEqual(DEFAULT_MODEL);
    });
  });
});
