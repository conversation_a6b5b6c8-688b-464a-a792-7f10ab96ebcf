/* eslint-disable @typescript-eslint/naming-convention */
/**
 * Unit tests for ChatFlagsModel class, specifically testing the new enableGroupedTools feature flag.
 */
import { vi, describe, test, expect } from "vitest";
import { ChatFlagsModel } from "../chat-flags-model";
import { type IChatFlags } from "../types";
import { type ModelInfoRegistryEntry } from "@augment-internal/sidecar-libs/src/api/types";

describe("ChatFlagsModel - enableGroupedTools", () => {
  test("should have enableGroupedTools default to false", () => {
    const model = new ChatFlagsModel();
    expect(model.enableGroupedTools).toBe(false);
  });

  test("should update enableGroupedTools when provided in constructor", () => {
    const initialFlags: Partial<IChatFlags> = {
      enableGroupedTools: true,
    };
    const model = new ChatFlagsModel(initialFlags);
    expect(model.enableGroupedTools).toBe(true);
  });

  test("should update enableGroupedTools via update method", () => {
    const model = new ChatFlagsModel();
    expect(model.enableGroupedTools).toBe(false);

    model.update({ enableGroupedTools: true });
    expect(model.enableGroupedTools).toBe(true);

    model.update({ enableGroupedTools: false });
    expect(model.enableGroupedTools).toBe(false);
  });

  test("should maintain enableGroupedTools value when updating other flags", () => {
    const model = new ChatFlagsModel();
    model.update({ enableGroupedTools: true });
    expect(model.enableGroupedTools).toBe(true);

    // Update other flags, enableGroupedTools should remain true
    model.update({ enableDebugFeatures: true });
    expect(model.enableGroupedTools).toBe(true);
    expect(model.enableErgonomicsUpdate).toBe(true); // This should be true due to enableDebugFeatures
  });

  test("should be independent from enableErgonomicsUpdate", () => {
    const model = new ChatFlagsModel();

    // Test that enableGroupedTools can be true while enableErgonomicsUpdate is false
    model.update({ enableGroupedTools: true, enableDebugFeatures: false });
    expect(model.enableGroupedTools).toBe(true);
    expect(model.enableErgonomicsUpdate).toBe(false);

    // Test that enableErgonomicsUpdate can be true while enableGroupedTools is false
    model.update({ enableGroupedTools: false, enableDebugFeatures: true });
    expect(model.enableGroupedTools).toBe(false);
    expect(model.enableErgonomicsUpdate).toBe(true);

    // Test that both can be true
    model.update({ enableGroupedTools: true, enableDebugFeatures: true });
    expect(model.enableGroupedTools).toBe(true);
    expect(model.enableErgonomicsUpdate).toBe(true);
  });

  test("should notify subscribers when enableGroupedTools changes", () => {
    const model = new ChatFlagsModel();
    const mockSubscriber = vi.fn();

    model.subscribe(mockSubscriber);
    expect(mockSubscriber).toHaveBeenCalledTimes(1); // Initial call

    model.update({ enableGroupedTools: true });
    expect(mockSubscriber).toHaveBeenCalledTimes(2);
    expect(mockSubscriber).toHaveBeenLastCalledWith(model);
  });
});

describe("ChatFlagsModel - modelInfoRegistry", () => {
  test("should have modelInfoRegistry default to empty object", () => {
    const model = new ChatFlagsModel();
    expect(model.modelInfoRegistry).toEqual({});
  });

  test("should update modelInfoRegistry when provided in constructor", () => {
    const mockModelInfoRegistry: Record<string, ModelInfoRegistryEntry> = {
      "claude-sonnet-4": {
        displayName: "Claude Sonnet 4",
        shortName: "Sonnet 4",
        description: "Latest Claude Sonnet model",
        disabled: false,
      },
      "gpt-4": {
        displayName: "GPT-4",
        shortName: "GPT-4",
        description: "OpenAI GPT-4 model",
        disabled: true,
        disabled_reason: "Not available",
      },
    };

    const initialFlags: Partial<IChatFlags> = {
      modelInfoRegistry: mockModelInfoRegistry,
    };
    const model = new ChatFlagsModel(initialFlags);
    expect(model.modelInfoRegistry).toEqual(mockModelInfoRegistry);
  });

  test("should update modelInfoRegistry via update method", () => {
    const model = new ChatFlagsModel();
    expect(model.modelInfoRegistry).toEqual({});

    const mockModelInfoRegistry: Record<string, ModelInfoRegistryEntry> = {
      "claude-opus": {
        displayName: "Claude Opus",
        shortName: "Opus",
        description: "Claude Opus model",
        disabled: false,
      },
    };

    model.update({ modelInfoRegistry: mockModelInfoRegistry });
    expect(model.modelInfoRegistry).toEqual(mockModelInfoRegistry);
  });

  test("should maintain modelInfoRegistry value when updating other flags", () => {
    const mockModelInfoRegistry: Record<string, ModelInfoRegistryEntry> = {
      "test-model": {
        displayName: "Test Model",
        shortName: "Test",
        description: "Test model",
        disabled: false,
      },
    };

    const model = new ChatFlagsModel();
    model.update({ modelInfoRegistry: mockModelInfoRegistry });
    expect(model.modelInfoRegistry).toEqual(mockModelInfoRegistry);

    // Update other flags, modelInfoRegistry should remain unchanged
    model.update({ enableDebugFeatures: true });
    expect(model.modelInfoRegistry).toEqual(mockModelInfoRegistry);
  });

  test("should notify subscribers when modelInfoRegistry changes", () => {
    const model = new ChatFlagsModel();
    const mockSubscriber = vi.fn();

    model.subscribe(mockSubscriber);
    expect(mockSubscriber).toHaveBeenCalledTimes(1); // Initial call

    const mockModelInfoRegistry: Record<string, ModelInfoRegistryEntry> = {
      "new-model": {
        displayName: "New Model",
        shortName: "New",
        description: "New model",
        disabled: false,
      },
    };

    model.update({ modelInfoRegistry: mockModelInfoRegistry });
    expect(mockSubscriber).toHaveBeenCalledTimes(2);
    expect(mockSubscriber).toHaveBeenLastCalledWith(model);
  });
});
