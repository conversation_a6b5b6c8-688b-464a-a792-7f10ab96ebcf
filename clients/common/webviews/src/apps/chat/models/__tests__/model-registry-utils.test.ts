/* eslint-disable @typescript-eslint/naming-convention */
import { describe, it, expect } from "vitest";
import { formatModelRegistryEntries } from "../model-registry-utils";
import type { IChatFlags } from "../types";

describe("formatModelRegistryEntries", () => {
  describe("new modelInfoRegistry format", () => {
    it("should format entries from modelInfoRegistry with full metadata", () => {
      const flags: Partial<IChatFlags> = {
        modelInfoRegistry: {
          "claude-sonnet-4": {
            displayName: "Claude Sonnet 4",
            shortName: "Sonnet 4",
            description: "Advanced reasoning model",
            disabled: false,
          },
          "gpt-4": {
            displayName: "GPT-4",
            shortName: "GPT-4",
            description: "OpenAI's flagship model",
            disabled: true,
            disabled_reason: "Rate limited",
          },
        },
        modelRegistry: {},
      };

      const result = formatModelRegistryEntries(flags as IChatFlags);

      expect(result).toEqual([
        {
          id: "claude-sonnet-4",
          displayName: "Claude Sonnet 4",
          shortName: "Sonnet 4",
          description: "Advanced reasoning model",
          disabled: false,
        },
        {
          id: "gpt-4",
          displayName: "GPT-4",
          shortName: "GPT-4",
          description: "OpenAI's flagship model",
          disabled: true,
          disabled_reason: "Rate limited",
        },
      ]);
    });

    it("should handle empty modelInfoRegistry", () => {
      const flags: Partial<IChatFlags> = {
        modelInfoRegistry: {},
        modelRegistry: {
          "Claude 3": "claude-3",
        },
      };

      const result = formatModelRegistryEntries(flags as IChatFlags);

      expect(result).toEqual([
        {
          id: "claude-3",
          displayName: "Claude 3",
        },
      ]);
    });
  });

  describe("legacy modelRegistry format", () => {
    it("should format entries from legacy modelRegistry", () => {
      const flags: Partial<IChatFlags> = {
        modelInfoRegistry: {},
        modelRegistry: {
          "Claude Sonnet 4": "claude-sonnet-4",
          "GPT-4": "gpt-4",
          "Claude 3": "claude-3",
        },
      };

      const result = formatModelRegistryEntries(flags as IChatFlags);

      expect(result).toEqual([
        {
          id: "claude-sonnet-4",
          displayName: "Claude Sonnet 4",
        },
        {
          id: "gpt-4",
          displayName: "GPT-4",
        },
        {
          id: "claude-3",
          displayName: "Claude 3",
        },
      ]);
    });

    it("should handle missing modelRegistry", () => {
      const flags: Partial<IChatFlags> = {
        modelInfoRegistry: {},
        // modelRegistry is undefined
      };

      const result = formatModelRegistryEntries(flags as IChatFlags);

      expect(result).toEqual([]);
    });

    it("should handle empty modelRegistry", () => {
      const flags: Partial<IChatFlags> = {
        modelInfoRegistry: {},
        modelRegistry: {},
      };

      const result = formatModelRegistryEntries(flags as IChatFlags);

      expect(result).toEqual([]);
    });
  });

  describe("priority handling", () => {
    it("should prioritize modelInfoRegistry over legacy modelRegistry", () => {
      const flags: Partial<IChatFlags> = {
        modelInfoRegistry: {
          "claude-sonnet-4": {
            displayName: "Claude Sonnet 4 (New)",
            shortName: "Sonnet 4",
            disabled: false,
          },
        },
        modelRegistry: {
          "claude-sonnet-4": "Claude Sonnet 4 (Legacy)",
          "gpt-4": "GPT-4",
        },
      };

      const result = formatModelRegistryEntries(flags as IChatFlags);

      expect(result).toEqual([
        {
          id: "claude-sonnet-4",
          displayName: "Claude Sonnet 4 (New)",
          shortName: "Sonnet 4",
          disabled: false,
        },
      ]);
    });
  });
});
