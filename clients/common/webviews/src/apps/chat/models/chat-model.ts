import type { HostInterface } from "$common-webviews/src/common/hosts/host-types";
import {
  SmartPastePrecomputeMode,
  type WebViewMessage,
  WebViewMessageType,
} from "$vscode/src/webview-providers/webview-messages";
import debounce from "lodash.debounce";
import { type Readable, type Writable, writable } from "svelte/store";
import { ExtensionClient, type IExtensionClient } from "../extension-client";
import { type ChatModeModel } from "./chat-mode-model";
import { type IChatFlags, type IConversation, isAgentConversation } from "./types";
import { MessageBroker } from "$common-webviews/src/common/utils/message-broker";

import {
  ChatFlagsModel,
  DEFAULT_BIG_SYNC_THRESHOLD,
  DEFAULT_MAX_TRACKABLE_FILE_COUNT,
  DEFAULT_SMALL_SYNC_THRESHOLD,
} from "./chat-flags-model";
import { type SpecialContextInputModel } from "./context-model";
import { ConversationModel } from "./conversation-model";

import { type AsyncMsgSender } from "$common-webviews/src/common/hosts/async-messaging";
import type { MessageConsumer } from "$common-webviews/src/common/utils/message-broker";
import { type ChatMetricName } from "$vscode/src/metrics/types";
import {
  type ChatRequestNode,
  ChatRequestNodeType,
  ChatResultNodeType,
  type Exchange,
} from "@augment-internal/sidecar-libs/src/chat/chat-types";
import { isImageFile } from "$common-webviews/src/design-system/components/RichTextEditorAugment/plugins/File/file-type-utils";

import { type JSONContent } from "@tiptap/core";
import { type RemoteAgentsModel } from "../../remote-agent-manager/models/remote-agents-model";
import {
  ChatItemType,
  type ExchangeWithStatus,
  isChatItemExchangeWithStatus,
  isChatItemSuccessfulExchange,
  SeenState,
  signInConversationId,
} from "../types/chat-message";
import { NEW_AGENT_KEY } from "./agent-constants";
import { IdleMessageModel } from "./idle-message-model";
import { type ISendModeModel, SendModeModel } from "./send-mode-model";
import { EventTracker, type EventTrackerDependencies } from "../analytics/event-tracker";

import { ConversationPersistenceController } from "./conversation-model/persistence-controller-conversation";
import { RulesModel } from "../../settings/models/rules-model";

export const shareStatus = writable<"idle" | "copying" | "copied" | "failed">("idle");

export enum AgentExecutionMode {
  manual = "manual",
  auto = "auto",
}

export type SortableConversationFieldType =
  | "createdAt"
  | "lastInteractedAt"
  | "lastMessageTimestamp";

/**
 * This class is used to store the state of the chat webview.
 *
 * It conforms to the svelte store pattern to allow for easy integration with
 * the svelte framework.
 */
export class ChatModel implements Readable<IChatModel>, MessageConsumer {
  private _state: StoredState = {
    currentConversationId: undefined,
    conversations: {},
    agentExecutionMode: AgentExecutionMode.manual,
    isPanelCollapsed: true,
    displayedAnnouncements: [],
  };

  public readonly extensionClient: IExtensionClient;

  private _chatFlagsModel: ChatFlagsModel;
  private _currConversationModel: ConversationModel;
  private _chatModeModel: ChatModeModel | undefined; // Will be set after initialization
  private _sendModeModel: ISendModeModel;
  private _flagsLoaded: Writable<boolean> = writable(false); // Track if flags have been loaded
  private _eventTracker: EventTracker | undefined; // Will be set when ChatModeModel is set
  private _rulesModel: RulesModel;

  // Conversation persistence controller for managing save/persist operations
  private _persistenceController: ConversationPersistenceController;
  private _messageBroker: MessageBroker;

  private subscribers: Set<(chatHistory: ChatModel) => void> = new Set();
  public idleMessageModel = new IdleMessageModel();

  // Individual state variables
  public isPanelCollapsed: Writable<boolean>; // Single collapse state for both panels
  public agentExecutionMode: Writable<AgentExecutionMode>;
  public sortConversationsBy: Writable<SortableConversationFieldType>;
  public displayedAnnouncements: Writable<string[]>;

  constructor(
    private readonly _asyncMsgSender: AsyncMsgSender,
    private readonly _host: HostInterface,
    private readonly _specialContextInputModel: SpecialContextInputModel,
    public readonly options: {
      forceAgentConversation?: boolean;
      initialConversation?: IConversation;
      initialFlags?: Partial<IChatFlags>;
      onLoaded?: () => void;
      debounceConfig?: {
        wait?: number;
        maxWait?: number;
      };
    } = {},
  ) {
    // Initialize models
    this._chatFlagsModel = new ChatFlagsModel(options.initialFlags);
    this._messageBroker = new MessageBroker(this._host);
    this._rulesModel = new RulesModel(this._messageBroker, false);
    this.extensionClient = new ExtensionClient(
      this._host,
      this._asyncMsgSender,
      this._chatFlagsModel,
    );

    this._messageBroker.registerConsumer(this._rulesModel);
    this._currConversationModel = new ConversationModel(
      this.extensionClient,
      this._chatFlagsModel,
      this._specialContextInputModel,
      this.saveConversation,
      this._rulesModel,
      {
        forceAgentConversation: options.forceAgentConversation,
      },
    );

    this._sendModeModel = new SendModeModel();

    // Initialize the persistence controller
    this._persistenceController = new ConversationPersistenceController(
      this.extensionClient,
      this._chatFlagsModel,
    );

    // Initialize setState with configurable debounce timing (before initializeSync)
    const debounceWait = options.debounceConfig?.wait ?? 5_000; // Default 5 seconds
    const debounceMaxWait = options.debounceConfig?.maxWait ?? 30_000; // Default 30 seconds
    this.setState = debounce(
      (state: Partial<StoredState>) => {
        // Handle async persistence in a separate method
        this._setStateWithPersistence(state);
      },
      debounceWait,
      { maxWait: debounceMaxWait },
    );

    // Initialize the state (async initialization will be handled in onLoaded)
    this.initializeSync(options.initialConversation);

    // Initialize panel collapse state - use legacy values for backward compatibility if needed
    const initialCollapseState =
      this._state.isPanelCollapsed ??
      this._state.isAgentEditsCollapsed ??
      this._state.isTaskListCollapsed ??
      true;
    this.isPanelCollapsed = writable(initialCollapseState);

    // Initialize agent banner state from stored state or default to false
    // Will be updated with the correct value after chatInitData is loaded
    // However, if the auto mode flag is not enabled, default to manual mode
    this.agentExecutionMode = writable(this._state.agentExecutionMode ?? AgentExecutionMode.manual);

    // Initialize sort preference from stored state or default to lastMessageTimestamp
    this.sortConversationsBy = writable(this._state.sortConversationsBy ?? "lastMessageTimestamp");

    // Initialize displayed announcements from stored state or default to empty array
    this.displayedAnnouncements = writable(this._state.displayedAnnouncements ?? []);

    // Initialize send mode from stored state
    this._sendModeModel.initializeFromState(this._state.sendMode as any);

    // Send initialize message to extension
    this.onLoaded();
  }

  /**
   * Sets the ChatModeModel instance.
   * This should be called after both ChatModel and ChatModeModel are created.
   */
  setChatModeModel(chatModeModel: ChatModeModel): void {
    this._chatModeModel = chatModeModel;

    // Initialize EventTracker now that we have all dependencies
    const dependencies: EventTrackerDependencies = {
      extensionClient: this.extensionClient,
      chatModeType: chatModeModel.chatModeType,
      currentSendMode: this._sendModeModel.mode,
      agentExecutionMode: this.agentExecutionMode,
    };
    this._eventTracker = new EventTracker(dependencies);

    // Update the conversation model with the new event tracker
    this._currConversationModel.setEventTracker(this._eventTracker);
  }

  /**
   * Gets the flags loaded state.
   * This is used to prevent UI flashing while flags are loading.
   */
  get flagsLoaded(): Readable<boolean> {
    return this._flagsLoaded;
  }

  /**
   * Gets the EventTracker instance for analytics tracking.
   * Returns undefined if ChatModeModel hasn't been set yet.
   */
  get eventTracker(): EventTracker | undefined {
    return this._eventTracker;
  }

  get rulesModel(): RulesModel {
    return this._rulesModel;
  }

  /**
   * Initialize the chat model.
   * Notifies subscribers at the end of loading.
   */
  onLoaded = async (): Promise<void> => {
    const chatInitData = await this.extensionClient.getChatInitData();

    this._chatFlagsModel.update({
      enableEditableHistory: chatInitData.enableEditableHistory ?? false,
      enablePreferenceCollection: chatInitData.enablePreferenceCollection ?? false,
      enableRetrievalDataCollection: chatInitData.enableRetrievalDataCollection ?? false,
      enableDebugFeatures: chatInitData.enableDebugFeatures ?? false,
      enableRichTextHistory: chatInitData.useRichTextHistory ?? true,
      enableAgentSwarmMode: chatInitData.enableAgentSwarmMode ?? false,
      modelDisplayNameToId: chatInitData.modelDisplayNameToId ?? {},
      fullFeatured: chatInitData.fullFeatured ?? true,
      smallSyncThreshold: chatInitData.smallSyncThreshold ?? DEFAULT_SMALL_SYNC_THRESHOLD,
      bigSyncThreshold: chatInitData.bigSyncThreshold ?? DEFAULT_BIG_SYNC_THRESHOLD,
      enableExternalSourcesInChat: chatInitData.enableExternalSourcesInChat ?? false,
      enableSmartPaste: chatInitData.enableSmartPaste ?? false,
      enableDirectApply: chatInitData.enableDirectApply ?? false,
      summaryTitles: chatInitData.summaryTitles ?? false,
      suggestedEditsAvailable: chatInitData.suggestedEditsAvailable ?? false,
      enableShareService: chatInitData.enableShareService ?? false,
      maxTrackableFileCount: chatInitData.maxTrackableFileCount ?? DEFAULT_MAX_TRACKABLE_FILE_COUNT,
      enableDesignSystemRichTextEditor: chatInitData.enableDesignSystemRichTextEditor ?? false,
      enableSources: chatInitData.enableSources ?? false,
      enableChatMermaidDiagrams: chatInitData.enableChatMermaidDiagrams ?? false,
      smartPastePrecomputeMode:
        chatInitData.smartPastePrecomputeMode ?? SmartPastePrecomputeMode.visibleHover,
      useNewThreadsMenu: chatInitData.useNewThreadsMenu ?? false,
      enableChatMermaidDiagramsMinVersion:
        chatInitData.enableChatMermaidDiagramsMinVersion ?? false,

      idleNewSessionMessageTimeoutMs: chatInitData.idleNewSessionMessageTimeoutMs,
      idleNewSessionNotificationTimeoutMs: chatInitData.idleNewSessionNotificationTimeoutMs,
      enableChatMultimodal: chatInitData.enableChatMultimodal ?? false,
      enableAgentMode: chatInitData.enableAgentMode ?? false,
      agentMemoriesFilePathName: chatInitData.agentMemoriesFilePathName,
      enableRichCheckpointInfo: chatInitData.enableRichCheckpointInfo ?? false,
      userTier: chatInitData.userTier ?? "unknown",
      truncateChatHistory: chatInitData.truncateChatHistory ?? false,
      enableBackgroundAgents: chatInitData.enableBackgroundAgents ?? false,
      enableNewThreadsList: chatInitData.enableNewThreadsList ?? false,
      enableVirtualizedMessageList: chatInitData.enableVirtualizedMessageList ?? false,
      customPersonalityPrompts: chatInitData.customPersonalityPrompts ?? {},
      enablePersonalities: chatInitData.enablePersonalities ?? false,
      enableRules: chatInitData.enableRules ?? false,
      memoryClassificationOnFirstToken: chatInitData.memoryClassificationOnFirstToken ?? false,
      enableGenerateCommitMessage: chatInitData.enableGenerateCommitMessage ?? false,
      enablePromptEnhancer: chatInitData.enablePromptEnhancer ?? false,
      modelRegistry: chatInitData.modelRegistry ?? {},
      agentChatModel: chatInitData.agentChatModel ?? "",
      enableModelRegistry: chatInitData.enableModelRegistry ?? false,
      enableTaskList: chatInitData.enableTaskList ?? false,
      enableAgentAutoMode: chatInitData.enableAgentAutoMode ?? false,
      enableExchangeStorage: chatInitData.enableExchangeStorage ?? false,
      enableToolUseStateStorage: chatInitData.enableToolUseStateStorage ?? false,
      clientAnnouncement: chatInitData.clientAnnouncement ?? "",
      useHistorySummary: chatInitData.useHistorySummary ?? false,
      historySummaryParams: chatInitData.historySummaryParams ?? "",
      conversationHistorySizeThresholdBytes:
        chatInitData.conversationHistorySizeThresholdBytes ?? 0,
      retryChatStreamTimeouts: chatInitData.retryChatStreamTimeouts ?? false,
      enableCommitIndexing: chatInitData.enableCommitIndexing ?? false,
      enableMemoryRetrieval: chatInitData.enableMemoryRetrieval ?? false,
      isVscodeVersionOutdated: chatInitData.isVscodeVersionOutdated ?? false,
      vscodeMinVersion: chatInitData.vscodeMinVersion ?? "",
      enableAgentTabs: chatInitData.enableAgentTabs ?? false,
      enableAgentGitTracker: chatInitData.enableAgentGitTracker ?? false,
      remoteAgentsResumeHintAvailableTtlDays:
        chatInitData.remoteAgentsResumeHintAvailableTtlDays ?? 0,
      enableParallelTools: chatInitData.enableParallelTools ?? false,
      memoriesParams: chatInitData.memoriesParams ?? {},
      modelInfoRegistry: chatInitData.modelInfoRegistry ?? {},
    });

    // Make manual if feature flag is off
    if (!this._chatFlagsModel.enableAgentAutoMode) {
      this.agentExecutionMode.set(AgentExecutionMode.manual);
    }

    // Mark flags as loaded
    this._flagsLoaded.set(true);

    // Now that flags are loaded, complete the async initialization
    // This ensures the current conversation is properly hydrated during first load
    await this.initializeAsync(this.options.initialConversation);

    this.options.onLoaded?.();
    this.notifySubscribers();
  };

  /**
   * Subscribe to the state of the chat model.
   * Conforms to the svelte store pattern.
   *
   * @param subscriber A function that will be called whenever the state changes
   */
  subscribe = (sub: (chatModel: ChatModel) => void): (() => void) => {
    this.subscribers.add(sub);
    sub(this);
    return () => {
      this.subscribers.delete(sub);
    };
  };

  private initializeSync = (initialConversation?: IConversation) => {
    this._state = { ...this._state, ...this._host.getState() };

    // If we have an initial conversation, add it to the conversations state first
    if (initialConversation) {
      this._state.conversations = {
        ...this._state.conversations,
        [initialConversation.id]: initialConversation,
      };
    }

    // If we're in full-featured mode, and may enter the sign in conversation,
    // we want to override the sign-in conversation entry.
    // We do this by deleting the welcome conversation and replacing it with a new one.
    // This is to ensure that we do not persist any of the canned responses from Augment fake chat asking users to sign in
    if (this._chatFlagsModel.fullFeatured) {
      if (
        initialConversation?.id === signInConversationId ||
        this.currentConversationId === signInConversationId
      ) {
        delete this._state.conversations[signInConversationId];
        this.setCurrentConversationToWelcome();
      }
    }

    this._chatFlagsModel.subscribe((flags) => {
      this.idleMessageModel.idleNotifyTimeout = flags.idleNewSessionNotificationTimeoutMs;
      this.idleMessageModel.idleMessageTimeout = flags.idleNewSessionMessageTimeoutMs;
    });

    // Filter out invalid conversations, but keep the special key conversation
    this._state.conversations = Object.fromEntries(
      Object.entries(this._state.conversations).filter(
        ([id, conversation]) => id === NEW_AGENT_KEY || ConversationModel.isValid(conversation),
      ),
    );

    if (
      this.currentConversationId &&
      this.currentConversationId !== this.currentConversationModel.id
    ) {
      // Sync up the current conversation model with the chat model state
      const currentConversation = this.conversations[this.currentConversationId];
      if (currentConversation) this.currentConversationModel.setConversation(currentConversation);
    }

    // Initialize the shareable state for all legacy threads, since they existed
    // before the sharing feature was added.
    this.initializeIsShareableState();
    this.subscribe(() => this.idleMessageModel.activity());
    this.setState(this._state);
  };

  private async initializeAsync(initialConversation?: IConversation) {
    const targetConversationId = initialConversation?.id || this.currentConversationId;
    this._state.conversations = await this._persistenceController.hydrateCurrentConversation(
      this._state.conversations,
      targetConversationId,
    );

    // The current conversation model is the one that is currently being displayed.
    // It is the only one that can be interacted with. Once the user switches the conversation,
    // the current conversation model instance is the same, but it will be in charge of
    // a different conversation while the previous data is saved.
    if (initialConversation) {
      await this.setCurrentConversation(initialConversation.id);
    } else {
      await this.setCurrentConversation(this.currentConversationId);
    }
  }

  // Initialize the state that tracks whether a conversation is shareable. This
  // is for legacy threads that existed before the sharing feature was added.
  // Newer threads will have this state persisted correctly.
  private initializeIsShareableState = () => {
    const updatedConversations = { ...this._state.conversations };

    for (const [id, conversation] of Object.entries(updatedConversations)) {
      // A legacy conversation is shareable if it has at least one successful exchange
      if (conversation.isShareable) {
        continue;
      }
      const isShareable = conversation.chatHistory.some((item) =>
        isChatItemSuccessfulExchange(item),
      );

      updatedConversations[id] = {
        ...conversation,
        isShareable,
      };
    }

    this._state.conversations = updatedConversations;
  };

  private updateChatState = (state: Partial<StoredState>) => {
    this._state = { ...this._state, ...state };
    const convos = this._state.conversations;
    const pins = new Set<string>();
    for (const [id, convo] of Object.entries(convos)) {
      if (convo.isPinned) {
        pins.add(id);
      }
    }
    // Trigger a callback to eventually persist the state
    this.setState(this._state);
    this.notifySubscribers();
  };

  public saveImmediate = () => {
    this.setState(this._state);
    this.setState.flush();
  };

  /**
   * Update the state of the chat webview.
   * We throttle this so we do not hit the serialization of state too often.
   * Initialized in constructor with configurable debounce timing.
   *
   * @param state The state to update.
   */
  private setState: ReturnType<typeof debounce>;

  /**
   * Handles state setting with incremental async persistence of conversations. This
   * delegates entirely to the persistence controller. This is the only place where
   * persistence is handled.
   */
  private async _setStateWithPersistence(state: Partial<StoredState>): Promise<void> {
    const persistedConversations =
      await this._persistenceController.dehydrateConversationsIncremental(
        state.conversations ?? this._state.conversations,
        this.currentConversationId,
      );

    this._host.setState({
      ...state,
      conversations: persistedConversations,
    });
  }

  private notifySubscribers = () => {
    this.subscribers.forEach((sub) => sub(this));
  };

  /**
   * Wrap a function with a webview client metric
   *
   * @param eventName The name of the event to report
   * @param fn The function to wrap
   */
  withWebviewClientEvent = <TArgs extends any[], TResult>(
    eventName: ChatMetricName,
    fn: (...args: TArgs) => TResult,
  ): ((...args: TArgs) => TResult) => {
    return (...args: TArgs): TResult => {
      this.extensionClient.reportWebviewClientEvent(eventName);
      return fn(...args);
    };
  };

  private setCurrentConversationToWelcome = () => {
    // This function makes sure that we do not persist any of the
    // canned responses from Augment fake chat asking users to sign in.
    this.setCurrentConversation();
    this._currConversationModel.setName("Welcome to Augment");
    // We mark this chat item as seen so that we do not stream it again.
    // It will have already streamed in once when the user views it for the
    // first time in the sign in conversation that we are replacing.
    this._currConversationModel.addChatItem({
      chatItemType: ChatItemType.educateFeatures,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      request_id: crypto.randomUUID(),
      // eslint-disable-next-line @typescript-eslint/naming-convention
      seen_state: SeenState.seen,
    });
  };

  popCurrentConversation = async () => {
    const currentId = this.currentConversationId;
    if (!currentId) {
      return;
    }

    await this.deleteConversation(
      currentId,
      // Switch to the next conversation, if it exists. If not, switch to the previous.
      this.nextConversation?.id ?? this.previousConversation?.id,
    );
  };

  /**
   * Set the current conversation to the specified ID, or create a new one if none is provided
   *
   * @param conversationId The ID of the conversation to set as current, or undefined to create a new one
   * @param shouldUpdateLastInteractedAt Whether to update the lastInteractedAt timestamp
   */
  setCurrentConversation = async (
    conversationId?: string | undefined,
    shouldUpdateLastInteractedAt = true,
    options?: {
      newTaskUuid?: string;
      /**
       * Whether to no-op if the conversation ID is the same as the current conversation ID.
       * Calling setCurrentConversation with the current conversation ID while the conversation is still running
       * can cause recoverAllExchanges to be called multiple times, which can cause issues with interleaving responses.
       */
      noopIfSameConversation?: boolean;
    },
  ) => {
    if (conversationId === this.currentConversationId && options?.noopIfSameConversation) {
      return;
    }
    // If we don't find the conversation, we want to create a new one
    let newConversation: IConversation;

    // If no conversation ID is provided, create a new agent thread with the special key
    if (conversationId === undefined) {
      conversationId = NEW_AGENT_KEY;
    }

    // Get the existing conversation or create a new one
    const existingConversation = this._state.conversations[conversationId];
    newConversation = existingConversation
      ? await this._persistenceController.hydrateConversation(existingConversation)
      : ConversationModel.create({
          personaType: await this._currConversationModel.decidePersonaType(),
          rootTaskUuid: options?.newTaskUuid,
        });

    // If this is a new agent thread, set its ID to the special key
    if (conversationId === NEW_AGENT_KEY) {
      newConversation.id = NEW_AGENT_KEY;
    }

    if (options?.newTaskUuid) {
      newConversation.rootTaskUuid = options.newTaskUuid;
    }

    const isDeleted = this.conversations[this._currConversationModel.id] === undefined;
    this._currConversationModel.setConversation(
      newConversation,
      !isDeleted,
      shouldUpdateLastInteractedAt,
    );

    // Recover any exchanges that were not fully loaded by the webview, if available
    this._currConversationModel.recoverAllExchanges();

    // Reset the total characters cache and get new values when switching to a different conversation
    this._currConversationModel.resetTotalCharactersCache();
  };

  get flags(): ChatFlagsModel {
    return this._chatFlagsModel;
  }

  get specialContextInputModel(): SpecialContextInputModel {
    return this._specialContextInputModel;
  }

  get currentConversationId(): string | undefined {
    return this._state.currentConversationId;
  }

  get currentConversationModel(): ConversationModel {
    return this._currConversationModel;
  }

  get conversations(): { [conversationId: string]: IConversation } {
    return this._state.conversations;
  }

  get sendModeModel(): ISendModeModel {
    return this._sendModeModel;
  }

  get chatModeModel(): ChatModeModel | undefined {
    return this._chatModeModel;
  }

  /**
   * Sort conversations by specified parameters
   * @param by Field to sort by ("createdAt", "lastInteractedAt", or "lastMessageTimestamp")
   * @param direction Sort direction ("asc" or "desc")
   * @param filter Optional filter function to filter conversations
   * @returns Sorted array of conversations
   */
  orderedConversations(
    by?: "createdAt" | "lastInteractedAt" | "lastMessageTimestamp",
    direction: "asc" | "desc" = "desc",
    filter?: (conversation: IConversation) => boolean,
  ): IConversation[] {
    // Use the stored preference if no sort field is specified
    const sortBy = by || this._state.sortConversationsBy || "lastMessageTimestamp";
    let conversations = Object.values(this._state.conversations);

    // Apply filter if provided
    if (filter) {
      conversations = conversations.filter(filter);
    }

    // Sort by the specified field and direction
    return conversations.sort((a, b) => {
      const timeA = ConversationModel.getTime(a, sortBy).getTime();
      const timeB = ConversationModel.getTime(b, sortBy).getTime();
      return direction === "asc" ? timeA - timeB : timeB - timeA;
    });
  }

  get nextConversation(): IConversation | undefined {
    if (!this.currentConversationId) {
      return undefined;
    }

    const orderedConvos = this.orderedConversations();
    const currConvoIdx = orderedConvos.findIndex((conv) => conv.id === this.currentConversationId);

    return orderedConvos.length > currConvoIdx + 1 ? orderedConvos[currConvoIdx + 1] : undefined;
  }

  get previousConversation(): IConversation | undefined {
    if (!this.currentConversationId) {
      return undefined;
    }

    const orderedConvos = this.orderedConversations();
    const currConvoIdx = orderedConvos.findIndex((conv) => conv.id === this.currentConversationId);

    return currConvoIdx > 0 ? orderedConvos[currConvoIdx - 1] : undefined;
  }

  get host(): HostInterface {
    return this._host;
  }

  saveConversation = async (conversation: IConversation, doClearNewAgentThread?: boolean) => {
    // Mark this conversation as needing dehydration.
    // After a dehydration, we return what should actually be persisted to disk in its wake.
    this._persistenceController.markNeedsDehydration(conversation.id);
    this.updateChatState({
      ...this._state,
      currentConversationId: conversation.id,
      conversations: {
        ...this._state.conversations,
        [conversation.id]: conversation,
      },
    });

    if (doClearNewAgentThread) {
      delete this._state.conversations[NEW_AGENT_KEY];
    }
  };

  isConversationShareable = (conversationId: string) => {
    return this._state.conversations[conversationId]?.isShareable ?? true;
  };

  /**
   * Set the preference for sorting conversations
   * @param sortBy Field to sort by ("createdAt", "lastInteractedAt", or "lastMessageTimestamp")
   */
  setSortConversationsBy = (sortBy: "createdAt" | "lastInteractedAt" | "lastMessageTimestamp") => {
    this.sortConversationsBy.set(sortBy);
    this.updateChatState({});
  };

  getConversationUrl = async (conversationId: string): Promise<string> => {
    const conversation = this._state.conversations[conversationId];
    // If we already have a url, use it.
    if (conversation.lastUrl) {
      return conversation.lastUrl;
    }

    shareStatus.set("copying");

    const history = conversation?.chatHistory;
    const chat = history.reduce<Exchange[]>((acc, m) => {
      if (isChatItemSuccessfulExchange(m)) {
        acc.push({
          /* eslint-disable @typescript-eslint/naming-convention */
          request_id: m.request_id || "",
          request_message: m.request_message,
          response_text: m.response_text || "",
          /* eslint-enable @typescript-eslint/naming-convention */
        });
      }
      return acc;
    }, []);

    if (chat.length === 0) {
      throw new Error("No chat history to share");
    }

    const title = ConversationModel.getDisplayName(conversation);
    const msg = await this.extensionClient.saveChat(conversationId, chat, title);
    if (msg.data) {
      let result = msg.data.url;
      this.updateChatState({
        conversations: {
          ...this._state.conversations,
          [conversationId]: {
            ...conversation,
            lastUrl: result,
          },
        },
      });
      return result;
    } else {
      throw new Error("Failed to create URL");
    }
  };

  shareConversation = async (conversationId: string | undefined) => {
    if (conversationId === undefined) {
      return;
    }

    try {
      const url = await this.getConversationUrl(conversationId);
      if (!url) {
        shareStatus.set("idle");
        return;
      }

      navigator.clipboard.writeText(url);
      shareStatus.set("copied");
    } catch (err) {
      shareStatus.set("failed");
    }
  };

  deleteConversations = async (
    conversationIds: string[],
    nextConvoId: string | undefined = undefined,
    remoteAgentIds: string[] = [],
    remoteAgentsModel?: RemoteAgentsModel,
  ) => {
    const totalCount = conversationIds.length + remoteAgentIds.length;
    const ok = await this.extensionClient.openConfirmationModal({
      title: "Delete Conversation",
      message: `Are you sure you want to delete ${
        totalCount > 1 ? "these conversations" : "this conversation"
      }?`,
      confirmButtonText: "Delete",
      cancelButtonText: "Cancel",
    });

    if (ok) {
      // Delete regular conversations
      if (conversationIds.length > 0) {
        const idsToDelete: Set<string> = new Set(conversationIds);
        await this.deleteConversationIds(idsToDelete);
      }

      // Delete remote agents
      if (remoteAgentIds.length > 0 && remoteAgentsModel) {
        // Delete each remote agent individually
        // Note: We don't await these in parallel to avoid overwhelming the server
        for (const agentId of remoteAgentIds) {
          try {
            await remoteAgentsModel.deleteAgent(agentId, true);
          } catch (error) {
            console.error(`Failed to delete remote agent ${agentId}:`, error);
            // Continue with other deletions even if one fails
          }
        }
      }

      // If the current conversation is being deleted, select a new one conversation
      if (this.currentConversationId && conversationIds.includes(this.currentConversationId)) {
        this.setCurrentConversation(nextConvoId);
      }
    }
  };

  deleteConversation = async (
    conversationId: string,
    nextConvoId: string | undefined = undefined,
  ) => {
    await this.deleteConversations([conversationId], nextConvoId);
  };

  /**
   * Delete conversations from the chat state and clear their metadata
   *
   * @param conversationIds Set of conversation IDs to delete
   */
  private deleteConversationIds = async (conversationIds: Set<string>) => {
    // Retrieve all the request IDs for the conversations deleted
    const requestIdsToDelete: string[] = [];
    // Retrieve all the tool use IDs for the conversations deleted
    const toolUseIdsToDelete: string[] = [];

    for (const conversationId of conversationIds) {
      const requestIds = this._state.conversations[conversationId]?.requestIds ?? [];
      requestIdsToDelete.push(...requestIds);

      // Collect tool use IDs from the toolUseStates object
      const toolUseStates = this._state.conversations[conversationId]?.toolUseStates ?? {};
      for (const key of Object.keys(toolUseStates)) {
        const { toolUseId } = toolUseStates[key];
        if (toolUseId) {
          toolUseIdsToDelete.push(toolUseId);
        }
      }

      // Also collect tool use IDs from the structured output nodes in the chat history
      const conversation = this._state.conversations[conversationId];
      if (conversation) {
        for (const chatItem of conversation.chatHistory) {
          if (isChatItemExchangeWithStatus(chatItem) && chatItem.structured_output_nodes) {
            for (const node of chatItem.structured_output_nodes) {
              if (node.type === ChatResultNodeType.TOOL_USE && node.tool_use?.tool_use_id) {
                toolUseIdsToDelete.push(node.tool_use.tool_use_id);
              }
            }
          }
        }
      }
    }

    // Delete images from the conversation
    for (const conversation of Object.values(this._state.conversations)) {
      if (conversationIds.has(conversation.id)) {
        for (const chatItem of conversation.chatHistory) {
          if (isChatItemExchangeWithStatus(chatItem)) {
            this.deleteImagesInExchange(chatItem);
          }
        }
        const draft = conversation.draftExchange;
        if (draft) {
          this.deleteImagesInExchange(draft);
        }
      }
    }

    // Delete all exchanges for each conversation from the exchange manager
    for (const conversationId of conversationIds) {
      try {
        await this.extensionClient.deleteConversationExchanges(conversationId);
      } catch (error) {
        console.error(`Failed to delete exchanges for conversation ${conversationId}:`, error);
        // Continue with other deletions even if one fails
      }

      if (this.flags.enableToolUseStateStorage) {
        try {
          await this.extensionClient.deleteConversationToolUseStates(conversationId);
        } catch (error) {
          console.error(
            `Failed to delete tool use states for conversation ${conversationId}:`,
            error,
          );
          // Continue with other deletions even if one fails
        }
      }
    }

    // Clean up persistence tracking state for deleted conversations
    this._persistenceController.cleanupDeletedConversations(conversationIds);

    // First clear the actual chat state
    this.updateChatState({
      conversations: Object.fromEntries(
        Object.entries(this._state.conversations).filter(([id]) => !conversationIds.has(id)),
      ),
    });

    // Once it is deleted, delete the metadata
    this.extensionClient.clearMetadataFor({
      requestIds: requestIdsToDelete,
      conversationIds: Array.from(conversationIds),
      toolUseIds: toolUseIdsToDelete,
    });
  };

  /**
   * Delete invalid conversations of the specified conversation type.
   *
   * An invalid conversation is an empty conversation or does not have a name.
   * This method preserves the special NEW_AGENT_KEY conversation, even if it's invalid,
   * because it might be needed when the doUseNewDraftFunctionality flag is updated.
   *
   * @see ConversationModel.isValid
   * @param type The conversation type to be filtered: "all", "chat", or "agent"
   */
  deleteInvalidConversations(type: "all" | "chat" | "agent" = "all") {
    const emptyConversations = Object.keys(this.conversations).filter((id) => {
      // Always preserve the special key conversation
      if (id === NEW_AGENT_KEY) {
        return false;
      }

      const isInvalid = !ConversationModel.isValid(this.conversations[id]);
      const isAgent = isAgentConversation(this.conversations[id]);
      return (
        isInvalid &&
        ((type === "agent" && isAgent) || (type === "chat" && !isAgent) || type === "all")
      );
    });

    if (emptyConversations.length) {
      this.deleteConversationIds(new Set(emptyConversations));
    }
  }

  /**
   * Delete images from the asset manager in an exchange's rich text and structured request nodes
   *
   * @param exchange The exchange to delete images from
   */
  private deleteImagesInExchange = (exchange: ExchangeWithStatus) => {
    const allImages = new Set<string>([
      ...(exchange.rich_text_json_repr ? this.findImagesInJson(exchange.rich_text_json_repr) : []),
      ...(exchange.structured_request_nodes
        ? this.findImagesInStructuredRequest(exchange.structured_request_nodes)
        : []),
    ]);
    for (const image of allImages) {
      this.deleteImage(image);
    }
  };

  /**
   * Find the list of image IDs in a rich text JSON representation
   *
   * @param json The rich text JSON representation of the chat input
   * @returns The list of image IDs in the JSON
   */
  private findImagesInJson = (json: JSONContent) => {
    const images: string[] = [];

    const traverse = (node: JSONContent) => {
      if (node.type === "file" && node.attrs?.src) {
        // Only include actual images, not non-image attachments
        const fileName = node.attrs?.src;
        if (isImageFile(fileName)) {
          // The image src is the file name stored in the user's asset manager
          images.push(node.attrs.src);
        }
      } else if ((node.type === "doc" || node.type === "paragraph") && node.content) {
        for (const contentNode of node.content) {
          traverse(contentNode);
        }
      }
    };

    traverse(json);
    return images;
  };

  /**
   * Find the list of image IDs in a structured request node
   *
   * @param nodes The structured request nodes
   * @returns The list of image IDs in the nodes
   */
  private findImagesInStructuredRequest = (nodes: ChatRequestNode[]) => {
    const images = nodes.reduce((acc, node) => {
      if (node.type === ChatRequestNodeType.IMAGE_ID && node.image_id_node) {
        acc.push(node.image_id_node.image_id);
      }
      return acc;
    }, [] as string[]);
    return images;
  };

  toggleConversationPinned = (conversationId: string) => {
    const conversation = this._state.conversations[conversationId];
    const newState = {
      ...conversation,
      isPinned: !conversation.isPinned,
    };
    this.updateChatState({
      conversations: {
        ...this._state.conversations,
        [conversationId]: newState,
      },
    });
    // This shouldn't be necessary but there isn't currently a way for the
    // conversation model to be made aware of changes to the conversation in
    // the chat model state.
    if (conversationId === this.currentConversationId) {
      this._currConversationModel.toggleIsPinned();
    }
  };

  renameConversation = (conversationId: string, name: string) => {
    const conversation = this._state.conversations[conversationId];
    const newState = {
      ...conversation,
      name: name,
    };
    this.updateChatState({
      conversations: {
        ...this._state.conversations,
        [conversationId]: newState,
      },
    });
    // This shouldn't be necessary but there isn't currently a way for the
    // conversation model to be made aware of changes to the conversation in
    // the chat model state.
    if (conversationId === this.currentConversationId) {
      this._currConversationModel.setName(name);
    }
  };

  // Use the shared constant for new agent threads
  // eslint-disable-next-line @typescript-eslint/naming-convention
  static readonly NEW_AGENT_KEY = NEW_AGENT_KEY;

  smartPaste = (
    sourceRequestId: string,
    generatedCode: string,
    targetFile: string | null | undefined,
    options?: {
      dryRun?: boolean;
      requireFileConfirmation?: boolean;
    },
  ) => {
    // Get the chat history up until and including the request ID
    const chatHistory = this._currConversationModel.historyTo(
      sourceRequestId,
      true /* inclusive */,
    );

    // Filter + format chat history for consumption
    const filteredHistory: Exchange[] = chatHistory
      .filter((m): m is ExchangeWithStatus => isChatItemSuccessfulExchange(m))
      .map(
        (m: ExchangeWithStatus): Exchange => ({
          /* eslint-disable @typescript-eslint/naming-convention */
          request_message: m.request_message,
          response_text: m.response_text || "",
          request_id: m.request_id || "",
          /* eslint-enable @typescript-eslint/naming-convention */
        }),
      );

    this.extensionClient.smartPaste({
      generatedCode,
      chatHistory: filteredHistory,
      targetFile: targetFile ?? undefined,
      options,
    });
  };

  saveImage = async (file: File): Promise<string> => {
    return await this.extensionClient.saveImage(file);
  };

  saveAttachment = async (file: File): Promise<string> => {
    return await this.extensionClient.saveAttachment(file);
  };

  deleteImage = async (imageId: string): Promise<void> => {
    return await this.extensionClient.deleteImage(imageId);
  };

  renderImage = async (file: string) => {
    return await this.extensionClient.loadImage(file);
  };

  /**
   * Get the timestamp of the last message in the current conversation
   * @returns The ISO timestamp string of the last message, or undefined if there are no messages
   */
  get lastMessageTimestamp(): string | undefined {
    const lastExchange = this.currentConversationModel.lastExchange;
    return lastExchange?.timestamp;
  }

  handleMessageFromExtension(e: MessageEvent<WebViewMessage>): boolean {
    const msg = e.data;
    switch (msg.type) {
      case WebViewMessageType.newThread:
        if ("data" in msg && msg.data) {
          const mode = msg.data.mode;
          void (async () => {
            // Create a new conversation first
            await this.setCurrentConversation();

            if (mode && this._chatModeModel) {
              // Use the stored chat mode model reference
              if (mode.toLowerCase() === "agent") {
                await this._chatModeModel.handleSetToThreadType(
                  "localAgent",
                  AgentExecutionMode.manual,
                );
              } else if (mode.toLowerCase() === "chat") {
                await this._chatModeModel.handleSetToThreadType("chat");
              } else {
                console.warn("Unknown chat mode:", mode);
              }
            } else if (mode) {
              console.warn("ChatModeModel not available, cannot set mode:", mode);
            }
          })();
        } else {
          // Just create a new conversation with no special parameters
          this.setCurrentConversation();
        }
        return true;

      default:
        return false;
    }
  }
}

export interface StoredState {
  currentConversationId?: string;
  conversations: { [conversationId: string]: IConversation };
  agentExecutionMode?: AgentExecutionMode;
  isPanelCollapsed?: boolean; // Single collapse state for both panels
  shouldDisplayAgentBanner?: boolean; // State that controls the agent banner
  hasNotUsedAgent?: boolean; // State the controls the chaser effect on button
  sortConversationsBy?: "lastInteractedAt" | "createdAt" | "lastMessageTimestamp"; // Preference for sorting conversations
  displayedAnnouncements?: string[]; // List of announcements that have been displayed
  sendMode?: string; // Current send mode preference

  // Legacy fields - kept for backward compatibility
  isAgentEditsCollapsed?: boolean;
  isTaskListCollapsed?: boolean;
}

interface IChatModel {
  currentConversationModel: ConversationModel;
  conversations: { [conversationId: string]: IConversation };
  lastMessageTimestamp?: string;
}
