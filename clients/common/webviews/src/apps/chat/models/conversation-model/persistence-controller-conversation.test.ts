import { describe, test, expect, beforeEach, afterEach, vi } from "vitest";
import { ConversationPersistenceController } from "./persistence-controller-conversation";
import type { IConversation } from "../types";
import {
  ChatItemType,
  ExchangeStatus,
  SeenState,
  type ExchangeWithStatus,
  type ExchangePointer,
} from "../../types/chat-message";
import type { StoredExchange } from "@augment-internal/sidecar-libs/src/exchange-storage/storage-types";
import { PersonaType } from "@augment-internal/sidecar-libs/src/chat/chat-types";
import { ToolUsePhase, type ToolUseState } from "../../types/tool-use-state";

// Mock crypto.randomUUID
Object.defineProperty(global, "crypto", {
  value: {
    randomUUID: vi.fn(() => "mock-uuid-1234"),
  },
});

const MOCK_TIME = 1000000000; // Fixed timestamp for testing

describe("ConversationPersistenceController", () => {
  let controller: ConversationPersistenceController;
  let mockExtensionClient: any;
  let mockFlags: any;

  // Test data factories
  const createMockConversation = (overrides: Partial<IConversation> = {}): IConversation => ({
    /* eslint-disable @typescript-eslint/naming-convention */
    id: "conv-1",
    name: "Test Conversation",
    createdAtIso: "2023-01-01T00:00:00.000Z",
    lastInteractedAtIso: "2023-01-01T00:00:00.000Z",
    chatHistory: [],
    feedbackStates: {},
    toolUseStates: {},
    requestIds: [],
    personaType: PersonaType.DEFAULT,
    ...overrides,
    /* eslint-enable @typescript-eslint/naming-convention */
  });

  const createMockExchange = (overrides: Partial<ExchangeWithStatus> = {}): ExchangeWithStatus => ({
    /* eslint-disable @typescript-eslint/naming-convention */
    request_message: "Test request",
    response_text: "Test response",
    request_id: "req-1",
    timestamp: "2023-01-01T00:00:00Z",
    status: ExchangeStatus.success,
    seen_state: SeenState.seen,
    ...overrides,
    /* eslint-enable @typescript-eslint/naming-convention */
  });

  const createMockExchangePointer = (
    overrides: Partial<ExchangePointer> = {},
  ): ExchangePointer => ({
    /* eslint-disable @typescript-eslint/naming-convention */
    chatItemType: ChatItemType.exchangePointer,
    exchangeUuid: "req-1",
    timestamp: "2023-01-01T00:00:00Z",
    request_message: "Test request",
    status: ExchangeStatus.success,
    hasResponse: true,
    isStreaming: false,
    seen_state: SeenState.seen,
    ...overrides,
    /* eslint-enable @typescript-eslint/naming-convention */
  });

  const createMockStoredExchange = (overrides: Partial<StoredExchange> = {}): StoredExchange => ({
    /* eslint-disable @typescript-eslint/naming-convention */
    request_message: "Test request",
    response_text: "Test response",
    request_id: "req-1",
    uuid: "req-1",
    conversationId: "conv-1",
    status: "success",
    timestamp: "2023-01-01T00:00:00Z",
    seen_state: "seen",
    ...overrides,
    /* eslint-enable @typescript-eslint/naming-convention */
  });

  const createMockToolUseState = (overrides: Partial<ToolUseState> = {}): ToolUseState => ({
    /* eslint-disable @typescript-eslint/naming-convention */
    requestId: "req-1",
    toolUseId: "tool-1",
    phase: ToolUsePhase.completed,
    ...overrides,
    /* eslint-enable @typescript-eslint/naming-convention */
  });

  beforeEach(() => {
    vi.useFakeTimers();
    vi.setSystemTime(MOCK_TIME);

    // Create mock extension client
    mockExtensionClient = {
      saveExchanges: vi.fn().mockResolvedValue(undefined),
      loadExchanges: vi.fn().mockResolvedValue([]),
      saveToolUseStates: vi.fn().mockResolvedValue(undefined),
      loadConversationToolUseStates: vi.fn().mockResolvedValue({}),
    } as any;

    // Create mock flags
    mockFlags = {
      enableExchangeStorage: true,
      enableToolUseStateStorage: true,
    } as any;

    // Create controller
    controller = new ConversationPersistenceController(mockExtensionClient, mockFlags);
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  describe("constructor", () => {
    test("should initialize with extension client and flags", () => {
      expect(controller).toBeDefined();
    });

    test("should create dehydration cache with correct cooldown", () => {
      // The cache should be created with 5 minute cooldown
      expect(controller).toBeDefined();
    });
  });

  describe("hydrateConversation", () => {
    test("should attempt to hydrate data even when storage flags are disabled", async () => {
      mockFlags["enableExchangeStorage"] = false;
      mockFlags["enableToolUseStateStorage"] = false;

      const conversation = createMockConversation();
      const result = await controller.hydrateConversation(conversation);

      expect(result).toEqual(conversation);
      // Hydration should always attempt to load existing data, regardless of flags
      // loadExchanges is not called because there are no exchange pointers in the conversation
      expect(mockExtensionClient.loadExchanges).not.toHaveBeenCalled();
      // loadConversationToolUseStates is always called during hydration
      expect(mockExtensionClient.loadConversationToolUseStates).toHaveBeenCalledWith("conv-1");
    });

    test("should hydrate exchanges when enableExchangeStorage is true", async () => {
      const exchangePointer = createMockExchangePointer();
      const conversation = createMockConversation({
        chatHistory: [exchangePointer],
      });

      const storedExchange = createMockStoredExchange();
      mockExtensionClient["loadExchanges"].mockResolvedValue([storedExchange]);

      const result = await controller.hydrateConversation(conversation);

      expect(mockExtensionClient.loadExchanges).toHaveBeenCalledWith("conv-1", ["req-1"]);
      expect(result.chatHistory).toHaveLength(1);

      const hydratedExchange = result.chatHistory[0] as ExchangeWithStatus;
      expect(hydratedExchange.request_message).toBe("Test request");
      expect(hydratedExchange.response_text).toBe("Test response");
      expect(hydratedExchange.status).toBe(ExchangeStatus.success);
    });

    test("should hydrate tool use states when enableToolUseStateStorage is true", async () => {
      const conversation = createMockConversation();
      // eslint-disable-next-line @typescript-eslint/naming-convention
      const mockToolUseStates = { "tool-1": { state: "active" } };

      mockExtensionClient["loadConversationToolUseStates"].mockResolvedValue(mockToolUseStates);

      const result = await controller.hydrateConversation(conversation);

      expect(mockExtensionClient.loadConversationToolUseStates).toHaveBeenCalledWith("conv-1");
      expect(result.toolUseStates).toEqual(mockToolUseStates);
    });

    test("should handle hydration errors gracefully", async () => {
      const conversation = createMockConversation();

      mockExtensionClient["loadExchanges"].mockRejectedValue(new Error("Storage error"));
      mockExtensionClient["loadConversationToolUseStates"].mockRejectedValue(
        new Error("Storage error"),
      );

      const result = await controller.hydrateConversation(conversation);

      // Should return original conversation on error
      expect(result).toEqual(conversation);
    });

    test("should mark conversation as dirty after hydration", async () => {
      const conversation = createMockConversation();

      // Spy on the cache method
      const markDirtySpy = vi.spyOn(controller["_cache"], "markDirty");

      await controller.hydrateConversation(conversation);

      // Should call markDirty through the cache
      expect(markDirtySpy).toHaveBeenCalledWith("conv-1");
    });

    test("should not override existing tool use states during hydration", async () => {
      // Create conversation with existing tool use states
      const existingToolUseStates = {
        /* eslint-disable @typescript-eslint/naming-convention */
        "existing-tool-1": createMockToolUseState({ toolUseId: "existing-tool-1" }),
        "existing-tool-2": createMockToolUseState({ toolUseId: "existing-tool-2" }),
        /* eslint-enable @typescript-eslint/naming-convention */
      };
      const conversation = createMockConversation({ toolUseStates: existingToolUseStates });

      // Mock loaded tool use states from storage (some overlap, some new)
      const loadedToolUseStates = {
        /* eslint-disable @typescript-eslint/naming-convention */
        "existing-tool-2": createMockToolUseState({
          toolUseId: "existing-tool-2",
          phase: ToolUsePhase.error,
        }), // This should override the existing one
        "loaded-tool-3": createMockToolUseState({ toolUseId: "loaded-tool-3" }),
        /* eslint-enable @typescript-eslint/naming-convention */
      };

      mockExtensionClient["loadConversationToolUseStates"].mockResolvedValue(loadedToolUseStates);

      const result = await controller.hydrateConversation(conversation);

      expect(mockExtensionClient.loadConversationToolUseStates).toHaveBeenCalledWith("conv-1");

      // Should preserve existing tool use states and merge with loaded ones
      expect(result.toolUseStates).toEqual({
        /* eslint-disable @typescript-eslint/naming-convention */
        "existing-tool-1": existingToolUseStates["existing-tool-1"], // Preserved
        "existing-tool-2": loadedToolUseStates["existing-tool-2"], // Overridden by loaded state
        "loaded-tool-3": loadedToolUseStates["loaded-tool-3"], // Added from storage
        /* eslint-enable @typescript-eslint/naming-convention */
      });
    });
  });

  describe("dehydrateConversation", () => {
    test("should return conversation unchanged when storage flags are disabled", async () => {
      mockFlags["enableExchangeStorage"] = false;
      mockFlags["enableToolUseStateStorage"] = false;

      const conversation = createMockConversation();
      const result = await controller.dehydrateConversation(conversation);

      expect(result).toEqual(conversation);
      expect(mockExtensionClient.saveExchanges).not.toHaveBeenCalled();
      expect(mockExtensionClient.saveToolUseStates).not.toHaveBeenCalled();
    });

    test("should dehydrate exchanges when enableExchangeStorage is true", async () => {
      const exchange = createMockExchange();
      const conversation = createMockConversation({
        chatHistory: [exchange],
      });

      const result = await controller.dehydrateConversation(conversation);

      expect(mockExtensionClient.saveExchanges).toHaveBeenCalledWith(
        "conv-1",
        expect.arrayContaining([
          expect.objectContaining({
            /* eslint-disable @typescript-eslint/naming-convention */
            request_message: "Test request",
            response_text: "Test response",
            uuid: "req-1",
            conversationId: "conv-1",
            /* eslint-enable @typescript-eslint/naming-convention */
          }),
        ]),
      );

      expect(result.chatHistory).toHaveLength(1);
      const pointer = result.chatHistory[0] as ExchangePointer;
      expect(pointer.chatItemType).toBe(ChatItemType.exchangePointer);
      expect(pointer.exchangeUuid).toBe("req-1");
    });

    test("should dehydrate tool use states when enableToolUseStateStorage is true", async () => {
      // eslint-disable-next-line @typescript-eslint/naming-convention
      const toolUseStates = { "tool-1": createMockToolUseState() };
      const conversation = createMockConversation({ toolUseStates });

      const result = await controller.dehydrateConversation(conversation);

      expect(mockExtensionClient.saveToolUseStates).toHaveBeenCalledWith("conv-1", toolUseStates);
      expect(result.toolUseStates).toEqual({});
    });

    test("should handle dehydration errors gracefully", async () => {
      const exchange = createMockExchange();
      const conversation = createMockConversation({
        chatHistory: [exchange],
      });

      mockExtensionClient["saveExchanges"].mockRejectedValue(new Error("Storage error"));

      const result = await controller.dehydrateConversation(conversation);

      // Should return original conversation on error
      expect(result).toEqual(conversation);
    });

    test("should generate UUID for exchanges without request_id", async () => {
      // eslint-disable-next-line @typescript-eslint/naming-convention
      const exchange = createMockExchange({ request_id: undefined });
      const conversation = createMockConversation({
        chatHistory: [exchange],
      });

      // Mock crypto.randomUUID
      const mockUUID = "generated-uuid";
      const originalCrypto = global.crypto;
      Object.defineProperty(global, "crypto", {
        value: { randomUUID: vi.fn().mockReturnValue(mockUUID) },
        writable: true,
        configurable: true,
      });

      const result = await controller.dehydrateConversation(conversation);

      expect(mockExtensionClient.saveExchanges).toHaveBeenCalledWith(
        "conv-1",
        expect.arrayContaining([
          expect.objectContaining({
            /* eslint-disable @typescript-eslint/naming-convention */
            uuid: mockUUID,
            request_id: mockUUID,
            /* eslint-enable @typescript-eslint/naming-convention */
          }),
        ]),
      );

      const pointer = result.chatHistory[0] as ExchangePointer;
      expect(pointer.exchangeUuid).toBe(mockUUID);

      // Restore original crypto
      Object.defineProperty(global, "crypto", {
        value: originalCrypto,
        writable: true,
        configurable: true,
      });
    });
  });

  describe("dehydrateConversationsIncremental", () => {
    test("should return conversations unchanged when storage flags are disabled", async () => {
      mockFlags["enableExchangeStorage"] = false;
      mockFlags["enableToolUseStateStorage"] = false;

      const conversations = {
        /* eslint-disable @typescript-eslint/naming-convention */
        "conv-1": createMockConversation({ id: "conv-1" }),
        "conv-2": createMockConversation({ id: "conv-2" }),
        /* eslint-enable @typescript-eslint/naming-convention */
      };

      const result = await controller.dehydrateConversationsIncremental(conversations);

      expect(result).toEqual(conversations);
    });

    test("should process only conversations that need dehydration", async () => {
      const conv1 = createMockConversation({
        id: "conv-1",
        chatHistory: [createMockExchange()], // Has exchanges, needs dehydration
      });
      const conv2 = createMockConversation({
        id: "conv-2",
        chatHistory: [], // No exchanges, doesn't need dehydration
      });

      // eslint-disable-next-line @typescript-eslint/naming-convention
      const conversations = { "conv-1": conv1, "conv-2": conv2 };

      // Create spy before calling the method
      const markCleanSpy = vi.spyOn(controller["_cache"], "markClean");

      await controller.dehydrateConversationsIncremental(conversations);

      // Should only save exchanges for conv-1
      expect(mockExtensionClient.saveExchanges).toHaveBeenCalledTimes(1);
      expect(mockExtensionClient.saveExchanges).toHaveBeenCalledWith("conv-1", expect.any(Array));

      // conv-2 should be marked as clean (cached) since it has no exchanges
      expect(markCleanSpy).toHaveBeenCalledWith("conv-2");
    });

    test("should respect cooldown periods", async () => {
      const conversation = createMockConversation({
        chatHistory: [createMockExchange()],
      });
      // eslint-disable-next-line @typescript-eslint/naming-convention
      const conversations = { "conv-1": conversation };

      // Mock cache to return true for cooldown
      vi.spyOn(controller["_cache"], "isWithinCooldown").mockReturnValue(true);
      vi.spyOn(controller["_cache"], "isClean").mockReturnValue(false);

      const result = await controller.dehydrateConversationsIncremental(conversations);

      // Should not process conversation due to cooldown
      expect(mockExtensionClient.saveExchanges).not.toHaveBeenCalled();
      expect(result["conv-1"]).toEqual(conversation);
    });

    test("should limit processing to MAX_DEHYDRATIONS_PER_ITERATION", async () => {
      // Create 7 conversations that need dehydration (more than the limit of 5)
      const conversations: Record<string, IConversation> = {};
      for (let i = 1; i <= 7; i++) {
        /* eslint-disable @typescript-eslint/naming-convention */
        conversations[`conv-${i}`] = createMockConversation({
          id: `conv-${i}`,
          chatHistory: [createMockExchange({ request_id: `req-${i}` })],
        });
        /* eslint-enable @typescript-eslint/naming-convention */
      }

      await controller.dehydrateConversationsIncremental(conversations);

      // Should only process 5 conversations (MAX_DEHYDRATIONS_PER_ITERATION = 5)
      expect(mockExtensionClient.saveExchanges).toHaveBeenCalledTimes(5);
    });

    test("should handle dehydration errors gracefully", async () => {
      const conversation = createMockConversation({
        chatHistory: [createMockExchange()],
      });
      // eslint-disable-next-line @typescript-eslint/naming-convention
      const conversations = { "conv-1": conversation };

      mockExtensionClient["saveExchanges"].mockRejectedValue(new Error("Storage error"));

      const result = await controller.dehydrateConversationsIncremental(conversations);

      // Should return original conversation on error
      expect(result["conv-1"]).toEqual(conversation);
    });

    test("should mark successfully dehydrated conversations", async () => {
      const conversation = createMockConversation({
        chatHistory: [createMockExchange()],
      });
      // eslint-disable-next-line @typescript-eslint/naming-convention
      const conversations = { "conv-1": conversation };

      const markCleanSpy = vi.spyOn(controller["_cache"], "markClean");

      await controller.dehydrateConversationsIncremental(conversations);

      expect(markCleanSpy).toHaveBeenCalledWith("conv-1");
    });

    test("should always attempt to dehydrate current conversation even if within cooldown", async () => {
      mockFlags["enableExchangeStorage"] = true;
      const currentConversation = createMockConversation({
        id: "current-conv",
        chatHistory: [createMockExchange()],
      });
      const otherConversation = createMockConversation({
        id: "other-conv",
        chatHistory: [createMockExchange()],
      });
      const conversations: Record<string, IConversation> = {};
      conversations["current-conv"] = currentConversation;
      conversations["other-conv"] = otherConversation;

      // Mock current conversation as within cooldown, other as not
      vi.spyOn(controller["_cache"], "isWithinCooldown").mockImplementation(
        (id: string) => id === "current-conv",
      );
      vi.spyOn(controller["_cache"], "getLastProcessedTime").mockReturnValue(0);
      vi.spyOn(controller["_cache"], "isClean").mockReturnValue(false);

      await controller.dehydrateConversationsIncremental(conversations, "current-conv");

      // Current conversation should be processed despite cooldown
      expect(mockExtensionClient.saveExchanges).toHaveBeenCalledWith(
        "current-conv",
        expect.any(Array),
      );
      // Other conversation should also be processed since it's not in cooldown
      expect(mockExtensionClient.saveExchanges).toHaveBeenCalledWith(
        "other-conv",
        expect.any(Array),
      );
      expect(mockExtensionClient.saveExchanges).toHaveBeenCalledTimes(2);
    });

    test("should prioritize current conversation over others when hitting max limit", async () => {
      mockFlags["enableExchangeStorage"] = true;

      // Create 7 conversations that need dehydration (more than MAX_DEHYDRATIONS_PER_ITERATION = 5)
      const conversations: Record<string, IConversation> = {};
      for (let i = 1; i <= 7; i++) {
        conversations[`conv-${i}`] = createMockConversation({
          id: `conv-${i}`,
          chatHistory: [createMockExchange()],
        });
      }

      // All conversations are not within cooldown
      vi.spyOn(controller["_cache"], "isWithinCooldown").mockReturnValue(false);
      vi.spyOn(controller["_cache"], "getLastProcessedTime").mockReturnValue(0);
      vi.spyOn(controller["_cache"], "isClean").mockReturnValue(false);

      // Set conv-7 as current conversation
      await controller.dehydrateConversationsIncremental(conversations, "conv-7");

      // Should process exactly 5 conversations (the limit)
      expect(mockExtensionClient.saveExchanges).toHaveBeenCalledTimes(5);

      // Current conversation (conv-7) should always be included
      expect(mockExtensionClient.saveExchanges).toHaveBeenCalledWith("conv-7", expect.any(Array));

      // Four other conversations should be processed (conv-1 through conv-4 due to sorting by last processed time)
      expect(mockExtensionClient.saveExchanges).toHaveBeenCalledWith("conv-1", expect.any(Array));
      expect(mockExtensionClient.saveExchanges).toHaveBeenCalledWith("conv-2", expect.any(Array));
      expect(mockExtensionClient.saveExchanges).toHaveBeenCalledWith("conv-3", expect.any(Array));
      expect(mockExtensionClient.saveExchanges).toHaveBeenCalledWith("conv-4", expect.any(Array));
    });
  });

  describe("hydrateCurrentConversation", () => {
    test("should return conversations unchanged when target ID is not provided", async () => {
      const conversations = {
        /* eslint-disable @typescript-eslint/naming-convention */
        "conv-1": createMockConversation({ id: "conv-1" }),
        /* eslint-enable @typescript-eslint/naming-convention */
      };

      const result = await controller.hydrateCurrentConversation(conversations);

      expect(result).toEqual(conversations);
      expect(mockExtensionClient.loadExchanges).not.toHaveBeenCalled();
    });

    test("should return conversations unchanged when target conversation doesn't exist", async () => {
      const conversations = {
        /* eslint-disable @typescript-eslint/naming-convention */
        "conv-1": createMockConversation({ id: "conv-1" }),
        /* eslint-enable @typescript-eslint/naming-convention */
      };

      const result = await controller.hydrateCurrentConversation(conversations, "conv-2");

      expect(result).toEqual(conversations);
      expect(mockExtensionClient.loadExchanges).not.toHaveBeenCalled();
    });

    test("should hydrate target conversation successfully", async () => {
      const exchangePointer = createMockExchangePointer();
      const conversations = {
        /* eslint-disable @typescript-eslint/naming-convention */
        "conv-1": createMockConversation({
          id: "conv-1",
          chatHistory: [exchangePointer],
        }),
        "conv-2": createMockConversation({ id: "conv-2" }),
        /* eslint-enable @typescript-eslint/naming-convention */
      };

      const storedExchange = createMockStoredExchange();
      mockExtensionClient["loadExchanges"].mockResolvedValue([storedExchange]);

      const result = await controller.hydrateCurrentConversation(conversations, "conv-1");

      expect(mockExtensionClient.loadExchanges).toHaveBeenCalledWith("conv-1", ["req-1"]);
      expect(result["conv-1"].chatHistory).toHaveLength(1);
      expect(result["conv-2"]).toEqual(conversations["conv-2"]); // Unchanged
    });

    test("should handle hydration errors gracefully", async () => {
      const conversations = {
        /* eslint-disable @typescript-eslint/naming-convention */
        "conv-1": createMockConversation({ id: "conv-1" }),
        /* eslint-enable @typescript-eslint/naming-convention */
      };

      mockExtensionClient["loadExchanges"].mockRejectedValue(new Error("Storage error"));

      const result = await controller.hydrateCurrentConversation(conversations, "conv-1");

      expect(result).toEqual(conversations);
    });
  });

  describe("cleanupDeletedConversations", () => {
    test("should call cache cleanup with deleted conversation IDs", () => {
      const deletedIds = new Set(["conv-1", "conv-2"]);
      const cleanupSpy = vi.spyOn(controller["_cache"], "cleanup");

      controller.cleanupDeletedConversations(deletedIds);

      expect(cleanupSpy).toHaveBeenCalledWith(deletedIds);
    });
  });

  describe("exchange status conversion", () => {
    test("should correctly convert ExchangeStatus to StoredExchange status", async () => {
      const testCases = [
        { input: ExchangeStatus.success, expected: "success" },
        { input: ExchangeStatus.failed, expected: "failed" },
        { input: ExchangeStatus.sent, expected: "sent" },
      ];

      for (const testCase of testCases) {
        const exchange = createMockExchange({ status: testCase.input });
        const conversation = createMockConversation({
          chatHistory: [exchange],
        });

        await controller.dehydrateConversation(conversation);

        expect(mockExtensionClient.saveExchanges).toHaveBeenCalledWith(
          "conv-1",
          expect.arrayContaining([
            expect.objectContaining({
              status: testCase.expected,
            }),
          ]),
        );

        // Reset mock for next iteration
        mockExtensionClient["saveExchanges"].mockClear();
      }
    });

    test("should correctly convert StoredExchange status to ExchangeStatus", async () => {
      const testCases = [
        { input: "success", expected: ExchangeStatus.success },
        { input: "failed", expected: ExchangeStatus.failed },
        { input: "sent", expected: ExchangeStatus.sent },
      ];

      for (const testCase of testCases) {
        const exchangePointer = createMockExchangePointer();
        const conversation = createMockConversation({
          chatHistory: [exchangePointer],
        });

        const storedExchange = createMockStoredExchange({ status: testCase.input as any });
        mockExtensionClient["loadExchanges"].mockResolvedValue([storedExchange]);

        const result = await controller.hydrateConversation(conversation);

        const hydratedExchange = result.chatHistory[0] as ExchangeWithStatus;
        expect(hydratedExchange.status).toBe(testCase.expected);

        // Reset mock for next iteration
        mockExtensionClient["loadExchanges"].mockClear();
      }
    });
  });

  describe("seen state conversion", () => {
    test("should correctly convert SeenState to StoredExchange seen_state", async () => {
      const testCases = [
        { input: SeenState.seen, expected: "seen" },
        { input: SeenState.unseen, expected: "unseen" },
      ];

      for (const testCase of testCases) {
        // eslint-disable-next-line @typescript-eslint/naming-convention
        const exchange = createMockExchange({ seen_state: testCase.input });
        const conversation = createMockConversation({
          chatHistory: [exchange],
        });

        await controller.dehydrateConversation(conversation);

        expect(mockExtensionClient.saveExchanges).toHaveBeenCalledWith(
          "conv-1",
          expect.arrayContaining([
            expect.objectContaining({
              // eslint-disable-next-line @typescript-eslint/naming-convention
              seen_state: testCase.expected,
            }),
          ]),
        );

        // Reset mock for next iteration
        mockExtensionClient["saveExchanges"].mockClear();
      }
    });

    test("should correctly convert StoredExchange seen_state to SeenState", async () => {
      const testCases = [
        { input: "seen", expected: SeenState.seen },
        { input: "unseen", expected: SeenState.unseen },
      ];

      for (const testCase of testCases) {
        const exchangePointer = createMockExchangePointer();
        const conversation = createMockConversation({
          chatHistory: [exchangePointer],
        });

        // eslint-disable-next-line @typescript-eslint/naming-convention
        const storedExchange = createMockStoredExchange({ seen_state: testCase.input as any });
        mockExtensionClient["loadExchanges"].mockResolvedValue([storedExchange]);

        const result = await controller.hydrateConversation(conversation);

        const hydratedExchange = result.chatHistory[0] as ExchangeWithStatus;
        expect(hydratedExchange.seen_state).toBe(testCase.expected);

        // Reset mock for next iteration
        mockExtensionClient["loadExchanges"].mockClear();
      }
    });
  });
});
