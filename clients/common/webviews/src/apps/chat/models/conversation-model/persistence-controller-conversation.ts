import type { IConversation } from "../types";
import type { IExtensionClient } from "../../extension-client";
import type { ChatFlagsModel } from "../chat-flags-model";
import { isChatItemExchangeWithStatus, isChatItemExchangePointer } from "../../types/chat-message";
import type { StoredExchange } from "@augment-internal/sidecar-libs/src/exchange-storage/storage-types";
import {
  ChatItemType,
  type ExchangePointer,
  type ExchangeWithStatus,
  ExchangeStatus,
  SeenState,
  type ChatItem,
} from "../../types/chat-message";
import { PersistenceCache } from "./persistence-cache";
import { createRequestId } from "@augment-internal/sidecar-libs/src/utils/request-id";

/**
 * Controller responsible for managing conversation persistence operations.
 *
 * ## Purpose
 * Optimizes memory usage by moving large conversation data (exchanges, tool states)
 * between in-memory storage (fast access) and external KV storage (space efficient).
 *
 * ## Mental Model: "Smart Conversation Saver"
 * - Tracks when conversations were last saved
 * - Uses cache-based selection to avoid expensive content analysis
 * - Only saves conversations that have actually changed (selective persistence)
 * - Enforces cooldown periods to prevent excessive save operations
 *
 * ## Key Benefits
 * - Incremental processing (max 2 conversations per iteration)
 * - Cooldown prevention (5-minute minimum between persistence operations)
 * - Fast cache-based selection (avoids scanning conversation content)
 * - Simple cache system that tracks what is clean (doesn't need persistence work)
 */
export class ConversationPersistenceController {
  private readonly _maxItemsPerIteration = 5;

  constructor(
    private readonly _extensionClient: IExtensionClient,
    private readonly _flags: ChatFlagsModel,
    private readonly _cache: PersistenceCache = new PersistenceCache(5 * 60 * 1000),
  ) {}

  /**
   * Hydrate a conversation by loading exchanges and tool use states from storage.
   */
  async hydrateConversation(conversation: IConversation): Promise<IConversation> {
    let hydratedConversation = conversation;

    hydratedConversation = await this._hydrateExchanges(hydratedConversation);
    hydratedConversation = await this._hydrateToolUseStates(hydratedConversation);

    // Mark as dirty since it's now hydrated and may need persistence work
    this._cache.markDirty(conversation.id);
    return hydratedConversation;
  }

  /**
   * Dehydrate a conversation by storing exchanges and tool use states externally.
   */
  async dehydrateConversation(conversation: IConversation): Promise<IConversation> {
    let dehydratedConversation = conversation;

    // Dehydrate exchanges if enabled
    if (this._flags.enableExchangeStorage) {
      dehydratedConversation = await this._dehydrateExchanges(dehydratedConversation);
    }

    // Dehydrate tool use states if enabled
    if (this._flags.enableToolUseStateStorage) {
      dehydratedConversation = await this._dehydrateToolUseStates(dehydratedConversation);
    }

    return dehydratedConversation;
  }

  /**
   * Dehydrate conversations incrementally, processing only a maximum number per iteration.
   */
  async dehydrateConversationsIncremental(
    conversations: Record<string, IConversation>,
    currentConversationId?: string,
  ): Promise<Record<string, IConversation>> {
    if (!this._flags.enableExchangeStorage && !this._flags.enableToolUseStateStorage) {
      return conversations;
    }

    const conversationEntries = Object.entries(conversations);
    const conversationsToDehydrate = this._selectConversationsForDehydration(
      conversationEntries,
      currentConversationId,
    );
    const result: Record<string, IConversation> = {};

    // Process conversations incrementally
    for (const [id, conversation] of conversationEntries) {
      if (conversationsToDehydrate.includes(id)) {
        try {
          const dehydratedConversation = await this.dehydrateConversation(conversation);
          this._cache.markClean(id);

          // Special handling for current conversation: persist as dehydrated but return hydrated version
          if (id === currentConversationId) {
            result[id] = conversation; // Return original for current conversation
          } else {
            result[id] = dehydratedConversation; // Use dehydrated version
          }
        } catch (error) {
          console.warn(`Failed to dehydrate conversation ${id}:`, error);
          result[id] = conversation; // Fallback to original
        }
      } else {
        result[id] = conversation; // Use as-is if not selected for dehydration
      }
    }

    return result;
  }

  /**
   * Hydrate current conversation from a collection.
   */
  async hydrateCurrentConversation(
    conversations: Record<string, IConversation>,
    targetConversationId?: string,
  ): Promise<Record<string, IConversation>> {
    if (!targetConversationId || !conversations[targetConversationId]) {
      return conversations;
    }

    try {
      const hydratedConversation = await this.hydrateConversation(
        conversations[targetConversationId],
      );

      return {
        ...conversations,
        [targetConversationId]: hydratedConversation,
      };
    } catch (error) {
      console.warn(`Failed to hydrate conversation ${targetConversationId}:`, error);
      return conversations;
    }
  }

  markNeedsDehydration(conversationId: string): void {
    this._cache.markDirty(conversationId);
  }

  markDehydrated(conversationId: string): void {
    this._cache.markClean(conversationId);
  }

  cleanupDeletedConversations(conversationIds: Set<string>): void {
    this._cache.cleanup(conversationIds);
  }

  private _selectConversationsForDehydration(
    conversationEntries: [string, IConversation][],
    currentConversationId?: string,
  ): string[] {
    // Handle current conversation with priority
    const currentConversationCandidates: string[] = [];
    if (currentConversationId) {
      const currentConv = conversationEntries.find(([id]) => id === currentConversationId)?.[1];
      // Check cache instead of expensive _conversationNeedsDehydration call
      if (currentConv && !this._cache.isClean(currentConversationId)) {
        // Check if conversation actually has exchanges
        const hasExchanges = currentConv.chatHistory.some(isChatItemExchangeWithStatus);
        if (hasExchanges) {
          currentConversationCandidates.push(currentConversationId);
        } else {
          // Mark as clean if no exchanges (no persistence work needed)
          this._cache.markClean(currentConversationId);
        }
      }
    }

    // Filter other conversations that need dehydration
    const otherCandidates = conversationEntries
      .filter(([id, conversation]) => {
        if (id === currentConversationId) return false; // Skip current conversation

        // Check cache instead of expensive _conversationNeedsDehydration call
        if (this._cache.isClean(id)) {
          return false; // Already clean, no persistence work needed
        }

        // Check if conversation actually has exchanges
        const hasExchanges = conversation.chatHistory.some(isChatItemExchangeWithStatus);
        if (!hasExchanges) {
          // Mark as clean if no exchanges (no persistence work needed)
          this._cache.markClean(id);
          return false;
        }

        if (this._cache.isWithinCooldown(id)) return false; // Skip if recently dehydrated

        return true;
      })
      .map(([id]) => id);

    // Prioritize by oldest dehydration time
    const prioritizedOtherCandidates = otherCandidates.sort((a, b) => {
      const aLastDehydrated = this._cache.getLastProcessedTime(a);
      const bLastDehydrated = this._cache.getLastProcessedTime(b);
      return aLastDehydrated - bLastDehydrated;
    });

    // Combine current conversation (highest priority) with other candidates
    const allCandidates = [...currentConversationCandidates, ...prioritizedOtherCandidates];

    // Return up to MAX_ITEMS_PER_ITERATION conversations
    return allCandidates.slice(0, this._maxItemsPerIteration);
  }

  private async _dehydrateExchanges(conversation: IConversation): Promise<IConversation> {
    const exchangesToStore: StoredExchange[] = [];
    const persistedChatHistory: ChatItem[] = [];

    for (const item of conversation.chatHistory) {
      if (isChatItemExchangeWithStatus(item)) {
        // Cast to ExchangeWithStatus for proper type access
        const exchange = item as ExchangeWithStatus;
        // Use request_id as the unique identifier, generate one if missing
        const exchangeUuid = exchange.request_id || crypto?.randomUUID?.() || createRequestId();

        // Convert ExchangeWithStatus to StoredExchange for sidecar storage
        /* eslint-disable @typescript-eslint/naming-convention */
        const storedExchange: StoredExchange = {
          // Base Exchange fields
          request_message: exchange.request_message,
          response_text: exchange.response_text || "",
          request_id: exchangeUuid,
          request_nodes: exchange.structured_request_nodes,
          response_nodes: exchange.structured_output_nodes,
          // StoredExchange-specific fields
          uuid: exchangeUuid,
          conversationId: conversation.id,
          status:
            exchange.status === ExchangeStatus.success
              ? "success"
              : exchange.status === ExchangeStatus.failed
                ? "failed"
                : "sent",
          timestamp: exchange.timestamp || new Date().toISOString(),
          seen_state: exchange.seen_state === SeenState.seen ? "seen" : "unseen",
        };
        exchangesToStore.push(storedExchange);

        // Create ExchangePointer for lightweight storage
        const pointer: ExchangePointer = {
          chatItemType: ChatItemType.exchangePointer,
          exchangeUuid: exchangeUuid,
          timestamp: exchange.timestamp,
          request_message: exchange.request_message,
          status: exchange.status,
          hasResponse: Boolean(exchange.response_text),
          isStreaming: exchange.status === ExchangeStatus.sent, // Use status to determine streaming
          seen_state: exchange.seen_state,
        };
        /* eslint-enable @typescript-eslint/naming-convention */
        persistedChatHistory.push(pointer);
      } else {
        // Keep other chat items as-is
        persistedChatHistory.push(item);
      }
    }

    // Store exchanges in sidecar if any exist
    if (exchangesToStore.length > 0) {
      try {
        await this._extensionClient.saveExchanges(conversation.id, exchangesToStore);
      } catch (error) {
        // Fall back to keeping original conversation if storage fails
        return conversation;
      }
    }

    return {
      ...conversation,
      chatHistory: persistedChatHistory,
    };
  }

  private async _dehydrateToolUseStates(conversation: IConversation): Promise<IConversation> {
    // If feature flag is disabled, return as-is
    if (!this._flags.enableToolUseStateStorage) {
      return conversation;
    }

    // If no tool use states, return as-is
    if (!conversation.toolUseStates || Object.keys(conversation.toolUseStates).length === 0) {
      return conversation;
    }

    try {
      // Store tool use states in sidecar
      await this._extensionClient.saveToolUseStates(conversation.id, conversation.toolUseStates);

      // Return conversation with empty toolUseStates
      return {
        ...conversation,
        toolUseStates: {},
      };
    } catch (error) {
      // Fall back to keeping original conversation if storage fails
      console.warn(`Failed to store tool use states for conversation ${conversation.id}:`, error);
      return conversation;
    }
  }

  // Note: even if feature flag is disabled, we want to load exchanges if they exist
  private async _hydrateExchanges(conversation: IConversation): Promise<IConversation> {
    // Find all ExchangePointer items that need restoration
    const exchangePointers = conversation.chatHistory.filter(isChatItemExchangePointer);
    if (exchangePointers.length === 0) {
      return conversation; // No pointers to restore
    }

    try {
      // Collect all exchange UUIDs that need to be loaded
      const exchangeUuids = exchangePointers.map((pointer) => pointer.exchangeUuid);

      // Load exchanges from sidecar storage
      const storedExchanges = await this._extensionClient.loadExchanges(
        conversation.id,
        exchangeUuids,
      );
      const exchangeMap = new Map(storedExchanges.map((ex) => [ex.uuid, ex]));

      // Convert chat history by replacing pointers with full exchanges
      const restoredChatHistory: ChatItem[] = conversation.chatHistory.map((item) => {
        if (isChatItemExchangePointer(item)) {
          const storedExchange = exchangeMap.get(item.exchangeUuid);
          if (storedExchange) {
            // Convert StoredExchange back to ExchangeWithStatus
            /* eslint-disable @typescript-eslint/naming-convention */
            const exchange: ExchangeWithStatus = {
              // No chatItemType for ExchangeWithStatus (undefined)
              request_message: storedExchange.request_message,
              response_text: storedExchange.response_text,
              request_id: storedExchange.request_id,
              structured_request_nodes: storedExchange.request_nodes,
              structured_output_nodes: storedExchange.response_nodes,
              timestamp: storedExchange.timestamp,
              status:
                storedExchange.status === "success"
                  ? ExchangeStatus.success
                  : storedExchange.status === "failed"
                    ? ExchangeStatus.failed
                    : ExchangeStatus.sent,
              seen_state: storedExchange.seen_state === "seen" ? SeenState.seen : SeenState.unseen,
            };
            /* eslint-enable @typescript-eslint/naming-convention */
            return exchange;
          }
        }
        return item; // Keep other items as-is
      });

      return {
        ...conversation,
        chatHistory: restoredChatHistory,
      };
    } catch (error) {
      // Fall back to keeping original conversation if restoration fails
      console.warn(`Failed to restore exchanges for conversation ${conversation.id}:`, error);
      return conversation;
    }
  }

  private async _hydrateToolUseStates(conversation: IConversation): Promise<IConversation> {
    try {
      // Load tool use states from sidecar storage
      const toolUseStates = await this._extensionClient.loadConversationToolUseStates(
        conversation.id,
      );

      // If there are existing tool use states, do not overwrite them
      return {
        ...conversation,
        toolUseStates: { ...conversation.toolUseStates, ...toolUseStates },
      };
    } catch (error) {
      // Fall back to keeping original conversation if restoration fails
      console.warn(`Failed to restore tool use states for conversation ${conversation.id}:`, error);
      return conversation;
    }
  }
}

// Backward compatibility alias
export const conversationHydrationController = ConversationPersistenceController;
