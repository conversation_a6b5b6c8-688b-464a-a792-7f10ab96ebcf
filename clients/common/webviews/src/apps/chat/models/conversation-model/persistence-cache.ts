/**
 * Simple cache for tracking what is known to be clean for dehydration.
 *
 * This cache maintains a list of items that are known to be clean, meaning they
 * don't need the expensive persistence work during dehydration. These items can
 * be dehydrated in-place without actually doing the persistence/storage work.
 *
 * Just a wrapper around a Set that remembers:
 * - Which items are clean and don't need persistence work during dehydration
 * - When items were last processed (for cooldown logic)
 */
export class PersistenceCache {
  private _cleanItems: Set<string> = new Set();
  private _lastProcessedTime: Map<string, number> = new Map();

  constructor(private readonly cooldownMs: number = 5 * 60 * 1000) {}

  markClean(key: string): void {
    this._cleanItems.add(key);
    this._lastProcessedTime.set(key, Date.now());
  }

  markDirty(key: string): void {
    this._cleanItems.delete(key);
    this._lastProcessedTime.delete(key);
  }

  isClean(key: string): boolean {
    return this._cleanItems.has(key);
  }

  isWithinCooldown(key: string): boolean {
    const lastProcessed = this._lastProcessedTime.get(key);
    if (!lastProcessed) return false;
    return Date.now() - lastProcessed < this.cooldownMs;
  }

  getLastProcessedTime(key: string): number {
    return this._lastProcessedTime.get(key) || 0;
  }

  cleanup(deletedKeys: Set<string> | string[]): void {
    const keys = Array.isArray(deletedKeys) ? deletedKeys : Array.from(deletedKeys);
    for (const key of keys) {
      this.markDirty(key);
    }
  }

  clear(): void {
    this._cleanItems.clear();
    this._lastProcessedTime.clear();
  }

  getStats() {
    return {
      cleanCount: this._cleanItems.size,
      trackedCount: this._lastProcessedTime.size,
    };
  }
}
