import { describe, test, expect, beforeEach, vi } from "vitest";
import { PersistenceCache } from "./persistence-cache";

// Mock Date.now for consistent testing
vi.useFakeTimers();

describe("PersistenceCache", () => {
  let cache: PersistenceCache;
  const COOLDOWN_MS = 5 * 60 * 1000; // 5 minutes
  const MOCK_TIME = 1000000000; // Fixed timestamp for testing

  beforeEach(() => {
    vi.setSystemTime(MOCK_TIME);
    cache = new PersistenceCache(COOLDOWN_MS);
  });

  describe("constructor", () => {
    test("should initialize with default cooldown when no parameter provided", () => {
      const defaultCache = new PersistenceCache();
      expect(defaultCache).toBeDefined();
    });

    test("should initialize with custom cooldown", () => {
      const customCooldown = 10 * 60 * 1000; // 10 minutes
      const customCache = new PersistenceCache(customCooldown);
      expect(customCache).toBeDefined();
    });
  });

  describe("markClean", () => {
    test("should add conversation to clean set", () => {
      const conversationId = "conv-1";

      cache.markClean(conversationId);

      expect(cache.isClean(conversationId)).toBe(true);
    });

    test("should set timestamp for conversation", () => {
      const conversationId = "conv-1";

      cache.markClean(conversationId);

      expect(cache.getLastProcessedTime(conversationId)).toBe(MOCK_TIME);
    });

    test("should update timestamp when called multiple times", () => {
      const conversationId = "conv-1";

      cache.markClean(conversationId);
      const firstTime = cache.getLastProcessedTime(conversationId);

      // Advance time
      vi.advanceTimersByTime(1000);
      cache.markClean(conversationId);
      const secondTime = cache.getLastProcessedTime(conversationId);

      expect(secondTime).toBeGreaterThan(firstTime);
    });
  });

  describe("markDirty", () => {
    test("should remove conversation from clean set", () => {
      const conversationId = "conv-1";

      // First mark as clean
      cache.markClean(conversationId);
      expect(cache.isClean(conversationId)).toBe(true);

      // Then mark as dirty
      cache.markDirty(conversationId);
      expect(cache.isClean(conversationId)).toBe(false);
    });

    test("should remove timestamp for conversation", () => {
      const conversationId = "conv-1";

      // First mark as clean
      cache.markClean(conversationId);
      expect(cache.getLastProcessedTime(conversationId)).toBe(MOCK_TIME);

      // Then mark as dirty
      cache.markDirty(conversationId);
      expect(cache.getLastProcessedTime(conversationId)).toBe(0);
    });

    test("should handle removing non-existent conversation gracefully", () => {
      const conversationId = "non-existent";

      expect(() => cache.markDirty(conversationId)).not.toThrow();
      expect(cache.isClean(conversationId)).toBe(false);
    });
  });

  describe("isWithinCooldown", () => {
    test("should return false for conversation never processed", () => {
      const conversationId = "conv-1";

      expect(cache.isWithinCooldown(conversationId)).toBe(false);
    });

    test("should return true for recently processed conversation", () => {
      const conversationId = "conv-1";

      cache.markClean(conversationId);

      // Advance time by less than cooldown
      vi.advanceTimersByTime(COOLDOWN_MS - 1000);

      expect(cache.isWithinCooldown(conversationId)).toBe(true);
    });

    test("should return false for conversation processed outside cooldown", () => {
      const conversationId = "conv-1";

      cache.markClean(conversationId);

      // Advance time by more than cooldown
      vi.advanceTimersByTime(COOLDOWN_MS + 1000);

      expect(cache.isWithinCooldown(conversationId)).toBe(false);
    });

    test("should return false exactly at cooldown boundary", () => {
      const conversationId = "conv-1";

      cache.markClean(conversationId);

      // Advance time by exactly cooldown period
      vi.advanceTimersByTime(COOLDOWN_MS);

      expect(cache.isWithinCooldown(conversationId)).toBe(false);
    });
  });

  describe("isClean", () => {
    test("should return false for conversation not in cache", () => {
      const conversationId = "conv-1";

      expect(cache.isClean(conversationId)).toBe(false);
    });

    test("should return true for conversation in cache", () => {
      const conversationId = "conv-1";

      cache.markClean(conversationId);

      expect(cache.isClean(conversationId)).toBe(true);
    });
  });

  describe("getLastProcessedTime", () => {
    test("should return 0 for conversation never processed", () => {
      const conversationId = "conv-1";

      expect(cache.getLastProcessedTime(conversationId)).toBe(0);
    });

    test("should return correct timestamp for processed conversation", () => {
      const conversationId = "conv-1";

      cache.markClean(conversationId);

      expect(cache.getLastProcessedTime(conversationId)).toBe(MOCK_TIME);
    });
  });

  describe("cleanup", () => {
    test("should remove multiple conversations from cache", () => {
      const conv1 = "conv-1";
      const conv2 = "conv-2";
      const conv3 = "conv-3";

      // Mark all as clean
      cache.markClean(conv1);
      cache.markClean(conv2);
      cache.markClean(conv3);

      // Cleanup conv1 and conv2
      const deletedIds = new Set([conv1, conv2]);
      cache.cleanup(deletedIds);

      // Check results
      expect(cache.isClean(conv1)).toBe(false);
      expect(cache.isClean(conv2)).toBe(false);
      expect(cache.isClean(conv3)).toBe(true); // Should remain

      expect(cache.getLastProcessedTime(conv1)).toBe(0);
      expect(cache.getLastProcessedTime(conv2)).toBe(0);
      expect(cache.getLastProcessedTime(conv3)).toBe(MOCK_TIME); // Should remain
    });

    test("should handle empty deletion set gracefully", () => {
      const conversationId = "conv-1";

      cache.markClean(conversationId);

      cache.cleanup(new Set());

      // Should remain unchanged
      expect(cache.isClean(conversationId)).toBe(true);
      expect(cache.getLastProcessedTime(conversationId)).toBe(MOCK_TIME);
    });

    test("should handle non-existent conversations in deletion set", () => {
      const existingId = "conv-1";
      const nonExistentId = "conv-2";

      cache.markClean(existingId);

      const deletedIds = new Set([existingId, nonExistentId]);

      expect(() => cache.cleanup(deletedIds)).not.toThrow();
      expect(cache.isClean(existingId)).toBe(false);
    });
  });

  describe("getStats", () => {
    test("should return correct stats for empty cache", () => {
      const stats = cache.getStats();

      expect(stats.cleanCount).toBe(0);
      expect(stats.trackedCount).toBe(0);
    });

    test("should return correct stats with conversations", () => {
      cache.markClean("conv-1");
      cache.markClean("conv-2");

      const stats = cache.getStats();

      expect(stats.cleanCount).toBe(2);
      expect(stats.trackedCount).toBe(2);
    });

    test("should update stats after cleanup", () => {
      cache.markClean("conv-1");
      cache.markClean("conv-2");

      cache.cleanup(new Set(["conv-1"]));

      const stats = cache.getStats();

      expect(stats.cleanCount).toBe(1);
      expect(stats.trackedCount).toBe(1);
    });
  });

  describe("clear", () => {
    test("should remove all cache entries", () => {
      cache.markClean("conv-1");
      cache.markClean("conv-2");
      cache.markClean("conv-3");

      cache.clear();

      const stats = cache.getStats();
      expect(stats.cleanCount).toBe(0);
      expect(stats.trackedCount).toBe(0);

      expect(cache.isClean("conv-1")).toBe(false);
      expect(cache.isClean("conv-2")).toBe(false);
      expect(cache.isClean("conv-3")).toBe(false);

      expect(cache.getLastProcessedTime("conv-1")).toBe(0);
      expect(cache.getLastProcessedTime("conv-2")).toBe(0);
      expect(cache.getLastProcessedTime("conv-3")).toBe(0);
    });

    test("should handle clearing empty cache gracefully", () => {
      expect(() => cache.clear()).not.toThrow();

      const stats = cache.getStats();
      expect(stats.cleanCount).toBe(0);
      expect(stats.trackedCount).toBe(0);
    });
  });

  describe("edge cases and integration", () => {
    test("should handle rapid state changes correctly", () => {
      const conversationId = "conv-1";

      // Mark as clean
      cache.markClean(conversationId);
      expect(cache.isClean(conversationId)).toBe(true);

      // Mark as dirty
      cache.markDirty(conversationId);
      expect(cache.isClean(conversationId)).toBe(false);

      // Mark as clean again
      vi.advanceTimersByTime(1000);
      cache.markClean(conversationId);
      expect(cache.isClean(conversationId)).toBe(true);
      expect(cache.getLastProcessedTime(conversationId)).toBe(MOCK_TIME + 1000);
    });

    test("should maintain consistency between clean set and timestamp map", () => {
      const conversationId = "conv-1";

      // Initially both should be empty
      expect(cache.isClean(conversationId)).toBe(false);
      expect(cache.getLastProcessedTime(conversationId)).toBe(0);

      // After marking clean, both should be populated
      cache.markClean(conversationId);
      expect(cache.isClean(conversationId)).toBe(true);
      expect(cache.getLastProcessedTime(conversationId)).toBeGreaterThan(0);

      // After marking dirty, both should be cleared
      cache.markDirty(conversationId);
      expect(cache.isClean(conversationId)).toBe(false);
      expect(cache.getLastProcessedTime(conversationId)).toBe(0);
    });
  });
});
