import { writable } from "svelte/store";
import { Scheduler } from "../utils/scheduler";

/**
 * The state of the idle status.
 * busy - not idle.
 * idle-notify - idle, show the big button.
 * idle-message - idle, show the message.
 */
type IdleState = "busy" | "idle-notify" | "idle-message";

export const DEFAULT_IDLE_MESSAGE_TIMEOUT_MS = 1000 * 60 * 5; // 5 minutes
export const DEFAULT_IDLE_NOTIFICATION_TIMEOUT_MS = 1000 * 60 * 60; // 1 hour
export class IdleMessageModel {
  private unsubNotify: undefined | (() => void);
  private unsubMessage: undefined | (() => void);

  constructor(
    private _idleNotifyTimeout: number = 0,
    private _idleMessageTimeout: number = 0,
    private idleScheduler = new Scheduler(),
    public idleStatus = writable<IdleState>("busy"),
    public focusAfterIdle = writable(false),
  ) {
    this.idleNotifyTimeout = _idleNotifyTimeout;
    this.idleMessageTimeout = _idleMessageTimeout;
  }

  set idleMessageTimeout(timeout: number) {
    if (this._idleMessageTimeout !== timeout) {
      this._idleMessageTimeout = timeout;
      this.unsubMessage?.();
      this.unsubMessage = this.idleScheduler.once(timeout, () => {
        this.idleStatus.set("idle-message");
      });
    }
  }

  set idleNotifyTimeout(timeout: number) {
    if (this._idleNotifyTimeout !== timeout) {
      this._idleNotifyTimeout = timeout;
      this.unsubNotify?.();
      this.unsubNotify = this.idleScheduler.once(timeout, () => {
        this.idleStatus.set("idle-notify");
      });
    }
  }

  get idleMessageTimeout() {
    return this._idleMessageTimeout;
  }

  get idleNotifyTimeout() {
    return this._idleNotifyTimeout;
  }

  get notifyEnabled() {
    return this._idleNotifyTimeout > 0;
  }

  get messageEnabled() {
    return this._idleMessageTimeout > 0;
  }

  dispose() {
    this.unsubNotify?.();
    this.unsubMessage?.();
    this.idleScheduler.dispose();
    this.idleStatus.set("busy");
    this.focusAfterIdle.set(false);
  }

  activity = () => {
    this.idleStatus.set("busy");
    this.idleScheduler.reschedule();
  };

  focus = (isInFocus: boolean) => {
    this.focusAfterIdle.set(isInFocus);
  };
}
