import type { IModel } from "./model-registry";
import type { IChatFlags } from "./types";

/**
 * Formats model registry entries from chat model flags with backward compatibility support.
 *
 * This function handles the migration from the legacy modelRegistry format to the new
 * modelInfoRegistry format. The new format provides richer model metadata including
 * displayName, shortName, description, and disabled status, while the legacy format
 * only contained simple id->name mappings.
 *
 * New format (modelInfoRegistry): Record<string, ModelInfoRegistryEntry>
 *   - Contains full model metadata: displayName, shortName, description, disabled, etc.
 *   - Example: { "claude-sonnet-4": { displayName: "Claude Sonnet 4", shortName: "Sonnet 4", ... } }
 *
 * Legacy format (modelRegistry): Record<string, string>
 *   - Simple id->displayName mapping
 *   - Example: { "claude-sonnet-4": "Claude Sonnet 4" }
 *
 * The function prioritizes the new format when available, falling back to legacy format
 * for backward compatibility during the transition period.
 *
 * @param flags - The chat model flags containing model registry information
 * @returns Array of formatted IModel entries ready for use in the ModelRegistry
 */
export function formatModelRegistryEntries(flags: IChatFlags): IModel[] {
  const modelInfoRegistryFlags = flags.modelInfoRegistry || {};

  if (Object.keys(modelInfoRegistryFlags).length > 0) {
    // Use new modelInfoRegistry format - spread the full ModelInfoRegistryEntry object
    return Object.entries(modelInfoRegistryFlags).map(([id, model]) => ({
      id,
      ...model, // Includes displayName, shortName, description, disabled, etc.
    }));
  } else {
    // Fallback to legacy modelRegistry format - only has displayName
    const legacyModelRegistryFlags = flags.modelRegistry || {};
    return Object.entries(legacyModelRegistryFlags).map(([name, id]) => ({
      id,
      displayName: name, // Legacy format only provides the display name
    }));
  }
}
