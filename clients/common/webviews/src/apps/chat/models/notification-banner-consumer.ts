import { WebViewMessageType } from "$vscode/src/webview-providers/webview-messages";
import type { WebViewMessage } from "$vscode/src/webview-providers/webview-messages";
import type { MessageConsumer } from "$common-webviews/src/common/utils/message-broker";
import { notificationBannerStore } from "./notification-banner-store";

/**
 * Message consumer for handling banner notification messages from the extension
 */
export class NotificationBannerConsumer implements MessageConsumer {
  handleMessageFromExtension(e: MessageEvent<WebViewMessage>): boolean {
    const message = e.data;
    switch (message.type) {
      case WebViewMessageType.showBannerNotification: {
        const { notificationId, message: notificationMessage, level, actionItems } = message.data;
        notificationBannerStore.addNotification({
          notificationId,
          message: notificationMessage,
          level,
          actionItems,
        });

        return true;
      }

      case WebViewMessageType.dismissBannerNotification: {
        const { notificationId } = message.data;
        notificationBannerStore.removeNotification(notificationId);
        return true;
      }

      default:
        return false;
    }
  }
}
