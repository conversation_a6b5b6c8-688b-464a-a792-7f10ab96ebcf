import { writable, type Writable } from "svelte/store";
import type { ShowBannerNotificationData } from "$vscode/src/webview-providers/webview-messages";

/**
 * Store for managing banner notifications in the chat interface
 */
export class NotificationBannerStore {
  private _notifications: Writable<ShowBannerNotificationData[]> = writable([]);

  get notifications() {
    return this._notifications;
  }

  /**
   * Add a new banner notification
   */
  addNotification(notification: ShowBannerNotificationData) {
    this._notifications.update((notifications) => {
      // Remove any existing notification with the same ID
      const filtered = notifications.filter(
        (n) => n.notificationId !== notification.notificationId,
      );
      // Add the new notification
      const updated = [...filtered, notification];
      return updated;
    });
  }

  /**
   * Remove a banner notification by ID
   */
  removeNotification(notificationId: string) {
    this._notifications.update((notifications) =>
      notifications.filter((n) => n.notificationId !== notificationId),
    );
  }
}

export const notificationBannerStore = new NotificationBannerStore();
