import Handlebars from "handlebars";

// Handlebars template for agent round XML generation
export const AGENT_ROUND_TEMPLATE = `
<user>
{{{userMessage}}}
</user>
{{#if hasActions}}
<agent_actions>
{{#if filesModified}}
\t<files_modified>
{{#each filesModified}}
\t\t{{{this}}}
{{/each}}
\t</files_modified>
{{/if}}
{{#if filesCreated}}
\t<files_created>
{{#each filesCreated}}
\t\t{{{this}}}
{{/each}}
\t</files_created>
{{/if}}
{{#if filesDeleted}}
\t<files_deleted>
{{#each filesDeleted}}
\t\t{{{this}}}
{{/each}}
\t</files_deleted>
{{/if}}
{{#if filesViewed}}
\t<files_viewed>
{{#each filesViewed}}
\t\t{{{this}}}
{{/each}}
\t</files_viewed>
{{/if}}
{{#if terminalCommands}}
\t<terminal_commands>
{{#each terminalCommands}}
\t\t{{{this}}}
{{/each}}
\t</terminal_commands>
{{/if}}
</agent_actions>
{{/if}}
{{#if agentResponse}}
<agent_response>
{{{agentResponse}}}
</agent_response>
{{else if wasInterrupted}}
<agent_was_interrupted/>
{{else if continues}}
<agent_continues/>
{{/if}}
`.trim();

export const agentRoundTemplate = Handlebars.compile(AGENT_ROUND_TEMPLATE);
