export type AbridgedHistoryParams = {
  totalCharsLimit: number;
  userMessageCharsLimit: number;
  agentResponseCharsLimit: number;
  actionCharsLimit: number;

  numFilesModifiedLimit: number;
  numFilesCreatedLimit: number;
  numFilesDeletedLimit: number;
  numFilesViewedLimit: number;
  numTerminalCommandsLimit: number;
};

export type HistorySummaryParams = {
  triggerOnHistorySizeChars: number;
  historyTailSizeCharsToExclude: number;
  triggerOnHistorySizeCharsWhenCacheExpiring: number;

  prompt: string;

  cacheTTLMs: number;
  bufferTimeBeforeCacheExpirationMs: number;

  // fields to construct HistorySummaryMessage node
  summaryNodeRequestMessageTemplate: string;
  summaryNodeResponseMessage: string;

  abridgedHistoryParams: AbridgedHistoryParams;
};

export type AgentActionsSummary = {
  filesModified: Set<string>;
  filesCreated: Set<string>;
  filesDeleted: Set<string>;
  filesViewed: Set<string>;
  terminalCommands: Set<string>;
};

export type AbridgedAgentRound = {
  userMessage: string;
  agentFinalResponse: string;
  agentActionsSummary: AgentActionsSummary;
  wasInterrupted: boolean;
  continues: boolean;
};

export const DEFAULT_PARAMS: HistorySummaryParams = {
  triggerOnHistorySizeChars: 0,
  historyTailSizeCharsToExclude: 0,
  triggerOnHistorySizeCharsWhenCacheExpiring: 0,
  prompt: "",
  cacheTTLMs: 0,
  bufferTimeBeforeCacheExpirationMs: 0,
  summaryNodeRequestMessageTemplate: `
<supervisor>
Conversation history between Agent(you) and the user and history of tool calls was abridged and summarized to reduce context size.
Abridged conversation history:
{abridged_history}

Summary was generated by Agent(you) so 'I' in the summary represents Agent(you).
Here is the summary:
{summary}

Continue the conversation and finish the task given by the user from this point.
</supervisor>`,
  summaryNodeResponseMessage: "Ok. I will continue the conversation from this point.",
  abridgedHistoryParams: {
    totalCharsLimit: 10000,
    userMessageCharsLimit: 1000,
    agentResponseCharsLimit: 2000,
    actionCharsLimit: 200,
    numFilesModifiedLimit: 10,
    numFilesCreatedLimit: 10,
    numFilesDeletedLimit: 10,
    numFilesViewedLimit: 10,
    numTerminalCommandsLimit: 10,
  },
};
