import { describe, expect, test } from "vitest";
import { agentRoundTemplate } from "./agent-round-template";

describe("agent-round-template", () => {
  describe("agentRoundTemplate", () => {
    test("should render basic user message and agent response", () => {
      const data = {
        userMessage: "Hello, how are you?",
        agentResponse: "I'm doing well, thank you!",
        hasActions: false,
        filesModified: null,
        filesCreated: null,
        filesDeleted: null,
        filesViewed: null,
        terminalCommands: null,
      };

      const result = agentRoundTemplate(data);

      expect(result).toContain("<user>");
      expect(result).toContain("Hello, how are you?");
      expect(result).toContain("</user>");
      expect(result).toContain("<agent_response>");
      expect(result).toContain("I'm doing well, thank you!"); // Raw output with triple braces
      expect(result).toContain("</agent_response>");
      expect(result).not.toContain("<agent_actions>");
    });

    test("should render actions when hasActions is true", () => {
      const data = {
        userMessage: "Create a new file",
        agentResponse: "File created successfully",
        hasActions: true,
        filesModified: ["existing.ts"],
        filesCreated: ["new-file.ts"],
        filesDeleted: null,
        filesViewed: ["config.json"],
        terminalCommands: ["npm install"],
      };

      const result = agentRoundTemplate(data);

      expect(result).toContain("<user>");
      expect(result).toContain("Create a new file");
      expect(result).toContain("<agent_actions>");
      expect(result).toContain("<files_modified>");
      expect(result).toContain("existing.ts");
      expect(result).toContain("</files_modified>");
      expect(result).toContain("<files_created>");
      expect(result).toContain("new-file.ts");
      expect(result).toContain("</files_created>");
      expect(result).toContain("<files_viewed>");
      expect(result).toContain("config.json");
      expect(result).toContain("</files_viewed>");
      expect(result).toContain("<terminal_commands>");
      expect(result).toContain("npm install");
      expect(result).toContain("</terminal_commands>");
      expect(result).toContain("</agent_actions>");
      expect(result).toContain("<agent_response>");
      expect(result).toContain("File created successfully");
      expect(result).toContain("</agent_response>");
    });

    test("should render only non-null action types", () => {
      const data = {
        userMessage: "View a file",
        agentResponse: "Here's the file content",
        hasActions: true,
        filesModified: null,
        filesCreated: null,
        filesDeleted: null,
        filesViewed: ["important.ts"],
        terminalCommands: null,
      };

      const result = agentRoundTemplate(data);

      expect(result).toContain("<agent_actions>");
      expect(result).not.toContain("<files_modified>");
      expect(result).not.toContain("<files_created>");
      expect(result).not.toContain("<files_deleted>");
      expect(result).toContain("<files_viewed>");
      expect(result).toContain("important.ts");
      expect(result).not.toContain("<terminal_commands>");
      expect(result).toContain("</agent_actions>");
    });

    test("should handle empty agentResponse as null", () => {
      const data = {
        userMessage: "Test message",
        agentResponse: "",
        hasActions: false,
        filesModified: null,
        filesCreated: null,
        filesDeleted: null,
        filesViewed: null,
        terminalCommands: null,
      };

      const result = agentRoundTemplate(data);

      expect(result).toContain("<user>");
      expect(result).toContain("Test message");
      expect(result).not.toContain("<agent_response>");
      expect(result).not.toContain("<agent_actions>");
    });

    test("should handle multiple files in each category", () => {
      const data = {
        userMessage: "Refactor the codebase",
        agentResponse: "Refactoring complete",
        hasActions: true,
        filesModified: ["file1.ts", "file2.ts", "file3.ts"],
        filesCreated: ["new1.ts", "new2.ts"],
        filesDeleted: ["old1.ts"],
        filesViewed: ["config1.json", "config2.json"],
        terminalCommands: ["npm test", "npm build", "git commit"],
      };

      const result = agentRoundTemplate(data);

      // Check files modified
      expect(result).toContain("file1.ts");
      expect(result).toContain("file2.ts");
      expect(result).toContain("file3.ts");

      // Check files created
      expect(result).toContain("new1.ts");
      expect(result).toContain("new2.ts");

      // Check files deleted
      expect(result).toContain("old1.ts");

      // Check files viewed
      expect(result).toContain("config1.json");
      expect(result).toContain("config2.json");

      // Check terminal commands
      expect(result).toContain("npm test");
      expect(result).toContain("npm build");
      expect(result).toContain("git commit");
    });

    test("should preserve proper XML indentation", () => {
      const data = {
        userMessage: "Test",
        agentResponse: "Response",
        hasActions: true,
        filesModified: ["file.ts"],
        filesCreated: null,
        filesDeleted: null,
        filesViewed: null,
        terminalCommands: null,
      };

      const result = agentRoundTemplate(data);

      // Check that the XML is properly structured (contains expected elements)
      expect(result).toContain("<user>");
      expect(result).toContain("<agent_actions>");
      expect(result).toContain("<files_modified>");
      expect(result).toContain("file.ts");
      expect(result).toContain("</agent_actions>");
      expect(result).toContain("<agent_response>");
    });

    test("should not escape special characters in user message and agent response", () => {
      const data = {
        userMessage: "Use `backticks` & ampersands in code: const x = `template ${var}`;",
        agentResponse: "Here's code with special chars: `console.log('test & debug')` & more",
        hasActions: false,
        filesModified: null,
        filesCreated: null,
        filesDeleted: null,
        filesViewed: null,
        terminalCommands: null,
      };

      const result = agentRoundTemplate(data);

      // Verify that special characters are NOT HTML-escaped (raw output)
      expect(result).toContain(
        "Use `backticks` & ampersands in code: const x = `template ${var}`;",
      );
      expect(result).toContain(
        "Here's code with special chars: `console.log('test & debug')` & more",
      );

      // Verify that HTML-escaped versions are NOT present
      expect(result).not.toContain("&amp;");
      expect(result).not.toContain("&#x60;"); // HTML entity for backtick
      expect(result).not.toContain("&lt;");
      expect(result).not.toContain("&gt;");
    });
  });
});
