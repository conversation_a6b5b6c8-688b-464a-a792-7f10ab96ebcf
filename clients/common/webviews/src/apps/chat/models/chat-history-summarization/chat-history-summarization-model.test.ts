/* eslint-disable @typescript-eslint/naming-convention */
import { beforeEach, describe, expect, test, vi } from "vitest";
import { ChatHistorySummarizationModel } from "./chat-history-summarization-model";
import type { IConversationModel } from "../conversation-model-interface";
import type { IExtensionClient } from "../../extension-client";
import type { ChatFlagsModel } from "../chat-flags-model";
import {
  ChatItemType,
  ExchangeStatus,
  SeenState,
  type ChatItem,
  type HistorySummaryMessage,
} from "../../types/chat-message";
import { ChatResultNodeType } from "@augment-internal/sidecar-libs/src/chat/chat-types";
import { AgentRequestEventName } from "@augment-internal/sidecar-libs/src/metrics/types";
import type { Exchange } from "@augment-internal/sidecar-libs/src/chat/chat-types";
import { _DO_NOT_USE_IN_APP_CODE_unlockAllChains } from "../../utils/locking-chain";

// Helper function to create exchanges with specific character sizes
function createExchangeWithSize(id: string, targetSize: number): Exchange {
  const baseExchange: Exchange = {
    request_id: id,
    request_message: `Message ${id}`,
    response_text: `Response ${id}`,
    response_nodes: [],
  };

  // Calculate current size
  const currentSize = JSON.stringify(baseExchange).length;

  // Add padding to reach target size
  if (targetSize > currentSize) {
    const padding = "x".repeat(targetSize - currentSize);
    baseExchange.request_message += padding;
  }

  return baseExchange;
}

// Helper function to create exchanges that will definitely trigger summarization
function createLargeExchanges(): Exchange[] {
  return [
    {
      request_id: "req-1",
      request_message: "x".repeat(600), // Large message
      response_text: "x".repeat(600), // Large response
      response_nodes: [],
    },
    {
      request_id: "req-2",
      request_message: "x".repeat(600), // Large message
      response_text: "x".repeat(600), // Large response
      response_nodes: [],
    },
  ];
}

// Mock dependencies
const mockConversationModel = {
  conversationId: "test-conversation-id",
  chatHistory: [],
  convertHistoryToExchanges: vi.fn(),
  sendSilentExchange: vi.fn(),
  insertChatItem: vi.fn(),
  onSendExchange: vi.fn(),
  onNewConversation: vi.fn(),
  onHistoryDelete: vi.fn(),
} as IConversationModel;

const mockExtensionClient = {
  reportAgentRequestEvent: vi.fn(),
} as unknown as IExtensionClient;

const mockChatFlagsModel: ChatFlagsModel = {
  useHistorySummary: true,
  historySummaryParams: JSON.stringify({
    trigger_on_history_size_chars: 1000,
    history_tail_size_chars_to_exclude: 500,
    trigger_on_history_size_chars_when_cache_expiring: 800,
    prompt: "Summarize the conversation",
    cache_ttl_ms: 300000,
    buffer_time_before_cache_expiration_ms: 30000,
  }),
  subscribe: vi.fn(),
} as any;

describe("ChatHistorySummarizationModel", () => {
  let model: ChatHistorySummarizationModel;

  beforeEach(() => {
    _DO_NOT_USE_IN_APP_CODE_unlockAllChains();
    vi.clearAllMocks();

    // Set up default mock behavior for conversation model
    mockConversationModel.convertHistoryToExchanges = vi.fn().mockReturnValue([]);
    mockConversationModel.sendSilentExchange = vi.fn().mockResolvedValue({
      responseText: "This is a summary",
      requestId: "summary-req-id",
    });

    model = new ChatHistorySummarizationModel(
      mockConversationModel,
      mockExtensionClient,
      mockChatFlagsModel,
    );
  });

  describe("constructor", () => {
    test("should initialize with parsed params", () => {
      expect(mockChatFlagsModel.subscribe).toHaveBeenCalledWith(expect.any(Function));
    });

    test("should handle empty params string", () => {
      const emptyFlagsModel = {
        ...mockChatFlagsModel,
        historySummaryParams: "",
      };

      expect(() => {
        new ChatHistorySummarizationModel(
          mockConversationModel,
          mockExtensionClient,
          emptyFlagsModel as any,
        );
      }).not.toThrow();
    });

    test("should handle invalid JSON params", () => {
      const invalidFlagsModel = {
        ...mockChatFlagsModel,
        historySummaryParams: "invalid json",
      };

      expect(() => {
        new ChatHistorySummarizationModel(
          mockConversationModel,
          mockExtensionClient,
          invalidFlagsModel as any,
        );
      }).not.toThrow();
    });
  });

  describe("cancelRunningOrScheduledSummarizations", () => {
    test("should cancel all callbacks", () => {
      const cancelAllSpy = vi.spyOn(model["_callbacksManager"], "cancelAll");
      model.cancelRunningOrScheduledSummarizations();
      expect(cancelAllSpy).toHaveBeenCalled();
    });
  });

  describe("clearStaleHistorySummaryNodes", () => {
    test("should filter out stale history summary nodes", () => {
      const chatHistory: ChatItem[] = [
        {
          chatItemType: ChatItemType.historySummary,
          summaryVersion: 0, // stale version (different from current version 2)
          request_id: "old-summary",
        } as HistorySummaryMessage,
        {
          chatItemType: ChatItemType.historySummary,
          summaryVersion: 3, // current version
          request_id: "current-summary",
        } as HistorySummaryMessage,
        {
          request_message: "Regular message",
          status: ExchangeStatus.success,
        } as any,
      ];

      const result = model.clearStaleHistorySummaryNodes(chatHistory);

      expect(result).toHaveLength(2);
      expect(result[0]).toEqual(chatHistory[1]); // current version summary
      expect(result[1]).toEqual(chatHistory[2]); // regular message
    });

    test("should keep non-history-summary items", () => {
      const chatHistory: ChatItem[] = [
        {
          request_message: "Regular message",
          status: ExchangeStatus.success,
        } as any,
      ];

      const result = model.clearStaleHistorySummaryNodes(chatHistory);
      expect(result).toEqual(chatHistory);
    });
  });

  describe("maybeScheduleSummarization", () => {
    test("should not schedule when useHistorySummary is false", () => {
      const flagsModel = {
        ...mockChatFlagsModel,
        useHistorySummary: false,
      };
      const testModel = new ChatHistorySummarizationModel(
        mockConversationModel,
        mockExtensionClient,
        flagsModel as any,
      );

      const addCallbackSpy = vi.spyOn(testModel["_callbacksManager"], "addCallback");
      testModel.maybeScheduleSummarization(1000);
      expect(addCallbackSpy).not.toHaveBeenCalled();
    });

    test("should not schedule when trigger threshold is 0", () => {
      const flagsModel = {
        ...mockChatFlagsModel,
        historySummaryParams: JSON.stringify({
          trigger_on_history_size_chars: 0,
          trigger_on_history_size_chars_when_cache_expiring: 0,
          cache_ttl_ms: 0,
          buffer_time_before_cache_expiration_ms: 0,
        }),
      };
      const testModel = new ChatHistorySummarizationModel(
        mockConversationModel,
        mockExtensionClient,
        flagsModel as any,
      );

      const addCallbackSpy = vi.spyOn(testModel["_callbacksManager"], "addCallback");
      console.log("Test params:", testModel["_params"]);
      testModel.maybeScheduleSummarization(1000);
      expect(addCallbackSpy).not.toHaveBeenCalled();
    });

    test("should schedule summarization when conditions are met", () => {
      const addCallbackSpy = vi.spyOn(model["_callbacksManager"], "addCallback");
      model.maybeScheduleSummarization(1000); // 1 second response duration

      expect(addCallbackSpy).toHaveBeenCalledWith(expect.any(Function), expect.any(Number));
    });

    test("should not schedule when delay would be negative", () => {
      const addCallbackSpy = vi.spyOn(model["_callbacksManager"], "addCallback");
      model.maybeScheduleSummarization(300000); // Very long response duration

      expect(addCallbackSpy).not.toHaveBeenCalled();
    });
  });

  describe("preprocessChatHistory", () => {
    test("should trim history to last summary when useHistorySummary is true", () => {
      const chatHistory: ChatItem[] = [
        { request_message: "Message 1" } as any,
        { request_message: "Message 2" } as any,
        {
          chatItemType: ChatItemType.historySummary,
          summaryVersion: 3,
          request_id: "summary-1",
        } as HistorySummaryMessage,
        { request_message: "Message 3" } as any,
      ];

      const result = model.preprocessChatHistory(chatHistory);

      expect(result).toHaveLength(2);
      expect(result[0]).toEqual(chatHistory[2]); // summary
      expect(result[1]).toEqual(chatHistory[3]); // message after summary
    });

    test("should remove all history summaries when useHistorySummary is false", () => {
      const flagsModel = {
        ...mockChatFlagsModel,
        useHistorySummary: false,
      };
      const testModel = new ChatHistorySummarizationModel(
        mockConversationModel,
        mockExtensionClient,
        flagsModel as any,
      );

      const chatHistory: ChatItem[] = [
        { request_message: "Message 1" } as any,
        {
          chatItemType: ChatItemType.historySummary,
          summaryVersion: 3,
          request_id: "summary-1",
        } as HistorySummaryMessage,
        { request_message: "Message 2" } as any,
      ];

      const result = testModel.preprocessChatHistory(chatHistory);

      expect(result).toHaveLength(2);
      expect(result[0]).toEqual(chatHistory[0]);
      expect(result[1]).toEqual(chatHistory[2]);
    });

    test("should filter out stale summary versions", () => {
      const chatHistory: ChatItem[] = [
        {
          chatItemType: ChatItemType.historySummary,
          summaryVersion: 0, // stale
          request_id: "old-summary",
        } as HistorySummaryMessage,
        {
          chatItemType: ChatItemType.historySummary,
          summaryVersion: 3, // current
          request_id: "current-summary",
        } as HistorySummaryMessage,
        { request_message: "Message" } as any,
      ];

      const result = model.preprocessChatHistory(chatHistory);

      expect(result).toHaveLength(2);
      expect(result[0]).toEqual(chatHistory[1]); // current summary
      expect(result[1]).toEqual(chatHistory[2]); // message
    });
  });

  describe("maybeAddHistorySummaryNode", () => {
    beforeEach(() => {
      // Mock splitChatHistoryByCharLimit
      vi.doMock("@augment-internal/sidecar-libs/src/chat/chat-truncation", () => ({
        splitChatHistoryByCharLimit: vi.fn().mockReturnValue({
          head: [
            {
              request_id: "req-1",
              request_message: "Test message",
              response_nodes: [
                {
                  id: 1,
                  type: ChatResultNodeType.RAW_RESPONSE,
                  content: "Test response",
                },
              ],
            },
          ],
          tail: [],
          headSizeChars: 100,
          tailSizeChars: 0,
        }),
      }));

      mockConversationModel.convertHistoryToExchanges = vi.fn().mockReturnValue([
        {
          request_id: "req-1",
          request_message: "Test message",
          response_nodes: [],
        },
      ]);

      mockConversationModel.sendSilentExchange = vi.fn().mockResolvedValue({
        responseText: "This is a summary",
        requestId: "summary-req-id",
      });

      // Mock the chatHistory property
      Object.defineProperty(mockConversationModel, "chatHistory", {
        value: [
          {
            request_id: "req-1",
            request_message: "Test message",
            status: ExchangeStatus.success,
          },
        ] as any[],
        writable: true,
        configurable: true,
      });
    });

    test("should return false when prompt is empty", async () => {
      const flagsModel = {
        ...mockChatFlagsModel,
        historySummaryParams: JSON.stringify({
          prompt: "",
        }),
      };
      const testModel = new ChatHistorySummarizationModel(
        mockConversationModel,
        mockExtensionClient,
        flagsModel as any,
      );

      const result = await testModel.maybeAddHistorySummaryNode();
      expect(result).toBe(false);
    });

    test("should return false when threshold is 0", async () => {
      const flagsModel = {
        ...mockChatFlagsModel,
        historySummaryParams: JSON.stringify({
          prompt: "Summarize",
          trigger_on_history_size_chars: 0,
          trigger_on_history_size_chars_when_cache_expiring: 0,
        }),
      };
      const testModel = new ChatHistorySummarizationModel(
        mockConversationModel,
        mockExtensionClient,
        flagsModel as any,
      );

      const result = await testModel.maybeAddHistorySummaryNode();
      expect(result).toBe(false);
    });

    test("should return false when head is empty", async () => {
      const { splitChatHistoryByCharLimit } = await import(
        "@augment-internal/sidecar-libs/src/chat/chat-truncation"
      );
      (splitChatHistoryByCharLimit as any).mockReturnValue({
        head: [],
        tail: [],
        headSizeChars: 0,
        tailSizeChars: 0,
      });

      const result = await model.maybeAddHistorySummaryNode();
      expect(result).toBe(false);
    });

    test("should return false when aborted", async () => {
      const abortController = new AbortController();
      abortController.abort();

      const result = await model.maybeAddHistorySummaryNode(false, abortController.signal);
      expect(result).toBe(false);
    });

    test("should return false when no request ID returned", async () => {
      mockConversationModel.sendSilentExchange = vi.fn().mockResolvedValue({
        responseText: "Summary",
        requestId: null,
      });

      const result = await model.maybeAddHistorySummaryNode();
      expect(result).toBe(false);
    });

    test("should return false when response text is empty", async () => {
      mockConversationModel.sendSilentExchange = vi.fn().mockResolvedValue({
        responseText: "",
        requestId: "summary-req-id",
      });

      const result = await model.maybeAddHistorySummaryNode();
      expect(result).toBe(false);
    });

    test("should successfully create history summary", async () => {
      // Create exchanges large enough to trigger summarization (> 1000 chars)
      const exchanges = createLargeExchanges();

      (mockConversationModel.convertHistoryToExchanges as any).mockReturnValue(exchanges);

      const result = await model.maybeAddHistorySummaryNode();

      expect(result).toBe(true);
      expect(mockConversationModel.sendSilentExchange).toHaveBeenCalledWith({
        request_message: "Summarize the conversation",
        disableRetrieval: true,
        disableSelectedCodeDetails: true,
        chatHistory: expect.any(Array), // The head portion from splitChatHistoryByCharLimit
      });
      expect(mockConversationModel.insertChatItem).toHaveBeenCalledWith(
        expect.any(Number),
        expect.objectContaining({
          chatItemType: ChatItemType.historySummary,
          summaryVersion: 3,
          request_id: "summary-req-id",
          request_message: expect.stringContaining("<summary>\nThis is a summary\n</summary>"),
          response_text: "Ok. I will continue the conversation from this point.",
          status: ExchangeStatus.success,
          seen_state: SeenState.seen,
        }),
      );
      expect(mockExtensionClient.reportAgentRequestEvent).toHaveBeenCalledWith({
        eventName: AgentRequestEventName.chatHistorySummarization,
        conversationId: "test-conversation-id",
        requestId: "summary-req-id",
        chatHistoryLength: expect.any(Number), // Don't hardcode this since it depends on real function behavior
        eventData: {
          chatHistorySummarizationData: expect.objectContaining({
            totalHistoryCharCount: expect.any(Number),
            totalHistoryExchangeCount: expect.any(Number),
            headCharCount: expect.any(Number),
            headExchangeCount: expect.any(Number),
            summaryCharCount: expect.any(Number),
            summarizationDurationMs: expect.any(Number),
            isCacheAboutToExpire: false,
            isAborted: false,
          }),
        },
      });
    });

    test("should use cache expiring threshold when isCacheAboutToExpire is true", async () => {
      // Create exchanges large enough to trigger cache expiring threshold (> 800 chars)
      const exchanges = [
        createExchangeWithSize("req-1", 500),
        createExchangeWithSize("req-2", 500),
      ];

      (mockConversationModel.convertHistoryToExchanges as any).mockReturnValue(exchanges);

      const result = await model.maybeAddHistorySummaryNode(true);
      expect(result).toBe(true);

      // Verify that the analytics data reflects cache expiration
      expect(mockExtensionClient.reportAgentRequestEvent).toHaveBeenCalledWith(
        expect.objectContaining({
          eventData: {
            chatHistorySummarizationData: expect.objectContaining({
              isCacheAboutToExpire: true,
            }),
          },
        }),
      );
    });

    test("should handle tool use nodes correctly", async () => {
      // Create exchanges with tool use nodes and sufficient size to trigger summarization
      const exchanges = [
        {
          request_id: "req-1",
          request_message: "x".repeat(600), // Large message to ensure head is not empty
          response_text: "x".repeat(600), // Large response
          response_nodes: [
            {
              id: 1,
              type: ChatResultNodeType.TOOL_USE,
              content: "Tool use content",
              tool_use: {
                tool_name: "test-tool",
                tool_use_id: "tool-1",
                input_json: "{}",
              },
            },
            {
              id: 2,
              type: ChatResultNodeType.RAW_RESPONSE,
              content: "Response content",
            },
          ],
        },
        {
          request_id: "req-2",
          request_message: "x".repeat(600), // Large message
          response_text: "x".repeat(600), // Large response
          response_nodes: [],
        },
      ];

      (mockConversationModel.convertHistoryToExchanges as any).mockReturnValue(exchanges);

      const result = await model.maybeAddHistorySummaryNode();

      expect(result).toBe(true);
      expect(mockConversationModel.insertChatItem).toHaveBeenCalledWith(
        expect.any(Number),
        expect.objectContaining({
          structured_output_nodes: expect.arrayContaining([
            expect.objectContaining({
              type: ChatResultNodeType.RAW_RESPONSE,
              content: "Ok. I will continue the conversation from this point.",
            }),
            expect.objectContaining({
              type: ChatResultNodeType.TOOL_USE,
              content: "Tool use content",
            }),
          ]),
        }),
      );
    });
  });
});
