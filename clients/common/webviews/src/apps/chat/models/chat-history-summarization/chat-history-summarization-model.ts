/* eslint-disable no-console */
import { TimedCallbacksManager } from "@augment-internal/sidecar-libs/src/utils/timed-callbacks-manager";
import type { IConversationModel } from "../conversation-model-interface";
import {
  estimateHistorySizeInChars,
  splitChatHistoryByCharLimit,
} from "@augment-internal/sidecar-libs/src/chat/chat-truncation";
import type { IExtensionClient } from "../../extension-client";
import {
  AgentRequestEventName,
  type ChatHistorySummarizationData,
} from "@augment-internal/sidecar-libs/src/metrics/types";
import {
  type HistorySummaryMessage,
  ChatItemType,
  ExchangeStatus,
  SeenState,
  type ChatItem,
  isChatItemHistorySummary,
} from "../../types/chat-message";
import type { ChatFlagsModel } from "../chat-flags-model";
import {
  type ChatResultNode,
  ChatResultNodeType,
  type Exchange,
} from "@augment-internal/sidecar-libs/src/chat/chat-types";
import {
  type HistorySummaryParams,
  abridgeChatHistory,
  abridgedAgentRoundToText,
  parseParamsStr,
} from "./index";

export class ChatHistorySummarizationModel {
  // The current version of the history summary.
  private readonly historySummaryVersion: number = 3;
  private readonly _callbacksManager = new TimedCallbacksManager();
  private _params: HistorySummaryParams;

  constructor(
    private readonly _conversationModel: IConversationModel,
    private readonly _extensionClient: IExtensionClient,
    private readonly _chatFlagModel: ChatFlagsModel,
  ) {
    this._params = parseParamsStr(_chatFlagModel.historySummaryParams);
    _chatFlagModel.subscribe((flags) => {
      this._params = parseParamsStr(flags.historySummaryParams);
    });
  }

  public cancelRunningOrScheduledSummarizations(): void {
    this._callbacksManager.cancelAll();
  }

  /**
   * Generates abridged history text from the provided exchanges
   */
  private generateAbridgedHistoryText(head: Exchange[]): string {
    // Generate abridged chat history from the original chat history
    // We need to find the ChatItems that correspond to the head exchanges
    const headRequestIds = new Set(head.map((exchange) => exchange.request_id));
    const headChatItems = this._conversationModel.chatHistory.filter(
      (item) => item.request_id && headRequestIds.has(item.request_id),
    );
    const abridgedRounds = abridgeChatHistory(headChatItems);

    // Apply character limits and generate text in reverse order (most recent first)
    let totalChars = 0;
    const roundTexts: string[] = [];

    // Process rounds in reverse order to prioritize most recent rounds
    for (let i = abridgedRounds.length - 1; i >= 0; i--) {
      const round = abridgedRounds[i];
      const roundText = abridgedAgentRoundToText(round, this._params.abridgedHistoryParams);
      if (totalChars + roundText.length > this._params.abridgedHistoryParams.totalCharsLimit) {
        break; // Stop adding rounds if we exceed the total character limit
      }
      roundTexts.push(roundText); // Add to end, will reverse later
      totalChars += roundText.length;
    }

    // Reverse to restore chronological order (oldest first)
    roundTexts.reverse();
    return roundTexts.join("\n");
  }

  public clearStaleHistorySummaryNodes(chatHistory: ChatItem[]): ChatItem[] {
    return chatHistory.filter(
      (m) => !isChatItemHistorySummary(m) || m.summaryVersion === this.historySummaryVersion,
    );
  }

  public maybeScheduleSummarization(responseDurationMs: number): void {
    if (
      !this._chatFlagModel.useHistorySummary ||
      this._params.triggerOnHistorySizeCharsWhenCacheExpiring <= 0
    ) {
      return;
    }

    const maybeSummarizeAfterMs =
      this._params.cacheTTLMs - responseDurationMs - this._params.bufferTimeBeforeCacheExpirationMs;

    if (maybeSummarizeAfterMs > 0) {
      // Schedule chat history summarization just before input token cache expires after current request
      this._callbacksManager.addCallback(
        (abortSignal: AbortSignal) => {
          void this.maybeAddHistorySummaryNode(
            true, // isCacheAboutToExpire
            abortSignal,
          );
        },
        maybeSummarizeAfterMs, // delayMs
      );
    }
  }

  public preprocessChatHistory(history: ChatItem[]): ChatItem[] {
    const lastSummaryIdx = history.findLastIndex(
      (m) => isChatItemHistorySummary(m) && m.summaryVersion === this.historySummaryVersion,
    );
    if (this._chatFlagModel.useHistorySummary) {
      if (lastSummaryIdx > 0) {
        // Trim the history to the last summary node
        console.info(
          `Using history summary node found at index ${lastSummaryIdx} with requestId: ${history[lastSummaryIdx].request_id}`,
        );
        history = history.slice(lastSummaryIdx);
      }
      // Remove any history summary nodes that are not the latest version
      history = history.filter(
        (m) => !isChatItemHistorySummary(m) || m.summaryVersion === this.historySummaryVersion,
      );
    } else {
      // Remove any history summary nodes
      history = history.filter((m) => !isChatItemHistorySummary(m));
    }

    return history;
  }

  /**
   * Creates a history summary node by calling the chat model to summarize the provided
   * exchanges.
   */
  public async maybeAddHistorySummaryNode(
    isCacheAboutToExpire: boolean = false,
    abortSignal?: AbortSignal,
  ): Promise<boolean> {
    console.log("maybeAddHistorySummaryNode. isCacheAboutToExpire: ", isCacheAboutToExpire);

    if (!this._params.prompt || this._params.prompt.trim() === "") {
      console.log("maybeAddHistorySummaryNode. empty prompt");
      return false;
    }

    const historyExchanges = this._conversationModel.convertHistoryToExchanges(
      this._conversationModel.chatHistory,
    );
    // Split the history into two parts: head and tail.
    // The head is the part that we will summarize.
    // The tail is the part that we will keep in the history.
    // Use different thresholds depending on whether cache is about to expire
    const maxCharsThreshold = isCacheAboutToExpire
      ? this._params.triggerOnHistorySizeCharsWhenCacheExpiring
      : this._params.triggerOnHistorySizeChars;

    console.log("maybeAddHistorySummaryNode. maxCharsThreshold: ", maxCharsThreshold);
    if (maxCharsThreshold <= 0) {
      return false;
    }

    const { head, tail, headSizeChars, tailSizeChars } = splitChatHistoryByCharLimit(
      historyExchanges,
      this._params.historyTailSizeCharsToExclude,
      maxCharsThreshold,
      1, // lowerMinCount. Make sure that at least the last exchange is not summarized so that the model sees the last exchange in full
    );
    console.log(
      "maybeAddHistorySummaryNode. headSizeChars: ",
      headSizeChars,
      " tailSizeChars: ",
      tailSizeChars,
    );

    if (head.length === 0) {
      console.log("maybeAddHistorySummaryNode. head is empty. nothing to summarize");
      return false;
    }

    // Calculate character counts for metrics
    const totalHistoryChars = estimateHistorySizeInChars(historyExchanges);
    const headChars = estimateHistorySizeInChars(head);
    const tailChars = estimateHistorySizeInChars(tail);

    // Prepare summarization event data (will be completed after summarization)
    const eventData: ChatHistorySummarizationData = {
      totalHistoryCharCount: totalHistoryChars,
      totalHistoryExchangeCount: historyExchanges.length,
      headCharCount: headChars,
      headExchangeCount: head.length,
      headLastRequestId: head.at(-1)?.request_id ?? "",
      tailCharCount: tailChars,
      tailExchangeCount: tail.length,
      tailLastRequestId: tail.at(-1)?.request_id ?? "",
      // These will be filled in after summarization completes
      summaryCharCount: 0,
      summarizationDurationMs: 0,
      isCacheAboutToExpire: isCacheAboutToExpire,
      isAborted: false,
    };

    // If head ends with a tool use, then split that out so we can include in the
    // summarized request.
    let lastResponseNodes = head.at(-1)?.response_nodes ?? [];
    let lastToolUses: ChatResultNode[] = lastResponseNodes.filter(
      (node) => node.type === ChatResultNodeType.TOOL_USE,
    );
    if (lastToolUses.length > 0) {
      head.at(-1)!.response_nodes = lastResponseNodes.filter(
        (node) => node.type !== ChatResultNodeType.TOOL_USE,
      );
    }

    console.info("Summarizing %d turns of conversation history.", head.length);
    const abridgedHistoryText = this.generateAbridgedHistoryText(head);
    console.info("Abridged history text size: %d characters.", abridgedHistoryText.length);

    // Call the model to generate the summary
    const startTime = Date.now();
    const summarizationResponse = await this._conversationModel.sendSilentExchange({
      /* eslint-disable @typescript-eslint/naming-convention */
      request_message: this._params.prompt,
      // Make sure we send the message with history.
      disableRetrieval: true,
      disableSelectedCodeDetails: true,
      chatHistory: head,
      /* eslint-enable @typescript-eslint/naming-convention */
    });
    const endTime = Date.now();
    console.info("Summary text size: %d characters.", summarizationResponse.responseText.length);

    eventData.summaryCharCount = summarizationResponse.responseText.length;
    eventData.summarizationDurationMs = endTime - startTime;
    eventData.isAborted = !!abortSignal?.aborted;

    this._extensionClient.reportAgentRequestEvent({
      eventName: AgentRequestEventName.chatHistorySummarization,
      conversationId: this._conversationModel.conversationId,
      requestId: summarizationResponse.requestId ?? "UNKNOWN_REQUEST_ID",
      chatHistoryLength: this._conversationModel.chatHistory.length,
      eventData: {
        chatHistorySummarizationData: eventData,
      },
    });

    if (abortSignal?.aborted) {
      console.log("maybeAddHistorySummaryNode. aborted");
      return false;
    }

    if (!summarizationResponse.requestId || summarizationResponse.responseText.trim() === "") {
      console.log("maybeAddHistorySummaryNode. no request id or empty response");
      return false;
    }

    let requestMessage = this._params.summaryNodeRequestMessageTemplate.replace(
      "{summary}",
      `<summary>\n${summarizationResponse.responseText}\n</summary>`,
    );

    if (requestMessage.includes("{abridged_history}")) {
      requestMessage = requestMessage.replace(
        "{abridged_history}",
        `<abridged_history>\n${abridgedHistoryText}\n</abridged_history>`,
      );
    }
    const responseMessage = this._params.summaryNodeResponseMessage;

    const historySummaryItem: HistorySummaryMessage = {
      /* eslint-disable @typescript-eslint/naming-convention */
      chatItemType: ChatItemType.historySummary,
      summaryVersion: this.historySummaryVersion,
      // NOTE(arun): By using the history request id here, we can trace what summarized
      // in RI.
      request_id: summarizationResponse.requestId,
      request_message: requestMessage,
      response_text: responseMessage,
      structured_output_nodes: [
        {
          id: lastToolUses.map((node) => node.id).reduce((acc, id) => Math.max(acc, id), -1) + 1,
          type: ChatResultNodeType.RAW_RESPONSE,
          content: responseMessage,
        },
        ...lastToolUses,
      ],
      status: ExchangeStatus.success,
      seen_state: SeenState.seen,
      timestamp: new Date().toISOString(),
      /* eslint-enable @typescript-eslint/naming-convention */
    };

    // Insert the history summary node right after the last message in head.
    // Seek in reverse to be a little more efficient.
    const index =
      this._conversationModel.chatHistory.findLastIndex(
        (m) => m.request_id === head.at(-1)!.request_id,
      ) + 1;

    console.info("Adding a history summary node at index %d", index);

    this._conversationModel.insertChatItem(index, historySummaryItem);

    return true;
  }
}
