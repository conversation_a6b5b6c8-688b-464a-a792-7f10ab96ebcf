/* eslint-disable @typescript-eslint/naming-convention */
import { describe, expect, test } from "vitest";
import {
  updateActionsSummary,
  abridgeChatHistory,
  abridgedAgentRoundToText,
} from "./abridge-functions";
import type { AgentActionsSummary, AbridgedHistoryParams, AbridgedAgentRound } from "./types";
import { ChatItemType, ExchangeStatus, SeenState, type ChatItem } from "../../types/chat-message";
import { ChatResultNodeType } from "@augment-internal/sidecar-libs/src/chat/chat-types";

describe("abridge-functions", () => {
  // Helper function to create empty agent actions summary
  const createEmptyActionsSummary = (): AgentActionsSummary => ({
    filesModified: new Set<string>(),
    filesCreated: new Set<string>(),
    filesDeleted: new Set<string>(),
    filesViewed: new Set<string>(),
    terminalCommands: new Set<string>(),
  });

  // Helper function to create test parameters
  const createTestParams = (
    overrides: Partial<AbridgedHistoryParams> = {},
  ): AbridgedHistoryParams => ({
    totalCharsLimit: 10000,
    userMessageCharsLimit: 2000,
    agentResponseCharsLimit: 2000,
    actionCharsLimit: 120,
    numFilesModifiedLimit: 10,
    numFilesCreatedLimit: 10,
    numFilesDeletedLimit: 10,
    numFilesViewedLimit: 10,
    numTerminalCommandsLimit: 10,
    ...overrides,
  });

  describe("updateActionsSummary", () => {
    test("should parse str-replace-editor tool correctly", () => {
      const toolUse = {
        tool_name: "str-replace-editor",
        input_json: JSON.stringify({
          command: "str_replace",
          path: "test.ts",
          old_str: "old",
          new_str: "new",
        }),
      };
      const summary = createEmptyActionsSummary();

      updateActionsSummary(toolUse, summary);

      expect(summary.filesModified.has("test.ts")).toBe(true);
      expect(summary.filesCreated.size).toBe(0);
      expect(summary.filesDeleted.size).toBe(0);
      expect(summary.filesViewed.size).toBe(0);
      expect(summary.terminalCommands.size).toBe(0);
    });

    test("should parse save-file tool correctly", () => {
      const toolUse = {
        tool_name: "save-file",
        input_json: JSON.stringify({
          path: "new-file.ts",
          file_content: "content",
        }),
      };
      const summary = createEmptyActionsSummary();

      updateActionsSummary(toolUse, summary);

      expect(summary.filesCreated.has("new-file.ts")).toBe(true);
      expect(summary.filesModified.size).toBe(0);
    });

    test("should parse remove-files tool correctly", () => {
      const toolUse = {
        tool_name: "remove-files",
        input_json: JSON.stringify({
          file_paths: ["file1.ts", "file2.ts"],
        }),
      };
      const summary = createEmptyActionsSummary();

      updateActionsSummary(toolUse, summary);

      expect(summary.filesDeleted.has("file1.ts")).toBe(true);
      expect(summary.filesDeleted.has("file2.ts")).toBe(true);
      expect(summary.filesDeleted.size).toBe(2);
    });

    test("should parse view tool correctly", () => {
      const toolUse = {
        tool_name: "view",
        input_json: JSON.stringify({
          path: "viewed-file.ts",
          type: "file",
        }),
      };
      const summary = createEmptyActionsSummary();

      updateActionsSummary(toolUse, summary);

      expect(summary.filesViewed.has("viewed-file.ts")).toBe(true);
    });

    test("should parse launch-process tool correctly", () => {
      const toolUse = {
        tool_name: "launch-process",
        input_json: JSON.stringify({
          command: "npm test",
          wait: true,
        }),
      };
      const summary = createEmptyActionsSummary();

      updateActionsSummary(toolUse, summary);

      expect(summary.terminalCommands.has("npm test")).toBe(true);
    });

    test("should handle invalid JSON gracefully", () => {
      const toolUse = {
        tool_name: "str-replace-editor",
        input_json: "invalid json",
      };
      const summary = createEmptyActionsSummary();

      expect(() => updateActionsSummary(toolUse, summary)).not.toThrow();
      expect(summary.filesModified.size).toBe(0);
    });
  });

  describe("abridgeChatHistory", () => {
    test("should remove modified files from viewed files", () => {
      const chatHistory: ChatItem[] = [
        {
          request_message: "Test request",
          status: ExchangeStatus.success,
          structured_output_nodes: [
            {
              type: ChatResultNodeType.TOOL_USE,
              tool_use: {
                tool_name: "view",
                input_json: JSON.stringify({ path: "test.ts" }),
              },
            },
            {
              type: ChatResultNodeType.TOOL_USE,
              tool_use: {
                tool_name: "str-replace-editor",
                input_json: JSON.stringify({ path: "test.ts" }),
              },
            },
          ],
        } as any,
      ];

      const rounds = abridgeChatHistory(chatHistory);

      expect(rounds).toHaveLength(1);
      const round = rounds[0];

      // test.ts should be in filesModified but NOT in filesViewed (removed due to redundancy)
      expect(round.agentActionsSummary.filesModified.has("test.ts")).toBe(true);
      expect(round.agentActionsSummary.filesViewed.has("test.ts")).toBe(false);
    });
    test("should process chat items correctly", () => {
      const mockChatItems: ChatItem[] = [
        {
          // chatItemType should be undefined for regular exchanges
          request_id: "req-1",
          request_message: "Help me debug this issue",
          response_text: "I'll help you debug the issue",
          structured_output_nodes: [
            {
              id: 1,
              type: ChatResultNodeType.TOOL_USE,
              content: "",
              tool_use: {
                tool_use_id: "test-id",
                tool_name: "str-replace-editor",
                input_json: JSON.stringify({
                  path: "debug.ts",
                  command: "str_replace",
                }),
              },
            },
          ],
          status: ExchangeStatus.success,
          seen_state: SeenState.seen,
          timestamp: "2023-01-01T00:00:00Z",
        },
      ];

      const result = abridgeChatHistory(mockChatItems);

      expect(result).toHaveLength(1);
      expect(result[0].userMessage).toBe("Help me debug this issue");
      expect(result[0].agentActionsSummary.filesModified.has("debug.ts")).toBe(true);
    });

    test("should skip non-exchange items", () => {
      const mockChatItems: ChatItem[] = [
        {
          chatItemType: ChatItemType.historySummary,
          request_id: "summary-1",
        } as any,
        {
          request_id: "req-1",
          request_message: "Valid exchange",
          response_text: "Response",
          status: ExchangeStatus.success,
          seen_state: SeenState.seen,
          timestamp: "2023-01-01T00:00:00Z",
        },
      ];

      const result = abridgeChatHistory(mockChatItems);

      expect(result).toHaveLength(1);
      expect(result[0].userMessage).toBe("Valid exchange");
    });
  });

  describe("abridgedAgentRoundToText", () => {
    test("should generate XML for round with actions and response", () => {
      const round: AbridgedAgentRound = {
        userMessage: "Test user message",
        agentFinalResponse: "Test agent response",
        agentActionsSummary: {
          filesModified: new Set(["file1.ts"]),
          filesCreated: new Set(["file2.ts"]),
          filesDeleted: new Set(),
          filesViewed: new Set(),
          terminalCommands: new Set(["npm test"]),
        },
        wasInterrupted: false,
        continues: false,
      };

      const result = abridgedAgentRoundToText(round, createTestParams());

      expect(result).toContain("<user>");
      expect(result).toContain("Test user message");
      expect(result).toContain("<agent_actions>");
      expect(result).toContain("<files_modified>");
      expect(result).toContain("file1.ts");
      expect(result).toContain("<files_created>");
      expect(result).toContain("file2.ts");
      expect(result).toContain("<terminal_commands>");
      expect(result).toContain("npm test");
      expect(result).toContain("<agent_response>");
      expect(result).toContain("Test agent response");
    });

    test("should truncate messages when limits provided", () => {
      const longUserMessage =
        "This is a very long user message that should be truncated in the middle to preserve both the beginning and the end of the message content.";
      const longAgentResponse =
        "This is a very long agent response that should also be truncated in the middle to preserve both the beginning and the end of the response content.";

      const round: AbridgedAgentRound = {
        userMessage: longUserMessage,
        agentFinalResponse: longAgentResponse,
        agentActionsSummary: createEmptyActionsSummary(),
        wasInterrupted: false,
        continues: false,
      };

      const params = createTestParams({
        userMessageCharsLimit: 50,
        agentResponseCharsLimit: 50,
      });

      const result = abridgedAgentRoundToText(round, params);

      // Check that the result contains truncated messages with "..." in the middle
      expect(result).toContain("...");
      // Check that it starts with the beginning of the user message
      expect(result).toContain("This is a very lon");
      // Check that it contains the end of the user message
      expect(result).toContain("content.");
    });

    test("should apply action limits and show overflow messages", () => {
      const round: AbridgedAgentRound = {
        userMessage: "Test message",
        agentFinalResponse: "Test response",
        agentActionsSummary: {
          filesModified: new Set(["file1.ts", "file2.ts", "file3.ts", "file4.ts", "file5.ts"]),
          filesCreated: new Set(["new1.ts", "new2.ts"]),
          filesDeleted: new Set(),
          filesViewed: new Set(["view1.ts", "view2.ts", "view3.ts"]),
          terminalCommands: new Set(["npm test", "git status", "ls -la", "pwd"]),
        },
        wasInterrupted: false,
        continues: false,
      };

      const params = createTestParams({
        numFilesModifiedLimit: 3,
        numFilesCreatedLimit: 1,
        numFilesViewedLimit: 2,
        numTerminalCommandsLimit: 2,
      });

      const result = abridgedAgentRoundToText(round, params);

      // Should show overflow messages
      expect(result).toContain("... 2 more files"); // 5 modified files, limit 3, so 2 more
      expect(result).toContain("... 1 more files"); // 2 created files, limit 1, so 1 more
      expect(result).toContain("... 1 more files"); // 3 viewed files, limit 2, so 1 more
      expect(result).toContain("... 2 more commands"); // 4 terminal commands, limit 2, so 2 more
    });

    test("should handle round with no actions", () => {
      const round: AbridgedAgentRound = {
        userMessage: "Simple question",
        agentFinalResponse: "Simple answer",
        agentActionsSummary: createEmptyActionsSummary(),
        wasInterrupted: false,
        continues: false,
      };

      const result = abridgedAgentRoundToText(round, createTestParams());

      expect(result).toContain("<user>");
      expect(result).toContain("Simple question");
      expect(result).not.toContain("<agent_actions>");
      expect(result).toContain("<agent_response>");
      expect(result).toContain("Simple answer");
    });

    test("should handle round with actions but no response", () => {
      const round: AbridgedAgentRound = {
        userMessage: "Do something",
        agentFinalResponse: "",
        agentActionsSummary: {
          filesModified: new Set(["file.ts"]),
          filesCreated: new Set(),
          filesDeleted: new Set(),
          filesViewed: new Set(),
          terminalCommands: new Set(),
        },
        wasInterrupted: false,
        continues: false,
      };

      const result = abridgedAgentRoundToText(round, createTestParams());

      expect(result).toContain("<user>");
      expect(result).toContain("Do something");
      expect(result).toContain("<agent_actions>");
      expect(result).toContain("file.ts");
      expect(result).not.toContain("<agent_response>");
    });

    test("should handle interrupted round", () => {
      const round: AbridgedAgentRound = {
        userMessage: "Do something",
        agentFinalResponse: "",
        agentActionsSummary: {
          filesModified: new Set(["file.ts"]),
          filesCreated: new Set(),
          filesDeleted: new Set(),
          filesViewed: new Set(),
          terminalCommands: new Set(),
        },
        wasInterrupted: true,
        continues: false,
      };

      const result = abridgedAgentRoundToText(round, createTestParams());

      expect(result).toContain("<user>");
      expect(result).toContain("Do something");
      expect(result).toContain("<agent_actions>");
      expect(result).toContain("file.ts");
      expect(result).toContain("<agent_was_interrupted/>");
      expect(result).not.toContain("<agent_response>");
    });

    test("should handle continuing round", () => {
      const round: AbridgedAgentRound = {
        userMessage: "Do something",
        agentFinalResponse: "",
        agentActionsSummary: {
          filesModified: new Set(["file.ts"]),
          filesCreated: new Set(),
          filesDeleted: new Set(),
          filesViewed: new Set(),
          terminalCommands: new Set(),
        },
        wasInterrupted: false,
        continues: true,
      };

      const result = abridgedAgentRoundToText(round, createTestParams());

      expect(result).toContain("<user>");
      expect(result).toContain("Do something");
      expect(result).toContain("<agent_actions>");
      expect(result).toContain("file.ts");
      expect(result).toContain("<agent_continues/>");
      expect(result).not.toContain("<agent_response>");
    });
  });
});
