import { describe, expect, test } from "vitest";
import { truncateMiddle, getValueOrDefault } from "./utils";

describe("utils", () => {
  describe("truncateMiddle", () => {
    test("should not truncate text shorter than maxLength", () => {
      const text = "Short text";
      const result = truncateMiddle(text, 50);
      expect(result).toBe(text);
    });

    test("should truncate text longer than maxLength", () => {
      const text =
        "This is a very long text that should be truncated in the middle to preserve both start and end";
      const maxLength = 50;
      const result = truncateMiddle(text, maxLength);

      expect(result.length).toBeLessThanOrEqual(maxLength);
      expect(result).toContain("...");
      expect(result).toContain("This is a very"); // Start preserved
      expect(result).toContain("start and end"); // End preserved
    });

    test("should respect custom start and end ratios", () => {
      const text = "This is a very long text that should be truncated";
      const maxLength = 30;
      const result = truncateMiddle(text, maxLength, 0.7, 0.3); // 70% start, 30% end

      expect(result.length).toBeLessThanOrEqual(maxLength);
      expect(result).toContain("...");
      // Should have more content from the start due to 0.7 ratio
      expect(result).toContain("This is a very");
      expect(result).toContain("runcated"); // End part of "truncated"
    });

    test("should handle edge case with very small maxLength", () => {
      const text = "Long text";
      const result = truncateMiddle(text, 5);

      expect(result.length).toBeLessThanOrEqual(5);
      expect(result).toContain("...");
    });
  });

  describe("getValueOrDefault", () => {
    test("should return value when defined", () => {
      expect(getValueOrDefault(42, 100)).toBe(42);
      expect(getValueOrDefault("hello", "default")).toBe("hello");
      expect(getValueOrDefault(true, false)).toBe(true);
    });

    test("should return default when value is undefined", () => {
      expect(getValueOrDefault(undefined, 100)).toBe(100);
      expect(getValueOrDefault(undefined, "default")).toBe("default");
      expect(getValueOrDefault(undefined, false)).toBe(false);
    });

    test("should return 0 when value is 0 (not default)", () => {
      expect(getValueOrDefault(0, 100)).toBe(0);
    });

    test("should return empty string when value is empty string (not default)", () => {
      expect(getValueOrDefault("", "default")).toBe("");
    });

    test("should return false when value is false (not default)", () => {
      expect(getValueOrDefault(false, true)).toBe(false);
    });
  });
});
