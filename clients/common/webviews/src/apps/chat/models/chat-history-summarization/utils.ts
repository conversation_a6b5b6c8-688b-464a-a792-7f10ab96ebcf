/**
 * Truncates text by removing content from the middle while preserving start and end.
 * @param text The text to truncate
 * @param maxLength Maximum allowed length (<=0 means no limit)
 * @param startRatio Ratio of text to keep at the start
 * @param endRatio Ratio of text to keep at the end
 * @returns Truncated text with "..." in the middle if truncation occurred
 */
export function truncateMiddle(
  text: string,
  maxLength: number,
  startRatio: number = 0.5,
  endRatio: number = 0.5,
): string {
  if (text.length <= maxLength || maxLength <= 0) {
    return text;
  }

  // Validate ratios
  if (startRatio + endRatio > 1.0) {
    throw new Error("startRatio + endRatio cannot exceed 1.0");
  }

  const ellipsis = "...";
  const availableLength = maxLength - ellipsis.length;

  // Handle edge case where maxLength is too small
  if (availableLength <= 0) {
    return ellipsis.substring(0, maxLength);
  }

  const startLength = Math.floor(availableLength * startRatio);
  const endLength = Math.floor(availableLength * endRatio);

  const startText = text.substring(0, startLength);
  const endText = text.substring(text.length - endLength);

  return startText + ellipsis + endText;
}

/**
 * Helper function to get value with fallback to default, properly handling undefined vs falsy values
 * @param value The value to check
 * @param defaultValue The default value to use if value is undefined
 * @returns The value if defined, otherwise the default
 */
export const getValueOrDefault = <T>(value: T | undefined, defaultValue: T): T =>
  value !== undefined ? value : defaultValue;
