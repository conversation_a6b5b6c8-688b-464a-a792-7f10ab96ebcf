/* eslint-disable no-console */
import { getValueOrDefault } from "./utils";
import { DEFAULT_PARAMS, type HistorySummaryParams } from "./types";

/**
 * Parses the history summary parameters from a JSON string
 */
export function parseParamsStr(paramsStr: string): HistorySummaryParams {
  try {
    if (!paramsStr) {
      console.log("historySummaryParams is empty. Using default params");
      return DEFAULT_PARAMS;
    }
    const params = JSON.parse(paramsStr);

    const parsedParams = {
      triggerOnHistorySizeChars: getValueOrDefault(
        params.trigger_on_history_size_chars,
        DEFAULT_PARAMS.triggerOnHistorySizeChars,
      ),
      historyTailSizeCharsToExclude: getValueOrDefault(
        params.history_tail_size_chars_to_exclude,
        DEFAULT_PARAMS.historyTailSizeCharsToExclude,
      ),
      triggerOnHistorySizeCharsWhenCacheExpiring: getValueOrDefault(
        params.trigger_on_history_size_chars_when_cache_expiring,
        DEFAULT_PARAMS.triggerOnHistorySizeCharsWhenCacheExpiring,
      ),
      prompt: getValueOrDefault(params.prompt, DEFAULT_PARAMS.prompt),
      cacheTTLMs: getValueOrDefault(params.cache_ttl_ms, DEFAULT_PARAMS.cacheTTLMs),
      bufferTimeBeforeCacheExpirationMs: getValueOrDefault(
        params.buffer_time_before_cache_expiration_ms,
        DEFAULT_PARAMS.bufferTimeBeforeCacheExpirationMs,
      ),
      summaryNodeRequestMessageTemplate: getValueOrDefault(
        params.summary_node_request_message_template,
        DEFAULT_PARAMS.summaryNodeRequestMessageTemplate,
      ),
      summaryNodeResponseMessage: getValueOrDefault(
        params.summary_node_response_message,
        DEFAULT_PARAMS.summaryNodeResponseMessage,
      ),
      abridgedHistoryParams: {
        totalCharsLimit: getValueOrDefault(
          params.abridged_history_params?.total_chars_limit,
          DEFAULT_PARAMS.abridgedHistoryParams.totalCharsLimit,
        ),
        userMessageCharsLimit: getValueOrDefault(
          params.abridged_history_params?.user_message_chars_limit,
          DEFAULT_PARAMS.abridgedHistoryParams.userMessageCharsLimit,
        ),
        agentResponseCharsLimit: getValueOrDefault(
          params.abridged_history_params?.agent_response_chars_limit,
          DEFAULT_PARAMS.abridgedHistoryParams.agentResponseCharsLimit,
        ),
        actionCharsLimit: getValueOrDefault(
          params.abridged_history_params?.action_chars_limit,
          DEFAULT_PARAMS.abridgedHistoryParams.actionCharsLimit,
        ),
        numFilesModifiedLimit: getValueOrDefault(
          params.abridged_history_params?.num_files_modified_limit,
          DEFAULT_PARAMS.abridgedHistoryParams.numFilesModifiedLimit,
        ),
        numFilesCreatedLimit: getValueOrDefault(
          params.abridged_history_params?.num_files_created_limit,
          DEFAULT_PARAMS.abridgedHistoryParams.numFilesCreatedLimit,
        ),
        numFilesDeletedLimit: getValueOrDefault(
          params.abridged_history_params?.num_files_deleted_limit,
          DEFAULT_PARAMS.abridgedHistoryParams.numFilesDeletedLimit,
        ),
        numFilesViewedLimit: getValueOrDefault(
          params.abridged_history_params?.num_files_viewed_limit,
          DEFAULT_PARAMS.abridgedHistoryParams.numFilesViewedLimit,
        ),
        numTerminalCommandsLimit: getValueOrDefault(
          params.abridged_history_params?.num_terminal_commands_limit,
          DEFAULT_PARAMS.abridgedHistoryParams.numTerminalCommandsLimit,
        ),
      },
    };
    // Check that summaryNodeRequestMessageTemplate contains {summary}
    if (!parsedParams.summaryNodeRequestMessageTemplate.includes("{summary}")) {
      console.error(
        "summaryNodeRequestMessageTemplate must contain {summary}. Using default template",
      );
      parsedParams.summaryNodeRequestMessageTemplate =
        DEFAULT_PARAMS.summaryNodeRequestMessageTemplate;
    }

    const logSafeParams = { ...parsedParams, prompt: parsedParams.prompt.slice(0, 10) + "..." };
    console.log("historySummaryParams updated: ", logSafeParams);
    return parsedParams;
  } catch (e) {
    console.error("Failed to parse history_summary_params:", e);
    return DEFAULT_PARAMS;
  }
}
