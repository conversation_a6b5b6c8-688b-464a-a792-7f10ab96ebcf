/* eslint-disable @typescript-eslint/naming-convention */
import { describe, expect, test, vi, beforeEach, afterEach } from "vitest";
import { parseParamsStr } from "./params-parser";
import { DEFAULT_PARAMS } from "./types";

describe("params-parser", () => {
  let consoleSpy: any;

  beforeEach(() => {
    consoleSpy = vi.spyOn(console, "log").mockImplementation(() => {});
    vi.spyOn(console, "error").mockImplementation(() => {});
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe("parseParamsStr", () => {
    test("should return default params when paramsStr is empty", () => {
      const result = parseParamsStr("");

      expect(result).toEqual(DEFAULT_PARAMS);
      expect(consoleSpy).toHaveBeenCalledWith(
        "historySummaryParams is empty. Using default params",
      );
    });

    test("should handle valid JSON parameters", () => {
      const validParams = {
        trigger_on_history_size_chars: 2000,
        history_tail_size_chars_to_exclude: 1000,
        trigger_on_history_size_chars_when_cache_expiring: 1500,
        prompt: "Custom prompt",
        cache_ttl_ms: 600000,
        buffer_time_before_cache_expiration_ms: 60000,
        summary_node_request_message_template: "Custom template with {summary}",
        summary_node_response_message: "Custom response",
        abridged_history_params: {
          total_chars_limit: 15000,
          user_message_chars_limit: 3000,
          agent_response_chars_limit: 3000,
          action_chars_limit: 120,
          num_files_modified_limit: 15,
          num_files_created_limit: 15,
          num_files_deleted_limit: 15,
          num_files_viewed_limit: 15,
          num_terminal_commands_limit: 15,
        },
      };

      const params = parseParamsStr(JSON.stringify(validParams));

      expect(params.triggerOnHistorySizeChars).toBe(2000);
      expect(params.historyTailSizeCharsToExclude).toBe(1000);
      expect(params.triggerOnHistorySizeCharsWhenCacheExpiring).toBe(1500);
      expect(params.prompt).toBe("Custom prompt");
      expect(params.cacheTTLMs).toBe(600000);
      expect(params.bufferTimeBeforeCacheExpirationMs).toBe(60000);
      expect(params.summaryNodeRequestMessageTemplate).toBe("Custom template with {summary}");
      expect(params.summaryNodeResponseMessage).toBe("Custom response");
      expect(params.abridgedHistoryParams.totalCharsLimit).toBe(15000);
      expect(params.abridgedHistoryParams.userMessageCharsLimit).toBe(3000);
      expect(params.abridgedHistoryParams.agentResponseCharsLimit).toBe(3000);
      expect(params.abridgedHistoryParams.actionCharsLimit).toBe(120);
      expect(params.abridgedHistoryParams.numFilesModifiedLimit).toBe(15);
      expect(params.abridgedHistoryParams.numFilesCreatedLimit).toBe(15);
      expect(params.abridgedHistoryParams.numFilesDeletedLimit).toBe(15);
      expect(params.abridgedHistoryParams.numFilesViewedLimit).toBe(15);
      expect(params.abridgedHistoryParams.numTerminalCommandsLimit).toBe(15);
    });

    test("should use default values for missing parameters", () => {
      const partialParams = {
        prompt: "Only prompt provided",
      };

      const params = parseParamsStr(JSON.stringify(partialParams));

      expect(params.triggerOnHistorySizeChars).toBe(DEFAULT_PARAMS.triggerOnHistorySizeChars);
      expect(params.prompt).toBe("Only prompt provided");
      expect(params.cacheTTLMs).toBe(DEFAULT_PARAMS.cacheTTLMs);
      expect(params.abridgedHistoryParams.totalCharsLimit).toBe(
        DEFAULT_PARAMS.abridgedHistoryParams.totalCharsLimit,
      );
    });

    test("should handle 0 values correctly without defaulting", () => {
      const paramsWithZeros = {
        prompt: "Test prompt",
        trigger_on_history_size_chars: 0,
        cache_ttl_ms: 0,
      };

      const params = parseParamsStr(JSON.stringify(paramsWithZeros));

      expect(params.triggerOnHistorySizeChars).toBe(0);
      expect(params.cacheTTLMs).toBe(0);
      expect(params.prompt).toBe("Test prompt");
    });

    test("should handle invalid JSON and return default params", () => {
      const errorSpy = vi.spyOn(console, "error");

      const result = parseParamsStr("invalid json");

      expect(result).toEqual(DEFAULT_PARAMS);
      expect(errorSpy).toHaveBeenCalledWith(
        "Failed to parse history_summary_params:",
        expect.any(Error),
      );
    });

    test("should validate summaryNodeRequestMessageTemplate contains {summary}", () => {
      const errorSpy = vi.spyOn(console, "error");

      const paramsWithoutSummary = {
        summary_node_request_message_template: "Template without summary placeholder",
      };

      const params = parseParamsStr(JSON.stringify(paramsWithoutSummary));

      expect(params.summaryNodeRequestMessageTemplate).toBe(
        DEFAULT_PARAMS.summaryNodeRequestMessageTemplate,
      );
      expect(errorSpy).toHaveBeenCalledWith(
        "summaryNodeRequestMessageTemplate must contain {summary}. Using default template",
      );
    });

    test("should keep valid summaryNodeRequestMessageTemplate with {summary}", () => {
      const validTemplate = "Custom template with {summary} placeholder";
      const paramsWithValidTemplate = {
        summary_node_request_message_template: validTemplate,
      };

      const params = parseParamsStr(JSON.stringify(paramsWithValidTemplate));

      expect(params.summaryNodeRequestMessageTemplate).toBe(validTemplate);
    });

    test("should log safe params without exposing full prompt", () => {
      const longPrompt = "This is a very long prompt that should be truncated in the log";
      const paramsWithLongPrompt = {
        prompt: longPrompt,
      };

      const logSpy = vi.spyOn(console, "log");

      parseParamsStr(JSON.stringify(paramsWithLongPrompt));

      // Should log with truncated prompt
      expect(logSpy).toHaveBeenCalledWith(
        "historySummaryParams updated: ",
        expect.objectContaining({
          prompt: "This is a ...",
        }),
      );
    });

    test("should handle missing abridged_history_params gracefully", () => {
      const paramsWithoutAbridged = {
        prompt: "Test prompt",
        trigger_on_history_size_chars: 1000,
      };

      const params = parseParamsStr(JSON.stringify(paramsWithoutAbridged));

      expect(params.abridgedHistoryParams).toEqual(DEFAULT_PARAMS.abridgedHistoryParams);
      expect(params.prompt).toBe("Test prompt");
      expect(params.triggerOnHistorySizeChars).toBe(1000);
    });

    test("should handle partial abridged_history_params", () => {
      const paramsWithPartialAbridged = {
        abridged_history_params: {
          total_chars_limit: 5000,
          user_message_chars_limit: 1000,
          // Missing other parameters
        },
      };

      const params = parseParamsStr(JSON.stringify(paramsWithPartialAbridged));

      expect(params.abridgedHistoryParams.totalCharsLimit).toBe(5000);
      expect(params.abridgedHistoryParams.userMessageCharsLimit).toBe(1000);
      // Should use defaults for missing parameters
      expect(params.abridgedHistoryParams.agentResponseCharsLimit).toBe(
        DEFAULT_PARAMS.abridgedHistoryParams.agentResponseCharsLimit,
      );
      expect(params.abridgedHistoryParams.numFilesModifiedLimit).toBe(
        DEFAULT_PARAMS.abridgedHistoryParams.numFilesModifiedLimit,
      );
    });
  });
});
