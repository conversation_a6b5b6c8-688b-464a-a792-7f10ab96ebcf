/* eslint-disable @typescript-eslint/naming-convention */
import {
  type ChatItem,
  type ExchangeWithStatus,
  isChatItemExchangeWithStatus,
  hasToolResult,
} from "../../types/chat-message";
import { ChatResultNodeType } from "@augment-internal/sidecar-libs/src/chat/chat-types";
import { agentRoundTemplate } from "./agent-round-template";
import { truncateMiddle } from "./utils";
import type { AbridgedHistoryParams, AgentActionsSummary, AbridgedAgentRound } from "./types";

/**
 * Converts chat history into abridged agent rounds
 */
export function abridgeChatHistory(history: ChatItem[]): AbridgedAgentRound[] {
  const rounds: AbridgedAgentRound[] = [];
  let currentRound: AbridgedAgentRound | null = null;

  for (const item of history) {
    // Skip non-exchange items
    if (!isChatItemExchangeWithStatus(item)) {
      continue;
    }

    const exchange = item as ExchangeWithStatus;

    // Start a new round with user message (is not a tool result)
    if (!hasToolResult(exchange)) {
      // If there's a current round, push it to rounds
      if (currentRound) {
        if (currentRound.agentFinalResponse.trim() === "") {
          currentRound.wasInterrupted = true;
        }
        rounds.push(currentRound);
      }

      // Start new round
      currentRound = {
        userMessage: extractUserMessage(exchange),
        agentActionsSummary: {
          filesModified: new Set(),
          filesCreated: new Set(),
          filesDeleted: new Set(),
          filesViewed: new Set(),
          terminalCommands: new Set(),
        },
        agentFinalResponse: "",
        wasInterrupted: false,
        continues: false,
      };
    }

    if (!currentRound) {
      continue;
    }

    // Process tool uses from structured_output_nodes
    let hasToolUses = false;
    if (exchange.structured_output_nodes) {
      for (const node of exchange.structured_output_nodes) {
        if (node.type === ChatResultNodeType.TOOL_USE && node.tool_use) {
          hasToolUses = true;
          updateActionsSummary(node.tool_use, currentRound.agentActionsSummary);
        }
      }
    }

    // Also check response_text for agent final response
    if (!hasToolUses && exchange.response_text) {
      currentRound.agentFinalResponse = exchange.response_text;
    }
  }

  // Don't forget the last round
  if (currentRound) {
    if (currentRound.agentFinalResponse.trim() === "") {
      currentRound.continues = true;
    }
    rounds.push(currentRound);
  }

  // Finalize all rounds by removing redundant viewed files
  for (const round of rounds) {
    finalizeActionsSummary(round.agentActionsSummary);
  }

  return rounds;
}

/**
 * Finalizes the actions summary by removing redundant entries
 */
function finalizeActionsSummary(summary: AgentActionsSummary): void {
  // Remove modified files from viewed files since they're redundant
  for (const modifiedFile of summary.filesModified) {
    summary.filesViewed.delete(modifiedFile);
  }
}

/**
 * Parses tool use and updates the agent actions summary
 */
export function updateActionsSummary(
  toolUse: { tool_name: string; input_json: string },
  summary: AgentActionsSummary,
): void {
  try {
    const input = JSON.parse(toolUse.input_json);

    switch (toolUse.tool_name) {
      case "str-replace-editor":
        if (input.path) {
          summary.filesModified.add(input.path);
        }
        break;
      case "save-file":
        if (input.path) {
          summary.filesCreated.add(input.path);
        }
        break;
      case "remove-files":
        if (input.file_paths && Array.isArray(input.file_paths)) {
          for (const path of input.file_paths) {
            summary.filesDeleted.add(path);
          }
        }
        break;
      case "view":
        if (input.path) {
          summary.filesViewed.add(input.path);
        }
        break;
      case "launch-process":
        if (input.command) {
          summary.terminalCommands.add(input.command);
        }
        break;
      // Add more tool types as needed
    }
  } catch (e) {
    // If we can't parse the input JSON, skip this tool use
    console.warn("Failed to parse tool use input:", e);
  }
}

/**
 * Converts a Set to an Array with limiting and overflow indication
 */
function setToArrayWithLimit(
  set: Set<string>,
  numItemslimit: number,
  perItemCharsLimit: number,
  itemType: string = "files",
): string[] | null {
  if (set.size === 0) return null;

  // Sort for determinism
  const items = Array.from(set).sort((a, b) => a.localeCompare(b));

  // Truncate each item to the perItemCharsLimit by replacing middle with "..."
  const truncate = (s: string): string => truncateMiddle(s, perItemCharsLimit);

  if (items.length <= numItemslimit) {
    return items.map(truncate);
  }

  const limitedItems = items.slice(0, numItemslimit).map(truncate);
  const overflow = items.length - numItemslimit;
  limitedItems.push(`... ${overflow} more ${itemType}`);
  return limitedItems;
}

/**
 * Converts an abridged agent round to text representation
 */
export function abridgedAgentRoundToText(
  round: AbridgedAgentRound,
  params: AbridgedHistoryParams,
): string {
  // Apply character limits if provided
  let userMessage = round.userMessage;
  let agentResponse = round.agentFinalResponse;

  if (userMessage.length > params.userMessageCharsLimit) {
    userMessage = truncateMiddle(userMessage, params.userMessageCharsLimit);
  }
  if (agentResponse.length > params.agentResponseCharsLimit) {
    agentResponse = truncateMiddle(agentResponse, params.agentResponseCharsLimit);
  }

  const hasActions =
    round.agentActionsSummary.filesModified.size > 0 ||
    round.agentActionsSummary.filesCreated.size > 0 ||
    round.agentActionsSummary.filesDeleted.size > 0 ||
    round.agentActionsSummary.filesViewed.size > 0 ||
    round.agentActionsSummary.terminalCommands.size > 0;

  // Apply limits and prepare data for Handlebars template
  const templateData = {
    userMessage,
    agentResponse: agentResponse && agentResponse.trim() !== "" ? agentResponse : null,
    hasActions,
    filesModified: setToArrayWithLimit(
      round.agentActionsSummary.filesModified,
      params.numFilesModifiedLimit,
      params.actionCharsLimit,
    ),
    filesCreated: setToArrayWithLimit(
      round.agentActionsSummary.filesCreated,
      params.numFilesCreatedLimit,
      params.actionCharsLimit,
    ),
    filesDeleted: setToArrayWithLimit(
      round.agentActionsSummary.filesDeleted,
      params.numFilesDeletedLimit,
      params.actionCharsLimit,
    ),
    filesViewed: setToArrayWithLimit(
      round.agentActionsSummary.filesViewed,
      params.numFilesViewedLimit,
      params.actionCharsLimit,
    ),
    terminalCommands: setToArrayWithLimit(
      round.agentActionsSummary.terminalCommands,
      params.numTerminalCommandsLimit,
      params.actionCharsLimit,
      "commands",
    ),
    wasInterrupted: round.wasInterrupted,
    continues: round.continues,
  };

  return agentRoundTemplate(templateData);
}

function extractUserMessage(exchange: ExchangeWithStatus): string {
  let userMessage = exchange.request_message || "";
  // Check if user attached files/images/documents in structured_request_nodes
  if (
    exchange.structured_request_nodes?.some((node) => node.image_node || node.image_id_node) ??
    false
  ) {
    userMessage += "\n[User attached image]";
  }
  if (
    exchange.structured_request_nodes?.some((node) => node.file_node || node.file_id_node) ??
    false
  ) {
    userMessage += "\n[User attached document]";
  }
  return userMessage;
}
