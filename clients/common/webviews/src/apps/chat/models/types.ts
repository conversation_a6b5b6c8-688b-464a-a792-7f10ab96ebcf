import type { IMentionable } from "$common-webviews/src/common/components/inputs/types";

import { type EloModelConfiguration } from "$vscode/src/webview-panels/preference-panel-types";
import {
  type FileDetails,
  type SmartPastePrecomputeMode,
  type UserTier,
} from "$vscode/src/webview-providers/webview-messages";
import type { SyncingStatus } from "$vscode/src/workspace/types";
import { type PersonaType } from "@augment-internal/sidecar-libs/src/chat/chat-types";
import type { ModelInfoRegistryEntry } from "@augment-internal/sidecar-libs/src/api/types";
import type { IQualifiedPathName } from "@augment-internal/sidecar-libs/src/workspace/workspace-types";
import type { ChatItem, ExchangeWithStatus } from "../types/chat-message";
import type { FeedbackState } from "../types/feedback-state";
import { type ToolUseState } from "../types/tool-use-state";

/**
 * The below types describe the interface of a conversation
 */
export interface IConversation extends ISerializedConversation {
  selectedModelId?: string | null | undefined;
}

export interface ISerializedConversation {
  id: string;
  name?: string;
  createdAtIso: string;
  lastInteractedAtIso: string;
  chatHistory: ChatItem[];
  feedbackStates: { [requestId: string]: FeedbackState };
  toolUseStates?: { [toolUseId: string]: ToolUseState };
  draftExchange?: ExchangeWithStatus;
  draftActiveContextIds?: string[];
  requestIds: string[];
  isPinned?: boolean;
  // A previously saved url for this conversation. Note, saving the conversation
  // is idempotent in the backend, so maintaing this state in the client is an
  // optimization.
  lastUrl?: string;
  isShareable?: boolean;
  extraData?: Record<string, unknown>;
  personaType?: PersonaType;
  // The UUID of the root task associated with this conversation
  rootTaskUuid?: string;
}

export interface IAgentConversation extends IConversation {
  extraData: {
    isAgentConversation: true;
    hasDirtyEdits?: boolean;
    hasAgentOnboarded?: boolean;
    baselineTimestamp?: number;
    hasTitleGenerated?: boolean;
  };
}

/**
 * Returns true if the conversation is an IDE agent conversation (not remote)
 */
export function isAgentConversation(convo: IConversation): convo is IAgentConversation {
  return (convo as IAgentConversation).extraData?.isAgentConversation === true;
}

/**
 * The below types describe different forms of special context:
 * - Source folders
 * - Files
 * - File selections
 *
 * Each type is stored separately in the SpecialContextInputModel, and this info
 * can be pulled from multiple sources. Generally, we rely on each source to report
 * its own context to the context-model -- the context model has no awareness of the
 * sources, and does not care either. This means we can aggregate context from many
 * different places into the same model.
 */
export enum ContextStatus {
  active,
  inactive,
}

/**
 * The base interface for all context types.
 */
export interface IContextInfo {
  status: ContextStatus;
  referenceCount: number;
  pinned?: boolean;
  showWarning?: boolean;
}

// Context for selected code in files
// eslint-disable-next-line @typescript-eslint/no-empty-interface
export interface IFileSelectionInfo extends FileDetails {}

export interface FindFilesFn {
  (query: IQualifiedPathName, limit?: number): Promise<FileDetails[]>;
}

export interface GetSuggestionsFn<T extends IMentionable> {
  (query: string, isAgentMode?: boolean): Promise<T[]>;
}

export interface IChatFlags {
  enableEditableHistory: boolean;
  enablePreferenceCollection: boolean;
  enableRetrievalDataCollection: boolean;
  enableDebugFeatures: boolean;
  enableConversationDebugUtils: boolean;
  enableRichTextHistory: boolean;
  enableAgentSwarmMode?: boolean;
  modelDisplayNameToId: { [model: string]: string | null };
  fullFeatured: boolean;
  enableExternalSourcesInChat: boolean;
  smallSyncThreshold: number;
  bigSyncThreshold: number;
  enableSmartPaste: boolean;
  enableDirectApply: boolean;
  summaryTitles: boolean;
  suggestedEditsAvailable: boolean;
  enableShareService: boolean;
  maxTrackableFileCount: number;
  enableSources: boolean;
  enableChatMermaidDiagrams: boolean;
  smartPastePrecomputeMode: SmartPastePrecomputeMode;
  useNewThreadsMenu: boolean;
  enableChatMermaidDiagramsMinVersion: boolean;
  enablePromptEnhancer: boolean;
  memoryClassificationOnFirstToken: boolean;
  enableDesignSystemRichTextEditor: boolean;
  idleNewSessionNotificationTimeoutMs: number;
  idleNewSessionMessageTimeoutMs: number;
  enableChatMultimodal: boolean;
  enableAgentMode?: boolean;
  enableAgentAutoMode?: boolean;
  enableRichCheckpointInfo?: boolean;
  agentMemoriesFilePathName?: IQualifiedPathName | undefined;
  conversationHistorySizeThresholdBytes?: number;
  enableTaskList?: boolean;
  userTier?: UserTier;
  eloModelConfiguration?: EloModelConfiguration;
  truncateChatHistory?: boolean;
  memoriesParams?: { [key: string]: string | number | boolean };
  enableBackgroundAgents?: boolean;
  enableNewThreadsList?: boolean;
  enableVirtualizedMessageList?: boolean;
  customPersonalityPrompts?: {
    agent?: string;
    prototyper?: string;
    brainstorm?: string;
    reviewer?: string;
  };
  enablePersonalities?: boolean;
  enableRules?: boolean;
  enableGenerateCommitMessage?: boolean;
  enableModelRegistry?: boolean;
  modelRegistry?: Record<string, string>;
  modelInfoRegistry?: Record<string, ModelInfoRegistryEntry>;
  agentChatModel?: string;
  clientAnnouncement?: string;
  useHistorySummary?: boolean;
  historySummaryParams?: string;
  enableExchangeStorage?: boolean;
  enableToolUseStateStorage?: boolean;
  retryChatStreamTimeouts?: boolean;
  enableCommitIndexing?: boolean;
  enableMemoryRetrieval?: boolean;
  enableAgentTabs?: boolean;
  isVscodeVersionOutdated?: boolean;
  vscodeMinVersion?: string;
  enableGroupedTools?: boolean;
  remoteAgentsResumeHintAvailableTtlDays?: number;
  enableParallelTools?: boolean;
  enableAgentGitTracker?: boolean;
}

export interface WorkspaceSyncingOverview {
  status: SyncingStatus;
  totalFiles: number;
  syncedCount: number;
  backlogSize: number;
  newlyTrackedFolders: string[];
}

export enum ChatInputType {
  normal = "Normal",
}

export interface StreamStateHandlerI {
  handleChunk(chunk: Partial<ExchangeWithStatus>): Partial<ExchangeWithStatus>;
  handleComplete(): AsyncGenerator<Partial<ExchangeWithStatus>>;
}
