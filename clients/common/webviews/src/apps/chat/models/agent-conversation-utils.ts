import {
  type ChatR<PERSON>ultN<PERSON>,
  ChatResultNodeType,
  type ChatResultToolUse,
} from "@augment-internal/sidecar-libs/src/chat/chat-types";
import {
  AgentExchangeStatus,
  type ChatItem,
  ExchangeStatus,
  type ExchangeWithStatus,
  isChatItemExchangeWithStatus,
} from "../types/chat-message";
import { ToolUsePhase } from "../types/tool-use-state";
import type { ConversationModel } from "./conversation-model";
import { onlyDisplayableToolNodes } from "../utils/tool-utils";
import { type ChatFlagsModel } from "./chat-flags-model";

/**
 * Checks if a chat item represents a user message
 */
export const isUserMessage = (chatItem: ChatItem): boolean => {
  return chatItem.chatItemType === undefined;
};

// Memory-related functions have been moved to MemoryUtils class

/**
 * Computes the current status of the agent turn.
 * Used to determine the state of the current agent interaction.
 *
 * @returns The current AgentExchangeStatus
 */
export const computeAgentExchangeStatus = (
  conversationModel: ConversationModel,
  flagsModel: ChatFlagsModel,
): AgentExchangeStatus => {
  const lastExchange = conversationModel.chatHistory.at(-1);

  // If no last exchange or not a proper exchange with status, agent is not running
  if (!lastExchange || !isChatItemExchangeWithStatus(lastExchange)) {
    return AgentExchangeStatus.notRunning;
  }

  // Check if exchange itself is complete (success or failed)
  const isExchangeComplete =
    lastExchange.status === ExchangeStatus.success ||
    lastExchange.status === ExchangeStatus.failed ||
    lastExchange.status === ExchangeStatus.cancelled;
  if (!isExchangeComplete) {
    return AgentExchangeStatus.running;
  }

  // Beyond this point, normal exchanges should already be complete. So everything below should be
  // checking tool uses

  // Check the last exchange for its status and tool use state
  // General logic is as follows:
  // - If there is no tool use at all, this is likely a bug. We'll just default to reporting not running,
  //     since no tool uses in a returned response block should mean the agent is not running.
  // - If there is a tool use, we check the tool use state to determine the agent status.
  // - If the tool use is runnable, the agent is awaiting user action.
  // - If the tool use is aborted, the agent is not running. This happens when cancellations occur but the
  //     agent is not expected to continue the conversation.
  // - If the tool use is cancelled, the agent is still running -- it is expected to continue the conversation.
  // - If the tool use is anything else, the agent is still running.

  const toolUseNodes =
    lastExchange.structured_output_nodes?.filter(
      // eslint-disable-next-line @typescript-eslint/naming-convention
      (node): node is ChatResultNode & { tool_use: ChatResultToolUse } =>
        node.type === ChatResultNodeType.TOOL_USE && !!node.tool_use,
    ) ?? [];

  // eslint-disable-next-line @typescript-eslint/naming-convention
  let lastToolUseNode: (ChatResultNode & { tool_use: ChatResultToolUse }) | undefined;
  if (flagsModel.enableParallelTools) {
    const displayableNodes = onlyDisplayableToolNodes(
      lastExchange.request_id,
      toolUseNodes,
      conversationModel,
      // eslint-disable-next-line @typescript-eslint/naming-convention
    );
    lastToolUseNode = displayableNodes.at(-1) as ChatResultNode & { tool_use: ChatResultToolUse };

    // If onlyDisplayableToolNodes filtered out all nodes but we have tool use nodes,
    // fall back to the actual last tool use node to prevent incorrect state transitions
    if (!lastToolUseNode && toolUseNodes.length > 0) {
      lastToolUseNode = toolUseNodes.at(-1) as ChatResultNode & { tool_use: ChatResultToolUse };
    }
  } else {
    lastToolUseNode = toolUseNodes.at(-1);
  }

  if (!lastToolUseNode || !lastToolUseNode.tool_use) {
    // If there is no tool use node, normal exchange status should have already returned.
    // If we somehow enter this block, we are not running -- likely, we cancelled or errored somewhere.
    return AgentExchangeStatus.notRunning;
  }

  // Check the status based on the last tool use node in our last exchange
  const toolState = conversationModel.getToolUseState(
    lastExchange.request_id,
    lastToolUseNode.tool_use.tool_use_id,
  );

  switch (toolState.phase) {
    case ToolUsePhase.runnable:
      return AgentExchangeStatus.awaitingUserAction;
    case ToolUsePhase.cancelled:
      return AgentExchangeStatus.notRunning;
    default:
      return AgentExchangeStatus.running;
  }
};

/**
 * Checks if a chat item is a user exchange (has a request message)
 *
 * @param chatItem The chat item to check
 * @returns True if the chat item is a user exchange
 */
export const isUserExchange = (chatItem: ChatItem) => {
  return isChatItemExchangeWithStatus(chatItem) && !!chatItem.request_message;
};

/**
 * Filters a chat history to only include user exchanges.
 *
 * In an agent conversation, this would exclude tool calls so it would not be a
 * complete representation of the chat history. But this can be useful to count
 * how many times the user has sent a message.
 *
 * @param chatHistory The chat history to filter
 * @returns Array of exchanges that are user exchanges
 */
export const filterByUserExchange = (chatHistory: ChatItem[]) => {
  return chatHistory.filter((exchange): exchange is ExchangeWithStatus => isUserExchange(exchange));
};

/**
 * Gets the last exchange with a message (i.e. a user message)
 * This is used to append the computed memory to the last user message, as opposed to automated messages
 * sent as tool responses, which we send with user messages in order to get tool responses
 * over to the agent.
 *
 * @param conversationModel
 * @returns
 */
export const getLastUserExchange = (
  conversationModel: ConversationModel,
): ExchangeWithStatus | undefined => {
  return conversationModel.chatHistory.findLast((exchange): exchange is ExchangeWithStatus =>
    isUserExchange(exchange),
  );
};

/**
 * Gets all exchanges since the last user message (inclusive)
 * This is used to determine if the current turn is complete
 * by checking if all tool uses are complete
 *
 * @param conversationModel
 * @returns
 */
export const getCurrentAgenticTurnExchanges = (
  conversationModel: ConversationModel,
  filter?: (exchange: ExchangeWithStatus) => boolean,
): ExchangeWithStatus[] => {
  const lastUserExchange = getLastUserExchange(conversationModel);
  if (!lastUserExchange?.request_id) return [];
  const predicate = (exchange: ChatItem): exchange is ExchangeWithStatus => {
    return isChatItemExchangeWithStatus(exchange) && (!filter || filter(exchange));
  };
  const exchanges = conversationModel
    .historyFrom(lastUserExchange.request_id, true)
    .filter(predicate);
  return exchanges;
};

/**
 * Cancels the last runnable tool use in the current agent turn, if any.
 *
 * @param conversationModel - The current conversation model
 * @returns {boolean} True if a tool use was cancelled, false otherwise
 */
export const cancelLastRunnableToolUse = (conversationModel: ConversationModel): boolean => {
  const lastExchange = conversationModel.chatHistory.at(-1);
  if (!lastExchange?.request_id || !isChatItemExchangeWithStatus(lastExchange)) {
    return false;
  }
  const toolUseNodes =
    lastExchange.structured_output_nodes?.filter(
      (node) => node.type === ChatResultNodeType.TOOL_USE,
    ) ?? [];

  for (const node of toolUseNodes) {
    if (!node.tool_use) continue;
    const toolState = conversationModel.getToolUseState(
      lastExchange.request_id,
      node.tool_use.tool_use_id,
    );
    if (toolState.phase === ToolUsePhase.runnable) {
      conversationModel.updateToolUseState({
        requestId: lastExchange.request_id,
        toolUseId: node.tool_use.tool_use_id,
        phase: ToolUsePhase.cancelled,
      });
      return true;
    }
  }
  return false;
};
