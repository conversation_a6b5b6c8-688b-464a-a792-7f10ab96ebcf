<script lang="ts">
  import "./chat.css";

  import { host } from "$common-webviews/src/common/hosts/host";
  import {
    MessageBroker,
    type MessageConsumer,
  } from "$common-webviews/src/common/utils/message-broker";
  import { createGlobalKeybindingsAction } from "$common-webviews/src/common/actions/globalKeybindingsAction";
  import type RichTextEditorRoot from "$common-webviews/src/design-system/components/RichTextEditorAugment/Root.svelte";
  import {
    WebViewMessageType,
    type WebViewMessage,
  } from "$vscode/src/webview-providers/webview-messages";
  import { onMount, setContext } from "svelte";

  import { config as hotkeyConfig } from "./chat-keybindings";
  import { ActionsModel } from "./models/actions-model";
  import { ChatModel } from "./models/chat-model";
  import { SpecialContextInputModel } from "./models/context-model";
  import { OnboardingWorkspaceModel } from "./models/onboarding-workspace-model";
  import { type IChatFlags, type IConversation } from "./models/types";

  import { RemoteAgentStatus } from "@augment-internal/sidecar-libs/src/remote-agent/remote-agent-types";
  import {
    SHARED_AGENT_STORE_CONTEXT_KEY,
    SHARED_AGENT_STORE_NAME,
    validateChatHomeState,
    type ChatHomeWebviewState,
  } from "$vscode/src/webview-panels/remote-agents/common-webview-store";
  import { ModelRegistry } from "./models/model-registry";
  import { formatModelRegistryEntries } from "./models/model-registry-utils";
  import { writable } from "svelte/store";
  import { SharedWebviewStoreModel } from "../../common/models/shared-webview-store-model";
  import GenerateSetupScriptButton from "./components/GenerateSetupScriptButton.svelte";
  import { GitReferenceModel } from "../remote-agent-manager/models/git-reference-model";
  import {
    getMessageRenderOptions,
    SELECTED_TURN_INDEX_CONTEXT_KEY,
  } from "../remote-agent-manager/models/message-render-options";
  import { RemoteAgentDiffOpsModel } from "../remote-agent-manager/models/ra-diff-ops-model";
  import { RemoteAgentsModel } from "../remote-agent-manager/models/remote-agents-model";
  import { createMultiRepoGitContext } from "./components/git-agent-edit/context";
  import { setChatModel } from "./contexts/chat-model-context";
  import { setRulesModel } from "./contexts/rules-model-context";
  import ActionCard from "./components/action-cards/ActionCard.svelte";
  import ClientAnnouncementCard from "./components/action-cards/ClientAnnouncementCard.svelte";
  import ChatInput from "./components/ChatInput/ChatInput.svelte";
  import GitCommitInput from "./components/git-agent-edit/git-input/GitCommitInput.svelte";
  import ChatLayout from "./components/ChatLayout.svelte";
  import {
    ChatThreadBodyTab,
    createChatThreadBodyModel,
    ChatThreadBodyModel,
  } from "./components/chat-thread-body/chat-thread-body-model";
  import { createChatThreadBodyState } from "./components/chat-thread-body/chat-thread-body-state";
  import MessageSection from "./components/conversation/MessageSection.svelte";
  import ChatDivider from "./components/DraggableChatDivider.svelte";
  import RemoteAgentActions from "./components/RemoteAgentActions.svelte";
  import SetupScriptSaveBar from "./components/SetupScriptSaveBar.svelte";
  import StatusBars from "./components/status-bar/StatusBars.svelte";
  import SwappablePanel from "./components/SwappablePanel.svelte";
  import { ChatThreadBody } from "./components/chat-thread-body";
  import ThreadHeader from "./components/ThreadHeader.svelte";

  import { setConversationNavigationItemsContext } from "./components/conversation/navigation";
  import { AgentConversationModel } from "./models/agent-conversation-model";
  import { ChatModeModel } from "./models/chat-mode-model";
  import { CheckpointStore } from "./models/checkpoint-store";
  import { SlashCommandModel } from "./models/slash-command-model";
  import { SubscriptionModel, type SubscriptionInfo } from "./models/subscription-model";
  import { CurrentConversationTaskStore } from "./models/task-store";
  import { ToolsWebviewModel } from "./models/tools-webview-model";
  import { NotificationBannerConsumer } from "./models/notification-banner-consumer";
  import {
    subscribeRemoteAgentsModelToSharedStore,
    subscribeSharedStoreToRemoteAgentsModel,
  } from "./utils/subscribe-store";

  import FileSizeWarning from "./components/warnings/FileSizeWarning.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import { SoundModel } from "../settings/models/sound-model";
  import RemoteAgentSetup from "../remote-agent-manager/chat/components/RemoteAgentSetup.svelte";
  import ChatInputWarnings from "./components/warnings/ChatInputWarnings.svelte";
  import ChatHeader from "./components/ChatHeader.svelte";
  import FullSizeOverlayAugment from "$common-webviews/src/design-system/components/FullSizeOverlayAugment/FullSizeOverlayAugment.svelte";
  import ThreadsPanel from "$common-webviews/src/apps/chat/components/threads/ThreadsPanel.svelte";
  import { setExtensionClientContext } from "./extension-client-context";
  import RegisterCommand from "$common-webviews/src/common/components/keybindings/RegisterCommand.svelte";

  export let initialConversation: IConversation | undefined = undefined;
  export let initialFlags: Partial<IChatFlags> | undefined = undefined;
  export let initialSubscriptionInfo: SubscriptionInfo | undefined = undefined;
  export let initialSubscriptionDismissed: boolean | undefined = undefined;

  const msgBroker = new MessageBroker(host);
  let contextModel = new SpecialContextInputModel();
  let actionsModel = new ActionsModel();
  let notificationBannerConsumer = new NotificationBannerConsumer();
  msgBroker.registerConsumer(actionsModel);
  msgBroker.registerConsumer(notificationBannerConsumer);
  let agentConversationModel: AgentConversationModel;

  let chatModel = new ChatModel(msgBroker, host, contextModel, {
    forceAgentConversation: true,
    initialConversation,
    initialFlags,
    onLoaded: () => {
      // Load in the modelRegistry flags on Chat load to populate the ModelPicker state
      const modelRegistryEntries = formatModelRegistryEntries($chatModel.flags);
      modelRegistry.registerModels(modelRegistryEntries);
    },
  });
  msgBroker.registerConsumer(chatModel.rulesModel);
  const soundModel = new SoundModel(msgBroker);
  setContext(SoundModel.key, soundModel);
  // Create git reference model first
  const gitRefModel = new GitReferenceModel(msgBroker);

  // Always create multi-repo git context (sets itself in context internally)
  const multiRepoGitContext = createMultiRepoGitContext(chatModel.extensionClient, chatModel.flags);

  // Remote agents - create this first so it can be passed to ToolsWebviewModel
  const remoteAgentsModel = new RemoteAgentsModel({
    msgBroker,
    isActive: false,
    flagsModel: chatModel.flags,
    host,
    gitRefModel,
  });

  let toolsWebviewModel = new ToolsWebviewModel(
    chatModel.currentConversationModel,
    chatModel.extensionClient,
    chatModel,
    soundModel,
    remoteAgentsModel,
  );
  const checkpointStore = new CheckpointStore(
    chatModel.flags,
    chatModel.currentConversationModel,
    chatModel.extensionClient,
  );

  msgBroker.registerConsumer(checkpointStore);
  msgBroker.registerConsumer(chatModel);
  msgBroker.registerConsumer(chatModel.specialContextInputModel);

  agentConversationModel = new AgentConversationModel(
    chatModel,
    toolsWebviewModel,
    checkpointStore,
    soundModel,
  );
  const taskStore = new CurrentConversationTaskStore(
    chatModel,
    chatModel.extensionClient,
    chatModel.currentConversationModel,
    agentConversationModel,
  );
  msgBroker.registerConsumer(taskStore);
  const diffOperationsModel = new RemoteAgentDiffOpsModel(msgBroker);
  msgBroker.registerConsumer(remoteAgentsModel);
  setContext(RemoteAgentsModel.key, remoteAgentsModel);
  setContext(GitReferenceModel.key, gitRefModel);
  setContext(RemoteAgentDiffOpsModel.key, diffOperationsModel);
  setContext(CurrentConversationTaskStore.key, taskStore);
  setContext(SoundModel.key, soundModel);

  const selectedTurnIndex = writable(-1);
  setContext(SELECTED_TURN_INDEX_CONTEXT_KEY, selectedTurnIndex);

  const initialSharedState: ChatHomeWebviewState = {
    agentOverviews: [],
    selectedAgentId: undefined,
    activeWebviews: ["chat"], // Start with chat as active
    pinnedAgents: {},
  };
  const sharedWebviewStore = new SharedWebviewStoreModel<ChatHomeWebviewState>(
    msgBroker,
    initialSharedState,
    validateChatHomeState,
    SHARED_AGENT_STORE_NAME,
  );
  msgBroker.registerConsumer(sharedWebviewStore);
  setContext(SHARED_AGENT_STORE_CONTEXT_KEY, sharedWebviewStore);

  const modelRegistry = new ModelRegistry(chatModel);
  // Create the ChatModeModel first
  const chatModeModel = new ChatModeModel(chatModel, agentConversationModel, remoteAgentsModel);
  msgBroker.registerConsumer(chatModeModel);
  setContext(ChatModeModel.key, chatModeModel);

  // Set the ChatModeModel in the ChatModel
  chatModel.setChatModeModel(chatModeModel);

  // Subscribe to remoteAgentsModel changes to update the shared store
  $: subscribeSharedStoreToRemoteAgentsModel(
    sharedWebviewStore,
    $remoteAgentsModel,
    initialSharedState,
  );
  let prevStoreState = sharedWebviewStore.state;
  // Subscribe to shared store changes to update the remote agents model
  $: subscribeRemoteAgentsModelToSharedStore(
    prevStoreState,
    $sharedWebviewStore,
    $remoteAgentsModel,
    chatModel,
    chatModeModel,
  );

  const slashCommandModel = new SlashCommandModel(chatModel, agentConversationModel, chatModeModel);
  msgBroker.registerConsumer(slashCommandModel);

  // Create subscription model for subscription status
  const subscriptionModel = new SubscriptionModel(
    chatModel.extensionClient,
    initialSubscriptionInfo,
    initialSubscriptionDismissed,
  );

  // TODO (mattgauntseo): Move flags out of chat model
  const onboardingWorkspaceModel = new OnboardingWorkspaceModel(chatModel, chatModel.flags);
  msgBroker.registerConsumer(onboardingWorkspaceModel);

  // Make the chat model available to subcomponents
  setChatModel(chatModel);
  setRulesModel(chatModel.rulesModel);
  setContext("agentConversationModel", agentConversationModel);
  setContext("onboardingWorkspaceModel", onboardingWorkspaceModel);
  setContext("toolsWebviewModel", toolsWebviewModel);
  setContext("checkpointStore", checkpointStore);
  setContext("slashCommandModel", slashCommandModel);
  setContext("modelRegistry", modelRegistry);
  setExtensionClientContext(chatModel.extensionClient);
  setContext(SubscriptionModel.key, subscriptionModel);

  // UI visibility flags
  $: flagsLoaded = $chatModel.flagsLoaded && $flagsModel.fullFeatured;
  $: shouldShowThreadsList = flagsLoaded && !$remoteAgentsModel.isRemoteAgentSshWindow;
  $: shouldShowThreadHeader = flagsLoaded;

  $: messageRenderOptions = getMessageRenderOptions($remoteAgentsModel);

  $: shouldShowStatusBar = !messageRenderOptions.doHideStatusBars;
  $: isRemoteAgentStarting =
    $remoteAgentsModel.isCreatingAgent ||
    ($remoteAgentsModel.isActive &&
      $remoteAgentsModel.currentAgent?.status === RemoteAgentStatus.agentStarting);

  $: inputPlaceholder = (() => {
    if (isRemoteAgentStarting) {
      return "Remote Agent is starting";
    }
    if ($remoteAgentsModel.isActive) {
      if ($remoteAgentsModel.currentAgentId) {
        return "Ask Remote Agent to edit code, run tests, create a pull request";
      } else {
        return "Fix this bug, tackle this ticket, review comments in my pull request";
      }
    }
    if ($isCurrConversationAgentic) {
      return "Ask or instruct Augment Agent";
    }
    return "Ask or instruct Augment";
  })();

  // Set up reactive models
  const flagsModel = $chatModel.flags;
  setContext("flagsModel", flagsModel);

  // External state for thread body model (now created via util factory with strong typing)
  const threadBodyState = createChatThreadBodyState({
    isCurrConversationAgentic: agentConversationModel.isCurrConversationAgentic,
    remoteAgentsModel: remoteAgentsModel,
    canShowTaskList: taskStore.canShowTaskList,
    targetCheckpointSummary: checkpointStore.targetCheckpointSummary,
    totalCheckpointCount: checkpointStore.totalCheckpointCount,
  });

  // Create thread body model at Chat level for global access
  const threadBodyModel = createChatThreadBodyModel(
    chatModel.flags,
    threadBodyState,
    agentConversationModel.isCurrConversationAgentic,
  );
  ChatThreadBodyModel.setInContext(threadBodyModel);

  $: hasActions = $actionsModel.actions.length > 0;
  const { isCurrConversationAgentic } = agentConversationModel;

  $: shouldShowRemoteAgentActions =
    $remoteAgentsModel.isActive &&
    $remoteAgentsModel.currentAgent &&
    !$remoteAgentsModel.currentAgent?.is_setup_script_agent;

  // Add ResetOnboardingHandler class before onMount
  class ResetOnboardingHandler implements MessageConsumer {
    constructor(private _agentConversationModel: AgentConversationModel) {}

    handleMessageFromExtension(e: MessageEvent<WebViewMessage>): boolean {
      if (e.data.type === WebViewMessageType.resetAgentOnboarding) {
        void this._agentConversationModel.resetOnboarding();
        return true;
      }
      return false;
    }
  }

  // Register the handler with the message broker
  msgBroker.registerConsumer(new ResetOnboardingHandler(agentConversationModel));

  let previsRemoteAgentSshWindow = false;
  let prevRemoteAgentId: string | undefined = undefined;

  // Handle git commit
  function handleGitCommit(message: string) {
    const activeController = multiRepoGitContext.controller.getActiveRepositoryController();
    if (activeController) {
      void activeController.commit(message);
    }
  }

  const handleisRemoteAgentSshWindow = (remoteAgentsModel: RemoteAgentsModel) => {
    const isRemoteAgentSshWindow = remoteAgentsModel.isRemoteAgentSshWindow;
    const remoteAgentId = remoteAgentsModel.remoteAgentSshWindowId;

    // If the state didn't change, do nothing
    if (
      previsRemoteAgentSshWindow === isRemoteAgentSshWindow &&
      prevRemoteAgentId === remoteAgentId
    ) {
      return;
    }
    previsRemoteAgentSshWindow = isRemoteAgentSshWindow;
    prevRemoteAgentId = remoteAgentId;

    // If we're in a remote agent window, set the chat to remote agent mode
    if (isRemoteAgentSshWindow && remoteAgentId) {
      chatModeModel.setToRemoteAgent(remoteAgentId);
      remoteAgentsModel.setCurrentAgent(remoteAgentId);
      remoteAgentsModel.setIsActive(true);
    }
  };

  // If the flags change, handle the remote agent window state
  $: handleisRemoteAgentSshWindow($remoteAgentsModel);

  // ==== Lifecycle ====
  // When the element is first rendered and goes from undefined => an HTMLElement,
  // we need to focus it so that the user can start typing
  onMount(() => {
    // If we're in a remote agent window, set the chat to remote agent mode
    if ($remoteAgentsModel.isRemoteAgentSshWindow && $remoteAgentsModel.remoteAgentSshWindowId) {
      // Force the chat to remote agent mode
      chatModeModel.setToRemoteAgent($remoteAgentsModel.remoteAgentSshWindowId);

      // Set the current agent to the remote agent ID
      remoteAgentsModel.setCurrentAgent($remoteAgentsModel.remoteAgentSshWindowId);

      // Make sure the remote agents model is active
      remoteAgentsModel.setIsActive(true);
    }

    const disposables = [
      toolsWebviewModel.dispose,
      remoteAgentsModel.dispose,
      subscriptionModel.dispose,
      soundModel?.dispose,
    ];

    return () => {
      disposables.forEach((d) => d?.());
    };
  });

  let inputArea: HTMLElement;

  $: isTallInput = $remoteAgentsModel.isActive || isInRemoteAgentSetupMode;
  $: isShrunk =
    $remoteAgentsModel.isActive &&
    $remoteAgentsModel.currentAgentId &&
    !$chatModel.currentConversationModel.hasDraft;

  let richTextEditorRoot: RichTextEditorRoot | undefined = undefined;

  $: showThreadBody =
    $flagsModel.enableAgentMode &&
    $isCurrConversationAgentic &&
    $flagsModel.enableAgentTabs &&
    !$remoteAgentsModel.isActive;

  // Track rich text editor focus state for CSS class application
  // Ensure the isFocused is a function before calling it due to weird rendering behavior
  // when the richTextEditor is unrendered
  $: isRichTextEditorFocused = richTextEditorRoot?.isFocused?.() || writable(false);
  $: isInRemoteAgentSetupMode = $remoteAgentsModel.isActive && !$remoteAgentsModel.currentAgentId;
  $: shouldShowChatDivider = true && !showThreadBody;

  // Get rich text content for file warnings
  $: draftExchange = $chatModel.currentConversationModel.draftExchange;
  $: richTextContent = draftExchange?.rich_text_json_repr;

  // Get active tab from thread body model
  $: activeTab = threadBodyModel.activeTab;

  // Show git commit input when on git edits tab (always show)
  $: shouldShowGitCommitInput =
    chatModel.flags.enableAgentGitTracker &&
    $activeTab === ChatThreadBodyTab.agentGitEdits &&
    $isCurrConversationAgentic &&
    !$remoteAgentsModel.isActive;

  $: enableDebugFeatures = $chatModel?.flags?.enableDebugFeatures ?? false;
  setConversationNavigationItemsContext();

  let threadListDialog: FullSizeOverlayAugment;
</script>

<svelte:window
  on:message={msgBroker.onMessageFromExtension}
  on:beforeunload={() => {
    $chatModel.saveImmediate();
  }}
  use:createGlobalKeybindingsAction={{
    config: hotkeyConfig,
    context: () => {
      return {
        chatModel,
        remoteAgentsModel,
        chatModeModel,
        richTextEditorRoot,
        openThreadsDialog: () => threadListDialog.toggleModal(),
      };
    },
  }}
/>

<FullSizeOverlayAugment bind:this={threadListDialog} animationDirection="left">
  <ThreadsPanel {chatModel} onClose={() => threadListDialog.closeModal()} />
</FullSizeOverlayAugment>

<div class="chat">
  <ChatLayout>
    <!-- Header Area -->
    <svelte:fragment slot="header-area">
      <div class="l-chat-header">
        <ChatHeader onOpenThreads={() => threadListDialog.showModal()} {shouldShowThreadsList} />
        {#if shouldShowThreadHeader}
          <ThreadHeader />
        {/if}
        {#if enableDebugFeatures}
          <RegisterCommand name="toggleThreadsPanel" handler={threadListDialog.toggleModal} />
        {/if}
      </div>
    </svelte:fragment>

    <!-- Message List -->
    <svelte:fragment slot="message-area">
      {#if showThreadBody}
        <!-- ChatThreadBody takes over the entire message area when tabs are enabled -->
        <ChatThreadBody>
          <ChatDivider
            slot="chat-divider"
            bind:inputArea
            on:dragEnd={() => {
              if (isShrunk) {
                // focus the input to keep it tall
                richTextEditorRoot?.requestFocus();
              }
            }}
          />
        </ChatThreadBody>
      {:else}
        <!-- Traditional message section when tabs are disabled -->
        <MessageSection {chatModel} {onboardingWorkspaceModel} {remoteAgentsModel} />
      {/if}
    </svelte:fragment>
    <svelte:fragment slot="actions-area">
      {#if shouldShowRemoteAgentActions}
        <RemoteAgentActions />
      {:else if shouldShowStatusBar}
        <div class="l-chat-actions-area">
          <StatusBars {contextModel} />
          <ClientAnnouncementCard />
          {#if $flagsModel.enableAgentMode && $isCurrConversationAgentic && !$flagsModel.enableAgentTabs}
            <SwappablePanel />
          {/if}
          {#if hasActions}
            <ActionCard card={$actionsModel.currentCard} flagsModel={$flagsModel} />
          {/if}
        </div>
      {/if}
      <FileSizeWarning {richTextContent} />
    </svelte:fragment>

    <!-- Divider Area -->
    <svelte:fragment slot="divider-area">
      {#if shouldShowChatDivider}
        <ChatDivider
          bind:inputArea
          on:dragEnd={() => {
            if (isShrunk) {
              // focus the input to keep it tall
              richTextEditorRoot?.requestFocus();
            }
          }}
        />
      {/if}
    </svelte:fragment>

    <svelte:fragment slot="input-hint">
      {#if isInRemoteAgentSetupMode}
        <TextAugment size={1} class="c-chat-input-hint"
          >Kick off a remote agent to work in parallel, in an isolated environment that will keep
          running, even when you shut off your computer.</TextAugment
        >
      {/if}
    </svelte:fragment>

    <!-- Input Area -->
    <svelte:fragment slot="input-area">
      {#if shouldShowGitCommitInput}
        <div
          class="l-input-area-design-system"
          class:l-input-area-design-system--tall={isTallInput}
          class:l-input-area-design-system--shrunk={isShrunk}
          class:l-input-area-design-system--editor-focused={$isRichTextEditorFocused}
          class:l-input-area-design-system--has-banner={true}
          bind:this={inputArea}
        >
          <GitCommitInput
            onCommit={handleGitCommit}
            variant="solid"
            stagedFilesCount={0}
            isCommitting={false}
            canCommit={true}
            placeholder="Enter commit message..."
          />
        </div>
      {:else if $remoteAgentsModel.isActive && $remoteAgentsModel.currentAgent?.is_setup_script_agent && $remoteAgentsModel.currentConversation}
        <ChatInput editable hasSendButton={true}>
          <SetupScriptSaveBar slot="banner" />
        </ChatInput>
      {:else if isInRemoteAgentSetupMode}
        <div
          class="l-chat-setup-area l-input-area-agent-setup l-input-area-design-system"
          bind:this={inputArea}
        >
          <ChatInput editable hasSendButton={true}>
            <RemoteAgentSetup slot="banner" />
            <GenerateSetupScriptButton {remoteAgentsModel} slot="activeButton" />
          </ChatInput>
        </div>
      {:else if !isInRemoteAgentSetupMode}
        <div
          class="l-input-area-design-system"
          class:l-input-area-design-system--tall={isTallInput}
          class:l-input-area-design-system--shrunk={isShrunk}
          class:l-input-area-design-system--editor-focused={$isRichTextEditorFocused}
          class:l-input-area-design-system--has-banner={true}
          bind:this={inputArea}
        >
          <ChatInput placeholder={inputPlaceholder} bind:richTextEditorRoot>
            <svelte:fragment slot="banner">
              <ChatInputWarnings />
            </svelte:fragment>
          </ChatInput>
        </div>
      {/if}
    </svelte:fragment>
  </ChatLayout>
</div>

<style>
  .chat {
    display: flex;
    flex-direction: column;
    height: 100vh;
    overflow: hidden;
    max-width: var(--augment-chat-max-width);
    margin: auto;
  }

  :global(.c-chat-floating-button) {
    flex: 0 1 auto;
    width: fit-content;
  }

  .l-input-area-design-system {
    min-height: 7.5lh;
    max-height: 50vh;
    height: 100%;

    max-width: 100vw;
    transition:
      min-height 0.1s ease,
      max-height 0.1s ease,
      height 0.1s ease;
  }
  :global(.l-input-area-design-system.is-dragging) {
    transition: none;
  }

  .l-input-area-design-system--tall {
    height: clamp(5vh, 9lh, 15vh);
    flex: none;
    min-height: 2lh;
  }

  .l-input-area-agent-setup.l-input-area-design-system {
    min-height: 14lh;
  }

  /* Consolidated styles for shrunk and inactive input state */
  :global(
      .l-input-area-design-system--shrunk:not(.l-input-area-design-system--editor-focused):not(
          .is-dragging
        )
    ) {
    height: 4lh;
    min-height: 4lh;
    max-height: 0;
    height: 0;
    cursor: pointer;

    &:global(:has(.c-chat-input-banner)) {
      /* Ensure the banner and chat input are visible when shrunk */
      height: fit-content;
      max-height: fit-content;
    }

    /* Hide rich text editor overflow */
    & :global(.l-rich-text-editor-augment) {
      overflow: hidden;
    }

    & :global(.c-rewrite-prompt-button),
    & :global(.c-action-bar-context) {
      display: none;
    }

    /* Truncate placeholder text to one line with ellipsis */
    & :global(.tiptap p.is-editor-empty:first-child::before) {
      white-space: nowrap;
      position: absolute;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: calc(100% - 20px); /* Leave some space for padding */
      width: 100%;
      display: inline-block;
      float: none;
      height: 1.3em;
    }
  }

  .l-chat-actions-area {
    display: contents;
    flex-direction: column;
    gap: var(--ds-spacing-2);
  }
  .l-chat-header :global(svg) {
    --icon-size: var(--ds-icon-size-0);
    opacity: 1;
  }
</style>
