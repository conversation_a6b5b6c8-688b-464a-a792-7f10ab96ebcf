import { describe, test, expect, vi } from "vitest";
import { ChatStreamWithRetry } from "./chat-stream-with-retry";
import { ExchangeStatus, SeenState } from "./types/chat-message";

describe("ChatStreamWithRetry", () => {
  test("should retry on retriable errors and eventually succeed", async () => {
    // Mock data
    const requestId = "test-request-id";
    const chatMessage = { message: "test message" } as any;

    // Mock stream function that fails with retriable errors twice, then succeeds
    let attemptCount = 0;
    const mockStartStreamFn = vi.fn(async function* () {
      attemptCount++;

      if (attemptCount <= 2) {
        // First two attempts fail with retriable error
        yield {
          /* eslint-disable @typescript-eslint/naming-convention */
          request_id: requestId,
          status: ExchangeStatus.failed,
          isRetriable: true,
          display_error_message: "Service temporarily unavailable",
          /* eslint-enable @typescript-eslint/naming-convention */
        };
        return;
      }

      // Third attempt succeeds
      yield {
        /* eslint-disable @typescript-eslint/naming-convention */
        request_id: requestId,
        status: ExchangeStatus.sent,
        response_text: "Hello, ",
        /* eslint-enable @typescript-eslint/naming-convention */
      };

      yield {
        /* eslint-disable @typescript-eslint/naming-convention */
        request_id: requestId,
        status: ExchangeStatus.sent,
        response_text: "world!",
        /* eslint-enable @typescript-eslint/naming-convention */
      };

      yield {
        /* eslint-disable @typescript-eslint/naming-convention */
        request_id: requestId,
        status: ExchangeStatus.success,
        /* eslint-enable @typescript-eslint/naming-convention */
      };
    });

    // Create instance with shorter delays for testing
    const chatStream = new ChatStreamWithRetry(
      requestId,
      chatMessage,
      mockStartStreamFn,
      3, // maxRetries
      100, // baseDelay (ms)
    );

    // Collect all chunks from the stream
    const chunks = [];
    for await (const chunk of chatStream.getStream()) {
      chunks.push(chunk);
    }

    // Verify behavior
    expect(mockStartStreamFn).toHaveBeenCalledTimes(3);

    // Check that we got the expected chunks
    expect(chunks.length).toBeGreaterThan(4); // 2 retry status + 3 success chunks

    // First chunk should be a retriable error status
    expect(chunks[0].status).toBe(ExchangeStatus.sent);
    expect(chunks[0].display_error_message).toContain("Retrying in");
    expect(chunks[0].isRetriable).toBe(true);

    // Should have a "Generating response..." message before the actual response
    const generatingIndex = chunks.findIndex(
      (chunk) =>
        chunk.status === ExchangeStatus.sent &&
        chunk.display_error_message === "Generating response... (Attempt 2)",
    );
    expect(generatingIndex).toBeGreaterThan(0);

    // Should have successful response chunks
    const successChunks = chunks.filter(
      (chunk) => chunk.response_text && chunk.status === ExchangeStatus.sent,
    );
    expect(successChunks.length).toBe(2);
    expect(successChunks[0].response_text).toBe("Hello, ");
    expect(successChunks[1].response_text).toBe("world!");

    // Final chunk should indicate success
    const lastChunk = chunks[chunks.length - 1];
    expect(lastChunk.status).toBe(ExchangeStatus.success);
  });

  test("should stop retrying after maxRetries and return failure", async () => {
    // Mock data
    const requestId = "test-request-id";
    const chatMessage = { message: "test message" } as any;
    const errorMessage = "Service temporarily unavailable";

    // Mock stream function that always fails with retriable error
    const mockStartStreamFn = vi.fn(async function* () {
      yield {
        /* eslint-disable @typescript-eslint/naming-convention */
        request_id: requestId,
        status: ExchangeStatus.failed,
        isRetriable: true,
        display_error_message: errorMessage,
        /* eslint-enable @typescript-eslint/naming-convention */
      };
    });

    // Create instance with shorter delays for testing
    const maxRetries = 2;
    const chatStream = new ChatStreamWithRetry(
      requestId,
      chatMessage,
      mockStartStreamFn,
      maxRetries,
      100, // baseDelay (ms)
    );

    // Collect all chunks from the stream
    const chunks = [];
    for await (const chunk of chatStream.getStream()) {
      chunks.push(chunk);
    }

    // Verify behavior
    expect(mockStartStreamFn).toHaveBeenCalledTimes(maxRetries + 1); // Initial attempt + retries

    // Check that we got the expected chunks
    expect(chunks.length).toBe(2 * maxRetries + 1); // 2 chunks per retry (status + generating) + final failure

    // Final chunk should be a non-retriable failure with the original error message
    const lastChunk = chunks[chunks.length - 1];
    expect(lastChunk.status).toBe(ExchangeStatus.failed);
    expect(lastChunk.isRetriable).toBe(false);
    expect(lastChunk.display_error_message).toBe(errorMessage);
    expect(lastChunk.seen_state).toBe(SeenState.unseen);
  });

  test("should handle cancellation during retry delay", async () => {
    // Mock data
    const requestId = "test-request-id";
    const chatMessage = { message: "test message" } as any;

    // Mock stream function that fails with retriable error
    const mockStartStreamFn = vi.fn(async function* () {
      yield {
        /* eslint-disable @typescript-eslint/naming-convention */
        request_id: requestId,
        status: ExchangeStatus.failed,
        isRetriable: true,
        display_error_message: "Service temporarily unavailable",
        /* eslint-enable @typescript-eslint/naming-convention */
      };
    });

    // Create instance with shorter delay for testing
    const chatStream = new ChatStreamWithRetry(
      requestId,
      chatMessage,
      mockStartStreamFn,
      3, // maxRetries
      100, // baseDelay (ms)
    );

    // Start a separate process that will cancel the stream after a short delay
    setTimeout(() => {
      chatStream.cancel();
    }, 10);

    // Collect all chunks from the stream
    const chunks = [];
    for await (const chunk of chatStream.getStream()) {
      chunks.push(chunk);
    }

    // No need to restore anything as we're using the real setTimeout

    // Verify behavior
    expect(mockStartStreamFn).toHaveBeenCalledTimes(1);

    // We should have the retry status chunk and the cancelled status chunk
    expect(chunks.length).toBeGreaterThanOrEqual(2);

    // First chunk should be a retriable error status
    expect(chunks[0].status).toBe(ExchangeStatus.sent);
    expect(chunks[0].display_error_message).toContain("Retrying in");

    // Last chunk should indicate cancellation
    const lastChunk = chunks[chunks.length - 1];
    expect(lastChunk.status).toBe(ExchangeStatus.cancelled);
  });

  test("should immediately pass through non-retriable errors", async () => {
    // Mock data
    const requestId = "test-request-id";
    const chatMessage = { message: "test message" } as any;
    const errorMessage = "This is a non-retriable error";

    // Mock stream function that fails with non-retriable error
    const mockStartStreamFn = vi.fn(async function* () {
      yield {
        /* eslint-disable @typescript-eslint/naming-convention */
        request_id: requestId,
        status: ExchangeStatus.failed,
        isRetriable: false,
        display_error_message: errorMessage,
        /* eslint-enable @typescript-eslint/naming-convention */
      };
    });

    // Create instance
    const chatStream = new ChatStreamWithRetry(
      requestId,
      chatMessage,
      mockStartStreamFn,
      3, // maxRetries
      100, // baseDelay (ms)
    );

    // Collect all chunks from the stream
    const chunks = [];
    for await (const chunk of chatStream.getStream()) {
      chunks.push(chunk);
    }

    // Verify behavior
    expect(mockStartStreamFn).toHaveBeenCalledTimes(1); // Should only be called once

    // Should have exactly 1 chunk: the error
    expect(chunks.length).toBe(1);

    // The chunk should be the non-retriable error
    expect(chunks[0].status).toBe(ExchangeStatus.failed);
    expect(chunks[0].isRetriable).toBe(false);
    expect(chunks[0].display_error_message).toBe(errorMessage);
  });

  test("should not retry if a retriable error occurs after the first chunk", async () => {
    // Mock data
    const requestId = "test-request-id";
    const chatMessage = { message: "test message" } as any;
    const errorMessage = "This is a retriable error after a successful chunk";

    // Mock stream function that yields a successful chunk and then a retriable error
    const mockStartStreamFn = vi.fn(async function* () {
      // First yield a successful chunk
      yield {
        /* eslint-disable @typescript-eslint/naming-convention */
        request_id: requestId,
        status: ExchangeStatus.sent,
        response_text: "This is a successful chunk",
        /* eslint-enable @typescript-eslint/naming-convention */
      };

      // Then yield a retriable error
      yield {
        /* eslint-disable @typescript-eslint/naming-convention */
        request_id: requestId,
        status: ExchangeStatus.failed,
        isRetriable: true,
        display_error_message: errorMessage,
        /* eslint-enable @typescript-eslint/naming-convention */
      };
    });

    // Create instance
    const chatStream = new ChatStreamWithRetry(
      requestId,
      chatMessage,
      mockStartStreamFn,
      3, // maxRetries
      100, // baseDelay (ms)
    );

    // Collect all chunks from the stream
    const chunks = [];
    for await (const chunk of chatStream.getStream()) {
      chunks.push(chunk);
    }

    // Verify behavior
    expect(mockStartStreamFn).toHaveBeenCalledTimes(1); // Should only be called once

    // Should have exactly 2 chunks: the successful chunk and the error
    expect(chunks.length).toBe(2);

    // First chunk should be the successful chunk
    expect(chunks[0].status).toBe(ExchangeStatus.sent);
    expect(chunks[0].response_text).toBe("This is a successful chunk");

    // Second chunk should be the error, which should be passed through without retry
    expect(chunks[1].status).toBe(ExchangeStatus.failed);
    expect(chunks[1].isRetriable).toBe(true); // The error is still marked as retriable
    expect(chunks[1].display_error_message).toBe(errorMessage);
  });

  test("should retry without backoff when shouldBackoff is false", async () => {
    // Mock data
    const requestId = "test-request-id";
    const chatMessage = { message: "test message" } as any;

    // Mock stream function that fails with retriable error without backoff, then succeeds
    let attemptCount = 0;
    const mockStartStreamFn = vi.fn(async function* () {
      attemptCount++;

      if (attemptCount === 1) {
        // First attempt fails with retriable error but no backoff (like 408 timeout)
        yield {
          /* eslint-disable @typescript-eslint/naming-convention */
          request_id: requestId,
          status: ExchangeStatus.failed,
          isRetriable: true,
          shouldBackoff: false,
          display_error_message: "Request timeout",
          /* eslint-enable @typescript-eslint/naming-convention */
        };
        return;
      }

      // Second attempt succeeds
      yield {
        /* eslint-disable @typescript-eslint/naming-convention */
        request_id: requestId,
        status: ExchangeStatus.sent,
        response_text: "Success after timeout",
        /* eslint-enable @typescript-eslint/naming-convention */
      };

      yield {
        /* eslint-disable @typescript-eslint/naming-convention */
        request_id: requestId,
        status: ExchangeStatus.success,
        /* eslint-enable @typescript-eslint/naming-convention */
      };
    });

    // Create instance with shorter delays for testing
    const chatStream = new ChatStreamWithRetry(
      requestId,
      chatMessage,
      mockStartStreamFn,
      3, // maxRetries
      1000, // baseDelay (ms) - longer to verify no backoff
    );

    // Collect all chunks from the stream
    const chunks = [];
    const startTime = Date.now();
    for await (const chunk of chatStream.getStream()) {
      chunks.push(chunk);
    }
    const endTime = Date.now();

    // Verify behavior
    expect(mockStartStreamFn).toHaveBeenCalledTimes(2);

    // Should complete quickly since no backoff delay was applied
    expect(endTime - startTime).toBeLessThan(500); // Should be much less than 1000ms baseDelay

    // Should have "Generating response..." message but no countdown messages
    const generatingIndex = chunks.findIndex(
      (chunk) =>
        chunk.status === ExchangeStatus.sent &&
        chunk.display_error_message === "Generating response... (Attempt 2)",
    );
    expect(generatingIndex).toBeGreaterThanOrEqual(0);

    // Should not have any "Retrying in X seconds" messages
    const retryingMessages = chunks.filter(
      (chunk) => chunk.display_error_message?.includes("Retrying in"),
    );
    expect(retryingMessages.length).toBe(0);

    // Should have successful response chunks
    const successChunks = chunks.filter(
      (chunk) => chunk.response_text && chunk.status === ExchangeStatus.sent,
    );
    expect(successChunks.length).toBe(1);
    expect(successChunks[0].response_text).toBe("Success after timeout");

    // Final chunk should indicate success
    const lastChunk = chunks[chunks.length - 1];
    expect(lastChunk.status).toBe(ExchangeStatus.success);
  });
});
