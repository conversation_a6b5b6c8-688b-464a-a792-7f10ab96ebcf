import { config } from "./chat-keybindings";
import type { ChatModel } from "./models/chat-model";
import type { RemoteAgentsModel } from "../remote-agent-manager/models/remote-agents-model";
import type { ChatModeModel } from "./models/chat-mode-model";
import type RichTextEditorRoot from "../../design-system/components/RichTextEditorAugment/Root.svelte";
import { writable } from "svelte/store";
import { describe, it, expect, vi, beforeEach } from "vitest";
import { type IHotkeysCofiguration } from "$common-webviews/src/common/keybindings/types";
import hotkeys from "hotkeys-js";

// Mock the dependencies
const mockChatModel = {
  flags: {
    enableDebugFeatures: false,
  },
} as ChatModel;
const mockRemoteAgentsModel = {
  isActive: false,
} as RemoteAgentsModel;
const mockChatModeModel = {} as ChatModeModel;

const getBrokenDownConfig = (config: IHotkeysCofiguration): IHotkeysCofiguration => {
  const brokendownConfig: IHotkeysCofiguration = {};
  const allConfigKeys = Object.keys(config);
  for (const key of allConfigKeys) {
    const keys = key.split(/[,\s]+/);
    for (const k of keys) {
      brokendownConfig[k.trim()] = config[key];
    }
  }
  return brokendownConfig;
};

// Mock RichTextEditorRoot with focus state
const createMockRichTextEditorRoot = (isFocused: boolean) => {
  const mockRoot = {
    forceFocus: vi.fn(),
    isFocused: () => writable(isFocused),
  } as unknown as RichTextEditorRoot;
  return mockRoot;
};

const initBindings = (isFocused: boolean, isEditorUndefined: boolean = false) => {
  const mockRichTextEditorRoot = isEditorUndefined
    ? undefined
    : createMockRichTextEditorRoot(isFocused);
  const context = {
    chatModel: mockChatModel,
    remoteAgentsModel: mockRemoteAgentsModel,
    chatModeModel: mockChatModeModel,
    richTextEditorRoot: mockRichTextEditorRoot,
  };
  return context;
};

describe("chat-keybindings", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    hotkeys.deleteScope("all");
  });

  describe("Enter key handling", () => {
    it("should focus editor when Enter is pressed and editor is not focused", () => {
      const context = initBindings(false);
      const { richTextEditorRoot: mockRichTextEditorRoot } = context;
      getBrokenDownConfig(config)["enter"].handler?.(new KeyboardEvent("keydown"), null, context);
      expect(mockRichTextEditorRoot!.forceFocus).toHaveBeenCalledOnce();
    });

    it("should NOT focus editor when Shift+Enter is pressed and editor is not focused", () => {
      const context = initBindings(false);
      const { richTextEditorRoot: mockRichTextEditorRoot } = context;
      getBrokenDownConfig(config)["enter+enter"]?.handler?.(
        new KeyboardEvent("keydown"),
        null,
        context,
      );
      expect(mockRichTextEditorRoot!.forceFocus).not.toHaveBeenCalledOnce();
    });

    it("should NOT focus editor when Enter is pressed and editor is already focused", () => {
      const context = initBindings(true);
      const { richTextEditorRoot: mockRichTextEditorRoot } = context;
      getBrokenDownConfig(config)["enter"].handler?.(new KeyboardEvent("keydown"), null, context);
      expect(mockRichTextEditorRoot!.forceFocus).not.toHaveBeenCalled();
    });

    it("should NOT focus editor when Shift+Enter is pressed and editor is already focused", () => {
      const context = initBindings(true);
      const { richTextEditorRoot: mockRichTextEditorRoot } = context;
      getBrokenDownConfig(config)["enter+enter"]?.handler?.(
        new KeyboardEvent("keydown"),
        null,
        context,
      );
      expect(mockRichTextEditorRoot!.forceFocus).not.toHaveBeenCalled();
    });

    it("should handle case when richTextEditorRoot is undefined", () => {
      initBindings(true, true);
      const context = initBindings(true, true);
      // Should not throw an error
      expect(() => {
        getBrokenDownConfig(config)["enter"].handler?.(new KeyboardEvent("keydown"), null, context);
      }).not.toThrow();
    });
  });

  describe("Other key handling", () => {
    it("should handle non-Enter keys without focusing editor", () => {
      const context = initBindings(false);
      const { richTextEditorRoot: mockRichTextEditorRoot } = context;
      getBrokenDownConfig(config)["space"]?.handler?.(new KeyboardEvent("keydown"), null, context);
      expect(mockRichTextEditorRoot!.forceFocus).not.toHaveBeenCalled();
    });
  });
});
