import type {
  ChangedFile,
  DiffAppliedState,
  DiffExplanation,
  RemoteAgent,
} from "@augment-internal/sidecar-libs/src/remote-agent/remote-agent-types";

export interface FileToApply {
  path: string;
  originalCode: string;
  newCode: string;
}

/**
 * Get all unique file paths from a diff explanation
 */
export function getAllFilePaths(diffExplanation: DiffExplanation): Set<string> {
  const allPaths = new Set<string>();
  if (diffExplanation && diffExplanation.length > 0) {
    diffExplanation
      .flatMap((section) => section.sections || [])
      .flatMap((subsection) => subsection.changes)
      .forEach((change) => {
        allPaths.add(change.path);
      });
  }
  return allPaths;
}

/**
 * Prepare files to apply from diff explanation and changed files
 *
 * @param diffExplanation The diff explanation containing file changes
 * @param changedFiles Original changed files (fallback)
 * @param modifiedCodeMap Map of modified code by path
 * @returns Object containing files to apply and status information
 */
export function prepareFilesToApply(
  diffExplanation: DiffExplanation,
  changedFiles: ChangedFile[],
  modifiedCodeMap: Record<string, string> = {},
  appliedFiles: Record<string, DiffAppliedState> = {},
): {
  filesToApply: FileToApply[];
  allPaths: string[];
  areAllPathsApplied: boolean;
} {
  // Get all unique file paths from diffExplanation
  const allPathsSet = getAllFilePaths(diffExplanation);
  const allPaths = Array.from(allPathsSet);

  // Fallback to changedFiles if diffExplanation is empty
  if (allPaths.length === 0) {
    allPaths.push(...changedFiles.map((file) => file.new_path || file.old_path).filter(Boolean));
  }

  // Check if all paths are already applied
  const areAllPathsApplied = allPaths.every((path) => appliedFiles[path] === "applied");

  // Filter out already applied or pending files
  const pathsToApply = allPaths.filter(
    (path) => !appliedFiles[path] || appliedFiles[path] === "none",
  );

  // Prepare files to apply
  const filesToApply: FileToApply[] = [];

  pathsToApply.forEach((path) => {
    // Find the file change in diffExplanation
    const fileChange = diffExplanation
      .flatMap((section) => section.sections || [])
      .flatMap((subsection) => subsection.changes)
      .find((change) => change.path === path);

    if (fileChange) {
      // Use modified code from map if available, otherwise use original modified code
      filesToApply.push({
        path,
        originalCode: fileChange.originalCode,
        newCode: modifiedCodeMap[path] || fileChange.modifiedCode,
      });
    } else {
      // Find the file in changedFiles as fallback
      const file = changedFiles.find((f) => (f.new_path || f.old_path) === path);
      if (file) {
        filesToApply.push({
          path,
          originalCode: file.old_contents || "",
          newCode: modifiedCodeMap[path] || file.new_contents || "",
        });
      }
    }
  });

  return {
    filesToApply,
    allPaths,
    areAllPathsApplied,
  };
}

/**
 * Apply all prepared file changes
 *
 * @param filesToApply Array of files to apply
 * @param applyChangesFunction Function to apply changes for a single file
 */
export async function applyPreparedChanges(
  filesToApply: FileToApply[],
  applyChangesFunction: (path: string, originalCode: string, newCode: string) => Promise<void>,
): Promise<void> {
  if (!filesToApply.length || !applyChangesFunction) return;

  // Apply each file change
  await Promise.all(
    filesToApply.map(async (file) => {
      return await applyChangesFunction(file.path, file.originalCode, file.newCode);
    }),
  );
  return;
}

/**
 * Sorts the overviews by updated_at or started_at, with the current agent first if it exists
 * Creates a new array, does not mutate the original
 *
 * @returns the sorted overviews
 */
export function sortOverviews(newOverviews: RemoteAgent[]): RemoteAgent[] {
  const sortedOverviews = newOverviews.sort((a, b) => {
    const aUpdated = new Date(a.updated_at || a.started_at);
    const bUpdated = new Date(b.updated_at || b.started_at);
    return bUpdated.getTime() - aUpdated.getTime();
  });

  return sortedOverviews;
}
