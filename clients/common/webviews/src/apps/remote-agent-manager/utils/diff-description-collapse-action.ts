import { type Action } from "svelte/action";

export type DiffDescriptionCollapseActionParams = {
  path: string;
  onCollapseStateChange?: (collapsed: boolean) => void;
};

export function getDiffDescriptionId(path: string, index: number) {
  return `${path}:${index}`;
}

export const diffDescriptionCollapseAction: Action<
  HTMLDivElement,
  DiffDescriptionCollapseActionParams
> = (node, params: DiffDescriptionCollapseActionParams) => {
  let observer: MutationObserver | null = null;
  let resizeObserver: ResizeObserver | null = null;
  let animationFrameId: number | null = null;
  let hasBeenCollapsed = false; // Track if this element has been collapsed due to intersection

  function updateCollapseState() {
    // Cancel any pending animation frame
    if (animationFrameId) {
      cancelAnimationFrame(animationFrameId);
    }

    // Use requestAnimationFrame to ensure DOM is fully updated
    animationFrameId = requestAnimationFrame(() => {
      const { path, onCollapseStateChange } = params;

      // If this element has already been collapsed due to intersection, don't check again
      if (hasBeenCollapsed) {
        animationFrameId = null;
        return;
      }

      // Get all description elements for this path
      const allElements = Array.from(
        document.querySelectorAll(`[data-description-id^="${path}:"]`),
      ) as HTMLElement[];

      // Check if current element intersects with any other element
      let hasIntersection = false;

      for (const otherElement of allElements) {
        if (otherElement !== node && areElementsIntersecting(node, otherElement)) {
          hasIntersection = true;
          break;
        }
      }

      // If we found an intersection, mark this element as collapsed and don't check again
      if (hasIntersection) {
        hasBeenCollapsed = true;
      }

      // Call the callback to update the collapsed state in the component
      if (onCollapseStateChange) {
        onCollapseStateChange(hasIntersection);
      }

      animationFrameId = null;
    });
  }

  function areElementsIntersecting(el1: Element, el2: Element): boolean {
    const rect1 = el1.getBoundingClientRect();
    const rect2 = el2.getBoundingClientRect();

    const verticalOverlap = !(rect1.bottom <= rect2.top || rect2.bottom <= rect1.top);

    return verticalOverlap;
  }

  function setupObservers() {
    // Clean up existing observers
    cleanup();

    // Set up MutationObserver to watch for DOM changes
    observer = new MutationObserver(() => {
      updateCollapseState();
    });

    // Observe the parent container for changes
    const container = node.closest(".descriptions") || document.body;
    observer.observe(container, {
      childList: true,
      subtree: true,
      attributes: true,
      attributeFilter: ["style", "data-description-id"],
    });

    // Set up ResizeObserver to watch for size changes
    if (window.ResizeObserver) {
      resizeObserver = new ResizeObserver(() => {
        updateCollapseState();
      });
      resizeObserver.observe(node);
    }

    // Also observe window resize
    window.addEventListener("resize", updateCollapseState);
    window.addEventListener("scroll", updateCollapseState);
  }

  function cleanup() {
    if (observer) {
      observer.disconnect();
      observer = null;
    }

    if (resizeObserver) {
      resizeObserver.disconnect();
      resizeObserver = null;
    }

    if (animationFrameId) {
      cancelAnimationFrame(animationFrameId);
      animationFrameId = null;
    }

    window.removeEventListener("resize", updateCollapseState);
    window.removeEventListener("scroll", updateCollapseState);
  }

  // Initial setup with DOM is ready to ensure all elements are in the DOM
  const initializeWhenReady = () => {
    if (document.readyState === "loading") {
      document.addEventListener("DOMContentLoaded", () => {
        setupObservers();
        updateCollapseState();
      });
    } else {
      // DOM is already ready
      requestAnimationFrame(() => {
        setupObservers();
        updateCollapseState();
      });
    }
  };

  initializeWhenReady();

  return {
    update: (newParams) => {
      params = newParams;
      // Reset the collapsed state when parameters change (e.g., new content)
      hasBeenCollapsed = false;
      updateCollapseState();
    },
    destroy: cleanup,
  };
};
