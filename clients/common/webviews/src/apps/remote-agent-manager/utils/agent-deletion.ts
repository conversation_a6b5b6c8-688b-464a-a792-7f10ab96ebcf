import { type RemoteAgent } from "@augment-internal/sidecar-libs/src/remote-agent/remote-agent-types";

/** The number of days before deletion to warn the user */
export const WARN_USER_OF_DELETION_DAYS = 7;
/** The number of milliseconds before deletion to warn the user */
export const WARN_USER_OF_DELETION_MS = WARN_USER_OF_DELETION_DAYS * 24 * 60 * 60 * 1000;

export function shouldWarnOfRemoteAgentDeletion(
  agent: RemoteAgent | undefined,
  autoResumeDays?: number,
): boolean {
  if (!agent?.expires_at) {
    // If we don't know when the agent will expire, then we can't warn
    return false;
  }
  const deletionTime = new Date(agent.expires_at).getTime();
  const now = Date.now();
  const timeRemainingDuration = deletionTime - now;
  if (autoResumeDays) {
    // If we know how many days we allow for auto-resume, then we can use that
    // to determine if we should warn the user. We do not want to warn the user
    // if we are within the auto-resume window. If we are no longer auto-resuming,
    // we'll need to warn the user if they are less than WARN_USER_OF_DELETION_DAYS
    // of the deletion time.
    const lastUpdateTime = new Date(agent.updated_at).getTime();
    const autoResumeEndTime = lastUpdateTime + autoResumeDays * 24 * 60 * 60 * 1000;

    // If we're still within the auto-resume window, don't warn yet
    if (now < autoResumeEndTime) {
      return false;
    }
  }
  // We're past the auto-resume window (or there is no window), so warn if we're within the warning period
  return timeRemainingDuration < WARN_USER_OF_DELETION_MS;
}
