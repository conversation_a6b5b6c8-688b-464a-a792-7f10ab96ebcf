import { describe, expect, it } from "vitest";
import {
  searchBranches,
  searchRepos,
  parseRemoteUrl,
  removeOriginPrefix,
  parseRemoteBranchName,
  getLocalBranchName,
  filterLocalGitBranches,
} from "./git";
import type {
  Gith<PERSON>Branch,
  GithubRepo,
} from "@augment-internal/sidecar-libs/src/remote-agent/remote-agent-types";
import type { GitBranch } from "$vscode/src/remote-agent-manager/commit-ref-types";

describe("git utils", () => {
  describe("searchBranches", () => {
    /* eslint-disable @typescript-eslint/naming-convention */
    const mockBranches: GithubBranch[] = [
      {
        name: "main",
        commit: { sha: "abc123", url: "https://github.com/repo/commit/abc123" },
        protected: true,
      },
      {
        name: "feature/user-auth",
        commit: { sha: "def456", url: "https://github.com/repo/commit/def456" },
        protected: false,
      },
      {
        name: "feature/payment-api",
        commit: { sha: "ghi789", url: "https://github.com/repo/commit/ghi789" },
        protected: false,
      },
      {
        name: "bugfix/login-issue",
        commit: { sha: "jkl012", url: "https://github.com/repo/commit/jkl012" },
        protected: false,
      },
    ];
    /* eslint-enable @typescript-eslint/naming-convention */

    it("should return branches that include the given value", () => {
      const result = searchBranches(mockBranches, "feature");
      expect(result).toHaveLength(2);
      expect(result[0].name).toBe("feature/user-auth");
      expect(result[1].name).toBe("feature/payment-api");
    });

    it("should return an empty array if no branches match", () => {
      const result = searchBranches(mockBranches, "nonexistent");
      expect(result).toHaveLength(0);
    });

    it("should return all branches if search value is empty", () => {
      const result = searchBranches(mockBranches, "");
      expect(result).toHaveLength(4);
    });

    it("should be case insensitive", () => {
      const result = searchBranches(mockBranches, "Feature");
      expect(result).toHaveLength(2);
      expect(result[0].name).toBe("feature/user-auth");
      expect(result[1].name).toBe("feature/payment-api");
    });
  });

  describe("searchRepos", () => {
    /* eslint-disable @typescript-eslint/naming-convention */
    const mockRepos: GithubRepo[] = [
      {
        owner: "augmentcode",
        name: "augment",
        html_url: "https://github.com/augmentcode/augment",
        created_at: "2022-01-01T00:00:00Z",
        updated_at: "2022-02-01T00:00:00Z",
        default_branch: "main",
      },
      {
        owner: "augmentcode",
        name: "augment-cli",
        html_url: "https://github.com/augmentcode/augment-cli",
        created_at: "2022-01-15T00:00:00Z",
        updated_at: "2022-02-15T00:00:00Z",
        default_branch: "main",
      },
      {
        owner: "augmentcode",
        name: "augment-docs",
        html_url: "https://github.com/augmentcode/augment-docs",
        created_at: "2022-01-20T00:00:00Z",
        updated_at: "2022-02-20T00:00:00Z",
        default_branch: "main",
      },
      {
        owner: "augmentcode",
        name: "demo-repo",
        html_url: "https://github.com/augmentcode/demo-repo",
        created_at: "2022-01-25T00:00:00Z",
        updated_at: "2022-02-25T00:00:00Z",
        default_branch: "main",
      },
    ];
    /* eslint-enable @typescript-eslint/naming-convention */

    it("should return repos that include the given value", () => {
      const result = searchRepos(mockRepos, "augment-");
      expect(result).toHaveLength(2);
      expect(result[0].name).toBe("augment-cli");
      expect(result[1].name).toBe("augment-docs");
    });

    it("should return an empty array if no repos match", () => {
      const result = searchRepos(mockRepos, "nonexistent");
      expect(result).toHaveLength(0);
    });

    it("should return all repos if search value is empty", () => {
      const result = searchRepos(mockRepos, "");
      expect(result).toHaveLength(4);
    });

    it("should be case insensitive", () => {
      const result = searchRepos(mockRepos, "Augment-");
      expect(result).toHaveLength(2);
      expect(result[0].name).toBe("augment-cli");
      expect(result[1].name).toBe("augment-docs");
    });

    describe("owner/repo format search", () => {
      it("should match exact owner and partial repo name", () => {
        const result = searchRepos(mockRepos, "augmentcode/augment");
        expect(result).toHaveLength(3);
        expect(result.map((r) => r.name)).toEqual(["augment", "augment-cli", "augment-docs"]);
      });

      it("should match exact owner and partial repo name with case insensitivity", () => {
        const result = searchRepos(mockRepos, "AugmentCode/AUGMENT");
        expect(result).toHaveLength(3);
        expect(result.map((r) => r.name)).toEqual(["augment", "augment-cli", "augment-docs"]);
      });

      it("should require exact owner match in owner/repo format", () => {
        const result = searchRepos(mockRepos, "augment/augment");
        expect(result).toHaveLength(0);
      });

      it("should match specific repo with owner/repo format", () => {
        const result = searchRepos(mockRepos, "augmentcode/demo");
        expect(result).toHaveLength(1);
        expect(result[0].name).toBe("demo-repo");
      });

      it("should return empty array when owner exists but repo doesn't match", () => {
        const result = searchRepos(mockRepos, "augmentcode/nonexistent");
        expect(result).toHaveLength(0);
      });

      it("should return empty array when owner doesn't exist", () => {
        const result = searchRepos(mockRepos, "nonexistentowner/augment");
        expect(result).toHaveLength(0);
      });
    });

    describe("single term search", () => {
      it("should match by owner name only", () => {
        const result = searchRepos(mockRepos, "augmentcode");
        expect(result).toHaveLength(4);
        expect(result.map((r) => r.name)).toEqual([
          "augment",
          "augment-cli",
          "augment-docs",
          "demo-repo",
        ]);
      });

      it("should match by partial owner name", () => {
        const result = searchRepos(mockRepos, "augment");
        expect(result).toHaveLength(4); // matches both owner "augmentcode" and repo names containing "augment"
      });

      it("should match by repo name only", () => {
        const result = searchRepos(mockRepos, "demo");
        expect(result).toHaveLength(1);
        expect(result[0].name).toBe("demo-repo");
      });

      it("should match by partial repo name", () => {
        const result = searchRepos(mockRepos, "cli");
        expect(result).toHaveLength(1);
        expect(result[0].name).toBe("augment-cli");
      });
    });

    describe("edge cases", () => {
      it("should handle multiple slashes by only considering first two terms", () => {
        const result = searchRepos(mockRepos, "augmentcode/augment/extra/stuff");
        expect(result).toHaveLength(3);
        expect(result.map((r) => r.name)).toEqual(["augment", "augment-cli", "augment-docs"]);
      });

      it("should handle leading slash", () => {
        const result = searchRepos(mockRepos, "/augmentcode");
        expect(result).toHaveLength(4);
      });

      it("should handle trailing slash", () => {
        const result = searchRepos(mockRepos, "augmentcode/");
        expect(result).toHaveLength(4);
      });

      it("should handle whitespace in search terms", () => {
        const result = searchRepos(mockRepos, " augment ");
        expect(result).toHaveLength(0); // " augment " (with spaces) doesn't match "augment"
      });

      it("should handle empty string after slash", () => {
        const result = searchRepos(mockRepos, "augmentcode/");
        expect(result).toHaveLength(4); // becomes single term search for "augmentcode"
      });

      it("should handle only slash", () => {
        const result = searchRepos(mockRepos, "/");
        expect(result).toHaveLength(4); // no search terms after filtering
      });

      it("should handle multiple consecutive slashes", () => {
        const result = searchRepos(mockRepos, "augmentcode//augment");
        expect(result).toHaveLength(4); // becomes single term search for "augmentcode" (empty string filtered out)
      });
    });
  });

  describe("parseRemoteUrl", () => {
    it("should parse a standard GitHub URL", () => {
      const result = parseRemoteUrl("https://github.com/augmentcode/augment.git");
      expect(result).toEqual({ owner: "augmentcode", name: "augment" });
    });

    it("should parse a GitHub URL without .git extension", () => {
      const result = parseRemoteUrl("https://github.com/augmentcode/augment");
      expect(result).toEqual({ owner: "augmentcode", name: "augment" });
    });

    it("should parse a GitHub URL with additional path segments", () => {
      const result = parseRemoteUrl("https://github.com/augmentcode/augment/tree/main");
      expect(result).toEqual({ owner: "augmentcode", name: "augment" });
    });

    it("should parse a GitHub URL with a period in the name", () => {
      const result = parseRemoteUrl("https://github.com/augmentcode/augment.vim.git");
      expect(result).toEqual({ owner: "augmentcode", name: "augment.vim" });
    });

    it("should handle SSH URLs", () => {
      const result = parseRemoteUrl("**************:augmentcode/augment.git");
      expect(result).toBeUndefined();
    });

    it("should return undefined for non-GitHub URLs", () => {
      const result = parseRemoteUrl("https://gitlab.com/augmentcode/augment.git");
      expect(result).toBeUndefined();
    });

    it("should return undefined for invalid URLs", () => {
      const result = parseRemoteUrl("not-a-url");
      expect(result).toBeUndefined();
    });

    it("should return undefined for empty strings", () => {
      const result = parseRemoteUrl("");
      expect(result).toBeUndefined();
    });
  });

  describe("parseRemoteBranchName", () => {
    it("should parse full git refs", () => {
      const result = parseRemoteBranchName("refs/remotes/origin/main");
      expect(result).toEqual({ remote: "origin", branch: "main" });
    });

    it("should parse short remote refs", () => {
      const result = parseRemoteBranchName("origin/feature-branch");
      expect(result).toEqual({ remote: "origin", branch: "feature-branch" });
    });

    it("should parse refs with different remote names", () => {
      const result = parseRemoteBranchName("upstream/develop");
      expect(result).toEqual({ remote: "upstream", branch: "develop" });
    });

    it("should parse refs with common remote names", () => {
      expect(parseRemoteBranchName("github/main")).toEqual({ remote: "github", branch: "main" });
      expect(parseRemoteBranchName("gitlab/master")).toEqual({
        remote: "gitlab",
        branch: "master",
      });
      expect(parseRemoteBranchName("fork/feature")).toEqual({ remote: "fork", branch: "feature" });
    });

    it("should not parse branch names with slashes as remote refs", () => {
      const result = parseRemoteBranchName("feature/user-auth");
      expect(result).toBeNull();
    });

    it("should handle branches with multiple slashes correctly", () => {
      // Remote branch with slashes in branch name
      const result1 = parseRemoteBranchName("origin/username/feature/work-in-progress");
      expect(result1).toEqual({ remote: "origin", branch: "username/feature/work-in-progress" });

      // Local branch with slashes should not be parsed as remote
      const result2 = parseRemoteBranchName("username/feature/work-in-progress");
      expect(result2).toBeNull();

      // Full ref with slashes in branch name
      const result3 = parseRemoteBranchName(
        "refs/remotes/origin/username/feature/work-in-progress",
      );
      expect(result3).toEqual({ remote: "origin", branch: "username/feature/work-in-progress" });
    });

    it("should parse complex branch names", () => {
      const result = parseRemoteBranchName("origin/feature/user-auth");
      expect(result).toEqual({ remote: "origin", branch: "feature/user-auth" });
    });

    it("should return null for local branch names", () => {
      const result = parseRemoteBranchName("main");
      expect(result).toBeNull();
    });

    it("should return null for empty string", () => {
      const result = parseRemoteBranchName("");
      expect(result).toBeNull();
    });
  });

  describe("getLocalBranchName", () => {
    it("should extract branch name from full git refs", () => {
      const result = getLocalBranchName("refs/remotes/origin/main");
      expect(result).toBe("main");
    });

    it("should extract branch name from short remote refs", () => {
      const result = getLocalBranchName("origin/feature-branch");
      expect(result).toBe("feature-branch");
    });

    it("should handle complex branch names", () => {
      const result = getLocalBranchName("origin/feature/user-auth");
      expect(result).toBe("feature/user-auth");
    });

    it("should return unchanged for local branch names", () => {
      const result = getLocalBranchName("main");
      expect(result).toBe("main");
    });

    it("should handle empty string", () => {
      const result = getLocalBranchName("");
      expect(result).toBe("");
    });

    it("should handle branch names with slashes", () => {
      const result = getLocalBranchName("origin/bugfix/fix-login-issue");
      expect(result).toBe("bugfix/fix-login-issue");
    });

    it("should handle symbolic reference format", () => {
      const result = getLocalBranchName("origin/HEAD -> origin/main");
      expect(result).toBe("main");
    });

    it("should handle nested symbolic references", () => {
      const result = getLocalBranchName("upstream/HEAD -> upstream/develop");
      expect(result).toBe("develop");
    });

    it("should handle different remote names", () => {
      expect(getLocalBranchName("upstream/feature-branch")).toBe("feature-branch");
      expect(getLocalBranchName("fork/bugfix")).toBe("bugfix");
      expect(getLocalBranchName("github/main")).toBe("main");
    });

    it("should handle branches with multiple slashes", () => {
      // Remote branches with slashes in branch name
      expect(getLocalBranchName("origin/username/feature/work-in-progress")).toBe(
        "username/feature/work-in-progress",
      );
      expect(getLocalBranchName("upstream/team/project/bugfix")).toBe("team/project/bugfix");

      // Local branches with slashes should remain unchanged
      expect(getLocalBranchName("username/feature/work-in-progress")).toBe(
        "username/feature/work-in-progress",
      );

      // Full refs with slashes
      expect(getLocalBranchName("refs/remotes/origin/user/feature/branch")).toBe(
        "user/feature/branch",
      );
    });
  });

  describe("removeOriginPrefix (deprecated)", () => {
    it("should remove 'origin/' prefix from branch name", () => {
      const result = removeOriginPrefix("origin/main");
      expect(result).toBe("main");
    });

    it("should not modify branch name without 'origin/' prefix", () => {
      const result = removeOriginPrefix("feature/branch");
      expect(result).toBe("feature/branch");
    });

    it("should handle empty string", () => {
      const result = removeOriginPrefix("");
      expect(result).toBe("");
    });

    it("should only remove prefix at the beginning", () => {
      const result = removeOriginPrefix("feature/origin/branch");
      expect(result).toBe("feature/origin/branch");
    });
  });

  describe("filterLocalGitBranches", () => {
    const createMockBranches = (): GitBranch[] => [
      {
        name: "main",
        isRemote: true,
        isCurrentBranch: true,
        isDefault: true,
      },
      {
        name: "feature",
        isRemote: true,
        isCurrentBranch: false,
        isDefault: false,
      },
      {
        name: "bugfix",
        isRemote: false,
        isCurrentBranch: false,
        isDefault: false,
      },
    ];

    it("should filter out non-remote branches", () => {
      const branches = createMockBranches();
      const result = filterLocalGitBranches(branches);

      expect(result).toHaveLength(2);
      expect(result.every((b) => b.isRemote)).toBe(true);
    });

    it("should not filter out default branch when current branch is different", () => {
      const branches: GitBranch[] = [
        {
          name: "feature",
          isRemote: false,
          isCurrentBranch: true,
          isDefault: false,
        },
        {
          name: "origin/main",
          isRemote: true,
          isCurrentBranch: false,
          isDefault: true,
        },
        {
          name: "origin/feature",
          isRemote: true,
          isCurrentBranch: false,
          isDefault: false,
        },
      ];

      const result = filterLocalGitBranches(branches);

      expect(result).toHaveLength(2);
      expect(result.some((b) => b.isDefault)).toBe(true);
    });

    it("should handle empty array", () => {
      const result = filterLocalGitBranches([]);
      expect(result).toHaveLength(0);
    });

    it("should handle branches without current or default flags", () => {
      const branches: GitBranch[] = [
        {
          name: "origin/main",
          isRemote: true,
        },
        {
          name: "origin/feature",
          isRemote: true,
        },
        {
          name: "local-branch",
          isRemote: false,
        },
      ];

      const result = filterLocalGitBranches(branches);

      expect(result).toHaveLength(2);
      expect(result.every((b) => b.isRemote)).toBe(true);
    });

    it("should include default branch even when current branch is also default (dev deploy scenario)", () => {
      const branches: GitBranch[] = [
        {
          name: "main",
          isRemote: false,
          isCurrentBranch: true,
          isDefault: false, // Local current branch
        },
        {
          name: "origin/main",
          isRemote: true,
          isCurrentBranch: false,
          isDefault: true, // Remote default branch
        },
        {
          name: "origin/feature",
          isRemote: true,
          isCurrentBranch: false,
          isDefault: false,
        },
      ];

      const result = filterLocalGitBranches(branches);

      expect(result).toHaveLength(2);
      expect(result.some((b) => b.isDefault)).toBe(true);
      expect(result.find((b) => b.isDefault)?.name).toBe("origin/main");
    });

    it("should include all remote branches regardless of current/default status", () => {
      const branches: GitBranch[] = [
        {
          name: "main",
          isRemote: false,
          isCurrentBranch: true,
          isDefault: false,
        },
        {
          name: "origin/main",
          isRemote: true,
          isCurrentBranch: false,
          isDefault: true,
        },
        {
          name: "origin/develop",
          isRemote: true,
          isCurrentBranch: false,
          isDefault: false,
        },
        {
          name: "origin/feature/auth",
          isRemote: true,
          isCurrentBranch: false,
          isDefault: false,
        },
        {
          name: "local-feature",
          isRemote: false,
          isCurrentBranch: false,
          isDefault: false,
        },
      ];

      const result = filterLocalGitBranches(branches);

      expect(result).toHaveLength(3);
      expect(result.every((b) => b.isRemote)).toBe(true);
      expect(result.map((b) => b.name)).toEqual([
        "origin/main",
        "origin/develop",
        "origin/feature/auth",
      ]);
    });

    it("should handle mixed remote names correctly", () => {
      const branches: GitBranch[] = [
        {
          name: "upstream/main",
          isRemote: true,
          isCurrentBranch: false,
          isDefault: true,
        },
        {
          name: "fork/feature",
          isRemote: true,
          isCurrentBranch: false,
          isDefault: false,
        },
        {
          name: "github/develop",
          isRemote: true,
          isCurrentBranch: false,
          isDefault: false,
        },
        {
          name: "local-branch",
          isRemote: false,
          isCurrentBranch: true,
          isDefault: false,
        },
      ];

      const result = filterLocalGitBranches(branches);

      expect(result).toHaveLength(3);
      expect(result.every((b) => b.isRemote)).toBe(true);
      expect(result.map((b) => b.name)).toEqual([
        "upstream/main",
        "fork/feature",
        "github/develop",
      ]);
    });

    it("should preserve all properties of remote branches", () => {
      const branches: GitBranch[] = [
        {
          name: "origin/main",
          isRemote: true,
          isCurrentBranch: false,
          isDefault: true,
        },
        {
          name: "origin/feature",
          isRemote: true,
          isCurrentBranch: true, // Remote branch that's also current
          isDefault: false,
        },
      ];

      const result = filterLocalGitBranches(branches);

      expect(result).toHaveLength(2);

      const mainBranch = result.find((b) => b.name === "origin/main");
      expect(mainBranch).toEqual({
        name: "origin/main",
        isRemote: true,
        isCurrentBranch: false,
        isDefault: true,
      });

      const featureBranch = result.find((b) => b.name === "origin/feature");
      expect(featureBranch).toEqual({
        name: "origin/feature",
        isRemote: true,
        isCurrentBranch: true,
        isDefault: false,
      });
    });
  });
});
