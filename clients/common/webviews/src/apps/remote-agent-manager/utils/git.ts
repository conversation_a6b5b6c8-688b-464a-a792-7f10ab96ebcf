import {
  type GithubRepo,
  type GithubBranch,
} from "@augment-internal/sidecar-libs/src/remote-agent/remote-agent-types";
import { type GitBranch } from "$vscode/src/remote-agent-manager/commit-ref-types";

export function searchBranches(branches: GithubBranch[], value: string) {
  return branches.filter((branch) => branch.name.toLowerCase().includes(value.toLowerCase()));
}

export function searchRepos(repos: GithubRepo[], value: string) {
  if (value === "") {
    return repos;
  }
  const searchTerms = value
    .toLowerCase()
    .split("/", 2)
    .filter((term) => term.length > 0);
  return repos.filter((repo) => {
    const owner = repo.owner.toLowerCase();
    const name = repo.name.toLowerCase();
    // If a single term is present, it can be the repo name or the owner
    if (searchTerms.length === 1) {
      return owner.includes(searchTerms[0]) || name.includes(searchTerms[0]);
    } else if (searchTerms.length === 2) {
      // If two terms are present, the first must be the owner and the second term must be a part of the repo name
      return searchTerms[0] === owner && name.includes(searchTerms[1]);
    } else {
      return true;
    }
  });
}

export function parseRemoteUrl(remoteUrl: string): { owner: string; name: string } | undefined {
  // Match GitHub URLs in the format github.com/owner/name[.git][/...]
  // This regex captures the owner and repository name, handling repository names with periods
  const match = remoteUrl.match(/github\.com\/([^/]+)\/([^/]+?)(?:\.git|\/|$)/);
  if (!match) {
    return undefined;
  }
  return { owner: match[1], name: match[2] };
}

/**
 * Parse a remote branch reference and extract the remote name and local branch name
 * @param branchRef - Git branch reference (e.g., "refs/remotes/origin/main", "upstream/main", "main")
 * @returns Object with remote and branch names, or null if not a remote reference
 */
export function parseRemoteBranchName(
  branchRef: string,
): { remote: string; branch: string } | null {
  // Handle full refs like "refs/remotes/origin/main"
  const fullRefMatch = branchRef.match(/^refs\/remotes\/([^/]+)\/(.+)$/);
  if (fullRefMatch) {
    return { remote: fullRefMatch[1], branch: fullRefMatch[2] };
  }

  // Handle short refs like "origin/main", "upstream/develop", etc.
  // Be more careful to avoid false positives with branch names that contain slashes
  const shortRefMatch = branchRef.match(/^([^/]+)\/(.+)$/);
  if (shortRefMatch) {
    const [, remoteName, branchName] = shortRefMatch;
    // Common remote names to help identify actual remotes vs branch names with slashes
    const commonRemotes = ["origin", "upstream", "fork", "github", "gitlab", "bitbucket"];
    if (commonRemotes.includes(remoteName) || remoteName.includes(".")) {
      return { remote: remoteName, branch: branchName };
    }
  }

  // Not a remote branch reference
  return null;
}

/**
 * Extract the local branch name from a git branch reference
 * Handles various git ref formats and removes remote prefixes
 * @param branchRef - Git branch reference
 * @returns Local branch name without remote prefix
 */
export function getLocalBranchName(branchRef: string): string {
  // Handle symbolic reference format like "origin/HEAD -> origin/main" or "upstream/HEAD -> upstream/develop"
  const symbolicMatch = branchRef.match(/^[^/]+\/HEAD\s*->\s*(.+)$/);
  if (symbolicMatch) {
    // Recursively parse the target reference
    return getLocalBranchName(symbolicMatch[1]);
  }

  const parsed = parseRemoteBranchName(branchRef);
  return parsed ? parsed.branch : branchRef;
}

/**
 * @deprecated Use getLocalBranchName instead for better remote handling
 * Remove 'origin/' prefix from branch name for backward compatibility
 */
export function removeOriginPrefix(branchName: string): string {
  // Maintain exact backward compatibility - only remove 'origin/' prefix
  return branchName.replace(/^origin\//, "");
}

/**
 * Filters out any branches that are not remote.
 */
export function filterLocalGitBranches(branches: GitBranch[]): GitBranch[] {
  return branches.filter((b) => {
    return b.isRemote;
  });
}
