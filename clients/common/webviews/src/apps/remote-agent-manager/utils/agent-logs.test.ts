import { describe, it, expect } from "vitest";
import { mergeAgentSetupLogs, sortByStepAndSequenceNumber } from "./agent-logs";
import type {
  RemoteWorkspaceSetupStatus,
  RemoteWorkspaceSetupStep,
} from "@augment-internal/sidecar-libs/src/remote-agent/remote-agent-types";
import { RemoteWorkspaceSetupStepStatus } from "@augment-internal/sidecar-libs/src/remote-agent/remote-agent-types";

describe("agent-logs utility functions", () => {
  // Helper function to create a step
  const createStep = (
    stepNumber: number,
    sequenceId: number,
    status: RemoteWorkspaceSetupStepStatus = RemoteWorkspaceSetupStepStatus.running,
    description: string = `Step ${stepNumber}`,
    logs: string = `Logs for step ${stepNumber}, sequence ${sequenceId}`,
  ): RemoteWorkspaceSetupStep => {
    return {
      /* eslint-disable @typescript-eslint/naming-convention */
      step_number: stepNumber,
      sequence_id: sequenceId,
      status,
      step_description: description,
      logs,
      /* eslint-enable @typescript-eslint/naming-convention */
    };
  };

  // Helper function to create logs
  const createLogs = (steps: RemoteWorkspaceSetupStep[]): RemoteWorkspaceSetupStatus => {
    return { steps };
  };

  describe("sortByStepAndSequenceNumber", () => {
    it("should return new logs when existing logs are empty", () => {
      const existingLogs = createLogs([]);
      const newLogs = createLogs([createStep(0, 0), createStep(1, 0)]);

      const result = sortByStepAndSequenceNumber(existingLogs, newLogs);
      expect(result).toEqual(newLogs);
    });

    it("should return existing logs when new logs are empty", () => {
      const existingLogs = createLogs([createStep(0, 0), createStep(1, 0)]);
      const newLogs = createLogs([]);

      const result = sortByStepAndSequenceNumber(existingLogs, newLogs);
      expect(result).toEqual(existingLogs);
    });

    it("should merge logs with different step numbers in ascending order", () => {
      const existingLogs = createLogs([createStep(0, 0), createStep(2, 0)]);
      const newLogs = createLogs([createStep(1, 0), createStep(3, 0)]);

      const result = sortByStepAndSequenceNumber(existingLogs, newLogs);
      expect(result.steps).toHaveLength(4);
      expect(result.steps[0].step_number).toBe(0);
      expect(result.steps[1].step_number).toBe(1);
      expect(result.steps[2].step_number).toBe(2);
      expect(result.steps[3].step_number).toBe(3);
    });

    it("should handle when existing logs have lower step numbers", () => {
      const existingLogs = createLogs([createStep(0, 0), createStep(1, 0)]);
      const newLogs = createLogs([createStep(2, 0), createStep(3, 0)]);

      const result = sortByStepAndSequenceNumber(existingLogs, newLogs);
      expect(result.steps).toHaveLength(4);
      expect(result.steps.map((step: RemoteWorkspaceSetupStep) => step.step_number)).toEqual([
        0, 1, 2, 3,
      ]);
    });

    it("should handle when new logs have lower step numbers", () => {
      const existingLogs = createLogs([createStep(2, 0), createStep(3, 0)]);
      const newLogs = createLogs([createStep(0, 0), createStep(1, 0)]);

      const result = sortByStepAndSequenceNumber(existingLogs, newLogs);
      expect(result.steps).toHaveLength(4);
      expect(result.steps.map((step: RemoteWorkspaceSetupStep) => step.step_number)).toEqual([
        0, 1, 2, 3,
      ]);
    });

    it("should merge logs with same step numbers but different sequence IDs in ascending order", () => {
      const existingLogs = createLogs([createStep(0, 0), createStep(1, 0), createStep(1, 2)]);
      const newLogs = createLogs([createStep(0, 1), createStep(1, 1), createStep(1, 3)]);

      const result = sortByStepAndSequenceNumber(existingLogs, newLogs);
      expect(result.steps).toHaveLength(6);

      // Check step 0 sequence IDs
      expect(result.steps[0].step_number).toBe(0);
      expect(result.steps[0].sequence_id).toBe(0);
      expect(result.steps[1].step_number).toBe(0);
      expect(result.steps[1].sequence_id).toBe(1);

      // Check step 1 sequence IDs
      expect(result.steps[2].step_number).toBe(1);
      expect(result.steps[2].sequence_id).toBe(0);
      expect(result.steps[3].step_number).toBe(1);
      expect(result.steps[3].sequence_id).toBe(1);
      expect(result.steps[4].step_number).toBe(1);
      expect(result.steps[4].sequence_id).toBe(2);
      expect(result.steps[5].step_number).toBe(1);
      expect(result.steps[5].sequence_id).toBe(3);
    });

    it("should handle when existing logs have lower sequence IDs", () => {
      const existingLogs = createLogs([createStep(0, 0), createStep(0, 1)]);
      const newLogs = createLogs([createStep(0, 2), createStep(0, 3)]);

      const result = sortByStepAndSequenceNumber(existingLogs, newLogs);
      expect(result.steps).toHaveLength(4);
      expect(result.steps.map((step: RemoteWorkspaceSetupStep) => step.sequence_id)).toEqual([
        0, 1, 2, 3,
      ]);
    });

    it("should handle when new logs have lower sequence IDs", () => {
      const existingLogs = createLogs([createStep(0, 2), createStep(0, 3)]);
      const newLogs = createLogs([createStep(0, 0), createStep(0, 1)]);

      const result = sortByStepAndSequenceNumber(existingLogs, newLogs);
      expect(result.steps).toHaveLength(4);
      expect(result.steps.map((step: RemoteWorkspaceSetupStep) => step.sequence_id)).toEqual([
        0, 1, 2, 3,
      ]);
    });
  });

  describe("mergeAgentSetupLogs", () => {
    it("should merge steps with the same step number", () => {
      const logs = createLogs([
        createStep(0, 0, RemoteWorkspaceSetupStepStatus.running, "Step 0", "Initial logs"),
        createStep(0, 1, RemoteWorkspaceSetupStepStatus.success, "Step 0", "Updated logs"),
        createStep(1, 0, RemoteWorkspaceSetupStepStatus.running, "Step 1", "Step 1 logs"),
      ]);

      const result = mergeAgentSetupLogs(logs);
      expect(result.steps).toHaveLength(2);
      expect(result.steps[0].step_number).toBe(0);
      expect(result.steps[0].status).toBe(RemoteWorkspaceSetupStepStatus.success);
      expect(result.steps[0].logs).toBe("Updated logs");
      expect(result.steps[1].step_number).toBe(1);
    });

    it("should not overwrite success status with running status", () => {
      const logs = createLogs([
        createStep(0, 0, RemoteWorkspaceSetupStepStatus.success, "Step 0", "Success logs"),
        createStep(0, 1, RemoteWorkspaceSetupStepStatus.running, "Step 0", "Running logs"),
      ]);

      const result = mergeAgentSetupLogs(logs);
      expect(result.steps).toHaveLength(1);
      expect(result.steps[0].status).toBe(RemoteWorkspaceSetupStepStatus.success);
    });

    it("should append logs for the same step when sequence ID increases", () => {
      const logs = createLogs([
        createStep(1, 0, RemoteWorkspaceSetupStepStatus.running, "Step 1", "Initial logs"),
        createStep(1, 1, RemoteWorkspaceSetupStepStatus.running, "Step 1", "More logs"),
      ]);

      const result = mergeAgentSetupLogs(logs);
      expect(result.steps).toHaveLength(1);
      expect(result.steps[0].logs).toBe("Initial logs\nMore logs");
      expect(result.steps[0].sequence_id).toBe(1);
    });

    it("should overwrite logs for step 0 regardless of sequence ID", () => {
      const logs = createLogs([
        createStep(
          0,
          0,
          RemoteWorkspaceSetupStepStatus.running,
          "Container Setup",
          "Initial setup",
        ),
        createStep(
          0,
          1,
          RemoteWorkspaceSetupStepStatus.running,
          "Container Setup",
          "Updated setup",
        ),
      ]);

      const result = mergeAgentSetupLogs(logs);
      expect(result.steps).toHaveLength(1);
      expect(result.steps[0].logs).toBe("Updated setup");
    });

    it("should handle a complex mix of steps", () => {
      // The logs need to be sorted by step number first for mergeAgentSetupLogs to work correctly
      // This is because mergeAgentSetupLogs only merges adjacent steps with the same step number
      const logs = createLogs([
        // Step 0 logs
        createStep(
          0,
          0,
          RemoteWorkspaceSetupStepStatus.running,
          "Container Setup",
          "Initial setup",
        ),
        createStep(
          0,
          1,
          RemoteWorkspaceSetupStepStatus.success,
          "Container Setup",
          "Setup complete",
        ),
        // Step 1 logs
        createStep(1, 0, RemoteWorkspaceSetupStepStatus.running, "Step 1", "Step 1 initial"),
        createStep(1, 1, RemoteWorkspaceSetupStepStatus.running, "Step 1", "Step 1 progress"),
        createStep(1, 2, RemoteWorkspaceSetupStepStatus.success, "Step 1", "Step 1 complete"),
        // Step 2 logs
        createStep(2, 0, RemoteWorkspaceSetupStepStatus.running, "Step 2", "Step 2 started"),
      ]);

      const result = mergeAgentSetupLogs(logs);
      expect(result.steps).toHaveLength(3);

      // Check container setup (step 0)
      expect(result.steps[0].step_number).toBe(0);
      expect(result.steps[0].status).toBe(RemoteWorkspaceSetupStepStatus.success);
      expect(result.steps[0].logs).toBe("Setup complete");

      // Check step 1
      expect(result.steps[1].step_number).toBe(1);
      expect(result.steps[1].status).toBe(RemoteWorkspaceSetupStepStatus.success);
      expect(result.steps[1].logs).toBe("Step 1 initial\nStep 1 progress\nStep 1 complete");
      expect(result.steps[1].sequence_id).toBe(2);

      // Check step 2
      expect(result.steps[2].step_number).toBe(2);
      expect(result.steps[2].status).toBe(RemoteWorkspaceSetupStepStatus.running);
      expect(result.steps[2].logs).toBe("Step 2 started");
    });
  });

  describe("integration of sortByStepAndSequenceNumber and mergeAgentSetupLogs", () => {
    it("should correctly sort and merge logs from different sources", () => {
      const existingLogs = createLogs([
        createStep(
          0,
          0,
          RemoteWorkspaceSetupStepStatus.running,
          "Container Setup",
          "Initial setup",
        ),
        createStep(1, 0, RemoteWorkspaceSetupStepStatus.running, "Step 1", "Step 1 initial"),
      ]);

      const newLogs = createLogs([
        createStep(
          0,
          1,
          RemoteWorkspaceSetupStepStatus.success,
          "Container Setup",
          "Setup complete",
        ),
        createStep(1, 1, RemoteWorkspaceSetupStepStatus.success, "Step 1", "Step 1 complete"),
        createStep(2, 0, RemoteWorkspaceSetupStepStatus.running, "Step 2", "Step 2 started"),
      ]);

      // First sort the logs
      const sortedLogs = sortByStepAndSequenceNumber(existingLogs, newLogs);
      expect(sortedLogs.steps).toHaveLength(5);

      // Then merge them
      const mergedLogs = mergeAgentSetupLogs(sortedLogs);
      expect(mergedLogs.steps).toHaveLength(3);

      // Check container setup (step 0)
      expect(mergedLogs.steps[0].step_number).toBe(0);
      expect(mergedLogs.steps[0].status).toBe(RemoteWorkspaceSetupStepStatus.success);
      expect(mergedLogs.steps[0].logs).toBe("Setup complete");

      // Check step 1
      expect(mergedLogs.steps[1].step_number).toBe(1);
      expect(mergedLogs.steps[1].status).toBe(RemoteWorkspaceSetupStepStatus.success);
      expect(mergedLogs.steps[1].logs).toBe("Step 1 initial\nStep 1 complete");

      // Check step 2
      expect(mergedLogs.steps[2].step_number).toBe(2);
      expect(mergedLogs.steps[2].logs).toBe("Step 2 started");
    });
  });
});
