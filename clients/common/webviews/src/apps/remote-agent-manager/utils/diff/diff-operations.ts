import type { Diff } from "@augment-internal/sidecar-libs/src/remote-agent/remote-agent-types";
import { generateDiff, generateDiffs, getDiffStats, isNewFile, isDeletedFile } from "./diff-utils";

/**
 * A utility class for performing diff operations
 * This class provides methods for generating diffs, analyzing them, and formatting them
 */
export class DiffOperations {
  /**
   * Generate a diff for a single file
   *
   * @param oldPath - The path of the original file
   * @param newPath - The path of the modified file
   * @param oldContent - The content of the original file
   * @param newContent - The content of the modified file
   * @returns A Diff object containing the diff, original code, and modified code
   */
  public static generateDiff(
    oldPath: string,
    newPath: string,
    oldContent: string,
    newContent: string,
  ): Diff {
    return generateDiff(oldPath, newPath, oldContent, newContent);
  }

  /**
   * Generate diffs for multiple files
   *
   * @param files - Array of file objects with old and new content
   * @returns Array of Diff objects
   */
  public static generateDiffs(
    files: Array<{
      oldPath: string;
      newPath: string;
      oldContent: string;
      newContent: string;
    }>,
  ): Diff[] {
    return generateDiffs(files);
  }

  /**
   * Calculate statistics for a diff
   *
   * @param diff - The diff string to analyze
   * @returns Object containing the number of additions and deletions
   */
  public static getDiffStats(diff: string): { additions: number; deletions: number } {
    return getDiffStats(diff);
  }

  /**
   * Calculate statistics for a Diff object
   *
   * @param diff - The Diff object to analyze
   * @returns Object containing the number of additions and deletions
   */
  public static getDiffObjectStats(diff: Diff): { additions: number; deletions: number } {
    return getDiffStats(diff.diff);
  }

  /**
   * Check if a file is new (added) based on its diff
   *
   * @param diff - The diff object to analyze
   * @returns Boolean indicating if the file is new
   */
  public static isNewFile(diff: Diff): boolean {
    return isNewFile(diff);
  }

  /**
   * Check if a file is deleted based on its diff
   *
   * @param diff - The diff object to analyze
   * @returns Boolean indicating if the file is deleted
   */
  public static isDeletedFile(diff: Diff): boolean {
    return isDeletedFile(diff);
  }
}

// Export the utility functions directly for convenience
export { generateDiff, generateDiffs, getDiffStats, isNewFile, isDeletedFile };
