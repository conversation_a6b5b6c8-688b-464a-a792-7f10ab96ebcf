import { describe, expect, it } from "vitest";
import {
  removeGitExtension,
  getRepoNameFromUrl,
  getOrgRepoFromUrl,
  getBranchUrl,
  getAgentRepoUrl,
} from "./repository-utils";
import type { RemoteAgent } from "@augment-internal/sidecar-libs/src/remote-agent/remote-agent-types";

describe("repository-utils", () => {
  describe("removeGitExtension", () => {
    it("should remove .git extension from the end of URL", () => {
      expect(removeGitExtension("https://github.com/org/repo.git")).toBe(
        "https://github.com/org/repo",
      );
    });

    it("should not remove .git from middle of URL", () => {
      expect(removeGitExtension("https://github.com/org/my.git.repo")).toBe(
        "https://github.com/org/my.git.repo",
      );
    });

    it("should not remove .git from middle of URL with .git extension", () => {
      expect(removeGitExtension("https://github.com/org/my.git.repo.git")).toBe(
        "https://github.com/org/my.git.repo",
      );
    });

    it("should handle URLs without .git extension", () => {
      expect(removeGitExtension("https://github.com/org/repo")).toBe("https://github.com/org/repo");
    });

    it("should handle empty string", () => {
      expect(removeGitExtension("")).toBe("");
    });

    it("should handle URLs with .git in domain", () => {
      expect(removeGitExtension("https://my.git.domain.com/org/repo.git")).toBe(
        "https://my.git.domain.com/org/repo",
      );
    });
  });

  describe("getRepoNameFromUrl", () => {
    it("should extract repo name from GitHub HTTPS URL", () => {
      expect(getRepoNameFromUrl("https://github.com/augmentcode/augment")).toBe("augment");
    });

    it("should extract repo name from GitHub HTTPS URL with .git", () => {
      expect(getRepoNameFromUrl("https://github.com/augmentcode/augment.git")).toBe("augment");
    });

    it("should extract repo name with .git in the name", () => {
      expect(getRepoNameFromUrl("https://github.com/augmentcode/my.git.repo")).toBe("my.git.repo");
    });

    it("should extract repo name with .git in the name and .git extension", () => {
      expect(getRepoNameFromUrl("https://github.com/augmentcode/my.git.repo.git")).toBe(
        "my.git.repo",
      );
    });

    it("should handle non-GitHub URLs", () => {
      expect(getRepoNameFromUrl("https://gitlab.com/org/project.git")).toBe("project");
    });

    it("should handle URLs with multiple path segments", () => {
      expect(getRepoNameFromUrl("https://example.com/path/to/repo.git")).toBe("repo");
    });

    it("should handle empty URL", () => {
      expect(getRepoNameFromUrl("")).toBe("");
    });

    it("should handle URL with trailing slash", () => {
      expect(getRepoNameFromUrl("https://github.com/org/repo/")).toBe("repo");
    });

    it("should handle complex repo names", () => {
      expect(getRepoNameFromUrl("https://github.com/org/my-repo_with.dots")).toBe(
        "my-repo_with.dots",
      );
    });
  });

  describe("getOrgRepoFromUrl", () => {
    it("should extract org/repo from GitHub HTTPS URL", () => {
      expect(getOrgRepoFromUrl("https://github.com/augmentcode/augment")).toBe(
        "augmentcode/augment",
      );
    });

    it("should extract org/repo from GitHub HTTPS URL with .git", () => {
      expect(getOrgRepoFromUrl("https://github.com/augmentcode/augment.git")).toBe(
        "augmentcode/augment",
      );
    });

    it("should extract org/repo with .git in repo name", () => {
      expect(getOrgRepoFromUrl("https://github.com/augmentcode/my.git.repo")).toBe(
        "augmentcode/my.git.repo",
      );
    });

    it("should extract org/repo with .git in repo name and .git extension", () => {
      expect(getOrgRepoFromUrl("https://github.com/augmentcode/my.git.repo.git")).toBe(
        "augmentcode/my.git.repo",
      );
    });

    it("should handle non-GitHub URLs", () => {
      expect(getOrgRepoFromUrl("https://gitlab.com/myorg/project.git")).toBe("myorg/project");
    });

    it("should handle URLs with single path segment", () => {
      expect(getOrgRepoFromUrl("https://example.com/repo.git")).toBe("example.com/repo");
    });

    it("should handle empty URL", () => {
      expect(getOrgRepoFromUrl("")).toBe("");
    });

    it("should handle URL with trailing slash", () => {
      expect(getOrgRepoFromUrl("https://github.com/org/repo/")).toBe("org/repo");
    });

    it("should handle URLs with additional path segments", () => {
      expect(getOrgRepoFromUrl("https://github.com/org/repo/tree/main")).toBe("org/repo");
    });
  });

  describe("getBranchUrl", () => {
    it("should construct branch URL from repo URL and branch name", () => {
      const result = getBranchUrl("https://github.com/org/repo.git", "feature-branch");
      expect(result).toBe("https://github.com/org/repo/tree/feature-branch");
    });

    it("should handle repo URL without .git extension", () => {
      const result = getBranchUrl("https://github.com/org/repo", "main");
      expect(result).toBe("https://github.com/org/repo/tree/main");
    });

    it("should handle empty repo URL", () => {
      const result = getBranchUrl("", "main");
      expect(result).toBe("");
    });

    it("should handle empty branch name", () => {
      const result = getBranchUrl("https://github.com/org/repo", "");
      expect(result).toBe("https://github.com/org/repo");
    });

    it("should handle repo with .git in name", () => {
      const result = getBranchUrl("https://github.com/org/my.git.repo.git", "main");
      expect(result).toBe("https://github.com/org/my.git.repo/tree/main");
    });
  });

  describe("getAgentRepoUrl", () => {
    it("should extract repository URL from agent", () => {
      const agent: RemoteAgent = {
        // eslint-disable-next-line @typescript-eslint/naming-convention
        workspace_setup: {
          // eslint-disable-next-line @typescript-eslint/naming-convention
          starting_files: {
            // eslint-disable-next-line @typescript-eslint/naming-convention
            github_commit_ref: {
              // eslint-disable-next-line @typescript-eslint/naming-convention
              repository_url: "https://github.com/org/repo.git",
            },
          },
        },
      } as RemoteAgent;

      expect(getAgentRepoUrl(agent)).toBe("https://github.com/org/repo.git");
    });

    it("should return empty string for agent without repository URL", () => {
      const agent: RemoteAgent = {} as RemoteAgent;
      expect(getAgentRepoUrl(agent)).toBe("");
    });

    it("should return empty string for agent with partial workspace setup", () => {
      const agent: RemoteAgent = {
        // eslint-disable-next-line @typescript-eslint/naming-convention
        workspace_setup: {
          // eslint-disable-next-line @typescript-eslint/naming-convention
          starting_files: {},
        },
      } as RemoteAgent;
      expect(getAgentRepoUrl(agent)).toBe("");
    });
  });
});
