import { describe, it, expect, beforeEach, afterEach, vi } from "vitest";
import { shouldWarnOfRemoteAgentDeletion } from "./agent-deletion";
import {
  type RemoteAgent,
  RemoteAgentStatus,
  RemoteAgentWorkspaceStatus,
} from "@augment-internal/sidecar-libs/src/remote-agent/remote-agent-types";

// Helper function to create a mock agent
function createMockAgent(expiresAt: Date, updatedAt: Date): RemoteAgent {
  return {
    /* eslint-disable @typescript-eslint/naming-convention */
    remote_agent_id: "test-agent",
    status: RemoteAgentStatus.agentIdle,
    started_at: new Date().toISOString(),
    updated_at: updatedAt.toISOString(),
    session_summary: "Test session",
    turn_summaries: [],
    workspace_status: RemoteAgentWorkspaceStatus.workspaceRunning,
    expires_at: expiresAt.toISOString(),
    /* eslint-enable @typescript-eslint/naming-convention */
  };
}

describe("shouldWarnOfRemoteAgentDeletion", () => {
  const now = new Date("2024-01-15T12:00:00Z");

  // Mock Date.now() to return a consistent time
  beforeEach(() => {
    vi.spyOn(Date, "now").mockReturnValue(now.getTime());
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe("without autoResumeDays", () => {
    it("should warn when agent expires within warning period", () => {
      const expiresAt = new Date(now.getTime() + 5 * 24 * 60 * 60 * 1000); // 5 days from now
      const updatedAt = new Date(now.getTime() - 1 * 24 * 60 * 60 * 1000); // 1 day ago
      const agent = createMockAgent(expiresAt, updatedAt);

      expect(shouldWarnOfRemoteAgentDeletion(agent)).toBe(true);
    });

    it("should not warn when agent expires outside warning period", () => {
      const expiresAt = new Date(now.getTime() + 10 * 24 * 60 * 60 * 1000); // 10 days from now
      const updatedAt = new Date(now.getTime() - 1 * 24 * 60 * 60 * 1000); // 1 day ago
      const agent = createMockAgent(expiresAt, updatedAt);

      expect(shouldWarnOfRemoteAgentDeletion(agent)).toBe(false);
    });

    it("should not warn when agent has no expiration date", () => {
      const agent = createMockAgent(new Date(), new Date());
      agent.expires_at = "";

      expect(shouldWarnOfRemoteAgentDeletion(agent)).toBe(false);
    });
  });

  describe("with autoResumeDays", () => {
    const autoResumeDays = 21;

    it("should not warn when still within auto-resume window", () => {
      const expiresAt = new Date(now.getTime() + 5 * 24 * 60 * 60 * 1000); // 5 days from now
      const updatedAt = new Date(now.getTime() - 10 * 24 * 60 * 60 * 1000); // 10 days ago (still within 21-day window)
      const agent = createMockAgent(expiresAt, updatedAt);

      expect(shouldWarnOfRemoteAgentDeletion(agent, autoResumeDays)).toBe(false);
    });

    it("should warn when past auto-resume window and within deletion warning period", () => {
      const expiresAt = new Date(now.getTime() + 5 * 24 * 60 * 60 * 1000); // 5 days from now
      const updatedAt = new Date(now.getTime() - 25 * 24 * 60 * 60 * 1000); // 25 days ago (past 21-day window)
      const agent = createMockAgent(expiresAt, updatedAt);

      expect(shouldWarnOfRemoteAgentDeletion(agent, autoResumeDays)).toBe(true);
    });

    it("should not warn when past auto-resume window but outside deletion warning period", () => {
      const expiresAt = new Date(now.getTime() + 10 * 24 * 60 * 60 * 1000); // 10 days from now
      const updatedAt = new Date(now.getTime() - 25 * 24 * 60 * 60 * 1000); // 25 days ago (past 21-day window)
      const agent = createMockAgent(expiresAt, updatedAt);

      expect(shouldWarnOfRemoteAgentDeletion(agent, autoResumeDays)).toBe(false);
    });

    it("should handle edge case where agent was just updated (at boundary of auto-resume window)", () => {
      const expiresAt = new Date(now.getTime() + 5 * 24 * 60 * 60 * 1000); // 5 days from now
      const updatedAt = new Date(now.getTime() - autoResumeDays * 24 * 60 * 60 * 1000); // exactly at auto-resume boundary
      const agent = createMockAgent(expiresAt, updatedAt);

      expect(shouldWarnOfRemoteAgentDeletion(agent, autoResumeDays)).toBe(true);
    });
  });
});
