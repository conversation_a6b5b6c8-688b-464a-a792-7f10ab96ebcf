import { type RemoteAgentExchange } from "@augment-internal/sidecar-libs/src/remote-agent/remote-agent-types";

/**
 * Given two chat histories, merge the new history into the existing one based on sequence IDs.
 *
 * The new history replaces any exchanges with the same sequence ID in the existing history.
 * Both histories must be monotonically increasing in sequence ID. This is also
 * reinforced by the backend and beachhead.
 *
 */
export function mergeRemoteAgentChatHistory(
  existingHistory: RemoteAgentExchange[],
  newHistory: RemoteAgentExchange[],
): RemoteAgentExchange[] {
  if (existingHistory.length === 0) {
    return newHistory;
  }
  if (newHistory.length === 0) {
    return existingHistory;
  }

  const mergedHistory: RemoteAgentExchange[] = [];
  let i = 0;
  let j = 0;

  while (i < existingHistory.length && j < newHistory.length) {
    const existingSequenceId = existingHistory[i].sequence_id;
    const newSequenceId = newHistory[j].sequence_id;
    // There should always be a sequence ID but if there isn't we will ignore
    // this exchange
    if (newSequenceId === undefined) {
      console.warn("New history has an exchange with an undefined sequence ID");
      j++;
      continue;
    }
    if (existingSequenceId === undefined) {
      console.warn("Existing history has an exchange with an undefined sequence ID");
      i++;
      continue;
    }
    if (existingSequenceId < newSequenceId) {
      mergedHistory.push(existingHistory[i]);
      i++;
    } else if (existingSequenceId > newSequenceId) {
      mergedHistory.push(newHistory[j]);
      j++;
    } else {
      // Replace the existing exchange with the new one
      mergedHistory.push(newHistory[j]);
      i++;
      j++;
    }
  }

  // Add any remaining exchanges from either history
  while (i < existingHistory.length) {
    mergedHistory.push(existingHistory[i]);
    i++;
  }
  while (j < newHistory.length) {
    mergedHistory.push(newHistory[j]);
    j++;
  }

  return mergedHistory;
}
