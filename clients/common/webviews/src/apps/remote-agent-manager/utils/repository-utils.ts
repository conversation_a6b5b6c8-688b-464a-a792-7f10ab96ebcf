import { derived, type Readable } from "svelte/store";
import type { RemoteAgent } from "@augment-internal/sidecar-libs/src/remote-agent/remote-agent-types";
import { getLocalBranchName } from "./git";

/**
 * Utility functions for working with repository URLs and references
 */

export function removeGitExtension(url: string): string {
  return url.replace(/\.git$/, "");
}

/**
 * Extract the repository name from a URL
 * @param url The repository URL
 * @returns The repository name
 */
export function getRepoNameFromUrl(url: string): string {
  if (!url) return "";

  // Handle different URL formats
  // For GitHub URLs like https://github.com/org/repo
  const githubMatch = url.match(/github\.com\/([^/]+)\/([^/]+)/);
  if (githubMatch) {
    return githubMatch[2].replace(/\.git$/, ""); // Return just the repo name, without .git
  }

  // For other URLs, try to extract the last part
  const parts = url.split("/").filter(Boolean);
  const lastPart = parts.length > 0 ? parts[parts.length - 1] : "";
  return lastPart.replace(/\.git$/, "");
}

/**
 * Extract the organization/repository from a URL
 * @param url The repository URL
 * @returns The org/repo string
 */
export function getOrgRepoFromUrl(url: string): string {
  if (!url) return "";

  // Handle different URL formats
  // For GitHub URLs like https://github.com/org/repo
  const githubMatch = url.match(/github\.com\/([^/]+)\/([^/]+)/);
  if (githubMatch) {
    return `${githubMatch[1]}/${githubMatch[2].replace(/\.git$/, "")}`; // Return org/repo without .git
  }

  // For other URLs, try to extract the last two parts
  const parts = url.split("/").filter(Boolean);
  return parts.length > 1
    ? `${parts[parts.length - 2]}/${parts[parts.length - 1].replace(/\.git$/, "")}`
    : parts.length > 0
      ? parts[parts.length - 1].replace(/\.git$/, "")
      : "";
}

/**
 * Constructs a valid branch URL for GitHub
 * @param repoUrl The base repository URL
 * @param branchName The branch name
 * @returns A valid URL to the branch on GitHub
 */
export function getBranchUrl(repoUrl: string, branchName: string): string {
  if (!repoUrl || !branchName) return repoUrl;

  // Clean up the repository URL
  const cleanRepoUrl = removeGitExtension(repoUrl);

  // Clean up the branch name (remove remote prefix if present)
  const cleanBranchName = getLocalBranchName(branchName);

  // Construct the URL to the branch
  return `${cleanRepoUrl}/tree/${cleanBranchName}`;
}

/** Get the repository URL from an agent */
export function getAgentRepoUrl(agent: RemoteAgent): string {
  return agent.workspace_setup?.starting_files?.github_commit_ref?.repository_url || "";
}

export type RepoInfo = { name: string; fullPath: string };
export function getRepoInfo(agent: RemoteAgent): RepoInfo {
  if (!agent || typeof agent !== "object" || !("workspace_setup" in agent)) {
    return { name: "Unknown", fullPath: "Unknown" };
  }
  const repoUrl = getAgentRepoUrl(agent);
  return {
    name: getRepoNameFromUrl(repoUrl),
    fullPath: getOrgRepoFromUrl(repoUrl),
  };
}

export function isFromDifferentRepo(agent: RemoteAgent, currentRepoUrl: string): boolean {
  if (!agent || typeof agent !== "object" || !("workspace_setup" in agent)) {
    return false;
  }
  const agentRepoUrl = getAgentRepoUrl(agent);
  return !!agentRepoUrl && agentRepoUrl !== currentRepoUrl;
}

/**
 * Create a store to track whether an agent is from a different repository
 * @param currentRepoUrl The current repository URL
 * @returns A store that returns a function to check if an agent is from a different repository
 */
export function isAgentFromDifferentRepoStore(currentRepoUrl: Readable<string>) {
  return derived(currentRepoUrl, ($currentRepoUrl) => (agent: RemoteAgent): boolean => {
    if (!$currentRepoUrl) {
      return true;
    }
    const currentRepoUrl = removeGitExtension($currentRepoUrl);
    const agentRepoUrl = removeGitExtension(getAgentRepoUrl(agent));
    const result = !!agentRepoUrl && !!currentRepoUrl && agentRepoUrl !== currentRepoUrl;
    return result;
  });
}
