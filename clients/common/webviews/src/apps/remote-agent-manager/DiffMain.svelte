<script lang="ts">
  import { host } from "$common-webviews/src/common/hosts/host";
  import { MessageBroker } from "$common-webviews/src/common/utils/message-broker";
  import { onMount } from "svelte";
  import { RemoteAgentDiffModel } from "./models/remote-agent-diff-model";
  import { RemoteAgentDiffOpsModel } from "./models/ra-diff-ops-model";
  import SpinnerAugment from "$common-webviews/src/design-system/components/SpinnerAugment.svelte";
  import RemoteAgentDiffPage from "./components/RemoteAgentDiffPage.svelte";
  import { setContext } from "svelte";
  import MonacoProvider from "$common-webviews/src/design-system/components/MonacoProvider";

  /**
   * Main component for the remote agent diff panel webview
   */

  let msgBroker = new MessageBroker(host);
  let remoteAgentDiffModel = new RemoteAgentDiffModel(msgBroker);
  msgBroker.registerConsumer(remoteAgentDiffModel);

  let diffOperationsModel = new RemoteAgentDiffOpsModel(msgBroker);
  setContext(RemoteAgentDiffOpsModel.key, diffOperationsModel);
  setContext(RemoteAgentDiffModel.key, remoteAgentDiffModel);

  $: opts = $remoteAgentDiffModel.opts;

  onMount(() => {
    remoteAgentDiffModel.onPanelLoaded();

    return () => {
      msgBroker.dispose();
    };
  });
</script>

<svelte:window on:message={msgBroker.onMessageFromExtension} />
<MonacoProvider.Root>
  <div class="l-main">
    {#if opts}
      <RemoteAgentDiffPage
        changedFiles={opts.changedFiles}
        agentLabel={opts.sessionSummary}
        latestUserPrompt={opts.userPrompt}
        pendingFiles={diffOperationsModel.applyingFilePaths || []}
        appliedFiles={diffOperationsModel.appliedFilePaths || []}
        isAgentFromDifferentRepo={opts.isAgentFromDifferentRepo || false}
      />
    {:else}
      <div class="l-center">
        <SpinnerAugment size={1} />
        <p>Loading diff view...</p>
      </div>
    {/if}
  </div>
</MonacoProvider.Root>

<style>
  .l-main {
    height: 100vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    padding: var(--ds-spacing-2) var(--ds-spacing-2) 0 var(--ds-spacing-2);
    background-color: var(--ds-surface);
  }

  .l-center {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: var(--ds-spacing-2);
    height: 100%;
  }
</style>
