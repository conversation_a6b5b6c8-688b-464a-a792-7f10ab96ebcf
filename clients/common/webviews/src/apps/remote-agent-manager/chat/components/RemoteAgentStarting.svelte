<script lang="ts">
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import SpinnerAugment from "$common-webviews/src/design-system/components/SpinnerAugment.svelte";

  /** Placeholder component to show while a remote agent is starting */
</script>

<div class="l-center-contents">
  <SpinnerAugment size={1} />
  <TextAugment size={1} color="secondary">Starting remote agent...</TextAugment>
</div>

<style>
  .l-center-contents {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--ds-spacing-2);
  }
</style>
