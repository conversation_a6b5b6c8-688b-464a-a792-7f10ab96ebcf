<script lang="ts">
  import CalloutAugment from "$common-webviews/src/design-system/components/CalloutAugment.svelte";
  import {
    type CommitRef,
    type GithubBranch,
  } from "@augment-internal/sidecar-libs/src/remote-agent/remote-agent-types";
  import { type SetupScript } from "$vscode/src/utils/remote-agent-setup/types";
  import {
    RemoteAgentSessionEventName,
    RemoteAgentSetupWindowAction,
  } from "@augment-internal/sidecar-libs/src/metrics/types";
  import { getContext, onDestroy, onMount } from "svelte";
  import { fly } from "svelte/transition";
  import CommitRefSelector from "../../components/CommitRefSelector.svelte";
  import SetupScriptSelector from "../../components/SetupScriptSelector/SetupScriptSelector.svelte";

  import { RemoteAgentsModel } from "../../models/remote-agents-model";
  import AgentLimitCard from "./AgentLimitCard.svelte";
  import SeparatorAugment from "$common-webviews/src/design-system/components/SeparatorAugment.svelte";

  /** Component to display the remote agent setup screen in chat */

  const remoteAgentsModel = getContext<RemoteAgentsModel>(RemoteAgentsModel.key);

  $: commitRef = $remoteAgentsModel.newAgentDraft?.commitRef ?? null;
  $: selectedBranch = $remoteAgentsModel.newAgentDraft?.selectedBranch ?? null;

  let commitRefErrorMessage = "";
  let isCommitRefLoading = false;

  let agentLimitErrorMessage: string | undefined;

  // Add variables to store the last used values
  let lastUsedRepoUrl: string | null = null;
  let lastUsedBranch: string | null = null;
  let lastUsedSetupScript: string | null = null;

  // Reactive computation for error message and disabled state
  $: errorMessage = (() => {
    // Check if we have valid repository and branch data
    const hasValidRepo = commitRef?.github_commit_ref?.repository_url;
    const hasValidBranch = selectedBranch?.name;

    // Additional check: ensure we're not in a loading state where branch data might be incomplete
    const isDataComplete = !isCommitRefLoading && hasValidRepo && hasValidBranch;

    return (
      commitRefErrorMessage ||
      agentLimitErrorMessage ||
      (isCommitRefLoading ? "Loading repos and branches..." : "") ||
      (!hasValidRepo && "Please select a repository") ||
      (!hasValidBranch && "Please select a branch") ||
      (!isDataComplete && hasValidRepo && hasValidBranch ? "Loading branch data..." : "") ||
      ""
    );
  })();

  // Reactive computation for the current disabled state
  $: currentIsDisabled = !!errorMessage;

  // Load the last used values when the component is mounted
  onMount(async () => {
    try {
      const lastSetup = await remoteAgentsModel.getLastRemoteAgentSetup();
      lastUsedRepoUrl = lastSetup.lastRemoteAgentGitRepoUrl;
      lastUsedBranch = lastSetup.lastRemoteAgentGitBranch;
      lastUsedSetupScript = lastSetup.lastRemoteAgentSetupScript;

      // Mark that the user has seen the remote agent interface
      // This will make the chaser disappear from the mode toggle button
      remoteAgentsModel.setHasEverUsedRemoteAgent(true);

      // Report the setup page opened event
      await remoteAgentsModel.reportRemoteAgentEvent({
        eventName: RemoteAgentSessionEventName.setupPageOpened,
        remoteAgentId: "",
        eventData: {
          setupPageOpened: {},
        },
      });

      // Report the remote agent setup window open event
      await remoteAgentsModel.reportRemoteAgentSetupWindowEvent(RemoteAgentSetupWindowAction.open);
    } catch (error) {
      console.error("Failed to load last remote agent setup:", error);
    }
  });

  async function handleCommitRefChange(
    event: CustomEvent<{
      commitRef: CommitRef;
      selectedBranch: GithubBranch;
    }>,
  ) {
    remoteAgentsModel.setRemoteAgentCreationError(null);

    const currentDraft = remoteAgentsModel.newAgentDraft;
    if (currentDraft) {
      remoteAgentsModel.setNewAgentDraft({
        ...currentDraft,
        commitRef: event.detail.commitRef,
        selectedBranch: event.detail.selectedBranch,
        isDisabled: currentIsDisabled,
      });
    } else {
      remoteAgentsModel.setNewAgentDraft({
        commitRef: event.detail.commitRef,
        selectedBranch: event.detail.selectedBranch,
        setupScript: null,
        isDisabled: currentIsDisabled,
        enableNotification: true,
      });
    }
  }

  function handleSetupScriptChange(event: CustomEvent<{ script: SetupScript | null }>) {
    remoteAgentsModel.setRemoteAgentCreationError(null);
    const currentDraft = remoteAgentsModel.newAgentDraft;

    if (currentDraft) {
      remoteAgentsModel.setNewAgentDraft({
        ...currentDraft,
        setupScript: event.detail.script,
        isDisabled: currentIsDisabled,
      });
    } else {
      remoteAgentsModel.setNewAgentDraft({
        commitRef: null,
        selectedBranch: null,
        setupScript: event.detail.script,
        isDisabled: currentIsDisabled,
        enableNotification: true,
      });
    }
  }

  // Reactively update the draft's isDisabled state when it changes
  $: if (
    remoteAgentsModel.newAgentDraft &&
    remoteAgentsModel.newAgentDraft.isDisabled !== currentIsDisabled
  ) {
    remoteAgentsModel.setNewAgentDraft({
      ...remoteAgentsModel.newAgentDraft,
      isDisabled: currentIsDisabled,
    });
  }

  onDestroy(async () => {
    try {
      // Report the remote agent setup window close event
      await remoteAgentsModel.reportRemoteAgentSetupWindowEvent(RemoteAgentSetupWindowAction.close);
    } catch (error) {
      console.error("Failed to report remote agent setup window close event:", error);
    }

    remoteAgentsModel.setNewAgentDraft(null);
    remoteAgentsModel.setCreationMetrics(undefined);
  });
</script>

<div class="remote-agent-setup">
  <div class="content">
    <div class="form-fields">
      <div class="error-message" transition:fly={{ y: 10 }}>
        <AgentLimitCard bind:agentLimitErrorMessage />
      </div>
      {#if $remoteAgentsModel.remoteAgentCreationError}
        <div class="error-message" transition:fly={{ y: 10 }}>
          <CalloutAugment color="error" variant="soft" size={2}>
            {$remoteAgentsModel.remoteAgentCreationError}
          </CalloutAugment>
        </div>
      {/if}
      <div class="commit-ref-selector">
        <CommitRefSelector
          on:commitRefChange={handleCommitRefChange}
          {lastUsedRepoUrl}
          lastUsedBranchName={lastUsedBranch}
          bind:errorMessage={commitRefErrorMessage}
          bind:isLoading={isCommitRefLoading}
        />
      </div>
      <div class="setup-script">
        <SetupScriptSelector
          on:setupScriptChange={handleSetupScriptChange}
          lastUsedScriptPath={lastUsedSetupScript}
        />
        <SeparatorAugment size={4} />
      </div>
    </div>
  </div>
</div>

<style>
  .commit-ref-selector {
    padding: 0 var(--remote-agent-setup-horizontal-padding) var(--ds-spacing-1);
  }

  .remote-agent-setup {
    display: flex;
    flex-direction: column;
    height: 100%;
    width: 100%;
    --remote-agent-setup-horizontal-padding: var(--ds-spacing-1);
  }

  @media (min-width: 300px) {
    .remote-agent-setup {
      --remote-agent-setup-horizontal-padding: var(--ds-spacing-1);
    }
  }

  .content {
    display: flex;
    flex-direction: column;
    gap: 0;
  }

  .setup-script {
    margin: 0 var(--remote-agent-setup-horizontal-padding);
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-2);
  }

  .error-message {
    margin-top: var(--ds-spacing-1);
    margin-bottom: var(--ds-spacing-2);
    margin-left: var(--remote-agent-setup-horizontal-padding);
    margin-right: var(--remote-agent-setup-horizontal-padding);
  }
</style>
