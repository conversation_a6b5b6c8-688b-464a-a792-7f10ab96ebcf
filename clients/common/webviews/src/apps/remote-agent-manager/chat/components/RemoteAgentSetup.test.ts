import { render } from "@testing-library/svelte";
import { tick } from "svelte";
import { get } from "svelte/store";
import { afterEach, beforeEach, describe, expect, test, vi } from "vitest";
import { RemoteAgentsModel } from "../../models/remote-agents-model";
import { GitReferenceModel } from "../../models/git-reference-model";
import { MessageBroker } from "$common-webviews/src/common/utils/message-broker";
import { host } from "$common-webviews/src/common/hosts/__mocks__/host";
import { RemoteAgentSetupWindowAction } from "@augment-internal/sidecar-libs/src/metrics/types";
import RemoteAgentSetup from "./RemoteAgentSetup.svelte";
import { WebViewMessageType } from "$vscode/src/webview-providers/webview-messages";

describe("RemoteAgentSetup Component", () => {
  let remoteAgentsModel: RemoteAgentsModel;
  let gitRefModel: GitReferenceModel;
  let messageBroker: MessageBroker;
  let mockChatModel: any;

  beforeEach(() => {
    // Setup host mock
    host.postMessage.mockImplementation((msg) => {
      switch (msg.type) {
        case WebViewMessageType.asyncWrapper: {
          switch (msg.baseMsg?.type) {
            case WebViewMessageType.getRemoteAgentNotificationEnabledRequest: {
              window.dispatchEvent(
                new MessageEvent("message", {
                  data: {
                    type: WebViewMessageType.asyncWrapper,
                    requestId: msg.requestId,
                    error: null,
                    baseMsg: {
                      type: WebViewMessageType.getRemoteAgentNotificationEnabledResponse,
                      data: {}, // Empty notification settings
                    },
                    streamCtx: undefined,
                  },
                }),
              );
              break;
            }
            case WebViewMessageType.getRemoteAgentStatus:
              window.dispatchEvent(
                new MessageEvent("message", {
                  data: {
                    type: WebViewMessageType.asyncWrapper,
                    requestId: msg.requestId,
                    error: null,
                    baseMsg: {
                      type: WebViewMessageType.remoteAgentStatusResponse,
                      data: { isRemoteAgentSshWindow: false },
                    },
                    streamCtx: undefined,
                  },
                }),
              );
              break;
          }
          break;
        }
      }
    });

    // Initialize dependencies
    messageBroker = new MessageBroker(host);

    const flagsModel = {
      enableBackgroundAgents: true,
      subscribe: vi.fn(),
    } as any;

    gitRefModel = new GitReferenceModel(messageBroker);

    // Mock GitReferenceModel methods that child components use
    vi.spyOn(gitRefModel, "listUserRepos").mockResolvedValue({
      repos: [],
      error: undefined,
      isDevDeploy: false,
      hasNextPage: false,
      nextPage: 0,
    });
    vi.spyOn(gitRefModel, "isGithubAuthenticated").mockResolvedValue(true);
    vi.spyOn(gitRefModel, "listBranches").mockResolvedValue({
      branches: [],
      defaultBranch: undefined,
      currentBranch: undefined,
    });
    vi.spyOn(gitRefModel, "getCurrentLocalBranch").mockResolvedValue("main");
    vi.spyOn(gitRefModel, "getRemoteUrl").mockResolvedValue({
      remoteUrl: "https://github.com/test/repo",
    });

    // Create mock ChatModel with extensionClient
    mockChatModel = {
      extensionClient: {
        openFile: vi.fn(),
        reportWebviewClientEvent: vi.fn(),
      },
    };

    remoteAgentsModel = new RemoteAgentsModel({
      msgBroker: messageBroker,
      isActive: false,
      flagsModel,
      host,
      gitRefModel,
    });

    // Mock RemoteAgentsModel methods to avoid network calls
    vi.spyOn(remoteAgentsModel, "getLastRemoteAgentSetup").mockResolvedValue({
      lastRemoteAgentGitRepoUrl: null,
      lastRemoteAgentGitBranch: null,
      lastRemoteAgentSetupScript: null,
    });
    vi.spyOn(remoteAgentsModel, "reportRemoteAgentEvent").mockResolvedValue();
    vi.spyOn(remoteAgentsModel, "reportRemoteAgentSetupWindowEvent").mockResolvedValue();
  });

  afterEach(() => {
    if (remoteAgentsModel) {
      remoteAgentsModel.dispose();
    }
    vi.clearAllTimers();
    vi.restoreAllMocks();
  });

  test("should render component and report setup window open event on mount", async () => {
    const reportSpy = vi.spyOn(remoteAgentsModel, "reportRemoteAgentSetupWindowEvent");
    const getLastSetupSpy = vi.spyOn(remoteAgentsModel, "getLastRemoteAgentSetup");

    const { container } = render(RemoteAgentSetup, {
      context: new Map([
        [RemoteAgentsModel.key, remoteAgentsModel],
        [GitReferenceModel.key, gitRefModel],
        ["chatModel", mockChatModel],
      ]),
    });

    // Wait for onMount to complete - need to wait for async operations
    await tick();
    await new Promise((resolve) => setTimeout(resolve, 10)); // Give async operations time to complete

    // Verify component rendered
    expect(container.querySelector(".remote-agent-setup")).toBeTruthy();
    // The component may show loading state initially, so check for either loading or selector text
    expect(
      container.textContent?.includes("Loading repositories...") ||
        container.textContent?.includes("Choose repository..."),
    ).toBeTruthy();
    expect(container.textContent).toContain("Choose branch...");

    // Verify getLastRemoteAgentSetup was called
    expect(getLastSetupSpy).toHaveBeenCalled();

    // Verify setup window open event was reported
    expect(reportSpy).toHaveBeenCalledWith(RemoteAgentSetupWindowAction.open);

    // Verify hasEverUsedRemoteAgent was set to true
    expect(get(remoteAgentsModel.hasEverUsedRemoteAgent)).toBe(true);
  });

  test("should report setup window close event when component is destroyed", async () => {
    const reportSpy = vi.spyOn(remoteAgentsModel, "reportRemoteAgentSetupWindowEvent");

    const { unmount } = render(RemoteAgentSetup, {
      context: new Map([
        [RemoteAgentsModel.key, remoteAgentsModel],
        [GitReferenceModel.key, gitRefModel],
        ["chatModel", mockChatModel],
      ]),
    });

    // Wait for onMount to complete
    await tick();

    // Set up some draft data to verify cleanup
    remoteAgentsModel.setNewAgentDraft({
      commitRef: null,
      selectedBranch: null,
      setupScript: null,
      isDisabled: false,
      enableNotification: true,
    });

    // Unmount the component to trigger onDestroy
    unmount();
    await tick();

    // Verify setup window close event was reported
    expect(reportSpy).toHaveBeenCalledWith(RemoteAgentSetupWindowAction.close);

    // Verify cleanup occurred
    expect(remoteAgentsModel.newAgentDraft).toBe(null);
    expect(remoteAgentsModel.creationMetrics).toBe(undefined);
  });

  test("should handle errors in event reporting gracefully", async () => {
    const consoleSpy = vi.spyOn(console, "error").mockImplementation(() => {});
    // Make getLastRemoteAgentSetup fail to trigger the error handling
    vi.spyOn(remoteAgentsModel, "getLastRemoteAgentSetup").mockRejectedValue(
      new Error("Network error"),
    );

    render(RemoteAgentSetup, {
      context: new Map([
        [RemoteAgentsModel.key, remoteAgentsModel],
        [GitReferenceModel.key, gitRefModel],
        ["chatModel", mockChatModel],
      ]),
    });

    // Wait for onMount to complete
    await tick();
    await new Promise((resolve) => setTimeout(resolve, 10)); // Give async operations time to complete

    // Verify error was logged
    expect(consoleSpy).toHaveBeenCalledWith(
      "Failed to load last remote agent setup:",
      expect.any(Error),
    );

    consoleSpy.mockRestore();
  });

  test("should display error messages when present", async () => {
    // Set up an error in the model
    remoteAgentsModel.setRemoteAgentCreationError("Test error message");

    const { container } = render(RemoteAgentSetup, {
      context: new Map([
        [RemoteAgentsModel.key, remoteAgentsModel],
        [GitReferenceModel.key, gitRefModel],
        ["chatModel", mockChatModel],
      ]),
    });

    await tick();

    // Verify error message is displayed
    expect(container.textContent).toContain("Test error message");
    expect(container.querySelector(".error-message")).toBeTruthy();
  });

  test("should render commit ref selector and setup script selector", async () => {
    const { container } = render(RemoteAgentSetup, {
      context: new Map([
        [RemoteAgentsModel.key, remoteAgentsModel],
        [GitReferenceModel.key, gitRefModel],
        ["chatModel", mockChatModel],
      ]),
    });

    await tick();

    // Verify both selectors are present
    expect(container.querySelector(".commit-ref-selector")).toBeTruthy();
    expect(container.querySelector(".setup-script")).toBeTruthy();

    // Verify descriptive text is present
    expect(container.textContent).toContain("Choose repository...");
    expect(container.textContent).toContain("Use basic environment");
  });
});
