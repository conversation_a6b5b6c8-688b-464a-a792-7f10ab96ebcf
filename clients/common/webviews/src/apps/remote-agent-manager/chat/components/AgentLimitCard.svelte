<script lang="ts">
  import {
    type RemoteAgent,
    RemoteAgentWorkspaceStatus,
  } from "@augment-internal/sidecar-libs/src/remote-agent/remote-agent-types";
  import { getContext } from "svelte";
  import { RemoteAgentsModel } from "../../models/remote-agents-model";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import CalloutAugment from "$common-webviews/src/design-system/components/CalloutAugment.svelte";
  import ExclamationTriangle from "$common-webviews/src/design-system/icons/exclamation-triangle.svelte";
  import Trash from "$common-webviews/src/design-system/icons/trash.svelte";
  import { AGENT_LIMIT_MSG, ACTIVE_AGENT_LIMIT_MSG } from "../../constants";
  import RegularPauseIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/pause.svg?component";

  export let agentLimitErrorMessage: string | undefined = undefined;

  type AgentIdAndTitle = {
    id: string;
    title: string;
  };

  const remoteAgentsModel = getContext<RemoteAgentsModel>(RemoteAgentsModel.key);

  let isDeletingOldestAgent = false;
  let oldestAgent: AgentIdAndTitle | undefined;
  let oldestActiveAgent: AgentIdAndTitle | undefined;
  let warningButtonText: string | undefined;

  $: totalAgentLimitReached =
    !!$remoteAgentsModel.maxRemoteAgents &&
    $remoteAgentsModel.agentOverviews.length >= $remoteAgentsModel.maxRemoteAgents;
  $: activeAgentLimitReached =
    !!$remoteAgentsModel.maxActiveRemoteAgents &&
    $remoteAgentsModel.agentOverviews.filter(
      (a) => a.workspace_status === RemoteAgentWorkspaceStatus.workspaceRunning,
    ).length >= $remoteAgentsModel.maxActiveRemoteAgents;

  let sortedAgents: RemoteAgent[] = [];

  $: {
    if (totalAgentLimitReached) {
      sortedAgents = sortAgentsByTime();
      oldestAgent = getAgentIdAndTitle(sortedAgents[0]);
      agentLimitErrorMessage = createErrorMessage(
        AGENT_LIMIT_MSG,
        $remoteAgentsModel.maxRemoteAgents,
      );
      warningButtonText = `Delete Oldest Agent${oldestAgent ? `: ${oldestAgent.title}` : ""}`;
    } else if (activeAgentLimitReached) {
      sortedAgents = sortAgentsByTime();
      const activeAgents = sortedAgents.filter(
        (a) => a.workspace_status === RemoteAgentWorkspaceStatus.workspaceRunning,
      );
      oldestActiveAgent = getAgentIdAndTitle(activeAgents[0]);
      agentLimitErrorMessage = createErrorMessage(
        ACTIVE_AGENT_LIMIT_MSG,
        $remoteAgentsModel.maxActiveRemoteAgents,
      );
      warningButtonText = `Pause Oldest Agent${oldestActiveAgent ? `: ${oldestActiveAgent.title}` : ""}`;
    } else {
      oldestAgent = undefined;
      agentLimitErrorMessage = undefined;
    }
  }

  function sortAgentsByTime() {
    return $remoteAgentsModel.agentOverviews.sort((a, b) => {
      const aTime = new Date(a.started_at).getTime();
      const bTime = new Date(b.started_at).getTime();
      return aTime - bTime;
    });
  }

  function getAgentIdAndTitle(agent: RemoteAgent | undefined) {
    if (!agent) return undefined;
    const title = agent.is_setup_script_agent
      ? "Setup script generation"
      : agent.session_summary || "";
    return {
      id: agent.remote_agent_id,
      title: title.length > 30 ? title.substring(0, 27) + "..." : title,
    };
  }

  /**
   * Creates the error message to display
   * @param message The message to display. Must contain the placeholder %MAX_AGENTS%
   * @param maxAgents The maximum number of agents
   */
  function createErrorMessage(message: string, maxAgents: number) {
    return message.replace("%MAX_AGENTS%", maxAgents.toString());
  }

  /**
   * Deletes the oldest agent based on creation time
   */
  async function deleteOldestAgent() {
    if (isDeletingOldestAgent || !oldestAgent?.id) return;

    try {
      isDeletingOldestAgent = true;

      // Delete the oldest agent using the stored ID
      await remoteAgentsModel.deleteAgent(oldestAgent.id);
    } catch (error) {
      console.error("Failed to delete oldest agent:", error);
    } finally {
      isDeletingOldestAgent = false;
    }
  }

  /**
   * Pauses the oldest active agent based on creation time
   */
  async function pauseOldestActiveAgent() {
    if (isDeletingOldestAgent || !oldestActiveAgent?.id) return;

    try {
      isDeletingOldestAgent = true;

      // Pause the oldest active agent using the stored ID
      await remoteAgentsModel.pauseRemoteAgentWorkspace(oldestActiveAgent.id);
    } catch (error) {
      console.error("Failed to pause oldest active agent:", error);
    } finally {
      isDeletingOldestAgent = false;
    }
  }
</script>

{#if !!agentLimitErrorMessage}
  <CalloutAugment color="info" variant="soft" size={2}>
    <ExclamationTriangle slot="icon" />
    <div class="agent-limit-message">
      <p>
        {(totalAgentLimitReached ? AGENT_LIMIT_MSG : ACTIVE_AGENT_LIMIT_MSG).replace(
          "%MAX_AGENTS%",
          (totalAgentLimitReached
            ? $remoteAgentsModel.maxRemoteAgents
            : $remoteAgentsModel.maxActiveRemoteAgents
          ).toString(),
        )}
      </p>
      <ButtonAugment
        variant="soft"
        color="neutral"
        size={1}
        on:click={() => {
          if (activeAgentLimitReached) {
            pauseOldestActiveAgent();
            return;
          }
          deleteOldestAgent();
        }}
      >
        <div slot="iconLeft">
          {#if activeAgentLimitReached}
            <RegularPauseIcon />
          {:else}
            <Trash />
          {/if}
        </div>
        {warningButtonText}
      </ButtonAugment>
    </div>
  </CalloutAugment>
{/if}

<style>
  .agent-limit-message {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-3);
  }

  .agent-limit-message p {
    margin: 0;
  }
</style>
