<script lang="ts">
  import {
    type RemoteAgent,
    RemoteAgentStatus,
  } from "@augment-internal/sidecar-libs/src/remote-agent/remote-agent-types";
  import {
    SHARED_AGENT_STORE_CONTEXT_KEY,
    type ChatHomeWebviewState,
  } from "$vscode/src/webview-panels/remote-agents/common-webview-store";
  import { getContext } from "svelte";
  import { type SharedWebviewStoreModel } from "../../../common/models/shared-webview-store-model";
  import AgentCardBase from "../components/AgentList/AgentCardBase.svelte";
  import { RemoteAgentsClient } from "../models/remote-agents-client";

  export let agent: RemoteAgent;
  export let selected: boolean = false;
  export let onSelect: (agentId: string) => void;

  const remoteAgentsClient = getContext<RemoteAgentsClient>(RemoteAgentsClient.key);
  const sharedWebviewStore = getContext<SharedWebviewStoreModel<ChatHomeWebviewState>>(
    SHARED_AGENT_STORE_CONTEXT_KEY,
  );

  // Optimistic deletion state
  let isDeleting = false;
  let deletionError: string | null = null;
  let deletionErrorTimeout: ReturnType<typeof setTimeout> | null = null;

  const onSsh = async (agent: RemoteAgent) => {
    return await remoteAgentsClient.sshToRemoteAgent(agent.remote_agent_id);
  };

  async function deleteAgent(agentId: string) {
    // Clear any existing error
    clearDeletionError();

    // Set deleting state for immediate UI feedback
    isDeleting = true;

    // Optimistically remove the agent from the UI
    const originalAgentOverviews = $sharedWebviewStore.state?.agentOverviews || [];

    try {
      const success = await remoteAgentsClient.deleteRemoteAgent(agentId);

      if (!success) {
        throw new Error("Failed to delete agent");
      }
      sharedWebviewStore.update((state) => {
        if (!state) return;
        return {
          ...state,
          agentOverviews: state.agentOverviews.filter((a) => a.remote_agent_id !== agentId),
        };
      });

      // If successful, also remove from pinned agents if it was pinned
      const pinnedAgents = $sharedWebviewStore.state?.pinnedAgents || {};
      if (pinnedAgents[agentId]) {
        try {
          await remoteAgentsClient.deletePinnedAgentFromStore(agentId);
          const updatedPinnedAgents = await remoteAgentsClient.getPinnedAgentsFromStore();
          sharedWebviewStore.update((state) => {
            if (!state) return;
            return {
              ...state,
              pinnedAgents: updatedPinnedAgents,
            };
          });
        } catch (pinnedError) {
          console.error("Failed to remove pinned status:", pinnedError);
          // Don't revert the deletion for pinned status failure
        }
      }
    } catch (error) {
      console.error("Failed to delete agent:", error);

      // Revert the optimistic update
      sharedWebviewStore.update((state) => {
        if (!state) return;
        return {
          ...state,
          agentOverviews: originalAgentOverviews,
        };
      });

      // Show error message
      deletionError = error instanceof Error ? error.message : "Failed to delete agent";

      // Auto-dismiss error after 5 seconds
      deletionErrorTimeout = setTimeout(() => {
        clearDeletionError();
      }, 5000);
    } finally {
      isDeleting = false;
    }
  }

  function clearDeletionError() {
    deletionError = null;
    if (deletionErrorTimeout) {
      clearTimeout(deletionErrorTimeout);
      deletionErrorTimeout = null;
    }
  }

  async function togglePinned(agentId: string) {
    try {
      if (isPinned) {
        // Also remove from the global store
        await remoteAgentsClient.deletePinnedAgentFromStore(agentId);
      } else {
        // Save to the global store
        await remoteAgentsClient.savePinnedAgentToStore(agentId, true);
      }

      const pinnedAgents = await remoteAgentsClient.getPinnedAgentsFromStore();

      // Update the shared store with the returned pinnedAgents
      sharedWebviewStore.update((state) => {
        if (!state) return;
        return {
          ...state,
          pinnedAgents,
        };
      });
    } catch (error) {
      console.error("Failed to toggle pinned status:", error);
    }
  }

  $: pinnedAgents = $sharedWebviewStore.state?.pinnedAgents || {};
  $: isPinned = pinnedAgents?.[agent.remote_agent_id] === true;

  // SSH is only for running or idle agents
  $: canSsh =
    agent.status === RemoteAgentStatus.agentRunning || agent.status === RemoteAgentStatus.agentIdle;

  async function handleSsh() {
    if (canSsh) {
      return await onSsh(agent);
    }
    return false;
  }
</script>

<AgentCardBase
  {agent}
  {selected}
  {isPinned}
  {onSelect}
  onDelete={() => deleteAgent(agent.remote_agent_id)}
  bind:deletionError
  bind:isDeleting
  onTogglePinned={() => togglePinned(agent.remote_agent_id)}
  sshConfig={{ onSSH: handleSsh, canSSH: canSsh }}
/>
