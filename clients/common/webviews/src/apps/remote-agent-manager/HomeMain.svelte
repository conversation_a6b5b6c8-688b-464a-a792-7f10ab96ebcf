<script lang="ts">
  import { host } from "$common-webviews/src/common/hosts/host";
  import { MessageBroker } from "$common-webviews/src/common/utils/message-broker";
  import {
    SHARED_AGENT_STORE_CONTEXT_KEY,
    SHARED_AGENT_STORE_NAME,
    validateChatHomeState,
    type ChatHomeWebviewState,
  } from "$vscode/src/webview-panels/remote-agents/common-webview-store";
  import { SharedWebviewStoreModel } from "../../common/models/shared-webview-store-model";
  import { onMount, setContext } from "svelte";
  import AgentList from "./home-panel/AgentList.svelte";
  import { RemoteAgentsClient } from "./models/remote-agents-client";
  import AugmentLogo from "$common-webviews/src/design-system/icons/augment/augment-logo.svelte";

  const msgBroker = new MessageBroker(host);

  const store = new SharedWebviewStoreModel<ChatHomeWebviewState>(
    msgBroker,
    undefined,
    validateChatHomeState,
    SHARED_AGENT_STORE_NAME,
  );
  msgBroker.registerConsumer(store);
  setContext(SHARED_AGENT_STORE_CONTEXT_KEY, store);

  const remoteAgentsClient = new RemoteAgentsClient(msgBroker);
  setContext(RemoteAgentsClient.key, remoteAgentsClient);

  // Create the RemoteAgentsModel instance
  onMount(() => {
    // First fetch the state from the extension
    store.fetchStateFromExtension().then(() => {
      // Initialize the shared store with pinned agents if not already set
      store.update((state) => {
        if (!state) return;

        // Add home to active webviews
        const activeWebviews = [...state.activeWebviews, "home"] as ("chat" | "home")[];

        // Initialize pinnedAgents if not already set
        if (!state.pinnedAgents) {
          return {
            ...state,
            activeWebviews,
            pinnedAgents: {},
          };
        }

        return {
          ...state,
          activeWebviews,
        };
      });
    });

    return () => {
      msgBroker.dispose();
      remoteAgentsClient.dispose();
    };
  });
</script>

<svelte:window on:message={msgBroker.onMessageFromExtension} />
<div class="l-main">
  <h1 class="l-main__title">
    <span class="l-main__title-logo">
      <AugmentLogo />
    </span>
    Remote Agents
  </h1>
  <AgentList />
</div>

<style>
  .l-main {
    height: 100vh;
    overflow: auto;
    display: flex;
    flex-direction: column;
    padding: var(--ds-spacing-8) var(--ds-spacing-3) 0 var(--ds-spacing-8);
    background-color: var(--ds-surface);
  }

  .l-main__title {
    padding: 0 1rem;
    display: flex;
    align-items: center;
    font-weight: 400;
  }

  .l-main__title-logo {
    display: inline-block;
    vertical-align: middle;
    margin-right: 0.8rem;
    margin-bottom: 5px;
  }

  :global(svg) {
    height: 28px;
    width: 28px;
  }
</style>
