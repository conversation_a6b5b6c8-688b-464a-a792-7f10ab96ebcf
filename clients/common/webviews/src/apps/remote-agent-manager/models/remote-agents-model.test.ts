/* eslint-disable @typescript-eslint/naming-convention */
import {
  RemoteAgentStatus,
  RemoteAgentWorkspaceStatus,
} from "@augment-internal/sidecar-libs/src/remote-agent/remote-agent-types";
import { WebViewMessageType } from "$vscode/src/webview-providers/webview-messages";
import { AgentWebViewMessageType } from "@augment-internal/sidecar-libs/src/webview-messages/message-types/agent-messages";
import { afterEach, beforeEach, describe, expect, test, vi } from "vitest";
import { RemoteAgentsModel } from "./remote-agents-model";
import { ChatRequestNodeType } from "@augment-internal/sidecar-libs/src/chat/chat-types";
import {
  type IRemoteAgentsStateModel,
  type RemoteAgentsStateUpdate,
  SendMessageErrorType,
} from "./remote-agents-state-model/remote-agents-state-model";
import { HostClientType, type HostInterface } from "$common-webviews/src/common/hosts/host-types";
import { MessageBroker } from "$common-webviews/src/common/utils/message-broker";
import {
  RemoteAgentSessionEventName,
  RemoteAgentSetupWindowAction,
  SourceControlType,
} from "@augment-internal/sidecar-libs/src/metrics/types";

describe("RemoteAgentsModel", () => {
  let msgBroker: MessageBroker;
  let isActive: boolean;
  let mockStateModel: IRemoteAgentsStateModel;
  let mockHost: HostInterface;
  let mockGitRefModel: any;
  let createdModels: RemoteAgentsModel[] = [];

  const flagsModel = {
    enableBackgroundAgents: true,
    subscribe: vi.fn(),
  } as any;

  // Helper function to create and track models for cleanup
  const createModel = (options: any = {}) => {
    const defaultOptions: any = {
      msgBroker,
      isActive,
      flagsModel,
      host: mockHost,
      gitRefModel: mockGitRefModel,
    };

    // Only include stateModel if not explicitly set to undefined
    if (options.stateModel !== undefined) {
      defaultOptions.stateModel = options.stateModel;
    } else if (!("stateModel" in options)) {
      // If stateModel is not specified in options, use the mock
      defaultOptions.stateModel = mockStateModel;
    }

    const model = new RemoteAgentsModel({
      ...defaultOptions,
      ...options,
    });
    createdModels.push(model);
    return model;
  };

  function mockAgentId(id: number | string): string {
    return `agent-${id}`;
  }

  beforeEach(() => {
    mockHost = {
      clientType: HostClientType.vscode,
      postMessage: vi.fn().mockImplementation((msg) => {
        // Handle async messages by dispatching responses
        if (msg.type === "async-wrapper" && msg.baseMsg) {
          setTimeout(() => {
            switch (msg.baseMsg.type) {
              case AgentWebViewMessageType.checkHasEverUsedRemoteAgent:
                window.dispatchEvent(
                  new MessageEvent("message", {
                    data: {
                      type: "async-wrapper",
                      requestId: msg.requestId,
                      error: null,
                      baseMsg: {
                        type: AgentWebViewMessageType.checkHasEverUsedRemoteAgentResponse,
                        data: false,
                      },
                      streamCtx: undefined,
                    },
                  }),
                );
                break;
              case AgentWebViewMessageType.setHasEverUsedRemoteAgent:
                window.dispatchEvent(
                  new MessageEvent("message", {
                    data: {
                      type: "async-wrapper",
                      requestId: msg.requestId,
                      error: null,
                      baseMsg: {
                        type: "empty",
                      },
                      streamCtx: undefined,
                    },
                  }),
                );
                break;
            }
          }, 0);
        }
      }),
      getState: vi.fn(),
      setState: vi.fn(),
    };

    // Mock the subscribe method
    flagsModel.subscribe.mockImplementation((callback: (flagsModel: any) => void) => {
      callback(flagsModel);
    });

    msgBroker = new MessageBroker(mockHost);
    isActive = true;

    // Mock the send method
    vi.spyOn(msgBroker, "send").mockImplementation((message) => {
      const type = message.type;

      // Mock responses for different message types
      switch (type) {
        case WebViewMessageType.getRemoteAgentOverviewsRequest:
          return Promise.resolve({
            type: WebViewMessageType.getRemoteAgentOverviewsResponse,
            data: {
              overviews: [
                {
                  remote_agent_id: mockAgentId(1),
                  status: RemoteAgentStatus.agentIdle,
                  session_summary: "Test agent 1",
                  turn_summaries: ["Summary 1"],
                  started_at: new Date().toISOString(),
                  updated_at: new Date().toISOString(),
                  workspace_setup: {
                    starting_files: {
                      github_commit_ref: {
                        repository_url: "https://github.com/test/repo",
                        git_ref: "main",
                      },
                    },
                  },
                  current_branch: "feature/test",
                },
                {
                  remote_agent_id: mockAgentId(2),
                  status: RemoteAgentStatus.agentRunning,
                  session_summary: "Test agent 2",
                  turn_summaries: ["Summary 2"],
                  started_at: new Date().toISOString(),
                  updated_at: new Date().toISOString(),
                  workspace_setup: {
                    starting_files: {
                      github_commit_ref: {
                        repository_url: "https://github.com/test/repo",
                        git_ref: "main",
                      },
                    },
                  },
                  current_branch: "feature/test2",
                },
              ],
            },
          }) as any;

        case WebViewMessageType.getRemoteAgentChatHistoryRequest:
          return Promise.resolve({
            type: WebViewMessageType.getRemoteAgentChatHistoryResponse,
            data: {
              chatHistory: [
                {
                  exchange: {
                    request_message: "Test request",
                    response_text: "Test response",
                    request_id: "test-id",
                    timestamp: new Date().toISOString(),
                  },
                  changed_files: [],
                },
              ],
            },
          }) as any;

        case WebViewMessageType.createRemoteAgentRequest:
          return Promise.resolve({
            type: WebViewMessageType.createRemoteAgentResponse,
            data: {
              agentId: mockAgentId(3),
              success: true,
            },
          }) as any;

        case WebViewMessageType.deleteRemoteAgentRequest:
          return Promise.resolve({
            type: WebViewMessageType.deleteRemoteAgentResponse,
            data: {
              success: true,
            },
          }) as any;

        case WebViewMessageType.remoteAgentChatRequest:
          return Promise.resolve({
            type: WebViewMessageType.remoteAgentChatResponse,
            data: {
              success: true,
            },
          }) as any;

        case WebViewMessageType.remoteAgentInterruptRequest:
          return Promise.resolve({
            type: WebViewMessageType.remoteAgentInterruptResponse,
            data: {
              success: true,
            },
          }) as any;

        default:
          return Promise.resolve({ type: "unknown", data: {} });
      }
    });

    // Clear all timers
    vi.useFakeTimers();

    // Create a mock state model
    mockStateModel = {
      state: {
        agentOverviews: [],
        agentConversations: new Map(),
        agentLogs: new Map(),

        overviewError: undefined,
        conversationError: undefined,
        logsError: undefined,
        maxRemoteAgents: 10,
        maxActiveRemoteAgents: 5,

        isOverviewsLoading: false,
        isConversationLoading: false,
        isLogsLoading: false,

        logPollFailedCount: 0,
      },
      startStateUpdates: vi.fn(),
      stopStateUpdates: vi.fn(),
      refreshCurrentAgent: vi.fn().mockImplementation((agentId) => {
        // Simulate fetching agent conversation
        msgBroker.send(
          {
            type: WebViewMessageType.getRemoteAgentChatHistoryRequest,
            data: { agentId, lastProcessedSequenceId: 0 },
          },
          0,
        );
        return Promise.resolve();
      }),
      refreshAgentOverviews: vi.fn().mockImplementation(() => {
        // Simulate fetching agent overviews
        msgBroker.send(
          {
            type: WebViewMessageType.getRemoteAgentOverviewsRequest,
          },
          0,
        );
        return Promise.resolve([
          {
            remote_agent_id: mockAgentId(1),
            status: RemoteAgentStatus.agentIdle,
            session_summary: "Test agent 1",
          },
          {
            remote_agent_id: mockAgentId(2),
            status: RemoteAgentStatus.agentRunning,
            session_summary: "Test agent 2",
          },
        ]);
      }),
      refreshAgentLogs: vi.fn().mockImplementation((_) => {
        return Promise.resolve({
          steps: [
            {
              name: "Step 1",
              status: "success",
              logs: ["Log 1", "Log 2"],
            },
          ],
        });
      }),
      onStateUpdate: vi.fn().mockImplementation((callback) => {
        // Store the callback for later use in tests
        (mockStateModel as any).callback = callback;
        return () => {};
      }),
      dispose: vi.fn(),
    };

    // Create a mock git reference model
    mockGitRefModel = {
      listUserRepos: vi.fn().mockResolvedValue({
        repos: [
          { owner: "test", name: "repo1" },
          { owner: "test", name: "repo2" },
        ],
        error: undefined,
        isDevDeploy: false,
      }),
      isGithubAuthenticated: vi.fn().mockResolvedValue(true),
    };
  });

  afterEach(() => {
    // Dispose of any models created during tests to prevent timeout errors
    createdModels.forEach((model) => {
      try {
        model.dispose();
      } catch (error) {
        // Ignore disposal errors in tests
      }
    });
    createdModels = [];

    vi.clearAllTimers();
    vi.useRealTimers();
    vi.clearAllMocks();
  });

  test("should initialize with state model", () => {
    // Create a model with our mock state model
    const model = createModel();

    expect(model).toBeDefined();
    expect(model.agentOverviews).toEqual([]);
    expect(model.currentAgent).toBeUndefined();
    expect(model.currentStatus).toBe(RemoteAgentStatus.agentIdle);

    // Verify that the state model's onStateUpdate was called
    expect(mockStateModel.onStateUpdate).toHaveBeenCalled();

    // Verify that startStateUpdates was called since isActive is true
    expect(mockStateModel.startStateUpdates).toHaveBeenCalled();
  });

  test("should update state when state model sends updates", async () => {
    // Create a model with our mock state model
    const model = createModel();

    // Reset the mock
    vi.clearAllMocks();

    // Simulate an overview update from the state model
    const overviewUpdate: RemoteAgentsStateUpdate = {
      type: "overviews",
      data: [
        {
          remote_agent_id: mockAgentId(1),
          status: RemoteAgentStatus.agentIdle,
          session_summary: "Test agent 1",
        } as any,
        {
          remote_agent_id: mockAgentId(2),
          status: RemoteAgentStatus.agentRunning,
          session_summary: "Test agent 2",
        } as any,
      ],
    };

    // Call the callback directly
    (mockStateModel as any).callback(overviewUpdate);

    // Check that the agent overviews were updated
    expect(model.agentOverviews.length).toBe(2);
    expect(model.agentOverviews[0].remote_agent_id).toBe(mockAgentId(1));
    expect(model.agentOverviews[1].remote_agent_id).toBe(mockAgentId(2));
  });

  test("should set current agent and start state updates for it", async () => {
    // Create a model with our mock state model
    const model = new RemoteAgentsModel({
      msgBroker,
      isActive,
      flagsModel,
      host: mockHost,
      gitRefModel: mockGitRefModel,
      stateModel: mockStateModel,
    });

    // Reset the mock
    vi.clearAllMocks();

    // Set current agent
    await model.setCurrentAgent(mockAgentId(1));

    // Check that startStateUpdates was called with the agent ID
    expect(mockStateModel.startStateUpdates).toHaveBeenCalledWith(
      expect.objectContaining({
        conversation: { agentId: mockAgentId(1) },
        logs: { agentId: mockAgentId(1) },
      }),
    );
  });

  test("should call resume hint when setting current agent with paused workspace", async () => {
    // Create a model with our mock state model
    const model = new RemoteAgentsModel({
      msgBroker,
      isActive,
      flagsModel,
      host: mockHost,
      gitRefModel: mockGitRefModel,
      stateModel: mockStateModel,
    });

    // Mock the agent overviews to include an agent with paused workspace
    (model as any)._state.agentOverviews = [
      {
        remote_agent_id: mockAgentId(1),
        status: RemoteAgentStatus.agentIdle,
        started_at: "2023-01-01T00:00:00Z",
        updated_at: "2023-01-01T00:00:00Z",
        session_summary: "Test agent",
        turn_summaries: [],
        workspace_status: RemoteAgentWorkspaceStatus.workspacePaused,
        expires_at: "2023-01-02T00:00:00Z",
      },
    ];

    // Mock the client's resumeHintRemoteAgent method
    const mockResumeHint = vi.fn().mockResolvedValue({ type: WebViewMessageType.empty });
    // Ensure the client exists and add the method
    if (!(model as any)._remoteAgentsClient) {
      (model as any)._remoteAgentsClient = {};
    }
    (model as any)._remoteAgentsClient.resumeHintRemoteAgent = mockResumeHint;

    // Reset the mock
    vi.clearAllMocks();

    // Set current agent
    await model.setCurrentAgent(mockAgentId(1));

    // Check that resumeHintRemoteAgent was called with the agent ID
    expect(mockResumeHint).toHaveBeenCalledWith(mockAgentId(1));
  });

  test("should not fail if resume hint fails", async () => {
    // Create a model with our mock state model
    const model = new RemoteAgentsModel({
      msgBroker,
      isActive,
      flagsModel,
      host: mockHost,
      gitRefModel: mockGitRefModel,
      stateModel: mockStateModel,
    });

    // Mock the agent overviews to include an agent with paused workspace
    (model as any)._state.agentOverviews = [
      {
        remote_agent_id: mockAgentId(1),
        status: RemoteAgentStatus.agentIdle,
        started_at: "2023-01-01T00:00:00Z",
        updated_at: "2023-01-01T00:00:00Z",
        session_summary: "Test agent",
        turn_summaries: [],
        workspace_status: RemoteAgentWorkspaceStatus.workspacePaused,
        expires_at: "2023-01-02T00:00:00Z",
      },
    ];

    // Mock the client's resumeHintRemoteAgent method to throw an error
    const mockResumeHint = vi.fn().mockRejectedValue(new Error("Resume hint failed"));
    // Ensure the client exists and add the method
    if (!(model as any)._remoteAgentsClient) {
      (model as any)._remoteAgentsClient = {};
    }
    (model as any)._remoteAgentsClient.resumeHintRemoteAgent = mockResumeHint;

    // Mock console.warn to avoid noise in test output
    const mockWarn = vi.spyOn(console, "warn").mockImplementation(() => {});

    // Reset the mock
    vi.clearAllMocks();

    // Set current agent - should not throw
    await expect(model.setCurrentAgent(mockAgentId(1))).resolves.not.toThrow();

    // Check that resumeHintRemoteAgent was called
    expect(mockResumeHint).toHaveBeenCalledWith(mockAgentId(1));

    // Check that warning was logged
    expect(mockWarn).toHaveBeenCalledWith(
      "Failed to send resume hint to remote agent:",
      expect.any(Error),
    );

    mockWarn.mockRestore();
  });

  test("should not call resume hint when workspace is not paused", async () => {
    // Create a model with our mock state model
    const model = new RemoteAgentsModel({
      msgBroker,
      isActive,
      flagsModel,
      host: mockHost,
      gitRefModel: mockGitRefModel,
      stateModel: mockStateModel,
    });

    // Mock the agent overviews to include an agent with running workspace (not paused)
    (model as any)._state.agentOverviews = [
      {
        remote_agent_id: mockAgentId(1),
        status: RemoteAgentStatus.agentIdle,
        started_at: "2023-01-01T00:00:00Z",
        updated_at: "2023-01-01T00:00:00Z",
        session_summary: "Test agent",
        turn_summaries: [],
        workspace_status: RemoteAgentWorkspaceStatus.workspaceRunning, // Not paused
        expires_at: "2023-01-02T00:00:00Z",
      },
    ];

    // Mock the client's resumeHintRemoteAgent method
    const mockResumeHint = vi.fn().mockResolvedValue({ type: WebViewMessageType.empty });
    // Ensure the client exists and add the method
    if (!(model as any)._remoteAgentsClient) {
      (model as any)._remoteAgentsClient = {};
    }
    (model as any)._remoteAgentsClient.resumeHintRemoteAgent = mockResumeHint;

    // Reset the mock
    vi.clearAllMocks();

    // Set current agent
    await model.setCurrentAgent(mockAgentId(1));

    // Check that resumeHintRemoteAgent was NOT called because workspace is not paused
    expect(mockResumeHint).not.toHaveBeenCalled();
  });

  test("should call resume hint when setting current agent with pausing workspace", async () => {
    // Create a model with our mock state model
    const model = new RemoteAgentsModel({
      msgBroker,
      isActive,
      flagsModel,
      host: mockHost,
      gitRefModel: mockGitRefModel,
      stateModel: mockStateModel,
    });

    // Mock the agent overviews to include an agent with pausing workspace
    (model as any)._state.agentOverviews = [
      {
        remote_agent_id: mockAgentId(1),
        status: RemoteAgentStatus.agentIdle,
        started_at: "2023-01-01T00:00:00Z",
        updated_at: "2023-01-01T00:00:00Z",
        session_summary: "Test agent",
        turn_summaries: [],
        workspace_status: RemoteAgentWorkspaceStatus.workspacePausing,
        expires_at: "2023-01-02T00:00:00Z",
      },
    ];

    // Mock the client's resumeHintRemoteAgent method
    const mockResumeHint = vi.fn().mockResolvedValue({ type: WebViewMessageType.empty });
    // Ensure the client exists and add the method
    if (!(model as any)._remoteAgentsClient) {
      (model as any)._remoteAgentsClient = {};
    }
    (model as any)._remoteAgentsClient.resumeHintRemoteAgent = mockResumeHint;

    // Reset the mock
    vi.clearAllMocks();

    // Set current agent
    await model.setCurrentAgent(mockAgentId(1));

    // Check that resumeHintRemoteAgent was called with the agent ID
    expect(mockResumeHint).toHaveBeenCalledWith(mockAgentId(1));
  });

  test("should not call resume hint when clearing current agent", async () => {
    // Create a model with our mock state model
    const model = new RemoteAgentsModel({
      msgBroker,
      isActive,
      flagsModel,
      host: mockHost,
      gitRefModel: mockGitRefModel,
      stateModel: mockStateModel,
    });

    // Mock the client's resumeHintRemoteAgent method
    const mockResumeHint = vi.fn().mockResolvedValue({ type: WebViewMessageType.empty });
    // Ensure the client exists and add the method
    if (!(model as any)._remoteAgentsClient) {
      (model as any)._remoteAgentsClient = {};
    }
    (model as any)._remoteAgentsClient.resumeHintRemoteAgent = mockResumeHint;

    // Reset the mock
    vi.clearAllMocks();

    // Set current agent to undefined (clear)
    await model.setCurrentAgent(undefined);

    // Check that resumeHintRemoteAgent was NOT called
    expect(mockResumeHint).not.toHaveBeenCalled();
  });

  test("should clear current agent and stop state updates for it", async () => {
    // Create a model with our mock state model

    const model = new RemoteAgentsModel({
      msgBroker,
      isActive,
      flagsModel,
      host: mockHost,
      gitRefModel: mockGitRefModel,
      stateModel: mockStateModel,
    });

    // Set current agent
    await model.setCurrentAgent(mockAgentId(1));

    // Reset the mock
    vi.clearAllMocks();

    // Clear current agent
    model.clearCurrentAgent();

    // Check that stopStateUpdates was called with the agent ID
    expect(mockStateModel.stopStateUpdates).toHaveBeenCalledWith(
      expect.objectContaining({
        conversation: { agentId: mockAgentId(1) },
        logs: { agentId: mockAgentId(1) },
      }),
    );

    expect(model.currentAgentId).toBeUndefined();
  });

  test("should send message to agent and refresh state", async () => {
    // Create a model with our mock state model
    const model = new RemoteAgentsModel({
      msgBroker,
      isActive,
      flagsModel,
      host: mockHost,
      gitRefModel: mockGitRefModel,
      stateModel: mockStateModel,
    });

    // Set current agent
    await model.setCurrentAgent(mockAgentId(1));

    // Reset the mock
    vi.clearAllMocks();

    // Send message
    const result = await model.sendMessage("Test message");

    expect(result).toBe(true);

    // Check that send was called with the correct parameters
    expect(msgBroker.send).toHaveBeenCalledWith(
      {
        type: WebViewMessageType.remoteAgentChatRequest,
        data: {
          agentId: mockAgentId(1),
          requestDetails: {
            request_nodes: [
              {
                id: 1,
                type: ChatRequestNodeType.TEXT,
                text_node: {
                  content: "Test message",
                },
              },
            ],
          },
          timeoutMs: 90_000,
        },
      },
      expect.any(Number),
    );
  });

  test("should fail to send message when no agent is selected", async () => {
    const model = new RemoteAgentsModel({
      msgBroker,
      isActive,
      flagsModel,
      host: mockHost,
      gitRefModel: mockGitRefModel,
      stateModel: mockStateModel,
    });

    // Try to send message without setting current agent
    const result = await model.sendMessage("Test message");

    expect(result).toBe(false);
    expect(model.error).toBeDefined();
  });

  test("should interrupt agent and refresh state", async () => {
    // Create a model with our mock state model
    const model = new RemoteAgentsModel({
      msgBroker,
      isActive,
      flagsModel,
      host: mockHost,
      gitRefModel: mockGitRefModel,
      stateModel: mockStateModel,
    });

    // Set current agent
    await model.setCurrentAgent(mockAgentId(1));

    // Reset the mock
    vi.clearAllMocks();

    // Interrupt agent
    await model.interruptAgent();

    // Check that the interrupt request was sent
    expect(msgBroker.send).toHaveBeenCalledWith(
      expect.objectContaining({
        type: WebViewMessageType.remoteAgentInterruptRequest,
        data: expect.objectContaining({
          agentId: mockAgentId(1),
        }),
      }),
      expect.any(Number),
    );
  });

  test("should fail to interrupt when no agent is selected", async () => {
    const model = new RemoteAgentsModel({
      msgBroker,
      isActive,
      flagsModel,
      host: mockHost,
      gitRefModel: mockGitRefModel,
    });
    createdModels.push(model);

    // Try to interrupt without setting current agent
    await model.interruptAgent();

    expect(model.error).toBeDefined();
  });

  test("should create remote agent", async () => {
    const model = new RemoteAgentsModel({
      msgBroker,
      isActive,
      flagsModel,
      host: mockHost,
      gitRefModel: mockGitRefModel,
    });
    createdModels.push(model);

    // Create remote agent
    const workspaceSetup = {
      starting_files: {
        github_commit_ref: {
          repository_url: "https://github.com/test/repo",
          git_ref: "main",
        },
      },
    };

    const agentId = await model.createRemoteAgent("Initial prompt", workspaceSetup);

    expect(agentId).toBe(mockAgentId(3));
    expect(msgBroker.send).toHaveBeenCalledWith(
      expect.objectContaining({
        type: WebViewMessageType.createRemoteAgentRequest,
        data: expect.objectContaining({
          prompt: "Initial prompt",
          workspaceSetup,
        }),
      }),
      expect.any(Number),
    );
  });

  test("should delete agent", async () => {
    const model = new RemoteAgentsModel({
      msgBroker,
      isActive,
      flagsModel,
      host: mockHost,
      gitRefModel: mockGitRefModel,
    });
    createdModels.push(model);

    // Delete agent
    await model.deleteAgent(mockAgentId(1));

    expect(msgBroker.send).toHaveBeenCalledWith(
      expect.objectContaining({
        type: WebViewMessageType.deleteRemoteAgentRequest,
        data: expect.objectContaining({
          agentId: mockAgentId(1),
          doSkipConfirmation: false,
        }),
      }),
      expect.any(Number),
    );
  });

  test("should set and get new agent draft", () => {
    const model = new RemoteAgentsModel({
      msgBroker,
      isActive,
      flagsModel,
      host: mockHost,
      gitRefModel: mockGitRefModel,
    });
    createdModels.push(model);

    const draft = {
      commitRef: {
        github_commit_ref: {
          repository_url: "https://github.com/test/repo",
          git_ref: "main",
        },
      },
      selectedBranch: { name: "main", commit: { sha: "", url: "" }, protected: false },
      isDisabled: false,
      setupScript: null,
      enableNotification: true,
    };

    model.setNewAgentDraft(draft);

    // Access the draft through the model's state
    expect(model.newAgentDraft).toEqual(draft);
  });

  test("should create agent from draft", async () => {
    const model = new RemoteAgentsModel({
      msgBroker,
      isActive,
      flagsModel,
      host: mockHost,
      gitRefModel: mockGitRefModel,
    });

    const draft = {
      commitRef: {
        github_commit_ref: {
          repository_url: "https://github.com/test/repo",
          git_ref: "main",
        },
      },
      selectedBranch: { name: "main", commit: { sha: "", url: "" }, protected: false },
      isDisabled: false,
      setupScript: null,
      enableNotification: true,
    };

    model.setNewAgentDraft(draft);

    const agentId = await model.createRemoteAgentFromDraft("Initial prompt");

    expect(agentId).toBe(mockAgentId(3));
    expect(msgBroker.send).toHaveBeenCalledWith(
      expect.objectContaining({
        type: WebViewMessageType.createRemoteAgentRequest,
        data: expect.objectContaining({
          prompt: "Initial prompt",
          workspaceSetup: {
            starting_files: draft.commitRef,
          },
        }),
      }),
      expect.any(Number),
    );
  });

  test("should fail to create agent from draft when no draft is set", async () => {
    const model = new RemoteAgentsModel({
      msgBroker,
      isActive,
      flagsModel,
      host: mockHost,
      gitRefModel: mockGitRefModel,
    });

    // Mock the setRemoteAgentCreationError method to set the error
    vi.spyOn(model, "setRemoteAgentCreationError").mockImplementation(function (error) {
      (model as any)._state.remoteAgentCreationError = error;
    });

    // Try to create agent without setting draft
    const agentId = await model.createRemoteAgentFromDraft("Initial prompt");

    expect(agentId).toBeUndefined();
    expect(model.remoteAgentCreationError).toBe(
      "No workspace selected. Please select a workspace first.",
    );
  });

  test("should set and get remote agent creation error", () => {
    const model = new RemoteAgentsModel({
      msgBroker,
      isActive,
      flagsModel,
      host: mockHost,
      gitRefModel: mockGitRefModel,
    });

    model.setRemoteAgentCreationError("Test error");

    // Access the error through the model's state
    expect(model.remoteAgentCreationError).toBe("Test error");
  });

  test("should handle message from extension", () => {
    const model = new RemoteAgentsModel({
      msgBroker,
      isActive,
      flagsModel,
      host: mockHost,
      gitRefModel: mockGitRefModel,
    });

    // Test that the model can handle messages from extension
    const result = model.handleMessageFromExtension({
      data: {
        type: WebViewMessageType.showRemoteAgentDiffPanel,
      },
    } as MessageEvent<any>);

    expect(result).toBe(true);
  });

  test("should dispose state model when disposed", () => {
    // Create a model with our mock state model
    const model = new RemoteAgentsModel({
      msgBroker,
      isActive,
      flagsModel,
      host: mockHost,
      gitRefModel: mockGitRefModel,
      stateModel: mockStateModel,
    });

    // Reset the mock
    vi.clearAllMocks();

    // Dispose the model
    model.dispose();

    // Check that the state model's dispose method was called
    expect(mockStateModel.dispose).toHaveBeenCalled();
  });

  test("should toggle state updates based on isActive", () => {
    vi.useFakeTimers();

    // Create model with isActive set to false
    const model = new RemoteAgentsModel({
      msgBroker,
      isActive: false,
      flagsModel,
      host: mockHost,
      gitRefModel: mockGitRefModel,
      stateModel: mockStateModel,
    });

    // Set isActive to true
    model.setIsActive(true);

    // Verify startStateUpdates was called
    expect(mockStateModel.startStateUpdates).toHaveBeenCalled();

    // Set isActive back to false
    model.setIsActive(false);

    // Verify stopStateUpdates was called
    expect(mockStateModel.stopStateUpdates).toHaveBeenCalled();

    vi.useRealTimers();
  });

  test("should handle error in state model updates", async () => {
    // Create a model with our mock state model
    const model = new RemoteAgentsModel({
      msgBroker,
      isActive,
      flagsModel,
      host: mockHost,
      gitRefModel: mockGitRefModel,
      stateModel: mockStateModel,
    });

    // Reset the mock
    vi.clearAllMocks();

    const error = { errorMessage: "Test error from server" };

    // Simulate an error update from the state model
    const errorUpdate: RemoteAgentsStateUpdate = {
      type: "overviews",
      data: [],
      error,
    };

    // Call the callback directly
    (mockStateModel as any).callback(errorUpdate);

    // Verify that the error was set in the model state
    expect(model.agentThreadsError).toBe(error);
  });

  test("should handle conversation error in state model updates", async () => {
    // Create a model with our mock state model
    const model = new RemoteAgentsModel({
      msgBroker,
      isActive,
      flagsModel,
      host: mockHost,
      gitRefModel: mockGitRefModel,
      stateModel: mockStateModel,
    });

    // Set current agent ID
    await model.setCurrentAgent("test-agent-id");

    // Reset the mock
    vi.clearAllMocks();

    const error = { errorMessage: "Test error from chat history" };
    // Simulate an error update from the state model
    const errorUpdate: RemoteAgentsStateUpdate = {
      type: "conversation",
      agentId: "test-agent-id",
      data: [],
      error,
    };

    // Call the callback directly
    (mockStateModel as any).callback(errorUpdate);

    // Verify that the error was set in the model state
    expect(model.agentChatHistoryError).toBe(error);
  });

  describe("Last remote agent setup persistence", () => {
    test("should save and retrieve branch and script selection", async () => {
      const model = new RemoteAgentsModel({
        msgBroker,
        isActive,
        flagsModel,
        host: mockHost,
        gitRefModel: mockGitRefModel,
      });

      // Mock the send method for saveLastRemoteAgentSetup
      vi.spyOn(msgBroker, "send").mockImplementationOnce((message) => {
        expect(message).toEqual({
          type: WebViewMessageType.saveLastRemoteAgentSetupRequest,
          data: {
            lastRemoteAgentGitRepoUrl: "https://github.com/test/repo.git",
            lastRemoteAgentGitBranch: "feature-branch",
            lastRemoteAgentSetupScript: "/path/to/setup.sh",
          },
        });
        return Promise.resolve({ type: WebViewMessageType.empty });
      });

      // Test saving with values
      await model.saveLastRemoteAgentSetup(
        "https://github.com/test/repo.git",
        "feature-branch",
        "/path/to/setup.sh",
      );

      // Reset the mock to test retrieval
      vi.clearAllMocks();

      // Mock the send method for getLastRemoteAgentSetup
      vi.spyOn(msgBroker, "send").mockImplementationOnce((message) => {
        expect(message).toEqual({
          type: WebViewMessageType.getLastRemoteAgentSetupRequest,
        });
        return Promise.resolve({
          type: WebViewMessageType.getLastRemoteAgentSetupResponse,
          data: {
            lastRemoteAgentGitRepoUrl: "https://github.com/test/repo.git",
            lastRemoteAgentGitBranch: "feature-branch",
            lastRemoteAgentSetupScript: "/path/to/setup.sh",
          },
        });
      });

      // Test retrieving the values
      const result = await model.getLastRemoteAgentSetup();

      // Verify the returned values
      expect(result.lastRemoteAgentGitRepoUrl).toBe("https://github.com/test/repo.git");
      expect(result.lastRemoteAgentGitBranch).toBe("feature-branch");
      expect(result.lastRemoteAgentSetupScript).toBe("/path/to/setup.sh");
    });

    test("should handle null values for 'No Setup' selection", async () => {
      const model = new RemoteAgentsModel({
        msgBroker,
        isActive,
        flagsModel,
        host: mockHost,
        gitRefModel: mockGitRefModel,
      });

      // Mock the send method for saveLastRemoteAgentSetup with null script
      vi.spyOn(msgBroker, "send").mockImplementationOnce((message) => {
        expect(message).toEqual({
          type: WebViewMessageType.saveLastRemoteAgentSetupRequest,
          data: {
            lastRemoteAgentGitRepoUrl: "https://github.com/test/repo.git",
            lastRemoteAgentGitBranch: "main",
            lastRemoteAgentSetupScript: null,
          },
        });
        return Promise.resolve({ type: WebViewMessageType.empty });
      });

      // Test saving with null script (No Setup)
      await model.saveLastRemoteAgentSetup("https://github.com/test/repo.git", "main", null);

      // Reset the mock to test retrieval
      vi.clearAllMocks();

      // Mock the send method for getLastRemoteAgentSetup with null script
      vi.spyOn(msgBroker, "send").mockImplementationOnce((message) => {
        expect(message).toEqual({
          type: WebViewMessageType.getLastRemoteAgentSetupRequest,
        });
        return Promise.resolve({
          type: WebViewMessageType.getLastRemoteAgentSetupResponse,
          data: {
            lastRemoteAgentGitRepoUrl: "https://github.com/test/repo.git",
            lastRemoteAgentGitBranch: "main",
            lastRemoteAgentSetupScript: null,
          },
        });
      });

      // Test retrieving the values
      const result = await model.getLastRemoteAgentSetup();

      // Verify the returned values include null for script
      expect(result.lastRemoteAgentGitRepoUrl).toBe("https://github.com/test/repo.git");
      expect(result.lastRemoteAgentGitBranch).toBe("main");
      expect(result.lastRemoteAgentSetupScript).toBe(null);
    });
  });

  describe("sendMessage error handling", () => {
    test("should prevent sendMessage when agent is in failed state", async () => {
      const model = new RemoteAgentsModel({
        msgBroker,
        isActive,
        flagsModel,
        host: mockHost,
        gitRefModel: mockGitRefModel,
        stateModel: mockStateModel,
      });

      // Set current agent with failed status
      await model.setCurrentAgent(mockAgentId(1));

      // Simulate agent failed state
      const overviewUpdate: RemoteAgentsStateUpdate = {
        type: "overviews",
        data: [
          {
            remote_agent_id: mockAgentId(1),
            status: RemoteAgentStatus.agentFailed,
            session_summary: "Failed agent",
          } as any,
        ],
      };
      (mockStateModel as any).callback(overviewUpdate);

      // Try to send message
      const result = await model.sendMessage("Test message");

      expect(result).toBe(false);
      expect(model.sendMessageError).toBeDefined();
      expect(model.sendMessageError?.type).toBe(SendMessageErrorType.agentFailed);
      expect(model.sendMessageError?.canRetry).toBe(false);
    });

    test("should handle chat request failure", async () => {
      // Mock send to reject for chat requests
      vi.spyOn(msgBroker, "send").mockImplementation((message) => {
        if (message.type === WebViewMessageType.remoteAgentChatRequest) {
          return Promise.reject(new Error("Network error"));
        }
        return Promise.resolve({
          type: WebViewMessageType.remoteAgentChatResponse,
          data: [],
        } as any);
      });

      const model = new RemoteAgentsModel({
        msgBroker,
        isActive,
        flagsModel,
        host: mockHost,
        gitRefModel: mockGitRefModel,
        stateModel: mockStateModel,
      });

      await model.setCurrentAgent(mockAgentId(1));

      const result = await model.sendMessage("Test message");

      expect(result).toBe(false);
      expect(model.sendMessageError).toBeDefined();
      expect(model.sendMessageError?.type).toBe(SendMessageErrorType.chatRequestFailed);
      expect(model.sendMessageError?.canRetry).toBe(true);
      expect(model.sendMessageError?.failedExchangeId).toBeDefined();
    });

    test("should handle message timeout", async () => {
      const model = new RemoteAgentsModel({
        msgBroker,
        isActive,
        flagsModel,
        host: mockHost,
        gitRefModel: mockGitRefModel,
        stateModel: mockStateModel,
      });

      await model.setCurrentAgent(mockAgentId(1));

      // Start sending a message
      const sendPromise = model.sendMessage("Test message");

      // Fast-forward time to trigger timeout
      vi.advanceTimersByTime(90000); // 1.5 minutes

      await sendPromise;

      // Check that timeout error was set
      expect(model.sendMessageError).toBeDefined();
      expect(model.sendMessageError?.type).toBe(SendMessageErrorType.messageTimeout);
      expect(model.sendMessageError?.canRetry).toBe(true);
    });

    test("should retry failed message", async () => {
      const model = new RemoteAgentsModel({
        msgBroker,
        isActive,
        flagsModel,
        host: mockHost,
        gitRefModel: mockGitRefModel,
        stateModel: mockStateModel,
      });

      await model.setCurrentAgent(mockAgentId(1));

      // Set up a sendMessage error
      const error = {
        type: SendMessageErrorType.chatRequestFailed,
        errorMessage: "Test error",
        canRetry: true,
        failedExchangeId: "pending-123",
      };
      (model as any)._agentSendMessageErrors.set(mockAgentId(1), error);
      (model as any)._state.sendMessageError = error;

      // Add a failed exchange to the conversation
      const conversation = {
        exchanges: [
          {
            exchange: {
              request_message: "Failed message",
              response_text: "",
              request_id: "pending-123",
              response_nodes: [],
              request_nodes: [],
            },
            changed_files: [],
            sequence_id: 1,
          },
        ],
        lastFetched: new Date(),
      };
      (model as any)._agentConversations.set(mockAgentId(1), conversation);

      // Reset mocks to track retry
      vi.clearAllMocks();

      // Retry the failed message
      const result = await model.retryFailedMessage(mockAgentId(1), "pending-123");

      expect(result).toBe(true);
      expect(model.sendMessageError).toBeUndefined();

      // Verify the failed exchange was removed and message was resent
      expect(msgBroker.send).toHaveBeenCalledWith(
        expect.objectContaining({
          type: WebViewMessageType.remoteAgentChatRequest,
          data: expect.objectContaining({
            agentId: mockAgentId(1),
            requestDetails: expect.objectContaining({
              request_nodes: expect.arrayContaining([
                expect.objectContaining({
                  text_node: expect.objectContaining({
                    content: "Failed message",
                  }),
                }),
              ]),
            }),
          }),
        }),
        expect.any(Number),
      );
    });

    test("should clear sendMessage error", async () => {
      const model = new RemoteAgentsModel({
        msgBroker,
        isActive,
        flagsModel,
        host: mockHost,
        gitRefModel: mockGitRefModel,
        stateModel: mockStateModel,
      });

      // Set current agent first
      await model.setCurrentAgent(mockAgentId(1));

      // Set an error
      const error = {
        type: SendMessageErrorType.chatRequestFailed,
        errorMessage: "Test error",
        canRetry: true,
      };
      (model as any)._agentSendMessageErrors.set(mockAgentId(1), error);
      (model as any)._state.sendMessageError = error;

      expect(model.sendMessageError).toBeDefined();

      // Clear the error
      model.clearSendMessageError();

      expect(model.sendMessageError).toBeUndefined();
    });

    test("should not consider agent running when there's a sendMessage error", async () => {
      const model = new RemoteAgentsModel({
        msgBroker,
        isActive,
        flagsModel,
        host: mockHost,
        gitRefModel: mockGitRefModel,
        stateModel: mockStateModel,
      });

      await model.setCurrentAgent(mockAgentId(1));

      // Add a pending exchange
      const conversation = {
        exchanges: [
          {
            exchange: {
              request_message: "Pending message",
              response_text: "",
              request_id: "pending-123",
              response_nodes: [],
              request_nodes: [],
            },
            changed_files: [],
            sequence_id: 1,
          },
        ],
        lastFetched: new Date(),
      };
      (model as any)._agentConversations.set(mockAgentId(1), conversation);

      // Without error, should be considered running
      expect(model.isCurrentAgentRunning).toBe(true);

      // Set sendMessage error for the current agent
      const error = {
        type: SendMessageErrorType.chatRequestFailed,
        errorMessage: "Test error",
        canRetry: true,
      };
      (model as any)._agentSendMessageErrors.set(mockAgentId(1), error);
      (model as any)._state.sendMessageError = error;

      // With error, should not be considered running
      expect(model.isCurrentAgentRunning).toBe(false);
    });

    test("should clear sendMessage error when pending exchange is replaced by server response", async () => {
      const model = new RemoteAgentsModel({
        msgBroker,
        isActive,
        flagsModel,
        host: mockHost,
        gitRefModel: mockGitRefModel,
        stateModel: mockStateModel,
      });

      await model.setCurrentAgent(mockAgentId(1));

      // Set up a sendMessage error for a pending exchange
      const error = {
        type: SendMessageErrorType.chatRequestFailed,
        errorMessage: "Network error occurred",
        canRetry: true,
        failedExchangeId: "pending-123",
      };
      (model as any)._agentSendMessageErrors.set(mockAgentId(1), error);
      (model as any)._state.sendMessageError = error;

      expect(model.sendMessageError).toBeDefined();
      expect(model.sendMessageError?.failedExchangeId).toBe("pending-123");

      // Now simulate a conversation update with a server response that replaces the pending exchange
      // The key point: the pending exchange "pending-123" is NO LONGER in the conversation
      const conversationUpdate: RemoteAgentsStateUpdate = {
        type: "conversation",
        agentId: mockAgentId(1),
        data: [
          {
            exchange: {
              request_id: "server-assigned-456", // Server assigns its own ID, no more "pending-123"
              request_message: "test message",
              response_text: "Response from agent",
              request_nodes: [],
              response_nodes: [],
            },
            changed_files: [],
            sequence_id: 1,
            finished_at: new Date().toISOString(),
          },
        ],
        error: undefined,
      };

      // Trigger the conversation update through the state model callback
      (mockStateModel as any).callback(conversationUpdate);

      // The sendMessage error should now be cleared because the pending exchange is gone
      // (replaced by the real server response)
      expect(model.sendMessageError).toBeUndefined();
    });
  });

  describe("Remote Agent Usage Tracking", () => {
    test("should initialize hasEverUsedRemoteAgent as undefined", () => {
      const model = new RemoteAgentsModel({
        msgBroker,
        isActive,
        flagsModel,
        host: mockHost,
        gitRefModel: mockGitRefModel,
        stateModel: mockStateModel,
      });

      expect(model.hasEverUsedRemoteAgent).toBeDefined();
    });

    test("should have setHasEverUsedRemoteAgent method", () => {
      const model = new RemoteAgentsModel({
        msgBroker,
        isActive,
        flagsModel,
        host: mockHost,
        gitRefModel: mockGitRefModel,
        stateModel: mockStateModel,
      });

      expect(typeof model.setHasEverUsedRemoteAgent).toBe("function");
    });

    test("should have refreshHasEverUsedRemoteAgent method", () => {
      const model = new RemoteAgentsModel({
        msgBroker,
        isActive,
        flagsModel,
        host: mockHost,
        gitRefModel: mockGitRefModel,
        stateModel: mockStateModel,
      });

      expect(typeof model.refreshHasEverUsedRemoteAgent).toBe("function");
    });
  });

  describe("updateRemoteAgentTitle", () => {
    test("should update agent title optimistically and then sync with server", async () => {
      const model = createModel();

      // Manually set up initial agent overviews to avoid waiting for async initialization
      (model as any)._state.agentOverviews = [
        {
          remote_agent_id: mockAgentId(1),
          status: RemoteAgentStatus.agentIdle,
          session_summary: "Test agent 1",
          turn_summaries: ["Summary 1"],
          started_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          workspace_setup: {
            starting_files: {
              github_commit_ref: {
                repository_url: "https://github.com/test/repo",
                git_ref: "main",
              },
            },
          },
          current_branch: "feature/test",
        },
      ];

      // Mock the client's updateRemoteAgentTitle method
      const mockUpdateTitle = vi.fn().mockResolvedValue({
        data: {
          success: true,
          agent: {
            remote_agent_id: mockAgentId(1),
            status: RemoteAgentStatus.agentIdle,
            title: "Updated Title",
            session_summary: "Test agent 1",
            turn_summaries: ["Summary 1"],
            started_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            workspace_setup: {
              starting_files: {
                github_commit_ref: {
                  repository_url: "https://github.com/test/repo",
                  git_ref: "main",
                },
              },
            },
            current_branch: "feature/test",
          },
        },
      });

      // Ensure the client exists and add the method
      if (!(model as any)._remoteAgentsClient) {
        (model as any)._remoteAgentsClient = {};
      }
      (model as any)._remoteAgentsClient.updateRemoteAgentTitle = mockUpdateTitle;

      // Verify initial state
      expect(model.agentOverviews).toHaveLength(1);
      expect(model.agentOverviews[0].remote_agent_id).toBe(mockAgentId(1));
      expect(model.agentOverviews[0].title).toBeUndefined(); // No title initially

      // Update the title
      await model.updateRemoteAgentTitle(mockAgentId(1), "Updated Title");

      // Verify the API was called
      expect(mockUpdateTitle).toHaveBeenCalledWith(mockAgentId(1), "Updated Title");

      // Verify the local state was updated with server response
      expect(model.agentOverviews[0].title).toBe("Updated Title");
    });

    test("should update current agent title when it matches", async () => {
      const model = createModel();

      // Manually set up initial agent overviews to avoid waiting for async initialization
      (model as any)._state.agentOverviews = [
        {
          remote_agent_id: mockAgentId(1),
          status: RemoteAgentStatus.agentIdle,
          session_summary: "Test agent 1",
          turn_summaries: ["Summary 1"],
          started_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          workspace_setup: {
            starting_files: {
              github_commit_ref: {
                repository_url: "https://github.com/test/repo",
                git_ref: "main",
              },
            },
          },
          current_branch: "feature/test",
        },
      ];

      // Set current agent ID to match the one we'll update
      (model as any)._state.currentAgentId = mockAgentId(1);

      // Mock the client's updateRemoteAgentTitle method
      const mockUpdateTitle = vi.fn().mockResolvedValue({
        data: {
          success: true,
          agent: {
            remote_agent_id: mockAgentId(1),
            status: RemoteAgentStatus.agentIdle,
            title: "Updated Current Agent Title",
            session_summary: "Test agent 1",
            turn_summaries: ["Summary 1"],
            started_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            workspace_setup: {
              starting_files: {
                github_commit_ref: {
                  repository_url: "https://github.com/test/repo",
                  git_ref: "main",
                },
              },
            },
            current_branch: "feature/test",
          },
        },
      });

      // Ensure the client exists and add the method
      if (!(model as any)._remoteAgentsClient) {
        (model as any)._remoteAgentsClient = {};
      }
      (model as any)._remoteAgentsClient.updateRemoteAgentTitle = mockUpdateTitle;

      // Verify initial state
      expect(model.currentAgent?.remote_agent_id).toBe(mockAgentId(1));
      expect(model.currentAgent?.title).toBeUndefined(); // No title initially

      // Update the title
      await model.updateRemoteAgentTitle(mockAgentId(1), "Updated Current Agent Title");

      // Verify both agentOverviews and currentAgent were updated
      expect(model.agentOverviews[0].title).toBe("Updated Current Agent Title");
      expect(model.currentAgent?.title).toBe("Updated Current Agent Title");
    });

    test("should rollback optimistic update on API failure", async () => {
      const model = createModel();

      // Manually set up initial agent overviews to avoid waiting for async initialization
      const originalTitle = "Original Title";
      (model as any)._state.agentOverviews = [
        {
          remote_agent_id: mockAgentId(1),
          status: RemoteAgentStatus.agentIdle,
          title: originalTitle,
          session_summary: "Test agent 1",
          turn_summaries: ["Summary 1"],
          started_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          workspace_setup: {
            starting_files: {
              github_commit_ref: {
                repository_url: "https://github.com/test/repo",
                git_ref: "main",
              },
            },
          },
          current_branch: "feature/test",
        },
      ];

      // Set current agent ID to match the one we'll update
      (model as any)._state.currentAgentId = mockAgentId(1);

      // Mock the client's updateRemoteAgentTitle method to fail
      const mockUpdateTitle = vi.fn().mockResolvedValue({
        data: {
          success: false,
          error: "Server error",
        },
      });

      // Ensure the client exists and add the method
      if (!(model as any)._remoteAgentsClient) {
        (model as any)._remoteAgentsClient = {};
      }
      (model as any)._remoteAgentsClient.updateRemoteAgentTitle = mockUpdateTitle;

      // Try to update the title (should fail and rollback)
      await expect(model.updateRemoteAgentTitle(mockAgentId(1), "Failed Update")).rejects.toThrow(
        "Server error",
      );

      // Verify the title was rolled back to original
      expect(model.agentOverviews[0].title).toBe(originalTitle);
      expect(model.currentAgent?.title).toBe(originalTitle);
      expect(model.error).toBe("Server error");
    });

    test("should rollback optimistic update on network error", async () => {
      const model = createModel();

      // Manually set up initial agent overviews to avoid waiting for async initialization
      const originalTitle = "Original Title";
      (model as any)._state.agentOverviews = [
        {
          remote_agent_id: mockAgentId(1),
          status: RemoteAgentStatus.agentIdle,
          title: originalTitle,
          session_summary: "Test agent 1",
          turn_summaries: ["Summary 1"],
          started_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          workspace_setup: {
            starting_files: {
              github_commit_ref: {
                repository_url: "https://github.com/test/repo",
                git_ref: "main",
              },
            },
          },
          current_branch: "feature/test",
        },
      ];

      // Set current agent ID to match the one we'll update
      (model as any)._state.currentAgentId = mockAgentId(1);

      // Mock the client's updateRemoteAgentTitle method to throw
      const mockUpdateTitle = vi.fn().mockRejectedValue(new Error("Network error"));

      // Ensure the client exists and add the method
      if (!(model as any)._remoteAgentsClient) {
        (model as any)._remoteAgentsClient = {};
      }
      (model as any)._remoteAgentsClient.updateRemoteAgentTitle = mockUpdateTitle;

      // Try to update the title (should fail and rollback)
      await expect(model.updateRemoteAgentTitle(mockAgentId(1), "Failed Update")).rejects.toThrow(
        "Network error",
      );

      // Verify the title was rolled back to original
      expect(model.agentOverviews[0].title).toBe(originalTitle);
      expect(model.currentAgent?.title).toBe(originalTitle);
      expect(model.error).toBe("Network error");
    });

    test("should return early if agent not found", async () => {
      const model = createModel();

      // Manually set up initial agent overviews to avoid waiting for async initialization
      (model as any)._state.agentOverviews = [
        {
          remote_agent_id: mockAgentId(1),
          status: RemoteAgentStatus.agentIdle,
          session_summary: "Test agent 1",
          turn_summaries: ["Summary 1"],
          started_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          workspace_setup: {
            starting_files: {
              github_commit_ref: {
                repository_url: "https://github.com/test/repo",
                git_ref: "main",
              },
            },
          },
          current_branch: "feature/test",
        },
      ];

      // Mock console.warn to capture the warning
      const mockWarn = vi.spyOn(console, "warn").mockImplementation(() => {});

      // Mock the client's updateRemoteAgentTitle method (should not be called)
      const mockUpdateTitle = vi.fn();

      // Ensure the client exists and add the method
      if (!(model as any)._remoteAgentsClient) {
        (model as any)._remoteAgentsClient = {};
      }
      (model as any)._remoteAgentsClient.updateRemoteAgentTitle = mockUpdateTitle;

      // Try to update title for non-existent agent
      await model.updateRemoteAgentTitle("non-existent-agent", "New Title");

      // Verify warning was logged and API was not called
      expect(mockWarn).toHaveBeenCalledWith(
        "Agent with ID non-existent-agent not found in overviews",
      );
      expect(mockUpdateTitle).not.toHaveBeenCalled();

      // Cleanup
      mockWarn.mockRestore();
    });
  });

  describe("reportRemoteAgentSetupWindowEvent", () => {
    test("should report repo selection event with repo hash", async () => {
      const model = createModel();
      const reportSpy = vi.spyOn(model, "reportRemoteAgentEvent");

      // Set up a draft with repo information
      model.setNewAgentDraft({
        commitRef: {
          github_commit_ref: {
            repository_url: "https://github.com/test/repo",
            git_ref: "main",
          },
        },
        selectedBranch: null,
        setupScript: null,
        isDisabled: false,
        enableNotification: true,
      });

      await model.reportRemoteAgentSetupWindowEvent(RemoteAgentSetupWindowAction.selectRepo);

      expect(reportSpy).toHaveBeenCalledWith({
        eventName: RemoteAgentSessionEventName.remoteAgentSetupWindow,
        remoteAgentId: "",
        eventData: {
          remoteAgentSetupWindowData: {
            action: RemoteAgentSetupWindowAction.selectRepo,
            repoHash: expect.any(String),
            branchHash: "",
            hasSetupScriptSelected: false,
            githubIntegrationEnabled: true,
            numReposAvailable: 2,
            sourceControl: SourceControlType.unknownSourceControl,
          },
        },
      });

      // Verify that repo hash is not empty and is a valid hash
      const callArgs = reportSpy.mock.calls[0][0];
      const repoHash = callArgs.eventData?.remoteAgentSetupWindowData?.repoHash;
      expect(repoHash).not.toBe("");
      expect(typeof repoHash).toBe("string");
    });

    test("should report branch selection event with branch hash", async () => {
      const model = createModel();
      const reportSpy = vi.spyOn(model, "reportRemoteAgentEvent");

      // Set up a draft with branch information
      model.setNewAgentDraft({
        commitRef: null,
        selectedBranch: {
          name: "feature-branch",
          commit: {
            sha: "abc123",
            url: "https://api.github.com/repos/test-user/test-repo/commits/abc123",
          },
          protected: false,
        },
        setupScript: null,
        isDisabled: false,
        enableNotification: true,
      });

      await model.reportRemoteAgentSetupWindowEvent(RemoteAgentSetupWindowAction.selectBranch);

      expect(reportSpy).toHaveBeenCalledWith({
        eventName: RemoteAgentSessionEventName.remoteAgentSetupWindow,
        remoteAgentId: "",
        eventData: {
          remoteAgentSetupWindowData: {
            action: RemoteAgentSetupWindowAction.selectBranch,
            repoHash: "",
            branchHash: expect.any(String),
            hasSetupScriptSelected: false,
            githubIntegrationEnabled: true,
            numReposAvailable: 2,
            sourceControl: SourceControlType.unknownSourceControl,
          },
        },
      });

      // Verify that branch hash is not empty and is a valid hash
      const callArgs = reportSpy.mock.calls[0][0];
      const branchHash = callArgs.eventData?.remoteAgentSetupWindowData?.branchHash;
      expect(branchHash).not.toBe("");
      expect(typeof branchHash).toBe("string");
    });

    test("should report setup script selection event with hasSetupScriptSelected true", async () => {
      const model = createModel();
      const reportSpy = vi.spyOn(model, "reportRemoteAgentEvent");

      // Set up a draft with setup script
      model.setNewAgentDraft({
        commitRef: null,
        selectedBranch: null,
        setupScript: {
          name: "setup.sh",
          path: "/path/to/setup.sh",
          content: "#!/bin/bash\necho 'setup'",
          location: "home",
        },
        isDisabled: false,
        enableNotification: true,
      });

      await model.reportRemoteAgentSetupWindowEvent(RemoteAgentSetupWindowAction.selectSetupScript);

      expect(reportSpy).toHaveBeenCalledWith({
        eventName: RemoteAgentSessionEventName.remoteAgentSetupWindow,
        remoteAgentId: "",
        eventData: {
          remoteAgentSetupWindowData: {
            action: RemoteAgentSetupWindowAction.selectSetupScript,
            repoHash: "",
            branchHash: "",
            hasSetupScriptSelected: true,
            githubIntegrationEnabled: true,
            numReposAvailable: 2,
            sourceControl: SourceControlType.unknownSourceControl,
          },
        },
      });
    });

    test("should handle errors in event reporting gracefully", async () => {
      const model = createModel();
      const consoleSpy = vi.spyOn(console, "error").mockImplementation(() => {});
      const reportSpy = vi
        .spyOn(model, "reportRemoteAgentEvent")
        .mockRejectedValue(new Error("Network error"));

      await model.reportRemoteAgentSetupWindowEvent(RemoteAgentSetupWindowAction.open);

      expect(consoleSpy).toHaveBeenCalledWith(
        "Failed to report remote agent setup window event:",
        expect.any(Error),
      );

      consoleSpy.mockRestore();
      reportSpy.mockRestore();
    });

    test("should use simpleHash for repo and branch hashing", async () => {
      const model = createModel();
      const reportSpy = vi.spyOn(model, "reportRemoteAgentEvent");
      const simpleHashSpy = vi.spyOn(model as any, "simpleHash");

      // Set up a draft with both repo and branch information
      model.setNewAgentDraft({
        commitRef: {
          github_commit_ref: {
            repository_url: "https://github.com/test/repo",
            git_ref: "main",
          },
        },
        selectedBranch: {
          name: "feature-branch",
          commit: {
            sha: "abc123",
            url: "https://api.github.com/repos/test-user/test-repo/commits/abc123",
          },
          protected: false,
        },
        setupScript: null,
        isDisabled: false,
        enableNotification: true,
      });

      await model.reportRemoteAgentSetupWindowEvent(RemoteAgentSetupWindowAction.selectRepo);

      // Verify that simpleHash was called for both repo URL and branch name
      expect(simpleHashSpy).toHaveBeenCalledWith("https://github.com/test/repo");
      expect(simpleHashSpy).toHaveBeenCalledWith("feature-branch");

      simpleHashSpy.mockRestore();
    });

    test("should return empty string for hash when values are undefined", async () => {
      const model = createModel();
      const reportSpy = vi.spyOn(model, "reportRemoteAgentEvent");

      // Set up a draft with null/undefined values
      model.setNewAgentDraft({
        commitRef: null,
        selectedBranch: null,
        setupScript: null,
        isDisabled: false,
        enableNotification: true,
      });

      await model.reportRemoteAgentSetupWindowEvent(RemoteAgentSetupWindowAction.open);

      const callArgs = reportSpy.mock.calls[0][0];
      expect(callArgs.eventData?.remoteAgentSetupWindowData?.repoHash).toBe("");
      expect(callArgs.eventData?.remoteAgentSetupWindowData?.branchHash).toBe("");
    });
  });
});
