import { beforeEach, describe, expect, it, vi } from "vitest";
import { get } from "svelte/store";
import { RepoBranchSelectorModel } from "./repo-branch-selector-model";

describe("RepoBranchSelectorModel", () => {
  let mockGitRefModel: any;
  let repoBranchSelectorModel: RepoBranchSelectorModel;

  beforeEach(() => {
    mockGitRefModel = {
      listUserRepos: vi.fn().mockResolvedValue({
        /* eslint-disable @typescript-eslint/naming-convention */
        repos: [
          {
            owner: "augmentcode",
            name: "augment",
            html_url: "https://github.com/augmentcode/augment",
            default_branch: "main",
            created_at: "2021-01-01",
            updated_at: "2021-01-01",
          },
          {
            owner: "augmentcode",
            name: "augment.vim",
            html_url: "https://github.com/augmentcode/augment.vim",
            default_branch: "main",
            created_at: "2021-01-01",
            updated_at: "2021-01-01",
          },
          {
            owner: "sveltejs",
            name: "svelte",
            html_url: "https://github.com/sveltejs/svelte",
            default_branch: "main",
            created_at: "2021-01-01",
            updated_at: "2021-01-01",
          },
        ],
        error: undefined,
        isDevDeploy: false,
        /* eslint-enable @typescript-eslint/naming-convention */
      }),
      getRemoteUrl: vi.fn().mockResolvedValue({
        remoteUrl: "https://github.com/augmentcode/augment.vim",
        error: undefined,
      }),
      getGithubRepo: vi.fn().mockResolvedValue({
        repo: {
          /* eslint-disable @typescript-eslint/naming-convention */
          owner: "augmentcode",
          name: "augment.vim",
          html_url: "https://github.com/augmentcode/augment.vim",
          default_branch: "main",
          created_at: "2021-01-01",
          updated_at: "2021-01-01",
          /* eslint-enable @typescript-eslint/naming-convention */
        },
        error: undefined,
      }),
      listRepoBranches: vi.fn().mockResolvedValue({
        branches: [
          {
            name: "main",
            commit: {
              sha: "123",
              url: "https://github.com/augmentcode/augment/commit/123",
            },
            protected: false,
          },
          {
            name: "feature",
            commit: {
              sha: "456",
              url: "https://github.com/augmentcode/augment/commit/456",
            },
            protected: false,
          },
          {
            name: "name/bugfix",
            commit: {
              sha: "789",
              url: "https://github.com/augmentcode/augment/commit/789",
            },
            protected: false,
          },
        ],
        error: undefined,
        hasNextPage: false,
      }),
    };
    repoBranchSelectorModel = new RepoBranchSelectorModel(mockGitRefModel, 2);
  });

  describe("updateReposList", () => {
    it("should update the allRepos store with the list of repos", async () => {
      await repoBranchSelectorModel.updateReposList();
      const allRepos = get(repoBranchSelectorModel.allRepos);
      expect(allRepos).toHaveLength(3);
      expect(allRepos[0].name).toBe("augment");
      expect(allRepos[1].name).toBe("augment.vim");
      expect(allRepos[2].name).toBe("svelte");
      expect(repoBranchSelectorModel.isDevDeploy).toBe(false);
      expect(repoBranchSelectorModel.error).toBeUndefined();
    });

    it("should set isDevDeploy to true if the response indicates a dev deploy", async () => {
      mockGitRefModel.listUserRepos.mockResolvedValue({
        repos: [],
        error: undefined,
        isDevDeploy: true,
      });
      await repoBranchSelectorModel.updateReposList();
      expect(repoBranchSelectorModel.isDevDeploy).toBe(true);
    });

    it("should set an error if the request fails", async () => {
      mockGitRefModel.listUserRepos.mockResolvedValue({
        repos: [],
        error: "Test error",
        isDevDeploy: false,
      });
      await repoBranchSelectorModel.updateReposList();
      expect(repoBranchSelectorModel.error).toBe("Test error");
    });
  });

  describe("preselectRepo", () => {
    it("should preselect the current workspace repo if it is in the allRepos list", async () => {
      await repoBranchSelectorModel.updateReposList();
      await repoBranchSelectorModel.preselectRepo();
      expect(get(repoBranchSelectorModel.selectedRepo)?.name).toBe("augment.vim");
    });

    it("should preselect the first repo if the current workspace is not a repo", async () => {
      mockGitRefModel.getRemoteUrl.mockResolvedValue({
        remoteUrl: "",
        error: "Not a git repo",
      });
      await repoBranchSelectorModel.updateReposList();
      await repoBranchSelectorModel.preselectRepo();
      expect(get(repoBranchSelectorModel.selectedRepo)?.name).toBe("augment");
    });

    it("should preselect last used repo if it is in the allRepos list and the current workspace is not a repo", async () => {
      mockGitRefModel.getRemoteUrl.mockResolvedValue({
        remoteUrl: "",
        error: "Not a git repo",
      });
      repoBranchSelectorModel.lastUsedRepoUrl = "https://github.com/sveltejs/svelte";
      await repoBranchSelectorModel.updateReposList();
      await repoBranchSelectorModel.preselectRepo();
      expect(get(repoBranchSelectorModel.selectedRepo)?.name).toBe("svelte");
    });

    it("should preselect the current workspace repo if it is a public repo", async () => {
      mockGitRefModel.getRemoteUrl.mockResolvedValue({
        remoteUrl: "https://github.com/sveltejs/svelte",
        error: undefined,
      });
      await repoBranchSelectorModel.updateReposList();
      await repoBranchSelectorModel.preselectRepo();
      expect(get(repoBranchSelectorModel.selectedRepo)?.name).toBe("svelte");
    });
  });

  describe("updateBranches", () => {
    it("should update the allBranches store with the list of branches", async () => {
      await repoBranchSelectorModel.updateReposList();
      await repoBranchSelectorModel.preselectRepo();
      await repoBranchSelectorModel.updateBranches();
      const allBranches = get(repoBranchSelectorModel.allBranches);
      expect(allBranches).toHaveLength(3);
      expect(allBranches[0].name).toBe("main");
      expect(allBranches[1].name).toBe("feature");
      expect(allBranches[2].name).toBe("name/bugfix");
    });
  });

  describe("loadMoreBranches", () => {
    it("should load more branches if there are more pages", async () => {
      mockGitRefModel.listRepoBranches.mockResolvedValue({
        branches: [
          {
            name: "main",
            commit: {
              sha: "123",
              url: "https://github.com/augmentcode/augment/commit/123",
            },
            protected: false,
          },
          {
            name: "feature",
            commit: {
              sha: "456",
              url: "https://github.com/augmentcode/augment/commit/456",
            },
            protected: false,
          },
          {
            name: "name/bugfix",
            commit: {
              sha: "789",
              url: "https://github.com/augmentcode/augment/commit/789",
            },
            protected: false,
          },
        ],
        error: undefined,
        hasNextPage: true,
        nextPage: 2,
      });
      await repoBranchSelectorModel.updateReposList();
      await repoBranchSelectorModel.preselectRepo();
      await repoBranchSelectorModel.updateBranches();
      let allBranches = get(repoBranchSelectorModel.allBranches);
      expect(allBranches).toHaveLength(6);
      expect(allBranches[0].name).toBe("main");
      expect(allBranches[1].name).toBe("feature");
      expect(allBranches[2].name).toBe("name/bugfix");
      expect(allBranches[3].name).toBe("main");
      expect(allBranches[4].name).toBe("feature");
      expect(allBranches[5].name).toBe("name/bugfix");
      mockGitRefModel.listRepoBranches.mockResolvedValue({
        branches: [
          {
            name: "name/bugfix2",
            commit: {
              sha: "123",
              url: "https://github.com/augmentcode/augment/commit/123",
            },
            protected: false,
          },
        ],
        error: undefined,
        hasNextPage: false,
        nextPage: 0,
      });
      await repoBranchSelectorModel.loadMoreBranches();
      allBranches = get(repoBranchSelectorModel.allBranches);
      expect(allBranches).toHaveLength(7);
      expect(allBranches[0].name).toBe("main");
      expect(allBranches[5].name).toBe("name/bugfix");
      expect(allBranches[6].name).toBe("name/bugfix2");
    });

    it("should return false if there are no more pages to load", async () => {
      await repoBranchSelectorModel.updateReposList();
      await repoBranchSelectorModel.preselectRepo();
      await repoBranchSelectorModel.updateBranches();
      const result = await repoBranchSelectorModel.loadMoreBranches();
      expect(result).toBe(false);
    });
  });

  describe("setter methods", () => {
    it("should clear branches when selectedRepo is set", () => {
      const testRepo = {
        /* eslint-disable @typescript-eslint/naming-convention */
        owner: "test",
        name: "test-repo",
        html_url: "https://github.com/test/test-repo",
        default_branch: "main",
        /* eslint-enable @typescript-eslint/naming-convention */
      };

      // Set some branches first
      repoBranchSelectorModel.allBranches.set([
        {
          name: "main",
          commit: { sha: "123", url: "test-url" },
          protected: false,
        },
      ]);

      // Set a new repo
      repoBranchSelectorModel.selectedRepo = testRepo;

      // Branches should be cleared
      expect(get(repoBranchSelectorModel.allBranches)).toHaveLength(0);
      expect(get(repoBranchSelectorModel.selectedRepo)).toEqual(testRepo);
    });

    it("should set selectedBranch correctly", () => {
      const testBranch = {
        name: "feature",
        commit: { sha: "456", url: "test-url" },
        protected: false,
      };

      repoBranchSelectorModel.selectedBranch = testBranch;
      expect(get(repoBranchSelectorModel.selectedBranch)).toEqual(testBranch);
    });

    it("should set and get lastUsedRepoUrl correctly", () => {
      const testUrl = "https://github.com/test/repo";
      repoBranchSelectorModel.lastUsedRepoUrl = testUrl;
      expect(repoBranchSelectorModel.lastUsedRepoUrl).toBe(testUrl);
    });

    it("should set and get lastUsedBranchName correctly", () => {
      const testBranch = "feature-branch";
      repoBranchSelectorModel.lastUsedBranchName = testBranch;
      expect(repoBranchSelectorModel.lastUsedBranchName).toBe(testBranch);
    });
  });

  describe("preselectBranch", () => {
    beforeEach(async () => {
      await repoBranchSelectorModel.updateReposList();
      await repoBranchSelectorModel.preselectRepo();
      await repoBranchSelectorModel.updateBranches();
    });

    it("should do nothing if no repo is selected", () => {
      repoBranchSelectorModel.selectedRepo = undefined;
      repoBranchSelectorModel.preselectBranch();
      expect(get(repoBranchSelectorModel.selectedBranch)).toBeUndefined();
    });

    it("should do nothing if a branch is already selected", () => {
      const existingBranch = {
        name: "existing",
        commit: { sha: "123", url: "test-url" },
        protected: false,
      };
      repoBranchSelectorModel.selectedBranch = existingBranch;
      repoBranchSelectorModel.preselectBranch();
      expect(get(repoBranchSelectorModel.selectedBranch)).toEqual(existingBranch);
    });

    it("should preselect last used branch if available", () => {
      repoBranchSelectorModel.lastUsedBranchName = "feature";
      repoBranchSelectorModel.preselectBranch();
      expect(get(repoBranchSelectorModel.selectedBranch)?.name).toBe("feature");
    });

    it("should preselect default branch if last used branch is not found", () => {
      repoBranchSelectorModel.lastUsedBranchName = "nonexistent";
      repoBranchSelectorModel.preselectBranch();
      expect(get(repoBranchSelectorModel.selectedBranch)?.name).toBe("main");
    });

    it("should create optimistic default branch if not yet loaded", () => {
      // Clear branches to simulate not loaded yet
      repoBranchSelectorModel.allBranches.set([]);
      repoBranchSelectorModel.preselectBranch();
      const selectedBranch = get(repoBranchSelectorModel.selectedBranch);
      expect(selectedBranch?.name).toBe("main");
      expect(selectedBranch?.commit.sha).toBe("");
      expect(selectedBranch?.commit.url).toBe("");
      expect(selectedBranch?.protected).toBe(false);
    });

    it("should preselect first branch if no default branch is available", () => {
      // Set a repo without default_branch
      const repoWithoutDefault = {
        /* eslint-disable @typescript-eslint/naming-convention */
        owner: "test",
        name: "test-repo",
        html_url: "https://github.com/test/test-repo",
        /* eslint-enable @typescript-eslint/naming-convention */
      };
      repoBranchSelectorModel.selectedRepo = repoWithoutDefault;
      repoBranchSelectorModel.allBranches.set([
        {
          name: "develop",
          commit: { sha: "123", url: "test-url" },
          protected: false,
        },
      ]);
      repoBranchSelectorModel.preselectBranch();
      expect(get(repoBranchSelectorModel.selectedBranch)?.name).toBe("develop");
    });
  });

  describe("error handling", () => {
    it("should handle errors in updateBranches", async () => {
      mockGitRefModel.listRepoBranches.mockResolvedValue({
        branches: [],
        error: "Branch fetch error",
        hasNextPage: false,
        nextPage: 0,
      });

      await repoBranchSelectorModel.updateReposList();
      await repoBranchSelectorModel.preselectRepo();
      await repoBranchSelectorModel.updateBranches();

      expect(repoBranchSelectorModel.error).toBe("Branch fetch error");
    });

    it("should handle errors when fetching public repo in preselectRepo", async () => {
      mockGitRefModel.getRemoteUrl.mockResolvedValue({
        remoteUrl: "https://github.com/unknown/repo",
        error: undefined,
      });
      mockGitRefModel.getGithubRepo.mockResolvedValue({
        repo: {},
        error: "Repo not found",
      });

      await repoBranchSelectorModel.updateReposList();
      await repoBranchSelectorModel.preselectRepo();

      // Should fallback to first repo in allRepos
      expect(get(repoBranchSelectorModel.selectedRepo)?.name).toBe("augment");
    });

    it("should handle exceptions when fetching public repo in preselectRepo", async () => {
      mockGitRefModel.getRemoteUrl.mockResolvedValue({
        remoteUrl: "https://github.com/unknown/repo",
        error: undefined,
      });
      mockGitRefModel.getGithubRepo.mockRejectedValue(new Error("Network error"));

      await repoBranchSelectorModel.updateReposList();
      await repoBranchSelectorModel.preselectRepo();

      // Should fallback to first repo in allRepos
      expect(get(repoBranchSelectorModel.selectedRepo)?.name).toBe("augment");
    });
  });

  describe("updateBranches edge cases", () => {
    it("should return false when no repo is selected", async () => {
      const result = await repoBranchSelectorModel.updateBranches();
      expect(result).toBe(false);
    });

    it("should handle preselectBranch parameter", async () => {
      await repoBranchSelectorModel.updateReposList();
      await repoBranchSelectorModel.preselectRepo();

      // Clear selected branch to test preselection
      repoBranchSelectorModel.selectedBranch = undefined;

      await repoBranchSelectorModel.updateBranches(true);

      // Should have preselected the default branch
      expect(get(repoBranchSelectorModel.selectedBranch)?.name).toBe("main");
    });
  });

  describe("dev deploy scenarios", () => {
    beforeEach(() => {
      // Add listBranches mock for dev deploy scenarios
      mockGitRefModel.listBranches = vi.fn().mockResolvedValue({
        branches: [
          {
            name: "origin/main",
            isDefault: true,
          },
          {
            name: "origin/feature",
            isDefault: false,
          },
        ],
      });
    });

    it("should handle dev deploy in updateReposList", async () => {
      mockGitRefModel.listUserRepos.mockResolvedValue({
        repos: [],
        error: undefined,
        isDevDeploy: true,
      });

      await repoBranchSelectorModel.updateReposList();

      expect(repoBranchSelectorModel.isDevDeploy).toBe(true);
      expect(mockGitRefModel.getRemoteUrl).toHaveBeenCalled();
    });

    it("should handle dev deploy with error in fetchLocalRepos", async () => {
      mockGitRefModel.listUserRepos.mockResolvedValue({
        repos: [],
        error: undefined,
        isDevDeploy: true,
      });
      mockGitRefModel.getRemoteUrl.mockResolvedValue({
        remoteUrl: "",
        error: "Not a git repo",
      });

      await repoBranchSelectorModel.updateReposList();

      expect(repoBranchSelectorModel.error).toBe("Not a git repo");
      expect(get(repoBranchSelectorModel.allRepos)).toHaveLength(0);
    });

    it("should handle dev deploy in updateBranches", async () => {
      mockGitRefModel.listUserRepos.mockResolvedValue({
        repos: [],
        error: undefined,
        isDevDeploy: true,
      });

      await repoBranchSelectorModel.updateReposList();
      await repoBranchSelectorModel.preselectRepo();
      const result = await repoBranchSelectorModel.updateBranches();

      expect(result).toBe(false); // Dev deploy doesn't have pagination
      expect(mockGitRefModel.listBranches).toHaveBeenCalled();
    });
  });

  describe("preselectRepo edge cases", () => {
    it("should do nothing if repo is already selected", async () => {
      const existingRepo = {
        /* eslint-disable @typescript-eslint/naming-convention */
        owner: "existing",
        name: "repo",
        html_url: "https://github.com/existing/repo",
        default_branch: "main",
        /* eslint-enable @typescript-eslint/naming-convention */
      };
      repoBranchSelectorModel.selectedRepo = existingRepo;

      await repoBranchSelectorModel.preselectRepo();

      expect(get(repoBranchSelectorModel.selectedRepo)).toEqual(existingRepo);
      expect(mockGitRefModel.getRemoteUrl).not.toHaveBeenCalled();
    });

    it("should add public repo to allRepos when successfully fetched", async () => {
      mockGitRefModel.getRemoteUrl.mockResolvedValue({
        remoteUrl: "https://github.com/public/repo",
        error: undefined,
      });
      mockGitRefModel.getGithubRepo.mockResolvedValue({
        repo: {
          /* eslint-disable @typescript-eslint/naming-convention */
          owner: "public",
          name: "repo",
          html_url: "https://github.com/public/repo",
          default_branch: "main",
          /* eslint-enable @typescript-eslint/naming-convention */
        },
        error: undefined,
      });

      await repoBranchSelectorModel.updateReposList();
      await repoBranchSelectorModel.preselectRepo();

      const allRepos = get(repoBranchSelectorModel.allRepos);
      expect(allRepos).toHaveLength(4); // Original 3 + 1 new
      expect(allRepos[0].name).toBe("repo"); // Should be first
      expect(get(repoBranchSelectorModel.selectedRepo)?.name).toBe("repo");
    });
  });
});
