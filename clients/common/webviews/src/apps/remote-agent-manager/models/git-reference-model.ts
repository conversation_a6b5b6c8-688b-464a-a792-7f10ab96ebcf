import type { AsyncMsgSender } from "$common-webviews/src/common/hosts/async-messaging";
import { type GitBranch } from "$vscode/src/remote-agent-manager/commit-ref-types";
import {
  type GithubBranch,
  type GithubRepo,
} from "@augment-internal/sidecar-libs/src/remote-agent/remote-agent-types";
import {
  type GitFetchRequestMessage,
  type GitFetchResponseMessage,
  WebViewMessageType,
  type GetGitBranchesRequestMessage,
  type GetGitBranchesResponseMessage,
  type GetGithubRepoRequestMessage,
  type GetGithubRepoResponseMessage,
  type GetRemoteUrlRequestMessage,
  type GetRemoteUrlResponseMessage,
  type GetWorkspaceDiffRequestMessage,
  type GetWorkspaceDiffResponseMessage,
  type IsGitRepositoryRequestMessage,
  type IsGitRepositoryResponseMessage,
  type IsGithubAuthenticatedRequestMessage,
  type IsGithubAuthenticatedResponseMessage,
  type AuthenticateGithubRequestMessage,
  type AuthenticateGithubResponseMessage,
  type RevokeGithubAccessRequestMessage,
  type RevokeGithubAccessResponseMessage,
  type ListGithubReposForAuthenticatedUserRequestMessage,
  type ListGithubReposForAuthenticatedUserResponseMessage,
  type ListGithubRepoBranchesRequestMessage,
  type ListGithubRepoBranchesResponseMessage,
  type GetCurrentLocalBranchRequestMessage,
  type GetCurrentLocalBranchResponseMessage,
} from "$vscode/src/webview-providers/webview-messages";
import { get, type Writable, writable } from "svelte/store";

/**
 * Response for fetchBranches
 */
export interface BranchesResponse {
  branches: GitBranch[];
  defaultBranch?: GitBranch;
  currentBranch?: GitBranch;
}

/**
 * GitReferenceModel provides methods to fetch git branch and commit information
 * for the remote agent manager.
 */
export class GitReferenceModel {
  public static key = "gitReferenceModel"; // for svelte context

  // Timeout for GitHub API calls (in milliseconds)
  private readonly _githubTimeoutMs = 90_000;

  // Cached repository URL. This is set when we fetch the remote url from the extension.
  // It should be stable for the lifetime of the webview since the user can't change
  // the repository URL in the same IDE session.
  private _currentRepositoryUrl: Writable<string | undefined> = writable(undefined);

  constructor(private readonly _asyncMsgSender: AsyncMsgSender) {}

  /**
   * List all repos for the authenticated user
   * @param page the page to fetch
   * @returns Promise with list of repos
   */
  async listUserRepos(page?: number): Promise<{
    repos: GithubRepo[];
    hasNextPage: boolean;
    nextPage: number;
    error?: string;
    isDevDeploy?: boolean;
  }> {
    try {
      const response = await this._asyncMsgSender.send<
        ListGithubReposForAuthenticatedUserRequestMessage,
        ListGithubReposForAuthenticatedUserResponseMessage
      >(
        {
          type: WebViewMessageType.listGithubReposForAuthenticatedUserRequest,
          data: {
            page,
          },
        },
        this._githubTimeoutMs,
      );

      return {
        repos: response.data.repos,
        hasNextPage: response.data.hasNextPage,
        nextPage: response.data.nextPage,
        error: response.data.error,
        isDevDeploy: response.data.isDevDeploy,
      };
    } catch (err) {
      console.error("Failed to list user repos:", err);
      return {
        repos: [],
        hasNextPage: false,
        nextPage: 0,
        error: `Error: ${err instanceof Error ? err.message : String(err)}`,
      };
    }
  }

  /**
   * List all branches for a given repo
   * @param repo The repo to list branches for
   * @returns Promise with list of branches
   */
  async listRepoBranches(
    repo: GithubRepo,
    page?: number,
  ): Promise<{
    branches: GithubBranch[];
    hasNextPage: boolean;
    nextPage: number;
    error?: string;
    isDevDeploy?: boolean;
  }> {
    try {
      const response = await this._asyncMsgSender.send<
        ListGithubRepoBranchesRequestMessage,
        ListGithubRepoBranchesResponseMessage
      >(
        {
          type: WebViewMessageType.listGithubRepoBranchesRequest,
          data: { repo, page },
        },
        this._githubTimeoutMs,
      );

      return {
        branches: response.data.branches,
        hasNextPage: response.data.hasNextPage,
        nextPage: response.data.nextPage,
        error: response.data.error,
        isDevDeploy: response.data.isDevDeploy,
      };
    } catch (err) {
      console.error("Failed to list repo branches:", err);
      return {
        branches: [],
        hasNextPage: false,
        nextPage: 0,
        error: `Error: ${err instanceof Error ? err.message : String(err)}`,
      };
    }
  }

  /**
   * Get a GitHub repository by owner and name
   * @param repo The repo to get (only owner and name are required)
   * @returns Promise with the repo details
   */
  async getGithubRepo(repo: GithubRepo): Promise<{
    repo: GithubRepo;
    error?: string;
    isDevDeploy?: boolean;
  }> {
    try {
      const response = await this._asyncMsgSender.send<
        GetGithubRepoRequestMessage,
        GetGithubRepoResponseMessage
      >(
        {
          type: WebViewMessageType.getGithubRepoRequest,
          data: { repo },
        },
        this._githubTimeoutMs,
      );

      return {
        repo: response.data.repo,
        error: response.data.error,
        isDevDeploy: response.data.isDevDeploy,
      };
    } catch (err) {
      console.error("Failed to get GitHub repo:", err);
      return {
        repo,
        error: `Error: ${err instanceof Error ? err.message : String(err)}`,
      };
    }
  }

  async getCurrentLocalBranch(): Promise<string | undefined> {
    try {
      const response = await this._asyncMsgSender.send<
        GetCurrentLocalBranchRequestMessage,
        GetCurrentLocalBranchResponseMessage
      >(
        {
          type: WebViewMessageType.getCurrentLocalBranchRequest,
        },
        10000,
      );

      return response.data.branch;
    } catch (err) {
      console.error("Failed to get current local branch:", err);
      return undefined;
    }
  }

  /**
   * Get git branches from the extension. This does not fetch from remote.
   * @returns Promise with branches and currentBranch
   */
  async listBranches(prefix: string = ""): Promise<BranchesResponse> {
    try {
      const response = await this._asyncMsgSender.send<
        GetGitBranchesRequestMessage,
        GetGitBranchesResponseMessage
      >(
        {
          type: WebViewMessageType.getGitBranchesRequest,
          data: { prefix },
        },
        10000,
      );

      return {
        branches: response.data.branches,
      };
    } catch (err) {
      console.error("Failed to fetch branches:", err);
      return { branches: [] };
    }
  }

  /**
   * Diff the workspace from a remote branch
   * @param commitHash The commit hash to get the diff from
   * @returns Promise with the diff as a string
   */
  async getWorkspaceDiff(branchName: string): Promise<string> {
    try {
      const response = await this._asyncMsgSender.send<
        GetWorkspaceDiffRequestMessage,
        GetWorkspaceDiffResponseMessage
      >(
        {
          type: WebViewMessageType.getWorkspaceDiffRequest,
          data: { branchName },
        },
        10000,
      );

      return response.data.diff;
    } catch (err) {
      console.error("Failed to get workspace diff:", err);
      return "";
    }
  }

  /**
   * The current repository URL. This is set when we fetch the remote url from the extension.
   * It should be stable for the lifetime of the webview since the user can't change
   * the repository URL in the same IDE session.
   */
  get currentRepositoryUrl(): Writable<string | undefined> {
    return this._currentRepositoryUrl;
  }

  /**
   * Gets the current repository URL from the extension. If we have already
   * fetched the URL successfully, we will return the cached value.
   *
   * Use `currentRepositoryUrl` store if you want to subscribe to changes to this value.
   */
  async getRemoteUrl(): Promise<{ remoteUrl: string; error?: string }> {
    const currentRepoUrl = get(this._currentRepositoryUrl);
    if (currentRepoUrl) {
      return { remoteUrl: currentRepoUrl };
    }

    try {
      const response = await this._asyncMsgSender.send<
        GetRemoteUrlRequestMessage,
        GetRemoteUrlResponseMessage
      >(
        {
          type: WebViewMessageType.getRemoteUrlRequest,
        },
        10000,
      );

      if (!response.data.error) {
        this._currentRepositoryUrl.set(response.data.remoteUrl);
      } else {
        this._currentRepositoryUrl.set(undefined);
      }

      return { remoteUrl: response.data.remoteUrl };
    } catch (err) {
      console.error("Failed to get remote url:", err);
      return { remoteUrl: "", error: `Error: ${err instanceof Error ? err.message : String(err)}` };
    }
  }

  async fetch(): Promise<void> {
    try {
      await this._asyncMsgSender.send<GitFetchRequestMessage, GitFetchResponseMessage>(
        {
          type: WebViewMessageType.gitFetchRequest,
        },
        10000,
      );
    } catch (err) {
      console.error("Failed to fetch remote branch:", err);
    }
  }

  async isGitRepository(): Promise<boolean> {
    try {
      const response = await this._asyncMsgSender.send<
        IsGitRepositoryRequestMessage,
        IsGitRepositoryResponseMessage
      >(
        {
          type: WebViewMessageType.isGitRepositoryRequest,
        },
        10000,
      );

      return response.data.isGitRepository;
    } catch (err) {
      console.error("Failed to check if is git repository:", err);
      return false;
    }
  }

  /**
   * Check if the user is authenticated with GitHub
   * @returns Promise with authentication status
   */
  async isGithubAuthenticated(): Promise<boolean> {
    try {
      const response = await this._asyncMsgSender.send<
        IsGithubAuthenticatedRequestMessage,
        IsGithubAuthenticatedResponseMessage
      >(
        {
          type: WebViewMessageType.isGithubAuthenticatedRequest,
        },
        this._githubTimeoutMs,
      );

      return response.data.isAuthenticated;
    } catch (err) {
      console.error("Failed to check GitHub authentication status:", err);
      return false;
    }
  }

  /**
   * Authenticate with GitHub by opening the OAuth flow
   * @returns Promise with success status and optional error message
   */
  async authenticateGithub(): Promise<{ success: boolean; message?: string; url?: string }> {
    try {
      const response = await this._asyncMsgSender.send<
        AuthenticateGithubRequestMessage,
        AuthenticateGithubResponseMessage
      >(
        {
          type: WebViewMessageType.authenticateGithubRequest,
        },
        this._githubTimeoutMs,
      );

      return {
        success: response.data.success,
        message: response.data.message,
        url: response.data.url,
      };
    } catch (err) {
      console.error("Failed to authenticate with GitHub:", err);
      return {
        success: false,
        message: `Error: ${err instanceof Error ? err.message : String(err)}`,
      };
    }
  }

  /**
   * Revoke GitHub access by removing the API token
   * @returns Promise with success status and optional error message
   */
  async revokeGithubAccess(): Promise<{ success: boolean; message?: string }> {
    try {
      const response = await this._asyncMsgSender.send<
        RevokeGithubAccessRequestMessage,
        RevokeGithubAccessResponseMessage
      >(
        {
          type: WebViewMessageType.revokeGithubAccessRequest,
        },
        10000,
      );

      return {
        success: response.data.success,
        message: response.data.message,
      };
    } catch (err) {
      console.error("Failed to revoke GitHub access:", err);
      return {
        success: false,
        message: `Error: ${err instanceof Error ? err.message : String(err)}`,
      };
    }
  }
}
