import { type ListRemoteAgentsStreamResponse } from "@augment-internal/sidecar-libs/src/remote-agent/remote-agent-types";
import { type IRemoteAgentsError } from "./remote-agents-state-model";
import { StreamWithRetry } from "./stream-with-retry";

/**
 * Type-specific stream update for overview streams
 */
export type RemoteAgentsOverviewsStreamUpdate = ListRemoteAgentsStreamResponse | IRemoteAgentsError;

export const isOverviewsStreamUpdate = (
  update: RemoteAgentsOverviewsStreamUpdate,
): update is ListRemoteAgentsStreamResponse => {
  return update != null && (update as ListRemoteAgentsStreamResponse).updates !== undefined;
};

/**
 * RemoteAgentsListStreamWithRetry provides a retry mechanism for streaming remote agent overviews.
 * It extends StreamWithRetry with default options optimized for list streams.
 */
export class RemoteAgentsListStreamWithRetry extends StreamWithRetry<
  ListRemoteAgentsStreamResponse,
  string | undefined
> {
  /**
   * Creates a new RemoteAgentsListStreamWithRetry instance with default options for list streams.
   *
   * @param lastUpdateTimestamp - The last update timestamp
   * @param startStreamFn - Function to start a stream
   * @param cancelStreamFn - Function to cancel a stream
   * @param maxRetries - Maximum number of retry attempts (default: 5)
   * @param baseDelay - Base delay between retries in milliseconds (default: 4000)
   */
  constructor(
    lastUpdateTimestamp: string | undefined,
    startStreamFn: (
      agentId: string,
      streamId: string,
      lastUpdateTimestamp: string | undefined,
    ) => AsyncGenerator<ListRemoteAgentsStreamResponse>,
    cancelStreamFn: (streamId: string) => Promise<void>,
    maxRetries: number = 5,
    baseDelay: number = 4000,
  ) {
    super(
      "overviews", // Use a fixed ID for overviews stream
      lastUpdateTimestamp,
      startStreamFn,
      cancelStreamFn,
      maxRetries,
      baseDelay,
      2, // attemptErrorThreshold - show errors after 2 failed attempts (default)
      undefined, // unhandledErrorMessage - use default error messages
    );
  }
}
