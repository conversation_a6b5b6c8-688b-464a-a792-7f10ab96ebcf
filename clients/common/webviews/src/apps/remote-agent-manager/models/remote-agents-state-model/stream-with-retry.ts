import { STREAM_CANCELLED, STREAM_TIMEOUT } from "@augment-internal/sidecar-libs/src/api/types";
import { type IRemoteAgentsError } from "./remote-agents-state-model";

/**
 * Custom error class for stream retry exhaustion
 */
export class StreamRetryExhaustedError extends Error {
  constructor(message: string) {
    super(message);
    this.name = "StreamRetryExhaustedError";
  }
}

/**
 * Generic stream update type that can be either the response type or an error
 */
export type RemoteAgentsStreamUpdate<T> = T | IRemoteAgentsError;

/**
 * StreamWithRetry provides a retry mechanism for streaming arbitrary response types.
 * It handles retriable errors and cleanup on cancellation.
 *
 * T is the type of the response.
 *
 * StreamLastProcessedValue is the type of the last processed value (number or string)
 * For example, for the history stream, the last processed value is a number (sequence ID)
 * For the overviews stream, the last processed value is a string (timestamp) or undefined (initial stream)
 */
export class StreamWithRetry<T, StreamLastProcessedValueType extends number | string | undefined> {
  private _isCancelled = false;
  private _isExhausted = false;
  /**
   * Unique ID for the stream. The extension keeps a map of streamId to AbortController. When we cancel
   * the stream from the webviews side, we call cancelStreamFn with this ID to close the real stream on the extension side.
   * */
  public readonly streamId: string;

  public get isCancelled() {
    return this._isCancelled;
  }

  public get isExhausted() {
    return this._isExhausted;
  }

  /**
   * Creates a new StreamWithRetry instance.
   *
   * @param agentId - The ID of the remote agent (or stream identifier)
   * @param lastProcessedValue - The last processed sequence ID (number) or timestamp (string)
   * @param startStreamFn - Function to start a stream
   * @param cancelStreamFn - Function to cancel a stream
   * @param maxRetries - Maximum number of retry attempts
   * @param baseDelay - Base delay between retries in milliseconds
   * @param attemptErrorThreshold - Number of consecutive errors to retry before showing an error to the user
   * @param unhandledErrorMessage - Message to display when an unhandled error occurs
   */
  constructor(
    private readonly agentId: string,
    private readonly lastProcessedValue: StreamLastProcessedValueType,
    private readonly startStreamFn: (
      agentId: string,
      streamId: string,
      lastProcessedValue: StreamLastProcessedValueType,
    ) => AsyncGenerator<T>,
    private readonly cancelStreamFn: (streamId: string) => Promise<void>,
    private readonly maxRetries: number = 5,
    private readonly baseDelay: number = 4000,
    private readonly attemptErrorThreshold: number = 2,
    private readonly unhandledErrorMessage?: string,
  ) {
    this.streamId = crypto.randomUUID();
  }

  /**
   * Cancels the stream.
   */
  public async cancel(): Promise<void> {
    this._isCancelled = true;
    await this.cancelStreamFn(this.streamId);
  }

  /**
   * Returns the stream of updates.
   *
   * Handles retriable errors on starting the stream. Once the stream has
   * started, any errors are passed through to the caller.
   *
   * @throws StreamRetryExhaustedError when all retry attempts have been exhausted
   * @throws Error for any other errors that occur during the stream
   */
  public async *getStream(): AsyncGenerator<RemoteAgentsStreamUpdate<T>> {
    let retryCount = 0;

    while (!this._isCancelled) {
      try {
        const generator = this.startStreamFn(this.agentId, this.streamId, this.lastProcessedValue);

        for await (const chunk of generator) {
          if (this._isCancelled) {
            return;
          }
          // Reset retry count on successful stream
          retryCount = 0;
          yield chunk;
        }
        // If we've consumed the stream to completion, we're done
        return;
      } catch (error) {
        // Treat all errors as retriable, only error after exhausting retries or cancellation.
        const errorMessage = error instanceof Error ? error.message : String(error);

        if (errorMessage === STREAM_CANCELLED) {
          // Message from aborted fetch, treat as cancellation
          this._isCancelled = true;
        }
        // Cancellation and exhaustion are the only non-retriable exit conditions
        if (this._isCancelled) {
          return;
        }

        // If we've exhausted retries, throw an error
        retryCount++;
        if (retryCount > this.maxRetries) {
          this._isExhausted = true;
          throw new StreamRetryExhaustedError(
            `Failed after ${this.maxRetries} attempts: ${errorMessage}`,
          );
        }

        let delayMs = this.baseDelay * 2 ** (retryCount - 1);

        // callApiStream has a built in timeout to cancel the fetch. But for remote agents streams,
        // we want to keep the stream open as long as the user is looking at the data. So we
        // treat the timeout error as a signal to check if the stream has been cancelled, and if it hasn't,
        // we should reestablish the connection.
        const isTimeoutError = errorMessage === STREAM_TIMEOUT;
        if (isTimeoutError) {
          // Immediately retry on timeout errors.
          delayMs = 0;
        } else {
          // Before sleeping, yield an error to the client so the user knows we're gonna retry in a bit.
          // This doesn't apply to timeout errors since those should be silently retried.
          // Only yield an error if we're at attempt >= attemptErrorThreshold
          if (retryCount > this.attemptErrorThreshold) {
            yield {
              errorMessage: this.unhandledErrorMessage ?? errorMessage,
              retryAt: new Date(Date.now() + delayMs),
            };
          }
        }

        console.warn(
          `Retrying remote agent stream in ${delayMs / 1000} seconds... (Attempt ${retryCount} of ${this.maxRetries})`,
        );

        await new Promise((resolve) => setTimeout(resolve, delayMs));
        continue;
      }
    }
  }
}
