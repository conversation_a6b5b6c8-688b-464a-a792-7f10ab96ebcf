// Retry configuration for API calls
const MAX_RETRIES = 3;
const RETRY_DELAY_BASE_MS = 1000; // Start with 1 second delay
const RETRY_DELAY_MAX_MS = 10000; // Max 10 second delay

/**
 * Generic polling manager to handle polling for different data types
 */
interface PollingConfig<T> {
  /** Default polling interval in milliseconds */
  defaultInterval: number;
  /** Function to refresh data */
  refreshFn: (id?: string) => Promise<T>;
  /** Optional condition to stop polling */
  stopCondition?: (data: T, id?: string) => boolean;
}

/**
 * Manages polling for a specific data type
 */
export class AgentsPollingManager<T> {
  private _pollingTimers = new Map<string, ReturnType<typeof setInterval>>();
  private _pollingInterval: number;
  private _failedAttempts = 0;
  private _lastSuccessfulFetch = 0;

  constructor(private readonly _config: PollingConfig<T>) {
    this._pollingInterval = _config.defaultInterval;
  }

  /**
   * Start polling for data
   * @param id Optional ID for entity-specific polling
   */
  start(id?: string): void {
    // If already polling for this ID, stop it first
    if (id && this._pollingTimers.has(id)) {
      this.stop(id);
    } else if (!id && this._pollingTimers.has("global")) {
      this.stop("global");
    }

    // Immediately fetch data
    this.refresh(id);

    // Set up interval for polling
    const timerId = setInterval(() => {
      this.refresh(id);
    }, this._pollingInterval);

    // Store timer ID
    if (id) {
      this._pollingTimers.set(id, timerId);
    } else {
      this._pollingTimers.set("global", timerId);
    }
  }

  /**
   * Stop polling for data
   * @param id Optional ID for entity-specific polling
   */
  stop(id?: string): void {
    if (id) {
      const timer = this._pollingTimers.get(id);
      if (timer) {
        clearInterval(timer);
        this._pollingTimers.delete(id);
      }
    } else {
      // Stop all timers if no ID provided
      for (const [timerId, timer] of this._pollingTimers.entries()) {
        clearInterval(timer);
        this._pollingTimers.delete(timerId);
      }
    }
  }

  /**
   * Refresh data immediately
   * @param id Optional ID for entity-specific refresh
   * @returns The refreshed data or null if an error occurred
   */
  async refresh(id?: string): Promise<T | null> {
    try {
      const result = await this._config.refreshFn(id);
      this._failedAttempts = 0;
      this._lastSuccessfulFetch = Date.now();
      this._pollingInterval = this._config.defaultInterval;

      // Check if we should stop polling based on the result
      if (this._config.stopCondition && id && this._config.stopCondition(result, id)) {
        this.stop(id);
      }

      return result;
    } catch (error) {
      this._failedAttempts++;

      // Exponential backoff for retries
      if (this._failedAttempts > MAX_RETRIES) {
        this._pollingInterval = RETRY_DELAY_MAX_MS;
      } else {
        this._pollingInterval = Math.min(
          RETRY_DELAY_BASE_MS * Math.pow(2, this._failedAttempts),
          RETRY_DELAY_MAX_MS,
        );
      }

      return null;
    }
  }

  /**
   * Check if polling is active for a specific ID or globally
   */
  isPolling(id?: string): boolean {
    if (id) {
      return this._pollingTimers.has(id);
    }
    return this._pollingTimers.size > 0;
  }

  get timeSinceLastSuccessfulFetch(): number {
    return Date.now() - this._lastSuccessfulFetch;
  }

  get failedAttempts(): number {
    return this._failedAttempts;
  }

  resetFailedAttempts(): void {
    this._failedAttempts = 0;
  }
}
