import { type GetRemoteAgentHistoryStreamResponse } from "@augment-internal/sidecar-libs/src/remote-agent/remote-agent-types";
import { type IRemoteAgentsError } from "./remote-agents-state-model";
import { StreamWithRetry } from "./stream-with-retry";

/**
 * Type-specific stream update for history streams
 */
export type RemoteAgentsHistoryStreamUpdate =
  | GetRemoteAgentHistoryStreamResponse
  | IRemoteAgentsError;

export const isHistoryStreamUpdate = (
  update: RemoteAgentsHistoryStreamUpdate,
): update is GetRemoteAgentHistoryStreamResponse => {
  return update != null && (update as GetRemoteAgentHistoryStreamResponse).updates !== undefined;
};

/**
 * RemoteAgentsHistoryStreamWithRetry provides a retry mechanism for streaming remote agent history.
 * It extends StreamWithRetry with default options optimized for history streams.
 */
export class RemoteAgentsHistoryStreamWithRetry extends StreamWithRetry<
  GetRemoteAgentHistoryStreamResponse,
  number
> {
  /**
   * Creates a new RemoteAgentsHistoryStreamWithRetry instance with default options for history streams.
   *
   * @param agentId - The ID of the remote agent
   * @param lastProcessedSequenceId - The last processed sequence ID
   * @param startStreamFn - Function to start a stream
   * @param cancelStreamFn - Function to cancel a stream
   * @param maxRetries - Maximum number of retry attempts (default: 5)
   * @param baseDelay - Base delay between retries in milliseconds (default: 4000)
   */
  constructor(
    agentId: string,
    lastProcessedSequenceId: number,
    startStreamFn: (
      agentId: string,
      streamId: string,
      lastProcessedSequenceId: number,
    ) => AsyncGenerator<GetRemoteAgentHistoryStreamResponse>,
    cancelStreamFn: (streamId: string) => Promise<void>,
    maxRetries: number = 5,
    baseDelay: number = 4000,
  ) {
    super(
      agentId,
      lastProcessedSequenceId,
      startStreamFn,
      cancelStreamFn,
      maxRetries,
      baseDelay,
      1, // attemptErrorThreshold - show errors after 1 failed attempt
      "There was an error connecting to the remote agent.", // unhandledErrorMessage
    );
  }
}
