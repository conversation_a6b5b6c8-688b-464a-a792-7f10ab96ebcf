/* eslint-disable @typescript-eslint/naming-convention */
import { type GetRemoteAgentHistoryStreamResponse } from "@augment-internal/sidecar-libs/src/remote-agent/remote-agent-types";
import { describe, expect, test } from "vitest";
import { isHistoryStreamUpdate } from "../remote-agents-history-stream-with-retry";
import { type IRemoteAgentsError } from "../remote-agents-state-model";

describe("isHistoryStreamUpdate", () => {
  test("should return true for valid GetRemoteAgentHistoryStreamResponse", () => {
    const validResponse: GetRemoteAgentHistoryStreamResponse = {
      updates: [
        {
          type: 1, // AgentHistoryUpdateType.AGENT_HISTORY_EXCHANGE
          exchange: {
            exchange: { id: "exchange-1" } as any,
            changed_files: [],
            sequence_id: 124,
          },
        },
      ],
    };

    const result = isHistoryStreamUpdate(validResponse);
    expect(result).toBe(true);
  });

  test("should return false for IRemoteAgentsError", () => {
    const errorResponse: IRemoteAgentsError = {
      errorMessage: "There was an error connecting to the remote agent.",
      retryAt: new Date(),
    };

    const result = isHistoryStreamUpdate(errorResponse);
    expect(result).toBe(false);
  });

  test("should return false for objects without updates property", () => {
    const invalidResponse = {
      someOtherProperty: "value",
    } as any;

    const result = isHistoryStreamUpdate(invalidResponse);
    expect(result).toBe(false);
  });

  test("should return false for null or undefined", () => {
    expect(isHistoryStreamUpdate(null as any)).toBe(false);
    expect(isHistoryStreamUpdate(undefined as any)).toBe(false);
  });
});
