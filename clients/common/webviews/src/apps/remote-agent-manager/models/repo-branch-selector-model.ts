import {
  type GithubBranch,
  type GithubRepo,
} from "@augment-internal/sidecar-libs/src/remote-agent/remote-agent-types";
import { type GitReferenceModel } from "./git-reference-model";
import { filterLocalGitBranches, getLocalBranchName, parseRemoteUrl } from "../utils/git";
import { type GitBranch } from "$vscode/src/remote-agent-manager/commit-ref-types";
import { get, writable, type Writable } from "svelte/store";

export class RepoBranchSelectorModel {
  /** The currently selected repo */
  private _selectedRepo: Writable<GithubRepo | undefined> = writable(undefined);
  /** The currently selected branch */
  private _selectedBranch: Writable<GithubBranch | undefined> = writable(undefined);

  /** All repos this user has access to */
  private _allRepos: Writable<GithubRepo[]> = writable([]);
  /** All branches for the selected repo */
  private _allBranches: Writable<GithubBranch[]> = writable([]);

  private _addedRepos: GithubRepo[] = [];

  /** Error message to display to the user */
  private _error: string | undefined = undefined;

  /** Whether the user is connected to a dev deploy */
  private _isDevDeploy = false;

  /** Number of pages to load at a time from the GitHub API */
  private static _defaultPagesPerLoad = 20;
  /** Number of repo pages that have been loaded */
  private _loadedRepoPages = 0;
  /** Whether there are more repo pages to load */
  private _hasMoreRepoPages = false;
  /** Number of branch pages that have been loaded */
  private _loadedBranchPages = 0;
  /** Whether there are more branch pages to load */
  private _hasMoreBranchPages = false;

  /** Last used repo URL name */
  private _lastUsedRepoUrl: string | undefined;
  /** Last used branch name */
  private _lastUsedBranchName: string | undefined;

  constructor(
    private readonly gitRefModel: GitReferenceModel,
    private readonly _pagesPerLoad = RepoBranchSelectorModel._defaultPagesPerLoad,
  ) {}

  get selectedRepo(): Writable<GithubRepo | undefined> {
    return this._selectedRepo;
  }

  set selectedRepo(repo: GithubRepo | undefined) {
    this._selectedRepo.set(repo);
    // Clear the branches when the repo changes
    this._allBranches.set([]);
  }

  get selectedBranch(): Writable<GithubBranch | undefined> {
    return this._selectedBranch;
  }

  set selectedBranch(branch: GithubBranch | undefined) {
    this._selectedBranch.set(branch);
  }

  get allRepos(): Writable<GithubRepo[]> {
    return this._allRepos;
  }

  get allBranches(): Writable<GithubBranch[]> {
    return this._allBranches;
  }

  get error(): string | undefined {
    return this._error;
  }

  get isDevDeploy(): boolean {
    return this._isDevDeploy;
  }

  set lastUsedRepoUrl(repoUrl: string | undefined) {
    this._lastUsedRepoUrl = repoUrl;
  }

  get lastUsedRepoUrl(): string | undefined {
    return this._lastUsedRepoUrl;
  }

  set lastUsedBranchName(branchName: string | undefined) {
    this._lastUsedBranchName = branchName;
  }

  get lastUsedBranchName(): string | undefined {
    return this._lastUsedBranchName;
  }

  /** Fetch repos available to the user and update allRepos */
  async updateReposList() {
    const { repos, error, isDevDeploy } = await this.gitRefModel.listUserRepos();
    if (isDevDeploy) {
      this._isDevDeploy = true;
      this._allRepos.set(await this.fetchLocalRepos());
      return;
    }
    if (error) {
      this._error = error;
      return;
    }
    this._isDevDeploy = isDevDeploy || false;
    try {
      // Check if the current workspace has a repo and is in the allRepos list
      const currentWorkspaceRepo = await this.getCurrentWorkspaceRepo();
      if (currentWorkspaceRepo) {
        const isInRepoList = repos.some((repo) => {
          return (
            repo.name === currentWorkspaceRepo.name && repo.owner === currentWorkspaceRepo.owner
          );
        });
        if (!isInRepoList) {
          // The user hasn't contributed to this repo yet, so we need to
          // check if it is public
          const { repo: currentRepo, error: repoError } =
            await this.gitRefModel.getGithubRepo(currentWorkspaceRepo);
          if (repoError) {
            console.error("Failed to fetch GitHub repo:", repoError);
          } else if (currentRepo) {
            repos.unshift(currentRepo);
          }
        }
      }
    } catch (error) {
      console.error("Failed to fetch GitHub repo:", error);
    }
    this._allRepos.set([...this._addedRepos, ...repos]);
  }

  async updateRepos(preselectRepo = false, onRepoPreselected?: () => void) {
    this._loadedRepoPages = 0;
    this._hasMoreBranchPages = false;
    this._allRepos.set([]);
    await this.updateReposAtPage(1, preselectRepo, onRepoPreselected);
    return this._hasMoreRepoPages;
  }

  /** Continue to load more repos and update allRepos */
  async loadMoreRepos() {
    if (!this._hasMoreRepoPages) {
      return false;
    }
    await this.updateReposAtPage(this._loadedRepoPages + 1);
    return this._hasMoreRepoPages;
  }

  /**
   * Pre-select a repo and update selectedRepo based on the current allRepos list
   *
   * If the current workspace repo is a public repo that the user has not contributed to yet,
   * then we will fetch the repo details and add it to the allRepos list.
   */
  async preselectRepo() {
    const selectedRepo = get(this._selectedRepo);
    if (selectedRepo) {
      // If we already have a selected repo, do nothing
      return false;
    }

    const selectLastUsedRepo = (repoUrl: string) => {
      const lastUsedRepo = get(this._allRepos).find((repo) => repo.html_url === repoUrl);
      if (lastUsedRepo) {
        this._selectedRepo.set(lastUsedRepo);
        return true;
      }
      return false;
    };

    // Check if the current workspace has a repo and is in the allRepos list
    const currentWorkspaceRepo = await this.getCurrentWorkspaceRepo();
    const allRepos = get(this._allRepos);
    if (currentWorkspaceRepo) {
      const userRepo = allRepos.find((repo) => {
        return repo.name === currentWorkspaceRepo.name && repo.owner === currentWorkspaceRepo.owner;
      });
      if (userRepo) {
        // Pre-select the current workspace repo
        this._selectedRepo.set(userRepo);
        return true;
      }
    }
    if (this._lastUsedRepoUrl) {
      return selectLastUsedRepo(this._lastUsedRepoUrl);
    } else {
      this._selectedRepo.set(allRepos[0]);
      return true;
    }
  }

  /**
   * Pre-select the user's current local workspace's repo and update selectedRepo
   *
   * Check to see if the user's current local workspace repo is a GitHub repo
   * and fetch for details on GitHub.
   *
   * @returns true if a repo was preselected, false otherwise
   */
  async preselectLocalRepo() {
    if (get(this._selectedRepo)) {
      return false;
    }
    const currentWorkspaceRepo = await this.getCurrentWorkspaceRepo();
    if (!currentWorkspaceRepo) {
      return false;
    }
    const { repo, error } = await this.gitRefModel.getGithubRepo(currentWorkspaceRepo);
    if (error) {
      // If we can't find the current repo, do not block as the user can select a repo
      // on their own
      console.error("Failed to preselect local repo:", error);
      return false;
    }
    this._selectedRepo.set(repo);
    return true;
  }

  addRepo(repo: GithubRepo) {
    this._addedRepos.push(repo);
    this._allRepos.update((repos) => {
      if (repos.some((r) => r.name === repo.name && r.owner === repo.owner)) {
        return repos;
      }
      return [repo, ...repos];
    });
  }

  /**
   * Fetch branches from zero for the selected repo and update allBranches
   *
   * @param preselectBranch Whether to preselect a branch while fetching the branches
   * @param onBranchPreselected Callback to call when a branch is preselected
   */
  async updateBranches(preselectBranch = false, onBranchPreselected?: () => void) {
    const selectedRepo = get(this._selectedRepo);
    if (!selectedRepo) {
      return false;
    }
    if (this._isDevDeploy) {
      this._allBranches.set(await this.fetchLocalBranches());
      return false;
    }
    this._loadedBranchPages = 0;
    this._hasMoreBranchPages = false;
    this._allBranches.set([]);
    await this.updateBranchesAtPage(1, preselectBranch, onBranchPreselected);
    return this._hasMoreBranchPages;
  }

  /** Continue to load more branches for the selected repo and update allBranches */
  async loadMoreBranches() {
    if (!this._hasMoreBranchPages) {
      return false;
    }
    await this.updateBranchesAtPage(this._loadedBranchPages + 1);
    return this._hasMoreBranchPages;
  }

  /**
   * Preselect a branch based on the last used branch name, the default branch, or the first branch
   *
   * @returns true if a branch was preselected, false otherwise
   */
  preselectBranch() {
    const selectedRepo = get(this._selectedRepo);
    if (!selectedRepo) {
      return false;
    }
    const selectedBranch = get(this._selectedBranch);
    if (selectedBranch) {
      return false;
    }
    const allBranches = get(this._allBranches);
    if (this._lastUsedBranchName) {
      const lastUsedBranch = allBranches.find((branch) => branch.name === this._lastUsedBranchName);
      if (lastUsedBranch) {
        this._selectedBranch.set(lastUsedBranch);
        return true;
      }
    }
    if (selectedRepo.default_branch) {
      const defaultBranch = allBranches.find(
        (branch) => branch.name === selectedRepo.default_branch,
      );
      if (defaultBranch) {
        this._selectedBranch.set(defaultBranch);
        return true;
      } else {
        // We have not yet loaded the default branch yet, so we will
        // optimistically select the default branch and then update
        // it when we load the default branch.
        this._selectedBranch.set({
          name: selectedRepo.default_branch,
          commit: { sha: "", url: "" },
          protected: false,
        });
        return true;
      }
    }
    this._selectedBranch.set(allBranches[0]);
    return true;
  }

  private async fetchLocalRepos() {
    console.warn("Fetching repos from local git environment.");
    const { remoteUrl, error: remoteUrlError } = await this.gitRefModel.getRemoteUrl();
    const parsedRemoteUrl = parseRemoteUrl(remoteUrl);
    if (!parsedRemoteUrl || remoteUrlError) {
      this._error = remoteUrlError ?? "Failed to parse remote URL in your local git repo.";
      return [];
    }
    const { owner: repoOwner, name: repoName } = parsedRemoteUrl;
    return [
      {
        name: repoName,
        owner: repoOwner,
        // eslint-disable-next-line @typescript-eslint/naming-convention
        html_url: remoteUrl,
      },
    ];
  }

  private async fetchLocalBranches() {
    console.warn("Fetching branches from local git environment.");
    const branchesResponse = await this.gitRefModel.listBranches();
    const branches = filterLocalGitBranches(branchesResponse.branches);
    const defaultBranch = branches.find((b) => b.isDefault);
    const toBranch = (branch: GitBranch) => {
      return {
        name: getLocalBranchName(branch.name),
        commit: { sha: "", url: "" },
        protected: false,
      };
    };
    if (defaultBranch) {
      this._selectedBranch.set(toBranch(defaultBranch));
    }
    return branches.map(toBranch);
  }

  private async getCurrentWorkspaceRepo(): Promise<GithubRepo | undefined> {
    const { remoteUrl, error: remoteUrlError } = await this.gitRefModel.getRemoteUrl();
    if (remoteUrlError) {
      return undefined;
    }
    const parsedRemoteUrl = parseRemoteUrl(remoteUrl);
    if (!parsedRemoteUrl) {
      return undefined;
    }
    const { owner: repoOwner, name: repoName } = parsedRemoteUrl;
    return {
      name: repoName,
      owner: repoOwner,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      html_url: remoteUrl,
    };
  }

  private async updateReposAtPage(
    page: number,
    preselectRepo = false,
    onRepoPreselected?: () => void,
  ) {
    if (page <= this._loadedRepoPages) {
      // We have already loaded this page, do nothing
      return;
    }

    do {
      const reposResponse = await this.gitRefModel.listUserRepos(page);
      if (reposResponse.error) {
        this._error = reposResponse.error;
        break;
      }
      if (reposResponse.isDevDeploy) {
        this._isDevDeploy = true;
        this._allRepos.set(await this.fetchLocalRepos());
        break;
      } else if (this._isDevDeploy) {
        this._isDevDeploy = false;
      }
      this._allRepos.update((currentRepos) => [...currentRepos, ...reposResponse.repos]);
      page = reposResponse.nextPage;
      this._loadedRepoPages++;
      this._hasMoreRepoPages = reposResponse.hasNextPage;
      if (preselectRepo && (await this.preselectRepo())) {
        onRepoPreselected?.();
      }
    } while (this._loadedRepoPages % this._pagesPerLoad !== 0 && this._hasMoreRepoPages);
  }

  private async updateBranchesAtPage(
    page: number,
    preselectBranch = false,
    onBranchPreselected?: () => void,
  ) {
    const currentRepo = get(this._selectedRepo);
    if (!currentRepo) {
      return;
    }
    if (page <= this._loadedBranchPages) {
      // We have already loaded this page, do nothing
      return;
    }

    do {
      if (currentRepo !== get(this._selectedRepo)) {
        // The selected repo changed while we were loading branches, so clear the current branches
        this._allBranches.set([]);
        break;
      }
      const branchesResponse = await this.gitRefModel.listRepoBranches(currentRepo, page);
      if (branchesResponse.error) {
        this._error = branchesResponse.error;
        break;
      }
      this._allBranches.update((currentBranches) => [
        ...currentBranches,
        ...branchesResponse.branches,
      ]);
      page = branchesResponse.nextPage;
      this._loadedBranchPages++;
      this._hasMoreBranchPages = branchesResponse.hasNextPage;
      if (preselectBranch && this.preselectBranch()) {
        onBranchPreselected?.();
      }
    } while (this._loadedBranchPages % this._pagesPerLoad !== 0 && this._hasMoreBranchPages);
  }
}
