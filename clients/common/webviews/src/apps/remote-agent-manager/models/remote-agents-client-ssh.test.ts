import { describe, it, expect, vi, beforeEach } from "vitest";
import { RemoteAgentsClient } from "./remote-agents-client";
import { WebViewMessageType } from "$vscode/src/webview-providers/webview-messages";
import type { MessageBroker } from "$common-webviews/src/common/utils/message-broker";

// Mock the message broker
const mockMessageBroker = {
  send: vi.fn(),
  sendStream: vi.fn(),
  dispose: vi.fn(),
} as unknown as MessageBroker;

describe("RemoteAgentsClient SSH Permission Tests", () => {
  let client: RemoteAgentsClient;

  beforeEach(() => {
    vi.clearAllMocks();
    client = new RemoteAgentsClient(mockMessageBroker);
  });

  describe("getShouldShowSSHConfigPermissionPrompt", () => {
    it("should send correct message and return response data", async () => {
      // Setup mock response
      const mockResponse = {
        type: WebViewMessageType.getShouldShowSSHConfigPermissionPromptResponse,
        data: true,
      };
      mockMessageBroker.send = vi.fn().mockResolvedValue(mockResponse);

      const result = await client.getShouldShowSSHConfigPermissionPrompt();

      expect(result).toBe(true);
      expect(mockMessageBroker.send).toHaveBeenCalledWith({
        type: WebViewMessageType.getShouldShowSSHConfigPermissionPromptRequest,
      });
    });

    it("should return false when response indicates no prompt needed", async () => {
      const mockResponse = {
        type: WebViewMessageType.getShouldShowSSHConfigPermissionPromptResponse,
        data: false,
      };
      mockMessageBroker.send = vi.fn().mockResolvedValue(mockResponse);

      const result = await client.getShouldShowSSHConfigPermissionPrompt();

      expect(result).toBe(false);
    });

    it("should handle message broker errors", async () => {
      const error = new Error("Message broker failed");
      mockMessageBroker.send = vi.fn().mockRejectedValue(error);

      await expect(client.getShouldShowSSHConfigPermissionPrompt()).rejects.toThrow(
        "Message broker failed",
      );
    });
  });

  describe("setPermissionToWriteToSSHConfig", () => {
    it("should send correct message with true permission", async () => {
      mockMessageBroker.send = vi.fn().mockResolvedValue({});

      await client.setPermissionToWriteToSSHConfig(true);

      expect(mockMessageBroker.send).toHaveBeenCalledWith({
        type: WebViewMessageType.setPermissionToWriteToSSHConfig,
        data: true,
      });
    });

    it("should send correct message with false permission", async () => {
      mockMessageBroker.send = vi.fn().mockResolvedValue({});

      await client.setPermissionToWriteToSSHConfig(false);

      expect(mockMessageBroker.send).toHaveBeenCalledWith({
        type: WebViewMessageType.setPermissionToWriteToSSHConfig,
        data: false,
      });
    });

    it("should handle message broker errors gracefully", async () => {
      const error = new Error("Failed to send message");
      mockMessageBroker.send = vi.fn().mockRejectedValue(error);

      await expect(client.setPermissionToWriteToSSHConfig(true)).rejects.toThrow(
        "Failed to send message",
      );
    });

    it("should not return any value (void function)", async () => {
      mockMessageBroker.send = vi.fn().mockResolvedValue({});

      const result = await client.setPermissionToWriteToSSHConfig(true);

      expect(result).toBeUndefined();
    });
  });

  describe("SSH Permission Integration", () => {
    it("should handle complete permission flow", async () => {
      // Step 1: Check if permission prompt should be shown
      mockMessageBroker.send = vi
        .fn()
        .mockResolvedValueOnce({
          type: WebViewMessageType.getShouldShowSSHConfigPermissionPromptResponse,
          data: true,
        })
        .mockResolvedValueOnce({}) // For setPermission call
        .mockResolvedValueOnce({
          type: WebViewMessageType.getShouldShowSSHConfigPermissionPromptResponse,
          data: false,
        });

      // Initial check - should show prompt
      const shouldShowInitially = await client.getShouldShowSSHConfigPermissionPrompt();
      expect(shouldShowInitially).toBe(true);

      // Set permission
      await client.setPermissionToWriteToSSHConfig(true);

      // Check again - should not show prompt
      const shouldShowAfter = await client.getShouldShowSSHConfigPermissionPrompt();
      expect(shouldShowAfter).toBe(false);

      // Verify all calls were made correctly
      expect(mockMessageBroker.send).toHaveBeenCalledTimes(3);
      expect(mockMessageBroker.send).toHaveBeenNthCalledWith(1, {
        type: WebViewMessageType.getShouldShowSSHConfigPermissionPromptRequest,
      });
      expect(mockMessageBroker.send).toHaveBeenNthCalledWith(2, {
        type: WebViewMessageType.setPermissionToWriteToSSHConfig,
        data: true,
      });
      expect(mockMessageBroker.send).toHaveBeenNthCalledWith(3, {
        type: WebViewMessageType.getShouldShowSSHConfigPermissionPromptRequest,
      });
    });
  });
});
