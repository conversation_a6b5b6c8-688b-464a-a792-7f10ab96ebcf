import { describe, expect, test, vi, beforeEach } from "vitest";
import { RemoteAgentsClient } from "./remote-agents-client";
import {
  WebViewMessageType,
  RemoteAgentResumeHintReason,
} from "$vscode/src/webview-providers/webview-messages";
import type { MessageBroker } from "$common-webviews/src/common/utils/message-broker";

describe("RemoteAgentsClient - Resume Hint", () => {
  let client: RemoteAgentsClient;
  let mockMsgBroker: MessageBroker;

  beforeEach(() => {
    mockMsgBroker = {
      send: vi.fn(),
      postMessage: vi.fn(),
    } as any;

    client = new RemoteAgentsClient(mockMsgBroker);
  });

  test("should send resume hint with default reason", async () => {
    const mockResponse = { type: WebViewMessageType.empty };
    (mockMsgBroker.send as any).mockResolvedValue(mockResponse);

    const agentId = "test-agent-123";
    await client.resumeHintRemoteAgent(agentId);

    expect(mockMsgBroker.send).toHaveBeenCalledWith(
      {
        type: WebViewMessageType.remoteAgentResumeHintRequest,
        data: {
          agentId,
          hintReason: RemoteAgentResumeHintReason.viewingAgent, // numeric enum value (2)
        },
      },
      10_000,
    );
  });

  test("should send resume hint with custom reason", async () => {
    const mockResponse = { type: WebViewMessageType.empty };
    (mockMsgBroker.send as any).mockResolvedValue(mockResponse);

    const agentId = "test-agent-123";
    const customReason = RemoteAgentResumeHintReason.typingMessage;

    await client.resumeHintRemoteAgent(agentId, customReason);

    expect(mockMsgBroker.send).toHaveBeenCalledWith(
      {
        type: WebViewMessageType.remoteAgentResumeHintRequest,
        data: {
          agentId,
          hintReason: RemoteAgentResumeHintReason.typingMessage, // numeric enum value (1)
        },
      },
      10_000,
    );
  });

  test("should handle resume hint failure", async () => {
    const error = new Error("Network error");
    (mockMsgBroker.send as any).mockRejectedValue(error);

    const agentId = "test-agent-123";

    await expect(client.resumeHintRemoteAgent(agentId)).rejects.toThrow("Network error");
  });

  test("should use correct timeout for resume hint", async () => {
    const mockResponse = { type: WebViewMessageType.empty };
    (mockMsgBroker.send as any).mockResolvedValue(mockResponse);

    const agentId = "test-agent-123";
    await client.resumeHintRemoteAgent(agentId);

    // Verify that the timeout is 10 seconds (10_000 ms)
    expect(mockMsgBroker.send).toHaveBeenCalledWith(expect.any(Object), 10_000);
  });
});

describe("RemoteAgentResumeHintReason enum", () => {
  test("should have correct enum values", () => {
    expect(RemoteAgentResumeHintReason.unspecified).toBe(0);
    expect(RemoteAgentResumeHintReason.typingMessage).toBe(1);
    expect(RemoteAgentResumeHintReason.viewingAgent).toBe(2);
  });
});
