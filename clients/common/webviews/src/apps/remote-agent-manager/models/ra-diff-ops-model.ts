import { type AsyncMsgSender } from "$common-webviews/src/common/hosts/async-messaging";
import {
  type ApplyChangesRequestMessage,
  type ApplyChangesResponseMessage,
  type CanApplyChangesRequestMessage,
  type CanApplyChangesResponseMessage,
  type DiffDescriptionsRequestMessage,
  type DiffDescriptionsResponseMessage,
  type DiffExplanationRequestMessage,
  type DiffExplanationResponseMessage,
  type DiffGroupChangesRequestMessage,
  type DiffGroupChangesResponseMessage,
  type EmptyMessage,
  type OpenFileRequestMessage,
  type OpenFileResponseMessage,
  type PreviewApplyChangesRequestMessage,
  type PreviewApplyChangesResponseMessage,
  type ReportAgentChangesAppliedMessage,
  type StashUnstagedChangesRequestMessage,
  type StashUnstagedChangesResponseMessage,
  WebViewMessageType,
} from "$vscode/src/webview-providers/webview-messages";
import { writable } from "svelte/store";

/**
 * Model that handles diff operations for remote agent changes.
 */
export class RemoteAgentDiffOpsModel {
  public static key = "remoteAgentsDiffOpsModel"; // for svelte context

  private _applyingFilePaths = writable<string[]>([]);
  private _appliedFilePaths = writable<string[]>([]);

  constructor(private readonly _asyncMsgSender: AsyncMsgSender) {}

  public get applyingFilePaths() {
    let result: string[] = [];
    this._applyingFilePaths.subscribe((value) => {
      result = value;
    })();
    return result;
  }

  public get appliedFilePaths() {
    let result: string[] = [];
    this._appliedFilePaths.subscribe((value) => {
      result = value;
    })();
    return result;
  }

  /**
   * Get explanation for diff changes
   * @param changedFiles The files to get explanations for
   * @param apikey Optional API key for the LLM
   * @param timeoutMs Optional timeout in milliseconds (default: 30000)
   */
  async getDiffExplanation(changedFiles: any[], apikey?: string, timeoutMs: number = 30000) {
    try {
      const response = await this._asyncMsgSender.send<
        DiffExplanationRequestMessage,
        DiffExplanationResponseMessage
      >(
        {
          type: WebViewMessageType.diffExplanationRequest,
          data: {
            changedFiles,
            apikey,
          },
        },
        timeoutMs,
      );
      return response.data.explanation;
    } catch (err) {
      console.error("Failed to get diff explanation:", err);
      return [];
    }
  }

  /**
   * Group changes by file using LLM
   */
  async groupChanges(changedFiles: any[], changesById = false, apikey?: string) {
    try {
      const response = await this._asyncMsgSender.send<
        DiffGroupChangesRequestMessage,
        DiffGroupChangesResponseMessage
      >({
        type: WebViewMessageType.diffGroupChangesRequest,
        data: {
          changedFiles,
          changesById,
          apikey,
        },
      });
      return response.data.groupedChanges;
    } catch (err) {
      console.error("Failed to group changes:", err);
      return [];
    }
  }

  /**
   * Get descriptions for grouped changes
   */
  async getDescriptions(groupedChanges: any[], apikey?: string) {
    try {
      const response = await this._asyncMsgSender.send<
        DiffDescriptionsRequestMessage,
        DiffDescriptionsResponseMessage
      >(
        {
          type: WebViewMessageType.diffDescriptionsRequest,
          data: {
            groupedChanges,
            apikey,
          },
        },
        100_000, // 100 second timeout for this step
      );
      return { explanation: response.data.explanation, error: response.data.error };
    } catch (err) {
      console.error("Failed to get descriptions:", err);
      return {
        explanation: [],
        error: `Failed to get descriptions: ${err instanceof Error ? err.message : String(err)}`,
      };
    }
  }

  async canApplyChanges() {
    try {
      const response = await this._asyncMsgSender.send<
        CanApplyChangesRequestMessage,
        CanApplyChangesResponseMessage
      >(
        {
          type: WebViewMessageType.canApplyChangesRequest,
        },
        10000, // 10 second timeout
      );
      return response.data;
    } catch (err) {
      console.error("Failed to check if can apply changes:", err);
      return {
        canApply: false,
        hasUnstagedChanges: false,
        error: `Failed to check if can apply changes: ${err instanceof Error ? err.message : String(err)}`,
      };
    }
  }

  /**
   * Apply changes to a file
   */
  async applyChanges(
    path: string,
    originalCode: string,
    newCode: string,
  ): Promise<{
    success: boolean;
    hasConflicts?: boolean;
    error?: string;
  }> {
    this._applyingFilePaths.update((paths) => [...paths.filter((item) => item !== path), path]);

    try {
      const response = await this._asyncMsgSender.send<
        ApplyChangesRequestMessage,
        ApplyChangesResponseMessage
      >(
        {
          type: WebViewMessageType.applyChangesRequest,
          data: {
            path,
            originalCode,
            newCode,
          },
        },
        30000, // 30 second timeout
      );
      const { success, hasConflicts, error } = response.data;
      if (success) {
        this._appliedFilePaths.update((paths) => [...paths.filter((item) => item !== path), path]);
      } else if (error) {
        console.error("Failed to apply changes:", error);
      }
      return { success, hasConflicts, error };
    } catch (err) {
      console.error("applyChanges error", err);
      return {
        success: false,
        error: `Error: ${err instanceof Error ? err.message : String(err)}`,
      };
    } finally {
      this._applyingFilePaths.update((paths) => paths.filter((item) => item !== path));
    }
  }

  async previewApplyChanges(
    path: string,
    originalCode: string,
    newCode: string,
  ): Promise<{
    mergedContent: string;
    hasConflicts: boolean;
    error?: string;
  }> {
    try {
      const response = await this._asyncMsgSender.send<
        PreviewApplyChangesRequestMessage,
        PreviewApplyChangesResponseMessage
      >(
        {
          type: WebViewMessageType.previewApplyChangesRequest,
          data: {
            path,
            originalCode,
            newCode,
          },
        },
        30000, // 30 second timeout
      );
      return response.data;
    } catch (err) {
      console.error("previewApplyChanges error", err);
      return {
        mergedContent: "",
        hasConflicts: false,
        error: `Error: ${err instanceof Error ? err.message : String(err)}`,
      };
    }
  }

  /**
   * Open a file in the editor
   */
  async openFile(path: string): Promise<boolean> {
    try {
      const response = await this._asyncMsgSender.send<
        OpenFileRequestMessage,
        OpenFileResponseMessage
      >(
        {
          type: WebViewMessageType.openFileRequest,
          data: {
            path,
          },
        },
        10000, // 10 second timeout
      );
      if (!response.data.success) {
        console.error("Failed to open file:", response.data.error);
      }
      return response.data.success;
    } catch (err) {
      console.error("openFile error", err);
    }

    return false;
  }

  async stashUnstagedChanges() {
    try {
      const response = await this._asyncMsgSender.send<
        StashUnstagedChangesRequestMessage,
        StashUnstagedChangesResponseMessage
      >(
        {
          type: WebViewMessageType.stashUnstagedChangesRequest,
        },
        10000, // 10 second timeout
      );
      if (!response.data.success) {
        console.error("Failed to stash unstaged changes:", response.data.error);
      }
      return response.data.success;
    } catch (err) {
      console.error("stashUnstagedChanges error", err);
    }

    return false;
  }

  /**
   * Report apply changes event
   */
  async reportApplyChangesEvent() {
    await this._asyncMsgSender.send<ReportAgentChangesAppliedMessage, EmptyMessage>({
      type: WebViewMessageType.reportAgentChangesApplied,
    });
  }
}
