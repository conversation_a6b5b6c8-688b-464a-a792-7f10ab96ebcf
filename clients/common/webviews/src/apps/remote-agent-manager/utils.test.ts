/* eslint-disable @typescript-eslint/naming-convention */
import { describe, expect, it } from "vitest";
import {
  getAggregateChanges,
  getUserMessagePrecedingTurn,
  getIndexOfPrecedingUserMessage,
  getIndexOfNextUserMessage,
  getTurnList,
  getChangesForTurn,
  getAllChangesBetweenUserMessages,
} from "./utils";
import {
  type RemoteAgentExchange,
  type ChangedFile,
  FileChangeType,
} from "@augment-internal/sidecar-libs/src/remote-agent/remote-agent-types";
import {
  ChatRequestNodeType,
  ChatResultNodeType,
  type Exchange,
  type ChatRequestNode,
  type ChatResultNode,
} from "@augment-internal/sidecar-libs/src/chat/chat-types";

// Mock data helpers
const createExchange = (
  requestMessage: string = "",
  responseText: string = "",
  requestNodes: ChatRequestNode[] = [],
  responseNodes: ChatResultNode[] = [],
): Exchange => ({
  request_message: requestMessage,
  response_text: responseText,
  request_id: "test-id",
  request_nodes: requestNodes,
  response_nodes: responseNodes,
});

const createRemoteAgentExchange = (
  exchange: Exchange,
  changedFiles: ChangedFile[] = [],
  sequenceId: number = 1,
): RemoteAgentExchange => ({
  exchange,
  changed_files: changedFiles,
  sequence_id: sequenceId,
  finished_at: "2023-01-01T00:00:00Z",
});

const createChangedFile = (
  newPath: string,
  oldPath: string = "",
  newContents: string = "new content",
  oldContents: string = "old content",
  changeType: FileChangeType = FileChangeType.modified,
): ChangedFile => ({
  id: `file-${newPath}`,
  new_path: newPath,
  old_path: oldPath,
  new_contents: newContents,
  old_contents: oldContents,
  change_type: changeType,
});

const createToolUseNode = (toolUseId: string): ChatResultNode => ({
  id: 1,
  type: ChatResultNodeType.TOOL_USE,
  content: "",
  tool_use: {
    tool_use_id: toolUseId,
    tool_name: "test-tool",
    input_json: "{}",
  },
});

const createToolResultNode = (toolUseId: string): ChatRequestNode => ({
  id: 1,
  type: ChatRequestNodeType.TOOL_RESULT,
  tool_result_node: {
    tool_use_id: toolUseId,
    content: "tool result",
    is_error: false,
    content_nodes: [],
  },
});

// Mock exchange list for testing
const mockExchanges: RemoteAgentExchange[] = [
  // Exchange 0: First user message
  createRemoteAgentExchange(createExchange("Create a new React component", ""), [], 1),
  // Exchange 1: Agent response with tool use
  createRemoteAgentExchange(
    createExchange(
      "",
      "I'll create a React component for you.",
      [],
      [createToolUseNode("tool-123")],
    ),
    [],
    2,
  ),
  // Exchange 2: Tool result
  createRemoteAgentExchange(
    createExchange("", "Component created successfully.", [createToolResultNode("tool-123")]),
    [
      createChangedFile(
        "Component.tsx",
        "",
        "export const Component = () => <div>Hello</div>;",
        "",
      ),
    ],
    3,
  ),
  // Exchange 3: Agent continues
  createRemoteAgentExchange(
    createExchange("", "Now let me add some styling."),
    [
      createChangedFile(
        "Component.tsx",
        "Component.tsx",
        "export const Component = () => <div className='hello'>Hello</div>;",
        "export const Component = () => <div>Hello</div>;",
      ),
    ],
    4,
  ),
  // Exchange 4: Second user message
  createRemoteAgentExchange(createExchange("Add a prop for the message", ""), [], 5),
  // Exchange 5: Agent response to second user message
  createRemoteAgentExchange(
    createExchange("", "I'll add a message prop.", [], [createToolUseNode("tool-456")]),
    [],
    6,
  ),
  // Exchange 6: Tool result for second tool use
  createRemoteAgentExchange(
    createExchange("", "Prop added successfully.", [createToolResultNode("tool-456")]),
    [
      createChangedFile("types.ts", "", "export interface ComponentProps { message: string; }", ""),
      createChangedFile(
        "Component.tsx",
        "Component.tsx",
        "export const Component = ({ message }: { message: string }) => <div className='hello'>{message}</div>;",
        "export const Component = () => <div className='hello'>Hello</div>;",
      ),
    ],
    7,
  ),
];

describe("utils - exchange and turn functions", () => {
  describe("getAggregateChanges", () => {
    it("should return empty array for empty chat history", () => {
      const result = getAggregateChanges([]);
      expect(result).toEqual([]);
    });

    it("should aggregate all changes from mock exchanges", () => {
      const result = getAggregateChanges(mockExchanges);
      expect(result).toHaveLength(2); // Component.tsx and types.ts

      const componentFile = result.find((f) => f.new_path === "Component.tsx");
      expect(componentFile).toBeDefined();
      expect(componentFile?.new_contents).toContain("message: string");

      const typesFile = result.find((f) => f.new_path === "types.ts");
      expect(typesFile).toBeDefined();
    });

    it("should handle file modifications correctly", () => {
      // Using exchanges 1-3 which modify Component.tsx
      const result = getAggregateChanges(mockExchanges.slice(1, 4));
      expect(result).toHaveLength(1);
      expect(result[0].new_path).toBe("Component.tsx");
      expect(result[0].new_contents).toContain("className='hello'");
    });

    it("should filter out files that have been reverted to original state", () => {
      const file1v1 = createChangedFile("file1.ts", "file1.ts", "modified", "original");
      const file1v2 = createChangedFile("file1.ts", "file1.ts", "original", "original"); // Reverted

      const exchange1 = createRemoteAgentExchange(createExchange("message1"), [file1v1], 1);
      const exchange2 = createRemoteAgentExchange(createExchange("message2"), [file1v2], 2);

      const result = getAggregateChanges([exchange1, exchange2]);
      expect(result).toHaveLength(0); // Should be filtered out
    });

    it("should keep added and deleted files even if content matches", () => {
      const addedFile = createChangedFile("new.ts", "", "content", "", FileChangeType.added);
      const deletedFile = createChangedFile("", "old.ts", "", "content", FileChangeType.deleted);

      const exchange = createRemoteAgentExchange(
        createExchange("message"),
        [addedFile, deletedFile],
        1,
      );

      const result = getAggregateChanges([exchange]);
      expect(result).toHaveLength(2);
    });

    it("should handle single deleted file correctly", () => {
      const deletedFile = createChangedFile(
        "",
        "test_readme.md",
        "",
        "This is a test readme file.\nThis is the second line added to the test readme.\n",
        FileChangeType.deleted,
      );

      const exchange = createRemoteAgentExchange(createExchange("Delete file"), [deletedFile], 1);

      const result = getAggregateChanges([exchange]);
      expect(result).toHaveLength(1);
      expect(result[0].old_path).toBe("test_readme.md");
      expect(result[0].new_path).toBe("");
      expect(result[0].change_type).toBe(FileChangeType.deleted);
      expect(result[0].new_contents).toBe("");
      expect(result[0].old_contents).toBe(
        "This is a test readme file.\nThis is the second line added to the test readme.\n",
      );
    });

    it("should handle multiple deleted files correctly", () => {
      const deletedFile1 = createChangedFile(
        "",
        "file1.ts",
        "",
        "content of file1",
        FileChangeType.deleted,
      );
      const deletedFile2 = createChangedFile(
        "",
        "file2.ts",
        "",
        "content of file2",
        FileChangeType.deleted,
      );
      const deletedFile3 = createChangedFile(
        "",
        "file3.ts",
        "",
        "content of file3",
        FileChangeType.deleted,
      );

      const exchange = createRemoteAgentExchange(
        createExchange("Delete multiple files"),
        [deletedFile1, deletedFile2, deletedFile3],
        1,
      );

      const result = getAggregateChanges([exchange]);
      expect(result).toHaveLength(3);

      const file1Result = result.find((f) => f.old_path === "file1.ts");
      const file2Result = result.find((f) => f.old_path === "file2.ts");
      const file3Result = result.find((f) => f.old_path === "file3.ts");

      expect(file1Result).toBeDefined();
      expect(file2Result).toBeDefined();
      expect(file3Result).toBeDefined();

      expect(file1Result?.change_type).toBe(FileChangeType.deleted);
      expect(file2Result?.change_type).toBe(FileChangeType.deleted);
      expect(file3Result?.change_type).toBe(FileChangeType.deleted);
    });

    it("should handle mixed added, modified, and deleted files", () => {
      const addedFile = createChangedFile("new.ts", "", "new content", "", FileChangeType.added);
      const modifiedFile = createChangedFile(
        "existing.ts",
        "existing.ts",
        "modified content",
        "original content",
        FileChangeType.modified,
      );
      const deletedFile = createChangedFile(
        "",
        "old.ts",
        "",
        "deleted content",
        FileChangeType.deleted,
      );

      const exchange = createRemoteAgentExchange(
        createExchange("Mixed operations"),
        [addedFile, modifiedFile, deletedFile],
        1,
      );

      const result = getAggregateChanges([exchange]);
      expect(result).toHaveLength(3);

      const addedResult = result.find((f) => f.new_path === "new.ts");
      const modifiedResult = result.find((f) => f.new_path === "existing.ts");
      const deletedResult = result.find((f) => f.old_path === "old.ts");

      expect(addedResult).toBeDefined();
      expect(modifiedResult).toBeDefined();
      expect(deletedResult).toBeDefined();

      expect(addedResult?.change_type).toBe(FileChangeType.added);
      expect(modifiedResult?.change_type).toBe(FileChangeType.modified);
      expect(deletedResult?.change_type).toBe(FileChangeType.deleted);
    });

    it("should handle multiple deletions of the same file (latest wins)", () => {
      const deletedFile1 = createChangedFile(
        "",
        "same.ts",
        "",
        "first deletion content",
        FileChangeType.deleted,
      );
      const deletedFile2 = createChangedFile(
        "",
        "same.ts",
        "",
        "second deletion content",
        FileChangeType.deleted,
      );

      const exchange1 = createRemoteAgentExchange(
        createExchange("First deletion"),
        [deletedFile1],
        1,
      );
      const exchange2 = createRemoteAgentExchange(
        createExchange("Second deletion"),
        [deletedFile2],
        2,
      );

      const result = getAggregateChanges([exchange1, exchange2]);
      expect(result).toHaveLength(1);
      expect(result[0].old_path).toBe("same.ts");
      expect(result[0].old_contents).toBe("second deletion content"); // Latest should win
    });

    it("should filter out files that are added then deleted in aggregate view (no net change)", () => {
      const addedFile = createChangedFile(
        "temp.ts",
        "",
        "temporary content",
        "",
        FileChangeType.added,
      );
      const deletedFile = createChangedFile(
        "",
        "temp.ts",
        "",
        "temporary content",
        FileChangeType.deleted,
      );

      const exchange1 = createRemoteAgentExchange(createExchange("Add file"), [addedFile], 1);
      const exchange2 = createRemoteAgentExchange(createExchange("Delete file"), [deletedFile], 2);

      // Aggregate view should filter out add+delete cycles
      const aggregateResult = getAggregateChanges([exchange1, exchange2]);
      expect(aggregateResult).toHaveLength(0); // Should be filtered out as no net change
    });

    it("should show individual turn changes even when aggregate shows no net change", () => {
      const addedFile = createChangedFile(
        "temp.ts",
        "",
        "temporary content",
        "",
        FileChangeType.added,
      );
      const deletedFile = createChangedFile(
        "",
        "temp.ts",
        "",
        "temporary content",
        FileChangeType.deleted,
      );

      // Create separate turns with user messages between them
      const userMessage1 = createRemoteAgentExchange(
        createExchange("Create a temp file", ""),
        [],
        0,
      );
      const agentAdd = createRemoteAgentExchange(
        createExchange("", "I'll create the file."),
        [addedFile],
        1,
      );
      const userMessage2 = createRemoteAgentExchange(createExchange("Now delete it", ""), [], 2);
      const agentDelete = createRemoteAgentExchange(
        createExchange("", "I'll delete the file."),
        [deletedFile],
        3,
      );

      const fullConversation = [userMessage1, agentAdd, userMessage2, agentDelete];

      // Aggregate view should show no net change
      const aggregateResult = getAggregateChanges(fullConversation);
      expect(aggregateResult).toHaveLength(0);

      // First turn should show the addition
      const turn1Result = getAllChangesBetweenUserMessages(fullConversation, 1);
      expect(turn1Result).toHaveLength(1);
      expect(turn1Result[0].change_type).toBe(FileChangeType.added);

      // Second turn should show the deletion
      const turn2Result = getAllChangesBetweenUserMessages(fullConversation, 3);
      expect(turn2Result).toHaveLength(1);
      expect(turn2Result[0].change_type).toBe(FileChangeType.deleted);
    });

    it("should handle complex sequence: modify, delete, add with different content", () => {
      const modifiedFile = createChangedFile(
        "complex.ts",
        "complex.ts",
        "modified content",
        "original content",
        FileChangeType.modified,
      );
      const deletedFile = createChangedFile(
        "",
        "complex.ts",
        "",
        "modified content",
        FileChangeType.deleted,
      );
      const addedFile = createChangedFile(
        "complex.ts",
        "",
        "new content",
        "",
        FileChangeType.added,
      );

      const exchange1 = createRemoteAgentExchange(createExchange("Modify file"), [modifiedFile], 1);
      const exchange2 = createRemoteAgentExchange(createExchange("Delete file"), [deletedFile], 2);
      const exchange3 = createRemoteAgentExchange(createExchange("Add new file"), [addedFile], 3);

      const result = getAggregateChanges([exchange1, exchange2, exchange3]);
      expect(result).toHaveLength(1); // Net effect: original file replaced with new content
      expect(result[0].new_path).toBe("complex.ts");
      expect(result[0].new_contents).toBe("new content");
    });

    it("should show deleted files in both turn-specific and aggregate views when file existed before", () => {
      const userMessage = createRemoteAgentExchange(
        createExchange("Delete the readme file", ""),
        [],
        1,
      );
      const deletedFile = createChangedFile(
        "",
        "README.md",
        "",
        "# Project README\nThis file existed before the conversation.",
        FileChangeType.deleted,
      );
      const agentResponse = createRemoteAgentExchange(
        createExchange("", "I'll delete the README file for you."),
        [deletedFile],
        2,
      );

      // Test turn-specific view - should show the deletion
      const turnResult = getAllChangesBetweenUserMessages([userMessage, agentResponse], 1);
      expect(turnResult).toHaveLength(1);
      expect(turnResult[0].old_path).toBe("README.md");
      expect(turnResult[0].new_path).toBe("");
      expect(turnResult[0].change_type).toBe(FileChangeType.deleted);

      // Test aggregate view - should also show the deletion since it's a net change (file existed before)
      const aggregateResult = getAggregateChanges([userMessage, agentResponse]);
      expect(aggregateResult).toHaveLength(1);
      expect(aggregateResult[0].old_path).toBe("README.md");
      expect(aggregateResult[0].change_type).toBe(FileChangeType.deleted);
    });

    it("should show files added then deleted then added again with different content", () => {
      const addedFile1 = createChangedFile(
        "cycle.ts",
        "",
        "first content",
        "",
        FileChangeType.added,
      );
      const deletedFile = createChangedFile(
        "",
        "cycle.ts",
        "",
        "first content",
        FileChangeType.deleted,
      );
      const addedFile2 = createChangedFile(
        "cycle.ts",
        "",
        "second content",
        "",
        FileChangeType.added,
      );

      const exchange1 = createRemoteAgentExchange(createExchange("Add file"), [addedFile1], 1);
      const exchange2 = createRemoteAgentExchange(createExchange("Delete file"), [deletedFile], 2);
      const exchange3 = createRemoteAgentExchange(
        createExchange("Add file again"),
        [addedFile2],
        3,
      );

      // Aggregate view should show net change (file didn't exist, now exists with content)
      const aggregateResult = getAggregateChanges([exchange1, exchange2, exchange3]);
      expect(aggregateResult).toHaveLength(1);
      expect(aggregateResult[0].new_path).toBe("cycle.ts");
      expect(aggregateResult[0].new_contents).toBe("second content");
      expect(aggregateResult[0].change_type).toBe(FileChangeType.added);
    });

    it("should handle multiple modifications to the same file", () => {
      const addedFile1 = createChangedFile(
        "multiple.ts",
        "multiple.ts",
        "first update",
        "original content",
        FileChangeType.modified,
      );
      const addedFile2 = createChangedFile(
        "multiple.ts",
        "",
        "second update with first update",
        "first update",
        FileChangeType.modified,
      );

      const exchange1 = createRemoteAgentExchange(createExchange("Update file"), [addedFile1], 1);
      const exchange2 = createRemoteAgentExchange(
        createExchange("Add more to file"),
        [addedFile2],
        2,
      );

      // Aggregate view should show net change (file had no, now exists with content)
      const aggregateResult = getAggregateChanges([exchange1, exchange2]);
      expect(aggregateResult).toHaveLength(1);
      expect(aggregateResult[0].new_path).toBe("multiple.ts");
      expect(aggregateResult[0].new_contents).toBe("second update with first update");
      expect(
        aggregateResult[0].old_contents,
        "Should have original content, not intermediate update",
      ).toBe("original content");
      expect(aggregateResult[0].change_type).toBe(FileChangeType.modified);
    });
  });

  describe("getUserMessagePrecedingTurn", () => {
    it("should return first user message for turn 1", () => {
      const result = getUserMessagePrecedingTurn(mockExchanges, 1);
      expect(result).toBe("Create a new React component");
    });

    it("should return first user message for turn 3", () => {
      const result = getUserMessagePrecedingTurn(mockExchanges, 3);
      expect(result).toBe("Create a new React component");
    });

    it("should return second user message for turn 5", () => {
      const result = getUserMessagePrecedingTurn(mockExchanges, 5);
      expect(result).toBe("Add a prop for the message");
    });

    it("should return the user message when called on a user message", () => {
      // With the edge case fix, when called on a user message, it returns that message
      const result = getUserMessagePrecedingTurn(mockExchanges, 0);
      expect(result).toBe("Create a new React component");
    });
  });

  describe("getIndexOfPrecedingUserMessage", () => {
    it("should return 0 for exchanges after first user message", () => {
      expect(getIndexOfPrecedingUserMessage(mockExchanges, 1)).toBe(0);
      expect(getIndexOfPrecedingUserMessage(mockExchanges, 2)).toBe(0);
      expect(getIndexOfPrecedingUserMessage(mockExchanges, 3)).toBe(0);
    });

    it("should return 4 for exchanges after second user message", () => {
      expect(getIndexOfPrecedingUserMessage(mockExchanges, 5)).toBe(4);
      expect(getIndexOfPrecedingUserMessage(mockExchanges, 6)).toBe(4);
    });

    it("should return the same index when called on a user message", () => {
      // With the edge case fix, when called on a user message, it returns that index
      expect(getIndexOfPrecedingUserMessage(mockExchanges, 0)).toBe(0);
      expect(getIndexOfPrecedingUserMessage(mockExchanges, 4)).toBe(4);
    });

    // Edge case tests
    it("should handle empty chat history", () => {
      expect(getIndexOfPrecedingUserMessage([], 0)).toBe(-1);
    });

    it("should handle history with no user messages", () => {
      const agentOnlyExchanges = [
        createRemoteAgentExchange(createExchange("", "Agent response 1"), [], 1),
        createRemoteAgentExchange(createExchange("", "Agent response 2"), [], 2),
      ];
      expect(getIndexOfPrecedingUserMessage(agentOnlyExchanges, 0)).toBe(-1);
      expect(getIndexOfPrecedingUserMessage(agentOnlyExchanges, 1)).toBe(-1);
    });

    it("should handle out of bounds turnIndex", () => {
      expect(getIndexOfPrecedingUserMessage(mockExchanges, 100)).toBe(-1); // Out of bounds should return -1
      expect(getIndexOfPrecedingUserMessage(mockExchanges, -1)).toBe(-1); // Negative index should return -1
    });
  });

  describe("getIndexOfNextUserMessage", () => {
    it("should return 4 for exchanges before second user message", () => {
      // The function searches from the turnIndex, not after it
      expect(getIndexOfNextUserMessage(mockExchanges, 1)).toBe(4);
      expect(getIndexOfNextUserMessage(mockExchanges, 2)).toBe(4);
      expect(getIndexOfNextUserMessage(mockExchanges, 3)).toBe(4);
    });

    it("should return history length for exchanges after last user message", () => {
      expect(getIndexOfNextUserMessage(mockExchanges, 5)).toBe(mockExchanges.length);
      expect(getIndexOfNextUserMessage(mockExchanges, 6)).toBe(mockExchanges.length);
    });

    it("should return the next user message index when called on a user message", () => {
      expect(getIndexOfNextUserMessage(mockExchanges, 0)).toBe(4);
      expect(getIndexOfNextUserMessage(mockExchanges, 4)).toBe(mockExchanges.length);
    });
  });

  describe("getTurnList", () => {
    it("should return exchanges for first turn", () => {
      const result = getTurnList(mockExchanges, 1);
      expect(result).toHaveLength(3); // Exchanges 1, 2, 3 (agent responses only)
      expect(result[0]).toBe(mockExchanges[1]); // Agent response
      expect(result[1]).toBe(mockExchanges[2]); // Tool result
      expect(result[2]).toBe(mockExchanges[3]); // Agent continues
    });

    it("should return exchanges for second turn", () => {
      const result = getTurnList(mockExchanges, 5);
      expect(result).toHaveLength(2); // Exchanges 5, 6 (agent responses only)
      expect(result[0]).toBe(mockExchanges[5]); // Agent response
      expect(result[1]).toBe(mockExchanges[6]); // Tool result
    });

    it("should handle turn at the beginning of conversation", () => {
      const result = getTurnList(mockExchanges, 0);
      expect(result).toHaveLength(3); // Exchanges 1, 2, 3 (agent responses after first user message)
      expect(result[0]).toBe(mockExchanges[1]); // Agent response
      expect(result[1]).toBe(mockExchanges[2]); // Tool result
      expect(result[2]).toBe(mockExchanges[3]); // Agent continues
    });

    it("should return all exchanges after last user message", () => {
      const result = getTurnList(mockExchanges, 6);
      expect(result).toHaveLength(2); // Exchanges 5, 6 (agent responses after last user message)
      expect(result[0]).toBe(mockExchanges[5]); // Agent response
      expect(result[1]).toBe(mockExchanges[6]); // Tool result
    });
  });

  describe("getChangesForTurn", () => {
    it("should return changes for first tool use", () => {
      const result = getChangesForTurn(mockExchanges, 1);
      expect(result).toHaveLength(1); // Only Component.tsx from exchange 1

      const componentFile = result.find((f) => f.new_path === "Component.tsx");
      expect(componentFile).toBeDefined();
      expect(componentFile?.new_contents).toContain("export const Component");
    });

    it("should return changes for second tool use", () => {
      const result = getChangesForTurn(mockExchanges, 5);
      expect(result).toHaveLength(2); // Component.tsx and types.ts

      const componentFile = result.find((f) => f.new_path === "Component.tsx");
      expect(componentFile).toBeDefined();
      expect(componentFile?.new_contents).toContain("message: string");

      const typesFile = result.find((f) => f.new_path === "types.ts");
      expect(typesFile).toBeDefined();
    });

    it("should return empty array if no tool call at turn", () => {
      // Exchange 3 has no tool call
      const result = getChangesForTurn(mockExchanges, 3);
      expect(result).toEqual([]);
    });

    it("should return empty array if at user message turn", () => {
      // Exchange 0 and 4 are user messages
      expect(getChangesForTurn(mockExchanges, 0)).toEqual([]);
      expect(getChangesForTurn(mockExchanges, 4)).toEqual([]);
    });

    it("should handle missing tool result", () => {
      // Create exchange with tool use but no matching tool result
      const exchangeWithNoResult = createRemoteAgentExchange(
        createExchange("", "response", [], [createToolUseNode("orphan-tool")]),
        [createChangedFile("orphan.ts", "", "content", "")],
        8,
      );

      const result = getChangesForTurn([...mockExchanges, exchangeWithNoResult], 7);
      expect(result).toEqual([]);
    });
  });

  describe("getAllChangesBetweenUserMessages", () => {
    it("should return changes for first turn", () => {
      const result = getAllChangesBetweenUserMessages(mockExchanges, 1);
      expect(result).toHaveLength(1); // Component.tsx

      const componentFile = result.find((f) => f.new_path === "Component.tsx");
      expect(componentFile).toBeDefined();
      expect(componentFile?.new_contents).toContain("className='hello'");
    });

    it("should return changes for second turn", () => {
      const result = getAllChangesBetweenUserMessages(mockExchanges, 5);
      expect(result).toHaveLength(2); // Component.tsx and types.ts

      const componentFile = result.find((f) => f.new_path === "Component.tsx");
      expect(componentFile).toBeDefined();
      expect(componentFile?.new_contents).toContain("message: string");

      const typesFile = result.find((f) => f.new_path === "types.ts");
      expect(typesFile).toBeDefined();
    });

    it("should handle conversation ending with user message and response with no changed files", () => {
      // This is the specific edge case mentioned by the user
      const conversationWithNoFilesAtEnd = [
        ...mockExchanges,
        // Add a user message
        createRemoteAgentExchange(createExchange("Just say hello", ""), [], 8),
        // Add agent response with no changed files
        createRemoteAgentExchange(createExchange("", "Hello! How can I help you?"), [], 9),
      ];

      const result = getAllChangesBetweenUserMessages(conversationWithNoFilesAtEnd, 7);
      expect(result).toEqual([]); // Should return empty array since no files were changed
    });

    it("should handle empty chat history", () => {
      const result = getAllChangesBetweenUserMessages([], 0);
      expect(result).toEqual([]);
    });

    it("should handle single user message with no responses", () => {
      const singleUserMessage = [createRemoteAgentExchange(createExchange("Hello", ""), [], 1)];

      const result = getAllChangesBetweenUserMessages(singleUserMessage, 0);
      expect(result).toEqual([]); // No exchanges between user messages
    });

    it("should handle conversation with only agent responses (no user messages)", () => {
      const agentOnlyExchanges = [
        createRemoteAgentExchange(
          createExchange("", "Agent response 1"),
          [createChangedFile("file1.ts", "", "content1", "")],
          1,
        ),
        createRemoteAgentExchange(
          createExchange("", "Agent response 2"),
          [createChangedFile("file2.ts", "", "content2", "")],
          2,
        ),
      ];

      // When there are no user messages, we should return all changes since there are no boundaries
      const result = getAllChangesBetweenUserMessages(agentOnlyExchanges, 0);
      expect(result).toHaveLength(2); // All files are included since there are no user message boundaries
      expect(result.find((f) => f.new_path === "file1.ts")).toBeDefined();
      expect(result.find((f) => f.new_path === "file2.ts")).toBeDefined();
    });

    it("should aggregate changes correctly across multiple exchanges in a turn", () => {
      // Test that it properly aggregates changes from multiple exchanges between user messages
      const result = getAllChangesBetweenUserMessages(mockExchanges, 2);
      expect(result).toHaveLength(1); // Should aggregate Component.tsx changes

      const componentFile = result[0];
      expect(componentFile.new_path).toBe("Component.tsx");
      // Should have the final version after all modifications in the turn
      expect(componentFile.new_contents).toContain("className='hello'");
    });

    it("should handle out of bounds turnIndex", () => {
      const result = getAllChangesBetweenUserMessages(mockExchanges, 100);
      expect(result).toEqual([]); // Out of bounds should return empty array

      const result2 = getAllChangesBetweenUserMessages(mockExchanges, -1);
      expect(result2).toEqual([]); // Negative index should return empty array
    });
  });
});
