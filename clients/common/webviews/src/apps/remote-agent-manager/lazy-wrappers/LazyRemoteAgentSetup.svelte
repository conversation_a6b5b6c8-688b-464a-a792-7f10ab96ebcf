<script lang="ts">
  import SpinnerAugment from "$common-webviews/src/design-system/components/SpinnerAugment.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
</script>

{#await import("../chat/components/RemoteAgentSetup.svelte")}
  <div class="c-lazy-loading-container">
    <SpinnerAugment size={1} />
    <TextAugment size={1} color="secondary">Loading agent setup...</TextAugment>
  </div>
{:then { default: Component }}
  <Component />
{:catch error}
  <div class="c-lazy-loading-error">
    <TextAugment size={1} color="error"
      >Failed to load agent setup. Please try closing and re-opening the panel.
    </TextAugment>
  </div>
  <div class="c-lazy-loading-error">
    <TextAugment size={1} color="error">{error}</TextAugment>
  </div>
{/await}

<style>
  .c-lazy-loading-container {
    padding: var(--ds-spacing-2);
  }
</style>
