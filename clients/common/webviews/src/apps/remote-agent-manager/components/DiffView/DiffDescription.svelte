<script lang="ts">
  import MarkdownWithColors from "$common-webviews/src/common/components/markdown/MarkdownWithColors.svelte";
  import { type DiffExplanationSubSectionDescription } from "@augment-internal/sidecar-libs/src/remote-agent/remote-agent-types";
  import {
    diffDescriptionCollapseAction,
    getDiffDescriptionId,
  } from "../../utils/diff-description-collapse-action";
  import debounce from "lodash.debounce";
  import { onMount, onDestroy } from "svelte";

  /** The description to display */
  export let description: DiffExplanationSubSectionDescription;
  /** The position to display the description at which will be used as `top: ${position}px` */
  export let position: number;
  /** The ID for this file's changes. The descriptions for this file should all have the same ID */
  export let fileId: string;
  /** The index of this description in the list of descriptions for this file */
  export let index: number;

  let isHovered = false;
  let isCollapsed = false;
  let containerElement: HTMLDivElement;
  /** The available width for the description text which is used to decide how much to truncate */
  let availableWidth = 0;

  const debouncedSetHovered = debounce((value: boolean) => {
    // We need to debounce because if the mouse is at the edge of the collapsed div, the div
    // will momentarily shrink before it expands which causes the mouse to leave the div and
    // the div to flicker.
    isHovered = value;
  }, 100);

  /** Measure the width of the text using a canvas. */
  function measureTextWidth(text: string): number {
    const canvas = document.createElement("canvas");
    const context = canvas.getContext("2d");
    if (!context) return text.length * 8; // fallback estimate
    return context.measureText(text).width;
  }

  /** Check the available width for the description text. */
  function updateAvailableWidth() {
    if (containerElement) {
      const rect = containerElement.getBoundingClientRect();
      availableWidth = rect.width - 128; // rough estimate for padding
    }
  }

  let resizeObserver: ResizeObserver | null = null;

  onMount(() => {
    if (containerElement && typeof ResizeObserver !== "undefined") {
      // When the page resizes, we need to calculate the available width
      resizeObserver = new ResizeObserver(() => {
        updateAvailableWidth();
      });
      resizeObserver.observe(containerElement);
      // Initial measurement on mount
      updateAvailableWidth();
    }
  });

  onDestroy(() => {
    if (resizeObserver) {
      resizeObserver.disconnect();
    }
  });

  $: if (containerElement) {
    updateAvailableWidth();
  }

  $: truncatedText = (() => {
    const firstLine = description.text.split("\n")[0];
    const words = firstLine.split(" ");
    let result = "";

    if (availableWidth <= 0) {
      // If there's no width, estimate using character length
      for (const word of words) {
        const testResult = result + (result ? " " : "") + word;
        if (testResult.length > 30) break;
        result = testResult;
      }
    } else {
      for (const word of words) {
        const testResult = result + (result ? " " : "") + word;
        const textWidth = measureTextWidth(testResult + "...");
        if (textWidth > availableWidth) break;
        result = testResult;
      }
    }

    return result + "...";
  })();
</script>

<div
  bind:this={containerElement}
  class="c-diff-description"
  style="top: {position}px;"
  class:c-diff-description__collapsed={isCollapsed && !isHovered}
  class:c-diff-description__hovered={isHovered}
  data-description-id={getDiffDescriptionId(fileId, index)}
  use:diffDescriptionCollapseAction={{
    path: fileId,
    onCollapseStateChange: (collapsed) => (isCollapsed = collapsed),
  }}
  on:mouseenter={(event) => {
    debouncedSetHovered.cancel();
    isHovered = true;
    event.stopPropagation();
  }}
  on:mouseleave={(event) => {
    debouncedSetHovered(false);
    event.stopPropagation();
  }}
  role="region"
  aria-label="Code diff description"
>
  <div class="c-diff-description__content">
    <MarkdownWithColors markdown={description.text} />
  </div>
  <div class="c-diff-description__truncated-content">
    <MarkdownWithColors markdown={truncatedText} />
    <div class="c-diff-description__expand-hint">hover to expand</div>
  </div>
</div>

<style>
  .c-diff-description {
    position: absolute;
    left: 0;
    right: 0;
    background: var(--collapsible-panel-background);
    padding: var(--ds-spacing-1) var(--ds-spacing-2);
    margin-top: calc(0px - var(--ds-spacing-1));
    border-radius: var(--ds-border-radius-1);
    margin-bottom: var(--ds-spacing-2);
    font-size: 0.8rem;
    display: grid;
    grid-template-rows: 1fr 0fr;
    transition:
      grid-template-rows 0.3s ease,
      background-color 0.3s ease;
    overflow: hidden;
  }

  .c-diff-description.c-diff-description__collapsed {
    grid-template-rows: 0fr 1fr;
  }

  .c-diff-description__hovered {
    background-color: var(--ds-color-neutral-5);
    z-index: 999; /* Ensure this description appears above others */
  }

  .c-diff-description__content {
    min-height: 0;
    overflow: hidden;
  }

  .c-diff-description__truncated-content {
    min-height: 0;
    overflow: hidden;
  }

  .c-diff-description :global(p) {
    line-height: 1.3em;
  }

  .c-diff-description__expand-hint {
    color: var(--ds-text-secondary);
    font-style: italic;
    font-size: 0.7rem;
    margin-left: var(--ds-spacing-2);
    opacity: 0.5;
  }
</style>
