<script lang="ts">
  import Codespan from "$common-webviews/src/common/components/markdown/Codespan.svelte";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import ModalAugment from "$common-webviews/src/design-system/components/ModalAugment.svelte";
  import SpinnerAugment from "$common-webviews/src/design-system/components/SpinnerAugment.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import { getContext } from "svelte";
  import { RemoteAgentDiffOpsModel } from "../../models/ra-diff-ops-model";

  export let showModal = false;
  export let applyAllChanges: () => void;

  const diffOpsModel = getContext<RemoteAgentDiffOpsModel>(RemoteAgentDiffOpsModel.key);

  let stashError: string | undefined = undefined;
  let stashLoading = false;

  async function stashAndApplyChanges() {
    stashLoading = true;
    const stashed = await diffOpsModel.stashUnstagedChanges();
    if (!stashed) {
      stashError =
        "Failed to stash changes. Please manually stash or commit your unstaged changes.";
      stashLoading = false;
      return;
    }
    // We need to wait for the unstaged changes to be stashed from the workspace
    // before applying the changes
    // Otherwise, the apply changes will try to apply with the unstaged changes
    // still in the workspace
    await new Promise((resolve) => setTimeout(resolve, 1500));
    stashError = undefined;
    showModal = false;
    applyAllChanges();
    stashLoading = false;
  }

  function cancelStashAndApplyChanges() {
    showModal = false;
    stashError = undefined;
  }
</script>

<ModalAugment show={showModal} title="Unstaged changes" on:cancel={cancelStashAndApplyChanges}>
  <div class="c-unstaged-changes-modal__body">
    <TextAugment>
      There are unstaged changes in your working directory. Please commit your changes or we will
      run
      <Codespan token={{ type: "codespan", text: "`git stash`", raw: "`git stash`" }} />
      to stash your changes before applying changes from the remote agent.
    </TextAugment>
    {#if stashError}
      <TextAugment>{stashError}</TextAugment>
    {/if}
  </div>
  <div class="c-unstaged-changes-modal__footer" slot="footer">
    <ButtonAugment
      variant="solid"
      color="accent"
      on:click={stashAndApplyChanges}
      disabled={!!stashError || stashLoading}
    >
      {#if stashLoading}
        <div class="c-unstaged-changes-modal__stash-button-loading">
          <SpinnerAugment size={1} />
        </div>
      {/if}
      <span class="c-unstaged-changes-modal__stash-button-text" class:loading={stashLoading}>
        Stash & Apply Locally
      </span>
    </ButtonAugment>
    <ButtonAugment
      variant="solid"
      color="neutral"
      on:click={cancelStashAndApplyChanges}
      disabled={stashLoading}
    >
      Abort
    </ButtonAugment>
  </div>
</ModalAugment>

<style>
  .c-unstaged-changes-modal__body :global(.c-text) {
    display: block;
  }

  .c-unstaged-changes-modal__footer {
    display: flex;
    flex-direction: row;
    gap: var(--ds-spacing-2);
  }

  .c-unstaged-changes-modal__stash-button-text.loading {
    opacity: 0;
    pointer-events: none;
  }

  .c-unstaged-changes-modal__stash-button-loading {
    position: absolute;
  }
</style>
