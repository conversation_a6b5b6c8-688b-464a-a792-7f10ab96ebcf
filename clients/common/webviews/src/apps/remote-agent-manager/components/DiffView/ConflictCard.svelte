<script lang="ts">
  import Filespan from "$common-webviews/src/common/components/Filespan.svelte";
  import LanguageIcon from "$common-webviews/src/common/components/language-icons/LanguageIcon.svelte";
  import CardAugment from "$common-webviews/src/design-system/components/CardAugment.svelte";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import OpenInNewWindow from "$common-webviews/src/design-system/icons/open-in-new-window.svelte";
  import RegularTriangleExclamationIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/triangle-exclamation.svg?component";

  export let files: Set<string>;
  export let hasAppliedAll: boolean;
  export let onOpenFile: ((path: string) => Promise<boolean>) | undefined = undefined;
</script>

<div class="c-conflicts-card">
  <CardAugment includeBackground={false}>
    <div class="c-conflicts-card__header">
      <div class="c-conflicts-card__icon">
        <RegularTriangleExclamationIcon />
      </div>
      <span class="c-conflicts-card__title">
        Conflicts ({files.size})
      </span>
    </div>
    <div class="c-conflicts-card__description">
      {#if hasAppliedAll}
        The following files have merge conflicts that need to be resolved manually.
      {:else}
        The following files will have merge conflicts if applied locally. Conflict markers will be
        added to the file which can be resolved manually after applying.
      {/if}
    </div>
    {#each files as file}
      <div class="c-conflicts-card__file">
        <TextTooltipAugment content={file} nested={true}>
          <LanguageIcon filename={file} />
          <Filespan filepath={file} />
        </TextTooltipAugment>
        <TextTooltipAugment content="Open file">
          <IconButtonAugment
            size={1}
            variant="ghost-block"
            color="neutral"
            on:click={() => onOpenFile?.(file)}
          >
            <OpenInNewWindow />
          </IconButtonAugment>
        </TextTooltipAugment>
      </div>
    {/each}
  </CardAugment>
</div>

<style>
  .c-conflicts-card__header {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-2);
    padding-bottom: var(--ds-spacing-2);
  }

  .c-conflicts-card__icon {
    display: block;
  }

  .c-conflicts-card__title {
    margin: 0;
    font-size: 1rem;
    font-weight: 500;
    width: 100%;
  }

  .c-conflicts-card__description {
    margin: var(--ds-spacing-1) 0;
    color: var(--ds-color-neutral-10);
    font-weight: 400;
  }

  .c-conflicts-card__file {
    display: flex;
    flex-direction: row;
    gap: var(--ds-spacing-5);
    align-items: center;

    --diff-view-conflicts-max-width: calc(100vw - 200px);
  }

  /* Tippy injects some max-width styling that we need to overwrite */
  .c-conflicts-card__file :global(.l-tooltip-contents > div) {
    max-width: var(--diff-view-conflicts-max-width) !important;
  }

  .c-conflicts-card__file :global(.tippy-box) {
    max-width: var(--diff-view-conflicts-max-width) !important;
  }

  .c-conflicts-card__file :global(.l-tooltip-contents > div span) {
    text-align: start;
  }
</style>
