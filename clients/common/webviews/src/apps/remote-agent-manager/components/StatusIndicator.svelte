<script lang="ts">
  import { dsColorAttribute } from "$common-webviews/src/design-system/_libs/component-utils";
  import {
    type RemoteAgentStatus,
    type RemoteAgentWorkspaceStatus,
  } from "@augment-internal/sidecar-libs/src/remote-agent/remote-agent-types";
  import { getStatusBadgeColor, getStatusText } from "../utils";

  export let status: RemoteAgentStatus;
  export let workspaceStatus: RemoteAgentWorkspaceStatus;
  export let isExpanded: boolean = false;
  export let hasUpdates: boolean = false;

  $: statusText = getStatusText(status, workspaceStatus, hasUpdates);
  $: color = getStatusBadgeColor(statusText);
</script>

<div
  class="status-indicator-container"
  class:expanded={isExpanded}
  class:collapsed={!isExpanded}
  role="status"
  aria-label="Agent status: {statusText}"
  title="Status: {statusText}"
  {...dsColorAttribute(color)}
>
  <div class="status-dot"></div>
  <div class="status-label">
    {statusText}
  </div>
</div>

<style>
  .status-indicator-container {
    position: relative;
    display: flex;
    align-items: center;
    border-radius: 10px;
    height: 16px;
    gap: var(--ds-spacing-1);
    background-color: var(--ds-color-a3);
    color: var(--ds-color-11);
    overflow: hidden;
    padding-inline: calc(1.5 * var(--ds-spacing-1));
    transition: width 0.2s;
    width: 17px; /* Just enough for dot + padding */

    &:hover {
      width: auto;
    }
    &.expanded {
      width: auto;
    }
  }

  .status-dot {
    width: 5px;
    height: 5px;
    border-radius: 50%;
    flex-shrink: 0;
    background-color: var(--ds-color-9);
    display: none;
  }

  .collapsed .status-dot {
    display: block;
  }

  .status-label {
    font-size: 10px;
    white-space: nowrap;
    line-height: 16px;
    opacity: 0;
    transition: opacity 0.2s;
  }

  .expanded .status-label,
  .collapsed:hover .status-label {
    opacity: 1;
  }
</style>
