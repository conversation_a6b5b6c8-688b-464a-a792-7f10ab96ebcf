<script lang="ts">
  export let count = 2;
</script>

<div class="c-skeleton-diff">
  {#each Array(count) as _, sectionIndex}
    <div class="c-skeleton-diff__section">
      <div class="c-skeleton-diff__header">
        <div class="c-skeleton-diff__content">
          <div class="c-skeleton-diff__title"></div>
          <div class="c-skeleton-diff__description">
            <div class="c-skeleton-diff__line"></div>
            <div class="c-skeleton-diff__line" style="width: 85%;"></div>
          </div>
        </div>

        {#if sectionIndex === 0}
          <div class="c-skeleton-diff__controls">
            <div class="c-skeleton-diff__button"></div>
          </div>
        {/if}
      </div>

      {#each Array(2) as _}
        <div class="c-skeleton-diff__subsection">
          <div class="c-skeleton-diff__header">
            <div class="c-skeleton-diff__content">
              <div class="c-skeleton-diff__subtitle"></div>
            </div>
            <div class="c-skeleton-diff__icon"></div>
          </div>

          <div class="c-skeleton-diff__changes">
            {#each Array(2) as _}
              <div class="c-skeleton-diff__changes-item">
                <div class="c-skeleton-diff__file-header">
                  <div class="c-skeleton-diff__file-info">
                    <div class="c-skeleton-diff__file-icon"></div>
                    <div class="c-skeleton-diff__file-path"></div>
                  </div>
                  <div class="c-skeleton-diff__file-actions"></div>
                </div>
                <div class="c-skeleton-diff__code-block">
                  <div class="c-skeleton-diff__code-line">
                    <span class="c-skeleton-diff__line-number"></span>
                    <span class="c-skeleton-diff__line-content"></span>
                  </div>
                  <div class="c-skeleton-diff__code-line">
                    <span class="c-skeleton-diff__line-number"></span>
                    <span class="c-skeleton-diff__line-content" style="width: 70%;"></span>
                  </div>
                  <div class="c-skeleton-diff__code-line">
                    <span class="c-skeleton-diff__line-number"></span>
                    <span class="c-skeleton-diff__line-content" style="width: 85%;"></span>
                  </div>
                  <div class="c-skeleton-diff__code-line">
                    <span class="c-skeleton-diff__line-number"></span>
                    <span class="c-skeleton-diff__line-content" style="width: 60%;"></span>
                  </div>
                </div>
              </div>
            {/each}
          </div>
        </div>
      {/each}
    </div>
  {/each}
</div>

<style>
  .c-skeleton-diff {
    animation: fade-in 0.3s ease-in-out;
  }

  .c-skeleton-diff__section {
    margin-bottom: var(--ds-spacing-6);
  }

  .c-skeleton-diff__header {
    display: flex;
    align-items: start;
    gap: var(--ds-spacing-1);
    padding: var(--ds-spacing-1) 0 var(--ds-spacing-2) 0;
    flex: 1;
  }

  .c-skeleton-diff__content {
    flex: 1;
    padding: 0 0 var(--ds-spacing-1) 0;
  }

  .c-skeleton-diff__title {
    height: 20px;
    width: 40%;
    background: var(--ds-color-neutral-3);
    border-radius: var(--ds-border-radius-1);
    margin-bottom: var(--ds-spacing-2);
    animation: pulse 1.5s infinite;
  }

  .c-skeleton-diff__subtitle {
    height: 18px;
    width: 30%;
    background: var(--ds-color-neutral-3);
    border-radius: var(--ds-border-radius-1);
    animation: pulse 1.5s infinite;
  }

  .c-skeleton-diff__description {
    margin-top: var(--ds-spacing-2);
  }

  .c-skeleton-diff__line {
    height: 14px;
    width: 100%;
    background: var(--ds-color-neutral-3);
    border-radius: var(--ds-border-radius-1);
    margin-bottom: var(--ds-spacing-2);
    animation: pulse 1.5s infinite;
  }

  .c-skeleton-diff__controls {
    display: flex;
    justify-content: flex-end;
  }

  .c-skeleton-diff__button {
    height: 28px;
    width: 100px;
    background: var(--ds-color-neutral-3);
    border-radius: var(--ds-border-radius-1);
    animation: pulse 1.5s infinite;
  }

  .c-skeleton-diff__icon {
    height: 20px;
    width: 20px;
    background: var(--ds-color-neutral-3);
    border-radius: 50%;
    animation: pulse 1.5s infinite;
  }

  .c-skeleton-diff__subsection {
    margin-top: var(--ds-spacing-4);
    margin-bottom: var(--ds-spacing-4);
  }

  .c-skeleton-diff__changes {
    margin-top: var(--ds-spacing-2);
  }

  .c-skeleton-diff__changes-item {
    margin-bottom: var(--ds-spacing-4);
    border: 1px solid var(--ds-color-neutral-3);
    border-radius: var(--ds-border-radius-1);
    overflow: hidden;
  }

  .c-skeleton-diff__file-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--ds-spacing-2);
    background: var(--ds-color-neutral-2);
    border-bottom: 1px solid var(--ds-color-neutral-3);
  }

  .c-skeleton-diff__file-info {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-2);
  }

  .c-skeleton-diff__file-icon {
    height: 16px;
    width: 16px;
    background: var(--ds-color-neutral-3);
    border-radius: var(--ds-border-radius-1);
    animation: pulse 1.5s infinite;
  }

  .c-skeleton-diff__file-path {
    height: 16px;
    width: 60%;
    background: var(--ds-color-neutral-3);
    border-radius: var(--ds-border-radius-1);
    animation: pulse 1.5s infinite;
  }

  .c-skeleton-diff__file-actions {
    height: 24px;
    width: 80px;
    background: var(--ds-color-neutral-3);
    border-radius: var(--ds-border-radius-1);
    animation: pulse 1.5s infinite;
  }

  .c-skeleton-diff__code-block {
    padding: var(--ds-spacing-2);
    background: var(--ds-color-neutral-1);
  }

  .c-skeleton-diff__code-line {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-2);
    margin-bottom: var(--ds-spacing-1);
  }

  .c-skeleton-diff__line-number {
    height: 14px;
    width: 24px;
    background: var(--ds-color-neutral-3);
    border-radius: var(--ds-border-radius-1);
    animation: pulse 1.5s infinite;
  }

  .c-skeleton-diff__line-content {
    height: 14px;
    width: 90%;
    background: var(--ds-color-neutral-3);
    border-radius: var(--ds-border-radius-1);
    animation: pulse 1.5s infinite;
  }

  @keyframes pulse {
    0% {
      opacity: 0.6;
    }
    50% {
      opacity: 0.3;
    }
    100% {
      opacity: 0.6;
    }
  }

  @keyframes fade-in {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }
</style>
