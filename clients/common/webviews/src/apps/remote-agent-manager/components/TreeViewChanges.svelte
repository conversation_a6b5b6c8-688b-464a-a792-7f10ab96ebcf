<script lang="ts">
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import {
    type ChangedFile,
    FileChangeType,
  } from "@augment-internal/sidecar-libs/src/remote-agent/remote-agent-types";
  import TreeViewNode from "./TreeViewNode.svelte";
  // Svelte components are automatically exported as default exports

  export let changedFiles: ChangedFile[] = [];
  export let isLoading: boolean = false;

  // Create a nested tree structure
  $: fileTree = buildFileTreeFromChangedFiles(changedFiles);

  interface TreeNode {
    name: string;
    path: string;
    isFile: boolean;
    children: Map<string, TreeNode>;
    isExpanded: boolean;
    displayName?: string; // For showing the full path in collapsed folders
  }

  function buildFileTreeFromChangedFiles(changedFiles: ChangedFile[]) {
    const root: TreeNode = {
      name: "",
      path: "",
      isFile: false,
      children: new Map(),
      isExpanded: true,
    };

    // Process each changed file
    changedFiles.forEach((file) => {
      // For added or modified files, use new_path
      // For deleted files, use old_path
      const path = file.change_type === FileChangeType.deleted ? file.old_path : file.new_path;

      if (path) {
        addPathToTree(root, path);
      }
    });

    // Collapse folders that don't contain other folders
    collapseFolders(root);

    return root;
  }

  function addPathToTree(root: TreeNode, filePath: string) {
    const parts = filePath.split("/");
    let currentNode = root;

    // Process all parts of the path
    for (let i = 0; i < parts.length; i++) {
      const part = parts[i];
      const isFile = i === parts.length - 1;
      const currentPath = parts.slice(0, i + 1).join("/");

      if (!currentNode.children.has(part)) {
        currentNode.children.set(part, {
          name: part,
          path: currentPath,
          isFile,
          children: new Map(),
          isExpanded: true,
        });
      }

      currentNode = currentNode.children.get(part)!;
    }
  }

  // Function to flatten folders that only contain a single subfolder
  function collapseFolders(node: TreeNode) {
    if (node.isFile) return;

    // For the root node, process each child separately to preserve at least one level
    if (node.path === "") {
      const rootChildren = Array.from(node.children.values()).filter((child) => !child.isFile);
      for (const child of rootChildren) {
        flattenSingleFolderPath(child, true); // Pass true for isTopLevel
      }
      return;
    }

    // For non-root nodes, start the flattening process
    flattenSingleFolderPath(node);
  }

  // Function to flatten a folder path if it only contains a single subfolder
  function flattenSingleFolderPath(node: TreeNode, isTopLevel: boolean = false) {
    if (node.isFile) return;

    // For top-level folders, we need to collect the full path before flattening
    let fullPath = "";
    if (isTopLevel) {
      fullPath = collectFullPath(node);
    }

    // Process all children recursively first
    const folderChildren = Array.from(node.children.values()).filter((child) => !child.isFile);
    for (const child of folderChildren) {
      flattenSingleFolderPath(child);
    }

    // Get all children that are folders and files after processing children
    const updatedFolderChildren = Array.from(node.children.values()).filter(
      (child) => !child.isFile,
    );
    const fileChildren = Array.from(node.children.values()).filter((child) => child.isFile);

    // Check if this folder only contains a single subfolder and no files
    if (updatedFolderChildren.length === 1 && fileChildren.length === 0) {
      const childFolder = updatedFolderChildren[0];
      const childName = childFolder.name;

      // For top-level folders (direct children of root), we want to preserve the folder
      // but set its display name to show the full path
      if (isTopLevel) {
        // Set the display name for this node to show the full path
        node.displayName = fullPath || `${node.name}/${childName}`;

        // Move all grandchildren up to this node with combined names
        for (const [grandChildName, grandChild] of childFolder.children.entries()) {
          // Create a new combined name for the key
          const newName = `${grandChildName}`;

          // Move the grandchild up to this node
          node.children.set(newName, grandChild);
        }

        // Remove the child folder since its contents have been moved up
        node.children.delete(childName);
      } else {
        // For non-top-level folders, set the display name for the child folder
        if (node.displayName) {
          childFolder.displayName = `${node.displayName}/${childName}`;
        } else {
          childFolder.displayName = `${node.name}/${childName}`;
        }

        // Move all grandchildren up to this node with combined names
        for (const [grandChildName, grandChild] of childFolder.children.entries()) {
          // Create a new combined name for the key
          const newName = `${childName}/${grandChildName}`;

          // Move the grandchild up to this node
          node.children.set(newName, grandChild);
        }

        // Remove the child folder since its contents have been moved up
        node.children.delete(childName);
      }
    }
  }

  // Helper function to collect the full path by traversing the tree
  function collectFullPath(node: TreeNode): string {
    // For top-level folders, we want to collect the full path of nested folders
    // that only contain a single subfolder

    // Start with the node's path which already includes the node's name
    let fullPath = node.path.split("/");
    let currentNode = node;

    // Keep traversing down as long as there's only one folder child and no files
    while (true) {
      // Get all children that are folders and files
      const folderChildren = Array.from(currentNode.children.values()).filter(
        (child) => !child.isFile,
      );
      const fileChildren = Array.from(currentNode.children.values()).filter(
        (child) => child.isFile,
      );

      // If there's only one folder child and no files, continue down the path
      if (folderChildren.length === 1 && fileChildren.length === 0) {
        currentNode = folderChildren[0];
        fullPath.push(currentNode.name);
      } else {
        // Otherwise, we've reached the end of the collapsible path
        break;
      }
    }

    return fullPath.join("/");
  }
</script>

<div class="tree-view">
  <div class="tree-view__content" role="tree" aria-label="Changed Files">
    {#if isLoading}
      <div class="tree-view__loading">
        <div class="tree-view__skeleton">
          <div class="tree-view__skeleton-item"></div>
          <div class="tree-view__skeleton-item" style="margin-left: 12px;"></div>
          <div class="tree-view__skeleton-item" style="margin-left: 12px;"></div>
          <div class="tree-view__skeleton-item"></div>
          <div class="tree-view__skeleton-item" style="margin-left: 12px;"></div>
          <div class="tree-view__skeleton-item" style="width: 70%;"></div>
        </div>
      </div>
    {:else if fileTree.children.size === 0}
      <div class="tree-view__empty">
        <TextAugment size={1} color="neutral">No changed files</TextAugment>
      </div>
    {:else}
      {#each Array.from(fileTree.children.values()).sort( (a, b) => (a.isFile === b.isFile ? a.name.localeCompare(b.name) : a.isFile ? 1 : -1), ) as node}
        <TreeViewNode {node} indentLevel={0} />
      {/each}
    {/if}
  </div>
</div>

<style>
  .tree-view {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .tree-view__content {
    flex: 1;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: var(--ds-color-neutral-6) transparent;
  }

  .tree-view__content::-webkit-scrollbar {
    width: 6px;
  }

  .tree-view__content::-webkit-scrollbar-track {
    background: transparent;
  }

  .tree-view__content::-webkit-scrollbar-thumb {
    background-color: var(--ds-color-neutral-6);
    border-radius: 3px;
  }

  .tree-view__loading,
  .tree-view__empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: var(--ds-spacing-2);
    padding: var(--ds-spacing-4) 0;
    color: var(--ds-color-neutral-9);
  }

  .tree-view__skeleton {
    width: 100%;
    padding: var(--ds-spacing-2);
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-2);
  }

  .tree-view__skeleton-item {
    height: 16px;
    width: 90%;
    background: var(--ds-color-neutral-3);
    border-radius: var(--ds-border-radius-1);
    animation: pulse 1.5s infinite;
  }

  @keyframes pulse {
    0% {
      opacity: 0.6;
    }
    50% {
      opacity: 0.3;
    }
    100% {
      opacity: 0.6;
    }
  }
</style>
