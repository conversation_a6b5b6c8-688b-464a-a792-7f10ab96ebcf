<script lang="ts">
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import CalloutAugment, {
    type CalloutColor,
  } from "$common-webviews/src/design-system/components/CalloutAugment.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import BranchIcon from "$common-webviews/src/design-system/icons/branch.svelte";
  import Reload from "$common-webviews/src/design-system/icons/reload.svelte";
  import MagnifyingGlass from "$common-webviews/src/design-system/icons/magnifying-glass.svelte";
  import debounce from "lodash.debounce";
  import {
    type GithubRepo,
    type CommitRef,
    type GithubBranch,
  } from "@augment-internal/sidecar-libs/src/remote-agent/remote-agent-types";
  import { parseRemoteUrl, searchBranches, searchRepos } from "../utils/git";
  import { createEventDispatcher, getContext, onMount } from "svelte";
  import { RemoteAgentSetupWindowAction } from "@augment-internal/sidecar-libs/src/metrics/types";
  import { GitReferenceModel } from "../models/git-reference-model";
  import Github from "$common-webviews/src/design-system/icons/github.svelte";
  import RegularArrowRotateRightIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/arrow-rotate-right.svg?component";
  import RegularChevronsDownIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/chevrons-down.svg?component";
  import GithubAuthCard from "../chat/components/GithubAuthCard.svelte";
  import SearchableDropdown from "./SearchableDropdown.svelte";
  import { RemoteAgentsModel } from "../models/remote-agents-model";
  import SpinnerAugment from "$common-webviews/src/design-system/components/SpinnerAugment.svelte";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import { TooltipTriggerOn } from "$common-webviews/src/design-system/_primitives/TooltipAugment/types";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import { RepoBranchSelectorModel } from "../models/repo-branch-selector-model";
  import { derived, type Readable, writable } from "svelte/store";

  const gitRefModel = getContext<GitReferenceModel>(GitReferenceModel.key);
  const dispatch = createEventDispatcher<{
    commitRefChange: {
      commitRef: CommitRef;
      selectedBranch: GithubBranch;
    };
  }>();
  const remoteAgentsModel = getContext<RemoteAgentsModel>(RemoteAgentsModel.key);

  const repoBranchModel = new RepoBranchSelectorModel(gitRefModel);

  const allRepos = repoBranchModel.allRepos;
  const allBranches = repoBranchModel.allBranches;
  const selectedRepo = repoBranchModel.selectedRepo;
  const selectedBranch = repoBranchModel.selectedBranch;

  $: repoBranchModel.lastUsedRepoUrl = lastUsedRepoUrl ?? undefined;
  $: repoBranchModel.lastUsedBranchName = lastUsedBranchName ?? undefined;

  export let errorMessage = "";
  export let isLoading = false;
  export let lastUsedBranchName: string | null = null;
  export let lastUsedRepoUrl: string | null = null;

  $: hasError = errorMessage !== "";
  let calloutColor: CalloutColor = "warning";

  /** True if the branches are currently loading */
  let branchLoading = false;
  /** True if the repos are currently loading */
  let repoLoading = false;
  /** True if there are more pages of branches to load */
  let branchHasMorePages = false;
  /** True if there are more pages of repos to load */
  let repoHasMorePages = false;
  /** True if Repo is selected and branches are loading and no branch is selected */
  $: shouldShowBranchLoading = $selectedRepo && branchLoading && !$selectedBranch;
  /** True if repos are loading and no repo is selected */
  $: shouldShowRepoLoading = repoLoading && !$selectedRepo;

  /** True if we are currently displaying a repo using a GitHub URL */
  let displayingGithubRepo = false;

  /** Set of repo names that have duplicates */
  const duplicateRepoNames = new Set<string>();
  /** The title to display for the repo selector */
  let repoTitle = "Choose repository...";
  $: {
    if ($selectedRepo) {
      const { owner, name } = $selectedRepo;
      repoTitle = duplicateRepoNames.has(name) ? `${owner}/${name}` : name;
    } else {
      repoTitle = "Choose repository...";
    }
  }

  /** True if the user is searching for a repo */
  let searchRepoActive = false;
  /**
   * The repos that have been searched for and added to the dropdown
   * Key: {owner}/{name}, Value: GithubRepo
   */
  const searchedRepoResults: Map<string, GithubRepo> = new Map();

  /** True if the user is searching for a branch */
  let searchBranchActive = false;

  let requestCloseRepo: () => void = () => {};
  let requestCloseBranch: () => void = () => {};

  const repoSearchValue = writable<string>("");
  const branchSearchValue = writable<string>("");

  function _handleRepoSearchChange(value: string) {
    repoSearchValue.set(value);
    searchRepoActive = true;
  }

  const handleRepoSearchChange = debounce(_handleRepoSearchChange, 300, {
    leading: false,
    trailing: true,
  });

  function _handleBranchSearchChange(value: string) {
    branchSearchValue.set(value);
    searchBranchActive = true;
  }
  const handleBranchSearchChange = debounce(_handleBranchSearchChange, 300, {
    leading: false,
    trailing: true,
  });

  const errors = {
    noRemoteBranches:
      "No remote branches found. Remote agents require remote branches to work properly. Please push your current branch to remote with 'git push -u origin <branch>'.",
    failedToFetchBranches: "Failed to fetch branches. Please try again.",
    failedToParseRemoteUrl:
      "Failed to parse remote URL in your local git repo. Please check your remote URL and try again.",
    failedToFetchFromRemote: "Failed to fetch from remote. Please try again.",
  };

  onMount(async () => {
    // load all repos and branches
    await fetchReposAndBranches();
  });

  /** The repos to display in the dropdown. This is a subset of allRepos based on the search input */
  const displayedRepos: Readable<GithubRepo[]> = derived(
    [allRepos, repoSearchValue],
    ([$allRepos, $repoSearchValue], set) => {
      hasError = false;
      displayingGithubRepo = false;
      try {
        if (searchRepoActive) {
          const parsedRepo = parseRemoteUrl($repoSearchValue);
          if (parsedRepo) {
            gitRefModel
              .getGithubRepo(parsedRepo)
              .then(({ repo, error }) => {
                if (error) {
                  throw new Error(error);
                }
                const repoKey = `${repo.owner}/${repo.name}`;
                if (!searchedRepoResults.has(repoKey)) {
                  searchedRepoResults.set(repoKey, repo);
                  repoBranchModel.addRepo(repo);
                }
                displayingGithubRepo = true;
                set([repo]);
              })
              .catch((error) => {
                console.error("Failed to get GitHub repo from URL", error);
                set([]);
              });
          }
          set(searchRepos($allRepos, $repoSearchValue));
        } else {
          set($allRepos);
        }
      } catch (error) {
        console.error("Error fetching repos:", error);
        setError(errors.failedToFetchFromRemote);
        set([]);
      }
    },
  );

  /** The branches to display in the dropdown. This is a subset of allBranches based on the search input */
  const displayedBranches = derived(
    [allBranches, selectedRepo, branchSearchValue],
    ([$allBranches, $selectedRepo, $branchSearchValue]) => {
      if (searchBranchActive && $branchSearchValue.trim() !== "") {
        return searchBranches($allBranches, $branchSearchValue);
      } else {
        if ($selectedRepo?.default_branch) {
          const defaultBranch = $allBranches.find(
            (branch) => branch.name === $selectedRepo?.default_branch,
          );
          return [
            defaultBranch
              ? defaultBranch
              : {
                  name: $selectedRepo.default_branch,
                  commit: { sha: "", url: "" },
                  protected: false,
                },
            ...$allBranches.filter((branch) => branch.name !== $selectedRepo?.default_branch),
          ];
        } else {
          return $allBranches;
        }
      }
    },
  );

  /** Check if there are any duplicate repo names and update the duplicateRepoNames set */
  function checkForDuplicateRepoNames() {
    duplicateRepoNames.clear();
    const repoNames = new Set<string>();
    $allRepos.forEach((repo) => {
      if (repoNames.has(repo.name)) {
        duplicateRepoNames.add(repo.name);
      } else {
        repoNames.add(repo.name);
      }
    });
  }

  /** Fetch repos available to the user and pre-select one if possible */
  async function fetchAndSelectRepo() {
    repoLoading = true;
    repoHasMorePages = await repoBranchModel.updateRepos(true, updateCommitRef);
    checkForDuplicateRepoNames();
    await repoBranchModel.preselectRepo();
    repoLoading = false;
  }

  /**
   * Fetch all branches from the first page for the selected repo. The
   * `branchLoadedPageCount` will increment as this function loads more pages.
   * This will fetch LOAD_PAGES at a time.
   */
  async function fetchAllBranches() {
    if (!$selectedRepo) {
      return;
    }
    branchLoading = true;
    branchHasMorePages = await repoBranchModel.updateBranches(true, updateCommitRef);
    branchLoading = false;
  }

  function fetchAllBranchesBackground() {
    if (!selectedRepo) {
      return;
    }
    fetchAllBranches()
      .then(() => {
        isLoading = false;
        if (!hasError) {
          updateCommitRef();
        }
      })
      .catch((error) => {
        console.error("Error fetching all branches:", error);
        setError(
          `Failed to fetch branches: ${error instanceof Error ? error.message : String(error)}`,
        );
      });
  }

  let isGithubAuthenticated = true;
  const checkIsGithubAuthenticated = async () => {
    try {
      isGithubAuthenticated = await gitRefModel.isGithubAuthenticated();
      if (!isGithubAuthenticated) {
        setError("Please authenticate with GitHub to use this feature.", "info");
      }
    } catch (error) {
      console.error("Failed to check GitHub authentication status:", error);
      setError("Please authenticate with GitHub to use this feature.");
      isGithubAuthenticated = false;
    }
  };

  /**
   * Get the initial data for the selectors
   *
   * Fetched for the user's local workspace repo, all repos the user has access to,
   * and the branches for the repo if one was preselected during this process.
   */
  async function getInitialData() {
    isLoading = true;
    clearError();

    try {
      // Check if the user is authenticated
      await checkIsGithubAuthenticated();
      if (!isGithubAuthenticated) {
        isLoading = false;
        return;
      }

      repoLoading = true;
      branchLoading = true;
      // If we the user has a lot of repos, it is possible that the user's current repo
      // may not be in the first fetch response so in order to speed up time to interactive
      // we'll fetch the user's local workspace repo in parallel.
      let hasPreselectedLocal = false;
      repoBranchModel
        .preselectLocalRepo()
        .then((hasPreselected) => {
          hasPreselectedLocal = hasPreselected;
          updateCommitRef();
          return hasPreselected;
        })
        .then((hasPreselected) => {
          if (hasPreselected) {
            fetchAllBranchesBackground();
          }
        })
        .catch((error) => {
          console.error("Failed to preselect local repo while fetching all repos: ", error);
        });
      await fetchAndSelectRepo();
      if (!hasError && !hasPreselectedLocal) {
        // Fetch all branches if we haven't preselected the local repo yet, because that
        // parallel path will also try to fetch all branches. That path will take precedence
        // because it should be faster
        repoLoading = false;
        // Fetch all branches in the background because this can take a long time for repos
        // with a lot of branches.
        fetchAllBranchesBackground();
      }

      // Create and dispatch initial commit ref if no errors
      if (!hasError) {
        updateCommitRef();
      }
    } catch (error) {
      console.error("Error fetching git data:", error);
      setError(errors.failedToFetchBranches);
    } finally {
      isLoading = false;
    }
  }

  async function fetchReposAndBranches() {
    isLoading = true;
    try {
      // Fetch repos and branches
      await getInitialData();
    } catch (error) {
      console.error("Error fetching and syncing branches:", error);
      setError("Failed to fetch repos and branches. Please try again.");
    } finally {
      isLoading = false;
    }
  }

  function setError(message: string, color: CalloutColor = "warning") {
    console.error("Error:", message);
    hasError = true;
    errorMessage = message;
    calloutColor = color;
  }

  function clearError() {
    hasError = false;
    errorMessage = "";
    calloutColor = "warning";
  }

  async function selectBranch(option: GithubBranch) {
    repoBranchModel.selectedBranch = option;
    // Reset searching state
    searchBranchActive = false;
    setBranchInputValue($selectedBranch?.name ?? "");
    // Update the creation metrics
    const oldMetrics = remoteAgentsModel.creationMetrics;
    remoteAgentsModel.setCreationMetrics({
      changedRepo: oldMetrics?.changedRepo ?? false,
      changedBranch: true,
    });
    // Update the commit ref
    updateCommitRef();
    requestCloseBranch();

    // Log branch selection event
    try {
      await remoteAgentsModel.reportRemoteAgentSetupWindowEvent(
        RemoteAgentSetupWindowAction.selectBranch,
      );
    } catch (error) {
      console.error("Failed to report branch selection event:", error);
    }
  }

  async function selectRepo(option: GithubRepo) {
    branchLoading = true; // preemptively set this to true so that no-branches error doesn't show up
    repoBranchModel.selectedRepo = option;
    repoBranchModel.selectedBranch = undefined;
    // reset searching state
    searchRepoActive = false;
    setRepoInputValue("");
    requestCloseRepo();
    // fetch all branches in the background
    fetchAllBranchesBackground();
    // Update the creation metrics
    const oldMetrics = remoteAgentsModel.creationMetrics;
    remoteAgentsModel.setCreationMetrics({
      changedRepo: true,
      changedBranch: oldMetrics?.changedBranch ?? false,
    });

    // Log repo selection event
    try {
      await remoteAgentsModel.reportRemoteAgentSetupWindowEvent(
        RemoteAgentSetupWindowAction.selectRepo,
      );
    } catch (error) {
      console.error("Failed to report repo selection event:", error);
    }
  }

  function onDropdownOpenChange(open: boolean, type?: "repo" | "branch") {
    if (!open) {
      if (type === "repo") {
        searchRepoActive = false;
      } else if (type === "branch") {
        searchBranchActive = false;
      } else {
        searchRepoActive = false;
        searchBranchActive = false;
      }
    }
  }

  function updateCommitRef() {
    if (!$selectedRepo?.html_url || !$selectedBranch) {
      return;
    }

    const commitRef: CommitRef = {
      /* eslint-disable @typescript-eslint/naming-convention */
      github_commit_ref: {
        repository_url: $selectedRepo.html_url,
        git_ref: $selectedBranch.name,
      },
      /* eslint-enable @typescript-eslint/naming-convention */
    };

    // Pass additional information about the selection
    dispatch("commitRefChange", {
      commitRef,
      selectedBranch: $selectedBranch,
    });
  }

  $: if (searchBranchActive) {
    // When the user begins searching, reset the input box
    setBranchInputValue("");
  } else {
    // When the user stops searching, show the currently selected branch
    setBranchInputValue($selectedBranch?.name ?? "");
  }

  // helper function to stop unnecessary reactivity
  const setBranchInputValue = (value: string) => {
    branchSearchValue.set(value);
  };

  const setRepoInputValue = (value: string) => {
    repoSearchValue.set(value);
  };

  $: isBranchSelectorDisabled = !$selectedRepo || (!searchBranchActive && !$allBranches.length);

  function handleBranchOpenChange(open: boolean) {
    if (open && isBranchSelectorDisabled) {
      requestCloseBranch();
      return;
    }
    onDropdownOpenChange(open, "branch");
  }

  function handleRepoOpenChange(open: boolean) {
    if (open && !$allRepos.length) {
      requestCloseRepo();
      return;
    }
    onDropdownOpenChange(open, "repo");
  }

  /**
   * Refresh the branches list
   *
   * This is used when the branch list is stale and the user may want to refresh it.
   */
  function refreshBranchesList() {
    // Load the first page again
    fetchAllBranchesBackground();
  }
</script>

<div class="c-commit-ref-selector">
  <div class="c-commit-ref-selector__content">
    <!-- Show selectors only when there's no error -->
    {#if !hasError}
      <div class="c-commit-ref-selector__selectors-container">
        <!-- Repository Selector -->
        <div class="c-commit-ref-selector__selector">
          <SearchableDropdown
            title={repoTitle}
            placeholder="Search repositories or paste a GitHub URL..."
            itemKeyFn={(repo) => `${repo.owner}-${repo.name}`}
            isLoading={shouldShowRepoLoading}
            disabled={!$allRepos.length}
            bind:searchValue={$repoSearchValue}
            items={$displayedRepos}
            selectedItem={$selectedRepo}
            itemLabelFn={(repo) => `${repo.owner}/${repo.name}`}
            noItemsLabel="No repositories found"
            loadingLabel="Loading repositories..."
            on:openChange={(e) => handleRepoOpenChange(e.detail)}
            on:search={(e) => handleRepoSearchChange(e.detail)}
            on:select={(e) => selectRepo(e.detail)}
          >
            <Github slot="icon" />
            <MagnifyingGlass slot="searchIcon" />
            <svelte:fragment slot="footer">
              {#if repoHasMorePages && !repoLoading && !displayingGithubRepo}
                <TextTooltipAugment
                  content={`${$allRepos.length} repos loaded`}
                  triggerOn={[TooltipTriggerOn.Hover]}
                  nested={false}
                >
                  <button
                    type="button"
                    class="c-commit-ref-selector__item c-commit-ref-selector__item--loading"
                    on:click={async () => {
                      repoLoading = true;
                      repoHasMorePages = await repoBranchModel.loadMoreRepos();
                      repoLoading = false;
                    }}
                  >
                    <div class="c-commit-ref-selector__item-icon">
                      <RegularChevronsDownIcon />
                    </div>
                    <div class="c-commit-ref-selector__item-content">
                      <TextAugment
                        size={2}
                        color="neutral"
                        class="c-commit-ref-selector__item-name"
                      >
                        Load more repos
                      </TextAugment>
                    </div>
                  </button>
                </TextTooltipAugment>
              {:else if repoLoading}
                <div
                  class="c-commit-ref-selector__item c-commit-ref-selector__item--loading c-commit-ref-selector__item--disabled"
                >
                  <div class="c-commit-ref-selector__item-icon">
                    <SpinnerAugment size={1} />
                  </div>
                  <div class="c-commit-ref-selector__item-content">
                    <TextAugment size={2} color="neutral" class="c-commit-ref-selector__item-name">
                      Loading repos...
                    </TextAugment>
                  </div>
                </div>
              {/if}
            </svelte:fragment>
          </SearchableDropdown>
        </div>

        <!-- Branch Selector -->
        <div class="c-commit-ref-selector__selector">
          <SearchableDropdown
            title={$selectedBranch?.name || "Choose branch..."}
            itemKeyFn={(branch, index) => `${branch.name}-${branch.commit.sha}=${index}`}
            placeholder="Search branches..."
            isLoading={shouldShowBranchLoading}
            disabled={isBranchSelectorDisabled}
            bind:searchValue={$branchSearchValue}
            items={$displayedBranches}
            selectedItem={$selectedBranch}
            itemLabelFn={(branch) => branch.name.replace("origin/", "")}
            noItemsLabel={branchLoading ? "" : "No branches found"}
            loadingLabel="Loading branches..."
            on:openChange={(e) => handleBranchOpenChange(e.detail)}
            on:search={(e) => handleBranchSearchChange(e.detail)}
            on:select={(e) => selectBranch(e.detail)}
          >
            <BranchIcon slot="icon" />
            <MagnifyingGlass slot="searchIcon" />
            <div slot="inputButton" class="c-commit-ref-selector__refresh-button">
              <TextTooltipAugment
                content="Refresh branches"
                triggerOn={[TooltipTriggerOn.Hover]}
                nested={false}
              >
                <IconButtonAugment
                  variant="ghost"
                  color="neutral"
                  size={1}
                  on:click={refreshBranchesList}
                >
                  <RegularArrowRotateRightIcon />
                </IconButtonAugment>
              </TextTooltipAugment>
            </div>
            <svelte:fragment slot="footer">
              {#if branchHasMorePages && !branchLoading}
                <TextTooltipAugment
                  content={`${$allBranches.length} branches loaded`}
                  triggerOn={[TooltipTriggerOn.Hover]}
                  nested={false}
                >
                  <button
                    type="button"
                    class="c-commit-ref-selector__item c-commit-ref-selector__item--loading"
                    on:click={async () => {
                      branchLoading = true;
                      branchHasMorePages = await repoBranchModel.loadMoreBranches();
                      branchLoading = false;
                    }}
                  >
                    <div class="c-commit-ref-selector__item-icon">
                      <RegularChevronsDownIcon />
                    </div>
                    <div class="c-commit-ref-selector__item-content">
                      <TextAugment
                        size={2}
                        color="neutral"
                        class="c-commit-ref-selector__item-name"
                      >
                        Load more branches
                      </TextAugment>
                    </div>
                  </button>
                </TextTooltipAugment>
              {:else if branchLoading}
                <div
                  class="c-commit-ref-selector__item c-commit-ref-selector__item--loading c-commit-ref-selector__item--disabled"
                >
                  <div class="c-commit-ref-selector__item-icon">
                    <SpinnerAugment size={1} />
                  </div>
                  <div class="c-commit-ref-selector__item-content">
                    <TextAugment size={2} color="neutral" class="c-commit-ref-selector__item-name">
                      Loading branches...
                    </TextAugment>
                  </div>
                </div>
              {/if}
            </svelte:fragment>
          </SearchableDropdown>
        </div>
      </div>
    {:else if hasError}
      <!-- Show error callout for critical errors -->
      <div class="c-commit-ref-selector__error">
        <CalloutAugment color={calloutColor} variant="soft" size={2}>
          <div class="c-commit-ref-selector__error-content">
            <div class="c-commit-ref-selector__error-message">
              {errorMessage}
            </div>
            {#if !isGithubAuthenticated}
              <GithubAuthCard
                on:authStateChange={async (event) => {
                  const { isAuthenticated } = event.detail;
                  if (isAuthenticated !== isGithubAuthenticated) {
                    isGithubAuthenticated = isAuthenticated;
                    if (isAuthenticated) {
                      await fetchReposAndBranches();
                    }
                  }
                }}
              />
            {:else}
              <ButtonAugment
                variant="ghost"
                color="warning"
                size={1}
                on:click={fetchReposAndBranches}
                loading={isLoading}
                class="c-commit-ref-selector__fetch-button"
              >
                <span slot="iconLeft"><Reload /></span>
                Reload available repos and branches
              </ButtonAugment>
            {/if}
          </div>
        </CalloutAugment>
      </div>
    {/if}
  </div>
</div>

<style>
  .c-commit-ref-selector__selectors-container {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-2);
    width: 100%;
  }

  .c-commit-ref-selector__selector {
    width: 100%;
  }

  .c-commit-ref-selector__refresh-button :global(svg) {
    --icon-size: 12px;
  }

  .c-commit-ref-selector {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-3);
    width: 100%;
  }

  .c-commit-ref-selector__content {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-3);
    width: 100%;
    user-select: none;
  }

  .c-commit-ref-selector__content :global(.l-dropdown-menu-augment__contents--size-2) {
    padding-bottom: 0;
  }

  .c-commit-ref-selector__content :global(.c-dropdown-menu-augment__separator) {
    margin: var(--ds-spacing-2) 0;
  }

  .c-commit-ref-selector__error {
    max-width: 600px;
    margin-top: var(--ds-spacing-3);
  }
  .c-commit-ref-selector__error :global(> .c-callout) {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  :global(.c-commit-ref-selector__workspace-option .c-dropdown-menu-augment__item) {
    color: var(--ds-color-accent-11);
    font-weight: 500;
    width: 100%;
  }
  :global(.c-commit-ref-selector__workspace-tooltip) {
    display: block;
    width: 100%;
  }

  .c-commit-ref-selector__selectors-container {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    flex-wrap: wrap;
    gap: var(--ds-spacing-1);
    width: 100%;
  }

  .c-commit-ref-selector__selector {
    display: flex;
    width: 100%;
    align-items: center;
    flex-wrap: wrap;
    gap: var(--ds-spacing-1);
    max-width: 100%;
    flex: 1;
  }

  .c-commit-ref-selector__selector :global(.c-card-button),
  .c-commit-ref-selector__selector :global(.c-searchable-dropdown) {
    max-width: 100%;
    width: 100%;
  }

  .c-commit-ref-selector :global(.l-tooltip-contents) {
    margin-left: var(--remote-agent-setup-horizontal-padding);
    width: 100%;
  }

  .c-commit-ref-selector :global(.tippy-box) {
    max-width: unset !important; /** Tippy sets a max-width of 350px by default on the element */
    width: calc(
      100vw - var(--remote-agent-setup-horizontal-padding) -
        var(--remote-agent-setup-horizontal-padding) - var(--ds-spacing-4) - var(--ds-spacing-1)
    );
    min-width: 250px;
  }
  @media (max-width: 400px) {
    .c-commit-ref-selector :global(.l-tooltip-contents) {
      margin-left: 0;
    }
  }

  .c-commit-ref-selector :global(.l-dropdown-menu-augment__contents) {
    max-height: 300px;
    min-height: 40px;
    overflow-y: auto;
  }

  .c-commit-ref-selector__item {
    cursor: pointer;
    width: 100%;
    border: unset;
    background-color: unset;
    color: unset;
    font-size: inherit;
    font-weight: inherit;
    line-height: inherit;
    padding: 0;
    outline: none;
    border-radius: var(--ds-radius-1);
  }

  /* prevent nested scrollbars */
  .c-commit-ref-selector__selector :global(.c-searchable-dropdown__content) {
    max-height: none;
  }

  .c-commit-ref-selector__item {
    padding: var(--ds-spacing-2) var(--ds-spacing-2);

    &.c-commit-ref-selector__item--disabled {
      cursor: default;
    }

    &.c-commit-ref-selector__item--loading {
      display: flex;
      justify-content: start;
      align-items: center;
      gap: var(--ds-spacing-1);
    }
  }

  .c-commit-ref-selector__item-icon :global(svg) {
    width: 15px;
    height: 15px;
    display: block;
    color: var(--ds-color-neutral-11);
  }

  .c-commit-ref-selector__item-content {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    white-space: nowrap;
  }

  .c-commit-ref-selector__item-content :global(.c-text) {
    display: block;
    color: var(--ds-color-neutral-11);
    text-align: left;
  }

  .c-commit-ref-selector__item-content :global(.c-text.c-commit-ref-selector__item-name) {
    overflow: hidden;
    text-overflow: ellipsis;
    flex: 1;
  }

  :not(.c-commit-ref-selector__item--disabled) {
    &.c-commit-ref-selector__item:focus,
    &.c-commit-ref-selector__item:active,
    &.c-commit-ref-selector__item:hover {
      background-color: var(--ds-color-neutral-6);
      color: var(--ds-color-neutral-12);
    }
  }

  .c-commit-ref-selector__item-content :global(.c-text.c-commit-ref-selector__item-date) {
    font-style: italic;
  }
  @media (max-width: 450px) {
    .c-commit-ref-selector__item-content :global(.c-commit-ref-selector__item-date) {
      display: none;
    }
  }

  .c-commit-ref-selector__error-content {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-3);
    width: 100%;
  }

  .c-commit-ref-selector__error-message {
    margin-bottom: var(--ds-spacing-1);
  }
</style>
