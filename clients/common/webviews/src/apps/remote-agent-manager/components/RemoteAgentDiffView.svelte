<script lang="ts">
  import RemoteAgentDiffIcon from "$common-webviews/src/apps/remote-agent-manager/components/RemoteAgentDiffIcon.svelte";
  import RemoteAgentDiffViewCode from "$common-webviews/src/apps/remote-agent-manager/components/RemoteAgentDiffViewCode.svelte";
  import SkeletonDiffView from "$common-webviews/src/apps/remote-agent-manager/components/SkeletonDiffView.svelte";
  import MaterialIcon from "$common-webviews/src/common/components/MaterialIcon.svelte";
  import { AsyncMsgSender } from "$common-webviews/src/common/hosts/async-messaging";
  import { host } from "$common-webviews/src/common/hosts/host";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import SpinnerAugment from "$common-webviews/src/design-system/components/SpinnerAugment.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import Collapse from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/collapse.svg?component";
  import Expand from "$common-webviews/src/design-system/icons/augment/expand.svelte";
  import Play from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/play-augment.svg?component";
  import ExclamationTriangle from "$common-webviews/src/design-system/icons/exclamation-triangle.svelte";
  import type {
    ChangedFile,
    Diff,
    DiffAppliedState,
    DiffExplanation,
    DiffExplanationSection,
    DiffExplanationSubSection,
    DiffType,
  } from "@augment-internal/sidecar-libs/src/remote-agent/remote-agent-types";
  import { createTwoFilesPatch } from "diff";
  import { getContext, onMount } from "svelte";
  import { writable } from "svelte/store";
  import MarkdownWithColors from "../../../common/components/markdown/MarkdownWithColors.svelte";
  import { applyPreparedChanges, getAllFilePaths, prepareFilesToApply } from "../utils/index";
  import TreeViewChanges from "./TreeViewChanges.svelte";
  import { generateStableId } from "$vscode/src/utils/get-diff-explanation";
  import { RemoteAgentDiffOpsModel } from "../models/ra-diff-ops-model";
  import RegularTriangleExclamationIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/triangle-exclamation.svg?component";
  import ConflictCard from "./DiffView/ConflictCard.svelte";
  import StashModal from "./DiffView/StashModal.svelte";

  // Component props
  export let changedFiles: ChangedFile[];
  export let agentLabel: string | undefined = undefined;
  export let latestUserPrompt: string | undefined = undefined;
  export let onApplyChanges:
    | ((path: string, originalCode: string, newCode: string) => Promise<void>)
    | undefined = undefined;
  export let onOpenFile: ((path: string) => Promise<boolean>) | undefined = undefined;
  export let onRenderBackup: (() => void) | undefined = undefined;
  export let preloadedExplanation: DiffExplanation | undefined = undefined;
  export let isAgentFromDifferentRepo: boolean = false;
  /** List of file paths that have conflicts when merging */
  export let conflictFiles: Set<string> = new Set();

  const diffOpsModel = getContext<RemoteAgentDiffOpsModel>(RemoteAgentDiffOpsModel.key);

  // Component state
  let apikey = "";
  let isMounted = false;
  let diffExplanation: DiffExplanation = [];
  let groupedChanges: { path: string; changes: Diff[] }[] = [];
  let isLoading = false;
  let isLoadingDescriptions = false;
  let errorMessage: string | null = null;
  let areAllExpanded = true;
  // Track expanded states for tree nodes
  let collapsedPaths: Record<string, boolean> = {};
  let diffComponents: RemoteAgentDiffViewCode[] = [];
  let isApplyingAll = false;
  let hasAppliedAll = false;
  let areDescriptionsVisible = true;

  /** List of file paths that we have detected will have conflicts before merging */
  let previewConflictFiles: Set<string> = new Set();

  /** True if we should show the modal for unstaged changes */
  let showUnstagedChangesModal = false;

  /**
   * Key: Path
   * Value: Applied state of the file
   */
  const diffViewFilesMap = writable<Record<string, DiffAppliedState>>({});

  $: {
    if (changedFiles) {
      // When the changedFiles change, update the diffViewFiles store
      diffViewFilesMap.set(
        changedFiles.reduce(
          (acc, file) => {
            // Use appropriate path as key: old_path for deleted files, new_path for others
            const fileKey = file.new_path || file.old_path;
            // If this file was already in the store, keep its state
            acc[fileKey] = $diffViewFilesMap[fileKey] ?? "none";
            return acc;
          },
          {} as Record<string, DiffAppliedState>,
        ),
      );
    }
  }

  // Track modified code for each file
  let modifiedCodeMap: Record<string, string> = {};

  $: areAnyCollapsed = Object.values(collapsedPaths).some(Boolean);

  // Initialize modifiedCodeMap when diffExplanation changes
  $: if (diffExplanation && diffExplanation.length > 0) {
    // Initialize with the original modified code for each file
    diffExplanation
      .flatMap((section) => section.sections || [])
      .flatMap((subsection) => subsection.changes)
      .forEach((change) => {
        if (!modifiedCodeMap[change.path]) {
          modifiedCodeMap[change.path] = change.modifiedCode;
        }
      });
  }

  // Track changes to diffExplanation, applied and pending files
  $: diffExplanationString = JSON.stringify(diffExplanation);

  // Initialize expanded states when diffExplanation changes
  $: if (diffExplanation && diffExplanation.length > 0) {
    const allPaths = getAllFilePaths(diffExplanation);
    // Set default expanded state for new paths
    Array.from(allPaths).forEach((path) => {
      if (collapsedPaths[path] === undefined) {
        collapsedPaths[path] = !areAllExpanded;
      }
    });

    // Check if all paths are expanded or collapsed
    const pathsThatHaveBeenCollapsed = Object.keys(collapsedPaths).filter(
      (path) => collapsedPaths[path],
    );
    const pathsInDiff = Array.from(allPaths);

    // Update areAllExpanded if all paths are collapsed
    if (pathsInDiff.length > 0) {
      areAllExpanded = !pathsInDiff.some((path) => pathsThatHaveBeenCollapsed.includes(path));
    }
  }

  // Computed property to check if there are any files to apply
  $: hasFilesToApply = (() => {
    // Re-compute when diffExplanation, appliedFiles, or pendingFiles change
    if (diffExplanationString && $diffViewFilesMap) {
      const allPaths = getAllFilePaths(diffExplanation);
      if (allPaths.size === 0) return false;

      // Check if there are any files that haven't been applied yet
      return Array.from(allPaths).some((path) => $diffViewFilesMap[path] !== "applied");
    }
    return false;
  })();

  // Initialize component
  onMount(() => {
    const savedApiKey = localStorage.getItem("anthropic_apikey");
    if (savedApiKey) {
      apikey = savedApiKey;
    }
    isMounted = true;
  });

  /**
   * Toggle expansion state of all diff components
   */
  function toggleAllDiffs() {
    // Check if all paths are currently expanded
    const allPaths = getAllFilePaths(diffExplanation);

    // If all paths are expanded, collapse them all
    // If any path is collapsed, expand them all
    const shouldExpand = Object.values(collapsedPaths).some(Boolean);

    // Update the areAllExpanded state
    areAllExpanded = shouldExpand;

    // Update all expanded states
    Array.from(allPaths).forEach((path) => {
      collapsedPaths[path] = !areAllExpanded;
    });
  }

  /**
   * Handle code changes from a file
   */
  function handleCodeChange(path: string, modifiedCode: string) {
    // Store the modified code in our map
    modifiedCodeMap[path] = modifiedCode;
  }

  async function applyOneChange(path: string, originalCode: string, newCode: string) {
    if (!onApplyChanges) {
      return;
    }
    diffViewFilesMap.update((files) => {
      files[path] = "pending";
      return files;
    });
    return new Promise<void>((resolve) => {
      onApplyChanges?.(path, originalCode, newCode).then(() => {
        diffViewFilesMap.update((files) => {
          files[path] = "applied";
          return files;
        });
        resolve();
      });
    });
  }

  $: hasAppliedAll = Object.keys($diffViewFilesMap).every((path) => {
    return $diffViewFilesMap[path] === "applied";
  });

  $: pendingFiles = Object.keys($diffViewFilesMap).filter(
    (path) => $diffViewFilesMap[path] === "pending",
  );

  async function checkAndApplyAllChanges() {
    const canApplyChanges = await diffOpsModel.canApplyChanges();
    if (!canApplyChanges.canApply) {
      if (canApplyChanges.hasUnstagedChanges) {
        showUnstagedChangesModal = true;
      }
      return;
    }
    applyAllChanges();
  }

  /**
   * Apply changes to all files
   */
  function applyAllChanges() {
    if (!onApplyChanges) return;

    // Report event
    diffOpsModel.reportApplyChangesEvent();

    // Set applying state
    isApplyingAll = true;
    hasAppliedAll = false;

    // Use the utility functions to prepare and apply changes
    const { filesToApply, areAllPathsApplied } = prepareFilesToApply(
      diffExplanation,
      changedFiles,
      modifiedCodeMap,
    );

    // Check if all paths are already applied or if there are no files to apply
    if (areAllPathsApplied || filesToApply.length === 0) {
      hasAppliedAll = areAllPathsApplied;
      return;
    }

    applyPreparedChanges(filesToApply, applyOneChange).then(() => {
      isApplyingAll = false;
      hasAppliedAll = true;
    });
  }

  async function getMergeConflictFiles(
    diffExplanation: DiffExplanation,
    changedFiles: ChangedFile[],
    modifiedCodeMap: Record<string, string>,
  ) {
    const { filesToApply } = prepareFilesToApply(diffExplanation, changedFiles, modifiedCodeMap);
    const result = new Set<string>();
    for (const file of filesToApply) {
      const response = await diffOpsModel.previewApplyChanges(
        file.path,
        file.originalCode,
        file.newCode,
      );
      if (response.hasConflicts) {
        result.add(file.path);
      }
    }
    previewConflictFiles = result;
  }

  $: getMergeConflictFiles(diffExplanation, changedFiles, modifiedCodeMap);

  /**
   * Create a skeleton diffExplanation from grouped changes
   * This will be shown while waiting for the descriptions
   * Also generates stable IDs for code chunks
   */
  function createSkeletonExplanation(
    changes: { path: string; changes: Diff[] }[],
  ): DiffExplanation {
    // Create a section for all files
    const section: DiffExplanationSection = {
      title: "Loading...",
      description: "",
      sections: [],
    };

    // Create a subsection for each file in the original order
    changes.forEach((fileChange) => {
      // Generate stable IDs for each change based on the file path
      const changesWithStableIds = fileChange.changes.map((change) => {
        // If the change already has an ID, use it
        if (change.id) return change;

        // Otherwise, generate a stable ID based on the path and content hash
        const pathId = generateStableId(change.path);
        const contentHash = generateStableId(change.originalCode + change.modifiedCode);
        return {
          ...change,
          id: `${pathId}-${contentHash}`,
        };
      });

      const subsection: DiffExplanationSubSection = {
        title: fileChange.path,
        descriptions: [], // Empty descriptions indicate loading state
        type: "other",
        changes: changesWithStableIds,
      };
      section.sections.push(subsection);
    });

    return [section];
  }

  /**
   * Create a simple diffExplanation from changedFiles
   */
  function createSimpleDiffExplanation(files: ChangedFile[]): DiffExplanation {
    // Create a simple section for all files
    const section: DiffExplanationSection = {
      title: "Changed Files",
      description: `${files.length} files were changed`,
      sections: [],
    };

    // Group files by type (added, modified, deleted)
    const added: ChangedFile[] = [];
    const modified: ChangedFile[] = [];
    const deleted: ChangedFile[] = [];

    files.forEach((file) => {
      if (!file.old_path) {
        added.push(file);
      } else if (!file.new_path) {
        deleted.push(file);
      } else {
        modified.push(file);
      }
    });

    // Create subsections for each type
    if (added.length > 0) {
      section.sections.push(createSubsection("Added files", "feature", added));
    }

    if (modified.length > 0) {
      section.sections.push(createSubsection("Modified files", "fix", modified));
    }

    if (deleted.length > 0) {
      section.sections.push(createSubsection("Deleted files", "chore", deleted));
    }

    return [section];
  }

  /**
   * Create a subsection for a group of files
   */
  function createSubsection(
    title: string,
    type: DiffType,
    files: ChangedFile[],
  ): DiffExplanationSubSection {
    const changes: Diff[] = [];

    files.forEach((file) => {
      const path = file.new_path || file.old_path;
      const oldContent = file.old_contents || "";
      const newContent = file.new_contents || "";

      // For new files, we'll use an empty string for old_path instead of /dev/null
      // This helps avoid showing a red line at the top of new files
      const oldPath = !file.old_path ? "" : file.old_path;

      // Create a diff for the file
      const diffResult = createTwoFilesPatch(
        oldPath,
        file.new_path || "/dev/null",
        oldContent,
        newContent,
        "",
        "",
        { context: 3 },
      );

      // Generate a stable ID based on the path and content
      const pathId = generateStableId(path);
      const contentHash = generateStableId(oldContent + newContent);
      const stableId = `${pathId}-${contentHash}`;

      changes.push({
        id: stableId,
        path,
        diff: diffResult,
        originalCode: oldContent,
        modifiedCode: newContent,
      });
    });

    return {
      title,
      descriptions: [],
      type,
      changes,
    };
  }

  $: isEmpty = changedFiles.length === 0;

  /**
   * Fetch explanation for the changed files using a two-step process
   * 1. First get grouped changes
   * 2. Then get descriptions for those changes
   */
  async function getExplanation() {
    if (!isMounted) return;
    isLoading = true;
    isLoadingDescriptions = false;
    errorMessage = null;
    groupedChanges = [];
    diffExplanation = [];

    if (isEmpty) {
      isLoading = false;
      return;
    }

    // Check if the changedFiles are too large (more than 12 files or 500KB total)
    const MAX_FILES = 12;
    const MAX_CONTENT_SIZE = 500 * 1024; // 500KB
    const MAX_INDIVIDUAL_FILE_SIZE_FOR_DESCRIPTION_BYTES = 100 * 1024; // 100KB limit for sending to description service

    let totalSize = 0;
    changedFiles.forEach((file) => {
      totalSize += (file.old_contents?.length || 0) + (file.new_contents?.length || 0);
    });

    const isTooLarge = changedFiles.length > MAX_FILES || totalSize > MAX_CONTENT_SIZE;

    if (isTooLarge) {
      // Use the simple explanation for large changes
      try {
        diffExplanation = createSimpleDiffExplanation(changedFiles);
      } catch (error) {
        console.error("Failed to create simple explanation:", error);
        errorMessage = "Failed to create explanation for large changes.";
      }
      isLoading = false;
      return;
    }

    try {
      const asyncMsgSender = new AsyncMsgSender((message) => host.postMessage(message));

      // Create a map to store the full changes by ID
      const changesById = new Map();

      // Generate stable IDs for each file and create a lightweight version
      const changedFilesWithIds = changedFiles.map((file) => {
        const path = file.new_path || file.old_path;
        const oldContent = file.old_contents || "";
        const newContent = file.new_contents || "";

        // Generate a stable ID based on the path and content
        const pathId = generateStableId(path);
        const contentHash = generateStableId(oldContent + newContent);
        const stableId = `${pathId}-${contentHash}`;

        // Store the full file content in the map
        changesById.set(stableId, {
          // eslint-disable-next-line @typescript-eslint/naming-convention
          old_path: file.old_path,
          // eslint-disable-next-line @typescript-eslint/naming-convention
          new_path: file.new_path,
          // eslint-disable-next-line @typescript-eslint/naming-convention
          old_contents: oldContent,
          // eslint-disable-next-line @typescript-eslint/naming-convention
          new_contents: newContent,
          // eslint-disable-next-line @typescript-eslint/naming-convention
          change_type: file.change_type,
        });

        // Return a lightweight version with just the ID and paths
        return {
          id: stableId,
          // eslint-disable-next-line @typescript-eslint/naming-convention
          old_path: file.old_path,
          // eslint-disable-next-line @typescript-eslint/naming-convention
          new_path: file.new_path,
          // eslint-disable-next-line @typescript-eslint/naming-convention
          change_type: file.change_type,
        };
      });

      // Step 1: Get grouped changes
      try {
        const canSkipGrouping = changedFilesWithIds.length === 1;
        let groupedChangesWithIds: { path: string; changes: any[] }[] = [];
        if (canSkipGrouping) {
          groupedChangesWithIds = changedFilesWithIds.map((file) => ({
            path: file.new_path || file.old_path,
            changes: [
              {
                id: file.id,
                path: file.new_path || file.old_path,
                diff: `File: ${file.new_path || file.old_path}\nChange type: ${file.change_type || "modified"}`,
                originalCode: "",
                modifiedCode: "",
              },
            ],
          }));
        } else {
          const groupChangesRes = await asyncMsgSender.send(
            {
              type: "get-diff-group-changes-request" as any, // Use string literal instead of enum
              data: { changedFiles: changedFilesWithIds, changesById: true, apikey },
            },
            30000, // 30 second timeout for this step
          );

          // Get the grouped changes with IDs
          groupedChangesWithIds = (groupChangesRes as any).data.groupedChanges;
        }

        // Restore the full content for each change using our map
        groupedChanges = groupedChangesWithIds.map((group: any) => {
          return {
            path: group.path,
            changes: group.changes.map((change: any) => {
              // If this is a change with an ID, restore the full content
              if (change.id && changesById.has(change.id)) {
                const fullChange = changesById.get(change.id);

                // Generate the diff content if it's not already present
                // This ensures we have proper diff content for the UI
                let diffContent = change.diff;
                if (!diffContent || diffContent.startsWith("File:")) {
                  // Generate a proper diff using the full content
                  diffContent = createTwoFilesPatch(
                    fullChange.old_path || "",
                    fullChange.new_path || "",
                    fullChange.old_contents || "",
                    fullChange.new_contents || "",
                  );
                }

                return {
                  ...change,
                  diff: diffContent,
                  // eslint-disable-next-line @typescript-eslint/naming-convention
                  old_path: fullChange.old_path,
                  // eslint-disable-next-line @typescript-eslint/naming-convention
                  new_path: fullChange.new_path,
                  // eslint-disable-next-line @typescript-eslint/naming-convention
                  old_contents: fullChange.old_contents,
                  // eslint-disable-next-line @typescript-eslint/naming-convention
                  new_contents: fullChange.new_contents,
                  // eslint-disable-next-line @typescript-eslint/naming-convention
                  change_type: fullChange.change_type,
                  originalCode: fullChange.old_contents || "",
                  modifiedCode: fullChange.new_contents || "",
                };
              }
              return change;
            }),
          };
        });
      } catch (error) {
        console.error("Failed to group changes with LLM, falling back to simple grouping:", error);

        // Fallback: Use the simple explanation for changes
        // This is similar to what we do for large changes
        try {
          // Create a simple explanation using the original changedFiles
          // We need to convert back from IDs to full content
          const originalChangedFiles = changedFilesWithIds.map((file: any) => {
            if (file.id && changesById.has(file.id)) {
              const fullChange = changesById.get(file.id);

              // Make sure we have all the required fields
              return {
                ...file,
                // eslint-disable-next-line @typescript-eslint/naming-convention
                old_path: fullChange.old_path,
                // eslint-disable-next-line @typescript-eslint/naming-convention
                new_path: fullChange.new_path,
                // eslint-disable-next-line @typescript-eslint/naming-convention
                old_contents: fullChange.old_contents || "",
                // eslint-disable-next-line @typescript-eslint/naming-convention
                new_contents: fullChange.new_contents || "",
                // eslint-disable-next-line @typescript-eslint/naming-convention
                change_type: fullChange.change_type,
              };
            }
            return file;
          });

          // Create a simple explanation
          diffExplanation = createSimpleDiffExplanation(originalChangedFiles);

          // Extract the grouped changes from the explanation
          groupedChanges = diffExplanation[0].sections.map((section) => ({
            path: section.title,
            changes: section.changes,
          }));

          // We already have the full explanation, so we can skip the second step
          isLoadingDescriptions = false;
        } catch (fallbackError) {
          console.error("Failed to create simple explanation:", fallbackError);
          errorMessage = "Failed to group changes. Please try again.";
        }
      }

      // Stop loading indicator for the initial step
      isLoading = false;

      if (!groupedChanges || groupedChanges.length === 0) {
        throw new Error("Failed to group changes");
      }

      // If we don't already have a full explanation (from the fallback)
      if (!diffExplanation || diffExplanation.length === 0) {
        // Create a skeleton explanation with stable IDs to show while waiting for descriptions
        diffExplanation = createSkeletonExplanation(groupedChanges);

        // Extract the grouped changes with stable IDs from the skeleton explanation
        // Make sure we're sending the full hydrated changes with IDs
        const groupedChangesWithStableIds = diffExplanation[0].sections.map((section) => ({
          path: section.title,
          changes: section.changes.map((change) => {
            // Check if the original or modified code is too large for the description service
            const originalCodeSize = change.originalCode?.length || 0;
            const modifiedCodeSize = change.modifiedCode?.length || 0;
            const diffSize = change.diff?.length || 0;

            // If any of the content is too large, use placeholders or truncated content
            const isTooLargeForDescription =
              originalCodeSize > MAX_INDIVIDUAL_FILE_SIZE_FOR_DESCRIPTION_BYTES ||
              modifiedCodeSize > MAX_INDIVIDUAL_FILE_SIZE_FOR_DESCRIPTION_BYTES ||
              diffSize > MAX_INDIVIDUAL_FILE_SIZE_FOR_DESCRIPTION_BYTES;

            if (isTooLargeForDescription) {
              // For the description service, we'll use a placeholder message
              // This doesn't affect the actual content in diffExplanation that's used for UI rendering
              return {
                id: change.id,
                path: change.path,
                diff: `File: ${change.path}\nContent too large to include in explanation request (${Math.max(originalCodeSize, modifiedCodeSize, diffSize)} bytes)`,
                // Use a placeholder message that won't be displayed in the UI
                // The actual content is preserved in the diffExplanation object
                originalCode:
                  originalCodeSize > MAX_INDIVIDUAL_FILE_SIZE_FOR_DESCRIPTION_BYTES
                    ? `[File content too large: ${originalCodeSize} bytes]`
                    : change.originalCode,
                modifiedCode:
                  modifiedCodeSize > MAX_INDIVIDUAL_FILE_SIZE_FOR_DESCRIPTION_BYTES
                    ? `[File content too large: ${modifiedCodeSize} bytes]`
                    : change.modifiedCode,
              };
            }

            // For normal-sized files, include the full content
            return {
              id: change.id,
              path: change.path,
              diff: change.diff,
              // Include these fields to ensure proper hydration on return
              originalCode: change.originalCode,
              modifiedCode: change.modifiedCode,
            };
          }),
        }));

        // Start loading indicator for descriptions
        isLoadingDescriptions = true;

        // Step 2: Get descriptions for the grouped changes with stable IDs
        try {
          // Get the explanation with descriptions from the service
          const { explanation: explanationWithDescriptions, error } =
            await diffOpsModel.getDescriptions(groupedChangesWithStableIds, apikey);

          if (error === "Token limit exceeded") {
            diffExplanation = createSimpleDiffExplanation(changedFiles);
            isLoading = false;
            isLoadingDescriptions = false;
            return;
          }

          // Merge the descriptions with our original full content
          // This ensures we keep the full file content while getting the descriptions
          if (explanationWithDescriptions && explanationWithDescriptions.length > 0) {
            // Type-safe iteration through the explanation structure
            explanationWithDescriptions.forEach(
              (section: DiffExplanationSection, sectionIndex: number) => {
                if (section.sections) {
                  section.sections.forEach(
                    (subsection: DiffExplanationSubSection, subsectionIndex: number) => {
                      if (subsection.changes) {
                        subsection.changes.forEach((change: Diff) => {
                          // Find the corresponding change in the original explanation
                          const originalSection = diffExplanation[sectionIndex];
                          if (originalSection && originalSection.sections) {
                            const originalSubsection = originalSection.sections[subsectionIndex];
                            if (originalSubsection && originalSubsection.changes) {
                              const originalChange = originalSubsection.changes.find(
                                (c: Diff) => c.id === change.id,
                              );
                              if (originalChange) {
                                // Preserve the original code content
                                change.originalCode = originalChange.originalCode;
                                change.modifiedCode = originalChange.modifiedCode;
                                change.diff = originalChange.diff;
                              }
                            }
                          }
                        });
                      }
                    },
                  );
                }
              },
            );
          }

          diffExplanation = explanationWithDescriptions;
        } catch (descriptionsError) {
          console.error(
            "Failed to get descriptions, using skeleton explanation:",
            descriptionsError,
          );
          // Keep the skeleton explanation we already created
        }
      }

      if (diffExplanation.length === 0) {
        errorMessage = "Failed to generate explanation.";
      }
    } catch (error) {
      console.error("Failed to get explanation:", error);
      errorMessage =
        error instanceof Error
          ? error.message
          : "An error occurred while generating the explanation.";
    } finally {
      isLoading = false;
      isLoadingDescriptions = false;
    }
  }

  // Track changes to files and trigger explanation
  $: changedFilesString = JSON.stringify(changedFiles);
  let lastChangedFilesString = "";
  $: if (isMounted && changedFilesString) {
    if (changedFilesString !== lastChangedFilesString) {
      lastChangedFilesString = changedFilesString;
      // If we have preloaded explanations, use them
      if (preloadedExplanation && preloadedExplanation.length > 0) {
        diffExplanation = preloadedExplanation;
        isLoading = false;
        isLoadingDescriptions = false;
      } else {
        // Otherwise, fetch the explanation
        getExplanation();
      }
      // Reset apply states when files change
      isApplyingAll = false;
      hasAppliedAll = false;
      // Reset modified code map when files change
      modifiedCodeMap = {};
    }
  }

  // Reset hasAppliedAll when diffExplanation changes
  $: if (diffExplanationString && hasAppliedAll) {
    // Check if all files are still applied
    const allPaths = getAllFilePaths(diffExplanation);
    const allStillApplied = Array.from(allPaths).every(
      (path) => $diffViewFilesMap[path] === "applied",
    );
    if (!allStillApplied) {
      hasAppliedAll = false;
    }
  }

  $: appliedWithConflicts = hasAppliedAll && conflictFiles.size > 0;
  $: applyAllDisabled =
    isAgentFromDifferentRepo ||
    isApplyingAll ||
    hasAppliedAll ||
    pendingFiles.length > 0 ||
    !hasFilesToApply;
  let applyAllTooltip = "Apply all changes locally";
  $: {
    if (applyAllDisabled) {
      if (isAgentFromDifferentRepo) {
        applyAllTooltip = "Cannot apply changes from a different repository locally";
      } else if (isApplyingAll) {
        applyAllTooltip = "Applying changes...";
      } else if (appliedWithConflicts) {
        applyAllTooltip = "All changes applied, but conflicts need to be resolved manually";
      } else if (hasAppliedAll) {
        applyAllTooltip = "All changes applied";
      } else if (pendingFiles.length > 0) {
        applyAllTooltip = "Waiting for changes to apply";
      } else if (!hasFilesToApply) {
        applyAllTooltip = "No changes to apply";
      }
    } else {
      applyAllTooltip = "Apply all changes locally";
    }
  }
</script>

<div class="c-diff-view">
  {#if errorMessage}
    <div class="c-diff-view__error">
      <ExclamationTriangle />
      {errorMessage}
      <ButtonAugment variant="ghost" size={1} on:click={getExplanation}>Retry</ButtonAugment>
      {#if onRenderBackup}
        <ButtonAugment variant="ghost" size={1} on:click={onRenderBackup}
          >Render as list</ButtonAugment
        >
      {/if}
    </div>
  {/if}

  {#if isEmpty}
    <div class="c-diff-view__empty">
      <TextAugment size={2} color="secondary">No files changed</TextAugment>
    </div>
  {:else}
    <div class="c-diff-view__layout">
      <div class="c-diff-view__tree">
        <div class="c-diff-view__tree__header">
          {#if agentLabel && latestUserPrompt !== agentLabel}
            <TextAugment size={1} class="c-diff-view__tree__header__label">
              Changes from agent
            </TextAugment>
            <TextAugment size={1} weight="medium" class="c-diff-view__tree__header__title">
              {agentLabel}
            </TextAugment>
          {/if}

          {#if latestUserPrompt}
            <TextAugment size={1} class="c-diff-view__tree__header__label"
              >Last user prompt</TextAugment
            >
            <TextAugment size={1} weight="medium" class="c-diff-view__tree__header__title">
              {latestUserPrompt}
            </TextAugment>
          {/if}

          <TextAugment size={1} class="c-diff-view__tree__header__label">Changed files</TextAugment>
          <TreeViewChanges {changedFiles} />
        </div>
      </div>

      <div class="c-diff-view__explanation">
        {#if isLoading && groupedChanges.length === 0}
          <div class="c-diff-view__controls">
            <TextTooltipAugment
              content={isApplyingAll
                ? "Applying changes..."
                : hasAppliedAll
                  ? "All changes applied"
                  : !hasFilesToApply
                    ? "No changes to apply"
                    : "Apply all changes"}
            >
              <ButtonAugment
                variant="ghost-block"
                color="neutral"
                size={2}
                on:click={checkAndApplyAllChanges}
                disabled={isApplyingAll ||
                  hasAppliedAll ||
                  pendingFiles.length > 0 ||
                  !hasFilesToApply}
              >
                {#if isApplyingAll}
                  <div class="c-diff-view__applying">
                    <SpinnerAugment size={1} useCurrentColor={true} />
                    <TextAugment size={2}>Applying...</TextAugment>
                  </div>
                {:else if hasAppliedAll}
                  <div class="c-diff-view__applied">
                    <TextAugment size={2}>
                      Applied
                      <MaterialIcon iconName="check" />
                    </TextAugment>
                  </div>
                {:else}
                  Apply All
                  <div class="c-diff-view__controls__icon">
                    <Play />
                  </div>
                {/if}
              </ButtonAugment>
            </TextTooltipAugment>
          </div>

          <SkeletonDiffView count={2} />
        {:else if diffExplanation && diffExplanation.length > 0}
          {#each diffExplanation as section, sectionIndex}
            <div class="c-diff-view__section" id={`section-${sectionIndex}`}>
              <div class="c-diff-view__header">
                <div class="c-diff-view__content">
                  <h5 class="c-diff-view__title">
                    {#if isLoadingDescriptions && section.title === "Loading..."}
                      <div class="c-diff-view__skeleton-title"></div>
                    {:else}
                      {section.title}
                    {/if}
                  </h5>
                  <div class="c-diff-view__description">
                    {#if isLoadingDescriptions && section.description === ""}
                      <div class="c-diff-view__skeleton-text"></div>
                      <div class="c-diff-view__skeleton-text"></div>
                    {:else}
                      <MarkdownWithColors markdown={section.description} />
                    {/if}
                  </div>
                </div>

                {#if sectionIndex === 0}
                  <div class="c-diff-view__controls">
                    <ButtonAugment
                      variant="ghost-block"
                      color="neutral"
                      size={2}
                      on:click={toggleAllDiffs}
                    >
                      {#if !areAnyCollapsed}
                        <Collapse />
                        Collapse All
                      {:else}
                        <Expand />
                        Expand All
                      {/if}
                    </ButtonAugment>

                    <TextTooltipAugment content={applyAllTooltip}>
                      <ButtonAugment
                        variant="ghost-block"
                        color="neutral"
                        size={2}
                        on:click={checkAndApplyAllChanges}
                        disabled={applyAllDisabled}
                      >
                        {#if isApplyingAll}
                          <div class="c-diff-view__applying">
                            <SpinnerAugment size={1} useCurrentColor={true} />
                            <TextAugment size={2}>Applying...</TextAugment>
                          </div>
                        {:else if hasAppliedAll}
                          <div class="c-diff-view__applied">
                            <TextAugment size={2}>Applied</TextAugment>
                            {#if appliedWithConflicts}
                              <RegularTriangleExclamationIcon slot="rightIcon" />
                            {:else}
                              <MaterialIcon iconName="check" />
                            {/if}
                          </div>
                        {:else}
                          Apply All
                          <div class="c-diff-view__controls__icon">
                            <Play />
                          </div>
                        {/if}
                      </ButtonAugment>
                    </TextTooltipAugment>
                  </div>
                {/if}
              </div>
              {#if ((hasAppliedAll && conflictFiles.size > 0) || (!hasAppliedAll && previewConflictFiles.size > 0)) && sectionIndex === 0}
                {@const filesWithConflicts = hasAppliedAll ? conflictFiles : previewConflictFiles}
                <ConflictCard files={filesWithConflicts} {hasAppliedAll} {onOpenFile} />
              {/if}
              {#each section.sections || [] as subsection, subsectionIndex}
                <div
                  class="c-diff-view__subsection"
                  id={`subsection-${sectionIndex}-${subsectionIndex}`}
                >
                  <div class="c-diff-view__header">
                    <div class="c-diff-view__content">
                      <div class="c-diff-view__icon">
                        <RemoteAgentDiffIcon type={subsection.type} />
                      </div>
                      <h5 class="c-diff-view__title">
                        {#if isLoadingDescriptions && subsection.descriptions.length === 0}
                          <div class="c-diff-view__skeleton-text"></div>
                        {:else}
                          {subsection.title}
                        {/if}
                      </h5>
                      {#if !isLoadingDescriptions && subsection.warning}
                        <div class="c-diff-view__warning">
                          <ExclamationTriangle />
                          {subsection.warning}
                        </div>
                      {/if}
                    </div>
                  </div>

                  <div class="c-diff-view__changes">
                    {#each subsection.changes as fileChange (fileChange.id)}
                      <div class="c-diff-view__changes-item">
                        <RemoteAgentDiffViewCode
                          path={fileChange.path}
                          change={fileChange}
                          descriptions={subsection.descriptions}
                          isExpandedDefault={collapsedPaths[fileChange.path] !== undefined
                            ? !collapsedPaths[fileChange.path]
                            : areAllExpanded}
                          bind:isCollapsed={collapsedPaths[fileChange.path]}
                          bind:this={diffComponents[
                            sectionIndex * 100 +
                              subsectionIndex * 10 +
                              (fileChange.path.length % 10)
                          ]}
                          bind:areDescriptionsVisible
                          isApplying={$diffViewFilesMap[fileChange.path] === "pending"}
                          hasApplied={$diffViewFilesMap[fileChange.path] === "applied"}
                          onCodeChange={(modifiedCode) => {
                            // Store the modified code in our map
                            handleCodeChange(fileChange.path, modifiedCode);
                          }}
                          onApplyChanges={() => {
                            applyOneChange(
                              fileChange.path,
                              fileChange.originalCode,
                              fileChange.modifiedCode,
                            );
                          }}
                          onOpenFile={onOpenFile ? () => onOpenFile(fileChange.path) : undefined}
                          {isAgentFromDifferentRepo}
                        />
                      </div>
                    {/each}
                  </div>
                </div>
              {/each}
            </div>
          {/each}
        {/if}
      </div>
    </div>
  {/if}
</div>
<StashModal bind:showModal={showUnstagedChangesModal} {applyAllChanges} />

<style>
  .c-diff-view {
    padding: var(--ds-spacing-2) var(--ds-spacing-2);
    container-type: inline-size;
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .c-diff-view__empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    flex: 1;
    padding: var(--ds-spacing-4);
  }

  .c-diff-view__layout {
    display: grid;
    grid-template-columns: 200px 1fr;
    gap: var(--ds-spacing-5);
    flex: 1;
  }
  .c-diff-view__tree {
    display: flex;
    flex-direction: column;
    padding-top: var(--ds-spacing-2);
    position: sticky;
    top: 0;
    height: fit-content;
  }
  .c-diff-view__tree__header {
    display: flex;
    flex-direction: column;
    padding: var(--ds-spacing-1) 0;
    width: 100%;
  }
  .c-diff-view__tree__header :global(.c-diff-view__tree__header__label) {
    display: block;
    color: var(--ds-color-neutral-10);
    margin-bottom: 2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .c-diff-view__tree__header :global(.c-diff-view__tree__header__title) {
    display: block;
    color: var(--ds-color-neutral-12);
    margin-bottom: var(--ds-spacing-4);
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  @media (max-width: 1300px) {
    .c-diff-view__layout {
      grid-template-columns: 150px 1fr;
    }
  }
  @media (max-width: 1000px) {
    .c-diff-view {
      padding: var(--ds-spacing-2) 0;
    }
    .c-diff-view__layout {
      grid-template-columns: 100px 1fr;
    }
  }
  @media (max-width: 900px) {
    .c-diff-view__layout {
      grid-template-columns: 1fr;
    }
    .c-diff-view__tree {
      position: relative;
    }
  }

  .c-diff-view__explanation {
    min-width: 0;
  }

  .c-diff-view__error {
    color: var(--ds-color-neutral-9);
    font-size: 0.9rem;
    margin-bottom: var(--ds-spacing-3);
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-2);
  }

  .c-diff-view__section {
    margin-bottom: var(--ds-spacing-6);
  }

  .c-diff-view__subsection {
    margin-top: var(--ds-spacing-2);
    margin-bottom: var(--ds-spacing-1);
  }
  .c-diff-view__icon {
    color: var(--ds-color-neutral-8);
    margin: 0 var(--ds-spacing-3) 0 0;
  }

  .c-diff-view__header {
    display: flex;
    align-items: start;
    gap: var(--ds-spacing-1);
    padding: var(--ds-spacing-2) 0 var(--ds-spacing-2) 0;
    flex: 1;
  }

  @container (max-width: 600px) {
    .c-diff-view__header {
      flex-direction: column-reverse;
      gap: var(--ds-spacing-3);
    }
    .c-diff-view__controls {
      align-self: flex-start;
    }
  }

  .c-diff-view__subsection .c-diff-view__header .c-diff-view__content {
    display: block;
  }
  .c-diff-view__subsection .c-diff-view__header .c-diff-view__content {
    position: sticky;
    top: var(--ds-spacing-6);
    align-self: flex-start;
    padding: 0 0 var(--ds-spacing-1) 0;
    display: flex;
    align-items: center;
  }
  .c-diff-view__content {
    flex: 1;
    width: 100%;
  }
  .c-diff-view__title {
    margin: 0;
    font-size: 1rem;
    font-weight: 500;
    padding-top: 3.5px;
    width: 100%;
  }
  /* Subsection title styles */
  .c-diff-view__subsection .c-diff-view__title {
    font-weight: 500;
  }

  .c-diff-view__description {
    margin: var(--ds-spacing-1) 0 0 0;
    color: var(--ds-color-neutral-10);
    font-weight: 400;
  }

  .c-diff-view__description :global(p.augment-markdown-paragraph) {
    padding: 0;
  }

  .c-diff-view__warning {
    color: var(--ds-color-warning-11);
    font-size: 0.9rem;
    margin-top: var(--ds-spacing-2);
    margin-bottom: var(--ds-spacing-2);
    margin-left: var(--ds-spacing-6);
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-2);
  }

  .c-diff-view__changes {
    padding-left: var(--ds-spacing-4);
    border-left: 1px solid var(--ds-color-neutral-5);
    margin-left: 6px;
  }
  @media (max-width: 1000px) {
    .c-diff-view__changes {
      padding: var(--ds-spacing-1) 0 var(--ds-spacing-3) var(--ds-spacing-4);
    }
  }
  .c-diff-view__changes-item {
    margin-bottom: var(--ds-spacing-3);
  }

  /* Animation for loading states */
  @keyframes pulse {
    0% {
      opacity: 0.6;
    }
    50% {
      opacity: 0.3;
    }
    100% {
      opacity: 0.6;
    }
  }

  .c-diff-view__controls {
    display: flex;
    justify-content: flex-end;
  }

  .c-diff-view__controls__icon {
    color: var(--ds-color-neutral-7);
    transform: scale(0.9) translateY(2px);
  }

  .c-diff-view__applying {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-1);
  }

  .c-diff-view__applied {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-1);
  }

  /* Loading skeleton styles */
  .c-diff-view__skeleton-title {
    height: 1.3rem;
    width: 60%;
    background-color: var(--ds-color-neutral-3);
    border-radius: var(--ds-radius-1);
    animation: pulse 1.5s infinite ease-in-out;
  }

  .c-diff-view__skeleton-text {
    height: 0.9rem;
    width: 100%;
    background-color: var(--ds-color-neutral-3);
    border-radius: var(--ds-radius-1);
    margin-bottom: var(--ds-spacing-1);
    animation: pulse 1.5s infinite ease-in-out;
  }

  .c-diff-view__skeleton-text:first-child {
    width: 90%;
  }

  .c-diff-view__skeleton-text:last-child {
    width: 70%;
  }

  @keyframes pulse {
    0% {
      opacity: 0.6;
    }
    50% {
      opacity: 0.8;
    }
    100% {
      opacity: 0.6;
    }
  }
</style>
