<script lang="ts">
  import VSCodeCodicon from "$common-webviews/src/common/components/vscode/VSCodeCodicon.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import LanguageIcon from "$common-webviews/src/common/components/language-icons/LanguageIcon.svelte";
  import { getFocusedPathContext } from "./context";

  // Svelte components are automatically exported

  export let node: {
    name: string;
    path: string;
    isFile: boolean;
    children: Map<string, any>;
    isExpanded: boolean;
    displayName?: string; // For showing the full path in collapsed folders
  };
  export let indentLevel: number = 0;

  // Get the focused path context
  const focusedPath = getFocusedPathContext();

  function toggleNode() {
    if (!node.isFile) {
      // Toggle folder expansion
      node.isExpanded = !node.isExpanded;
    } else {
      // For files, just set the focused path
      focusedPath.set(node.path);
    }
  }
</script>

<div class="tree-node">
  <div
    class="tree-node__content"
    class:selected={node.path === $focusedPath}
    class:collapsed-folder={node.displayName && !node.isFile}
    on:click={toggleNode}
    role="treeitem"
    tabindex="0"
    on:keydown={(e) => e.key === "Enter" && toggleNode()}
    aria-selected={node.path === $focusedPath}
    aria-expanded={!node.isFile ? node.isExpanded : undefined}
  >
    <div class="tree-node__indent" style="width: {indentLevel * 6}px"></div>

    <div class="tree-node__icon-container">
      {#if !node.isFile}
        <VSCodeCodicon icon={node.isExpanded ? "chevron-down" : "chevron-right"} />
      {:else}
        <LanguageIcon filename={node.name} />
      {/if}
    </div>

    <span
      class="tree-node__label"
      class:full-path={node.displayName}
      title={node.displayName || node.name}
    >
      <TextAugment size={1}>
        <!-- Format the display name to show the full path -->
        <span class="full-path-text">{node.displayName || node.name}</span>
      </TextAugment>
    </span>
  </div>

  {#if !node.isFile && node.isExpanded && node.children.size > 0}
    <div class="tree-node__children" role="group">
      {#each Array.from(node.children.values()).sort( (a, b) => (a.isFile === b.isFile ? a.name.localeCompare(b.name) : a.isFile ? 1 : -1), ) as childNode}
        <svelte:self node={childNode} indentLevel={indentLevel + 1} />
      {/each}
    </div>
  {/if}
</div>

<style>
  .tree-node {
    width: 100%;
  }

  .tree-node__content {
    display: flex;
    align-items: center;
    padding: var(--ds-spacing-1) 0;
    border-radius: var(--ds-border-radius-1);
    cursor: pointer;
    height: 24px;
  }

  .tree-node__content:hover {
    background: var(--ds-color-neutral-3);
  }

  @keyframes pulse {
    0% {
      background: var(--ds-color-accent-3);
      color: var(--ds-color-accent-11);
    }
    50% {
      background: var(--ds-color-accent-4);
      color: var(--ds-color-accent-11);
    }
    100% {
      background: none;
      color: inherit;
    }
  }
  .tree-node__content.selected {
    animation: pulse 1.5s ease-in-out;
  }

  .tree-node__indent {
    flex-shrink: 0;
  }

  .tree-node__icon-container {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 16px;
    height: 16px;
    margin-right: 4px;
    flex-shrink: 0;
  }

  .tree-node__label {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .tree-node__label.full-path {
    font-style: italic;
    color: var(--ds-color-neutral-10);
  }

  .full-path-text {
    display: inline-block;
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .tree-node__content.collapsed-folder {
    background-color: var(--ds-color-neutral-2);
  }

  .tree-node__content.collapsed-folder:hover {
    background-color: var(--ds-color-neutral-3);
  }

  .tree-node__children {
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }
</style>
