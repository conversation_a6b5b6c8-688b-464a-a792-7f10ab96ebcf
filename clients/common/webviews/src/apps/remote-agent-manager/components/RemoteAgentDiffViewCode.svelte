<script lang="ts">
  import CollapsibleAugment from "$common-webviews/src/design-system/components/CollapsibleAugment/CollapsibleAugment.svelte";
  import CollapseButtonAugment from "$common-webviews/src/design-system/components/CollapsibleAugment/CollapseButtonAugment.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import { themeStore } from "$common-webviews/src/common/hosts/user-themes/theme-store";
  import { getMonacoTheme } from "$common-webviews/src/common/utils/monaco-theme";
  import type {
    Diff,
    DiffExplanationSubSectionDescription,
  } from "@augment-internal/sidecar-libs/src/remote-agent/remote-agent-types";
  import MonacoDiff from "./MonacoDiff.svelte";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import MaterialIcon from "$common-webviews/src/common/components/MaterialIcon.svelte";
  import Play from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/play-augment.svg?component";
  import {
    getFileDirectory,
    getFilename,
    normalizeFilePath,
  } from "$common-webviews/src/common/utils/file-paths";
  import {
    getDiffStats,
    isNewFile as checkIsNewFile,
    isDeletedFile as checkIsDeletedFile,
  } from "../utils/diff";
  import {
    isImageFile as isImage,
    isKnownBinary,
    isTooLargeForDiff,
    getMimeType,
    MAX_FILE_SIZE_FOR_DIFF_BYTES,
  } from "$common-webviews/src/design-system/components/RichTextEditorAugment/plugins/File/file-type-utils";
  import { TooltipTriggerOn } from "$common-webviews/src/design-system/_primitives/TooltipAugment/types";
  import { getFocusedPathContext, getFocusedPathId } from "./context";
  import { getContext, onMount } from "svelte";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import OpenInNewWindow from "$common-webviews/src/design-system/icons/open-in-new-window.svelte";
  import { RemoteAgentDiffOpsModel } from "../models/ra-diff-ops-model";

  export let path: string;
  export let change: Diff;
  export let descriptions: DiffExplanationSubSectionDescription[] = [];
  export let areDescriptionsVisible: boolean = true;
  export let isExpandedDefault: boolean;
  export let isCollapsed: boolean = !isExpandedDefault;
  export let isApplying: boolean;
  export let hasApplied: boolean;
  export let onApplyChanges: (() => void) | undefined = undefined;
  export let onCodeChange: ((modifiedCode: string) => void) | undefined = undefined;
  export let onOpenFile: (() => Promise<boolean>) | undefined = undefined;
  export let isAgentFromDifferentRepo: boolean = false;

  const focusedPath = getFocusedPathContext();

  const diffOpsModel = getContext<RemoteAgentDiffOpsModel>(RemoteAgentDiffOpsModel.key);

  // Track the current modified code
  let currentModifiedCode = change.modifiedCode;
  $: currentModifiedCode = change.modifiedCode;

  // Handle code changes from the Monaco editor
  function handleCodeChange(event: CustomEvent<{ modifiedCode: string }>) {
    currentModifiedCode = event.detail.modifiedCode;
    // Notify parent component about the code change
    onCodeChange?.(currentModifiedCode);
  }

  // Apply changes with the current modified code
  function applyCurrentChanges() {
    // Report event
    diffOpsModel.reportApplyChangesEvent();
    // Update the change object with the current modified code
    change.modifiedCode = currentModifiedCode;
    // Ensure parent has the latest code
    onCodeChange?.(currentModifiedCode);
    // Call the parent's apply changes function
    onApplyChanges?.();
  }

  // Use the utility functions to get diff stats and check if it's a new file
  $: diffStats = getDiffStats(change.diff);
  $: additions = diffStats.additions;
  $: deletions = diffStats.deletions;

  // Determine if this is a new file or deleted file
  $: isNewFile = checkIsNewFile(change);
  $: isDeletedFile = checkIsDeletedFile(change);

  // File type and size checks
  $: isImageFile = isImage(path);
  $: mimeType = getMimeType(path);
  $: isBinaryFile = isKnownBinary(path); // Excludes images

  $: originalContentLength = change.originalCode?.length || 0;
  $: modifiedContentLength = currentModifiedCode?.length || 0;

  $: isOriginalTooLarge = isTooLargeForDiff(originalContentLength);
  $: isModifiedTooLarge = isTooLargeForDiff(modifiedContentLength);

  // Determine file status for display logic
  $: isFileActuallyDeleted = !currentModifiedCode && !!change.originalCode; // Current code is empty, but original had content
  $: isFileActuallyAdded = !!currentModifiedCode && !change.originalCode; // Current code has content, original was empty

  // Conditions for showing special messages
  $: showAsImage = isImageFile;
  $: showAsBinaryMessage = !isImageFile && isBinaryFile;
  $: showAsTooLargeMessage =
    !isImageFile &&
    !isBinaryFile &&
    (isModifiedTooLarge ||
      (isFileActuallyDeleted && isOriginalTooLarge) ||
      (isFileActuallyAdded && isModifiedTooLarge));

  $: monacoTheme = getMonacoTheme($themeStore?.category, $themeStore?.intensity);

  $: normalizedPath = normalizeFilePath(path);

  $: isApplyButtonDisabled = isApplying || isAgentFromDifferentRepo;
  $: applyButtonTooltip = (() => {
    if (isApplying) {
      return "Applying changes...";
    } else if (hasApplied) {
      return "Reapply changes to local file";
    } else if (isAgentFromDifferentRepo) {
      return "Cannot apply changes from a different repository locally";
    } else {
      return "Apply changes to local file";
    }
  })();

  let openFileTooltip = normalizedPath;

  function resetOpenFileTooltip() {
    openFileTooltip = `Open ${normalizedPath ?? "file"}`;
  }

  async function handleOpenFile() {
    if (onOpenFile) {
      openFileTooltip = "Opening file...";

      const success = await onOpenFile();
      if (!success) {
        openFileTooltip = "Failed to open file. Does the file exist?";
        setTimeout(() => {
          resetOpenFileTooltip();
        }, 2000);
      } else {
        resetOpenFileTooltip();
      }
    }
  }

  onMount(() => {
    resetOpenFileTooltip();
  });
</script>

<div class="c" id={getFocusedPathId(path)} class:focused={$focusedPath === path}>
  <CollapsibleAugment bind:collapsed={isCollapsed} stickyHeader>
    <div slot="header" class="header">
      <CollapseButtonAugment />

      <div class="c-path">
        <TextTooltipAugment
          content={openFileTooltip}
          triggerOn={[TooltipTriggerOn.Hover]}
          delayDurationMs={300}
        >
          <ButtonAugment
            variant="ghost-block"
            color="neutral"
            size={1}
            class="c-codeblock__filename"
            on:click={handleOpenFile}
          >
            {getFilename(normalizedPath)}
          </ButtonAugment>
        </TextTooltipAugment>
        {#if getFileDirectory(normalizedPath)}
          <span class="c-directory">
            {getFileDirectory(normalizedPath)}
          </span>
        {/if}
      </div>

      {#if isNewFile}
        <span class="new-file-badge">New File</span>
      {:else}
        <div class="changes-indicator">
          {#if additions > 0}
            <span class="additions">
              <TextAugment size={1}>+{additions}</TextAugment>
            </span>
          {/if}
          {#if deletions > 0}
            <span class="deletions">
              <TextAugment size={1}>-{deletions}</TextAugment>
            </span>
          {/if}
        </div>
      {/if}

      <TextTooltipAugment
        content={applyButtonTooltip}
        triggerOn={[TooltipTriggerOn.Hover]}
        delayDurationMs={300}
      >
        <ButtonAugment
          variant="ghost-block"
          color="neutral"
          size={2}
          on:click={applyCurrentChanges}
          disabled={isApplyButtonDisabled}
        >
          {#if hasApplied}
            Applied
          {:else}
            Apply
          {/if}

          {#if hasApplied}
            <div class="applied">
              <MaterialIcon iconName="check" />
            </div>
          {:else}
            <div class="applied__icon">
              <Play />
            </div>
          {/if}
        </ButtonAugment>
      </TextTooltipAugment>
      {#if hasApplied}
        <TextTooltipAugment
          content={openFileTooltip}
          triggerOn={[TooltipTriggerOn.Hover]}
          delayDurationMs={300}
        >
          <IconButtonAugment size={1} variant="ghost" color="neutral" on:click={handleOpenFile}>
            <OpenInNewWindow />
          </IconButtonAugment>
        </TextTooltipAugment>
      {/if}
    </div>
    <div class="changes">
      {#if showAsImage}
        <div class="image-container">
          {#if isFileActuallyDeleted}
            <TextAugment class="image-info-text">
              Image deleted: {getFilename(normalizedPath)}
            </TextAugment>
            {#if change.originalCode}
              <TextAugment class="image-info-text">Previous version:</TextAugment>
              <img
                src="data:{getMimeType(path)};base64,{btoa(change.originalCode)}"
                alt="Original {getFilename(normalizedPath)}"
                class="image-preview"
              />
            {/if}
          {:else if currentModifiedCode}
            <TextAugment class="image-info-text">
              {#if isNewFile || isFileActuallyAdded}New image added{:else}Image modified{/if}: {getFilename(
                normalizedPath,
              )}
            </TextAugment>
            <img
              src="data:{mimeType};base64,{btoa(currentModifiedCode)}"
              alt="Current {getFilename(normalizedPath)}"
              class="image-preview"
            />
            {#if change.originalCode && currentModifiedCode !== change.originalCode && !isNewFile}
              <TextAugment class="image-info-text">Previous version:</TextAugment>
              <img
                src="data:{getMimeType(path)};base64,{btoa(change.originalCode)}"
                alt="Original {getFilename(normalizedPath)}"
                class="image-preview image-preview--previous"
              />
            {/if}
          {/if}
        </div>
      {:else if showAsBinaryMessage}
        <div class="binary-file-message">
          <TextAugment>
            {#if isNewFile || isFileActuallyAdded}
              Binary file added: {getFilename(normalizedPath)}.
            {:else if isFileActuallyDeleted}
              Binary file deleted: {getFilename(normalizedPath)}.
            {:else}
              Binary file modified: {getFilename(normalizedPath)}.
            {/if}
            No text preview available.
          </TextAugment>
        </div>
      {:else if showAsTooLargeMessage}
        <div class="too-large-message">
          <TextAugment size={1}>
            File "{getFilename(normalizedPath)}" is too large to display a diff (size: {isFileActuallyDeleted
              ? originalContentLength
              : modifiedContentLength} bytes, max: {MAX_FILE_SIZE_FOR_DIFF_BYTES} bytes).
          </TextAugment>
        </div>
      {:else}
        <MonacoDiff
          {path}
          originalCode={change.originalCode}
          modifiedCode={currentModifiedCode}
          theme={monacoTheme}
          {descriptions}
          bind:areDescriptionsVisible
          {isNewFile}
          {isDeletedFile}
          on:codeChange={handleCodeChange}
        />
      {/if}
    </div>
  </CollapsibleAugment>
</div>

<style>
  .c {
    width: 100%;
    border: 1px solid transparent;
    border-radius: var(--ds-radius-2);
  }

  .c :global(.c-collapsible) {
    --collapsible-panel-border: 1px solid var(--ds-color-neutral-5);
  }

  .header {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-2);
    padding: var(--ds-spacing-1) var(--ds-spacing-2) var(--ds-spacing-1) var(--ds-spacing-2);
    width: 100%;
  }

  .c-path {
    display: flex;
    flex-direction: row;
    align-items: center;
    min-width: 0;
    width: 100%;
    flex: 1;
  }

  .c-path :global(.l-tooltip-trigger) {
    max-width: 100%;
  }

  .c-path:has(.c-directory) :global(.l-tooltip-trigger) {
    max-width: 65%;
  }

  .c-path :global(.c-codeblock__filename) {
    width: 100%;
    color: var(--ds-color-neutral-12);
  }

  .c-path :global(.c-codeblock__filename .c-button--content) {
    display: block;
    overflow: hidden;
    text-wrap: nowrap;
    text-overflow: ellipsis;
  }

  .c-directory {
    color: var(--ds-color-neutral-10);
    overflow: hidden;
    text-wrap: nowrap;
    text-overflow: ellipsis;
  }

  .changes-indicator {
    display: flex;
    gap: var(--ds-spacing-2);
    font-family: var(--augment-monospace-font-family);
    margin-left: auto;
  }

  .additions {
    color: var(--ds-color-success-9);
  }

  .deletions {
    color: var(--ds-color-error-9);
  }

  .changes {
    padding: var(--ds-spacing-2) var(--ds-spacing-4);
  }

  .image-container {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-2);
    align-items: flex-start;
  }

  .image-preview {
    max-width: 100%;
    max-height: 500px; /* Adjust as needed */
    border: 1px solid var(--ds-color-neutral-5);
    border-radius: var(--ds-border-radius-1);
  }

  .image-preview--previous {
    opacity: 0.7;
    border-style: dashed;
  }

  .c :global(.image-info-text),
  .binary-file-message,
  .too-large-message {
    padding: var(--ds-spacing-2);
    border-radius: var(--ds-border-radius-1);
    width: 100%;
    box-sizing: border-box;
    color: var(--ds-color-neutral-11);
  }

  .applied {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-1);
    margin-left: var(--ds-spacing-2);
  }

  .applied__icon {
    color: var(--ds-color-neutral-7);
    transform: scale(0.9) translateY(2px);
  }

  .new-file-badge {
    display: inline-block;
    background-color: var(--ds-color-success-5);
    color: var(--ds-color-success-11);
    font-size: 0.75rem;
    font-weight: 500;
    padding: 2px 6px;
    border-radius: 4px;
    margin-left: 8px;
  }

  @keyframes highlight-pulse {
    0% {
      border-color: var(--ds-color-accent-6);
    }
    50% {
      border-color: var(--ds-color-accent-5);
    }
    100% {
      border-color: transparent;
    }
  }

  .c.focused {
    animation: highlight-pulse 1.5s ease-in-out;
  }

  .c.focused {
    position: relative;
    z-index: 1;
  }
</style>
