<script lang="ts" generics="T">
  import { type SetupScriptStaticOption } from "../types";

  import SpinnerAugment from "$common-webviews/src/design-system/components/SpinnerAugment.svelte";
  import DropdownMenuAugment from "$common-webviews/src/design-system/components/DropdownMenuAugment/index.js";
  import ChevronDown from "$common-webviews/src/design-system/icons/chevron-down.svelte";
  import { createEventDispatcher } from "svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";

  export let title: string = "";
  export let placeholder: string = "Search...";
  export let isLoading: boolean = false;
  export let disabled: boolean = false;
  export let searchValue: string = "";
  export let items: T[] = [];
  export let selectedItem: T | null = null;
  export let actions: SetupScriptStaticOption[] = [];
  export let actionLabelFn: (item: SetupScriptStaticOption) => string = (item) =>
    item?.toString() || "";
  export let itemTitle: string | undefined = undefined;
  export let itemLabelFn: (item: T) => string = (item) => item?.toString() || "";
  export let itemKeyFn: (item: T, index: number) => string = (item) => item?.toString() || "";
  export let isItemSelected: ((item: T, selectedItem: T | null) => boolean) | undefined = undefined;
  export let noItemsLabel: string = "No items found";
  export let loadingLabel: string = "Loading...";

  let isOpen = false;
  let requestClose: () => void = () => {};

  const dispatch = createEventDispatcher<{
    openChange: boolean;
    search: string;
    select: T;
    action: SetupScriptStaticOption["type"];
  }>();

  function handleOpenChange(open: boolean) {
    if (disabled) {
      return;
    }

    isOpen = open;

    if (open && selectedItem) {
      const selectedLabel = itemLabelFn(selectedItem);
      searchValue = selectedLabel;
      // Dispatch empty search to show all items initially
      dispatch("search", "");
      setTimeout(() => {
        const input = document.querySelector(
          ".c-searchable-dropdown__trigger-input",
        ) as HTMLInputElement;
        if (input) {
          input.select();
        }
      }, 0);
    } else if (open) {
      // Clear search value when opening without a selected item
      searchValue = "";
      dispatch("search", "");
    }

    dispatch("openChange", open);
  }

  function handleSearchChange(value: string) {
    searchValue = value;
    dispatch("search", value);
  }

  function handleSelect(item: T) {
    selectedItem = item;
    dispatch("select", item);
    requestClose();
  }

  function handleActionSelect(action: SetupScriptStaticOption) {
    dispatch("action", action.type);
    requestClose();
  }
</script>

<div class="c-searchable-dropdown">
  <DropdownMenuAugment.Root bind:requestClose onOpenChange={handleOpenChange}>
    <DropdownMenuAugment.Trigger>
      {#if isOpen}
        <div class="c-searchable-dropdown__input-container">
          <div class="c-searchable-dropdown__icon">
            <slot name="searchIcon">
              <slot name="icon"></slot>
            </slot>
          </div>
          <input
            type="text"
            class="c-searchable-dropdown__trigger-input"
            {placeholder}
            bind:value={searchValue}
            on:input={(e) => handleSearchChange(e.currentTarget.value)}
            on:click|stopPropagation
            on:mousedown|stopPropagation
          />
          {#if $$slots.inputButton}
            <slot name="inputButton" />
          {/if}
        </div>
      {:else}
        <div
          class="c-searchable-dropdown__button"
          class:c-searchable-dropdown__button--disabled={disabled}
          role="button"
          tabindex={disabled ? -1 : 0}
        >
          <div class="c-searchable-dropdown__icon-text">
            <div class="c-searchable-dropdown__icon">
              {#if isLoading}
                <SpinnerAugment size={1} useCurrentColor={true} />
              {:else}
                <slot name="icon"></slot>
              {/if}
            </div>
            <span class="c-searchable-dropdown__button-text">
              {#if $$slots.title}
                <slot name="title"></slot>
              {:else}
                {isLoading ? loadingLabel : title}
              {/if}
            </span>
          </div>
          <div class="c-searchable-dropdown__chevron">
            <ChevronDown />
          </div>
        </div>
      {/if}
    </DropdownMenuAugment.Trigger>
    {#if !disabled}
      <DropdownMenuAugment.Content side="top" align="start">
        {#if actions.length > 0}
          {#each actions as action (action.name)}
            <DropdownMenuAugment.Item
              onSelect={() => handleActionSelect(action)}
              disabled={action.disabled}
            >
              <slot name="action" {action}>
                {actionLabelFn(action)}
              </slot>
            </DropdownMenuAugment.Item>
          {/each}
          <div class="c-searchable-dropdown__separator">
            <DropdownMenuAugment.Separator />
          </div>
        {/if}
        {#if isLoading}
          <DropdownMenuAugment.Item disabled>
            <div class="c-searchable-dropdown__loading">
              <SpinnerAugment size={1} useCurrentColor={true} />
              <span>{loadingLabel}</span>
            </div>
          </DropdownMenuAugment.Item>
        {:else if items.length > 0}
          {#if itemTitle}
            <div class="c-searchable-dropdown__item-title">
              <TextAugment size={1} class="c-searchable-dropdown__item-title"
                >{itemTitle}</TextAugment
              >
            </div>
          {/if}
          {#each items as item, index (item === null ? `null-item-${index}` : itemKeyFn(item, index))}
            <DropdownMenuAugment.Item
              onSelect={() => handleSelect(item)}
              highlight={isItemSelected
                ? isItemSelected(item, selectedItem)
                : selectedItem
                  ? itemLabelFn(selectedItem) === itemLabelFn(item)
                  : false}
            >
              <slot name="item" {item}>
                {itemLabelFn(item)}
              </slot>
            </DropdownMenuAugment.Item>
          {/each}
        {:else if noItemsLabel}
          <DropdownMenuAugment.Item disabled>
            {noItemsLabel}
          </DropdownMenuAugment.Item>
        {/if}
        {#if $$slots.footer}
          <slot name="footer"></slot>
        {/if}
        <div style="margin-bottom: var(--ds-spacing-2);"></div>
      </DropdownMenuAugment.Content>
    {/if}
  </DropdownMenuAugment.Root>
</div>

<style>
  .c-searchable-dropdown {
    width: 100%;
    display: block;
  }

  .c-searchable-dropdown :global(.l-tooltip-trigger) {
    width: 100% !important;
  }

  .c-searchable-dropdown :global(.l-tooltip-trigger > div) {
    width: 100% !important;
  }

  .c-searchable-dropdown__input-container {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-2);
    padding: 0 var(--ds-spacing-2);
    border: 1px solid var(--augment-border-color);
    border-radius: 6px;
    background-color: var(--vscode-input-background);
    width: 100%;
    min-height: 1.5rem;
    box-sizing: border-box;
  }

  .c-searchable-dropdown__button {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 0 var(--ds-spacing-2);
    min-height: 1.5rem;
    box-sizing: border-box;
    border: 1px solid var(--augment-border-color);
    border-radius: 6px;
    background-color: var(--vscode-input-background);
    cursor: pointer;
    user-select: none;
    transition: border-color 0.2s ease;
    color: var(--vscode-input-foreground);
  }

  .c-searchable-dropdown__button:hover:not(.c-searchable-dropdown__button--disabled) {
    border-color: var(--vscode-checkbox-selectBorder);
  }

  .c-searchable-dropdown__button:focus-visible {
    outline: none;
    border-color: var(--ds-color-accent-7);
  }

  .c-searchable-dropdown__button--disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .c-searchable-dropdown__icon-text {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: var(--ds-spacing-2);
    flex: 1;
    min-width: 0;
    overflow: hidden;
  }

  .c-searchable-dropdown__button-text {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: var(--ds-font-size-1);
    min-width: 0;
    color: var(--ds-color-neutral-12);
  }

  .c-searchable-dropdown__icon {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
  }

  .c-searchable-dropdown__chevron {
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--ds-color-neutral-11);
    flex-shrink: 0;
    margin-left: var(--ds-spacing-2);
  }

  .c-searchable-dropdown__trigger-input {
    flex: 1;
    border: none;
    background: transparent;
    font-size: var(--ds-font-size-1);
    font-family: inherit;
    font-weight: inherit;
    color: var(--ds-color-neutral-12);
    width: 100%;
    height: 100%;
    padding: 0;
    margin: 0;
    border-radius: var(--ds-border-radius-1);
    box-sizing: border-box;
    line-height: normal;
    appearance: none;
    -webkit-appearance: none;
  }

  .c-searchable-dropdown__trigger-input:focus,
  .c-searchable-dropdown__trigger-input:focus-visible {
    outline: none;
    box-shadow: none;
    border: none;
    border-radius: var(--ds-border-radius-1);
  }

  .c-searchable-dropdown__input-container:focus-within {
    border-color: var(--ds-color-accent-9);
    outline: none;
  }

  .c-searchable-dropdown__loading {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-1);
  }

  /* Ensure searchable dropdown content appears above other elements */
  .c-searchable-dropdown :global([data-tippy-root]) {
    z-index: var(--z-dropdown-menu);
  }

  .c-searchable-dropdown :global(.tippy-box) {
    z-index: var(--z-dropdown-menu);
  }

  .c-searchable-dropdown__item-title {
    color: var(--ds-color-neutral-11);
  }

  .c-searchable-dropdown__separator {
    margin-top: var(--ds-spacing-2);
  }
</style>
