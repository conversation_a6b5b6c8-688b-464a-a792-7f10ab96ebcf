<script lang="ts">
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import type { ChangedFile } from "@augment-internal/sidecar-libs/src/remote-agent/remote-agent-types";
  import { slide } from "svelte/transition";
  import { calculateFileChanges } from "./calculate-file-changes";
  import { DiffOperations } from "../utils/diff";
  import RemoteAgentDiffViewCode from "./RemoteAgentDiffViewCode.svelte";
  import Play from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/play-augment.svg?component";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import TreeViewChanges from "./TreeViewChanges.svelte";

  export let changedFiles: ChangedFile[];
  export let onApplyChanges: (path: string, originalCode: string, newCode: string) => void;
  export let onOpenFile: ((path: string) => Promise<boolean>) | undefined = undefined;
  export let pendingFiles: string[] = [];
  export let appliedFiles: string[] = [];
  export let isLoadingTreeView: boolean = false;

  // Track modified code for each file
  let modifiedCodeMap: Record<string, string> = {};
  let isApplyingAll = false;
  let hasAppliedAll = false;

  /**
   * Handle code changes from a file
   */
  function handleCodeChange(path: string, modifiedCode: string) {
    // Store the modified code in our map
    modifiedCodeMap[path] = modifiedCode;
  }

  /**
   * Apply changes to all files
   */
  function applyAllChanges() {
    if (!onApplyChanges) return;

    // Get all unique file paths
    const allPaths = filesWithEditsToShow.map((file) => file.qualifiedPathName.relPath);

    // Check if all paths are already applied
    const areAllPathsApplied = allPaths.every((path) => appliedFiles.includes(path));
    if (areAllPathsApplied) {
      hasAppliedAll = true;
      return; // Exit early if all files are already applied
    }

    const filesToApply = allPaths.filter(
      (path) => !appliedFiles.includes(path) && !pendingFiles.includes(path),
    );

    if (filesToApply.length === 0) {
      return;
    }

    // Set applying state
    isApplyingAll = true;
    hasAppliedAll = false;

    // Apply each change
    filesToApply.forEach((path) => {
      // Find the file in filesWithEditsToShow
      const file = filesWithEditsToShow.find((f) => f.qualifiedPathName.relPath === path);
      if (file) {
        // Use the stored modified code if available, otherwise use the original
        const codeToApply = modifiedCodeMap[path] || file.newContents;
        onApplyChanges(path, file.oldContents, codeToApply);
      }
    });
  }

  // Track changes to files and applied files
  $: changedFilesString = JSON.stringify(changedFiles);
  $: appliedFilesString = JSON.stringify(appliedFiles);
  $: pendingFilesString = JSON.stringify(pendingFiles);

  // Reset state when files change
  $: if (changedFilesString) {
    modifiedCodeMap = {};
    isApplyingAll = false;
    hasAppliedAll = false;
  }

  // Computed property to check if there are any files to apply
  $: hasFilesToApply = (() => {
    // Re-compute when files, appliedFiles, or pendingFiles change
    if (changedFilesString && appliedFilesString && pendingFilesString) {
      const allPaths = filesWithEditsToShow.map((file) => file.qualifiedPathName.relPath);
      if (allPaths.length === 0) return false;

      // Check if there are any files that haven't been applied yet
      return allPaths.some((path) => !appliedFiles.includes(path) && !pendingFiles.includes(path));
    }
    return false;
  })();

  // Check if all files have been applied
  $: {
    if (filesWithEditsToShow.length > 0 && !isApplyingAll && appliedFilesString) {
      const allPaths = filesWithEditsToShow.map((file) => file.qualifiedPathName.relPath);

      // If there are no files to apply, don't update the state
      if (allPaths.length > 0) {
        // Check if all files have been applied
        const allApplied = allPaths.every((path) => appliedFiles.includes(path));

        // Update hasAppliedAll if all files are applied
        if (allApplied && appliedFiles.length > 0) {
          hasAppliedAll = true;
        } else if (!allApplied && hasAppliedAll) {
          // Reset if some files are no longer applied
          hasAppliedAll = false;
        }
      }
    }
  }

  // Update apply states when pending or applied files change during Apply All
  $: if (isApplyingAll) {
    // Get all unique file paths
    const allPaths = filesWithEditsToShow.map((file) => file.qualifiedPathName.relPath);

    // Check if all files that were being applied are now applied
    const filesToApply = allPaths.filter(
      (path) => !appliedFiles.includes(path) && !pendingFiles.includes(path),
    );

    if (filesToApply.length === 0) {
      // All files are either applied or pending
      const allPathsCovered = allPaths.every(
        (path) => appliedFiles.includes(path) || pendingFiles.includes(path),
      );

      if (allPathsCovered) {
        // Check if all pending files are now applied
        const allFullyApplied = pendingFiles.length === 0;

        if (allFullyApplied && appliedFiles.length > 0) {
          isApplyingAll = false;
          hasAppliedAll = true;
        }
      }
    }
  }

  $: filesWithEditsToShow = changedFiles.map((file) => {
    const path = file.new_path || file.old_path;
    const oldContents = file.old_contents || "";
    const newContents = file.new_contents || "";

    // Generate a diff using our utility function
    const diff = DiffOperations.generateDiff(
      file.old_path,
      file.new_path,
      oldContents,
      newContents,
    );

    // For backward compatibility, also calculate line changes
    const lineChanges = calculateFileChanges(oldContents, newContents);

    // Initialize the modified code map with the original modified code
    if (!modifiedCodeMap[path]) {
      modifiedCodeMap[path] = newContents;
    }

    return {
      qualifiedPathName: {
        rootPath: "",
        relPath: path,
      },
      lineChanges,
      oldContents,
      newContents,
      diff,
    };
  });
</script>

<div class="c-edits-list-container">
  <div class="c-file-explorer__layout">
    <div class="c-file-explorer__tree">
      <div class="c-file-explorer__tree__header">
        <TextAugment size={1} class="c-file-explorer__tree__header__label"
          >Changed files</TextAugment
        >
        <TreeViewChanges {changedFiles} isLoading={isLoadingTreeView} />
      </div>
    </div>
    <div class="c-file-explorer__details">
      {#if filesWithEditsToShow.length > 0}
        <div class="c-edits-list-header">
          <div class="c-edits-list-controls">
            {#if filesWithEditsToShow.length > 0}
              <ButtonAugment
                variant="ghost-block"
                color="neutral"
                size={2}
                on:click={applyAllChanges}
                disabled={isApplyingAll ||
                  hasAppliedAll ||
                  pendingFiles.length > 0 ||
                  !hasFilesToApply}
              >
                {#if isApplyingAll}
                  Applying...
                {:else if hasAppliedAll}
                  All applied
                {:else}
                  Apply all
                {/if}
                <div class="c-edits-list-controls__icon">
                  <Play />
                </div>
              </ButtonAugment>
            {/if}
          </div>
        </div>
        <div class="c-edits-list">
          <!-- Single file changes section -->
          <div class="c-edits-section">
            {#each filesWithEditsToShow as file (file.qualifiedPathName.relPath)}
              <div class="" transition:slide>
                <RemoteAgentDiffViewCode
                  path={file.qualifiedPathName.relPath}
                  change={file.diff}
                  isApplying={pendingFiles.includes(file.qualifiedPathName.relPath)}
                  hasApplied={appliedFiles.includes(file.qualifiedPathName.relPath)}
                  onCodeChange={(modifiedCode) => {
                    // Store the modified code in our map
                    handleCodeChange(file.qualifiedPathName.relPath, modifiedCode);
                  }}
                  onApplyChanges={() => {
                    // Use the stored modified code if available, otherwise use the original
                    const codeToApply =
                      modifiedCodeMap[file.qualifiedPathName.relPath] || file.newContents;
                    onApplyChanges(file.qualifiedPathName.relPath, file.oldContents, codeToApply);
                  }}
                  onOpenFile={onOpenFile
                    ? () => onOpenFile(file.qualifiedPathName.relPath)
                    : undefined}
                  isExpandedDefault
                />
              </div>
            {/each}
          </div>
        </div>
      {:else}
        <div class="c-edits-list c-edits-list--empty">
          <TextAugment size={1} color="neutral">No changes to show</TextAugment>
        </div>
      {/if}
    </div>
  </div>
</div>

<style>
  .c-file-explorer__layout {
    display: grid;
    grid-template-columns: 200px 1fr; /* Default for wider screens */
    gap: var(--ds-spacing-5);
    height: 100%;
    overflow: hidden; /* Prevent scrollbars on layout itself */
  }

  .c-file-explorer__tree {
    padding-top: var(--ds-spacing-2);
    position: sticky;
    top: 0;
    height: fit-content; /* Or max-height: 100vh; if it should scroll independently */
    overflow-y: auto; /* Allow scrolling for tree if it overflows */
  }

  .c-file-explorer__tree__header {
    display: flex;
    flex-direction: column;
    padding: var(--ds-spacing-1) var(--ds-spacing-2); /* Add some horizontal padding */
    width: 100%;
  }

  .c-file-explorer__details {
    min-width: 0;
    overflow-y: auto; /* Allow scrolling for details panel */
    height: 100%;
    padding: 0 var(--ds-spacing-2); /* Add some padding */
  }

  /* Responsive adjustments */
  @media (max-width: 1300px) {
    .c-file-explorer__layout {
      grid-template-columns: 150px 1fr;
    }
  }

  @media (max-width: 1000px) {
    .c-edits-list-container {
      /* Targeting root for padding adjustment */
      padding: var(--ds-spacing-2) 0;
    }
    .c-file-explorer__layout {
      grid-template-columns: 100px 1fr;
    }
  }

  @media (max-width: 900px) {
    .c-file-explorer__layout {
      grid-template-columns: 1fr; /* Stack tree and details */
    }
    .c-file-explorer__tree {
      position: relative; /* Reset sticky positioning */
      border-right: none;
      border-bottom: 1px solid var(--ds-color-neutral-3);
      max-height: 30vh; /* Limit height when stacked */
    }
    .c-file-explorer__details {
      padding: var(--ds-spacing-2); /* Add padding when stacked */
    }
  }

  .c-edits-list-container {
    height: 100%;
    overflow: scroll;
  }

  .c-edits-list {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-2);
    padding: var(--ds-spacing-1);
  }

  .c-edits-list--empty {
    opacity: 50%;

    display: flex;
    justify-content: center;
    align-items: center;
    padding: var(--ds-spacing-3);

    /* Disable selection */
    user-select: none;
  }

  .c-edits-list-header {
    padding: var(--ds-spacing-2);
  }

  .c-edits-list-controls {
    display: flex;
    justify-content: flex-end;
    gap: var(--ds-spacing-2);
  }

  .c-edits-list-controls__icon {
    color: var(--ds-color-neutral-7);
    transform: scale(0.9) translateY(2px);
  }

  .c-edits-section {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-3);
  }
</style>
