<script lang="ts">
  import TextFieldAugment from "$common-webviews/src/design-system/components/TextFieldAugment.svelte";
  import { createEventDispatcher } from "svelte";

  export let name: string;
  export let path: string;
  export let isPath: boolean = false;
  export let isRenaming: boolean = false;
  export let isSelected: boolean = false;
  export let showPath: boolean = true;

  const dispatch = createEventDispatcher<{
    rename: { oldName: string; newName: string };
    cancelRename: void;
  }>();

  function splitNameAndExtension(filename: string) {
    const lastDotIndex = filename.lastIndexOf(".");
    if (lastDotIndex === -1) {
      return { baseName: filename, extension: "" };
    }
    return {
      baseName: filename.substring(0, lastDotIndex),
      extension: filename.substring(lastDotIndex),
    };
  }

  const { baseName, extension } = splitNameAndExtension(name);
  let editableBaseName = baseName;
  let textInput: HTMLInputElement | undefined = undefined;

  // Focus the text input when entering rename mode
  $: if (isRenaming && textInput) {
    setTimeout(() => {
      textInput?.focus();
      // Select all text for easy editing
      textInput?.select();
    }, 0);
  }

  function handleKeyDown(event: KeyboardEvent) {
    // Allow arrow keys to work normally in the text field
    if (
      event.key === "ArrowLeft" ||
      event.key === "ArrowRight" ||
      event.key === "ArrowUp" ||
      event.key === "ArrowDown"
    ) {
      // Stop propagation to prevent the dropdown menu from handling these keys
      event.stopPropagation();
      return;
    }

    if (event.key === "Enter") {
      event.preventDefault();
      if (editableBaseName.trim() && editableBaseName !== baseName) {
        const newName = editableBaseName.trim() + extension;
        dispatch("rename", { oldName: name, newName });
      } else {
        // If name is empty or unchanged, just cancel
        dispatch("cancelRename");
      }
    } else if (event.key === "Escape") {
      event.preventDefault();
      event.stopPropagation();
      dispatch("cancelRename");
    }
  }

  function handleBlur() {
    dispatch("cancelRename");
  }
</script>

<div
  class="c-setup-script-selector__script-item-content"
  class:c-setup-script-selector__script-item-content--renaming={isRenaming}
  class:c-setup-script-selector__script-item-content--is-path={isPath}
  class:c-setup-script-selector__script-item-content--selected={isSelected}
  role="presentation"
>
  {#if $$slots.icon}
    <div class="c-setup-script-selector__icon">
      <slot name="icon"></slot>
    </div>
  {/if}
  <div class="c-setup-script-selector__script-info">
    {#if isRenaming}
      <div
        class="c-setup-script-selector__rename-input"
        on:click|stopPropagation
        on:mousedown|stopPropagation
        role="presentation"
      >
        <div class="c-setup-script-selector__rename-input-container" role="presentation">
          <TextFieldAugment
            bind:value={editableBaseName}
            bind:textInput
            size={1}
            variant="surface"
            on:keydown={handleKeyDown}
            on:blur={handleBlur}
          />
          {#if extension}
            <span class="c-setup-script-selector__extension">{extension}</span>
          {/if}
        </div>
      </div>
    {:else}
      <span class="c-setup-script-selector__script-name">
        {name}
      </span>
      {#if showPath}
        <span class="c-setup-script-selector__script-path">
          {path}
        </span>
      {/if}
    {/if}
  </div>
  <div class="c-setup-script-selector__script-actions">
    <slot />
  </div>
</div>

<style>
  .c-setup-script-selector__script-item-content {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    min-width: 50px;
    padding: 0 var(--ds-spacing-2);
  }

  .c-setup-script-selector__script-item-content--selected {
    background-color: var(--gray-a4);
  }

  /* When selected, use the same styling as dropdown menu items */
  :global(.c-dropdown-menu-augment__item.c-dropdown-menu-augment__item--highlighted)
    .c-setup-script-selector__script-item-content,
  :global(.c-dropdown-menu-augment__item:hover) .c-setup-script-selector__script-item-content {
    background-color: var(--gray-a4);
  }

  /* Fix text colors to match dropdown menu items */
  :global(.c-dropdown-menu-augment__item:hover) .c-setup-script-selector__script-path,
  :global(.c-dropdown-menu-augment__item.c-dropdown-menu-augment__item--highlighted)
    .c-setup-script-selector__script-path {
    color: inherit;
  }

  .c-setup-script-selector__icon {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: var(--ds-spacing-3);
    margin-left: 1px;
    align-self: flex-start;
  }

  .c-setup-script-selector__script-info {
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow: hidden;
  }

  .c-setup-script-selector__rename-input {
    width: 100%;
    padding-right: var(--ds-spacing-2);
    position: relative;
    z-index: 10;
  }

  .c-setup-script-selector__rename-input-container {
    display: flex;
    align-items: center;
    position: relative;
  }

  .c-setup-script-selector__extension {
    color: var(--ds-color-neutral-9);
    font-size: 0.9em;
    margin-left: 2px;
    user-select: none;
    pointer-events: none;
    display: flex;
    align-items: center;
    height: 100%;
    padding: 0 2px;
  }

  .c-setup-script-selector__script-item-content--renaming {
    pointer-events: auto;
  }

  .c-setup-script-selector__script-item-content--is-path .c-setup-script-selector__script-name {
    font-weight: 400;
  }
  .c-setup-script-selector__script-item-content--is-path .c-setup-script-selector__script-path {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  .c-setup-script-selector__script-actions {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--ds-spacing-1);
  }

  .c-setup-script-selector__script-name {
    white-space: nowrap;
    overflow: hidden;
    font-size: 0.83rem;
    text-overflow: ellipsis;
    font-weight: 500;
  }

  .c-setup-script-selector__script-path {
    font-size: 0.85em;
    margin-top: 2px;
    color: var(--ds-color-neutral-9);
  }

  :global(.c-dropdown-menu-augment__item[disabled] .c-setup-script-selector__script-path) {
    color: inherit;
  }

  /* Fix for dropdown item hover state */
  :global(.c-dropdown-menu-augment__item:hover .c-setup-script-selector__script-path),
  :global(.c-dropdown-menu-augment__item--highlighted .c-setup-script-selector__extension),
  :global(.c-dropdown-menu-augment__item:hover .c-setup-script-selector__extension),
  :global(.c-dropdown-menu-augment__item:hover .c-setup-script-selector__rename-input) {
    color: inherit;
  }
</style>
