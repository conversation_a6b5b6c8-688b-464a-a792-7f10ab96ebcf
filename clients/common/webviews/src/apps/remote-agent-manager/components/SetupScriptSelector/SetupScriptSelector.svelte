<script lang="ts">
  import { type ChatModel } from "$common-webviews/src/apps/chat/models/chat-model";
  // Import design system components
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import CalloutAugment from "$common-webviews/src/design-system/components/CalloutAugment.svelte";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import Terminal from "$common-webviews/src/design-system/icons/augment/terminal.svelte";
  import RegularArrowUpRightFromSquareIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/arrow-up-right-from-square.svg?component";
  import RegularPenIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/pen.svg?component";
  import RegularTrashIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/trash.svg?component";
  import MagicWand from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/wand-magic-sparkles.svg?component";
  import Edit from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/pen-to-square.svg?component";
  import Reload from "$common-webviews/src/design-system/icons/reload.svelte";
  import MagnifyingGlass from "$common-webviews/src/design-system/icons/magnifying-glass.svelte";
  import {
    type SetupScript,
    type SetupScriptLocation,
    type SetupScriptTypes,
  } from "$vscode/src/utils/remote-agent-setup/types";
  import type { SetupScriptStaticOption } from "../../types";
  import {
    createEventDispatcher,
    getContext,
    onDestroy,
    onMount,
    type ComponentType,
  } from "svelte";
  import { RemoteAgentsModel } from "../../models/remote-agents-model";
  import { RemoteAgentSetupWindowAction } from "@augment-internal/sidecar-libs/src/metrics/types";
  import ScriptItem from "./ScriptItem.svelte";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import SearchableDropdown from "../SearchableDropdown.svelte";
  import TextCombo from "$common-webviews/src/common/components/TextCombo.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";

  export let errorMessage = "";
  export let isLoading = false;
  export let lastUsedScriptPath: string | null = null;
  export let disableNewAgentCreation = false;

  const remoteAgentsModel = getContext<RemoteAgentsModel>(RemoteAgentsModel.key);
  const dispatch = createEventDispatcher<{
    setupScriptChange: { script: SetupScript | null };
  }>();

  const chatModel = getContext<ChatModel>("chatModel");
  const extensionClient = chatModel.extensionClient;
  const openFile = (script: { path: string; location: SetupScriptLocation }) => {
    extensionClient.openFile({
      repoRoot: "",
      pathName: script.path,
      allowOutOfWorkspace: true,
      openLocalUri: script.location === "home",
    });
  };

  // Creates a manual setup script template in the home directory
  const createManualScript = async () => {
    // Log manually create setup script event
    try {
      await remoteAgentsModel.reportRemoteAgentSetupWindowEvent(
        RemoteAgentSetupWindowAction.manuallyCreateSetupScript,
      );
    } catch (error) {
      console.error("Failed to report manually create setup script event:", error);
    }
    try {
      // Default script name
      const scriptName = "setup.sh";

      // Template content with explanatory comments
      const templateContent = `#!/bin/bash

# Setup Script for Remote Agent Environment
#
# This script installs dependencies and configures the environment for your project.
# It runs with sudo privileges when needed.
#
# Examples:
# sudo apt-get update && sudo apt-get install -y package-name
# pip install package-name
# npm install -g package-name
# export ENV_VAR=value

# Add your commands below:

`;

      // Save the script to the home directory
      const result = await remoteAgentsModel.saveSetupScript(scriptName, templateContent, "home");

      if (result.success && result.path) {
        // Reload the scripts list
        await loadScripts();

        // Find the newly created script
        const newScript = scripts.find((script) => script.path === result.path);
        if (newScript) {
          // Select the new script
          selectOption(newScript);

          // Open the file in the editor
          openFile(newScript);
        }
      } else {
        console.error("Failed to create manual setup script:", result.error);
        errorMessage = `Failed to create manual setup script: ${result.error || "Unknown error"}`;
      }
    } catch (error) {
      console.error("Error creating manual setup script:", error);
      errorMessage = `Error creating manual setup script: ${error instanceof Error ? error.message : String(error)}`;
    }
  };

  let scripts: SetupScript[] = [];
  let searchValue = "";
  let scriptBeingRenamed: SetupScript | null = null;
  let displayedScripts: SetupScript[] = scripts;
  let isInitialLoad = true;
  let selectedAction: SetupScriptStaticOption["type"] | null = null;

  // Make selectedScript reactive to changes in the draft
  $: selectedScript = $remoteAgentsModel.newAgentDraft?.setupScript ?? null;

  $: hasError = errorMessage !== "";

  let staticActions: Record<
    SetupScriptTypes,
    { name: string; icon: ComponentType; disabled?: boolean }
  > = {
    basic: {
      name: "Use basic environment",
      // SVELTE5_MIGRATION - AU-11867
      icon: Terminal as unknown as ComponentType,
    },
    auto: {
      name: "Auto-generate setup script",
      // SVELTE5_MIGRATION - AU-11867
      icon: MagicWand as unknown as ComponentType,
      disabled: disableNewAgentCreation,
    },
    manual: {
      name: "Write a script by hand",
      icon: Edit,
    },
  };

  $: staticActionsList = Object.entries(staticActions).map(([key, action]) => ({
    name: action.name,
    type: key as SetupScriptTypes,
    icon: action.icon,
    disabled: action.disabled,
  })) as SetupScriptStaticOption[];

  // Filter scripts based on search
  $: {
    if (searchValue.trim() !== "") {
      // // Filter scripts based on search
      const filteredScripts = scripts.filter(
        (script) =>
          script.name.toLowerCase().includes(searchValue.toLowerCase()) ||
          script.path.toLowerCase().includes(searchValue.toLowerCase()),
      );

      // Add the "No Setup" option at the beginning if it matches the search
      displayedScripts = filteredScripts;
    } else {
      displayedScripts = scripts;
    }
  }

  const errors = {
    noScriptsFound:
      "No setup scripts found. You can create one in ~/.augment/env/, <git root>/.augment/env/, or <workspace root>/.augment/env/.",
    failedToFetchScripts: "Failed to fetch setup scripts. Please try again.",
  };

  onMount(async () => {
    await loadScripts();
    // Only use the draft script if we don't have a lastUsedScriptPath that was found
    if (lastUsedScriptPath === null) {
      // If lastUsedScriptPath is explicitly null, select "No Setup"
      selectOption(null);
    } else if ($remoteAgentsModel.newAgentDraft?.setupScript && !selectedScript) {
      selectOption($remoteAgentsModel.newAgentDraft.setupScript);
    }
  });

  onDestroy(() => {
    remoteAgentsModel.setGenerateSetupScriptSelected(false);
  });

  async function loadScripts() {
    errorMessage = "";
    try {
      const previouslySelectedPath = selectedScript?.path;
      scripts = await remoteAgentsModel.listSetupScripts();

      // Only set selectedScript during initial load or if it's not already set
      if (isInitialLoad) {
        // If we have a lastUsedScriptPath and it's not null, try to find that script
        if (lastUsedScriptPath && scripts.length > 0) {
          const savedScript = scripts.find((script) => script.path === lastUsedScriptPath);
          if (savedScript) {
            selectedScript = savedScript;
            selectedAction = null;
            updateSelectedScript();
          }
        } else if (lastUsedScriptPath === null) {
          // If lastUsedScriptPath is explicitly null, select "No Setup"
          selectedScript = null;
          updateSelectedScript();
        }
      } else if (previouslySelectedPath) {
        // If we already had a selection, make sure it references the updated script object
        const currentScript = scripts.find((script) => script.path === previouslySelectedPath);
        if (currentScript) {
          selectedScript = currentScript;
          selectedAction = null;
        }
      }

      isInitialLoad = false;

      // Check for error conditions
      if (scripts.length === 0) {
        errorMessage = errors.noScriptsFound;
      } else {
        errorMessage = "";
      }
    } catch (error) {
      console.error("Error fetching setup scripts:", error);
      errorMessage = errors.failedToFetchScripts;
    }
  }

  async function startDeleteScript(script: SetupScript, event?: MouseEvent) {
    if (event) {
      event.stopPropagation();
    }

    try {
      const result = await remoteAgentsModel.deleteSetupScript(script.name, script.location);
      if (result.success) {
        // If the deleted script was selected, reset selection
        if (selectedScript?.path === script.path) {
          selectOption(null);
        }
        // Reload scripts list
        await loadScripts();
      } else {
        console.error("Failed to delete script:", result.error);
        setError(`Failed to delete script: ${result.error || "Unknown error"}`);
      }
    } catch (error) {
      console.error("Error deleting script:", error);
      setError(`Error deleting script: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  async function startRenameScript(script: SetupScript, event?: MouseEvent) {
    if (event) {
      event.stopPropagation();
    }
    scriptBeingRenamed = script;
  }

  async function handleRename(
    script: SetupScript,
    event: CustomEvent<{ oldName: string; newName: string }>,
  ) {
    const { oldName, newName } = event.detail;

    try {
      const result = await remoteAgentsModel.renameSetupScript(oldName, newName, script.location);
      if (result.success) {
        // Always reload scripts and select the renamed script
        await loadScripts();
        const updatedScript = scripts.find((s) => s.path === result.path);
        if (updatedScript) {
          // Select the script but don't close the dropdown
          selectOption(updatedScript);
        }
      } else {
        console.error("Failed to rename script:", result.error);
        setError(`Failed to rename script: ${result.error || "Unknown error"}`);
      }
    } catch (error) {
      console.error("Error renaming script:", error);
      setError(`Error renaming script: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      cancelRename();
    }
  }

  function cancelRename() {
    scriptBeingRenamed = null;
  }

  function setError(message: string) {
    errorMessage = message;
  }

  function handleSearchChange(value: string) {
    searchValue = value;
  }

  function handleSelect(script: SetupScript | null) {
    selectOption(script);
  }

  function handleAction(actionType: SetupScriptStaticOption["type"]) {
    selectedAction = actionType;
    selectedScript = null;
    if (actionType === "basic") {
      selectOption(null);
    } else if (actionType === "auto") {
      // Set the generateSetupScriptSelected flag to true
      remoteAgentsModel.setGenerateSetupScriptSelected(true);
      return;
    } else if (actionType === "manual") {
      createManualScript();
    }
    remoteAgentsModel.setGenerateSetupScriptSelected(false);
  }

  function handleOpenChange(open: boolean) {
    if (open) {
      loadScripts();
      searchValue = "";
    }
  }

  async function selectOption(script: SetupScript | null) {
    selectedScript = script;
    updateSelectedScript();
    remoteAgentsModel.saveLastRemoteAgentSetup(null, null, selectedScript?.path || null);

    // Log setup script selection event (only if a script is actually selected)
    if (script) {
      try {
        await remoteAgentsModel.reportRemoteAgentSetupWindowEvent(
          RemoteAgentSetupWindowAction.selectSetupScript,
        );
      } catch (error) {
        console.error("Failed to report setup script selection event:", error);
      }
    }
  }

  function updateSelectedScript() {
    dispatch("setupScriptChange", {
      script: selectedScript,
    });
  }

  $: getTitle = () => {
    if (isLoading) {
      return "...";
    }
    if (selectedScript) {
      // Special case for the generate option
      if (selectedScript.isGenerateOption) {
        return selectedScript.name;
      }

      // Use the location property directly
      switch (selectedScript.location) {
        case "home":
          return "~/.augment/env/" + selectedScript.name;
        case "git":
        case "workspace":
        default:
          return selectedScript.path;
      }
    }
    if (selectedAction) {
      return staticActions[selectedAction].name;
    }
    return "Use basic environment";
  };

  $: title = getTitle();
  $: isTitlePath = !!selectedScript?.path;
  $: titleFilename = isTitlePath ? title.split("/").pop()! : title;
  $: titleBasePath = isTitlePath ? title.slice(0, title.lastIndexOf("/")) : "";

  // Helper function to safely get the icon for an action
  function getActionIcon(action: SetupScriptStaticOption) {
    return staticActions[action.type].icon;
  }
</script>

<div class="c-setup-script-selector">
  <div class="c-setup-script-selector__content">
    <!-- Show script selector only when there's no error or when there's a "no scripts found" error -->
    {#if !hasError || errorMessage === errors.noScriptsFound}
      <div class="c-setup-script-selector__script-line-container">
        <div class="c-setup-script-selector__script-line">
          <SearchableDropdown
            placeholder="Search scripts..."
            {isLoading}
            disabled={false}
            bind:searchValue
            actions={staticActionsList}
            actionLabelFn={(action) => action?.name || ""}
            items={displayedScripts}
            selectedItem={selectedScript}
            itemTitle="Available scripts"
            itemLabelFn={(script) => script?.name || ""}
            itemKeyFn={(script) => `${script?.path}-${script?.location}-${script?.name}`}
            isItemSelected={(item, selectedItem) => {
              // Both null means "Use basic environment" is selected
              if (item === null && selectedItem === null) return true;
              // For regular scripts, compare by path
              if (item && selectedItem) return item.path === selectedItem.path;
              return false;
            }}
            noItemsLabel="No scripts found"
            loadingLabel="Loading scripts..."
            on:openChange={(e) => handleOpenChange(e.detail)}
            on:search={(e) => handleSearchChange(e.detail)}
            on:select={(e) => handleSelect(e.detail)}
            on:action={(e) => handleAction(e.detail)}
          >
            <div slot="title">
              {#if isTitlePath}
                <TextCombo>
                  <span slot="text">{titleFilename}</span>
                  <span slot="grayText">{titleBasePath}</span>
                </TextCombo>
              {:else}
                <TextCombo>
                  <span slot="text">{title}</span>
                </TextCombo>
              {/if}
            </div>
            <Terminal slot="icon" />
            <MagnifyingGlass slot="searchIcon" />

            <svelte:fragment slot="action" let:action>
              {#if action}
                <div class="c-action-selector">
                  <svelte:component this={getActionIcon(action)} />
                  <TextAugment size={1}>{action.name}</TextAugment>
                </div>
              {/if}
            </svelte:fragment>

            <svelte:fragment slot="item" let:item>
              {#if item}
                <ScriptItem
                  name={item.name}
                  path={item.path}
                  isPath
                  showPath={false}
                  isRenaming={scriptBeingRenamed?.path === item.path}
                  isSelected={!!(selectedScript && selectedScript.path === item.path)}
                  on:rename={(e) => handleRename(item, e)}
                  on:cancelRename={cancelRename}
                >
                  <TextTooltipAugment content="Open script in editor" nested={false}>
                    <IconButtonAugment
                      size={1}
                      variant="ghost-block"
                      class="c-setup-script-selector__action-button"
                      on:click={(e) => {
                        e.stopPropagation();
                        openFile(item);
                        selectOption(item);
                      }}
                    >
                      <RegularArrowUpRightFromSquareIcon />
                    </IconButtonAugment>
                  </TextTooltipAugment>

                  <TextTooltipAugment content="Rename script" nested={false}>
                    <IconButtonAugment
                      size={1}
                      variant="ghost-block"
                      class="c-setup-script-selector__action-button"
                      on:click={(e) => {
                        e.stopPropagation();
                        startRenameScript(item);
                      }}
                    >
                      <RegularPenIcon />
                    </IconButtonAugment>
                  </TextTooltipAugment>

                  <TextTooltipAugment content="Delete script" nested={false}>
                    <IconButtonAugment
                      size={1}
                      variant="ghost-block"
                      class="c-setup-script-selector__action-button"
                      on:click={(e) => {
                        e.stopPropagation();
                        startDeleteScript(item);
                      }}
                    >
                      <RegularTrashIcon />
                    </IconButtonAugment>
                  </TextTooltipAugment>
                </ScriptItem>
              {/if}
            </svelte:fragment>
          </SearchableDropdown>
        </div>
      </div>
    {/if}
    {#if hasError && errorMessage !== errors.noScriptsFound}
      <!-- Show error callout for critical errors -->
      <div class="c-setup-script-selector__error">
        <CalloutAugment color="warning" variant="soft" size={2}>
          <div class="c-setup-script-selector__error-content">
            <div class="c-setup-script-selector__error-message">
              {errorMessage}
            </div>
            <ButtonAugment
              variant="ghost"
              color="warning"
              size={1}
              on:click={loadScripts}
              loading={isLoading}
            >
              <span slot="iconLeft"><Reload /></span>
              Refresh
            </ButtonAugment>
          </div>
        </CalloutAugment>
      </div>
    {/if}
  </div>
</div>

<style>
  .c-setup-script-selector {
    display: flex;
    width: 100%;
    flex-direction: column;
  }

  .c-setup-script-selector :global(button.c-base-btn.c-dropdown-menu-augment__item) {
    padding: 0;
  }

  .c-setup-script-selector__content {
    display: flex;
    flex-direction: column;
    width: 100%;
  }

  .c-setup-script-selector__error {
    max-width: 600px;
    margin-top: var(--ds-spacing-3);
  }

  :global(.c-setup-script-selector__create-option .c-dropdown-menu-augment__item) {
    color: var(--ds-color-accent-11);
    font-weight: 500;
    width: 100%;
  }

  .c-setup-script-selector__script-line-container {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    width: 100%;
    gap: var(--ds-spacing-2);
  }

  .c-setup-script-selector__script-line {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    width: 100%;
    gap: var(--ds-spacing-1);
  }

  .c-setup-script-selector :global(.c-dropdown-menu-augment__separator) {
    margin: 0 var(--ds-spacing-1) var(--ds-spacing-3);
  }

  .c-setup-script-selector__script-line
    :global(
      .c-card-button
        > .l-tooltip-trigger
        > [data-tippy-root]
        > .tippy-box
        > .tippy-content
        > .l-tooltip-contents
    ) {
    margin-left: calc(var(--remote-agent-setup-horizontal-padding) + var(--ds-spacing-1));
    width: 100%;
  }

  .c-setup-script-selector
    :global(.c-card-button > .l-tooltip-trigger > [data-tippy-root] > .tippy-box) {
    max-width: unset !important; /** Tippy sets a max-width of 350px by default on the element */
    width: calc(
      100vw - var(--remote-agent-setup-horizontal-padding) -
        var(--remote-agent-setup-horizontal-padding) - var(--ds-spacing-4) - var(--ds-spacing-1)
    );
    min-width: 250px;
  }

  @media (max-width: 400px) {
    .c-setup-script-selector__script-line :global(.l-tooltip-contents) {
      margin-left: 0;
    }
  }

  .c-setup-script-selector :global(.c-setup-script-selector__action-button svg) {
    width: 12px;
  }

  /* Fix button color in highlighted dropdown items */
  :global(.c-dropdown-menu-augment__item--highlighted .c-setup-script-selector__action-button):not(
      :hover
    ),
  :global(.c-dropdown-menu-augment__item:hover .c-setup-script-selector__action-button):not(
      :hover
    ) {
    color: inherit;
  }

  :global(.c-dropdown-menu-augment__item) {
    width: 100%;
  }

  .c-setup-script-selector__error-content {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-3);
  }

  .c-setup-script-selector__error-message {
    margin-bottom: var(--ds-spacing-1);
  }

  .c-setup-script-selector :global(.c-searchable-dropdown) {
    width: 100%;
  }

  .c-setup-script-selector :global(.l-dropdown-menu-augment__contents) {
    max-height: 300px;
    min-height: 40px;
    overflow-y: auto;
  }

  /* prevent nested scrollbars */
  .c-setup-script-selector :global(.c-searchable-dropdown__content) {
    max-height: none;
  }

  .c-action-selector {
    display: flex;
    flex-direction: row;
    gap: var(--ds-spacing-2);
    width: 100%;
    min-width: 50px;
    padding: var(--ds-spacing-1) var(--ds-spacing-2);
    gap: var(--ds-spacing-2);
  }

  .c-setup-script-selector :global(.l-dropdown-menu-augment__contents) {
    max-height: 300px;
    min-height: 40px;
    overflow-y: auto;
  }

  .c-setup-script-selector :global(.c-card-button),
  .c-setup-script-selector :global(.c-searchable-dropdown) {
    max-width: 100%;
    width: 100%;
  }

  .c-setup-script-selector :global(.l-tooltip-contents) {
    margin-left: var(--remote-agent-setup-horizontal-padding);
    width: calc(100% - var(--remote-agent-setup-horizontal-padding));
  }

  .c-setup-script-selector :global(.tippy-box) {
    max-width: unset !important;
    width: auto;
    max-width: calc(
      100vw - var(--remote-agent-setup-horizontal-padding) -
        var(--remote-agent-setup-horizontal-padding) - var(--ds-spacing-4) - var(--ds-spacing-1)
    );
    min-width: 250px;
  }

  @media (max-width: 400px) {
    .c-setup-script-selector :global(.l-tooltip-contents) {
      margin-left: 0;
    }
  }
</style>
