import { render, fireEvent, waitFor } from "@testing-library/svelte";
import { vi, describe, it, expect, beforeEach } from "vitest";
import SetupScriptSelector from "./SetupScriptSelector.svelte";
import type { SetupScript } from "$vscode/src/utils/remote-agent-setup/types";
import { RemoteAgentsModel } from "../../models/remote-agents-model";

// Create a mock that implements the Readable interface like RemoteAgentsModel
const createMockRemoteAgentsModel = () => {
  const mockModel = {
    listSetupScripts: vi.fn(),
    saveLastRemoteAgentSetup: vi.fn(),
    reportRemoteAgentSetupWindowEvent: vi.fn(),
    setGenerateSetupScriptSelected: vi.fn(),
    newAgentDraft: null,
    subscribe: vi.fn((callback) => {
      callback(mockModel);
      return () => {};
    }),
  };
  return mockModel;
};

const mockChatModel = {
  extensionClient: {
    openFile: vi.fn(),
  },
} as any;

const mockScripts: SetupScript[] = [
  {
    name: "setup.sh",
    path: "/home/<USER>/.augment/env/setup.sh",
    content: "#!/bin/bash\necho 'setup'",
    location: "home",
  },
  {
    name: "project-setup.sh",
    path: "/workspace/.augment/env/project-setup.sh",
    content: "#!/bin/bash\necho 'project setup'",
    location: "workspace",
  },
  {
    name: "git-setup.sh",
    path: "/repo/.augment/env/git-setup.sh",
    content: "#!/bin/bash\necho 'git setup'",
    location: "git",
  },
];

describe("SetupScriptSelector displayedScripts filtering and option states", () => {
  let mockRemoteAgentsModel: any;

  beforeEach(() => {
    vi.clearAllMocks();
    mockRemoteAgentsModel = createMockRemoteAgentsModel();
    mockRemoteAgentsModel.listSetupScripts.mockResolvedValue(mockScripts);
    mockRemoteAgentsModel.newAgentDraft = null;
  });

  const renderComponent = (disableNewAgentCreation = false) => {
    return render(SetupScriptSelector, {
      props: { disableNewAgentCreation },
      context: new Map([
        [RemoteAgentsModel.key, mockRemoteAgentsModel],
        ["chatModel", mockChatModel],
      ]),
    });
  };

  const openDropdown = async (container: HTMLElement) => {
    await waitFor(() => {
      expect(mockRemoteAgentsModel.listSetupScripts).toHaveBeenCalled();
    });
    const dropdownTrigger = container.querySelector('[role="button"]')!;
    await fireEvent.click(dropdownTrigger);
  };

  const findButtonByText = (container: HTMLElement, text: string) => {
    const buttons = container.querySelectorAll("button");
    return Array.from(buttons).find((button) => button.textContent?.includes(text));
  };

  it("should display all scripts when no search filter is applied", async () => {
    const { container, getByText } = renderComponent();
    await openDropdown(container);

    await waitFor(() => {
      expect(getByText("setup.sh")).toBeInTheDocument();
      expect(getByText("project-setup.sh")).toBeInTheDocument();
      expect(getByText("git-setup.sh")).toBeInTheDocument();
    });
  });

  it("should filter displayedScripts based on search value", async () => {
    const { container, getByPlaceholderText, getByText, queryByText } = renderComponent();
    await openDropdown(container);

    const searchInput = getByPlaceholderText("Search scripts...");

    // Test filtering by script name
    await fireEvent.input(searchInput, { target: { value: "project" } });
    await waitFor(() => {
      expect(getByText("project-setup.sh")).toBeInTheDocument();
      expect(queryByText("setup.sh")).not.toBeInTheDocument();
      expect(queryByText("git-setup.sh")).not.toBeInTheDocument();
    });

    // Test case-insensitive filtering
    await fireEvent.input(searchInput, { target: { value: "SETUP" } });
    await waitFor(() => {
      expect(getByText("setup.sh")).toBeInTheDocument();
      expect(getByText("project-setup.sh")).toBeInTheDocument();
      expect(getByText("git-setup.sh")).toBeInTheDocument();
    });

    // Test no matches
    await fireEvent.input(searchInput, { target: { value: "nonexistent" } });
    await waitFor(() => {
      expect(queryByText("setup.sh")).not.toBeInTheDocument();
      expect(queryByText("project-setup.sh")).not.toBeInTheDocument();
      expect(queryByText("git-setup.sh")).not.toBeInTheDocument();
      expect(getByText("No scripts found")).toBeInTheDocument();
    });
  });

  it("should disable auto-generate option when disableNewAgentCreation is true", async () => {
    const { container } = renderComponent(true);
    await openDropdown(container);

    await waitFor(() => {
      const autoGenerateButton = findButtonByText(container, "Auto-generate setup script");
      expect(autoGenerateButton).toBeTruthy();
      expect(autoGenerateButton).toBeDisabled();
    });
  });

  it("should enable auto-generate option when disableNewAgentCreation is false", async () => {
    const { container } = renderComponent(false);
    await openDropdown(container);

    await waitFor(() => {
      const autoGenerateButton = findButtonByText(container, "Auto-generate setup script");
      expect(autoGenerateButton).toBeTruthy();
      expect(autoGenerateButton).not.toBeDisabled();
    });
  });

  it("should always enable basic and manual options regardless of disableNewAgentCreation", async () => {
    const { container } = renderComponent(true);
    await openDropdown(container);

    await waitFor(() => {
      const basicButton = findButtonByText(container, "Use basic environment");
      const manualButton = findButtonByText(container, "Write a script by hand");

      expect(basicButton).toBeTruthy();
      expect(manualButton).toBeTruthy();
      expect(basicButton).not.toBeDisabled();
      expect(manualButton).not.toBeDisabled();
    });
  });

  it("should reset displayedScripts to all scripts when search is cleared", async () => {
    const { container, getByPlaceholderText, getByText } = renderComponent();
    await openDropdown(container);

    const searchInput = getByPlaceholderText("Search scripts...");

    // Apply a filter then clear it
    await fireEvent.input(searchInput, { target: { value: "project" } });
    await fireEvent.input(searchInput, { target: { value: "" } });

    await waitFor(() => {
      expect(getByText("setup.sh")).toBeInTheDocument();
      expect(getByText("project-setup.sh")).toBeInTheDocument();
      expect(getByText("git-setup.sh")).toBeInTheDocument();
    });
  });
});
