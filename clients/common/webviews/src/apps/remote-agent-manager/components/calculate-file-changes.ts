import type { ChatAgentFileChanges } from "@augment-internal/sidecar-libs/src/chat/chat-types";
import { type Change, createTwoFilesPatch, parsePatch } from "diff";

export function calculateFileChanges(
  oldContent: string,
  newContent: string,
): ChatAgentFileChanges & { diff: string } {
  // Create a unified diff
  const diffResult = createTwoFilesPatch("oldFile", "newFile", oldContent, newContent, "", "", {
    context: 3,
  });

  // Parse the diff to get detailed changes
  const patches = parsePatch(diffResult);

  let totalAddedLines = 0;
  let totalRemovedLines = 0;
  let changes: Change[] = [];

  // Calculate totals from the parsed diff
  for (const patch of patches) {
    for (const hunk of patch.hunks) {
      for (const line of hunk.lines) {
        const isAdded = line.startsWith("+");
        const isRemoved = line.startsWith("-");
        if (isAdded) totalAddedLines++;
        if (isRemoved) totalRemovedLines++;

        // Store change information
        changes.push({
          value: line,
          added: isAdded,
          removed: isRemoved,
        });
      }
    }
  }

  return {
    totalAddedLines,
    totalRemovedLines,
    changes,
    diff: diffResult,
  };
}
