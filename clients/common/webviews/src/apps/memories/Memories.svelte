<script lang="ts">
  import { host } from "$common-webviews/src/common/hosts/host";
  import {
    type WebViewMessage,
    WebViewMessageType,
  } from "$vscode/src/webview-providers/webview-messages";
  import MemoriesMarkdownEditor from "./MemoriesMarkdownEditor.svelte";
  import { writable } from "svelte/store";
  import { onDestroy, onMount } from "svelte";
  import { MessageBroker } from "$common-webviews/src/common/utils/message-broker";
  const msgBroker = new MessageBroker(host);

  // Create stores for the editor content and path
  const editorContent = writable<string | null>(null);
  const editorPath = writable<string | null>(null);

  // Handle messages from the extension
  const memoriesConsumer = {
    handleMessageFromExtension(event: MessageEvent<WebViewMessage>) {
      const message = event.data;

      if (message && message.type === WebViewMessageType.loadFile) {
        if (message.data.content !== undefined) {
          // Strip beginning newlines from the text
          const trimmedText = message.data.content.replace(/^\n+/, "");
          editorContent.set(trimmedText);
        }
        if (message.data.pathName) {
          editorPath.set(message.data.pathName);
        }
      }
      return true;
    },
  };

  onMount(() => {
    // Set up message listener
    msgBroker.registerConsumer(memoriesConsumer);
    // Signal to the extension that the webview is ready to receive content
    host.postMessage({
      type: WebViewMessageType.memoriesLoaded,
    });
  });
  onDestroy(() => {
    msgBroker.dispose();
  });
</script>

<svelte:window on:message={msgBroker.onMessageFromExtension} />
<div class="c-memories-container">
  {#if $editorContent !== null && $editorPath !== null}
    <MemoriesMarkdownEditor text={$editorContent} path={$editorPath} />
  {:else}
    Loading memories...
  {/if}
</div>

<style>
  .c-memories-container {
    display: flex;
    flex-direction: column;
    padding: var(--ds-spacing-4);
    background: var(--ds-surface);
    width: 100%;
    height: 100vh;
    box-sizing: border-box;
    overflow: auto;
  }
</style>
