import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import { HostClientType } from "$common-webviews/src/common/hosts/host-types";
import { DEFAULT_STRINGS } from "./default-strings";
import { JETBRAINS_STRINGS } from "./jetbrains-strings";

describe("clientStrings", () => {
  // Import the actual module to test the exported instance
  let clientStringsModule;

  beforeEach(() => {
    // Clear module cache to ensure fresh import
    vi.resetModules();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it("should export a clientStrings instance with VSCode strings by default", async () => {
    // Mock getHost to return VSCode client type
    const getHostMock = vi.fn().mockReturnValue({
      clientType: HostClientType.vscode,
    });

    vi.doMock("$common-webviews/src/common/hosts/create-host", () => ({
      getHost: getHostMock,
    }));

    // Import the module
    clientStringsModule = await import("./client-strings");

    // Verify the exported instance
    expect(clientStringsModule.clientStrings).toBeDefined();
    expect(clientStringsModule.clientStrings.get("mcpDocsURL")).toBe(DEFAULT_STRINGS.mcpDocsURL);
    expect(getHostMock).toHaveBeenCalledTimes(1);
  });

  it("should use JetBrains strings when host is JetBrains", async () => {
    // Mock getHost to return JetBrains client type
    const getHostMock = vi.fn().mockReturnValue({
      clientType: HostClientType.jetbrains,
    });

    vi.doMock("$common-webviews/src/common/hosts/create-host", () => ({
      getHost: getHostMock,
    }));

    // Import the module
    clientStringsModule = await import("./client-strings");

    // Verify the exported instance uses JetBrains strings
    expect(clientStringsModule.clientStrings).toBeDefined();
    expect(clientStringsModule.clientStrings.get("mcpDocsURL")).toBe(JETBRAINS_STRINGS.mcpDocsURL);
    expect(getHostMock).toHaveBeenCalledTimes(1);
  });
});
