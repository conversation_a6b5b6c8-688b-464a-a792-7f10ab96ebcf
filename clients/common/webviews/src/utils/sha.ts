/**
 * Calculates the SHA-256 hash of the given data.
 * @param data - The input data as a Uint8Array to be hashed
 * @returns A Promise that resolves to a lowercase hexadecimal string representation of the SHA-256 hash
 */
export async function sha256(data: Uint8Array): Promise<string> {
  const hashBuffer = await crypto.subtle.digest("SHA-256", data);
  const hashArray = Array.from(new Uint8Array(hashBuffer));
  const hashHex = hashArray.map((b) => b.toString(16).padStart(2, "0")).join("");
  return hashHex;
}
