import {
  ChangeType,
  type IEditSuggestion,
  SuggestionState,
} from "$vscode/src/next-edit/next-edit-types";
import {
  getLineRange,
  getPath,
  indexOfSuggestion,
  atIndexSuggestion,
  areSameSuggestion,
  sortAndFilterSuggestions,
  sortSuggestions,
  nextSuggestion,
  previousSuggestion,
} from "$common-webviews/src/common/components/suggestion-tree/navigation-utils";
import { expect, test, describe } from "vitest";

const mockSuggestion = {
  lineRange: { start: 0, stop: 2 },
  qualifiedPathName: {
    rootPath: "/root",
    relPath: "file.ts",
  },
  result: { suggestionId: "123" },
  state: SuggestionState.fresh,
  changeType: ChangeType.modification,
} as unknown as IEditSuggestion;

describe("navigation-utils", () => {
  test("getLineRange", () => {
    expect(getLineRange(mockSuggestion)).toBe("1-3");
  });

  test("getPath", () => {
    expect(getPath(mockSuggestion, false)).toBe("file.ts");
    expect(getPath(mockSuggestion, true)).toBe("/root/file.ts");
  });

  test("indexOfSuggestion", () => {
    const map = new Map<string, IEditSuggestion[]>([
      makeSuggestionPair("123", "file1.ts"),
      makeSuggestionPair("456", "file2.ts"),
      makeSuggestionPair("567", "file3.ts"),
    ]);
    expect(indexOfSuggestion(map, makeSuggestion("123", "file1.ts"))).toBe(0);
  });

  test("atIndexSuggestion", () => {
    const first = makeSuggestionPair("123", "file1.ts");
    const second = makeSuggestionPair("456", "file2.ts");
    const map = new Map<string, IEditSuggestion[]>([first, second]);
    expect(atIndexSuggestion(map, 0)).toEqual(first[1][0]);
    expect(atIndexSuggestion(map, 1)).toEqual(second[1][0]);
    expect(atIndexSuggestion(map, 2)).toEqual(second[1][0]);
    expect(atIndexSuggestion(map, 3)).toEqual(second[1][0]);
    expect(atIndexSuggestion(map, -1)).toEqual(first[1][0]);
  });

  test("areSameSuggestion", () => {
    expect(areSameSuggestion(mockSuggestion, mockSuggestion)).toBe(true);
    expect(areSameSuggestion(mockSuggestion, makeSuggestion("555", "file1.ts"))).toBe(false);
    expect(areSameSuggestion(mockSuggestion, undefined)).toBe(false);
  });

  test("sortAndFilterSuggestions", () => {
    const suggestions: IEditSuggestion[] = [
      makeSuggestion("123", "file1.ts"),
      makeSuggestion("345", "file2.ts"),
      makeSuggestion("678", "file3.ts"),
    ];
    const result = sortAndFilterSuggestions(suggestions);
    expect(result).toHaveLength(3);
  });

  test("sortSuggestions", () => {
    const suggestions: IEditSuggestion[] = [
      makeSuggestion("123", "file1.ts"),
      makeSuggestion("345", "file2.ts"),
    ];
    const result = sortSuggestions(suggestions);
    expect(result).toHaveLength(2);
    expect(result[0][0]).toBe("file1.ts");
    expect(result[0][1]).toHaveLength(1);
    expect(result[1][0]).toBe("file2.ts");
  });

  test("nextSuggestion and previousSuggestion", () => {
    const suggestion = makeSuggestion("123", "file1.ts");
    const map = new Map<string, IEditSuggestion[]>([
      makeSuggestionPair("444", "file1.ts", suggestion),
      makeSuggestionPair("789", "file2.ts", makeSuggestion("444", "file2.ts")),
      makeSuggestionPair("323", "file3.ts", makeSuggestion("777", "file3.ts")),
    ]);

    const [nextIndex, nextSugg] = nextSuggestion(map, suggestion);
    expect(nextIndex).toBe(2);
    expect(nextSugg?.result.suggestionId).toBe("789");

    const [prevIndex, prevSugg] = previousSuggestion(map, suggestion);
    expect(prevIndex).toBe(0);

    expect(prevSugg?.result.suggestionId).toBe("444");
  });
});

const makeSuggestion = (suggestionId: string, relPath: string = `file-${suggestionId}.ts`) =>
  ({
    ...mockSuggestion,
    qualifiedPathName: { ...mockSuggestion.qualifiedPathName, relPath },
    result: {
      ...mockSuggestion.result,
      suggestionId,
    },
  }) as IEditSuggestion;

const makeSuggestionPair = (
  suggestionId: string,
  relPath: string,
  ...rest: IEditSuggestion[]
): [string, IEditSuggestion[]] => [
  relPath,
  [
    makeSuggestion(suggestionId, relPath),
    ...rest.map((v) => ({
      ...v,
      qualifiedPathName: { ...v.qualifiedPathName, relPath },
    })),
  ],
];
