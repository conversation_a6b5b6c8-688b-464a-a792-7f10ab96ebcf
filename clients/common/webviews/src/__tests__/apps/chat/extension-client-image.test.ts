import { describe, it, expect, vi, beforeEach } from "vitest";
import { ExtensionClient } from "../../../apps/chat/extension-client";
import { WebViewMessageType } from "$vscode/src/webview-providers/webview-messages";
import { readFileAsDataURL } from "$common-webviews/src/common/utils/file-base64";

describe("Image Handling", () => {
  let mockHost: any;
  let mockAsyncMsgSender: any;
  let mockFlags: any;
  let extensionClient: ExtensionClient;
  const imageData = new Uint8Array([1, 2, 3, 4, 5]); // Simple test data

  beforeEach(() => {
    mockHost = {
      postMessage: vi.fn(),
    };

    mockAsyncMsgSender = {
      send: vi.fn().mockImplementation((request: any) => {
        // Handle saveImage request
        if (request.type === WebViewMessageType.chatSaveImageRequest) {
          return Promise.resolve({
            data: request.data.filename,
          });
        }
        // Handle loadImage request
        if (request.type === WebViewMessageType.chatLoadImageRequest) {
          // Return the same data that was used in the test
          const data: string | undefined = btoa(String.fromCharCode(...imageData));
          return Promise.resolve({
            data,
          });
        }
        // Handle deleteImage request
        if (request.type === WebViewMessageType.chatDeleteImageRequest) {
          return Promise.resolve({
            data: undefined,
          });
        }
        // Default response for other requests
        return Promise.resolve({
          data: {
            sources: [{ id: "doc1", name: "Documentation 1" }],
          },
        });
      }),
      stream: vi.fn(),
    };

    mockFlags = {
      enableExternalSourcesInChat: true,
      enableAgentMode: true,
    };

    extensionClient = new ExtensionClient(mockHost, mockAsyncMsgSender, mockFlags);
  });

  describe("save and load image", () => {
    beforeEach(() => {
      // Mock the File.prototype.arrayBuffer method
      global.File.prototype.arrayBuffer = vi.fn().mockImplementation(function () {
        const data = imageData;
        return Promise.resolve(data.buffer);
      });
    });
    it("should properly construct and load an image file", async () => {
      // Setup: Create a realistic image file
      const filename = "test-image.png";
      const file = new File([imageData], filename, { type: "image/png" });

      // Save the image first
      await extensionClient.saveImage(file, filename);
      const expectedUrl = await readFileAsDataURL(file);

      // Load the image
      const dataUrl = await extensionClient.loadImage(filename);

      // Verify the result
      expect(dataUrl).toBe(expectedUrl);

      // Verify the async message sender was called with the correct parameters
      expect(mockAsyncMsgSender.send).toHaveBeenCalledWith(
        expect.objectContaining({
          type: WebViewMessageType.chatLoadImageRequest,
          data: filename,
        }),
        10000,
      );
    });

    it("should handle different image types correctly", async () => {
      // Test with JPEG
      const jpegFilename = "test-image.jpg";
      const jpegFile = new File([imageData], jpegFilename, { type: "image/jpeg" });

      // Save and load JPEG
      await extensionClient.saveImage(jpegFile, jpegFilename);
      const jpegDataUrl = await extensionClient.loadImage(jpegFilename);
      const expectedUrl = await readFileAsDataURL(jpegFile);
      expect(jpegDataUrl).toBe(expectedUrl);

      // Verify the async message sender was called with the correct parameters
      expect(mockAsyncMsgSender.send).toHaveBeenCalledWith(
        expect.objectContaining({
          type: WebViewMessageType.chatLoadImageRequest,
          data: jpegFilename,
        }),
        10000,
      );
    });
  });

  describe("deleteImage", () => {
    it("should send a delete request for the specified image", async () => {
      const filename = "to-delete.png";

      // Delete the image
      await extensionClient.deleteImage(filename);

      // Verify the async message sender was called with the correct parameters
      expect(mockAsyncMsgSender.send).toHaveBeenCalledWith(
        expect.objectContaining({
          type: WebViewMessageType.chatDeleteImageRequest,
          data: filename,
        }),
        10000,
      );
    });
  });
});
