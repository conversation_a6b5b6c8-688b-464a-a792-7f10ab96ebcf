import { describe, test, expect, beforeEach, vi } from "vitest";
import {
  MCPServerModel,
  MCPServerError,
} from "$common-webviews/src/apps/settings/models/mcp-server-model";
import {
  type MCPServerStdio,
  type MCPServerHttp,
} from "$vscode/src/webview-providers/webview-messages";

// Helper types for tests
type MCPServerStdioWithoutId = Omit<MCPServerStdio, "id">;
type MCPServerHttpWithoutId = Omit<MCPServerHttp, "id">;

describe("MCPServerModel", () => {
  let model: MCPServerModel;
  let mockHost: any;

  beforeEach(() => {
    // Create a mock host
    mockHost = {
      postMessage: vi.fn(),
      getState: vi.fn(),
      setState: vi.fn(),
    };

    // Create a new instance of the model with the mock host
    model = new MCPServerModel(mockHost);
  });

  describe("parseServerConfigFromJSON", () => {
    test("should parse an array of server configurations", () => {
      const jsonString = `[
        {
          "name": "Server 1",
          "command": "command1",
          "args": ["arg1", "arg2"]
        },
        {
          "name": "Server 2",
          "command": "command2",
          "args": ["arg3", "arg4"]
        }
      ]`;

      const result = MCPServerModel.parseServerConfigFromJSON(jsonString);

      expect(result).toHaveLength(2);
      expect(result[0].type).toBe("stdio");
      expect(result[0].name).toBe("Server 1");
      expect((result[0] as MCPServerStdioWithoutId).command).toBe("command1 arg1 arg2");
      expect((result[0] as MCPServerStdioWithoutId).arguments).toBe("");
      expect((result[0] as MCPServerStdioWithoutId).useShellInterpolation).toBe(true);

      expect(result[1].type).toBe("stdio");
      expect(result[1].name).toBe("Server 2");
      expect((result[1] as MCPServerStdioWithoutId).command).toBe("command2 arg3 arg4");
      expect((result[1] as MCPServerStdioWithoutId).arguments).toBe("");
      expect((result[1] as MCPServerStdioWithoutId).useShellInterpolation).toBe(true);
    });

    test("should parse an object with 'servers' property as an array", () => {
      const jsonString = `{
        "servers": [
          {
            "name": "Server 1",
            "command": "command1",
            "args": ["arg1", "arg2"]
          },
          {
            "name": "Server 2",
            "command": "command2",
            "args": ["arg3", "arg4"]
          }
        ]
      }`;

      const result = MCPServerModel.parseServerConfigFromJSON(jsonString);

      expect(result).toHaveLength(2);
      expect(result[0].type).toBe("stdio");
      expect(result[0].name).toBe("Server 1");
      expect((result[0] as MCPServerStdioWithoutId).command).toBe("command1 arg1 arg2");
      expect(result[1].type).toBe("stdio");
      expect(result[1].name).toBe("Server 2");
      expect((result[1] as MCPServerStdioWithoutId).command).toBe("command2 arg3 arg4");
    });

    test("should parse an object with 'mcpServers' property as an array", () => {
      const jsonString = `{
        "mcpServers": [
          {
            "name": "Server 1",
            "command": "command1",
            "args": ["arg1", "arg2"]
          }
        ]
      }`;

      const result = MCPServerModel.parseServerConfigFromJSON(jsonString);

      expect(result).toHaveLength(1);
      expect(result[0].type).toBe("stdio");
      expect(result[0].name).toBe("Server 1");
      expect((result[0] as MCPServerStdioWithoutId).command).toBe("command1 arg1 arg2");
    });

    test("should parse an object with 'servers' property as a record", () => {
      const jsonString = `{
        "servers": {
          "server-1": {
            "command": "command1",
            "args": ["arg1", "arg2"]
          },
          "server-2": {
            "command": "command2",
            "args": ["arg3", "arg4"]
          }
        }
      }`;

      const result = MCPServerModel.parseServerConfigFromJSON(jsonString);

      expect(result).toHaveLength(2);
      // Name should be taken from the key if not present in the config
      expect(result[0].type).toBe("stdio");
      expect(result[0].name).toBe("server-1");
      expect((result[0] as MCPServerStdioWithoutId).command).toBe("command1 arg1 arg2");
      expect(result[1].type).toBe("stdio");
      expect(result[1].name).toBe("server-2");
      expect((result[1] as MCPServerStdioWithoutId).command).toBe("command2 arg3 arg4");
    });

    test("should parse a Claude Desktop format with server names as keys", () => {
      const jsonString = `{
        "time-server": {
          "command": "uvx",
          "args": ["mcp-server-time"]
        },
        "echo-server": {
          "command": "uvx",
          "args": ["mcp-server-echo"]
        }
      }`;

      const result = MCPServerModel.parseServerConfigFromJSON(jsonString);

      expect(result).toHaveLength(2);
      expect(result[0].type).toBe("stdio");
      expect(result[0].name).toBe("time-server");
      expect((result[0] as MCPServerStdioWithoutId).command).toBe("uvx mcp-server-time");
      expect(result[1].type).toBe("stdio");
      expect(result[1].name).toBe("echo-server");
      expect((result[1] as MCPServerStdioWithoutId).command).toBe("uvx mcp-server-echo");
    });

    test("should parse a single server object", () => {
      const jsonString = `{
        "name": "Single Server",
        "command": "single-command",
        "args": ["arg1", "arg2"]
      }`;

      const result = MCPServerModel.parseServerConfigFromJSON(jsonString);

      expect(result).toHaveLength(1);
      expect(result[0].type).toBe("stdio");
      expect(result[0].name).toBe("Single Server");
      expect((result[0] as MCPServerStdioWithoutId).command).toBe("single-command arg1 arg2");
    });

    test("should handle environment variables", () => {
      const jsonString = `{
        "name": "Server With Env",
        "command": "command",
        "args": ["arg1"],
        "env": {
          "DEBUG": true,
          "PORT": 3000,
          "NULL_VALUE": null
        }
      }`;

      const result = MCPServerModel.parseServerConfigFromJSON(jsonString);

      expect(result).toHaveLength(1);
      expect(result[0].type).toBe("stdio");
      expect(result[0].name).toBe("Server With Env");
      expect((result[0] as MCPServerStdioWithoutId).command).toBe("command arg1");
      expect((result[0] as MCPServerStdioWithoutId).env).toEqual({
        /* eslint-disable @typescript-eslint/naming-convention */
        DEBUG: "true",
        PORT: "3000",
        /* eslint-enable @typescript-eslint/naming-convention */
      });
    });

    test("should use 'title' as name if 'name' is not provided", () => {
      const jsonString = `{
        "title": "Server Title",
        "command": "command"
      }`;

      const result = MCPServerModel.parseServerConfigFromJSON(jsonString);

      expect(result).toHaveLength(1);
      expect(result[0].name).toBe("Server Title");
    });

    test("should use command as name if neither 'name' nor 'title' is provided", () => {
      const jsonString = `{
        "command": "fallback-name-command"
      }`;

      const result = MCPServerModel.parseServerConfigFromJSON(jsonString);

      expect(result).toHaveLength(1);
      expect(result[0].type).toBe("stdio");
      expect(result[0].name).toBe("fallback-name-command");
    });

    test("should parse HTTP server configuration", () => {
      const jsonString = `{
        "name": "HTTP Server",
        "type": "http",
        "url": "https://api.example.com/mcp"
      }`;

      const result = MCPServerModel.parseServerConfigFromJSON(jsonString);

      expect(result).toHaveLength(1);
      expect(result[0].type).toBe("http");
      expect(result[0].name).toBe("HTTP Server");
      expect((result[0] as MCPServerHttpWithoutId).url).toBe("https://api.example.com/mcp");
    });

    test("should parse SSE server configuration", () => {
      const jsonString = `{
        "name": "SSE Server",
        "type": "sse",
        "url": "https://api.example.com/sse"
      }`;

      const result = MCPServerModel.parseServerConfigFromJSON(jsonString);

      expect(result).toHaveLength(1);
      expect(result[0].type).toBe("sse");
      expect(result[0].name).toBe("SSE Server");
      expect((result[0] as MCPServerHttpWithoutId).url).toBe("https://api.example.com/sse");
    });

    test("should infer HTTP type from URL when type is not specified", () => {
      const jsonString = `{
        "name": "Inferred HTTP Server",
        "url": "https://api.example.com/mcp"
      }`;

      const result = MCPServerModel.parseServerConfigFromJSON(jsonString);

      expect(result).toHaveLength(1);
      expect(result[0].type).toBe("http");
      expect(result[0].name).toBe("Inferred HTTP Server");
      expect((result[0] as MCPServerHttpWithoutId).url).toBe("https://api.example.com/mcp");
    });

    test("should throw an error for invalid JSON", () => {
      const invalidJson = "{invalid: json}";

      expect(() => MCPServerModel.parseServerConfigFromJSON(invalidJson)).toThrow(MCPServerError);
    });

    test("should throw an error for unsupported format", () => {
      const invalidFormat = `"not an object or array"`;

      expect(() => MCPServerModel.parseServerConfigFromJSON(invalidFormat)).toThrow(MCPServerError);
    });

    test("should throw an error for JSON with invalid key format", () => {
      const jsonString = `{
        invalid key: {
          "command": "uvx",
          "args": ["mcp-server-time"]
        },
      }`;

      expect(() => MCPServerModel.parseServerConfigFromJSON(jsonString)).toThrow(MCPServerError);
    });
  });

  describe("getDuplicateServerIds", () => {
    test("should return empty set when there are no duplicate server names", () => {
      const servers = [
        {
          id: "1",
          type: "stdio" as const,
          name: "Server 1",
          command: "command1",
          arguments: "",
          useShellInterpolation: true,
        },
        {
          id: "2",
          type: "stdio" as const,
          name: "Server 2",
          command: "command2",
          arguments: "",
          useShellInterpolation: true,
        },
        {
          id: "3",
          type: "stdio" as const,
          name: "Server 3",
          command: "command3",
          arguments: "",
          useShellInterpolation: true,
        },
      ];

      const duplicateIds = MCPServerModel.parseDuplicateServerIds(servers);

      expect(duplicateIds.size).toBe(0);
    });

    test("should identify duplicate server names and return their IDs", () => {
      const servers = [
        {
          id: "1",
          type: "stdio" as const,
          name: "Server 1",
          command: "command1",
          arguments: "",
          useShellInterpolation: true,
        },
        {
          id: "2",
          type: "stdio" as const,
          name: "Server 2",
          command: "command2",
          arguments: "",
          useShellInterpolation: true,
        },
        {
          id: "3",
          type: "stdio" as const,
          name: "Server 1",
          command: "command3",
          arguments: "",
          useShellInterpolation: true,
        }, // Duplicate name
        {
          id: "4",
          type: "stdio" as const,
          name: "Server 3",
          command: "command4",
          arguments: "",
          useShellInterpolation: true,
        },
        {
          id: "5",
          type: "stdio" as const,
          name: "Server 2",
          command: "command5",
          arguments: "",
          useShellInterpolation: true,
        }, // Duplicate name
      ];

      const duplicateIds = MCPServerModel.parseDuplicateServerIds(servers);

      expect(duplicateIds.size).toBe(2);
      expect(duplicateIds.has("3")).toBe(true); // Second occurrence of "Server 1"
      expect(duplicateIds.has("5")).toBe(true); // Second occurrence of "Server 2"
    });

    test("should identify all duplicates beyond the first occurrence", () => {
      const servers = [
        {
          id: "1",
          type: "stdio" as const,
          name: "Server 1",
          command: "command1",
          arguments: "",
          useShellInterpolation: true,
        },
        {
          id: "2",
          type: "stdio" as const,
          name: "Server 1",
          command: "command2",
          arguments: "",
          useShellInterpolation: true,
        }, // Duplicate
        {
          id: "3",
          type: "stdio" as const,
          name: "Server 1",
          command: "command3",
          arguments: "",
          useShellInterpolation: true,
        }, // Duplicate
        {
          id: "4",
          type: "stdio" as const,
          name: "Server 2",
          command: "command4",
          arguments: "",
          useShellInterpolation: true,
        },
      ];

      const duplicateIds = MCPServerModel.parseDuplicateServerIds(servers);

      expect(duplicateIds.size).toBe(2);
      expect(duplicateIds.has("2")).toBe(true); // Second occurrence of "Server 1"
      expect(duplicateIds.has("3")).toBe(true); // Third occurrence of "Server 1"
    });

    test("should handle empty server array", () => {
      const servers: any[] = [];
      const duplicateIds = MCPServerModel.parseDuplicateServerIds(servers);

      expect(duplicateIds.size).toBe(0);
    });
  });

  describe("addServers", () => {
    test("should add multiple servers in a single operation", () => {
      // Clear any previous calls
      mockHost.postMessage.mockClear();

      const serversToAdd: MCPServerStdioWithoutId[] = [
        {
          type: "stdio",
          name: "Context 7",
          command: "npx -y @upstash/context7-mcp@latest",
          arguments: "",
          useShellInterpolation: true,
        },
        {
          type: "stdio",
          name: "Playwright",
          command: "npx -y @playwright/mcp@latest",
          arguments: "",
          useShellInterpolation: true,
        },
        {
          type: "stdio",
          name: "Tavily",
          command: "npx -y tavily-mcp@latest",
          arguments: "",
          useShellInterpolation: true,
          // eslint-disable-next-line @typescript-eslint/naming-convention
          env: { TAVILY_API_KEY: "your-tavily-api-key-here" }, // pragma: allowlist secret
        },
      ];

      model.addServers(serversToAdd);

      // Should call postMessage only once for all servers
      expect(mockHost.postMessage).toHaveBeenCalledTimes(1);

      // Verify the message contains all three servers
      const messageCall = mockHost.postMessage.mock.calls[0][0];
      expect(messageCall.data).toHaveLength(3);
      expect(messageCall.data[0].name).toBe("Context 7");
      expect(messageCall.data[1].name).toBe("Playwright");
      expect(messageCall.data[2].name).toBe("Tavily");
    });

    test("should throw error if any server name already exists", () => {
      // First add a server
      const existingServer: MCPServerStdioWithoutId = {
        type: "stdio",
        name: "Existing Server",
        command: "existing-command",
        arguments: "",
        useShellInterpolation: true,
      };
      model.addServer(existingServer);

      // Reset mock to clear the first call
      mockHost.postMessage.mockClear();

      // Try to add servers where one has a duplicate name
      const serversToAdd: MCPServerStdioWithoutId[] = [
        {
          type: "stdio",
          name: "New Server",
          command: "new-command",
          arguments: "",
          useShellInterpolation: true,
        },
        {
          type: "stdio",
          name: "Existing Server", // This should cause an error
          command: "another-command",
          arguments: "",
          useShellInterpolation: true,
        },
      ];

      expect(() => model.addServers(serversToAdd)).toThrow(MCPServerError);

      // Should not have called postMessage since the operation failed
      expect(mockHost.postMessage).not.toHaveBeenCalled();
    });

    test("should handle empty array gracefully", () => {
      // Clear any previous calls
      mockHost.postMessage.mockClear();

      model.addServers([]);

      // Should call postMessage once with empty array
      expect(mockHost.postMessage).toHaveBeenCalledTimes(1);
      const messageCall = mockHost.postMessage.mock.calls[0][0];
      expect(messageCall.data).toHaveLength(0);
    });
  });
});
