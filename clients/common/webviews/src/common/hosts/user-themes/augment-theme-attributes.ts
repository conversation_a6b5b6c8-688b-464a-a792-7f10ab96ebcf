let _documentNode: HTMLElement | undefined = document.documentElement;

/**
 * This is only here so we can support testing with storybook.  Please do not use
 * in production.
 *
 * @param node
 */
export function setDocumentNode(node: HTMLElement | undefined) {
  _documentNode = node;
}

export function getDocumentNode() {
  return _documentNode ?? document.documentElement;
}

export enum UserThemeCategory {
  light = "light",
  dark = "dark",
}
export enum UserThemeIntensity {
  regular = "regular",
  highContrast = "high-contrast",
}

export const AUGMENT_THEME_CATEGORY_ATTR = "data-augment-theme-category";
export const AUGMENT_THEME_INTENSITY_ATTR = "data-augment-theme-intensity";

export function getCategory(): UserThemeCategory | undefined {
  const category = getDocumentNode().getAttribute(AUGMENT_THEME_CATEGORY_ATTR);
  if (category && Object.values(UserThemeCategory).includes(category as UserThemeCategory)) {
    return category as UserThemeCategory;
  }
  return undefined;
}

export function setCategory(category: UserThemeCategory | undefined) {
  if (category === undefined) {
    getDocumentNode().removeAttribute(AUGMENT_THEME_CATEGORY_ATTR);
  } else {
    getDocumentNode().setAttribute(AUGMENT_THEME_CATEGORY_ATTR, category);
  }
}

export function getIntensity(): UserThemeIntensity | undefined {
  const intensity = getDocumentNode().getAttribute(AUGMENT_THEME_INTENSITY_ATTR);
  if (intensity && Object.values(UserThemeIntensity).includes(intensity as UserThemeIntensity)) {
    return intensity as UserThemeIntensity;
  }
  return undefined;
}

export function setIntensity(intensity: UserThemeIntensity | undefined) {
  if (intensity === undefined) {
    getDocumentNode().removeAttribute(AUGMENT_THEME_INTENSITY_ATTR);
  } else {
    getDocumentNode().setAttribute(AUGMENT_THEME_INTENSITY_ATTR, intensity);
  }
}
