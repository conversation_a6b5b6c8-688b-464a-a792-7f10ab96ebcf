import { writable } from "svelte/store";
import {
  getCategory,
  type UserThemeCategory,
  type UserThemeIntensity,
  AUGMENT_THEME_CATEGORY_ATTR,
  AUGMENT_THEME_INTENSITY_ATTR,
  getIntensity,
  getDocumentNode,
} from "./augment-theme-attributes";

export const themeStore = writable<AugmentUserThemeDetails | undefined>(undefined);

export type AugmentUserThemeDetails = {
  category?: UserThemeCategory;
  intensity?: UserThemeIntensity;
};

export function attachAugmentThemeObserver(
  cb: (category: UserThemeCategory | undefined, intensity: UserThemeIntensity | undefined) => void,
) {
  const observer = new MutationObserver((mutationList) => {
    for (const mutation of mutationList) {
      if (mutation.type === "attributes") {
        cb(getCategory(), getIntensity());
        break;
      }
    }
  });
  observer.observe(getDocumentNode(), {
    attributeFilter: [AUGMENT_THEME_CATEGORY_ATTR, AUGMENT_THEME_INTENSITY_ATTR],
    attributes: true,
  });
  return observer;
}

function initializeThemeStore() {
  // Setup listeners for changes from the client.
  attachAugmentThemeObserver((category, intensity) => {
    themeStore.update((): AugmentUserThemeDetails => {
      return {
        category: category,
        intensity: intensity,
      };
    });
  });

  // Initialize the store with the current details.
  themeStore.update((): AugmentUserThemeDetails => {
    return {
      category: getCategory(),
      intensity: getIntensity(),
    };
  });
}

initializeThemeStore();
