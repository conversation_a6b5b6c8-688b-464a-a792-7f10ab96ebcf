import { HostClientType, type HostInterface } from "./host-types";

export function isIntelliJHost(): boolean {
  return (window as any).augment_intellij !== undefined;
}

/**
 * Creates an enhanced IntelliJ host interface with state management capabilities,
 * that will setState in a background thread to avoid freezing the UI.
 *
 * @returns the enhanced IntelliJ host interface
 */
export function configureIntelliJHost(): HostInterface {
  const intelliJHost = (window as any).augment_intellij as IntelliJHost | undefined;
  if (
    intelliJHost === undefined ||
    intelliJHost.setState === undefined ||
    intelliJHost.getState === undefined ||
    intelliJHost.postMessage === undefined
  ) {
    throw new Error("Augment IntelliJ host not available");
  }

  window.augment = window.augment || {};

  let initialized = false;
  window.augment.host = {
    clientType: HostClientType.jetbrains,
    setState: (state: any) => {
      if (!initialized) {
        console.error("Host not initialized");
      }
      intelliJHost.setState(state);
    },
    getState: () => {
      if (!initialized) {
        console.error("Host not initialized");
      }
      return intelliJHost.getState();
    },
    postMessage: (msg: any) => {
      if (!initialized) {
        console.error("Host not initialized");
      }
      intelliJHost.postMessage(msg);
    },
    initialize: async () => {
      await intelliJHost.initializationPromise;
      initialized = true;
    },
  };

  return window.augment.host;
}

type IntelliJHost = {
  postMessage: (msg: any) => void;
  getState: () => any;
  setState: (state: any) => void;
  initializationPromise: Promise<void>;
};
