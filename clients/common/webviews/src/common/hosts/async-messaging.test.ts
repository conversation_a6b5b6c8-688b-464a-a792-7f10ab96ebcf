import {
  WebViewMessageType,
  type AsyncWebViewMessage,
  type ChatModelReply,
  type ChatUserMessage,
  type WebViewMessage,
} from "$vscode/src/webview-providers/webview-messages";
import {
  afterEach,
  beforeEach,
  describe,
  expect,
  type Mock,
  test,
  vi,
  type MockInstance,
} from "vitest";
import { AsyncMsgSender, AsyncMsgError, wrapRequest } from "./async-messaging";
import { type WebViewMessageTypes } from "$vscode/src/utils/webviews/types";

describe("messaging", () => {
  beforeEach(() => {});

  afterEach(() => {
    vi.restoreAllMocks();
  });
  describe("single async", () => {
    describe("wrapRequest", () => {
      test("returns an object with the correct type property", () => {
        const msg: WebViewMessage = {
          type: WebViewMessageType.chatUserMessage,
          data: { text: "hello", chatHistory: [], userSpecifiedFiles: [], silent: false },
        };
        const wrappedMsg = wrapRequest(msg);
        expect(wrappedMsg.type).toBe(WebViewMessageType.asyncWrapper);
      });

      test("returns a wrapped message", () => {
        vi.spyOn(crypto, "randomUUID").mockReturnValue("mock-uuid-1-2-3");
        const msg: WebViewMessage = {
          type: WebViewMessageType.chatUserMessage,
          data: { text: "hello", chatHistory: [], userSpecifiedFiles: [], silent: false },
        };
        const wrappedMsg = wrapRequest(msg);
        expect(wrappedMsg.requestId).toBe("mock-uuid-1-2-3");
        expect(wrappedMsg.baseMsg).toBe(msg);
      });
    });

    describe("AsyncMsgSender", () => {
      const timeoutMs = 500;
      // The mocks below simply take a posted message and echos it back to the handlers.
      let windowAddLisenerSpy: MockInstance;
      let consoleDebugSpy: MockInstance;
      let postMsg: Mock<(msg: AsyncWebViewMessage<WebViewMessageTypes>) => void>;

      beforeEach(() => {
        vi.clearAllMocks();
        vi.useFakeTimers();
        windowAddLisenerSpy = vi.spyOn(window, "addEventListener");
        consoleDebugSpy = vi.spyOn(console, "debug").mockImplementation(vi.fn());
        postMsg = vi.fn((msg: AsyncWebViewMessage<WebViewMessage>) => {
          window.dispatchEvent(new AsyncMessageEvent(msg));
        });
      });

      afterEach(() => {
        vi.clearAllMocks();
        vi.useRealTimers();
      });

      test("AsyncMsgSender construction works as expected", () => {
        new AsyncMsgSender(postMsg);
        expect(windowAddLisenerSpy).toBeCalledTimes(1);
      });

      test("Post a message and time out out when receiving different request id", async () => {
        const postMsgEchoDifferentRequestId = (msg: AsyncWebViewMessage<WebViewMessageTypes>) => {
          postMsg({ ...msg, requestId: "different-request-id" });
        };
        const asyncMsgSender = new AsyncMsgSender(postMsgEchoDifferentRequestId, timeoutMs);
        const baseMsg: WebViewMessage = {
          type: WebViewMessageType.chatUserMessage,
          data: { text: "hello", chatHistory: [], userSpecifiedFiles: [], silent: false },
        };
        const sendPromise = asyncMsgSender.send(baseMsg);
        vi.advanceTimersByTime(timeoutMs);
        await expect(sendPromise).rejects.toThrow(AsyncMsgError);
        await expect(sendPromise).rejects.toMatchObject({
          name: "MessageTimeout",
          message: expect.stringMatching(/Request timed out/)
        });
        await expect(consoleDebugSpy).toHaveBeenCalledTimes(1);
      });

      test("Post a message and time out when not receiving any request", async () => {
        const postMsgNeverReceive = vi.fn();
        const asyncMsgSender = new AsyncMsgSender(postMsgNeverReceive, timeoutMs);
        const baseMsg: WebViewMessage = {
          type: WebViewMessageType.chatUserMessage,
          data: { text: "hello", chatHistory: [], userSpecifiedFiles: [], silent: false },
        };
        const sendPromise = asyncMsgSender.send(baseMsg);
        vi.advanceTimersByTime(timeoutMs);
        await expect(sendPromise).rejects.toThrow(AsyncMsgError);
        await expect(sendPromise).rejects.toMatchObject({
          name: "MessageTimeout",
          message: expect.stringMatching(/Request timed out/)
        });
        await expect(consoleDebugSpy).toHaveBeenCalledTimes(1);
      });

      test("Post a message and time out, ignoring non-async messages", async () => {
        const postMsgNeverReceive = vi.fn();
        const asyncMsgSender = new AsyncMsgSender(postMsgNeverReceive, timeoutMs);
        const baseMsg: WebViewMessage = {
          type: WebViewMessageType.chatUserMessage,
          data: { text: "hello", chatHistory: [], userSpecifiedFiles: [], silent: false },
        };
        const sendPromise = asyncMsgSender.send(baseMsg);
        window.dispatchEvent(
          new MessageEvent("message", {
            data: {
              type: WebViewMessageType.chatInitialize,
            },
          }),
        );
        vi.advanceTimersByTime(timeoutMs);
        await expect(sendPromise).rejects.toThrow(AsyncMsgError);
        await expect(sendPromise).rejects.toMatchObject({
          name: "MessageTimeout",
          message: expect.stringMatching(/Request timed out/)
        });
        await expect(consoleDebugSpy).toHaveBeenCalledTimes(1);
      });

      test("Sends a message and echoes as expected", async () => {
        const asyncMsgSender = new AsyncMsgSender(postMsg);

        expect(postMsg).toHaveBeenCalledTimes(0);
        const baseMsg: WebViewMessage = {
          type: WebViewMessageType.chatUserMessage,
          data: { text: "hello", chatHistory: [], userSpecifiedFiles: [], silent: false },
        };
        const result = await asyncMsgSender.send(baseMsg);
        expect(postMsg).toHaveBeenCalledTimes(1);
        expect(result).toBe(baseMsg);
      });
    });
  });

  describe("stream async", () => {
    describe("AsyncMsgSender", () => {
      // The mocks below simply take a posted message and echos it back to the handlers.
      let windowAddLisenerSpy: MockInstance;
      let postMsg: Mock<(msg: AsyncWebViewMessage<WebViewMessageTypes>) => void>;
      function sendStreamChunk(
        requestId: string,
        nextRequestId: string,
        index: number,
        isStreamComplete: boolean,
        text: string,
        error: string | null = null,
      ) {
        postMsg({
          type: WebViewMessageType.asyncWrapper,
          requestId: requestId,
          error,
          baseMsg: {
            type: WebViewMessageType.chatModelReply,
            data: {
              text: text,
              requestId: requestId,
              streaming: true,
              workspaceFileChunks: [],
            },
          },
          streamCtx: {
            streamMsgIdx: index,
            streamNextRequestId: nextRequestId,
            isStreamComplete,
          },
        });
      }

      beforeEach(() => {
        vi.clearAllMocks();
        vi.useFakeTimers();
        windowAddLisenerSpy = vi.spyOn(window, "addEventListener");
        postMsg = vi.fn((msg: AsyncWebViewMessage<WebViewMessage>) => {
          window.dispatchEvent(new AsyncMessageEvent(msg));
        });
      });

      afterEach(() => {
        vi.useRealTimers();
      });

      test("AsyncMsgSender construction works as expected", () => {
        new AsyncMsgSender(postMsg);
        expect(windowAddLisenerSpy).toBeCalledTimes(1);
      });

      test("Post a message and stream correctly", async () => {
        // Set up mocks
        const streamDelayMs = 100;
        const dataToStream = ["hello", " ", "world", "!"];
        let mockPost = vi.fn((msg: AsyncWebViewMessage<WebViewMessageTypes>) => {
          if (!msg.streamCtx) {
            postMsg(msg);
          } else {
            (async () => {
              let currRequestId = msg.requestId;
              for (let i = 0; i < dataToStream.length; i++) {
                await new Promise((resolve) => setTimeout(resolve, streamDelayMs));
                const newRequestId = `${msg.requestId}-${i + 1}`;
                sendStreamChunk(currRequestId, newRequestId, i, false, dataToStream[i]);
                currRequestId = newRequestId;
              }
              await new Promise((resolve) => setTimeout(resolve, streamDelayMs));
              sendStreamChunk(currRequestId, "", dataToStream.length, true, "");
            })();
          }
          vi.advanceTimersByTime(streamDelayMs);
        });

        // Run test
        const asyncMsgSender = new AsyncMsgSender(mockPost);
        const request: ChatUserMessage = {
          type: WebViewMessageType.chatUserMessage,
          data: { text: "hello", chatHistory: [], userSpecifiedFiles: [], silent: false },
        };

        const dataToReceive = [...dataToStream];
        vi.advanceTimersByTime(streamDelayMs);
        for await (const msg of asyncMsgSender.stream<ChatUserMessage, ChatModelReply>(request)) {
          expect(msg.type).toBe(WebViewMessageType.chatModelReply);
          expect(dataToReceive.shift()).toBe(msg.data.text);
          vi.advanceTimersByTime(streamDelayMs);
        }
      });

      test("Post a message and stream with error", async () => {
        // Set up mocks
        const streamDelayMs = 100;
        const dataToStream = ["hello", " ", "world", "!"];
        let mockPost = vi.fn((msg: AsyncWebViewMessage<WebViewMessageTypes>) => {
          if (!msg.streamCtx) {
            postMsg(msg);
          } else {
            (async () => {
              let currRequestId = msg.requestId;
              let newRequestId = "";
              for (let i = 0; i < dataToStream.length; i++) {
                await new Promise((resolve) => setTimeout(resolve, streamDelayMs));
                newRequestId = `${msg.requestId}-${i + 1}`;
                sendStreamChunk(currRequestId, newRequestId, i, false, dataToStream[i]);
                currRequestId = newRequestId;
              }
              await new Promise((resolve) => setTimeout(resolve, streamDelayMs));
              sendStreamChunk(
                currRequestId,
                newRequestId,
                dataToStream.length,
                false,
                "",
                "This is an error. Oh no!",
              );
            })();
          }
          vi.advanceTimersByTime(streamDelayMs);
        });

        // Run test
        const asyncMsgSender = new AsyncMsgSender(mockPost);
        const request: ChatUserMessage = {
          type: WebViewMessageType.chatUserMessage,
          data: { text: "hello", chatHistory: [], userSpecifiedFiles: [], silent: false },
        };

        const dataToReceive = [...dataToStream];
        vi.advanceTimersByTime(streamDelayMs);
        const run = async () => {
          for await (const msg of asyncMsgSender.stream<ChatUserMessage, ChatModelReply>(request)) {
            expect(msg.type).toBe(WebViewMessageType.chatModelReply);
            expect(dataToReceive.shift()).toBe(msg.data.text);
            vi.advanceTimersByTime(streamDelayMs);
          }
        };
        await expect(run()).rejects.toThrow(new Error("This is an error. Oh no!"));
      });

      test("Post a message and stream with timeout", async () => {
        // Set up mocks
        const streamDelayMs = 60000;
        const dataToStream = ["hello", " ", "world", "!"];
        let mockPost = vi.fn((msg: AsyncWebViewMessage<WebViewMessageTypes>) => {
          if (!msg.streamCtx) {
            postMsg(msg);
          } else {
            (async () => {
              let currRequestId = msg.requestId;
              for (let i = 0; i < dataToStream.length; i++) {
                await new Promise((resolve) => setTimeout(resolve, streamDelayMs));
                const newRequestId = `${msg.requestId}-${i + 1}`;
                sendStreamChunk(currRequestId, newRequestId, i, false, dataToStream[i]);
                currRequestId = newRequestId;
              }
              await new Promise((resolve) => setTimeout(resolve, streamDelayMs));
              sendStreamChunk(currRequestId, "", dataToStream.length, true, "");
            })();
          }
          vi.advanceTimersByTime(streamDelayMs);
        });

        // Run test
        const asyncMsgSender = new AsyncMsgSender(mockPost, streamDelayMs);
        const request: ChatUserMessage = {
          type: WebViewMessageType.chatUserMessage,
          data: { text: "hello", chatHistory: [], userSpecifiedFiles: [], silent: false },
        };

        const dataToReceive = [...dataToStream];
        vi.advanceTimersByTime(streamDelayMs);
        const run = async () => {
          for await (const msg of asyncMsgSender.stream<ChatUserMessage, ChatModelReply>(request)) {
            expect(msg.type).toBe(WebViewMessageType.chatModelReply);
            expect(dataToReceive.shift()).toBe(msg.data.text);
            vi.advanceTimersByTime(streamDelayMs);
          }
        };
        const error = await run().catch(e => e);
        expect(error).toBeInstanceOf(AsyncMsgError);
        expect(error).toMatchObject({
          name: "StreamTimeout",
          message: "Stream timed out"
        });
      });
    });
  });
});

class AsyncMessageEvent extends MessageEvent<AsyncWebViewMessage<WebViewMessage>> {
  constructor(data: AsyncWebViewMessage<WebViewMessage>) {
    super("message", { data });
  }
}
