import type { WebViewMessage } from "$vscode/src/webview-providers/webview-messages";
import { HostClientType, type HostInterface } from "../host-types";
import {
  setCategory,
  setIntensity,
  UserThemeCategory,
  UserThemeIntensity,
} from "../user-themes/augment-theme-attributes";

const VSCODE_THEME_KIND_ATTR = "data-vscode-theme-kind";

declare global {
  var acquireVsCodeApi: (() => VSCodeInterface) | undefined;
}

export function isVSCodeHost(): boolean {
  return self["acquireVsCodeApi"] !== undefined;
}

// This function maps the VSCode theme kind attribute to the augment attributes.
function onThemeKindChanged() {
  setCategory(getThemeCategory());
  setIntensity(getThemeIntensity());
}

function mapVSCodeAndAugmentAttributes() {
  const observer = new MutationObserver(onThemeKindChanged);
  observer.observe(document.body, {
    attributeFilter: [VSCODE_THEME_KIND_ATTR],
    attributes: true,
  });
  onThemeKindChanged();
  return observer;
}

export function getVSCodeHost(): HostInterface {
  if (self.acquireVsCodeApi === undefined) {
    throw new Error(`acquireVsCodeAPI not available`);
  }

  mapVSCodeAndAugmentAttributes();

  return {
    ...(self.acquireVsCodeApi() as VSCodeInterface),
    clientType: HostClientType.vscode,
  };
}

enum VSCodeThemeKind {
  dark = "vscode-dark",
  light = "vscode-light",
  highContrast = "vscode-high-contrast",
  highContrastLight = "vscode-high-contrast-light",
}

const vscodeAttrToCategory: { [key in VSCodeThemeKind]: UserThemeCategory } = {
  [VSCodeThemeKind.dark]: UserThemeCategory.dark,
  [VSCodeThemeKind.highContrast]: UserThemeCategory.dark,
  [VSCodeThemeKind.light]: UserThemeCategory.light,
  [VSCodeThemeKind.highContrastLight]: UserThemeCategory.light,
};

const vscodeAttrToIntensity: { [key in VSCodeThemeKind]: UserThemeIntensity } = {
  [VSCodeThemeKind.dark]: UserThemeIntensity.regular,
  [VSCodeThemeKind.light]: UserThemeIntensity.regular,
  [VSCodeThemeKind.highContrast]: UserThemeIntensity.highContrast,
  [VSCodeThemeKind.highContrastLight]: UserThemeIntensity.highContrast,
};

function getThemeCategory(): UserThemeCategory | undefined {
  const attr = document.body.getAttribute(VSCODE_THEME_KIND_ATTR);
  if (!attr) {
    return undefined;
  }
  return vscodeAttrToCategory[attr as VSCodeThemeKind];
}

function getThemeIntensity(): UserThemeIntensity | undefined {
  const attr = document.body.getAttribute(VSCODE_THEME_KIND_ATTR);
  if (!attr) {
    return undefined;
  }
  return vscodeAttrToIntensity[attr as VSCodeThemeKind];
}

export interface VSCodeInterface {
  postMessage(data: WebViewMessage): void;
  getState(): any | undefined;
  setState(state: any): void;
}
