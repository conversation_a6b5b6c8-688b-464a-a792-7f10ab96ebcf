import { afterEach, beforeEach, describe, expect, test, vi } from "vitest";
import { getVSCodeHost, isVSCodeHost } from "./vscode";

describe("vscode", () => {
  const fakeVsCodeApi = {
    postMessage: () => {},
    getState: () => {},
    setState: () => {},
  };

  function mockVsCodeApi() {
    self.acquireVsCodeApi = vi.fn().mockImplementation(() => fakeVsCodeApi);
  }

  beforeEach(() => {
    self.acquireVsCodeApi = undefined;
  });

  afterEach(() => {
    self.acquireVsCodeApi = undefined;
  });

  describe("isVSCodeHost", () => {
    test("return false for non-vscode env", () => {
      expect(isVSCodeHost()).toBe(false);
    });

    test("return true for vscode env", () => {
      mockVsCodeApi();
      expect(isVSCodeHost()).toBe(true);
    });
  });
  describe("getVSCodeHost", () => {
    test("throw for non-vscode environment", () => {
      expect(() => getVSCodeHost()).toThrow();
    });

    test("returns and caches vscode host", () => {
      mockVsCodeApi();

      const got = getVSCodeHost();
      expect(got).toStrictEqual({
        ...fakeVsCodeApi,
        clientType: "vscode",
      });

      expect(self.acquireVsCodeApi).toHaveBeenCalledTimes(1);
    });
  });
});
