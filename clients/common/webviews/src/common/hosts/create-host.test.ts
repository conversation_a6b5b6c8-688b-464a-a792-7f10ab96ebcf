import { expect, test, vi, describe, beforeEach, afterEach } from "vitest";
import { getHost } from "$common-webviews/src/common/hosts/create-host";
import { isVSCodeHost } from "$common-webviews/src/common/hosts/vscode/vscode";
import { isIntelliJHost } from "$common-webviews/src/common/hosts/intellij";

describe("create-host", () => {
  // Save original values
  const originalWorker = global.Worker;
  const originalCreateObjectURL = URL.createObjectURL;

  beforeEach(() => {
    // Reset global objects
    self.acquireVsCodeApi = undefined;
    delete window.augment;
    delete (window as any).augment_intellij;

    // Reset mocks
    vi.resetAllMocks();
  });

  afterEach(() => {
    // Reset global objects
    self.acquireVsCodeApi = undefined;
    delete window.augment;
    delete (window as any).augment_intellij;

    // Reset Worker-related globals
    global.Worker = originalWorker;
    URL.createObjectURL = originalCreateObjectURL;
  });

  test("create VSCode host", () => {
    const vscodeHost = {
      postMessage: () => {},
      getState: () => {},
      setState: () => {},
    };
    self.acquireVsCodeApi = vi.fn().mockImplementation(() => vscodeHost);
    expect(isVSCodeHost()).toBe(true);
    expect(getHost()).toStrictEqual({
      ...vscodeHost,
      clientType: "vscode",
    });

    // vscode api can only be acquired once, so check multiple
    // calls are cached.
    expect(getHost()).toStrictEqual({
      ...vscodeHost,
      clientType: "vscode",
    });
    expect(self.acquireVsCodeApi).toHaveBeenCalledTimes(1);

    expect(window.augment).toBeDefined();
    expect(window!.augment!.host).toStrictEqual({
      ...vscodeHost,
      clientType: "vscode",
    });
  });

  test("create IntelliJ host", async () => {
    // Save original values
    const originalWorker = global.Worker;
    const originalCreateObjectURL = URL.createObjectURL;

    // Setup IntelliJ host environment with spy functions
    const postMessageSpy = vi.fn();
    const getStateSpy = vi.fn().mockReturnValue({ testData: "test" });
    const setStateSpy = vi.fn();

    (window as any).augment_intellij = {
      postMessage: postMessageSpy,
      setState: setStateSpy,
      getState: getStateSpy,
    };

    // Define worker because it does not exist in test environment
    global.Worker = class MockWorker {
      onmessage: ((ev: MessageEvent) => any) | null = null;
      constructor(public url: string) {}
      postMessage(message: any): void {
        setTimeout(() => {
          // simulate asynchronous behavior
          if (this.onmessage) {
            const encodedData = "encoded_" + JSON.stringify(message);
            this.onmessage({ data: { success: true, data: encodedData } } as MessageEvent);
          }
        }, 10);
      }
      terminate(): void {}
    } as any;

    // Mock URL.createObjectURL if it doesn't exist
    URL.createObjectURL = vi.fn().mockReturnValue("blob:worker-url");

    // Expect isIntelliJHost to return true
    expect(isIntelliJHost()).toBe(true);

    // Get the host
    const host = getHost();

    // Verify the host is properly set up
    expect(host).toBeDefined();
    expect(window.augment).toBeDefined();
    expect(window!.augment!.host).toBeDefined();

    // Test getState function is properly connected
    const state = host.getState();
    expect(getStateSpy).toHaveBeenCalled();
    expect(state).toEqual({ testData: "test" });

    // Test postMessage function is properly connected
    const testMessage = { type: "test", data: "message" };
    host.postMessage(testMessage);
    expect(postMessageSpy).toHaveBeenCalledWith(testMessage);

    // Test setState function triggers the IntelliJ setState
    const testState = { newState: "value" };
    host.setState(testState);

    // Allow the worker to process the message
    await new Promise((resolve) => setTimeout(resolve, 20));
    expect(setStateSpy).toHaveBeenCalled();

    // Restore original values
    global.Worker = originalWorker;
    URL.createObjectURL = originalCreateObjectURL;
  });
});
