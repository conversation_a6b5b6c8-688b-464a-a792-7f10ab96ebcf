<script lang="ts">
  import TextAugment, {
    type TextSize,
  } from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import { getFileDirectory, getFilename, normalizeFilePath } from "../utils/file-paths";

  let clazz = "";
  export { clazz as class };
  export let filepath: string;
  export let size: TextSize = 1;
  export let nopath: boolean = false;
  export let growname: boolean = true;
  export let onClick: ((e: MouseEvent) => void) | undefined = undefined;

  $: normalizedFilepath = normalizeFilePath(filepath);

  // Remove all empty parts from the path
  $: filename = getFilename(normalizedFilepath);
  $: dir = getFileDirectory(normalizedFilepath);
  $: ele = !onClick ? "div" : "button";
</script>

<TextAugment {size}>
  <svelte:element
    this={ele}
    class={`c-filespan ${clazz}`}
    role={onClick ? "button" : ""}
    tabindex="0"
    on:click={onClick}
  >
    {#if $$slots.leftIcon}
      <slot name="leftIcon" />
    {/if}
    <span class="c-filespan__filename">{filename}</span>
    {#if !nopath}
      <div class="c-filespan__dir" class:growname>
        <!-- We wrap directory in RTL for ellipses on left, but the actual
                text contents are LTR, so we need to wrap the contents -->
        <div class="c-filespan__dir-text">{dir}</div>
      </div>
    {/if}
    {#if $$slots.rightIcon}
      <span class="right-icons">
        <slot name="rightIcon" />
      </span>
    {/if}
  </svelte:element>
</TextAugment>

<style>
  button[class~="c-filespan"] {
    background: none;
    color: inherit;
    border: none;
    padding: 0;
    font: inherit;
    cursor: pointer;
    outline: inherit;
    appearance: none;
    &:hover {
      opacity: 0.7;
    }
  }
  .c-filespan,
  button.c-filespan.c-filespan {
    display: flex;
    gap: var(--ds-spacing-0_5);
    width: 100%;
    overflow: hidden;
    flex-direction: row;
    align-items: center;
  }

  .c-filespan > .c-filespan__filename,
  .c-filespan > .c-filespan__dir {
    /* This will always attempt to take up any available space (grow 0)
        but if it needs to shrink, it will do so after the filename since it has
        a non-zero flex-basis */
    flex: 0 1 auto;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .c-filespan > .c-filespan__dir {
    /* This will allow the directory to grow after the filename,
        and will shrink before the filename */

    overflow: hidden;
    display: inline-block;

    /* We want the ellipsis to be on the left side, so we do RTL */
    text-overflow: ellipsis;
    direction: rtl;
    text-align: left;

    /* Lighter color for the directory path */
    opacity: 50%;
  }
  .c-filespan > .c-filespan__dir.growname {
    flex: 1 1 0px;
  }

  .c-filespan > .c-filespan__dir > .c-filespan__dir-text {
    display: inline;
    width: fit-content;
    direction: ltr;
  }

  .c-filespan > :global(:first-child),
  .c-filespan > :global(.material-symbols-outlined:first-child) {
    margin-left: 0;
  }

  .c-filespan > :global(:last-child),
  .c-filespan > :global(.material-symbols-outlined:last-child) {
    margin-right: 0;
  }

  /* Size SVG icons in leftIcon and rightIcon slots consistently */
  .c-filespan :global(svg) {
    height: var(--ds-icon-size-1);
    width: var(--ds-icon-size-1);
  }

  /* Hide rightIcon slot by default, show on hover */
  .c-filespan .right-icons {
    opacity: 0;
    transition: opacity 0.2s ease;
  }

  .c-filespan:hover .right-icons {
    opacity: 1;
  }
</style>
