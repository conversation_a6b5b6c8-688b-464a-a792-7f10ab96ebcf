<script lang="ts">
  import { match } from "ts-pattern";
  import { themeStore } from "$common-webviews/src/common/hosts/user-themes/theme-store";
  import PencilInsertionLight from "./NexteditAdditionLight.svelte";
  import PencilInsertionDark from "./NexteditAdditionDark.svelte";
  import PencilDeletionLight from "./NexteditDeletionLight.svelte";
  import PencilDeletionDark from "./NexteditDeletionDark.svelte";
  import PencilChangeLight from "./NexteditChangeLight.svelte";
  import PencilChangeDark from "./NexteditChangeDark.svelte";
  import PencilAppliedLight from "./NexteditAppliedLight.svelte";
  import PencilAppliedDark from "./NexteditAppliedDark.svelte";
  import PencilRejectedLight from "./NexteditRejectedLight.svelte";
  import PencilRejectedDark from "./NexteditRejectedDark.svelte";
  import PencilActiveLight from "./NexteditActiveLight.svelte";
  import PencilActiveDark from "./NexteditActiveDark.svelte";
  import PencilInactiveLight from "./NexteditInactiveLight.svelte";
  import PencilInactiveDark from "./NexteditInactiveDark.svelte";
  import { UserThemeCategory } from "../../hosts/user-themes/augment-theme-attributes";
  import {
    SuggestionState,
    type ChangeType,
    type IEditSuggestion,
  } from "$vscode/src/next-edit/next-edit-types";

  const pencils = {
    insertion: {
      light: PencilInsertionLight,
      dark: PencilInsertionDark,
    },
    deletion: {
      light: PencilDeletionLight,
      dark: PencilDeletionDark,
    },
    modification: {
      light: PencilChangeLight,
      dark: PencilChangeDark,
    },
    noop: {
      light: PencilChangeLight,
      dark: PencilChangeDark,
    },
    active: {
      light: PencilActiveLight,
      dark: PencilActiveDark,
    },
    inactive: {
      light: PencilInactiveLight,
      dark: PencilInactiveDark,
    },
    accepted: {
      light: PencilAppliedLight,
      dark: PencilAppliedDark,
    },
    rejected: {
      light: PencilRejectedLight,
      dark: PencilRejectedDark,
    },
    // TODO: support showing muted suggestions in the panel
    // muted: {
    //   light: PencilMutedLight,
    //   dark: PencilMutedDark,
    // },
  };
  // Uses the `mask` mode which fills in the svg with the current color
  export let mask = false;
  export let maskColor = "currentColor";
  export let suggestion: {
    changeType: IEditSuggestion["changeType"];
    state?: IEditSuggestion["state"];
  };
  // Allows theme to be overridden. It will follow the themes settings via css and the theme attribute.
  export let themeCategory: UserThemeCategory | undefined = undefined;
  $: {
    if ($themeStore?.category) {
      themeCategory ??= $themeStore.category;
    }
  }
  let changeType: ChangeType | "accepted";
  $: changeType = match(suggestion)
    .with(
      { state: SuggestionState.stale },
      { state: SuggestionState.accepted },
      () => "accepted" as const,
    )
    .otherwise(({ changeType }) => changeType);
  $: pencilType = pencils[changeType] ?? pencils.active;
  $: PencilComponent = pencilType[themeCategory ?? UserThemeCategory.light];
</script>

<span class="c-pencil-icon">
  <svelte:component this={PencilComponent} {mask} {maskColor} />
  <slot />
</span>

<style>
  .c-pencil-icon {
    display: inline-flex;
    align-items: center;
  }
</style>
