<script lang="ts">
  import tippy, { type Instance, type Props as _TippyProps } from "tippy.js";

  type TippyProps = Partial<Omit<_TippyProps, "content">>;
  interface TooltipProps {
    tippy: TippyProps;
    target?: HTMLElement | undefined;
  }

  export let tippyProps: TippyProps;
  export let tooltip: Instance<_TippyProps> | undefined = undefined;

  // Let consumers bind to the tooltip trigger
  export let tooltipTrigger: HTMLElement | undefined = undefined;

  /**
   * Takes a node and turns it into a tippy instance. We keep track of the
   * tooltipAnchor, and if the anchor
   * @param node
   * @param props
   */
  const tooltipFn = (node: HTMLElement, props: TooltipProps) => {
    const initOrSetProps = (props: TooltipProps): void => {
      if (!props.target) {
        return;
      }
      const newProps: Partial<_TippyProps> = {
        offset: [0, 4],
        ...props.tippy,
        popperOptions: {
          strategy: "fixed",
          ...props.tippy.popperOptions,
        },
        content: node,
        appendTo: props.target,
        interactive: true,
      };
      if (!tooltip) {
        tooltip = tippy(props.target, newProps);
      } else {
        tooltip.setProps(newProps);
        tooltip.setContent(node);
      }
    };

    initOrSetProps(props);

    return {
      update: (props: TooltipProps): void => {
        initOrSetProps(props);
      },
      destroy: () => {
        tooltip?.destroy();
        tooltip = undefined;
      },
    };
  };

  // The component that tippy will use as the tooltip and inject styles/behavior on
  let tooltipAnchor: HTMLElement | undefined = undefined;
</script>

<div class="c-tooltip-anchor" bind:this={tooltipAnchor}>
  <slot name="trigger">
    <!-- Add the tooltip trigger to the body.
      Example usage:
      <ButtonAugment
          on:click={() => {
            tooltip.show();
          }}
        -->
  </slot>
</div>

<div
  use:tooltipFn={{ tippy: { triggerTarget: tooltipTrigger, ...tippyProps }, target: tooltipAnchor }}
>
  <slot name="tooltipContents" {tooltip} />
</div>

<style>
  .c-tooltip-anchor {
    display: inline-flex;
    flex-direction: row;
    align-items: center;
  }
</style>
