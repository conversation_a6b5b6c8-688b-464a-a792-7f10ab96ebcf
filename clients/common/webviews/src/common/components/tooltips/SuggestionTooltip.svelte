<script lang="ts" generics="TOption extends IMentionable">
  import type { Readable } from "svelte/store";
  import type { Instance, Props as _TippyProps } from "tippy.js";

  import { onKey } from "../../utils/keypress";

  import type { IMentionSuggestionData } from "../inputs/mentions/store";
  import { type IMentionable } from "../inputs/types";
  import Tooltip from "./Tooltip.svelte";
  import VsCodeOptionItem from "../vscode/VSCodeOptionItem.svelte";
  import TooltipContents from "../TooltipContents.svelte";

  type TippyProps = Partial<Omit<_TippyProps, "content">>;
  export let tippyProps: TippyProps;
  export let suggestionsData: Readable<IMentionSuggestionData<TOption> | null>;
  export let onSelectItem: (item: TOption) => void = () => {};

  // Pull out useful data from the store
  $: suggestions = $suggestionsData?.items;
  $: emptyMessage = $suggestionsData?.emptyMessage;
  $: shouldShow = (suggestions && suggestions.length > 0) || emptyMessage;
  $: selectedIdx = $suggestionsData?.selectedIdx;

  // React to data
  $: shouldShow ? tooltip?.show() : tooltip?.hide();

  let isActive: boolean | undefined = undefined;
  let tooltip: Instance | undefined = undefined;
  function scrollOnSelect(e: HTMLElement, props: boolean) {
    let currSelected: boolean = props;
    return {
      update(props: boolean) {
        if (props && !currSelected) {
          e.scrollIntoView({ behavior: "instant", block: "nearest" });
        }
        currSelected = props;
      },
    };
  }
</script>

<Tooltip
  tippyProps={{
    ...tippyProps,
    delay: 0,
    duration: 0,
    trigger: "manual",
    showOnCreate: false,
    onShow: () => {
      isActive = true;
    },
    onHide: () => {
      isActive = false;
    },
  }}
  bind:tooltip
>
  <slot name="trigger" slot="trigger" {isActive} />
  <div class="c-dropdown-contents" slot="tooltipContents">
    {#if suggestions}
      <TooltipContents class="c-dropdown-card">
        {#if suggestions.length === 0 && emptyMessage}
          <VsCodeOptionItem class="c-dropdown-item-empty">{emptyMessage}</VsCodeOptionItem>
        {/if}
        {#each suggestions as option, index}
          {@const selected = selectedIdx === index}
          {@const selectOption = () => onSelectItem?.(option)}
          {@const onKeyDown = onKey("Enter", selectOption)}
          <!-- on:mousedown prevent default prevents the blur on the input -->
          <VsCodeOptionItem
            {index}
            {selected}
            class="c-dropdown-item"
            on:keydown={onKeyDown}
            on:mousedown={(e) => {
              e.preventDefault();
            }}
            on:click={selectOption}
          >
            <div use:scrollOnSelect={selected}>
              <slot name="optionItem" {option} />
            </div>
          </VsCodeOptionItem>
        {/each}
      </TooltipContents>
    {/if}
  </div>
</Tooltip>

<style>
  .c-dropdown-contents :global(.c-dropdown-card) {
    max-height: 50vh;
    overflow-y: auto;
    scrollbar-color: var(--augment-scrollbar-color) transparent;
    /* We add a border with the box-shadow for c-dropdown-contents */
    border: none;
    /* Ensure this matches the containers border-radius */
    border-radius: var(--suggestion-border-radius);
  }

  .c-dropdown-contents {
    --suggestion-border-radius: 2px;

    box-shadow: var(--ds-shadow-3);
    border-radius: var(--suggestion-border-radius);
  }

  .c-dropdown-contents :global(.c-dropdown-item) {
    padding: 0;
  }

  .c-dropdown-contents :global(.c-dropdown-item-empty) {
    padding: var(--ds-spacing-3);
  }
</style>
