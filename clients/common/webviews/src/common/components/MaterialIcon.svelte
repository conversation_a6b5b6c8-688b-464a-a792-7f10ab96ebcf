<script lang="ts">
  import "./material_icon.css";

  let clazz = "";
  export { clazz as class };
  export let iconName: string = "";
  export let fill: boolean = false;
  export let grade: "low" | "normal" | "high" = "normal";
  export let title: string | undefined = undefined;
  let fillStr: string;
  let wght: string;
  let grad: string;
  $: fillStr = fill ? "1" : "0";
  $: wght = fill ? "700" : "400";
  $: switch (grade) {
    case "low":
      grad = "-25";
      break;
    case "normal":
      grad = "0";
      break;
    case "high":
      grad = "200";
      break;
  }
</script>

<span
  class={`material-symbols-outlined ${clazz}`}
  style="font-variation-settings: 'FILL' {fillStr}, 'wght' {wght}, 'GRAD' {grad};"
  {title}
>
  {iconName}
</span>

<style>
  .material-symbols-outlined {
    font-size: 1rem;
  }
</style>
