import type { Action } from "svelte/action";
import { onKey } from "../../utils/keypress";

type DetailAttributes = {
  [k in `on:${"open" | "close"}-${"start" | "end"}`]?: (e: CustomEvent<HTMLDetailsElement>) => void;
};

const defaultOptions: KeyframeAnimationOptions = {
  duration: 300,
  easing: "ease-out",
};
/**
This code defines a Svelte action called animateDetails that adds animation to HTML <details> elements. Here's a breakdown of its functionality:
- It defines types for detail attributes, which are custom events for open and close states.
- The animateDetails function takes optional animation options.  The element is passed in per the Svelte action.
Inside the function:
- It finds the summary and content elements within the details element.
- It determines if the animation should affect width or height based on the writing mode.
- It sets up an animatePanel function that handles the actual animation using the Web Animations API.
- It creates a MutationObserver to watch for changes to the open attribute of the details element.
- It sets up click and keypress (Enter key) event listeners on the summary element to toggle the details open/closed state.
The animation itself:
- Dispatches custom events at the start and end of opening/closing.
- Animates the blockSize (height or width) of the content element.
- Updates the open state of the details element after animation.
- The action returns an object with destroy and update methods destroy removes event listeners and disconnects the observer.
- update allows for updating animation options.
This action provides a smooth, animated expansion and collapse effect for <details> element.
**/

export const animateDetails: Action<
  HTMLDetailsElement,
  KeyframeAnimationOptions | undefined,
  DetailAttributes
> = (detail: HTMLDetailsElement, options: KeyframeAnimationOptions = defaultOptions) => {
  const summary = detail.querySelector("summary");
  const content = detail.querySelector(".c-detail__content");
  if (!(summary && content)) {
    return;
  }

  options = {
    ...defaultOptions,
    ...options,
  };

  const widthChanges = /^(tb|vertical)/.test(getComputedStyle(content).writingMode);

  let transitioning = false;

  const animatePanel = (opening: boolean) => {
    transitioning = true;

    if (opening) {
      detail.open = true;
    }

    detail.dispatchEvent(
      new CustomEvent(opening ? "open-start" : "close-start", {
        detail,
      }),
    );

    const contentSize = content[widthChanges ? "clientWidth" : "clientHeight"];

    const animation = content.animate(
      {
        blockSize: opening ? ["0", `${contentSize}px`] : [`${contentSize}px`, "0"],
      },

      options,
    );

    animation.oncancel =
      animation.onfinish =
      animation.onremove =
        () => {
          detail.dispatchEvent(
            new CustomEvent(opening ? "open-end" : "close-end", {
              detail: detail,
            }),
          );

          if (!opening) {
            detail.open = false;
          }

          transitioning = false;
        };
  };

  const observer = new MutationObserver((mutationList) => {
    for (const mutation of mutationList) {
      if (mutation.type === "attributes" && mutation.attributeName === "open") {
        if (transitioning) {
          return;
        }

        if (detail.open) {
          animatePanel(true);
        }
      }
    }
  });
  observer.observe(detail, { attributes: true });
  const onClick = (e: Event) => {
    e.preventDefault();

    if (transitioning) {
      return;
    }
    animatePanel(!detail.open);
  };

  const onEnter = onKey("Enter", onClick);
  summary.addEventListener("click", onClick);
  summary.addEventListener("keypress", onEnter);

  return {
    destroy() {
      observer.disconnect();
      summary.removeEventListener("click", onClick);
      summary.removeEventListener("keypress", onEnter);
    },
    update(newOptions: KeyframeAnimationOptions = defaultOptions) {
      options = {
        ...options,
        ...newOptions,
      };
    },
  };
};
