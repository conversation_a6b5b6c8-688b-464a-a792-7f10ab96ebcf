<script lang="ts">
  let clazz: string = "";
  export { clazz as class };
  export let highlighted: boolean = false;
  export let style: string = "";
  export let overflowVisible: boolean = false;
  export let borders: ("top" | "bottom" | "left" | "right" | "top-bottom" | "left-right")[] = [
    "top",
    "bottom",
    "left",
    "right",
  ];
  export let reserveBorderSpace: boolean = false;

  $: computedBorders = borders.map((border) => border.split("-")).flat();

  $: hasTop = computedBorders.includes("top");
  $: hasBottom = computedBorders.includes("bottom");
  $: hasLeft = computedBorders.includes("left");
  $: hasRight = computedBorders.includes("right");
</script>

<div class="c-card-container" {style} class:overflow-visible={overflowVisible}>
  <div
    class={`c-card ${clazz}`}
    class:highlighted
    class:hasTop
    class:hasBottom
    class:hasLeft
    class:hasRight
    class:reserve-border-space={reserveBorderSpace}
  >
    <slot />
  </div>
</div>

<style>
  .c-card-container {
    flex: 1 0 auto;
    width: 100%;
    max-width: 100%;
    overflow: hidden;
  }

  .c-card {
    overflow: hidden;
    background-color: var(--vscode-input-background, var(--intellij-editor-searchField-background));
  }

  .c-card.reserve-border-space {
    border-width: 1px;
    border-style: solid;
    border-color: transparent;
  }

  .c-card.hasTop {
    border-top: 1px solid var(--augment-border-color);
  }

  .c-card.hasBottom {
    border-bottom: 1px solid var(--augment-border-color);
  }

  .c-card.hasLeft {
    border-left: 1px solid var(--augment-border-color);
  }

  .c-card.hasRight {
    border-right: 1px solid var(--augment-border-color);
  }
</style>
