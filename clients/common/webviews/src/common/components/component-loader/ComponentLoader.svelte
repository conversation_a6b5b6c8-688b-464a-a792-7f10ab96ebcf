<script lang="ts" generics="T extends Record<string,any>">
  import { wait } from "./util";

  import SpinnerAugment from "$common-webviews/src/design-system/components/SpinnerAugment.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import AugmentLogo from "$common-webviews/src/design-system/icons/augment/augment-logo.svelte";
  import { onDestroy, type ComponentType, type SvelteComponent, onMount } from "svelte";
  import { fade } from "svelte/transition";
  import messages from "./messages.json";

  export let minDisplayTime = 1000;
  export let loader: () => Promise<ComponentType<SvelteComponent<T>>>;
  export let props: T;
  export let title = "Augment Code";
  // Calculate timeout per character based on average reading speed:
  // - Average reading speed is 175-300 words per minute (using midpoint 237.5 wpm)
  // - Average word length is 5.1 characters
  // - Convert to milliseconds per character: (237.5 wpm / 60_000 ms/min / 5.1 chars/word)
  export let randomize = true;
  export let retryCount = 3;
  export let loadingMessages = messages.messages;
  export let errorMessages = messages.errors;
  export let errorMessage = "An error occurred while loading. Please try again later.";

  let messageCopy: string[] = loadingMessages.slice(1);
  let currentMessage = loadingMessages[0];
  let state: "loading" | "retry" | "error" = "loading";
  let abort = new AbortController();

  async function nextMessage() {
    if (messageCopy.length === 0) {
      messageCopy = [...(state === "retry" ? errorMessages : loadingMessages)];
    }
    currentMessage =
      state === "error"
        ? errorMessage
        : (messageCopy.splice(
            state !== "retry" && randomize ? Math.floor(Math.random() * messageCopy.length) : 0,
            1,
          )[0] ?? "");
  }

  onDestroy(() => abort.abort());

  onMount(nextMessage);

  async function loadLoader(currentRetry = 0): Promise<ComponentType<SvelteComponent<T>>> {
    try {
      const [ret] = await Promise.all([loader(), wait(minDisplayTime, abort.signal)]);
      return ret;
    } catch (e) {
      console.error("Failed to load component", e);
      state = "retry";

      if (currentRetry === 0) {
        messageCopy = [...errorMessages];
      }
      if (retryCount && currentRetry <= retryCount) {
        return await loadLoader(currentRetry + 1);
      } else {
        state = "error";
        throw new Error("Failed to load component after retrying. Please try again later.");
      }
    }
  }
</script>

{#await loadLoader()}
  <div class="l-loader" transition:fade>
    <div class="l-loader__logo">
      <AugmentLogo />
      <TextAugment size={2}>{title}</TextAugment>
    </div>
    <div class="l-loader__message-container">
      <SpinnerAugment />
      <TextAugment size={1} color="secondary">
        {currentMessage}
      </TextAugment>
    </div>
  </div>
{:then Component}
  <div transition:fade class="l-component">
    <Component {...props} />
  </div>
{:catch _error}
  <div class="l-loader" transition:fade>
    <div class="l-loader__logo">
      <AugmentLogo />
      <TextAugment size={3}>{title}</TextAugment>
    </div>
    <div class="l-loader__message-container l-loader-error-message">
      <TextAugment size={3}>An Error Occurred.</TextAugment>

      <TextAugment size={1}>
        <code>{errorMessage}</code>
      </TextAugment>
    </div>
  </div>
{/await}

<style>
  .l-component {
    display: contents;
  }
  .l-loader {
    position: absolute;
    top: 0;
    left: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100vh;
    width: 100vw;
    gap: var(--ds-spacing-3);
    background-color: var(--augment-window-background);
    color: var(--augment-text-color-secondary);
    overflow: hidden;
    padding: var(--ds-spacing-3);
  }

  .l-loader__logo {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--ds-spacing-2);
  }

  .l-loader__message-container {
    display: inline-flex;
    flex-direction: row;
    justify-content: center;
    text-align: center;
    gap: var(--ds-spacing-1);
    & > :global(.c-spinner) {
      position: relative;
      top: 2px;
    }
  }

  .l-loader-error-message {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: var(--ds-spacing-1);
    max-width: 50%;
    & > * {
      display: inline-flex;
    }
  }
</style>
