#!/usr/bin/env tsx
import fs from "fs";
import path from "path";
/**
 * This script generates language to extension mappings from the vscode repo.
 * Code heavily insipired by MIT licensed (https://github.com/microsoft/vscode/blob/main/extensions/theme-seti/build/update-icon-theme.js)
 */

function mergeMapping(
  to: Record<PropertyKey, string[]>,
  from: Record<PropertyKey, string[]>,
  property: PropertyKey,
) {
  if (from[property]) {
    if (to[property]) {
      to[property].push(...from[property]);
    } else {
      to[property] = from[property];
    }
  }
}
const nonBuiltInLanguages: Record<string, Mapping> = {
  // { fileNames, extensions  }
  argdown: { extensions: ["ad", "adown", "argdown", "argdn"] },
  bicep: { extensions: ["bicep"] },
  elixir: { extensions: ["ex"] },
  elm: { extensions: ["elm"] },
  erb: { extensions: ["erb", "rhtml", "html.erb"] },
  // eslint-disable-next-line @typescript-eslint/naming-convention
  "github-issues": { extensions: ["github-issues"] },
  gradle: { extensions: ["gradle"] },
  godot: { extensions: ["gd", "godot", "tres", "tscn"] },
  haml: { extensions: ["haml"] },
  haskell: { extensions: ["hs"] },
  haxe: { extensions: ["hx"] },
  jinja: { extensions: ["jinja"] },
  kotlin: { extensions: ["kt"] },
  mustache: { extensions: ["mustache", "mst", "mu", "stache"] },
  nunjucks: {
    extensions: ["nunjucks", "nunjs", "nunj", "nj", "njk", "tmpl", "tpl"],
  },
  ocaml: { extensions: ["ml", "mli", "mll", "mly", "eliom", "eliomi"] },
  puppet: { extensions: ["puppet"] },
  r: { extensions: ["r", "rhistory", "rprofile", "rt"] },
  rescript: { extensions: ["res", "resi"] },
  sass: { extensions: ["sass"] },
  stylus: { extensions: ["styl"] },
  terraform: { extensions: ["tf", "tfvars", "hcl"] },
  todo: { fileNames: ["todo"] },
  vala: { extensions: ["vala"] },
  vue: { extensions: ["vue"] },
};
const toLowerCase = (s: string) => s.toLowerCase();

type Mapping = {
  extensions?: string[];
  fileNames?: string[];
  filenamePatterns?: string[];
};
function getLanguageMappings(extensionsDir: string) {
  const langMappings: Record<string, Mapping> = {};
  const allExtensions = fs.readdirSync(extensionsDir);
  for (let i = 0; i < allExtensions.length; i++) {
    const dirPath = path.join(extensionsDir, allExtensions[i], "package.json");
    if (fs.existsSync(dirPath)) {
      const jsonContent = JSON.parse(fs.readFileSync(dirPath, "utf-8"));
      const languages = jsonContent.contributes && jsonContent.contributes.languages;
      if (Array.isArray(languages)) {
        for (let k = 0; k < languages.length; k++) {
          const languageId = languages[k].id;
          if (languageId) {
            const extensions = languages[k].extensions;
            const mapping: Mapping = {};
            if (Array.isArray(extensions)) {
              mapping.extensions = extensions.map((e) => e.substr(1).toLowerCase());
            }
            const filenames = languages[k].filenames;
            if (Array.isArray(filenames)) {
              mapping.fileNames = filenames.map(toLowerCase);
            }
            const filenamePatterns = languages[k].filenamePatterns;
            if (Array.isArray(filenamePatterns)) {
              mapping.filenamePatterns = filenamePatterns.map(toLowerCase);
            }
            const existing = langMappings[languageId];

            if (existing) {
              // multiple contributions to the same language
              // give preference to the contribution wth the configuration
              if (languages[k].configuration) {
                mergeMapping(mapping, existing, "extensions");
                mergeMapping(mapping, existing, "fileNames");
                mergeMapping(mapping, existing, "filenamePatterns");
                langMappings[languageId] = mapping;
              } else {
                mergeMapping(existing, mapping, "extensions");
                mergeMapping(existing, mapping, "fileNames");
                mergeMapping(existing, mapping, "filenamePatterns");
              }
            } else {
              langMappings[languageId] = mapping;
            }
          }
        }
      }
    }
  }
  return Object.assign(langMappings, nonBuiltInLanguages);
}

async function main(outputPath = "extensions.json", vscodePath: string): Promise<string> {
  if (!outputPath || !vscodePath) {
    throw new Error("Please provide output path and vscode path");
  }
  const vscodeSetiPath = path.join(vscodePath, "extensions");

  //This may throw an error if it does not exist which is fine.
  if (!(await fs.promises.stat(vscodeSetiPath)).isDirectory) {
    throw new Error(
      "theme-seti not found in vscode path  please provide the path to the vscode repo",
    );
  }
  await fs.promises.writeFile(
    outputPath,
    JSON.stringify(
      Array.from(Object.entries(getLanguageMappings(vscodeSetiPath)), ([lang, { extensions }]) => [
        lang,
        extensions ?? [],
      ]),
    ) + "\n",
  );

  return outputPath;
}

main(process.argv[2], process.argv[3]).then(
  // eslint-disable-next-line no-console
  (v) => console.log(`wrote file: ${v}`),
  (e) => {
    // eslint-disable-next-line no-console
    console.error(`error: ${e}`);
    process.exit(1);
  },
);
