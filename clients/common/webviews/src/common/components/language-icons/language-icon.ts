import { UserThemeCategory } from "../../hosts/user-themes/augment-theme-attributes";
import extensions from "./extensions.json";
import iconThemeJson from "./vs-seti-icon-theme.json";
type IconDef = {
  fontColor?: string;
  fontCharacter: string;
};

const iconTheme = iconThemeJson as {
  light: {
    fileExtensions: Record<string, string>;
    languageIds: Record<string, string>;
  };
  fileExtensions: Record<string, string>;
  languageIds: Record<string, string>;
  iconDefinitions: Record<string, IconDef>;
};

const extensionsMap = new Map<string, string>(
  (extensions as [string, string[]][]).flatMap(([lang, extensions]) =>
    (extensions ?? [lang]).map((ext) => [ext, lang]),
  ),
);

const LANG_TO_LANGUAGE_ID = {
  bash: "shellscript",
  zsh: "shellscript",
  fish: "shellscript",
  sh: "shellscript",
  env: "properties",
  svelte: "html",
} as const as Record<string, keyof typeof iconTheme.languageIds>;

export const resolveLanguage = (name: string): string => {
  const filename = name.split("/").at(-1);
  if (!filename) {
    return "";
  }
  const exts = filename.split(".");
  return (
    LANG_TO_LANGUAGE_ID[name] ??
    extensionsMap.get(exts.at(-1) ?? "") ??
    extensionsMap.get(exts.slice(-2).join(".")) ??
    filename
  ).replace("typescriptreact", "typescript");
};

const objToVars = (obj: undefined | IconDef) =>
  obj == null
    ? ""
    : `--augment-file-icon-font-color: ${obj.fontColor ?? "currentColor"};--augment-file-icon-font-character: "${obj.fontCharacter}";`;

const findLanguageId = (ext: string | undefined, light: boolean): string | undefined => {
  if (!ext) {
    return undefined;
  }
  const lang = extensionsMap.get(ext) ?? ext;
  if (lang) {
    const val =
      (light ? iconTheme.light.languageIds[lang] : undefined) ?? iconTheme.languageIds[lang];
    if (val) {
      return val;
    }
  }
  //Some colors are the same in light and dark, so we need to check both if it is light mode.
  return (
    (light ? iconTheme.light.fileExtensions[ext] : undefined) ??
    (light ? iconTheme.light.languageIds[ext] : undefined) ??
    iconTheme.fileExtensions[ext] ??
    iconTheme.languageIds[ext]
  );
};

export const createCssVars = (
  ext: string | undefined,
  language: string | undefined,
  category: UserThemeCategory = UserThemeCategory.dark,
): string => {
  if (language) {
    return createCssVarsForLanguage(language, category);
  }
  return createCssVarsForFile(ext ?? "", category);
};

export const createCssVarsForFile = (
  name: string,
  category: UserThemeCategory = UserThemeCategory.dark,
): string => {
  const light = category === UserThemeCategory.light;
  //match full names, like webpack.config.js
  const fName = name.split("/").at(-1)?.toLocaleLowerCase() ?? "";
  const languageId =
    findLanguageId(fName, light) ??
    //match extensions, like .js
    findLanguageId(fName.split(".").at(-1), light) ??
    //match extensions with two parts, like stuff.d.ts
    findLanguageId(fName.split(".").slice(-2).join("."), light) ??
    //show the default icon
    (light ? "_default_light" : "_default");

  //match extension aliases.
  return languageId ? objToVars(iconTheme.iconDefinitions[languageId]) : "";
};
export const createCssVarsForLanguage = (
  language: string,
  category: UserThemeCategory = UserThemeCategory.dark,
): string => {
  const languageId = findLanguageId(language, category === UserThemeCategory.light);
  return languageId ? objToVars(iconTheme.iconDefinitions[languageId]) : "";
};
