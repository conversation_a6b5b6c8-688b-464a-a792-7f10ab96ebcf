[["bat", ["bat", "cmd"]], ["clojure", ["clj", "cljs", "cljc", "cljx", "clojure", "edn"]], ["coffeescript", ["coffee", "cson", "iced"]], ["jsonc", ["jsonc", "eslintrc", "eslintrc.json", "jsfmtrc", "jshintrc", "swcrc", "hintrc", "babelrc", "code-workspace", "anguage-configuration.json", "con-theme.json", "olor-theme.json"]], ["json", ["json", "bower<PERSON>", "jscsrc", "webmanifest", "js.map", "css.map", "ts.map", "har", "jslintrc", "j<PERSON>ld", "g<PERSON><PERSON><PERSON>", "ipynb", "vuerc", "code-profile", "tsbuildinfo"]], ["c", ["c", "i"]], ["cpp", ["cpp", "cppm", "cc", "ccm", "cxx", "cxxm", "c++", "c++m", "hpp", "hh", "hxx", "h++", "h", "ii", "ino", "inl", "ipp", "ixx", "tpp", "txx", "hpp.in", "h.in"]], ["cuda-cpp", ["cu", "cuh"]], ["csharp", ["cs", "csx", "cake"]], ["css", ["css"]], ["dart", ["dart"]], ["diff", ["diff", "patch", "rej"]], ["dockerfile", ["dockerfile", "containerfile"]], ["ignore", ["gitignore_global", "gitignore", "git-blame-ignore-revs", "npmignore"]], ["fsharp", ["fs", "fsi", "fsx", "fsscript"]], ["git-commit", []], ["git-rebase", []], ["go", ["go"]], ["groovy", ["groovy", "gvy", "gradle", "jen<PERSON><PERSON><PERSON>", "nf"]], ["handlebars", ["handlebars", "hbs", "hjs"]], ["hlsl", ["hlsl", "hlsli", "fx", "fxh", "vsh", "psh", "cginc", "compute"]], ["html", ["html", "htm", "shtml", "xhtml", "xht", "mdoc", "jsp", "asp", "aspx", "jshtm", "volt", "ejs", "rhtml"]], ["ini", ["ini"]], ["properties", ["conf", "properties", "cfg", "directory", "gitattributes", "gitconfig", "g<PERSON><PERSON><PERSON><PERSON>", "editorconfig", "repo", "npmrc"]], ["java", ["java", "jav"]], ["javascriptreact", ["jsx"]], ["javascript", ["js", "es6", "mjs", "cjs", "pac"]], ["jsx-tags", []], ["jsonl", ["jsonl"]], ["snippets", ["code-snippets"]], ["julia", ["jl"]], ["juliamarkdown", ["jmd"]], ["tex", ["sty", "cls", "bbx", "cbx"]], ["latex", ["tex", "ltx", "ctx"]], ["bibtex", ["bib"]], ["cpp_embedded_latex", []], ["markdown_latex_combined", []], ["less", ["less"]], ["log", ["log", ".log.?"]], ["lua", ["lua"]], ["makefile", ["mak", "mk"]], ["markdown", ["md", "mkd", "mdwn", "mdown", "markdown", "markdn", "mdtxt", "mdtext", "workbook"]], ["markdown-math", []], ["objective-c", ["m"]], ["objective-cpp", ["mm"]], ["perl", ["pl", "pm", "pod", "t", "pl", "psgi"]], ["raku", ["raku", "rak<PERSON><PERSON>", "rak<PERSON><PERSON>", "rak<PERSON><PERSON>", "nqp", "p6", "pl6", "pm6"]], ["php", ["php", "php4", "php5", "phtml", "ctp"]], ["powershell", ["ps1", "psm1", "psd1", "pssc", "psrc"]], ["jade", ["pug", "jade"]], ["python", ["py", "rpy", "pyw", "cpy", "gyp", "gypi", "pyi", "ipy", "pyt"]], ["r", ["r", "rhistory", "rprofile", "rt"]], ["razor", ["cshtml", "razor"]], ["restructuredtext", ["rst"]], ["ruby", ["rb", "rbx", "rjs", "gemspec", "rake", "ru", "erb", "podspec", "rbi"]], ["rust", ["rs"]], ["scss", ["scss"]], ["search-result", ["code-search"]], ["shaderlab", ["shader"]], ["shellscript", ["sh", "bash", "bashrc", "bash_aliases", "bash_profile", "bash_login", "ebuild", "eclass", "profile", "bash_logout", "xprofile", "xsession", "xsessionrc", "xsession", "zsh", "zshrc", "zprofile", "zlogin", "zlogout", "zshenv", "zsh-theme", "fish", "ksh", "csh", "cshrc", "tcshrc", "yashrc", "yash_profile"]], ["sql", ["sql", "dsql"]], ["swift", ["swift"]], ["typescript", ["ts", "cts", "mts"]], ["typescriptreact", ["tsx"]], ["vb", ["vb", "brs", "vbs", "bas", "vba"]], ["xml", ["xml", "xsd", "ascx", "atom", "axml", "axaml", "bpmn", "cpt", "csl", "c<PERSON><PERSON><PERSON>", "csproj.user", "dita", "ditamap", "dtd", "ent", "mod", "dtml", "fsproj", "fxml", "iml", "isml", "jmx", "launch", "menu", "mxml", "nuspec", "opml", "owl", "proj", "props", "pt", "publishsettings", "pubxml", "pubxml.user", "rbxlx", "rbxmx", "rdf", "rng", "rss", "s<PERSON><PERSON><PERSON>", "storyboard", "svg", "targets", "tld", "tmx", "vbproj", "vbproj.user", "vcxproj", "vcxproj.filters", "wsdl", "wxi", "wxl", "wxs", "xaml", "xbl", "xib", "xlf", "xliff", "xpdl", "xul", "xoml"]], ["xsl", ["xsl", "xslt"]], ["dockercompose", []], ["yaml", ["yaml", "yml", "e<PERSON>l", "eyml", "cff", "yaml-tmlanguage", "yaml-tmpreferences", "yaml-tmtheme"]], ["argdown", ["ad", "adown", "argdown", "argdn"]], ["bicep", ["bicep"]], ["elixir", ["ex"]], ["elm", ["elm"]], ["erb", ["erb", "rhtml", "html.erb"]], ["github-issues", ["github-issues"]], ["gradle", ["gradle"]], ["godot", ["gd", "godot", "tres", "tscn"]], ["haml", ["haml"]], ["haskell", ["hs"]], ["haxe", ["hx"]], ["jinja", ["jinja"]], ["kotlin", ["kt"]], ["mustache", ["mustache", "mst", "mu", "stache"]], ["nunjucks", ["nunjucks", "nunjs", "nunj", "nj", "njk", "tmpl", "tpl"]], ["ocaml", ["ml", "mli", "mll", "mly", "eliom", "<PERSON><PERSON><PERSON>"]], ["puppet", ["puppet"]], ["rescript", ["res", "resi"]], ["sass", ["sass"]], ["stylus", ["styl"]], ["terraform", ["tf", "tfvars", "hcl"]], ["todo", []], ["vala", ["vala"]], ["vue", ["vue"]]]