<script lang="ts">
  import type { IEditSuggestion } from "$vscode/src/next-edit/next-edit-types";
  import LanguageIcon from "$common-webviews/src/common/components/language-icons/LanguageIcon.svelte";
  import SuggestionTreeItemActions from "../suggestion-tree/SuggestionTreeItemActions.svelte";
  import type { ActionConfig, OnCodeAction } from "../code-roll/types";

  export let filepath: string;
  export { clazz as class };
  export let value: IEditSuggestion[];
  export let onCodeAction: OnCodeAction;
  let clazz = "";
  let isHovered = false;
  export let codeActions: ActionConfig[] = [];
</script>

<div
  class="c-icon-file-path {clazz}"
  title={filepath}
  on:mouseenter={() => (isHovered = true)}
  on:mouseleave={() => (isHovered = false)}
  role="button"
  tabindex="0"
>
  <LanguageIcon filename={filepath} class="c-icon-file-path__lang-icon" />
  <div class="c-icon-file-path__path">{filepath.replace(/^\//, "")}</div>
  {#if isHovered && value.length && codeActions.length}
    <SuggestionTreeItemActions {onCodeAction} {codeActions} {value} />
  {/if}
</div>

<style>
  .c-icon-file-path {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 0;
    flex: 1;
    overflow: hidden;
    position: relative;
    outline: none;
  }
  .c-icon-file-path__lang-icon {
    display: flex;
    align-items: center;
  }
  .c-icon-file-path__path {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    direction: rtl;
  }
</style>
