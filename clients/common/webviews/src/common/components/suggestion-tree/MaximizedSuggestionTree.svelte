<script lang="ts">
  import { type IEditSuggestion } from "$vscode/src/next-edit/next-edit-types";
  import { calculateDuration, fadeScaleOut } from "./animation-util";
  import { getCodeRollSelection, getCurrentSuggestion } from "../code-roll/code-roll-context";
  import type { OnCodeAction } from "../code-roll/types";
  import Detail from "../detail/Detail.svelte";
  import IconFilePath from "../icon-file-path/IconFilePath.svelte";
  import SuggestionTreeItem from "./SuggestionTreeItem.svelte";
  import { areSameSuggestion } from "./navigation-utils";
  import { buildCodeActions } from "../code-roll/code-roll-util";

  export let onCodeAction: OnCodeAction;
  export let sortedPathSuggestionsMap = new Map<string, IEditSuggestion[]>();
  export let show = true;
  let scrollContainer: HTMLElement;
  const ctx = getCodeRollSelection();
</script>

<ul class="c-suggestion-tree__maximized" class:hidden={!show} bind:this={scrollContainer}>
  {#each sortedPathSuggestionsMap as [path, suggestions] (path)}
    <li transition:fadeScaleOut>
      <Detail
        duration={calculateDuration(suggestions.length)}
        class="c-suggestion-tree-item"
        open={$ctx.isOpen?.[path] ?? true}
        onChangeOpen={(open) => ($ctx.isOpen = { ...$ctx.isOpen, [path]: open })}
      >
        <IconFilePath
          filepath={path}
          slot="summary"
          class="c-suggestion-tree-item__lang-summary"
          value={suggestions}
          {onCodeAction}
          codeActions={suggestions.every((s) => s.state === "accepted")
            ? buildCodeActions("rejectAllInFile" /* , "undoAllInFile" */)
            : buildCodeActions("rejectAllInFile", "acceptAllInFile")}
        />
        <ul class="c-suggestion-tree-item__inner">
          {#each suggestions as suggestion (suggestion.result.suggestionId)}
            <SuggestionTreeItem
              {onCodeAction}
              {suggestion}
              shouldScrollIntoView={areSameSuggestion(suggestion, getCurrentSuggestion($ctx))}
              {scrollContainer}
            />
          {/each}
        </ul>
      </Detail>
    </li>
  {/each}
</ul>

<style>
  .c-suggestion-tree__maximized {
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: column;
    flex: 1;
    list-style: none;
    overflow: auto;
    user-select: none;
  }
  .hidden {
    display: none !important;
  }
  .c-suggestion-tree__maximized > li {
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: column;
    flex: 0;
    list-style: none;
    line-height: var(--augment-tree-list-item-height);
  }
  :global(.c-suggestion-tree-item) {
    --p-1: 0.25rem;
    --p-2: 0.5rem;
    --p-6: 1.5rem;

    overflow: hidden;
    border-color: var(--augment-code-roll-item-border);
  }
  :global(.c-suggestion-tree-item__lang-summary) {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: var(--ds-spacing-1);
    white-space: nowrap;
    overflow: hidden;
  }
  :global(.c-suggestion-tree-item__lang-summary.c-language-icon):before {
    display: inline;
    font-size: 24px;
    padding: 0;
    align-items: center;
  }
  :global(.c-suggestion-tree-item.c-suggestion-tree-item) :global(.c-detail__summary) {
    gap: 0;
    padding: 0 0 0 var(--ds-spacing-2);
    height: var(--augment-tree-list-item-height);
  }
  :global(.c-suggestion-tree-item.c-suggestion-tree-item)
    :global(.c-detail__summary)
    :global(.c-detail__chevron):before {
    color: var(--augment-foreground);
    opacity: 0.5;
  }
  :global(.c-suggestion-tree-item.c-suggestion-tree-item) :global(.c-detail__summary):focus-within {
    background-color: var(--ds-color-neutral-6);
    --parent-bg-color: var(--ds-color-neutral-6);
  }
  .c-suggestion-tree-item__inner {
    overflow: hidden;
    padding-left: 0;
    display: flex;
    list-style: none;
    flex-direction: column;
    flex: 1;
  }
</style>
