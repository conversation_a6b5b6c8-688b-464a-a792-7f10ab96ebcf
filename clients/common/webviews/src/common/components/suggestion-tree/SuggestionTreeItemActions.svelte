<script lang="ts" generics="T extends Action, V extends ActionValue<T>">
  import type { Action, ActionConfig, ActionValue } from "../code-roll/types";
  import ActionButtons from "../code-roll/ActionButtons.svelte";

  export let onCodeAction: ActionValue<V>;
  export let value: ActionValue<T>;
  export let codeActions: ActionConfig[];
</script>

<div class="c-suggestion-tree-item-actions">
  <div class="c-suggestion-tree-item-actions__inner">
    <ActionButtons compact onAction={onCodeAction} actions={codeActions} {value} />
  </div>
</div>

<style>
  :global(:root) {
    --c-suggestion-tree-item-actions-offset: 32px;
  }

  @media (max-width: 600px) {
    :global(:root) {
      --c-suggestion-tree-item-actions-offset: 0;
    }
  }

  .c-suggestion-tree-item-actions {
    display: flex;
    position: absolute;
    right: 0;
    padding: inherit;
    top: 50%;
    transform: translateY(-50%);
    pointer-events: none;
  }

  .c-suggestion-tree-item-actions
    :global(.c-action-buttons)
    :global(.c-icon-btn)
    :global(.c-base-btn) {
    --base-btn-color: var(--augment-code-roll-selection-color, var(--ds-color-neutral-12));
  }

  .c-suggestion-tree-item-actions::before {
    content: "";
    position: relative;
    top: 0;
    bottom: 0;
    width: var(--c-suggestion-tree-item-actions-offset);
    background: linear-gradient(
      to right,
      transparent,
      var(--parent-bg-color, var(--augment-window-background))
    );
    z-index: 0;
  }

  .c-suggestion-tree-item-actions__inner {
    background-color: var(--parent-bg-color, var(--augment-window-background, inherit));
    padding-right: 2px;
    pointer-events: initial;
  }
</style>
