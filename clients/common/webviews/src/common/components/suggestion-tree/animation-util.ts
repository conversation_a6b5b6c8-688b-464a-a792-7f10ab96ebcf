import { linear } from "svelte/easing";
import { type TransitionConfig } from "svelte/transition";

export const clamp = (value: number, min: number, max: number) =>
  Math.min(Math.max(value, min), max);

/**
 * Calculate how long a duration should be given a array length.
 *
 * @param value - number to do the calc from, usually list length.
 * @param itemTimeMs - the amount of time * value (per item)
 * @param minMs - minimum duration
 * @param maxMs - maximum duration
 * @returns
 */
export const calculateDuration = (
  value: number,
  itemTimeMs: number = 30,
  minMs: number = 230,
  maxMs: number = 500,
) => {
  return clamp(value * itemTimeMs, minMs, maxMs);
};

export function fadeScaleOut(
  element: HTMLElement,
  options: TransitionConfig = { duration: 600, easing: linear },
) {
  // Split the duration between fade and scale
  const height = element.getBoundingClientRect().height;
  return {
    ...options,
    /**
     * So we use the number to create our own scale.  first fading
     * to 0, then scaling to 0. it would be nicer to have
     * some sort combinator.
     */
    css(u: number) {
      const opacity = 1 - (u > 0.5 ? 1 - (u - 0.5) * 2 : 1);
      const mHeight = (u < 0.5 ? u * 2 : 1) * height;
      return `max-height: ${mHeight}px; opacity: ${opacity};`;
    },
  };
}
