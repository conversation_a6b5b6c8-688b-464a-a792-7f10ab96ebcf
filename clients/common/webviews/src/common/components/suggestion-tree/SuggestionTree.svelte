<script lang="ts">
  import { type IEditSuggestion } from "$vscode/src/next-edit/next-edit-types";
  import { areSameSuggestion, atIndexSuggestion, indexOfSuggestion } from "./navigation-utils";
  import {
    getCodeRollSelection,
    getCurrentSuggestion,
    getLevel,
  } from "../code-roll/code-roll-context";
  import type { OnCodeAction } from "../code-roll/types";
  import MinimizedSuggestionTree from "./MinimizedSuggestionTree.svelte";
  import MaximizedSuggestionTree from "./MaximizedSuggestionTree.svelte";

  export let onCodeAction: OnCodeAction;
  export let sortedPathSuggestionsMap = new Map<string, IEditSuggestion[]>();
  export let minimized = false;
  let previousNextSuggestion: IEditSuggestion | undefined;
  const ctx = getCodeRollSelection();

  function openIt(suggestion: IEditSuggestion | undefined, open = true) {
    if (!suggestion?.qualifiedPathName.relPath) {
      return;
    }
    $ctx.nextSuggestion = suggestion;
    $ctx.isOpen = {
      ...$ctx.isOpen,
      [suggestion.qualifiedPathName.relPath]: open,
    };
  }

  function upOrDown(e: KeyboardEvent, up?: boolean) {
    e.preventDefault();
    e.stopPropagation();
    const suggestion = getCurrentSuggestion($ctx);
    const suggestionIndex = indexOfSuggestion(sortedPathSuggestionsMap, suggestion);
    const nextFocusedSuggestion = atIndexSuggestion(
      sortedPathSuggestionsMap,
      suggestionIndex + (up ? -1 : 1),
    );
    if (!nextFocusedSuggestion) {
      return;
    }
    if (nextFocusedSuggestion === suggestion) {
      return;
    }
    openIt(nextFocusedSuggestion);
    if (getLevel($ctx) === "active") {
      onCodeAction("active", nextFocusedSuggestion);
    } else {
      onCodeAction("select", nextFocusedSuggestion);
    }
  }

  function handleKeydown(event: KeyboardEvent) {
    switch (event.code) {
      case "KeyZ":
        // Only capture Ctrl-Z or Cmd-Z. This is the default keybinding for undo.
        if (!event.metaKey && !event.ctrlKey) {
          return;
        }
        event.preventDefault();
        event.stopPropagation();
        if (!$ctx.nextSuggestion) {
          return;
        }
        openIt($ctx.nextSuggestion);
        onCodeAction("undo", $ctx.nextSuggestion);
        break;
      case "KeyK":
      case "ArrowUp":
        // Don't capture any modifiers
        if (event.metaKey || event.altKey || event.ctrlKey || event.shiftKey) {
          return;
        }
        upOrDown(event, true);
        break;
      case "KeyJ":
      case "ArrowDown":
        // Don't capture any modifiers
        if (event.metaKey || event.altKey || event.ctrlKey || event.shiftKey) {
          return;
        }
        upOrDown(event);
        break;
      case "ArrowRight":
      case "Space": {
        // Don't capture any modifiers
        if (event.metaKey || event.altKey || event.ctrlKey || event.shiftKey) {
          return;
        }
        event.preventDefault();
        event.stopPropagation();
        const suggestion = getCurrentSuggestion($ctx);
        openIt(suggestion);
        onCodeAction(getLevel($ctx) === "select" ? "active" : "select", suggestion);
        break;
      }
      case "ArrowLeft":
      case "Escape":
        // Don't capture any modifiers
        if (event.metaKey || event.altKey || event.ctrlKey || event.shiftKey) {
          return;
        }
        event.preventDefault();
        event.stopPropagation();
        onCodeAction("dismiss");
        break;
      case "Enter":
        // Don't capture any modifiers
        if (event.metaKey || event.altKey || event.ctrlKey || event.shiftKey) {
          return;
        }
        if (!$ctx.nextSuggestion) {
          return;
        }
        event.preventDefault();
        event.stopPropagation();
        onCodeAction("accept", $ctx.nextSuggestion);
        break;
      case "Backspace":
        // Don't capture any modifiers
        if (event.metaKey || event.altKey || event.ctrlKey || event.shiftKey) {
          return;
        }
        if (!$ctx.nextSuggestion) {
          return;
        }
        event.preventDefault();
        event.stopPropagation();
        onCodeAction("reject", $ctx.nextSuggestion);
        break;
    }
  }

  $: {
    if (
      $ctx.nextSuggestion &&
      getLevel($ctx) === "next" &&
      !areSameSuggestion($ctx.nextSuggestion, previousNextSuggestion)
    ) {
      previousNextSuggestion = $ctx.nextSuggestion;
      openIt($ctx.nextSuggestion);
    }
  }
</script>

<svelte:body on:keydown={handleKeydown} />
<MinimizedSuggestionTree
  {sortedPathSuggestionsMap}
  {onCodeAction}
  show={minimized && !!sortedPathSuggestionsMap.size}
/>
<MaximizedSuggestionTree
  {sortedPathSuggestionsMap}
  {onCodeAction}
  show={!minimized && !!sortedPathSuggestionsMap.size}
/>
<div class="c-suggestion-tree__no-suggestions" class:hidden={sortedPathSuggestionsMap.size}>
  <slot name="no-suggestions" />
</div>

<style>
  :global(.c-suggestion-tree.c-suggestion-tree) {
    flex: 1;
    overflow: hidden;
  }
  :global(.c-suggestion-tree) > :global(.c-detail__content.c-detail__content) {
    flex: 1;
    height: 100%;
    align-items: flex-start;
    overflow: auto;
  }

  :global(.c-icon-file-path.c-suggestion-tree-item__lang-summary) :global(.c-icon-file-path__path) {
    padding-right: var(--ds-spacing-3);
  }

  :global(.c-suggestion-tree) > :global(.c-detail__summary) > :global(.c-detail__chevron):before {
    color: var(--augment-foreground);
    opacity: 0.5;
  }

  :global(.c-suggestion-tree__refresh--refreshing):before {
    vertical-align: middle;
    display: inline-block;
    animation-name: next-edit-suggestions-spin;
    animation-duration: 1.5s;
    animation-iteration-count: 0;
    animation-timing-function: linear;
    animation-fill-mode: forwards;
    margin-top: -0.4em;
  }

  :global(.c-suggestion-tree) {
    --p-1: 0.25rem;
    --p-2: 0.5rem;

    display: flex;
    flex-direction: column;
    margin: 0;
    padding: var(--p-2) 0;
  }
  :global(.c-suggestion-tree.c-suggestion-tree) > :global(.c-detail__summary) {
    padding: var(--p-1);
    border-radius: var(--augment-border-radius);
    flex: 0;
  }
  .hidden {
    display: none !important;
  }
  .c-suggestion-tree__no-suggestions {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: var(--p-2);
    color: var(--augment-foreground);
    height: 100%;
    width: 100%;
    flex: 1;
    opacity: 0.5;
  }
  @keyframes -global-next-edit-suggestions-spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
</style>
