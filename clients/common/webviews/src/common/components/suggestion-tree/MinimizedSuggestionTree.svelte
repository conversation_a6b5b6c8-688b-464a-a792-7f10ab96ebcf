<script lang="ts">
  import type { IEditSuggestion } from "$vscode/src/next-edit/next-edit-types";
  import type { OnCodeAction } from "../code-roll/types";
  import { type SelectionState, selectionState } from "../code-roll/suggestion-util";
  import { getCodeRollSelection, getCurrentSuggestion } from "../code-roll/code-roll-context";
  import { onKey } from "../../utils/keypress";
  import { scrollIntoView } from "./scroll-view-util";
  import { areSameSuggestion } from "./navigation-utils";

  export let onCodeAction: OnCodeAction;
  export let sortedPathSuggestionsMap = new Map<string, IEditSuggestion[]>();
  export let show = true;
  const ctx = getCodeRollSelection();
  let scrollContainer: HTMLElement;

  function styleForSelection(state: SelectionState) {
    let value = "var(--ds-color-neutral-7, currentColor)";
    if (state === "active") {
      value = "var(--augment-code-roll-item-background-active, currentColor)";
    }
    if (state === "select") {
      value = "currentColor";
    }
    if (state === "next") {
      value = "var(--ds-color-neutral-10, currentColor)";
    }
    return `--augment-code-roll-selection-background: ${value}`;
  }
</script>

<div class="c-suggestion-tree__minimized" class:hidden={!show} bind:this={scrollContainer}>
  {#each sortedPathSuggestionsMap.entries() as [path, suggestions] (path)}
    <div class="c-suggestion-tree__file-divider" title={path}></div>
    {#each suggestions as suggestion}
      {@const state = selectionState(suggestion, $ctx, true)}
      <div
        role="button"
        tabindex="0"
        title="{suggestion.lineRange.start + 1}: {suggestion.result.changeDescription}"
        class="c-suggestion-tree__tick-mark {state}"
        style={styleForSelection(state)}
        on:click={() => onCodeAction("select", suggestion)}
        on:keydown={onKey("Space", () => onCodeAction("select", suggestion))}
        use:scrollIntoView={{
          scrollContainer,
          doScroll: areSameSuggestion(suggestion, getCurrentSuggestion($ctx)),
          scrollIntoView: { behavior: "smooth", block: "nearest" },
        }}
      ></div>
    {/each}
  {/each}
</div>

<style>
  .c-suggestion-tree__minimized {
    display: flex;
    flex-direction: column;
    padding: 10% 1px;
    overflow: auto;
    flex: 1;
    user-select: none;
  }
  .hidden {
    display: none !important;
  }
  .c-suggestion-tree__file-divider,
  .c-suggestion-tree__tick-mark {
    padding: 8px 10%;
    height: 3px;
    margin: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    outline: none;
  }
  .c-suggestion-tree__file-divider::after {
    content: "";
    height: 2px;
    width: 100%;
    background: var(--augment-border-color);
  }
  .c-suggestion-tree__tick-mark {
    cursor: pointer;
  }
  .c-suggestion-tree__tick-mark::after {
    content: "";
    width: 30%;
    height: 2px;
    border-radius: 1px;
    transition:
      height 0.3s ease-in-out,
      width 0.3s ease-in-out,
      background-color 0.3s ease-in-out;
    background-color: var(--augment-code-roll-selection-background, currentColor);
  }
  .c-suggestion-tree__tick-mark.active::after,
  .c-suggestion-tree__tick-mark.select::after,
  .c-suggestion-tree__tick-mark.next::after {
    height: 3px;
    width: 50%;
  }
  .c-suggestion-tree__tick-mark:hover {
    background-color: var(--vscode-list-hoverBackground, var(--ds-color-neutral-4, currentColor));
  }
</style>
