import { ChangeType, type IEditSuggestion } from "$vscode/src/next-edit/next-edit-types";
import { clamp } from "./animation-util";

export function getLineRange(suggestion: IEditSuggestion): string {
  let start = suggestion.lineRange.start + 1;
  let stop = suggestion.lineRange.stop + 1;
  return `${start}-${stop}`;
}

export function getPath(suggestion: IEditSuggestion, useFullPath: boolean): string {
  if (!useFullPath) {
    return suggestion.qualifiedPathName.relPath;
  }
  const rootPath = suggestion.qualifiedPathName.rootPath;
  const relPath = suggestion.qualifiedPathName.relPath;
  const separator = rootPath.includes("\\") || relPath.includes("\\") ? "\\" : "/";
  return rootPath + (rootPath.endsWith(separator) ? "" : separator) + relPath;
}
export function indexOfSuggestion(
  map: Map<string, IEditSuggestion[]>,
  suggestion?: IEditSuggestion,
) {
  if (!suggestion?.result.suggestionId) {
    return -1;
  }
  let i = 0;
  for (const s of flattenValuesIt(map)) {
    if (areSameSuggestion(s, suggestion)) {
      return i;
    }
    i++;
  }
  return -1;
}

/** gets the index of the suggestion */
export function atIndexSuggestion(
  map: Map<string, IEditSuggestion[]>,
  index: number,
): IEditSuggestion {
  const all = flattenValues(map);
  const idx = clamp(index, 0, all.length - 1);
  return all[idx];
}

export function areSameSuggestion(a: IEditSuggestion | undefined, b: IEditSuggestion | undefined) {
  if (a?.result.suggestionId === undefined || b?.result.suggestionId === undefined) {
    return false;
  }
  return a.result.suggestionId === b.result.suggestionId;
}

export function sortAndFilterSuggestions(
  suggestions: IEditSuggestion[],
): [string, IEditSuggestion[]][] {
  return sortSuggestions(suggestions.filter((s) => s.changeType !== ChangeType.noop));
}
export function sortSuggestions(suggestions: IEditSuggestion[]): [string, IEditSuggestion[]][] {
  const useFullPath =
    suggestions.length > 1 &&
    suggestions.some(
      (s) => s.qualifiedPathName.rootPath !== suggestions[0].qualifiedPathName.rootPath,
    );
  const suggestionsByFile: Map<string, IEditSuggestion[]> = new Map();
  for (const suggestion of suggestions) {
    const path = getPath(suggestion, useFullPath);
    if (suggestionsByFile.has(path)) {
      suggestionsByFile.get(path)!.push(suggestion);
    } else {
      suggestionsByFile.set(path, [suggestion]);
    }
  }
  // Sort each list of suggestions by line range.
  for (const [, item] of suggestionsByFile) {
    item.sort((a, b) => a.lineRange.start - b.lineRange.start);
  }
  const ret = Array.from(suggestionsByFile.entries());
  // Sort files by path.
  return ret.sort(([a], [b]) => a.localeCompare(b));
}

function* combine<V>(...a: Iterable<V>[]): Iterable<V> {
  for (const v of a) {
    yield* v;
  }
}

export function nextSuggestion(
  map: Map<string, IEditSuggestion[]>,
  suggestion?: IEditSuggestion,
): [number, IEditSuggestion | undefined] {
  if (suggestion == null) {
    return [0, map.values().next().value?.[0]] as const;
  }
  const entries = map.values();
  let it = entries.next();
  let i = 0;
  while (!it.done) {
    const value = it.value ?? [];
    i += value.length;
    if (value.some((s) => areSameSuggestion(s, suggestion))) {
      it = entries.next();
      if (it.done) {
        break;
      }
      return [i, it.value?.[0]] as const;
    }

    it = entries.next();
  }
  return previousSuggestion(map, suggestion);
}

const flattenValuesIt = <T>(map: Map<unknown, T[]>) => combine(...map.values());
const flattenValues = <T>(map: Map<unknown, T[]>) => Array.from(flattenValuesIt(map));

export function previousSuggestion(
  map: Map<string, IEditSuggestion[]>,
  suggestion?: IEditSuggestion,
): [number, IEditSuggestion | undefined] {
  if (suggestion == null) {
    return [0, map.values().next().value?.[0]] as const;
  }

  const entries = flattenValues(map);

  for (let i = 1, c = 0; i < entries.length; i++, c++) {
    if (areSameSuggestion(entries[i], suggestion)) {
      return [c, entries[c]] as const;
    }
  }
  return [0, undefined] as const;
}

export function stopEvent<T extends (...args: any[]) => any>(fn?: T, ...args: Parameters<T>) {
  return function onStop(e: Event) {
    e.preventDefault();
    e.stopPropagation();
    e.stopImmediatePropagation();
    fn?.(...args);
  };
}
