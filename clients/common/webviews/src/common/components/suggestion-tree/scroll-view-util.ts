export type ScrollViewOptions = Partial<ScrollViewAllOptions>;

interface ScrollViewAllOptions {
  scrollIntoView: Pick<ScrollIntoViewOptions, "behavior" | "block">;
  scrollDelayMS: number;
  doScroll: boolean;
  useSmartBlockAlignment: boolean;
  // default scrollContainer is the document, which is not ideal, you should probably specify this
  scrollContainer: HTMLElement;
}

const defaultOptions: ScrollViewAllOptions = {
  scrollContainer: document.documentElement,
  scrollIntoView: { behavior: "smooth", block: "nearest" } as const,
  scrollDelayMS: 100,
  useSmartBlockAlignment: true,
  doScroll: true,
} as const;

/**
 * This will scroll a element into view, if it is not already visible.
 * Only supports vertical scroll and does not support "inline" scroll option
 *
 * If a user scrolls outside of view, so sad, it won't do it again until
 * an update gets called.
 */
export function scrollIntoView(el: HTMLElement, optionsArg: ScrollViewOptions = defaultOptions) {
  let timeoutId: ReturnType<typeof setTimeout> | undefined = undefined;

  let options: ScrollViewAllOptions = Object.assign({}, defaultOptions, optionsArg);

  function update(newOptionsArg: ScrollViewOptions) {
    let { doScroll, scrollIntoView, scrollDelayMS, useSmartBlockAlignment, scrollContainer } =
      Object.assign({}, options, newOptionsArg);

    if (doScroll) {
      if (useSmartBlockAlignment) {
        if (
          el.getBoundingClientRect().height >
          (scrollContainer?.getBoundingClientRect().height ?? Infinity)
        ) {
          scrollIntoView = Object.assign({}, scrollIntoView, { block: "start" });
        }
      }
      timeoutId = setTimeout(() => {
        const boundingRect = el.getBoundingClientRect();
        // element is not fully rendered yet
        if (
          boundingRect.bottom === 0 &&
          boundingRect.top === 0 &&
          boundingRect.height === 0 &&
          boundingRect.width === 0
        ) {
          return;
        }

        const scrollTop = computeScrollTop(
          el,
          scrollContainer,
          scrollIntoView?.block ?? defaultOptions.scrollIntoView.block,
        );
        scrollContainer.scrollTo({
          top: scrollTop,
          behavior: scrollIntoView?.behavior,
        });
      }, scrollDelayMS);
    }
  }

  // Initial update
  update(options);

  return {
    update,
    destroy() {
      clearTimeout(timeoutId);
    },
  };
}

function computeScrollTop(
  el: HTMLElement,
  scrollContainer: HTMLElement,
  block: ScrollIntoViewOptions["block"],
): number {
  const boundingRect = el.getBoundingClientRect();
  const containerRect = scrollContainer.getBoundingClientRect();

  if (block === "nearest") {
    if (boundingRect.bottom > containerRect.bottom) {
      block = "end";
    } else if (boundingRect.top < containerRect.top) {
      block = "start";
    } else {
      return scrollContainer.scrollTop; // don't scroll
    }
  }

  if (boundingRect.height > containerRect.height) {
    return scrollContainer.scrollTop + boundingRect.top;
  } else if (block === "start") {
    return scrollContainer.scrollTop + boundingRect.top;
  } else if (block === "end") {
    return scrollContainer.scrollTop + boundingRect.bottom - containerRect.height;
  } else {
    return (
      scrollContainer.scrollTop +
      boundingRect.top -
      (containerRect.height - boundingRect.height) / 2
    );
  }
}
