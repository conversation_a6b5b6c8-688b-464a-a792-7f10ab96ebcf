<script lang="ts">
  import { fadeScaleOut } from "./animation-util";
  import { scrollIntoView } from "./scroll-view-util";
  import { buildCodeActions } from "../code-roll/code-roll-util";
  import SuggestionTreeItemButton from "./SuggestionTreeItemButton.svelte";
  import SuggestionTreeItemActions from "./SuggestionTreeItemActions.svelte";
  import { SuggestionState, type IEditSuggestion } from "$vscode/src/next-edit/next-edit-types";
  import type { OnCodeAction } from "../code-roll/types";
  import { selectionState, styleForSelection } from "../code-roll/suggestion-util";
  import { getCodeRollSelection } from "../code-roll/code-roll-context";

  export let suggestion: IEditSuggestion;
  export let onCodeAction: OnCodeAction;
  export let shouldScrollIntoView = false;
  export let scrollContainer: HTMLElement;
  let isHovered = false;
  let ctx = getCodeRollSelection();

  export let state = selectionState(suggestion, $ctx, true);
  $: {
    state = selectionState(suggestion, $ctx, true);
  }
  $: codeActions =
    suggestion.state === SuggestionState.accepted
      ? buildCodeActions("reject", "undo")
      : buildCodeActions("reject", "accept");
</script>

<li
  transition:fadeScaleOut
  class="c-suggestion-tree-item__suggestion"
  use:scrollIntoView={{
    scrollContainer,
    doScroll: shouldScrollIntoView,
    scrollIntoView: { behavior: "smooth", block: "nearest" },
  }}
  on:mouseenter={() => (isHovered = true)}
  on:mouseleave={() => (isHovered = false)}
  style={styleForSelection(state)}
>
  <SuggestionTreeItemButton {suggestion} {onCodeAction} />
  {#if isHovered}
    <SuggestionTreeItemActions {onCodeAction} {codeActions} value={suggestion} />
  {/if}
</li>

<style>
  .c-suggestion-tree-item__suggestion {
    --parent-bg-color: var(--augment-code-roll-selection-background);
    outline: none;
    gap: 0;
    position: relative;
    display: flex;
    overflow: hidden;
    padding: 0 1px;
  }
</style>
