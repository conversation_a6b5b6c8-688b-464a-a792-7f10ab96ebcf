<script lang="ts">
  import { type IEditSuggestion } from "$vscode/src/next-edit/next-edit-types";
  import { onKey } from "../../utils/keypress";
  import PencilIcon from "../pencil-icons/PencilIcon.svelte";
  import { getPath, getLineRange, areSameSuggestion } from "./navigation-utils";
  import { selectionState } from "../code-roll/suggestion-util";
  import { getCodeRollSelection } from "../code-roll/code-roll-context";
  import type { OnCodeAction } from "../code-roll/types";

  export let suggestion: IEditSuggestion;
  export let onCodeAction: OnCodeAction;

  const ctx = getCodeRollSelection();
  let state = selectionState(suggestion, $ctx, true);
  $: {
    state = selectionState(suggestion, $ctx, true);
  }

  function doDoubleClick() {
    // TODO - it seems like this dismiss never took effect in the old mouseDown handler
    // and it doesnt work now either. Consider just removing this part.
    if (areSameSuggestion(suggestion, $ctx.activeSuggestion)) {
      onCodeAction("dismiss");
    } else {
      onCodeAction("active", suggestion);
    }
  }

  function doSingleClick() {
    onCodeAction("select", suggestion);
  }
</script>

<button
  on:click={doSingleClick}
  on:dblclick={doDoubleClick}
  on:keydown={onKey("Space", () => onCodeAction("select", suggestion))}
  tabindex={0}
  title="{getPath(suggestion, true)}:{getLineRange(suggestion)}"
  class="c-suggestion-tree-item-button {state}"
>
  <PencilIcon mask={state !== "none"} {suggestion} />
  <span class="c-suggestion-tree-item-button__description">
    <span class="c-suggestion-tree-item__description__linenumber">
      {suggestion.lineRange.start + 1}:
    </span>
    <span class="c-suggestion-tree-item__description__path">
      {suggestion.result.changeDescription}
    </span>
  </span>
</button>

<style>
  .c-suggestion-tree-item-button {
    background: var(--augment-code-roll-selection-background, transparent);
    color: var(--augment-code-roll-selection-color, var(--augment-foreground));
    outline: unset;
    border: unset;
    width: 100%;
    padding-left: 40px;
    position: relative;
    gap: 0.5em;
    cursor: pointer;
    align-items: center;
    display: grid;
    grid-template-columns: auto 1fr auto;
    cursor: pointer;
    text-align: start;
    white-space: nowrap;
    padding-block: 0;
    line-height: var(--augment-tree-list-item-height);
    height: var(--augment-tree-list-item-height);
  }
  .c-suggestion-tree-item__description__path {
    display: inline;
    padding: 0;
    gap: 0.25em;
    text-align: left;
    white-space: nowrap;
    direction: ltr;
    overflow: hidden;
    text-overflow: ellipsis;
    max-height: 4em;
  }
  .c-suggestion-tree-item-button__description {
    display: flex;
    flex: 1;
    overflow: hidden;
    flex-wrap: nowrap;
  }
</style>
