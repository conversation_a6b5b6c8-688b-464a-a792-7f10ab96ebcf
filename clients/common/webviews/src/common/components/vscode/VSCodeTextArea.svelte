<script lang="ts">
  export let textarea: HTMLTextAreaElement | undefined = undefined;
  export let value: string = "";
  export let placeholder: string = "";
  export let rows: number = 1;
  export let resize: "none" | "both" | "horizontal" | "vertical" = "none";
  export let disabled: boolean = false;
  let clazz = "";
  export { clazz as class };
</script>

<textarea
  class={`c-textarea ${clazz}`}
  class:c-textarea--resize-none={resize === "none"}
  class:c-textarea--resize-both={resize === "both"}
  class:c-textarea--resize-horizontal={resize === "horizontal"}
  class:c-textarea--resize-vertical={resize === "vertical"}
  {disabled}
  {placeholder}
  {rows}
  bind:this={textarea}
  bind:value
  on:keydown
  on:input
></textarea>

<style>
  .c-textarea {
    display: inline-block;
    font-family: var(--augment-font-family);
    outline: none;
    user-select: none;

    box-sizing: border-box;
    position: relative;
    background-color: transparent;
    border-radius: var(--ds-radius-2);
    border: 1px solid var(--ds-color-neutral-a5);
    font-size: var(--augment-font-size);
    padding: 9px;
    width: 100%;
    min-width: 100px;
  }

  .c-textarea:focus-within:not([disabled]) {
    border-color: var(--ds-color-neutral-a6);
  }

  .c-textarea:hover,
  .c-textarea:focus-visible,
  .c-textarea:disabled,
  .c-textarea:active {
    outline: none;
  }
  /* We don't want to style the scrollbars in IntelliJ since it already has its own overrides */
  :global(body:not(.augment-intellij)) .c-textarea::-webkit-scrollbar {
    width: 10px;
    height: 10px;
  }
  :global(body:not(.augment-intellij)) .c-textarea::-webkit-scrollbar-corner {
    background: var(--vscode-input-background-internal);
  }
  :global(body:not(.augment-intellij)) .c-textarea::-webkit-scrollbar-thumb {
    background: var(--vscode-scrollbarSlider-background);
  }
  :global(body:not(.augment-intellij)) .c-textarea::-webkit-scrollbar-thumb:hover {
    background: var(--vscode-scrollbarSlider-hoverBackground);
  }
  :global(body:not(.augment-intellij)) .c-textarea::-webkit-scrollbar-thumb:active {
    background: var(--vscode-scrollbarSlider-activeBackground);
  }

  .c-textarea--resize-none {
    resize: none;
  }
  .c-textarea--resize-both {
    resize: both;
  }
  .c-textarea--resize-horizontal {
    resize: horizontal;
  }
  .c-textarea--resize-vertical {
    resize: vertical;
  }

  .c-textarea:disabled {
    cursor: not-allowed;
    opacity: 0.4;
    border-color: var(--vscode-dropdown-border-internal);
  }
</style>
