<script lang="ts">
  export let index: number = 0;
  export let selected: boolean = false;
  let clazz = "";
  export { clazz as class };
</script>

<div
  class={`c-option-item ${clazz}`}
  class:c-option-item--selected={selected}
  role="button"
  tabindex={index}
  on:click
  on:keydown
  on:mousedown
  on:mouseup
>
  <slot />
</div>

<style>
  .c-option-item {
    --augment-option-item-color: var(
      --vscode-list-activeSelectionForeground,
      var(--intellij-list-selectionForeground)
    );
    --augment-option-item-background: var(
      --vscode-list-activeSelectionBackground,
      var(--intellij-list-selectionBackground)
    );
    --augment-option-item-background-hover: var(
      --vscode-list-hoverBackground,
      var(--intellij-list-selectionInactiveBackground)
    );
    --augment-option-item-color-hover: var(
      --vscode-list-hoverForeground,
      var(--intellij-list-foreground)
    );

    font-family: var(--augment-font-family);
    border: 1px solid transparent;
    color: var(--augment-foreground);
    cursor: pointer;
    fill: currentColor;
    font-size: var(--augment-font-size);
    outline: none;
    overflow: hidden;
    padding: 0 2px 1px;
    user-select: none;
    white-space: nowrap;
  }

  .c-option-item:focus-visible {
    border-color: var(--vscode-focus-border-color);
  }

  .c-option-item--selected,
  .c-option-item:focus-visible {
    background: var(--augment-option-item-background);
    color: var(--augment-option-item-color);
  }

  .c-option-item:hover,
  .c-option-item--selected:hover,
  .c-option-item:focus-visible:hover {
    background: var(--augment-option-item-background-hover);
    color: var(--augment-option-item-color-hover);
  }

  .c-option-item:disabled {
    cursor: not-allowed;
    opacity: 0.4;
  }

  .c-option-item:disabled:hover {
    background-color: inherit;
  }

  .c-option-item :global(.material-symbols-outlined) {
    margin: 0;
  }
</style>
