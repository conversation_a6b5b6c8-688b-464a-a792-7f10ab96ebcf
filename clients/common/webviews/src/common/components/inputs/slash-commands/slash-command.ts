import { Node } from "@tiptap/core";
import Suggestion, { type SuggestionOptions } from "@tiptap/suggestion";
import { PluginKey } from "@tiptap/pm/state";

export type SlashCommandOptions<SuggestionItem = any> = {
  suggestion: Omit<SuggestionOptions<SuggestionItem>, "editor">;
};

/**
 * Extension to support slash commands.
 */
export const slashCommand = Node.create<SlashCommandOptions>({
  name: "slash-command",

  addProseMirrorPlugins() {
    return [
      Suggestion({
        char: "/",
        pluginKey: new PluginKey("slash-command"),
        editor: this.editor,
        ...this.options.suggestion,
      }),
    ];
  },
});
