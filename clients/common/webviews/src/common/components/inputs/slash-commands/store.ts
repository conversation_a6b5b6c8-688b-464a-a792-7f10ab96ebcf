import type { SvelteComponent } from "svelte";
import { get, writable, type Writable } from "svelte/store";
import type { Node } from "@tiptap/core";
import { slashCommand, type SlashCommandOptions } from "./slash-command";
import type { SuggestionKeyDownProps, SuggestionProps } from "@tiptap/suggestion";

import type { CommandArgs, ITipTapExtensionProvider } from "../types";
import type { ISlashCommandOptionData } from "$common-webviews/src/apps/chat/types/slash-command-list";

// A top-level interface describing the slash-command suggestions pop-up
// information
export interface ISlashCommandSuggestionData {
  // The current visible options for the slash-command
  items?: ISlashCommandOptionData[] | undefined;
  // The current props for the slash-command, set by the lifecycle hooks in TipTap
  props?: SuggestionProps<ISlashCommandOptionData>;
  // The index of the currently selected item
  selectedIdx: number;
}

export interface IRenderOptions {
  suggestionItem: typeof SvelteComponent<{ option: ISlashCommandOptionData }>;
}

export interface ISlashCommandStoreProps {
  render: IRenderOptions;
  onSelectItem: (item: ISlashCommandOptionData, isDirectSelect: boolean) => boolean;
  getSuggestions: (query: string | undefined) => ISlashCommandOptionData[];
  onKeyboardDown?: (props: SuggestionKeyDownProps) => boolean;
  onExit?: () => void;
}

/**
 * A class that stores the state of the slash-command component.
 */
export class SlashCommandStore implements ITipTapExtensionProvider {
  // A TipTap `slashCommand` node that is passed into TipTap to render the component.
  private _slashCommand: Node<SlashCommandOptions, ISlashCommandOptionData>;

  // The current options for the slash-command.
  private _slashCommandSuggestionData: Writable<ISlashCommandSuggestionData | null> =
    writable(null);

  constructor(private props: ISlashCommandStoreProps) {
    // Pull out const references for all of the functions we want to use in the slash-command extension
    const getSuggestions = ({ query }: { query: string }): ISlashCommandOptionData[] => {
      return this.props.getSuggestions(query);
    };
    const setSuggestionProps = this.setSuggestionProps;
    const onExitSuggestion = this.onExitSuggestion;
    const onSelectItem = this.props.onSelectItem;
    const onSuggestionKeyDown = this.onSuggestionKeyDown;

    this._slashCommand = slashCommand.extend({
      // Add additional options to the slash-command object
      addOptions(): SlashCommandOptions {
        const parentOpts = this.parent?.();
        return {
          // Add the suggestion plugin options to the slash-command plugin
          suggestion: {
            ...parentOpts?.suggestion,
            allowedPrefixes: [],
            items: getSuggestions,
            command: ({ props: optionItem }: CommandArgs<ISlashCommandOptionData>) => {
              onSelectItem(optionItem, false);
            },
            // These are hooks that are called by TipTap whenever renders need to occur.
            // These effectively notify our system when certain events are happening in TipTap,
            // and allow us to update the actual visual state accordingly
            render: () => ({
              onStart: setSuggestionProps,
              onUpdate: setSuggestionProps,
              onKeyDown: onSuggestionKeyDown,
              onExit: onExitSuggestion,
            }),
          },
        };
      },
    });
  }

  public getAllItems = (): ISlashCommandOptionData[] => {
    return this.props.getSuggestions(undefined);
  };

  public get tipTapExtension(): Node<SlashCommandOptions, ISlashCommandOptionData> {
    return this._slashCommand;
  }

  public get slashCommandSuggestionsData(): Writable<ISlashCommandSuggestionData | null> {
    return this._slashCommandSuggestionData;
  }

  public get suggestionItem(): typeof SvelteComponent<{
    option: ISlashCommandOptionData;
  }> {
    return this.props.render.suggestionItem;
  }

  // Trigger a selection programmatically on the slash-command.
  public selectItem = (item: ISlashCommandOptionData, isDirectSelect: boolean = false): void => {
    this.props.onSelectItem(item, isDirectSelect);
  };

  // Set the suggestion props for the slash-command. We need to do this since we need a way for TipTap
  // to notify us of changes in TipTap, and for us to store them for our own renderers to
  // decide what to render and how to show things.
  private setSuggestionProps = (props: SuggestionProps<ISlashCommandOptionData>): void => {
    this._slashCommandSuggestionData.update((currData) => {
      return {
        selectedIdx: 0,
        ...currData,
        props,
        items: props.items,
      };
    });
  };

  // When TipTap tells us to stop rendering, clear the slash-command data and call the onExit callback
  private onExitSuggestion = (): void => {
    this._slashCommandSuggestionData.set(null);
    this.props.onExit?.();
  };

  /**
   * This function provides the keyboard handler for navigating through the suggestion menu
   * using only the keyboard.
   *
   * @param props: the event props, passed in from TipTap's suggestions callbacks
   * @returns
   */
  private onSuggestionKeyDown = (props: SuggestionKeyDownProps): boolean => {
    const suggestionData = get(this._slashCommandSuggestionData);
    if (suggestionData === null) {
      return false;
    }

    const { items, props: suggestionProps, selectedIdx } = suggestionData ?? {};

    // Handle the case with no suggestions
    if (!items || items.length === 0 || !suggestionProps) {
      // Still eat Enter so that we don't send the chat
      // which contains an incorrect command
      switch (props.event.key) {
        case "Enter":
          props.event.stopPropagation();
          return true;
      }
      return false;
    }

    const selectedItem = items?.[selectedIdx];
    if (this.props.onKeyboardDown !== undefined && this.props.onKeyboardDown(props)) {
      return true;
    }

    switch (props.event.key) {
      case "ArrowDown":
        this._slashCommandSuggestionData.set({
          ...suggestionData,
          selectedIdx: (selectedIdx + 1) % items.length,
        });
        return true;
      case "ArrowUp":
        this._slashCommandSuggestionData.set({
          ...suggestionData,
          selectedIdx: (items.length + selectedIdx - 1) % items.length,
        });
        return true;
      case "Tab":
        suggestionProps?.command(selectedItem);
        return true;
      case "Enter":
        props.event.stopPropagation();
        suggestionProps?.command(selectedItem);
        return true;
      case " ":
        if (selectedItem.id === suggestionData.props?.query) {
          props.event.stopPropagation();
          suggestionProps?.command(selectedItem);
          return true;
        }
    }
    return false;
  };
}
