import { type Editor } from "@tiptap/core";
import { type Node as ProseMirrorNode } from "prosemirror-model";

export function getMentionNodes(e: Editor): ProseMirrorNode[] {
  let mentionNodes: ProseMirrorNode[] = [];
  e.$doc.content.descendants((node) => {
    if (node.type.name === "mention") {
      mentionNodes.push(node);
    }
  });
  return mentionNodes;
}

/**
 * Finds new mentions in the current document that didn't exist in the previous document
 * @param currentDoc The current document state
 * @param previousDoc The previous document state
 * @param callback Called for each new mention found
 */
export function findNewMentions(
  currentDoc: ProseMirrorNode,
  previousDoc: ProseMirrorNode | null,
  callback: (node: ProseMirrorNode, pos: number) => void,
): void {
  // Traverse all nodes in the current document
  currentDoc.descendants((node, pos) => {
    if (node.type.name === "mention") {
      let existedBefore = false;

      // Search in the previous doc for the same mention node
      if (previousDoc !== null && node) {
        const docSizeDiff: number = Math.abs(currentDoc.nodeSize - previousDoc.nodeSize);
        // Calculate the size of the previous document, accounting for wrapping tokens
        const docSize: number = previousDoc.nodeSize - 2;
        // Define a search buffer to look around the current position
        const searchBuffer: number = docSizeDiff + node.nodeSize + 1;

        // Get the start and end positions to search in the previous doc
        const startPos: number = Math.max(0, pos - searchBuffer);
        const endPos: number = Math.min(docSize, pos + searchBuffer);
        // Search for the mention node in the previous document
        previousDoc.nodesBetween(startPos, endPos, (oldNode) => {
          // If a matching mention node is found, mark it as existing and stop searching
          if (oldNode.type.name === "mention" && oldNode.attrs.id === node.attrs.id) {
            existedBefore = true;
            return false; // Stop searching
          }
        });
      }

      // If the mention is new (didn't exist before), call the callback function
      if (!existedBefore) {
        callback(node, pos);
      }
    }
  });
}
