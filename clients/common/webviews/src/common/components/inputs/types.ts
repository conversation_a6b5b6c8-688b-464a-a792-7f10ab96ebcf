import {
  type AnyExtension,
  type Editor,
  type JSONContent,
  type KeyboardShortcutCommand,
  type Range,
} from "@tiptap/core";
import type { Node as ProseMirrorNode } from "prosemirror-model";
import type { ComponentType, SvelteComponent } from "svelte";
import type { Action } from "svelte/action";
import type { Readable } from "svelte/store";

export interface IMentionable {
  label: string; // The diplay name
  id: string; // A unique identifier
  name?: string; // The serialized text name for the LLM. Defaults to id
}

// TipTap command arguments type
export interface CommandArgs<TOption extends IMentionable> {
  editor: Editor;
  range: Range;
  props: TOption;
}

export interface IContentChangedFn {
  (content: { text: string; json: JSONContent | undefined }): void;
}

export interface IFocusChangedFn {
  (isFocused: boolean): void;
}

export interface IEditableChangedFn {
  (isEditable: boolean): void;
}

export interface ISetupFn {
  (): void;
}

export interface IDisposeFn {
  (): void;
}

export interface ITipTapExtensionProvider {
  tipTapExtension: AnyExtension;
}

export interface ISvelteRenderActionProvider {
  svelteRenderAction: Action<HTMLDivElement>;
}

/**
 * IEditorConstructorOptions defines the configuration for initializing an EditorView.
 * This interface is crucial for setting up the TipTap editor environment.
 */
export interface IEditorConstructorOptions {
  svelteComponent?: ComponentType<SvelteComponent<{ view: IEditorView }>>;
  content?: string | JSONContent | JSONContent[];
  placeholder?: string;
  editable?: boolean;
  focusOnInit?: boolean;
  extensionProviders?: ITipTapExtensionProvider[];
  svelteRenderers?: ISvelteRenderActionProvider[];
  /**
   * Custom text serializers for specific node types.
   * This is useful for controlling how complex nodes are converted to plain text.
   */
  textSerializers?: Record<string, ({ node }: { node: ProseMirrorNode }) => string>;
  /** Custom keyboard shortcuts to add to the editor */
  keyboardShortcuts?: Record<string, KeyboardShortcutCommand>;

  /**
   * Lifecycle listeners attached during initialization.
   * These listeners persist for the lifetime of the EditorView instance.
   */
  onContentChanged?: IContentChangedFn;
  onFocusChanged?: IFocusChangedFn;
  onEditableChanged?: IEditableChangedFn;
  onFocus?: () => void;
  onBlur?: () => void;
}

/**
 * IEditorView defines the public interface for interacting with the Rich Text Input.
 * It provides methods for managing content, handling events, and controlling editor state.
 */
export interface IEditorView extends ISvelteRenderActionProvider {
  /** The DOM element containing the editor */
  domElement: HTMLDivElement;

  // Content lifecycle methods
  clearContent: () => void;
  getContent: () => string;
  getJsonContent: () => JSONContent;
  setContent: (content: string | JSONContent | JSONContent[]) => void;

  // Event handling methods
  onContentChanged: (cb: IContentChangedFn) => IDisposeFn;
  onFocusChanged: (cb: IFocusChangedFn) => IDisposeFn;
  onEditableChanged: (cb: IEditableChangedFn) => IDisposeFn;
  onFocus: (cb: () => void) => IDisposeFn;
  onBlur: (cb: () => void) => IDisposeFn;

  // Editor state modification methods
  focus: () => void;
  blur: () => void;
  scrollToCursor: () => void;
  setEditable: (editable: boolean) => void;

  // Reactive editor state properties
  isEditable: Readable<boolean>;
  isFocused: Readable<boolean>;
}
