<script lang="ts">
  import Diff from "./diff/Diff.svelte";
  import { type IEditSuggestion } from "$vscode/src/next-edit/next-edit-types";
  import { noop } from "./code-roll-util";
  import type { ActionConfig, OnCodeAction } from "./types";
  import { type IQualifiedPathName } from "@augment-internal/sidecar-libs/src/workspace/workspace-types";
  import { resolveLanguage } from "../language-icons/language-icon";
  import SpinnerAugment from "../../../design-system/components/SpinnerAugment.svelte";

  export let suggestions: IEditSuggestion[];
  export let filepath: IQualifiedPathName;
  export let originalCode: string;
  export let loaded = false;
  export let language: string = resolveLanguage(filepath.relPath);
  export let onCodeAction: OnCodeAction = noop;
  export let codeActions: ActionConfig[] = [];
  export let scrollContainer: HTMLElement | undefined = undefined;
</script>

<div class="c-code-roll-item-diff">
  {#if loaded}
    <Diff
      {onCodeAction}
      {codeActions}
      {suggestions}
      {originalCode}
      {language}
      path={filepath.relPath}
      {scrollContainer}
    />
  {:else}
    <div class="c-code-roll-item__loading">
      <SpinnerAugment />
    </div>
  {/if}
</div>

<style>
  .c-code-roll-item-diff {
    display: flex;
    flex-direction: column;
    flex: 1;
    border-radius: var(--augment-code-roll-item-radius);
    border-top-left-radius: 0;
    border-top-right-radius: 0;
    border-top: none;
  }

  .c-code-roll-item-diff :global(.monaco-editor) {
    --vscode-editor-background: var(--augment-code-roll-item-background);
    --vscode-input-background: var(--augment-code-roll-item-background);
    --vscode-editorGutter-background: var(--augment-code-roll-item-background);
    background-color: unset;
  }
  .c-code-roll-item__loading {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    align-self: center;
    height: 100%;
    width: 100%;
  }
</style>
