import { SuggestionState, type IEditSuggestion } from "$vscode/src/next-edit/next-edit-types";

import type * as Monaco from "monaco-editor/esm/vs/editor/editor.api";

import { calculateSuggestionLines } from "../code-roll-util";

// Helper function to handle the common logic
function processSuggestions(
  code: string,
  suggestions: IEditSuggestion[],
  getRemoveLength: (suggestion: IEditSuggestion) => number,
  getInsertCode: (suggestion: IEditSuggestion) => string | undefined,
) {
  const chars = code.split("");
  // Apply from the bottom of the file so the line numbers don't change
  const sorted = suggestions.toSorted(
    ({ lineRange: { start: a } }, { lineRange: { start: b } }) => b - a,
  );

  for (const suggestion of sorted) {
    const spliceArgs: Parameters<typeof chars.splice> = [
      suggestion.result.charStart,
      getRemoveLength(suggestion),
    ];

    const insertCode = getInsertCode(suggestion);
    if (insertCode) {
      spliceArgs.push(...insertCode.split(""));
    }

    chars.splice(...spliceArgs);
  }

  return chars.join("");
}

export function applySuggestions(originalCode: string, suggestions: IEditSuggestion[]) {
  return processSuggestions(
    originalCode,
    suggestions,
    (suggestion) => suggestion.result.charEnd - suggestion.result.charStart,
    (suggestion) => suggestion.result.suggestedCode,
  );
}

export function undoSuggestions(currentCode: string, suggestions: IEditSuggestion[]) {
  return processSuggestions(
    currentCode,
    suggestions,
    (suggestion) => suggestion.result.suggestedCode.split("").length,
    (suggestion) => suggestion.result.existingCode,
  );
}
/**
 * Just a little cache busting, id gen.
 */
export const nextId = (
  (id = 0) =>
  () =>
    Date.now() + "-" + id++
)();

/**
 *
 * Calculate the number of visible lines in a diff view based on the suggestions and options. It attempts
 * to take into account the verying size of the code expansion lines.  The number of those depends on
 * where the diff is relative to the file and the number of suggestions.
 *
 */

export function calculateVisibleLines(
  suggestions: IEditSuggestion[],
  {
    enabled = true,
    revealLineCount = 5,
    minimumLineCount = 3,
    contextLineCount = 5,
  }: Monaco.editor.IStandaloneDiffEditorConstructionOptions["hideUnchangedRegions"] = {
    enabled: true,
    revealLineCount: 5,
    minimumLineCount: 3,
    contextLineCount: 5,
  },
  totalLines: number,
  lineChangesBySuggestion: Map<IEditSuggestion, Monaco.editor.ILineChange> | undefined,
): {
  lines: number;
  decorations: number;
  hasTopDecorations: boolean;
} {
  const ret = { lines: 0, decorations: 0, hasTopDecorations: false };
  if (!suggestions.length || !enabled) {
    return ret;
  }
  let decorations = 0;
  let hasTopDecorations = false;
  // Sort suggestions by start line to process them in order
  const sortedSuggestions = [...suggestions].sort((a, b) => a.lineRange.start - b.lineRange.start);

  let visibleLines = 1;
  for (let i = 0, n = 1; i < sortedSuggestions.length; i++, n++) {
    const suggestion = sortedSuggestions[i],
      next = sortedSuggestions[n];
    visibleLines += Math.min(suggestion.lineRange.start + 1, revealLineCount);
    const diffSize = lineChangesBySuggestion?.get(suggestion)
      ? // If we have a line change from monaco, use that to calculate the size of the diff.
        normalizeRange(lineChangesBySuggestion.get(suggestion)!).lineCount
      : calculateSuggestionLines(suggestion);
    visibleLines += diffSize;
    if (suggestion.lineRange.start - revealLineCount > 1) {
      hasTopDecorations = true;
      decorations++;
    } else if (suggestion.lineRange.start - revealLineCount === 1) {
      // This is a little tricky - (empirically) monaco decides that if there is one line left
      // it will show the line instead of the hidden lines widget, so we add the line instead of decorations.
      visibleLines++;
    }
    if (next) {
      const betweenDiff = next.lineRange.start - suggestion.lineRange.start;

      if (betweenDiff > contextLineCount + revealLineCount) {
        decorations++;
        visibleLines += contextLineCount;
        visibleLines += revealLineCount;
      } else {
        visibleLines += betweenDiff;
      }
    } else {
      if (suggestion.lineRange.stop < totalLines) {
        decorations++;
        visibleLines += Math.min(totalLines - suggestion.lineRange.stop, revealLineCount);
      } else {
        visibleLines += contextLineCount;
        visibleLines += revealLineCount;
      }
    }
  }

  return { lines: Math.max(visibleLines, minimumLineCount), decorations, hasTopDecorations };
}

// NOTE you must dispose of the returned models.
export const applyChanges = (
  editorInstance: Monaco.editor.IStandaloneDiffEditor,
  suggestions: IEditSuggestion[],
  originalCode: string,
  language: string,
  path: string,
  $monaco: typeof Monaco,
): Monaco.editor.ITextModel[] => {
  if (!(editorInstance && originalCode)) {
    return [];
  }

  // any suggestions that have already been applied should be undone to get the code into its original state.
  originalCode = undoSuggestions(
    originalCode,
    suggestions.filter((s) => s.state === SuggestionState.accepted),
  );
  const modifiedCode = applySuggestions(originalCode, suggestions);
  editorInstance.getModel()?.original?.dispose();
  editorInstance.getModel()?.modified?.dispose();
  const originalModel = $monaco.editor.createModel(
    originalCode,
    language,
    $monaco.Uri.parse("file://" + path + `#${nextId()}`),
  );
  const modifiedModel = $monaco.editor.createModel(
    modifiedCode,
    language,
    $monaco.Uri.parse("file://" + path + `#${nextId()}`),
  );
  editorInstance.setModel({
    original: originalModel,
    modified: modifiedModel,
  });
  return [originalModel, modifiedModel];
};

export function suggestionToKey(suggestion: IEditSuggestion) {
  return `${suggestion.requestId}#${suggestion.result?.suggestionId}`;
}

export function cacheKeyForSuggestions(suggestions: IEditSuggestion[]) {
  return suggestions.map(suggestionToKey).join(":");
}

type Range = { start: number; end: number };

/** Find contiguous ranges in an array of start, end pairs. */
function findRanges(unprocessedRanges: Range[]): Range[] {
  // Sort charChanges by originalStartLineNumber first
  const changes = unprocessedRanges.toSorted((a, b) => a.start - b.start);
  const ranges = [];
  let current = changes.shift()!;
  while (changes.length) {
    const change = changes.shift()!;

    if (change.start <= current.end + 1) {
      // Update currentRange to encompass currentChange
      current.end = Math.max(current.end, change.end);
    } else {
      // Push currentRange to ranges and start a new range
      ranges.push(current);
      current = change;
    }
  }
  ranges.push(current);
  return ranges;
}

/** Count the number of lines in an array of ranges. */
function countLinesInRanges(ranges: Range[]) {
  return ranges.reduce((acc, curr) => {
    acc += curr.end - curr.start + 1;
    return acc;
  }, 0);
}

function countGapsInRanges(ranges: Range[]) {
  return ranges.reduce((acc, curr, i) => {
    if (i === 0) {
      return acc;
    }
    acc += curr.start - ranges[i - 1].end - 1;
    return acc;
  }, 0);
}

/**
 * Normalizes a range to [startLine, endLine) for
 * easier manipulation elsewhere
 *
 * @param range: range to normalize
 * @returns: {lineCount, afterLineNumber} - these get passed into CodeRollSuggestionWindow
 */
export function normalizeRange(change: Monaco.editor.ILineChange) {
  let afterLineNumber;
  let lineCount: number;
  // the following three conditions are mutally exclusive
  if (change.modifiedEndLineNumber === 0) {
    // DELETION - Monaco indicates by setting modifiedEndLineNumber to 0
    lineCount = change.originalEndLineNumber - change.originalStartLineNumber + 1;
    // original start line is the line before the deleted line
    afterLineNumber = change.originalStartLineNumber - 1;
  } else if (change.charChanges?.length) {
    // MODIFICATION - Monaco indicates by setting charChanges

    // We know that Monaco does not give us good data in cases where non-contiguous changes are made.
    // This is the best we can do with the data given, the approximation will always be GTE the actual value.
    const originalRanges = findRanges(
      change.charChanges.map((c) => ({
        start: c.originalStartLineNumber,
        end: c.originalEndLineNumber,
      })),
    );
    const modifiedRanges = findRanges(
      change.charChanges.map((c) => ({
        start: c.modifiedStartLineNumber,
        end: c.modifiedEndLineNumber,
      })),
    );
    const originalLineCount = countLinesInRanges(originalRanges);
    const modifiedLineCount = countLinesInRanges(modifiedRanges);
    const unchangedOriginalLineCount = countGapsInRanges(originalRanges);
    const unchangedModifiedLineCount = countGapsInRanges(modifiedRanges);
    // these should be the same but nobody should trust Monaco
    const unchangedLineCount = Math.max(unchangedOriginalLineCount, unchangedModifiedLineCount);
    lineCount = originalLineCount + modifiedLineCount + unchangedLineCount;
    afterLineNumber = change.modifiedStartLineNumber - 1;
  } else if (change.originalEndLineNumber === 0) {
    // INSERTION - Monaco indicates by setting originalEndLineNumber to 0
    lineCount = change.modifiedEndLineNumber - change.modifiedStartLineNumber + 1;
    afterLineNumber = change.modifiedStartLineNumber - 1;
  } else {
    throw new Error("Unexpected line change");
  }
  return { lineCount, afterLineNumber };
}

function combineRange(...changes: Monaco.editor.ILineChange[]) {
  return changes.reduce((combined, current) => ({
    ...lineCombine(combined, current),
    charChanges: [...(combined.charChanges ?? []), ...(current.charChanges ?? [])],
  }));
}

function lineCombine(
  ...changes: {
    modifiedStartLineNumber: number;
    modifiedEndLineNumber: number;
    originalEndLineNumber: number;
    originalStartLineNumber: number;
  }[]
) {
  return changes.reduce((combined, current) => ({
    originalStartLineNumber: Math.min(
      combined.originalStartLineNumber,
      current.originalStartLineNumber,
    ),
    originalEndLineNumber: Math.max(combined.originalEndLineNumber, current.originalEndLineNumber),
    modifiedStartLineNumber: Math.min(
      combined.modifiedStartLineNumber,
      current.modifiedStartLineNumber,
    ),
    modifiedEndLineNumber: Math.max(combined.modifiedEndLineNumber, current.modifiedEndLineNumber),
  }));
}
/**
 * The sugestion is in the range of the change.
 */
function isInRange(suggestion: IEditSuggestion, change: Monaco.editor.ILineChange) {
  //If we are lucky its a pretty direct mapping.  They use different offsets so
  // we need to add one.   We may need to add 1 further down, but do not.
  if (change.originalStartLineNumber === suggestion.lineRange.start + 1) {
    return true;
  }
  const changeStart = Math.min(change.originalStartLineNumber, change.modifiedStartLineNumber);
  const changeEnd = Math.max(change.originalEndLineNumber, change.modifiedEndLineNumber);

  return (
    // Change starts within suggestion
    (changeStart >= suggestion.lineRange.start && changeStart <= suggestion.lineRange.stop) ||
    // Change ends within suggestion
    (changeEnd >= suggestion.lineRange.start && changeEnd <= suggestion.lineRange.stop) ||
    // Change encompasses suggestion
    (changeStart <= suggestion.lineRange.start && changeEnd >= suggestion.lineRange.stop)
  );
}

export function combineLineChangesBySuggestion(
  suggestions: IEditSuggestion[],
  changes: Monaco.editor.ILineChange[],
) {
  const ret: Map<IEditSuggestion, Monaco.editor.ILineChange> = new Map();
  const sorted = suggestions.toSorted(
    ({ lineRange: { start: a } }, { lineRange: { start: b } }) => a - b,
  );

  OUTER: for (const change of changes.toSorted(
    (
      { modifiedStartLineNumber: a, originalStartLineNumber: a1 },
      { modifiedStartLineNumber: b, originalStartLineNumber: b1 },
    ) => a - b || a1 - b1,
  )) {
    for (const suggestion of sorted) {
      if (isInRange(suggestion, change)) {
        const oRange = ret.get(suggestion);
        ret.set(suggestion, oRange ? combineRange(oRange, change) : change);
        continue OUTER;
      }
    }
  }
  return ret;
}
