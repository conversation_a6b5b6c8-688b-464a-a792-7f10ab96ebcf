import { type IEditSuggestion } from "$vscode/src/next-edit/next-edit-types";
import { type IQualifiedPathName } from "@augment-internal/sidecar-libs/src/workspace/workspace-types";

import type * as Monaco from "monaco-editor/esm/vs/editor/editor.api";

import { areSameSuggestion } from "../suggestion-tree/navigation-utils";
import AcceptSvg from "./icons/accept.svg?url";
import JumpToSvg from "./icons/jump-to.svg?url";
import RejectSvg from "./icons/reject.svg?url";
import UndoSvg from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/arrow-turn-left.svg?url";

import type { Action, ActionConfig } from "./types";

export function noop() {}

export type CodeActionsArray<K extends (keyof CODE_ACTIONS)[]> = {
  [P in keyof K]: CODE_ACTIONS[K[P]];
};

export const buildCodeActions = <const K extends (keyof CODE_ACTIONS)[]>(
  ...codeActionKeys: K
): CodeActionsArray<K> => {
  return codeActionKeys.map((key) => CODE_ACTIONS[key]) as CodeActionsArray<K>;
};

// eslint-disable-next-line @typescript-eslint/naming-convention
type CODE_ACTIONS = {
  [K in keyof typeof CODE_ACTIONS]: K extends Action | "|"
    ? K extends Action
      ? ActionConfig<K>
      : "|"
    : never;
};

const CODE_ACTIONS = {
  active: {
    action: "active",
    label: "Focus in Editor",
    icon: JumpToSvg,
  },
  accept: {
    action: "accept",
    label: "Accept",
    icon: AcceptSvg,
  },
  reject: {
    action: "reject",
    label: "Reject",
    icon: RejectSvg,
  },
  acceptAllInFile: {
    action: "acceptAllInFile",
    label: "Accept All",
    icon: AcceptSvg,
  },
  rejectAllInFile: {
    action: "rejectAllInFile",
    label: "Reject All",
    icon: RejectSvg,
  },
  undo: {
    action: "undo",
    label: "Undo",
    icon: UndoSvg,
  },
  undoAllInFile: {
    action: "undoAllInFile",
    label: "Undo All",
    icon: UndoSvg,
  },
  // eslint-disable-next-line @typescript-eslint/naming-convention
  "|": "|",
} as const;

CODE_ACTIONS satisfies CODE_ACTIONS;

export const FILE_ACTIONS: ActionConfig[] = [] as const;

export function stop<T extends readonly any[]>(fn: (...args: T) => void, ...args: T) {
  return function stopIt(e: Event) {
    e.preventDefault();
    e.stopPropagation();
    e.stopImmediatePropagation();
    fn(...args);
  };
}

export function hasSuggestion(
  suggestions: IEditSuggestion[],
  ...selectedSuggestion: (IEditSuggestion | undefined)[]
) {
  return suggestions.some((s) =>
    selectedSuggestion.some((v) => (v == null ? false : areSameSuggestion(v, s))),
  );
}
export function resolveLineRange(
  lineChange: Monaco.editor.ILineChange,
): IEditSuggestion["lineRange"] {
  return {
    start: Math.min(lineChange.originalStartLineNumber, lineChange.modifiedStartLineNumber),
    stop: Math.max(lineChange.originalEndLineNumber, lineChange.modifiedEndLineNumber),
  };
}

export function countLines(str: string, trailing = false) {
  return str.split("\n").length + (trailing && str.endsWith("\n") ? -1 : 0);
}

export function trimTrailingNewline(str: string): string {
  if (str.endsWith("\n")) {
    return str.slice(0, -1);
  }
  return str;
}

// TODO this seems incorrect as large-changes line 24 should be 11 but returns 13 here
export function calculateSuggestionLines({ lineRange, result }: IEditSuggestion) {
  let lines = lineRange.stop - lineRange.start;
  if (result.suggestedCode) {
    lines += countLines(result.suggestedCode, true);
  }
  return lines;
}

export function isSameFile(a: IQualifiedPathName, b: IQualifiedPathName) {
  return a === b || (a.relPath === b.relPath && a.rootPath === b.rootPath);
}

export function calculateAfterLineNumber(suggestion: IEditSuggestion) {
  //If a suggestion starts with a newline, we need to add an extra line to the start.  It is
  const add = suggestion.result.suggestedCode.startsWith("\n") ? 1 : 0;
  const start = suggestion.lineRange.start;
  return start + add; //Math.max(context, start) + add;
}
