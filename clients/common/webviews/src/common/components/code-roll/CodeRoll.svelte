<script lang="ts">
  import { sortAndFilterSuggestions } from "../suggestion-tree/navigation-utils";
  import { type IEditSuggestion } from "$vscode/src/next-edit/next-edit-types";
  import { noop } from "./code-roll-util";
  import CodeRollItem from "./CodeRollItem.svelte";
  import type { OnCodeAction, FileReader, ActionConfig } from "./types";
  import SpinnerAugment from "$common-webviews/src/design-system/components/SpinnerAugment.svelte";

  export let suggestions: IEditSuggestion[] = [];
  export let onCodeAction: OnCodeAction = noop;
  export let codeActions: ActionConfig[] = [];
  export let loading = true;
  export let readFile: FileReader;

  $: sortedPathSuggestionsMap = new Map(sortAndFilterSuggestions(suggestions));
</script>

<ul class="c-code-roll">
  {#if loading}
    <li class="c-code-roll__item"><SpinnerAugment /></li>
  {/if}
  {#each sortedPathSuggestionsMap as [_, suggs]}
    <li class="c-code-roll__item">
      <CodeRollItem
        {codeActions}
        filepath={suggs[0]?.qualifiedPathName}
        {readFile}
        {onCodeAction}
        suggestions={suggs}
      />
    </li>
  {/each}
</ul>

<style>
  .c-code-roll {
    --augment-code-roll-item-border: var(--ds-color-neutral-a6);
    --augment-code-roll-item-background: transparent;
    --augment-code-roll-item-radius: 5px;

    list-style: none;
    padding: 0;
    margin: 0;
    gap: 10px;
    padding: 10px;
    border-radius: var(--augment-border-radius);
    display: flex;
    flex-direction: column;
  }
  .c-code-roll__item {
    padding: 0;
    margin: 0;
    display: flex;
  }
</style>
