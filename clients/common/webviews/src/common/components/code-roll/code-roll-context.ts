import { type IEditSuggestion } from "$vscode/src/next-edit/next-edit-types";

import { getContext, setContext } from "svelte";
import { type Writable, writable } from "svelte/store";

export interface CodeRollSelection {
  nextSuggestion?: IEditSuggestion | undefined;
  selectedSuggestion?: IEditSuggestion | undefined;
  activeSuggestion?: IEditSuggestion | undefined;
  isOpen?: Record<string, boolean>;
}

const key = Symbol("code-roll-selection-context");

export function getCodeRollSelection(): Writable<CodeRollSelection> {
  let ctx = getContext<Writable<CodeRollSelection>>(key);
  if (!ctx) {
    ctx = setCodeRollSelection({});
  }

  return ctx;
}

export function setCodeRollSelection(context: CodeRollSelection) {
  return setContext(key, writable<CodeRollSelection>(context));
}

export function getCurrentSuggestion(context: CodeRollSelection): IEditSuggestion | undefined {
  return context.activeSuggestion ?? context.selectedSuggestion ?? context.nextSuggestion;
}

export function getLevel(context: CodeRollSelection): "active" | "select" | "next" {
  return context.activeSuggestion ? "active" : context.selectedSuggestion ? "select" : "next";
}
