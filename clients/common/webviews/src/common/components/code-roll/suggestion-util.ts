import { type IEditSuggestion } from "$vscode/src/next-edit/next-edit-types";

import { areSameSuggestion } from "../suggestion-tree/navigation-utils";
import { type CodeRollSelection } from "./code-roll-context";
import "./suggestion.css";

export type SelectionState = "next" | "select" | "active" | "none";

export function selectionState(
  suggestion: IEditSuggestion,
  context: CodeRollSelection,
  isTreeList: boolean,
): SelectionState {
  if (context.activeSuggestion) {
    if (areSameSuggestion(context.activeSuggestion, suggestion)) {
      return isTreeList ? "select" : "active";
    }
    return "none";
  } else if (context.selectedSuggestion) {
    if (areSameSuggestion(context.selectedSuggestion, suggestion)) {
      return isTreeList ? "active" : "select";
    }
    return "none";
  } else if (context.nextSuggestion) {
    return areSameSuggestion(context.nextSuggestion, suggestion) ? "next" : "none";
  }
  return "none";
}

export function styleForSelection(state: SelectionState) {
  return [
    "", //; before
    `--augment-code-roll-selection-background: var(--augment-code-roll-item-background-${state})`,
    `--augment-code-roll-selection-color: var(--augment-code-roll-item-color-${state})`,
    `--augment-code-roll-selection-border: var(--augment-code-roll-selection-background)`,
    "", //; after
  ].join(";");
}
