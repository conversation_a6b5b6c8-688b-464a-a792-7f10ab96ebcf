<script lang="ts">
  import { noop } from "./code-roll-util";
  import type { ActionConfig, OnFileAction } from "./types";
  import IconFilePath from "../icon-file-path/IconFilePath.svelte";
  import ActionButtons from "./ActionButtons.svelte";
  import { type IQualifiedPathName } from "@augment-internal/sidecar-libs/src/workspace/workspace-types";
  import type { IEditSuggestion } from "$vscode/src/next-edit/next-edit-types";

  export let filepath: IQualifiedPathName;
  export let onFileAction: OnFileAction = noop;
  export let fileActions: ActionConfig[] = [];
  export let suggestions: IEditSuggestion[];
</script>

<div class="c-code-roll-item-header">
  <IconFilePath filepath={filepath.relPath} onCodeAction={noop} value={suggestions} />
  <ActionButtons actions={fileActions} onAction={onFileAction} value={filepath} />
</div>

<style>
  .c-code-roll-item-header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
    border-radius: var(--augment-border-radius, 5px);
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
    width: 100%;
    overflow: hidden;
    color: rgba(160, 160, 160, 1);
  }
</style>
