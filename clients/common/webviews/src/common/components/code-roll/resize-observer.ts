import { type ActionReturn } from "svelte/action";
interface ResizeObserverAction {
  onResize: ResizeObserverCallback;
  options?: ResizeObserverOptions;
}
export const resize = (
  element: HTMLElement,
  { onResize, options }: ResizeObserverAction,
): ActionReturn<ResizeObserverAction> => {
  const resizeObserver = new ResizeObserver(onResize);
  resizeObserver.observe(element, options);
  return {
    destroy() {
      resizeObserver.unobserve(element);
      resizeObserver.disconnect();
    },
  };
};
