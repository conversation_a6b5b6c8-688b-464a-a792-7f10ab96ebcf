import { type IEditSuggestion } from "$vscode/src/next-edit/next-edit-types";
import { type IQualifiedPathName } from "@augment-internal/sidecar-libs/src/workspace/workspace-types";

export interface ActionConfigObj<T extends string = Action> {
  action: T;
  label: string;
  icon: string;
  disabled?: boolean;
}

export type ActionConfig<T extends string = Action> = ActionConfigObj<T> | "|";

export type Action =
  | "accept"
  | "reject"
  | "undo"
  | "expand"
  | "active"
  | "select"
  | "refresh"
  | "acceptAllInFile"
  | "rejectAllInFile"
  | "undoAllInFile"
  | "dismiss";

export type FileReader = (filename: IQualifiedPathName) => Promise<string>;

export type ActionValue<T extends string, V = any> = T extends "acceptAllInFile" | "rejectAllInFile"
  ? V[]
  : V;

export type OnAction<V> = <T extends string>(action: T, value?: ActionValue<T, V>) => void;

export type OnCodeAction = OnAction<IEditSuggestion>;

export type OnFileAction = OnAction<IQualifiedPathName>;
