<script lang="ts">
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import { onMount } from "svelte";
  import { fly } from "svelte/transition";
  import { resize } from "../code-roll/resize-observer";

  /**
   * This is a 2-way version of the Drawer component. It allows
   * resizing in both directions and automatically switches
   * to a column layout when the view is narrow.
   *
   * The sizing is specified in percentages. The percentage
   * is the size of the first (left/top) panel. The second panel is
   * the inverse of that.
   */

  type Side = "left" | "right" | "both";
  type MinimizedSide = "left" | "right" | null;

  /** Initial percentage for the first panel (0-100) */
  export let initialPercentage = 50;
  /** Which side should be minimizable: "left", "right", or "both" */
  export let side: Side = "left";
  /** Whether the drawer is currently minimized */
  export let minimized = false;
  /** CSS class to apply to the container */
  let clazz = "";
  export { clazz as class };
  /** Whether to show the button to restore from minimized state */
  export let showButton = true;
  /** Width threshold at which to switch to column layout */
  export let columnLayoutThreshold = 600;
  /** Force a specific layout mode. If undefined, layout mode is automatic based on width */
  export let layoutMode: "row" | "column" | undefined = undefined;
  /** Minimum percentage for the resizable panel when expanded */
  export let minPercentage = 15;
  /** Maximum percentage for the resizable panel when expanded */
  export let maxPercentage = 85;
  /** Deadzone in px to ignore when minimizing */
  export let deadzone = 40;
  /** Which side is currently minimized when side === "both" */
  export let minimizedSide: MinimizedSide = null;

  let containerElement: HTMLElement;
  let isDragging = false;
  let startX: number;
  let startY: number;
  /** Percentage taken by the first panel at the start of the drag */
  let startPercentage: number;
  /** Percentage taken by the first panel */
  let currentPercentage = initialPercentage;
  let useColumnLayout = false;

  onMount(() => {
    updateLayout();
    // Ensure initial percentage is within bounds
    currentPercentage = Math.max(minPercentage, Math.min(maxPercentage, initialPercentage));
  });

  function startDrag(event: MouseEvent) {
    if (!containerElement) return;

    isDragging = true;

    if (useColumnLayout) {
      // Vertical dragging in column layout
      startY = event.clientY;
    } else {
      // Horizontal dragging in row layout
      startX = event.clientX;
    }

    startPercentage = currentPercentage;

    // Prevent text selection during drag
    event.preventDefault();
  }

  function doDrag(event: MouseEvent) {
    if (!isDragging || !containerElement) {
      return;
    }

    let newPercentage: number;
    let deltaPercent: number;
    const containerSize = useColumnLayout
      ? containerElement.clientHeight
      : containerElement.clientWidth;

    if (useColumnLayout) {
      const deltaY = event.clientY - startY;
      deltaPercent = (deltaY / containerSize) * 100;
    } else {
      const deltaX = event.clientX - startX;
      deltaPercent = (deltaX / containerSize) * 100;
    }
    newPercentage = startPercentage + deltaPercent;

    const deadzonePercentage = (deadzone / containerSize) * 100;

    // Check if we should minimize based on deadzone
    if (side === "left") {
      if (newPercentage <= minPercentage - deadzonePercentage) {
        minimized = true;
      } else if (newPercentage < minPercentage) {
        // Within deadzone, snap to minPercentage
        minimized = false;
        currentPercentage = minPercentage;
      } else {
        // Normal drag within bounds
        minimized = false;
        currentPercentage = Math.min(maxPercentage, newPercentage);
      }
    } else if (side === "right") {
      if (newPercentage >= maxPercentage + deadzonePercentage) {
        minimized = true;
      } else if (newPercentage > maxPercentage) {
        // Within deadzone, snap to maxPercentage
        minimized = false;
        currentPercentage = maxPercentage;
      } else {
        // Normal drag within bounds
        minimized = false;
        currentPercentage = Math.max(minPercentage, newPercentage);
      }
    } else {
      // side === "both"
      if (newPercentage <= minPercentage - deadzonePercentage) {
        // Minimize left side
        minimized = true;
        minimizedSide = "left";
      } else if (newPercentage >= maxPercentage + deadzonePercentage) {
        // Minimize right side
        minimized = true;
        minimizedSide = "right";
      } else if (newPercentage < minPercentage) {
        // Within left deadzone, snap to minPercentage
        minimized = false;
        currentPercentage = minPercentage;
      } else if (newPercentage > maxPercentage) {
        // Within right deadzone, snap to maxPercentage
        minimized = false;
        currentPercentage = maxPercentage;
      } else {
        // Normal drag within bounds
        minimized = false;
        currentPercentage = newPercentage;
      }
    }
  }

  function stopDrag() {
    isDragging = false;
  }

  function toggleDrawer() {
    minimized = !minimized;
    if (!minimized) {
      // Restore to last known percentage or default to minPercentage + 5
      currentPercentage = Math.max(minPercentage + 5, currentPercentage);
    }
    if (side === "both" && minimized) {
      // Minimize the smaller side
      if (currentPercentage < 50) {
        minimizedSide = "left";
      } else {
        minimizedSide = "right";
      }
    }
  }

  function updateLayout() {
    if (!containerElement) {
      return;
    }

    // If layoutMode is explicitly set, use that
    if (layoutMode !== undefined) {
      useColumnLayout = layoutMode === "column";
      return;
    }

    // Otherwise use automatic layout based on width
    useColumnLayout = containerElement.clientWidth < columnLayoutThreshold;
  }

  // Watch for layoutMode changes
  $: if (layoutMode !== undefined) {
    useColumnLayout = layoutMode === "column";
  }

  $: isFirstPanelMinimized =
    minimized && (side === "left" || (side === "both" && minimizedSide === "left"));
  $: isSecondPanelMinimized =
    minimized && (side === "right" || (side === "both" && minimizedSide === "right"));
  $: isFirstPanelInert = isDragging || isFirstPanelMinimized;
  $: isSecondPanelInert = isDragging || isSecondPanelMinimized;

  // Percentage to pass to CSS
  $: effectivePercentage = (() => {
    if (minimized) {
      if (side === "both") {
        return minimizedSide === "left" ? 0 : 100;
      } else {
        return side === "left" ? 0 : 100;
      }
    } else {
      return currentPercentage;
    }
  })();
</script>

<svelte:window on:mousemove={doDrag} on:mouseup={stopDrag} />

<div
  class="c-drawer {clazz}"
  class:is-dragging={isDragging}
  class:is-minimized={minimized}
  class:is-column={useColumnLayout}
  class:minimize-left={side === "left"}
  class:minimize-right={side === "right"}
  class:minimize-both={side === "both"}
  bind:this={containerElement}
  use:resize={{
    onResize: () => layoutMode === undefined && updateLayout(),
  }}
  style="--drawer-percentage: {effectivePercentage}%; --inverse-drawer-percentage: {100 -
    effectivePercentage}%;"
>
  <div class="c-drawer__first c_drawer__panel" inert={isFirstPanelInert}>
    <slot name="left" />
  </div>

  <!--
  Drag handle
  Disabling aria here as we do not want focus going here. This does not
  really have a non-mouse affordance.
  -->
  <div
    aria-hidden="true"
    class="c-drawer__handle"
    on:mousedown={startDrag}
    on:dblclick={toggleDrawer}
  ></div>

  <div class="c-drawer__second c_drawer__panel" inert={isSecondPanelInert}>
    <slot name="right" />
  </div>

  <!-- Hidden state indicator -->
  {#if minimized && showButton}
    <div
      transition:fly={{ y: 0, x: 0, duration: 200 }}
      class="c-drawer__hidden-indicator"
      class:c-drawer__hidden-indicator--left={side === "left" ||
        (side === "both" && minimizedSide === "left")}
      class:c-drawer__hidden-indicator--right={side === "right" ||
        (side === "both" && minimizedSide === "right")}
    >
      <IconButtonAugment
        variant="solid"
        color="accent"
        size={2}
        radius={"full"}
        title="Show panel"
        on:click={toggleDrawer}
      >
        <span class="c-drawer__hidden-indicator-ellipsis">...</span>
      </IconButtonAugment>
    </div>
  {/if}
</div>

<style>
  .c-drawer {
    display: flex;
    flex: 1;
    width: 100%;
    height: 100%;
    position: relative;
    --augment-drawer-active-color: var(--ds-color-accent-9);
  }

  /* Row layout (default) */
  .c-drawer {
    flex-direction: row;
  }

  /* Column layout */
  .c-drawer.is-column {
    flex-direction: column;
  }

  /* First (top/left) */
  .c-drawer__first {
    flex-basis: var(--drawer-percentage);
  }

  /* Second (bottom/right) */
  .c-drawer__second {
    flex-basis: var(--inverse-drawer-percentage);
  }

  /** Common */
  .c_drawer__panel {
    min-height: 0;
    min-width: 0;
    overflow: auto;
    scrollbar-color: var(--augment-scrollbar-color) rgba(0, 0, 0, 0.3);
  }

  /* Handle minimized state */
  .c-drawer.is-minimized.minimize-left .c-drawer__first {
    flex-basis: 0;
    overflow: hidden;
  }

  .c-drawer.is-minimized.minimize-right .c-drawer__second {
    flex-basis: 0;
    overflow: hidden;
  }

  .c-drawer.is-minimized.minimize-left .c-drawer__second,
  .c-drawer.is-minimized.minimize-right .c-drawer__first {
    flex-basis: 100%;
  }

  /* Drag handle common */
  .c-drawer__handle {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    z-index: 10;
    transition: background-color 50ms ease 0ms;
  }

  /* Handle when in row layout */
  .c-drawer:not(.is-column) .c-drawer__handle {
    width: 4px;
    cursor: col-resize;
    margin: 0 3px;
    border-left: 1px solid rgba(255, 255, 255, 0.1);
  }

  /* Handle when in column layout */
  .c-drawer.is-column .c-drawer__handle {
    width: 100%;
    height: 4px;
    cursor: row-resize;
    margin: 3px 0;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
  }

  /* Hide handle when minimized */
  .c-drawer.is-minimized .c-drawer__handle {
    display: none;
  }

  /* Handle hover and active states */
  .c-drawer__handle:hover,
  .is-dragging .c-drawer__handle {
    background-color: var(--augment-drawer-active-color);
  }

  .c-drawer__handle:hover {
    transition-delay: 400ms;
  }

  .c-drawer__handle:not(:hover) {
    transition-delay: 0ms;
  }

  /* Dragging cursor styles */
  .c-drawer.is-dragging {
    cursor: col-resize;
    user-select: none;
  }

  .c-drawer.is-column.is-dragging {
    cursor: row-resize;
  }

  /* Hidden state */
  .c-drawer__hidden-indicator {
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0.99;
    transition: opacity 0.2s ease;
    border-radius: 100%;
    box-shadow: var(--ds-shadow-no-border-3);
  }

  .c-drawer:not(.is-column) .c-drawer__hidden-indicator--left {
    left: 0px;
    bottom: 0px;
  }

  /** In row layout, indicator is at top right */
  .c-drawer:not(.is-column) .c-drawer__hidden-indicator--right {
    right: 0px;
    top: 0px;
  }

  .c-drawer.is-column .c-drawer__hidden-indicator--left {
    top: 0px;
    right: 0px;
  }

  /** In column layout, indicator is at bottom left */
  .c-drawer.is-column .c-drawer__hidden-indicator--right {
    bottom: 0px;
    left: 0px;
  }

  .c-drawer__hidden-indicator:hover {
    opacity: 0.5;
  }

  .c-drawer__hidden-indicator-ellipsis {
    font-size: 1rem;
    font-weight: bold;
    color: white;
  }
</style>
