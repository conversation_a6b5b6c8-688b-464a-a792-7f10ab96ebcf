<script lang="ts">
  /**
   * This file is a wrapper that allows any component to be rendered in a "floating" fashion.
   * The component will always be positioned absolutely
   */
  let clazz: string = "";
  export { clazz as class };
  export let xPos: "left" | "middle" | "right" = "left";
  export let yPos: "top" | "middle" | "bottom" | "below" = "middle";
</script>

<div
  class={`c-floating__container ${clazz}`}
  class:c-floating__x-left={xPos === "left"}
  class:c-floating__x-middle={xPos === "middle"}
  class:c-floating__x-right={xPos === "right"}
  class:c-floating__y-top={yPos === "top"}
  class:c-floating__y-middle={yPos === "middle"}
  class:c-floating__y-bottom={yPos === "bottom"}
  class:c-floating__y-below={yPos === "below"}
>
  <slot />
</div>

<style>
  .c-floating__container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    display: flex;
    flex-direction: column;
    z-index: var(--z-floating);
  }

  .c-floating__x-left {
    align-items: start;
  }

  .c-floating__x-middle {
    align-items: center;
  }

  .c-floating__x-right {
    align-items: end;
  }

  .c-floating__y-top {
    top: 0;
    bottom: auto;
  }

  .c-floating__y-middle {
    top: 50%;
    transform: translateY(-50%);
  }

  .c-floating__y-bottom {
    top: auto;
    bottom: 0;
  }
  .c-floating__y-below {
    top: 100%;
    bottom: auto;
  }
</style>
