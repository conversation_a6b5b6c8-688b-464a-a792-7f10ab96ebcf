export type ScrollToOptions = { scrollTo: boolean; delay: number; options: ScrollIntoViewOptions };

export function scrollTo(element: HTMLElement, opt: ScrollToOptions) {
  let timeout: number | undefined | ReturnType<typeof setTimeout>;
  function update({ scrollTo, delay, options }: ScrollToOptions) {
    clearTimeout(timeout);
    if (scrollTo) {
      timeout = setTimeout(() => {
        element.scrollIntoView(options);
      }, delay);
    }
  }
  update(opt);
  return {
    update,
    destroy() {
      clearTimeout(timeout);
    },
  };
}
