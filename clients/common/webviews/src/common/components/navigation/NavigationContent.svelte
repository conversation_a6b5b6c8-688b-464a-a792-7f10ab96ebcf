<script lang="ts">
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import type { NavigationItem } from "./navigation-types";
  export let item: NavigationItem | undefined;
</script>

<div class="c-navigation__content" id={item?.id}>
  <slot name="header" />
  <div>
    {#if item != null}
      <TextAugment size={4} weight="medium" color="neutral">
        <div class="c-navigation__content-header">
          {item?.name}
        </div>
      </TextAugment>
      {#if item?.description}
        <TextAugment color="secondary" size={1} weight="light">
          <div class="c-navigation__content-description">
            {item?.description}
          </div>
        </TextAugment>
      {/if}
      <div class="c-navigation__content-container">
        <slot name="content" {item} />
      </div>
    {/if}
  </div>
</div>

<style>
  .c-navigation__content {
    padding: var(--ds-spacing-6);
    flex: 2;
    width: 100%;
    max-width: 600px;
  }
  .c-navigation__content-header {
    display: flex;
    flex-direction: row;
    gap: var(--ds-spacing-3);
    align-items: center;
  }
  .c-navigation__content-container {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-3);
    flex: 1;
    overflow: auto;
    padding: var(--ds-spacing-3) 0;
  }
  .c-navigation__content-description {
    padding: var(--ds-spacing-2) 0;
  }
</style>
