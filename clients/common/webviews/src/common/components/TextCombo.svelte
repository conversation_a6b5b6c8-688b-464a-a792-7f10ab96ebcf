<script lang="ts" context="module">
  export type TruncateDirection = "left" | "right";
</script>

<script lang="ts">
  import TextAugment, { type TextSize } from "../../design-system/components/TextAugment.svelte";
  let clazz = "";
  export { clazz as class };
  export let size: TextSize = 1;
  export let align: "left" | "right" = "left";
  export let greyTextTruncateDirection: TruncateDirection = "right";
  export let shrink: boolean = false;
</script>

<TextAugment {size} weight="medium">
  <div
    class={`c-text-combo ${clazz}`}
    class:c-text-combo--align-right={align === "right"}
    class:c-text-combo--shrink={shrink}
    role="button"
    tabindex="-1"
    on:click
    on:keydown
    on:keyup
    on:blur
    on:focus
    on:mouseenter
    on:mouseleave
    on:contextmenu
  >
    {#if $$slots.leftIcon}
      <slot name="leftIcon" />
    {/if}

    {#if $$slots.text}
      <span class="c-text-combo__text">
        <slot name="text" />
      </span>
    {/if}

    {#if $$slots.grayText}
      <div
        class="c-text-combo__gray"
        class:c-text-combo--gray-truncate-left={greyTextTruncateDirection === "left"}
        class:c-text-combo--gray-truncate-right={greyTextTruncateDirection === "right"}
      >
        <div class="c-text-combo__gray-text">
          <slot name="grayText" />
        </div>
      </div>
    {/if}

    {#if $$slots.rightIcon}
      <slot name="rightIcon" />
    {/if}
  </div>
</TextAugment>

<style>
  .c-text-combo {
    display: flex;
    gap: var(--ds-spacing-1);
    width: 100%;
    flex-direction: row;
    align-items: center;
    min-width: 0;
  }
  .c-text-combo--shrink {
    width: auto;
    flex: 0 1 auto;
    min-width: 0;
    overflow: hidden;
  }
  .c-text-combo--align-right {
    justify-content: flex-end;
  }
  .c-text-combo > .c-text-combo__text,
  .c-text-combo > .c-text-combo__gray {
    flex: 0 1 auto;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    min-width: 0;
  }
  .c-text-combo > .c-text-combo__gray {
    flex: 1 1 0px;
    overflow: hidden;
    display: inline-block;
    text-overflow: ellipsis;
    text-align: left;
    opacity: 50%;
    min-width: 0;

    &.c-text-combo--gray-truncate-left {
      direction: rtl;
    }

    &.c-text-combo--gray-truncate-right {
      direction: ltr;
    }
  }
  .c-text-combo > .c-text-combo__gray > .c-text-combo__gray-text {
    display: inline;
    width: fit-content;
    direction: ltr;
    min-width: 0;
  }
  .c-text-combo > :global(:first-child),
  .c-text-combo > :global(.material-symbols-outlined:first-child) {
    margin-left: 0;
    flex-shrink: 0;
  }
  .c-text-combo > :global(:last-child),
  .c-text-combo > :global(.material-symbols-outlined:last-child) {
    margin-right: 0;
    flex-shrink: 0;
  }
</style>
