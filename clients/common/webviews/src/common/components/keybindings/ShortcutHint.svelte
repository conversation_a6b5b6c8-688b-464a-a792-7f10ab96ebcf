<script lang="ts">
  import { config } from "$common-webviews/src/apps/chat/chat-keybindings";
  import { mapKeyCombo } from "../../keybindings/multiplatform-keys-config-map";
  import { isIcon, mapKeyBindingToIcons } from "./icons";

  let { commandName, keybinding, keysOnly = false }: { commandName?: string; keybinding?: string; keysOnly?: boolean } = $props();

  const keyBindingString = $derived.by(() => {
    const macKeybindingString = keybinding || Object.keys(config).find((keybinding) => {
      const keybindingConfig = config[keybinding];
      return keybindingConfig.commandName === commandName;
    });

    return macKeybindingString ? mapKeyCombo(macKeybindingString) : undefined;
  });

  const icons = $derived.by(() => {
    return keyBindingString ? mapKeyBindingToIcons(keyBindingString) : [];
  });

  const shortcutName = $derived.by(() => {
    return keyBindingString ? config[keyBindingString]?.name : "";
  });

</script>

<span>
  {keysOnly ? "" : shortcutName}
  <span class="c-shortcut-hint">
    {#each icons as icon}
      {#if isIcon(icon)}
        <span class="c-shortcut-hint__icon">
          {icon.icon}
        </span>
      {:else}
        <span class="c-shortcut-hint__icon-delimeter">
          {" or "}
        </span>
      {/if}
    {/each}
  </span>
</span>

<style>
  .c-shortcut-hint {
    display: inline-flex;
    gap: 2px;
  }

  .c-shortcut-hint__icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0 var(--ds-spacing-1);
    border: 1px solid var(--keyboard-shortcut-hint-border, currentColor);
    border-radius: var(--ds-spacing-1);
    color: var(--keyboard-shortcut-hint-color, currentColor);
    font-size: 0.7rem;
    line-height: 1.5;
    background: var(--keyboard-shortcut-hint-bg, unset);
  }

  .c-shortcut-hint__icon-delimeter {
    margin: auto var(--ds-spacing-1);
  }
</style>
