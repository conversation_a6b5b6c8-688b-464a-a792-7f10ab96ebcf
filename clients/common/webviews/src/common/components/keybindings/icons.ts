const keyIconsMap: Record<string, string> = {
  ctrl: "Ctrl",
  control: "Ctrl",
  option: "⌥",
  cmd: "⌘",
  command: "⌘",
  shift: "⇧",
  plus: "+",
  minus: "-",
  at: "@",
  up: "↑",
  down: "↓",
  enter: "⏎",
  esc: "⎋",
  backspace: "⌫",
  period: ".",
}

export interface IKeyIcon {
  icon: string;
}

export const isIcon = (obj: any): obj is IKeyIcon => {
  return obj && obj.icon;
}

const mapToIcons = (singleKeyBindingString: string): IKeyIcon[] => {
  return singleKeyBindingString.split("+").map((key) => {
    return {
      icon: keyIconsMap[key] || key.toUpperCase(),
    }
  });
}

export const mapKeyBindingToIcons = (keyBindingString: string) => {
  const keybindings = keyBindingString.split(/,\s*/);
  return keybindings.reduce((acc, keybinding, index) => {
    if (index > 0) {
      acc.push("|");
    }
    return acc.concat(mapToIcons(keybinding));
  }, [] as Array<IKeyIcon | string>);
}