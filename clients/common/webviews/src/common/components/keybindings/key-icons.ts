/**
 * `navigator.platform` is deprecated, but is still the least-bad way to detect modifier
 * keys on Windows/Linux vs MacOS.
 *
 * https://developer.mozilla.org/en-US/docs/Web/API/Navigator/platform#examples
 */
export const isMac: boolean = navigator.platform.toUpperCase().indexOf("MAC") >= 0;

export enum KeyIcon {
  cmd = "⌘",
  shift = "⇧",
  plus = "+",
  minus = "-",
  at = "@",
  arrowUp = "↑",
  arrowDown = "↓",
  enter = "⏎",
  esc = "⎋",
  backspace = "⌫",
}

export enum SmallKeyIcon {
  leftBracket = "[",
  rightBracket = "]",
  forwardSlash = "/",
  backSlash = "\\",
  ctrl = "Ctrl",
  /* eslint-disable @typescript-eslint/naming-convention */
  L = "L",
  R = "R",
  period = ".",
  /* eslint-enable @typescript-eslint/naming-convention */
}

export interface IPlatformKeyIcon {
  cmdOrCtrl: KeyIcon | SmallKeyIcon;
}

// eslint-disable-next-line @typescript-eslint/naming-convention
export const PlatformKeyIcon: Readonly<IPlatformKeyIcon> = {
  cmdOrCtrl: isMac ? KeyIcon.cmd : SmallKeyIcon.ctrl,
};

const smallKeyIconValues: Set<string> = new Set<string>(Object.values(SmallKeyIcon));

export function isSmallKeyIcon(icon: string): icon is keyof typeof SmallKeyIcon {
  return smallKeyIconValues.has(icon);
}
