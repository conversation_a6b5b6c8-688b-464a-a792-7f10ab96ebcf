<script lang="ts">
  import { setContext } from "svelte";

  export let location: "topBar" | "bottomBar" = "bottomBar";
  /** If "left", the message text will appear on the left of the slot */
  export let align: "left" | "right" = "right";

  let messageText: string = "";
  let timeoutID: ReturnType<typeof setTimeout> | undefined;
  const MSG_DURATION_MS = 2500;

  // This function sets the message text and starts a timeout to clear the message after a certain duration
  let setMessageText = (text: string) => {
    messageText = text;
    if (timeoutID) {
      clearTimeout(timeoutID);
    }

    // A new timeout is set to clear the message after the specified duration
    // The ID of this new timeout is stored in the timeoutID variable
    timeoutID = setTimeout(() => {
      messageText = "";
    }, MSG_DURATION_MS);
  };
  setContext("augment-codeblock-actions-message", { setMessageText });
</script>

<div
  class="c-codeblocks__action-bar"
  class:c-codeblocks__loc-top-bar={location === "topBar"}
  class:c-codeblocks__loc-bottom-bar={location === "bottomBar"}
  class:c-codeblocks__loc-left-align={align === "left"}
>
  <slot />
  <div class="c-codeblocks__action-bar-status">
    {messageText || ""}
  </div>
</div>

<style>
  .c-codeblocks__action-bar {
    --code-blocks-padding: calc(var(--ds-spacing-1) * 0.5);

    display: flex;
    align-items: center;
    padding-inline: var(--code-blocks-padding);
  }

  .c-codeblocks__action-bar.c-codeblocks__loc-top-bar,
  .c-codeblocks__action-bar.c-codeblocks__loc-bottom-bar {
    padding-block: var(--code-blocks-padding);
  }

  .c-codeblocks__action-bar.c-codeblocks__loc-left-align {
    flex-direction: row-reverse;
  }

  .c-codeblocks__action-bar-status {
    margin: 0 var(--ds-spacing-1);
  }
</style>
