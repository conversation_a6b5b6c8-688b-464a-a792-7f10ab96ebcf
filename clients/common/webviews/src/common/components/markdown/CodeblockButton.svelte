<script lang="ts">
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import { type ICodeblockActionButton } from "$common-webviews/src/apps/chat/components/markdown-ext/utils";
  import MaterialIcon from "../MaterialIcon.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import { noop } from "lodash";

  export let data: ICodeblockActionButton;
  $: ({
    title,
    onClick,
    onSuccessMessage,
    iconName,
    buttonText,
    onMouseDown,
    onMouseLeave,
    onMouseOver,
    onBlur,
    onFocus,
  } = data);

  let successMessage: string | undefined = undefined;

  // Bind this to the tooltip's close function
  let requestClose: () => void = () => {};
  let successTimer: ReturnType<typeof setTimeout> | undefined = undefined;
  function onClickWrapper(e: MouseEvent) {
    e.stopPropagation();
    onClick();
    successMessage = onSuccessMessage;

    // Clear any existing timer and set a new one
    clearTimeout(successTimer);
    successTimer = setTimeout(requestClose, 1500);
  }

  // If we close the tooltip, clear the success message and timer
  function onOpenChange(open: boolean) {
    if (!open) {
      clearTimeout(successTimer);
      successMessage = undefined;
      successTimer = undefined;
    }
  }
</script>

<TextTooltipAugment bind:requestClose {onOpenChange} content={successMessage ?? title}>
  <ButtonAugment
    variant="ghost-block"
    color="neutral"
    size={1}
    on:click={onClickWrapper}
    on:mouseover={onMouseOver || noop}
    on:mouseleave={onMouseLeave || noop}
    on:blur={onBlur || noop}
    on:focus={onFocus || noop}
    on:mousedown={onMouseDown || noop}
  >
    <slot>
      {#if iconName || buttonText}
        <TextAugment size={1}>
          <MaterialIcon {iconName} />
          {buttonText ?? ""}
        </TextAugment>
      {/if}
    </slot>
  </ButtonAugment>
</TextTooltipAugment>
