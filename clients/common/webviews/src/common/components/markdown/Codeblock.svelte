<script lang="ts">
  import type { Tokens } from "marked";
  import { type editor, type Selection } from "monaco-editor";
  import SimpleMonaco from "$common-webviews/src/design-system/components/MonacoProvider/SimpleMonaco.svelte";
  // Import monaco context
  import { MonacoContext } from "$common-webviews/src/design-system/components/MonacoProvider";
  import { getMonacoLayoutManager } from "$common-webviews/src/common/utils/monaco-layout-manager";

  export let scroll: boolean = false;
  export let token: Tokens.Code;
  export let language: string | undefined = undefined;
  export let padding: { top?: number; bottom?: number } = { top: 0, bottom: 0 };
  export let editorInstance: editor.IStandaloneCodeEditor | undefined = undefined;
  export let element: HTMLDivElement | undefined = undefined;
  export let height: number | undefined = undefined;

  const monacoContext = MonacoContext.getContext();
  const monaco = monacoContext.monaco;

  // Layout manager for coordinated layout operations
  const layoutManager = getMonacoLayoutManager();

  export const getSelectionOrContents = (): string => {
    if (!editorInstance) {
      return "";
    }
    return getSelections() || getContents() || "";
  };

  export const getSelections = (): string | undefined => {
    if (!editorInstance) {
      return undefined;
    }
    const selections = editorInstance.getSelections();

    if (!selections?.length) {
      return undefined;
    }

    // If selections cover no characters, return the entire code block
    const model = editorInstance.getModel();
    const totalSelectedCharacters = selections
      .map((s: Selection) => model?.getValueLengthInRange(s) || 0)
      .reduce((a: number, b: number) => a + b, 0);
    if (totalSelectedCharacters === 0) {
      return undefined;
    }

    // If selections have characters, return the selected code
    const selectionValues = selections
      .sort($monaco?.Range.compareRangesUsingStarts)
      .map((s: Selection) => model?.getValueInRange(s) || "");
    return selectionValues.join("\n");
  };

  export const getContents = (): string | undefined => {
    if (!editorInstance) {
      return undefined;
    }
    return editorInstance.getValue() || "";
  };

  // Keep editor padding in sync with props
  const updateEditorPadding = (padding: { top?: number; bottom?: number }) => {
    editorInstance?.updateOptions({ padding });
  };
  $: updateEditorPadding(padding);

  $: {
    editorInstance?.updateOptions({
      scrollbar: { vertical: height !== undefined ? "auto" : "hidden" },
    });
  }
</script>

<svelte:window on:focus={() => layoutManager.requestLayout()} />
<div class="c-codeblock" on:mouseenter role="button" tabindex="0" bind:this={element}>
  <div class="c-codeblock__top-bar-anchor monaco-component">
    <div class="c-codeblock__top-bar-left">
      <!-- The top right slot has precedence over the left. The left will
             have its overflow hidden -->
      <slot name="topBarLeft" />
    </div>
    <slot name="topBarRight" />
  </div>
  <SimpleMonaco
    options={{
      lineNumbers: "off",
      wrappingIndent: "same",
      padding,
      wordWrap: scroll ? "off" : "on",
      contextmenu: false,
      wordBasedSuggestions: "off",
      renderLineHighlight: "none",
      occurrencesHighlight: "off",
      selectionHighlight: false,
      codeLens: false,
      links: false,
      hover: { enabled: false },
      hideCursorInOverviewRuler: true,
      renderWhitespace: "none",
      renderFinalNewline: "on",
    }}
    text={token.text}
    lang={language || token.lang}
    bind:editorInstance
    {height}
  />
  <div class="c-codeblock__actions-bar-anchor">
    <slot name="actionsBar">
      <!--
            Example usage:

            <CodeblockActions>
                <CodeblockButton
                    title="Copy to clipboard"
                    icon="file_copy"
                    onClick={onCopy}
                    onSuccessMessage="Copied!"
                />
                <CodeblockButton
                    title="Insert code block"
                    icon="format_indent_increase"
                    onClick={onInsertCodeblockCode}
                    onSuccessMessage="Inserted!"
                />
                <CodeblockButton
                    title="Smart paste"
                    icon="bolt"
                    onClick={onSmartPaste}
                    onSuccessMessage="Smart pasted!"
                />
            </CodeblockActions> -->
    </slot>
  </div>
</div>

<style>
  .c-codeblock {
    position: relative;
  }

  .c-codeblock__top-bar-anchor,
  .c-codeblock__actions-bar-anchor {
    position: absolute;
    height: fit-content;
    width: 100%;
    z-index: var(--z-codeblock-actions);
    display: flex;
    justify-content: space-between;
    overflow: hidden;
    /**
         * Because the anchor has absolute positioning, we need to set the
         * background color so that when the codeblock is scrollable, the text
         * doesn't show through the background.
         */
    background-color: var(--user-theme-sidebar-background);
  }

  .c-codeblock__top-bar-anchor {
    top: 0;
    border: 1px solid var(--augment-border-color);
    border-radius: var(--augment-border-radius) var(--augment-border-radius) 0 0;
  }

  .c-codeblock__top-bar-anchor:has(~ :global(.c-codeblock .monaco-editor.focused)) {
    /** When editor is in focus, update the border color to match */
    border-color: var(--augment-focus-border-color) var(--augment-focus-border-color)
      var(--augment-border-color) var(--augment-focus-border-color);
  }

  .c-codeblock__actions-bar-anchor {
    bottom: 0;
  }
  .c-codeblock__top-bar-left {
    overflow: hidden;
  }
</style>
