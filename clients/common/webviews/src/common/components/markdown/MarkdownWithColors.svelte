<script lang="ts">
  import Markdown from "./Markdown.svelte";
  import MarkdownWithColorsCodespan from "./MarkdownWithColorsCodespan.svelte";

  export let markdown: string;

  const fixColorQuote = (markdown: string) => {
    const hexColorRegex = /`?#[0-9a-fA-F]{3,6}`?/g;
    return markdown.replace(hexColorRegex, (match) => {
      if (match.startsWith("`")) return match;
      return `\`${match}\``;
    });
  };

  const renderers = {
    codespan: MarkdownWithColorsCodespan,
  };
</script>

<Markdown markdown={fixColorQuote(markdown)} {renderers} />
