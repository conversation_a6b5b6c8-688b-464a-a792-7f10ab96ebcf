import { type editor } from "monaco-editor";

export const DEFAULT_MONACO_OPTIONS: Partial<editor.IStandaloneEditorConstructionOptions> = {
  lineNumbers: "off",
  wrappingIndent: "same",
  contextmenu: false,
  wordBasedSuggestions: "off",
  renderLineHighlight: "none",
  occurrencesHighlight: "off",
  selectionHighlight: false,
  codeLens: false,
  links: false,
  hover: { enabled: false },
  hideCursorInOverviewRuler: true,
  renderWhitespace: "none",
  renderFinalNewline: "on",
  lineHeight: 16.5,
  fontSize: 11,
};
