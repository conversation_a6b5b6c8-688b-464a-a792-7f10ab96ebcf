import { type FileDetails } from "$vscode/src/webview-providers/webview-messages";
import { type IQualifiedPathName } from "@augment-internal/sidecar-libs/src/workspace/workspace-types";

export interface ResolvePathFn {
  (path: IQualifiedPathName): Promise<FileDetails | undefined>;
}

export interface OpenLocalFileFn {
  (details: FileDetails): void;
}

export interface CodeActionFn {
  (code: string): void;
}
