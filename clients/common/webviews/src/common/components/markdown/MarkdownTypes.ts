export enum MarkdownBlockType {
  plainText = "plainText",
  specialBlock = "specialBlock",
}

export type MarkdownBlock = MarkdownPlainText | MarkdownSpecialBlock;

export class MarkdownPlainText {
  constructor(public text: string) {}
  type = MarkdownBlockType.plainText;
}

export class MarkdownSpecialBlock {
  constructor(public text: string) {}
  type = MarkdownBlockType.specialBlock;
}

export function flattenMarkdownBlocks(markdownBlocks: MarkdownBlock[]) {
  return markdownBlocks.map((block) => block.text).join("");
}

export function* markdownBlocksIterator(markdownBlocks: MarkdownBlock[]) {
  // This iterator has been defined as such because we don't want to iterate character-by-character for
  // blocks of text that are special markdown blocks, as they transform in funny ways that then cause us
  // to show markdown artifacts. Hence, we just yield the whole block as-is.
  for (const block of markdownBlocks) {
    if (block.type === MarkdownBlockType.specialBlock) {
      yield block.text;
    } else {
      for (const char of block.text) {
        yield char;
      }
    }
  }
}
