<script lang="ts">
  import Markdown, { type Renderers } from "@magidoc/plugin-svelte-marked";

  import Codeblock from "./Codeblock.svelte";
  import Codespan from "./Codespan.svelte";
  import Del from "./Del.svelte";
  import MarkdownParagraph from "./MarkdownParagraph.svelte";

  export let markdown: string;
  export let renderers: Renderers = {};
</script>

<div class="c-markdown">
  <Markdown
    source={markdown}
    renderers={{
      codespan: Codespan,
      code: Codeblock,
      paragraph: MarkdownParagraph,
      del: Del,
      ...renderers,
    }}
  />
</div>

<style>
  .c-markdown {
    width: 100%;
  }

  :global(.c-markdown > p) {
    word-break: break-word;
  }

  .c-markdown :global(h1) {
    line-height: 1em;
  }

  /* Smaller default left-padding for lists */
  .c-markdown :global(ul),
  .c-markdown :global(ol) {
    padding-inline-start: var(--ds-spacing-5);
  }

  .c-markdown :global(ul ul),
  .c-markdown :global(ul ol),
  .c-markdown :global(ol ul),
  .c-markdown :global(ol ol) {
    padding-inline-start: var(--ds-spacing-4);
  }

  :global(.c-markdown > table) {
    display: block;
    overflow-x: auto;
  }

  :global(.c-markdown > *:first-child) {
    margin-top: 0;
  }

  :global(.c-markdown > *:last-child) {
    margin-bottom: 0;
  }
  .c-markdown {
    & :global(h1),
    & :global(h2),
    & :global(h3),
    & :global(h4),
    & :global(h5),
    & :global(h6) {
      font-size: 1.5rem;
      font-weight: bold;
      line-height: 150%;
      font-family: var(--augment-font-family);
      padding-left: var(--ds-spacing-2);
    }
    & :global(h1) {
      font-size: 1.125rem;
    }
    & :global(h2) {
      font-size: 1rem;
    }
    & :global(h3) {
      font-size: 0.875rem;
    }
    & :global(h4),
    & :global(h5),
    & :global(h6) {
      font-size: 0.75rem;
    }

    & :global(strong) {
      font-family: var(--augment-font-family);
      font-weight: 600;
    }
    & :global(li),
    & :global(ol),
    & :global(ul) {
      line-height: 150%;
    }

    & :global(li > ul) {
      padding-top: var(--ds-spacing-2);
      padding-bottom: var(--ds-spacing-2);
    }
    & :global(ul) {
      padding-left: var(--ds-spacing-7);
    }
  }
</style>
