<script lang="ts">
  import { onDestroy } from "svelte";
  import DropdownMenuAugment from "$common-webviews/src/design-system/components/DropdownMenuAugment";
  import type { ComponentType } from "svelte";

  export let icon: ComponentType;
  export let idleLabel: string;
  export let successLabel: string;
  export let onSelect: (() => void | Promise<void>) | undefined = undefined;
  export let successDuration: number = 500;
  export let disabled: boolean = false;

  // State for action
  let actionState: "idle" | "success" | "error" = "idle";
  let actionTimeout: ReturnType<typeof setTimeout> | undefined;

  async function handleAction() {
    try {
      await onSelect?.();
      actionState = "success";
      clearTimeout(actionTimeout);
      actionTimeout = setTimeout(() => {
        actionState = "idle";
      }, successDuration);
    } catch (error) {
      actionState = "error";
      actionTimeout = setTimeout(() => {
        actionState = "idle";
      }, successDuration);
    }
  }

  onDestroy(() => {
    clearTimeout(actionTimeout);
  });
</script>

<DropdownMenuAugment.Item
  color={actionState === "error" ? "error" : undefined}
  onSelect={handleAction}
  {disabled}
>
  <svelte:component this={icon} slot="iconLeft" />
  {#if actionState === "error"}
    Action failed
  {:else if actionState === "success"}
    {successLabel}
  {:else}
    {idleLabel}
  {/if}
</DropdownMenuAugment.Item>
