import { expect, test, vi, describe, beforeEach, afterEach } from "vitest";
import { FocusModel } from "./focus-model";

describe("FocusModel", () => {
  const TEST_ITEMS = ["a", "b", "c"];

  let model: FocusModel<string>;

  beforeEach(() => {
    model = new FocusModel(TEST_ITEMS);
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  test("should initialize", () => {
    expect(model).toBeDefined();
    expect(model.items).toEqual(TEST_ITEMS);
  });

  test("should set items", () => {
    const newItems = ["d", "e", "f"];
    model.setItems(newItems);
    expect(model.items).toEqual(newItems);
  });

  test("should set focus", () => {
    model.setFocus("b");
    expect(model.focusedItem).toEqual("b");
    expect(model.focusedItemIdx).toEqual(1);
  });

  test("should focus next", () => {
    model.setFocus("b");
    model.focusNext();
    expect(model.focusedItem).toEqual("c");
    expect(model.focusedItemIdx).toEqual(2);
  });

  test("should focus prev", () => {
    model.setFocus("b");
    model.focusPrev();
    expect(model.focusedItem).toEqual("a");
    expect(model.focusedItemIdx).toEqual(0);
  });

  test("should wrap focus next", () => {
    model.setFocus("c");
    model.focusNext();
    expect(model.focusedItem).toEqual("a");
    expect(model.focusedItemIdx).toEqual(0);
  });

  test("should wrap focus prev", () => {
    model.setFocus("a");
    model.focusPrev();
    expect(model.focusedItem).toEqual("c");
    expect(model.focusedItemIdx).toEqual(2);
  });

  test("should unset focus", () => {
    model.setFocus("b");
    model.setFocus(undefined);
    expect(model.focusedItem).toBeUndefined();
    expect(model.focusedItemIdx).toBeUndefined();
  });

  test("should unset focus on empty items", () => {
    model.setItems([]);
    model.setFocus("b");
    expect(model.focusedItem).toBeUndefined();
    expect(model.focusedItemIdx).toBeUndefined();
  });

  test("should unset focus on focus next", () => {
    model.setItems([]);
    model.focusNext();
    expect(model.focusedItem).toBeUndefined();
    expect(model.focusedItemIdx).toBeUndefined();
  });

  test("should unset focus on focus prev", () => {
    model.setItems([]);
    model.focusPrev();
    expect(model.focusedItem).toBeUndefined();
    expect(model.focusedItemIdx).toBeUndefined();
  });

  test("should notify subscribers", () => {
    const subscriber = vi.fn();
    model.subscribe(subscriber);
    expect(subscriber).toHaveBeenCalledTimes(1);
    model.setFocus("b");
    expect(subscriber).toHaveBeenCalledTimes(2);
  });

  test("should set focus idx", () => {
    model.setFocusIdx(1);
    expect(model.focusedItem).toEqual("b");
    expect(model.focusedItemIdx).toEqual(1);
  });

  test("should unset focus idx", () => {
    model.setFocusIdx(1);
    model.setFocusIdx(undefined);
    expect(model.focusedItem).toBeUndefined();
    expect(model.focusedItemIdx).toBeUndefined();
  });

  test("focus last leaf when list shrinks", () => {
    model.setFocusIdx(2);
    model.setItems(["a", "b"]);
    expect(model.focusedItem).toEqual("b");
    expect(model.focusedItemIdx).toEqual(1);
  });

  test("do not modify focus when list grows", () => {
    model.setItems([]);
    expect(model.focusedItem).toBeUndefined();
    expect(model.focusedItemIdx).toBeUndefined();

    model.setItems(["a", "b"]);
    expect(model.focusedItem).toBeUndefined();
    expect(model.focusedItemIdx).toBeUndefined();

    model.setFocus("a");
    expect(model.focusedItem).toEqual("a");
    expect(model.focusedItemIdx).toEqual(0);

    model.setItems(["a", "b", "c"]);
    expect(model.focusedItem).toEqual("a");
    expect(model.focusedItemIdx).toEqual(0);
  });

  test("wrap around positive focus idx", () => {
    model.setFocusIdx(3);
    expect(model.focusedItem).toEqual("a");
    expect(model.focusedItemIdx).toEqual(0);
  });

  test("wrap around negative focus idx", () => {
    model.setFocusIdx(-1);
    expect(model.focusedItem).toEqual("c");
    expect(model.focusedItemIdx).toEqual(2);
  });

  test("wrap around negative focus idx multiple times", () => {
    model.setFocusIdx(-3);
    expect(model.focusedItem).toEqual("a");
    expect(model.focusedItemIdx).toEqual(0);
  });

  test("wrap around positive focus idx multiple times", () => {
    model.setFocusIdx(5);
    expect(model.focusedItem).toEqual("c");
    expect(model.focusedItemIdx).toEqual(2);
  });
});
