import {
  type MessageBroker,
  type MessageConsumer,
} from "$common-webviews/src/common/utils/message-broker";
import {
  type DataWebviewMessageWithId,
  type EmptyMessage,
  type GetSharedWebviewStateMessage,
  type GetSharedWebviewStateResponseMessage,
  WebViewMessageType,
} from "$vscode/src/webview-providers/webview-messages";
import { type Readable } from "svelte/motion";

/**
 * An in-memory store that can be used to share state between webviews.
 */
export class SharedWebviewStoreModel<T>
  implements Readable<SharedWebviewStoreModel<T>>, MessageConsumer
{
  private subscribers: Set<(value: SharedWebviewStoreModel<T>) => void> = new Set();

  subscribe(run: (value: SharedWebviewStoreModel<T>) => void): () => void {
    this.subscribers.add(run);
    run(this);
    return () => {
      this.subscribers.delete(run);
    };
  }

  private notifySubscribers(): void {
    this.subscribers.forEach((sub) => sub(this));
  }

  constructor(
    private readonly _msgBroker: MessageBroker,
    private _state: T | undefined = undefined,
    private validateState: (state: unknown) => state is T,
    private readonly _storeId: string,
  ) {
    // If we have an initial state, tell other webviews about it.
    if (_state) {
      this.setStateInternal(_state);
    }
  }

  get state(): T | undefined {
    return this._state;
  }

  get storeId(): string {
    return this._storeId;
  }

  /**
   * When we get a message from the extension, we need to make sure it's for this store
   * specifically before applying it.
   */
  private shouldAcceptMessage(message: DataWebviewMessageWithId<unknown>, data: unknown): boolean {
    return message.id === this.storeId && this.validateState(data);
  }

  update(updater: (state: T | undefined) => T | undefined): void {
    const newState = updater(this._state);
    if (newState === undefined) {
      return;
    }
    this.setStateInternal(newState);
  }

  setState(state: T): void {
    this.setStateInternal(state);
  }

  private async setStateInternal(state: T): Promise<void> {
    if (JSON.stringify(this._state) === JSON.stringify(state)) {
      return;
    }
    this._state = state;
    // Notify any other subscribed webviews
    this._msgBroker.postMessage({
      type: WebViewMessageType.updateSharedWebviewState,
      data: state,
      id: this.storeId,
    });
  }

  public async fetchStateFromExtension(): Promise<void> {
    const response = await this._msgBroker.send<
      GetSharedWebviewStateMessage,
      GetSharedWebviewStateResponseMessage<T> | EmptyMessage
    >({
      type: WebViewMessageType.getSharedWebviewState,
      id: this.storeId,
      data: {},
    });
    if (response.type === WebViewMessageType.getSharedWebviewStateResponse) {
      if (this.shouldAcceptMessage(response, response.data)) {
        this._state = response.data;
        this.notifySubscribers();
      }
    }
  }

  handleMessageFromExtension(message: MessageEvent): boolean {
    switch (message.data.type) {
      case WebViewMessageType.updateSharedWebviewState: {
        if (this.shouldAcceptMessage(message.data, message.data.data)) {
          this._state = message.data.data;
          this.notifySubscribers();
          return true;
        } else {
          return false;
        }
      }
      case WebViewMessageType.getSharedWebviewStateResponse: {
        if (this.shouldAcceptMessage(message.data, message.data.data)) {
          this._state = message.data.data;
          this.notifySubscribers();
          return true;
        } else {
          return false;
        }
      }
      default:
        return false;
    }
  }
}
