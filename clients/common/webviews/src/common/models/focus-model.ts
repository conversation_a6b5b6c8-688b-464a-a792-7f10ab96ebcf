import type { Readable } from "svelte/store";

/**
 * Interface for focus data.
 *
 * In general, used for situations where users may want keyboard interactions to toggle through
 * different elements, such as codeblocks, diff chunks, etc.
 */

export interface IFocusData<T> {
  /** Array of items */
  items: T[];
  /** The currently focused item, if any */
  focusedItem: T | undefined;
  /** The index of the currently focused item, if any */
  focusedItemIdx: number | undefined;
}

/**
 * Interface for a focus model, extending Svelte's Readable store.
 * @template T The type of items being focused.
 */
export interface IFocusModel<T> extends Readable<IFocusData<T>> {
  /** Set the items in the focus model */
  setItems(items: T[]): void;
  /** Set focus to a specific item */
  setFocus(item: T | undefined): void;
  /** Set focus to a specific item index */
  setFocusIdx(idx: number | undefined): void;
  /** Focus the next item */
  focusNext(): number | undefined;
  /** Focus the previous item */
  focusPrev(): number | undefined;
}

export interface IGetIdxOptions {
  nowrap?: boolean;
}

/**
 * Implementation of the IFocusModel interface.
 * Manages focus state for a collection of items.
 * @template T The type of items being focused.
 */
export class FocusModel<T> implements IFocusModel<T> {
  private _items: T[] = [];
  private _focusedItemIdx: number | undefined;

  private _subscribers: Set<(model: FocusModel<T>) => void> = new Set();

  /**
   * Creates a new FocusModel instance.
   * @param items Initial array of items (optional).
   */
  constructor(items: T[] = []) {
    this._items = items;
  }

  /**
   * Svelte store subscription method.
   * @param sub Subscription callback function.
   * @returns Unsubscribe function.
   */
  subscribe = (sub: (model: FocusModel<T>) => void): (() => void) => {
    this._subscribers.add(sub);
    sub(this);
    return () => {
      this._subscribers.delete(sub);
    };
  };

  /** Get the current array of items */
  get items(): T[] {
    return this._items;
  }

  /** Get the currently focused item, if any */
  get focusedItem(): T | undefined {
    if (this._focusedItemIdx === undefined) {
      return undefined;
    }
    return this._items[this._focusedItemIdx];
  }

  /** Get the index of the currently focused item, if any */
  get focusedItemIdx(): number | undefined {
    return this._focusedItemIdx;
  }

  /**
   * Set the items in the focus model. If the items array is empty, unset focus.
   * If the index is suddenly out-of-range, set focus to a previous item.
   * Finally, if there is no currently focused item, but items exist, focus the first item.
   *
   * @param items New array of items.
   */
  setItems = (items: T[]): void => {
    this._items = items;
    if (this._items.length === 0) {
      this.setFocusIdx(undefined);
    } else if (this._focusedItemIdx !== undefined && this._focusedItemIdx >= this._items.length) {
      this.setFocusIdx(this._items.length - 1);
    } else if (this._focusedItemIdx === undefined) {
      this.setFocusIdx(undefined);
    } else {
      this.setFocusIdx(this._focusedItemIdx);
    }
  };

  /**
   * Set focus to a specific item.
   * If the item is not in the items array, unset focus.
   * @param item Item to focus.
   */
  setFocus = (item: T | undefined): void => {
    if (item !== undefined && item === this.focusedItem) {
      return;
    }

    const itemIdx = item ? this._items.indexOf(item) : -1;
    if (itemIdx === -1) {
      this.setFocusIdx(undefined);
    } else {
      this.setFocusIdx(itemIdx);
    }
  };

  /**
   * Set focus to a specific item index.
   * If the index is -1, wrap around.
   *
   * @param idx Index of the item to focus.
   */
  setFocusIdx = (idx: number | undefined): void => {
    if (idx === this._focusedItemIdx || this._items.length === 0) {
      return;
    }

    if (idx === undefined) {
      this._focusedItemIdx = undefined;
      this.notifySubscribers();
      return;
    }

    // Get greatest multiple of items.length that is less than or equal to idx
    const idxOffset = Math.floor(idx / this._items.length) * this._items.length;
    this._focusedItemIdx = (idx - idxOffset) % this._items.length;
    this.notifySubscribers();
  };

  initFocusIdx = (idx: number | undefined): boolean => {
    if (this._focusedItemIdx !== undefined) {
      return false;
    }
    this.setFocusIdx(idx);
    return true;
  };

  /**
   * Focus the next item. If there is no currently focused item, focus the first item.
   * If there are no items, unset focus. Wraps around.
   *
   * @returns The index of the focused item, or undefined if there is no next item.
   */
  focusNext = (): number | undefined => {
    const nextIdx = this.nextIdx();
    if (nextIdx === undefined) {
      return undefined;
    }
    this.setFocus(this._items[nextIdx]);
    return nextIdx;
  };

  /**
   * Focus the previous item. If there is no currently focused item, focus the last item.
   * If there are no items, unset focus. Wraps around.
   *
   * @returns The index of the focused item, or undefined if there is no previous item.
   */
  focusPrev = (): number | undefined => {
    const prevIdx = this.prevIdx();
    if (prevIdx === undefined) {
      return undefined;
    }
    this.setFocus(this._items[prevIdx]);
    return prevIdx;
  };

  /**
   * Set focus to the first item.
   */
  public prevIdx = (opts: IGetIdxOptions = {}): number | undefined => {
    if (this._items.length === 0) {
      return undefined;
    }

    // If there is no currently focused item, focus the last item
    if (this._focusedItemIdx === undefined) {
      return this._items.length - 1;
    }

    // If nowrap is true, and the currently focused item is the first item, keep the current focus
    if (opts.nowrap && this._focusedItemIdx === 0) {
      return 0;
    }

    // Focus the previous item
    return (this._focusedItemIdx - 1 + this._items.length) % this._items.length;
  };

  /**
   * Get the index of the currently focused item, or undefined if there is no currently focused item.
   */
  public nextIdx = (opts: IGetIdxOptions = {}): number | undefined => {
    if (this._items.length === 0) {
      return undefined;
    }

    // If there is no currently focused item, focus the first item
    if (this._focusedItemIdx === undefined) {
      return 0;
    }

    // If nowrap is true, and the currently focused item is the last item, keep the current focus
    if (opts.nowrap && this._focusedItemIdx === this._items.length - 1) {
      return this._items.length - 1;
    }

    return (this._focusedItemIdx + 1) % this._items.length;
  };

  /**
   * Notify all subscribers of changes in the focus model.
   */
  private notifySubscribers = () => {
    this._subscribers.forEach((sub) => sub(this));
  };
}
