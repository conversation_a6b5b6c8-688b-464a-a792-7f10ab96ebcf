import { describe, it, expect, vi, beforeEach } from "vitest";
import type { IHotkeysCofiguration } from "./types";

// Helper to control platform before (re)importing the module under test
const setNavigatorPlatform = (value: string) => {
  Object.defineProperty(window.navigator, "platform", {
    configurable: true,
    value,
  });
};

// Dynamic import to ensure isMac is evaluated against the mocked platform
const importModule = async () => await import("./multiplatform-keys-config-map");

describe("multiplatform-keys-config-map", () => {
  beforeEach(() => {
    vi.restoreAllMocks();
  });

  describe("isMac detection", () => {
    it("is true when navigator.platform includes 'Mac'", async () => {
      setNavigatorPlatform("MacIntel");
      await vi.resetModules();
      const mod = await importModule();
      expect(mod.isMac).toBe(true);
    });

    it("is false on non-mac platforms", async () => {
      setNavigatorPlatform("Win32");
      await vi.resetModules();
      const mod = await importModule();
      expect(mod.isMac).toBe(false);
    });
  });

  describe("mapKeyCombo", () => {
    it("maps command and option to ctrl and alt", async () => {
      // platform independent
      await vi.resetModules();
      const { mapKeyCombo } = await importModule();

      expect(mapKeyCombo("command+b")).toBe("ctrl+b");
      expect(mapKeyCombo("option+f")).toBe("alt+f");
      expect(mapKeyCombo("command+option+k")).toBe("ctrl+alt+k");
      expect(mapKeyCombo("shift+option+enter")).toBe("shift+alt+enter");
      // unchanged when no mapping applies
      expect(mapKeyCombo("ctrl+s")).toBe("ctrl+s");
    });
  });

  describe("mapKeybindingConfig", () => {
    it("returns the same object on mac (no mapping)", async () => {
      setNavigatorPlatform("MacIntel");
      await vi.resetModules();
      const { mapKeybindingConfig } = await importModule();

      const handler = vi.fn();
      const config: IHotkeysCofiguration = {
        "command+b": { name: "Bold", description: "toggle bold", handler },
        "ctrl+o": { name: "Open", description: "open file", handler },
      };

      const result = mapKeybindingConfig(config);
      // On Mac, it should return the exact same reference (implementation detail)
      expect(result).toBe(config);
      expect(Object.keys(result)).toEqual(["command+b", "ctrl+o"]);
    });

    it("maps mac modifiers to non-mac equivalents when not on mac", async () => {
      setNavigatorPlatform("Linux x86_64");
      await vi.resetModules();
      const { mapKeybindingConfig } = await importModule();

      const handler = vi.fn();
      const config: IHotkeysCofiguration = {
        "command+b": { name: "Bold", description: "toggle bold", handler },
        "option+k": { name: "OptionK", description: "option k", handler },
        "command+option+k": { name: "Both", description: "both", handler },
        "ctrl+o": { name: "Open", description: "open file", handler }, // no change expected
      };

      const result = mapKeybindingConfig(config);

      // Should not be the same object
      expect(result).not.toBe(config);

      // Entries with mac modifiers should include both original and mapped combos
      expect(result["command+b, ctrl+b"]).toBe(config["command+b"]);
      expect(result["option+k, alt+k"]).toBe(config["option+k"]);
      expect(result["command+option+k, ctrl+alt+k"]).toBe(config["command+option+k"]);

      // Entries without mac-specific modifiers remain as-is
      expect(result["ctrl+o"]).toBe(config["ctrl+o"]);

      // Sanity: no unexpected extra keys
      expect(Object.keys(result).sort()).toEqual(
        [
          "command+b, ctrl+b",
          "option+k, alt+k",
          "command+option+k, ctrl+alt+k",
          "ctrl+o",
        ].sort(),
      );
    });
  });
});

