import { type IHotkeysCofiguration } from "./types";

/**
 * `navigator.platform` is deprecated, but is still the least-bad way to detect modifier
 * keys on Windows/Linux vs MacOS.
 *
 * https://developer.mozilla.org/en-US/docs/Web/API/Navigator/platform#examples
 */
export const isMac: boolean = navigator.platform.toUpperCase().indexOf("MAC") >= 0;

const keysMultiplatformMap: Record<string, string> = {
  "command": "ctrl",
  "option": "alt",
};

export const mapKeyCombo = (keyCombo: string): string => {
  if (isMac) {
    return keyCombo;
  }
  return keyCombo.split('+').map((key) => keysMultiplatformMap[key] || key).join('+');
}

export const mapKeybindingConfig = (config: IHotkeysCofiguration): IHotkeysCofiguration => {
    // we have config for mac by default, no need to map
    if (isMac) {
        return config;
    }
    const keys = Object.keys(config);
    const newConfig: IHotkeysCofiguration = {};
    for (const keyCombo of keys) {
        const mappedKeyCombo = mapKeyCombo(keyCombo);
        if (mappedKeyCombo === keyCombo) {
            newConfig[keyCombo] = config[keyCombo];
            continue;
        }
        newConfig[`${keyCombo}, ${mappedKeyCombo}`] = config[keyCombo];
    }
    return newConfig;
}