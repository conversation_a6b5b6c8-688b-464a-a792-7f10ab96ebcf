export type HotkeyCallback = (e: KeyboardEvent, handler: any, context: any) => void;


export interface HotKeyConfiguration {
  name: string;
  description: string;
  commandName?: string;
  handler: HotkeyCallback;
}

export interface HotKeyCommandConfiguration {
  name: string;
  description: string;
  commandName: string;
  handler?: HotkeyCallback;
}


export interface IHotkeysCofiguration {
  [key: string]: HotKeyConfiguration | HotKeyCommandConfiguration
}

export interface IHotkeysActionParams {
  config: IHotkeysCofiguration;
  context: () => any;
  scope?: string;
}