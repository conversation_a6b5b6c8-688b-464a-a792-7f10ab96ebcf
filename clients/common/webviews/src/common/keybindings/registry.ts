const commands: Record<string, Array<() => void> | undefined> = {};

export const registerCommand = (name: string, handler: (() => void) = () => {}) => {
  commands[name] = (commands[name] || []).concat(handler);
  return () => {
	  const handlers = commands[name] || [];
	  commands[name] = handlers.filter((commandHandler) => {
		  return commandHandler !== handler;
	  });
	  if (commands[name].length === 0) {
	    commands[name] = undefined;
	  }
  }
};

export const triggerCommand = (command: string) => {
  const handlers = commands[command] || [];
  if (handlers.length === 0) {
    return false;
  }
  try {
    handlers.forEach((handler) => handler());
  } finally {
    return true;
  }
};