/**
 * Utility functions for playing sound effects in the webview
 *
 * This module handles the browser's user gesture requirements for audio playback.
 * Modern browsers require audio to be initiated by a user gesture, so this module:
 *
 * 1. Automatically sets up event listeners to unlock audio on first user interaction
 * 2. Gracefully handles cases where audio cannot be played due to gesture requirements
 * 3. Provides fallback behavior when audio files fail to load
 *
 * Usage:
 * - Use `playSound()` to play sound effects - it will work after the first user interaction
 * - Call `disposeSounds()` when cleaning up to release resources
 */

import agentCompleteSound from "$common-webviews/src/assets/sounds/agent-complete.mp3?url";

/**
 * Sound effect types available in the application
 */
export enum SoundEffect {
  /* eslint-disable-next-line @typescript-eslint/naming-convention */
  AGENT_COMPLETE = "agent-complete",
}

/**
 * Configuration for sound effects
 */
interface SoundConfig {
  enabled: boolean;
  volume: number; // 0.0 to 1.0
}

/**
 * Sound file mappings
 */
const SOUND_FILES: Record<SoundEffect, string> = {
  [SoundEffect.AGENT_COMPLETE]: agentCompleteSound,
};

/**
 * Extended HTMLAudioElement with unlock state
 */
interface AudioElementWithState extends HTMLAudioElement {
  _isUnlocked?: boolean;
}

/**
 * Singleton class for managing sound utilities
 */
class SoundUtils {
  private static _instance: SoundUtils;
  private audioCache = new Map<SoundEffect, AudioElementWithState>();

  private constructor() {
    // Private constructor to prevent direct instantiation
  }

  /**
   * Get the singleton instance
   */
  public static getInstance(): SoundUtils {
    if (!SoundUtils._instance) {
      SoundUtils._instance = new SoundUtils();
    }
    return SoundUtils._instance;
  }

  /**
   * Create or get cached audio element for a sound effect
   */
  private retrieveAudioElement(
    soundEffect: SoundEffect,
    soundConfig: SoundConfig,
  ): AudioElementWithState {
    let audio = this.audioCache.get(soundEffect);

    if (!audio) {
      audio = new Audio() as AudioElementWithState;
      audio.src = SOUND_FILES[soundEffect];
      audio.volume = soundConfig.volume;
      audio.preload = "auto";
      audio._isUnlocked = false;
      this.audioCache.set(soundEffect, audio);
    } else {
      // Update volume in case it changed
      audio.volume = soundConfig.volume;
    }

    return audio;
  }

  /**
   * Play a sound effect
   */
  public async playSound(soundEffect: SoundEffect, soundConfig: SoundConfig): Promise<void> {
    if (!soundConfig.enabled) {
      return;
    }

    try {
      const audio = this.retrieveAudioElement(soundEffect, soundConfig);

      // Reset the audio to the beginning in case it was played before
      audio.currentTime = 0;

      // Play the sound
      await audio.play();
    } catch (error) {
      // If this is the first attempt and audio is locked, provide helpful message
      if (error instanceof DOMException && error.name === "NotAllowedError") {
        console.error("Audio blocked by browser policy. Sound will work after user interaction.");
        return;
      }

      // For other errors, log but don't throw
      console.error("Failed to play sound:", error);
    }
  }

  /**
   * Unlock audio by playing a sound at 0 volume
   * This satisfies browser autoplay policies that require user interaction
   */
  public async unlockSoundForConfig(soundConfig: SoundConfig): Promise<void> {
    if (!soundConfig.enabled) {
      return;
    }

    const audio = this.retrieveAudioElement(SoundEffect.AGENT_COMPLETE, soundConfig);

    if (audio._isUnlocked) {
      return;
    }

    try {
      // Play agent complete sound at 0 volume to unlock audio
      await this.playSound(SoundEffect.AGENT_COMPLETE, { enabled: true, volume: 0 });
      audio._isUnlocked = true;
    } catch (error) {
      console.warn("Failed to unlock sound:", error);
    }
  }

  /**
   * Dispose of all cached audio elements and cleanup AudioContext
   */
  public disposeSounds(): void {
    this.audioCache.forEach((audio) => {
      audio.pause();
      audio.src = "";
      // Reset unlock state for this audio element
      audio._isUnlocked = false;
    });
    this.audioCache.clear();
  }
}

/**
 * Get the singleton instance of SoundUtils
 */
const soundUtils = SoundUtils.getInstance();

/**
 * Export the singleton instance for direct access if needed
 */
export { soundUtils };
