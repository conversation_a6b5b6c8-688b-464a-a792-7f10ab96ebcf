import { describe, it, expect, vi } from "vitest";
import { formatTimestampForChatMessage, formatFullTimestamp, differenceInDays, setJitteredInterval } from "./time-utils";

describe("time-utils", () => {
  describe("formatTimestampForChatMessage", () => {
    it("should return empty string for undefined timestamp", () => {
      expect(formatTimestampForChatMessage(undefined)).toBe("");
    });

    it("should format time only for recent timestamps", () => {
      const now = new Date();
      const recent = new Date(now.getTime() - 2 * 60 * 60 * 1000); // 2 hours ago
      const result = formatTimestampForChatMessage(recent);
      expect(result).toMatch(/^\d{1,2}:\d{2} (AM|PM)$/); // HH:MM AM/PM format
    });

    it("should format date and time for older timestamps", () => {
      const now = new Date();
      const old = new Date(now.getTime() - 25 * 60 * 60 * 1000); // 25 hours ago
      const result = formatTimestampForChatMessage(old);
      expect(result).toMatch(/^[A-Za-z]{3} \d{1,2} \d{1,2}:\d{2} (AM|PM)$/); // MMM DD HH:MM AM/PM format
    });
  });

  describe("formatFullTimestamp", () => {
    it("should return empty string for undefined timestamp", () => {
      expect(formatFullTimestamp(undefined)).toBe("");
    });

    it("should format full date and time", () => {
      const date = new Date("2023-12-25T15:30:00Z");
      const result = formatFullTimestamp(date);
      expect(result).toMatch(/^[A-Za-z]{3} \d{1,2}, \d{4} \d{1,2}:\d{2} (AM|PM)$/); // MMM DD, YYYY HH:MM AM/PM format
    });
  });

  describe("differenceInDays", () => {
    it("should calculate positive difference in days", () => {
      const date1 = new Date("2023-12-25");
      const date2 = new Date("2023-12-20");
      expect(differenceInDays(date1, date2)).toBe(5);
    });

    it("should calculate negative difference in days", () => {
      const date1 = new Date("2023-12-20");
      const date2 = new Date("2023-12-25");
      expect(differenceInDays(date1, date2)).toBe(-5);
    });

    it("should return 0 for same dates", () => {
      const date = new Date("2023-12-25");
      expect(differenceInDays(date, date)).toBe(0);
    });
  });

  describe("setJitteredInterval", () => {
    it("should set interval between 75% and 100% of base interval by default (25% jitter)", () => {
      const setIntervalSpy = vi.spyOn(global, "setInterval");
      const mockCallback = vi.fn();
      const baseInterval = 60000; // 60 seconds
      const minExpected = baseInterval * 0.75; // 45 seconds (75% of base)
      const maxExpected = baseInterval; // 60 seconds (100% of base)

      // Test multiple times to ensure randomness works correctly
      for (let i = 0; i < 100; i++) {
        setJitteredInterval(mockCallback, baseInterval);
        const [, actualInterval] = setIntervalSpy.mock.calls[i];
        expect(actualInterval).toBeGreaterThanOrEqual(minExpected);
        expect(actualInterval).toBeLessThanOrEqual(maxExpected);
      }

      setIntervalSpy.mockRestore();
    });

    it("should work with different base intervals", () => {
      const setIntervalSpy = vi.spyOn(global, "setInterval");
      const mockCallback = vi.fn();
      const testCases = [1000, 5000, 30000, 120000, 300000];

      testCases.forEach((baseInterval, caseIndex) => {
        const minExpected = baseInterval * 0.75; // 25% jitter = 75%-100% range
        const maxExpected = baseInterval;

        for (let i = 0; i < 10; i++) {
          setJitteredInterval(mockCallback, baseInterval);
          const callIndex = caseIndex * 10 + i;
          const [, actualInterval] = setIntervalSpy.mock.calls[callIndex];
          expect(actualInterval).toBeGreaterThanOrEqual(minExpected);
          expect(actualInterval).toBeLessThanOrEqual(maxExpected);
        }
      });

      setIntervalSpy.mockRestore();
    });

    it("should produce different values on multiple calls", () => {
      const setIntervalSpy = vi.spyOn(global, "setInterval");
      const mockCallback = vi.fn();
      const baseInterval = 60000;
      const results = new Set();

      // Generate 50 results - they should not all be the same
      for (let i = 0; i < 50; i++) {
        setJitteredInterval(mockCallback, baseInterval);
        const [, actualInterval] = setIntervalSpy.mock.calls[i];
        results.add(actualInterval);
      }

      // With randomness, we should get multiple unique values
      expect(results.size).toBeGreaterThan(1);
      setIntervalSpy.mockRestore();
    });

    it("should handle edge case with very small intervals", () => {
      const setIntervalSpy = vi.spyOn(global, "setInterval");
      const mockCallback = vi.fn();
      const baseInterval = 1; // 1ms
      const minExpected = 0.75; // 25% jitter = 75%-100% range
      const maxExpected = 1;

      setJitteredInterval(mockCallback, baseInterval);
      const [, actualInterval] = setIntervalSpy.mock.calls[0];
      expect(actualInterval).toBeGreaterThanOrEqual(minExpected);
      expect(actualInterval).toBeLessThanOrEqual(maxExpected);

      setIntervalSpy.mockRestore();
    });

    it("should be deterministic with mocked Math.random", () => {
      const setIntervalSpy = vi.spyOn(global, "setInterval");
      const mockCallback = vi.fn();
      const baseInterval = 60000;
      const mockRandom = vi.spyOn(Math, "random");

      // Mock Math.random to return 0.5 (middle of range)
      mockRandom.mockReturnValue(0.5);

      setJitteredInterval(mockCallback, baseInterval);
      const [, actualInterval] = setIntervalSpy.mock.calls[0];
      // With 25% jitter: 75%-100% range, Math.random=0.5 gives middle = 87.5%
      const expected = baseInterval * 0.75 + (baseInterval * 0.25 * 0.5); // 75% + half of the 25% range
      expect(actualInterval).toBe(expected);

      mockRandom.mockRestore();
      setIntervalSpy.mockRestore();
    });

    it("should accept custom jitter parameter", () => {
      const setIntervalSpy = vi.spyOn(global, "setInterval");
      const mockCallback = vi.fn();
      const baseInterval = 60000;
      const customJitter = 0.5; // 50% jitter = 50%-100% range (more variance)
      const minExpected = baseInterval * (1 - customJitter); // 30 seconds (50% of base)
      const maxExpected = baseInterval; // 60 seconds (100% of base)

      for (let i = 0; i < 50; i++) {
        setJitteredInterval(mockCallback, baseInterval, customJitter);
        const [, actualInterval] = setIntervalSpy.mock.calls[i];
        expect(actualInterval).toBeGreaterThanOrEqual(minExpected);
        expect(actualInterval).toBeLessThanOrEqual(maxExpected);
      }

      setIntervalSpy.mockRestore();
    });

    it("should validate jitter parameter and default to 0.25 for invalid values", () => {
      const setIntervalSpy = vi.spyOn(global, "setInterval");
      const mockCallback = vi.fn();
      const baseInterval = 60000;
      const defaultMinExpected = baseInterval * 0.75; // 45 seconds (25% jitter = 75%-100%)
      const maxExpected = baseInterval; // 60 seconds

      // Test invalid values that should default to 0.25
      const invalidValues = [-0.1, 1.1, 2, -1, NaN, Infinity, -Infinity];

      invalidValues.forEach((invalidValue, valueIndex) => {
        for (let i = 0; i < 10; i++) {
          setJitteredInterval(mockCallback, baseInterval, invalidValue);
          const callIndex = valueIndex * 10 + i;
          const [, actualInterval] = setIntervalSpy.mock.calls[callIndex];
          expect(actualInterval).toBeGreaterThanOrEqual(defaultMinExpected);
          expect(actualInterval).toBeLessThanOrEqual(maxExpected);
        }
      });

      setIntervalSpy.mockRestore();
    });

    it("should work with boundary edge cases", () => {
      const setIntervalSpy = vi.spyOn(global, "setInterval");
      const mockCallback = vi.fn();
      const baseInterval = 60000;

      // Test with jitter = 0 (no jitter, always 100% of base interval)
      for (let i = 0; i < 10; i++) {
        setJitteredInterval(mockCallback, baseInterval, 0);
        const [, actualInterval] = setIntervalSpy.mock.calls[i];
        expect(actualInterval).toBe(baseInterval);
      }

      // Test with jitter = 1 (maximum jitter, 0%-100% range)
      for (let i = 0; i < 10; i++) {
        setJitteredInterval(mockCallback, baseInterval, 1);
        const [, actualInterval] = setIntervalSpy.mock.calls[10 + i];
        expect(actualInterval).toBeGreaterThanOrEqual(0);
        expect(actualInterval).toBeLessThanOrEqual(baseInterval);
      }

      // Test with jitter = 0.1 (low jitter, 90%-100% range)
      const minExpected = baseInterval * 0.9;
      for (let i = 0; i < 10; i++) {
        setJitteredInterval(mockCallback, baseInterval, 0.1);
        const [, actualInterval] = setIntervalSpy.mock.calls[20 + i];
        expect(actualInterval).toBeGreaterThanOrEqual(minExpected);
        expect(actualInterval).toBeLessThanOrEqual(baseInterval);
      }

      setIntervalSpy.mockRestore();
    });

    it("should return a valid interval ID that can be cleared", () => {
      const clearIntervalSpy = vi.spyOn(global, "clearInterval");
      const mockCallback = vi.fn();

      const intervalId = setJitteredInterval(mockCallback, 1000);
      expect(intervalId).toBeDefined();

      clearInterval(intervalId);
      expect(clearIntervalSpy).toHaveBeenCalledWith(intervalId);

      clearIntervalSpy.mockRestore();
    });
  });
});
