import extensions from "../components/language-icons/extensions.json";

// Create extensionsMap directly to avoid import issues
const extensionsMap = new Map<string, string>(
  (extensions as [string, string[]][]).flatMap(([lang, extensions]) =>
    (extensions ?? [lang]).map((ext) => [ext, lang]),
  ),
);

/**
 * Utility function to get Monaco language ID from file path.
 *
 * This function reuses the comprehensive language detection logic from the language-icon
 * utility, which is based on VSCode's extension mappings and handles complex cases like:
 * - Multi-part extensions (e.g., .d.ts)
 * - Full filenames (e.g., Dockerfile, Makefile)
 * - Case-insensitive matching
 *
 * @param name - File path or filename
 * @returns Monaco language ID or undefined if not detected
 */
export const resolveMonacoLanguage = (name: string): string | undefined => {
  if (!name) return undefined;

  // Use the same logic as resolveLanguage from language-icon
  const filename = name.split("/").at(-1);
  if (!filename) return undefined;

  const fName = filename.toLocaleLowerCase();
  const exts = fName.split(".");

  const detectedLanguage =
    extensionsMap.get(fName) ?? // match full names, like dockerfile
    extensionsMap.get(exts.at(-1) ?? "") ?? // match extensions, like .js
    extensionsMap.get(exts.slice(-2).join(".")) ?? // match extensions with two parts, like .d.ts
    undefined;

  // Map some language names to Monaco-specific identifiers where they differ
  /* eslint-disable @typescript-eslint/naming-convention */
  const monacoLanguageMap: Record<string, string> = {
    javascriptreact: "javascript",
    typescriptreact: "typescript",
    shellscript: "shell",
    "cuda-cpp": "cpp",
    jsonc: "json",
    jsonl: "json",
    properties: "ini",
    "objective-c": "objective-c",
    "objective-cpp": "objective-cpp",
    restructuredtext: "restructuredtext",
  };
  /* eslint-enable @typescript-eslint/naming-convention */

  return detectedLanguage ? (monacoLanguageMap[detectedLanguage] ?? detectedLanguage) : undefined;
};
