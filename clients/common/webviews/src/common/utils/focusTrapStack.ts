/**
 * Focus Trap Stack Management
 *
 * Provides a centralized way to manage multiple focus traps in a LIFO (Last In, First Out) manner.
 * This ensures proper coordination between modals, dropdowns, and other focus-trapping components.
 */

/**
 * Extended Document interface that includes the focus trap stack property
 */
interface DocumentWithFocusTrapStack extends Document {
  [key: string]: unknown;
}

/**
 * Focus trap stack manager that stores the stack on the document to avoid global state issues
 */
export class FocusTrapStack {
  // eslint-disable-next-line @typescript-eslint/naming-convention
  private static readonly STACK_KEY = "__focusTrapStack";

  /**
   * Get the focus trap stack for the current document
   * Creates a new stack if one doesn't exist
   */
  private static getStack(): HTMLElement[] {
    const doc = typeof document !== "undefined" ? (document as DocumentWithFocusTrapStack) : null;
    if (!doc) return [];

    // Store stack on document to avoid global state
    if (!doc[FocusTrapStack.STACK_KEY]) {
      doc[FocusTrapStack.STACK_KEY] = [];
    }
    return doc[FocusTrapStack.STACK_KEY] as HTMLElement[];
  }

  /**
   * Add an element to the focus trap stack
   * The element becomes the active trap (top of stack)
   */
  static add(element: HTMLElement): void {
    const stack = FocusTrapStack.getStack();

    // Avoid duplicates
    if (!stack.includes(element)) {
      stack.push(element);
    }
  }

  /**
   * Remove an element from the focus trap stack
   * Returns true if the element was found and removed
   */
  static remove(element: HTMLElement): boolean {
    const stack = FocusTrapStack.getStack();
    const index = stack.indexOf(element);

    if (index !== -1) {
      stack.splice(index, 1);
      return true;
    }

    return false;
  }

  /**
   * Check if an element is the currently active trap (top of stack)
   */
  static isActive(element: HTMLElement): boolean {
    const stack = FocusTrapStack.getStack();
    return stack.length > 0 && stack[stack.length - 1] === element;
  }

  /**
   * Get the currently active trap element (top of stack)
   * Returns undefined if stack is empty
   */
  static getActive(): HTMLElement | undefined {
    const stack = FocusTrapStack.getStack();
    return stack.length > 0 ? stack[stack.length - 1] : undefined;
  }

  /**
   * Get the current size of the stack
   */
  static size(): number {
    return FocusTrapStack.getStack().length;
  }

  /**
   * Check if the stack is empty
   */
  static isEmpty(): boolean {
    return FocusTrapStack.size() === 0;
  }

  /**
   * Clear all elements from the stack
   * Useful for cleanup or testing
   */
  static clear(): void {
    const doc = typeof document !== "undefined" ? (document as DocumentWithFocusTrapStack) : null;
    if (doc) {
      doc[FocusTrapStack.STACK_KEY] = [];
    }
  }

  /**
   * Get a copy of the current stack (for debugging/testing)
   */
  static getAll(): HTMLElement[] {
    return [...FocusTrapStack.getStack()];
  }
}

/**
 * Legacy function for backward compatibility
 * @deprecated Use FocusTrapStack.getAll() instead
 */
export function getTrapStack(): HTMLElement[] {
  return FocusTrapStack.getAll();
}
