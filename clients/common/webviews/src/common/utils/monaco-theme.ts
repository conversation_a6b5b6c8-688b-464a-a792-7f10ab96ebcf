import {
  UserThemeCategory,
  type UserThemeIntensity,
} from "../hosts/user-themes/augment-theme-attributes";

export const HIGHT_CONTRAST_CATEGORY_TO_MONACO_THEME: Map<UserThemeCategory, string> = new Map([
  [UserThemeCategory.light, "hc-light"],
  [UserThemeCategory.dark, "hc-black"],
]);
export const CATEGORY_TO_MONACO_THEME: Map<UserThemeCategory, string> = new Map([
  [UserThemeCategory.light, "vs"],
  [UserThemeCategory.dark, "vs-dark"],
]);

export function getMonacoTheme(
  category: UserThemeCategory | undefined,
  intensity: UserThemeIntensity | undefined,
): string {
  if (!category) {
    return "";
  }

  let themeMap = CATEGORY_TO_MONACO_THEME;
  if (intensity === "high-contrast") {
    themeMap = HIGHT_CONTRAST_CATEGORY_TO_MONACO_THEME;
  }
  return themeMap.get(category) || "";
}
