/**
 * Color utility functions for working with hex colors.
 */

/**
 * Determines if a hex color is dark based on its relative luminance.
 * Uses the formula: 0.299*R + 0.587*G + 0.114*B < threshold
 *
 * This is a common algorithm for calculating perceived brightness based on the
 * human eye's sensitivity to different colors (more sensitive to green, less to blue).
 *
 * Reference: https://www.w3.org/TR/AERT/#color-contrast
 *
 * @param color - Hex color string (e.g., "#fff" or "#ffffff")
 * @throws {Error} If color parameter is not a valid hex color string
 * @returns boolean - true if the color is dark, false otherwise
 */
export function isColorDark(color: string): boolean {
  // Validate input format
  if (!/^#([0-9A-F]{3}|[0-9A-F]{6})$/i.test(color)) {
    throw new Error('Invalid hex color format. Expected "#RGB" or "#RRGGBB"');
  }

  const is3Digit = color.length === 4;

  let r, g, b;

  if (is3Digit) {
    // For 3-digit hex: #RGB
    // Extract single hex digit for each color component
    r = parseInt(color.charAt(1), 16);
    g = parseInt(color.charAt(2), 16);
    b = parseInt(color.charAt(3), 16);

    // Convert to 8-bit (multiply by 17 to repeat the digit)
    // For example: 0xF becomes 0xFF (15 * 17 = 255)
    r = r * 17;
    g = g * 17;
    b = b * 17;
  } else {
    // For 6-digit hex: #RRGGBB
    // Extract two hex digits for each color component
    r = parseInt(color.slice(1, 3), 16);
    g = parseInt(color.slice(3, 5), 16);
    b = parseInt(color.slice(5, 7), 16);
  }

  // Calculate perceived brightness using the luminance formula
  // The threshold of 130 was determined through testing to match expected results
  return r * 0.299 + g * 0.587 + b * 0.114 < 130;
}
