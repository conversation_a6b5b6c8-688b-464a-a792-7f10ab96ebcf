import {
  WebViewMessageType,
  type ReadFileRequest,
  type ReadFileResponse,
} from "$vscode/src/webview-providers/webview-messages";
import { type AsyncMsgSender } from "$common-webviews/src/common/hosts/async-messaging";
import { type IQualifiedPathName } from "@augment-internal/sidecar-libs/src/workspace/workspace-types";
import { type FileReader } from "../components/code-roll/types";

/**
 * Creates a FileReader that streams file contents via readFileRequest.
 * The extension yields the initial content (from open buffer or disk),
 * and subsequent updates whenever the file changes in VSCode.
 */
export function createAsyncMessageFileReader(asyncMsgSender: AsyncMsgSender): FileReader {
  return async function readFile(qualifiedPathName: IQualifiedPathName): Promise<string> {
    // 1) Open a streaming request to the extension
    //    The extension's generator yields each new content version (initial + subsequent).
    const result = await asyncMsgSender.send<ReadFileRequest, ReadFileResponse>({
      type: WebViewMessageType.readFileRequest,
      data: { pathName: qualifiedPathName },
    });

    if ("error" in result.data) {
      throw new Error(result.data.error);
    }
    return result.data.content;
  };
}
