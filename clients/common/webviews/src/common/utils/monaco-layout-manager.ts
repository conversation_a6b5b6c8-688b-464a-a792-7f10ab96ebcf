import type * as Monaco from "monaco-editor/esm/vs/editor/editor.api";
import throttle from "lodash.throttle";
import type { DebouncedFunc } from "lodash";
import { tick } from "svelte";

export type MonacoEditor = Monaco.editor.IStandaloneCodeEditor | Monaco.editor.IDiffEditor;

/**
 * Interface for objects that can have their height updated during layout operations.
 * This allows the layout manager to coordinate both Monaco layout calls and
 * any associated DOM height updates.
 */
export interface LayoutTarget {
  /** The Monaco editor instance */
  editor: MonacoEditor;
  /** Optional function to update the container height */
  updateHeight?: () => void;
  /** Optional identifier for debugging */
  id?: string;
}

/**
 * Centralized Monaco Layout Manager
 *
 * This singleton coordinates all Monaco editor layout operations to prevent
 * the "thundering herd" problem where multiple editors simultaneously call
 * layout() when the window regains focus, causing visual glitches and performance issues.
 *
 * Uses lodash debounce to batch multiple rapid layout requests into a single operation,
 * with safe error handling for individual editor failures.
 */
export class MonacoLayoutManager {
  private static instance: MonacoLayoutManager | undefined;
  private registeredTargets = new Map<MonacoEditor, LayoutTarget>();
  private throttledPerformLayout: DebouncedFunc<() => Promise<void>>;

  private constructor() {
    // Private constructor for singleton pattern
    // Create debounced version of performLayout, which resizes up to 3 times a second
    this.throttledPerformLayout = throttle(this.performLayout.bind(this), 333, {
      leading: false,
      trailing: true,
    });
  }

  /**
   * Get the singleton instance of the Monaco Layout Manager
   */
  static getInstance(): MonacoLayoutManager {
    if (!MonacoLayoutManager.instance) {
      MonacoLayoutManager.instance = new MonacoLayoutManager();
    }
    return MonacoLayoutManager.instance;
  }

  /**
   * Register a Monaco editor with the layout manager
   *
   * @param target - The layout target containing the editor and optional height update function
   * @returns A cleanup function to unregister the editor
   */
  registerEditor(target: LayoutTarget): () => void {
    this.registeredTargets.set(target.editor, target);

    return () => {
      this.registeredTargets.delete(target.editor);
    };
  }

  /**
   * Request a layout operation for all registered editors
   *
   * This method debounces multiple simultaneous requests to prevent
   * performance issues when many editors need layout updates at once.
   */
  requestLayout(): void {
    // Lodash debounce handles all the coordination - just call it!
    this.throttledPerformLayout();
  }

  /**
   * Perform layout on all registered editors
   *
   * This is called by the debounced function after batching multiple requests.
   */
  private async performLayout(): Promise<void> {
    await tick();

    // Process all registered editors
    for (const [editor, target] of this.registeredTargets) {
      try {
        // First update height if a height update function is provided
        if (target.updateHeight) {
          target.updateHeight();
        }

        // Then perform the Monaco layout
        editor.layout();
      } catch (error) {
        // Log the error but continue with other editors
        console.warn(`Monaco layout failed for editor ${target.id || "unknown"}:`, error);
      }
    }
  }

  /**
   * Get the number of currently registered editors
   * Useful for debugging and monitoring
   */
  getRegisteredEditorCount(): number {
    return this.registeredTargets.size;
  }

  /**
   * Clear all registered editors
   * Useful for testing or cleanup scenarios
   */
  clearAllEditors(): void {
    this.registeredTargets.clear();
    // Cancel any pending debounced layout operations
    this.throttledPerformLayout.cancel();
  }

  /**
   * Check if a specific editor is registered
   */
  isEditorRegistered(editor: MonacoEditor): boolean {
    return this.registeredTargets.has(editor);
  }
}

/**
 * Convenience function to get the Monaco Layout Manager instance
 * This provides a shorter import path for components that need the manager
 */
export function getMonacoLayoutManager(): MonacoLayoutManager {
  return MonacoLayoutManager.getInstance();
}
