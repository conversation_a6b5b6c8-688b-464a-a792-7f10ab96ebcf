import { type WebViewMessageTypes } from "$vscode/src/utils/webviews/types";
import {
  WebViewMessageType,
  type AsyncWebViewMessage,
  type WebViewMessage,
} from "$vscode/src/webview-providers/webview-messages";
import { AsyncMsgSender } from "../hosts/async-messaging";
import type { HostInterface } from "../hosts/host-types";

export class MessageBroker extends AsyncMsgSender {
  private _consumers: Array<MessageConsumer> = [];

  constructor(private readonly _host: HostInterface) {
    super((message: AsyncWebViewMessage<WebViewMessageTypes>) => {
      this._host.postMessage(message);
    });

    this.onMessageFromExtension = this.onMessageFromExtension.bind(this);
  }

  dispose() {
    this._consumers = [];
  }

  postMessage(msg: WebViewMessage) {
    this._host.postMessage(msg);
  }

  registerConsumer(consumer: MessageConsumer) {
    this._consumers.push(consumer);
  }

  onMessageFromExtension(e: MessageEvent<WebViewMessage>) {
    // Async messages will be handled by AsyncMsgSender (which MsgBroker
    // extends) which sets it's own message handler.
    if (e.data.type === WebViewMessageType.asyncWrapper) {
      return;
    }

    this._consumers.forEach((c) => {
      if (c.handleMessageFromExtension(e)) {
        return;
      }
    });
  }
}

export interface MessageConsumer {
  handleMessageFromExtension(e: MessageEvent<WebViewMessage>): boolean;
}
