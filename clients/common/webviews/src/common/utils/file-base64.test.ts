import { describe, test, expect } from "vitest";
import { getBase64FileType, getBase64FileData, base64ToBytes } from "./file-base64";

describe("file-type utils", () => {
  describe("getBase64FileType", () => {
    test("extracts MIME type from valid base64 data URL", () => {
      const base64 =
        "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8z8BQDwAEhQGAhKmMIQAAAABJRU5ErkJggg==";
      expect(getBase64FileType(base64)).toBe("image/png");
    });

    test("extracts MIME type with additional parameters", () => {
      const base64 = "data:application/json;charset=utf-8;base64,eyJuYW1lIjoiSm9obiJ9";
      expect(getBase64FileType(base64)).toBe("application/json");
    });

    test("returns undefined for invalid data URL", () => {
      const invalidBase64 = "invalid-data-url";
      expect(getBase64FileType(invalidBase64)).toBeUndefined();
    });

    test("handles different MIME types", () => {
      const testCases = [
        { input: "data:text/plain;base64,SGVsbG8=", expected: "text/plain" },
        { input: "data:image/jpeg;base64,/9j/4AAQ", expected: "image/jpeg" },
        { input: "data:application/pdf;base64,JVBERi0", expected: "application/pdf" },
      ];

      testCases.forEach(({ input, expected }) => {
        expect(getBase64FileType(input)).toBe(expected);
      });
    });
  });

  describe("getBase64FileData", () => {
    test("extracts base64 data from valid data URL", () => {
      const base64 = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA";
      expect(getBase64FileData(base64)).toBe("iVBORw0KGgoAAAANSUhEUgAA");
    });

    test("returns original string for invalid data URL", () => {
      const invalidBase64 = "invalid-data-url";
      expect(getBase64FileData(invalidBase64)).toBe("invalid-data-url");
    });

    test("handles data URL with multiple semicolons", () => {
      const base64 = "data:application/json;charset=utf-8;base64,eyJuYW1lIjoiSm9obiJ9";
      expect(getBase64FileData(base64)).toBe("eyJuYW1lIjoiSm9obiJ9");
    });

    test("handles empty base64 data", () => {
      const base64 = "data:text/plain;base64,";
      expect(getBase64FileData(base64)).toBe("");
    });
  });

  describe("base64ToBytes", () => {
    test("converts base64 string to Uint8Array", async () => {
      const base64 = "SGVsbG8gV29ybGQh"; // "Hello World!" in base64
      const expected = new Uint8Array([72, 101, 108, 108, 111, 32, 87, 111, 114, 108, 100, 33]);
      expect(await base64ToBytes(base64)).toEqual(expected);
    });
  });
});
