import { describe, it, expect, beforeEach, vi } from "vitest";
import { FocusTrapStack } from "./focusTrapStack";

// Factory function to create HTMLElement instances for testing
// Store the original document.createElement before it gets mocked
const originalCreateElement = document.createElement.bind(document);

function createTestHTMLElement(tagName: string = "div"): HTMLElement {
  return originalCreateElement(tagName);
}

describe("FocusTrapStack", () => {
  let element1: HTMLElement;
  let element2: HTMLElement;
  let element3: HTMLElement;
  let originalDocument: Document;

  beforeEach(() => {
    // Reset mocks
    vi.clearAllMocks();

    // Store original document
    originalDocument = global.document;

    // Create test elements first (before mocking document)
    element1 = createTestHTMLElement("div");
    element2 = createTestHTMLElement("dialog");
    element3 = createTestHTMLElement("section");

    // Create mock document with focus trap stack
    const mockDocument = {
      // eslint-disable-next-line @typescript-eslint/naming-convention
      __focusTrapStack: [] as HTMLElement[],
    };
    global.document = mockDocument as unknown as Document;

    // Clear the stack
    FocusTrapStack.clear();
  });

  afterEach(() => {
    // Restore original document
    global.document = originalDocument;
  });

  it("should start with empty stack", () => {
    expect(FocusTrapStack.isEmpty()).toBe(true);
    expect(FocusTrapStack.size()).toBe(0);
    expect(FocusTrapStack.getActive()).toBeUndefined();
  });

  it("should add elements to stack", () => {
    FocusTrapStack.add(element1);

    expect(FocusTrapStack.size()).toBe(1);
    expect(FocusTrapStack.isEmpty()).toBe(false);
    expect(FocusTrapStack.getActive()).toBe(element1);
    expect(FocusTrapStack.isActive(element1)).toBe(true);
  });

  it("should maintain LIFO order (Last In, First Out)", () => {
    FocusTrapStack.add(element1);
    FocusTrapStack.add(element2);
    FocusTrapStack.add(element3);

    expect(FocusTrapStack.size()).toBe(3);
    expect(FocusTrapStack.getActive()).toBe(element3); // Last added is active
    expect(FocusTrapStack.isActive(element3)).toBe(true);
    expect(FocusTrapStack.isActive(element1)).toBe(false);
    expect(FocusTrapStack.isActive(element2)).toBe(false);

    const all = FocusTrapStack.getAll();
    expect(all).toEqual([element1, element2, element3]);
  });

  it("should remove elements from stack", () => {
    FocusTrapStack.add(element1);
    FocusTrapStack.add(element2);
    FocusTrapStack.add(element3);

    // Remove middle element
    const removed = FocusTrapStack.remove(element2);
    expect(removed).toBe(true);
    expect(FocusTrapStack.size()).toBe(2);
    expect(FocusTrapStack.getActive()).toBe(element3); // Still active

    const all = FocusTrapStack.getAll();
    expect(all).toEqual([element1, element3]);
  });

  it("should handle removing non-existent element", () => {
    FocusTrapStack.add(element1);

    const removed = FocusTrapStack.remove(element2);
    expect(removed).toBe(false);
    expect(FocusTrapStack.size()).toBe(1);
  });

  it("should prevent duplicate elements", () => {
    FocusTrapStack.add(element1);
    FocusTrapStack.add(element1); // Try to add same element again

    expect(FocusTrapStack.size()).toBe(1);
    expect(FocusTrapStack.getActive()).toBe(element1);
  });

  it("should clear all elements", () => {
    FocusTrapStack.add(element1);
    FocusTrapStack.add(element2);
    FocusTrapStack.add(element3);

    expect(FocusTrapStack.size()).toBe(3);

    FocusTrapStack.clear();

    expect(FocusTrapStack.isEmpty()).toBe(true);
    expect(FocusTrapStack.size()).toBe(0);
    expect(FocusTrapStack.getActive()).toBeUndefined();
  });

  it("should handle operations when document is undefined", () => {
    // Temporarily remove document
    const originalDocument = global.document;
    delete (global as unknown as { document?: Document }).document;

    // Should not throw errors
    expect(() => {
      FocusTrapStack.add(element1);
      FocusTrapStack.remove(element1);
      FocusTrapStack.clear();
    }).not.toThrow();

    expect(FocusTrapStack.size()).toBe(0);
    expect(FocusTrapStack.isEmpty()).toBe(true);

    // Restore document
    global.document = originalDocument;
  });

  it("should return copy of stack in getAll()", () => {
    FocusTrapStack.add(element1);
    FocusTrapStack.add(element2);

    const all1 = FocusTrapStack.getAll();
    const all2 = FocusTrapStack.getAll();

    // Should be different array instances
    expect(all1).not.toBe(all2);
    // But with same content
    expect(all1).toEqual(all2);

    // Modifying returned array should not affect internal stack
    all1.push(element3);
    expect(FocusTrapStack.size()).toBe(2);
  });
});
