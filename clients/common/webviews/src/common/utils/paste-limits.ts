/**
 * Options for paste size limit configuration
 */
export interface PasteSizeLimitOptions {
  /** Maximum number of characters allowed in a paste operation (default: 100,000) */
  maxPasteSize?: number;
  /** Callback function triggered when paste size exceeds the limit */
  onExceedsPasteLimit?: (pasteSize: number, maxSize: number) => void;
}

/**
 * Sets up a paste event listener that prevents pasting text that exceeds a specified size limit.
 * Calls the provided callback when a paste is blocked.
 *
 * @param options Configuration options for paste size limits
 * @returns A cleanup function to remove the event listener
 */
export function setupPasteSizeLimit(options: PasteSizeLimitOptions = {}) {
  const MAX_PASTE_SIZE = options.maxPasteSize ?? 100000; // 100k characters default
  const onExceedsPasteLimit = options.onExceedsPasteLimit;

  const pasteHandler = function (e: ClipboardEvent) {
    const clipboardData = e.clipboardData || (window as any).clipboardData;
    if (!clipboardData) return;
    const text = clipboardData.getData("text/plain");

    if (!text || text.length <= MAX_PASTE_SIZE) return;

    // Prevent the default paste
    e.preventDefault();

    // Log warning
    console.warn(
      `Paste operation blocked: Content exceeds ${MAX_PASTE_SIZE} character limit (${text.length} characters)`,
    );

    // Call the callback if provided
    if (onExceedsPasteLimit) {
      onExceedsPasteLimit(text.length, MAX_PASTE_SIZE);
    }
  };

  document.addEventListener("paste", pasteHandler, true);

  // Return a cleanup function
  return () => {
    document.removeEventListener("paste", pasteHandler, true);
  };
}
