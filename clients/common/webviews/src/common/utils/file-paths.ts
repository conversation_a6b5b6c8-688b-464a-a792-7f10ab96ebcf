/**
 * Normalizes file paths to use forward slashes, regardless of platform.
 * This is important for consistent path handling across different platforms.
 *
 * @param filepath The filepath to normalize
 *
 * @returns The normalized filepath.
 */
export function normalizeFilePath(filepath: string): string {
  return filepath.replace(/\\/g, "/");
}

/**
 * Split a filepath into its components.
 *
 * @param filepath The filepath to split where each directory is separated by a "/"
 *
 * @returns The components of the filepath.
 */
export function splitFilePath(filepath: string): string[] {
  return filepath.split("/").filter((part) => part.length > 0);
}

/**
 * Get the filename from a filepath.
 *
 * @param filepath The filepath to get the filename from where each directory is separated by a "/"
 *
 * @returns The filename.
 */
export function getFilename(filepath: string): string {
  return splitFilePath(filepath).at(-1) ?? "";
}

/**
 * Get the directory from a filepath.
 *
 * @param filepath The filepath to get the directory from where each directory is separated by a "/"
 *
 * @returns The directory.
 */
export function getFileDirectory(filepath: string): string {
  return splitFilePath(filepath).slice(0, -1).join("/");
}
