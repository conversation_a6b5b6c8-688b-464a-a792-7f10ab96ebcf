/**
 * Formats a timestamp into a human-readable time string
 * Just time if in the last 24 hours, otherwise date and time
 *
 * @param timestamp - ISO string timestamp or Date object
 * @returns A human-readable time string
 */
export function formatTimestampForChatMessage(timestamp: string | Date | undefined): string {
  if (!timestamp) {
    return "";
  }

  const date = timestamp instanceof Date ? timestamp : new Date(timestamp);
  const now = new Date();
  const diffMs = now.getTime() - date.getTime();
  const seconds = Math.floor(diffMs / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  if (hours < 24) {
    return date.toLocaleTimeString([], {
      hour: "2-digit",
      minute: "2-digit",
    });
  }

  return (
    date.toLocaleDateString([], {
      month: "short",
      day: "numeric",
    }) +
    " " +
    date.toLocaleTimeString([], {
      hour: "2-digit",
      minute: "2-digit",
    })
  );
}

/**
 * Formats a timestamp into a full date and time string
 *
 * @param timestamp - ISO string timestamp or Date object
 * @returns A formatted date and time string
 */
export function formatFullTimestamp(timestamp: string | Date | undefined): string {
  if (!timestamp) {
    return "";
  }

  const date = timestamp instanceof Date ? timestamp : new Date(timestamp);
  return (
    date.toLocaleDateString([], {
      year: "numeric",
      month: "short",
      day: "numeric",
    }) +
    " " +
    date.toLocaleTimeString([], {
      hour: "2-digit",
      minute: "2-digit",
    })
  );
}

/**
 * Returns the difference in days between two dates
 *
 * (date1 - date2)
 *
 * @param date1 - The first date
 * @param date2 - The second date
 * @returns The difference in days
 */
export function differenceInDays(date1: Date, date2: Date): number {
  const diffTime = date1.getTime() - date2.getTime();
  return Math.floor(diffTime / (1000 * 60 * 60 * 24));
}

/**
 * Sets an interval with jitter to prevent thundering herd problems.
 * Works like setInterval but applies random jitter to the interval timing.
 *
 * @param callback - The function to call repeatedly
 * @param baseInterval - The base interval in milliseconds
 * @param jitter - The amount of jitter as a fraction (0-1). Higher values = more variance. Defaults to 0.25 (25% variance)
 * @returns The interval ID that can be used with clearInterval
 */
export function setJitteredInterval(
  callback: () => void,
  baseInterval: number,
  jitter: number = 0.25
): NodeJS.Timeout {
  // Validate jitter is between 0 and 1, default to 0.25 if invalid
  const validJitter = (jitter >= 0 && jitter <= 1) ? jitter : 0.25;

  // Calculate range: (1-jitter) to 1.0 of base interval
  // jitter=0.25 means 75%-100%, jitter=0.5 means 50%-100%, etc.
  const minInterval = baseInterval * (1 - validJitter);
  const maxInterval = baseInterval;
  const jitteredInterval = Math.random() * (maxInterval - minInterval) + minInterval;

  return setInterval(callback, jitteredInterval);
}
