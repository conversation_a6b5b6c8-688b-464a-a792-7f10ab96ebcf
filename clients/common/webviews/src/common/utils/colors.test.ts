import { describe, it, expect } from "vitest";
import { isColorDark } from "./colors";

describe("isColorDark", () => {
  it("should identify dark colors correctly", () => {
    // Dark colors
    expect(isColorDark("#000000")).toBe(true);
    expect(isColorDark("#000")).toBe(true);
    expect(isColorDark("#111")).toBe(true);
    expect(isColorDark("#222222")).toBe(true);
    expect(isColorDark("#003366")).toBe(true);
    expect(isColorDark("#330066")).toBe(true);
  });

  it("should identify light colors correctly", () => {
    // Light colors
    expect(isColorDark("#ffffff")).toBe(false);
    expect(isColorDark("#fff")).toBe(false);
    expect(isColorDark("#eee")).toBe(false);
    expect(isColorDark("#dddddd")).toBe(false);
    expect(isColorDark("#ffcc00")).toBe(false);
    expect(isColorDark("#99ccff")).toBe(false);
  });

  it("should handle 3-digit hex colors", () => {
    console.log("Red (#f00):", isColorDark("#f00"));
    console.log("Green (#0f0):", isColorDark("#0f0"));
    console.log("Blue (#00f):", isColorDark("#00f"));

    expect(isColorDark("#f00")).toBe(true); // Red
    expect(isColorDark("#0f0")).toBe(false); // Green
    expect(isColorDark("#00f")).toBe(true); // Blue
  });

  it("should handle 6-digit hex colors", () => {
    console.log("Red (#ff0000):", isColorDark("#ff0000"));
    console.log("Green (#00ff00):", isColorDark("#00ff00"));
    console.log("Blue (#0000ff):", isColorDark("#0000ff"));

    expect(isColorDark("#ff0000")).toBe(true); // Red
    expect(isColorDark("#00ff00")).toBe(false); // Green
    expect(isColorDark("#0000ff")).toBe(true); // Blue
  });
});
