/**
 * Tests for sound utilities
 *
 * Note: These tests are primarily for API validation since actual audio playback
 * requires user interaction and cannot be easily tested in a headless environment.
 */

import { describe, it, expect, beforeEach, vi } from "vitest";
import agentCompleteSound from "$common-webviews/src/assets/sounds/agent-complete.mp3?url";

// Mock Audio constructor for testing
global.Audio = vi.fn().mockImplementation(() => ({
  play: vi.fn().mockResolvedValue(undefined),
  pause: vi.fn(),
  currentTime: 0,
  volume: 0.5,
  src: "",
  preload: "auto",
}));

// Mock AudioContext for fallback testing
global.AudioContext = vi.fn().mockImplementation(() => ({
  createOscillator: vi.fn().mockReturnValue({
    connect: vi.fn(),
    frequency: {
      setValueAtTime: vi.fn(),
    },
    type: "sine",
    start: vi.fn(),
    stop: vi.fn(),
  }),
  createGain: vi.fn().mockReturnValue({
    connect: vi.fn(),
    gain: {
      setValueAtTime: vi.fn(),
      exponentialRampToValueAtTime: vi.fn(),
    },
  }),
  destination: {},
  currentTime: 0,
}));

describe("Sound Utils", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe("Asset Import", () => {
    it("should import agent complete sound URL", () => {
      expect(agentCompleteSound).toBeDefined();
      expect(typeof agentCompleteSound).toBe("string");
      expect(agentCompleteSound).toMatch(/agent-complete\.mp3/);
    });
  });
});
