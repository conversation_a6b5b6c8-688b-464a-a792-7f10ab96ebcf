export function addEventListener<
  TTarget extends EventTarget | Window | Document,
  TEventName extends string,
  <PERSON><PERSON>vent extends TTarget extends Window
    ? WindowEventMap[TEventName & keyof WindowEventMap]
    : TTarget extends Document
      ? DocumentEventMap[TEventName & keyof DocumentEventMap]
      : Event,
>(element: TTarget, event: TEventName, handler: (e: TEvent) => void) {
  element.addEventListener(event, handler as EventListener);
  return () => {
    element.removeEventListener(event, handler as EventListener);
  };
}

export function collectFns(...fns: Array<() => void>): () => void {
  return function collectedFns() {
    fns.forEach(execute);
  };
}

export function execute(fn: () => void): void {
  fn();
}
