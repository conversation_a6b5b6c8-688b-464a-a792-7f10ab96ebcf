/**
 * Extracts the MIME type from a base64-encoded data URL string.
 * @param base64 - The base64-encoded data URL string (e.g., "data:image/png;base64,...")
 * @returns The MIME type (e.g., "image/png") if found, undefined otherwise
 */
export function getBase64FileType(base64: string): string | undefined {
  const match = base64.match(/data:([a-zA-Z0-9]+\/[a-zA-Z0-9-.+]+).*,.*/);
  if (match) {
    return match[1];
  }
  return undefined;
}

/**
 * Extracts the base64-encoded data from a base64-encoded data URL string.
 * @param base64 - The base64-encoded data URL string (e.g., "data:image/png;base64,...")
 * @returns The base64-encoded data (e.g., "...") if found, the original string otherwise
 */
export function getBase64FileData(base64: string): string {
  return base64.replace(/^data:.*?;base64,/, "");
}

/**
 * Converts a file to a base64-encoded data URL string.
 *
 * @param file - The file to read
 * @returns A base64-encoded data URL string
 */
export async function readFileAsDataURL(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = (event) => resolve(event.target?.result as string);
    reader.onerror = reject;
    reader.readAsDataURL(file);
  });
}

// https://developer.mozilla.org/en-US/docs/Glossary/Base64#the_unicode_problem
function base64ToBytesBlocking(base64: string): Uint8Array {
  const binString = atob(base64);
  return Uint8Array.from(binString, (m) => m.codePointAt(0) || 0);
}

/**
 * Converts base64 string to bytes in a non-blocking way using a Web Worker.
 * Use this for large base64 strings to prevent UI freezing.
 *
 * @param base64 - The base64 string to convert
 * @returns Promise resolving to a Uint8Array
 */
export async function base64ToBytes(base64: string): Promise<Uint8Array> {
  // For small strings, just use the synchronous version
  if (base64.length < 10000) {
    return Promise.resolve(base64ToBytesBlocking(base64));
  }

  return new Promise((resolve, reject) => {
    const worker = new Worker(
      URL.createObjectURL(
        new Blob(
          [
            `
            self.onmessage = function(e) {
              try {
                const base64 = e.data;
                const binString = atob(base64);
                const bytes = new Uint8Array(binString.length);
                for (let i = 0; i < binString.length; i++) {
                  bytes[i] = binString.charCodeAt(i);
                }
                self.postMessage(bytes, [bytes.buffer]);
              } catch (error) {
                self.postMessage({ error: error.message });
              }
            };
            `,
          ],
          { type: "application/javascript" },
        ),
      ),
    );

    worker.onmessage = function (e) {
      if (e.data.error) {
        reject(new Error(e.data.error));
      } else {
        resolve(e.data);
      }
      worker.terminate();
    };

    worker.onerror = function (e) {
      reject(e.error);
      worker.terminate();
    };

    worker.postMessage(base64);
  });
}

function bytesToBase64Blocking(bytes: Uint8Array): string {
  const binString = Array.from(bytes, (byte) => String.fromCodePoint(byte)).join("");
  return btoa(binString);
}

export async function bytesToBase64(bytes: Uint8Array): Promise<string> {
  // For small byte arrays, just use the synchronous version
  if (bytes.length < 10000) {
    return Promise.resolve(bytesToBase64Blocking(bytes));
  }

  return new Promise((resolve, reject) => {
    const worker = new Worker(
      URL.createObjectURL(
        new Blob(
          [
            `
            self.onmessage = function(e) {
              try {
                const bytes = e.data;
                const binString = Array.from(bytes, (byte) => String.fromCodePoint(byte)).join("");
                const base64 = btoa(binString);
                self.postMessage(base64);
              } catch (error) {
                self.postMessage({ error: error.message });
              }
            };
            `,
          ],
          { type: "application/javascript" },
        ),
      ),
    );

    worker.onmessage = function (e) {
      if (e.data.error) {
        reject(new Error(e.data.error));
      } else {
        resolve(e.data);
      }
      worker.terminate();
    };

    worker.onerror = function (e) {
      reject(e.error);
      worker.terminate();
    };

    worker.postMessage(bytes, [bytes.buffer]);
  });
}
