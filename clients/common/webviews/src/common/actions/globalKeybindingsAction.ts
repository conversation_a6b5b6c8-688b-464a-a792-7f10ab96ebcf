import hotkeys from "hotkeys-js";
import { type Action } from "svelte/action";
import {
  type IHotkeysCofiguration,
  type IHotkeysActionParams,
  type HotkeyCallback,
  type HotKeyConfiguration,
  type HotKeyCommandConfiguration,
} from "$common-webviews/src/common/keybindings/types";
import { mapKeybindingConfig } from "../keybindings/multiplatform-keys-config-map";
import { triggerCommand } from "../keybindings/registry";

function setupGlobalHotkey(key: string, callback: HotkeyCallback, getContext: () => any) {
  hotkeys(key, { keydown: true, element: document.body }, (e: KeyboardEvent, handler: any) => {
    return callback(e, handler, getContext());
  });
}

function getKeybindingHandler(
  config: HotKeyConfiguration | HotKeyCommandConfiguration,
): HotkeyCallback | undefined {
  const { handler, commandName } = config;
  if (handler) {
    return handler;
  }
  if (commandName) {
    return (e: KeyboardEvent) => {
      if (triggerCommand(commandName)) {
        e.preventDefault();
      }
    };
  }
  return undefined;
}

function applyConfig(config: IHotkeysCofiguration, getContext: () => any) {
  const mappedConfig = mapKeybindingConfig(config);
  for (const key in mappedConfig) {
    const keyHandler = getKeybindingHandler(mappedConfig[key]);
    if (!keyHandler) {
      continue;
    }
    setupGlobalHotkey(key, keyHandler, getContext);
  }
}

// allow hotkeys in all inputs
hotkeys.filter = () => true;

export const createGlobalKeybindingsAction: Action<HTMLElement, IHotkeysActionParams> = (
  _node: HTMLElement,
  { config, context: getContext }: IHotkeysActionParams,
) => {
  applyConfig(config, getContext);

  return {
    update({ config: newConfig, context: newGetContext }: IHotkeysActionParams) {
      hotkeys.deleteScope("all");
      applyConfig(newConfig, newGetContext);
    },
    destroy() {
      hotkeys.deleteScope("all");
    },
  };
};
