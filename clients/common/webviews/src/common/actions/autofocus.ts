import { tick } from "svelte";

/**
 * Svelte action that automatically focuses an element when a condition becomes true
 *
 * Usage examples:
 * <div use:autofocus={isSelected}>           // Focus when selected
 * <input use:autofocus={isActive}>          // Focus when active
 * <button use:autofocus={shouldHighlight}>  // Focus when highlighted
 *
 * @param element The element to focus
 * @param condition Boolean condition that triggers focus when true
 */
export function autofocus(element: HTMLElement, condition: boolean) {
  let previousCondition = false;

  const update = (newCondition: boolean) => {
    // Only focus if condition changed from false to true
    if (newCondition && !previousCondition) {
      tick().then(() => {
        element.focus();
      });
    }
    previousCondition = newCondition;
  };

  // Initial check
  update(condition);

  return {
    update,
    destroy() {
      // No cleanup needed
    },
  };
}
