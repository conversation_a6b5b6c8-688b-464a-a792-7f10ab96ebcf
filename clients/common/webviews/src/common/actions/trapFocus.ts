import type { Action } from "svelte/action";
import { FocusTrapStack } from "../utils/focusTrapStack.js";

/**
 * Configuration options for the trapFocus action
 */
export interface TrapFocusOptions {
  /** Whether focus trapping is enabled */
  enabled?: boolean;
  /** Element to focus when trap is activated (defaults to first focusable element) */
  initialFocus?: HTMLElement | null;
  /** Whether to restore focus to the previously focused element when the trap deactivates (defaults to true) */
  restoreFocusOnClose?: boolean;
}

/**
 * Gets all focusable elements within a container
 */
function getFocusableElements(container: HTMLElement): HTMLElement[] {
  const elements = [...container.querySelectorAll("*")] as HTMLElement[];
  return elements.filter((element) => element.tabIndex >= 0);
}

/**
 * Svelte action that traps focus within a container element.
 *
 * This action ensures that when Tab or Shift+Tab is pressed, focus cycles
 * only through focusable elements within the container, providing proper
 * accessibility for modal dialogs and other overlay components.
 *
 * The action automatically saves the currently focused element when activated
 * and restores focus to it when deactivated, eliminating the need to manually
 * manage return focus.
 *
 * @param node - The container element to trap focus within
 * @param options - Configuration options for the focus trap
 * @returns Object with update and destroy methods for Svelte action lifecycle
 *
 * @example
 * // Basic usage - enabled by default
 * <div use:trapFocus>
 *   <button>First focusable</button>
 *   <input type="text" />
 *   <button>Last focusable</button>
 * </div>
 *
 * // Conditional usage
 * <div use:trapFocus={{ enabled: showDialog }}>
 *   <!-- Dialog content -->
 * </div>
 */
export const trapFocus: Action<HTMLElement, TrapFocusOptions | undefined> = (
  node: HTMLElement,
  options: TrapFocusOptions = {},
) => {
  let { enabled = true, initialFocus = null, restoreFocusOnClose = true } = options;
  let previouslyFocusedElement: HTMLElement | null = null;
  let lastFocusedElement: HTMLElement | null = null;
  let removeListener: (() => void) | null = null;

  /** Get first and last focusable elements */
  function getFirstAndLastFocusable(): [HTMLElement, HTMLElement] {
    const elements = getFocusableElements(node);
    const first = elements[0] || node;
    const last = elements[elements.length - 1] || node;
    return [first, last];
  }

  /** Activate focus trap */
  function activate() {
    if (!enabled) return;

    // Add this trap to the stack
    FocusTrapStack.add(node);

    // Store the currently focused element to return focus to later
    previouslyFocusedElement = document.activeElement as HTMLElement;

    // Focus the initial element or the first focusable element
    const elementToFocus = initialFocus || getFirstAndLastFocusable()[0];
    if (elementToFocus) {
      elementToFocus.focus();
    }

    // Handle Tab and Shift+Tab key presses to prevent default browser behavior
    const handleKeyDown = (event: KeyboardEvent) => {
      // Only handle Tab key and only if this is the active trap
      if (event.key !== "Tab" || !FocusTrapStack.isActive(node)) {
        return;
      }

      const currentElement = document.activeElement as HTMLElement;

      // Only handle if current focus is within our container
      if (!node.contains(currentElement)) {
        return;
      }

      const [firstFocusable, lastFocusable] = getFirstAndLastFocusable();

      // Handle Shift+Tab (backward)
      if (event.shiftKey) {
        if (currentElement === firstFocusable) {
          event.preventDefault();
          lastFocusable.focus();
        }
      } else {
        // Handle Tab (forward)
        if (currentElement === lastFocusable) {
          event.preventDefault();
          firstFocusable.focus();
        }
      }
    };

    // Handle focus changes as a safety net for other focus movements
    const handleFocusIn = (event: FocusEvent) => {
      // Only handle focus if this is the active trap (top of stack)
      if (!FocusTrapStack.isActive(node)) {
        return;
      }

      const target = event.target as HTMLElement;

      // If focus on container we might return focus to window, so focus last selected element
      if (node === target) {
        lastFocusedElement?.focus();
        return;
      }

      // If focus is already inside the active trap, do nothing
      if (node.contains(target)) {
        lastFocusedElement = target;
        return;
      }

      // Get focusable elements - if there are none, don't interfere
      const focusableElements = getFocusableElements(node);
      if (focusableElements.length === 0) {
        return;
      }

      const [firstFocusable] = getFirstAndLastFocusable();
      const elementToFocus = lastFocusedElement || firstFocusable;

      // Prevent infinite loops by checking if we're trying to focus an element we just focused
      if (target === elementToFocus) {
        return;
      }

      // If the container itself is the only focusable element and it's not actually focusable,
      // don't try to redirect focus
      if (elementToFocus === node && node.tabIndex < 0) {
        return;
      }

      // If something is trying to steal focus from within the body, redirect to first focusable
      if (document.body.contains(target)) {
        lastFocusedElement = elementToFocus;
        elementToFocus.focus();
      }

      // Only redirect focus in very specific cases to avoid interfering with legitimate focus changes
      // This is a safety net for cases like clicking outside a modal or programmatic focus to null
      if (!target || target === document.body) {
        // Focus went to document.body or null - redirect to first focusable element
        lastFocusedElement = elementToFocus;
        elementToFocus.focus();
      }
      // For all other cases, let the focus change happen naturally
    };

    document.addEventListener("keydown", handleKeyDown);
    document.addEventListener("focusin", handleFocusIn);
    removeListener = () => {
      document.removeEventListener("keydown", handleKeyDown);
      document.removeEventListener("focusin", handleFocusIn);
    };
  }

  /** Deactivate focus trap */
  function deactivate() {
    // Remove this trap from the stack
    FocusTrapStack.remove(node);

    if (removeListener) {
      removeListener();
      removeListener = null;
    }

    // Only return focus if configured and there are no other traps active
    if (
      restoreFocusOnClose &&
      FocusTrapStack.isEmpty() &&
      previouslyFocusedElement &&
      typeof previouslyFocusedElement.focus === "function"
    ) {
      previouslyFocusedElement.focus();
    }
  }

  // Activate focus trap immediately if enabled
  if (enabled) {
    activate();
  }

  return {
    update(newOptions: TrapFocusOptions | undefined = {}) {
      const wasEnabled = enabled;

      // Update options
      enabled = newOptions.enabled ?? enabled;
      initialFocus = newOptions.initialFocus ?? initialFocus;
      restoreFocusOnClose = newOptions.restoreFocusOnClose ?? restoreFocusOnClose;

      // Handle enable/disable state changes
      if (!wasEnabled && enabled) {
        activate();
      } else if (wasEnabled && !enabled) {
        deactivate();
      }
    },

    destroy() {
      deactivate();
    },
  };
};
