import { isAugmentURI } from "../utils/augment-uri";
import { WebViewMessageType } from "$vscode/src/webview-providers/webview-messages";
import { host } from "$common-webviews/src/common/hosts/host";

function targetIsELement(target: EventTarget): target is HTMLElement {
  return target && target instanceof HTMLAnchorElement
}

function getTargetLink(target: EventTarget | null): HTMLAnchorElement | null {
  if (!target || !targetIsELement(target)) {
    return null;
  }
  return target.closest('a');
}
/*
  We use `augment://` as internal links in our app, this action
  catches any anchor element with such link and reroute to the 404 page,
  this is a bit 
*/
export function catchUnhandledAugmentLinks(node: HTMLElement) {
  function handleClick(event: MouseEvent) {
    const link = getTargetLink(event.target);
    if (!link) {
      return;
    }
    const href = link.getAttribute('href');

    if (href && isAugmentURI(href)) {
      const message = `Unhandled augment link: "${href}"`;
      const error = new Error(message);
      
      host.postMessage({
        type: WebViewMessageType.reportError,
        data: {
          originalRequestId: null,
          sanitizedMessage: error.message,
          stackTrace: error.stack || "",
          diagnostics: [],
        },
      });

      host.postMessage({
        type: WebViewMessageType.showNotification,
        data: {
          message: message,
          type: "error",
        }
      });

      event.stopImmediatePropagation();
      event.preventDefault();
      event.stopPropagation();
      
      return false;
    }
  }

  node.addEventListener('click', handleClick);

  return {
    destroy() {
      node.removeEventListener('click', handleClick);
    }
  };
}
