import debounce from "lodash.debounce";
import { getContext, setContext } from "svelte";
import { type Writable, writable } from "svelte/store";

/**
 * Manages hover behavior with debounced start and end actions.
 */
interface OnHoverParameters {
  /** Delay before triggering hover start (default: 160ms) */
  hoverTriggerDuration?: number;
  /** Called when hover starts */
  onHoverStart: () => void;
  /** Called when hover ends */
  onHoverEnd: () => void;
}

/**
 * Handles hover logic with debounced actions.
 *
 * This class manages mouse interactions and triggers
 * debounced hover start and end callbacks. It provides
 * methods to handle mouse enter, leave, and move events,
 * as well as cancellation of hover actions.
 *
 * The class uses debounce to prevent rapid triggering
 * of hover actions during quick mouse movements, improving
 * performance and user experience.
 *
 * Expected behavior examples:
 * 1. User moves mouse over element quickly: No hover action triggered.
 * 2. User moves mouse over element and stays: Hover start triggered after delay.
 * 3. User moves mouse into and away from element: Hover started but cancelled.
 * 4. User moves mouse within element: Hover start reset, a second hover triggers.
 *
 * @example
 * const hoverContext = new HoverContext({
 *   onHoverStart: () => console.log('Hover started'),
 *   onHoverEnd: () => console.log('Hover ended'),
 *   hoverTriggerDuration: 200
 * });
 */
export class HoverContext {
  // Default debounce time for hover end. 2 frames @ 30FPS
  // eslint-disable-next-line @typescript-eslint/naming-convention
  private static DEFAULT_HOVER_END_DEBOUNCE_MS = 67;
  private debouncedHoverStart: ReturnType<typeof debounce> | undefined;
  private debouncedHoverEnd: ReturnType<typeof debounce> | undefined;

  constructor(params: OnHoverParameters) {
    this.debouncedHoverStart = debounce(params.onHoverStart, params.hoverTriggerDuration);
    this.debouncedHoverEnd = debounce(
      params.onHoverEnd,
      HoverContext.DEFAULT_HOVER_END_DEBOUNCE_MS,
    );
  }

  /** Cancels hover end and starts hover */
  handleMouseEnter = () => {
    this.debouncedHoverEnd?.cancel();
    this.debouncedHoverStart?.();
  };

  /** Cancels hover start and ends hover */
  handleMouseLeave = () => {
    this.debouncedHoverStart?.cancel();
    this.debouncedHoverEnd?.();
  };

  /** Resets hover start on mouse move */
  handleMouseMove = () => {
    this.debouncedHoverEnd?.cancel();
    this.debouncedHoverStart?.();
  };

  /** Cancels both hover start and end */
  cancelHovers = () => {
    this.debouncedHoverStart?.cancel();
    this.debouncedHoverEnd?.cancel();
  };

  /** Cleans up debounced actions */
  destroy() {
    this.debouncedHoverStart?.cancel();
    this.debouncedHoverEnd?.cancel();
  }
}

/**
 * Svelte action for adding hover functionality to an element.
 *
 * This action uses HoverContext to manage hover state based on mouse interactions.
 * It prevents rapid triggering during quick mouse movements using debounce.
 *
 * @param node - Target HTML element
 * @param hoverContext - HoverContext instance for managing hover behavior
 * @returns Object with destroy method for cleanup
 *
 * @example
 * <div use:onHover={new HoverContext({
 *   onHoverStart: () => console.log('Hover started'),
 *   onHoverEnd: () => console.log('Hover ended'),
 *   hoverTriggerDuration: 200
 * })}>
 *   Hover me
 * </div>
 */
export function onHover(node: HTMLElement, hoverContext: HoverContext) {
  node.addEventListener("mouseenter", hoverContext.handleMouseEnter);
  node.addEventListener("mouseleave", hoverContext.handleMouseLeave);
  node.addEventListener("mousemove", hoverContext.handleMouseMove);

  return {
    destroy() {
      node.removeEventListener("mouseenter", hoverContext.handleMouseEnter);
      node.removeEventListener("mouseleave", hoverContext.handleMouseLeave);
      node.removeEventListener("mousemove", hoverContext.handleMouseMove);
    },
  };
}

//Private symbol for the hover context.
const HOVER_SYMBOL = Symbol("hover-action-context");
/**
 *  Retrieves the hover context.
 *
 * This function retrieves the hover context that was set up by the `createHoverAction`
 * function. It throws an error if the hover context has not been set up.
 *
 * The hover context is a writable store that represents the hover state.
 *
 * @throws {Error} If the hover context has not been set up.
 * @returns A writable store that represents the hover state.
 */
export function getHoverContext(): Writable<boolean> {
  const ctx = getContext<Writable<boolean>>(HOVER_SYMBOL);
  if (!ctx) {
    throw new Error("Hover context not found, call createHoverAction first");
  }
  return ctx;
}
/**
 * Creates a hover action and sets up the context.
 *
 * This function creates a writable store for the hover state
 * and sets it up as a context. It also creates a HoverContext
 * instance and returns a function that can be used as a Svelte
 * action.
 *
 * The returned function can be used as a Svelte action to add
 * hover functionality to an element.
 *
 * The hover state can be accessed using the `getHoverContext`
 * function.
 *
 * @param hoverTriggerDuration
 * @returns
 */
export function createHoverAction(hoverTriggerDuration = 100) {
  const isHover: Writable<boolean> = writable(false);
  setContext(HOVER_SYMBOL, isHover);
  const ctx = new HoverContext({
    onHoverStart() {
      isHover.set(true);
    },
    onHoverEnd() {
      isHover.set(false);
    },
    hoverTriggerDuration,
  });

  return function onHover$inner(node: HTMLElement) {
    return onHover(node, ctx);
  };
}
