import { describe, it, expect, beforeEach, afterEach, vi } from "vitest";
import { trapFocus } from "./trapFocus";
import { FocusTrapStack } from "../utils/focusTrapStack";

// Mock DOM methods
const mockAddEventListener = vi.fn();
const mockRemoveEventListener = vi.fn();

// Mock window.getComputedStyle
const mockGetComputedStyle = vi.fn();

// Mock FocusTrapStack
vi.mock("../utils/focusTrapStack", () => ({
  // eslint-disable-next-line @typescript-eslint/naming-convention
  FocusTrapStack: {
    add: vi.fn(),
    remove: vi.fn(),
    isActive: vi.fn(() => true), // Default to active for tests
    isEmpty: vi.fn(() => false),
    clear: vi.fn(),
  },
}));

// Factory function to create HTMLElement instances with mocked methods
// We need to store the original document.createElement before it gets mocked
const originalCreateElement = document.createElement.bind(document);

function createMockHTMLElement(tagName: string = "div"): HTMLElement {
  const element = originalCreateElement(tagName);

  // Mock the methods we need for testing
  vi.spyOn(element, "focus").mockImplementation(() => {});
  vi.spyOn(element, "contains").mockReturnValue(true);
  vi.spyOn(element, "getBoundingClientRect").mockReturnValue({
    width: 100,
    height: 30,
    top: 0,
    left: 0,
    bottom: 30,
    right: 100,
    x: 0,
    y: 0,
    toJSON: () => ({}),
  } as DOMRect);
  vi.spyOn(element, "querySelectorAll").mockReturnValue([] as unknown as NodeListOf<Element>);

  return element;
}

describe("trapFocus", () => {
  let container: HTMLElement;
  let button1: HTMLElement;
  let button2: HTMLElement;
  let input: HTMLElement;
  let originalDocument: Document;
  let originalWindow: Window & typeof globalThis;

  beforeEach(() => {
    vi.clearAllMocks();

    // Clear the focus trap stack
    vi.mocked(FocusTrapStack.clear).mockClear();
    vi.mocked(FocusTrapStack.isActive).mockReturnValue(true);

    // Store originals
    originalDocument = global.document;
    originalWindow = global.window;

    // Create real DOM elements first (before mocking document)
    container = createMockHTMLElement("div");
    button1 = createMockHTMLElement("button");
    button2 = createMockHTMLElement("button");
    input = createMockHTMLElement("input");

    // Setup DOM mocks
    const mockDocument = {
      activeElement: null,
      addEventListener: mockAddEventListener,
      removeEventListener: mockRemoveEventListener,
      dispatchEvent: vi.fn(),
    };
    global.document = mockDocument as unknown as Document;
    global.window = { getComputedStyle: mockGetComputedStyle } as unknown as Window &
      typeof globalThis;

    mockGetComputedStyle.mockReturnValue({
      display: "block",
      visibility: "visible",
    });

    // Setup querySelectorAll to return focusable elements
    vi.mocked(container.querySelectorAll).mockReturnValue([
      button1,
      input,
      button2,
    ] as unknown as NodeListOf<Element>);

    // Setup container.contains to return true for our test elements by default
    vi.mocked(container.contains).mockImplementation((element) => {
      return element === button1 || element === button2 || element === input;
    });
  });

  afterEach(() => {
    // Restore originals
    global.document = originalDocument;
    global.window = originalWindow;
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it("should add keydown and focusin event listeners when enabled", async () => {
    const action = trapFocus(container, { enabled: true });

    // Should add event listeners after setTimeout
    await new Promise((resolve) => setTimeout(resolve, 1));
    expect(mockAddEventListener).toHaveBeenCalledWith("keydown", expect.any(Function));
    expect(mockAddEventListener).toHaveBeenCalledWith("focusin", expect.any(Function));

    action?.destroy?.();
  });

  it("should focus first element on activation", async () => {
    trapFocus(container, { enabled: true });

    await new Promise((resolve) => setTimeout(resolve, 1));
    expect(vi.mocked(button1.focus)).toHaveBeenCalled();
  });

  it("should focus specified initial element", async () => {
    trapFocus(container, {
      enabled: true,
      initialFocus: input,
    });

    await new Promise((resolve) => setTimeout(resolve, 1));
    expect(vi.mocked(input.focus)).toHaveBeenCalled();
  });

  it("should remove event listeners on destroy", async () => {
    const action = trapFocus(container, { enabled: true })!;

    // Wait for initial activation
    await new Promise((resolve) => setTimeout(resolve, 1));

    action.destroy?.();

    expect(mockRemoveEventListener).toHaveBeenCalledWith("keydown", expect.any(Function));
    expect(mockRemoveEventListener).toHaveBeenCalledWith("focusin", expect.any(Function));
  });

  it("should handle update to disable focus trap", async () => {
    const action = trapFocus(container, { enabled: true })!;

    // Wait for initial activation
    await new Promise((resolve) => setTimeout(resolve, 1));

    action.update?.({ enabled: false });

    expect(mockRemoveEventListener).toHaveBeenCalledWith("keydown", expect.any(Function));
    expect(mockRemoveEventListener).toHaveBeenCalledWith("focusin", expect.any(Function));
  });

  it("should handle update to enable focus trap", async () => {
    const action = trapFocus(container, { enabled: false })!;

    action.update?.({ enabled: true });

    await new Promise((resolve) => setTimeout(resolve, 1));
    expect(mockAddEventListener).toHaveBeenCalledWith("keydown", expect.any(Function));
    expect(mockAddEventListener).toHaveBeenCalledWith("focusin", expect.any(Function));
  });
  it("should restore focus to previously focused element on destroy by default", async () => {
    const previouslyFocused = createMockHTMLElement("button");
    // Set active element BEFORE activation to capture as previouslyFocusedElement
    Object.defineProperty(document, "activeElement", {
      value: previouslyFocused,
      writable: true,
      configurable: true,
    });

    const action = trapFocus(container, { enabled: true })!;
    await new Promise((resolve) => setTimeout(resolve, 1));

    // Destroy should restore focus to previously focused element
    vi.mocked(FocusTrapStack.isEmpty).mockReturnValue(true);
    action.destroy?.();
    expect(vi.mocked(previouslyFocused.focus)).toHaveBeenCalled();
  });

  it("should NOT restore focus when restoreFocusOnClose is false", async () => {
    const previouslyFocused = createMockHTMLElement("button");
    Object.defineProperty(document, "activeElement", {
      value: previouslyFocused,
      writable: true,
      configurable: true,
    });

    const action = trapFocus(container, { enabled: true, restoreFocusOnClose: false })!;
    await new Promise((resolve) => setTimeout(resolve, 1));

    vi.mocked(FocusTrapStack.isEmpty).mockReturnValue(true);
    action.destroy?.();
    expect(vi.mocked(previouslyFocused.focus)).not.toHaveBeenCalled();
  });

  it("should filter out hidden elements", async () => {
    const hiddenButton = createMockHTMLElement("button");
    vi.mocked(hiddenButton.getBoundingClientRect).mockReturnValue({
      width: 0,
      height: 0,
      top: 0,
      left: 0,
      bottom: 0,
      right: 0,
      x: 0,
      y: 0,
      toJSON: () => ({}),
    } as DOMRect);

    vi.mocked(container.querySelectorAll).mockReturnValue([
      button1,
      hiddenButton,
      button2,
    ] as unknown as NodeListOf<Element>);

    // The focus trap should only consider visible elements
    trapFocus(container, { enabled: true });

    // Wait for the setTimeout to execute
    await new Promise((resolve) => setTimeout(resolve, 1));
    expect(vi.mocked(container.querySelectorAll)).toHaveBeenCalled();
  });

  it("should handle elements with display: none", async () => {
    mockGetComputedStyle.mockImplementation((element) => {
      if (element === button2) {
        return { display: "none", visibility: "visible" };
      }
      return { display: "block", visibility: "visible" };
    });

    vi.mocked(container.querySelectorAll).mockReturnValue([
      button1,
      button2,
      input,
    ] as unknown as NodeListOf<Element>);

    trapFocus(container, { enabled: true });

    // Should still work with visible elements
    await new Promise((resolve) => setTimeout(resolve, 1));
    expect(vi.mocked(button1.focus)).toHaveBeenCalled();
  });

  it("should handle Tab key to cycle focus forward", async () => {
    let keydownHandler: ((event: KeyboardEvent) => void) | undefined;

    // Capture the keydown handler when it's added
    mockAddEventListener.mockReset();
    mockAddEventListener.mockImplementation((event: string, handler: EventListener) => {
      if (event === "keydown") {
        keydownHandler = handler as (event: KeyboardEvent) => void;
      }
    });

    const action = trapFocus(container, { enabled: true });
    await new Promise((resolve) => setTimeout(resolve, 1));

    // Mock document.activeElement to be the last button (button2)
    Object.defineProperty(document, "activeElement", {
      value: button2,
      writable: true,
      configurable: true,
    });

    // Ensure container.contains returns true for button2
    vi.mocked(container.contains).mockImplementation((element) => {
      return element === button1 || element === button2 || element === input;
    });

    // Simulate Tab key press on last element
    const tabEvent = new KeyboardEvent("keydown", { key: "Tab", shiftKey: false });
    const preventDefaultSpy = vi.spyOn(tabEvent, "preventDefault");

    // Call the handler directly
    if (keydownHandler) {
      keydownHandler(tabEvent);
    }

    // Should prevent default and focus first element
    expect(preventDefaultSpy).toHaveBeenCalled();
    expect(vi.mocked(button1.focus)).toHaveBeenCalled();

    action?.destroy?.();
  });

  it("should handle Shift+Tab key to cycle focus backward", async () => {
    let keydownHandler: ((event: KeyboardEvent) => void) | undefined;

    // Capture the keydown handler when it's added
    mockAddEventListener.mockReset();
    mockAddEventListener.mockImplementation((event: string, handler: EventListener) => {
      if (event === "keydown") {
        keydownHandler = handler as (event: KeyboardEvent) => void;
      }
    });

    const action = trapFocus(container, { enabled: true });
    await new Promise((resolve) => setTimeout(resolve, 1));

    // Mock document.activeElement to be the first button (button1)
    Object.defineProperty(document, "activeElement", {
      value: button1,
      writable: true,
      configurable: true,
    });

    // Ensure container.contains returns true for button1
    vi.mocked(container.contains).mockImplementation((element) => {
      return element === button1 || element === button2 || element === input;
    });

    // Simulate Shift+Tab key press on first element
    const shiftTabEvent = new KeyboardEvent("keydown", { key: "Tab", shiftKey: true });
    const preventDefaultSpy = vi.spyOn(shiftTabEvent, "preventDefault");

    // Call the handler directly
    if (keydownHandler) {
      keydownHandler(shiftTabEvent);
    }

    // Should prevent default and focus last element
    expect(preventDefaultSpy).toHaveBeenCalled();
    expect(vi.mocked(button2.focus)).toHaveBeenCalled();

    action?.destroy?.();
  });

  it("should handle focus escaping via focusin event (safety net)", async () => {
    let focusinHandler: ((event: FocusEvent) => void) | undefined;

    // Capture the focusin handler when it's added
    mockAddEventListener.mockReset();
    mockAddEventListener.mockImplementation(
      (event: string, handler: EventListener, capture?: boolean) => {
        if (event === "focusin" && capture === true) {
          focusinHandler = handler as (event: FocusEvent) => void;
        }
      },
    );

    const action = trapFocus(container, { enabled: true });
    await new Promise((resolve) => setTimeout(resolve, 1));

    // Create an element outside the container to simulate focus escaping
    const outsideElement = createMockHTMLElement("button");
    // Ensure container.contains returns false for outsideElement
    vi.mocked(container.contains).mockImplementation((element) => {
      return element === button1 || element === button2 || element === input;
    });

    // Simulate focusin event where focus moved outside (e.g., programmatic focus change)
    const focusinEvent = new FocusEvent("focusin", {
      relatedTarget: button2,
    });
    // Mock the target property
    Object.defineProperty(focusinEvent, "target", {
      value: outsideElement,
      writable: false,
    });

    // Call the handler directly
    if (focusinHandler) {
      focusinHandler(focusinEvent);
    }

    // Should focus first element as safety net
    expect(vi.mocked(button1.focus)).toHaveBeenCalled();

    action?.destroy?.();
  });

  it("should handle nested focus traps (stack behavior)", async () => {
    // Create outer trap
    const outerTrap = trapFocus(container, { enabled: true });
    await new Promise((resolve) => setTimeout(resolve, 1));

    // Create inner trap (should become active and be on top of stack)
    const innerContainer = createMockHTMLElement("div");
    const innerButton = createMockHTMLElement("button");

    // Setup inner container with focusable elements
    vi.mocked(innerContainer.querySelectorAll).mockReturnValue([
      innerButton,
    ] as unknown as NodeListOf<Element>);

    const innerTrap = trapFocus(innerContainer, { enabled: true });
    await new Promise((resolve) => setTimeout(resolve, 1));

    // Both traps should exist and have listeners
    expect(outerTrap).toBeDefined();
    expect(innerTrap).toBeDefined();

    // Clean up in reverse order (LIFO - Last In, First Out)
    if (innerTrap) innerTrap.destroy?.();
    if (outerTrap) outerTrap.destroy?.();

    // Test passes if no errors are thrown during cleanup
    expect(true).toBe(true);
  });
});
