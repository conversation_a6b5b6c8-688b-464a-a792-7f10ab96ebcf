import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import { startMonitoringPerformance } from "../core";

/* eslint-disable @typescript-eslint/naming-convention */
// eslint doesn't like some of the property names in the mocks hence disabling

// Mock Sentry
vi.mock("@sentry/svelte", () => ({
  startInactiveSpan: vi.fn(() => ({
    setStatus: vi.fn(),
    end: vi.fn(),
  })),
}));

describe("Performance Monitoring with Sentry", () => {
  let mockPerformanceObserverCallback: any;
  let rafCallCount = 0;

  beforeEach(() => {
    // Use fake timers for deterministic testing - this is the Vitest best practice
    vi.useFakeTimers();

    // Clear mocks
    vi.clearAllMocks();
    rafCallCount = 0;

    // Mock window.augmentPerformance
    window.augmentPerformance = {
      initialized: false,
    };

    // Mock requestAnimationFrame with controlled recursion to prevent infinite loops
    // This approach is better than using real setTimeout delays
    vi.spyOn(window, "requestAnimationFrame").mockImplementation((cb) => {
      // Only execute the callback for the first few calls to prevent infinite recursion
      if (rafCallCount < 3) {
        rafCallCount++;
        // Use setTimeout with fake timers for controlled async behavior
        setTimeout(() => cb(performance.now() + 1000), 0);
      }
      return rafCallCount;
    });

    // Mock PerformanceObserver
    // @ts-expect-error - Mocking global
    global.PerformanceObserver = vi.fn().mockImplementation((callback) => {
      mockPerformanceObserverCallback = callback;
      return {
        observe: vi.fn(),
      };
    });
    // @ts-expect-error - Mocking global
    PerformanceObserver.supportedEntryTypes = ["event"];
  });

  afterEach(() => {
    // Restore real timers and mocks
    vi.useRealTimers();
    vi.restoreAllMocks();
  });

  it("should create slow framerate span in Sentry", async () => {
    // Import Sentry to access the mocked functions
    const Sentry = await import("@sentry/svelte");

    // Arrange
    const options = {
      lowFramerateThreshold: 30,
      slowInpThreshold: 200,
    };

    // Act
    startMonitoringPerformance(options);

    // Get the callback function passed to requestAnimationFrame
    const rafSpy = vi.mocked(window.requestAnimationFrame);
    expect(rafSpy).toHaveBeenCalled();
    const framerateMeasurementFn = rafSpy.mock.calls[0][0];

    // Simulate multiple frames to trigger framerate calculation
    framerateMeasurementFn(0);
    await vi.advanceTimersByTimeAsync(10);
    framerateMeasurementFn(1000);
    await vi.advanceTimersByTimeAsync(10);

    // Assert
    expect(Sentry.startInactiveSpan).toHaveBeenCalledWith({
      name: "slow_framerate",
      op: "performance.monitoring",
      attributes: expect.objectContaining({
        "performance.fps": expect.any(Number),
        "performance.threshold": 30,
      }),
    });
  });

  it("should create slow INP span in Sentry", async () => {
    // Import Sentry to access the mocked functions
    const Sentry = await import("@sentry/svelte");

    // Arrange
    const options = {
      lowFramerateThreshold: 30,
      slowInpThreshold: 200,
    };

    // Act
    startMonitoringPerformance(options);

    // Verify PerformanceObserver was created
    const observerConstructor = vi.mocked(PerformanceObserver);
    expect(observerConstructor).toHaveBeenCalled();
    expect(mockPerformanceObserverCallback).toBeDefined();

    // Create mock entry list with slow INP data
    const mockEntryList = {
      getEntries: vi.fn().mockReturnValue([
        {
          interactionId: "test-interaction",
          duration: 300,
          target: "button.submit",
          startTime: 100,
        },
      ]),
    };

    // Call the callback with slow INP data
    mockPerformanceObserverCallback(mockEntryList);

    // Assert
    expect(Sentry.startInactiveSpan).toHaveBeenCalledWith({
      name: "slow_inp",
      op: "performance.monitoring",
      attributes: expect.objectContaining({
        "performance.inp": expect.any(Number),
        "performance.threshold": 200,
      }),
    });
  });
});
