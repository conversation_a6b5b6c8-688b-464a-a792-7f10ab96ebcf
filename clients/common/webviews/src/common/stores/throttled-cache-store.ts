import { writable, type Readable } from "svelte/store";

// Default cache settings
export const DEFAULT_CACHE_THROTTLE_MS = 1000; // 1 second throttle

/**
 * Interface for a throttled cached store
 */
export interface IThrottledCachedStore<T> extends Readable<T> {
  /**
   * Reset the cache
   */
  resetCache(): void;

  /**
   * Force update the store value
   */
  updateStore(): void;
}

/**
 * Creates a generic store with throttled caching
 *
 * @param calculate Function that calculates the value to be stored
 * @param initialValue Initial value for the store
 * @param throttleMs Throttle time in milliseconds
 * @returns A store that provides the cached value with throttling
 */
export function createThrottledCachedStore<T>(
  calculate: () => T,
  initialValue: T,
  throttleMs: number = DEFAULT_CACHE_THROTTLE_MS,
): IThrottledCachedStore<T> {
  // Internal state for caching
  let cache: T | null = null;
  let cacheTimestamp = 0;

  // Create a writable store
  const store = writable<T>(initialValue);

  // Function to calculate the value with caching
  const calculateWithCache = (): T => {
    const now = Date.now();

    // Return cached value if within throttle interval
    if (cache !== null && now - cacheTimestamp < throttleMs) {
      return cache;
    }

    // Calculate new value
    const newValue = calculate();

    // Update cache
    cache = newValue;
    cacheTimestamp = now;

    return newValue;
  };

  // Function to update the store value
  const updateStore = () => {
    const calculatedValue = calculateWithCache();
    store.set(calculatedValue);
  };

  // Reset cache function
  const resetCache = () => {
    cache = null;
    updateStore();
  };

  // Create the readable store with the reset and update functions
  return {
    subscribe: store.subscribe,
    resetCache,
    updateStore,
  };
}
