import { describe, it, expect, vi, beforeEach } from "vitest";
import { createThrottledCachedStore, DEFAULT_CACHE_THROTTLE_MS } from "./throttled-cache-store";
import { get } from "svelte/store";

describe("createThrottledCachedStore", () => {
  beforeEach(() => {
    vi.useFakeTimers();
  });

  it("should create a store with the initial value", () => {
    const calculate = vi.fn().mockReturnValue(42);
    const store = createThrottledCachedStore(calculate, 0);

    expect(get(store)).toBe(0); // Initial value
    expect(calculate).not.toHaveBeenCalled(); // Calculation not called yet
  });

  it("should calculate the value when updateStore is called", () => {
    const calculate = vi.fn().mockReturnValue(42);
    const store = createThrottledCachedStore(calculate, 0);

    store.updateStore();

    expect(get(store)).toBe(42);
    expect(calculate).toHaveBeenCalledTimes(1);
  });

  it("should use cached value within throttle interval", () => {
    const calculate = vi.fn().mockReturnValue(42);
    const store = createThrottledCachedStore(calculate, 0);

    store.updateStore();
    expect(get(store)).toBe(42);
    expect(calculate).toHaveBeenCalledTimes(1);

    // Update again within throttle interval
    calculate.mockReturnValue(100); // This should not be used due to throttling
    store.updateStore();

    expect(get(store)).toBe(42); // Still the cached value
    expect(calculate).toHaveBeenCalledTimes(1); // Not called again
  });

  it("should recalculate after throttle interval", () => {
    const calculate = vi.fn().mockReturnValue(42);
    const store = createThrottledCachedStore(calculate, 0);

    store.updateStore();
    expect(get(store)).toBe(42);

    // Advance time past throttle interval
    vi.advanceTimersByTime(DEFAULT_CACHE_THROTTLE_MS + 10);

    // Update with new value
    calculate.mockReturnValue(100);
    store.updateStore();

    expect(get(store)).toBe(100); // New value
    expect(calculate).toHaveBeenCalledTimes(2); // Called again
  });

  it("should reset cache when resetCache is called", () => {
    const calculate = vi.fn().mockReturnValue(42);
    const store = createThrottledCachedStore(calculate, 0);

    store.updateStore();
    expect(get(store)).toBe(42);
    expect(calculate).toHaveBeenCalledTimes(1);

    // Reset cache and update again
    calculate.mockReturnValue(100);
    store.resetCache();

    expect(get(store)).toBe(100); // New value after reset
    expect(calculate).toHaveBeenCalledTimes(2); // Called again after reset
  });

  it("should work with different types", () => {
    // Test with string type
    const calculateString = vi.fn().mockReturnValue("hello");
    const stringStore = createThrottledCachedStore(calculateString, "");

    stringStore.updateStore();
    expect(get(stringStore)).toBe("hello");

    // Test with object type
    const testObj = { name: "test", value: 123 };
    const calculateObject = vi.fn().mockReturnValue(testObj);
    const objectStore = createThrottledCachedStore(calculateObject, {});

    objectStore.updateStore();
    expect(get(objectStore)).toEqual(testObj);
  });

  it("should use custom throttle time", () => {
    const customThrottleMs = 2000;
    const calculate = vi.fn().mockReturnValue(42);
    const store = createThrottledCachedStore(calculate, 0, customThrottleMs);

    store.updateStore();
    expect(get(store)).toBe(42);

    // Advance time past default throttle but before custom throttle
    vi.advanceTimersByTime(DEFAULT_CACHE_THROTTLE_MS + 10);

    // Update should still use cached value
    calculate.mockReturnValue(100);
    store.updateStore();
    expect(get(store)).toBe(42); // Still cached
    expect(calculate).toHaveBeenCalledTimes(1);

    // Advance time past custom throttle
    vi.advanceTimersByTime(customThrottleMs);

    // Now it should recalculate
    store.updateStore();
    expect(get(store)).toBe(100);
    expect(calculate).toHaveBeenCalledTimes(2);
  });
});
