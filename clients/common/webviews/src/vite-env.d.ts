/// <reference types="svelte" />
/// <reference types="vite/client" />
declare module "*.json5" {
  export default unknown;
}
declare module "*.svg?component" {
  export default ComponentType<SvelteComponent>;
}
declare module "*.mp3?url" {
  const src: string;
  export default src;
}
declare module "*.mdx" {
  const MDXComponent: any;
  export default MDXComponent;
}

// Add type definitions for Vite's import.meta.glob feature
interface ImportMeta {
  glob: (pattern: string, options?: {
    eager?: boolean;
    as?: string;
    import?: string;
    query?: string | Record<string, string>;
  }) => Record<string, any>;
}
