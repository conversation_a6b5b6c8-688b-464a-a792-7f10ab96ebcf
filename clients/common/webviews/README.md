# Augment WebViews

These webviews are intended to be shared between clients (Web,VSCode and IntelliJ). We are attempting to limit dependencies
back to vscode or intellij from these components. In addition within the components we expect the 'apps' to depend on components
but not the other way around. App to App depenencies should also not exist.

# Storybook

All components are expected to have a storybook documenting there usage and visuals. This should help with discoverability and
testing.

# Running storybook

```
pnpm run dev
```

# Chromatic

Chromatic is used to review changes to the storybook. It is run on every PR and on every commit to main. It will comment on the PR with a link to the changes.
