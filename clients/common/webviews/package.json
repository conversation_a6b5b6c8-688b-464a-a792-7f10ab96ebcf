{"name": "augment-webviews", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "pnpm build-and-copy-protos && storybook dev -p 6006", "dev:vite-hmr-vscode": ". ../../vscode/.augment-hmr-env && vite --port ${AUGMENT_HMR_PORT}", "build:vscode": "vite build --emptyOutDir --outDir ../../vscode/common-webviews/", "build:intellij": "vite build --emptyOutDir --outDir ../../intellij/src/main/resources/webviews/", "preview": "vite preview", "check": "svelte-check --workspace . --tsconfig ./tsconfig.json --fail-on-warnings", "test": "pnpm run lint:fix && pnpm run vitest && pnpm run check", "vitest": "vitest run", "vitest:watch": "vitest", "vitest:coverage": "vitest run --coverage", "lint": "npm run eslint && npm run prettier", "lint:fix": "npm run eslint:fix && npm run prettier:fix", "eslint": "git ls-files -- . | xargs pre-commit run eslint --files", "eslint:fix": "git ls-files -- . | xargs pre-commit run eslint --hook-stage=manual --files", "prettier": "git ls-files -- . | xargs pre-commit run prettier --hook-stage=manual --files", "prettier:fix": "git ls-files -- . | xargs pre-commit run prettier --files", "watch:vscode": "pnpm build:vscode --watch", "watch:intellij": "pnpm build:intellij --watch", "extension-generate": "tsx ./src/common/components/language-icons/extensions-generate.ts src/common/components/language-icons/extensions.json", "build-and-copy-protos": "bazel run //clients/sidecar/libs/protos:echo_service_ts_proto.generate_stubs && bazel run //clients/sidecar/libs/protos:test_service_ts_proto.generate_stubs && bazel run //clients/sidecar/libs/protos:sidecar_libs_ts_protos.copy", "build-storybook": "pnpm build-and-copy-protos && NODE_OPTIONS='--max-old-space-size=8192' storybook build", "chromatic": "npx chromatic --project-token=chpt_ea00cd7833f1948"}, "devDependencies": {"@chromatic-com/storybook": "^4.0.1", "@gitbutler/svelte-comment-injector": "^1.0.0", "@google-cloud/storage": "^7.15.0", "@magidoc/plugin-svelte-marked": "^4.1.4", "@poppanator/sveltekit-svg": "^5.0.0", "@popperjs/core": "^2.11.8", "@radix-ui/colors": "^3.0.0", "@rollup/plugin-replace": "^5.0.5", "@rollup/plugin-terser": "^0.4.4", "@storybook/addon-docs": "^9.0.18", "@storybook/addon-links": "^9.0.18", "@storybook/svelte": "^9.0.18", "@storybook/svelte-vite": "^9.0.18", "@sveltejs/vite-plugin-svelte": "^4.0.0", "@testing-library/jest-dom": "6.4.8", "@testing-library/svelte": "^5.2.8", "@testing-library/user-event": "14.4.3", "@tsconfig/svelte": "^5.0.2", "@types/diff": "^7.0.1", "@types/handlebars": "^4.1.0", "@types/lodash": "^4.14.202", "@types/lodash.clonedeep": "^4.5.9", "@types/lodash.debounce": "^4.0.9", "@types/lodash.isequal": "^4.5.8", "@types/lodash.throttle": "^4.1.9", "@types/scroll": "^3.0.3", "@types/sortablejs": "^1.15.8", "@vitest/coverage-v8": "^2.1.5", "chromatic": "^13.1.2", "eslint-plugin-storybook": "^9.0.18", "highlight.js": "^11.9.0", "jsdom": "^24.1.0", "json5": "^2.2.3", "monaco-editor": "^0.52.2", "prettier-plugin-svelte": "^3.2.6", "rollup": "^4.22.4", "storybook": "^9.0.18", "svelte": "^5.35.4", "svelte-check": "^4.0.0", "svelte-eslint-parser": "^0.42.0", "tslib": "^2.6.2", "tsx": "^4.19.2", "typescript": "^5.5.3", "vite": "5.4.19", "vite-plugin-json5": "^1.1.6", "vitest": "^2.1.5"}, "dependencies": {"@bufbuild/protobuf": "^2.3.0", "@connectrpc/connect": "2.0.2", "@number-flow/svelte": "^0.3.9", "@rive-app/canvas": "^2.26.4", "@sentry/svelte": "^9.20.0", "@tiptap/core": "^2.4.0", "@tiptap/extension-document": "^2.4.0", "@tiptap/extension-hard-break": "^2.4.0", "@tiptap/extension-history": "^2.4.0", "@tiptap/extension-image": "^2.11.3", "@tiptap/extension-mention": "^2.4.0", "@tiptap/extension-paragraph": "^2.4.0", "@tiptap/extension-placeholder": "^2.4.0", "@tiptap/extension-text": "^2.4.0", "@tiptap/pm": "^2.4.0", "@tiptap/suggestion": "^2.4.0", "@types/lru-cache": "^7.10.10", "@types/marked": "^6.0.0", "@vscode/codicons": "^0.0.36", "async-mutex": "^0.5.0", "date-fns": "^3.3.1", "diff": "^7.0.0", "dompurify": "^3.2.4", "eslint-plugin-svelte": "^2.45.1", "fuse.js": "^7.0.0", "github-slugger": "^2.0.0", "handlebars": "^4.7.8", "highlight.js": "^11.9.0", "hotkeys-js": "^3.13.15", "isomorphic-git": "^1.32.1", "lodash": "^4.17.21", "lodash.clonedeep": "^4.5.0", "lodash.debounce": "^4.0.8", "lodash.isequal": "^4.5.0", "lodash.throttle": "^4.1.1", "lru-cache": "^11.0.0", "marked": "^12.0.1", "mermaid": "^11.4.1", "monaco-editor": "^0.52.2", "motion": "^12.5.0", "scroll": "^3.0.1", "sortablejs": "^1.15.2", "svg-pan-zoom": "^3.6.2", "tippy.js": "^6.3.7", "ts-pattern": "^5.6.2", "zod": "^3.23.8"}, "peerDependencies": {"@popperjs/core": "^2.11.8", "@testing-library/dom": "^10.4.0", "aria-query": "^5.3.0", "dom-accessibility-api": "^0.7.0", "exenv-es6": "^1.1.1", "lz-string": "^1.5.0", "orderedmap": "^2.1.1", "pretty-format": "^29.7.0", "prosemirror-commands": "^1.5.2", "prosemirror-history": "^1.4.0", "prosemirror-keymap": "^1.2.2", "prosemirror-model": "^1.21.0", "prosemirror-schema-list": "^1.3.0", "prosemirror-state": "^1.4.3", "prosemirror-transform": "^1.9.0", "prosemirror-view": "^1.33.6", "rope-sequence": "^1.3.4", "tabbable": "^6.2.0", "w3c-keyname": "^2.2.8"}}