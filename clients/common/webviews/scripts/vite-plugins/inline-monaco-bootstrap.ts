import * as fs from "fs";
import * as path from "path";
import type { Plugin } from "vite";

interface InlineMonacoBootstrapOptions {
  /**
   * Path to the Monaco bootstrap script
   * If not provided, will use the default path in the common webviews directory
   */
  monacoBootstrapPath?: string;
}

/**
 * Vite plugin to inline the Monaco bootstrap script during build
 * @param options Plugin options
 * @returns Vite plugin
 */
export function inlineMonacoBootstrap(options: InlineMonacoBootstrapOptions = {}): Plugin {
  // Use the provided path or default to the local path in the common webviews directory
  const monacoBootstrapPath =
    options.monacoBootstrapPath || path.resolve(__dirname, "monaco-bootstrap.js");

  return {
    name: "inline-monaco-bootstrap",
    transformIndexHtml(html: string) {
      // Read the bootstrap script content
      const scriptPath = path.resolve(monacoBootstrapPath);
      const scriptContent = fs.readFileSync(scriptPath, "utf-8");

      // Replace the comment with the script content
      return html.replace(
        `<!-- vite-inline-monaco-bootstrap -->`,
        `<script>\n${scriptContent}\n</script>`,
      );
    },
  };
}

export default inlineMonacoBootstrap;
