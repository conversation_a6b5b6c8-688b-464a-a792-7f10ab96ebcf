import { vitePreprocess } from "@sveltejs/vite-plugin-svelte";

import svelteInjectComment from "@gitbutler/svelte-comment-injector";

// Consult https://svelte.dev/docs#compile-time-svelte-preprocess
// for more information about preprocessors
const preprocess = [vitePreprocess({ script: true })];
if (process.env.STORYBOOK === "true" || process.env.NODE_ENV === "development") {
  //this is a devtool that helps us figure out svelte.
  preprocess.push(svelteInjectComment());
}
export default {
  preprocess,
};
