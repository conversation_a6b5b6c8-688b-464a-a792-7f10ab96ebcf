load(
    "@aspect_rules_js//js:defs.bzl",
    "js_library",
    "js_test",
)
load("@aspect_rules_ts//ts:defs.bzl", "ts_config")
load("@npm//:defs.bzl", "npm_link_all_packages")
load(
    "@npm//clients/common/webviews:svelte-check/package_json.bzl",
    svelte_check_bin = "bin",
)
load("@npm//clients/common/webviews:vite/package_json.bzl", vite_bin = "bin")
load("@npm//clients/common/webviews:vitest/package_json.bzl", vitest_bin = "bin")

npm_link_all_packages()

SRC_GLOBS = [
    "src/**/*.ts",
    "src/**/*.svelte",
    "src/**/*.css",
    "src/**/*.svg",
    "src/**/*.mp4",
    "src/**/*.mp3",
    "src/**/*.json",
    "src/**/*.woff",
    "*.html",
]

js_library(
    name = "src_files",
    srcs = glob(["src/design-system/icons/augment/rive/*.riv"]) + glob(SRC_GLOBS) + [
        "package.json",
        ":tsconfig",
        ":tsconfig_node",
        "//clients:tsconfig",
        "//clients/sidecar/libs:src",
        "//clients/sidecar/libs/protos:sidecar_libs_ts_protos",
    ] + [
        ":node_modules/@bufbuild/protobuf",
        ":node_modules/@connectrpc/connect",
        ":node_modules/@magidoc/plugin-svelte-marked",
        ":node_modules/@popperjs/core",
        ":node_modules/@tiptap/core",
        ":node_modules/@types/diff",
        ":node_modules/@types/lodash",
        ":node_modules/@types/lodash.clonedeep",
        ":node_modules/@types/lodash.debounce",
        ":node_modules/@types/lodash.throttle",
        ":node_modules/diff",
        ":node_modules/fuse.js",
        ":node_modules/github-slugger",
        ":node_modules/highlight.js",
        ":node_modules/lodash",
        ":node_modules/lodash.clonedeep",
        ":node_modules/lodash.debounce",
        ":node_modules/lodash.throttle",
        ":node_modules/marked",
        ":node_modules/monaco-editor",
        ":node_modules/prosemirror-model",
        ":node_modules/tippy.js",
    ],
    visibility = [
        "//clients/beachhead:__pkg__",
        "//clients/vscode:__subpackages__",
    ],
    deps = [
        "//clients/vscode:shared_webview_files",
    ],
)

js_library(
    name = "shared_src_files_common",
    srcs = [
        # TSConfigs
        ":tsconfig",
        ":tsconfig_node",
        # Host files
        "src/common/hosts/host.ts",
        "src/common/hosts/create-host.ts",
        "src/common/hosts/vscode/vscode.ts",
        "src/common/hosts/host-types.ts",
        # Models
        "src/common/models/focus-model.ts",
        # CSS Files
        "src/common/css/reset.css",
        "src/common/css/_z-index.css",
        "src/common/css/variables.css",
    ],
)

# TSConfig for the project source files
ts_config(
    name = "tsconfig",
    src = "tsconfig.json",
    visibility = [
        "//clients:__pkg__",
    ],
    deps = [
        ":node_modules/@tsconfig/svelte",
        ":tsconfig_node",
    ],
)

# TSConfig used for the vite.config.ts file
ts_config(
    name = "tsconfig_node",
    src = "tsconfig.node.json",
)

CONFIGS = [
    ".eslintrc.cjs",  # Eslint config for this project
    "package.json",  # Lists deps/scripts/etc.
    "svelte.config.js",  # Svelte config for this project
    "vite.config.ts",  # Vite config for this project
    "vitest-setup.ts",  # Run before each vitest test
    ":tsconfig",  # Used as tsconfig for this project
    ":tsconfig_node",  # Used for vite config.ts files
    "//clients:tsconfig",  # Shared tsconfig target
]

VITE_PLUGINS = [
    "scripts/vite-plugins/inline-monaco-bootstrap.ts",
    "scripts/vite-plugins/monaco-bootstrap.js",
]

vite_bin.vite(
    name = "webview-apps",
    srcs = [
        ":node_modules",
        ":src_files",
        "//clients/sidecar/libs/protos:sidecar_libs_ts_protos",
    ] + CONFIGS + VITE_PLUGINS,
    args = [
        "build",
        "-c",
        "vite.config.ts",
    ],
    chdir = package_name(),
    out_dirs = ["dist/"],
    silent_on_success = False,  # Always log vite output to console
    visibility = ["//clients/vscode:__subpackages__"],
)

# This is used by the webapp to build the webview apps
js_library(
    name = "webview-apps-full-src",
    srcs = glob(SRC_GLOBS) + VITE_PLUGINS,
    visibility = ["//clients/webapp:__pkg__"],
    deps = [
        ":node_modules",
        "//clients:tsconfig",
        "//clients/sidecar/libs:src",
        "//clients/sidecar/libs/protos:sidecar_libs_ts_protos",
        "//clients/vscode:shared_webview_files",
    ],
)

# Just verify that the bundle produced "something reasonable" but doesn't
# verify it functions in a browser.
js_test(
    name = "build_smoke_test",
    timeout = "short",
    data = CONFIGS + VITE_PLUGINS + [":webview-apps"],
    entry_point = "build-smoke-test.js",
)

svelte_check_bin.svelte_check_test(
    name = "svelte_check_test",
    size = "large",  # Type checking can take a while, especially on clean builds
    args = [
        "--workspace",
        package_name(),
        "--tsconfig",
        "tsconfig.json",
        "--fail-on-warnings",
    ],
    data = [
               ":node_modules",
               ":src_files",
           ] + CONFIGS +
           VITE_PLUGINS + glob([
        "mocks/**/*",
    ]),
    include_transitive_types = True,  # Needed for type checking
)

vitest_bin.vitest_test(
    name = "vitest_test",
    args = [
        "run",
        "--config=vite.config.ts",
    ],
    chdir = package_name(),
    data = [
               ":node_modules",
               ":src_files",
               "//clients/vscode:shared_webview_files",
           ] + CONFIGS +
           VITE_PLUGINS + glob([
        "mocks/**/*",
    ]),
)
