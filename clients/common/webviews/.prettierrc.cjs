// Because we add a plugin here, we needed to make this a .cjs file.
// This is because the combination of:
// - pre-commit
// - pnpm
// - prettier-plugin-svelte
// Doesn't seem to work very well together and this was the only solution
// we could get to work.
// Future versions of prettier may handle finding the plugins better (or
// support ESMs).
// Background: https://github.com/tailwindlabs/prettier-plugin-tailwindcss/issues/113#issuecomment-1509646605
module.exports = {
  $schema: "https://json.schemastore.org/prettierrc",
  semi: true,
  printWidth: 100,
  tabWidth: 2,
  plugins: [require("prettier-plugin-svelte")],
  overrides: [
    {
      files: "*.svelte",
      options: {
        parser: "svelte",
      },
    },
  ],
};
