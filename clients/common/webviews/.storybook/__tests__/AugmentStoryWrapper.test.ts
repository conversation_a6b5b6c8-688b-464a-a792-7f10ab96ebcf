import { describe, it, expect, beforeEach, afterEach, vi } from "vitest";

/**
 * Test suite for AugmentStoryWrapper Chromatic detection functionality
 *
 * These tests verify that the wrapper correctly detects when running in Chromatic
 * and applies appropriate theme handling for consistent visual regression testing.
 */

describe("AugmentStoryWrapper Chromatic Detection", () => {
  // Store original values to restore after tests
  let originalWindow: any;
  let originalProcess: any;
  let originalLocation: any;
  let originalNavigator: any;

  beforeEach(() => {
    // Store original values
    originalWindow = global.window;
    originalProcess = global.process;

    // Mock window object
    global.window = {
      location: {
        href: "http://localhost:6006",
        search: "",
      },
      navigator: {
        userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36",
      },
    } as any;

    // Mock process object
    global.process = {
      env: {},
    } as any;
  });

  afterEach(() => {
    // Restore original values
    global.window = originalWindow;
    global.process = originalProcess;
    vi.clearAllMocks();
  });

  describe("Chromatic Environment Detection", () => {
    it("should detect Chromatic via __CHROMATIC__ global variable", () => {
      // Set up Chromatic global variable
      (global.window as any).__CHROMATIC__ = true;

      // Import the component to test the detection logic
      // Note: In a real test, we'd need to dynamically import or test the actual function
      const mockIsRunningInChromatic = () => {
        if (typeof window !== "undefined") {
          if ((window as any).__CHROMATIC__) {
            return true;
          }
        }
        return false;
      };

      expect(mockIsRunningInChromatic()).toBe(true);
    });

    it("should detect Chromatic via URL parameters", () => {
      global.window.location.search = "?chromatic=true";

      const mockIsRunningInChromatic = () => {
        if (typeof window !== "undefined") {
          const urlParams = new URLSearchParams(window.location.search);
          return urlParams.has("chromatic") || urlParams.get("chromatic") === "true";
        }
        return false;
      };

      expect(mockIsRunningInChromatic()).toBe(true);
    });

    it("should detect Chromatic via chromatic.com URL", () => {
      global.window.location.href = "https://main--project.chromatic.com/story/example";

      const mockIsRunningInChromatic = () => {
        if (typeof window !== "undefined") {
          return window.location.href.includes("chromatic.com");
        }
        return false;
      };

      expect(mockIsRunningInChromatic()).toBe(true);
    });

    it("should detect Chromatic via user agent", () => {
      global.window.navigator.userAgent =
        "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 Chromatic";

      const mockIsRunningInChromatic = () => {
        if (typeof window !== "undefined") {
          const userAgent = window.navigator.userAgent;
          return userAgent.includes("Chromatic") || userAgent.includes("chromatic");
        }
        return false;
      };

      expect(mockIsRunningInChromatic()).toBe(true);
    });

    it("should detect Chromatic via environment variables", () => {
      global.process.env.CHROMATIC_PROJECT_TOKEN = "test-token";

      const mockIsRunningInChromatic = () => {
        if (typeof process !== "undefined" && process.env) {
          return !!(
            process.env.CHROMATIC_PROJECT_TOKEN ||
            process.env.CHROMATIC_BUILD_ID ||
            process.env.CHROMATIC_BRANCH ||
            process.env.CHROMATIC_SHA
          );
        }
        return false;
      };

      expect(mockIsRunningInChromatic()).toBe(true);
    });

    it("should not detect Chromatic in normal Storybook environment", () => {
      // Default setup should not detect Chromatic
      const mockIsRunningInChromatic = () => {
        if (typeof window !== "undefined") {
          const urlParams = new URLSearchParams(window.location.search);
          const isChromatic =
            urlParams.has("chromatic") || window.location.href.includes("chromatic.com");
          const userAgent = navigator.userAgent;
          const isChromaticUA = userAgent.includes("Chromatic");
          return isChromatic || isChromaticUA;
        }
        return false;
      };

      expect(mockIsRunningInChromatic()).toBe(false);
    });
  });

  describe("Theme Handling in Chromatic", () => {
    it("should apply theme consistently when in Chromatic environment", () => {
      // Set up Chromatic environment
      (global.window as any).__CHROMATIC__ = true;

      const mockGetEffectiveTheme = (inputTheme: string) => {
        const isChromatic = !!(global.window as any).__CHROMATIC__;

        if (isChromatic) {
          console.log(`[Chromatic] Using theme: ${inputTheme}`);
          return inputTheme;
        }

        return inputTheme;
      };

      const consoleSpy = vi.spyOn(console, "log").mockImplementation(() => {});

      const result = mockGetEffectiveTheme("vscode-dracula");

      expect(result).toBe("vscode-dracula");
      expect(consoleSpy).toHaveBeenCalledWith("[Chromatic] Using theme: vscode-dracula");

      consoleSpy.mockRestore();
    });

    it("should add data attributes for Chromatic debugging", () => {
      // Mock document
      const mockDocument = {
        documentElement: {
          setAttribute: vi.fn(),
        },
      };
      global.document = mockDocument as any;

      // Set up Chromatic environment
      (global.window as any).__CHROMATIC__ = true;

      const mockEnsureChromaticThemeConsistency = (theme: string) => {
        const isChromatic = !!(global.window as any).__CHROMATIC__;

        if (isChromatic && typeof document !== "undefined") {
          document.documentElement.setAttribute("data-chromatic-theme", theme);
          document.documentElement.setAttribute("data-chromatic-mode", "true");
        }
      };

      mockEnsureChromaticThemeConsistency("vscode-light-plus");

      expect(mockDocument.documentElement.setAttribute).toHaveBeenCalledWith(
        "data-chromatic-theme",
        "vscode-light-plus",
      );
      expect(mockDocument.documentElement.setAttribute).toHaveBeenCalledWith(
        "data-chromatic-mode",
        "true",
      );
    });
  });

  describe("Integration with Storybook Modes", () => {
    it("should respect theme from Storybook modes configuration", () => {
      // Set up Chromatic environment
      (global.window as any).__CHROMATIC__ = true;

      // Mock different themes that would come from modes configuration
      const themes = ["vscode-dracula", "vscode-light-plus", "intellij-darcula"];

      themes.forEach((theme) => {
        const mockGetEffectiveTheme = (inputTheme: string) => {
          const isChromatic = !!(global.window as any).__CHROMATIC__;
          return isChromatic ? inputTheme : inputTheme;
        };

        const result = mockGetEffectiveTheme(theme);
        expect(result).toBe(theme);
      });
    });
  });
});
