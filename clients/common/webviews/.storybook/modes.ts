import { MockTheme, mockThemeCategory } from "../mocks/hosts/mock-host";

/**
 * We can add more themes here for testing in chromatic.
 */
export const CHROMATIC_USE_THEMES = [MockTheme.vscodeDracula, MockTheme.vscodeLightPlus];
export const allThemes = Object.values(MockTheme).reduce(
  (acc, theme) => {
    if (!CHROMATIC_USE_THEMES.includes(theme)) {
      return acc;
    }
    acc[theme] = {
      backgrounds: mockThemeCategory(theme),
      theme,
      viewport: "large",
    };
    return acc;
  },
  {} as Record<keyof typeof MockTheme, { backgrounds: string; theme: MockTheme; viewport: string }>,
);
