# Chromatic Integration for AugmentStoryWrapper

This document explains how the `AugmentStoryWrapper.svelte` component has been enhanced to work seamlessly with Chromatic, the visual testing service for Storybook.

## Overview

The `AugmentStoryWrapper` component now automatically detects when it's running in a Chromatic environment and applies specific optimizations to ensure consistent visual regression testing across light and dark theme modes.

## Features

### 1. Chromatic Environment Detection

The wrapper detects Chromatic through multiple methods:

- **Global Variables**: Checks for `window.__CHROMATIC__` (set by `@chromatic-com/storybook` addon)
- **URL Parameters**: Detects `chromatic=true` or `chromatic-build-id` parameters
- **URL Patterns**: Recognizes `chromatic.com`, `chromatic-builds`, or `chromatic.app` domains
- **User Agent**: Identifies Chromatic-specific user agent strings
- **Environment Variables**: Checks for `CHROMATIC_PROJECT_TOKEN`, `CHROMATIC_BUILD_ID`, etc.
- **Window Properties**: Looks for Chromatic-specific window properties

### 2. Theme Consistency

When running in Chromatic:

- **Respects Mode Configuration**: Uses themes exactly as specified in the `modes.ts` configuration
- **Consistent Application**: Ensures themes are applied consistently across all stories
- **Debug Logging**: Provides console logs to help debug theme application
- **Data Attributes**: Adds `data-chromatic-theme` and `data-chromatic-mode` attributes for debugging

### 3. Visual Testing Optimizations

Chromatic-specific CSS optimizations include:

- **Font Smoothing**: Applies consistent font rendering (`-webkit-font-smoothing: antialiased`)
- **Animation Disabling**: Removes all animations and transitions for consistent screenshots
- **Style Recalculation**: Forces style recalculation to ensure theme consistency

## Configuration

### Storybook Configuration

The Chromatic integration works with the existing Storybook configuration in `preview.tsx`:

```typescript
parameters: {
  chromatic: {
    modes: allThemes, // Defined in modes.ts
  },
}
```

### Theme Modes

All available themes are automatically configured for Chromatic testing via the `allThemes` object in `modes.ts`, which includes:

- VSCode themes (Light Plus, Dracula, Cobalt, etc.)
- IntelliJ themes (Light, Darcula, Material, etc.)
- High contrast themes

## Usage

### For Story Authors

No changes are required for individual stories. The wrapper automatically:

1. Detects the Chromatic environment
2. Applies the correct theme based on the mode being tested
3. Ensures consistent rendering for visual regression testing

### For Chromatic Builds

When Chromatic runs visual tests:

1. Each story is rendered in all configured theme modes
2. The wrapper ensures consistent theme application
3. Screenshots are captured with disabled animations for stability
4. Debug information is logged to help troubleshoot any issues

## Testing

The integration includes comprehensive tests in `.storybook/__tests__/AugmentStoryWrapper.test.ts` that verify:

- Chromatic environment detection across all methods
- Theme handling consistency
- Data attribute application
- Integration with Storybook modes

Run tests with:

```bash
pnpm run vitest .storybook/__tests__/AugmentStoryWrapper.test.ts
```

## Debugging

### Console Logs

When running in Chromatic, you'll see logs like:

```
[Chromatic] Using theme: vscode-dracula
[Chromatic] Applying theme: vscode-dracula
[Chromatic] Re-applying theme for consistency: vscode-dracula
```

### Data Attributes

The wrapper adds these attributes to help with debugging:

- `data-chromatic-mode="true"` on the document element
- `data-chromatic-theme="theme-name"` on the document element

### CSS Classes

Chromatic-specific styles are applied via:

- `:global([data-chromatic-mode="true"])` for general Chromatic optimizations
- `:global([data-chromatic-theme])` for theme-specific optimizations

## Troubleshooting

### Theme Not Applied Correctly

1. Check console logs for Chromatic detection messages
2. Verify the theme is included in `modes.ts`
3. Ensure the theme exists in the `MockTheme` enum

### Visual Inconsistencies

1. Verify animations are disabled (check for `data-chromatic-mode` attribute)
2. Check that font smoothing is applied
3. Ensure style recalculation is triggered

### Detection Issues

1. Check if Chromatic environment variables are set
2. Verify the `@chromatic-com/storybook` addon is installed
3. Look for Chromatic-specific URL parameters or domains

## Implementation Details

The implementation consists of:

1. **Detection Logic**: Multi-method Chromatic environment detection
2. **Theme Management**: Consistent theme application with fallbacks
3. **CSS Optimizations**: Chromatic-specific styles for visual consistency
4. **Debug Support**: Comprehensive logging and data attributes
5. **Test Coverage**: Full test suite for all functionality

This integration ensures that Storybook stories render consistently in Chromatic's visual testing environment while maintaining full functionality in local development.
