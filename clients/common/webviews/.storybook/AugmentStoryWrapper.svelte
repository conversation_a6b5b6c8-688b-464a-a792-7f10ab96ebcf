<script lang="ts">
  import "$common-webviews/src/common/css/reset.css";
  import "$common-webviews/mocks/hosts/monaco-init";
  import isChromatic from "chromatic/isChromatic";

  import { injectCSSStyles, setMockTheme } from "../mocks/hosts/mock-host";
  import MonacoProvider from "$common-webviews/src/design-system/components/MonacoProvider";

  /**
   * This component takes the theme attribute from storybook and sets up the
   * necessary attributes and injects the styles to emulate a clients theme
   * and styles. This component should shield the component from storybook
   * styling. It also provides an adaptor to this web-component to svelte.
   *
   * When running in Chromatic (visual testing service), it automatically
   * detects the environment and ensures proper theme handling for consistent
   * visual regression testing across light and dark modes.
   */

  export let theme: string;

  /**
   * Ensures theme is properly applied for Chromatic visual testing
   */
  function ensureChromaticThemeConsistency(theme: string): void {
    if (isChromatic()) {
      // Add data attributes to help with debugging and ensure consistent styling
      if (typeof document !== "undefined") {
        document.documentElement.setAttribute("data-chromatic-theme", theme);
        document.documentElement.setAttribute("data-chromatic-mode", "true");

        // Force a style recalculation to ensure consistency
        document.body.style.display = "none";
        document.body.offsetHeight; // Trigger reflow
        document.body.style.display = "";
      }
    }
  }

  $: {
    if (isChromatic()) {
      console.log(`[Chromatic] Applying theme: ${theme}`);
    }
    setMockTheme(theme as any);
    injectCSSStyles(theme as any, document.body);

    // Ensure consistency for Chromatic visual testing
    ensureChromaticThemeConsistency(theme);
  }

  // Initial setup
  setMockTheme(theme as any);
  injectCSSStyles(theme as any, document.body);
  ensureChromaticThemeConsistency(theme);
</script>

<MonacoProvider.Root>
  <div class="augment-story-wrapper">
    <slot />
  </div>
</MonacoProvider.Root>

<style>
  :root {
    /* VSCode uses 16px as its root font size, do not change this*/
    font-size: 16px;
    height: 100%;
  }

  /* Ensure the body allows scrolling */
  :global(body) {
    overflow-y: auto;
    height: 100%;
  }

  /* Ensure the Storybook iframe allows scrolling */
  :global(#storybook-root) {
    height: auto;
    min-height: 100%;
  }

  .augment-story-wrapper {
    display: flex;
    flex-direction: column;
    padding: 20px;
    background-color: var(--augment-window-background);
    width: 100%;
    /* Allow content to determine height, don't force min-height */
    min-height: fit-content;
  }

  /* Remove horizontal padding for fullscreen chat components that need full viewport */
  .augment-story-wrapper:has(:global(.chat)),
  .augment-story-wrapper:has(:global(.l-chat-wrapper)) {
    padding-inline: 0px;
    height: 100vh;
    min-height: 100vh;
  }

  /* Chromatic-specific styles for consistent visual testing */
  :global([data-chromatic-mode="true"]) {
    /* Ensure consistent rendering in Chromatic */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* Disable animations and transitions for consistent screenshots in Chromatic */
  :global([data-chromatic-mode="true"] *),
  :global([data-chromatic-mode="true"] *::before),
  :global([data-chromatic-mode="true"] *::after) {
    animation-duration: 0s !important;
    animation-delay: 0s !important;
    transition-duration: 0s !important;
    transition-delay: 0s !important;
  }

  /* Ensure theme-specific styles are properly applied in Chromatic */
  :global([data-chromatic-theme]) .augment-story-wrapper {
    /* Force style recalculation to ensure theme consistency */
    transform: translateZ(0);
  }
</style>
