{
  "extends": "../../tsconfig.json",
  "compilerOptions": {
    //inlined from [@tsconfig/svelte/tsconfig.json](https://github.com/tsconfig/bases?tab=readme-ov-file#svelte-tsconfigjson)
    "moduleResolution": "node",
    "noEmit": true,
    /**
      To have warnings/errors of the Svelte compiler at the correct position,
      enable source maps by default.
     */
    "sourceMap": true,

    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    //end of inlined
    "target": "ESNext",
    "useDefineForClassFields": true,
    "module": "ESNext",
    "resolveJsonModule": true,
    "lib": ["ESNext", "DOM", "DOM.Iterable"],

    // Needed to ensure svg imports work with svelte-check
    "types": ["vite/client", "@testing-library/jest-dom", "wicg-file-system-access", "vitest/globals"],

    /**
     * Typecheck JS in `.svelte` and `.js` files by default.
     * Disable checkJs if you'd like to use dynamic types in JS.
     * Note that setting allowJs false does not prevent the use
     * of JS in `.svelte` files.
     */
    "allowJs": true,
    "checkJs": true,
    "isolatedModules": false
    // enable when this works?
    //    "verbatimModuleSyntax": false,
  },
  "include": [
    "vitest-setup.ts",
    "src/**/*.ts",
    "src/**/*.svelte",
    "mocks/**/*.ts",
    "mocks/**/*.svelte",
    "scripts/**/*.ts",
  ],
  "references": [{ "path": "./tsconfig.node.json" }]
}
