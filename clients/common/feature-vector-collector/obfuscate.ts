/**
 * @precondition
 *  - Jscrambler credentials stored in google secret manager under the name `bazel-runner-jscrambler-<access/secret>-key`
 *  - `pnpm build` has been run to build the feature vector collector
 *
 * @postcondition
 *  - obfuscated version of the feature vector collector is written to `src/feature-vector-collector-obfuscated.js`
 *  - signatures written to `src/feature-vector-collector-obfuscated.signatures.json`. These signatures will be verified by a test.
 *
 *
 */

import jscrambler from "jscrambler";
import * as fs from "fs";
import * as crypto from "crypto";
import { SecretManagerServiceClient } from "@google-cloud/secret-manager";
import * as path from "path";

type Signature = {
  data: {
    timestamp: number;
    [file: string]: string | number;
  };
  hash: string;
};

async function getCredentials() {
  const secretKeyPath =
    "projects/1035750215372/secrets/bazel-runner-jscrambler-secret-key/versions/latest";
  const accessKeyPath =
    "projects/1035750215372/secrets/bazel-runner-jscrambler-access-key/versions/latest";
  const accessKey = await getSecret(accessKeyPath);
  const secretKey = await getSecret(secretKeyPath);
  return { accessKey, secretKey };
}

async function getSecret(secretPath: string) {
  const client = new SecretManagerServiceClient();
  const [res] = await client.accessSecretVersion({
    name: secretPath,
  });
  const val = res.payload?.data?.toString();
  if (!val) throw new Error(`Secret '${secretPath}' is empty`);
  return val;
}

async function obfuscate() {
  const credentials = await getCredentials();
  const config = JSON.parse(
    await fs.promises.readFile(path.join(__dirname, "jscrambler.json"), "utf8"),
  );

  config.keys = credentials;

  if (!config.keys.accessKey || !config.keys.secretKey) {
    throw new Error("Missing Jscrambler credentials");
  }

  if (fs.existsSync("out/feature-vector-collector.js")) {
    // Add files to obfuscate
    config.filesSrc = ["out/feature-vector-collector.js"];
    config.filesDest = "src/feature-vector-collector-obfuscated.js"; // we want the output in src
  } else {
    throw new Error("No feature vector collector file found");
  }

  await jscrambler.protectAndDownload(config);
  console.log("Obfuscation complete");
  await ensureTrailingNewline(config.filesDest);
}

async function calculateFileSignature(file: string) {
  const fileContent = fs.readFileSync(file, "utf8");
  const hasher = crypto.createHash("sha256");
  hasher.update(fileContent);
  return hasher.digest("hex");
}

function calculateHash(data: string) {
  const hasher = crypto.createHash("sha256");
  hasher.update(data);
  return hasher.digest("hex");
}

async function calculateSignatures() {
  const signatureData: Signature["data"] = {
    timestamp: Date.now(),
  };

  const files = [
    "src/feature-vector-collector-obfuscated.js",
    "src/feature-vector-collector.ts",
  ];
  for (const file of files) {
    signatureData[file] = await calculateFileSignature(file);
  }

  const signature: Signature = {
    data: signatureData,
    hash: calculateHash(JSON.stringify(signatureData, null, 2)),
  };

  fs.writeFileSync(
    "src/feature-vector-collector-obfuscated.signatures.json",
    JSON.stringify(signature, null, 2) + "\n", // add newline for git
  );
}

async function ensureTrailingNewline(filePath: string) {
  const content = fs.readFileSync(filePath, "utf8");
  if (!content.endsWith("\n")) {
    fs.writeFileSync(filePath, content + "\n");
  }
}

async function main() {
  await obfuscate();
  await calculateSignatures();
  await ensureTrailingNewline("src/feature-vector-collector-obfuscated.js");
  await ensureTrailingNewline(
    "src/feature-vector-collector-obfuscated.signatures.json",
  );
}

main()
  .then(() => {
    console.log("done");
    process.exit(0);
  })
  .catch((err) => {
    console.error("Error:", err);
    process.exit(1);
  });
