// jscrambler does not offer types.
declare module 'jscrambler' {
  interface ProtectAndDownloadConfig {
    keys: {
      accessKey: string;
      secretKey: string;
    };
    applicationId: string;
    filesSrc: string[];
    outputDir: string;
    params?: unknown[];
  }

  interface JscramblerAPI {
    protectAndDownload(config: ProtectAndDownloadConfig): Promise<void>;
  }

  const api: JscramblerAPI;
  export default api;
}