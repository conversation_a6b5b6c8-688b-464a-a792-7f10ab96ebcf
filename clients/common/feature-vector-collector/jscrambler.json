{"applicationId": "683a10e3d710d5fe0466d8ab", "jscramblerVersion": "8.3", "areSubscribersOrdered": false, "useRecommendedOrder": true, "tolerateMinification": true, "profilingDataMode": "off", "useAppClassification": true, "browsers": {}, "sourceMaps": false, "params": [{"name": "objectPropertiesSparsing"}, {"name": "variableMasking"}, {"name": "whitespaceRemoval"}, {"name": "dotToBracketNotation"}, {"name": "stringConcealing"}, {"name": "functionReordering"}, {"name": "propertyKeysObfuscation", "options": {"encoding": ["hexadecimal"]}}, {"name": "regexObfuscation"}, {"options": {"features": ["opaqueSteps"]}, "name": "controlFlowFlattening"}, {"name": "booleanToAnything"}, {"name": "identifiersRenaming"}, {"name": "globalVariableIndirection"}]}