module.exports = {
  preset: "ts-jest",
  testEnvironment: "node",
  roots: ["<rootDir>/src"],
  testMatch: ["**/__tests__/**/*.test.ts"],
  transform: {
    "^.+\\.ts$": "ts-jest",
  },
  moduleFileExtensions: ["ts", "js", "json", "node"],
  collectCoverage: true,
  coverageDirectory: "coverage",
  // Force Jest to exit after tests complete to prevent hanging
  forceExit: true,
  // Detect open handles that might prevent <PERSON><PERSON> from exiting
  detectOpenHandles: true,
  // Set test timeout to prevent individual tests from hanging
  testTimeout: 30000, // 30 seconds
  // Set global timeout for the entire test suite
  globalTimeout: 60000, // 60 seconds
};
