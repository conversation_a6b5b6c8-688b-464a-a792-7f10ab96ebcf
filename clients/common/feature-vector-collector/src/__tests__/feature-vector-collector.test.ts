import { createFeatures } from "../feature-vector-collector";
import { createFeatures as createFeaturesObfuscated } from "../feature-vector-collector-obfuscated";

describe("feature-vector-collector", () => {
  it("SANITY should create vector with numeric keys", async () => {
    const features = await createFeatures({
      version: "1.0.0",
      env: {
        machineId: "1234567890",
      },
    });
    const vector = features.toVector();
    // assert all keys are integerrs
    for (const key of Object.keys(vector)) {
      expect({ key, isInteger: Number.isInteger(Number(key)) }).toEqual({
        key,
        isInteger: true,
      });
    }
  });
});

// Important to test the obfuscated version, because obfuscation can break the code
describe("feature-vector-collector OBFUSCATED", () => {
  it("SANITY should create vector with numeric keys", async () => {
    const features = await createFeaturesObfuscated({
      version: "1.0.0",
      env: {
        machineId: "1234567890",
      },
    });
    const vector = features.toVector();
    // assert all keys are integerrs
    for (const key of Object.keys(vector)) {
      expect({ key, isInteger: Number.isInteger(Number(key)) }).toEqual({
        key,
        isInteger: true,
      });
    }
  });
});
