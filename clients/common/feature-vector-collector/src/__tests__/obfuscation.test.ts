import * as crypto from "crypto";
import path from "path";
import * as fs from "fs";

describe("obfuscation", () => {
  it("should not change the output of the feature vector collector", () => {
    const filepath = path.join(
      __dirname,
      "../feature-vector-collector-obfuscated.signatures.json",
    );
    // assert file exists
    expect(fs.existsSync(filepath)).toBe(true);
    // read the file
    const signatures = JSON.parse(fs.readFileSync(filepath, "utf8"));

    // for each file verify the hash matches
    for (const file of Object.keys(signatures.data)) {
      if (file === "timestamp") {
        continue;
      }
      const filepath = path.join(__dirname, "../../", file);
      // assert file exists
      expect({
        file,
        dirname: __dirname,
        filepath,
        exists: fs.existsSync(filepath),
      }).toEqual({
        filepath,
        file,
        dirname: __dirname,
        exists: true,
      });
      const fileContent = fs.readFileSync(filepath, "utf8");
      const hasher = crypto.createHash("sha256");
      hasher.update(fileContent);
      expect(hasher.digest("hex")).toEqual(signatures.data[file]);
    }

    // verify the hash matches
    const hash = crypto
      .createHash("sha256")
      .update(JSON.stringify(signatures.data, null, 2))
      .digest("hex");
    expect(hash).toEqual(signatures.hash);
  });
});
