{"private": true, "dependencies": {"node-machine-id": "^1.1.12", "systeminformation": "^5.21.22"}, "scripts": {"build:all": "npm run build:tsc && npm run build:bundle && npm run build:obfuscate", "build:tsc": "tsc", "build:bundle": "esbuild out/feature-vector-collector.js --bundle --platform=node --target=node18 --outfile=out/feature-vector-collector.js --external:vscode --allow-overwrite", "build:obfuscate": "ts-node --project tsconfig.obfuscate.json obfuscate.ts", "test": "jest"}, "devDependencies": {"@google-cloud/secret-manager": "^6.0.1", "@types/jest": "^29.5.11", "@types/node": "^22.9.1", "jest": "^29.7.0", "jscrambler": "^8.9.0", "ts-jest": "^29.4.0", "typescript": "^5.2.2", "esbuild": "^0.19.0", "jest-cli": "^29.7.0", "jest-junit": "^15.0.0", "ts-node": "^10.9.2"}}