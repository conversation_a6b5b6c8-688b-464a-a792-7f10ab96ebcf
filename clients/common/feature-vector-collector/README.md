# Feature Vector Collector

## Overview

The **Feature Vector Collector** module is responsible for collecting identity-related information about the user. This information is used by fraud prevention.

To ensure flexibility and maintain strong security practices, this module is separated from other components in the system.
This separation enables independent obfuscation, which is critical due to the client-side execution context.

## Why Obfuscation Matters

Since the code runs on the client side, it is inherently accessible and subject to reverse engineering.
By isolating the identity-related logic in this module, we can apply focused and enhanced obfuscation techniques.
This makes it significantly harder for malicious actors to understand or interfere with our data collection mechanisms.

## Key Responsibilities

- Collect user identity signals (e.g., device fingerprints, browser characteristics, behavior patterns)
- Package collected data into a consistent feature vector format

## Design Principles

- **Modularity**: The module is standalone to allow independent versioning and obfuscation.
- **Security**: Designed with obfuscation as a first-class concern to protect sensitive collection logic.
- **Minimal Surface Area**: Only exposes necessary APIs for integration, reducing risk of misuse.

## Build Process

The module uses a multi-step build process:

1. **TypeScript Compilation**: `tsc` compiles TypeScript to JavaScript
2. **Bundling**: `esbuild` bundles all dependencies into the output files
3. **Obfuscation**: `jscrambler` obfuscates the output files

```bash
npm run build:all
```

This creates bundled files in `out/` directory:
- `out/feature-vector-collector.js` - Single file with all dependencies bundled (ready for obfuscation)
- `out/feature-vector-collector.d.ts` - TypeScript declarations

And an obfuscated version in `src/` directory:
- `src/feature-vector-collector-obfuscated.js` - Obfuscated version of the feature vector collector
- `src/feature-vector-collector-obfuscated.d.ts` - TypeScript declarations for the obfuscated version
- `src/feature-vector-collector-obfuscated.signatures.json` - Signatures for the obfuscated version, helps verify the obfuscated version is is not stale compared to the source.

## Usage

The following will use the obfuscated version, mapped by `clients/tsconfig.json`.

```js
import { createFeatures, FeatureVector, Features } from '@augment-internal/feature-vector-collector';

const userFeatures: Features = createFeatures();
const featureVector: FeatureVector = userFeatures.toVector();

// Use `userFeatures` in analytics, personalization, or risk evaluation workflows.
```


## Feature Entries

Each feature vector contains the following data points with their corresponding index numbers:

### Basic System Information
- **[0] vscode**: VSCode version number (e.g., "1.100.3")
- **[2] os**: Operating system type (e.g., "Linux", "Windows", "Darwin")
- **[7] arch**: System architecture (e.g., "x86_64", "arm64")
- **[6] hostname**: Computer name on the network (e.g., "dev-xz-1")
- **[8] username**: Current user's login name (e.g., "xiaolei")
- **[25] timezone**: System timezone setting (e.g., "UTC+0000")

### Hardware Information
- **[3] cpu**: CPU model name (e.g., "Intel(R) Xeon(R) CPU @ 2.20GHz")
- **[5] numCpus**: Number of CPU cores (e.g., "16")
- **[4] memory**: Total system RAM in bytes (e.g., "67418394624" = ~64GB)
- **[33] cpuFlags**: CPU feature capabilities (long string of processor features)
- **[24] gpuInfo**: Graphics card details including model and VRAM (JSON format)
- **[26] diskLayout**: Physical storage devices with sizes and types (JSON format)

### Machine Identifiers (Different Sources)
- **[1] machineId**: VSCode's internal machine ID (generated by VSCode itself)
- **[16] osMachineId**: Operating system's machine ID (from node-machine-id library)
- **[22] userDataMachineId**: Machine ID stored in VSCode's user data folder
- **[13] telemetryDevDeviceId**: VSCode telemetry device ID (often empty)

### Network Information
- **[9] macAddresses**: Network card MAC addresses for external interfaces
- **[20] sshPublicKey**: User's SSH public key content (for Git/remote access)
- **[39] sshKnownHosts**: Recent SSH connection history (last 5 entries, hashed hostnames)

### File System Information
- **[17] homeDirectoryIno**: Unique file system ID of user's home folder
- **[18] projectRootIno**: Unique file system ID of current working directory
- **[21] userDataPathIno**: Unique file system ID of VSCode's user data folder
- **[23] storageUriPath**: File path where VSCode extension stores data

### System Details
- **[10] osRelease**: Operating system version (e.g., "5.15.0-1075-gcp")
- **[11] kernelVersion**: Kernel build information
- **[38] systemBootTime**: When the system was last started (timestamp in milliseconds)
- **[19] gitUserEmail**: Git configuration email address
- **[37] hypervisorType**: Virtualization platform if running in VM (often empty)

### Hardware Components
- **[27] systemInfo**: Computer manufacturer and model (JSON with Google, Dell, etc.)
- **[28] biosInfo**: BIOS/UEFI firmware details (vendor, version, date)
- **[29] baseboardInfo**: Motherboard manufacturer and model information
- **[30] chassisInfo**: Computer case/chassis details
- **[31] baseboardAssetTag**: Motherboard asset tracking identifier
- **[32] chassisAssetTag**: Chassis asset tracking identifier

### Connected Devices
- **[34] memoryModuleSerials**: RAM stick serial numbers (often empty on VMs)
- **[35] usbDeviceIds**: Connected USB devices (vendor:product IDs)
- **[36] audioDeviceIds**: Audio hardware (manufacturer:name pairs)

### Request Metadata
- **[14] requestId**: Unique ID for this specific data collection
- **[15] randomHash**: Random value generated each time
- **[12] checksum**: SHA256 hash of all other values combined
