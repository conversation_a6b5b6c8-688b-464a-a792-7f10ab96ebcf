load("@aspect_bazel_lib//lib:copy_to_bin.bzl", "copy_to_bin")

# it is not clear if anybody should actually depend on these files
# but for now prevent further spread

filegroup(
    name = "file-ext",
    srcs = ["augment_supported_extensions.json"],
    visibility = ["//services/api_proxy/client:__subpackages__"],
)

copy_to_bin(
    name = "augment_supported_extensions",
    srcs = ["augment_supported_extensions.json"],
    visibility = [
        "//clients/beachhead:__subpackages__",
        "//clients/vim/sidecar:__subpackages__",
        "//clients/vscode:__subpackages__",
    ],
)
