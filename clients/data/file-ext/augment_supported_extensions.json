[{"name": "C", "vscode_name": "c", "type": "programming", "extensions": [".c", ".cats", ".h", ".idc", ".w"]}, {"name": "C++", "vscode_name": "cpp", "type": "programming", "extensions": [".cpp", ".c++", ".cc", ".cp", ".cxx", ".h", ".h++", ".hh", ".hpp", ".hxx", ".inc", ".inl", ".ipp", ".tcc", ".tpp"]}, {"name": "Go", "vscode_name": "go", "type": "programming", "extensions": [".go"]}, {"name": "Java", "vscode_name": "java", "type": "programming", "extensions": [".java"]}, {"name": "JavaScript", "vscode_name": "javascript", "type": "programming", "extensions": [".js", "._js", ".bones", ".es", ".es6", ".frag", ".gs", ".jake", ".jsb", ".jscad", ".jsfl", ".jsm", ".jss", ".njs", ".pac", ".sjs", ".ssjs", ".sublime-build", ".sublime-commands", ".sublime-completions", ".sublime-keymap", ".sublime-macro", ".sublime-menu", ".sublime-mousemap", ".sublime-project", ".sublime-settings", ".sublime-theme", ".sublime-workspace", ".sublime_metrics", ".sublime_session", ".xsjs", ".xsjslib"]}, {"name": "Python", "vscode_name": "python", "type": "programming", "extensions": [".py", ".bzl", ".cgi", ".fcgi", ".gyp", ".lmi", ".pyde", ".pyp", ".pyt", ".pyw", ".rpy", ".tac", ".wsgi", ".xpy"]}]