// ESBuild plugin for ESM/CommonJS compatibility
export const esmShimPlugin = {
    name: 'esm-shim',
    setup(build) {
        // Inject compatibility shims
        build.onLoad({ filter: /\.js$/ }, async (args) => {
            const fs = require('fs');
            let contents = await fs.promises.readFile(args.path, 'utf8');
            
            // Only add shims to entry files or files that need them
            if (args.path.includes('cli.js') || args.path.includes('index.js')) {
                const shims = `
// ESM/CommonJS compatibility shims
if (typeof globalThis.__filename === 'undefined') {
    globalThis.__filename = import.meta.url.replace('file://', '');
}
if (typeof globalThis.__dirname === 'undefined') {
    globalThis.__dirname = new URL('.', import.meta.url).pathname;
}
if (typeof globalThis.require === 'undefined') {
    globalThis.require = (await import('module')).createRequire(import.meta.url);
}
`;
                contents = shims + contents;
            }
            
            return { contents, loader: 'js' };
        });
    }
};
