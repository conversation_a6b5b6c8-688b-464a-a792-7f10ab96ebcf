#!/usr/bin/env python3
"""Generate build-time information for the CLI."""

import os
import sys


def main():
    if len(sys.argv) != 2:
        print("Usage: generate-build-info.py <output-file>", file=sys.stderr)
        sys.exit(1)

    output_file = sys.argv[1]

    # Read stamp variables from bazel
    stamp_vars = {}
    try:
        with open("bazel-out/stable-status.txt", "r") as f:
            for line in f.readlines():
                try:
                    key, value = line.strip().split(" ", 1)
                    stamp_vars[key] = value
                except ValueError:
                    pass
    except FileNotFoundError:
        print("Stamp file not found. Assuming dev build.", file=sys.stderr)

    git_commit = stamp_vars.get("STABLE_GIT_COMMIT_SHORT", "unknown")
    git_dirty = stamp_vars.get("STABLE_GIT_DIRTY", "false")

    # Create the TypeScript file with build info
    content = f"""// This file is auto-generated. Do not edit manually.
export const BUILD_GIT_COMMIT = "{git_commit}";
export const BUILD_GIT_DIRTY = {git_dirty};
"""

    # Ensure output directory exists
    os.makedirs(os.path.dirname(output_file), exist_ok=True)

    with open(output_file, "w", encoding="utf-8") as f:
        f.write(content)

    dirty_suffix = "-dirty" if git_dirty == "true" else ""
    print(f"Generated build info: {git_commit}{dirty_suffix}")


if __name__ == "__main__":
    main()
