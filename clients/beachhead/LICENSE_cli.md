# License

## Augment CLI

Custom Proprietary License for Augment CLI

Copyright (c) 2025, Augment Computing, Inc.

Permission is hereby granted to use the Augment CLI (`augment.mjs`) solely for its intended purpose
as a command-line interface for Augment services.

### License Grant

Subject to maintaining an active subscription with Augment Computing, Inc. ("Augment"), you are
granted a limited, non-exclusive, revocable license to use the Augment CLI (augment.mjs) solely to
access Augment’s subscription-based services ("Augment Services").

### Subscription Requirement

Use of the CLI requires an active subscription. If your subscription ends, your right to use the CLI
automatically terminates.

### Restrictions

1. Redistribution:
   Redistribution of the CLI, in part or in whole, is prohibited without prior written permission
   from Augment.

2. Modification:
   The CLI must not be reverse-engineered, decompiled, or modified.

3. Exclusive Use:
   The CLI may not be embed or integrate the into commercially sold or distributed software or
   services without explicit permission. Internal scripting and automation are permitted.

4. Commercial Use:
   Commercial use of the CLI requires a separate licensing agreement.

5. Distribution:
   The CLI is distributed exclusively through authorized distribution channels, *specifically
   Augment’s official npm registry*.

6. Intellectual Property
   Augment retains all intellectual property rights to the CLI. No rights other than those
   explicitly stated here are granted.

### Disclaimer

THE CLI IS PROVIDED "AS IS," WITHOUT WARRANTIES OF ANY KIND, EXPRESS OR IMPLIED. AUGMENT DISCLAIMS
ALL WARRANTIES, INCLUDING MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE, AND NON-INFRINGEMENT."

### Limitation of Liability

IN NO EVENT SHALL AUGMENT BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, CONSEQUENTIAL, OR SPECIAL
DAMAGES ARISING OUT OF OR IN CONNECTION WITH THE CLI OR ITS USE.

For licensing inquiries, <NAME_EMAIL>.
