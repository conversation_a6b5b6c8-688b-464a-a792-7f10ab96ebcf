# Augment CLI Tool

The Augment CLI tool is a command-line interface for interacting with the Augment Code platform.

## Building

### Using Bazel (Recommended)

```bash
# Build and Run the development bundle (extra -- to separate cli flags from bazel flags)
bazel run //:agent-cli -- --help

# Build production JavaScript bundle
bazel build //clients/beachhead:cli

# Build development JavaScript bundle with sourcemaps
bazel build //clients/beachhead:cli_dev
```

### Using npm/pnpm

```bash
# Install dependencies
pnpm install

# Build production bundle
pnpm build

# Build development bundle
pnpm build:dev
```

## Packaging

**JavaScript bundles** (`//clients/beachhead:cli`) - Require Node.js, output: `bazel-bin/clients/beachhead/out/augment.mjs`

## Testing

```bash
# Run tests
bazel test //clients/beachhead:test

# Manual testing with development build
bazel build //clients/beachhead:cli_dev
./bazel-bin/clients/beachhead/out/augment_dev.js --help
```
