/**
 * This file contains types shared/used by webviews.
 */

export class AugmentInstruction {
    public occuredAt: Date;

    constructor(
        public sessionId: string,
        public requestId: string,
        public suggestionId: string,
        public requestedAt: Date,
        public repoRoot: string,
        public pathName: string,
        public vscodeLanguageId: string,
        public modelName: string,
        public prompt: string,
        public prefix: string,
        public selectedText: string,
        public modifiedText: string,
        public suffix: string,
        public selectionStartLine: number,
        public selectionStartColumn: number,
        public selectionEndLine: number,
        public selectionEndColumn: number,
        public userRequested: boolean
    ) {
        this.occuredAt = new Date();
    }
}
