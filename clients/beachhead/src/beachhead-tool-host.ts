import { ShellProcessTools } from "@augment-internal/sidecar-libs/src/tools/sidecar-tools/shell-process-tools";
import { getDefaultShell } from "@augment-internal/sidecar-libs/src/tools/sidecar-tools/shell-utils";
import { IToolHost } from "@augment-internal/sidecar-libs/src/tools/tool-host";
import { ITool, ToolHostBase } from "@augment-internal/sidecar-libs/src/tools/tool-host-base";
import {
    LocalToolType,
    ShellConfig,
    ToolHostName,
    ToolHostOptions,
} from "@augment-internal/sidecar-libs/src/tools/tool-types";

import { AgentState } from "./agent_loop/state";
import {
    TerminalKillProcessTool,
    TerminalLaunchProcessTool,
    TerminalListProcessesTool,
    TerminalReadProcessTool,
    TerminalWriteProcessTool,
} from "./process-tools";
import { SetupScriptTool } from "./tools/setup-script-tool";
import { SubAgentTool } from "./tools/sub-agent-tool";

export function beachheadToolHostFactory(
    workspaceRoot: string,
    setupMode: boolean,
    enableSubAgent: boolean = false,
    isInteractive: boolean = true,
    isBeachhead: boolean = false,
    agentState: AgentState
) {
    return (_options: ToolHostOptions) => {
        return new BeachheadToolHost(
            workspaceRoot,
            setupMode,
            enableSubAgent,
            isInteractive,
            isBeachhead,
            agentState
        );
    };
}

/**
 * Manages local tools that run directly in the beachhead client environment.
 * This host is responsible for registering, configuring, and executing local tools
 * that don't require communication with the sidecar or remote backend.
 */
class BeachheadToolHost extends ToolHostBase<LocalToolType> {
    constructor(
        private readonly workspaceRoot: string,
        private readonly setupMode: boolean,
        private readonly enableSubAgent: boolean = false,
        private readonly isInteractive: boolean = false,
        private readonly isBeachhead: boolean = false,
        private readonly agentState?: AgentState
    ) {
        const tools: ITool<LocalToolType>[] = setupMode ? [new SetupScriptTool(workspaceRoot)] : [];

        if (!setupMode) {
            const shellInfo: ShellConfig = {
                name: getDefaultShell(process.platform),
                path: undefined,
                args: undefined,
                env: undefined,
                friendlyName: "exec",
            };
            const processTools = new ShellProcessTools(shellInfo);
            tools.push(new TerminalLaunchProcessTool(processTools));
            tools.push(new TerminalKillProcessTool(processTools));
            tools.push(new TerminalReadProcessTool(processTools));
            tools.push(new TerminalWriteProcessTool(processTools));
            tools.push(new TerminalListProcessesTool(processTools));

            if (enableSubAgent && agentState) {
                tools.push(new SubAgentTool(workspaceRoot, agentState, isInteractive, isBeachhead));
            }
        }

        super(tools, ToolHostName.localToolHost);
    }

    factory(_preconditionWait: Promise<void>): IToolHost {
        return new BeachheadToolHost(
            this.workspaceRoot,
            this.setupMode,
            this.enableSubAgent,
            this.isInteractive,
            this.isBeachhead,
            this.agentState
        );
    }
}
