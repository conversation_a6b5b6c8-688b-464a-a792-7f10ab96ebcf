import {
    Context,
    context,
    isValidSpanId,
    isValidTraceId,
    trace,
    TraceFlags,
    Tracer,
} from "@opentelemetry/api";
import { OTLPTraceExporter } from "@opentelemetry/exporter-trace-otlp-http";
import { defaultResource, resourceFromAttributes } from "@opentelemetry/resources";
import { BatchSpanProcessor } from "@opentelemetry/sdk-trace-base";
import { NodeTracerProvider } from "@opentelemetry/sdk-trace-node";
import { ATTR_SERVICE_NAME, ATTR_SERVICE_VERSION } from "@opentelemetry/semantic-conventions";

import { AugmentLogger, getLogger } from "./logging";

let globalOtelServiceName: string | undefined;

export function setupTracing(serviceName: string, serviceVersion: string) {
    const logger = getLogger("otel");

    logger.info("Setting up otel tracing for service %s", serviceName);
    globalOtelServiceName = serviceName;

    if (
        !process.env.OTEL_EXPORTER_OTLP_ENDPOINT &&
        !process.env.OTEL_EXPORTER_OTLP_TRACES_ENDPOINT
    ) {
        // getTracer() will return a no-op tracer if we don't set up a provider
        logger.info(
            "OTEL_EXPORTER_OTLP_ENDPOINT and OTEL_EXPORTER_OTLP_TRACES_ENDPOINT are not set, not setting up tracing"
        );
        return;
    }

    logger.info(
        "Setting up otel tracing, OTEL_EXPORTER_OTLP_ENDPOINT=%s OTEL_EXPORTER_OTLP_TRACES_ENDPOINT=%s",
        process.env.OTEL_EXPORTER_OTLP_ENDPOINT,
        process.env.OTEL_EXPORTER_OTLP_TRACES_ENDPOINT
    );

    const exporter = new OTLPTraceExporter();

    const resource = defaultResource().merge(
        resourceFromAttributes({
            [ATTR_SERVICE_NAME]: serviceName,
            [ATTR_SERVICE_VERSION]: serviceVersion,
        })
    );

    const provider = new NodeTracerProvider({
        resource: resource,
        spanProcessors: [new BatchSpanProcessor(exporter, {})],
    });

    provider.register();
    logger.info("Registered otel tracer provider");

    process.on("SIGTERM", () => {
        logger.info("SIGTERM signal received: closing tracer");
        shutdownHandler(provider, exporter).catch((err) => {
            logger.error("Error shutting down tracer", err);
        });
    });
    process.on("SIGINT", () => {
        logger.info("SIGINT signal received: closing tracer");
        shutdownHandler(provider, exporter).catch((err) => {
            logger.error("Error shutting down tracer", err);
        });
    });
}

export function getTracer(): Tracer {
    if (!globalOtelServiceName) {
        // This will return a no-op tracer, because no one has called setupTracing()
        return trace.getTracer("undefined");
    }
    return trace.getTracer(globalOtelServiceName);
}

export async function wrapInSpan<T>(spanName: string, promise: Promise<T>): Promise<T> {
    return getTracer().startActiveSpan(spanName, async (span) => {
        try {
            return await promise;
        } finally {
            span.end();
        }
    });
}

export async function wrapInSpanOrUndefined<T>(
    spanName: string,
    promise: Promise<T> | undefined
): Promise<T | undefined> {
    if (!promise) {
        return undefined;
    }
    return await wrapInSpan(spanName, promise);
}

async function shutdownHandler(provider: NodeTracerProvider, exporter: OTLPTraceExporter) {
    await Promise.all([provider.shutdown(), exporter.shutdown()]);
}

/**
 * Executes the provided callback with the parent context provided, if the
 * parent context is valid and relatively recent.
 *
 * The parent context is expected to have been provided by the API call that
 * kicked off the operation we want to trace. APIs that support this will
 * return the context as part of their response.
 *
 * Required fields in parentContext:
 * - trace_id
 * - span_id or parent_span_id
 * - timestamp
 *
 * @param parentContext The parent context to use
 * @param callback The callback to execute with the parent context
 */
export function withParentContext<T>(
    parentContext: Record<string, string> | undefined,
    callback: () => T
): T {
    const logger = getLogger("otel");

    let contextWithSpan: Context | undefined;
    try {
        contextWithSpan = withParentContextHelper(logger, parentContext);
    } catch (err) {
        logger.warn("Failed to set parent context, executing without parent context", err);
        contextWithSpan = undefined;
    }

    if (!contextWithSpan) {
        return callback();
    }
    return context.with(contextWithSpan, callback);
}

function withParentContextHelper(
    logger: AugmentLogger,
    parentContext: Record<string, string> | undefined
) {
    if (!parentContext) {
        logger.debug("No parent context provided, executing without parent context");
        return undefined;
    }

    // Extract trace context fields from the parent context
    const traceId = parentContext.trace_id;
    const spanId = parentContext.span_id || parentContext.parent_span_id;
    const rawTimestamp = parentContext.timestamp;
    const traceFlags = TraceFlags.SAMPLED;

    if (!traceId || !spanId || !rawTimestamp) {
        logger.warn(
            "Invalid parent context: missing trace_id, span_id/parent_span_id, or timestamp, executing without parent context"
        );
        return undefined;
    }

    // If the timestamp is too old, don't use it to avoid accidentally polluting
    // the wrong trace.
    const timestamp = new Date(rawTimestamp);
    const age = Date.now() - timestamp.getTime();
    const tenMinutes = 1000 * 60 * 10;
    if (age > tenMinutes) {
        logger.warn("Parent context timestamp is too old, executing without parent context");
        return undefined;
    }

    // Validate trace and span IDs
    if (!isValidTraceId(traceId) || !isValidSpanId(spanId)) {
        logger.warn(
            "Invalid parent context: invalid traceId or spanId format, executing without parent context"
        );
        return undefined;
    }

    // Create a span context object
    const spanContext = {
        traceId,
        spanId,
        traceFlags,
        traceState: undefined, // TraceState is optional and complex to parse from string
    };

    // Set the span context in the current context and execute the callback
    logger.debug(
        "Executing with parent context: traceId=%s, spanId=%s, traceFlags=%d",
        traceId,
        spanId,
        traceFlags
    );

    return trace.setSpanContext(context.active(), spanContext);
}
