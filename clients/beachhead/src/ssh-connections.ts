import { spawn } from "child_process";

import { FeatureFlags } from "./feature-flags";
import { getLogger } from "./logging";

export class SSHConnectionFinder {
    private _sshPort: number;
    private _featureFlags: FeatureFlags;
    private _activeConnections: string[];
    private logger = getLogger("SSHConnectionFinder");

    private _lastLogTime: number;
    private _lastLogCount: number;

    public constructor(sshPort: number, featureFlags: FeatureFlags) {
        this._sshPort = sshPort;
        this._featureFlags = featureFlags;
        this._activeConnections = [];

        this._lastLogTime = 0;
        this._lastLogCount = -1;

        this.logger.info(
            `SSHConnectionFinder created. sshPort: ${sshPort}, checkInterval: ${featureFlags.agentSshConnectionCheckIntervalMs}, logInterval: ${featureFlags.agentSshConnectionCheckLogIntervalMs}`
        );
    }

    public async startBackgroundMonitoring(): Promise<void> {
        // Start a background process that monitors for active SSH connections
        // and updates a local cache of active connections
        for (;;) {
            try {
                await this.runOneBackgroundCheck();
            } catch (err) {
                this.logger.error("Failed to run background check", err);
            }
            await new Promise((resolve) =>
                setTimeout(resolve, this._featureFlags.agentSshConnectionCheckIntervalMs)
            );
        }
    }

    public hasActiveSSHConnections(): boolean {
        return this._activeConnections.length > 0;
    }

    public async runOneBackgroundCheck(): Promise<void> {
        // Run a simple check for active SSH connections
        const result = await new Promise<string>((resolve, reject) => {
            const ssArgs = [`-HOtnp`, `state`, `connected`, `sport = :${this._sshPort}`];
            const child = spawn("ss", ssArgs, {
                stdio: ["ignore", "pipe", "inherit"],
            });
            child.on("error", (err: Error) => {
                throw new Error(`Failed to start process: ${err.message}`);
            });
            let output = "";
            child.stdout.on("data", (data) => {
                output += data;
            });
            child.on("close", (code) => {
                if (code === 0) {
                    resolve(output);
                } else {
                    reject(new Error(`ss exited with code ${code}`));
                }
            });
        });

        // Parse the output and update the active connections
        const lines = result.split("\n");
        const activeConnections: string[] = [];
        for (const line of lines) {
            if (line.trim() === "") {
                continue;
            }
            activeConnections.push(line);
        }

        const now = Date.now();
        if (
            activeConnections.length !== this._lastLogCount ||
            now - this._lastLogTime > this._featureFlags.agentSshConnectionCheckLogIntervalMs
        ) {
            this.logger.info(`Found ${activeConnections.length} active SSH connections`);
            this._lastLogTime = now;
            this._lastLogCount = activeConnections.length;
        }

        this._activeConnections = activeConnections;
    }
}
