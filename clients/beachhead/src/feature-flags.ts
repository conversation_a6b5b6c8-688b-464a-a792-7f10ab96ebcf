import cloneDeep from "lodash/cloneDeep";

import { getLogger } from "./logging";
import { MonitoredParameter } from "./monitored-parameter";
import * as vscode from "./vscode";

// Default max size for files to be uploaded. The backend is expected to
// override this through a feature flag.
const DEFAULT_MAX_UPLOAD_SIZE_BYTES: number = 128 * 1024;

/**
 * General fields an types in feature flags
 */
export interface FeatureFlags {
    gitDiff: boolean;
    gitDiffPollingFrequencyMSec: number;
    additionalChatModels: string;
    smallSyncThreshold: number;
    bigSyncThreshold: number;
    enableWorkspaceManagerUi: boolean;
    enableInstructions: boolean;
    enableSmartPaste: boolean;
    enableSmartPasteMinVersion: string;
    enableViewTextDocument: boolean;
    bypassLanguageFilter: boolean;
    enableHindsight: boolean;
    maxUploadSizeBytes: number;
    vscodeNextEditMinVersion: string;
    vscodeNextEditUx1MaxVersion: string;
    vscodeNextEditUx2MaxVersion: string;
    vscodeFlywheelMinVersion: string;
    vscodeExternalSourcesInChatMinVersion: string;
    vscodeShareMinVersion: string;
    maxTrackableFileCount: number;
    maxTrackableFileCountWithoutPermission: number;
    minUploadedPercentageWithoutPermission: number;
    vscodeSourcesMinVersion: string;
    vscodeChatHintDecorationMinVersion: string;
    nextEditDebounceMs: number;
    enableCompletionFileEditEvents: boolean;
    vscodeEnableCpuProfile: boolean;
    verifyFolderIsSourceRepo: boolean;
    refuseToSyncHomeDirectories: boolean;
    enableFileLimitsForSyncingPermission: boolean;
    enableChatMermaidDiagrams: boolean;
    enableSummaryTitles: boolean;
    // smartPastePrecomputeMode: SmartPastePrecomputeMode;
    vscodeNewThreadsMenuMinVersion: string;
    vscodeEditableHistoryMinVersion: string;
    vscodeEnableChatMermaidDiagramsMinVersion: string;
    userGuidelinesLengthLimit: number;
    workspaceGuidelinesLengthLimit: number;

    useCheckpointManagerContextMinVersion: string;
    validateCheckpointManagerContext: boolean;
    vscodeDesignSystemRichTextEditorMinVersion: string;
    vscodeChatWithToolsMinVersion: string;
    vscodeAgentModeMinVersion: string;
    vscodeAgentEditTool: string;
    memoriesParams: { [key: string]: string | number | boolean };
    agentEditToolMinViewSize: number;
    agentEditToolSchemaType: string;
    agentEditToolEnableFuzzyMatching: boolean;
    agentEditToolFuzzyMatchSuccessMessage: string;
    agentEditToolFuzzyMatchMaxDiff: number;
    agentEditToolFuzzyMatchMaxDiffRatio: number;
    agentEditToolFuzzyMatchMinAllMatchStreakBetweenDiffs: number;
    agentEditToolInstructionsReminder: boolean;
    agentEditToolShowResultSnippet: boolean;
    agentEditToolMaxLines: number;
    agentSaveFileToolInstructionsReminder: boolean;
    vscodeTaskListMinVersion: string;
    grepSearchToolEnable: boolean;
    grepSearchToolTimelimitSec: number;
    grepSearchToolOutputCharsLimit: number;
    grepSearchToolNumContextLines: number;
    agentReportStreamedChatEveryChunk: number;
    agentMaxTotalChangedFilesSizeBytes: number;
    agentMaxChangedFilesSkippedPaths: number;
    agentIdleStatusUpdateIntervalMs: number;
    agentMaxIterations: number;
    agentSshConnectionCheckIntervalMs: number;
    agentSshConnectionCheckLogIntervalMs: number;
    beachheadEnableSentry: boolean;
    beachheadErrorSamplingRate?: number;
    beachheadTraceSamplingRate?: number;
    historySummaryMinVersion: string;
    historySummaryParams: string;
    enableNewThreadsList: boolean;
    enableUntruncatedContentStorage: boolean;
    maxLinesTerminalProcessOutput: number;
    truncationFooterAdditionText: string;
    enableCommitIndexing: boolean;
    maxCommitsToIndex: number;
    enableAgentSwarmMode: boolean;
    enableSwarmMode: boolean;
    enableToolUseStateStorage: boolean;
    enableAgentGitTracker: boolean;
    beachheadEnableSubAgentTool: boolean;
    agentViewToolParams: string;
}

export const defaultFeatureFlags: FeatureFlags = {
    gitDiff: false,
    gitDiffPollingFrequencyMSec: 0,
    additionalChatModels: "",
    smallSyncThreshold: 15,
    bigSyncThreshold: 1000,
    enableWorkspaceManagerUi: false,
    enableInstructions: false,
    enableSmartPaste: false,
    enableSmartPasteMinVersion: "",
    enableViewTextDocument: false,
    bypassLanguageFilter: false,
    enableHindsight: false,
    maxUploadSizeBytes: DEFAULT_MAX_UPLOAD_SIZE_BYTES,
    vscodeNextEditMinVersion: "",
    vscodeNextEditUx1MaxVersion: "",
    vscodeNextEditUx2MaxVersion: "",
    vscodeFlywheelMinVersion: "",
    vscodeExternalSourcesInChatMinVersion: "",
    vscodeShareMinVersion: "",
    maxTrackableFileCount: 250_000,
    maxTrackableFileCountWithoutPermission: 150_000,
    minUploadedPercentageWithoutPermission: 90,
    vscodeSourcesMinVersion: "",
    vscodeChatHintDecorationMinVersion: "",
    nextEditDebounceMs: 400,
    enableCompletionFileEditEvents: false,
    vscodeEnableCpuProfile: false,
    verifyFolderIsSourceRepo: false,
    refuseToSyncHomeDirectories: false,
    enableFileLimitsForSyncingPermission: false,
    enableChatMermaidDiagrams: false,
    enableSummaryTitles: false,
    // smartPastePrecomputeMode: SmartPastePrecomputeMode.visibleHover,
    vscodeNewThreadsMenuMinVersion: "",
    vscodeEditableHistoryMinVersion: "",
    vscodeEnableChatMermaidDiagramsMinVersion: "",
    userGuidelinesLengthLimit: 2000,
    workspaceGuidelinesLengthLimit: 2000,

    useCheckpointManagerContextMinVersion: "",
    validateCheckpointManagerContext: false,
    vscodeDesignSystemRichTextEditorMinVersion: "",
    vscodeChatWithToolsMinVersion: "",
    vscodeAgentModeMinVersion: "",
    vscodeAgentEditTool: "str_replace_editor_tool",
    memoriesParams: {},
    agentEditToolMinViewSize: 0,
    agentEditToolSchemaType: "StrReplaceEditorToolDefinitionNested",
    agentEditToolEnableFuzzyMatching: false,
    agentEditToolFuzzyMatchSuccessMessage:
        "Replacement successful. old_str and new_str were slightly modified to match the original file content.",
    agentEditToolFuzzyMatchMaxDiff: 50,
    agentEditToolFuzzyMatchMaxDiffRatio: 0.15,
    agentEditToolFuzzyMatchMinAllMatchStreakBetweenDiffs: 5,
    agentEditToolInstructionsReminder: false,
    agentEditToolShowResultSnippet: true,
    agentEditToolMaxLines: 200,
    agentSaveFileToolInstructionsReminder: false,
    vscodeTaskListMinVersion: "",
    grepSearchToolEnable: false,
    grepSearchToolTimelimitSec: 10,
    grepSearchToolOutputCharsLimit: 5000,
    grepSearchToolNumContextLines: 5,
    agentReportStreamedChatEveryChunk: 3,
    agentMaxTotalChangedFilesSizeBytes: 2 * 1024 * 1024, // 2 MB
    agentMaxChangedFilesSkippedPaths: 1000,
    agentIdleStatusUpdateIntervalMs: 60 * 1000, // 1 minute
    agentMaxIterations: 200, // this is a safeguard: we also have a turn limit on the back end
    agentSshConnectionCheckIntervalMs: 5000,
    agentSshConnectionCheckLogIntervalMs: 5 * 60 * 1000, // 5 minutes
    beachheadEnableSentry: false,
    beachheadErrorSamplingRate: 0.0,
    beachheadTraceSamplingRate: 0.0,
    historySummaryMinVersion: "",
    historySummaryParams: "",
    enableNewThreadsList: false,
    enableUntruncatedContentStorage: false,
    maxLinesTerminalProcessOutput: 0,
    truncationFooterAdditionText: "",
    enableCommitIndexing: false,
    maxCommitsToIndex: 0,
    enableAgentSwarmMode: false,
    enableSwarmMode: false,
    enableToolUseStateStorage: false,
    enableAgentGitTracker: false,
    beachheadEnableSubAgentTool: false,
    agentViewToolParams: "",
};

export interface FeatureFlagChangeEvent {
    readonly previousFlags: Readonly<FeatureFlags>;
    readonly newFlags: Readonly<FeatureFlags>;
    readonly changedFlags: ReadonlyArray<string>;
}

// Represents a subscription to a change in feature flags.
// The subscription is canceled when the object is disposed.
export class FeatureFlagChangeSubscription implements vscode.Disposable {
    private _disposed = false;
    private readonly _currentFlags: FeatureFlags;

    constructor(
        currentFlags: FeatureFlags,
        private readonly _watchedFlags: ReadonlyArray<keyof FeatureFlags>,
        private readonly _callback: (event: FeatureFlagChangeEvent) => void
    ) {
        this._currentFlags = cloneDeep(currentFlags);
    }

    public get disposed(): boolean {
        return this._disposed;
    }

    public trigger(newFlags: Readonly<FeatureFlags>): void {
        if (this._disposed) {
            return;
        }
        // check if any of my watched flags changed
        const changedFlags: string[] = [];
        for (const flag of this._watchedFlags) {
            if (newFlags[flag] !== this._currentFlags[flag]) {
                changedFlags.push(flag);
            }
        }
        if (changedFlags.length > 0) {
            this._callback({
                previousFlags: this._currentFlags,
                newFlags,
                changedFlags,
            });
        }
    }

    // Cancel the subscription
    public dispose() {
        this._disposed = true;
    }
}

export interface IFeatureFlagManagerOptions {
    initialFlags?: FeatureFlags;
    fetcher?: (cancelToken: vscode.CancellationToken) => Promise<FeatureFlags | undefined>;
    refreshIntervalMSec?: number;
}

/**
 * FeatureFlagManager is a class that maintains the current values of feature
 * flags and allows other components to listen for feature flag changes.
 */
export class FeatureFlagManager implements vscode.Disposable {
    private _subscriptions: FeatureFlagChangeSubscription[] = [];
    // Note(rich): Timeout seems to be the correct type for modern NodeJS.
    private _refreshTimer?: NodeJS.Timeout;
    private _disposed: boolean = false;

    private _logger = getLogger("FeatureFlagManager");

    private _flags: MonitoredParameter<FeatureFlags>;

    constructor(options?: IFeatureFlagManagerOptions) {
        this._flags = new MonitoredParameter<FeatureFlags>("feature flags", this._logger);
        this._flags.update(options?.initialFlags ?? defaultFeatureFlags);
        this._setupRefreshTimer(options);
    }

    public get currentFlags(): Readonly<FeatureFlags> {
        if (this._disposed) {
            throw Error("FeatureFlagManager has been disposed");
        }
        return this._flags.value!;
    }

    // When using update() to update the flags, any changes will trigger
    // the corresponding listeners.
    public update(other: FeatureFlags): void {
        if (this._disposed) {
            throw Error("FeatureFlagManager has been disposed");
        }
        this._flags.update(other);
        this._subscriptions = this._subscriptions.filter((subscription) => !subscription.disposed);
        // Try and trigger each subscription
        for (const subscription of this._subscriptions) {
            subscription.trigger(other);
        }
    }

    public subscribe(
        watchedFlags: ReadonlyArray<keyof FeatureFlags>,
        callback: (event: FeatureFlagChangeEvent) => void
    ): FeatureFlagChangeSubscription {
        if (this._disposed) {
            throw Error("FeatureFlagManager has been disposed");
        }
        const subscription = new FeatureFlagChangeSubscription(
            this._flags.value!,
            watchedFlags,
            callback
        );
        this._subscriptions.push(subscription);
        return subscription;
    }

    private _setupRefreshTimer(options?: IFeatureFlagManagerOptions) {
        if (!options?.fetcher || !options?.refreshIntervalMSec) {
            return;
        }

        this._cleanupRefreshTimer();
        const cancellationToken: vscode.CancellationToken = new vscode.CancellationTokenSource()
            .token;
        const fetcher = options.fetcher;
        const refreshIntervalMSec: number = options.refreshIntervalMSec;

        // Wrap the callback with cancellation
        const callback = async (): Promise<void> => {
            const newFlags = await fetcher(cancellationToken);
            if (newFlags) {
                this.update(newFlags);
            } else if (cancellationToken.isCancellationRequested) {
                this._cleanupRefreshTimer();
            }
        };

        this._refreshTimer = setInterval(() => void callback(), refreshIntervalMSec);
    }

    private _cleanupRefreshTimer() {
        clearInterval(this._refreshTimer);
        this._refreshTimer = undefined;
    }

    public dispose() {
        if (this._disposed) {
            return;
        }
        this._subscriptions.forEach((subscription) => subscription.dispose());
        this._subscriptions = [];
        this._cleanupRefreshTimer();
        this._disposed = true;
    }
}
