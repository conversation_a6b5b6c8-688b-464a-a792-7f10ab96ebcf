import { Exchange } from "@augment-internal/sidecar-libs/src/chat/chat-types";
import {
    checkShellAllowlist,
    getShellAllowlist,
} from "@augment-internal/sidecar-libs/src/tools/sidecar-tools/shell-allowlist";
import { ShellProcessTools } from "@augment-internal/sidecar-libs/src/tools/sidecar-tools/shell-process-tools";
import { quote } from "@augment-internal/sidecar-libs/src/tools/sidecar-tools/shell-utils";
import {
    errorToolResponse,
    successToolResponse,
} from "@augment-internal/sidecar-libs/src/tools/sidecar-tools/tool-use-response";
import {
    LocalToolType,
    ToolBase,
    ToolSafety,
    ToolUseResponse,
} from "@augment-internal/sidecar-libs/src/tools/tool-types";

/**
 * A tool that launches a new process.
 */
export class TerminalLaunchProcessTool extends ToolBase<LocalToolType> {
    constructor(public readonly processTools: ShellProcessTools) {
        super(LocalToolType.launchProcess, ToolSafety.Check);
    }

    public readonly version = 2;

    public get description(): string {
        return `\
Launch a new process with a shell command. A process can be waiting (\`wait=true\`) or non-waiting (\`wait=false\`).

If \`wait=true\`, launches the process in an interactive terminal, and waits for the process to complete up to
\`max_wait_seconds\` seconds. If the process ends during this period, the tool call returns. If the timeout
expires, the process will continue running in the background but the tool call will return. You can then
interact with the process using the other process tools.

If \`wait=false\`, launches a background process in a separate terminal. This returns immediately, while the
process keeps running in the background.

Notes:
- Use \`wait=true\` processes when the command is expected to be short, or when you can't
proceed with your task until the process is complete. Use \`wait=false\` for processes that are
expected to run in the background, such as starting a server you'll need to interact with, or a
long-running process that does not need to complete before proceeding with the task.
- If this tool returns while the process is still running, you can continue to interact with the process
using the other available tools. You can wait for the process, read from it, write to it, kill it, etc.
- You can use this tool to interact with the user's local version control system. Do not use the
retrieval tool for that purpose.
- If there is a more specific tool available that can perform the function, use that tool instead of
this one.

The OS is ${process.platform}. The shell is '${this.processTools.shellName}'.`;
    }

    public readonly inputSchemaJson: string = JSON.stringify({
        /* eslint-disable @typescript-eslint/naming-convention */
        type: "object",
        properties: {
            command: {
                type: "string",
                description: "The shell command to execute.",
            },
            wait: {
                type: "boolean",
                description: "Whether to wait for the command to complete.",
            },
            max_wait_seconds: {
                type: "number",
                description: `Number of seconds to wait for the command to complete. Only relevant when wait=true. 10 minutes may be a good default: increase from there if needed.`,
            },
            cwd: {
                type: "string",
                description:
                    "Required parameter. Absolute path to the working directory for the command.",
            },
        },
        required: ["command", "wait", "max_wait_seconds", "cwd"],
        /* eslint-enable @typescript-eslint/naming-convention */
    });

    public checkToolCallSafe(toolInput: Record<string, any>): boolean {
        const allowlist = getShellAllowlist(process.platform, this.processTools.shellName);
        const command = toolInput.command as string;
        return checkShellAllowlist(allowlist, command, this.processTools.shellName);
    }

    public async call(
        toolInput: Record<string, any>,
        _chatHistory: Exchange[],
        abortSignal: AbortSignal,
        _toolUseId: string
    ): Promise<ToolUseResponse> {
        try {
            const wait = toolInput.wait as boolean;
            const maxWaitSeconds = toolInput.max_wait_seconds as number;
            const cwd = toolInput.cwd as string | undefined;

            // Wrap the tool command in `bash -l -c` so it's executed in a login shell. This is
            // needed to ensure the configuration from the setup script is loaded.
            const wrappedCommand = `bash -l -c ${quote([toolInput.command as string])}`;
            const pid = await this.processTools.launch(wrappedCommand, cwd, abortSignal);
            if (typeof pid === "string") {
                return errorToolResponse(pid);
            }

            if (!wait) {
                return successToolResponse(`Process launched with terminal ID ${pid}`);
            }

            // Wait for process to complete or timeout
            const result = await this.processTools.waitForProcess(pid, maxWaitSeconds, abortSignal);

            if (result.returnCode === null) {
                return successToolResponse(`\
Command may still be running. You can use read-process to get more output
and kill-process to terminate it if needed.
Terminal ID ${pid}
Output so far:
<output>
${result.output}
</output>`);
            }

            return {
                text: `\
Here are the results from executing the command.
<return-code>
${result.returnCode}
</return-code>
<output>
${result.output}
</output>`,
                isError: result.returnCode !== 0,
            };
        } catch (e: any) {
            // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
            return errorToolResponse(`Failed to launch process: ${e.message ?? ""}`);
        }
    }
}

/**
 * A tool that kills a terminal.
 * Note that this only kills terminals launched with the launch-process tool.
 */
export class TerminalKillProcessTool extends ToolBase<LocalToolType> {
    constructor(private readonly _processTools: ShellProcessTools) {
        super(LocalToolType.killProcess, ToolSafety.Safe);
    }

    public readonly description: string = "Kill a process by its terminal ID.";

    public readonly inputSchemaJson: string = JSON.stringify({
        /* eslint-disable @typescript-eslint/naming-convention */
        type: "object",
        properties: {
            terminal_id: {
                type: "integer",
                description: "Terminal ID to kill.",
            },
        },
        required: ["terminal_id"],
        /* eslint-enable @typescript-eslint/naming-convention */
    });

    public checkToolCallSafe(_toolInput: Record<string, any>): boolean {
        return true;
    }

    public async call(
        toolInput: Record<string, any>,
        _chatHistory: Exchange[],
        _abortSignal: AbortSignal,
        _toolUseId: string
    ): Promise<ToolUseResponse> {
        const id = toolInput.terminal_id as number;
        const output = await this._processTools.kill(id);
        if (output) {
            if (output.killed) {
                return successToolResponse(
                    `Terminal ${id} killed\n<output>${output.output}</output>`
                );
            } else {
                return successToolResponse(
                    `Terminal ${id} already exited\n<output>${output.output}</output>\n<return-code>${output.returnCode}</return-code>`
                );
            }
        } else {
            return errorToolResponse(`Terminal ${id} not found`);
        }
    }
}

/**
 * A tool that reads output from a terminal.
 * Note that this only reads from terminals launched with the launch-process tool.
 */
export class TerminalReadProcessTool extends ToolBase<LocalToolType> {
    constructor(private readonly _processTools: ShellProcessTools) {
        super(LocalToolType.readProcess, ToolSafety.Safe);
    }

    public readonly description: string = `\
Read output from a terminal.

If \`wait=true\` and the process has not yet completed, waits for the terminal to complete up to \`max_wait_seconds\` seconds before returning its output.

If \`wait=false\` or the process has already completed, returns immediately with the current output.`;

    public readonly inputSchemaJson: string = JSON.stringify({
        /* eslint-disable @typescript-eslint/naming-convention */
        type: "object",
        properties: {
            terminal_id: {
                type: "integer",
                description: "Terminal ID to read from.",
            },
            wait: {
                type: "boolean",
                description: "Whether to wait for the command to complete.",
            },
            max_wait_seconds: {
                type: "number",
                description: `Number of seconds to wait for the command to complete. Only relevant when wait=true. 1 minute may be a good default: increase from there if needed.`,
            },
        },
        required: ["terminal_id", "wait", "max_wait_seconds"],
        /* eslint-enable @typescript-eslint/naming-convention */
    });

    public checkToolCallSafe(_toolInput: Record<string, any>): boolean {
        return true;
    }

    public async call(
        toolInput: Record<string, any>,
        _chatHistory: Exchange[],
        _abortSignal: AbortSignal,
        _toolUseId: string
    ): Promise<ToolUseResponse> {
        const id = toolInput.terminal_id as number;
        const wait = toolInput.wait as boolean;
        const maxWaitSeconds = toolInput.max_wait_seconds as number;
        if (wait) {
            await this._processTools.waitForProcess(id, maxWaitSeconds, _abortSignal);
        }
        const output = await this._processTools.readOutput(id);
        if (!output) {
            return errorToolResponse(`Terminal ${id} not found`);
        }

        const status = output.returnCode !== null ? "completed" : "still running";
        let response = `\
Here is the output from terminal ${id} (status: ${status}):
<output>${output.output}</output>\n`;

        if (output.returnCode !== null) {
            response += `<return-code>\n${output.returnCode}\n</return-code>\n`;
        }

        return successToolResponse(response);
    }
}

/**
 * A tool that writes input to a terminal.
 * Note that this only writes to terminals launched with the launch-process tool.
 */
export class TerminalWriteProcessTool extends ToolBase<LocalToolType> {
    constructor(private readonly _processTools: ShellProcessTools) {
        super(LocalToolType.writeProcess, ToolSafety.Safe);
    }

    public readonly description: string = "Write input to a terminal.";

    public readonly inputSchemaJson: string = JSON.stringify({
        /* eslint-disable @typescript-eslint/naming-convention */
        type: "object",
        properties: {
            terminal_id: {
                type: "integer",
                description: "Terminal ID to write to.",
            },
            input_text: {
                type: "string",
                description: "Text to write to the process's stdin.",
            },
        },
        required: ["terminal_id", "input_text"],
        /* eslint-enable @typescript-eslint/naming-convention */
    });

    public checkToolCallSafe(_toolInput: Record<string, any>): boolean {
        return true;
    }

    public call(
        toolInput: Record<string, any>,
        _chatHistory: Exchange[],
        _abortSignal: AbortSignal,
        _toolUseId: string
    ): Promise<ToolUseResponse> {
        const id = toolInput.terminal_id as number;
        const inputText = toolInput.input_text as string;
        if (this._processTools.writeInput(id, inputText)) {
            return Promise.resolve(successToolResponse(`Input written to terminal ${id}`));
        } else {
            return Promise.resolve(errorToolResponse(`Terminal ${id} not found or write failed`));
        }
    }
}

/**
 * A tool that lists all known terminals and their states.
 * Note that this only lists terminals launched with the launch-process tool.
 */
export class TerminalListProcessesTool extends ToolBase<LocalToolType> {
    constructor(private readonly _processTools: ShellProcessTools) {
        super(LocalToolType.listProcesses, ToolSafety.Safe);
    }

    public readonly description: string = `List all known terminals created with the ${LocalToolType.launchProcess} tool and their states.`;

    public readonly inputSchemaJson: string = JSON.stringify({
        /* eslint-disable @typescript-eslint/naming-convention */
        type: "object",
        properties: {},
        required: [],
        /* eslint-enable @typescript-eslint/naming-convention */
    });

    public checkToolCallSafe(_toolInput: Record<string, any>): boolean {
        return true;
    }

    public call(
        _toolInput: Record<string, any>,
        _chatHistory: Exchange[],
        _abortSignal: AbortSignal,
        _toolUseId: string
    ): Promise<ToolUseResponse> {
        const processes = this._processTools.listProcesses();
        if (processes.length === 0) {
            return Promise.resolve(successToolResponse("No processes found"));
        }

        const lines = processes.map((proc) => {
            let status = proc.state;
            if (proc.returnCode !== null) {
                status += ` (return code: ${proc.returnCode})`;
            }
            return `Terminal ${proc.id} [${status}]: ${proc.command}`;
        });
        // List the number of running processes to make it clear to the model.
        const runningCount = processes.filter((proc) => proc.state === "running").length;
        if (runningCount === 1) {
            lines.push("\nThere is 1 process still running.");
        } else if (runningCount > 1) {
            lines.push(`\nThere are ${runningCount} processes still running.`);
        }

        return Promise.resolve(
            successToolResponse("Here are all known processes:\n\n" + lines.join("\n"))
        );
    }
}
