import { type AugmentLogger, setLibraryLogger } from "@augment-internal/sidecar-libs/src/logging";
import { createLogger, format, Logger, transports } from "winston";

export { type AugmentLogger } from "@augment-internal/sidecar-libs/src/logging";

let logger: Logger | undefined;
let logFilePath: string | undefined;
let logLevel: string = "info";

export function configureLogging(logFile?: string, level?: string): void {
    if (logger) {
        throw new Error(
            "Logging has already been initialized. Cannot configure after logger creation."
        );
    }
    logFilePath = logFile;
    logLevel = level || "info";
}

function singletonLogger(): Logger {
    if (logger) {
        return logger;
    }

    let transport;
    if (logFilePath && logFilePath !== "-") {
        // Use file transport if log file path is specified and not "-"
        transport = new transports.File({
            filename: logFilePath,
            level: logLevel,
        });
    } else {
        // Default to console transport (also used when logFilePath is "-")
        // Use stdout for all logs to avoid interfering with crash detection
        transport = new transports.Console({
            level: logLevel,
        });
    }

    logger = createLogger({
        level: logLevel,
        exitOnError: false,
        format: format.combine(
            format.timestamp({
                format: "YYYY-MM-DD HH:mm:ss.SSS",
            }),
            format.splat(),
            format.printf(
                (info) =>
                    `${String(info.timestamp)} [${String(info.level)}] '${String(info.prefix)}': ${String(info.message)}`
            )
        ),
        transports: transport,
    });
    setLibraryLogger(logger);
    return logger;
}

export function getLogger(prefix: string): AugmentLogger {
    return singletonLogger().child({ prefix });
}
