import type {
    ChatMode,
    ChatRequestNode,
    ChatResultNode,
    Exchange,
    MemoriesInfo,
    Rule,
} from "@augment-internal/sidecar-libs/src/chat/chat-types";
import type {
    RemoteToolId,
    ToolDefinitionWithSettings,
    ToolUseRequest,
    ToolUseResponse,
} from "@augment-internal/sidecar-libs/src/tools/tool-types";
import type { IQualifiedPathName } from "@augment-internal/sidecar-libs/src/workspace/workspace-types";
import { type languages } from "monaco-editor";

import type { ChatFeedback } from "../chat/chat-types";
import { UserGuidelinesState, WorkspaceGuidelinesState } from "../chat/guidelines-types";
import type { AugmentInstruction } from "../code-edit-types";
import type { CompletionFeedback } from "../completions/completion-types";
import type { AnyActionName } from "../main-panel/action-cards/types";
import type { AgentRequestEventData, AgentSessionEventData, ClientMetric } from "../metrics/types";
import { WebviewName } from "../metrics/types";
import type {
    IEditSuggestion,
    NextEditFeedback,
    NextEditResultInfo,
} from "../next-edit/next-edit-types";
import type { GitBranch } from "../remote-agent-manager/commit-ref-types";
import {
    ChangedFile,
    CommitRef,
    DiffExplanation,
    RemoteAgentChatRequestDetails,
    RemoteAgentDetails,
    RemoteAgentExchange,
    RemoteAgentStatus,
} from "../remote-agent-manager/types";
import type { Diagnostic, FileType, SelectedCodeDetails } from "../utils/types";
import type { PreferenceInput, PreferenceResult } from "../webview-panels/preference-panel-types";
import { ToolConfigInitData, ToolConfigSaveRequest } from "../webview-panels/settings-panel-types";
import type {
    ISourceFolderInfo,
    SyncingEnabledState,
    SyncingStatusEvent,
} from "../workspace/types";

export enum WebViewMessageType {
    asyncWrapper = "async-wrapper",

    historyLoaded = "history-loaded",
    historyInitialize = "history-initialize",
    completionRating = "completion-rating",
    completionRatingDone = "completion-rating-done",
    nextEditRating = "next-edit-rating",
    nextEditRatingDone = "next-edit-rating-done",
    completions = "completions",
    historyConfig = "history-config",
    copyRequestID = "copy-request-id-to-clipboard",
    openFile = "open-file",
    openAndEditFile = "open-and-edit-file",

    diffViewNotifyReinit = "diff-view-notify-reinit",
    diffViewLoaded = "diff-view-loaded",
    diffViewInitialize = "diff-view-initialize",
    diffViewResolveChunk = "diff-view-resolve-chunk",
    diffViewFetchPendingStream = "diff-view-fetch-pending-stream",
    diffViewDiffStreamStarted = "diff-view-diff-stream-started",
    diffViewDiffStreamChunk = "diff-view-diff-stream-chunk",
    diffViewDiffStreamEnded = "diff-view-diff-stream-ended",
    diffViewAcceptAllChunks = "diff-view-accept-all-chunks",
    diffViewAcceptFocusedChunk = "diff-view-accept-selected-chunk",
    diffViewRejectFocusedChunk = "diff-view-reject-focused-chunk",
    diffViewFocusPrevChunk = "diff-view-focus-prev-chunk",
    diffViewFocusNextChunk = "diff-view-focus-next-chunk",
    diffViewWindowFocusChange = "diff-view-window-focus-change",
    disposeDiffView = "dispose-diff-view",

    reportWebviewClientMetric = "report-webview-client-metric",
    reportAgentSessionEvent = "report-agent-session-event",
    reportAgentRequestEvent = "report-agent-request-event",

    openConfirmationModal = "open-confirmation-modal",
    confirmationModalResponse = "confirmation-modal-response",

    clientTools = "client-tools",
    currentlyOpenFiles = "currently-open-files",
    findFileRequest = "find-file-request",
    resolveFileRequest = "resolve-file-request",
    findFileResponse = "find-file-response",
    resolveFileResponse = "resolve-file-response",
    findRecentlyOpenedFilesRequest = "find-recently-opened-files",
    findRecentlyOpenedFilesResponse = "find-recently-opened-files-response",
    findFolderRequest = "find-folder-request",
    findFolderResponse = "find-folder-response",
    findExternalSourcesRequest = "find-external-sources-request",
    findExternalSourcesResponse = "find-external-sources-response",
    findSymbolRequest = "find-symbol-request",
    findSymbolRegexRequest = "find-symbol-regex-request",
    findSymbolResponse = "find-symbol-response",
    fileRangesSelected = "file-ranges-selected",
    getDiagnosticsRequest = "get-diagnostics-request",
    getDiagnosticsResponse = "get-diagnostics-response",
    resolveWorkspaceFileChunkRequest = "resolve-workspace-file-chunk",
    resolveWorkspaceFileChunkResponse = "resolve-workspace-file-chunk-response",
    sourceFoldersUpdated = "source-folders-updated",
    sourceFoldersSyncStatus = "source-folders-sync-status",
    syncEnabledState = "sync-enabled-state",
    shouldShowSummary = "should-show-summary",
    showAugmentPanel = "show-augment-panel",
    updateGuidelinesState = "update-guidelines-state",
    openGuidelines = "open-guidelines",
    updateUserGuidelines = "update-user-guidelines",

    chatSetCurrentConversation = "chat-set-current-conversation",
    chatAgentEditListHasUpdates = "chat-agent-edit-list-has-updates",
    chatGetAgentEditListRequest = "chat-get-agent-edit-list-request",
    chatAgentEditListResponse = "chat-agent-edit-list-response",
    getAgentEditChangesByRequestId = "getAgentEditChangesByRequestId",
    getAgentEditContentsByRequestId = "getAgentEditContentsByRequestId",
    chatAgentEditAcceptAll = "chat-agent-edit-accept-all",
    chatAgentEditRejectAll = "chat-agent-edit-reject-all",
    chatReviewAgentFile = "chat-review-agent-file",
    chatModeChanged = "chat-mode-changed",
    chatClearMetadata = "chat-clear-metadata",
    chatLoaded = "chat-loaded",
    chatInitialize = "chat-initialize",
    chatGetStreamRequest = "chat-get-stream-request",
    chatUserMessage = "chat-user-message",
    generateCommitMessage = "generate-commit-message",
    chatUserCancel = "chat-user-cancel",
    chatModelReply = "chat-model-reply",
    chatModelReplyFailed = "chat-model-reply-failed",
    chatInstructionMessage = "chat-instruction-message",
    chatInstructionModelReply = "chat-instruction-model-reply",
    chatCreateFile = "chat-create-file",
    chatSmartPaste = "chat-smart-paste",
    chatRating = "chat-rating",
    chatRatingDone = "chat-rating-done",
    chatStreamDone = "chat-stream-done",
    chatCancelSmartPastePreload = "chat-cancel-smart-paste-preload",
    runSlashCommand = "run-slash-command",
    callTool = "call-tool",
    callToolResponse = "call-tool-response",
    cancelToolRun = "cancel-tool-run",
    cancelToolRunResponse = "cancel-tool-run-response",
    toolCheckSafe = "check-safe",
    toolCheckSafeResponse = "check-safe-response",
    checkToolExists = "checkToolExists",
    checkToolExistsResponse = "checkToolExistsResponse",
    closeAllToolProcesses = "close-all-tool-processes",
    getToolCallCheckpoint = "get-tool-call-checkpoint",
    getToolCallCheckpointResponse = "get-tool-call-checkpoint-response",
    revertToTimestamp = "revert-to-timestamp",
    revertToTimestampResponse = "revert-to-timestamp-response",
    updateAditionalChatModels = "update-additional-chat-models",
    saveChat = "save-chat",
    saveChatDone = "save-chat-done",
    newThread = "new-thread",
    chatSaveImageRequest = "chat-save-image-request",
    chatSaveImageResponse = "chat-save-image-response",
    chatLoadImageRequest = "chat-load-image-request",
    chatLoadImageResponse = "chat-load-image-response",
    chatDeleteImageRequest = "chat-delete-image-request",
    chatDeleteImageResponse = "chat-delete-image-response",

    instructions = "instructions",

    nextEditDismiss = "next-edit-dismiss",
    nextEditLoaded = "next-edit-loaded",
    nextEditSuggestions = "next-edit-suggestions",
    nextEditSuggestionsAction = "next-edit-suggestions-action",
    nextEditRefreshStarted = "next-edit-refresh-started",
    nextEditRefreshFinished = "next-edit-refresh-finished",
    nextEditCancel = "next-edit-cancel",
    nextEditPreviewActive = "next-edit-preview-active",
    nextEditSuggestionsChanged = "next-edit-suggestions-changed",
    nextEditNextSuggestionChanged = "next-edit-next-suggestion-changed",
    nextEditOpenSuggestion = "next-edit-open-suggestion",
    nextEditToggleSuggestionTree = "next-edit-toggle-suggestion-tree",
    nextEditActiveSuggestionChanged = "next-edit-active-suggestion",
    nextEditPanelFocus = "next-edit-panel-focus",

    onboardingLoaded = "onboarding-loaded",
    onboardingUpdateState = "onboarding-update-state",
    onboardingSignIn = "onboarding-sign-in",
    usedChat = "used-chat",

    preferencePanelLoaded = "preference-panel-loaded",
    preferenceInit = "preference-init",
    preferenceResultMessage = "preference-result-message",
    preferenceNotify = "preference-notify",

    mainPanelDisplayApp = "main-panel-display-app",
    mainPanelLoaded = "main-panel-loaded",
    mainPanelActions = "main-panel-actions",
    mainPanelPerformAction = "main-panel-perform-action",
    usedSlashAction = "used-slash-action",

    readFileRequest = "read-file-request",
    readFileResponse = "read-file-response",

    wsContextGetChildrenRequest = "ws-context-get-children-request",
    wsContextGetChildrenResponse = "ws-context-get-children-response",
    wsContextGetSourceFoldersRequest = "ws-context-get-source-folders-request",
    wsContextGetSourceFoldersResponse = "ws-context-get-source-folders-response",
    wsContextAddMoreSourceFolders = "ws-context-add-more-source-folders",
    wsContextRemoveSourceFolder = "ws-context-remove-source-folder",
    wsContextSourceFoldersChanged = "ws-context-source-folders-changed",
    wsContextFolderContentsChanged = "ws-context-folder-contents-changed",
    wsContextUserRequestedRefresh = "ws-context-user-requested-refresh",

    augmentLink = "augment-link",

    openAgentCheckpoint = "open-agent-checkpoint",
    resetAgentOnboarding = "reset-agent-onboarding",
    empty = "empty",
    chatGetAgentOnboardingPromptRequest = "chat-get-agent-onboarding-prompt-request",
    chatGetAgentOnboardingPromptResponse = "chat-get-agent-onboarding-prompt-response",

    setRemoteAgentId = "set-remote-agent-id",
    remoteAgentManagerLoaded = "remote-agent-manager-loaded",
    remoteAgentManagerInitialize = "remote-agent-manager-initialize",
    getRemoteAgentOverviewsRequest = "get-remote-agent-overviews-request",
    getRemoteAgentOverviewsResponse = "get-remote-agent-overviews-response",
    getRemoteAgentChatHistoryRequest = "get-remote-agent-chat-history-request",
    getRemoteAgentChatHistoryResponse = "get-remote-agent-chat-history-response",
    createRemoteAgentRequest = "create-remote-agent-request",
    createRemoteAgentResponse = "create-remote-agent-response",
    openRemoteAgentManager = "open-remote-agent-manager",
    remoteAgentChatRequest = "remote-agent-chat-request",
    remoteAgentChatResponse = "remote-agent-chat-response",
    remoteAgentDeleteRequest = "remote-agent-delete-request",
    remoteAgentDeleteResponse = "remote-agent-delete-response",
    remoteAgentStopRequest = "remote-agent-stop-request",
    remoteAgentStopResponse = "remote-agent-stop-response",

    // Git reference messages for remote agent
    getGitBranchesRequest = "get-git-branches-request",
    getGitBranchesResponse = "get-git-branches-response",
    getWorkspaceDiffRequest = "get-workspace-diff-request",
    getWorkspaceDiffResponse = "get-workspace-diff-response",
    getRemoteUrlRequest = "get-remote-url-request",
    getRemoteUrlResponse = "get-remote-url-response",
    diffExplanationRequest = "get-diff-explanation-request",
    diffExplanationResponse = "get-diff-explanation-response",
    applyChangesRequest = "apply-changes-request",
    applyChangesResponse = "apply-changes-response",

    triggerInitialOrientation = "trigger-initial-orientation",

    // Agent auto mode approval
    checkAgentAutoModeApproval = "check-agent-auto-mode-approval",
    checkAgentAutoModeApprovalResponse = "check-agent-auto-mode-approval-response",
    setAgentAutoModeApproved = "set-agent-auto-mode-approved",

    toolConfigLoaded = "tool-config-loaded",
    toolConfigInitialize = "tool-config-initialize",
    toolConfigSave = "tool-config-save",
    toolConfigGetDefinitions = "tool-config-get-definitions",
    toolConfigDefinitionsResponse = "tool-config-definitions-response",
    toolConfigStartOAuth = "tool-config-start-oauth",
    toolConfigStartOAuthResponse = "tool-config-start-oauth-response",
    toolConfigRevokeAccess = "tool-config-revoke-access",
}

interface BasicWebViewMessage {
    type: WebViewMessageType;
}

interface DataWebViewMessage<T> extends BasicWebViewMessage {
    data: T;
}

export interface AsyncStreamCtx {
    isStreamComplete?: boolean;
    streamMsgIdx: number;
    streamNextRequestId: string;
}

export interface AsyncWebViewMessage<T extends BasicWebViewMessage> extends BasicWebViewMessage {
    type: WebViewMessageType.asyncWrapper;
    requestId: string;
    error: string | null;
    baseMsg: T | null;

    // Only populated for streams
    streamCtx?: AsyncStreamCtx | undefined;
}

interface HistoryLoadedMessage extends BasicWebViewMessage {
    type: WebViewMessageType.historyLoaded;
}

interface HistoryInitializeMessage extends DataWebViewMessage<HistoryInitializeMessageData> {
    type: WebViewMessageType.historyInitialize;
}

interface HistoryInitializeMessageData {
    completionRequests: HistoryCompletionRequest[];
    instructions: AugmentInstruction[];
    nextEdits: NextEditResultInfo[];
    config?: HistoryConfig;
}

export interface HistoryConfig {
    enableDebugFeatures: boolean;
    enableReviewerWorkflows: boolean;
}

// The HistoryCompletionRequest is a reduced version of the CompletionRequest
// type. This only includes the fields that are needed for the history webview
// and is a format that can be passed from the extension to the Webview.
export interface HistoryCompletionRequest {
    occuredAt: string; // This is not a Date object as it is not serializable.
    requestId: string;
    pathName: string;
    repoRoot: string;
    prefix: string;
    suffix: string;
    completions: HistoryAugmentCompletion[];
}

export interface HistoryAugmentCompletion {
    text: string;
    skippedSuffix: string;
    suffixReplacementText: string;
}

interface CompletionRatingMessage extends DataWebViewMessage<CompletionFeedback> {
    type: WebViewMessageType.completionRating;
}

interface CompletionRatingDoneMessage extends DataWebViewMessage<CompletionRatingDoneMessageData> {
    type: WebViewMessageType.completionRatingDone;
}

interface CompletionRatingDoneMessageData {
    success: boolean;
    requestId: string;
}

interface NextEditRatingMessage extends DataWebViewMessage<NextEditFeedback> {
    type: WebViewMessageType.nextEditRating;
}
export interface NextEditCurorWithinSuggestion
    extends DataWebViewMessage<IEditSuggestion | undefined> {
    type: WebViewMessageType.nextEditActiveSuggestionChanged;
}
export interface NextEditPanelFocusMessage extends BasicWebViewMessage {
    type: WebViewMessageType.nextEditPanelFocus;
}

interface NextEditRatingDoneMessage extends DataWebViewMessage<NextEditRatingDoneMessageData> {
    type: WebViewMessageType.nextEditRatingDone;
}

interface NextEditToggleSuggestionTreeMessage extends BasicWebViewMessage {
    type: WebViewMessageType.nextEditToggleSuggestionTree;
}

interface NextEditRatingDoneMessageData {
    success: boolean;
    requestId: string;
}

interface CompletionsMessage extends DataWebViewMessage<HistoryCompletionRequest[]> {
    type: WebViewMessageType.completions;
}

interface InstructionsMessage extends DataWebViewMessage<AugmentInstruction[]> {
    type: WebViewMessageType.instructions;
}

interface HistoryConfigMessage extends DataWebViewMessage<HistoryConfig> {
    type: WebViewMessageType.historyConfig;
}

interface CopyRequestIDToClipboardMessage extends DataWebViewMessage<string> {
    type: WebViewMessageType.copyRequestID;
}

export interface OpenFileMessageData extends FileDetails {
    allowOutOfWorkspace?: boolean;
}

export interface OpenFileMessage extends DataWebViewMessage<OpenFileMessageData> {
    type: WebViewMessageType.openFile;
}

export interface OpenAndEditFileMessage extends DataWebViewMessage<FileDetails> {
    type: WebViewMessageType.openAndEditFile;
}

export interface OpenConfirmationModalData {
    title: string;
    message: string;
    confirmButtonText: string;
    cancelButtonText: string;
}

export interface OpenConfirmationModal extends DataWebViewMessage<OpenConfirmationModalData> {
    type: WebViewMessageType.openConfirmationModal;
}

export interface ConfirmationModalResponseData {
    ok: boolean;
    error?: string;
}

export interface ConfirmationModalResponse
    extends DataWebViewMessage<ConfirmationModalResponseData> {
    type: WebViewMessageType.confirmationModalResponse;
}

export interface CheckAgentAutoModeApproval extends BasicWebViewMessage {
    type: WebViewMessageType.checkAgentAutoModeApproval;
}

export interface CheckAgentAutoModeApprovalResponse extends DataWebViewMessage<boolean> {
    type: WebViewMessageType.checkAgentAutoModeApprovalResponse;
}

export interface SetAgentAutoModeApproved extends DataWebViewMessage<boolean> {
    type: WebViewMessageType.setAgentAutoModeApproved;
}

export interface ChatLoaded extends BasicWebViewMessage {
    type: WebViewMessageType.chatLoaded;
}

export interface ChatClearMetadataData {
    requestIds?: string[];
    conversationIds?: string[];
    toolUseIds?: string[];
}

export interface ChatClearMetadata extends DataWebViewMessage<ChatClearMetadataData> {
    type: WebViewMessageType.chatClearMetadata;
}

export enum SmartPastePrecomputeMode {
    off = "off",
    visibleHover = "visible-hover",
    visible = "visible",
    on = "on",
}

export type UserTier = "unknown" | "community" | "professional" | "enterprise";

export interface ChatInitializeMessageData {
    // Flags
    enableEditableHistory?: boolean;
    enablePreferenceCollection?: boolean;
    enableRetrievalDataCollection?: boolean;
    enableDebugFeatures?: boolean;
    useRichTextHistory?: boolean;
    modelDisplayNameToId?: { [key: string]: string | null };
    fullFeatured?: boolean;
    enableExternalSourcesInChat?: boolean;
    smallSyncThreshold?: number;
    bigSyncThreshold?: number;
    enableSmartPaste?: boolean;
    summaryTitles?: boolean;
    suggestedEditsAvailable?: boolean;
    enableShareService?: boolean;
    maxTrackableFileCount?: number;
    enableDesignSystemRichTextEditor?: boolean;
    enableSources?: boolean;
    enableChatMermaidDiagrams?: boolean;
    smartPastePrecomputeMode?: SmartPastePrecomputeMode;
    useNewThreadsMenu?: boolean;
    enableChatMermaidDiagramsMinVersion?: boolean;
    idleNewSessionNotificationTimeoutMs?: number;
    idleNewSessionMessageTimeoutMs?: number;
    enableChatMultimodal?: boolean;
    enableAgentMode?: boolean;

    // Data
    agentMemoriesFilePathName?: IQualifiedPathName | undefined;
    currentChatMode?: ChatMode;

    userTier?: UserTier;
    retryChatStreamTimeouts?: boolean;
}

export interface ChatInitialize extends DataWebViewMessage<ChatInitializeMessageData> {
    type: WebViewMessageType.chatInitialize;
}

export interface ChatRequestId {
    requestId: string;
    lastChunkId?: number;
}

export interface ChatGetStreamRequest extends DataWebViewMessage<ChatRequestId> {
    type: WebViewMessageType.chatGetStreamRequest;
}

export interface ChatUserMessage extends DataWebViewMessage<ChatUserMessageData> {
    type: WebViewMessageType.chatUserMessage;
}

export interface GenerateCommitMessage extends BasicWebViewMessage {
    type: WebViewMessageType.generateCommitMessage;
}

export interface ChatUserCancel extends DataWebViewMessage<ChatRequestId> {
    type: WebViewMessageType.chatUserCancel;
}

export interface ChatAgentEditListHasUpdatesMessage extends BasicWebViewMessage {
    type: WebViewMessageType.chatAgentEditListHasUpdates;
}

export interface ChatGetAgentEditListRequest
    extends DataWebViewMessage<{
        fromTimestamp?: number;
        toTimestamp?: number;
    }> {
    type: WebViewMessageType.chatGetAgentEditListRequest;
}

export interface ChatAgentEditListData {
    edits: ChatAgentEdit[];
}

export interface ChatAgentFileChangeSummary {
    totalAddedLines: number;
    totalRemovedLines: number;

    /** Diff statistics for working directory → staging (unstaged changes) */
    unstagedChanges?: {
        addedLines: number;
        removedLines: number;
    };

    /** Diff statistics for staging → HEAD (staged changes) */
    stagedChanges?: {
        addedLines: number;
        removedLines: number;
    };
}

export interface ChatAgentEdit {
    qualifiedPathName: IQualifiedPathName;
    changesSummary?: ChatAgentFileChangeSummary;
}

export interface ChatAgentEditListResponse extends DataWebViewMessage<ChatAgentEditListData> {
    type: WebViewMessageType.chatAgentEditListResponse;
}

export interface GetAgentEditChangesByRequestIdRequest extends DataWebViewMessage<RequestIdData> {
    type: WebViewMessageType.getAgentEditChangesByRequestId;
}

export interface GetAgentEditChangesByRequestIdResponse
    extends DataWebViewMessage<ChatAgentFileChangeSummary | undefined> {
    type: WebViewMessageType.getAgentEditChangesByRequestId;
}

export interface GetAgentEditContentsByRequestIdRequest extends DataWebViewMessage<RequestIdData> {
    type: WebViewMessageType.getAgentEditContentsByRequestId;
}

export interface RequestIdData {
    requestId: string;
}

export interface GetAgentEditContentsByRequestIdResponse
    extends DataWebViewMessage<EditContentsData | undefined> {
    type: WebViewMessageType.getAgentEditContentsByRequestId;
}

export interface DiffExplanationRequest extends DataWebViewMessage<DiffExplanationRequestData> {
    type: WebViewMessageType.diffExplanationRequest;
}

export interface DiffExplanationRequestData {
    apikey?: string;
    changedFiles: ChangedFile[];
}

export interface DiffExplanationResponse extends DataWebViewMessage<DiffExplanationResponseData> {
    type: WebViewMessageType.diffExplanationResponse;
}

export interface DiffExplanationResponseData {
    explanation: DiffExplanation;
}

export interface EditContentsData {
    originalCode: string;
    modifiedCode: string;
}

export interface ChatAgentEditAcceptAll extends BasicWebViewMessage {
    type: WebViewMessageType.chatAgentEditAcceptAll;
}

export interface ChatAgentEditRejectAll extends BasicWebViewMessage {
    type: WebViewMessageType.chatAgentEditRejectAll;
}

export interface ChatSetCurrentConversation extends DataWebViewMessage<{ conversationId: string }> {
    type: WebViewMessageType.chatSetCurrentConversation;
}

export interface ChatReviewAgentFileData {
    qualifiedPathName: IQualifiedPathName;
    fromTimestamp?: number;
    toTimestamp?: number;
    retainFocus?: boolean; // If true, don't open the diff view
}

export interface ChatReviewAgentFileMessage extends DataWebViewMessage<ChatReviewAgentFileData> {
    type: WebViewMessageType.chatReviewAgentFile;
}

export interface IChatActiveContext {
    userSpecifiedFiles: IQualifiedPathName[];
    recentFiles?: IQualifiedPathName[];
    ruleFiles?: Rule[];
    externalSources?: ExternalSource[];
    selections?: FileDetails[];
    sourceFolders?: IQualifiedPathName[];
}

export interface ChatUserMessageData {
    text: string;
    chatHistory: Exchange[];
    modelId?: string | undefined;
    context?: IChatActiveContext;
    disableRetrieval?: boolean;
    disableSelectedCodeDetails?: boolean;
    // Here for backwards compatibility
    userSpecifiedFiles?: IQualifiedPathName[];
    externalSourceIds?: string[];
    nodes?: ChatRequestNode[];
    memoriesInfo?: MemoriesInfo;
    rules?: Rule[];
}

export interface ChatModelReply extends DataWebViewMessage<ChatModelReplyData> {
    type: WebViewMessageType.chatModelReply;
}

export interface ChatInstructionModelReply
    extends DataWebViewMessage<ChatInstructionModelReplyData> {
    type: WebViewMessageType.chatInstructionModelReply;
}

export interface WorkspaceFileChunk {
    charStart: number;
    charEnd: number;
    blobName: string;
    file?: FileDetails;
}

interface ChatModelReplyData {
    text: string;
    requestId: string;
    workspaceFileChunks: WorkspaceFileChunk[];
    streaming?: boolean;
    nodes?: ChatResultNode[];
    chunkId?: number;
}

interface ChatInstructionModelReplyData {
    text: string;
    requestId: string;
    replacementText: string | null;
    replacementStartLine: number | null;
    replacementEndLine: number | null;
}

export interface ReportAgentSessionEvent extends DataWebViewMessage<AgentSessionEventData> {
    type: WebViewMessageType.reportAgentSessionEvent;
}

export interface ReportAgentRequestEvent extends DataWebViewMessage<AgentRequestEventData> {
    type: WebViewMessageType.reportAgentRequestEvent;
}

export interface ChatModelReplyFailed extends DataWebViewMessage<ChatModelReplyData> {
    type: WebViewMessageType.chatModelReplyFailed;
    displayErrorMessage: string;
}

export interface ChatInstructionMessage extends DataWebViewMessage<ChatInstructionData> {
    type: WebViewMessageType.chatInstructionMessage;
}

export interface ChatInstructionData {
    instruction: string;
    selectedCodeDetails: SelectedCodeDetails;
}

export interface ChatCreateFileData {
    code: string;
    relPath?: string;
}

interface ChatCreateFile extends DataWebViewMessage<ChatCreateFileData> {
    type: WebViewMessageType.chatCreateFile;
}

export interface ChatSmartPasteData {
    generatedCode: string;
    chatHistory: Exchange[];
    targetFile?: string;
    options?: {
        dryRun?: boolean;
        preload?: boolean;
        requireFileConfirmation?: boolean;
    };
}

interface SaveChatConversationData {
    conversationId: string;
    chatHistory: Exchange[];
    title: string;
}

interface SaveChatResult {
    uuid: string;
    url: string;
}

export interface ChatCancelSmartPastePreload extends BasicWebViewMessage {
    type: WebViewMessageType.chatCancelSmartPastePreload;
}

export interface ChatSmartPaste extends DataWebViewMessage<ChatSmartPasteData> {
    type: WebViewMessageType.chatSmartPaste;
}

export interface ChatRatingMessage extends DataWebViewMessage<ChatFeedback> {
    type: WebViewMessageType.chatRating;
}

export interface ChatRatingDoneMessage extends DataWebViewMessage<ChatFeedback> {
    type: WebViewMessageType.chatRatingDone;
}

export interface RunSlashCommand extends DataWebViewMessage<string> {
    type: WebViewMessageType.runSlashCommand;
}

export interface CallToolMessage extends DataWebViewMessage<ToolUseRequest> {
    type: WebViewMessageType.callTool;
}

export interface CallToolResponse extends DataWebViewMessage<ToolUseResponse> {
    type: WebViewMessageType.callToolResponse;
}

export interface CancelToolRunMessage
    extends DataWebViewMessage<{ requestId: string; toolUseId: string }> {
    type: WebViewMessageType.cancelToolRun;
}

export interface CancelToolRunResponse extends BasicWebViewMessage {
    type: WebViewMessageType.cancelToolRunResponse;
}

export interface CheckToolExistsRequest extends BasicWebViewMessage {
    type: WebViewMessageType.checkToolExists;
    toolName: string;
}

export interface CheckToolExistsResponse extends BasicWebViewMessage {
    type: WebViewMessageType.checkToolExistsResponse;
    exists: boolean;
}

export interface CloseAllToolProcesses extends BasicWebViewMessage {
    type: WebViewMessageType.closeAllToolProcesses;
}

export interface GetToolCallCheckpoint extends DataWebViewMessage<RequestIdData> {
    type: WebViewMessageType.getToolCallCheckpoint;
}

export interface GetToolCallCheckpointResponse
    extends DataWebViewMessage<{ checkpointNumber: number | undefined }> {
    type: WebViewMessageType.getToolCallCheckpointResponse;
}

export interface RevertToTimestampRequestData {
    timestamp: number;
    // If specified, only revert these files
    qualifiedPathNames?: IQualifiedPathName[];
}

export interface RevertToTimestamp extends DataWebViewMessage<RevertToTimestampRequestData> {
    type: WebViewMessageType.revertToTimestamp;
}

export interface RevertToTimestampResponse extends BasicWebViewMessage {
    type: WebViewMessageType.revertToTimestampResponse;
}

export interface NewThread extends BasicWebViewMessage {
    type: WebViewMessageType.newThread;
}

export type ChatWebViewMessage =
    | ChatSetCurrentConversation
    | ChatLoaded
    | ChatClearMetadata
    | ChatInitialize
    | ChatGetStreamRequest
    | ChatUserMessage
    | GenerateCommitMessage
    | ChatUserCancel
    | ChatModelReply
    | ChatModelReplyFailed
    | ChatInstructionMessage
    | ChatInstructionModelReply
    | ChatCreateFile
    | ChatSmartPaste
    | ChatCancelSmartPastePreload
    | ChatRatingMessage
    | ChatRatingDoneMessage
    | ChatSaveImageRequest
    | ChatSaveImageResponse
    | ChatLoadImageRequest
    | ChatLoadImageResponse
    | ChatDeleteImageRequest
    | ChatDeleteImageResponse
    | RunSlashCommand
    | CallToolMessage
    | CallToolResponse
    | CancelToolRunMessage
    | CancelToolRunResponse
    | CheckToolExistsRequest
    | CheckToolExistsResponse
    | CloseAllToolProcesses
    | NewThread
    | ChatGetAgentEditListRequest
    | ChatAgentEditListResponse
    | ChatAgentEditAcceptAll
    | ChatAgentEditRejectAll
    | GetToolCallCheckpoint
    | GetToolCallCheckpointResponse
    | RevertToTimestamp
    | RevertToTimestampResponse
    | ReportAgentSessionEvent;

export interface SaveChatMessage extends DataWebViewMessage<SaveChatConversationData> {
    type: WebViewMessageType.saveChat;
}

export interface SaveChatDoneMessage extends DataWebViewMessage<SaveChatResult> {
    type: WebViewMessageType.saveChatDone;
}

export interface FileData {
    filename: string;
    data: Uint8Array;
}

export interface ChatSaveImageRequest extends DataWebViewMessage<FileData> {
    type: WebViewMessageType.chatSaveImageRequest;
}

export interface ChatSaveImageResponse extends DataWebViewMessage<string> {
    type: WebViewMessageType.chatSaveImageResponse;
}

export interface ChatLoadImageRequest extends DataWebViewMessage<string> {
    type: WebViewMessageType.chatLoadImageRequest;
}

export interface ChatLoadImageResponse extends DataWebViewMessage<Uint8Array | undefined> {
    type: WebViewMessageType.chatLoadImageResponse;
}

export interface ChatDeleteImageRequest extends DataWebViewMessage<string> {
    type: WebViewMessageType.chatDeleteImageRequest;
}

export interface ChatDeleteImageResponse extends BasicWebViewMessage {
    type: WebViewMessageType.chatDeleteImageResponse;
}

export interface CurrentlyOpenFiles extends DataWebViewMessage<FileDetails[]> {
    type: WebViewMessageType.currentlyOpenFiles;
}

export interface WebviewClientMetricData extends ClientMetric {
    webviewName: WebviewName;
}

export interface ReportWebviewClientMetricRequest
    extends DataWebViewMessage<WebviewClientMetricData> {
    type: WebViewMessageType.reportWebviewClientMetric;
}

export interface SourceFoldersUpdatedData {
    sourceFolders: ISourceFolderInfo[];
}

export interface SourceFoldersUpdated extends DataWebViewMessage<SourceFoldersUpdatedData> {
    type: WebViewMessageType.sourceFoldersUpdated;
}

export interface SourceFoldersSyncStatus extends DataWebViewMessage<SyncingStatusEvent> {
    type: WebViewMessageType.sourceFoldersSyncStatus;
}

export interface SyncEnabledStateUpdate extends DataWebViewMessage<SyncingEnabledState> {
    type: WebViewMessageType.syncEnabledState;
}

export interface GuidelinesStates {
    userGuidelines?: UserGuidelinesState;
    workspaceGuidelines?: WorkspaceGuidelinesState[];
}

export interface UpdateGuidelinesState extends DataWebViewMessage<GuidelinesStates> {
    type: WebViewMessageType.updateGuidelinesState;
}

export interface OpenGuidelines extends DataWebViewMessage<string> {
    type: WebViewMessageType.openGuidelines;
}

export interface UpdateUserGuidelines extends DataWebViewMessage<string> {
    type: WebViewMessageType.updateUserGuidelines;
}

export interface FileRangesSelected extends DataWebViewMessage<FileDetails[]> {
    type: WebViewMessageType.fileRangesSelected;
}

export interface ISearchScopeArgs {
    files: FileDetails[];
}

export interface FindFileRequestData extends IQualifiedPathName {
    exactMatch?: boolean;
    maxResults?: number;
    searchScope?: ISearchScopeArgs;
}

export interface FindFileRequest extends DataWebViewMessage<FindFileRequestData> {
    type: WebViewMessageType.findFileRequest;
}

export interface FindFileResponse extends DataWebViewMessage<FileDetails[]> {
    type: WebViewMessageType.findFileResponse;
}

export interface ResolveFileRequest extends DataWebViewMessage<FindFileRequestData> {
    type: WebViewMessageType.resolveFileRequest;
}

export interface ResolveFileResponse extends DataWebViewMessage<FileDetails | undefined> {
    type: WebViewMessageType.resolveFileResponse;
}

export interface FindRecentlyOpenedFilesRequest extends DataWebViewMessage<FindFileRequestData> {
    type: WebViewMessageType.findRecentlyOpenedFilesRequest;
}

export interface FindRecentlyOpenedFilesResponse extends DataWebViewMessage<FileDetails[]> {
    type: WebViewMessageType.findRecentlyOpenedFilesResponse;
}

export interface FindFolderRequestData extends IQualifiedPathName {
    exactMatch?: boolean;
    maxResults?: number;
}

export interface FindFolderRequest extends DataWebViewMessage<FindFolderRequestData> {
    type: WebViewMessageType.findFolderRequest;
}

export interface FindFolderResponse extends DataWebViewMessage<FileDetails[]> {
    type: WebViewMessageType.findFolderResponse;
}

export interface ExternalSource {
    id: string;
    name: string; // Short, human-readable name
    title: string; // Long, human-readable description
    // eslint-disable-next-line @typescript-eslint/naming-convention
    source_type: number;
}

export interface SearchExternalSourcesRequest {
    query: string;
    // eslint-disable-next-line @typescript-eslint/naming-convention
    source_types: string[];
}

export interface SearchExternalSourcesResponse {
    sources: ExternalSource[];
}

export interface FindExternalSourcesRequest
    extends DataWebViewMessage<SearchExternalSourcesRequest> {
    type: WebViewMessageType.findExternalSourcesRequest;
}

export interface FindExternalSourcesResponse
    extends DataWebViewMessage<SearchExternalSourcesResponse> {
    type: WebViewMessageType.findExternalSourcesResponse;
}

export interface FindSymbolRequestData {
    query: string;
    searchScope: ISearchScopeArgs;
}

export interface FindSymbolRequest extends DataWebViewMessage<FindSymbolRequestData> {
    type: WebViewMessageType.findSymbolRequest;
}

export interface FindSymbolRegexRequest extends DataWebViewMessage<FindSymbolRequestData> {
    type: WebViewMessageType.findSymbolRegexRequest;
}

export interface FindSymbolResponseData extends languages.DocumentSymbol {
    file: FileDetails;
}

export interface FindSymbolResponse extends DataWebViewMessage<FindSymbolResponseData[]> {
    type: WebViewMessageType.findSymbolResponse;
}

export interface GetDiagnosticsRequest extends BasicWebViewMessage {
    type: WebViewMessageType.getDiagnosticsRequest;
}

export interface GetDiagnosticsResponse extends DataWebViewMessage<Diagnostic[]> {
    type: WebViewMessageType.getDiagnosticsResponse;
}

export interface ResolveWorkspaceFileChunkRequest extends DataWebViewMessage<WorkspaceFileChunk> {
    type: WebViewMessageType.resolveWorkspaceFileChunkRequest;
}

export interface ResolveWorkspaceFileChunkResponse extends DataWebViewMessage<FileDetails> {
    type: WebViewMessageType.resolveWorkspaceFileChunkResponse;
}

interface NextEditDismissMessage extends BasicWebViewMessage {
    type: WebViewMessageType.nextEditDismiss;
}

interface NextEditLoadedMessage extends BasicWebViewMessage {
    type: WebViewMessageType.nextEditLoaded;
}

interface NextEditSuggestionsMessage extends DataWebViewMessage<NextEditResultInfo> {
    type: WebViewMessageType.nextEditSuggestions;
}

interface NextEditRefreshStarted extends DataWebViewMessage<string> {
    type: WebViewMessageType.nextEditRefreshStarted;
}

interface NextEditRefreshFinished extends BasicWebViewMessage {
    type: WebViewMessageType.nextEditRefreshFinished;
}

export interface NextEditSuggestionsActionMessage
    extends DataWebViewMessage<
        | { accept: IEditSuggestion }
        | { reject: IEditSuggestion }
        | { acceptAllInFile: IEditSuggestion[] }
        | { rejectAllInFile: IEditSuggestion[] }
        | { undoAllInFile: IEditSuggestion[] }
        | { undo: IEditSuggestion }
    > {
    type: WebViewMessageType.nextEditSuggestionsAction;
}

export interface NextEditCancelMessage extends BasicWebViewMessage {
    type: WebViewMessageType.nextEditCancel;
}

export interface NextEditSuggestionsChangedData {
    suggestions: IEditSuggestion[];
}

export interface NextEditPreviewActive extends DataWebViewMessage<IEditSuggestion> {
    type: WebViewMessageType.nextEditPreviewActive;
}

export interface NextEditSuggestionsChanged
    extends DataWebViewMessage<NextEditSuggestionsChangedData> {
    type: WebViewMessageType.nextEditSuggestionsChanged;
}

export interface NextEditFocusedSuggestionChanged
    extends DataWebViewMessage<IEditSuggestion | undefined> {
    type: WebViewMessageType.nextEditNextSuggestionChanged;
}

export interface NextEditOpenSuggestion extends DataWebViewMessage<IEditSuggestion> {
    type: WebViewMessageType.nextEditOpenSuggestion;
}
export interface ReadFileRequest extends DataWebViewMessage<{ pathName: IQualifiedPathName }> {
    type: WebViewMessageType.readFileRequest;
}

export interface ReadFileResponse extends DataWebViewMessage<IResolvedFileResult> {
    type: WebViewMessageType.readFileResponse;
}

export interface IResolvedFileContent {
    pathName: IQualifiedPathName;
    content: string;
}
export interface IResolvedFileError {
    pathName: IQualifiedPathName;
    error: string;
}
export type IResolvedFileResult = IResolvedFileContent | IResolvedFileError;

export type NextEditWebViewMessage =
    | NextEditRefreshStarted
    | NextEditCancelMessage
    | ReadFileRequest
    | ReadFileResponse
    | NextEditCurorWithinSuggestion
    | NextEditPanelFocusMessage;

export type NextEditVSCodeToWebViewMessage =
    | NextEditToggleSuggestionTreeMessage
    | NextEditPanelFocusMessage;

export interface FileDetails {
    repoRoot: string;
    pathName: string;
    fileType?: FileType;
    uriScheme?: string;
    // The display range of the file. Expressed with 1-indexing for historical reasons.
    // Only contains the start and end lines, 1-indexed.
    range?: {
        start: number;
        stop: number;
    };
    // These are the full range of the file, expressed with 0-indexing.
    // The full range includes the start and end lines, as well as the
    // start and end character columns.
    fullRange?: {
        startLineNumber: number;
        startColumn: number;
        endLineNumber: number;
        endColumn: number;
    };
    originalCode?: string;
    modifiedCode?: string;
    lineChanges?: LineChanges;
    // Whether actions on this file should be triggered from a different tab.
    differentTab?: boolean;
    requestId?: string;
    suggestionId?: string;
    // The snippet to highlight in the file.
    snippet?: string;
}

export interface IInstructionInitializeData {
    selection: {
        start: {
            line: number;
            character: number;
        };
        end: {
            line: number;
            character: number;
        };
    };
}

export interface DiffViewInitializeData {
    file: FileDetails;
    keybindings?: { [key: string]: string };
    instruction?: IInstructionInitializeData;
    editable?: boolean;
    disableResolution?: boolean;
    disableApply?: boolean;
}

export interface DiffViewFetchPendingStream extends BasicWebViewMessage {
    type: WebViewMessageType.diffViewFetchPendingStream;
}

export enum DiffViewResolveType {
    accept = "accept",
    reject = "reject",
}

export interface DiffViewResolveData {
    file: FileDetails;
    changes: FileDetails[];
    resolveType: DiffViewResolveType;
    shouldApplyToAll?: boolean; // User applied accept/reject to all chunks
}

export interface DiffViewResolveMessage extends DataWebViewMessage<DiffViewResolveData> {
    type: WebViewMessageType.diffViewResolveChunk;
}

export type DiffViewStreamMessage =
    | DiffViewDiffStreamStarted
    | DiffViewDiffStreamChunk
    | DiffViewDiffStreamEnded;

export interface DiffViewDiffStreamStarted
    extends DataWebViewMessage<{ requestId: string; streamId: string }> {
    type: WebViewMessageType.diffViewDiffStreamStarted;
}

export interface DiffViewDiffStreamChunkData {
    requestId?: string;
    streamId?: string;
    newChunkStart?: {
        // 1-indexed, [inclusive, exclusive)
        // Start line relative to original document from
        // start of the stream
        originalStartLine: number;
        // Start line relative to already-applied edits
        // from earlier in the stream
        stagedStartLine: number;
    };
    chunkContinue?: {
        newText: string;
    };
    chunkEnd?: {
        // 1-indexed, [inclusive, exclusive)
        // Start line relative to original document from
        // start of the stream
        originalStartLine: number;
        originalEndLine: number;
        // Start line relative to already-applied edits
        // from earlier in the stream
        stagedStartLine: number;
        stagedEndLine: number;
    };
}

export interface DiffViewDiffStreamChunk extends DataWebViewMessage<DiffViewDiffStreamChunkData> {
    type: WebViewMessageType.diffViewDiffStreamChunk;
}

export interface DiffViewDiffStreamEnded
    extends DataWebViewMessage<{ requestId: string; streamId: string }> {
    type: WebViewMessageType.diffViewDiffStreamEnded;
}

export interface DiffViewAcceptAllChunks extends BasicWebViewMessage {
    type: WebViewMessageType.diffViewAcceptAllChunks;
}

export interface DiffViewAcceptSelectedChunk extends BasicWebViewMessage {
    type: WebViewMessageType.diffViewAcceptFocusedChunk;
}

export interface DiffViewRejectFocusedChunk extends BasicWebViewMessage {
    type: WebViewMessageType.diffViewRejectFocusedChunk;
}

export interface DiffViewFocusPrevChunk extends BasicWebViewMessage {
    type: WebViewMessageType.diffViewFocusPrevChunk;
}

export interface DiffViewFocusNextChunk extends BasicWebViewMessage {
    type: WebViewMessageType.diffViewFocusNextChunk;
}

export interface DiffViewWindowFocusChange extends DataWebViewMessage<boolean> {
    type: WebViewMessageType.diffViewWindowFocusChange;
}

export interface ShouldShowSummary extends BasicWebViewMessage {
    type: WebViewMessageType.shouldShowSummary;
}

export interface ShowAugmentPanel extends BasicWebViewMessage {
    type: WebViewMessageType.showAugmentPanel;
}

export interface AbsPathFileDetails extends FileDetails {
    absPath: string;
}

export interface LineChanges {
    lineChanges: LineChange[];
    lineOffset: number;
}

export interface LineChange {
    originalStart: number;
    originalEnd: number;
    modifiedStart: number;
    modifiedEnd: number;
}

interface OnboardingLoaded extends BasicWebViewMessage {
    type: WebViewMessageType.onboardingLoaded;
}

interface OnboardingUpdateState extends DataWebViewMessage<OnboardingInializeData> {
    type: WebViewMessageType.onboardingUpdateState;
}

interface OnboardingInializeData {
    isLoggedIn: boolean | undefined;
}

interface OnboardingSignIn extends BasicWebViewMessage {
    type: WebViewMessageType.onboardingSignIn;
}

interface UsedChat extends BasicWebViewMessage {
    type: WebViewMessageType.usedChat;
}

interface PreferenceInit extends DataWebViewMessage<PreferenceInput> {
    type: WebViewMessageType.preferenceInit;
}

interface PreferenceResultMessage extends DataWebViewMessage<PreferenceResult> {
    type: WebViewMessageType.preferenceResultMessage;
}

interface PreferenceNotify extends DataWebViewMessage<string> {
    type: WebViewMessageType.preferenceNotify;
}

interface PreferencePanelLoaded extends BasicWebViewMessage {
    type: WebViewMessageType.preferencePanelLoaded;
}

export enum MainPanelApp {
    signIn = "sign-in",
    chat = "chat",
    workspaceContext = "workspace-context",
    awaitingSyncingPermission = "awaiting-syncing-permission",
    settings = "settings",
}

export interface MainPanelDisplayApp extends DataWebViewMessage<MainPanelApp | undefined> {
    type: WebViewMessageType.mainPanelDisplayApp;
}

interface MainPanelLoaded extends BasicWebViewMessage {
    type: WebViewMessageType.mainPanelLoaded;
}

interface MainPanelActions extends DataWebViewMessage<Array<AnyActionName>> {
    type: WebViewMessageType.mainPanelActions;
}

export interface MainPanelPerformAction extends DataWebViewMessage<string> {
    type: WebViewMessageType.mainPanelPerformAction;
}

export interface UsedSlashAction extends BasicWebViewMessage {
    type: WebViewMessageType.usedSlashAction;
}

export interface DiffViewLoadedMessage extends BasicWebViewMessage {
    type: WebViewMessageType.diffViewLoaded;
}

export interface DisposeDiffViewMessage extends BasicWebViewMessage {
    type: WebViewMessageType.disposeDiffView;
}

export interface DiffViewInitializeMessage extends DataWebViewMessage<DiffViewInitializeData> {
    type: WebViewMessageType.diffViewInitialize;
}

export interface DiffViewNotifyReinitMessage extends DataWebViewMessage<DiffViewInitializeData> {
    type: WebViewMessageType.diffViewNotifyReinit;
}

export interface TriggerInitialOrientation extends BasicWebViewMessage {
    type: WebViewMessageType.triggerInitialOrientation;
}

export interface EmptyMessage extends BasicWebViewMessage {
    type: WebViewMessageType.empty;
}

export enum WSContextFileInclusionState {
    included = "included",
    excluded = "excluded",
    partial = "partial",
}

export interface WSContextSourceFolder {
    name: string;
    fileId: WSContextFileItemId;
    inclusionState: WSContextFileInclusionState;
    isWorkspaceFolder: boolean;
    isNestedFolder: boolean;
    isPending: boolean;
    trackedFileCount?: number;
}

export interface WSContextFileItemId {
    folderRoot: string;
    relPath: string;
}

export interface WSContextFileItem {
    name: string;
    fileId: WSContextFileItemId;
    type: "file" | "folder";
    inclusionState: WSContextFileInclusionState;
    reason: string;
    trackedFileCount?: number;
}

export interface WSContextGetChildrenRequestData {
    fileId: WSContextFileItemId;
}

export interface WSContextGetChildrenResponseData {
    children: WSContextFileItem[];
}

export interface WSContextGetChildrenRequest
    extends DataWebViewMessage<WSContextGetChildrenRequestData> {
    type: WebViewMessageType.wsContextGetChildrenRequest;
}

export interface WSContextGetChildrenResponse
    extends DataWebViewMessage<WSContextGetChildrenResponseData> {
    type: WebViewMessageType.wsContextGetChildrenResponse;
}

export interface WSContextGetSourceFoldersRequest extends BasicWebViewMessage {
    type: WebViewMessageType.wsContextGetSourceFoldersRequest;
}

export interface WSContextGetSourceFoldersResponseData {
    workspaceFolders: WSContextSourceFolder[];
}

export interface WSContextGetSourceFoldersResponse
    extends DataWebViewMessage<WSContextGetSourceFoldersResponseData> {
    type: WebViewMessageType.wsContextGetSourceFoldersResponse;
}

export interface WSContextAddMoreSourceFolders extends BasicWebViewMessage {
    type: WebViewMessageType.wsContextAddMoreSourceFolders;
}

export interface WSContextRemoveSourceFolder extends DataWebViewMessage<string> {
    type: WebViewMessageType.wsContextRemoveSourceFolder;
}

export interface WSContextSourceFoldersChanged extends BasicWebViewMessage {
    type: WebViewMessageType.wsContextSourceFoldersChanged;
}

export interface WSContextFolderContentsChanged extends DataWebViewMessage<string> {
    type: WebViewMessageType.wsContextFolderContentsChanged;
}

export interface WSContextUserRequestedRefresh extends BasicWebViewMessage {
    type: WebViewMessageType.wsContextUserRequestedRefresh;
}

export interface AugmentCommand extends DataWebViewMessage<string> {
    type: WebViewMessageType.augmentLink;
}

export interface ChatModeChangedMessage
    extends DataWebViewMessage<{
        mode: ChatMode;
    }> {
    type: WebViewMessageType.chatModeChanged;
}

export interface ChatGetAgentOnboardingPromptRequest extends DataWebViewMessage<{}> {
    type: WebViewMessageType.chatGetAgentOnboardingPromptRequest;
}

export interface ChatGetAgentOnboardingPromptResponse
    extends DataWebViewMessage<{ prompt: string }> {
    type: WebViewMessageType.chatGetAgentOnboardingPromptResponse;
}

export interface OpenAgentCheckpointData {
    requestId: string;
    retainFocus?: boolean;
}

export interface OpenAgentCheckpoint extends DataWebViewMessage<OpenAgentCheckpointData> {
    type: WebViewMessageType.openAgentCheckpoint;
}

export interface ResetAgentOnboardingMessage extends BasicWebViewMessage {
    type: WebViewMessageType.resetAgentOnboarding;
}

////////////// Remote Agents //////////////
export interface SetRemoteAgentIdMessage
    extends DataWebViewMessage<{ agentId: string | undefined }> {
    type: WebViewMessageType.setRemoteAgentId;
}

export interface RemoteAgentManagerLoadedMessage extends BasicWebViewMessage {
    type: WebViewMessageType.remoteAgentManagerLoaded;
}

export interface DiffExplanationRequestMessage
    extends DataWebViewMessage<DiffExplanationRequestData> {
    type: WebViewMessageType.diffExplanationRequest;
}

export interface DiffExplanationResponseMessage
    extends DataWebViewMessage<DiffExplanationResponseData> {
    type: WebViewMessageType.diffExplanationResponse;
}

export interface RemoteAgentManagerInitializeMessage
    extends DataWebViewMessage<AgentManagerInitializeData> {
    type: WebViewMessageType.remoteAgentManagerInitialize;
}

export interface ApplyChangesRequestMessage extends DataWebViewMessage<ApplyChangesRequestData> {
    type: WebViewMessageType.applyChangesRequest;
}

export interface ApplyChangesResponseMessage extends DataWebViewMessage<ApplyChangesResponseData> {
    type: WebViewMessageType.applyChangesResponse;
}

export interface ApplyChangesRequestData {
    path: string;
    originalCode: string;
    newCode: string;
}

export interface ApplyChangesResponseData {
    success: boolean;
    error?: string;
}

export interface AgentManagerInitializeData {
    // TODO
}

export interface GetRemoteAgentOverviewsRequest extends BasicWebViewMessage {
    type: WebViewMessageType.getRemoteAgentOverviewsRequest;
}

export interface GetRemoteAgentOverviewsResponse extends DataWebViewMessage<AgentOverviewsData> {
    type: WebViewMessageType.getRemoteAgentOverviewsResponse;
}

export interface AgentOverviewsData {
    overviews: RemoteAgentDetails[];
}

export interface CreateRemoteAgentData {
    prompt: string;
    commitRef: CommitRef;
}

export interface GetRemoteAgentChatHistoryRequest extends DataWebViewMessage<{ agentId: string }> {
    type: WebViewMessageType.getRemoteAgentChatHistoryRequest;
}

export interface GetRemoteAgentChatHistoryResponse
    extends DataWebViewMessage<{ chatHistory: RemoteAgentExchange[] }> {
    type: WebViewMessageType.getRemoteAgentChatHistoryResponse;
}

export interface CreateRemoteAgentRequestMessage extends DataWebViewMessage<CreateRemoteAgentData> {
    type: WebViewMessageType.createRemoteAgentRequest;
}

export interface CreateRemoteAgentResponseMessage
    extends DataWebViewMessage<{
        success: boolean;
        agentId?: string;
        error?: string;
    }> {
    type: WebViewMessageType.createRemoteAgentResponse;
}

export interface OpenRemoteAgentManagerMessage extends DataWebViewMessage<{ agentId: string }> {
    type: WebViewMessageType.openRemoteAgentManager;
}

export interface RemoteAgentChatRequestMessage
    extends DataWebViewMessage<{ agentId: string; requestDetails: RemoteAgentChatRequestDetails }> {
    type: WebViewMessageType.remoteAgentChatRequest;
}

export interface RemoteAgentChatResponseMessage extends DataWebViewMessage<ChatResultNode[]> {
    type: WebViewMessageType.remoteAgentChatResponse;
}

export interface RemoteAgentDeleteRequestMessage extends DataWebViewMessage<{ agentId: string }> {
    type: WebViewMessageType.remoteAgentDeleteRequest;
}

export interface RemoteAgentDeleteResponseMessage extends DataWebViewMessage<RemoteAgentStatus> {
    type: WebViewMessageType.remoteAgentDeleteResponse;
}

export interface RemoteAgentStopRequestMessage extends DataWebViewMessage<{ agentId: string }> {
    type: WebViewMessageType.remoteAgentStopRequest;
}

export interface RemoteAgentStopResponseMessage extends DataWebViewMessage<RemoteAgentStatus> {
    type: WebViewMessageType.remoteAgentStopResponse;
}

////////////// Git Reference Messages //////////////
export interface GetGitBranchesRequestMessage
    extends DataWebViewMessage<{ prefix?: string; limit?: number }> {
    type: WebViewMessageType.getGitBranchesRequest;
}

export interface GetGitBranchesResponseMessage
    extends DataWebViewMessage<{
        branches: GitBranch[];
    }> {
    type: WebViewMessageType.getGitBranchesResponse;
}

export interface GetRemoteUrlRequestMessage extends BasicWebViewMessage {
    type: WebViewMessageType.getRemoteUrlRequest;
}

export interface GetRemoteUrlResponseMessage
    extends DataWebViewMessage<{
        remoteUrl: string;
    }> {
    type: WebViewMessageType.getRemoteUrlResponse;
}

export interface GetWorkspaceDiffRequestMessage
    extends DataWebViewMessage<{
        branchName: string;
    }> {
    type: WebViewMessageType.getWorkspaceDiffRequest;
}

export interface GetWorkspaceDiffResponseMessage
    extends DataWebViewMessage<{
        diff: string;
    }> {
    type: WebViewMessageType.getWorkspaceDiffResponse;
}

export interface ToolConfigLoaded extends BasicWebViewMessage {
    type: WebViewMessageType.toolConfigLoaded;
}

export interface ToolConfigInitialize extends DataWebViewMessage<ToolConfigInitData> {
    type: WebViewMessageType.toolConfigInitialize;
}

export interface ToolConfigSave extends DataWebViewMessage<ToolConfigSaveRequest> {
    type: WebViewMessageType.toolConfigSave;
}

export interface ToolConfigGetDefinitions extends BasicWebViewMessage {
    type: WebViewMessageType.toolConfigGetDefinitions;
}

export interface ToolConfigDefinitionsResponse
    extends DataWebViewMessage<{
        hostTools: ToolDefinitionWithSettings[];
    }> {
    type: WebViewMessageType.toolConfigDefinitionsResponse;
}

export interface ToolConfigStartOAuthRequest {
    authUrl: string;
}

export interface ToolConfigStartOAuth extends DataWebViewMessage<ToolConfigStartOAuthRequest> {
    type: WebViewMessageType.toolConfigStartOAuth;
}

export interface ToolConfigStartOAuthResponse extends DataWebViewMessage<{ ok: boolean }> {
    type: WebViewMessageType.toolConfigStartOAuthResponse;
}

export interface ToolConfigRevokeAccessRequest {
    toolId: RemoteToolId;
}

export interface ToolConfigRevokeAccess extends DataWebViewMessage<ToolConfigRevokeAccessRequest> {
    type: WebViewMessageType.toolConfigRevokeAccess;
}

export type WebViewMessage =
    | HistoryLoadedMessage
    | HistoryInitializeMessage
    | HistoryConfigMessage
    | CopyRequestIDToClipboardMessage
    | OpenFileMessage
    | OpenAndEditFileMessage
    | OpenConfirmationModal
    | ConfirmationModalResponse
    | CompletionRatingMessage
    | CompletionRatingDoneMessage
    | NextEditRatingMessage
    | NextEditRatingDoneMessage
    | NextEditCurorWithinSuggestion
    | NextEditPanelFocusMessage
    | CompletionsMessage
    | CurrentlyOpenFiles
    | FindFileRequest
    | FindFileResponse
    | ResolveFileRequest
    | ResolveFileResponse
    | FindRecentlyOpenedFilesRequest
    | FindRecentlyOpenedFilesResponse
    | FindFolderRequest
    | FindFolderResponse
    | FindExternalSourcesRequest
    | FindExternalSourcesResponse
    | FindSymbolRequest
    | FindSymbolRegexRequest
    | FindSymbolResponse
    | FileRangesSelected
    | GetDiagnosticsRequest
    | GetDiagnosticsResponse
    | ResolveWorkspaceFileChunkRequest
    | ResolveWorkspaceFileChunkResponse
    | SourceFoldersUpdated
    | SourceFoldersSyncStatus
    | InstructionsMessage
    | ReportWebviewClientMetricRequest
    | ReportAgentSessionEvent
    | ReportAgentRequestEvent
    | ChatLoaded
    | ChatClearMetadata
    | ChatInitialize
    | ChatGetStreamRequest
    | ChatUserMessage
    | GenerateCommitMessage
    | ChatUserCancel
    | ChatModelReply
    | ChatModelReplyFailed
    | ChatInstructionMessage
    | ChatInstructionModelReply
    | ChatCreateFile
    | ChatSmartPaste
    | ChatCancelSmartPastePreload
    | ChatRatingMessage
    | ChatRatingDoneMessage
    | ChatSaveImageRequest
    | ChatSaveImageResponse
    | ChatLoadImageRequest
    | ChatLoadImageResponse
    | ChatDeleteImageRequest
    | ChatDeleteImageResponse
    | ChatSetCurrentConversation
    | RunSlashCommand
    | CallToolMessage
    | CallToolResponse
    | CancelToolRunMessage
    | CancelToolRunResponse
    | CheckToolExistsRequest
    | CheckToolExistsResponse
    | CloseAllToolProcesses
    | GetToolCallCheckpoint
    | GetToolCallCheckpointResponse
    | RevertToTimestamp
    | RevertToTimestampResponse
    | NewThread
    | DiffViewLoadedMessage
    | DiffViewFetchPendingStream
    | DiffViewInitializeMessage
    | DiffViewNotifyReinitMessage
    | DiffViewResolveMessage
    | DiffViewDiffStreamStarted
    | DiffViewDiffStreamChunk
    | DiffViewDiffStreamEnded
    | DiffViewAcceptAllChunks
    | DiffViewAcceptSelectedChunk
    | DiffViewRejectFocusedChunk
    | DiffViewFocusPrevChunk
    | DiffViewFocusNextChunk
    | DiffViewWindowFocusChange
    | DisposeDiffViewMessage
    | NextEditDismissMessage
    | NextEditLoadedMessage
    | NextEditSuggestionsMessage
    | NextEditRefreshStarted
    | NextEditRefreshFinished
    | NextEditCancelMessage
    | NextEditPreviewActive
    | NextEditSuggestionsChanged
    | NextEditSuggestionsActionMessage
    | NextEditFocusedSuggestionChanged
    | NextEditOpenSuggestion
    | ReadFileRequest
    | ReadFileResponse
    | NextEditToggleSuggestionTreeMessage
    | OnboardingLoaded
    | OnboardingUpdateState
    | OnboardingSignIn
    | UsedChat
    | PreferenceInit
    | PreferenceResultMessage
    | PreferenceNotify
    | PreferencePanelLoaded
    | SaveChatMessage
    | SaveChatDoneMessage
    | ShouldShowSummary
    | ShowAugmentPanel
    | MainPanelLoaded
    | MainPanelDisplayApp
    | MainPanelActions
    | MainPanelPerformAction
    | UsedSlashAction
    | EmptyMessage
    | WSContextGetChildrenRequest
    | WSContextGetChildrenResponse
    | WSContextGetSourceFoldersRequest
    | WSContextGetSourceFoldersResponse
    | WSContextAddMoreSourceFolders
    | WSContextRemoveSourceFolder
    | WSContextSourceFoldersChanged
    | WSContextFolderContentsChanged
    | WSContextUserRequestedRefresh
    | AugmentCommand
    | SyncEnabledStateUpdate
    | UpdateGuidelinesState
    | OpenGuidelines
    | UpdateUserGuidelines
    | AsyncWebViewMessage<WebViewMessage>
    | ChatModeChangedMessage
    | ChatReviewAgentFileMessage
    | ChatGetAgentEditListRequest
    | ChatAgentEditListResponse
    | ChatAgentEditListHasUpdatesMessage
    | ChatAgentEditAcceptAll
    | ChatAgentEditRejectAll
    | ChatGetAgentOnboardingPromptRequest
    | ChatGetAgentOnboardingPromptResponse
    | OpenAgentCheckpoint
    | SetRemoteAgentIdMessage
    | RemoteAgentManagerLoadedMessage
    | RemoteAgentManagerInitializeMessage
    | GetRemoteAgentOverviewsRequest
    | GetRemoteAgentOverviewsResponse
    | GetRemoteAgentChatHistoryRequest
    | GetRemoteAgentChatHistoryResponse
    | ApplyChangesRequestMessage
    | ApplyChangesResponseMessage
    | CreateRemoteAgentRequestMessage
    | CreateRemoteAgentResponseMessage
    | OpenRemoteAgentManagerMessage
    | RemoteAgentChatRequestMessage
    | RemoteAgentChatResponseMessage
    | RemoteAgentDeleteRequestMessage
    | RemoteAgentDeleteResponseMessage
    | RemoteAgentStopRequestMessage
    | RemoteAgentStopResponseMessage
    | GetGitBranchesRequestMessage
    | GetGitBranchesResponseMessage
    | GetWorkspaceDiffRequestMessage
    | GetWorkspaceDiffResponseMessage
    | GetRemoteUrlRequestMessage
    | GetRemoteUrlResponseMessage
    | DiffExplanationRequestMessage
    | DiffExplanationResponseMessage
    | ResetAgentOnboardingMessage
    | GetAgentEditChangesByRequestIdRequest
    | GetAgentEditChangesByRequestIdResponse
    | GetAgentEditContentsByRequestIdRequest
    | GetAgentEditContentsByRequestIdResponse
    | TriggerInitialOrientation
    | CheckAgentAutoModeApproval
    | CheckAgentAutoModeApprovalResponse
    | SetAgentAutoModeApproved
    | ToolConfigLoaded
    | ToolConfigInitialize
    | ToolConfigSave
    | ToolConfigGetDefinitions
    | ToolConfigDefinitionsResponse
    | ToolConfigStartOAuth
    | ToolConfigStartOAuthResponse
    | ToolConfigRevokeAccess;
