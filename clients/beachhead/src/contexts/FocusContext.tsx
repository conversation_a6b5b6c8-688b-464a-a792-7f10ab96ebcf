import React, { createContext, useContext, useState, useCallback, ReactNode } from 'react';

interface KeypressHandler {
  onKeypress: (input: string, key: any) => void;
}

interface FocusContextType {
  focusStack: string[];
  pushFocus: (elementId: string) => void;
  popFocus: () => void;
  registerHandler: (elementId: string, handler: KeypressHandler) => void;
  unregisterHandler: (elementId: string) => void;
  getCurrentHandler: () => KeypressHandler | undefined;
}

const FocusContext = createContext<FocusContextType | undefined>(undefined);

export const useFocusContext = () => {
  const context = useContext(FocusContext);
  if (!context) {
    throw new Error('useFocusContext must be used within a FocusProvider');
  }
  return context;
};

interface FocusProviderProps {
  children: ReactNode;
}

export const FocusProvider: React.FC<FocusProviderProps> = ({ children }) => {
  const [focusStack, setFocusStack] = useState<string[]>([]);
  const [handlers, setHandlers] = useState<Map<string, KeypressHandler>>(new Map());

  const pushFocus = useCallback((elementId: string) => {
    setFocusStack(prev => [...prev, elementId]);
  }, []);

  const popFocus = useCallback(() => {
    setFocusStack(prev => prev.slice(0, -1));
  }, []);

  const registerHandler = useCallback((elementId: string, handler: KeypressHandler) => {
    setHandlers(prev => new Map(prev).set(elementId, handler));
  }, []);

  const unregisterHandler = useCallback((elementId: string) => {
    setHandlers(prev => {
      const next = new Map(prev);
      next.delete(elementId);
      return next;
    });
  }, []);

  const getCurrentHandler = useCallback(() => {
    if (focusStack.length === 0) return undefined;
    const currentFocusId = focusStack[focusStack.length - 1];
    return handlers.get(currentFocusId);
  }, [focusStack, handlers]);

  const value: FocusContextType = {
    focusStack,
    pushFocus,
    popFocus,
    registerHandler,
    unregisterHandler,
    getCurrentHandler,
  };

  return <FocusContext.Provider value={value}>{children}</FocusContext.Provider>;
};
