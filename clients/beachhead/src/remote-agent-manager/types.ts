/* eslint-disable @typescript-eslint/naming-convention */
import { ChatRequestNode, Exchange } from "@augment-internal/sidecar-libs/src/chat/chat-types";
import { McpServerType } from "@augment-internal/sidecar-libs/src/tools/mcp-server-utils";

// Enum for Remote Agent Status
// TODO: fix type mapping
export enum RemoteAgentStatus {
    agentUnspecified = 0,
    // The agent is starting and setting up its environment
    agentStarting = 1,
    // The agent is working on its task
    agentRunning = 2,
    // The agent has completed its task, and is awaiting further instructions.
    // The task has either completed or failed.
    agentIdle = 3,
    // The agent encountered an error and cannot continue
    // We don't report that here, instead we let the wrapper (img/innie/init.sh)
    // report this after the beachhead has crashed too many times in a row.
    // agentFailed = 4,
}

// Enum for File Change Type
export enum FileChangeType {
    added = 0,
    deleted = 1,
    modified = 2,
    renamed = 3,
}

export interface ChangedFile {
    // Empty if file was added
    old_path: string;
    // Empty if the file was deleted
    new_path: string;
    old_contents: string;
    new_contents: string;
    change_type: FileChangeType;
}

export type CommitRef = {
    GithubCommitRef: {
        repository_url: string;
        git_ref: string;
        patch?: string;
    };
};

export interface RemoteAgentChatHistoryResponse {
    chat_history: RemoteAgentExchange[];
}

export interface RemoteAgentExchange {
    exchange: Exchange;
    // Files changed as a result of this exchange
    changed_files: ChangedFile[];
    // Sequence ID for tracking the order of exchanges
    sequence_id: number;
    // Optional user-facing summary of the turn (or last few turns).
    turn_summary?: string;
    // Timestamp when the exchange was finished.
    finished_at: string; // ISO string format
    // Non-exhaustive list of changed file paths that were skipped due to size limits
    changed_files_skipped: string[];
    // Count of changed files not included in changed_files due to size limits
    changed_files_skipped_count?: number;
}

export enum RemoteWorkspaceSetupStepStatus {
    unknown = 0,
    running = 1,
    success = 2,
    failure = 3,
    skipped = 4,
}

export interface RemoteWorkspaceSetupStep {
    step_description: string;
    logs: string;
    status: RemoteWorkspaceSetupStepStatus;
    sequence_id: number;
    step_number: number;
}

export interface RemoteWorkspaceSetupStatus {
    steps: RemoteWorkspaceSetupStep[];
}

export interface RemoteAgentDetails {
    remote_agent_id: string;
    status: RemoteAgentStatus;
    // A summary of the whole agentic session
    session_summary: string;
    // Summaries of what happened in each turn
    turn_summaries: string[];
    started_at: string; // ISO string format
    // Commit has the agent started with
    initial_commit_ref: CommitRef;
    // The branch the agent is currently on
    currentBranch: string;
}

export interface RemoteAgentListRequest {}

export interface RemoteAgentListResponse {
    remote_agents: RemoteAgentDetails[];
}

export interface RemoteAgentChatHistoryRequest {
    remote_agent_id: string;
}

export interface McpServerConfig {
    command: string;
    args?: string[];
    timeout_ms?: number;
    env?: Record<string, string>;
    use_shell_interpolation?: boolean;
    name?: string;
    disabled?: boolean;
    url?: string;
    type?: McpServerType;
}

export type RemoteAgentChatRequestDetails = {
    request_nodes?: ChatRequestNode[];
    user_guidelines?: string;
    workspace_guidelines?: string;
    agent_memories?: string;
    model_id?: string;
    mcp_servers?: McpServerConfig[];
};

export interface AgentWorkspaceInterrupt {}

export interface AgentWorkspaceChatRequest {
    request_details: RemoteAgentChatRequestDetails;
}

export interface AgentWorkspaceUpdate {
    sequence_id: number;
    update: {
        Interrupt?: AgentWorkspaceInterrupt;
        ChatRequest?: AgentWorkspaceChatRequest;
    };
}

export interface AgentWorkspacePollUpdateRequest {
    remote_agent_id: string;
    // The sequence ID of the last update received
    // by the agent. The server will return all
    // updates with a sequence ID greater than this.
    last_processed_sequence_id: number;
}

export interface AgentWorkspacePollUpdateResponse {
    // All updates with a sequence ID greater than the
    // last_processed_sequence_id in the request.
    updates: AgentWorkspaceUpdate[];
}

export enum AgentWorkspaceUpdateType {
    AGENT_WORKSPACE_UPDATE_TYPE_UNSPECIFIED = 0,
    AGENT_WORKSPACE_UPDATE_INTERRUPT = 1,
    AGENT_WORKSPACE_UPDATE_CHAT_REQUEST = 2,
}

export interface AgentWorkspaceStreamUpdate {
    type: AgentWorkspaceUpdateType;
    sequence_id: number;
    interrupt?: AgentWorkspaceInterrupt;
    chat_request?: AgentWorkspaceChatRequest;
}

export interface AgentWorkspaceStreamResponse {
    updates: AgentWorkspaceStreamUpdate[];
}

export type DiffExplanation = DiffExplanationSection[];
export type DiffExplanationSection = {
    title: string;
    description: string;
    sections: DiffExplanationSubSection[];
};
export type DiffType =
    | "fix"
    | "feature"
    | "refactor"
    | "documentation"
    | "style"
    | "test"
    | "chore"
    | "revert"
    | "other";
export type DiffExplanationSubSection = {
    title: string;
    description: string;
    type: DiffType;
    warning?: string;
    changes: Diff[];
};
export type Diff = {
    id: string;
    path: string;
    diff: string;
    originalCode: string;
    modifiedCode: string;
};
