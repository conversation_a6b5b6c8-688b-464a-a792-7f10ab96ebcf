// Core dependencies
import { Exchange } from "@augment-internal/sidecar-libs/src/chat/chat-types";
import {
    errorToolResponse,
    successToolResponse,
} from "@augment-internal/sidecar-libs/src/tools/sidecar-tools/tool-use-response";
import {
    LocalToolType,
    ToolBase,
    ToolSafety,
    ToolUseResponse,
} from "@augment-internal/sidecar-libs/src/tools/tool-types";
import { executeCommand } from "@augment-internal/sidecar-libs/src/tools/tool-utils";
// Node.js built-in modules
import { spawn } from "child_process";
import { randomUUID } from "crypto";
// Third-party dependencies
import * as fs from "fs";
import * as os from "os";
import * as path from "path";

import { AgentState, SubAgentResult } from "../agent_loop/state";
// Local types
import { ChangedFile, FileChangeType } from "../remote-agent-manager/types";

// Maximum number of concurrent sub-agents to prevent resource exhaustion
const MAX_CONCURRENT_SUBAGENTS = 10;

/**
 * Get all changed files from completed exchanges plus current exchange.
 * Each file appears only once, showing the progression from initial state to final state.
 */
export function getAllChangedFiles(
    chatHistory: Array<{ changedFiles: ChangedFile[] }>,
    currentChangedFiles: ChangedFile[]
): ChangedFile[] {
    // Collect all changed files from completed exchanges and current exchange
    const allFiles = [
        ...chatHistory.flatMap((exchange) => exchange.changedFiles),
        ...currentChangedFiles,
    ];

    // Create a map to track the evolution of each file path
    const fileMap = new Map<string, ChangedFile>();

    for (const file of allFiles) {
        const filePath = file.new_path || file.old_path;

        if (fileMap.has(filePath)) {
            // File already exists, update it to show progression from initial to final state
            const existing = fileMap.get(filePath)!;
            const aggregatedChangeType = aggregateChangeType(existing, file);

            fileMap.set(filePath, {
                old_path: existing.old_path, // Keep original old_path
                new_path: file.new_path, // Use latest new_path
                old_contents: existing.old_contents, // Keep original old_contents
                new_contents: file.new_contents, // Use latest new_contents
                change_type: aggregatedChangeType, // Smart aggregation
            });
        } else {
            // First time seeing this file
            fileMap.set(filePath, file);
        }
    }

    return Array.from(fileMap.values());
}

/**
 * Intelligently aggregate change types based on the progression of file changes
 */
export function aggregateChangeType(existing: ChangedFile, latest: ChangedFile): FileChangeType {
    // If the file was originally added and then deleted, it's effectively no change
    // But since we're tracking changes, we'll consider it as added then deleted
    if (
        existing.change_type === FileChangeType.added &&
        latest.change_type === FileChangeType.deleted
    ) {
        return FileChangeType.deleted;
    }

    // If the file was originally deleted and then added back, it's modified
    if (
        existing.change_type === FileChangeType.deleted &&
        latest.change_type === FileChangeType.added
    ) {
        return FileChangeType.modified;
    }

    // If the file was added initially, it remains added (regardless of subsequent modifications)
    if (existing.change_type === FileChangeType.added) {
        return FileChangeType.added;
    }

    // If the file is deleted in the latest change, it's deleted
    if (latest.change_type === FileChangeType.deleted) {
        return FileChangeType.deleted;
    }

    // If there was a rename involved, check if paths actually changed
    if (
        existing.change_type === FileChangeType.renamed ||
        latest.change_type === FileChangeType.renamed
    ) {
        const originalPath = existing.old_path;
        const finalPath = latest.new_path;

        if (originalPath !== finalPath) {
            return FileChangeType.renamed;
        } else {
            // Path didn't actually change, so it's just modified
            return FileChangeType.modified;
        }
    }

    // Default case: if it's not added or deleted, it's modified
    return FileChangeType.modified;
}

export class SubAgentTool extends ToolBase<LocalToolType> {
    private readonly workspaceRoot: string;
    private readonly isInteractive: boolean;
    private readonly isBeachhead: boolean;
    private readonly agentState: AgentState;

    /**
     * Check if a process is still running by PID
     */
    private static isProcessRunning(pid: number): boolean {
        try {
            process.kill(pid, 0); // Signal 0 checks if process exists
            return true;
        } catch {
            return false;
        }
    }

    /**
     * Get agent name from sub-agent ID using results store
     */
    private getNameFromSubAgentId(subAgentId: string): string {
        const result = this.agentState.getSubAgentStoredResult(subAgentId);
        if (!result) {
            throw new Error(`No agent found for sub-agent ID: ${subAgentId}`);
        }
        return result.name;
    }

    /**
     * Convert agent names to sub-agent IDs, validating that all agents exist
     */
    private validateAndGetSubAgentIds(names: string[]): string[] | undefined {
        if (names.length === 0) {
            return undefined;
        }

        const subAgentIds: string[] = [];
        const storedResults = this.agentState.getAllSubAgentStoredResults();
        for (const name of names) {
            // Find sub-agent ID by searching through stored results
            const foundSubAgentId = Object.keys(storedResults).find(
                (subAgentId) => storedResults[subAgentId].name === name
            );
            if (!foundSubAgentId) {
                return undefined;
            }
            subAgentIds.push(foundSubAgentId);
        }

        return subAgentIds;
    }

    constructor(
        workspaceRoot: string,
        agentState: AgentState,
        isInteractive: boolean = true,
        isBeachhead: boolean = false
    ) {
        super(LocalToolType.LocalSubAgent, ToolSafety.Check);
        this.workspaceRoot = workspaceRoot;
        this.agentState = agentState;
        this.isInteractive = isInteractive;
        this.isBeachhead = isBeachhead;
    }

    public get description(): string {
        return `Create and manage sub-agents that work in isolated environments to complete tasks independently.

        **Key Features:**
        - Each sub-agent runs in its own isolated filesystem, so to see the changes, use the diff action.
        - Sub-agents can create and modify without affecting your main workspace

        **IMPORTANT**: Sub-agent file changes are NOT automatically applied to your current workspace.
        Use the diff action to review changes before applying them with the apply action.

        **Multiple Agent Support:**
        You can run multiple agents simultaneously and review their changes:
        - Run multiple tasks: run(instruction=["task 1", "task 2", "task 3"], name=["agent1", "agent2", "agent3"])
        - Review changes: diff(name=["agent1", "agent2", "agent3"])
        - Apply changes: apply(name=["agent1", "agent2", "agent3"])

        **Example workflow:**
        1. Run agents: run(instruction=["implement feature A", "write tests for A", "update docs for A"], name=["feature-impl", "feature-tests", "feature-docs"])
        2. Review changes: diff(name=["feature-impl", "feature-tests", "feature-docs"])
        3. Apply changes: apply(name=["feature-impl", "feature-tests", "feature-docs"])

        Agents are automatically terminated when they complete their tasks or when interrupted.

        Available actions:
        • **run** - Create new sub-agents with initial task instructions and wait for completion (one agent per instruction, combines create+wait)
        • **diff** - Show file changes made by completed sub-agents using unified diff format
        • **apply** - Apply file changes from completed sub-agents to your local workspace (ALWAYS use diff first to review changes)
        `;
    }

    public readonly inputSchemaJson: string = JSON.stringify({
        /* eslint-disable @typescript-eslint/naming-convention */
        type: "object",
        properties: {
            action: {
                type: "string",
                enum: ["run", "diff", "apply"],
                description: `Action to perform:
'run' - creates new sub-agents and waits for completion
'diff' - shows file changes from completed sub-agents
'apply' - applies file changes from completed sub-agents to local workspace`,
            },
            name: {
                oneOf: [
                    {
                        type: "string",
                        description: "Single agent name",
                    },
                    {
                        type: "array",
                        items: {
                            type: "string",
                        },
                        description: "List of agent names",
                    },
                ],
                description: `Name(s) of sub-agents. Names must be unique and contain no spaces.
For 'diff': provide names of completed agents.
For 'apply': provide names of completed agents whose changes should be applied.
For 'run': provide names for new agents (required when creating agents).`,
            },
            instruction: {
                oneOf: [
                    {
                        type: "string",
                        description: "Single instruction",
                    },
                    {
                        type: "array",
                        items: {
                            type: "string",
                        },
                        description: "List of instructions",
                    },
                ],
                description: `Your instruction(s) to send to the assistant(s) (required for 'run' action only).
Can be a single string or array of strings.
For 'run': provide initial task instructions for new agents (one agent created per instruction).
Not used for 'diff' or 'apply' actions.
When creating multiple agents, consider how their tasks relate - they can work on different parts of the same feature, different files, or complementary tasks.`,
            },
        },
        required: ["action"],
        /* eslint-enable @typescript-eslint/naming-convention */
    });

    public checkToolCallSafe(_toolInput: Record<string, any>): boolean {
        // All sub-agent operations are considered safe
        return true;
    }

    public async call(
        toolInput: Record<string, unknown>,
        _chatHistory: Exchange[],
        abortSignal: AbortSignal,
        _toolUseId: string
    ): Promise<ToolUseResponse> {
        const action = toolInput as unknown as {
            action: "run" | "diff" | "apply";
            name?: string | string[];
            instruction?: string | string[];
        };

        try {
            // Normalize name to array
            const names = action.name
                ? Array.isArray(action.name)
                    ? action.name
                    : [action.name]
                : [];

            // Normalize instruction to array
            const instructions = action.instruction
                ? Array.isArray(action.instruction)
                    ? action.instruction
                    : [action.instruction]
                : [];

            // Handle run action: create agents and wait for completion
            if (action.action === "run") {
                if (instructions.length === 0) {
                    return errorToolResponse("No instructions provided for run action");
                }

                if (names.length === 0) {
                    return errorToolResponse("No names provided for run action");
                }
                if (!process.env.AUGMENT_API_TOKEN || !process.env.AUGMENT_API_URL) {
                    return errorToolResponse(
                        "Both AUGMENT_API_TOKEN and AUGMENT_API_URL must be set"
                    );
                }
                if (names.length !== instructions.length) {
                    return errorToolResponse("Number of names must match number of instructions");
                }

                // Check maximum concurrent subagents limit
                if (names.length > MAX_CONCURRENT_SUBAGENTS) {
                    return errorToolResponse(
                        `Cannot create ${names.length} new sub-agents. Max allowed: ${MAX_CONCURRENT_SUBAGENTS}`
                    );
                }

                // Validate names are unique and contain no spaces
                for (const name of names) {
                    if (name.includes(" ")) {
                        return errorToolResponse(`Agent name "${name}" cannot contain spaces`);
                    }
                    // Check if name is already in use by searching stored results
                    const isNameInUse = Object.values(
                        this.agentState.getAllSubAgentStoredResults()
                    ).some((result) => result.name === name);
                    if (isNameInUse) {
                        return errorToolResponse(`Agent name "${name}" is already in use`);
                    }
                }

                // Create new agents concurrently for each instruction
                const agentPromises = instructions.map((instruction, index) =>
                    this.createAgent(instruction, names[index])
                );
                const ret = await Promise.all(agentPromises);
                const createdAgents = ret.filter(
                    (subAgentId): subAgentId is string => subAgentId !== undefined
                );
                const failedAgents = names.filter((_, index) => ret[index] === undefined);
                if (createdAgents.length === 0) {
                    return errorToolResponse(
                        "All agents failed to start within the timeout period"
                    );
                }
                let responseText = "";
                if (failedAgents.length > 0) {
                    responseText += `${failedAgents.map((name) => `### Agent ${name} failed to start`).join("\n\n")}\n\n`;
                }
                try {
                    const waitResults: SubAgentResult[] = await this.waitForAgents(
                        createdAgents,
                        abortSignal
                    );
                    responseText += `${waitResults.map((result) => `### Agent ${result.name} Output:\n${result.result}`).join("\n\n")}`;
                    return successToolResponse(responseText);
                } catch (error) {
                    return errorToolResponse(
                        `Failed to wait for agents: ${error instanceof Error ? error.message : String(error)}`
                    );
                }
            }

            // Handle diff action: show file changes for specified agent names
            if (action.action === "diff") {
                const subAgentIds = this.validateAndGetSubAgentIds(names);
                if (!subAgentIds) {
                    return errorToolResponse(
                        "No agent names provided or agents not found. See the agent names from the run result."
                    );
                }

                const textResults = subAgentIds.map((subAgentId, index) => {
                    const storedResult = this.agentState.getSubAgentStoredResult(subAgentId);
                    if (!storedResult) {
                        throw new Error(
                            `No stored result found for agent ${names[index]}. Agent may not have completed yet.`
                        );
                    }
                    return `### Agent ${names[index]} Diff:\n\`\`\`\n${storedResult.diff}\n\`\`\`\n`;
                });

                return successToolResponse(textResults.join("\n\n"));
            }

            // Handle apply action: apply file changes to local workspace
            if (action.action === "apply") {
                const subAgentIds = this.validateAndGetSubAgentIds(names);
                if (!subAgentIds) {
                    return errorToolResponse(
                        "No agent names provided or agents not found. See the agent names from the run result."
                    );
                }

                try {
                    const applyResults = await Promise.all(
                        subAgentIds.map(async (subAgentId, index) => {
                            const storedResult =
                                this.agentState.getSubAgentStoredResult(subAgentId);
                            if (!storedResult) {
                                throw new Error(
                                    `No stored result found for agent ${names[index]}. Agent may not have completed yet.`
                                );
                            }
                            const success = await this.applyChangesToWorkspace(storedResult);
                            return { name: names[index], success };
                        })
                    );

                    const responseText = applyResults
                        .map(
                            (result) =>
                                `### Agent ${result.name} Apply: ${result.success ? "Success" : "Failed"}`
                        )
                        .join("\n\n");

                    return successToolResponse(responseText);
                } catch (error) {
                    return errorToolResponse(
                        `Failed to apply changes: ${error instanceof Error ? error.message : String(error)}`
                    );
                }
            }

            // This should never be reached due to TypeScript type checking
            return errorToolResponse(`Unknown action: ${action.action as string}`);
        } catch (e: unknown) {
            return errorToolResponse(
                `Failed to execute beachhead sub-agent action: ${e instanceof Error ? e.message : String(e)}`
            );
        }
    }

    private async createAgent(
        instruction: string,
        name: string,
        timeoutMs: number = 5000
    ): Promise<string | undefined> {
        // Check if fuse-overlayfs is available
        const fuseAvailable = await this.checkFuseOverlayfsAvailable();
        if (!fuseAvailable) {
            throw new Error(
                "fuse-overlayfs is not available on this system. Please install fuse-overlayfs to enable filesystem isolation for sub-agents (`sudo apt install fuse-overlayfs` on Ubuntu)."
            );
        }

        // Validate workspace root exists
        if (!fs.existsSync(this.workspaceRoot)) {
            throw new Error(`Workspace root does not exist: ${this.workspaceRoot}`);
        }

        // Generate unique sub-agent ID and set up isolated filesystem
        const subAgentId = randomUUID();
        await this.setupIsolatedFilesystem(this.workspaceRoot, subAgentId);
        const isolatedPath = path.join(os.tmpdir(), subAgentId, "isolated_view");

        // Write instruction to a temporary file to avoid CLI argument length limits
        const instructionFile = path.join(os.tmpdir(), subAgentId, "instruction.txt");
        await fs.promises.writeFile(instructionFile, instruction, "utf8");

        const isNode: boolean = process.execPath.toLowerCase().endsWith("node");
        let args = [
            "--workspace-root",
            isolatedPath,
            "--sub-agent-mode",
            "--instruction-file",
            instructionFile,
        ];
        if (this.isBeachhead) {
            args = ["--cli", ...args];
        } else if (isNode) {
            args = [process.argv[1], "--print", "--quiet", ...args];
        } else {
            throw new Error("Unsupported runtime for sub-agent tool");
        }

        const childProcess = spawn(process.execPath, args, {
            cwd: isolatedPath,
            stdio: ["pipe", "pipe", "pipe"],
            detached: false,
            env: {
                ...process.env,
                SUB_AGENT_ID: subAgentId,
                SUB_AGENT_NAME: name,
            },
        });
        const debugFile: string = path.join(os.tmpdir(), subAgentId, "debug.log");
        childProcess.stdout?.on("data", (data: Buffer) => {
            try {
                fs.appendFileSync(debugFile, data.toString());
            } catch (error) {
                // Ignore errors writing to debug file
            }
        });

        childProcess.stderr?.on("data", (data: Buffer) => {
            try {
                fs.appendFileSync(debugFile, data.toString());
            } catch (error) {
                // Ignore errors writing to debug file
            }
        });

        const statusFile = path.join(os.tmpdir(), subAgentId, "status.json");
        const startTime = Date.now();
        while (Date.now() - startTime < timeoutMs) {
            if (fs.existsSync(statusFile)) {
                try {
                    const data = await fs.promises.readFile(statusFile, "utf8");
                    const response: SubAgentResult = JSON.parse(data) as SubAgentResult;
                    // Store the initial result in agent state using sub-agent ID
                    this.agentState.setSubAgentStoredResult(subAgentId, response);
                    return subAgentId;
                } catch {
                    // File exists but not readable yet, continue waiting
                }
            }
            await new Promise((resolve) => setTimeout(resolve, 100));
        }

        void this.cleanupIsolatedFilesystem(subAgentId);
        return undefined;
    }

    /**
     * Check if fuse-overlayfs is available on the system
     */
    private async checkFuseOverlayfsAvailable(): Promise<boolean> {
        try {
            const result = await executeCommand("which fuse-overlayfs", { timeout: 5000 });
            return result !== undefined && result.length > 0;
        } catch {
            return false;
        }
    }

    /**
     * Set up isolated filesystem using fuse-overlayfs
     * @param workspaceRoot - The workspace root directory to mount
     * @param subAgentId - The unique sub-agent ID to use as directory name
     */
    private async setupIsolatedFilesystem(
        workspaceRoot: string,
        subAgentId: string
    ): Promise<void> {
        const subAgentDir = path.join(os.tmpdir(), subAgentId);

        try {
            // Create the sub-agent directory
            await fs.promises.mkdir(subAgentDir, { recursive: true });

            const [upperDir, workDir, isolatedPath] = [
                path.join(subAgentDir, "overlay_upper"),
                path.join(subAgentDir, "overlay_work"),
                path.join(subAgentDir, "isolated_view"),
            ];

            await Promise.all([
                fs.promises.mkdir(upperDir, { recursive: true }),
                fs.promises.mkdir(workDir, { recursive: true }),
                fs.promises.mkdir(isolatedPath, { recursive: true }),
            ]);

            const mountCmd = `fuse-overlayfs -o lowerdir=${workspaceRoot},upperdir=${upperDir},workdir=${workDir} ${isolatedPath}`;
            const mountResult = await executeCommand(mountCmd, { timeout: 10000 });

            if (mountResult === undefined) {
                throw new Error("Failed to mount fuse-overlayfs");
            }
        } catch (error) {
            await fs.promises.rm(subAgentDir, { recursive: true, force: true }).catch(() => {});
            throw new Error(
                `Failed to setup isolated filesystem: ${error instanceof Error ? error.message : String(error)}`
            );
        }
    }

    /**
     * Clean up isolated filesystem for a sub-agent
     */
    private async cleanupIsolatedFilesystem(subAgentId: string): Promise<void> {
        try {
            const subAgentDir = path.join(os.tmpdir(), subAgentId);
            const unmountCmd = `fusermount -u ${path.join(subAgentDir, "isolated_view")}`;
            await executeCommand(unmountCmd, { timeout: 10000 });
            await new Promise((resolve) => setTimeout(resolve, 1000)); // Wait for unmount to complete
            await fs.promises.rm(subAgentDir, { recursive: true, force: true });
        } catch (error) {
            // Silently ignore cleanup errors - best effort cleanup
        }
    }

    /**
     * Display progress update for one or more agents with timestamps and completion status
     */
    private displayProgress(isUpdate: boolean = false, frameIndex: number = 0): void {
        if (!this.isInteractive) {
            return;
        }

        const terminalWidth = process.stdout.columns || 80;

        // Thinking animation frames
        const thinkingFrames = ["⠋", "⠙", "⠹", "⠸", "⠼", "⠴", "⠦", "⠧", "⠇", "⠏"];
        const thinkingFrame = thinkingFrames[frameIndex % thinkingFrames.length];

        const storedResults = this.agentState.getAllSubAgentStoredResults();
        const progressLines = Array.from(
            Object.entries(storedResults),
            ([subAgentId, resultResponse]: [string, SubAgentResult]) => {
                const name = this.getNameFromSubAgentId(subAgentId);
                const isCompleted = !SubAgentTool.isProcessRunning(resultResponse.pid);
                const message = isCompleted ? "Done!" : resultResponse.update.replace(/\n/g, " ");
                const prefix = `${isCompleted ? "✅" : thinkingFrame} [${name}] `;
                const availableWidth = terminalWidth - prefix.length - 5;
                const truncatedMessage =
                    message.length > availableWidth
                        ? message.substring(0, availableWidth - 3) + "..."
                        : message;
                return `${prefix}${truncatedMessage}`;
            }
        );

        if (isUpdate && Object.keys(storedResults).length > 1) {
            process.stdout.write(`\u001b[${Object.keys(storedResults).length - 1}A`);
        }

        progressLines.forEach((line, i) => {
            if (i > 0) {
                process.stdout.write("\n");
            }
            process.stdout.write("\r\u001b[K" + line);
        });
    }

    /**
     * Wait for agents with coordinated progress display (handles single or multiple agents)
     */
    private async waitForAgents(
        subAgentIds: string[],
        abortSignal: AbortSignal
    ): Promise<SubAgentResult[]> {
        let interrupted = false;

        const interruptHandler = (_signal: NodeJS.Signals) => {
            interrupted = true;
            // Prevent the signal from propagating to the parent process
            // by not re-emitting it or calling process.exit()
        };

        // Register our handler and remove any existing ones temporarily
        const existingListeners = process.listeners("SIGINT");
        process.removeAllListeners("SIGINT");
        process.on("SIGINT", interruptHandler);

        try {
            // Poll until all agents are idle or interrupted
            let allDone = false;
            let frameIndex = 0;
            while (!allDone && !interrupted && !abortSignal.aborted) {
                // Update store for each agent
                const isRunning = [];
                for (const subAgentId of subAgentIds) {
                    const agentInfo = this.agentState.getSubAgentStoredResult(subAgentId)!;
                    isRunning.push(SubAgentTool.isProcessRunning(agentInfo.pid));
                    const statusFile = path.join(os.tmpdir(), subAgentId, "status.json");
                    if (fs.existsSync(statusFile)) {
                        try {
                            const data = await fs.promises.readFile(statusFile, "utf8");
                            const response: SubAgentResult = JSON.parse(data) as SubAgentResult;
                            // Update the store in place
                            this.agentState.setSubAgentStoredResult(subAgentId, response);
                        } catch {
                            // File not readable yet, keep current state
                        }
                    }
                }
                this.displayProgress(true, frameIndex);
                frameIndex++;
                allDone = isRunning.every((running) => !running);
                await new Promise((resolve) => setTimeout(resolve, 100));
            }
            return subAgentIds.map((subAgentId) => {
                return this.agentState.getSubAgentStoredResult(subAgentId)!;
            });
        } finally {
            // Restore original signal handling
            process.removeListener("SIGINT", interruptHandler);
            existingListeners.forEach((listener) => {
                process.on("SIGINT", listener as (...args: any[]) => void);
            });
            subAgentIds.forEach((subAgentId) => {
                if (interrupted || abortSignal.aborted) {
                    try {
                        const agentInfo = this.agentState.getSubAgentStoredResult(subAgentId);
                        if (agentInfo) {
                            process.kill(agentInfo.pid, "SIGKILL");
                        }
                    } catch (error) {
                        // Ignore errors when killing the process
                    }
                }
                void this.cleanupIsolatedFilesystem(subAgentId);
            });
            process.stdout.write(
                interrupted ? "\nSub-agents interrupted by user (Ctrl+C)\n" : "\n"
            );
        }
    }

    /**
     * Apply changes from a sub-agent to the local workspace
     */
    private async applyChangesToWorkspace(storedResult: SubAgentResult): Promise<boolean> {
        try {
            if (!storedResult.diff || storedResult.diff.trim() === "") {
                return true;
            }

            // Use git apply to apply the diff patch
            const { spawn } = await import("child_process");
            const gitApply = spawn("git", ["apply", "--verbose"], {
                cwd: this.workspaceRoot,
                stdio: ["pipe", "pipe", "pipe"],
            });

            return new Promise((resolve) => {
                gitApply.stdin.write(storedResult.diff);
                gitApply.stdin.end();

                gitApply.on("close", (code) => {
                    resolve(code === 0);
                });

                gitApply.on("error", () => {
                    resolve(false);
                });
            });
        } catch (error) {
            return false;
        }
    }
}
