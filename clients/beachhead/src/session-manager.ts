import * as fs from "fs";
import * as path from "path";

import { AgentState, ExchangeState } from "./agent_loop/state";
import { getLogger } from "./logging";
import { fileExists, makeDirsSync, readFileUtf8, writeFileUtf8 } from "./utils/fs-utils";

export interface SessionData {
    sessionId: string;
    created: string; // ISO timestamp
    modified: string; // ISO timestamp
    chatHistory: ExchangeState[];
    agentState: {
        userGuidelines: string;
        workspaceGuidelines: string;
        agentMemories: string;
        modelId?: string;
    };
}

export interface SessionMetadata {
    sessionId: string;
    created: string;
    modified: string;
    exchangeCount: number;
}

/**
 * SessionManager handles persistent CLI sessions.
 * Sessions are stored in ~/.augment/sessions/SESSION-ID.json
 */
export class SessionManager {
    private logger = getLogger("SessionManager");
    private sessionsDir: string;
    private savingEnabled: boolean;

    constructor(cacheDir: string, enableSaving: boolean = true) {
        this.sessionsDir = path.join(cacheDir, "sessions");
        this.savingEnabled = enableSaving;
        this.ensureSessionsDirectoryExists();
    }

    /**
     * Ensures the sessions directory exists
     */
    private ensureSessionsDirectoryExists(): void {
        try {
            makeDirsSync(this.sessionsDir);
        } catch (error) {
            this.logger.error("Failed to create sessions directory: %s", error);
            throw new Error(`Failed to create sessions directory: ${String(error)}`);
        }
    }

    /**
     * Gets the file path for a session
     */
    private getSessionFilePath(sessionId: string): string {
        return path.join(this.sessionsDir, `${sessionId}.json`);
    }

    /**
     * Saves a session to disk
     */
    async saveSession(agentState: Readonly<AgentState>): Promise<void> {
        if (!this.savingEnabled) {
            // Session saving is disabled, do nothing
            return;
        }

        const sessionId = agentState.conversationId;
        const filePath = this.getSessionFilePath(sessionId);
        const now = new Date().toISOString();

        // Check if session already exists to determine if this is creation or update
        const isNewSession = !fileExists(filePath);
        let created = now;

        if (!isNewSession) {
            try {
                const existingSession = await this.loadSession(sessionId);
                created = existingSession.created;
            } catch (error) {
                // If we can't load existing session, treat as new
                this.logger.warn(
                    "Could not load existing session for update, treating as new: %s",
                    error
                );
            }
        }

        const sessionData: SessionData = {
            sessionId,
            created,
            modified: now,
            chatHistory: [...agentState.chatHistory], // Create a mutable copy
            agentState: {
                userGuidelines: agentState.userGuidelines,
                workspaceGuidelines: agentState.workspaceGuidelines,
                agentMemories: agentState.agentMemories,
                modelId: agentState.modelId,
            },
        };

        try {
            await writeFileUtf8(filePath, JSON.stringify(sessionData, null, 2));
            this.logger.info("Session saved: %s", sessionId);
        } catch (error) {
            this.logger.error("Failed to save session %s: %s", sessionId, error);
            throw new Error(`Failed to save session: ${String(error)}`);
        }
    }

    /**
     * Loads a session from disk
     */
    async loadSession(sessionId: string): Promise<SessionData> {
        const filePath = this.getSessionFilePath(sessionId);

        try {
            const content = await readFileUtf8(filePath);
            const sessionData = JSON.parse(content) as SessionData;

            // Validate session data structure
            if (!sessionData.sessionId || !sessionData.created || !sessionData.modified) {
                throw new Error("Invalid session data structure");
            }

            return sessionData;
        } catch (error) {
            this.logger.error("Failed to load session %s: %s", sessionId, error);
            throw new Error(`Failed to load session: ${String(error)}`);
        }
    }

    /**
     * Checks if a session exists
     */
    sessionExists(sessionId: string): boolean {
        const filePath = this.getSessionFilePath(sessionId);
        return fileExists(filePath);
    }

    /**
     * Gets metadata for all sessions (without loading full chat history)
     */
    async getAllSessionsMetadata(): Promise<SessionMetadata[]> {
        try {
            const files = await fs.promises.readdir(this.sessionsDir);
            const sessionFiles = files.filter((file) => file.endsWith(".json"));

            const metadata: SessionMetadata[] = [];

            for (const file of sessionFiles) {
                try {
                    const _sessionId = path.basename(file, ".json");
                    const filePath = path.join(this.sessionsDir, file);
                    const content = await readFileUtf8(filePath);
                    const sessionData = JSON.parse(content) as SessionData;

                    metadata.push({
                        sessionId: sessionData.sessionId,
                        created: sessionData.created,
                        modified: sessionData.modified,
                        exchangeCount: sessionData.chatHistory.length,
                    });
                } catch (error) {
                    this.logger.warn("Failed to read session metadata from %s: %s", file, error);
                    // Continue with other files
                }
            }

            // Sort by modified date (most recent first)
            metadata.sort(
                (a, b) => new Date(b.modified).getTime() - new Date(a.modified).getTime()
            );

            return metadata;
        } catch (error) {
            this.logger.error("Failed to get sessions metadata: %s", error);
            return [];
        }
    }

    /**
     * Gets the most recent session metadata
     */
    async getLastSessionMetadata(): Promise<SessionMetadata | null> {
        const allSessions = await this.getAllSessionsMetadata();
        return allSessions.length > 0 ? allSessions[0] : null;
    }

    /**
     * Restores an AgentState from a saved session
     */
    async restoreAgentState(sessionId: string): Promise<AgentState> {
        const sessionData = await this.loadSession(sessionId);

        // Create new AgentState with the saved data
        const agentState = new AgentState(
            undefined, // remoteAgentId - will be set by the agent loop
            sessionData.agentState.userGuidelines,
            sessionData.agentState.workspaceGuidelines,
            sessionData.agentState.agentMemories,
            sessionData.agentState.modelId || ""
        );

        // Restore the conversation ID to match the session
        // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access, @typescript-eslint/no-explicit-any
        (agentState as any)._conversationId = sessionId;

        // Restore chat history
        for (const exchange of sessionData.chatHistory) {
            agentState.addExchangeToHistory(exchange.exchange, exchange.completed, {
                sequenceId: exchange.sequenceId,
                finishedAt: exchange.finishedAt,
                changedFiles: exchange.changedFiles,
                changedFilesSkipped: exchange.changedFilesSkipped,
                changedFilesSkippedCount: exchange.changedFilesSkippedCount,
            });
        }

        this.logger.info("Agent state restored from session: %s", sessionId);
        return agentState;
    }

    /**
     * Deletes a session from disk
     */
    async deleteSession(sessionId: string): Promise<void> {
        const filePath = this.getSessionFilePath(sessionId);

        try {
            await fs.promises.unlink(filePath);
            this.logger.info("Session deleted: %s", sessionId);
        } catch (error) {
            if ((error as NodeJS.ErrnoException).code === "ENOENT") {
                // Session doesn't exist, that's fine
                return;
            }
            this.logger.error("Failed to delete session %s: %s", sessionId, error);
            throw new Error(`Failed to delete session: ${String(error)}`);
        }
    }
}
