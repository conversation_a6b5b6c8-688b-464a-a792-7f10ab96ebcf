import React from "react";

export const render = jest.fn().mockReturnValue({
    unmount: jest.fn(),
    rerender: jest.fn(),
    clear: jest.fn(),
    waitUntilExit: jest.fn().mockResolvedValue(undefined),
});

export const Box = jest.fn(({ children }) => children);
export const Text = jest.fn(({ children }) => children);
export const useApp = jest.fn().mockReturnValue({
    exit: jest.fn(),
});
export const useInput = jest.fn();
export const useStdout = jest.fn().mockReturnValue({
    stdout: process.stdout,
    write: jest.fn(),
});
export const useFocus = jest.fn().mockReturnValue({
    isFocused: false,
});
export const useFocusManager = jest.fn().mockReturnValue({
    focusNext: jest.fn(),
    focusPrevious: jest.fn(),
});