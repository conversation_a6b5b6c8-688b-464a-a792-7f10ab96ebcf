import { APIServer } from "../augment-api";

export class MockAPIServer implements Partial<APIServer> {
    createRequestId = jest.fn().mockReturnValue("mock-request-id");
    checkpointBlobs = jest.fn().mockResolvedValue({
        checkpointId: "mock-checkpoint-id",
        addedBlobs: [],
        deletedBlobs: [],
    });
    probeBlobs = jest.fn().mockResolvedValue({
        blobsStatus: {},
    });
    uploadBlobs = jest.fn().mockResolvedValue({
        uploadResults: {},
    });
}
