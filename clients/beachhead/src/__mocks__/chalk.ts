const chalk = {
    bold: jest.fn((text: string) => text),
    dim: jest.fn((text: string) => text),
    italic: jest.fn((text: string) => text),
    underline: jest.fn((text: string) => text),
    inverse: jest.fn((text: string) => text),
    strikethrough: jest.fn((text: string) => text),
    red: jest.fn((text: string) => text),
    green: jest.fn((text: string) => text),
    yellow: jest.fn((text: string) => text),
    blue: jest.fn((text: string) => text),
    magenta: jest.fn((text: string) => text),
    cyan: jest.fn((text: string) => text),
    white: jest.fn((text: string) => text),
    gray: jest.fn((text: string) => text),
    grey: jest.fn((text: string) => text),
    black: jest.fn((text: string) => text),
    rgb: jest.fn(() => (text: string) => text),
    hex: jest.fn(() => (text: string) => text),
    bgRed: jest.fn((text: string) => text),
    bgGreen: jest.fn((text: string) => text),
    bgYellow: jest.fn((text: string) => text),
    bgBlue: jest.fn((text: string) => text),
    bgMagenta: jest.fn((text: string) => text),
    bgCyan: jest.fn((text: string) => text),
    bgWhite: jest.fn((text: string) => text),
    bgBlack: jest.fn((text: string) => text),
};

// Make chalk chainable
Object.keys(chalk).forEach((key) => {
    Object.assign(chalk[key as keyof typeof chalk], chalk);
});

export default chalk;