// Mock for gradient-string
const gradient = {
    vice: jest.fn((text: string) => text),
    cristal: jest.fn((text: string) => text),
    teen: jest.fn((text: string) => text),
    mind: jest.fn((text: string) => text),
    morning: jest.fn((text: string) => text),
    fruit: jest.fn((text: string) => text),
    instagram: jest.fn((text: string) => text),
    atlas: jest.fn((text: string) => text),
    retro: jest.fn((text: string) => text),
    summer: jest.fn((text: string) => text),
    pastel: jest.fn((text: string) => text),
    rainbow: jest.fn((text: string) => text),
};

export default gradient;