import React from "react";

/**
 * Mock for hast-util-to-jsx-runtime
 * This mock simulates the behavior of toJsxRuntime for testing purposes
 */
export const toJsxRuntime = jest.fn((tree: any, options: any) => {
    const { Fragment, jsx, jsxs, components } = options;
    
    // Simple mock implementation that processes the tree
    const processNode = (node: any): any => {
        if (node.type === "text") {
            return node.value;
        }
        
        if (node.type === "element") {
            const Component = components[node.tagName] || components[Symbol.for("hast.element")] || "span";
            const props: any = {};
            
            // Convert properties to props
            if (node.properties) {
                if (node.properties.className) {
                    props.className = node.properties.className;
                }
            }
            
            const children = node.children ? node.children.map(processNode) : null;
            
            // Use React.createElement for compatibility
            return React.createElement(Component, props, children);
        }
        
        if (node.type === "root") {
            const children = node.children ? node.children.map(processNode) : [];
            return React.createElement(Fragment, null, children);
        }
        
        return null;
    };
    
    return processNode(tree);
});
