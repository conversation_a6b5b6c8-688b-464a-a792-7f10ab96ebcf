// Mock for lowlight module
export const createLowlight = jest.fn(() => ({
    highlight: jest.fn((language: string, code: string) => ({
        type: "root",
        children: [
            {
                type: "element",
                tagName: "span",
                properties: { className: ["hljs-keyword"] },
                children: [{ type: "text", value: "const" }],
            },
            { type: "text", value: " " },
            {
                type: "element",
                tagName: "span",
                properties: { className: ["hljs-variable"] },
                children: [{ type: "text", value: "x" }],
            },
            { type: "text", value: " = " },
            {
                type: "element",
                tagName: "span",
                properties: { className: ["hljs-number"] },
                children: [{ type: "text", value: "42" }],
            },
            { type: "text", value: ";" },
        ],
    })),
    highlightAuto: jest.fn((code: string) => ({
        type: "root",
        children: [{ type: "text", value: code }],
    })),
    listLanguages: jest.fn(() => ["javascript", "typescript", "python", "bash"]),
    register: jest.fn(),
    registered: jest.fn((name: string) =>
        ["javascript", "typescript", "python", "bash"].includes(name)
    ),
}));

// Mock common language imports that might be used
export const common = ["javascript", "typescript", "python", "bash"];
