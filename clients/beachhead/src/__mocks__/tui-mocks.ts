// Centralized TUI mocks for testing
import type { FC, ReactNode } from "react";

// Ink mocks
export const mockInk = {
    render: jest.fn().mockReturnValue({
        unmount: jest.fn(),
        rerender: jest.fn(),
        clear: jest.fn(),
        waitUntilExit: jest.fn().mockResolvedValue(undefined),
    }),
    Box: jest.fn(({ children }: { children: ReactNode }) => children),
    Text: jest.fn(({ children }: { children: ReactNode }) => children),
    useApp: jest.fn().mockReturnValue({
        exit: jest.fn(),
    }),
    useInput: jest.fn(),
    useStdout: jest.fn().mockReturnValue({
        stdout: process.stdout,
        write: jest.fn(),
    }),
    useFocus: jest.fn().mockReturnValue({
        isFocused: false,
    }),
    useFocusManager: jest.fn().mockReturnValue({
        focusNext: jest.fn(),
        focusPrevious: jest.fn(),
    }),
};

// Chalk mock with proper chaining
export const createChalkMock = () => {
    const chalkMethods = [
        'bold', 'dim', 'italic', 'underline', 'inverse', 'strikethrough',
        'red', 'green', 'yellow', 'blue', 'magenta', 'cyan', 'white',
        'gray', 'grey', 'black', 'bgRed', 'bgGreen', 'bgYellow', 'bgBlue',
        'bgMagenta', 'bgCyan', 'bgWhite', 'bgBlack'
    ];

    const chalk: any = {};
    
    // Create chainable methods
    chalkMethods.forEach(method => {
        chalk[method] = jest.fn((text: string) => text);
        // Make each method return chalk for chaining
        Object.assign(chalk[method], chalk);
    });

    // Special methods
    chalk.rgb = jest.fn(() => (text: string) => text);
    chalk.hex = jest.fn(() => (text: string) => text);
    Object.assign(chalk.rgb(), chalk);
    Object.assign(chalk.hex(), chalk);

    return chalk;
};

// TextInput mock
export const mockTextInput: FC<any> = jest.fn().mockImplementation(() => null);
