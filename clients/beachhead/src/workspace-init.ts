import { Span } from "@opentelemetry/api";
import { <PERSON><PERSON><PERSON> } from "buffer";
import { spawn } from "child_process";
import * as fs from "fs";
import * as os from "os";
import * as path from "path";
import { Writable } from "stream";

import { AgentConfig } from "./agent-config";
import { getLogger } from "./logging";
import { getTracer } from "./otel";
import {
    RemoteWorkspaceSetupStatus,
    RemoteWorkspaceSetupStep,
    RemoteWorkspaceSetupStepStatus,
} from "./remote-agent-manager/types";
import { SetupPersistence } from "./setup-persistence";

/**
 * Custom error class for git operation failures
 */
export class GitError extends Error {
    constructor(commandName: string, originalError?: Error) {
        super(
            `Git operation failed: ${commandName}${originalError ? ` - ${originalError.message}` : ""}`
        );
        this.name = "GitError";
    }
}

export enum CommandStatus {
    PENDING = "PENDING",
    RUNNING = "RUNNING",
    SUCCESS = "SUCCESS",
    SKIPPED = "SKIPPED",
    FAILED = "FAILED",
}

// CommandFunc is an async function which is called by Command. It takes
// a write buffer for stdout/stderr. It returns false if the command was SKIPPED,
// returns true if the command was SUCCESS, and throws and error if the command
// FAILED.
export type CommandFunc = (out: Writable) => Promise<boolean>;

export class Command {
    private _name: string;
    private _func: CommandFunc;
    private _status: CommandStatus;
    private _output: string;
    private _sequence_id: number;
    private _last_sent_output_length: number;
    private _last_sent_status: CommandStatus;
    private _fatal: boolean;
    private _error: Error | undefined;

    constructor(name: string, func: CommandFunc, fatal: boolean = false) {
        this._name = name;
        this._func = func;
        this._status = CommandStatus.PENDING;
        this._output = "";
        this._sequence_id = 0;
        this._last_sent_output_length = 0;
        this._last_sent_status = CommandStatus.PENDING;
        this._fatal = fatal;
        this._error = undefined;
    }

    public name(): string {
        return this._name;
    }

    public status(): CommandStatus {
        return this._status;
    }

    // output is the cummulative output, so far
    public output(): string {
        return this._output;
    }

    public sequence_id(): number {
        return this._sequence_id;
    }

    public increment_sequence_id(): void {
        this._sequence_id++;
    }

    public last_sent_output_length(): number {
        return this._last_sent_output_length;
    }

    public last_sent_status(): CommandStatus {
        return this._last_sent_status;
    }

    public set_last_sent_output_length(length: number): void {
        this._last_sent_output_length = length;
    }

    public set_last_sent_status(status: CommandStatus): void {
        this._last_sent_status = status;
    }

    public fatal(): boolean {
        return this._fatal;
    }

    public error(): Error | undefined {
        return this._error;
    }

    public async run(): Promise<void> {
        const out = new Writable({
            write: (chunk: Buffer, encoding: string, callback: Function) => {
                if (!Buffer.isBuffer(chunk)) {
                    callback(new Error("Expected chunk to be a Buffer"));
                    return;
                }
                const text = chunk.toString();
                this._output += text;
                callback();
            },
        });

        this._status = CommandStatus.RUNNING;
        try {
            if (await this._func(out)) {
                this._status = CommandStatus.SUCCESS;
            } else {
                this._status = CommandStatus.SKIPPED;
            }
        } catch (err) {
            this._status = CommandStatus.FAILED;
            // The error will be set by the specific command function if it's a git operation
            // Otherwise, create a generic error
            if (!this._error) {
                this._error = err instanceof Error ? err : new Error(err as string);
            }
            this._output += `${this._error.message}`;
        } finally {
            out.end();
            // if output is non-empty, and output doesn't end in a newline add one
            if (this._output.length > 0 && !this._output.endsWith("\n")) {
                this._output += "\n";
            }
        }
    }
}

export class InitCommands {
    private _cfg: AgentConfig;
    private _path: string;
    private _cmds: Command[];
    private _skipped_due_to_persistence = false;
    private _setupPersistence: SetupPersistence;

    constructor(cfg: AgentConfig, workspacePath: string, persistentRoot?: string) {
        this._cfg = cfg;
        this._path = workspacePath;
        // Use persistentRoot for setup persistence if available, otherwise fall back to workspace path
        const setupPersistenceDir = persistentRoot
            ? path.join(persistentRoot, "setup_state")
            : workspacePath;
        this._setupPersistence = new SetupPersistence(setupPersistenceDir);
        this._cmds = [
            new Command("git lfs install", this.git_lfs_install.bind(this), false), // non-fatal
            new Command("git clone", this.git_clone.bind(this), true), // fatal
            new Command("git submodule update", this.git_submodule_update.bind(this), false), // non-fatal
            new Command("git apply", this.apply_patch.bind(this), true), // fatal
            new Command("setup script", this.run_setup_script.bind(this), false), // non-fatal
        ];
    }

    public async run(): Promise<boolean> {
        const log = getLogger("Beachhead");

        // Check if setup script has already been completed - this means all init commands were successful
        if (this._setupPersistence.isSetupScriptCompleted()) {
            log.info(`Setup script already completed, skipping all init commands.`);
            this._skipped_due_to_persistence = true;
            return false;
        }

        if (fs.existsSync(this._path) && fs.readdirSync(this._path).length > 0) {
            log.info(
                `Workspace already exists and is not empty (path: ${this._path}), skipping init.`
            );
            return false;
        }

        log.info(`Initializing workspace at ${this._path}.`);

        for (const cmd of this._cmds) {
            await getTracer().startActiveSpan(`initCommand.${cmd.name()}`, async (span: Span) => {
                log.info(`Init Command [${cmd.name()}]: running...`);
                try {
                    await cmd.run();
                } finally {
                    span.end();
                }
            });
            log.info(
                `Init Command [${cmd.name()}]: exited ${cmd.status()}: OUTPUT_START:\n${cmd.output()}OUTPUT_END`
            );
            if (cmd.status() === CommandStatus.FAILED && cmd.fatal()) {
                break;
            }
        }

        return true;
    }

    private async git_clone(out: Writable): Promise<boolean> {
        // NOTE(mattm): This relies on the git credential helper configured in init.sh.
        const log = getLogger("Beachhead");
        const ref = this._cfg.git_ref();

        const startTime = Date.now();

        log.info(`Starting single-branch clone of ${this._cfg.git_repo()} branch ${ref}`);
        let branchCloneFailed = false;
        try {
            await this._spawn(out, [
                "git",
                "clone",
                "--single-branch",
                "--branch",
                ref,
                "--depth",
                "1",
                this._cfg.git_repo(),
                this._path,
            ]);
        } catch (err) {
            const originalError = err instanceof Error ? err : new Error(err as string);
            log.error(`Failed to clone: ${originalError.message}`);
            branchCloneFailed = true;
        }

        // In case the repo is empty and has no branches, try again without
        // --branch, but only succeed if we the resulting branches match. This
        // is a bit of a hack
        // TODO: Maybe a better long term solution would be to call
        // /github/get-repo and add an "isEmpty" field to that response that we
        // could check here?
        if (branchCloneFailed) {
            out.write("Repo may be empty, trying again without --branch...");
            try {
                await this._spawn(out, [
                    "git",
                    "clone",
                    "--single-branch",
                    "--depth",
                    "1",
                    this._cfg.git_repo(),
                    this._path,
                ]);
            } catch (err) {
                const originalError = err instanceof Error ? err : new Error(err as string);
                throw new GitError("git clone", originalError);
            }

            // Get the current branch
            let currentBranch = "";
            const branchOut = new Writable({
                write: (chunk: Buffer, encoding: string, callback: Function) => {
                    if (!Buffer.isBuffer(chunk)) {
                        callback(new Error("Expected chunk to be a Buffer"));
                        return;
                    }
                    const text = chunk.toString();
                    currentBranch += text;
                    callback();
                },
            });
            await this._spawn(branchOut, ["git", "-C", this._path, "branch", "--show-current"]);
            if (currentBranch.trim() !== ref) {
                out.write(
                    `Default branch ${currentBranch} does not match requested branch ${ref}, failing...`
                );
                const entries = fs.readdirSync(this._path);
                for (const entry of entries) {
                    fs.rmSync(path.join(this._path, entry), { recursive: true, force: true });
                }
                throw new GitError(
                    "git clone",
                    new Error(`unable to clone repo ${this._cfg.git_repo()} branch ${ref}`)
                );
            }
        }

        const duration = Date.now() - startTime;
        log.info(`Git clone completed in ${duration}ms`);
        return true;
    }

    private async git_lfs_install(out: Writable): Promise<boolean> {
        const log = getLogger("Beachhead");

        log.info("Starting git lfs install");
        try {
            await this._spawn(out, ["git", "lfs", "install"]);
            log.info("Git lfs install completed successfully");
            return true;
        } catch (err) {
            const originalError = err instanceof Error ? err : new Error(err as string);
            log.warn(`Git lfs install failed (non-fatal): ${originalError.message}`);
            // This is a non-fatal operation, so we create a GitError and let it propagate
            throw new GitError("git lfs install", originalError);
        }
    }

    private async git_submodule_update(out: Writable): Promise<boolean> {
        const log = getLogger("Beachhead");

        log.info("Starting recursive submodule update");
        try {
            await this._spawn(out, [
                "git",
                "-C",
                this._path,
                "submodule",
                "update",
                "--init",
                "--recursive",
            ]);
            log.info("Git submodule update completed successfully");
            return true;
        } catch (err) {
            const originalError = err instanceof Error ? err : new Error(err as string);
            log.warn(`Git submodule update failed (non-fatal): ${originalError.message}`);
            // This is a non-fatal operation, so we create a GitError and let it propagate
            throw new GitError("git submodule update", originalError);
        }
    }

    private async apply_patch(out: Writable): Promise<boolean> {
        const patch = this._cfg.initial_patch();
        if (!patch) {
            return false;
        }
        await this._spawn(out, ["git", "-C", this._path, "apply", "-"], patch);
        return true;
    }

    private async run_setup_script(out: Writable): Promise<boolean> {
        const script = this._cfg.setup_script();
        if (!script) {
            out.write("No setup script provided. Skipping.\n");
            return false;
        }

        // Check if setup script has already been completed
        if (this._setupPersistence.isSetupScriptCompleted()) {
            out.write("Setup script already completed. Skipping.\n");
            return false;
        }

        // Strip any Windows CRLF line endings.
        const script_unix = script.replaceAll("\r\n", "\n");

        const script_path = `/var/tmp/user-${os.userInfo().uid}/workspace-setup.sh`;
        await fs.promises.mkdir(path.dirname(script_path), { recursive: true });
        await fs.promises.writeFile(script_path, script_unix);
        await fs.promises.chmod(script_path, 0o755);
        await this._spawn(out, [script_path], "", this._path);

        // Mark setup script as completed
        await this._setupPersistence.markSetupScriptCompleted();
        return true;
    }

    private async _spawn(
        out: Writable,
        args: string[],
        stdin: string = "",
        cwd: string | undefined = undefined
    ): Promise<void> {
        const log = getLogger("Beachhead");
        log.info(`Init Spawning: ${args.join(" ")}.`);
        return new Promise<void>((resolve, reject) => {
            const proc = spawn(args[0], args.slice(1), {
                stdio: [
                    stdin ? "pipe" : "ignore", // stdin
                    "pipe", // stdout
                    "pipe", // stderr
                ],
                cwd: cwd,
                shell: false,
            });
            if (stdin) {
                proc.stdin?.write(stdin, "utf-8");
                proc.stdin?.end();
            }
            proc.on("error", (err: Error) => {
                reject(new Error(`Failed to start process: ${err.message}`));
            });
            proc.stdout?.on("data", (data: Buffer) => {
                out.write(data);
            });
            proc.stderr?.on("data", (data: Buffer) => {
                out.write(data);
            });
            // NOTE(mpauly): We changed from "close" to "exit" because the "close" event isn't fired
            // until all background processes finished. This causes setup scripts to hang when they
            // launched background processes, which is non-intuitive and prevents use cases like
            // starting a server in the setup script. It's possible that this change will lead to
            // logs to be dropped when the process exists, so if that's being reported by users we
            // should revisit this.
            proc.on("exit", (code: number) => {
                if (code !== 0) {
                    reject(new Error(`Non-zero exit code: ${code}`));
                } else {
                    resolve();
                }
            });
        });
    }

    public commands(): Command[] {
        return this._cmds;
    }

    public latest_command(): Command | undefined {
        if (this._cmds.length === 0) {
            return undefined;
        }
        return this._cmds[this._cmds.length - 1];
    }

    public overall_status(): CommandStatus {
        // If any command is RUNNING, then RUNNING.
        // If any command is FAILED, then FAILED.
        // If all commands are SKIPPED, then SKIPPED (this should include empty list).
        // If all commands are PENDING, then PENDING.
        // Otherwise, SUCCESS.
        let all_skipped = true;
        let all_pending = true;
        for (const cmd of this._cmds) {
            if (cmd.status() === CommandStatus.RUNNING) {
                return CommandStatus.RUNNING;
            }
            if (cmd.status() === CommandStatus.FAILED) {
                return CommandStatus.FAILED;
            }
            if (cmd.status() !== CommandStatus.PENDING) {
                all_pending = false;
            }
            if (cmd.status() !== CommandStatus.SKIPPED) {
                all_skipped = false;
            }
        }
        if (all_skipped) {
            return CommandStatus.SKIPPED;
        }
        if (all_pending) {
            return CommandStatus.PENDING;
        }
        return CommandStatus.SUCCESS;
    }

    public failed(): boolean {
        return this.overall_status() === CommandStatus.FAILED;
    }

    public skipped_due_to_persistence(): boolean {
        return this._skipped_due_to_persistence;
    }

    public workspace_setup_status(): RemoteWorkspaceSetupStatus {
        let steps: RemoteWorkspaceSetupStep[] = [];
        for (let i = 0; i < this._cmds.length; i++) {
            const cmd = this._cmds[i];
            const current_logs = cmd.output();
            const current_length = cmd.last_sent_output_length();
            // Check if there are new logs by comparing lengths
            if (current_logs.length > current_length || cmd.status() !== cmd.last_sent_status()) {
                cmd.increment_sequence_id();

                // Calculate only the new logs that were added since last update
                // Extract only the new part of the logs using the stored index
                const logs = current_logs.substring(cmd.last_sent_output_length());

                // Update the last sent output length to the current full logs length
                cmd.set_last_sent_output_length(current_logs.length);
                cmd.set_last_sent_status(cmd.status());

                steps.push({
                    step_description: cmd.name(),
                    logs: logs,
                    status:
                        cmd.status() === CommandStatus.RUNNING
                            ? RemoteWorkspaceSetupStepStatus.running
                            : cmd.status() === CommandStatus.SUCCESS
                              ? RemoteWorkspaceSetupStepStatus.success
                              : cmd.status() === CommandStatus.FAILED
                                ? RemoteWorkspaceSetupStepStatus.failure
                                : cmd.status() === CommandStatus.SKIPPED
                                  ? RemoteWorkspaceSetupStepStatus.skipped
                                  : RemoteWorkspaceSetupStepStatus.unknown,
                    sequence_id: cmd.sequence_id(),
                    step_number: i,
                });
            }
        }
        return {
            steps: steps,
        };
    }
}
