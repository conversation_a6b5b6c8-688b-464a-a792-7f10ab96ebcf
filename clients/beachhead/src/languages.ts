/* eslint-disable @typescript-eslint/no-unsafe-argument */

/* eslint-disable @typescript-eslint/no-unsafe-member-access */

/* eslint-disable @typescript-eslint/no-unsafe-assignment */
import { Language } from "./augment-api";

const EXTENSIONS_JSON = require("../../data/file-ext/augment_supported_extensions.json");

export const defaultSupportedLanguages: Language[] = [];
export const defaultSupportedFileExtensions = new Set<string>();

for (const languageObj of EXTENSIONS_JSON) {
    defaultSupportedLanguages.push({
        name: languageObj.name,
        vscodeName: languageObj.vscodeName,
        extensions: languageObj.extensions,
    });
    for (const fileExt of languageObj.extensions) {
        defaultSupportedFileExtensions.add(fileExt);
    }
}
