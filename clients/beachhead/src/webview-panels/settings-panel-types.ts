import { ToolDefinitionWithSettings } from "@augment-internal/sidecar-libs/src/tools/tool-types";

export const CONFIG_VERSION = 1;

// Core state types
export interface ToolConfigState {
    version: number;
    tools: ToolSettings[];
}

export interface ToolSettings {
    config: any;
    isConfigured: boolean;
    name: string;
}

// Message types
export interface ToolConfigSaveRequest {
    toolConfig: string;
    isConfigured: boolean;
    toolName: string;
}

export interface ToolConfigInitData {
    toolConfigs: ToolSettings[];
    hostTools: ToolDefinitionWithSettings[];
    enableDebugFeatures?: boolean;
    enableAgentMode?: boolean;
}

// Error types
export class ToolConfigError extends Error {
    constructor(message: string) {
        super(message);
        this.name = "ToolConfigError";
    }
}

export class ToolConfigIndexError extends ToolConfigError {
    constructor(index: number) {
        super(`Invalid tool config index: ${index}`);
        this.name = "ToolConfigIndexError";
    }
}

export class ToolConfigParseError extends ToolConfigError {
    constructor(message: string) {
        super(`Failed to parse tool config: ${message}`);
        this.name = "ToolConfigParseError";
    }
}
