import { getErrmsg } from "@augment-internal/sidecar-libs/src/exceptions";
import {
    RemoteInfoSource,
    RemoteToolDefinition,
    RunRemoteToolResult,
} from "@augment-internal/sidecar-libs/src/tools/remote-tools/remote-tool-host";
import {
    GitHubToolExtraInput,
    RemoteToolId,
    ToolAvailabilityStatus,
} from "@augment-internal/sidecar-libs/src/tools/tool-types";

import { APIServer } from "./augment-api";
import { getLogger } from "./logging";

/**
 * Provides an interface for the beachhead client to communicate with remote tools on the Augment backend.
 * This implementation handles tool discovery, execution, and safety checking for remote tools.
 */
export class BeachheadRemoteInfo implements RemoteInfoSource {
    private _logger = getLogger("BeachheadRemoteInfo");

    constructor(
        private readonly _apiServer: APIServer,
        private readonly _setupMode: boolean,
        private readonly _githubApiToken?: string
    ) {}

    public async retrieveRemoteTools(
        supportedTools: RemoteToolId[]
    ): Promise<RemoteToolDefinition[]> {
        if (this._setupMode) {
            return [];
        }
        try {
            const apiResult = await this._apiServer.listRemoteTools(supportedTools);
            const availableTools = apiResult.tools.filter((tool) => {
                // Include tools that are Available
                if (tool.availabilityStatus === ToolAvailabilityStatus.Available) {
                    return true;
                }

                // Include tools that are UserConfigRequired if we have the required extra input
                if (tool.availabilityStatus === ToolAvailabilityStatus.UserConfigRequired) {
                    switch (tool.remoteToolId) {
                        case RemoteToolId.GitHubApi:
                            return !!this._githubApiToken;
                        default:
                            return false;
                    }
                }

                return false;
            });
            return availableTools;
        } catch (e: any) {
            this._logger.error("Failed to list remote tools: %s", getErrmsg(e));
            return [];
        }
    }

    public filterToolsWithExtraInput(toolIds: RemoteToolId[]): Promise<Set<RemoteToolId>> {
        // Filter tools based on whether we have the required credentials/configuration
        const availableTools = new Set<RemoteToolId>();

        for (const toolId of toolIds) {
            switch (toolId) {
                case RemoteToolId.GitHubApi:
                    // GitHub API tool is available if we have a GitHub API token
                    if (this._githubApiToken) {
                        availableTools.add(toolId);
                    }
                    break;
                // Add other tools here as needed when they support extra input in beachhead
                default:
                    // For tools we don't explicitly handle, assume they're not available
                    break;
            }
        }

        return Promise.resolve(availableTools);
    }

    public async runRemoteTool(
        toolRequestId: string,
        toolName: string,
        toolInputJson: string,
        toolId: RemoteToolId,
        signal: AbortSignal
    ): Promise<RunRemoteToolResult> {
        // Provide GitHub API token if available and tool is GitHub API
        let extraToolInput: GitHubToolExtraInput | undefined;
        if (toolId === RemoteToolId.GitHubApi && this._githubApiToken) {
            extraToolInput = {
                apiToken: this._githubApiToken,
            };
        }

        return await this._apiServer.runRemoteTool(
            toolRequestId,
            toolName,
            toolInputJson,
            toolId,
            extraToolInput,
            signal
        );
    }
}
