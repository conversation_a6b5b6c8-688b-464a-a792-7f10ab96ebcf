import express from "express";
import http from "http";

import { <PERSON><PERSON><PERSON> } from "./agent_loop/agent_loop";
import { getLogger } from "./logging";
import { RemoteAgentStatus } from "./remote-agent-manager/types";
import { initializeExpressErrorHandler } from "./sentry-config";
import { SSHManager } from "./ssh";

export function startServer(port: number, agentLoop: AgentLoop, ssh: SSHManager) {
    const app = express();
    const server = http.createServer(app);

    const logger = getLogger("Beachhead");

    // Add middleware to parse JSON and URL-encoded bodies
    app.use(express.json({ strict: false }));
    app.use(express.urlencoded({ extended: true }));

    // Add health check endpoint
    app.get("/health", (req: express.Request, res: express.Response) => {
        res.status(200).json({
            status: "OK",
            agent_working: agentLoop.status() === RemoteAgentStatus.agentRunning,
        });
    });

    // eslint-disable-next-line @typescript-eslint/no-misused-promises
    app.post("/ssh/authorized_keys", async (req: express.Request, res: express.Response) => {
        try {
            // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
            if (!req.body || typeof req.body !== "object" || !Array.isArray(req.body.public_keys)) {
                return res.status(400).json({
                    error: "Request body must be a JSON object with a 'public_keys' array",
                    body_value: JSON.stringify(req.body),
                });
            }
            // eslint-disable-next-line @typescript-eslint/no-unsafe-argument, @typescript-eslint/no-unsafe-member-access
            await ssh.appendAuthorizedKeys(req.body.public_keys);
            res.status(200).json(await ssh.sshConfig());
        } catch (error) {
            const errorStr = error instanceof Error ? error.message : String(error);
            logger.error(`Error in SSH authorized_keys endpoint: ${errorStr}`);
            res.status(500).json({ error: errorStr });
        }
    });

    // eslint-disable-next-line @typescript-eslint/no-misused-promises
    app.get("/ssh/config", async (_req: express.Request, res: express.Response) => {
        res.status(200).json(await ssh.sshConfig());
    });

    // Sentry error handling after all routes are defined
    initializeExpressErrorHandler(app);

    // Start the server
    server.listen(port, () => {
        logger.info(`Web server listening on port ${port}`);
    });

    // Setup graceful shutdown
    process.on("SIGTERM", () => {
        logger.info("SIGTERM signal received: closing HTTP server");
        server.close(() => {
            logger.info("HTTP server closed");
        });
    });
}
