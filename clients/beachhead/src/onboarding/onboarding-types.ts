/**
 * OnboardingSessionEventName is the name of an onboarding session event.
 * Matches IntelliJ's OnboardingSessionEventName.api_names.
 */
/* eslint-disable @typescript-eslint/naming-convention */
export enum OnboardingSessionEventName {
    SignedIn = "signed-in",
    StartedSyncing = "started-syncing",
    FinishedSyncing = "finished-syncing",
    SawSummary = "saw-summary",
    UsedChat = "used-chat",
    AcceptedCompletion = "accepted-completion",
    UsedSlashAction = "used-slash-action",
}
/*eslint-enable @typescript-eslint/naming-convention */
