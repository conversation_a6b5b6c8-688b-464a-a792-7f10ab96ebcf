import Denque from "denque";

/**
 * AsyncQueue provides a queue with asynchronous operations.
 * It allows consumers to wait for items when the queue is empty,
 * and automatically resolves waiting consumers when new items are added.
 *
 * Uses Denque for efficient queue operations with O(1) complexity.
 *
 * @example
 * ```typescript
 * const queue = new AsyncQueue<string>();
 *
 * // Consumer
 * async function consumer() {
 *   while (true) {
 *     const item = await queue.pop(); // Waits if queue is empty
 *     console.log(`Processed: ${item}`);
 *   }
 * }
 *
 * // Producer
 * function producer() {
 *   queue.push("task1");
 *   queue.push("task2");
 * }
 *
 * consumer();
 * producer();
 * ```
 */
export class AsyncQueue<T> {
    private queue = new Denque<T>();
    private waiters = new Denque<(value: T) => void>();

    push(item: T): void {
        if (this.waiters.length > 0) {
            // If someone is waiting, give them the item directly
            const waiter = this.waiters.shift()!;
            waiter(item);
        } else {
            // Otherwise add to queue
            this.queue.push(item);
        }
    }

    async pop(): Promise<T> {
        if (this.queue.length > 0) {
            // If items available, return immediately
            return this.queue.shift()!;
        }

        // Otherwise wait for an item
        return new Promise<T>((resolve) => {
            this.waiters.push(resolve);
        });
    }

    get size(): number {
        return this.queue.length;
    }

    clear(): void {
        this.queue.clear();
    }
}
