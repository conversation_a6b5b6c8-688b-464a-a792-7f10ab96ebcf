/* eslint-disable no-console */
import { extractStrReplaceEntries } from "@augment-internal/sidecar-libs/src/tools/sidecar-tools/str-replace-editor-tool/utils";
import { ToolUseResponse } from "@augment-internal/sidecar-libs/src/tools/tool-types";
import { createTwoFilesPatch } from "diff";

import { OutputConfig, OutputVerbosity } from "../cli/options/output";
import {
    formatCliContent,
    formatCliHeader,
    formatCliSection,
    formatDiff,
    formatLaunchProcessOutput,
    formatToolCallParameters,
} from "../cli/utils/interactive-formatting-utils";
import { AgentLoopEventListener } from "./agent_loop";

/**
 * Text mode event listener that handles CLI output formatting.
 * This listener is used when mode is 'text' (for non-interactive, text-mode, and quit).
 * TUI mode uses a separate TUIAgentEventListener.
 */
export class TextEventListener implements AgentLoopEventListener {
    private hasShownAssistantHeader = false;
    private verbosity: OutputVerbosity;
    private finalMessage: string = "";

    constructor(config: OutputConfig) {
        this.verbosity = config.verbosity;
    }

    onAssistantResponseStart(): void {
        if (
            this.verbosity === OutputVerbosity.DEFAULT ||
            this.verbosity === OutputVerbosity.COMPACT
        ) {
            // In default and compact modes, show header immediately and start streaming
            if (!this.hasShownAssistantHeader) {
                console.log(formatCliHeader("ASSISTANT", true));
                this.hasShownAssistantHeader = true;
            }
        } else {
            // In quiet mode, reset accumulator for the final message
            this.finalMessage = "";
        }
    }

    onAssistantResponseChunk(text: string): void {
        if (
            this.verbosity === OutputVerbosity.DEFAULT ||
            this.verbosity === OutputVerbosity.COMPACT
        ) {
            // Stream in default and compact modes
            process.stdout.write(text);
        } else {
            // Accumulate in quiet mode - only show the last message
            this.finalMessage += text;
        }
    }

    onAssistantResponseEnd(): void {
        if (
            this.verbosity === OutputVerbosity.DEFAULT ||
            this.verbosity === OutputVerbosity.COMPACT
        ) {
            // Add newline to finish the streamed response
            if (this.hasShownAssistantHeader) {
                console.log();
            }
            // Reset state for next exchange
            this.hasShownAssistantHeader = false;
        }
        // In quiet mode, don't print anything yet - wait for agent loop to complete
    }

    onAgentLoopComplete(): void {
        if (this.verbosity === OutputVerbosity.QUIET) {
            // In quiet mode, print only the final message when everything is complete
            console.log(this.finalMessage);
        }
    }

    onToolCallStart(toolName: string, toolInput: Record<string, any>): void {
        // Only show tool calls in default and compact modes
        if (this.verbosity === OutputVerbosity.QUIET) {
            return;
        }

        console.log(formatCliSection(`🔧 Tool call: ${toolName}`));

        // Only display section heading in compact mode
        if (this.verbosity === OutputVerbosity.COMPACT) {
            return;
        }
        let toolHandled = false;

        // Generate and display diff for str-replace-editor tool calls
        if (toolName === "str-replace-editor") {
            toolHandled = this.handleStrReplaceEditorInput(toolInput);
        }

        if (!toolHandled) {
            this.handleGeneralToolInput(toolName, toolInput);
        }
    }

    onToolCallResult(toolName: string, toolResult: ToolUseResponse): void {
        // Only show tool results in default and compact modes
        if (this.verbosity === OutputVerbosity.QUIET) {
            return;
        }

        if (this.verbosity === OutputVerbosity.COMPACT) {
            // In compact mode, show tool result in a single line
            const status = toolResult.isError ? "❌" : "✅";
            console.log(`📋 ${toolName} ${status}`);
            return;
        }

        // Default mode: show full tool result details
        console.log(formatCliSection(`📋 Tool result: ${toolName}`));
        if (toolResult.isError) {
            console.log("❌ Error:");

            // Special handling for GitHub authentication errors
            if (toolName === "github-api" && this.isGitHubAuthError(toolResult.text)) {
                console.log(formatCliContent(toolResult.text, 15));
                console.log(`
💡 GitHub Authentication Help:
   Your GitHub token appears to be invalid, expired, or missing required permissions.

   To fix this, generate a new GitHub Personal Access Token:
   1. Go to: GitHub → Settings → Developer settings → Personal access tokens → Tokens (classic)
   2. Generate a new token with these scopes:
      • repo (Full control of private repositories)
      • user:email (Access to user email addresses)
   3. Replace your token file content or set GITHUB_API_TOKEN environment variable
`);
            } else if (toolName === "launch-process") {
                // Use special formatting for launch-process output even in error cases
                console.log(formatLaunchProcessOutput(toolResult.text));
            } else {
                console.log(formatCliContent(toolResult.text, 15));
            }
        } else {
            // Only show special indicator for errors, not for success
            // Use special formatting for different tool types
            if (toolName === "launch-process") {
                console.log(formatLaunchProcessOutput(toolResult.text));
            } else {
                console.log(formatCliContent(toolResult.text, 15));
            }
        }
    }

    private isGitHubAuthError(errorText: string): boolean {
        // Check for common GitHub authentication error patterns
        return (
            errorText.includes("Authentication failed") ||
            errorText.includes("Bad credentials") ||
            errorText.includes("401") ||
            errorText.includes("Unauthorized") ||
            errorText.includes("Invalid token") ||
            errorText.includes("Token expired")
        );
    }

    /**
     * Generate and display simple diff for str-replace-editor tool calls
     */
    private handleStrReplaceEditorInput(toolInput: Record<string, any>): boolean {
        const command = toolInput.command as string;

        if (command === "str_replace") {
            const filePath = toolInput.path as string;

            if (!filePath) {
                return false;
            }

            // Extract all str_replace entries using the utility function
            const entries = extractStrReplaceEntries(toolInput);

            if (entries.length === 0) {
                return false;
            }

            // Generate and display diff for each replacement entry
            for (const entry of entries) {
                const oldStr = entry.old_str as string;
                const newStr = entry.new_str as string;

                if (oldStr && newStr !== undefined) {
                    // Generate simple diff between old and new strings
                    const diff = createTwoFilesPatch(
                        filePath,
                        filePath,
                        oldStr,
                        newStr,
                        "before",
                        "after",
                        { context: 0 }
                    );

                    // Only include the diff content (skip the file headers)
                    const diffLines = diff.split("\n");
                    const diffContent = diffLines.slice(2).join("\n"); // Skip the first two lines (--- and +++)

                    if (diffContent.trim()) {
                        // Display the diff with entry index if there are multiple entries
                        if (entries.length > 1) {
                            console.log(formatDiff(`Entry ${entry.index}:\n${diffContent}`));
                        } else {
                            console.log(formatDiff(diffContent));
                        }
                    }
                }
            }

            return true;
        }

        return false;
    }

    handleGeneralToolInput(_toolName: string, toolInput: Record<string, any>): void {
        const formattedInput = formatToolCallParameters(toolInput);
        // Indent the parameters by adding 3 spaces to each line
        const indentedInput = formattedInput
            .split("\n")
            .map((line) => `   ${line}`)
            .join("\n");
        console.log(formatCliContent(indentedInput, 15));
    }

    onError(_error: unknown, _context?: string): void {
        // For now, we don't add any special CLI error handling
        // The existing error logging in AgentLoop will handle this
    }

    onMaxIterationsExceeded(maxIterations: number): void {
        console.log(
            formatCliContent(
                `\n⚠️  The conversation has been paused because maximum iterations reached (${maxIterations}).\n` +
                    `You can continue the conversation by running the cli with -c command.\n`,
                11 // Warning color (yellow)
            )
        );
    }
}
