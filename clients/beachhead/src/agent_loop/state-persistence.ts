import * as fs from "fs";
import * as path from "path";

import { getLogger } from "../logging";
import { fileExists, makeDirsSync, readFileUtf8, writeFileUtf8 } from "../utils/fs-utils";
import { AgentState, ExchangeState } from "./state";

interface SerializedState {
    // Original AgentState data (serialized)
    agentState: string;
    // Metadata for easier querying without parsing the full state
    timestamp: string;
}

/**
 * StatePersistence handles persisting AgentState objects to files.
 * It provides methods to save and load agent state.
 */
export class StatePersistence {
    private logger = getLogger("StatePersistence");
    private directoryPath: string;

    /**
     * Creates a new StatePersistence instance.
     *
     * @param directoryPath The directory path where state files will be stored
     */
    constructor(directoryPath: string) {
        this.directoryPath = directoryPath;
        this.ensureDirectoryExists();
    }

    /**
     * Saves an AgentState to a file.
     * This is an async operation that returns a promise.
     *
     * @param state The readonly AgentState to persist
     * @returns A promise that resolves when the state is saved, or rejects if there's an error
     */
    async save(state: Readonly<AgentState>): Promise<void> {
        const timestamp = new Date();
        return this.writeStateToFile(state, timestamp);
    }

    /**
     * DEPRECATED: should remove after 30 days
     * Gets the path to the state file.
     *
     * @returns The path to the state file
     */
    private getStateFilePathDeprecated(): string {
        return path.join(this.directoryPath, "agent_state.json");
    }

    private getStateFilePathV2(): string {
        return path.join(this.directoryPath, "agent_state_v2.json");
    }

    /**
     * Gets the path to an exchange file.
     *
     * @param sequenceId The sequence ID of the exchange
     * @returns The path to the exchange file
     */
    private _getExchangeFilePath(sequenceId: number): string {
        return path.join(this.directoryPath, `exchange_${sequenceId}.json`);
    }

    /**
     * Loads and deserializes the most recent state.
     *
     * @returns A promise that resolves with a Result containing either the deserialized AgentState or an error
     */
    async loadLatestAgentState(): Promise<AgentState> {
        try {
            if (this._hasChunkedFormat()) {
                return this._loadAgentStateChunkedJson();
            }

            // Fall back to the old format
            const filePath = this.getStateFilePathDeprecated();
            let serializedState: SerializedState;
            const fileContent = await readFileUtf8(filePath);
            try {
                serializedState = JSON.parse(fileContent) as SerializedState;
            } catch (error) {
                this.logger.error(`Failed to parse agent state: ${fileContent}`);
                throw error;
            }
            const agentState = this.deserializeAgentState(serializedState.agentState);
            return agentState;
        } catch (error) {
            this.logger.error(`Unexpected error loading agent state: ${String(error)}`);
            throw new Error(`Unexpected error loading agent state: ${String(error)}`);
        }
    }

    /**
     * Checks if a saved state exists.
     * This is useful for startup checks without loading the entire state.
     *
     * @returns A promise that resolves to true if a saved state exists, false otherwise
     */
    public checkSavedStateExists(): boolean {
        return (
            fileExists(this.getStateFilePathDeprecated()) || fileExists(this.getStateFilePathV2())
        );
    }

    /**
     * Deserializes a JSON string back into an AgentState object.
     *
     * @param serializedState The serialized state string
     * @returns The deserialized AgentState
     */
    private deserializeAgentState(serializedState: string): AgentState {
        try {
            // Use the static fromJson method for type-safe deserialization
            return AgentState.fromJson(serializedState);
        } catch (error) {
            this.logger.error(`Failed to deserialize agent state: ${String(error)}`);
            throw error;
        }
    }

    /**
     * Deserializes a JSON string back into an AgentState object with separate chat history.
     *
     * @param serializedState The serialized state string
     * @param chatHistory The chat history to use
     * @returns The deserialized AgentState
     */
    private deserializeAgentStateWithHistory(
        serializedState: string,
        chatHistory: ExchangeState[]
    ): AgentState {
        try {
            // Use the static fromJson method with separate chat history
            return AgentState.fromJson(serializedState, chatHistory);
        } catch (error) {
            this.logger.error(`Failed to deserialize agent state with history: ${String(error)}`);
            throw error;
        }
    }

    /**
     * Ensures the directory for state files exists.
     */
    private ensureDirectoryExists(): void {
        try {
            makeDirsSync(this.directoryPath);
        } catch (error) {
            this.logger.error(`Failed to create directory ${this.directoryPath}: ${String(error)}`);
            throw error;
        }
    }

    /**
     * Writes an AgentState to files using the JSON format that splits chat history.
     * Uses atomic file operations (write to temp files then rename all together) to ensure data integrity.
     * 1. agent_state_v2.json contains the complete state WITHOUT chat history
     * 2. exchange_[sequenceId].json contains each entry in AgentState._chatHistory
     *
     * @param state The readonly AgentState to write
     * @param timestamp The readonly timestamp to include in the state
     */
    private async writeStateToFile(
        state: Readonly<AgentState>,
        timestamp: Readonly<Date>
    ): Promise<void> {
        const tempFiles: string[] = [];
        const renameOperations: Array<{ from: string; to: string }> = [];

        try {
            this.ensureDirectoryExists();

            // Create a copy of the state without chat history
            const stateWithoutHistory = new AgentState();
            Object.assign(stateWithoutHistory, state);
            stateWithoutHistory.clearChatHistory();

            // Write the main state file to temp location
            const serializedState = this.serializeState(stateWithoutHistory, timestamp);
            const filePath = this.getStateFilePathV2();
            const tempMainFilePath = `${filePath}.tmp`;

            // Write each exchange to temp files
            // TODO: skip exchanges that are already written
            const chatHistory = state.chatHistory || [];
            for (const exchange of chatHistory) {
                const exchangeFilePath = this._getExchangeFilePath(exchange.sequenceId);
                const tempExchangeFilePath = `${exchangeFilePath}.tmp`;
                tempFiles.push(tempExchangeFilePath);
                renameOperations.push({ from: tempExchangeFilePath, to: exchangeFilePath });

                const exchangeContent = JSON.stringify(exchange, null, 2);
                await writeFileUtf8(tempExchangeFilePath, exchangeContent);
            }

            // Write the main state file last
            tempFiles.push(tempMainFilePath);
            renameOperations.push({ from: tempMainFilePath, to: filePath });

            await writeFileUtf8(tempMainFilePath, serializedState);

            // Rename all files together to minimize partial writes
            for (const operation of renameOperations) {
                await fs.promises.rename(operation.from, operation.to);
                // Ensure the file is synced to disk
                // TODO: It is unfortunate that the rename is not atomic and we
                // need this to avoid writing empty files that can crash the
                // agent. Maybe another reason to read this from the backend
                // instead of writing to local files?
                const fd = await fs.promises.open(operation.to, "r+");
                await fd.sync();
                await fd.close();
            }

            const totalExchanges = chatHistory.length;
            this.logger.info(
                `Wrote state file and ${totalExchanges} exchange files to ${this.directoryPath}`
            );
        } catch (error) {
            // Clean up any remaining temporary files
            for (const tempFile of tempFiles) {
                try {
                    await fs.promises.unlink(tempFile);
                    this.logger.debug(`Cleaned up temporary file: ${tempFile}`);
                } catch (cleanupError) {
                    // Log but don't throw - cleanup is best effort
                    this.logger.warn(
                        `Failed to clean up temporary file ${tempFile}: ${String(cleanupError)}`
                    );
                }
            }

            this.logger.error(`Failed to write state to file: ${String(error)}`);
            throw error;
        }
    }

    /**
     * Checks if the state directory has the JSON format (separate exchange files).
     * This is determined by checking if the main state file was created by the new format.
     *
     * @returns A promise that resolves to true if JSON format is detected
     */
    private _hasChunkedFormat(): boolean {
        if (fileExists(this.getStateFilePathV2())) {
            return true;
        }
        return false;
    }

    /**
     * Loads and deserializes the agent state using the JSON format with split chat history.
     *
     * @returns A promise that resolves with the deserialized AgentState
     */
    private async _loadAgentStateChunkedJson(): Promise<AgentState> {
        try {
            const filePath = this.getStateFilePathV2();
            const fileContent = await readFileUtf8(filePath);
            let serializedState: SerializedState;
            try {
                serializedState = JSON.parse(fileContent) as SerializedState;
            } catch (error) {
                this.logger.error(`Failed to parse agent state: ${fileContent}`);
                throw error;
            }

            // Load all exchange files
            const chatHistory = await this.loadAllExchanges();

            // Load the main state with the separate chat history
            const agentState = this.deserializeAgentStateWithHistory(
                serializedState.agentState,
                chatHistory
            );

            return agentState;
        } catch (error) {
            this.logger.error(`Failed to load agent state from JSON format: ${String(error)}`);
            throw error;
        }
    }

    /**
     * Loads all exchange files and returns them as an array.
     *
     * @returns A promise that resolves with an array of ExchangeState objects
     */
    private async loadAllExchanges(): Promise<ExchangeState[]> {
        const exchanges: ExchangeState[] = [];
        try {
            const files = await fs.promises.readdir(this.directoryPath);
            const exchangeFiles = files
                .filter((file) => file.startsWith("exchange_") && file.endsWith(".json"))
                .sort((a, b) => {
                    const aNum = parseInt(a.match(/exchange_(\d+)\.json/)?.[1] || "0");
                    const bNum = parseInt(b.match(/exchange_(\d+)\.json/)?.[1] || "0");
                    return aNum - bNum;
                });

            for (const file of exchangeFiles) {
                const filePath = path.join(this.directoryPath, file);
                const content = await readFileUtf8(filePath);
                let exchange: ExchangeState;
                try {
                    exchange = JSON.parse(content) as ExchangeState;
                } catch (error) {
                    this.logger.error(`Failed to parse exchange file ${filePath}: ${content}`);
                    throw error;
                }
                exchanges.push(exchange);
            }

            return exchanges;
        } catch (error) {
            this.logger.error(`Failed to load exchanges: ${String(error)}`);
            throw error;
        }
    }

    /**
     * Serializes an AgentState to a JSON string.
     *
     * @param state The readonly AgentState to serialize
     * @param timestamp The readonly timestamp to include in the serialized state
     * @returns A JSON string representation of the state
     */
    private serializeState(state: Readonly<AgentState>, timestamp: Readonly<Date>): string {
        try {
            const serializedAgentState = JSON.stringify(state);

            const stateForStorage: SerializedState = {
                agentState: serializedAgentState,
                timestamp: timestamp.toISOString(),
            };

            return JSON.stringify(stateForStorage, null, 2);
        } catch (error) {
            this.logger.error(`Failed to serialize state: ${String(error)}`);
            throw error;
        }
    }
}
