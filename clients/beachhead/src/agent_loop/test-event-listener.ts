import { ToolUseResponse } from "@augment-internal/sidecar-libs/src/tools/tool-types";

import { AgentLoopEventListener } from "./agent_loop";

/**
 * Test event listener that logs all events for testing purposes.
 * This can be used to verify that events are being fired correctly.
 */
export class TestEventListener implements AgentLoopEventListener {
    public events: Array<{ type: string; data?: unknown; timestamp: number }> = [];

    private logEvent(type: string, data?: unknown): void {
        this.events.push({
            type,
            data,
            timestamp: Date.now(),
        });
        // eslint-disable-next-line no-console
        console.log(`[TestEventListener] ${type}`, data ? JSON.stringify(data, null, 2) : "");
    }

    onThinkingStart(): void {
        this.logEvent("thinking_start");
    }

    onThinkingStop(): void {
        this.logEvent("thinking_stop");
    }

    onAssistantResponseStart(): void {
        this.logEvent("assistant_response_start");
    }

    onAssistantResponseChunk(text: string): void {
        this.logEvent("assistant_response_chunk", { text });
    }

    onAssistantResponseEnd(): void {
        this.logEvent("assistant_response_end");
    }

    onToolCallStart(toolName: string, toolInput: Record<string, any>): void {
        this.logEvent("tool_call_start", { toolName, toolInput });
    }

    onToolCallResult(toolName: string, toolResult: ToolUseResponse): void {
        this.logEvent("tool_call_result", {
            toolName,
            isError: toolResult.isError,
            textLength: toolResult.text.length,
        });
    }

    onError(error: unknown, context?: string): void {
        this.logEvent("error", { error: String(error), context });
    }

    onMaxIterationsExceeded(maxIterations: number): void {
        this.logEvent("maxIterationsExceeded", { maxIterations });
    }

    /**
     * Get a summary of events that occurred
     */
    getEventSummary(): string[] {
        return this.events.map((event) => event.type);
    }

    /**
     * Clear all recorded events
     */
    clearEvents(): void {
        this.events = [];
    }

    /**
     * Get events of a specific type
     */
    getEventsOfType(type: string): Array<{ type: string; data?: unknown; timestamp: number }> {
        return this.events.filter((event) => event.type === type);
    }
}
