import {
    ChatRequestNode,
    ChatRequestNodeType,
} from "@augment-internal/sidecar-libs/src/chat/chat-types";

import { getLogger } from "./logging";
import { McpServerConfig } from "./remote-agent-manager/types";
import { readFileUtf8 } from "./utils/fs-utils";

// Enum for different request sources/agent types (matches protobuf)
export enum RequestSource {
    REQUEST_SOURCE_UNSPECIFIED = 0,
    REQUEST_SOURCE_IDE = 1,
    REQUEST_SOURCE_AUGMENT_ACTIONS = 2,
    REQUEST_SOURCE_SLACK_BOT = 3,
    REQUEST_SOURCE_GITHUB_BOT = 4,
    REQUEST_SOURCE_LINEAR_BOT = 5,
    REQUEST_SOURCE_CLI = 6,
}

export class AgentConfig {
    private _raw: jAgentConfig;

    constructor(config: jAgentConfig) {
        this._raw = config;
    }

    public raw(): jAgentConfig {
        return this._raw;
    }

    public git_repo(): string {
        return this.raw().workspace_setup?.github_ref?.url ?? "";
    }

    public git_ref(): string {
        return this.raw().workspace_setup?.github_ref?.ref ?? "";
    }

    public initial_patch(): string {
        return this.raw().workspace_setup?.patch ?? "";
    }

    public is_setup_script_agent(): boolean {
        return this.raw().is_setup_script_agent ?? false;
    }

    public setup_script(): string {
        let patch = this.raw().workspace_setup?.setup_script ?? "";
        if (patch && !patch.startsWith("#!")) {
            patch = `#!/bin/bash\n${patch}`;
        }
        return patch;
    }

    public starting_nodes(): ChatRequestNode[] {
        const startingNodes = this.raw().starting_nodes;
        if (!startingNodes) {
            return [];
        }
        return startingNodes.map((node) => ({
            ...node,
            id: node.id ?? 0,
            type: node.type ?? ChatRequestNodeType.TEXT,
        }));
    }

    public user_guidelines(): string {
        return this.raw().user_guidelines ?? "";
    }

    public workspace_guidelines(): string {
        return this.raw().workspace_guidelines ?? "";
    }

    public agent_memories(): string {
        return this.raw().agent_memories ?? "";
    }

    public workspace_api_url(): string {
        return this.raw().workspace_setup?.api_url ?? "";
    }

    public model_id(): string {
        return this.raw().model ?? "";
    }

    public mcp_servers(): McpServerConfig[] {
        return this.raw().mcp_servers || [];
    }

    public request_source(): RequestSource {
        return this.raw().request_source ?? RequestSource.REQUEST_SOURCE_UNSPECIFIED;
    }

    public static async fromFile(configPath: string): Promise<AgentConfig> {
        const logger = getLogger("Beachhead");
        try {
            const buf = await readFileUtf8(configPath);
            const json = JSON.parse(buf) as jAgentConfig;
            return new AgentConfig(json);
        } catch (error) {
            logger.error("Failed to read config file: %s", error);
            throw error;
        }
    }
}

export interface jAgentConfig {
    workspace_setup: {
        github_ref: {
            url: string;
            ref: string;
        };
        patch: string;
        setup_script: string;
        api_url: string;
    };
    starting_nodes?: ChatRequestNode[];
    user_guidelines: string;
    workspace_guidelines: string;
    agent_memories: string;
    model: string;
    is_setup_script_agent?: boolean;
    mcp_servers?: McpServerConfig[];
    request_source?: RequestSource;
}
