/* eslint-disable @typescript-eslint/no-unsafe-assignment */

/* eslint-disable @typescript-eslint/no-unsafe-argument */
import { BackoffParams } from "@augment-internal/sidecar-libs/src/utils/promise-utils";

import { type AugmentLogger, getLogger } from "./logging";
import { CountReporter, ProgressReporter } from "./reporting";
import { Disposable, EventEmitter } from "./vscode";

/**
 * ErrorHandler is an interface for throwing an exception to report a failure
 * during processing of a WorkQueue item.
 */
export interface ErrorHandler {
    throwError(error: any, retry: boolean): void;
}

/**
 * WorkQueueItemFailed is an exception used for reporting a failure during
 * processing of a workQueue item or item batch. The error indicates whether
 * the failed item should be retried, and also gives an optional object (presumably
 * an Error, but can be anything) that should be delivered to waiters.
 */
class WorkQueueItemFailed extends Error {
    constructor(
        public toThrow: any,
        public retry: boolean
    ) {
        const message = toThrow instanceof Error ? toThrow.message : `${toThrow}`;
        super(`WorkQueueItemFailed: ${message}, retry = ${retry}`);
    }
}

/**
 * WorkQueueErrorHandler is a class that implements ErrorHandler. An instance
 * of this class is passed to an adapter's processOne/processMany method as a
 * means for the method to report failures.
 */
class WorkQueueErrorHandler {
    throwError(error: any, retry: boolean): void {
        throw new WorkQueueItemFailed(error, retry);
    }
}

/**
 * WorkQueueProgressReporter implements ProgressReporter, keeping in mind that more
 * items can be added to the queue while we are waiting for it to become empty
 * (hence the resetting of totalItems in update()).
 */
class WorkQueueProgressReporter {
    private progressReporter: ProgressReporter;
    private completedItems = 0;
    private totalItems: number;

    constructor(progressReporter: ProgressReporter, totalItems: number) {
        this.progressReporter = progressReporter;
        this.totalItems = totalItems;
        this.update(0, this.totalItems);
    }

    update(doneIncr: number, remaining: number) {
        this.completedItems += doneIncr;
        this.totalItems = this.completedItems + remaining;
        this.progressReporter.update(this.completedItems, this.totalItems);
    }

    cancel() {
        this.progressReporter.cancel();
    }
}

/**
 * This pile of Singleton* and Batch* types exists to make it possible to configure
 * a WorkQueue to operate either on single items or batches of items. They attempt to
 * work around limitations in TypeScript generics.
 */

type SingletonProcessorFn<ItemT> = (item: ItemT, errorHandler: ErrorHandler) => Promise<void>;

type BatchProcessorFn<ItemT> = (items: Set<ItemT>, errorHandler: ErrorHandler) => Promise<void>;

export interface SingletonProcessor<ItemT> {
    processOne: SingletonProcessorFn<ItemT>;
}

export interface BatchProcessor<ItemT> {
    maxBatchSize: number | undefined;
    processBatch: BatchProcessorFn<ItemT>;
}

interface ProcessItem {
    itemCount: () => number;
    start: (errorHandler: WorkQueueErrorHandler) => Promise<void>;
}

class SingletonProcessItem<ItemT> {
    constructor(
        private item: ItemT,
        private process: SingletonProcessorFn<ItemT>
    ) {}

    public itemCount() {
        return 1;
    }

    public start(errorHandler: WorkQueueErrorHandler): Promise<void> {
        return this.process(this.item, errorHandler);
    }
}

class BatchProcessItem<ItemT> {
    constructor(
        private items: Set<ItemT>,
        private process: BatchProcessorFn<ItemT>
    ) {}

    public itemCount() {
        return this.items.size;
    }

    public start(errorHandler: WorkQueueErrorHandler): Promise<void> {
        return this.process(this.items, errorHandler);
    }
}

class QueueAdapterBase<ItemT> {
    protected queue = new Set<ItemT>();

    get size(): number {
        return this.queue.size;
    }

    public add(item: ItemT): void {
        this.queue.add(item);
    }

    public delete(item: ItemT): void {
        this.queue.delete(item);
    }
}

class SingletonWorkQueueAdapter<ItemT> extends QueueAdapterBase<ItemT> {
    constructor(private process: SingletonProcessorFn<ItemT>) {
        super();
    }

    public dequeue(): ProcessItem {
        const iter = this.queue.values().next();
        if (iter.done) {
            throw new Error("Cannot dequeue from empty queue");
        }
        const item = iter.value;
        this.queue.delete(item);
        return new SingletonProcessItem<ItemT>(item, this.process);
    }
}

class BatchWorkQueueAdapter<ItemT> extends QueueAdapterBase<ItemT> {
    constructor(
        public process: BatchProcessorFn<ItemT>,
        private _maxBatchSize: number | undefined
    ) {
        super();
    }

    public dequeue(): ProcessItem {
        let items: Set<ItemT>;
        if (this._maxBatchSize === undefined || this.queue.size <= this._maxBatchSize) {
            items = this.queue;
            this.queue = new Set<ItemT>();
        } else {
            items = new Set<ItemT>();
            const iter = this.queue.values();
            for (let i = 0; i < this._maxBatchSize; i++) {
                const next = iter.next();
                if (next.done) {
                    break;
                }
                const item = next.value;
                this.queue.delete(item);
                items.add(item);
            }
        }
        return new BatchProcessItem<ItemT>(items, this.process);
    }
}

/**
 * A WorkQueue is a queue of items to be processed in the background. The template
 * parameter ItemT indicates the type of the items in the queue. There are two
 * constructor parameters:
 * - name: A name for the queue. It is only used for diagnostics reporting.
 * - processor: An object that indicates how to process the items in the queue.
 *
 * If items are to be dequeued and processed one at a time, pass an instance of
 * SingletonProcessor<ItemT>. If items are to be dequeued and processed in batches,
 * pass an instance of BatchAdapter<ItemT>.
 */

type WorkQueueAdapter<ItemT> = SingletonWorkQueueAdapter<ItemT> | BatchWorkQueueAdapter<ItemT>;

export class WorkQueue<ItemT> {
    // }, AdapterT extends WorkQueueAdapter<ItemT>> {
    static queueStatusChanged = "QueueStatusChanged";
    static itemFailed = "ItemFailed";

    static defaultBackoffParams = {
        initialMS: 100,
        mult: 2,
        maxMS: 30000,
    };

    private eventEmitters: { [key: string]: EventEmitter<any> } = {
        [WorkQueue.queueStatusChanged]: new EventEmitter<null>(),
        [WorkQueue.itemFailed]: new EventEmitter<Error>(),
    };
    private queue: WorkQueueAdapter<ItemT>;
    private countReporters = new Set<CountReporter>();
    private progressReporters = new Set<WorkQueueProgressReporter>();
    private errorHandler: WorkQueueErrorHandler;
    private backoffParams: BackoffParams;

    private itemsInProgress = 0;
    private stopping = false;

    private readonly logger: AugmentLogger;

    constructor(
        public name: string,
        processor: SingletonProcessor<ItemT> | BatchProcessor<ItemT>,
        private shutdownError?: Error,
        backoffParams?: BackoffParams
    ) {
        if ("processOne" in processor) {
            this.queue = new SingletonWorkQueueAdapter(processor.processOne);
        } else if ("processBatch" in processor) {
            this.queue = new BatchWorkQueueAdapter(processor.processBatch, processor.maxBatchSize);
        } else {
            // (this shouldn't be possible, but the language insists that we have
            // this else-clause)
            throw new Error("Invalid processor type");
        }

        this.backoffParams = backoffParams || WorkQueue.defaultBackoffParams;
        this.logger = getLogger(`WorkQueue[${name}]`);
        this.errorHandler = new WorkQueueErrorHandler();
    }

    // stop() prevents items from being added or removed from the queue and
    // terminates background processing after any in-flight work completes.
    public stop(): void {
        this.stopping = true;
        this.update();
        this.notifyStatusChanged();
    }

    // add() enqueues a new item.
    public add(item: ItemT): void {
        if (this.stopping) {
            return;
        }

        this.queue.add(item);
        this.update();
        void this.kick();
    }

    // delete() removes an item from the queue if it is present. If the item is
    // currently being processed, this is a no-op (the in-progress work is not
    // terminated).
    public delete(item: ItemT): void {
        if (this.stopping) {
            return;
        }

        this.queue.delete(item);
        this.update();
    }

    // size() returns the number of items in the queue, including any in-progress
    // items.
    public size(): number {
        return this.queue.size + this.itemsInProgress;
    }

    // reportQueueSize installs a CountReporter to report the current size of the
    // queue. There is no way to uninstall the reporter.
    public reportQueueSize(countReporter: CountReporter) {
        const size = this.size();
        countReporter.update(size);
        this.countReporters.add(countReporter);
    }

    // awaitEmpty returns a Promise that resolves when the queue becomes empty.
    // If the shutdown() method is called before the queue becomes empty, the
    // Promise will be rejected. If rejectOnItemFailure is true, the Promise
    // will also be rejected if an item fails.
    public awaitEmpty(
        progressReporter?: ProgressReporter,
        rejectOnItemFailure: boolean = true
    ): Promise<void> {
        if (this.stopping) {
            return Promise.resolve();
        }

        const size = this.size();
        if (size === 0) {
            return Promise.resolve();
        }

        if (progressReporter) {
            this.progressReporters.add(new WorkQueueProgressReporter(progressReporter, size));
        }

        return new Promise((resolve, reject) => {
            const subscriptions: Disposable[] = [];
            function unsubscribe() {
                for (const disposable of subscriptions) {
                    disposable.dispose();
                }
            }
            subscriptions.push(
                this.eventEmitters[WorkQueue.queueStatusChanged].event(() => {
                    unsubscribe();
                    if (this.stopping) {
                        reject(this.shutdownError);
                    } else {
                        resolve();
                    }
                })
            );
            if (rejectOnItemFailure) {
                subscriptions.push(
                    this.eventEmitters[WorkQueue.itemFailed].event((e: any) => {
                        unsubscribe();
                        reject(e);
                    })
                );
            }
        });
    }

    // update reports that completedIncr items have finished processing and reports
    // progress to the countReporters and progressReporters. (completedIncr is an
    // increment, not a running total).
    private update(completedIncr = 0) {
        if (this.stopping) {
            for (const reporter of this.countReporters) {
                reporter.cancel();
            }
            for (const reporter of this.progressReporters) {
                reporter.cancel();
            }
        } else {
            this.itemsInProgress -= completedIncr;
            const size = this.size();
            for (const reporter of this.countReporters) {
                reporter.update(size);
            }
            for (const reporter of this.progressReporters) {
                reporter.update(completedIncr, size);
            }
        }
    }

    /**
     * notifyStatusChanged notifies all status waiters that the queue is either
     * empty or shutting down. It removes all event listeners, and clears the set of
     * progress reporters, as there will be no further events or progress reports to
     * send to them.
     */
    private notifyStatusChanged() {
        this.eventEmitters[WorkQueue.queueStatusChanged].fire(null);
        this.progressReporters.clear();
    }

    /**
     * notifyItemFailed notifies all status waiters that an item has failed. It does
     * not touch the set of ProgressReporters as it is up to their creators to decide
     * whether they want to continue to report progress while the failed item retries.
     */
    private notifyItemFailed(error: Error) {
        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
        const toThrow = error instanceof WorkQueueItemFailed ? error.toThrow : error;
        this.eventEmitters[WorkQueue.itemFailed].fire(toThrow);
    }

    /**
     * delay() returns a Promise that will resolve in the given number of milliseconds.
     * The returned Promise will never reject.
     */
    private delay(delayMS: number): Promise<void> {
        return new Promise((resolve) => setTimeout(resolve, delayMS));
    }

    /**
     * kick() starts the processing of items in the queue if processing isn't already
     * in progress. It stops when the queue becomes empty or when this WorkQueue has
     * been told to shut down. It will retry failed items if requested by the adaptor's
     * processOne/processMany method.
     */
    private async kick(): Promise<void> {
        if (this.itemsInProgress) {
            return;
        }

        while (!this.stopping && this.queue.size !== 0) {
            const toProcess = this.queue.dequeue();
            this.itemsInProgress = toProcess.itemCount();
            let backoffMs = 0;
            let retryCount = 0;
            do {
                try {
                    await toProcess.start(this.errorHandler);
                    if (retryCount) {
                        this.logger.debug(`item succeeded; retries = ${retryCount}`);
                    }
                    break;
                } catch (e: any) {
                    this.notifyItemFailed(e as Error);
                    const retry = e instanceof WorkQueueItemFailed && e.retry;
                    if (!retry) {
                        this.logger.debug(`item failed, not retrying; retries = ${retryCount}`);
                        break;
                    }
                }

                this.logger.debug(
                    `item failed, retrying in ${backoffMs} ms; retries =` + ` ${retryCount}`
                );
                await this.delay(backoffMs);
                this.logger.debug("retrying");

                // (compute the back-off we will use next time around)
                if (backoffMs === 0) {
                    backoffMs = this.backoffParams.initialMS;
                } else {
                    backoffMs = Math.min(
                        backoffMs * this.backoffParams.mult,
                        this.backoffParams.maxMS
                    );
                }
                retryCount++;
            } while (!this.stopping);

            if (!this.stopping) {
                this.update(this.itemsInProgress);
            }
        }

        this.notifyStatusChanged();
    }
}
