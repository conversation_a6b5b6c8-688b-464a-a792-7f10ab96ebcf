/**
 * Utilities for handling slash commands in CLI interactive mode
 */

// Valid slash commands for CLI interactive mode
export const VALID_SLASH_COMMANDS = new Set(["/exit", "/help", "/request-id"]);

export function isValidSlashCommand(input: string): boolean {
    return VALID_SLASH_COMMANDS.has(input.toLowerCase());
}

export function processSlashCommand(
    input: string
): "exit" | "help" | "request-id" | "clear" | "invalid" | "regular" | "empty" {
    const trimmed = input.trim();

    if (trimmed === "") {
        return "empty";
    }

    if (trimmed.startsWith("/")) {
        if (trimmed.toLowerCase() === "/exit") {
            return "exit";
        } else if (trimmed.toLowerCase() === "/help") {
            return "help";
        } else if (trimmed.toLowerCase() === "/request-id") {
            return "request-id";
        } else if (trimmed.toLowerCase() === "/clear") {
            return "clear";
        } else {
            return "invalid";
        }
    } else {
        return "regular";
    }
}
