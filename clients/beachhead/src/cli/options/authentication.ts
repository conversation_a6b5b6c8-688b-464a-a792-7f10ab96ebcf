import { Argv } from "yargs";

/**
 * Authentication-related command line arguments
 */
export interface AuthenticationArgs {
    login: boolean;
    "login-url"?: string;
    logout: boolean;
    "revoke-all-augment-tokens": boolean;
    "print-augment-token": boolean;
    "augment-token-file"?: string;
    "api-url"?: string;
    "github-api-token"?: string;
}

/**
 * Adds authentication-related options to yargs
 */
export function addAuthenticationOptions(yargs: Argv): Argv {
    return yargs
        .option("login", {
            type: "boolean",
            description: "Authenticate with Augment using OAuth.",
            demandOption: false,
            default: false,
        })
        .option("logout", {
            type: "boolean",
            description: "Log out of Augment and remove stored OAuth session.",
            demandOption: false,
            default: false,
        })
        .option("revoke-all-augment-tokens", {
            type: "boolean",
            description: "Revoke all authentication tokens for the current user.",
            demandOption: false,
            default: false,
        })
        .option("print-augment-token", {
            type: "boolean",
            description: "Print the current authentication token. This token can be provided as AUGMENT_API_TOKEN environment variable when using this tool for automation.",
            demandOption: false,
            default: false,
        })
        .option("github-api-token", {
            type: "string",
            description:
                "Path to file containing GitHub API token. Enables GitHub integration for creating PRs, managing issues, reading repository information, and interacting with GitHub APIs. The token must have 'repo' and 'user:email' scopes. Generate at: GitHub → Settings → Developer settings → Personal access tokens. Takes precedence over GITHUB_API_TOKEN environment variable. If neither is provided, will try to use the GitHub integration configured through the IDE extension.",
            demandOption: false,
        })
        .option("augment-token-file", {
            type: "string",
            description: "Path to file containing authentication token.",
            demandOption: false,
        })
        .option("api-url", {
            type: "string",
            description: "API URL to use for Augment if not using OAuth.",
            demandOption: false,
            hidden: true,
        })
        .option("login-url", {
            type: "string",
            description:
                "OAuth URL to use for authentication (for debugging/dev deployments only).",
            demandOption: false,
            hidden: true,
        })
        .group([
            "login",
            "logout",
            "revoke-all-augment-tokens",
            "print-augment-token",
            "augment-token-file",
            "github-api-token"
        ], "Authentication:");
}
