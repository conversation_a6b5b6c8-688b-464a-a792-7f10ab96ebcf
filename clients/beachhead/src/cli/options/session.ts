import { Argv } from "yargs";

/**
 * Session management command line arguments
 */
export interface SessionArgs {
    continue: boolean;
    "dont-save-session": boolean;
    "delete-saved-sessions": boolean;
}

/**
 * Adds session management options to yargs
 */
export function addSessionOptions(yargs: Argv): Argv {
    return yargs
        .option("continue", {
            type: "boolean",
            alias: "c",
            description:
                "Restores the previous conversation history and continues from where you left off.",
            demandOption: false,
            default: false,
        })
        .option("dont-save-session", {
            type: "boolean",
            description:
                "When specified, the agent will not save conversation history to disk.",
            demandOption: false,
            default: false,
        })
        .option("delete-saved-sessions", {
            type: "boolean",
            description:
                "Delete all saved sessions from disk and exit. This will permanently remove all stored conversation history.",
            demandOption: false,
            default: false,
        })
        .group([
            "continue",
            "dont-save-session",
            "delete-saved-sessions"
        ], "Session Management:");
}
