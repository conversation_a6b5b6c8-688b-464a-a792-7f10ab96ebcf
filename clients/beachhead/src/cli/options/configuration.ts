import * as os from "os";
import * as path from "path";
import { Argv } from "yargs";

/**
 * Configuration and workspace setup command line arguments
 */
export interface ConfigurationArgs {
    "workspace-root"?: string;
    "augment-cache-dir": string;
    rules?: string[];
}

/**
 * Adds configuration and workspace setup options to yargs
 */
export function addConfigurationOptions(yargs: Argv): Argv {
    return yargs
        .option("workspace-root", {
            type: "string",
            alias: "w",
            description:
                "Root folder of the workspace to index (auto-detects git repository root if not specified)",
            demandOption: false,
        })
        .option("augment-cache-dir", {
            type: "string",
            description:
                "Directory to store Augment cache files (session data, chat history, etc.). Defaults to ~/.augment",
            demandOption: false,
            default: path.join(os.homedir(), ".augment"),
        })
        .option("rules", {
            type: "array",
            nargs: 1,
            description:
                "Path to file containing additional rules to append to workspace guidelines. Can be specified multiple times.",
            demandOption: false,
        })
        .group([
            "workspace-root",
            "augment-cache-dir",
            "rules"
        ], "Configuration:");
}
