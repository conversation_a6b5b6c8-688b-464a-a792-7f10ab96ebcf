import { Argv } from "yargs";

/**
 * Tool and integration configuration command line arguments
 */
export interface ToolsArgs {
    "mcp-config"?: string[];
    "enable-sub-agent-tool": boolean;
    "sub-agent-mode": boolean;
}

/**
 * Adds tool and integration configuration options to yargs
 */
export function addToolsOptions(yargs: Argv): Argv {
    return yargs
        .option("mcp-config", {
            type: "array",
            nargs: 1,
            description:
                "Path to JSON file containing MCP server configurations, or inline JSON configuration. Can be specified multiple times.",
            demandOption: false,
        })
        .option("enable-sub-agent-tool", {
            type: "boolean",
            description:
                "Enable the sub-agent tool that allows spawning isolated sub-agents to work on tasks independently. Sub-agents run in their own filesystem isolation using fuse-overlayfs. (Experimental)",
            demandOption: false,
            default: false,
            hidden: true,
        })
        .option("sub-agent-mode", {
            type: "boolean",
            description:
                "Run this agent as a sub-agent (not to enable creation of sub-agents). This enables status file background tasks for coordination with parent agent.",
            demandOption: false,
            default: false,
            hidden: true,
        })
        .group([
            "mcp-config",
            "enable-sub-agent-tool",
            "sub-agent-mode"
        ], "Tools & Integrations:");
}
