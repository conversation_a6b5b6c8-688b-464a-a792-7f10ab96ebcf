import path from "path";
import { Argv } from "yargs";
import * as os from "os";

export enum OutputMode {
    TEXT = 'text',
    TUI = 'tui'
}

export enum OutputVerbosity {
    QUIET = 'quiet',
    COMPACT = 'compact',
    DEFAULT = 'default'
}

export enum LogLevel {
    DEBUG = 'debug',
    INFO = 'info',
    WARN = 'warn',
    ERROR = 'error'
}

export interface OutputArgs {
    "print": boolean;
    compact: boolean;
    quiet: boolean;
    "no-bell": boolean;
    "log-file"?: string;
    "log-level": string;
}

export interface NotificationSettings {
    bell: boolean;
}

export interface LogSettings {
    file: string;
    level: LogLevel;
}

export interface OutputConfig {
    mode: OutputMode;
    verbosity: OutputVerbosity;
    notification_settings: NotificationSettings;
    log_settings: LogSettings;
}

export function addOutputOptions(yargs: Argv): Argv {
    return yargs
        .options("print", {
            type: "boolean",
            description: "Prints results as simple text for a single instruction. Useful for automation and CI/CD pipelines.",
            demandOption: false,
            default: false,
        })
        .option("quiet", {
            type: "boolean",
            description:
                "Enable quiet output mode. Only the last assistant message in a turn gets shown in full. Useful for piping or automating output.",
            demandOption: false,
            default: false,
        })
        .option("compact", {
            type: "boolean",
            description:
                "Enable compact output mode. Tool calls, tool results, and intermediate assistant messages are shown in a single line each. The last assistant message in a turn gets shown in full.",
            demandOption: false,
            default: false,
        })
        .option("no-bell", {
            type: "boolean",
            description:
                "Disable the visual bell that sounds when the agent completes a task in interactive mode.",
            demandOption: false,
            default: false,
        })
        .option("log-file", {
            type: "string",
            description:
                "Path to log file. Use '-' to log to stderr. If not specified, logs to console.",
            demandOption: false,
            hidden: true,
        })
        .option("log-level", {
            type: "string",
            description: "Log level (error, warn, info, debug).",
            demandOption: false,
            default: "info",
            choices: ["error", "warn", "info", "debug"],
            hidden: true,
        })
        .group([
            "print",
            "compact",
            "no-bell",
            "log-file",
            "log-level",
        ], "Output & Interaction:");
}

export function buildOutputConfig(args: OutputArgs): OutputConfig {
    const mode = args.print ? OutputMode.TEXT : OutputMode.TUI;

    // Determine verbosity
    let verbosity: OutputVerbosity;
    if (args.quiet) {
        verbosity = OutputVerbosity.QUIET;
    } else if (args.compact) {
        verbosity = OutputVerbosity.COMPACT;
    } else {
        verbosity = OutputVerbosity.DEFAULT;
    }

    // Determine log settings
    let logFile = args["log-file"];
    if (!logFile) {
        // Create a default log file in /tmp for augment binary
        logFile = path.join(os.tmpdir(), "augment-log.txt");
    }
    const logLevel = (args["log-level"] || "").toLowerCase();
    let level: LogLevel;
    switch (logLevel) {
        case 'debug':
            level = LogLevel.DEBUG;
            break;
        case 'info':
            level = LogLevel.INFO;
            break;
        case 'warn':
            level = LogLevel.WARN;
            break;
        case 'error':
            level = LogLevel.ERROR;
            break;
        default:
            // In quiet mode, suppress logs unless user explicitly set log-file to stderr
            if (verbosity === OutputVerbosity.QUIET && logFile !== "-") {
                level = LogLevel.ERROR;
                break;
            } else {
                level = LogLevel.INFO; // Default fallback
                break;
            }
    }

    return {
        mode,
        verbosity,
        notification_settings: {
            bell: !args["no-bell"]
        },
        log_settings: {
            file: logFile,
            level
        }
    };
}
