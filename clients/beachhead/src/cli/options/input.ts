import { Argv } from "yargs";

/**
 * Input source command line arguments
 */
export interface InputArgs {
    instruction?: string;
    "instruction-file"?: string;
}

/**
 * Adds input source options to yargs
 */
export function addInputOptions(yargs: Argv): Argv {
    return yargs 
        .command("$0 [instruction]", "Run Augment CLI Agent", (yargs) => {
            return yargs.positional("instruction", {
                type: "string",
                description:
                    "Initial instruction to run. Mutually exclusive with --instruction-file.",
                demandOption: false,
            });
        })
        .option("instruction", {  
            type: "string",  
            description: "Initial instruction to run. Mutually exclusive with --instruction-file.",  
            demandOption: false,  
        })
        .option("instruction-file", {
            type: "string",
            description:
                "Path to a file containing the initial instruction. Ideal for complex workflows and reusable task definitions. Mutually exclusive with instruction.",
            demandOption: false,
        })
        .group([
            "instruction",
            "instruction-file",
        ], "Input:");
}
