import * as fs from "fs";
import * as os from "os";
import * as path from "path";

import { findVCS } from "../../vcs/vcs-finder";

/**
 * Validates that a workspace root directory exists and is accessible.
 * Shows a nice error message and exits if the directory doesn't exist.
 */
function validateWorkspaceRoot(workspaceRoot: string): void {
    try {
        const stats = fs.statSync(workspaceRoot);
        if (!stats.isDirectory()) {
            /* eslint-disable-next-line no-console */
            console.error(`
❌ Workspace root error: The specified path is not a directory

   Path: ${workspaceRoot}

   Please specify a valid directory path with --workspace-root or set the
   AUGMENT_WORKSPACE_ROOT environment variable.
`);
            process.exit(1);
        }

        // Test if we can access the directory by trying to read it
        try {
            fs.accessSync(workspaceRoot, fs.constants.R_OK | fs.constants.X_OK);
        } catch (accessError) {
            /* eslint-disable-next-line no-console */
            console.error(`
❌ Workspace root error: Permission denied

   Path: ${workspaceRoot}

   You don't have permission to access this directory.
   Please check the directory permissions or choose a different path.

   Required permissions: read and execute access
`);
            process.exit(1);
        }
    } catch (error) {
        if ((error as NodeJS.ErrnoException).code === "ENOENT") {
            /* eslint-disable-next-line no-console */
            console.error(`
❌ Workspace root error: Directory does not exist

   Path: ${workspaceRoot}

   Please check that the directory exists and try again.

   To create a new workspace directory:
     mkdir -p "${workspaceRoot}"

   Or run without --workspace-root to auto-detect from git repository.
`);
        } else if ((error as NodeJS.ErrnoException).code === "EACCES") {
            /* eslint-disable-next-line no-console */
            console.error(`
❌ Workspace root error: Permission denied

   Path: ${workspaceRoot}

   You don't have permission to access this directory.
   Please check the directory permissions or choose a different path.
`);
        } else {
            /* eslint-disable-next-line no-console */
            console.error(`
❌ Workspace root error: Cannot access directory

   Path: ${workspaceRoot}
   Error: ${(error as Error).message}

   Please check that the directory exists and is accessible.
`);
        }
        process.exit(1);
    }
}

/**
 * Automatically detects the workspace root directory.
 * First checks for explicit --workspace-root or AUGMENT_WORKSPACE_ROOT.
 * If not provided, tries to find the git repository root from the current directory.
 * If no git root is found, creates a temporary workspace directory.
 */
export async function detectWorkspaceRoot(
    explicitRoot?: string
): Promise<{ path: string; isTemporary: boolean }> {
    // If explicitly provided, validate it exists
    if (explicitRoot) {
        explicitRoot = path.resolve(explicitRoot); // Ensure absolute path
        validateWorkspaceRoot(explicitRoot);
        return { path: explicitRoot, isTemporary: false };
    }

    // Check environment variable
    let envRoot = process.env.AUGMENT_WORKSPACE_ROOT;
    if (envRoot) {
        envRoot = path.resolve(envRoot); // Ensure absolute path
        validateWorkspaceRoot(envRoot);
        return { path: envRoot, isTemporary: false };
    }

    // Try to find git repository root from current directory
    try {
        const vcsDetails = await findVCS(process.cwd());
        if (vcsDetails) {
            return { path: vcsDetails.root.fsPath, isTemporary: false };
        }
    } catch (error) {
        // VCS detection failed, continue to fallback
    }

    // Create temporary workspace directory
    const tempDir = await fs.promises.mkdtemp(path.join(os.tmpdir(), "augment-workspace-"));
    return { path: tempDir, isTemporary: true };
}
