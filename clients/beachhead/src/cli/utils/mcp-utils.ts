import {
    type NormalizedMCPServer,
    parseServerConfigFromJSON,
} from "@augment-internal/sidecar-libs/src/tools/mcp-server-utils";
import { McpServerConfig } from "@augment-internal/sidecar-libs/src/tools/tool-types";
import * as fs from "fs";

/**
 * Parse MCP server configurations from a JSON file or inline JSON string.
 */
export async function parseMcpServerConfig(configInput: string): Promise<McpServerConfig[]> {
    let configContent: string;

    // Check if the input looks like a JSON string (starts with [ or {)
    if (configInput.trim().startsWith("[") || configInput.trim().startsWith("{")) {
        // Treat as inline JSON
        configContent = configInput;
    } else {
        // Treat as file path
        try {
            configContent = await fs.promises.readFile(configInput, "utf8");
        } catch (error) {
            throw new Error(
                `Failed to read config file "${configInput}": ${error instanceof Error ? error.message : String(error)}`
            );
        }
    }

    return parseMcpServerConfigFromContent(configContent);
}

/**
 * Parse MCP server configurations from JSON content.
 * Uses the parseServerConfigFromJSON method from mcp-server-model and converts to McpServerConfig[].
 */
function parseMcpServerConfigFromContent(configContent: string): McpServerConfig[] {
    try {
        const normalizedServers = parseServerConfigFromJSON(configContent);

        // Convert NormalizedMCPServer[] to McpServerConfig[]
        return normalizedServers.map((server: NormalizedMCPServer) => {
            if (server.type === "stdio") {
                // Type assertion to access stdio-specific properties
                const stdioServer = server as {
                    type: "stdio";
                    name?: string;
                    command: string;
                    env?: Record<string, string | number | boolean | null | undefined>;
                    useShellInterpolation?: boolean;
                };
                const config: McpServerConfig = {
                    type: "stdio",
                    name: server.name,
                    command: stdioServer.command,
                };

                // The webview stores args in the command string, but CLI expects separate args array
                // For now, we'll keep the command as-is since it's already combined

                if (stdioServer.env) {
                    // Convert env values to strings as required by McpServerConfig
                    const envRecord: Record<string, string> = {};
                    for (const [key, value] of Object.entries(stdioServer.env)) {
                        if (value != null) {
                            envRecord[key] = String(value);
                        }
                    }
                    config.env = envRecord;
                }

                if (stdioServer.useShellInterpolation !== undefined) {
                    config.useShellInterpolation = stdioServer.useShellInterpolation;
                }

                return config;
            } else {
                // HTTP or SSE server
                const httpServer = server as {
                    type: "http" | "sse";
                    name?: string;
                    url: string;
                };
                const config: McpServerConfig = {
                    type: server.type,
                    name: server.name,
                    url: httpServer.url,
                };

                return config;
            }
        });
    } catch (error) {
        throw new Error(
            `Failed to parse MCP server configuration: ${error instanceof Error ? error.message : String(error)}`
        );
    }
}

/**
 * Validate MCP server configurations for common issues.
 */
export function validateMcpServerConfigurations(mcpServers: McpServerConfig[]): void {
    const serverNames = new Set<string>();

    for (const server of mcpServers) {
        // Check for duplicate names
        if (serverNames.has(server.name || "")) {
            throw new Error(
                `Duplicate MCP server name: "${server.name}". Server names must be unique.`
            );
        }
        if (server.name) {
            serverNames.add(server.name);
        }

        // Validate server-specific configurations
        if (server.type === "stdio") {
            // For stdio servers, validate command exists (basic check)
            if (!server.command) {
                throw new Error(
                    `MCP server "${server.name}" is missing required command for stdio type`
                );
            }

            // Validate timeout is reasonable
            if (
                server.timeoutMs !== undefined &&
                (server.timeoutMs < 1000 || server.timeoutMs > 300000)
            ) {
                throw new Error(
                    `MCP server "${server.name}" timeout must be between 1000ms and 300000ms (5 minutes)`
                );
            }

            // Validate environment variables
            if (server.env) {
                for (const [key, value] of Object.entries(server.env)) {
                    if (typeof key !== "string" || typeof value !== "string") {
                        throw new Error(
                            `MCP server "${server.name}" environment variables must be string key-value pairs`
                        );
                    }
                }
            }
        } else {
            // For HTTP/SSE servers, validate URL format
            if (!server.url) {
                throw new Error(
                    `MCP server "${server.name}" is missing required url for ${server.type} type`
                );
            }

            try {
                new URL(server.url);
            } catch {
                throw new Error(`MCP server "${server.name}" has invalid URL: "${server.url}"`);
            }

            // Validate timeout is reasonable
            if (
                server.timeoutMs !== undefined &&
                (server.timeoutMs < 1000 || server.timeoutMs > 60000)
            ) {
                throw new Error(
                    `MCP server "${server.name}" timeout must be between 1000ms and 60000ms (1 minute) for HTTP/SSE servers`
                );
            }
        }
    }
}

/**
 * Parse MCP server configurations from command line arguments.
 */
export async function parseMcpServerConfigurations(
    mcpConfig: string[] | undefined
): Promise<McpServerConfig[]> {
    const mcpServers: McpServerConfig[] = [];

    // Parse configuration files or inline JSON
    if (mcpConfig) {
        for (const configInput of mcpConfig) {
            try {
                const configs = await parseMcpServerConfig(configInput);
                mcpServers.push(...configs);
            } catch (error) {
                const inputType =
                    configInput.trim().startsWith("[") || configInput.trim().startsWith("{")
                        ? "inline JSON"
                        : "file";
                throw new Error(
                    `Failed to parse MCP server configuration from ${inputType}: ${error instanceof Error ? error.message : String(error)}`
                );
            }
        }
    }

    // Validate the final configuration
    try {
        validateMcpServerConfigurations(mcpServers);
    } catch (error) {
        throw new Error(
            `MCP server configuration validation failed: ${error instanceof Error ? error.message : String(error)}`
        );
    }

    return mcpServers;
}
