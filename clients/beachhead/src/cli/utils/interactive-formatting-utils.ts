/**
 * CLI formatting utilities for the beachhead client
 */
import { logger } from "@sentry/node";

/**
 * Simple thinking animation for CLI mode with duration tracking
 */
export class ThinkingAnimation {
    private interval: NodeJS.Timeout | null = null;
    private frames = ["⠋", "⠙", "⠹", "⠸", "⠼", "⠴", "⠦", "⠧", "⠇", "⠏"];
    private currentFrame = 0;
    private isRunning = false;
    private startTime: number = 0;
    private baseMessage: string = "";

    private formatDuration(seconds: number): string {
        if (seconds < 5) {
            return ""; // Show nothing for less than 5 seconds
        } else if (seconds < 10) {
            return " for a few seconds";
        } else if (seconds < 60) {
            return " for a few seconds";
        } else if (seconds < 120) {
            return " for a minute";
        } else if (seconds < 600) {
            return " for several minutes";
        } else if (seconds < 1200) {
            return " for more than 10 minutes";
        } else if (seconds < 3600) {
            return " for more than 20 minutes";
        } else {
            return " for more than an hour";
        }
    }

    start(message: string = "🤖 Thinking"): void {
        if (this.isRunning) {
            this.stop();
        }

        this.isRunning = true;
        this.currentFrame = 0;
        this.startTime = Date.now();
        this.baseMessage = message;

        // Show initial message
        process.stdout.write(`${message} ${this.frames[this.currentFrame]}`);

        this.interval = setInterval(() => {
            // Guard against race condition: don't update if animation was stopped
            if (!this.isRunning) {
                return;
            }

            const elapsedSeconds = Math.floor((Date.now() - this.startTime) / 1000);
            const durationText = this.formatDuration(elapsedSeconds);
            const fullMessage = `${this.baseMessage}${durationText}`;

            // Clear line and write new content
            process.stdout.write(`\r\x1B[K${fullMessage} ${this.frames[this.currentFrame]}`);
            this.currentFrame = (this.currentFrame + 1) % this.frames.length;
        }, 100);
    }

    stop(): void {
        if (!this.isRunning) {
            return;
        }

        this.isRunning = false;

        if (this.interval) {
            clearInterval(this.interval);
            this.interval = null;
        }

        // Clear the line completely
        process.stdout.write("\r\x1B[K");
    }
}

// Define literal types for the different header and section titles
export type CliHeaderTitle = "USER" | "ASSISTANT";
export type CliSectionTitle = string; // Tool names are dynamic, so we keep this flexible

/**
 * Formats headers for user and assistant messages in CLI mode
 */
export function formatCliHeader(title: CliHeaderTitle, interactiveMode: boolean = false): string {
    if (title === "USER") {
        return "\n>";
    } else if (title === "ASSISTANT") {
        if (interactiveMode) {
            return "🤖";
        } else {
            return "\n🤖";
        }
    }
    // This should never happen with the literal type, but keeping for safety
    const line = "─".repeat(40);
    return `\n${line}\n${String(title)}\n${line}`;
}

/**
 * Formats sections for tool calls and results in CLI mode
 * @param title The section title (e.g., "🔧 view", "📋 save-file")
 */
export function formatCliSection(title: CliSectionTitle): string {
    // Use grey color (ANSI code 90) for tool headers
    const greyColor = "\x1b[90m";
    const resetColor = "\x1b[0m";
    return `\n${greyColor}${title}${resetColor}`;
}

/**
 * Formats content with optional line truncation
 * @param content The content to format
 * @param maxLines Maximum number of lines to show before truncating
 */
export function formatCliContent(content: string, maxLines: number = 20): string {
    const lines = content.split("\n");
    if (lines.length <= maxLines) {
        return content;
    }
    const truncated = lines.slice(0, maxLines).join("\n");
    return `${truncated}\n... (${lines.length - maxLines} more lines)`;
}

/**
 * Formats tool call parameters in a nicer format
 * @param input The tool input object
 * @returns Formatted parameters string
 */
export function formatToolCallParameters(input: Record<string, any>): string {
    const lines: string[] = [];

    for (const [key, value] of Object.entries(input)) {
        let formattedValue: string;

        if (typeof value === "string") {
            formattedValue = `"${value}"`;
        } else if (typeof value === "number" || typeof value === "boolean") {
            formattedValue = String(value);
        } else if (value === null) {
            formattedValue = "null";
        } else if (value === undefined) {
            formattedValue = "undefined";
        } else if (Array.isArray(value)) {
            formattedValue = JSON.stringify(value);
        } else if (typeof value === "object") {
            formattedValue = JSON.stringify(value, null, 2);
        } else {
            formattedValue = JSON.stringify(value);
        }

        lines.push(`${key}: ${formattedValue}`);
    }

    return lines.join("\n");
}

/**
 * Parses and formats launch-process tool output for better readability
 * @param content The raw tool output content
 * @returns Formatted content or original if not a launch-process result
 */
export function formatLaunchProcessOutput(content: string): string {
    // Check if this looks like launch-process output
    if (
        !content.includes("<return-code>") ||
        !content.includes("Here are the results from executing the command")
    ) {
        return content;
    }

    try {
        // Extract return code
        const returnCodeMatch = content.match(/<return-code>\s*(\d+)\s*<\/return-code>/);
        const returnCode = returnCodeMatch ? parseInt(returnCodeMatch[1]) : null;

        // Extract output - beachhead uses <output> tags
        let outputMatch = content.match(/<output>([\s\S]*?)<\/output>/);

        const output = outputMatch ? outputMatch[1].trim() : "";

        // Format the output nicely
        let formatted = "";

        if (returnCode !== null) {
            if (returnCode === 0) {
                formatted += "✅ Command completed successfully\n";
            } else {
                formatted += `  Command failed with exit code ${returnCode}\n`;
            }
        }

        if (output) {
            formatted += `\n📤 Output:\n${output}`;
        } else {
            formatted += "\n(No output)";
        }

        return formatted;
    } catch (error) {
        // If parsing fails, return original content
        logger.error(`Failed to parse launch-process output: ${String(error)}`);
        return content;
    }
}

/**
 * ANSI color codes for terminal output
 */
const COLORS = {
    RED: "\x1b[31m",
    GREEN: "\x1b[32m",
    CYAN: "\x1b[36m",
    YELLOW: "\x1b[33m",
    RESET: "\x1b[0m",
    BOLD: "\x1b[1m",
    DIM: "\x1b[2m",
};

/**
 * Format a diff with colors and nice headers for CLI display
 */
export function formatDiff(diffText: string): string {
    const lines = diffText.split("\n");
    const formattedLines: string[] = [];
    const MAX_DIFF_LINES = 15; // Limit diff size to match other tool output
    let diffLineCount = 0;
    let fileName = "";

    // Extract filename from the original headers
    for (const line of lines) {
        if (line.startsWith("--- ")) {
            const match = line.match(/^---\s+(.+?)\s+/);
            if (match) {
                fileName = match[1];
                break;
            }
        }
    }

    for (let i = 0; i < lines.length; i++) {
        const line = lines[i];

        // Skip the ugly file headers (--- and +++)
        if (line.startsWith("---") || line.startsWith("+++")) {
            continue;
        }

        // Count lines that are part of the actual diff content
        if (
            line.startsWith("@@") ||
            line.startsWith("-") ||
            line.startsWith("+") ||
            (line.length > 0 && !line.startsWith("\\") && i > 1)
        ) {
            diffLineCount++;

            if (diffLineCount > MAX_DIFF_LINES) {
                const remainingLines = lines.length - i;
                formattedLines.push(`${COLORS.DIM}... ${remainingLines} more lines${COLORS.RESET}`);
                break;
            }
        }

        if (line.startsWith("@@")) {
            // Parse the hunk header to extract line numbers
            const hunkMatch = line.match(/@@\s*-(\d+)(?:,(\d+))?\s*\+(\d+)(?:,(\d+))?\s*@@/);
            if (hunkMatch) {
                const oldStart = parseInt(hunkMatch[1]);
                const oldCount = hunkMatch[2] ? parseInt(hunkMatch[2]) : 1;
                const newStart = parseInt(hunkMatch[3]);
                const newCount = hunkMatch[4] ? parseInt(hunkMatch[4]) : 1;

                // Create human-readable header
                const oldRange =
                    oldCount === 1
                        ? `line ${oldStart}`
                        : `lines ${oldStart}-${oldStart + oldCount - 1}`;
                const newRange =
                    newCount === 1
                        ? `line ${newStart}`
                        : `lines ${newStart}-${newStart + newCount - 1}`;

                const humanHeader = fileName
                    ? `📝 ${fileName} (${oldRange} → ${newRange})`
                    : `📝 ${oldRange} → ${newRange}`;

                formattedLines.push(`${COLORS.CYAN}${humanHeader}${COLORS.RESET}`);
            } else {
                // Fallback to original format if parsing fails
                const cleanHeader = line.replace(/^@@\s*/, "").replace(/\s*@@$/, "");
                formattedLines.push(`${COLORS.CYAN}@@ ${cleanHeader} @@${COLORS.RESET}`);
            }
        } else if (line.startsWith("-")) {
            // Removed line - red
            formattedLines.push(`${COLORS.RED}${line}${COLORS.RESET}`);
        } else if (line.startsWith("+")) {
            // Added line - green
            formattedLines.push(`${COLORS.GREEN}${line}${COLORS.RESET}`);
        } else if (line.length > 0 && !line.startsWith("\\")) {
            // Context lines - dim
            formattedLines.push(`${COLORS.DIM}${line}${COLORS.RESET}`);
        }
    }

    return formattedLines.join("\n");
}

/**
 * Check if a tool result is from a file editing tool
 */
export function isFileEditToolResult(toolName: string, toolResult: string): boolean {
    if (toolName !== "str-replace-editor") {
        return false;
    }

    // Check if the result contains file edit patterns
    return /Successfully edited the file|Failed to edit the file|Partially edited the file/.test(
        toolResult
    );
}
