import {
    FileDeletedEvent,
    FileDidMoveEvent,
} from "@augment-internal/sidecar-libs/src/agent/agent-edit-types";
import {
    FileDetails,
    FileType,
    IClientWorkspaces,
    ListDirectoryResult,
    PathInfo,
} from "@augment-internal/sidecar-libs/src/client-interfaces/client-workspaces";
import { getErrmsg } from "@augment-internal/sidecar-libs/src/exceptions";
import { ISidecarDisposable } from "@augment-internal/sidecar-libs/src/lifecycle/disposable-types";
import { QualifiedPathName } from "@augment-internal/sidecar-libs/src/workspace/qualified-path-name";
import * as path from "path";

import { getLogger } from "../logging";
// TODO(AU-8547): Move these to the sidecar
import {
    deleteFile,
    fileExists,
    makeDirs,
    readDirectory,
    readFileUtf8,
    statFile,
    writeFileUtf8,
} from "../utils/fs-utils";
import { directoryContainsPath, isAbsolutePathName, relativePathName } from "../utils/path-utils";
import { FileType as BeachheadFileType } from "../utils/types";
import { BeachheadWorkspaceManager } from "../workspace-manager";

// BeachheadClientWorkspaces provides a client workspaces implementation for the
// beachhead client. This allows the sidecar tools implementation to be used in
// the beachhead client.
export class BeachheadClientWorkspaces implements IClientWorkspaces {
    private logger = getLogger("BeachheadClientWorkspaces");

    constructor(private workspaceManager: BeachheadWorkspaceManager) {}

    getCwd(): Promise<string | undefined> {
        return Promise.resolve(this.workspaceManager.workspaceRoot);
    }

    getWorkspaceRoot(): Promise<string | undefined> {
        return Promise.resolve(this.workspaceManager.workspaceRoot);
    }

    async readFile(path: string): Promise<FileDetails> {
        const qualifiedPath = getQualifiedPath(path, this.workspaceManager.workspaceRoot);
        if (qualifiedPath === undefined) {
            return {};
        }
        const contents = await readWorkspaceFile(qualifiedPath);
        return {
            contents,
            filepath: qualifiedPath,
        };
    }

    async writeFile(filePath: QualifiedPathName, contents: string): Promise<void> {
        try {
            // Ensure parent directory exists before writing the file
            const parentDir = path.dirname(filePath.absPath);
            await makeDirs(parentDir);

            await writeFileUtf8(filePath.absPath, contents);
            this.logger.debug(`Wrote file ${filePath.absPath}`);
        } catch (e) {
            this.logger.error(`Failed to write file ${filePath.absPath}: ${getErrmsg(e)}`);
        }
    }

    async deleteFile(filePath: QualifiedPathName): Promise<void> {
        // Check if file exists before trying to delete it
        if (!fileExists(filePath.absPath)) {
            return;
        }

        try {
            await deleteFile(filePath.absPath);
            this.logger.debug(`Deleted file ${filePath.absPath}`);
        } catch (e) {
            this.logger.error(`Failed to delete file ${filePath.absPath}: ${getErrmsg(e)}`);
        }
    }

    async getQualifiedPathName(path: string): Promise<QualifiedPathName | undefined> {
        return Promise.resolve(getQualifiedPath(path, this.workspaceManager.workspaceRoot));
    }

    async getPathInfo(path: string): Promise<PathInfo> {
        const qualifiedPath = getQualifiedPath(path, this.workspaceManager.workspaceRoot);
        if (qualifiedPath === undefined) {
            return { exists: false };
        }

        try {
            const stat = await statFile(qualifiedPath.absPath);
            let type = FileType.Unknown;

            // Map beachhead FileType to sidecar FileType
            if (stat.type === BeachheadFileType.file) {
                type = FileType.File;
            } else if (stat.type === BeachheadFileType.directory) {
                type = FileType.Directory;
            }

            return {
                type,
                filepath: qualifiedPath,
                exists: true,
            };
        } catch (e) {
            this.logger.debug(
                `Failed to get path info for ${qualifiedPath.absPath}: ${getErrmsg(e)}`
            );
            return {
                exists: false,
                filepath: qualifiedPath,
            };
        }
    }

    async findFiles(
        _includeGlob: string,
        _excludeGlob?: string | null,
        _maxResults?: number,
        _timelimit?: number
    ): Promise<QualifiedPathName[]> {
        // TODO implement
        return Promise.resolve([]);
    }

    async listDirectory(
        path: string,
        depth: number = 2,
        showHidden: boolean = false
    ): Promise<ListDirectoryResult> {
        // Get the qualified path to ensure it's within the workspace
        const qualifiedPath = getQualifiedPath(path, this.workspaceManager.workspaceRoot);
        if (!qualifiedPath) {
            return { entries: [], errorMessage: "Path is outside the workspace" };
        }

        try {
            // Check if the path exists and is a directory
            const stat = await statFile(qualifiedPath.absPath);
            if (stat.type !== BeachheadFileType.directory) {
                return { entries: [], errorMessage: "Path is not a directory" };
            }

            // List directory contents with specified depth and hidden file visibility
            const results: string[] = [];
            await this._listDirectoryRecursive(
                qualifiedPath.absPath,
                qualifiedPath.absPath,
                0,
                depth,
                results,
                showHidden
            );

            // Sort results for consistent output
            results.sort();
            return { entries: results };
        } catch (error) {
            const errorMessage =
                error instanceof Error ? error.message : "Directory not found or cannot be read";
            return { entries: [], errorMessage };
        }
    }

    private async _listDirectoryRecursive(
        basePath: string,
        currentPath: string,
        currentDepth: number,
        maxDepth: number,
        results: string[],
        showHidden: boolean = false
    ): Promise<void> {
        if (currentDepth >= maxDepth) {
            return;
        }

        try {
            const entries = await readDirectory(currentPath);

            for (const [name, type] of entries) {
                // Skip hidden files and directories (those starting with '.') unless showHidden is true
                if (!showHidden && name.startsWith(".")) {
                    continue;
                }

                const fullPath = path.join(currentPath, name);
                const relativePath = path.relative(basePath, fullPath);

                // Add the current entry to results
                results.push(relativePath || ".");

                // If it's a directory and we haven't reached max depth, recurse
                // current child is at depth currentDepth + 1
                if (type === BeachheadFileType.directory && currentDepth + 1 < maxDepth) {
                    await this._listDirectoryRecursive(
                        basePath,
                        fullPath,
                        currentDepth + 1,
                        maxDepth,
                        results,
                        showHidden
                    );
                }
            }
        } catch (error) {
            // Ignore errors for individual directories (e.g., permission denied)
            this.logger.warn(`Failed to list directory ${currentPath}:`, error);
        }
    }

    getRipgrepPath(): Promise<string | undefined> {
        return Promise.resolve("/usr/bin/rg");
    }

    onFileDeleted(callback: (event: FileDeletedEvent) => void): ISidecarDisposable {
        return this.workspaceManager.onFileDeleted(callback);
    }

    onFileDidMove(callback: (event: FileDidMoveEvent) => void): ISidecarDisposable {
        return this.workspaceManager.onFileDidMove(callback);
    }
}

// Get the absolute path of a file within the workspace
export function getQualifiedPath(
    path: string,
    workspaceRoot: string
): QualifiedPathName | undefined {
    if (isAbsolutePathName(path)) {
        if (!directoryContainsPath(workspaceRoot, path)) {
            return undefined;
        }
        const relativePath = relativePathName(workspaceRoot, path);
        if (relativePath === undefined) {
            return undefined;
        }
        return new QualifiedPathName(workspaceRoot, relativePath);
    }
    return new QualifiedPathName(workspaceRoot, path);
}

// Read a file from the workspace
export async function readWorkspaceFile(path: QualifiedPathName): Promise<string | undefined> {
    if (!fileExists(path.absPath)) {
        return undefined;
    }
    try {
        return await readFileUtf8(path.absPath);
    } catch (e) {
        return undefined;
    }
}
