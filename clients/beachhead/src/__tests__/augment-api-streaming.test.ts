import { APIServerImplWithErrorReporting, AuthSessionStore } from "../augment-api";
import { AugmentConfigListener } from "../augment-config-listener";

function createMockFetch(responseContent: string) {
    return jest.fn().mockImplementation(async () => {
        const encoder = new TextEncoder();
        const stream = new ReadableStream({
            start(controller) {
                const bytes = encoder.encode(responseContent);
                controller.enqueue(bytes);
                controller.close();
            },
        });

        return {
            ok: true,
            status: 200,
            headers: new Headers({ "content-type": "application/json" }),
            body: stream,
        };
    });
}

class MockConfigListener extends AugmentConfigListener {
    getConfig() {
        return {
            apiToken: "test-token",
            completionURL: "http://test-server",
            modelName: "test-model",
            chat: {
                url: "http://test-server",
                model: "test-model",
            },
            completion: {
                url: "http://test-server",
                model: "test-model",
            },
        };
    }
}

// Mock auth session store
class MockAuthSessionStore implements AuthSessionStore {
    useOAuth = false;

    async getSession() {
        return null;
    }

    async saveSession(_accessToken: string, _tenantURL: string) {
        // Mock implementation - do nothing
    }

    async removeSession() {
        // Mock implementation - do nothing
    }
}

describe("APIServerImplWithErrorReporting streaming with spaces", () => {
    let mockFetch: jest.MockedFunction<any>;
    let mockConfigListener: MockConfigListener;
    let mockAuthStore: MockAuthSessionStore;

    beforeEach(() => {
        mockConfigListener = new MockConfigListener();
        mockAuthStore = new MockAuthSessionStore();
        // Initialize mock fetch
        mockFetch = jest.fn();
    });

    test("handles spaces before JSON payloads in line-delimited stream", async () => {
        const responseContent = [
            '   {"text": "Hello"}',
            '     {"text": " world"}',
            '{"text": "!"}',
            '  {"text": " How are you?"}',
            "   ",
            '    {"text": " Fine."}',
            "          ",
        ].join("\n");

        mockFetch = createMockFetch(responseContent);

        // Create a new API server instance with the mock fetch
        const apiServer = new APIServerImplWithErrorReporting(
            mockConfigListener,
            mockAuthStore,
            "test-session",
            "test-user-agent",
            mockFetch
        );

        const config = mockConfigListener.getConfig();
        const stream = await (apiServer as any).callApiStream(
            "test-request",
            config,
            "test-endpoint",
            { message: "test" },
            (json: any) => json
        );

        const results: any[] = [];
        for await (const result of stream) {
            results.push(result);
        }

        // Should capture all JSON lines, including those with leading spaces
        expect(results).toHaveLength(5);
        expect(results[0]).toEqual({ text: "Hello" });
        expect(results[1]).toEqual({ text: " world" });
        expect(results[2]).toEqual({ text: "!" });
        expect(results[3]).toEqual({ text: " How are you?" });
        expect(results[4]).toEqual({ text: " Fine." });
    }, 10000); // 10 second timeout
});
