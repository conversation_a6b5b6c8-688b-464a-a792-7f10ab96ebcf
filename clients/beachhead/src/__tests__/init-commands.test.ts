// Import fs after mocking
import * as fs from "fs";

import { AgentConfig } from "../agent-config";
import { CommandStatus, InitCommands } from "../workspace-init";

// Mock dependencies
jest.mock("child_process");
jest.mock("../logging", () => ({
    getLogger: jest.fn().mockReturnValue({
        info: jest.fn(),
        error: jest.fn(),
        warn: jest.fn(),
        debug: jest.fn(),
    }),
}));

// Mock fs module
jest.mock("fs", () => ({
    existsSync: jest.fn().mockReturnValue(false),
    readdirSync: jest.fn().mockReturnValue([]),
    writeFileSync: jest.fn(),
    chmodSync: jest.fn(),
    promises: {
        mkdir: jest.fn(),
        writeFile: jest.fn(),
        chmod: jest.fn(),
        stat: jest.fn(),
        readFile: jest.fn(),
    },
}));

describe("InitCommands", () => {
    let mockConfig: AgentConfig;
    let initCommands: InitCommands;
    const testPath = "/test/workspace/path";
    let commandNames = [
        "git lfs install",
        "git clone",
        "git submodule update",
        "git apply",
        "setup script",
    ];
    let commandCount = commandNames.length;

    beforeEach(() => {
        // Reset mocks
        jest.clearAllMocks();

        // Create a mock AgentConfig
        mockConfig = {
            git_repo: jest.fn().mockReturnValue("https://github.com/example/repo.git"),
            git_ref: jest.fn().mockReturnValue("main"),
            initial_patch: jest.fn().mockReturnValue(null),
            setup_script: jest.fn().mockReturnValue(null),
        } as unknown as AgentConfig;

        // Reset fs mocks to default values
        (fs.existsSync as jest.Mock).mockReturnValue(false);
        (fs.readdirSync as jest.Mock).mockReturnValue([]);

        // Create InitCommands with mocked dependencies
        initCommands = new InitCommands(mockConfig, testPath);
    });

    describe("constructor", () => {
        it("should initialize with the correct commands", () => {
            const commands = (initCommands as any)._cmds;

            expect(commands.length).toBe(commandCount);
            for (let i = 0; i < commandCount; i++) {
                expect(commands[i].name()).toBe(commandNames[i]);
            }
        });

        it("should use workspace path for setup persistence when persistentRoot is not provided", () => {
            const initCommandsWithoutPersistent = new InitCommands(mockConfig, testPath);
            const setupPersistence = (initCommandsWithoutPersistent as any)._setupPersistence;

            // The setup persistence should use the workspace path directly
            expect(setupPersistence.directoryPath).toBe(testPath);
        });

        it("should use persistentRoot/setup_state for setup persistence when persistentRoot is provided", () => {
            const persistentRoot = "/persistent/root";
            const initCommandsWithPersistent = new InitCommands(
                mockConfig,
                testPath,
                persistentRoot
            );
            const setupPersistence = (initCommandsWithPersistent as any)._setupPersistence;

            // The setup persistence should use persistentRoot/setup_state
            expect(setupPersistence.directoryPath).toBe("/persistent/root/setup_state");
        });
    });

    describe("run", () => {
        it("should skip initialization if workspace exists and is not empty", async () => {
            // Mock that the workspace exists and is not empty
            (fs.existsSync as jest.Mock).mockReturnValue(true);
            (fs.readdirSync as jest.Mock).mockReturnValue(["some-file"]);

            const result = await initCommands.run();

            // Should return false to indicate skipped
            expect(result).toBe(false);

            // Should not run any commands
            const commands = (initCommands as any)._cmds;
            for (const cmd of commands) {
                expect(cmd.status()).toBe(CommandStatus.PENDING);
            }
        });

        it("should run all commands in sequence until completion", async () => {
            // Create separate mock functions for each command to track execution order
            const mockRuns = Array.from({ length: commandCount }, () =>
                jest.fn().mockResolvedValue(undefined)
            );

            // Replace the run method on all commands with individual mocks
            const commands = (initCommands as any)._cmds;
            for (let i = 0; i < commands.length; i++) {
                commands[i].run = mockRuns[i];
            }

            const result = await initCommands.run();

            // Should return true to indicate success
            expect(result).toBe(true);

            // Should have run all commands
            for (let i = 0; i < mockRuns.length; i++) {
                expect(mockRuns[i]).toHaveBeenCalledTimes(1);
            }

            // Verify commands were executed in sequence by checking call order
            // Each mock should be called before the next one
            for (let i = 1; i < mockRuns.length; i++) {
                expect(mockRuns[i - 1].mock.invocationCallOrder[0]).toBeLessThan(
                    mockRuns[i].mock.invocationCallOrder[0]
                );
            }
        });

        it("should stop running commands if one fails", async () => {
            // Mock commands to run successfully except the second one
            const commands = (initCommands as any)._cmds;

            // First command runs successfully
            commands[0].run = jest.fn().mockImplementation(async () => {
                commands[0]._status = CommandStatus.SUCCESS;
            });

            // Second command fails
            commands[1].run = jest.fn().mockImplementation(async () => {
                commands[1]._status = CommandStatus.FAILED;
            });

            // Third command should not run
            commands[2].run = jest.fn();

            const result = await initCommands.run();

            // Should still return true (initialization was attempted)
            expect(result).toBe(true);

            // Should have run only the first two commands
            expect(commands[0].run).toHaveBeenCalled();
            expect(commands[1].run).toHaveBeenCalled();
            expect(commands[2].run).not.toHaveBeenCalled();
        });
    });

    describe("overall_status", () => {
        it("should return RUNNING if any command is running", () => {
            const commands = (initCommands as any)._cmds;

            // Set first command to RUNNING
            commands[0]._status = CommandStatus.RUNNING;

            expect(initCommands.overall_status()).toBe(CommandStatus.RUNNING);
        });

        it("should return FAILED if any command failed", () => {
            const commands = (initCommands as any)._cmds;

            // Set first command to SUCCESS and second to FAILED
            commands[0]._status = CommandStatus.SUCCESS;
            commands[1]._status = CommandStatus.FAILED;

            expect(initCommands.overall_status()).toBe(CommandStatus.FAILED);
        });

        it("should return SKIPPED if all commands are skipped", () => {
            const commands = (initCommands as any)._cmds;

            // Set all commands to SKIPPED
            for (const cmd of commands) {
                cmd._status = CommandStatus.SKIPPED;
            }

            expect(initCommands.overall_status()).toBe(CommandStatus.SKIPPED);
        });

        it("should return PENDING if all commands are pending", () => {
            // By default, all commands are PENDING
            expect(initCommands.overall_status()).toBe(CommandStatus.PENDING);
        });

        it("should return SUCCESS if some commands succeeded and others were skipped", () => {
            const commands = (initCommands as any)._cmds;

            // Set first command to SUCCESS and others to SKIPPED
            commands[0]._status = CommandStatus.SUCCESS;
            for (let i = 1; i < commands.length; i++) {
                commands[i]._status = CommandStatus.SKIPPED;
            }

            expect(initCommands.overall_status()).toBe(CommandStatus.SUCCESS);
        });
    });

    describe("failed", () => {
        it("should return true if overall status is FAILED", () => {
            const commands = (initCommands as any)._cmds;

            // Set a command to FAILED
            commands[0]._status = CommandStatus.FAILED;

            expect(initCommands.failed()).toBe(true);
        });

        it("should return false if overall status is not FAILED", () => {
            const commands = (initCommands as any)._cmds;

            // Set commands to non-FAILED statuses
            commands[0]._status = CommandStatus.SUCCESS;
            commands[1]._status = CommandStatus.SKIPPED;

            expect(initCommands.failed()).toBe(false);
        });
    });

    describe("run_setup_script persistence", () => {
        let testPath: string;
        let mockOut: any;

        beforeEach(() => {
            testPath = "/test/workspace/path";
            mockOut = {
                write: jest.fn(),
            };
        });

        it("should skip setup script if no script is provided", async () => {
            // Setup: No setup script
            (mockConfig.setup_script as jest.Mock).mockReturnValue(null);

            // Get the setup script command (4th command, index 3)
            const runSetupScript = (initCommands as any).run_setup_script.bind(initCommands);

            // Execute
            const result = await runSetupScript(mockOut);

            // Verify
            expect(result).toBe(false);
            expect(mockOut.write).toHaveBeenCalledWith("No setup script provided. Skipping.\n");
            expect(fs.existsSync).not.toHaveBeenCalled();
        });

        it("should skip setup script if marker file exists", async () => {
            // Setup: Script exists but marker file also exists
            (mockConfig.setup_script as jest.Mock).mockReturnValue("#!/bin/bash\necho 'setup'");
            (fs.existsSync as jest.Mock).mockReturnValue(true);

            const runSetupScript = (initCommands as any).run_setup_script.bind(initCommands);

            // Execute
            const result = await runSetupScript(mockOut);

            // Verify
            expect(result).toBe(false);
            expect(mockOut.write).toHaveBeenCalledWith(
                "Setup script already completed. Skipping.\n"
            );
            expect(fs.existsSync).toHaveBeenCalledWith(`${testPath}/.setup_script_completed`);
        });

        it("should run setup script and create marker file on success", async () => {
            // Setup: Script exists, no marker file
            const testScript = "#!/bin/bash\necho 'Running setup'";
            (mockConfig.setup_script as jest.Mock).mockReturnValue(testScript);
            (fs.existsSync as jest.Mock).mockReturnValue(false);

            // Mock fs.promises methods
            (fs.promises.mkdir as jest.Mock).mockResolvedValue(undefined);
            (fs.promises.writeFile as jest.Mock).mockResolvedValue(undefined);
            (fs.promises.chmod as jest.Mock).mockResolvedValue(undefined);

            // Mock _spawn to succeed
            const mockSpawn = jest.fn().mockResolvedValue(undefined);
            (initCommands as any)._spawn = mockSpawn;

            const runSetupScript = (initCommands as any).run_setup_script.bind(initCommands);

            // Execute
            const result = await runSetupScript(mockOut);

            // Verify
            expect(result).toBe(true);
            expect(fs.existsSync).toHaveBeenCalledWith(`${testPath}/.setup_script_completed`);

            // Verify script was written and executed
            expect(fs.promises.writeFile).toHaveBeenCalledWith(
                expect.stringContaining("workspace-setup.sh"),
                testScript
            );
            expect(fs.promises.chmod).toHaveBeenCalledWith(
                expect.stringContaining("workspace-setup.sh"),
                0o755
            );
            expect(mockSpawn).toHaveBeenCalledWith(
                mockOut,
                [expect.stringContaining("workspace-setup.sh")],
                "",
                testPath
            );

            // Verify marker file was created
            expect(fs.promises.writeFile).toHaveBeenCalledWith(
                `${testPath}/.setup_script_completed`,
                ""
            );
        });

        it("should not create marker file if script execution fails", async () => {
            // Setup: Script exists, no marker file
            const testScript = "#!/bin/bash\nexit 1";
            (mockConfig.setup_script as jest.Mock).mockReturnValue(testScript);
            (fs.existsSync as jest.Mock).mockReturnValue(false);

            // Mock fs.promises methods
            (fs.promises.mkdir as jest.Mock).mockResolvedValue(undefined);
            (fs.promises.writeFile as jest.Mock).mockResolvedValue(undefined);
            (fs.promises.chmod as jest.Mock).mockResolvedValue(undefined);

            // Mock _spawn to fail
            const mockSpawn = jest.fn().mockRejectedValue(new Error("Script failed"));
            (initCommands as any)._spawn = mockSpawn;

            const runSetupScript = (initCommands as any).run_setup_script.bind(initCommands);

            // Execute and expect it to throw
            await expect(runSetupScript(mockOut)).rejects.toThrow("Script failed");

            // Verify marker file was NOT created (writeFile should only be called once for the script)
            expect(fs.promises.writeFile).toHaveBeenCalledTimes(1);
            expect(fs.promises.writeFile).toHaveBeenCalledWith(
                expect.stringContaining("workspace-setup.sh"),
                testScript
            );
            expect(fs.promises.writeFile).not.toHaveBeenCalledWith(
                `${testPath}/.setup_script_completed`,
                ""
            );
        });

        it("should use correct marker file path", async () => {
            // Setup: Script exists, no marker file
            (mockConfig.setup_script as jest.Mock).mockReturnValue("#!/bin/bash\necho 'test'");
            (fs.existsSync as jest.Mock).mockReturnValue(false);

            const runSetupScript = (initCommands as any).run_setup_script.bind(initCommands);

            // Execute (will fail at _spawn but that's ok for this test)
            try {
                await runSetupScript(mockOut);
            } catch (e) {
                // Ignore the error, we just want to check the marker file path
            }

            // Verify the correct marker file path was checked
            expect(fs.existsSync).toHaveBeenCalledWith(`${testPath}/.setup_script_completed`);
        });
    });

    describe("init commands persistence", () => {
        it("should skip all init commands if setup script marker file exists", async () => {
            // Setup: Setup script already completed (which means all init commands were successful)
            (fs.existsSync as jest.Mock).mockImplementation((path: string) => {
                return path.endsWith(".setup_script_completed");
            });

            const result = await initCommands.run();

            // Should return false to indicate skipped
            expect(result).toBe(false);

            // Should check for the setup script marker file
            expect(fs.existsSync).toHaveBeenCalledWith(`${testPath}/.setup_script_completed`);

            // Should not run any commands
            const commands = (initCommands as any)._cmds;
            for (const cmd of commands) {
                expect(cmd.status()).toBe(CommandStatus.PENDING);
            }
        });

        it("should run all commands if no marker file exists", async () => {
            // Setup: No marker files exist, workspace doesn't exist
            (fs.existsSync as jest.Mock).mockReturnValue(false);
            (fs.readdirSync as jest.Mock).mockReturnValue([]);

            // Mock all commands to succeed
            const commands = (initCommands as any)._cmds;
            for (const cmd of commands) {
                cmd.run = jest.fn().mockImplementation(async () => {
                    cmd._status = CommandStatus.SUCCESS;
                });
            }

            const result = await initCommands.run();

            // Should return true to indicate commands were run
            expect(result).toBe(true);

            // Should run all commands
            for (const cmd of commands) {
                expect(cmd.run).toHaveBeenCalled();
            }
        });

        it("should stop running commands if one fails", async () => {
            // Setup: No marker files exist, workspace doesn't exist
            (fs.existsSync as jest.Mock).mockReturnValue(false);
            (fs.readdirSync as jest.Mock).mockReturnValue([]);

            // Mock all commands with spies
            const commands = (initCommands as any)._cmds;
            for (let i = 0; i < commands.length; i++) {
                commands[i].run = jest.fn().mockImplementation(async () => {
                    // First command succeeds, second fails, rest should not be called
                    if (i === 0) {
                        commands[i]._status = CommandStatus.SUCCESS;
                    } else if (i === 1) {
                        commands[i]._status = CommandStatus.FAILED;
                    } else {
                        commands[i]._status = CommandStatus.SUCCESS;
                    }
                });
            }

            const result = await initCommands.run();

            // Should return true (initialization was attempted)
            expect(result).toBe(true);

            // Should run first two commands but not subsequent ones after failure
            expect(commands[0].run).toHaveBeenCalled();
            expect(commands[1].run).toHaveBeenCalled();
            if (commands.length > 2) {
                expect(commands[2].run).not.toHaveBeenCalled();
            }
        });
    });
});
