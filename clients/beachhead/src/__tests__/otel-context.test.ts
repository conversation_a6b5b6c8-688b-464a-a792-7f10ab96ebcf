import { trace } from "@opentelemetry/api";
import { OTLPTraceExporter } from "@opentelemetry/exporter-trace-otlp-http";
import { BatchSpanProcessor } from "@opentelemetry/sdk-trace-base";
import { NodeTracerProvider } from "@opentelemetry/sdk-trace-node";

import { getTracer, withParentContext } from "../otel";

function setupTestTracing() {
    const exporter = new OTLPTraceExporter();

    const provider = new NodeTracerProvider({
        spanProcessors: [new BatchSpanProcessor(exporter, {})],
    });

    provider.register();

    return async () => {
        await Promise.all([provider.shutdown(), exporter.shutdown()]);
    };
}

function getContext(): [string, string, string] {
    let traceId = "";
    let spanId = "";
    const parentSpanId = trace.getActiveSpan()?.spanContext().spanId || "";
    getTracer().startActiveSpan("test", (span) => {
        const spanContext = span.spanContext();
        traceId = spanContext.traceId;
        spanId = spanContext.spanId;
    });
    return [traceId, spanId, parentSpanId];
}

let shutdownTracing: () => Promise<void>;

describe("withParentContext", () => {
    beforeAll(() => {
        shutdownTracing = setupTestTracing();
    });
    afterAll(async () => {
        await shutdownTracing();
    });

    it("should execute callback with parent trace context", () => {
        const mockOtelContext = {
            trace_id: "12345678901234567890123456789012",
            parent_span_id: "1234567890123456",
            trace_flags: "1",
            timestamp: new Date().toISOString(),
        };

        const [traceId, spanId, parentSpanId] = withParentContext(mockOtelContext, getContext);

        expect(traceId).toBe(mockOtelContext.trace_id);
        expect(spanId).not.toBe(mockOtelContext.parent_span_id);
        expect(parentSpanId).toBe(mockOtelContext.parent_span_id);
    });

    it("should execute callback without parent context when trace_id is missing", () => {
        const mockOtelContext = {
            parent_span_id: "1234567890123456",
            trace_flags: "1",
            timestamp: new Date().toISOString(),
        };

        const [_, spanId, parentSpanId] = withParentContext(mockOtelContext, getContext);

        expect(spanId).not.toBe(mockOtelContext.parent_span_id);
        expect(parentSpanId).not.toBe(mockOtelContext.parent_span_id);
    });

    it("should execute callback without parent context when span_id is missing", () => {
        const mockOtelContext = {
            trace_id: "12345678901234567890123456789012",
            trace_flags: "1",
            timestamp: new Date().toISOString(),
        };

        const [traceId, _] = withParentContext(mockOtelContext, getContext);

        expect(traceId).not.toBe(mockOtelContext.trace_id);
    });

    it("should execute callback without parent context when timestamp is missing", () => {
        const mockOtelContext = {
            trace_id: "12345678901234567890123456789012",
            parent_span_id: "1234567890123456",
            trace_flags: "1",
        };

        const [traceId, spanId, parentSpanId] = withParentContext(mockOtelContext, getContext);

        expect(traceId).not.toBe(mockOtelContext.trace_id);
        expect(spanId).not.toBe(mockOtelContext.parent_span_id);
        expect(parentSpanId).not.toBe(mockOtelContext.parent_span_id);
    });

    it("should execute callback without parent context when trace_id is invalid", () => {
        const mockOtelContext = {
            trace_id: "invalid-trace-id",
            parent_span_id: "1234567890123456",
            trace_flags: "1",
            timestamp: new Date().toISOString(),
        };

        const [traceId, spanId, parentSpanId] = withParentContext(mockOtelContext, getContext);

        expect(traceId).not.toBe(mockOtelContext.trace_id);
        expect(spanId).not.toBe(mockOtelContext.parent_span_id);
        expect(parentSpanId).not.toBe(mockOtelContext.parent_span_id);
    });

    it("should execute callback without parent context when span_id is invalid", () => {
        const mockOtelContext = {
            trace_id: "12345678901234567890123456789012",
            parent_span_id: "invalid-span-id",
            trace_flags: "1",
            timestamp: new Date().toISOString(),
        };

        const [traceId, spanId, parentSpanId] = withParentContext(mockOtelContext, getContext);

        expect(traceId).not.toBe(mockOtelContext.trace_id);
        expect(spanId).not.toBe(mockOtelContext.parent_span_id);
        expect(parentSpanId).not.toBe(mockOtelContext.parent_span_id);
    });

    it("should execute callback without parent context when timestamp is old", () => {
        const mockOtelContext = {
            trace_id: "12345678901234567890123456789012",
            parent_span_id: "1234567890123456",
            trace_flags: "1",
            timestamp: "2024-01-01T00:00:00Z",
        };

        const [traceId, spanId, parentSpanId] = withParentContext(mockOtelContext, getContext);

        expect(traceId).not.toBe(mockOtelContext.trace_id);
        expect(spanId).not.toBe(mockOtelContext.parent_span_id);
        expect(parentSpanId).not.toBe(mockOtelContext.parent_span_id);
    });
});
