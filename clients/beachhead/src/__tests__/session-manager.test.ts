import { Exchange } from "@augment-internal/sidecar-libs/src/chat/chat-types";
import * as fs from "fs";
import * as os from "os";
import * as path from "path";

import { AgentState } from "../agent_loop/state";
import { SessionData, SessionManager } from "../session-manager";

describe("SessionManager", () => {
    let tempDir: string;
    let sessionManager: SessionManager;

    beforeEach(() => {
        // Create a temporary directory for each test
        tempDir = fs.mkdtempSync(path.join(os.tmpdir(), "session-manager-test-"));
        sessionManager = new SessionManager(tempDir);
    });

    afterEach(() => {
        // Clean up the temporary directory
        if (tempDir && fs.existsSync(tempDir)) {
            fs.rmSync(tempDir, { recursive: true, force: true });
        }
    });

    describe("constructor", () => {
        it("should create sessions directory", () => {
            const sessionsDir = path.join(tempDir, "sessions");
            expect(fs.existsSync(sessionsDir)).toBe(true);
        });

        it("should create SessionManager with provided cache directory", () => {
            const testCacheDir = "/tmp/test-cache";
            const sessionManager = new SessionManager(testCacheDir);
            // Should not throw
            expect(sessionManager).toBeDefined();
        });

        it("should create SessionManager with saving enabled by default", () => {
            const sessionManager = new SessionManager(tempDir);
            expect(sessionManager).toBeDefined();
        });

        it("should create SessionManager with saving explicitly enabled", () => {
            const sessionManager = new SessionManager(tempDir, true);
            expect(sessionManager).toBeDefined();
        });

        it("should create SessionManager with saving disabled", () => {
            const sessionManager = new SessionManager(tempDir, false);
            expect(sessionManager).toBeDefined();
        });
    });

    describe("saveSession", () => {
        it("should save a new session", async () => {
            const agentState = new AgentState(
                undefined,
                "user guidelines",
                "workspace guidelines",
                "memories",
                "model-id"
            );

            await sessionManager.saveSession(agentState);

            const sessionId = agentState.conversationId;
            const sessionPath = path.join(tempDir, "sessions", `${sessionId}.json`);
            expect(fs.existsSync(sessionPath)).toBe(true);

            const savedData = JSON.parse(fs.readFileSync(sessionPath, "utf-8")) as SessionData;
            expect(savedData.sessionId).toBe(sessionId);
            expect(savedData.created).toBeDefined();
            expect(savedData.modified).toBeDefined();
            expect(savedData.chatHistory).toEqual([]);
            expect(savedData.agentState.userGuidelines).toBe("user guidelines");
            expect(savedData.agentState.workspaceGuidelines).toBe("workspace guidelines");
            expect(savedData.agentState.agentMemories).toBe("memories");
            expect(savedData.agentState.modelId).toBe("model-id");
        });

        it("should update existing session with new modified timestamp", async () => {
            const agentState = new AgentState(
                undefined,
                "user guidelines",
                "workspace guidelines",
                "memories",
                "model-id"
            );

            // Save initial session
            await sessionManager.saveSession(agentState);
            const sessionId = agentState.conversationId;

            // Load the saved session to get the created timestamp
            const initialSession = await sessionManager.loadSession(sessionId);
            const initialCreated = initialSession.created;
            const initialModified = initialSession.modified;

            // Wait a bit to ensure different timestamp
            await new Promise((resolve) => setTimeout(resolve, 10));

            // Save again
            await sessionManager.saveSession(agentState);

            // Load again and verify timestamps
            const updatedSession = await sessionManager.loadSession(sessionId);
            expect(updatedSession.created).toBe(initialCreated); // Should remain the same
            expect(updatedSession.modified).not.toBe(initialModified); // Should be updated
        });

        it("should save session with chat history", async () => {
            const agentState = new AgentState(
                undefined,
                "user guidelines",
                "workspace guidelines",
                "memories",
                "model-id"
            );

            // Add some mock chat history
            const mockExchange: Exchange = {
                request_id: "test-request-id",
                request_message: "test message",
                request_nodes: [],
                response_text: "test response",
                response_nodes: [],
            };

            agentState.addExchangeToHistory(mockExchange, true);

            await sessionManager.saveSession(agentState);

            const sessionId = agentState.conversationId;
            const savedSession = await sessionManager.loadSession(sessionId);

            expect(savedSession.chatHistory).toHaveLength(1);
            expect(savedSession.chatHistory[0].exchange.request_message).toBe("test message");
            expect(savedSession.chatHistory[0].exchange.response_text).toBe("test response");
            expect(savedSession.chatHistory[0].completed).toBe(true);
        });

        it("should not save session when saving is disabled", async () => {
            const sessionManagerDisabled = new SessionManager(tempDir, false);
            const agentState = new AgentState(
                undefined,
                "user guidelines",
                "workspace guidelines",
                "memories",
                "model-id"
            );

            // This should be a no-op
            await sessionManagerDisabled.saveSession(agentState);

            const sessionId = agentState.conversationId;
            const sessionPath = path.join(tempDir, "sessions", `${sessionId}.json`);

            // Session file should not exist
            expect(fs.existsSync(sessionPath)).toBe(false);
        });

        it("should not create any files when saving is disabled", async () => {
            const sessionManagerDisabled = new SessionManager(tempDir, false);
            const agentState1 = new AgentState();
            const agentState2 = new AgentState();

            // Try to save multiple sessions
            await sessionManagerDisabled.saveSession(agentState1);
            await sessionManagerDisabled.saveSession(agentState2);

            const sessionsDir = path.join(tempDir, "sessions");
            const sessionFiles = fs.readdirSync(sessionsDir);

            // No session files should be created
            expect(sessionFiles).toHaveLength(0);
        });
    });

    describe("loadSession", () => {
        it("should load an existing session", async () => {
            const agentState = new AgentState(
                undefined,
                "user guidelines",
                "workspace guidelines",
                "memories",
                "model-id"
            );
            await sessionManager.saveSession(agentState);

            const sessionId = agentState.conversationId;
            const loadedSession = await sessionManager.loadSession(sessionId);

            expect(loadedSession.sessionId).toBe(sessionId);
            expect(loadedSession.agentState.userGuidelines).toBe("user guidelines");
            expect(loadedSession.agentState.workspaceGuidelines).toBe("workspace guidelines");
            expect(loadedSession.agentState.agentMemories).toBe("memories");
            expect(loadedSession.agentState.modelId).toBe("model-id");
        });

        it("should throw error for non-existent session", async () => {
            await expect(sessionManager.loadSession("non-existent-id")).rejects.toThrow(
                "Failed to load session"
            );
        });

        it("should throw error for invalid session data", async () => {
            const sessionId = "invalid-session";
            const sessionPath = path.join(tempDir, "sessions", `${sessionId}.json`);

            // Write invalid JSON
            fs.writeFileSync(sessionPath, "invalid json");

            await expect(sessionManager.loadSession(sessionId)).rejects.toThrow(
                "Failed to load session"
            );
        });

        it("should throw error for session with missing required fields", async () => {
            const sessionId = "incomplete-session";
            const sessionPath = path.join(tempDir, "sessions", `${sessionId}.json`);

            // Write incomplete session data
            const incompleteData = { sessionId: "test" }; // missing created, modified, etc.
            fs.writeFileSync(sessionPath, JSON.stringify(incompleteData));

            await expect(sessionManager.loadSession(sessionId)).rejects.toThrow(
                "Invalid session data structure"
            );
        });
    });

    describe("sessionExists", () => {
        it("should return true for existing session", async () => {
            const agentState = new AgentState();
            await sessionManager.saveSession(agentState);

            const exists = sessionManager.sessionExists(agentState.conversationId);
            expect(exists).toBe(true);
        });

        it("should return false for non-existent session", () => {
            const exists = sessionManager.sessionExists("non-existent-id");
            expect(exists).toBe(false);
        });
    });

    describe("getAllSessionsMetadata", () => {
        it("should return empty array when no sessions exist", async () => {
            const metadata = await sessionManager.getAllSessionsMetadata();
            expect(metadata).toEqual([]);
        });

        it("should return metadata for all sessions", async () => {
            // Create multiple sessions
            const agentState1 = new AgentState();
            const agentState2 = new AgentState();

            await sessionManager.saveSession(agentState1);
            await new Promise((resolve) => setTimeout(resolve, 10)); // Ensure different timestamps
            await sessionManager.saveSession(agentState2);

            const metadata = await sessionManager.getAllSessionsMetadata();

            expect(metadata).toHaveLength(2);
            expect(metadata[0].sessionId).toBe(agentState2.conversationId); // Most recent first
            expect(metadata[1].sessionId).toBe(agentState1.conversationId);
            expect(metadata[0].exchangeCount).toBe(0);
            expect(metadata[1].exchangeCount).toBe(0);
        });

        it("should sort sessions by modified date (most recent first)", async () => {
            const agentState1 = new AgentState();
            const agentState2 = new AgentState();

            await sessionManager.saveSession(agentState1);
            await new Promise((resolve) => setTimeout(resolve, 10));
            await sessionManager.saveSession(agentState2);
            await new Promise((resolve) => setTimeout(resolve, 10));

            // Update first session (should move it to the top)
            await sessionManager.saveSession(agentState1);

            const metadata = await sessionManager.getAllSessionsMetadata();

            expect(metadata).toHaveLength(2);
            expect(metadata[0].sessionId).toBe(agentState1.conversationId); // Most recently modified
            expect(metadata[1].sessionId).toBe(agentState2.conversationId);
        });

        it("should handle corrupted session files gracefully", async () => {
            // Create a valid session
            const agentState = new AgentState();
            await sessionManager.saveSession(agentState);

            // Create a corrupted session file
            const corruptedPath = path.join(tempDir, "sessions", "corrupted.json");
            fs.writeFileSync(corruptedPath, "invalid json");

            const metadata = await sessionManager.getAllSessionsMetadata();

            // Should return only the valid session
            expect(metadata).toHaveLength(1);
            expect(metadata[0].sessionId).toBe(agentState.conversationId);
        });
    });

    describe("getLastSessionMetadata", () => {
        it("should return null when no sessions exist", async () => {
            const lastSession = await sessionManager.getLastSessionMetadata();
            expect(lastSession).toBeNull();
        });

        it("should return the most recent session", async () => {
            const agentState1 = new AgentState();
            const agentState2 = new AgentState();

            await sessionManager.saveSession(agentState1);
            await new Promise((resolve) => setTimeout(resolve, 10));
            await sessionManager.saveSession(agentState2);

            const lastSession = await sessionManager.getLastSessionMetadata();

            expect(lastSession).not.toBeNull();
            expect(lastSession!.sessionId).toBe(agentState2.conversationId);
        });
    });

    describe("restoreAgentState", () => {
        it("should restore agent state from session", async () => {
            const originalState = new AgentState(
                undefined,
                "user guidelines",
                "workspace guidelines",
                "memories",
                "model-id"
            );

            // Add some chat history
            const mockExchange: Exchange = {
                request_id: "test-request-id",
                request_message: "test message",
                request_nodes: [],
                response_text: "test response",
                response_nodes: [],
            };
            originalState.addExchangeToHistory(mockExchange, true);

            await sessionManager.saveSession(originalState);

            const restoredState = await sessionManager.restoreAgentState(
                originalState.conversationId
            );

            expect(restoredState.conversationId).toBe(originalState.conversationId);
            expect(restoredState.userGuidelines).toBe("user guidelines");
            expect(restoredState.workspaceGuidelines).toBe("workspace guidelines");
            expect(restoredState.agentMemories).toBe("memories");
            expect(restoredState.modelId).toBe("model-id");
            expect(restoredState.chatHistory).toHaveLength(1);
            expect(restoredState.chatHistory[0].exchange.request_message).toBe("test message");
        });

        it("should throw error for non-existent session", async () => {
            await expect(sessionManager.restoreAgentState("non-existent-id")).rejects.toThrow(
                "Failed to load session"
            );
        });
    });

    describe("deleteSession", () => {
        it("should delete an existing session", async () => {
            const agentState = new AgentState();
            await sessionManager.saveSession(agentState);

            const sessionId = agentState.conversationId;
            expect(sessionManager.sessionExists(sessionId)).toBe(true);

            await sessionManager.deleteSession(sessionId);

            expect(sessionManager.sessionExists(sessionId)).toBe(false);
        });

        it("should not throw error when deleting non-existent session", async () => {
            await expect(sessionManager.deleteSession("non-existent-id")).resolves.not.toThrow();
        });
    });
});
