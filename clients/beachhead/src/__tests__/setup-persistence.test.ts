import * as fs from "fs";
import * as path from "path";
import { v4 as uuidv4 } from "uuid";

import { SetupPersistence } from "../setup-persistence";

describe("SetupPersistence", () => {
    let tempDir: string;
    let setupPersistence: SetupPersistence;

    beforeEach(() => {
        // Create a unique temporary directory for each test
        tempDir = path.join(__dirname, `test_setup_${uuidv4()}`);
        fs.mkdirSync(tempDir, { recursive: true });
        setupPersistence = new SetupPersistence(tempDir);
    });

    afterEach(() => {
        // Clean up the temporary directory
        if (fs.existsSync(tempDir)) {
            fs.rmSync(tempDir, { recursive: true, force: true });
        }
    });

    describe("isSetupScriptCompleted", () => {
        it("should return false when no marker file exists", () => {
            const result = setupPersistence.isSetupScriptCompleted();
            expect(result).toBe(false);
        });

        it("should return true when marker file exists", async () => {
            // Create the marker file
            await setupPersistence.markSetupScriptCompleted();

            const result = setupPersistence.isSetupScriptCompleted();
            expect(result).toBe(true);
        });

        it("should handle errors gracefully and return false", () => {
            // Create a SetupPersistence with an invalid path to trigger an error
            const invalidSetupPersistence = new SetupPersistence(
                "/invalid/path/that/does/not/exist"
            );

            const result = invalidSetupPersistence.isSetupScriptCompleted();
            expect(result).toBe(false);
        });
    });

    describe("markSetupScriptCompleted", () => {
        it("should create the marker file", async () => {
            await setupPersistence.markSetupScriptCompleted();

            const markerPath = path.join(tempDir, ".setup_script_completed");
            expect(fs.existsSync(markerPath)).toBe(true);
        });

        it("should create an empty marker file", async () => {
            await setupPersistence.markSetupScriptCompleted();

            const markerPath = path.join(tempDir, ".setup_script_completed");
            const content = fs.readFileSync(markerPath, "utf8");
            expect(content).toBe("");
        });

        it("should not fail if marker file already exists", async () => {
            // Create marker file twice
            await setupPersistence.markSetupScriptCompleted();
            await setupPersistence.markSetupScriptCompleted();

            const markerPath = path.join(tempDir, ".setup_script_completed");
            expect(fs.existsSync(markerPath)).toBe(true);
        });

        it("should throw error when unable to create marker file", async () => {
            // Create a SetupPersistence with an invalid path
            const invalidSetupPersistence = new SetupPersistence(
                "/invalid/path/that/does/not/exist"
            );

            await expect(invalidSetupPersistence.markSetupScriptCompleted()).rejects.toThrow();
        });
    });

    describe("integration", () => {
        it("should work through a complete lifecycle", async () => {
            // Initially, setup should not be completed
            expect(setupPersistence.isSetupScriptCompleted()).toBe(false);

            // Mark as completed
            await setupPersistence.markSetupScriptCompleted();
            expect(setupPersistence.isSetupScriptCompleted()).toBe(true);
        });

        it("should persist across different instances", async () => {
            // Mark as completed with first instance
            await setupPersistence.markSetupScriptCompleted();

            // Create a new instance pointing to the same directory
            const newSetupPersistence = new SetupPersistence(tempDir);
            expect(newSetupPersistence.isSetupScriptCompleted()).toBe(true);
        });
    });
});
