import * as fs from "fs";
import * as os from "os";
import * as path from "path";

import { readGitHubApiToken } from "../github-token";

// Mock the logger to avoid console output during tests
jest.mock("../logging", () => ({
    getLogger: () => ({
        error: jest.fn(),
        info: jest.fn(),
        warn: jest.fn(),
        debug: jest.fn(),
    }),
}));

describe("readGitHubApiToken", () => {
    let tempDir: string;
    let tempTokenFile: string;
    let originalEnvToken: string | undefined;

    beforeEach(() => {
        // Save original environment variable
        originalEnvToken = process.env.GITHUB_API_TOKEN;

        // Create temp directory and file path
        tempDir = fs.mkdtempSync(path.join(os.tmpdir(), "github-token-test-"));
        tempTokenFile = path.join(tempDir, "github-token.txt");
    });

    afterEach(() => {
        // Restore original environment variable
        if (originalEnvToken !== undefined) {
            process.env.GITHUB_API_TOKEN = originalEnvToken;
        } else {
            delete process.env.GITHUB_API_TOKEN;
        }

        // Clean up temp files
        if (fs.existsSync(tempTokenFile)) {
            fs.unlinkSync(tempTokenFile);
        }
        if (fs.existsSync(tempDir)) {
            fs.rmdirSync(tempDir);
        }
    });

    describe("command line flag priority", () => {
        it("should return file token when both file and environment variable are set", async () => {
            const envToken = "env-token-123";
            const fileToken = "file-token-456";

            // Set environment variable
            process.env.GITHUB_API_TOKEN = envToken;

            // Create file with different token
            fs.writeFileSync(tempTokenFile, fileToken);

            // File (command line flag) should take priority
            const result = await readGitHubApiToken(tempTokenFile);
            expect(result).toBe(fileToken);
        });

        it("should return environment variable when set, even without file path", async () => {
            const envToken = "env-token-only";
            process.env.GITHUB_API_TOKEN = envToken;

            const result = await readGitHubApiToken();
            expect(result).toBe(envToken);
        });
    });

    describe("file reading", () => {
        beforeEach(() => {
            // Ensure no environment variable interferes
            delete process.env.GITHUB_API_TOKEN;
        });

        it("should read token from file when environment variable not set", async () => {
            const fileToken = "file-token-789";
            fs.writeFileSync(tempTokenFile, fileToken);

            const result = await readGitHubApiToken(tempTokenFile);
            expect(result).toBe(fileToken);
        });

        it("should trim whitespace from file content", async () => {
            const fileTokenWithWhitespace = "  \n  file-token-with-spaces  \t\n  ";
            const expectedToken = "file-token-with-spaces";
            fs.writeFileSync(tempTokenFile, fileTokenWithWhitespace);

            const result = await readGitHubApiToken(tempTokenFile);
            expect(result).toBe(expectedToken);
        });

        it("should return empty string when file does not exist", async () => {
            const nonExistentFile = path.join(tempDir, "non-existent.txt");

            const result = await readGitHubApiToken(nonExistentFile);
            expect(result).toBe("");
        });

        it("should return empty string when file path not provided", async () => {
            const result = await readGitHubApiToken();
            expect(result).toBe("");
        });

        it("should handle file read errors gracefully", async () => {
            // Create a file and then make it unreadable
            fs.writeFileSync(tempTokenFile, "test-token");
            fs.chmodSync(tempTokenFile, 0o000); // Remove all permissions

            const result = await readGitHubApiToken(tempTokenFile);
            expect(result).toBe("");

            // Restore permissions for cleanup
            fs.chmodSync(tempTokenFile, 0o644);
        });
    });

    describe("fallback behavior", () => {
        beforeEach(() => {
            delete process.env.GITHUB_API_TOKEN;
        });

        it("should return empty string when no environment variable and no file", async () => {
            const result = await readGitHubApiToken();
            expect(result).toBe("");
        });

        it("should return empty string when no environment variable and file is empty", async () => {
            fs.writeFileSync(tempTokenFile, "");

            const result = await readGitHubApiToken(tempTokenFile);
            expect(result).toBe("");
        });

        it("should return empty string when no environment variable and file contains only whitespace", async () => {
            fs.writeFileSync(tempTokenFile, "   \n\t   ");

            const result = await readGitHubApiToken(tempTokenFile);
            expect(result).toBe("");
        });
    });
});
