import { spawn } from "child_process";
import * as fs from "fs";
import * as os from "os";
import * as path from "path";

/**
 * Check if authentication is available either via environment variables or OAuth session
 */
function isAuthenticationAvailable(): boolean {
    // Check for environment variables
    if (process.env.AUGMENT_API_URL && process.env.AUGMENT_API_TOKEN) {
        return true;
    }

    // Check for OAuth session file
    const sessionPath = path.join(os.homedir(), ".augment", "session.json");
    if (fs.existsSync(sessionPath)) {
        try {
            const sessionData = JSON.parse(fs.readFileSync(sessionPath, "utf-8"));
            // Basic validation that session has required fields
            if (sessionData.accessToken && sessionData.tenantURL) {
                return true;
            }
        } catch (error) {
            // Invalid session file, continue to skip
        }
    }

    return false;
}

/**
 * Copy the session file from the default location to the test cache directory
 * so that the CLI can find it when using a custom --augment-cache-dir
 */
function setupAuthenticationForTest(cacheDir: string): void {
    const defaultSessionPath = path.join(os.homedir(), ".augment", "session.json");
    const testSessionPath = path.join(cacheDir, "session.json");

    if (fs.existsSync(defaultSessionPath)) {
        // Ensure the cache directory exists
        if (!fs.existsSync(cacheDir)) {
            fs.mkdirSync(cacheDir, { recursive: true });
        }
        // Copy the session file
        fs.copyFileSync(defaultSessionPath, testSessionPath);
    }
}

describe("End-to-End Tests", () => {
    let tempDir: string;

    beforeEach(() => {
        // Create a temporary directory for each test
        tempDir = fs.mkdtempSync(path.join(os.tmpdir(), "beachhead-e2e-"));
    });

    afterEach(() => {
        // Clean up the temporary directory
        if (tempDir && fs.existsSync(tempDir)) {
            fs.rmSync(tempDir, { recursive: true, force: true });
        }
    });

    it("should write a poem to poem.txt", async () => {
        // Skip test if authentication is not available
        if (!isAuthenticationAvailable()) {
            // eslint-disable-next-line no-console
            console.log(
                "Skipping E2E test: Authentication not available (need AUGMENT_API_URL + AUGMENT_API_TOKEN or ~/.augment/session.json)"
            );
            return;
        }

        const instruction = "Write a short poem about coding and save it to a file called poem.txt";
        const expectedFilePath = path.join(tempDir, "poem.txt");

        // Run the CLI binary
        const result = await runCliBinary([
            "--workspace-root",
            tempDir,
            "--print",
            instruction,
        ]);

        // Check that the command succeeded
        expect(result.exitCode).toBe(0);

        // Check that poem.txt was created
        expect(fs.existsSync(expectedFilePath)).toBe(true);

        // Check that the file has content
        const poemContent = fs.readFileSync(expectedFilePath, "utf-8");
        expect(poemContent.trim()).not.toBe("");
        expect(poemContent.length).toBeGreaterThan(10); // Should be more than just a few characters

        // Basic checks that it looks like a poem (has multiple lines)
        const lines = poemContent.trim().split("\n");
        expect(lines.length).toBeGreaterThan(1);
    }, 60000); // 60 second timeout for E2E test

    it("should accept --no-bell option without errors", async () => {
        // Skip test if authentication is not available
        if (!isAuthenticationAvailable()) {
            // eslint-disable-next-line no-console
            console.log(
                "Skipping E2E test: Authentication not available (need AUGMENT_API_URL + AUGMENT_API_TOKEN or ~/.augment/session.json)"
            );
            return;
        }

        const instruction = "Create a file called test.txt with the content 'test'";

        // Run the CLI binary with --no-bell option
        const result = await runCliBinary([
            "--workspace-root",
            tempDir,
            "--no-bell",
            "--print",
            instruction,
        ]);

        // Check that the command succeeded (no parsing errors)
        expect(result.exitCode).toBe(0);

        // Check that the instruction was executed
        const testFilePath = path.join(tempDir, "test.txt");
        expect(fs.existsSync(testFilePath)).toBe(true);
    }, 60000); // 60 second timeout for E2E test

    it("should create multiple files when instructed", async () => {
        // Skip test if authentication is not available
        if (!isAuthenticationAvailable()) {
            // eslint-disable-next-line no-console
            console.log(
                "Skipping E2E test: Authentication not available (need AUGMENT_API_URL + AUGMENT_API_TOKEN or ~/.augment/session.json)"
            );
            return;
        }

        const instruction =
            "Create three files: hello.txt with 'Hello', world.txt with 'World', and readme.md with a simple README";

        // Run the CLI binary
        const result = await runCliBinary([
            "--workspace-root",
            tempDir,
            "--print",
            instruction,
        ]);

        // Check that the command succeeded
        expect(result.exitCode).toBe(0);

        // Check that all three files were created
        expect(fs.existsSync(path.join(tempDir, "hello.txt"))).toBe(true);
        expect(fs.existsSync(path.join(tempDir, "world.txt"))).toBe(true);
        expect(fs.existsSync(path.join(tempDir, "readme.md"))).toBe(true);

        // Check that the files have expected content
        const helloContent = fs.readFileSync(path.join(tempDir, "hello.txt"), "utf-8");
        const worldContent = fs.readFileSync(path.join(tempDir, "world.txt"), "utf-8");
        const readmeContent = fs.readFileSync(path.join(tempDir, "readme.md"), "utf-8");

        expect(helloContent.trim()).toContain("Hello");
        expect(worldContent.trim()).toContain("World");
        expect(readmeContent.trim()).not.toBe("");
        expect(readmeContent.length).toBeGreaterThan(10); // Should be more than just a few characters
    }, 60000); // 60 second timeout for E2E test

    it("should use rules files to modify agent behavior", async () => {
        // Skip test if authentication is not available
        if (!isAuthenticationAvailable()) {
            // eslint-disable-next-line no-console
            console.log(
                "Skipping E2E test: Authentication not available (need AUGMENT_API_URL + AUGMENT_API_TOKEN or ~/.augment/session.json)"
            );
            return;
        }

        // Create a rules file with specific instruction
        const rulesFilePath = path.join(tempDir, "test-rules.md");
        const rulesContent = `# Test Rules

## Response Format Rule
End every answer with reciting "the quick brown fox jumps over the lazy dog".`;

        fs.writeFileSync(rulesFilePath, rulesContent, "utf-8");

        const instruction = "hello";

        // Run the CLI binary with the rules file
        const result = await runCliBinary([
            "--workspace-root",
            tempDir,
            "--print",
            "--rules",
            rulesFilePath,
            instruction,
        ]);

        // Check that the command succeeded
        expect(result.exitCode).toBe(0);

        // Check that the output contains the required phrase from the rules
        const output = result.stdout.toLowerCase();
        expect(output).toContain("the quick brown fox");
        expect(output).toContain("jumps over the lazy dog");

        // Also check that it responded to the greeting
        expect(output).toMatch(/hello|hi|greet/i);
    }, 60000); // 60 second timeout for E2E test

    it("should abort when rules file does not exist", async () => {
        // Skip test if authentication is not available
        if (!isAuthenticationAvailable()) {
            // eslint-disable-next-line no-console
            console.log(
                "Skipping E2E test: Authentication not available (need AUGMENT_API_URL + AUGMENT_API_TOKEN or ~/.augment/session.json)"
            );
            return;
        }

        const nonExistentRulesFile = path.join(tempDir, "non-existent-rules.md");
        const instruction = "hello";

        // Run the CLI binary with a non-existent rules file
        const result = await runCliBinary([
            "--workspace-root",
            tempDir,
            "--print",
            "--rules",
            nonExistentRulesFile,
            instruction,
        ]);

        // Check that the command failed
        expect(result.exitCode).not.toBe(0);

        // Check that the error message mentions the missing rules file
        const errorOutput = result.stderr.toLowerCase();
        expect(errorOutput).toContain("rules file not found");
        expect(errorOutput).toContain("non-existent-rules.md");
    }, 60000); // 60 second timeout for E2E test

    it("should save and resume sessions with --continue", async () => {
        // Skip test if authentication is not available
        if (!isAuthenticationAvailable()) {
            // eslint-disable-next-line no-console
            console.log(
                "Skipping E2E test: Authentication not available (need AUGMENT_API_URL + AUGMENT_API_TOKEN or ~/.augment/session.json)"
            );
            return;
        }

        const cacheDir = path.join(tempDir, ".augment");
        const sessionsDir = path.join(cacheDir, "sessions");

        // Set up authentication for the test
        setupAuthenticationForTest(cacheDir);

        // First session: Create a file
        const instruction1 =
            "Create a file called session-test.txt with the content 'First session'";
        const result1 = await runCliBinary([
            "--workspace-root",
            tempDir,
            "--augment-cache-dir",
            cacheDir,
            "--print",
            instruction1,
        ]);

        expect(result1.exitCode).toBe(0);
        expect(fs.existsSync(path.join(tempDir, "session-test.txt"))).toBe(true);

        // Verify session was saved
        expect(fs.existsSync(sessionsDir)).toBe(true);
        const sessionFiles = fs.readdirSync(sessionsDir).filter((f) => f.endsWith(".json"));
        expect(sessionFiles.length).toBe(1);

        // Read the session file to verify it contains the exchange
        const sessionPath = path.join(sessionsDir, sessionFiles[0]);
        const sessionData = JSON.parse(fs.readFileSync(sessionPath, "utf-8"));
        expect(sessionData.sessionId).toBeDefined();
        expect(sessionData.created).toBeDefined();
        expect(sessionData.modified).toBeDefined();
        expect(sessionData.chatHistory.length).toBeGreaterThan(0);

        // Second session: Resume and create another file
        const instruction2 =
            "Create another file called session-test-2.txt with the content 'Second session'";
        const result2 = await runCliBinary([
            "--workspace-root",
            tempDir,
            "--augment-cache-dir",
            cacheDir,
            "--continue",
            "--print",
            instruction2,
        ]);

        expect(result2.exitCode).toBe(0);
        expect(fs.existsSync(path.join(tempDir, "session-test-2.txt"))).toBe(true);

        // Verify the output indicates session was resumed
        expect(result2.stdout).toContain("Resumed session from");

        // Verify the session file was updated (should still be only one file but with more exchanges)
        const updatedSessionFiles = fs.readdirSync(sessionsDir).filter((f) => f.endsWith(".json"));
        expect(updatedSessionFiles.length).toBe(1);

        const updatedSessionData = JSON.parse(fs.readFileSync(sessionPath, "utf-8"));
        expect(updatedSessionData.chatHistory.length).toBeGreaterThan(
            sessionData.chatHistory.length
        );
        expect(updatedSessionData.modified).not.toBe(sessionData.modified); // Should be updated
    }, 90000); // 90 second timeout for longer E2E test

    it("should handle --continue when no previous session exists", async () => {
        // Skip test if authentication is not available
        if (!isAuthenticationAvailable()) {
            // eslint-disable-next-line no-console
            console.log(
                "Skipping E2E test: Authentication not available (need AUGMENT_API_URL + AUGMENT_API_TOKEN or ~/.augment/session.json)"
            );
            return;
        }

        const cacheDir = path.join(tempDir, ".augment");

        // Set up authentication for the test
        setupAuthenticationForTest(cacheDir);

        const instruction =
            "Create a file called no-previous-session.txt with the content 'No previous session'";

        const result = await runCliBinary([
            "--workspace-root",
            tempDir,
            "--augment-cache-dir",
            cacheDir,
            "--continue",
            "--print",
            instruction,
        ]);

        expect(result.exitCode).toBe(0);
        expect(fs.existsSync(path.join(tempDir, "no-previous-session.txt"))).toBe(true);

        // Verify the output indicates no previous session was found
        expect(result.stdout).toContain("No previous session found");

        // Verify a new session was created
        const sessionsDir = path.join(cacheDir, "sessions");
        expect(fs.existsSync(sessionsDir)).toBe(true);
        const sessionFiles = fs.readdirSync(sessionsDir).filter((f) => f.endsWith(".json"));
        expect(sessionFiles.length).toBe(1);
    }, 60000); // 60 second timeout for E2E test

    it("should allow --continue with instruction arguments", async () => {
        // Skip test if authentication is not available
        if (!isAuthenticationAvailable()) {
            // eslint-disable-next-line no-console
            console.log(
                "Skipping E2E test: Authentication not available (need AUGMENT_API_URL + AUGMENT_API_TOKEN or ~/.augment/session.json)"
            );
            return;
        }

        const cacheDir = path.join(tempDir, ".augment");

        // Set up authentication for the test
        setupAuthenticationForTest(cacheDir);

        // First create a session
        const result1 = await runCliBinary([
            "--workspace-root",
            tempDir,
            "--augment-cache-dir",
            cacheDir,
            "--print",
            "Create a file called first-session.txt with content 'First session'",
        ]);

        expect(result1.exitCode).toBe(0);
        expect(fs.existsSync(path.join(tempDir, "first-session.txt"))).toBe(true);

        // Now resume with a new instruction
        const result2 = await runCliBinary([
            "--workspace-root",
            tempDir,
            "--augment-cache-dir",
            cacheDir,
            "--continue",
            "--print",
            "Create another file called resumed-with-instruction.txt with content 'Resumed with new instruction'",
        ]);

        expect(result2.exitCode).toBe(0);
        expect(fs.existsSync(path.join(tempDir, "resumed-with-instruction.txt"))).toBe(true);

        // Verify the output indicates session was resumed
        expect(result2.stdout).toContain("Resumed session from");
    }, 90000); // 90 second timeout for E2E test

    it("should preserve chat history across sessions", async () => {
        // Skip test if authentication is not available
        if (!isAuthenticationAvailable()) {
            // eslint-disable-next-line no-console
            console.log(
                "Skipping E2E test: Authentication not available (need AUGMENT_API_URL + AUGMENT_API_TOKEN or ~/.augment/session.json)"
            );
            return;
        }

        const cacheDir = path.join(tempDir, ".augment");

        // Set up authentication for the test
        setupAuthenticationForTest(cacheDir);

        // First session: Ask about the capital of France
        const result1 = await runCliBinary([
            "--workspace-root",
            tempDir,
            "--augment-cache-dir",
            cacheDir,
            "--print",
            "What is the capital of France? Please answer in a single word.",
        ]);

        expect(result1.exitCode).toBe(0);
        expect(result1.stdout.toLowerCase()).toContain("paris");

        // Second session: Resume and ask what question was asked previously
        const result2 = await runCliBinary([
            "--workspace-root",
            tempDir,
            "--augment-cache-dir",
            cacheDir,
            "--continue",
            "--print",
            "What question did I just ask you in our previous conversation?",
        ]);

        expect(result2.exitCode).toBe(0);

        // Verify the output indicates session was resumed
        expect(result2.stdout).toContain("Resumed session from");

        // Verify the agent remembers the previous question about France
        expect(result2.stdout.toLowerCase()).toContain("france");
    }, 120000); // 120 second timeout for longer E2E test
});

/**
 * Helper function to run the CLI binary with given arguments and return the result.
 */
function runCliBinary(args: string[]): Promise<{
    exitCode: number;
    stdout: string;
    stderr: string;
}> {
    return new Promise((resolve, reject) => {
        // Use the built CLI binary - in the test environment, it's available in the runfiles
        // Check if we're in a Bazel test environment
        let binaryPath: string;
        if (process.env.RUNFILES_DIR) {
            // Bazel test environment
            binaryPath = path.join(
                process.env.RUNFILES_DIR,
                "_main/clients/beachhead/out/augment.mjs"
            );
        } else {
            // Local development environment
            binaryPath = path.join(process.cwd(), "out/augment.mjs");
        }

        const child = spawn("node", [binaryPath, ...args], {
            env: {
                ...process.env,
                // Ensure we have the required environment variables
                AUGMENT_API_URL: process.env.AUGMENT_API_URL,
                AUGMENT_API_TOKEN: process.env.AUGMENT_API_TOKEN,
            },
            stdio: ["pipe", "pipe", "pipe"],
        });

        // Close stdin immediately to prevent hanging
        child.stdin?.end();

        let stdout = "";
        let stderr = "";

        child.stdout?.on("data", (data) => {
            stdout += data.toString();
        });

        child.stderr?.on("data", (data) => {
            stderr += data.toString();
        });

        child.on("close", (code) => {
            clearTimeout(timeoutId);
            resolve({
                exitCode: code || 0,
                stdout,
                stderr,
            });
        });

        child.on("error", (error) => {
            clearTimeout(timeoutId);
            reject(error);
        });

        // Set a timeout to prevent hanging
        const timeoutId = setTimeout(() => {
            child.kill("SIGKILL"); // Use SIGKILL for more forceful termination
            reject(new Error("Test timed out"));
        }, 55000); // 55 seconds, slightly less than Jest timeout
    });
}
