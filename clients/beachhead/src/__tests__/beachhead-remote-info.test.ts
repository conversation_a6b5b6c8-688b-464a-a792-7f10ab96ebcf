import {
    RemoteToolId,
    ToolAvailabilityStatus,
} from "@augment-internal/sidecar-libs/src/tools/tool-types";

import { APIServer } from "../augment-api";
import { BeachheadRemoteInfo } from "../beachhead-remote-info";

// Mock the APIServer
jest.mock("../augment-api");
jest.mock("../logging", () => ({
    getLogger: () => ({
        error: jest.fn(),
        info: jest.fn(),
        warn: jest.fn(),
        debug: jest.fn(),
    }),
}));

describe("BeachheadRemoteInfo", () => {
    let mockApiServer: jest.Mocked<APIServer>;
    let beachheadRemoteInfo: BeachheadRemoteInfo;

    beforeEach(() => {
        mockApiServer = {
            runRemoteTool: jest.fn(),
            listRemoteTools: jest.fn(),
        } as any;
    });

    describe("GitHub token handling", () => {
        it("should pass GitHub token when provided and tool is GitHub API", async () => {
            const githubToken = "test-github-token-123";
            beachheadRemoteInfo = new BeachheadRemoteInfo(mockApiServer, false, githubToken);

            mockApiServer.runRemoteTool.mockResolvedValue({
                toolOutput: "test output",
                toolResultMessage: "success",
                status: "success" as any,
            });

            await beachheadRemoteInfo.runRemoteTool(
                "test-request-id",
                "github-api",
                "{}",
                RemoteToolId.GitHubApi,
                new AbortController().signal
            );

            expect(mockApiServer.runRemoteTool).toHaveBeenCalledWith(
                "test-request-id",
                "github-api",
                "{}",
                RemoteToolId.GitHubApi,
                { apiToken: githubToken },
                expect.any(AbortSignal)
            );
        });

        it("should not pass GitHub token when not provided", async () => {
            beachheadRemoteInfo = new BeachheadRemoteInfo(mockApiServer, false);

            mockApiServer.runRemoteTool.mockResolvedValue({
                toolOutput: "test output",
                toolResultMessage: "success",
                status: "success" as any,
            });

            await beachheadRemoteInfo.runRemoteTool(
                "test-request-id",
                "github-api",
                "{}",
                RemoteToolId.GitHubApi,
                new AbortController().signal
            );

            expect(mockApiServer.runRemoteTool).toHaveBeenCalledWith(
                "test-request-id",
                "github-api",
                "{}",
                RemoteToolId.GitHubApi,
                undefined,
                expect.any(AbortSignal)
            );
        });

        it("should not pass GitHub token for non-GitHub tools", async () => {
            const githubToken = "test-github-token-123";
            beachheadRemoteInfo = new BeachheadRemoteInfo(mockApiServer, false, githubToken);

            mockApiServer.runRemoteTool.mockResolvedValue({
                toolOutput: "test output",
                toolResultMessage: "success",
                status: "success" as any,
            });

            await beachheadRemoteInfo.runRemoteTool(
                "test-request-id",
                "linear",
                "{}",
                RemoteToolId.Linear,
                new AbortController().signal
            );

            expect(mockApiServer.runRemoteTool).toHaveBeenCalledWith(
                "test-request-id",
                "linear",
                "{}",
                RemoteToolId.Linear,
                undefined,
                expect.any(AbortSignal)
            );
        });

        it("should not pass GitHub token when token is empty string", async () => {
            beachheadRemoteInfo = new BeachheadRemoteInfo(mockApiServer, false, "");

            mockApiServer.runRemoteTool.mockResolvedValue({
                toolOutput: "test output",
                toolResultMessage: "success",
                status: "success" as any,
            });

            await beachheadRemoteInfo.runRemoteTool(
                "test-request-id",
                "github-api",
                "{}",
                RemoteToolId.GitHubApi,
                new AbortController().signal
            );

            expect(mockApiServer.runRemoteTool).toHaveBeenCalledWith(
                "test-request-id",
                "github-api",
                "{}",
                RemoteToolId.GitHubApi,
                undefined,
                expect.any(AbortSignal)
            );
        });

        it("should handle whitespace-only GitHub token as empty", async () => {
            beachheadRemoteInfo = new BeachheadRemoteInfo(mockApiServer, false, "   \n\t   ");

            mockApiServer.runRemoteTool.mockResolvedValue({
                toolOutput: "test output",
                toolResultMessage: "success",
                status: "success" as any,
            });

            await beachheadRemoteInfo.runRemoteTool(
                "test-request-id",
                "github-api",
                "{}",
                RemoteToolId.GitHubApi,
                new AbortController().signal
            );

            // Should still pass the whitespace token since BeachheadRemoteInfo doesn't trim
            expect(mockApiServer.runRemoteTool).toHaveBeenCalledWith(
                "test-request-id",
                "github-api",
                "{}",
                RemoteToolId.GitHubApi,
                { apiToken: "   \n\t   " },
                expect.any(AbortSignal)
            );
        });
    });

    describe("retrieveRemoteTools", () => {
        it("should include GitHub API tool when token is provided and tool is UserConfigRequired", async () => {
            const githubToken = "test-github-token-123";
            beachheadRemoteInfo = new BeachheadRemoteInfo(mockApiServer, false, githubToken);

            mockApiServer.listRemoteTools.mockResolvedValue({
                tools: [
                    {
                        toolDefinition: {
                            name: "github-api",
                            description: "GitHub API tool",
                            input_schema_json: "{}",
                            tool_safety: 1,
                        },
                        remoteToolId: RemoteToolId.GitHubApi,
                        availabilityStatus: ToolAvailabilityStatus.UserConfigRequired,
                        toolSafety: 1,
                        oauthUrl: "",
                    },
                    {
                        toolDefinition: {
                            name: "web-search",
                            description: "Web search tool",
                            input_schema_json: "{}",
                            tool_safety: 1,
                        },
                        remoteToolId: RemoteToolId.WebSearch,
                        availabilityStatus: ToolAvailabilityStatus.Available,
                        toolSafety: 1,
                        oauthUrl: "",
                    },
                ],
            });

            const result = await beachheadRemoteInfo.retrieveRemoteTools([
                RemoteToolId.GitHubApi,
                RemoteToolId.WebSearch,
            ]);

            expect(result).toHaveLength(2);
            expect(result.map((t) => t.remoteToolId)).toContain(RemoteToolId.GitHubApi);
            expect(result.map((t) => t.remoteToolId)).toContain(RemoteToolId.WebSearch);
        });

        it("should exclude GitHub API tool when token is not provided and tool is UserConfigRequired", async () => {
            beachheadRemoteInfo = new BeachheadRemoteInfo(mockApiServer, false);

            mockApiServer.listRemoteTools.mockResolvedValue({
                tools: [
                    {
                        toolDefinition: {
                            name: "github-api",
                            description: "GitHub API tool",
                            input_schema_json: "{}",
                            tool_safety: 1,
                        },
                        remoteToolId: RemoteToolId.GitHubApi,
                        availabilityStatus: ToolAvailabilityStatus.UserConfigRequired,
                        toolSafety: 1,
                        oauthUrl: "",
                    },
                    {
                        toolDefinition: {
                            name: "web-search",
                            description: "Web search tool",
                            input_schema_json: "{}",
                            tool_safety: 1,
                        },
                        remoteToolId: RemoteToolId.WebSearch,
                        availabilityStatus: ToolAvailabilityStatus.Available,
                        toolSafety: 1,
                        oauthUrl: "",
                    },
                ],
            });

            const result = await beachheadRemoteInfo.retrieveRemoteTools([
                RemoteToolId.GitHubApi,
                RemoteToolId.WebSearch,
            ]);

            expect(result).toHaveLength(1);
            expect(result.map((t) => t.remoteToolId)).toContain(RemoteToolId.WebSearch);
            expect(result.map((t) => t.remoteToolId)).not.toContain(RemoteToolId.GitHubApi);
        });

        it("should include GitHub API tool when token is provided and tool is Available", async () => {
            const githubToken = "test-github-token-123";
            beachheadRemoteInfo = new BeachheadRemoteInfo(mockApiServer, false, githubToken);

            mockApiServer.listRemoteTools.mockResolvedValue({
                tools: [
                    {
                        toolDefinition: {
                            name: "github-api",
                            description: "GitHub API tool",
                            input_schema_json: "{}",
                            tool_safety: 1,
                        },
                        remoteToolId: RemoteToolId.GitHubApi,
                        availabilityStatus: ToolAvailabilityStatus.Available,
                        toolSafety: 1,
                        oauthUrl: "",
                    },
                ],
            });

            const result = await beachheadRemoteInfo.retrieveRemoteTools([RemoteToolId.GitHubApi]);

            expect(result).toHaveLength(1);
            expect(result[0].remoteToolId).toBe(RemoteToolId.GitHubApi);
        });

        it("should return empty array in setup mode", async () => {
            const githubToken = "test-github-token-123";
            beachheadRemoteInfo = new BeachheadRemoteInfo(mockApiServer, true, githubToken);

            const result = await beachheadRemoteInfo.retrieveRemoteTools([RemoteToolId.GitHubApi]);

            expect(result).toHaveLength(0);
            expect(mockApiServer.listRemoteTools).not.toHaveBeenCalled();
        });
    });

    describe("filterToolsWithExtraInput", () => {
        it("should return GitHub API tool when GitHub token is provided", async () => {
            const githubToken = "test-github-token-123";
            beachheadRemoteInfo = new BeachheadRemoteInfo(mockApiServer, false, githubToken);

            const result = await beachheadRemoteInfo.filterToolsWithExtraInput([
                RemoteToolId.GitHubApi,
                RemoteToolId.Linear,
                RemoteToolId.Notion,
            ]);

            expect(result).toEqual(new Set([RemoteToolId.GitHubApi]));
        });

        it("should not return GitHub API tool when GitHub token is not provided", async () => {
            beachheadRemoteInfo = new BeachheadRemoteInfo(mockApiServer, false);

            const result = await beachheadRemoteInfo.filterToolsWithExtraInput([
                RemoteToolId.GitHubApi,
                RemoteToolId.Linear,
                RemoteToolId.Notion,
            ]);

            expect(result).toEqual(new Set());
        });

        it("should not return GitHub API tool when GitHub token is empty string", async () => {
            beachheadRemoteInfo = new BeachheadRemoteInfo(mockApiServer, false, "");

            const result = await beachheadRemoteInfo.filterToolsWithExtraInput([
                RemoteToolId.GitHubApi,
                RemoteToolId.Linear,
                RemoteToolId.Notion,
            ]);

            expect(result).toEqual(new Set());
        });

        it("should return GitHub API tool when GitHub token is whitespace (not trimmed)", async () => {
            beachheadRemoteInfo = new BeachheadRemoteInfo(mockApiServer, false, "   \n\t   ");

            const result = await beachheadRemoteInfo.filterToolsWithExtraInput([
                RemoteToolId.GitHubApi,
                RemoteToolId.Linear,
                RemoteToolId.Notion,
            ]);

            // Should return GitHub API tool since BeachheadRemoteInfo doesn't trim the token
            expect(result).toEqual(new Set([RemoteToolId.GitHubApi]));
        });

        it("should return empty set when no tools are requested", async () => {
            const githubToken = "test-github-token-123";
            beachheadRemoteInfo = new BeachheadRemoteInfo(mockApiServer, false, githubToken);

            const result = await beachheadRemoteInfo.filterToolsWithExtraInput([]);

            expect(result).toEqual(new Set());
        });

        it("should only return GitHub API tool and ignore other tools", async () => {
            const githubToken = "test-github-token-123";
            beachheadRemoteInfo = new BeachheadRemoteInfo(mockApiServer, false, githubToken);

            const result = await beachheadRemoteInfo.filterToolsWithExtraInput([
                RemoteToolId.GitHubApi,
                RemoteToolId.Linear,
                RemoteToolId.Notion,
                RemoteToolId.Jira,
                RemoteToolId.Confluence,
                RemoteToolId.WebSearch,
            ]);

            // Only GitHub API should be returned, other tools are not supported in beachhead
            expect(result).toEqual(new Set([RemoteToolId.GitHubApi]));
        });
    });
});
