import { RemoteToolId } from "@augment-internal/sidecar-libs/src/tools/tool-types";
import * as fs from "fs";
import * as os from "os";
import * as path from "path";

import { APIServer } from "../augment-api";
import { BeachheadRemoteInfo } from "../beachhead-remote-info";
import { readGitHubApiToken } from "../github-token";

// Mock the APIServer and logging
jest.mock("../augment-api");
jest.mock("../logging", () => ({
    getLogger: () => ({
        error: jest.fn(),
        info: jest.fn(),
        warn: jest.fn(),
        debug: jest.fn(),
    }),
}));

describe("GitHub Integration End-to-End", () => {
    let tempDir: string;
    let tempTokenFile: string;
    let originalEnvToken: string | undefined;
    let mockApiServer: jest.Mocked<APIServer>;

    beforeEach(() => {
        // Save original environment variable
        originalEnvToken = process.env.GITHUB_API_TOKEN;

        // Create temp directory and file path
        tempDir = fs.mkdtempSync(path.join(os.tmpdir(), "github-integration-test-"));
        tempTokenFile = path.join(tempDir, "github-token.txt");

        // Setup mock API server
        mockApiServer = {
            runRemoteTool: jest.fn(),
            listRemoteTools: jest.fn(),
        } as any;

        mockApiServer.runRemoteTool.mockResolvedValue({
            toolOutput: "test output",
            toolResultMessage: "success",
            status: "success" as any,
        });
    });

    afterEach(() => {
        // Restore original environment variable
        if (originalEnvToken !== undefined) {
            process.env.GITHUB_API_TOKEN = originalEnvToken;
        } else {
            delete process.env.GITHUB_API_TOKEN;
        }

        // Clean up temp files
        if (fs.existsSync(tempTokenFile)) {
            fs.unlinkSync(tempTokenFile);
        }
        if (fs.existsSync(tempDir)) {
            fs.rmdirSync(tempDir);
        }
    });

    it("should read token from environment and pass to GitHub API tool", async () => {
        const envToken = "env-integration-token";
        process.env.GITHUB_API_TOKEN = envToken;

        // Read token using the function
        const token = await readGitHubApiToken();
        expect(token).toBe(envToken);

        // Use token with BeachheadRemoteInfo
        const remoteInfo = new BeachheadRemoteInfo(mockApiServer, false, token);

        await remoteInfo.runRemoteTool(
            "test-request-id",
            "github-api",
            "{}",
            RemoteToolId.GitHubApi,
            new AbortController().signal
        );

        expect(mockApiServer.runRemoteTool).toHaveBeenCalledWith(
            "test-request-id",
            "github-api",
            "{}",
            RemoteToolId.GitHubApi,
            { apiToken: envToken },
            expect.any(AbortSignal)
        );
    });

    it("should read token from file and pass to GitHub API tool", async () => {
        delete process.env.GITHUB_API_TOKEN;

        const fileToken = "file-integration-token";
        fs.writeFileSync(tempTokenFile, fileToken);

        // Read token using the function
        const token = await readGitHubApiToken(tempTokenFile);
        expect(token).toBe(fileToken);

        // Use token with BeachheadRemoteInfo
        const remoteInfo = new BeachheadRemoteInfo(mockApiServer, false, token);

        await remoteInfo.runRemoteTool(
            "test-request-id",
            "github-api",
            "{}",
            RemoteToolId.GitHubApi,
            new AbortController().signal
        );

        expect(mockApiServer.runRemoteTool).toHaveBeenCalledWith(
            "test-request-id",
            "github-api",
            "{}",
            RemoteToolId.GitHubApi,
            { apiToken: fileToken },
            expect.any(AbortSignal)
        );
    });

    it("should handle no token available and not pass extra input", async () => {
        delete process.env.GITHUB_API_TOKEN;

        // Read token using the function (no file provided)
        const token = await readGitHubApiToken();
        expect(token).toBe("");

        // Use empty token with BeachheadRemoteInfo
        const remoteInfo = new BeachheadRemoteInfo(mockApiServer, false, token);

        await remoteInfo.runRemoteTool(
            "test-request-id",
            "github-api",
            "{}",
            RemoteToolId.GitHubApi,
            new AbortController().signal
        );

        expect(mockApiServer.runRemoteTool).toHaveBeenCalledWith(
            "test-request-id",
            "github-api",
            "{}",
            RemoteToolId.GitHubApi,
            undefined,
            expect.any(AbortSignal)
        );
    });

    it("should prioritize file over environment variable in full flow", async () => {
        const envToken = "env-secondary-token";
        const fileToken = "file-priority-token";

        process.env.GITHUB_API_TOKEN = envToken;
        fs.writeFileSync(tempTokenFile, fileToken);

        // Read token using the function
        const token = await readGitHubApiToken(tempTokenFile);
        expect(token).toBe(fileToken); // File (command line flag) should win

        // Use token with BeachheadRemoteInfo
        const remoteInfo = new BeachheadRemoteInfo(mockApiServer, false, token);

        await remoteInfo.runRemoteTool(
            "test-request-id",
            "github-api",
            "{}",
            RemoteToolId.GitHubApi,
            new AbortController().signal
        );

        expect(mockApiServer.runRemoteTool).toHaveBeenCalledWith(
            "test-request-id",
            "github-api",
            "{}",
            RemoteToolId.GitHubApi,
            { apiToken: fileToken },
            expect.any(AbortSignal)
        );
    });

    it("should handle file read errors in full flow", async () => {
        delete process.env.GITHUB_API_TOKEN;

        // Create a file and make it unreadable
        fs.writeFileSync(tempTokenFile, "unreadable-token");
        fs.chmodSync(tempTokenFile, 0o000);

        // Read token using the function
        const token = await readGitHubApiToken(tempTokenFile);
        expect(token).toBe(""); // Should fallback to empty

        // Use empty token with BeachheadRemoteInfo
        const remoteInfo = new BeachheadRemoteInfo(mockApiServer, false, token);

        await remoteInfo.runRemoteTool(
            "test-request-id",
            "github-api",
            "{}",
            RemoteToolId.GitHubApi,
            new AbortController().signal
        );

        expect(mockApiServer.runRemoteTool).toHaveBeenCalledWith(
            "test-request-id",
            "github-api",
            "{}",
            RemoteToolId.GitHubApi,
            undefined,
            expect.any(AbortSignal)
        );

        // Restore permissions for cleanup
        fs.chmodSync(tempTokenFile, 0o644);
    });
});
