import { AsyncQueue } from "../../agent_loop/async-queue";

describe("AsyncQueue", () => {
    test("push and pop with items already in queue", async () => {
        const queue = new AsyncQueue<number>();
        queue.push(1);
        queue.push(2);
        queue.push(3);

        expect(queue.size).toBe(3);

        const item1 = await queue.pop();
        const item2 = await queue.pop();
        const item3 = await queue.pop();

        expect(item1).toBe(1);
        expect(item2).toBe(2);
        expect(item3).toBe(3);
        expect(queue.size).toBe(0);
    });

    test("pop waits for items when queue is empty", async () => {
        const queue = new AsyncQueue<string>();

        // Start popping from an empty queue - this should wait
        const popPromise = queue.pop();

        // Verify the promise hasn't resolved yet
        let resolved = false;
        popPromise.then(() => {
            resolved = true;
        });

        // Wait a tick to allow any synchronous resolution
        await new Promise((resolve) => setTimeout(resolve, 0));
        expect(resolved).toBe(false);

        // Now push an item
        queue.push("test item");

        // The pop should now resolve with our item
        const item = await popPromise;
        expect(item).toBe("test item");
        expect(resolved).toBe(true);
    });

    test("multiple consumers wait and receive items in order", async () => {
        const queue = new AsyncQueue<number>();
        const results: number[] = [];

        // Create three waiting consumers
        const consumer1 = queue.pop().then((item) => {
            results.push(item);
            return item;
        });

        const consumer2 = queue.pop().then((item) => {
            results.push(item);
            return item;
        });

        const consumer3 = queue.pop().then((item) => {
            results.push(item);
            return item;
        });

        // Push items one by one
        queue.push(1);
        queue.push(2);
        queue.push(3);

        // Wait for all consumers
        const [item1, item2, item3] = await Promise.all([consumer1, consumer2, consumer3]);

        // Verify each consumer got the correct item
        expect(item1).toBe(1);
        expect(item2).toBe(2);
        expect(item3).toBe(3);

        // Verify the items were processed in order
        expect(results).toEqual([1, 2, 3]);
    });

    test("clear removes all items from the queue", async () => {
        const queue = new AsyncQueue<number>();
        queue.push(1);
        queue.push(2);
        queue.push(3);

        expect(queue.size).toBe(3);

        queue.clear();

        expect(queue.size).toBe(0);

        // Push a new item and verify we can still pop it
        queue.push(4);
        const item = await queue.pop();
        expect(item).toBe(4);
    });

    test("waiters are not affected by clear", async () => {
        const queue = new AsyncQueue<string>();

        // Start a pop operation that will wait
        const popPromise = queue.pop();

        // Clear the queue (should have no effect on waiters)
        queue.clear();

        // Push an item and verify the waiter receives it
        queue.push("after clear");
        const item = await popPromise;
        expect(item).toBe("after clear");
    });

    test("push directly to waiters when queue is empty", async () => {
        const queue = new AsyncQueue<string>();

        // Start two pop operations that will wait
        const pop1 = queue.pop();
        const pop2 = queue.pop();

        // Push items
        queue.push("item1");
        queue.push("item2");

        // Verify the waiters received the items directly
        expect(await pop1).toBe("item1");
        expect(await pop2).toBe("item2");

        // Queue should still be empty since items went directly to waiters
        expect(queue.size).toBe(0);
    });
});
