/* eslint-disable no-console */
import { formatCliSection } from "../../cli/utils/interactive-formatting-utils";
import { TextEventListener } from "../../agent_loop/text-event-listener";
import { OutputConfig, OutputMode, OutputVerbosity, LogLevel } from "../../cli/options/output";

// Mock console.log and process.stdout.write to capture output
const mockConsoleLog = jest.fn();
const mockStdoutWrite = jest.fn();
const originalConsoleLog = console.log;
const originalStdoutWrite = process.stdout.write;

describe("TextEventListener", () => {
    let textEventListener: TextEventListener;

    const createQuietConfig = (): OutputConfig => ({
        mode: OutputMode.TEXT,
        verbosity: OutputVerbosity.QUIET,
        notification_settings: { bell: false },
        log_settings: { file: "", level: LogLevel.INFO }
    });

    const createDefaultConfig = (): OutputConfig => ({
        mode: OutputMode.TEXT,
        verbosity: OutputVerbosity.DEFAULT,
        notification_settings: { bell: false },
        log_settings: { file: "", level: LogLevel.INFO }
    });

    const createCompactConfig = (): OutputConfig => ({
        mode: OutputMode.TEXT,
        verbosity: OutputVerbosity.COMPACT,
        notification_settings: { bell: false },
        log_settings: { file: "", level: LogLevel.INFO }
    });

    beforeEach(() => {
        jest.clearAllMocks();
        console.log = mockConsoleLog;
        process.stdout.write = mockStdoutWrite as any;
        textEventListener = new TextEventListener(createQuietConfig());
    });

    afterEach(() => {
        console.log = originalConsoleLog;
        process.stdout.write = originalStdoutWrite;
    });

    describe("handleStrReplaceEditorInput", () => {
        it("should handle single str_replace entry", () => {
            const toolInput = {
                command: "str_replace",
                path: "test.txt",
                old_str: "old content",
                new_str: "new content",
            };

            const result = (textEventListener as any).handleStrReplaceEditorInput(toolInput);

            expect(result).toBe(true);
            expect(mockConsoleLog).toHaveBeenCalled();
            // Check that a diff was generated and displayed
            const loggedContent = mockConsoleLog.mock.calls.map((call) => call[0]).join("\n");
            expect(loggedContent).toContain("-old content");
            expect(loggedContent).toContain("+new content");
        });

        it("should handle multiple str_replace entries with indexed parameters", () => {
            const toolInput = {
                command: "str_replace",
                path: "test.txt",
                old_str_1: "old content 1",
                new_str_1: "new content 1",
                old_str_2: "old content 2",
                new_str_2: "new content 2",
            };

            const result = (textEventListener as any).handleStrReplaceEditorInput(toolInput);

            expect(result).toBe(true);
            expect(mockConsoleLog).toHaveBeenCalled();

            // Check that diffs for both entries were generated and displayed
            const loggedContent = mockConsoleLog.mock.calls.map((call) => call[0]).join("\n");
            expect(loggedContent).toContain("Entry 1:");
            expect(loggedContent).toContain("-old content 1");
            expect(loggedContent).toContain("+new content 1");
            expect(loggedContent).toContain("Entry 2:");
            expect(loggedContent).toContain("-old content 2");
            expect(loggedContent).toContain("+new content 2");
        });

        it("should handle mixed indexed and non-indexed entries", () => {
            const toolInput = {
                command: "str_replace",
                path: "test.txt",
                old_str: "old content",
                new_str: "new content",
                old_str_1: "old content 1",
                new_str_1: "new content 1",
            };

            const result = (textEventListener as any).handleStrReplaceEditorInput(toolInput);

            expect(result).toBe(true);
            expect(mockConsoleLog).toHaveBeenCalled();

            // Check that diffs for both entries were generated and displayed
            const loggedContent = mockConsoleLog.mock.calls.map((call) => call[0]).join("\n");
            expect(loggedContent).toContain("Entry 0:");
            expect(loggedContent).toContain("-old content");
            expect(loggedContent).toContain("+new content");
            expect(loggedContent).toContain("Entry 1:");
            expect(loggedContent).toContain("-old content 1");
            expect(loggedContent).toContain("+new content 1");
        });

        it("should return false for non-str_replace commands", () => {
            const toolInput = {
                command: "insert",
                path: "test.txt",
                insert_line: 5,
                new_str: "inserted content",
            };

            const result = (textEventListener as any).handleStrReplaceEditorInput(toolInput);

            expect(result).toBe(false);
            expect(mockConsoleLog).not.toHaveBeenCalled();
        });

        it("should return false when path is missing", () => {
            const toolInput = {
                command: "str_replace",
                old_str: "old content",
                new_str: "new content",
            };

            const result = (textEventListener as any).handleStrReplaceEditorInput(toolInput);

            expect(result).toBe(false);
            expect(mockConsoleLog).not.toHaveBeenCalled();
        });

        it("should return false when no str_replace entries are found", () => {
            const toolInput = {
                command: "str_replace",
                path: "test.txt",
                // Missing old_str and new_str
            };

            const result = (textEventListener as any).handleStrReplaceEditorInput(toolInput);

            expect(result).toBe(false);
            expect(mockConsoleLog).not.toHaveBeenCalled();
        });
    });

    describe("streaming behavior", () => {
        it("should stream assistant response chunks in default mode", () => {
            const defaultListener = new TextEventListener(createDefaultConfig());

            defaultListener.onAssistantResponseStart();
            defaultListener.onAssistantResponseChunk("Hello ");
            defaultListener.onAssistantResponseChunk("world!");
            defaultListener.onAssistantResponseEnd();

            // Should stream to stdout
            expect(mockStdoutWrite).toHaveBeenCalledWith("Hello ");
            expect(mockStdoutWrite).toHaveBeenCalledWith("world!");
            expect(mockConsoleLog).toHaveBeenCalled(); // Header should be shown
        });

        it("should accumulate and display final message in quiet mode", () => {
            const quietListener = new TextEventListener(createQuietConfig());

            quietListener.onAssistantResponseStart();
            quietListener.onAssistantResponseChunk("Hello ");
            quietListener.onAssistantResponseChunk("world!");
            quietListener.onAssistantResponseEnd();

            // Should NOT stream to stdout in text mode
            expect(mockStdoutWrite).not.toHaveBeenCalled();
            expect(mockConsoleLog).not.toHaveBeenCalled(); // No header during accumulation

            // Only when agent loop completes should it display the final message
            quietListener.onAgentLoopComplete();
            expect(mockConsoleLog).toHaveBeenCalledWith("Hello world!");
        });

        it("should suppress tool calls in quiet mode", () => {
            const quietListener = new TextEventListener(createQuietConfig());

            quietListener.onToolCallStart("test-tool", { param: "value" });

            // Should not log anything for tool calls in quiet mode
            expect(mockConsoleLog).not.toHaveBeenCalled();
        });

        it("should show tool calls in default mode", () => {
            const defaultListener = new TextEventListener(createDefaultConfig());

            defaultListener.onToolCallStart("test-tool", { param: "value" });

            // Should log tool call in default mode
            expect(mockConsoleLog).toHaveBeenCalled();
            const loggedContent = mockConsoleLog.mock.calls.map((call) => call[0]).join("\n");
            expect(loggedContent).toContain("test-tool");
        });

        it("should show compact tool calls in compact mode", () => {
            const compactListener = new TextEventListener(createCompactConfig());

            compactListener.onToolCallStart("test-tool", { param: "value" });
            compactListener.onToolCallResult("test-tool", { isError: false, text: "result" });

            expect(mockConsoleLog).toHaveBeenCalledWith(formatCliSection("🔧 Tool call: test-tool"));
            expect(mockConsoleLog).toHaveBeenCalledWith("📋 test-tool ✅");
        });

        it("should only show the last assistant message in quiet mode", () => {
            const quietListener = new TextEventListener(createQuietConfig());

            // Simulate multiple assistant responses (like in a conversation)
            // First response
            quietListener.onAssistantResponseStart();
            quietListener.onAssistantResponseChunk("First response");
            quietListener.onAssistantResponseEnd();

            // Second response (this should be the final one shown)
            quietListener.onAssistantResponseStart();
            quietListener.onAssistantResponseChunk("Final response");
            quietListener.onAssistantResponseEnd();

            // Nothing should be displayed during accumulation
            expect(mockStdoutWrite).not.toHaveBeenCalled();
            expect(mockConsoleLog).not.toHaveBeenCalled();

            // Only the last message should be displayed when complete
            quietListener.onAgentLoopComplete();
            expect(mockConsoleLog).toHaveBeenCalledWith("Final response");
            expect(mockConsoleLog).toHaveBeenCalledTimes(1);
        });
    });
});
