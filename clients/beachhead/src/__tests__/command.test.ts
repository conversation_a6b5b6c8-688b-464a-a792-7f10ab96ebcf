import { Writable } from "stream";

import { Command, CommandStatus } from "../workspace-init";

describe("Command", () => {
    let command: Command;
    let mockFunc: jest.Mock;

    beforeEach(() => {
        mockFunc = jest.fn();
        command = new Command("test-command", mockFunc);
    });

    describe("constructor", () => {
        it("should initialize with correct default values", () => {
            expect(command.name()).toBe("test-command");
            expect(command.status()).toBe(CommandStatus.PENDING);
            expect(command.output()).toBe("");
            expect(command.sequence_id()).toBe(0);
            expect(command.last_sent_output_length()).toBe(0);
        });
    });

    describe("sequence_id management", () => {
        it("should increment sequence_id", () => {
            expect(command.sequence_id()).toBe(0);

            command.increment_sequence_id();
            expect(command.sequence_id()).toBe(1);

            command.increment_sequence_id();
            expect(command.sequence_id()).toBe(2);
        });

        it("should update last_sent_output_length", () => {
            expect(command.last_sent_output_length()).toBe(0);

            command.set_last_sent_output_length(10);
            expect(command.last_sent_output_length()).toBe(10);

            command.set_last_sent_output_length(25);
            expect(command.last_sent_output_length()).toBe(25);
        });
    });

    describe("run", () => {
        it("should set status to RUNNING when started", async () => {
            // Setup a function that will resolve after we check the status
            let resolvePromise: (value: boolean) => void;
            mockFunc.mockImplementation(async () => {
                // This will allow us to check the status while running
                await new Promise<void>((resolve) => {
                    resolvePromise = () => {
                        resolve();
                        return true;
                    };
                });
                return true;
            });

            // Start running the command but don't await it
            const runPromise = command.run();

            // Check that status is RUNNING
            expect(command.status()).toBe(CommandStatus.RUNNING);

            // Now resolve the function and wait for run to complete
            resolvePromise!(true);
            await runPromise;

            // After completion, status should be SUCCESS
            expect(command.status()).toBe(CommandStatus.SUCCESS);
        });

        it("should set status to SUCCESS when function returns true", async () => {
            mockFunc.mockResolvedValue(true);

            await command.run();

            expect(command.status()).toBe(CommandStatus.SUCCESS);
            expect(mockFunc).toHaveBeenCalled();
        });

        it("should set status to SKIPPED when function returns false", async () => {
            mockFunc.mockResolvedValue(false);

            await command.run();

            expect(command.status()).toBe(CommandStatus.SKIPPED);
            expect(mockFunc).toHaveBeenCalled();
        });

        it("should set status to FAILED when function throws an error", async () => {
            const errorMessage = "Test error";
            mockFunc.mockRejectedValue(new Error(errorMessage));

            await command.run();

            expect(command.status()).toBe(CommandStatus.FAILED);
            expect(command.output()).toContain(errorMessage);
            expect(mockFunc).toHaveBeenCalled();
        });

        it("should capture output written to the provided stream", async () => {
            // Setup a function that writes to the output stream
            mockFunc.mockImplementation((out: Writable) => {
                out.write(Buffer.from("Test output line 1\n"));
                out.write(Buffer.from("Test output line 2"));
                return Promise.resolve(true);
            });

            await command.run();

            expect(command.output()).toBe("Test output line 1\nTest output line 2\n");
            expect(command.status()).toBe(CommandStatus.SUCCESS);
        });

        it("should add a newline to output if it doesn't end with one", async () => {
            // Setup a function that writes to the output stream without a trailing newline
            mockFunc.mockImplementation((out: Writable) => {
                out.write(Buffer.from("Output without newline"));
                return Promise.resolve(true);
            });

            await command.run();

            expect(command.output()).toBe("Output without newline\n");
        });

        it("should not add a newline if output is empty", async () => {
            // Setup a function that doesn't write anything
            mockFunc.mockResolvedValue(true);

            await command.run();

            expect(command.output()).toBe("");
        });
    });
});
