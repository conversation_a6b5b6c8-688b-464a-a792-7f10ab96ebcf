import { Exchange } from "@augment-internal/sidecar-libs/src/chat/chat-types";
import { ShellProcessTools } from "@augment-internal/sidecar-libs/src/tools/sidecar-tools/shell-process-tools";
import { LocalToolType, ToolSafety } from "@augment-internal/sidecar-libs/src/tools/tool-types";

import {
    TerminalKillProcessTool,
    TerminalLaunchProcessTool,
    TerminalListProcessesTool,
    TerminalReadProcessTool,
    TerminalWriteProcessTool,
} from "../process-tools";

// Mock the shell allowlist functions
jest.mock("@augment-internal/sidecar-libs/src/tools/sidecar-tools/shell-allowlist", () => ({
    getShellAllowlist: jest.fn().mockReturnValue([]),
    checkShellAllowlist: jest.fn().mockReturnValue(true),
}));

describe("Process Tools", () => {
    let mockProcessTools: jest.Mocked<ShellProcessTools>;
    let mockAbortSignal: AbortSignal;
    let mockToolUseId: string;
    let mockChatHistory: Exchange[];

    beforeEach(() => {
        // Create a mock ShellProcessTools
        mockProcessTools = {
            launch: jest.fn(),
            kill: jest.fn(),
            readOutput: jest.fn(),
            writeInput: jest.fn(),
            listProcesses: jest.fn(),
            waitForProcess: jest.fn(),
            shellName: "bash",
        } as any;

        mockAbortSignal = new AbortController().signal;
        mockToolUseId = "test-tool-use-id";
        mockChatHistory = [];
    });

    describe("TerminalLaunchProcessTool", () => {
        let tool: TerminalLaunchProcessTool;

        beforeEach(() => {
            tool = new TerminalLaunchProcessTool(mockProcessTools);
        });

        it("should initialize with correct properties", () => {
            expect(tool.name).toBe(LocalToolType.launchProcess);
            expect(tool.toolSafety).toBe(ToolSafety.Check);
            expect(tool.version).toBe(2);
            expect(tool.description).toContain("Launch a new process");
        });

        it("should have correct input schema", () => {
            const schema = JSON.parse(tool.inputSchemaJson);
            expect(schema.required).toEqual(["command", "wait", "max_wait_seconds", "cwd"]);
            expect(schema.properties.command.type).toBe("string");
            expect(schema.properties.wait.type).toBe("boolean");
            expect(schema.properties.max_wait_seconds.type).toBe("number");
            expect(schema.properties.cwd.type).toBe("string");
        });

        it("should launch process without waiting", async () => {
            mockProcessTools.launch.mockResolvedValue(123);

            const result = await tool.call(
                {
                    command: "echo hello",
                    wait: false,
                    max_wait_seconds: 10,
                    cwd: "/tmp",
                },
                mockChatHistory,
                mockAbortSignal,
                mockToolUseId
            );

            expect(mockProcessTools.launch).toHaveBeenCalledWith(
                "bash -l -c 'echo hello'", // Note that it's intentional that the command is wrapped so it runs in a login shell
                "/tmp",
                mockAbortSignal
            );
            expect(result.isError).toBe(false);
            expect(result.text).toContain("Process launched with terminal ID 123");
        });

        it("should launch process with waiting and completion", async () => {
            mockProcessTools.launch.mockResolvedValue(123);
            mockProcessTools.waitForProcess.mockResolvedValue({
                output: "hello world",
                returnCode: 0,
            });

            const result = await tool.call(
                {
                    command: "echo hello",
                    wait: true,
                    max_wait_seconds: 10,
                    cwd: "/tmp",
                },
                mockChatHistory,
                mockAbortSignal,
                mockToolUseId
            );

            expect(mockProcessTools.waitForProcess).toHaveBeenCalledWith(123, 10, mockAbortSignal);
            expect(result.isError).toBe(false);
            expect(result.text).toContain("<return-code>");
            expect(result.text).toContain("0");
            expect(result.text).toContain("</return-code>");
            expect(result.text).toContain("<output>");
            expect(result.text).toContain("hello world");
            expect(result.text).toContain("</output>");
        });

        it("should handle process timeout", async () => {
            mockProcessTools.launch.mockResolvedValue(123);
            mockProcessTools.waitForProcess.mockResolvedValue({
                output: "partial output",
                returnCode: null, // Still running
            });

            const result = await tool.call(
                {
                    command: "sleep 100",
                    wait: true,
                    max_wait_seconds: 1,
                    cwd: "/tmp",
                },
                mockChatHistory,
                mockAbortSignal,
                mockToolUseId
            );

            expect(result.isError).toBe(false);
            expect(result.text).toContain("Command may still be running");
            expect(result.text).toContain("Terminal ID 123");
            expect(result.text).toContain("partial output");
        });

        it("should handle launch errors", async () => {
            mockProcessTools.launch.mockRejectedValue(new Error("Launch failed"));

            const result = await tool.call(
                {
                    command: "invalid-command",
                    wait: false,
                    max_wait_seconds: 10,
                    cwd: "/tmp",
                },
                mockChatHistory,
                mockAbortSignal,
                mockToolUseId
            );

            expect(result.isError).toBe(true);
            expect(result.text).toContain("Failed to launch process: Launch failed");
        });

        it("should handle string error from launch", async () => {
            // Mock launch to return a string error instead of a number
            (mockProcessTools.launch as jest.Mock).mockResolvedValue("Error message");

            const result = await tool.call(
                {
                    command: "echo hello",
                    wait: false,
                    max_wait_seconds: 10,
                    cwd: "/tmp",
                },
                mockChatHistory,
                mockAbortSignal,
                mockToolUseId
            );

            expect(result.isError).toBe(true);
            expect(result.text).toBe("Error message");
        });

        it("should handle non-zero exit codes", async () => {
            mockProcessTools.launch.mockResolvedValue(123);
            mockProcessTools.waitForProcess.mockResolvedValue({
                output: "error output",
                returnCode: 1,
            });

            const result = await tool.call(
                {
                    command: "exit 1",
                    wait: true,
                    max_wait_seconds: 10,
                    cwd: "/tmp",
                },
                mockChatHistory,
                mockAbortSignal,
                mockToolUseId
            );

            expect(result.isError).toBe(true);
            expect(result.text).toContain("<return-code>\n1\n</return-code>");
        });
    });

    describe("TerminalKillProcessTool", () => {
        let tool: TerminalKillProcessTool;

        beforeEach(() => {
            tool = new TerminalKillProcessTool(mockProcessTools);
        });

        it("should initialize with correct properties", () => {
            expect(tool.name).toBe(LocalToolType.killProcess);
            expect(tool.toolSafety).toBe(ToolSafety.Safe);
            expect(tool.description).toBe("Kill a process by its terminal ID.");
        });

        it("should kill a running process", async () => {
            mockProcessTools.kill.mockResolvedValue({
                output: "process output",
                killed: true,
                returnCode: -1,
            });

            const result = await tool.call(
                { terminal_id: 123 },
                mockChatHistory,
                mockAbortSignal,
                mockToolUseId
            );

            expect(mockProcessTools.kill).toHaveBeenCalledWith(123);
            expect(result.isError).toBe(false);
            expect(result.text).toContain("Terminal 123 killed");
            expect(result.text).toContain("process output");
        });

        it("should handle already exited process", async () => {
            mockProcessTools.kill.mockResolvedValue({
                output: "final output",
                killed: false,
                returnCode: 0,
            });

            const result = await tool.call(
                { terminal_id: 123 },
                mockChatHistory,
                mockAbortSignal,
                mockToolUseId
            );

            expect(result.isError).toBe(false);
            expect(result.text).toContain("Terminal 123 already exited");
            expect(result.text).toContain("<return-code>0</return-code>");
        });

        it("should handle terminal not found", async () => {
            mockProcessTools.kill.mockResolvedValue(undefined);

            const result = await tool.call(
                { terminal_id: 999 },
                mockChatHistory,
                mockAbortSignal,
                mockToolUseId
            );

            expect(result.isError).toBe(true);
            expect(result.text).toBe("Terminal 999 not found");
        });
    });

    describe("TerminalReadProcessTool", () => {
        let tool: TerminalReadProcessTool;

        beforeEach(() => {
            tool = new TerminalReadProcessTool(mockProcessTools);
        });

        it("should initialize with correct properties", () => {
            expect(tool.name).toBe(LocalToolType.readProcess);
            expect(tool.toolSafety).toBe(ToolSafety.Safe);
            expect(tool.description).toContain("Read output from a terminal");
        });

        it("should read output without waiting", async () => {
            mockProcessTools.readOutput.mockResolvedValue({
                output: "command output",
                returnCode: 0,
            });

            const result = await tool.call(
                {
                    terminal_id: 123,
                    wait: false,
                    max_wait_seconds: 10,
                },
                mockChatHistory,
                mockAbortSignal,
                mockToolUseId
            );

            expect(mockProcessTools.readOutput).toHaveBeenCalledWith(123);
            expect(mockProcessTools.waitForProcess).not.toHaveBeenCalled();
            expect(result.isError).toBe(false);
            expect(result.text).toContain("terminal 123 (status: completed)");
            expect(result.text).toContain("command output");
            expect(result.text).toContain("<return-code>");
            expect(result.text).toContain("0");
            expect(result.text).toContain("</return-code>");
        });

        it("should read output with waiting", async () => {
            mockProcessTools.readOutput.mockResolvedValue({
                output: "final output",
                returnCode: null, // Still running
            });

            const result = await tool.call(
                {
                    terminal_id: 123,
                    wait: true,
                    max_wait_seconds: 30,
                },
                mockChatHistory,
                mockAbortSignal,
                mockToolUseId
            );

            expect(mockProcessTools.waitForProcess).toHaveBeenCalledWith(123, 30, mockAbortSignal);
            expect(result.isError).toBe(false);
            expect(result.text).toContain("terminal 123 (status: still running)");
            expect(result.text).not.toContain("<return-code>");
        });

        it("should handle terminal not found", async () => {
            mockProcessTools.readOutput.mockResolvedValue(undefined);

            const result = await tool.call(
                {
                    terminal_id: 999,
                    wait: false,
                    max_wait_seconds: 10,
                },
                mockChatHistory,
                mockAbortSignal,
                mockToolUseId
            );

            expect(result.isError).toBe(true);
            expect(result.text).toBe("Terminal 999 not found");
        });
    });

    describe("TerminalWriteProcessTool", () => {
        let tool: TerminalWriteProcessTool;

        beforeEach(() => {
            tool = new TerminalWriteProcessTool(mockProcessTools);
        });

        it("should initialize with correct properties", () => {
            expect(tool.name).toBe(LocalToolType.writeProcess);
            expect(tool.toolSafety).toBe(ToolSafety.Safe);
            expect(tool.description).toBe("Write input to a terminal.");
        });

        it("should have correct input schema", () => {
            const schema = JSON.parse(tool.inputSchemaJson);
            expect(schema.required).toEqual(["terminal_id", "input_text"]);
            expect(schema.properties.terminal_id.type).toBe("integer");
            expect(schema.properties.input_text.type).toBe("string");
        });

        it("should write input successfully", async () => {
            mockProcessTools.writeInput.mockReturnValue(true);

            const result = await tool.call(
                {
                    terminal_id: 123,
                    input_text: "hello world\n",
                },
                mockChatHistory,
                mockAbortSignal,
                mockToolUseId
            );

            expect(mockProcessTools.writeInput).toHaveBeenCalledWith(123, "hello world\n");
            expect(result.isError).toBe(false);
            expect(result.text).toBe("Input written to terminal 123");
        });

        it("should handle write failure", async () => {
            mockProcessTools.writeInput.mockReturnValue(false);

            const result = await tool.call(
                {
                    terminal_id: 999,
                    input_text: "test input",
                },
                mockChatHistory,
                mockAbortSignal,
                mockToolUseId
            );

            expect(result.isError).toBe(true);
            expect(result.text).toBe("Terminal 999 not found or write failed");
        });
    });

    describe("TerminalListProcessesTool", () => {
        let tool: TerminalListProcessesTool;

        beforeEach(() => {
            tool = new TerminalListProcessesTool(mockProcessTools);
        });

        it("should initialize with correct properties", () => {
            expect(tool.name).toBe(LocalToolType.listProcesses);
            expect(tool.toolSafety).toBe(ToolSafety.Safe);
            expect(tool.description).toContain("List all known terminals");
        });

        it("should handle empty process list", async () => {
            mockProcessTools.listProcesses.mockReturnValue([]);

            const result = await tool.call({}, mockChatHistory, mockAbortSignal, mockToolUseId);

            expect(result.isError).toBe(false);
            expect(result.text).toBe("No processes found");
        });

        it("should list running processes", async () => {
            mockProcessTools.listProcesses.mockReturnValue([
                {
                    id: 1,
                    command: "echo hello",
                    state: "completed",
                    returnCode: 0,
                },
                {
                    id: 2,
                    command: "sleep 100",
                    state: "running",
                    returnCode: null,
                },
                {
                    id: 3,
                    command: "invalid-cmd",
                    state: "killed",
                    returnCode: -1,
                },
            ]);

            const result = await tool.call({}, mockChatHistory, mockAbortSignal, mockToolUseId);

            expect(result.isError).toBe(false);
            expect(result.text).toContain("Here are all known processes:");
            expect(result.text).toContain("Terminal 1 [completed (return code: 0)]: echo hello");
            expect(result.text).toContain("Terminal 2 [running]: sleep 100");
            expect(result.text).toContain("Terminal 3 [killed (return code: -1)]: invalid-cmd");
            expect(result.text).toContain("There is 1 process still running.");
        });

        it("should handle multiple running processes", async () => {
            mockProcessTools.listProcesses.mockReturnValue([
                {
                    id: 1,
                    command: "sleep 100",
                    state: "running",
                    returnCode: null,
                },
                {
                    id: 2,
                    command: "tail -f log.txt",
                    state: "running",
                    returnCode: null,
                },
            ]);

            const result = await tool.call({}, mockChatHistory, mockAbortSignal, mockToolUseId);

            expect(result.text).toContain("There are 2 processes still running.");
        });

        it("should handle no running processes", async () => {
            mockProcessTools.listProcesses.mockReturnValue([
                {
                    id: 1,
                    command: "echo hello",
                    state: "completed",
                    returnCode: 0,
                },
            ]);

            const result = await tool.call({}, mockChatHistory, mockAbortSignal, mockToolUseId);

            expect(result.text).not.toContain("process still running");
            expect(result.text).not.toContain("processes still running");
        });
    });
});
