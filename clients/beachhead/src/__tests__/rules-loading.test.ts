import * as fs from "fs";
import * as os from "os";
import * as path from "path";

import { loadAllRules } from "../cli";

describe("Rules Loading", () => {
    let tempDir: string;

    beforeEach(async () => {
        // Create a temporary directory for each test
        tempDir = await fs.promises.mkdtemp(path.join(os.tmpdir(), "rules-test-"));
    });

    afterEach(async () => {
        // Clean up temporary directory
        await fs.promises.rm(tempDir, { recursive: true, force: true });
    });

    test("should load legacy .augment-guidelines file", async () => {
        // Create legacy guidelines file
        const guidelinesContent = "# Legacy Guidelines\nThis is a test guideline.";
        await fs.promises.writeFile(path.join(tempDir, ".augment-guidelines"), guidelinesContent);

        const result = await loadAllRules(tempDir);
        expect(result).toContain("Legacy Guidelines");
        expect(result).toContain("This is a test guideline.");
    });

    test("should load new .augment/rules/ format with always_apply rules", async () => {
        // Create .augment/rules directory
        const rulesDir = path.join(tempDir, ".augment", "rules");
        await fs.promises.mkdir(rulesDir, { recursive: true });

        // Create an always_apply rule
        const alwaysRule = `---
type: "always_apply"
---

# Always Apply Rule
This rule should always be included.`;
        await fs.promises.writeFile(path.join(rulesDir, "always.md"), alwaysRule);

        // Create a manual rule (should be filtered out)
        const manualRule = `---
type: "manual"
---

# Manual Rule
This rule should not be included.`;
        await fs.promises.writeFile(path.join(rulesDir, "manual.md"), manualRule);

        const result = await loadAllRules(tempDir);
        expect(result).toContain("Always Apply Rule");
        expect(result).toContain("This rule should always be included.");
        expect(result).not.toContain("Manual Rule");
        expect(result).not.toContain("This rule should not be included.");
    });

    test("should load legacy alwaysApply format as always_apply", async () => {
        // Create .augment/rules directory
        const rulesDir = path.join(tempDir, ".augment", "rules");
        await fs.promises.mkdir(rulesDir, { recursive: true });

        // Create a legacy alwaysApply rule
        const legacyRule = `---
alwaysApply: true
---

# Legacy Always Apply Rule
This uses the old alwaysApply format.`;
        await fs.promises.writeFile(path.join(rulesDir, "legacy.md"), legacyRule);

        const result = await loadAllRules(tempDir);
        expect(result).toContain("Legacy Always Apply Rule");
        expect(result).toContain("This uses the old alwaysApply format.");
    });

    test("should combine legacy guidelines and new rules", async () => {
        // Create legacy guidelines file
        const guidelinesContent = "# Legacy Guidelines\nLegacy content.";
        await fs.promises.writeFile(path.join(tempDir, ".augment-guidelines"), guidelinesContent);

        // Create .augment/rules directory with always_apply rule
        const rulesDir = path.join(tempDir, ".augment", "rules");
        await fs.promises.mkdir(rulesDir, { recursive: true });

        const newRule = `---
type: "always_apply"
---

# New Rule
New rules content.`;
        await fs.promises.writeFile(path.join(rulesDir, "new.md"), newRule);

        const result = await loadAllRules(tempDir);
        expect(result).toContain("Legacy Guidelines");
        expect(result).toContain("Legacy content.");
        expect(result).toContain("New Rule");
        expect(result).toContain("New rules content.");
    });

    test("should handle missing rules directory gracefully", async () => {
        // Only create legacy guidelines
        const guidelinesContent = "# Only Legacy";
        await fs.promises.writeFile(path.join(tempDir, ".augment-guidelines"), guidelinesContent);

        const result = await loadAllRules(tempDir);
        expect(result).toContain("Only Legacy");
    });

    test("should handle missing guidelines file gracefully", async () => {
        // Only create new rules
        const rulesDir = path.join(tempDir, ".augment", "rules");
        await fs.promises.mkdir(rulesDir, { recursive: true });

        const newRule = `---
type: "always_apply"
---

# Only New Rule`;
        await fs.promises.writeFile(path.join(rulesDir, "new.md"), newRule);

        const result = await loadAllRules(tempDir);
        expect(result).toContain("Only New Rule");
    });

    test("should return empty string when no rules exist", async () => {
        const result = await loadAllRules(tempDir);
        expect(result).toBe("");
    });
});
