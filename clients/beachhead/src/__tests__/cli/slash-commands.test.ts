/**
 * Tests for CLI slash command validation in interactive mode
 */
import {
    isValidSlashCommand,
    processSlashCommand,
    VALID_SLASH_COMMANDS,
} from "../../cli/slash-commands";

describe("CLI Slash Command Validation", () => {
    describe("VALID_SLASH_COMMANDS constant", () => {
        it("should contain the expected valid commands", () => {
            expect(VALID_SLASH_COMMANDS.has("/exit")).toBe(true);
            expect(VALID_SLASH_COMMANDS.has("/help")).toBe(true);
            expect(VALID_SLASH_COMMANDS.has("/request-id")).toBe(true);
            expect(VALID_SLASH_COMMANDS.size).toBe(3);
        });
    });

    describe("isValidSlashCommand", () => {
        it("should recognize valid slash commands", () => {
            expect(isValidSlashCommand("/exit")).toBe(true);
            expect(isValidSlashCommand("/help")).toBe(true);
            expect(isValidSlashCommand("/request-id")).toBe(true);
        });

        it("should be case insensitive", () => {
            expect(isValidSlashCommand("/EXIT")).toBe(true);
            expect(isValidSlashCommand("/Help")).toBe(true);
            expect(isValidSlashCommand("/REQUEST-ID")).toBe(true);
        });

        it("should reject invalid slash commands", () => {
            expect(isValidSlashCommand("/unknown")).toBe(false);
            expect(isValidSlashCommand("/foobar")).toBe(false);
            expect(isValidSlashCommand("/")).toBe(false);
            expect(isValidSlashCommand("/find")).toBe(false); // This is a webview command, not CLI
            expect(isValidSlashCommand("/fix")).toBe(false); // This is a webview command, not CLI
        });

        it("should handle edge cases", () => {
            expect(isValidSlashCommand("")).toBe(false);
            expect(isValidSlashCommand("help")).toBe(false); // Without slash
            expect(isValidSlashCommand("exit")).toBe(false); // Without slash
        });
    });

    describe("processSlashCommand function", () => {
        it("should correctly categorize valid slash commands", () => {
            expect(processSlashCommand("/exit")).toBe("exit");
            expect(processSlashCommand("/help")).toBe("help");
            expect(processSlashCommand("/request-id")).toBe("request-id");
        });

        it("should handle case insensitivity", () => {
            expect(processSlashCommand("/EXIT")).toBe("exit");
            expect(processSlashCommand("/Help")).toBe("help");
            expect(processSlashCommand("/REQUEST-ID")).toBe("request-id");
        });

        it("should categorize invalid slash commands", () => {
            expect(processSlashCommand("/unknown")).toBe("invalid");
            expect(processSlashCommand("/foobar")).toBe("invalid");
            expect(processSlashCommand("/")).toBe("invalid");
            expect(processSlashCommand("/find")).toBe("invalid");
            expect(processSlashCommand("/fix")).toBe("invalid");
        });

        it("should categorize regular instructions", () => {
            expect(processSlashCommand("analyze this codebase")).toBe("regular");
            expect(processSlashCommand("fix the bug")).toBe("regular");
            expect(processSlashCommand("help me with this")).toBe("regular"); // No slash
        });

        it("should handle empty input", () => {
            expect(processSlashCommand("")).toBe("empty");
            expect(processSlashCommand("   ")).toBe("empty");
        });

        it("should handle whitespace around commands", () => {
            expect(processSlashCommand(" /help ")).toBe("help");
            expect(processSlashCommand("  /exit  ")).toBe("exit");
        });
    });

    describe("error message formatting", () => {
        function formatInvalidSlashCommandError(command: string): string {
            return `❌ Unknown command: ${command}

Available commands:
  /exit        - Exit the interactive session
  /help        - Show this help message
  /request-id  - Show the last request ID for support purposes

Type your instruction without a slash to send it to the agent.`;
        }

        it("should format error messages correctly", () => {
            const errorMsg = formatInvalidSlashCommandError("/unknown");
            expect(errorMsg).toContain("❌ Unknown command: /unknown");
            expect(errorMsg).toContain("/exit");
            expect(errorMsg).toContain("/help");
            expect(errorMsg).toContain("/request-id");
            expect(errorMsg).toContain("Type your instruction without a slash");
        });
    });
});
