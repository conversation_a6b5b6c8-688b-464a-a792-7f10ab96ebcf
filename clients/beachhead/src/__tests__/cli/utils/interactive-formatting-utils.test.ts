/**
 * Tests for ThinkingAnimation to verify race condition fixes
 */
import { ThinkingAnimation } from "../../../cli/utils/interactive-formatting-utils";

// Mock process.stdout.write to capture output
const mockStdoutWrite = jest.fn();
const originalStdoutWrite = process.stdout.write;

describe("ThinkingAnimation", () => {
    beforeEach(() => {
        jest.clearAllMocks();
        // Mock process.stdout.write
        process.stdout.write = mockStdoutWrite;
        jest.useFakeTimers();
    });

    afterEach(() => {
        // Restore original stdout.write
        process.stdout.write = originalStdoutWrite;
        jest.useRealTimers();
    });

    test("should stop animation immediately without race condition", () => {
        const animation = new ThinkingAnimation();

        // Start the animation
        animation.start("Testing");

        // Verify initial output
        expect(mockStdoutWrite).toHaveBeenCalledWith("Testing ⠋");

        // Clear the mock to focus on what happens after
        mockStdoutWrite.mockClear();

        // Advance time to trigger interval callback
        jest.advanceTimersByTime(100);

        // Should have updated the animation
        expect(mockStdoutWrite).toHaveBeenCalled();
        mockStdoutWrite.mockClear();

        // Stop the animation
        animation.stop();

        // Verify the line was cleared
        expect(mockStdoutWrite).toHaveBeenCalledWith("\r\x1B[K");
        mockStdoutWrite.mockClear();

        // Advance time again - this should NOT trigger any more output
        // due to the race condition fix
        jest.advanceTimersByTime(100);
        jest.advanceTimersByTime(100);
        jest.advanceTimersByTime(100);

        // Should not have any more output after stop()
        expect(mockStdoutWrite).not.toHaveBeenCalled();
    });

    test("should handle multiple start calls without creating multiple animations", () => {
        const animation = new ThinkingAnimation();

        // Start the animation
        animation.start("First");
        expect(mockStdoutWrite).toHaveBeenCalledWith("First ⠋");
        mockStdoutWrite.mockClear();

        // Start again - should not create issues
        animation.start("Second");
        expect(mockStdoutWrite).toHaveBeenCalledWith("Second ⠋");
        mockStdoutWrite.mockClear();

        // Advance time
        jest.advanceTimersByTime(100);

        // Should only have one animation running (the second one)
        const calls = mockStdoutWrite.mock.calls;
        expect(calls.length).toBe(1);
        expect(calls[0][0]).toContain("Second");

        // Stop and verify no more output
        animation.stop();
        mockStdoutWrite.mockClear();

        jest.advanceTimersByTime(200);
        expect(mockStdoutWrite).not.toHaveBeenCalled();
    });

    test("should handle stop called multiple times", () => {
        const animation = new ThinkingAnimation();

        // Start the animation
        animation.start("Testing");
        mockStdoutWrite.mockClear();

        // Stop multiple times
        animation.stop();
        animation.stop();
        animation.stop();

        // Should only clear once
        expect(mockStdoutWrite).toHaveBeenCalledTimes(1);
        expect(mockStdoutWrite).toHaveBeenCalledWith("\r\x1B[K");

        mockStdoutWrite.mockClear();

        // Advance time - should not produce any output
        jest.advanceTimersByTime(500);
        expect(mockStdoutWrite).not.toHaveBeenCalled();
    });

    test("should handle stop called before start", () => {
        const animation = new ThinkingAnimation();

        // Stop before starting - should not crash
        expect(() => animation.stop()).not.toThrow();

        // Should not have any output
        expect(mockStdoutWrite).not.toHaveBeenCalled();
    });
});
