import { McpServerConfig } from "@augment-internal/sidecar-libs/src/tools/tool-types";

import { validateMcpServerConfigurations } from "../../../cli/utils/mcp-utils";

// Mock fs module
jest.mock("fs");

describe("MCP Server CLI Configuration", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe("validateMcpServerConfigurations", () => {
        test("should validate valid configurations", () => {
            const servers: McpServerConfig[] = [
                { name: "fs", type: "stdio", command: "mcp-fs" },
                { name: "web", type: "http", url: "https://example.com" },
            ];

            expect(() => validateMcpServerConfigurations(servers)).not.toThrow();
        });

        test("should throw error for duplicate names", () => {
            const servers: McpServerConfig[] = [
                { name: "test", type: "stdio", command: "cmd1" },
                { name: "test", type: "http", url: "https://example.com" },
            ];

            expect(() => validateMcpServerConfigurations(servers)).toThrow(
                'Duplicate MCP server name: "test". Server names must be unique.'
            );
        });

        test("should throw error for stdio server without command", () => {
            const servers = [{ name: "test", type: "stdio" } as any];

            expect(() => validateMcpServerConfigurations(servers)).toThrow(
                'MCP server "test" is missing required command for stdio type'
            );
        });

        test("should throw error for http server without url", () => {
            const servers = [{ name: "test", type: "http" } as any];

            expect(() => validateMcpServerConfigurations(servers)).toThrow(
                'MCP server "test" is missing required url for http type'
            );
        });

        test("should throw error for invalid URL", () => {
            const servers = [{ name: "test", type: "http", url: "not-a-url" } as any];

            expect(() => validateMcpServerConfigurations(servers)).toThrow(
                'MCP server "test" has invalid URL: "not-a-url"'
            );
        });

        test("should throw error for invalid stdio timeout", () => {
            const servers = [
                { name: "test", type: "stdio", command: "cmd", timeoutMs: 500 } as any,
            ];

            expect(() => validateMcpServerConfigurations(servers)).toThrow(
                'MCP server "test" timeout must be between 1000ms and 300000ms (5 minutes)'
            );
        });

        test("should throw error for invalid http timeout", () => {
            const servers = [
                {
                    name: "test",
                    type: "http",
                    url: "https://example.com",
                    timeoutMs: 500,
                } as any,
            ];

            expect(() => validateMcpServerConfigurations(servers)).toThrow(
                'MCP server "test" timeout must be between 1000ms and 60000ms (1 minute) for HTTP/SSE servers'
            );
        });

        test("should throw error for invalid environment variables", () => {
            const servers = [
                {
                    name: "test",
                    type: "stdio",
                    command: "cmd",
                    env: { key: 123 },
                } as any,
            ];

            expect(() => validateMcpServerConfigurations(servers)).toThrow(
                'MCP server "test" environment variables must be string key-value pairs'
            );
        });
    });
});
