import { AgentConfig } from "../agent-config";
import { RemoteWorkspaceSetupStepStatus } from "../remote-agent-manager/types";
import { Command, CommandStatus, InitCommands } from "../workspace-init";

// Mock the Command class
jest.mock("../workspace-init", () => {
    const originalModule = jest.requireActual("../workspace-init");

    // Create a mock Command class that extends the original
    class MockCommand extends originalModule.Command {
        constructor(name: string, func: any) {
            super(name, func);
        }
    }

    return {
        ...originalModule,
        Command: MockCommand,
    };
});

describe("InitCommands", () => {
    let mockConfig: AgentConfig;
    let initCommands: InitCommands;
    let mockCommands: Command[];

    beforeEach(() => {
        // Create a mock AgentConfig
        mockConfig = {
            git_repo: jest.fn().mockReturnValue("https://github.com/example/repo.git"),
            git_ref: jest.fn().mockReturnValue("main"),
            initial_patch: jest.fn().mockReturnValue(null),
            setup_script: jest.fn().mockReturnValue(null),
        } as unknown as AgentConfig;

        // Create InitCommands with mocked dependencies
        initCommands = new InitCommands(mockConfig, "/test/workspace/path");

        // Get the internal commands array for testing
        mockCommands = (initCommands as any)._cmds;
    });

    describe("workspace_setup_status", () => {
        it("should return empty steps array when there are no new logs", () => {
            // Setup: All commands have no new logs (current length equals last sent length)
            mockCommands.forEach((cmd) => {
                jest.spyOn(cmd, "output").mockReturnValue("Some logs");
                jest.spyOn(cmd, "last_sent_output_length").mockReturnValue("Some logs".length);
            });

            // Execute
            const result = initCommands.workspace_setup_status();

            // Verify
            expect(result.steps).toEqual([]);
            // Verify that increment_sequence_id was not called
            mockCommands.forEach((cmd) => {
                // We need to spy on the method before we can check if it was called
                const incrementSpy = jest.spyOn(cmd, "increment_sequence_id");
                expect(incrementSpy).not.toHaveBeenCalled();
            });
        });

        it("should include steps with new logs and increment sequence ID", () => {
            // Setup: First command has new logs
            const oldLogs = "Initial logs";
            const newLogs = "Initial logs\nNew logs added";

            jest.spyOn(mockCommands[0], "name").mockReturnValue("git clone");
            jest.spyOn(mockCommands[0], "output").mockReturnValue(newLogs);
            jest.spyOn(mockCommands[0], "last_sent_output_length").mockReturnValue(oldLogs.length);
            jest.spyOn(mockCommands[0], "status").mockReturnValue(CommandStatus.RUNNING);
            jest.spyOn(mockCommands[0], "sequence_id").mockReturnValue(1);
            jest.spyOn(mockCommands[0], "increment_sequence_id");
            jest.spyOn(mockCommands[0], "set_last_sent_output_length");

            // Other commands have no new logs
            for (let i = 1; i < mockCommands.length; i++) {
                jest.spyOn(mockCommands[i], "output").mockReturnValue("Some logs");
                jest.spyOn(mockCommands[i], "last_sent_output_length").mockReturnValue(
                    "Some logs".length
                );
            }

            // Execute
            const result = initCommands.workspace_setup_status();

            // Verify
            expect(result.steps.length).toBe(1);
            expect(result.steps[0]).toEqual({
                step_description: "git clone",
                logs: "\nNew logs added",
                status: RemoteWorkspaceSetupStepStatus.running,
                sequence_id: 1,
                step_number: 0,
            });

            // Verify sequence ID was incremented
            expect(mockCommands[0].increment_sequence_id).toHaveBeenCalledTimes(1);

            // Verify last_sent_output_length was updated
            expect(mockCommands[0].set_last_sent_output_length).toHaveBeenCalledWith(
                newLogs.length
            );
        });

        it("should map CommandStatus.SUCCESS to RemoteWorkspaceSetupStepStatus.success", () => {
            // Setup: Command with SUCCESS status and new logs
            jest.spyOn(mockCommands[0], "name").mockReturnValue("git clone");
            jest.spyOn(mockCommands[0], "output").mockReturnValue("Logs with new content");
            jest.spyOn(mockCommands[0], "last_sent_output_length").mockReturnValue("Logs".length);
            jest.spyOn(mockCommands[0], "status").mockReturnValue(CommandStatus.SUCCESS);
            jest.spyOn(mockCommands[0], "sequence_id").mockReturnValue(2);

            // Execute
            const result = initCommands.workspace_setup_status();

            // Verify
            expect(result.steps[0].status).toBe(RemoteWorkspaceSetupStepStatus.success);
        });

        it("should map CommandStatus.FAILED to RemoteWorkspaceSetupStepStatus.failure", () => {
            // Setup: Command with FAILED status and new logs
            jest.spyOn(mockCommands[0], "name").mockReturnValue("git clone");
            jest.spyOn(mockCommands[0], "output").mockReturnValue("Error logs with new content");
            jest.spyOn(mockCommands[0], "last_sent_output_length").mockReturnValue("Error".length);
            jest.spyOn(mockCommands[0], "status").mockReturnValue(CommandStatus.FAILED);
            jest.spyOn(mockCommands[0], "sequence_id").mockReturnValue(3);

            // Execute
            const result = initCommands.workspace_setup_status();

            // Verify
            expect(result.steps[0].status).toBe(RemoteWorkspaceSetupStepStatus.failure);
        });

        it("should map other CommandStatus values to RemoteWorkspaceSetupStepStatus.unknown", () => {
            // Setup: Command with PENDING status and new logs
            jest.spyOn(mockCommands[0], "name").mockReturnValue("git clone");
            jest.spyOn(mockCommands[0], "output").mockReturnValue("Pending logs with new content");
            jest.spyOn(mockCommands[0], "last_sent_output_length").mockReturnValue(
                "Pending".length
            );
            jest.spyOn(mockCommands[0], "status").mockReturnValue(CommandStatus.PENDING);
            jest.spyOn(mockCommands[0], "sequence_id").mockReturnValue(4);

            // Execute
            const result = initCommands.workspace_setup_status();

            // Verify
            expect(result.steps[0].status).toBe(RemoteWorkspaceSetupStepStatus.unknown);
        });

        it("should handle multiple commands with new logs", () => {
            // Only set up the first command to have new logs
            // This better matches the actual behavior of the implementation
            const cmd = mockCommands[0];
            const oldLength = 0;
            const newLength = 10;

            jest.spyOn(cmd, "name").mockReturnValue("command-0");
            jest.spyOn(cmd, "output").mockReturnValue("x".repeat(newLength));
            jest.spyOn(cmd, "last_sent_output_length").mockReturnValue(oldLength);
            jest.spyOn(cmd, "status").mockReturnValue(CommandStatus.RUNNING);
            jest.spyOn(cmd, "sequence_id").mockReturnValue(0);
            jest.spyOn(cmd, "increment_sequence_id");
            jest.spyOn(cmd, "set_last_sent_output_length");

            // Execute
            const result = initCommands.workspace_setup_status();

            // Verify - only the first command should have new logs
            expect(result.steps.length).toBe(1);

            // Verify the step
            const step = result.steps[0];
            expect(step.step_description).toBe("command-0");
            expect(step.logs).toBe("x".repeat(newLength - oldLength));
            expect(step.status).toBe(RemoteWorkspaceSetupStepStatus.running);
            expect(step.sequence_id).toBe(0);
            expect(step.step_number).toBe(0);

            // Verify sequence ID was incremented
            expect(cmd.increment_sequence_id).toHaveBeenCalledTimes(1);

            // Verify last_sent_output_length was updated
            expect(cmd.set_last_sent_output_length).toHaveBeenCalledWith(newLength);
        });

        it("should update steps when status changes but logs don't", () => {
            // Setup: Command with same logs but different status
            const cmd = mockCommands[0];
            const logs = "Command logs";

            jest.spyOn(cmd, "name").mockReturnValue("git clone");
            jest.spyOn(cmd, "output").mockReturnValue(logs);
            jest.spyOn(cmd, "last_sent_output_length").mockReturnValue(logs.length);
            jest.spyOn(cmd, "status").mockReturnValue(CommandStatus.SUCCESS);
            jest.spyOn(cmd, "last_sent_status").mockReturnValue(CommandStatus.RUNNING);
            jest.spyOn(cmd, "sequence_id").mockReturnValue(5);
            jest.spyOn(cmd, "increment_sequence_id");
            jest.spyOn(cmd, "set_last_sent_output_length");
            jest.spyOn(cmd, "set_last_sent_status");

            // Execute
            const result = initCommands.workspace_setup_status();

            // Verify
            expect(result.steps.length).toBe(1);
            expect(result.steps[0]).toEqual({
                step_description: "git clone",
                logs: "", // No new logs, just status change
                status: RemoteWorkspaceSetupStepStatus.success,
                sequence_id: 5,
                step_number: 0,
            });

            // Verify sequence ID was incremented
            expect(cmd.increment_sequence_id).toHaveBeenCalledTimes(1);

            // Verify last_sent_status was updated
            expect(cmd.set_last_sent_status).toHaveBeenCalledWith(CommandStatus.SUCCESS);
        });

        it("should track last_sent_status to avoid duplicate updates", () => {
            // Setup: Command with same logs and same status as last sent
            const cmd = mockCommands[0];
            const logs = "Command logs";

            jest.spyOn(cmd, "name").mockReturnValue("git clone");
            jest.spyOn(cmd, "output").mockReturnValue(logs);
            jest.spyOn(cmd, "last_sent_output_length").mockReturnValue(logs.length);
            jest.spyOn(cmd, "status").mockReturnValue(CommandStatus.SUCCESS);
            jest.spyOn(cmd, "last_sent_status").mockReturnValue(CommandStatus.SUCCESS);
            jest.spyOn(cmd, "increment_sequence_id");

            // Execute
            const result = initCommands.workspace_setup_status();

            // Verify no steps were added (no changes)
            expect(result.steps.length).toBe(0);

            // Verify sequence ID was not incremented
            expect(cmd.increment_sequence_id).not.toHaveBeenCalled();
        });
    });
});
