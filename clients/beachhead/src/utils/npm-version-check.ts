export const PACKAGE_NAME = "@augmentcode/auggie";

export interface VersionCheckResult {
    latestVersion: string | null;
    error?: string;
}

export interface DeprecationCheckResult {
    deprecated: boolean;
    message?: string;
    error?: string;
}

interface NpmPackageInfo {
    version: string;
    deprecated?: string;
}

interface NpmRegistryResponse {
    "dist-tags": {
        latest: string;
        [key: string]: string;
    };
    versions: {
        [version: string]: NpmPackageInfo;
    };
}

/**
 * Fetches the latest version of a package from npm registry.
 * @param packageName The npm package name to check
 * @returns The latest version or null if unable to fetch
 */
export async function fetchLatestNpmVersion(packageName: string): Promise<VersionCheckResult> {
    try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

        const response = await fetch(
            `https://registry.npmjs.org/${encodeURIComponent(packageName)}/latest`,
            {
                signal: controller.signal,
                headers: {
                    Accept: "application/json",
                },
            }
        );

        clearTimeout(timeoutId);

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = (await response.json()) as NpmPackageInfo;

        return {
            latestVersion: data.version,
        };
    } catch (error) {
        // Return null if we can't fetch the version (offline, npm down, etc.)
        let errorMessage = "Unable to fetch latest version";
        if (error instanceof Error) {
            if (error.name === "AbortError") {
                errorMessage = "Request timeout";
            } else {
                errorMessage = error.message;
            }
        }

        return {
            latestVersion: null,
            error: errorMessage,
        };
    }
}

/**
 * Compares two semver version strings.
 * @param current Current version string
 * @param latest Latest version string
 * @returns true if latest is newer than current
 */
export function updateAvailable(current: string, latest: string): boolean {
    // Remove any git info from the current version (e.g., "0.1.0 (commit abc123)")
    const cleanCurrent = current.split(" ")[0];

    // Split versions into parts
    const currentParts = cleanCurrent.split(".").map(Number);
    const latestParts = latest.split(".").map(Number);

    // Compare major, minor, patch
    for (let i = 0; i < Math.max(currentParts.length, latestParts.length); i++) {
        const currentPart = currentParts[i] || 0;
        const latestPart = latestParts[i] || 0;

        if (latestPart > currentPart) {
            return true;
        }
        if (latestPart < currentPart) {
            return false;
        }
    }

    return false; // Versions are equal
}

/**
 * Checks if a specific version of a package is deprecated.
 * @param packageName The npm package name
 * @param version The version to check (optional, checks latest if not provided)
 * @returns Deprecation status and message
 */
export async function checkDeprecation(
    packageName: string,
    version?: string
): Promise<DeprecationCheckResult> {
    try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

        // If checking specific version, use the version endpoint
        // Otherwise, fetch full package data to check latest version
        const url = version
            ? `https://registry.npmjs.org/${encodeURIComponent(packageName)}/${encodeURIComponent(version)}`
            : `https://registry.npmjs.org/${encodeURIComponent(packageName)}`;

        const response = await fetch(url, {
            signal: controller.signal,
            headers: {
                Accept: "application/json",
            },
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
            if (response.status === 404) {
                throw new Error(
                    `Package or version not found: ${packageName}${version ? `@${version}` : ""}`
                );
            }
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();

        let deprecated: string | undefined;

        if (version) {
            // Specific version check
            deprecated = (data as NpmPackageInfo).deprecated;
        } else {
            // Check latest version
            const registryData = data as NpmRegistryResponse;
            const latestVersion = registryData["dist-tags"].latest;
            deprecated = registryData.versions[latestVersion]?.deprecated;
        }

        return {
            deprecated: !!deprecated,
            message: deprecated,
        };
    } catch (error) {
        let errorMessage = "Unable to check deprecation status";
        if (error instanceof Error) {
            if (error.name === "AbortError") {
                errorMessage = "Request timeout";
            } else {
                errorMessage = error.message;
            }
        }

        return {
            deprecated: false,
            error: errorMessage,
        };
    }
}

/**
 * Checks if there's a newer version of @augmentcode/auggie available.
 * @param currentVersion The current version of the application
 * @returns Object indicating if update is available, deprecation status, and the latest version
 */
export async function checkForUpdate(currentVersion: string): Promise<{
    updateAvailable: boolean;
    latestVersion: string | null;
    currentVersionDeprecated?: boolean;
    deprecationMessage?: string;
    error?: string;
}> {
    // Fetch latest version
    const versionResult = await fetchLatestNpmVersion(PACKAGE_NAME);

    if (!versionResult.latestVersion) {
        return {
            updateAvailable: false,
            latestVersion: null,
            error: versionResult.error,
        };
    }

    // Check if update is available
    const isUpdateAvailable = updateAvailable(currentVersion, versionResult.latestVersion);

    // Check if current version is deprecated
    // Extract clean version without git info
    const cleanCurrentVersion = currentVersion.split(" ")[0];
    const deprecationResult = await checkDeprecation(PACKAGE_NAME, cleanCurrentVersion);

    return {
        updateAvailable: isUpdateAvailable,
        latestVersion: versionResult.latestVersion,
        currentVersionDeprecated: deprecationResult.deprecated,
        deprecationMessage: deprecationResult.message,
    };
}
