import * as crypto from "crypto";
import * as fs from "fs";
import * as fsPromises from "fs/promises";

import { FileType, StatInfo } from "./types";

function _fileTypeFromDirEnt(dirEnt: fs.Dirent): FileType {
    if (dirEnt.isFile()) {
        return FileType.file;
    }
    if (dirEnt.isDirectory()) {
        return FileType.directory;
    }
    return FileType.other;
}

function _fileTypeFromStat(st: fs.Stats): FileType {
    if (st.isFile()) {
        return FileType.file;
    }
    if (st.isDirectory()) {
        return FileType.directory;
    }
    return FileType.other;
}

function makeStatInfo(st: fs.Stats): StatInfo {
    // st.mtimeMs gives a fractional number of milliseconds (that is, potentially
    // finer-than-ms precision). We floor it to get a whole number.
    return {
        size: st.size,
        type: _fileTypeFromStat(st),
        mtime: Math.floor(st.mtimeMs),
    };
}

/**
 * The functions below are the filesystem interface used by the extension.
 */

export async function statFile(pathName: string): Promise<StatInfo> {
    // Use lstat instead of stat. If pathName is a symlink then the former reports on the link
    // itself (which is what we want), while the latter reports on its target. If pathName is not
    // a symlink then the two functions are identical.
    const st = await fsPromises.lstat(pathName);
    return makeStatInfo(st);
}

/**
 * This function returns if file path is readable.
 *
 * @param pathName
 * @returns true if file exists and no error occurs while accessing, false otherwise
 *
 */
export async function fileIsReadable(pathName: string): Promise<boolean> {
    try {
        await fsPromises.access(pathName);
        return true;
    } catch {
        return false;
    }
}

/**
 * This function indicates whether the given path name exists and refers to a plain file.
 *
 * @param pathName
 * @returns true if the given path points to a plain file, or false if the path does not exist
 * or is not a plain file.
 */
export function fileExists(pathName: string): boolean {
    try {
        const statInfo = statFileSync(pathName);
        return statInfo.type === FileType.file;
    } catch {
        return false;
    }
}

/**
 * This function indicates whether the given path name exists and refers to a directory.
 *
 * @param pathName
 * @returns true if the given path points to a directory, or false if the path does not exist
 * or is not a directory.
 */
export async function directoryExistsAsync(pathName: string): Promise<boolean> {
    try {
        const st = await fsPromises.stat(pathName);
        return st.isDirectory();
    } catch {
        return false;
    }
}

export function statFileSync(pathName: string): StatInfo {
    // Use lstatSync instead of statSync. If pathName is a symlink then the former reports on the
    // link itself (which is what we want), while the latter reports on its target. If pathName is
    // not a symlink then the two functions are identical.
    const st = fs.lstatSync(pathName);
    return makeStatInfo(st);
}

export async function readDirectory(pathName: string): Promise<[string, FileType][]> {
    const result: [string, FileType][] = [];

    const files = await fsPromises.readdir(pathName, { withFileTypes: true });
    for (const dirEnt of files) {
        result.push([dirEnt.name, _fileTypeFromDirEnt(dirEnt)]);
    }
    return result;
}

export function readDirectorySync(pathName: string): [string, FileType][] {
    const result: [string, FileType][] = [];

    const files = fs.readdirSync(pathName, { withFileTypes: true });
    for (const dirEnt of files) {
        result.push([dirEnt.name, _fileTypeFromDirEnt(dirEnt)]);
    }
    return result;
}

export async function makeDirs(pathName: string): Promise<void> {
    await fsPromises.mkdir(pathName, { recursive: true });
}

export function makeDirsSync(pathName: string): void {
    fs.mkdirSync(pathName, { recursive: true });
}

export async function rename(oldPath: string, newPath: string): Promise<void> {
    await fsPromises.rename(oldPath, newPath);
}

export async function readFileRaw(pathName: string): Promise<Buffer> {
    return await fsPromises.readFile(pathName);
}

/**
 * Write atomically to the file at the path
 *
 * As a side effect, a temporary file is created while the write is in progress.
 * The temporary file is renamed to the target path when the write is complete.
 * If the write fails, the temporary file is deleted.
 *
 * @param pathName path to write to
 * @param contents contents to write
 * @returns
 */
export async function writeFileRaw(pathName: string, contents: Uint8Array): Promise<void> {
    // We create a temporary file so that if writing is interrupted, the given
    // file path does not contain a corrupted file.
    const tmpPath = `${pathName}.${crypto.randomBytes(8).toString("hex")}.tmp`;
    try {
        // Write to a temporary file so that if it fails the original file is not corrupted
        // and the temporary fily is deleted.
        await fsPromises.writeFile(tmpPath, contents);
        await fsPromises.rename(tmpPath, pathName);
    } catch (error) {
        await fsPromises.unlink(tmpPath);
        throw error;
    }
    return;
}

export async function readFileUtf8(pathName: string): Promise<string> {
    return await fsPromises.readFile(pathName, { encoding: "utf8" });
}

export async function writeFileUtf8(pathName: string, text: string): Promise<void> {
    return await fsPromises.writeFile(pathName, text, { encoding: "utf8" });
}

// use this if you want to write something on dispose. otherwise opt for async.
export function writeFileUtf8Sync(pathName: string, text: string): void {
    return fs.writeFileSync(pathName, text, { encoding: "utf8" });
}

export async function deleteFile(pathName: string): Promise<void> {
    return await fsPromises.unlink(pathName);
}
