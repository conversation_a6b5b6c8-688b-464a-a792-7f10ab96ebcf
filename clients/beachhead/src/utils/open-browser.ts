import { exec } from "child_process";

/**
 * Opens a URL in the default browser
 */
export function openBrowser(url: string): void {
    const platform = process.platform;
    let command: string;

    switch (platform) {
        case "darwin": // macOS
            command = `open "${url}"`;
            break;
        case "win32": // Windows
            command = `start "" "${url}"`;
            break;
        default: // Linux and others
            command = `xdg-open "${url}"`;
            break;
    }

    exec(command, (error) => {
        if (error) {
            // Silently fail - browser opening is a convenience feature
            // The user can still copy/paste the URL manually
        }
    });
}
