import { exec } from "child_process";

import { getLogger } from "../logging";

/**
 * Checks if a Docker image is available locally.
 * @param imageName The name of the Docker image to check
 * @returns A promise that resolves to true if the image is available, false otherwise
 */
export async function isDockerImageAvailable(imageName: string): Promise<boolean> {
    return new Promise((resolve) => {
        exec(`docker inspect ${imageName}`, (error) => {
            // If there's no error, the image exists
            if (!error) {
                resolve(true);
            } else {
                // Docker inspect returns non-zero exit code if the image doesn't exist
                resolve(false);
            }
        });
    });
}

/**
 * Waits for a Docker image to become available, with a timeout.
 * @param imageName The name of the Docker image to wait for
 * @param timeoutMs Maximum time to wait in milliseconds
 * @param intervalMs Interval between checks in milliseconds (default: 1000 = 1 second)
 * @returns A promise that resolves when the image is available, or rejects if the timeout is reached
 */
export async function waitForDockerImage(
    imageName: string,
    timeoutMs: number,
    intervalMs: number = 1000
): Promise<void> {
    const logger = getLogger("DockerUtils");
    const startTime = process.hrtime();

    // Helper function to get elapsed time in milliseconds using hrtime
    const getElapsedMs = (): number => {
        const diff = process.hrtime(startTime);
        return diff[0] * 1000 + diff[1] / 1000000;
    };

    while (getElapsedMs() < timeoutMs) {
        logger.info(`Checking for Docker image: ${imageName}`);

        if (await isDockerImageAvailable(imageName)) {
            logger.info(`Docker image ${imageName} is available`);
            return;
        }

        const elapsedSeconds = Math.floor(getElapsedMs() / 1000);
        const remainingSeconds = Math.floor(timeoutMs / 1000) - elapsedSeconds;
        logger.info(
            `Image not available. Elapsed: ${elapsedSeconds}s, remaining: ${remainingSeconds}s`
        );
        await new Promise((resolve) => setTimeout(resolve, intervalMs));
    }

    throw new Error(`Docker image ${imageName} not available after ${timeoutMs / 1000}s`);
}
