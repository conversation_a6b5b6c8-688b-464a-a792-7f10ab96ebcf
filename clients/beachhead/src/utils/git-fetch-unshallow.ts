import { spawn } from "child_process";

import { getLogger } from "../logging";

/**
 * Git fetch --unshallow utility for converting shallow clones to full repositories
 */
export class GitFetchUnshallow {
    private _workspaceRoot: string;
    private _fetchPromise: Promise<void> | null = null;
    private _logger = getLogger("GitFetchUnshallow");

    constructor(workspaceRoot: string) {
        this._workspaceRoot = workspaceRoot;
    }

    /**
     * Start git fetch --unshallow in the background
     * @returns Promise that resolves when the fetch is complete
     */
    public startFetch(): Promise<void> {
        if (this._fetchPromise) {
            return this._fetchPromise;
        }

        this._fetchPromise = this._performFetch();
        return this._fetchPromise;
    }

    /**
     * Wait for git fetch --unshallow to complete
     * @returns Promise that resolves when the fetch is complete
     */
    public async awaitFetchComplete(): Promise<void> {
        if (this._fetchPromise) {
            await this._fetchPromise;
        }
    }

    private async _performFetch(): Promise<void> {
        const startTime = Date.now();

        try {
            // First check if the repository is shallow
            const isShallow = await this._isShallowRepository();
            if (!isShallow) {
                this._logger.debug("Repository is not shallow, skipping git fetch --unshallow");
                return;
            }

            this._logger.info(
                "Starting git fetch --unshallow to convert shallow clone to full repository"
            );

            await this._spawnGitFetch();

            const duration = Date.now() - startTime;
            this._logger.info(`Git fetch --unshallow completed in ${duration}ms`);
        } catch (error) {
            const duration = Date.now() - startTime;
            this._logger.warn(
                `Git fetch --unshallow failed after ${duration}ms: ${
                    error instanceof Error ? error.message : String(error)
                }`
            );
            // Don't throw the error - we want to continue even if fetch fails
        }
    }

    private async _isShallowRepository(): Promise<boolean> {
        return new Promise((resolve) => {
            const proc = spawn(
                "git",
                ["-C", this._workspaceRoot, "rev-parse", "--is-shallow-repository"],
                {
                    stdio: ["ignore", "pipe", "pipe"],
                }
            );

            let output = "";
            proc.stdout?.on("data", (data: Buffer) => {
                output += data.toString();
            });

            proc.on("close", (code) => {
                if (code === 0) {
                    // Command succeeded, check output
                    resolve(output.trim() === "true");
                } else {
                    // Command failed, assume not shallow
                    resolve(false);
                }
            });

            proc.on("error", () => {
                // Error running command, assume not shallow
                resolve(false);
            });
        });
    }

    private async _spawnGitFetch(): Promise<void> {
        return new Promise<void>((resolve, reject) => {
            const proc = spawn("git", ["-C", this._workspaceRoot, "fetch", "--unshallow"], {
                stdio: ["ignore", "pipe", "pipe"],
            });

            proc.on("error", (err: Error) => {
                reject(new Error(`Failed to start git fetch --unshallow: ${err.message}`));
            });

            let stdout = "";
            let stderr = "";

            proc.stdout?.on("data", (data: Buffer) => {
                stdout += data.toString();
            });

            proc.stderr?.on("data", (data: Buffer) => {
                stderr += data.toString();
            });

            proc.on("close", (code: number) => {
                if (code === 0) {
                    resolve();
                } else {
                    reject(
                        new Error(
                            `Git fetch --unshallow exited with code ${code}: ${stderr || stdout}`
                        )
                    );
                }
            });
        });
    }
}
