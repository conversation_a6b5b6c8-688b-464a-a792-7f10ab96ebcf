import ignore, { Ignore } from "../../../../third_party/node-ignore";
import * as vscode from "../vscode";
import { readFileUtf8 } from "./fs-utils";
import { AcceptPath, PathAcceptance, RejectPath } from "./path-acceptance";
import { directoryContainsPath, dirNameUri, relativeDirName } from "./path-utils";
import { FileType } from "./types";
import { uriToAbsPath, uriToDisplayablePath } from "./uri";

// IgnoreRuleAcceptance is an interface for indicating whether a path name is accepted by ignore
// rules.
interface IgnoreRuleAcceptance extends PathAcceptance {
    explicit: boolean;
}

// AcceptPathImplicit indicates a path that was implicitly accepted because no ignore rules
// apply to it.
export class AcceptPathImplicit extends AcceptPath implements IgnoreRuleAcceptance {
    public readonly explicit = false;
    public format(): string {
        return "Tracked";
    }
}

// AcceptPathExplicit indicates a path that was explicitly accepted by an ignore rule.
export class AcceptPathExplicit extends AcceptPath implements IgnoreRuleAcceptance {
    public readonly explicit = true;
    constructor(public readonly ignoreSourceName: string) {
        super();
    }

    public format(): string {
        return `Tracked (${this.ignoreSourceName})`;
    }
}

// RejectPathExplicit indicates a path that was explicitly rejected by an ignore rule.
export class RejectPathExplicit extends RejectPath implements IgnoreRuleAcceptance {
    public readonly explicit = true;
    constructor(public readonly ignoreSourceName: string) {
        super();
    }

    public format(): string {
        return `Not tracked (${this.ignoreSourceName})`;
    }
}

// IgnoreStack is an interface for a stack of ignore files that interact with one another.
export interface IgnoreStack {
    // getPathInfo indicates whether the given path is accepted by the IgnoreStack
    getPathInfo(candidatePath: string): IgnoreRuleAcceptance;

    // buildAtop returns a new IgnoreStack that is the result of placing any ignore files
    // in dirUri on top of this IgnoreStack. The current IgnoreStack is not modified.
    // The optional dirEntries is a list of [name, type] pairs indicating the contents of the
    // directory. It is an optimization to help the stack avoid trying to read files that
    // don't exist, which turns out to be surprisingly expensive.
    buildAtop(dirUri: vscode.Uri, dirEntries?: Array<[string, FileType]>): Promise<IgnoreStack>;
}

class IgnoreStackEntry {
    constructor(
        public readonly dirName: string,
        public readonly ignoreSource: IgnoreSource,
        public readonly rules: Ignore,
        public readonly next: IgnoreStackEntry | undefined
    ) {}
}

// SingleIgnoreStack is a stack of ignore files of a single type (eg. just gitignore or just
// augmentignore). The ignore file name (presumably ".gitignore" or ".augmentignore") is a
// parameter to the constructor.
class SingleIgnoreStack implements IgnoreStack {
    private constructor(
        private readonly _rootUri: vscode.Uri,
        private readonly _ignoreSource: IgnoreSource,
        private readonly _top: IgnoreStackEntry | undefined
    ) {}

    public static async buildNew(
        ignoreSource: IgnoreSource,
        startUri: vscode.Uri,
        rootUri: vscode.Uri
    ): Promise<SingleIgnoreStack> {
        const stack = new Array<[string, IgnoreSource, Ignore]>();
        if (relativeDirName(rootUri, startUri) !== "") {
            let currDirUri = startUri;
            while (true) {
                currDirUri = dirNameUri(currDirUri);
                const relativeDirPath = relativeDirName(rootUri, currDirUri);
                const rules = await ignoreSource.getRules(currDirUri);
                if (rules) {
                    stack.push([relativeDirPath, ignoreSource, rules]);
                }
                if (relativeDirPath === "") {
                    break;
                }
            }
        }
        let top: IgnoreStackEntry | undefined = undefined;
        for (let idx = stack.length - 1; idx >= 0; idx--) {
            const [dirName, ignoreSource, rules] = stack[idx];
            top = new IgnoreStackEntry(dirName, ignoreSource, rules, top);
        }
        return new SingleIgnoreStack(rootUri, ignoreSource, top);
    }

    // IgnoreStack
    public async buildAtop(
        dirUri: vscode.Uri,
        dirEntries?: Array<[string, FileType]>
    ): Promise<IgnoreStack> {
        const rules = await this._ignoreSource.getRules(dirUri, dirEntries);
        if (rules === undefined) {
            return this;
        }

        const relDirPath = relativeDirName(this._rootUri, dirUri);
        const newTop = new IgnoreStackEntry(relDirPath, this._ignoreSource, rules, this._top);
        return new SingleIgnoreStack(this._rootUri, this._ignoreSource, newTop);
    }

    // IgnoreStack
    public getPathInfo(candidatePath: string): IgnoreRuleAcceptance {
        for (let curr = this._top; curr !== undefined; curr = curr.next) {
            if (!directoryContainsPath(curr.dirName, candidatePath)) {
                throw new Error(
                    `candidatePath "${candidatePath}" is not below ` +
                        `ignore file's parent "${curr.dirName}"`
                );
            }

            // Get the portion of the candidate path that is relative to the current ignore file's
            // directory. This is needed for ignore file rules that apply only to the current
            // directory. For example, the rule "abc/xyz" rejects only "./abc/xyz", not a "abc/xyz"
            // found elsewhere in the tree.
            const relCandidatePath = candidatePath.slice(curr.dirName.length);
            const result = curr.rules.test(relCandidatePath);
            if (result.ignored) {
                // The path was rejected. Stop here and reject without consulting
                // any higher-level ignore files. Rejection here trumps anything
                // they might say.
                return new RejectPathExplicit(
                    curr.ignoreSource.getName(vscode.Utils.joinPath(this._rootUri, curr.dirName))
                );
            }
            if (result.unignored) {
                // The path was _explicitly_ accepted. Stop here and accept without
                // consulting any higher-level ignore files. Explicit acceptance
                // here trumps anything they might say.
                return new AcceptPathExplicit(
                    curr.ignoreSource.getName(vscode.Utils.joinPath(this._rootUri, curr.dirName))
                );
            }
            // The path was _implicitly_ accepted. Keep going to see what higher-
            // level ignore files have to say about it.
        }

        // Paths that are not explicitly rejected are accepted.
        return new AcceptPathImplicit();
    }
}

// MultiIgnoreStack manages a collection of SingleIgnoreStacks, where one stack can override
// another (eg. augmentignore overrides gitignore). The list of ignore file names is a
// parameter to the constructor, where names later in the list override names earlier in the
// list.
export class MultiIgnoreStack implements IgnoreStack {
    constructor(
        private readonly _ignoreSources: Array<IgnoreSource>,
        private readonly _ignoreStacks: IgnoreStack[]
    ) {}

    public static async buildNew(
        ignoreSources: IgnoreSource[],
        startUri: vscode.Uri,
        rootUri: vscode.Uri
    ): Promise<MultiIgnoreStack> {
        const ignoreStacks = new Array<IgnoreStack>();
        for (const ignoreSource of ignoreSources) {
            const ignoreStack = await SingleIgnoreStack.buildNew(ignoreSource, startUri, rootUri);
            ignoreStacks.push(ignoreStack);
        }
        return new MultiIgnoreStack(ignoreSources, ignoreStacks);
    }

    // IgnoreStack
    public async buildAtop(
        dirUri: vscode.Uri,
        dirEntries?: Array<[string, FileType]>
    ): Promise<IgnoreStack> {
        const ignoreStacks = new Array<IgnoreStack>();
        let diffCount = 0;
        for (let idx = 0; idx < this._ignoreStacks.length; idx++) {
            const base = this._ignoreStacks[idx];
            const ignoreStack = await base.buildAtop(dirUri, dirEntries);
            if (ignoreStack !== base) {
                diffCount++;
            }
            ignoreStacks.push(ignoreStack);
        }

        if (diffCount === 0) {
            // No new ignore files were found. Just keep using this same multi-stack.
            return this;
        }
        return new MultiIgnoreStack(this._ignoreSources, ignoreStacks);
    }

    // IgnoreStack
    public getPathInfo(candidatePath: string): IgnoreRuleAcceptance {
        for (let idx = this._ignoreStacks.length - 1; idx >= 0; idx--) {
            const ignoreStack = this._ignoreStacks[idx];
            const result = ignoreStack.getPathInfo(candidatePath);
            if (result.explicit) {
                return result;
            }
        }
        return new AcceptPathImplicit();
    }
}

// readIgnoreFile returns the rules found in `dirPath/ignoreFileName` if it exists. Undefined
// otherwise.
async function readIgnoreFile(
    dirPath: vscode.Uri,
    ignoreFileName: string
): Promise<Ignore | undefined> {
    const ignoreUri = vscode.Utils.joinPath(dirPath, ignoreFileName);
    try {
        const content = await readFileUtf8(ignoreUri.fsPath);
        const rules = ignore({ ignorecase: false });
        rules.add(content);
        return rules;
    } catch {}

    return undefined;
}

// IgnoreStackBuilder is a class for building (Multi)IgnoreStacks.
export class IgnoreStackBuilder {
    constructor(private readonly _ignoreSources: IgnoreSource[] = []) {}

    public async build(startUri: vscode.Uri, rootUri: vscode.Uri): Promise<IgnoreStack> {
        const ignoreStack = await MultiIgnoreStack.buildNew(this._ignoreSources, startUri, rootUri);
        return ignoreStack;
    }
}

export interface IgnoreSource {
    getRules(
        currDirUri: vscode.Uri,
        dirEntries?: Array<[string, FileType]>
    ): Promise<Ignore | undefined>;
    getName(prefix: vscode.Uri): string;
}

export class IgnoreSourceFile implements IgnoreSource {
    constructor(public readonly filename: string) {}

    getName(prefix: vscode.Uri): string {
        return uriToDisplayablePath(vscode.Utils.joinPath(prefix, this.filename));
    }

    public async getRules(
        currDirUri: vscode.Uri,
        dirEntries?: Array<[string, FileType]>
    ): Promise<Ignore | undefined> {
        // we do this optimization to check if the ignore file is present in our
        // list of files so that we don't query the filesystem repeatedly since
        // that is slower than just checking an in-memory list.
        if (dirEntries !== undefined) {
            const ignoreFilePresent =
                dirEntries.find(
                    ([name, type]) => type === FileType.file && this.filename === name
                ) !== undefined;
            if (!ignoreFilePresent) {
                return undefined;
            }
        }
        return readIgnoreFile(currDirUri, this.filename);
    }
}

export class IgnoreSourceBuiltin implements IgnoreSource {
    constructor(private readonly _sourceFolderRootPath: string) {}

    getName(): string {
        return "default Augment rules";
    }

    getRules(_currDirUri: vscode.Uri): Promise<Ignore | undefined> {
        return new Promise((resolve) => {
            if (uriToAbsPath(_currDirUri) !== this._sourceFolderRootPath) {
                resolve(undefined);
            } else {
                const rules = ignore({ ignorecase: false });
                rules.add([
                    ".git",
                    "*.pem",
                    "*.key",
                    "*.pfx",
                    "*.p12",
                    "*.jks",
                    "*.keystore",
                    "*.pkcs12",
                    "*.crt",
                    "*.cer",
                    "id_rsa",
                    "id_ed25519",
                    "id_ecdsa",
                    "id_dsa",
                    ".augment-guidelines",
                ]);
                resolve(rules);
            }
        });
    }
}
