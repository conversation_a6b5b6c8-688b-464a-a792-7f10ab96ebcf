import { getBaseVersion, getVersionWithGitInfo } from "../version";

describe("version utilities", () => {
    describe("getBaseVersion", () => {
        it("should return a valid semver version", () => {
            const baseVersion = getBaseVersion();
            expect(typeof baseVersion).toBe("string");
            expect(baseVersion).toMatch(/^\d+\.\d+\.\d+$/);
        });
    });

    describe("getVersionWithGitInfo", () => {
        it("should return a version string", () => {
            const version = getVersionWithGitInfo();
            expect(typeof version).toBe("string");
            expect(version.length).toBeGreaterThan(0);
        });

        it("should include the base version", () => {
            const baseVersion = getBaseVersion();
            const version = getVersionWithGitInfo();
            expect(version).toMatch(new RegExp(`^${baseVersion.replace(/\./g, "\\.")}`));
        });

        it("should include git info when in a git repository", () => {
            const baseVersion = getBaseVersion();
            const version = getVersionWithGitInfo();
            // Should either be just the base version (if not in git repo)
            // or include git commit hash in the new format (if in git repo)
            const escapedBaseVersion = baseVersion.replace(/\./g, "\\.");
            expect(version).toMatch(
                new RegExp(`^${escapedBaseVersion}( \\(commit [a-f0-9]+( dirty)?\\))?$`)
            );
        });
    });
});
