import { updateAvailable, checkForUpdate, fetchLatestNpmVersion, checkDeprecation } from "../npm-version-check";

// Mock global fetch
global.fetch = jest.fn();

describe("npm-version-check", () => {
    const mockFetch = global.fetch as jest.MockedFunction<typeof global.fetch>;

    beforeEach(() => {
        jest.clearAllMocks();
        jest.useFakeTimers();
        // Reset fetch mock
        mockFetch.mockReset();
    });

    afterEach(() => {
        jest.useRealTimers();
        // Clear any pending timers to prevent Jest warnings
        jest.clearAllTimers();
    });

    describe("fetchLatestNpmVersion", () => {
        it("should return the latest version when API call succeeds", async () => {
            mockFetch.mockResolvedValueOnce({
                ok: true,
                json: async () => ({ version: "1.2.3" }),
            } as Response);
            
            const result = await fetchLatestNpmVersion("@augmentcode/auggie");
            
            expect(result.latestVersion).toBe("1.2.3");
            expect(result.error).toBeUndefined();
            expect(mockFetch).toHaveBeenCalledWith(
                "https://registry.npmjs.org/%40augmentcode%2Fauggie/latest",
                expect.objectContaining({
                    headers: { "Accept": "application/json" },
                    signal: expect.any(AbortSignal)
                })
            );
        });

        it("should return null when API call fails", async () => {
            mockFetch.mockRejectedValueOnce(new Error("Network error"));
            
            const result = await fetchLatestNpmVersion("@augmentcode/auggie");
            
            expect(result.latestVersion).toBeNull();
            expect(result.error).toBe("Network error");
        });

        it("should handle HTTP errors", async () => {
            mockFetch.mockResolvedValueOnce({
                ok: false,
                status: 500,
                statusText: "Internal Server Error",
            } as Response);
            
            const result = await fetchLatestNpmVersion("@augmentcode/auggie");
            
            expect(result.latestVersion).toBeNull();
            expect(result.error).toBe("HTTP 500: Internal Server Error");
        });

        it("should handle package not found (404)", async () => {
            mockFetch.mockResolvedValueOnce({
                ok: false,
                status: 404,
                statusText: "Not Found",
            } as Response);
            
            const result = await fetchLatestNpmVersion("@augmentcode/non-existent-package");
            
            expect(result.latestVersion).toBeNull();
            expect(result.error).toBe("HTTP 404: Not Found");
            expect(mockFetch).toHaveBeenCalledWith(
                "https://registry.npmjs.org/%40augmentcode%2Fnon-existent-package/latest",
                expect.any(Object)
            );
        });
    });

    describe("updateAvailable", () => {
        it("should correctly compare semver versions", () => {
            expect(updateAvailable("1.0.0", "2.0.0")).toBe(true);
            expect(updateAvailable("1.0.0", "1.1.0")).toBe(true);
            expect(updateAvailable("1.0.0", "1.0.1")).toBe(true);
            expect(updateAvailable("2.0.0", "1.0.0")).toBe(false);
            expect(updateAvailable("1.1.0", "1.0.0")).toBe(false);
            expect(updateAvailable("1.0.1", "1.0.0")).toBe(false);
            expect(updateAvailable("1.0.0", "1.0.0")).toBe(false);
        });

        it("should handle versions with git info", () => {
            expect(updateAvailable("1.0.0 (commit abc123)", "1.0.1")).toBe(true);
            expect(updateAvailable("1.0.0 (commit abc123 dirty)", "1.0.0")).toBe(false);
        });

        it("should handle versions with different number of parts", () => {
            expect(updateAvailable("1.0", "1.0.1")).toBe(true);
            expect(updateAvailable("1.0.0", "1.1")).toBe(true);
            expect(updateAvailable("1", "2.0.0")).toBe(true);
        });
    });

    describe("checkForUpdate", () => {
        it("should return updateAvailable true when newer version exists", async () => {
            mockFetch
                .mockResolvedValueOnce({
                    ok: true,
                    json: async () => ({ version: "2.0.0" }),
                } as Response)
                .mockResolvedValueOnce({
                    ok: true,
                    json: async () => ({ version: "1.0.0" }),
                } as Response);
            
            const result = await checkForUpdate("1.0.0");
            
            expect(result.updateAvailable).toBe(true);
            expect(result.latestVersion).toBe("2.0.0");
            expect(result.error).toBeUndefined();
        });

        it("should return updateAvailable false when current version is latest", async () => {
            mockFetch
                .mockResolvedValueOnce({
                    ok: true,
                    json: async () => ({ version: "1.0.0" }),
                } as Response)
                .mockResolvedValueOnce({
                    ok: true,
                    json: async () => ({ version: "1.0.0" }),
                } as Response);
            
            const result = await checkForUpdate("1.0.0");
            
            expect(result.updateAvailable).toBe(false);
            expect(result.latestVersion).toBe("1.0.0");
        });

        it("should return updateAvailable false when API call fails", async () => {
            mockFetch.mockRejectedValueOnce(new Error("Offline"));
            
            const result = await checkForUpdate("1.0.0");
            
            expect(result.updateAvailable).toBe(false);
            expect(result.latestVersion).toBeNull();
            expect(result.error).toBe("Offline");
        });

        it("should handle package not found gracefully", async () => {
            mockFetch.mockResolvedValueOnce({
                ok: false,
                status: 404,
                statusText: "Not Found",
            } as Response);
            
            const result = await checkForUpdate("1.0.0");
            
            expect(result.updateAvailable).toBe(false);
            expect(result.latestVersion).toBeNull();
            expect(result.error).toBe("HTTP 404: Not Found");
            expect(result.currentVersionDeprecated).toBeUndefined();
            expect(result.deprecationMessage).toBeUndefined();
        });
    });

    describe("checkDeprecation", () => {
        it("should return deprecated true when package version is deprecated", async () => {
            mockFetch.mockResolvedValueOnce({
                ok: true,
                json: async () => ({ 
                    version: "1.0.0",
                    deprecated: "This version has been deprecated" 
                }),
            } as Response);
            
            const result = await checkDeprecation("@augmentcode/auggie", "1.0.0");
            
            expect(result.deprecated).toBe(true);
            expect(result.message).toBe("This version has been deprecated");
            expect(mockFetch).toHaveBeenCalledWith(
                "https://registry.npmjs.org/%40augmentcode%2Fauggie/1.0.0",
                expect.objectContaining({
                    headers: { "Accept": "application/json" },
                    signal: expect.any(AbortSignal)
                })
            );
        });

        it("should return deprecated false when package version is not deprecated", async () => {
            mockFetch.mockResolvedValueOnce({
                ok: true,
                json: async () => ({ version: "1.0.0" }),
            } as Response);
            
            const result = await checkDeprecation("@augmentcode/auggie", "1.0.0");
            
            expect(result.deprecated).toBe(false);
            expect(result.message).toBeUndefined();
        });

        it("should check latest version when no version provided", async () => {
            mockFetch.mockResolvedValueOnce({
                ok: true,
                json: async () => ({ 
                    "dist-tags": { latest: "2.0.0" },
                    versions: {
                        "2.0.0": { deprecated: "Package has been deprecated" }
                    }
                }),
            } as Response);
            
            const result = await checkDeprecation("some-package");
            
            expect(result.deprecated).toBe(true);
            expect(result.message).toBe("Package has been deprecated");
            expect(mockFetch).toHaveBeenCalledWith(
                "https://registry.npmjs.org/some-package",
                expect.any(Object)
            );
        });

        it("should handle 404 errors", async () => {
            mockFetch.mockResolvedValueOnce({
                ok: false,
                status: 404,
                statusText: "Not Found",
            } as Response);
            
            const result = await checkDeprecation("@augmentcode/auggie", "1.0.0");
            
            expect(result.deprecated).toBe(false);
            expect(result.error).toContain("Package or version not found");
        });

        it("should handle non-existent package gracefully", async () => {
            mockFetch.mockResolvedValueOnce({
                ok: false,
                status: 404,
                statusText: "Not Found",
            } as Response);
            
            const result = await checkDeprecation("@company/non-existent-package");
            
            expect(result.deprecated).toBe(false);
            expect(result.error).toBe("Package or version not found: @company/non-existent-package");
            expect(mockFetch).toHaveBeenCalledWith(
                "https://registry.npmjs.org/%40company%2Fnon-existent-package",
                expect.any(Object)
            );
        });

        it("should handle non-existent version of existing package", async () => {
            mockFetch.mockResolvedValueOnce({
                ok: false,
                status: 404,
                statusText: "Not Found",
            } as Response);
            
            const result = await checkDeprecation("@augmentcode/auggie", "99.99.99");
            
            expect(result.deprecated).toBe(false);
            expect(result.error).toBe("Package or version not found: @augmentcode/auggie@99.99.99");
            expect(mockFetch).toHaveBeenCalledWith(
                "https://registry.npmjs.org/%40augmentcode%2Fauggie/99.99.99",
                expect.any(Object)
            );
        });
    });

    describe("checkForUpdate with deprecation", () => {
        it("should include deprecation status in response", async () => {
            // Mock latest version check
            mockFetch
                .mockResolvedValueOnce({
                    ok: true,
                    json: async () => ({ version: "2.0.0" }),
                } as Response)
                // Mock deprecation check
                .mockResolvedValueOnce({
                    ok: true,
                    json: async () => ({ 
                        version: "1.0.0",
                        deprecated: "Current version deprecated" 
                    }),
                } as Response);
            
            const result = await checkForUpdate("1.0.0");
            
            expect(result.updateAvailable).toBe(true);
            expect(result.latestVersion).toBe("2.0.0");
            expect(result.currentVersionDeprecated).toBe(true);
            expect(result.deprecationMessage).toBe("Current version deprecated");
        });

        it("should handle non-deprecated current version", async () => {
            mockFetch
                .mockResolvedValueOnce({
                    ok: true,
                    json: async () => ({ version: "1.0.0" }),
                } as Response)
                // Mock deprecation check
                .mockResolvedValueOnce({
                    ok: true,
                    json: async () => ({ version: "1.0.0" }),
                } as Response);
            
            const result = await checkForUpdate("1.0.0");
            
            expect(result.updateAvailable).toBe(false);
            expect(result.currentVersionDeprecated).toBe(false);
            expect(result.deprecationMessage).toBeUndefined();
        });
    });
});