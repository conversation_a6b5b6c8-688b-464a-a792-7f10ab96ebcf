/**
 * Common range types.
 *
 * This is different from vscode.Range, which is a (line, character) range.
 *
 * These ranges are defined here to allow sharing with webviews.
 */

/**
 * A basic range type.
 *
 * This is defined as an interface to allow us to send the type over to a webview.
 */
export interface IRange {
    /** Inclusive start. */
    start: number;
    /** Exclusive stop. */
    stop: number;
}

/** An implementation of Range with utility methods. */
export class Range implements IRange {
    constructor(
        public start: number,
        public stop: number
    ) {}

    /** Check if this range is equal to another. */
    equals(other: IRange): boolean {
        return this.start === other.start && this.stop === other.stop;
    }

    get length(): number {
        return this.stop - this.start;
    }

    toString(): string {
        return `[${this.start}, ${this.stop})`;
    }

    /** Ranges are ordered by their start index. */
    compareTo(other: IRange): number {
        return this.start - other.start;
    }

    /** Check if this range contains an index or another range. */
    contains(other: IRange | number): boolean {
        if (typeof other === "number") {
            other = new Range(other, other + 1);
        }
        return this.start <= other.start && this.stop >= other.stop && this.stop > other.start;
    }

    /** Check if this range intersects with another with non-empty intersection. */
    intersects(other: IRange | number): boolean {
        if (typeof other === "number") {
            other = new Range(other, other + 1);
        }
        return this.start < other.stop && this.stop > other.start;
    }

    /** Check if this range shares a boundary with another. */
    touches(other: IRange | number): boolean {
        if (typeof other === "number") {
            other = new Range(other, other + 1);
        }
        return this.start === other.stop || this.stop === other.start;
    }

    /** Return the intersection of this range and another. */
    intersection(other: IRange): Range | undefined {
        if (this.intersects(other)) {
            return new Range(Math.max(this.start, other.start), Math.min(this.stop, other.stop));
        } else {
            return undefined;
        }
    }

    /** Return the distance between this range and another. */
    distanceTo(other: IRange | number): number {
        if (typeof other === "number") {
            other = new Range(other, other + 1);
        }
        // Logic taken from //base/ranges/range_types.py
        return -Math.min(0, Math.min(this.stop, other.stop) - Math.max(this.start, other.start));
    }

    /**
    Return a new range with the given offsets
    Always returns start >= min
    Always returns stop <= max
    Always returns start <= stop
    If the offsets make start > stop, returns the value in (min, max) closest to mean(start, stop), rounded up.
    */
    offset(startOffset: number, stopOffset: number, min = -Infinity, max = Infinity): Range {
        const newStart: number = Math.min(max, Math.max(min, this.start + startOffset));
        const newStop: number = Math.max(min, Math.min(max, this.stop + stopOffset));
        if (newStart > newStop) {
            // If the range is invalid, return the (validated) middle of the range.
            const middle: number = Math.ceil((this.start + this.stop) / 2);
            const newMiddle: number = Math.max(min, Math.min(max, middle));
            return new Range(newMiddle, newMiddle);
        }
        return new Range(newStart, newStop);
    }

    /** Check if any two ranges in the provided array overlap. */
    static anyOverlaps(ranges: IRange[]): boolean {
        if (ranges.length <= 1) {
            return false;
        }

        // Sort the ranges based on their start values
        ranges.sort((a, b) => (a.start === b.start ? a.stop - b.stop : a.start - b.start));

        let lastStop = ranges[0].stop;
        for (let i = 1; i < ranges.length; i++) {
            if (ranges[i].start < lastStop) {
                return true;
            }
            lastStop = ranges[i].stop;
        }

        return false;
    }

    /** Merge touching ranges into a list of non-touching ranges. */
    static mergeTouching(ranges: readonly Range[]): Range[] {
        if (ranges.length <= 1) {
            return [...ranges];
        }

        // Sort the ranges based on their start values
        const sortedRanges = [...ranges].sort((a, b) => a.start - b.start);

        const mergedRanges: Range[] = [sortedRanges[0]];
        for (let i = 1; i < sortedRanges.length; i++) {
            const currentRange = mergedRanges[mergedRanges.length - 1];
            const nextRange = sortedRanges[i];
            if (currentRange.stop >= nextRange.start) {
                currentRange.stop = Math.max(currentRange.stop, nextRange.stop);
            } else {
                mergedRanges.push(nextRange);
            }
        }

        return mergedRanges;
    }
}

/** Represents a range of characters in a file. */
export class CharRange extends Range {}
export type ICharRange = IRange;

/** Represents a range of lines in a file. */
export class LineRange extends Range {}
export type ILineRange = IRange;
