import { getLogger } from "./logging";
import { fileExists, readFileUtf8 } from "./utils/fs-utils";

/**
 * Reads GitHub API token from file or environment variable.
 * Priority order:
 * 1. File specified by tokenFilePath parameter (command line flag)
 * 2. GITHUB_API_TOKEN environment variable
 * 3. Empty string (fallback)
 */
export async function readGitHubApiToken(tokenFilePath?: string): Promise<string> {
    const logger = getLogger("Beachhead");

    // First check file path if provided (command line flag takes precedence)
    if (tokenFilePath) {
        try {
            if (fileExists(tokenFilePath)) {
                return (await readFileUtf8(tokenFilePath)).trim();
            }
        } catch (error) {
            logger.error("Failed to read GitHub token file: %s", error);
        }
    }

    // Then check environment variable
    const envToken = process.env.GITHUB_API_TOKEN;
    if (envToken) {
        return envToken;
    }

    return "";
}
