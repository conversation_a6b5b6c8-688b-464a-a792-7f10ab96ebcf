#!/usr/bin/env node

/**
 * Entry point for the CLI application.
 * This file handles the ESM entry point detection and calls the main function.
 * Keeping this separate from cli.ts allows cli.ts to be imported as a module
 * without triggering the main() function.
 */
import { realpathSync } from "fs";
import { fileURLToPath } from "url";

import { main } from "./cli";

// Only run main when this file is executed directly
// This is the ESM way to detect if a file is the entry point
// Check if this module is being run directly (not imported)
// This handles symlinks by resolving both paths to their actual file locations
// and guards against process.argv[1] being undefined (e.g., when using node -e)
const currentFile = fileURLToPath(import.meta.url);
const executedFile = process.argv[1] ? realpathSync(process.argv[1]) : null;

if (executedFile && currentFile === executedFile) {
    main()
        .then(() => {
            process.exit(0);
        })
        .catch((error) => {
            // eslint-disable-next-line no-console
            console.error("Main function failed:", error);
            process.exit(1);
        });
}
