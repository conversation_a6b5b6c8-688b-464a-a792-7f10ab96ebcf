import * as crypto from "crypto";

export const blobNamingVersion = 2023102300;

export class BlobTooLargeError extends Error {
    constructor(maxBlobSize: number) {
        super(`content exceeds maximum size of ${maxBlobSize}`);
    }
}

export class BlobNameCalculator {
    private readonly _textEncoder = new TextEncoder();

    constructor(public readonly maxBlobSize: number) {}

    private _hash(path: string, contents: Uint8Array): string {
        const hash = crypto.createHash("sha256");
        hash.update(path);
        hash.update(contents);
        return hash.digest("hex");
    }

    public calculateOrThrow(
        path: string,
        contents: string | Uint8Array,
        checkFileSize = true
    ): string {
        if (typeof contents === "string") {
            contents = this._textEncoder.encode(contents);
        }

        if (checkFileSize && contents.length > this.maxBlobSize) {
            throw new BlobTooLargeError(this.maxBlobSize);
        }

        return this._hash(path, contents);
    }

    public calculate(path: string, contents: string | Uint8Array): string | undefined {
        try {
            return this.calculateOrThrow(path, contents, true);
        } catch {
            return undefined;
        }
    }

    public calculateNoThrow(path: string, contents: string | Uint8Array): string {
        return this.calculateOrThrow(path, contents, false);
    }
}
