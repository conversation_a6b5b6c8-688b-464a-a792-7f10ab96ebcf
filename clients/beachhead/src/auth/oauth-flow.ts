import { getErrmsg } from "@augment-internal/sidecar-libs/src/exceptions";
import { createHash, randomBytes } from "crypto";
import * as fs from "fs";
import * as os from "os";
import * as path from "path";
import { URL } from "url";

import { APIServer } from "../augment-api";
import { AugmentConfigListener } from "../augment-config-listener";
import { getLogger } from "../logging";
import { AuthSessionStore } from "./auth-session-store";

const AUGMENT_HOSTNAME = process.env.TEST_HOSTNAME ?? ".augmentcode.com";
const SIGN_IN_TIMEOUT_MINS = 10;

// This class manages the OAuth flow for beachhead CLI.
// It generates OAuth URLs and processes authentication responses.
export class OAuthFlow {
    private _logger = getLogger("OAuthFlow");
    private _statePath: string;

    constructor(
        private _config: AugmentConfigListener,
        private _apiServer: APIServer,
        private _authSession: AuthSessionStore,
        cacheDir?: string
    ) {
        const augmentDir = cacheDir || path.join(os.homedir(), ".augment");
        this._statePath = path.join(augmentDir, "oauth-state.json");

        // Ensure the directory exists
        if (!fs.existsSync(augmentDir)) {
            fs.mkdirSync(augmentDir, { recursive: true });
        }
    }

    public startFlow(): string {
        this._logger.info("Creating new OAuth session...");
        const authUri = this.login();

        // Return the URL string for display to user
        const urlString = authUri.toString();
        return urlString;
    }

    private createOAuthState(): OAuthState {
        this._logger.info("Creating OAuth state");
        const verifier = base64URLEncode(randomBytes(32));
        const challenge = base64URLEncode(sha256(Buffer.from(verifier)));
        // NOTE: 8 bytes of randomness should be fine for the state
        const state = base64URLEncode(randomBytes(8));
        const oauthState: OAuthState = {
            codeVerifier: verifier,
            codeChallenge: challenge,
            state,
            creationTime: new Date().getTime(),
        };

        fs.writeFileSync(this._statePath, JSON.stringify(oauthState, null, 2), "utf8");
        this._logger.info("Created OAuth state");
        return oauthState;
    }

    private getOAuthState(): OAuthState | null {
        this._logger.info("Getting OAuth state");
        try {
            if (!fs.existsSync(this._statePath)) {
                return null;
            }

            const stateData = fs.readFileSync(this._statePath, "utf8");
            const state = JSON.parse(stateData) as OAuthState;

            // Ensure the state is not too old
            if (new Date().getTime() - state.creationTime < SIGN_IN_TIMEOUT_MINS * 60 * 1000) {
                return state;
            }
        } catch (error) {
            this._logger.error("Failed to read OAuth state:", error);
        }
        return null;
    }

    private removeOAuthState(): void {
        this._logger.info("Removing OAuth state");
        try {
            if (fs.existsSync(this._statePath)) {
                fs.unlinkSync(this._statePath);
            }
        } catch (error) {
            this._logger.error("Failed to remove OAuth state:", error);
        }
    }

    // Login the user via OAuth flow
    private login(): URL {
        try {
            // Create the state for the OAuth flow
            const oauthState = this.createOAuthState();
            return this.generateAuthorizeURL(oauthState);
        } catch (e) {
            this.removeOAuthState();
            throw e;
        }
    }

    // Generate the authorization URL for the user to visit
    private generateAuthorizeURL(oauthState: OAuthState): URL {
        // Use same client ID as vim sidecar for compatibility
        const searchParams = new URLSearchParams({
            /* eslint-disable @typescript-eslint/naming-convention */
            response_type: "code",
            code_challenge: oauthState.codeChallenge,
            client_id: "v",
            /* eslint-enable @typescript-eslint/naming-convention */
            state: oauthState.state,
            prompt: "login",
        });

        const authorizeURL = new URL(
            `/authorize?${searchParams.toString()}`,
            this._config.config.oauth.url
        );

        return authorizeURL;
    }

    public async handleAuthJson(jsonData: string): Promise<string> {
        // Parse the JSON data from the user
        const args = JSON.parse(jsonData) as AuthArgs;
        const oauthState = this.getOAuthState();
        if (!oauthState) {
            throw new Error("No OAuth state found");
        }

        // Since we have a response, we won't need this state again
        this.removeOAuthState();

        if (oauthState.state !== args.state) {
            throw new Error("Unknown state");
        }

        if (args.error) {
            const parts: string[] = [`(${args.error})`];
            if (args.error_description) {
                parts.push(args.error_description);
            }
            throw new Error(`OAuth request failed: ${parts.join(" ")}`);
        }

        if (!args.code) {
            throw new Error("No code");
        }

        if (!args.tenant_url) {
            throw new Error("No tenant URL");
        } else if (!new URL(args.tenant_url).hostname.endsWith(AUGMENT_HOSTNAME)) {
            throw new Error("OAuth request failed: invalid OAuth tenant URL");
        }

        // Swap the code for an access token
        try {
            this._logger.info("Calling get access token to retrieve access token");
            const accessToken = await this._apiServer.getAccessToken(
                "", // This is the redirect URI, which should be an empty string for CLI
                args.tenant_url,
                oauthState.codeVerifier,
                args.code
            );
            await this._authSession.saveSession(accessToken, args.tenant_url);
            this._logger.info("Successfully retrieved and saved access token");
            return accessToken;
        } catch (err) {
            this._logger.error(`Failed to get and save access token: ${getErrmsg(err)}`);
            throw new Error(
                `If you have a firewall, please add "${args.tenant_url}" to your allowlist.`
            );
        }
    }
}

export function base64URLEncode(data: Buffer): string {
    return data.toString("base64").replace(/\+/g, "-").replace(/\//g, "_").replace(/=/g, "");
}

function sha256(buffer: Buffer): Buffer {
    return createHash("sha256").update(buffer).digest();
}

export interface OAuthState {
    codeVerifier: string;
    codeChallenge: string;
    state: string;
    creationTime: number;
}

// Represents the decoded json data from the OAuth response
export interface AuthArgs {
    code: string;
    state: string;
    error?: string;
    error_description?: string;
    /* eslint-disable @typescript-eslint/naming-convention */
    tenant_url: string;
    /* eslint-enable @typescript-eslint/naming-convention */
}
