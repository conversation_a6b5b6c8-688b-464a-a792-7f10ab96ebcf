import * as fs from "fs";
import * as os from "os";
import * as path from "path";

import { getLogger } from "../logging";

const SESSION_SCOPES = ["read", "write"];

export interface AugmentSession {
    accessToken: string;
    scopes: string[];
    tenantURL: string;
}

export interface AuthSessionStore {
    useOAuth: boolean;
    isLoggedIn: boolean | undefined;
    getSession(): Promise<AugmentSession | null>;
    saveSession(accessToken: string, tenantURL: string): Promise<void>;
    removeSession(): Promise<void>;
}

export class FileBasedAuthSessionStore implements AuthSessionStore {
    private _logger = getLogger("AuthSessionStore");
    private _sessionPath: string;
    private _isLoggedIn: boolean | undefined = undefined;

    constructor(cacheDir?: string) {
        const augmentDir = cacheDir || path.join(os.homedir(), ".augment");
        this._sessionPath = path.join(augmentDir, "session.json");

        // Ensure the directory exists
        if (!fs.existsSync(augmentDir)) {
            fs.mkdirSync(augmentDir, { recursive: true });
        }

        // Initialize login status synchronously
        this._initializeLoginStatusSync();
    }

    get useOAuth(): boolean {
        return true;
    }

    get isLoggedIn(): boolean | undefined {
        return this._isLoggedIn;
    }

    private _parseSessionFromString(sessionString: string): AugmentSession | null {
        try {
            const session = JSON.parse(sessionString) as AugmentSession;

            // Validate session structure
            if (session.accessToken && session.tenantURL && Array.isArray(session.scopes)) {
                return session;
            } else {
                this._logger.warn(
                    "Session validation failed: missing or invalid required fields (accessToken, tenantURL, scopes)"
                );
                return null;
            }
        } catch (parseError) {
            this._logger.warn("Failed to parse session JSON:", parseError);
            return null;
        }
    }

    private _initializeLoginStatusSync(): void {
        try {
            // First check for environment variable
            const envAuth = process.env.AUGMENT_SESSION_AUTH;
            if (envAuth) {
                const session = this._parseSessionFromString(envAuth);
                if (session) {
                    this._isLoggedIn = true;
                    this._logger.info(
                        "Using authentication from AUGMENT_SESSION_AUTH environment variable"
                    );
                    return;
                }
                // Error logging is handled in _parseSessionFromString
            }

            // Fall back to session file
            if (!fs.existsSync(this._sessionPath)) {
                this._isLoggedIn = false;
                return;
            }

            const sessionData = fs.readFileSync(this._sessionPath, "utf8");
            const session = this._parseSessionFromString(sessionData);

            if (session) {
                this._isLoggedIn = true;
            } else {
                this._isLoggedIn = false;
            }
        } catch (error) {
            this._logger.error("Failed to initialize login status:", error);
            this._isLoggedIn = false;
        }
    }

    public async getSession(): Promise<AugmentSession | null> {
        try {
            // First check for environment variable
            const envAuth = process.env.AUGMENT_SESSION_AUTH;
            if (envAuth) {
                const session = this._parseSessionFromString(envAuth);
                if (session) {
                    return session;
                }
                // Error logging is handled in _parseSessionFromString
            }

            // Fall back to session file
            if (!fs.existsSync(this._sessionPath)) {
                return null;
            }

            const sessionData = fs.readFileSync(this._sessionPath, "utf8");
            const session = this._parseSessionFromString(sessionData);

            if (session) {
                return session;
            } else {
                this._logger.warn("Invalid session data found, removing session");
                await this.removeSession();
                return null;
            }
        } catch (error) {
            this._logger.error("Failed to read session:", error);
            return null;
        }
    }

    public saveSession(accessToken: string, tenantURL: string): Promise<void> {
        try {
            const session: AugmentSession = {
                accessToken,
                tenantURL,
                scopes: SESSION_SCOPES,
            };

            fs.writeFileSync(this._sessionPath, JSON.stringify(session, null, 2), "utf8");

            // Update env vars used by sub agents on session refresh
            process.env.AUGMENT_API_URL = session.tenantURL;
            process.env.AUGMENT_API_TOKEN = session.accessToken;

            this._isLoggedIn = true;
            this._logger.info("Session saved successfully");
            return Promise.resolve();
        } catch (error) {
            this._logger.error("Failed to save session:", error);
            return Promise.reject(error);
        }
    }

    public removeSession(): Promise<void> {
        try {
            if (fs.existsSync(this._sessionPath)) {
                fs.unlinkSync(this._sessionPath);
            }
            this._isLoggedIn = false;
            this._logger.info("Session removed successfully");
            return Promise.resolve();
        } catch (error) {
            this._logger.error("Failed to remove session:", error);
            return Promise.reject(error);
        }
    }
}
