import { APIServerImplWithErrorReporting as APIServerImpl } from "./augment-api";
import { getLogger } from "./logging";
import {
    RemoteWorkspaceSetupStatus,
    RemoteWorkspaceSetupStep,
    RemoteWorkspaceSetupStepStatus,
} from "./remote-agent-manager/types";
import { CommandStatus, InitCommands } from "./workspace-init";

export class WorkspaceSetupStatusTracker {
    private _initCommands: InitCommands | undefined;

    private _logger = getLogger("WorkspaceSetupStatusTracker");

    constructor(initCommands?: InitCommands) {
        this._initCommands = initCommands;
    }

    // Maximum number of characters allowed in a single log message
    private readonly MAX_LOG_CHARS = 1000;

    public report_status(): RemoteWorkspaceSetupStatus {
        let steps: RemoteWorkspaceSetupStep[] = [];

        // Check if init commands were skipped due to persistence.
        // If they were skipped we do not really need to return any status
        if (this._initCommands?.skipped_due_to_persistence()) {
            return {
                steps: [],
            };
        }

        if (this._initCommands) {
            let updated_steps: RemoteWorkspaceSetupStep[] = [];
            updated_steps = this._initCommands.workspace_setup_status().steps;
            // Once steps are populated, then let's do that, so it is easier so we account for all steps.
            // Also let us increment the cmd sequence id.
            for (const step of updated_steps) {
                const chunk_logs = this.splitLogsIntoChunks(step.logs);
                // if no new logs, then just add the step
                if (chunk_logs.length === 0) {
                    steps.push(step);
                    continue;
                }
                // if there are multiple chunks, then we need to send them all with running status
                // we need to increment the sequence id for each new chunk
                if (chunk_logs.length > 1) {
                    for (let i = 0; i < chunk_logs.length - 1; i++) {
                        steps.push({
                            step_description: step.step_description,
                            logs: chunk_logs[i],
                            status: RemoteWorkspaceSetupStepStatus.running,
                            sequence_id: this._initCommands
                                ?.commands()
                                [step.step_number].sequence_id(), //eslint-disable-line
                            step_number: step.step_number,
                        });
                        this._initCommands?.commands()[step.step_number].increment_sequence_id();
                    }
                }
                const chunk = chunk_logs[chunk_logs.length - 1];
                steps.push({
                    step_description: step.step_description,
                    logs: chunk,
                    status: step.status,
                    sequence_id: this._initCommands?.commands()[step.step_number].sequence_id(),
                    step_number: step.step_number,
                });
            }
        }

        return {
            steps: steps,
        };
    }

    /**
     * Splits a log string into chunks of MAX_LOG_CHARS
     * @param logs The log string to split
     * @returns An array of log chunks, each with a maximum of MAX_LOG_CHARS characters
     */
    private splitLogsIntoChunks(logs: string): string[] {
        const chunks: string[] = [];
        let remainingLogs = logs;

        while (remainingLogs.length > 0) {
            const chunk = remainingLogs.substring(0, this.MAX_LOG_CHARS);
            chunks.push(chunk);
            remainingLogs = remainingLogs.substring(this.MAX_LOG_CHARS);
        }

        return chunks;
    }

    private isInitializationComplete(): boolean {
        if (!this._initCommands) {
            return true;
        }

        const status = this._initCommands.overall_status();
        return (
            status === CommandStatus.SUCCESS ||
            status === CommandStatus.FAILED ||
            status === CommandStatus.SKIPPED
        );
    }

    public run_report_status_logs_loop(apiServer: APIServerImpl, agentId: string): Promise<void> {
        return new Promise((resolve) => {
            const intervalId = setInterval(() => {
                (async () => {
                    try {
                        const status = this.report_status();
                        if (status.steps.length !== 0) {
                            // Log the steps being reported (truncate logs for readability)
                            const truncatedSteps = status.steps.map((step) => ({
                                ...step,
                                logs:
                                    step.logs.length > 100
                                        ? step.logs.substring(0, 100) + "..."
                                        : step.logs,
                            }));
                            this._logger.info(
                                `Reporting ${status.steps.length} log steps for agent ${agentId}: ${JSON.stringify(truncatedSteps)}`
                            );

                            // report each step separately
                            for (const step of status.steps) {
                                this._logger.debug(
                                    `Reporting step ${step.step_number} with sequence ID ${step.sequence_id} (${step.logs.length} chars)`
                                );
                                try {
                                    await apiServer.reportWorkspaceSetupLogs(agentId, {
                                        steps: [step],
                                    });
                                } catch (error) {
                                    this._logger.error(
                                        "Error reporting workspace setup logs for step: %s",
                                        error instanceof Error ? error.message : String(error)
                                    );
                                }
                            }
                        }
                        if (this.isInitializationComplete()) {
                            clearInterval(intervalId);
                            this._logger.info(
                                "Workspace initialization complete, stopping status reporting"
                            );
                            resolve();
                        }
                    } catch (error: unknown) {
                        const errorMessage = error instanceof Error ? error.message : String(error);
                        this._logger.error(
                            "Error in report_status_logs_loop interval: %s",
                            errorMessage
                        );
                        clearInterval(intervalId);
                        resolve();
                    }
                })().catch((err) => {
                    // This catch is to handle unexpected errors from the async IIFE itself
                    // and to satisfy linters about unhandled promises.
                    this._logger.error(
                        "Unexpected error in setInterval async wrapper: %s",
                        err instanceof Error ? err.message : String(err)
                    );
                });
            }, 1000);
        });
    }
}
