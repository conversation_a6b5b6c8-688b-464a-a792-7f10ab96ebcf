import {
    FileDeletedEvent,
    FileDidMoveEvent,
} from "@augment-internal/sidecar-libs/src/agent/agent-edit-types";
import { getErrmsg } from "@augment-internal/sidecar-libs/src/exceptions";
import { DisposableService } from "@augment-internal/sidecar-libs/src/lifecycle/disposable-service";

import { APIServer, Blobs } from "./augment-api";
import { FeatureFlagManager, FeatureFlags } from "./feature-flags";
import { newFileReader } from "./file-reader";
import { getLogger } from "./logging";
import { ChangedFile, FileChangeType } from "./remote-agent-manager/types";
import { IgnoreSourceBuiltin, IgnoreSourceFile, IgnoreStackBuilder } from "./utils/ignore-file";
import { FullPathFilter, PathIterator } from "./utils/path-iterator";
import { makePathFilter } from "./utils/path-iterator";
import { FileType } from "./utils/types";
import * as vscode from "./vscode";
import { BlobsCheckpointManager } from "./workspace/blobs-checkpoint-manager";
import { DiskFileManager } from "./workspace/disk-file-manager";
import { FilesystemChangeTracker } from "./workspace/filesystem-change-tracker";
import { PathHandlerImpl } from "./workspace/path-handler";
import { PathMap } from "./workspace/path-map";
import { IndexingEvent } from "./workspace/types";
import { PathHandler } from "./workspace/types";

export interface BeachheadWorkspaceManager {
    workspaceRoot: string;
    getCheckpoint(): Blobs | undefined;
    createSnapshot(): Promise<number>;
    getChangesSince(snapshotId: number): Promise<ChangedFile[]>;
    awaitBlobsUploaded(): Promise<void>;
    onFileDeleted: vscode.Event<FileDeletedEvent>;
    onFileDidMove: vscode.Event<FileDidMoveEvent>;
    onIndexing: vscode.Event<IndexingEvent>;
}

export class BeachheadWorkspaceManagerImpl
    extends DisposableService
    implements BeachheadWorkspaceManager
{
    private _pathMap: PathMap;
    private _pathHandler: PathHandler;
    private _diskFileManager: DiskFileManager;
    private _blobsCheckpointManager: BlobsCheckpointManager;
    private _featureFlagManager: FeatureFlagManager;
    private _folderId: number;
    private _maxUploadSizeBytes: number;
    private _pathFilter: FullPathFilter | undefined;

    // Filesystem change tracking
    /**
     * Filesystem change tracker instance.
     * Note: This is initialized asynchronously during constructor to avoid blocking agent startup.
     * Use _filesystemChangeTrackerReadyPromise before accessing.
     */
    private _filesystemChangeTracker: FilesystemChangeTracker | undefined;
    private _filesystemChangeTrackerReadyPromise: Promise<void> | undefined;

    private _logger = getLogger("WorkspaceManager");

    private readonly _fileDeletedEmitter = this.addDisposable(
        new vscode.EventEmitter<FileDeletedEvent>()
    );
    private readonly _fileDidMoveEmitter = this.addDisposable(
        new vscode.EventEmitter<FileDidMoveEvent>()
    );

    private readonly _indexingEmitter = this.addDisposable(
        new vscode.EventEmitter<IndexingEvent>()
    );

    constructor(
        private _workspaceRoot: string,
        private _apiServer: APIServer,
        featureFlags: FeatureFlags
    ) {
        super();

        this._maxUploadSizeBytes = featureFlags.maxUploadSizeBytes;
        this._featureFlagManager = new FeatureFlagManager({ initialFlags: featureFlags });

        this._pathMap = new PathMap();
        this._pathHandler = new PathHandlerImpl(this._maxUploadSizeBytes, newFileReader());
        this._diskFileManager = new DiskFileManager(
            "beachhead",
            this._apiServer,
            this._pathHandler,
            this._pathMap
        );
        this._blobsCheckpointManager = new BlobsCheckpointManager(
            this._apiServer,
            this._featureFlagManager,
            this._pathMap.onDidChangeBlobName
        );

        this._filesystemChangeTracker = undefined;
        this._folderId = this._pathMap.openSourceFolder(this.workspaceRoot, this.workspaceRoot);

        this.addDisposables(
            this._pathMap,
            this._featureFlagManager,
            this._diskFileManager,
            this._blobsCheckpointManager
        );

        // Connect to DiskFileManager progress events
        this._setupIndexingProgressTracking();
    }

    get workspaceRoot(): string {
        return this._workspaceRoot;
    }

    public async initialize(): Promise<void> {
        const ignoreStackBuilder = new IgnoreStackBuilder([
            new IgnoreSourceFile(".gitignore"),
            new IgnoreSourceBuiltin(this.workspaceRoot),
            new IgnoreSourceFile(".augmentignore"),
        ]);
        this._pathFilter = await makePathFilter(
            vscode.Uri.file(this.workspaceRoot),
            vscode.Uri.file(this.workspaceRoot),
            ignoreStackBuilder,
            undefined
        );

        // Start filesystem change tracker initialization in parallel
        // This can take 1-3 seconds and doesn't need to block agent loop startup
        this._startFilesystemChangeTrackerInitialization(this._pathFilter);

        // Create path iterator and enumerate paths
        const pathIterator = new PathIterator(
            this.workspaceRoot,
            vscode.Uri.file(this.workspaceRoot),
            vscode.Uri.file(this.workspaceRoot),
            this._pathFilter
        );
        for await (const [_fileUri, relPath, fileType, acceptance] of pathIterator) {
            this._pathMap.insert(this._folderId, relPath, fileType, acceptance);
            if (fileType === FileType.file && acceptance.accepted) {
                this._diskFileManager.ingestPath(this._folderId, relPath);
            }
        }
        // To enable quick startup, don't wait for blob uploads to complete
        // Instead, we will wait for blob uploads when needed, such as before
        // Before calling codebase-retrieval tool.
        // await this._diskFileManager.awaitQuiesced();
    }

    /**
     * Start filesystem change tracker initialization in parallel to avoid blocking agent startup
     */
    private _startFilesystemChangeTrackerInitialization(pathFilter: FullPathFilter): void {
        this._filesystemChangeTrackerReadyPromise = this._initializeFilesystemChangeTracker(
            pathFilter
        ).catch((error) => {
            this._logger.error(
                `Filesystem change tracker initialization failed: ${getErrmsg(error)}`
            );
            // Re-throw to maintain the rejected promise state for proper error handling
            throw error;
        });
    }

    /**
     * Initialize the filesystem change tracker asynchronously
     */
    private async _initializeFilesystemChangeTracker(pathFilter: FullPathFilter): Promise<void> {
        try {
            this._logger.info("Starting filesystem change tracker initialization");

            this._filesystemChangeTracker = new FilesystemChangeTracker(
                this._workspaceRoot,
                pathFilter,
                this._maxUploadSizeBytes
            );
            this.addDisposable(this._filesystemChangeTracker);

            // Set up event handler for file changes to trigger blob uploads
            this.addDisposable(
                this._filesystemChangeTracker.onFileChange((change) => {
                    this._handleFileChange(change);
                })
            );

            this._logger.info("Filesystem change tracker initialization complete");
        } catch (error) {
            this._logger.error(
                `Failed to initialize filesystem change tracker: ${getErrmsg(error)}`
            );
            throw error;
        }
    }

    public getCheckpoint(): Blobs | undefined {
        return this._blobsCheckpointManager.getContext();
    }

    public get onFileDeleted(): vscode.Event<FileDeletedEvent> {
        return this._fileDeletedEmitter.event;
    }

    public get onFileDidMove(): vscode.Event<FileDidMoveEvent> {
        return this._fileDidMoveEmitter.event;
    }

    public get onIndexing(): vscode.Event<IndexingEvent> {
        return this._indexingEmitter.event;
    }

    /**
     * Create a snapshot of the current filesystem state for change tracking
     * @returns Snapshot ID that can be used with getChangesSince()
     */
    public async createSnapshot(): Promise<number> {
        if (!this._filesystemChangeTrackerReadyPromise) {
            throw new Error("Filesystem change tracker is not initialized");
        }
        await this._filesystemChangeTrackerReadyPromise;
        if (!this._filesystemChangeTracker) {
            throw new Error("Filesystem change tracker is not initialized");
        }
        return this._filesystemChangeTracker.createSnapshot();
    }

    /**
     * Get changes since snapshotId using the filesystem change tracker
     * @param snapshotId The snapshotId to get changes since
     * @returns An array of changed files since the snapshotId
     */
    public async getChangesSince(snapshotId: number): Promise<ChangedFile[]> {
        if (!this._filesystemChangeTrackerReadyPromise) {
            throw new Error("Filesystem change tracker is not initialized");
        }
        await this._filesystemChangeTrackerReadyPromise;

        // Rebuilding the Path filter in case it was changed
        const ignoreStackBuilder = new IgnoreStackBuilder([
            new IgnoreSourceFile(".gitignore"),
            new IgnoreSourceBuiltin(this.workspaceRoot),
            new IgnoreSourceFile(".augmentignore"),
        ]);
        this._pathFilter = await makePathFilter(
            vscode.Uri.file(this.workspaceRoot),
            vscode.Uri.file(this.workspaceRoot),
            ignoreStackBuilder,
            undefined
        );
        try {
            const changes = await this._filesystemChangeTracker?.getChangesSince(snapshotId);
            if (changes) {
                return changes.filter((change) => {
                    const acceptance = this._pathFilter!.getPathInfo(
                        change.new_path,
                        FileType.file
                    );
                    return acceptance.accepted;
                });
            }
        } catch (error) {
            this._logger.error(`Error during change detection: ${getErrmsg(error)}`);
        }
        return [];
    }

    /**
     * Handle a single file change by updating path map and triggering blob upload asynchronously
     */
    private _handleFileChange(change: ChangedFile): void {
        if (!this._pathFilter) {
            this._logger.warn("Path filter not available, skipping file change handling");
            return;
        }
        const acceptance = this._pathFilter.getPathInfo(change.new_path, FileType.file);
        try {
            switch (change.change_type) {
                case FileChangeType.added:
                    this._pathMap.insert(
                        this._folderId,
                        change.new_path,
                        FileType.file,
                        acceptance
                    );
                    break;
                case FileChangeType.deleted:
                    this._pathMap.remove(this._folderId, change.new_path);
                    break;
                case FileChangeType.modified:
                    // No changes to path map for modified files
                    break;
                case FileChangeType.renamed:
                    this._pathMap.remove(this._folderId, change.old_path);
                    this._pathMap.insert(
                        this._folderId,
                        change.new_path,
                        FileType.file,
                        acceptance
                    );
                    break;
            }

            if (change.change_type !== FileChangeType.deleted && acceptance.accepted) {
                this._diskFileManager.ingestPath(this._folderId, change.new_path);
            }
        } catch (error) {
            this._logger.error(`Error handling file change: ${getErrmsg(error)}`);
        }
    }

    /**
     * Wait for all blob uploads to complete
     * @returns Promise that resolves when all blob uploads are finished
     */
    public async awaitBlobsUploaded(): Promise<void> {
        await this._diskFileManager.awaitQuiesced();
    }

    /**
     * Set up indexing progress tracking by connecting to DiskFileManager events
     */
    private _setupIndexingProgressTracking(): void {
        let totalFilesTracked = 0;
        let indexingStarted = false;

        // Listen to progress changes from DiskFileManager
        this.addDisposable(
            this._diskFileManager.onDidChangeInProgressItemCount((itemsInFlight: number) => {
                const filesProcessed = Math.max(0, totalFilesTracked - itemsInFlight);
                const percentage =
                    totalFilesTracked > 0
                        ? Math.round((filesProcessed / totalFilesTracked) * 100)
                        : 0;

                if (!indexingStarted && itemsInFlight > 0) {
                    indexingStarted = true;
                    totalFilesTracked = itemsInFlight + filesProcessed;
                }

                if (indexingStarted) {
                    this._indexingEmitter.fire({
                        workspaceRoot: this.workspaceRoot,
                        phase: percentage === 100 ? "complete" : "indexing",
                        percentage,
                    });
                }
            })
        );
    }

    /**
     * Dispose of resources including the filesystem change tracker
     */
    public dispose(): void {
        super.dispose();
    }
}
