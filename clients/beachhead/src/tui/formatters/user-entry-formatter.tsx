import { Box, Text } from "ink";
import React from "react";

import { UserEntry } from "../types/transcript-types";
import { ICONS } from "./utils/constants";

// Component to format UserEntry
export const UserEntryFormatter: React.FC<{ entry: UserEntry }> = ({ entry }) => {
    return (
        <Box
            marginBottom={1}
            width="100%"
            minWidth="100%"
            borderStyle="single"
            borderLeft={false}
            borderRight={false}
            borderTop={true}
            borderBottom={false}
            borderColor="grey"
            borderDimColor
        >
            <Text bold color="white" dimColor>
                {ICONS.PROMPT} {entry.content}
            </Text>
        </Box>
    );
};

