import { Box, Text } from "ink";
import React from "react";

import { ErrorEntry } from "../types/transcript-types";
import { ResultItem } from "./components";

interface ErrorEntryFormatterProps {
    entry: ErrorEntry;
}

export const ErrorEntryFormatter: React.FC<ErrorEntryFormatterProps> = ({ entry }) => {
    return (
        <Box flexDirection="column" marginBottom={1}>
            <Box>
                <Text color="red">* </Text>
                <Text color="red">{entry.error}</Text>
            </Box>
            {entry.context && (
                <ResultItem>
                    <Text dimColor>{entry.context}</Text>
                </ResultItem>
            )}
        </Box>
    );
};