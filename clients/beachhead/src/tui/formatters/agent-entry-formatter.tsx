import { Box, Text } from "ink";
import React from "react";

import { useTranscriptContext } from "../contexts/TranscriptContext";
import { AgentEntry } from "../types/transcript-types";
import { ICONS } from "./utils/constants";
import { formatMarkdown } from "./utils/markdown";

// Component to format AgentEntry
export const AgentEntryFormatter = ({ entry }: { entry: AgentEntry }) => {
    const { openCodeBlockRef } = useTranscriptContext();

    // Get current state and format content
    const [formattedContent, currentLanguage] = formatMarkdown(
        entry.content,
        openCodeBlockRef.current
    );

    // Update the ref with the new state
    openCodeBlockRef.current = currentLanguage;

    if (entry.isStreaming) {
        return (
            <Box flexDirection="column" marginLeft={2} marginRight={8}>
                {formattedContent}
            </Box>
        );
    }

    return (
        <Box>
            <Text color="white" bold>
                {ICONS.RESPONSE}
            </Text>
            <Box flexDirection="column" marginLeft={1} marginRight={8}>
                {formattedContent}
            </Box>
        </Box>
    );
};
