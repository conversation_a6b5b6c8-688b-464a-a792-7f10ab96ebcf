/**
 * Pluralize a word based on count
 * @param word The word to pluralize
 * @param count The count to determine if plural is needed
 * @param plural Optional custom plural form (defaults to word + 's')
 * @returns The pluralized string
 * 
 * @example
 * pluralize('file', 1) // 'file'
 * pluralize('file', 2) // 'files'
 * pluralize('box', 3, 'boxes') // 'boxes'
 * pluralize('child', 2, 'children') // 'children'
 */
export function pluralize(word: string, count: number, plural?: string): string {
    if (count === 1) {
        return word;
    }
    return plural || `${word}s`;
}

/**
 * Pluralize with count prefix
 * @param word The word to pluralize
 * @param count The count to determine if plural is needed
 * @param plural Optional custom plural form (defaults to word + 's')
 * @returns The count followed by the pluralized string
 * 
 * @example
 * pluralizeWithCount('file', 1) // '1 file'
 * pluralizeWithCount('file', 3) // '3 files'
 * pluralizeWithCount('box', 5, 'boxes') // '5 boxes'
 */
export function pluralizeWithCount(word: string, count: number, plural?: string): string {
    return `${count} ${pluralize(word, count, plural)}`;
}
