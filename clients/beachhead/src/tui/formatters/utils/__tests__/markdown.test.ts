import React from "react";

import {
    extractInlineMarkdownMatches,
    formatMarkdown,
    parseCodeFenceAttributes,
} from "../../../../tui/formatters/utils/markdown";

describe("Markdown Parser", () => {
    describe("formatMarkdown code fence detection", () => {
        test("should handle plain text", () => {
            const content = "This is just plain text with no code blocks.";
            const [elements, currentLanguage] = formatMarkdown(content);

            expect(currentLanguage).toBe(null);
            expect(React.isValidElement(elements)).toBe(true);
        });

        test("should handle code block with just three backticks", () => {
            const content = "```\nsome code\n```";
            const [elements, currentLanguage] = formatMarkdown(content);

            expect(currentLanguage).toBe(null);
            expect(React.isValidElement(elements)).toBe(true);
        });

        test("should handle code block with just four backticks", () => {
            const content = "````\nsome code\n````";
            const [elements, currentLanguage] = formatMarkdown(content);

            expect(currentLanguage).toBe(null);
            expect(React.isValidElement(elements)).toBe(true);
        });

        test("should detect unclosed code fence", () => {
            const content = "```tsx\nconst test = 'hello';";
            const [elements, currentLanguage] = formatMarkdown(content);

            expect(currentLanguage).toBe("tsx");
            expect(React.isValidElement(elements)).toBe(true);
        });

        test("should properly close code fence and set openCodeBlock to false", () => {
            const content = "```tsx\nconst test = 'hello';\n```";
            const [elements, currentLanguage] = formatMarkdown(content);

            expect(currentLanguage).toBe(null);
            expect(React.isValidElement(elements)).toBe(true);
        });

        test("should close code block when starting with current language", () => {
            const content = "console.log('more code');\n```";
            const [elements, currentLanguage] = formatMarkdown(content, "tsx");

            expect(currentLanguage).toBe(null);
            expect(React.isValidElement(elements)).toBe(true);
        });

        test("should remain in code block when starting with current language and no closing fence", () => {
            const content = "console.log('still in code block');";
            const [elements, currentLanguage] = formatMarkdown(content, "tsx");

            expect(currentLanguage).toBe("tsx");
            expect(React.isValidElement(elements)).toBe(true);
        });

        test("should track language from code fence", () => {
            const content = "````python\nprint('hello')\n````";
            const [elements, currentLanguage] = formatMarkdown(content, null);

            expect(currentLanguage).toBe(null); // Closed by the end fence
            expect(React.isValidElement(elements)).toBe(true);
        });

        test("should default to empty string when no language specified", () => {
            const content = "````\nconsole.log('hello');";
            const [elements, currentLanguage] = formatMarkdown(content, null);

            expect(currentLanguage).toBe("");
            expect(React.isValidElement(elements)).toBe(true);
        });
    });
    describe("parseCodeFenceAttributes", () => {
        test("should handle three backticks without text", () => {
            const result = parseCodeFenceAttributes("```");
            expect(result).toEqual({});
        });

        test("should handle four backticks without text", () => {
            const result = parseCodeFenceAttributes("````");
            expect(result).toEqual({});
        });

        test("should parse language only", () => {
            const result = parseCodeFenceAttributes("```tsx");
            expect(result).toEqual({
                lang: "tsx",
            });
        });

        test("should parse path without language", () => {
            const result = parseCodeFenceAttributes("``` path=src/test.js");
            expect(result).toEqual({
                path: "src/test.js",
            });
        });

        test("should parse mode without language", () => {
            const result = parseCodeFenceAttributes("``` mode=FULL");
            expect(result).toEqual({
                mode: "FULL",
            });
        });

        test("should parse language with path and mode", () => {
            const result = parseCodeFenceAttributes(
                "```tsx path=clients/beachhead/src/tui/ui/Repl.tsx mode=EXCERPT"
            );
            expect(result).toEqual({
                lang: "tsx",
                path: "clients/beachhead/src/tui/ui/Repl.tsx",
                mode: "EXCERPT",
            });
        });

        test("should handle four backticks with attributes", () => {
            const result = parseCodeFenceAttributes("````tsx path=test.tsx mode=EXCERPT");
            expect(result).toEqual({
                lang: "tsx",
                path: "test.tsx",
                mode: "EXCERPT",
            });
        });

        test("should handle complex file paths", () => {
            const result = parseCodeFenceAttributes(
                "```typescript path=src/components/ui/Button.component.tsx mode=EXCERPT"
            );
            expect(result).toEqual({
                lang: "typescript",
                path: "src/components/ui/Button.component.tsx",
                mode: "EXCERPT",
            });
        });

        test("should return empty object for invalid fence", () => {
            const result = parseCodeFenceAttributes("not a fence");
            expect(result).toEqual({});
        });
    });

    describe("formatMarkdown CodeMetadata output", () => {
        test("should render CodeMetadata when path is present", () => {
            const content = "```tsx path=test.tsx mode=EXCERPT\nconst x = 1;\n```";
            const [elements] = formatMarkdown(content);

            // Extract the children from the Fragment
            const children = (elements as any).props.children;

            // Should have CodeMetadata as first element
            const codeMetadata = children[0];
            expect(codeMetadata.props.path).toBe("test.tsx");
            expect(codeMetadata.props.mode).toBe("EXCERPT");
        });

        test("should not render CodeMetadata when path is missing", () => {
            const content = "```tsx\nconst x = 1;\n```";
            const [elements] = formatMarkdown(content);

            // Extract the children from the Fragment
            const children = React.Children.toArray((elements as any).props.children);

            const hasCodeMetadata = children.some(
                (child: any) => child.type?.name === "CodeMetadata"
            );
            expect(hasCodeMetadata).toBe(false);
        });
    });

    describe("formatMarkdown CodeLine output", () => {
        test("should render CodeLine components when inside code block", () => {
            const content = "console.log('line 1');\nconsole.log('line 2');";
            const [elements] = formatMarkdown(content, "tsx");

            // Extract the children from the Fragment
            const children = React.Children.toArray((elements as any).props.children);

            // Should have two CodeLine components
            expect(children).toHaveLength(2);

            const hasCodeLine = children.some((child: any) => child.type?.name === "CodeLine");
            expect(hasCodeLine).toBe(true);

            // Verify CodeLine components are rendered with correct content
            expect((children[0] as any).props.children).toBe("console.log('line 1');");
            expect((children[1] as any).props.children).toBe("console.log('line 2');");
        });
    });

    describe("formatMarkdown BulletList output", () => {
        test("should render individual BulletList components for dash bullets", () => {
            const content = "- First item\n- Second item\n- Third item";
            const [elements] = formatMarkdown(content);

            // Extract the children from the Fragment
            const children = React.Children.toArray((elements as any).props.children);

            // Should have three individual BulletList components
            expect(children).toHaveLength(3);

            children.forEach((child: any) => {
                expect(child.type?.name).toBe("BulletList");
                expect(child.props.items).toHaveLength(1);
            });

            expect((children[0] as any).props.items).toEqual(["First item"]);
            expect((children[1] as any).props.items).toEqual(["Second item"]);
            expect((children[2] as any).props.items).toEqual(["Third item"]);
        });

        test("should render individual BulletList components for asterisk bullets", () => {
            const content = "* Item one\n* Item two";
            const [elements] = formatMarkdown(content);

            const children = React.Children.toArray((elements as any).props.children);

            expect(children).toHaveLength(2);
            expect((children[0] as any).type?.name).toBe("BulletList");
            expect((children[0] as any).props.items).toEqual(["Item one"]);
            expect((children[1] as any).type?.name).toBe("BulletList");
            expect((children[1] as any).props.items).toEqual(["Item two"]);
        });

        test("should render individual BulletList components for bullet point bullets", () => {
            const content = "• First bullet\n• Second bullet";
            const [elements] = formatMarkdown(content);

            const children = React.Children.toArray((elements as any).props.children);

            expect(children).toHaveLength(2);
            expect((children[0] as any).type?.name).toBe("BulletList");
            expect((children[0] as any).props.items).toEqual(["First bullet"]);
            expect((children[1] as any).type?.name).toBe("BulletList");
            expect((children[1] as any).props.items).toEqual(["Second bullet"]);
        });

        test("should handle mixed bullet types as individual items", () => {
            const content = "- First item\n* Second item\n• Third item";
            const [elements] = formatMarkdown(content);

            const children = React.Children.toArray((elements as any).props.children);

            expect(children).toHaveLength(3);
            expect((children[0] as any).type?.name).toBe("BulletList");
            expect((children[0] as any).props.items).toEqual(["First item"]);
            expect((children[1] as any).type?.name).toBe("BulletList");
            expect((children[1] as any).props.items).toEqual(["Second item"]);
            expect((children[2] as any).type?.name).toBe("BulletList");
            expect((children[2] as any).props.items).toEqual(["Third item"]);
        });

        test("should handle single bullet item", () => {
            const content = "- Single item";
            const [elements] = formatMarkdown(content);

            const children = React.Children.toArray((elements as any).props.children);
            const bulletList = children[0] as any;

            expect(bulletList.type?.name).toBe("BulletList");
            expect(bulletList.props.items).toEqual(["Single item"]);
        });

        test("should handle bullet items with extra whitespace", () => {
            const content = "-   Item with spaces\n*    Another item\n•     Third item";
            const [elements] = formatMarkdown(content);

            const children = React.Children.toArray((elements as any).props.children);

            expect(children).toHaveLength(3);
            expect((children[0] as any).type?.name).toBe("BulletList");
            expect((children[0] as any).props.items).toEqual(["Item with spaces"]);
            expect((children[1] as any).type?.name).toBe("BulletList");
            expect((children[1] as any).props.items).toEqual(["Another item"]);
            expect((children[2] as any).type?.name).toBe("BulletList");
            expect((children[2] as any).props.items).toEqual(["Third item"]);
        });

        test("should render individual bullets with text in between", () => {
            const content = "- First item\n- Second item\nRegular text\n- This starts a new list";
            const [elements] = formatMarkdown(content);

            const children = React.Children.toArray((elements as any).props.children);

            // Should have: BulletList, BulletList, MarkdownText, BulletList
            expect(children).toHaveLength(4);

            expect((children[0] as any).type?.name).toBe("BulletList");
            expect((children[0] as any).props.items).toEqual(["First item"]);

            expect((children[1] as any).type?.name).toBe("BulletList");
            expect((children[1] as any).props.items).toEqual(["Second item"]);

            expect((children[2] as any).type?.name).toBe("MarkdownText");
            expect((children[2] as any).props.children).toBe("Regular text");

            expect((children[3] as any).type?.name).toBe("BulletList");
            expect((children[3] as any).props.items).toEqual(["This starts a new list"]);
        });

        test("should not match bullet-like text without proper spacing", () => {
            const content = "-not a bullet\n*also not a bullet\n•neither is this";
            const [elements] = formatMarkdown(content);

            const children = React.Children.toArray((elements as any).props.children);

            // Should render as regular MarkdownText, not BulletList
            expect(children).toHaveLength(3);
            children.forEach((child: any) => {
                expect(child.type?.name).toBe("MarkdownText");
            });
        });
    });

    describe("formatMarkdown NumberedList output", () => {
        test("should render individual NumberedList components", () => {
            const content = "1. First item\n2. Second item\n3. Third item";
            const [elements] = formatMarkdown(content);

            const children = React.Children.toArray((elements as any).props.children);

            // Should have three individual NumberedList components
            expect(children).toHaveLength(3);

            children.forEach((child: any) => {
                expect(child.type?.name).toBe("NumberedList");
                expect(child.props.items).toHaveLength(1);
            });

            expect((children[0] as any).props.items).toEqual(["First item"]);
            expect((children[0] as any).props.startIndex).toBe(1);
            expect((children[1] as any).props.items).toEqual(["Second item"]);
            expect((children[1] as any).props.startIndex).toBe(2);
            expect((children[2] as any).props.items).toEqual(["Third item"]);
            expect((children[2] as any).props.startIndex).toBe(3);
        });

        test("should handle numbered lists starting from different numbers", () => {
            const content = "5. Fifth item\n6. Sixth item";
            const [elements] = formatMarkdown(content);

            const children = React.Children.toArray((elements as any).props.children);

            expect(children).toHaveLength(2);
            expect((children[0] as any).type?.name).toBe("NumberedList");
            expect((children[0] as any).props.items).toEqual(["Fifth item"]);
            expect((children[0] as any).props.startIndex).toBe(5);
            expect((children[1] as any).type?.name).toBe("NumberedList");
            expect((children[1] as any).props.items).toEqual(["Sixth item"]);
            expect((children[1] as any).props.startIndex).toBe(6);
        });

        test("should handle single numbered item", () => {
            const content = "1. Single item";
            const [elements] = formatMarkdown(content);

            const children = React.Children.toArray((elements as any).props.children);

            expect(children).toHaveLength(1);
            const numberedList = children[0] as any;
            expect(numberedList.type?.name).toBe("NumberedList");
            expect(numberedList.props.items).toEqual(["Single item"]);
            expect(numberedList.props.startIndex).toBe(1);
        });

        test("should render individual numbered items with text in between", () => {
            const content = "1. First item\n2. Second item\nRegular text\n3. Third item";
            const [elements] = formatMarkdown(content);

            const children = React.Children.toArray((elements as any).props.children);

            // Should have: NumberedList, NumberedList, MarkdownText, NumberedList
            expect(children).toHaveLength(4);

            expect((children[0] as any).type?.name).toBe("NumberedList");
            expect((children[0] as any).props.items).toEqual(["First item"]);
            expect((children[0] as any).props.startIndex).toBe(1);

            expect((children[1] as any).type?.name).toBe("NumberedList");
            expect((children[1] as any).props.items).toEqual(["Second item"]);
            expect((children[1] as any).props.startIndex).toBe(2);

            expect((children[2] as any).type?.name).toBe("MarkdownText");
            expect((children[2] as any).props.children).toBe("Regular text");

            expect((children[3] as any).type?.name).toBe("NumberedList");
            expect((children[3] as any).props.items).toEqual(["Third item"]);
            expect((children[3] as any).props.startIndex).toBe(3);
        });

        test("should not match numbered-like text without proper spacing", () => {
            const content = "1.not a number\n2.also not a number";
            const [elements] = formatMarkdown(content);

            const children = React.Children.toArray((elements as any).props.children);

            // Should render as regular MarkdownText, not NumberedList
            expect(children).toHaveLength(2);
            children.forEach((child: any) => {
                expect(child.type?.name).toBe("MarkdownText");
            });
        });
    });

    describe("formatMarkdown MarkdownHeader output", () => {
        test("should render MarkdownHeader for h1", () => {
            const content = "# Main Title";
            const [elements] = formatMarkdown(content);

            const children = React.Children.toArray((elements as any).props.children);

            expect(children).toHaveLength(1);
            const header = children[0] as any;
            expect(header.type?.name).toBe("MarkdownHeader");
            expect(header.props.level).toBe(1);
            expect(header.props.children).toBe("Main Title");
        });

        test("should render MarkdownHeader for h2", () => {
            const content = "## Section Title";
            const [elements] = formatMarkdown(content);

            const children = React.Children.toArray((elements as any).props.children);

            expect(children).toHaveLength(1);
            const header = children[0] as any;
            expect(header.type?.name).toBe("MarkdownHeader");
            expect(header.props.level).toBe(2);
            expect(header.props.children).toBe("Section Title");
        });

        test("should render MarkdownHeader for h3", () => {
            const content = "### Subsection Title";
            const [elements] = formatMarkdown(content);

            const children = React.Children.toArray((elements as any).props.children);

            expect(children).toHaveLength(1);
            const header = children[0] as any;
            expect(header.type?.name).toBe("MarkdownHeader");
            expect(header.props.level).toBe(3);
            expect(header.props.children).toBe("Subsection Title");
        });

        test("should render MarkdownHeader for h4", () => {
            const content = "#### Sub-subsection Title";
            const [elements] = formatMarkdown(content);

            const children = React.Children.toArray((elements as any).props.children);

            expect(children).toHaveLength(1);
            const header = children[0] as any;
            expect(header.type?.name).toBe("MarkdownHeader");
            expect(header.props.level).toBe(4);
            expect(header.props.children).toBe("Sub-subsection Title");
        });

        test("should handle headers with extra whitespace", () => {
            const content = "##   Title with spaces   ";
            const [elements] = formatMarkdown(content);

            const children = React.Children.toArray((elements as any).props.children);

            expect(children).toHaveLength(1);
            const header = children[0] as any;
            expect(header.type?.name).toBe("MarkdownHeader");
            expect(header.props.level).toBe(2);
            expect(header.props.children).toBe("Title with spaces");
        });

        test("should render multiple headers", () => {
            const content = "# Main Title\n## Section\n### Subsection";
            const [elements] = formatMarkdown(content);

            const children = React.Children.toArray((elements as any).props.children);

            expect(children).toHaveLength(3);

            expect((children[0] as any).type?.name).toBe("MarkdownHeader");
            expect((children[0] as any).props.level).toBe(1);
            expect((children[0] as any).props.children).toBe("Main Title");

            expect((children[1] as any).type?.name).toBe("MarkdownHeader");
            expect((children[1] as any).props.level).toBe(2);
            expect((children[1] as any).props.children).toBe("Section");

            expect((children[2] as any).type?.name).toBe("MarkdownHeader");
            expect((children[2] as any).props.level).toBe(3);
            expect((children[2] as any).props.children).toBe("Subsection");
        });

        test("should handle headers mixed with other content", () => {
            const content = "# Title\nRegular text\n## Section\n- Bullet item";
            const [elements] = formatMarkdown(content);

            const children = React.Children.toArray((elements as any).props.children);

            expect(children).toHaveLength(4);

            expect((children[0] as any).type?.name).toBe("MarkdownHeader");
            expect((children[0] as any).props.level).toBe(1);
            expect((children[0] as any).props.children).toBe("Title");

            expect((children[1] as any).type?.name).toBe("MarkdownText");
            expect((children[1] as any).props.children).toBe("Regular text");

            expect((children[2] as any).type?.name).toBe("MarkdownHeader");
            expect((children[2] as any).props.level).toBe(2);
            expect((children[2] as any).props.children).toBe("Section");

            expect((children[3] as any).type?.name).toBe("BulletList");
            expect((children[3] as any).props.items).toEqual(["Bullet item"]);
        });

        test("should not match hash-like text without proper spacing", () => {
            const content = "#not a header\n##also not a header";
            const [elements] = formatMarkdown(content);

            const children = React.Children.toArray((elements as any).props.children);

            // Should render as regular MarkdownText, not MarkdownHeader
            expect(children).toHaveLength(2);
            children.forEach((child: any) => {
                expect(child.type?.name).toBe("MarkdownText");
            });
        });
    });

    describe("MarkdownText inline formatting", () => {
        describe("strong text", () => {
            test("should render strong text with ** syntax", () => {
                const content = "This is **strong text** in a sentence.";
                const [elements] = formatMarkdown(content);

                const children = React.Children.toArray((elements as any).props.children);
                expect(children).toHaveLength(1);

                const markdownText = children[0] as any;
                expect(markdownText.type?.name).toBe("MarkdownText");
                expect(markdownText.props.children).toBe(content);
            });

            test("should render strong text with __ syntax", () => {
                const content = "This is __strong text__ in a sentence.";
                const [elements] = formatMarkdown(content);

                const children = React.Children.toArray((elements as any).props.children);
                expect(children).toHaveLength(1);

                const markdownText = children[0] as any;
                expect(markdownText.type?.name).toBe("MarkdownText");
                expect(markdownText.props.children).toBe(content);
            });

            test("should handle multiple strong text segments", () => {
                const content = "**First strong** and __second strong__ text.";
                const [elements] = formatMarkdown(content);

                const children = React.Children.toArray((elements as any).props.children);
                expect(children).toHaveLength(1);

                const markdownText = children[0] as any;
                expect(markdownText.type?.name).toBe("MarkdownText");
                expect(markdownText.props.children).toBe(content);
            });
        });

        describe("emphasis text", () => {
            test("should render emphasis text with * syntax", () => {
                const content = "This is *emphasized text* in a sentence.";
                const [elements] = formatMarkdown(content);

                const children = React.Children.toArray((elements as any).props.children);
                expect(children).toHaveLength(1);

                const markdownText = children[0] as any;
                expect(markdownText.type?.name).toBe("MarkdownText");
                expect(markdownText.props.children).toBe(content);
            });

            test("should render emphasis text with _ syntax", () => {
                const content = "This is _emphasized text_ in a sentence.";
                const [elements] = formatMarkdown(content);

                const children = React.Children.toArray((elements as any).props.children);
                expect(children).toHaveLength(1);

                const markdownText = children[0] as any;
                expect(markdownText.type?.name).toBe("MarkdownText");
                expect(markdownText.props.children).toBe(content);
            });

            test("should handle multiple emphasis text segments", () => {
                const content = "*First emphasis* and _second emphasis_ text.";
                const [elements] = formatMarkdown(content);

                const children = React.Children.toArray((elements as any).props.children);
                expect(children).toHaveLength(1);

                const markdownText = children[0] as any;
                expect(markdownText.type?.name).toBe("MarkdownText");
                expect(markdownText.props.children).toBe(content);
            });
        });

        describe("code text", () => {
            test("should render inline code", () => {
                const content = "Use the `console.log()` function to debug.";
                const [elements] = formatMarkdown(content);

                const children = React.Children.toArray((elements as any).props.children);
                expect(children).toHaveLength(1);

                const markdownText = children[0] as any;
                expect(markdownText.type?.name).toBe("MarkdownText");
                expect(markdownText.props.children).toBe(content);
            });

            test("should handle multiple inline code segments", () => {
                const content = "Use `const` or `let` for variables.";
                const [elements] = formatMarkdown(content);

                const children = React.Children.toArray((elements as any).props.children);
                expect(children).toHaveLength(1);

                const markdownText = children[0] as any;
                expect(markdownText.type?.name).toBe("MarkdownText");
                expect(markdownText.props.children).toBe(content);
            });
        });

        describe("link text", () => {
            test("should render links", () => {
                const content = "Visit [Google](https://google.com) for search.";
                const [elements] = formatMarkdown(content);

                const children = React.Children.toArray((elements as any).props.children);
                expect(children).toHaveLength(1);

                const markdownText = children[0] as any;
                expect(markdownText.type?.name).toBe("MarkdownText");
                expect(markdownText.props.children).toBe(content);
            });

            test("should handle multiple links", () => {
                const content =
                    "Visit [Google](https://google.com) or [GitHub](https://github.com).";
                const [elements] = formatMarkdown(content);

                const children = React.Children.toArray((elements as any).props.children);
                expect(children).toHaveLength(1);

                const markdownText = children[0] as any;
                expect(markdownText.type?.name).toBe("MarkdownText");
                expect(markdownText.props.children).toBe(content);
            });
        });

        describe("combined formatting", () => {
            test("should handle strong and emphasis together", () => {
                const content = "This has **strong** and *emphasis* text.";
                const [elements] = formatMarkdown(content);

                const children = React.Children.toArray((elements as any).props.children);
                expect(children).toHaveLength(1);

                const markdownText = children[0] as any;
                expect(markdownText.type?.name).toBe("MarkdownText");
                expect(markdownText.props.children).toBe(content);
            });

            test("should handle all formatting types together", () => {
                const content =
                    "**Strong**, *emphasis*, `code`, and [link](https://example.com) text.";
                const [elements] = formatMarkdown(content);

                const children = React.Children.toArray((elements as any).props.children);
                expect(children).toHaveLength(1);

                const markdownText = children[0] as any;
                expect(markdownText.type?.name).toBe("MarkdownText");
                expect(markdownText.props.children).toBe(content);
            });

            test("should handle nested-like formatting", () => {
                const content =
                    "**Strong with `code` inside** and *emphasis with [link](https://example.com)*.";
                const [elements] = formatMarkdown(content);

                const children = React.Children.toArray((elements as any).props.children);
                expect(children).toHaveLength(1);

                const markdownText = children[0] as any;
                expect(markdownText.type?.name).toBe("MarkdownText");
                expect(markdownText.props.children).toBe(content);
            });
        });

        describe("plain text", () => {
            test("should render plain text without formatting", () => {
                const content = "This is just plain text with no formatting.";
                const [elements] = formatMarkdown(content);

                const children = React.Children.toArray((elements as any).props.children);
                expect(children).toHaveLength(1);

                const markdownText = children[0] as any;
                expect(markdownText.type?.name).toBe("MarkdownText");
                expect(markdownText.props.children).toBe(content);
            });

            test("should handle text with special characters but no formatting", () => {
                const content = "Text with * and _ and ` characters but not formatted.";
                const [elements] = formatMarkdown(content);

                const children = React.Children.toArray((elements as any).props.children);
                expect(children).toHaveLength(1);

                const markdownText = children[0] as any;
                expect(markdownText.type?.name).toBe("MarkdownText");
                expect(markdownText.props.children).toBe(content);
            });
        });
    });

    describe("Blockquote parsing", () => {
        test("should render blockquote with > and space", () => {
            const content = "> This is a blockquote";
            const [result] = formatMarkdown(content);
            const children = React.Children.toArray((result as any).props.children);

            expect(children).toHaveLength(1);

            const blockquoteElement = children[0] as any;
            expect(blockquoteElement.type?.name).toBe("Blockquote");
            expect(blockquoteElement.props.children).toBe("This is a blockquote");
        });

        test("should render multiple blockquotes", () => {
            const content = "> First quote\n> Second quote";
            const [result] = formatMarkdown(content);
            const children = React.Children.toArray((result as any).props.children);

            expect(children).toHaveLength(2);

            const firstBlockquote = children[0] as any;
            const secondBlockquote = children[1] as any;

            expect(firstBlockquote.type?.name).toBe("Blockquote");
            expect(firstBlockquote.props.children).toBe("First quote");
            expect(secondBlockquote.type?.name).toBe("Blockquote");
            expect(secondBlockquote.props.children).toBe("Second quote");
        });

        test("should not match > without space", () => {
            const content = ">This is not a blockquote";
            const [result] = formatMarkdown(content);
            const children = React.Children.toArray((result as any).props.children);

            expect(children).toHaveLength(1);

            const textElement = children[0] as any;
            expect(textElement.type?.name).toBe("MarkdownText");
        });

        test("should handle blockquote with inline formatting", () => {
            const content = "> This is **bold** text in a blockquote";
            const [result] = formatMarkdown(content);
            const children = React.Children.toArray((result as any).props.children);

            expect(children).toHaveLength(1);

            const blockquoteElement = children[0] as any;
            expect(blockquoteElement.type?.name).toBe("Blockquote");
            expect(blockquoteElement.props.children).toBe("This is **bold** text in a blockquote");
        });
    });

    describe("Horizontal rule parsing", () => {
        test("should render horizontal rule with three asterisks", () => {
            const content = "***";
            const [result] = formatMarkdown(content);
            const children = React.Children.toArray((result as any).props.children);

            expect(children).toHaveLength(1);

            const hrElement = children[0] as any;
            expect(hrElement.type?.name).toBe("HorizontalRule");
        });

        test("should render horizontal rule with three dashes", () => {
            const content = "---";
            const [result] = formatMarkdown(content);
            const children = React.Children.toArray((result as any).props.children);

            expect(children).toHaveLength(1);

            const hrElement = children[0] as any;
            expect(hrElement.type?.name).toBe("HorizontalRule");
        });

        test("should render horizontal rule with three underscores", () => {
            const content = "___";
            const [result] = formatMarkdown(content);
            const children = React.Children.toArray((result as any).props.children);

            expect(children).toHaveLength(1);

            const hrElement = children[0] as any;
            expect(hrElement.type?.name).toBe("HorizontalRule");
        });

        test("should render horizontal rule with more than three characters", () => {
            const content = "*****";
            const [result] = formatMarkdown(content);
            const children = React.Children.toArray((result as any).props.children);

            expect(children).toHaveLength(1);

            const hrElement = children[0] as any;
            expect(hrElement.type?.name).toBe("HorizontalRule");
        });

        test("should render horizontal rule with spaces between characters", () => {
            const content = "* * *";
            const [result] = formatMarkdown(content);
            const children = React.Children.toArray((result as any).props.children);

            expect(children).toHaveLength(1);

            const hrElement = children[0] as any;
            expect(hrElement.type?.name).toBe("HorizontalRule");
        });

        test("should render horizontal rule with mixed spaces", () => {
            const content = "- - - - -";
            const [result] = formatMarkdown(content);
            const children = React.Children.toArray((result as any).props.children);

            expect(children).toHaveLength(1);

            const hrElement = children[0] as any;
            expect(hrElement.type?.name).toBe("HorizontalRule");
        });

        test("should not match with only two characters", () => {
            const content = "**";
            const [result] = formatMarkdown(content);
            const children = React.Children.toArray((result as any).props.children);

            expect(children).toHaveLength(1);

            const textElement = children[0] as any;
            expect(textElement.type?.name).toBe("MarkdownText");
        });

        test("should not match mixed character types", () => {
            const content = "*-_";
            const [result] = formatMarkdown(content);
            const children = React.Children.toArray((result as any).props.children);

            expect(children).toHaveLength(1);

            const textElement = children[0] as any;
            expect(textElement.type?.name).toBe("MarkdownText");
        });

        test("should not match with other characters mixed in", () => {
            const content = "***a***";
            const [result] = formatMarkdown(content);
            const children = React.Children.toArray((result as any).props.children);

            expect(children).toHaveLength(1);

            const textElement = children[0] as any;
            expect(textElement.type?.name).toBe("MarkdownText");
        });

        test("should handle multiple horizontal rules", () => {
            const content = "***\n---\n___";
            const [result] = formatMarkdown(content);
            const children = React.Children.toArray((result as any).props.children);

            expect(children).toHaveLength(3);

            const firstHr = children[0] as any;
            const secondHr = children[1] as any;
            const thirdHr = children[2] as any;

            expect(firstHr.type?.name).toBe("HorizontalRule");
            expect(secondHr.type?.name).toBe("HorizontalRule");
            expect(thirdHr.type?.name).toBe("HorizontalRule");
        });
    });

    describe("Inline markdown pattern matching", () => {
        describe("strong text patterns", () => {
            test("should match ** syntax", () => {
                const text = "This is **strong text** in a sentence.";
                const matches = extractInlineMarkdownMatches(text);

                expect(matches).toHaveLength(1);
                expect(matches[0].type).toBe("strong");
                expect(matches[0].content).toBe("strong text");
            });

            test("should match __ syntax", () => {
                const text = "This is __strong text__ in a sentence.";
                const matches = extractInlineMarkdownMatches(text);

                expect(matches).toHaveLength(1);
                expect(matches[0].type).toBe("strong");
                expect(matches[0].content).toBe("strong text");
            });

            test("should match multiple ** segments", () => {
                const text = "**First** and **second** strong text.";
                const matches = extractInlineMarkdownMatches(text);

                expect(matches).toHaveLength(2);
                expect(matches[0].type).toBe("strong");
                expect(matches[0].content).toBe("First");
                expect(matches[1].type).toBe("strong");
                expect(matches[1].content).toBe("second");
            });

            test("should not match single asterisks", () => {
                const text = "This is *not strong* text.";
                const matches = extractInlineMarkdownMatches(text);

                const strongMatches = matches.filter((m) => m.type === "strong");
                expect(strongMatches).toHaveLength(0);
            });
        });

        describe("emphasis text patterns", () => {
            test("should match * syntax", () => {
                const text = "This is *emphasized text* in a sentence.";
                const matches = extractInlineMarkdownMatches(text);

                expect(matches).toHaveLength(1);
                expect(matches[0].type).toBe("emphasis");
                expect(matches[0].content).toBe("emphasized text");
            });

            test("should match _ syntax", () => {
                const text = "This is _emphasized text_ in a sentence.";
                const matches = extractInlineMarkdownMatches(text);

                expect(matches).toHaveLength(1);
                expect(matches[0].type).toBe("emphasis");
                expect(matches[0].content).toBe("emphasized text");
            });

            test("should match multiple * segments", () => {
                const text = "*First* and *second* emphasis text.";
                const matches = extractInlineMarkdownMatches(text);

                const emphasisMatches = matches.filter((m) => m.type === "emphasis");
                expect(emphasisMatches).toHaveLength(2);
                expect(emphasisMatches[0].content).toBe("First");
                expect(emphasisMatches[1].content).toBe("second");
            });

            test("should not match double asterisks", () => {
                const text = "This is **not emphasis** text.";
                const matches = extractInlineMarkdownMatches(text);

                const emphasisMatches = matches.filter((m) => m.type === "emphasis");
                expect(emphasisMatches).toHaveLength(0);
            });
        });

        describe("code text patterns", () => {
            test("should match inline code", () => {
                const text = "Use the `console.log()` function to debug.";
                const matches = extractInlineMarkdownMatches(text);

                expect(matches).toHaveLength(1);
                expect(matches[0].type).toBe("code");
                expect(matches[0].content).toBe("console.log()");
            });

            test("should match multiple code segments", () => {
                const text = "Use `const` or `let` for variables.";
                const matches = extractInlineMarkdownMatches(text);

                const codeMatches = matches.filter((m) => m.type === "code");
                expect(codeMatches).toHaveLength(2);
                expect(codeMatches[0].content).toBe("const");
                expect(codeMatches[1].content).toBe("let");
            });

            test("should not match empty backticks", () => {
                const text = "Empty `` backticks should not match.";
                const matches = extractInlineMarkdownMatches(text);

                const codeMatches = matches.filter((m) => m.type === "code");
                expect(codeMatches).toHaveLength(0);
            });
        });

        describe("link patterns", () => {
            test("should match basic links", () => {
                const text = "Visit [Google](https://google.com) for search.";
                const matches = extractInlineMarkdownMatches(text);

                expect(matches).toHaveLength(1);
                expect(matches[0].type).toBe("link");
                expect(matches[0].content).toBe("Google");
                expect(matches[0].url).toBe("https://google.com");
            });

            test("should match multiple links", () => {
                const text = "Visit [Google](https://google.com) or [GitHub](https://github.com).";
                const matches = extractInlineMarkdownMatches(text);

                const linkMatches = matches.filter((m) => m.type === "link");
                expect(linkMatches).toHaveLength(2);
                expect(linkMatches[0].content).toBe("Google");
                expect(linkMatches[0].url).toBe("https://google.com");
                expect(linkMatches[1].content).toBe("GitHub");
                expect(linkMatches[1].url).toBe("https://github.com");
            });

            test("should not match malformed links", () => {
                const text = "This has [malformed link without closing paren.";
                const matches = extractInlineMarkdownMatches(text);

                const linkMatches = matches.filter((m) => m.type === "link");
                expect(linkMatches).toHaveLength(0);
            });
        });

        describe("pattern precedence", () => {
            test("should distinguish between strong and emphasis", () => {
                const text = "**Strong** and *emphasis* text.";
                const matches = extractInlineMarkdownMatches(text);

                expect(matches).toHaveLength(2);

                const strongMatches = matches.filter((m) => m.type === "strong");
                const emphasisMatches = matches.filter((m) => m.type === "emphasis");

                expect(strongMatches).toHaveLength(1);
                expect(strongMatches[0].content).toBe("Strong");
                expect(emphasisMatches).toHaveLength(1);
                expect(emphasisMatches[0].content).toBe("emphasis");
            });

            test("should handle mixed syntax types", () => {
                const text = "**Strong** and _emphasis_ text.";
                const matches = extractInlineMarkdownMatches(text);

                expect(matches).toHaveLength(2);

                const strongMatches = matches.filter((m) => m.type === "strong");
                const emphasisMatches = matches.filter((m) => m.type === "emphasis");

                expect(strongMatches).toHaveLength(1);
                expect(strongMatches[0].content).toBe("Strong");
                expect(emphasisMatches).toHaveLength(1);
                expect(emphasisMatches[0].content).toBe("emphasis");
            });

            test("should handle all formatting types together", () => {
                const text =
                    "**Strong**, *emphasis*, `code`, and [link](https://example.com) text.";
                const matches = extractInlineMarkdownMatches(text);

                expect(matches).toHaveLength(4);
                expect(matches[0].type).toBe("strong");
                expect(matches[0].content).toBe("Strong");
                expect(matches[1].type).toBe("emphasis");
                expect(matches[1].content).toBe("emphasis");
                expect(matches[2].type).toBe("code");
                expect(matches[2].content).toBe("code");
                expect(matches[3].type).toBe("link");
                expect(matches[3].content).toBe("link");
                expect(matches[3].url).toBe("https://example.com");
            });
        });
    });
});
