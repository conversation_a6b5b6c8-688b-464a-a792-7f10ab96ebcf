import { processDiffEntries } from "../diff";
import type {
    StrReplaceEntryRaw,
} from "@augment-internal/sidecar-libs/src/tools/sidecar-tools/str-replace-editor-tool/utils";

describe("processDiffEntries", () => {
    describe("str_replace type", () => {
        it("should handle non-string old_str and new_str values", () => {
            const entries: StrReplaceEntryRaw[] = [
                {
                    index: 0,
                    old_str: null as any,
                    new_str: undefined as any,
                    old_str_start_line_number: 1,
                },
                {
                    index: 1,
                    old_str: 123 as any,
                    new_str: true as any,
                    old_str_start_line_number: 5,
                },
            ];

            // This should not throw an error
            expect(() => {
                processDiffEntries(entries, "str_replace");
            }).not.toThrow();

            const result = processDiffEntries(entries, "str_replace");
            expect(result).toBeDefined();
            expect(result.diffLines).toBeDefined();
            expect(result.totalAdditions).toBeGreaterThanOrEqual(0);
            expect(result.totalRemovals).toBeGreaterThanOrEqual(0);
            expect(result.maxLineNumber).toBeGreaterThanOrEqual(0);
        });

        it("should handle normal string values correctly", () => {
            const entries: StrReplaceEntryRaw[] = [
                {
                    index: 0,
                    old_str: "old text",
                    new_str: "new text",
                    old_str_start_line_number: 1,
                },
            ];

            const result = processDiffEntries(entries, "str_replace");
            expect(result.diffLines.length).toBeGreaterThan(0);
            expect(result.totalAdditions).toBe(1);
            expect(result.totalRemovals).toBe(1);
        });
    });
});