import {
    InsertLineEntryRaw,
    StrReplaceEntryRaw,
} from "@augment-internal/sidecar-libs/src/tools/sidecar-tools/str-replace-editor-tool/utils";
import { diffLines as diffLinesUtil, diffWords } from "diff";

export interface DiffLine {
    type: "addition" | "removal";
    lineNumber: number;
    content: string;
    key: string;
    compareContent?: string;
}

export interface DiffProcessingResult {
    diffLines: DiffLine[];
    totalAdditions: number;
    totalRemovals: number;
    maxLineNumber: number;
}

export interface WordSegment {
    text: string;
    isHighlighted: boolean;
}

export function computeWordDiffSegments(
    content: string,
    compareContent: string | undefined,
    type: "addition" | "removal"
): WordSegment[] {
    if (!compareContent) {
        // No comparison, return entire content as non-highlighted
        return [{ text: content, isHighlighted: false }];
    }

    // Perform word-level diff
    const wordChanges = diffWords(
        type === "removal" ? content : compareContent,
        type === "removal" ? compareContent : content
    );

    // Convert to simpler segment structure
    const segments: WordSegment[] = [];

    // highlight changed words, show unchanged words
    for (const part of wordChanges) {
        if (type === "removal") {
            if (part.removed) {
                segments.push({ text: part.value, isHighlighted: true });
            } else if (!part.added) {
                segments.push({ text: part.value, isHighlighted: false });
            }
            // Skip added words (they don't appear in the removal line)
        } else {
            if (part.added) {
                segments.push({ text: part.value, isHighlighted: true });
            } else if (!part.removed) {
                segments.push({ text: part.value, isHighlighted: false });
            }
            // Skip removed words (they don't appear in the addition line)
        }
    }

    return segments;
}

function splitIntoNonEmptyLines(text: string): string[] {
    return text.split("\n").filter((line) => line.length > 0);
}

function createDiffLines(
    lines: string[],
    type: "addition" | "removal",
    startLineNumber: number,
    keyPrefix: string
): DiffLine[] {
    const diffLines: DiffLine[] = [];
    let lineNum = startLineNumber;

    lines.forEach((line) => {
        diffLines.push({
            type,
            lineNumber: lineNum,
            content: line,
            key: `${keyPrefix}-${lineNum}-${line}`,
        });
        lineNum++;
    });

    return diffLines;
}

// Pair removals and additions for word-level comparison
function pairDiffLines(
    removalData: DiffLine[],
    additionData: DiffLine[],
    diffLines: DiffLine[]
): void {
    const maxLength = Math.max(removalData.length, additionData.length);

    // Add removals with comparison content
    for (let i = 0; i < maxLength; i++) {
        if (i < removalData.length) {
            const removal = removalData[i];
            // If there's a corresponding addition, use it for comparison
            if (i < additionData.length) {
                removal.compareContent = additionData[i].content;
            }
            diffLines.push(removal);
        }
    }

    // Add additions with comparison content
    for (let i = 0; i < additionData.length; i++) {
        const addition = additionData[i];
        // If there's a corresponding removal, use it for comparison
        if (i < removalData.length) {
            addition.compareContent = removalData[i].content;
        }
        diffLines.push(addition);
    }
}

// Process diff entries and return structured data
export function processDiffEntries(
    entries: StrReplaceEntryRaw[] | InsertLineEntryRaw[],
    type: "str_replace" | "insert"
): DiffProcessingResult {
    let totalAdditions = 0;
    let totalRemovals = 0;
    const diffLines: DiffLine[] = [];

    if (type === "str_replace") {
        const strReplaceEntries = entries as StrReplaceEntryRaw[];

        strReplaceEntries.forEach((entry) => {
            const lines = diffLinesUtil(String(entry.old_str || ""), String(entry.new_str || ""));
            let currentLineNumber: number = (entry.old_str_start_line_number as number) || 1;

            // Track removals and additions separately for pairing
            const removalData: DiffLine[] = [];
            const additionData: DiffLine[] = [];

            lines.forEach((change) => {
                const count = change.count || 0;

                if (change.removed) {
                    totalRemovals += count;
                    const changeLines = splitIntoNonEmptyLines(change.value);
                    const removals = createDiffLines(
                        changeLines,
                        "removal",
                        currentLineNumber,
                        "remove"
                    );
                    removalData.push(...removals);
                    currentLineNumber += count;
                } else if (change.added) {
                    totalAdditions += count;
                    const changeLines = splitIntoNonEmptyLines(change.value);
                    // For additions in a replacement, start at the original line number
                    const additions = createDiffLines(
                        changeLines,
                        "addition",
                        entry.old_str_start_line_number as number,
                        "add"
                    );
                    additionData.push(...additions);
                } else {
                    // Unchanged lines - just advance line number
                    currentLineNumber += count;
                }
            });

            // Pair removals with additions for word-level comparison
            pairDiffLines(removalData, additionData, diffLines);
        });
    } else {
        // Process insert entries
        const insertEntries = entries as InsertLineEntryRaw[];

        insertEntries.forEach((entry) => {
            const insertLineNumber = (entry.insert_line as number) || 0;
            const newStr = entry.new_str as string;
            const newLines = splitIntoNonEmptyLines(newStr);

            totalAdditions += newLines.length;

            // Insert after line N means the content appears at line N+1
            const insertions = createDiffLines(
                newLines,
                "addition",
                insertLineNumber + 1,
                "insert"
            );
            diffLines.push(...insertions);
        });
    }

    const maxLineNumber = Math.max(...diffLines.map((d) => d.lineNumber), 0);

    return {
        diffLines,
        totalAdditions,
        totalRemovals,
        maxLineNumber,
    };
}

// Sort diff lines by line number and type (removals before additions)
export function sortDiffLines(diffLines: DiffLine[]): DiffLine[] {
    return [...diffLines].sort((a, b) => {
        if (a.lineNumber === b.lineNumber) {
            // Same line number: removals come before additions
            return a.type === "removal" ? -1 : 1;
        }
        return a.lineNumber - b.lineNumber;
    });
}

export interface GapInfo {
    afterLineNumber: number;
    beforeLineNumber: number;
    hiddenLines: number;
    isNewSection: boolean;
}

export function detectGaps(sortedDiffLines: DiffLine[]): Map<number, GapInfo> {
    const gaps = new Map<number, GapInfo>();
    let lastLineNumber = -1;
    let lastType = "";

    sortedDiffLines.forEach((diffLine, index) => {
        const currentLineNumber = diffLine.lineNumber;
        const currentType = diffLine.type;

        if (lastLineNumber !== -1) {
            const isNewSection = currentType === "removal" && lastType === "addition";
            const hasGap = currentLineNumber > lastLineNumber + 1;

            if (isNewSection || hasGap) {
                const hiddenLines = currentLineNumber - lastLineNumber - 1;
                gaps.set(index, {
                    afterLineNumber: lastLineNumber,
                    beforeLineNumber: currentLineNumber,
                    hiddenLines,
                    isNewSection,
                });
            }
        }

        lastLineNumber = currentLineNumber;
        lastType = currentType;
    });

    return gaps;
}
