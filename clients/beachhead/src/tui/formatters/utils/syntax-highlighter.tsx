import { toJsxRuntime } from "hast-util-to-jsx-runtime";
import bash from "highlight.js/lib/languages/bash";
import cpp from "highlight.js/lib/languages/cpp";
import csharp from "highlight.js/lib/languages/csharp";
import css from "highlight.js/lib/languages/css";
import diff from "highlight.js/lib/languages/diff";
import dockerfile from "highlight.js/lib/languages/dockerfile";
import go from "highlight.js/lib/languages/go";
import graphql from "highlight.js/lib/languages/graphql";
import ini from "highlight.js/lib/languages/ini";
import java from "highlight.js/lib/languages/java";
import javascript from "highlight.js/lib/languages/javascript";
import json from "highlight.js/lib/languages/json";
import kotlin from "highlight.js/lib/languages/kotlin";
import less from "highlight.js/lib/languages/less";
import makefile from "highlight.js/lib/languages/makefile";
import markdown from "highlight.js/lib/languages/markdown";
import php from "highlight.js/lib/languages/php";
import properties from "highlight.js/lib/languages/properties";
import python from "highlight.js/lib/languages/python";
import ruby from "highlight.js/lib/languages/ruby";
import rust from "highlight.js/lib/languages/rust";
import scss from "highlight.js/lib/languages/scss";
import shell from "highlight.js/lib/languages/shell";
import sql from "highlight.js/lib/languages/sql";
import swift from "highlight.js/lib/languages/swift";
import typescript from "highlight.js/lib/languages/typescript";
import html from "highlight.js/lib/languages/xml";
import xml from "highlight.js/lib/languages/xml";
import yaml from "highlight.js/lib/languages/yaml";
import { Text } from "ink";
import { createLowlight } from "lowlight";
import * as React from "react";
import { Fragment, jsx, jsxs } from "react/jsx-runtime";

// Create and configure lowlight instance
const lowlight = createLowlight();

// Register all languages
lowlight.register(".env", ini);
lowlight.register("bash", bash);
lowlight.register("c", cpp);
lowlight.register("c++", cpp);
lowlight.register("cpp", cpp);
lowlight.register("cs", csharp);
lowlight.register("csharp", csharp);
lowlight.register("css", css);
lowlight.register("diff", diff);
lowlight.register("dockerfile", dockerfile);
lowlight.register("dotenv", ini);
lowlight.register("env", ini);
lowlight.register("go", go);
lowlight.register("gql", graphql);
lowlight.register("graphql", graphql);
lowlight.register("hcl", properties);
lowlight.register("html", html);
lowlight.register("ini", ini);
lowlight.register("java", java);
lowlight.register("javascript", javascript);
lowlight.register("js", javascript);
lowlight.register("json", json);
lowlight.register("jsx", javascript);
lowlight.register("kotlin", kotlin);
lowlight.register("less", less);
lowlight.register("make", makefile);
lowlight.register("makefile", makefile);
lowlight.register("markdown", markdown);
lowlight.register("md", markdown);
lowlight.register("mdx", markdown);
lowlight.register("mk", makefile);
lowlight.register("php", php);
lowlight.register("py", python);
lowlight.register("python", python);
lowlight.register("rb", ruby);
lowlight.register("ruby", ruby);
lowlight.register("rust", rust);
lowlight.register("scss", scss);
lowlight.register("sh", shell);
lowlight.register("shell", shell);
lowlight.register("sql", sql);
lowlight.register("svelte", html);
lowlight.register("swift", swift);
lowlight.register("terraform", properties);
lowlight.register("tf", properties);
lowlight.register("toml", properties);
lowlight.register("ts", typescript);
lowlight.register("tsx", typescript);
lowlight.register("typescript", typescript);
lowlight.register("vue", xml);
lowlight.register("xml", xml);
lowlight.register("yaml", yaml);
lowlight.register("yml", yaml);

/**
 * Token class to color mapping for terminal display
 * Lowlight uses highlight.js classes which follow the pattern hljs-*
 */
const tokenColors: Record<string, string> = {
    "hljs-keyword": "magentaBright", // Keywords (import, export, const, etc.)
    "hljs-built_in": "cyanBright", // Built-ins
    "hljs-type": "cyanBright", // Types
    "hljs-literal": "cyanBright", // Literals (true, false, null)
    "hljs-number": "cyanBright", // Numbers
    "hljs-string": "greenBright", // Strings
    "hljs-regexp": "greenBright", // Regular expressions
    "hljs-function": "yellowBright", // Functions
    "hljs-title": "yellowBright", // Function/class names
    "hljs-params": "white", // Function parameters
    "hljs-comment": "gray", // Comments
    "hljs-doctag": "gray", // Documentation tags
    "hljs-meta": "gray", // Meta information
    "hljs-symbol": "magentaBright", // Symbols
    "hljs-class": "yellowBright", // Class names
    "hljs-attr": "cyanBright", // Attributes
    "hljs-variable": "blueBright", // Variables
    "hljs-template-variable": "blueBright", // Template variables
    "hljs-selector-tag": "magentaBright", // CSS/selector tags
    "hljs-selector-id": "yellowBright", // CSS IDs
    "hljs-selector-class": "yellowBright", // CSS classes
    "hljs-selector-attr": "cyanBright", // CSS attributes
    "hljs-selector-pseudo": "cyanBright", // CSS pseudo selectors
    "hljs-tag": "magentaBright", // HTML/XML tags
    "hljs-name": "magentaBright", // Tag names
    "hljs-attribute": "cyanBright", // HTML/XML attributes
    "hljs-link": "blueBright", // Links
    "hljs-bullet": "magentaBright", // List bullets
    "hljs-quote": "gray", // Quotes
    "hljs-deletion": "redBright", // Deleted text
    "hljs-addition": "greenBright", // Added text
    "hljs-emphasis": "italic", // Emphasized text
    "hljs-strong": "bold", // Strong text
};

/**
 * Custom component for rendering syntax highlighted spans
 */
const SyntaxSpan: React.FC<{ className?: string | string[]; children?: React.ReactNode }> = ({
    className,
    children,
}) => {
    // Handle className as either string or array
    const classes =
        typeof className === "string"
            ? className.split(" ")
            : Array.isArray(className)
              ? className
              : [];

    const colorClass = classes.find((cls) => typeof cls === "string" && tokenColors[cls]);
    const color =
        colorClass && typeof colorClass === "string" ? tokenColors[colorClass] : undefined;

    if (color) {
        return <Text color={color}>{children}</Text>;
    }

    return <>{children}</>;
};

/**
 * Components mapping for hast-util-to-jsx-runtime
 */
const components = {
    span: SyntaxSpan,
    // Default handler for any other elements
    [Symbol.for("hast.element")]: SyntaxSpan,
};

/**
 * Highlight code synchronously - returns highlighted JSX or plain text
 */
export function highlightCode(code: string, language: string | undefined): React.ReactNode {
    if (!language) {
        return code; // Return plain text if no language specified
    }

    try {
        const result = lowlight.highlight(language.toLowerCase(), code);
        return toJsxRuntime(result, {
            Fragment,
            jsx,
            jsxs,
            components,
            passKeys: true,
        });
    } catch (error) {
        // Language might not be registered, try auto-detection
        try {
            const result = lowlight.highlightAuto(code);
            return toJsxRuntime(result, {
                Fragment,
                jsx,
                jsxs,
                components,
                passKeys: true,
            });
        } catch (autoError) {
            console.error("Syntax highlighting error:", error);
            return code; // Fallback to plain text on error
        }
    }
}
