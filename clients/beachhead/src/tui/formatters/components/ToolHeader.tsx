import { Box, Text } from "ink";
import React from "react";

interface ToolHeaderProps {
    label: string;
    description: string;
}

export const ToolHeader: React.FC<ToolHeaderProps> = ({ label, description }) => (
    <Box>
        <Box marginRight={1}>
            <Text color="white" bold>
                »
            </Text>
        </Box>
        <Text color="white" bold>
            {label} <Text dimColor>- {description}</Text>
        </Text>
    </Box>
);
