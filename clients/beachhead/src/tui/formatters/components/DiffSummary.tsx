import { Box, Text } from "ink";
import React from "react";

import { ICONS } from "../utils/constants";

interface DiffSummaryProps {
    path: string;
    totalAdditions: number;
    totalRemovals: number;
}

export const DiffSummary: React.FC<DiffSummaryProps> = ({
    path,
    totalAdditions,
    totalRemovals,
}) => {
    const additionsText = totalAdditions > 0 && (
        <Text key="additions" color="green">
            {totalAdditions} addition{totalAdditions !== 1 ? "s" : ""}
        </Text>
    );

    const removalsText = totalRemovals > 0 && (
        <Text key="removals" color="red">
            {totalRemovals} removal{totalRemovals !== 1 ? "s" : ""}
        </Text>
    );

    return (
        <Box marginLeft={2}>
            <Text color="gray">
                {ICONS.CORNER}{" "}
                <Text dimColor color="white">
                    Edited{" "}
                </Text>
            </Text>
            <Text color="white">{path} </Text>
            <Text dimColor>with </Text>
            {additionsText ? additionsText : null}
            {additionsText && removalsText ? <Text dimColor> and </Text> : null}
            {removalsText ? removalsText : null}
            {!additionsText && !removalsText ? <Text dimColor>no changes</Text> : null}
        </Box>
    );
};
