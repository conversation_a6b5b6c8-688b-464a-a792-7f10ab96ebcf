import { Box, Text } from "ink";
import React from "react";

import { formatInlineMarkdown } from "../utils/markdown";

interface MarkdownTextProps {
    children: string;
    [key: string]: any;
}

export const MarkdownText = ({ children, ...props }: MarkdownTextProps) => {
    const formattedText = formatInlineMarkdown(children);

    return (
        <Box flexDirection="column">
            <Text {...props}>{formattedText}</Text>
        </Box>
    );
};
