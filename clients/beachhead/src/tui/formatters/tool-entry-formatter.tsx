import React from "react";

import { ToolEntry } from "../types/transcript-types";
import {
    CodebaseRetrievalTool,
    EditTool,
    GenericTool,
    KillProcessTool,
    LaunchProcessTool,
    ListProcessesTool,
    ReadProcessTool,
    RemoveFilesTool,
    RenderMermaidTool,
    SaveFileTool,
    ViewTool,
    WebFetchTool,
    WebSearchTool,
} from "./tools";

/*
 * Complete list of tools that need formatters:
 *
 * Currently implemented:
 * ✓ codebase-retrieval
 * ✓ kill-process
 * ✓ launch-process
 * ✓ list-processes
 * ✓ read-process
 * ✓ remove-files
 * ✓ save-file
 * ✓ str-replace-editor
 * ✓ view
 * ✓ web-search
 * ✓ web-fetch
 * ✓ render-mermaid
 *
 * LocalToolType tools (from tool-types.ts):
 * - clarify
 * - onboarding-sub-agent
 * - write-process
 * - wait-process
 * - open-browser
 * - remember
 * - diagnostics
 * - setup-script
 * - git-commit-retrieval
 * - spawn-sub-agent
 *
 * SidecarToolType tools (from sidecar-tool-types.ts):
 * - shell
 * - remember
 * - view_tasklist
 * - view-range-untruncated
 * - search-untruncated
 * - reorganize_tasklist
 * - update_tasks
 * - add_tasks
 * - grep-search
 *
 * Additional tools that may exist:
 * - github-api
 * - linear
 * - glean
 *
 */

// Component to format ToolEntry
export const ToolEntryFormatter = React.memo<{ entry: ToolEntry }>(({ entry }) => {
    switch (entry.toolName) {
        case "codebase-retrieval":
            return <CodebaseRetrievalTool entry={entry} />;
        case "kill-process":
            return <KillProcessTool entry={entry} />;
        case "launch-process":
            return <LaunchProcessTool entry={entry} />;
        case "list-processes":
            return <ListProcessesTool entry={entry} />;
        case "read-process":
            return <ReadProcessTool entry={entry} />;
        case "remove-files":
            return <RemoveFilesTool entry={entry} />;
        case "render-mermaid":
            return <RenderMermaidTool entry={entry} />;
        case "save-file":
            return <SaveFileTool entry={entry} />;
        case "str-replace-editor":
            return <EditTool entry={entry} />;
        case "view":
            return <ViewTool entry={entry} />;
        case "web-fetch":
            return <WebFetchTool entry={entry} />;
        case "web-search":
            return <WebSearchTool entry={entry} />;
        default:
            return <GenericTool entry={entry} />;
    }
});
