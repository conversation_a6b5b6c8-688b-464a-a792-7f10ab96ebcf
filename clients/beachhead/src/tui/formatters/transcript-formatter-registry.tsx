import { Text, Box } from "ink";
import React from "react";

import {
    AgentEntry,
    TranscriptEntry,
    TranscriptEntryType,
    UserEntry,
    ToolEntry,
    ErrorEntry,
    SystemEntry,
} from "../types/transcript-types";
// Import formatters
import { AgentEntryFormatter } from "./agent-entry-formatter";
import { UserEntryFormatter } from "./user-entry-formatter";
import { ToolEntryFormatter } from "./tool-entry-formatter";
import { ErrorEntryFormatter } from "./error-entry-formatter";
import { SystemEntryFormatter } from "./system-entry-formatter";

// Type for a formatter function that takes an entry and returns a React element
export type TranscriptFormatter<T extends TranscriptEntry = TranscriptEntry> = (
    entry: T
) => React.ReactElement;

// Registry to store formatters for each entry type
export class TranscriptFormatterRegistry {
    private formatters = new Map<TranscriptEntryType, TranscriptFormatter>();

    register<T extends TranscriptEntry>(
        type: TranscriptEntryType,
        formatter: TranscriptFormatter<T>
    ): void {
        this.formatters.set(type, formatter as TranscriptFormatter);
    }

    format(entry: TranscriptEntry): React.ReactElement {
        const formatter = this.formatters.get(entry.type);
        if (!formatter) {
            // Fallback formatter for unregistered types - show full data structure
            return React.createElement(
                Box,
                { flexDirection: "column", marginBottom: 1 },
                React.createElement(
                    Text,
                    { color: "yellow", bold: true },
                    `[${entry.type}] No formatter registered - Data structure:`
                ),
                React.createElement(
                    Text,
                    { color: "gray" },
                    JSON.stringify(entry, null, 2)
                )
            );
        }
        return formatter(entry);
    }

    hasFormatter(type: TranscriptEntryType): boolean {
        return this.formatters.has(type);
    }
}

// Create and export a singleton registry instance
export const transcriptFormatterRegistry = new TranscriptFormatterRegistry();

// Register all formatters
transcriptFormatterRegistry.register<AgentEntry>(TranscriptEntryType.Agent, (entry) => (
    <AgentEntryFormatter entry={entry} />
));

transcriptFormatterRegistry.register<UserEntry>(TranscriptEntryType.User, (entry) => (
    <UserEntryFormatter entry={entry} />
));

transcriptFormatterRegistry.register<ToolEntry>(TranscriptEntryType.Tool, (entry) => (
    <ToolEntryFormatter entry={entry} />
));

transcriptFormatterRegistry.register<ErrorEntry>(TranscriptEntryType.Error, (entry) => (
    <ErrorEntryFormatter entry={entry} />
));

transcriptFormatterRegistry.register<SystemEntry>(TranscriptEntryType.System, (entry) => (
    <SystemEntryFormatter entry={entry} />
));
