import React from "react";
import { ToolEntry } from "../../types/transcript-types";
import { ResultSummary, Tool<PERSON><PERSON>ult, ToolStart } from "../components";

interface ParsedKillProcessOutput {
    message: string;
}

function parseOutput(text: string): ParsedKillProcessOutput {
    // Extract the terminal killed message (e.g., "Terminal 1 killed")
    const killedMessage = text.split("\n")[0] || "Process terminated";
    return { message: killedMessage };
}

export const KillProcessTool = React.memo<{ entry: ToolEntry }>(({ entry }) => {
    if (entry.phase === "start") {
        return <ToolStart label="Terminate process" description="kill process" />;
    }

    // Result phase
    const hasError = entry.output?.isError || false;
    const outputText = entry.output?.text || "";

    const parsed = parseOutput(outputText);

    return (
        <ToolResult>
            <ResultSummary
                text={hasError ? (outputText || "Failed to kill process") : parsed.message}
                color={hasError ? "red" : undefined}
            />
        </ToolResult>
    );
});
