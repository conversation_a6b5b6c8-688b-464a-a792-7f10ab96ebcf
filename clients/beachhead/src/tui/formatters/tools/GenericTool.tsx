import { Box, Text } from "ink";
import React from "react";

import { ToolEntry } from "../../types/transcript-types";
import { OutputPreview, ResultItem, ResultSummary, ToolResult, ToolStart } from "../components";

export const GenericTool = React.memo<{ entry: ToolEntry }>(({ entry }) => {
    if (entry.phase === "start") {
        const summary = entry.input?.summary || "";
        return (
            <Box flexDirection="column">
                <ToolStart label={entry.toolName} description="running tool" />
                <ResultItem>
                    <Text dimColor>{summary}</Text>
                </ResultItem>
            </Box>
        );
    }

    // Result phase
    const hasError = entry.output?.isError || false;
    const outputText = entry.output?.text || "";
    const outputLines = outputText.split("\n");

    return (
        <ToolResult>
            <ResultSummary text={`Completed ${entry.toolName}`} />
            {hasError && <ResultSummary text={`Error in ${entry.toolName}`} color="red" />}
            <OutputPreview lines={outputLines} />
        </ToolResult>
    );
});
