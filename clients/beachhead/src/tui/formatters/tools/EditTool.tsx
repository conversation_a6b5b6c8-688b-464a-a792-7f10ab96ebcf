import {
    extractInsertLineEntries,
    extractStrReplaceEntries,
} from "@augment-internal/sidecar-libs/src/tools/sidecar-tools/str-replace-editor-tool/utils";
import { Box, Text } from "ink";
import React from "react";

import { ToolEntry } from "../../types/transcript-types";
import { Diff, ToolR<PERSON>ult, ToolStart } from "../components";

interface StrReplaceInput extends ToolEntry {
    command: "str_replace";
    path: string;
    instruction_reminder: string;
    old_str_1?: string;
    new_str_1?: string;
    old_str_start_line_number_1?: number;
    old_str_end_line_number_1?: number;
    [key: string]: any; // For additional numbered entries
}

interface InsertInput extends ToolEntry {
    command: "insert";
    path: string;
    instruction_reminder: string;
    insert_line_1?: number;
    new_str_1?: string;
    [key: string]: any; // For additional numbered entries
}

export const EditTool = React.memo<{ entry: ToolEntry }>(({ entry }) => {
    const path = entry.input?.path || "unknown";

    if (entry.phase === "start") {
        const command = entry.input?.command;
        const entries =
            command === "str_replace"
                ? extractStrReplaceEntries(entry.input as StrReplaceInput)
                : extractInsertLineEntries(entry.input as InsertInput);

        return (
            <>
                <ToolStart label={path} description="edit file" />
                <Box flexDirection="column" marginLeft={2}>
                    <Diff entries={entries} path={path} type={command} />
                </Box>
            </>
        );
    }

    // Result phase
    const hasError = entry.output?.isError || false;

    return (
        <ToolResult>
            {hasError && (
                <Box marginLeft={2}>
                    <Text color="red" dimColor>
                        * There was a problem editing the file.
                    </Text>
                </Box>
            )}
            <Text> </Text>
        </ToolResult>
    );
});
