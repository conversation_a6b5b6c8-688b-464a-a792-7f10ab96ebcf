import { Box, Text } from "ink";
import React from "react";

import { ToolEntry } from "../../types/transcript-types";
import { OutputPreview, ResultItem, ResultSummary, ToolResult, ToolStart } from "../components";

function parseOutput(text: string): string[] {
    const pathRegex = /^Path:\s+(.+)$/gm;
    const matches = [...text.matchAll(pathRegex)];
    const filePaths = matches.map((match) => match[1].trim());

    return filePaths;
}

export const CodebaseRetrievalTool = React.memo<{ entry: ToolEntry }>(({ entry }) => {
    if (entry.phase === "start") {
        return (
            <>
                <ToolStart label="Codebase search" description="context engine" />
                <ResultItem>
                    <Text dimColor>{entry.input?.information_request}</Text>
                </ResultItem>
            </>
        );
    }

    // Result phase
    const hasError = entry.output?.isError || false;
    const outputText = entry.output?.text || "";

    const filePaths = parseOutput(outputText);

    return (
        <ToolResult>
            <ResultSummary
                text={
                    hasError
                        ? outputText || "Search failed"
                        : `Found ${filePaths.length} file${filePaths.length === 1 ? "" : "s"}`
                }
                color={hasError ? "red" : undefined}
            />
            {!hasError && filePaths.length > 0 && (
                <Box flexDirection="column" marginLeft={2}>
                    <OutputPreview lines={filePaths} />
                </Box>
            )}
        </ToolResult>
    );
});
