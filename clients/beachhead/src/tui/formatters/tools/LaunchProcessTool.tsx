import { Box, Text } from "ink";
import React from "react";

import { ToolEntry } from "../../types/transcript-types";
import { OutputPreview, ResultItem, ResultSummary, ToolResult, ToolStart } from "../components";

const parseOutput = (
    outputText: string
): {
    returnCode: number | null;
    output: string;
} => {
    // Extract return code from <return-code> tags
    const returnCodeMatch = outputText.match(/<return-code>\s*(\d+)\s*<\/return-code>/);
    const returnCode = returnCodeMatch ? parseInt(returnCodeMatch[1]) : null;

    // Extract output content from <output> tags
    const outputMatch = outputText.match(/<output>\s*([\s\S]*?)\s*<\/output>/);
    let output = outputMatch ? outputMatch[1].trim() : "";

    // Remove the first line if it exists
    if (output) {
        const lines = output.split("\n");
        if (lines.length > 1) {
            output = lines.slice(1).join("\n");
        }
    }

    return { returnCode, output };
};

export const LaunchProcessTool = React.memo<{ entry: ToolEntry }>(({ entry }) => {
    if (entry.phase === "start") {
        const command = entry.input?.command || "unknown command";
        return <ToolStart label={command} description="running command" />;
    }

    // Result phase
    const hasError = entry.output?.isError || false;
    const outputText = entry.output?.text || "";

    if (hasError) {
        // Parse the output block for error description
        const outputMatch = outputText.match(/<output>([\s\S]*?)<\/output>/);
        const errorMessage = outputMatch ? outputMatch[1].trim() : "Command failed";

        return (
            <ToolResult>
                <ResultSummary text="Command error" color="red" />
                <ResultItem>
                    <Text color="white" dimColor>
                        {errorMessage}
                    </Text>
                </ResultItem>
            </ToolResult>
        );
    }

    // Success case - show full entry data structure
    const { returnCode, output } = parseOutput(outputText);

    // Determine if command was successful (null or 0) or failed (> 0)
    const isSuccess = returnCode === null || returnCode === 0;
    const summaryText = isSuccess ? "Command completed" : "Command error";
    const summaryColor = isSuccess ? "green" : "red";

    return (
        <ToolResult>
            <ResultSummary text={summaryText} color={summaryColor} />
            {output && (
                <Box flexDirection="column" marginLeft={2} marginTop={1}>
                    <Text color="white" dimColor>
                        Parsed output:
                    </Text>
                    <OutputPreview lines={output.split("\n")} />
                </Box>
            )}
        </ToolResult>
    );
});
