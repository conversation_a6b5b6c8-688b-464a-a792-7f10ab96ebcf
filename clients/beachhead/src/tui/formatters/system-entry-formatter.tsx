import { Box, Text } from "ink";
import React from "react";

import { SystemEntry } from "../types/transcript-types";
import { ICONS } from "./utils/constants";

// Component to format SystemEntry
export const SystemEntryFormatter: React.FC<{ entry: SystemEntry }> = ({ entry }) => {
    const boxProps = {
        marginY: 1,
        width: "100%",
        minWidth: "100%",
        ...(entry.hasBorder && {
            borderStyle: "round" as const,
            borderColor: "green",
            borderDimColor: true,
            paddingX: 1,
        }),
    };

    return (
        <Box {...boxProps}>
            <Text color="white">
                {"\n"}
                <Text dimColor>{ICONS.SYSTEM}</Text> {entry.message}
            </Text>
        </Box>
    );
};
