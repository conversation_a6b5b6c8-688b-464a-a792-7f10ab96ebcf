import { Box, Text } from "ink";
import React from "react";

interface InputDisplayProps {
    wrappedLines: string[];
    cursorLine: number;
    cursorColumn: number;
    placeholder?: string;
}

/**
 * InputDisplay - Pure presentational component that renders text lines with a cursor
 *
 * This component is responsible only for rendering:
 * - Text lines with proper formatting
 * - Cursor at the specified position
 * - Placeholder text when value is empty
 *
 * All calculations (text wrapping, cursor position) are done by the parent component.
 */
export const InputDisplay: React.FC<InputDisplayProps> = ({
    wrappedLines,
    cursorLine,
    cursorColumn,
    placeholder = "",
}) => {
    // calculateWrappedText returns [""] for empty text
    const isShowingPlaceholder = wrappedLines.length === 1 && wrappedLines[0] === "" && placeholder;
    const lines = isShowingPlaceholder ? [placeholder] : wrappedLines;

    // When showing placeholder, cursor is always at position 0
    const effectiveCursorLine = isShowingPlaceholder ? 0 : cursorLine;
    const effectiveCursorColumn = isShowingPlaceholder ? 0 : cursorColumn;

    return (
        <Box flexDirection="column" width="100%">
            {lines.map((line, lineIndex) => (
                <Box key={lineIndex} width="100%">
                    {lineIndex === effectiveCursorLine ? (
                        // Line with cursor
                        <>
                            <Text color={isShowingPlaceholder ? "gray" : undefined}>
                                {line.substring(0, effectiveCursorColumn)}
                            </Text>
                            <Text inverse>{line[effectiveCursorColumn] || " "}</Text>
                            <Text color={isShowingPlaceholder ? "gray" : undefined}>
                                {line.substring(effectiveCursorColumn + 1)}
                            </Text>
                        </>
                    ) : (
                        // Line without cursor
                        <Text color={isShowingPlaceholder ? "gray" : undefined}>{line || " "}</Text>
                    )}
                </Box>
            ))}
        </Box>
    );
};
