/**
 * Notification types for the TUI system
 */

/**
 * Valid notification types
 */
export type NotificationType = "error" | "success" | "info";

/**
 * Notification data structure with optional timeout
 */
export interface NotificationData {
    message: string;
    type: NotificationType;
    timeout?: number;
}

/**
 * Notification state type (can be null when no notification is shown)
 */
export type NotificationState = NotificationData | null;

/**
 * Notification setter function type
 */
export type NotificationSetter = (notification: NotificationState) => void;
