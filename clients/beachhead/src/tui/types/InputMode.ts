/**
 * Input mode types for the TUI application
 */

/**
 * Enum representing the different input modes
 */
export enum InputMode {
    Normal = "normal",
    Command = "command",
}

/**
 * Configuration for each input mode
 */
export interface InputModeConfig {
    /** The mode identifier */
    mode: InputMode;
    /** Display name for the mode */
    displayName: string;
    /** Border color when focused */
    borderColor: string;
    /** Prompt prefix (e.g., "> " for normal, ":" for command) */
    promptPrefix: string;
    /** Placeholder text when input is empty */
    placeholder: string;
    /** Help text to display in the footer */
    helpText: string;
}

/**
 * Type-safe configuration map for all input modes
 */
export const INPUT_MODE_CONFIGS: Record<InputMode, InputModeConfig> = {
    [InputMode.Normal]: {
        mode: InputMode.Normal,
        displayName: "Normal",
        borderColor: "grey",
        promptPrefix: "›",
        placeholder: "Try 'how do I log an error?' or type / for commands",
        helpText: "Type / for commands • ↑↓ for history • Esc/Ctrl+C to interrupt",
    },
    [InputMode.Command]: {
        mode: InputMode.Command,
        displayName: "Command",
        borderColor: "blue",
        promptPrefix: "/",
        placeholder: "Enter command...",
        helpText: "↑↓ to select • Enter to run • Esc to cancel",
    },
};

/**
 * Helper function to get configuration for a specific mode
 */
export function getInputModeConfig(mode: InputMode): InputModeConfig {
    return INPUT_MODE_CONFIGS[mode];
}

/**
 * Type guard to check if a string is a valid InputMode
 */
export function isValidInputMode(mode: string): mode is InputMode {
    return Object.values(InputMode).includes(mode as InputMode);
}
