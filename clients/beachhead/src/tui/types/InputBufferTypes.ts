/**
 * InputBuffer state and action types for useReducer
 */

/**
 * The complete state of the input buffer
 */
export interface InputBufferState {
    // Text content
    text: string;

    // Cursor state
    cursorPosition: number;
    desiredColumn: number | null; // For maintaining column during vertical navigation

    // Paste state
    isPasting: boolean;
    pasteBuffer: string;

    // UI dimensions (derived state - could be computed)
    availableWidth: number;

    // Configuration (could be props instead of state)
    placeholder: string;
    promptPrefix: string;
}

/**
 * All possible actions that can modify the input buffer state
 */
export type InputBufferAction =
    // Text manipulation actions
    | { type: "SET_TEXT"; text: string }
    | { type: "INSERT_TEXT"; text: string; position?: number } // position defaults to current cursor
    | { type: "DELETE_RANGE"; start: number; end: number }
    | { type: "DELETE_CHAR_BACKWARD" } // backspace
    | { type: "DELETE_CHAR_FORWARD" } // delete key
    | { type: "DELETE_WORD_BACKWARD" }
    | { type: "DELETE_WORD_FORWARD" }
    | { type: "DELETE_TO_LINE_START" } // Ctrl+U
    | { type: "DELETE_TO_LINE_END" } // Ctrl+K
    | { type: "TRANSPOSE_CHARS" } // Ctrl+T

    // Cursor movement actions
    | { type: "MOVE_CURSOR"; position: number; preserveDesiredColumn?: boolean }
    | { type: "MOVE_CURSOR_LEFT"; count?: number }
    | { type: "MOVE_CURSOR_RIGHT"; count?: number }
    | { type: "MOVE_CURSOR_UP" }
    | { type: "MOVE_CURSOR_DOWN" }
    | { type: "MOVE_CURSOR_WORD_LEFT" }
    | { type: "MOVE_CURSOR_WORD_RIGHT" }
    | { type: "MOVE_CURSOR_START" } // Ctrl+A
    | { type: "MOVE_CURSOR_END" } // Ctrl+E
    | { type: "SET_DESIRED_COLUMN"; column: number | null }

    // Paste actions
    | { type: "START_PASTE" }
    | { type: "APPEND_PASTE_CONTENT"; content: string }
    | { type: "END_PASTE"; finalContent?: string }
    | { type: "CANCEL_PASTE" }

    // Configuration actions
    | { type: "SET_AVAILABLE_WIDTH"; width: number }
    | { type: "SET_PLACEHOLDER"; placeholder: string }
    | { type: "SET_PROMPT_PREFIX"; promptPrefix: string }

    // Composite actions (combinations of simpler actions)
    | { type: "INSERT_NEWLINE" }; // Shift+Enter or Ctrl+J

/**
 * Action creators for better ergonomics
 */
export const InputBufferActions = {
    // Text actions
    setText: (text: string): InputBufferAction => ({ type: "SET_TEXT", text }),
    insertText: (text: string, position?: number): InputBufferAction => ({
        type: "INSERT_TEXT",
        text,
        position,
    }),
    deleteRange: (start: number, end: number): InputBufferAction => ({
        type: "DELETE_RANGE",
        start,
        end,
    }),

    // Cursor actions
    moveCursor: (position: number, preserveDesiredColumn = false): InputBufferAction => ({
        type: "MOVE_CURSOR",
        position,
        preserveDesiredColumn,
    }),
    moveCursorLeft: (count = 1): InputBufferAction => ({ type: "MOVE_CURSOR_LEFT", count }),
    moveCursorRight: (count = 1): InputBufferAction => ({ type: "MOVE_CURSOR_RIGHT", count }),

    // Paste actions
    startPaste: (): InputBufferAction => ({ type: "START_PASTE" }),
    appendPasteContent: (content: string): InputBufferAction => ({
        type: "APPEND_PASTE_CONTENT",
        content,
    }),
    endPaste: (finalContent?: string): InputBufferAction => ({
        type: "END_PASTE",
        finalContent,
    }),

    // Configuration actions
    setAvailableWidth: (width: number): InputBufferAction => ({
        type: "SET_AVAILABLE_WIDTH",
        width,
    }),
} as const;

/**
 * Initial state factory
 */
export const createInitialInputBufferState = (
    initialText = "",
    availableWidth = 80,
    placeholder = "",
    promptPrefix = ""
): InputBufferState => ({
    text: initialText,
    cursorPosition: initialText.length,
    desiredColumn: null,
    isPasting: false,
    pasteBuffer: "",
    availableWidth,
    placeholder,
    promptPrefix,
});

/**
 * Type guards for action types
 */
export const isTextAction = (action: InputBufferAction): boolean => {
    return [
        "SET_TEXT",
        "INSERT_TEXT",
        "DELETE_RANGE",
        "DELETE_CHAR_BACKWARD",
        "DELETE_CHAR_FORWARD",
        "DELETE_WORD_BACKWARD",
        "DELETE_WORD_FORWARD",
        "DELETE_TO_LINE_START",
        "DELETE_TO_LINE_END",
        "TRANSPOSE_CHARS",
        "INSERT_NEWLINE",
    ].includes(action.type);
};

export const isCursorAction = (action: InputBufferAction): boolean => {
    return action.type.startsWith("MOVE_CURSOR") || action.type === "SET_DESIRED_COLUMN";
};

export const isPasteAction = (action: InputBufferAction): boolean => {
    return action.type.includes("PASTE");
};
