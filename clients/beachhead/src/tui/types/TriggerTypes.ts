/**
 * Types for trigger configuration options and configs
 */

export interface TriggerOption {
    type: "boolean" | "text" | "select";
    key: string;
    label: string;
    description: string;
    required?: boolean;
    defaultValue?: any;
    placeholder?: string;
    options?: Array<{ value: any; label: string }>;
}

export interface NewPRConfig {
    includeDrafts: boolean;
}

export interface LabelConfig {
    labelName: string;
}

export type TriggerConfig = NewPRConfig | LabelConfig;
