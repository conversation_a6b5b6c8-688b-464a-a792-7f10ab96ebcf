/**
 * Shared types for workflow wizard steps
 */

import { WorkflowConfig } from "./WorkflowTypes";

export interface BaseStepProps {
    config: WorkflowConfig;
    onUpdateConfig: (updates: Partial<WorkflowConfig>) => void;
    onNext: () => void;
    onPrevious: () => void;
    isFirstStep: boolean;
    isLastStep: boolean;
    currentStepIndex?: number;
    totalSteps?: number;
    isGenerating?: boolean;
    isComplete?: boolean;
    onGenerate?: () => void;
}

export interface SelectStepOption {
    value: any;
    label: string;
    description: string;
}

export interface ToggleField {
    key: string;
    label: string;
    description: string;
    defaultValue?: boolean;
}

export interface InputField {
    key: string;
    label: string;
    description: string;
    required?: boolean;
    placeholder?: string;
    defaultValue?: string;
}

export interface DisplayItem {
    label: string;
    value: string;
}
