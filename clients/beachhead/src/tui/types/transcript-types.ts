// Enhanced transcript types with rich data support

export enum TranscriptEntryType {
    User = "user",
    Agent = "agent",
    Error = "error",
    Command = "command",
    Tool = "tool",
    System = "system",
}

// Base interface for all transcript entries
export interface BaseTranscriptEntry {
    key: string;
    timestamp: Date;
    type: TranscriptEntryType;
}

// User entry - simple string content
export interface UserEntry extends BaseTranscriptEntry {
    type: TranscriptEntryType.User;
    content: string;
}

// Agent entry
export interface AgentEntry extends BaseTranscriptEntry {
    type: TranscriptEntryType.Agent;
    content: string;
    isStreaming: boolean;
}

// Tool entry - includes tool execution details
export interface ToolEntry extends BaseTranscriptEntry {
    type: TranscriptEntryType.Tool;
    toolName: string;
    phase: "start" | "result";
    input?: Record<string, any>;
    output?: {
        text: string;
        isError: boolean;
    };
    duration?: number; // Time between start and result in ms
}

// Error entry - includes context
export interface ErrorEntry extends BaseTranscriptEntry {
    type: TranscriptEntryType.Error;
    error: string;
    context?: string;
}

// Command entry - for slash commands
export interface CommandEntry extends BaseTranscriptEntry {
    type: TranscriptEntryType.Command;
    command: string;
    args?: string[];
}

// System entry - for system messages
export interface SystemEntry extends BaseTranscriptEntry {
    type: TranscriptEntryType.System;
    message: string;
    hasBorder: boolean;
}

// Union type for all transcript entries
export type TranscriptEntry =
    | UserEntry
    | AgentEntry
    | ToolEntry
    | ErrorEntry
    | CommandEntry
    | SystemEntry;

// Type guards
export function isUserEntry(entry: TranscriptEntry): entry is UserEntry {
    return entry.type === TranscriptEntryType.User;
}

export function isAgentEntry(entry: TranscriptEntry): entry is AgentEntry {
    return entry.type === TranscriptEntryType.Agent;
}

export function isToolEntry(entry: TranscriptEntry): entry is ToolEntry {
    return entry.type === TranscriptEntryType.Tool;
}

export function isErrorEntry(entry: TranscriptEntry): entry is ErrorEntry {
    return entry.type === TranscriptEntryType.Error;
}

export function isCommandEntry(entry: TranscriptEntry): entry is CommandEntry {
    return entry.type === TranscriptEntryType.Command;
}

export function isSystemEntry(entry: TranscriptEntry): entry is SystemEntry {
    return entry.type === TranscriptEntryType.System;
}
