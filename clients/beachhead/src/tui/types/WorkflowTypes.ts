/**
 * Core types for GitHub workflow generation
 */

import { BaseAction } from "../actions/BaseAction";
import { BaseTrigger } from "../triggers/BaseTrigger";

export interface WorkflowConfig {
    action?: BaseAction;
    trigger?: BaseTrigger;
    triggerConfig?: any; // Will be typed based on trigger
}

export interface WorkflowStep {
    id: string;
    title: string;
    description: string;
}

export type WorkflowMode = "generation" | "setup";

export interface WorkflowWizardState {
    mode: WorkflowMode;
    currentStep: number;
    config: WorkflowConfig;
    isComplete: boolean;
    isGenerating: boolean;
    error?: string;
}

// Generation workflow steps
export const GENERATION_STEPS: WorkflowStep[] = [
    {
        id: "action",
        title: "Select Action",
        description: "Choose what you want to automate",
    },
    {
        id: "trigger",
        title: "Select Trigger",
        description: "Choose when the action should run",
    },
    {
        id: "options",
        title: "Configure Options",
        description: "Set additional options for your workflow",
    },
    {
        id: "confirm",
        title: "Review Configuration",
        description: "Review your settings before generating",
    },
    {
        id: "generating",
        title: "Generating Workflow",
        description: "Creating your GitHub workflow file",
    },
];

// Setup workflow steps
export const SETUP_STEPS: WorkflowStep[] = [
    {
        id: "setup-overview",
        title: "Setup Overview",
        description: "What you need to configure",
    },
    {
        id: "setup-credentials",
        title: "Get Credentials",
        description: "Find your Augment session file",
    },
    {
        id: "setup-github-nav",
        title: "Navigate to GitHub",
        description: "Find the right settings page",
    },
    {
        id: "setup-github-secret",
        title: "Add Session Auth Secret",
        description: "Configure the AUGMENT_SESSION_AUTH secret",
    },
    {
        id: "setup-deploy",
        title: "Deploy Workflow",
        description: "Commit and push your workflow",
    },
    {
        id: "complete",
        title: "Complete",
        description: "Setup completed successfully",
    },
];

// Combined for backward compatibility (if needed elsewhere)
export const WORKFLOW_STEPS: WorkflowStep[] = [...GENERATION_STEPS, ...SETUP_STEPS];
