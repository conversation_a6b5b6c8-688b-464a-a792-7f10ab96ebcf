/**
 * Key Processor Types - Types for the hook-based key processing system
 *
 * This module defines the types for the simplified hook-based input processing:
 * useKeyPressHandler -> KeyProcessor functions -> InputBufferReducer
 */
import { ProcessingState } from "../hooks/useAgent";
import { InputBufferAction, InputBufferState } from "./InputBufferTypes";
import { InputMode } from "./InputMode";

/**
 * Keyboard event information from Ink
 */
export interface KeyEvent {
    /** The input string (character or special sequence) */
    input: string;
    /** Key flags and information from Ink */
    key: {
        upArrow?: boolean;
        downArrow?: boolean;
        leftArrow?: boolean;
        rightArrow?: boolean;
        return?: boolean;
        escape?: boolean;
        ctrl?: boolean;
        shift?: boolean;
        meta?: boolean;
        backspace?: boolean;
        delete?: boolean;
        tab?: boolean;
        pageDown?: boolean;
        pageUp?: boolean;
    };
}

/**
 * Context information available to processors
 */
export interface ProcessingContext {
    /** Current buffer state */
    bufferState?: InputBufferState;
    /** Dispatch function for buffer actions */
    dispatch?: (action: InputBufferAction) => void;
    /** Current input mode */
    mode?: InputMode;
    /** Current agent processing state */
    processingState?: ProcessingState;
    /** Whether a menu is currently shown */
    menu?: boolean;
    /** Whether a popover (like help) is currently shown */
    popover?: boolean;
    /** Stable ref for paste buffer content */
    pasteBufferRef?: React.MutableRefObject<string>;
    /** Stable ref for paste mode state */
    isPastingRef?: React.MutableRefObject<boolean>;
}

/**
 * Result of processing a key event
 */
export interface KeyProcessingResult {
    /** Actions to dispatch to the InputBufferReducer */
    actions: InputBufferAction[];
    /** Whether the event was handled and should not bubble up */
    handled: boolean;
    /** Optional side effects (hooks, commands, etc.) */
    sideEffects?: SideEffect[];
}

/**
 * Side effects that can be triggered by key processing
 */
export interface SideEffect {
    type:
        | "TRIGGER_SLASH_COMMAND"
        | "TRIGGER_HOOK"
        | "SET_NOTIFICATION"
        | "SET_MODE"
        | "EXECUTE_COMMAND"
        | "ADD_SYSTEM_ENTRY"
        | "CUSTOM";
    payload?: any;
}

/**
 * Key processor interface
 */
export interface KeyProcessor {
    /**
     * Check if this processor can handle the given event
     */
    canHandle: (event: KeyEvent, context: ProcessingContext) => boolean;

    /**
     * Process the event and return the result
     */
    process: (event: KeyEvent, context: ProcessingContext) => KeyProcessingResult;

    /**
     * Priority of this processor (higher = processed first)
     */
    priority?: number;

    /**
     * Optional name for debugging
     */
    name?: string;
}

/**
 * Hook function that can be triggered by side effects
 */
export type HookFunction = (payload?: any) => void;

/**
 * Registry of hook functions
 */
export interface HookRegistry {
    slashCommand?: HookFunction;
    [key: string]: HookFunction | undefined;
}

/**
 * Configuration for useKeyPressHandler
 */
export interface UseKeyPressHandlerOptions {
    /** Key processors to use */
    processors?: KeyProcessor[];
    /** Hook registry for side effects */
    hookRegistry?: HookRegistry;
    /** Whether to prevent default behavior */
    preventDefault?: boolean;
    /** Whether to stop propagation */
    stopPropagation?: boolean;
    /** Whether the handler is active */
    active?: boolean;
    /** Whether to enable debug logging */
    debug?: boolean;
}

/**
 * Return type for useKeyPressHandler
 */
export interface UseKeyPressHandlerReturn {
    /** The key press handler function */
    handleKeyPress: (event: KeyboardEvent) => void;
    /** Reference to attach to the container element */
    containerRef: React.RefObject<HTMLDivElement | null>;
}

/**
 * Helper type for creating processors
 */
export type ProcessorFactory<T = any> = (options?: T) => KeyProcessor;
