/**
 * Types for structured trigger configurations
 */

export interface PullRequestTriggerConfig {
    event: "pull_request";
    types?: string[];
    branches?: string[];
    paths?: string[];
}

export interface PushTriggerConfig {
    event: "push";
    branches?: string[];
    paths?: string[];
    tags?: string[];
}

export interface ScheduleTriggerConfig {
    event: "schedule";
    cron: string[];
}

export interface WorkflowDispatchTriggerConfig {
    event: "workflow_dispatch";
    inputs?: Record<string, any>;
}

export interface IssuesTriggerConfig {
    event: "issues";
    types?: string[];
}

export interface ReleasesTriggerConfig {
    event: "release";
    types?: string[];
}

export type TriggerEventConfig =
    | PullRequestTriggerConfig
    | PushTriggerConfig
    | ScheduleTriggerConfig
    | WorkflowDispatchTriggerConfig
    | IssuesTriggerConfig
    | ReleasesTriggerConfig;
