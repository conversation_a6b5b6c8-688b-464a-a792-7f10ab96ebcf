import { useCallback } from "react";

import { ToolHostName } from "@augment-internal/sidecar-libs/src/tools/tool-types";
import { ToolsModel } from "@augment-internal/sidecar-libs/src/tools/tools-model";
import { SideEffect } from "../types/KeyProcessorTypes";
import { createSystemEntrySideEffect } from "./useKeypressRouter";

interface UseMcpStatusOptions {
    toolsModel: ToolsModel;
}

/**
 * Hook for handling MCP status display command
 */
export function useMcpStatus({ toolsModel }: UseMcpStatusOptions) {
    const handleMcpStatus = useCallback((): SideEffect[] => {
        // Get all MCP hosts from the tools model
        const mcpHosts = toolsModel.hosts.filter((host) => host.getName() === ToolHostName.mcpHost);

        if (mcpHosts.length === 0) {
            return [
                createSystemEntrySideEffect(
                    "No MCP servers configured, use --mcp-config to add servers",
                    false
                ),
            ];
        }

        // Check each MCP host status synchronously (using current state)
        const statusResults: string[] = [];

        for (const host of mcpHosts) {
            try {
                const initStatus = (host as any).getInitializationStatus?.() || "unknown";
                // Get server name from config instead of async tools call
                const serverName = (host as any)._config?.name || "unknown";

                let statusIcon: string;
                let statusText: string;

                if (initStatus === "success") {
                    // Try to get tool count synchronously if possible
                    const toolCount = (host as any)._tools?.length || 0;
                    if (toolCount > 0) {
                        statusIcon = "✅";
                        statusText = `${serverName} (${toolCount} tool${toolCount !== 1 ? "s" : ""})`;
                    } else {
                        statusIcon = "✅";
                        statusText = `${serverName} (ready)`;
                    }
                } else if (initStatus === "failed") {
                    statusIcon = "❌";
                    statusText = `${serverName} (failed)`;
                } else {
                    statusIcon = "⏳";
                    statusText = `${serverName} (initializing...)`;
                }

                const result = `${statusIcon} ${statusText}`;
                statusResults.push(result);
            } catch (error) {
                const serverName = (host as any)._config?.name || "unknown";
                const errorMessage = error instanceof Error ? error.message : String(error);
                const result = `❌ ${serverName} (error: ${errorMessage})`;
                statusResults.push(result);
            }
        }

        // Show results with proper formatting
        const statusOutput = ["MCP Server Status:", ...statusResults.map(result => `  ${result}`)].join('\n');
        return [createSystemEntrySideEffect(statusOutput, false)];
    }, [toolsModel]);

    return handleMcpStatus;
}
