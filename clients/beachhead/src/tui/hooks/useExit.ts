import { useApp } from "ink";
import { useCallback } from "react";
import { SessionManager } from "../../session-manager";
import { AgentState } from "../../agent_loop/state";

interface UseExitOptions {
    sessionManager: SessionManager;
    agentState: AgentState;
}

/**
 * Hook that creates an exit handler which saves the session before exiting
 */
export function useExit({ sessionManager, agentState }: UseExitOptions) {
    const { exit } = useApp();
    
    const handleExit = useCallback(async () => {
        try {
            await sessionManager.saveSession(agentState);
        } catch (error) {
            // Don't fail the exit if session saving fails, just log it
            console.error("Failed to save session on exit:", error);
        } finally {
            exit();
        }
    }, [sessionManager, agentState, exit]);

    return handleExit;
}