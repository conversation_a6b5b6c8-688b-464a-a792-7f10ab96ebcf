/**
 * useSubmitQuery - Hook that creates a query submission processor
 *
 * This processor handles Enter key presses to submit input to the agent.
 */
import { useMemo } from "react";

import { InputBufferActions } from "../types/InputBufferTypes";
import { InputMode } from "../types/InputMode";
import { KeyEvent, KeyProcessor, ProcessingContext } from "../types/KeyProcessorTypes";
import { createActionResult, createSideEffectResult } from "../utils/key-processing-utils";
import { ProcessingState } from "./useAgent";
import { createNotificationSideEffect } from "./useKeypressRouter";

export interface UseSubmitQueryOptions {
    /** Priority for this processor (default: 5) */
    priority?: number;
    /** Custom name for debugging */
    name?: string;
    /** Callback to submit query to agent */
    onSubmitQuery: (value: string) => Promise<void>;
    /** History manager for adding to history */
    historyManager: {
        addToHistory: (value: string) => void;
    };
}

export function useSubmitQuery(options: UseSubmitQueryOptions): KeyProcessor {
    const { priority = 5, name = "SubmitQuery", onSubmitQuery, historyManager } = options;

    return useMemo(
        (): KeyProcessor => ({
            name,
            priority,

            canHandle: (event: KeyEvent, _context: ProcessingContext): boolean => {
                // Handle Enter key (without Shift modifier)
                return !!(event.key.return && !event.key.shift);
            },

            process: (event: KeyEvent, context: ProcessingContext) => {
                const { bufferState } = context;
                const value = bufferState?.text || "";

                // Don't process empty input at all
                if (!value.trim()) {
                    return createActionResult([InputBufferActions.setText("")]);
                }

                // If the agent is currently running don't allow sending new messages
                if (context.processingState !== ProcessingState.Idle) {
                    const notificationSideEffect = createNotificationSideEffect({
                        message:
                            "Agent is currently running. Please interrupt or wait for it to finish before sending a new message.",
                        type: "info",
                        timeout: 3000,
                    });

                    return createSideEffectResult([notificationSideEffect]);
                }

                // Add to history for normal input
                // Do not add to history for slash commands
                if (context.mode === InputMode.Normal) {
                    historyManager.addToHistory(value);
                }

                // Submit the query and clear the input via reducer
                onSubmitQuery(value);
                return createActionResult([InputBufferActions.setText("")]);
            },
        }),
        [priority, name, onSubmitQuery, historyManager]
    );
}
