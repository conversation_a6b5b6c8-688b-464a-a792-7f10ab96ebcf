import { useCallback } from "react";

import { Agent<PERSON>oop } from "../../agent_loop/agent_loop";
import { SessionManager } from "../../session-manager";
import { SideEffect } from "../types/KeyProcessorTypes";
import { createSystemEntrySideEffect } from "./useKeypressRouter";

interface UseNewSessionOptions {
    sessionManager: SessionManager;
    agentLoop: AgentLoop;
}

/**
 * Hook for handling new session command
 */
export function useNewSession({ sessionManager, agentLoop }: UseNewSessionOptions) {
    const handleNewSession = useCallback((): SideEffect[] => {
        // Save current session
        sessionManager.saveSession(agentLoop.agentState);

        // Clear the agent's chat history to start fresh
        agentLoop.clearState();

        // Return side effect to add system message
        return [createSystemEntrySideEffect("Started a new conversation", true)];
    }, [sessionManager, agentLoop]);

    return handleNewSession;
}
