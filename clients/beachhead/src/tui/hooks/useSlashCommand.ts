import { use<PERSON><PERSON>back, useMemo } from "react";

import { InputBufferActions } from "../types/InputBufferTypes";
import { InputMode } from "../types/InputMode";
import { KeyEvent, KeyProcessor, ProcessingContext, SideEffect } from "../types/KeyProcessorTypes";
import { MenuItem, MenuProvider } from "../types/MenuProvider";
import {
    createActionResult,
    createEmptyResult,
    createSideEffectResult,
} from "../utils/key-processing-utils";
import { createModeSideEffect, createNotificationSideEffect } from "./useKeypressRouter";

interface UseSlashCommandOptions {
    onExit?: () => void;
    onHelp?: () => void;
    onLogout: () => Promise<void>;
    onNew?: () => SideEffect[];
    onRequestId?: () => SideEffect[];
    onMcpStatus?: () => SideEffect[];
    onGitHubWorkflow?: () => void;
    priority?: number;
}

interface UseSlashCommandReturn {
    processCommand: (
        input: string,
        onSuccess?: (command: string) => void,
        onError?: (error: string) => void
    ) => { handled: boolean; sideEffects: SideEffect[] };
    isValid: (commandName: string) => boolean;
    slashCommands: SlashCommand[];
    slashCommandProcessor: KeyProcessor;
}

export interface SlashCommand {
    name: string;
    altName?: string;
    description: string;
    action: (commandName: string) => SideEffect[] | void;
}

export class CommandMenuProvider implements MenuProvider {
    name = "SlashCommands";
    private allCommands: SlashCommand[];
    private getCurrentMode: () => InputMode;

    constructor(allCommands: SlashCommand[], getCurrentMode: () => InputMode) {
        this.allCommands = allCommands || [];
        this.getCurrentMode = getCurrentMode;
    }

    shouldShowMenu(_query: string): boolean {
        return this.getCurrentMode() === InputMode.Command;
    }

    async getMenuItems(query: string): Promise<MenuItem[]> {
        // Command without "/" prefix
        const partialCommand = query;

        if (!this.allCommands) {
            return [];
        }

        const filtered = this.allCommands
            .filter((cmd) => cmd.name.startsWith(partialCommand))
            .map((cmd) => ({
                label: cmd.name,
                value: cmd.name,
                description: cmd.description,
            }));

        return filtered;
    }
}

/**
 * Creates a slash command processor
 */
export function useSlashCommandProcessor(options: {
    processCommand: (
        input: string,
        onSuccess?: (command: string) => void,
        onError?: (error: string) => void
    ) => { handled: boolean; sideEffects: SideEffect[] };
    slashCommands: SlashCommand[];
    priority?: number;
}): KeyProcessor {
    const { processCommand, slashCommands, priority = 22 } = options;

    return useMemo(
        (): KeyProcessor => ({
            name: "SlashCommand",
            priority: priority, // Between shortcuts (20) and history (15)

            canHandle: (event: KeyEvent, context: ProcessingContext): boolean => {
                // Handle "/" key to enter command mode in Normal mode
                if (
                    event.input === "/" &&
                    context.mode === InputMode.Normal &&
                    context.bufferState?.text === ""
                ) {
                    return true;
                }

                // Handle escape in command mode
                if (event.key.escape && context.mode === InputMode.Command) {
                    return true;
                }

                // Handle backspace/delete when buffer is empty in command mode
                if (
                    (event.key.backspace || event.key.delete) &&
                    context.mode === InputMode.Command &&
                    context.bufferState?.text === ""
                ) {
                    return true;
                }

                // Handle Enter in command mode to execute command
                if (event.key.return && context.mode === InputMode.Command) {
                    return true;
                }

                return false;
            },

            process: (event: KeyEvent, context: ProcessingContext) => {
                // Enter command mode
                if (event.input === "/" && context.mode === InputMode.Normal) {
                    return createSideEffectResult([createModeSideEffect(InputMode.Command)]);
                }

                // Exit command mode on Escape
                if (event.key.escape && context.mode === InputMode.Command) {
                    return createActionResult(
                        [InputBufferActions.setText("")],
                        [createModeSideEffect(InputMode.Normal)]
                    );
                }

                // Exit command mode on backspace/delete when empty
                if (
                    (event.key.backspace || event.key.delete) &&
                    context.mode === InputMode.Command &&
                    context.bufferState?.text === ""
                ) {
                    return createSideEffectResult([createModeSideEffect(InputMode.Normal)]);
                }

                // Execute command on Enter
                if (event.key.return && context.mode === InputMode.Command) {
                    const commandText = context.bufferState?.text || "";

                    // Process the command
                    const result = processCommand(
                        commandText,
                        (_command) => {
                            // Success handled - mode change will be via side effect
                        },
                        (_error) => {
                            // Error - show notification but stay in command mode
                            // This will be handled by side effects
                        }
                    );

                    if (result.handled) {
                        // Clear input and prepare side effects
                        const actions = [InputBufferActions.setText("")];
                        const sideEffects = [];

                        // Check if command was found
                        const command = slashCommands.find(
                            (cmd) =>
                                cmd.name.toLowerCase() === commandText.toLowerCase() ||
                                cmd.altName?.toLowerCase() === commandText.toLowerCase()
                        );

                        if (command) {
                            // Command found - return to normal mode
                            sideEffects.push(createModeSideEffect(InputMode.Normal));

                            // Add side effects returned from processCommand
                            sideEffects.push(...result.sideEffects);
                        } else {
                            // Command not found - show error and stay in command mode
                            sideEffects.push(
                                createNotificationSideEffect({
                                    message: `Unknown command: ${commandText}`,
                                    type: "error",
                                    timeout: 3000,
                                })
                            );
                        }

                        return createActionResult(actions, sideEffects);
                    }
                }

                return createEmptyResult();
            },
        }),
        [processCommand, slashCommands, priority]
    );
}

/**
 * Hook for handling slash commands in the input
 */
export function useSlashCommand(options: UseSlashCommandOptions): UseSlashCommandReturn {
    const {
        onExit,
        onHelp,
        onNew,
        onLogout,
        onRequestId,
        onMcpStatus,
        onGitHubWorkflow,
        priority = 22,
    } = options;

    const slashCommands: SlashCommand[] = useMemo(() => {
        const commands: SlashCommand[] = [
            {
                name: "new",
                description: "Start a new conversation",
                action: () => {
                    return onNew?.() || [];
                },
            },
            {
                name: "request-id",
                description:
                    "Show the request ID of the most recent message (for support purposes)",
                action: () => {
                    return onRequestId?.() || [];
                },
            },
            {
                name: "github-workflow",
                description: "Set up GitHub workflows for PR automation",
                action: () => {
                    onGitHubWorkflow?.();
                },
            },
            {
                name: "mcp-status",
                description: "Show the status of MCP servers",
                action: () => {
                    return onMcpStatus?.() || [];
                },
            },
            {
                name: "help",
                altName: "?",
                description: "Show help menu",
                action: () => {
                    onHelp?.();
                },
            },
            {
                name: "exit",
                description: "Exit the application",
                action: () => {
                    onExit?.();
                },
            },
            {
                name: "logout",
                description: "Log out of Augment",
                action: async () => {
                    await onLogout();
                    // Exit after logout
                    onExit?.();
                },
            },
        ];

        return commands;
    }, [onExit, onHelp, onLogout, onNew, onRequestId, onMcpStatus, onGitHubWorkflow]);

    const processCommand = useCallback(
        (
            input: string,
            onSuccess?: (command: string) => void,
            onError?: (error: string) => void
        ): { handled: boolean; sideEffects: SideEffect[] } => {
            let commandName = input.toLowerCase();

            if (!commandName) {
                return { handled: false, sideEffects: [] };
            }

            // Remove leading slash if present
            if (commandName.startsWith("/")) {
                commandName = commandName.slice(1);
            }

            const command = slashCommands.find(
                (cmd) =>
                    cmd.name.toLowerCase() === commandName ||
                    cmd.altName?.toLowerCase() === commandName
            );

            if (!command) {
                if (onError) {
                    onError(`Unknown command: ${commandName}`);
                }
                return { handled: true, sideEffects: [] };
            }

            try {
                const actionResult = command.action(command.name);
                if (onSuccess) {
                    onSuccess(command.name);
                }

                // Return side effects directly
                const sideEffects = actionResult && Array.isArray(actionResult) ? actionResult : [];

                // Also store for backward compatibility with processor
                if (sideEffects.length > 0) {
                    (command as any)._pendingSideEffects = sideEffects;
                }

                return { handled: true, sideEffects };
            } catch (error) {
                const errorMsg = `Error executing ${command.name}: ${(error as Error).message}`;
                if (onError) {
                    onError(errorMsg);
                }
                return { handled: true, sideEffects: [] };
            }
        },
        [slashCommands]
    );

    const isValid = useCallback(
        (commandName: string): boolean => {
            if (!commandName) {
                return false;
            }

            // Remove leading slash if present
            if (commandName.startsWith("/")) {
                commandName = commandName.slice(1);
            }

            const normalizedCommandName = commandName.toLowerCase();

            return slashCommands.some(
                (cmd) =>
                    cmd.name.toLowerCase() === normalizedCommandName ||
                    cmd.altName?.toLowerCase() === normalizedCommandName
            );
        },
        [slashCommands]
    );

    // Create the slash command processor
    const slashCommandProcessor = useSlashCommandProcessor({
        processCommand,
        slashCommands,
        priority,
    });

    return {
        processCommand,
        isValid,
        slashCommands,
        slashCommandProcessor,
    };
}
