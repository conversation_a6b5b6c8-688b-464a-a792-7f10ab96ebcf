import { useCallback, useRef, useState } from "react";

interface UseTwoStepActionOptions {
    onAction: () => void;
    message: string;
    timeoutMs?: number;
}

interface UseTwoStepActionReturn {
    trigger: () => void;
    notification: { message: string; type: "info" } | null;
    reset: () => void;
}

/**
 * Generic hook to manage two-step confirmation pattern
 * First trigger shows waiting state, second trigger (within timeout) executes action
 */
export function useTwoStepAction({
    onAction,
    message,
    timeoutMs = 2000,
}: UseTwoStepActionOptions): UseTwoStepActionReturn {
    const [isActive, setIsActive] = useState(false);
    const timeoutRef = useRef<NodeJS.Timeout | null>(null);

    const reset = useCallback(() => {
        setIsActive(false);
        if (timeoutRef.current) {
            clearTimeout(timeoutRef.current);
            timeoutRef.current = null;
        }
    }, []);

    const trigger = useCallback(() => {
        if (isActive) {
            // Second trigger - execute action
            reset();
            onAction();
        } else {
            // First trigger - show waiting state and start timeout
            setIsActive(true);

            // Clear any existing timeout
            if (timeoutRef.current) {
                clearTimeout(timeoutRef.current);
            }

            // Set timeout to reset state
            timeoutRef.current = setTimeout(() => {
                setIsActive(false);
                timeoutRef.current = null;
            }, timeoutMs);
        }
    }, [isActive, onAction, reset, timeoutMs]);

    const notification = isActive ? { message, type: "info" as const } : null;

    return {
        trigger,
        notification,
        reset,
    };
}
