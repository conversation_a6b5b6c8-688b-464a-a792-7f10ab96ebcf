/**
 * useDefaultInput - Hook that creates a default input processor
 *
 * This processor handles regular text input, cursor movement, and basic editing operations.
 */
import { useMemo } from "react";

import { InputBufferAction } from "../types/InputBufferTypes";
import { KeyEvent, KeyProcessor, ProcessingContext } from "../types/KeyProcessorTypes";
import { createActionResult, createEmptyResult } from "../utils/key-processing-utils";

export interface UseDefaultInputOptions {
    /** Priority for this processor (default: 10) */
    priority?: number;
    /** Custom name for debugging */
    name?: string;
}

export function useDefaultInput(options: UseDefaultInputOptions = {}): KeyProcessor {
    const { priority = 10, name = "DefaultInput" } = options;

    return useMemo(
        (): KeyProcessor => ({
            name,
            priority,

            canHandle: (event: KeyEvent, _context: ProcessingContext): boolean => {
                // Handle most keyboard events except system-specific ones
                return !isSystemInput(event);
            },

            process: (event: KeyEvent, context: ProcessingContext) => {
                const actions: InputBufferAction[] = [];

                // Handle terminal-specific delete sequences first
                const terminalAction = handleTerminalSequences(event, context);
                if (terminalAction) {
                    actions.push(terminalAction);
                    return createActionResult(actions);
                }

                // Handle control key combinations
                if (event.key.ctrl) {
                    const action = handleControlKey(event, context);
                    if (action) {
                        actions.push(action);
                        return createActionResult(actions);
                    }
                }

                // Handle navigation keys
                const navigationAction = handleNavigationKey(event, context);
                if (navigationAction) {
                    actions.push(navigationAction);
                    return createActionResult(actions);
                }

                // Handle editing keys
                const editingAction = handleEditingKey(event, context);
                if (editingAction) {
                    actions.push(editingAction);
                    return createActionResult(actions);
                }

                // Handle various enter/return sequences that should insert newlines
                // These are typically sent when Shift+Enter or Ctrl+J is pressed in different terminals
                if (
                    (event.input === "\n" && !event.key.return) ||
                    (event.input === "\r" && !event.key.return) ||
                    event.input === "\\\\\\r"
                ) {
                    actions.push({ type: "INSERT_NEWLINE" });
                    return createActionResult(actions);
                }

                // Handle regular character input
                if (isPrintableCharacter(event)) {
                    actions.push({
                        type: "INSERT_TEXT",
                        text: event.input,
                    });
                    return createActionResult(actions);
                }

                // If we can't handle it, return unhandled
                return createEmptyResult(false);
            },
        }),
        [priority, name]
    );
}

function handleTerminalSequences(
    event: KeyEvent,
    _context: ProcessingContext
): InputBufferAction | null {
    // Handle common terminal delete sequences

    // \x7f (127) - DEL character, often sent by the "Backspace" key on Mac
    if (event.input === "\x7f") {
        return { type: "DELETE_CHAR_BACKWARD" };
    }

    // \x08 (8) - BS character, might be sent by some terminals for backspace
    if (event.input === "\x08") {
        return { type: "DELETE_CHAR_BACKWARD" };
    }

    // ESC[3~ - ANSI escape sequence for forward delete
    if (event.input.includes("[3~")) {
        return { type: "DELETE_CHAR_FORWARD" };
    }

    // ESC[P - Another forward delete sequence
    if (event.input.includes("[P")) {
        return { type: "DELETE_CHAR_FORWARD" };
    }

    return null;
}

function handleControlKey(event: KeyEvent, _context: ProcessingContext): InputBufferAction | null {
    if (!event.key.ctrl) {
        return null;
    }

    // Map common control sequences
    switch (event.input.toLowerCase()) {
        case "a":
            // Beginning of line
            return { type: "MOVE_CURSOR_START" };

        case "e":
            // End of line
            return { type: "MOVE_CURSOR_END" };

        case "k":
            // Kill to end of line
            return { type: "DELETE_TO_LINE_END" };

        case "u":
            // Kill to beginning of line
            return { type: "DELETE_TO_LINE_START" };

        case "w":
            // Delete word backward
            return { type: "DELETE_WORD_BACKWARD" };

        case "d":
            // Delete character forward (like Delete key)
            return { type: "DELETE_CHAR_FORWARD" };

        case "h":
            // Delete character backward (like Backspace)
            return { type: "DELETE_CHAR_BACKWARD" };

        case "t":
            // Transpose characters
            return { type: "TRANSPOSE_CHARS" };

        case "b":
            // Move cursor left
            return { type: "MOVE_CURSOR_LEFT" };

        case "f":
            // Move cursor right
            return { type: "MOVE_CURSOR_RIGHT" };

        case "j":
            // Insert newline
            return { type: "INSERT_NEWLINE" };

        default:
            return null;
    }
}

function handleNavigationKey(
    event: KeyEvent,
    _context: ProcessingContext
): InputBufferAction | null {
    if (event.key.leftArrow) {
        if (event.key.ctrl || event.key.meta) {
            return { type: "MOVE_CURSOR_WORD_LEFT" };
        }
        return { type: "MOVE_CURSOR_LEFT" };
    }

    if (event.key.rightArrow) {
        if (event.key.ctrl || event.key.meta) {
            return { type: "MOVE_CURSOR_WORD_RIGHT" };
        }
        return { type: "MOVE_CURSOR_RIGHT" };
    }

    if (event.key.upArrow) {
        return { type: "MOVE_CURSOR_UP" };
    }

    if (event.key.downArrow) {
        return { type: "MOVE_CURSOR_DOWN" };
    }

    return null;
}

function handleEditingKey(event: KeyEvent, _context: ProcessingContext): InputBufferAction | null {
    if (event.key.backspace) {
        if (event.key.ctrl || event.key.meta) {
            return { type: "DELETE_WORD_BACKWARD" };
        }
        return { type: "DELETE_CHAR_BACKWARD" };
    }

    if (event.key.delete) {
        if (event.key.ctrl || event.key.meta) {
            return { type: "DELETE_WORD_BACKWARD" };
        }
        return { type: "DELETE_CHAR_BACKWARD" };
    }

    if (event.key.return) {
        if (event.key.shift) {
            // Shift+Return - insert newline
            return { type: "INSERT_NEWLINE" };
        }

        // Regular return - we'll handle submission at a higher level
        return null;
    }

    if (event.key.tab) {
        // Insert tab character
        return { type: "INSERT_TEXT", text: "\t" };
    }

    return null;
}

function isPrintableCharacter(event: KeyEvent): boolean {
    // Check if it's a regular printable character
    return (
        event.input.length === 1 &&
        !event.key.ctrl &&
        !event.key.meta &&
        !event.key.escape &&
        !event.key.return &&
        !event.key.backspace &&
        !event.key.delete &&
        !event.key.tab &&
        !event.key.upArrow &&
        !event.key.downArrow &&
        !event.key.leftArrow &&
        !event.key.rightArrow &&
        event.input >= " " // ASCII 32 and above
    );
}

function isSystemInput(event: KeyEvent): boolean {
    // System inputs that we shouldn't handle
    if (event.key.escape && !event.key.ctrl && !event.key.meta) {
        return true; // Pure escape key
    }

    // Handle Page Up/Down as system keys for now
    if (event.key.pageUp || event.key.pageDown) {
        return true;
    }

    return false;
}
