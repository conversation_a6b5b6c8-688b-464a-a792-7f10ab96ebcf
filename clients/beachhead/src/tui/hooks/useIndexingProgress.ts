import { useEffect, useState } from "react";

import { BeachheadWorkspaceManager } from "../../workspace-manager";
import { IndexingProgress } from "../ui/components/IndexingProgressBar";

/**
 * Hook for tracking indexing progress in the TUI.
 */

interface UseIndexingProgressOptions {
    workspaceManager: BeachheadWorkspaceManager;
}

interface UseIndexingProgressReturn {
    progress: IndexingProgress;
}

export function useIndexingProgress({
    workspaceManager,
}: UseIndexingProgressOptions): UseIndexingProgressReturn {
    const [progress, setProgress] = useState<IndexingProgress>({
        percentage: 0,
        phase: "idle",
    });

    useEffect(() => {
        let indexingHideTimeout: NodeJS.Timeout | null = null;
        let indexingStartTime: number | null = null;
        let shouldShowProgress = false;
        let progressDelayTimeout: NodeJS.Timeout | null = null;

        // Thresholds for showing progress bar - only show for substantial indexing work
        const MIN_DURATION_THRESHOLD_MS = 2000; // Only show progress if indexing takes longer than 2 seconds

        const disposables: Array<{ dispose(): void }> = [];
        disposables.push(
            workspaceManager.onIndexing((event) => {
                // Record when indexing first starts
                if (indexingStartTime === null && event.phase === "indexing") {
                    indexingStartTime = Date.now();

                    // Set a delay before showing progress - if indexing completes before this delay,
                    // we won't show the progress bar at all
                    progressDelayTimeout = setTimeout(() => {
                        shouldShowProgress = true;
                        // If we're still indexing after the delay, show the progress
                        if (event.phase === "indexing") {
                            setProgress({
                                percentage: event.percentage,
                                phase: event.phase,
                            });
                        }
                    }, MIN_DURATION_THRESHOLD_MS);

                    // Don't show progress immediately - wait for the delay
                    return;
                }

                // If we've decided to show progress, update it
                if (shouldShowProgress) {
                    setProgress({
                        percentage: event.percentage,
                        phase: event.phase,
                    });
                }

                if (event.phase === "complete") {
                    // Clear the progress delay timeout if it's still pending
                    if (progressDelayTimeout) {
                        clearTimeout(progressDelayTimeout);
                        progressDelayTimeout = null;
                    }

                    // Only show completion if we were showing progress
                    if (shouldShowProgress) {
                        setProgress({
                            percentage: event.percentage,
                            phase: event.phase,
                        });

                        indexingHideTimeout = setTimeout(() => {
                            setProgress({
                                phase: "idle",
                                percentage: 0,
                            });
                        }, 3000);
                    }

                    // Reset state for next indexing session
                    indexingStartTime = null;
                    shouldShowProgress = false;
                }
            })
        );

        return () => {
            if (indexingHideTimeout) {
                clearTimeout(indexingHideTimeout);
            }
            if (progressDelayTimeout) {
                clearTimeout(progressDelayTimeout);
            }
            disposables.forEach((d) => d.dispose());
        };
    }, [workspaceManager]);

    return {
        progress,
    };
}
