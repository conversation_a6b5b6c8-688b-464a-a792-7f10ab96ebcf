/**
 * useKeyPressHandler - Hook for processing keyboard events with pluggable processors
 *
 * This hook provides a clean, React-idiomatic way to handle keyboard input with:
 * - Pluggable key processors for different input types
 * - Priority-based processing order
 * - Side effect handling (hooks, commands, etc.)
 * - Direct integration with InputBufferReducer
 */
import { useCallback, useEffect, useRef } from "react";

import { InputBufferAction, InputBufferState } from "../types/InputBufferTypes";
import {
    HookRegistry,
    KeyEvent,
    KeyProcessingResult,
    ProcessingContext,
    SideEffect,
    UseKeyPressHandlerOptions,
    UseKeyPressHandlerReturn,
} from "../types/KeyProcessorTypes";

export function useKeyPressHandler(
    bufferState: InputBufferState,
    dispatch: (action: InputBufferAction) => void,
    options: UseKeyPressHandlerOptions = {}
): UseKeyPressHandlerReturn {
    const {
        processors = [],
        hookRegistry = {},
        preventDefault = true,
        stopPropagation = true,
        active = true,
        debug = false,
    } = options;

    const containerRef = useRef<HTMLDivElement>(null);

    // Sort processors by priority (higher priority first)
    const sortedProcessors = processors.sort((a, b) => (b.priority || 0) - (a.priority || 0));

    const handleKeyPress = useCallback(
        (event: KeyboardEvent) => {
            if (!active) {
                return;
            }

            if (debug) {
                console.log("[useKeyPressHandler] Raw keyboard event:", event);
            }

            // Convert DOM KeyboardEvent to our KeyEvent interface
            const keyEvent: KeyEvent = {
                input: event.key,
                key: {
                    upArrow: event.key === "ArrowUp",
                    downArrow: event.key === "ArrowDown",
                    leftArrow: event.key === "ArrowLeft",
                    rightArrow: event.key === "ArrowRight",
                    return: event.key === "Enter",
                    escape: event.key === "Escape",
                    ctrl: event.ctrlKey,
                    shift: event.shiftKey,
                    meta: event.metaKey,
                    backspace: event.key === "Backspace",
                    delete: event.key === "Delete",
                    tab: event.key === "Tab",
                    pageDown: event.key === "PageDown",
                    pageUp: event.key === "PageUp",
                },
            };

            // Create processing context
            const context: ProcessingContext = {
                bufferState,
            };

            // Process through each processor in priority order
            for (const processor of sortedProcessors) {
                if (processor.canHandle(keyEvent, context)) {
                    if (debug) {
                        console.log(
                            `[useKeyPressHandler] ${processor.name || "Processor"} handling event`
                        );
                    }

                    const result: KeyProcessingResult = processor.process(keyEvent, context);

                    if (debug) {
                        console.log("[useKeyPressHandler] Processing result:", result);
                    }

                    if (result.handled) {
                        // Prevent default behavior if requested
                        if (preventDefault) {
                            event.preventDefault();
                        }
                        if (stopPropagation) {
                            event.stopPropagation();
                        }

                        // Dispatch actions immediately for responsive UI
                        if (result.actions.length > 0) {
                            result.actions.forEach((action) => {
                                if (debug) {
                                    console.log("[useKeyPressHandler] Dispatching action:", action);
                                }
                                dispatch(action);
                            });
                        }

                        // Execute side effects
                        if (result.sideEffects) {
                            result.sideEffects.forEach((sideEffect) => {
                                executeSideEffect(sideEffect, hookRegistry, debug);
                            });
                        }

                        return; // Event was handled, stop processing
                    }
                }
            }

            if (debug) {
                console.log("[useKeyPressHandler] No processor handled event:", keyEvent);
            }
        },
        [
            active,
            bufferState,
            dispatch,
            sortedProcessors,
            hookRegistry,
            preventDefault,
            stopPropagation,
            debug,
        ]
    );

    // Attach event listener to container
    useEffect(() => {
        const container = containerRef.current;
        if (!container || !active) {
            return;
        }

        // Make sure the container can receive focus
        if (!container.hasAttribute("tabIndex")) {
            container.setAttribute("tabIndex", "-1");
        }

        container.addEventListener("keydown", handleKeyPress);

        return () => {
            container.removeEventListener("keydown", handleKeyPress);
        };
    }, [handleKeyPress, active]);

    // Focus the container when it becomes active
    useEffect(() => {
        if (active && containerRef.current) {
            containerRef.current.focus();
        }
    }, [active]);

    return {
        handleKeyPress,
        containerRef,
    };
}

/**
 * Execute a side effect
 */
function executeSideEffect(
    sideEffect: SideEffect,
    hookRegistry: HookRegistry,
    debug: boolean
): void {
    if (debug) {
        console.log("[useKeyPressHandler] Executing side effect:", sideEffect);
    }

    switch (sideEffect.type) {
        case "TRIGGER_SLASH_COMMAND":
            if (hookRegistry.slashCommand) {
                hookRegistry.slashCommand(sideEffect.payload);
            } else if (debug) {
                console.warn("[useKeyPressHandler] No slash command hook registered");
            }
            break;

        case "TRIGGER_HOOK": {
            const hookName = sideEffect.payload?.hookName;
            if (hookName && hookRegistry[hookName]) {
                hookRegistry[hookName](sideEffect.payload);
            } else if (debug) {
                console.warn("[useKeyPressHandler] Hook not found:", hookName);
            }
            break;
        }

        case "CUSTOM":
            // Custom side effects can be handled by the application
            if (debug) {
                console.log("[useKeyPressHandler] Custom side effect:", sideEffect.payload);
            }
            break;

        default:
            if (debug) {
                console.warn("[useKeyPressHandler] Unknown side effect type:", sideEffect.type);
            }
    }
}

/**
 * Helper function to create an empty processing result
 */
export function createEmptyResult(handled: boolean = false): KeyProcessingResult {
    return {
        actions: [],
        handled,
    };
}

/**
 * Helper function to create a result with actions
 */
export function createActionResult(
    actions: InputBufferAction[],
    sideEffects?: SideEffect[]
): KeyProcessingResult {
    return {
        actions,
        handled: true,
        sideEffects,
    };
}

/**
 * Helper function to create a result with side effects only
 */
export function createSideEffectResult(sideEffects: SideEffect[]): KeyProcessingResult {
    return {
        actions: [],
        handled: true,
        sideEffects,
    };
}
