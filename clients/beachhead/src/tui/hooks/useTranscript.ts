import { useCallback, useRef, useState } from "react";

import {
    AgentEntry,
    CommandEntry,
    ErrorEntry,
    SystemEntry,
    ToolEntry,
    TranscriptEntry,
    TranscriptEntryType,
    UserEntry,
} from "../types/transcript-types";

// Re-export types for backward compatibility
export { TranscriptEntryType, TranscriptEntry } from "../types/transcript-types";

interface UseTranscriptReturn {
    transcript: TranscriptEntry[];
    addUserEntry: (content: string) => void;
    addAgentEntry: (content: string, isStreaming?: boolean) => void;
    addToolEntry: (entry: Omit<ToolEntry, "key" | "timestamp" | "type">) => void;
    addErrorEntry: (error: string, context?: string) => void;
    addCommandEntry: (command: string, args?: string[]) => void;
    addSystemEntry: (message: string, hasBorder: boolean) => void;
    clearTranscript: () => void;
    openCodeBlockRef: { current: string | null };
}

export function useTranscript(): UseTranscriptReturn {
    const [transcript, setTranscript] = useState<TranscriptEntry[]>([]);
    const openCodeBlockRef = useRef<string | null>(null);

    const generateKey = useCallback(() => {
        const timestamp = new Date().getTime();
        const random = Math.random().toString(36).substring(2, 11);
        return `${timestamp}-${random}`;
    }, []);

    const addUserEntry = useCallback(
        (content: string): void => {
            const entry: UserEntry = {
                key: generateKey(),
                timestamp: new Date(),
                type: TranscriptEntryType.User,
                content,
            };
            setTranscript((prev) => [...prev, entry]);
        },
        [generateKey]
    );

    const addAgentEntry = useCallback(
        (content: string, isStreaming: boolean = false): void => {
            const entry: AgentEntry = {
                key: generateKey(),
                timestamp: new Date(),
                type: TranscriptEntryType.Agent,
                content,
                isStreaming,
            };
            setTranscript((prev) => [...prev, entry]);
        },
        [generateKey]
    );

    const addToolEntry = useCallback(
        (toolData: Omit<ToolEntry, "key" | "timestamp" | "type">): void => {
            const entry: ToolEntry = {
                key: generateKey(),
                timestamp: new Date(),
                type: TranscriptEntryType.Tool,
                ...toolData,
            };
            setTranscript((prev) => [...prev, entry]);
        },
        [generateKey]
    );

    const addErrorEntry = useCallback(
        (error: string, context?: string): void => {
            const entry: ErrorEntry = {
                key: generateKey(),
                timestamp: new Date(),
                type: TranscriptEntryType.Error,
                error,
                context,
            };
            setTranscript((prev) => [...prev, entry]);
        },
        [generateKey]
    );

    const addCommandEntry = useCallback(
        (command: string, args?: string[]): void => {
            const entry: CommandEntry = {
                key: generateKey(),
                timestamp: new Date(),
                type: TranscriptEntryType.Command,
                command,
                args,
            };
            setTranscript((prev) => [...prev, entry]);
        },
        [generateKey]
    );

    const addSystemEntry = useCallback(
        (message: string, hasBorder: boolean): void => {
            const entry: SystemEntry = {
                key: generateKey(),
                timestamp: new Date(),
                type: TranscriptEntryType.System,
                message,
                hasBorder,
            };
            setTranscript((prev) => [...prev, entry]);
        },
        [generateKey]
    );

    const clearTranscript = useCallback((): void => {
        setTranscript([]);
    }, []);

    return {
        transcript,
        addUserEntry,
        addAgentEntry,
        addToolEntry,
        addErrorEntry,
        addCommandEntry,
        addSystemEntry,
        clearTranscript,
        openCodeBlockRef,
    };
}
