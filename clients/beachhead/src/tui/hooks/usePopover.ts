import { useCallback, useMemo, useState } from "react";

export interface KeyboardShortcut {
    key: string;           // e.g., "Esc", "Enter", "Ctrl+C", "↑↓"
    description: string;   // e.g., "to dismiss", "to confirm", "to navigate"
}

export interface UsePopoverOptions {
    shortcuts: KeyboardShortcut[]; // Required, no defaults
}

export interface UsePopoverReturn {
    show: boolean;
    openPopover: () => void;
    closePopover: () => void;
    shortcutsText: string; // Formatted text for display
}

/**
 * Formats an array of keyboard shortcuts into a consistent display string
 * Examples:
 * - Single: "Press Esc to dismiss"
 * - Multiple: "Press Esc to dismiss • Enter to confirm • Tab to navigate"
 */
function formatShortcuts(shortcuts: KeyboardShortcut[]): string {
    if (shortcuts.length === 0) {
        return "";
    }

    if (shortcuts.length === 1) {
        const { key, description } = shortcuts[0];
        return `Press ${key} ${description}`;
    }

    // Multiple shortcuts: join with bullet separator
    const formattedShortcuts = shortcuts.map(({ key, description }) => `${key} ${description}`);
    return `Press ${formattedShortcuts.join(" • ")}`;
}

/**
 * Hook for managing popover state and keyboard shortcuts display
 * 
 * @param options Configuration object containing shortcuts array
 * @returns Object with popover state management and formatted shortcuts text
 * 
 * @example
 * ```typescript
 * const popover = usePopover({
 *     shortcuts: [
 *         { key: "Esc", description: "to dismiss" },
 *         { key: "Enter", description: "to confirm" }
 *     ]
 * });
 * 
 * // Use in component:
 * <Popover 
 *     show={popover.show} 
 *     onDismiss={popover.closePopover}
 *     shortcutsText={popover.shortcutsText}
 * >
 *     {content}
 * </Popover>
 * ```
 */
export function usePopover({ shortcuts }: UsePopoverOptions): UsePopoverReturn {
    const [show, setShow] = useState(false);

    // Memoize shortcuts text to prevent unnecessary re-renders and flickering
    // Only re-compute when shortcuts array actually changes
    const shortcutsText = useMemo(() => {
        return formatShortcuts(shortcuts);
    }, [shortcuts]);

    // Stable callback references to prevent unnecessary re-renders
    const openPopover = useCallback(() => {
        setShow(true);
    }, []);

    const closePopover = useCallback(() => {
        setShow(false);
    }, []);

    return {
        show,
        openPopover,
        closePopover,
        shortcutsText,
    };
}
