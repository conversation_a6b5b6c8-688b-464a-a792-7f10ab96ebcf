import { useStdout } from "ink";
import { useEffect, useReducer, useRef } from "react";

import { inputBufferReducer } from "../reducers/inputBufferReducer";
import { createInitialInputBufferState, InputBufferActions } from "../types/InputBufferTypes";
import { calculateWrappedText } from "../utils/buffer-utils";

interface UseInputBufferOptions {
    onChange?: (value: string) => void;
    placeholder?: string;
    promptPrefix?: string;
}

interface UseInputBufferReturn {
    // State
    value: string;
    cursorPosition: number;
    wrappedLines: string[];
    cursorLine: number;
    cursorColumn: number;
    availableWidth: number;
    placeholder?: string;

    // Handlers
    onChange: (value: string) => void;

    // For router integration
    bufferState: any;
    dispatch: (action: any) => void;
}

// Bracketed paste mode constants
const ENABLE_BRACKETED_PASTE = "\x1b[?2004h";
const DISABLE_BRACKETED_PASTE = "\x1b[?2004l";

/**
 * Hook that manages input buffer state and keyboard handling
 * Returns state and handlers for building input components
 */
export function useInputBuffer({
    onChange,
    placeholder = "",
    promptPrefix = "",
}: UseInputBufferOptions): UseInputBufferReturn {
    const { stdout } = useStdout();

    // Initialize reducer state from props
    const [bufferState, dispatch] = useReducer(
        inputBufferReducer,
        createInitialInputBufferState(
            "", // Start with empty text
            Math.max(stdout.columns - promptPrefix.length - 6, 20), // Calculate initial width
            placeholder,
            promptPrefix
        )
    );

    // Calculate available width for text (account for prompt, padding, border)
    // Border: 2, paddingLeft: 1, paddingRight: 1, space after prompt: 1 = 5
    // Add 1 extra for safety to prevent border breaking
    const availableWidth = Math.max(stdout.columns - promptPrefix.length - 6, 20);

    // Sync screen width changes to reducer (only when actually different)
    useEffect(() => {
        if (bufferState.availableWidth !== availableWidth) {
            dispatch(InputBufferActions.setAvailableWidth(availableWidth));
        }
    }, [availableWidth, bufferState.availableWidth]);

    // Call external callbacks when internal state changes (only call if different)
    const prevTextRef = useRef(bufferState.text);
    useEffect(() => {
        if (onChange && bufferState.text !== prevTextRef.current) {
            prevTextRef.current = bufferState.text;
            onChange(bufferState.text);
        }
    }, [bufferState.text, onChange]);

    // Enable bracketed paste mode
    useEffect(() => {
        process.stdout.write(ENABLE_BRACKETED_PASTE);

        // Handle cleanup for both process exit and component unmount
        const handleExit = () => {
            process.stdout.write(DISABLE_BRACKETED_PASTE);
        };

        process.on("exit", handleExit);
        process.on("SIGINT", handleExit);
        process.on("SIGTERM", handleExit);

        return () => {
            process.removeListener("exit", handleExit);
            process.removeListener("SIGINT", handleExit);
            process.removeListener("SIGTERM", handleExit);
            handleExit();
        };
    }, []);

    // Calculate wrapped text from reducer state
    const { lines, cursorLine, cursorColumn } = calculateWrappedText(
        bufferState.text,
        bufferState.availableWidth,
        bufferState.cursorPosition
    );

    return {
        value: bufferState.text,
        cursorPosition: bufferState.cursorPosition,
        wrappedLines: lines,
        cursorLine,
        cursorColumn,
        availableWidth: bufferState.availableWidth,
        placeholder: bufferState.placeholder,
        onChange: onChange || (() => {}),
        bufferState,
        dispatch,
    };
}
