import React, { useCallback, useMemo, useState } from "react";

import { KeyEvent, KeyProcessor, ProcessingContext } from "../types/KeyProcessorTypes";
import { HelpPopover } from "../ui/components/HelpPopover";
import { createEmptyResult } from "../utils/key-processing-utils";

interface UseHelpOptions {
    priority?: number;
}

interface UseHelpReturn {
    showHelp: boolean;
    openHelp: () => void;
    closeHelp: () => void;
    toggleHelp: () => void;
    helpProcessor: KeyProcessor;
    HelpPopover: React.FC<{ shortcutsText: string }>; // Component that needs shortcutsText prop
}

/**
 * Hook for managing help system state and behavior
 */
export function useHelp(options: UseHelpOptions = {}): UseHelpReturn {
    const { priority = 25 } = options;
    const [showHelp, setShowHelp] = useState(false);

    const openHelp = useCallback(() => {
        setShowHelp(true);
    }, []);

    const closeHelp = useCallback(() => {
        setShowHelp(false);
    }, []);

    const toggleHelp = useCallback(() => {
        setShowHelp((prev) => !prev);
    }, []);

    // Create help processor to handle escape when popover is open
    const helpProcessor = useMemo(
        (): KeyProcessor => ({
            name: "Help",
            priority: priority, // Higher than menu navigation (24)

            canHandle: (event: KeyEvent, context: ProcessingContext): boolean => {
                // Handle escape key when popover is shown
                if (event.key.escape && context.popover) {
                    return true;
                }
                return false;
            },

            process: (event: KeyEvent, context: ProcessingContext) => {
                // Close help popover on escape
                if (event.key.escape && context.popover) {
                    closeHelp();
                    return createEmptyResult(true);
                }
                return createEmptyResult();
            },
        }),
        [closeHelp, priority]
    );

    // Create a component that's already wired up with the help state
    const HelpPopoverComponent = useCallback<React.FC<{ shortcutsText: string }>>(
        ({ shortcutsText }) => {
            return React.createElement(HelpPopover, {
                show: showHelp,
                onDismiss: closeHelp,
                shortcutsText,
            });
        },
        [showHelp, closeHelp]
    );

    return {
        showHelp,
        openHelp,
        closeHelp,
        toggleHelp,
        helpProcessor,
        HelpPopover: HelpPopoverComponent,
    };
}
