/**
 * useShortcuts - Hook that creates a global shortcuts processor
 *
 * This processor handles global keyboard shortcuts like Ctrl+C, Ctrl+D, Escape.
 */
import { useEffect, useMemo, useRef, useState } from "react";

import { InputBufferActions } from "../types/InputBufferTypes";
import { KeyEvent, KeyProcessor, ProcessingContext } from "../types/KeyProcessorTypes";
import {
    createActionResult,
    createEmptyResult,
    createSideEffectResult,
} from "../utils/key-processing-utils";
import { ProcessingState } from "./useAgent";
import { createNotificationSideEffect } from "./useKeypressRouter";

export interface UseShortcutsOptions {
    /** Priority for this processor (default: 20 - highest priority) */
    priority?: number;
    /** Custom name for debugging */
    name?: string;
    /** Callback function to exit the application */
    onExit: () => void;
    /** Callback function to interrupt the agent */
    onInterruptAgent: () => void;
}

export function useShortcuts(options: UseShortcutsOptions): KeyProcessor {
    const {
        priority = 20, // Higher priority than other processors
        name = "Shortcuts",
        onExit,
        onInterruptAgent,
    } = options;

    // Direct confirmation state management
    const [ctrlCConfirmation, setCtrlCConfirmation] = useState(false);
    const [ctrlDConfirmation, setCtrlDConfirmation] = useState(false);
    const [escapeConfirmation, setEscapeConfirmation] = useState(false);

    const ctrlCTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);
    const ctrlDTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);
    const escapeTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);

    // Helper function to start confirmation timer
    const startConfirmation = (
        setConfirmation: (active: boolean) => void,
        timeoutRef: React.RefObject<ReturnType<typeof setTimeout> | null>,
        message: string
    ) => {
        setConfirmation(true);

        // Clear existing timeout
        if (timeoutRef.current) {
            clearTimeout(timeoutRef.current);
        }

        // Set new timeout
        timeoutRef.current = setTimeout(() => {
            setConfirmation(false);
            timeoutRef.current = null;
        }, 2000);

        return createNotificationSideEffect({ message, type: "info", timeout: 2000 });
    };

    // Helper function to clear confirmation
    const clearConfirmation = (
        setConfirmation: (active: boolean) => void,
        timeoutRef: React.RefObject<ReturnType<typeof setTimeout> | null>
    ) => {
        setConfirmation(false);
        if (timeoutRef.current) {
            clearTimeout(timeoutRef.current);
            timeoutRef.current = null;
        }

        return createNotificationSideEffect(null);
    };

    // Cleanup on unmount
    useEffect(() => {
        return () => {
            if (ctrlCTimeoutRef.current) {
                clearTimeout(ctrlCTimeoutRef.current);
            }
            if (ctrlDTimeoutRef.current) {
                clearTimeout(ctrlDTimeoutRef.current);
            }
            if (escapeTimeoutRef.current) {
                clearTimeout(escapeTimeoutRef.current);
            }
        };
    }, []);

    return useMemo(
        (): KeyProcessor => ({
            name,
            priority,

            canHandle: (event: KeyEvent, _context: ProcessingContext): boolean => {
                // Handle Ctrl+C for exit
                if (event.key.ctrl && event.input === "c") {
                    return true;
                }

                // Handle Ctrl+D for exit
                if (event.key.ctrl && event.input === "d") {
                    return true;
                }

                // Handle Escape for interrupting agent when busy or clearing input when idle
                if (event.key.escape) {
                    return true;
                }

                return false;
            },

            process: (event: KeyEvent, context: ProcessingContext) => {
                // Handle Ctrl+C with two-step confirmation
                if (event.key.ctrl && event.input === "c") {
                    if (context.processingState !== ProcessingState.Idle) {
                        // Agent is busy - interrupt immediately without confirmation
                        onInterruptAgent();
                    }

                    if (ctrlCConfirmation) {
                        // Second press - actually exit
                        const clearSideEffect = clearConfirmation(
                            setCtrlCConfirmation,
                            ctrlCTimeoutRef
                        );
                        onExit();
                        return createSideEffectResult([clearSideEffect]);
                    } else {
                        // First press - start confirmation
                        const startSideEffect = startConfirmation(
                            setCtrlCConfirmation,
                            ctrlCTimeoutRef,
                            "Press Ctrl+C again to exit"
                        );
                        return createSideEffectResult([startSideEffect]);
                    }
                }

                // Handle Ctrl+D with two-step confirmation
                if (event.key.ctrl && event.input === "d") {
                    if (context.processingState !== ProcessingState.Idle) {
                        // Agent is busy - interrupt immediately without confirmation
                        onInterruptAgent();
                    }

                    if (ctrlDConfirmation) {
                        // Second press - actually exit
                        const clearSideEffect = clearConfirmation(
                            setCtrlDConfirmation,
                            ctrlDTimeoutRef
                        );
                        onExit();
                        return createSideEffectResult([clearSideEffect]);
                    } else {
                        // First press - start confirmation
                        const startSideEffect = startConfirmation(
                            setCtrlDConfirmation,
                            ctrlDTimeoutRef,
                            "Press Ctrl+D again to exit"
                        );
                        return createSideEffectResult([startSideEffect]);
                    }
                }

                // Handle Escape - interrupt agent if busy, clear input with confirmation if idle
                if (event.key.escape) {
                    if (context.processingState !== ProcessingState.Idle) {
                        // Agent is busy - interrupt immediately without confirmation
                        onInterruptAgent();
                        return createEmptyResult();
                    } else {
                        // Agent is idle - use two-step confirmation to clear input
                        if (escapeConfirmation) {
                            // Second press - actually clear input
                            const clearSideEffect = clearConfirmation(
                                setEscapeConfirmation,
                                escapeTimeoutRef
                            );
                            return createActionResult(
                                [InputBufferActions.setText("")],
                                [clearSideEffect]
                            );
                        } else {
                            // First press - start confirmation
                            const startSideEffect = startConfirmation(
                                setEscapeConfirmation,
                                escapeTimeoutRef,
                                "Press Escape again to clear input"
                            );
                            return createSideEffectResult([startSideEffect]);
                        }
                    }
                }

                return createEmptyResult();
            },
        }),
        [
            priority,
            name,
            onExit,
            onInterruptAgent,
            ctrlCConfirmation,
            ctrlDConfirmation,
            escapeConfirmation,
            startConfirmation,
            clearConfirmation,
        ]
    );
}
