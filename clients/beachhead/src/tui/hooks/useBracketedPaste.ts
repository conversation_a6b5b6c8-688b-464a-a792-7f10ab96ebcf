import { useMemo, useRef } from "react";

import { InputBufferActions } from "../types/InputBufferTypes";
import { KeyEvent, KeyProcessor, ProcessingContext } from "../types/KeyProcessorTypes";
import { createActionResult, createEmptyResult } from "../utils/key-processing-utils";

/**
 * Pure function that implements the bracketed paste processing logic.
 * This can be tested independently of React hooks.
 */
export function bracketedPasteProcessor(
    name: string = "BracketedPaste",
    priority: number = 30
): KeyProcessor {
    return {
        name,
        priority,

        canHandle: (event: KeyEvent, context: ProcessingContext): boolean => {
            // Always handle content when we're in paste mode first
            if (context.isPastingRef?.current) {
                return true;
            }

            // Handle bracketed paste sequences
            if (
                event.input.includes("[200~") ||
                event.input.includes("[201~") ||
                event.input.includes("\x1b[200~") ||
                event.input.includes("\x1b[201~")
            ) {
                return true;
            }

            return false;
        },

        process: (event: KeyEvent, context: ProcessingContext) => {
            const input = event.input;
            const { pasteBufferRef, isPastingRef } = context;

            // Ensure refs are available
            if (!pasteBufferRef || !isPastingRef) {
                return createEmptyResult(false);
            }

            // If we're already in paste mode, we need to check for the end marker first
            if (isPastingRef.current) {
                let endMarkerStart = -1;

                // Look for [201~ first (without escape)
                let bracketEndIndex = input.indexOf("[201~");
                if (bracketEndIndex !== -1) {
                    endMarkerStart = bracketEndIndex;
                }

                // Look for \x1b[201~ (with escape)
                let escapeEndIndex = input.indexOf("\x1b[201~");
                if (escapeEndIndex !== -1) {
                    if (endMarkerStart === -1 || escapeEndIndex < endMarkerStart) {
                        endMarkerStart = escapeEndIndex;
                    }
                }

                if (endMarkerStart !== -1) {
                    // Found end marker - add any content before it to the buffer
                    if (endMarkerStart > 0) {
                        const finalContent = input.substring(0, endMarkerStart);
                        pasteBufferRef.current += finalContent;
                    }

                    // Get the complete paste content and reset state
                    const completeContent = pasteBufferRef.current;
                    pasteBufferRef.current = "";
                    isPastingRef.current = false;

                    // End the paste with the complete content
                    return createActionResult([InputBufferActions.endPaste(completeContent)]);
                } else {
                    // No end marker found - append all content to buffer
                    pasteBufferRef.current += input;
                    return createActionResult([]); // No actions needed, just buffering
                }
            }

            // Not in paste mode - check for start marker
            let startIndex = input.indexOf("[200~");
            if (startIndex === -1) {
                startIndex = input.indexOf("\x1b[200~");
            }

            if (startIndex !== -1) {
                // Found start marker
                const markerLength = input[startIndex] === "\x1b" ? 6 : 5;
                const contentStart = startIndex + markerLength;

                // Check if end marker is also in this chunk (complete paste in one input)
                let endMarkerStart = -1;

                // Look for [201~ first (without escape)
                let bracketEndIndex = input.indexOf("[201~", contentStart);
                if (bracketEndIndex !== -1) {
                    endMarkerStart = bracketEndIndex;
                }

                // Look for \x1b[201~ (with escape)
                let escapeEndIndex = input.indexOf("\x1b[201~", contentStart);
                if (escapeEndIndex !== -1) {
                    if (endMarkerStart === -1 || escapeEndIndex < endMarkerStart) {
                        endMarkerStart = escapeEndIndex;
                    }
                }

                if (endMarkerStart !== -1 && endMarkerStart >= contentStart) {
                    // Complete paste in single input
                    const content = input.substring(contentStart, endMarkerStart);
                    // Set paste mode, buffer content, and immediately end
                    isPastingRef.current = true;
                    pasteBufferRef.current = content;
                    isPastingRef.current = false;

                    return createActionResult([
                        InputBufferActions.startPaste(),
                        InputBufferActions.endPaste(content),
                    ]);
                } else {
                    // Only start marker found - begin paste mode
                    isPastingRef.current = true;
                    pasteBufferRef.current = "";

                    // Extract any content after the start marker
                    if (contentStart < input.length) {
                        const initialContent = input.substring(contentStart);
                        if (initialContent.length > 0) {
                            pasteBufferRef.current = initialContent;
                        }
                    }

                    return createActionResult([InputBufferActions.startPaste()]);
                }
            }

            // No paste markers found and not in paste mode
            return createEmptyResult(false); // Not our event
        },
    };
}

export interface UseBracketedPasteOptions {
    /** Priority for this processor (default: 30) */
    priority?: number;
    /** Custom name for debugging */
    name?: string;
}

export interface UseBracketedPasteReturn {
    bracketedPasteProcessor: KeyProcessor;
    pasteBufferRef: React.MutableRefObject<string>;
    isPastingRef: React.MutableRefObject<boolean>;
}

/**
 * Hook that provides a key processor for handling bracketed paste mode.
 * Detects paste start/end markers and manages paste content accumulation.
 *
 * Returns both the processor and refs that need to be passed through context.
 * The refs are created here but must be passed through ProcessingContext to
 * ensure the processor always uses the same ref instances across renders.
 */
export function useBracketedPaste(options: UseBracketedPasteOptions = {}): UseBracketedPasteReturn {
    const { priority = 30, name = "BracketedPaste" } = options;

    // Create stable refs for paste handling
    const pasteBufferRef = useRef<string>("");
    const isPastingRef = useRef<boolean>(false);

    const processor = useMemo(() => bracketedPasteProcessor(name, priority), [priority, name]);

    return {
        bracketedPasteProcessor: processor,
        pasteBufferRef,
        isPastingRef,
    };
}
