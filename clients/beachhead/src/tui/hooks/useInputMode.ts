import { useCallback, useState } from "react";

import { InputMode } from "../types/InputMode";

export interface UseInputModeReturn {
    mode: InputMode;
    setMode: (mode: InputMode) => void;
    toggleMode: () => void;
    isCommandMode: boolean;
    isNormalMode: boolean;
}

/**
 * Hook for managing input modes in the TUI
 */
export function useInputMode(initialMode: InputMode = InputMode.Normal): UseInputModeReturn {
    const [mode, setMode] = useState<InputMode>(initialMode);

    const toggleMode = useCallback(() => {
        setMode((currentMode) =>
            currentMode !== InputMode.Normal ? InputMode.Normal : currentMode
        );
    }, []);

    const isCommandMode = mode === InputMode.Command;
    const isNormalMode = mode === InputMode.Normal;

    return {
        mode,
        setMode,
        toggleMode,
        isCommandMode,
        isNormalMode,
    };
}
