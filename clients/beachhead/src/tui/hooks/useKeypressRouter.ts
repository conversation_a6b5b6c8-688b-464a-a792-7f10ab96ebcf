import { KeyEvent, KeyProcessor, ProcessingContext, SideEffect } from "../types/KeyProcessorTypes";
import { NotificationSetter, NotificationState } from "../types/NotificationTypes";
import { useDefaultInput } from "./useDefaultInput";
import { useKeypress } from "./useKeypress";

interface UseKeypressRouterOptions {
    processors?: KeyProcessor[];
    context?: ProcessingContext;
    setNotification?: NotificationSetter;
    setMode?: (mode: any) => void;
    processCommand?: (
        command: string,
        onSuccess?: (command: string) => void,
        onError?: (error: string) => void
    ) => { handled: boolean; sideEffects: SideEffect[] } | boolean;
    addSystemEntry?: (message: string, hasBorder: boolean) => void;
}

export function useKeypressRouter({
    processors = [],
    context,
    setNotification,
    setMode,
    processCommand,
    addSystemEntry,
}: UseKeypressRouterOptions = {}) {
    // Set up default input processor
    const defaultInputProcessor = useDefaultInput({
        priority: 10,
    });

    // Combine provided processors with default processor
    const allProcessors = [defaultInputProcessor, ...processors];

    // Sort processors by priority (higher priority first)
    const sortedProcessors = allProcessors.sort((a, b) => (b.priority || 0) - (a.priority || 0));

    useKeypress({
        onKeypress: (input, key) => {
            // Create KeyEvent from Ink input
            const keyEvent: KeyEvent = { input, key };

            // Try each processor in priority order
            for (const processor of sortedProcessors) {
                if (processor.canHandle(keyEvent, context || {})) {
                    const result = processor.process(keyEvent, context || {});

                    if (result.handled) {
                        // Dispatch actions if provided in context
                        if (result.actions.length > 0 && context?.dispatch) {
                            const dispatch = context.dispatch;
                            result.actions.forEach((action) => {
                                dispatch(action);
                            });
                        }

                        // Handle side effects
                        if (result.sideEffects && result.sideEffects.length > 0) {
                            result.sideEffects.forEach((sideEffect) =>
                                executeSideEffect(sideEffect, {
                                    setNotification,
                                    setMode,
                                    processCommand,
                                    addSystemEntry,
                                })
                            );
                        }
                        return; // Event was handled, stop processing
                    }
                }
            }
        },
    });
}

function executeSideEffect(
    sideEffect: SideEffect,
    handlers: {
        setNotification?: NotificationSetter;
        setMode?: (mode: any) => void;
        processCommand?: (
            command: string,
            onSuccess?: (command: string) => void,
            onError?: (error: string) => void
        ) => { handled: boolean; sideEffects: SideEffect[] } | boolean;
        addSystemEntry?: (message: string, hasBorder: boolean) => void;
    }
) {
    switch (sideEffect.type) {
        case "SET_NOTIFICATION":
            if (handlers.setNotification) {
                const notification = sideEffect.payload;
                handlers.setNotification(notification);

                // Handle timeout if specified
                if (notification && notification.timeout) {
                    setTimeout(() => {
                        handlers.setNotification?.(null);
                    }, notification.timeout);
                }
            }
            break;
        case "SET_MODE":
            if (handlers.setMode) {
                handlers.setMode(sideEffect.payload);
            }
            break;
        case "EXECUTE_COMMAND":
            if (handlers.processCommand) {
                const { command, onSuccess, onError } = sideEffect.payload;
                const result = handlers.processCommand(
                    command,
                    (successCommand) => {
                        // Execute success side effects
                        if (onSuccess) {
                            const successSideEffects = onSuccess(successCommand);
                            successSideEffects.forEach((se: SideEffect) =>
                                executeSideEffect(se, handlers)
                            );
                        }
                    },
                    (errorMessage) => {
                        // Execute error side effects
                        // Execute error side effects
                        if (onError) {
                            const errorSideEffects = onError(errorMessage);
                            errorSideEffects.forEach((se: SideEffect) =>
                                executeSideEffect(se, handlers)
                            );
                        }
                    }
                );

                // Execute any side effects returned by the command
                if (
                    result &&
                    typeof result === "object" &&
                    "sideEffects" in result &&
                    Array.isArray(result.sideEffects)
                ) {
                    result.sideEffects.forEach((se: SideEffect) => executeSideEffect(se, handlers));
                }
            }
            break;
        case "ADD_SYSTEM_ENTRY":
            if (handlers.addSystemEntry) {
                const { message, hasBorder } = sideEffect.payload;
                handlers.addSystemEntry(message, hasBorder);
            }
            break;
        // Add other side effect types as needed
        default:
            console.warn("Unknown side effect type:", sideEffect.type);
    }
}

/**
 * Helper function to create a notification side effect
 */
export function createNotificationSideEffect(notification: NotificationState): SideEffect {
    return {
        type: "SET_NOTIFICATION",
        payload: notification,
    };
}

/**
 * Helper function to create a mode change side effect
 */
export function createModeSideEffect(mode: any): SideEffect {
    return {
        type: "SET_MODE",
        payload: mode,
    };
}

/**
 * Helper function to create a command execution side effect
 */
export function createExecuteCommandSideEffect(
    command: string,
    onSuccess?: (command: string) => SideEffect[],
    onError?: (error: string) => SideEffect[]
): SideEffect {
    return {
        type: "EXECUTE_COMMAND",
        payload: {
            command,
            onSuccess,
            onError,
        },
    };
}

/**
 * Helper function to create a system entry side effect
 */
export function createSystemEntrySideEffect(message: string, hasBorder: boolean): SideEffect {
    return {
        type: "ADD_SYSTEM_ENTRY",
        payload: {
            message,
            hasBorder,
        },
    };
}
