import { useCallback } from "react";

import { AgentState } from "../../agent_loop/state";
import { SideEffect } from "../types/KeyProcessorTypes";
import { createSystemEntrySideEffect } from "./useKeypressRouter";

interface UseRequestIdOptions {
    agentState: AgentState;
}

/**
 * Hook for handling request ID display command
 */
export function useRequestId({ agentState }: UseRequestIdOptions) {
    const handleRequestId = useCallback((): SideEffect[] => {
        const chatHistory = agentState.chatHistory;
        if (chatHistory.length === 0) {
            return [createSystemEntrySideEffect("No messages found", false)];
        }

        // Get the most recent exchange
        const mostRecentExchange = chatHistory[chatHistory.length - 1];
        const requestId = mostRecentExchange.exchange?.request_id;

        if (requestId) {
            return [createSystemEntrySideEffect(`Request ID: ${requestId}`, false)];
        } else {
            return [
                createSystemEntrySideEffect(
                    "No request ID found for the most recent message",
                    false
                ),
            ];
        }
    }, [agentState]);

    return handleRequestId;
}
