/* eslint-disable no-case-declarations */
import { useCallback, useState, useMemo } from "react";
import React from "react";
import { isEqual } from "lodash";
import {
    WorkflowConfig,
    WorkflowWizardState,
    GENERATION_STEPS,
    SETUP_STEPS
} from "../types/WorkflowTypes";
import { writeWorkflowFile } from "../utils/fileUtils";
import { GitHubWorkflowWizard } from "../ui/components/GitHubWorkflowWizard";
import { resetGlobalEditingState } from "../ui/components/steps/InputStep";
import { usePopover, KeyboardShortcut } from "./usePopover";
import { KeyProcessor, KeyEvent, ProcessingContext } from "../types/KeyProcessorTypes";
import { createEmptyResult } from "../utils/key-processing-utils";

export interface GitHubWorkflowWizardProps {
    show: boolean;
    wizardState: WorkflowWizardState;
    onNext: () => void;
    onPrevious: () => void;
    onUpdateConfig: (updates: Partial<WorkflowConfig>) => void;
    onGenerate: () => void;
    onDismiss: () => void;
}

interface UseGitHubWorkflowWizardReturn {
    showWizard: boolean;
    openWizard: () => void;
    closeWizard: () => void;
    wizardState: WorkflowWizardState;
    nextStep: () => void;
    previousStep: () => void;
    updateConfig: (updates: Partial<WorkflowConfig>) => void;
    generateWorkflow: () => void;
    shortcutsText: string; // Formatted shortcuts text for display
    GitHubWorkflowWizard: React.ComponentType;
    processor: KeyProcessor; // Key processor for handling keypress events
}

interface UseGitHubWorkflowWizardProcessorOptions {
    showWizard: boolean;
    closeWizard: () => void;
    priority?: number;
}

/**
 * Creates a processor for GitHub Workflow Wizard keypress handling
 */
function useGitHubWorkflowWizardProcessor(
    options: UseGitHubWorkflowWizardProcessorOptions
): KeyProcessor {
    const { showWizard, closeWizard, priority = 30 } = options;

    return useMemo(
        (): KeyProcessor => ({
            name: "GitHubWorkflowWizard",
            priority: priority, // Higher than other processors to consume keys when active

            canHandle: (event: KeyEvent, _context: ProcessingContext): boolean => {
                // Only handle events when the wizard is shown
                if (!showWizard) {
                    return false;
                }

                // Handle escape to close wizard
                if (event.key.escape) {
                    return true;
                }

                // While wizard is open, consume all key presses to prevent other handlers
                return true;
            },

            process: (event: KeyEvent, _context: ProcessingContext) => {
                // Handle escape to close wizard
                if (event.key.escape) {
                    closeWizard();
                    return createEmptyResult(true); // handled = true
                }

                // For all other keys while wizard is open, just consume them
                // The actual wizard component will handle its own internal navigation
                return createEmptyResult(true); // handled = true to prevent bubbling
            },
        }),
        [showWizard, closeWizard, priority]
    );
}

/**
 * Hook for managing GitHub workflow wizard state and behavior
 */
export function useGitHubWorkflowWizard(): UseGitHubWorkflowWizardReturn {
    const [showWizard, setShowWizard] = useState(false);
    const [wizardState, setWizardState] = useState<WorkflowWizardState>({
        mode: "generation",
        currentStep: 0,
        config: {},
        isComplete: false,
        isGenerating: false,
    });

    // Helper function to get current steps based on mode
    const getCurrentSteps = useCallback(() => {
        return wizardState.mode === "generation" ? GENERATION_STEPS : SETUP_STEPS;
    }, [wizardState.mode]);

    // Calculate dynamic shortcuts based on wizard state and current step context
    const shortcuts = useMemo((): KeyboardShortcut[] => {
        if (!showWizard) {return [];}

        const currentSteps = getCurrentSteps();
        const isFirstStep = wizardState.currentStep === 0;
        const isLastStep = wizardState.currentStep === currentSteps.length - 1;
        const currentStep = currentSteps[wizardState.currentStep];

        const baseShortcuts: KeyboardShortcut[] = [];

        // Step-specific shortcuts based on step type
        if (currentStep) {
            switch (currentStep.id) {
                case "action":
                case "trigger":
                    // Selection steps
                    baseShortcuts.push({ key: "↑↓", description: "to select" });
                    break;
                case "options":
                    // Options step - depends on the trigger type
                    const trigger = wizardState.config.trigger;
                    const configOptions = trigger?.getConfigurationOptions() || [];
                    const booleanFields = configOptions.filter(opt => opt.type === "boolean");
                    const textFields = configOptions.filter(opt => opt.type === "text");

                    if (booleanFields.length > 0 && textFields.length === 0) {
                        // Toggle step
                        baseShortcuts.push({ key: "Space", description: "to toggle" });
                    } else if (textFields.length > 0) {
                        // Input step
                        baseShortcuts.push({ key: "E", description: "to edit" });
                    }
                    break;
                case "confirm":
                    // Confirmation step
                    baseShortcuts.push({ key: "Enter", description: "to generate" });
                    break;
                case "setup-credentials":
                case "setup-github-secret":
                    baseShortcuts.push({ key: "C", description: "to copy session" });
                    break;
                case "generating":
                case "setup-overview":
                case "setup-github-nav":
                case "setup-deploy":
                    // Standard Navigation
                    break;
            }
        }

        // Navigation shortcuts
        if (!isLastStep && currentStep?.id !== "confirm") {
            baseShortcuts.push({ key: "Enter", description: "to continue" });
        }

        if (!isFirstStep && currentStep?.id !== "generating") {
            baseShortcuts.push({ key: "Backspace", description: "to go back" });
        }

        // Always include escape
        if (currentStep?.id !== "generating" && wizardState.mode !== "setup") {
            baseShortcuts.push({ key: "Esc", description: "to cancel" });
        }
        if (wizardState.mode === "setup") {
            baseShortcuts.push({ key: "Esc", description: "to close" });
        }

        return baseShortcuts;
    }, [showWizard, wizardState.currentStep, wizardState.mode, wizardState.config.trigger, getCurrentSteps]);

    // Use popover hook for consistent shortcuts formatting
    const popover = usePopover({ shortcuts });

    const openWizard = useCallback(() => {
        setShowWizard(true);
        setWizardState({
            mode: "generation",
            currentStep: 0,
            config: {},
            isComplete: false,
            isGenerating: false,
        });
    }, []);

    const closeWizard = useCallback(() => {
        setShowWizard(false);
        setWizardState({
            mode: "generation",
            currentStep: 0,
            config: {},
            isComplete: false,
            isGenerating: false,
        });
        // Reset any global editing state when wizard closes
        resetGlobalEditingState();
    }, []);

    const updateConfig = useCallback((updates: Partial<WorkflowConfig>) => {
        setWizardState(prev => {
            const newConfig = { ...prev.config, ...updates };

            // Check if the config actually changed to prevent unnecessary updates
            const configChanged = !isEqual(prev.config, newConfig);

            // Only update state if config actually changed
            if (!configChanged) {
                return prev; // Return same state to prevent re-render
            }

            return {
                ...prev,
                config: newConfig,
                error: undefined, // Clear any previous errors
            };
        });
    }, []);

    const nextStep = useCallback(() => {
        setWizardState(prev => {
            const currentSteps = prev.mode === "generation" ? GENERATION_STEPS : SETUP_STEPS;

            // Generation mode validations
            if (prev.mode === "generation") {
                if (prev.currentStep === 0 && !prev.config.action) {
                    return {
                        ...prev,
                        error: "Please select an action",
                    };
                }

                if (prev.currentStep === 1 && !prev.config.trigger) {
                    return {
                        ...prev,
                        error: "Please select a trigger",
                    };
                }

                if (prev.currentStep === 2) {
                    // Use trigger validation for options step
                    const trigger = prev.config.trigger;
                    if (trigger && !trigger.isConfigValid(prev.config.triggerConfig || {})) {
                        return {
                            ...prev,
                            error: "Please complete the required configuration options",
                        };
                    }
                }

                // Don't allow manual navigation past the confirmation step in generation mode
                if (prev.currentStep >= 3) {
                    return prev;
                }
            }

            const nextStepIndex = Math.min(prev.currentStep + 1, currentSteps.length - 1);

            // Auto-populate trigger config when moving to options step (generation mode only)
            if (prev.mode === "generation" && nextStepIndex === 2 && prev.config.trigger && (!prev.config.triggerConfig || prev.config.triggerConfig === undefined)) {
                const trigger = prev.config.trigger;
                const defaultConfig = trigger.getDefaultConfigForAction(prev.config.action);
                return {
                    ...prev,
                    currentStep: nextStepIndex,
                    config: { ...prev.config, triggerConfig: defaultConfig },
                    error: undefined,
                };
            }

            return {
                ...prev,
                currentStep: nextStepIndex,
                error: undefined,
            };
        });
    }, []);

    const previousStep = useCallback(() => {
        setWizardState(prev => ({
            ...prev,
            currentStep: Math.max(prev.currentStep - 1, 0),
            error: undefined,
        }));
    }, []);

    const generateWorkflow = useCallback(() => {
        setWizardState(prev => ({
            ...prev,
            currentStep: 4, // Move to generating step
            isGenerating: true,
            error: undefined
        }));

        // Perform the actual generation asynchronously with a longer delay
        // to ensure the GeneratingStep is rendered first
        setTimeout(() => {
            setWizardState(prev => {
                try {
                    const result = writeWorkflowFile(prev.config);

                    if (result.success) {
                        return {
                            ...prev,
                            mode: "setup", // Switch to setup mode
                            currentStep: 0, // Start at first setup step
                            isGenerating: false,
                            isComplete: false, // Not complete until setup is done
                            error: undefined,
                        };
                    } else {
                        return {
                            ...prev,
                            currentStep: 3,
                            isGenerating: false,
                            error: result.error || "Failed to generate workflow file",
                        };
                    }
                } catch (error) {
                    return {
                        ...prev,
                        currentStep: 3,
                        isGenerating: false,
                        error: error instanceof Error ? error.message : "Unknown error occurred",
                    };
                }
            });
        }, 500); // Longer delay to ensure GeneratingStep renders and can see the transition
    }, []);

    // Create the key processor for handling wizard keypress events
    const processor = useGitHubWorkflowWizardProcessor({
        showWizard,
        closeWizard,
        priority: 30, // High priority to consume keys when wizard is active
    });

    // Create a component that's already wired up with the wizard state
    const GitHubWorkflowWizardComponent = useCallback(() => {
        return React.createElement(GitHubWorkflowWizard, {
            show: showWizard,
            wizardState,
            onNext: nextStep,
            onPrevious: previousStep,
            onUpdateConfig: updateConfig,
            onGenerate: generateWorkflow,
            onDismiss: closeWizard,
            onClose: closeWizard,
            shortcutsText: popover.shortcutsText,
        });
    }, [showWizard, wizardState, nextStep, previousStep, updateConfig, generateWorkflow, closeWizard, popover.shortcutsText]);

    return {
        showWizard,
        openWizard,
        closeWizard,
        wizardState,
        nextStep,
        previousStep,
        updateConfig,
        generateWorkflow,
        shortcutsText: popover.shortcutsText,
        GitHubWorkflowWizard: GitHubWorkflowWizardComponent,
        processor,
    };
}
