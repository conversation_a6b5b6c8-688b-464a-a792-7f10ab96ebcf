/**
 * useHistoryNavigation - Hook that creates a history navigation processor
 *
 * This processor handles arrow key navigation through command history.
 */
import {
    ChatRequestNode,
    ChatRequestNodeType,
} from "@augment-internal/sidecar-libs/src/chat/chat-types";
import { useMemo, useRef, useState } from "react";

import { ExchangeState } from "../../agent_loop/state";
import { InputBufferActions } from "../types/InputBufferTypes";
import { InputMode } from "../types/InputMode";
import { KeyEvent, KeyProcessor, ProcessingContext } from "../types/KeyProcessorTypes";
import { createActionResult, createEmptyResult } from "../utils/key-processing-utils";

export interface UseHistoryNavigationOptions {
    /** Priority for this processor (default: 15) */
    priority?: number;
    /** Custom name for debugging */
    name?: string;
    /** History items to navigate through */
    history: string[];
    /** Max history size */
    maxHistorySize?: number;
}

// Processor that handles history navigation using arrow keys
export function useHistoryNavigation(options: UseHistoryNavigationOptions): KeyProcessor {
    const {
        priority = 15, // Between shortcuts (20) and default input (10)
        name = "HistoryNavigation",
        history,
    } = options;

    // Track current position in history (-1 means not navigating)
    const [historyIndex, setHistoryIndex] = useState(-1);

    // Store the current input value when we start navigating
    const savedInputRef = useRef<string>("");

    return useMemo(
        (): KeyProcessor => ({
            name,
            priority,

            canHandle: (event: KeyEvent, context: ProcessingContext): boolean => {
                // Only handle arrow keys in Normal mode
                if (context.mode !== InputMode.Normal) {
                    return false;
                }

                return !!(event.key.upArrow || event.key.downArrow);
            },

            process: (event: KeyEvent, context: ProcessingContext) => {
                const currentText = context.bufferState?.text || "";

                if (event.key.upArrow) {
                    // Navigate to older history
                    if (history.length === 0) {
                        return createEmptyResult();
                    }

                    let newIndex = historyIndex;

                    if (historyIndex === -1) {
                        // Starting navigation - save current input
                        savedInputRef.current = currentText;
                        newIndex = history.length - 1;
                    } else if (historyIndex > 0) {
                        // Move to older entry
                        newIndex = historyIndex - 1;
                    }

                    if (newIndex !== historyIndex) {
                        setHistoryIndex(newIndex);
                        return createActionResult([
                            InputBufferActions.setText(history[newIndex]),
                            InputBufferActions.moveCursor(history[newIndex].length),
                        ]);
                    }
                }

                if (event.key.downArrow) {
                    // Navigate to newer history
                    if (historyIndex === -1) {
                        // Not currently navigating
                        return createEmptyResult();
                    }

                    let newIndex = historyIndex;
                    let newText = "";

                    if (historyIndex < history.length - 1) {
                        // Move to newer entry
                        newIndex = historyIndex + 1;
                        newText = history[newIndex];
                    } else {
                        // Return to saved input
                        newIndex = -1;
                        newText = savedInputRef.current;
                    }

                    setHistoryIndex(newIndex);
                    return createActionResult([
                        InputBufferActions.setText(newText),
                        InputBufferActions.moveCursor(newText.length),
                    ]);
                }

                return createEmptyResult();
            },
        }),
        [priority, name, history, historyIndex]
    );
}

/**
 * Hook to manage input history state
 */
export function useInputHistoryManager(initialHistory: string[] = [], maxSize = 100) {
    const [history, setHistory] = useState<string[]>(initialHistory);

    const addToHistory = (value: string) => {
        if (!value.trim()) {
            return;
        }

        setHistory((prev) => {
            // Remove duplicates
            const filtered = prev.filter((item) => item !== value);
            // Add to end (most recent)
            const updated = [...filtered, value];
            // Limit size
            if (updated.length > maxSize) {
                return updated.slice(updated.length - maxSize);
            }
            return updated;
        });
    };

    const clearHistory = () => setHistory([]);

    return { history, addToHistory, clearHistory };
}

/**
 * Combined hook that extracts history from agent state and provides navigation processor
 */
export function useInputHistory(
    agentState: { chatHistory: readonly ExchangeState[]; requestNodes: readonly ChatRequestNode[] },
    options?: Partial<UseHistoryNavigationOptions>
) {
    // Extract initial history from agent state
    const initialInputHistory = useMemo(() => {
        const historyMessages = extractUserMessagesFromChatHistory(agentState.chatHistory);
        const currentMessages = extractUserMessagesFromRequestNodes(agentState.requestNodes);
        return [...historyMessages, ...currentMessages];
    }, [agentState.chatHistory, agentState.requestNodes]);

    // Create history manager
    const historyManager = useInputHistoryManager(initialInputHistory, options?.maxHistorySize);

    // Create navigation processor
    const historyProcessor = useHistoryNavigation({
        ...options,
        history: historyManager.history,
    });

    return {
        historyManager,
        historyProcessor,
    };
}

/**
 * Extracts user text messages from chat history exchanges and current request nodes
 * Only includes TEXT type nodes from request_nodes, which represent actual user input
 */
export function extractUserMessagesFromChatHistory(
    chatHistory: readonly ExchangeState[]
): string[] {
    const userMessages: string[] = [];

    for (const exchangeState of chatHistory) {
        const exchange = exchangeState.exchange;

        // First try the legacy request_message field
        if (exchange.request_message && exchange.request_message.trim()) {
            userMessages.push(exchange.request_message.trim());
            continue;
        }

        // Then check request_nodes for TEXT nodes
        if (exchange.request_nodes) {
            for (const node of exchange.request_nodes) {
                if (node.type === ChatRequestNodeType.TEXT && node.text_node?.content) {
                    const content = node.text_node.content.trim();
                    if (content) {
                        userMessages.push(content);
                        // Only take the first text node per exchange to avoid duplicates
                        break;
                    }
                }
            }
        }
    }

    return userMessages;
}

/**
 * Extracts user text messages from current request nodes (pending/unfinished exchange)
 * This captures the "last message" that might not be in chat history yet
 */
export function extractUserMessagesFromRequestNodes(
    requestNodes: readonly ChatRequestNode[]
): string[] {
    const userMessages: string[] = [];

    for (const node of requestNodes) {
        if (node.type === ChatRequestNodeType.TEXT && node.text_node?.content) {
            const content = node.text_node.content.trim();
            if (content) {
                userMessages.push(content);
                // Only take the first text node to avoid duplicates
                break;
            }
        }
    }

    return userMessages;
}
