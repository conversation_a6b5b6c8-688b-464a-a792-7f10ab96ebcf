import { useEffect } from "react";

import { checkForUpdate, PACKAGE_NAME } from "../../utils/npm-version-check";
import { VERSION } from "../context";
import { NotificationSetter } from "../types/NotificationTypes";

interface UseUpdateNotifierOptions {
    setNotification: NotificationSetter;
}

/**
 * Hook that checks for package updates and shows notifications
 * for available updates or deprecated versions.
 */
export function useUpdateNotifier({ setNotification }: UseUpdateNotifierOptions): void {
    useEffect(() => {
        const checkForUpdates = async () => {
            const updateInfo = await checkForUpdate(VERSION);

            // Show deprecation warning if current version is deprecated
            if (updateInfo.currentVersionDeprecated) {
                setNotification({
                    type: "error",
                    message: `Current version ${VERSION} is deprecated. ${
                        updateInfo.deprecationMessage || "Please update to the latest version."
                    }`,
                    timeout: 10000, // Show for 10 seconds
                });

                // Clear notification after timeout
                setTimeout(() => {
                    setNotification(null);
                }, 10000);
            }
            // Show update notification if newer version available (and not already showing deprecation)
            else if (updateInfo.updateAvailable && updateInfo.latestVersion) {
                setNotification({
                    type: "info",
                    message: `A new version of ${PACKAGE_NAME} is available. Run 'npm install -g ${PACKAGE_NAME}' to upgrade.`,
                    timeout: 10000, // Show for 10 seconds
                });

                // Clear notification after timeout
                setTimeout(() => {
                    setNotification(null);
                }, 10000);
            }
        };

        checkForUpdates().catch(() => {
            // Silently fail if we can't check for updates
        });
    }, [setNotification, VERSION]);
}
