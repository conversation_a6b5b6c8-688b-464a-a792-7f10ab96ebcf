import { useCallback, useEffect, useMemo, useState } from "react";

import { InputBufferActions } from "../types/InputBufferTypes";
import { InputMode } from "../types/InputMode";
import { KeyEvent, KeyProcessor, ProcessingContext } from "../types/KeyProcessorTypes";
import { MenuItem, MenuProvider } from "../types/MenuProvider";
import { createActionResult, createEmptyResult } from "../utils/key-processing-utils";
import {
    createExecuteCommandSideEffect,
    createModeSideEffect,
    createNotificationSideEffect,
} from "./useKeypressRouter";
import { CommandMenuProvider } from "./useSlashCommand";

interface UseSelectMenuReturn {
    items: MenuItem[];
    activeMenuIndex: number;
    showMenu: boolean;
    menuNavigationProcessor: KeyProcessor;
}

interface UseSelectMenuOptions {
    query: string;
    slashCommands: any[];
    getCurrentMode: () => InputMode;
    triggeredBy?: any;
    priority?: number;
}

export function useSelectMenu({
    query,
    slashCommands,
    getCurrentMode,
    triggeredBy,
    priority = 24,
}: UseSelectMenuOptions): UseSelectMenuReturn {
    const [items, setItems] = useState<MenuItem[]>([]);
    const [activeMenuIndex, setActiveMenuIndex] = useState(-1);
    const [showMenu, setShowMenu] = useState(false);

    // Create menu providers - moved from Repl for better encapsulation
    const providers = useMemo<MenuProvider[]>(() => {
        const commandProvider = new CommandMenuProvider(slashCommands, getCurrentMode);
        return [commandProvider];
    }, [slashCommands, getCurrentMode]);

    useEffect(() => {
        const activeProvider = providers.find((p) => p.shouldShowMenu(query));

        if (!activeProvider) {
            setItems([]);
            setShowMenu(false);
            setActiveMenuIndex(-1);
            return;
        }

        const fetchItems = async () => {
            const newItems = await activeProvider.getMenuItems(query);
            setItems(newItems);
            setShowMenu(newItems.length > 0);
            setActiveMenuIndex(newItems.length > 0 ? 0 : -1);
        };

        fetchItems();
    }, [query, providers, triggeredBy]);

    const navigateUp = useCallback(() => {
        if (items.length === 0) {
            return;
        }
        setActiveMenuIndex((prev) => (prev > 0 ? prev - 1 : items.length - 1));
    }, [items.length]);

    const navigateDown = useCallback(() => {
        if (items.length === 0) {
            return;
        }
        setActiveMenuIndex((prev) => (prev + 1) % items.length);
    }, [items.length]);

    const selectCurrentItem = useCallback(() => {
        if (activeMenuIndex >= 0 && activeMenuIndex < items.length) {
            return items[activeMenuIndex];
        }
        return null;
    }, [activeMenuIndex, items]);

    // Create the menu navigation processor
    const menuNavigationProcessor = useMemo(
        (): KeyProcessor => ({
            name: "MenuNavigation",
            priority: priority,

            canHandle: (event: KeyEvent, context: ProcessingContext): boolean => {
                // Only handle keypresses when menu is shown
                if (!context.menu) {
                    return false;
                }

                // Handle up/down arrow navigation
                if (event.key.upArrow || event.key.downArrow) {
                    return true;
                }

                // Handle enter/return to select current item
                if (event.key.return) {
                    return true;
                }

                return false;
            },

            process: (event: KeyEvent, _context: ProcessingContext) => {
                // Handle up arrow navigation
                if (event.key.upArrow) {
                    navigateUp();
                    return createEmptyResult();
                }

                // Handle down arrow navigation
                if (event.key.downArrow) {
                    navigateDown();
                    return createEmptyResult();
                }

                // Handle enter/return to select current item
                if (event.key.return) {
                    const selectedItem = selectCurrentItem();
                    if (selectedItem) {
                        // Clear input and execute command
                        return createActionResult(
                            [InputBufferActions.setText("")],
                            [
                                createExecuteCommandSideEffect(
                                    selectedItem.value,
                                    (_command) => [
                                        // Success: return to normal mode
                                        createModeSideEffect(InputMode.Normal),
                                    ],
                                    (error) => [
                                        // Error: show notification (stay in current mode)
                                        createNotificationSideEffect({
                                            message: error,
                                            type: "error",
                                            timeout: 3000,
                                        }),
                                    ]
                                ),
                            ]
                        );
                    }
                    return createEmptyResult();
                }

                return createEmptyResult();
            },
        }),
        [navigateUp, navigateDown, selectCurrentItem, priority]
    );

    return {
        items,
        activeMenuIndex,
        showMenu,
        menuNavigationProcessor,
    };
}
