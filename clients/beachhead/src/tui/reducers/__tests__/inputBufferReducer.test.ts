import { inputBufferReducer } from "../inputBufferReducer";
import {
    InputBufferState,
    InputBufferAction,
    createInitialInputBufferState,
} from "../../types/InputBufferTypes";

describe("inputBufferReducer", () => {
    let initialState: InputBufferState;

    beforeEach(() => {
        initialState = createInitialInputBufferState("hello world", 80, "Enter text...", "> ");
    });

    describe("Text manipulation actions", () => {
        describe("SET_TEXT", () => {
            it("should set new text and update cursor position", () => {
                const action: InputBufferAction = { type: "SET_TEXT", text: "new text" };
                const result = inputBufferReducer(initialState, action);

                expect(result.text).toBe("new text");
                expect(result.cursorPosition).toBe(8); // length of "new text"
                expect(result.desiredColumn).toBeNull();
            });

            it("should clamp cursor position if text is shorter", () => {
                const state = { ...initialState, cursorPosition: 20 };
                const action: InputBufferAction = { type: "SET_TEXT", text: "short" };
                const result = inputBufferReducer(state, action);

                expect(result.text).toBe("short");
                expect(result.cursorPosition).toBe(5); // clamped to text length
            });
        });

        describe("INSERT_TEXT", () => {
            it("should insert text at cursor position", () => {
                const state = { ...initialState, cursorPosition: 5 }; // After "hello"
                const action: InputBufferAction = { type: "INSERT_TEXT", text: " beautiful" };
                const result = inputBufferReducer(state, action);

                expect(result.text).toBe("hello beautiful world");
                expect(result.cursorPosition).toBe(15); // After inserted text
                expect(result.desiredColumn).toBeNull();
            });

            it("should insert text at specified position", () => {
                const action: InputBufferAction = { 
                    type: "INSERT_TEXT", 
                    text: "beautiful ", 
                    position: 6 
                };
                const result = inputBufferReducer(initialState, action);

                expect(result.text).toBe("hello beautiful world");
                expect(result.cursorPosition).toBe(16); // After inserted text
            });
        });

        describe("DELETE_RANGE", () => {
            it("should delete text in specified range", () => {
                // "hello world" -> delete from 6 to 11 removes "world" -> "hello "
                const action: InputBufferAction = { type: "DELETE_RANGE", start: 6, end: 11 };
                const result = inputBufferReducer(initialState, action);

                expect(result.text).toBe("hello ");
                expect(result.cursorPosition).toBe(6);
                expect(result.desiredColumn).toBeNull();
            });

            it("should handle cursor position after deletion", () => {
                const state = { ...initialState, cursorPosition: 10 };
                const action: InputBufferAction = { type: "DELETE_RANGE", start: 6, end: 11 };
                const result = inputBufferReducer(state, action);

                expect(result.cursorPosition).toBe(6); // Clamped to start position
            });
        });

        describe("DELETE_CHAR_BACKWARD", () => {
            it("should delete character before cursor", () => {
                const state = { ...initialState, cursorPosition: 5 };
                const action: InputBufferAction = { type: "DELETE_CHAR_BACKWARD" };
                const result = inputBufferReducer(state, action);

                expect(result.text).toBe("hell world");
                expect(result.cursorPosition).toBe(4);
                expect(result.desiredColumn).toBeNull();
            });

            it("should not delete when cursor is at start", () => {
                const state = { ...initialState, cursorPosition: 0 };
                const action: InputBufferAction = { type: "DELETE_CHAR_BACKWARD" };
                const result = inputBufferReducer(state, action);

                expect(result).toBe(state); // Should return same state
            });
        });

        describe("DELETE_CHAR_FORWARD", () => {
            it("should delete character at cursor", () => {
                const state = { ...initialState, cursorPosition: 5 };
                const action: InputBufferAction = { type: "DELETE_CHAR_FORWARD" };
                const result = inputBufferReducer(state, action);

                expect(result.text).toBe("helloworld");
                expect(result.cursorPosition).toBe(5);
                expect(result.desiredColumn).toBeNull();
            });

            it("should not delete when cursor is at end", () => {
                const state = { ...initialState, cursorPosition: initialState.text.length };
                const action: InputBufferAction = { type: "DELETE_CHAR_FORWARD" };
                const result = inputBufferReducer(state, action);

                expect(result).toBe(state);
            });
        });

        describe("DELETE_WORD_BACKWARD", () => {
            it("should delete word before cursor", () => {
                const state = { ...initialState, cursorPosition: 11 }; // End of "world"
                const action: InputBufferAction = { type: "DELETE_WORD_BACKWARD" };
                const result = inputBufferReducer(state, action);

                expect(result.text).toBe("hello ");
                expect(result.cursorPosition).toBe(6);
                expect(result.desiredColumn).toBeNull();
            });

            it("should not delete when cursor is at start", () => {
                const state = { ...initialState, cursorPosition: 0 };
                const action: InputBufferAction = { type: "DELETE_WORD_BACKWARD" };
                const result = inputBufferReducer(state, action);

                expect(result).toBe(state);
            });
        });

        describe("DELETE_WORD_FORWARD", () => {
            it("should delete word after cursor", () => {
                const state = { ...initialState, cursorPosition: 0 };
                const action: InputBufferAction = { type: "DELETE_WORD_FORWARD" };
                const result = inputBufferReducer(state, action);

                expect(result.text).toBe("world");
                expect(result.cursorPosition).toBe(0);
                expect(result.desiredColumn).toBeNull();
            });

            it("should not delete when cursor is at end", () => {
                const state = { ...initialState, cursorPosition: initialState.text.length };
                const action: InputBufferAction = { type: "DELETE_WORD_FORWARD" };
                const result = inputBufferReducer(state, action);

                expect(result).toBe(state);
            });
        });

        describe("DELETE_TO_LINE_START", () => {
            it("should delete from cursor to start", () => {
                const state = { ...initialState, cursorPosition: 6 };
                const action: InputBufferAction = { type: "DELETE_TO_LINE_START" };
                const result = inputBufferReducer(state, action);

                expect(result.text).toBe("world");
                expect(result.cursorPosition).toBe(0);
                expect(result.desiredColumn).toBeNull();
            });
        });

        describe("DELETE_TO_LINE_END", () => {
            it("should delete from cursor to end", () => {
                const state = { ...initialState, cursorPosition: 5 };
                const action: InputBufferAction = { type: "DELETE_TO_LINE_END" };
                const result = inputBufferReducer(state, action);

                expect(result.text).toBe("hello");
                expect(result.cursorPosition).toBe(5);
                expect(result.desiredColumn).toBeNull();
            });
        });

        describe("TRANSPOSE_CHARS", () => {
            it("should transpose characters at cursor", () => {
                const state = { ...initialState, cursorPosition: 5 }; // Between 'o' and ' '
                const action: InputBufferAction = { type: "TRANSPOSE_CHARS" };
                const result = inputBufferReducer(state, action);

                // Transposes 'o' and ' ' -> "hell oworld"
                expect(result.text).toBe("hell oworld");
                expect(result.cursorPosition).toBe(6);
                expect(result.desiredColumn).toBeNull();
            });

            it("should not transpose at boundaries", () => {
                const state = { ...initialState, cursorPosition: 0 };
                const action: InputBufferAction = { type: "TRANSPOSE_CHARS" };
                const result = inputBufferReducer(state, action);

                expect(result.text).toBe(initialState.text);
                expect(result.cursorPosition).toBe(0);
            });
        });

        describe("INSERT_NEWLINE", () => {
            it("should insert newline at cursor", () => {
                const state = { ...initialState, cursorPosition: 5 };
                const action: InputBufferAction = { type: "INSERT_NEWLINE" };
                const result = inputBufferReducer(state, action);

                expect(result.text).toBe("hello\n world");
                expect(result.cursorPosition).toBe(6);
                expect(result.desiredColumn).toBeNull();
            });
        });
    });

    describe("Cursor movement actions", () => {
        describe("MOVE_CURSOR", () => {
            it("should move cursor to specified position", () => {
                const action: InputBufferAction = { type: "MOVE_CURSOR", position: 5 };
                const result = inputBufferReducer(initialState, action);

                expect(result.cursorPosition).toBe(5);
                expect(result.desiredColumn).toBeNull();
            });

            it("should preserve desired column when specified", () => {
                const state = { ...initialState, desiredColumn: 3 };
                const action: InputBufferAction = { 
                    type: "MOVE_CURSOR", 
                    position: 5, 
                    preserveDesiredColumn: true 
                };
                const result = inputBufferReducer(state, action);

                expect(result.cursorPosition).toBe(5);
                expect(result.desiredColumn).toBe(3);
            });

            it("should clamp cursor position to valid range", () => {
                const action: InputBufferAction = { type: "MOVE_CURSOR", position: 100 };
                const result = inputBufferReducer(initialState, action);

                expect(result.cursorPosition).toBe(initialState.text.length);
            });
        });

        describe("MOVE_CURSOR_START", () => {
            it("should move cursor to start", () => {
                const state = { ...initialState, cursorPosition: 5 };
                const action: InputBufferAction = { type: "MOVE_CURSOR_START" };
                const result = inputBufferReducer(state, action);

                expect(result.cursorPosition).toBe(0);
                expect(result.desiredColumn).toBeNull();
            });
        });

        describe("MOVE_CURSOR_END", () => {
            it("should move cursor to end", () => {
                const state = { ...initialState, cursorPosition: 5 };
                const action: InputBufferAction = { type: "MOVE_CURSOR_END" };
                const result = inputBufferReducer(state, action);

                expect(result.cursorPosition).toBe(initialState.text.length);
                expect(result.desiredColumn).toBeNull();
            });
        });

        describe("MOVE_CURSOR_LEFT", () => {
            it("should move cursor left by one", () => {
                const state = { ...initialState, cursorPosition: 5 };
                const action: InputBufferAction = { type: "MOVE_CURSOR_LEFT" };
                const result = inputBufferReducer(state, action);

                expect(result.cursorPosition).toBe(4);
                expect(result.desiredColumn).toBeNull();
            });

            it("should move cursor left by specified count", () => {
                const state = { ...initialState, cursorPosition: 5 };
                const action: InputBufferAction = { type: "MOVE_CURSOR_LEFT", count: 3 };
                const result = inputBufferReducer(state, action);

                expect(result.cursorPosition).toBe(2);
            });

            it("should not move past start", () => {
                const state = { ...initialState, cursorPosition: 2 };
                const action: InputBufferAction = { type: "MOVE_CURSOR_LEFT", count: 5 };
                const result = inputBufferReducer(state, action);

                expect(result.cursorPosition).toBe(0);
            });
        });

        describe("MOVE_CURSOR_RIGHT", () => {
            it("should move cursor right by one", () => {
                const state = { ...initialState, cursorPosition: 5 };
                const action: InputBufferAction = { type: "MOVE_CURSOR_RIGHT" };
                const result = inputBufferReducer(state, action);

                expect(result.cursorPosition).toBe(6);
                expect(result.desiredColumn).toBeNull();
            });

            it("should move cursor right by specified count", () => {
                const state = { ...initialState, cursorPosition: 5 };
                const action: InputBufferAction = { type: "MOVE_CURSOR_RIGHT", count: 3 };
                const result = inputBufferReducer(state, action);

                expect(result.cursorPosition).toBe(8);
            });

            it("should not move past end", () => {
                const state = { ...initialState, cursorPosition: 10 };
                const action: InputBufferAction = { type: "MOVE_CURSOR_RIGHT", count: 5 };
                const result = inputBufferReducer(state, action);

                expect(result.cursorPosition).toBe(initialState.text.length);
            });
        });

        describe("SET_DESIRED_COLUMN", () => {
            it("should set desired column", () => {
                const action: InputBufferAction = { type: "SET_DESIRED_COLUMN", column: 5 };
                const result = inputBufferReducer(initialState, action);

                expect(result.desiredColumn).toBe(5);
                expect(result.cursorPosition).toBe(initialState.cursorPosition);
            });

            it("should clear desired column when set to null", () => {
                const state = { ...initialState, desiredColumn: 5 };
                const action: InputBufferAction = { type: "SET_DESIRED_COLUMN", column: null };
                const result = inputBufferReducer(state, action);

                expect(result.desiredColumn).toBeNull();
            });
        });
    });

    describe("Paste actions", () => {
        describe("START_PASTE", () => {
            it("should start paste mode", () => {
                const action: InputBufferAction = { type: "START_PASTE" };
                const result = inputBufferReducer(initialState, action);

                expect(result.isPasting).toBe(true);
                expect(result.pasteBuffer).toBe("");
            });
        });

        describe("APPEND_PASTE_CONTENT", () => {
            it("should append content to paste buffer", () => {
                const state = { ...initialState, isPasting: true, pasteBuffer: "hello" };
                const action: InputBufferAction = { type: "APPEND_PASTE_CONTENT", content: " world" };
                const result = inputBufferReducer(state, action);

                expect(result.pasteBuffer).toBe("hello world");
                expect(result.isPasting).toBe(true);
            });
        });

        describe("END_PASTE", () => {
            it("should insert paste buffer content", () => {
                const state = { 
                    ...initialState, 
                    isPasting: true, 
                    pasteBuffer: "pasted text",
                    cursorPosition: 5 
                };
                const action: InputBufferAction = { type: "END_PASTE" };
                const result = inputBufferReducer(state, action);

                expect(result.text).toBe("hellopasted text world");
                expect(result.cursorPosition).toBe(16); // 5 + length of "pasted text"
                expect(result.isPasting).toBe(false);
                expect(result.pasteBuffer).toBe("");
                expect(result.desiredColumn).toBeNull();
            });

            it("should use final content when provided", () => {
                const state = { 
                    ...initialState, 
                    isPasting: true, 
                    pasteBuffer: "old content",
                    cursorPosition: 5 
                };
                const action: InputBufferAction = { 
                    type: "END_PASTE", 
                    finalContent: "final content" 
                };
                const result = inputBufferReducer(state, action);

                expect(result.text).toBe("hellofinal content world");
                expect(result.cursorPosition).toBe(18); // 5 + length of "final content"
            });

            it("should clean pasted text", () => {
                const state = { 
                    ...initialState, 
                    isPasting: true, 
                    pasteBuffer: "text\r\nwith\rlinebreaks",
                    cursorPosition: 5 
                };
                const action: InputBufferAction = { type: "END_PASTE" };
                const result = inputBufferReducer(state, action);

                expect(result.text).toBe("hellotext\nwith\nlinebreaks world");
            });
        });

        describe("CANCEL_PASTE", () => {
            it("should cancel paste mode", () => {
                const state = { 
                    ...initialState, 
                    isPasting: true, 
                    pasteBuffer: "some content" 
                };
                const action: InputBufferAction = { type: "CANCEL_PASTE" };
                const result = inputBufferReducer(state, action);

                expect(result.isPasting).toBe(false);
                expect(result.pasteBuffer).toBe("");
                expect(result.text).toBe(initialState.text); // Text unchanged
            });
        });
    });

    describe("Configuration actions", () => {
        describe("SET_AVAILABLE_WIDTH", () => {
            it("should set available width", () => {
                const action: InputBufferAction = { type: "SET_AVAILABLE_WIDTH", width: 120 };
                const result = inputBufferReducer(initialState, action);

                expect(result.availableWidth).toBe(120);
            });
        });

        describe("SET_PLACEHOLDER", () => {
            it("should set placeholder", () => {
                const action: InputBufferAction = { 
                    type: "SET_PLACEHOLDER", 
                    placeholder: "New placeholder" 
                };
                const result = inputBufferReducer(initialState, action);

                expect(result.placeholder).toBe("New placeholder");
            });
        });

        describe("SET_PROMPT_PREFIX", () => {
            it("should set prompt prefix", () => {
                const action: InputBufferAction = { 
                    type: "SET_PROMPT_PREFIX", 
                    promptPrefix: "$ " 
                };
                const result = inputBufferReducer(initialState, action);

                expect(result.promptPrefix).toBe("$ ");
            });
        });
    });

    describe("Edge cases and immutability", () => {
        it("should not mutate original state", () => {
            const originalState = { ...initialState };
            const action: InputBufferAction = { type: "INSERT_TEXT", text: "test" };
            
            inputBufferReducer(initialState, action);

            expect(initialState).toEqual(originalState);
        });

        it("should handle empty text operations", () => {
            const emptyState = createInitialInputBufferState("", 80);
            const action: InputBufferAction = { type: "INSERT_TEXT", text: "first text" };
            const result = inputBufferReducer(emptyState, action);

            expect(result.text).toBe("first text");
            expect(result.cursorPosition).toBe(10);
        });

        it("should handle UP/DOWN movements with CursorManager", () => {
            // Single line text - movements should not change position but may set desiredColumn
            const upAction: InputBufferAction = { type: "MOVE_CURSOR_UP" };
            const downAction: InputBufferAction = { type: "MOVE_CURSOR_DOWN" };
            
            const upResult = inputBufferReducer(initialState, upAction);
            const downResult = inputBufferReducer(initialState, downAction);

            // Position should remain the same for single line
            expect(upResult.cursorPosition).toBe(initialState.cursorPosition);
            expect(downResult.cursorPosition).toBe(initialState.cursorPosition);
            
            // But desiredColumn might be set
            expect(upResult.desiredColumn).toBeNull(); // No change for single line
            expect(downResult.desiredColumn).toBeNull();
        });
    });
});