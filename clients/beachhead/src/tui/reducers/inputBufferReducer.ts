import { InputBufferAction, InputBufferState } from "../types/InputBufferTypes";
import {
    clampCursorPosition,
    cleanPastedText,
    deleteTextRange,
    findWordEnd,
    findWordStart,
    insertTextAt,
    transposeChars,
} from "../utils/text-utils";
import { moveVertically } from "../utils/cursor-utils";

/**
 * Pure reducer function for InputBuffer state management
 * Handles all text editing operations and cursor movements
 */
export function inputBufferReducer(
    state: InputBufferState,
    action: InputBufferAction
): InputBufferState {
    switch (action.type) {
        // Text manipulation actions
        case "SET_TEXT": {
            const newCursorPosition = clampCursorPosition(state.cursorPosition, action.text.length);
            return {
                ...state,
                text: action.text,
                cursorPosition: newCursorPosition,
                desiredColumn: null, // Reset desired column on text change
            };
        }

        case "INSERT_TEXT": {
            const insertPosition = action.position ?? state.cursorPosition;
            const newText = insertTextAt(state.text, action.text, insertPosition);
            const newCursorPosition = insertPosition + action.text.length;

            return {
                ...state,
                text: newText,
                cursorPosition: newCursorPosition,
                desiredColumn: null,
            };
        }

        case "DELETE_RANGE": {
            const { start, end } = action;
            const newText = deleteTextRange(state.text, start, end);
            const newCursorPosition = Math.min(start, newText.length);

            return {
                ...state,
                text: newText,
                cursorPosition: newCursorPosition,
                desiredColumn: null,
            };
        }

        case "DELETE_CHAR_BACKWARD": {
            if (state.cursorPosition <= 0) {
                return state;
            }

            const newText = deleteTextRange(
                state.text,
                state.cursorPosition - 1,
                state.cursorPosition
            );
            return {
                ...state,
                text: newText,
                cursorPosition: state.cursorPosition - 1,
                desiredColumn: null,
            };
        }

        case "DELETE_CHAR_FORWARD": {
            if (state.cursorPosition >= state.text.length) {
                return state;
            }

            const newText = deleteTextRange(
                state.text,
                state.cursorPosition,
                state.cursorPosition + 1
            );
            return {
                ...state,
                text: newText,
                desiredColumn: null,
            };
        }

        case "DELETE_WORD_BACKWARD": {
            if (state.cursorPosition <= 0) {
                return state;
            }

            const wordStart = findWordStart(state.text, state.cursorPosition);
            const newText = deleteTextRange(state.text, wordStart, state.cursorPosition);

            return {
                ...state,
                text: newText,
                cursorPosition: wordStart,
                desiredColumn: null,
            };
        }

        case "DELETE_WORD_FORWARD": {
            if (state.cursorPosition >= state.text.length) {
                return state;
            }

            const wordEnd = findWordEnd(state.text, state.cursorPosition);
            const newText = deleteTextRange(state.text, state.cursorPosition, wordEnd);

            return {
                ...state,
                text: newText,
                desiredColumn: null,
            };
        }

        case "DELETE_TO_LINE_START": {
            const newText = state.text.substring(state.cursorPosition);
            return {
                ...state,
                text: newText,
                cursorPosition: 0,
                desiredColumn: null,
            };
        }

        case "DELETE_TO_LINE_END": {
            const newText = state.text.substring(0, state.cursorPosition);
            return {
                ...state,
                text: newText,
                desiredColumn: null,
            };
        }

        case "TRANSPOSE_CHARS": {
            const { newText, newCursorPosition } = transposeChars(state.text, state.cursorPosition);
            return {
                ...state,
                text: newText,
                cursorPosition: newCursorPosition,
                desiredColumn: null,
            };
        }

        case "INSERT_NEWLINE": {
            const newText = insertTextAt(state.text, "\n", state.cursorPosition);
            return {
                ...state,
                text: newText,
                cursorPosition: state.cursorPosition + 1,
                desiredColumn: null,
            };
        }

        // Cursor movement actions
        case "MOVE_CURSOR": {
            const newPosition = clampCursorPosition(action.position, state.text.length);
            return {
                ...state,
                cursorPosition: newPosition,
                desiredColumn: action.preserveDesiredColumn ? state.desiredColumn : null,
            };
        }

        case "MOVE_CURSOR_START": {
            return {
                ...state,
                cursorPosition: 0,
                desiredColumn: null,
            };
        }

        case "MOVE_CURSOR_END": {
            return {
                ...state,
                cursorPosition: state.text.length,
                desiredColumn: null,
            };
        }

        case "MOVE_CURSOR_LEFT": {
            const count = action.count ?? 1;
            const newPosition = Math.max(0, state.cursorPosition - count);
            return {
                ...state,
                cursorPosition: newPosition,
                desiredColumn: null,
            };
        }

        case "MOVE_CURSOR_RIGHT": {
            const count = action.count ?? 1;
            const newPosition = Math.min(state.text.length, state.cursorPosition + count);
            return {
                ...state,
                cursorPosition: newPosition,
                desiredColumn: null,
            };
        }

        case "MOVE_CURSOR_WORD_LEFT": {
            const newPosition = findWordStart(state.text, state.cursorPosition);
            return {
                ...state,
                cursorPosition: newPosition,
                desiredColumn: null,
            };
        }

        case "MOVE_CURSOR_WORD_RIGHT": {
            const newPosition = findWordEnd(state.text, state.cursorPosition);
            return {
                ...state,
                cursorPosition: newPosition,
                desiredColumn: null,
            };
        }

        case "MOVE_CURSOR_UP": {
            const result = moveVertically(
                state.text,
                state.availableWidth,
                state.cursorPosition,
                'up',
                state.desiredColumn
            );
            
            return {
                ...state,
                cursorPosition: result.newCursorPosition,
                desiredColumn: result.newDesiredColumn,
            };
        }

        case "MOVE_CURSOR_DOWN": {
            const result = moveVertically(
                state.text,
                state.availableWidth,
                state.cursorPosition,
                'down',
                state.desiredColumn
            );
            
            return {
                ...state,
                cursorPosition: result.newCursorPosition,
                desiredColumn: result.newDesiredColumn,
            };
        }

        case "SET_DESIRED_COLUMN": {
            return {
                ...state,
                desiredColumn: action.column,
            };
        }

        // Paste actions
        case "START_PASTE": {
            return {
                ...state,
                isPasting: true,
                pasteBuffer: "",
            };
        }

        case "APPEND_PASTE_CONTENT": {
            return {
                ...state,
                pasteBuffer: state.pasteBuffer + action.content,
            };
        }

        case "END_PASTE": {
            const contentToInsert = action.finalContent ?? state.pasteBuffer;
            const cleanedContent = cleanPastedText(contentToInsert);
            const newText = insertTextAt(state.text, cleanedContent, state.cursorPosition);

            return {
                ...state,
                text: newText,
                cursorPosition: state.cursorPosition + cleanedContent.length,
                isPasting: false,
                pasteBuffer: "",
                desiredColumn: null,
            };
        }

        case "CANCEL_PASTE": {
            return {
                ...state,
                isPasting: false,
                pasteBuffer: "",
            };
        }

        // Configuration actions
        case "SET_AVAILABLE_WIDTH": {
            return {
                ...state,
                availableWidth: action.width,
            };
        }

        case "SET_PLACEHOLDER": {
            return {
                ...state,
                placeholder: action.placeholder,
            };
        }

        case "SET_PROMPT_PREFIX": {
            return {
                ...state,
                promptPrefix: action.promptPrefix,
            };
        }

        default: {
            // TypeScript should ensure this is never reached
            const _exhaustive: never = action;
            return state;
        }
    }
}
