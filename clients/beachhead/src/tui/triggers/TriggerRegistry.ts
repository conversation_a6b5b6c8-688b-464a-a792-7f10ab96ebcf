/**
 * Registry for managing available workflow triggers
 */

import { BaseTrigger } from "./BaseTrigger";
import { NewPRTrigger } from "./NewPRTrigger";
import { LabelTrigger } from "./LabelTrigger";

export class TriggerRegistry {
    private static triggers: Map<string, BaseTrigger> = new Map();
    private static initialized = false;
    
    /**
     * Initialize the registry with default triggers
     */
    private static initialize(): void {
        if (this.initialized) return;
        
        this.register(new NewPRTrigger());
        this.register(new LabelTrigger());
        
        this.initialized = true;
    }
    
    /**
     * Register a new trigger
     */
    static register(trigger: BaseTrigger): void {
        this.triggers.set(trigger.id, trigger);
    }
    
    /**
     * Get a trigger by ID
     */
    static get(id: string): BaseTrigger | undefined {
        this.initialize();
        return this.triggers.get(id);
    }
    
    /**
     * Get all registered triggers
     */
    static getAll(): BaseTrigger[] {
        this.initialize();
        return Array.from(this.triggers.values());
    }
    
    /**
     * Validate a trigger configuration
     */
    static validateConfig(triggerId: string, config: any): boolean {
        const trigger = this.get(triggerId);
        return trigger ? trigger.isConfigValid(config) : false;
    }
}
