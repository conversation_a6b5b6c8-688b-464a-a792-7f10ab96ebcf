/**
 * Abstract base class for GitHub workflow triggers
 */

import { TriggerOption } from "../types/TriggerTypes";
import { TriggerEventConfig } from "../types/TriggerConfigTypes";
import { BaseAction } from "../actions/BaseAction";

export abstract class BaseTrigger {
    abstract readonly id: string;
    abstract readonly displayName: string;
    abstract readonly description: string;
    
    /**
     * Get configuration options specific to this trigger type
     */
    abstract getConfigurationOptions(): TriggerOption[];
    
    /**
     * Validate trigger-specific configuration
     */
    abstract isConfigValid(config: any): boolean;

    /**
     * Generate structured trigger event configuration
     */
    abstract generateTriggerEventConfig(config: any): TriggerEventConfig[];
    
    /**
     * Generate the job-level if condition
     */
    abstract generateJobCondition(config: any): string;
    

    
    /**
     * Get default configuration values for this trigger
     */
    abstract getDefaultConfig(): any;
    
    /**
     * Get default configuration with action-specific values
     */
    getDefaultConfigForAction(_action: BaseAction | undefined): any {
        return this.getDefaultConfig();
    }
}
