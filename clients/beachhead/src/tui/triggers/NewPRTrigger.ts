/**
 * Trigger for new pull request events
 */

import { BaseTrigger } from "./BaseTrigger";
import { TriggerOption, NewPRConfig } from "../types/TriggerTypes";
import { TriggerEventConfig } from "../types/TriggerConfigTypes";

export class NewPRTrigger extends BaseTrigger {
    readonly id = "new_pr";
    readonly displayName = "On new PR creation";
    readonly description = "Run automatically when PRs are opened or ready for review";
    
    getConfigurationOptions(): TriggerOption[] {
        return [
            {
                type: "boolean",
                key: "includeDrafts",
                label: "Include draft PRs",
                description: "When enabled, the workflow will also run on draft pull requests",
                defaultValue: false
            }
        ];
    }
    
    isConfigValid(config: NewPRConfig): boolean {
        return config.includeDrafts !== undefined;
    }

    generateTriggerEventConfig(_config: NewPRConfig): TriggerEventConfig[] {
        return [{
            event: "pull_request",
            types: ["opened", "ready_for_review"]
        }];
    }
    
    generateJobCondition(config: NewPRConfig): string {
        const forkCheck = "github.event.pull_request.head.repo.full_name == github.event.pull_request.base.repo.full_name";
        
        if (config.includeDrafts) {
            return `if: ${forkCheck}`;
        } else {
            return `if: ${forkCheck} && github.event.pull_request.draft == false`;
        }
    }
    
    getDefaultConfig(): NewPRConfig {
        return { includeDrafts: false };
    }
}
