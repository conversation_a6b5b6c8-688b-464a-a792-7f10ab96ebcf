/**
 * Trigger for label-based pull request events
 */

import { BaseTrigger } from "./BaseTrigger";
import { TriggerOption, LabelConfig } from "../types/TriggerTypes";
import { TriggerEventConfig } from "../types/TriggerConfigTypes";
import { BaseAction } from "../actions/BaseAction";

export class LabelTrigger extends BaseTrigger {
    readonly id = "label";
    readonly displayName = "When a label is added to a PR";
    readonly description = "Run when a specific label is added to a pull request";
    
    getConfigurationOptions(): TriggerOption[] {
        return [
            {
                type: "text",
                key: "labelName",
                label: "Label name",
                description: "The workflow will run when this label is added to a PR",
                required: true,
                placeholder: "Enter label name..."
            }
        ];
    }
    
    isConfigValid(config: LabelConfig): boolean {
        return !!config.labelName?.trim();
    }

    generateTriggerEventConfig(_config: LabelConfig): TriggerEventConfig[] {
        return [{
            event: "pull_request",
            types: ["labeled"]
        }];
    }
    
    generateJobCondition(config: LabelConfig): string {
        const forkCheck = "github.event.pull_request.head.repo.full_name == github.event.pull_request.base.repo.full_name";
        const labelCheck = `github.event.label.name == '${config.labelName}'`;
        return `if: ${forkCheck} && ${labelCheck}`;
    }
    
    getDefaultConfig(): LabelConfig {
        return { labelName: "" };
    }
    
    getDefaultConfigForAction(action: BaseAction | undefined): LabelConfig {
        const defaultLabel = action ? action.getDefaultLabel() : "";

        return { labelName: defaultLabel };
    }
}
