import React, { createContext, useContext } from "react";

interface TranscriptContextValue {
    openCodeBlockRef: { current: string | null };
}

const TranscriptContext = createContext<TranscriptContextValue | null>(null);

interface TranscriptProviderProps {
    children: React.ReactNode;
    openCodeBlockRef: { current: string | null };
}

export const TranscriptProvider: React.FC<TranscriptProviderProps> = ({
    children,
    openCodeBlockRef,
}) => {
    return (
        <TranscriptContext.Provider value={{ openCodeBlockRef }}>
            {children}
        </TranscriptContext.Provider>
    );
};

export const useTranscriptContext = (): TranscriptContextValue => {
    const context = useContext(TranscriptContext);
    if (!context) {
        throw new Error("useTranscriptContext must be used within a TranscriptProvider");
    }
    return context;
};
