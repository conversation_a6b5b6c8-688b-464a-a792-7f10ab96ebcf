import { Box, Text } from "ink";
import React from "react";

export interface PopoverProps {
    show: boolean;
    onDismiss: () => void;
    children: React.ReactNode;
    height?: string | number;
    shortcutsText: string; // Required prop for keyboard shortcuts display
}

export const Popover: React.FC<PopoverProps> = ({ show, children, height, shortcutsText }) => {
    if (!show) {
        return null;
    }

    return (
        <>
            <Box
                width="100%"
                height={height}
                flexDirection="column"
                borderStyle="double"
                borderColor="blue"
                paddingX={2}
                paddingY={1}
            >
                {children}
            </Box>
            <Box paddingX={1}>
                <Text color="gray">{shortcutsText}</Text>
            </Box>
        </>
    );
};
