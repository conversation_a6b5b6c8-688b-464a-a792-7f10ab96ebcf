import { Box, Text } from "ink";
import React from "react";

import { InputDisplay } from "../../components/InputDisplay";
import { InputModeConfig } from "../../types/InputMode";

interface InputPromptProps {
    mode: InputModeConfig;
    wrappedLines: string[];
    cursorLine: number;
    cursorColumn: number;
}

/**
 * InputPrompt - Pure presentational component for the input box UI
 *
 * This component only handles rendering:
 * - Border and styling around the input
 * - Prompt prefix
 * - Delegates text rendering to InputDisplay
 *
 * All logic (keyboard handling, state management) should be handled
 * by the parent component using useInputBuffer hook.
 */
export const InputPrompt: React.FC<InputPromptProps> = ({
    mode,
    wrappedLines,
    cursorLine,
    cursorColumn,
}) => {
    return (
        <Box
            borderStyle="round"
            borderColor={mode.borderColor}
            padding={0}
            paddingLeft={1}
            paddingRight={1}
        >
            <Text color="white" bold>
                {`${mode.promptPrefix} `}
            </Text>
            <InputDisplay
                wrappedLines={wrappedLines}
                cursorLine={cursorLine}
                cursorColumn={cursorColumn}
                placeholder={mode.placeholder}
            />
        </Box>
    );
};
