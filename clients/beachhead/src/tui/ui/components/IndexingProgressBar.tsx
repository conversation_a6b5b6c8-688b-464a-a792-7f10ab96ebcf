import { Box, Text } from "ink";
import Spinner from "ink-spinner";
import React from "react";

export interface IndexingProgress {
    percentage: number;
    phase: "indexing" | "complete" | "idle";
}

interface IndexingProgressBarProps {
    progress: IndexingProgress;
}

export const IndexingProgressBar: React.FC<IndexingProgressBarProps> = ({ progress }) => {
    if (progress.phase === "idle") {
        return null;
    }

    const getPhaseText = (phase: string): string => {
        switch (phase) {
            case "indexing":
                return "Indexing...";
            case "complete":
                return "Indexing complete";
            default:
                return "";
        }
    };

    const getProgressPercentage = (): number => {
        return progress.percentage || 0;
    };

    const renderProgressBar = (): string => {
        const width = 30;
        const percentage = getProgressPercentage();
        const filled = Math.round((percentage / 100) * width);
        const empty = width - filled;

        return "█".repeat(filled) + "░".repeat(empty);
    };

    const phaseText = getPhaseText(progress.phase);
    const percentage = getProgressPercentage();
    const progressBar = renderProgressBar();

    if (progress.phase === "complete") {
        return (
            <Box paddingLeft={1} paddingTop={1}>
                <Text color="green">✓ {phaseText}</Text>
            </Box>
        );
    }

    return (
        <Box paddingLeft={1} paddingTop={1} gap={1} flexDirection="row">
            <Box>
                <Text color="cyan">
                    <Spinner type="dots3" /> {phaseText}
                </Text>
            </Box>
            <Box marginTop={0}>
                <Text color="gray">
                    {progressBar} {percentage}%
                </Text>
            </Box>
        </Box>
    );
};
