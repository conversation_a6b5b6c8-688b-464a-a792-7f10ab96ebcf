import { Box, Text } from "ink";
import React, { useMemo } from "react";
import { WorkflowConfig, WorkflowWizardState, GENERATION_STEPS, SETUP_STEPS } from "../../types/WorkflowTypes";
import { Popover } from "./Popover";
import { ActionSelectionStep } from "./steps/ActionSelectionStep";
import { TriggerSelectionStep } from "./steps/TriggerSelectionStep";
import { OptionsStep } from "./steps/OptionsStep";
import { ConfirmationStep } from "./steps/ConfirmationStep";
import { GeneratingStep } from "./steps/GeneratingStep";
import { SetupOverviewStep } from "./steps/SetupOverviewStep";
import { SetupCredentialsStep } from "./steps/SetupCredentialsStep";
import { SetupGitHubNavStep } from "./steps/SetupGitHubNavStep";
import { SetupGitHubSecretStep } from "./steps/SetupGitHubSecretStep";

import { SetupDeployStep } from "./steps/SetupDeployStep";
import { CompleteStep } from "./steps/CompleteStep";

export interface GitHubWorkflowWizardProps {
    show: boolean;
    wizardState: WorkflowWizardState;
    onNext: () => void;
    onPrevious: () => void;
    onUpdateConfig: (updates: Partial<WorkflowConfig>) => void;
    onGenerate: () => void;
    onDismiss: () => void;
    onClose: () => void;
    shortcutsText: string;
}

export const GitHubWorkflowWizard: React.FC<GitHubWorkflowWizardProps> = ({
    show,
    wizardState,
    onNext,
    onPrevious,
    onUpdateConfig,
    onGenerate,
    onDismiss,
    onClose,
    shortcutsText,
}) => {
    if (!show) {
        return null;
    }

    const currentSteps = wizardState.mode === "generation" ? GENERATION_STEPS : SETUP_STEPS;
    const currentStep = currentSteps[wizardState.currentStep];
    const isFirstStep = wizardState.currentStep === 0;
    const isLastStep = wizardState.currentStep === currentSteps.length - 1;

    // Memoize stepProps to prevent infinite re-renders in child components
    const stepProps = useMemo(() => ({
        config: wizardState.config,
        onUpdateConfig,
        onNext,
        onPrevious,
        onGenerate,
        isFirstStep,
        isLastStep,
        currentStepIndex: wizardState.currentStep,
        totalSteps: currentSteps.length,
        isGenerating: wizardState.isGenerating,
        isComplete: wizardState.isComplete,
    }), [
        wizardState.config,
        wizardState.currentStep,
        wizardState.mode,
        wizardState.isGenerating,
        wizardState.isComplete,
        onUpdateConfig,
        onNext,
        onPrevious,
        onGenerate,
        isFirstStep,
        isLastStep,
        currentSteps.length,
    ]);

    const renderCurrentStep = () => {
        const action = wizardState.config.action;
        const workflowFileName = action ? action.getFileName() : 'workflow.yml';

        if (wizardState.mode === "generation") {
            switch (wizardState.currentStep) {
                case 0:
                    return <ActionSelectionStep {...stepProps} />;
                case 1:
                    return <TriggerSelectionStep {...stepProps} />;
                case 2:
                    return <OptionsStep {...stepProps} />;
                case 3:
                    return <ConfirmationStep {...stepProps} />;
                case 4:
                    return <GeneratingStep {...stepProps} />;
                default:
                    return <Text color="red">Unknown generation step</Text>;
            }
        } else {
            // Setup mode
            switch (wizardState.currentStep) {
                case 0:
                    return <SetupOverviewStep {...stepProps} workflowFileName={workflowFileName} />;
                case 1:
                    return <SetupCredentialsStep {...stepProps} />;
                case 2:
                    return <SetupGitHubNavStep {...stepProps} />;
                case 3:
                    return <SetupGitHubSecretStep {...stepProps} />;
                case 4:
                    return <SetupDeployStep {...stepProps} workflowFileName={workflowFileName} />;
                case 5:
                    return <CompleteStep {...stepProps} onClose={onClose} />;
                default:
                    return <Text color="red">Unknown setup step</Text>;
            }
        }
    };

    return (
        <Popover show={show} onDismiss={onDismiss} height={27} shortcutsText={shortcutsText}>
            <Box flexDirection="column" gap={1}>
                {/* Header */}
                <Box flexDirection="row" justifyContent="space-between">
                    <Text bold color="yellow">
                        {wizardState.mode === "generation" ? "GitHub Workflow Generation" : "GitHub Workflow Setup"}
                    </Text>
                    <Text color="gray">
                        Step {wizardState.currentStep + 1} of {currentSteps.length}
                    </Text>
                </Box>

                {/* Progress indicator */}
                <Box flexDirection="row" gap={1}>
                    {currentSteps.map((step, index) => (
                        <Text
                            key={step.id}
                            color={
                                index === wizardState.currentStep
                                    ? "blue"
                                    : index < wizardState.currentStep
                                    ? "green"
                                    : "gray"
                            }
                        >
                            {index === wizardState.currentStep ? "●" : index < wizardState.currentStep ? "✓" : "○"}
                        </Text>
                    ))}
                </Box>

                {/* Current step title and description */}
                <Box flexDirection="column">
                    <Text bold>{currentStep.title}</Text>
                    <Text dimColor>{currentStep.description}</Text>
                </Box>

                {/* Error message */}
                {wizardState.error && (
                    <Box>
                        <Text color="red">⚠️ {wizardState.error}</Text>
                    </Box>
                )}

                {/* Step content */}
                <Box flexDirection="column" marginTop={1}>
                    {renderCurrentStep()}
                </Box>


            </Box>
        </Popover>
    );
};
