import { Box, Text } from "ink";
import React from "react";

import { MenuItem } from "../../types/MenuProvider";

interface SelectMenuProps {
    show: boolean;
    items: MenuItem[];
    activeIndex: number;
    width?: string;
}

export const SelectMenu: React.FC<SelectMenuProps> = ({
    show,
    items,
    activeIndex,
    width = "100%",
}) => {
    if (!show) {
        return null;
    }

    return (
        <Box flexDirection="column" width={width} marginBottom={1} paddingX={2}>
            {items.map((item, index) => (
                <Text key={index} color={index === activeIndex ? "blue" : "grey"}>
                    {index === activeIndex ? "⏺ " : "  "}
                    {item.label}
                    {item.description ? ` - ${item.description}` : ""}
                </Text>
            ))}
        </Box>
    );
};
