import { vice } from "gradient-string";
import { Box, Text } from "ink";
import React from "react";

export const Banner: React.FC = React.memo(() => {
    const ascii = `
                                            ░██  BETA     
                                                          
 ░██████   ░██    ░██  ░████████  ░████████ ░██ ░███████  
      ░██  ░██    ░██ ░██    ░██ ░██    ░██ ░██░██    ░██ 
 ░███████  ░██    ░██ ░██    ░██ ░██    ░██ ░██░█████████ 
░██   ░██  ░██   ░███ ░██   ░███ ░██   ░███ ░██░██        
 ░█████░██  ░█████░██  ░█████░██  ░█████░██ ░██ ░███████  
                             ░██        ░██               
 by Augment Code       ░███████   ░███████                
`;

    return (
        <Box marginBottom={1}>
            <Text>{vice(ascii)}</Text>
        </Box>
    );
});
