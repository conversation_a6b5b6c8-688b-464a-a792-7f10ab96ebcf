/**
 * Setup GitHub Secret Step - Guide users through adding the API token secret
 */

import { Box, Text, useInput } from "ink";
import React, { useState, useEffect } from "react";
import * as fs from "fs";
import * as path from "path";
import * as os from "os";
import { BaseStepProps } from "../../../types/BaseStepTypes";

export interface SetupGitHubSecretStepProps extends BaseStepProps {}

interface AugmentCredentials {
    sessionContent?: string;
    found: boolean;
}

export const SetupGitHubSecretStep: React.FC<SetupGitHubSecretStepProps> = ({
    onNext,
    onPrevious,
    isFirstStep,
}) => {
    const [credentials, setCredentials] = useState<AugmentCredentials>({ found: false });

    // Auto-detect Augment credentials for display
    useEffect(() => {
        const detectCredentials = async () => {
            try {
                const sessionPath = path.join(os.homedir(), '.augment', 'session.json');

                if (fs.existsSync(sessionPath)) {
                    const sessionContent = fs.readFileSync(sessionPath, 'utf8');
                    setCredentials({
                        sessionContent,
                        found: true
                    });
                }
            } catch (error) {
                // Silently fail - we'll show manual instructions
            }
        };

        detectCredentials();
    }, []);

    // Copy to clipboard functionality
    const copyToClipboard = async (text: string, label: string) => {
        try {
            // Use process.stdout.write to copy to clipboard (works in terminal environments)
            process.stdout.write(`\x1b]52;c;${Buffer.from(text).toString('base64')}\x1b\\`);
            // Note: We don't set feedback here to avoid state conflicts with parent component
        } catch (error) {
            // Silently fail
        }
    };

    useInput((input, key) => {
        if (input === 'c' && credentials.found && credentials.sessionContent) {
            copyToClipboard(credentials.sessionContent, "Session file");
        } else if (key.return) {
            onNext();
        } else if ((key.backspace || key.delete) && !isFirstStep) {
            onPrevious();
        }
    });

    return (
        <Box flexDirection="column" gap={1}>
            <Text color="blue" bold>🔐 Step 3: Add Session Auth Secret</Text>

            <Box flexDirection="column" paddingLeft={2} gap={1}>
                <Text>
                    Now let's add your Augment session file as a repository secret.
                </Text>
                <Box>
                    <Text color="yellow" bold>Follow these steps:</Text>
                </Box>
                <Box flexDirection="column" paddingLeft={2}>
                    <Text>
                        <Text color="cyan" bold>1.</Text> Make sure you're on the "Secrets" tab (should be selected by default)
                    </Text>
                    <Text>
                        <Text color="cyan" bold>2.</Text> Click the <Text color="cyan">"New repository secret"</Text> button
                    </Text>
                    <Text>
                        <Text color="cyan" bold>3.</Text> In the "Name" field, enter exactly: <Text color="cyan" bold>AUGMENT_SESSION_AUTH</Text>
                    </Text>
                    <Text>
                        <Text color="cyan" bold>4.</Text> In the "Secret" field, enter your session file content:
                    </Text>
                    <Box paddingLeft={4}>
                        {credentials.found && credentials.sessionContent ? (
                            <Text color="yellow">💡 Press 'C' to copy the full session content</Text>
                        ) : (
                            <Text color="yellow">Your full session.json file content</Text>
                        )}
                    </Box>
                    <Text>
                        <Text color="cyan" bold>5.</Text> Click <Text color="cyan">"Add secret"</Text> to save it
                    </Text>
                </Box>
            </Box>
        </Box>
    );
};
