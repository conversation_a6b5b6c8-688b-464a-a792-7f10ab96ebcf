/**
 * Component for text input fields
 */

import { Box, Text, useInput } from "ink";
import React, { useState, useEffect, useRef } from "react";
import { BaseStepProps, InputField } from "../../../types/BaseStepTypes";

export interface InputStepProps extends BaseStepProps {
    title: string;
    fields: InputField[];
    values: Record<string, string>;
    onValuesChange: (values: Record<string, string>) => void;
    helpText?: string;
}

// Global state to preserve editing state across component remounts
const globalEditingState = {
    isEditing: false,
    editingField: null as string | null,
    componentId: null as string | null,
    // Add a buffer for the current field value to reduce visual flickering
    currentValue: '' as string,
};

// Export function to reset global editing state (for wizard cleanup)
export const resetGlobalEditingState = () => {
    globalEditingState.isEditing = false;
    globalEditingState.editingField = null;
    globalEditingState.componentId = null;
    globalEditingState.currentValue = '';
};

export const InputStep: React.FC<InputStepProps> = ({
    title,
    fields,
    values,
    onValuesChange,
    onNext,
    onPrevious,
    isFirstStep,
    helpText,
}) => {
    // Create a stable component ID based on the first field key
    const componentId = fields.length > 0 ? fields[0].key : 'unknown';

    // Initialize local state with default values
    const [localValues, setLocalValues] = useState(() => {
        const initialValues = { ...values };
        fields.forEach(field => {
            if (initialValues[field.key] === undefined) {
                initialValues[field.key] = field.defaultValue || "";
            }
        });
        return initialValues;
    });

    // Initialize editing state from global state if this is the same component
    const [isEditingText, setIsEditingText] = useState(() => {
        return globalEditingState.componentId === componentId ? globalEditingState.isEditing : false;
    });
    const [editingField, setEditingField] = useState<string | null>(() => {
        return globalEditingState.componentId === componentId ? globalEditingState.editingField : null;
    });

    // Sync local values with global buffer on mount if we're restoring editing state
    useEffect(() => {
        if (globalEditingState.componentId === componentId &&
            globalEditingState.isEditing &&
            globalEditingState.editingField &&
            globalEditingState.currentValue !== undefined) {
            setLocalValues(prev => ({
                ...prev,
                [globalEditingState.editingField!]: globalEditingState.currentValue
            }));
        }
    }, [componentId]);

    // Cleanup global state when component unmounts for a different component
    useEffect(() => {
        return () => {
            // Only clear global state if this is a different component
            if (globalEditingState.componentId !== componentId) {
                globalEditingState.isEditing = false;
                globalEditingState.editingField = null;
                globalEditingState.componentId = null;
            }
        };
    }, [componentId]);

    // Micro-batching for parent updates to reduce remount frequency during fast typing
    const updateTimeoutRef = useRef<NodeJS.Timeout | undefined>(undefined);

    useEffect(() => {
        // Clear any pending update
        if (updateTimeoutRef.current) {
            clearTimeout(updateTimeoutRef.current);
        }

        // Very short micro-batch during editing to reduce remount frequency
        if (isEditingText) {
            updateTimeoutRef.current = setTimeout(() => {
                onValuesChange(localValues);
            }, 10); // Very short 10ms micro-batch
        } else {
            // Immediate update when not editing
            onValuesChange(localValues);
        }

        // Cleanup
        return () => {
            if (updateTimeoutRef.current) {
                clearTimeout(updateTimeoutRef.current);
            }
        };
    }, [localValues, onValuesChange, isEditingText]);

    const updateValue = (key: string, value: string) => {
        setLocalValues(prev => ({
            ...prev,
            [key]: value
        }));
        // Update global buffer to reduce visual flickering during remounts
        if (globalEditingState.editingField === key) {
            globalEditingState.currentValue = value;
        }
    };

    const startEditing = (fieldKey: string) => {
        setIsEditingText(true);
        setEditingField(fieldKey);
        // Sync with global state
        globalEditingState.isEditing = true;
        globalEditingState.editingField = fieldKey;
        globalEditingState.componentId = componentId;
        globalEditingState.currentValue = localValues[fieldKey] || '';
    };

    const stopEditing = () => {
        setIsEditingText(false);
        setEditingField(null);
        // Sync with global state
        globalEditingState.isEditing = false;
        globalEditingState.editingField = null;
        globalEditingState.componentId = null;
        globalEditingState.currentValue = '';
    };

    // Use refs to avoid stale closure issues in useInput
    const isEditingRef = useRef(isEditingText);
    const editingFieldRef = useRef(editingField);

    useEffect(() => {
        isEditingRef.current = isEditingText;
        editingFieldRef.current = editingField;
    }, [isEditingText, editingField]);

    useInput((input, key) => {
        const currentIsEditing = isEditingRef.current;
        const currentEditingField = editingFieldRef.current;

        if (currentIsEditing && currentEditingField) {
            if (key.return || (key.escape && !key.meta && !key.delete && !key.backspace)) {
                // Only exit on explicit escape key press, not escape sequences from delete/backspace keys
                stopEditing();
            } else if (key.backspace || key.delete) {
                updateValue(currentEditingField, (localValues[currentEditingField] || "").slice(0, -1));
            } else if (input && input.length === 1) {
                updateValue(currentEditingField, (localValues[currentEditingField] || "") + input);
            }
        } else {
            if (key.return) {
                // Check if we need to start editing a required field
                const requiredField = fields.find(field => field.required && !localValues[field.key]?.trim());
                if (requiredField) {
                    startEditing(requiredField.key);
                } else {
                    onNext();
                }
            } else if ((key.backspace || key.delete) && !isFirstStep) {
                onPrevious();
            } else if (input === "e" || input === "E") {
                // Start editing the first field (for single field cases)
                if (fields.length === 1) {
                    startEditing(fields[0].key);
                }
            }
        }
    });

    return (
        <Box flexDirection="column" gap={1}>
            <Text>{title}</Text>
            
            <Box flexDirection="column" paddingLeft={2}>
                {fields.map(field => (
                    <Box key={field.key} flexDirection="column" marginBottom={1}>
                        <Box flexDirection="row" gap={1}>
                            <Text>{field.label}:</Text>
                            <Text color={isEditingText && editingField === field.key ? "yellow" : "blue"}>
                                {(() => {
                                    // Always use buffered value during editing for maximum stability
                                    if (isEditingText && editingField === field.key) {
                                        // Check global state first for the most current value
                                        if (globalEditingState.componentId === componentId &&
                                            globalEditingState.editingField === field.key &&
                                            globalEditingState.currentValue !== undefined) {
                                            return globalEditingState.currentValue || field.placeholder || "(empty)";
                                        }
                                    }
                                    return localValues[field.key] || field.placeholder || "(empty)";
                                })()}
                                {isEditingText && editingField === field.key ? "_" : ""}
                            </Text>
                        </Box>
                        <Text color="white" dimColor>
                            {field.description}
                        </Text>
                    </Box>
                ))}
            </Box>

            {helpText && (
                <Box paddingTop={1}>
                    <Text color="gray">{helpText}</Text>
                </Box>
            )}
        </Box>
    );
};
