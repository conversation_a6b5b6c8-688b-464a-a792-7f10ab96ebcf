import React, { useMemo, useCallback } from "react";
import { BaseStepProps } from "../../../types/BaseStepTypes";
import { SelectStep } from "./SelectStep";
import { ActionRegistry } from "../../../actions/ActionRegistry";

export const ActionSelectionStep: React.FC<BaseStepProps> = (props) => {
    const actions = ActionRegistry.getAll();

    // Memoize the options array to prevent unnecessary re-renders
    const options = useMemo(() =>
        actions.map(action => ({
            value: action.id,
            label: action.displayName,
            description: action.description,
        })),
        [actions]
    );

    // Memoize the callback to prevent unnecessary re-renders
    const onSelectionChange = useCallback((actionId: string) => {
        const action = ActionRegistry.get(actionId);
        props.onUpdateConfig({ action });
    }, [props.onUpdateConfig]);

    return (
        <SelectStep
            {...props}
            title="Choose the action you want to automate:"
            options={options}
            selectedValue={props.config.action?.id}
            onSelectionChange={onSelectionChange}
        />
    );
};
