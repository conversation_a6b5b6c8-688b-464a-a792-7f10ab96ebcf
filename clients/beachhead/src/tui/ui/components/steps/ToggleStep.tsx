/**
 * Component for boolean toggle fields
 */

import { Box, Text, useInput } from "ink";
import React, { useState, useEffect } from "react";
import { BaseStepProps, ToggleField } from "../../../types/BaseStepTypes";

export interface ToggleStepProps extends BaseStepProps {
    title: string;
    fields: ToggleField[];
    values: Record<string, boolean>;
    onValuesChange: (values: Record<string, boolean>) => void;
    helpText?: string;
}

export const ToggleStep: React.FC<ToggleStepProps> = ({
    title,
    fields,
    values,
    onValuesChange,
    onNext,
    onPrevious,
    isFirstStep,
    helpText,
}) => {
    // Initialize local state with default values
    const [localValues, setLocalValues] = useState(() => {
        const initialValues = { ...values };
        fields.forEach(field => {
            if (initialValues[field.key] === undefined) {
                initialValues[field.key] = field.defaultValue || false;
            }
        });
        return initialValues;
    });

    // Update parent when local values change
    useEffect(() => {
        onValuesChange(localValues);
    }, [localValues]);

    const toggleValue = (key: string) => {
        setLocalValues(prev => ({
            ...prev,
            [key]: !prev[key]
        }));
    };

    useInput((input, key) => {
        if (input === " ") {
            // Toggle the first boolean field (for single field cases)
            if (fields.length === 1) {
                toggleValue(fields[0].key);
            }
        } else if (key.return) {
            onNext();
        } else if ((key.backspace || key.delete) && !isFirstStep) {
            onPrevious();
        }
    });

    return (
        <Box flexDirection="column" gap={1}>
            <Text>{title}</Text>
            
            <Box flexDirection="column" paddingLeft={2}>
                {fields.map(field => (
                    <Box key={field.key} flexDirection="column" marginBottom={1}>
                        <Box flexDirection="row" gap={1}>
                            <Box width={2}>
                                <Text color="green">
                                    {localValues[field.key] ? "☑" : "☐"}
                                </Text>
                            </Box>
                            <Text>{field.label}</Text>
                        </Box>
                        <Box flexDirection="row" gap={1}>
                            <Box width={2} />
                            <Text color="white" dimColor>
                                {field.description}
                            </Text>
                        </Box>
                    </Box>
                ))}
                <Box paddingTop={1}>
                    <Text color="yellow">💡 Tip: Press 'space' to toggle the option</Text>
                </Box>
            </Box>

            {helpText && (
                <Box paddingTop={1}>
                    <Text color="gray">{helpText}</Text>
                </Box>
            )}
        </Box>
    );
};
