/**
 * Setup GitHub Navigation Step - Guide users to the right GitHub settings page
 */

import { Box, Text, useInput } from "ink";
import React from "react";
import { BaseStepProps } from "../../../types/BaseStepTypes";

export interface SetupGitHubNavStepProps extends BaseStepProps {}

export const SetupGitHubNavStep: React.FC<SetupGitHubNavStepProps> = ({
    onNext,
    onPrevious,
    isFirstStep,
}) => {
    useInput((input, key) => {
        if (key.return) {
            onNext();
        } else if ((key.backspace || key.delete) && !isFirstStep) {
            onPrevious();
        }
    });

    return (
        <Box flexDirection="column">
            <Text color="blue" bold>🧭 Step 2: Navigate to GitHub Settings</Text>
            
            <Box flexDirection="column" paddingLeft={2} paddingTop={1} gap={1}>
                <Text>
                    First, let's navigate to the right place in your GitHub repository to add secrets and variables.
                </Text>
                
                <Box flexDirection="column" paddingLeft={2}>
                    <Text>
                        <Text color="cyan" bold>1.</Text> Open your repository on GitHub in a web browser
                    </Text>
                    <Text>
                        <Text color="cyan" bold>2.</Text> Click on the <Text color="cyan">Settings</Text> tab (near the top of the page)
                    </Text>
                    <Text>
                        <Text color="cyan" bold>3.</Text> In the left sidebar, look for <Text color="cyan">Secrets and variables</Text>
                    </Text>
                    <Text>
                        <Text color="cyan" bold>4.</Text> Click on <Text color="cyan">Actions</Text> under "Secrets and variables"
                    </Text>
                </Box>
                <Box paddingTop={1}>
                    <Text color="yellow">💡 Note:</Text>
                </Box>
                <Box flexDirection="column" paddingLeft={2}>
                    <Text>If you don't see the Settings tab, you might not have admin access to the repository.</Text>
                    <Text>Contact the repository owner to add the secrets and variables for you.</Text>
                </Box>
            </Box>
        </Box>
    );
};
