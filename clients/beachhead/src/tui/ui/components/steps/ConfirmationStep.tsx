import { Box, Text, useInput } from "ink";
import React, { useMemo } from "react";
import { BaseStepProps, DisplayItem } from "../../../types/BaseStepTypes";

export const ConfirmationStep: React.FC<BaseStepProps> = ({
    config,
    onGenerate,
    onPrevious,
    isFirstStep,
}) => {
    const action = config.action!;
    const trigger = config.trigger!;

    // Memoize display items to prevent unnecessary re-renders
    const items = useMemo((): DisplayItem[] => {
        const displayItems: DisplayItem[] = [
            { label: "Action", value: action.displayName },
            { label: "Trigger", value: trigger.displayName },
        ];

        // Add trigger-specific configuration
        if (config.triggerConfig && trigger) {
            trigger.getConfigurationOptions().forEach(option => {
                const value = config.triggerConfig[option.key];
                if (value !== undefined) {
                    displayItems.push({
                        label: option.label,
                        value: option.type === "boolean" ? (value ? "Yes" : "No") : value
                    });
                }
            });
        }

        // Add filename
        displayItems.push({ label: "File", value: `.github/workflows/${action.getFileName()}` });

        return displayItems;
    }, [action, trigger, config.triggerConfig]);

    useInput((input, key) => {
        if (key.return && onGenerate) {
            onGenerate();
        } else if ((key.backspace || key.delete) && !isFirstStep) {
            onPrevious();
        }
    });

    return (
        <Box flexDirection="column" gap={1}>
            <Text>Review your workflow configuration:</Text>

            <Box flexDirection="column" paddingLeft={2} gap={1}>
                {items.map((item, index) => (
                    <Box key={index} flexDirection="row">
                        <Text color="cyan">{item.label}:</Text>
                        <Text> {item.value}</Text>
                    </Box>
                ))}
            </Box>
        </Box>
    );
};
