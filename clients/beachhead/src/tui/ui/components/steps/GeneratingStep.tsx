/**
 * Component for showing workflow generation progress
 */

import { Box, Text } from "ink";
import React, { useEffect } from "react";
import { BaseStepProps } from "../../../types/BaseStepTypes";

export const GeneratingStep: React.FC<BaseStepProps> = ({
    onNext,
    isGenerating,
    isComplete,
}) => {
    // Auto-advance to next step when generation completes
    useEffect(() => {
        if (!isGenerating && isComplete) {
            // Brief delay to show the completion state
            const timer = setTimeout(() => {
                onNext();
            }, 200);

            return () => clearTimeout(timer);
        }
    }, [isGenerating, isComplete, onNext]);

    return (
        <Box flexDirection="column" gap={1}>
            <Text color="yellow">⏳ Generating workflow file...</Text>
            <Text color="gray">Please wait while we create your GitHub workflow...</Text>
        </Box>
    );
};
