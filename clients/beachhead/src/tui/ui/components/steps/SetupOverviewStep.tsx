/**
 * Setup Overview Step - Explains what needs to be configured
 */

import { Box, Text, useInput } from "ink";
import React from "react";
import { BaseStepProps } from "../../../types/BaseStepTypes";

export interface SetupOverviewStepProps extends BaseStepProps {
    workflowFileName: string;
}

export const SetupOverviewStep: React.FC<SetupOverviewStepProps> = ({
    onNext,
    onPrevious,
    isFirstStep,
    workflowFileName,
}) => {
    useInput((input, key) => {
        if (key.return) {
            onNext();
        } else if ((key.backspace || key.delete) && !isFirstStep) {
            onPrevious();
        }
    });

    return (
        <Box flexDirection="column" gap={1}>
            <Text color="blue" bold>📋 Setup Overview</Text>
            
            <Box flexDirection="column" paddingLeft={2} gap={1}>
                <Text>
                    Your workflow <Text color="cyan">{workflowFileName}</Text> has been generated successfully!
                </Text>
                
                <Text>
                    However, it needs a GitHub secret to work with the Augment API.
                </Text>

                <Box paddingTop={1}>
                    <Text color="yellow" bold>What you'll need to configure:</Text>
                </Box>

                <Box flexDirection="column" paddingLeft={2}>
                    <Text>🔐 <Text color="cyan">AUGMENT_SESSION_AUTH</Text> - Your Augment session file content (secret)</Text>
                </Box>

                <Box paddingTop={1}>
                    <Text>
                        The next steps will guide you through finding your session file and
                        configuring it in your GitHub repository.
                    </Text>
                </Box>
            </Box>
        </Box>
    );
};
