/**
 * Component for selecting from a list of options
 */

import { Box, Text, useInput } from "ink";
import React, { useState, useEffect } from "react";
import { BaseStepProps, SelectStepOption } from "../../../types/BaseStepTypes";

export interface SelectStepProps extends BaseStepProps {
    title: string;
    options: SelectStepOption[];
    selectedValue?: any;
    onSelectionChange: (value: any) => void;
}

export const SelectStep: React.FC<SelectStepProps> = ({
    title,
    options,
    selectedValue,
    onSelectionChange,
    onNext,
    onPrevious,
    isFirstStep,
}) => {
    const [selectedIndex, setSelectedIndex] = useState(() => {
        if (selectedValue !== undefined) {
            const index = options.findIndex(option => option.value === selectedValue);
            return index >= 0 ? index : 0;
        }
        return 0;
    });

    // Update config when selection changes
    useEffect(() => {
        const selectedOption = options[selectedIndex];
        if (selectedOption && selectedOption.value !== selectedValue) {
            onSelectionChange(selectedOption.value);
        }
    }, [selectedIndex, selectedValue, onSelectionChange, options]);

    useInput((input, key) => {
        if (key.upArrow) {
            setSelectedIndex(prev => Math.max(0, prev - 1));
        } else if (key.downArrow) {
            setSelectedIndex(prev => Math.min(options.length - 1, prev + 1));
        } else if (key.return) {
            onNext();
        } else if ((key.backspace || key.delete) && !isFirstStep) {
            onPrevious();
        }
    });

    return (
        <Box flexDirection="column" gap={1}>
            <Text>{title}</Text>
            
            <Box flexDirection="column" paddingLeft={2}>
                {options.map((option, index) => (
                    <Box key={option.value} flexDirection="column" marginBottom={1}>
                        <Text color={index === selectedIndex ? "blue" : "white"}>
                            {index === selectedIndex ? "⏺ " : "  "}
                            {option.label}
                        </Text>
                        <Text color={index === selectedIndex ? "blue" : "white"} dimColor>
                            {"  "}
                            {option.description}
                        </Text>
                    </Box>
                ))}
            </Box>
        </Box>
    );
};
