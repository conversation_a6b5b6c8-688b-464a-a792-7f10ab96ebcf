import React, { useMemo, useCallback } from "react";
import { BaseStepProps } from "../../../types/BaseStepTypes";
import { SelectStep } from "./SelectStep";
import { TriggerRegistry } from "../../../triggers/TriggerRegistry";

export const TriggerSelectionStep: React.FC<BaseStepProps> = (props) => {
    const triggers = TriggerRegistry.getAll();

    // Memoize the title to prevent unnecessary re-renders
    const title = useMemo(() =>
        `Choose when the ${props.config.action?.id === "describe" ? "description" : "review"} should run:`,
        [props.config.action?.id]
    );

    // Memoize the options array to prevent unnecessary re-renders
    const options = useMemo(() =>
        triggers.map(trigger => ({
            value: trigger.id,
            label: trigger.displayName,
            description: trigger.description,
        })),
        [triggers]
    );

    // Memoize the callback to prevent unnecessary re-renders
    const onSelectionChange = useCallback((triggerId: string) => {
        const trigger = TriggerRegistry.get(triggerId);
        // Clear triggerConfig when trigger changes to ensure fresh default values
        props.onUpdateConfig({ trigger, triggerConfig: undefined });
    }, [props.onUpdateConfig]);

    return (
        <SelectStep
            {...props}
            title={title}
            options={options}
            selectedValue={props.config.trigger?.id}
            onSelectionChange={onSelectionChange}
        />
    );
};
