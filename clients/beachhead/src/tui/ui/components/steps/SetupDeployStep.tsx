/**
 * Setup Deploy Step - Guide users through deploying their workflow
 */

import { Box, Text, useInput } from "ink";
import React from "react";
import { BaseStepProps } from "../../../types/BaseStepTypes";

export interface SetupDeployStepProps extends BaseStepProps {
    workflowFileName: string;
}

export const SetupDeployStep: React.FC<SetupDeployStepProps> = ({
    onNext,
    onPrevious,
    isFirstStep,
    workflowFileName,
}) => {
    useInput((input, key) => {
        if (key.return) {
            onNext();
        } else if ((key.backspace || key.delete) && !isFirstStep) {
            onPrevious();
        }
    });

    return (
        <Box flexDirection="column">
            <Text color="blue" bold>🚀 Step 4: Deploy Your Workflow</Text>

            <Box flexDirection="column" gap={1} paddingTop={1} paddingLeft={2}>
                <Text>Once your GitHub secret is configured, deploy your workflow:</Text>
                <Box>
                    <Text color="yellow" bold>1. Review the generated workflow file: </Text><Text color="cyan">.github/workflows/{workflowFileName}</Text>
                </Box>
                <Box>
                    <Text color="yellow" bold>2. Commit the workflow to your repository:</Text>
                </Box>
                <Box>
                    <Text color="yellow" bold>3. Push the changes to GitHub</Text>
                </Box>
                <Box>
                    <Text color="green" bold>4. 🎉 Your workflow is ready!</Text>
                </Box>
                <Box flexDirection="column" paddingLeft={2}>
                    <Text>The workflow will run automatically based on your trigger settings.</Text>
                    <Text>Check the <Text color="cyan">Actions</Text> tab in your GitHub repository to monitor execution.</Text>
                </Box>
            </Box>
        </Box>
    );
};
