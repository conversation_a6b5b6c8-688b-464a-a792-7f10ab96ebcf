/**
 * Component for showing workflow generation completion and next steps
 */

import { Box, Text, useInput } from "ink";
import React, { useMemo } from "react";
import { BaseStepProps } from "../../../types/BaseStepTypes";

export interface CompleteStepProps extends BaseStepProps {
    onClose?: () => void;
}

export const CompleteStep: React.FC<CompleteStepProps> = ({
    config,
    onPrevious,
    onClose,
    isFirstStep,
}) => {
    const action = config.action!;
    const workflowFileName = `.github/workflows/${action.getFileName()}`;

    // Memoize next steps to prevent unnecessary re-renders
    const nextSteps = useMemo(() => [
        "✅ GitHub session auth secret configured",
        "✅ Workflow file generated successfully",
        "",
        "🎉 Your Augment workflow is ready to use!",
        "",
        "The workflow will automatically run based on your trigger settings when merged.",
        "Check the Actions tab in your GitHub repository to monitor execution."
    ], []);

    useInput((input, key) => {
        if (key.escape && onClose) {
            onClose();
        } else if ((key.backspace || key.delete) && !isFirstStep) {
            onPrevious();
        }
    });

    return (
        <Box flexDirection="column" gap={1}>
            <Text color="green" bold>🎉 Setup Complete!</Text>
            <Box flexDirection="column" paddingLeft={2} gap={1}>
                <Text>
                    <Text color="cyan">File created:</Text>
                    <Text> {workflowFileName}</Text>
                </Text>
                <Text>
                    <Text color="cyan">Action:</Text>
                    <Text> {action.displayName}</Text>
                </Text>
                <Text>
                    <Text color="cyan">Trigger:</Text>
                    <Text> {config.trigger?.displayName}</Text>
                </Text>
            </Box>
            <Box flexDirection="column" gap={1}>
                <Text color="blue">Status:</Text>
                <Box flexDirection="column" paddingLeft={2}>
                    {nextSteps.map((step, index) => (
                        <Text key={index} color={step.startsWith('✅') ? 'green' :
                                                step.startsWith('🎉') ? 'blue' : 'white'}>
                            {step}
                        </Text>
                    ))}
                </Box>
            </Box>
        </Box>
    );
};
