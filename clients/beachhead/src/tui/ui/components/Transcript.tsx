import { Box, Static } from "ink";
import React from "react";

import { TranscriptFormatter } from "../../components/TranscriptFormatter";
import { TranscriptProvider } from "../../contexts/TranscriptContext";
import { TranscriptEntry } from "../../hooks/useTranscript";
import { Welcome } from "./Welcome";

interface TranscriptProps {
    transcript: TranscriptEntry[];
    instruction?: string;
    openCodeBlockRef: { current: string | null };
}

export const Transcript: React.FC<TranscriptProps> = ({
    transcript,
    instruction,
    openCodeBlockRef,
}) => {
    // Create a combined static items array with welcome as the first item (only if no initial instruction)
    const staticItems = React.useMemo(() => {
        const items = [];

        // Only show welcome screen if no initial instruction was provided
        if (!instruction || !instruction.trim()) {
            items.push({ key: "welcome", type: "welcome" as const });
        }

        items.push(...transcript);
        return items;
    }, [transcript, instruction]);

    return (
        <TranscriptProvider openCodeBlockRef={openCodeBlockRef}>
            <Box flexDirection="column" paddingLeft={1} paddingRight={12} marginBottom={1}>
                <Static items={staticItems}>
                    {(item) => {
                        if (item.type === "welcome") {
                            return <Welcome key="welcome" />;
                        }
                        return <TranscriptFormatter key={item.key} entry={item} />;
                    }}
                </Static>
            </Box>
        </TranscriptProvider>
    );
};
