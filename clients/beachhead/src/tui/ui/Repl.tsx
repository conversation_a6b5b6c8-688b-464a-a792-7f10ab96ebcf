import { ToolsModel } from "@augment-internal/sidecar-libs/src/tools/tools-model";
import { Box } from "ink";
import React, { useEffect, useState } from "react";

import { AgentLoop } from "../../agent_loop/agent_loop";
import { SessionManager } from "../../session-manager";
import { useAgent } from "../hooks/useAgent";
import { useBracketedPaste } from "../hooks/useBracketedPaste";
import { useExit } from "../hooks/useExit";
import { useGitHubWorkflowWizard } from "../hooks/useGitHubWorkflowWizard";
import { useHelp } from "../hooks/useHelp";
import { useIndexingProgress } from "../hooks/useIndexingProgress";
import { useInputBuffer } from "../hooks/useInputBuffer";
import { useInputHistory } from "../hooks/useInputHistory";
import { useInputMode } from "../hooks/useInputMode";
import { useKeypressRouter } from "../hooks/useKeypressRouter";
import { useMcpStatus } from "../hooks/useMcpStatus";
import { useNewSession } from "../hooks/useNewSession";
import { useRequestId } from "../hooks/useRequestId";
import { useSelectMenu } from "../hooks/useSelectMenu";
import { useShortcuts } from "../hooks/useShortcuts";
import { useSlashCommand } from "../hooks/useSlashCommand";
import { useSubmitQuery } from "../hooks/useSubmitQuery";
import { useTranscript } from "../hooks/useTranscript";
import { useUpdateNotifier } from "../hooks/useUpdateNotifier";
import { getInputModeConfig } from "../types/InputMode";
import { NotificationState } from "../types/NotificationTypes";
import { ActivityIndicator } from "./components/ActivityIndicator";
import { Footer } from "./components/Footer";
import { IndexingProgressBar } from "./components/IndexingProgressBar";
import { InputPrompt } from "./components/InputPrompt";
import { Notification } from "./components/Notification";
import { SelectMenu } from "./components/SelectMenu";
import { Transcript } from "./components/Transcript";

interface ReplProps {
    workspaceRoot: string;
    instruction?: string;
    sessionManager: SessionManager;
    agentLoop: AgentLoop;
    toolsModel: ToolsModel;
    onLogout: () => Promise<void>;
}

// Main app after agent initialization
export const Repl: React.FC<ReplProps> = ({
    workspaceRoot,
    instruction,
    agentLoop,
    onLogout,
    sessionManager,
    toolsModel,
}) => {
    const {
        processingState,
        submitQuery,
        interruptAgent,
        transcript,
        addSystemEntry,
        requestStartTime,
        hideInput,
    } = useAgent(agentLoop, sessionManager);

    const handleExit = useExit({
        sessionManager,
        agentState: agentLoop.agentState,
    });

    const { openCodeBlockRef } = useTranscript();

    // Indexing progress tracking
    const { progress: indexingProgress } = useIndexingProgress({
        workspaceManager: agentLoop.getWorkspaceManager(),
    });
    const [notification, setNotification] = useState<NotificationState>(null);

    const { mode: inputMode, setMode } = useInputMode();
    const modeConfig = getInputModeConfig(inputMode);
    const getCurrentMode = React.useCallback(() => inputMode, [inputMode]);

    const { showHelp, openHelp, helpProcessor, HelpPopover } = useHelp();

    const {
        showWizard,
        openWizard,
        processor: gitHubWizardProcessor,
        GitHubWorkflowWizard,
    } = useGitHubWorkflowWizard();

    const handleNewSession = useNewSession({
        sessionManager,
        agentLoop,
    });

    const handleRequestId = useRequestId({
        agentState: agentLoop.agentState,
    });

    const handleMcpStatus = useMcpStatus({
        toolsModel,
    });

    const { processCommand, slashCommands, slashCommandProcessor } = useSlashCommand({
        onExit: handleExit,
        onHelp: openHelp,
        onLogout,
        onNew: handleNewSession,
        onRequestId: handleRequestId,
        onMcpStatus: handleMcpStatus,
        onGitHubWorkflow: openWizard,
    });

    // Use input buffer hook for state management
    const {
        wrappedLines,
        cursorLine,
        cursorColumn,
        bufferState,
        dispatch,
        value: inputValue,
    } = useInputBuffer({
        placeholder: modeConfig.placeholder,
        promptPrefix: modeConfig.promptPrefix,
    });

    // Use the select menu hook with integrated navigation
    const { items, activeMenuIndex, showMenu, menuNavigationProcessor } = useSelectMenu({
        query: inputValue,
        slashCommands,
        getCurrentMode,
        triggeredBy: inputMode,
    });

    // Set up global shortcuts processor
    const shortcutsProcessor = useShortcuts({
        onExit: handleExit,
        onInterruptAgent: interruptAgent,
    });

    // Set up history extraction and navigation
    const { historyManager, historyProcessor } = useInputHistory(agentLoop.agentState);

    // Set up submit query processor
    const submitQueryProcessor = useSubmitQuery({
        onSubmitQuery: submitQuery,
        historyManager,
    });

    // Set up bracketed paste processor
    const { bracketedPasteProcessor, pasteBufferRef, isPastingRef } = useBracketedPaste();

    // Global keyboard router - routes keypress events through processors
    // Priority order: Bracketed Paste (30) > GitHub Workflow Wizard (30) > Help (25, when context.popover) > Menu Navigation (24, when context.menu) > SlashCommand (22) > Shortcuts (20) > History Nav (15) > Default Input (10) > Submit Query (5)
    useKeypressRouter({
        processors: [
            bracketedPasteProcessor,
            gitHubWizardProcessor,
            helpProcessor,
            menuNavigationProcessor,
            shortcutsProcessor,
            slashCommandProcessor,
            historyProcessor,
            submitQueryProcessor,
        ],
        context: {
            bufferState,
            dispatch,
            processingState,
            mode: inputMode,
            menu: showMenu,
            popover: showHelp || showWizard,
            pasteBufferRef,
            isPastingRef,
        },
        setNotification,
        setMode,
        processCommand,
        addSystemEntry,
    });

    // Check for updates on startup
    useUpdateNotifier({ setNotification });

    // Auto-submit initial instruction if provided
    useEffect(() => {
        if (instruction && instruction.trim()) {
            const timer = setTimeout(() => {
                submitQuery(instruction);
            }, 100);

            return () => clearTimeout(timer);
        }
    }, [instruction, submitQuery]);

    return (
        <Box flexDirection="column" paddingRight={1}>
            <Transcript
                transcript={transcript}
                instruction={instruction}
                openCodeBlockRef={openCodeBlockRef}
            />
            <ActivityIndicator state={processingState} startTime={requestStartTime} />
            <IndexingProgressBar progress={indexingProgress} />
            <Notification notification={notification} />
            {showHelp && <HelpPopover shortcutsText="Press Esc to dismiss" />}
            {showWizard && <GitHubWorkflowWizard />}
            {!showHelp && !showWizard && !hideInput && (
                <Box flexDirection="column" display={showHelp || hideInput ? "none" : "flex"}>
                    <InputPrompt
                        key={inputMode}
                        mode={modeConfig}
                        wrappedLines={wrappedLines}
                        cursorLine={cursorLine}
                        cursorColumn={cursorColumn}
                    />
                    <SelectMenu show={showMenu} items={items} activeIndex={activeMenuIndex} />
                    <Footer helpText={modeConfig.helpText} workspaceRoot={workspaceRoot} />
                </Box>
            )}
        </Box>
    );
};
