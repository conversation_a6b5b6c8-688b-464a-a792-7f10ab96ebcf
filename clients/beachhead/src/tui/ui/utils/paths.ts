import * as os from "os";
import * as path from "path";

/**
 * Contracts a path by replacing the home directory with ~
 * @param filePath The path to contract
 * @returns The contracted path
 */
export function tildeContraction(filePath: string): string {
    const homeDir = os.homedir();
    if (filePath.startsWith(homeDir)) {
        return filePath.replace(homeDir, "~");
    }
    return filePath;
}

/**
 * Expands a path by replacing ~ with the home directory
 * @param filePath The path to expand
 * @returns The expanded path
 */
export function tildeExpansion(filePath: string): string {
    if (filePath.startsWith("~")) {
        return path.join(os.homedir(), filePath.slice(1));
    }
    return filePath;
}
