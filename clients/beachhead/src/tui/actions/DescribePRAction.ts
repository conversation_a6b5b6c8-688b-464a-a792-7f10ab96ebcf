/**
 * Action for automatically generating PR descriptions
 */

import { BaseAction } from "./BaseAction";

export class DescribePRAction extends BaseAction {
    readonly id = "describe";
    readonly displayName = "Describe PRs";
    readonly description = "Automatically generate descriptions for pull requests";
    
    getWorkflowName(): string {
        return "Pull Request Descriptor";
    }
    
    getActionRepository(): string {
        return "augmentcode/describe-pr@v0";
    }
    
    getJobName(): string {
        return "describe-pr";
    }
    
    getStepName(): string {
        return "Generate PR Description";
    }
    
    getDefaultLabel(): string {
        return "augment_describe";
    }
}
