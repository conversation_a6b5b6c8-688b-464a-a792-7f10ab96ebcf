/**
 * Action for automatically generating PR reviews
 */

import { BaseAction } from "./BaseAction";

export class ReviewPRAction extends BaseAction {
    readonly id = "review";
    readonly displayName = "Review PRs";
    readonly description = "Automatically generate reviews for pull requests";
    
    getWorkflowName(): string {
        return "Pull Request Review";
    }
    
    getActionRepository(): string {
        return "augmentcode/review-pr@v0";
    }
    
    getJobName(): string {
        return "review-pr";
    }
    
    getStepName(): string {
        return "Generate PR Review";
    }
    
    getDefaultLabel(): string {
        return "augment_review";
    }
}
