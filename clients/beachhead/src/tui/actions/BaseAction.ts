/**
 * Abstract base class for GitHub workflow actions
 */

export abstract class BaseAction {
    abstract readonly id: string;
    abstract readonly displayName: string;
    abstract readonly description: string;
    
    /**
     * Get the workflow name for this action
     */
    abstract getWorkflowName(): string;
    
    /**
     * Get the GitHub action repository and version
     */
    abstract getActionRepository(): string;
    
    /**
     * Get the job name for this action
     */
    abstract getJobName(): string;
    
    /**
     * Get the step name for this action
     */
    abstract getStepName(): string;
    
    /**
     * Get the default label name for this action
     */
    abstract getDefaultLabel(): string;

    /**
     * Get the filename for the workflow
     */
    getFileName(): string {
        return `augment-${this.getJobName()}.yml`;
    }
}
