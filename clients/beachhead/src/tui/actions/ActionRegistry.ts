/**
 * Registry for managing available workflow actions
 */

import { BaseAction } from "./BaseAction";
import { DescribePRAction } from "./DescribePRAction";
import { ReviewPRAction } from "./ReviewPRAction";

export class ActionRegistry {
    private static actions: Map<string, BaseAction> = new Map();
    private static initialized = false;
    
    /**
     * Initialize the registry with default actions
     */
    private static initialize(): void {
        if (this.initialized) return;
        
        this.register(new DescribePRAction());
        this.register(new ReviewPRAction());
        
        this.initialized = true;
    }
    
    /**
     * Register a new action
     */
    static register(action: BaseAction): void {
        this.actions.set(action.id, action);
    }
    
    /**
     * Get an action by ID
     */
    static get(id: string): BaseAction | undefined {
        this.initialize();
        return this.actions.get(id);
    }
    
    /**
     * Get all registered actions
     */
    static getAll(): BaseAction[] {
        this.initialize();
        return Array.from(this.actions.values());
    }
}
