import {
    findLineEnd,
    findLineStart,
    getCursorFromLineColumn,
    getLineAndColumn,
    getLineCount,
    isAtLineEnd,
    isAtLineStart,
    moveToLineEndOrDocumentEnd,
    moveToLineStartOrDocumentStart,
    moveVertically,
} from "../cursor-utils";

describe("cursor-utils", () => {
    describe("getCursorFromLineColumn", () => {
        it("should convert line/column to cursor position", () => {
            const result = getCursorFromLineColumn("hello world test", 5, 2, 3);
            // Lines: ["hello", " worl", "d tes", "t"]
            // Line 2 starts at position 10, column 3 -> position 13
            expect(result).toBe(13);
        });

        it("should handle line 0", () => {
            const result = getCursorFromLineColumn("hello world", 10, 0, 5);
            expect(result).toBe(5);
        });

        it("should clamp column to line length", () => {
            const result = getCursorFromLineColumn("hello world", 5, 0, 10);
            // Line 0 is "hello" (length 5), so column 10 -> 5
            expect(result).toBe(5);
        });

        it("should handle invalid line numbers", () => {
            const result = getCursorFromLineColumn("hello", 10, -1, 3);
            expect(result).toBe(0);

            const result2 = getCursorFromLineColumn("hello", 10, 10, 3);
            expect(result2).toBe(0);
        });

        it("should handle negative column", () => {
            const result = getCursorFromLineColumn("hello world", 10, 0, -5);
            expect(result).toBe(0);
        });
    });

    describe("getLineAndColumn", () => {
        it("should return line and column for cursor position", () => {
            const result = getLineAndColumn("hello world test", 5, 8);
            expect(result.line).toBe(1);
            expect(result.column).toBe(3);
        });

        it("should handle cursor at start", () => {
            const result = getLineAndColumn("hello world", 10, 0);
            expect(result.line).toBe(0);
            expect(result.column).toBe(0);
        });

        it("should handle cursor at end", () => {
            const result = getLineAndColumn("hello", 10, 5);
            expect(result.line).toBe(0);
            expect(result.column).toBe(5);
        });
    });

    describe("moveVertically", () => {
        it("should move cursor up", () => {
            const text = "line1\nline2\nline3";
            const result = moveVertically(text, 10, 8, "up"); // From "line2" position 2
            expect(result.newCursorPosition).toBe(2); // To "line1" position 2
            expect(result.newDesiredColumn).toBe(2);
        });

        it("should move cursor down", () => {
            const text = "line1\nline2\nline3";
            const result = moveVertically(text, 10, 2, "down"); // From "line1" position 2
            expect(result.newCursorPosition).toBe(8); // To "line2" position 2
            expect(result.newDesiredColumn).toBe(2);
        });

        it("should maintain desired column", () => {
            const text = "long line\nshort\nvery long line";
            const result = moveVertically(text, 20, 5, "down", 5); // Maintain column 5
            expect(result.newCursorPosition).toBe(15); // "short" only has 5 chars, so position 5
            expect(result.newDesiredColumn).toBe(5);
        });

        it("should not move beyond boundaries", () => {
            const text = "single line";
            const upResult = moveVertically(text, 20, 5, "up");
            const downResult = moveVertically(text, 20, 5, "down");

            expect(upResult.newCursorPosition).toBe(5);
            expect(downResult.newCursorPosition).toBe(5);
        });

        it("should handle empty lines", () => {
            const text = "line1\n\nline3";
            const result = moveVertically(text, 10, 2, "down"); // From "line1" to empty line
            expect(result.newCursorPosition).toBe(6); // Start of empty line
            expect(result.newDesiredColumn).toBe(2);
        });

        it("should clamp to shorter lines", () => {
            const text = "very long line\nsh\nother line";
            const result = moveVertically(text, 20, 10, "down"); // From long line to "sh"
            expect(result.newCursorPosition).toBe(17); // End of "sh" (position 2 in short line)
            expect(result.newDesiredColumn).toBe(10);
        });
    });

    describe("findLineStart", () => {
        it("should find start of current line", () => {
            const result = findLineStart("hello world test", 5, 8);
            // Position 8 is in line 1 (" worl"), which starts at position 5
            expect(result).toBe(5);
        });

        it("should handle first line", () => {
            const result = findLineStart("hello world", 10, 3);
            expect(result).toBe(0);
        });

        it("should handle multi-line text", () => {
            const result = findLineStart("line1\nline2\nline3", 10, 8);
            // Position 8 is in "line2", which starts at position 6
            expect(result).toBe(6);
        });
    });

    describe("findLineEnd", () => {
        it("should find end of current line", () => {
            const result = findLineEnd("hello world test", 5, 8);
            // Position 8 is in line 1 (" worl"), which ends at position 10
            expect(result).toBe(10);
        });

        it("should handle last line", () => {
            const result = findLineEnd("hello world", 10, 8);
            expect(result).toBe(10); // End of line, not text
        });

        it("should handle multi-line text", () => {
            const result = findLineEnd("line1\nline2\nline3", 10, 8);
            // Position 8 is in "line2", which ends at position 11
            expect(result).toBe(11);
        });
    });

    describe("moveToLineStartOrDocumentStart", () => {
        it("should move to line start from middle of first line", () => {
            const result = moveToLineStartOrDocumentStart("hello world", 10, 5);
            expect(result).toBe(0);
        });

        it("should stay at document start", () => {
            const result = moveToLineStartOrDocumentStart("hello world", 10, 0);
            expect(result).toBe(0);
        });

        it("should move to line start from middle of non-first line", () => {
            const result = moveToLineStartOrDocumentStart("line1\nline2", 10, 8);
            expect(result).toBe(6); // Start of "line2"
        });

        it("should move to document start from line start of non-first line", () => {
            const result = moveToLineStartOrDocumentStart("line1\nline2", 10, 6);
            expect(result).toBe(0);
        });
    });

    describe("moveToLineEndOrDocumentEnd", () => {
        it("should move to line end from middle of last line", () => {
            const result = moveToLineEndOrDocumentEnd("hello world", 10, 5);
            expect(result).toBe(10); // End of line, which is also end of text for single line
        });

        it("should stay at document end", () => {
            const result = moveToLineEndOrDocumentEnd("hello world", 10, 11);
            expect(result).toBe(11);
        });

        it("should move to line end from middle of non-last line", () => {
            const result = moveToLineEndOrDocumentEnd("line1\nline2", 10, 2);
            expect(result).toBe(5); // End of "line1"
        });

        it("should move to document end from line end of non-last line", () => {
            const result = moveToLineEndOrDocumentEnd("line1\nline2", 10, 5);
            expect(result).toBe(11); // End of text
        });
    });

    describe("getLineCount", () => {
        it("should count single line", () => {
            const result = getLineCount("hello world", 20);
            expect(result).toBe(1);
        });

        it("should count wrapped lines", () => {
            const result = getLineCount("hello world test", 5);
            expect(result).toBe(4); // ["hello", " worl", "d tes", "t"]
        });

        it("should count multi-line text", () => {
            const result = getLineCount("line1\nline2\nline3", 10);
            expect(result).toBe(3);
        });

        it("should handle empty text", () => {
            const result = getLineCount("", 10);
            expect(result).toBe(1);
        });

        it("should handle zero width", () => {
            const result = getLineCount("hello", 0);
            expect(result).toBe(1);
        });
    });

    describe("isAtLineStart", () => {
        it("should return true for cursor at line start", () => {
            const result = isAtLineStart("hello world test", 5, 5);
            // Position 5 is start of " worl" line
            expect(result).toBe(true);
        });

        it("should return false for cursor not at line start", () => {
            const result = isAtLineStart("hello world", 10, 3);
            expect(result).toBe(false);
        });

        it("should return true for cursor at document start", () => {
            const result = isAtLineStart("hello world", 10, 0);
            expect(result).toBe(true);
        });

        it("should handle multi-line text", () => {
            const result1 = isAtLineStart("line1\nline2", 10, 6); // Start of "line2"
            const result2 = isAtLineStart("line1\nline2", 10, 8); // Middle of "line2"
            expect(result1).toBe(true);
            expect(result2).toBe(false);
        });
    });

    describe("isAtLineEnd", () => {
        it("should return true for cursor at line end", () => {
            // Simple test with single line first
            const result = isAtLineEnd("hello", 10, 5);
            expect(result).toBe(true);
        });

        it("should return false for cursor not at line end", () => {
            const result = isAtLineEnd("hello world", 10, 3);
            expect(result).toBe(false);
        });

        it("should return true for cursor at document end", () => {
            const result = isAtLineEnd("hello world", 10, 11);
            expect(result).toBe(true);
        });

        it("should handle multi-line text", () => {
            const result1 = isAtLineEnd("line1\nline2", 10, 5); // End of "line1"
            const result2 = isAtLineEnd("line1\nline2", 10, 3); // Middle of "line1"
            expect(result1).toBe(true);
            expect(result2).toBe(false);
        });
    });

    describe("Edge cases and integration", () => {
        it("should maintain consistency between position conversions", () => {
            const text = "line1\nline2\nline3";
            const width = 10;

            // Test round-trip conversion
            for (let pos = 0; pos <= text.length; pos++) {
                const { line, column } = getLineAndColumn(text, width, pos);
                const backToPos = getCursorFromLineColumn(text, width, line, column);
                expect(backToPos).toBe(pos);
            }
        });

        it("should handle vertical movement with different line lengths", () => {
            const text = "a\nlong line\nb";
            let pos = 1; // End of first line "a"
            let desiredCol = null;

            // Move down to "long line"
            const down1 = moveVertically(text, 20, pos, "down", desiredCol);
            expect(down1.newCursorPosition).toBe(3); // Position 1 in "long line"

            // Move down to "b"
            const down2 = moveVertically(
                text,
                20,
                down1.newCursorPosition,
                "down",
                down1.newDesiredColumn
            );
            expect(down2.newCursorPosition).toBe(13); // End of "b" - position 13 is after "b"
        });

        it("should handle operations on empty strings", () => {
            expect(getCursorFromLineColumn("", 10, 0, 0)).toBe(0);
            expect(getLineAndColumn("", 10, 0)).toEqual({ line: 0, column: 0 });
            expect(moveVertically("", 10, 0, "up")).toEqual({
                newCursorPosition: 0,
                newDesiredColumn: null,
            });
            expect(findLineStart("", 10, 0)).toBe(0);
            expect(findLineEnd("", 10, 0)).toBe(0);
            expect(getLineCount("", 10)).toBe(1);
            expect(isAtLineStart("", 10, 0)).toBe(true);
            expect(isAtLineEnd("", 10, 0)).toBe(true);
        });

        it("should handle single character strings", () => {
            const text = "a";
            expect(getCursorFromLineColumn(text, 10, 0, 1)).toBe(1);
            expect(getLineAndColumn(text, 10, 1)).toEqual({ line: 0, column: 1 });
            expect(findLineStart(text, 10, 1)).toBe(0);
            expect(findLineEnd(text, 10, 0)).toBe(1);
        });

        it("should handle strings with only whitespace", () => {
            const text = "   ";
            expect(isAtLineStart(text, 10, 0)).toBe(true);
            expect(isAtLineEnd(text, 10, 3)).toBe(true);
            expect(getLineCount(text, 10)).toBe(1);
        });

        it("should handle strings with mixed content", () => {
            const text = "hello\tworld\n\ntest  end";
            const lineCount = getLineCount(text, 20);
            expect(lineCount).toBe(3); // Three hard lines

            const wordStart = findLineStart(text, 20, 15); // Middle of "test"
            expect(wordStart).toBe(13); // Start of "test  end" line
        });
    });
});
