import { ToolUseResponse } from "@augment-internal/sidecar-libs/src/tools/tool-types";

import { AgentLoopEventListener } from "../../agent_loop/agent_loop";

/**
 * TUI-specific event listener that forwards agent loop events to the TUI components.
 * Unlike the CLI event listener, this doesn't directly write to stdout but instead
 * calls callbacks that update the TUI state.
 */
export class TUIAgentEventListener implements AgentLoopEventListener {
    private callbacks: AgentLoopEventListener;

    constructor(callbacks: AgentLoopEventListener = {}) {
        this.callbacks = callbacks;
    }

    onThinkingStart(): void {
        this.callbacks.onThinkingStart?.();
    }

    onThinkingStop(): void {
        this.callbacks.onThinkingStop?.();
    }

    onAssistantResponseStart(): void {
        this.callbacks.onAssistantResponseStart?.();
    }

    onAssistantResponseChunk(text: string): void {
        this.callbacks.onAssistantResponseChunk?.(text);
    }

    onAssistantResponseEnd(): void {
        this.callbacks.onAssistantResponseEnd?.();
    }

    onToolCallStart(toolName: string, toolInput: Record<string, any>): void {
        this.callbacks.onToolCallStart?.(toolName, toolInput);
    }

    onToolCallResult(toolName: string, toolResult: ToolUseResponse): void {
        this.callbacks.onToolCallResult?.(toolName, toolResult);
    }

    onError(error: unknown, context?: string): void {
        this.callbacks.onError?.(error, context);
    }

    onAgentLoopComplete(): void {
        this.callbacks.onAgentLoopComplete?.();
    }

    onMaxIterationsExceeded(maxIterations: number): void {
        this.callbacks.onMaxIterationsExceeded?.(maxIterations);
    }

    /**
     * Update the callbacks dynamically
     */
    updateCallbacks(callbacks: Partial<AgentLoopEventListener>): void {
        this.callbacks = { ...this.callbacks, ...callbacks };
    }
}
