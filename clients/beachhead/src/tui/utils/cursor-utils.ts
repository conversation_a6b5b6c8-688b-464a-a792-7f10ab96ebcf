/**
 * Cursor Utilities - Pure utility functions for cursor position management and navigation
 * 
 * This module provides cursor-specific functionality that works with text layout:
 * - Cursor position conversions between absolute and line/column coordinates
 * - Cursor movement operations (vertical navigation, line boundaries)
 * - Cursor position validation and boundary checks
 */

import { buildLineMapping, calculateWrappedText } from "./buffer-utils";

/**
 * Result of a vertical cursor movement operation
 */
export interface VerticalMovementResult {
    newCursorPosition: number;
    newDesiredColumn: number | null;
}

/**
 * Converts line and column coordinates to absolute cursor position.
 * 
 * @param text - The text
 * @param width - Available width for wrapping
 * @param targetLine - Target line index
 * @param targetColumn - Target column index
 * @returns Absolute cursor position
 */
export function getCursorFromLineColumn(
    text: string,
    width: number,
    targetLine: number,
    targetColumn: number
): number {
    const { lineStartPositions, lineLengths } = buildLineMapping(text, width);

    if (targetLine < 0 || targetLine >= lineStartPositions.length) {
        return 0; // Clamp to start if invalid line
    }

    const lineStart = lineStartPositions[targetLine];
    const lineLength = lineLengths[targetLine];
    const clampedColumn = Math.max(0, Math.min(targetColumn, lineLength));

    return lineStart + clampedColumn;
}

/**
 * Gets the line and column coordinates for a given cursor position.
 * 
 * @param text - The text
 * @param width - Available width for wrapping
 * @param cursorPosition - Absolute cursor position
 * @returns Object with line and column indices
 */
export function getLineAndColumn(
    text: string,
    width: number,
    cursorPosition: number
): { line: number; column: number } {
    const wrapped = calculateWrappedText(text, width, cursorPosition);
    return { line: wrapped.cursorLine, column: wrapped.cursorColumn };
}

/**
 * Moves cursor vertically (up or down) while attempting to maintain column position.
 * 
 * @param text - The text
 * @param width - Available width for wrapping
 * @param currentCursorPosition - Current cursor position
 * @param direction - Direction to move ('up' or 'down')
 * @param desiredColumn - Preferred column to maintain (null to use current column)
 * @returns New cursor position and updated desired column
 */
export function moveVertically(
    text: string,
    width: number,
    currentCursorPosition: number,
    direction: 'up' | 'down',
    desiredColumn: number | null = null
): VerticalMovementResult {
    const { lines, lineStartPositions, lineLengths } = buildLineMapping(text, width);
    const { cursorLine, cursorColumn } = calculateWrappedText(text, width, currentCursorPosition);

    // Determine target line
    const targetLine = direction === 'up' ? cursorLine - 1 : cursorLine + 1;

    // Check if movement is possible
    if (targetLine < 0 || targetLine >= lines.length) {
        // Can't move beyond boundaries
        return {
            newCursorPosition: currentCursorPosition,
            newDesiredColumn: desiredColumn,
        };
    }

    // Use desired column if available, otherwise current column
    const preferredColumn = desiredColumn ?? cursorColumn;
    
    // For empty lines, always position at column 0
    const targetColumn = lineLengths[targetLine] === 0
        ? 0
        : Math.min(preferredColumn, lineLengths[targetLine]);

    const newCursorPosition = lineStartPositions[targetLine] + targetColumn;

    // Set desired column if this is the first vertical movement
    const newDesiredColumn = desiredColumn ?? cursorColumn;

    return {
        newCursorPosition,
        newDesiredColumn,
    };
}

/**
 * Finds the start position of the line containing the cursor.
 * 
 * @param text - The text
 * @param width - Available width for wrapping
 * @param cursorPosition - Current cursor position
 * @returns Position of the line start
 */
export function findLineStart(text: string, width: number, cursorPosition: number): number {
    const { cursorLine } = calculateWrappedText(text, width, cursorPosition);
    const { lineStartPositions } = buildLineMapping(text, width);
    return lineStartPositions[cursorLine] ?? 0;
}

/**
 * Finds the end position of the line containing the cursor.
 * 
 * @param text - The text
 * @param width - Available width for wrapping
 * @param cursorPosition - Current cursor position
 * @returns Position of the line end
 */
export function findLineEnd(text: string, width: number, cursorPosition: number): number {
    const { cursorLine } = calculateWrappedText(text, width, cursorPosition);
    const { lineStartPositions, lineLengths } = buildLineMapping(text, width);
    
    if (cursorLine >= lineStartPositions.length) {
        return text.length;
    }
    
    return lineStartPositions[cursorLine] + lineLengths[cursorLine];
}

/**
 * Handles cursor movement to the beginning of line for multi-line aware navigation.
 * If already at line start and on first line, moves to absolute start.
 * 
 * @param text - The text
 * @param width - Available width for wrapping
 * @param cursorPosition - Current cursor position
 * @returns New cursor position
 */
export function moveToLineStartOrDocumentStart(
    text: string,
    width: number,
    cursorPosition: number
): number {
    const { cursorLine, cursorColumn } = calculateWrappedText(text, width, cursorPosition);
    
    if (cursorLine === 0 && cursorColumn > 0) {
        // On first line but not at start - move to line start
        return findLineStart(text, width, cursorPosition);
    } else if (cursorLine === 0 && cursorColumn === 0) {
        // Already at document start - stay there
        return 0;
    } else if (cursorColumn > 0) {
        // On non-first line, not at line start - move to line start
        return findLineStart(text, width, cursorPosition);
    } else {
        // At line start on non-first line - move to document start
        return 0;
    }
}

/**
 * Handles cursor movement to the end of line for multi-line aware navigation.
 * If already at line end and on last line, moves to absolute end.
 * 
 * @param text - The text
 * @param width - Available width for wrapping
 * @param cursorPosition - Current cursor position
 * @returns New cursor position
 */
export function moveToLineEndOrDocumentEnd(
    text: string,
    width: number,
    cursorPosition: number
): number {
    const { lines, cursorLine, cursorColumn } = calculateWrappedText(text, width, cursorPosition);
    const { lineLengths } = buildLineMapping(text, width);
    
    const isLastLine = cursorLine === lines.length - 1;
    const isAtLineEnd = cursorColumn === lineLengths[cursorLine];
    
    if (isLastLine && isAtLineEnd) {
        // Already at document end - stay there
        return text.length;
    } else if (isLastLine && !isAtLineEnd) {
        // On last line but not at end - move to line end (which is document end)
        return text.length;
    } else if (!isAtLineEnd) {
        // Not at line end - move to line end
        return findLineEnd(text, width, cursorPosition);
    } else {
        // At line end on non-last line - move to document end
        return text.length;
    }
}

/**
 * Calculates the total number of display lines for given text and width.
 * 
 * @param text - The text
 * @param width - Available width for wrapping
 * @returns Number of display lines
 */
export function getLineCount(text: string, width: number): number {
    const { lines } = buildLineMapping(text, width);
    return lines.length;
}

/**
 * Checks if a cursor position is at the start of a line.
 * 
 * @param text - The text
 * @param width - Available width for wrapping
 * @param cursorPosition - Cursor position to check
 * @returns True if at line start
 */
export function isAtLineStart(text: string, width: number, cursorPosition: number): boolean {
    const { cursorColumn } = calculateWrappedText(text, width, cursorPosition);
    return cursorColumn === 0;
}

/**
 * Checks if a cursor position is at the end of a line.
 * 
 * @param text - The text
 * @param width - Available width for wrapping
 * @param cursorPosition - Cursor position to check
 * @returns True if at line end
 */
export function isAtLineEnd(text: string, width: number, cursorPosition: number): boolean {
    const { cursorLine, cursorColumn } = calculateWrappedText(text, width, cursorPosition);
    const { lineLengths } = buildLineMapping(text, width);
    return cursorColumn === lineLengths[cursorLine];
}