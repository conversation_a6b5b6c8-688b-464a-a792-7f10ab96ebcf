/**
 * Utility functions for handling streaming content and finding safe split points
 */

/**
 * Find the last safe split point in a string for progressive rendering.
 * This implementation prioritizes natural breaks for line-by-line streaming.
 *
 * @param content The content to find a split point in
 * @returns The index of the last safe split point, or content.length if no safe point found
 */
export function findLastSafeSplitPoint(content: string): number {
    // Simple approach: use natural split points anywhere (including inside code blocks)
    const naturalSplit = findNaturalSplitPoint(content);
    return naturalSplit > 0 ? naturalSplit : content.length;
}

/**
 * Find natural split points in content for better readability
 *  @param content The content to check
 *  @returns The index of the last natural split point, or -1 if no split point found
 */
function findNaturalSplitPoint(content: string): number {
    // Priority order for natural splits (optimized for line-by-line streaming):

    // 1. Double newlines (paragraph breaks) - best for readability
    let lastDoubleNewline = content.lastIndexOf("\n\n");
    if (lastDoubleNewline > 100) {
        return lastDoubleNewline + 2; // Include the double newline
    }

    // 2. Single newlines - enables line-by-line streaming including code blocks
    let lastNewline = content.lastIndexOf("\n");
    if (lastNewline > 100) {
        return lastNewline + 1; // Include the newline
    }

    return -1; // No good split point found
}

/**
 * Split content at a natural breakpoint and return the chunk and remainder
 * @param content The content to split
 * @returns Tuple [completedChunk, remaining] - empty completedChunk means no split point found
 */
export function splitContent(content: string): [string, string] {
    const naturalSplitPoint = findNaturalSplitPoint(content);

    if (naturalSplitPoint > 100) {
        // Has a good natural split point
        const completeChunk = content.substring(0, naturalSplitPoint);
        const remaining = content.substring(naturalSplitPoint);

        return [completeChunk, remaining];
    }

    // No natural split point - return empty completed chunk and all content as remaining
    return ["", content];
}
