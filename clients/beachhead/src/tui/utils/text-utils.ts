/**
 * Text manipulation utilities for InputBuffer reducer
 */

/**
 * Find the start of the word containing the given position
 */
export const findWordStart = (text: string, position: number): number => {
    if (position <= 0) {
        return 0;
    }

    let wordStart = position - 1;
    // Skip spaces
    while (wordStart > 0 && text[wordStart] === " ") {
        wordStart--;
    }
    // Find word boundary
    while (wordStart > 0 && text[wordStart - 1] !== " ") {
        wordStart--;
    }
    return wordStart;
};

/**
 * Find the end of the word containing the given position
 */
export const findWordEnd = (text: string, position: number): number => {
    if (position >= text.length) {
        return text.length;
    }

    let wordEnd = position;
    // Skip current word
    while (wordEnd < text.length && text[wordEnd] !== " ") {
        wordEnd++;
    }
    // Skip spaces
    while (wordEnd < text.length && text[wordEnd] === " ") {
        wordEnd++;
    }
    return wordEnd;
};

/**
 * Insert text at a specific position
 */
export const insertTextAt = (
    originalText: string,
    insertText: string,
    position: number
): string => {
    return originalText.substring(0, position) + insertText + originalText.substring(position);
};

/**
 * Delete text between two positions
 */
export const deleteTextRange = (text: string, start: number, end: number): string => {
    return text.substring(0, start) + text.substring(end);
};

/**
 * Transpose two characters at cursor position
 */
export const transposeChars = (
    text: string,
    cursorPosition: number
): {
    newText: string;
    newCursorPosition: number;
} => {
    if (cursorPosition <= 0 || cursorPosition >= text.length) {
        return { newText: text, newCursorPosition: cursorPosition };
    }

    const chars = text.split("");
    [chars[cursorPosition - 1], chars[cursorPosition]] = [
        chars[cursorPosition],
        chars[cursorPosition - 1],
    ];

    return {
        newText: chars.join(""),
        newCursorPosition: Math.min(cursorPosition + 1, text.length),
    };
};

/**
 * Clamp cursor position to valid range
 */
export const clampCursorPosition = (position: number, textLength: number): number => {
    return Math.max(0, Math.min(position, textLength));
};

/**
 * Clean pasted text by normalizing line endings and removing control characters
 */
export const cleanPastedText = (text: string): string => {
    return (
        text
            .replace(/\r\n/g, "\n")
            .replace(/\r/g, "\n")
            // eslint-disable-next-line no-control-regex
            .replace(/[\x00-\x08\x0B-\x0C\x0E-\x1F\x7F]/g, "")
    ); // Strip control characters except \t, \n, \r
};
