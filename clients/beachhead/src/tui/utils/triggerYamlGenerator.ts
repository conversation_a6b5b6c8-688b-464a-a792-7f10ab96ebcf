/**
 * Utilities for generating YAML from structured trigger configurations
 */

import { TriggerEventConfig, PullRequestTriggerConfig, PushTriggerConfig, ScheduleTriggerConfig } from "../types/TriggerConfigTypes";

/**
 * Groups trigger configs by event type
 */
function groupBy<T extends { event: string }>(items: T[], key: keyof T): Record<string, T[]> {
    return items.reduce((groups, item) => {
        const group = item[key] as string;
        groups[group] = groups[group] || [];
        groups[group].push(item);
        return groups;
    }, {} as Record<string, T[]>);
}

/**
 * Generates YAML for pull_request events
 */
function generatePullRequestYAML(configs: PullRequestTriggerConfig[]): string {
    const allTypes = [...new Set(configs.flatMap(c => c.types || []))];
    const allBranches = [...new Set(configs.flatMap(c => c.branches || []))];
    const allPaths = [...new Set(configs.flatMap(c => c.paths || []))];
    
    let yaml = "  pull_request:";
    
    if (allTypes.length > 0) {
        yaml += `\n    types: [${allTypes.join(', ')}]`;
    }
    
    if (allBranches.length > 0) {
        yaml += `\n    branches: [${allBranches.join(', ')}]`;
    }
    
    if (allPaths.length > 0) {
        yaml += `\n    paths: [${allPaths.map(p => `'${p}'`).join(', ')}]`;
    }
    
    return yaml;
}

/**
 * Generates YAML for push events
 */
function generatePushYAML(configs: PushTriggerConfig[]): string {
    const allBranches = [...new Set(configs.flatMap(c => c.branches || []))];
    const allPaths = [...new Set(configs.flatMap(c => c.paths || []))];
    const allTags = [...new Set(configs.flatMap(c => c.tags || []))];
    
    let yaml = "  push:";
    
    if (allBranches.length > 0) {
        yaml += `\n    branches: [${allBranches.join(', ')}]`;
    }
    
    if (allTags.length > 0) {
        yaml += `\n    tags: [${allTags.join(', ')}]`;
    }
    
    if (allPaths.length > 0) {
        yaml += `\n    paths: [${allPaths.map(p => `'${p}'`).join(', ')}]`;
    }
    
    return yaml;
}

/**
 * Generates YAML for schedule events
 */
function generateScheduleYAML(configs: ScheduleTriggerConfig[]): string {
    const allCrons = [...new Set(configs.flatMap(c => c.cron || []))];
    
    let yaml = "  schedule:";
    allCrons.forEach(cron => {
        yaml += `\n    - cron: '${cron}'`;
    });
    
    return yaml;
}

/**
 * Generates YAML for workflow_dispatch events
 */
function generateWorkflowDispatchYAML(): string {
    return "  workflow_dispatch:";
}

/**
 * Generates YAML for issues events
 */
function generateIssuesYAML(configs: any[]): string {
    const allTypes = [...new Set(configs.flatMap(c => c.types || []))];
    
    let yaml = "  issues:";
    
    if (allTypes.length > 0) {
        yaml += `\n    types: [${allTypes.join(', ')}]`;
    }
    
    return yaml;
}

/**
 * Generates YAML for release events
 */
function generateReleaseYAML(configs: any[]): string {
    const allTypes = [...new Set(configs.flatMap(c => c.types || []))];
    
    let yaml = "  release:";
    
    if (allTypes.length > 0) {
        yaml += `\n    types: [${allTypes.join(', ')}]`;
    }
    
    return yaml;
}

/**
 * Generates complete trigger YAML from structured trigger configurations
 */
export function generateTriggerYAML(triggerConfigs: TriggerEventConfig[]): string {
    if (triggerConfigs.length === 0) {
        throw new Error("At least one trigger configuration is required");
    }
    
    const eventGroups = groupBy(triggerConfigs, 'event');
    
    const yamlParts = Object.entries(eventGroups).map(([event, configs]) => {
        switch (event) {
            case 'pull_request':
                return generatePullRequestYAML(configs as PullRequestTriggerConfig[]);
            case 'push':
                return generatePushYAML(configs as PushTriggerConfig[]);
            case 'schedule':
                return generateScheduleYAML(configs as ScheduleTriggerConfig[]);
            case 'workflow_dispatch':
                return generateWorkflowDispatchYAML();
            case 'issues':
                return generateIssuesYAML(configs);
            case 'release':
                return generateReleaseYAML(configs);
            default:
                // Fallback for unknown event types
                return `  ${event}:`;
        }
    });
    
    return `on:\n${yamlParts.join('\n')}`;
}
