/**
 * GitHub workflow YAML generation utilities
 */

import { WorkflowConfig } from "../types/WorkflowTypes";
import { generateTriggerYAML } from "./triggerYamlGenerator";

/**
 * Generates GitHub workflow YAML content based on configuration
 */
export function generateWorkflowYAML(config: WorkflowConfig): string {
    const { action, trigger, triggerConfig } = config;

    if (!action || !trigger || !triggerConfig) {
        throw new Error("Action, trigger, and trigger config must be specified");
    }

    const workflowName = action.getWorkflowName();
    const actionRepo = action.getActionRepository();
    const jobName = action.getJobName();
    const stepName = action.getStepName();

    const triggerEventConfigs = trigger.generateTriggerEventConfig(triggerConfig);
    const triggerConfigYaml = generateTriggerYAML(triggerEventConfigs);
    const jobCondition = trigger.generateJobCondition(triggerConfig);

    return `name: ${workflowName}

${triggerConfigYaml}

permissions:
  contents: read
  pull-requests: write

jobs:
  ${jobName}:
    name: ${workflowName}
    runs-on:
      - ubuntu-latest
    ${jobCondition}
    steps:
      - name: ${stepName}
        uses: ${actionRepo}
        with:
          augment_session_auth: \${{ secrets.AUGMENT_SESSION_AUTH }}
          github_token: \${{ secrets.GITHUB_TOKEN }}
          pull_number: \${{ github.event.pull_request.number }}
          repo_name: \${{ github.repository }}
`;
}
