/**
 * Buffer Utilities - Pure utility functions for text layout and line mapping
 * 
 * This module provides the core functionality for text layout operations:
 * - Text wrapping and line mapping calculations
 * - Cursor position calculations within wrapped text
 * - Multi-line text layout utilities
 */

/**
 * Represents the mapping between display lines and text positions
 */
export interface LineMapping {
    lines: string[];
    lineStartPositions: number[];
    lineLengths: number[];
}

/**
 * Represents wrapped text with cursor position information
 */
export interface WrappedText {
    lines: string[];
    cursorLine: number;
    cursorColumn: number;
}

/**
 * Builds a mapping of display lines to their corresponding text positions.
 * This is the foundation for all cursor operations in wrapped text.
 * 
 * @param text - The text to analyze
 * @param width - Available width for text wrapping
 * @returns Line mapping with lines, start positions, and lengths
 */
export function buildLineMapping(text: string, width: number): LineMapping {
    const lines: string[] = [];
    const lineStartPositions: number[] = [];
    const lineLengths: number[] = [];

    if (width <= 0) {
        return { lines: [""], lineStartPositions: [0], lineLengths: [0] };
    }

    const hardLines = text.split("\n");
    let position = 0;

    for (let i = 0; i < hardLines.length; i++) {
        const hardLine = hardLines[i];

        if (hardLine.length === 0) {
            // Empty line
            lines.push("");
            lineStartPositions.push(position);
            lineLengths.push(0);
        } else {
            // Wrap long lines
            for (let j = 0; j < hardLine.length; j += width) {
                const chunk = hardLine.substring(j, j + width);
                lines.push(chunk);
                lineStartPositions.push(position + j);
                lineLengths.push(chunk.length);
            }
        }

        position += hardLine.length;
        if (i < hardLines.length - 1) {
            position += 1; // Account for newline character
        }
    }

    return { lines, lineStartPositions, lineLengths };
}

/**
 * Calculates how text should be wrapped for display and determines cursor position.
 * 
 * @param text - The text to wrap
 * @param width - Available width for wrapping
 * @param cursorPosition - Absolute cursor position in the text
 * @returns Wrapped text with cursor line and column
 */
export function calculateWrappedText(
    text: string,
    width: number,
    cursorPosition: number
): WrappedText {
    if (width <= 0 || !text || text.length === 0) {
        return { lines: [""], cursorLine: 0, cursorColumn: 0 };
    }

    const { lines, lineStartPositions } = buildLineMapping(text, width);

    // Find which line contains the cursor
    let cursorLine = 0;
    let cursorColumn = 0;

    for (let i = 0; i < lineStartPositions.length; i++) {
        const lineStart = lineStartPositions[i];
        const lineEnd = i + 1 < lineStartPositions.length ? lineStartPositions[i + 1] : text.length + 1;

        if (cursorPosition >= lineStart && cursorPosition < lineEnd) {
            cursorLine = i;
            cursorColumn = Math.min(cursorPosition - lineStart, lines[i].length);
            break;
        }
    }

    // Handle cursor at very end of text on last line
    if (cursorPosition === text.length && lineStartPositions.length > 0) {
        const lastLineIdx = lineStartPositions.length - 1;
        if (cursorPosition >= lineStartPositions[lastLineIdx]) {
            cursorLine = lastLineIdx;
            cursorColumn = cursorPosition - lineStartPositions[lastLineIdx];
        }
    }

    return { lines, cursorLine, cursorColumn };
}