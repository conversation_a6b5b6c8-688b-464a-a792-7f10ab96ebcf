/**
 * File system utilities for workflow generation
 */

import * as fs from "fs";
import * as path from "path";
import { WorkflowConfig } from "../types/WorkflowTypes";
import { generateWorkflowYAML } from "./workflowGenerator";

export interface WriteResult {
    success: boolean;
    filePath?: string;
    error?: string;
}

/**
 * Ensures the .github/workflows directory exists
 */
export function ensureWorkflowDirectory(): string {
    const workflowDir = path.join(process.cwd(), ".github", "workflows");
    
    if (!fs.existsSync(workflowDir)) {
        fs.mkdirSync(workflowDir, { recursive: true });
    }
    
    return workflowDir;
}

/**
 * Writes the workflow file to the filesystem
 */
export function writeWorkflowFile(config: WorkflowConfig): WriteResult {
    try {
        const workflowDir = ensureWorkflowDirectory();
        const filePath = path.join(workflowDir, config.action!.getFileName());
        const yamlContent = generateWorkflowYAML(config);
        
        fs.writeFileSync(filePath, yamlContent, "utf8");
        
        return { success: true, filePath };
    } catch (error) {
        return { 
            success: false, 
            error: error instanceof Error ? error.message : "Unknown error occurred" 
        };
    }
}
