/**
 * Utility functions for creating key processing results
 */
import { KeyProcessingResult, SideEffect } from "../types/KeyProcessorTypes";
import { InputBufferAction } from "../types/InputBufferTypes";

/**
 * Helper function to create an empty processing result
 */
export function createEmptyResult(handled: boolean = false): KeyProcessingResult {
    return {
        actions: [],
        handled,
    };
}

/**
 * Helper function to create a result with actions
 */
export function createActionResult(
    actions: InputBufferAction[],
    sideEffects?: SideEffect[]
): KeyProcessingResult {
    return {
        actions,
        handled: true,
        sideEffects,
    };
}

/**
 * Helper function to create a result with side effects only
 */
export function createSideEffectResult(sideEffects: SideEffect[]): KeyProcessingResult {
    return {
        actions: [],
        handled: true,
        sideEffects,
    };
}