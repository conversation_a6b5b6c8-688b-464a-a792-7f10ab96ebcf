const { pathsToModuleNameMapper } = require("ts-jest");
const { compilerOptions } = require("../tsconfig");

const pathAliases = pathsToModuleNameMapper(compilerOptions.paths, {
    prefix: "<rootDir>/../",
});

module.exports = {
    roots: ["<rootDir>"],
    transform: {
        // eslint-disable-next-line @typescript-eslint/naming-convention
        "^.+\\.(ts|tsx)?$": [
            "ts-jest",
            {
                tsconfig: {
                    module: "commonjs",
                    target: "ES2022",
                    esModuleInterop: true,
                    allowSyntheticDefaultImports: true,
                    jsx: "react",
                },
            },
        ],
    },
    testRegex: "(/__tests__/.*|(\\.|/)(test|spec))\\.test.ts?$",
    moduleFileExtensions: ["ts", "tsx", "js", "jsx", "json", "node"],
    collectCoverage: true,
    clearMocks: true,
    resetMocks: false,
    coverageDirectory: "coverage",
    testEnvironment: "node",
    setupFilesAfterEnv: ["<rootDir>/src/__mocks__/setup-jest.ts"],
    // Any local modules that we wish to mock need to be added to this list.
    moduleNameMapper: {
        ...pathAliases,
        // eslint-disable-next-line @typescript-eslint/naming-convention
        "^ink$": "<rootDir>/src/__mocks__/ink.ts",
        "^ink-text-input$": "<rootDir>/src/__mocks__/ink-text-input.ts",
        "^ink-spinner$": "<rootDir>/src/__mocks__/ink-spinner.ts",
        "^gradient-string$": "<rootDir>/src/__mocks__/gradient-string.ts",
        "^chalk$": "<rootDir>/src/__mocks__/chalk.ts",
        "^lowlight$": "<rootDir>/src/__mocks__/lowlight.ts",
        "^lowlight/lib/common$": "<rootDir>/src/__mocks__/lowlight.ts",
        "^highlight\\.js/lib/languages/(.*)$": "<rootDir>/src/__mocks__/highlight-language.ts",
        "^hast-util-to-jsx-runtime$": "<rootDir>/src/__mocks__/hast-util-to-jsx-runtime.ts",
        "^react/jsx-runtime$": "<rootDir>/src/__mocks__/react-jsx-runtime.ts",
        // TODO(jeff): Add these mocks.
        // "^.*/os$": "<rootDir>/src/__mocks__/os.ts",
        // "^.*/fs-utils$": "<rootDir>/src/__mocks__/fs-utils.ts",
    },
    transformIgnorePatterns: [
        "node_modules/(?!(@augment-internal/sidecar-libs|.aspect_rules_js/@augment\\+sidecar-libs|lowlight|highlight\\.js|hast-util-to-jsx-runtime))",
    ],
};
