# Remote Agents Beachhead

The remote agent beachhead is a long-running process that manages workspace
synchronization and executes local development tools on behalf of the agent loop
running in the backend. The beachhead is deployed in a container with customer
code, isolating the agent loop from the execution environment.

## Build Targets

This project provides several build targets:

### Beachhead Service
- `//clients/beachhead:beachhead` - The beachhead service binary

### CLI Tool
- `//clients/beachhead:cli` - Production CLI JavaScript bundle
- `//clients/beachhead:cli_dev` - Development CLI JavaScript bundle
- `//clients/beachhead:cli_self_contained` - Production CLI self-contained executable
- `//clients/beachhead:cli_self_contained_dev` - Development CLI self-contained executable

**For detailed CLI documentation, see [README_cli.md](README_cli.md)**

## Beachhead Service Usage

### Basic Usage

```bash
# Run beachhead service
AUGMENT_API_URL=https://staging-shard-0.api.augmentcode.com \
AUGMENT_API_TOKEN=$(cat ~/.augment/token) \
bazel run //clients/beachhead:beachhead -- --workspace-root /path/to/workspace
```

### CLI Mode

The beachhead can also operate in CLI mode for direct command execution:

```bash
# Single command execution
AUGMENT_API_URL=https://staging-shard-0.api.augmentcode.com \
AUGMENT_API_TOKEN=$(cat ~/.augment/token) \
bazel run //clients/beachhead:beachhead -- --cli --workspace-root /path/to/workspace --instruction "create a new temp.txt file with the content hello world"

# Interactive CLI mode
AUGMENT_API_URL=https://staging-shard-0.api.augmentcode.com \
AUGMENT_API_TOKEN=$(cat ~/.augment/token) \
bazel run //clients/beachhead:beachhead -- --cli --interactive --workspace-root /path/to/workspace
```

## GitHub Integration

The beachhead supports providing GitHub API tokens for remote tool calls. This allows the GitHub tool to use your personal access token instead of relying on backend-stored tokens.

### Using a GitHub Token File

```bash
# Create a file containing your GitHub token
echo "your-github-token-here" > ~/.github-token

# Run beachhead with GitHub token file
AUGMENT_API_URL=https://staging-shard-0.api.augmentcode.com \
AUGMENT_API_TOKEN=$(cat ~/.augment/token) \
bazel run //clients/beachhead:beachhead -- --cli --workspace-root /path/to/workspace --github-api-token ~/.github-token --instruction "list my GitHub repositories"
```

### Using Environment Variable

```bash
# Set GitHub token via environment variable
export GITHUB_API_TOKEN="your-github-token-here"

# Run beachhead (token will be automatically detected)
AUGMENT_API_URL=https://staging-shard-0.api.augmentcode.com \
AUGMENT_API_TOKEN=$(cat ~/.augment/token) \
bazel run //clients/beachhead:beachhead -- --cli --workspace-root /path/to/workspace --instruction "list my GitHub repositories"
```

**Note**: Environment variable takes precedence over file-based token. The token is only used for GitHub API tool calls and is passed securely to the backend.

## Architecture

The beachhead operates in two main modes:

### Service Mode (Default)
- Long-running process that connects to the Augment backend
- Manages workspace synchronization
- Executes tools and commands on behalf of remote agents
- Suitable for containerized deployments

### CLI Mode (`--cli` flag)
- Direct command execution interface
- Can run single commands or interactive sessions
- Useful for local development and testing
- See [README_cli.md](README_cli.md) for dedicated CLI tool builds

## Configuration

### Environment Variables

- `AUGMENT_API_URL` - Backend API endpoint
- `AUGMENT_API_TOKEN` - Authentication token
- `GITHUB_API_TOKEN` - Optional GitHub API token

### Command Line Options

- `--workspace-root` - Path to the workspace directory
- `--cli` - Enable CLI mode
- `--interactive` - Start interactive session (CLI mode only)
- `--instruction` - Execute single instruction (CLI mode only)
- `--github-api-token` - Path to GitHub token file

## Development

### Building

```bash
# Build beachhead service
bazel build //clients/beachhead:beachhead

# Build all targets
bazel build //clients/beachhead:all
```

### Testing

```bash
# Run tests
bazel test //clients/beachhead:test
```
