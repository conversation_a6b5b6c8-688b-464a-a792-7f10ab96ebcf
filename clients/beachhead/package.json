{"name": "@augment/beachhead", "private": true, "type": "module", "exports": {".": {"import": "./out/index.js", "require": "./out/index.cjs"}, "./cli": {"import": "./out/cli.js", "require": "./out/cli.cjs"}}, "scripts": {"cli:dev": "pnpm run build:dev && node --enable-source-maps out/augment_dev.mjs", "cli": "pnpm run build && node out/augment.mjs", "start": "pnpm run build-dev && node --enable-source-maps out/index.cjs", "build": "pnpm run clean && pnpm run esbuild-beachhead && pnpm run esbuild-cli", "build:dev": "pnpm run clean && DEVELOPMENT=true node esbuild.config.js", "clean": "rm -rf out", "esbuild-beachhead": "esbuild src/index.ts --bundle --platform=node --tsconfig=./tsconfig.json --outfile=./out/index.js", "esbuild-beachhead-dev": "esbuild src/index.ts --bundle --platform=node --tsconfig=./tsconfig.json --outfile=./out/index.js --sourcemap", "esbuild-cli": "node esbuild.config.js", "lint": "pnpm run eslint && pnpm run prettier", "eslint": "git ls-files -- . | xargs pre-commit run eslint --hook-stage=manual --files", "prettier": "git ls-files -- . | xargs pre-commit run prettier --hook-stage=manual --files", "test": "jest --config ./jest.config.cjs"}, "dependencies": {"@opentelemetry/api": "^1.9.0", "@opentelemetry/exporter-trace-otlp-http": "^0.202.0", "@opentelemetry/resources": "^2.0.1", "@opentelemetry/sdk-trace-base": "^2.0.1", "@opentelemetry/sdk-trace-node": "^2.0.1", "@opentelemetry/semantic-conventions": "^1.34.0", "@sentry/node": "^9.20.0", "chalk": "^5.4.1", "denque": "^2.1.0", "diff": "^7.0.0", "gradient-string": "^3.0.0", "hast-util-to-jsx-runtime": "^2.3.6", "highlight.js": "^11.9.0", "ink": "^6.0.1", "ink-spinner": "^5.0.0", "ink-text-input": "^6.0.0", "lowlight": "^3.3.0", "react": "19.1.0", "react-19": "npm:react@19.1.0", "react-devtools-core": "^4.28.5", "yargs": "^17.7.2"}, "devDependencies": {"@types/diff": "^7.0.1", "@types/express": "4.16", "@types/hast": "^3.0.4", "@types/ink-text-input": "^2.0.5", "@types/jest": "^29.5.11", "@types/lodash": "^4.17.16", "@types/node": "^22.13.9", "@types/react": "19.1.8", "@types/yargs": "^17.0.32", "esbuild": "^0.25.0", "express": "^4.21.2", "jest": "^29.7.0", "jest-cli": "^29.7.0", "jest-junit": "^15.0.0", "lodash": "^4.17.21", "monaco-editor": "^0.52.2", "ts-jest": "^29.1.2", "uuid": "^11.0.3", "vscode-uri": "^3.0.8", "winston": "^3.11.0", "zod": "^3.23.8"}}