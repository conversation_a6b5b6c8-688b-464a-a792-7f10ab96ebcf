load("@bazel_tools//tools/build_defs/repo:http.bzl", "http_file")

_CONTAINRED_VERSION = "2.1.3"
_CONTAINERD_SHA256 = "436cc160c33b37ec25b89fb5c72fc879ab2b3416df5d7af240c3e9c2f4065d3c"

_STARGZ_SNAPSHOTTER_VERSION = "0.17.0"
_STARGZ_SNAPSHOTTER_SHA256 = "e3cd9aed03a0fc82adc2484a3fe94381d21f52d998419e15ca019744d27e18b7"

_ORAS_VERSION = "1.2.3"
_ORAS_SHA256 = "b4efc97a91f471f323f193ea4b4d63d8ff443ca3aab514151a30751330852827"

def containerd_setup():
    http_file(
        name = "containerd",
        url = "https://github.com/containerd/containerd/releases/download/v{v}/containerd-{v}-linux-amd64.tar.gz".format(v = _CONTAINRED_VERSION),
        sha256 = _CONTAINERD_SHA256,
        downloaded_file_path = "containerd-linux-amd64.tar.gz",
    )
    http_file(
        name = "stargz-snapshotter",
        url = "https://github.com/containerd/stargz-snapshotter/releases/download/v{v}/stargz-snapshotter-v{v}-linux-amd64.tar.gz".format(v = _STARGZ_SNAPSHOTTER_VERSION),
        sha256 = _STARGZ_SNAPSHOTTER_SHA256,
        downloaded_file_path = "stargz-snapshotter-linux-amd64.tar.gz",
    )

    # It's hard to have nice things. `oras` is the only CLI that can easily push an OCI tar that doesn't have a docker compatible manifest.json (which breaks streaming).
    #  - skopeo can too, but is a pain to build because it has cgo deps on gpgme
    #  - regctl comes close but needs the oci-layout tar extracted to a dir
    #  - crane requires the docker compatible manifest.json (for tars, not for dirs)
    #  - ctr requires a running containerd
    http_file(
        name = "oras",
        url = "https://github.com/oras-project/oras/releases/download/v{v}/oras_{v}_linux_amd64.tar.gz".format(v = _ORAS_VERSION),
        sha256 = _ORAS_SHA256,
        downloaded_file_path = "oras_linux_amd64.tar.gz",
    )

def oci_streamable_image(name, src, fmt = "zstdchunked", out = None, visibility = None):
    native.genrule(
        name = name,
        srcs = [src],
        outs = [out or "%s.oci.%s.tar" % (name, fmt)],
        tags = ["manual", "no-sandbox"],
        cmd_bash = """
        declare -r FMT="{fmt}"
        declare -r CONTAINERD_BIN="$$(readlink -f "$(location //clients/beachhead/img:containerd)")"
        declare -r CTR_REMOTE_BIN="$$(readlink -f "$(location //clients/beachhead/img:ctr-remote)")"
        declare -r SRC_OCI="$$(readlink -f "$<")"
        declare -r IMPORT_BASE_NAME="bzl/tmp:orig"
        declare -r CONVERTED_NAME="bzl/tmp:latest"
        declare -r SRC_DIGEST="$$(
          "$(BSDTAR_BIN)" -xf "$$SRC_OCI" -O index.json | "$(JQ_BIN)" -r '.manifests[0].digest'
        )"
        declare -r IMPORT_NAME="$$IMPORT_BASE_NAME@$$SRC_DIGEST"

        declare -r tmpdir="$$(mktemp -d -t bzl.oci-streamable-image.XXXX)"

        cleanup() {{
            printf "Killing (TERM) containerd...\\n" 2>&1
            setsid sudo kill -TERM $$(jobs -p)

            printf "Cleaning up %s...\\n" "$$tmpdir" 2>&1
            sudo rm -fr "$$tmpdir"
        }}
        trap 'cleanup' EXIT

        run_containerd() {{
            setsid sudo "$$CONTAINERD_BIN" "$$@" &
        }}
        ctr-remote() {{
            "$$CTR_REMOTE_BIN" --address="$$tmpdir/containerd.sock" "$$@"
        }}

        declare -r containerd_cfg="$$tmpdir/containerd.toml"
        printf "Generating %s...\\n" "$$containerd_cfg"
        {{
            printf 'version = 2\\n'
            printf '\\n'
            printf 'root = "%s/root"\\n' "$$tmpdir"
            printf 'state = "%s/state"\\n' "$$tmpdir"
            printf '\\n'
            printf 'disabled_plugins = [\\n'
            printf ' "io.containerd.grpc.v1.cri",\\n'
            printf '  "io.containerd.grpc.v1.tasks",\\n'
            printf ']\\n'
            printf '\\n'
            printf '[grpc]\\n'
            printf '  address = "%s/containerd.sock"\\n' "$$tmpdir"
            printf '  uid = %d\\n' "$$(id -u)"
            printf '  gid = %d\\n' "$$(id -g)"
            printf '\\n'
            printf '[ttrpc]\\n'
            printf '  address = ""\\n'
            printf '  uid = %d\\n' "$$(id -u)"
            printf '  gid = %d\\n' "$$(id -g)"
        }} > "$$containerd_cfg"

        printf "Running containerd in %s...\\n" "$$tmpdir" 2>&1
        run_containerd --config "$$containerd_cfg"
        sleep 2s

        printf "Importing %s as %s...\\n" "$$SRC_OCI" "$$IMPORT_BASE_NAME" 2>&1
        ctr-remote image import --digests --local --base-name "$$IMPORT_BASE_NAME" "$$SRC_OCI"

        printf "Converting %s to %s...\\n" "$$IMPORT_NAME" "$$CONVERTED_NAME" 2>&1
        ctr-remote image convert "--$$FMT" --oci "$$IMPORT_NAME" "$$CONVERTED_NAME"

        printf "Exporting %s to %s...\\n"  "$$CONVERTED_NAME" "$@" 2>&1
        ctr-remote image export --skip-manifest-json "$@" "$$CONVERTED_NAME"
        """.format(
            fmt = fmt,
        ),
        tools = [
            "//clients/beachhead/img:ctr-remote",
            "//clients/beachhead/img:containerd",
        ],
        toolchains = [
            "@bsd_tar_toolchains//:resolved_toolchain",
            "@jq_toolchains//:resolved_toolchain",
        ],
        visibility = visibility,
    )
