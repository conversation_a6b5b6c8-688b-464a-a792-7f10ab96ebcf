load("deploy.bzl", "deploy", "retag")

################################################################################
#
# Deployments
#

deploy(
    name = "deploy-dev",
    args = [
        "--env=DEV",
        # No --tag. Use either --tag=dev-{user|team} or --tag=DEV on the command line.
    ],
    oci_innie = "//clients/beachhead/img/innie:innie_oci",
    oci_outie = "//clients/beachhead/img/outie:outie_oci",
    visibility = [
        "//services/test:__subpackages__",
    ],
)

deploy(
    name = "deploy-staging",
    args = [
        "--env=STAGING",
        # --tag can be provided on command line, defaults to STAGING if not specified
    ],
    oci_innie = "//clients/beachhead/img/innie:innie_oci",
    oci_outie = "//clients/beachhead/img/outie:outie_oci",
)

deploy(
    name = "rebuild-prod",
    args = [
        "--env=PROD",
        "--tag=PROD",  # Enforce, for now.
    ],
    oci_innie = "//clients/beachhead/img/innie:innie_oci",
    oci_outie = "//clients/beachhead/img/outie:outie_oci",
)

deploy(
    name = "deploy-prod",
    args = [
        "--env=PROD",
        # --tag can be provided on command line, defaults to PROD if not specified
    ],
    oci_innie = "//clients/beachhead/img/innie:innie_oci",
    oci_outie = "//clients/beachhead/img/outie:outie_oci",
)

################################################################################
#
# containerd
#

genrule(
    name = "containerd",
    srcs = ["@containerd//file"],
    outs = ["containerd"],
    cmd_bash = """
    $(BSDTAR_BIN) -C '$(@D)' -xzf '$<' --strip-components=1 "bin/$$(basename '$@')"
    """,
    executable = True,
    toolchains = ["@bsd_tar_toolchains//:resolved_toolchain"],
    visibility = ["//clients/beachhead/img:__subpackages__"],
)

genrule(
    name = "ctr-remote",
    srcs = ["@stargz-snapshotter//file"],
    outs = ["ctr-remote"],
    cmd_bash = """
    $(BSDTAR_BIN) -C '$(@D)' -xzf '$<' "$$(basename '$@')"
    """,
    executable = True,
    toolchains = ["@bsd_tar_toolchains//:resolved_toolchain"],
    visibility = ["//clients/beachhead/img:__subpackages__"],
)

genrule(
    name = "oras",
    srcs = ["@oras//file"],
    outs = ["oras"],
    cmd_bash = """
    $(BSDTAR_BIN) -C '$(@D)' -xzf '$<' "$$(basename '$@')"
    """,
    executable = True,
    toolchains = ["@bsd_tar_toolchains//:resolved_toolchain"],
    visibility = ["//clients/beachhead/img:__subpackages__"],
)
