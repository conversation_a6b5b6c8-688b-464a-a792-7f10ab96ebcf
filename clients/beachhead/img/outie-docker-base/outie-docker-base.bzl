#
# This file is auto-generated by rebuild.sh. DO NOT EDIT BY HAND.
#

load("@rules_oci//oci:pull.bzl", "oci_pull")

def docker_outie_base_setup():
    oci_pull(
        name = "remote-agents-outie-base",
        registry = "us-central1-docker.pkg.dev",
        repository = "system-services-dev/base-images/remote-agents-outie-base",
        digest = "sha256:589749dcac7630edd5ad575a4d8c41c9f8f97136b20e6b3797528a9e1d08551c",
    )
