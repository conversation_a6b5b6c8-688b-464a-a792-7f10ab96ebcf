load("@bazel_tools//tools/build_defs/repo:http.bzl", "http_file")

LINUX_LTS_VERSION = "6.12.29"
_LINUX_LTS_SHA256 = "e8b2ec7e2338ccb9c86de7154f6edcaadfce80907493c143e85a82776bb5064d"

def _linux_extension_impl(mctx):
    http_file(
        name = "linux-lts-source",
        url = "https://cdn.kernel.org/pub/linux/kernel/v6.x/linux-{v}.tar.xz".format(v = LINUX_LTS_VERSION),
        sha256 = _LINUX_LTS_SHA256,
        downloaded_file_path = "linux-lts-source.tar.xz",
    )

linux_extension = module_extension(implementation = _linux_extension_impl)

def vmlinuz(
        name,
        kconfig,
        linux_source = "@linux-lts-source//file:linux-lts-source.tar.xz",
        out = "vmlinuz",
        visibility = None,
        tags = None,
        config_local_version = "",

        # https://docs.kernel.org/kbuild/reproducible-builds.html
        kbuild_build_timestamp = "@1700000000",
        kbuild_build_user = "bazel",
        kbuild_build_host = "bazel"):
    """Build a vmlinuz (bzImage) kernel.

    NOTE(mattm): This rule is not a pure bazel rule. The linux source and kernel .config are
    well-defined as input dependencies, however this calls `make bzImage` and so requires os packages such as:
     - bison
     - flex
     - build-essential
     - libelf-dev
     - ...

    TODO(mattm): Add kernel version as an argument after figuring out the proper way to add to MODULE.bazel.

    Args:
      name: Name of the target.
      kconfig: Kernel .config file.
      linux_source: Linux source tarball.
      out: Output file name (defaults to 'vmlinuz').
      visibility: Visibility of the target.
      kbuild_build_timestamp: KBUILD_BUILD_TIMESTAMP environment variable.
      kbuild_build_user: KBUILD_BUILD_USER environment variable.
      kbuild_build_host: KBUILD_BUILD_HOST environment variable.
      config_local_version: CONFIG_LOCALVERSION kernel config variable.
    """
    native.genrule(
        name = name + "_config",
        srcs = [
            linux_source,
            kconfig,
        ],
        outs = [out + ".config"],
        visibility = visibility,
        tags = tags,
        cmd_bash = """
        declare -r src="$$(readlink -f "$(location {linux_source})")"
        declare -r cfg="$$(readlink -f "$(location {kconfig})")"
        declare -r out="$$(readlink -f "$@")"
        declare -r config_local_version="{config_local_version}"

        declare -r tmpdir="$$(mktemp -d -t bzl.linux-lts.XXXX)"
        trap 'rm -rf "$$tmpdir"' EXIT

        tar -C "$$tmpdir" -xf "$$src" --strip-components=1
        cp "$$cfg" "$$tmpdir"/.config

        if [[ "$$config_local_version" ]]; then
          "$$tmpdir/scripts/config" --file "$$tmpdir/.config" --set-str CONFIG_LOCALVERSION "$$config_local_version"
        fi

        export KBUILD_BUILD_TIMESTAMP="$$(date --utc --date='{kbuild_build_timestamp}')"
        export KBUILD_BUILD_USER="{kbuild_build_user}"
        export KBUILD_BUILD_HOST="{kbuild_build_host}"

        yes '' | make -C "$$tmpdir" oldconfig || true

        cp "$$tmpdir"/.config "$$out"
        """.format(
            kconfig = kconfig,
            linux_source = linux_source,
            kbuild_build_timestamp = kbuild_build_timestamp,
            kbuild_build_user = kbuild_build_user,
            kbuild_build_host = kbuild_build_host,
            config_local_version = config_local_version or "",
        ),
    )
    native.genrule(
        name = name,
        srcs = [
            linux_source,
            ":" + name + "_config",
        ],
        outs = [out],
        visibility = visibility,
        tags = tags,
        cmd_bash = """
        declare -r src="$$(readlink -f "$(location {linux_source})")"
        declare -r cfg="$$(readlink -f "$(location :{name}_config)")"
        declare -r out="$$(readlink -f "$@")"

        declare -r tmpdir="$$(mktemp -d -t bzl.linux-lts.XXXX)"
        trap 'rm -rf "$$tmpdir"' EXIT

        tar -C "$$tmpdir" -xf "$$src" --strip-components=1
        cp "$$cfg" "$$tmpdir"/.config

        export KBUILD_BUILD_TIMESTAMP="$$(date --utc --date='{kbuild_build_timestamp}')"
        export KBUILD_BUILD_USER="{kbuild_build_user}"
        export KBUILD_BUILD_HOST="{kbuild_build_host}"

        make -C "$$tmpdir" -j "$$(nproc)" bzImage

        cp "$$tmpdir"/arch/x86/boot/bzImage "$$out"
        """.format(
            name = name,
            linux_source = linux_source,
            kbuild_build_timestamp = kbuild_build_timestamp,
            kbuild_build_user = kbuild_build_user,
            kbuild_build_host = kbuild_build_host,
        ),
    )
