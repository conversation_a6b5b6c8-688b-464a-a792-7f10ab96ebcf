load("//clients/beachhead/img/innie-docker-base:innie-docker-base.bzl", "docker_innie_base_setup")
load("//clients/beachhead/img/outie-docker-base:outie-docker-base.bzl", "docker_outie_base_setup")
load("//clients/beachhead/img/outie:otel.bzl", "otel_cli")
load("//clients/beachhead/img:containerd.bzl", "containerd_setup")

def remote_agents_base_images_setup():
    docker_innie_base_setup()
    docker_outie_base_setup()
    otel_cli()
    containerd_setup()
