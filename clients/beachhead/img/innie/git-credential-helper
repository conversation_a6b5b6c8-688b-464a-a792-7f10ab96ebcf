#!/bin/bash

set -e

main() {
	declare -r cfg="$1"
	declare -r cmd="${2}"
	declare -r input_spec="$(</dev/stdin)"

	if [[ "$cmd" != "get" ]]; then
		# Silently ignore
		return 0
	fi

	declare -r username="$(cat "$cfg" | jq '.github_username // "token"' -r)"
	declare -r token="$(cat "$cfg" | jq '.github_user_token // ""' -r)"

	if [[ ! "$token" ]]; then
		# Silently ignore
		return 0
	fi

	declare -A spec
	while IFS='=' read -r key val; do
		spec["$key"]="$val"
	done <<<"$input_spec"

	if [[ "${spec[protocol]}" != "https" || "${spec[host]}" != "github.com" ]]; then
		printf "Ignoring %s://%s, only https://github.com supported.\n" "${spec[protocol]}" "${spec[host]}" >&2
		return 0
	fi

	printf "%s\n" "$input_spec"
	printf "username=%s\npassword=%s\n" "$username" "$token"
}

main "$@"
