# Augment Remote Agent - Image Build and Deploy

This directory contains the Dockerfile and a deployment tool for the
remote-agent image. The inputs, config, and tooling are all handled in bazel, but docker
is not.

## Image

The image is an `ubuntu:22.04` base image with some packages like `git`
installed, and the remote-agent binary copied in. An `init.sh` script handles
the listening port, persistent storage, and mounted secrets (including the
agent config).

## Deploy

There is one build target for each environment. This enforces the mapping from environment to cluster, and then
provides the cluster-specific metadata needed to format a container image name.

**Dev:**

If you have configured your `dev_deploy` to use a dedicated image:

```
$ bazel run //clients/beachhead/img:deploy-dev -- push --tag=dev-${AUG_USER?}

Image Name: us-central1-docker.pkg.dev/augment-research-gsc/agents-dev-us-central1/augment-remote-agent:dev-${AUG_USER}
```

If you have configured your `dev_deploy` to use the shared `DEV` image.

```
$ bazel run //clients/beachhead/img:deploy-dev -- push --tag=DEV

Image Name: us-central1-docker.pkg.dev/augment-research-gsc/agents-dev-us-central1/augment-remote-agent:DEV
```

**E2E Testing**

Using either dev tag above, this will set the _${AUGMENT_E2E_TESTING}_ environment variable in your dev pod, which will
push beachhead logs directly to GCP.

```
$ bazel run //clients/beachhead/img:deploy-dev --//clients/beachhead/img/outie:e2e_testing=true -- push --tag=$tag
```

You can pull beachhead logs from these tests with
```
cloud logging read  --project=augment-research-gsc \
  'jsonPayload.component="<EMAIL>" AND jsonPayload.augment_e2e_testing="1" AND timestamp>="2025-05-16T22:00:00"' \
  --limit 10 --format=json | jq '.[].jsonPayload.MESSAGE'
```

See: https://console.cloud.google.com/artifacts/docker/augment-research-gsc/us-central1/agents-dev-us-central1

**Staging:**

```
$ bazel run //clients/beachhead/img:deploy-staging -- push

Image Name: us-central1-docker.pkg.dev/agent-sandbox-prod/agents-staging-us-central1/augment-remote-agent:STAGING
```

See: https://console.cloud.google.com/artifacts/docker/agent-sandbox-prod/us-central1/agents-staging-us-central1

**Prod Copy (e.g., STAGING to PROD-TESTING):**

```
$ bazel run //clients/beachhead/img:deploy-prod -- copy --tag=PROD-TESTING --source-tag=STAGING

Copying from STAGING registry to PROD registry with new tag
```

See: https://console.cloud.google.com/artifacts/docker/agent-sandbox-prod/us-central1/agents-prod-us-central1

**Prod:**
*It is preferable to do a copy, to promote from staging*

```
$ bazel run //clients/beachhead/img:deploy-prod -- push

Image Name: us-central1-docker.pkg.dev/agent-sandbox-prod/agents-prod-us-central1/augment-remote-agent:PROD
```
