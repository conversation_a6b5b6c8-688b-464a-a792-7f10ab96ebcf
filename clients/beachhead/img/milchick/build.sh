#!/bin/bash

set -euo pipefail

cleanup() {
	if mountpoint -q "$dir"; then
		printf "CLEANUP: Unmounting (recursive) %s...\n" "$dir"
		sudo umount --recursive "$dir"
	fi
}

main() {
	declare -r name="${1:-image}"
	declare -rg dir="$name.dir"
	declare -r img="$name.btrfs"
	declare -r pkg_snapshot="20250729T000000Z"
	declare -r innie_img_tgt="//clients/beachhead/img/innie:innie_oci_tar"
	declare -r vmlinuz_tgt="//clients/beachhead/img/kernel:vmlinuz"

	if [[ "$name" == '--vm' ]]; then
		shift
		in_vm "$@"
		rm -f "$0" # remove self from image
		return
	fi

	printf "$(tput bold)Collecting innie info...$(tput sgr0)\n"
	# TODO(mattm): hardcode this for now since the cquery takes a few seconds
	# declare -r innie_img_tar="$(bazel info execution_root)/$(bazel cquery --output=files "$innie_img_tgt")"
	declare -r innie_img_tar="$(bazel info execution_root)/bazel-out/k8-fastbuild/bin/clients/beachhead/img/innie/innie.oci.tar"

	if [[ "$name" == '--chroot' ]]; then
		in_chroot
		return
	fi
	if [[ "$name" == '--run' ]]; then
		shift
		run_qemu "image.btrfs" "$@"
		return
	fi

	if (($(id -u) == 0)); then
		printf "This script is not meant to run directly as root.\n" >&2
		return 1
	fi

	trap cleanup EXIT

	if [[ ! -e "$dir" ]]; then
		mkdir -p "$dir"
		declare -ra pkgs=(
			apt
			bind9-dnsutils
			btrfs-progs
			curl
			# docker.io
			gnupg
			inotify-tools
			iproute2
			iputils-ping
			jq
			nano
			openssh-server
			rsync
			socat
			systemd-container
			systemd-journal-remote
			tree
			unzip
			vim
			zstd
		)
		declare -ra debbs=(
			debootstrap
			--components='main,universe,multiverse'
			--include="$(
				local IFS=,
				echo "${pkgs[@]}"
			)"
			--cache-dir=/var/cache/apt
			'noble' "$dir" "https://snapshot.ubuntu.com/ubuntu/$pkg_snapshot"
		)
		sudo "${debbs[@]}"
	fi

	if [[ -e "$dir" ]]; then
		printf "$(tput bold)Building innie ...$(tput sgr0)\n"
		bazel build "$innie_img_tgt"

		# Self bind makes recursive umount easier.
		sudo mount --bind "$dir" "$dir"

		sudo cp "$0" "$dir/root/" # This script is used again in the chroot and again in the vm.
		sudo mkdir -p "$dir/root/src"

		sudo mount -o bind,ro "$(dirname "$0")/src" "$dir/root/src"
		sudo mount -o bind,ro /proc "$dir/proc"
		sudo mount -o bind,ro /sys "$dir/sys"

		sudo chroot "$dir" "/root/$(basename "$0")" --chroot

		cleanup
		sudo rmdir "$dir/root/src"
	fi

	if [[ -e "$img" ]]; then
		rm "$img"
	fi
	if [[ ! -e "$img" ]]; then
		truncate -s 5G "$img"
		sudo mkfs.btrfs -L vm-root -r "$dir" "$img"
	fi

	run_qemu "$img" "provision-vm-and-innie.target"
}

in_chroot() {
	declare -r src="$(dirname "$0")/src"

	################################################################################
	# root fs

	# Use systemd-remount-fs to add noatime, which isn't working on the kernel command line
	printf "\n%s %s %s %s 0 0\n" "LABEL=vm-root" "/" "btrfs" "noatime,compress=zstd:6" >>/etc/fstab
	ln -s /usr/lib/systemd/system/systemd-growfs-root.service /etc/systemd/system/multi-user.target.wants/systemd-growfs-root.service

	################################################################################
	# filesystem

	mkdir -p /etc/systemd/system/local-fs.target.wants/
	mkdir -p /etc/systemd/system/multi-user.target.wants/
	mkdir -p /etc/systemd/system/network.target.wants/
	mkdir -p /etc/systemd/journald.conf.d/

	################################################################################
	# first-boot (hostname, rootpw, ...)

	echo 'augment-agent' >/etc/hostname
	passwd -d root
	systemctl <NAME_EMAIL> console-setup.service getty.target

	install -oroot -groot -m0644 "$src/bash_history" /root/.bash_history
	printf "alias sd=systemctl\n" >>/root/.bashrc
	printf "alias log=journalctl\n" >>/root/.bashrc

	systemctl set-default multi-user.target

	################################################################################
	# network

	systemctl enable systemd-networkd
	# It takes ~13s for eth0 to be ready, but we don't need it for docker load.
	systemctl disable systemd-networkd-wait-online
	systemctl disable networkd-dispatcher.service

	install -oroot -groot -m0644 "$src/10-augment-vm.network" /etc/systemd/network/
	install -oroot -groot -m0644 "$src/hosts" /etc/

	################################################################################
	# ssh

	install -oroot -groot -m0644 "$src/00-augment.sshd.conf" /etc/ssh/sshd_config.d/
	systemctl enable ssh.service

	################################################################################
	# docker

	curl -fsSL https://download.docker.com/linux/ubuntu/gpg -o /etc/apt/keyrings/docker.asc
	echo "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.asc] https://download.docker.com/linux/ubuntu $(. /etc/os-release && echo "$VERSION_CODENAME") stable" >/etc/apt/sources.list.d/docker.list
	apt-get update
	apt-get install -qqy docker-ce docker-ce-cli containerd.io fuse

	install -oroot -groot -m0644 "$src/docker-daemon.json" /etc/docker/daemon.json
	update-alternatives --set iptables /usr/sbin/iptables-legacy

	install -oroot -groot -m0644 "$src/docker-iptables-lockdown.service" /etc/systemd/system/
	systemctl enable docker-iptables-lockdown.service

	################################################################################
	# systemd-journal

	# This listens on port 19531 by default. (It can listen on a vsock directly as well).
	systemctl enable systemd-journal-gatewayd.{service,socket}
	systemctl enable systemd-journal-upload.service

	mkdir -p /etc/systemd/system/systemd-journal-upload.service.d/
	install -oroot -groot -m0644 "$src/systemd-journal-upload-destination.conf" -D /etc/systemd/system/systemd-journal-upload.service.d/destination.conf
	systemctl disable rsyslog.service

	################################################################################
	# /tmp tmpfs

	ln -sf ../../../usr/share/systemd/tmp.mount /etc/systemd/system/tmp.mount
	systemctl enable tmp.mount

	################################################################################
	# /mnt/persist

	install -oroot -groot -m0644 "$src/mnt-persist.mount" /etc/systemd/system/
	systemctl enable mnt-persist.mount

	################################################################################
	# kubectl proxy

	declare -r KUBECTL_VERSION=v1.32
	if [[ ! -e /etc/apt/keyrings/kubernetes-apt-keyring.gpg ]]; then
		mkdir -p /etc/apt/keyrings
		curl -fsSL https://pkgs.k8s.io/core:/stable:/"$KUBECTL_VERSION"/deb/Release.key | gpg --dearmor -o /etc/apt/keyrings/kubernetes-apt-keyring.gpg
	fi
	if [[ ! -e /etc/apt/sources.list.d/kubernetes.list ]]; then
		echo "deb [signed-by=/etc/apt/keyrings/kubernetes-apt-keyring.gpg] https://pkgs.k8s.io/core:/stable:/$KUBECTL_VERSION/deb/ /" >/etc/apt/sources.list.d/kubernetes.list
		DEBIAN_FRONTEND=noninteractive apt-get update && apt-get install -y kubectl && apt-get clean
	fi

	install -oroot -groot -m0644 "$src/kubectl-proxy.service" /etc/systemd/system/
	systemctl enable kubectl-proxy.service

	################################################################################
	# provisioning target

	install -oroot -groot -m0644 "$src/provision-vm-and-innie.target" /etc/systemd/system/
	install -oroot -groot -m0644 "$src/provision-vm-and-innie.service" /etc/systemd/system/

	################################################################################
	# innie container

	install -oroot -groot -m0755 "$src/docker-innie-starter.sh" /root/
	install -oroot -groot -m0644 "$src/docker-innie-starter.service" /etc/systemd/system/
	install -oroot -groot -m0644 "$src/<EMAIL>" /etc/systemd/system/
	install -oroot -groot -m0644 "$src/root-virtiofs.mount" /etc/systemd/system/
}

in_vm() {
	declare -r img="/mnt/innie.oci.tar"
	declare -r img_name="augment-remote-agent:build"
	declare -r tmp_name="tmp-build"

	printf "Mounting 9p virtiofs.\n"
	mount -t 9p -o trans=virtio innie0 /mnt

	printf "Loading: %s.\n" "$img"
	docker load -i "$img"

	printf "Starting: %s container.\n" "$tmp_name"
	docker run --detach --privileged --name "$tmp_name" -p 12375:2375 --entrypoint=/bin/bash "$img_name" -c 'sleep inf'
	docker exec -u0 "$tmp_name" /usr/bin/update-alternatives --set iptables /usr/sbin/iptables-legacy
	sleep 1
	printf "Running: %s containerd.\n" "$tmp_name"
	docker exec --detach --privileged -u0 "$tmp_name" containerd
	sleep 1
	printf "Running: %s dockerd.\n" "$tmp_name"
	docker exec --detach --privileged -u0 "$tmp_name" dockerd --containerd=/run/containerd/containerd.sock -H tcp://0.0.0.0:2375

	declare -i attempts=0
	declare -i sleep=1
	set +e
	while ! docker -H tcp://localhost:12375 images; do
		((attempts++))
		((attempts > 60)) && sleep=30
		((attempts > 600)) && sleep=300
		printf "Waiting for docker before loading images (sleep %s)...\n" "$sleep" >&2
		sleep "$sleep"
	done
	set -e

	printf "Loading: %s.\n" "$img"
	docker -H tcp://localhost:12375 load -i "$img"

	printf "Unmounting 9p virtiofs.\n"
	umount /mnt

	printf "Stopping: %s container.\n" "$tmp_name"
	docker stop "$tmp_name"

	printf "Snapshotting image %s from %s.\n" "$img_name" "$tmp_name"
	docker commit "$tmp_name" "$img_name"

	printf "Removing %s container.\n" "$tmp_name"
	docker rm "$tmp_name"

	printf "Docker system prune.\n"
	docker system prune --force

	declare -ri rounds=2
	declare -i r
	for ((r = 1; r <= rounds; r++)); do
		printf "Defraging, Recompressing, and Trimming btrfs (round %d of %d)...\n" "$r" "$rounds"
		btrfs filesystem defrag -czstd -r /
		fstrim -v /
		sync
	done

	printf "Enabling docker-innie-starter.service.\n"
	systemctl enable docker-innie-starter.service <EMAIL>
	systemctl enable root-virtiofs.mount
}

run_qemu() {
	declare -r img="$1"
	declare -r systemd_target="${2:-multi-user.target}"
	declare -r br="br0"
	declare -ri cpus=4
	declare -r ram=4g

	bazel build "$vmlinuz_tgt"
	declare -r vmlinuz="$(bazel info execution_root)/bazel-out/k8-fastbuild/bin/clients/beachhead/img/kernel/vmlinuz"

	declare -a cmd=(
		qemu-system-x86_64
		-enable-kvm

		-M microvm,x-option-roms=off,pit=off,pic=off,isa-serial=off,rtc=off
		-nodefaults
		-no-user-config -nographic -display none
		-cpu host -smp "$cpus" -m "$ram"

		-kernel "$vmlinuz"
		-append "console=hvc0 root=/dev/vda fsck.mode=skip rootflags=compress=zstd:6 rw systemd.unit=$systemd_target"

		-chardev stdio,id=virtiocon0
		-device virtio-serial-device
		-device virtconsole,chardev=virtiocon0

		-drive id=root0,file="$img",format=raw,if=none,discard=on
		-device virtio-blk-device,drive=root0

		-fsdev local,path="$(dirname "$innie_img_tar")",security_model=passthrough,id=innie0
		-device virtio-9p-device,fsdev=innie0,mount_tag=innie0
	)

	if [[ "$(ip -o l show "$br" type bridge 2>/dev/null)" ]]; then
		cmd+=(
			-netdev bridge,br="$br",id=net0
			-device virtio-net-device,netdev=net0
		)
	else
		cmd+=(-net none)
	fi

	printf "$(tput bold)%s$(tput sgr0)\n" "${cmd[*]}"
	sudo "${cmd[@]}"
}

time main "$@"
