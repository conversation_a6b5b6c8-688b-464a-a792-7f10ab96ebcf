def debootstrap(
        name,
        release,
        mirror,
        size,
        out = None,
        components = None,
        packages = None,
        cache_dir = "/var/cache/apt",
        tars = None,
        fs = "btrfs",
        label = None,
        visibility = None,
        tags = None):
    """Build a debootstrap filesystem.

    This rule builds a raw file containing a filesystem. It makes use of root (sudo)
    and the bazel "no-sandbox" tag because that's the easiest way to get file permissions
    and ownership correct (in theory fakechroot fakeroot would work too, but had issues
    with bazel).

    There are three phases:
      1. debootstrap into a temporary directory.
      2. [optional] extract additional TAR files into the filesystem.
      3. mkfs.{fs} the output raw filesystem.

    Args:
      name: Name for target.
      out: Output filename, defaults to {name}.{fs}.raw
      visibility: Visibility for target.

    Debootstrap Args:
      release: The debootsrap 'suite' flag, e.g. "jammy".
      mirror: The debootstrap mirror, should be a snapshot mirror e.g., https://snapshot.ubuntu.com/ubuntu/20250509T000000Z
      components: The debootstrap --components flag.
      packages: The debootstrap --include flag.
      cache_dir: The debootstrap --cache-dir flag, defaults to /var/cache/apt.

    Tar Args:
      tars: An optional list of tar files to extract after debootstrap.

    Filesystem Args:
      size: A size to pass to `truncate -s` to set the filesize of the raw file. E.g. "1G".
      fs: The filesystem to use, defaults to "btrfs".
      label: An optional filesystem label when creating the filesystem.
    """
    if out == None:
        out = "%s.%s.raw" % (name, fs)
    native.genrule(
        name = name,
        outs = [out],
        srcs = tars,
        visibility = visibility,
        cmd_bash = """
        set -euo pipefail

        declare -r root="$$(mktemp -d -p /tmp -t bzl.debootstrap.XXXX)"
        trap 'sudo rm -rf "$$root"' EXIT

        ### Debootstrap

        declare -a cmd=(debootstrap)
        [[ "{cache_dir}" ]] && cmd+=(--cache-dir="{cache_dir}")
        [[ "{components}" ]] && cmd+=(--components="{components}")
        [[ "{packages}" ]] && cmd+=(--include="{packages}")
        cmd+=("{release}" "$$root" "{mirror}")

        sudo "$${{cmd[@]}}"

        ### Tars

        declare -ra tars=({tars})
        for tar in "$${{tars[@]}}"; do
            sudo tar -C "$$root" -xf "$$tar"
        done

        ### Filesystem

        rm -f '$@'
        truncate -s '{size}' '$@'

        declare -a mkfs=(mkfs.{fs} -r "$$root" '$@')
        [[ "{label}" ]] && mkfs+=(-L "{label}")
        sudo "$${{mkfs[@]}}"

        echo -n 'Sparse size: ' && du -h '$@'
        """.format(
            name = name,
            components = ",".join(components or []),
            packages = ",".join(packages or []),
            mirror = mirror,
            release = release,
            cache_dir = cache_dir or "",
            tars = " ".join(["$(location %s)" % t for t in tars or []]),
            fs = fs,
            label = label or "",
            size = size,
        ),
        tags = ["no-sandbox"] + (tags or []),
    )
