#!/bin/bash

set -euo pipefail
cd "$(dirname "$0")"

main() {
	declare -r image="$1"
	declare -r namespace="dev-mattm"
	declare -r tag="$(date +'%Y%m%d-%H%M%S')"
	declare -r name="remote-agent-vm-root-$tag"
	declare -r vmdk="$name.vmdk"
	declare -r gcs="gs://gcp-us1-user/mattm/remote-agent-vm-root-import/$vmdk"
	declare -r project="augment-research-gsc"
	declare -r location="us-central1"
	declare -r kubectx="gke_augment-research-gsc_us-central1_gcp-agent0"

	trap "run rm -f '$vmdk'" EXIT

	# gcp says vmdk imports are fastest: https://cloud.google.com/compute/docs/import/importing-virtual-disks#supported-formats
	run qemu-img convert -O vmdk "$image" "$vmdk"

	run gcloud storage --project="$project" cp "$vmdk" "$gcs"
	run gcloud migration vms image-imports create "$name" \
		--project="$project" \
		--location="$location" \
		--source-file="$gcs" \
		--image-name="$name" \
		--skip-os-adaptation \
		--single-region-storage

	declare prev_state=""
	while true; do
		state="$(gcloud migration vms image-imports describe --location="$location" "$name" --format=json | jq '.recentImageImportJobs[0].state' -r)"
		if [[ "$state" != "$prev_state" ]]; then
			printf '\nImport Import State [%s]: %s' "$name" "$state"
		else
			printf '.'
		fi
		[[ "$state" == "SUCCEEDED" ]] && echo && break
		[[ "$state" == "FAILED" ]] && echo && return 1
		prev_state="$state"
		sleep 1s
	done

	run gcloud compute disks create --project="$project" "$name" \
		--type=pd-balanced \
		--size=10GB \
		--zone="$location-a" \
		--image="$name" \
		--image-project="$project"

	run gcloud compute snapshots create --project="$project" "$name" \
		--source-disk="$name" \
		--source-disk-zone="$location-a" \
		--storage-location="$location"

	run kubectl --context="$kubectx" --namespace="$namespace" apply -f <(printf '%s' "$VS_TEMPLATE" | sed -e "s/@NAMESPACE@/$namespace/g" -e "s/@NAME@/$name/g" -e "s/@PROJECT@/$project/g")
	run kubectl --context="$kubectx" --namespace="$namespace" describe volumesnapshot "$name"
	run kubectl --context="$kubectx" describe volumesnapshotcontent "$name"
}

run() {
	declare -ra cmd=("$@")
	printf "Running: $(
		tput bold
		tput setaf 4
	)%s$(tput sgr0)\n" "${cmd[*]}" >&2
	time "${cmd[@]}"
}

VS_TEMPLATE='---
apiVersion: snapshot.storage.k8s.io/v1
kind: VolumeSnapshot
metadata:
  name: @NAME@
  namespace: @NAMESPACE@
spec:
  volumeSnapshotClassName: pd-image
  source:
    volumeSnapshotContentName: @NAME@
---
apiVersion: snapshot.storage.k8s.io/v1
kind: VolumeSnapshotContent
metadata:
  name: @NAME@
spec:
  deletionPolicy: Retain
  driver: pd.csi.storage.gke.io
  source:
    snapshotHandle: projects/@PROJECT@/global/snapshots/@NAME@
  volumeSnapshotRef:
    kind: VolumeSnapshot
    name: @NAME@
    namespace: @NAMESPACE@
'

main "$@"
