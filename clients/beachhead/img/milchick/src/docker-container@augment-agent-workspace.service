[Unit]
Description=Augment Agent Workspace Container (augment-agent-workspace)
Requires=docker.service docker-innie-starter.service
After=docker.service docker-innie-starter.service

RequiresMountsFor=/root/virtiofs

[Service]
ExecStart=/root/docker-innie-starter.sh run
Restart=no # beachhead restarts are handled in init.sh. The container and unit persist for debugging.
RemainAfterExit=yes

[Install]
WantedBy=multi-user.target
