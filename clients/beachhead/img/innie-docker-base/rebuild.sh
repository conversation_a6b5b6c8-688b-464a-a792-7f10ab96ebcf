#!/bin/bash

set -euo pipefail

main() {
	declare -r dir="$(dirname "$0")"
	declare -r tag="$(date +'%Y%m%d')"
	declare -r registry="us-central1-docker.pkg.dev"
	declare -r registry_path="system-services-dev/base-images"
	declare -r name="remote-agents-innie-base"
	declare -r img="$registry/$registry_path/$name:$tag"
	declare -r oci=/tmp/"$name.$tag.oci-layout"
	declare -r bzl="$dir/innie-docker-base.bzl"

	docker buildx inspect container &> /dev/null || \
	run docker buildx create --driver=docker-container --name=container

	run docker buildx build --builder=container --output type=oci,tar=false,dest="$oci" -t "$img" "$dir"

	declare -r digest="$(<"$oci/index.json" jq '.manifests[0].digest' -r)"
	printf "$(tput bold; tput setaf 2)Built %s@%s$(tput sgr0)\n" "$img" "$digest" >&2

	cat > "$bzl" <<-EOF
	#
	# This file is auto-generated by rebuild.sh. DO NOT EDIT BY HAND.
	#

	load("@rules_oci//oci:pull.bzl", "oci_pull")

	def docker_innie_base_setup():
	    oci_pull(
	        name = "$name",
	        registry = "$registry",
	        repository = "$registry_path/$name",
	        digest = "$digest",
	    )
	EOF

	git diff "$bzl"

	run crane push "$oci" "$img"
	run rm -fr "$oci"
}

run() {
	declare -ra cmd=("$@")
	printf "Running: $(tput bold; tput setaf 4)%s$(tput sgr0)\n" "${cmd[*]}" >&2
	"${cmd[@]}"
}

get_digest() {
	declare -r img="$1"
	docker inspect --format='{{index .RepoDigests 0}}' "$img" | cut -d'@' -f2
}

crane() {
	bazel run @com_github_google_go_containerregistry//cmd/crane -- "$@"
}

main "$@"
