package main

import (
	"context"
	"fmt"
	"time"

	request_insight_proto "github.com/augmentcode/augment/services/request_insight/proto"
	request_insight_publisher "github.com/augmentcode/augment/services/request_insight/publisher"
	"google.golang.org/protobuf/types/known/timestamppb"
)

// PubSubClient is an interface for publishing to pub/sub
type PubSubClient interface {
	PublishRemoteAgentLog(ctx context.Context, entries []LogEntry, startTime, endTime time.Time) error
	Close() error
}

// RequestInsightPubSubClient is a client for publishing messages to GCP pub/sub using the request_insight_publisher
type RequestInsightPubSubClient struct {
	publisher  request_insight_publisher.RequestInsightPublisher
	sessionID  string
	tenantID   string
	tenantName string
	projectID  string
	topicName  string
}

// NewPubSubClient creates a new request insight pub/sub client
func NewPubSubClient(ctx context.Context, projectID, topicName string) (PubSubClient, error) {
	// Create a request insight publisher
	pub, err := request_insight_publisher.NewRequestInsightPublisher(ctx, projectID, topicName)
	if err != nil {
		return nil, fmt.Errorf("failed to create request insight publisher: %w", err)
	}

	return &RequestInsightPubSubClient{
		publisher: pub,
		// The sessionID will be set later by the log processor
		sessionID: "",
		projectID: projectID,
		topicName: topicName,
	}, nil
}

// SetSessionID sets the session ID for the client
func (c *RequestInsightPubSubClient) SetSessionID(sessionID string) {
	c.sessionID = sessionID
}

// SetTenantInfo sets the tenant ID and name for the client
func (c *RequestInsightPubSubClient) SetTenantInfo(tenantID, tenantName string) {
	c.tenantID = tenantID
	c.tenantName = tenantName
}

// Close closes the request insight publisher
func (c *RequestInsightPubSubClient) Close() error {
	return c.publisher.Close()
}

// PublishRemoteAgentLog publishes a remote agent log event to pub/sub
func (c *RequestInsightPubSubClient) PublishRemoteAgentLog(ctx context.Context, entries []LogEntry, startTime, endTime time.Time) error {
	// Create a session event
	sessionEvent := request_insight_publisher.NewSessionEvent()

	// Convert log entries to proto format
	protoEntries := make([]*request_insight_proto.RemoteAgentLogEntry, len(entries))
	for i, entry := range entries {
		protoEntries[i] = &request_insight_proto.RemoteAgentLogEntry{
			Message:   entry.Message,
			Transport: entry.Transport,
			Timestamp: timestamppb.New(time.Now()), // Default timestamp
		}
		// Try to parse the raw timestamp if available
		if entry.RawTimestamp != "" {
			if parsedTime, err := time.Parse(time.RFC3339, entry.RawTimestamp); err == nil {
				protoEntries[i].Timestamp = timestamppb.New(parsedTime)
			}
		}
	}

	// Create a remote agent log event
	customEvent := &request_insight_proto.SessionEvent_RemoteAgentLog{
		RemoteAgentLog: &request_insight_proto.RemoteAgentLog{
			Entries:        protoEntries,
			RemoteAgentId:  c.sessionID,
			Component:      "ra-ri-client",
			StartTimestamp: timestamppb.New(startTime),
			EndTimestamp:   timestamppb.New(endTime),
		},
	}

	// Set the event
	sessionEvent.Event = customEvent

	// Create tenant info using the provided tenant ID and name
	tenantInfo := &request_insight_proto.TenantInfo{
		TenantId:   c.tenantID,
		TenantName: c.tenantName,
	}

	// Publish the event - passing nil for opaqueUserID since we don't have one
	return c.publisher.PublishSessionEvent(ctx, c.sessionID, nil, tenantInfo, sessionEvent)
}
