package main

import (
	"context"
	"strings"
	"testing"
	"time"
)

// MockPubSubClient is a mock implementation of PubSubClient for testing
type MockPubSubClient struct {
	PublishedLogs       [][]LogEntry
	SessionID           string
	TenantID            string
	TenantName          string
	PublishError        error // To simulate errors during publishing
	SetSessionIDCalled  bool
	SetTenantInfoCalled bool
}

func (m *MockPubSubClient) PublishRemoteAgentLog(ctx context.Context, entries []LogEntry, firstEntryTime, lastEntryTime time.Time) error {
	if m.PublishError != nil {
		return m.PublishError
	}
	m.PublishedLogs = append(m.PublishedLogs, entries)
	return nil
}

func (m *MockPubSubClient) SetSessionID(sessionID string) {
	m.SessionID = sessionID
	m.SetSessionIDCalled = true
}

func (m *MockPubSubClient) SetTenantInfo(tenantID, tenantName string) {
	m.TenantID = tenantID
	m.TenantName = tenantName
	m.SetTenantInfoCalled = true
}

func (m *MockPubSubClient) Close() error {
	return nil
}

func TestMessageJoining(t *testing.T) {
	// Test that messages are joined with newlines
	messages := []string{"message 1", "message 2", "message 3"}
	joined := strings.Join(messages, "\n")

	expected := "message 1\nmessage 2\nmessage 3"
	if joined != expected {
		t.Fatalf("Expected joined message to be '%s', got '%s'", expected, joined)
	}
}
