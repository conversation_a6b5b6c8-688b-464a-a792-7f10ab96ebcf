package main

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"net/url"

	"github.com/rs/zerolog/log"
)

// A simple web server proxy that forwards requests to the OTLP endpoint
type OtelProxy struct {
	listenPort              int
	destinationOtlpEndpoint string

	server *http.Server
}

func NewOtelProxy(listenPort int, destinationOtlpEndpoint string) *OtelProxy {
	return &OtelProxy{
		listenPort:              listenPort,
		destinationOtlpEndpoint: destinationOtlpEndpoint,

		server: &http.Server{
			Addr: fmt.Sprintf(":%d", listenPort),
		},
	}
}

func (p *OtelProxy) Run() error {
	log.Info().
		Int("port", p.listenPort).
		Str("destination", p.destinationOtlpEndpoint).
		Msg("Starting proxy server")

	p.server.Handler = http.HandlerFunc(p.proxyHandler)
	err := p.server.ListenAndServe()

	// ServerClosed is expected when we call Shutdown
	if err != nil && err != http.ErrServerClosed {
		return err
	}
	return nil
}

func (p *OtelProxy) Shutdown(ctx context.Context) error {
	return p.server.Shutdown(ctx)
}

func (p *OtelProxy) proxyHandler(w http.ResponseWriter, r *http.Request) {
	log.Info().Msgf("Received request: %s %s", r.Method, r.URL.Path)

	if p.destinationOtlpEndpoint == "" {
		log.Warn().Msg("No destination endpoint configured")
		w.WriteHeader(http.StatusOK)
		return
	}

	// Forward the request to the OTLP endpoint, copy headers from the incoming req
	endpoint, err := url.JoinPath(p.destinationOtlpEndpoint, r.URL.Path)
	if err != nil {
		log.Error().Err(err).Msg("Failed to join path")
		w.WriteHeader(http.StatusInternalServerError)
		return
	}

	req, err := http.NewRequestWithContext(r.Context(), r.Method, endpoint, r.Body)
	if err != nil {
		log.Error().Err(err).Msg("Failed to create request")
		w.WriteHeader(http.StatusInternalServerError)
		return
	}
	for k, v := range r.Header {
		req.Header[k] = v
	}

	// Send the request
	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		log.Error().Err(err).Msg("Failed to send request")
		w.WriteHeader(http.StatusInternalServerError)
		return
	}
	defer resp.Body.Close()

	// Copy the response
	for k, v := range resp.Header {
		w.Header()[k] = v
	}
	w.WriteHeader(resp.StatusCode)
	_, err = io.Copy(w, resp.Body)
	if err != nil {
		log.Error().Err(err).Msg("Failed to write response")
		return
	}
}
