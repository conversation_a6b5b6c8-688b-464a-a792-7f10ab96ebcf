load("@bazel_tools//tools/build_defs/repo:http.bzl", "http_file")

_OTEL_CLI_VERSION = "0.4.5"
_OTEL_CLI_SHA256 = "2f192fadfb2107a92ae617ca93fd7c0b532fa618a5ebc3917e641c6a9fbaeb45"

_OTELCOL_CONTRIB_VERSION = "0.128.0"
_OTELCOL_CONTRIB_SHA256 = "09b1332e29968bacdb7ce564073302ef9567c71919842544b4382f0f15456fd6"

def otel_cli():
    http_file(
        name = "otel-cli",
        url = "https://github.com/equinix-labs/otel-cli/releases/download/v{v}/otel-cli_{v}_linux_amd64.tar.gz".format(v = _OTEL_CLI_VERSION),
        sha256 = _OTEL_CLI_SHA256,
        downloaded_file_path = "otel-cli_linux_amd64.tar.gz",
    )
    http_file(
        name = "otelcol-contrib",
        url = "https://github.com/open-telemetry/opentelemetry-collector-releases/releases/download/v{v}/otelcol-contrib_{v}_linux_amd64.tar.gz".format(v = _OTELCOL_CONTRIB_VERSION),
        sha256 = _OTELCOL_CONTRIB_SHA256,
        downloaded_file_path = "otel-contrib_linux_amd64.tar.gz",
    )
