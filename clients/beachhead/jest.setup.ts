// Jest setup file for ESM/CommonJS compatibility
import { TextEncoder, TextDecoder } from 'util';

// Polyfill for Node.js globals in test environment
global.TextEncoder = TextEncoder;
global.TextDecoder = TextDecoder as any;

// Mock ESM modules
jest.mock('ink', () => require('./__mocks__/tui-mocks').mockInk);
jest.mock('chalk', () => require('./__mocks__/tui-mocks').createChalkMock());
jest.mock('ink-text-input', () => ({
    default: require('./__mocks__/tui-mocks').mockTextInput
}));

// Set up environment variables for tests
process.env.NODE_ENV = 'test';
