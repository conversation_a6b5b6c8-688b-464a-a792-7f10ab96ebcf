load("@aspect_rules_esbuild//esbuild:defs.bzl", "esbuild")
load("@aspect_rules_jest//jest:defs.bzl", "jest_test")
load("@aspect_rules_ts//ts:defs.bzl", "ts_config", "ts_project")
load("@bazel_skylib//rules:build_test.bzl", "build_test")
load("@npm//:defs.bzl", "npm_link_all_packages")
load("//tools/bzl/node_sea:node_sea.bzl", "node_sea_binary")

npm_link_all_packages()

SRC_FILES = glob(
    [
        "src/**/*.ts",
        "src/**/*.tsx",
    ],
    exclude = [
        "src/**/__tests__/**",
        "src/**/__mocks__/**",
    ],
) + [
    "//clients/sidecar/libs:src",
]

TEST_FILES = glob(
    [
        "src/**/__tests__/**/*.ts",
        "src/**/__tests__/__fixtures__/*.json",
        "src/**/__tests__/**/__snapshots__/*.snap",
        "src/**/__tests__/**/*.html",
        "src/**/__mocks__/**/*.ts",
    ],
)

BUILD_DEPS = [
    ":node_modules",
    "//third_party/node-ignore",
]

ts_config(
    name = "tsconfig",
    src = "tsconfig.json",
    deps = [
        "//clients:tsconfig",
        "//clients/sidecar/libs:tsconfig",
    ],
)

filegroup(
    name = "package",
    srcs = ["package.json"],
)

# Generate build-time information
genrule(
    name = "build_info",
    srcs = ["generate-build-info.py"],
    outs = ["src/generated/build-info.ts"],
    cmd = "$(location generate-build-info.py) $@",
    stamp = 1,
)

esbuild(
    name = "bundle",
    srcs = SRC_FILES + [
        ":package",
        ":tsconfig",
        "//clients/data/file-ext:augment_supported_extensions",
    ],
    entry_point = "src/index.ts",
    external = [
        # We don't need this dependency, ignore it
        "@opentelemetry/exporter-jaeger",
    ],
    format = "cjs",
    minify = False,
    output = "out/index.bundle.js",
    platform = "node",
    sourcemap = "inline",
    target = "ES2022",
    tsconfig = ":tsconfig",
    deps = BUILD_DEPS,
)

# Production CLI bundle (ESM)
esbuild(
    name = "cli",
    srcs = SRC_FILES + [
        ":build_info",
        ":package",
        ":tsconfig",
        "//clients/data/file-ext:augment_supported_extensions",
    ],
    config = {
        "banner": {
            "js": "import { createRequire } from 'module'; const require = createRequire(import.meta.url);",
        },
    },
    define = {
        "global": "globalThis",
    },
    entry_point = "src/cli-entry.ts",
    external = [
        # Node.js built-in modules
        "fs",
        "path",
        "url",
        "util",
        "events",
        "stream",
        "crypto",
        "os",
        "child_process",
        "net",
        "tls",
        "http",
        "https",
        "zlib",
        "buffer",
        "process",
        "module",
        "assert",
        "querystring",
        "string_decoder",
        "bufferutil",
        "utf-8-validate",
    ],
    format = "esm",
    minify = True,
    output = "out/augment.mjs",
    platform = "node",
    sourcemap = False,
    target = "node18",
    tsconfig = ":tsconfig",
    deps = BUILD_DEPS,
)

# Development CLI bundle (ESM)
esbuild(
    name = "cli_dev",
    srcs = SRC_FILES + [
        ":build_info",
        ":package",
        ":tsconfig",
        "//clients/data/file-ext:augment_supported_extensions",
    ],
    config = {
        "banner": {
            "js": "import { createRequire } from 'module'; const require = createRequire(import.meta.url);",
        },
    },
    define = {
        "global": "globalThis",
    },
    entry_point = "src/cli-entry.ts",
    external = [
        # Node.js built-in modules
        "fs",
        "path",
        "url",
        "util",
        "events",
        "stream",
        "crypto",
        "os",
        "child_process",
        "net",
        "tls",
        "http",
        "https",
        "zlib",
        "buffer",
        "process",
        "module",
        "assert",
        "querystring",
        "string_decoder",
        "bufferutil",
        "utf-8-validate",
    ],
    format = "esm",
    minify = False,
    output = "out/augment_dev.mjs",
    platform = "node",
    sourcemap = "inline",
    target = "node18",
    tsconfig = ":tsconfig",
    deps = BUILD_DEPS,
)

genrule(
    name = "cli_dev_run",
    srcs = [":cli_dev"],
    outs = ["cli_dev_run"],
    cmd = "cp $< $@",
    executable = True,
    visibility = ["//visibility:public"],
)

# Rules for building a SEA binary.
#
# https://nodejs.org/api/single-executable-applications.html
#

node_sea_binary(
    name = "beachhead",
    src = ":out/index.bundle.js",
    out = "out/beachhead",
    visibility = ["//clients/beachhead:__subpackages__"],
)

# Testing

# This target should never be depended on, instead rely on the :src target.
# This target is only for typechecking.
ts_project(
    name = "ts",
    srcs = SRC_FILES,
    no_emit = True,
    root_dir = "..",
    tsconfig = ":tsconfig",
    deps = BUILD_DEPS,
)

build_test(
    name = "build_test",
    targets = [":ts"],
)

jest_test(
    name = "test",
    timeout = "moderate",
    config = "jest.config.cjs",
    data = SRC_FILES +
           TEST_FILES + glob(
        [
            "scripts/**/*.ts",
        ],
    ) + BUILD_DEPS + [
        ":beachhead",  # Add beachhead binary for E2E tests
        ":cli",  # Add CLI binary for E2E tests
        ":package",
        ":tsconfig",
        "//clients/data/file-ext:augment_supported_extensions",
    ],
    node_modules = "//clients/beachhead:node_modules",
)
