import { execSync } from "child_process";
import { build } from "esbuild";
import { mkdirSync } from "fs";
import { dirname, resolve } from "path";
import { fileURLToPath } from "url";

// Get the directory of this script
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Ensure out directory exists
const outDir = resolve(__dirname, "out");
mkdirSync(outDir, { recursive: true });

// Output file based on environment
const outFile = process.env.DEVELOPMENT === "true" ? "out/augment_dev.mjs" : "out/augment.mjs";

export const configs = {
    // CLI bundle (ESM)
    cli: {
        bundle: true,
        platform: "node",
        tsconfig: resolve(__dirname, "tsconfig.json"),
        target: "node18",
        sourcemap: process.env.DEVELOPMENT === "true" ? "inline" : false,
        minify: process.env.DEVELOPMENT !== "true",
        resolveExtensions: [".ts", ".tsx", ".js", ".jsx"],
        loader: {
            ".ts": "ts",
            ".tsx": "tsx",
        },
        entryPoints: [resolve(__dirname, "src/cli-entry.ts")],
        outfile: resolve(__dirname, outFile),
        format: "esm",
        define: {
            global: "globalThis",
        },
        banner: {
            js: "import { createRequire } from 'module'; const require = createRequire(import.meta.url);",
        },
        external: [
            // Node.js built-in modules only
            "fs",
            "path",
            "url",
            "util",
            "events",
            "stream",
            "crypto",
            "os",
            "child_process",
            "net",
            "tls",
            "http",
            "https",
            "zlib",
            "buffer",
            "process",
            "module",
            "assert",
            "querystring",
            "string_decoder",
            "bufferutil",
            "utf-8-validate",
        ],
    },
};

export async function buildAll() {
    // eslint-disable-next-line no-console
    console.log("🔨 Starting build process...");

    // Generate build info before building
    try {
        execSync("python3 generate-build-info.py src/generated/build-info.ts", {
            stdio: "inherit",
            cwd: __dirname,
        });
    } catch (error) {
        // eslint-disable-next-line no-console
        console.warn("⚠️ Failed to generate build info, using fallback");
    }

    try {
        await Promise.all([build(configs.cli)]);
        // eslint-disable-next-line no-console
        console.log("✅ Build completed successfully");
    } catch (error) {
        // eslint-disable-next-line no-console
        console.error("❌ Build failed:", error);
        process.exit(1);
    }
}

// If run directly
if (import.meta.url === `file://${process.argv[1]}`) {
    buildAll();
}
