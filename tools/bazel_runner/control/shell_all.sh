#!/bin/bash

# Script to run kubecfg_shell command for pvId values
# Usage: ./shell_all.sh [--pvids "0 1 2 3"] [--cleanup-only] [other arguments...]
# If --pvids is not specified, defaults to range 0-127
# If --cleanup-only is specified, only performs pod cleanup without running bazel commands
# All other arguments will be passed as the 'args' parameter to the bazel command

set -euo pipefail

# Parse command line arguments
PVIDS=()
ARGS=()
CLEANUP_ONLY=false

while [[ $# -gt 0 ]]; do
	case $1 in
	--pvids)
		# Parse space-separated list of pvIds
		IFS=' ' read -ra PVIDS <<<"$2"
		shift 2
		;;
	--cleanup-only)
		# Only perform cleanup, don't run bazel commands
		CLEANUP_ONLY=true
		shift
		;;
	*)
		# All other arguments go to ARGS
		ARGS+=("$1")
		shift
		;;
	esac
done

# If no pvIds specified, use default range 0-127
if [[ ${#PVIDS[@]} -eq 0 ]]; then
	PVIDS=($(seq 0 127))
fi

# Convert ARGS array back to string for bazel command
ARGS_STRING="${ARGS[*]}"

# Function to check and cleanup pod if needed
cleanup_pod_if_needed() {
	local pvId=$1
	local pod_name="bazel-shell-$pvId"
	local namespace="test"

	echo "Checking pod $pod_name in namespace $namespace..."

	# Check if pod exists and get its status
	local pod_status
	if pod_status=$(kubectl get pod --context gke_system-services-dev_us-central1_us-central1-dev "$pod_name" -n "$namespace" -o jsonpath='{.status.phase}' 2>/dev/null); then
		echo "Pod $pod_name exists with status: $pod_status"

		# Only delete if status is NOT "Creating" or "Running"
		if [[ "$pod_status" != "Running" && "$pod_status" != "Pending" ]]; then
			echo "Deleting pod $pod_name (status: $pod_status)..."
			if kubectl delete pod "$pod_name" --context gke_system-services-dev_us-central1_us-central1-dev -n "$namespace" --ignore-not-found=true; then
				echo "Successfully deleted pod $pod_name"
				# Wait a moment for deletion to complete
				sleep 2
			else
				echo "WARNING: Failed to delete pod $pod_name" >&2
			fi
		else
			echo "Pod $pod_name is in $pod_status state, skipping deletion"
		fi
	else
		echo "Pod $pod_name does not exist, proceeding..."
	fi
}

# Function to run bazel command for a specific pvId
run_bazel_command() {
	local pvId=$1
	echo "Running bazel command for pvId=$pvId..."

	# Auto-confirm prompts by piping yes command - ignore exit codes since it returns 1 even on success
	yes | bazel run //tools/bazel_runner/control:kubecfg_shell -- apply --extra-config-args="args=$ARGS_STRING" --extra-config-args="pvId=$pvId" || true

	echo "Completed pvId=$pvId"
}

# Function to run commands sequentially
run_sequential() {
	for pvId in "${PVIDS[@]}"; do
		# Check and cleanup pod if needed
		cleanup_pod_if_needed "$pvId"

		# Only run bazel command if not in cleanup-only mode
		if [[ "$CLEANUP_ONLY" != "true" ]]; then
			run_bazel_command "$pvId"
		fi
	done
}

# Main execution
main() {
	if [[ "$CLEANUP_ONLY" == "true" ]]; then
		echo "Starting shell_all.sh in CLEANUP-ONLY mode"
		echo "Processing pvId values for cleanup: ${PVIDS[*]}"
		echo "Total pvIds to cleanup: ${#PVIDS[@]}"
	else
		echo "Starting shell_all.sh with arguments: $ARGS_STRING"
		echo "Processing pvId values: ${PVIDS[*]}"
		echo "Total pvIds to process: ${#PVIDS[@]}"
	fi

	run_sequential

	if [[ "$CLEANUP_ONLY" == "true" ]]; then
		echo "All pod cleanup operations completed!"
	else
		echo "All bazel commands completed successfully!"
	fi
}

# Handle script interruption
trap 'echo "Script interrupted. Cleaning up..."; exit 130' INT TERM

# Run main function
main "$@"
