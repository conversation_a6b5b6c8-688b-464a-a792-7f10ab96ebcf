package main

import (
	"context"
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestDecodeAuditLogMessage(t *testing.T) {
	bot := &AuditLogSlackBot{}

	// Test valid audit log message
	auditLogData := map[string]interface{}{
		"@type": "type.eng.augmentcode.com/AuditLog",
		"authenticationInfo": map[string]interface{}{
			"principalEmail": "<EMAIL>",
		},
		"principal": map[string]interface{}{
			"user_id": "user123",
		},
		"tenant": map[string]interface{}{
			"name": "test-tenant",
		},
		"resource": map[string]interface{}{
			"type": "Request",
			"name": "request123",
		},
		"message":   "User accessed resource",
		"timestamp": "2024-01-01T12:00:00Z",
	}

	// Create a LogMessage structure that contains the audit log data
	logMessageData := map[string]interface{}{
		"insertId":    "test-insert-id",
		"jsonPayload": auditLogData,
	}

	// Marshal to JSON
	logMessageJSON, err := json.Marshal(logMessageData)
	require.NoError(t, err)

	ctx := context.Background()
	logMessage, auditLog, err := bot.decodeAuditLogMessage(ctx, logMessageJSON)
	require.NoError(t, err)

	assert.Equal(t, "test-insert-id", logMessage.InsertID)
	assert.Equal(t, "type.eng.augmentcode.com/AuditLog", auditLog.Type)
	assert.Equal(t, "User accessed resource", auditLog.Message)
}

func TestDecodeAuditLogMessage_InvalidJSON(t *testing.T) {
	bot := &AuditLogSlackBot{}

	// Test invalid JSON data
	invalidData := []byte("invalid json data")

	ctx := context.Background()
	_, _, err := bot.decodeAuditLogMessage(ctx, invalidData)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "failed to unmarshal audit log message")
}

func TestFormatSlackMessage(t *testing.T) {
	config := &Config{
		Slack: SlackConfig{
			DefaultChannel: "#audit-logs",
		},
	}

	bot := &AuditLogSlackBot{
		config: config,
	}

	logMessage := &LogMessage{
		InsertID: "test-insert-id-123",
	}

	auditLog := &AuditLogMessage{
		Type:    "type.eng.augmentcode.com/AuditLog",
		Message: "User accessed sensitive resource",
		Tenant: TenantInfo{
			Name: "test-tenant",
		},
		AuthenticationInfo: AuthenticationInfo{
			Principal: "user123",
		},
	}

	ctx := context.Background()
	slackMessage := bot.formatSlackMessage(ctx, logMessage, auditLog)

	assert.Equal(t, "#audit-logs", slackMessage.Channel)
	assert.Contains(t, slackMessage.Text, "User accessed sensitive resource")
	assert.Contains(t, slackMessage.Text, "test-tenant")
	assert.Contains(t, slackMessage.Text, "user123")
}

func TestConfigValidation(t *testing.T) {
	// Test valid config
	validConfig := Config{
		PubSub: PubSubConfig{
			ProjectID:              "test-project",
			SubscriptionID:         "test-subscription",
			MaxConcurrentReceivers: 10,
		},
		Slack: SlackConfig{
			BotToken:       "xoxb-test-token",
			DefaultChannel: "#audit-logs",
		},
		PromPort: 9090,
	}

	// Should not panic or error with valid config
	assert.NotEmpty(t, validConfig.PubSub.ProjectID)
	assert.NotEmpty(t, validConfig.Slack.BotToken)
	assert.Greater(t, validConfig.PromPort, 0)
}
