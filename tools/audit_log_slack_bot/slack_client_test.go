package main

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestSlackClient_Creation(t *testing.T) {
	client := NewSlackClient("xoxb-fake-token")
	assert.NotNil(t, client)
	assert.Equal(t, "xoxb-fake-token", client.botToken)
	assert.NotNil(t, client.slackAPI)
}

func TestSlackMessageOptions(t *testing.T) {
	options := &SlackMessageOptions{
		Channel:  "#test",
		Text:     "Test message",
		ThreadTS: "1234567890.123456",
	}
	assert.Equal(t, "#test", options.Channel)
	assert.Equal(t, "Test message", options.Text)
	assert.Equal(t, "1234567890.123456", options.ThreadTS)
}

func TestSlackClient_BotTokenMode(t *testing.T) {
	// Test with bot token
	client := NewSlackClient("xoxb-fake-token")
	assert.NotNil(t, client.slackAPI)
	assert.Equal(t, "xoxb-fake-token", client.botToken)
}
