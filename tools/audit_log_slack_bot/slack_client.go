package main

import (
	"context"
	"fmt"

	"github.com/rs/zerolog/log"
	"github.com/slack-go/slack"
)

// SlackClient handles sending messages to <PERSON>lack using bot token
type SlackClient struct {
	botToken string
	slackAPI *slack.Client
}

// NewSlackClient creates a new Slack client with bot token
func NewSlackClient(botToken string) *SlackClient {
	client := &SlackClient{
		botToken: botToken,
	}

	// Initialize Slack API client
	client.slackAPI = slack.New(botToken)

	return client
}

// SlackMessageOptions contains options for sending Slack messages
type SlackMessageOptions struct {
	Channel  string        // Channel to send to (required)
	Text     string        // Fallback text (required)
	Blocks   []slack.Block // Rich message blocks (optional)
	ThreadTS string        // Thread timestamp for threading (optional)
}

// SlackMessageResponse contains the response from sending a message
type SlackMessageResponse struct {
	Timestamp string // Message timestamp for threading
	Channel   string // Channel where message was sent
}

// SendMessage sends a message to <PERSON>lack
func (c *SlackClient) SendMessage(ctx context.Context, options *SlackMessageOptions) (*SlackMessageResponse, error) {
	response, err := c.sendMessageOnce(ctx, options)
	if err == nil {
		return response, nil
	}
	return nil, err
}

// sendMessageOnce sends a message to Slack once without retry
func (c *SlackClient) sendMessageOnce(ctx context.Context, options *SlackMessageOptions) (*SlackMessageResponse, error) {
	return c.sendMessageWithBotToken(ctx, options)
}

// sendMessageWithBotToken sends a message using the Slack API with bot token
func (c *SlackClient) sendMessageWithBotToken(ctx context.Context, options *SlackMessageOptions) (*SlackMessageResponse, error) {
	log.Ctx(ctx).Debug().
		Str("channel", options.Channel).
		Str("text", options.Text).
		Bool("has_blocks", len(options.Blocks) > 0).
		Str("thread_ts", options.ThreadTS).
		Msg("Sending message to Slack via bot token")

	// Prepare message options
	msgOptions := []slack.MsgOption{
		slack.MsgOptionText(options.Text, false),
	}

	if len(options.Blocks) > 0 {
		msgOptions = append(msgOptions, slack.MsgOptionBlocks(options.Blocks...))
	}

	if options.ThreadTS != "" {
		msgOptions = append(msgOptions, slack.MsgOptionTS(options.ThreadTS))
	}

	// Send the message
	channel, timestamp, err := c.slackAPI.PostMessageContext(ctx, options.Channel, msgOptions...)
	if err != nil {
		return nil, fmt.Errorf("failed to send message via Slack API: %w", err)
	}

	log.Ctx(ctx).Debug().
		Str("channel", channel).
		Str("timestamp", timestamp).
		Msg("Successfully sent message to Slack via bot token")

	return &SlackMessageResponse{
		Timestamp: timestamp,
		Channel:   channel,
	}, nil
}

// LookupUserByEmail looks up a Slack user by their email address
// Returns the user ID if found, empty string if not found, or error if lookup fails
func (c *SlackClient) LookupUserByEmail(ctx context.Context, email string) (string, error) {
	// Only available in bot token mode
	if c.botToken == "" || c.slackAPI == nil {
		return "", fmt.Errorf("user lookup requires bot token mode")
	}

	log.Ctx(ctx).Debug().
		Str("email", email).
		Msg("Looking up Slack user by email")

	user, err := c.slackAPI.GetUserByEmailContext(ctx, email)
	if err != nil {
		// Check if it's a "user not found" error
		if slackErr, ok := err.(*slack.SlackErrorResponse); ok && slackErr.Err == "users_not_found" {
			log.Ctx(ctx).Debug().
				Str("email", email).
				Msg("User not found in Slack")
			return "", nil // User not found, but not an error
		}
		return "", fmt.Errorf("failed to lookup user by email: %w", err)
	}

	log.Ctx(ctx).Debug().
		Str("email", email).
		Str("user_id", user.ID).
		Str("user_name", user.Name).
		Msg("Successfully found Slack user")

	return user.ID, nil
}

// GetMessageLink gets a permanent link to a Slack message
func (c *SlackClient) GetMessageLink(ctx context.Context, channel, timestamp string) (string, error) {
	// Only available in bot token mode
	if c.botToken == "" || c.slackAPI == nil {
		return "", fmt.Errorf("message permalink requires bot token mode")
	}

	log.Ctx(ctx).Debug().
		Str("channel", channel).
		Str("timestamp", timestamp).
		Msg("Getting Slack message permalink")

	permalink, err := c.slackAPI.GetPermalinkContext(ctx, &slack.PermalinkParameters{
		Channel: channel,
		Ts:      timestamp,
	})
	if err != nil {
		return "", fmt.Errorf("failed to get message permalink: %w", err)
	}

	log.Ctx(ctx).Debug().
		Str("channel", channel).
		Str("timestamp", timestamp).
		Str("permalink", permalink).
		Msg("Successfully got Slack message permalink")

	return permalink, nil
}
