# Audit Log Slack Bot

A Go service that forwards audit log entries from Google Cloud Pub/Sub to Slack channels. This service reads audit log messages from a GCP Cloud Logging sink via Pub/Sub, decodes the base64-encoded JSON payloads, and forwards them to designated Slack channels using webhooks.

## Architecture

The service consists of the following components:

- **Pub/Sub Subscriber**: Receives audit log messages from a GCP Pub/Sub topic
- **Message Processor**: Decodes base64 JSON payloads and extracts audit log information
- **Slack Client**: Forwards formatted messages to Slack channels via webhooks
- **Health Check**: Provides health and metrics endpoints for monitoring

## Configuration

The service is configured via a JSON configuration file. Here's an example configuration:

```json
{
  "pubsub": {
    "project_id": "your-gcp-project",
    "subscription_id": "audit-log-slack-bot-subscription",
    "max_concurrent_receivers": 10
  },
  "slack": {
    "webhook_url": "https://hooks.slack.com/services/YOUR/WEBHOOK/URL",
    "default_channel": "#audit-logs",
    "username": "Audit Log Bot",
    "icon_emoji": ":shield:"
  },
  "prom_port": 9090,
  "debug": false,
  "retry": {
    "max_attempts": 3,
    "initial_backoff_ms": 1000,
    "max_backoff_ms": 30000,
    "backoff_multiplier": 2.0
  }
}
```

### Configuration Fields

#### Pub/Sub Configuration (`pubsub`)
- `project_id`: GCP project ID containing the Pub/Sub subscription
- `subscription_id`: Name of the Pub/Sub subscription to read from
- `max_concurrent_receivers`: Maximum number of concurrent message processors

#### Slack Configuration (`slack`)
- `webhook_url`: Slack webhook URL for posting messages
- `default_channel`: Default channel to post messages to (can be overridden)
- `username`: Bot username displayed in Slack
- `icon_emoji`: Bot icon emoji displayed in Slack

#### Retry Configuration (`retry`)
- `max_attempts`: Maximum number of retry attempts for failed Slack messages
- `initial_backoff_ms`: Initial backoff duration in milliseconds
- `max_backoff_ms`: Maximum backoff duration in milliseconds
- `backoff_multiplier`: Multiplier for exponential backoff

#### Other Configuration
- `prom_port`: Port for Prometheus metrics and health check endpoints
- `debug`: Enable debug logging

## Prerequisites

### GCP Setup

1. **Cloud Logging Sink**: Create a Cloud Logging sink that filters audit logs and sends them to a Pub/Sub topic:
   ```bash
   gcloud logging sinks create audit-log-sink \
     pubsub.googleapis.com/projects/YOUR_PROJECT/topics/audit-logs \
     --log-filter='jsonPayload.@type="type.eng.augmentcode.com/AuditLog"'
   ```

2. **Pub/Sub Topic and Subscription**: Create the topic and subscription:
   ```bash
   gcloud pubsub topics create audit-logs
   gcloud pubsub subscriptions create audit-log-slack-bot-subscription \
     --topic=audit-logs
   ```

3. **IAM Permissions**: Ensure the service account has the following permissions:
   - `pubsub.subscriber` on the subscription
   - `pubsub.viewer` on the subscription

### Slack Setup

1. **Slack App**: Create a Slack app and configure an incoming webhook
2. **Webhook URL**: Get the webhook URL from your Slack app configuration
3. **Channel**: Create or identify the channel where audit logs should be posted

## Deployment

### Local Development

1. Build the service:
   ```bash
   bazel build //tools/audit_log_slack_bot:audit_log_slack_bot
   ```

2. Run tests:
   ```bash
   bazel test //tools/audit_log_slack_bot:audit_log_slack_bot_test
   ```

3. Run locally:
   ```bash
   bazel run //tools/audit_log_slack_bot:audit_log_slack_bot -- --config /path/to/config.json
   ```

### Kubernetes Deployment

1. Deploy to development:
   ```bash
   bazel run //tools/audit_log_slack_bot:kubecfg
   ```

2. Check deployment status:
   ```bash
   kubectl get pods -l app=audit-log-slack-bot
   ```

3. View logs:
   ```bash
   kubectl logs -l app=audit-log-slack-bot -f
   ```

## Message Format

The service processes audit log messages with the following structure:

```json
{
  "@type": "type.eng.augmentcode.com/AuditLog",
  "authenticationInfo": {
    "principalEmail": "<EMAIL>"
  },
  "principal": {
    "user_id": "user123"
  },
  "tenant": {
    "name": "tenant-name"
  },
  "resource": {
    "type": "Request",
    "name": "request-id"
  },
  "message": "User accessed sensitive resource",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

The service formats these into Slack messages like:

```
🔍 **Audit Log Alert**

**Message:** User accessed sensitive resource
**Timestamp:** 2024-01-01T12:00:00Z
**Tenant:** tenant-name
**User:** user123
```

## Monitoring

The service exposes the following endpoints:

- `/health`: Health check endpoint (returns 200 OK when healthy)
- `/metrics`: Prometheus metrics endpoint

### Metrics

The service includes standard Go runtime metrics and custom metrics for:
- Message processing rates
- Slack delivery success/failure rates
- Retry attempts
- Processing latency

## Error Handling

The service implements robust error handling:

1. **Message Processing Errors**: Failed messages are nacked and will be retried by Pub/Sub
2. **Slack Delivery Errors**: Implements exponential backoff retry with configurable limits
3. **Graceful Shutdown**: Handles SIGTERM/SIGINT signals for clean shutdown
4. **Circuit Breaking**: Prevents cascading failures during Slack outages

## Security Considerations

- **Webhook URL**: Store Slack webhook URLs securely (use Kubernetes secrets in production)
- **Message Content**: Audit logs may contain sensitive information - ensure appropriate channel permissions
- **Network Security**: Use TLS for all external communications
- **Access Control**: Limit access to the service configuration and logs

## Troubleshooting

### Common Issues

1. **Messages not being received**:
   - Check Pub/Sub subscription exists and has messages
   - Verify IAM permissions for the service account
   - Check Cloud Logging sink configuration

2. **Messages not appearing in Slack**:
   - Verify webhook URL is correct and active
   - Check Slack channel permissions
   - Review service logs for delivery errors

3. **High memory usage**:
   - Reduce `max_concurrent_receivers` in configuration
   - Check for message processing bottlenecks

### Debugging

Enable debug logging by setting `debug: true` in the configuration or setting the `LOG_DEBUG` environment variable.

View detailed logs:
```bash
kubectl logs -l app=audit-log-slack-bot --tail=100
```

## Development

### Adding New Features

1. Follow the established patterns in `main.go` and `slack_client.go`
2. Add comprehensive tests for new functionality
3. Update configuration schema if needed
4. Update this README with new features

### Testing

Run all tests:
```bash
bazel test //tools/audit_log_slack_bot/...
```

Run specific test:
```bash
bazel test //tools/audit_log_slack_bot:audit_log_slack_bot_test --test_filter=TestSlackClient_SendMessage_Success
```
