// K8S deployment file for the audit log Slack bot service
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local configMapLib = import 'deploy/common/config-map-lib.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';

// The function that creates the deployment
// env: the environment (DEV, PROD, ...)
// namespace: the namespace that the deployment is created in
// cloud: the cloud (GCP_US_CENTRAL1_DEV, GCP_US_CENTRAL1_PROD, ...)
// namespace_config: the namespace config from //deploy/tenants/namespace_configs
function(env, namespace, cloud, namespace_config)
  // The app name is used in the kubernetes object names. It is also added as label to each object
  // so that we know which app an object belongs to
  local appName = 'audit-log-bot';

  // Creates a service account for the pod
  // A service account is needed to access GCP resources or kubernetes resources
  local serviceAccount = gcpLib.createServiceAccount(
    appName, env, cloud, namespace, iam=true,
  );

  // Mount the Slack bot token secret
  local slackBotTokenSecret = gcpLib.mountSecretManagerSecret(
    env=env,
    cloud=cloud,
    namespace=namespace,
    appName=appName,
    purpose='slack-bot-token',
    version={
      PROD: '1',
      STAGING: '1',
      DEV: 'latest',
    }[env],
    serviceAccount=serviceAccount,
    overrideSecretName='%s-slack-bot-token' % std.asciiLower(env),
  );

  // Configuration that will be passed to the server as a JSON file
  local config = {
    pubsub: {
      project_id: cloudInfo[cloud].projectId,
      subscription_id: '%s-subscription' % appName,
      max_concurrent_receivers: 10,
    },
    slack: {
      bot_token_file: slackBotTokenSecret.filePath,  // Path to the mounted secret file
      default_channel: if env != 'DEV' then 'C096YLP0TDM' else 'C06AFD46C4V',
    },
    prom_port: 9090,
    filter_env: env,
  };

  // A config map is a Kubernetes object that contains configuration data it is "mounted" into a pod
  local configMap = configMapLib.createConfigMap(appName=appName, namespace=namespace, config=config);

  // Creates a container that runs the server
  local container = {
    name: appName,
    // The target is the bazel target that builds the docker image
    target: {
      name: '//tools/audit_log_slack_bot:image',
      dst: appName,
    },
    // The arguments that are passed to the server
    args: [
      '--config',
      configMap.filename,
    ],
    // Ports that the pod exposes
    ports: [
      {
        containerPort: 9090,
        name: 'metrics',
      },
    ],
    // The environment variables that are passed to the server
    env: [
      {
        name: 'POD_ENV',
        value: env,
      },
    ],
    // The volumes that are mounted into the pod
    volumeMounts: [
      configMap.volumeMountDef,
      slackBotTokenSecret.volumeMountDef,
    ],
    // The health check is used to determine if the pod is ready to receive traffic
    readinessProbe: {
      httpGet: {
        path: '/health',
        port: 9090,
      },
      initialDelaySeconds: 10,
      periodSeconds: 30,
    },
    livenessProbe: {
      httpGet: {
        path: '/health',
        port: 9090,
      },
      initialDelaySeconds: 30,
      periodSeconds: 60,
    },
    // Resource requests and limits
    resources: {
      requests: {
        cpu: '100m',
        memory: '128Mi',
      },
      limits: {
        cpu: '500m',
        memory: '512Mi',
      },
    },
  };

  local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
  local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=appName);

  local pod =
    {
      serviceAccountName: serviceAccount.name,
      priorityClassName: cloudInfo.envToPriorityClass(env),
      tolerations: tolerations,
      affinity: affinity,
      containers: [
        container,
      ],
      volumes: [
        configMap.podVolumeDef,
        slackBotTokenSecret.podVolumeDef,
      ],
    };
  local deployment =
    {
      apiVersion: 'apps/v1',
      kind: 'Deployment',
      metadata: {
        name: appName,
        namespace: namespace,
        labels: {
          app: appName,
        },
      },
      spec: {
        replicas: 1,
        strategy: {
          type: 'RollingUpdate',
          rollingUpdate: {
            maxSurge: 1,
            maxUnavailable: 0,
          },
        },
        selector: {
          matchLabels: {
            app: appName,
          },
        },
        template: {
          metadata: {
            labels: {
              app: appName,
            },
          },
          spec: pod,
        },
      },
    };
  // Create Pub/Sub subscription
  local subscription = {
    apiVersion: 'pubsub.cnrm.cloud.google.com/v1beta1',
    kind: 'PubSubSubscription',
    metadata: {
      name: '%s-subscription' % appName,
      namespace: namespace,
      // Don't delete in dev because this causes a lot of errors at the end of a test run, if the
      // topic is deleted before the pod.
      annotations: if env == 'DEV' then {
        'cnrm.cloud.google.com/deletion-policy': 'abandon',
      } else {},
      labels: {
        app: appName,
      },
    },
    spec: {
      topicRef: {
        external: 'projects/%s/topics/audit-log-topic' % cloudInfo[cloud].projectId,
      },
      ackDeadlineSeconds: 60,
      retryPolicy: {
        minimumBackoff: '5s',
        maximumBackoff: '300s',
      },
      // Retain messages for 1 hour in dev and 7 days in staging/prod.
      messageRetentionDuration: if env == 'DEV' then '3600s' else '604800s',
      retainAckedMessages: false,
    },
  };

  // Grant service account access to the subscription
  local subscriptionPolicy = {
    apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
    kind: 'IAMPartialPolicy',
    metadata: {
      name: '%s-subscription-policy' % appName,
      namespace: namespace,
      labels: {
        app: appName,
      },
    },
    spec: {
      resourceRef: {
        kind: 'PubSubSubscription',
        name: '%s-subscription' % appName,
      },
      bindings: [
        {
          role: 'roles/pubsub.subscriber',
          members: [
            { member: 'serviceAccount:%s' % serviceAccount.serviceAccountGcpEmailAddress },
          ],
        },
        {
          role: 'roles/pubsub.viewer',
          members: [
            { member: 'serviceAccount:%s' % serviceAccount.serviceAccountGcpEmailAddress },
          ],
        },
      ],
    },
  };

  // Grant service account access to the topic (for acknowledgments)
  local topicPolicy = {
    apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
    kind: 'IAMPartialPolicy',
    metadata: {
      name: '%s-topic-policy' % appName,
      namespace: namespace,
      labels: {
        app: appName,
      },
    },
    spec: {
      resourceRef: {
        kind: 'PubSubTopic',
        external: 'projects/system-services-dev/topics/audit-log-topic',
      },
      bindings: [
        {
          role: 'roles/pubsub.subscriber',
          members: [
            { member: 'serviceAccount:%s' % serviceAccount.serviceAccountGcpEmailAddress },
          ],
        },
      ],
    },
  };

  lib.flatten([
    configMap.objects,
    serviceAccount.objects,
    slackBotTokenSecret.objects,
    subscription,
    subscriptionPolicy,
    topicPolicy,
    deployment,
  ])
