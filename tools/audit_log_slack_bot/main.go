package main

import (
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"io"
	"net/http"
	"os"
	"os/signal"
	"strings"
	"sync"
	"syscall"

	"cloud.google.com/go/pubsub"
	"github.com/augmentcode/augment/base/logging"
	tracing "github.com/augmentcode/augment/base/tracing/go"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"github.com/rs/zerolog/log"
	_ "go.uber.org/automaxprocs" // Set GOMAXPROCS to container limits on startup
)

var configFile = flag.String("config", "", "Path to config file")

type Config struct {
	// Pub/Sub configuration
	PubSub PubSubConfig `json:"pubsub"`

	// Slack configuration
	Slack SlackConfig `json:"slack"`

	// Prometheus metrics port
	PromPort int `json:"prom_port"`

	// Filter environment
	FilterEnv string `json:"filter_env"`
}

type PubSubConfig struct {
	ProjectID      string `json:"project_id"`
	SubscriptionID string `json:"subscription_id"`
	// Maximum number of concurrent message processors
	MaxConcurrentReceivers int `json:"max_concurrent_receivers"`
}

type SlackConfig struct {
	// Slack bot token for API access (direct token)
	BotToken string `json:"bot_token"`
	// Path to file containing the bot token (preferred over direct token)
	BotTokenFile string `json:"bot_token_file"`
	// Default channel to post to (can be overridden per message)
	DefaultChannel string `json:"default_channel"`
}

type RetryConfig struct {
	// Maximum number of retry attempts
	MaxAttempts int `json:"max_attempts"`
	// Initial backoff duration in milliseconds
	InitialBackoffMs int `json:"initial_backoff_ms"`
	// Maximum backoff duration in milliseconds
	MaxBackoffMs int `json:"max_backoff_ms"`
	// Backoff multiplier
	BackoffMultiplier float64 `json:"backoff_multiplier"`
}

// Validate validates the configuration and sets defaults
func (c *Config) Validate() error {
	// Validate Pub/Sub config
	if c.PubSub.ProjectID == "" {
		return fmt.Errorf("pubsub.project_id is required")
	}
	if c.PubSub.SubscriptionID == "" {
		return fmt.Errorf("pubsub.subscription_id is required")
	}
	if c.PubSub.MaxConcurrentReceivers <= 0 {
		c.PubSub.MaxConcurrentReceivers = 10 // default
	}

	// Validate Slack config - require bot token (either direct or from file)
	if c.Slack.BotToken == "" && c.Slack.BotTokenFile == "" {
		return fmt.Errorf("either slack.bot_token or slack.bot_token_file is required")
	}
	if c.Slack.DefaultChannel == "" {
		return fmt.Errorf("slack.default_channel is required")
	}

	// Validate metrics port
	if c.PromPort <= 0 {
		c.PromPort = 9090 // default
	}

	return nil
}

type LabelInfo struct {
	AppName string `json:"k8s-pod/app"`
}

type LogMessage struct {
	InsertID    string                 `json:"insertId"`
	JsonPayload map[string]interface{} `json:"jsonPayload"`
	Timestamp   string                 `json:"timestamp"`
	Labels      LabelInfo              `json:"labels"`
}

type AuthenticationInfo struct {
	Principal     string `json:"principal"`
	PrincipalType string `json:"principal_type"`
	GenieID       string `json:"genie_id"`
}

type TenantInfo struct {
	Name string `json:"name"`
	Id   string `json:"id"`
}

type ResourceInfo struct {
	Type  string `json:"@type"`
	Name  string `json:"name"`
	Value string `json:"value"`
	// set if Permission
	BusinessReason string `json:"business_reason"`
	Url            string `json:"url"`
}

// AuditLogMessage represents the structure of audit log messages
type AuditLogMessage struct {
	Type               string             `json:"@type"`
	AuthenticationInfo AuthenticationInfo `json:"authenticationInfo"`
	Tenant             TenantInfo         `json:"tenant"`
	Resource           ResourceInfo       `json:"resource"`
	Message            string             `json:"message"`
	Env                string             `json:"env"`
}

func mapToAuditLogMessage(logMessage *LogMessage) (*AuditLogMessage, error) {
	jsonPayload := logMessage.JsonPayload
	data, err := json.Marshal(jsonPayload)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal jsonPayload: %w", err)
	}
	var auditLog AuditLogMessage
	err = json.Unmarshal(data, &auditLog)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal audit log message: %w", err)
	}
	return &auditLog, nil
}

// SlackMessage represents the structure of messages sent to Slack
type SlackMessage struct {
	Channel string `json:"channel,omitempty"`
	Text    string `json:"text"`
}

type AuditLogSlackBot struct {
	config       *Config
	pubsubClient *pubsub.Client
	subscription *pubsub.Subscription
	slackClient  *SlackClient
	filterEnv    string
}

// getBotToken returns the bot token from either direct config or file
func getBotToken(config *SlackConfig) (string, error) {
	if config.BotToken != "" {
		return config.BotToken, nil
	}

	if config.BotTokenFile != "" {
		file, err := os.Open(config.BotTokenFile)
		if err != nil {
			return "", fmt.Errorf("failed to open bot token file: %w", err)
		}
		defer file.Close()

		tokenBytes, err := io.ReadAll(file)
		if err != nil {
			return "", fmt.Errorf("failed to read bot token file: %w", err)
		}

		token := strings.TrimSpace(string(tokenBytes))
		if token == "" {
			return "", fmt.Errorf("bot token file is empty")
		}

		return token, nil
	}

	return "", fmt.Errorf("no bot token configured")
}

func main() {
	logging.SetupServerLogging()

	flag.Parse()
	log.Info().Msgf("Config file: %s", *configFile)

	var config Config
	if *configFile == "" {
		log.Fatal().Msg("Missing config file")
	}

	f, err := os.Open(*configFile)
	if err != nil {
		log.Fatal().Err(err).Msg("Error opening config file")
	}
	defer f.Close()

	decoder := json.NewDecoder(f)
	decoder.DisallowUnknownFields()
	if err := decoder.Decode(&config); err != nil {
		log.Fatal().Err(err).Msg("Error decoding config file")
	}

	// Validate and set defaults
	if err := config.Validate(); err != nil {
		log.Fatal().Err(err).Msg("Invalid configuration")
	}

	log.Info().Msgf("Config loaded successfully: %v", config)

	tracingShutdown := tracing.Init()
	defer tracingShutdown()

	// Create a channel to catch OS signals
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGTERM, syscall.SIGINT)
	wg := sync.WaitGroup{}

	// Start Prometheus metrics server
	go func() {
		http.Handle("/metrics", promhttp.Handler())
		http.HandleFunc("/health", healthCheck)
		err := http.ListenAndServe(fmt.Sprintf(":%d", config.PromPort), nil)
		if err != nil && err != http.ErrServerClosed {
			log.Fatal().Err(err).Msg("Error starting metrics server")
		}
	}()

	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// Initialize the audit log Slack bot
	bot, err := NewAuditLogSlackBot(ctx, &config)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to create audit log Slack bot")
	}
	defer bot.Close()

	// Start processing messages
	wg.Add(1)
	go func() {
		defer wg.Done()
		log.Info().Msg("Starting audit log message processing")
		err := bot.Start(ctx)
		if err != nil {
			log.Error().Err(err).Msg("Error processing audit log messages")
		}
		log.Info().Msg("Audit log message processing stopped")
	}()

	// Wait for shutdown signal
	sig := <-sigChan
	log.Info().Msgf("Received signal: %v", sig)
	cancel()
	wg.Wait()
	log.Info().Msg("Server stopped")
}

func healthCheck(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusOK)
	w.Write([]byte("OK"))
}

func NewAuditLogSlackBot(ctx context.Context, config *Config) (*AuditLogSlackBot, error) {
	// Create Pub/Sub client
	pubsubClient, err := pubsub.NewClient(ctx, config.PubSub.ProjectID)
	if err != nil {
		return nil, fmt.Errorf("failed to create Pub/Sub client: %w", err)
	}

	// Get the subscription
	subscription := pubsubClient.Subscription(config.PubSub.SubscriptionID)

	// Configure subscription settings
	subscription.ReceiveSettings.NumGoroutines = config.PubSub.MaxConcurrentReceivers

	// Create Slack client
	botToken, err := getBotToken(&config.Slack)
	if err != nil {
		return nil, fmt.Errorf("failed to get bot token: %w", err)
	}
	slackClient := NewSlackClient(botToken)

	return &AuditLogSlackBot{
		config:       config,
		pubsubClient: pubsubClient,
		subscription: subscription,
		slackClient:  slackClient,
		filterEnv:    config.FilterEnv,
	}, nil
}

func (bot *AuditLogSlackBot) Start(ctx context.Context) error {
	return bot.subscription.Receive(ctx, bot.processMessage)
}

func (bot *AuditLogSlackBot) Close() error {
	if bot.pubsubClient != nil {
		return bot.pubsubClient.Close()
	}
	return nil
}

func (bot *AuditLogSlackBot) processMessage(ctx context.Context, msg *pubsub.Message) {
	log.Ctx(ctx).Debug().Msg("Processing audit log message")

	// The message data is base64 encoded JSON from Cloud Logging
	logMessage, auditLog, err := bot.decodeAuditLogMessage(ctx, msg.Data)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to decode audit log message")
		msg.Nack()
		return
	}
	if auditLog.AuthenticationInfo.PrincipalType != "INTERNAL_IAP" {
		log.Ctx(ctx).Debug().Msg("Skipping non-IAP user")
		msg.Ack()
		return
	}
	if auditLog.Env != "" && auditLog.Env != bot.filterEnv {
		log.Ctx(ctx).Debug().Msg("Skipping message from wrong environment")
		msg.Ack()
		return
	}
	if logMessage.Labels.AppName == "bigtable-proxy" {
		// we skip bigtable proxy as these are already audit logged at a higher level
		log.Ctx(ctx).Debug().Msg("Skipping bigtable-proxy message")
		msg.Ack()
		return
	}

	// Format the audit log for Slack
	slackMessage := bot.formatSlackMessage(ctx, logMessage, auditLog)

	// Send to Slack
	response, err := bot.slackClient.SendMessage(ctx, slackMessage)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to send message to Slack")
		msg.Nack()
		return
	}

	log.Ctx(ctx).Info().
		Str("channel", response.Channel).
		Str("timestamp", response.Timestamp).
		Msg("Successfully processed and forwarded audit log message")
	msg.Ack()
}

func (bot *AuditLogSlackBot) decodeAuditLogMessage(ctx context.Context, data []byte) (*LogMessage, *AuditLogMessage, error) {
	// Parse the decoded JSON into our audit log structure
	var logMessage LogMessage
	err := json.Unmarshal(data, &logMessage)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to unmarshal audit log message: %w", err)
	}
	log.Ctx(ctx).Info().Msgf("Log Message: %v", logMessage)

	auditLog, err := mapToAuditLogMessage(&logMessage)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to map to audit log message: %w", err)
	}

	return &logMessage, auditLog, nil
}

func (bot *AuditLogSlackBot) formatSlackMessage(ctx context.Context, logMessage *LogMessage, auditLog *AuditLogMessage) *SlackMessageOptions {
	// Format the audit log into a readable Slack message
	text := fmt.Sprintf("🔍 *Audit Log ID `%s`):*\n", logMessage.InsertID)
	msg := auditLog.Message
	if auditLog.Resource.Type == "type.eng.augmentcode.com/Permission" {
		msg += fmt.Sprintf("\nBusiness Reason: `%s`", auditLog.Resource.BusinessReason)
		if auditLog.Resource.Url != "" {
			msg += fmt.Sprintf("/ `%s`", auditLog.Resource.Url)
		}
	}
	text += fmt.Sprintf("```\n%s\n```\n", msg)
	text += fmt.Sprintf("*Employee:* `%s`\n", auditLog.AuthenticationInfo.Principal)
	if auditLog.AuthenticationInfo.GenieID != "" {
		text += fmt.Sprintf("*Genie ID:* `%s`\n", auditLog.AuthenticationInfo.GenieID)
	}
	switch auditLog.Resource.Type {
	case "type.eng.augmentcode.com/Request":
		text += fmt.Sprintf("*Acessed Request ID:* `%s`\n", auditLog.Resource.Value)
	case "type.eng.augmentcode.com/Blob":
		text += fmt.Sprintf("*Acessed Blob Name:* `%s`\n", auditLog.Resource.Value)
	case "type.eng.augmentcode.com/User":
		text += fmt.Sprintf("*Acessed User ID:* `%s`\n", auditLog.Resource.Value)
	}
	if auditLog.Tenant.Name != "" || auditLog.Tenant.Id != "" {
		text += "*Tenant*"
		if auditLog.Tenant.Name != "" {
			text += fmt.Sprintf(" `%s`", auditLog.Tenant.Name)
			if auditLog.Tenant.Id != "" {
				text += fmt.Sprintf(" (`%s`)", auditLog.Tenant.Id)
			}
		} else {
			text += fmt.Sprintf(" `%s`", auditLog.Tenant.Id)
		}
		text += "\n"
	}

	return &SlackMessageOptions{
		Channel: bot.config.Slack.DefaultChannel,
		Text:    text,
	}
}
