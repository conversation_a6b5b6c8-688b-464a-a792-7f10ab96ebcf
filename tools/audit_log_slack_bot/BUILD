load("//tools/bzl:go.bzl", "go_binary", "go_library", "go_oci_image", "go_test")
load("//tools/bzl:kubecfg.bzl", "kubecfg")

package(default_visibility = ["//visibility:private"])

go_library(
    name = "audit_log_slack_bot_lib",
    srcs = [
        "main.go",
        "slack_client.go",
    ],
    importpath = "github.com/augmentcode/augment/tools/audit_log_slack_bot",
    visibility = ["//visibility:private"],
    deps = [
        "//base/logging:logging_go",
        "//base/tracing/go:tracing_go",
        "@com_github_prometheus_client_golang//prometheus/promhttp",
        "@com_github_rs_zerolog//log",
        "@com_github_slack_go_slack//:slack",
        "@com_google_cloud_go_pubsub//:pubsub",
        "@org_uber_go_automaxprocs//:automaxprocs",
    ],
)

go_binary(
    name = "audit_log_slack_bot",
    embed = [":audit_log_slack_bot_lib"],
)

go_test(
    name = "audit_log_slack_bot_test",
    srcs = [
        "main_test.go",
        "slack_client_test.go",
    ],
    embed = [":audit_log_slack_bot_lib"],
    deps = [
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//require",
    ],
)

# Create a container image
go_oci_image(
    name = "image",
    package_name = package_name(),
    binary = ":audit_log_slack_bot",
)

kubecfg(
    name = "kubecfg",
    src = "deploy.jsonnet",
    data = [
        ":image",
    ],
    deps = [
        "//deploy/common:cert-lib",
        "//deploy/common:cloud_info",
        "//deploy/common:config-map-lib",
        "//deploy/common:lib",
        "//deploy/common:node-lib",
        "//deploy/gcp:gcp-lib",
    ],
)
