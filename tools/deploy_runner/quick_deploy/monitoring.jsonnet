local monitoringLib = import 'deploy/gcp/monitoring-lib.jsonnet';

function(cloud)
  // Alert on having any messages in the dead letter queue subscription for quick-deploy
  local deadLetterSpec = {
    displayName: 'Quick Deploy dead letter queue',
    conditionPrometheusQueryLanguage: {
      duration: '60s',
      evaluationInterval: '60s',
      labels: { severity: 'warning' },
      query: |||
        sum by (subscription_id) (pubsub_googleapis_com:subscription_num_undelivered_messages{
          monitored_resource="pubsub_subscription",
          subscription_id=~".*quick-deploy-2.*deadletter-sub$"
        }) > 0
      |||,
    },
  };

  [
    monitoringLib.alertPolicy(
      cloud,
      deadLetterSpec,
      'quick-deploy-deadletter-queue',
      'Quick Deploy has messages in dead letter subscription %s' % monitoringLib.label('subscription_id')
    ),
  ]
