#!/usr/bin/env python3
"""Blob fetch and process stage for Vanguard to Binks pipeline.

This stage fetches blob content from GCS and processes files.
"""

import argparse
import logging
from pathlib import Path

from base.datasets.gcp_creds import get_gcp_creds
from research.data.ray.ray_utils import <PERSON><PERSON><PERSON><PERSON>

from experimental.zheren.data.vanguard_to_binks_blob_fetch_and_process_actor import (
    BlobFetchAndProcessActor,
)

logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def main():
    """Main function for blob fetch and process stage."""
    parser = argparse.ArgumentParser(
        description="Blob fetch and process stage for Vanguard to Binks pipeline"
    )

    # Input/Output arguments
    parser.add_argument(
        "--input",
        type=str,
        required=True,
        help="Input directory containing classified requests",
    )
    parser.add_argument(
        "--output",
        type=str,
        required=True,
        help="Output directory for processed requests with blob content",
    )

    # Blob configuration
    parser.add_argument(
        "--blob-cache-gb",
        type=float,
        default=1.0,
        help="Blob cache size in GB (default: 1.0)",
    )
    parser.add_argument(
        "--service-account-file",
        type=str,
        default=None,
        help="Path to service account JSON file for GCP authentication",
    )

    # Ray configuration
    parser.add_argument(
        "--mode",
        type=str,
        default="local",
        choices=["local", "ray"],
        help="Execution mode: local (for testing) or ray (distributed)",
    )
    parser.add_argument(
        "--num-workers",
        type=int,
        default=20,
        help="Number of blob fetcher workers (default: 20)",
    )
    parser.add_argument(
        "--num-cpu-per-worker",
        type=int,
        default=4,
        help="Number of CPUs per worker (default: 4)",
    )

    args = parser.parse_args()

    # Validate service account file if provided
    if args.service_account_file:
        get_gcp_creds(args.service_account_file)

    # Create output directory if needed
    output_dir = Path(args.output)
    if not output_dir.exists():
        output_dir.mkdir(parents=True, exist_ok=True)

    logger.info(f"Running blob fetch and process stage in {args.mode} mode")
    logger.info(f"Input: {args.input}")
    logger.info(f"Output: {args.output}")

    # Run the stage
    with RayRunner(
        actor_cls=BlobFetchAndProcessActor,
        actor_args={
            "blob_cache_gb": args.blob_cache_gb,
            "service_account_json": args.service_account_file,
        },
        num_workers=args.num_workers,
        num_cpu_per_worker=args.num_cpu_per_worker,
        num_gpu_per_worker=0,
        local=args.mode == "local",
    ) as runner:
        runner.process_jsonl(args.input, args.output)

    # Count output records
    if args.mode == "local":
        total_count = 0
        for output_file in output_dir.glob("*.jsonl"):
            with open(output_file, "r") as f:
                count = sum(1 for _ in f)
                total_count += count
        logger.info(f"Blob fetch and process stage produced {total_count} records")


if __name__ == "__main__":
    main()
