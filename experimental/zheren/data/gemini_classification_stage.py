#!/usr/bin/env python3
"""Gemini classification stage for Vanguard to Binks pipeline.

This stage uses Gemini to classify queries as answerable or not answerable.
Only used for CHAT requests, not CODEBASE_RETRIEVAL.
"""

import argparse
import logging
from pathlib import Path

from research.data.ray.ray_utils import <PERSON><PERSON><PERSON><PERSON>

from experimental.zheren.data.vanguard_to_binks_gemini_classification_actor import (
    GeminiQueryClassificationActor,
)

logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def main():
    """Main function for Gemini classification stage."""
    parser = argparse.ArgumentParser(
        description="Gemini classification stage for Vanguard to Binks pipeline"
    )

    # Input/Output arguments
    parser.add_argument(
        "--input",
        type=str,
        required=True,
        help="Input directory containing classified requests",
    )
    parser.add_argument(
        "--output",
        type=str,
        required=True,
        help="Output directory for Gemini-classified requests",
    )

    # Gemini configuration
    parser.add_argument(
        "--project-id",
        type=str,
        default="augment-research-gsc",
        help="GCP project ID for Gemini API (default: augment-research-gsc)",
    )
    parser.add_argument(
        "--location",
        type=str,
        default="us-central1",
        help="GCP location for Gemini API (default: us-central1)",
    )
    parser.add_argument(
        "--model",
        type=str,
        default="gemini-2.5-flash",
        help="Gemini model name (default: gemini-2.5-flash)",
    )
    parser.add_argument(
        "--temperature",
        type=float,
        default=0.1,
        help="Temperature for Gemini generation (default: 0.1)",
    )
    parser.add_argument(
        "--max-retries",
        type=int,
        default=3,
        help="Maximum retries for Gemini API calls (default: 3)",
    )
    parser.add_argument(
        "--retry-delay",
        type=float,
        default=1.0,
        help="Initial retry delay in seconds (default: 1.0)",
    )
    parser.add_argument(
        "--pass-through-on-error",
        action="store_true",
        help="Pass through queries on Gemini API errors instead of filtering out",
    )
    parser.add_argument(
        "--log-token-usage-every",
        type=int,
        default=100,
        help="Log token usage every N queries (default: 100)",
    )

    # Ray configuration
    parser.add_argument(
        "--mode",
        type=str,
        default="local",
        choices=["local", "ray"],
        help="Execution mode: local (for testing) or ray (distributed)",
    )
    parser.add_argument(
        "--num-workers",
        type=int,
        default=4,
        help="Number of Gemini classifier workers (default: 4)",
    )
    parser.add_argument(
        "--num-cpu-per-worker",
        type=int,
        default=2,
        help="Number of CPUs per worker (default: 2)",
    )

    args = parser.parse_args()

    # Create output directory if needed
    output_dir = Path(args.output)
    if not output_dir.exists():
        output_dir.mkdir(parents=True, exist_ok=True)

    logger.info(f"Running Gemini classification stage in {args.mode} mode")
    logger.info(f"Input: {args.input}")
    logger.info(f"Output: {args.output}")

    # Run the stage
    with RayRunner(
        actor_cls=GeminiQueryClassificationActor,
        actor_args={
            "project_id": args.project_id,
            "location": args.location,
            "model_name": args.model,
            "temperature": args.temperature,
            "max_retries": args.max_retries,
            "retry_delay": args.retry_delay,
            "pass_through_on_error": args.pass_through_on_error,
            "log_token_usage_every": args.log_token_usage_every,
        },
        num_workers=args.num_workers,
        num_cpu_per_worker=args.num_cpu_per_worker,
        num_gpu_per_worker=0,
        local=args.mode == "local",
    ) as runner:
        runner.process_jsonl(args.input, args.output)

    # Count output records
    if args.mode == "local":
        total_count = 0
        for output_file in output_dir.glob("*.jsonl"):
            with open(output_file, "r") as f:
                count = sum(1 for _ in f)
                total_count += count
        logger.info(f"Gemini classification stage produced {total_count} records")


if __name__ == "__main__":
    main()
