#!/usr/bin/env python3
"""Create initial configuration for Vanguard to Binks pipeline.

This script creates the initial PipelineConfig that will be processed by the
BigQuery fetch stage.
"""

import argparse
import json
import logging
from pathlib import Path
from typing import List, Optional

from base.datasets.tenants import DATASET_TENANTS
from experimental.zheren.data.vanguard_to_binks_modular_data_structures import (
    PipelineConfig,
)
from experimental.zheren.data.vanguard_data_utils import VanguardRequestType

logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def main():
    """Main function to create initial pipeline configuration."""
    parser = argparse.ArgumentParser(
        description="Create initial configuration for Vanguard to Binks pipeline"
    )

    # Required arguments
    parser.add_argument(
        "--date-from",
        type=str,
        required=True,
        help="Start date for data collection (YYYY-MM-DD)",
    )
    parser.add_argument(
        "--date-to",
        type=str,
        required=True,
        help="End date for data collection (YYYY-MM-DD)",
    )
    parser.add_argument(
        "--output",
        type=str,
        required=True,
        help="Output path for the initial config JSONL file",
    )

    # Optional arguments
    parser.add_argument(
        "--tenants",
        type=str,
        default=None,
        help="Comma-separated list of specific tenants to process (default: all tenants)",
    )
    parser.add_argument(
        "--limit",
        type=int,
        default=None,
        help="Limit number of requests per tenant (for testing)",
    )
    parser.add_argument(
        "--request-type",
        type=str,
        choices=["CHAT", "CODEBASE_RETRIEVAL"],
        default="CHAT",
        help="Type of requests to process (default: CHAT)",
    )

    args = parser.parse_args()

    # Validate dates
    from datetime import datetime

    try:
        datetime.strptime(args.date_from, "%Y-%m-%d")
        datetime.strptime(args.date_to, "%Y-%m-%d")
    except ValueError:
        logger.error("Invalid date format. Use YYYY-MM-DD")
        return 1

    # Get tenants to process
    if args.tenants:
        tenant_names = [t.strip() for t in args.tenants.split(",")]
        # Validate tenant names
        invalid_tenants = [name for name in tenant_names if name not in DATASET_TENANTS]
        if invalid_tenants:
            logger.error(f"Invalid tenant names: {invalid_tenants}")
            logger.error(f"Available tenants: {sorted(DATASET_TENANTS.keys())}")
            return 1
    else:
        tenant_names = list(DATASET_TENANTS.keys())

    logger.info(f"Creating initial config for {len(tenant_names)} tenants")
    logger.info(f"Date range: {args.date_from} to {args.date_to}")
    logger.info(f"Request type: {args.request_type}")
    if args.limit:
        logger.info(f"Limit per tenant: {args.limit}")

    # Create pipeline configuration
    pipeline_config = PipelineConfig(
        tenant_names=tenant_names,
        date_from=args.date_from,
        date_to=args.date_to,
        limit=args.limit,
        request_type=VanguardRequestType.from_string(args.request_type),
    )

    # Create output directory if needed
    output_path = Path(args.output)
    output_dir = output_path.parent
    if not output_dir.exists():
        output_dir.mkdir(parents=True, exist_ok=True)

    # Write configuration
    with open(output_path, "w") as f:
        f.write(pipeline_config.to_json() + "\n")

    logger.info(f"Initial configuration written to {args.output}")

    # Print summary
    logger.info("\nConfiguration summary:")
    logger.info(f"  Tenants: {len(tenant_names)}")
    logger.info(f"  Date range: {args.date_from} to {args.date_to}")
    logger.info(f"  Request type: {args.request_type}")
    logger.info(f"  Limit per tenant: {args.limit if args.limit else 'No limit'}")

    # Show first few tenants
    if len(tenant_names) > 5:
        logger.info(f"  First 5 tenants: {tenant_names[:5]}")
        logger.info(f"  ... and {len(tenant_names) - 5} more")
    else:
        logger.info(f"  Tenants: {tenant_names}")

    return 0


if __name__ == "__main__":
    exit(main())
