#!/usr/bin/env python3
"""Classification stage for Vanguard to Binks pipeline.

This stage classifies requests using PleaseHold model to filter out non-codebase questions.
"""

import argparse
import logging
from pathlib import Path
from typing import Optional

from base.datasets.gcp_creds import get_gcp_creds
from research.data.ray.ray_utils import <PERSON><PERSON><PERSON><PERSON>

from experimental.zheren.data.vanguard_to_binks_classification_actor import (
    SingleRequestClassificationActor,
)

logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def main():
    """Main function for classification stage."""
    parser = argparse.ArgumentParser(
        description="Classification stage for Vanguard to Binks pipeline"
    )

    # Input/Output arguments
    parser.add_argument(
        "--input",
        type=str,
        required=True,
        help="Input directory containing VanguardSingleRequest records",
    )
    parser.add_argument(
        "--output",
        type=str,
        required=True,
        help="Output directory for classified requests",
    )

    # PleaseHold configuration
    parser.add_argument(
        "--pleasehold-checkpoint",
        type=str,
        default="/mnt/efs/augment/checkpoints/pleasehold/pleasehold_v2_qwen14b_bsz128_ff_fp8",
        help="Path to PleaseHold model checkpoint",
    )
    parser.add_argument(
        "--pleasehold-sha256",
        type=str,
        default="301cb2137636abaee6a0dfb08c7e50d95aca9677cb33a7ccc72858c02938cad6",
        help="SHA256 hash of PleaseHold checkpoint",
    )
    parser.add_argument(
        "--docsets",
        type=str,
        default=None,
        help="Comma-separated list of docsets for PleaseHold",
    )
    parser.add_argument(
        "--service-account-file",
        type=str,
        default=None,
        help="Path to service account JSON file for GCP authentication",
    )

    # Ray configuration
    parser.add_argument(
        "--mode",
        type=str,
        default="local",
        choices=["local", "ray"],
        help="Execution mode: local (for testing) or ray (distributed)",
    )
    parser.add_argument(
        "--num-workers",
        type=int,
        default=4,
        help="Number of classifier workers (default: 4)",
    )
    parser.add_argument(
        "--num-cpu-per-worker",
        type=int,
        default=8,
        help="Number of CPUs per worker (default: 8)",
    )
    parser.add_argument(
        "--num-gpu-per-worker",
        type=int,
        default=0,
        help="Number of GPUs per worker (default: 0, set to 1 for GPU mode)",
    )

    args = parser.parse_args()

    # Validate service account file if provided
    if args.service_account_file:
        get_gcp_creds(args.service_account_file)

    # Create output directory if needed
    output_dir = Path(args.output)
    if not output_dir.exists():
        output_dir.mkdir(parents=True, exist_ok=True)

    logger.info(f"Running classification stage in {args.mode} mode")
    logger.info(f"Input: {args.input}")
    logger.info(f"Output: {args.output}")

    # Run the stage
    with RayRunner(
        actor_cls=SingleRequestClassificationActor,
        actor_args={
            "pleasehold_checkpoint": args.pleasehold_checkpoint,
            "pleasehold_sha256": args.pleasehold_sha256,
            "docsets": args.docsets.split(",") if args.docsets else None,
            "service_account_json": args.service_account_file,
        },
        num_workers=args.num_workers,
        num_cpu_per_worker=args.num_cpu_per_worker,
        num_gpu_per_worker=args.num_gpu_per_worker,
        local=args.mode == "local",
    ) as runner:
        runner.process_jsonl(args.input, args.output)

    # Count output records
    if args.mode == "local":
        total_count = 0
        for output_file in output_dir.glob("*.jsonl"):
            with open(output_file, "r") as f:
                count = sum(1 for _ in f)
                total_count += count
        logger.info(f"Classification stage produced {total_count} records")


if __name__ == "__main__":
    main()
