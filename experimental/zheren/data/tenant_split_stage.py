#!/usr/bin/env python3
"""Tenant split stage for Vanguard to Binks pipeline.

This stage splits a pipeline configuration into per-tenant configurations
to enable parallel processing.
"""

import argparse
import logging
from pathlib import Path

from research.data.ray.ray_utils import Ray<PERSON>unner
from experimental.zheren.data.vanguard_to_binks_tenant_split_actor import (
    TenantSplitActor,
)

logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def main():
    """Main function for tenant split stage."""
    parser = argparse.ArgumentParser(
        description="Tenant split stage for Vanguard to Binks pipeline"
    )

    # Input/Output arguments
    parser.add_argument(
        "--input",
        type=str,
        required=True,
        help="Input path containing pipeline configuration JSONL file",
    )
    parser.add_argument(
        "--output",
        type=str,
        required=True,
        help="Output directory for per-tenant configuration files",
    )

    # Ray configuration
    parser.add_argument(
        "--mode",
        type=str,
        default="local",
        choices=["local", "ray"],
        help="Execution mode: local (for testing) or ray (distributed)",
    )

    args = parser.parse_args()

    # Create output directory if needed
    output_path = Path(args.output)
    if not output_path.exists():
        output_path.mkdir(parents=True, exist_ok=True)

    logger.info(f"Running tenant split stage in {args.mode} mode")
    logger.info(f"Input: {args.input}")
    logger.info(f"Output: {args.output}")

    # Run the stage with a single worker (splitting is fast)
    with RayRunner(
        actor_cls=TenantSplitActor,
        actor_args={},
        num_workers=1,
        num_cpu_per_worker=1,
        num_gpu_per_worker=0,
        local=args.mode == "local",
    ) as runner:
        runner.process_jsonl(args.input, args.output)

    # Count output records
    if args.mode == "local":
        total_count = 0
        for output_file in output_path.glob("*.jsonl"):
            with open(output_file, "r") as f:
                count = sum(1 for _ in f)
                total_count += count
        logger.info(f"Tenant split stage produced {total_count} per-tenant configs")


if __name__ == "__main__":
    main()
