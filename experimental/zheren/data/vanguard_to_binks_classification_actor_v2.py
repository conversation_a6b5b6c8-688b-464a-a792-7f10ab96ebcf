#!/usr/bin/env python3
"""Classification actor for the modular Vanguard to Binks pipeline.

This module provides an actor for classifying requests using PleaseHold
to determine if they are codebase-related. This version assumes requests
already have their messages fetched.
"""

import logging
from typing import Optional

# Import Ray utilities
from research.data.ray.ray_utils import AbstractRayActor

# Import data structures
from experimental.zheren.data.vanguard_to_binks_modular_data_structures import (
    FilteredVanguardSingleRequestWithMetadata,
    VanguardRequestType,
)

# Import PleaseHold classifier
from experimental.zheren.data.pleasehold_classifier_actor import (
    PleaseHoldClassifierActor,
    ClassificationRequest,
)

logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class SingleRequestClassificationActor(
    AbstractRayActor[
        FilteredVanguardSingleRequestWithMetadata,
        FilteredVanguardSingleRequestWithMetadata,
    ]
):
    """Ray actor that classifies requests and filters for codebase-related ones.

    This actor:
    1. Takes a request with message already fetched
    2. Classifies the request using PleaseHold (skips CODEBASE_RETRIEVAL)
    3. Returns the request if it's codebase-related, empty list otherwise
    """

    def __init__(
        self,
        pleasehold_checkpoint: str = "/mnt/efs/augment/checkpoints/pleasehold/pleasehold_v2_qwen14b_bsz128_ff_fp8",
        pleasehold_sha256: Optional[
            str
        ] = "301cb2137636abaee6a0dfb08c7e50d95aca9677cb33a7ccc72858c02938cad6",
        docsets: Optional[list[str]] = None,
    ):
        super().__init__(
            input_cls=FilteredVanguardSingleRequestWithMetadata,
            output_cls=FilteredVanguardSingleRequestWithMetadata,
        )

        # Initialize PleaseHold classifier
        self.classifier = PleaseHoldClassifierActor(
            checkpoint_path=pleasehold_checkpoint,
            checkpoint_sha256=pleasehold_sha256,
            docsets=docsets or [],
            num_gpus=1,  # Use single GPU per actor
        )

        logger.info("Initialized SingleRequestClassificationActor with PleaseHold")

    def process(
        self, row: FilteredVanguardSingleRequestWithMetadata
    ) -> list[FilteredVanguardSingleRequestWithMetadata]:
        """Process a request through classification and filtering."""
        logger.info(
            f"Processing request {row.request_id} of type {row.request_type.value}"
        )

        # Skip classification for codebase retrieval requests - they are already codebase-related
        if row.request_type == VanguardRequestType.CODEBASE_RETRIEVAL:
            logger.info(
                f"Request {row.request_id} is CODEBASE_RETRIEVAL - skipping classification"
            )
            return [row]  # Pass through as-is

        # For CHAT requests, perform classification
        logger.info(f"Request {row.request_id} is CHAT - classifying")

        # Create classification request
        classification_req = ClassificationRequest(
            request_id=row.request_id,
            message=row.message,
            tenant_name=row.tenant_name,
            blob_names=row.blob_names,
            file_paths=None,  # Will be populated later if needed
        )

        # Classify the request
        results = self.classifier.process(classification_req)

        if results and results[0].is_codebase_related:
            logger.debug(
                f"Request {row.request_id} classified as category {results[0].category} - keeping"
            )
            return [row]  # Pass through as-is
        else:
            logger.debug(
                f"Request {row.request_id} not codebase-related - filtering out"
            )
            return []  # Filter out
