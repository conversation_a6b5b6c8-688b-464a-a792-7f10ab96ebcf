#!/usr/bin/env python3
"""Request fetch stage for Vanguard to Binks pipeline.

This stage fetches requests from BigQuery and their content from GCS.
It combines the functionality of BigQuery querying and GCS retrieval.
"""

import argparse
import logging
from pathlib import Path

from research.data.ray.ray_utils import <PERSON><PERSON><PERSON><PERSON>
from experimental.zheren.data.vanguard_to_binks_request_fetch_actor import (
    RequestQueryFetchActor,
)

logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def main():
    """Main function for request fetch stage."""
    parser = argparse.ArgumentParser(
        description="Request fetch stage for Vanguard to Binks pipeline"
    )

    # Input/Output arguments
    parser.add_argument(
        "--input",
        type=str,
        required=True,
        help="Input path containing pipeline configuration JSONL files",
    )
    parser.add_argument(
        "--output",
        type=str,
        required=True,
        help="Output directory for fetched requests with metadata",
    )

    # GCP configuration
    parser.add_argument(
        "--service-account-file",
        type=str,
        help="Path to service account JSON file for GCP access",
    )
    parser.add_argument(
        "--batch-size",
        type=int,
        default=1000,
        help="Number of rows to fetch per BigQuery page (default: 1000)",
    )

    # Ray configuration
    parser.add_argument(
        "--mode",
        type=str,
        default="local",
        choices=["local", "ray"],
        help="Execution mode: local (for testing) or ray (distributed)",
    )
    parser.add_argument(
        "--num-workers",
        type=int,
        default=4,
        help="Number of fetch workers (default: 4)",
    )
    parser.add_argument(
        "--num-cpu-per-worker",
        type=int,
        default=2,
        help="Number of CPUs per worker (default: 2)",
    )

    args = parser.parse_args()

    # Create output directory if needed
    output_path = Path(args.output)
    if not output_path.exists():
        output_path.mkdir(parents=True, exist_ok=True)

    logger.info(f"Running request fetch stage in {args.mode} mode")
    logger.info(f"Input: {args.input}")
    logger.info(f"Output: {args.output}")

    # Run the stage
    with RayRunner(
        actor_cls=RequestQueryFetchActor,
        actor_args={
            "service_account_json": args.service_account_file,
            "batch_size": args.batch_size,
        },
        num_workers=args.num_workers,
        num_cpu_per_worker=args.num_cpu_per_worker,
        num_gpu_per_worker=0,
        local=args.mode == "local",
    ) as runner:
        runner.process_jsonl(args.input, args.output)

    # Count output records
    if args.mode == "local":
        total_count = 0
        for output_file in output_path.glob("*.jsonl"):
            with open(output_file, "r") as f:
                count = sum(1 for _ in f)
                total_count += count
        logger.info(f"Request fetch stage produced {total_count} records")


if __name__ == "__main__":
    main()
