#!/usr/bin/env python3
"""Repository assembly stage for Vanguard to Binks pipeline.

This stage assembles the final Binks format output with selected silver files.
"""

import argparse
import logging
from pathlib import Path

from research.data.ray.ray_utils import <PERSON><PERSON>unner

from experimental.zheren.data.vanguard_to_binks_file_processing_actors import (
    RepositoryAssemblerActor,
)

logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def main():
    """Main function for repository assembly stage."""
    parser = argparse.ArgumentParser(
        description="Repository assembly stage for Vanguard to Binks pipeline"
    )

    # Input/Output arguments
    parser.add_argument(
        "--input",
        type=str,
        required=True,
        help="Input directory containing requests with selected silver files",
    )
    parser.add_argument(
        "--output",
        type=str,
        required=True,
        help="Output directory for the final JSONL files",
    )

    # Ray configuration
    parser.add_argument(
        "--mode",
        type=str,
        default="local",
        choices=["local", "ray"],
        help="Execution mode: local (for testing) or ray (distributed)",
    )
    parser.add_argument(
        "--num-workers",
        type=int,
        default=8,
        help="Number of repository assembler workers (default: 8)",
    )
    parser.add_argument(
        "--num-cpu-per-worker",
        type=int,
        default=2,
        help="Number of CPUs per worker (default: 2)",
    )

    args = parser.parse_args()

    # Create output directory if needed
    output_path = Path(args.output)
    if not output_path.exists():
        output_path.mkdir(parents=True, exist_ok=True)

    logger.info(f"Running repository assembly stage in {args.mode} mode")
    logger.info(f"Input: {args.input}")
    logger.info(f"Output: {args.output}")

    # Run the stage
    with RayRunner(
        actor_cls=RepositoryAssemblerActor,
        actor_args={},
        num_workers=args.num_workers,
        num_cpu_per_worker=args.num_cpu_per_worker,
        num_gpu_per_worker=0,
        local=args.mode == "local",
    ) as runner:
        runner.process_jsonl(args.input, args.output)

    # Count output records
    if args.mode == "local":
        total_count = 0
        for output_file in output_path.glob("*.jsonl"):
            with open(output_file, "r") as f:
                count = sum(1 for _ in f)
                total_count += count
        logger.info(f"Repository assembly stage produced {total_count} records")


if __name__ == "__main__":
    main()
