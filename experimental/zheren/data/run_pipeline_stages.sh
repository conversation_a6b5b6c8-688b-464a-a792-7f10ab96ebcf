#!/bin/bash
# Example script to run the Vanguard to Binks pipeline using individual stage scripts

# Configuration
DATE_FROM="2025-06-01"
DATE_TO="2025-06-07"
WORK_DIR="/tmp/vanguard_binks_pipeline"
MODE="local"  # or "ray"
REQUEST_TYPE="CHAT"  # or "CODEBASE_RETRIEVAL"

# Tenant selection (optional - leave empty for all tenants)
# Examples:
# TENANTS=""  # Use all tenants
# TENANTS="dogfood,dogfood-shard"  # Use specific tenants
# TENANTS="i0-vanguard0,i0-vanguard1,i0-vanguard2"  # Use specific Vanguard tenants
TENANTS=""

# Limit per tenant (optional - leave empty for no limit)
LIMIT=""  # or set to a number like "1000"

# Create work directory
mkdir -p $WORK_DIR

# Create initial config using the dedicated script
echo "Creating initial pipeline config..."
TENANT_ARG=""
if [ ! -z "$TENANTS" ]; then
    TENANT_ARG="--tenants $TENANTS"
fi

LIMIT_ARG=""
if [ ! -z "$LIMIT" ]; then
    LIMIT_ARG="--limit $LIMIT"
fi

python experimental/zheren/data/create_initial_config.py \
    --date-from $DATE_FROM \
    --date-to $DATE_TO \
    --output $WORK_DIR/initial_config.jsonl \
    --request-type $REQUEST_TYPE \
    $TENANT_ARG \
    $LIMIT_ARG

# Stage 1: BigQuery Fetch
echo "Running BigQuery fetch stage..."
python experimental/zheren/data/bigquery_fetch_stage.py \
    --input $WORK_DIR/initial_config.jsonl \
    --output $WORK_DIR/stage0_fetch \
    --mode $MODE \
    --num-workers 10 \
    --batch-size 1000

# Stage 2: Classification
echo "Running classification stage..."
python experimental/zheren/data/classification_stage.py \
    --input $WORK_DIR/stage0_fetch \
    --output $WORK_DIR/stage1_classify \
    --mode $MODE \
    --num-workers 4 \
    --num-gpu-per-worker 1

# Stage 3: Gemini Classification (only for CHAT requests)
if [ "$REQUEST_TYPE" = "CHAT" ]; then
    echo "Running Gemini classification stage..."
    python experimental/zheren/data/gemini_classification_stage.py \
        --input $WORK_DIR/stage1_classify \
        --output $WORK_DIR/stage2_gemini_classify \
        --mode $MODE \
        --num-workers 4

    BLOB_INPUT=$WORK_DIR/stage2_gemini_classify
else
    BLOB_INPUT=$WORK_DIR/stage1_classify
fi

# Stage 4: Blob Fetch and Process
echo "Running blob fetch and process stage..."
python experimental/zheren/data/blob_fetch_and_process_stage.py \
    --input $BLOB_INPUT \
    --output $WORK_DIR/stage3_process \
    --mode $MODE \
    --num-workers 20 \
    --blob-cache-gb 1.0

# Stage 5: Silver File Selection
echo "Running silver file selection stage..."
python experimental/zheren/data/silver_file_selection_stage.py \
    --input $WORK_DIR/stage3_process \
    --output $WORK_DIR/stage4_silver \
    --mode $MODE \
    --num-workers 2 \
    --num-gpu-per-worker 1 \
    --max-silver-files 128

# Stage 6: Repository Assembly
echo "Running repository assembly stage..."
python experimental/zheren/data/repository_assembly_stage.py \
    --input $WORK_DIR/stage4_silver \
    --output $WORK_DIR/stage5_final \
    --mode $MODE \
    --num-workers 8

echo "Pipeline complete! Output written to $WORK_DIR/stage5_final/"
echo "Final output files:"
ls -la $WORK_DIR/stage5_final/
