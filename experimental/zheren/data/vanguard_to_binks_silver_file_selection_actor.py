"""Silver file selection actor for Vanguard to Binks pipeline.

This actor uses the Chatanol4 retriever to select relevant "silver files"
from all processed files based on the user's query.
"""

import logging
from typing import Optional

from base.tokenizers import create_tokenizer_by_name
from research.data.ray.ray_utils import AbstractRayActor
from experimental.zheren.data.vanguard_to_binks_modular_data_structures import (
    ProcessedRequest,
    ProcessedRequestWithSilverFiles,
    ProcessedFile,
)

from base.prompt_format_retrieve import (
    get_retrieval_prompt_formatter_by_name,
    ChatRetrieverPromptInput,
)
from research.core.types import Document, Chunk
from research.retrieval.chunking_functions import get_chunker
from research.retrieval.retrieval_database import RetrievalDatabase
from research.retrieval.scorers.scoring_interface import (
    RetrievalDatabaseScorer,
    get_scorer,
)

logger = logging.getLogger(__name__)


class SilverFileSelectionActor(
    AbstractRayActor[ProcessedRequest, ProcessedRequestWithSilverFiles]
):
    """Select silver files using Chatanol4 retriever.

    This actor:
    1. Creates a retrieval database from all processed files
    2. Queries the database with the user's message
    3. Extracts unique file paths from retrieved chunks
    4. Returns only the relevant "silver files"
    """

    def __init__(
        self,
        scorer_type: str = "dense_scorer_v2_fbwd",
        scorer_ckpt: str = "/mnt/efs/augment/user/tongfei/hm/chatanol/consolidate_mp_model/InitCkpt=ethanol-qwen25coder-1b5&Tokenizer=qwen25coder/out",
        tokenizer: str = "qwen25coder",
        chunker_name: str = "smart_line_level",
        chunker_max_chunk_chars: int = 768,
        chunker_max_headers: int = 3,
        query_formatter: str = "chatanol6",
        document_formatter: str = "chatanol6-embedding-with-path-key",
        num_retrieved_chunks: int = 128,  # Number of chunks to use for file selection
        num_retrieved_extra: int = 384,  # Extra chunks to retrieve initially
        max_silver_files: int = 128,
        max_tokens: int = 1024,
    ):
        super().__init__(
            input_cls=ProcessedRequest,
            output_cls=ProcessedRequestWithSilverFiles,
        )

        self.num_retrieved_chunks = num_retrieved_chunks
        self.num_retrieved_extra = num_retrieved_extra
        self.max_silver_files = max_silver_files
        self.retriever_model = f"{scorer_type}:{scorer_ckpt}"

        # Initialize chunker
        self.chunker = get_chunker(chunker_name, max_chunk_chars=768)

        self.tokenizer = create_tokenizer_by_name(tokenizer)
        self.scorer = get_scorer(
            name=scorer_type,
            checkpoint_path=scorer_ckpt,
            tokenizer_name=tokenizer,
            query_formatter=get_retrieval_prompt_formatter_by_name(
                query_formatter, self.tokenizer
            ),
            document_formatter=get_retrieval_prompt_formatter_by_name(
                document_formatter, self.tokenizer
            ),
        )

        logger.info(
            f"Initialized SilverFileSelectionActor with retriever={self.retriever_model}, "
            f"chunker={chunker_name}, num_chunks={num_retrieved_chunks}, "
            f"num_extra={num_retrieved_extra}, max_files={max_silver_files}"
        )

    def process(self, row: ProcessedRequest) -> list[ProcessedRequestWithSilverFiles]:
        """Process a single request to select silver files."""
        self.scorer.remove_all_docs()
        try:
            # Create retrieval database from all files
            retrieval_database = RetrievalDatabase(
                chunker=self.chunker,
                scorer=self.scorer,
            )

            # Convert ProcessedFiles to Documents for retrieval
            docs = []
            file_map = {}  # Map from path to ProcessedFile

            for file in row.files:
                if file.path and file.content:
                    # Use file path as document ID for easy lookup
                    doc = Document(
                        id=file.path,
                        text=file.content,
                        path=file.path,
                    )
                    docs.append(doc)
                    file_map[file.path] = file

            if not docs:
                logger.warning(
                    f"No valid documents for retrieval in request {row.request_id}"
                )
                # Return with empty silver files
                return [
                    ProcessedRequestWithSilverFiles(
                        tenant_name=row.tenant_name,
                        request_id=row.request_id,
                        message=row.message,
                        files=row.files,
                        silver_file_paths=[],
                        total_size=row.total_size,
                        language_stats=row.language_stats,
                        workspace_key=row.workspace_key,
                        max_file_language=row.max_file_language,
                        max_size_language=row.max_size_language,
                        num_retrieved_chunks=0,
                        num_unique_files_in_chunks=0,
                        retriever_model=self.retriever_model,
                    )
                ]

            # Add documents to retrieval database
            retrieval_database.add_docs(docs)

            # Create query input
            model_input = ChatRetrieverPromptInput(
                prefix="",
                suffix="",
                path="",
                selected_code="",
                message=row.message,
            )

            # Query the retrieval database (retrieves all chunks)
            retrieved_chunks, _ = retrieval_database.query(model_input)

            # Calculate total chunks to consider (following gen_stage.py pattern)
            k = self.num_retrieved_chunks + self.num_retrieved_extra

            # Take only the top k chunks
            retrieved_chunks = retrieved_chunks[:k]

            # Extract unique file paths from retrieved chunks while preserving order
            seen_paths = set()
            silver_file_paths = []

            for chunk in retrieved_chunks:
                if chunk.parent_doc.path and chunk.parent_doc.path not in seen_paths:
                    seen_paths.add(chunk.parent_doc.path)
                    # Verify the path exists in our file map and we haven't reached the limit
                    if (
                        chunk.parent_doc.path in file_map
                        and len(silver_file_paths) < self.max_silver_files
                    ):
                        silver_file_paths.append(chunk.parent_doc.path)

            # Track total unique files for logging
            unique_file_paths = seen_paths

            logger.info(
                f"Request {row.request_id}: Using top {k} chunks "
                f"from {len(unique_file_paths)} unique files, selected {len(silver_file_paths)} silver files"
            )

            return [
                ProcessedRequestWithSilverFiles(
                    tenant_name=row.tenant_name,
                    request_id=row.request_id,
                    message=row.message,
                    files=row.files,
                    silver_file_paths=silver_file_paths,
                    total_size=row.total_size,
                    language_stats=row.language_stats,
                    workspace_key=row.workspace_key,
                    max_file_language=row.max_file_language,
                    max_size_language=row.max_size_language,
                    num_retrieved_chunks=len(retrieved_chunks),
                    num_unique_files_in_chunks=len(unique_file_paths),
                    retriever_model=self.retriever_model,
                )
            ]

        except Exception as e:
            logger.error(
                f"Error selecting silver files for request {row.request_id}: {e}",
                exc_info=True,
            )
            # Return with empty silver files on error
            return [
                ProcessedRequestWithSilverFiles(
                    tenant_name=row.tenant_name,
                    request_id=row.request_id,
                    message=row.message,
                    files=row.files,
                    silver_file_paths=[],
                    total_size=row.total_size,
                    language_stats=row.language_stats,
                    workspace_key=row.workspace_key,
                    max_file_language=row.max_file_language,
                    max_size_language=row.max_size_language,
                    num_retrieved_chunks=0,
                    num_unique_files_in_chunks=0,
                    retriever_model=self.retriever_model,
                )
            ]
