#!/usr/bin/env python3
"""Request fetching actor for the modular Vanguard to Binks pipeline.

This module provides an actor that fetches requests from BigQuery and GCS,
combining the functionality of BigQuery querying and GCS request retrieval.
"""

import logging
from typing import Optional
import google.auth
from google.oauth2 import service_account

from research.data.ray.ray_utils import AbstractRayActor
from base.datasets.tenants import get_tenant, DATASET_TENANTS
from experimental.zheren.data.vanguard_to_binks_modular_data_structures import (
    PipelineConfig,
    FilteredVanguardSingleRequestWithMetadata,
    VanguardRequestType,
)
from experimental.zheren.data.vanguard_data_utils import (
    query_requests_by_type,
    get_request_from_gcs,
)

logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class RequestQueryFetchActor(
    AbstractRayActor[PipelineConfig, FilteredVanguardSingleRequestWithMetadata]
):
    """Fetches requests from BigQuery and retrieves their content from GCS.

    This actor combines BigQuery querying and GCS retrieval to:
    1. Query BigQuery for request IDs based on the pipeline configuration
    2. Fetch the actual request content (message and blob_names) from GCS
    3. Return complete request data ready for classification or processing

    For CODEBASE_RETRIEVAL requests, this actor returns them directly since
    they don't need classification.
    """

    def __init__(
        self,
        service_account_json: Optional[str] = None,
        batch_size: int = 1000,
    ):
        """Initialize the request fetch actor.

        Args:
            service_account_json: Path to service account JSON file
            batch_size: Number of rows to fetch per BigQuery page
        """
        super().__init__(
            input_cls=PipelineConfig,
            output_cls=FilteredVanguardSingleRequestWithMetadata,
        )

        # Initialize credentials
        if service_account_json:
            self.credentials = service_account.Credentials.from_service_account_file(
                service_account_json
            )
        else:
            self.credentials = google.auth.default()[0]

        self.batch_size = batch_size
        logger.info(f"Initialized RequestQueryFetchActor with batch_size={batch_size}")

    def process(
        self, row: PipelineConfig
    ) -> list[FilteredVanguardSingleRequestWithMetadata]:
        """Fetch requests from BigQuery and their content from GCS.

        Args:
            row: PipelineConfig with tenant names, date range, request type, and limit

        Returns:
            List of FilteredVanguardSingleRequestWithMetadata objects with full request data
        """
        all_requests = []
        tenant_names = row.tenant_names or list(DATASET_TENANTS.keys())

        # Calculate per-tenant limit if a total limit is specified
        if row.limit:
            per_tenant_limit = row.limit // len(tenant_names)
            remainder = row.limit % len(tenant_names)
        else:
            per_tenant_limit = None
            remainder = 0

        for idx, tenant_name in enumerate(tenant_names):
            try:
                tenant = get_tenant(tenant_name)
                tenant_id = tenant.tenant_id

                # Calculate this tenant's limit
                tenant_limit = per_tenant_limit
                if tenant_limit is not None and idx < remainder:
                    tenant_limit += 1

                logger.info(
                    f"Querying BigQuery for {tenant_name} ({row.request_type.value}) "
                    f"[{row.date_from} to {row.date_to}]"
                    + (f" with limit {tenant_limit}" if tenant_limit else "")
                )

                # Query BigQuery for request IDs
                try:
                    results = query_requests_by_type(
                        tenant=tenant,
                        request_type=row.request_type,
                        date_from=row.date_from,
                        date_to=row.date_to,
                        limit=tenant_limit,
                        credentials=self.credentials,
                        page_size=self.batch_size,
                    )
                except Exception as e:
                    logger.error(f"Error querying BigQuery for {tenant_name}: {e}")
                    raise

                # Fetch full request data from GCS for each request ID
                tenant_requests = []
                for result_row in results:
                    request_id = result_row["request_id"]

                    # Fetch request content from GCS
                    request = get_request_from_gcs(
                        tenant,
                        tenant_id,
                        request_id,
                        row.request_type,
                        self.credentials,
                    )

                    if not request or not request.message:
                        logger.warning(
                            f"No valid request found for {request_id} in {tenant_name}"
                        )
                        continue

                    # Create complete request object with metadata
                    full_request = FilteredVanguardSingleRequestWithMetadata(
                        tenant_name=tenant_name,
                        request_id=request_id,
                        message=request.message,
                        blob_names=request.blob_names if request.blob_names else [],
                        request_type=row.request_type,
                    )
                    tenant_requests.append(full_request)

                logger.info(
                    f"Fetched {len(tenant_requests)} complete requests for {tenant_name}"
                )
                all_requests.extend(tenant_requests)

            except ValueError:
                logger.warning(f"Skipping unknown tenant: {tenant_name}")
                continue

        logger.info(
            f"Total fetched: {len(all_requests)} complete requests across {len(tenant_names)} tenants"
        )
        return all_requests
