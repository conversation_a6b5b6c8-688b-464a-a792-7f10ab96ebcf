#!/usr/bin/env python3
"""Combined blob fetching and file processing actor for the Vanguard to Binks pipeline.

This module combines the I/O-intensive blob fetching with CPU-intensive file processing
to eliminate intermediate file I/O and improve performance.
"""

import base64
import hashlib
import logging
from collections import defaultdict
from pathlib import Path
from typing import Optional, Any
import google.auth
from google.cloud import storage
from google.oauth2 import service_account

from research.data.ray.ray_utils import AbstractRayActor
from base.datasets.tenants import get_tenant
from base.datasets.gcs_blob_cache import GCSBlobCache
from base.languages import guess_language
from experimental.zheren.data.vanguard_to_binks_modular_data_structures import (
    FilteredVanguardSingleRequestWithMetadata,
    ProcessedRequest,
    ProcessedFile,
    LanguageStats,
)

logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class BlobFetchAndProcessActor(
    AbstractRayActor[FilteredVanguardSingleRequestWithMetadata, ProcessedRequest]
):
    """Combined actor for blob fetching and file processing.

    This actor combines the functionality of BlobContentFetcherActor and FileProcessorActor
    to eliminate intermediate I/O and improve performance. It fetches blobs from GCS and
    immediately processes them for language detection and statistics.
    """

    def __init__(
        self,
        blob_cache_gb: float = 1.0,
        service_account_json: Optional[str] = None,
    ):
        """Initialize the combined blob fetch and process actor.

        Args:
            blob_cache_gb: Size of blob cache in GB
            service_account_json: Path to service account JSON file
        """
        super().__init__(
            input_cls=FilteredVanguardSingleRequestWithMetadata,
            output_cls=ProcessedRequest,
        )

        # Initialize credentials
        if service_account_json:
            self.credentials = service_account.Credentials.from_service_account_file(
                service_account_json
            )
        else:
            self.credentials = google.auth.default()[0]

        # Cache configuration
        self.blob_cache_size_bytes = int(blob_cache_gb * 2**30)

        # Tenant and cache management
        self._tenant_cache = {}
        self._blob_cache = {}

        logger.info(
            f"Initialized BlobFetchAndProcessActor with {blob_cache_gb}GB cache"
        )

    def _get_or_create_blob_cache(self, tenant_name: str) -> tuple[Any, GCSBlobCache]:
        """Get or create tenant and blob cache."""
        if tenant_name not in self._tenant_cache:
            tenant = get_tenant(tenant_name)
            self._tenant_cache[tenant_name] = tenant

            # Create storage client and bucket
            storage_client = storage.Client(
                project=tenant.project_id, credentials=self.credentials
            )
            blob_bucket = storage_client.bucket(tenant.blob_bucket_name)

            # Create blob cache for this tenant
            self._blob_cache[tenant_name] = GCSBlobCache(
                bucket=blob_bucket,
                bucket_prefix=tenant.blob_bucket_prefix,
                max_size_bytes=self.blob_cache_size_bytes,
                num_threads=32,  # Default from other implementations
            )
            logger.info(f"Created blob cache for tenant {tenant_name}")

        return self._tenant_cache[tenant_name], self._blob_cache[tenant_name]

    def _transform_blob_id(self, blob_id: str) -> str:
        """Transform blob ID to hex format for GCS lookup.

        Blob IDs can be in two formats:
        1. Already hex-encoded (64 chars, 0-9a-f)
        2. Base64-encoded (needs to be decoded to hex)
        """
        # Check if it's already a hex string
        if len(blob_id) == 64 and all(c in "0123456789abcdef" for c in blob_id.lower()):
            return blob_id

        # Otherwise, try to decode from base64
        try:
            blob_bytes = base64.b64decode(blob_id)
            return blob_bytes.hex()
        except Exception as e:
            logger.warning(f"Failed to transform blob ID {blob_id}: {e}")
            return blob_id

    def _extract_workspace_key(self, paths: list[str]) -> str:
        """Extract a workspace identifier from file paths."""
        if not paths:
            return "unknown"

        # Try to find common prefix or use hash of paths
        path_str = "|".join(sorted(paths))
        return hashlib.md5(path_str.encode()).hexdigest()[:8]

    def _process_file(
        self, blob_id: str, hex_id: str, path: str, content: str
    ) -> ProcessedFile:
        """Process a single file with language detection."""
        size = len(content.encode("utf-8"))

        # Detect language
        language_info = guess_language(path)
        language = language_info if language_info else "unknown"
        langpart = language_info if language_info else "unknown"

        # Extract extension
        extension = Path(path).suffix.lstrip(".") if path else None
        if not extension:
            extension = "txt"

        return ProcessedFile(
            blob_id=blob_id,  # Original blob_id
            hex_id=hex_id,  # Hex-encoded ID for GCS
            path=path,
            content=content,
            size=size,
            language=language,
            langpart=langpart,
            extension=extension,
        )

    def process(
        self, row: FilteredVanguardSingleRequestWithMetadata
    ) -> list[ProcessedRequest]:
        """Fetch and process blobs in a single operation.

        Args:
            row: FilteredVanguardSingleRequestWithMetadata with message and blob_names

        Returns:
            List containing ProcessedRequest with processed files
        """
        if not row.blob_names:
            logger.warning(f"No blobs to process for request {row.request_id}")
            return []

        tenant, blob_cache = self._get_or_create_blob_cache(row.tenant_name)

        # Transform blob IDs to hex format
        hex_blob_ids = []
        original_to_hex = {}
        for blob_id in row.blob_names:
            hex_id = self._transform_blob_id(blob_id)
            hex_blob_ids.append(hex_id)
            original_to_hex[blob_id] = hex_id

        logger.debug(f"Request {row.request_id} has {len(row.blob_names)} blobs")

        # Fetch all blobs at once (GCSBlobCache handles parallelization internally)
        blob_results = blob_cache.get(hex_blob_ids)

        # Process files as we iterate through results (memory efficient)
        processed_files = []
        total_size = 0
        language_stats = defaultdict(lambda: {"count": 0, "size": 0})
        successful_fetches = 0
        failed_fetches = 0

        for hex_id, result in zip(hex_blob_ids, blob_results):
            if result:
                successful_fetches += 1

                # Find original blob ID
                original_id = None
                for orig, hx in original_to_hex.items():
                    if hx == hex_id:
                        original_id = orig
                        break

                if original_id:
                    # Process the file immediately
                    processed_file = self._process_file(
                        blob_id=original_id,
                        hex_id=hex_id,
                        path=str(result.path),
                        content=result.content,
                    )
                    processed_files.append(processed_file)

                    # Update statistics
                    total_size += processed_file.size
                    language_stats[processed_file.language]["count"] += 1
                    language_stats[processed_file.language]["size"] += (
                        processed_file.size
                    )
            else:
                failed_fetches += 1
                logger.debug(f"Failed to fetch blob {hex_id}")

        # Log fetch statistics
        total_blobs = len(row.blob_names)
        logger.info(
            f"Fetched and processed blobs for request {row.request_id}: "
            f"{successful_fetches} successful, {failed_fetches} failed out of {total_blobs} total"
        )

        if not processed_files:
            logger.warning(f"No files processed for request {row.request_id}")
            return []

        # Calculate final language statistics
        final_language_stats = {}
        for lang, stats in language_stats.items():
            final_language_stats[lang] = LanguageStats(
                file_count=stats["count"],
                total_size=stats["size"],
                percentage_of_files=(stats["count"] / len(processed_files)) * 100,
                percentage_of_size=(stats["size"] / total_size) * 100
                if total_size > 0
                else 0,
            )

        # Find dominant languages
        max_file_lang = max(
            language_stats.keys(),
            key=lambda x: language_stats[x]["count"],
            default="unknown",
        )
        max_size_lang = max(
            language_stats.keys(),
            key=lambda x: language_stats[x]["size"],
            default="unknown",
        )

        # Extract workspace key
        paths = [f.path for f in processed_files if f.path]
        workspace_key = self._extract_workspace_key(paths)

        # Log processing statistics
        logger.info(
            f"Processed request {row.request_id}: {len(processed_files)} files, "
            f"{total_size} bytes total"
        )

        # Create result
        result = ProcessedRequest(
            tenant_name=row.tenant_name,
            request_id=row.request_id,
            message=row.message,
            files=processed_files,
            total_size=total_size,
            language_stats=final_language_stats,
            workspace_key=workspace_key,
            max_file_language=max_file_lang,
            max_size_language=max_size_lang,
        )

        return [result]
