#!/usr/bin/env python3
"""BigQuery fetch stage for Vanguard to Binks pipeline.

This stage fetches requests from BigQuery based on the pipeline configuration.
"""

import argparse
import logging
from pathlib import Path
from typing import Optional

from base.datasets.gcp_creds import get_gcp_creds
from research.data.ray.ray_utils import <PERSON><PERSON><PERSON><PERSON>

from experimental.zheren.data.vanguard_to_binks_modular_data_structures import (
    PipelineConfig,
)
from experimental.zheren.data.vanguard_to_binks_bigquery_actors import (
    BigQueryRequestFetcherActor,
)
from experimental.zheren.data.vanguard_data_utils import VanguardRequestType

logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def main():
    """Main function for BigQuery fetch stage."""
    parser = argparse.ArgumentParser(
        description="BigQuery fetch stage for Vanguard to Binks pipeline"
    )

    # Input/Output arguments
    parser.add_argument(
        "--input",
        type=str,
        required=True,
        help="Input JSONL file containing PipelineConfig",
    )
    parser.add_argument(
        "--output",
        type=str,
        required=True,
        help="Output directory for fetched requests",
    )

    # BigQuery configuration
    parser.add_argument(
        "--service-account-file",
        type=str,
        default=None,
        help="Path to service account JSON file for GCP authentication",
    )
    parser.add_argument(
        "--batch-size",
        type=int,
        default=1000,
        help="BigQuery result batch size (default: 1000)",
    )

    # Ray configuration
    parser.add_argument(
        "--mode",
        type=str,
        default="local",
        choices=["local", "ray"],
        help="Execution mode: local (for testing) or ray (distributed)",
    )
    parser.add_argument(
        "--num-workers",
        type=int,
        default=10,
        help="Number of BigQuery fetcher workers (default: 10)",
    )
    parser.add_argument(
        "--num-cpu-per-worker",
        type=int,
        default=2,
        help="Number of CPUs per worker (default: 2)",
    )

    args = parser.parse_args()

    # Validate service account file if provided
    if args.service_account_file:
        get_gcp_creds(args.service_account_file)

    # Create output directory if needed
    output_dir = Path(args.output)
    if not output_dir.exists():
        output_dir.mkdir(parents=True, exist_ok=True)

    logger.info(f"Running BigQuery fetch stage in {args.mode} mode")
    logger.info(f"Input: {args.input}")
    logger.info(f"Output: {args.output}")

    # Run the stage
    with RayRunner(
        actor_cls=BigQueryRequestFetcherActor,
        actor_args={
            "service_account_json": args.service_account_file,
            "batch_size": args.batch_size,
        },
        num_workers=args.num_workers,
        num_cpu_per_worker=args.num_cpu_per_worker,
        num_gpu_per_worker=0,
        local=args.mode == "local",
    ) as runner:
        runner.process_jsonl(args.input, args.output)

    # Count output records
    if args.mode == "local":
        total_count = 0
        for output_file in output_dir.glob("*.jsonl"):
            with open(output_file, "r") as f:
                count = sum(1 for _ in f)
                total_count += count
        logger.info(f"BigQuery fetch stage produced {total_count} records")


if __name__ == "__main__":
    main()
