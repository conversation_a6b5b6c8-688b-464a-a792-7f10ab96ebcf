"""Utilities to view diffs in a notebook."""

import atexit
import difflib
import shutil
import tempfile
import textwrap
from dataclasses import dataclass
from functools import lru_cache
from pathlib import Path
from typing import Callable, Literal, Optional

from jinja2 import Template
from pygments import highlight
from pygments.formatters import HtmlFormatter  # pylint:disable=no-name-in-module
from pygments.lexers import get_lexer_by_name, guess_lexer_for_filename
from pygments.util import ClassNotFound

TEMPLATE_PATH = Path(__file__).parent


@lru_cache
def twoway_template() -> Template:
    return Template(Path(TEMPLATE_PATH / "twoway_template.html").read_text())


@lru_cache
def report_template() -> Template:
    return Template(Path(TEMPLATE_PATH / "report.html").read_text())


@dataclass
class TwoWayDiff:
    """Minimal representation for a two-way diff."""

    path: str
    prefix: str
    left: str
    right: str
    suffix: str
    id: str = ""
    left_url: str = "https://support.dogfood.t.us-central1.prod.augmentcode.com/request"
    right_url: str = (
        "https://support.dogfood.t.us-central1.prod.augmentcode.com/request"
    )
    left_request_id: Optional[str] = None
    right_request_id: Optional[str] = None


def _extract_pre_contents(html_code: str) -> str:
    """Extracts the marked-up lines from the HTML generated by pygments.

    The format of the HTML is something like:
    <div class="highlight">
        <pre><span class="c1">...</span>
        <span class="c1">...</span>
        ...
        </pre>
    </div>
    """
    return html_code.split("<pre>")[1].split("</pre>")[0]


def _make_twoway_diff_table(
    left_code: str,
    right_code: str,
    code_to_html: Callable[[str], str],
    left_url: str = "https://support.dogfood.t.us-central1.prod.augmentcode.com/request",
    right_url: str = "https://support.dogfood.t.us-central1.prod.augmentcode.com/request",
    left_request_id: Optional[str] = None,
    right_request_id: Optional[str] = None,
) -> str:
    match = difflib.SequenceMatcher(
        a=left_code.splitlines(keepends=True), b=right_code.splitlines(keepends=True)
    )

    left_html = code_to_html(left_code).splitlines(keepends=True)
    right_html = code_to_html(right_code).splitlines(keepends=True)
    left_rows, right_rows = [], []
    for opcode, left_start, left_end, right_start, right_end in match.get_opcodes():
        if opcode == "equal":
            left_rows += left_html[left_start:left_end]
            right_rows += right_html[right_start:right_end]
        elif opcode == "replace":
            left_rows += [
                f"<span class='diff-minus'>{line.rstrip()}</span>\n"
                for line in left_html[left_start:left_end]
            ]
            right_rows += [
                f"<span class='diff-plus'>{line.rstrip()}</span>\n"
                for line in right_html[right_start:right_end]
            ]
        elif opcode == "delete":
            left_rows += [
                f"<span class='diff-minus'>{line.rstrip()}</span>\n"
                for line in left_html[left_start:left_end]
            ]
            right_rows += ["<span></span>\n" for _ in left_html[left_start:left_end]]
        elif opcode == "insert":
            left_rows += ["<span></span>\n" for _ in right_html[right_start:right_end]]
            right_rows += [
                f"<span class='diff-plus'>{line.rstrip()}</span>\n"
                for line in right_html[right_start:right_end]
            ]
    if left_request_id or right_request_id:
        return textwrap.dedent(
            """
            <tr>
            <td><a href="{left_url}/{left_request_id}">{left_request_id}</a></td>
            <td><a href="{right_url}/{right_request_id}">{right_request_id}</a></td>
            </tr>
            <tr>
            <td class="left">
                <pre>{left_rows}</pre>
            </td>
            <td class="right">
                <pre>{right_rows}</pre>
            </td>
            </tr>
            """
        ).format(
            left_rows="".join(left_rows),
            right_rows="".join(right_rows),
            left_url=left_url,
            right_url=right_url,
            left_request_id=left_request_id,
            right_request_id=right_request_id,
        )
    else:
        return textwrap.dedent(
            """
            <tr>
            <td class="left">
                <pre>{left_rows}</pre>
            </td>
            <td class="right">
                <pre>{right_rows}</pre>
            </td>
            </tr>
            """
        ).format(left_rows="".join(left_rows), right_rows="".join(right_rows))


STYLE = Literal["dark", "light"]


def make_twoway_diff(diff: TwoWayDiff, style: STYLE = "dark") -> dict:
    """Renders a two-way diff."""
    pygments_style = {"dark": "monokai", "light": "default"}[style]

    code = diff.prefix + diff.left + diff.suffix  # Get one version of the code.
    try:
        lexer = guess_lexer_for_filename(diff.path, code)
    except ClassNotFound:
        if diff.path.endswith(".ipynb"):
            lexer = get_lexer_by_name("python")
        elif diff.path.endswith(".svelte"):
            lexer = get_lexer_by_name("typescript")
        else:
            # Don't do syntax highlighting.
            lexer = get_lexer_by_name("text")

    formatter = HtmlFormatter(style=pygments_style, linenos="inline")

    def code_to_html(code: str) -> str:
        return _extract_pre_contents(highlight(code, lexer, formatter))

    prefix = code_to_html(diff.prefix)
    formatter.linenostart = len(diff.prefix.splitlines()) + 1
    diff_table = _make_twoway_diff_table(
        diff.left,
        diff.right,
        code_to_html,
        left_url=diff.left_url,
        right_url=diff.right_url,
        left_request_id=diff.left_request_id,
        right_request_id=diff.right_request_id,
    )
    formatter.linenostart += len(diff.left.splitlines())
    suffix = code_to_html(diff.suffix)

    return {
        "style": formatter.get_style_defs(".highlight"),
        "rootId": diff.id,
        "prefix": prefix,
        "diff_table": diff_table,
        "suffix": suffix,
        "colorscheme": style,
        "path": diff.path,
    }


def render_twoway_diff(diff: TwoWayDiff, style: STYLE = "dark") -> str:
    """Renders a two-way diff."""
    return twoway_template().render(
        **make_twoway_diff(diff, style),
    )


def render_twoway_diff_report(
    diffs: list[TwoWayDiff], style: STYLE = "dark", title: str = ""
) -> str:
    """Renders a two-way diff."""

    diff_dicts = {
        diff.id: make_twoway_diff(diff, style) for i, diff in enumerate(diffs)
    }

    css_style = next(iter(diff_dicts.values()))["style"]

    return report_template().render(
        title=title,
        style=css_style,
        colorscheme=style,
        diffs=diff_dicts,
    )


# Resources to serve these generated files in a notebook.


@lru_cache
def random_dir() -> Path:
    """Returns a temporary directory where we'll save our HTML resources.

    The directory will be stable across multiple calls in a single session.
    """
    tmpdir = tempfile.mkdtemp()
    atexit.register(shutil.rmtree, tmpdir)
    return Path(tempfile.mkdtemp())
