{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Quick analysis of Code Edit Data"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import json\n", "from pathlib import Path\n", "from google.cloud import storage"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["BASE_DIR = Path(\"/mnt/efs/hdc/code-edit-logs/\")\n", "TURING_DIR = BASE_DIR / \"aitutor-turing-export\"\n", "PARETO_DIR = BASE_DIR / \"aitutor-pareto-export\"\n", "\n", "TURING_GCS_BUCKET = \"augment-vendor-aitutor-turing\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Annotations"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'request_id': '4f79e30b-3825-4537-99f1-bbf74b6dab47',\n", "  'session_id': '9bccc9bc-d330-42e3-87ad-d56d9f839982',\n", "  'instruction': 'Create another entery for selected code and replace leading with trailing',\n", "  'selected_code': '### With Leading Icon\\n\\n<ComponentPreview name=\"input-leading-icon\" />\\n',\n", "  'prefix': '---\\ntitle: Input\\ndescription: Displays a form input field or a component that looks like an input field.\\ncomponent: true\\n---\\n\\n<ComponentPreview name=\"input-demo\" className=\"[&_input]:max-w-xs\" />\\n\\n## Installation\\n\\n<Tabs defaultValue=\"cli\">\\n\\n<TabsList>\\n  <TabsTrigger value=\"cli\">CLI</TabsTrigger>\\n  <TabsTrigger value=\"manual\">Manual</TabsTrigger>\\n</TabsList>\\n<TabsContent value=\"cli\">\\n\\n```bash\\nnpx shadcn-ui@latest add input\\n```\\n\\n</TabsContent>\\n\\n<TabsContent value=\"manual\">\\n\\n<Steps>\\n\\n<Step>Copy and paste the following code into your project.</Step>\\n\\n<ComponentSource name=\"input\" />\\n\\n<Step>Update the import paths to match your project setup.</Step>\\n\\n</Steps>\\n\\n</TabsContent>\\n\\n</Tabs>\\n\\n## Usage\\n\\n```tsx\\nimport { Input } from \"@/components/ui/input\"\\n```\\n\\n```tsx\\n<Input />\\n```\\n\\n## Examples\\n\\n### Default\\n\\n<ComponentPreview name=\"input-demo\" className=\"[&_input]:max-w-xs\" />\\n\\n### File\\n\\n<ComponentPreview name=\"input-file\" className=\"[&_input]:max-w-xs\" />\\n\\n### Disabled\\n\\n<ComponentPreview name=\"input-disabled\" className=\"[&_input]:max-w-xs\" />\\n\\n### With Label\\n\\n<ComponentPreview name=\"input-with-label\" className=\"[&_input]:max-w-xs\" />\\n\\n### With Button\\n\\n<ComponentPreview name=\"input-with-button\" className=\"[&_input]:max-w-xs\" />\\n\\n### Form\\n\\n<ComponentPreview name=\"input-form\" />\\n\\n',\n", "  'suffix': '',\n", "  'path': 'ui/apps/www/content/docs/components/input.mdx',\n", "  'prefix_begin': 0,\n", "  'selection_begin': 1284,\n", "  'selection_end': 1354,\n", "  'suffix_end': 1354,\n", "  'timestamp': '2024-01-17T07:16:22.654848',\n", "  'response': '### With Trailing Icon\\n\\n<ComponentPreview name=\"input-trailing-icon\" />\\n',\n", "  'status': 'change_prompt',\n", "  'human_annotated_response': None,\n", "  'human_annotated_instruction': None,\n", "  'user_id': 'mukul-kumar-turing-contractor'},\n", " {'request_id': '2e7f98cb-d6ea-4838-a0be-ac84d42eb155',\n", "  'session_id': '9bccc9bc-d330-42e3-87ad-d56d9f839982',\n", "  'instruction': 'Create another entry for selected code and replace leading with trailing, basically duplicate and replace',\n", "  'selected_code': '### With Leading Icon\\n\\n<ComponentPreview name=\"input-leading-icon\" />\\n',\n", "  'prefix': '---\\ntitle: Input\\ndescription: Displays a form input field or a component that looks like an input field.\\ncomponent: true\\n---\\n\\n<ComponentPreview name=\"input-demo\" className=\"[&_input]:max-w-xs\" />\\n\\n## Installation\\n\\n<Tabs defaultValue=\"cli\">\\n\\n<TabsList>\\n  <TabsTrigger value=\"cli\">CLI</TabsTrigger>\\n  <TabsTrigger value=\"manual\">Manual</TabsTrigger>\\n</TabsList>\\n<TabsContent value=\"cli\">\\n\\n```bash\\nnpx shadcn-ui@latest add input\\n```\\n\\n</TabsContent>\\n\\n<TabsContent value=\"manual\">\\n\\n<Steps>\\n\\n<Step>Copy and paste the following code into your project.</Step>\\n\\n<ComponentSource name=\"input\" />\\n\\n<Step>Update the import paths to match your project setup.</Step>\\n\\n</Steps>\\n\\n</TabsContent>\\n\\n</Tabs>\\n\\n## Usage\\n\\n```tsx\\nimport { Input } from \"@/components/ui/input\"\\n```\\n\\n```tsx\\n<Input />\\n```\\n\\n## Examples\\n\\n### Default\\n\\n<ComponentPreview name=\"input-demo\" className=\"[&_input]:max-w-xs\" />\\n\\n### File\\n\\n<ComponentPreview name=\"input-file\" className=\"[&_input]:max-w-xs\" />\\n\\n### Disabled\\n\\n<ComponentPreview name=\"input-disabled\" className=\"[&_input]:max-w-xs\" />\\n\\n### With Label\\n\\n<ComponentPreview name=\"input-with-label\" className=\"[&_input]:max-w-xs\" />\\n\\n### With Button\\n\\n<ComponentPreview name=\"input-with-button\" className=\"[&_input]:max-w-xs\" />\\n\\n### Form\\n\\n<ComponentPreview name=\"input-form\" />\\n\\n',\n", "  'suffix': '',\n", "  'path': 'ui/apps/www/content/docs/components/input.mdx',\n", "  'prefix_begin': 0,\n", "  'selection_begin': 1284,\n", "  'selection_end': 1354,\n", "  'suffix_end': 1354,\n", "  'timestamp': '2024-01-17T07:18:47.210246',\n", "  'response': '### With Trailing Icon\\n\\n<ComponentPreview name=\"input-trailing-icon\" />\\n',\n", "  'status': 'accept-annotation',\n", "  'human_annotated_response': '### With Leading Icon\\n\\n<ComponentPreview name=\"input-leading-icon\" />\\n\\n### With Trailing Icon\\n\\n<ComponentPreview name=\"input-trailing-icon\" />\\n',\n", "  'human_annotated_instruction': 'Create another entry for selected code and replace leading with trailing, basically duplicate and replace',\n", "  'user_id': 'mukul-kumar-turing-contractor'},\n", " {'request_id': '6b6ce8c3-5132-4e35-a79e-c8658f74ae1a',\n", "  'session_id': '9bccc9bc-d330-42e3-87ad-d56d9f839982',\n", "  'instruction': 'Remove empty tags from JSX',\n", "  'selected_code': \"export default function InputIconTrailingIconDemo() {\\n    return (\\n        <>\\n        <InputWithIcon\\n            icon={<Input />}\\n            inputProps={{\\n                placeholder: 'Email',\\n            }}\\n            position={'trailing'}\\n        />\\n        </>\\n    );\\n}\\n\",\n", "  'prefix': \"import { Input } from '@/registry/new-york/ui/input';\\nimport React from 'react';\\nimport { InputWithIcon } from '../ui/input-with-icon';\\n\\n\",\n", "  'suffix': '',\n", "  'path': 'ui/apps/www/registry/default/example/input-trailing-icon.tsx',\n", "  'prefix_begin': 0,\n", "  'selection_begin': 137,\n", "  'selection_end': 412,\n", "  'suffix_end': 400,\n", "  'timestamp': '2024-01-17T07:03:10.053837',\n", "  'response': \"export default function InputIconTrailingIconDemo() {\\n    return (\\n        <InputWithIcon\\n            icon={<Input />}\\n            inputProps={{\\n                placeholder: 'Email',\\n            }}\\n            position={'trailing'}\\n        />\\n    );\\n}\\n\",\n", "  'status': 'accept',\n", "  'human_annotated_response': None,\n", "  'human_annotated_instruction': None,\n", "  'user_id': 'mukul-kumar-turing-contractor'}]"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["client = storage.Client()\n", "bucket = client.bucket(TURING_GCS)\n", "annotations = list(bucket.list_blobs(prefix=f\"code-edit-logs-qa-pass\", match_glob=\"**/*.json\"))\n", "objs = [json.loads(ann.download_as_text()) for ann in annotations]\n", "objs"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/html": ["<h1>Create another entery for selected code and replace leading with trailing (change_prompt)</h1>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["\n", "<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Transitional//EN\"\n", "          \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd\">\n", "\n", "<html>\n", "\n", "<head>\n", "    <meta http-equiv=\"Content-Type\"\n", "          content=\"text/html; charset=utf-8\" />\n", "    <title></title>\n", "    <style type=\"text/css\">\n", "        table.diff {font-family:Courier; border:medium;}\n", "        .diff_header {background-color:#e0e0e0}\n", "        td.diff_header {text-align:right}\n", "        .diff_next {background-color:#c0c0c0}\n", "        .diff_add {background-color:#aaffaa}\n", "        .diff_chg {background-color:#ffff77}\n", "        .diff_sub {background-color:#ffaaaa}\n", "    </style>\n", "</head>\n", "\n", "<body>\n", "    \n", "    <table class=\"diff\" id=\"difflib_chg_to0__top\"\n", "           cellspacing=\"0\" cellpadding=\"0\" rules=\"groups\" >\n", "        <colgroup></colgroup> <colgroup></colgroup> <colgroup></colgroup>\n", "        <colgroup></colgroup> <colgroup></colgroup> <colgroup></colgroup>\n", "        \n", "        <tbody>\n", "            <tr><td class=\"diff_next\"><a href=\"#difflib_chg_to0__0\">f</a></td><td class=\"diff_header\" id=\"from0_1\">1</td><td nowrap=\"nowrap\">---</td><td class=\"diff_next\"><a href=\"#difflib_chg_to0__0\">f</a></td><td class=\"diff_header\" id=\"to0_1\">1</td><td nowrap=\"nowrap\">---</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from0_2\">2</td><td nowrap=\"nowrap\">title:&nbsp;Input</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to0_2\">2</td><td nowrap=\"nowrap\">title:&nbsp;Input</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from0_3\">3</td><td nowrap=\"nowrap\">description:&nbsp;Displays&nbsp;a&nbsp;form&nbsp;input&nbsp;field&nbsp;or&nbsp;a&nbsp;component&nbsp;that&nbsp;looks&nbsp;like&nbsp;an&nbsp;input&nbsp;field.</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to0_3\">3</td><td nowrap=\"nowrap\">description:&nbsp;Displays&nbsp;a&nbsp;form&nbsp;input&nbsp;field&nbsp;or&nbsp;a&nbsp;component&nbsp;that&nbsp;looks&nbsp;like&nbsp;an&nbsp;input&nbsp;field.</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from0_4\">4</td><td nowrap=\"nowrap\">component:&nbsp;true</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to0_4\">4</td><td nowrap=\"nowrap\">component:&nbsp;true</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from0_5\">5</td><td nowrap=\"nowrap\">---</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to0_5\">5</td><td nowrap=\"nowrap\">---</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from0_6\">6</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to0_6\">6</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from0_7\">7</td><td nowrap=\"nowrap\">&lt;ComponentPreview&nbsp;name=\"input-demo\"&nbsp;className=\"[&amp;_input]:max-w-xs\"&nbsp;/&gt;</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to0_7\">7</td><td nowrap=\"nowrap\">&lt;ComponentPreview&nbsp;name=\"input-demo\"&nbsp;className=\"[&amp;_input]:max-w-xs\"&nbsp;/&gt;</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from0_8\">8</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to0_8\">8</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from0_9\">9</td><td nowrap=\"nowrap\">##&nbsp;Installation</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to0_9\">9</td><td nowrap=\"nowrap\">##&nbsp;Installation</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from0_10\">10</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to0_10\">10</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from0_11\">11</td><td nowrap=\"nowrap\">&lt;Tabs&nbsp;defaultValue=\"cli\"&gt;</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to0_11\">11</td><td nowrap=\"nowrap\">&lt;Tabs&nbsp;defaultValue=\"cli\"&gt;</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from0_12\">12</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to0_12\">12</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from0_13\">13</td><td nowrap=\"nowrap\">&lt;TabsList&gt;</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to0_13\">13</td><td nowrap=\"nowrap\">&lt;TabsList&gt;</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from0_14\">14</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&lt;TabsTrigger&nbsp;value=\"cli\"&gt;CLI&lt;/TabsTrigger&gt;</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to0_14\">14</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&lt;TabsTrigger&nbsp;value=\"cli\"&gt;CLI&lt;/TabsTrigger&gt;</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from0_15\">15</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&lt;TabsTrigger&nbsp;value=\"manual\"&gt;Manual&lt;/TabsTrigger&gt;</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to0_15\">15</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&lt;TabsTrigger&nbsp;value=\"manual\"&gt;Manual&lt;/TabsTrigger&gt;</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from0_16\">16</td><td nowrap=\"nowrap\">&lt;/TabsList&gt;</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to0_16\">16</td><td nowrap=\"nowrap\">&lt;/TabsList&gt;</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from0_17\">17</td><td nowrap=\"nowrap\">&lt;TabsContent&nbsp;value=\"cli\"&gt;</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to0_17\">17</td><td nowrap=\"nowrap\">&lt;TabsContent&nbsp;value=\"cli\"&gt;</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from0_18\">18</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to0_18\">18</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from0_19\">19</td><td nowrap=\"nowrap\">```bash</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to0_19\">19</td><td nowrap=\"nowrap\">```bash</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from0_20\">20</td><td nowrap=\"nowrap\">npx&nbsp;shadcn-ui@latest&nbsp;add&nbsp;input</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to0_20\">20</td><td nowrap=\"nowrap\">npx&nbsp;shadcn-ui@latest&nbsp;add&nbsp;input</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from0_21\">21</td><td nowrap=\"nowrap\">```</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to0_21\">21</td><td nowrap=\"nowrap\">```</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from0_22\">22</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to0_22\">22</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from0_23\">23</td><td nowrap=\"nowrap\">&lt;/TabsContent&gt;</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to0_23\">23</td><td nowrap=\"nowrap\">&lt;/TabsContent&gt;</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from0_24\">24</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to0_24\">24</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from0_25\">25</td><td nowrap=\"nowrap\">&lt;TabsContent&nbsp;value=\"manual\"&gt;</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to0_25\">25</td><td nowrap=\"nowrap\">&lt;TabsContent&nbsp;value=\"manual\"&gt;</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from0_26\">26</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to0_26\">26</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from0_27\">27</td><td nowrap=\"nowrap\">&lt;Steps&gt;</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to0_27\">27</td><td nowrap=\"nowrap\">&lt;Steps&gt;</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from0_28\">28</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to0_28\">28</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from0_29\">29</td><td nowrap=\"nowrap\">&lt;Step&gt;Copy&nbsp;and&nbsp;paste&nbsp;the&nbsp;following&nbsp;code&nbsp;into&nbsp;your&nbsp;project.&lt;/Step&gt;</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to0_29\">29</td><td nowrap=\"nowrap\">&lt;Step&gt;Copy&nbsp;and&nbsp;paste&nbsp;the&nbsp;following&nbsp;code&nbsp;into&nbsp;your&nbsp;project.&lt;/Step&gt;</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from0_30\">30</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to0_30\">30</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from0_31\">31</td><td nowrap=\"nowrap\">&lt;ComponentSource&nbsp;name=\"input\"&nbsp;/&gt;</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to0_31\">31</td><td nowrap=\"nowrap\">&lt;ComponentSource&nbsp;name=\"input\"&nbsp;/&gt;</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from0_32\">32</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to0_32\">32</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from0_33\">33</td><td nowrap=\"nowrap\">&lt;Step&gt;Update&nbsp;the&nbsp;import&nbsp;paths&nbsp;to&nbsp;match&nbsp;your&nbsp;project&nbsp;setup.&lt;/Step&gt;</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to0_33\">33</td><td nowrap=\"nowrap\">&lt;Step&gt;Update&nbsp;the&nbsp;import&nbsp;paths&nbsp;to&nbsp;match&nbsp;your&nbsp;project&nbsp;setup.&lt;/Step&gt;</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from0_34\">34</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to0_34\">34</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from0_35\">35</td><td nowrap=\"nowrap\">&lt;/Steps&gt;</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to0_35\">35</td><td nowrap=\"nowrap\">&lt;/Steps&gt;</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from0_36\">36</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to0_36\">36</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from0_37\">37</td><td nowrap=\"nowrap\">&lt;/TabsContent&gt;</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to0_37\">37</td><td nowrap=\"nowrap\">&lt;/TabsContent&gt;</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from0_38\">38</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to0_38\">38</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from0_39\">39</td><td nowrap=\"nowrap\">&lt;/Tabs&gt;</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to0_39\">39</td><td nowrap=\"nowrap\">&lt;/Tabs&gt;</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from0_40\">40</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to0_40\">40</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from0_41\">41</td><td nowrap=\"nowrap\">##&nbsp;Usage</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to0_41\">41</td><td nowrap=\"nowrap\">##&nbsp;Usage</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from0_42\">42</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to0_42\">42</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from0_43\">43</td><td nowrap=\"nowrap\">```tsx</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to0_43\">43</td><td nowrap=\"nowrap\">```tsx</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from0_44\">44</td><td nowrap=\"nowrap\">import&nbsp;{&nbsp;Input&nbsp;}&nbsp;from&nbsp;\"@/components/ui/input\"</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to0_44\">44</td><td nowrap=\"nowrap\">import&nbsp;{&nbsp;Input&nbsp;}&nbsp;from&nbsp;\"@/components/ui/input\"</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from0_45\">45</td><td nowrap=\"nowrap\">```</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to0_45\">45</td><td nowrap=\"nowrap\">```</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from0_46\">46</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to0_46\">46</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from0_47\">47</td><td nowrap=\"nowrap\">```tsx</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to0_47\">47</td><td nowrap=\"nowrap\">```tsx</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from0_48\">48</td><td nowrap=\"nowrap\">&lt;Input&nbsp;/&gt;</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to0_48\">48</td><td nowrap=\"nowrap\">&lt;Input&nbsp;/&gt;</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from0_49\">49</td><td nowrap=\"nowrap\">```</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to0_49\">49</td><td nowrap=\"nowrap\">```</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from0_50\">50</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to0_50\">50</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from0_51\">51</td><td nowrap=\"nowrap\">##&nbsp;Examples</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to0_51\">51</td><td nowrap=\"nowrap\">##&nbsp;Examples</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from0_52\">52</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to0_52\">52</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from0_53\">53</td><td nowrap=\"nowrap\">###&nbsp;Default</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to0_53\">53</td><td nowrap=\"nowrap\">###&nbsp;Default</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from0_54\">54</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to0_54\">54</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from0_55\">55</td><td nowrap=\"nowrap\">&lt;ComponentPreview&nbsp;name=\"input-demo\"&nbsp;className=\"[&amp;_input]:max-w-xs\"&nbsp;/&gt;</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to0_55\">55</td><td nowrap=\"nowrap\">&lt;ComponentPreview&nbsp;name=\"input-demo\"&nbsp;className=\"[&amp;_input]:max-w-xs\"&nbsp;/&gt;</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from0_56\">56</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to0_56\">56</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from0_57\">57</td><td nowrap=\"nowrap\">###&nbsp;File</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to0_57\">57</td><td nowrap=\"nowrap\">###&nbsp;File</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from0_58\">58</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to0_58\">58</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from0_59\">59</td><td nowrap=\"nowrap\">&lt;ComponentPreview&nbsp;name=\"input-file\"&nbsp;className=\"[&amp;_input]:max-w-xs\"&nbsp;/&gt;</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to0_59\">59</td><td nowrap=\"nowrap\">&lt;ComponentPreview&nbsp;name=\"input-file\"&nbsp;className=\"[&amp;_input]:max-w-xs\"&nbsp;/&gt;</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from0_60\">60</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to0_60\">60</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from0_61\">61</td><td nowrap=\"nowrap\">###&nbsp;Disabled</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to0_61\">61</td><td nowrap=\"nowrap\">###&nbsp;Disabled</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from0_62\">62</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to0_62\">62</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from0_63\">63</td><td nowrap=\"nowrap\">&lt;ComponentPreview&nbsp;name=\"input-disabled\"&nbsp;className=\"[&amp;_input]:max-w-xs\"&nbsp;/&gt;</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to0_63\">63</td><td nowrap=\"nowrap\">&lt;ComponentPreview&nbsp;name=\"input-disabled\"&nbsp;className=\"[&amp;_input]:max-w-xs\"&nbsp;/&gt;</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from0_64\">64</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to0_64\">64</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from0_65\">65</td><td nowrap=\"nowrap\">###&nbsp;With&nbsp;Label</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to0_65\">65</td><td nowrap=\"nowrap\">###&nbsp;With&nbsp;Label</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from0_66\">66</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to0_66\">66</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from0_67\">67</td><td nowrap=\"nowrap\">&lt;ComponentPreview&nbsp;name=\"input-with-label\"&nbsp;className=\"[&amp;_input]:max-w-xs\"&nbsp;/&gt;</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to0_67\">67</td><td nowrap=\"nowrap\">&lt;ComponentPreview&nbsp;name=\"input-with-label\"&nbsp;className=\"[&amp;_input]:max-w-xs\"&nbsp;/&gt;</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from0_68\">68</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to0_68\">68</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from0_69\">69</td><td nowrap=\"nowrap\">###&nbsp;With&nbsp;Button</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to0_69\">69</td><td nowrap=\"nowrap\">###&nbsp;With&nbsp;Button</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from0_70\">70</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to0_70\">70</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from0_71\">71</td><td nowrap=\"nowrap\">&lt;ComponentPreview&nbsp;name=\"input-with-button\"&nbsp;className=\"[&amp;_input]:max-w-xs\"&nbsp;/&gt;</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to0_71\">71</td><td nowrap=\"nowrap\">&lt;ComponentPreview&nbsp;name=\"input-with-button\"&nbsp;className=\"[&amp;_input]:max-w-xs\"&nbsp;/&gt;</td></tr>\n", "            <tr><td class=\"diff_next\" id=\"difflib_chg_to0__0\"></td><td class=\"diff_header\" id=\"from0_72\">72</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to0_72\">72</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from0_73\">73</td><td nowrap=\"nowrap\">###&nbsp;Form</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to0_73\">73</td><td nowrap=\"nowrap\">###&nbsp;Form</td></tr>\n", "            <tr><td class=\"diff_next\" id=\"difflib_chg_to0__1\"></td><td class=\"diff_header\" id=\"from0_74\">74</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to0_74\">74</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from0_75\">75</td><td nowrap=\"nowrap\">&lt;ComponentPreview&nbsp;name=\"input-form\"&nbsp;/&gt;</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to0_75\">75</td><td nowrap=\"nowrap\">&lt;ComponentPreview&nbsp;name=\"input-form\"&nbsp;/&gt;</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from0_76\">76</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to0_76\">76</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"><a href=\"#difflib_chg_to0__1\">n</a></td><td class=\"diff_header\" id=\"from0_77\">77</td><td nowrap=\"nowrap\">###&nbsp;With&nbsp;<span class=\"diff_chg\">Le</span>a<span class=\"diff_chg\">d</span>ing&nbsp;Icon</td><td class=\"diff_next\"><a href=\"#difflib_chg_to0__1\">n</a></td><td class=\"diff_header\" id=\"to0_77\">77</td><td nowrap=\"nowrap\">###&nbsp;With&nbsp;<span class=\"diff_chg\">Tr</span>a<span class=\"diff_chg\">il</span>ing&nbsp;Icon</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from0_78\">78</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to0_78\">78</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"><a href=\"#difflib_chg_to0__top\">t</a></td><td class=\"diff_header\" id=\"from0_79\">79</td><td nowrap=\"nowrap\">&lt;ComponentPreview&nbsp;name=\"input-l<span class=\"diff_sub\">ead</span>ing-icon\"&nbsp;/&gt;</td><td class=\"diff_next\"><a href=\"#difflib_chg_to0__top\">t</a></td><td class=\"diff_header\" id=\"to0_79\">79</td><td nowrap=\"nowrap\">&lt;ComponentPreview&nbsp;name=\"input-<span class=\"diff_add\">trai</span>ling-icon\"&nbsp;/&gt;</td></tr>\n", "        </tbody>\n", "    </table>\n", "    <table class=\"diff\" summary=\"Legends\">\n", "        <tr> <th colspan=\"2\"> Legends </th> </tr>\n", "        <tr> <td> <table border=\"\" summary=\"Colors\">\n", "                      <tr><th> Colors </th> </tr>\n", "                      <tr><td class=\"diff_add\">&nbsp;Added&nbsp;</td></tr>\n", "                      <tr><td class=\"diff_chg\">Changed</td> </tr>\n", "                      <tr><td class=\"diff_sub\">Deleted</td> </tr>\n", "                  </table></td>\n", "             <td> <table border=\"\" summary=\"Links\">\n", "                      <tr><th colspan=\"2\"> Links </th> </tr>\n", "                      <tr><td>(f)irst change</td> </tr>\n", "                      <tr><td>(n)ext change</td> </tr>\n", "                      <tr><td>(t)op</td> </tr>\n", "                  </table></td> </tr>\n", "    </table>\n", "</body>\n", "\n", "</html>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<h1>Create another entry for selected code and replace leading with trailing, basically duplicate and replace (accept-annotation)</h1>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["\n", "<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Transitional//EN\"\n", "          \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd\">\n", "\n", "<html>\n", "\n", "<head>\n", "    <meta http-equiv=\"Content-Type\"\n", "          content=\"text/html; charset=utf-8\" />\n", "    <title></title>\n", "    <style type=\"text/css\">\n", "        table.diff {font-family:Courier; border:medium;}\n", "        .diff_header {background-color:#e0e0e0}\n", "        td.diff_header {text-align:right}\n", "        .diff_next {background-color:#c0c0c0}\n", "        .diff_add {background-color:#aaffaa}\n", "        .diff_chg {background-color:#ffff77}\n", "        .diff_sub {background-color:#ffaaaa}\n", "    </style>\n", "</head>\n", "\n", "<body>\n", "    \n", "    <table class=\"diff\" id=\"difflib_chg_to1__top\"\n", "           cellspacing=\"0\" cellpadding=\"0\" rules=\"groups\" >\n", "        <colgroup></colgroup> <colgroup></colgroup> <colgroup></colgroup>\n", "        <colgroup></colgroup> <colgroup></colgroup> <colgroup></colgroup>\n", "        \n", "        <tbody>\n", "            <tr><td class=\"diff_next\"><a href=\"#difflib_chg_to1__0\">f</a></td><td class=\"diff_header\" id=\"from1_1\">1</td><td nowrap=\"nowrap\">---</td><td class=\"diff_next\"><a href=\"#difflib_chg_to1__0\">f</a></td><td class=\"diff_header\" id=\"to1_1\">1</td><td nowrap=\"nowrap\">---</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from1_2\">2</td><td nowrap=\"nowrap\">title:&nbsp;Input</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to1_2\">2</td><td nowrap=\"nowrap\">title:&nbsp;Input</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from1_3\">3</td><td nowrap=\"nowrap\">description:&nbsp;Displays&nbsp;a&nbsp;form&nbsp;input&nbsp;field&nbsp;or&nbsp;a&nbsp;component&nbsp;that&nbsp;looks&nbsp;like&nbsp;an&nbsp;input&nbsp;field.</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to1_3\">3</td><td nowrap=\"nowrap\">description:&nbsp;Displays&nbsp;a&nbsp;form&nbsp;input&nbsp;field&nbsp;or&nbsp;a&nbsp;component&nbsp;that&nbsp;looks&nbsp;like&nbsp;an&nbsp;input&nbsp;field.</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from1_4\">4</td><td nowrap=\"nowrap\">component:&nbsp;true</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to1_4\">4</td><td nowrap=\"nowrap\">component:&nbsp;true</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from1_5\">5</td><td nowrap=\"nowrap\">---</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to1_5\">5</td><td nowrap=\"nowrap\">---</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from1_6\">6</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to1_6\">6</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from1_7\">7</td><td nowrap=\"nowrap\">&lt;ComponentPreview&nbsp;name=\"input-demo\"&nbsp;className=\"[&amp;_input]:max-w-xs\"&nbsp;/&gt;</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to1_7\">7</td><td nowrap=\"nowrap\">&lt;ComponentPreview&nbsp;name=\"input-demo\"&nbsp;className=\"[&amp;_input]:max-w-xs\"&nbsp;/&gt;</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from1_8\">8</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to1_8\">8</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from1_9\">9</td><td nowrap=\"nowrap\">##&nbsp;Installation</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to1_9\">9</td><td nowrap=\"nowrap\">##&nbsp;Installation</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from1_10\">10</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to1_10\">10</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from1_11\">11</td><td nowrap=\"nowrap\">&lt;Tabs&nbsp;defaultValue=\"cli\"&gt;</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to1_11\">11</td><td nowrap=\"nowrap\">&lt;Tabs&nbsp;defaultValue=\"cli\"&gt;</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from1_12\">12</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to1_12\">12</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from1_13\">13</td><td nowrap=\"nowrap\">&lt;TabsList&gt;</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to1_13\">13</td><td nowrap=\"nowrap\">&lt;TabsList&gt;</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from1_14\">14</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&lt;TabsTrigger&nbsp;value=\"cli\"&gt;CLI&lt;/TabsTrigger&gt;</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to1_14\">14</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&lt;TabsTrigger&nbsp;value=\"cli\"&gt;CLI&lt;/TabsTrigger&gt;</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from1_15\">15</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&lt;TabsTrigger&nbsp;value=\"manual\"&gt;Manual&lt;/TabsTrigger&gt;</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to1_15\">15</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&lt;TabsTrigger&nbsp;value=\"manual\"&gt;Manual&lt;/TabsTrigger&gt;</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from1_16\">16</td><td nowrap=\"nowrap\">&lt;/TabsList&gt;</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to1_16\">16</td><td nowrap=\"nowrap\">&lt;/TabsList&gt;</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from1_17\">17</td><td nowrap=\"nowrap\">&lt;TabsContent&nbsp;value=\"cli\"&gt;</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to1_17\">17</td><td nowrap=\"nowrap\">&lt;TabsContent&nbsp;value=\"cli\"&gt;</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from1_18\">18</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to1_18\">18</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from1_19\">19</td><td nowrap=\"nowrap\">```bash</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to1_19\">19</td><td nowrap=\"nowrap\">```bash</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from1_20\">20</td><td nowrap=\"nowrap\">npx&nbsp;shadcn-ui@latest&nbsp;add&nbsp;input</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to1_20\">20</td><td nowrap=\"nowrap\">npx&nbsp;shadcn-ui@latest&nbsp;add&nbsp;input</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from1_21\">21</td><td nowrap=\"nowrap\">```</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to1_21\">21</td><td nowrap=\"nowrap\">```</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from1_22\">22</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to1_22\">22</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from1_23\">23</td><td nowrap=\"nowrap\">&lt;/TabsContent&gt;</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to1_23\">23</td><td nowrap=\"nowrap\">&lt;/TabsContent&gt;</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from1_24\">24</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to1_24\">24</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from1_25\">25</td><td nowrap=\"nowrap\">&lt;TabsContent&nbsp;value=\"manual\"&gt;</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to1_25\">25</td><td nowrap=\"nowrap\">&lt;TabsContent&nbsp;value=\"manual\"&gt;</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from1_26\">26</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to1_26\">26</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from1_27\">27</td><td nowrap=\"nowrap\">&lt;Steps&gt;</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to1_27\">27</td><td nowrap=\"nowrap\">&lt;Steps&gt;</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from1_28\">28</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to1_28\">28</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from1_29\">29</td><td nowrap=\"nowrap\">&lt;Step&gt;Copy&nbsp;and&nbsp;paste&nbsp;the&nbsp;following&nbsp;code&nbsp;into&nbsp;your&nbsp;project.&lt;/Step&gt;</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to1_29\">29</td><td nowrap=\"nowrap\">&lt;Step&gt;Copy&nbsp;and&nbsp;paste&nbsp;the&nbsp;following&nbsp;code&nbsp;into&nbsp;your&nbsp;project.&lt;/Step&gt;</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from1_30\">30</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to1_30\">30</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from1_31\">31</td><td nowrap=\"nowrap\">&lt;ComponentSource&nbsp;name=\"input\"&nbsp;/&gt;</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to1_31\">31</td><td nowrap=\"nowrap\">&lt;ComponentSource&nbsp;name=\"input\"&nbsp;/&gt;</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from1_32\">32</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to1_32\">32</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from1_33\">33</td><td nowrap=\"nowrap\">&lt;Step&gt;Update&nbsp;the&nbsp;import&nbsp;paths&nbsp;to&nbsp;match&nbsp;your&nbsp;project&nbsp;setup.&lt;/Step&gt;</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to1_33\">33</td><td nowrap=\"nowrap\">&lt;Step&gt;Update&nbsp;the&nbsp;import&nbsp;paths&nbsp;to&nbsp;match&nbsp;your&nbsp;project&nbsp;setup.&lt;/Step&gt;</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from1_34\">34</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to1_34\">34</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from1_35\">35</td><td nowrap=\"nowrap\">&lt;/Steps&gt;</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to1_35\">35</td><td nowrap=\"nowrap\">&lt;/Steps&gt;</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from1_36\">36</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to1_36\">36</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from1_37\">37</td><td nowrap=\"nowrap\">&lt;/TabsContent&gt;</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to1_37\">37</td><td nowrap=\"nowrap\">&lt;/TabsContent&gt;</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from1_38\">38</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to1_38\">38</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from1_39\">39</td><td nowrap=\"nowrap\">&lt;/Tabs&gt;</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to1_39\">39</td><td nowrap=\"nowrap\">&lt;/Tabs&gt;</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from1_40\">40</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to1_40\">40</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from1_41\">41</td><td nowrap=\"nowrap\">##&nbsp;Usage</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to1_41\">41</td><td nowrap=\"nowrap\">##&nbsp;Usage</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from1_42\">42</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to1_42\">42</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from1_43\">43</td><td nowrap=\"nowrap\">```tsx</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to1_43\">43</td><td nowrap=\"nowrap\">```tsx</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from1_44\">44</td><td nowrap=\"nowrap\">import&nbsp;{&nbsp;Input&nbsp;}&nbsp;from&nbsp;\"@/components/ui/input\"</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to1_44\">44</td><td nowrap=\"nowrap\">import&nbsp;{&nbsp;Input&nbsp;}&nbsp;from&nbsp;\"@/components/ui/input\"</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from1_45\">45</td><td nowrap=\"nowrap\">```</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to1_45\">45</td><td nowrap=\"nowrap\">```</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from1_46\">46</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to1_46\">46</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from1_47\">47</td><td nowrap=\"nowrap\">```tsx</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to1_47\">47</td><td nowrap=\"nowrap\">```tsx</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from1_48\">48</td><td nowrap=\"nowrap\">&lt;Input&nbsp;/&gt;</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to1_48\">48</td><td nowrap=\"nowrap\">&lt;Input&nbsp;/&gt;</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from1_49\">49</td><td nowrap=\"nowrap\">```</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to1_49\">49</td><td nowrap=\"nowrap\">```</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from1_50\">50</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to1_50\">50</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from1_51\">51</td><td nowrap=\"nowrap\">##&nbsp;Examples</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to1_51\">51</td><td nowrap=\"nowrap\">##&nbsp;Examples</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from1_52\">52</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to1_52\">52</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from1_53\">53</td><td nowrap=\"nowrap\">###&nbsp;Default</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to1_53\">53</td><td nowrap=\"nowrap\">###&nbsp;Default</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from1_54\">54</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to1_54\">54</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from1_55\">55</td><td nowrap=\"nowrap\">&lt;ComponentPreview&nbsp;name=\"input-demo\"&nbsp;className=\"[&amp;_input]:max-w-xs\"&nbsp;/&gt;</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to1_55\">55</td><td nowrap=\"nowrap\">&lt;ComponentPreview&nbsp;name=\"input-demo\"&nbsp;className=\"[&amp;_input]:max-w-xs\"&nbsp;/&gt;</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from1_56\">56</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to1_56\">56</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from1_57\">57</td><td nowrap=\"nowrap\">###&nbsp;File</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to1_57\">57</td><td nowrap=\"nowrap\">###&nbsp;File</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from1_58\">58</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to1_58\">58</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from1_59\">59</td><td nowrap=\"nowrap\">&lt;ComponentPreview&nbsp;name=\"input-file\"&nbsp;className=\"[&amp;_input]:max-w-xs\"&nbsp;/&gt;</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to1_59\">59</td><td nowrap=\"nowrap\">&lt;ComponentPreview&nbsp;name=\"input-file\"&nbsp;className=\"[&amp;_input]:max-w-xs\"&nbsp;/&gt;</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from1_60\">60</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to1_60\">60</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from1_61\">61</td><td nowrap=\"nowrap\">###&nbsp;Disabled</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to1_61\">61</td><td nowrap=\"nowrap\">###&nbsp;Disabled</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from1_62\">62</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to1_62\">62</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from1_63\">63</td><td nowrap=\"nowrap\">&lt;ComponentPreview&nbsp;name=\"input-disabled\"&nbsp;className=\"[&amp;_input]:max-w-xs\"&nbsp;/&gt;</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to1_63\">63</td><td nowrap=\"nowrap\">&lt;ComponentPreview&nbsp;name=\"input-disabled\"&nbsp;className=\"[&amp;_input]:max-w-xs\"&nbsp;/&gt;</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from1_64\">64</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to1_64\">64</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from1_65\">65</td><td nowrap=\"nowrap\">###&nbsp;With&nbsp;Label</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to1_65\">65</td><td nowrap=\"nowrap\">###&nbsp;With&nbsp;Label</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from1_66\">66</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to1_66\">66</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from1_67\">67</td><td nowrap=\"nowrap\">&lt;ComponentPreview&nbsp;name=\"input-with-label\"&nbsp;className=\"[&amp;_input]:max-w-xs\"&nbsp;/&gt;</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to1_67\">67</td><td nowrap=\"nowrap\">&lt;ComponentPreview&nbsp;name=\"input-with-label\"&nbsp;className=\"[&amp;_input]:max-w-xs\"&nbsp;/&gt;</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from1_68\">68</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to1_68\">68</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from1_69\">69</td><td nowrap=\"nowrap\">###&nbsp;With&nbsp;Button</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to1_69\">69</td><td nowrap=\"nowrap\">###&nbsp;With&nbsp;Button</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from1_70\">70</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to1_70\">70</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from1_71\">71</td><td nowrap=\"nowrap\">&lt;ComponentPreview&nbsp;name=\"input-with-button\"&nbsp;className=\"[&amp;_input]:max-w-xs\"&nbsp;/&gt;</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to1_71\">71</td><td nowrap=\"nowrap\">&lt;ComponentPreview&nbsp;name=\"input-with-button\"&nbsp;className=\"[&amp;_input]:max-w-xs\"&nbsp;/&gt;</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from1_72\">72</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to1_72\">72</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from1_73\">73</td><td nowrap=\"nowrap\">###&nbsp;Form</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to1_73\">73</td><td nowrap=\"nowrap\">###&nbsp;Form</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from1_74\">74</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to1_74\">74</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\" id=\"difflib_chg_to1__0\"></td><td class=\"diff_header\" id=\"from1_75\">75</td><td nowrap=\"nowrap\">&lt;ComponentPreview&nbsp;name=\"input-form\"&nbsp;/&gt;</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to1_75\">75</td><td nowrap=\"nowrap\">&lt;ComponentPreview&nbsp;name=\"input-form\"&nbsp;/&gt;</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from1_76\">76</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to1_76\">76</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from1_77\">77</td><td nowrap=\"nowrap\">###&nbsp;With&nbsp;Leading&nbsp;Icon</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to1_77\">77</td><td nowrap=\"nowrap\">###&nbsp;With&nbsp;Leading&nbsp;Icon</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from1_78\">78</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to1_78\">78</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from1_79\">79</td><td nowrap=\"nowrap\">&lt;ComponentPreview&nbsp;name=\"input-leading-icon\"&nbsp;/&gt;</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to1_79\">79</td><td nowrap=\"nowrap\">&lt;ComponentPreview&nbsp;name=\"input-leading-icon\"&nbsp;/&gt;</td></tr>\n", "            <tr><td class=\"diff_next\"><a href=\"#difflib_chg_to1__top\">t</a></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"><a href=\"#difflib_chg_to1__top\">t</a></td><td class=\"diff_header\" id=\"to1_80\">80</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to1_81\">81</td><td nowrap=\"nowrap\"><span class=\"diff_add\">###&nbsp;With&nbsp;Trailing&nbsp;Icon</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to1_82\">82</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to1_83\">83</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&lt;ComponentPreview&nbsp;name=\"input-trailing-icon\"&nbsp;/&gt;</span></td></tr>\n", "        </tbody>\n", "    </table>\n", "    <table class=\"diff\" summary=\"Legends\">\n", "        <tr> <th colspan=\"2\"> Legends </th> </tr>\n", "        <tr> <td> <table border=\"\" summary=\"Colors\">\n", "                      <tr><th> Colors </th> </tr>\n", "                      <tr><td class=\"diff_add\">&nbsp;Added&nbsp;</td></tr>\n", "                      <tr><td class=\"diff_chg\">Changed</td> </tr>\n", "                      <tr><td class=\"diff_sub\">Deleted</td> </tr>\n", "                  </table></td>\n", "             <td> <table border=\"\" summary=\"Links\">\n", "                      <tr><th colspan=\"2\"> Links </th> </tr>\n", "                      <tr><td>(f)irst change</td> </tr>\n", "                      <tr><td>(n)ext change</td> </tr>\n", "                      <tr><td>(t)op</td> </tr>\n", "                  </table></td> </tr>\n", "    </table>\n", "</body>\n", "\n", "</html>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<h1>Remove empty tags from JSX (accept)</h1>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["\n", "<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Transitional//EN\"\n", "          \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd\">\n", "\n", "<html>\n", "\n", "<head>\n", "    <meta http-equiv=\"Content-Type\"\n", "          content=\"text/html; charset=utf-8\" />\n", "    <title></title>\n", "    <style type=\"text/css\">\n", "        table.diff {font-family:Courier; border:medium;}\n", "        .diff_header {background-color:#e0e0e0}\n", "        td.diff_header {text-align:right}\n", "        .diff_next {background-color:#c0c0c0}\n", "        .diff_add {background-color:#aaffaa}\n", "        .diff_chg {background-color:#ffff77}\n", "        .diff_sub {background-color:#ffaaaa}\n", "    </style>\n", "</head>\n", "\n", "<body>\n", "    \n", "    <table class=\"diff\" id=\"difflib_chg_to2__top\"\n", "           cellspacing=\"0\" cellpadding=\"0\" rules=\"groups\" >\n", "        <colgroup></colgroup> <colgroup></colgroup> <colgroup></colgroup>\n", "        <colgroup></colgroup> <colgroup></colgroup> <colgroup></colgroup>\n", "        \n", "        <tbody>\n", "            <tr><td class=\"diff_next\"><a href=\"#difflib_chg_to2__0\">f</a></td><td class=\"diff_header\" id=\"from2_1\">1</td><td nowrap=\"nowrap\">import&nbsp;{&nbsp;Input&nbsp;}&nbsp;from&nbsp;'@/registry/new-york/ui/input';</td><td class=\"diff_next\"><a href=\"#difflib_chg_to2__0\">f</a></td><td class=\"diff_header\" id=\"to2_1\">1</td><td nowrap=\"nowrap\">import&nbsp;{&nbsp;Input&nbsp;}&nbsp;from&nbsp;'@/registry/new-york/ui/input';</td></tr>\n", "            <tr><td class=\"diff_next\" id=\"difflib_chg_to2__0\"></td><td class=\"diff_header\" id=\"from2_2\">2</td><td nowrap=\"nowrap\">import&nbsp;React&nbsp;from&nbsp;'react';</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to2_2\">2</td><td nowrap=\"nowrap\">import&nbsp;React&nbsp;from&nbsp;'react';</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from2_3\">3</td><td nowrap=\"nowrap\">import&nbsp;{&nbsp;InputWithIcon&nbsp;}&nbsp;from&nbsp;'../ui/input-with-icon';</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to2_3\">3</td><td nowrap=\"nowrap\">import&nbsp;{&nbsp;InputWithIcon&nbsp;}&nbsp;from&nbsp;'../ui/input-with-icon';</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from2_4\">4</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to2_4\">4</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from2_5\">5</td><td nowrap=\"nowrap\">export&nbsp;default&nbsp;function&nbsp;InputIconTrailingIconDemo()&nbsp;{</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to2_5\">5</td><td nowrap=\"nowrap\">export&nbsp;default&nbsp;function&nbsp;InputIconTrailingIconDemo()&nbsp;{</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from2_6\">6</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;(</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to2_6\">6</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;(</td></tr>\n", "            <tr><td class=\"diff_next\"><a href=\"#difflib_chg_to2__1\">n</a></td><td class=\"diff_header\" id=\"from2_7\">7</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;&gt;</span></td><td class=\"diff_next\"><a href=\"#difflib_chg_to2__1\">n</a></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from2_8\">8</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;InputWithIcon</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to2_7\">7</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;InputWithIcon</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from2_9\">9</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;icon={&lt;Input&nbsp;/&gt;}</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to2_8\">8</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;icon={&lt;Input&nbsp;/&gt;}</td></tr>\n", "            <tr><td class=\"diff_next\" id=\"difflib_chg_to2__1\"></td><td class=\"diff_header\" id=\"from2_10\">10</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;inputProps={{</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to2_9\">9</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;inputProps={{</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from2_11\">11</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;placeholder:&nbsp;'Email',</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to2_10\">10</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;placeholder:&nbsp;'Email',</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from2_12\">12</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;}}</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to2_11\">11</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;}}</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from2_13\">13</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;position={'trailing'}</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to2_12\">12</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;position={'trailing'}</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from2_14\">14</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;/&gt;</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to2_13\">13</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;/&gt;</td></tr>\n", "            <tr><td class=\"diff_next\"><a href=\"#difflib_chg_to2__top\">t</a></td><td class=\"diff_header\" id=\"from2_15\">15</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;/&gt;</span></td><td class=\"diff_next\"><a href=\"#difflib_chg_to2__top\">t</a></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from2_16\">16</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;);</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to2_14\">14</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;);</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from2_17\">17</td><td nowrap=\"nowrap\">}</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to2_15\">15</td><td nowrap=\"nowrap\">}</td></tr>\n", "        </tbody>\n", "    </table>\n", "    <table class=\"diff\" summary=\"Legends\">\n", "        <tr> <th colspan=\"2\"> Legends </th> </tr>\n", "        <tr> <td> <table border=\"\" summary=\"Colors\">\n", "                      <tr><th> Colors </th> </tr>\n", "                      <tr><td class=\"diff_add\">&nbsp;Added&nbsp;</td></tr>\n", "                      <tr><td class=\"diff_chg\">Changed</td> </tr>\n", "                      <tr><td class=\"diff_sub\">Deleted</td> </tr>\n", "                  </table></td>\n", "             <td> <table border=\"\" summary=\"Links\">\n", "                      <tr><th colspan=\"2\"> Links </th> </tr>\n", "                      <tr><td>(f)irst change</td> </tr>\n", "                      <tr><td>(n)ext change</td> </tr>\n", "                      <tr><td>(t)op</td> </tr>\n", "                  </table></td> </tr>\n", "    </table>\n", "</body>\n", "\n", "</html>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from IPython.display import display, HTML\n", "import difflib\n", "\n", "def as_diff_html(obj):\n", "    before = obj[\"prefix\"] + obj[\"selected_code\"] + obj[\"suffix\"]\n", "    after = obj[\"prefix\"] + (obj[\"human_annotated_response\"] or obj[\"response\"]) + obj[\"suffix\"]\n", "    return difflib.HtmlDiff().make_file(before.splitlines(), after.splitlines())\n", "\n", "for obj in objs:\n", "    display(HTML(f\"<h1>{obj['human_annotated_instruction'] or obj['instruction']} ({obj['status']})</h1>\"))\n", "    display(HTML(as_diff_html(obj)))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Code Edits"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"text/plain": ["19"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["objs = [json.loads(f.read_text()) for f in (BASE_DIR / \"aitutor-pareto-export\").glob(\"**/*.json\")]\n", "len(objs)"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"text/html": ["<h3>please fix it, make the request always streamed to TARGET_URL, should follow redirect, proxy the request to redirected url instead of just redirect the link to client (accept)</h3>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["\n", "<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Transitional//EN\"\n", "          \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd\">\n", "\n", "<html>\n", "\n", "<head>\n", "    <meta http-equiv=\"Content-Type\"\n", "          content=\"text/html; charset=utf-8\" />\n", "    <title></title>\n", "    <style type=\"text/css\">\n", "        table.diff {font-family:Courier; border:medium;}\n", "        .diff_header {background-color:#e0e0e0}\n", "        td.diff_header {text-align:right}\n", "        .diff_next {background-color:#c0c0c0}\n", "        .diff_add {background-color:#aaffaa}\n", "        .diff_chg {background-color:#ffff77}\n", "        .diff_sub {background-color:#ffaaaa}\n", "    </style>\n", "</head>\n", "\n", "<body>\n", "    \n", "    <table class=\"diff\" id=\"difflib_chg_to13__top\"\n", "           cellspacing=\"0\" cellpadding=\"0\" rules=\"groups\" >\n", "        <colgroup></colgroup> <colgroup></colgroup> <colgroup></colgroup>\n", "        <colgroup></colgroup> <colgroup></colgroup> <colgroup></colgroup>\n", "        \n", "        <tbody>\n", "            <tr><td class=\"diff_next\"><a href=\"#difflib_chg_to13__0\">f</a></td><td class=\"diff_header\" id=\"from13_1\">1</td><td nowrap=\"nowrap\">import&nbsp;os</td><td class=\"diff_next\"><a href=\"#difflib_chg_to13__0\">f</a></td><td class=\"diff_header\" id=\"to13_1\">1</td><td nowrap=\"nowrap\">import&nbsp;os</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from13_2\">2</td><td nowrap=\"nowrap\">from&nbsp;flask&nbsp;import&nbsp;Flask,&nbsp;request,&nbsp;redirect,&nbsp;Response</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to13_2\">2</td><td nowrap=\"nowrap\">from&nbsp;flask&nbsp;import&nbsp;Flask,&nbsp;request,&nbsp;redirect,&nbsp;Response</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from13_3\">3</td><td nowrap=\"nowrap\">import&nbsp;requests</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to13_3\">3</td><td nowrap=\"nowrap\">import&nbsp;requests</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from13_4\">4</td><td nowrap=\"nowrap\">from&nbsp;dotenv&nbsp;import&nbsp;load_dotenv</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to13_4\">4</td><td nowrap=\"nowrap\">from&nbsp;dotenv&nbsp;import&nbsp;load_dotenv</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from13_5\">5</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to13_5\">5</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from13_6\">6</td><td nowrap=\"nowrap\">load_dotenv()&nbsp;&nbsp;#&nbsp;Load&nbsp;environment&nbsp;variables&nbsp;from&nbsp;.env&nbsp;file</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to13_6\">6</td><td nowrap=\"nowrap\">load_dotenv()&nbsp;&nbsp;#&nbsp;Load&nbsp;environment&nbsp;variables&nbsp;from&nbsp;.env&nbsp;file</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from13_7\">7</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to13_7\">7</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from13_8\">8</td><td nowrap=\"nowrap\">app&nbsp;=&nbsp;Flask(__name__)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to13_8\">8</td><td nowrap=\"nowrap\">app&nbsp;=&nbsp;Flask(__name__)</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from13_9\">9</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to13_9\">9</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from13_10\">10</td><td nowrap=\"nowrap\">TARGET_URL&nbsp;=&nbsp;os.getenv('TARGET_URL',&nbsp;'https://github.com')</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to13_10\">10</td><td nowrap=\"nowrap\">TARGET_URL&nbsp;=&nbsp;os.getenv('TARGET_URL',&nbsp;'https://github.com')</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from13_11\">11</td><td nowrap=\"nowrap\">PATH_START&nbsp;=&nbsp;os.getenv('PATH_START',&nbsp;'/electerm/electerm/releases/download/')</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to13_11\">11</td><td nowrap=\"nowrap\">PATH_START&nbsp;=&nbsp;os.getenv('PATH_START',&nbsp;'/electerm/electerm/releases/download/')</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from13_12\">12</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to13_12\">12</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from13_13\">13</td><td nowrap=\"nowrap\">print(TARGET_URL,&nbsp;PATH_START)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to13_13\">13</td><td nowrap=\"nowrap\">print(TARGET_URL,&nbsp;PATH_START)</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from13_14\">14</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to13_14\">14</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\" id=\"difflib_chg_to13__0\"></td><td class=\"diff_header\" id=\"from13_15\">15</td><td nowrap=\"nowrap\">@app.route('/',&nbsp;defaults={'path':&nbsp;''})</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to13_15\">15</td><td nowrap=\"nowrap\">@app.route('/',&nbsp;defaults={'path':&nbsp;''})</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from13_16\">16</td><td nowrap=\"nowrap\">@app.route('/&lt;path:path&gt;')</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to13_16\">16</td><td nowrap=\"nowrap\">@app.route('/&lt;path:path&gt;')</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from13_17\">17</td><td nowrap=\"nowrap\">def&nbsp;proxy(path):</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to13_17\">17</td><td nowrap=\"nowrap\">def&nbsp;proxy(path):</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from13_18\">18</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;print(path)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to13_18\">18</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;print(path)</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from13_19\">19</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;if&nbsp;request.method&nbsp;==&nbsp;'GET'&nbsp;and&nbsp;path.startswith(PATH_START):</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to13_19\">19</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;if&nbsp;request.method&nbsp;==&nbsp;'GET'&nbsp;and&nbsp;path.startswith(PATH_START):</td></tr>\n", "            <tr><td class=\"diff_next\"><a href=\"#difflib_chg_to13__top\">t</a></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"><a href=\"#difflib_chg_to13__top\">t</a></td><td class=\"diff_header\" id=\"to13_20\">20</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;#&nbsp;Make&nbsp;the&nbsp;request&nbsp;always&nbsp;streamed&nbsp;to&nbsp;TARGET_URL,&nbsp;should&nbsp;follow&nbsp;redirect</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from13_20\">20</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;response&nbsp;=&nbsp;requests.get(TARGET_URL&nbsp;+&nbsp;'/'&nbsp;+&nbsp;path,&nbsp;stream=True)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to13_21\">21</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;response&nbsp;=&nbsp;requests.get(TARGET_URL&nbsp;+&nbsp;'/'&nbsp;+&nbsp;path,&nbsp;stream=True<span class=\"diff_add\">,&nbsp;allow_redirects=True</span>)</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from13_21\">21</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from13_22\">22</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;#&nbsp;Check&nbsp;if&nbsp;the&nbsp;response&nbsp;is&nbsp;a&nbsp;redirect</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from13_23\">23</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;if&nbsp;response.status_code&nbsp;==&nbsp;302:</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from13_24\">24</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;one_year_in_seconds&nbsp;=&nbsp;365&nbsp;*&nbsp;24&nbsp;*&nbsp;60&nbsp;*&nbsp;60</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from13_25\">25</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;cache_control&nbsp;=&nbsp;f'public,&nbsp;max-age={one_year_in_seconds}'</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from13_26\">26</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from13_27\">27</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;#&nbsp;Set&nbsp;the&nbsp;Cache-Control&nbsp;and&nbsp;Expires&nbsp;headers</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from13_28\">28</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;response.headers['Cache-Control']&nbsp;=&nbsp;cache_control</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from13_29\">29</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;response.headers['Expires']&nbsp;=&nbsp;one_year_in_seconds</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from13_30\">30</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from13_31\">31</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;redirect(response.headers['location'],&nbsp;code=302)</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from13_32\">32</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to13_22\">22</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from13_33\">33</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;def&nbsp;generate():</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to13_23\">23</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;def&nbsp;generate():</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from13_34\">34</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;for&nbsp;chunk&nbsp;in&nbsp;response.iter_content(chunk_size=8192):</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to13_24\">24</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;for&nbsp;chunk&nbsp;in&nbsp;response.iter_content(chunk_size=8192):</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from13_35\">35</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;yield&nbsp;chunk</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to13_25\">25</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;yield&nbsp;chunk</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from13_36\">36</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to13_26\">26</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from13_37\">37</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;#&nbsp;Return&nbsp;the&nbsp;proxied&nbsp;response</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to13_27\">27</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;#&nbsp;Return&nbsp;the&nbsp;proxied&nbsp;response</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from13_38\">38</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;Response(generate(),&nbsp;headers=response.headers,&nbsp;status=response.status_code)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to13_28\">28</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;Response(generate(),&nbsp;headers=response.headers,&nbsp;status=response.status_code)</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from13_39\">39</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;else:</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to13_29\">29</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;else:</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from13_40\">40</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;'Not&nbsp;Found',&nbsp;404</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to13_30\">30</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;'Not&nbsp;Found',&nbsp;404</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from13_41\">41</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to13_31\">31</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from13_42\">42</td><td nowrap=\"nowrap\">@app.route('/test')</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to13_32\">32</td><td nowrap=\"nowrap\">@app.route('/test')</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from13_43\">43</td><td nowrap=\"nowrap\">def&nbsp;test():</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to13_33\">33</td><td nowrap=\"nowrap\">def&nbsp;test():</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from13_44\">44</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;'ok'</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to13_34\">34</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;'ok'</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from13_45\">45</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to13_35\">35</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from13_46\">46</td><td nowrap=\"nowrap\">if&nbsp;__name__&nbsp;==&nbsp;\"__main__\":</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to13_36\">36</td><td nowrap=\"nowrap\">if&nbsp;__name__&nbsp;==&nbsp;\"__main__\":</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from13_47\">47</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;HOST&nbsp;=&nbsp;os.getenv('HOST',&nbsp;'127.0.0.1')</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to13_37\">37</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;HOST&nbsp;=&nbsp;os.getenv('HOST',&nbsp;'127.0.0.1')</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from13_48\">48</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;PORT&nbsp;=&nbsp;int(os.getenv('PORT',&nbsp;3000))</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to13_38\">38</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;PORT&nbsp;=&nbsp;int(os.getenv('PORT',&nbsp;3000))</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from13_49\">49</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to13_39\">39</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from13_50\">50</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;app.run(host=HOST,&nbsp;port=PORT)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to13_40\">40</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;app.run(host=HOST,&nbsp;port=PORT)</td></tr>\n", "        </tbody>\n", "    </table>\n", "    <table class=\"diff\" summary=\"Legends\">\n", "        <tr> <th colspan=\"2\"> Legends </th> </tr>\n", "        <tr> <td> <table border=\"\" summary=\"Colors\">\n", "                      <tr><th> Colors </th> </tr>\n", "                      <tr><td class=\"diff_add\">&nbsp;Added&nbsp;</td></tr>\n", "                      <tr><td class=\"diff_chg\">Changed</td> </tr>\n", "                      <tr><td class=\"diff_sub\">Deleted</td> </tr>\n", "                  </table></td>\n", "             <td> <table border=\"\" summary=\"Links\">\n", "                      <tr><th colspan=\"2\"> Links </th> </tr>\n", "                      <tr><td>(f)irst change</td> </tr>\n", "                      <tr><td>(n)ext change</td> </tr>\n", "                      <tr><td>(t)op</td> </tr>\n", "                  </table></td> </tr>\n", "    </table>\n", "</body>\n", "\n", "</html>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<h3>response = requests.get(response.headers['Location'], stream=True) seems only error response: net::ERR_CONTENT_DECODING_FAILED , how to fix? (accept-annotation)</h3>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["\n", "<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Transitional//EN\"\n", "          \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd\">\n", "\n", "<html>\n", "\n", "<head>\n", "    <meta http-equiv=\"Content-Type\"\n", "          content=\"text/html; charset=utf-8\" />\n", "    <title></title>\n", "    <style type=\"text/css\">\n", "        table.diff {font-family:Courier; border:medium;}\n", "        .diff_header {background-color:#e0e0e0}\n", "        td.diff_header {text-align:right}\n", "        .diff_next {background-color:#c0c0c0}\n", "        .diff_add {background-color:#aaffaa}\n", "        .diff_chg {background-color:#ffff77}\n", "        .diff_sub {background-color:#ffaaaa}\n", "    </style>\n", "</head>\n", "\n", "<body>\n", "    \n", "    <table class=\"diff\" id=\"difflib_chg_to14__top\"\n", "           cellspacing=\"0\" cellpadding=\"0\" rules=\"groups\" >\n", "        <colgroup></colgroup> <colgroup></colgroup> <colgroup></colgroup>\n", "        <colgroup></colgroup> <colgroup></colgroup> <colgroup></colgroup>\n", "        \n", "        <tbody>\n", "            <tr><td class=\"diff_next\"><a href=\"#difflib_chg_to14__0\">f</a></td><td class=\"diff_header\" id=\"from14_1\">1</td><td nowrap=\"nowrap\">import&nbsp;os</td><td class=\"diff_next\"><a href=\"#difflib_chg_to14__0\">f</a></td><td class=\"diff_header\" id=\"to14_1\">1</td><td nowrap=\"nowrap\">import&nbsp;os</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from14_2\">2</td><td nowrap=\"nowrap\">from&nbsp;flask&nbsp;import&nbsp;Flask,&nbsp;request,&nbsp;redirect,&nbsp;Response</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to14_2\">2</td><td nowrap=\"nowrap\">from&nbsp;flask&nbsp;import&nbsp;Flask,&nbsp;request,&nbsp;redirect,&nbsp;Response</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from14_3\">3</td><td nowrap=\"nowrap\">import&nbsp;requests</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to14_3\">3</td><td nowrap=\"nowrap\">import&nbsp;requests</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from14_4\">4</td><td nowrap=\"nowrap\">from&nbsp;dotenv&nbsp;import&nbsp;load_dotenv</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to14_4\">4</td><td nowrap=\"nowrap\">from&nbsp;dotenv&nbsp;import&nbsp;load_dotenv</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from14_5\">5</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to14_5\">5</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from14_6\">6</td><td nowrap=\"nowrap\">load_dotenv()&nbsp;&nbsp;#&nbsp;Load&nbsp;environment&nbsp;variables&nbsp;from&nbsp;.env&nbsp;file</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to14_6\">6</td><td nowrap=\"nowrap\">load_dotenv()&nbsp;&nbsp;#&nbsp;Load&nbsp;environment&nbsp;variables&nbsp;from&nbsp;.env&nbsp;file</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from14_7\">7</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to14_7\">7</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from14_8\">8</td><td nowrap=\"nowrap\">app&nbsp;=&nbsp;Flask(__name__)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to14_8\">8</td><td nowrap=\"nowrap\">app&nbsp;=&nbsp;Flask(__name__)</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from14_9\">9</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to14_9\">9</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from14_10\">10</td><td nowrap=\"nowrap\">TARGET_URL&nbsp;=&nbsp;os.getenv('TARGET_URL',&nbsp;'https://github.com')</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to14_10\">10</td><td nowrap=\"nowrap\">TARGET_URL&nbsp;=&nbsp;os.getenv('TARGET_URL',&nbsp;'https://github.com')</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from14_11\">11</td><td nowrap=\"nowrap\">PATH_START&nbsp;=&nbsp;os.getenv('PATH_START',&nbsp;'/electerm/electerm/releases/download/')</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to14_11\">11</td><td nowrap=\"nowrap\">PATH_START&nbsp;=&nbsp;os.getenv('PATH_START',&nbsp;'/electerm/electerm/releases/download/')</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from14_12\">12</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to14_12\">12</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from14_13\">13</td><td nowrap=\"nowrap\">print(TARGET_URL,&nbsp;PATH_START)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to14_13\">13</td><td nowrap=\"nowrap\">print(TARGET_URL,&nbsp;PATH_START)</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from14_14\">14</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to14_14\">14</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from14_15\">15</td><td nowrap=\"nowrap\">@app.route('/',&nbsp;defaults={'path':&nbsp;''})</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to14_15\">15</td><td nowrap=\"nowrap\">@app.route('/',&nbsp;defaults={'path':&nbsp;''})</td></tr>\n", "            <tr><td class=\"diff_next\" id=\"difflib_chg_to14__0\"></td><td class=\"diff_header\" id=\"from14_16\">16</td><td nowrap=\"nowrap\">@app.route('/&lt;path:path&gt;')</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to14_16\">16</td><td nowrap=\"nowrap\">@app.route('/&lt;path:path&gt;')</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from14_17\">17</td><td nowrap=\"nowrap\">def&nbsp;proxy(path):</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to14_17\">17</td><td nowrap=\"nowrap\">def&nbsp;proxy(path):</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from14_18\">18</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;print(path)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to14_18\">18</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;print(path)</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from14_19\">19</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;if&nbsp;request.method&nbsp;==&nbsp;'GET'&nbsp;and&nbsp;('/'&nbsp;+&nbsp;path).startswith(PATH_START):</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to14_19\">19</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;if&nbsp;request.method&nbsp;==&nbsp;'GET'&nbsp;and&nbsp;('/'&nbsp;+&nbsp;path).startswith(PATH_START):</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from14_20\">20</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;#&nbsp;Make&nbsp;the&nbsp;request&nbsp;to&nbsp;TARGET_URL,&nbsp;do&nbsp;not&nbsp;automatically&nbsp;follow&nbsp;redirect</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to14_20\">20</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;#&nbsp;Make&nbsp;the&nbsp;request&nbsp;to&nbsp;TARGET_URL,&nbsp;do&nbsp;not&nbsp;automatically&nbsp;follow&nbsp;redirect</td></tr>\n", "            <tr><td class=\"diff_next\" id=\"difflib_chg_to14__1\"><a href=\"#difflib_chg_to14__1\">n</a></td><td class=\"diff_header\" id=\"from14_21\">21</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;response&nbsp;=&nbsp;requests.get(TARGET_URL&nbsp;+&nbsp;'/'&nbsp;+&nbsp;path,&nbsp;stream=True,&nbsp;allow_redirects=False)</td><td class=\"diff_next\"><a href=\"#difflib_chg_to14__1\">n</a></td><td class=\"diff_header\" id=\"to14_21\">21</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;response&nbsp;=&nbsp;requests.get(TARGET_URL&nbsp;+&nbsp;'/'&nbsp;+&nbsp;path,&nbsp;stream=True,&nbsp;allow_redirects=False<span class=\"diff_add\">,&nbsp;headers={'Accept-Encoding':&nbsp;'identity'}</span>)</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from14_22\">22</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to14_22\">22</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from14_23\">23</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;#&nbsp;If&nbsp;the&nbsp;response&nbsp;is&nbsp;a&nbsp;redirect,&nbsp;follow&nbsp;the&nbsp;redirect&nbsp;and&nbsp;get&nbsp;the&nbsp;new&nbsp;response</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to14_23\">23</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;#&nbsp;If&nbsp;the&nbsp;response&nbsp;is&nbsp;a&nbsp;redirect,&nbsp;follow&nbsp;the&nbsp;redirect&nbsp;and&nbsp;get&nbsp;the&nbsp;new&nbsp;response</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from14_24\">24</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;if&nbsp;response.status_code&nbsp;in&nbsp;(301,&nbsp;302,&nbsp;303,&nbsp;307,&nbsp;308):</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to14_24\">24</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;if&nbsp;response.status_code&nbsp;in&nbsp;(301,&nbsp;302,&nbsp;303,&nbsp;307,&nbsp;308):</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from14_25\">25</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;print('path',&nbsp;response.headers['Location'])</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to14_25\">25</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;print('path',&nbsp;response.headers['Location'])</td></tr>\n", "            <tr><td class=\"diff_next\"><a href=\"#difflib_chg_to14__2\">n</a></td><td class=\"diff_header\" id=\"from14_26\">26</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;response&nbsp;=&nbsp;requests.get(response.headers['Location'],&nbsp;stream=True)</td><td class=\"diff_next\"><a href=\"#difflib_chg_to14__2\">n</a></td><td class=\"diff_header\" id=\"to14_26\">26</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;response&nbsp;=&nbsp;requests.get(response.headers['Location'],&nbsp;stream=True<span class=\"diff_add\">,&nbsp;headers={'Accept-Encoding':&nbsp;'identity'}</span>)</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from14_27\">27</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to14_27\">27</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\" id=\"difflib_chg_to14__2\"></td><td class=\"diff_header\" id=\"from14_28\">28</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;def&nbsp;generate():</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to14_28\">28</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;def&nbsp;generate():</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from14_29\">29</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;for&nbsp;chunk&nbsp;in&nbsp;response.iter_content(chunk_size=8192):</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to14_29\">29</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;for&nbsp;chunk&nbsp;in&nbsp;response.iter_content(chunk_size=8192):</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from14_30\">30</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;yield&nbsp;chunk</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to14_30\">30</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;yield&nbsp;chunk</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from14_31\">31</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to14_31\">31</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from14_32\">32</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;#&nbsp;Return&nbsp;the&nbsp;proxied&nbsp;response</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to14_32\">32</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;#&nbsp;Return&nbsp;the&nbsp;proxied&nbsp;response</td></tr>\n", "            <tr><td class=\"diff_next\"><a href=\"#difflib_chg_to14__top\">t</a></td><td class=\"diff_header\" id=\"from14_33\">33</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;Response(generate(),&nbsp;headers=dict(response.headers),&nbsp;status=response.status_code)</td><td class=\"diff_next\"><a href=\"#difflib_chg_to14__top\">t</a></td><td class=\"diff_header\" id=\"to14_33\">33</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;Response(generate(),&nbsp;headers=dict(response.headers),&nbsp;status=response.status_code<span class=\"diff_add\">,&nbsp;direct_passthrough=True</span>)</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from14_34\">34</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;else:</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to14_34\">34</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;else:</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from14_35\">35</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;'Not&nbsp;Found',&nbsp;404</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to14_35\">35</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;'Not&nbsp;Found',&nbsp;404</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from14_36\">36</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to14_36\">36</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from14_37\">37</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to14_37\">37</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from14_38\">38</td><td nowrap=\"nowrap\">@app.route('/test')</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to14_38\">38</td><td nowrap=\"nowrap\">@app.route('/test')</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from14_39\">39</td><td nowrap=\"nowrap\">def&nbsp;test():</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to14_39\">39</td><td nowrap=\"nowrap\">def&nbsp;test():</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from14_40\">40</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;'ok'</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to14_40\">40</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;'ok'</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from14_41\">41</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to14_41\">41</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from14_42\">42</td><td nowrap=\"nowrap\">if&nbsp;__name__&nbsp;==&nbsp;\"__main__\":</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to14_42\">42</td><td nowrap=\"nowrap\">if&nbsp;__name__&nbsp;==&nbsp;\"__main__\":</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from14_43\">43</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;HOST&nbsp;=&nbsp;os.getenv('HOST',&nbsp;'127.0.0.1')</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to14_43\">43</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;HOST&nbsp;=&nbsp;os.getenv('HOST',&nbsp;'127.0.0.1')</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from14_44\">44</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;PORT&nbsp;=&nbsp;int(os.getenv('PORT',&nbsp;3000))</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to14_44\">44</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;PORT&nbsp;=&nbsp;int(os.getenv('PORT',&nbsp;3000))</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from14_45\">45</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to14_45\">45</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from14_46\">46</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;app.run(host=HOST,&nbsp;port=PORT)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to14_46\">46</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;app.run(host=HOST,&nbsp;port=PORT)</td></tr>\n", "        </tbody>\n", "    </table>\n", "    <table class=\"diff\" summary=\"Legends\">\n", "        <tr> <th colspan=\"2\"> Legends </th> </tr>\n", "        <tr> <td> <table border=\"\" summary=\"Colors\">\n", "                      <tr><th> Colors </th> </tr>\n", "                      <tr><td class=\"diff_add\">&nbsp;Added&nbsp;</td></tr>\n", "                      <tr><td class=\"diff_chg\">Changed</td> </tr>\n", "                      <tr><td class=\"diff_sub\">Deleted</td> </tr>\n", "                  </table></td>\n", "             <td> <table border=\"\" summary=\"Links\">\n", "                      <tr><th colspan=\"2\"> Links </th> </tr>\n", "                      <tr><td>(f)irst change</td> </tr>\n", "                      <tr><td>(n)ext change</td> </tr>\n", "                      <tr><td>(t)op</td> </tr>\n", "                  </table></td> </tr>\n", "    </table>\n", "</body>\n", "\n", "</html>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<h3>can you fix the follow redirect, when target url redirect to another url(different domain), it did not follow redirect correctly (reject)</h3>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["\n", "<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Transitional//EN\"\n", "          \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd\">\n", "\n", "<html>\n", "\n", "<head>\n", "    <meta http-equiv=\"Content-Type\"\n", "          content=\"text/html; charset=utf-8\" />\n", "    <title></title>\n", "    <style type=\"text/css\">\n", "        table.diff {font-family:Courier; border:medium;}\n", "        .diff_header {background-color:#e0e0e0}\n", "        td.diff_header {text-align:right}\n", "        .diff_next {background-color:#c0c0c0}\n", "        .diff_add {background-color:#aaffaa}\n", "        .diff_chg {background-color:#ffff77}\n", "        .diff_sub {background-color:#ffaaaa}\n", "    </style>\n", "</head>\n", "\n", "<body>\n", "    \n", "    <table class=\"diff\" id=\"difflib_chg_to15__top\"\n", "           cellspacing=\"0\" cellpadding=\"0\" rules=\"groups\" >\n", "        <colgroup></colgroup> <colgroup></colgroup> <colgroup></colgroup>\n", "        <colgroup></colgroup> <colgroup></colgroup> <colgroup></colgroup>\n", "        \n", "        <tbody>\n", "            <tr><td class=\"diff_next\"><a href=\"#difflib_chg_to15__0\">f</a></td><td class=\"diff_header\" id=\"from15_1\">1</td><td nowrap=\"nowrap\">import&nbsp;os</td><td class=\"diff_next\"><a href=\"#difflib_chg_to15__0\">f</a></td><td class=\"diff_header\" id=\"to15_1\">1</td><td nowrap=\"nowrap\">import&nbsp;os</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from15_2\">2</td><td nowrap=\"nowrap\">from&nbsp;flask&nbsp;import&nbsp;Flask,&nbsp;request,&nbsp;redirect,&nbsp;Response</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to15_2\">2</td><td nowrap=\"nowrap\">from&nbsp;flask&nbsp;import&nbsp;Flask,&nbsp;request,&nbsp;redirect,&nbsp;Response</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from15_3\">3</td><td nowrap=\"nowrap\">import&nbsp;requests</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to15_3\">3</td><td nowrap=\"nowrap\">import&nbsp;requests</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from15_4\">4</td><td nowrap=\"nowrap\">from&nbsp;dotenv&nbsp;import&nbsp;load_dotenv</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to15_4\">4</td><td nowrap=\"nowrap\">from&nbsp;dotenv&nbsp;import&nbsp;load_dotenv</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from15_5\">5</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to15_5\">5</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from15_6\">6</td><td nowrap=\"nowrap\">load_dotenv()&nbsp;&nbsp;#&nbsp;Load&nbsp;environment&nbsp;variables&nbsp;from&nbsp;.env&nbsp;file</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to15_6\">6</td><td nowrap=\"nowrap\">load_dotenv()&nbsp;&nbsp;#&nbsp;Load&nbsp;environment&nbsp;variables&nbsp;from&nbsp;.env&nbsp;file</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from15_7\">7</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to15_7\">7</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from15_8\">8</td><td nowrap=\"nowrap\">app&nbsp;=&nbsp;Flask(__name__)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to15_8\">8</td><td nowrap=\"nowrap\">app&nbsp;=&nbsp;Flask(__name__)</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from15_9\">9</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to15_9\">9</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from15_10\">10</td><td nowrap=\"nowrap\">TARGET_URL&nbsp;=&nbsp;os.getenv('TARGET_URL',&nbsp;'https://github.com')</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to15_10\">10</td><td nowrap=\"nowrap\">TARGET_URL&nbsp;=&nbsp;os.getenv('TARGET_URL',&nbsp;'https://github.com')</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from15_11\">11</td><td nowrap=\"nowrap\">PATH_START&nbsp;=&nbsp;os.getenv('PATH_START',&nbsp;'/electerm/electerm/releases/download/')</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to15_11\">11</td><td nowrap=\"nowrap\">PATH_START&nbsp;=&nbsp;os.getenv('PATH_START',&nbsp;'/electerm/electerm/releases/download/')</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from15_12\">12</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to15_12\">12</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from15_13\">13</td><td nowrap=\"nowrap\">print(TARGET_URL,&nbsp;PATH_START)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to15_13\">13</td><td nowrap=\"nowrap\">print(TARGET_URL,&nbsp;PATH_START)</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from15_14\">14</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to15_14\">14</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from15_15\">15</td><td nowrap=\"nowrap\">@app.route('/',&nbsp;defaults={'path':&nbsp;''})</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to15_15\">15</td><td nowrap=\"nowrap\">@app.route('/',&nbsp;defaults={'path':&nbsp;''})</td></tr>\n", "            <tr><td class=\"diff_next\" id=\"difflib_chg_to15__0\"></td><td class=\"diff_header\" id=\"from15_16\">16</td><td nowrap=\"nowrap\">@app.route('/&lt;path:path&gt;')</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to15_16\">16</td><td nowrap=\"nowrap\">@app.route('/&lt;path:path&gt;')</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from15_17\">17</td><td nowrap=\"nowrap\">def&nbsp;proxy(path):</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to15_17\">17</td><td nowrap=\"nowrap\">def&nbsp;proxy(path):</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from15_18\">18</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;print(path)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to15_18\">18</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;print(path)</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from15_19\">19</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;if&nbsp;request.method&nbsp;==&nbsp;'GET'&nbsp;and&nbsp;('/'&nbsp;+&nbsp;path).startswith(PATH_START):</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to15_19\">19</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;if&nbsp;request.method&nbsp;==&nbsp;'GET'&nbsp;and&nbsp;('/'&nbsp;+&nbsp;path).startswith(PATH_START):</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from15_20\">20</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;#&nbsp;Make&nbsp;the&nbsp;request&nbsp;always&nbsp;streamed&nbsp;to&nbsp;TARGET_URL,&nbsp;should&nbsp;follow&nbsp;redirect</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to15_20\">20</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;#&nbsp;Make&nbsp;the&nbsp;request&nbsp;always&nbsp;streamed&nbsp;to&nbsp;TARGET_URL,&nbsp;should&nbsp;follow&nbsp;redirect</td></tr>\n", "            <tr><td class=\"diff_next\"><a href=\"#difflib_chg_to15__top\">t</a></td><td class=\"diff_header\" id=\"from15_21\">21</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;response&nbsp;=&nbsp;requests.get(TARGET_URL&nbsp;+&nbsp;'/'&nbsp;+&nbsp;path,&nbsp;stream=True<span class=\"diff_sub\">,&nbsp;allow_redirects=True</span>)</td><td class=\"diff_next\"><a href=\"#difflib_chg_to15__top\">t</a></td><td class=\"diff_header\" id=\"to15_21\">21</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;response&nbsp;=&nbsp;requests.get(TARGET_URL&nbsp;+&nbsp;'/'&nbsp;+&nbsp;path,&nbsp;stream=True)</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from15_22\">22</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to15_22\">22</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from15_23\">23</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;def&nbsp;generate():</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to15_23\">23</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;def&nbsp;generate():</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from15_24\">24</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;for&nbsp;chunk&nbsp;in&nbsp;response.iter_content(chunk_size=8192):</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to15_24\">24</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;for&nbsp;chunk&nbsp;in&nbsp;response.iter_content(chunk_size=8192):</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from15_25\">25</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;yield&nbsp;chunk</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to15_25\">25</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;yield&nbsp;chunk</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from15_26\">26</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to15_26\">26</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from15_27\">27</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;#&nbsp;Return&nbsp;the&nbsp;proxied&nbsp;response</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to15_27\">27</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;#&nbsp;Return&nbsp;the&nbsp;proxied&nbsp;response</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from15_28\">28</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;Response(generate(),&nbsp;headers=dict(response.headers),&nbsp;status=response.status_code)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to15_28\">28</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;Response(generate(),&nbsp;headers=dict(response.headers),&nbsp;status=response.status_code)</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from15_29\">29</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;else:</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to15_29\">29</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;else:</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from15_30\">30</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;'Not&nbsp;Found',&nbsp;404</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to15_30\">30</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;'Not&nbsp;Found',&nbsp;404</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from15_31\">31</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to15_31\">31</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from15_32\">32</td><td nowrap=\"nowrap\">@app.route('/test')</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to15_32\">32</td><td nowrap=\"nowrap\">@app.route('/test')</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from15_33\">33</td><td nowrap=\"nowrap\">def&nbsp;test():</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to15_33\">33</td><td nowrap=\"nowrap\">def&nbsp;test():</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from15_34\">34</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;'ok'</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to15_34\">34</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;'ok'</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from15_35\">35</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to15_35\">35</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from15_36\">36</td><td nowrap=\"nowrap\">if&nbsp;__name__&nbsp;==&nbsp;\"__main__\":</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to15_36\">36</td><td nowrap=\"nowrap\">if&nbsp;__name__&nbsp;==&nbsp;\"__main__\":</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from15_37\">37</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;HOST&nbsp;=&nbsp;os.getenv('HOST',&nbsp;'127.0.0.1')</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to15_37\">37</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;HOST&nbsp;=&nbsp;os.getenv('HOST',&nbsp;'127.0.0.1')</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from15_38\">38</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;PORT&nbsp;=&nbsp;int(os.getenv('PORT',&nbsp;3000))</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to15_38\">38</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;PORT&nbsp;=&nbsp;int(os.getenv('PORT',&nbsp;3000))</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from15_39\">39</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to15_39\">39</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from15_40\">40</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;app.run(host=HOST,&nbsp;port=PORT)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to15_40\">40</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;app.run(host=HOST,&nbsp;port=PORT)</td></tr>\n", "        </tbody>\n", "    </table>\n", "    <table class=\"diff\" summary=\"Legends\">\n", "        <tr> <th colspan=\"2\"> Legends </th> </tr>\n", "        <tr> <td> <table border=\"\" summary=\"Colors\">\n", "                      <tr><th> Colors </th> </tr>\n", "                      <tr><td class=\"diff_add\">&nbsp;Added&nbsp;</td></tr>\n", "                      <tr><td class=\"diff_chg\">Changed</td> </tr>\n", "                      <tr><td class=\"diff_sub\">Deleted</td> </tr>\n", "                  </table></td>\n", "             <td> <table border=\"\" summary=\"Links\">\n", "                      <tr><th colspan=\"2\"> Links </th> </tr>\n", "                      <tr><td>(f)irst change</td> </tr>\n", "                      <tr><td>(n)ext change</td> </tr>\n", "                      <tr><td>(t)op</td> </tr>\n", "                  </table></td> </tr>\n", "    </table>\n", "</body>\n", "\n", "</html>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<h3>print TARGET_URL PATH_START (accept)</h3>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["\n", "<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Transitional//EN\"\n", "          \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd\">\n", "\n", "<html>\n", "\n", "<head>\n", "    <meta http-equiv=\"Content-Type\"\n", "          content=\"text/html; charset=utf-8\" />\n", "    <title></title>\n", "    <style type=\"text/css\">\n", "        table.diff {font-family:Courier; border:medium;}\n", "        .diff_header {background-color:#e0e0e0}\n", "        td.diff_header {text-align:right}\n", "        .diff_next {background-color:#c0c0c0}\n", "        .diff_add {background-color:#aaffaa}\n", "        .diff_chg {background-color:#ffff77}\n", "        .diff_sub {background-color:#ffaaaa}\n", "    </style>\n", "</head>\n", "\n", "<body>\n", "    \n", "    <table class=\"diff\" id=\"difflib_chg_to16__top\"\n", "           cellspacing=\"0\" cellpadding=\"0\" rules=\"groups\" >\n", "        <colgroup></colgroup> <colgroup></colgroup> <colgroup></colgroup>\n", "        <colgroup></colgroup> <colgroup></colgroup> <colgroup></colgroup>\n", "        \n", "        <tbody>\n", "            <tr><td class=\"diff_next\"><a href=\"#difflib_chg_to16__0\">f</a></td><td class=\"diff_header\" id=\"from16_1\">1</td><td nowrap=\"nowrap\">import&nbsp;os</td><td class=\"diff_next\"><a href=\"#difflib_chg_to16__0\">f</a></td><td class=\"diff_header\" id=\"to16_1\">1</td><td nowrap=\"nowrap\">import&nbsp;os</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from16_2\">2</td><td nowrap=\"nowrap\">from&nbsp;flask&nbsp;import&nbsp;Flask,&nbsp;request,&nbsp;redirect,&nbsp;Response</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to16_2\">2</td><td nowrap=\"nowrap\">from&nbsp;flask&nbsp;import&nbsp;Flask,&nbsp;request,&nbsp;redirect,&nbsp;Response</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from16_3\">3</td><td nowrap=\"nowrap\">import&nbsp;requests</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to16_3\">3</td><td nowrap=\"nowrap\">import&nbsp;requests</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from16_4\">4</td><td nowrap=\"nowrap\">from&nbsp;dotenv&nbsp;import&nbsp;load_dotenv</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to16_4\">4</td><td nowrap=\"nowrap\">from&nbsp;dotenv&nbsp;import&nbsp;load_dotenv</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from16_5\">5</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to16_5\">5</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from16_6\">6</td><td nowrap=\"nowrap\">load_dotenv()&nbsp;&nbsp;#&nbsp;Load&nbsp;environment&nbsp;variables&nbsp;from&nbsp;.env&nbsp;file</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to16_6\">6</td><td nowrap=\"nowrap\">load_dotenv()&nbsp;&nbsp;#&nbsp;Load&nbsp;environment&nbsp;variables&nbsp;from&nbsp;.env&nbsp;file</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from16_7\">7</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to16_7\">7</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\" id=\"difflib_chg_to16__0\"></td><td class=\"diff_header\" id=\"from16_8\">8</td><td nowrap=\"nowrap\">app&nbsp;=&nbsp;Flask(__name__)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to16_8\">8</td><td nowrap=\"nowrap\">app&nbsp;=&nbsp;Flask(__name__)</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from16_9\">9</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to16_9\">9</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from16_10\">10</td><td nowrap=\"nowrap\">TARGET_URL&nbsp;=&nbsp;os.getenv('TARGET_URL',&nbsp;'https://github.com')</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to16_10\">10</td><td nowrap=\"nowrap\">TARGET_URL&nbsp;=&nbsp;os.getenv('TARGET_URL',&nbsp;'https://github.com')</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from16_11\">11</td><td nowrap=\"nowrap\">PATH_START&nbsp;=&nbsp;os.getenv('PATH_START',&nbsp;'/electerm/electerm/releases/download/')</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to16_11\">11</td><td nowrap=\"nowrap\">PATH_START&nbsp;=&nbsp;os.getenv('PATH_START',&nbsp;'/electerm/electerm/releases/download/')</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from16_12\">12</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to16_12\">12</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"><a href=\"#difflib_chg_to16__top\">t</a></td><td class=\"diff_header\" id=\"from16_13\">13</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">#&nbsp;</span>print<span class=\"diff_chg\">&nbsp;</span>TARGET_URL&nbsp;PATH_START</td><td class=\"diff_next\"><a href=\"#difflib_chg_to16__top\">t</a></td><td class=\"diff_header\" id=\"to16_13\">13</td><td nowrap=\"nowrap\">print<span class=\"diff_chg\">(</span>TARGET_URL<span class=\"diff_add\">,</span>&nbsp;PATH_START<span class=\"diff_add\">)</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from16_14\">14</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to16_14\">14</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from16_15\">15</td><td nowrap=\"nowrap\">@app.route('/',&nbsp;defaults={'path':&nbsp;''})</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to16_15\">15</td><td nowrap=\"nowrap\">@app.route('/',&nbsp;defaults={'path':&nbsp;''})</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from16_16\">16</td><td nowrap=\"nowrap\">@app.route('/&lt;path:path&gt;')</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to16_16\">16</td><td nowrap=\"nowrap\">@app.route('/&lt;path:path&gt;')</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from16_17\">17</td><td nowrap=\"nowrap\">def&nbsp;proxy(path):</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to16_17\">17</td><td nowrap=\"nowrap\">def&nbsp;proxy(path):</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from16_18\">18</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;if&nbsp;request.method&nbsp;==&nbsp;'GET'&nbsp;and&nbsp;path.startswith(PATH_START):</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to16_18\">18</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;if&nbsp;request.method&nbsp;==&nbsp;'GET'&nbsp;and&nbsp;path.startswith(PATH_START):</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from16_19\">19</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;response&nbsp;=&nbsp;requests.get(TARGET_URL&nbsp;+&nbsp;'/'&nbsp;+&nbsp;path,&nbsp;stream=True)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to16_19\">19</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;response&nbsp;=&nbsp;requests.get(TARGET_URL&nbsp;+&nbsp;'/'&nbsp;+&nbsp;path,&nbsp;stream=True)</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from16_20\">20</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to16_20\">20</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from16_21\">21</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;#&nbsp;Check&nbsp;if&nbsp;the&nbsp;response&nbsp;is&nbsp;a&nbsp;redirect</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to16_21\">21</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;#&nbsp;Check&nbsp;if&nbsp;the&nbsp;response&nbsp;is&nbsp;a&nbsp;redirect</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from16_22\">22</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;if&nbsp;response.status_code&nbsp;==&nbsp;302:</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to16_22\">22</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;if&nbsp;response.status_code&nbsp;==&nbsp;302:</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from16_23\">23</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;one_year_in_seconds&nbsp;=&nbsp;365&nbsp;*&nbsp;24&nbsp;*&nbsp;60&nbsp;*&nbsp;60</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to16_23\">23</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;one_year_in_seconds&nbsp;=&nbsp;365&nbsp;*&nbsp;24&nbsp;*&nbsp;60&nbsp;*&nbsp;60</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from16_24\">24</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;cache_control&nbsp;=&nbsp;f'public,&nbsp;max-age={one_year_in_seconds}'</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to16_24\">24</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;cache_control&nbsp;=&nbsp;f'public,&nbsp;max-age={one_year_in_seconds}'</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from16_25\">25</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to16_25\">25</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from16_26\">26</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;#&nbsp;Set&nbsp;the&nbsp;Cache-Control&nbsp;and&nbsp;Expires&nbsp;headers</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to16_26\">26</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;#&nbsp;Set&nbsp;the&nbsp;Cache-Control&nbsp;and&nbsp;Expires&nbsp;headers</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from16_27\">27</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;response.headers['Cache-Control']&nbsp;=&nbsp;cache_control</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to16_27\">27</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;response.headers['Cache-Control']&nbsp;=&nbsp;cache_control</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from16_28\">28</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;response.headers['Expires']&nbsp;=&nbsp;one_year_in_seconds</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to16_28\">28</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;response.headers['Expires']&nbsp;=&nbsp;one_year_in_seconds</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from16_29\">29</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to16_29\">29</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from16_30\">30</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;redirect(response.headers['location'],&nbsp;code=302)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to16_30\">30</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;redirect(response.headers['location'],&nbsp;code=302)</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from16_31\">31</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to16_31\">31</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from16_32\">32</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;def&nbsp;generate():</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to16_32\">32</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;def&nbsp;generate():</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from16_33\">33</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;for&nbsp;chunk&nbsp;in&nbsp;response.iter_content(chunk_size=8192):</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to16_33\">33</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;for&nbsp;chunk&nbsp;in&nbsp;response.iter_content(chunk_size=8192):</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from16_34\">34</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;yield&nbsp;chunk</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to16_34\">34</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;yield&nbsp;chunk</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from16_35\">35</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to16_35\">35</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from16_36\">36</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;#&nbsp;Return&nbsp;the&nbsp;proxied&nbsp;response</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to16_36\">36</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;#&nbsp;Return&nbsp;the&nbsp;proxied&nbsp;response</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from16_37\">37</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;Response(generate(),&nbsp;headers=response.headers,&nbsp;status=response.status_code)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to16_37\">37</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;Response(generate(),&nbsp;headers=response.headers,&nbsp;status=response.status_code)</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from16_38\">38</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;else:</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to16_38\">38</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;else:</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from16_39\">39</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;'Not&nbsp;Found',&nbsp;404</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to16_39\">39</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;'Not&nbsp;Found',&nbsp;404</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from16_40\">40</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to16_40\">40</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from16_41\">41</td><td nowrap=\"nowrap\">@app.route('/test')</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to16_41\">41</td><td nowrap=\"nowrap\">@app.route('/test')</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from16_42\">42</td><td nowrap=\"nowrap\">def&nbsp;test():</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to16_42\">42</td><td nowrap=\"nowrap\">def&nbsp;test():</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from16_43\">43</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;'ok'</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to16_43\">43</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;'ok'</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from16_44\">44</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to16_44\">44</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from16_45\">45</td><td nowrap=\"nowrap\">if&nbsp;__name__&nbsp;==&nbsp;\"__main__\":</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to16_45\">45</td><td nowrap=\"nowrap\">if&nbsp;__name__&nbsp;==&nbsp;\"__main__\":</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from16_46\">46</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;HOST&nbsp;=&nbsp;os.getenv('HOST',&nbsp;'127.0.0.1')</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to16_46\">46</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;HOST&nbsp;=&nbsp;os.getenv('HOST',&nbsp;'127.0.0.1')</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from16_47\">47</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;PORT&nbsp;=&nbsp;int(os.getenv('PORT',&nbsp;3000))</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to16_47\">47</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;PORT&nbsp;=&nbsp;int(os.getenv('PORT',&nbsp;3000))</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from16_48\">48</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to16_48\">48</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from16_49\">49</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;app.run(host=HOST,&nbsp;port=PORT)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to16_49\">49</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;app.run(host=HOST,&nbsp;port=PORT)</td></tr>\n", "        </tbody>\n", "    </table>\n", "    <table class=\"diff\" summary=\"Legends\">\n", "        <tr> <th colspan=\"2\"> Legends </th> </tr>\n", "        <tr> <td> <table border=\"\" summary=\"Colors\">\n", "                      <tr><th> Colors </th> </tr>\n", "                      <tr><td class=\"diff_add\">&nbsp;Added&nbsp;</td></tr>\n", "                      <tr><td class=\"diff_chg\">Changed</td> </tr>\n", "                      <tr><td class=\"diff_sub\">Deleted</td> </tr>\n", "                  </table></td>\n", "             <td> <table border=\"\" summary=\"Links\">\n", "                      <tr><th colspan=\"2\"> Links </th> </tr>\n", "                      <tr><td>(f)irst change</td> </tr>\n", "                      <tr><td>(n)ext change</td> </tr>\n", "                      <tr><td>(t)op</td> </tr>\n", "                  </table></td> </tr>\n", "    </table>\n", "</body>\n", "\n", "</html>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<h3>please fix these code that get error: ValueError: too many values to unpack (expected 2) (reject)</h3>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["\n", "<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Transitional//EN\"\n", "          \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd\">\n", "\n", "<html>\n", "\n", "<head>\n", "    <meta http-equiv=\"Content-Type\"\n", "          content=\"text/html; charset=utf-8\" />\n", "    <title></title>\n", "    <style type=\"text/css\">\n", "        table.diff {font-family:Courier; border:medium;}\n", "        .diff_header {background-color:#e0e0e0}\n", "        td.diff_header {text-align:right}\n", "        .diff_next {background-color:#c0c0c0}\n", "        .diff_add {background-color:#aaffaa}\n", "        .diff_chg {background-color:#ffff77}\n", "        .diff_sub {background-color:#ffaaaa}\n", "    </style>\n", "</head>\n", "\n", "<body>\n", "    \n", "    <table class=\"diff\" id=\"difflib_chg_to17__top\"\n", "           cellspacing=\"0\" cellpadding=\"0\" rules=\"groups\" >\n", "        <colgroup></colgroup> <colgroup></colgroup> <colgroup></colgroup>\n", "        <colgroup></colgroup> <colgroup></colgroup> <colgroup></colgroup>\n", "        \n", "        <tbody>\n", "            <tr><td class=\"diff_next\"><a href=\"#difflib_chg_to17__0\">f</a></td><td class=\"diff_header\" id=\"from17_1\">1</td><td nowrap=\"nowrap\">import&nbsp;os</td><td class=\"diff_next\"><a href=\"#difflib_chg_to17__0\">f</a></td><td class=\"diff_header\" id=\"to17_1\">1</td><td nowrap=\"nowrap\">import&nbsp;os</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from17_2\">2</td><td nowrap=\"nowrap\">from&nbsp;flask&nbsp;import&nbsp;Flask,&nbsp;request,&nbsp;redirect,&nbsp;Response</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to17_2\">2</td><td nowrap=\"nowrap\">from&nbsp;flask&nbsp;import&nbsp;Flask,&nbsp;request,&nbsp;redirect,&nbsp;Response</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from17_3\">3</td><td nowrap=\"nowrap\">import&nbsp;requests</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to17_3\">3</td><td nowrap=\"nowrap\">import&nbsp;requests</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from17_4\">4</td><td nowrap=\"nowrap\">from&nbsp;dotenv&nbsp;import&nbsp;load_dotenv</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to17_4\">4</td><td nowrap=\"nowrap\">from&nbsp;dotenv&nbsp;import&nbsp;load_dotenv</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from17_5\">5</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to17_5\">5</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from17_6\">6</td><td nowrap=\"nowrap\">load_dotenv()&nbsp;&nbsp;#&nbsp;Load&nbsp;environment&nbsp;variables&nbsp;from&nbsp;.env&nbsp;file</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to17_6\">6</td><td nowrap=\"nowrap\">load_dotenv()&nbsp;&nbsp;#&nbsp;Load&nbsp;environment&nbsp;variables&nbsp;from&nbsp;.env&nbsp;file</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from17_7\">7</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to17_7\">7</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from17_8\">8</td><td nowrap=\"nowrap\">app&nbsp;=&nbsp;Flask(__name__)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to17_8\">8</td><td nowrap=\"nowrap\">app&nbsp;=&nbsp;Flask(__name__)</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from17_9\">9</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to17_9\">9</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from17_10\">10</td><td nowrap=\"nowrap\">TARGET_URL&nbsp;=&nbsp;os.getenv('TARGET_URL',&nbsp;'https://github.com')</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to17_10\">10</td><td nowrap=\"nowrap\">TARGET_URL&nbsp;=&nbsp;os.getenv('TARGET_URL',&nbsp;'https://github.com')</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from17_11\">11</td><td nowrap=\"nowrap\">PATH_START&nbsp;=&nbsp;os.getenv('PATH_START',&nbsp;'/electerm/electerm/releases/download/')</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to17_11\">11</td><td nowrap=\"nowrap\">PATH_START&nbsp;=&nbsp;os.getenv('PATH_START',&nbsp;'/electerm/electerm/releases/download/')</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from17_12\">12</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to17_12\">12</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from17_13\">13</td><td nowrap=\"nowrap\">print(TARGET_URL,&nbsp;PATH_START)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to17_13\">13</td><td nowrap=\"nowrap\">print(TARGET_URL,&nbsp;PATH_START)</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from17_14\">14</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to17_14\">14</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\" id=\"difflib_chg_to17__0\"></td><td class=\"diff_header\" id=\"from17_15\">15</td><td nowrap=\"nowrap\">@app.route('/',&nbsp;defaults={'path':&nbsp;''})</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to17_15\">15</td><td nowrap=\"nowrap\">@app.route('/',&nbsp;defaults={'path':&nbsp;''})</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from17_16\">16</td><td nowrap=\"nowrap\">@app.route('/&lt;path:path&gt;')</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to17_16\">16</td><td nowrap=\"nowrap\">@app.route('/&lt;path:path&gt;')</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from17_17\">17</td><td nowrap=\"nowrap\">def&nbsp;proxy(path):</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to17_17\">17</td><td nowrap=\"nowrap\">def&nbsp;proxy(path):</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from17_18\">18</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;print(path)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to17_18\">18</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;print(path)</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from17_19\">19</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;if&nbsp;request.method&nbsp;==&nbsp;'GET'&nbsp;and&nbsp;('/'&nbsp;+&nbsp;path).startswith(PATH_START):</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to17_19\">19</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;if&nbsp;request.method&nbsp;==&nbsp;'GET'&nbsp;and&nbsp;('/'&nbsp;+&nbsp;path).startswith(PATH_START):</td></tr>\n", "            <tr><td class=\"diff_next\"><a href=\"#difflib_chg_to17__top\">t</a></td><td class=\"diff_header\" id=\"from17_20\">20</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;#&nbsp;Make&nbsp;the&nbsp;request&nbsp;always&nbsp;streamed&nbsp;to&nbsp;TARGET_URL,&nbsp;should&nbsp;follow&nbsp;redirect</span></td><td class=\"diff_next\"><a href=\"#difflib_chg_to17__top\">t</a></td><td class=\"diff_header\" id=\"to17_20\">20</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;response&nbsp;=&nbsp;requests.get(TARGET_URL&nbsp;+&nbsp;'/'&nbsp;+&nbsp;path,&nbsp;stream=True,&nbsp;allow_redirects=True)</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to17_21\">21</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to17_22\">22</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;def&nbsp;generate():</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to17_23\">23</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;for&nbsp;chunk&nbsp;in&nbsp;response.iter_content(chunk_size=8192):</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to17_24\">24</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;yield&nbsp;chunk</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from17_21\">21</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;response&nbsp;=&nbsp;requests.get(TARGET_URL&nbsp;+&nbsp;'/'&nbsp;+&nbsp;path,&nbsp;stream=True,&nbsp;allow_redirects=True)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to17_25\">25</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;response&nbsp;=&nbsp;requests.get(TARGET_URL&nbsp;+&nbsp;'/'&nbsp;+&nbsp;path,&nbsp;stream=True,&nbsp;allow_redirects=True)</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from17_22\">22</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to17_26\">26</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from17_23\">23</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;def&nbsp;generate():</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to17_27\">27</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;def&nbsp;generate():</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from17_24\">24</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;for&nbsp;chunk&nbsp;in&nbsp;response.iter_content(chunk_size=8192):</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to17_28\">28</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;for&nbsp;chunk&nbsp;in&nbsp;response.iter_content(chunk_size=8192):</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from17_25\">25</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;yield&nbsp;chunk</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to17_29\">29</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;yield&nbsp;chunk</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from17_26\">26</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to17_30\">30</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from17_27\">27</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;#&nbsp;Return&nbsp;the&nbsp;proxied&nbsp;response</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to17_31\">31</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;#&nbsp;Return&nbsp;the&nbsp;proxied&nbsp;response</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from17_28\">28</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;Response(generate(),&nbsp;headers=response.headers,&nbsp;status=response.status_code)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to17_32\">32</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;Response(generate(),&nbsp;headers=response.headers,&nbsp;status=response.status_code)</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from17_29\">29</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;else:</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to17_33\">33</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;else:</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from17_30\">30</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;'Not&nbsp;Found',&nbsp;404</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to17_34\">34</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;'Not&nbsp;Found',&nbsp;404</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from17_31\">31</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to17_35\">35</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from17_32\">32</td><td nowrap=\"nowrap\">@app.route('/test')</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to17_36\">36</td><td nowrap=\"nowrap\">@app.route('/test')</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from17_33\">33</td><td nowrap=\"nowrap\">def&nbsp;test():</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to17_37\">37</td><td nowrap=\"nowrap\">def&nbsp;test():</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from17_34\">34</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;'ok'</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to17_38\">38</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;'ok'</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from17_35\">35</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to17_39\">39</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from17_36\">36</td><td nowrap=\"nowrap\">if&nbsp;__name__&nbsp;==&nbsp;\"__main__\":</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to17_40\">40</td><td nowrap=\"nowrap\">if&nbsp;__name__&nbsp;==&nbsp;\"__main__\":</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from17_37\">37</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;HOST&nbsp;=&nbsp;os.getenv('HOST',&nbsp;'127.0.0.1')</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to17_41\">41</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;HOST&nbsp;=&nbsp;os.getenv('HOST',&nbsp;'127.0.0.1')</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from17_38\">38</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;PORT&nbsp;=&nbsp;int(os.getenv('PORT',&nbsp;3000))</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to17_42\">42</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;PORT&nbsp;=&nbsp;int(os.getenv('PORT',&nbsp;3000))</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from17_39\">39</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to17_43\">43</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from17_40\">40</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;app.run(host=HOST,&nbsp;port=PORT)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to17_44\">44</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;app.run(host=HOST,&nbsp;port=PORT)</td></tr>\n", "        </tbody>\n", "    </table>\n", "    <table class=\"diff\" summary=\"Legends\">\n", "        <tr> <th colspan=\"2\"> Legends </th> </tr>\n", "        <tr> <td> <table border=\"\" summary=\"Colors\">\n", "                      <tr><th> Colors </th> </tr>\n", "                      <tr><td class=\"diff_add\">&nbsp;Added&nbsp;</td></tr>\n", "                      <tr><td class=\"diff_chg\">Changed</td> </tr>\n", "                      <tr><td class=\"diff_sub\">Deleted</td> </tr>\n", "                  </table></td>\n", "             <td> <table border=\"\" summary=\"Links\">\n", "                      <tr><th colspan=\"2\"> Links </th> </tr>\n", "                      <tr><td>(f)irst change</td> </tr>\n", "                      <tr><td>(n)ext change</td> </tr>\n", "                      <tr><td>(t)op</td> </tr>\n", "                  </table></td> </tr>\n", "    </table>\n", "</body>\n", "\n", "</html>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<h3>rewrite with python (accept)</h3>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["\n", "<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Transitional//EN\"\n", "          \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd\">\n", "\n", "<html>\n", "\n", "<head>\n", "    <meta http-equiv=\"Content-Type\"\n", "          content=\"text/html; charset=utf-8\" />\n", "    <title></title>\n", "    <style type=\"text/css\">\n", "        table.diff {font-family:Courier; border:medium;}\n", "        .diff_header {background-color:#e0e0e0}\n", "        td.diff_header {text-align:right}\n", "        .diff_next {background-color:#c0c0c0}\n", "        .diff_add {background-color:#aaffaa}\n", "        .diff_chg {background-color:#ffff77}\n", "        .diff_sub {background-color:#ffaaaa}\n", "    </style>\n", "</head>\n", "\n", "<body>\n", "    \n", "    <table class=\"diff\" id=\"difflib_chg_to18__top\"\n", "           cellspacing=\"0\" cellpadding=\"0\" rules=\"groups\" >\n", "        <colgroup></colgroup> <colgroup></colgroup> <colgroup></colgroup>\n", "        <colgroup></colgroup> <colgroup></colgroup> <colgroup></colgroup>\n", "        \n", "        <tbody>\n", "            <tr><td class=\"diff_next\" id=\"difflib_chg_to18__1\"><a href=\"#difflib_chg_to18__1\">n</a></td><td class=\"diff_header\" id=\"from18_1\">1</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">const&nbsp;{&nbsp;createProxyMiddleware&nbsp;}&nbsp;=&nbsp;require('http-proxy-middleware')</span></td><td class=\"diff_next\"><a href=\"#difflib_chg_to18__1\">n</a></td><td class=\"diff_header\" id=\"to18_1\">1</td><td nowrap=\"nowrap\"><span class=\"diff_add\">from&nbsp;flask&nbsp;import&nbsp;Flask,&nbsp;request,&nbsp;redirect,&nbsp;make_response</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from18_2\">2</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">const&nbsp;app&nbsp;=&nbsp;require('express')()</span></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to18_2\">2</td><td nowrap=\"nowrap\"><span class=\"diff_add\">import&nbsp;requests</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from18_3\">3</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">app.disable('x-powered-by')</span></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to18_3\">3</td><td nowrap=\"nowrap\"><span class=\"diff_add\">import&nbsp;os</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to18_4\">4</td><td nowrap=\"nowrap\"><span class=\"diff_add\">from&nbsp;datetime&nbsp;import&nbsp;timedelta,&nbsp;datetime</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from18_4\">4</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to18_5\">5</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\" id=\"difflib_chg_to18__2\"><a href=\"#difflib_chg_to18__2\">n</a></td><td class=\"diff_header\" id=\"from18_5\">5</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">const&nbsp;{</span></td><td class=\"diff_next\"><a href=\"#difflib_chg_to18__2\">n</a></td><td class=\"diff_header\" id=\"to18_6\">6</td><td nowrap=\"nowrap\"><span class=\"diff_add\">app&nbsp;=&nbsp;Flask(__name__)</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from18_6\">6</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;TARGET_URL&nbsp;=&nbsp;'https://github.com',</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from18_7\">7</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;PATH_START&nbsp;=&nbsp;'/electerm/electerm/releases/download/'</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from18_8\">8</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">}&nbsp;=&nbsp;process.env</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from18_9\">9</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to18_7\">7</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\" id=\"difflib_chg_to18__3\"><a href=\"#difflib_chg_to18__3\">n</a></td><td class=\"diff_header\" id=\"from18_10\">10</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">const&nbsp;filter&nbsp;=&nbsp;function&nbsp;(pathname,&nbsp;req)&nbsp;{</span></td><td class=\"diff_next\"><a href=\"#difflib_chg_to18__3\">n</a></td><td class=\"diff_header\" id=\"to18_8\">8</td><td nowrap=\"nowrap\"><span class=\"diff_add\">TARGET_URL&nbsp;=&nbsp;os.getenv('TARGET_URL',&nbsp;'https://github.com')</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from18_11\">11</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;return&nbsp;pathname.startsWith(PATH_START)&nbsp;&amp;&amp;</span></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to18_9\">9</td><td nowrap=\"nowrap\"><span class=\"diff_add\">PATH_START&nbsp;=&nbsp;'/electerm/electerm/releases/download/'</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from18_12\">12</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;req.method&nbsp;===&nbsp;'GET'</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from18_13\">13</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">}</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from18_14\">14</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to18_10\">10</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"><a href=\"#difflib_chg_to18__4\">n</a></td><td class=\"diff_header\" id=\"from18_15\">15</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">const&nbsp;opts&nbsp;=&nbsp;{</span></td><td class=\"diff_next\"><a href=\"#difflib_chg_to18__4\">n</a></td><td class=\"diff_header\" id=\"to18_11\">11</td><td nowrap=\"nowrap\"><span class=\"diff_add\">@app.route('/',&nbsp;defaults={'path':&nbsp;''})</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from18_16\">16</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;target:&nbsp;TARGET_URL,</span></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to18_12\">12</td><td nowrap=\"nowrap\"><span class=\"diff_add\">@app.route('/&lt;path:path&gt;')</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from18_17\">17</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;//&nbsp;followRedirects:&nbsp;true,</span></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to18_13\">13</td><td nowrap=\"nowrap\"><span class=\"diff_add\">def&nbsp;proxy(path):</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from18_18\">18</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;changeOrigin:&nbsp;true,</span></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to18_14\">14</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;if&nbsp;path.startswith(PATH_START)&nbsp;and&nbsp;request.method&nbsp;==&nbsp;'GET':</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from18_19\">19</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;//&nbsp;pathRewrite:&nbsp;function&nbsp;(path,&nbsp;req)&nbsp;{&nbsp;return&nbsp;path.replace('/download',&nbsp;'/')&nbsp;},</span></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to18_15\">15</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;response&nbsp;=&nbsp;requests.get(TARGET_URL&nbsp;+&nbsp;'/'&nbsp;+&nbsp;path,&nbsp;stream=True)</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from18_20\">20</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;followRedirects:&nbsp;true,</span></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to18_16\">16</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from18_21\">21</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;//&nbsp;Define&nbsp;the&nbsp;handleProxyResponse&nbsp;function</span></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to18_17\">17</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;if&nbsp;response.status_code&nbsp;==&nbsp;302:</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from18_22\">22</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;onProxyRes:&nbsp;function&nbsp;handleProxyResponse&nbsp;(proxyRes,&nbsp;req,&nbsp;res)&nbsp;{</span></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to18_18\">18</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;redirect_url&nbsp;=&nbsp;response.headers['location']</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from18_23\">23</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;if&nbsp;(proxyRes.statusCode&nbsp;===&nbsp;302)&nbsp;{</span></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to18_19\">19</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from18_24\">24</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;const&nbsp;redirectUrl&nbsp;=&nbsp;proxyRes.headers.location</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from18_25\">25</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class=\"diff_chg\">const</span>&nbsp;one<span class=\"diff_chg\">Y</span>ear<span class=\"diff_chg\">I</span>n<span class=\"diff_chg\">S</span>econds&nbsp;=&nbsp;365&nbsp;*&nbsp;24&nbsp;*&nbsp;60&nbsp;*&nbsp;60</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to18_20\">20</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class=\"diff_chg\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>&nbsp;one<span class=\"diff_chg\">_y</span>ear<span class=\"diff_chg\">_i</span>n<span class=\"diff_chg\">_s</span>econds&nbsp;=&nbsp;365&nbsp;*&nbsp;24&nbsp;*&nbsp;60&nbsp;*&nbsp;60</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from18_26\">26</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class=\"diff_chg\">const</span>&nbsp;cache<span class=\"diff_chg\">C</span>ontrol&nbsp;=&nbsp;<span class=\"diff_chg\">`</span>public,&nbsp;max-age=<span class=\"diff_sub\">$</span>{one<span class=\"diff_chg\">Y</span>ear<span class=\"diff_chg\">I</span>n<span class=\"diff_chg\">S</span>econds}<span class=\"diff_chg\">`</span></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to18_21\">21</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class=\"diff_chg\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>&nbsp;cache<span class=\"diff_chg\">_c</span>ontrol&nbsp;=&nbsp;<span class=\"diff_chg\">f'</span>public,&nbsp;max-age={one<span class=\"diff_chg\">_y</span>ear<span class=\"diff_chg\">_i</span>n<span class=\"diff_chg\">_s</span>econds}<span class=\"diff_chg\">'</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from18_27\">27</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;const&nbsp;expires&nbsp;=&nbsp;new&nbsp;Date(Date.now()&nbsp;+&nbsp;oneYearInSeconds&nbsp;*&nbsp;1000).toUTCString()</span></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to18_22\">22</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;expires&nbsp;=&nbsp;(datetime.utcnow()&nbsp;+&nbsp;timedelta(seconds=one_year_in_seconds)).strftime('%a,&nbsp;%d&nbsp;%b&nbsp;%Y&nbsp;%H:%M:%S&nbsp;GMT')</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to18_23\">23</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to18_24\">24</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;resp&nbsp;=&nbsp;make_response(redirect(redirect_url))</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from18_28\">28</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;res.s<span class=\"diff_chg\">et(</span>'Cache-Control'<span class=\"diff_chg\">,</span>&nbsp;cache<span class=\"diff_chg\">C</span>ontrol<span class=\"diff_sub\">)</span></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to18_25\">25</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;res<span class=\"diff_add\">p</span>.<span class=\"diff_add\">header</span>s<span class=\"diff_chg\">[</span>'Cache-Control'<span class=\"diff_chg\">]&nbsp;=</span>&nbsp;cache<span class=\"diff_chg\">_c</span>ontrol</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from18_29\">29</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;res.set('Expires',&nbsp;expires)</span></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to18_26\">26</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;resp.headers['Expires']&nbsp;=&nbsp;expires</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from18_30\">30</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;res.redirect(redirectUrl)</span></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to18_27\">27</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to18_28\">28</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;resp,&nbsp;302</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to18_29\">29</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></td></tr>\n", "            <tr><td class=\"diff_next\" id=\"difflib_chg_to18__4\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to18_30\">30</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;response.content,&nbsp;response.status_code,&nbsp;response.headers.items()</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from18_31\">31</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;<span class=\"diff_sub\">}</span></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to18_31\">31</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from18_32\">32</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;}</span></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to18_32\">32</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;else:</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from18_33\">33</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">}</span></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to18_33\">33</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;'Not&nbsp;Found',&nbsp;404</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from18_34\">34</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to18_34\">34</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"><a href=\"#difflib_chg_to18__top\">t</a></td><td class=\"diff_header\" id=\"from18_35\">35</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">app.use(</span></td><td class=\"diff_next\"><a href=\"#difflib_chg_to18__top\">t</a></td><td class=\"diff_header\" id=\"to18_35\">35</td><td nowrap=\"nowrap\"><span class=\"diff_add\">@app.route('/test')</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from18_36\">36</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;'/',</span></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to18_36\">36</td><td nowrap=\"nowrap\"><span class=\"diff_add\">def&nbsp;test():</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from18_37\">37</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;createProxyMiddleware(filter,&nbsp;opts)</span></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to18_37\">37</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;'ok'</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from18_38\">38</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">)</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from18_39\">39</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from18_40\">40</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">app.get(</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from18_41\">41</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;'/test',</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from18_42\">42</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;(req,&nbsp;res)&nbsp;=&gt;&nbsp;res.send('ok')</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from18_43\">43</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">)</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from18_44\">44</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from18_45\">45</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">module.exports&nbsp;=&nbsp;app</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "        </tbody>\n", "    </table>\n", "    <table class=\"diff\" summary=\"Legends\">\n", "        <tr> <th colspan=\"2\"> Legends </th> </tr>\n", "        <tr> <td> <table border=\"\" summary=\"Colors\">\n", "                      <tr><th> Colors </th> </tr>\n", "                      <tr><td class=\"diff_add\">&nbsp;Added&nbsp;</td></tr>\n", "                      <tr><td class=\"diff_chg\">Changed</td> </tr>\n", "                      <tr><td class=\"diff_sub\">Deleted</td> </tr>\n", "                  </table></td>\n", "             <td> <table border=\"\" summary=\"Links\">\n", "                      <tr><th colspan=\"2\"> Links </th> </tr>\n", "                      <tr><td>(f)irst change</td> </tr>\n", "                      <tr><td>(n)ext change</td> </tr>\n", "                      <tr><td>(t)op</td> </tr>\n", "                  </table></td> </tr>\n", "    </table>\n", "</body>\n", "\n", "</html>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<h3>can you improve my function (accept-annotation)</h3>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["\n", "<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Transitional//EN\"\n", "          \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd\">\n", "\n", "<html>\n", "\n", "<head>\n", "    <meta http-equiv=\"Content-Type\"\n", "          content=\"text/html; charset=utf-8\" />\n", "    <title></title>\n", "    <style type=\"text/css\">\n", "        table.diff {font-family:Courier; border:medium;}\n", "        .diff_header {background-color:#e0e0e0}\n", "        td.diff_header {text-align:right}\n", "        .diff_next {background-color:#c0c0c0}\n", "        .diff_add {background-color:#aaffaa}\n", "        .diff_chg {background-color:#ffff77}\n", "        .diff_sub {background-color:#ffaaaa}\n", "    </style>\n", "</head>\n", "\n", "<body>\n", "    \n", "    <table class=\"diff\" id=\"difflib_chg_to19__top\"\n", "           cellspacing=\"0\" cellpadding=\"0\" rules=\"groups\" >\n", "        <colgroup></colgroup> <colgroup></colgroup> <colgroup></colgroup>\n", "        <colgroup></colgroup> <colgroup></colgroup> <colgroup></colgroup>\n", "        \n", "        <tbody>\n", "            <tr><td class=\"diff_next\"><a href=\"#difflib_chg_to19__0\">f</a></td><td class=\"diff_header\" id=\"from19_1\">1</td><td nowrap=\"nowrap\">//&nbsp;from&nbsp;https://zhuanlan.zhihu.com/p/112564936</td><td class=\"diff_next\"><a href=\"#difflib_chg_to19__0\">f</a></td><td class=\"diff_header\" id=\"to19_1\">1</td><td nowrap=\"nowrap\">//&nbsp;from&nbsp;https://zhuanlan.zhihu.com/p/112564936</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from19_2\">2</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to19_2\">2</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from19_3\">3</td><td nowrap=\"nowrap\">const&nbsp;{&nbsp;screen&nbsp;}&nbsp;=&nbsp;require('electron')</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to19_3\">3</td><td nowrap=\"nowrap\">const&nbsp;{&nbsp;screen&nbsp;}&nbsp;=&nbsp;require('electron')</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from19_4\">4</td><td nowrap=\"nowrap\">const&nbsp;{</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to19_4\">4</td><td nowrap=\"nowrap\">const&nbsp;{</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from19_5\">5</td><td nowrap=\"nowrap\">&nbsp;&nbsp;isLinux</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to19_5\">5</td><td nowrap=\"nowrap\">&nbsp;&nbsp;isLinux</td></tr>\n", "            <tr><td class=\"diff_next\" id=\"difflib_chg_to19__0\"></td><td class=\"diff_header\" id=\"from19_6\">6</td><td nowrap=\"nowrap\">}&nbsp;=&nbsp;require('../common/runtime-constants')</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to19_6\">6</td><td nowrap=\"nowrap\">}&nbsp;=&nbsp;require('../common/runtime-constants')</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from19_7\">7</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to19_7\">7</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\" id=\"difflib_chg_to19__1\"></td><td class=\"diff_header\" id=\"from19_8\">8</td><td nowrap=\"nowrap\">let&nbsp;mouseStartPosition&nbsp;=&nbsp;{&nbsp;x:&nbsp;0,&nbsp;y:&nbsp;0&nbsp;}</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to19_8\">8</td><td nowrap=\"nowrap\">let&nbsp;mouseStartPosition&nbsp;=&nbsp;{&nbsp;x:&nbsp;0,&nbsp;y:&nbsp;0&nbsp;}</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from19_9\">9</td><td nowrap=\"nowrap\">let&nbsp;movingInterval&nbsp;=&nbsp;null</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to19_9\">9</td><td nowrap=\"nowrap\">let&nbsp;movingInterval&nbsp;=&nbsp;null</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from19_10\">10</td><td nowrap=\"nowrap\">let&nbsp;dragCount&nbsp;=&nbsp;0</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to19_10\">10</td><td nowrap=\"nowrap\">let&nbsp;dragCount&nbsp;=&nbsp;0</td></tr>\n", "            <tr><td class=\"diff_next\"><a href=\"#difflib_chg_to19__1\">n</a></td><td class=\"diff_header\" id=\"from19_11\">11</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;</span></td><td class=\"diff_next\"><a href=\"#difflib_chg_to19__1\">n</a></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from19_12\">12</td><td nowrap=\"nowrap\">function&nbsp;windowMove&nbsp;(canMoving)&nbsp;{</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to19_11\">11</td><td nowrap=\"nowrap\">function&nbsp;windowMove&nbsp;(canMoving)&nbsp;{</td></tr>\n", "            <tr><td class=\"diff_next\" id=\"difflib_chg_to19__2\"><a href=\"#difflib_chg_to19__2\">n</a></td><td class=\"diff_header\" id=\"from19_13\">13</td><td nowrap=\"nowrap\">&nbsp;&nbsp;const&nbsp;{&nbsp;win&nbsp;}&nbsp;=&nbsp;global</td><td class=\"diff_next\"><a href=\"#difflib_chg_to19__2\">n</a></td><td class=\"diff_header\" id=\"to19_12\">12</td><td nowrap=\"nowrap\">&nbsp;&nbsp;const&nbsp;{&nbsp;win&nbsp;}&nbsp;=&nbsp;global<span class=\"diff_add\">;</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from19_14\">14</td><td nowrap=\"nowrap\">&nbsp;&nbsp;<span class=\"diff_chg\">cons</span>t&nbsp;size&nbsp;=&nbsp;win.getBounds()</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to19_13\">13</td><td nowrap=\"nowrap\">&nbsp;&nbsp;<span class=\"diff_chg\">le</span>t&nbsp;size&nbsp;=&nbsp;win.getBounds()<span class=\"diff_add\">;</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from19_15\">15</td><td nowrap=\"nowrap\">&nbsp;&nbsp;const&nbsp;scr&nbsp;=&nbsp;screen.getDisplayNearestPoint(size)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to19_14\">14</td><td nowrap=\"nowrap\">&nbsp;&nbsp;const&nbsp;scr&nbsp;=&nbsp;screen.getDisplayNearestPoint(size)<span class=\"diff_add\">;</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to19_15\">15</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;</span></td></tr>\n", "            <tr><td class=\"diff_next\" id=\"difflib_chg_to19__3\"></td><td class=\"diff_header\" id=\"from19_16\">16</td><td nowrap=\"nowrap\">&nbsp;&nbsp;if&nbsp;(canMoving)&nbsp;{</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to19_16\">16</td><td nowrap=\"nowrap\">&nbsp;&nbsp;if&nbsp;(canMoving)&nbsp;{</td></tr>\n", "            <tr><td class=\"diff_next\"><a href=\"#difflib_chg_to19__3\">n</a></td><td class=\"diff_header\" id=\"from19_17\">17</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;win.setResizable(false)</td><td class=\"diff_next\"><a href=\"#difflib_chg_to19__3\">n</a></td><td class=\"diff_header\" id=\"to19_17\">17</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;win.setResizable(false)<span class=\"diff_add\">;</span></td></tr>\n", "            <tr><td class=\"diff_next\" id=\"difflib_chg_to19__4\"></td><td class=\"diff_header\" id=\"from19_18\">18</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;mouseStartPosition&nbsp;=&nbsp;screen.getCursorScreenPoint()</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to19_18\">18</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;mouseStartPosition&nbsp;=&nbsp;screen.getCursorScreenPoint()<span class=\"diff_add\">;</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from19_19\">19</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;</span></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to19_19\">19</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;</span></td></tr>\n", "            <tr><td class=\"diff_next\" id=\"difflib_chg_to19__5\"></td><td class=\"diff_header\" id=\"from19_20\">20</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;if&nbsp;(movingInterval)&nbsp;{</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to19_20\">20</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;if&nbsp;(movingInterval)&nbsp;{</td></tr>\n", "            <tr><td class=\"diff_next\"><a href=\"#difflib_chg_to19__4\">n</a></td><td class=\"diff_header\" id=\"from19_21\">21</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;clearInterval(movingInterval)</td><td class=\"diff_next\"><a href=\"#difflib_chg_to19__4\">n</a></td><td class=\"diff_header\" id=\"to19_21\">21</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;clearInterval(movingInterval)<span class=\"diff_add\">;</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from19_22\">22</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;}</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to19_22\">22</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;}</td></tr>\n", "            <tr><td class=\"diff_next\"><a href=\"#difflib_chg_to19__5\">n</a></td><td class=\"diff_header\" id=\"from19_23\">23</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;</span></td><td class=\"diff_next\"><a href=\"#difflib_chg_to19__5\">n</a></td><td class=\"diff_header\" id=\"to19_23\">23</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;</span></td></tr>\n", "            <tr><td class=\"diff_next\" id=\"difflib_chg_to19__6\"></td><td class=\"diff_header\" id=\"from19_24\">24</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;movingInterval&nbsp;=&nbsp;setInterval(()&nbsp;=&gt;&nbsp;{</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to19_24\">24</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;movingInterval&nbsp;=&nbsp;setInterval(()&nbsp;=&gt;&nbsp;{</td></tr>\n", "            <tr><td class=\"diff_next\"><a href=\"#difflib_chg_to19__6\">n</a></td><td class=\"diff_header\" id=\"from19_25\">25</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;dragCount&nbsp;=&nbsp;dragCount&nbsp;+&nbsp;1</span></td><td class=\"diff_next\"><a href=\"#difflib_chg_to19__6\">n</a></td><td class=\"diff_header\" id=\"to19_25\">25</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;dragCount&nbsp;=&nbsp;dragCount&nbsp;+&nbsp;1;&nbsp;//&nbsp;Increment&nbsp;the&nbsp;count&nbsp;directly&nbsp;instead&nbsp;of&nbsp;assigning&nbsp;a&nbsp;new&nbsp;value</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from19_26\">26</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;if&nbsp;(dragCount&nbsp;&gt;&nbsp;1000)&nbsp;{</span></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to19_26\">26</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;if&nbsp;(dragCount&nbsp;&gt;&nbsp;1000)&nbsp;{&nbsp;//&nbsp;avoid&nbsp;bigger&nbsp;than&nbsp;int&nbsp;max</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from19_27\">27</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;dragCount&nbsp;=&nbsp;1000</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to19_27\">27</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;dragCount&nbsp;=&nbsp;1000</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from19_28\">28</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;}</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to19_28\">28</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;}</td></tr>\n", "            <tr><td class=\"diff_next\"><a href=\"#difflib_chg_to19__7\">n</a></td><td class=\"diff_header\" id=\"from19_29\">29</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;const&nbsp;cursorPosition&nbsp;=&nbsp;screen.getCursorScreenPoint()</td><td class=\"diff_next\"><a href=\"#difflib_chg_to19__7\">n</a></td><td class=\"diff_header\" id=\"to19_29\">29</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;const&nbsp;cursorPosition&nbsp;=&nbsp;screen.getCursorScreenPoint()<span class=\"diff_add\">;</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from19_30\">30</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class=\"diff_chg\">cons</span>t&nbsp;x&nbsp;=&nbsp;size.x&nbsp;+&nbsp;cursorPosition.x&nbsp;-&nbsp;mouseStartPosition.x</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to19_30\">30</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class=\"diff_chg\">le</span>t&nbsp;x&nbsp;=&nbsp;size.x&nbsp;+&nbsp;cursorPosition.x&nbsp;-&nbsp;mouseStartPosition.x<span class=\"diff_add\">;</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from19_31\">31</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class=\"diff_chg\">cons</span>t&nbsp;y&nbsp;=&nbsp;size.y&nbsp;+&nbsp;cursorPosition.y&nbsp;-&nbsp;mouseStartPosition.y</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to19_31\">31</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class=\"diff_chg\">le</span>t&nbsp;y&nbsp;=&nbsp;size.y&nbsp;+&nbsp;cursorPosition.y&nbsp;-&nbsp;mouseStartPosition.y<span class=\"diff_add\">;</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from19_32\">32</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;const&nbsp;{&nbsp;workAreaSize&nbsp;}&nbsp;=&nbsp;scr</span></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to19_32\">32</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;let&nbsp;{&nbsp;width:&nbsp;nw,&nbsp;height:&nbsp;nh&nbsp;}&nbsp;=&nbsp;size;&nbsp;//&nbsp;Destructure&nbsp;the&nbsp;width&nbsp;and&nbsp;height&nbsp;from&nbsp;size&nbsp;for&nbsp;readability</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from19_33\">33</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;let&nbsp;nw&nbsp;=&nbsp;size.width</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from19_34\">34</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;let&nbsp;nh&nbsp;=&nbsp;size.height</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from19_35\">35</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;if&nbsp;(</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from19_36\">36</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;isLinux&nbsp;&amp;&amp;</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from19_37\">37</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;dragCount&nbsp;&gt;&nbsp;200&nbsp;&amp;&amp;</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from19_38\">38</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;size.width&nbsp;===&nbsp;workAreaSize.width&nbsp;&amp;&amp;</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from19_39\">39</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;size.height&nbsp;===&nbsp;workAreaSize.height</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\" id=\"difflib_chg_to19__7\"></td><td class=\"diff_header\" id=\"from19_40\">40</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class=\"diff_sub\">)&nbsp;{</span></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to19_33\">33</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from19_41\">41</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;nw&nbsp;=&nbsp;size.width&nbsp;-&nbsp;100</span></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to19_34\">34</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;if&nbsp;(isLinux&nbsp;&amp;&amp;&nbsp;dragCount&nbsp;&gt;&nbsp;200&nbsp;&amp;&amp;&nbsp;size.width&nbsp;===&nbsp;scr.workAreaSize.width&nbsp;&amp;&amp;&nbsp;size.height&nbsp;===&nbsp;scr.workAreaSize.height)&nbsp;{</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from19_42\">42</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;nh&nbsp;=&nbsp;size.height&nbsp;-&nbsp;100</span></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to19_35\">35</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;nw&nbsp;=&nbsp;nw&nbsp;-&nbsp;100;&nbsp;//&nbsp;follow&nbsp;standard&nbsp;code&nbsp;format,&nbsp;aovid&nbsp;use&nbsp;x--&nbsp;or&nbsp;x++&nbsp;or&nbsp;x&nbsp;-=&nbsp;or&nbsp;x&nbsp;+=</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to19_36\">36</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;nh&nbsp;=&nbsp;nw&nbsp;-&nbsp;100;</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from19_43\">43</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;}</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to19_37\">37</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;}</td></tr>\n", "            <tr><td class=\"diff_next\"><a href=\"#difflib_chg_to19__8\">n</a></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"><a href=\"#difflib_chg_to19__8\">n</a></td><td class=\"diff_header\" id=\"to19_38\">38</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></td></tr>\n", "            <tr><td class=\"diff_next\" id=\"difflib_chg_to19__8\"></td><td class=\"diff_header\" id=\"from19_44\">44</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;win.setBounds({</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to19_39\">39</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;win.setBounds({</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from19_45\">45</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;width:&nbsp;nw,</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to19_40\">40</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;width:&nbsp;nw,</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from19_46\">46</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;height:&nbsp;nh,</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to19_41\">41</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;height:&nbsp;nh,</td></tr>\n", "            <tr><td class=\"diff_next\" id=\"difflib_chg_to19__9\"></td><td class=\"diff_header\" id=\"from19_47\">47</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;x,</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to19_42\">42</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;x,</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from19_48\">48</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;y</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to19_43\">43</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;y</td></tr>\n", "            <tr><td class=\"diff_next\"><a href=\"#difflib_chg_to19__9\">n</a></td><td class=\"diff_header\" id=\"from19_49\">49</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;})</td><td class=\"diff_next\"><a href=\"#difflib_chg_to19__9\">n</a></td><td class=\"diff_header\" id=\"to19_44\">44</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;})<span class=\"diff_add\">;</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from19_50\">50</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;},&nbsp;1)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to19_45\">45</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;},&nbsp;1)<span class=\"diff_add\">;</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from19_51\">51</td><td nowrap=\"nowrap\">&nbsp;&nbsp;}&nbsp;else&nbsp;{</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to19_46\">46</td><td nowrap=\"nowrap\">&nbsp;&nbsp;}&nbsp;else&nbsp;{</td></tr>\n", "            <tr><td class=\"diff_next\"><a href=\"#difflib_chg_to19__top\">t</a></td><td class=\"diff_header\" id=\"from19_52\">52</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;win.setResizable(true)</td><td class=\"diff_next\"><a href=\"#difflib_chg_to19__top\">t</a></td><td class=\"diff_header\" id=\"to19_47\">47</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;win.setResizable(true)<span class=\"diff_add\">;</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from19_53\">53</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;dragCount&nbsp;=&nbsp;0</span></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to19_48\">48</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;dragCount&nbsp;=&nbsp;0;&nbsp;//&nbsp;Reset&nbsp;the&nbsp;count&nbsp;when&nbsp;moving&nbsp;is&nbsp;not&nbsp;allowed</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from19_54\">54</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;clearInterval(movingInterval)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to19_49\">49</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;clearInterval(movingInterval)<span class=\"diff_add\">;</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from19_55\">55</td><td nowrap=\"nowrap\">&nbsp;&nbsp;}</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to19_50\">50</td><td nowrap=\"nowrap\">&nbsp;&nbsp;}</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from19_56\">56</td><td nowrap=\"nowrap\">}</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to19_51\">51</td><td nowrap=\"nowrap\">}</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from19_57\">57</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to19_52\">52</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from19_58\">58</td><td nowrap=\"nowrap\">module.exports&nbsp;=&nbsp;windowMove</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to19_53\">53</td><td nowrap=\"nowrap\">module.exports&nbsp;=&nbsp;windowMove</td></tr>\n", "        </tbody>\n", "    </table>\n", "    <table class=\"diff\" summary=\"Legends\">\n", "        <tr> <th colspan=\"2\"> Legends </th> </tr>\n", "        <tr> <td> <table border=\"\" summary=\"Colors\">\n", "                      <tr><th> Colors </th> </tr>\n", "                      <tr><td class=\"diff_add\">&nbsp;Added&nbsp;</td></tr>\n", "                      <tr><td class=\"diff_chg\">Changed</td> </tr>\n", "                      <tr><td class=\"diff_sub\">Deleted</td> </tr>\n", "                  </table></td>\n", "             <td> <table border=\"\" summary=\"Links\">\n", "                      <tr><th colspan=\"2\"> Links </th> </tr>\n", "                      <tr><td>(f)irst change</td> </tr>\n", "                      <tr><td>(n)ext change</td> </tr>\n", "                      <tr><td>(t)op</td> </tr>\n", "                  </table></td> </tr>\n", "    </table>\n", "</body>\n", "\n", "</html>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<h3>use dotenv package to load envs (accept)</h3>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["\n", "<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Transitional//EN\"\n", "          \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd\">\n", "\n", "<html>\n", "\n", "<head>\n", "    <meta http-equiv=\"Content-Type\"\n", "          content=\"text/html; charset=utf-8\" />\n", "    <title></title>\n", "    <style type=\"text/css\">\n", "        table.diff {font-family:Courier; border:medium;}\n", "        .diff_header {background-color:#e0e0e0}\n", "        td.diff_header {text-align:right}\n", "        .diff_next {background-color:#c0c0c0}\n", "        .diff_add {background-color:#aaffaa}\n", "        .diff_chg {background-color:#ffff77}\n", "        .diff_sub {background-color:#ffaaaa}\n", "    </style>\n", "</head>\n", "\n", "<body>\n", "    \n", "    <table class=\"diff\" id=\"difflib_chg_to20__top\"\n", "           cellspacing=\"0\" cellpadding=\"0\" rules=\"groups\" >\n", "        <colgroup></colgroup> <colgroup></colgroup> <colgroup></colgroup>\n", "        <colgroup></colgroup> <colgroup></colgroup> <colgroup></colgroup>\n", "        \n", "        <tbody>\n", "            <tr><td class=\"diff_next\" id=\"difflib_chg_to20__0\"><a href=\"#difflib_chg_to20__0\">f</a></td><td class=\"diff_header\" id=\"from20_1\">1</td><td nowrap=\"nowrap\">from&nbsp;flask&nbsp;import&nbsp;Flask,&nbsp;request,&nbsp;redirect,&nbsp;make_response</td><td class=\"diff_next\"><a href=\"#difflib_chg_to20__0\">f</a></td><td class=\"diff_header\" id=\"to20_1\">1</td><td nowrap=\"nowrap\">from&nbsp;flask&nbsp;import&nbsp;Flask,&nbsp;request,&nbsp;redirect,&nbsp;make_response</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from20_2\">2</td><td nowrap=\"nowrap\">import&nbsp;requests</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to20_2\">2</td><td nowrap=\"nowrap\">import&nbsp;requests</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from20_3\">3</td><td nowrap=\"nowrap\">import&nbsp;os</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to20_3\">3</td><td nowrap=\"nowrap\">import&nbsp;os</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from20_4\">4</td><td nowrap=\"nowrap\">from&nbsp;datetime&nbsp;import&nbsp;timedelta,&nbsp;datetime</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to20_4\">4</td><td nowrap=\"nowrap\">from&nbsp;datetime&nbsp;import&nbsp;timedelta,&nbsp;datetime</td></tr>\n", "            <tr><td class=\"diff_next\"><a href=\"#difflib_chg_to20__top\">t</a></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"><a href=\"#difflib_chg_to20__top\">t</a></td><td class=\"diff_header\" id=\"to20_5\">5</td><td nowrap=\"nowrap\"><span class=\"diff_add\">from&nbsp;dotenv&nbsp;import&nbsp;load_dotenv</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from20_5\">5</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to20_6\">6</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from20_6\">6</td><td nowrap=\"nowrap\">app&nbsp;=&nbsp;Flask(__name__)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to20_7\">7</td><td nowrap=\"nowrap\">app&nbsp;=&nbsp;Flask(__name__)</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from20_7\">7</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to20_8\">8</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from20_8\">8</td><td nowrap=\"nowrap\">TARGET_URL&nbsp;=&nbsp;os.getenv('TARGET_URL',&nbsp;'https://github.com')</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to20_9\">9</td><td nowrap=\"nowrap\">TARGET_URL&nbsp;=&nbsp;os.getenv('TARGET_URL',&nbsp;'https://github.com')</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from20_9\">9</td><td nowrap=\"nowrap\">PATH_START&nbsp;=&nbsp;'/electerm/electerm/releases/download/'</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to20_10\">10</td><td nowrap=\"nowrap\">PATH_START&nbsp;=&nbsp;'/electerm/electerm/releases/download/'</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from20_10\">10</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to20_11\">11</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from20_11\">11</td><td nowrap=\"nowrap\">@app.route('/',&nbsp;defaults={'path':&nbsp;''})</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to20_12\">12</td><td nowrap=\"nowrap\">@app.route('/',&nbsp;defaults={'path':&nbsp;''})</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from20_12\">12</td><td nowrap=\"nowrap\">@app.route('/&lt;path:path&gt;')</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to20_13\">13</td><td nowrap=\"nowrap\">@app.route('/&lt;path:path&gt;')</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from20_13\">13</td><td nowrap=\"nowrap\">def&nbsp;proxy(path):</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to20_14\">14</td><td nowrap=\"nowrap\">def&nbsp;proxy(path):</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from20_14\">14</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;if&nbsp;path.startswith(PATH_START)&nbsp;and&nbsp;request.method&nbsp;==&nbsp;'GET':</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to20_15\">15</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;if&nbsp;path.startswith(PATH_START)&nbsp;and&nbsp;request.method&nbsp;==&nbsp;'GET':</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from20_15\">15</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;response&nbsp;=&nbsp;requests.get(TARGET_URL&nbsp;+&nbsp;'/'&nbsp;+&nbsp;path,&nbsp;stream=True)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to20_16\">16</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;response&nbsp;=&nbsp;requests.get(TARGET_URL&nbsp;+&nbsp;'/'&nbsp;+&nbsp;path,&nbsp;stream=True)</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from20_16\">16</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to20_17\">17</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from20_17\">17</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;if&nbsp;response.status_code&nbsp;==&nbsp;302:</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to20_18\">18</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;if&nbsp;response.status_code&nbsp;==&nbsp;302:</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from20_18\">18</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;redirect_url&nbsp;=&nbsp;response.headers['location']</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to20_19\">19</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;redirect_url&nbsp;=&nbsp;response.headers['location']</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from20_19\">19</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to20_20\">20</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from20_20\">20</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;one_year_in_seconds&nbsp;=&nbsp;365&nbsp;*&nbsp;24&nbsp;*&nbsp;60&nbsp;*&nbsp;60</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to20_21\">21</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;one_year_in_seconds&nbsp;=&nbsp;365&nbsp;*&nbsp;24&nbsp;*&nbsp;60&nbsp;*&nbsp;60</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from20_21\">21</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;cache_control&nbsp;=&nbsp;f'public,&nbsp;max-age={one_year_in_seconds}'</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to20_22\">22</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;cache_control&nbsp;=&nbsp;f'public,&nbsp;max-age={one_year_in_seconds}'</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from20_22\">22</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;expires&nbsp;=&nbsp;(datetime.utcnow()&nbsp;+&nbsp;timedelta(seconds=one_year_in_seconds)).strftime('%a,&nbsp;%d&nbsp;%b&nbsp;%Y&nbsp;%H:%M:%S&nbsp;GMT')</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to20_23\">23</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;expires&nbsp;=&nbsp;(datetime.utcnow()&nbsp;+&nbsp;timedelta(seconds=one_year_in_seconds)).strftime('%a,&nbsp;%d&nbsp;%b&nbsp;%Y&nbsp;%H:%M:%S&nbsp;GMT')</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from20_23\">23</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to20_24\">24</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from20_24\">24</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;resp&nbsp;=&nbsp;make_response(redirect(redirect_url))</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to20_25\">25</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;resp&nbsp;=&nbsp;make_response(redirect(redirect_url))</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from20_25\">25</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;resp.headers['Cache-Control']&nbsp;=&nbsp;cache_control</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to20_26\">26</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;resp.headers['Cache-Control']&nbsp;=&nbsp;cache_control</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from20_26\">26</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;resp.headers['Expires']&nbsp;=&nbsp;expires</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to20_27\">27</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;resp.headers['Expires']&nbsp;=&nbsp;expires</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from20_27\">27</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to20_28\">28</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from20_28\">28</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;resp,&nbsp;302</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to20_29\">29</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;resp,&nbsp;302</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from20_29\">29</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to20_30\">30</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from20_30\">30</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;response.content,&nbsp;response.status_code,&nbsp;response.headers.items()</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to20_31\">31</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;response.content,&nbsp;response.status_code,&nbsp;response.headers.items()</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from20_31\">31</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to20_32\">32</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from20_32\">32</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;else:</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to20_33\">33</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;else:</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from20_33\">33</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;'Not&nbsp;Found',&nbsp;404</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to20_34\">34</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;'Not&nbsp;Found',&nbsp;404</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from20_34\">34</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to20_35\">35</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from20_35\">35</td><td nowrap=\"nowrap\">@app.route('/test')</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to20_36\">36</td><td nowrap=\"nowrap\">@app.route('/test')</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from20_36\">36</td><td nowrap=\"nowrap\">def&nbsp;test():</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to20_37\">37</td><td nowrap=\"nowrap\">def&nbsp;test():</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from20_37\">37</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;'ok'</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to20_38\">38</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;'ok'</td></tr>\n", "        </tbody>\n", "    </table>\n", "    <table class=\"diff\" summary=\"Legends\">\n", "        <tr> <th colspan=\"2\"> Legends </th> </tr>\n", "        <tr> <td> <table border=\"\" summary=\"Colors\">\n", "                      <tr><th> Colors </th> </tr>\n", "                      <tr><td class=\"diff_add\">&nbsp;Added&nbsp;</td></tr>\n", "                      <tr><td class=\"diff_chg\">Changed</td> </tr>\n", "                      <tr><td class=\"diff_sub\">Deleted</td> </tr>\n", "                  </table></td>\n", "             <td> <table border=\"\" summary=\"Links\">\n", "                      <tr><th colspan=\"2\"> Links </th> </tr>\n", "                      <tr><td>(f)irst change</td> </tr>\n", "                      <tr><td>(n)ext change</td> </tr>\n", "                      <tr><td>(t)op</td> </tr>\n", "                  </table></td> </tr>\n", "    </table>\n", "</body>\n", "\n", "</html>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<h3>response = requests.get(response.headers['Location'], stream=True) seems only error response: net::ERR_CONTENT_DECODING_FAILED , how to fix?? (accept)</h3>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["\n", "<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Transitional//EN\"\n", "          \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd\">\n", "\n", "<html>\n", "\n", "<head>\n", "    <meta http-equiv=\"Content-Type\"\n", "          content=\"text/html; charset=utf-8\" />\n", "    <title></title>\n", "    <style type=\"text/css\">\n", "        table.diff {font-family:Courier; border:medium;}\n", "        .diff_header {background-color:#e0e0e0}\n", "        td.diff_header {text-align:right}\n", "        .diff_next {background-color:#c0c0c0}\n", "        .diff_add {background-color:#aaffaa}\n", "        .diff_chg {background-color:#ffff77}\n", "        .diff_sub {background-color:#ffaaaa}\n", "    </style>\n", "</head>\n", "\n", "<body>\n", "    \n", "    <table class=\"diff\" id=\"difflib_chg_to21__top\"\n", "           cellspacing=\"0\" cellpadding=\"0\" rules=\"groups\" >\n", "        <colgroup></colgroup> <colgroup></colgroup> <colgroup></colgroup>\n", "        <colgroup></colgroup> <colgroup></colgroup> <colgroup></colgroup>\n", "        \n", "        <tbody>\n", "            <tr><td class=\"diff_next\"><a href=\"#difflib_chg_to21__0\">f</a></td><td class=\"diff_header\" id=\"from21_1\">1</td><td nowrap=\"nowrap\">import&nbsp;os</td><td class=\"diff_next\"><a href=\"#difflib_chg_to21__0\">f</a></td><td class=\"diff_header\" id=\"to21_1\">1</td><td nowrap=\"nowrap\">import&nbsp;os</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from21_2\">2</td><td nowrap=\"nowrap\">from&nbsp;flask&nbsp;import&nbsp;Flask,&nbsp;request,&nbsp;redirect,&nbsp;Response</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to21_2\">2</td><td nowrap=\"nowrap\">from&nbsp;flask&nbsp;import&nbsp;Flask,&nbsp;request,&nbsp;redirect,&nbsp;Response</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from21_3\">3</td><td nowrap=\"nowrap\">import&nbsp;requests</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to21_3\">3</td><td nowrap=\"nowrap\">import&nbsp;requests</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from21_4\">4</td><td nowrap=\"nowrap\">from&nbsp;dotenv&nbsp;import&nbsp;load_dotenv</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to21_4\">4</td><td nowrap=\"nowrap\">from&nbsp;dotenv&nbsp;import&nbsp;load_dotenv</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from21_5\">5</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to21_5\">5</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from21_6\">6</td><td nowrap=\"nowrap\">load_dotenv()&nbsp;&nbsp;#&nbsp;Load&nbsp;environment&nbsp;variables&nbsp;from&nbsp;.env&nbsp;file</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to21_6\">6</td><td nowrap=\"nowrap\">load_dotenv()&nbsp;&nbsp;#&nbsp;Load&nbsp;environment&nbsp;variables&nbsp;from&nbsp;.env&nbsp;file</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from21_7\">7</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to21_7\">7</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from21_8\">8</td><td nowrap=\"nowrap\">app&nbsp;=&nbsp;Flask(__name__)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to21_8\">8</td><td nowrap=\"nowrap\">app&nbsp;=&nbsp;Flask(__name__)</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from21_9\">9</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to21_9\">9</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from21_10\">10</td><td nowrap=\"nowrap\">TARGET_URL&nbsp;=&nbsp;os.getenv('TARGET_URL',&nbsp;'https://github.com')</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to21_10\">10</td><td nowrap=\"nowrap\">TARGET_URL&nbsp;=&nbsp;os.getenv('TARGET_URL',&nbsp;'https://github.com')</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from21_11\">11</td><td nowrap=\"nowrap\">PATH_START&nbsp;=&nbsp;os.getenv('PATH_START',&nbsp;'/electerm/electerm/releases/download/')</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to21_11\">11</td><td nowrap=\"nowrap\">PATH_START&nbsp;=&nbsp;os.getenv('PATH_START',&nbsp;'/electerm/electerm/releases/download/')</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from21_12\">12</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to21_12\">12</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from21_13\">13</td><td nowrap=\"nowrap\">print(TARGET_URL,&nbsp;PATH_START)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to21_13\">13</td><td nowrap=\"nowrap\">print(TARGET_URL,&nbsp;PATH_START)</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from21_14\">14</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to21_14\">14</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from21_15\">15</td><td nowrap=\"nowrap\">@app.route('/',&nbsp;defaults={'path':&nbsp;''})</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to21_15\">15</td><td nowrap=\"nowrap\">@app.route('/',&nbsp;defaults={'path':&nbsp;''})</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from21_16\">16</td><td nowrap=\"nowrap\">@app.route('/&lt;path:path&gt;')</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to21_16\">16</td><td nowrap=\"nowrap\">@app.route('/&lt;path:path&gt;')</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from21_17\">17</td><td nowrap=\"nowrap\">def&nbsp;proxy(path):</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to21_17\">17</td><td nowrap=\"nowrap\">def&nbsp;proxy(path):</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from21_18\">18</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;print(path)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to21_18\">18</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;print(path)</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from21_19\">19</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;if&nbsp;request.method&nbsp;==&nbsp;'GET'&nbsp;and&nbsp;('/'&nbsp;+&nbsp;path).startswith(PATH_START):</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to21_19\">19</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;if&nbsp;request.method&nbsp;==&nbsp;'GET'&nbsp;and&nbsp;('/'&nbsp;+&nbsp;path).startswith(PATH_START):</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from21_20\">20</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;#&nbsp;Make&nbsp;the&nbsp;request&nbsp;to&nbsp;TARGET_URL,&nbsp;do&nbsp;not&nbsp;automatically&nbsp;follow&nbsp;redirect</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to21_20\">20</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;#&nbsp;Make&nbsp;the&nbsp;request&nbsp;to&nbsp;TARGET_URL,&nbsp;do&nbsp;not&nbsp;automatically&nbsp;follow&nbsp;redirect</td></tr>\n", "            <tr><td class=\"diff_next\" id=\"difflib_chg_to21__0\"></td><td class=\"diff_header\" id=\"from21_21\">21</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;response&nbsp;=&nbsp;requests.get(TARGET_URL&nbsp;+&nbsp;'/'&nbsp;+&nbsp;path,&nbsp;stream=True,&nbsp;allow_redirects=False)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to21_21\">21</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;response&nbsp;=&nbsp;requests.get(TARGET_URL&nbsp;+&nbsp;'/'&nbsp;+&nbsp;path,&nbsp;stream=True,&nbsp;allow_redirects=False)</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from21_22\">22</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to21_22\">22</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from21_23\">23</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;#&nbsp;If&nbsp;the&nbsp;response&nbsp;is&nbsp;a&nbsp;redirect,&nbsp;follow&nbsp;the&nbsp;redirect&nbsp;and&nbsp;get&nbsp;the&nbsp;new&nbsp;response</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to21_23\">23</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;#&nbsp;If&nbsp;the&nbsp;response&nbsp;is&nbsp;a&nbsp;redirect,&nbsp;follow&nbsp;the&nbsp;redirect&nbsp;and&nbsp;get&nbsp;the&nbsp;new&nbsp;response</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from21_24\">24</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;if&nbsp;response.status_code&nbsp;in&nbsp;(301,&nbsp;302,&nbsp;303,&nbsp;307,&nbsp;308):</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to21_24\">24</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;if&nbsp;response.status_code&nbsp;in&nbsp;(301,&nbsp;302,&nbsp;303,&nbsp;307,&nbsp;308):</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from21_25\">25</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;print('path',&nbsp;response.headers['Location'])</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to21_25\">25</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;print('path',&nbsp;response.headers['Location'])</td></tr>\n", "            <tr><td class=\"diff_next\"><a href=\"#difflib_chg_to21__top\">t</a></td><td class=\"diff_header\" id=\"from21_26\">26</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;response&nbsp;=&nbsp;requests.get(response.headers['Location'],&nbsp;stream=True)</td><td class=\"diff_next\"><a href=\"#difflib_chg_to21__top\">t</a></td><td class=\"diff_header\" id=\"to21_26\">26</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;response&nbsp;=&nbsp;requests.get(response.headers['Location'],&nbsp;stream=True<span class=\"diff_add\">,&nbsp;decode_content=True</span>)</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from21_27\">27</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to21_27\">27</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from21_28\">28</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;def&nbsp;generate():</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to21_28\">28</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;def&nbsp;generate():</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from21_29\">29</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;for&nbsp;chunk&nbsp;in&nbsp;response.iter_content(chunk_size=8192):</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to21_29\">29</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;for&nbsp;chunk&nbsp;in&nbsp;response.iter_content(chunk_size=8192):</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from21_30\">30</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;yield&nbsp;chunk</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to21_30\">30</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;yield&nbsp;chunk</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from21_31\">31</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to21_31\">31</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from21_32\">32</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;#&nbsp;Return&nbsp;the&nbsp;proxied&nbsp;response</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to21_32\">32</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;#&nbsp;Return&nbsp;the&nbsp;proxied&nbsp;response</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from21_33\">33</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;Response(generate(),&nbsp;headers=dict(response.headers),&nbsp;status=response.status_code)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to21_33\">33</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;Response(generate(),&nbsp;headers=dict(response.headers),&nbsp;status=response.status_code)</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from21_34\">34</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;else:</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to21_34\">34</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;else:</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from21_35\">35</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;'Not&nbsp;Found',&nbsp;404</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to21_35\">35</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;'Not&nbsp;Found',&nbsp;404</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from21_36\">36</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to21_36\">36</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from21_37\">37</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to21_37\">37</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from21_38\">38</td><td nowrap=\"nowrap\">@app.route('/test')</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to21_38\">38</td><td nowrap=\"nowrap\">@app.route('/test')</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from21_39\">39</td><td nowrap=\"nowrap\">def&nbsp;test():</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to21_39\">39</td><td nowrap=\"nowrap\">def&nbsp;test():</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from21_40\">40</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;'ok'</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to21_40\">40</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;'ok'</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from21_41\">41</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to21_41\">41</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from21_42\">42</td><td nowrap=\"nowrap\">if&nbsp;__name__&nbsp;==&nbsp;\"__main__\":</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to21_42\">42</td><td nowrap=\"nowrap\">if&nbsp;__name__&nbsp;==&nbsp;\"__main__\":</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from21_43\">43</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;HOST&nbsp;=&nbsp;os.getenv('HOST',&nbsp;'127.0.0.1')</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to21_43\">43</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;HOST&nbsp;=&nbsp;os.getenv('HOST',&nbsp;'127.0.0.1')</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from21_44\">44</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;PORT&nbsp;=&nbsp;int(os.getenv('PORT',&nbsp;3000))</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to21_44\">44</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;PORT&nbsp;=&nbsp;int(os.getenv('PORT',&nbsp;3000))</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from21_45\">45</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to21_45\">45</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from21_46\">46</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;app.run(host=HOST,&nbsp;port=PORT)</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to21_46\">46</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;app.run(host=HOST,&nbsp;port=PORT)</td></tr>\n", "        </tbody>\n", "    </table>\n", "    <table class=\"diff\" summary=\"Legends\">\n", "        <tr> <th colspan=\"2\"> Legends </th> </tr>\n", "        <tr> <td> <table border=\"\" summary=\"Colors\">\n", "                      <tr><th> Colors </th> </tr>\n", "                      <tr><td class=\"diff_add\">&nbsp;Added&nbsp;</td></tr>\n", "                      <tr><td class=\"diff_chg\">Changed</td> </tr>\n", "                      <tr><td class=\"diff_sub\">Deleted</td> </tr>\n", "                  </table></td>\n", "             <td> <table border=\"\" summary=\"Links\">\n", "                      <tr><th colspan=\"2\"> Links </th> </tr>\n", "                      <tr><td>(f)irst change</td> </tr>\n", "                      <tr><td>(n)ext change</td> </tr>\n", "                      <tr><td>(t)op</td> </tr>\n", "                  </table></td> </tr>\n", "    </table>\n", "</body>\n", "\n", "</html>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<h3>rewrite with python3 (accept)</h3>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["\n", "<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Transitional//EN\"\n", "          \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd\">\n", "\n", "<html>\n", "\n", "<head>\n", "    <meta http-equiv=\"Content-Type\"\n", "          content=\"text/html; charset=utf-8\" />\n", "    <title></title>\n", "    <style type=\"text/css\">\n", "        table.diff {font-family:Courier; border:medium;}\n", "        .diff_header {background-color:#e0e0e0}\n", "        td.diff_header {text-align:right}\n", "        .diff_next {background-color:#c0c0c0}\n", "        .diff_add {background-color:#aaffaa}\n", "        .diff_chg {background-color:#ffff77}\n", "        .diff_sub {background-color:#ffaaaa}\n", "    </style>\n", "</head>\n", "\n", "<body>\n", "    \n", "    <table class=\"diff\" id=\"difflib_chg_to22__top\"\n", "           cellspacing=\"0\" cellpadding=\"0\" rules=\"groups\" >\n", "        <colgroup></colgroup> <colgroup></colgroup> <colgroup></colgroup>\n", "        <colgroup></colgroup> <colgroup></colgroup> <colgroup></colgroup>\n", "        \n", "        <tbody>\n", "            <tr><td class=\"diff_next\" id=\"difflib_chg_to22__1\"><a href=\"#difflib_chg_to22__1\">n</a></td><td class=\"diff_header\" id=\"from22_1\">1</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">require('dotenv').config()</span></td><td class=\"diff_next\"><a href=\"#difflib_chg_to22__1\">n</a></td><td class=\"diff_header\" id=\"to22_1\">1</td><td nowrap=\"nowrap\"><span class=\"diff_add\">import&nbsp;os</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from22_2\">2</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">const&nbsp;{&nbsp;createProxyMiddleware&nbsp;}&nbsp;=&nbsp;require('http-proxy-middleware')</span></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to22_2\">2</td><td nowrap=\"nowrap\"><span class=\"diff_add\">from&nbsp;flask&nbsp;import&nbsp;Flask,&nbsp;request,&nbsp;redirect,&nbsp;Response</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from22_3\">3</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">const&nbsp;app&nbsp;=&nbsp;require('express')()</span></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to22_3\">3</td><td nowrap=\"nowrap\"><span class=\"diff_add\">import&nbsp;requests</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from22_4\">4</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">app.disable('x-powered-by')</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from22_5\">5</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to22_4\">4</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\" id=\"difflib_chg_to22__2\"><a href=\"#difflib_chg_to22__2\">n</a></td><td class=\"diff_header\" id=\"from22_6\">6</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">const&nbsp;{</span></td><td class=\"diff_next\"><a href=\"#difflib_chg_to22__2\">n</a></td><td class=\"diff_header\" id=\"to22_5\">5</td><td nowrap=\"nowrap\"><span class=\"diff_add\">app&nbsp;=&nbsp;Flask(__name__)</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from22_7\">7</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;TARGET_URL&nbsp;=&nbsp;'https://github.com',</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from22_8\">8</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;PATH_START&nbsp;=&nbsp;'/electerm/electerm/releases/download/'</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from22_9\">9</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">}&nbsp;=&nbsp;process.env</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from22_10\">10</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to22_6\">6</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\" id=\"difflib_chg_to22__3\"><a href=\"#difflib_chg_to22__3\">n</a></td><td class=\"diff_header\" id=\"from22_11\">11</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">const&nbsp;filter&nbsp;=&nbsp;function&nbsp;(pathname,&nbsp;req)&nbsp;{</span></td><td class=\"diff_next\"><a href=\"#difflib_chg_to22__3\">n</a></td><td class=\"diff_header\" id=\"to22_7\">7</td><td nowrap=\"nowrap\"><span class=\"diff_add\">TARGET_URL&nbsp;=&nbsp;os.getenv('TARGET_URL',&nbsp;'https://github.com')</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from22_12\">12</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;return&nbsp;pathname.startsWith(PATH_START)&nbsp;&amp;&amp;</span></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to22_8\">8</td><td nowrap=\"nowrap\"><span class=\"diff_add\">PATH_START&nbsp;=&nbsp;os.getenv('PATH_START',&nbsp;'/electerm/electerm/releases/download/')</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from22_13\">13</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;req.method&nbsp;===&nbsp;'GET'</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from22_14\">14</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">}</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from22_15\">15</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to22_9\">9</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"><a href=\"#difflib_chg_to22__4\">n</a></td><td class=\"diff_header\" id=\"from22_16\">16</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">const&nbsp;opts&nbsp;=&nbsp;{</span></td><td class=\"diff_next\"><a href=\"#difflib_chg_to22__4\">n</a></td><td class=\"diff_header\" id=\"to22_10\">10</td><td nowrap=\"nowrap\"><span class=\"diff_add\">@app.route('/',&nbsp;defaults={'path':&nbsp;''})</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from22_17\">17</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;target:&nbsp;TARGET_URL,</span></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to22_11\">11</td><td nowrap=\"nowrap\"><span class=\"diff_add\">@app.route('/&lt;path:path&gt;')</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from22_18\">18</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;//&nbsp;followRedirects:&nbsp;true,</span></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to22_12\">12</td><td nowrap=\"nowrap\"><span class=\"diff_add\">def&nbsp;proxy(path):</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from22_19\">19</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;changeOrigin:&nbsp;true,</span></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to22_13\">13</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;if&nbsp;request.method&nbsp;==&nbsp;'GET'&nbsp;and&nbsp;path.startswith(PATH_START):</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from22_20\">20</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;//&nbsp;pathRewrite:&nbsp;function&nbsp;(path,&nbsp;req)&nbsp;{&nbsp;return&nbsp;path.replace('/download',&nbsp;'/')&nbsp;},</span></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to22_14\">14</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;response&nbsp;=&nbsp;requests.get(TARGET_URL&nbsp;+&nbsp;'/'&nbsp;+&nbsp;path,&nbsp;stream=True)</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from22_21\">21</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;followRedirects:&nbsp;true,</span></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to22_15\">15</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from22_22\">22</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;//&nbsp;Define&nbsp;the&nbsp;handleProxyResponse&nbsp;function</span></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to22_16\">16</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;#&nbsp;Check&nbsp;if&nbsp;the&nbsp;response&nbsp;is&nbsp;a&nbsp;redirect</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from22_23\">23</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;onProxyRes:&nbsp;function&nbsp;handleProxyResponse&nbsp;(proxyRes,&nbsp;req,&nbsp;res)&nbsp;{</span></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to22_17\">17</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;if&nbsp;response.status_code&nbsp;==&nbsp;302:</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from22_24\">24</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;if&nbsp;(proxyRes.statusCode&nbsp;===&nbsp;302)&nbsp;{</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from22_25\">25</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;const&nbsp;redirectUrl&nbsp;=&nbsp;proxyRes.headers.location</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from22_26\">26</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class=\"diff_chg\">const</span>&nbsp;one<span class=\"diff_chg\">Y</span>ear<span class=\"diff_chg\">I</span>n<span class=\"diff_chg\">S</span>econds&nbsp;=&nbsp;365&nbsp;*&nbsp;24&nbsp;*&nbsp;60&nbsp;*&nbsp;60</td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to22_18\">18</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class=\"diff_chg\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>&nbsp;one<span class=\"diff_chg\">_y</span>ear<span class=\"diff_chg\">_i</span>n<span class=\"diff_chg\">_s</span>econds&nbsp;=&nbsp;365&nbsp;*&nbsp;24&nbsp;*&nbsp;60&nbsp;*&nbsp;60</td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from22_27\">27</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class=\"diff_chg\">const</span>&nbsp;cache<span class=\"diff_chg\">C</span>ontrol&nbsp;=&nbsp;<span class=\"diff_chg\">`</span>public,&nbsp;max-age=<span class=\"diff_sub\">$</span>{one<span class=\"diff_chg\">Y</span>ear<span class=\"diff_chg\">I</span>n<span class=\"diff_chg\">S</span>econds}<span class=\"diff_chg\">`</span></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to22_19\">19</td><td nowrap=\"nowrap\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class=\"diff_chg\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>&nbsp;cache<span class=\"diff_chg\">_c</span>ontrol&nbsp;=&nbsp;<span class=\"diff_chg\">f'</span>public,&nbsp;max-age={one<span class=\"diff_chg\">_y</span>ear<span class=\"diff_chg\">_i</span>n<span class=\"diff_chg\">_s</span>econds}<span class=\"diff_chg\">'</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from22_28\">28</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;const&nbsp;expires&nbsp;=&nbsp;new&nbsp;Date(Date.now()&nbsp;+&nbsp;oneYearInSeconds&nbsp;*&nbsp;1000).toUTCString()</span></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to22_20\">20</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from22_29\">29</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;res.set('Cache-Control',&nbsp;cacheControl)</span></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to22_21\">21</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;#&nbsp;Set&nbsp;the&nbsp;Cache-Control&nbsp;and&nbsp;Expires&nbsp;headers</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from22_30\">30</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;res.set('Expires',&nbsp;expires)</span></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to22_22\">22</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;response.headers['Cache-Control']&nbsp;=&nbsp;cache_control</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from22_31\">31</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;res.redirect(redirectUrl)</span></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to22_23\">23</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;response.headers['Expires']&nbsp;=&nbsp;one_year_in_seconds</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from22_32\">32</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;&nbsp;&nbsp;}</span></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to22_24\">24</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from22_33\">33</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;}</span></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to22_25\">25</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;redirect(response.headers['location'],&nbsp;code=302)</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from22_34\">34</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">}</span></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to22_26\">26</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to22_27\">27</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;def&nbsp;generate():</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to22_28\">28</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;for&nbsp;chunk&nbsp;in&nbsp;response.iter_content(chunk_size=8192):</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to22_29\">29</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;yield&nbsp;chunk</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to22_30\">30</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></td></tr>\n", "            <tr><td class=\"diff_next\" id=\"difflib_chg_to22__4\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to22_31\">31</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;#&nbsp;Return&nbsp;the&nbsp;proxied&nbsp;response</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to22_32\">32</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;Response(generate(),&nbsp;headers=response.headers,&nbsp;status=response.status_code)</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to22_33\">33</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;else:</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to22_34\">34</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;'Not&nbsp;Found',&nbsp;404</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from22_35\">35</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to22_35\">35</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\" id=\"difflib_chg_to22__5\"><a href=\"#difflib_chg_to22__5\">n</a></td><td class=\"diff_header\" id=\"from22_36\">36</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">app.use(</span></td><td class=\"diff_next\"><a href=\"#difflib_chg_to22__5\">n</a></td><td class=\"diff_header\" id=\"to22_36\">36</td><td nowrap=\"nowrap\"><span class=\"diff_add\">@app.route('/test')</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from22_37\">37</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;'/',</span></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to22_37\">37</td><td nowrap=\"nowrap\"><span class=\"diff_add\">def&nbsp;test():</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from22_38\">38</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;createProxyMiddleware(filter,&nbsp;opts)</span></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to22_38\">38</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;'ok'</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from22_39\">39</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">)</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from22_40\">40</td><td nowrap=\"nowrap\"></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to22_39\">39</td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"><a href=\"#difflib_chg_to22__top\">t</a></td><td class=\"diff_header\" id=\"from22_41\">41</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">app.get(</span></td><td class=\"diff_next\"><a href=\"#difflib_chg_to22__top\">t</a></td><td class=\"diff_header\" id=\"to22_40\">40</td><td nowrap=\"nowrap\"><span class=\"diff_add\">if&nbsp;__name__&nbsp;==&nbsp;\"__main__\":</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from22_42\">42</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;'/test',</span></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to22_41\">41</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;HOST&nbsp;=&nbsp;os.getenv('HOST',&nbsp;'127.0.0.1')</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from22_43\">43</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;(req,&nbsp;res)&nbsp;=&gt;&nbsp;res.send('ok')</span></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to22_42\">42</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;PORT&nbsp;=&nbsp;int(os.getenv('PORT',&nbsp;3000))</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from22_44\">44</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">)</span></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to22_43\">43</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from22_45\">45</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;</span></td><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"to22_44\">44</td><td nowrap=\"nowrap\"><span class=\"diff_add\">&nbsp;&nbsp;&nbsp;&nbsp;app.run(host=HOST,&nbsp;port=PORT)</span></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from22_46\">46</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">const&nbsp;{</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from22_47\">47</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;HOST&nbsp;=&nbsp;'127.0.0.1',</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from22_48\">48</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;PORT&nbsp;=&nbsp;3000</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from22_49\">49</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">}&nbsp;=&nbsp;process.env</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from22_50\">50</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from22_51\">51</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">app.listen(PORT,&nbsp;HOST,&nbsp;()&nbsp;=&gt;&nbsp;{</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from22_52\">52</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">&nbsp;&nbsp;console.log(`server&nbsp;is&nbsp;running&nbsp;at&nbsp;http://${HOST}:${PORT}`)</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "            <tr><td class=\"diff_next\"></td><td class=\"diff_header\" id=\"from22_53\">53</td><td nowrap=\"nowrap\"><span class=\"diff_sub\">})</span></td><td class=\"diff_next\"></td><td class=\"diff_header\"></td><td nowrap=\"nowrap\"></td></tr>\n", "        </tbody>\n", "    </table>\n", "    <table class=\"diff\" summary=\"Legends\">\n", "        <tr> <th colspan=\"2\"> Legends </th> </tr>\n", "        <tr> <td> <table border=\"\" summary=\"Colors\">\n", "                      <tr><th> Colors </th> </tr>\n", "                      <tr><td class=\"diff_add\">&nbsp;Added&nbsp;</td></tr>\n", "                      <tr><td class=\"diff_chg\">Changed</td> </tr>\n", "                      <tr><td class=\"diff_sub\">Deleted</td> </tr>\n", "                  </table></td>\n", "             <td> <table border=\"\" summary=\"Links\">\n", "                      <tr><th colspan=\"2\"> Links </th> </tr>\n", "                      <tr><td>(f)irst change</td> </tr>\n", "                      <tr><td>(n)ext change</td> </tr>\n", "                      <tr><td>(t)op</td> </tr>\n", "                  </table></td> </tr>\n", "    </table>\n", "</body>\n", "\n", "</html>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["for obj in objs[:10]:\n", "    display(HTML(f\"<h3>{obj['human_annotated_instruction'] or obj['instruction']} ({obj['status']})</h3>\"))\n", "    display(HTML(as_diff_html(obj)))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}