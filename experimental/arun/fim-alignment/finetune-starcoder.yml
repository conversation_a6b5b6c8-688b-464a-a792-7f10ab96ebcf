# Configuration for FIM alignment finetuning on ~4B tokens across 8 languages.

includes:
  - augment_configs/starcoder/model/starcoder.yml
determined:
  name: fim-alignment-starcoder-all-400Mtoks-ignore-pads
  workspace: Dev
  project: arun
  labels: ["starcoder", "fim-alignment"]
  max_restarts: 0
  perform_initial_validation: True

augment:
  podspec_path: "8xA100.yaml"
  gpu_count: 8
  save_trial_best: 0

  checkpoint_handling:
    action: "gc"
  entrypoint: "/run/determined/workdir/research/gpt-neox/jobs/determined.sh"

overrides:
  wandb_name: fim-alignment-starcoder-all-400Mtoks-ignore-pads
  wandb_group: arun # change this to your group
  wandb_project: new-FIM-sampler

  # data (sequence lengths are 8192 + 1)
  train_data_paths:
    [
      "/mnt/efs/augment/data/processed/new-fim/starcoder-all-500K-splits/train",
    ]
  valid_data_paths:
    [
      "/mnt/efs/augment/data/processed/new-fim/starcoder-all-500K-splits/valid",
    ]
  max_valid_data_size: 1_600
  test_data_paths:
    [
      "/mnt/efs/augment/data/processed/new-fim/starcoder-all-500K-splits/test",
    ]
  data_impl: mmap
  dataset_type: direct

  # 32 * 8k = 0.25M tokens per step
  train_micro_batch_size_per_gpu: 2
  gradient_accumulation_steps: 2
  train_batch_size: 16   # 2 * 8 GPUs

  # 2k * 0.25M = 0.5B tokens = ~0.5 epoch
  train_iters: 8000
  warmup: 0.01
  lr_decay_style: constant

  optimizer:
    params:
      betas:
        - 0.9
        - 0.95
      eps: 1.0e-08
      lr: 1.6e-05
    type: Adam

  weight-decay: 0.1

  # Eval/save frequency
  eval_interval: 500
  save_interval: 500
  log-interval: 100

  # FIM context loss mask
  loss_mask_mode: pad
  extra_loss_masks:
    - pad
    - fim-context
    - eod-only
