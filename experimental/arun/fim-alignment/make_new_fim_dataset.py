"""This script generates a new FIM dataset from the Stack using FimSampler."""
# pylint: disable=no-name-in-module
# %%

import json
from pathlib import Path
from typing import Sequence

from tqdm import tqdm

from research.fim.fim_prompt import FimPrompt<PERSON><PERSON><PERSON><PERSON>, FormattedFimProblem
from research.static_analysis.common import LanguageID
from research.utils.generate_fim_data import FimDataProcessor, IntRange
from research.utils.token_array_utils import (
    load_indexed_dataset,
    make_indexed_dataset,
    split_shuffle_pack_dataset,
)


def as_million(x):
    return f"{x / 1e6:.3}M"


def prob_statistics(probs: Sequence[FormattedFimProblem]):
    total_tks = sum(len(p.tokens) for p in probs)
    middle_tks = sum(len(p.middle_range) for p in probs)
    prefix_tks = sum(len(p.prefix_range) for p in probs)
    suffix_tks = sum(len(p.suffix_range) for p in probs)
    empty_suffix_ratio = (
        sum(not bool(p.suffix_range) for p in probs) / len(probs) if probs else 0
    )

    return {
        "n_probs": str(len(probs)),
        "total_tks": as_million(total_tks),
        "tks_per_prob": f"{total_tks / len(probs):.1f}" if probs else 0,
        "middle_tks": as_million(middle_tks),
        "prefix_tks": as_million(prefix_tks),
        "suffix_tks": as_million(suffix_tks),
        "empty_suffix_ratio": f"{empty_suffix_ratio:.1%}",
    }


# %%
seq_length = 1024 * 8
processor = FimDataProcessor(
    FimPromptFormatter.for_star_coder, seq_len_range=IntRange(1024, seq_length // 2)
)
stack_path = Path("/mnt/efs/aug-cw-lga1/data/raw/the-stack-dedup.2023-02-04/data/")
language_maps: dict[str, LanguageID] = {
    "python": "python",
    "c": "cpp",
    "c++": "cpp",
    "javascript": "javascript",
    "typescript": "typescript",
    "java": "java",
    "go": "go",
    "rust": "rust",
}
files_per_lang = 500_000
# files_per_lang = 5_000

problems, errors = processor.fim_data_from_the_stack(
    stack_path,
    language_maps=language_maps,
    files_per_lang=files_per_lang,
)
for e in errors[:10]:
    print(e)

print(f"{len(problems)=}, {len(errors)=}")
print(stats := prob_statistics(problems))

n_prob_k = len(problems) // 1000
dataset_dir = Path("/mnt/efs/augment/data/processed/new-fim/starcoder-all-500K")

formatter = FimPromptFormatter.for_star_coder()
make_indexed_dataset(
    dataset_dir / "dataset",
    tqdm([p.tokens for p in problems], "make_indexed_dataset"),
)
idata = load_indexed_dataset(dataset_dir / "dataset")
(dataset_dir / "fim_stats.txt").write_text(json.dumps(stats))
print(f"Indexed dataset saved to: {dataset_dir}")

splits_dir = Path(str(dataset_dir) + "-splits")
split_shuffle_pack_dataset(
    idata,
    splits_dir,
    split_sizes={"train": 0.95, "valid": 0.05, "test": 0.05},
    seq_len=seq_length + 1,
    tokenizer=formatter.tokenizer,
)
print(f"Splits saving to: {splits_dir}")
