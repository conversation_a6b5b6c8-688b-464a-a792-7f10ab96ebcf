#!/usr/bin/env bash
# Simple script to rename imports from one to the other.
#
# Usage:
#   rename_imports.sh   # Show changes.
#   rename_imports.sh -e  # Make changes.

usage() { echo "Usage: $0 [-e]" 1>&2; exit 1; }

FORCE_EDIT=0
while getopts ":e" o; do
    case "${o}" in
        e)
            FORCE_EDIT=1
            ;;
        *)
            usage
            ;;
    esac
done

echo "Started with..."
# Figure out which files are problems
egrep "(from|import) augment\." -R . --include="*.py" --include="*.ipynb"

if [[ ${FORCE_EDIT} == 0 ]]; then
     echo "If you want to edit the files above, please rerun with the '-e' flag:"
     usage
fi

# Fix the obvious cases
for dir in research experimental deploy; do
    for submodule in research experimental; do
        find $dir -type f \( -name '*.py' -or -name "*.ipynb" \) -exec \
            sed -i "s/import augment\.${submodule}/import ${submodule}/" {} +
        find $dir -type f \( -name '*.py' -or -name "*.ipynb" \) -exec \
            sed -i "s/from augment\.${submodule}/from ${submodule}/" {} +
    done
done;

echo "Ended with..."
# Figure out the remaining problems
egrep "(from|import) augment\." -R . --include="*.py" --include="*.ipynb"
