"""Fix-up dual encoder checkpoints.

We previously had a hack to deleted the document model keys from the state dict, but
this is not necessary and problematic when there are e.g., multiple DualEncoderModels in
a checkpoint or other complicated situations.

This script fixes the state dict by re-inserting the document model keys. Note that the
way the script works is heuristic and will only work for checkpoints where any key
with `query_model` is due to a DualEncoderModel.

Usage:
$ python3 fixup_dual_encoder_checkpoints.py <path_to_checkpoint>
"""

import argparse
import json
import logging
from pathlib import Path

import torch

from research.fastbackward import distributed, retrieval_models

logger = logging.getLogger(__name__)


def fixup_checkpoint(checkpoint_path: Path):
    component_configs = json.loads((checkpoint_path / "params.json").read_text())
    components_to_fixup = []
    for component_name, component_config in component_configs.items():
        if (
            component_config["component_name"]
            != "research.fastbackward.retrieval_models.DualEncoderModel"
        ):
            continue
        if component_config["query_model"] != component_config["doc_model"]:
            logger.info(
                f"Skipping dual-encoder model {component_name} because "
                f"{component_config['query_model']=} != {component_config['doc_model']=}"
            )
            continue
        components_to_fixup.append(component_name)

    if not components_to_fixup:
        logger.info("No dual-encoder models to fixup.")
        return
    else:
        logger.info("Attempting to fixup dual-encoder models: %s", components_to_fixup)

    if (checkpoint_path / "consolidated.00.bk.pth").exists():
        # If the backup exists, load from it.
        logger.info("Loading from backup.")
        state_dict = torch.load(checkpoint_path / "consolidated.00.bk.pth")
    else:
        state_dict = torch.load(checkpoint_path / "consolidated.00.pth")
        torch.save(state_dict, checkpoint_path / "consolidated.00.bk.pth")

    for key, value in list(state_dict.items()):
        if "query_model" not in key.split("."):
            continue
        logger.info("Adding a doc_model key for %s", key)
        state_dict[update_key(key)] = value
    torch.save(state_dict, checkpoint_path / "consolidated.00.pth")

    logger.info("Validating we can load the checkpoint.")
    distributed.init_distributed_for_training(1)
    retrieval_models.load_checkpoint(checkpoint_path)
    logger.info("Done.")


def update_key(key: str) -> str:
    parts = key.split(".")
    modify_count = 0
    for i, part in enumerate(parts):
        if part == "query_model":
            parts[i] = "doc_model"
            modify_count += 1

    assert (
        modify_count == 1
    ), f"Expected to modify exactly one key, but modified {modify_count} keys."

    return ".".join(parts)


def main():
    logging.basicConfig(level=logging.INFO)

    parser = argparse.ArgumentParser()
    parser.add_argument("checkpoint_path", type=Path)
    args = parser.parse_args()
    fixup_checkpoint(args.checkpoint_path)


if __name__ == "__main__":
    main()
