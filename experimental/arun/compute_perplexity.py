"""<PERSON><PERSON><PERSON> to compute the perplexity of a model on a dataset."""

import argparse
import math

import tqdm
from megatron.data import indexed_dataset
from megatron.tokenizer.tokenizer import AbstractTokenizer, get_tokenizer

from base.fastforward import fwd_utils
from base.fastforward.starcoder import fwd_starcoder, fwd_starcoder_fp8


def get_target_span(tokens: list[int], tokenizer: AbstractTokenizer):
    target_start = tokens.index(tokenizer.fim_middle_id)
    try:
        # End after the EOD token.
        target_end = tokens.index(tokenizer.eod_id) + 1
        return target_start, target_end
    except ValueError:
        pass
    try:
        # End or at the first PAD token.
        target_end = tokens.index(tokenizer.pad_id)
    except ValueError:
        target_end = len(tokens)
    return target_start, target_end


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "-cp", "--checkpoint_path", type=str, required=True, help="model to use"
    )
    parser.add_argument("--data", type=str, required=True, help="data to use")
    parser.add_argument("--fp8", action="store_true", help="use fp8")
    parser.add_argument("--with_target", "-t", action="store_true", help="use target")
    parser.add_argument(
        "--limit", type=int, default=None, help="limit the number of examples"
    )

    args = parser.parse_args()

    dataset = indexed_dataset.make_dataset(args.data, "mmap", skip_warmup=True)
    assert dataset is not None
    examples = [dataset.get(i).tolist() for i in range(args.limit or len(dataset))]

    tokenizer = get_tokenizer("StarCoderTokenizer")

    model_spec = fwd_utils.get_model_spec_from_neox_checkpoint(args.checkpoint_path)

    if args.fp8:
        step = fwd_starcoder_fp8.generate_step_fn(model_spec)
    else:
        step = fwd_starcoder.generate_step_fn(model_spec)
    step = fwd_utils.pad_and_step(step, [32, 256, 1024, 2048, 4096, 8192])
    attn = fwd_starcoder.StarcoderAttentionFactory(model_spec)(8192)

    log_ppl, cnt = 0.0, 0
    with tqdm.tqdm(examples) as t:
        for example in t:
            if args.with_target:
                target_start, target_end = get_target_span(example, tokenizer)
                if target_start == target_end:
                    continue
                input_, target = (
                    example[:target_start],
                    example[target_start:target_end],
                )
                sample_log_ppl = fwd_utils.log_perplexity_continuation(
                    step, attn, input_, target
                )
                sample_size = len(target) - 1
            else:
                sample_log_ppl = fwd_utils.log_perplexity(step, attn, example)
                sample_size = len(example) - 1

            cnt += sample_size
            log_ppl += (sample_log_ppl - log_ppl) * sample_size / cnt
            t.set_description(f"Log Perplexity: {log_ppl:0.6f}")

    print(f"Log Perplexity: {log_ppl}")
    print(f"Perplexity: {math.exp(-log_ppl)}")


if __name__ == "__main__":
    main()
