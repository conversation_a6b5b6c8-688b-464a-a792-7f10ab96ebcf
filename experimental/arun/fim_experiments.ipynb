{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Finetuning to align FIM model completions to semantic boundaries"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Takeaways\n", "* The base StarCoder models do well on the simple FIM examples with a short context\n", "  window and few functions, but *reliably generates \"scope-breaking\" completions* in\n", "  more realistic scenarios.\n", "* The most common location for scope-breaking completions appear to be places where\n", "  *there is no reasonable code* that could be added in the middle.\n", "* The *current set of finetuned StarCoder models generate fewer scope-breaking*\n", "  completions, but it is still not perfect. We observed one regression on FIM behavior\n", "  (`fim_docstring.py`) and three improvements (`fim_argparse.py`,\n", "   `fim_dataclass_method.py`, and `fim_multi_function_1.py`). No regressions were\n", "   observed on the main Vulcan dataset.\n", "* Additionally, when models do not see a suffix, they tend to generate the rest of the\n", "  file. This leads to long and unintuitive completions.\n", "\n", "#### Next Steps\n", "* Train on more FIM data for longer. We could also experiment with filtering on higher\n", "  quality repos during fine-tuning.\n", "* We plan to address the whole-file completions when generating without a suffix by\n", "  conditioning the length of the \"middle\" to be smaller. We'll do this by dropping out\n", "  random functions in the suffix to avoid overfitting the model to short files.\n", "  \n", "### Methodology\n", "* Built on <PERSON>'s suite of simple FIM examples. These were mostly already solved by\n", "  starcoder, so farmed a set of more realistic FIM settings in our codebase using\n", "  the VS Code plugin. The examples were simplified and added as examples to Vulcan.\n", "* For each completion, we generate 3 samples with a temperature of 0.2 to better analyze\n", "  the distributional behavior of the model.\n", "\n", "#### Caveats\n", "* The analysis is restricted to Python files.\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# Load models. We are only comparing StarCoder, and doing so via model-server.\n", "import logging\n", "logging.basicConfig(level=logging.WARNING)\n", "\n", "from research.models.core import RemoteModel, get_model\n", "\n", "# # All our models currently are loaded on the same machine.\n", "# STARCODER_URL = 'http://216.153.48.243:5000'\n", "# model = RemoteModel(STARCODER_URL)\n", "# model.load()\n", "\n", "# model_custom = RemoteModel('http://10.145.97.199:5000')\n", "# model_custom.load()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Vulcan Test Suite"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 10 FIM tests.\n", "Loaded 23 general tests.\n"]}], "source": ["# Load the Vulcan tests\n", "from research.eval.vulcan import load_tests, VulcanTest\n", "from research.eval.patch_lib import Patch\n", "# NOTE: Set test_pattern below to load a specific test.\n", "VULCAN_TESTS = load_tests()\n", "FIM_TESTS = [test for test in VULCAN_TESTS if \"fim\" in test.meta.get(\"tags\", [])]\n", "VULCAN_TESTS = [test for test in VULCAN_TESTS if \"fim\" not in test.meta.get(\"tags\", [])]\n", "print(f\"Loaded {len(FIM_TESTS)} FIM tests.\")\n", "print(f\"Loaded {len(VULCAN_TESTS)} general tests.\")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["# Display tests\n", "import pathlib\n", "from termcolor import colored\n", "from research.core.model_input import ModelInput\n", "from research.models.meta_model import GenerationOptions\n", "\n", "def run_test(model, test: VulcanTest, temperature: float = 0, max_tokens: int = 1024) -> Patch:\n", "    # Make sure we generate sufficient tokens for the expected case. Here we are\n", "    # intentionally using an underestimate #chars:#tokens ratio.\n", "    if \"max_generated_tokens\" in test.meta:\n", "        max_tokens = int(test.meta[\"max_generated_tokens\"])\n", "    elif test.expected:\n", "        max_tokens = len(test.expected)\n", "\n", "    predicted = model.generate(\n", "        ModelInput(prefix=test.prefix, suffix=test.suffix),\n", "        GenerationOptions(temperature=temperature, max_generated_tokens=max_tokens),\n", "    )\n", "    # NOTE(arun): for a \"empty\" generation, the model and model server return None.\n", "    return test.as_patch(predicted or \"\")\n", "\n", "def display_output(test: VulcanTest, model_output: str, extra_title_content: str = \"\"):\n", "    correct = model_output.strip() == test.expected.strip()\n", "\n", "    name = pathlib.Path(test.filename).name\n", "\n", "    print(\"========================================================================================\")\n", "    print(f\"EXAMPLE: {name} {'✓' if correct else '✗'} | {extra_title_content}\") \n", "    print(\"----------------------------------------------------------------------------------------\")\n", "    print(\n", "        colored(test.prefix, \"blue\") +\n", "        colored(model_output, \"white\", \"on_black\") +\n", "        colored(test.suffix, \"blue\")\n", "    )\n", "    print(\"========================================================================================\")\n", "    print()\n", "    print()\n", "\n", "\n", "import datetime\n", "import jsonlines\n", "VULCAN_EVAL_ROOT = pathlib.Path(\"/mnt/efs/augment/eval/vulcan/\")\n", "\n", "def run_all_tests(model, model_name: str,\n", "                  tests:list[VulcanTest] = VULCAN_TESTS,\n", "                  save_results: bool = False,\n", "                  n_samples: int = 1,\n", "                  temperature: float = 0,\n", "                  ):\n", "    patches = []\n", "\n", "    print(\"========================================================================================\")\n", "    print(f\"MODEL: {model_name}\")\n", "    print(\"----------------------------------------------------------------------------------------\")\n", "    for test in tests:\n", "        for sample in range(n_samples):\n", "            model_output = run_test(model, test, temperature=temperature)\n", "            patches.append(model_output)\n", "            display_output(test, model_output.patch_content,\n", "                           extra_title_content=f\"model={model_name}, temp={temperature}, sample={sample}\")\n", "\n", "    if save_results and patches:\n", "        # Save output to a patches file.\n", "        output = (VULCAN_EVAL_ROOT / f\"{datetime.date.today():%Y%m%d}_{model_name}.jsonl\")\n", "        with output.open(\"w\") as fp:\n", "            jsonlines.Writer(fp).write_all([patch.to_json() for patch in patches])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Checkpoint starcoderbase_neox (default)"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["========================================================================================\n", "MODEL: starcoder_default\n", "----------------------------------------------------------------------------------------\n", "========================================================================================\n", "EXAMPLE: fim_docstring.py ✗ | model=starcoder_default, temp=0.2, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mdef get_odd_ints(lower, upper):\n", "    \"\"\"\u001b[0m\u001b[40m\u001b[97m\n", "    Return a list of odd integers between lower and upper.\n", "    \u001b[0m\u001b[34m\"\"\"\n", "    return [i for i in range(lower, upper) if i % 2 == 1]\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_docstring.py ✗ | model=starcoder_default, temp=0.2, sample=1\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mdef get_odd_ints(lower, upper):\n", "    \"\"\"\u001b[0m\u001b[40m\u001b[97m\n", "    Returns a list of odd integers between lower and upper.\n", "    \u001b[0m\u001b[34m\"\"\"\n", "    return [i for i in range(lower, upper) if i % 2 == 1]\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_docstring.py ✗ | model=starcoder_default, temp=0.2, sample=2\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mdef get_odd_ints(lower, upper):\n", "    \"\"\"\u001b[0m\u001b[40m\u001b[97m\n", "    Return a list of odd integers between lower and upper.\n", "    \u001b[0m\u001b[34m\"\"\"\n", "    return [i for i in range(lower, upper) if i % 2 == 1]\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_noop.py ✓ | model=starcoder_default, temp=0.2, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mdef hello_world():\u001b[0m\u001b[40m\u001b[97m\u001b[0m\u001b[34m\n", "    print(\"Hello World!\")\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_noop.py ✓ | model=starcoder_default, temp=0.2, sample=1\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mdef hello_world():\u001b[0m\u001b[40m\u001b[97m\u001b[0m\u001b[34m\n", "    print(\"Hello World!\")\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_noop.py ✓ | model=starcoder_default, temp=0.2, sample=2\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mdef hello_world():\u001b[0m\u001b[40m\u001b[97m\u001b[0m\u001b[34m\n", "    print(\"Hello World!\")\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_trailing_return_with_var.py ✗ | model=starcoder_default, temp=0.2, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mdef get_odd_ints(lower, upper):\n", "    results = []\u001b[0m\u001b[40m\u001b[97m\n", "    for i in range(lower, upper + 1):\n", "        if i % 2!= 0:\n", "            results.append(i)\u001b[0m\u001b[34m\n", "    return results\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_trailing_return_with_var.py ✗ | model=starcoder_default, temp=0.2, sample=1\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mdef get_odd_ints(lower, upper):\n", "    results = []\u001b[0m\u001b[40m\u001b[97m\n", "    for i in range(lower, upper + 1):\n", "        if i % 2 == 1:\n", "            results.append(i)\u001b[0m\u001b[34m\n", "    return results\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_trailing_return_with_var.py ✗ | model=starcoder_default, temp=0.2, sample=2\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mdef get_odd_ints(lower, upper):\n", "    results = []\u001b[0m\u001b[40m\u001b[97m\n", "    for i in range(lower, upper + 1):\n", "        if i % 2 == 1:\n", "            results.append(i)\u001b[0m\u001b[34m\n", "    return results\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_multi_function_2.py ✗ | model=starcoder_default, temp=0.2, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport copy\n", "from dataclasses import asdict, dataclass\n", "import json\n", "import pathlib\n", "\n", "@dataclass\n", "class MyClass:\n", "    def save_to_json(self, filename):\n", "        \"\"\"Saves the dataclass instance to a JSON file.\"\"\"\n", "        with pathlib.Path(filename).open(\"w\") as f:\n", "            json.dump(asdict(self), f)\n", "\n", "    @classmethod\n", "    def load_from_json(cls, filename):\n", "        \"\"\"Loads a dataclass instance from a JSON file.\"\"\"\n", "        with pathlib.Path(filename).open(\"r\") as f:\n", "            data = json.load(f)\n", "        \u001b[0m\u001b[40m\u001b[97m    data = {key: value for key, value in data.items() if value is not None}\u001b[0m\u001b[34m\n", "\n", "        return cls(**data)\n", "\n", "    def clone(self):\n", "        \"\"\"Returns a deep copy of the instance.\"\"\"\n", "        return copy.deepcopy(self)\n", "\n", "    def __repr__(self):\n", "        attributes = \",\\n  \".join(\n", "            f\"{key} = {value!r}\" for key, value in vars(self).items()\n", "        )\n", "        return f\"ModelInput(\\n  {attributes}\\n)\"\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_multi_function_2.py ✗ | model=starcoder_default, temp=0.2, sample=1\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport copy\n", "from dataclasses import asdict, dataclass\n", "import json\n", "import pathlib\n", "\n", "@dataclass\n", "class MyClass:\n", "    def save_to_json(self, filename):\n", "        \"\"\"Saves the dataclass instance to a JSON file.\"\"\"\n", "        with pathlib.Path(filename).open(\"w\") as f:\n", "            json.dump(asdict(self), f)\n", "\n", "    @classmethod\n", "    def load_from_json(cls, filename):\n", "        \"\"\"Loads a dataclass instance from a JSON file.\"\"\"\n", "        with pathlib.Path(filename).open(\"r\") as f:\n", "            data = json.load(f)\n", "        \u001b[0m\u001b[40m\u001b[97m    data = {key: value for key, value in data.items() if value is not None}\u001b[0m\u001b[34m\n", "\n", "        return cls(**data)\n", "\n", "    def clone(self):\n", "        \"\"\"Returns a deep copy of the instance.\"\"\"\n", "        return copy.deepcopy(self)\n", "\n", "    def __repr__(self):\n", "        attributes = \",\\n  \".join(\n", "            f\"{key} = {value!r}\" for key, value in vars(self).items()\n", "        )\n", "        return f\"ModelInput(\\n  {attributes}\\n)\"\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_multi_function_2.py ✗ | model=starcoder_default, temp=0.2, sample=2\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport copy\n", "from dataclasses import asdict, dataclass\n", "import json\n", "import pathlib\n", "\n", "@dataclass\n", "class MyClass:\n", "    def save_to_json(self, filename):\n", "        \"\"\"Saves the dataclass instance to a JSON file.\"\"\"\n", "        with pathlib.Path(filename).open(\"w\") as f:\n", "            json.dump(asdict(self), f)\n", "\n", "    @classmethod\n", "    def load_from_json(cls, filename):\n", "        \"\"\"Loads a dataclass instance from a JSON file.\"\"\"\n", "        with pathlib.Path(filename).open(\"r\") as f:\n", "            data = json.load(f)\n", "        \u001b[0m\u001b[40m\u001b[97m    data = {key: value for key, value in data.items() if value is not None}\u001b[0m\u001b[34m\n", "\n", "        return cls(**data)\n", "\n", "    def clone(self):\n", "        \"\"\"Returns a deep copy of the instance.\"\"\"\n", "        return copy.deepcopy(self)\n", "\n", "    def __repr__(self):\n", "        attributes = \",\\n  \".join(\n", "            f\"{key} = {value!r}\" for key, value in vars(self).items()\n", "        )\n", "        return f\"ModelInput(\\n  {attributes}\\n)\"\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_argparse.py ✗ | model=starcoder_default, temp=0.2, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34<PERSON><PERSON><PERSON> argparse\n", "\n", "parser = argparse.ArgumentParser()\n", "parser.add_argument(\n", "    \"--model\",\n", "    type=str,\n", "    default=\"my_model\",\n", "    help=\"which model to serve\",\n", ")\n", "parser.add_argument(\n", "    \"--disable_completions\",\n", "    action=\"store_true\",\n", "    \u001b[0m\u001b[40m\u001b[97mhelp=\"disable completions\",\n", ")\n", "parser.add_argument(\n", "    \"--disable_completion_requests\",\n", "    action=\"store_true\",\u001b[0m\u001b[34m\n", "    help=\"disable completion requests\",\n", ")\n", "parser.add_argument(\n", "    \"--single_line_completions\",\n", "    action=\"store_true\",\n", "    help=\"whether to return single-line completions\",\n", ")\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_argparse.py ✗ | model=starcoder_default, temp=0.2, sample=1\n", "----------------------------------------------------------------------------------------\n", "\u001b[34<PERSON><PERSON><PERSON> argparse\n", "\n", "parser = argparse.ArgumentParser()\n", "parser.add_argument(\n", "    \"--model\",\n", "    type=str,\n", "    default=\"my_model\",\n", "    help=\"which model to serve\",\n", ")\n", "parser.add_argument(\n", "    \"--disable_completions\",\n", "    action=\"store_true\",\n", "    \u001b[0m\u001b[40m\u001b[97mhelp=\"disable completions\",\n", ")\n", "parser.add_argument(\n", "    \"--disable_completion_requests\",\n", "    action=\"store_true\",\u001b[0m\u001b[34m\n", "    help=\"disable completion requests\",\n", ")\n", "parser.add_argument(\n", "    \"--single_line_completions\",\n", "    action=\"store_true\",\n", "    help=\"whether to return single-line completions\",\n", ")\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_argparse.py ✗ | model=starcoder_default, temp=0.2, sample=2\n", "----------------------------------------------------------------------------------------\n", "\u001b[34<PERSON><PERSON><PERSON> argparse\n", "\n", "parser = argparse.ArgumentParser()\n", "parser.add_argument(\n", "    \"--model\",\n", "    type=str,\n", "    default=\"my_model\",\n", "    help=\"which model to serve\",\n", ")\n", "parser.add_argument(\n", "    \"--disable_completions\",\n", "    action=\"store_true\",\n", "    \u001b[0m\u001b[40m\u001b[97mhelp=\"disable completions\",\n", ")\n", "parser.add_argument(\n", "    \"--disable_completion_requests\",\n", "    action=\"store_true\",\u001b[0m\u001b[34m\n", "    help=\"disable completion requests\",\n", ")\n", "parser.add_argument(\n", "    \"--single_line_completions\",\n", "    action=\"store_true\",\n", "    help=\"whether to return single-line completions\",\n", ")\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_multi_function_1.py ✗ | model=starcoder_default, temp=0.2, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport copy\n", "from dataclasses import asdict, dataclass\n", "import json\n", "import pathlib\n", "\n", "@dataclass\n", "class MyClass:\n", "    def save_to_json(self, filename):\n", "        \"\"\"Saves the dataclass instance to a JSON file.\"\"\"\n", "        with pathlib.Path(filename).open(\"w\") as f:\n", "            json.dump(asdict(self), f)\n", "\n", "    @classmethod\n", "    def load_from_json(cls, filename):\n", "        \"\"\"Loads a dataclass instance from a JSON file.\"\"\"\n", "        with pathlib.Path(filename).open(\"r\") as f:\n", "            data = json.load(f)\n", "\n", "        return cls(**data)\n", "\n", "    def clone(self):\n", "        \"\"\"Returns a deep copy of the instance.\"\"\"\n", "        \u001b[0m\u001b[40m\u001b[97mreturn copy.deepcopy(self)\n", "\n", "    def __repr__(self):\n", "        attributes = \",\\n  \".join(\n", "            f\"{key} = {value!r}\" for key, value in vars(self).items()\n", "        )\n", "        return f\"MyClass(\\n  {attributes}\\n)\"\n", "\n", "@dataclass\n", "class ModelInput:\n", "    \"\"\"\n", "    A dataclass for storing model input.\n", "    \"\"\"\n", "    # The model's input.\n", "    input: MyClass\n", "\n", "    # The model's output.\n", "    output: MyClass\n", "\n", "    def save_to_json(self, filename):\n", "        \"\"\"Saves the dataclass instance to a JSON file.\"\"\"\n", "        with pathlib.Path(filename).open(\"w\") as f:\n", "            json.dump(asdict(self), f)\n", "\n", "    @classmethod\n", "    def load_from_json(cls, filename):\n", "        \"\"\"Loads a dataclass instance from a JSON file.\"\"\"\n", "        with pathlib.Path(filename).open(\"r\") as f:\n", "            data = json.load(f)\n", "\n", "        return cls(**data)\n", "\n", "    def clone(self):\n", "        \"\"\"Returns a deep copy of the instance.\"\"\"\u001b[0m\u001b[34m\n", "        return copy.deepcopy(self)\n", "\n", "    def __repr__(self):\n", "        attributes = \",\\n  \".join(\n", "            f\"{key} = {value!r}\" for key, value in vars(self).items()\n", "        )\n", "        return f\"ModelInput(\\n  {attributes}\\n)\"\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_multi_function_1.py ✗ | model=starcoder_default, temp=0.2, sample=1\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport copy\n", "from dataclasses import asdict, dataclass\n", "import json\n", "import pathlib\n", "\n", "@dataclass\n", "class MyClass:\n", "    def save_to_json(self, filename):\n", "        \"\"\"Saves the dataclass instance to a JSON file.\"\"\"\n", "        with pathlib.Path(filename).open(\"w\") as f:\n", "            json.dump(asdict(self), f)\n", "\n", "    @classmethod\n", "    def load_from_json(cls, filename):\n", "        \"\"\"Loads a dataclass instance from a JSON file.\"\"\"\n", "        with pathlib.Path(filename).open(\"r\") as f:\n", "            data = json.load(f)\n", "\n", "        return cls(**data)\n", "\n", "    def clone(self):\n", "        \"\"\"Returns a deep copy of the instance.\"\"\"\n", "        \u001b[0m\u001b[40m\u001b[97mreturn copy.deepcopy(self)\n", "\n", "    def __repr__(self):\n", "        attributes = \",\\n  \".join(\n", "            f\"{key} = {value!r}\" for key, value in vars(self).items()\n", "        )\n", "        return f\"MyClass(\\n  {attributes}\\n)\"\n", "\n", "\n", "@dataclass\n", "class ModelInput:\n", "    \"\"\"\n", "    A dataclass to represent the input to a model.\n", "\n", "    This is a simple example of a dataclass.\n", "    \"\"\"\n", "\n", "    # The number of features in the input.\n", "    n_features: int\n", "\n", "    # The number of samples in the input.\n", "    n_samples: int\n", "\n", "    # The number of classes in the output.\n", "    n_classes: int\n", "\n", "    # The number of output dimensions.\n", "    n_outputs: int\n", "\n", "    # The number of output dimensions.\n", "    n_layers: int\n", "\n", "    # The number of neurons in each layer.\n", "    n_neurons: int\n", "\n", "    # The activation function to use in each layer.\n", "    activation: str\n", "\n", "    # The dropout rate to use in each layer.\n", "    dropout: float\n", "\n", "    # The batch size to use in training.\n", "    batch_size: int\n", "\n", "    # The number of epochs to train for.\n", "    epochs: int\n", "\n", "    # The learning rate to use in training.\n", "    learning_rate: float\n", "\n", "    # The optimizer to use in training.\n", "    optimizer: str\n", "\n", "    # The loss function to use in training.\n", "    loss: str\n", "\n", "    # The metric to use in training.\n", "    metric: str\n", "\n", "    # The random seed to use in training.\n", "    random_seed: int\n", "\n", "    # The number of workers to use in training.\n", "    n_workers: int\n", "\n", "    # The number of GPUs to use in training.\n", "    n_gpus: int\n", "\n", "    # The number of CPUs to use in training.\n", "    n_cpus: int\n", "\n", "    # The number of threads to use in training.\n", "    n_threads: int\n", "\n", "    # The number of batches to train for in each epoch.\n", "    batches_per_epoch: int\n", "\n", "    # The number of batches to train for in each epoch.\n", "    batches_per_epoch: int\n", "\n", "    # The number of batches to train for in each epoch.\n", "    batches_per_epoch: int\n", "\n", "    # The number of batches to train for in each epoch.\n", "    batches_per_epoch: int\n", "\n", "    # The number of batches to train for in each epoch.\n", "    batches_per_epoch: int\n", "\n", "    # The number of batches to train for in each epoch.\n", "    batches_per_epoch: int\n", "\n", "    # The number of batches to train for in each epoch.\n", "    batches_per_epoch: int\n", "\n", "    # The number of batches to train for in each epoch.\n", "    batches_per_epoch: int\n", "\n", "    # The number of batches to train for in each epoch.\n", "    batches_per_epoch: int\n", "\n", "    # The number of batches to train for in each epoch.\n", "    batches_per_epoch: int\n", "\n", "    # The number of batches to train for in each epoch.\n", "    batches_per_epoch: int\n", "\n", "    # The number of batches to train for in each epoch.\n", "    batches_per_epoch: int\n", "\n", "    # The number of batches to train for in each epoch.\n", "    batches_per_epoch: int\n", "\n", "    # The number of batches to train for in each epoch.\n", "    batches_per_epoch: int\n", "\n", "    # The number of batches to train for in each epoch.\n", "    batches_per_epoch: int\n", "\n", "    # The number of batches to train for in each epoch.\n", "    batches_per_epoch: int\n", "\n", "    # The number of batches to train for in each epoch.\n", "    batches_per_epoch: int\n", "\n", "    # The number of batches to train for in each epoch.\n", "    batches_per_epoch: int\n", "\n", "    # The number of batches to train for in each epoch.\n", "    batches_per_epoch: int\n", "\n", "    # The number of batches to train for in each epoch.\n", "    batches_per_epoch: int\n", "\n", "    # The number of batches to train for in each epoch.\n", "    batches_per_epoch: int\n", "\n", "    # The number of batches to train for in each epoch.\n", "    batches_per_epoch: int\n", "\n", "    # The number of batches to train for in each epoch.\n", "    batches_per_epoch: int\n", "\n", "    # The number of batches to train for in each epoch.\n", "    batches_per_epoch: int\n", "\n", "    # The number of batches to train for in each epoch.\n", "    batches_per_epoch: int\n", "\n", "    # The number of batches to train for in each epoch.\n", "    batches_per_epoch: int\n", "\n", "    # The number of batches to train for in each epoch.\n", "    batches_per_epoch: int\n", "\n", "    # The number of batches to train for in each epoch.\n", "    batches_per_epoch: int\n", "\n", "    # The number of batches to train for in each epoch.\n", "    batches_per_epoch: int\n", "\n", "    # The number of batches to train for in each epoch.\n", "    batches_per_epoch:\u001b[0m\u001b[34m\n", "        return copy.deepcopy(self)\n", "\n", "    def __repr__(self):\n", "        attributes = \",\\n  \".join(\n", "            f\"{key} = {value!r}\" for key, value in vars(self).items()\n", "        )\n", "        return f\"ModelInput(\\n  {attributes}\\n)\"\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_multi_function_1.py ✗ | model=starcoder_default, temp=0.2, sample=2\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport copy\n", "from dataclasses import asdict, dataclass\n", "import json\n", "import pathlib\n", "\n", "@dataclass\n", "class MyClass:\n", "    def save_to_json(self, filename):\n", "        \"\"\"Saves the dataclass instance to a JSON file.\"\"\"\n", "        with pathlib.Path(filename).open(\"w\") as f:\n", "            json.dump(asdict(self), f)\n", "\n", "    @classmethod\n", "    def load_from_json(cls, filename):\n", "        \"\"\"Loads a dataclass instance from a JSON file.\"\"\"\n", "        with pathlib.Path(filename).open(\"r\") as f:\n", "            data = json.load(f)\n", "\n", "        return cls(**data)\n", "\n", "    def clone(self):\n", "        \"\"\"Returns a deep copy of the instance.\"\"\"\n", "        \u001b[0m\u001b[40m\u001b[97mreturn copy.deepcopy(self)\n", "\n", "    def __repr__(self):\n", "        attributes = \",\\n  \".join(\n", "            f\"{key} = {value!r}\" for key, value in vars(self).items()\n", "        )\n", "        return f\"MyClass(\\n  {attributes}\\n)\"\n", "\n", "@dataclass\n", "class ModelInput:\n", "    \"\"\"Input data for a model.\"\"\"\n", "    x: float\n", "    y: float\n", "\n", "    def save_to_json(self, filename):\n", "        \"\"\"Saves the dataclass instance to a JSON file.\"\"\"\n", "        with pathlib.Path(filename).open(\"w\") as f:\n", "            json.dump(asdict(self), f)\n", "\n", "    @classmethod\n", "    def load_from_json(cls, filename):\n", "        \"\"\"Loads a dataclass instance from a JSON file.\"\"\"\n", "        with pathlib.Path(filename).open(\"r\") as f:\n", "            data = json.load(f)\n", "\n", "        return cls(**data)\n", "\n", "    def clone(self):\n", "        \"\"\"Returns a deep copy of the instance.\"\"\"\u001b[0m\u001b[34m\n", "        return copy.deepcopy(self)\n", "\n", "    def __repr__(self):\n", "        attributes = \",\\n  \".join(\n", "            f\"{key} = {value!r}\" for key, value in vars(self).items()\n", "        )\n", "        return f\"ModelInput(\\n  {attributes}\\n)\"\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_trailing_return copy.py ✓ | model=starcoder_default, temp=0.2, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mdef hello_world():\n", "\u001b[0m\u001b[40m\u001b[97m    \u001b[0m\u001b[34mprint(\"Hello World!\")\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_trailing_return copy.py ✓ | model=starcoder_default, temp=0.2, sample=1\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mdef hello_world():\n", "\u001b[0m\u001b[40m\u001b[97m    \u001b[0m\u001b[34mprint(\"Hello World!\")\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_trailing_return copy.py ✓ | model=starcoder_default, temp=0.2, sample=2\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mdef hello_world():\n", "\u001b[0m\u001b[40m\u001b[97m    \u001b[0m\u001b[34mprint(\"Hello World!\")\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_dataclass_methods.py ✗ | model=starcoder_default, temp=0.2, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mfrom dataclasses import dataclass, field\n", "from typing import *\n", "\n", "@dataclass\n", "class Patch:\n", "    \"\"\"Represents a patch to a file using its character span.\"\"\"\n", "\n", "    file_content: str = \"\"\n", "    \"\"\"The file contents being modified. text being patched. Assumes UTF-8 encoding.\"\"\"\n", "    char_start: int = 0\n", "    \"\"\"Character offset for the patch.\"\"\"\n", "    char_end: int = 0\n", "    \"\"\"character offset for the patch.\"\"\"\n", "    patch_content: str = \"\"\n", "    \"\"\"the patch content.\"\"\"\n", "    patch_id: str = \"\"\n", "\n", "    @classmethod\n", "    def for_span(\n", "        cls,\n", "        file_content: str,\n", "        char_start: int,\n", "        char_end: int,\n", "        \u001b[0m\u001b[40m\u001b[97m**kwargs,\n", "    ):\n", "        \"\"\"create a patch for the region [char_start, char_end) in file contents.\"\"\"\n", "        return cls(\n", "            file_content=file_content,\n", "            char_start=char_start,\n", "            char_end=char_end,\n", "            patch_content=file_content[char_start:char_end],\n", "            **kwargs,\n", "        )\n", "\n", "    @classmethod\n", "    def empty(cls, file_content: str, **kwargs):\n", "        \"\"\"Create an empty Patch for a file.\"\"\"\n", "        return Patch(\n", "            file_content=file_content,\n", "            char_start=0,\n", "            char_end=0,\n", "            patch_content=\"\",\n", "            **kwargs,\n", "        )\n", "<commit_msg>add patch_id to Patch.for_span<commit_after>\"\"\"\n", "<PERSON> represents a patch to a file using its character span.\n", "\"\"\"\n", "\n", "from dataclasses import dataclass, field\n", "from typing import *\n", "\n", "@dataclass\n", "class Patch:\n", "    \"\"\"Represents a patch to a file using its character span.\"\"\"\n", "\n", "    file_content: str = \"\"\n", "    \"\"\"The file contents being modified. text being patched. Assumes UTF-8 encoding.\"\"\"\n", "    char_start: int = 0\n", "    \"\"\"Character offset for the patch.\"\"\"\n", "    char_end: int = 0\n", "    \"\"\"character offset for the patch.\"\"\"\n", "    patch_content: str = \"\"\n", "    \"\"\"the patch content.\"\"\"\n", "    patch_id: str = \"\"\n", "\n", "    @classmethod\n", "    def for_span(\n", "        cls,\n", "        file_content: str,\n", "        char_start: int,\n", "        char_end: int,\n", "        patch_id: str = \"\",\u001b[0m\u001b[34m\n", "        **kwargs,\n", "    ):\n", "        \"\"\"create a patch for the region [char_start, char_end) in file contents.\"\"\"\n", "        return cls(\n", "            file_content=file_content,\n", "            char_start=char_start,\n", "            char_end=char_end,\n", "            patch_content=file_content[char_start:char_end],\n", "            **kwargs,\n", "        )\n", "\n", "    @classmethod\n", "    def empty(cls, file_content: str, **kwargs):\n", "        \"\"\"Create an empty Patch for a file.\"\"\"\n", "        return Patch(\n", "            file_content=file_content,\n", "            char_start=0,\n", "            char_end=0,\n", "            patch_content=\"\",\n", "            **kwargs,\n", "        )\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_dataclass_methods.py ✗ | model=starcoder_default, temp=0.2, sample=1\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mfrom dataclasses import dataclass, field\n", "from typing import *\n", "\n", "@dataclass\n", "class Patch:\n", "    \"\"\"Represents a patch to a file using its character span.\"\"\"\n", "\n", "    file_content: str = \"\"\n", "    \"\"\"The file contents being modified. text being patched. Assumes UTF-8 encoding.\"\"\"\n", "    char_start: int = 0\n", "    \"\"\"Character offset for the patch.\"\"\"\n", "    char_end: int = 0\n", "    \"\"\"character offset for the patch.\"\"\"\n", "    patch_content: str = \"\"\n", "    \"\"\"the patch content.\"\"\"\n", "    patch_id: str = \"\"\n", "\n", "    @classmethod\n", "    def for_span(\n", "        cls,\n", "        file_content: str,\n", "        char_start: int,\n", "        char_end: int,\n", "        \u001b[0m\u001b[40m\u001b[97m**kwargs,\n", "    ):\n", "        \"\"\"create a patch for the region [char_start, char_end) in file contents.\"\"\"\n", "        return cls(\n", "            file_content=file_content,\n", "            char_start=char_start,\n", "            char_end=char_end,\n", "            patch_content=file_content[char_start:char_end],\n", "            **kwargs,\n", "        )\n", "\n", "    @classmethod\n", "    def empty(cls, file_content: str, **kwargs):\n", "        \"\"\"Create an empty Patch for a file.\"\"\"\n", "        return Patch(\n", "            file_content=file_content,\n", "            char_start=0,\n", "            char_end=0,\n", "            patch_content=\"\",\n", "            **kwargs,\n", "        )\n", "<commit_msg>add patch_id to Patch<commit_after>\"\"\"\n", "Classes for representing patches to files.\n", "\"\"\"\n", "\n", "from dataclasses import dataclass, field\n", "from typing import *\n", "\n", "@dataclass\n", "class Patch:\n", "    \"\"\"Represents a patch to a file using its character span.\"\"\"\n", "\n", "    file_content: str = \"\"\n", "    \"\"\"The file contents being modified. text being patched. Assumes UTF-8 encoding.\"\"\"\n", "    char_start: int = 0\n", "    \"\"\"Character offset for the patch.\"\"\"\n", "    char_end: int = 0\n", "    \"\"\"character offset for the patch.\"\"\"\n", "    patch_content: str = \"\"\n", "    \"\"\"the patch content.\"\"\"\n", "    patch_id: str = \"\"\n", "\n", "    @classmethod\n", "    def for_span(\n", "        cls,\n", "        file_content: str,\n", "        char_start: int,\n", "        char_end: int,\u001b[0m\u001b[34m\n", "        **kwargs,\n", "    ):\n", "        \"\"\"create a patch for the region [char_start, char_end) in file contents.\"\"\"\n", "        return cls(\n", "            file_content=file_content,\n", "            char_start=char_start,\n", "            char_end=char_end,\n", "            patch_content=file_content[char_start:char_end],\n", "            **kwargs,\n", "        )\n", "\n", "    @classmethod\n", "    def empty(cls, file_content: str, **kwargs):\n", "        \"\"\"Create an empty Patch for a file.\"\"\"\n", "        return Patch(\n", "            file_content=file_content,\n", "            char_start=0,\n", "            char_end=0,\n", "            patch_content=\"\",\n", "            **kwargs,\n", "        )\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_dataclass_methods.py ✗ | model=starcoder_default, temp=0.2, sample=2\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mfrom dataclasses import dataclass, field\n", "from typing import *\n", "\n", "@dataclass\n", "class Patch:\n", "    \"\"\"Represents a patch to a file using its character span.\"\"\"\n", "\n", "    file_content: str = \"\"\n", "    \"\"\"The file contents being modified. text being patched. Assumes UTF-8 encoding.\"\"\"\n", "    char_start: int = 0\n", "    \"\"\"Character offset for the patch.\"\"\"\n", "    char_end: int = 0\n", "    \"\"\"character offset for the patch.\"\"\"\n", "    patch_content: str = \"\"\n", "    \"\"\"the patch content.\"\"\"\n", "    patch_id: str = \"\"\n", "\n", "    @classmethod\n", "    def for_span(\n", "        cls,\n", "        file_content: str,\n", "        char_start: int,\n", "        char_end: int,\n", "        \u001b[0m\u001b[40m\u001b[97m**kwargs,\n", "    ):\n", "        \"\"\"create a patch for the region [char_start, char_end) in file contents.\"\"\"\n", "        return cls(\n", "            file_content=file_content,\n", "            char_start=char_start,\n", "            char_end=char_end,\n", "            patch_content=file_content[char_start:char_end],\n", "            **kwargs,\n", "        )\n", "\n", "    @classmethod\n", "    def empty(cls, file_content: str, **kwargs):\n", "        \"\"\"Create an empty Patch for a file.\"\"\"\n", "        return Patch(\n", "            file_content=file_content,\n", "            char_start=0,\n", "            char_end=0,\n", "            patch_content=\"\",\n", "            **kwargs,\n", "        )\n", "<commit_msg>add patch_id to Patch.for_span<commit_after>from dataclasses import dataclass, field\n", "from typing import *\n", "\n", "@dataclass\n", "class Patch:\n", "    \"\"\"Represents a patch to a file using its character span.\"\"\"\n", "\n", "    file_content: str = \"\"\n", "    \"\"\"The file contents being modified. text being patched. Assumes UTF-8 encoding.\"\"\"\n", "    char_start: int = 0\n", "    \"\"\"Character offset for the patch.\"\"\"\n", "    char_end: int = 0\n", "    \"\"\"character offset for the patch.\"\"\"\n", "    patch_content: str = \"\"\n", "    \"\"\"the patch content.\"\"\"\n", "    patch_id: str = \"\"\n", "\n", "    @classmethod\n", "    def for_span(\n", "        cls,\n", "        file_content: str,\n", "        char_start: int,\n", "        char_end: int,\n", "        patch_id: str,\u001b[0m\u001b[34m\n", "        **kwargs,\n", "    ):\n", "        \"\"\"create a patch for the region [char_start, char_end) in file contents.\"\"\"\n", "        return cls(\n", "            file_content=file_content,\n", "            char_start=char_start,\n", "            char_end=char_end,\n", "            patch_content=file_content[char_start:char_end],\n", "            **kwargs,\n", "        )\n", "\n", "    @classmethod\n", "    def empty(cls, file_content: str, **kwargs):\n", "        \"\"\"Create an empty Patch for a file.\"\"\"\n", "        return Patch(\n", "            file_content=file_content,\n", "            char_start=0,\n", "            char_end=0,\n", "            patch_content=\"\",\n", "            **kwargs,\n", "        )\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_dataclass.py ✗ | model=starcoder_default, temp=0.2, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mfrom dataclasses import dataclass, field\n", "from typing import *\n", "\n", "@dataclass\n", "class Datum:\n", "    \"\"\"Represents a blob of data.\"\"\"\n", "\n", "    source: str = \"\"\n", "    \"\"\"Where the data comes from.\"\"\"\n", "    char_start: int = 0\n", "    \"\"\"Character offset for the blob.\"\"\"\n", "    char_end: int = 0\n", "    \"\"\"Character offset for the blob.\"\"\"\n", "    _extra: Mapping = field(default_factory=dict, repr=True, init=True\u001b[0m\u001b[40m\u001b[97m, compare=False\u001b[0m\u001b[34m)\n", "    \"\"\"A handy field to store extra data.\"\"\"\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_dataclass.py ✗ | model=starcoder_default, temp=0.2, sample=1\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mfrom dataclasses import dataclass, field\n", "from typing import *\n", "\n", "@dataclass\n", "class Datum:\n", "    \"\"\"Represents a blob of data.\"\"\"\n", "\n", "    source: str = \"\"\n", "    \"\"\"Where the data comes from.\"\"\"\n", "    char_start: int = 0\n", "    \"\"\"Character offset for the blob.\"\"\"\n", "    char_end: int = 0\n", "    \"\"\"Character offset for the blob.\"\"\"\n", "    _extra: Mapping = field(default_factory=dict, repr=True, init=True\u001b[0m\u001b[40m\u001b[97m, compare=False\u001b[0m\u001b[34m)\n", "    \"\"\"A handy field to store extra data.\"\"\"\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_dataclass.py ✓ | model=starcoder_default, temp=0.2, sample=2\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mfrom dataclasses import dataclass, field\n", "from typing import *\n", "\n", "@dataclass\n", "class Datum:\n", "    \"\"\"Represents a blob of data.\"\"\"\n", "\n", "    source: str = \"\"\n", "    \"\"\"Where the data comes from.\"\"\"\n", "    char_start: int = 0\n", "    \"\"\"Character offset for the blob.\"\"\"\n", "    char_end: int = 0\n", "    \"\"\"Character offset for the blob.\"\"\"\n", "    _extra: Mapping = field(default_factory=dict, repr=True, init=True\u001b[0m\u001b[40m\u001b[97m\u001b[0m\u001b[34m)\n", "    \"\"\"A handy field to store extra data.\"\"\"\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_trailing_return.py ✗ | model=starcoder_default, temp=0.2, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mdef get_odd_ints(lower, upper):\n", "\u001b[0m\u001b[40m\u001b[97m    results = []\n", "    for i in range(lower, upper + 1):\n", "        if i % 2 == 1:\n", "            results.append(i)\u001b[0m\u001b[34m\n", "    return results\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_trailing_return.py ✗ | model=starcoder_default, temp=0.2, sample=1\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mdef get_odd_ints(lower, upper):\n", "\u001b[0m\u001b[40m\u001b[97m    results = []\n", "    for i in range(lower, upper + 1):\n", "        if i % 2 == 1:\n", "            results.append(i)\u001b[0m\u001b[34m\n", "    return results\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_trailing_return.py ✗ | model=starcoder_default, temp=0.2, sample=2\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mdef get_odd_ints(lower, upper):\n", "\u001b[0m\u001b[40m\u001b[97m    results = []\n", "    for i in range(lower, upper + 1):\n", "        if i % 2 == 1:\n", "            results.append(i)\u001b[0m\u001b[34m\n", "    return results\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n"]}], "source": ["# FIM-behavior\n", "run_all_tests(model, \"starcoder_default\", FIM_TESTS, save_results=False, n_samples=3, temperature=0.2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Checkpoint starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d\n", "\n", "* [Determined](https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/det/experiments/9678/checkpoints?row=c8db6db8-5e7b-4800-8da1-31b495166e3e)\n", "* [WANDB](https://wandb.ai/augment/starcoder/runs/3uf0j1ma)\n", "* Trained on ~250k lines of Python.\n"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["========================================================================================\n", "MODEL: starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d\n", "----------------------------------------------------------------------------------------\n", "========================================================================================\n", "EXAMPLE: structure_2.cpp ✗ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.2, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "#include <vector>\n", "\n", "struct Person {\n", "    char* name;\n", "    int age;\n", "    std::vector<Person*> parents;\n", "};\n", "\n", "void get_ancestors\u001b[0m\u001b[40m\u001b[97m(Person* person, std::vector<Person*>& ancestors) {\n", "    for (Person* parent : person->parents) {\n", "        ancestors.push_back(parent);\n", "        get_ancestors(parent, ancestors);\n", "    }\n", "}\u001b[0m\u001b[34m\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_2.cpp ✗ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.2, sample=1\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "#include <vector>\n", "\n", "struct Person {\n", "    char* name;\n", "    int age;\n", "    std::vector<Person*> parents;\n", "};\n", "\n", "void get_ancestors\u001b[0m\u001b[40m\u001b[97m(Person* person, std::vector<Person*>& ancestors) {\n", "    for (Person* parent : person->parents) {\n", "        ancestors.push_back(parent);\n", "        get_ancestors(parent, ancestors);\n", "    }\n", "}\u001b[0m\u001b[34m\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_2.cpp ✗ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.2, sample=2\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "#include <vector>\n", "\n", "struct Person {\n", "    char* name;\n", "    int age;\n", "    std::vector<Person*> parents;\n", "};\n", "\n", "void get_ancestors\u001b[0m\u001b[40m\u001b[97m(Person* person, std::vector<Person*>& ancestors) {\n", "    for (Person* parent : person->parents) {\n", "        get_ancestors(parent, ancestors);\n", "    }\n", "    ancestors.push_back(person);\n", "}\u001b[0m\u001b[34m\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_4.cpp ✗ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.2, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "#include <ctime>\n", "#include <iostream>\n", "#include <string>\n", "\n", "struct Person {\n", "    std::string name;\n", "    std::string profession;\n", "    std::tm dob;\n", "};\n", "\n", "std::ostream& operator<<(std::ostream& os, const Person& person) {\n", "    os << person.name << \u001b[0m\u001b[40m\u001b[97m\" is a \" << person.profession\u001b[0m\u001b[34m << \"and was born on \" << std::asctime(&person.dob);\n", "    return os;\n", "}\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_4.cpp ✗ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.2, sample=1\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "#include <ctime>\n", "#include <iostream>\n", "#include <string>\n", "\n", "struct Person {\n", "    std::string name;\n", "    std::string profession;\n", "    std::tm dob;\n", "};\n", "\n", "std::ostream& operator<<(std::ostream& os, const Person& person) {\n", "    os << person.name << \u001b[0m\u001b[40m\u001b[97m\" is a \" << person.profession\u001b[0m\u001b[34m << \"and was born on \" << std::asctime(&person.dob);\n", "    return os;\n", "}\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_4.cpp ✗ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.2, sample=2\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "#include <ctime>\n", "#include <iostream>\n", "#include <string>\n", "\n", "struct Person {\n", "    std::string name;\n", "    std::string profession;\n", "    std::tm dob;\n", "};\n", "\n", "std::ostream& operator<<(std::ostream& os, const Person& person) {\n", "    os << person.name << \u001b[0m\u001b[40m\u001b[97m\" is a \" << person.profession\u001b[0m\u001b[34m << \"and was born on \" << std::asctime(&person.dob);\n", "    return os;\n", "}\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: double_free.cpp ✗ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.2, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "int main() {\n", "    int *arr = new int[10];\n", "    // Fill arr with increasing numbers\n", "    for (int i = 0; i < 10; i++) arr[i] = i;\n", "    delete arr;\n", "    // Fill arr with decreasing numbers\n", "\u001b[0m\u001b[40m\u001b[97m    for (int i = 0; i < 10; i++) arr[i] = 9 - i;\n", "    delete arr;\n", "    // Fill arr with random numbers\n", "    for (int i = 0; i < 10; i++) arr[i] = rand() % 10;\n", "    delete arr;\n", "   \u001b[0m\u001b[34m}\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: double_free.cpp ✗ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.2, sample=1\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "int main() {\n", "    int *arr = new int[10];\n", "    // Fill arr with increasing numbers\n", "    for (int i = 0; i < 10; i++) arr[i] = i;\n", "    delete arr;\n", "    // Fill arr with decreasing numbers\n", "\u001b[0m\u001b[40m\u001b[97m    for (int i = 0; i < 10; i++) arr[i] = 9 - i;\n", "    delete arr;\n", "    // Fill arr with random numbers\n", "    for (int i = 0; i < 10; i++) arr[i] = rand() % 10;\n", "    delete arr;\n", "   \u001b[0m\u001b[34m}\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: double_free.cpp ✗ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.2, sample=2\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "int main() {\n", "    int *arr = new int[10];\n", "    // Fill arr with increasing numbers\n", "    for (int i = 0; i < 10; i++) arr[i] = i;\n", "    delete arr;\n", "    // Fill arr with decreasing numbers\n", "\u001b[0m\u001b[40m\u001b[97m    for (int i = 9; i >= 0; i--) arr[i] = i;\n", "    delete arr;\n", "    // Fill arr with random numbers\n", "    for (int i = 0; i < 10; i++) arr[i] = rand() % 100;\n", "    delete arr;\n", "    return 0\u001b[0m\u001b[34m}\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_1.cpp ✓ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.2, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "#include <vector>\n", "\n", "struct Person {\n", "    char* name;\n", "    int age;\n", "\u001b[0m\u001b[40m\u001b[97m    std::vector<Person*> parents;\n", "\u001b[0m\u001b[34m};\n", "\n", "void get_ancestors(const Person& person, std::vector<const Person*> acc) {\n", "    acc.push_back(&person);\n", "    for (const auto* parent : person.parents) {\n", "        get_ancestors(*parent, acc);\n", "    }\n", "}\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_1.cpp ✓ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.2, sample=1\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "#include <vector>\n", "\n", "struct Person {\n", "    char* name;\n", "    int age;\n", "\u001b[0m\u001b[40m\u001b[97m    std::vector<Person*> parents;\n", "\u001b[0m\u001b[34m};\n", "\n", "void get_ancestors(const Person& person, std::vector<const Person*> acc) {\n", "    acc.push_back(&person);\n", "    for (const auto* parent : person.parents) {\n", "        get_ancestors(*parent, acc);\n", "    }\n", "}\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_1.cpp ✗ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.2, sample=2\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "#include <vector>\n", "\n", "struct Person {\n", "    char* name;\n", "    int age;\n", "\u001b[0m\u001b[40m\u001b[97m    std::vector<const Person*> parents;\n", "\u001b[0m\u001b[34m};\n", "\n", "void get_ancestors(const Person& person, std::vector<const Person*> acc) {\n", "    acc.push_back(&person);\n", "    for (const auto* parent : person.parents) {\n", "        get_ancestors(*parent, acc);\n", "    }\n", "}\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_5.cpp ✗ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.2, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "#include <ctime>\n", "#include <iostream>\n", "#include <string>\n", "\n", "struct Person {\n", "    std::string name;\n", "    std::string profession;\n", "    std::tm dob;\n", "};\n", "\n", "std::ostream& operator<<(std::ostream& os, const Person& person) {\n", "\u001b[0m\u001b[40m\u001b[97m    os << person.name << \", \" << person.profession << \", \" << person.dob.tm_mday << \"/\" << person.dob.tm_mon << \"/\" << person.dob.tm_year + 1900;\n", "    return os;\n", "\u001b[0m\u001b[34m}\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_5.cpp ✗ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.2, sample=1\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "#include <ctime>\n", "#include <iostream>\n", "#include <string>\n", "\n", "struct Person {\n", "    std::string name;\n", "    std::string profession;\n", "    std::tm dob;\n", "};\n", "\n", "std::ostream& operator<<(std::ostream& os, const Person& person) {\n", "\u001b[0m\u001b[40m\u001b[97m    os << person.name << \", \" << person.profession << \", \" << std::put_time(&person.dob, \"%Y-%m-%d\") << '\\n';\n", "    return os;\n", "\u001b[0m\u001b[34m}\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_5.cpp ✗ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.2, sample=2\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "#include <ctime>\n", "#include <iostream>\n", "#include <string>\n", "\n", "struct Person {\n", "    std::string name;\n", "    std::string profession;\n", "    std::tm dob;\n", "};\n", "\n", "std::ostream& operator<<(std::ostream& os, const Person& person) {\n", "\u001b[0m\u001b[40m\u001b[97m    os << \"Name: \" << person.name << \", Profession: \" << person.profession\n", "       << \", Date of birth: \" << std::put_time(&person.dob, \"%d %B %Y\");\n", "    return os;\n", "}\n", "\n", "int main() {\n", "    std::vector<Person> people = {\n", "        {\"<PERSON>\", \"<PERSON>\", {1980, 1, 1}},\n", "        {\"<PERSON>\", \"<PERSON>\", {1978, 12, 1}},\n", "        {\"<PERSON>\", \"<PERSON>\u001b[0m\u001b[34m}\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_3.cpp ✗ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.2, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "#include <ctime>\n", "#include <iostream>\n", "#include <string>\n", "#include <vector>\n", "\n", "struct Person {\n", "    std::string name;\n", "    std::tm dob;\n", "    std::vector<Person*> parents;\n", "};\n", "\n", "int main() {\n", "    Person alice = {\n", "        \"Alice\",\n", "        {tm_mday: 1, tm_mon: 4, tm_year: 89},\n", "        {}};\n", "    int age = \u001b[0m\u001b[40m\u001b[97m2019 - alice.dob.tm_year;\u001b[0m\u001b[34m\n", "    std::cout << \"Alice is \" << age << \" years old\" << std::endl;\n", "\n", "    return 0;\n", "}\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_3.cpp ✗ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.2, sample=1\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "#include <ctime>\n", "#include <iostream>\n", "#include <string>\n", "#include <vector>\n", "\n", "struct Person {\n", "    std::string name;\n", "    std::tm dob;\n", "    std::vector<Person*> parents;\n", "};\n", "\n", "int main() {\n", "    Person alice = {\n", "        \"Alice\",\n", "        {tm_mday: 1, tm_mon: 4, tm_year: 89},\n", "        {}};\n", "    int age = \u001b[0m\u001b[40m\u001b[97mstd::time(0) - std::mktime(&alice.dob);\u001b[0m\u001b[34m\n", "    std::cout << \"Alice is \" << age << \" years old\" << std::endl;\n", "\n", "    return 0;\n", "}\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_3.cpp ✗ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.2, sample=2\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "#include <ctime>\n", "#include <iostream>\n", "#include <string>\n", "#include <vector>\n", "\n", "struct Person {\n", "    std::string name;\n", "    std::tm dob;\n", "    std::vector<Person*> parents;\n", "};\n", "\n", "int main() {\n", "    Person alice = {\n", "        \"Alice\",\n", "        {tm_mday: 1, tm_mon: 4, tm_year: 89},\n", "        {}};\n", "    int age = \u001b[0m\u001b[40m\u001b[97m2019 - alice.dob.tm_year + 5 * (alice.dob.tm_mon - 1);\u001b[0m\u001b[34m\n", "    std::cout << \"Alice is \" << age << \" years old\" << std::endl;\n", "\n", "    return 0;\n", "}\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_1.go ✓ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.2, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mpackage main\n", "\n", "type Person struct {\n", "\tname    string\n", "\tage     int\n", "\t\u001b[0m\u001b[40m\u001b[97mparents []*Person\u001b[0m\u001b[34m\n", "}\n", "\n", "func get_ancestors(person *Person, acc []*Person) []*Person {\n", "\tfor _, parent := range person.parents {\n", "\t\tacc = append(acc, parent)\n", "\t\tacc = get_ancestors(parent, acc)\n", "\t}\n", "\treturn acc\n", "}\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_1.go ✓ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.2, sample=1\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mpackage main\n", "\n", "type Person struct {\n", "\tname    string\n", "\tage     int\n", "\t\u001b[0m\u001b[40m\u001b[97mparents []*Person\u001b[0m\u001b[34m\n", "}\n", "\n", "func get_ancestors(person *Person, acc []*Person) []*Person {\n", "\tfor _, parent := range person.parents {\n", "\t\tacc = append(acc, parent)\n", "\t\tacc = get_ancestors(parent, acc)\n", "\t}\n", "\treturn acc\n", "}\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_1.go ✓ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.2, sample=2\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mpackage main\n", "\n", "type Person struct {\n", "\tname    string\n", "\tage     int\n", "\t\u001b[0m\u001b[40m\u001b[97mparents []*Person\u001b[0m\u001b[34m\n", "}\n", "\n", "func get_ancestors(person *Person, acc []*Person) []*Person {\n", "\tfor _, parent := range person.parents {\n", "\t\tacc = append(acc, parent)\n", "\t\tacc = get_ancestors(parent, acc)\n", "\t}\n", "\treturn acc\n", "}\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_2.go ✗ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.2, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mpackage main\n", "\n", "type Person struct {\n", "\tname    string\n", "\tage     int\n", "\tparents []*Person\n", "}\n", "\n", "func get_ancestors\u001b[0m\u001b[40m\u001b[97m(person *Person) []string {\n", "\tvar ancestors []string\n", "\tfor _, parent := range person.parents {\n", "\t\tancestors = append(ancestors, parent.name)\n", "\t\tancestors = append(ancestors, get_ancestors(parent)...)\n", "\t}\n", "\treturn ancestors\n", "}\n", "\n", "func main() {\n", "\t// Create a family tree\n", "\tjim := Person{name: \"<PERSON>\", age: 50}\n", "\tsally := Person{name: \"<PERSON>\", age: 40}\n", "\tjim.parents = append(jim.parents, &sally)\n", "\n", "\tsue := Person{name: \"<PERSON>\", age: 45}\n", "\tjim.parents = append(jim.parents, &sue)\n", "\n", "\t// Print the ancestors\u001b[0m\u001b[34m\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_2.go ✗ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.2, sample=1\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mpackage main\n", "\n", "type Person struct {\n", "\tname    string\n", "\tage     int\n", "\tparents []*Person\n", "}\n", "\n", "func get_ancestors\u001b[0m\u001b[40m\u001b[97m(person *Person) []string {\n", "\tvar ancestors []string\n", "\tfor _, parent := range person.parents {\n", "\t\tancestors = append(ancestors, parent.name)\n", "\t\tancestors = append(ancestors, get_ancestors(parent)...)\n", "\t}\n", "\treturn ancestors\n", "}\n", "\n", "func main() {\n", "\t// create family tree\n", "\tf := Person{name: \"<PERSON>\", age: 40}\n", "\te := Person{name: \"<PERSON>\", age: 30, parents: []*Person{&f}}\n", "\td := Person{name: \"<PERSON>\", age: 20, parents: []*Person{&f, &e}}\n", "\tc := Person{name: \"<PERSON>\", age: 10, parents: []*Person{&f, &e}}\n", "\t\u001b[0m\u001b[34m\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_2.go ✗ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.2, sample=2\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mpackage main\n", "\n", "type Person struct {\n", "\tname    string\n", "\tage     int\n", "\tparents []*Person\n", "}\n", "\n", "func get_ancestors\u001b[0m\u001b[40m\u001b[97m(person *Person) []string {\n", "\tvar ancestors []string\n", "\tfor _, parent := range person.parents {\n", "\t\tancestors = append(ancestors, parent.name)\n", "\t\tancestors = append(ancestors, get_ancestors(parent)...)\n", "\t}\n", "\treturn ancestors\n", "}\n", "\n", "func main() {\n", "\t// Create a family tree\n", "\tf := Person{\n", "\t\tname: \"Father\",\n", "\t\tage:  70,\n", "\t\tparents: []*Person{\n", "\t\t\t&Person{\n", "\t\t\t\tname: \"<PERSON>\",\n", "\t\t\t\tage:  40,\n", "\t\t\t},\n", "\t\t\t&Person{\n", "\t\t\t\tname: \"<PERSON>\",\n", "\t\t\t\tage:  60,\n", "\t\t\t},\n", "\t\t},\n", "\t}\n", "\n", "\tm := Person{\n", "\t\tname: \"<PERSON>\",\n", "\t\tage\u001b[0m\u001b[34m\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_3.go ✗ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.2, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mpackage main\n", "\n", "type Person struct {\n", "\tname    string\n", "\tage     int\n", "\tparents []*Person\n", "}\n", "\n", "func get_ancestors(person *Person, acc []*Person) []*Person {\n", "\u001b[0m\u001b[40m\u001b[97m\tif person.parents == nil {\n", "\t\treturn acc\n", "\t}\n", "\n", "\tfor _, parent := range person.parents {\n", "\t\tacc = append(acc, parent)\n", "\t\tacc = get_ancestors(parent, acc)\n", "\t}\n", "\n", "\treturn acc\n", "\u001b[0m\u001b[34m}\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_3.go ✗ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.2, sample=1\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mpackage main\n", "\n", "type Person struct {\n", "\tname    string\n", "\tage     int\n", "\tparents []*Person\n", "}\n", "\n", "func get_ancestors(person *Person, acc []*Person) []*Person {\n", "\u001b[0m\u001b[40m\u001b[97m\tif person == nil {\n", "\t\treturn acc\n", "\t}\n", "\n", "\tacc = append(acc, person)\n", "\n", "\tfor _, parent := range person.parents {\n", "\t\tacc = get_ancestors(parent, acc)\n", "\t}\n", "\n", "\treturn acc\n", "\u001b[0m\u001b[34m}\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_3.go ✗ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.2, sample=2\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mpackage main\n", "\n", "type Person struct {\n", "\tname    string\n", "\tage     int\n", "\tparents []*Person\n", "}\n", "\n", "func get_ancestors(person *Person, acc []*Person) []*Person {\n", "\u001b[0m\u001b[40m\u001b[97m\tif person.parents == nil {\n", "\t\treturn acc\n", "\t}\n", "\n", "\tfor _, parent := range person.parents {\n", "\t\tacc = get_ancestors(parent, acc)\n", "\t}\n", "\n", "\treturn append(acc, person)\n", "\u001b[0m\u001b[34m}\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_4.go ✗ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.2, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mpackage main\n", "\n", "import \"sort\"\n", "\n", "type Person struct {\n", "\tname    string\n", "\tage     int\n", "}\n", "\n", "func main() {\n", "\tpeople := []Person{\n", "\t\t\tPerson{\"alice\", 60},\n", "\t\t\tPerson{\"bob\", 50},\n", "\t\t\tPerson{\"charlie\", 20},\n", "\t\t}\n", "\t// sort by age\n", "\u001b[0m\u001b[40m\u001b[97m\tsort.Slice(people, func(i, j int) bool {\n", "\t\treturn people[i].age < people[j].age\n", "\t})\n", "\t// sort by name\n", "\tsort.Slice(people, func(i, j int) bool {\n", "\t\treturn people[i].name < people[j].name\n", "\t})\n", "\t// sort by name and age\n", "\tsort.Slice(people, func(i, j int) bool {\n", "\t\tif\u001b[0m\u001b[34m}\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_4.go ✗ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.2, sample=1\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mpackage main\n", "\n", "import \"sort\"\n", "\n", "type Person struct {\n", "\tname    string\n", "\tage     int\n", "}\n", "\n", "func main() {\n", "\tpeople := []Person{\n", "\t\t\tPerson{\"alice\", 60},\n", "\t\t\tPerson{\"bob\", 50},\n", "\t\t\tPerson{\"charlie\", 20},\n", "\t\t}\n", "\t// sort by age\n", "\u001b[0m\u001b[40m\u001b[97m\tsort.Slice(people, func(i, j int) bool {\n", "\t\treturn people[i].age < people[j].age\n", "\t})\n", "\t// sort by name\n", "\tsort.Slice(people, func(i, j int) bool {\n", "\t\treturn people[i].name < people[j].name\n", "\t})\n", "\t// sort by name\n", "\tsort.Slice(people, func(i, j int) bool {\n", "\t\treturn people[\u001b[0m\u001b[34m}\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_4.go ✗ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.2, sample=2\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mpackage main\n", "\n", "import \"sort\"\n", "\n", "type Person struct {\n", "\tname    string\n", "\tage     int\n", "}\n", "\n", "func main() {\n", "\tpeople := []Person{\n", "\t\t\tPerson{\"alice\", 60},\n", "\t\t\tPerson{\"bob\", 50},\n", "\t\t\tPerson{\"charlie\", 20},\n", "\t\t}\n", "\t// sort by age\n", "\u001b[0m\u001b[40m\u001b[97m\tsort.Slice(people, func(i, j int) bool {\n", "\t\treturn people[i].age < people[j].age\n", "\t})\n", "\t// sort by name\n", "\tsort.Slice(people, func(i, j int) bool {\n", "\t\treturn people[i].name < people[j].name\n", "\t})\n", "\t// sort by age and name\n", "\tsort.Slice(people, func(i, j int) bool {\n", "\t\tif\u001b[0m\u001b[34m}\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: chunk_request.py ✗ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.2, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport math\n", "\n", "\n", "def make_chunked_requests(payload, chunk_size):\n", "    \"\"\"Chunk a payload into `chunk_size` portions and make requests for them.\n", "\n", "    Args:\n", "        payload: the payload to chunk and send.\n", "\n", "    Returns:\n", "        A list of requests; each request is a dictionary with keys:\n", "            chunk_idx: the index of the current chunk.\n", "            chunk_count: how many chunks are required in total.\n", "            chunk_payload: the chunk payload.\n", "    \"\"\"\n", "    requests = []\n", "    chunk_count = math.ceil(len(payload) / chunk_size)\n", "\u001b[0m\u001b[40m\u001b[97m    for chunk_idx in range(chunk_count):\n", "        chunk_payload = payload[chunk_idx * chunk_size : (chunk_idx + 1) * chunk_size]\n", "        requests.append(\n", "            {\n", "                \"chunk_idx\": chunk_idx,\n", "                \"chunk_count\": chunk_count,\n", "                \"chunk_payload\": chunk_payload,\n", "            }\n", "        )\u001b[0m\u001b[34m    return requests\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: chunk_request.py ✗ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.2, sample=1\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport math\n", "\n", "\n", "def make_chunked_requests(payload, chunk_size):\n", "    \"\"\"Chunk a payload into `chunk_size` portions and make requests for them.\n", "\n", "    Args:\n", "        payload: the payload to chunk and send.\n", "\n", "    Returns:\n", "        A list of requests; each request is a dictionary with keys:\n", "            chunk_idx: the index of the current chunk.\n", "            chunk_count: how many chunks are required in total.\n", "            chunk_payload: the chunk payload.\n", "    \"\"\"\n", "    requests = []\n", "    chunk_count = math.ceil(len(payload) / chunk_size)\n", "\u001b[0m\u001b[40m\u001b[97m    for chunk_idx in range(chunk_count):\n", "        chunk_payload = payload[chunk_idx * chunk_size : (chunk_idx + 1) * chunk_size]\n", "        requests.append(\n", "            {\n", "                \"chunk_idx\": chunk_idx,\n", "                \"chunk_count\": chunk_count,\n", "                \"chunk_payload\": chunk_payload,\n", "            }\n", "        )\u001b[0m\u001b[34m    return requests\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: chunk_request.py ✗ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.2, sample=2\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport math\n", "\n", "\n", "def make_chunked_requests(payload, chunk_size):\n", "    \"\"\"Chunk a payload into `chunk_size` portions and make requests for them.\n", "\n", "    Args:\n", "        payload: the payload to chunk and send.\n", "\n", "    Returns:\n", "        A list of requests; each request is a dictionary with keys:\n", "            chunk_idx: the index of the current chunk.\n", "            chunk_count: how many chunks are required in total.\n", "            chunk_payload: the chunk payload.\n", "    \"\"\"\n", "    requests = []\n", "    chunk_count = math.ceil(len(payload) / chunk_size)\n", "\u001b[0m\u001b[40m\u001b[97m    for chunk_idx in range(chunk_count):\n", "        chunk_payload = payload[chunk_idx * chunk_size : (chunk_idx + 1) * chunk_size]\n", "        requests.append(\n", "            {\n", "                \"chunk_idx\": chunk_idx,\n", "                \"chunk_count\": chunk_count,\n", "                \"chunk_payload\": chunk_payload,\n", "            }\n", "        )\u001b[0m\u001b[34m    return requests\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: infer_preconditions.py ✗ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.2, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mdef remove_duplicates(sorted_lst):\n", "    \"\"\"Remove duplicates in-place from a sorted list.\"\"\"\n", "    if not sorted_lst:\n", "        return\n", "    write_idx = 0\n", "    for i in range(1, len(sorted_lst)):\n", "        if sorted_lst[i] != sorted_lst[write_idx]:\n", "            write_idx += 1\n", "            sorted_lst[write_idx] = sorted_lst[i]\n", "    del sorted_lst[write_idx+1:]\n", "\n", "\n", "lst = [1, 4, 2, 6, 1, 3, 4]\n", "# Print the list of unique integers in lst.\n", "\u001b[0m\u001b[40m\u001b[97mprint(list(set(lst)))\n", "\n", "# Print the list of unique integers in lst, sorted.\n", "print(list(set(lst)))\n", "remove_duplicates(lst)\n", "print(lst)\n", "\u001b[0m\u001b[34m\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: infer_preconditions.py ✗ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.2, sample=1\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mdef remove_duplicates(sorted_lst):\n", "    \"\"\"Remove duplicates in-place from a sorted list.\"\"\"\n", "    if not sorted_lst:\n", "        return\n", "    write_idx = 0\n", "    for i in range(1, len(sorted_lst)):\n", "        if sorted_lst[i] != sorted_lst[write_idx]:\n", "            write_idx += 1\n", "            sorted_lst[write_idx] = sorted_lst[i]\n", "    del sorted_lst[write_idx+1:]\n", "\n", "\n", "lst = [1, 4, 2, 6, 1, 3, 4]\n", "# Print the list of unique integers in lst.\n", "\u001b[0m\u001b[40m\u001b[97mprint(lst)\n", "remove_duplicates(lst)\n", "print(lst)\n", "\u001b[0m\u001b[34m\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: infer_preconditions.py ✗ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.2, sample=2\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mdef remove_duplicates(sorted_lst):\n", "    \"\"\"Remove duplicates in-place from a sorted list.\"\"\"\n", "    if not sorted_lst:\n", "        return\n", "    write_idx = 0\n", "    for i in range(1, len(sorted_lst)):\n", "        if sorted_lst[i] != sorted_lst[write_idx]:\n", "            write_idx += 1\n", "            sorted_lst[write_idx] = sorted_lst[i]\n", "    del sorted_lst[write_idx+1:]\n", "\n", "\n", "lst = [1, 4, 2, 6, 1, 3, 4]\n", "# Print the list of unique integers in lst.\n", "\u001b[0m\u001b[40m\u001b[97mprint(lst)\n", "remove_duplicates(lst)\n", "print(lst)\n", "\u001b[0m\u001b[34m\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_4.py ✗ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.2, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport dataclasses\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    age: int\n", "    profession: str\n", "\n", "\n", "persons = [\n", "    Person('<PERSON>', 42, '<PERSON>'),\n", "    Person('<PERSON>', 22, '<PERSON>er'),\n", "    Person('<PERSON>', 12, ''),\n", "    Person('<PERSON>', 18, 'Entrepreneur'),\n", "]\n", "\n", "# Sort persons by age\n", "\u001b[0m\u001b[40m\u001b[97mpersons.sort(key=lambda p: p.age)\n", "\n", "# Print persons\n", "for person in persons:\n", "    print(person)\n", "\u001b[0m\u001b[34m\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_4.py ✗ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.2, sample=1\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport dataclasses\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    age: int\n", "    profession: str\n", "\n", "\n", "persons = [\n", "    Person('<PERSON>', 42, '<PERSON>'),\n", "    Person('<PERSON>', 22, '<PERSON>er'),\n", "    Person('<PERSON>', 12, ''),\n", "    Person('<PERSON>', 18, 'Entrepreneur'),\n", "]\n", "\n", "# Sort persons by age\n", "\u001b[0m\u001b[40m\u001b[97mpersons.sort(key=lambda person: person.age)\n", "\n", "# Print persons\n", "for person in persons:\n", "    print(person)\u001b[0m\u001b[34m\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_4.py ✗ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.2, sample=2\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport dataclasses\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    age: int\n", "    profession: str\n", "\n", "\n", "persons = [\n", "    Person('<PERSON>', 42, '<PERSON>'),\n", "    Person('<PERSON>', 22, '<PERSON>er'),\n", "    Person('<PERSON>', 12, ''),\n", "    Person('<PERSON>', 18, 'Entrepreneur'),\n", "]\n", "\n", "# Sort persons by age\n", "\u001b[0m\u001b[40m\u001b[97mpersons.sort(key=lambda p: p.age)\n", "\n", "# Print persons\n", "for person in persons:\n", "    print(person)\n", "\u001b[0m\u001b[34m\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_6.py ✓ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.2, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport dataclasses\n", "import datetime\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    dob: datetime.date\n", "    profession: str\n", "\n", "    def __str__(self):\n", "        return f\"{self.name} \u001b[0m\u001b[40m\u001b[97mis a {self.profession}\u001b[0m\u001b[34m and is {self.dob.year} years old\"\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_6.py ✓ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.2, sample=1\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport dataclasses\n", "import datetime\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    dob: datetime.date\n", "    profession: str\n", "\n", "    def __str__(self):\n", "        return f\"{self.name} \u001b[0m\u001b[40m\u001b[97mis a {self.profession}\u001b[0m\u001b[34m and is {self.dob.year} years old\"\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_6.py ✓ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.2, sample=2\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport dataclasses\n", "import datetime\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    dob: datetime.date\n", "    profession: str\n", "\n", "    def __str__(self):\n", "        return f\"{self.name} \u001b[0m\u001b[40m\u001b[97mis a {self.profession}\u001b[0m\u001b[34m and is {self.dob.year} years old\"\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_2.py ✗ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.2, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "import dataclasses\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    age: int\n", "    parents: list['Person']\n", "\n", "\n", "def get_ancestors(person: Person):\n", "    ancestors = []\n", "\u001b[0m\u001b[40m\u001b[97m\n", "#     for parent in person.parents:\n", "#         ancestors.extend(get_ancestors(parent))\n", "\n", "    ancestors.extend(get_ancestors(person.parents[0]))\n", "\n", "    return ancestors\n", "\n", "\n", "if __name__ == '__main__':\n", "    john = Person('<PERSON>', 30, [Person('<PERSON>', 35, []), Person('<PERSON>', 33, [])])\n", "    jack = Person('<PERSON>', 27, [Person('<PERSON>', 35, []), Person('<PERSON>', 33, [])])\n", "\n", "    print(get_ancestors(john))\n", "    print(get\u001b[0m\u001b[34m\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_2.py ✗ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.2, sample=1\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "import dataclasses\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    age: int\n", "    parents: list['Person']\n", "\n", "\n", "def get_ancestors(person: Person):\n", "    ancestors = []\n", "\u001b[0m\u001b[40m\u001b[97m\n", "#     for parent in person.parents:\n", "#         ancestors.append(parent)\n", "#         ancestors.extend(get_ancestors(parent))\n", "\n", "    ancestors.extend(person.parents)\n", "    for parent in person.parents:\n", "        ancestors.extend(get_ancestors(parent))\n", "\n", "    return ancestors\n", "\n", "\n", "if __name__ == '__main__':\n", "    john = Person('<PERSON>', 30, [\n", "        Person('<PERSON>', 35, []),\n", "        Person('<PERSON>', 25, [])\n", "    ])\n", "    jack = Person('<PERSON>', 20, [\n", "        Person('<PERSON>',\u001b[0m\u001b[34m\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_2.py ✗ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.2, sample=2\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "import dataclasses\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    age: int\n", "    parents: list['Person']\n", "\n", "\n", "def get_ancestors(person: Person):\n", "    ancestors = []\n", "\u001b[0m\u001b[40m\u001b[97m\n", "#     for parent in person.parents:\n", "#         ancestors.extend(get_ancestors(parent))\n", "\n", "#     ancestors.append(person)\n", "\n", "#     return ancestors\n", "\n", "    return [\n", "        *get_ancestors(parent)\n", "        for parent in person.parents\n", "    ] + [person]\n", "\n", "\n", "if __name__ == '__main__':\n", "    john = Person('<PERSON>', 30, [\n", "        Person('<PERSON>', 25, []),\n", "        Person('<PERSON>', 30, []),\n", "    ])\n", "\n", "    jack = Person('<PERSON>', 20, [\n", "        Person('<PERSON>', 2\u001b[0m\u001b[34m\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_3.py ✗ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.2, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport dataclasses\n", "import datetime\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    dob: datetime.date\n", "    profession: str\n", "\n", "\n", "alice = Person('<PERSON>', datetime.date.fromisoformat('1989-04-01'), 'Engineer')\n", "now = datetime.date.today()\n", "\u001b[0m\u001b[40m\u001b[97mage = now.year - alice.dob.year - ((now.month, now.day) < (alice.dob.month, alice.dob.day))\n", "\u001b[0m\u001b[34mprint(f\"<PERSON> is {age} years old!\")\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_3.py ✗ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.2, sample=1\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport dataclasses\n", "import datetime\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    dob: datetime.date\n", "    profession: str\n", "\n", "\n", "alice = Person('<PERSON>', datetime.date.fromisoformat('1989-04-01'), 'Engineer')\n", "now = datetime.date.today()\n", "\u001b[0m\u001b[40m\u001b[97mage = now.year - alice.dob.year - ((now.month, now.day) < (alice.dob.month, alice.dob.day))\n", "\u001b[0m\u001b[34mprint(f\"<PERSON> is {age} years old!\")\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_3.py ✗ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.2, sample=2\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport dataclasses\n", "import datetime\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    dob: datetime.date\n", "    profession: str\n", "\n", "\n", "alice = Person('<PERSON>', datetime.date.fromisoformat('1989-04-01'), 'Engineer')\n", "now = datetime.date.today()\n", "\u001b[0m\u001b[40m\u001b[97mage = now.year - alice.dob.year - ((now.month, now.day) < (alice.dob.month, alice.dob.day))\n", "\u001b[0m\u001b[34mprint(f\"<PERSON> is {age} years old!\")\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_1.py ✗ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.2, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport dataclasses\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    age: int\n", "\u001b[0m\u001b[40m\u001b[97m    parents: List['Person'] = dataclasses.field(default_factory=list)\u001b[0m\u001b[34m\n", "\n", "def get_ancestors(person: Person):\n", "    ret = []\n", "    for parent in person.parents:\n", "        ret.append(parent)\n", "        ret.extend(get_ancestors(parent))\n", "    return ret\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_1.py ✗ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.2, sample=1\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport dataclasses\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    age: int\n", "\u001b[0m\u001b[40m\u001b[97m    parents: List['Person'] = dataclasses.field(default_factory=list)\u001b[0m\u001b[34m\n", "\n", "def get_ancestors(person: Person):\n", "    ret = []\n", "    for parent in person.parents:\n", "        ret.append(parent)\n", "        ret.extend(get_ancestors(parent))\n", "    return ret\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_1.py ✗ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.2, sample=2\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport dataclasses\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    age: int\n", "\u001b[0m\u001b[40m\u001b[97m    parents: List['Person'] = dataclasses.field(default_factory=list)\u001b[0m\u001b[34m\n", "\n", "def get_ancestors(person: Person):\n", "    ret = []\n", "    for parent in person.parents:\n", "        ret.append(parent)\n", "        ret.extend(get_ancestors(parent))\n", "    return ret\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: testing_1.py ✗ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.2, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "import dataclasses\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    age: int\n", "    profession: str\n", "\n", "\n", "def sort_by_age(people):\n", "    people.sort(key=lambda x: x.age)\n", "\n", "\n", "def test_sort_by_age():\n", "    p1 = Person('<PERSON>', 42, '<PERSON>')\n", "    p2 = Person('<PERSON>', 22, 'Marketer')\n", "    p3 = Person('<PERSON>', 12, '')\n", "\u001b[0m\u001b[40m\u001b[97m\n", "if __name__ == '__main__':\n", "    test_sort_by_age()\u001b[0m\u001b[34m\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: testing_1.py ✗ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.2, sample=1\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "import dataclasses\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    age: int\n", "    profession: str\n", "\n", "\n", "def sort_by_age(people):\n", "    people.sort(key=lambda x: x.age)\n", "\n", "\n", "def test_sort_by_age():\n", "    p1 = Person('<PERSON>', 42, '<PERSON>')\n", "    p2 = Person('<PERSON>', 22, 'Marketer')\n", "    p3 = Person('<PERSON>', 12, '')\n", "\u001b[0m\u001b[40m\u001b[97m\n", "#    sort_by_age([p1, p2, p3])\n", "\n", "    assert p1.age < p2.age < p3.age\u001b[0m\u001b[34m\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: testing_1.py ✗ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.2, sample=2\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "import dataclasses\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    age: int\n", "    profession: str\n", "\n", "\n", "def sort_by_age(people):\n", "    people.sort(key=lambda x: x.age)\n", "\n", "\n", "def test_sort_by_age():\n", "    p1 = Person('<PERSON>', 42, '<PERSON>')\n", "    p2 = Person('<PERSON>', 22, 'Marketer')\n", "    p3 = Person('<PERSON>', 12, '')\n", "\u001b[0m\u001b[40m\u001b[97m\n", "if __name__ == '__main__':\n", "    test_sort_by_age()\u001b[0m\u001b[34m\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: testing_2.py ✗ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.2, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "import dataclasses\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    age: int\n", "    profession: str\n", "\n", "\n", "def sort_by_age(people):\n", "    people.sort(key=lambda x: x.age)\n", "\n", "\n", "def test_sort_by_age():\n", "\u001b[0m\u001b[40m\u001b[97m    p1 = Person('<PERSON>', 30,'student')\n", "    p2 = Person('<PERSON>', 20, 'teacher')\n", "    p3 = Person('<PERSON>', 18, 'artist')\u001b[0m\u001b[34m    people = [p1, p2, p3]\n", "    sort_by_age(people)\n", "    assert people == [p3, p1, p2]\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: testing_2.py ✗ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.2, sample=1\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "import dataclasses\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    age: int\n", "    profession: str\n", "\n", "\n", "def sort_by_age(people):\n", "    people.sort(key=lambda x: x.age)\n", "\n", "\n", "def test_sort_by_age():\n", "\u001b[0m\u001b[40m\u001b[97m    p1 = Person('<PERSON>', 20,'student')\n", "    p2 = Person('<PERSON>', 21, 'teacher')\n", "    p3 = Person('<PERSON>', 19, 'driver')\u001b[0m\u001b[34m    people = [p1, p2, p3]\n", "    sort_by_age(people)\n", "    assert people == [p3, p1, p2]\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: testing_2.py ✗ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.2, sample=2\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "import dataclasses\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    age: int\n", "    profession: str\n", "\n", "\n", "def sort_by_age(people):\n", "    people.sort(key=lambda x: x.age)\n", "\n", "\n", "def test_sort_by_age():\n", "\u001b[0m\u001b[40m\u001b[97m    p1 = Person('<PERSON>', 25, 'Engineer')\n", "    p2 = Person('<PERSON>', 30, '<PERSON>')\n", "    p3 = Person('<PERSON>', 18, 'Engineer')\u001b[0m\u001b[34m    people = [p1, p2, p3]\n", "    sort_by_age(people)\n", "    assert people == [p3, p1, p2]\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: remove_duplicates.py ✗ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.2, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "def remove_duplicates(sorted_lst):\n", "    \"\"\"Remove duplicates in-place from a sorted list.\"\"\"\n", "    if not sorted_lst:\n", "        return\n", "\u001b[0m\u001b[40m\u001b[97m#     print(\"Before: \", sorted_lst)\n", "    write = 0\n", "    for read in range(1, len(sorted_lst)):\n", "        if sorted_lst[read]!= sorted_lst[write]:\n", "            write += 1\n", "            if read!= write:\n", "                sorted_lst[write] = sorted_lst[read]\n", "    write += 1\n", "    if write < len(sorted_lst):\n", "        del sorted_lst[write+1:]\n", "#     print(\"After: \", sorted_lst)\n", "\n", "def get_all_files(path):\n", "    \"\"\"Get all files in a directory.\"\"\"\n", "    files = []\n", "    for (dirpath, dirnames, filenames) in os.walk(path):\n", "        files.extend([os.path.join(dirpath, file) for file in filenames])\n", "    return files\n", "\n", "def get_all_dirs(path):\n", "    \"\"\"Get all directories in a directory.\"\"\"\n", "    files = []\n", "    for (dirpath, dirnames, filenames) in os.walk(path):\n", "        files.extend\u001b[0m\u001b[34m\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: remove_duplicates.py ✗ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.2, sample=1\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "def remove_duplicates(sorted_lst):\n", "    \"\"\"Remove duplicates in-place from a sorted list.\"\"\"\n", "    if not sorted_lst:\n", "        return\n", "\u001b[0m\u001b[40m\u001b[97m#     print(sorted_lst)\n", "    for i in range(len(sorted_lst)-1, 0, -1):\n", "        if sorted_lst[i] == sorted_lst[i-1]:\n", "            del sorted_lst[i]\n", "\n", "def remove_duplicates_v2(sorted_lst):\n", "    \"\"\"Remove duplicates in-place from a sorted list.\"\"\"\n", "    if not sorted_lst:\n", "        return\n", "    for i in range(len(sorted_lst)-1, 0, -1):\n", "        if sorted_lst[i] == sorted_lst[i-1]:\n", "            sorted_lst.pop(i)\n", "\n", "def remove_duplicates_v3(sorted_lst):\n", "    \"\"\"Remove duplicates in-place from a sorted list.\"\"\"\n", "    if not sorted_lst:\n", "        return\n", "    for i in range(len(sorted_lst)-1, 0, -1):\n", "        if sorted_lst[i] == sorted_lst[i-1]:\n", "            sorted_lst.pop(i)\n", "            i -= \u001b[0m\u001b[34m\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: remove_duplicates.py ✗ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.2, sample=2\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "def remove_duplicates(sorted_lst):\n", "    \"\"\"Remove duplicates in-place from a sorted list.\"\"\"\n", "    if not sorted_lst:\n", "        return\n", "\u001b[0m\u001b[40m\u001b[97m#     print(\"sorted_lst\", sorted_lst)\n", "    for i in range(len(sorted_lst)-1, 0, -1):\n", "        if sorted_lst[i] == sorted_lst[i-1]:\n", "            del sorted_lst[i]\n", "#     print(\"deduplicated\", sorted_lst)\n", "\n", "def get_all_files(path):\n", "    \"\"\"\n", "    Get all files in a directory.\n", "\n", "    Args:\n", "        path: directory path\n", "\n", "    Returns:\n", "        list of file paths\n", "    \"\"\"\n", "    files = []\n", "    for root, _, fnames in os.walk(path):\n", "        for fname in fnames:\n", "            files.append(os.path.join(root, fname))\n", "    return files\n", "\n", "def get_all_dirs(path):\n", "    \"\"\"\n", "    Get all directories in a directory.\n", "\n", "    Args:\n", "        path: directory path\n", "\n", "    Returns:\n", "        list of directory paths\n", "    \"\"\"\n", "    dirs = []\n", "    for root, _, _ in os.walk(path):\n", "        dirs.append(root)\n", "    return dirs\n", "\n", "\u001b[0m\u001b[34m\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_5.py ✗ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.2, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport dataclasses\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    age: int\n", "\u001b[0m\u001b[40m\u001b[97m\n", "    def __repr__(self):\n", "        return f\"{self.__class__.__name__}({self.name!r}, {self.age!r})\"\u001b[0m\u001b[34m\n", "def get_ancestors(person: Person):\n", "    ret = []\n", "    for parent\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_5.py ✗ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.2, sample=1\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport dataclasses\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    age: int\n", "\u001b[0m\u001b[40m\u001b[97m\n", "    def __post_init__(self):\n", "        print(f\"__post_init__ {self}\")\u001b[0m\u001b[34m\n", "def get_ancestors(person: Person):\n", "    ret = []\n", "    for parent\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_5.py ✗ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.2, sample=2\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport dataclasses\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    age: int\n", "\u001b[0m\u001b[40m\u001b[97m\n", "    def __post_init__(self):\n", "        print(f\"__post_init__ {self}\")\u001b[0m\u001b[34m\n", "def get_ancestors(person: Person):\n", "    ret = []\n", "    for parent\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: resources_1.py ✗ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.2, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "def get_line_at_pct(fname, pct):\n", "    file = open(fname)\n", "    lines = sum(1 for line in file)\n", "    file.close()\n", "\n", "    line_no = int(pct * lines)\n", "\u001b[0m\u001b[40m\u001b[97m\n", "#    print(line_no)\n", "\n", "    file = open(fname)\n", "    for line_no in range(line_no):\n", "        file.readline()\n", "    line = file.readline()\n", "    file.close()\n", "\n", "    return line\n", "\n", "def get_pct_at_line(fname, line_no):\n", "    file = open(fname)\n", "    lines = sum(1 for line in file)\n", "    file.close()\n", "\n", "    pct = line_no / lines\n", "\n", "    return pct\n", "\n", "def get_pct_\u001b[0m\u001b[34m\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: resources_1.py ✗ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.2, sample=1\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "def get_line_at_pct(fname, pct):\n", "    file = open(fname)\n", "    lines = sum(1 for line in file)\n", "    file.close()\n", "\n", "    line_no = int(pct * lines)\n", "\u001b[0m\u001b[40m\u001b[97m\n", "def get_line_at_pct_2(fname, pct):\n", "    file = open(fname)\n", "    lines = sum(1 for line in file)\n", "    file.close()\n", "\n", "    line_no = int(pct * lines)\n", "\n", "    file = open(fname)\n", "    for line_no in range(line_no):\n", "        file.readline()\n", "    line = file.readline()\n", "    file.close()\n", "\n", "    return line\n", "\n", "def get_line_at_pct_3(fname, pct):\u001b[0m\u001b[34m\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: resources_1.py ✗ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.2, sample=2\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "def get_line_at_pct(fname, pct):\n", "    file = open(fname)\n", "    lines = sum(1 for line in file)\n", "    file.close()\n", "\n", "    line_no = int(pct * lines)\n", "\u001b[0m\u001b[40m\u001b[97m\n", "def get_line_at_pct_2(fname, pct):\n", "    file = open(fname)\n", "    lines = sum(1 for line in file)\n", "    file.close()\n", "\n", "    line_no = int(pct * lines)\n", "    file = open(fname)\n", "    for i in range(line_no):\n", "        file.readline()\n", "    line = file.readline()\n", "    file.close()\n", "    return line\n", "\n", "def get_line_at_pct_3(fname, pct):\n", "    file\u001b[0m\u001b[34m\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_7.py ✗ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.2, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport dataclasses\n", "import datetime\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    dob: datetime.date\n", "    profession: str\n", "\n", "    def __str__(self):\n", "\u001b[0m\u001b[40m\u001b[97m\treturn f\"{self.name} ({self.dob.year}) is a {self.profession}\"\n", "\n", "if __name__ == \"__main__\":\n", "    p = Person(\"<PERSON>\", datetime.date(1980, 1, 1), \"plumber\")\n", "    print(p)\n", "\u001b[0m\u001b[34m\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_7.py ✗ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.2, sample=1\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport dataclasses\n", "import datetime\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    dob: datetime.date\n", "    profession: str\n", "\n", "    def __str__(self):\n", "\u001b[0m\u001b[40m\u001b[97m\treturn f\"{self.name} ({self.dob.year})\"\n", "\n", "@dataclasses.dataclass\n", "class Company:\n", "    name: str\n", "    founding_year: int\n", "    employees: list[Person]\n", "\n", "    def __str__(self):\n", "\treturn f\"{self.name} ({self.founding_year})\"\n", "\n", "@dataclasses.dataclass\n", "class Unicorn:\n", "    name: str\u001b[0m\u001b[34m\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_7.py ✗ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.2, sample=2\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport dataclasses\n", "import datetime\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    dob: datetime.date\n", "    profession: str\n", "\n", "    def __str__(self):\n", "\u001b[0m\u001b[40m\u001b[97m\treturn f\"{self.name} ({self.dob.year}) is a {self.profession}\"\n", "\n", "if __name__ == \"__main__\":\n", "    p = Person(name=\"<PERSON>\", dob=datetime.date(1980, 1, 1), profession=\"plumber\")\n", "    print(p)\n", "\u001b[0m\u001b[34m\u001b[0m\n", "========================================================================================\n", "\n", "\n"]}], "source": ["# FIM behavior\n", "run_all_tests(model_custom, \"starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d\", FIM_TESTS, n_samples=3, temperature=0.2)"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["========================================================================================\n", "MODEL: starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d\n", "----------------------------------------------------------------------------------------\n", "========================================================================================\n", "EXAMPLE: structure_2.cpp ✗ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.0, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "#include <vector>\n", "\n", "struct Person {\n", "    char* name;\n", "    int age;\n", "    std::vector<Person*> parents;\n", "};\n", "\n", "void get_ancestors\u001b[0m\u001b[40m\u001b[97m(Person* person, std::vector<Person*>& ancestors) {\n", "    for (Person* parent : person->parents) {\n", "        ancestors.push_back(parent);\n", "        get_ancestors(parent, ancestors);\n", "    }\n", "}\u001b[0m\u001b[34m\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_4.cpp ✗ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.0, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "#include <ctime>\n", "#include <iostream>\n", "#include <string>\n", "\n", "struct Person {\n", "    std::string name;\n", "    std::string profession;\n", "    std::tm dob;\n", "};\n", "\n", "std::ostream& operator<<(std::ostream& os, const Person& person) {\n", "    os << person.name << \u001b[0m\u001b[40m\u001b[97m\" is a \" << person.profession\u001b[0m\u001b[34m << \"and was born on \" << std::asctime(&person.dob);\n", "    return os;\n", "}\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: double_free.cpp ✗ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.0, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "int main() {\n", "    int *arr = new int[10];\n", "    // Fill arr with increasing numbers\n", "    for (int i = 0; i < 10; i++) arr[i] = i;\n", "    delete arr;\n", "    // Fill arr with decreasing numbers\n", "\u001b[0m\u001b[40m\u001b[97m    for (int i = 0; i < 10; i++) arr[i] = 9 - i;\n", "    delete arr;\n", "    // Fill arr with random numbers\n", "    for (int i = 0; i < 10; i++) arr[i] = rand() % 10;\n", "    delete arr;\n", "   \u001b[0m\u001b[34m}\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_1.cpp ✓ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.0, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "#include <vector>\n", "\n", "struct Person {\n", "    char* name;\n", "    int age;\n", "\u001b[0m\u001b[40m\u001b[97m    std::vector<Person*> parents;\n", "\u001b[0m\u001b[34m};\n", "\n", "void get_ancestors(const Person& person, std::vector<const Person*> acc) {\n", "    acc.push_back(&person);\n", "    for (const auto* parent : person.parents) {\n", "        get_ancestors(*parent, acc);\n", "    }\n", "}\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_5.cpp ✗ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.0, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "#include <ctime>\n", "#include <iostream>\n", "#include <string>\n", "\n", "struct Person {\n", "    std::string name;\n", "    std::string profession;\n", "    std::tm dob;\n", "};\n", "\n", "std::ostream& operator<<(std::ostream& os, const Person& person) {\n", "\u001b[0m\u001b[40m\u001b[97m    os << \"Name: \" << person.name << \", profession: \" << person.profession\n", "       << \", date of birth: \" << std::put_time(&person.dob, \"%Y-%m-%d\");\n", "    return os;\n", "\u001b[0m\u001b[34m}\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_3.cpp ✗ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.0, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "#include <ctime>\n", "#include <iostream>\n", "#include <string>\n", "#include <vector>\n", "\n", "struct Person {\n", "    std::string name;\n", "    std::tm dob;\n", "    std::vector<Person*> parents;\n", "};\n", "\n", "int main() {\n", "    Person alice = {\n", "        \"Alice\",\n", "        {tm_mday: 1, tm_mon: 4, tm_year: 89},\n", "        {}};\n", "    int age = \u001b[0m\u001b[40m\u001b[97mstd::time(0) - std::mktime(&alice.dob);\u001b[0m\u001b[34m\n", "    std::cout << \"Alice is \" << age << \" years old\" << std::endl;\n", "\n", "    return 0;\n", "}\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_1.go ✓ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.0, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mpackage main\n", "\n", "type Person struct {\n", "\tname    string\n", "\tage     int\n", "\t\u001b[0m\u001b[40m\u001b[97mparents []*Person\u001b[0m\u001b[34m\n", "}\n", "\n", "func get_ancestors(person *Person, acc []*Person) []*Person {\n", "\tfor _, parent := range person.parents {\n", "\t\tacc = append(acc, parent)\n", "\t\tacc = get_ancestors(parent, acc)\n", "\t}\n", "\treturn acc\n", "}\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_2.go ✗ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.0, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mpackage main\n", "\n", "type Person struct {\n", "\tname    string\n", "\tage     int\n", "\tparents []*Person\n", "}\n", "\n", "func get_ancestors\u001b[0m\u001b[40m\u001b[97m(person *Person) (ancestors []*Person) {\n", "\tfor _, parent := range person.parents {\n", "\t\tancestors = append(ancestors, parent)\n", "\t\tancestors = append(ancestors, get_ancestors(parent)...)\n", "\t}\n", "\treturn ancestors\n", "}\n", "\n", "func main() {\n", "\t// Make a family tree\n", "\tf := Person{\n", "\t\tname: \"Father\",\n", "\t\tage:  100,\n", "\t}\n", "\tm := Person{\n", "\t\tname: \"<PERSON>\",\n", "\t\tage:  99,\n", "\t}\n", "\tb := Person{\n", "\t\tname: \"<PERSON>\",\n", "\t\tage:  0,\n", "\t}\n", "\tf.parents = append(f.parents, &m)\n", "\tm.parents = append(m.parents, &f\u001b[0m\u001b[34m\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_3.go ✗ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.0, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mpackage main\n", "\n", "type Person struct {\n", "\tname    string\n", "\tage     int\n", "\tparents []*Person\n", "}\n", "\n", "func get_ancestors(person *Person, acc []*Person) []*Person {\n", "\u001b[0m\u001b[40m\u001b[97m\tif person == nil {\n", "\t\treturn acc\n", "\t}\n", "\n", "\tacc = append(acc, person)\n", "\n", "\tfor _, parent := range person.parents {\n", "\t\tacc = get_ancestors(parent, acc)\n", "\t}\n", "\n", "\treturn acc\n", "\u001b[0m\u001b[34m}\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_4.go ✗ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.0, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mpackage main\n", "\n", "import \"sort\"\n", "\n", "type Person struct {\n", "\tname    string\n", "\tage     int\n", "}\n", "\n", "func main() {\n", "\tpeople := []Person{\n", "\t\t\tPerson{\"alice\", 60},\n", "\t\t\tPerson{\"bob\", 50},\n", "\t\t\tPerson{\"charlie\", 20},\n", "\t\t}\n", "\t// sort by age\n", "\u001b[0m\u001b[40m\u001b[97m\tsort.Slice(people, func(i, j int) bool {\n", "\t\treturn people[i].age < people[j].age\n", "\t})\n", "\t// sort by name\n", "\tsort.Slice(people, func(i, j int) bool {\n", "\t\treturn people[i].name < people[j].name\n", "\t})\n", "\t// sort by age and name\n", "\tsort.Slice(people, func(i, j int) bool {\n", "\t\tif\u001b[0m\u001b[34m}\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: chunk_request.py ✗ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.0, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport math\n", "\n", "\n", "def make_chunked_requests(payload, chunk_size):\n", "    \"\"\"Chunk a payload into `chunk_size` portions and make requests for them.\n", "\n", "    Args:\n", "        payload: the payload to chunk and send.\n", "\n", "    Returns:\n", "        A list of requests; each request is a dictionary with keys:\n", "            chunk_idx: the index of the current chunk.\n", "            chunk_count: how many chunks are required in total.\n", "            chunk_payload: the chunk payload.\n", "    \"\"\"\n", "    requests = []\n", "    chunk_count = math.ceil(len(payload) / chunk_size)\n", "\u001b[0m\u001b[40m\u001b[97m    for chunk_idx in range(chunk_count):\n", "        chunk_payload = payload[chunk_idx * chunk_size : (chunk_idx + 1) * chunk_size]\n", "        requests.append(\n", "            {\n", "                \"chunk_idx\": chunk_idx,\n", "                \"chunk_count\": chunk_count,\n", "                \"chunk_payload\": chunk_payload,\n", "            }\n", "        )\u001b[0m\u001b[34m    return requests\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: infer_preconditions.py ✗ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.0, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mdef remove_duplicates(sorted_lst):\n", "    \"\"\"Remove duplicates in-place from a sorted list.\"\"\"\n", "    if not sorted_lst:\n", "        return\n", "    write_idx = 0\n", "    for i in range(1, len(sorted_lst)):\n", "        if sorted_lst[i] != sorted_lst[write_idx]:\n", "            write_idx += 1\n", "            sorted_lst[write_idx] = sorted_lst[i]\n", "    del sorted_lst[write_idx+1:]\n", "\n", "\n", "lst = [1, 4, 2, 6, 1, 3, 4]\n", "# Print the list of unique integers in lst.\n", "\u001b[0m\u001b[40m\u001b[97mprint(lst)\n", "remove_duplicates(lst)\n", "print(lst)\n", "\u001b[0m\u001b[34m\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_4.py ✗ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.0, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport dataclasses\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    age: int\n", "    profession: str\n", "\n", "\n", "persons = [\n", "    Person('<PERSON>', 42, '<PERSON>'),\n", "    Person('<PERSON>', 22, '<PERSON>er'),\n", "    Person('<PERSON>', 12, ''),\n", "    Person('<PERSON>', 18, 'Entrepreneur'),\n", "]\n", "\n", "# Sort persons by age\n", "\u001b[0m\u001b[40m\u001b[97mpersons.sort(key=lambda person: person.age)\n", "\n", "# Print persons\n", "for person in persons:\n", "    print(person.name, person.age\u001b[0m\u001b[34m\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_6.py ✓ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.0, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport dataclasses\n", "import datetime\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    dob: datetime.date\n", "    profession: str\n", "\n", "    def __str__(self):\n", "        return f\"{self.name} \u001b[0m\u001b[40m\u001b[97mis a {self.profession}\u001b[0m\u001b[34m and is {self.dob.year} years old\"\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_2.py ✗ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.0, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "import dataclasses\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    age: int\n", "    parents: list['Person']\n", "\n", "\n", "def get_ancestors(person: Person):\n", "    ancestors = []\n", "\u001b[0m\u001b[40m\u001b[97m\n", "#     for parent in person.parents:\n", "#         ancestors.append(parent)\n", "#         ancestors.extend(get_ancestors(parent))\n", "\n", "    ancestors.extend(get_ancestors(parent) for parent in person.parents)\n", "    ancestors.append(person)\n", "\n", "    return ancestors\n", "\n", "if __name__ == '__main__':\n", "    john = Person('<PERSON>', 30, [\n", "        Person('<PERSON>', 25, []),\n", "        Person('<PERSON>', 20, [])\n", "    ])\n", "    jack = Person('<PERSON>', 25, [\n", "        Person('<PERSON>', 25\u001b[0m\u001b[34m\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_3.py ✗ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.0, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport dataclasses\n", "import datetime\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    dob: datetime.date\n", "    profession: str\n", "\n", "\n", "alice = Person('<PERSON>', datetime.date.fromisoformat('1989-04-01'), 'Engineer')\n", "now = datetime.date.today()\n", "\u001b[0m\u001b[40m\u001b[97mage = now.year - alice.dob.year - ((now.month, now.day) < (alice.dob.month, alice.dob.day))\n", "\u001b[0m\u001b[34mprint(f\"<PERSON> is {age} years old!\")\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_1.py ✗ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.0, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport dataclasses\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    age: int\n", "\u001b[0m\u001b[40m\u001b[97m    parents: List['Person'] = dataclasses.field(default_factory=list)\u001b[0m\u001b[34m\n", "\n", "def get_ancestors(person: Person):\n", "    ret = []\n", "    for parent in person.parents:\n", "        ret.append(parent)\n", "        ret.extend(get_ancestors(parent))\n", "    return ret\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: testing_1.py ✗ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.0, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "import dataclasses\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    age: int\n", "    profession: str\n", "\n", "\n", "def sort_by_age(people):\n", "    people.sort(key=lambda x: x.age)\n", "\n", "\n", "def test_sort_by_age():\n", "    p1 = Person('<PERSON>', 42, '<PERSON>')\n", "    p2 = Person('<PERSON>', 22, 'Marketer')\n", "    p3 = Person('<PERSON>', 12, '')\n", "\u001b[0m\u001b[40m\u001b[97m\n", "#    sort_by_age([p1, p2, p3])\n", "\n", "    assert [p1, p2, p3] == [Person('<PERSON>', 22, '<PERSON>er'), Person('<PERSON>', 42, 'Engineer'), Person('<PERSON>', 12, '')]\u001b[0m\u001b[34m\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: testing_2.py ✗ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.0, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "import dataclasses\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    age: int\n", "    profession: str\n", "\n", "\n", "def sort_by_age(people):\n", "    people.sort(key=lambda x: x.age)\n", "\n", "\n", "def test_sort_by_age():\n", "\u001b[0m\u001b[40m\u001b[97m    p1 = Person('<PERSON>', 30,'student')\n", "    p2 = Person('<PERSON>', 20, 'teacher')\n", "    p3 = Person('<PERSON>', 18, 'artist')\u001b[0m\u001b[34m    people = [p1, p2, p3]\n", "    sort_by_age(people)\n", "    assert people == [p3, p1, p2]\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: remove_duplicates.py ✗ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.0, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "def remove_duplicates(sorted_lst):\n", "    \"\"\"Remove duplicates in-place from a sorted list.\"\"\"\n", "    if not sorted_lst:\n", "        return\n", "\u001b[0m\u001b[40m\u001b[97m#     print(sorted_lst)\n", "    for i in range(len(sorted_lst)-1, 0, -1):\n", "        if sorted_lst[i] == sorted_lst[i-1]:\n", "            del sorted_lst[i]\n", "\n", "def remove_duplicates_v2(sorted_lst):\n", "    \"\"\"Remove duplicates in-place from a sorted list.\"\"\"\n", "    if not sorted_lst:\n", "        return\n", "    for i in range(len(sorted_lst)-1, 0, -1):\n", "        if sorted_lst[i] == sorted_lst[i-1]:\n", "            sorted_lst.pop(i)\n", "\n", "def remove_duplicates_v3(sorted_lst):\n", "    \"\"\"Remove duplicates in-place from a sorted list.\"\"\"\n", "    if not sorted_lst:\n", "        return\n", "    for i in range(len(sorted_lst)-1, 0, -1):\n", "        if sorted_lst[i] == sorted_lst[i-1]:\n", "            sorted_lst.pop(i)\n", "            i -= \u001b[0m\u001b[34m\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_5.py ✗ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.0, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport dataclasses\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    age: int\n", "\u001b[0m\u001b[40m\u001b[97m\n", "    def __post_init__(self):\n", "        print(f\"__post_init__ {self}\")\u001b[0m\u001b[34m\n", "def get_ancestors(person: Person):\n", "    ret = []\n", "    for parent\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: resources_1.py ✗ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.0, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "def get_line_at_pct(fname, pct):\n", "    file = open(fname)\n", "    lines = sum(1 for line in file)\n", "    file.close()\n", "\n", "    line_no = int(pct * lines)\n", "\u001b[0m\u001b[40m\u001b[97m\n", "def get_line_at_pct_in_file(fname, pct):\n", "    file = open(fname)\n", "    lines = sum(1 for line in file)\n", "    file.close()\n", "\n", "    line_no = int(pct * lines)\n", "    print(\"Lines in file: %s\" % lines)\n", "    print(\"Getting line: %s\" % line_no)\n", "\n", "    file = open(fname)\n", "    for line in file:\n", "        if line_no == 0:\n", "            return line\n", "        line_\u001b[0m\u001b[34m\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_7.py ✗ | model=starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d, temp=0.0, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport dataclasses\n", "import datetime\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    dob: datetime.date\n", "    profession: str\n", "\n", "    def __str__(self):\n", "\u001b[0m\u001b[40m\u001b[97m\treturn f\"{self.name} ({self.dob.year}) is a {self.profession}\"\n", "\n", "if __name__ == \"__main__\":\n", "    p = Person(\"<PERSON>\", datetime.date(1980, 1, 1), \"plumber\")\n", "    print(p)\n", "\u001b[0m\u001b[34m\u001b[0m\n", "========================================================================================\n", "\n", "\n"]}], "source": ["# General behavior\n", "run_all_tests(model_custom, \"starcoder_0654eb2a-4422-4bd1-9e2b-876dc9f8918d\", VULCAN_TESTS, temperature=0.0)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Checkpoint arun/codegen-2b-fim-baseline"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model = get_model(\"codegen-2b-fim\")\n", "model.load()"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["========================================================================================\n", "MODEL: codegen-2b-fim-baseline\n", "----------------------------------------------------------------------------------------\n", "========================================================================================\n", "EXAMPLE: fim_leading_spaces.py ✓ | model=codegen-2b-fim-baseline, temp=0.2, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mdef hello_world():\n", "\u001b[0m\u001b[40m\u001b[97m    \u001b[0m\u001b[34mprint(\"Hello World!\")\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_leading_spaces.py ✓ | model=codegen-2b-fim-baseline, temp=0.2, sample=1\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mdef hello_world():\n", "\u001b[0m\u001b[40m\u001b[97m    \u001b[0m\u001b[34mprint(\"Hello World!\")\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_leading_spaces.py ✓ | model=codegen-2b-fim-baseline, temp=0.2, sample=2\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mdef hello_world():\n", "\u001b[0m\u001b[40m\u001b[97m    \u001b[0m\u001b[34mprint(\"Hello World!\")\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_docstring.py ✗ | model=codegen-2b-fim-baseline, temp=0.2, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mdef get_odd_ints(lower, upper):\n", "    \"\"\"\u001b[0m\u001b[40m\u001b[97m\n", "    Return a list of odd integers in the range [lower, upper).\n", "    \u001b[0m\u001b[34m\"\"\"\n", "    return [i for i in range(lower, upper) if i % 2 == 1]\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_docstring.py ✗ | model=codegen-2b-fim-baseline, temp=0.2, sample=1\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mdef get_odd_ints(lower, upper):\n", "    \"\"\"\u001b[0m\u001b[40m\u001b[97m\n", "    Return an array of odd integers in the range [lower, upper).\n", "    \u001b[0m\u001b[34m\"\"\"\n", "    return [i for i in range(lower, upper) if i % 2 == 1]\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_docstring.py ✗ | model=codegen-2b-fim-baseline, temp=0.2, sample=2\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mdef get_odd_ints(lower, upper):\n", "    \"\"\"\u001b[0m\u001b[40m\u001b[97m\n", "    Return an array of odd integers in the range [lower, upper).\n", "    \u001b[0m\u001b[34m\"\"\"\n", "    return [i for i in range(lower, upper) if i % 2 == 1]\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_noop.py ✓ | model=codegen-2b-fim-baseline, temp=0.2, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mdef hello_world():\u001b[0m\u001b[40m\u001b[97m\u001b[0m\u001b[34m\n", "    print(\"Hello World!\")\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_noop.py ✓ | model=codegen-2b-fim-baseline, temp=0.2, sample=1\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mdef hello_world():\u001b[0m\u001b[40m\u001b[97m\u001b[0m\u001b[34m\n", "    print(\"Hello World!\")\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_noop.py ✓ | model=codegen-2b-fim-baseline, temp=0.2, sample=2\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mdef hello_world():\u001b[0m\u001b[40m\u001b[97m\u001b[0m\u001b[34m\n", "    print(\"Hello World!\")\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_trailing_return_with_var.py ✗ | model=codegen-2b-fim-baseline, temp=0.2, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mdef get_odd_ints(lower, upper):\n", "    results = []\u001b[0m\u001b[40m\u001b[97m\n", "    for i in range(lower, upper + 1):\n", "        if i % 2 == 1:\n", "            results.append(i)\u001b[0m\u001b[34m\n", "    return results\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_trailing_return_with_var.py ✗ | model=codegen-2b-fim-baseline, temp=0.2, sample=1\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mdef get_odd_ints(lower, upper):\n", "    results = []\u001b[0m\u001b[40m\u001b[97m\n", "    for i in range(lower, upper):\n", "        if i % 2 == 1:\n", "            results.append(i)\u001b[0m\u001b[34m\n", "    return results\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_trailing_return_with_var.py ✗ | model=codegen-2b-fim-baseline, temp=0.2, sample=2\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mdef get_odd_ints(lower, upper):\n", "    results = []\u001b[0m\u001b[40m\u001b[97m\n", "    for i in range(lower, upper + 1):\n", "        if i % 2 == 1:\n", "            results.append(i)\u001b[0m\u001b[34m\n", "    return results\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_multi_function_2.py ✗ | model=codegen-2b-fim-baseline, temp=0.2, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport copy\n", "from dataclasses import asdict, dataclass\n", "import json\n", "import pathlib\n", "\n", "@dataclass\n", "class MyClass:\n", "    def save_to_json(self, filename):\n", "        \"\"\"Saves the dataclass instance to a JSON file.\"\"\"\n", "        with pathlib.Path(filename).open(\"w\") as f:\n", "            json.dump(asdict(self), f)\n", "\n", "    @classmethod\n", "    def load_from_json(cls, filename):\n", "        \"\"\"Loads a dataclass instance from a JSON file.\"\"\"\n", "        with pathlib.Path(filename).open(\"r\") as f:\n", "            data = json.load(f)\n", "\u001b[0m\u001b[40m\u001b[97m        return cls(**data)\n", "\n", "    def clone(self):\n", "        \"\"\"Returns a deep copy of the instance.\"\"\"\n", "        return copy.deepcopy(self)\n", "\n", "    def __repr__(self):\n", "        attributes = \",\\n  \".join(\n", "            f\"{key} = {value!r}\" for key, value in vars(self).items()\n", "        )\n", "        return f\"ModelInput(\\n  {attributes}\\n)\"\n", "\n", "@dataclass\n", "class MyClass2:\n", "    def save_to_json(self, filename):\u001b[0m\u001b[34m\n", "        return cls(**data)\n", "\n", "    def clone(self):\n", "        \"\"\"Returns a deep copy of the instance.\"\"\"\n", "        return copy.deepcopy(self)\n", "\n", "    def __repr__(self):\n", "        attributes = \",\\n  \".join(\n", "            f\"{key} = {value!r}\" for key, value in vars(self).items()\n", "        )\n", "        return f\"ModelInput(\\n  {attributes}\\n)\"\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_multi_function_2.py ✗ | model=codegen-2b-fim-baseline, temp=0.2, sample=1\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport copy\n", "from dataclasses import asdict, dataclass\n", "import json\n", "import pathlib\n", "\n", "@dataclass\n", "class MyClass:\n", "    def save_to_json(self, filename):\n", "        \"\"\"Saves the dataclass instance to a JSON file.\"\"\"\n", "        with pathlib.Path(filename).open(\"w\") as f:\n", "            json.dump(asdict(self), f)\n", "\n", "    @classmethod\n", "    def load_from_json(cls, filename):\n", "        \"\"\"Loads a dataclass instance from a JSON file.\"\"\"\n", "        with pathlib.Path(filename).open(\"r\") as f:\n", "            data = json.load(f)\n", "\u001b[0m\u001b[40m\u001b[97m        return cls(**data)\n", "\n", "    def clone(self):\n", "        \"\"\"Returns a deep copy of the instance.\"\"\"\n", "        return copy.deepcopy(self)\n", "\n", "    def __repr__(self):\n", "        attributes = \",\\n  \".join(\n", "            f\"{key} = {value!r}\" for key, value in vars(self).items()\n", "        )\n", "        return f\"ModelInput(\\n  {attributes}\\n)\"\n", "\n", "@dataclass\n", "class MyClass2:\n", "    def save_to_json(self, filename):\u001b[0m\u001b[34m\n", "        return cls(**data)\n", "\n", "    def clone(self):\n", "        \"\"\"Returns a deep copy of the instance.\"\"\"\n", "        return copy.deepcopy(self)\n", "\n", "    def __repr__(self):\n", "        attributes = \",\\n  \".join(\n", "            f\"{key} = {value!r}\" for key, value in vars(self).items()\n", "        )\n", "        return f\"ModelInput(\\n  {attributes}\\n)\"\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_multi_function_2.py ✗ | model=codegen-2b-fim-baseline, temp=0.2, sample=2\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport copy\n", "from dataclasses import asdict, dataclass\n", "import json\n", "import pathlib\n", "\n", "@dataclass\n", "class MyClass:\n", "    def save_to_json(self, filename):\n", "        \"\"\"Saves the dataclass instance to a JSON file.\"\"\"\n", "        with pathlib.Path(filename).open(\"w\") as f:\n", "            json.dump(asdict(self), f)\n", "\n", "    @classmethod\n", "    def load_from_json(cls, filename):\n", "        \"\"\"Loads a dataclass instance from a JSON file.\"\"\"\n", "        with pathlib.Path(filename).open(\"r\") as f:\n", "            data = json.load(f)\n", "\u001b[0m\u001b[40m\u001b[97m        return cls(**data)\n", "\n", "@dataclass\n", "class ModelInput:\n", "    \"\"\"\n", "    A class to represent a model input.\n", "\n", "    Attributes:\n", "        name: The name of the input.\n", "        shape: The shape of the input.\n", "        dtype: The data type of the input.\n", "        data: The data of the input.\n", "    \"\"\"\n", "    name: str\n", "    shape: tuple\n", "    dtype: str\n", "    data: Any\n", "\n", "    def __post_init__(self):\n", "        \"\"\"\n", "        Initializes the instance.\n", "        \"\"\"\n", "        \u001b[0m\u001b[34m\n", "        return cls(**data)\n", "\n", "    def clone(self):\n", "        \"\"\"Returns a deep copy of the instance.\"\"\"\n", "        return copy.deepcopy(self)\n", "\n", "    def __repr__(self):\n", "        attributes = \",\\n  \".join(\n", "            f\"{key} = {value!r}\" for key, value in vars(self).items()\n", "        )\n", "        return f\"ModelInput(\\n  {attributes}\\n)\"\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_argparse.py ✗ | model=codegen-2b-fim-baseline, temp=0.2, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34<PERSON><PERSON><PERSON> argparse\n", "\n", "parser = argparse.ArgumentParser()\n", "parser.add_argument(\n", "    \"--model\",\n", "    type=str,\n", "    default=\"my_model\",\n", "    help=\"which model to serve\",\n", ")\n", "parser.add_argument(\n", "    \"--disable_completions\",\n", "    action=\"store_true\",\n", "\u001b[0m\u001b[40m\u001b[97m    help=\"disable completion requests\",\n", ")\n", "parser.add_argument(\n", "    \"--single_line_completions\",\n", "    action=\"store_true\",\n", "    help=\"whether to return single-line completions\",\n", ")\n", "parser.add_argument(\n", "    \"--disable_content_type_negotiation\",\n", "    action=\"store_true\",\n", "    help=\"disable content type negotiation\",\n", ")\n", "parser.add_argument(\n", "    \"--disable_compression\",\n", "    action=\"store_true\",\n", "    help=\"disable compression\",\n", ")\n", "parser.add_\u001b[0m\u001b[34m    help=\"disable completion requests\",\n", ")\n", "parser.add_argument(\n", "    \"--single_line_completions\",\n", "    action=\"store_true\",\n", "    help=\"whether to return single-line completions\",\n", ")\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_argparse.py ✗ | model=codegen-2b-fim-baseline, temp=0.2, sample=1\n", "----------------------------------------------------------------------------------------\n", "\u001b[34<PERSON><PERSON><PERSON> argparse\n", "\n", "parser = argparse.ArgumentParser()\n", "parser.add_argument(\n", "    \"--model\",\n", "    type=str,\n", "    default=\"my_model\",\n", "    help=\"which model to serve\",\n", ")\n", "parser.add_argument(\n", "    \"--disable_completions\",\n", "    action=\"store_true\",\n", "\u001b[0m\u001b[40m\u001b[97m    help=\"disable completion requests\",\n", ")\n", "parser.add_argument(\n", "    \"--single_line_completions\",\n", "    action=\"store_true\",\n", "    help=\"whether to return single-line completions\",\n", ")\n", "parser.add_argument(\n", "    \"--disable_extensions\",\n", "    action=\"store_true\",\n", "    help=\"disable extensions\",\n", ")\n", "parser.add_argument(\n", "    \"--disable_hints\",\n", "    action=\"store_true\",\n", "    help=\"disable hints\",\n", ")\n", "parser.add_argument(\n", "    \"--\u001b[0m\u001b[34m    help=\"disable completion requests\",\n", ")\n", "parser.add_argument(\n", "    \"--single_line_completions\",\n", "    action=\"store_true\",\n", "    help=\"whether to return single-line completions\",\n", ")\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_argparse.py ✗ | model=codegen-2b-fim-baseline, temp=0.2, sample=2\n", "----------------------------------------------------------------------------------------\n", "\u001b[34<PERSON><PERSON><PERSON> argparse\n", "\n", "parser = argparse.ArgumentParser()\n", "parser.add_argument(\n", "    \"--model\",\n", "    type=str,\n", "    default=\"my_model\",\n", "    help=\"which model to serve\",\n", ")\n", "parser.add_argument(\n", "    \"--disable_completions\",\n", "    action=\"store_true\",\n", "\u001b[0m\u001b[40m\u001b[97m    help=\"disable completion requests\",\n", ")\n", "parser.add_argument(\n", "    \"--single_line_completions\",\n", "    action=\"store_true\",\n", "    help=\"whether to return single-line completions\",\n", ")\n", "parser.add_argument(\n", "    \"--single_line_completions_delimiter\",\n", "    type=str,\n", "    default=\"\\n\",\n", "    help=\"single-line completion delimiter\",\n", ")\n", "parser.add_argument(\n", "    \"--single_line_completions_max_lines\",\n", "    type=int\u001b[0m\u001b[34m    help=\"disable completion requests\",\n", ")\n", "parser.add_argument(\n", "    \"--single_line_completions\",\n", "    action=\"store_true\",\n", "    help=\"whether to return single-line completions\",\n", ")\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_multi_function_1.py ✓ | model=codegen-2b-fim-baseline, temp=0.2, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport copy\n", "from dataclasses import asdict, dataclass\n", "import json\n", "import pathlib\n", "\n", "@dataclass\n", "class MyClass:\n", "    def save_to_json(self, filename):\n", "        \"\"\"Saves the dataclass instance to a JSON file.\"\"\"\n", "        with pathlib.Path(filename).open(\"w\") as f:\n", "            json.dump(asdict(self), f)\n", "\n", "    @classmethod\n", "    def load_from_json(cls, filename):\n", "        \"\"\"Loads a dataclass instance from a JSON file.\"\"\"\n", "        with pathlib.Path(filename).open(\"r\") as f:\n", "            data = json.load(f)\n", "\n", "        return cls(**data)\n", "\n", "    def clone(self):\n", "        \"\"\"Returns a deep copy of the instance.\"\"\"\n", "\u001b[0m\u001b[40m\u001b[97m\u001b[0m\u001b[34m        return copy.deepcopy(self)\n", "\n", "    def __repr__(self):\n", "        attributes = \",\\n  \".join(\n", "            f\"{key} = {value!r}\" for key, value in vars(self).items()\n", "        )\n", "        return f\"ModelInput(\\n  {attributes}\\n)\"\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_multi_function_1.py ✓ | model=codegen-2b-fim-baseline, temp=0.2, sample=1\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport copy\n", "from dataclasses import asdict, dataclass\n", "import json\n", "import pathlib\n", "\n", "@dataclass\n", "class MyClass:\n", "    def save_to_json(self, filename):\n", "        \"\"\"Saves the dataclass instance to a JSON file.\"\"\"\n", "        with pathlib.Path(filename).open(\"w\") as f:\n", "            json.dump(asdict(self), f)\n", "\n", "    @classmethod\n", "    def load_from_json(cls, filename):\n", "        \"\"\"Loads a dataclass instance from a JSON file.\"\"\"\n", "        with pathlib.Path(filename).open(\"r\") as f:\n", "            data = json.load(f)\n", "\n", "        return cls(**data)\n", "\n", "    def clone(self):\n", "        \"\"\"Returns a deep copy of the instance.\"\"\"\n", "\u001b[0m\u001b[40m\u001b[97m\u001b[0m\u001b[34m        return copy.deepcopy(self)\n", "\n", "    def __repr__(self):\n", "        attributes = \",\\n  \".join(\n", "            f\"{key} = {value!r}\" for key, value in vars(self).items()\n", "        )\n", "        return f\"ModelInput(\\n  {attributes}\\n)\"\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_multi_function_1.py ✓ | model=codegen-2b-fim-baseline, temp=0.2, sample=2\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport copy\n", "from dataclasses import asdict, dataclass\n", "import json\n", "import pathlib\n", "\n", "@dataclass\n", "class MyClass:\n", "    def save_to_json(self, filename):\n", "        \"\"\"Saves the dataclass instance to a JSON file.\"\"\"\n", "        with pathlib.Path(filename).open(\"w\") as f:\n", "            json.dump(asdict(self), f)\n", "\n", "    @classmethod\n", "    def load_from_json(cls, filename):\n", "        \"\"\"Loads a dataclass instance from a JSON file.\"\"\"\n", "        with pathlib.Path(filename).open(\"r\") as f:\n", "            data = json.load(f)\n", "\n", "        return cls(**data)\n", "\n", "    def clone(self):\n", "        \"\"\"Returns a deep copy of the instance.\"\"\"\n", "\u001b[0m\u001b[40m\u001b[97m\u001b[0m\u001b[34m        return copy.deepcopy(self)\n", "\n", "    def __repr__(self):\n", "        attributes = \",\\n  \".join(\n", "            f\"{key} = {value!r}\" for key, value in vars(self).items()\n", "        )\n", "        return f\"ModelInput(\\n  {attributes}\\n)\"\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_dataclass_methods.py ✗ | model=codegen-2b-fim-baseline, temp=0.2, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mfrom dataclasses import dataclass, field\n", "from typing import *\n", "\n", "@dataclass\n", "class Patch:\n", "    \"\"\"Represents a patch to a file using its character span.\"\"\"\n", "\n", "    file_content: str = \"\"\n", "    \"\"\"The file contents being modified. text being patched. Assumes UTF-8 encoding.\"\"\"\n", "    char_start: int = 0\n", "    \"\"\"Character offset for the patch.\"\"\"\n", "    char_end: int = 0\n", "    \"\"\"character offset for the patch.\"\"\"\n", "    patch_content: str = \"\"\n", "    \"\"\"the patch content.\"\"\"\n", "    patch_id: str = \"\"\n", "\n", "    @classmethod\n", "    def for_span(\n", "        cls,\n", "        file_content: str,\n", "        char_start: int,\n", "        char_end: int,\n", "\u001b[0m\u001b[40m\u001b[97m        patch_content: str,\n", "        **kwargs,\n", "    ):\n", "        \"\"\"Create a patch for the region [char_start, char_end) in file contents.\"\"\"\n", "        return cls(\n", "            file_content=file_content,\n", "            char_start=char_start,\n", "            char_end=char_end,\n", "            patch_content=patch_content,\n", "            **kwargs,\n", "        )\n", "\n", "    @classmethod\n", "    def for_file(\n", "        cls,\n", "        file_content: str,\n", "        **kwargs,\n", "    ):\u001b[0m\u001b[34m        **kwargs,\n", "    ):\n", "        \"\"\"create a patch for the region [char_start, char_end) in file contents.\"\"\"\n", "        return cls(\n", "            file_content=file_content,\n", "            char_start=char_start,\n", "            char_end=char_end,\n", "            patch_content=file_content[char_start:char_end],\n", "            **kwargs,\n", "        )\n", "\n", "    @classmethod\n", "    def empty(cls, file_content: str, **kwargs):\n", "        \"\"\"Create an empty Patch for a file.\"\"\"\n", "        return Patch(\n", "            file_content=file_content,\n", "            char_start=0,\n", "            char_end=0,\n", "            patch_content=\"\",\n", "            **kwargs,\n", "        )\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_dataclass_methods.py ✗ | model=codegen-2b-fim-baseline, temp=0.2, sample=1\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mfrom dataclasses import dataclass, field\n", "from typing import *\n", "\n", "@dataclass\n", "class Patch:\n", "    \"\"\"Represents a patch to a file using its character span.\"\"\"\n", "\n", "    file_content: str = \"\"\n", "    \"\"\"The file contents being modified. text being patched. Assumes UTF-8 encoding.\"\"\"\n", "    char_start: int = 0\n", "    \"\"\"Character offset for the patch.\"\"\"\n", "    char_end: int = 0\n", "    \"\"\"character offset for the patch.\"\"\"\n", "    patch_content: str = \"\"\n", "    \"\"\"the patch content.\"\"\"\n", "    patch_id: str = \"\"\n", "\n", "    @classmethod\n", "    def for_span(\n", "        cls,\n", "        file_content: str,\n", "        char_start: int,\n", "        char_end: int,\n", "\u001b[0m\u001b[40m\u001b[97m        patch_content: str,\n", "        **kwargs,\n", "    ):\n", "        \"\"\"Create a patch for the region [char_start, char_end) in file contents.\"\"\"\n", "        return cls(\n", "            file_content=file_content,\n", "            char_start=char_start,\n", "            char_end=char_end,\n", "            patch_content=patch_content,\n", "            **kwargs,\n", "        )\n", "\n", "    @classmethod\n", "    def for_file(\n", "        cls,\n", "        file_content: str,\n", "        **kwargs,\n", "    ):\u001b[0m\u001b[34m        **kwargs,\n", "    ):\n", "        \"\"\"create a patch for the region [char_start, char_end) in file contents.\"\"\"\n", "        return cls(\n", "            file_content=file_content,\n", "            char_start=char_start,\n", "            char_end=char_end,\n", "            patch_content=file_content[char_start:char_end],\n", "            **kwargs,\n", "        )\n", "\n", "    @classmethod\n", "    def empty(cls, file_content: str, **kwargs):\n", "        \"\"\"Create an empty Patch for a file.\"\"\"\n", "        return Patch(\n", "            file_content=file_content,\n", "            char_start=0,\n", "            char_end=0,\n", "            patch_content=\"\",\n", "            **kwargs,\n", "        )\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_dataclass_methods.py ✗ | model=codegen-2b-fim-baseline, temp=0.2, sample=2\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mfrom dataclasses import dataclass, field\n", "from typing import *\n", "\n", "@dataclass\n", "class Patch:\n", "    \"\"\"Represents a patch to a file using its character span.\"\"\"\n", "\n", "    file_content: str = \"\"\n", "    \"\"\"The file contents being modified. text being patched. Assumes UTF-8 encoding.\"\"\"\n", "    char_start: int = 0\n", "    \"\"\"Character offset for the patch.\"\"\"\n", "    char_end: int = 0\n", "    \"\"\"character offset for the patch.\"\"\"\n", "    patch_content: str = \"\"\n", "    \"\"\"the patch content.\"\"\"\n", "    patch_id: str = \"\"\n", "\n", "    @classmethod\n", "    def for_span(\n", "        cls,\n", "        file_content: str,\n", "        char_start: int,\n", "        char_end: int,\n", "\u001b[0m\u001b[40m\u001b[97m        patch_content: str,\n", "        **kwargs,\n", "    ):\n", "        \"\"\"Create a patch for the region [char_start, char_end) in file contents.\"\"\"\n", "        return cls(\n", "            file_content=file_content,\n", "            char_start=char_start,\n", "            char_end=char_end,\n", "            patch_content=patch_content,\n", "            **kwargs,\n", "        )\n", "\n", "    @classmethod\n", "    def for_range(\n", "        cls,\n", "        file_content: str,\n", "        char_start: int,\n", "\u001b[0m\u001b[34m        **kwargs,\n", "    ):\n", "        \"\"\"create a patch for the region [char_start, char_end) in file contents.\"\"\"\n", "        return cls(\n", "            file_content=file_content,\n", "            char_start=char_start,\n", "            char_end=char_end,\n", "            patch_content=file_content[char_start:char_end],\n", "            **kwargs,\n", "        )\n", "\n", "    @classmethod\n", "    def empty(cls, file_content: str, **kwargs):\n", "        \"\"\"Create an empty Patch for a file.\"\"\"\n", "        return Patch(\n", "            file_content=file_content,\n", "            char_start=0,\n", "            char_end=0,\n", "            patch_content=\"\",\n", "            **kwargs,\n", "        )\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_dataclass.py ✗ | model=codegen-2b-fim-baseline, temp=0.2, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mfrom dataclasses import dataclass, field\n", "from typing import *\n", "\n", "@dataclass\n", "class Datum:\n", "    \"\"\"Represents a blob of data.\"\"\"\n", "\n", "    source: str = \"\"\n", "    \"\"\"Where the data comes from.\"\"\"\n", "    char_start: int = 0\n", "    \"\"\"Character offset for the blob.\"\"\"\n", "    char_end: int = 0\n", "    \"\"\"Character offset for the blob.\"\"\"\n", "    _extra: Mapping = field(default_factory=dict, repr=True, init=True\u001b[0m\u001b[40m\u001b[97m)\n", "    \"\"\"A handy field to store extra data.\"\"\"\n", "\n", "    def __post_init__(self):\n", "        self.char_start = self.char_start if self.char_start is not None else 0\n", "        self.char_end = self.char_end if self.char_end is not None else 0\n", "\n", "    def __repr__(self):\n", "        return f\"Datum(source={self.source}, char_start={self.char_start}, char_end={self.char_end}, _extra={self._extra})\"\n", "\n", "    def __\u001b[0m\u001b[34m)\n", "    \"\"\"A handy field to store extra data.\"\"\"\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_dataclass.py ✗ | model=codegen-2b-fim-baseline, temp=0.2, sample=1\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mfrom dataclasses import dataclass, field\n", "from typing import *\n", "\n", "@dataclass\n", "class Datum:\n", "    \"\"\"Represents a blob of data.\"\"\"\n", "\n", "    source: str = \"\"\n", "    \"\"\"Where the data comes from.\"\"\"\n", "    char_start: int = 0\n", "    \"\"\"Character offset for the blob.\"\"\"\n", "    char_end: int = 0\n", "    \"\"\"Character offset for the blob.\"\"\"\n", "    _extra: Mapping = field(default_factory=dict, repr=True, init=True\u001b[0m\u001b[40m\u001b[97m)\n", "    \"\"\"A handy field to store extra data.\"\"\"\n", "\n", "    def __post_init__(self):\n", "        self.char_end = self.char_start + len(self.source)\n", "\n", "    def __repr__(self):\n", "        return f\"Datum(source={self.source!r}, char_start={self.char_start}, char_end={self.char_end})\"\n", "\n", "    def __str__(self):\n", "        return self.source[self.char_start:self.char_end]\n", "\n", "    def __eq__(self,\u001b[0m\u001b[34m)\n", "    \"\"\"A handy field to store extra data.\"\"\"\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_dataclass.py ✗ | model=codegen-2b-fim-baseline, temp=0.2, sample=2\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mfrom dataclasses import dataclass, field\n", "from typing import *\n", "\n", "@dataclass\n", "class Datum:\n", "    \"\"\"Represents a blob of data.\"\"\"\n", "\n", "    source: str = \"\"\n", "    \"\"\"Where the data comes from.\"\"\"\n", "    char_start: int = 0\n", "    \"\"\"Character offset for the blob.\"\"\"\n", "    char_end: int = 0\n", "    \"\"\"Character offset for the blob.\"\"\"\n", "    _extra: Mapping = field(default_factory=dict, repr=True, init=True\u001b[0m\u001b[40m\u001b[97m)\n", "    \"\"\"A handy field to store extra data.\"\"\"\n", "\n", "    def __post_init__(self):\n", "        self.char_start = self.char_end = 0\n", "        self.source = self.char_start = self.char_end = \"\"\n", "\n", "    def __repr__(self):\n", "        return f\"Datum({self.source}, {self.char_start}, {self.char_end})\"\n", "\n", "    def __str__(self):\n", "        return f\"{self.source} ({self.char_start}-{self.char_end\u001b[0m\u001b[34m)\n", "    \"\"\"A handy field to store extra data.\"\"\"\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_trailing_return.py ✗ | model=codegen-2b-fim-baseline, temp=0.2, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mdef get_odd_ints(lower, upper):\n", "\u001b[0m\u001b[40m\u001b[97m    \"\"\"\n", "    Get odd integers between lower and upper.\n", "    \"\"\"\n", "    results = []\n", "    for i in range(lower, upper + 1):\n", "        if i % 2 == 1:\n", "            results.append(i)\u001b[0m\u001b[34m\n", "    return results\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_trailing_return.py ✗ | model=codegen-2b-fim-baseline, temp=0.2, sample=1\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mdef get_odd_ints(lower, upper):\n", "\u001b[0m\u001b[40m\u001b[97m    results = []\n", "    for i in range(lower, upper + 1):\n", "        if i % 2 == 1:\n", "            results.append(i)\u001b[0m\u001b[34m\n", "    return results\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_trailing_return.py ✗ | model=codegen-2b-fim-baseline, temp=0.2, sample=2\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mdef get_odd_ints(lower, upper):\n", "\u001b[0m\u001b[40m\u001b[97m    results = []\n", "    for i in range(lower, upper + 1):\n", "        if i % 2 == 1:\n", "            results.append(i)\u001b[0m\u001b[34m\n", "    return results\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n"]}], "source": ["# FIM behavior\n", "run_all_tests(model, \"codegen-2b-fim-baseline\", FIM_TESTS, n_samples=3, temperature=0.2)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["========================================================================================\n", "MODEL: codegen-2b-fim-baseline\n", "----------------------------------------------------------------------------------------\n", "========================================================================================\n", "EXAMPLE: structure_2.cpp ✗ | model=codegen-2b-fim-baseline, temp=0.0, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "#include <vector>\n", "\n", "struct Person {\n", "    char* name;\n", "    int age;\n", "    std::vector<Person*> parents;\n", "};\n", "\n", "void get_ancestors\u001b[0m\u001b[40m\u001b[97m(Person* p);\n", "\n", "void get_descendants(Person* p);\n", "\n", "void get_descendants_recursive(Person* p);\n", "\n", "void get_descendants_iterative(Person* p);\n", "\n", "void get_descendants_iterative_recursive(Person* p);\n", "\n", "void get_descendants_iterative_recursive_with_stack(Person* p);\n", "\n", "void get_descendants_iterative_recursive_with_stack_and_queue(Person* p);\n", "\n", "void get_descendants_iterative_recursive_with_stack_and_queue_and_queue_with_stack(Person* p);\n", "\n", "void get_descendants_iterative_recursive_with_stack_and_queue_and_queue_with_stack_\u001b[0m\u001b[34m\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_4.cpp ✗ | model=codegen-2b-fim-baseline, temp=0.0, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "#include <ctime>\n", "#include <iostream>\n", "#include <string>\n", "\n", "struct Person {\n", "    std::string name;\n", "    std::string profession;\n", "    std::tm dob;\n", "};\n", "\n", "std::ostream& operator<<(std::ostream& os, const Person& person) {\n", "    os << person.name << \u001b[0m\u001b[40m\u001b[97m\n", "        \" is a \" << person.profession << \n", "        \" and was born on \" << std::asctime(&\u001b[0m\u001b[34m << \"and was born on \" << std::asctime(&person.dob);\n", "    return os;\n", "}\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: double_free.cpp ✗ | model=codegen-2b-fim-baseline, temp=0.0, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "int main() {\n", "    int *arr = new int[10];\n", "    // Fill arr with increasing numbers\n", "    for (int i = 0; i < 10; i++) arr[i] = i;\n", "    delete arr;\n", "    // Fill arr with decreasing numbers\n", "\u001b[0m\u001b[40m\u001b[97m    for (int i = 9; i >= 0; i--) arr[i] = i;\n", "    // Print arr\n", "    for (int i = 0; i < 10; i++) cout << arr[i] << \" \";\n", "    cout << endl;\n", "    // Print arr\n", "    for (int i =\u001b[0m\u001b[34m}\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_1.cpp ✗ | model=codegen-2b-fim-baseline, temp=0.0, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "#include <vector>\n", "\n", "struct Person {\n", "    char* name;\n", "    int age;\n", "\u001b[0m\u001b[40m\u001b[97m    std::vector<const Person*> parents;\n", "\u001b[0m\u001b[34m};\n", "\n", "void get_ancestors(const Person& person, std::vector<const Person*> acc) {\n", "    acc.push_back(&person);\n", "    for (const auto* parent : person.parents) {\n", "        get_ancestors(*parent, acc);\n", "    }\n", "}\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_5.cpp ✗ | model=codegen-2b-fim-baseline, temp=0.0, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "#include <ctime>\n", "#include <iostream>\n", "#include <string>\n", "\n", "struct Person {\n", "    std::string name;\n", "    std::string profession;\n", "    std::tm dob;\n", "};\n", "\n", "std::ostream& operator<<(std::ostream& os, const Person& person) {\n", "\u001b[0m\u001b[40m\u001b[97m    os << person.name << \" \" << person.profession << \" \" << person.dob.tm_year << \"-\" << person.dob.tm_mon << \"-\" << person.dob.tm_mday;\n", "    return os;\n", "}\n", "\n", "int main() {\n", "    std::cout << \"Enter name: \";\n", "    std::string name;\n", "    std::cin >> name;\n", "\n", "    std::cout << \"Enter profession: \";\n", "    std::string profession;\n", "    std::\u001b[0m\u001b[34m}\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_3.cpp ✗ | model=codegen-2b-fim-baseline, temp=0.0, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "#include <ctime>\n", "#include <iostream>\n", "#include <string>\n", "#include <vector>\n", "\n", "struct Person {\n", "    std::string name;\n", "    std::tm dob;\n", "    std::vector<Person*> parents;\n", "};\n", "\n", "int main() {\n", "    Person alice = {\n", "        \"Alice\",\n", "        {tm_mday: 1, tm_mon: 4, tm_year: 89},\n", "        {}};\n", "    int age = \u001b[0m\u001b[40m\u001b[97m\n", "        (alice.dob.tm_year - 1900) * 365 +\n", "        (alice.dob.tm_mon - 1) * 30 +\n", "        alice.dob.tm_mday - 1;\u001b[0m\u001b[34m\n", "    std::cout << \"Alice is \" << age << \" years old\" << std::endl;\n", "\n", "    return 0;\n", "}\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_1.go ✗ | model=codegen-2b-fim-baseline, temp=0.0, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mpackage main\n", "\n", "type Person struct {\n", "\tname    string\n", "\tage     int\n", "\t\u001b[0m\u001b[40m\u001b[97mparents []*Person\n", "}\n", "\n", "func main() {\n", "\tp1 :=\u001b[0m\u001b[34m\n", "}\n", "\n", "func get_ancestors(person *Person, acc []*Person) []*Person {\n", "\tfor _, parent := range person.parents {\n", "\t\tacc = append(acc, parent)\n", "\t\tacc = get_ancestors(parent, acc)\n", "\t}\n", "\treturn acc\n", "}\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_2.go ✗ | model=codegen-2b-fim-baseline, temp=0.0, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mpackage main\n", "\n", "type Person struct {\n", "\tname    string\n", "\tage     int\n", "\tparents []*Person\n", "}\n", "\n", "func get_ancestors\u001b[0m\u001b[40m\u001b[97m(p *Person) []*Person {\n", "\tvar ancestors []*Person\n", "\tfor _, parent := range p.parents {\n", "\t\tancestors = append(ancestors, parent)\n", "\t\tancestors = append(ancestors, get_ancestors(parent)...)\n", "\t}\n", "\treturn ancestors\n", "}\n", "\n", "func main() {\n", "\tp1 := &Person{name: \"<PERSON>\", age: 18}\n", "\tp2 := &Person{name: \"<PERSON>\", age: 19}\n", "\tp3 := &Person{name: \"<PERSON>\", age: 20}\n", "\tp4 := &Person{name: \"<PERSON>\", age: 21}\n", "\tp5 := &Person{name: \"<PERSON>\", age: 22}\n", "\tp6 := &Person{name: \"<PERSON>\", age: 23}\u001b[0m\u001b[34m\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_3.go ✗ | model=codegen-2b-fim-baseline, temp=0.0, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mpackage main\n", "\n", "type Person struct {\n", "\tname    string\n", "\tage     int\n", "\tparents []*Person\n", "}\n", "\n", "func get_ancestors(person *Person, acc []*Person) []*Person {\n", "\u001b[0m\u001b[40m\u001b[97m\tif person == nil {\n", "\t\treturn acc\n", "\t}\n", "\tacc = append(acc, person)\n", "\tfor _, p := range person.parents {\n", "\t\tacc = get_ancestors(p, acc)\n", "\t}\n", "\treturn acc\n", "}\n", "\n", "func main() {\n", "\tp1 := &Person{\n", "\t\tname:    \"<PERSON>\",\n", "\t\tage:     30,\n", "\t\tparents: []*Person{},\n", "\t}\n", "\tp2 := &Person{\n", "\t\tname:    \"<PERSON>\",\n", "\t\tage:     30,\n", "\t\tparents:\u001b[0m\u001b[34m}\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_4.go ✗ | model=codegen-2b-fim-baseline, temp=0.0, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mpackage main\n", "\n", "import \"sort\"\n", "\n", "type Person struct {\n", "\tname    string\n", "\tage     int\n", "}\n", "\n", "func main() {\n", "\tpeople := []Person{\n", "\t\t\tPerson{\"alice\", 60},\n", "\t\t\tPerson{\"bob\", 50},\n", "\t\t\tPerson{\"charlie\", 20},\n", "\t\t}\n", "\t// sort by age\n", "\u001b[0m\u001b[40m\u001b[97m\tsort.Slice(people, func(i, j int) bool {\n", "\t\treturn people[i].age < people[j].age\n", "\t})\n", "\t// print\n", "\tfor _, person := range people {\n", "\t\tfmt.Println(person.name, person.age)\n", "\t}\n", "\u001b[0m\u001b[34m}\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: chunk_request.py ✗ | model=codegen-2b-fim-baseline, temp=0.0, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport math\n", "\n", "\n", "def make_chunked_requests(payload, chunk_size):\n", "    \"\"\"Chunk a payload into `chunk_size` portions and make requests for them.\n", "\n", "    Args:\n", "        payload: the payload to chunk and send.\n", "\n", "    Returns:\n", "        A list of requests; each request is a dictionary with keys:\n", "            chunk_idx: the index of the current chunk.\n", "            chunk_count: how many chunks are required in total.\n", "            chunk_payload: the chunk payload.\n", "    \"\"\"\n", "    requests = []\n", "    chunk_count = math.ceil(len(payload) / chunk_size)\n", "\u001b[0m\u001b[40m\u001b[97m    for chunk_idx in range(chunk_count):\n", "        chunk_payload = payload[chunk_idx * chunk_size: (chunk_idx + 1) * chunk_size]\n", "        requests.append({\n", "            'chunk_idx': chunk_idx,\n", "            'chunk_count': chunk_count,\n", "            'chunk_payload': chunk_payload\n", "        })\n", "\u001b[0m\u001b[34m    return requests\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: infer_preconditions.py ✗ | model=codegen-2b-fim-baseline, temp=0.0, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mdef remove_duplicates(sorted_lst):\n", "    \"\"\"Remove duplicates in-place from a sorted list.\"\"\"\n", "    if not sorted_lst:\n", "        return\n", "    write_idx = 0\n", "    for i in range(1, len(sorted_lst)):\n", "        if sorted_lst[i] != sorted_lst[write_idx]:\n", "            write_idx += 1\n", "            sorted_lst[write_idx] = sorted_lst[i]\n", "    del sorted_lst[write_idx+1:]\n", "\n", "\n", "lst = [1, 4, 2, 6, 1, 3, 4]\n", "# Print the list of unique integers in lst.\n", "\u001b[0m\u001b[40m\u001b[97mprint(list(set(lst)))\n", "\n", "# Print the list of unique integers in lst, but in sorted order.\n", "print(list(set(lst)))\n", "\n", "# Print the list of unique\u001b[0m\u001b[34m\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_4.py ✗ | model=codegen-2b-fim-baseline, temp=0.0, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport dataclasses\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    age: int\n", "    profession: str\n", "\n", "\n", "persons = [\n", "    Person('<PERSON>', 42, '<PERSON>'),\n", "    Person('<PERSON>', 22, '<PERSON>er'),\n", "    Person('<PERSON>', 12, ''),\n", "    Person('<PERSON>', 18, 'Entrepreneur'),\n", "]\n", "\n", "# Sort persons by age\n", "\u001b[0m\u001b[40m\u001b[97mpersons.sort(key=lambda p: p.age)\n", "\n", "print(persons)\n", "\u001b[0m\u001b[34m\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_6.py ✗ | model=codegen-2b-fim-baseline, temp=0.0, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport dataclasses\n", "import datetime\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    dob: datetime.date\n", "    profession: str\n", "\n", "    def __str__(self):\n", "        return f\"{self.name} \u001b[0m\u001b[40m\u001b[97m\u001b[0m\u001b[34m and is {self.dob.year} years old\"\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_2.py ✗ | model=codegen-2b-fim-baseline, temp=0.0, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "import dataclasses\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    age: int\n", "    parents: list['Person']\n", "\n", "\n", "def get_ancestors(person: Person):\n", "    ancestors = []\n", "\u001b[0m\u001b[40m\u001b[97m    for parent in person.parents:\n", "        ancestors.append(get_ancestors(parent))\n", "    return ancestors\n", "\n", "def get_descendants(person: Person):\n", "    descendants = []\n", "    for child in person.children:\n", "        descendants.append(get_descendants(child))\n", "    return descendants\n", "\n", "def get_descendants_and_ancestors(person: Person):\n", "    descendants = get_descendants(person)\n", "    ancestors = get_ancestors(person)\n", "    return descendants, ancestors\n", "\n", "def get_descendants_and_ancestors\u001b[0m\u001b[34m\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_3.py ✗ | model=codegen-2b-fim-baseline, temp=0.0, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport dataclasses\n", "import datetime\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    dob: datetime.date\n", "    profession: str\n", "\n", "\n", "alice = Person('<PERSON>', datetime.date.fromisoformat('1989-04-01'), 'Engineer')\n", "now = datetime.date.today()\n", "\u001b[0m\u001b[40m\u001b[97m\n", "print(f\"<PERSON>'s age is {alice.dob - now} years old.\")\n", "\n", "\u001b[0m\u001b[34mprint(f\"<PERSON> is {age} years old!\")\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_1.py ✗ | model=codegen-2b-fim-baseline, temp=0.0, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport dataclasses\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    age: int\n", "\u001b[0m\u001b[40m\u001b[97m    parents: List[Person] = dataclasses.field(default_factory=list)\n", "    children: List[Person] = dataclasses.field(default_factory=list)\n", "\n", "def get_descendants(person: Person):\n", "    ret = []\n", "    for child in person.children:\n", "\u001b[0m\u001b[34m\n", "\n", "def get_ancestors(person: Person):\n", "    ret = []\n", "    for parent in person.parents:\n", "        ret.append(parent)\n", "        ret.extend(get_ancestors(parent))\n", "    return ret\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: testing_1.py ✗ | model=codegen-2b-fim-baseline, temp=0.0, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "import dataclasses\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    age: int\n", "    profession: str\n", "\n", "\n", "def sort_by_age(people):\n", "    people.sort(key=lambda x: x.age)\n", "\n", "\n", "def test_sort_by_age():\n", "    p1 = Person('<PERSON>', 42, '<PERSON>')\n", "    p2 = Person('<PERSON>', 22, 'Marketer')\n", "    p3 = Person('<PERSON>', 12, '')\n", "\u001b[0m\u001b[40m\u001b[97m    p4 = Person('<PERSON>', 10, '<PERSON><PERSON><PERSON>')\n", "    people = [p1, p2, p3, p4]\n", "    sort_by_age(people)\n", "    assert people == [p1, p4, p2, p3]\n", "\n", "\n", "if __name__ == '__main__':\n", "    test_sort_by_age()\n", "\u001b[0m\u001b[34m\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: testing_2.py ✗ | model=codegen-2b-fim-baseline, temp=0.0, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "import dataclasses\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    age: int\n", "    profession: str\n", "\n", "\n", "def sort_by_age(people):\n", "    people.sort(key=lambda x: x.age)\n", "\n", "\n", "def test_sort_by_age():\n", "\u001b[0m\u001b[40m\u001b[97m    p1 = Person('<PERSON>', 30, 'Engineer')\n", "    p2 = Person('<PERSON>', 25, 'Engineer')\n", "    p3 = Person('<PERSON>', 40, '<PERSON><PERSON><PERSON>')\n", "\u001b[0m\u001b[34m    people = [p1, p2, p3]\n", "    sort_by_age(people)\n", "    assert people == [p3, p1, p2]\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: remove_duplicates.py ✗ | model=codegen-2b-fim-baseline, temp=0.0, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "def remove_duplicates(sorted_lst):\n", "    \"\"\"Remove duplicates in-place from a sorted list.\"\"\"\n", "    if not sorted_lst:\n", "        return\n", "\u001b[0m\u001b[40m\u001b[97m    last = sorted_lst[0]\n", "    for i in range(1, len(sorted_lst)):\n", "        if sorted_lst[i] == last:\n", "            sorted_lst.pop(i)\n", "        else:\n", "            last = sorted_lst[i]\n", "    return sorted_lst\n", "\n", "\n", "def get_sorted_list(lst):\n", "    \"\"\"Sort a list in-place.\"\"\"\n", "    return sorted(lst, key=lambda x: x.lower())\n", "\n", "\n", "def get_sorted_list_by_key(lst, key):\n", "    \"\"\"Sort a list in-place by a key.\"\"\"\n", "    return sorted(lst, key=lambda x: key(x))\n", "\n", "\n", "def get_sorted_list_by_value(lst, key):\n", "    \"\"\"Sort a list in-place by a key.\"\"\"\n", "    return sorted(lst, key=lambda x: key(x, reverse=True\u001b[0m\u001b[34m\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_5.py ✗ | model=codegen-2b-fim-baseline, temp=0.0, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport dataclasses\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    age: int\n", "\u001b[0m\u001b[40m\u001b[97m    gender: str\n", "    email: str\n", "    phone: str\n", "    address: str\n", "    city: str\n", "    state: str\n", "    zip: str\n", "    country: str\n", "    birthday: str\n", "    birthplace: str\n", "    birthplace_country: str\n", "    birthplace_state: str\n", "    birth\u001b[0m\u001b[34m\n", "def get_ancestors(person: Person):\n", "    ret = []\n", "    for parent\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: resources_1.py ✗ | model=codegen-2b-fim-baseline, temp=0.0, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "def get_line_at_pct(fname, pct):\n", "    file = open(fname)\n", "    lines = sum(1 for line in file)\n", "    file.close()\n", "\n", "    line_no = int(pct * lines)\n", "\u001b[0m\u001b[40m\u001b[97m    file = open(fname)\n", "    line = file.readline()\n", "    file.close()\n", "    return line\n", "\n", "def get_line_at_pct_with_offset(fname, pct, offset):\n", "    file = open(fname)\n", "    lines = sum(1 for line in file)\n", "    file.close()\n", "\n", "    line_no = int(pct * lines) + offset\n", "    file = open(fname)\n", "    line = file.\u001b[0m\u001b[34m\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_7.py ✗ | model=codegen-2b-fim-baseline, temp=0.0, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport dataclasses\n", "import datetime\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    dob: datetime.date\n", "    profession: str\n", "\n", "    def __str__(self):\n", "\u001b[0m\u001b[40m\u001b[97m        return f\"{self.name} {self.dob} {self.profession}\"\n", "\n", "@dataclasses.dataclass\n", "class Student(Person):\n", "    pass\n", "\n", "@dataclasses.dataclass\n", "class Teacher(Person):\n", "    pass\n", "\n", "@dataclasses.dataclass\n", "class StudentAndTeacher(Person):\n", "    pass\n", "\n", "@datac\u001b[0m\u001b[34m\u001b[0m\n", "========================================================================================\n", "\n", "\n"]}], "source": ["# General behavior\n", "run_all_tests(model, \"codegen-2b-fim-baseline\", VULCAN_TESTS, temperature=0.0)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Checkpoint arun/codegen-2b-fim.11095\n", "\n", "* [Determined](https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/det/experiments/11095/)\n", "* Trained on ~250k lines of Python for 1000 steps.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model = get_model(\"codegen-2b-fim\", checkpoint_path=pathlib.Path(\"arun/codegen-2b-fim.11095\"))\n", "model.load()"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["NeoXArgs.from_ymls() [PosixPath('/mnt/efs/augment/checkpoints/arun/codegen-2b-fim.11095/config.yml')]\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:root:Trying to run with neox_args.deepspeed=False -- this is dangerous!\n"]}, {"name": "stdout", "output_type": "stream", "text": ["NeoXArgs.configure_distributed_args() using world size: 1 and model-parallel size: 1 \n", "> building CodeGenTokenizer tokenizer ...\n", " > padded vocab (size: 50327) with 873 dummy tokens (new size: 51200)\n", "> initializing torch distributed ...\n", "[2023-07-27 02:43:01,856] [INFO] [distributed.py:36:init_distributed] Not using the DeepSpeed or torch.distributed launchers, attempting to detect MPI environment...\n", "[2023-07-27 02:43:01,966] [INFO] [distributed.py:83:mpi_discovery] Discovered MPI settings of world_rank=0, local_rank=0, world_size=1, master_addr=216.153.48.143, master_port=6000\n", "[2023-07-27 02:43:01,967] [INFO] [distributed.py:46:init_distributed] Initializing torch distributed with backend: nccl\n", "> initializing model parallel with size 1\n", "MPU DP: [0]\n", "MPU PP: [0]\n", "MPU MP: [0]\n", "> setting random seeds to 1234 ...\n", "[2023-07-27 02:43:01,972] [INFO] [checkpointing.py:223:model_parallel_cuda_manual_seed] > initializing model parallel cuda seeds on global rank 0, model parallel rank 0, and data parallel rank 0 with model parallel seed: 3952 and data parallel seed: 1234\n", "make: Entering directory '/home/<USER>/augment/research/gpt-neox/megatron/data'\n", "make: Nothing to be done for 'default'.\n", "make: Leaving directory '/home/<USER>/augment/research/gpt-neox/megatron/data'\n", "building GPT2 model ...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[arun-dev:89106] mca_base_component_repository_open: unable to open mca_op_avx: /usr/local/openmpi-4.1.0/lib/openmpi/mca_op_avx.so: undefined symbol: ompi_op_base_module_t_class (ignored)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["========================================================================================\n", "MODEL: codegen-2b-fim.11095\n", "----------------------------------------------------------------------------------------\n", "========================================================================================\n", "EXAMPLE: fim_leading_spaces.py ✗ | model=codegen-2b-fim.11095, temp=0.2, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mdef hello_world():\n", "\u001b[0m\u001b[40m\u001b[97m    print(\"Hello World!\")\n", "\n", "hello_world()\n", "\u001b[0m\u001b[34mprint(\"Hello World!\")\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_leading_spaces.py ✗ | model=codegen-2b-fim.11095, temp=0.2, sample=1\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mdef hello_world():\n", "\u001b[0m\u001b[40m\u001b[97m    print(\"Hello World!\")\n", "\n", "hello_world()\n", "\u001b[0m\u001b[34mprint(\"Hello World!\")\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_leading_spaces.py ✗ | model=codegen-2b-fim.11095, temp=0.2, sample=2\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mdef hello_world():\n", "\u001b[0m\u001b[40m\u001b[97m    print(\"Hello World!\")\n", "\n", "hello_world()\n", "\u001b[0m\u001b[34mprint(\"Hello World!\")\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_docstring.py ✗ | model=codegen-2b-fim.11095, temp=0.2, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mdef get_odd_ints(lower, upper):\n", "    \"\"\"\u001b[0m\u001b[40m\u001b[97m\n", "    Returns an array of odd integers in the range [lower, upper]\n", "    \"\"\"\n", "    return [i for i in range(lower, upper) if i % 2 == 1]\u001b[0m\u001b[34m\"\"\"\n", "    return [i for i in range(lower, upper) if i % 2 == 1]\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_docstring.py ✗ | model=codegen-2b-fim.11095, temp=0.2, sample=1\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mdef get_odd_ints(lower, upper):\n", "    \"\"\"\u001b[0m\u001b[40m\u001b[97m\n", "    Returns an array of odd integers from lower to upper.\n", "    \"\"\"\n", "    return [i for i in range(lower, upper) if i % 2 == 1]\u001b[0m\u001b[34m\"\"\"\n", "    return [i for i in range(lower, upper) if i % 2 == 1]\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_docstring.py ✗ | model=codegen-2b-fim.11095, temp=0.2, sample=2\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mdef get_odd_ints(lower, upper):\n", "    \"\"\"\u001b[0m\u001b[40m\u001b[97m\n", "    Return a list of odd integers from lower to upper.\n", "\n", "    Args:\n", "        lower: lower bound\n", "        upper: upper bound\n", "\n", "    Returns:\n", "        list of odd integers from lower to upper\n", "    \"\"\"\n", "    return [i for i in range(lower, upper) if i % 2 == 1]\u001b[0m\u001b[34m\"\"\"\n", "    return [i for i in range(lower, upper) if i % 2 == 1]\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_noop.py ✗ | model=codegen-2b-fim.11095, temp=0.2, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mdef hello_world():\u001b[0m\u001b[40m\u001b[97m\n", "    print(\"Hello World!\")\n", "    print(\"Hello World!\")\n", "    print(\"Hello World!\")\n", "    print(\"Hello World!\")\n", "    print(\"Hello World!\")\n", "    print(\"Hello World!\")\n", "    print(\"Hello World!\")\n", "    print(\"Hello World!\")\n", "    print(\"Hello World!\")\n", "    print(\"Hello World!\")\n", "    print(\"Hello World!\")\n", "    print(\"Hello World!\")\n", "    print(\"Hello World!\")\n", "    print(\"Hello World!\")\n", "    print(\"Hello World!\")\n", "    print(\"Hello World!\")\u001b[0m\u001b[34m\n", "    print(\"Hello World!\")\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_noop.py ✗ | model=codegen-2b-fim.11095, temp=0.2, sample=1\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mdef hello_world():\u001b[0m\u001b[40m\u001b[97m\n", "    print(\"Hello World!\")\n", "    print(\"Hello World!\")\n", "    print(\"Hello World!\")\n", "    print(\"Hello World!\")\n", "    print(\"Hello World!\")\n", "    print(\"Hello World!\")\n", "    print(\"Hello World!\")\n", "    print(\"Hello World!\")\n", "    print(\"Hello World!\")\n", "    print(\"Hello World!\")\n", "    print(\"Hello World!\")\n", "    print(\"Hello World!\")\n", "    print(\"Hello World!\")\n", "    print(\"Hello World!\")\n", "    print(\"Hello World!\")\n", "    print(\"Hello World!\")\u001b[0m\u001b[34m\n", "    print(\"Hello World!\")\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_noop.py ✗ | model=codegen-2b-fim.11095, temp=0.2, sample=2\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mdef hello_world():\u001b[0m\u001b[40m\u001b[97m\n", "    print(\"Hello World!\")\n", "    print(\"Hello World!\")\n", "    print(\"Hello World!\")\n", "    print(\"Hello World!\")\n", "    print(\"Hello World!\")\n", "    print(\"Hello World!\")\n", "    print(\"Hello World!\")\n", "    print(\"Hello World!\")\n", "    print(\"Hello World!\")\n", "    print(\"Hello World!\")\n", "    print(\"Hello World!\")\n", "    print(\"Hello World!\")\n", "    print(\"Hello World!\")\n", "    print(\"Hello World!\")\n", "    print(\"Hello World!\")\n", "    print(\"Hello World!\")\u001b[0m\u001b[34m\n", "    print(\"Hello World!\")\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_trailing_return_with_var.py ✗ | model=codegen-2b-fim.11095, temp=0.2, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mdef get_odd_ints(lower, upper):\n", "    results = []\u001b[0m\u001b[40m\u001b[97m\n", "    for i in range(lower, upper + 1):\n", "        if i % 2 == 0:\n", "            results.append(i)\n", "    return results\u001b[0m\u001b[34m\n", "    return results\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_trailing_return_with_var.py ✗ | model=codegen-2b-fim.11095, temp=0.2, sample=1\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mdef get_odd_ints(lower, upper):\n", "    results = []\u001b[0m\u001b[40m\u001b[97m\n", "    for i in range(lower, upper + 1):\n", "        if i % 2 == 0:\n", "            results.append(i)\n", "    return results\u001b[0m\u001b[34m\n", "    return results\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_trailing_return_with_var.py ✗ | model=codegen-2b-fim.11095, temp=0.2, sample=2\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mdef get_odd_ints(lower, upper):\n", "    results = []\u001b[0m\u001b[40m\u001b[97m\n", "    for i in range(lower, upper + 1):\n", "        if i % 2 == 0:\n", "            results.append(i)\n", "    return results\u001b[0m\u001b[34m\n", "    return results\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_multi_function_2.py ✗ | model=codegen-2b-fim.11095, temp=0.2, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport copy\n", "from dataclasses import asdict, dataclass\n", "import json\n", "import pathlib\n", "\n", "@dataclass\n", "class MyClass:\n", "    def save_to_json(self, filename):\n", "        \"\"\"Saves the dataclass instance to a JSON file.\"\"\"\n", "        with pathlib.Path(filename).open(\"w\") as f:\n", "            json.dump(asdict(self), f)\n", "\n", "    @classmethod\n", "    def load_from_json(cls, filename):\n", "        \"\"\"Loads a dataclass instance from a JSON file.\"\"\"\n", "        with pathlib.Path(filename).open(\"r\") as f:\n", "            data = json.load(f)\n", "\u001b[0m\u001b[40m\u001b[97m        return cls(**data)\u001b[0m\u001b[34m\n", "        return cls(**data)\n", "\n", "    def clone(self):\n", "        \"\"\"Returns a deep copy of the instance.\"\"\"\n", "        return copy.deepcopy(self)\n", "\n", "    def __repr__(self):\n", "        attributes = \",\\n  \".join(\n", "            f\"{key} = {value!r}\" for key, value in vars(self).items()\n", "        )\n", "        return f\"ModelInput(\\n  {attributes}\\n)\"\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_multi_function_2.py ✗ | model=codegen-2b-fim.11095, temp=0.2, sample=1\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport copy\n", "from dataclasses import asdict, dataclass\n", "import json\n", "import pathlib\n", "\n", "@dataclass\n", "class MyClass:\n", "    def save_to_json(self, filename):\n", "        \"\"\"Saves the dataclass instance to a JSON file.\"\"\"\n", "        with pathlib.Path(filename).open(\"w\") as f:\n", "            json.dump(asdict(self), f)\n", "\n", "    @classmethod\n", "    def load_from_json(cls, filename):\n", "        \"\"\"Loads a dataclass instance from a JSON file.\"\"\"\n", "        with pathlib.Path(filename).open(\"r\") as f:\n", "            data = json.load(f)\n", "\u001b[0m\u001b[40m\u001b[97m        return cls(**data)\u001b[0m\u001b[34m\n", "        return cls(**data)\n", "\n", "    def clone(self):\n", "        \"\"\"Returns a deep copy of the instance.\"\"\"\n", "        return copy.deepcopy(self)\n", "\n", "    def __repr__(self):\n", "        attributes = \",\\n  \".join(\n", "            f\"{key} = {value!r}\" for key, value in vars(self).items()\n", "        )\n", "        return f\"ModelInput(\\n  {attributes}\\n)\"\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_multi_function_2.py ✗ | model=codegen-2b-fim.11095, temp=0.2, sample=2\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport copy\n", "from dataclasses import asdict, dataclass\n", "import json\n", "import pathlib\n", "\n", "@dataclass\n", "class MyClass:\n", "    def save_to_json(self, filename):\n", "        \"\"\"Saves the dataclass instance to a JSON file.\"\"\"\n", "        with pathlib.Path(filename).open(\"w\") as f:\n", "            json.dump(asdict(self), f)\n", "\n", "    @classmethod\n", "    def load_from_json(cls, filename):\n", "        \"\"\"Loads a dataclass instance from a JSON file.\"\"\"\n", "        with pathlib.Path(filename).open(\"r\") as f:\n", "            data = json.load(f)\n", "\u001b[0m\u001b[40m\u001b[97m            return cls(**data)\u001b[0m\u001b[34m\n", "        return cls(**data)\n", "\n", "    def clone(self):\n", "        \"\"\"Returns a deep copy of the instance.\"\"\"\n", "        return copy.deepcopy(self)\n", "\n", "    def __repr__(self):\n", "        attributes = \",\\n  \".join(\n", "            f\"{key} = {value!r}\" for key, value in vars(self).items()\n", "        )\n", "        return f\"ModelInput(\\n  {attributes}\\n)\"\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_argparse.py ✗ | model=codegen-2b-fim.11095, temp=0.2, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34<PERSON><PERSON><PERSON> argparse\n", "\n", "parser = argparse.ArgumentParser()\n", "parser.add_argument(\n", "    \"--model\",\n", "    type=str,\n", "    default=\"my_model\",\n", "    help=\"which model to serve\",\n", ")\n", "parser.add_argument(\n", "    \"--disable_completions\",\n", "    action=\"store_true\",\n", "\u001b[0m\u001b[40m\u001b[97m    help=\"disable completion requests\",\n", ")\n", "parser.add_argument(\n", "    \"--single_line_completions\",\n", "    action=\"store_true\",\n", "    help=\"whether to return single-line completions\",\n", ")\n", "args = parser.parse_args()\n", "print(args)\n", "\u001b[0m\u001b[34m    help=\"disable completion requests\",\n", ")\n", "parser.add_argument(\n", "    \"--single_line_completions\",\n", "    action=\"store_true\",\n", "    help=\"whether to return single-line completions\",\n", ")\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_argparse.py ✗ | model=codegen-2b-fim.11095, temp=0.2, sample=1\n", "----------------------------------------------------------------------------------------\n", "\u001b[34<PERSON><PERSON><PERSON> argparse\n", "\n", "parser = argparse.ArgumentParser()\n", "parser.add_argument(\n", "    \"--model\",\n", "    type=str,\n", "    default=\"my_model\",\n", "    help=\"which model to serve\",\n", ")\n", "parser.add_argument(\n", "    \"--disable_completions\",\n", "    action=\"store_true\",\n", "\u001b[0m\u001b[40m\u001b[97m    help=\"disable completion requests\",\n", ")\n", "parser.add_argument(\n", "    \"--single_line_completions\",\n", "    action=\"store_true\",\n", "    help=\"whether to return single-line completions\",\n", ")\n", "args = parser.parse_args()\n", "print(args)\n", "\u001b[0m\u001b[34m    help=\"disable completion requests\",\n", ")\n", "parser.add_argument(\n", "    \"--single_line_completions\",\n", "    action=\"store_true\",\n", "    help=\"whether to return single-line completions\",\n", ")\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_argparse.py ✗ | model=codegen-2b-fim.11095, temp=0.2, sample=2\n", "----------------------------------------------------------------------------------------\n", "\u001b[34<PERSON><PERSON><PERSON> argparse\n", "\n", "parser = argparse.ArgumentParser()\n", "parser.add_argument(\n", "    \"--model\",\n", "    type=str,\n", "    default=\"my_model\",\n", "    help=\"which model to serve\",\n", ")\n", "parser.add_argument(\n", "    \"--disable_completions\",\n", "    action=\"store_true\",\n", "\u001b[0m\u001b[40m\u001b[97m    help=\"disable completion requests\",\n", ")\n", "parser.add_argument(\n", "    \"--single_line_completions\",\n", "    action=\"store_true\",\n", "    help=\"whether to return single-line completions\",\n", ")\n", "args = parser.parse_args()\n", "print(args)\n", "\u001b[0m\u001b[34m    help=\"disable completion requests\",\n", ")\n", "parser.add_argument(\n", "    \"--single_line_completions\",\n", "    action=\"store_true\",\n", "    help=\"whether to return single-line completions\",\n", ")\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_multi_function_1.py ✗ | model=codegen-2b-fim.11095, temp=0.2, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport copy\n", "from dataclasses import asdict, dataclass\n", "import json\n", "import pathlib\n", "\n", "@dataclass\n", "class MyClass:\n", "    def save_to_json(self, filename):\n", "        \"\"\"Saves the dataclass instance to a JSON file.\"\"\"\n", "        with pathlib.Path(filename).open(\"w\") as f:\n", "            json.dump(asdict(self), f)\n", "\n", "    @classmethod\n", "    def load_from_json(cls, filename):\n", "        \"\"\"Loads a dataclass instance from a JSON file.\"\"\"\n", "        with pathlib.Path(filename).open(\"r\") as f:\n", "            data = json.load(f)\n", "\n", "        return cls(**data)\n", "\n", "    def clone(self):\n", "        \"\"\"Returns a deep copy of the instance.\"\"\"\n", "\u001b[0m\u001b[40m\u001b[97m        return copy.deepcopy(self)\u001b[0m\u001b[34m        return copy.deepcopy(self)\n", "\n", "    def __repr__(self):\n", "        attributes = \",\\n  \".join(\n", "            f\"{key} = {value!r}\" for key, value in vars(self).items()\n", "        )\n", "        return f\"ModelInput(\\n  {attributes}\\n)\"\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_multi_function_1.py ✗ | model=codegen-2b-fim.11095, temp=0.2, sample=1\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport copy\n", "from dataclasses import asdict, dataclass\n", "import json\n", "import pathlib\n", "\n", "@dataclass\n", "class MyClass:\n", "    def save_to_json(self, filename):\n", "        \"\"\"Saves the dataclass instance to a JSON file.\"\"\"\n", "        with pathlib.Path(filename).open(\"w\") as f:\n", "            json.dump(asdict(self), f)\n", "\n", "    @classmethod\n", "    def load_from_json(cls, filename):\n", "        \"\"\"Loads a dataclass instance from a JSON file.\"\"\"\n", "        with pathlib.Path(filename).open(\"r\") as f:\n", "            data = json.load(f)\n", "\n", "        return cls(**data)\n", "\n", "    def clone(self):\n", "        \"\"\"Returns a deep copy of the instance.\"\"\"\n", "\u001b[0m\u001b[40m\u001b[97m        return copy.deepcopy(self)\u001b[0m\u001b[34m        return copy.deepcopy(self)\n", "\n", "    def __repr__(self):\n", "        attributes = \",\\n  \".join(\n", "            f\"{key} = {value!r}\" for key, value in vars(self).items()\n", "        )\n", "        return f\"ModelInput(\\n  {attributes}\\n)\"\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_multi_function_1.py ✗ | model=codegen-2b-fim.11095, temp=0.2, sample=2\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport copy\n", "from dataclasses import asdict, dataclass\n", "import json\n", "import pathlib\n", "\n", "@dataclass\n", "class MyClass:\n", "    def save_to_json(self, filename):\n", "        \"\"\"Saves the dataclass instance to a JSON file.\"\"\"\n", "        with pathlib.Path(filename).open(\"w\") as f:\n", "            json.dump(asdict(self), f)\n", "\n", "    @classmethod\n", "    def load_from_json(cls, filename):\n", "        \"\"\"Loads a dataclass instance from a JSON file.\"\"\"\n", "        with pathlib.Path(filename).open(\"r\") as f:\n", "            data = json.load(f)\n", "\n", "        return cls(**data)\n", "\n", "    def clone(self):\n", "        \"\"\"Returns a deep copy of the instance.\"\"\"\n", "\u001b[0m\u001b[40m\u001b[97m        return copy.deepcopy(self)\n", "\u001b[0m\u001b[34m        return copy.deepcopy(self)\n", "\n", "    def __repr__(self):\n", "        attributes = \",\\n  \".join(\n", "            f\"{key} = {value!r}\" for key, value in vars(self).items()\n", "        )\n", "        return f\"ModelInput(\\n  {attributes}\\n)\"\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_dataclass_methods.py ✗ | model=codegen-2b-fim.11095, temp=0.2, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mfrom dataclasses import dataclass, field\n", "from typing import *\n", "\n", "@dataclass\n", "class Patch:\n", "    \"\"\"Represents a patch to a file using its character span.\"\"\"\n", "\n", "    file_content: str = \"\"\n", "    \"\"\"The file contents being modified. text being patched. Assumes UTF-8 encoding.\"\"\"\n", "    char_start: int = 0\n", "    \"\"\"Character offset for the patch.\"\"\"\n", "    char_end: int = 0\n", "    \"\"\"character offset for the patch.\"\"\"\n", "    patch_content: str = \"\"\n", "    \"\"\"the patch content.\"\"\"\n", "    patch_id: str = \"\"\n", "\n", "    @classmethod\n", "    def for_span(\n", "        cls,\n", "        file_content: str,\n", "        char_start: int,\n", "        char_end: int,\n", "\u001b[0m\u001b[40m\u001b[97m        patch_content: str,\n", "        **kwargs,\n", "    ):\n", "        \"\"\"Create a patch for the region [char_start, char_end) in file contents.\"\"\"\n", "        return cls(\n", "            file_content=file_content,\n", "            char_start=char_start,\n", "            char_end=char_end,\n", "            patch_content=patch_content,\n", "            **kwargs,\n", "        )\u001b[0m\u001b[34m        **kwargs,\n", "    ):\n", "        \"\"\"create a patch for the region [char_start, char_end) in file contents.\"\"\"\n", "        return cls(\n", "            file_content=file_content,\n", "            char_start=char_start,\n", "            char_end=char_end,\n", "            patch_content=file_content[char_start:char_end],\n", "            **kwargs,\n", "        )\n", "\n", "    @classmethod\n", "    def empty(cls, file_content: str, **kwargs):\n", "        \"\"\"Create an empty Patch for a file.\"\"\"\n", "        return Patch(\n", "            file_content=file_content,\n", "            char_start=0,\n", "            char_end=0,\n", "            patch_content=\"\",\n", "            **kwargs,\n", "        )\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_dataclass_methods.py ✗ | model=codegen-2b-fim.11095, temp=0.2, sample=1\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mfrom dataclasses import dataclass, field\n", "from typing import *\n", "\n", "@dataclass\n", "class Patch:\n", "    \"\"\"Represents a patch to a file using its character span.\"\"\"\n", "\n", "    file_content: str = \"\"\n", "    \"\"\"The file contents being modified. text being patched. Assumes UTF-8 encoding.\"\"\"\n", "    char_start: int = 0\n", "    \"\"\"Character offset for the patch.\"\"\"\n", "    char_end: int = 0\n", "    \"\"\"character offset for the patch.\"\"\"\n", "    patch_content: str = \"\"\n", "    \"\"\"the patch content.\"\"\"\n", "    patch_id: str = \"\"\n", "\n", "    @classmethod\n", "    def for_span(\n", "        cls,\n", "        file_content: str,\n", "        char_start: int,\n", "        char_end: int,\n", "\u001b[0m\u001b[40m\u001b[97m        **kwargs,\n", "    ):\n", "        \"\"\"Create a patch for the region [char_start, char_end) in file contents.\"\"\"\n", "        return cls(\n", "            file_content=file_content,\n", "            char_start=char_start,\n", "            char_end=char_end,\n", "            patch_content=file_content[char_start:char_end],\n", "            **kwargs,\n", "        )\u001b[0m\u001b[34m        **kwargs,\n", "    ):\n", "        \"\"\"create a patch for the region [char_start, char_end) in file contents.\"\"\"\n", "        return cls(\n", "            file_content=file_content,\n", "            char_start=char_start,\n", "            char_end=char_end,\n", "            patch_content=file_content[char_start:char_end],\n", "            **kwargs,\n", "        )\n", "\n", "    @classmethod\n", "    def empty(cls, file_content: str, **kwargs):\n", "        \"\"\"Create an empty Patch for a file.\"\"\"\n", "        return Patch(\n", "            file_content=file_content,\n", "            char_start=0,\n", "            char_end=0,\n", "            patch_content=\"\",\n", "            **kwargs,\n", "        )\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_dataclass_methods.py ✗ | model=codegen-2b-fim.11095, temp=0.2, sample=2\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mfrom dataclasses import dataclass, field\n", "from typing import *\n", "\n", "@dataclass\n", "class Patch:\n", "    \"\"\"Represents a patch to a file using its character span.\"\"\"\n", "\n", "    file_content: str = \"\"\n", "    \"\"\"The file contents being modified. text being patched. Assumes UTF-8 encoding.\"\"\"\n", "    char_start: int = 0\n", "    \"\"\"Character offset for the patch.\"\"\"\n", "    char_end: int = 0\n", "    \"\"\"character offset for the patch.\"\"\"\n", "    patch_content: str = \"\"\n", "    \"\"\"the patch content.\"\"\"\n", "    patch_id: str = \"\"\n", "\n", "    @classmethod\n", "    def for_span(\n", "        cls,\n", "        file_content: str,\n", "        char_start: int,\n", "        char_end: int,\n", "\u001b[0m\u001b[40m\u001b[97m        **kwargs,\n", "    ):\n", "        \"\"\"Create a patch for the region [char_start, char_end) in file contents.\"\"\"\n", "        return cls(\n", "            file_content=file_content,\n", "            char_start=char_start,\n", "            char_end=char_end,\n", "            patch_content=file_content[char_start:char_end],\n", "            **kwargs,\n", "        )\u001b[0m\u001b[34m        **kwargs,\n", "    ):\n", "        \"\"\"create a patch for the region [char_start, char_end) in file contents.\"\"\"\n", "        return cls(\n", "            file_content=file_content,\n", "            char_start=char_start,\n", "            char_end=char_end,\n", "            patch_content=file_content[char_start:char_end],\n", "            **kwargs,\n", "        )\n", "\n", "    @classmethod\n", "    def empty(cls, file_content: str, **kwargs):\n", "        \"\"\"Create an empty Patch for a file.\"\"\"\n", "        return Patch(\n", "            file_content=file_content,\n", "            char_start=0,\n", "            char_end=0,\n", "            patch_content=\"\",\n", "            **kwargs,\n", "        )\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_dataclass.py ✗ | model=codegen-2b-fim.11095, temp=0.2, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mfrom dataclasses import dataclass, field\n", "from typing import *\n", "\n", "@dataclass\n", "class Datum:\n", "    \"\"\"Represents a blob of data.\"\"\"\n", "\n", "    source: str = \"\"\n", "    \"\"\"Where the data comes from.\"\"\"\n", "    char_start: int = 0\n", "    \"\"\"Character offset for the blob.\"\"\"\n", "    char_end: int = 0\n", "    \"\"\"Character offset for the blob.\"\"\"\n", "    _extra: Mapping = field(default_factory=dict, repr=True, init=True\u001b[0m\u001b[40m\u001b[97m)\n", "\n", "    def __post_init__(self):\n", "        self._extra = {}\n", "\n", "    def __repr__(self):\n", "        return f\"Datum(source={self.source}, char_start={self.char_start}, char_end={self.char_end}, extra={self._extra})\"\n", "\n", "    def __str__(self):\n", "        return self.__repr__()\n", "\n", "    def __eq__(self, other):\n", "        return (self.source == other.source) and (self.char_start == other.char_start) and (\u001b[0m\u001b[34m)\n", "    \"\"\"A handy field to store extra data.\"\"\"\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_dataclass.py ✗ | model=codegen-2b-fim.11095, temp=0.2, sample=1\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mfrom dataclasses import dataclass, field\n", "from typing import *\n", "\n", "@dataclass\n", "class Datum:\n", "    \"\"\"Represents a blob of data.\"\"\"\n", "\n", "    source: str = \"\"\n", "    \"\"\"Where the data comes from.\"\"\"\n", "    char_start: int = 0\n", "    \"\"\"Character offset for the blob.\"\"\"\n", "    char_end: int = 0\n", "    \"\"\"Character offset for the blob.\"\"\"\n", "    _extra: Mapping = field(default_factory=dict, repr=True, init=True\u001b[0m\u001b[40m\u001b[97m)\n", "    \"\"\"Extra data.\"\"\"\n", "\n", "    def __post_init__(self):\n", "        self._extra = {}\n", "\n", "@dataclass\n", "class DatumList:\n", "    \"\"\"Represents a list of datums.\"\"\"\n", "\n", "    datums: List[Datum] = field(default_factory=list)\n", "    \"\"\"The datums in the list.\"\"\"\n", "\n", "    def __post_init__(self):\n", "        self.datums = []\n", "\n", "@dataclass\n", "class DatumSet:\n", "    \"\"\"Represents a set of datums.\"\"\"\n", "\n", "    datums\u001b[0m\u001b[34m)\n", "    \"\"\"A handy field to store extra data.\"\"\"\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_dataclass.py ✗ | model=codegen-2b-fim.11095, temp=0.2, sample=2\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mfrom dataclasses import dataclass, field\n", "from typing import *\n", "\n", "@dataclass\n", "class Datum:\n", "    \"\"\"Represents a blob of data.\"\"\"\n", "\n", "    source: str = \"\"\n", "    \"\"\"Where the data comes from.\"\"\"\n", "    char_start: int = 0\n", "    \"\"\"Character offset for the blob.\"\"\"\n", "    char_end: int = 0\n", "    \"\"\"Character offset for the blob.\"\"\"\n", "    _extra: Mapping = field(default_factory=dict, repr=True, init=True\u001b[0m\u001b[40m\u001b[97m)\n", "    \"\"\"Extra data.\"\"\"\n", "    extra: Mapping = field(default_factory=dict, repr=True, init=True)\n", "    \"\"\"Extra data.\"\"\"\n", "\n", "    def __post_init__(self):\n", "        self.extra = self._extra\n", "        self.extra.update(self.extra)\n", "        self.extra.update(self.extra)\n", "        self.extra.update(self.extra)\n", "        self.extra.update(self.extra)\n", "        self.extra.update(self.extra)\n", "        self.extra.update(self\u001b[0m\u001b[34m)\n", "    \"\"\"A handy field to store extra data.\"\"\"\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_trailing_return.py ✗ | model=codegen-2b-fim.11095, temp=0.2, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mdef get_odd_ints(lower, upper):\n", "\u001b[0m\u001b[40m\u001b[97m    \"\"\"\n", "    Returns an array of odd integers in the range [lower, upper].\n", "    \"\"\"\n", "    return [i for i in range(lower, upper, 2) if i % 2 == 1]\u001b[0m\u001b[34m\n", "    return results\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_trailing_return.py ✗ | model=codegen-2b-fim.11095, temp=0.2, sample=1\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mdef get_odd_ints(lower, upper):\n", "\u001b[0m\u001b[40m\u001b[97m    \"\"\"\n", "    Returns a list of odd integers between the given lower and upper bounds.\n", "    \"\"\"\n", "    return [i for i in range(lower, upper, 2) if i % 2 == 1]\u001b[0m\u001b[34m\n", "    return results\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_trailing_return.py ✗ | model=codegen-2b-fim.11095, temp=0.2, sample=2\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mdef get_odd_ints(lower, upper):\n", "\u001b[0m\u001b[40m\u001b[97m    \"\"\"\n", "    Returns a list of odd integers from lower to upper.\n", "    \"\"\"\n", "    return [i for i in range(lower, upper, 2)]\u001b[0m\u001b[34m\n", "    return results\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n"]}], "source": ["# FIM behavior\n", "run_all_tests(model, \"codegen-2b-fim.11095\", FIM_TESTS, n_samples=3, temperature=0.2)"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["========================================================================================\n", "MODEL: codegen-2b-fim-baseline\n", "----------------------------------------------------------------------------------------\n", "========================================================================================\n", "EXAMPLE: structure_2.cpp ✗ | model=codegen-2b-fim-baseline, temp=0.0, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "#include <vector>\n", "\n", "struct Person {\n", "    char* name;\n", "    int age;\n", "    std::vector<Person*> parents;\n", "};\n", "\n", "void get_ancestors\u001b[0m\u001b[40m\u001b[97m(Person* p, std::vector<Person*>& ancestors) {\n", "    if (p == nullptr) {\n", "        return;\n", "    }\n", "    ancestors.push_back(p);\n", "    get_ancestors(p->parents[0], ancestors);\n", "}\n", "\n", "void get_ancestors(Person* p, std::vector<Person*>& ancestors) {\n", "    if (p == nullptr) {\n", "        return;\n", "    }\n", "    ancestors.push_back(p);\n", "    get_ancestors(p->parents[0], ancestors);\n", "}\n", "\n", "void get_ancestors(Person* p, std::vector<Person*>& ancestors) {\n", "    if (p == nullptr) {\n", "        return;\n", "    }\n", "    \u001b[0m\u001b[34m\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_4.cpp ✗ | model=codegen-2b-fim-baseline, temp=0.0, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "#include <ctime>\n", "#include <iostream>\n", "#include <string>\n", "\n", "struct Person {\n", "    std::string name;\n", "    std::string profession;\n", "    std::tm dob;\n", "};\n", "\n", "std::ostream& operator<<(std::ostream& os, const Person& person) {\n", "    os << person.name << \u001b[0m\u001b[40m\u001b[97m\n", "        \" and was born on \" << std::asctime(const_cast<const std::tm*>(&person.\u001b[0m\u001b[34m << \"and was born on \" << std::asctime(&person.dob);\n", "    return os;\n", "}\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: double_free.cpp ✗ | model=codegen-2b-fim-baseline, temp=0.0, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "int main() {\n", "    int *arr = new int[10];\n", "    // Fill arr with increasing numbers\n", "    for (int i = 0; i < 10; i++) arr[i] = i;\n", "    delete arr;\n", "    // Fill arr with decreasing numbers\n", "\u001b[0m\u001b[40m\u001b[97m    for (int i = 9; i >= 0; i--) arr[i] = i;\n", "    delete arr;\n", "    // Fill arr with random numbers\n", "    for (int i = 0; i < 10; i++) arr[i] = rand() % 10;\n", "    delete arr;\n", "    // Fill arr with random\u001b[0m\u001b[34m}\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_1.cpp ✗ | model=codegen-2b-fim-baseline, temp=0.0, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "#include <vector>\n", "\n", "struct Person {\n", "    char* name;\n", "    int age;\n", "\u001b[0m\u001b[40m\u001b[97m    std::vector<const Person*> parents;\n", "};\n", "\n", "void get_ancestors(const Person& person, std::vector<const Person*\u001b[0m\u001b[34m};\n", "\n", "void get_ancestors(const Person& person, std::vector<const Person*> acc) {\n", "    acc.push_back(&person);\n", "    for (const auto* parent : person.parents) {\n", "        get_ancestors(*parent, acc);\n", "    }\n", "}\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_5.cpp ✗ | model=codegen-2b-fim-baseline, temp=0.0, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "#include <ctime>\n", "#include <iostream>\n", "#include <string>\n", "\n", "struct Person {\n", "    std::string name;\n", "    std::string profession;\n", "    std::tm dob;\n", "};\n", "\n", "std::ostream& operator<<(std::ostream& os, const Person& person) {\n", "\u001b[0m\u001b[40m\u001b[97m    os << person.name << \" \" << person.profession << \" \" << std::put_time(person.dob, \"%Y-%m-%d\");\n", "    return os;\n", "}\n", "\n", "int main() {\n", "    Person p1 = { \"<PERSON>\", \"Engineer\", std::tm{ 2018, 1, 1 } };\n", "    Person p2 = { \"<PERSON>\", \"Salesperson\", std::tm{ 2018, 2, 2 } };\n", "    Person p3 = { \"<PERSON>\", \"<PERSON>\", std::\u001b[0m\u001b[34m}\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_3.cpp ✗ | model=codegen-2b-fim-baseline, temp=0.0, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "#include <ctime>\n", "#include <iostream>\n", "#include <string>\n", "#include <vector>\n", "\n", "struct Person {\n", "    std::string name;\n", "    std::tm dob;\n", "    std::vector<Person*> parents;\n", "};\n", "\n", "int main() {\n", "    Person alice = {\n", "        \"Alice\",\n", "        {tm_mday: 1, tm_mon: 4, tm_year: 89},\n", "        {}};\n", "    int age = \u001b[0m\u001b[40m\u001b[97m\n", "        alice.dob.tm_year -\n", "        alice.parents[0]->dob.tm_year -\n", "        alice.parents[1]->dob.tm_year -\n", "        alice.parents[2]->dob.tm_year;\n", "    std::cout << \"Alice is \" << age << \" years old\" << std::end\u001b[0m\u001b[34m\n", "    std::cout << \"Alice is \" << age << \" years old\" << std::endl;\n", "\n", "    return 0;\n", "}\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_1.go ✗ | model=codegen-2b-fim-baseline, temp=0.0, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mpackage main\n", "\n", "type Person struct {\n", "\tname    string\n", "\tage     int\n", "\t\u001b[0m\u001b[40m\u001b[97mparents  []*Person\n", "\tchildren []*Person\n", "}\n", "\n", "func (\u001b[0m\u001b[34m\n", "}\n", "\n", "func get_ancestors(person *Person, acc []*Person) []*Person {\n", "\tfor _, parent := range person.parents {\n", "\t\tacc = append(acc, parent)\n", "\t\tacc = get_ancestors(parent, acc)\n", "\t}\n", "\treturn acc\n", "}\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_2.go ✗ | model=codegen-2b-fim-baseline, temp=0.0, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mpackage main\n", "\n", "type Person struct {\n", "\tname    string\n", "\tage     int\n", "\tparents []*Person\n", "}\n", "\n", "func get_ancestors\u001b[0m\u001b[40m\u001b[97m(p *Person) []*Person {\n", "\tancestors := make([]*Person, 0)\n", "\tfor _, parent := range p.parents {\n", "\t\tancestors = append(ancestors, parent)\n", "\t}\n", "\treturn ancestors\n", "}\n", "\n", "func get_ancestors_recursive(p *Person) []*Person {\n", "\tancestors := make([]*Person, 0)\n", "\tfor _, parent := range p.parents {\n", "\t\tancestors = append(ancestors, parent)\n", "\t}\n", "\treturn ancestors\n", "}\n", "\n", "func main() {\n", "\tp1 := &Person{name: \"<PERSON>\", age: 25, parents: []*Person{&Person{name: \"<PERSON>\", age: 18, parents: []*Person{&Person{name: \"\u001b[0m\u001b[34m\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_3.go ✗ | model=codegen-2b-fim-baseline, temp=0.0, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mpackage main\n", "\n", "type Person struct {\n", "\tname    string\n", "\tage     int\n", "\tparents []*Person\n", "}\n", "\n", "func get_ancestors(person *Person, acc []*Person) []*Person {\n", "\u001b[0m\u001b[40m\u001b[97m\tif person == acc[0] {\n", "\t\treturn acc\n", "\t}\n", "\treturn get_ancestors(person.parents[0], acc)\n", "}\n", "\n", "func main() {\n", "\tp1, p2, p3, p4, p5, p6, p7, p8, p9, p10, p11, p12, p13, p14, p15, p16, p17, p18, p19, p20, p21, p22, p23, p24, p25, p26, p27, p28\u001b[0m\u001b[34m}\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_4.go ✗ | model=codegen-2b-fim-baseline, temp=0.0, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mpackage main\n", "\n", "import \"sort\"\n", "\n", "type Person struct {\n", "\tname    string\n", "\tage     int\n", "}\n", "\n", "func main() {\n", "\tpeople := []Person{\n", "\t\t\tPerson{\"alice\", 60},\n", "\t\t\tPerson{\"bob\", 50},\n", "\t\t\tPerson{\"charlie\", 20},\n", "\t\t}\n", "\t// sort by age\n", "\u001b[0m\u001b[40m\u001b[97m\tsort.Slice(people, func(i, j int) bool {\n", "\t\treturn people[i].age < people[j].age\n", "\t})\n", "\t// sort by name\n", "\tsort.Slice(people, func(i, j int) bool {\n", "\t\treturn people[i].name < people[j].name\n", "\t})\n", "\t// sort by age and name\n", "\tsort.Slice(people, func(i, j int) bool {\u001b[0m\u001b[34m}\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: chunk_request.py ✗ | model=codegen-2b-fim-baseline, temp=0.0, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport math\n", "\n", "\n", "def make_chunked_requests(payload, chunk_size):\n", "    \"\"\"Chunk a payload into `chunk_size` portions and make requests for them.\n", "\n", "    Args:\n", "        payload: the payload to chunk and send.\n", "\n", "    Returns:\n", "        A list of requests; each request is a dictionary with keys:\n", "            chunk_idx: the index of the current chunk.\n", "            chunk_count: how many chunks are required in total.\n", "            chunk_payload: the chunk payload.\n", "    \"\"\"\n", "    requests = []\n", "    chunk_count = math.ceil(len(payload) / chunk_size)\n", "\u001b[0m\u001b[40m\u001b[97m    for i in range(chunk_count):\n", "        chunk_payload = payload[i * chunk_size:(i + 1) * chunk_size]\n", "        requests.append({\n", "            'chunk_idx': i,\n", "            'chunk_count': chunk_count,\n", "            'chunk_payload': chunk_payload\n", "        })\n", "    return requests\u001b[0m\u001b[34m    return requests\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: infer_preconditions.py ✗ | model=codegen-2b-fim-baseline, temp=0.0, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mdef remove_duplicates(sorted_lst):\n", "    \"\"\"Remove duplicates in-place from a sorted list.\"\"\"\n", "    if not sorted_lst:\n", "        return\n", "    write_idx = 0\n", "    for i in range(1, len(sorted_lst)):\n", "        if sorted_lst[i] != sorted_lst[write_idx]:\n", "            write_idx += 1\n", "            sorted_lst[write_idx] = sorted_lst[i]\n", "    del sorted_lst[write_idx+1:]\n", "\n", "\n", "lst = [1, 4, 2, 6, 1, 3, 4]\n", "# Print the list of unique integers in lst.\n", "\u001b[0m\u001b[40m\u001b[97mprint(list(set(lst)))\n", "\n", "# Remove duplicates from lst.\n", "remove_duplicates(lst)\n", "print(list(set(lst)))\n", "\u001b[0m\u001b[34m\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_4.py ✗ | model=codegen-2b-fim-baseline, temp=0.0, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport dataclasses\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    age: int\n", "    profession: str\n", "\n", "\n", "persons = [\n", "    Person('<PERSON>', 42, '<PERSON>'),\n", "    Person('<PERSON>', 22, '<PERSON>er'),\n", "    Person('<PERSON>', 12, ''),\n", "    Person('<PERSON>', 18, 'Entrepreneur'),\n", "]\n", "\n", "# Sort persons by age\n", "\u001b[0m\u001b[40m\u001b[97msorted_persons = sorted(persons, key=lambda p: p.age)\n", "print(sorted_persons)\n", "\n", "# Sort persons\u001b[0m\u001b[34m\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_6.py ✗ | model=codegen-2b-fim-baseline, temp=0.0, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport dataclasses\n", "import datetime\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    dob: datetime.date\n", "    profession: str\n", "\n", "    def __str__(self):\n", "        return f\"{self.name} \u001b[0m\u001b[40m\u001b[97m가 {self.dob} 에서 {self.profession}\"\n", "\u001b[0m\u001b[34m and is {self.dob.year} years old\"\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_2.py ✗ | model=codegen-2b-fim-baseline, temp=0.0, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "import dataclasses\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    age: int\n", "    parents: list['Person']\n", "\n", "\n", "def get_ancestors(person: Person):\n", "    ancestors = []\n", "\u001b[0m\u001b[40m\u001b[97m    while person.parents:\n", "        ancestor = person.parents[0]\n", "        ancestors.append(ancestor)\n", "        person = ancestor\n", "    return ancestors\n", "\n", "\n", "def get_ancestors_recursive(person: Person):\n", "    ancestors = []\n", "    while person.parents:\n", "        ancestor = person.parents[0]\n", "        ancestors.append(ancestor)\n", "        person = ancestor\n", "    return ancestors\n", "\n", "\n", "def get_ancestors_recursive_with_parents(person: Person):\n", "    ancestors = []\n", "    while person.parents:\u001b[0m\u001b[34m\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_3.py ✗ | model=codegen-2b-fim-baseline, temp=0.0, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport dataclasses\n", "import datetime\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    dob: datetime.date\n", "    profession: str\n", "\n", "\n", "alice = Person('<PERSON>', datetime.date.fromisoformat('1989-04-01'), 'Engineer')\n", "now = datetime.date.today()\n", "\u001b[0m\u001b[40m\u001b[97mprint(f\"Alice is {alice.name}\")\n", "print(f\"<PERSON>'s birthday is {alice.dob}\")\n", "print(f\"Alice's profession is {alice.profession}\")\n", "print(f\"<PERSON>'s birthday is {alice.dob}\")\n", "print(f\"Alice's profession is {alice.profession}\")\n", "print(f\"<PERSON>'s birthday is {alice.dob}\")\n", "print(f\"Alice's profession is {alice.profession}\")\n", "print(f\"<PERSON>'s birthday is {alice.dob}\")\n", "print(f\"Alice's profession is {alice.profession}\")\n", "print(\u001b[0m\u001b[34mprint(f\"<PERSON> is {age} years old!\")\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_1.py ✗ | model=codegen-2b-fim-baseline, temp=0.0, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport dataclasses\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    age: int\n", "\u001b[0m\u001b[40m\u001b[97m    parents: list[Person] = dataclasses.field(default_factory=list)\n", "    children: list[Person] = dataclasses.field(default_factory=list)\n", "    ancestors: list[Person] = dataclasses.field(default_factory=list)\n", "    desc\u001b[0m\u001b[34m\n", "\n", "def get_ancestors(person: Person):\n", "    ret = []\n", "    for parent in person.parents:\n", "        ret.append(parent)\n", "        ret.extend(get_ancestors(parent))\n", "    return ret\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: testing_1.py ✗ | model=codegen-2b-fim-baseline, temp=0.0, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "import dataclasses\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    age: int\n", "    profession: str\n", "\n", "\n", "def sort_by_age(people):\n", "    people.sort(key=lambda x: x.age)\n", "\n", "\n", "def test_sort_by_age():\n", "    p1 = Person('<PERSON>', 42, '<PERSON>')\n", "    p2 = Person('<PERSON>', 22, 'Marketer')\n", "    p3 = Person('<PERSON>', 12, '')\n", "\u001b[0m\u001b[40m\u001b[97m    p4 = Person('<PERSON>', 23, 'Engineer')\n", "    p5 = Person('<PERSON>', 18, '<PERSON>er')\n", "    p6 = Person('<PERSON>', 18, '<PERSON>er')\n", "    p7 = Person('<PERSON>', 18, '<PERSON>er')\n", "    p8 = Person('<PERSON>', 18, '<PERSON>er')\n", "    p9 = Person('<PERSON>\u001b[0m\u001b[34m\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: testing_2.py ✗ | model=codegen-2b-fim-baseline, temp=0.0, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "import dataclasses\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    age: int\n", "    profession: str\n", "\n", "\n", "def sort_by_age(people):\n", "    people.sort(key=lambda x: x.age)\n", "\n", "\n", "def test_sort_by_age():\n", "\u001b[0m\u001b[40m\u001b[97m    p1 = Person(\"<PERSON>\", 30, \"Engineer\")\n", "    p2 = Person(\"<PERSON>\", 40, \"<PERSON>\")\n", "    p3 = Person(\"<PERSON>\", 30, \"<PERSON>\")\n", "    people = [p1, p2, p3]\n", "    sort_by_age(people)\n", "    assert people == [p3, p1, p2]\n", "    p1 = Person(\"<PERSON>\", 30, \"Engineer\")\n", "    p2 = Person(\"<PERSON>\", 40, \"<PERSON>\")\n", "    p3 =\u001b[0m\u001b[34m    people = [p1, p2, p3]\n", "    sort_by_age(people)\n", "    assert people == [p3, p1, p2]\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: remove_duplicates.py ✗ | model=codegen-2b-fim-baseline, temp=0.0, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "def remove_duplicates(sorted_lst):\n", "    \"\"\"Remove duplicates in-place from a sorted list.\"\"\"\n", "    if not sorted_lst:\n", "        return\n", "\u001b[0m\u001b[40m\u001b[97m    last = sorted_lst[0]\n", "    last_index = 0\n", "    for index, item in enumerate(sorted_lst):\n", "        if item == last:\n", "            sorted_lst.pop(last_index)\n", "            last = sorted_lst[last_index + 1]\n", "            last_index += 1\n", "        else:\n", "            last = item\n", "            last_index += 1\n", "    return sorted_lst\u001b[0m\u001b[34m\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_5.py ✗ | model=codegen-2b-fim-baseline, temp=0.0, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport dataclasses\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    age: int\n", "\u001b[0m\u001b[40m\u001b[97m    parent: Person\n", "    children: list[Person] = dataclasses.field(default_factory=list)\n", "    parents: list[Person] = dataclasses.field(default_factory=list)\n", "    childrens: list[Person] = dataclasses.field(default_factory=list\u001b[0m\u001b[34m\n", "def get_ancestors(person: Person):\n", "    ret = []\n", "    for parent\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: resources_1.py ✗ | model=codegen-2b-fim-baseline, temp=0.0, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "def get_line_at_pct(fname, pct):\n", "    file = open(fname)\n", "    lines = sum(1 for line in file)\n", "    file.close()\n", "\n", "    line_no = int(pct * lines)\n", "\u001b[0m\u001b[40m\u001b[97m    line_no = line_no / 100\n", "    line_no = line_no + 1\n", "    line_no = line_no - 1\n", "\n", "    return line_no\u001b[0m\u001b[34m\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_7.py ✗ | model=codegen-2b-fim-baseline, temp=0.0, sample=0\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport dataclasses\n", "import datetime\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    dob: datetime.date\n", "    profession: str\n", "\n", "    def __str__(self):\n", "\u001b[0m\u001b[40m\u001b[97m        return f\"{self.name} {self.dob} {self.profession}\"\n", "\n", "@dataclasses.dataclass\n", "class Student:\n", "    name: str\n", "    dob: datetime.date\n", "    profession: str\n", "    school: str\n", "\n", "    def __str__(self):\n", "        return f\"{self.name} {self.dob} {\u001b[0m\u001b[34m\u001b[0m\n", "========================================================================================\n", "\n", "\n"]}], "source": ["# General behavior\n", "run_all_tests(model, \"codegen-2b-fim-baseline\", VULCAN_TESTS, temperature=0.0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model.unload()"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}