{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import json\n", "import difflib\n", "from pathlib import Path"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["COMMITPACK_FT_DIR = Path(\"/mnt/efs/augment/user/dxy/datasets/edit.raw/commitpackft/data/\")"]}, {"cell_type": "code", "execution_count": 56, "metadata": {}, "outputs": [], "source": ["with (COMMITPACK_FT_DIR / \"python/data.jsonl\").open() as f:\n", "    objs = [json.loads(line) for _, line in zip(range(1000), f)]\n"]}, {"cell_type": "code", "execution_count": 130, "metadata": {}, "outputs": [], "source": ["import requests \n", "from functools import lru_cache\n", "\n", "@lru_cache\n", "def get_patch(repo: str, commit:str) -> str:\n", "    resp = requests.get(f\"https://github.com/{repo}//commit/{commit}.patch\")\n", "    if resp.status_code == 200:\n", "        text = resp.text\n", "        return text[text.index(\"\\ndiff --git\")+1:]\n", "    else:\n", "        return \"\"\n", "\n", "def get_patch_from_obj(obj):\n", "    repos = obj[\"repos\"].split(\",\")\n", "    return get_patch(repos[0], obj[\"commit\"])"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [], "source": ["from textwrap import dedent\n", "\n", "def generate_prompt(obj):\n", "    repos = obj[\"repos\"].split(\",\")\n", "    patch = get_patch(repos[0], obj[\"commit\"])\n", "    return dedent(\n", "f\"\"\"Describe the change being made in the diff below as a single-line instruction in active voice:\n", "```\n", "{patch}\n", "```\"\"\")\n"]}, {"cell_type": "code", "execution_count": 62, "metadata": {}, "outputs": [], "source": ["z = generate_prompt(objs[431])\n", "print(z)"]}, {"cell_type": "code", "execution_count": 136, "metadata": {}, "outputs": [], "source": ["from tqdm import tqdm\n", "from concurrent.futures import ThreadPoolExecutor"]}, {"cell_type": "code", "execution_count": 138, "metadata": {}, "outputs": [], "source": ["def parse_change(obj):\n", "    patch_text = get_patch_from_obj(obj)\n", "    if patch_text:\n", "        return unidiff.PatchSet.from_string(patch_text)\n", "    else:\n", "        return None\n", "\n", "with ThreadPoolExecutor(max_workers=10) as executor:\n", "    patches = list(tqdm(executor.map(parse_change, objs)))"]}, {"cell_type": "code", "execution_count": 141, "metadata": {}, "outputs": [], "source": ["multi_patches = [(obj, p) for obj, p in zip(objs, patches) if p and len(p[0]) > 1]\n", "len(multi_patches)"]}, {"cell_type": "code", "execution_count": 181, "metadata": {}, "outputs": [], "source": ["# template = \"\"\"Here are some recent changes:\n", "# ```\n", "# {changes}```\n", "\n", "# Does the following code need to be edited based on the recent changes? If so, describe\n", "# how the code should be changed in a single-line instruction using active voice. If not,\n", "# respond with \"Nothing to be done\".\n", "# ```\n", "# {selected_code}```\"\"\"\n", "\n", "# template = \"\"\"Here are some recent changes:\n", "# ```\n", "# {changes}```\n", "\n", "# Does the following code need to be edited based on the recent changes? If so, generate\n", "# the updated code. If not, respond with \"Nothing to be done\".\n", "# ```\n", "# {selected_code}```\"\"\"\n", "\n", "template = \"\"\"Here are some recent changes:\n", "```\n", "{changes}```\n", "\n", "Keeping the recent changes in mind, describe the following changes as a single line instruction using active voice:\n", "```\n", "{next_changes}```\"\"\"\n", "\n", "\n", "def generate_instruction_from_obj(obj, patch, template):\n", "    if patch is None:\n", "        patch: unidiff.PatchedFile = unidiff.PatchSet(get_patch_from_obj(obj))\n", "    patch = patch[0]\n", "    assert len(patch) > 1\n", "    header = \"\".join(str(patch).splitlines(True)[:4])\n", "    changes = f\"{header}\\n{patch[0]}\"\n", "    next_change = patch[1]\n", "    old_lines = obj[\"old_contents\"].splitlines(keepends=True)\n", "    next_changes = (\n", "    f\"\"\"{patch.patch_info}\\n\"\"\" + str(next_change)\n", "    )\n", "    selected_code = (\n", "    f\"\"\"{patch.patch_info}\\n\"\"\" +\n", "    \"\"\"@@ -{},{} +{},{} @@\\n\"\"\".format(\n", "        next_change.source_start, next_change.source_length,\n", "        next_change.target_start, next_change.source_length,\n", "    ) +\n", "    \"\".join(old_lines[next_change.source_start:next_change.source_start + next_change.source_length])\n", "    )\n", "    return template.format(changes=changes, next_changes=next_changes, selected_code=selected_code)\n", "\n"]}, {"cell_type": "code", "execution_count": 187, "metadata": {}, "outputs": [], "source": ["obj, patch = multi_patches[10]\n", "print(generate_instruction_from_obj(obj, patch, template))"]}, {"cell_type": "code", "execution_count": 188, "metadata": {}, "outputs": [], "source": ["print(patch)"]}, {"cell_type": "code", "execution_count": 144, "metadata": {}, "outputs": [], "source": ["print(multi_patches[0][1])"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}