"""Test that the FBWD model works as expected."""

import argparse
import logging
import logging.handlers
import pathlib

import torch
from tensordict import TensorDict

from base.prompt_format_retrieve.prompt_formatter import CompletionRetrieverPromptInput
from base.tokenizers import create_tokenizer_by_name
from base.tokenizers.tokenizer import RetrievalSpecialTokens, Tokenizer
from research.core.types import Chun<PERSON>, Document
from research.fastbackward import (
    distributed,
    retrieval_data,
    retrieval_models,
    train_retriever,
)
from research.models.embedding_models import fastbackward as fastbackward_embedders
from research.retrieval.scorers.dense_scorer_v2 import (
    create_dense_scorer_from_fastbackward_checkpoint,
)

logger = logging.getLogger(__name__)


@torch.no_grad()
def run_evaluation(model: torch.nn.Module, eval_loader: torch.utils.data.DataLoader):
    model.eval()
    my_device = distributed.get_local_device()
    metrics = retrieval_models.RankingMetrics(count=0)
    for i, X in enumerate(eval_loader):
        logger.info(f"Eval iter {i}/{len(eval_loader)}: {metrics}")
        # Move the data to the model.
        X = X.to(my_device, non_blocking=True)
        Y_hat = model(X)
        metrics_ = retrieval_models.compute_ranking_metrics(X, Y_hat)
        metrics.update(metrics_)
    logger.info(f"Eval iter {len(eval_loader)}/{len(eval_loader)}: {metrics}")

    # Combine the metrics across the data parallel group.
    retrieval_models.reduce_retrieval_metrics_dp(metrics)
    model.train()

    return metrics


def test_raw_model(
    checkpoint_path: pathlib.Path,
    eval_dataset: torch.utils.data.Dataset,
    tokenizer: Tokenizer,
):
    assert isinstance(tokenizer.special_tokens, RetrievalSpecialTokens)
    eval_loader = torch.utils.data.DataLoader[TensorDict](
        eval_dataset,
        sampler=torch.utils.data.DistributedSampler(eval_dataset, shuffle=False),
        batch_size=16,
        num_workers=4,
        collate_fn=retrieval_data.create_collate_retrieval_data_fn(
            tokenizer.special_tokens.end_of_key, tokenizer.special_tokens.padding
        ),
        pin_memory=True,
    )

    # 2. Create the model.
    logger.info("Loading model...")
    model = fastbackward_embedders.load_checkpoint_v2(checkpoint_path).get_with_type(
        "model", train_retriever.retrieval_models.DualEncoderModel
    )
    # Prepare the model for evaluation.
    model.eval()
    model.requires_grad_(False)
    # fastbackward models want to be in bfloat16 and on the GPU.
    model.cuda().to(torch.bfloat16)

    # Tests raw model.
    logger.info("Running eval...")
    metrics = run_evaluation(model, eval_loader)
    logger.info(f"Evaluation metrics: {metrics}")
    print(metrics.to_json(indent=2))


def score_single_example(scorer, tokenizer: Tokenizer, example: TensorDict):
    query_str = tokenizer.detokenize(example["query_tokens_BLq"][0].tolist())
    doc_strs = [
        tokenizer.detokenize(doc) for doc in example["doc_tokens_BKLd"][0].tolist()
    ]
    scorer.remove_all_docs()

    for i, doc_str in enumerate(doc_strs):
        scorer.add_doc(
            [
                Chunk(
                    id=f"doc.{i}.0",
                    text=doc_str,
                    parent_doc=Document(id=f"doc.{i}", text=doc_str),
                    char_offset=0,
                    length=0,
                    line_offset=0,
                    length_in_lines=0,
                )
            ]
        )

    ids, scores_ = scorer.score(
        CompletionRetrieverPromptInput(
            prefix=query_str,
            suffix="",
            path="",
        )
    )
    # Re-order the scores by their indices
    assert len(ids) == len(doc_strs)
    scores = [0.0 for _ in range(len(doc_strs))]
    for (_, chunk_id), score in zip(ids, scores_):
        _, i, _ = chunk_id.split(".", 2)
        scores[int(i)] = score

    # Compute metrics based on rankings
    labels_K = example["labels_BK"][0]
    targets_K = labels_K == labels_K.max()
    logits_K = torch.tensor(scores)
    pred_lprobs_K = torch.log_softmax(logits_K, dim=-1)
    pred_ranking_K = pred_lprobs_K.argsort(dim=-1, descending=True)

    return retrieval_models.compute_ranking_metrics_from_ranking(
        pred_lprobs_K, pred_ranking_K, targets_K
    )


def test_dense_scorer(
    checkpoint_path: pathlib.Path,
    eval_dataset: torch.utils.data.Dataset[TensorDict],
    tokenizer: Tokenizer,
):
    logging.info("Creating scorer...")
    scorer = create_dense_scorer_from_fastbackward_checkpoint(
        checkpoint_path,
        "starcoder",
        "passthrough",
        "passthrough",
        max_batch_size=16,
    )
    scorer.load()

    metrics = retrieval_models.RankingMetrics(count=0)
    for i, example in enumerate(eval_dataset):
        logger.info(f"Eval iter {i}/{len(eval_dataset)}: {metrics}")
        metrics.update(score_single_example(scorer, tokenizer, example))
    logger.info(f"Eval iter {len(eval_dataset)}/{len(eval_dataset)}: {metrics}")
    return metrics


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("--checkpoint_path", type=pathlib.Path, required=True)
    parser.add_argument("--data_path", type=pathlib.Path, required=True)
    parser.add_argument("--data_limit", type=int, default=1024)
    args = parser.parse_args()

    tokenizer = create_tokenizer_by_name("starcoder")
    assert isinstance(tokenizer.special_tokens, RetrievalSpecialTokens)
    distributed.init_distributed_for_training(1)

    # 1. Load the data.
    retrieval_data.patch_pin_memory()
    logger.info("Loading dataset...")
    eval_dataset = train_retriever.load_dataset(
        train_retriever.RetrievalDataConfig(
            path=args.data_path,
            max_document_tokens=1024,
            limit=args.data_limit,
        ),
        tokenizer,
    )
    logger.info("Total eval dataset size: %s", len(eval_dataset))

    # test_raw_model(args.checkpoint_path, eval_dataset, tokenizer)
    test_dense_scorer(args.checkpoint_path, eval_dataset, tokenizer)


if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.INFO)
    root_logger.addHandler(logging.StreamHandler())
    main()
