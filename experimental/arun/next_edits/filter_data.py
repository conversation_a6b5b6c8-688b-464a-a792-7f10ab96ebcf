"""Script to filter data for next edits project."""

from pathlib import Path

import numpy as np
import torch
from megatron.data import indexed_dataset
from tqdm import tqdm

from base.tokenizers import StarCoderSpecialTokens, create_tokenizer_by_name

INPUT_PATH = "/mnt/efs/augment/user/guy/data-pipeline/pr-next-edit-location-for-arun/next-edit-location-indexed-dataset/dataset"
OUTPUT_PATH = "/mnt/efs/augment/user/guy/data-pipeline/pr-next-edit-location-for-arun/next-edit-location-filtered-dataset/dataset"


def extract_scores(tokenizer, seq: list[int], key_id: int):
    ret = []
    while seq:
        start_delimiter = seq.index(key_id)
        end_delimiter = seq.index(key_id, start_delimiter + 1)
        ret.append(
            float(tokenizer.detokenize(seq[start_delimiter + 1 : end_delimiter]))
        )
        seq = seq[end_delimiter + 1 :]
    return ret


def main():
    tokenizer = create_tokenizer_by_name("starcoder")
    assert isinstance(tokenizer.special_tokens, StarCoderSpecialTokens)

    ds = indexed_dataset.make_dataset(str(INPUT_PATH), impl="mmap", skip_warmup=True)
    assert ds is not None

    Path(OUTPUT_PATH).parent.mkdir(parents=True, exist_ok=True)

    builder = indexed_dataset.make_builder(
        f"{OUTPUT_PATH}.bin",
        impl="mmap",
        vocab_size=tokenizer.vocab_size,
    )

    empty_query_count = 0
    empty_positive_count = 0
    for ex in tqdm(ds):  # type: ignore
        if ex[0] == tokenizer.special_tokens.end_of_query:
            # Skipping empty queries
            empty_query_count += 1
            continue
        scores = extract_scores(
            tokenizer, ex.tolist(), tokenizer.special_tokens.end_of_key
        )
        if not any(score == 0 for score in scores):
            empty_positive_count += 1
            continue

        builder.add_item(ex)
        builder.end_document()

    builder.finalize(
        f"{OUTPUT_PATH}.idx",
    )

    print(f"{empty_query_count=} {empty_positive_count=}")


if __name__ == "__main__":
    main()
