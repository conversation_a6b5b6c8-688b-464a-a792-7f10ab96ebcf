"""Generate a data viewer based on the output from the next edit task.

Example:
python3 experimental/arun/next_edits/generate_viewer.py -ds manual.v3 -i /mnt/efs/augment/eval/next_edits/manual.v3-starethanol-2024-07-18 -p
"""

import argparse
import functools
import json
import logging
import pathlib
import re
from concurrent.futures import ProcessPoolExecutor
from itertools import groupby

import jinja2
import torch
from pygments import highlight
from pygments.formatters import HtmlFormatter  # pylint:disable=no-name-in-module
from pygments.lexers import get_lexer_by_name, guess_lexer, guess_lexer_for_filename
from pygments.util import ClassNotFound
from tqdm import tqdm

from base.ranges.range_types import LineRange
from research.core.next_edit_location_prompt_input import FileLocation, PathAndDiff
from research.core.utils_for_log import setup_logging
from research.eval.harness.tasks import next_edit_location_eval_task
from research.eval.harness.tasks.next_edit_location_eval_task import (
    Output,
    TaskInput,
    load_dataset,
    load_output,
)
from research.fastbackward import (
    retrieval_models,
)
from research.next_edits.next_edits_dataset import Datum

logger = logging.getLogger(__name__)


DATASETS = {
    "manual.v3": (
        "/mnt/efs/augment/data/eval/next_edits/manual.v3.diffs.jsonl.zst",
        "/mnt/efs/augment/data/eval/next_edits/manual.v3.files.jsonl.zst",
    )
}


@functools.lru_cache
def load_template(template_path: pathlib.Path) -> jinja2.Template:
    """Load a jinja template from a local file."""
    with template_path.open() as f:
        template_str = f.read()

    return jinja2.Template(template_str)


def score_example(datum: TaskInput, output: Output):
    assert datum.prompt.label is not None

    targets_K = torch.tensor(
        [
            any(
                location.item.path == target_location.path
                and location.item.range.intersect(target_location.range) is not None
                for target_location in datum.prompt.label.locations
            )
            for location in output.scored_candidates
        ]
    )
    if not any(targets_K):
        return retrieval_models.RankingMetrics(count=0), targets_K

    scores_K = [location.score for location in output.scored_candidates]

    logits_K = torch.tensor(scores_K)
    pred_lprobs_K = torch.log_softmax(logits_K, dim=-1)
    pred_ranking_K = pred_lprobs_K.argsort(dim=-1, descending=True)

    metrics = retrieval_models.compute_ranking_metrics_from_ranking(
        pred_lprobs_K, pred_ranking_K, targets_K
    )

    return metrics, targets_K.cpu().tolist()


_HUNK_HEADER_RE = re.compile(r"^@@ -(\d+),(\d+) \+(\d+),(\d+) @@")


def render_diff(hunk: PathAndDiff, style: str = "dark"):
    pygments_style = {"dark": "monokai", "light": "default"}[style]

    # Approximate how many header lines are there.
    header_lines = 0
    for line in hunk.diff_text.splitlines():
        header_lines += 1
        if m := _HUNK_HEADER_RE.match(line):
            line_start = int(m.group(1))
            break
    else:
        line_start = 0

    lexer = get_lexer_by_name("diff")
    formatter = HtmlFormatter(
        style=pygments_style,
        linenos="inline",
        linenostart=line_start - header_lines,
    )
    return highlight(hunk.diff_text, lexer, formatter)


def get_styles(style: str = "dark"):
    pygments_style = {"dark": "monokai", "light": "default"}[style]
    formatter = HtmlFormatter(style=pygments_style, linenos="inline")
    return formatter.get_style_defs(".highlight")


def render_example(
    system: str,
    title: str,
    datum: Datum,
    output: Output,
    template: jinja2.Template,
):
    """Render a single example."""
    task_input = next_edit_location_eval_task.convert_to_task_input(datum)
    assert task_input and task_input.prompt.label is not None

    future_hunk_location = [
        (
            hunk,
            min(
                (
                    i
                    for i, location in enumerate(output.scored_candidates)
                    if location.item.intersect(
                        FileLocation(
                            hunk.path,
                            LineRange(
                                hunk.diff_hunk.source_start,
                                hunk.diff_hunk.source_start
                                + hunk.diff_hunk.source_length,
                            ),
                        )
                    )
                    is not None
                ),
                default=-1,
            ),
        )
        for hunk in task_input.prompt.future_hunks
    ]

    candidate_location_correct = {
        location.item: label
        for location, label in zip(
            output.scored_candidates, output.scored_candidate_labels
        )
    }

    return template.render(
        system=system,
        title=title,
        metrics=output.metrics,
        datum=task_input,
        output=output,
        future_hunk_location=future_hunk_location,
        candidate_location_correct=candidate_location_correct,
        styles=get_styles(),
        render_diff=render_diff,
        render_location=render_location,
        label_icons={True: "✅︎", False: "❌"},
        render_location_cutoff=32,
    )


def render_location(
    datum: TaskInput, location: FileLocation, n_context: int = 0
) -> str:
    # 1. Find the relevant document in the dataset.
    document = next(
        (document for document in datum.documents if document.path == location.path),
        None,
    )
    if not document:
        return f"(missing document {location})"
    lines = document.text.splitlines(True)
    # 2. Find the relevant code in the document.
    line_start = max(0, location.range.start - n_context)

    code = "".join(lines[line_start : location.range.stop + n_context])
    # 3. Highlight the code.
    try:
        lexer = guess_lexer_for_filename(location.path, code)
    except ClassNotFound:
        lexer = guess_lexer(code)

    formatter = HtmlFormatter(
        style="monokai",
        linenos="inline",
        linenostart=line_start + 1,
        hl_lines=list(
            range(
                location.range.start - line_start + 1,
                location.range.stop - line_start + 1,
            )
        ),
    )
    return highlight(code, lexer, formatter)


def render_index(
    system: str,
    metrics: dict[str, dict[str, retrieval_models.RankingMetrics]],
    dataset: list[next_edit_location_eval_task.Datum],
    outputs: list[next_edit_location_eval_task.Output],
    top_k: int,
):
    template = get_template(pathlib.Path(__file__).parent / "index.html")
    dataset_by_key = {
        (output.repo_name, output.group_id): datum
        for datum, output in zip(dataset, outputs)
    }
    grouped_outputs = [
        (dataset_by_key[key], list(group))
        for key, group in groupby(
            outputs,
            lambda output: (output.repo_name, output.group_id),
        )
    ]

    return template.render(
        system=system,
        metrics=metrics,
        grouped_outputs=grouped_outputs,
        top_k=top_k,
    )


@functools.lru_cache
def get_template(path: pathlib.Path):
    logger.info("Loading template from %s...", path)
    return load_template(path)


def save_example(
    system: str, title: str, datum: Datum, output: Output, output_path: pathlib.Path
):
    example_template = get_template(pathlib.Path(__file__).parent / "example.html")
    html = render_example(system, title, datum, output, example_template)
    (output_path).write_text(html)


def main():
    setup_logging()

    parser = argparse.ArgumentParser()
    parser.add_argument(
        "-i",
        "--input_paths",
        nargs="+",
        type=pathlib.Path,
        required=True,
        help="Path to one or more experiment output directories.",
    )
    parser.add_argument(
        "-o",
        "--output_root",
        type=pathlib.Path,
        default=pathlib.Path("/mnt/efs/augment/public_html/next_edit_location/evals/"),
        help="Path to output root directory.",
    )
    parser.add_argument(
        "-ds",
        "--dataset",
        choices=DATASETS.keys(),
        help="The dataset to use.",
    )
    parser.add_argument(
        "-d",
        "--diffs_path",
        type=pathlib.Path,
        help="Path to the diffs.",
    )
    parser.add_argument(
        "-f",
        "--files_path",
        type=pathlib.Path,
        help="Path to the files.",
    )
    parser.add_argument(
        "-l",
        "--limit",
        type=int,
        help="If set, only load these many entries.",
    )
    parser.add_argument(
        "-p",
        "--parallelize",
        action="store_true",
        help="If set, parallelize the generation of examples.",
    )
    parser.add_argument(
        "--index-only",
        action="store_true",
        help="If set, only generate the index.",
    )
    parser.add_argument(
        "--top-k",
        type=int,
        default=32,
        help="If set, only generate the index.",
    )
    args = parser.parse_args()

    if args.dataset is not None:
        args.diffs_path, args.files_path = DATASETS[args.dataset]
    assert (
        args.diffs_path is not None and args.files_path is not None
    ), "You must specify either --dataset or --diffs_path and --files_path"

    logger.info("Loading dataset...")
    dataset = [datum for datum in load_dataset(args.diffs_path, args.files_path)]

    for input_path in args.input_paths:
        artifact_path = next(input_path.glob("*.jsonl.zst"))
        metrics_path = next(input_path.glob("*results.jsonl"))
        output_path = args.output_root / input_path.name
        system = input_path.name

        logger.info("Loading metrics and output from %s...", artifact_path)
        metrics = next_edit_location_eval_task.OutputMetrics.schema().load(
            json.loads(metrics_path.read_text()),
        )
        outputs = load_output(artifact_path, limit=args.limit)

        logger.info("Generating index...")
        output_path.mkdir(parents=True, exist_ok=True)
        html = render_index(system, metrics, dataset, outputs, args.top_k)
        (output_path / "index.html").write_text(html)

        if args.index_only:
            logger.info("Skipping generating examples...")
            return

        logger.info("Generating examples...")
        if not args.parallelize:
            for datum, output in tqdm(zip(dataset, outputs), desc="Saving examples"):
                save_example(
                    system,
                    f"Example {datum.group_id}/{datum.group_sequence_id}",
                    datum,
                    output,
                    output_path
                    / f"example-{datum.group_id}.{datum.group_sequence_id}.html",
                )
        else:
            futures = []
            with ProcessPoolExecutor() as executor:
                for datum, output in zip(dataset, outputs):
                    futures.append(
                        executor.submit(
                            save_example,
                            system,
                            f"Example {datum.group_id}/{output.group_sequence_id}",
                            datum,
                            output,
                            output_path
                            / f"example-{datum.group_id}.{output.group_sequence_id}.html",
                        )
                    )
                for future in tqdm(futures, total=len(futures)):
                    future.result()


if __name__ == "__main__":
    main()
