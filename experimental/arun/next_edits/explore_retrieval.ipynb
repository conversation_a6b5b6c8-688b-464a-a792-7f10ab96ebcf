{"cells": [{"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["import pickle\n", "from pathlib import Path\n", "\n", "import pandas\n", "\n", "from base.prompt_format_retrieve.prompt_formatter import (\n", "    CompletionRetrieverPromptInput,\n", "    DocumentRetrieverPromptInput,\n", ")\n", "from research.core.types import Document\n", "from research.next_edits.edit_gen_stages import (\n", "    EditGenProblem,\n", "    EditRetrieverPrompt<PERSON><PERSON><PERSON>er,\n", ")\n", "from research.retrieval.scorers.dense_scorer_v2 import DenseRetrievalScorerV2\n", "from research.data.spark.pipelines.stages import next_edit_location_pipelines"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Load edit data from pipeline.\n", "\n", "We load data from the first stage of the next-edit generation pipeline, which sets up\n", "context with \"past to wip\" and \"wip to future\" diffs in an `EditGenProblem`."]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["import difflib\n", "DATA_ROOT = Path(\n", "    \"/mnt/efs/spark-data/shared/next-edit/stage1/gh_pr_train_repartitioned/S1.13_6000p_2000f\"\n", ")\n", "PROBLEM_COUNT = 100\n", "\n", "def changed_lines(problem: EditGenProblem) -> int:\n", "    return sum(1 for line in difflib.unified_diff(\n", "        problem.selected_code.splitlines(keepends=True),\n", "        problem.output.replacement.splitlines(keepends=True),\n", "    )\n", "        if line.startswith(\"+\") or line.startswith(\"-\")\n", "    )\n", "\n", "problems = list[EditGenProblem]()\n", "i = 0\n", "for i, data_file in enumerate(DATA_ROOT.glob(\"*.parquet\")):\n", "    df = pandas.read_parquet(data_file)\n", "    problems.extend([p\n", "                for ps in df[\"pickled_results\"].apply(pickle.loads).to_list()\n", "                for p in ps\n", "                if p.output.changed\n", "                # Larger edit regions\n", "                and changed_lines(p) > 10\n", "    ])\n", "    if len(problems) > PROBLEM_COUNT:\n", "        break\n", "print(f\"Loaded {len(problems)} problems from the first {i + 1} files.\")"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [], "source": ["import yaml\n", "\n", "from base.prompt_format_retrieve import (\n", "    CompletionRetrieverPromptInput,\n", "    DocumentRetrieverPromptInput,\n", "    InstructRetrieverPromptInput,\n", "    get_retrieval_prompt_formatter_by_name\n", ")\n", "from base.prompt_format_retrieve.ethanol_embedding_prompt_formatter import (\n", "    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "    Ethanol6DocumentFormatter,\n", ")\n", "\n", "from base.tokenizers import create_tokenizer_by_name\n", "from research.eval.harness import factories\n", "from research.retrieval.retrieval_database import RetrievalDatabase\n", "from research.retrieval.chunking_functions import LineLevelChunker\n", "from research.retrieval.scorers import dense_scorer_v2\n", "\n", "\n", "tokenizer = create_tokenizer_by_name(\"starcoder\")\n", "chunker = LineLevelChunker(max_lines_per_chunk=30)\n", "seth_scorer = dense_scorer_v2.create_dense_scorer_from_neox_checkpoint(\n", "  checkpoint_path=\"/mnt/efs/augment/checkpoints/star_ethanol/starethanol6_16.1_mean_proj_512_2000/global_step2000\",\n", "  query_formatter=get_retrieval_prompt_formatter_by_name(\n", "      \"ethanol6.16.1-query-embedding-add-selected-code-and-instructions\",\n", "      tokenizer,\n", "    ),\n", "  document_formatter=get_retrieval_prompt_formatter_by_name(\n", "      \"ethanol6-embedding-with-path-key\",\n", "      tokenizer,\n", "    ),\n", ")\n", "\n", "retriever_dense = (\n", "  RetrievalDatabase(\n", "      chunker=chunker, \n", "      scorer=seth_scorer,\n", "      skip_indexed=True\n", "  )\n", ")\n"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["# Render the results.\n", "from collections.abc import Iterable\n", "import difflib\n", "import jinja2 \n", "import jinja2_highlight\n", "from IPython.display import display, HTML\n", "\n", "from research.core.types import Chunk\n", "from research.next_edits.edit_gen_stages import RagEditGenProblem\n", "from research.retrieval.types import RetrievalScore\n", "from research.core.artifacts import collect_artifacts\n", "from research.core.types import compute_file_id\n", "\n", "env = jinja2.Environment(\n", "    extensions=[jinja2_highlight.HighlightExtension],\n", ")\n", "RESULT_TEMPLATE = env.from_string(r\"\"\"\n", "<body>\n", "<h2>{{title}}</h2>\n", "<details>\n", "<summary>Retriever Prompt</summary>\n", "{% highlight %}{{retriever_prompt}}{% endhighlight %}\n", "</details>\n", "\n", "<details>\n", "<summary>Target Change</summary>\n", "{% highlight %}{{future_diff}}{% endhighlight %}\n", "</details>\n", "\n", "{% for chunk, score in results %}\n", "<details>\n", "    <summary>{{chunk.parent_doc.path}} L{{chunk.line_offset}} {{'%0.2f'|format(score)}}</summary>\n", "    {% highlight %}{{chunk.text}}{% endhighlight %}\n", "</details>\n", "{% endfor %}\n", "</body>\n", "\"\"\")\n", "\n", "def render_results(\n", "        title: str,\n", "        problem: EditGenProblem | RagEditGenProblem,\n", "        retriever_prompt: str,\n", "        results: Iterable[tuple[Chun<PERSON>, RetrievalScore]]\n", "    ):\n", "    display(HTML(\n", "        RESULT_TEMPLATE.render(\n", "            title=title,\n", "            retriever_prompt=retriever_prompt,\n", "            future_diff=target_diff(problem),\n", "            results=results,\n", "        )\n", "    ))\n", "\n", "def get_results(problem: EditGenProblem, problem2query) -> tuple[str, list[tuple[Chunk, RetrievalScore]]]:\n", "    with collect_artifacts() as collector:\n", "        retriever_dense.add_docs(\n", "            [Document.new(code, path) for path, code in problem.repo_change.after_files.items()]\n", "        )\n", "\n", "        results = retriever_dense.query(\n", "                problem2<PERSON>y(problem),\n", "                top_k=16,\n", "                doc_ids=[compute_file_id(path, code) for path, code in problem.repo_change.after_files.items()],\n", "            )\n", "        artifacts = collector.get_flattened_artifacts()\n", "    return artifacts['retriever_prompt'], list(zip(*results))\n", "\n", "def target_diff(problem: EditGenProblem | RagEditGenProblem):\n", "    if isinstance(problem, RagEditGenProblem):\n", "        prefix = problem.input.current_code[:problem.input.edit_region.start].splitlines(keepends=True)\n", "        selected_code = problem.input.selected_code\n", "        path = problem.input.current_path\n", "    else:\n", "        prefix = problem.current_code[:problem.edit_region.start].splitlines(keepends=True)\n", "        selected_code = problem.selected_code\n", "        path = problem.current_path\n", "    \n", "\n", "    return \"\".join(difflib.unified_diff(\n", "        prefix + selected_code.splitlines(keepends=True),\n", "        prefix + problem.output.replacement.splitlines(keepends=True),\n", "        fromfile=str(path),\n", "        tofile=str(path),\n", "    ))"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [], "source": ["from research.utils import repo_change_utils\n", "\n", "\n", "PROBLEM = problems[3]\n", "\n", "print(repo_change_utils.patchset_from_repo_change(PROBLEM.repo_change, 3))\n", "print(\"~\"*80)\n", "\n", "print(target_diff(PROBLEM))"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["from base.prompt_format_retrieve.prompt_formatter import InstructRetrieverPromptInput\n", "\n", "\n", "def gold_query(problem: EditGenProblem):\n", "    return InstructRetrieverPromptInput(\n", "        prefix=problem.output.replacement,\n", "        suffix=\"\",\n", "        path=str(problem.current_path),\n", "        selected_code=\"\",\n", "        instruction=\"\",\n", "    )\n", "\n", "render_results(\"Gold\", PROBLEM, *get_results(PROBLEM, gold_query))\n"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["def droid_query(problem: EditGenProblem, n_lines: int = 10):\n", "    prefix = problem.current_code[:problem.edit_region.start]\n", "    suffix = problem.current_code[problem.edit_region.stop:]\n", "\n", "    return InstructRetrieverPromptInput(\n", "        prefix=prefix,\n", "        suffix=suffix,\n", "        path=str(problem.current_path),\n", "        selected_code=problem.selected_code,\n", "        instruction=problem.instruction,\n", "    )\n", "\n", "render_results(\"Droid\", PROBLEM, *get_results(PROBLEM, droid_query))\n"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["from research.next_edits.edit_gen_formatters import EditRetrieverPromptFormatter\n", "\n", "def pipeline_query(problem: EditGenProblem, n_lines: int = 10):\n", "    input = EditRetrieverPromptFormatter(use_diff=True, diff_context_lines=n_lines).format_retriever_prompt(\n", "        str(problem.current_path),\n", "        problem.current_code,\n", "        problem.edit_region,\n", "        [change.map(lambda x: x.to_file()) for change in problem.repo_change.changed_files],\n", "    )\n", "    return InstructRetrieverPromptInput(\n", "        prefix=input.prefix,\n", "        suffix=input.suffix,\n", "        path=str(input.path),\n", "        selected_code=\"\",\n", "        instruction=\"\",\n", "    )\n", "\n", "render_results(\"Pipeline w/ diff\", PROBLEM, *get_results(PROBLEM, pipeline_query))\n"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [], "source": ["from research.next_edits.edit_gen_formatters import EditRetrieverPromptFormatter\n", "\n", "def pipeline_query(problem: EditGenProblem, n_lines: int = 10):\n", "    input = EditRetrieverPromptFormatter(use_diff=False, diff_context_lines=n_lines).format_retriever_prompt(\n", "        str(problem.current_path),\n", "        problem.current_code,\n", "        problem.edit_region,\n", "        [change.map(lambda x: x.to_file()) for change in problem.repo_change.changed_files],\n", "    )\n", "    return InstructRetrieverPromptInput(\n", "        prefix=input.prefix,\n", "        suffix=input.suffix,\n", "        path=str(input.path),\n", "        selected_code=\"\",\n", "        instruction=\"\",\n", "    )\n", "\n", "render_results(\"Pipeline w/o diff\", PROBLEM, *get_results(PROBLEM, pipeline_query))\n"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [], "source": ["from research.next_edits.diff_formatter import default_diff_filter\n", "from research.next_edits.edit_gen_formatters import EditRetrieverPromptFormatter, format_file_changes\n", "\n", "def pipeline_query(problem: EditGenProblem, n_lines: int = 10):\n", "    diff_ = format_file_changes(\n", "        [change.map(lambda x: x.to_file()) for change in problem.repo_change.changed_files],\n", "        diff_context_lines=3,\n", "        diff_filter=default_diff_filter,\n", "        filter_duplicated_file_paths=True,\n", "    )\n", "\n", "    prefix = problem.current_code[:problem.edit_region.start]\n", "    suffix = problem.current_code[problem.edit_region.stop:]\n", "    return InstructRetrieverPromptInput(\n", "        prefix=diff_ + \"\\n\" + prefix,\n", "        suffix=suffix,\n", "        path=str(problem.current_path),\n", "        selected_code=problem.selected_code,\n", "        instruction=problem.instruction,\n", "    )\n", "\n", "render_results(\"Arun\", PROBLEM, *get_results(PROBLEM, pipeline_query))\n"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [], "source": ["from research.next_edits.diff_formatter import default_diff_filter\n", "from research.next_edits.edit_gen_formatters import EditRetrieverPromptFormatter, format_file_changes\n", "\n", "def pipeline_query(problem: EditGenProblem, n_lines: int = 10):\n", "    diff_ = format_file_changes(\n", "        [change.map(lambda x: x.to_file()) for change in problem.repo_change.changed_files],\n", "        diff_context_lines=3,\n", "        diff_filter=default_diff_filter,\n", "        filter_duplicated_file_paths=True,\n", "    )\n", "\n", "    prefix = problem.current_code[:problem.edit_region.start]\n", "    suffix = problem.current_code[problem.edit_region.stop:]\n", "    return InstructRetrieverPromptInput(\n", "        prefix=diff_ + \"\\n\" + prefix,\n", "        suffix=suffix,\n", "        path=str(problem.current_path),\n", "        selected_code=problem.output.replacement,\n", "        instruction=problem.instruction,\n", "    )\n", "\n", "render_results(\"ArunV2\", PROBLEM, *get_results(PROBLEM, pipeline_query))\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [], "source": ["from research.next_edits.diff_formatter import default_diff_filter\n", "from research.next_edits.edit_gen_formatters import EditRetrieverPromptFormatter, format_file_changes\n", "from research.next_edits.edit_gen_stages import first_n_lines, last_n_lines\n", "\n", "def pipeline_query(problem: EditGenProblem, n_lines: int = 10):\n", "    prefix = problem.current_code[:problem.edit_region.start]\n", "    suffix = problem.current_code[problem.edit_region.stop:]\n", "    return InstructRetrieverPromptInput(\n", "        prefix=first_n_lines(prefix, 10),\n", "        suffix=last_n_lines(suffix, 10),\n", "        path=str(problem.current_path),\n", "        selected_code=problem.output.replacement,\n", "        instruction=problem.instruction,\n", "    )\n", "\n", "render_results(\"ArunV3\", PROBLEM, *get_results(PROBLEM, pipeline_query))\n"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [], "source": ["from research.next_edits import edit_gen_stages\n", "\n", "retriever_config = edit_gen_stages.RetrieverConfigDict({\n", "    \"scorer\": {\n", "        \"name\": \"dense_scorer_v2_fbwd_neox\",\n", "        \"checkpoint_path\": \"/mnt/efs/augment/checkpoints/star_ethanol/starethanol6_16.1_mean_proj_512_2000/global_step2000\",\n", "        \"tokenizer_name\": \"starcoder\",\n", "    },\n", "    \"chunker\": {\n", "        \"name\": \"line_level\",\n", "        \"max_lines_per_chunk\": 30,\n", "    },\n", "    \"query_formatter\": {\n", "        \"name\": \"base:ethanol6.16.1-query-embedding-add-selected-code-and-instructions\",\n", "        \"tokenizer\": \"starcoder\",\n", "    },\n", "    \"document_formatter\": {\n", "        \"name\": \"base:ethanol6-embedding-with-path-key\",\n", "        \"tokenizer\": \"starcoder\",\n", "    },\n", "})\n", "\n", "ret = list(edit_gen_stages.perform_dense_retrieval(\n", "    config=edit_gen_stages.RetrievalConfig(\n", "        retriever_config=retriever_config,\n", "        num_retrieved_chunks=24,\n", "        timeout_per_repo=600,\n", "        skip_dense_retrieval=False,\n", "        prefix_suffix_lines=60,\n", "    ),\n", "    problems=[PROBLEM],\n", "    seed=42\n", "))\n", "\n", "len(ret)"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [], "source": ["# 60 lines\n", "render_results(\"EditGen\", PROBLEM, \"\", ((chunk, 0.0) for chunk in ret[0].input.retrieval_chunks))"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [], "source": ["# 30 lines\n", "render_results(\"EditGen\", PROBLEM, \"\", ((chunk, 0.0) for chunk in ret[0].input.retrieval_chunks))"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [], "source": ["# 5 lines\n", "render_results(\"EditGen\", PROBLEM, \"\", ((chunk, 0.0) for chunk in ret[0].input.retrieval_chunks))"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [], "source": ["chunks = [ret[0].input.retrieval_chunks[1]]\n", "# edit_gen_stages.filter_overlapping_chunks(PROBLEM, [ret[0].input.retrieval_chunks[1]], 10)\n", "edit_gen_stages.utils.filter_overlap_chunks(\n", "    str(PROBLEM.current_path),\n", "    edit_gen_stages.utils.Span(\n", "        260, 310\n", "        # len(PROBLEM.prefix) - len(edit_gen_stages.last_n_lines(PROBLEM.prefix, 10)),\n", "        # PROBLEM.edit_region.start,\n", "    ),\n", "    chunks,\n", ")"]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [], "source": ["(\n", "        len(PROBLEM.prefix) - len(edit_gen_stages.last_n_lines(PROBLEM.prefix, 10)),\n", "        PROBLEM.edit_region.start,\n", ")"]}, {"cell_type": "code", "execution_count": 45, "metadata": {}, "outputs": [], "source": ["PROBLEM.edit_region"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["import difflib\n", "\n", "from research.next_edits.edit_gen_stages import RagEditGenProblem\n", "DATA_ROOT = Path(\n", "    \"/mnt/efs/spark-data/shared/next-edit/stage2/gh_pr_train_repartitioned/S1.12_6000p_2000f,R1.3_edit_ethanol_synth_instruct\"\n", ")\n", "PROBLEM_COUNT = 100\n", "\n", "problems = list[RagEditGenProblem]()\n", "i = 0\n", "for i, data_file in enumerate(DATA_ROOT.glob(\"*.parquet\")):\n", "    df = pandas.read_parquet(data_file)\n", "    problems.extend([p\n", "                for ps in df[\"pickled_results\"].apply(pickle.loads).to_list()\n", "                for p in ps\n", "    ])\n", "    if len(problems) > PROBLEM_COUNT:\n", "        break\n", "print(f\"Loaded {len(problems)} problems from the first {i + 1} files.\")"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["PROBLEM = problems[4]\n", "render_results(\"EditGen\", PROBLEM, \"\", ((chunk, 0.0) for chunk in PROBLEM.input.retrieval_chunks))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}