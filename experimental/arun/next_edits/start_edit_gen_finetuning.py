# flake8: noqa
from __future__ import annotations

from pathlib import Path

from megatron.tokenizer.tokenizer import StarCoder2Tokenizer, StarCoderTokenizer
from research.core.constants import AUGMENT_EFS_ROOT
from research.environments.providers import ClusterName
from experimental.jiayi.finetuning.training_config_utils import (
    ModelName,
    ModelSize,
    StarCoderModelSize,
    get_neox_config,
    get_fastbackward_config,
    sync_gcp,
    wait_for_condition,
)


def edit_gen_fb_config(
    run_summary: str,
    model_name: ModelName,
    user: str,
    model_size: ModelSize,
    data_splits_path: Path,
    sequence_length: int,
    train_iters: int,
    is_quick_test: bool = False,
) -> dict:
    """Get the training config dict to be submitted to FastBackward.

    Args:
        run_summary: A short string summary of the training run.
        model_name: The model name to use.
        user: The training job will be submitted under Dev/{user}.
        model_size: The model size to use.
        data_splits_path: The directory containing the indexed dataset files.
        sequence_length: The sequence length to use.
        is_quick_test: Whether to use a quick test config.
    """

    training_name = f"{run_summary}-{model_name}_{model_size}"
    if is_quick_test:
        training_name += "-quicktest"

    wandb_project = "next-edit-gen"

    if is_quick_test:
        return get_fastbackward_config(
            model_name=model_name,
            model_size=model_size,
            training_name=training_name,
            wandb_project="quicktest",
            user=user,
            data_split_path=data_splits_path,
            sequence_length=sequence_length,
            n_gpu=8,
            n_nodes=1,
            batch_size_per_gpu=4,
            grad_accumulation_steps=2,
            train_iters=50,
            overrides={"eval_interval": 40, "eval_items": 128},
        )

    common_overrides = {
        "eval_items": 512 * 50,
        "eval_interval": 500,
        "log_interval": 100,
    }

    # effective batch size = 256
    if model_size == "3b":
        return get_fastbackward_config(
            model_name=model_name,
            model_size=model_size,
            training_name=training_name,
            wandb_project=wandb_project,
            user=user,
            data_split_path=data_splits_path,
            sequence_length=sequence_length,
            n_gpu=8,
            n_nodes=4,
            batch_size_per_gpu=4,
            grad_accumulation_steps=2,
            train_iters=train_iters,
            overrides=common_overrides,
        )
    if model_size == "7b":
        return get_fastbackward_config(
            model_name=model_name,
            model_size=model_size,
            training_name=training_name,
            wandb_project=wandb_project,
            user=user,
            data_split_path=data_splits_path,
            sequence_length=sequence_length,
            n_gpu=8,
            n_nodes=4,
            batch_size_per_gpu=2,
            grad_accumulation_steps=4,
            train_iters=train_iters,
            overrides=common_overrides,
        )
    elif model_size in ("15b", "16b"):
        return get_fastbackward_config(
            model_name=model_name,
            model_size=model_size,
            training_name=training_name,
            wandb_project=wandb_project,
            user=user,
            data_split_path=data_splits_path,
            sequence_length=sequence_length,
            n_gpu=8,
            n_nodes=8,
            batch_size_per_gpu=2,
            grad_accumulation_steps=4,
            train_iters=train_iters,
            overrides=common_overrides,
        )
    else:
        raise NotImplementedError(f"model_size {model_size} not supported")


def edit_gen_neox_config(
    user: str,
    model_size: StarCoderModelSize,
    data_splits_path: Path,
    train_iters: int,
    run_summary: str,
    is_quick_test: bool = False,
) -> dict:
    """Get the training config dict to be submitted to Determinted.

    Args:
        user: The training job will be submitted under Dev/{user}.
        model_size: The model size to use.
        data_splits_path: The directory containing the indexed dataset files.
        run_summary: A short string summary of the training run.
        is_quick_test: Whether to use a quick test config.
    """

    wandb_project = "next-edit-gen"

    common_overrides = {
        "data_split_path": data_splits_path,
        "loss_mask_mode": "fim-context",
        "extra_loss_masks": [
            "pad",
            "fim-context",
            "eod-only",
        ],
        "max_valid_data_size": 512 * 50,
        "eval_interval": 500,
        "save_interval": 1000,
        "log-interval": 100,
    }

    if is_quick_test:
        training_name = f"{run_summary}-starcoder1B-quicktest"
    else:
        training_name = f"{run_summary}-starcoder{model_size}"

    starcoder_1b_quicktest_config = get_neox_config(
        model_size="1b",
        training_name=training_name,
        user=user,
        wandb_project="quicktest",
        n_gpu=1,
        n_nodes=2,  # test the multi-node case
        batch_size_per_gpu=8,
        grad_accumulation_steps=2,
        train_iters=50,
        **{
            **common_overrides,
            "max_valid_data_size": 64,
            "eval_interval": 10,
            "save_interval": 50,
            "log-interval": 10,
        },
    )

    # effective batch size = 256
    starcoder_3b_config = get_neox_config(
        model_size="3b",
        training_name=training_name,
        user=user,
        wandb_project=wandb_project,
        n_gpu=8,
        n_nodes=4,
        batch_size_per_gpu=4,
        grad_accumulation_steps=2,
        train_iters=train_iters,
        **common_overrides,
    )

    starcoder_7b_config = get_neox_config(
        model_size="7b",
        training_name=training_name,
        user=user,
        wandb_project=wandb_project,
        n_gpu=8,
        n_nodes=4,
        batch_size_per_gpu=2,
        grad_accumulation_steps=4,
        train_iters=train_iters,
        **common_overrides,
    )

    starcoder_16b_config = get_neox_config(
        model_size="16b",
        training_name=training_name,
        user=user,
        wandb_project=wandb_project,
        n_gpu=8,
        n_nodes=4,
        batch_size_per_gpu=2,
        grad_accumulation_steps=8,
        train_iters=train_iters,
        **common_overrides,
    )

    if is_quick_test:
        return starcoder_1b_quicktest_config
    else:
        return {
            "3b": starcoder_3b_config,
            "7b": starcoder_7b_config,
            "16b": starcoder_16b_config,
        }[model_size]


def start_finetuning():
    dataset_path = Path(
        "/mnt/efs/augment/data/processed/next-edit/gh_pr_train_repartitioned/"
        # "S1.13.1_6000p_2000f,R1.2_no_retrieval_synth_instruct,P1.10.1_star2_context12"
        "S1.13.1_10000p_4000f,R1.4_ravenr_synth_instruct_K128,P1.14_star2_context12"
    )
    wait_for_condition(
        "dataset_path", lambda: (dataset_path / "_SUCCESS").exists(), retry_secs=60
    )
    model_name = "starcoder2"
    run_summary = dataset_path.name
    train_iters = 8000
    model_size = "15b"
    # model_size = "7b"
    training_with_fb = True
    is_quick_test = False
    cluster: ClusterName = "CW"

    if cluster != "CW":
        if model_name == "starcoder2":
            sync_gcp(AUGMENT_EFS_ROOT / StarCoder2Tokenizer.VocabFileDir)
        else:
            sync_gcp(AUGMENT_EFS_ROOT / StarCoderTokenizer.VocabFileDir)

    assert training_with_fb
    from research.fastbackward.determined.launch import launch_fb_job  # type: ignore

    train_config = edit_gen_fb_config(
        model_name=model_name,
        run_summary=run_summary,
        user="arun",
        model_size=model_size,
        data_splits_path=dataset_path,
        sequence_length=8192,
        train_iters=train_iters,
        is_quick_test=is_quick_test,
    )
    launch_fb_job(
        train_config,
        cluster=cluster,
    )


if __name__ == "__main__":
    start_finetuning()
