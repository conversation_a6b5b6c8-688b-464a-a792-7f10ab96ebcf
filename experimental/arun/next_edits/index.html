<meta charset="UTF-8">
<head>
<link rel="stylesheet" href="/arun/next_edits/styles/index.css" />
<link rel="stylesheet" href="/arun/next_edits/styles/monokai.css" />
<script src="https://unpkg.com/htmx.org@1.9.12" integrity="sha384-ujb1lZYygJmzgSwoxRggbCHcjc0rB2XoQrxeTUQyRjrOnlCoYta87iKBWq3EsdM2" crossorigin="anonymous"></script>
</head>

<body>
<h3>{{system}}</h3>
<div class="container">
<table class="summary" id="top">
    <tr class="header">
        <th></th>
        <th>{{"%0.4f" | format(metrics.macro_metrics[top_k].mean_ap)}}</th>
        <th colspan="{{top_k}}">{{metrics.macro_metrics[top_k]}}</th>
    </tr>
    {% for datum, outputs in grouped_outputs %}
    {% set group_idx = loop.index0 %}
    {% set group_metrics = metrics.per_group_metrics[datum.group_id][top_k] %}
    <tr id="group-{{group_idx}}">
        <th><a href="#group-{{group_idx}}">{{group_idx}}</a></th>
        <th>{{"%0.4f" | format(group_metrics.mean_ap)}}</th>
        <th colspan="{{top_k}}">{{datum.commit_meta.repo_name}}: {{datum.instruction}}</th>
    </tr>
    {% for o in outputs %}
    <tr>
        <td><a
             hx-get="example-{{o.group_id}}.{{o.group_sequence_id}}.html"
             hx-target="#example-view"
             />{{group_idx}}.{{loop.index0}}</a></td>
        <td>{{"%0.4f" | format(o.metrics.mean_ap)}}</td>
        {% for lbl in o.scored_candidate_labels[:top_k] %}
        <td class="target target-{{lbl}}"></td>
        {% endfor %}
    </tr>
    {% endfor %}
    <tr class="header">
        <th colspan="2"></th>
        <th colspan="{{top_k}}">{{group_metrics}}</th>
    </tr>
    {% endfor %}
    <tr id="bottom"></tr>
</table>
<div id="example-view"></div>
</div>
</body>
