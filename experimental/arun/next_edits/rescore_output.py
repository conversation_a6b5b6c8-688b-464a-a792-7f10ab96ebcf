"""<PERSON><PERSON><PERSON> to just re-run `compute_metrics` on some eval output to fix a metrics bug."""

import json
import logging
from collections import defaultdict
from dataclasses import replace
from pathlib import Path

from research.eval.harness.tasks import next_edit_location_eval_task
from research.next_edits.next_edits_dataset import Datum

logger = logging.getLogger(__name__)


def recompute_metrics(
    dataset: list[Datum],
    output: list[next_edit_location_eval_task.Output],
):
    agg_metrics = next_edit_location_eval_task.RankingMetrics(count=0)
    per_group_metrics = defaultdict(
        lambda: next_edit_location_eval_task.RankingMetrics(count=0)
    )
    for predicted, datum in zip(output, dataset):
        target = next_edit_location_eval_task.convert_to_task_input(datum)
        if target is None:
            continue
        assert target.prompt.label is not None
        predicted.metrics, predicted.scored_candidate_labels = (
            next_edit_location_eval_task.compute_metrics(
                predicted, target.prompt.label.locations, 256
            )
        )
        agg_metrics.update(predicted.metrics)
        per_group_metrics[datum.group_id].update(predicted.metrics)

    macro_agg_metrics = next_edit_location_eval_task.RankingMetrics(count=0)
    for group_metrics in per_group_metrics.values():
        macro_agg_metrics.update(replace(group_metrics, count=1))

    return agg_metrics, macro_agg_metrics, per_group_metrics


def main():
    import argparse

    logging.basicConfig(level=logging.INFO, force=True)

    parser = argparse.ArgumentParser()
    parser.add_argument(
        "output_path",
        type=Path,
        nargs="+",
        help="Path to the output jsonl.zst files",
    )
    parser.add_argument(
        "-d",
        "--dataset_path",
        type=Path,
        help="Path to the dataset to load.",
    )
    args = parser.parse_args()

    logger.info("Loading dataset...")
    dataset = next_edit_location_eval_task.load_dataset(args.dataset_path)
    for output_path in args.output_path:
        logger.info("Loading output for %s...", output_path)
        metrics_path = next(output_path.glob("*results.jsonl"))
        artifacts_path = next(output_path.glob("*.jsonl.zst"))
        output = next_edit_location_eval_task.load_output(artifacts_path)
        # Output is updated.
        agg_metrics, macro_agg_metrics, per_group_metrics = recompute_metrics(
            dataset, output
        )

        print(macro_agg_metrics)

        # utils.write_jsonl_zst(
        #     artifacts_path.with_suffix(".updated.jsonl.zst"),
        #     [asdict(o) for o in tqdm.tqdm(output, desc=f"saving output {output_path}")],
        # )

        metrics_obj = json.loads(metrics_path.read_text())
        metrics_obj["metrics"] = agg_metrics.to_dict()
        metrics_obj["macro_agg_metrics"] = macro_agg_metrics.to_dict()
        metrics_obj["per_group_metrics"] = {
            group_id: group_metrics.to_dict()
            for group_id, group_metrics in per_group_metrics.items()
        }
        # write to file
        metrics_path.with_suffix(".updated.jsonl").write_text(json.dumps(metrics_obj))


if __name__ == "__main__":
    main()
