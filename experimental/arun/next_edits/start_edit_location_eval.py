"""<PERSON><PERSON><PERSON> to run all edit generation evaluation jobs.

This script should be manually edited and run to launch all the evaluation jobs.
"""

from pathlib import Path
from typing import cast

import yaml

from experimental.jiayi.evaluation.eval_config_tools import launch_eval
from research.core.constants import AUGMENT_ROOT
from research.next_edits.edit_gen_formatters import SectionBudgetsDict

diff_context_lines = 9

reranker_config = {
    "name": "next_edit_gen",
    "model": {
        "name": "starcoder2_fastforward",
        "model_path": (
            "/mnt/efs/augment/checkpoints/"
            "next-edit-gen/S1.10-R1.0_no_retrieval-P1.10_context12-gh_pr_train_repartitioned-starcoder2_7b"
        ),
        "checkpoint_sha256": "71114870ff62d75f7b3a4fa1b814d66c57b7f3afd7a4fb5d1b8e5c7448bbcf25",
    },
    "generation_options": {"max_generated_tokens": 1},
    "prompt_formatter": {
        "use_diff_based_output": True,
        "diff_context_lines": diff_context_lines,
    },
    "retriever_formatter": {
        "use_diff": False,
    },
}

localizer_config = {
    "name": "next_edit_location",
    "group_by_path": False,
    "filter_input_ranges": False,
    "retriever": {
        "scorer": {
            "name": "dense_scorer_v2_fbwd",
            # "checkpoint_path": "/mnt/efs/augment/checkpoints/next-edit-location/raven1b.query.8targets.rel.S1.3,R1.2_v13-128.smart2000,Sc1.0_path_distance,T1.1_5-15lines.downsample10.instructions50",
            # "checkpoint_path": "/mnt/efs/augment/checkpoints/next-edit-location/raven1b.tied.S1.3,R1.2_v13-128.smart2000,Sc1.1_raven_edit_S24-R4-P18,T1.1,seth",
            "checkpoint_path": "/mnt/efs/augment/checkpoints/next-edit-location/raven1b.tied.S1.3,R1.2_v13-128.smart2000,Sc1.1_raven_edit_S24-R4-P18,T1.1,t0.1,seth",
            "tokenizer_name": "starcoder",
        },
        # FP8 version of raven1b.query.8targets.rel.S1.3,R1.2_v13-128.smart2000,Sc1.0_path_distance,T1.1_5-15lines.downsample10.instructions50
        # "scorer": {
        #     "name": "dense_scorer_ffwd_starcoder_fp8",
        #     "checkpoint_path": "/mnt/efs/augment/checkpoints/next-edit-location/raven-location-v2-query-fp8",
        #     "checkpoint_sha256": "e64c8292172afdcd2decec58f3bbc0dd973ce02ffa13445a5195e258ad959220",
        #     "doc_model_checkpoint_path": "/mnt/efs/augment/checkpoints/next-edit-location/raven-location-v2-doc-fp8",
        #     "doc_model_checkpoint_sha256": "012b00f1dcbafa10ad5d6bf69443aecde38f6419f659c38a9ffc3f7ffbee6426",
        #     "output_projection_dim": 512,
        #     "tokenizer_name": "starcoder",
        # },
        "chunker": {
            "name": "smart_line_level",
            # "max_chunk_chars": 1280,
            "max_chunk_chars": 2_000,
        },
        "query_formatter": {
            "name": "next_edit_location_query",
            "tokenizer": "starcoder",
            "deduplicate_identical_paths": True,
            "use_smart_header": True,
        },
        "document_formatter": {
            "name": "base:ethanol6-embedding-with-path-key",
            "tokenizer": "starcoder",
            "max_tokens": 999,
        },
    },
}

if __name__ == "__main__":
    tasks = [
        {
            "name": "next_edit_location",
            "dataset_path": dataset_path,
            "limit_examples": 1000,
            "top_ks": [3, 8, 32],
        }
        for dataset_path in [
            "/mnt/efs/augment/data/eval/next_edits/manual.v3.jsonl.zst",
            "/mnt/efs/augment/data/eval/next_edits/prs.v7.jsonl.zst",
        ]
    ]
    for use_reranker in [False, True]:
        if not use_reranker:
            system_config = localizer_config
        else:
            system_config = {
                "name": "next_edit_reranker",
                "localizer": localizer_config,
                "reranker": reranker_config,
                "rechunker": None,
                "filter_results_threshold": 0.6,
            }
        for task_config in tasks:
            dataset_name = Path(task_config["dataset_path"]).name.replace(
                ".jsonl.zst", ""
            )
            if system_config == localizer_config:
                model_name = Path(
                    localizer_config["retriever"]["scorer"]["checkpoint_path"]
                ).name
                determined_name = f"{dataset_name},{model_name}"
            else:
                localizer_name = Path(
                    localizer_config["retriever"]["scorer"]["checkpoint_path"]
                ).name
                model_name = Path(reranker_config["model"]["model_path"]).name
                determined_name = f"{dataset_name},{localizer_name},rrk-{model_name}"
            determined_name = f"{determined_name},topK{task_config['top_ks'][-1]}"
            if "filter_results_threshold" in system_config:
                determined_name += f",filter{system_config['filter_results_threshold']}"

            job_root = Path(f"/mnt/efs/augment/eval/next_edits/{determined_name}")
            if job_root.exists():
                print(f"Skipping {determined_name} because {job_root} exists.")
                continue

            launch_eval(
                determined_name,
                system_config,
                task_config,
                "1xH100.yaml",
                job_root=job_root,
            )
