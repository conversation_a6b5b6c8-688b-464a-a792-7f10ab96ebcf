"""<PERSON><PERSON><PERSON> to run all edit generation evaluation jobs.

This script should be manually edited and run to launch all the evaluation jobs.
"""

from pathlib import Path

from experimental.jiayi.evaluation.eval_config_tools import launch_eval
from research.data.spark.pipelines.stages.next_edit_gen_pipelines import (
    edit_ethanol_config,
)
from research.next_edits.edit_gen_stages import RetrieverConfigDict

diff_context_lines = 9

model_config = {
    "name": "starcoder2_fastforward",
    "model_path": (
        "/mnt/efs/augment/checkpoints/"
        "next-edit-gen/S1.13.1-R1.3_edit_ethanol_synth_instruct-P1.10.1_context12-gh_pr_train_repartitioned-starcoder2_7b-ffwd"
    ),
}

model_v2_config = {
    "name": "starcoder2_fastforward",
    "model_path": (
        "/mnt/efs/augment/checkpoints/"
        "next-edit-gen/S1.13.1,R1.4_ravenr,P1.14,starcoder2_15b-ffwd"
    ),
}

raven_edit_retriever_config: RetrieverConfigDict = {
    "scorer": {
        "name": "dense_scorer_v2_fbwd",
        "checkpoint_path": "/mnt/efs/augment/checkpoints/next-edit-gen-retrieval/ravenr1b.tied.S1.13.1_6000p_2000f,R1.3_edit_ethanol_synth_instruct_k128,Sc1.0_smart_chunks_1280,T1.0_diff1k_sc1",
        "tokenizer_name": "starcoder",
    },
    "chunker": {
        "name": "smart_line_level",
        "max_chunk_chars": 768,
    },
    "query_formatter": {
        "name": "next_edit_gen_query",
        "tokenizer": "starcoder",
    },
    "document_formatter": {
        "name": "base:ethanol6-embedding-with-path-key",
        "tokenizer": "starcoder",
    },
}

system_config = {
    "name": "next_edit_gen",
    "model": model_v2_config,
    "generation_options": {"max_generated_tokens": 800},
    "retriever": dict(raven_edit_retriever_config),
    "prompt_formatter": {
        "diff_context_lines": diff_context_lines,
        "max_prompt_tokens": 12_000,
        "section_budgets": {
            "suffix_tks": 800,
            "prefix_tks": 1200,
            "diff_tks": 4000,
            "filename_tks": 50,
            "instruction_tks": 250,
            "retrieval_tks": 4000,
        },
    },
    "use_gold_output_to_retrieve_chunks": False,
    "retriever_formatter": {
        "use_diff": True,
    },
}


if __name__ == "__main__":
    tasks = [
        {
            "name": "next_edit_gen",
            "dataset_path": dataset_path,
            "must_give_change": True,
            "limit_examples": 2000,
        }
        for dataset_path in [
            "/mnt/efs/augment/data/eval/next_edits/manual.v3.jsonl.zst",
            "/mnt/efs/augment/data/eval/next_edits/prs.v7.jsonl.zst",
        ]
    ]
    for task_config in tasks:
        model_name = Path(system_config["model"]["model_path"]).name
        dataset_name = Path(task_config["dataset_path"]).name
        determined_name = f"{dataset_name}, {model_name}"

        for retrieval_name, retriever_config in {
            "edit_ethanol": dict(edit_ethanol_config),
            "raven_edit_retriever": dict(raven_edit_retriever_config),
        }.items():
            system_config_ = system_config.copy()
            if not retriever_config:
                system_config_.pop("retriever", None)
            else:
                system_config_["retriever"] = retriever_config
            # assert system_config["retriever"]["chunker"]["name"] == "smart_line_level"
            max_tokens = system_config_["prompt_formatter"]["max_prompt_tokens"]
            determined_name_ = f"{determined_name},{retrieval_name},{max_tokens}tks"
            launch_eval(
                determined_name_,
                system_config_,
                task_config,
                "1xA100.yaml",
                local=False,
            )
