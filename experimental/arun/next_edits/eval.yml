# This file contains an example config for next edit location, and is not the
# recommended configuration.

system:
  name: next_edit_location_basic
  retriever:
    scorer:
      name: dense_scorer_v2
      # checkpoint_path: /mnt/efs/augment/checkpoints/next-edit-location/20240417.seth1b-bs12x8x8-lr2e-4clip-epochs2
      # checkpoint_path: /mnt/efs/augment/checkpoints/next-edit-location/20240417.seth1b-bs4x12x8-lr2e-5-epochs2-wdoc
      checkpoint_path: /mnt/efs/augment/checkpoints/next-edit-location/20240421.raven-bs8x2x16-lr2e-05-iters1000-K128
      tokenizer_name: starcoder
      query_prompt_formatter_name: next-edit-location-query
      doc_prompt_formatter_name: ethanol6-embedding-with-path-key

    # scorer:
    #   name: ethanol
    #   checkpoint_path: ethanol/ethanol6-04.1
    chunker:
      name: line_level
      max_lines_per_chunk: 30
    # We don't actually use these.
    query_formatter:
      name: ethanol6_query
      max_tokens: 4096
      add_path: true
    document_formatter:
      name: ethanol6_document
      max_tokens: 999
      add_path: true

task:
  name: next_edit_location
  # limit_examples: 5


# Podspec - set the default podspec for all checkpoints
# See gpt-neox/jobs/templates/podspecs/ for additional options
# Use the following for small models (<=2B)
# podspec: gpu-small.yaml
# Use the following for larger models (>=2B)
podspec: A40.yaml

# Determined
# name, workspace, project control location and display in the determined UI.

determined:
  name: Next Edit Location
  workspace: Dev
  project: playground
  # relative to research/gpt-neox
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml
