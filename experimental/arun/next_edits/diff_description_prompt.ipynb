{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [], "source": ["# Exploring prompts for generating diff descriptions.\n", "prompt = \"\"\"\n", "Read the diff below.\n", "\n", "```\n", "--- a/src/black/comments.py\n", "+++ b/src/black/comments.py\n", "@@ -213,7 +213,7 @@ def convert_one_fmt_off_pair(\n", "             prefix = first.prefix\n", "             if comment.value in FMT_OFF:\n", "                 first.prefix = prefix[comment.consumed :]\n", "-            if _contains_fmt_skip_comment(comment.value, mode):\n", "+            if is_fmt_skip:\n", "                 first.prefix = \"\"\n", "                 standalone_comment_prefix = prefix\n", "             else:\n", "```\n", "\n", "Write a brief description of the above diff in less than 20 words.\n", "Use present tense and imperative voice.\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Approach 1: directly using hugging<PERSON> to load LLaMA 8B.\n", "import transformers\n", "import torch\n", "\n", "model_id = \"/mnt/efs/augment/checkpoints/llama3.1/hf/Meta-Llama-3.1-8B-Instruct\"\n", "\n", "pipeline = transformers.pipeline(\n", "    \"text-generation\",\n", "    model=model_id,\n", "    model_kwargs={\"torch_dtype\": torch.bfloat16},\n", "    device_map=\"auto\",\n", ")"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["messages = [\n", "    {\"role\": \"user\", \"content\": prompt},\n", "]\n", "pipeline(messages, max_new_tokens=200)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["# Approach 2: using AugmentClient to call into LLaMA 70B/8B.\n", "from base.augment_client.client import AugmentClient\n", "\n", "AUGMENT_API_TOKEN = \"SECRET\"\n", "client = AugmentClient(\n", "    \"https://staging-shard-0.api.augmentcode.com/\", AUGMENT_API_TOKEN\n", ")\n", "model_client = client.client_for_model(\"binks-l3-70B-FP8-ug-chatanol1-16-3-chat\")\n", "model_client"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [], "source": ["response = model_client.chat(\n", "    selected_code=\"\",\n", "    message=prompt,\n", "    prefix=\"\",\n", "    suffix=\"\",\n", "    path=\"\",\n", ")\n", "print(response.text)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["from base.tokenizers import create_tokenizer_by_name\n", "from research.models.language_model import generate_tokens, stop_when_in\n", "from research.models.language_models.fastforward import create_from_llama3_checkpoint\n", "\n", "model_8b = create_from_llama3_checkpoint(\n", "    \"/mnt/efs/augment/checkpoints/llama3.1/ff/Meta-Llama-3.1-8B-Instruct\",\n", "    use_fp8=False,\n", ")\n", "\n", "tknzr = create_tokenizer_by_name(\"llama3_instruct\")\n", "generate_tokens(\n", "    model_8b,\n", "    tknzr.tokenize_safe(prompt),\n", "    200,\n", "    should_stop_fn=stop_when_in([tknzr.special_tokens.eos]),\n", ")"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [], "source": ["from base.tokenizers import create_tokenizer_by_name\n", "\n", "tknzr = create_tokenizer_by_name(\"llama3_instruct\")\n", "tokens = generate_tokens(\n", "    model_8b,\n", "    tknzr.tokenize_safe(prompt),\n", "    200,\n", "    should_stop_fn=stop_when_in([tknzr.special_tokens.eos]),\n", ").tokens\n", "print(tknzr.de<PERSON><PERSON><PERSON>(tokens))"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [], "source": ["tokens[28:30]"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [], "source": ["# Approach 2: using AugmentClient to call into LLaMA 70B/8B.\n", "from base.augment_client.client import AugmentClient\n", "\n", "AUGMENT_API_TOKEN = \"SECRET\"\n", "dev_client = AugmentClient(\n", "    \"https://dev-arun.us-central.api.augmentcode.com/\", AUGMENT_API_TOKEN\n", ")\n", "model_8b = dev_client.client_for_model(\"binks-8b-chat\")"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [], "source": ["print(\n", "    response := model_8b.chat(\n", "        message=\"hello\",\n", "        selected_code=\"\",\n", "        prefix=\"\",\n", "        suffix=\"\",\n", "        path=\"\",\n", "    ).text\n", ")"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["import pandas as pd"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["from dataclasses import dataclass\n", "from pathlib import Path\n", "import pickle\n", "\n", "from research.next_edits.edit_localization_stages import EditLocalizationProblem\n", "\n", "\n", "DATA_ROOT = Path(\n", "    \"/mnt/efs/spark-data/user/arun/next-edit-location/S1.2_prs_2k.keepmost.filter\"\n", ")\n", "\n", "\n", "def load_data(data_root: Path = DATA_ROOT, limit: int = 100):\n", "    data = []\n", "    for path in DATA_ROOT.glob(\"*.parquet\"):\n", "        df = pd.read_parquet(path)\n", "        for record in df.to_dict(orient=\"records\"):\n", "            for datum in pickle.loads(record[\"pickled_results\"]):\n", "                datum: EditLocalizationProblem\n", "                data.append(datum)\n", "\n", "        if len(data) > 100:\n", "            break\n", "\n", "    # Load the diffs.\n", "    return data"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["data = load_data()"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["from research.utils.repo_change_utils import patchset_from_repo_change\n", "\n", "ps = patchset_from_repo_change(\n", "    data[0].past_to_future_repo_change, 3, ignore_whitespace=True\n", ")\n", "print(ps)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}