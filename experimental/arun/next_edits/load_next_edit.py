"""Test script to load data from next edits projects and print some stats."""

import torch
from megatron.data import indexed_dataset

from base.tokenizers import StarCoderSpecialTokens, create_tokenizer_by_name
from research.fastbackward import data, retrieval_data

# PATH = "/mnt/efs/augment/user/guy/data-pipeline/pr-next-edit-location-for-arun/next-edit-location-filtered-dataset/dataset"
PATH = "/mnt/efs/augment/user/guy/data-pipeline/history-of-pr-next-edit-location/12-04-2024/next-edit-location-indexed-dataset-new/dataset"


def main():
    tokenizer = create_tokenizer_by_name("starcoder")
    assert isinstance(tokenizer.special_tokens, StarCoderSpecialTokens)
    retrieval_data.patch_pin_memory()

    ds = indexed_dataset.make_dataset(str(PATH), impl="mmap", skip_warmup=True)
    assert ds is not None

    ds = data.MapDataset(
        ds,
        retrieval_data.create_tokens_to_retrieval_data_fn(
            tokenizer=tokenizer,
            # Large number to prevent truncation.
            max_document_tokens=1024,
            documents_per_batch=64,
            max_query_tokens=4096,
        ),
    )

    def collate_fn(xs):
        # return dict(xs[0].items())
        return xs[0]

    train_loader = torch.utils.data.DataLoader(
        ds,
        num_workers=1,  # TODO: try different values
        # TODO(arun): We currently use a batch size of 1, so we don't need the collate
        # function. Change as necessary.
        collate_fn=collate_fn,
        pin_memory=True,
    )

    empty_query_count = 0
    for i, example in enumerate(train_loader):
        Lq, K, Ld = (
            example["query_tokens_BLq"].shape[-1],
            example["doc_tokens_BKLd"].shape[1],
            example["doc_tokens_BKLd"].shape[-1],
        )
        Kn = -1 * example["labels_BK"].sum()
        Kp = K - Kn
        print(f"{i}: Lq={Lq}, Ld={Ld}, Kn={Kn}, Kp={Kp}")
        if Lq == 1:
            empty_query_count += 1
    print(f"Empty query count: {empty_query_count}")


if __name__ == "__main__":
    main()
