{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "EVAL_DIRS = {\n", "    \"no_retriever\": Path(\"/home/<USER>/eval_analysis/prs.v7.no_retriever\"),\n", "    \"edit_ethanol_retriever\": Path(\"/home/<USER>/eval_analysis/prs.v7.edit_ethanol_retriever\"),\n", "    \"prs.v7.raven_edit_retriever\": Path(\"/home/<USER>/eval_analysis/prs.v7.raven_edit_retriever\"),\n", "}"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["def load_data_dir(eval_dir: Path):\n", "    return {\n", "        fname.stem: fname.read_text()\n", "        for fname in eval_dir.glob(\"*.txt\")\n", "    }\n", "\n", "def load_data(eval_dir: Path):\n", "    return sorted([\n", "            {\n", "                \"id\": dirname.name.split(\"__\", 1)[1],\n", "                **load_data_dir(dirname),\n", "            }\n", "            for dirname in eval_dir.iterdir()\n", "            if dirname.is_dir()\n", "        ], key=lambda x: x[\"id\"]\n", "    )"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["EVAL_DATA = {\n", "    name: load_data(eval_dir)\n", "    for name, eval_dir in EVAL_DIRS.items()\n", "}"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["for data in EVAL_DATA.values():\n", "    for datum in data:\n", "        datum[\"is_correct\"] = datum[\"is_correct\"] == \"True\""]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["from collections import defaultdict\n", "EVAL_BY_ID = defaultdict(dict)\n", "for name, data in EVAL_DATA.items():\n", "    for datum in data:\n", "        EVAL_BY_ID[datum[\"id\"]][name] = datum\n", "len(EVAL_BY_ID)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["# Wins for retrieval.\n", "wins_for_retrieval = []\n", "losses_for_retrieval = []\n", "ties_for_retrieval = []\n", "wins_for_arun = []\n", "losses_for_arun = []\n", "for id_, datum_by_sys in EVAL_BY_ID.items():\n", "    if not datum_by_sys[\"no_retriever\"][\"is_correct\"] and (\n", "        not datum_by_sys[\"edit_ethanol_retriever\"][\"is_correct\"]\n", "        and datum_by_sys[\"prs.v7.raven_edit_retriever\"][\"is_correct\"]\n", "    ):\n", "        wins_for_arun.append(datum_by_sys)\n", "\n", "    if not datum_by_sys[\"no_retriever\"][\"is_correct\"] and (\n", "        datum_by_sys[\"edit_ethanol_retriever\"][\"is_correct\"]\n", "        and not datum_by_sys[\"prs.v7.raven_edit_retriever\"][\"is_correct\"]\n", "    ):\n", "        losses_for_arun.append(datum_by_sys)\n", "\n", "    if not datum_by_sys[\"no_retriever\"][\"is_correct\"] and (\n", "        # datum_by_sys[\"edit_ethanol_retriever\"][\"is_correct\"]\n", "        False\n", "        or datum_by_sys[\"prs.v7.raven_edit_retriever\"][\"is_correct\"]\n", "    ):\n", "        wins_for_retrieval.append(datum_by_sys)\n", "    elif datum_by_sys[\"no_retriever\"][\"is_correct\"] and (\n", "        # not datum_by_sys[\"edit_ethanol_retriever\"][\"is_correct\"]\n", "        True\n", "        and not datum_by_sys[\"prs.v7.raven_edit_retriever\"][\"is_correct\"]\n", "    ):\n", "        losses_for_retrieval.append(datum_by_sys)\n", "    else:\n", "        ties_for_retrieval.append(datum_by_sys)\n", "\n", "len(wins_for_arun), len(losses_for_arun), len(wins_for_retrieval), len(losses_for_retrieval), len(ties_for_retrieval)"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["len({datum[\"no_retriever\"][\"id\"].rsplit(\"_\",1)[-1] for datum in wins_for_retrieval})"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["win = losses_for_retrieval[-1]\n", "win_a = win[\"no_retriever\"]\n", "win_b = win[\"edit_ethanol_retriever\"]\n", "win_c = win[\"prs.v7.raven_edit_retriever\"]\n", "\n", "print(\"===\"*10 + f\"No retriever {win_a['is_correct']}\" + \"===\"*10)\n", "print(win_a[\"predicted_change\"])\n", "\n", "print(\"===\"*10 + f\"Edit retriever {win_b['is_correct']}\" + \"===\"*10)\n", "print(win_b[\"predicted_change\"])\n", "\n", "print(\"===\"*10 + f\"Raven retriever {win_c['is_correct']}\" + \"===\"*10)\n", "print(win_c[\"predicted_change\"])"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["print(win_b[\"prompt_tokens\"])"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["print(win_b[\"retrieved_chunks\"])"]}, {"cell_type": "code", "execution_count": 54, "metadata": {}, "outputs": [], "source": ["print(EVAL_BY_ID[\"no_instruct_problem_1001\"][\"edit_ethanol_retriever\"][\"predicted_change\"])"]}, {"cell_type": "code", "execution_count": 55, "metadata": {}, "outputs": [], "source": ["print(EVAL_BY_ID[\"no_instruct_problem_1001\"][\"no_retriever\"][\"predicted_change\"])"]}, {"cell_type": "code", "execution_count": 49, "metadata": {}, "outputs": [], "source": ["is_correct_ids = [\n", "    datum[\"id\"]\n", "    for datum in EVAL_DATA[\"no_retriever\"]\n", "    if datum[\"is_correct\"]\n", "]\n", "len(is_correct_ids)"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [], "source": ["len(EVAL_DATA[\"edit_ethanol_retriever\"])"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["load_data_dir(EVAL_DIRS[\"no_retriever\"] / \"000__no_instruct_problem_1357\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}