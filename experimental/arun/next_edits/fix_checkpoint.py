import argparse
from pathlib import Path

import torch

from research.fastbackward.utils import flatten_dict, unflatten_dict


def main(checkpoint_path: Path):
    if not checkpoint_path.is_absolute():
        checkpoint_path = Path("/mnt/efs/augment/checkpoints/") / checkpoint_path
    if checkpoint_path.is_dir():
        checkpoint_path = checkpoint_path / "consolidated.00.pth"

    print("Loading...")
    state_dict = torch.load(checkpoint_path)

    print(state_dict.keys())

    if sorted(state_dict.keys()) == ["loss_fn", "model"]:
        print("All done.")
        return

    state_dict = unflatten_dict(state_dict, max_level=1)
    if "query_model" in state_dict:
        state_dict = {
            "model": flatten_dict(
                {
                    "query_model": state_dict["query_model"],
                    "doc_model": state_dict["doc_model"],
                }
            ),
            "loss_fn": state_dict["loss_fn"],
        }
    print(f"Saving: {state_dict.keys()} to {checkpoint_path}")
    torch.save(state_dict, checkpoint_path)


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("-c", "--checkpoint_path", type=Path, required=True)
    args = parser.parse_args()
    main(args.checkpoint_path)
