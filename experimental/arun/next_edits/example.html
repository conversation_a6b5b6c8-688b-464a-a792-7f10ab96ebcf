<meta charset="UTF-8">
<head>
<link rel="stylesheet" href="/arun/next_edits/styles/monokai.css" />
</style>
</head>

<body>
<h3>{{title}}</h3>

<h4>Metrics</h4>
<div class="metrics">
<pre>{{metrics}}</pre>
</div>

<div class="example">
<h4>Prompt</h4>
<summary>
    <code>{{datum.prompt.instruction}}</code>
</summary>
{% for change in datum.prompt.past_hunks %}
<details>
    <summary class="highlight">
        <code>{{change.path}}</code> <code><span class="gu">{{change.header}}</span></code>
    </summary>
    {{render_diff(change)}}
</details>
{% endfor %}

<!-- summary of each result. -->
<h4>Targets</h4>
<details class="targets" open>
    <summary>Found {{future_hunk_location | map(attribute=1) | select("greaterthan", -1) | list | length}}/ {{future_hunk_location | length}}</summary>
{% for change, location in future_hunk_location %}
<details class="location target-location">
    <summary class="highlight">
    {{label_icons[location > -1]}}
    {{location}}↓
    <code>{{change.path}}</code>
    <code class="gu">{{change.header}}</code>
    </summary>
    {{render_diff(change)}}
</details>
{% endfor %}
</details>

<h4>Predictions</h4>
<details class="predictions" open>
    <summary>Correct {{candidate_location_correct.values() | sum}}/{{output.scored_candidates | length}}</summary>
{% for scored_location in output.scored_candidates %}
{% set candidate_label = candidate_location_correct[scored_location.item] %}
<details class="location candidate-location">
    <summary>
    {{loop.index}}.  {{label_icons[candidate_label]}}
    <code>
        {{scored_location.item.path}}
        L{{scored_location.item.range.start}}-{{scored_location.item.range.stop}}
        ({{scored_location.score}})
    </code>
    </summary>
    {% if candidate_label or loop.index < render_location_cutoff %}
    {{render_location(datum, scored_location.item, 3)}}
    {% endif %}
</details>
{% endfor %}
</details>

</body>
