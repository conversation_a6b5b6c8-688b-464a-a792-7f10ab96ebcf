{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Experiment: Group and order diff hunks"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# Load some data to work with.\n", "from pathlib import Path\n", "import pickle\n", "import pandas as pd\n", "\n", "DATA_ROOT = Path(\n", "    \"/mnt/efs/spark-data/user/arun/next-edit-location/S1.2_prs_2k.keepmost.filter\"\n", ")\n", "\n", "data = {}\n", "for path in DATA_ROOT.glob(\"*.parquet\"):\n", "    df = pd.read_parquet(path)\n", "    for record in df.to_dict(orient=\"records\"):\n", "        for datum in pickle.loads(record[\"pickled_results\"]):\n", "            data[datum.pr_meta] = datum\n", "\n", "    if len(data) > 100:\n", "        break\n", "\n", "# Load the diffs.\n", "len(data)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["from research.utils.repo_change_utils import RepoChange, patchset_from_repo_change"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [], "source": ["from base.diff_utils.diff_formatter import format_file_changes_with_ranges\n", "\n", "repo_changes = []\n", "for pr_meta, datum in data.items():\n", "    rc = datum.past_to_future_repo_change\n", "    diff = format_file_changes_with_ranges(\n", "        [f.map(lambda x: x.to_file()) for f in rc.changed_files],\n", "        5,\n", "        filter_duplicated_file_paths=False,\n", "        use_smart_header=True,\n", "    )\n", "    repo_changes.append((datum, diff))"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["import jinja2"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["prompt_template = jinja2.Template(\"\"\"\n", "You are a helpful software engineer assisting a developer who is working on a codebase\n", "to sequence the changes they have made.\n", "\n", "Here are the change the developer has made.\n", "\n", "{% for hunk in hunks %}\n", "Hunk #{{loop.index}}:\n", "```\n", "{{hunk}}\n", "```\n", "{% endfor %}\n", "\n", "Here is the commit message they want to use:\n", "\n", "{{commit_message}}\n", "\n", "What are the high-level changes made in the above diff?\n", "\n", "Group the diff hunks according to these high-level changes and arrange them in the \\\n", "order in which they would have been implemented. The output should use the following \\\n", "format:\n", "\n", "1. (description of Group 1): (first hunk# of group 1), (second hunk# of group 1) ...\n", "2. (description of Group 2): (first hunk# of group 2), (second hunk# of group 2) ...\n", "...\n", "\n", "Every hunk should be listed under at least one group. In general, prefer ordering \\\n", "implementations before their corresponding imports. \\\n", "Implementations and their tests should be ordered next to each other.\n", "\"\"\")"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["datum, ps = repo_changes[2]"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [], "source": ["import unidiff\n", "\n", "\n", "def render_hunks(ps: unidiff.PatchSet):\n", "    for pfile in ps:\n", "        header = f\"--- {pfile.source_file}\\n+++ {pfile.target_file}\\n\"\n", "        for hunk in pfile:\n", "            yield header + str(hunk)\n", "\n", "\n", "def render_message(msg: str) -> str:\n", "    return \"\\n\".join(\"> \" + line for line in msg.splitlines())\n", "\n", "\n", "def generate(client, datum, diffs):\n", "    prompt = prompt_template.render(\n", "        hunks=[diff.text for diff in diffs],\n", "        commit_message=render_message(datum.commit_meta.message),\n", "    ).strip()\n", "    print(prompt)\n", "    resp = input(\"Continue? [y]/n\").strip()\n", "    if resp and resp != \"y\":\n", "        return\n", "\n", "    response = client.generate([prompt], max_tokens=1000)\n", "    print(response)\n", "    return response"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [], "source": ["from research.llm_apis.chat_utils import LLaMA31VertexAIChatClient, Llama3ChatClient\n", "\n", "gemini_client = LLaMA31VertexAIChatClient()\n", "\n", "# client.generate([\"hello\"], max_tokens=10)\n", "llama3_client = Llama3ChatClient(\"triton\", address=\"**************:8010\", timeout=60)"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [], "source": ["generate(gemini_client, *repo_changes[0])"]}, {"cell_type": "code", "execution_count": 52, "metadata": {}, "outputs": [], "source": ["import re\n", "\n", "response = \"\"\"\\\n", "1. **Update `SetAngleLengthModal` to use `InstanceProps` and create a `createSetAngleLengthModal` function:** (1)\n", "2. **Update `SetAbsDistance` to use `createSetAngleLengthModal`:** (7)\n", "3. **Update `SetVarNameModal` to use `InstanceProps` and create a `createSetVarNameModal` function:** (3)\n", "4. **Update `useConvertToVariable` to use `createSetVarNameModal`:** (17), (18)\n", "5. **Update `GetInfoModal` to use `InstanceProps` and create a `createInfoModal` function:** (2)\n", "6. **Update `Intersect` to use `createInfoModal`:** (5), (6)\n", "7. **Update `SetAngleBetween` to use `createInfoModal`:** (9), (10), (11)\n", "8. **Update `SetHorzVertDistance` to use `createInfoModal`:** (12), (13), (14)\n", "9. **Remove unused `create` import from `Intersect`:** (4)\n", "10. **Update `setAngleLength` to use `create` from `react-modal-promise` directly:** (15), (16) \n", "\"\"\"\n", "\n", "\n", "def parse_output(output: str):\n", "    pattern = r\"^(?P<num>\\d+)\\. \\**(?P<group>[^:]+):?\\**:? (?P<hunks>.*)$\"\n", "\n", "    groups = []\n", "    # Example usage:\n", "    lines = output.splitlines()\n", "    for line in lines:\n", "        match = re.match(pattern, line)\n", "        if match:\n", "            hunks = [int(h) for h in re.findall(r\"\\d+\", match.group(\"hunks\"))]\n", "            groups.append((match.group(\"group\"), hunks))\n", "        else:\n", "            print(\"No match:\", line)\n", "    return groups"]}, {"cell_type": "code", "execution_count": 54, "metadata": {}, "outputs": [], "source": ["groups = parse_output(response)\n", "groups"]}, {"cell_type": "code", "execution_count": 56, "metadata": {}, "outputs": [], "source": ["sorted([h for _, hunks in groups for h in hunks])"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [], "source": ["generate(llama3_client, *repo_changes[0])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}