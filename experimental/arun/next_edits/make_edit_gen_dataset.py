from pathlib import Path

from base.prompt_format_next_edit.gen_prompt_formatter import EditGenFormatterConfig
from research.data.spark.pipelines.stages.next_edit_gen_pipelines import (
    NEXT_EDIT_SPARK_ROOT,
    FromGitRepos,
    FromPRsV1,
    edit_ethanol_config,
    ethanol_config,
    raven_retriever_config,
    run_stages,
)
from research.data.spark.pipelines.stages.next_edit_location_pipelines import (
    PRToRepoChangeConfig,
)
from research.data.utils.bare_repo_utils import save_bare_repo_list_disk
from research.next_edits.edit_gen_stages import (
    PromptConfig,
    RetrievalConfig,
    SamplingConfig,
)


def make_edit_gen_dataset(use_prs: bool):
    """
    Statistics from running the pipelines using 32 workers:
    - Stage 1: takes about 3 hours
    - Stage 2: takes about 12 hours
    - Stage 3: takes about 4000s
    - Training data
        - "num_sequences": 1.468M
        - "num_tokens": 7.814B
        - "tokens_per_sequence": 5321.9
        - Training data as indexed dataset takes about 20G of disk space
    """

    if use_prs:
        pr_data_path = "/mnt/efs/spark-data/shared/gh_pr_train_repartitioned"
        data_source = FromPRsV1(
            pr_data_path=Path(pr_data_path), pr_config=PRToRepoChangeConfig()
        )
        synth_instruct_path = Path(
            "/mnt/efs/spark-data/user/guy/data-pipeline/pr_instructions_v2"
        )
    else:
        repo_list_name = "100K_repos"
        repo_list_path = f"s3://next-edit/repo_lists/{repo_list_name}/"
        repo_list_path = NEXT_EDIT_SPARK_ROOT / repo_list_name
        synth_instruct_path = None
        if not repo_list_path.exists():
            save_bare_repo_list_disk(repo_list_path, max_repos=100_000)
        data_source = FromGitRepos(repo_list_path)

    run_stages(
        data_source=data_source,
        sampling_config=SamplingConfig(
            max_problems_per_repo=10000,
            max_files_per_repo=4000,
            timeout_per_repo=1200,
        ),
        sampling_config_name="10000p_4000f",
        retrieval_config=RetrievalConfig(
            retriever_config=raven_retriever_config,
            num_retrieved_chunks=128,
            timeout_per_repo=600,
            synthetic_instructions_path=synth_instruct_path,
            skip_dense_retrieval=False,
        ),
        retrieval_config_name="ravenr_synth_instruct_K128",
        prompt_config=PromptConfig(
            tokenizer_name="starcoder2",
            formatter_config=EditGenFormatterConfig(
                diff_context_lines=12,
            ),
        ),
        prompt_config_name="star2_context12",
        max_workers=128,
    )


if __name__ == "__main__":
    make_edit_gen_dataset(use_prs=True)
