"""<PERSON><PERSON><PERSON> to generate ordered diffs using an LLM."""

# Load some data to work with.
import argparse
import dataclasses
import json
import logging
import pickle
import re
from collections.abc import Iterable
from concurrent.futures import ProcessPoolExecutor, ThreadPoolExecutor
from dataclasses import dataclass
from functools import partial
from pathlib import Path

import jinja2
import pandas as pd
from tqdm import tqdm

from base.diff_utils.diff_formatter import format_file_changes_with_ranges
from research.llm_apis.chat_utils import (
    Llama3ChatClient,
    LLaMA31VertexAIChatClient,
    OpenAIAPIClient,
)
from research.next_edits.edit_localization_stages import EditLocalizationProblem
from research.next_edits.next_edits_dataset import PRMeta
from research.utils.repo_change_utils import (
    CommitMeta,
    RepoChange,
    patchset_from_repo_change,
)

logger = logging.getLogger(__name__)

DATA_ROOT = Path(
    "/mnt/efs/spark-data/user/arun/next-edit-location/S1.2_prs_2k.keepmost.filter"
)


@dataclass
class Datum:
    pr_meta: PRMeta
    commit_meta: CommitMeta
    past_to_future_repo_change: RepoChange

    @property
    def hunks(self):
        return format_file_changes_with_ranges(
            [
                f.map(lambda x: x.to_file())
                for f in self.past_to_future_repo_change.changed_files
            ],
            5,
        )


def read_shard(path: Path) -> Iterable[Datum]:
    seen_prs = set()
    try:
        df = pd.read_parquet(path)
    except OSError:
        logger.exception("Failed to read %s", path)
        return

    for record in df.to_dict(orient="records"):
        for datum in pickle.loads(record["pickled_results"]):
            datum: EditLocalizationProblem
            if datum.pr_meta in seen_prs:
                continue
            seen_prs.add(datum.pr_meta)

            datum_ = Datum(
                datum.pr_meta,
                datum.commit_meta,
                datum.past_to_future_repo_change,
            )
            if len(datum_.hunks) > 1:
                yield datum_


def load_data(data_root: Path = DATA_ROOT, limit: int = 100) -> Iterable[list[Datum]]:
    seen_prs = set()
    count = 0
    parquet_files = list(DATA_ROOT.glob("*.parquet"))
    for path in tqdm(parquet_files, desc="Loading data"):
        if count > limit:
            break
        try:
            df = pd.read_parquet(path)
        except OSError:
            logger.exception("Failed to read %s", path)
            continue
        batch = []
        for i, record in enumerate(df.to_dict(orient="records")):
            if count > limit:
                break
            for datum in pickle.loads(record["pickled_results"]):
                if count > limit:
                    break
                datum: EditLocalizationProblem
                if datum.pr_meta in seen_prs:
                    continue
                seen_prs.add(datum.pr_meta)

                datum_ = Datum(
                    datum.pr_meta,
                    datum.commit_meta,
                    datum.past_to_future_repo_change,
                )
                if len(datum_.hunks) > 1:
                    # logger.info(
                    #     "Yielding %s/%s",
                    #     datum.pr_meta.repo_name,
                    #     datum.pr_meta.pr_number,
                    # )
                    count += 1
                    yield [datum_]
                    break
                else:
                    # logger.info(
                    #     "Skipping %s/%s",
                    #     datum.pr_meta.repo_name,
                    #     datum.pr_meta.pr_number,
                    # )
                    pass
        del df
        yield batch


prompt_template = jinja2.Template("""
You are a helpful coding assistant assistant a software engineer to order the changes \
they have made to their codebase.

Here are the changes the engineer made:
{% for hunk in hunks %}
Hunk #{{loop.index}}:
```
{{hunk}}
```
{% endfor %}

Here is the commit message they want to use:

{{commit_message}}

What are the high-level changes made in the above diff?

Group the diff hunks according to these high-level changes and arrange them in the \
order in which they would have been implemented. The output should use the following \
format:

1. (description of Group 1): (first hunk# of group 1), (second hunk# of group 1) ...
2. (description of Group 2): (first hunk# of group 2), (second hunk# of group 2) ...
...

Guidelines:
* Every hunk should be part of at least one group.
* Order definitions before their uses, and uses before their imports.
* Implementations and tests should be ordered next to each other.
* Order each semantically related change together, e.g. definitions, uses and tests. A\
 single file may need to be split into multiple groups.
* Avoid generic groups like "refactoring and cleanup" or "improve readability".
* Only output the list of groups and hunks.
""")


def quote_text(text: str) -> str:
    return "\n".join("> " + line for line in text.splitlines())


def generate(client, datum: Datum):
    prompt = prompt_template.render(
        hunks=[diff.text_with_header for diff in datum.hunks],
        commit_message=quote_text(datum.commit_meta.message),
    ).strip()
    try:
        response = client.generate([prompt], max_tokens=1000)
    except Exception:
        logger.info(f"Failed to generate {datum.pr_meta}")
        return {
            "prompt": prompt,
            "response": "(Failed to generate)",
            "groups": [],
            "missing_hunks": [],
        }

    groups = parse_output(response)
    missing_hunks = list(
        set(range(1, len(datum.hunks) + 1)) - set(h for g in groups for h in g["hunks"])
    )

    logger.info(f"Generated {datum.pr_meta}")
    return {
        "pr_meta": dataclasses.asdict(datum.pr_meta),
        "prompt": prompt,
        "response": response,
        "groups": groups,
        "missing_hunks": missing_hunks,
    }


def parse_output(response: str) -> list[dict]:
    pattern = r"^(?P<num>\d+)\. \**(?P<group>[^:]+):?\**:? (?P<hunks>.*)$"

    groups = []
    # Example usage:
    lines = response.splitlines()
    for line in lines:
        if match := re.match(pattern, line):
            hunks = [int(h) for h in re.findall(r"\d+", match.group("hunks"))]
            groups.append({"group": match.group("group"), "hunks": hunks})
        else:
            logger.warning(f"Couldn't parse line: {line}")
    return groups


def process_shard(model: str, path: Path):
    client = Llama3ChatClient("triton", address="**************:8010", timeout=60)
    if model == "llama3":
        client = Llama3ChatClient("triton", address="**************:8010", timeout=60)
    else:
        assert model == "gemini"
        client = LLaMA31VertexAIChatClient()

    results = [generate(client, datum) for datum in read_shard(path)]
    logger.info("Completed shard %s", path)
    return results


def main():
    logging.basicConfig(level=logging.INFO, force=True)

    parser = argparse.ArgumentParser()
    parser.add_argument("--data-root", type=Path, default=DATA_ROOT)
    parser.add_argument("--limit", type=int, default=100)
    parser.add_argument("-m", "--model", choices=["llama3", "gemini"], required=True)
    parser.add_argument("-o", "--output", type=Path, required=True)
    parser.add_argument("-w", "--workers", type=int, default=2, required=True)
    args = parser.parse_args()

    logger.info("Generating data...")
    count = 0
    with args.output.open("w") as f, ProcessPoolExecutor(
        max_workers=args.workers
    ) as executor:
        shards = list(DATA_ROOT.glob("*.parquet"))
        for generated_examples in executor.map(
            partial(process_shard, args.model), shards
        ):
            logger.info("Saving batch of %d examples", len(generated_examples))
            for example in generated_examples:
                json.dump(example, f)
                f.write("\n")
            count += len(generated_examples)
            if count > args.limit:
                break


if __name__ == "__main__":
    main()
