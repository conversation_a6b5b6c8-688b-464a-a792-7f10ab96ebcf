"""<PERSON>ript to generate a data viewer based on the output from the next edit task."""

# TODO(1): generate diffs in order
# TODO(2): color diffs.

import argparse
import json
import pathlib
from typing import TypedDict

import jinja2
from pygments import highlight
from pygments.formatters import HtmlFormatter  # pylint:disable=no-name-in-module
from pygments.lexers import get_lexer_by_name
from tqdm import tqdm

from base.diff_utils.diff_formatter import DiffHunk
from experimental.arun.next_edits.diff_ordering.generate_ordered_diffs import (
    DATA_ROOT,
    load_data,
)


class GroupDatum(TypedDict):
    group: str
    hunks: list[int]


class Datum(TypedDict):
    prompt: str
    response: str
    groups: list[GroupDatum]
    missing_hunks: list[int]


TEMPLATE = jinja2.Template(r"""
<link rel="stylesheet" type="text/css" href="style.css">

<h2>
{% if i > 0 %}
    <a href="{{i-1}}.html">&laquo;</a>
{% endif %}
Example {{i}} / {{n}}
{% if i < n-1 %}
    <a href="{{i+1}}.html">&raquo;</a>
{% endif %}
<link rel="stylesheet" type="text/css" href="style.css">
</h2>

<details open>
    <summary>{{pr_meta.title}}</summary>
{{pr_meta.description}}
</details>

{% for group in datum.groups %}
<h3>{{loop.index}}. {{group.group}}</h3>

{% for hunk_ix in group.hunks %}
{{hunks[hunk_ix - 1]}}
{% endfor %}

{% endfor %}

<h3>Response</h3>
<pre>{{datum.response}}</pre>

<h3>Prompt</h3>
<pre>{{datum.prompt | escape}}</pre>
""")


def render_hunk(hunk: DiffHunk, style: str = "light"):
    pygments_style = {"dark": "monokai", "light": "default"}[style]

    lexer = get_lexer_by_name("diff")
    formatter = HtmlFormatter(
        style=pygments_style,
        linenos="inline",
        linenostart=hunk.after_lrange.start - 1,
    )
    return highlight(hunk.text_with_header(), lexer, formatter)


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "-i",
        "--input",
        type=pathlib.Path,
        required=True,
        help="Path to an jsonl file containing the data.",
    )
    parser.add_argument(
        "-d",
        "--data_path",
        type=pathlib.Path,
        default=DATA_ROOT,
        help="Path to an jsonl file containing the data.",
    )
    parser.add_argument(
        "-o",
        "--output_path",
        type=pathlib.Path,
        required=True,
        help="Path to an output directory to save the files.",
    )

    args = parser.parse_args()

    raw_data = [datum for data in load_data(args.data_path) for datum in data]

    with args.input.open() as f:
        data = [json.loads(line) for line in f]

    args.output_path.mkdir(parents=True, exist_ok=True)
    (args.output_path / "style.css").write_text(
        HtmlFormatter().get_style_defs(".highlight")
    )

    for i, (datum, raw_datum) in tqdm(enumerate(zip(data, raw_data)), total=len(data)):
        rendered_hunks = [render_hunk(hunk) for hunk in raw_datum.hunks]

        (args.output_path / f"{i}.html").write_text(
            TEMPLATE.render(
                datum=datum,
                hunks=rendered_hunks,
                pr_meta=raw_datum.pr_meta,
                i=i,
                n=len(data),
            )
        )


if __name__ == "__main__":
    main()
