{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Notebook to simulate making \"suggested edits.\"\n", "\n", "This notebook simulates suggested edits in the following way:\n", "\n", "1. It creates a client to our Edit Server.\n", "2. It looks at the current git diff (using the `git` library) to prepare a prompt\n", "   for the edit model.\n", "3. It uses `notebook_utils` to render the diff."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# Add augment to the path to be able to import systems client.\n", "import sys\n", "\n", "sys.path.append(\"/home/<USER>/augment\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "import json\n", "\n", "import git\n", "\n", "from services.api_proxy.client.client import AugmentClient, EditResponse\n", "\n", "# Directory to save intermediate data.\n", "DATA_DIR = Path.home() / \"Projects/suggested-edits/\"\n", "\n", "AUGMENT_ROOT = Path.home() / \"augment\"\n", "repo = git.Repo(AUGMENT_ROOT)\n", "\n", "client = AugmentClient(\"https://dogfood.api.augmentcode.com\", \"SECRET\")\n", "model = client.client_for_model(\"droid-33B-FP8-R1-edit\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# Simple way to get blob names if we want retrieval.\n", "from base.blob_names.python.blob_names import get_blob_name\n", "\n", "_BLOB_CACHE = {}\n", "_BLOB_ICACHE = {}\n", "\n", "\n", "def blob_name(blob: git.Blob) -> str:\n", "    if blob not in _BLOB_CACHE:\n", "        hsh = get_blob_name(str(blob.path), blob.data_stream.read())\n", "        _BLOB_CACHE[blob] = hsh\n", "        _BLOB_ICACHE[hsh] = blob\n", "    return _BLOB_CACHE[blob]\n", "\n", "\n", "def blob(hsh: str) -> git.Blob:\n", "    return _BLOB_ICACHE[hsh]\n", "\n", "\n", "def compute_blob_names(commit: git.Commit) -> list[str]:\n", "    \"\"\"Compute the blob names for all the file names in the repo.\"\"\"\n", "    return [\n", "        blob_name(blob)\n", "        for blob in commit.tree.traverse()\n", "        if isinstance(blob, git.Blob) and Path(blob.path).suffix == \".py\"\n", "    ]\n", "\n", "\n", "bnames = compute_blob_names(repo.head.commit)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["from dataclasses import dataclass\n", "from dataclasses_json import dataclass_json\n", "\n", "\n", "@dataclass_json\n", "@dataclass\n", "class Example:\n", "    id: str\n", "    path: str\n", "    content: str\n", "    diff: str\n", "    blob_names: list[str]\n", "\n", "\n", "def create_example(id: str, path: str) -> Example:\n", "    diff = repo.git.diff(\n", "        [\n", "            d.a_path\n", "            for d in repo.index.diff(None)\n", "            # Ignore this file from the diffs.\n", "            if d.a_path != \"experimental/arun/suggested-edits/simulator.ipynb\"\n", "        ]\n", "    )\n", "\n", "    return Example(\n", "        id=id,\n", "        path=path,\n", "        content=(AUGMENT_ROOT / path).read_text(),\n", "        diff=diff,\n", "        blob_names=compute_blob_names(repo.head.commit),\n", "    )"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["def whole_file_region(ex: Example) -> slice:\n", "    return slice(0, len(ex.content))\n", "\n", "\n", "def batched_file_regions(ex: Example, batch_size: int) -> list[slice]:\n", "    lines = ex.content.splitlines(True)\n", "\n", "    # Get line offsets\n", "    offsets = [0]\n", "    for line in lines:\n", "        offsets.append(offsets[-1] + len(line))\n", "\n", "    return [\n", "        slice(offsets[i], offsets[min(len(offsets) - 1, i + batch_size)])\n", "        for i in range(0, len(offsets), batch_size)\n", "    ]\n", "\n", "\n", "@dataclass_json\n", "@dataclass\n", "class EditRequest:\n", "    example_id: str\n", "    prefix: str\n", "    suffix: str\n", "    selected_text: str\n", "    instruction: str\n", "    path: str\n", "    blob_names: list[str]\n", "\n", "\n", "EditResponse = dataclass_json(EditResponse)\n", "\n", "\n", "def generate_edit(\n", "    ex: Example,\n", "    template: str,\n", "    regions: list[slice],\n", "    with_retrieval: bool = False,\n", ") -> list[tuple[EditRequest, EditResponse]]:\n", "    ret = []\n", "    for region in regions:\n", "        prefix = ex.content[: region.start]\n", "        selected_text = ex.content[region]\n", "        suffix = ex.content[region.stop :]\n", "\n", "        request = EditRequest(\n", "            example_id=ex.id,\n", "            selected_text=selected_text,\n", "            instruction=template.format(diff=ex.diff),\n", "            prefix=prefix,\n", "            suffix=suffix,\n", "            path=ex.path,\n", "            blob_names=ex.blob_names if with_retrieval else [],\n", "        )\n", "\n", "        ret.append(\n", "            (\n", "                request,\n", "                model.edit(\n", "                    selected_text=request.selected_text,\n", "                    instruction=request.instruction,\n", "                    prefix=request.prefix,\n", "                    suffix=request.suffix,\n", "                    path=request.path,\n", "                    blob_names=request.blob_names,\n", "                ),\n", "            )\n", "        )\n", "\n", "    return ret"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["from IPython.display import display, HTML\n", "from experimental.arun.notebook_utils import TwoWayDiff, render_twoway_diff\n", "\n", "\n", "def render_report(pairs: list[tuple[EditRequest, EditResponse]]) -> str:\n", "    request = pairs[0][0]\n", "    ret = f\"\"\"\n", "    <h1>{request.path}::{request.example_id}</h1>\n", "    <h2>Instruction</h2>\n", "    <pre>{request.instruction}</pre>\n", "    \"\"\"\n", "    for request, response in pairs:\n", "        diff = render_twoway_diff(\n", "            TwoWayDiff(\n", "                request.path,\n", "                request.prefix,\n", "                request.selected_text,\n", "                response.text,\n", "                request.suffix,\n", "            )\n", "        )\n", "        ret += f\"\"\"\n", "        <h3>{response.request_id}</h3>\n", "        <pre>@@{request.path}:{len(request.prefix.splitlines())+1}@@</pre>\n", "        {diff}\n", "        \"\"\"\n", "    return ret\n", "\n", "\n", "def run_example(\n", "    ex: Example, template: str, regions: list[slice], with_retrieval: bool = False\n", "):\n", "    pairs = generate_edit(ex, template, regions, with_retrieval)\n", "    rid = pairs[0][1].request_id\n", "    html = render_report(pairs)\n", "    (DATA_DIR / f\"{ex.id}-{rid}.html\").write_text(html)\n", "    (DATA_DIR / f\"{ex.id}-{rid}.json\").write_text(\n", "        json.dumps(\n", "            {\n", "                \"example\": ex.to_dict(),\n", "                \"requests\": [req.to_dict() for req, _ in pairs],\n", "                \"responses\": [resp.to_dict() for _, resp in pairs],\n", "            }\n", "        )\n", "    )\n", "    display(HTML(html))"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["TEMPLATES = {}\n", "\n", "\n", "TEMPLATES[\"use-recent-changes\"] = \"\"\"\\\n", "Here are some recent changes in diff format:\n", "\n", "```\n", "{diff}\n", "```\n", "\n", "Update the selected code based on the above changes. Don't make any other changes.\n", "\"\"\"\n", "\n", "TEMPLATES[\"use-recent-changes-v2\"] = \"\"\"\\\n", "Here are some recent changes in diff format:\n", "\n", "```\n", "{diff}\n", "```\n", "\n", "Update the selected code based on the above changes, e.g. by importing any new packages\n", "introduced by the changes.\n", "\"\"\"\n", "\n", "TEMPLATES[\"targeted-instruction\"] = \"\"\"\\\n", "Update any uses of `get_target_span` based on the following change:\n", "\n", "```\n", "{diff}\n", "```\n", "\n", "Don't make any other changes.\n", "\"\"\"\n", "\n", "TEMPLATES[\"targeted-instruction-v2\"] = \"\"\"\\\n", "Make the function yield instead of return:\n", "\n", "```\n", "{diff}\n", "```\n", "\n", "Don't make any other changes.\n", "\"\"\"\n", "\n", "TEMPLATES[\"targeted-instruction-v3\"] = \"\"\"\\\n", "Here are some recent changes:\n", "\n", "```\n", "{diff}\n", "```\n", "\n", "Update code using the above data class.\n", "\"\"\"\n", "\n", "\n", "# template = TEMPLATES[\"use-recent-changes\"]\n", "template = TEMPLATES[\"targeted-instruction-v3\"]"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["ex = create_example(\n", "    \"add-field-to-dataclass\", \"research/eval/vulcan/testdata/py/fim_dataclass_attrs.py\"\n", ")\n", "# print(ex.diff)\n", "run_example(ex, template, batched_file_regions(ex, 40), True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.5"}}, "nbformat": 4, "nbformat_minor": 2}