{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "# %%"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from research.environments import get_eng_secret\n", "import experimental.slack_feedback_summarizer.slack_utils as su\n", "import experimental.slack_feedback_summarizer.slack_feedback_summarizer as sfs"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["TOKEN = get_eng_secret(\"research-slack-search-token\", namespace=\"gcp-us1\")\n", "client = su.SlackClient(token=TOKEN)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["channel_id = client.get_channel_id(\"feedback-agents-extension\")"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"data": {"text/plain": ["148"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["import datetime\n", "\n", "start_date = datetime.datetime.strptime(\"2025-04-28\", \"%Y-%m-%d\")\n", "end_date = datetime.datetime.strptime(\"2025-04-29\", \"%Y-%m-%d\")\n", "messages = client.get_messages(channel_id, start_date, end_date)\n", "len(messages)"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[2m2025-04-28 21:34:36\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mAnthropic Direct Client initialized\u001b[0m \u001b[36manthropic_target\u001b[0m=\u001b[35manthropic_direct\u001b[0m \u001b[36mclient_type\u001b[0m=\u001b[35manthropic_direct\u001b[0m \u001b[36mmodel_name\u001b[0m=\u001b[35mclaude-3-5-haiku-20241022\u001b[0m\n"]}], "source": ["sf_client = sfs.SlackFeedbackSummarizer(get_eng_secret(\"seal-research-anthropic-key\"))"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [{"data": {"text/plain": ["SlackMessage(text=':memo: Received User Feedback For Request <https://support.d7.t.us-central1.prod.augmentcode.com/t/d7-discovery6/request/31629d4c-f38d-44cd-b5df-11915fa31d23|31629d4c-f38d-44cd-b5df-11915fa31d23> (Request Access From <https://genie.us-central1.prod.augmentcode.com/?tab=2&amp;tenant=d7-discovery6&amp;redirect_url=https%3A//support.d7.t.us-central1.prod.augmentcode.com/t/d7-discovery6/request/31629d4c-f38d-44cd-b5df-11915fa31d23|Genie>)\\nTenant: `d7-discovery6` | Rating: `POSITIVE` | Note:\"informative run-down of the issues\"', user='U04NGAL63S6', ts='1745875937.305499', permalink='https://slack.com/archives/C08HWKMJGGY/p1745875937305499', thread_ts=None, replies=[])"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["messages[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["summaries = [sf_client.summarize_thread(m) for m in messages[:10]]"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["User provided positive feedback about an informative explanation of issues. The feedback was rated as 'POSITIVE' with a note appreciating the run-down of the issues.\n", "User provided positive feedback about a project overview, expressing appreciation for the presentation or content.\n", "User is experiencing persistent performance issues with an AI request that appears to be stuck loading indefinitely. The request to create a file is not completing consistently, and the user reports intermittent functionality when attempting to continue the request.\n", "User is experiencing performance issues with the AI agent extension, including 400 errors, slower performance, and unexpected usage limit errors despite having a paid subscription plan. The service was reportedly better during the free trial period.\n", "User provided positive feedback, rating the experience as 'Excellent'. The feedback appears to be a general commendation with no specific issues or requests.\n", "User reported a negative experience with the AI agent, expressing frustration that something keeps stopping or pausing (in Chinese: 'Why does it keep stopping?!'). This suggests a potential performance or functionality interruption in the system.\n", "User provided positive feedback, indicating willingness or agreement with something related to the AI agent extension. The feedback appears to be affirmative and supportive.\n", "User encountered an unexpected text length restriction when attempting to write a short message, preventing them from submitting their input. The system appears to be incorrectly enforcing a text limit.\n", "User provided negative feedback indicating that the AI agent did not follow the given instructions. The specific details of the instruction non-compliance are not elaborated in the message.\n", "User reported a language change issue where the language does not update across all dashboard pages. The feedback is in Arabic and indicates a localization or language switching problem.\n"]}], "source": ["for s in summaries:\n", "    print(s.summary)"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[2m2025-04-28 21:41:19\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mgenerating response for 0 messages\u001b[0m \u001b[36manthropic_target\u001b[0m=\u001b[35manthropic_direct\u001b[0m \u001b[36mclient_type\u001b[0m=\u001b[35manthropic_direct\u001b[0m\n", "\u001b[2m2025-04-28 21:41:19\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mtool_choice_param: NOT_GIVEN  \u001b[0m \u001b[36manthropic_target\u001b[0m=\u001b[35manthropic_direct\u001b[0m \u001b[36mclient_type\u001b[0m=\u001b[35manthropic_direct\u001b[0m\n", "\u001b[2m2025-04-28 21:41:20\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mPrompt cache usage: total input_tokens=732, input_tokens=732, cache_read_input_tokens=0, cache_creation_input_tokens=0, cache_ratio=0.00, service_tier=standard\u001b[0m \u001b[36manthropic_target\u001b[0m=\u001b[35manthropic_direct\u001b[0m \u001b[36mclient_type\u001b[0m=\u001b[35manthropic_direct\u001b[0m \u001b[36mmodel_caller\u001b[0m=\u001b[35mscript\u001b[0m \u001b[36mmodel_name\u001b[0m=\u001b[35mclaude-3-5-haiku-20241022\u001b[0m\n", "\u001b[2m2025-04-28 21:41:20\u001b[0m [\u001b[32m\u001b[1mdebug    \u001b[0m] \u001b[1mcontent_block_start           \u001b[0m \u001b[36manthropic_target\u001b[0m=\u001b[35manthropic_direct\u001b[0m \u001b[36mclient_type\u001b[0m=\u001b[35manthropic_direct\u001b[0m\n", "\u001b[2m2025-04-28 21:41:20\u001b[0m [\u001b[32m\u001b[1mdebug    \u001b[0m] \u001b[1mcontent_block_delta           \u001b[0m \u001b[36manthropic_target\u001b[0m=\u001b[35manthropic_direct\u001b[0m \u001b[36mclient_type\u001b[0m=\u001b[35manthropic_direct\u001b[0m\n", "\u001b[2m2025-04-28 21:41:20\u001b[0m [\u001b[32m\u001b[1mdebug    \u001b[0m] \u001b[1mcontent_block_delta           \u001b[0m \u001b[36manthropic_target\u001b[0m=\u001b[35manthropic_direct\u001b[0m \u001b[36mclient_type\u001b[0m=\u001b[35manthropic_direct\u001b[0m\n", "\u001b[2m2025-04-28 21:41:20\u001b[0m [\u001b[32m\u001b[1mdebug    \u001b[0m] \u001b[1mcontent_block_delta           \u001b[0m \u001b[36manthropic_target\u001b[0m=\u001b[35manthropic_direct\u001b[0m \u001b[36mclient_type\u001b[0m=\u001b[35manthropic_direct\u001b[0m\n", "\u001b[2m2025-04-28 21:41:20\u001b[0m [\u001b[32m\u001b[1mdebug    \u001b[0m] \u001b[1mcontent_block_delta           \u001b[0m \u001b[36manthropic_target\u001b[0m=\u001b[35manthropic_direct\u001b[0m \u001b[36mclient_type\u001b[0m=\u001b[35manthropic_direct\u001b[0m\n", "\u001b[2m2025-04-28 21:41:20\u001b[0m [\u001b[32m\u001b[1mdebug    \u001b[0m] \u001b[1mcontent_block_delta           \u001b[0m \u001b[36manthropic_target\u001b[0m=\u001b[35manthropic_direct\u001b[0m \u001b[36mclient_type\u001b[0m=\u001b[35manthropic_direct\u001b[0m\n", "\u001b[2m2025-04-28 21:41:20\u001b[0m [\u001b[32m\u001b[1mdebug    \u001b[0m] \u001b[1mcontent_block_delta           \u001b[0m \u001b[36manthropic_target\u001b[0m=\u001b[35manthropic_direct\u001b[0m \u001b[36mclient_type\u001b[0m=\u001b[35manthropic_direct\u001b[0m\n", "\u001b[2m2025-04-28 21:41:20\u001b[0m [\u001b[32m\u001b[1mdebug    \u001b[0m] \u001b[1mcontent_block_delta           \u001b[0m \u001b[36manthropic_target\u001b[0m=\u001b[35manthropic_direct\u001b[0m \u001b[36mclient_type\u001b[0m=\u001b[35manthropic_direct\u001b[0m\n", "\u001b[2m2025-04-28 21:41:20\u001b[0m [\u001b[32m\u001b[1mdebug    \u001b[0m] \u001b[1mcontent_block_delta           \u001b[0m \u001b[36manthropic_target\u001b[0m=\u001b[35manthropic_direct\u001b[0m \u001b[36mclient_type\u001b[0m=\u001b[35manthropic_direct\u001b[0m\n", "\u001b[2m2025-04-28 21:41:20\u001b[0m [\u001b[32m\u001b[1mdebug    \u001b[0m] \u001b[1mcontent_block_delta           \u001b[0m \u001b[36manthropic_target\u001b[0m=\u001b[35manthropic_direct\u001b[0m \u001b[36mclient_type\u001b[0m=\u001b[35manthropic_direct\u001b[0m\n", "\u001b[2m2025-04-28 21:41:20\u001b[0m [\u001b[32m\u001b[1mdebug    \u001b[0m] \u001b[1mcontent_block_delta           \u001b[0m \u001b[36manthropic_target\u001b[0m=\u001b[35manthropic_direct\u001b[0m \u001b[36mclient_type\u001b[0m=\u001b[35manthropic_direct\u001b[0m\n", "\u001b[2m2025-04-28 21:41:20\u001b[0m [\u001b[32m\u001b[1mdebug    \u001b[0m] \u001b[1mcontent_block_delta           \u001b[0m \u001b[36manthropic_target\u001b[0m=\u001b[35manthropic_direct\u001b[0m \u001b[36mclient_type\u001b[0m=\u001b[35manthropic_direct\u001b[0m\n", "\u001b[2m2025-04-28 21:41:20\u001b[0m [\u001b[32m\u001b[1mdebug    \u001b[0m] \u001b[1mcontent_block_delta           \u001b[0m \u001b[36manthropic_target\u001b[0m=\u001b[35manthropic_direct\u001b[0m \u001b[36mclient_type\u001b[0m=\u001b[35manthropic_direct\u001b[0m\n", "\u001b[2m2025-04-28 21:41:20\u001b[0m [\u001b[32m\u001b[1mdebug    \u001b[0m] \u001b[1mcontent_block_delta           \u001b[0m \u001b[36manthropic_target\u001b[0m=\u001b[35manthropic_direct\u001b[0m \u001b[36mclient_type\u001b[0m=\u001b[35manthropic_direct\u001b[0m\n", "\u001b[2m2025-04-28 21:41:20\u001b[0m [\u001b[32m\u001b[1mdebug    \u001b[0m] \u001b[1mcontent_block_delta           \u001b[0m \u001b[36manthropic_target\u001b[0m=\u001b[35manthropic_direct\u001b[0m \u001b[36mclient_type\u001b[0m=\u001b[35manthropic_direct\u001b[0m\n", "\u001b[2m2025-04-28 21:41:20\u001b[0m [\u001b[32m\u001b[1mdebug    \u001b[0m] \u001b[1mcontent_block_delta           \u001b[0m \u001b[36manthropic_target\u001b[0m=\u001b[35manthropic_direct\u001b[0m \u001b[36mclient_type\u001b[0m=\u001b[35manthropic_direct\u001b[0m\n", "\u001b[2m2025-04-28 21:41:20\u001b[0m [\u001b[32m\u001b[1mdebug    \u001b[0m] \u001b[1mcontent_block_delta           \u001b[0m \u001b[36manthropic_target\u001b[0m=\u001b[35manthropic_direct\u001b[0m \u001b[36mclient_type\u001b[0m=\u001b[35manthropic_direct\u001b[0m\n", "\u001b[2m2025-04-28 21:41:20\u001b[0m [\u001b[32m\u001b[1mdebug    \u001b[0m] \u001b[1mcontent_block_delta           \u001b[0m \u001b[36manthropic_target\u001b[0m=\u001b[35manthropic_direct\u001b[0m \u001b[36mclient_type\u001b[0m=\u001b[35manthropic_direct\u001b[0m\n", "\u001b[2m2025-04-28 21:41:21\u001b[0m [\u001b[32m\u001b[1mdebug    \u001b[0m] \u001b[1mcontent_block_delta           \u001b[0m \u001b[36manthropic_target\u001b[0m=\u001b[35manthropic_direct\u001b[0m \u001b[36mclient_type\u001b[0m=\u001b[35manthropic_direct\u001b[0m\n", "\u001b[2m2025-04-28 21:41:21\u001b[0m [\u001b[32m\u001b[1mdebug    \u001b[0m] \u001b[1mcontent_block_delta           \u001b[0m \u001b[36manthropic_target\u001b[0m=\u001b[35manthropic_direct\u001b[0m \u001b[36mclient_type\u001b[0m=\u001b[35manthropic_direct\u001b[0m\n", "\u001b[2m2025-04-28 21:41:21\u001b[0m [\u001b[32m\u001b[1mdebug    \u001b[0m] \u001b[1mcontent_block_delta           \u001b[0m \u001b[36manthropic_target\u001b[0m=\u001b[35manthropic_direct\u001b[0m \u001b[36mclient_type\u001b[0m=\u001b[35manthropic_direct\u001b[0m\n", "\u001b[2m2025-04-28 21:41:21\u001b[0m [\u001b[32m\u001b[1mdebug    \u001b[0m] \u001b[1mcontent_block_delta           \u001b[0m \u001b[36manthropic_target\u001b[0m=\u001b[35manthropic_direct\u001b[0m \u001b[36mclient_type\u001b[0m=\u001b[35manthropic_direct\u001b[0m\n", "\u001b[2m2025-04-28 21:41:21\u001b[0m [\u001b[32m\u001b[1mdebug    \u001b[0m] \u001b[1mcontent_block_delta           \u001b[0m \u001b[36manthropic_target\u001b[0m=\u001b[35manthropic_direct\u001b[0m \u001b[36mclient_type\u001b[0m=\u001b[35manthropic_direct\u001b[0m\n", "\u001b[2m2025-04-28 21:41:21\u001b[0m [\u001b[32m\u001b[1mdebug    \u001b[0m] \u001b[1mcontent_block_delta           \u001b[0m \u001b[36manthropic_target\u001b[0m=\u001b[35manthropic_direct\u001b[0m \u001b[36mclient_type\u001b[0m=\u001b[35manthropic_direct\u001b[0m\n", "\u001b[2m2025-04-28 21:41:21\u001b[0m [\u001b[32m\u001b[1mdebug    \u001b[0m] \u001b[1mcontent_block_delta           \u001b[0m \u001b[36manthropic_target\u001b[0m=\u001b[35manthropic_direct\u001b[0m \u001b[36mclient_type\u001b[0m=\u001b[35manthropic_direct\u001b[0m\n", "\u001b[2m2025-04-28 21:41:21\u001b[0m [\u001b[32m\u001b[1mdebug    \u001b[0m] \u001b[1mcontent_block_delta           \u001b[0m \u001b[36manthropic_target\u001b[0m=\u001b[35manthropic_direct\u001b[0m \u001b[36mclient_type\u001b[0m=\u001b[35manthropic_direct\u001b[0m\n", "\u001b[2m2025-04-28 21:41:21\u001b[0m [\u001b[32m\u001b[1mdebug    \u001b[0m] \u001b[1mcontent_block_delta           \u001b[0m \u001b[36manthropic_target\u001b[0m=\u001b[35manthropic_direct\u001b[0m \u001b[36mclient_type\u001b[0m=\u001b[35manthropic_direct\u001b[0m\n", "\u001b[2m2025-04-28 21:41:21\u001b[0m [\u001b[32m\u001b[1mdebug    \u001b[0m] \u001b[1mcontent_block_delta           \u001b[0m \u001b[36manthropic_target\u001b[0m=\u001b[35manthropic_direct\u001b[0m \u001b[36mclient_type\u001b[0m=\u001b[35manthropic_direct\u001b[0m\n", "\u001b[2m2025-04-28 21:41:21\u001b[0m [\u001b[32m\u001b[1mdebug    \u001b[0m] \u001b[1mcontent_block_delta           \u001b[0m \u001b[36manthropic_target\u001b[0m=\u001b[35manthropic_direct\u001b[0m \u001b[36mclient_type\u001b[0m=\u001b[35manthropic_direct\u001b[0m\n", "\u001b[2m2025-04-28 21:41:21\u001b[0m [\u001b[32m\u001b[1mdebug    \u001b[0m] \u001b[1mcontent_block_delta           \u001b[0m \u001b[36manthropic_target\u001b[0m=\u001b[35manthropic_direct\u001b[0m \u001b[36mclient_type\u001b[0m=\u001b[35manthropic_direct\u001b[0m\n", "\u001b[2m2025-04-28 21:41:21\u001b[0m [\u001b[32m\u001b[1mdebug    \u001b[0m] \u001b[1mcontent_block_delta           \u001b[0m \u001b[36manthropic_target\u001b[0m=\u001b[35manthropic_direct\u001b[0m \u001b[36mclient_type\u001b[0m=\u001b[35manthropic_direct\u001b[0m\n", "\u001b[2m2025-04-28 21:41:21\u001b[0m [\u001b[32m\u001b[1mdebug    \u001b[0m] \u001b[1mcontent_block_delta           \u001b[0m \u001b[36manthropic_target\u001b[0m=\u001b[35manthropic_direct\u001b[0m \u001b[36mclient_type\u001b[0m=\u001b[35manthropic_direct\u001b[0m\n", "\u001b[2m2025-04-28 21:41:21\u001b[0m [\u001b[32m\u001b[1mdebug    \u001b[0m] \u001b[1mcontent_block_delta           \u001b[0m \u001b[36manthropic_target\u001b[0m=\u001b[35manthropic_direct\u001b[0m \u001b[36mclient_type\u001b[0m=\u001b[35manthropic_direct\u001b[0m\n", "\u001b[2m2025-04-28 21:41:21\u001b[0m [\u001b[32m\u001b[1mdebug    \u001b[0m] \u001b[1mcontent_block_delta           \u001b[0m \u001b[36manthropic_target\u001b[0m=\u001b[35manthropic_direct\u001b[0m \u001b[36mclient_type\u001b[0m=\u001b[35manthropic_direct\u001b[0m\n", "\u001b[2m2025-04-28 21:41:21\u001b[0m [\u001b[32m\u001b[1mdebug    \u001b[0m] \u001b[1mcontent_block_delta           \u001b[0m \u001b[36manthropic_target\u001b[0m=\u001b[35manthropic_direct\u001b[0m \u001b[36mclient_type\u001b[0m=\u001b[35manthropic_direct\u001b[0m\n", "\u001b[2m2025-04-28 21:41:21\u001b[0m [\u001b[32m\u001b[1mdebug    \u001b[0m] \u001b[1mcontent_block_delta           \u001b[0m \u001b[36manthropic_target\u001b[0m=\u001b[35manthropic_direct\u001b[0m \u001b[36mclient_type\u001b[0m=\u001b[35manthropic_direct\u001b[0m\n", "\u001b[2m2025-04-28 21:41:21\u001b[0m [\u001b[32m\u001b[1mdebug    \u001b[0m] \u001b[1mcontent_block_delta           \u001b[0m \u001b[36manthropic_target\u001b[0m=\u001b[35manthropic_direct\u001b[0m \u001b[36mclient_type\u001b[0m=\u001b[35manthropic_direct\u001b[0m\n", "\u001b[2m2025-04-28 21:41:21\u001b[0m [\u001b[32m\u001b[1mdebug    \u001b[0m] \u001b[1mcontent_block_delta           \u001b[0m \u001b[36manthropic_target\u001b[0m=\u001b[35manthropic_direct\u001b[0m \u001b[36mclient_type\u001b[0m=\u001b[35manthropic_direct\u001b[0m\n", "\u001b[2m2025-04-28 21:41:21\u001b[0m [\u001b[32m\u001b[1mdebug    \u001b[0m] \u001b[1mcontent_block_delta           \u001b[0m \u001b[36manthropic_target\u001b[0m=\u001b[35manthropic_direct\u001b[0m \u001b[36mclient_type\u001b[0m=\u001b[35manthropic_direct\u001b[0m\n", "\u001b[2m2025-04-28 21:41:21\u001b[0m [\u001b[32m\u001b[1mdebug    \u001b[0m] \u001b[1mcontent_block_delta           \u001b[0m \u001b[36manthropic_target\u001b[0m=\u001b[35manthropic_direct\u001b[0m \u001b[36mclient_type\u001b[0m=\u001b[35manthropic_direct\u001b[0m\n", "\u001b[2m2025-04-28 21:41:22\u001b[0m [\u001b[32m\u001b[1mdebug    \u001b[0m] \u001b[1mcontent_block_delta           \u001b[0m \u001b[36manthropic_target\u001b[0m=\u001b[35manthropic_direct\u001b[0m \u001b[36mclient_type\u001b[0m=\u001b[35manthropic_direct\u001b[0m\n", "\u001b[2m2025-04-28 21:41:22\u001b[0m [\u001b[32m\u001b[1mdebug    \u001b[0m] \u001b[1mcontent_block_delta           \u001b[0m \u001b[36manthropic_target\u001b[0m=\u001b[35manthropic_direct\u001b[0m \u001b[36mclient_type\u001b[0m=\u001b[35manthropic_direct\u001b[0m\n", "\u001b[2m2025-04-28 21:41:22\u001b[0m [\u001b[32m\u001b[1mdebug    \u001b[0m] \u001b[1mcontent_block_delta           \u001b[0m \u001b[36manthropic_target\u001b[0m=\u001b[35manthropic_direct\u001b[0m \u001b[36mclient_type\u001b[0m=\u001b[35manthropic_direct\u001b[0m\n", "\u001b[2m2025-04-28 21:41:22\u001b[0m [\u001b[32m\u001b[1mdebug    \u001b[0m] \u001b[1mcontent_block_delta           \u001b[0m \u001b[36manthropic_target\u001b[0m=\u001b[35manthropic_direct\u001b[0m \u001b[36mclient_type\u001b[0m=\u001b[35manthropic_direct\u001b[0m\n", "\u001b[2m2025-04-28 21:41:22\u001b[0m [\u001b[32m\u001b[1mdebug    \u001b[0m] \u001b[1mcontent_block_delta           \u001b[0m \u001b[36manthropic_target\u001b[0m=\u001b[35manthropic_direct\u001b[0m \u001b[36mclient_type\u001b[0m=\u001b[35manthropic_direct\u001b[0m\n", "\u001b[2m2025-04-28 21:41:22\u001b[0m [\u001b[32m\u001b[1mdebug    \u001b[0m] \u001b[1mcontent_block_delta           \u001b[0m \u001b[36manthropic_target\u001b[0m=\u001b[35manthropic_direct\u001b[0m \u001b[36mclient_type\u001b[0m=\u001b[35manthropic_direct\u001b[0m\n", "\u001b[2m2025-04-28 21:41:22\u001b[0m [\u001b[32m\u001b[1mdebug    \u001b[0m] \u001b[1mcontent_block_delta           \u001b[0m \u001b[36manthropic_target\u001b[0m=\u001b[35manthropic_direct\u001b[0m \u001b[36mclient_type\u001b[0m=\u001b[35manthropic_direct\u001b[0m\n", "\u001b[2m2025-04-28 21:41:22\u001b[0m [\u001b[32m\u001b[1mdebug    \u001b[0m] \u001b[1mcontent_block_delta           \u001b[0m \u001b[36manthropic_target\u001b[0m=\u001b[35manthropic_direct\u001b[0m \u001b[36mclient_type\u001b[0m=\u001b[35manthropic_direct\u001b[0m\n", "\u001b[2m2025-04-28 21:41:22\u001b[0m [\u001b[32m\u001b[1mdebug    \u001b[0m] \u001b[1mcontent_block_delta           \u001b[0m \u001b[36manthropic_target\u001b[0m=\u001b[35manthropic_direct\u001b[0m \u001b[36mclient_type\u001b[0m=\u001b[35manthropic_direct\u001b[0m\n", "\u001b[2m2025-04-28 21:41:22\u001b[0m [\u001b[32m\u001b[1mdebug    \u001b[0m] \u001b[1mcontent_block_delta           \u001b[0m \u001b[36manthropic_target\u001b[0m=\u001b[35manthropic_direct\u001b[0m \u001b[36mclient_type\u001b[0m=\u001b[35manthropic_direct\u001b[0m\n", "\u001b[2m2025-04-28 21:41:22\u001b[0m [\u001b[32m\u001b[1mdebug    \u001b[0m] \u001b[1mcontent_block_delta           \u001b[0m \u001b[36manthropic_target\u001b[0m=\u001b[35manthropic_direct\u001b[0m \u001b[36mclient_type\u001b[0m=\u001b[35manthropic_direct\u001b[0m\n", "\u001b[2m2025-04-28 21:41:22\u001b[0m [\u001b[32m\u001b[1mdebug    \u001b[0m] \u001b[1mcontent_block_delta           \u001b[0m \u001b[36manthropic_target\u001b[0m=\u001b[35manthropic_direct\u001b[0m \u001b[36mclient_type\u001b[0m=\u001b[35manthropic_direct\u001b[0m\n", "\u001b[2m2025-04-28 21:41:22\u001b[0m [\u001b[32m\u001b[1mdebug    \u001b[0m] \u001b[1mcontent_block_delta           \u001b[0m \u001b[36manthropic_target\u001b[0m=\u001b[35manthropic_direct\u001b[0m \u001b[36mclient_type\u001b[0m=\u001b[35manthropic_direct\u001b[0m\n", "\u001b[2m2025-04-28 21:41:22\u001b[0m [\u001b[32m\u001b[1mdebug    \u001b[0m] \u001b[1mcontent_block_delta           \u001b[0m \u001b[36manthropic_target\u001b[0m=\u001b[35manthropic_direct\u001b[0m \u001b[36mclient_type\u001b[0m=\u001b[35manthropic_direct\u001b[0m\n", "\u001b[2m2025-04-28 21:41:22\u001b[0m [\u001b[32m\u001b[1mdebug    \u001b[0m] \u001b[1mmessage_delta                 \u001b[0m \u001b[36manthropic_target\u001b[0m=\u001b[35manthropic_direct\u001b[0m \u001b[36mclient_type\u001b[0m=\u001b[35manthropic_direct\u001b[0m\n", "\u001b[2m2025-04-28 21:41:22\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGeneration complete: output_tokens=186, tools_used=0\u001b[0m \u001b[36manthropic_target\u001b[0m=\u001b[35manthropic_direct\u001b[0m \u001b[36mclient_type\u001b[0m=\u001b[35manthropic_direct\u001b[0m\n"]}, {"data": {"text/plain": ["4"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["groups = sf_client.group_threads_by_theme(summaries)\n", "len(groups)"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"data": {"text/plain": ["[ThreadSummaryGroup(name='Language and Input Constraints', category='ui/ux issue', threads=[ThreadSummary(original_message=SlackMessage(text=':memo: Received User Feedback For Request <https://support.d12.t.us-central1.prod.augmentcode.com/t/d12-discovery0/request/4997ed8a-7db3-4bea-8377-81df8360a94c|4997ed8a-7db3-4bea-8377-81df8360a94c> (Request Access From <https://genie.us-central1.prod.augmentcode.com/?tab=2&amp;tenant=d12-discovery0&amp;redirect_url=https%3A//support.d12.t.us-central1.prod.augmentcode.com/t/d12-discovery0/request/4997ed8a-7db3-4bea-8377-81df8360a94c|Genie>)\\nTenant: `d12-discovery0` | Rating: `NEGATIVE` | Note:\"i wrote one short message and it says the text exceeds the allowable limit???\"', user='U04NGAL63S6', ts='1745871525.015509', permalink='https://slack.com/archives/C08HWKMJGGY/p1745871525015509', thread_ts=None, replies=[]), summary='User encountered an unexpected text length restriction when attempting to write a short message, preventing them from submitting their input. The system appears to be incorrectly enforcing a text limit.', status='Unresolved', type='Bug report', request_id='4997ed8a-7db3-4bea-8377-81df8360a94c', share_url=None), ThreadSummary(original_message=SlackMessage(text=':memo: Received User Feedback For Request <https://support.d19.t.us-central1.prod.augmentcode.com/t/d19-discovery1/request/d5748b1f-ed99-4833-bc9e-4e2da7467e57|d5748b1f-ed99-4833-bc9e-4e2da7467e57> (Request Access From <https://genie.us-central1.prod.augmentcode.com/?tab=2&amp;tenant=d19-discovery1&amp;redirect_url=https%3A//support.d19.t.us-central1.prod.augmentcode.com/t/d19-discovery1/request/d5748b1f-ed99-4833-bc9e-4e2da7467e57|Genie>)\\nTenant: `d19-discovery1` | Rating: `NEGATIVE` | Note:\"لايتم تغيير اللغة في في حميع صفحات لوحة التحكم\"', user='U04NGAL63S6', ts='1745870431.155279', permalink='https://slack.com/archives/C08HWKMJGGY/p1745870431155279', thread_ts=None, replies=[]), summary='User reported a language change issue where the language does not update across all dashboard pages. The feedback is in Arabic and indicates a localization or language switching problem.', status='Unresolved', type='Bug report', request_id='d5748b1f-ed99-4833-bc9e-4e2da7467e57', share_url=None)]),\n", " ThreadSummaryGroup(name='Performance and Loading Problems', category='stability issue', threads=[ThreadSummary(original_message=SlackMessage(text=':memo: Received User Feedback For Request <https://support.d8.t.us-central1.prod.augmentcode.com/t/d8-discovery7/request/5038fba2-e0ca-4af8-95df-68818a6d2995|5038fba2-e0ca-4af8-95df-68818a6d2995> (Request Access From <https://genie.us-central1.prod.augmentcode.com/?tab=2&amp;tenant=d8-discovery7&amp;redirect_url=https%3A//support.d8.t.us-central1.prod.augmentcode.com/t/d8-discovery7/request/5038fba2-e0ca-4af8-95df-68818a6d2995|Genie>)\\nTenant: `d8-discovery7` | Rating: `NEGATIVE` | Note:\"it\\'s taking forever for this request to finish or even progress. It was suppose to just create a file, but it keeps running, loading forever. \\nI got this a lot, every time i stopped the request, then type continue. It works sometimes, sometimes it does never works no matter how many times i do this.\"', user='U04NGAL63S6', ts='1745873028.310089', permalink='https://slack.com/archives/C08HWKMJGGY/p1745873028310089', thread_ts=None, replies=[]), summary='User is experiencing persistent performance issues with an AI request that appears to be stuck loading indefinitely. The request to create a file is not completing consistently, and the user reports intermittent functionality when attempting to continue the request.', status='Unresolved', type='Bug report', request_id='5038fba2-e0ca-4af8-95df-68818a6d2995', share_url=None), ThreadSummary(original_message=SlackMessage(text=':memo: Received User Feedback For Request <https://support.d14.t.us-central1.prod.augmentcode.com/t/d14-discovery0/request/05dfee86-41fc-4fdb-b35f-00a6070c7671|05dfee86-41fc-4fdb-b35f-00a6070c7671> (Request Access From <https://genie.us-central1.prod.augmentcode.com/?tab=2&amp;tenant=d14-discovery0&amp;redirect_url=https%3A//support.d14.t.us-central1.prod.augmentcode.com/t/d14-discovery0/request/05dfee86-41fc-4fdb-b35f-00a6070c7671|Genie>)\\nTenant: `d14-discovery0` | Rating: `NEGATIVE` | Note:\"esta dando erro 400, esta mais lento e dando erro de limites sendo que eu assinei o plano, quando era no periodo gratuito de teste esta muito melhor\"', user='U04NGAL63S6', ts='1745872974.212179', permalink='https://slack.com/archives/C08HWKMJGGY/p1745872974212179', thread_ts=None, replies=[]), summary='User is experiencing performance issues with the AI agent extension, including 400 errors, slower performance, and unexpected usage limit errors despite having a paid subscription plan. The service was reportedly better during the free trial period.', status='Unresolved', type='Bug report', request_id='05dfee86-41fc-4fdb-b35f-00a6070c7671', share_url=None), ThreadSummary(original_message=SlackMessage(text=':memo: Received User Feedback For Request <https://support.d13.t.us-central1.prod.augmentcode.com/t/d13-discovery5/request/4f450448-de09-48fb-92ab-9584350469db|4f450448-de09-48fb-92ab-9584350469db> (Request Access From <https://genie.us-central1.prod.augmentcode.com/?tab=2&amp;tenant=d13-discovery5&amp;redirect_url=https%3A//support.d13.t.us-central1.prod.augmentcode.com/t/d13-discovery5/request/4f450448-de09-48fb-92ab-9584350469db|Genie>)\\nTenant: `d13-discovery5` | Rating: `NEGATIVE` | Note:\"怎么老停下来？！\"', user='U04NGAL63S6', ts='1745872702.516119', permalink='https://slack.com/archives/C08HWKMJGGY/p1745872702516119', thread_ts=None, replies=[]), summary=\"User reported a negative experience with the AI agent, expressing frustration that something keeps stopping or pausing (in Chinese: 'Why does it keep stopping?!'). This suggests a potential performance or functionality interruption in the system.\", status='Unresolved', type='Bug report', request_id='4f450448-de09-48fb-92ab-9584350469db', share_url=None)]),\n", " ThreadSummaryGroup(name='Instruction Following and Response Accuracy', category='model quality issue', threads=[ThreadSummary(original_message=SlackMessage(text=':memo: Received User Feedback For Request <https://support.i1.t.us-central1.prod.augmentcode.com/t/i1-vanguard0/request/57a0c6d5-2ef0-4b9f-9d42-b19840d2106f|57a0c6d5-2ef0-4b9f-9d42-b19840d2106f>\\nTenant: `i1-vanguard0` | Rating: `NEGATIVE` | Note:\"doesnt follow the instruction\"', user='U04NGAL63S6', ts='1745871323.224469', permalink='https://slack.com/archives/C08HWKMJGGY/p1745871323224469', thread_ts=None, replies=[]), summary='User provided negative feedback indicating that the AI agent did not follow the given instructions. The specific details of the instruction non-compliance are not elaborated in the message.', status='Unresolved', type='Other', request_id='57a0c6d5-2ef0-4b9f-9d42-b19840d2106f', share_url=None)]),\n", " ThreadSummaryGroup(name='Positive User Experiences', category='other', threads=[ThreadSummary(original_message=SlackMessage(text=':memo: Received User Feedback For Request <https://support.d7.t.us-central1.prod.augmentcode.com/t/d7-discovery6/request/31629d4c-f38d-44cd-b5df-11915fa31d23|31629d4c-f38d-44cd-b5df-11915fa31d23> (Request Access From <https://genie.us-central1.prod.augmentcode.com/?tab=2&amp;tenant=d7-discovery6&amp;redirect_url=https%3A//support.d7.t.us-central1.prod.augmentcode.com/t/d7-discovery6/request/31629d4c-f38d-44cd-b5df-11915fa31d23|Genie>)\\nTenant: `d7-discovery6` | Rating: `POSITIVE` | Note:\"informative run-down of the issues\"', user='U04NGAL63S6', ts='1745875937.305499', permalink='https://slack.com/archives/C08HWKMJGGY/p1745875937305499', thread_ts=None, replies=[]), summary=\"User provided positive feedback about an informative explanation of issues. The feedback was rated as 'POSITIVE' with a note appreciating the run-down of the issues.\", status='Resolved', type='Praise', request_id='31629d4c-f38d-44cd-b5df-11915fa31d23', share_url=None), ThreadSummary(original_message=SlackMessage(text=':memo: Received User Feedback For Request <https://support.d7.t.us-central1.prod.augmentcode.com/t/d7-discovery6/request/83349e66-5bf8-4063-9e68-08908ea02fd4|83349e66-5bf8-4063-9e68-08908ea02fd4> (Request Access From <https://genie.us-central1.prod.augmentcode.com/?tab=2&amp;tenant=d7-discovery6&amp;redirect_url=https%3A//support.d7.t.us-central1.prod.augmentcode.com/t/d7-discovery6/request/83349e66-5bf8-4063-9e68-08908ea02fd4|Genie>)\\nTenant: `d7-discovery6` | Rating: `POSITIVE` | Note:\"Very nice overview of the project!\"', user='U04NGAL63S6', ts='1745875134.104939', permalink='https://slack.com/archives/C08HWKMJGGY/p1745875134104939', thread_ts=None, replies=[]), summary='User provided positive feedback about a project overview, expressing appreciation for the presentation or content.', status='Resolved', type='Praise', request_id='83349e66-5bf8-4063-9e68-08908ea02fd4', share_url=None), ThreadSummary(original_message=SlackMessage(text=':memo: Received User Feedback For Request <https://support.d17.t.us-central1.prod.augmentcode.com/t/d17-discovery4/request/31b99eb9-546e-4d67-a101-4feeb18b3092|31b99eb9-546e-4d67-a101-4feeb18b3092> (Request Access From <https://genie.us-central1.prod.augmentcode.com/?tab=2&amp;tenant=d17-discovery4&amp;redirect_url=https%3A//support.d17.t.us-central1.prod.augmentcode.com/t/d17-discovery4/request/31b99eb9-546e-4d67-a101-4feeb18b3092|Genie>)\\nTenant: `d17-discovery4` | Rating: `POSITIVE` | Note:\"Excellent!\"', user='U04NGAL63S6', ts='1745872930.417899', permalink='https://slack.com/archives/C08HWKMJGGY/p1745872930417899', thread_ts=None, replies=[]), summary=\"User provided positive feedback, rating the experience as 'Excellent'. The feedback appears to be a general commendation with no specific issues or requests.\", status='Resolved', type='Praise', request_id='31b99eb9-546e-4d67-a101-4feeb18b3092', share_url=None), ThreadSummary(original_message=SlackMessage(text=':memo: Received User Feedback For Request <https://support.d6.t.us-central1.prod.augmentcode.com/t/d6-discovery6/request/157ce594-8eb2-4731-9ad7-0b629f4377ff|157ce594-8eb2-4731-9ad7-0b629f4377ff> (Request Access From <https://genie.us-central1.prod.augmentcode.com/?tab=2&amp;tenant=d6-discovery6&amp;redirect_url=https%3A//support.d6.t.us-central1.prod.augmentcode.com/t/d6-discovery6/request/157ce594-8eb2-4731-9ad7-0b629f4377ff|Genie>)\\nTenant: `d6-discovery6` | Rating: `POSITIVE` | Note:\"Yes I would\"', user='U04NGAL63S6', ts='1745871850.105759', permalink='https://slack.com/archives/C08HWKMJGGY/p1745871850105759', thread_ts=None, replies=[]), summary='User provided positive feedback, indicating willingness or agreement with something related to the AI agent extension. The feedback appears to be affirmative and supportive.', status='Resolved', type='Praise', request_id='157ce594-8eb2-4731-9ad7-0b629f4377ff', share_url=None)])]"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["groups"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["msgs = client.client.conversations_history(\n", "    channel=channel_id, start_date=start_date, end_date=end_date\n", ")"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"data": {"text/plain": ["':memo: Received User Feedback For Request <https://support.d7.t.us-central1.prod.augmentcode.com/t/d7-discovery6/request/83349e66-5bf8-4063-9e68-08908ea02fd4|83349e66-5bf8-4063-9e68-08908ea02fd4> (Request Access From <https://genie.us-central1.prod.augmentcode.com/?tab=2&amp;tenant=d7-discovery6&amp;redirect_url=https%3A//support.d7.t.us-central1.prod.augmentcode.com/t/d7-discovery6/request/83349e66-5bf8-4063-9e68-08908ea02fd4|Genie>)\\nTenant: `d7-discovery6` | Rating: `POSITIVE` | Note:\"Very nice overview of the project!\"'"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["(\n", "    msgs[\"messages\"][0][\"blocks\"][0][\"text\"][\"text\"]\n", "    + msgs[\"messages\"][0][\"blocks\"][1][\"text\"][\"text\"]\n", ")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "from base.tokenizers import create_tokenizer_by_name\n", "from megatron.data.indexed_dataset import make_builder\n", "\n", "output_data_path = Path(\"/tmp/dataset\")\n", "tokenizer = create_tokenizer_by_name(\"starcoder\")\n", "\n", "builder = make_builder(\n", "    str(output_data_path / \"dataset.bin\"),\n", "    \"mmap\",\n", "    vocab_size=tokenizer.vocab_size,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n"]}], "source": ["from research.data.dataset.indexed_dataset import make_dataset\n", "\n", "ROOT = Path(\"/mnt/efs/spark-data/user/arun/next-edit-location/\")\n", "for path in [\n", "    ROOT\n", "    / \"S1.3_prs_2k.keepmost.filter.empty10,R1.2_v13-128.smart2000,Sc1.2_raven_edit_S27-R4-P18.10h,T1.1_5-15lines.downsample10.instructions50,indexed_dataset\",\n", "    ROOT\n", "    / \"S1.3_prs_2k.keepmost.filter.empty10.val,R1.2_v13-128.smart2000,Sc1.2_raven_edit_S27-R4-P18,T1.1_5-15lines.downsample10.instructions50,indexed_dataset\",\n", "]:\n", "    builder.merge_file_(str(path / \"dataset\"))\n", "\n", "builder.finalize(str(output_data_path / \"dataset.idx\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}