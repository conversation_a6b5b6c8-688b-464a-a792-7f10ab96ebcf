determined:
  name: &xname roguesl_fbwd_0
  description: null
  workspace: Dev
  project: arun

augment:
  podspec_path: "1xH100.yaml"
  gpu_count: 1

fastbackward_configs:
 - configs/starcoder_1b.py

fastbackward_args:
  loss_mask_policy: fim
  fim_middle_token_id: 2
  eot_token_id: 0
  pad_token_id: 49152
  gradient_accumulation_steps: 1
  batch_size: 1
  max_iters: 30
  warmup_iters: 0
  lr_decay_iters: 4000
  block_size: 4096
  min_lr: 1.0e-6
  learning_rate: 1.0e-5
  decay_lr: True
  eval_interval: 250
  eval_items: 1
  hf_checkpoint_dir: /mnt/efs/augment/external_models/starcoderbase-1b
  train_data_path: /mnt/efs/augment/data/processed/rag/dataset/roguesl_eth6_4m_morelang_fprefsufret_npref250_quant50_rdrop015/dataset
  eval_data_path: /mnt/efs/augment/data/processed/rag/dataset/roguesl_eth6_4m_morelang_fprefsufret_npref250_quant50_rdrop015/validation_dataset
  model_vocab_size: 51200
  checkpoint_optimizer_state: False

  # tokenizer_name: StarCoderTokenizer
  # use_research_tokenizer: True
  # visualize_logits_samples: 8

  run_name: *xname
  wandb_project: arun-sandbox
  wandb_group: rogue-repro
