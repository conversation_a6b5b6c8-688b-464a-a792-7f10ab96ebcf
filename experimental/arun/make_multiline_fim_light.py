"""Make a light variant of multiline fim."""

import collections
import json
import pathlib
import random

import zstandard

DATA_ROOT = pathlib.Path("/mnt/efs/augment/data/processed/human_eval_fim.v1/")
INPUT_DATA = DATA_ROOT / "HumanEval-MultiLineInfilling.jsonl.zst"
OUTPUT_DATA = DATA_ROOT / "HumanEval-MultiLineInfillingLight.jsonl.zst"


def load_data(path: pathlib.Path) -> list[dict]:
    with zstandard.open(str(path), "rt") as f:
        data = [json.loads(line) for line in f]
    return data


def save_data(objs: list[dict], path: pathlib.Path):
    with zstandard.open(str(path), "wt") as f:
        for obj in objs:
            json.dump(obj, f)
            f.write("\n")


def group_by_task_id(data: list[dict]) -> dict[str, list[dict]]:
    ret = collections.defaultdict(list)
    for datum in data:
        _, group_id, _ = datum["task_id"].rsplit("/", 2)
        ret[group_id].append(datum)
    return ret


def subsample_by_group(groups: dict[str, list[dict]]) -> list[dict]:
    return [random.choice(group) for group in groups.values()]


def main():
    data = load_data(INPUT_DATA)
    print(f"Starting with {len(data)} tasks")
    groups = group_by_task_id(data)
    print(f"Tasks are part of {len(groups)} groups")
    light = subsample_by_group(groups)
    print(f"Subsampled to {len(light)} tasks")
    save_data(light, OUTPUT_DATA)


if __name__ == "__main__":
    main()
