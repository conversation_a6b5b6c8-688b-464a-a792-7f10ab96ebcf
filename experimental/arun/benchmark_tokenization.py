"""Benchmark tokenization."""

import pathlib
import timeit

import numpy as np

import base.tokenizers
from services.request_insight import request_insight_pb2


def _get_token_proto(
    tokenizer: base.tokenizers.Tokenizer, token_id: int, log_prob: float = 0.0
) -> request_insight_pb2.Token:
    return request_insight_pb2.Token(
        token_id=token_id,
        text=tokenizer.detokenize([token_id]),
        log_probs=log_prob,
    )


def report_time(fn, repeat=10, number=100):
    timer = timeit.Timer(fn)
    times = [t / number for t in timer.repeat(repeat=repeat, number=number)]
    min_time = min(times)
    std_time = np.std(times)

    if min_time < 1e-3:
        return "{:.3f}us +/- {:.3f}us".format(min_time * 1e6, std_time * 1e6)
    elif min_time < 1:
        return "{:.3f}ms +/- {:.3f}ms".format(min_time * 1e3, std_time * 1e3)
    else:
        return "{:.3f}s +/- {:.3f}s".format(min_time, std_time)


def main():
    """Main."""
    tokenizer = base.tokenizers.create_tokenizer_by_name("rogue")
    text = (pathlib.Path(__file__).parent / "compute_perplexity.py").read_text()
    tokens = tokenizer.tokenize_safe(text)
    tokens = (tokens * 10)[:4096]
    print("Detokenizing: ", len(tokens))

    print("detokenize: ", report_time(lambda: tokenizer.detokenize(tokens)))
    print(
        "detokenize with offsets: ",
        report_time(lambda: tokenizer.detokenize_with_offsets(tokens)),
    )
    print(
        "detokenize with offsets old: ",
        report_time(lambda: [tokenizer.detokenize([t]) for t in tokens]),
    )


if __name__ == "__main__":
    main()
