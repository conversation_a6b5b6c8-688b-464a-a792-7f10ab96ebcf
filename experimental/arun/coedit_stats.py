"""Compute statistics on next-edit data based on how close/far chunks are."""

import argparse
from collections.abc import Iterable
from pathlib import Path

import numpy as np
from megatron.data import indexed_dataset

from base.tokenizers import StarCoderSpecialTokens, Tokenizer, create_tokenizer_by_name


def extract_path(doc_tokens: list[int], tokenizer: Tokenizer) -> str:
    """Extract the path from the document tokens."""
    # <|startofsequence|>adaptive_breakpoints/lib/src/adaptive_breakpoints.dart<fim_middle>
    special_tokens = tokenizer.special_tokens
    assert isinstance(special_tokens, StarCoderSpecialTokens)
    start_ix = doc_tokens.index(special_tokens.start_of_key)
    end_ix = doc_tokens.index(special_tokens.fim_middle, start_ix + 1)
    return tokenizer.detokenize(doc_tokens[start_ix + 1 : end_ix])


PathAndScore = tuple[str, float]


def parse_tokens(tokenizer: Tokenizer, tokens: list[int]) -> Iterable[PathAndScore]:
    """Parse the token sequence into a set of labeled path chunks."""
    special_tokens = tokenizer.special_tokens
    assert isinstance(special_tokens, StarCoderSpecialTokens)

    if not tokens:
        return []

    start_ix = tokens.index(special_tokens.end_of_query)

    try:
        while True:
            # Get the path:
            # <|startofsequence|>adaptive_breakpoints/lib/src/adaptive_breakpoints.dart<fim_middle>
            start_ix = tokens.index(special_tokens.start_of_key, start_ix + 1)
            end_ix = tokens.index(special_tokens.fim_middle, start_ix + 1)
            path = tokenizer.detokenize(tokens[start_ix + 1 : end_ix])

            start_ix = tokens.index(special_tokens.end_of_key, end_ix + 1)
            end_ix = tokens.index(special_tokens.end_of_key, start_ix + 1)
            score = float(tokenizer.detokenize(tokens[start_ix + 1 : end_ix]))
            start_ix = end_ix

            yield path, score

            if not tokens:
                break
    except ValueError:
        pass


def count_same_file_negatives(scored_paths: list[PathAndScore]):
    """Count the number of negative samples that are in the same file."""
    positives = {path for path, score in scored_paths if score == 0.0}
    negatives = [path for path, score in scored_paths if score < 0.0]
    return len(positives), len(negatives), len([p for p in negatives if p in positives])


def main(input_path: Path, n_samples: int, tokenizer: Tokenizer):
    dataset = indexed_dataset.MMapIndexedDataset(str(input_path), skip_warmup=True)
    assert dataset is not None

    fraction_same_file = 0.0

    for i in range(n_samples):
        tokens = dataset[i]
        assert isinstance(tokens, np.ndarray)
        n_positives, n_negatives, n_same_file = count_same_file_negatives(
            list(parse_tokens(tokenizer, tokens.tolist()))
        )
        # Discount the number of same file based on the number of positives -- more
        # positives => more same file.
        fraction_same_file += (n_same_file / n_negatives - fraction_same_file) / (i + 1)
        print(f"{i:>3} {n_positives:>3} {n_negatives:>3} {n_same_file:>3}")

    print(f"Fraction same file: {fraction_same_file:0.3f}")


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "-i",
        "--input",
        type=Path,
        required=True,
        help="Path to an indexed dataset to read.",
    )
    parser.add_argument(
        "-n",
        "--samples",
        type=int,
        default=100,
        help="The number of samples to use.",
    )
    parser.add_argument(
        "-t",
        "--tokenizer_name",
        type=str,
        default="starcoder",
        help="The number of samples to use.",
    )
    args = parser.parse_args()

    tokenizer = create_tokenizer_by_name(args.tokenizer_name)

    main(args.input, args.samples, tokenizer)
