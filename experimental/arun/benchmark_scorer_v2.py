"""Quick script to benchmark dense scorers."""

import logging
from collections.abc import Iterable
from itertools import islice
from pathlib import Path

import pandas as pd

from base.tokenizers import create_tokenizer_by_name
from research.core.diff_utils import Repository
from research.core.types import Document
from research.data.spark.pipelines.stages import prs
from research.data.spark.pipelines.utils.prs_dataset import RepositoryFileManager
from research.retrieval.chunking_functions import LineLevelChunker
from research.retrieval.scorers.dense_scorer_v2 import DenseRetrievalScorerV2

REPO_DATA_PATH = Path("/mnt/efs/spark-data/shared/gh_pr_repo_joined_sha_train")


logger = logging.getLogger(__name__)


def load_data(
    parquet_root: Path, repo_file_manager: RepositoryFileManager
) -> Iterable[Repository]:
    for path in parquet_root.glob("*.parquet"):
        df = pd.read_parquet(path)
        for pr in df.to_dict(orient="records"):
            repo_file_manager.add_repository_files(pr["repo_name"])
            repo = repo_file_manager.get_pr_files(pr)
            logger.info(
                f"Processing PR#{pr['number']} in repo {pr['repo_name']} ({len(repo.files)} files)"
            )
            yield repo


def do_work(
    repo: Repository, chunk_cache: prs.ChunkCache, scorer: DenseRetrievalScorerV2
):
    with prs.timeit(f"chunk processing ({len(repo.files)} files)"):
        chunks = []
        doc_chunks = {}
        for file in repo.files:
            doc = Document.new(file.contents, file.path)
            file_chunks = chunk_cache.get_chunks(doc)
            chunks.extend(file_chunks)
            doc_chunks[doc.id] = file_chunks
        scorer.add_docs(doc_chunks)


def main(batch_size: int):
    repo_file_manager = RepositoryFileManager(
        pr_repo_reference_parquet_file=Path(
            "/mnt/efs/spark-data/shared/gh_pr_repo_ref.parquet"
        )
    )

    tokenizer = create_tokenizer_by_name("starcoder")
    scorer = prs._create_scorer(
        tokenizer,
        prs.RetrievalConfig(
            checkpoint_path="/mnt/efs/augment/checkpoints/starethanol/starethanol6_16.1_mean_proj_512_2000/global_step2000",
            max_batch_size=batch_size,
        ),
    )

    chunker = LineLevelChunker(max_lines_per_chunk=30)
    chunk_cache = prs.ChunkCache(chunker=chunker)

    n_samples = 8

    with prs.timeit("total time"):
        for repo in islice(load_data(REPO_DATA_PATH, repo_file_manager), n_samples):
            do_work(repo, chunk_cache, scorer)


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser()
    parser.add_argument(
        "-b",
        "--batch-size",
        type=int,
        default=8,
        help="Maximum batch size for the embedder.",
    )

    args = parser.parse_args()

    main(args.batch_size)
