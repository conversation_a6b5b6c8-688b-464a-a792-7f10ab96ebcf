{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import json\n", "from pathlib import Path\n", "import os\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import zstandard"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# OLD\n", "# ROGUE_DIR = Path(\"/mnt/efs/augment/eval/jobs/Um8MgQAb\")\n", "# ENDER_DIR = Path(\"/mnt/efs/augment/eval/jobs/WiTrP7fB\")\n", "# FEB\n", "# ROGUE_DIR = Path(\"/mnt/efs/augment/eval/jobs/QT4oxTds/\")\n", "# ENDER_DIR = Path(\"/mnt/efs/augment/eval/jobs/XCKVs2GD\")\n", "# FEB -- newest models\n", "# ROGUE_DIR = Path(\"/mnt/efs/augment/eval/jobs/QT4oxTds/\")\n", "# ENDER_DIR = Path(\"/mnt/efs/augment/eval/jobs/XCKVs2GD\")\n", "DIRS = {\n", "    \"ender5k\": Path(\"/mnt/efs/augment/eval/jobs/kn6FnGWn\"),\n", "    \"ender5k-varusage\": Path(\"/mnt/efs/augment/eval/jobs/E8azN68A\"),\n", "    \"rogue5k\": Path(\"/mnt/efs/augment/eval/jobs/2c6DVtwP\"),\n", "}"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# load input/output\n", "from collections.abc import Iterable\n", "from research.eval.harness.tasks.pinocchio import PinocchioOutput\n", "\n", "def load_artifacts(output_path: Path) -> Iterable[PinocchioOutput]:\n", "    with zstandard.open(next(output_path.glob(\"*_completed_patches.jsonl.zst\")), \"rt\") as fh:\n", "        for line in fh:\n", "            yield PinocchioOutput.from_json(line)\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["from typing import Collection\n", "def report_set_output(sets: dict[str, Collection]):\n", "    for k, v in sets.items():\n", "        print(k, len(v))\n", "    keys = sorted(sets.keys())\n", "    for i, k in enumerate(keys):\n", "        for k_ in keys[i+1:]:\n", "            print(k, k_, len(set(sets[k]) & set(sets[k_])))"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["output = {\n", "    key: {datum.request_id: datum for datum in load_artifacts(data_dir)}\n", "    for key, data_dir in DIRS.items()\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["report_set_output(output)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def prefix_em(datum: PinocchioOutput) -> float:\n", "    # min_len = min(len(datum.generation), len(datum.ground_truth))\n", "    min_len = len(datum.ground_truth)\n", "    return float(datum.generation[:min_len] == datum.ground_truth[:min_len])\n", "\n", "output_em = {\n", "    key: {k: v for k, v in values.items() if prefix_em(v)}\n", "    for key, values in output.items()\n", "}\n", "report_set_output(output_em)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["report_set_output({\n", "    key: {(k, v.generation) for k, v in values.items()}\n", "    for key, values in output.items()\n", "})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "from IPython.display import display, HTML, IFrame\n", "from experimental.arun import notebook_utils\n", "import itertools\n", "AUGMENT_DIR = Path(\"/mnt/efs/augment/user/arun/ender-launch\")"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["AUGMENT_DIR = Path(\"/mnt/efs/augment/user/arun/ender-launch\")\n", "\n", "for key, values in output.items():\n", "    report = notebook_utils.render_twoway_diff_report([\n", "        notebook_utils.TwoWayDiff(\n", "                path=v.path,\n", "                prefix=v.prefix,\n", "                suffix=v.suffix,\n", "                left=v.ground_truth,\n", "                right=v.generation,\n", "                id=k,\n", "            )\n", "        for k, v in values.items()\n", "        if not prefix_em(v)\n", "    ][:200], title=key)\n", "    with AUGMENT_DIR / f\"report_{key}.html\" as f:\n", "        f.write_text(report)\n"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["AUGMENT_DIR = Path(\"/mnt/efs/augment/user/arun/ender-launch\")\n", "\n", "keys = sorted(output)\n", "\n", "for i, key in enumerate(output):\n", "    for key_ in keys[i+1:]:\n", "        report = notebook_utils.render_twoway_diff_report([\n", "            notebook_utils.TwoWayDiff(\n", "                    path=v.path,\n", "                    prefix=v.prefix,\n", "                    suffix=v.suffix,\n", "                    left=v.generation,\n", "                    right=v_.generation,\n", "                    id=k,\n", "                )\n", "            for k, v in output[key].items()\n", "            if (v_ := output[key_].get(k)) and v.generation != v_.generation\n", "        ][:200], title=f\"{key} vs {key_}\")\n", "        with AUGMENT_DIR / f\"report_{key}-vs-{key_}.html\" as f:\n", "            f.write_text(report)\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["server = notebook_utils.server(AUGMENT_DIR, 8081)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["server.serve_forever()"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [], "source": ["x = output[\"ender5k-varusage\"][\"0949a3ac-88cf-4068-aedf-a5d62934ab73\"]\n", "y = output[\"ender5k\"][\"0949a3ac-88cf-4068-aedf-a5d62934ab73\"]"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [], "source": ["x.generation == y.generation"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [], "source": ["x.generation"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [], "source": ["y.generation"]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [], "source": ["output[\"ender5k-varusage\"][\"02412171-9cf1-41a6-9f26-25782be2db3b\"].generation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}