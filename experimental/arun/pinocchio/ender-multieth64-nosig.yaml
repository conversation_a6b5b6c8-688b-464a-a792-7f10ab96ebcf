import_modules: experimental.igor.systems.ethanol
system:
  dense_retriever:
    chunker:
      max_lines_per_chunk: 30
      name: line_level
    document_formatter:
      add_path: true
      max_tokens: 999
      name: ethanol6_document
    query_formatter:
      add_path: true
      add_suffix: true
      max_tokens: 1023
      name: ethanol6_query
      prefix_ratio: 0.9
    scorer:
      checkpoint_path: ethanol/ethanol6-16.1
      name: ethanol
  fim_gen_mode: interactive
  generation_options:
    max_generated_tokens: 280
  model:
    model_path: ender/16b_ender_multieth64m_fullsig_nosig_b1ks4k
    name: ender_fastforward
    prompt:
      component_order:
      - signature
      - retrieval
      - prefix
      - suffix
      max_prefix_tokens: 1280
      max_prompt_tokens: 5120
      max_retrieved_chunk_tokens: -1
      max_signature_tokens: 0
      max_suffix_tokens: 768
  name: ender_sys
  sig_prompt_formatter:
    max_middle_tks: 1024
  signature_index:
    est_prefix_chars: 3840
    est_suffix_chars: 2304
    max_ctx_signature_chars: 3000
    sig_printer:
      show_full_method_signatures: true
    verbose: false

# Tasks
#   specify the evaluation tasks for each checkpoint
#
task:
  name: pinocchio
  # version: "dogfood-feb24"
  version: "dogfood-feedback"
  service_account_file: /mnt/augment/secrets/cw-ri-importer/cw-ri-importer.json

# Podspec - set the default podspec for all checkpoints
# See gpt-neox/jobs/templates/podspecs/ for additional options
# Use the following for small models (<=2B)
# podspec: gpu-small.yaml
# Use the following for larger models (>=2B)
podspec: A40.yaml

# Determined
# name, workspace, project control location and display in the determined UI.
#
# IF YOU DO NOT WANT TO EXECUTE CODE:
# To disable execution through hydra, choose the batch-eval.yaml below

determined:
  name: Pinoccio - Ender, Ethanol
  workspace: Dev
  project: playground
  # relative to research/gpt-neox
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml

augment:
  # Mapping of secret name to mountpoint *when not running with --local*.
  dai_gcp_service_accounts:
    - secret: aug-prod-cw-ri-importer  # pragma: allowlist secret
      mountpoint: /mnt/augment/secrets/cw-ri-importer
