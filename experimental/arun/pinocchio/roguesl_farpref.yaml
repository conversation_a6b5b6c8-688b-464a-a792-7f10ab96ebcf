import_modules: experimental.igor.systems.ethanol
system:
  experimental:
    remove_suffix: false
    retriever_top_k: 25
    trim_on_dedent: true
    trim_on_max_lines: null
  fim_gen_mode: interactive
  generation_options:
    max_generated_tokens: 280
    temperature: 0
    top_k: 0
    top_p: 0
  model:
    checkpoint_path: roguesl/16b_eth61m_depause_prefretsuf_npref100_olap0_quant20
    name: rogue_statelesscache
    prompt:
      component_order:
        - prefix
        - suffix
        - retrieval
        - nearby_prefix
      context_quant_token_len: 20
      max_filename_tokens: 50
      max_prefix_tokens: 1180
      max_prompt_tokens: 3816
      max_retrieved_chunk_tokens: -1
      max_suffix_tokens: 768
      nearby_prefix_token_len: 100
      nearby_prefix_token_overlap: 0
      nearby_suffix_token_len: 0
      nearby_suffix_token_overlap: 0
  name: basic_rag
  retriever:
    chunker:
      max_lines_per_chunk: 30
      name: line_level
    document_formatter:
      add_path: true
      name: ethanol6_document
    query_formatter:
      add_path: true
      max_tokens: 1023
      name: ethanol6_query
    scorer:
      checkpoint_path: ethanol/ethanol6-04.1
      name: ethanol

# Tasks
#   specify the evaluation tasks for each checkpoint
#
task:
  name: pinocchio
  version: "dogfood-feb24"
  service_account_file: /mnt/augment/secrets/cw-ri-importer/cw-ri-importer.json

# Podspec - set the default podspec for all checkpoints
# See gpt-neox/jobs/templates/podspecs/ for additional options
# Use the following for small models (<=2B)
# podspec: gpu-small.yaml
# Use the following for larger models (>=2B)
podspec: A40.yaml

# Determined
# name, workspace, project control location and display in the determined UI.
#
# IF YOU DO NOT WANT TO EXECUTE CODE:
# To disable execution through hydra, choose the batch-eval.yaml below

determined:
  name: Pinoccio - Rogue SL, Ethanol
  workspace: Dev
  project: playground
  # relative to research/gpt-neox
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml

augment:
  # Mapping of secret name to mountpoint *when not running with --local*.
  dai_gcp_service_accounts:
    - secret: aug-prod-cw-ri-importer  # pragma: allowlist secret
      mountpoint: /mnt/augment/secrets/cw-ri-importer
