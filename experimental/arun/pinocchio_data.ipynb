{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from google.cloud import storage\n", "from google.cloud import bigquery"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["bq = bigquery.Client() "]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [], "source": ["q = bq.query(\"\"\"\n", "SELECT request.request_id, request.raw_json as request_json, response.raw_json as response_json\n", "FROM `dev_request_insight_full_export_dataset.completion_event` request\n", "JOIN `dev_request_insight_full_export_dataset.completion_event` response USING (request_id)\n", "WHERE request.tenant = 'dev-arun'\n", "AND request.event_type = 'completion_host_request'\n", "AND response.event_type = 'completion_host_response'\n", "LIMIT 10\n", "\"\"\")"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [], "source": ["rows = list(q.result())"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"data": {"text/plain": ["(dict_keys(['blob_names', 'eos_token_id', 'lang', 'model', 'output_len', 'path', 'position', 'prefix', 'suffix', 'temperature', 'tokens', 'top_k', 'top_p']),\n", " dict_keys(['embeddings_prompt', 'retrieved_chunks', 'skipped_suffix', 'suffix_replacement_text', 'text', 'tokens', 'unknown_blob_names']))"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["row = rows[0]\n", "request = row[\"request_json\"]\n", "response = row[\"response_json\"]\n", "request.keys(), response.keys()"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [], "source": ["from termcolor import colored"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\"\"\"Module for signature retrieval.\"\"\"\n", "from __future__ import annotations\n", "\n", "from typing import Callable, Sequence, TypeVar, cast\n", "\n", "import structlog\n", "from typing_extensions import override\n", "\n", "from base.prompt_format import TokenList\n", "from base.prompt_format.ender_prompt_formatter import EnderPromptFormatter\n", "from base.prompt_format.prompt_formatter import PromptChunk\n", "from base.static_analysis.signature_index import SymbolSignature\n", "from base.tokenizers import RogueSpecialTokens\n", "from services.completion_host.single_model_server.model_tool import (\n", "    ToolState,\n", "    ModelOutput,\n", ")\n", "\n", "log = structlog.get_logger()\n", "\n", "\n", "class SignatureToolState(ToolState):\n", "    \"\"\"Tool state for inline signature retrieval.\"\"\"\n", "\n", "    def __init__(\n", "        self,\n", "        lookup: Callable[[str], Sequence[SymbolSignature]],\n", "        # TODO(AU-1093): We are coupling the SignatureToolState with a specific prompt\n", "        # formatter. This is not ideal. We should make the tool state more generic, but\n", "        # we don't yet have agreement on the interface.\n", "        prompt_formatter: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "    ):\n", "        self._lookup = lookup\n", "        self._prompt_formatter = prompt_formatter\n", "        self._tokenizer = self._prompt_formatter.tokenizer\n", "\n", "        special_tokens = cast(RogueSpecialTokens, self._tokenizer.special_tokens)\n", "        self._sig_lookup = special_tokens.sig_lookup\n", "        self._sig_begin = special_tokens.sig_begin\n", "        self._sig_end = special_tokens.sig_end\n", "\n", "        self._output_tokens = []\n", "        self._output_log_probs = []\n", "\n", "    def _decode_output(self, output: ModelOutput) -> tuple[ModelOutput, str | None]:\n", "        # Find the last occurrence of the sig lookup token.\n", "        if (sig_index := _find_last(output.output_tokens, self._sig_lookup)) is None:\n", "            return output, None\n", "        output = ModelOutput(\n", "            output.output_tokens[:sig_index],\n", "            output.log_probs[:sig_index] if output.log_probs else None,\n", "        )\n", "        lookup_tokens = output.output_tokens[sig_index + 1 :]\n", "\n", "        # It's possible the model stops generating mid-way through a signature lookup.\n", "        # In that case, we don't want to return the signature.\n", "        if lookup_tokens[-1] != self._sig_begin:\n", "            return output, None\n", "\n", "        symbol_name = self._tokenizer.detokenize(lookup_tokens[:-1])\n", "        return output, symbol_name\n", "\n", "    def _format_signatures(self, results: Sequence[SymbolSignature]) -> TokenList:\n", "        # TODO(arun): This is hard-coded here for now, but should be the responsiblity\n", "        # of the prompt formatter.\n", "        sigs = \"\\n\\n\".join(sig.text for sig in results)\n", "        return self._tokenizer.tokenize_safe(f\"\\n{sigs}\\n\")\n", "\n", "    @override\n", "    def use(\n", "        self,\n", "        prev_model_input: Token<PERSON><PERSON>,\n", "        prev_model_output: ModelOutput,\n", "        max_output_tokens: int,\n", "    ) -> TokenList | None:\n", "        # 1. Parse the query from the previous model output.\n", "        output, symbol_name = self._decode_output(prev_model_output)\n", "        # We only consume the output tokens if we're using the tool.\n", "        self._output_tokens.extend(output.log_probs\u001b[34m or []\u001b[0m)\n", "        log.info(\n", "            \"Tool added %d output tokens (total %d)\",\n", "            len(output),\n", "            len(self._output_tokens),\n", "        )\n", "\n", "        if not symbol_name:\n", "            log.info(\"Tool complete: nothing to lookup.\")\n", "            return None\n", "        if self.output_tokens_length() >= max_output_tokens:\n", "            log.info(\"Tool complete: out of output token budget.\")\n", "            return None\n", "\n", "        # 2. Perform an inline search for the symbol name.\n", "        sigs = self._lookup(symbol_name)\n", "        retrieved_chunks = [\n", "            PromptChunk(\n", "                text=sig.text,\n", "                path=str(sig.path),\n", "                origin=\"signature_retriever\",\n", "            )\n", "            for sig in sigs\n", "        ]\n", "        log.info(\"Tool found %d results\", len(retrieved_chunks))\n", "\n", "        # 3. Use the results to format additional tokens.\n", "        # NOTE(arun,jiayi): We format signatures even if sigs is empty to tell the model\n", "        # that we found no signatures.\n", "        sig_tokens = self._prompt_formatter.format_tool_output(\n", "            retrieved_chunks=retrieved_chunks,\n", "            previous_input_length=len(prev_model_input) + len(prev_model_output),\n", "            max_output_token_count=max_output_tokens - self.output_tokens_length(),\n", "        )\n", "        log.info(\"Tool generated %d additional prompt tokens\", len(sig_tokens))\n", "\n", "        if sig_tokens:\n", "            return [*prev_model_input, *prev_model_output, *sig_tokens]\n", "        else:\n", "            log.info(\"Tool complete: out of prompt token budget.\")\n", "            return None\n", "\n", "    @override\n", "    def output_tokens_length(self) -> int:\n", "        \"\"\"Get the remaining maximum output tokens to generate from the model.\"\"\"\n", "        return len(self._output_tokens)\n", "\n", "    @override\n", "    def complete(self) -> ModelOutput:\n", "        return ModelOutput(\n", "            self._output_tokens,\n", "            self._output_log_probs,\n", "        )\n", "\n", "\n", "T = TypeVar(\"T\")\n", "\n", "\n", "def _find_last(seq: Sequence[T], element: T) -> int | None:\n", "    for i, x in enumerate(reversed(seq)):\n", "        if x == element:\n", "            return len(seq) - i - 1\n", "\n"]}], "source": ["print(request[\"prefix\"] + colored(response[\"text\"], \"blue\") + request[\"suffix\"])"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"data": {"text/plain": ["['31837a86a39387dc196195a12c9e0d3cf5f698c8e408f6fbd857ab112ec8dddb',\n", " 'e5b9de3cb2b34a84df0a2c8e7e43564937a5d31e854c6455749c7622e6766c7c',\n", " '886006f9479eb055d76e317779cba58ee6111e12a7617d467b0b91c0a158ba47',\n", " '776d6b8ea4f6d92145bcd1408d5152bf2e1043b677b8d225585b78e82fff3c25',\n", " 'd776ba1b173647676945cc1a9edc9370efb3b294048d14599eb1b8c3af94bf54',\n", " '1674ad3429f442d3cd796d6674602c1983c272989dde041c15011e4273bd0053',\n", " 'ae5d6a64ef3154fb05719515c188c027d51a6d0f518adb050c83fff1a8fe33ae',\n", " '5c298d20e55d44263a6e9667a034ce62ba217092331188e11661f5b8ccf7d361',\n", " '8c836625875cf754a355c0c4486ad02e3b64a559bc8d05152ed60eda98d27c7e',\n", " 'c30d28f54f1c7f67b3037ae50cf2dd0aacf287ed8fa10046b823f2e93d49848c',\n", " '26c6b6926b09865456f8f20887c76259a00e078f08255e3c03859686392dc8aa',\n", " '73401000ded6f53883d6942ac8724d6679862fc71ab1af3ddd52edd90800f734',\n", " '9bd7f35ff053655758b3405bee1674c823623216c5705a4ab078ab6e1662f7e5',\n", " '04e5a68ba2bc567bf173da67691bed136cf86de0d54555743e03aa5ddf74c0e1',\n", " '1fa7fd46cdc1cdca856891af907acd0ffa48925a04e9b812aa7a4dba1f4d24c1',\n", " '046dc1c6d2e28ecab93e0a38319b9a6fa4210de208d1792ef23bd5cde58d8096',\n", " '2b3599e1137a3dd93e2c03d9db479d82a21bdbe132c08ae645dc0566bcaf30aa',\n", " 'a0e57bcc0e0868ec250975a3152f2da3546570ef4932372c7c27e2ca5e5dca26',\n", " '5315fa58fd7e99babb8a64529e4cd1e8c600d99ac1906b05a0a7b4e31308bbf4',\n", " 'b92bfa4ca5c6cf8fc0521f5c43d98e75f27f39c7d1b53ab7548d7466ab0d7ce3',\n", " '2161344c16bd9951fdf581e7951519e4864293550a65ee879fcf303ec14bc0a7',\n", " '146c52a32db13d577da971d53164b0e6a6b1c39f4bf0fa54dd4858f21b322288',\n", " 'c8f62fca46b77b66a2f5d68b8c39671019e7d96c6a115ff0d10939e73cbc7478',\n", " '6817df149de0ee89034c66c08efd629bcfba89160b8c595f47eae6a381002c7d',\n", " '60c24a5143f1374883db7d20719df69fa20f4b8c727eb1771466e251c98716c2',\n", " 'b5fb03a30812348b0b6bc926bb8bf02bd56ef3a150cff9836e37c0e8e1fc68d7',\n", " '34e5e7d11a7f390586915ab52d738e571d1a649c401e35e33bd649e4e184ad95',\n", " 'ab9cd18e796bd733125d858f33e03b13351bf0b52ef65913aaa3628abda7ebe0',\n", " 'c3ed882ad758404321a5e45a1ef199bbcaa7320c474898e479c220b483aa918b',\n", " '89c9d6279c3717e52d7cc6f47c0f689a22ab79e9d1e6bc7a38df2142481f6217',\n", " 'ffa1e378e8c107082623b80ca400173ec96d4eabed3d831d37833e11f6d396eb',\n", " 'cc4fb8fbd53f5d492269097bfe5589eeea7f6900151edc37585ae6b54dcd2e66',\n", " '4d8ab3939e4a2179c42f00de0e07f7e53a529b3ea87cc78e8687c4dedea7d2a2',\n", " '942622e2a48aa4d6c9762106e78b3f354bec31d88071fb0b548b209a67fecead',\n", " 'eaff44a909c0d4a3e646fd4f9d2113c553514ddcd134c794758f2a0d0be10077',\n", " 'e07d4a0588f4daee4dc8bf4f5c081debc2130ed0499bb4595ac9a8b8369cc4e9',\n", " 'db9f346b858dd527e5631633a175d9137969440ac857366f618cd1b15fa9c7e4',\n", " '42a64f2aad5c424c61548ce59e256d7a2c73480d19ea2d6e870b285c80cf79b5',\n", " 'ef618f24f5fd09bc969c19bd17d320ad3909766ec1108c12ae696dfb53b54ceb',\n", " '2c369b9715ebd52c3c5ef8c0c5f7b474c40db277e11fada911cecc9289c60925',\n", " '95b98497c84579054a3044a73c1c161d24b1e092cac9663edc6767bd40acb4b3',\n", " 'fe47c8ca6969c8e3fb4e9a4ac563a6e0874fd09c2a5258f45a0d27fa71d0a8c7',\n", " '1555b90f68547a9f8d4d5ffbf8c5688ed87689ec2c04ad2d754ac763796281e9',\n", " 'f8ea9f443dd486ec34e4a4ea69082594aa51d1e1728351eb4d51caaf14ab83e3',\n", " 'e1b118445202d1bf2da50349541de4c1035c5fbd8a393dad7b4c9a9bb605f099',\n", " '99078f430dd69cd4f424feeb4d4442c7344c34302414dd1d8a267e44200649f8',\n", " '6150859d827da940b58c2ac62c64731983b444054dbf219ef319d7f467f852e5',\n", " '24759fd08699789e1e500349ffb5123d9c35369ec9f9e78b3a1efa661a6020a5',\n", " '8500e9ee513e489b5813beb3dd8dba799ad2a7f475becebc2c430623d6fa9e21',\n", " 'bb61c9613ba0f9e881f4ee495d0416dfc05d124b1d8dc01f26c611c263fa636a',\n", " '48b7f7eae779caf85c8b358f66e4e5fc8e642d86718bec9810e8767fbd85ea16',\n", " 'be0461bd23054affa58795e794811e3e009c12c8213b76cf35ee0e10b31d3f80',\n", " '47f03e8bcaa321df25fec31653ffd1fbb243f2e3fd144e293de07a54a0d6ab06',\n", " '242b2c348683a4a460c1b5b47f2f31bae57d7ed4a1377b69ee3c0affe0cb35ee',\n", " '3ce808d3770ae02faec53ad5b65f2bcf5e80b93b1b1f4e6508135ed89fcfd805',\n", " 'fb499cd973b74de77498cd662a22b4a97434e6ad45ec4dfbffa8988587339c64',\n", " '48adcf2baa33ec70ac77f8e823b82e67238933474cbfae36298a7844f13a0f74',\n", " '2da1581bbe2c483ae38395da059af0e7a7bac887589a0d9c3fb1dd0d41561d4c',\n", " '8b8ac924d34d2cda787ff005b49d1e803499c3468182feb4456fea64b235da2a',\n", " '457d290625e85752daa41f558f63a7e347be375376fa6eaf926a3d370d906601',\n", " '4728474fde88c476fc9681e2a93c8d1237d4db62c5ebf4afff228402bacb3ff2',\n", " '8d9f2daa4cf43e0f7dc274d0bab430ffceca7797b76319f59a8ce65a0d2718a4',\n", " '8b7592b83a088839a180449e67a3f75899937095e76dab1e62373f81c5487481',\n", " '0df1f3b5c0eb6f6c199c4b2972238b9c411960baf44d3e5b36a39bfd2615be18',\n", " 'b80aa7debcd7dd5faeac7a406a6c6e6fbcec2bc3d650e82823045325be610261',\n", " '9e9e5f8abfd92ea049448336efbdbc8ea164ed7ded695f0644460e8f291c98ab',\n", " '3c615c7af47d34840107b003249e80fdbc7ace3fec68ef6a90ee7b354099e3e3',\n", " '54b6042ad633f43a974d4fbf6388564d9b6493d746b2ba0de030515ce582ca15',\n", " 'b39887b5e3dc59b21f3eea02ae8cd6127f43444869e71dd05cb7f11c0fa95574',\n", " '29c020ca8d4425f28ffe2b3f04dffff628d56f8751ef9535403ce9359aef5ac9',\n", " 'db9b5127f3b669284e977c98a48197632b338befc2a56cdd2f24f679bb00b1e7',\n", " '7dd7c5b2aa70281e52283357eca9da347529dc324f1a6c6ea334dfa1e9f59236',\n", " 'e01a0005ae924e883396b44575def35dc04141929d68d45d219f57247099b9dc',\n", " '68fc610cc6289c8dd4a1e54eaf3fd902da2df9e2aad6d94ec0d69bc472afd366',\n", " '9c60d6fb0d9f76d99945fbdc977cbe031336210f7d42bf6bd7994d26b8527891',\n", " '84a4072a86cf9afd2d969e6f4ab146d355e368bff074b90117bbd5aa01bea1e7',\n", " '598cacd9b8908819b82b6d9b6daee63501cf7b303261b0369f11aa6fb7feaacf',\n", " 'a43415e392e551a15f000d22e354145598317a91736d39479846cb430e524c54',\n", " 'f7168a8fde2b76cbe602ceaf32ab8c250353e6f4d4a34397de76066063fe4ad0',\n", " '5d67b64e1164c2e3609ebaab18d7adf26cb8f3eac3451d33a2aeb781bfd3cb03',\n", " '1dd9092cf591c0d76ac2af89ce6052d95d02798b56a995b68411b550fe241f1a',\n", " 'bea369bedb13174ec6a8c59e249786dee42a3b352bb4828a40bdc1278fd3a25f',\n", " 'abe434fb8dce27cec2d665fcb45dc7b7a2b8d2206daf572d8ff9f281ea2d7e4d',\n", " '512bbee84fb569d3245ff3b9748a8a128bb57d9a29d43ad7994a3fc13c0c647c',\n", " '167ccdbb67746f339c914d26dca1ed6d09f1e255539624cf2b1d5ff77338f990',\n", " '7b5f47b7350128630c5fa1f65800087a02865b85dbaf84af0d1c23df80172e2d',\n", " '212229070636bc97b2bc39f5b84f7b440b0c98442ab160b525703d83dd950e88',\n", " 'd4bce87ec3ba673200a962328ad37154a6e56982118f5558c40d04c9fef8bd00',\n", " '2571215391bf4656166fce7f0f0f5c22e2ec1c01272430d794b5404ce53e004a',\n", " '707c7b647b4f5b9d966d4068554e219302e8c4fca466e4b912cdfaa826a54399',\n", " '7c679a7dc7b4136a702b58e341434bde4f2b7d246a37d8a59a15379d3f27839b',\n", " 'c4d084dc83614eb372afdc90d103c5199b851492fa1baa697e542e06aafa20ef',\n", " '479e153737ea451a1ec2db6deb863bf1b041799199cffdc9153f641e568d1c7e',\n", " 'f4abc0f5da22232411e9952678bfbea1b9fb36bf0bf0b7ce807499bbc35fddc7',\n", " '56b8b4a0c10681493781b03980a3f88063100dbfd23da253057eb2ab0c0cea77',\n", " 'a17cbff5ff998b105c47faf99fdb6a2c3dd885ab6b39ec119a3b5037fc83fe23',\n", " 'b599eefccc0171c7a629a810b2ad81caee12eaea0f84873d6d27704829a4e71d',\n", " 'e645c949b82b5f6aa8c526c9db3dc61871cefd17bbbb1862aeaf993579eb5dac',\n", " '6c02321b94411b1468c5e4aa33c4506e74d8ed34e9c59bdad5989ba5b283ced8',\n", " '248b42ef42670e6cb2280f2d8b760c4c07f470a86de8b62f48460c9069a63806',\n", " 'b5c7eb642bae70f95940aea8ae925183c0798f011989f59f145edf18a13b5762',\n", " 'e7ed2be7966a47496ab87d6998fb0a5a86b762ffb7b419f3e994d39fb53d3727',\n", " '1538b5ec26cf65f48b5768ad9cacaac0c6d68fa3931dd035c263256a50f438b0',\n", " 'c9443afcea36153f17c1bc9538fc28c6309da2cddf06945d1f58fa2d07f8e45e',\n", " 'ca990ca3d50d6f8084d9e1302789288d268c38e4bcfa57ba0a1c6f7e6a0d5b48',\n", " 'e4cf74d1f1ff22caa303e1544ce2b2ecf8e1880ee48c865d9c7358ee979ed2b2',\n", " '6aeee03fec742f97a96d160bdb2b6bcb5428678dac82296021fe0d8e64379c76',\n", " 'd53b4bec22092b7007512e84ca3e4f760aff4f5e4d851332b31f3c92dee501b3',\n", " '6d415972c9e41e653f3f7bde3bd3cacfd2b04542587d1e832fb0d45e2a304461',\n", " 'bc5408dc995ee23ad2b5d56730b50ad51d8af5884b946adad6a45cfc541f56d4',\n", " '9a62bb1b6b66dfc81248e92f0e97b6979a1aef68053f87a840060412a97669a5',\n", " 'b9859cf09da4cceac98228a224bd5b55bbc8cfc28a16cb109c829dd4cde0f565',\n", " '21ff81920520aebeddf1c0321a29e9e60a7fef95243197542169035f0d96ca47',\n", " '4251aa953e70b8d5ababca3d45118dd9e1e871e4020f926d0d915ad83fe906ec',\n", " '6b8224cccc8f06ae0392bdd643533e342ac5e1369f164b4a5086fcac4a586648',\n", " '7832e9d52af3aaeb6743fc9b9941b89f8b9c544bef3765698671ed54844f12b6',\n", " 'a3c97c480b315ee005f467a3c3a445f62c5ea4ecc0e76f6a00f2cf5023866374',\n", " '6842b03268ab9f7a626f1f8a0ff07e4e9effb0edb34bb1369e0d3a504f9db0eb',\n", " '77aa27be98f9efa609b2cd9a0f201bcd45ef1dda8f4aa4eb41d7585076708b35',\n", " '431dd418ef610b5c365cf23b24d83bceab463329a0b2d3d95dba9b610dcc0f15',\n", " '26bd9c7c0d14e94c6877d519f10906db6c1680fb4e27008f61e3a0b66513788f',\n", " '2b1ee4a8acb4321f3e6a6438175270e8045a0d5d678109750cbd9107a1014f28',\n", " 'c6b583f73fbb0537519a4f36cb438b4561629c6b7d8008535d9e8253e7867b7d',\n", " '08ff627df809b710e7ebde900a85de3f0e1d595434d2a5a3e277bc4f886d143f',\n", " '43367b0e79667bd9f4e116d84729576fec6aaba908d733d47215cdfb9d24f97e',\n", " '320a12caa8fe4e3c2255ff6ed100d8d51d091d417ba6bde2f5d5a0acb768fe1b',\n", " '099fd86c63a53c0a306c445dd8df957a52953df3264b26d1c71302773d0892e9',\n", " 'a9673e8559744effed54b7b861eb718deeaa6ef31ed14e4ea2fb7a6d140be113',\n", " '28847939f975b001ec49bd2ae5959759b06268774c519523f20ffd80bf8dd9a0',\n", " '954dcca8fc8e532fdc3a5adc4cd62c5586bc3c06a1335adc3080cfd879d4c61f',\n", " 'e9854da1207191f95430659792bea7271d98f74424e7040a73e115ecffbe9d75',\n", " 'a35d217b3842287beae43d95302a1671496b161142660f4de4d89c60f5d851a0',\n", " '6ccce1c1b958f973513334186ff650fa00f25bc694211fbcfc7dd8d3bdd72035',\n", " '4735a04fb03b638de9db5995dbfa9cd32066bd0603cc45176668ad81cf7ba078',\n", " 'c984b78865b3125a0d47ec9c8367dd8e8ad5e524475ac0f849aa700aa7ef9168',\n", " '595a1895b9bae7fd7716823cf1459703883140385910b19b512bc19b5f2d2334',\n", " '7550c674424fc680a05d4c7cf8d27e6e571dfdbd2833772c6062f36f4416d16b',\n", " 'a1fd711d100fe5b3a851c2213c77a5f89681f141d6217227602a9c2dd3b93a96',\n", " 'b11659b0dd720f9923cf718132cd2dc5ab883d9f75abeca9cc72906de745798d',\n", " '720b8b948d66e8defa7eee8b9de57fa3ca3c8b40eb668be5fc33d73bd580a8af',\n", " 'a4da2acd9ba8217c4189ccfab53d7138171d786470c2aa24868d658e5466d7ae',\n", " '42f5fbfd921a59a19e476555f450773094962193f0f81ac272a97595a3a7d407',\n", " 'c9d5d8bac045064b4dd358c1f32c16357489d70dcbb5be75dafe8466f6ef0172',\n", " 'c53d1ffc837872d1918601bd606e6f973fff37f06024bd7db4da71d0fe9d34fb',\n", " '7fd5e4abe5f2468a5b9094b2acb2e30e612a8c3d341a918d6b4e96606d3c70ac',\n", " '72805a80cb305c9b1e78524224f2e65c1d090d43e4a5d6715bf94d80327c682f',\n", " '37181c5e320d6ae091c6299ce71e770ff8f9388b14959b105003f1d41aae3fec',\n", " 'ac98850704754c2b9b61e27cad056a275dc78e9ab157f510e319aa2813fee6c5',\n", " 'e50ce54e5979ee8dc6043ff59820e037f205f5f0c1963104ee1c2417cf2d65b4',\n", " 'c10247c7176111714f030080beb1da90fd915fe150a96a3770439d942628ed51',\n", " '92337cc6151d4fa6b823e695d3abff7955c37aaf0cab7ef594aced51aa22b5a6',\n", " 'd9a7ff643ed384950a49d42db29f588c29ddf76b70bf7e5e5a03b799991c11a9',\n", " '059e0dc02f817667d4bc14530a6bde4df968884eab6b6b24bdb776971fd3ceeb',\n", " 'a7bbcd6e95c85501f1268f7fe0f68f771706beb6ffb59114adc77f03068e7554',\n", " '470656cd6f31cf035738011d2ea0e9793b294747c17ee73776854cef946a6e6c',\n", " 'cdcaf57db999c4a785e8e5b296257b024750207abfd7866a8074771157b93940',\n", " 'd8e4881ee410563a61d875dde04164b688ff93e8eba76c9192459e0a32e9c204',\n", " 'b35ff73c6794721dce53bcc10199f35b89750720cdf7b0e211db9267e8d4a90d',\n", " '82ece6a204c7c42645997e0e24488d97cbb8bd7f91c917b4e351374e4b2ee274',\n", " '2b81042cb8faed5c262529106896377af5abbaced2b1fcc8ae07cabf2cfcdf5b',\n", " '182fe02ff1dad853dd507a1fe4b082d87edd3576502bb24b8af6b866d18a3eaa',\n", " '8b2787b3c76ce44003128b8f633ecf4df6dbb3cee66cd7c997dc9d8201bff32b',\n", " 'c71053680684b20f6f718091f265ac5cfc51c1be09fcddc576b589d70e30e4ae',\n", " '92bd84b2ec757554346b5da254f823f0c4a98dd0bde11b35a22f32cb020be03e',\n", " 'dcc112210f1c344b644ab4f90027fecb00086dd60ee6c0af197039946db873d6',\n", " '2f025ae67d526fb2d0d37088f44bac549ee165fca909ee1e625bb1d3481dbc34',\n", " 'ae0d17637d7fede9b662bd5aa73141e329f3e729e635726a3537391753a2ae94',\n", " '32e8821a51e277a81c4a528e1f0673057b90d6fc8797f9415efdda6e88416c8d',\n", " '7e2d9f22c346de1d03b3246de143d7e21bee20520def2c4bff999c52d0ee89ea',\n", " 'fe02120fd4a688fa84e08337b58e18b36aa63fb75b756530a8ea664087141e57',\n", " '4eb156d2657a161f9cc06a1479bf8384054b8290688f504ca32798612f16183b',\n", " '3e66266ac1295363e668ec0319faa163235f3d17b8a1ace9e69c085fbe902c6f',\n", " '4e919781c19f0ae471fb8e460fc364b5d35658f1c914edae98e8b82843bc5629',\n", " '1ae70faa25fd31c0d4e592c2fc2cc582cbe05479bbea3be3c3f82d0b7caedd34',\n", " '085e801a9828ebaf5885d8cf4555934604f1f7b11d93e6101c4968ab81c859d9',\n", " '42b933272e20e37a66248794ffee408c59571a9159492d6808666f157e221eda',\n", " '1d5bdcb469814c8333f99a456ead5c6ba6b1c13cc8ee53e0124293873f490345',\n", " '851e78ccd567e22f010c867e11068664780d4357227464a47d11ab500678e7a5',\n", " '0587217eea161e23709a65a26fd035df9cea36bbf5ea5dd347ee0f7e5590a153',\n", " 'f5d887807d737705ce9480d90a0e21e15e460c21a8d335ed18e56fa11c3ae8a4',\n", " '15519790af2330d05a63f5467c60af100cc6026f2c60224c3ff738b66999c833',\n", " 'eeb13884b16326ab6c8083332084c6a6506af0f421e53c2b70fde798445665e8',\n", " 'e1f7f94ab0aace21b2fcbc233490ca2aee70155974ce86cca838d2efc508825c',\n", " 'bd75cdac518d5d5ac04c886dabbca25421a5fb1d60448848a63e515f764bc71a',\n", " '74df79844a68ad9b1614d431ad26a7ed3219972e43a3e45a8ca0b5e2e7d9e584',\n", " '4c40392ebbfe75f943479ec8b5968269bcdb9172d6ccf97684e56d900a78cddf',\n", " '53f0143428485c84c1bcea205ca283873afaba7c034aa6abe7bdf1539fb6afbe',\n", " '8a08312d69cf5e5cb4a7031ab0beac6c82fff24bdd5760fba4ae466426428479',\n", " 'cc8aa9ca38c42060a16f1e393f8ae4276b1186f3e2a83aff660461ef269f755f',\n", " '52bcd5209ebe351cf788624b4e470a674e443769331f3dccd17cd9c0bc552bb8',\n", " '84d76bff1ad221fb8a058e77509dbd86c1dcff7e492107a2bde88ff26088b4e5',\n", " '3deadbd6a8a58aceeb6e54982566cc3cd6a0be1ab5e44980a102c067109dcb64',\n", " '79adc289e7aa6cd836d1ca650021cfdc557028e7910938ba0e13850654f69ba6',\n", " '6afb49eb3c278804245347b864cb68223eb179d292c2fe97c2fda4de517e6c6c',\n", " '1bd142fdc01915c1253a846d0e7508abd568748c5daf5210973420d2b0010e0d',\n", " 'cd4ec4e04cd4a04749b26237e7840fc852fe41b55d66a9bf0722c39c1606fe07',\n", " '1d4072feb716ca01ac237bdd3101e4947da6e4e5f680c5b7fc94e12b5fbdaba4',\n", " 'd269a546f8841f4121c90ffb76d0ef51b6f66751d9240e429e65f3d1645734da',\n", " '195491c0d2fd389650655d01cbbb16978c11166258fec34a37bd955aede26d42',\n", " '8136655ffe4b7aa5fd9a6d7b60bc302e3a25f91493db6c23741258d60c88de18',\n", " '3ae73063b05f6adb41ad0b29d5f50db2045db992291b118a9e6d1016bad7c824',\n", " '65647fe0648b34ca93ec664c875e7cbb4b70ef2ca07056b45897710a3ed9fad5',\n", " 'df769eef9dd1c723983b9d6810a52569bb42a9d26b7fd678feab0443c7a9d11d',\n", " '3b19fcfbb3440e9f0d9c61c02d9cca3bc4f1769db049bb2cbdd3e4037797dbd8',\n", " 'ab85cc22ca1eec11d4f7bf69ddf6552e1fd5196702588e008b405435b5592a60',\n", " 'aa7f14f96be013c54bcd11a74164720fd6352aea74e6c25f8b4ce31a69436848',\n", " 'abea91e2712a2c5f1fb70fded3e0923575dbfaa4a11c863b29bbb5180b9e087b',\n", " '870259f2af8d62236fe35726b2e17b75109a18a7cf1c763a51a3c251b1dcc3a7',\n", " 'd535c13ea07a04c924e5bf116cd3c7f9f6a2bf10a8f34a0a8020a868672e4c6d',\n", " 'cc2042ad68c01f31d8f6f2d63c258281537c376366bf105b3fcc651a1a825204',\n", " '1912a6eea06112898dab0c3a44f84976233ff5b227a7554a2f7f12457556db78',\n", " '180296a3ea18ce9bf1d9396dd93537f18bc1b81c1a45bcc95a1b91944e61183b',\n", " 'f2a60af93e10105e1d7d3f73b0a5329c45f73e3e483c85b9deba0b98b900360f',\n", " 'dcdc9740e2c3c10aedb6d1f37143f25a635cdf0cf5acf491d97030ca59d25574',\n", " '60295e6b7a0597566c83a46dc47addc5d775e18062f712d72dc7c6dd8c13cecd',\n", " 'f3a7b516fa5809802c3224dc29b4bf47e9c8e6bae46d3d044607a2c4c61b4516',\n", " 'ee73fb40e431116498ffb477fe53b2bd3a204b012aef348804612ee08c983225',\n", " 'b91a87f88c72537b2ca411e5b08ae2004e45a1725f1c8d65720af5a2cafca0c0',\n", " '4952d973fc1855a1a8c3fa4b9d027f29d32d644277c5e3dfd2feefa282b830ff',\n", " '2d5dd9fa883f0622e103077ed4cb89cbfceff59e742065267dca422a75a14ccc',\n", " 'c6e7eb034d461c1a56631f7ff0f961e66bdeb3d19326271e98e0c70d7f9366d4',\n", " 'dd76e9dfe84d8c7dcb32f48c40e453158e0abbc9421f632b530fa92ca263b6e3',\n", " '7d2ec0b7edd418d4775dfa97837555d4232312f7e551c8cdac0c484975ef4d30',\n", " '5355497fd876436166e91c390f174b0cbb7d1a6c055371a83c61d9f1cd17c38b',\n", " 'cf6068a3351cc5f1ce0f26e0a402aab5bce8d768af09d725bfef6a28db116728',\n", " '04a902cc0427ac21b662487038c1af86331f21a54b332bb1c03ff43ada4e4f74',\n", " 'bc5004e0a527998d7c6c3320aead608941a5dd95825c30b767eb5cf34ea64abd',\n", " 'bd5e64c81de9a9ce1954e3c8e12c6f0a3f3ea81d8935fd4e06e4dba4c6fe2aee',\n", " '2f72920a9852b3c80348ac00498644d411cc3cb60b1ef1edfa224399eb48f1d7',\n", " 'beb691dcbdff6ad9aa4c1fb4faf4667744240b7c96ffd084ecabcb5fa23d75ed',\n", " '1d754cae8f24885cecd0f1d65e46dd86ae50d80bf59aad3203aa5d15457fced4',\n", " 'd1d488ef8fed12fd06f143d7a661b089ee17654816d0f67b2bb6bc7b641d48cf',\n", " '76a1e7602c68c7bd7fe2a824c6fc002f8102ff6dfc7c2def1311252a35c0d0ac',\n", " '204ea4794d4740bf452bfe57858f0ac190cffb68862a5fc49e0e34e9bac342c3',\n", " '7e4954294767ccdb036b5a2c82494315efc916eb6f8d2d9c5087c24c8d92cad9',\n", " '4e1a26338dd3c15729608ffc445ae0ed64aed505fb57be7ba68d7bd028b53047',\n", " '2be64614d56136236b18d73cc2a94a1db90920be814be392934af9023ceef7db',\n", " '65880465a8ff68d625184a9ae154c01a012225b64f90563d527211e700f9daa8',\n", " '6e0fe02cfff9bd265f4b6252847f45fb543d55efcbd0f555caae299812a4429d',\n", " '673abc40c817bb920ebab3eb68a88acf19a2155421033f46fd17c410576fd9f4',\n", " 'cfdb4830568f2fd75c123384ccfb4703e0e536b182d0015d01f46a1f599e2269',\n", " '28235d930abedc67f2a2bc8d56c96dfbcb22e69b777c6335a5d2183330e30100',\n", " 'a82de2a8ff9cf9e6ac508a7fcd20772d36caa20b6a54104ff0a8cc1d9b705036',\n", " '8f1cf2c7714c0265dfa23bd68454bb83e23d70543bea7ec30fe7606c57a632a2',\n", " '33b0178dd8cfa16e97af934f69e39a6840fc86b67a2d5ff0d60298683fa4fa6a',\n", " 'a5ffd53a78b22939fe665fc0e791d56685c2b17ffc7f26c1f000c3010e54f640',\n", " 'bd4e983961121ad413d959b5f73f264ffbe04b474a838a707b7034b9b16202de',\n", " 'cd6df26dbeb3488ea16a738d5d93e58e050bf909ee50af3d30c3efeb7a5c4641',\n", " 'bbc3b43534f15643807296e8fa177c13553ea33df872e763f8b829d4736ccc10',\n", " 'cf897f26a46aae4a5f9c01676b060a3c77254b1f47e9eaeb5dc5b7d4de1fe326',\n", " '68011abf5b1bd5ae3102df2337a16ebf78e7d06bd0b4d3f6761e02b94723e99a',\n", " 'a864ebc2ba7128aa342aad31f196c8699fbf624926f42a151ec98f7b7121b3f9',\n", " 'fab0a41a348d5a849f347223e9e4da1ba435a78e2e599d8e59384d9785e4b104',\n", " 'f987bdc1fa626767031fdbeba080bbcaf917217829f1d1f7a81243d3b46bc10b',\n", " '810661dd6e9015f7a2162584138dd23044f7d433ec651e271f5071f2e125bbce',\n", " '420e43af140ddfee6d135f0a43f81f3de5ac6ca1a95fc1e898cfb3da4603548f',\n", " '88300c365504c01de44ea339b78257f2e8685f823e76d10e170e33a37e41d120',\n", " '085f48a4367a0fc1f880453559591f69c1c7d943a26e58dc1f9c26a0d59b11d6',\n", " '8d41733495b31e81da43c78b1cfe64941a98eb9e899d15007b6b4897b4bbbdbf',\n", " '8211fb526238ea95e62098250b8e449ae4fd2ea9b1a77b12f24d32d5f1ea1d40',\n", " '9cf04edfed03f4492913919e625c9eba0e033bfd0ac2c803b18a60ce27cf91b8',\n", " 'c51f5752f7924fce1503fa43b629e6c5c9e98d0d7373dee647b0be93df88a409',\n", " '22beaf24338c97f393b663ce956423e71f98bd4c47e9b52bf5cda9dacded2d9f',\n", " 'c51cd21c0b8c8cc76ca397a8339790ea1192d703d467b9fd368e487653998a2e',\n", " '216017949e4460f606b695f1b0092a004679ca67c8e5b999deb4a4b54d8c0751',\n", " '47ca17f737d4816ffc205fbfc95219d72667f386f9f52903d4825acffb41ca4d',\n", " 'd46938fc799821af097831cfbb8723b4fe7a7a34a3c652afe09be8130f5e2e8d',\n", " 'cd58db79476c0d2c9fa0ce3ca93c7aef115b424c0588776c8f49b03dd361a0f5',\n", " 'de43770701132ce5f5c28f28861f04ef1b7a5fc290888e86d19d518eb958d367',\n", " '6cc565b90d99d91d5ed8c828b7acc6218a85e22e2523626eea81c37416dbf7f7',\n", " '005423f29de409ba305e152c95dc7af275386f44f904f976e19f80379d99b63c',\n", " '7844df70e9d4185dbed528d0a0c0cc745c05db7ec8b75a6dc3ee86e39ecd379e',\n", " 'f3f66fbd70e62e8da8665477389c1dbf3eef0749befbdc529795a16ef45475af',\n", " 'd4524b2ae0a4c3393ced93181447fbb0031ad125c7195f2588c41f325195d755',\n", " 'ca9f6134f2935f9905469466c7a94c0bebe7916582d527ab282c113154807136',\n", " '46ca62d661d44d9e87014c5ae43d44cd0629f587c92ce29ece8de54475ba3a83',\n", " 'b96c3746b32b6e3f23695556c089142aa80f33e11833c937c287747aebf06bb3',\n", " 'a7dcd08e08c58a243156911baa25073abe7c2ea59d450c7254dfd29575cba2d3',\n", " '41ec056ddd0d6d537fddd10688a1f65750072e214cf2e1b69abda913a028d186',\n", " 'f19be4ef0a2fc4a043b19d0e3bc3ced92a7caaf4724e16d435a7a253eb23227c',\n", " 'e466440bb4b611a7f650886d37db90fcbee9f3763033eab0bb0a9f971e613333',\n", " '608962c984c20591f3ab901a36bec53a238df472c5fbee83da7fd3c14562cdd7',\n", " 'dd3b0d9d66912005058e668d691acdbaa776a369af7bd5622243ac26d83fd483',\n", " '8b0f4f33e2d9fe51a5f836363e5d71188a4646a07bbafa39323cfb8b244785ba',\n", " '0d062a28aa37b4ad3cca8c54c41cd877210237f30adfe079e62e3157be99d023',\n", " 'bdd0437abae2a072123d5d6eb1d3d19419c97383879772fa1455871dc850d6fc',\n", " '18e996a046bddf1f4f2cd574a147b633d09ff94c8591f586eee0aedd66c836c5',\n", " '1f2f9d0f05b3cb6c7d58bf51f33bafe4c6f0576efc41d45e8e49f8333207979b',\n", " '1abe2daafc7de0b40715b3eefbc2c8b629b9fc75f1b4a4efa1250793efa99280',\n", " 'f7729c65817200da44013f2c9744406df9f7f51792b4377d7b3ef66d4d878d48',\n", " 'ceec41a94325e8989afec170bd2a26492831de3bee65b88181774326f9662695',\n", " 'f3c4cee1a481c51518b03399d951ca973212aef2697ae1928876b3ba2bd94edb',\n", " '303796586a447a262914418bfd82c199ad65cd8f042b427bcdd89a66d5290989',\n", " 'e81b188d081af5e4112c5f4a6f0755c558bffd714002bdb4469374ce3af1394f',\n", " 'f9f7d3e5a2540552c5fcb5a66f12d43dfdba6b369d4361fbf7b5d20b7cb042db',\n", " 'aef55f20b7e14e811022e996a5def6ae9447123d9223532d89ed208d672c203d',\n", " '32117e9f63d7d5f448eecf13cbf1c13985bc57b195a155ad46a68c3f7d1b7fe5',\n", " '50d8b88db1611643b2dc5eaa87863fd7822d99e10893af15a566994e74fef9b8',\n", " '660c3edb6ba0c86c5023a02f9e6a5330b89abf3a39c1034776a87e3fc65996de',\n", " '0d10e706dac2048650d09f5cb8abf28037d801b503e0e7a7f332b6377f24afd4',\n", " '78e34e61dacfa51b06acc1ab070461b7ee2ead677224b777c076ab9840f5e264',\n", " 'c0c35e85c63f765c48917e3d846e5961f079a199638214acce038c5e25eaafe4',\n", " 'e7f341f914c8716cfcc444c844e70ab1e472d3e068aaa67823d54a421d13bbec',\n", " '8e9baedd61faeb59fa17a23ffb20b367fc0e709b30914ea44883e8b2dfb94981',\n", " '976591f38512c859f95fc723ade44f62db7584e803e64f9bec14bce1f6dba3ad',\n", " '9a246e4e6536cb09ae0eb1d9c721dfe9f12bd7029e5b53dec1ce7e69862c2b5d',\n", " 'ee310983e3f607393c8e9bb582893cb3fb7b72ee5581e900c946f54c2cfd7de5',\n", " '1aa68c0c0cc3f74ae90e2b613e25ac11914a004fbe91d07311bf5bcdbe3318c3',\n", " 'b520e4163d5ab31198b1cfe122c2b0efba714cf5135fb9e75f6b55d316b55d8d',\n", " 'a2df7a2e9a2d4501b95381c31379265ad238821b524d5d393c96331b0f5e68d0',\n", " '9e60533bb6e2ce65636b190cf4107a3df1c7184813fc3b31130c926d57de8556',\n", " 'f93f921b4bdbe833a8aa7e90273a7eb3a7c23d99fba970b1bc223cfce2a0da0c',\n", " '8e131a047afed58718647aa4b2efd1a5382bb7fcce20257f1a05f6e788b87c16',\n", " '5a61d8e8f1f4dfeac98e6668fae32a5789e9039c5b6ca2a54d0affa803302c4b',\n", " '303f6b277922caea4fbd3869f7ac691c15293f211afa9724fecbe85bb7bab109',\n", " '0e52dfda007b79b19dc6ccc1536a457286b3113ba4ac672ddfc5bcfecba23805',\n", " 'b68650994f9e704749117899b13e26763d3ac1f078bc9543e1fab3f2cbcf58b6',\n", " 'dd3f28e15ea5bad1f19309f3f24bceb185f07a85864de45ab60fcd94165d88c5',\n", " '1815f4acca61a486ccf677c22ca9785d1fc80b7f133d8dacd1857592c8bc70e6',\n", " 'ae3855b08db6367ea9b34a361e44db1be0c060e1b367a80c0a6abfb69ed54d7e',\n", " 'bc63d710af49c81f818c464b58c175d6c84e6b49712711d0ee4ca995dc50a3e6',\n", " '75d2134d4a0d06bb7aabaafbb281a4ac4477a365fd2aaa99a2846f4e2f5e0009',\n", " 'abf92c29f6a01f61fd98517e4cc354dc47d46a68d75d2edfcb255df3ba7a00de',\n", " '1e62653fa2f1e54393072d19afa8a5916f3d1129cff40a09ec4413e8431c49f2',\n", " '430a4a86ca3a1256d5bc8200282998719d66ee126fe2064a469fdf491d207dfa',\n", " '0ffcc9380ee40e105a09154798a779f07c11a4e54275db6877b832ca599e90d6',\n", " 'bf5df87446893946501bc8efbc47c4d221cd9ee4ce64695c672dc46b18d4ffc8',\n", " 'd8ffab18541e06882a48150357ee48357db043f63e73f4efab4c1f48b880c302',\n", " '08f8a2fea15a4e4cb6321020d86a844125aec18f494806dd8549ff35b09eb63a',\n", " 'dfb61297dcba82bcce26f1923a6dee6399280ffa9a46ec5021f90c26f43094ee',\n", " '445df5bd27e4482cee6f8d4e14a527ceb5c4742a46adc56ff29da3e5b6cf4965',\n", " '0a02a319543e7bc62bf6b32dd85abca5c400ebbc13ddf1b56eaa7c5a97bafa39',\n", " 'a68e9259fbfb513dcea67068bfc7330c96cd3fbe8938669ac25cba41b179d6cd',\n", " '8a08b693db978856aa1298b034bd7917611f5ab3b613dc635ffb0b47dc58e060',\n", " '68a26641c5d79e0be6c59d1415f43650fd3de1d50a7abeea5401098cc8b93c52',\n", " '49a359d0e479ea1233887a0835856428cd0ef66c72139f729459d5e92ee7cd50',\n", " '007073dfa71ac9e003c663c581d8d37a2d58afbfd152c28dac749c7285a4d722',\n", " '96399fea9ae68965d8fd9065c7169f410ff85f926aba319f8dbb45219abc1390',\n", " '471f188f7a8aabfb9a4ec2828fa4d8d34c68e3ee36ff9dd5e28902e8549ab992',\n", " '6147ba6b11dde82e4a762922d8e612f2ea59d0d7e7164ccadbd3cb8922419052',\n", " '8f0355189a4f97dec7a6524a5bf30e28ab43f91cf81b48242973d005424ea20e',\n", " '3deb34a37240edcdf9ccdb68a63fc1b20c082579ff6dc2d6684a0b5195725838',\n", " 'c02af7c9bb632f0be79a2e06f9c680b019086be461d288adf85463692a32cc07',\n", " '1a31fcdd198de4bdf98f643a349f385360b909dc12113b97242ff6636aff4aaf',\n", " '13e9332b06a20ecb87b156bd653f64bfd5a3640ace837e20912305b379497e07',\n", " '4fbe0e255697ffa387b5cccde71da9f1d1e36c4c831de9b1ef082455d070970a',\n", " '85a167f218243066558f8a8aa6a9fc09c06cd4c534eec584e773ab2186a0330a',\n", " '237d81f2a832a4351fbc27f521b916409e2d2f624dd212e0c9f01aace8e60e00',\n", " 'b62bdf42b038ee6d047eb9c696d96ad1c5ebc6f213c0370196ef7a5b1f98a654',\n", " '7b790d690420c9616e33a6f0750dddd28b70bd227722e3ed8b62f8dec8dd57ac',\n", " '30a64ce73c59fc808fee34c3670acf1bfc7b4c7ae6183f2c1d4bbdf5f583ee09',\n", " 'dac6644b6bae5cceab101e68b43d226cda9e60d672b88214872d4648cecd137a',\n", " '32e5f5fdde4e4cf6643cd42867e098ae4ed1cc895ebe082518fa76069e9b879c',\n", " '31b2121d3c77e116818a98ecf8d33fe8122404ae061cf5c98092b1cc14db06e1',\n", " '72453f183178d0b01e0b59dadfc541721d2f79614f95a88b5acc78e62a4ac156',\n", " '249b8e05019a2d6c6c9e35df3797423527b08e329ab642d6fe84920b1bac4d08',\n", " '124ca9d636d0cb4455697179b70bf33834acb48e46067c9ca560766067b3e2b8',\n", " '911088f80449a15dce3766eacdf95ee538f892933bfef29e7e20ec3488b78dbd',\n", " '900d8377812c55bf23638bb7645187b5d4318f9c3d6e903976d121058ebc8a32',\n", " 'a60ef37b019a2d60a31a734755b47f192137fa08a8d1f5edc6234f622cf6578c',\n", " 'fd7eb798f01e72e596e2826301f4d1479871852c337c5024614eb41dd6464419',\n", " 'bc7d182cf6ee1321cec77528b407f11e0c8fd5f2a9a9ccb0ca7732b21dc5f7dd',\n", " '4752abacebf1921b21506d35ae59d554dfb7bb122cc73f63223c7adffe2dfd70',\n", " 'c7d574e95f929ca386bff7ee3c05424a28fb6e50e0e2c4802696f7b2d2c3f7f5',\n", " '23bfd87a612ae9e79333370380109e8f2465948253a96dd88d3d2a7780382cda',\n", " '510a6f999f026f7e9664cc974246c036fd97ac629f8ed4947ae15e92bde6cd48',\n", " 'ae6a60d93f8a8604fba54160e0a529101d2602ea283559c3eb8d8fe46e7f419c',\n", " '7bc2ced2360980e24ff18c1f2cac41e24b72c66c0405f44d643cd779ed5af13d',\n", " 'afe68b797ed625e06f9c5e765d717fc2c128f755f4c0f8f5e1ff25ece1fdcd8d',\n", " 'cf234da757027a9b465fab1653e18513c5c8a0eb2f90cbb11ae7ba8e213e2110',\n", " '57220b8e23a6d7aa042a3236c8b1e3663796fd065625d163fc6f9d1baecfc73a',\n", " 'b4a99bdd75bb0851bf95cf69b8f9462611d9216113b6144cd50472e89a78f58d',\n", " '04f98f0ba1920a86e854b64d664b699bef4bc3d8eae6b76066a30ac5b0e8b7f6',\n", " '25c623d8061afdfb650a478cd284037f077d76731d642bb65e6288037f157893',\n", " 'bc3bd4bedb49833adc1fabe2943f0fa4f18f5b5e0213202bcc5429d5e62f2b44',\n", " '089a7d964b35cb1e3bb05e6de6a5470170228c40e3e2e75f58ae2d93924a1425',\n", " '2f15a6ab3dc0c0d8ea6ed9adf98dcba36db12e9d9237439453d66f4404658be9',\n", " '8397aba07a78b7075f634a9ca1a1f49e3437a3dff1eaceb0f896fb7cce0019f5',\n", " '0fe7ba2a4dcb6f109e110ce4e67d9fb557f91a9c697858d8a6990d5f4f64968c',\n", " 'ec8b86ae3163179d997fb599848fe44737105acfbe03a14e60d5fa5bd5830326',\n", " 'd352953a811b342c4a76a3e23e046e322d7d9fde202b268165f858c0432f8044',\n", " '5197be52e4477dc882bd31be0023522ce13f68ea253c24305d77c54eb8960095',\n", " '818de67ad618f4db16d4fd1d414a2b726223c79dc71b66bb35bfb5fd01a6f49c',\n", " 'a75e2ae1bd93a4731c8319ced3fb8c7e0f81ea2d9f721855b8e9bd4bec7e95de',\n", " '8c6ab085c27d2235ccd3e392ee17259ff0dc26e1411ce7011310427e9a4c3fca',\n", " '01ed373b3690a4056c748aa0f8a184a06c232e4b317a9eb0899c83174ad17b55',\n", " '78f06923834eb900ade769b9a3ef333aba3e256938c953cb16a3aa68acb71faa',\n", " '3eddade41a395b53ca6bcb31c5f25a85595a4fd8adbfa9d37ea7da4a8c572a04',\n", " '11cb5c3f7558a57eac575f0acad9a17f9804bfdea0eabeeab84579276f7455f8',\n", " 'a325705de67843909872375f3a69d624505131868ebe01c4778caed5aca2285b',\n", " '87b230ba6b127c7ec3f1334e46c5fa10f0a84c00391803628063a8d217b3f4bf',\n", " '833ba4464bf05a08fafc740ddccf694a9f8312d88b6affdd4d4ccfda1fc5bd21',\n", " '6715e2ab9e4fc52218f0660f00635a2fdc44c63e4989aa02edad2b6cb0e18996',\n", " 'd364156e8ff581f9441600c4749eba030c0907822c024ab99a7440f88cc64258',\n", " 'ffecb938d082792cb6761ca1e3b0b424648caacb44058dd2a7b67b56c11f2e6e',\n", " 'b9c26e1292aa9ffb9baf69a2e2ebb59afcf6258e4ae1f49732bab131a5a55256',\n", " 'e391c33f45037c3a421f2d78fa13603a721b7af36816723d6f244044453eb17a',\n", " 'd5932764d8c5e6f7a9a5d0a66fb72efa5c511d61d4300c8845d11106c04a04ea',\n", " '82cbd549312dab83b864ef69c01a185d576ec17df5754be53cb9261f5e3fc11e',\n", " '3e13a962b9334314d6999a5f5c63d669dc74e04e9a305988d0707f6478e628de',\n", " 'cbf3d3abeda4b74c18fff7ad721afe9314048eeeacb89e208d64fbbb307e2f47',\n", " 'f35c354f8e663c0f7d1b475b5a99c88c63b27739928221b93992f13b55cde808',\n", " '41eb96def9bb14260fdaad4f8841399b2aaf70e2977061c60b018d3907527b38',\n", " '4a59ae00d40395e38855ffe724f87f08ca25e047820286f1e446508c628d7fdf',\n", " '41e8d72f7a6217ed466b3761abc601471bf66a89b8f58c2c8fe34b286e9776e9',\n", " '56d85857f9c17c35da2162327dd938902ba958fb282c6dc21562bf3327c32449',\n", " '0710a90b33f5cda613450fd0e8b69e9f267ab626940b14afb3e20243e551843f',\n", " 'd58c13e07a218af962a90f93936684f5acad9be5764388826e0f12aa89902cae',\n", " '67c8747d63da5588da4e6ad3d98000c963c3b55683afc08d65d881bd3679f54d',\n", " 'b1b329e7915fc07f3e0c9c6f8bafb7cd6aa7d500e496094e560e7a08972c603e',\n", " '076eecf15ed1bfeea1cb92016837ea3a829b1c4f381d9c6ea76d3955c1bdac09',\n", " '71fa27c90eeab7aac0a9b0de1ba4fecdc3510622c9c6cc0dd495917e00c21aef',\n", " 'c13f7b5d5a3adc6e2e74879909a162c9ae6bb5545ecf8dc472c7d188c60aa331',\n", " '0af78e604042ffb5bb8824ecbdd77cc6558be48abdabaa96816782716b2a7fb9',\n", " '6e533e7ec538c5d8249146f2b5b2a4a94789ff0632993f929a39c3ed028d5d6e',\n", " 'b0af8d99e7547c062c2e55a3fd6cd1c46719bff4f0a1f0fa1215d037ef61dd84',\n", " 'ed8577e1aef999c50be68235270f7fcdae11e81303376b3f8ff58e846b851450',\n", " '89ab3837993edf917dbe70139e0d71173af322ac24f6cf4846f589c548f8500e',\n", " 'd7126d4a2e99c4b265d916d088268a110d97434e33ba5e493993899615eda72e',\n", " '89ff18c92ed3fe2d43d3ecdedec61bfdcd5af1a958abf70e87eb2c59cacdb5a9',\n", " '9e2ddfc8f49faec9fceb0615f08fa3eea778664226006a1ad82291dee4e9b1a5',\n", " '345683d60be1b6bb3e3384074339834fc24be6ab607a38a3a3a7e54035bc5fc5',\n", " '49689cc4db09cd65093db1e19e505280f63366699aee077ac7c81e67325465fb',\n", " 'eef954b035f8e290617de847bf71c3a28c79e8f6d707a7adf8aa55842abeb68c',\n", " '381451b8873deabceb414eab9e523568138d0a52a979adaa92dc790f4411cd04',\n", " 'ab119f6c1062ff0dd0c37cf36305a8f2f4862d2d78964d05a568535578704d1a',\n", " '790afe6a5fec54eee136145709da537136979e363d53a884f6f314d57a383304',\n", " '1e7884da4eb2fcd60a5fa9a6eab0e524cad6bfdebeae350fbcf0c3e4eadd3e25',\n", " 'ed55d8aa63a74df1e67a58289175ba0712aedc64daf6adbc4626643bb4f40c78',\n", " 'a2fefc11b2cdba302ed9addd622bb4f48829669d300f20a380795cc86cd22727',\n", " 'c3f25c6718cdf9bfbd74e2add33a01fd4d528ee02955da88dd4385c002cc0ab8',\n", " 'cbf3a4bf4fae7e032b4bab9adc6dce316c962a179f1083e47386626a5ac9b207',\n", " '0f681be6d39382108cc8a00abd6ed4e11f75e8e423f0863fef49482d4a20b3d0',\n", " '757af4a1acab5ede6dca5affc5e5af4305b04c449ad189efaa16704a6a902923',\n", " 'f45c0387836871284cfda397b547c3d95bdcb9153d22893e32476460326a4e14',\n", " '7ed088621a5b2069835391ced8ce565d3463998ebac985da9bdf9dbb1c684d5b',\n", " '45111e63718c8bf25855e698b382fe5255f309d3a5caf6bf995e78f326250a08',\n", " 'd1b84092f13f261acb47fe53986ff8742583ea45ca7cd952a64194ac67aadb57',\n", " '58475c667e85f6cb98f050f4f0e983800c54ac989b9541624aa156e4c6633b25',\n", " 'f6cee906ac65357a8deb2a0702cdbe9edb3f8619b119941fdf53deca6a6c7894',\n", " '4808afd797f5360f8b98a440a5d693bb1223f7ac9d52f41d6e3ea016670d4eab',\n", " '9b835a21ecff2c33bdec2703cfb63bff7bdbc892db21b4214e1db2009a654eae',\n", " 'adffaff74614118fff60b467a436fd6036fe44e3d277f1d4b5061b45c0ec70f1',\n", " 'fb78c87332f6bbc9cbed800640021e8e238f5aa4d287c9edf90f263eef3a76fd',\n", " '596dc1ee50a099dea8394c83b18a03ec7c0a8d405f9facd881d122ad0c37e9c5',\n", " '65bda6451ba4befdf6c809691703a48f3066878b418bb3a5d8b4ae1ec5fd8587',\n", " 'e0c12639f68bbd5142e24ac1a9d547a0737e086217f455bfc2678d2af2280811',\n", " '3622649eef97e92fd0abdf361d3334c6c8edc0acb66452f39fe8ff81f9b9ff27',\n", " 'f94a32da2c45dc3565bdc9c12fecb872e65c61a71c80ac0c84de81c434baa97d',\n", " '75d83084030d78f2deb69a2e4d3cfeec02314ab4cc136e22834fb7ac920ef99b',\n", " '9686f26f4b986ac5068ba7ba0f251a4d43b3cc1f60bbb3588aad26719534b336',\n", " '36dc6492c0a31096e8b169c30e22020ddfdb1bbd1857cc876331e5da027e3557',\n", " 'ae54231702f6b5ca92b65327f98d34d25fadf9ae4b0892f159e7b51b703d28be',\n", " 'ef9a6dd4fe8036ac20bbdfad374d0be106b592609f9fd9bc03330512e8f42ece',\n", " 'fb00230199296d9c209db071223397efc81ede9194efedf95e7c046c36b5430c',\n", " '33fc999aad0fd851f8945a900050545704f99335421693f3fe0a30ec41b46467',\n", " '17db013d4dba050de2cf1459d7f20c196bbb7b0f65438683256e99e93509eab9',\n", " 'e20b3e2085d382bddc038cdba3b9ed044e1da4fd6bf494bc59f0db1df18ded55',\n", " '81e6db815b101a650964ad5fbcbd869a9d67170e97ad53f2f3db85572e0bcb5e',\n", " 'fc7cd62813d95865326a7057b975ffdb966a8248cf2637a455eb60363b7963c9',\n", " 'ee4dff7e94640c03cc6eda372ebbe2e6fdf4f367a0f9657edb0a6f701f19c707',\n", " '046814dc7b9d38b24bef5ae7f4dfe63dea03f5b78bd5e5c8c741f5834b232b29',\n", " '6508a8c2c2019e738bffa113f1ae859a19536b0f0e9eee4479d2a9bff3faf6af',\n", " 'ae05203a6b3feaaeaffe4531ee17d364be6080369a357e13ca611ae7c8249b2f',\n", " '6f3bafc912f26795a818622b594c4b616a3ded4916ba148840dd9e953945e14b',\n", " 'db46164e02992a39d89a6d00662ed03633c7a49652997dba4c918187ee4007f5',\n", " '21b26f2b2a2e050126ffafc323df7f2f962bf84c8750a28d156ab46062262d68',\n", " '97dd21f689fcd1209cae57fdb1a0d09307a5685648a4aa3f724df950ee0e819e',\n", " '522e31d7e62cdd47308054ae86ffe8ff4f514cd55609cc7c069bb7375e02f6e7',\n", " 'c54a38c67d75633ff8148aaa116527e82ca785f9e2ecbf653add632cf7fd996b',\n", " '91c7adf9d2c5df6d238086bacdf74723f7ead51d24765f2e359947b6fa7093ff',\n", " '97d509cd9482dec43b8a1246c6db30daf5d3dd3c6fc3bfb4967c0cfba079de79',\n", " 'd3c7b12cfa91b166f25164fd143e742a218d61a9007dc7fa271cf857216fcaf1',\n", " 'f83e4758704c707fd32cb6aaaaba9d48e63a91b9faa7700dbe1f5198c69f6304',\n", " 'd875b44f754ca2a791ecd2d361d207772c2e1501fb6653ab0721c67c6f1bc64f',\n", " '768b8facaaa485d6d9d5161d6ca2cf3f1ecfd6ee8236f1b3d3282508c22e51a2',\n", " '4fe91c21999f691cdf0872533889decb251bf5206186fe081329e5420cdfad78',\n", " 'c29b91519bfb95dd63f9c238e916b9641251b8212e6cc251271e1c4b8eed0ac3',\n", " '95a880421a68f82dea7c6aefc4762cf7722faf324a3625190c001a76e1b953cf',\n", " '164e55dce2e36c3fd95a5f81c6e0e052a9c617e9fa33d19054e38611abf556c3',\n", " 'fd5be566ce2679aeb0a3dc106f4751f46c69ca6754c8c8e4263a80b0b180b410',\n", " '705e56df0cda3cb81ec267a268c1614354c67d888373de8d9ebc2a26d2af9bfb',\n", " '0aabc327b7760d3339ea2a0d8cdfc39d71d9878d4885c79d72dafadacc5c83d2',\n", " '1061dd3fb811a933013b47eaf8aea5e38d3662752742b7e49ed378b92484377e',\n", " '463fb2eb0620c2b76880aef1955c19ebccb484743e2b98a54779260626fcc7e9',\n", " '1e4a7f7a497a250e7bfdf5f1510d22d8a0d3c85f93e72dbb94ece64cd7be03a1',\n", " '262fdce7a5a482619949b84bd32a1b939b9071732343e726080b663a9d22f371',\n", " '3324128c939a31001542730c45207863b624bf6d9fbad65943cfb7e742d14d9e',\n", " 'da8527fffe8d74e5980a5ad2cdfc15c0ee53dfdda9f079ef51acfeab26ac9882',\n", " '49afc80c27e7562b3e163158ce50d78b8136ccdc007dfaa221f505b7ea2b928d',\n", " 'cba170b583660b82eb8a7cd13502652208b1879faca4c84e443cb6953a9db0de',\n", " '2584c8098f837f233628846e4691d12fcc5e953e0915b980d3f8acc1ab4ee1a3',\n", " '518a76c76865e9323391d80eee3fbd3c83c3938bbb8044d9ef244a160866e5a3',\n", " '315905003e8169094e2876c3bdfc871bb409d9bcb88b0ac8fa8e489e9b209e0f',\n", " '87da34076226783a4bcda7e1a443447b1fe2082bc029b4688729bb5bd2de7197',\n", " '9f76d6a28d0bfe1aa53deca8536f78e0993b48b9fd5cda275c51252fab6be113',\n", " '35f1c324169cb53e8a2eb6ad124172eca31ff51b5b87ae40cbf3db9079f56220',\n", " 'd45cd278b4fbe0bf9f7d5d1c1f2bf117f1d1c185d2cdaa7e77a4a8f78f233555',\n", " '169bf8354f420f298daa12dc5b416920bc4021a287c52c73c5443631389a33b2',\n", " '8ff74f93087e449958d16b8abdf11162c81147e044f8b08a4b94fcd817aba0ed',\n", " 'f9b8b8622e889f572bd593f608b428b200eb944eb2515c80c1bc45432b3c0f90',\n", " 'bbf84bfdb755942670e5788b7617af4057f04b73440597b322f810d16035c885',\n", " '5beeda95fccec4a3973de00a9421e4f4d44fe0d8373963fa1fcfd33d719930a3',\n", " 'd564ced63f7c4e5b6a9d5480575d8e87cfae0d9dc52f7ee38491b960a25f0c46',\n", " '994ebadd89af3b74f7a4d41df0318c606d817c41e57f5f9968c0b7571070a813',\n", " 'f3447a29c9bd4757b90d8442e6d49806aefba79928b1292fe9d3b2c035355757',\n", " '3c161cb060be56c9b62a3dd1b88184a26d50e7e69533024b46c65fdb952a9572',\n", " '7ae806eaedc6fcf087ef7dd83d47837331c55fd69005832a1e146054bed1472d',\n", " 'f452ca8e63883eb7ad75970b735d7c76e69c01240685a63170e28fbb084c2777',\n", " 'cf4c2b5e22afb57fa79bbbf411ae27ce3eeba0f0c171c0cd0c8ee9075d95efcc',\n", " 'd1bad812e48e211f6f90ef5e3a5393ea15cd549d10f923453e90eea3ad01c5ee',\n", " '5e56eed4fd281badb9bd70d89e9256a316b7d330d3c0112a727fb89c386e75d8',\n", " '9f84ae33b1a41da8ec8e859d951f62375bbcfee647823a29a2a7e15ec78884e0',\n", " '36770d11dad0de1e37ef005271920d9809f7396962d926476479c29e7bb61c2e',\n", " 'fb522b7d1de59c5e7b04919a4eddb596c1cadf94e4a2a73a6c6c014328fc51b4',\n", " '1cf482d8de74ab762a7e6fd60c1fc6fc6a38761604e9dc96d6f043da82538fcc',\n", " 'c462370d8734a44b5f45426dbc8e861af25ccc8e381617ddd8d2eeae7bff1c4c',\n", " '2661db9f66c1b077847a121ff6a9f4b9bd5c15efe7507290bcaba9f23eb07597',\n", " '22b9e9c6e19dcf436e86a6021ed6235de17e06862dca412821430efdcbe601c9',\n", " 'f4e2ac84dd1effeb4cbc3e9e04274c65af68ad191ecd35c5b5ea2a1a8271191f',\n", " 'c7ea95b888a45d938025af3aef31e2a1192b2fb2ccaf3bc52df6996ac32ec9bc',\n", " '3deaf5ef794bd3ad31967dfd0ca33e2037fc12bfec35b5015c16f1723541f527',\n", " 'e1bf8ba0a75b88dc7966baea17e2f7a46ddacc9bd443f561651c3e4fc34bfb7d',\n", " 'c1c8f27a1f5ea6177757a676a46d47b0756f4f7cf53b7a3ec1abffc44521219f',\n", " '5581eff1450a12aca18102dd7073da421068fc0d2fda9a5f63da9029a194deab',\n", " '955d3ac80550105051cc2b18e221c33d7d1bde8efee9d16fd623eb44fd58dd3d',\n", " '3070d197d6ae6821cbdb514b5fd0aa032bd20e21a8409786f9ed66583d42e82d',\n", " 'b18d00972eeacdd6c475db6d81a4a04f355d3c43a2aadad21033e6166822de75',\n", " 'd15800a317d4ac4836893b3ba90abcadecbf29f97fa70ce3a98ffd8593ef4343',\n", " 'a9c663749b134ff57e8ae92dd4411a35fdca698083c9da9a04a030e4a016bd07',\n", " '2e4942fcf50456a345764efd1aa46bfeaba7be723183d6341b7debb4e4e6df98',\n", " 'f4e1caa0131bf6cee1456b98fc6ad67b053f9dc4db438cc23164d845c11f95df',\n", " '822f8ac7e4040eb74645cceec73cc52bcad5e30b1ad83ff0e5c39b2d0ef584e8',\n", " 'dad1a9cbcc8265377880988acd5e929368bcac6e1b0789e10972c4f5630ff3f2',\n", " 'd8b302977047cc4e81e0932733443fefcfdb0ae0dfe7872a0f10eb2f5be0faba',\n", " 'b7639acb3ae5c5beb25c4a42679e102df171d8b05df4cea1a2384d9eaf7728b6',\n", " '8eb7db74189442372b0883028195b4baf8073192815f78ca9441f5052bdd7e1b',\n", " '4aec97e8d63ac212e29f81e9357fd26803ca7bb5027026cc60bccde81091b0b7',\n", " '1d653787db11626469cde60535c9e03de70287d17b2ff3cb67b2b7dec6986c00',\n", " '808d89a27e19704e2c6f80ce33935d64f8faf22e6b2a70ffde9019e401c744ea',\n", " 'b23f1d4400eba4cbde82437015fea859bd9bad7dece511b3137845b9cb9e3054',\n", " '6fd2b6bc846b02ca1cd44975639a1a7d3e07366dc0857d5d9dab5c9ee9ce169a',\n", " '4249c47aa6ffdbe8621fd863b671acd9d98358f10fee6a3f08ddf0be8f80d88d',\n", " 'e93e2cd0a7ffeebd9aa528a4c084578676c1646d617b38113f058a610d5406cf',\n", " 'b7862ac4a18c062acecf3dd5459c67cb6d28cb2a9026daa84220ddce3680413c',\n", " '4ca40a8a29b9e5f32bcba51105451c50164e96212b6eb46f806765c2034ad2cf',\n", " 'c3a4c4bc9794856438e5ebe5a9d635fb3027e881f85ba98d0d9345927dc46004',\n", " '5b8d0f7e15c49d0907d3c9da54226985140562deb0231d2edb97818dfa583b21',\n", " '1f42c61a8066dee89873ed9db2badb2ba9bc62e572dcdcc8ddabe0b5735e563e',\n", " '197b41d4460212eb358517feff8234743a0a14a28171ff388d4c2dd16f1c241b',\n", " '731e43328a43c66c69a3518c896743f71c193c10ec3b3fbfbbc0b618136dfbde',\n", " 'd572b4ac94b511aa03e02d618bc7788d21b37efe96fb590c54503242c8cc3307',\n", " '0c1ddcc4a1152781e99c5efb1baa47d5830222ce99bb298f0849bac4b067c882',\n", " '6be61ff3170887df6240901cd25ca806e302992bd5f9c48fa3047d3810993260',\n", " '4ee08c7de3d836261d5d6c9c79e720f39bcf1a047336b0d61eb4bdfb0b27c91e',\n", " '09accfa7f7f13f48ea85dc44aca972c7e9172960406ce9c52b294e1be7083e27',\n", " 'bc1005b756ba4ef7d9aae10cb1cc4ec6342d972ca96e713829f170d1008d37e2',\n", " 'ffeb68ffb147b965f4029a3839c9e286328368b22e1c344f852538fe957323ba',\n", " '362042a393622f4aa09b69d720a2a1a56a4416506a6eefacdd94ea7511fa131f',\n", " 'be8ac59fec744f4b533c252e0534d28df5672b65e1add9a5f14581fdec68f31e',\n", " '72e7a3744d39c2e7b61c41b682f1835bc8eb21242cc35193f7ac31eaed791abf',\n", " '0799db89941d6dd124898bc04b754ab7ea880d848fdd63cc69ca9319dbfee005',\n", " 'fa317077adf4d3ca17122b5eae15b682e83919169052078bdf2fde4e97ad545b',\n", " '7a7f9035bb58b9c8d269d95c4f3ee79449f4d28093d40ca6929d49d007eb78b4',\n", " '7f60f10d3a72507b836f1b8318276b14a427339175109a84a6acba3e3be54730',\n", " 'f956a04a5b48ce6c2941bf2c1f9a7e8a626e4b7f6aa819a7a0fffd6dd65a4415',\n", " '7805a5a739fa4ad0785b7273e89996fe2338da313f27fc47a21c727d570d8539',\n", " '25f68f68b51df5d07bba187993c662b19116b96fc6377fce13f424cb12fff16f',\n", " '816f8da72db721b5b33602bacaa0048fa8cc9ddb5b048b6270ddaffa23740aa6',\n", " '22d4db633e8ee89af82712b8f36f8ede795ff1fafa3d7987fca4d53a020af1f9',\n", " 'e86709f25272b5b1d7672ac282fa63b4721ea8f493c0625ae07703feee92ab3d',\n", " 'bcb765f29454c08dcae4e16df6e0480f9ba3e5c4dfd91e91af15cdbfb63a89fd',\n", " 'ad6bc9367486191d59fb7a15e34e66c1ed53888e7208283a97c5913c87f1d8a5',\n", " '8b111634d748613e4d1b27ef5f0de14a01c7567ab6efe549fab24173f745497f',\n", " '44c926c3f8a686a34d14c362e02e4c531622edb027a00f82c58898f23d646dfd',\n", " '3250bc82a945003918983c9e927e82915c8968e3fe4527214053dfe6bfea493a',\n", " '0ae68b918adc254b7bd13e6e59b782b652e9a591f455a1600d04fdc3d202cb85',\n", " '5c2e2290b3e57bfe805e15fe024129598d494db363d21557757a9d6a4a06396d',\n", " 'c24e4f48297c37658da4ff386cad71b98d091de53b804bce8427956affe8ca0b',\n", " 'ba4bae36e83b270881ca44509fc10b8a301e4d8e3a69357aa62046b39ff23bdd',\n", " 'fb0e41159dea201498262b28ddb3efb9c1e74a2199c17d42ea9eea4f60e53402',\n", " '8289f25233732a41a5e2dc6ca76029e8fcf2498c3217eb46e606a7b5c93f41c8',\n", " 'da186c372c8c410731aef1cffca16cdcc3e851e481c71210418c5a4edce10ede',\n", " '394363e3a197b2b2534b53aa1fe23febf0eb5952ca65c499945cbdfee33c0dba',\n", " '26d59333becc56bdc49e33056073d72f516037567f9979a683d6ab85b96be6bb',\n", " '7d914c978fe7bc6438d99e06287b99ac7b649a47a267a3c06d3d9188d9c7699e',\n", " '8ab676bfb45ad76a4860e8d5b025d372d65f03749c13df46b0b16bddb8e67413',\n", " '09b4b2cea56b52ddcaa2169193a29c07cc46409294ef14a3f23a36a2905209cb',\n", " '2510f9e974c610505eb1bb82e98ed0761831a48b554feadeef991a9185e87a0c',\n", " '96f646b43f4a7cdfd1d3eda3e65e13ebeb1ab5cd8eb6215126d90fe27c67417b',\n", " '519e5cedc4358c3fec7d8f12c730ab3e5e957e816a0319c488ca5fde845db547',\n", " 'e51543fe9cfc2ffbbdf4cd2a5715d37d959cefa9e0e7bd65a2815c6cc1c515eb',\n", " '8c89892293227c933ec0f8bffcd36d94782649065569969fea30fb571b755c62',\n", " '915974d837c5636b44682894a3260b229c084c80e1287f8d5954aeac85256208',\n", " 'ab970cdd7ec6c39669c52f5e2d477466d05dd8fe3fa31415d9e846345130444e',\n", " 'bc303624637c374536898887eb73dd14e7c955484779b5b6512ccac5571f806f',\n", " 'b95b3c0de7a384d31f9aa799a5d1da36640869a0459bb330c05cd93242c4819f',\n", " 'ccebca2be6f4091450b812e99cbdda88f79d4e31ec9fd8026bf30a07af7a0ad1',\n", " '140fb97ad06980f95e40cae291fd67475ed958fc9f999f9de7066d151eec03c4',\n", " '8ab4f3573364d0e996e1a55b468a11c5bd5b086f879f25efeb2e533a9352bff9',\n", " 'da99ceacd666adf6e78185ad9d3ae3b5b9136b9d54d0f95cf30c6c1d65b1f570',\n", " '20833c5480fd174ebb15f4c0de022f1ab85dea92d6a760518305ecab53325b08',\n", " 'a294c707d84b535fafafb3e4cb396da3132e6b45fa9b99768a319ae95af2ba16',\n", " 'd3e3f9ae9a8c5dea3016e25f454253d71541ad71af30fc084ccc49bdf7f6ea01',\n", " '9487522fc9eaaf5a04b6458e9098d79b67ff9bcf68255f7268a2f66ed91be855',\n", " 'c3ae1a1e4d746d5c46f854ae6ea22b4c5299d5978301590ea17ba5ca73517c4c',\n", " 'b46e33713aac149cdf2ef94afcfb9ddc0efcad2dbd2c7c14d83f352ed1cc0346',\n", " 'a52bb2924cc317a2891c6738e9ddbbaa2b0b91332814cdeea267c3b8b456c960',\n", " '5492fdb8c80431f4e7268de2892e1223551082e8395f8ff4a51b684dbc0cc3d2',\n", " '5bf2c111866209b85f6be480df606ed0b28a7cbfbc23b6ce07d41c2ded487e73',\n", " 'b54f95e7f3accad6fd504ca8b3a4723a1ff0a16ce558c738c99814e1f34184f6',\n", " '1aa82d061d9d8dd0c4d8fded0fc2c79cb05940a2d8e589ee27698086fdc80ef0',\n", " '087f6b5d60db9ac9b033ca08d6fd3e9bdf872f9cea0c6e9f0ddee3697a7ed342',\n", " 'c40a89c9a850124599088bbd81c0e4b62c11e825e85c8f6b9c454c2a5a5e2ec8',\n", " '8313c9815ab312dee9016f7547c65bdb65d7895f4a65ee7a931add0c9ba13b73',\n", " '4b2698429c48c3db1106d1218de0b94672bfacb438a3dfdf406fd005e4a8a242',\n", " '8d9e66071bcdaa2d380a2a944a461ddd4a25a79e162f720a0d7d575aac5224a6',\n", " 'ac754ac9cfa888bf122266a4550899812dcba1dcdb6f6ce08d47df5b742211b8',\n", " '74875120ab2c6ab7ddb9675514ed92491f9263351643cf2968a91708ed8cdd3b',\n", " 'f9031ca75b1d18ea073ca9e5b61b79dd23111213f7461403e9bd5df8404c0ab9',\n", " 'e755027fe8941d3665e96f6a9d9ca40d115988265643060f3f07f4af9bc867ea',\n", " 'b7a1485cde4ae028378b04030734680bca760c4fcacca377c51c9fb8f39bd046',\n", " 'c09ae5dad208723da8ad0adb5a81ca251440affd781034b6206bbc14ea28a2ea',\n", " '86addea44de184cc7739f9d4a190b402d30e09d123d8aa90da49bdc34d06a1aa',\n", " '19f0b5470b135ebac2c2c3f170f2029fe06fcd474c3b0d5c1020a9a61884a6c1',\n", " '08cee3145268c153312feac48286ca98f9ea2dd3ca894f35c19a5cd2aa366eb2',\n", " 'fa8b9f879604459d7256a076844f980a01fd56bcd8a6fc90efa13a44c168e1c8',\n", " 'f7f02403fa64820888c13da827325ae80b56811979068919e73be9ad2fce507c',\n", " 'da91d79cbf3bb15b953b28cd6e34273c6b74a353561b05c94ffb5565fce1eb45',\n", " '0ea0efdaaff9b19706de7c79299a785e4ddd01a005a521e1aabc345df0ded38b',\n", " 'ed2ca679d05763c1c3531c37437768a142a995bef15d2c411713a3f51c999f0b',\n", " '51ee2ef0e904a7f191db73de231e9d6614ae40c38c687b1ca56e4dae24f004a0',\n", " '646480bf5423f255b74a5065bfa6ae500e5d137e071b96d7dc551e05dd1acec0',\n", " '87c7155dcc7d37fb6e1b77272fe8cbaea61273186c795816825183b87f77d538',\n", " '87a93141edba525d772d41e9ae9eed0ace1cb03cd1053a2adb82d1315f5132d0',\n", " '5d1e1ca791924dba670e070dc81d9251d592d5efc39007fb338a604be0a20447',\n", " '60d9dfa51ebedeae130ad215a082dae70949bf5adf24e319d10104562fb4e800',\n", " '6755e6327e45e15bd47fd4258772c1ddb73d0fe09b30ed571d0ba06bbc79bc97',\n", " '471cc85d2c6e05efd2d2a68f574de703127dab7549f041d6294a00a9bf8a2d72',\n", " 'f802ce18ab9eda2838be002af11a1bfb727882a9a33ccfaad9ef2d41dad25233',\n", " '85a6586b799350cb0917f3e05c5605609009520856626b45b67a40ef18527e42',\n", " 'cab8f197ba5fb8d34881f9f34af82e6b95ef3f0ab5b54a67de6b1485ca7e00bc',\n", " 'f934a40d82bfe57421fee8f7a7c3d70568b14d551302e5eb18afdbea3f70a3a5',\n", " 'ff05667939c3ccae47f4aaa613ecc379130d2c2d10dc5e5da9c0cf4495d0bffe',\n", " '924f1d559552d9bedb5b1090879d2a9f2e1a7b243079491d005dc1fb358df19c',\n", " '7f66e1a5e9536de8aa2ea3eeedfc5d6c89be4e7894e86b617007b32c5a040fcb',\n", " '36a8c3a11f52478ca24b35adcd6b0358590288427b5741b85e8a7d6ce8760404',\n", " '901300334e7c3ebd627761c2d465cb27fd3ef0257faf7919c5b53e826760517c',\n", " '2404322132b998d2b5829672579469b8fe61477581acfc1b8e50e54a177e5fd9',\n", " '996a7bd7d7113e47eb99b67fcb48d1d3caaaa614bee1a7615f307a2ed12d1201',\n", " '0602fea04be416c86657f0e1732b02d8d4d4dc697ca2f8bbe49c8bd181ca9b7a',\n", " '795e7503127877d3877af2b32e90f12c9eac3c60c3cd7aecc5e7e24f9dc4af7e',\n", " '464ad4a8a17a7b896e68b70f77f9caf84daa7e40c71bf79b095e5046364ede23',\n", " '3f0f8706abcb8dae0594ff2f76c7ee1c51e84fefd121c783eca84240c9a1d27a',\n", " 'a83ce02dce43776a36c928b4e38b0d41ea08b210b3f4edeee7239d1a33bbe02c',\n", " '463977ac24efc97e9d6375f048d2d1e25bc74211b8364c632f13a1fc2e8ab96b',\n", " '98ef523406557149bf1b74e5434c38a8957cf29c56f93d99bb9a877cd30f539f',\n", " '19e78ded79ab5b6ea936c36d1ee744246b3a218ff6c489bd033c1d29cb84d8d3',\n", " '1f1800bc46f0647f4eaafc0d3606a3af202d5c181de0d1c57bc0c715458abeea',\n", " '37acdbbea4aa0144eca8a72b71c8f659779ec6d49cd1989c97b797524f7445cb',\n", " 'c411075f63453a75c32c5e2cbaf60c6af2a004ff74e076181e8de77f05a00772',\n", " '80fe4a3b9c688d02ed5055e399893587a40764d36f911b40544415e0a5a4951c',\n", " '0007795c0b526767681e4874f51a2fcb1bcd659835c2cadb37ec6e9298211762',\n", " '79e795e0120ad1334552b5e2c01d0b6f534d7f72ab4a839ec85acc97466cd851',\n", " '5f9fc3fdf01f256566c94d70964e0e36e47337d8df9730f8a64e4a110ea74aaf',\n", " 'ada2390c79401391907629d345a3b427cdc17909056c67b5eee38c4785f90d78',\n", " '7b09ce67a602e4cbe93f3ade9e77b2ff158c4da724cff12b6573866e29e61ba3',\n", " '814677da7f4d66e402943d801fc54fcb0b31c3cf86d537de0159aa356526be86',\n", " '5c3625e9547fc0c4fcf3d09ac896b598e1c0a82a18c9807e35757e5c3e80decc',\n", " 'f04175304e6daabba061d98be0d723e3348c4270f1e489783e7773b0909a53e6',\n", " '700d9c2cd3186e08ba6deeea8a315c247919350c07dd5283380bf80c049d7b55',\n", " '597e02efedbfde01cd8ff711337c6f5d8341f628dc92c022dc29e28cca2d79f0',\n", " '593f0c8dbff4f0283e672744b2ae0ac2bc80c867616267db5a959a1ad81de944',\n", " '65c216eb56758f0f47dc083b5635d54ac8b565122bf23d5225f6761d967fefc6',\n", " '28683eeb688528a747638461b494736b87fe59bf888dc87e62b5be507511bf58',\n", " '10195d9294195aad9376d385f69b57eba9f2730a563146338e2f464c20f0a0cc',\n", " 'f68e2a99d7d6b63c2e78aa3b5356d1ff2afb7e80ccf0e13518ff100eaf07231f',\n", " '439f596942a1644083a1a47f4d866d0faa7f00c0a6478baae167148cd19518b8',\n", " 'df0014dcb630780b85a8984d9e27e3cb335f3f7751ed8f3b5c7cb1b782eb3220',\n", " 'e6a355acf224005a2096ce85c422e3e08b89d3ef833fc4e301fd37e7e09146c1',\n", " '0fcde9c3d2d8483acdb771e4cbbadea37539325509ce5eecb0881c2c7c563e0c',\n", " 'fd4772a2f95807173d784862ca11026d888ead3f1096ade28251435bd7fb27b5',\n", " 'f28ec343a3f92a775ec33912cf15c198b23f3de6c0478ac04f202935cd398fbf',\n", " '4fb39c3d13492787583dd777b6603b0e85c41816f126fde0296a766a95a803e7',\n", " '91149bd9beac686230a9a00c56ae20c648ba5ac8ce2f41f6779f4ec2e858854e',\n", " '31f34d2bff726113de155499850041f64c0b19514ef1d0212f4dd441a94904b6',\n", " 'dacfdb3febf52bce62e493839c22ea8fa2a258519e6b707ac582490d0719dfd3',\n", " 'c91c7e3155b2b40cbc3874b9aec1a08bed382fbb992e75cba749528419fa7065',\n", " 'b75ae2c411fb0a02821fabe1190d1c64cda24a848f4a9c39ab55090a97ebf10e',\n", " 'bea5f6a6490ec01812f182496ed6e438ab3e5c8cd69e9acd4ffb00570c2c3e95',\n", " 'c6dc00681ffa07303ce2cbc4b829641a85f3599195018bdcdb3a785f04cf2904',\n", " 'c7af0a965093dbd263c3f760c610cc5414cb02f8fd3eacf408bc6ed6b4f74893',\n", " '06dac14195405fe27ccde3a04fe8014dd12f4dcc9f391c8174dabf0cc36b5d1d',\n", " '27124d14074574e5975af0e35051d2fdcfc9b1ca0f25951eac623d2a8e3650e5',\n", " 'c27162135830fbbef6a5f0023c71559853509196ba1f8e72985b794624d20647',\n", " '4b03b4f6870ca3783ccb77b3d92df9ef0fdf7d47993f8a0b750edbefef3393bb',\n", " 'd5df1afa67276c79c314d9bba31f690ae080c9319d620b26a70e5acd3a1c4ee8',\n", " '2645a53529fe91615260e4218a82ede5ea53d7e0270e0f83227c2866b517597a',\n", " 'c23c71b6fdc6c4aab2c9d36be8137427791961bff4015451b214cbffce72d89f',\n", " 'd185ba33f0cc8be8e54964bc3d109710d849afd445edc048cea41376db5cc8aa',\n", " 'e74209149cb4789d30336d90f6d7be41219999c000ed5defae539f2a04f3ef29',\n", " '08efbb560f8db4481e1f8fefaaf5ec558f922258e07ef4f97eba07bc17ae085c',\n", " '82ff268a51ff11973e5b39e7455729854f0fea197d9972309dbf275cfb936f76',\n", " '6a902cce4931d68aa068789e26334496ac2addcc812b54b6bf3037c246a28025',\n", " '25eb1f373fcb010a5de7b4d95d130228768873eac35c9536ecc631b95ebc8472',\n", " '45be90df0ce8227b7e036b569c42a446b6b7eb9804572ccf3b90f55ecf137ef9',\n", " 'a460ff73738467951858d0394dcfaafe890c9470b1521bd0eb925dd3de86175c',\n", " 'b14cde7798fbb50af773508c5299e49b72b4d45fdad36636e1fa3b6ba83786e0',\n", " 'fc856aafc0c131aeb1eeaf564018d0797bddac81d1c516a2943581c4374730c3',\n", " '1727d33ba63b6e84de48bee85233b2a61d4bd3ff4c7b4ffba163f5146f6e1701',\n", " 'e6f7d0973fcada02d17f27ca4fb4cf174a9ac9c8bbfc67b54c099e1f1ceeff49',\n", " 'd0f57f23308299f3156a11b7f6b612278fc5c71a31cfd111890396c11a18d5c4',\n", " '1a00b1f70bfdcd9b5374fde60384d7e701fbc909bb2a642548c1fc72a0700c9e',\n", " 'cb8427bb50c77e44b33247b119c8c5305dbf725a98e89bad5486f663b74f756d',\n", " 'fbb3c8063ce8ca0b78a14449b5f4b98308150fabd2ed77df7b9707906231da3b',\n", " '3c600944553096102629b181859aadc6274f26d2822e74fd5556a320a679ae18',\n", " '0623bd0bc50bca12c603ae2804b4497c02ad7067dd10dbfc1f89857e48a9026f',\n", " '4aaa1392d9f11c2e487546fe608975d0f6b74096efeb2339123b098daa2cba91',\n", " '4ce3b6a96c5a367911094353d0195da26ed437ff5260379970afb7ece5eef6e9',\n", " '717315e9b1ec6e4b196dc88467d79b1c6ba42438e68e0b86c108cb96afbbd853',\n", " '544ec68fb66a63aa522fc62670e6d751c810c06337b5d701e9c5bc637cacdf64',\n", " '970171907b7f06cc274fadbe26160c31c3172f0f9c603678f210a0451a8fd276',\n", " '533df3e4d609417024ba8858034d72e0dada7a126d7ab41d78521e1fe3d3bacc',\n", " '94593f633da7d46c7579ae329b91d0b6bbdb0fb3d691de592ac89f0dd55d005f',\n", " '93dec3a45371e861c81e488fd530a90e6a023aba3557752019121de005f1221b',\n", " 'ca664ec683f1547a68444af29f6d4b1808137588ddfd49a5271659c02b0d06ff',\n", " 'c7669c10425b52ba6d0f981af34f30184d81c2f43632bbe58351cc4898891cda',\n", " 'cfb7ed8d1b130992507b36ac374879c051d857dea8434fe6f0c4e0f3177c84c3',\n", " '01c1dd6620fc2bfbcc05f47fb689e5df505cc7473c2a1b70988defd6f33e2f53',\n", " '505b3e24f8ee0245c859efcf34bde622964dee2491e39f9163cb9deeef405b9e',\n", " 'aa575a90011c64a842f272bb19ffce396d34521dd7a5aa58d1657f8eb855152f',\n", " '0795d2153b6e66e8c4a82418ff260841d14096efd410be3f0f253974ed04baa5',\n", " 'c245497979b56452a8cfb3d61e2825a0a7bcc3c03f35cbedbc85c198fd6a7e1a',\n", " 'dcb7c71ecee94d82e2dbc78784e54fffa81c766bd20fd09df6a235a0eef832b2',\n", " '97b3fd085dea36bad946dd0bbaf06d105b9b9fcaed29ba70bdfb9c0d6ad6260e',\n", " 'ae1a58a1094c08f2a65296143d975f3fabcd7acf44e91e9f8d4982d2401ced22',\n", " 'd2f4a17d894964196027e71edf61382ed8f7732fdffe6112a0b1969655b8aae5',\n", " 'fc6ae52449e1bee85474cce7c098ddab2a64fded30461ead92c0111450bd871c',\n", " '320592d0b142996807c919a253f2d5ed57a1921233bd632ca36b3183af701c54',\n", " '130208917860804d889e66adceb4b72b0d7ac40b7c01990be7bcf58dfdadd167',\n", " 'fe4c53ee6ec311f5f9363af31eed092c3b180532acd52c91f13446adff014f79',\n", " '182815fcc485d72306daebcca05c9a0024194c21c5ef2554768e435940685557',\n", " 'cc9164fd1213cc759890ac30bbf8e233dd68282c1b276e4477070b867a0c901d',\n", " '1c1deafdc3c3c06fd69f0bdde14ada4341664f6464e7fa341f98f2675b6fcc25',\n", " 'b46b4ee08fbdc10ad33b6980ab7f890f5868aae37dff89ba4f067e380a849393',\n", " 'a86ff30daea6a932eebf37ddbc3e533c7f21a4ce0d6d1740582817727f422dca',\n", " '72f09bb98148d1855324a32fe2325aa0a882a8a6785d3c0ce1b010ff5222507d',\n", " 'dfd05a9f0225acfa10b9269738a4f5f1d0e7efe40c359bf6a73ff278e161937f',\n", " 'c902870d8556bb5a416a4946953ece651ed0d2e3ae3c6036fb811a12af33d2d7',\n", " 'e61f8218b7558570953a910653439b129970128b1c0667fad484137e5e310db2',\n", " 'ef6f8a221bf760a10dafea0e22e26aa55e1a86d324b07971953d5fea4a64c577',\n", " '5569c6a95c41a2dcfe8b0bd585985dcc905eff1d490b0dd6e07629bb042c6214',\n", " 'dc01c0a6a01dacc655f02e90609f02ec31026a7f733c970f39b3d6663d40c7e8',\n", " '5f22ca512784bbe32951a1951ae0902946137590278f952ab3e941d0d37f20a7',\n", " '50dcc11ea20272857fc9dbb7f9f22a7c56301f8cbf6da611aa093863bcd318eb',\n", " 'a4dae79a825465cb098e1ae45a517c0e45906a5aea57faf7a057ca7edc7b36fb',\n", " 'c337ddaa9158fefb101a67c174fb6c0da2c8881be567bbb7dd5d53edb514af31',\n", " 'd6d11c0d4ead6c40bd0a071c87b4c7bbe249c66a8ec97563b3ab2c88a74bc382',\n", " '2d09079355dcfeca4e8cb5d0cad1d98e5b0c8df666c584526554527b6d72e175',\n", " '13bfc2836174dcb394d29397717d2eda8c7419f02adbe40bc75c0413d492e3d5',\n", " 'a34d8c1e61f0bc14ce8e0f21bc5ba70255d18168774aa479ef18d0baa24c537b',\n", " '86955b310df1ae8985b68726f6a55902fdbf71dc26ec13a55dc5afe05c301cf0',\n", " '25601e33828f9bddf76aa97a88d8e1ef3503fb707035e7281a3f060b38d1567e',\n", " '688a0e2219adab255aae4c8386de2f6074d36a6d04388eb0019124283a447d24',\n", " 'd1722e5e7af41ee996dc6b24aa0279394ea2bd733c9863eefd16095758fc4213',\n", " '01d7f9d5cf0eaecb490813459c4aec75c953001774a297243e4a0b924ee47259',\n", " '20ff94ec76c9fd843ae1675ca965ad5d380564f698de68cd0a8e7cb67215116c',\n", " '59ee3437111218fba26a69d6359d47aa1228dd87a79294bd30d3ad2b2baefccf',\n", " 'e6b841bf975645281026f5da389fccdf87a5f0126cbcd4bac9caefc56696531f',\n", " '7a67b771ac604e8b56e9f23318f5254520e1e8b7a3b90a071f3f444e846f3b39',\n", " '1979686581713fbf9613acf27c99e71703809346dd3082d21364a462cbcaa2a4',\n", " '5f433054cfdf05e7288beedbe2bfd37c3e4272c6b6c5628e3bc1b31bd538b128',\n", " '21a83f6025dc6e48bcedbea2fa46950df4c6ce3c5f89553483523cce65214582',\n", " 'e44f9eefc1ce2f8d5da1741be50e99942abe2e9c640ce14f2db16e7a58de8f4c',\n", " '98f5fbe014627da3220d436b26530d3eab33919575d2c74e5c84b05cdb77f913',\n", " 'a860fd1314751219ef9c1a8f4a49c394f10e362e2f65f7087fce13bc6ee1b4ab',\n", " '1b7967e821d82b17dd15170a82d8c21b4b6f7504f5f2214b80130999dd2055bf',\n", " '8c8f430230b86709e55ebaa6046196056281c4723f9184d01cc701eae6e267c2',\n", " 'a3c7300a4ff85055c93bf1841cef20cc9dbe51ae9f4f0895930c884e15688027',\n", " 'f34b4f5b1280df827827e95943a58372f12a89ac6e3a39d1596d55cf7782a47a',\n", " '76a3b3ec71095e21de46851825ec1ceb899eaecaf0c674ab19dea92cd063da2b',\n", " 'f59f17b5909e267c9e14910041ab4ab4456433f199de766bdd7ed9db7886c1f2',\n", " 'aa418887fbbfee4eff301e3ab364fdb5927a78f10120a3fe7a70ed1da9ba9792',\n", " '39cfec7cde6c5cc16efa2d0a6b7dfbd8c02bfe1b9ac463ee9bfd9ff3b75c6fc1',\n", " '52c43a2de9e72c7faaae49e084f924fadcf64b185c63d3896ab70f686a28cb9c',\n", " '7ce7a5a0b87caabf19fb1c3ba82c6c56ebb26be7e8f279516308bb47c22b7728',\n", " 'b1c0c1b4cc1fffe201abfd88caec23455c73c5eaec6b309837d5dd4a2f00f2fe',\n", " 'ebef170c9cc9a28781e37e5bc2c4df376bf5b254af9d83dedc5b04ee858986ff',\n", " 'd8d8837a92db83a48f3f83705b75079ab3dc237f88219d4001442e9c5b33edda',\n", " 'e73388fafd0bb87a4a78694a132dd86d4e957781e47b6ab96bbe8a230132569e',\n", " '23246ec7d76ef2188e8ead533fb03c62ee0cc1377645541dabc98439778aaf9e',\n", " 'e270b4ed928a6c8a3bdf8cc21c4d2333da61eb112bb9b467af1ef9433df10424',\n", " 'c862d339cceb2fec667dc5250e45182c96324be1e6399c93f2ea4c26e036acfe',\n", " '4b8d1433ee1a4777cf2348a9ce3220d02db47646b7ce55dbea1fe9f69bc08945',\n", " '7e9807392fd6ae647b55e0e689651778aa88f133220e0903d3f5fec2bd8ab055',\n", " 'c5d5877edf7530b8d9e9e991ae54bec191a1bb75e9dd8987c379714018c12b33',\n", " '051307b4c198d943f60efaa968760f525c58babf5c7ce0046bc126808184c002',\n", " '990c15baef5c92ee2e3264daa05873ee0b538e65280083c1b65eb4f6cd18bdb7',\n", " '7eec7575efa0497e7be9e39fe76f94697dff8b8e992d5eed468f75f81cd9061d',\n", " 'b74d08b3a9c9c3dbac518b81b8f9e094c141d7644b1c873e7055fe42f61342c0',\n", " 'ed8c5bac8ca0bab417e70dc5fd811bf46fb9b1fd0b94510f025532d3a0991f54',\n", " '1e5aa3f06cf9a452ef9aa7877a28af9c3432b52dc7f98c33ab58dc6be6f5141f',\n", " '24311c850eee0d1b1c5c1b1cb9a0b8fbf9b77438cf9da3461fe505641eac217c',\n", " 'e987d0774f44712da768bed8f83aefe19d79791a37242f656713e8cdb03b0b9f',\n", " '2db24a786a66aea72cbaa15c6eb4fd09077c1a4a62f43f6e6b51eda2ccdb3b9c',\n", " 'de199eb3733d71b52c452885329c3f16f0933d5f9cad073a13ded0100e350563',\n", " 'b557687e93a9a491722250ba9e83b9a9aad9866b71f32e680cd744fcfb6ad49f',\n", " 'ee8063f292b50cc41bcd0125f1949d62e81b52509cb19c36716e74b22c6c736c',\n", " '679c9eefc06423132f019e4858b4524271f423deb4cb1f10a197295bdb90547e',\n", " '1d1ce5c0406c0bf61bc2a60e1524b471d59a8130a4ff116a3801d5026913a2f4',\n", " 'b22f68a88a1fe5a02f152d494ec351cdb15f66bea2a5f73f2d12a229a2b22fbd',\n", " '0ba648416ab414449b66cb377c2b2b76a7435e5e4ebe83ff6f319b40a78dded9',\n", " 'e4d3d5a769dc799ec1697472fc5f0a0548d0941b47ea09ff830e4ec6cde4e972',\n", " '45993afce2daa57be72ef676f00add4e4a44b5e1c684673bdea29debf5d90611',\n", " '11ad2a8d2ba37f29d5ffc8baba7773cbecbe31d5f62eba7f5ff1021b49f67489',\n", " 'a215d10cc7d7a66d320df483586b588718ddb25e4c4c0906d528b438b6257bd1',\n", " '09a56d96aef4125116b22fe8a2685baabb793d331e350b0a429a837a9d2751a2',\n", " 'eec1dea45a608d083989046938e7314e568a0edb71002f12243f5c9ef601eed5',\n", " 'cb97a4b80a6eba6a1da1bc907d5a685c882ec2d44ea7d16341fec9b18da48378',\n", " '54c02634f0c62a8ab12d0ee782cc7cd7bebbd79210ad09e226d04b953445ebbe',\n", " 'a1c8b5ac395336a906f5b97cb52a4c9629d3a8f098590ed79381fd67783ec189',\n", " '3b22eb789299f7e1f7bcd3a3e3de95289eea33cce8a7d20bb5fe1f3006bf3036',\n", " '093b998997073ccfaed5a6997c20e9784cc806bb9bc24502bf3dcd56ddddbdcc',\n", " 'b572fa3a37ef5171f821e4e9af3a399b5423e5a5049d0ae7240cec1920ac8b60',\n", " 'f84e01d3816a121c99e60f16c2dcd69a0e94f8beabcbec157f55043de20111b9',\n", " '1d7397b375c65130efdfe97316e70f378f18de276d78dc93c661e291ecf6c2a5',\n", " '1cb0a50c74d70030e229a54e8786ab976fc03541d269ffc99407a6ff2402f031',\n", " '4f72a458f2f16453aec9c27838d133050064b0178216ba0aebde23118de3c17b',\n", " 'f808f5888f512b025c237bc239d5d6f526e814d3670eb84012f163df3b4b7dc7',\n", " 'd53c50ca3000cc8b1716e58543efffe59ebfc451be232296f7a318953491be1d',\n", " '01fe0aea09cf4822f4402918b5e4f67b9205c2d294f13becb77a5a9f4fd63876',\n", " '53273be18ddbf906c5a05fb1565b4044ea4cbddd4fcc333b4aa3f970eb6d3260',\n", " '6722068927774ba582f400b1f8e359001791e184581a4897d137b8c2a9da80c7',\n", " '0fbf52f2cddf2f33ecec6230c45adad8e00a5f80b619fe431f2747855f4e2664',\n", " 'c3f01322822b245b048d3a803cd7601e6a4ad23c729824becd2acf9181d03dfa',\n", " '1b4ce84c835592af810d7a1214b8a9893dade1e75243efb0a0149bd9e33a2978',\n", " '5ba8f697d24d4d83e22c822c5911047ac3d507c69bf5434de8f9ecd12cff53d2',\n", " 'cc10263f9991ba2d3f28c94cfd171be144dcc513283b7987266f55a831fd2bd7',\n", " 'b020b776c99518fe5527da1bea165b00f632871f78acf564fc0fbdecea83c5ac',\n", " '848fe3aa902ac4d2dcb027e27f1fa1b3ed2591e78e3614e700472ac7c178d104',\n", " 'a1c3aa54b4163188ee55f406dc7509e873330aeebb57ba67a3cef9dccfd440b1',\n", " 'a88a42960d6074e315a20328f01ac28944c737a4014699669370a0d78ec55746',\n", " '83551eea36ba85d3cae090e4ce96c99aaeb46a76935f16a04d75d665e5e672b4',\n", " '3deb2cd6b178fd4cd578e05aa037e5d78f3f949e256e09f8185bc06c433a6665',\n", " 'd50bd1fe8086e6070f2c19486142db063a46187a7e319b3319fef4a18801107d',\n", " 'b12c6fc439fd1e126fd7bf024d0bd1ae858e48b00514654b3ed95c48782e4e4f',\n", " '60f1433e7da262d2ce92b8256707a67f9297ceaf46f5c2f6f514e54a1f6069a5',\n", " 'cb245ce95c24fad3110e50666f976d1b1d2f1371e073026ecce17e0ad17039c7',\n", " '8b4689ac89101e768e2f094baf81203041ac7bf18f36ae323a95c85ce144e907',\n", " '386a3a9242ff71165317900acd321142f06ff6fd72d3feb755b0b0f109266a06',\n", " 'b42eba2cda3b294b14c407e229e1e9afef102143fdcbb4e57a2b7c8bf7c9cf96',\n", " 'fa9538a4bf7c6e9def244a9692378a5c31c7d64c09ed5b8851bb81628d6dc5df',\n", " '17442e49274aed9e4e8b9f922b782b0986db96c31db438e948528e7e7ed968f3',\n", " 'e40587627024aa901a8b73a70f9f862bf69ef3ae75707533772919de9cba597c',\n", " '0ac0fa7a9be2c5679745d13d3c503aeb9b1396b29e3ac91f6a1c16993ae46bb9',\n", " '46a5f09a85e4a6d8febb091a340abf61702f45167fc1756bb5df45212c47d8b5',\n", " '2da71fadc678c3934fba4d92470afb45fbe16773cd410a825ce64bfbb5a975c6',\n", " '5307ddbdabbe1188f652d15488f61179f99ea184234aa86eb01e20409d0e1db5',\n", " 'a6894cb0479601528f180ef52ae36d504df8d684ec919e5a7a182ff5d44eb693',\n", " '50f1d3e7d649f66b8974661c391c72f8a62ee2c6f4829d196fa6db5afef05a0e',\n", " '9f159cfbe12f96b93518d7c0bc99fe660ff03955a241bc70f6032d5d861c43ef',\n", " 'e70e7f56d2dc213487fd50e4cd442345d3c1fecd5d3710b9f9e037adfdfe8c1d',\n", " '1a142f22f36e4eee735a193f170259174c1ba4c51eedf181a3d62f7777ba6ee5',\n", " '25fc74eb946fac35a58f825533171169d9a1097e697b916326b85d15c5f1d474',\n", " '1efe78b030ed8707b03f890619661231aab582e867cd54e560f07ea78434c3ed',\n", " 'bd1f50229422001e59419fdf2450837b860085203062a58b3b50a4d803c278ec',\n", " '67f451c5de250397fbc1d64033c7d0bb9eb54998f04b85c452ea4699b90a6fc2',\n", " '41011e9aa62a7a7313b9dbfc8fc89f3421b164aed68b0eecb61d0885443a7218',\n", " '0ddb7289d5b62594729a5c4ad5a4c499cf0b61981c3b359d68be3aed86e15158',\n", " '52b106bc7720773e66f3383c42e6235a942f16bdf0bec6f379838d3dc341d1f5',\n", " '3bea243fb82de0dd37feb71515e326fce7333edd8bbb2fff2ac83a9b899fbffd',\n", " 'faf8580b4c7e1336dafeac5acdad4211c5addb509736f1d669c70b4d30c0e2d4',\n", " 'd0b02666c74775fd0d2bf179c0072f739a0816b7bf6659d83931973f592d7c77',\n", " '0794f775dc5b47cc70f52091183fa6c290f81946e83f855b14e01983d8e91a11',\n", " '7e5399a31badb99b9759a360d03c3ca84a4625ecf5fb2f521948bc52730d7ce4',\n", " '204173b5245ba44b4b5adf1aba84ad974db1116c16ed673fdc56a51e39bc530c',\n", " '868afe56c08296f180301cd7328f5b8700faa4c9fdcba134550c82803526e47c',\n", " '920053fe6b48013b3fd58708e851ad09e86866e0c5acbabb7fef35d8549463d3',\n", " 'd14f8bd390c29f0f045549cf4b2ee3d2ff6a33641a27419fe6d6d6314c37c671',\n", " 'fd2094355337ee47e4111289d2a4fd694f5047a1b60067ed7fe7bfde18f44061',\n", " 'cb751390b88611899ee387fcd1e74bc6d32e99063d623524eef174a3949b3d70',\n", " 'df82d33430290b1150a930fd414ab8cab1570c2654a32d4135a5f150b5af88df',\n", " 'e58d74bc47f12ca2ed61168e9ca65249697c03953dfdde01d3d52f084f66cdee',\n", " '71f21fc43fce9f87e963bc8c2702f65c4b3cef404d7dea3730a4661c6143cdc0',\n", " 'aaf5e6c509dbfdda56f0403760e3735fcc063b5feab97d6e618858605fc6bd2b',\n", " '911ba0682f4ab34715ac6d9d5311d7494b326b352714470eb990f02c9f40dee4',\n", " '243875085da6dd55ca2e79cc55c75614e828cf3d83b118ffc770ff61e1d26340',\n", " '3c845d80ee504a6a7e5805697cd4e6cf0bcfdc3230bbf58e05a57007365962dc',\n", " 'ceeee3c9dccf29f60a22f567ed6db12e789c72a7a1bb46ed45987606550fcf41',\n", " 'a4f6b9b541a97015119d86fd4f85793e7e86a2f8b4433f64bf052aed1983793e',\n", " '9a2c65d9e5b66639a6e1cd93e3c9c1d2b593ca5f73c7be07b06eb40bd4eadcea',\n", " 'd510b7394d6aba79ba72a98c5697642dd0e66f32f50f1424e3bc26877e65385b',\n", " '6d56b669d64e82f7ab3cfce22b91aa817d1bccd9b47c0e039a92a03feeccfba1',\n", " '3fa1aa98d5b43442996a14a447a286f6500efa921c30e389e76ea8d4352d4b7c',\n", " 'a57834893888713fb16621d725d88746a5fb9524d650d0df92389af494e912ac',\n", " '8fbe7c8c08c722a09910ab4e8a0d3971735da76320d454d55fc0441925d0fce4',\n", " '1ad84da0f3d62c0afcfe6030e29def26000f8cd895c184d7e7d78f9131420203',\n", " '21bb347323586d1418837d8c92409fa9c27df0a449a6a5028ed0ad8765013b31',\n", " '7d74e6034bef36d9b6709cb40095147011cba71ed3dba4df46e11082731746b7',\n", " 'fe620359536af0e2bc880707cfc24bb6ef77fcf3bd68d9ea9f12691c9b9b3e8c',\n", " '5b90d1201e0fbdb8378aba7f19d729cbfb2b3c9d637705caafdb89688f52606c',\n", " '8c9a09b65fce063b69cb0f10e385c3dd4e3b767923edeb99f2526fa241934c68',\n", " '58d1ffa82b030f3ab974f9dac329293561022fc222f6a4cb9f5a32d99054473a',\n", " 'a0736fe961fd908bfbc748c9b7cf70780e7831c4ae983296de9c34a786119d7e',\n", " 'a10ac1f9b3343f8e7900a7df1be4b415cac68a6f386681c99e39fcb99447c216',\n", " '1a363d47ee7539dcb24a23c6de87ba3abbcedc0f271108347355e0ed1998f5fe',\n", " '3eb88d211ede058e434f57359e69a7aa9bf98e3e6f739d66c8dd6418c3b1fcf9',\n", " '5d76b0daf41bf6532b8440d924671a73f210e50865edc3d83156c66211e0efe3',\n", " '177b04674e101e8af78756cbbb70bd12a9f179fdf32f938b6931c026d9227772',\n", " 'fb33aaff2fbaca9e8a4697eab6953c187bb44c9fcac73d5bf1cd210acc365427',\n", " '009a20341e72aca710585e61264f347036cec2977828c336c614d24eb8cc7787',\n", " '58b683666f98c9333896fd0a7b27cf4a748915cc03b84552764e189fd0b63339',\n", " 'ad0b030ea0fe63c572817702c76003decf16d796a31b560e6905e2ecbd852f61',\n", " '8a59d86cbf111db286ba3e98791487af56bfd18f546fd9a0ba0ca68ead12dd20',\n", " '3f433c2fff23a905052ff45c4004e7dc4d5a6a2b52993638f8c2f2dfc8495bba',\n", " '443ccdd314785b0799ffa241f532571cb6c86f1ef5cfb4acba3276a9b3aaf9b8',\n", " 'ae44f2709a8d6aaa39149721c26f6298d1c52e4cb2a6bca21518f7431b3a4178',\n", " '54a41dcb4504ee6d2b9d5f83c0920b145f5d5b2158c5b7c0d394fb61690d80bc',\n", " '53a8a62c0ea8fb02d6972dd212715b09f4ef064b03b3884e5f76de460463ffd2',\n", " 'e7efad56227e150d23343a689139ae65dcfa6705556b696af8a3c302dfc55782',\n", " '5502d45ad42f564546d318e2804059a1ddc953c7fc7a5771ae568f8e8bf1ac75',\n", " 'c90deb7eab8ef59fdb7dd282eee8c0a7c38840de1e858993c55d18d0cf2229b5',\n", " '825476f0cd2ea8fbaaa3ea8332c34482a482dbf8a178c729fda412e3b2c24d8a',\n", " 'e655d0fb310789e0bece9405566f932741fe8f18376bb1192d894d9645ccb9ae',\n", " '1ec2ad63e22a2632e55fc3baebe307c5dea3fe18d19094cf767e361c62633ce1',\n", " 'e98e99cf94cfcc1252d793af465273c6467f8f6c8a976160a7fd1cb9c52c4150',\n", " '5efafc8f4126dc1a3fb2eb38c46d13a844ad11866c5a83f6a1925c09be2c59e1',\n", " '8575b6cbb087bf0f0a98eb6e545b26341baecebb86522a64a8d57cae3c16dd5f',\n", " '7bcc13c867946b7ab96130d26ffd20ca06fd5b41c0256ccdd961529def26ec4c',\n", " '746880c019cfccf3d02c115df8feb7949a9bf9c7ec905ef21655df25c53505f8',\n", " 'ef00fd0eee8f39a9eb333ffab9dd17460f05c57f7b9ab9c27c337f3ae50fcfce',\n", " 'd773c7e829ea8156ee7c99844ba9fedcd529657a30b3983a7cede51ee21b14d4',\n", " 'f724b3fe2fea1717c290607ecbe953b8eba0df0ddff6d1e5e85308917fc5558c',\n", " 'bc64bd57502b0832ffb281fd605607df6cf6b2b3fa11ff8be7d228adeb6db0d2',\n", " '1ff17356015e0ed4cc5c41486b83e2c03b561ec08d63463f4d06b2054adf2eed',\n", " '73ccd10652f00ee249eda19b7295862ec82d9a2b96fa4be211d1e9201f142bbe',\n", " 'c9aa9fc8c59a7113cc6ac1f5c906585c1b80c20cd91094175bd75242a5561225',\n", " '340a0aab256c92a83fb0b44eacfdacc6c0bcc209806daadc9ae1a44e030df391',\n", " '1348e56e85ba2b780789f2a163114d6fd5f0b159b2f4c5fb3d641c883478cbc3',\n", " '16123a202c1694e936f27a2336a4b9fab3e82a6e3f50cecb7a4123581a559cb5',\n", " 'd50f96acdc509156da89e74fddb74cbfa7ead4b088e5ce42de99bcd60aa3a181',\n", " '955afab505cee6eb63e9a9f9cbc8745be7172807ec0c476aa6b6c24d005e706a',\n", " 'e76469df305775dd764846df3648670118cd8341f3a6291a6601e3bcb31db1ba',\n", " 'd8969a46d63c472af92d0ac817cd41d404e00461c50a209955cef1d9bdbce3ca',\n", " 'daf1096a3dcd17243131f031431c141f8bbb301de1d3c8b94cce47c4a6f47a22',\n", " '733255759bc7056e4e614b1bf2be9a3a78b5b1385c34282677819640fb6701df',\n", " 'f5febf5b16143f275a0f9db68f509872e6affcce1536c4afb8c4513992a9e7ce',\n", " '20c5a4852c80d69b7c4a326370f4c7c721bd59dce483dc5609cf139a0925c049',\n", " '18e549f651eb9d969e81e97beb20df3bf14cddd93d8b62d8c9248cacf8629e2e',\n", " 'ef6ced85665166de0cb809bcbfce1e9254ee5d598c9e5bf669ff29cff5e2e310',\n", " '2e0a3e270d8190b8b93fb947d322ce15b31454c5c9869da0e1b8acc9a017f261',\n", " '6a6f1e7b89d7f49a0996952962a8f7245b78a29d514e9bf017b58c8beae03ef6',\n", " '3de499ad2d6cb5c55e2467e9750afe5977ccbc84e9563cdbc733757a84bbf424',\n", " 'f39a893cf5ff982d5e62f6642a8ba877f1d7ccd9360c379cb65f2117cf297cc9',\n", " 'd68ea3899dcbd8a26ee7c54c6d9ad3f8bc54b7b21aac97ad637c2ef65d449b2c',\n", " 'b717efe76fa0719522ed18102d720e909645e4ffe688339cf2efc1abe4e4a210',\n", " 'c77a9bc92bc06c69784e66e6fb6b7944d7240d1b6d25bd11a60d5cec019a46c7',\n", " '49fa272be27c78004f948973a9c1859397662a8cd2953420de1342860cc23e60',\n", " '30aa612c1e199fea5c096a0ded073d15fa48889d17de0c08166d94a8c7661533',\n", " '9a2bd5b627bf9021e5737fec53fdf2b0564614c89b43447fbaa0c7d7821ed161',\n", " 'a7e70ce17dc422e18564dcafda9b4ea9febdf25c755f277c3029e9af29fab9ce',\n", " 'f336154d74382772d6ffdf67da967b5de3b9470c178be8544eb6278356728f3f',\n", " 'd8c21b7454369ea2d83b5ebcb353610675f97edac6b12e159ebcd4f1348863e2',\n", " '199cc1bdf68345e76a6a332dad2a908c6fa574323578d0d6c4421785a7e3a4b3',\n", " '05f0084240b5799fd101404dea0fda38d2d79d360e465087097e61d90e68b8cc',\n", " 'e5caac18cd206c26894828565b1b352c70a7d059b625f9362e14e18c7f807412',\n", " '85893e6f7ce2f7deca1958148830ababf1701d2782bb49137ad0d428a7f36344',\n", " '28ddc556903a842759221a933cece9e6fdfc9d96001586f2c339af0486c7b8a3',\n", " 'ffe27dd48dbd79abba926df93134e718a5dea64f02633f78a461c05efdb9a275',\n", " '3edb4d7b2f96f3cc171a1d2809a7dc84872b777d90bb1eae366e093fee1ed52d',\n", " 'c26e53a598c864b0a0797e198d2f739b59f5cb88621fbc3cda2d3d682936034e',\n", " 'a0fd636373581277d6ce160cc38b90714e5056a5dbf4a7441ff4ef9e6cfd9a15',\n", " '73caee6e878050814b5c7b00b34a03cd8e74ad309c7f820c81df8dd9f3679954',\n", " '9be18f033806b46b42f12ff21b9a09a3050cce0e01d5cd5d48a8fc406a667220',\n", " '23fa2179594b54452ec0e4aeae5450f94f7fe3ff01c7b06de0f3f7f393f43600',\n", " '9df7bcedf2595b47ad1f48c8c8b21c1fec3b9408dbe5b7e9990d8e4678d8c63b',\n", " '22f394a339f64230f4a3a706967064f42de2cb2b9a2b3b1059b5296fede80b9f',\n", " '03c944310bbd012c220e1c2b05cf879cfde6a3d7dbbbdd11ebd9396df4a699d5',\n", " '3cf8fe88e1d524b1ee632e281162c5f12040731754fc4d7bcb4cf849384d4d4a',\n", " '51f8f14198500f56a63c30bdb9731b8e5d3aecbd2c62bff5fecfb1e7f30521b5',\n", " '18302c8624a6af955411b8dc773c6e4f8ae46cb99257981305b4d0d62f64eec3',\n", " '8c7b71cfb5a278437c4dfbfd774b157930763bca19f23b8b984181f90ff2b062',\n", " '12b7539fc5ff9253b699e001726f2697df2ea14cd83803f32ffcefd52c49bb50',\n", " 'a417fa4f00076d6775ae4cd470a5f56a4661c2c4186bce65249b52e205368f5a',\n", " 'f816f7f81bb2685fbd471a584d3b070e3dd36e239c84907bd6ef7b0d9e2be6fc',\n", " 'e1fdd3deed0d8659b8ad8ef1165e35d24b207d2d59985ed53c5fc74d819024a3',\n", " 'ec90384488a626403a6942f4161b0f15f02d79f4e7917cb749e071338fc73eaf',\n", " '6562b2f07f12e2b6bd35ae5fea5379213060fdb8bc7475a1ac0cc26ebc6e7033',\n", " '73d2df8c7ec1ab19c168d0e320882cc7da69ebdda02aa2e17229fdb603d99b87',\n", " '56c3da6e5868d6a8731bb878e6adbd0cc6911279ff877238f0198b50e3bacaba',\n", " '779859424d2cbfe6810a8b0fe88d267a0724c7d8b912c138655cf5b30dd475c5',\n", " '26f1ac01b9ee069b0fc4c7a354abbb78adbbaef7adb41279a62cc4697d8c8a1f',\n", " 'b50174826e93eb9d8752a2165a827f8f35263f876ccd785de1bcfd8c41202964',\n", " '05bec47187e144772b34b1e5c218b231312427f129b10d8a96839fe2d65398bb',\n", " '0a64b3abb0b3318f2c1c9f64c57de4e48248410487d05873a2ca1b3213545da4',\n", " 'b851b73d7e374f770872a6a6a40ad8368c9ef2b038b64006218e6ad5da6a2925',\n", " 'fa64bb3b94ba3fd35e1b3e3baa135fb5a1f188de5dabf7554a833d0b38542f8c',\n", " '794722be584699dcf3e016f2dc14c426d8be7fa2604e1afc62a4ad49d636a338',\n", " '6bce473dba5b87fbfc7fa4a71f12e497729e8f1258e01a96d35636919f9c4d56',\n", " '4c96397d6955fcdee199dc4794a73c7da1ae103f9d408036e0ed17df012c2c91',\n", " ...]"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["blob_names = request[\"blob_names\"]\n", "blob_names"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"data": {"text/plain": ["2260"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["len(blob_names)"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [], "source": ["bucket = storage.Client().bucket(\"augment-blob-exporter-dev-arun-dev\")"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [], "source": ["from concurrent import futures\n", "executor = futures.ThreadPoolExecutor(max_workers=32)"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [{"data": {"text/plain": ["[True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " True,\n", " ...]"]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["exists = list(executor.map(lambda blob_name: bucket.blob(f\"blobs/{blob_name}\").exists(), blob_names))\n", "exists"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [{"data": {"text/plain": ["1.0"]}, "execution_count": 43, "metadata": {}, "output_type": "execute_result"}], "source": ["sum(exists) / len(blob_names)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}