{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import sys\n", "from pathlib import Path \n", "sys.path.append(str(Path.home() / \"Projects/augment\"))"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["from experimental.arun import notebook_utils"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "AUGMENT_ROOT = Path.cwd().parent.parent\n", "\n", "file = (AUGMENT_ROOT / \"research/eval/edit/data/Bug-Fixing/001.txt\").read_text()\n", "\n", "def parse_diff(file_contents: str, path: str):\n", "    left_start = file_contents.find(\"<<<<<<<\\n\")\n", "    right_start = file_contents.find(\"=======\\n\")\n", "    suffix_start = file_contents.find(\">>>>>>>\\n\")\n", "\n", "    prefix = file_contents[:left_start]\n", "    left = file_contents[left_start + 8: right_start]\n", "    right = file_contents[right_start + 8: suffix_start]\n", "    suffix = file_contents[suffix_start + 8:]\n", "\n", "    return notebook_utils.TwoWayDiff(\n", "        path=path,\n", "        prefix=prefix,\n", "        left=left,\n", "        right=right,\n", "        suffix=suffix,\n", "    )\n", "diff = parse_diff(file, \"test.py\")\n", "diff"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["from IPython.display import display\n", "x = notebook_utils.display_twoway_diff(diff)\n", "display(x)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["notebook_utils._resource_directory()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.6"}}, "nbformat": 4, "nbformat_minor": 2}