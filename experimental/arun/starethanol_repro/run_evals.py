"""Helper script to run all the evaluation jobs.

This script should be manually edited and run to launch all the evaluation jobs.
"""

import copy
import os
import subprocess
import tempfile
from datetime import date
from pathlib import Path

import yaml

from research.core.constants import AUGMENT_ROOT
from research.fastbackward.utils import combine_dict, unflatten_dict

CHECKPOINT_ROOT = Path("/mnt/efs/augment/checkpoints")
RAVEN_ROOT = CHECKPOINT_ROOT / "next-edit-location"

template: dict = yaml.safe_load("""
determined:
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml
  name: fill-me
  project: arun
  workspace: Dev
  tags: starethanol-repro, eval
podspec: 1xA100.yaml
system:
  experimental:
    remove_suffix: false
    retriever_top_k: 100
    trim_on_dedent: false
    trim_on_max_lines: null
  generation_options:
    max_generated_tokens: 280
    temperature: 0
    top_k: 0
    top_p: 0
  model:
    checkpoint_path: rogue/diffb1m_16b_alphal_fixtoken
    name: rogue
    prompt:
      max_number_chunks: 32
      max_prefix_tokens: 1280
      max_prompt_tokens: 3816
      max_retrieved_chunk_tokens: -1
      max_suffix_tokens: 768
  name: basic_rag
  retriever:
    chunker:
      max_lines_per_chunk: -1
      name: line_level
    document_formatter:
      add_path: true
      add_prefix: false
      add_suffix: false
      max_tokens: 999
      name: ethanol6_document
      tokenizer_name: StarCoderTokenizer
    query_formatter:
      add_path: true
      add_suffix: true
      max_tokens: 1023
      name: ethanol6_query
      prefix_ratio: 0.9
      tokenizer_name: StarCoderTokenizer
    scorer:
        name: dense_scorer_v2_fbwd
        checkpoint_path: fill-me
task:
  name: fill-me
""")


TASKS = {
    "api": {
        "task.name": "api",
        "task.dataset": "finegrained-python.large",
        "system.retriever.chunker.max_lines_per_chunk": 40,
    },
    # "repoeval_23lines": {
    #     "task.name": "hydra",
    #     "task.dataset": "repoeval_2-3lines",
    #     "system.retriever.chunker.max_lines_per_chunk": 30,
    # },
    # "all_languages_23lines": {
    #     "task.name": "hydra",
    #     "task.dataset": "all_languages_2-3lines_medium_to_hard.v1.0",
    #     "task.hydra_block_resource_internet_access": True,
    #     "system.retriever.chunker.max_lines_per_chunk": 40,
    # },
    # "cceval": {
    #     "task.name": "cceval",
    #     "system.retriever.chunker.max_lines_per_chunk": 30,
    # },
}

SYSTEMS = {
    "baseline": {
        "system.retriever.scorer.name": "dense_scorer_v2_fbwd_neox",
        "system.retriever.scorer.checkpoint_path": "/mnt/efs/augment/checkpoints/star_ethanol/starethanol6_16.1_mean_proj_512_2000/global_step2000",
        "system.retriever.scorer.dtype": "float16",
    },
    # "batchmean-lls": {
    #     "system.retriever.scorer.name": "dense_scorer_v2_fbwd",
    #     "system.retriever.scorer.checkpoint_path": "/mnt/efs/augment/checkpoints/arun/starethanol-repro/seth6.16-fbwd-bs1x16x32-lr2e-05-iters2000-K128-batchmean-fixed-sampler-lls",
    # },
    # "batchmean-nolls": {
    #     "system.retriever.scorer.name": "dense_scorer_v2_fbwd",
    #     "system.retriever.scorer.checkpoint_path": "/mnt/efs/augment/checkpoints/arun/starethanol-repro/seth6.16-fbwd-bs1x16x32-lr2e-05-iters2000-K128-batchmean-fixed-sampler",
    # },
    # "batchmean-nolls-fp16": {
    #     "system.retriever.scorer.name": "dense_scorer_v2_fbwd",
    #     "system.retriever.scorer.checkpoint_path": "/mnt/efs/augment/checkpoints/arun/starethanol-repro/seth6.16-fbwd-bs1x16x32-lr2e-05-iters2000-K128-batchmean-fixed-sampler",
    #     "system.retriever.scorer.dtype": "float16",
    # },
    # "mean-lls": {
    #     "system.retriever.scorer.name": "dense_scorer_v2_fbwd",
    #     "system.retriever.scorer.checkpoint_path": "/mnt/efs/augment/checkpoints/arun/starethanol-repro/seth6.16-fbwd-bs1x16x32-lr2e-05-iters2000-K128-mean-fixed-sampler-lls",
    # },
    # "mean-lls-fp16": {
    #     "system.retriever.scorer.name": "dense_scorer_v2_fbwd",
    #     "system.retriever.scorer.checkpoint_path": "/mnt/efs/augment/checkpoints/arun/starethanol-repro/seth6.16-fbwd-bs1x16x32-lr2e-05-iters2000-K128-mean-fixed-sampler-lls",
    #     "system.retriever.scorer.dtype": "float16",
    # },
    # "mean-nolls": {
    #     "system.retriever.scorer.name": "dense_scorer_v2_fbwd",
    #     "system.retriever.scorer.checkpoint_path": "/mnt/efs/augment/checkpoints/arun/starethanol-repro/seth6.16-fbwd-bs1x16x32-lr2e-05-iters2000-K128-mean-fixed-sampler",
    # },
}


def run_eval(name: str, config: dict, local: bool = False):
    """Run the evaluation."""

    name = name.replace(" ", "-")
    config["determined"]["name"] = "starethanol-repro-" + name

    with tempfile.NamedTemporaryFile(mode="w", delete=False) as f:
        print(f"Running {name}: ")
        print(yaml.dump(config))

        yaml.dump(config, f)
        f.flush()
        f.close()

        proc = subprocess.run(
            "python3 research/eval/eval.py "
            f"{'--local' if local else ''} "
            f"--job_root /mnt/efs/augment/user/arun/starethanol-repro/{name} "
            f"{f.name}",
            shell=True,
            check=False,
            capture_output=True,
            cwd=AUGMENT_ROOT,
        )
        output = proc.stdout.decode("utf-8").strip()
        print(output)
        url = output.splitlines()[-1].strip()
        assert url.startswith("https://"), url
        # Delete the temp file.
        os.unlink(f.name)

    return url


def get_config(base_config: dict = template, *deltas: dict) -> dict:
    """Get the config."""
    ret = copy.deepcopy(base_config)
    for config_delta in deltas:
        config_delta = unflatten_dict(config_delta)
        ret = combine_dict(ret, config_delta)
    return ret


urls = {}

for task_name, task_config in TASKS.items():
    for system_name, system_config in SYSTEMS.items():
        name = f"{task_name}-{system_name}-{date.today()}"
        config = get_config(template, task_config, system_config)
        url = run_eval(name, config)
        urls[name] = url


print("Name URL")
for name, url in urls.items():
    print(f"{name} {url}")
