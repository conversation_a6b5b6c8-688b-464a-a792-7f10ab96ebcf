{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "\n", "import pandas as pd\n", "\n", "from base.tokenizers import create_tokenizer_by_name\n", "from base.retrieval.chunking import split_line_chunks"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# Load some test data.\n", "_PATH = Path(\"/mnt/efs/spark-data/shared/gh_pr_repo_files_1k\")\n", "\n", "def load_files(path: Path = _PATH):\n", "    for file in path.glob(\"*.parquet\"):\n", "        df = pd.read_parquet(file).sample(frac=1, replace=False)\n", "        yield from df.to_dict(orient=\"records\")\n", "\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "from itertools import islice\n", "from tqdm import tqdm\n", "from base.tokenizers.tokenizer import Tokenizer\n", "\n", "\n", "tokenizer = create_tokenizer_by_name(\"starcoder\")\n", "\n", "def compute_stats(tokenizer: Tokenizer,\n", "    chunk_max_lines: int,\n", "    chunk_max_chars: int,\n", "    overlap_lines: int,\n", "    max_line_width: int = 2048,\n", "    path: Path = _PATH,\n", "    n_samples: int = 1000,\n", "    ):\n", "    token_stats, char_stats = [], []\n", "    for dct in tqdm(islice(load_files(path), n_samples), total=n_samples):\n", "        for chunk in split_line_chunks(dct[\"content\"], chunk_max_lines=chunk_max_lines, chunk_max_chars=chunk_max_chars, overlap_lines=overlap_lines, max_line_width=max_line_width):\n", "            token_stats.append(len(tokenizer.tokenize_safe(chunk.text)))\n", "            char_stats.append(len(chunk.text))\n", "    return np.array(token_stats), np.array(char_stats)\n"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "\n", "def plot_stats(token_stats, char_stats, title):\n", "    fig, ax = plt.subplots(2, 2, figsize=(12, 12))\n", "    ax[0,0].hist(char_stats, bins=100)\n", "    ax[0,0].set_xlabel(\"Characters\")\n", "    ax[0,0].set_ylabel(\"Count\")\n", "    ax[0,0].set_title(f\"{title} Char Histogram\")\n", "    ax[0,0].grid()\n", "    ax[0,1].ecdf(char_stats)\n", "    ax[0,1].set_xlabel(\"Characters\")\n", "    ax[0,1].set_ylabel(\"Count\")\n", "    ax[0,1].set_title(f\"{title} Characters CDF\")\n", "    ax[0,1].set_yticks(np.arange(0, 1.1, 0.1))\n", "    ax[0,1].grid()\n", "    ax[1,0].hist(token_stats, bins=100)\n", "    ax[1,0].set_xlabel(\"Tokens\")\n", "    ax[1,0].set_ylabel(\"Count\")\n", "    ax[1,0].set_title(f\"{title} Tokens Histogram\")\n", "    ax[1,0].grid()\n", "    ax[1,1].ecdf(token_stats)\n", "    ax[1,1].set_xlabel(\"Tokens\")\n", "    ax[1,1].set_ylabel(\"Count\")\n", "    ax[1,1].set_title(f\"{title} Token CDF\")\n", "    ax[1,1].set_yticks(np.arange(0, 1.1, 0.1))\n", "    ax[1,1].grid()\n", "    plt.show()\n"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["stats = {}\n", "for chunk_max_lines in [30, 60, 90, 120]:\n", "    stats[chunk_max_lines] = compute_stats(\n", "        create_tokenizer_by_name(\"starcoder\"),\n", "        chunk_max_lines=chunk_max_lines,\n", "        chunk_max_chars=10*8048,\n", "        overlap_lines=0,\n", "        n_samples=10_000,\n", "    )\n", "    plot_stats(*stats[chunk_max_lines], title=f\"chunk_max_lines={chunk_max_lines}\")"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["for chunk_max_lines, (token_stats, char_stats) in stats.items():\n", "    plot_stats(token_stats, char_stats, title=f\"chunk_max_lines={chunk_max_lines}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}