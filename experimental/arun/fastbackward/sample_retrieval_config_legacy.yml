determined:
  description: null
  workspace: Dev
  project: TrainingTest

augment:
  podspec_path: "1xA100.yaml"
  gpu_count: 1
  project_group: finetuning
  keep_last_n_checkpoints: 3

fastbackward_args:
  run_name: retriever_test
  # Uncomment these if you want to log to wandb.
  # wandb_project: my-project
  # wandb_group: my-group

  components:
    model:
      # This is a function defined in the train script to create the model using
      # token ids from the training data tokenizer.
      $component_name: create_dual_encoder_with_tokenizer
      # We reference the query and document models below. If you wanted to tie these
      # components, just set them to the same name.
      query_model: $query_model
      doc_model: $doc_model
      # The tokenizer is created from the train data configuration and is injected in.
      tokenizer: $tokenizer
      freeze_document_model: True
    query_model:
      # Here is a helper function to load the query model from a starethanol checkpoint.
      $component_name: neox.load_starethanol_checkpoint
      checkpoint_path: /mnt/efs/augment/checkpoints/starethanol/starethanol6_16.1_mean_proj_512_2000/global_step2000
    doc_model:
      # This is another function defined in the train script to create an embedder model
      # from a language model -- it takes care of setting the input dimension based
      # on the language model's hidden dimension.
      $component_name: create_embedder_with_language_model
      # This is the language model we are using.
      language_model: $doc_model/lm
      # This has to match the output dimension of the query model.
      output_projection_dim: 512
      # And we'll set the bias.
      with_output_bias: True
    doc_model/lm:
      $component_name: neox.load_starcoder_checkpoint
      checkpoint_path: /mnt/efs/augment/checkpoints/starcoderbase-1b_neox/checkpoint
      skip_output: True
    loss_fn:
      $component_name: PerplexityLoss
      config:
        gold_temperature: 0.01
        pred_temperature: 1000.0
        logits_scale: 1.0
        learnable_logits_scale: True

  # NOTE: you will want to make changes to the parameters below: they are all very small
  # as this is a test script.
  train_options:
    batch_size: 8
    gradient_accumulation_steps: 1
    max_iters: 5
    log_interval: 1
    grad_clip: 1.0
    optimizer:
      learning_rate: 2.0e-5
      min_lr: 2.0e-6
      betas: [0.9, 0.95]

  eval_interval: 1
  checkpoint_interval: 100

  # "next edit location data"
  train_data:
    path: /mnt/efs/augment/user/guy/data-pipeline/history-of-pr-next-edit-location/12-04-2024/next-edit-location-indexed-dataset-new/dataset.train
    tokenizer_name: starcoder
  eval_data:
    path: /mnt/efs/augment/user/guy/data-pipeline/history-of-pr-next-edit-location/12-04-2024/next-edit-location-indexed-dataset-new/dataset.val
    tokenizer_name: starcoder
    limit: 16
