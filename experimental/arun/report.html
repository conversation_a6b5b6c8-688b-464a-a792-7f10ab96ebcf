<style>
{{style}}

/* Styling for the middle section for it to pop out. */
#middle {
    padding-top: 5px;
    padding-bottom: 5px;
    border: 1px solid;
    color: inherit;
    margin-left: -3px;
}
table#middle {
    color: inherit;
    width: 100%;
}
.hidden {
    display: none;
}
/* CSS */
.button-bar {
    display: flex;
    justify-content: center;
}
button {
  background-color: inherit;
  border-style: none;
  color: inherit;
  cursor: pointer;
  display: inline-block;
  padding: 9px 20px 8px;
}

button:hover, button:focus {
  opacity: .75;
}

.diff-plus {
    background-color: {{"lightgreen" if colorscheme == "light" else "darkgreen"}};
}

.diff-minus {
    background-color: {{"salmon" if colorscheme == "dark" else "darkred"}};
}

</style>
<script type="text/javascript">
const DEFAULT_LINES = 10;

function moveLines(sourceNode, targetNode, moveLast, nLines) {
  // Take the previous k elements from the buffer and push into prefix.
  const sourceLines = sourceNode.innerHTML.split(/^/m);
  if (nLines === undefined) {
    nLines = sourceLines.length;
  }
  nLines = Math.min(nLines, sourceLines.length);

  if (moveLast) {
    const moved = sourceLines.splice(sourceLines.length - nLines, nLines);
    sourceNode.innerHTML = sourceLines.join("");
    targetNode.innerHTML = moved.join("") + targetNode.innerHTML;
  } else {
    const moved = sourceLines.splice(0, nLines);
    sourceNode.innerHTML = sourceLines.join("");
    targetNode.innerHTML += moved.join("");
  }
}

function shrinkPrefix(rootId, nLines) {
  const prefixBuffer = document.getElementById(rootId).getElementsByClassName("prefix-buffer")[0];
  const prefix = document.getElementById(rootId).getElementsByClassName("prefix")[0];
  moveLines(prefix, prefixBuffer, false, nLines);
}

function growPrefix(rootId, nLines) {
  const prefixBuffer = document.getElementById(rootId).getElementsByClassName("prefix-buffer")[0];
  const prefix = document.getElementById(rootId).getElementsByClassName("prefix")[0];
  moveLines(prefixBuffer, prefix, true, nLines);
}

function shrinkSuffix(rootId, nLines) {
  const suffixBuffer = document.getElementById(rootId).getElementsByClassName("suffix-buffer")[0];
  const suffix = document.getElementById(rootId).getElementsByClassName("suffix")[0];
  moveLines(suffix, suffixBuffer, true, nLines);
}

function growSuffix(rootId, nLines) {
  const suffixBuffer = document.getElementById(rootId).getElementsByClassName("suffix-buffer")[0];
  const suffix = document.getElementById(rootId).getElementsByClassName("suffix")[0];
  moveLines(suffixBuffer, suffix, false, nLines);
}
</script>
<body>

<h1>{{ title }}</h1>

<ul>
{% for rootId in diffs %}
<li><a href="#{{rootId}}">{{rootId}}</a></li>
{% endfor %}
</ul>

{% for rootId, diff in diffs.items() %}
<div id="{{rootId}}" class="highlight">
    <h1>{{rootId}}: {{diff.path}}</h1>
    <pre class="prefix-buffer hidden">{{diff.prefix}}</pre>
    <div class="button-bar">
        <button onclick="shrinkPrefix('{{rootId}}')">[--]</button>
        <button onclick="shrinkPrefix('{{rootId}}', DEFAULT_LINES)">[-]</button>
        <button onclick="growPrefix('{{rootId}}', DEFAULT_LINES)">[+]</button>
        <button onclick="growPrefix('{{rootId}}')">[++]</button>
    </div>
    <pre class="prefix"></pre>
    <table id="middle">{{diff.diff_table}}</table>
    <pre class="suffix"></pre>
    <div class="button-bar">
        <button onclick="shrinkSuffix('{{rootId}}')">[--]</button>
        <button onclick="shrinkSuffix('{{rootId}}', DEFAULT_LINES)">[-]</button>
        <button onclick="growSuffix('{{rootId}}', DEFAULT_LINES)">[+]</button>
        <button onclick="growSuffix('{{rootId}}')">[++]</button>
    </div>
    <pre class="suffix-buffer hidden">{{diff.suffix}}</pre>
</div>
{% endfor %}

</body>
