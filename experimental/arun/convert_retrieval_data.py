"""Convert retrieval data between old and new formats.

Old format:
An indexed dataset where the rows are laid out as:

- {query}<|end-of-query|>
- {doc1}<|end-of-key|>{score}<|padding|>
- ...
- {docK}<|end-of-key|>{score}<|padding|>
- {query}<|end-of-query|>
- {doc1}<|end-of-key|>{score}<|padding|>
- ...
- {docK}<|end-of-key|>{score}<|padding|>

In other words, every sequence of K=128 rows is a single "example".

The new format concatenates all the K rows into a single item, without padding.
{query}<|end-of-query|> ↩
{doc1}<|end-of-key|>{score}<|end-of-key|> ↩
... ↩
{docK}<|end-of-key|>{score}<|end-of-key|> ↩


Usage:
python3 convert_retrieval_data.py \
    --input_path /path/to/old/data \

"""

import argparse
import logging
from collections.abc import Iterable
from dataclasses import dataclass
from functools import partial
from multiprocessing import Pool
from pathlib import Path
from tempfile import TemporaryDirectory

import megatron.data.indexed_dataset as indexed_dataset
import numpy as np
from megatron.tokenizer.tokenizer import (
    AbstractTokenizer,
    CodeGenTokenizer,
    StarCoderTokenizer,
    get_tokenizer,
)

TOKEN_SIZE = 2
INDEXED_DATASET_IMPL = "mmap"


logger = logging.getLogger(__name__)


class IndexedDatasetBuilderWrapper:
    """Convenience wrapper around an indexed dataset builder."""

    def __init__(self, output_dataset: Path, vocab_size: int):
        self.output_dataset = output_dataset
        self.builder = indexed_dataset.make_builder(
            str(output_dataset.with_suffix(".bin")),
            impl=INDEXED_DATASET_IMPL,
            vocab_size=vocab_size,
        )
        assert isinstance(self.builder, indexed_dataset.MMapIndexedDatasetBuilder)

    def add_item(self, arr: np.ndarray):
        """Add an item to the dataset."""
        self.builder.add_array_item(arr)  # type: ignore
        self.builder.end_document()

    def append_indexed_dataset(self, other_dataset: Path):
        """Append another indexed dataset to this one."""
        self.builder.merge_file_(str(other_dataset.with_suffix("")))

    def finalize(self):
        """Must be called after all rows have been added."""
        self.builder.finalize(str(self.output_dataset.with_suffix(".idx")))

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_value, exc_traceback):
        self.finalize()


@dataclass
class ExportResult:
    """Result of exporting a parquet file."""

    indexed_dataset_path: Path
    num_rows: int


def make_desired_length(tokens: list[int], desired_length: int) -> list[int]:
    if len(tokens) > desired_length:
        return tokens[:desired_length]
    else:
        return tokens + [0] * (desired_length - len(tokens))


def convert_batch_to_old(
    row_: np.ndarray,
    pad_token_id: int,
    end_of_query_token_id: int,
    end_of_key_token_id: int,
    max_sequence_length: int,
) -> Iterable[np.ndarray]:
    row: list[int] = row_.tolist()

    start = 0
    end = row.index(end_of_query_token_id)
    yield np.array(
        make_desired_length(row[start:end], max_sequence_length - 1)
        + [end_of_query_token_id]
    )
    start = end + 1

    # first positive.
    seen_positive = False
    while start < len(row):
        # Get past the score section.
        end = row.index(end_of_key_token_id, start) + 1
        is_positive = row[end : row.index(end_of_key_token_id, end)] == [34]
        end = row.index(end_of_key_token_id, end)
        assert end - start < max_sequence_length - 1

        tokens = row[start:end] + [pad_token_id]
        start = end + 1

        if seen_positive and is_positive:
            continue

        seen_positive = seen_positive or is_positive
        yield np.array(tokens)


def process_chunk_to_old(
    chunk: slice,
    *,
    input_path: Path,
    batch_size: int,
    pad_token_id: int,
    end_of_query_token_id: int,
    end_of_key_token_id: int,
    vocab_size: int,
    tmp_dir: Path,
    max_sequence_length: int,
) -> ExportResult:
    # NOTE(arun): we are re-loading the dataset for each batch, but it's fine for now.
    dataset = indexed_dataset.make_dataset(
        str(input_path), INDEXED_DATASET_IMPL, skip_warmup=True
    )
    assert dataset is not None

    # call with_suffix("") twice to remove .zstd.parquet suffixes
    indexed_dataset_path = tmp_dir / f"tmp-{chunk.start}"

    skipped_rows = 0

    with IndexedDatasetBuilderWrapper(indexed_dataset_path, vocab_size) as builder:
        for item in dataset[chunk]:
            subitems = list(
                convert_batch_to_old(
                    item,
                    pad_token_id,
                    end_of_query_token_id,
                    end_of_key_token_id,
                    max_sequence_length,
                )
            )
            if len(subitems) < batch_size:
                skipped_rows += 1
                continue

            for subitem in subitems[:batch_size]:
                builder.add_item(subitem)

    num_rows = chunk.stop - chunk.start

    logger.info(
        "Processed %d rows from %s[%d:%d] to %s (skipped %d)",
        num_rows - skipped_rows,
        input_path,
        chunk.start,
        chunk.stop,
        indexed_dataset_path,
        skipped_rows,
    )
    return ExportResult(indexed_dataset_path, num_rows)


def convert_to_old(
    input_path: Path,
    output_path: Path,
    batch_size: int,
    tokenizer: AbstractTokenizer,
    num_workers: int = 1,
    chunk_size: int = 8 * 1024,
    max_sequence_length: int = 1024,
    _tmp_dir: Path = Path("/tmp"),
):
    dataset = indexed_dataset.make_dataset(
        str(input_path), INDEXED_DATASET_IMPL, skip_warmup=True
    )
    assert dataset is not None
    assert isinstance(tokenizer, (CodeGenTokenizer, StarCoderTokenizer))

    batches = len(dataset)
    chunks = [
        slice(start_batch_idx, min(batches, start_batch_idx + chunk_size))
        for start_batch_idx in range(0, batches, chunk_size)
    ]
    logger.info(
        "Total dataset has %d rows => %d batches. Will be processed by %d workers in %d chunks",
        len(dataset),
        batches,
        num_workers,
        len(chunks),
    )

    output_path.mkdir(parents=True, exist_ok=True)
    output_dataset_path = output_path / input_path.name

    if output_dataset_path.with_suffix(".idx").exists():
        raise FileExistsError(f"Output path {output_dataset_path} already exists")

    num_exported_rows = 0
    with (
        TemporaryDirectory(dir=_tmp_dir) as tmp_dir,
        IndexedDatasetBuilderWrapper(
            output_dataset_path, tokenizer.vocab_size
        ) as builder,
    ):
        tmp_dir = Path(tmp_dir)
        process_chunk_fn = partial(
            process_chunk_to_old,
            input_path=input_path,
            batch_size=batch_size,
            pad_token_id=tokenizer.pad_id,
            end_of_query_token_id=tokenizer.ret_endofquery_id,
            end_of_key_token_id=tokenizer.ret_endofkey_id,
            vocab_size=tokenizer.vocab_size,
            tmp_dir=tmp_dir,
            max_sequence_length=max_sequence_length,
        )

        def process_exported_parquet_file(i, export_result):
            nonlocal num_exported_rows
            num_exported_rows += export_result.num_rows
            builder.append_indexed_dataset(export_result.indexed_dataset_path)
            print(f"Exported {export_result.indexed_dataset_path} to main split")

            export_result.indexed_dataset_path.with_suffix(".bin").unlink()
            export_result.indexed_dataset_path.with_suffix(".idx").unlink()

            queue_size = len(list(tmp_dir.glob("*.bin")))
            print(
                f"Completed {i+1} out of {len(chunks)} files. Merge queue size: {queue_size}"
            )
            print()

        if num_workers > 0:
            with Pool(num_workers) as pool:
                for i, export_result in enumerate(
                    pool.imap_unordered(process_chunk_fn, chunks)
                ):
                    process_exported_parquet_file(i, export_result)
        else:
            # Just run a single slice.
            export_result = process_chunk_fn(chunks[0])
            process_exported_parquet_file(0, export_result)

    print(f"Exported {num_exported_rows} into: {output_dataset_path}")


def convert_batch(
    rows_: list[np.ndarray],
    pad_token_id: int,
    end_of_query_token_id: int,
    end_of_key_token_id: int,
) -> np.ndarray:
    rows = [row.tolist() for row in rows_]

    query_rows = [
        row[: row.index(end_of_query_token_id) + 1]
        for row in rows[:1]
        if end_of_query_token_id in row
    ]
    doc_rows = [
        row[: row.index(pad_token_id)] for row in rows[1:] if end_of_key_token_id in row
    ]
    assert len(query_rows) == 1
    assert len(doc_rows) == len(rows) - 1

    ret = query_rows[0]
    for row in doc_rows:
        ret.extend(row)
        ret.append(end_of_key_token_id)
    return np.array(ret)


def process_chunk_to_new(
    chunk: slice,
    *,
    input_path: Path,
    batch_size: int,
    pad_token_id: int,
    end_of_query_token_id: int,
    end_of_key_token_id: int,
    vocab_size: int,
    tmp_dir: Path,
) -> ExportResult:
    # NOTE(arun): we are re-loading the dataset for each batch, but it's fine for now.
    dataset = indexed_dataset.make_dataset(
        str(input_path), INDEXED_DATASET_IMPL, skip_warmup=True
    )
    assert dataset is not None

    # call with_suffix("") twice to remove .zstd.parquet suffixes
    indexed_dataset_path = tmp_dir / f"tmp-{chunk.start}"

    with IndexedDatasetBuilderWrapper(indexed_dataset_path, vocab_size) as builder:
        for batch_idx in range(chunk.start, chunk.stop):
            batch = dataset[batch_idx * batch_size : (batch_idx + 1) * batch_size]
            assert isinstance(batch, list)
            builder.add_item(
                convert_batch(
                    batch,
                    pad_token_id,
                    end_of_query_token_id,
                    end_of_key_token_id,
                )
            )

    num_rows = chunk.stop - chunk.start

    logger.info(
        "Processed %d rows from %s[%d:%d] to %s",
        num_rows,
        input_path,
        chunk.start,
        chunk.stop,
        indexed_dataset_path,
    )
    return ExportResult(indexed_dataset_path, num_rows)


def convert_to_new(
    input_path: Path,
    output_path: Path,
    batch_size: int,
    tokenizer: AbstractTokenizer,
    num_workers: int = 1,
    chunk_size: int = 8 * 1024,
    _tmp_dir: Path = Path("/tmp"),
):
    dataset = indexed_dataset.make_dataset(
        str(input_path), INDEXED_DATASET_IMPL, skip_warmup=True
    )
    assert dataset is not None
    assert (
        len(dataset) % batch_size == 0
    ), f"{len(dataset)=} is not a multiple of {batch_size=}"
    assert isinstance(tokenizer, (CodeGenTokenizer, StarCoderTokenizer))

    batches = len(dataset) // batch_size
    chunks = [
        slice(start_batch_idx, min(batches, start_batch_idx + chunk_size))
        for start_batch_idx in range(0, batches, chunk_size)
    ]
    logger.info(
        "Total dataset has %d rows => %d batches. Will be processed by %d workers in %d chunks",
        len(dataset),
        batches,
        num_workers,
        len(chunks),
    )

    output_path.mkdir(parents=True, exist_ok=True)
    output_dataset_path = output_path / input_path.name

    if output_dataset_path.with_suffix(".idx").exists():
        raise FileExistsError(f"Output path {output_dataset_path} already exists")

    num_exported_rows = 0
    with (
        TemporaryDirectory(dir=_tmp_dir) as tmp_dir,
        IndexedDatasetBuilderWrapper(
            output_dataset_path, tokenizer.vocab_size
        ) as builder,
    ):
        tmp_dir = Path(tmp_dir)
        process_chunk_fn = partial(
            process_chunk_to_new,
            input_path=input_path,
            batch_size=batch_size,
            pad_token_id=tokenizer.pad_id,
            end_of_query_token_id=tokenizer.ret_endofquery_id,
            end_of_key_token_id=tokenizer.ret_endofkey_id,
            vocab_size=tokenizer.vocab_size,
            tmp_dir=tmp_dir,
        )

        def process_exported_parquet_file(i, export_result):
            nonlocal num_exported_rows
            num_exported_rows += export_result.num_rows
            builder.append_indexed_dataset(export_result.indexed_dataset_path)
            print(f"Exported {export_result.indexed_dataset_path} to main split")

            export_result.indexed_dataset_path.with_suffix(".bin").unlink()
            export_result.indexed_dataset_path.with_suffix(".idx").unlink()

            queue_size = len(list(tmp_dir.glob("*.bin")))
            print(
                f"Completed {i+1} out of {len(chunks)} files. Merge queue size: {queue_size}"
            )
            print()

        if num_workers > 0:
            with Pool(num_workers) as pool:
                for i, export_result in enumerate(
                    pool.imap_unordered(process_chunk_fn, chunks)
                ):
                    process_exported_parquet_file(i, export_result)
        else:
            # Just run a single slice.
            export_result = process_chunk_fn(chunks[0])
            process_exported_parquet_file(0, export_result)

    print(f"Exported {num_exported_rows} into: {output_dataset_path}")


def main():
    logging.basicConfig(level=logging.INFO)
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "-i",
        "--input",
        type=Path,
        required=True,
        help="path to the root of an indexed dataset in old format.",
    )
    parser.add_argument(
        "-o",
        "--output",
        type=Path,
        required=True,
        help="path to the root of where we should save the processed indexed dataset.",
    )
    parser.add_argument(
        "-b",
        "--batch-size",
        type=int,
        required=True,
        help="the batch size to use when exporting",
    )
    parser.add_argument(
        "-t",
        "--tokenizer",
        type=str,
        default="StarCoderTokenizer",
        help="The tokenizer to use for the padding token.",
    )
    parser.add_argument(
        "--num_workers",
        type=int,
        default=8,
        help="number of data worker processes, or 0 to avoid multiprocessing",
    )
    parser.add_argument(
        "--tmp_dir",
        type=Path,
        default=Path("/tmp"),
        help="a temporary directory",
    )
    parser.add_argument(
        "-l",
        "--max-sequence-length",
        type=int,
        default=1024,
        help="If converting from new to old, the max sequence length to use.",
    )
    parser.add_argument(
        "--to-old",
        action="store_true",
        default=False,
        help="If set, convert to the old format.",
    )
    args = parser.parse_args()

    args.tmp_dir.mkdir(parents=True, exist_ok=True)

    tokenizer = get_tokenizer(args.tokenizer)

    if args.to_old:
        convert_to_old(
            args.input,
            args.output,
            args.batch_size,
            tokenizer,
            num_workers=args.num_workers,
            max_sequence_length=args.max_sequence_length,
            _tmp_dir=args.tmp_dir,
        )
    else:
        convert_to_new(
            args.input,
            args.output,
            args.batch_size,
            tokenizer,
            num_workers=args.num_workers,
            _tmp_dir=args.tmp_dir,
        )


if __name__ == "__main__":
    main()
