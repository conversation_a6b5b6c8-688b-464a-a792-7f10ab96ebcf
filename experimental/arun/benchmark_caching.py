"""Quick script to benchmark dense scorers."""

import logging
from pathlib import Path

import yaml

from research.eval.harness.factories import create_retriever
from research.model_server.file_store import FileStore

logger = logging.getLogger(__name__)


def main():
    logging.basicConfig(level=logging.INFO, force=True)

    retriever = create_retriever(
        yaml.safe_load("""
    scorer:
        name: dense_scorer_v2_fbwd
        checkpoint_path: /mnt/efs/augment/checkpoints/next-edit-location/raven1b.v13-query-1pos-bs1x8x16-lr2e-05-iters2000-K128
        tokenizer_name: starcoder
        cache_dir: /tmp/augment/cache
    chunker:
        name: line_level
        max_lines_per_chunk: 30
    query_formatter:
        name: base:next-edit-location-query
        tokenizer: starcoder
    document_formatter:
        name: base:ethanol6-embedding-with-path-key
        tokenizer: starcoder
        max_tokens: 999
    """)
    )
    file_store = FileStore(
        Path(
            "/tmp/model_server_file_store/port_5000/64609937-cc50-4ffa-a2bb-7aecc276ba0d/"
        )
    )
    retriever.add_docs(file_store.get_documents())


if __name__ == "__main__":
    main()
