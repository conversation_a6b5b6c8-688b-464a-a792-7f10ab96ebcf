"""Determine how many repos have a single requirements.txt file."""

import glob
import json
from pathlib import Path

for path in glob.glob("*_repo_to_reqs.json"):
    with Path(path).open("r", encoding="utf-8") as f:
        data = json.load(f)
    num_repos_with_single_req = sum([1 for reqs in data.values() if len(reqs) == 1])
    total_repos = len(data)
    print(
        f"{path}: {num_repos_with_single_req} repos out of {total_repos} have a single reqs file, that's {num_repos_with_single_req/total_repos*100:.1f}%"
    )
