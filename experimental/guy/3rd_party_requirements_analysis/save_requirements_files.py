"""Save statistics on requirements.txt and similar files from The Stack.

Runs on parquet files and saves two files:
- /tmp/LANG_reqs.json : maps a requirement line to the number of times it appears in the dataset.
- /tmp/LANG_repo_to_reqs.json : maps a repository to the list of requirement files it contains.
"""

import argparse
import glob
import json
import re
from pathlib import Path

import pyarrow.parquet as pq


def print_parquet_schema(file_path: str):
    parquet_file = pq.ParquetFile(file_path)
    schema = parquet_file.schema
    print(schema)


def collect_requirements(
    file_paths: list[str],
    reqs_filename: str,
    reqs_output_filename: str,
    repo_to_reqs_output_filename: str,
    skip_reqs_pattern=None,
):
    """Print contents of parquet file."""
    if Path(reqs_output_filename).exists():
        raise FileExistsError(f"{reqs_output_filename} already exists.")
    if Path(repo_to_reqs_output_filename).exists():
        raise FileExistsError(f"{repo_to_reqs_output_filename} already exists.")

    num_reqs_files = 0
    reqs: dict[str, int] = {}
    repo_to_reqs: dict[str, list[str]] = {}
    for file_path in file_paths:
        print(f"processing {file_path}")
        table = pq.read_table(file_path).to_pandas()
        for i, row in table.iterrows():
            repo = row["max_stars_repo_name"]
            path = row["max_stars_repo_path"]

            if path.endswith(reqs_filename):
                if skip_reqs_pattern is not None and re.search(skip_reqs_pattern, path):
                    continue
                num_reqs_files += 1
                if repo not in repo_to_reqs:
                    repo_to_reqs[repo] = []
                repo_to_reqs[repo].append(path)

                content = row["content"]
                for line in content.splitlines():
                    line = line.strip()
                    if line not in reqs:
                        reqs[line] = 0
                    reqs[line] += 1
        print("=" * 80)
        print(f"so far: found {num_reqs_files} {reqs_filename} files")
        print(f"so far: found {len(reqs)} unique requirements")
        if len(repo_to_reqs) > 0:
            percent_repos_with_single_reqs_file = [
                len(v) == 1 for v in repo_to_reqs.values()
            ].count(True) / len(repo_to_reqs)
            print(
                f"so far: found {len(repo_to_reqs)} repos, {percent_repos_with_single_reqs_file:.1%} have a single {reqs_filename} file"
            )

        with Path(reqs_output_filename).open("w", encoding="utf-8") as f:
            json.dump(reqs, f, indent=2)
        with Path(repo_to_reqs_output_filename).open("w", encoding="utf-8") as f:
            json.dump(repo_to_reqs, f, indent=2)

        print("saved")

    print(f"found {num_reqs_files} {reqs_filename} files")
    print(f"found {len(reqs)} unique requirements")


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--lang",
        type=str,
        required=True,
        help="Language to use for the program, python or javascript",
    )
    args = parser.parse_args()

    if args.lang == "python-requirements":
        collect_requirements(
            file_paths=sorted(
                glob.glob(
                    "/mnt/efs/augment-lga1-nvme/data/raw/the-stack-dedup.2023-02-04/data/langpart=text/data_*.parquet"
                )
            ),
            reqs_filename="requirements.txt",
            reqs_output_filename="/tmp/python_requirements_reqs.json",
            repo_to_reqs_output_filename="/tmp/python_requirements_repo_to_reqs.json",
        )
    elif args.lang == "python-setup":
        collect_requirements(
            file_paths=sorted(
                glob.glob(
                    "/mnt/efs/augment-lga1-nvme/data/raw/the-stack-dedup.2023-02-04/data/langpart=python/data_*.parquet"
                )
            ),
            reqs_filename="setup.py",
            reqs_output_filename="/tmp/python_setup_reqs.json",
            repo_to_reqs_output_filename="/tmp/python_setup_repo_to_reqs.json",
        )
    elif args.lang == "javascript":
        collect_requirements(
            file_paths=sorted(
                glob.glob(
                    "/mnt/efs/augment-lga1-nvme/data/raw/the-stack-dedup.2023-02-04/data/langpart=json/data_*.parquet"
                )
            ),
            reqs_filename="package.json",
            reqs_output_filename="/tmp/javascript_reqs.json",
            repo_to_reqs_output_filename="/tmp/javascript_repo_to_reqs.json",
            skip_reqs_pattern="node_modules/",
        )
    else:
        raise ValueError(f"Language {args.lang} not supported")


if __name__ == "__main__":
    main()
