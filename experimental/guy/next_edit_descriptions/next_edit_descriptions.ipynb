{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["Generate descriptions for generated next-edit hunks."]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Examining the data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas\n", "from pathlib import Path\n", "from tqdm import tqdm\n", "\n", "descriptions_root = Path(\n", "    \"/mnt/efs/spark-data/user/guy/data-pipeline/next_edit_descriptions\"\n", ")\n", "\n", "\n", "def count_change_stats(diff):\n", "    num_added = 0\n", "    num_removed = 0\n", "    num_unchanged = 0\n", "    for line in diff.splitlines():\n", "        if line.startswith(\"+++\") or line.startswith(\"---\") or line.startswith(\"@@\"):\n", "            continue\n", "        if line.startswith(\"+\"):\n", "            num_added += 1\n", "        elif line.startswith(\"-\"):\n", "            num_removed += 1\n", "        else:\n", "            num_unchanged += 1\n", "\n", "    return num_added, num_removed, num_unchanged\n", "\n", "\n", "# parquet_file = list(descriptions_root.glob(\"*.parquet\"))[0]\n", "# df = pandas.read_parquet(parquet_file)\n", "\n", "all_num_added = []\n", "all_num_removed = []\n", "all_num_changed = []\n", "all_num_unchanged = []\n", "all_words = []\n", "\n", "max_files = 100\n", "\n", "parquet_files = list(descriptions_root.glob(\"*.parquet\"))[:max_files]\n", "\n", "for parquet_file in tqdm(parquet_files, total=len(parquet_files)):\n", "    try:\n", "        df = pandas.read_parquet(parquet_file)\n", "        for desc, diff in zip(\n", "            list(df[\"descriptions\"][0]), list(df[\"description_diff_inputs\"][0])\n", "        ):\n", "            num_added, num_removed, num_unchanged = count_change_stats(diff)\n", "\n", "            # Skip samples with no diff\n", "            if num_unchanged == 0:\n", "                continue\n", "\n", "            all_num_added.append(num_added)\n", "            all_num_removed.append(num_removed)\n", "            all_num_changed.append(num_added + num_removed)\n", "            all_num_unchanged.append(num_unchanged)\n", "\n", "            words = desc.split(\" \")\n", "            all_words.extend(words)\n", "\n", "    except Exception as e:\n", "        print(f\"Failed to process {parquet_file}: {e}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\n", "    f\"Frac of samples with >10 added lines: {sum(1 for n in all_num_added if n > 10) / len(all_num_added)}\"\n", ")\n", "print(\n", "    f\"Frac of samples with >10 changed lines: {sum(1 for n in all_num_changed if n > 10) / len(all_num_changed)}\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"typo\" in all_words"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from collections import Counter\n", "\n", "boring_words = [\"and\", \"to\", \"from\", \"with\", \"in\", \"add\", \"for\"]\n", "\n", "words = [w for w in all_words if w not in boring_words]\n", "\n", "# Count the occurrences of each word\n", "word_counts = Counter(words)\n", "\n", "# Get the 10 most common words\n", "top_k = word_counts.most_common(20)\n", "\n", "# Print the results\n", "print(\"Top most common words:\")\n", "for word, count in top_k:\n", "    print(f\"{word}: {count}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "from wordcloud import WordCloud\n", "\n", "# Assuming you have a string of text or a list of words\n", "# print(len(all_words))\n", "\n", "# Create and generate a word cloud image\n", "wordcloud = WordCloud(width=800, height=400, background_color=\"white\").generate(\n", "    \" \".join(all_words)\n", ")\n", "\n", "# Display the generated image\n", "plt.figure(figsize=(10, 5))\n", "plt.imshow(wordcloud, interpolation=\"bilinear\")\n", "plt.axis(\"off\")\n", "plt.tight_layout(pad=0)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "\n", "fig, ax = plt.subplots()\n", "ax.hist(all_num_added, bins=list(range(20)))\n", "ax.set_title(\"lines added\")\n", "\n", "fig, ax = plt.subplots()\n", "ax.hist(all_num_removed, bins=list(range(20)))\n", "ax.set_title(\"lines removed\")\n", "\n", "fig, ax = plt.subplots()\n", "ax.hist(all_num_changed, bins=list(range(20)))\n", "ax.set_title(\"lines changed\")\n", "\n", "fig, ax = plt.subplots()\n", "ax.hist(all_num_unchanged, bins=list(range(20)))\n", "ax.set_title(\"lines unchanged\")\n", "\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(sum(1 for n in all_num_changed if n == 0) / len(all_num_changed))"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Failed to process /mnt/efs/spark-data/user/guy/data-pipeline/next_edit_descriptions_v3_randomctx_shortdesc/part-00383-18a737dc-7c25-44d0-aa7c-6674b146ea3a-c000.zstd.parquet: 'descriptions'\n", "Failed to process /mnt/efs/spark-data/user/guy/data-pipeline/next_edit_descriptions_v3_randomctx_shortdesc/part-01232-18a737dc-7c25-44d0-aa7c-6674b146ea3a-c000.zstd.parquet: 'descriptions'\n", "Failed to process /mnt/efs/spark-data/user/guy/data-pipeline/next_edit_descriptions_v3_randomctx_shortdesc/part-01237-18a737dc-7c25-44d0-aa7c-6674b146ea3a-c000.zstd.parquet: 'descriptions'\n", "Failed to process /mnt/efs/spark-data/user/guy/data-pipeline/next_edit_descriptions_v3_randomctx_shortdesc/part-02277-18a737dc-7c25-44d0-aa7c-6674b146ea3a-c000.zstd.parquet: 'descriptions'\n", "Failed to process /mnt/efs/spark-data/user/guy/data-pipeline/next_edit_descriptions_v3_randomctx_shortdesc/part-02783-18a737dc-7c25-44d0-aa7c-6674b146ea3a-c000.zstd.parquet: 'descriptions'\n", "Enable Mailhog integration\n", "\n", "--- 5.6/config/php/zz-php.ini\n", "+++ 5.6/config/php/zz-php.ini\n", "@@ -3,12 +3,14 @@\n", " [php]\n", " memory_limit = 512M\n", " max_execution_time = 600\n", " always_populate_raw_post_data = -1\n", " sendmail_path = /bin/true\n", " date.timezone = UTC\n", " display_errors = On\n", " display_startup_errors = On\n", "+# Enable Mailhog integration by default\n", "+sendmail_path = '/usr/local/bin/mhsendmail --smtp-addr=mail:1025'\n", " \n", " ; Extention settings\n", " [opcache]\n", " opcache.memory_consumption = 128\n", "\n", "\n", "========================================================================================================================\n", "Enable Mailhog integration\n", "\n", "--- 7.1/config/php/zz-php.ini\n", "+++ 7.1/config/php/zz-php.ini\n", "@@ -6,8 +6,10 @@\n", " sendmail_path = /bin/true\n", " date.timezone = UTC\n", " display_errors = On\n", " display_startup_errors = On\n", "+# Enable Mailhog integration by default\n", "+sendmail_path = '/usr/local/bin/mhsendmail --smtp-addr=mail:1025'\n", " \n", " ; Extention settings\n", " [opcache]\n", " opcache.memory_consumption = 128\n", "\n", "\n", "========================================================================================================================\n", "Add Pantheon terminus\n", "\n", "--- README.md\n", "+++ README.md\n", "@@ -24,16 +24,17 @@\n", " - php\n", "   - php-fpm && php-cli\n", "   - xdebug\n", "   - composer\n", "   - drush\n", "     - registry_rebuild\n", "     - coder-8.x + phpcs\n", "-\n", "+    - Acquia Cloud API commands\n", "   - drupal console launcher\n", "+  - terminus (Pantheon)\n", "   - wp-cli\n", " - ruby\n", "   - ruby\n", "   - gem\n", "   - bundler\n", " - nodejs\n", "   - nvm\n", "\n", "\n", "========================================================================================================================\n", "Refactor startup script\n", "\n", "--- 7.1/startup.sh\n", "+++ 7.1/startup.sh\n", "@@ -1,5 +1,22 @@\n", "+#!/bin/bash\n", "+\n", "+# This script is running as root by default.\n", "+# Switching to the docker user can be done via \"gosu docker <command>\".\n", "+\n", "+HOME_DIR='/home/<USER>'\n", "+\n", "+DEBUG=${DEBUG:-0}\n", "+# Turn debugging ON when cli is started in the service mode\n", "+[[ \"$1\" == \"supervisord\" ]] && DEBUG=1\n", "+echo-debug ()\n", "+{\n", "+\t[[ \"$DEBUG\" != 0 ]] && echo \"$@\"\n", "+}\n", "+\n", "+uid_gid_reset ()\n", "+{\n", " #!/bin/bash\n", " \n", " # This script is running as root by default.\n", " # Switching to the docker user can be done via \"gosu docker <command>\".\n", " \n", "@@ -50,21 +67,11 @@\n", " }\n", " \n", " # Process templates\n", " # Private SSH key\n", " render_tmpl \"$HOME_DIR/.ssh/id_rsa\"\n", "-# Acquia Cloud API config\n", "-render_tmpl \"$HOME_DIR/.acquia/cloudapi.conf\"\n", "-\n", "-# Terminus authentication\n", "-[[ \"$SECRET_TERMINUS_TOKEN\" ]] && terminus_login\n", "-\n", "-# Docker user uid/gid mapping to the host user uid/gid\n", "-[[ \"$HOST_UID\" != \"\" ]] && [[ \"$HOST_GID\" != \"\" ]] && uid_gid_reset\n", "-\n", "-# Enable xdebug\n", "-[[ \"$XDEBUG_ENABLED\" != \"\" ]] && [[ \"$XDEBUG_ENABLED\" != \"0\" ]] && xdebug_enable\n", "+ \"0\" ]] && xdebug_enable\n", " \n", " # Make sure permissions are correct (after uid/gid change and COPY operations in Dockerfile)\n", " # To not bloat the image size, permissions on the home folder are reset at runtime.\n", " echo-debug \"Resetting permissions on $HOME_DIR and /var/www...\"\n", " chown \"$HOST_UID:$HOST_GID\" -R \"$HOME_DIR\"\n", "\n", "\n", "========================================================================================================================\n", "Add missing chmod command\n", "\n", "--- 7.1/startup.sh\n", "+++ 7.1/startup.sh\n", "@@ -60,11 +60,12 @@\n", " \n", " # Docker user uid/gid mapping to the host user uid/gid\n", " [[ \"$HOST_UID\" != \"\" ]] && [[ \"$HOST_GID\" != \"\" ]] && uid_gid_reset\n", " \n", " # Enable xdebug\n", "-[[ \"$XDEBUG_ENABLED\" != \"\" ]] && [[ \"$XDEBUG_ENABLED\" != \"0\" ]] && xdebug_enable\n", "+[[ \"$XDEBUG_ENABLED\" != \"\" ]] && [[ \"$XDEBUG_ENABLED\" !=chmod 0600 \"$HOME_DIR/.ssh/id_rsa\"\n", "+ \"0\" ]] && xdebug_enable\n", " \n", " # Make sure permissions are correct (after uid/gid change and COPY operations in Dockerfile)\n", " # To not bloat the image size, permissions on the home folder are reset at runtime.\n", " echo-debug \"Resetting permissions on $HOME_DIR and /var/www...\"\n", " chown \"$HOST_UID:$HOST_GID\" -R \"$HOME_DIR\"\n", "\n", "\n", "========================================================================================================================\n", "Reorder and duplicate code blocks\n", "\n", "--- 7.1/startup.sh\n", "+++ 7.1/startup.sh\n", "@@ -60,10 +60,20 @@\n", " \n", " # Docker user uid/gid mapping to the host user uid/gid\n", " [[ \"$HOST_UID\" != \"\" ]] && [[ \"$HOST_GID\" != \"\" ]] && uid_gid_reset\n", " \n", " # Enable xdebug\n", "+[[ \"$XDEBUG_ENABLED\" != \"\" ]] && [[ \"$XDEBUG_ENABLED\" !=# Acquia Cloud API config\n", "+render_tmpl \"$HOME_DIR/.acquia/cloudapi.conf\"\n", "+\n", "+# Terminus authentication\n", "+[[ \"$SECRET_TERMINUS_TOKEN\" ]] && terminus_login\n", "+\n", "+# Docker user uid/gid mapping to the host user uid/gid\n", "+[[ \"$HOST_UID\" != \"\" ]] && [[ \"$HOST_GID\" != \"\" ]] && uid_gid_reset\n", "+\n", "+# Enable xdebug\n", " [[ \"$XDEBUG_ENABLED\" != \"\" ]] && [[ \"$XDEBUG_ENABLED\" != \"0\" ]] && xdebug_enable\n", " \n", " # Make sure permissions are correct (after uid/gid change and COPY operations in Dockerfile)\n", " # To not bloat the image size, permissions on the home folder are reset at runtime.\n", " echo-debug \"Resetting permissions on $HOME_DIR and /var/www...\"\n", "\n", "\n", "========================================================================================================================\n", "Refactor startup script\n", "\n", "--- 7.1/startup.sh\n", "+++ 7.1/startup.sh\n", "@@ -1,3 +1,53 @@\n", "+#!/bin/bash\n", "+\n", "+# This script is running as root by default.\n", "+# Switching to the docker user can be done via \"gosu docker <command>\".\n", "+\n", "+HOME_DIR='/home/<USER>'\n", "+\n", "+DEBUG=${DEBUG:-0}\n", "+# Turn debugging ON when cli is started in the service mode\n", "+[[ \"$1\" == \"supervisord\" ]] && DEBUG=1\n", "+echo-debug ()\n", "+{\n", "+\t[[ \"$DEBUG\" != 0 ]] && echo \"$@\"\n", "+}\n", "+\n", "+uid_gid_reset ()\n", "+{\n", "+\tif [[ \"$HOST_UID\" != \"$(id -u docker)\" ]] || [[ \"$HOST_GID\" != \"$(id -g docker)\" ]]; then\n", "+\t\techo-debug \"Updating docker user uid/gid to $HOST_UID/$HOST_GID to match the host user uid/gid...\"\n", "+\t\tusermod -u \"$HOST_UID\" -o docker\n", "+\t\tgroupmod -g \"$HOST_GID\" -o \"$(id -gn docker)\"\n", "+\tfi\n", "+}\n", "+\n", "+xdebug_enable ()\n", "+{\n", "+\techo-debug \"Enabling xdebug...\"\n", "+\tsudo ln -s /opt/docker-php-ext-xdebug.ini /usr/local/etc/php/conf.d/\n", "+}\n", "+\n", "+# Helper function to render configs from go templates using gomplate\n", "+render_tmpl ()\n", "+{\n", "+\tlocal file=\"${1}\"\n", "+\tlocal tmpl=\"${1}.tmpl\"\n", "+\n", "+\tif [[ -f \"${tmpl}\" ]]; then\n", "+\t\techo-debug \"Rendering template: ${tmpl}...\"\n", "+\t\tgomplate --file \"${tmpl}\" --out \"${file}\"\n", "+\telse\n", "+\t\techo-debug \"Error: Template file not found: ${tmpl}\"\n", "+\t\treturn 1\n", "+\tfi\n", "+}\n", "+\n", "+terminus_login ()\n", "+{\n", "+\techo-debug \"Authenticating with Pantheon...\"\n", "+\tterminus auth:login --machine-token=\"$SECRET_TERMINUS_TOKEN\" > /dev/null\n", "+}\n", " #!/bin/bash\n", " \n", " # This script is running as root by default.\n", "@@ -52,36 +102,7 @@\n", " # Process templates\n", " # Private SSH key\n", " render_tmpl \"$HOME_DIR/.ssh/id_rsa\"\n", "-\n", "-# Acquia Cloud API config\n", "-render_tmpl \"$HOME_DIR/.acquia/cloudapi.conf\"\n", "-\n", "-# Terminus authentication\n", "-[[ \"$SECRET_TERMINUS_TOKEN\" ]] && terminus_login\n", "-\n", "-# Docker user uid/gid mapping to the host user uid/gid\n", "-[[ \"$HOST_UID\" != \"\" ]] && [[ \"$HOST_GID\" != \"\" ]] && uid_gid_reset\n", "-\n", "-# Enable xdebug\n", "-[[ \"$XDEBUG_ENABLED\" != \"\" ]] && [[ \"$XDEBUG_ENABLED\" != \"0\" ]] && xdebug_enable\n", "-\n", "-# Make sure permissions are correct (after uid/gid change and COPY operations in Dockerfile)\n", "-# To not bloat the image size, permissions on the home folder are reset at runtime.\n", "-echo-debug \"Resetting permissions on $HOME_DIR and /var/www...\"\n", "-chown \"$HOST_UID:$HOST_GID\" -R \"$HOME_DIR\"\n", "-# <PERSON><PERSON> resets the project root folder permissions to 0:0 when cli is recreated (e.g. an env variable updated).\n", "-# Why apply a fix/woraround for this at startup.\n", "-chown \"$HOST_UID:$HOST_GID\" /var/www\n", "-\n", "-# Initialization steps completed. Create a pid file to mark the container as healthy\n", "-echo-debug \"Preliminary initialization completed\"\n", "-touch /var/run/cli\n", "-\n", "-# Execute passed CMD arguments\n", "-echo-debug \"Executing the requested command...\"\n", "-# Service mode (run as root)\n", "-if [[ \"$1\" == \"supervisord\" ]]; then\n", "-\texec gosu root supervisord -c /etc/supervisor/conf.d/supervisord.conf\n", "+conf\n", " # Command mode (run as docker user)\n", " else\n", " \t# This makes sure the environment is set up correctly for the docker user\n", "\n", "\n", "========================================================================================================================\n", "Add missing file permission command\n", "\n", "--- 7.1/startup.sh\n", "+++ 7.1/startup.sh\n", "@@ -81,7 +81,8 @@\n", " echo-debug \"Executing the requested command...\"\n", " # Service mode (run as root)\n", " if [[ \"$1\" == \"supervisord\" ]]; then\n", "-\texec gosu root supervisord -c /etc/supervisor/conf.d/supervisord.conf\n", "+\texec gosu root supervisord -c /etc/supervisor/conf.d/supervisord.chmod 0600 \"$HOME_DIR/.ssh/id_rsa\"\n", "+onf\n", " # Command mode (run as docker user)\n", " else\n", " \t# This makes sure the environment is set up correctly for the docker user\n", "\n", "\n", "========================================================================================================================\n", "Add startup initialization steps\n", "\n", "--- 7.1/startup.sh\n", "+++ 7.1/startup.sh\n", "@@ -81,6 +81,34 @@\n", " echo-debug \"Executing the requested command...\"\n", " # Service mode (run as root)\n", " if [[ \"$1\" == \"supervisord\" ]]; then\n", "+\texec gosu root supervisord -c /etc/supervisor/conf.d/supervisord.c# Acquia Cloud API config\n", "+render_tmpl \"$HOME_DIR/.acquia/cloudapi.conf\"\n", "+\n", "+# Terminus authentication\n", "+[[ \"$SECRET_TERMINUS_TOKEN\" ]] && terminus_login\n", "+\n", "+# Docker user uid/gid mapping to the host user uid/gid\n", "+[[ \"$HOST_UID\" != \"\" ]] && [[ \"$HOST_GID\" != \"\" ]] && uid_gid_reset\n", "+\n", "+# Enable xdebug\n", "+[[ \"$XDEBUG_ENABLED\" != \"\" ]] && [[ \"$XDEBUG_ENABLED\" != \"0\" ]] && xdebug_enable\n", "+\n", "+# Make sure permissions are correct (after uid/gid change and COPY operations in Dockerfile)\n", "+# To not bloat the image size, permissions on the home folder are reset at runtime.\n", "+echo-debug \"Resetting permissions on $HOME_DIR and /var/www...\"\n", "+chown \"$HOST_UID:$HOST_GID\" -R \"$HOME_DIR\"\n", "+# <PERSON><PERSON> resets the project root folder permissions to 0:0 when cli is recreated (e.g. an env variable updated).\n", "+# Why apply a fix/woraround for this at startup.\n", "+chown \"$HOST_UID:$HOST_GID\" /var/www\n", "+\n", "+# Initialization steps completed. Create a pid file to mark the container as healthy\n", "+echo-debug \"Preliminary initialization completed\"\n", "+touch /var/run/cli\n", "+\n", "+# Execute passed CMD arguments\n", "+echo-debug \"Executing the requested command...\"\n", "+# Service mode (run as root)\n", "+if [[ \"$1\" == \"supervisord\" ]]; then\n", " \texec gosu root supervisord -c /etc/supervisor/conf.d/supervisord.conf\n", " # Command mode (run as docker user)\n", " else\n", "\n", "\n", "========================================================================================================================\n", "Refactor startup script\n", "\n", "--- 7.2/startup.sh\n", "+++ 7.2/startup.sh\n", "@@ -1,3 +1,49 @@\n", "+#!/bin/bash\n", "+\n", "+# This script is running as root by default.\n", "+# Switching to the docker user can be done via \"gosu docker <command>\".\n", "+\n", "+HOME_DIR='/home/<USER>'\n", "+\n", "+DEBUG=${DEBUG:-0}\n", "+# Turn debugging ON when cli is started in the service mode\n", "+[[ \"$1\" == \"supervisord\" ]] && DEBUG=1\n", "+echo-debug ()\n", "+{\n", "+\t[[ \"$DEBUG\" != 0 ]] && echo \"$@\"\n", "+}\n", "+\n", "+uid_gid_reset ()\n", "+{\n", "+\tif [[ \"$HOST_UID\" != \"$(id -u docker)\" ]] || [[ \"$HOST_GID\" != \"$(id -g docker)\" ]]; then\n", "+\t\techo-debug \"Updating docker user uid/gid to $HOST_UID/$HOST_GID to match the host user uid/gid...\"\n", "+\t\tusermod -u \"$HOST_UID\" -o docker\n", "+\t\tgroupmod -g \"$HOST_GID\" -o \"$(id -gn docker)\"\n", "+\tfi\n", "+}\n", "+\n", "+xdebug_enable ()\n", "+{\n", "+\techo-debug \"Enabling xdebug...\"\n", "+\tsudo ln -s /opt/docker-php-ext-xdebug.ini /usr/local/etc/php/conf.d/\n", "+}\n", "+\n", "+# Helper function to render configs from go templates using gomplate\n", "+render_tmpl ()\n", "+{\n", "+\tlocal file=\"${1}\"\n", "+\tlocal tmpl=\"${1}.tmpl\"\n", "+\n", "+\tif [[ -f \"${tmpl}\" ]]; then\n", "+\t\techo-debug \"Rendering template: ${tmpl}...\"\n", "+\t\tgomplate --file \"${tmpl}\" --out \"${file}\"\n", "+\telse\n", "+\t\techo-debug \"Error: Template file not found: ${tmpl}\"\n", "+\t\treturn 1\n", "+\tfi\n", "+}\n", "+\n", "+terminus_login ()\n", " #!/bin/bash\n", " \n", " # This script is running as root by default.\n", "@@ -52,33 +98,7 @@\n", " # Process templates\n", " # Private SSH key\n", " render_tmpl \"$HOME_DIR/.ssh/id_rsa\"\n", "-# Acquia Cloud API config\n", "-render_tmpl \"$HOME_DIR/.acquia/cloudapi.conf\"\n", "-\n", "-# Terminus authentication\n", "-[[ \"$SECRET_TERMINUS_TOKEN\" ]] && terminus_login\n", "-\n", "-# Docker user uid/gid mapping to the host user uid/gid\n", "-[[ \"$HOST_UID\" != \"\" ]] && [[ \"$HOST_GID\" != \"\" ]] && uid_gid_reset\n", "-\n", "-# Enable xdebug\n", "-[[ \"$XDEBUG_ENABLED\" != \"\" ]] && [[ \"$XDEBUG_ENABLED\" != \"0\" ]] && xdebug_enable\n", "-\n", "-# Make sure permissions are correct (after uid/gid change and COPY operations in Dockerfile)\n", "-# To not bloat the image size, permissions on the home folder are reset at runtime.\n", "-echo-debug \"Resetting permissions on $HOME_DIR and /var/www...\"\n", "-chown \"$HOST_UID:$HOST_GID\" -R \"$HOME_DIR\"\n", "-# <PERSON><PERSON> resets the project root folder permissions to 0:0 when cli is recreated (e.g. an env variable updated).\n", "-# Why apply a fix/woraround for this at startup.\n", "-chown \"$HOST_UID:$HOST_GID\" /var/www\n", "-\n", "-# Initialization steps completed. Create a pid file to mark the container as healthy\n", "-echo-debug \"Preliminary initialization completed\"\n", "-touch /var/run/cli\n", "-\n", "-# Execute passed CMD arguments\n", "-echo-debug \"Executing the requested command...\"\n", "-# Service mode (run as root)\n", "+ mode (run as root)\n", " if [[ \"$1\" == \"supervisord\" ]]; then\n", " \texec gosu root supervisord -c /etc/supervisor/conf.d/supervisord.conf\n", " # Command mode (run as docker user)\n", "\n", "\n", "========================================================================================================================\n", "Set SSH key permissions\n", "\n", "--- 7.2/startup.sh\n", "+++ 7.2/startup.sh\n", "@@ -78,7 +78,8 @@\n", " \n", " # Execute passed CMD arguments\n", " echo-debug \"Executing the requested command...\"\n", "-# Service mode (run as root)\n", "+# Servicechmod 0600 \"$HOME_DIR/.ssh/id_rsa\"\n", "+ mode (run as root)\n", " if [[ \"$1\" == \"supervisord\" ]]; then\n", " \texec gosu root supervisord -c /etc/supervisor/conf.d/supervisord.conf\n", " # Command mode (run as docker user)\n", "\n", "\n", "========================================================================================================================\n", "Add startup scripts\n", "\n", "--- 7.2/startup.sh\n", "+++ 7.2/startup.sh\n", "@@ -78,6 +78,32 @@\n", " \n", " # Execute passed CMD arguments\n", " echo-debug \"Executing the requested command...\"\n", "+# Service# Acquia Cloud API config\n", "+render_tmpl \"$HOME_DIR/.acquia/cloudapi.conf\"\n", "+\n", "+# Terminus authentication\n", "+[[ \"$SECRET_TERMINUS_TOKEN\" ]] && terminus_login\n", "+\n", "+# Docker user uid/gid mapping to the host user uid/gid\n", "+[[ \"$HOST_UID\" != \"\" ]] && [[ \"$HOST_GID\" != \"\" ]] && uid_gid_reset\n", "+\n", "+# Enable xdebug\n", "+[[ \"$XDEBUG_ENABLED\" != \"\" ]] && [[ \"$XDEBUG_ENABLED\" != \"0\" ]] && xdebug_enable\n", "+\n", "+# Make sure permissions are correct (after uid/gid change and COPY operations in Dockerfile)\n", "+# To not bloat the image size, permissions on the home folder are reset at runtime.\n", "+echo-debug \"Resetting permissions on $HOME_DIR and /var/www...\"\n", "+chown \"$HOST_UID:$HOST_GID\" -R \"$HOME_DIR\"\n", "+# <PERSON><PERSON> resets the project root folder permissions to 0:0 when cli is recreated (e.g. an env variable updated).\n", "+# Why apply a fix/woraround for this at startup.\n", "+chown \"$HOST_UID:$HOST_GID\" /var/www\n", "+\n", "+# Initialization steps completed. Create a pid file to mark the container as healthy\n", "+echo-debug \"Preliminary initialization completed\"\n", "+touch /var/run/cli\n", "+\n", "+# Execute passed CMD arguments\n", "+echo-debug \"Executing the requested command...\"\n", " # Service mode (run as root)\n", " if [[ \"$1\" == \"supervisord\" ]]; then\n", " \texec gosu root supervisord -c /etc/supervisor/conf.d/supervisord.conf\n", "\n", "\n", "========================================================================================================================\n", "Set private key permissions\n", "\n", "--- 7.0/startup.sh\n", "+++ 7.0/startup.sh\n", "@@ -25,60 +25,61 @@\n", " xdebug_enable ()\n", " {\n", " \techo-debug \"Enabling xdebug...\"\n", " \tsudo ln -s /opt/docker-php-ext-xdebug.ini /usr/local/etc/php/conf.d/\n", " }\n", " \n", " # Helper function to render configs from go templates using gomplate\n", " render_tmpl ()\n", " {\n", " \tlocal file=\"${1}\"\n", " \tlocal tmpl=\"${1}.tmpl\"\n", " \n", " \tif [[ -f \"${tmpl}\" ]]; then\n", " \t\techo-debug \"Rendering template: ${tmpl}...\"\n", " \t\tgomplate --file \"${tmpl}\" --out \"${file}\"\n", " \telse\n", " \t\techo-debug \"Error: Template file not found: ${tmpl}\"\n", " \t\treturn 1\n", " \tfi\n", " }\n", " \n", " terminus_login ()\n", " {\n", " \techo-debug \"Authenticating with Pantheon...\"\n", " \tterminus auth:login --machine-token=\"$SECRET_TERMINUS_TOKEN\" > /dev/null\n", " }\n", " \n", " # Process templates\n", " # Private SSH key\n", " render_tmpl \"$HOME_DIR/.ssh/id_rsa\"\n", "+chmod 0600 \"$HOME_DIR/.ssh/id_rsa\"\n", " # Acquia Cloud API config\n", " render_tmpl \"$HOME_DIR/.acquia/cloudapi.conf\"\n", " \n", " # Terminus authentication\n", " [[ \"$SECRET_TERMINUS_TOKEN\" ]] && terminus_login\n", " \n", " # Docker user uid/gid mapping to the host user uid/gid\n", " [[ \"$HOST_UID\" != \"\" ]] && [[ \"$HOST_GID\" != \"\" ]] && uid_gid_reset\n", " \n", " # Enable xdebug\n", " [[ \"$XDEBUG_ENABLED\" != \"\" ]] && [[ \"$XDEBUG_ENABLED\" != \"0\" ]] && xdebug_enable\n", " \n", " # Make sure permissions are correct (after uid/gid change and COPY operations in Dockerfile)\n", " # To not bloat the image size, permissions on the home folder are reset at runtime.\n", " echo-debug \"Resetting permissions on $HOME_DIR and /var/www...\"\n", " chown \"$HOST_UID:$HOST_GID\" -R \"$HOME_DIR\"\n", " # Docker resets the project root folder permissions to 0:0 when cli is recreated (e.g. an env variable updated).\n", " # Why apply a fix/woraround for this at startup.\n", " chown \"$HOST_UID:$HOST_GID\" /var/www\n", " \n", " # Initialization steps completed. Create a pid file to mark the container as healthy\n", " echo-debug \"Preliminary initialization completed\"\n", " touch /var/run/cli\n", " \n", " # Execute passed CMD arguments\n", " echo-debug \"Executing the requested command...\"\n", " # Service mode (run as root)\n", " if [[ \"$1\" == \"supervisord\" ]]; then\n", " \texec gosu root supervisord -c /etc/supervisor/conf.d/supervisord.conf\n", " # Command mode (run as docker user)\n", "\n", "\n", "========================================================================================================================\n", "Remove redundant nodejs\n", "\n", "--- README.md\n", "+++ README.md\n", "@@ -34,13 +34,13 @@\n", "@- php\n", "   - wp-cli\n", " - ruby\n", "   - ruby\n", "   - gem\n", "   - bundler\n", " - nodejs\n", "-  - nodejs (via nvm)\n", "+  - nodejs\n", "   - npm, yarn\n", " - python\n", " \n", " Other notable tools:\n", " \n", " - git\n", "\n", "\n", "========================================================================================================================\n", "Refactor startup script\n", "\n", "--- 7.2/startup.sh\n", "+++ 7.2/startup.sh\n", "@@ -1,9 +1,59 @@\n", "+#!/bin/bash\n", "+\n", "+# This script is running as root by default.\n", "+# Switching to the docker user can be done via \"gosu docker <command>\".\n", "+\n", "+HOME_DIR='/home/<USER>'\n", "+\n", "+DEBUG=${DEBUG:-0}\n", "+# Turn debugging ON when cli is started in the service mode\n", "+[[ \"$1\" == \"supervisord\" ]] && DEBUG=1\n", "+echo-debug ()\n", "+{\n", "+\t[[ \"$DEBUG\" != 0 ]] && echo \"$(date +\"%F %H:%M:%S\") | $@\"\n", "+}\n", "+\n", "+uid_gid_reset ()\n", "+{\n", "+\tif [[ \"$HOST_UID\" != \"$(id -u docker)\" ]] || [[ \"$HOST_GID\" != \"$(id -g docker)\" ]]; then\n", "+\t\techo-debug \"Updating docker user uid/gid to $HOST_UID/$HOST_GID to match the host user uid/gid...\"\n", "+\t\tusermod -u \"$HOST_UID\" -o docker\n", "+\t\tgroupmod -g \"$HOST_GID\" -o \"$(id -gn docker)\"\n", "+\tfi\n", "+}\n", "+\n", "+xdebug_enable ()\n", "+{\n", "+\techo-debug \"Enabling xdebug...\"\n", "+\tsudo ln -s /opt/docker-php-ext-xdebug.ini /usr/local/etc/php/conf.d/\n", "+}\n", "+\n", "+# Helper function to render configs from go templates using gomplate\n", "+render_tmpl ()\n", "+{\n", "+\tlocal file=\"${1}\"\n", "+\tlocal tmpl=\"${1}.tmpl\"\n", "+\n", "+\tif [[ -f \"${tmpl}\" ]]; then\n", "+\t\techo-debug \"Rendering template: ${tmpl}...\"\n", "+\t\tgomplate --file \"${tmpl}\" --out \"${file}\"\n", "+\telse\n", "+\t\techo-debug \"Error: Template file not found: ${tmpl}\"\n", "+\t\treturn 1\n", "+\tfi\n", "+}\n", "+\n", "+terminus_login ()\n", "+{\n", "+\techo-debug \"Authenticating with Pantheon...\"\n", "+\tterminus auth:login --machine-token=\"$SECRET_TERMINUS_TOKEN\" >/dev/null 2>&1\n", "+}\n", " #!/bin/bash\n", " \n", " # This script is running as root by default.\n", " # Switching to the docker user can be done via \"gosu docker <command>\".\n", " \n", " HOME_DIR='/home/<USER>'\n", " \n", " DEBUG=${DEBUG:-0}\n", " # Turn debugging ON when cli is started in the service mode\n", "@@ -53,40 +103,14 @@\n", " # Private SSH key\n", " render_tmpl \"$HOME_DIR/.ssh/id_rsa\"\n", " chmod 0600 \"$HOME_DIR/.ssh/id_rsa\"\n", " # Acquia Cloud API config\n", " render_tmpl \"$HOME_DIR/.acquia/cloudapi.conf\"\n", " \n", " # Terminus authentication\n", " [[ \"$SECRET_TERMINUS_TOKEN\" ]] && terminus_login\n", " \n", "-# Docker user uid/gid mapping to the host user uid/gid\n", "-[[ \"$HOST_UID\" != \"\" ]] && [[ \"$HOST_GID\" != \"\" ]] && uid_gid_reset\n", "-\n", "-# Enable xdebug\n", "-[[ \"$XDEBUG_ENABLED\" != \"\" ]] && [[ \"$XDEBUG_ENABLED\" != \"0\" ]] && xdebug_enable\n", "-\n", "-# Make sure permissions are correct (after uid/gid change and COPY operations in Dockerfile)\n", "-# To not bloat the image size, permissions on the home folder are reset at runtime.\n", "-echo-debug \"Resetting permissions on $HOME_DIR and /var/www...\"\n", "-chown \"${HOST_UID-:1000}:${HOST_GID:-1000}\" -R \"$HOME_DIR\"\n", "-# <PERSON><PERSON> resets the project root folder permissions to 0:0 when cli is recreated (e.g. an env variable updated).\n", "-# We apply a fix/workaround for this at startup (non-recursive).\n", "-chown \"${HOST_UID-:1000}:${HOST_GID:-1000}\" /var/www\n", "-\n", "-# Initialization steps completed. Create a pid file to mark the container as healthy\n", "-echo-debug \"Preliminary initialization completed\"\n", "-touch /var/run/cli\n", "-\n", "-# Execute passed CMD arguments\n", "-echo-debug \"Executing the requested command...\"\n", "-# Service mode (run as root)\n", "-if [[ \"$1\" == \"supervisord\" ]]; then\n", "-\texec gosu root supervisord -c /etc/supervisor/supervisord.conf\n", "-# Command mode (run as docker user)\n", "-else\n", "-\t# This makes sure the environment is set up correctly for the docker user\n", "-\tDOCKSALRC='source $HOME/.docksalrc >/dev/null 2>&1'\n", "+e $HOME/.docksalrc >/dev/null 2>&1'\n", " \t# Launch the passed command in an non-interactive bash session under docker user\n", " \t# $@ does not work here. $* has to be used.\n", " \texec gosu docker bash -c \"$DOCKSALRC; exec $*\"\n", " fi\n", "\n", "\n", "========================================================================================================================\n", "Improve sourcing of docksalrc\n", "\n", "--- 7.2/startup.sh\n", "+++ 7.2/startup.sh\n", "@@ -79,14 +79,20 @@\n", " \n", " # Execute passed CMD arguments\n", " echo-debug \"Executing the requested command...\"\n", " # Service mode (run as root)\n", " if [[ \"$1\" == \"supervisord\" ]]; then\n", " \texec gosu root supervisord -c /etc/supervisor/supervisord.conf\n", " # Command mode (run as docker user)\n", " else\n", " \t# This makes sure the environment is set up correctly for the docker user\n", "-\tDOCKSALRC='source $HOME/.docksalrc >/dev/null 2>&1'\n", "+\tDOCKSALRC='sourc# Convert all Environment Variables Prefixed with SECRET_\n", "+convert_secrets\n", "+\n", "+# Source Docksalrc for when someone runs bash in the container\n", "+echo \"source ~/.docksalrc\" | sudo -u docker tee -a ~/.bashrc\n", "+\n", "+e $HOME/.docksalrc >/dev/null 2>&1'\n", " \t# Launch the passed command in an non-interactive bash session under docker user\n", " \t# $@ does not work here. $* has to be used.\n", " \texec gosu docker bash -c \"$DOCKSALRC; exec $*\"\n", " fi\n", "\n", "\n", "========================================================================================================================\n", "Add uid/gid mapping and xdebug enablement\n", "\n", "--- 7.2/startup.sh\n", "+++ 7.2/startup.sh\n", "@@ -79,14 +79,40 @@\n", " \n", " # Execute passed CMD arguments\n", " echo-debug \"Executing the requested command...\"\n", " # Service mode (run as root)\n", " if [[ \"$1\" == \"supervisord\" ]]; then\n", " \texec gosu root supervisord -c /etc/supervisor/supervisord.conf\n", " # Command mode (run as docker user)\n", " else\n", " \t# This makes sure the environment is set up correctly for the docker user\n", "+\tDOCKSALRC='sourc# Docker user uid/gid mapping to the host user uid/gid\n", "+[[ \"$HOST_UID\" != \"\" ]] && [[ \"$HOST_GID\" != \"\" ]] && uid_gid_reset\n", "+\n", "+# Enable xdebug\n", "+[[ \"$XDEBUG_ENABLED\" != \"\" ]] && [[ \"$XDEBUG_ENABLED\" != \"0\" ]] && xdebug_enable\n", "+\n", "+# Make sure permissions are correct (after uid/gid change and COPY operations in Dockerfile)\n", "+# To not bloat the image size, permissions on the home folder are reset at runtime.\n", "+echo-debug \"Resetting permissions on $HOME_DIR and /var/www...\"\n", "+chown \"${HOST_UID-:1000}:${HOST_GID:-1000}\" -R \"$HOME_DIR\"\n", "+# <PERSON><PERSON> resets the project root folder permissions to 0:0 when cli is recreated (e.g. an env variable updated).\n", "+# We apply a fix/workaround for this at startup (non-recursive).\n", "+chown \"${HOST_UID-:1000}:${HOST_GID:-1000}\" /var/www\n", "+\n", "+# Initialization steps completed. Create a pid file to mark the container as healthy\n", "+echo-debug \"Preliminary initialization completed\"\n", "+touch /var/run/cli\n", "+\n", "+# Execute passed CMD arguments\n", "+echo-debug \"Executing the requested command...\"\n", "+# Service mode (run as root)\n", "+if [[ \"$1\" == \"supervisord\" ]]; then\n", "+\texec gosu root supervisord -c /etc/supervisor/supervisord.conf\n", "+# Command mode (run as docker user)\n", "+else\n", "+\t# This makes sure the environment is set up correctly for the docker user\n", " \tDOCKSALRC='source $HOME/.docksalrc >/dev/null 2>&1'\n", " \t# Launch the passed command in an non-interactive bash session under docker user\n", " \t# $@ does not work here. $* has to be used.\n", " \texec gosu docker bash -c \"$DOCKSALRC; exec $*\"\n", " fi\n", "\n", "\n", "========================================================================================================================\n", "Add secret conversion and bash config\n", "\n", "--- 7.0/startup.sh\n", "+++ 7.0/startup.sh\n", "@@ -69,14 +69,20 @@\n", " chmod 0600 \"$HOME_DIR/.ssh/id_rsa\"\n", " # Acquia Cloud API config\n", " render_tmpl \"$HOME_DIR/.acquia/cloudapi.conf\"\n", " \n", " # Terminus authentication\n", " [[ \"$SECRET_TERMINUS_TOKEN\" ]] && terminus_login\n", " \n", "+# Convert all Environment Variables Prefixed with SECRET_\n", "+convert_secrets\n", "+\n", "+# Source Docksalrc for when someone runs bash in the container\n", "+echo \"source ~/.docksalrc\" | sudo -u docker tee -a ~/.bashrc\n", "+\n", " # Docker user uid/gid mapping to the host user uid/gid\n", " [[ \"$HOST_UID\" != \"\" ]] && [[ \"$HOST_GID\" != \"\" ]] && uid_gid_reset\n", " \n", " # Enable xdebug\n", " [[ \"$XDEBUG_ENABLED\" != \"\" ]] && [[ \"$XDEBUG_ENABLED\" != \"0\" ]] && xdebug_enable\n", " \n", " # Make sure permissions are correct (after uid/gid change and COPY operations in Dockerfile)\n", "\n", "\n", "========================================================================================================================\n", "Extract and refactor startup script\n", "\n", "--- 7.1/startup.sh\n", "+++ 7.1/startup.sh\n", "@@ -1,6 +1,35 @@\n", "+#!/bin/bash\n", "+\n", "+# This script is running as root by default.\n", "+# Switching to the docker user can be done via \"gosu docker <command>\".\n", "+\n", "+HOME_DIR='/home/<USER>'\n", "+\n", "+DEBUG=${DEBUG:-0}\n", "+# Turn debugging ON when cli is started in the service mode\n", "+[[ \"$1\" == \"supervisord\" ]] && DEBUG=1\n", "+echo-debug ()\n", "+{\n", "+\t[[ \"$DEBUG\" != 0 ]] && echo \"$(date +\"%F %H:%M:%S\") | $@\"\n", "+}\n", "+\n", "+uid_gid_reset ()\n", "+{\n", "+\tif [[ \"$HOST_UID\" != \"$(id -u docker)\" ]] || [[ \"$HOST_GID\" != \"$(id -g docker)\" ]]; then\n", "+\t\techo-debug \"Updating docker user uid/gid to $HOST_UID/$HOST_GID to match the host user uid/gid...\"\n", "+\t\tusermod -u \"$HOST_UID\" -o docker\n", "+\t\tgroupmod -g \"$HOST_GID\" -o \"$(id -gn docker)\"\n", "+\tfi\n", "+}\n", "+\n", "+xdebug_enable ()\n", "+{\n", "+\techo-debug \"Enabling xdebug...\"\n", "+\tsudo ln -s /opt/docker-php-ext-xdebug.ini /usr/local/etc/php/conf.d/\n", "+}\n", " #!/bin/bash\n", " \n", " # This script is running as root by default.\n", " # Switching to the docker user can be done via \"gosu docker <command>\".\n", " \n", " HOME_DIR='/home/<USER>'\n", "@@ -40,36 +69,13 @@\n", "@render_tmpl ()\n", " \telse\n", " \t\techo-debug \"Error: Template file not found: ${tmpl}\"\n", " \t\treturn 1\n", " \tfi\n", " }\n", " \n", "-terminus_login ()\n", "-{\n", "-\techo-debug \"Authenticating with Pantheon...\"\n", "-\tterminus auth:login --machine-token=\"$SECRET_TERMINUS_TOKEN\" >/dev/null 2>&1\n", "-}\n", "-\n", "-# Process templates\n", "-# Private SSH key\n", "-render_tmpl \"$HOME_DIR/.ssh/id_rsa\"\n", "-chmod 0600 \"$HOME_DIR/.ssh/id_rsa\"\n", "-# Acquia Cloud API config\n", "-render_tmpl \"$HOME_DIR/.acquia/cloudapi.conf\"\n", "-\n", "-# Terminus authentication\n", "-[[ \"$SECRET_TERMINUS_TOKEN\" ]] && terminus_login\n", "-\n", "-# Docker user uid/gid mapping to the host user uid/gid\n", "-[[ \"$HOST_UID\" != \"\" ]] && [[ \"$HOST_GID\" != \"\" ]] && uid_gid_reset\n", "-\n", "-# Enable xdebug\n", "-[[ \"$XDEBUG_ENABLED\" != \"\" ]] && [[ \"$XDEBUG_ENABLED\" != \"0\" ]] && xdebug_enable\n", "-\n", "-# Make sure permissions are correct (after uid/gid change and COPY operations in Dockerfile)\n", "-# To not bloat the image size, permissions on the home folder are reset at runtime.\n", "+older are reset at runtime.\n", " echo-debug \"Resetting permissions on $HOME_DIR and /var/www...\"\n", " chown \"${HOST_UID-:1000}:${HOST_GID:-1000}\" -R \"$HOME_DIR\"\n", " # Docker resets the project root folder permissions to 0:0 when cli is recreated (e.g. an env variable updated).\n", " # We apply a fix/workaround for this at startup (non-recursive).\n", " chown \"${HOST_UID-:1000}:${HOST_GID:-1000}\" /var/www\n", " \n", "\n", "\n", "========================================================================================================================\n", "Add secret conversion function\n", "\n", "--- 7.1/startup.sh\n", "+++ 7.1/startup.sh\n", "@@ -63,13 +63,27 @@\n", " [[ \"$HOST_UID\" != \"\" ]] && [[ \"$HOST_GID\" != \"\" ]] && uid_gid_reset\n", " \n", " # Enable xdebug\n", " [[ \"$XDEBUG_ENABLED\" != \"\" ]] && [[ \"$XDEBUG_ENABLED\" != \"0\" ]] && xdebug_enable\n", " \n", " # Make sure permissions are correct (after uid/gid change and COPY operations in Dockerfile)\n", "-# To not bloat the image size, permissions on the home folder are reset at runtime.\n", "+# To not bloat the image size, permissions on the home f# Helper function to loop through all environment variables prefixed with SECRET_ and\n", "+# convert to the equivalent variable without SECRET. (ex SECRET_TERMINUS_TOKEN has a variable\n", "+# called TERMINUS_TOKEN.\n", "+convert_secrets ()\n", "+{\n", "+\teval 'secrets=(${!SECRET_@})'\n", "+\tfor secret_key in \"${secrets[@]}\"\n", "+\tdo\n", "+\t\tsecret_value=${!secret_key}\n", "+\t\tkey=${secret_key#SECRET_}\n", "+\t\techo \"export ${key}=\\\"${secret_value}\\\";\" | sudo -u docker tee -a ~/.docksalrc\n", "+\tdone\n", "+}\n", "+\n", "+older are reset at runtime.\n", " echo-debug \"Resetting permissions on $HOME_DIR and /var/www...\"\n", " chown \"${HOST_UID-:1000}:${HOST_GID:-1000}\" -R \"$HOME_DIR\"\n", " # Docker resets the project root folder permissions to 0:0 when cli is recreated (e.g. an env variable updated).\n", " # We apply a fix/workaround for this at startup (non-recursive).\n", " chown \"${HOST_UID-:1000}:${HOST_GID:-1000}\" /var/www\n", " \n", "\n", "\n", "========================================================================================================================\n", "Replace permission reset with terminus login\n", "\n", "--- 7.1/startup.sh\n", "+++ 7.1/startup.sh\n", "@@ -63,20 +63,29 @@\n", " [[ \"$HOST_UID\" != \"\" ]] && [[ \"$HOST_GID\" != \"\" ]] && uid_gid_reset\n", " \n", " # Enable xdebug\n", " [[ \"$XDEBUG_ENABLED\" != \"\" ]] && [[ \"$XDEBUG_ENABLED\" != \"0\" ]] && xdebug_enable\n", " \n", " # Make sure permissions are correct (after uid/gid change and COPY operations in Dockerfile)\n", "-# To not bloat the image size, permissions on the home folder are reset at runtime.\n", "-echo-debug \"Resetting permissions on $HOME_DIR and /var/www...\"\n", "-chown \"${HOST_UID-:1000}:${HOST_GID:-1000}\" -R \"$HOME_DIR\"\n", "-# <PERSON><PERSON> resets the project root folder permissions to 0:0 when cli is recreated (e.g. an env variable updated).\n", "-# We apply a fix/workaround for this at startup (non-recursive).\n", "-chown \"${HOST_UID-:1000}:${HOST_GID:-1000}\" /var/www\n", "+# To not bloat the image size, permissions on the home fterminus_login ()\n", "+{\n", "+\techo-debug \"Authenticating with Pantheon...\"\n", "+\tterminus auth:login --machine-token=\"$SECRET_TERMINUS_TOKEN\" >/dev/null 2>&1\n", "+}\n", " \n", "-# Initialization steps completed. Create a pid file to mark the container as healthy\n", "+# Process templates\n", "+# Private SSH key\n", "+render_tmpl \"$HOME_DIR/.ssh/id_rsa\"\n", "+chmod 0600 \"$HOME_DIR/.ssh/id_rsa\"\n", "+# Acquia Cloud API config\n", "+render_tmpl \"$HOME_DIR/.acquia/cloudapi.conf\"\n", "+\n", "+# Terminus authentication\n", "+[[ \"$SECRET_TERMINUS_TOKEN\" ]] && terminus_login\n", "+\n", "+ completed. Create a pid file to mark the container as healthy\n", " echo-debug \"Preliminary initialization completed\"\n", " touch /var/run/cli\n", " \n", " # Execute passed CMD arguments\n", " echo-debug \"Executing the requested command...\"\n", " # Service mode (run as root)\n", "\n", "\n", "========================================================================================================================\n", "Add initialization steps\n", "\n", "--- 7.1/startup.sh\n", "+++ 7.1/startup.sh\n", "@@ -70,13 +70,19 @@\n", " echo-debug \"Resetting permissions on $HOME_DIR and /var/www...\"\n", " chown \"${HOST_UID-:1000}:${HOST_GID:-1000}\" -R \"$HOME_DIR\"\n", " # Docker resets the project root folder permissions to 0:0 when cli is recreated (e.g. an env variable updated).\n", " # We apply a fix/workaround for this at startup (non-recursive).\n", " chown \"${HOST_UID-:1000}:${HOST_GID:-1000}\" /var/www\n", " \n", "-# Initialization steps completed. Create a pid file to mark the container as healthy\n", "+# Initialization steps# Convert all Environment Variables Prefixed with SECRET_\n", "+convert_secrets\n", "+\n", "+# Source Docksalrc for when someone runs bash in the container\n", "+echo \"source ~/.docksalrc\" | sudo -u docker tee -a ~/.bashrc\n", "+\n", "+ completed. Create a pid file to mark the container as healthy\n", " echo-debug \"Preliminary initialization completed\"\n", " touch /var/run/cli\n", " \n", " # Execute passed CMD arguments\n", " echo-debug \"Executing the requested command...\"\n", " # Service mode (run as root)\n", "\n", "\n", "========================================================================================================================\n", "Extract and refactor initialization steps\n", "\n", "--- 7.1/startup.sh\n", "+++ 7.1/startup.sh\n", "@@ -70,12 +70,26 @@\n", " echo-debug \"Resetting permissions on $HOME_DIR and /var/www...\"\n", " chown \"${HOST_UID-:1000}:${HOST_GID:-1000}\" -R \"$HOME_DIR\"\n", " # Docker resets the project root folder permissions to 0:0 when cli is recreated (e.g. an env variable updated).\n", " # We apply a fix/workaround for this at startup (non-recursive).\n", " chown \"${HOST_UID-:1000}:${HOST_GID:-1000}\" /var/www\n", " \n", "+# Initialization steps# Docker user uid/gid mapping to the host user uid/gid\n", "+[[ \"$HOST_UID\" != \"\" ]] && [[ \"$HOST_GID\" != \"\" ]] && uid_gid_reset\n", "+\n", "+# Enable xdebug\n", "+[[ \"$XDEBUG_ENABLED\" != \"\" ]] && [[ \"$XDEBUG_ENABLED\" != \"0\" ]] && xdebug_enable\n", "+\n", "+# Make sure permissions are correct (after uid/gid change and COPY operations in Dockerfile)\n", "+# To not bloat the image size, permissions on the home folder are reset at runtime.\n", "+echo-debug \"Resetting permissions on $HOME_DIR and /var/www...\"\n", "+chown \"${HOST_UID-:1000}:${HOST_GID:-1000}\" -R \"$HOME_DIR\"\n", "+# <PERSON><PERSON> resets the project root folder permissions to 0:0 when cli is recreated (e.g. an env variable updated).\n", "+# We apply a fix/workaround for this at startup (non-recursive).\n", "+chown \"${HOST_UID-:1000}:${HOST_GID:-1000}\" /var/www\n", "+\n", " # Initialization steps completed. Create a pid file to mark the container as healthy\n", " echo-debug \"Preliminary initialization completed\"\n", " touch /var/run/cli\n", " \n", " # Execute passed CMD arguments\n", " echo-debug \"Executing the requested command...\"\n", "\n", "\n", "========================================================================================================================\n", "Add documentation for CLI Docker image\n", "\n", "--- README.md\n", "+++ README.md\n", "@@ -97,13 +97,117 @@\n", " `SECRET_ACAPI_EMAIL` and `SECRET_ACAPI_KEY`\n", " \n", " Credentials used to authenticate with [Acquia Cloud API](https://docs.acquia.com/acquia-cloud/api).  \n", " Stored in `/home/<USER>/.acquia/cloudapi.conf` inside `cli`. \n", " \n", " Acquia Cloud API can be used via `ac-<command>` group of commands in Drush.\n", " \n", " `SECRET_TERMINUS_TOKEN`\n", "+# CLI Docker image for Docksal\n", "+\n", "+This image is focused on console tools necessary to develop LAMP stack applications.\n", "+\n", "+This image(s) is part of the [Docksal](http://docksal.io) image library.\n", "+\n", "+\n", "+## Versions and image tag naming convention\n", "+\n", "+- Stable versions\n", "+  - `2.0-php5.6`, `php5.6` - PHP 5.6\n", "+  - `2.0-php7.0`, `php7.0` - PHP 7.0\n", "+  - `2.0-php7.1`, `php7.1` - PHP 7.1\n", "+  - `2.0-php7.2`, `php7.2`, `latest` - PHP 7.2\n", "+- Development versions\n", "+  - `edge-php5.6` - PHP 5.6\n", "+  - `edge-php7.0` - PHP 7.0\n", "+  - `edge-php7.1` - PHP 7.1\n", "+  - `edge-php7.2` - PHP 7.2\n", "+\n", "+\n", "+## Includes\n", "+\n", "+- php\n", "+  - php-fpm && php-cli\n", "+  - xdebug\n", "+  - composer\n", "+  - drush\n", "+    - registry_rebuild\n", "+    - coder-8.x + phpcs\n", "+    - Acquia Cloud API commands\n", "+  - drupal console launcher\n", "+  - terminus (Pantheon)\n", "+  - platform (Platform.sh)\n", "+  - wp-cli\n", "+- ruby\n", "+  - ruby\n", "+  - gem\n", "+  - bundler\n", "+- nodejs\n", "+  - nodejs\n", "+  - npm, yarn\n", "+- python\n", "+\n", "+Other notable tools:\n", "+\n", "+- git\n", "+- curl/wget\n", "+- zip/unzip\n", "+- mysql-client\n", "+- imagemagick\n", "+- mc\n", "+- mhs<PERSON><PERSON>\n", "+\n", "+\n", "+## PHP database drivers support\n", "+\n", "+- SQLite - via `sqlite3`, `pdo_sqlite`\n", "+- MySQL - via `mysqli`, `mysqlnd`, `pdo_mysql`\n", "+- PostgreSQL - via `pgsql`, `pdo_pgsql`\n", "+- MSSQL - via `mssql` and `pdo_dblib` for PHP 5.6; `sqlsrv` and `pdo_sqlsrv` for PHP 7.0+\n", "+\n", "+\n", "+## Xdebug\n", "+\n", "+Xdebug is disabled by default.\n", "+\n", "+To enable it, run the image with `XDEBUG_ENABLED=1`:\n", "+\n", "+```yml\n", "+cli\n", "+...\n", "+  environment:\n", "+    ...\n", "+    - XDEBUG_ENABLED=1\n", "+    ...\n", "+```\n", "+\n", "+See [docs](https://docs.docksal.io/en/master/tools/xdebug) on using Xdebug for web and cli PHP debugging.\n", "+\n", "+\n", "+## Secrets and integrations\n", "+\n", "+`cli` can read secrets from environment variables and configure the respective integrations automatically at start.  \n", "+\n", "+The recommended place store secrets in Docksal is the global `$HOME/.docksal/docksal.env` file on the host. From there, \n", "+secrets are injected into the `cli` container's environment.\n", "+\n", "+Below is the list of secrets currently supported.\n", "+\n", "+`SECRET_SSH_PRIVATE_KEY`\n", "+\n", "+Use to pass a private SSH key. The key is stored in `/home/<USER>/.ssh/id_rsa` inside `cli` and will be considered \n", "+by the SSH client **in addition** to the keys loaded in `docksal-ssh-agent` when establishing a SSH connection \n", "+from within `cli`.\n", "+\n", "+`SECRET_ACAPI_EMAIL` and `SECRET_ACAPI_KEY`\n", "+\n", "+Credentials used to authenticate with [Acquia Cloud API](https://docs.acquia.com/acquia-cloud/api).  \n", "+Stored in `/home/<USER>/.acquia/cloudapi.conf` inside `cli`. \n", "+\n", "+Acquia Cloud API can be used via `ac-<command>` group of commands in Drush.\n", "+\n", "+`SECRET_TERMINUS_TOKEN`\n", " \n", " Credentials used to authenticate [<PERSON>rminus](https://pantheon.io/docs/terminus) with Pantheon.\n", " Stored in `/home/<USER>/.terminus/` inside `cli`.\n", " \n", " Terminus is installed and available globally in `cli`.\n", "\n", "\n", "========================================================================================================================\n", "Add Platform.sh CLI token\n", "\n", "--- README.md\n", "+++ README.md\n", "@@ -102,8 +102,15 @@\n", " Acquia Cloud API can be used via `ac-<command>` group of commands in Drush.\n", " \n", " `SECRET_TERMINUS_TOKEN`\n", " \n", " Credentials used to authenticate [<PERSON>rminus](https://pantheon.io/docs/terminus) with Pantheon.\n", " Stored in `/home/<USER>/.terminus/` inside `cli`.\n", " \n", " Terminus is installed and available globally in `cli`.\n", "+\n", "+`SECRET_PLATFORMSH_CLI_TOKEN`\n", "+\n", "+Credentials used to authenticate with the [Platform.sh CLI](https://github.com/platformsh/platformsh-cli) tool.\n", "+Stored in `/home/<USER>/.platform` inside `cli`.\n", "+\n", "+Platform CLI is installed and available globally in `cli`.\n", "\n", "\n", "========================================================================================================================\n", "Add shebang line\n", "\n", "--- 5.6/startup.sh\n", "+++ 5.6/startup.sh\n", "@@ -1,81 +1,84 @@\n", "+#!/bin/bash\n", "+\n", "+# This script is running as root by default.\n", "+# Switching to the docker user can be done via \"gosu docker <command>\".\n", "+\n", "+HOME_DIR='/home/<USER>'\n", "+\n", " #!/bin/bash\n", " \n", " # This script is running as root by default.\n", " # Switching to the docker user can be done via \"gosu docker <command>\".\n", " \n", " HOME_DIR='/home/<USER>'\n", " \n", " DEBUG=${DEBUG:-0}\n", " # Turn debugging ON when cli is started in the service mode\n", " [[ \"$1\" == \"supervisord\" ]] && DEBUG=1\n", " echo-debug ()\n", " {\n", " \t[[ \"$DEBUG\" != 0 ]] && echo \"$(date +\"%F %H:%M:%S\") | $@\"\n", " }\n", " \n", " uid_gid_reset ()\n", " {\n", " \tif [[ \"$HOST_UID\" != \"$(id -u docker)\" ]] || [[ \"$HOST_GID\" != \"$(id -g docker)\" ]]; then\n", " \t\techo-debug \"Updating docker user uid/gid to $HOST_UID/$HOST_GID to match the host user uid/gid...\"\n", " \t\tusermod -u \"$HOST_UID\" -o docker\n", " \t\tgroupmod -g \"$HOST_GID\" -o \"$(id -gn docker)\"\n", " \tfi\n", " }\n", " \n", " xdebug_enable ()\n", " {\n", " \techo-debug \"Enabling xdebug...\"\n", " \tsudo ln -s /opt/docker-php-ext-xdebug.ini /usr/local/etc/php/conf.d/\n", " }\n", " \n", " # Helper function to render configs from go templates using gomplate\n", " render_tmpl ()\n", " {\n", " \tlocal file=\"${1}\"\n", " \tlocal tmpl=\"${1}.tmpl\"\n", " \n", " \tif [[ -f \"${tmpl}\" ]]; then\n", " \t\techo-debug \"Rendering template: ${tmpl}...\"\n", " \t\tgomplate --file \"${tmpl}\" --out \"${file}\"\n", " \telse\n", " \t\techo-debug \"Error: Template file not found: ${tmpl}\"\n", " \t\treturn 1\n", " \tfi\n", " }\n", " \n", " # Helper function to loop through all environment variables prefixed with SECRET_ and\n", "-# convert to the equivalent variable without SECRET. (ex SECRET_TERMINUS_TOKEN has a variable\n", "-# called TERMINUS_TOKEN.\n", "-convert_secrets ()\n", "-{\n", "-\teval 'secrets=(${!SECRET_@})'\n", "+${!SECRET_@})'\n", " \tfor secret_key in \"${secrets[@]}\"\n", " \tdo\n", " \t\tsecret_value=${!secret_key}\n", " \t\tkey=${secret_key#SECRET_}\n", " \t\techo \"export ${key}=\\\"${secret_value}\\\";\" | sudo -u docker tee -a ~/.docksalrc\n", " \tdone\n", " }\n", " \n", " # Pantheon authentication\n", " terminus_login ()\n", " {\n", " \techo-debug \"Authenticating with Pantheon...\"\n", " \t# This has to be done using the docker user via su to load the user environment\n", " \t# Note: 'su -' = 'su -l'login'\n", " \tlocal output\n", " \toutput=$(sudo su - docker -c \"terminus auth:login --machine-token=\"$SECRET_TERMINUS_TOKEN\" >/dev/null 2>&1)\n", " \t #>/dev/null 2>&1\n", " \tif [[ $? != 0 ]]; then\n", " \t\techo-debug \"ERROR: Pantheon authentication failed.\"\n", " \t\techo\n", " \t\techo \"$output\"\n", " \t\techo\n", " \tfi\n", " }\n", " \n", " # Process templates\n", " # Private SSH key\n", " render_tmpl \"$HOME_DIR/.ssh/id_rsa\"\n", " chmod 0600 \"$HOME_DIR/.ssh/id_rsa\"\n", " # Acquia Cloud API config\n", "\n", "\n", "========================================================================================================================\n", "Simplify secret conversion\n", "\n", "--- 5.6/startup.sh\n", "+++ 5.6/startup.sh\n", "@@ -21,66 +21,63 @@\n", "@uid_gid_reset ()\n", " \t\tgroupmod -g \"$HOST_GID\" -o \"$(id -gn docker)\"\n", " \tfi\n", " }\n", " \n", " xdebug_enable ()\n", " {\n", " \techo-debug \"Enabling xdebug...\"\n", " \tsudo ln -s /opt/docker-php-ext-xdebug.ini /usr/local/etc/php/conf.d/\n", " }\n", " \n", " # Helper function to render configs from go templates using gomplate\n", " render_tmpl ()\n", " {\n", " \tlocal file=\"${1}\"\n", " \tlocal tmpl=\"${1}.tmpl\"\n", " \n", " \tif [[ -f \"${tmpl}\" ]]; then\n", " \t\techo-debug \"Rendering template: ${tmpl}...\"\n", " \t\tgomplate --file \"${tmpl}\" --out \"${file}\"\n", " \telse\n", " \t\techo-debug \"Error: Template file not found: ${tmpl}\"\n", " \t\treturn 1\n", " \tfi\n", " }\n", " \n", " # Helper function to loop through all environment variables prefixed with SECRET_ and\n", " # convert to the equivalent variable without SECRET. (ex SECRET_TERMINUS_TOKEN has a variable\n", " # called TERMINUS_TOKEN.\n", " convert_secrets ()\n", " {\n", "-\teval 'secrets=(${!SECRET_@})'\n", "-\tfor secret_key in \"${secrets[@]}\"\n", "-\tdo\n", "-\t\tsecret_value=${!secret_key}\n", "-\t\tkey=${secret_key#SECRET_}\n", "-\t\techo \"export ${key}=\\\"${secret_value}\\\";\" | sudo -u docker tee -a ~/.docksalrc\n", "+\teval 'secrets=(# convert to the equivalent variable without SECRET.\n", "+# Example: SECRET_TERMINUS_TOKEN => TERMINUS_TOKEN.\n", "+\"export ${key}=\\\"${secret_value}\\\";\" | sudo -u docker tee -a ~/.docksalrc\n", " \tdone\n", " }\n", " \n", " # Pantheon authentication\n", " terminus_login ()\n", " {\n", " \techo-debug \"Authenticating with Pantheon...\"\n", " \t# This has to be done using the docker user via su to load the user environment\n", " \t# Note: 'su -' = 'su -l'login'\n", " \tlocal output\n", " \toutput=$(sudo su - docker -c \"terminus auth:login --machine-token=\"$SECRET_TERMINUS_TOKEN\" >/dev/null 2>&1)\n", " \t #>/dev/null 2>&1\n", " \tif [[ $? != 0 ]]; then\n", " \t\techo-debug \"ERROR: Pantheon authentication failed.\"\n", " \t\techo\n", " \t\techo \"$output\"\n", " \t\techo\n", " \tfi\n", " }\n", " \n", " # Process templates\n", " # Private SSH key\n", " render_tmpl \"$HOME_DIR/.ssh/id_rsa\"\n", " chmod 0600 \"$HOME_DIR/.ssh/id_rsa\"\n", " # Acquia Cloud API config\n", " render_tmpl \"$HOME_DIR/.acquia/cloudapi.conf\"\n", " \n", " # Terminus authentication\n", " [[ \"$SECRET_TERMINUS_TOKEN\" ]] && terminus_login\n", " \n", "\n", "\n", "========================================================================================================================\n", "Remove unnecessary echo statement\n", "\n", "--- 5.6/startup.sh\n", "+++ 5.6/startup.sh\n", "@@ -26,61 +26,64 @@\n", "@xdebug_enable ()\n", " {\n", " \techo-debug \"Enabling xdebug...\"\n", " \tsudo ln -s /opt/docker-php-ext-xdebug.ini /usr/local/etc/php/conf.d/\n", " }\n", " \n", " # Helper function to render configs from go templates using gomplate\n", " render_tmpl ()\n", " {\n", " \tlocal file=\"${1}\"\n", " \tlocal tmpl=\"${1}.tmpl\"\n", " \n", " \tif [[ -f \"${tmpl}\" ]]; then\n", " \t\techo-debug \"Rendering template: ${tmpl}...\"\n", " \t\tgomplate --file \"${tmpl}\" --out \"${file}\"\n", " \telse\n", " \t\techo-debug \"Error: Template file not found: ${tmpl}\"\n", " \t\treturn 1\n", " \tfi\n", " }\n", " \n", " # Helper function to loop through all environment variables prefixed with SECRET_ and\n", " # convert to the equivalent variable without SECRET. (ex SECRET_TERMINUS_TOKEN has a variable\n", " # called TERMINUS_TOKEN.\n", " convert_secrets ()\n", " {\n", " \teval 'secrets=(${!SECRET_@})'\n", " \tfor secret_key in \"${secrets[@]}\"\n", " \tdo\n", " \t\tsecret_value=${!secret_key}\n", " \t\tkey=${secret_key#SECRET_}\n", "-\t\techo \"export ${key}=\\\"${secret_value}\\\";\" | sudo -u docker tee -a ~/.docksalrc\n", "+\t\techo convert_secrets ()\n", "+{\n", "+\teval 'secrets=(${!SECRET_@})'\n", "+r tee -a ~/.docksalrc\n", " \tdone\n", " }\n", " \n", " # Pantheon authentication\n", " terminus_login ()\n", " {\n", " \techo-debug \"Authenticating with Pantheon...\"\n", " \t# This has to be done using the docker user via su to load the user environment\n", " \t# Note: 'su -' = 'su -l'login'\n", " \tlocal output\n", " \toutput=$(sudo su - docker -c \"terminus auth:login --machine-token=\"$SECRET_TERMINUS_TOKEN\" >/dev/null 2>&1)\n", " \t #>/dev/null 2>&1\n", " \tif [[ $? != 0 ]]; then\n", " \t\techo-debug \"ERROR: Pantheon authentication failed.\"\n", " \t\techo\n", " \t\techo \"$output\"\n", " \t\techo\n", " \tfi\n", " }\n", " \n", " # Process templates\n", " # Private SSH key\n", " render_tmpl \"$HOME_DIR/.ssh/id_rsa\"\n", " chmod 0600 \"$HOME_DIR/.ssh/id_rsa\"\n", " # Acquia Cloud API config\n", " render_tmpl \"$HOME_DIR/.acquia/cloudapi.conf\"\n", " \n", " # Terminus authentication\n", " [[ \"$SECRET_TERMINUS_TOKEN\" ]] && terminus_login\n", " \n", "\n", "\n", "========================================================================================================================\n", "Move export to /etc/profile.d/secrets.sh\n", "\n", "--- 5.6/startup.sh\n", "+++ 5.6/startup.sh\n", "@@ -26,69 +26,71 @@\n", "@xdebug_enable ()\n", " {\n", " \techo-debug \"Enabling xdebug...\"\n", " \tsudo ln -s /opt/docker-php-ext-xdebug.ini /usr/local/etc/php/conf.d/\n", " }\n", " \n", " # Helper function to render configs from go templates using gomplate\n", " render_tmpl ()\n", " {\n", " \tlocal file=\"${1}\"\n", " \tlocal tmpl=\"${1}.tmpl\"\n", " \n", " \tif [[ -f \"${tmpl}\" ]]; then\n", " \t\techo-debug \"Rendering template: ${tmpl}...\"\n", " \t\tgomplate --file \"${tmpl}\" --out \"${file}\"\n", " \telse\n", " \t\techo-debug \"Error: Template file not found: ${tmpl}\"\n", " \t\treturn 1\n", " \tfi\n", " }\n", " \n", " # Helper function to loop through all environment variables prefixed with SECRET_ and\n", " # convert to the equivalent variable without SECRET. (ex SECRET_TERMINUS_TOKEN has a variable\n", " # called TERMINUS_TOKEN.\n", " convert_secrets ()\n", " {\n", " \teval 'secrets=(${!SECRET_@})'\n", " \tfor secret_key in \"${secrets[@]}\"\n", " \tdo\n", " \t\tsecret_value=${!secret_key}\n", " \t\tkey=${secret_key#SECRET_}\n", "-\t\techo \"export ${key}=\\\"${secret_value}\\\";\" | sudo -u docker tee -a ~/.docksalrc\n", "-\tdone\n", "-}\n", "+\t\techo \"export ${key}=\\\"${secret_value}\\\";\" | sudo -u docke\tfor secret_key in \"${secrets[@]}\"; do\n", "+\t\tkey=${secret_key#SECRET_}\n", "+\t\tsecret_value=${!secret_key}\n", " \n", "-# Pantheon authentication\n", "-terminus_login ()\n", "-{\n", "-\techo-debug \"Authenticating with Pantheon...\"\n", "-\t# This has to be done using the docker user via su to load the user environment\n", "+\t\t# Write new variables to /etc/profile.d/secrets.sh to make them available for all users/sessions\n", "+\t\techo \"export ${key}=\\\"${secret_value}\\\"\" | tee -a \"/etc/profile.d/secrets.sh\" >/dev/null\n", "+\n", "+\t\t# Also export new variables here\n", "+\t\t# This makes them available in the server/php-fpm environment\n", "+\t\teval \"export ${key}=${secret_value}\"\n", "+load the user environment\n", " \t# Note: 'su -' = 'su -l'login'\n", " \tlocal output\n", " \toutput=$(sudo su - docker -c \"terminus auth:login --machine-token=\"$SECRET_TERMINUS_TOKEN\" >/dev/null 2>&1)\n", " \t #>/dev/null 2>&1\n", " \tif [[ $? != 0 ]]; then\n", " \t\techo-debug \"ERROR: Pantheon authentication failed.\"\n", " \t\techo\n", " \t\techo \"$output\"\n", " \t\techo\n", " \tfi\n", " }\n", " \n", " # Process templates\n", " # Private SSH key\n", " render_tmpl \"$HOME_DIR/.ssh/id_rsa\"\n", " chmod 0600 \"$HOME_DIR/.ssh/id_rsa\"\n", " # Acquia Cloud API config\n", " render_tmpl \"$HOME_DIR/.acquia/cloudapi.conf\"\n", " \n", " # Terminus authentication\n", " [[ \"$SECRET_TERMINUS_TOKEN\" ]] && terminus_login\n", " \n", " # Convert all Environment Variables Prefixed with SECRET_\n", " convert_secrets\n", " \n", " # Source Docksalrc for when someone runs bash in the container\n", " echo \"source ~/.docksalrc\" | sudo -u docker tee -a ~/.bashrc\n", " \n", " # Docker user uid/gid mapping to the host user uid/gid\n", " [[ \"$HOST_UID\" != \"\" ]] && [[ \"$HOST_GID\" != \"\" ]] && uid_gid_reset\n", "\n", "\n", "========================================================================================================================\n", "Remove duplicate function definition\n", "\n", "--- 5.6/startup.sh\n", "+++ 5.6/startup.sh\n", "@@ -34,65 +34,69 @@\n", "@render_tmpl ()\n", " \tlocal file=\"${1}\"\n", " \tlocal tmpl=\"${1}.tmpl\"\n", " \n", " \tif [[ -f \"${tmpl}\" ]]; then\n", " \t\techo-debug \"Rendering template: ${tmpl}...\"\n", " \t\tgomplate --file \"${tmpl}\" --out \"${file}\"\n", " \telse\n", " \t\techo-debug \"Error: Template file not found: ${tmpl}\"\n", " \t\treturn 1\n", " \tfi\n", " }\n", " \n", " # Helper function to loop through all environment variables prefixed with SECRET_ and\n", " # convert to the equivalent variable without SECRET. (ex SECRET_TERMINUS_TOKEN has a variable\n", " # called TERMINUS_TOKEN.\n", " convert_secrets ()\n", " {\n", " \teval 'secrets=(${!SECRET_@})'\n", " \tfor secret_key in \"${secrets[@]}\"\n", " \tdo\n", " \t\tsecret_value=${!secret_key}\n", " \t\tkey=${secret_key#SECRET_}\n", " \t\techo \"export ${key}=\\\"${secret_value}\\\";\" | sudo -u docker tee -a ~/.docksalrc\n", " \tdone\n", " }\n", " \n", " # Pantheon authentication\n", " terminus_login ()\n", " {\n", " \techo-debug \"Authenticating with Pantheon...\"\n", "+\t# This has to be done using the docker user via su to \tdone\n", "+}\n", "+\n", "+# Pantheon authentication\n", "+terminus_login ()\n", "+{\n", "+\techo-debug \"Authenticating with Pantheon...\"\n", " \t# This has to be done using the docker user via su to load the user environment\n", "-\t# Note: 'su -' = 'su -l'login'\n", "-\tlocal output\n", "-\toutput=$(sudo su - docker -c \"terminus auth:login --machine-token=\"$SECRET_TERMINUS_TOKEN\" >/dev/null 2>&1)\n", "-\t #>/dev/null 2>&1\n", "+ #>/dev/null 2>&1\n", " \tif [[ $? != 0 ]]; then\n", " \t\techo-debug \"ERROR: Pantheon authentication failed.\"\n", " \t\techo\n", " \t\techo \"$output\"\n", " \t\techo\n", " \tfi\n", " }\n", " \n", " # Process templates\n", " # Private SSH key\n", " render_tmpl \"$HOME_DIR/.ssh/id_rsa\"\n", " chmod 0600 \"$HOME_DIR/.ssh/id_rsa\"\n", " # Acquia Cloud API config\n", " render_tmpl \"$HOME_DIR/.acquia/cloudapi.conf\"\n", " \n", " # Terminus authentication\n", " [[ \"$SECRET_TERMINUS_TOKEN\" ]] && terminus_login\n", " \n", " # Convert all Environment Variables Prefixed with SECRET_\n", " convert_secrets\n", " \n", " # Source Docksalrc for when someone runs bash in the container\n", " echo \"source ~/.docksalrc\" | sudo -u docker tee -a ~/.bashrc\n", " \n", " # Docker user uid/gid mapping to the host user uid/gid\n", " [[ \"$HOST_UID\" != \"\" ]] && [[ \"$HOST_GID\" != \"\" ]] && uid_gid_reset\n", " \n", " # Enable xdebug\n", " [[ \"$XDEBUG_ENABLED\" != \"\" ]] && [[ \"$XDEBUG_ENABLED\" != \"0\" ]] && xdebug_enable\n", " \n", "\n", "\n", "========================================================================================================================\n", "Remove error handling\n", "\n", "--- 5.6/startup.sh\n", "+++ 5.6/startup.sh\n", "@@ -38,71 +38,64 @@\n", "@render_tmpl ()\n", " \t\techo-debug \"Rendering template: ${tmpl}...\"\n", " \t\tgomplate --file \"${tmpl}\" --out \"${file}\"\n", " \telse\n", " \t\techo-debug \"Error: Template file not found: ${tmpl}\"\n", " \t\treturn 1\n", " \tfi\n", " }\n", " \n", " # Helper function to loop through all environment variables prefixed with SECRET_ and\n", " # convert to the equivalent variable without SECRET. (ex SECRET_TERMINUS_TOKEN has a variable\n", " # called TERMINUS_TOKEN.\n", " convert_secrets ()\n", " {\n", " \teval 'secrets=(${!SECRET_@})'\n", " \tfor secret_key in \"${secrets[@]}\"\n", " \tdo\n", " \t\tsecret_value=${!secret_key}\n", " \t\tkey=${secret_key#SECRET_}\n", " \t\techo \"export ${key}=\\\"${secret_value}\\\";\" | sudo -u docker tee -a ~/.docksalrc\n", " \tdone\n", " }\n", " \n", " # Pantheon authentication\n", " terminus_login ()\n", " {\n", " \techo-debug \"Authenticating with Pantheon...\"\n", " \t# This has to be done using the docker user via su to load the user environment\n", " \t# Note: 'su -' = 'su -l'login'\n", " \tlocal output\n", " \toutput=$(sudo su - docker -c \"terminus auth:login --machine-token=\"$SECRET_TERMINUS_TOKEN\" >/dev/null 2>&1)\n", "-\t #>/dev/null 2>&1\n", "-\tif [[ $? != 0 ]]; then\n", "-\t\techo-debug \"ERROR: Pantheon authentication failed.\"\n", "-\t\techo\n", "-\t\techo \"$output\"\n", "-\t\techo\n", "-\tfi\n", "-}\n", "-\n", "-# Process templates\n", "-# Private SSH key\n", "+\t\t# Note: 'su -' = 'su -l' = 'su --login'\n", "+\tlocal output\n", "+\toutput=$(sudo su - docker -c \"terminus auth:login --machine-token='${TERMINUS_TOKEN}'\" 2>&1)\n", "+ Private SSH key\n", " render_tmpl \"$HOME_DIR/.ssh/id_rsa\"\n", " chmod 0600 \"$HOME_DIR/.ssh/id_rsa\"\n", " # Acquia Cloud API config\n", " render_tmpl \"$HOME_DIR/.acquia/cloudapi.conf\"\n", " \n", " # Terminus authentication\n", " [[ \"$SECRET_TERMINUS_TOKEN\" ]] && terminus_login\n", " \n", " # Convert all Environment Variables Prefixed with SECRET_\n", " convert_secrets\n", " \n", " # Source Docksalrc for when someone runs bash in the container\n", " echo \"source ~/.docksalrc\" | sudo -u docker tee -a ~/.bashrc\n", " \n", " # Docker user uid/gid mapping to the host user uid/gid\n", " [[ \"$HOST_UID\" != \"\" ]] && [[ \"$HOST_GID\" != \"\" ]] && uid_gid_reset\n", " \n", " # Enable xdebug\n", " [[ \"$XDEBUG_ENABLED\" != \"\" ]] && [[ \"$XDEBUG_ENABLED\" != \"0\" ]] && xdebug_enable\n", " \n", " # Make sure permissions are correct (after uid/gid change and COPY operations in Dockerfile)\n", " # To not bloat the image size, permissions on the home folder are reset at runtime.\n", " echo-debug \"Resetting permissions on $HOME_DIR and /var/www...\"\n", " chown \"${HOST_UID-:1000}:${HOST_GID:-1000}\" -R \"$HOME_DIR\"\n", " # Docker resets the project root folder permissions to 0:0 when cli is recreated (e.g. an env variable updated).\n", " # We apply a fix/workaround for this at startup (non-recursive).\n", " chown \"${HOST_UID-:1000}:${HOST_GID:-1000}\" /var/www\n", " \n", " # Initialization steps completed. Create a pid file to mark the container as healthy\n", " echo-debug \"Preliminary initialization completed\"\n", "\n", "\n", "========================================================================================================================\n", "Remove duplicate code\n", "\n", "--- 5.6/startup.sh\n", "+++ 5.6/startup.sh\n", "@@ -48,60 +48,70 @@\n", " # called TERMINUS_TOKEN.\n", " convert_secrets ()\n", " {\n", " \teval 'secrets=(${!SECRET_@})'\n", " \tfor secret_key in \"${secrets[@]}\"\n", " \tdo\n", " \t\tsecret_value=${!secret_key}\n", " \t\tkey=${secret_key#SECRET_}\n", " \t\techo \"export ${key}=\\\"${secret_value}\\\";\" | sudo -u docker tee -a ~/.docksalrc\n", " \tdone\n", " }\n", " \n", " # Pantheon authentication\n", " terminus_login ()\n", " {\n", " \techo-debug \"Authenticating with Pantheon...\"\n", " \t# This has to be done using the docker user via su to load the user environment\n", " \t# Note: 'su -' = 'su -l'login'\n", " \tlocal output\n", " \toutput=$(sudo su - docker -c \"terminus auth:login --machine-token=\"$SECRET_TERMINUS_TOKEN\" >/dev/null 2>&1)\n", " \t #>/dev/null 2>&1\n", " \tif [[ $? != 0 ]]; then\n", " \t\techo-debug \"ERROR: Pantheon authentication failed.\"\n", " \t\techo\n", " \t\techo \"$output\"\n", " \t\techo\n", " \tfi\n", " }\n", " \n", " # Process templates\n", "+#\t #>/dev/null 2>&1\n", "+\tif [[ $? != 0 ]]; then\n", "+\t\techo-debug \"ERROR: Pantheon authentication failed.\"\n", "+\t\techo\n", "+\t\techo \"$output\"\n", "+\t\techo\n", "+\tfi\n", "+}\n", "+\n", "+# Process templates\n", " # Private SSH key\n", " render_tmpl \"$HOME_DIR/.ssh/id_rsa\"\n", " chmod 0600 \"$HOME_DIR/.ssh/id_rsa\"\n", " # Acquia Cloud API config\n", " render_tmpl \"$HOME_DIR/.acquia/cloudapi.conf\"\n", " \n", " # Terminus authentication\n", " [[ \"$SECRET_TERMINUS_TOKEN\" ]] && terminus_login\n", " \n", " # Convert all Environment Variables Prefixed with SECRET_\n", " convert_secrets\n", " \n", " # Source Docksalrc for when someone runs bash in the container\n", " echo \"source ~/.docksalrc\" | sudo -u docker tee -a ~/.bashrc\n", " \n", " # Docker user uid/gid mapping to the host user uid/gid\n", " [[ \"$HOST_UID\" != \"\" ]] && [[ \"$HOST_GID\" != \"\" ]] && uid_gid_reset\n", " \n", " # Enable xdebug\n", " [[ \"$XDEBUG_ENABLED\" != \"\" ]] && [[ \"$XDEBUG_ENABLED\" != \"0\" ]] && xdebug_enable\n", " \n", " # Make sure permissions are correct (after uid/gid change and COPY operations in Dockerfile)\n", " # To not bloat the image size, permissions on the home folder are reset at runtime.\n", " echo-debug \"Resetting permissions on $HOME_DIR and /var/www...\"\n", " chown \"${HOST_UID-:1000}:${HOST_GID:-1000}\" -R \"$HOME_DIR\"\n", " # Docker resets the project root folder permissions to 0:0 when cli is recreated (e.g. an env variable updated).\n", " # We apply a fix/workaround for this at startup (non-recursive).\n", " chown \"${HOST_UID-:1000}:${HOST_GID:-1000}\" /var/www\n", " \n", " # Initialization steps completed. Create a pid file to mark the container as healthy\n", "\n", "\n", "========================================================================================================================\n", "Refactor startup script\n", "\n", "--- 7.1/startup.sh\n", "+++ 7.1/startup.sh\n", "@@ -1,6 +1,62 @@\n", "+#!/bin/bash\n", "+\n", "+# This script is running as root by default.\n", "+# Switching to the docker user can be done via \"gosu docker <command>\".\n", "+\n", "+HOME_DIR='/home/<USER>'\n", "+\n", "+DEBUG=${DEBUG:-0}\n", "+# Turn debugging ON when cli is started in the service mode\n", "+[[ \"$1\" == \"supervisord\" ]] && DEBUG=1\n", "+echo-debug ()\n", "+{\n", "+\t[[ \"$DEBUG\" != 0 ]] && echo \"$(date +\"%F %H:%M:%S\") | $@\"\n", "+}\n", "+\n", "+uid_gid_reset ()\n", "+{\n", "+\tif [[ \"$HOST_UID\" != \"$(id -u docker)\" ]] || [[ \"$HOST_GID\" != \"$(id -g docker)\" ]]; then\n", "+\t\techo-debug \"Updating docker user uid/gid to $HOST_UID/$HOST_GID to match the host user uid/gid...\"\n", "+\t\tusermod -u \"$HOST_UID\" -o docker\n", "+\t\tgroupmod -g \"$HOST_GID\" -o \"$(id -gn docker)\"\n", "+\tfi\n", "+}\n", "+\n", "+xdebug_enable ()\n", "+{\n", "+\techo-debug \"Enabling xdebug...\"\n", "+\tsudo ln -s /opt/docker-php-ext-xdebug.ini /usr/local/etc/php/conf.d/\n", "+}\n", "+\n", "+# Helper function to render configs from go templates using gomplate\n", "+render_tmpl ()\n", "+{\n", "+\tlocal file=\"${1}\"\n", "+\tlocal tmpl=\"${1}.tmpl\"\n", "+\n", "+\tif [[ -f \"${tmpl}\" ]]; then\n", "+\t\techo-debug \"Rendering template: ${tmpl}...\"\n", "+\t\tgomplate --file \"${tmpl}\" --out \"${file}\"\n", "+\telse\n", "+\t\techo-debug \"Error: Template file not found: ${tmpl}\"\n", "+\t\treturn 1\n", "+\tfi\n", "+}\n", "+\n", "+# Helper function to loop through all environment variables prefixed with SECRET_ and\n", "+# convert to the equivalent variable without SECRET. (ex SECRET_TERMINUS_TOKEN => TERMINUS_TOKEN.\n", "+convert_secrets ()\n", "+{\n", "+\teval 'secrets=(${!SECRET_@})'\n", "+\tfor secret_key in \"${secrets[@]}\"; do\n", "+\tdo\n", "+\t\tsecret_value=${!secret_key}\n", "+\t\tkey=${secret_key#SECRET_}\n", "+\t\techo \"export ${key}=\\\"${secret_value}\\\";\" | sudo -u docker tee -a ~/.docksalrc\n", "+\tdone\n", " #!/bin/bash\n", " \n", " # This script is running as root by default.\n", " # Switching to the docker user can be done via \"gosu docker <command>\".\n", " \n", " HOME_DIR='/home/<USER>'\n", "@@ -53,56 +109,13 @@\n", "@convert_secrets ()\n", "@\tdo\n", " \t\tsecret_value=${!secret_key}\n", " \t\tkey=${secret_key#SECRET_}\n", " \t\techo \"export ${key}=\\\"${secret_value}\\\";\" | sudo -u docker tee -a ~/.docksalrc\n", " \tdone\n", " }\n", " \n", "-terminus_login ()\n", "-{\n", "-\techo-debug \"Authenticating with Pantheon...\"\n", "-\tterminus auth:login --machine-token='${TERMINUS_TOKEN}'\" 2>&1)\n", "-\t #>/dev/null 2>&1\n", "-\tif [[ $? != 0 ]]; then\n", "-\t\techo-debug \"ERROR: Pantheon authentication failed.\"\n", "-\t\techo\n", "-\t\techo \"$output\"\n", "-\t\techo\n", "-\tfi\n", "-}\n", "-\n", "-# Process templates\n", "-# Private SSH key\n", "-render_tmpl \"$HOME_DIR/.ssh/id_rsa\"\n", "-chmod 0600 \"$HOME_DIR/.ssh/id_rsa\"\n", "-# Acquia Cloud API config\n", "-render_tmpl \"$HOME_DIR/.acquia/cloudapi.conf\"\n", "-\n", "-# Terminus authentication\n", "-[[ \"$SECRET_TERMINUS_TOKEN\" ]] && terminus_login\n", "-\n", "-# Convert all Environment Variables Prefixed with SECRET_\n", "-convert_secrets\n", "-\n", "-# Source Docksalrc for when someone runs bash in the container\n", "-echo \"source ~/.docksalrc\" | sudo -u docker tee -a ~/.bashrc\n", "-\n", "-# Docker user uid/gid mapping to the host user uid/gid\n", "-[[ \"$HOST_UID\" != \"\" ]] && [[ \"$HOST_GID\" != \"\" ]] && uid_gid_reset\n", "-\n", "-# Enable xdebug\n", "-[[ \"$XDEBUG_ENABLED\" != \"\" ]] && [[ \"$XDEBUG_ENABLED\" != \"0\" ]] && xdebug_enable\n", "-\n", "-# Make sure permissions are correct (after uid/gid change and COPY operations in Dockerfile)\n", "-# To not bloat the image size, permissions on the home folder are reset at runtime.\n", "-echo-debug \"Resetting permissions on $HOME_DIR and /var/www...\"\n", "-chown \"${HOST_UID-:1000}:${HOST_GID:-1000}\" -R \"$HOME_DIR\"\n", "-# <PERSON><PERSON> resets the project root folder permissions to 0:0 when cli is recreated (e.g. an env variable updated).\n", "-# We apply a fix/workaround for this at startup (non-recursive).\n", "-chown \"${HOST_UID-:1000}:${HOST_GID:-1000}\" /var/www\n", "-\n", "-# Automatically authenticate with Pantheon in Terminus token is present\n", "+us token is present\n", " # Note: this has to happen after th home directory permissions are reset,\n", " # otherwise the docker user may not have write access to /home/<USER>\n", " [[ \"$TERMINUS_TOKEN\" != \"\" ]] && terminus_login\n", " echo-debug \"Preliminary initialization completed\"\n", " touch /var/run/cli\n", " \n", "\n", "\n", "========================================================================================================================\n", "Improve comment clarity\n", "\n", "--- 7.1/startup.sh\n", "+++ 7.1/startup.sh\n", "@@ -96,13 +96,14 @@\n", " echo-debug \"Resetting permissions on $HOME_DIR and /var/www...\"\n", " chown \"${HOST_UID-:1000}:${HOST_GID:-1000}\" -R \"$HOME_DIR\"\n", " # Docker resets the project root folder permissions to 0:0 when cli is recreated (e.g. an env variable updated).\n", " # We apply a fix/workaround for this at startup (non-recursive).\n", " chown \"${HOST_UID-:1000}:${HOST_GID:-1000}\" /var/www\n", " \n", "-# Automatically authenticate with Pantheon in Terminus token is present\n", "+# Automatically authenticate with Pantheon in Termin# Pantheon authentication\n", "+us token is present\n", " # Note: this has to happen after th home directory permissions are reset,\n", " # otherwise the docker user may not have write access to /home/<USER>\n", " [[ \"$TERMINUS_TOKEN\" != \"\" ]] && terminus_login\n", " echo-debug \"Preliminary initialization completed\"\n", " touch /var/run/cli\n", " \n", "\n", "\n", "========================================================================================================================\n", "Improve authentication logic\n", "\n", "--- 7.1/startup.sh\n", "+++ 7.1/startup.sh\n", "@@ -96,14 +96,16 @@\n", " echo-debug \"Resetting permissions on $HOME_DIR and /var/www...\"\n", " chown \"${HOST_UID-:1000}:${HOST_GID:-1000}\" -R \"$HOME_DIR\"\n", " # Docker resets the project root folder permissions to 0:0 when cli is recreated (e.g. an env variable updated).\n", " # We apply a fix/workaround for this at startup (non-recursive).\n", " chown \"${HOST_UID-:1000}:${HOST_GID:-1000}\" /var/www\n", " \n", "-# Automatically authenticate with Pantheon in Terminus token is present\n", "-# Note: this has to happen after th home directory permissions are reset,\n", "+# Automatically authenticate with Pantheon in Terminterminus_login ()\n", "+{\n", "+\techo-debug \"Authenticating with Pantheon...\"\n", "+tory permissions are reset,\n", " # otherwise the docker user may not have write access to /home/<USER>\n", " [[ \"$TERMINUS_TOKEN\" != \"\" ]] && terminus_login\n", " echo-debug \"Preliminary initialization completed\"\n", " touch /var/run/cli\n", " \n", " # If crontab file is found within project add contents to user crontab file.\n", "\n", "\n", "========================================================================================================================\n", "Improve authentication command\n", "\n", "--- 7.1/startup.sh\n", "+++ 7.1/startup.sh\n", "@@ -97,14 +97,17 @@\n", " chown \"${HOST_UID-:1000}:${HOST_GID:-1000}\" -R \"$HOME_DIR\"\n", " # Docker resets the project root folder permissions to 0:0 when cli is recreated (e.g. an env variable updated).\n", " # We apply a fix/workaround for this at startup (non-recursive).\n", " chown \"${HOST_UID-:1000}:${HOST_GID:-1000}\" /var/www\n", " \n", " # Automatically authenticate with Pantheon in Terminus token is present\n", "-# Note: this has to happen after th home directory permissions are reset,\n", "-# otherwise the docker user may not have write access to /home/<USER>\n", "+# Note: this has to happen after th home direc\t# This has to be done using the docker user via su to load the user environment\n", "+\t# Note: 'su -' = 'su -l' = 'su --login'\n", "+\tlocal output\n", "+\toutput=$(sudo su - docker -c \"terminus auth:login --machine-token='${TERMINUS_TOKEN}'\" 2>&1)\n", "+have write access to /home/<USER>\n", " [[ \"$TERMINUS_TOKEN\" != \"\" ]] && terminus_login\n", " echo-debug \"Preliminary initialization completed\"\n", " touch /var/run/cli\n", " \n", " # If crontab file is found within project add contents to user crontab file.\n", " if [[ -f ${PROJECT_ROOT}/.docksal/services/cli/crontab ]]; then\n", "\n", "\n", "========================================================================================================================\n", "Improve authentication handling\n", "\n", "--- 7.1/startup.sh\n", "+++ 7.1/startup.sh\n", "@@ -98,19 +98,29 @@\n", " # Docker resets the project root folder permissions to 0:0 when cli is recreated (e.g. an env variable updated).\n", " # We apply a fix/workaround for this at startup (non-recursive).\n", " chown \"${HOST_UID-:1000}:${HOST_GID:-1000}\" /var/www\n", " \n", " # Automatically authenticate with Pantheon in Terminus token is present\n", " # Note: this has to happen after th home directory permissions are reset,\n", "-# otherwise the docker user may not have write access to /home/<USER>\n", "-[[ \"$TERMINUS_TOKEN\" != \"\" ]] && terminus_login\n", "-echo-debug \"Preliminary initialization completed\"\n", "-touch /var/run/cli\n", "+# otherwise the docker user may not \t #>/dev/null 2>&1\n", "+\tif [[ $? != 0 ]]; then\n", "+\t\techo-debug \"ERROR: Pantheon authentication failed.\"\n", "+\t\techo\n", "+\t\techo \"$output\"\n", "+\t\techo\n", "+\tfi\n", "+}\n", " \n", "-# If crontab file is found within project add contents to user crontab file.\n", "-if [[ -f ${PROJECT_ROOT}/.docksal/services/cli/crontab ]]; then\n", "+# Process templates\n", "+# Private SSH key\n", "+render_tmpl \"$HOME_DIR/.ssh/id_rsa\"\n", "+chmod 0600 \"$HOME_DIR/.ssh/id_rsa\"\n", "+# Acquia Cloud API config\n", "+render_tmpl \"$HOME_DIR/.acquia/cloudapi.conf\"\n", "+\n", "+i/crontab ]]; then\n", " \tcat ${PROJECT_ROOT}/.docksal/services/cli/crontab | crontab -u docker -\n", " fi\n", " \n", " if [[ -x ${PROJECT_ROOT}/.docksal/services/cli/startup.sh ]]; then\n", " \techo-debug \"Running Custom Startup Script...\"\n", " \t${PROJECT_ROOT}/.docksal/services/cli/startup.sh\n", "\n", "\n", "========================================================================================================================\n", "Simplify crontab file handling\n", "\n", "--- 7.1/startup.sh\n", "+++ 7.1/startup.sh\n", "@@ -104,14 +104,13 @@\n", " # otherwise the docker user may not have write access to /home/<USER>\n", " [[ \"$TERMINUS_TOKEN\" != \"\" ]] && terminus_login\n", " echo-debug \"Preliminary initialization completed\"\n", " touch /var/run/cli\n", " \n", " # If crontab file is found within project add contents to user crontab file.\n", "-if [[ -f ${PROJECT_ROOT}/.docksal/services/cli/crontab ]]; then\n", "-\tcat ${PROJECT_ROOT}/.docksal/services/cli/crontab | crontab -u docker -\n", "+if [[ -f ${PROJECT_ROOT}/.docksal/services/cltab -u docker -\n", " fi\n", " \n", " if [[ -x ${PROJECT_ROOT}/.docksal/services/cli/startup.sh ]]; then\n", " \techo-debug \"Running Custom Startup Script...\"\n", " \t${PROJECT_ROOT}/.docksal/services/cli/startup.sh\n", " \techo-debug \"Custom Startup Script Complete...\"\n", "\n", "\n", "========================================================================================================================\n", "Add initialization scripts\n", "\n", "--- 7.1/startup.sh\n", "+++ 7.1/startup.sh\n", "@@ -105,12 +105,41 @@\n", " [[ \"$TERMINUS_TOKEN\" != \"\" ]] && terminus_login\n", " echo-debug \"Preliminary initialization completed\"\n", " touch /var/run/cli\n", " \n", " # If crontab file is found within project add contents to user crontab file.\n", " if [[ -f ${PROJECT_ROOT}/.docksal/services/cli/crontab ]]; then\n", "+\tcat ${PROJECT_ROOT}/.docksal/services/cli/crontab | cron# Convert all Environment Variables Prefixed with SECRET_\n", "+convert_secrets\n", "+\n", "+# Source Docksalrc for when someone runs bash in the container\n", "+echo \"source ~/.docksalrc\" | sudo -u docker tee -a ~/.bashrc\n", "+\n", "+# Docker user uid/gid mapping to the host user uid/gid\n", "+[[ \"$HOST_UID\" != \"\" ]] && [[ \"$HOST_GID\" != \"\" ]] && uid_gid_reset\n", "+\n", "+# Enable xdebug\n", "+[[ \"$XDEBUG_ENABLED\" != \"\" ]] && [[ \"$XDEBUG_ENABLED\" != \"0\" ]] && xdebug_enable\n", "+\n", "+# Make sure permissions are correct (after uid/gid change and COPY operations in Dockerfile)\n", "+# To not bloat the image size, permissions on the home folder are reset at runtime.\n", "+echo-debug \"Resetting permissions on $HOME_DIR and /var/www...\"\n", "+chown \"${HOST_UID-:1000}:${HOST_GID:-1000}\" -R \"$HOME_DIR\"\n", "+# <PERSON><PERSON> resets the project root folder permissions to 0:0 when cli is recreated (e.g. an env variable updated).\n", "+# We apply a fix/workaround for this at startup (non-recursive).\n", "+chown \"${HOST_UID-:1000}:${HOST_GID:-1000}\" /var/www\n", "+\n", "+# Automatically authenticate with Pantheon in Terminus token is present\n", "+# Note: this has to happen after th home directory permissions are reset,\n", "+# otherwise the docker user may not have write access to /home/<USER>\n", "+[[ \"$TERMINUS_TOKEN\" != \"\" ]] && terminus_login\n", "+echo-debug \"Preliminary initialization completed\"\n", "+touch /var/run/cli\n", "+\n", "+# If crontab file is found within project add contents to user crontab file.\n", "+if [[ -f ${PROJECT_ROOT}/.docksal/services/cli/crontab ]]; then\n", " \tcat ${PROJECT_ROOT}/.docksal/services/cli/crontab | crontab -u docker -\n", " fi\n", " \n", " if [[ -x ${PROJECT_ROOT}/.docksal/services/cli/startup.sh ]]; then\n", " \techo-debug \"Running Custom Startup Script...\"\n", " \t${PROJECT_ROOT}/.docksal/services/cli/startup.sh\n", "\n", "\n", "========================================================================================================================\n", "Improve startup script\n", "\n", "--- 7.1/startup.sh\n", "+++ 7.1/startup.sh\n", "@@ -1,30 +1,128 @@\n", "+#!/bin/bash\n", "+\n", "+# This script is running as root by default.\n", "+# Switching to the docker user can be done via \"gosu docker <command>\".\n", "+\n", "+HOME_DIR='/home/<USER>'\n", "+\n", "+DEBUG=${DEBUG:-0}\n", "+# Turn debugging ON when cli is started in the service mode\n", "+[[ \"$1\" == \"supervisord\" ]] && DEBUG=1\n", "+echo-debug ()\n", "+{\n", "+\t[[ \"$DEBUG\" != 0 ]] && echo \"$(date +\"%F %H:%M:%S\") | $@\"\n", "+}\n", "+\n", "+uid_gid_reset ()\n", "+{\n", "+\tif [[ \"$HOST_UID\" != \"$(id -u docker)\" ]] || [[ \"$HOST_GID\" != \"$(id -g docker)\" ]]; then\n", "+\t\techo-debug \"Updating docker user uid/gid to $HOST_UID/$HOST_GID to match the host user uid/gid...\"\n", "+\t\tusermod -u \"$HOST_UID\" -o docker\n", "+\t\tgroupmod -g \"$HOST_GID\" -o \"$(id -gn docker)\"\n", "+\tfi\n", "+}\n", "+\n", "+xdebug_enable ()\n", "+{\n", "+\techo-debug \"Enabling xdebug...\"\n", "+\tsudo ln -s /opt/docker-php-ext-xdebug.ini /usr/local/etc/php/conf.d/\n", "+}\n", "+\n", "+# Helper function to render configs from go templates using gomplate\n", "+render_tmpl ()\n", "+{\n", "+\tlocal file=\"${1}\"\n", "+\tlocal tmpl=\"${1}.tmpl\"\n", "+\n", "+\tif [[ -f \"${tmpl}\" ]]; then\n", "+\t\techo-debug \"Rendering template: ${tmpl}...\"\n", "+\t\tgomplate --file \"${tmpl}\" --out \"${file}\"\n", "+\telse\n", "+\t\techo-debug \"Error: Template file not found: ${tmpl}\"\n", "+\t\treturn 1\n", "+\tfi\n", "+}\n", "+\n", "+# Helper function to loop through all environment variables prefixed with SECRET_ and\n", "+# convert to the equivalent variable without SECRET. (ex SECRET_TERMINUS_TOKEN has a variable\n", "+# called TERMINUS_TOKEN.\n", "+convert_secrets ()\n", "+{\n", "+\teval 'secrets=(${!SECRET_@})'\n", "+\tfor secret_key in \"${secrets[@]}\"\n", "+\tdo\n", "+\t\tsecret_value=${!secret_key}\n", "+\n", "+\t\t# Write new variables to /etc/profile.d/secrets.sh to make them available for all users/ \"export ${key}=\\\"${secret_value}\\\";\" | tee -a \"/etc/profile.d/secrets.sh\" >/dev/null\n", "+\n", "+\t\t# Also export new variables here\n", "+\t\t# This makes them available in the server/php-fpm environment\n", "+\t\teval \"export ${key}=${secret_value}\"\n", "+\tdone\n", "+}\n", "+\n", "+# Pantheon authentication\n", "+terminus_login ()\n", "+{\n", "+\techo-debug \"Authenticating with Pantheon...\"\n", "+\t# This has to be done using the docker user via su to load the user environment\n", "+\t# Note: 'su -' = 'su -l' = 'su --login --machine-token=\"$SECRET_TERMINUS_TOKEN}'\" 2>&1\n", "+}\n", "+\n", "+# Process templates\n", "+# Private SSH key\n", "+render_tmpl \"$HOME_DIR/.ssh/id_rsa\"\n", "+chmod 0600 \"$HOME_DIR/.ssh/id_rsa\"\n", "+# Acquia Cloud API config\n", "+render_tmpl \"$HOME_DIR/.acquia/cloudapi.conf\"\n", "+\n", "+# Convert all Environment Variables Prefixed with SECRET_\n", "+convert_secrets\n", "+\n", "+# Docker user uid/gid mapping to the host user uid/gid\n", "+[[ \"$HOST_UID\" != \"\" ]] && [[ \"$HOST_GID\" != \"\" ]] && uid_gid_reset\n", "+\n", "+# Enable xdebug\n", "+[[ \"$XDEBUG_ENABLED\" != \"\" ]] && [[ \"$XDEBUG_ENABLED\" != \"0\" ]] && xdebug_enable\n", "+\n", "+# Make sure permissions are correct (after uid/gid change and COPY operations in Dockerfile)\n", "+# To not bloat the image size, permissions on the home folder are reset at runtime.\n", "+echo-debug \"Resetting permissions on $HOME_DIR and /var/www...\"\n", "+chown \"${HOST_UID-:1000}:${HOST_GID:-1000}\" -R \"$HOME_DIR\"\n", "+# <PERSON><PERSON> resets the project root folder permissions to 0:0 when cli is recreated (e.g. an env variable updated).\n", "+# We apply a fix/workaround for this at startup (non-recursive).\n", "+chown \"${HOST_UID-:1000}:${HOST_GID:-1000}\" /var/www\n", "+\n", "+# Automatically authenticate with Pantheon in Terminus token is present\n", "+# Note: this has to happen after th home directory permissions are reset,\n", "+# otherwise the docker user may not have write access to /home/<USER>\n", " #!/bin/bash\n", " \n", " # This script is running as root by default.\n", " # Switching to the docker user can be done via \"gosu docker <command>\".\n", " \n", " HOME_DIR='/home/<USER>'\n", " \n", " DEBUG=${DEBUG:-0}\n", " # Turn debugging ON when cli is started in the service mode\n", " [[ \"$1\" == \"supervisord\" ]] && DEBUG=1\n", " echo-debug ()\n", " {\n", " \t[[ \"$DEBUG\" != 0 ]] && echo \"$(date +\"%F %H:%M:%S\") | $@\"\n", " }\n", " \n", " uid_gid_reset ()\n", " {\n", " \tif [[ \"$HOST_UID\" != \"$(id -u docker)\" ]] || [[ \"$HOST_GID\" != \"$(id -g docker)\" ]]; then\n", " \t\techo-debug \"Updating docker user uid/gid to $HOST_UID/$HOST_GID to match the host user uid/gid...\"\n", " \t\tusermod -u \"$HOST_UID\" -o docker\n", " \t\tgroupmod -g \"$HOST_GID\" -o \"$(id -gn docker)\"\n", " \tfi\n", " }\n", " \n", " xdebug_enable ()\n", " {\n", " \techo-debug \"Enabling xdebug...\"\n", " \tsudo ln -s /opt/docker-php-ext-xdebug.ini /usr/local/etc/php/conf.d/\n", " }\n", " \n", "@@ -70,61 +168,30 @@\n", " }\n", " \n", " # Process templates\n", " # Private SSH key\n", " render_tmpl \"$HOME_DIR/.ssh/id_rsa\"\n", " chmod 0600 \"$HOME_DIR/.ssh/id_rsa\"\n", " # Acquia Cloud API config\n", " render_tmpl \"$HOME_DIR/.acquia/cloudapi.conf\"\n", " \n", " # Convert all Environment Variables Prefixed with SECRET_\n", " convert_secrets\n", " \n", " # Docker user uid/gid mapping to the host user uid/gid\n", " [[ \"$HOST_UID\" != \"\" ]] && [[ \"$HOST_GID\" != \"\" ]] && uid_gid_reset\n", " \n", " # Enable xdebug\n", " [[ \"$XDEBUG_ENABLED\" != \"\" ]] && [[ \"$XDEBUG_ENABLED\" != \"0\" ]] && xdebug_enable\n", " \n", " # Make sure permissions are correct (after uid/gid change and COPY operations in Dockerfile)\n", " # To not bloat the image size, permissions on the home folder are reset at runtime.\n", " echo-debug \"Resetting permissions on $HOME_DIR and /var/www...\"\n", " chown \"${HOST_UID-:1000}:${HOST_GID:-1000}\" -R \"$HOME_DIR\"\n", " # Docker resets the project root folder permissions to 0:0 when cli is recreated (e.g. an env variable updated).\n", " # We apply a fix/workaround for this at startup (non-recursive).\n", " chown \"${HOST_UID-:1000}:${HOST_GID:-1000}\" /var/www\n", " \n", " # Automatically authenticate with Pantheon in Terminus token is present\n", " # Note: this has to happen after th home directory permissions are reset,\n", " # otherwise the docker user may not have write access to /home/<USER>\n", " [[ \"$TERMINUS_TOKEN\" != \"\" ]] && terminus_login\n", "-echo-debug \"Preliminary initialization completed\"\n", "-touch /var/run/cli\n", "-\n", "-# If crontab file is found within project add contents to user crontab file.\n", "-if [[ -f ${PROJECT_ROOT}/.docksal/services/cli/crontab ]]; then\n", "-\tcat ${PROJECT_ROOT}/.docksal/services/cli/crontab | crontab -u docker -\n", "-fi\n", "-\n", "-# Initialization steps completed. Create a pid file to mark the container as healthy\n", "-echo-debug \"Preliminary initialization completed.\"\n", "-touch /var/run/cli\n", "-\n", "-if [[ -x ${PROJECT_ROOT}/.docksal/services/cli/startup.sh ]]; then\n", "-\techo-debug \"Running Custom Startup Script...\"\n", "-\t${PROJECT_ROOT}/.docksal/services/cli/startup.sh\n", "-\techo-debug \"Custom Startup Script Complete...\"\n", "-fi\n", "-\n", "-# Execute passed CMD arguments\n", "-echo-debug \"Executing the requested command...\"\n", "-# Service mode (run as root)\n", "-if [[ \"$1\" == \"supervisord\" ]]; then\n", "-\texec gosu root supervisord -c /etc/supervisor/supervisord.conf\n", "-# Command mode (run as docker user)\n", "-else\n", "-\t# This makes sure the environment is set up correctly for the docker user\n", "-\tDOCKSALRC='source $HOME/.docksalrc >/dev/null 2>&1'\n", "-\t# Launch the passed command in an non-interactive bash session under docker user\n", "-\t# $@ does not work here. $* has to be used.\n", "-\texec gosu docker bash -c \"$DOCKSALRC; exec $*\"\n", "-fi\n", "\n", "\n", "========================================================================================================================\n", "Remove duplicate code\n", "\n", "--- 7.1/startup.sh\n", "+++ 7.1/startup.sh\n", "@@ -101,30 +101,59 @@\n", " touch /var/run/cli\n", " \n", " # If crontab file is found within project add contents to user crontab file.\n", " if [[ -f ${PROJECT_ROOT}/.docksal/services/cli/crontab ]]; then\n", " \tcat ${PROJECT_ROOT}/.docksal/services/cli/crontab | crontab -u docker -\n", " fi\n", " \n", " # Initialization steps completed. Create a pid file to mark the container as healthy\n", " echo-debug \"Preliminary initialization completed.\"\n", " touch /var/run/cli\n", " \n", " if [[ -x ${PROJECT_ROOT}/.docksal/services/cli/startup.sh ]]; then\n", " \techo-debug \"Running Custom Startup Script...\"\n", " \t${PROJECT_ROOT}/.docksal/services/cli/startup.sh\n", " \techo-debug \"Custom Startup Script Complete...\"\n", " fi\n", " \n", " # Execute passed CMD arguments\n", " echo-debug \"Executing the requested command...\"\n", " # Service mode (run as root)\n", " if [[ \"$1\" == \"supervisord\" ]]; then\n", " \texec gosu root supervisord -c /etc/supervisor/supervisord.conf\n", " # Command mode (run as docker user)\n", " else\n", " \t# This makes sure the environment is set up correctly for the docker user\n", " \tDOCKSALRC='source $HOME/.docksalrc >/dev/null 2>&1'\n", " \t# Launch the passed command in an non-interactive bash session under docker user\n", " \t# $@ does not work here. $* has to be used.\n", " \texec gosu docker bash -c \"$DOCKSALRC; exec $*\"\n", " fi\n", "+\n", "+# If crontab file is found within project add contents to user crontab file.\n", "+if [[ -f ${PROJECT_ROOT}/.docksal/services/cli/crontab ]]; then\n", "+\tcat ${PROJECT_ROOT}/.docksal/services/cli/crontab | crontab -u docker -\n", "+fi\n", "+\n", "+# Initialization steps completed. Create a pid file to mark the container as healthy\n", "+echo-debug \"Preliminary initialization completed.\"\n", "+touch /var/run/cli\n", "+\n", "+if [[ -x ${PROJECT_ROOT}/.docksal/services/cli/startup.sh ]]; then\n", "+\techo-debug \"Running Custom Startup Script...\"\n", "+\t${PROJECT_ROOT}/.docksal/services/cli/startup.sh\n", "+\techo-debug \"Custom Startup Script Complete...\"\n", "+fi\n", "+\n", "+# Execute passed CMD arguments\n", "+echo-debug \"Executing the requested command...\"\n", "+# Service mode (run as root)\n", "+if [[ \"$1\" == \"supervisord\" ]]; then\n", "+\texec gosu root supervisord -c /etc/supervisor/supervisord.conf\n", "+# Command mode (run as docker user)\n", "+else\n", "+\t# This makes sure the environment is set up correctly for the docker user\n", "+\tDOCKSALRC='source $HOME/.docksalrc >/dev/null 2>&1'\n", "+\t# Launch the passed command in an non-interactive bash session under docker user\n", "+\t# $@ does not work here. $* has to be used.\n", "+\texec gosu docker bash -c \"$DOCKSALRC; exec $*\"\n", "+fi\n", "\n", "\n", "========================================================================================================================\n", "Add semicolon\n", "\n", "--- 7.1/startup.sh\n", "+++ 7.1/startup.sh\n", "@@ -46,13 +46,13 @@\n", " # Helper function to loop through all environment variables prefixed with SECRET_ and\n", " # convert to the equivalent variable without SECRET.\n", " # Example: SECRET_TERMINUS_TOKEN => TERMINUS_TOKEN.\n", " convert_secrets ()\n", " {\n", " \teval 'secrets=(${!SECRET_@})'\n", "-\tfor secret_key in \"${secrets[@]}\"\n", "+\tfor secret_key in \"${secrets[@]}\"; do\n", " \t\tkey=${secret_key#SECRET_}\n", " \t\tsecret_value=${!secret_key}\n", " \n", " \t\t# Write new variables to /etc/profile.d/secrets.sh to make them available for all users/sessions\n", " \t\techo \"export ${key}=\\\"${secret_value}\\\"\" | sudo -u docker tee -a ~/.docksalrc\n", " \tdone\n", "\n", "\n", "========================================================================================================================\n", "Write secrets to system-wide file and export variables\n", "\n", "--- 7.1/startup.sh\n", "+++ 7.1/startup.sh\n", "@@ -51,13 +51,17 @@\n", "@convert_secrets ()\n", " \teval 'secrets=(${!SECRET_@})'\n", " \tfor secret_key in \"${secrets[@]}\"\n", " \t\tkey=${secret_key#SECRET_}\n", " \t\tsecret_value=${!secret_key}\n", " \n", " \t\t# Write new variables to /etc/profile.d/secrets.sh to make them available for all users/sessions\n", "-\t\techo \"export ${key}=\\\"${secret_value}\\\"\" | sudo -u docker tee -a ~/.docksalrc\n", "+\t\techo \"export ${key}=\\\"${secret_value}\\\"\" | tee -a \"/etc/profile.d/secrets.sh\" >/dev/null\n", "+\n", "+\t\t# Also export new variables here\n", "+\t\t# This makes them available in the server/php-fpm environment\n", "+\t\teval \"export ${key}=${secret_value}\"\n", " \tdone\n", " }\n", " \n", " # Pantheon authentication\n", " terminus_login ()\n", " {\n", "\n", "\n", "========================================================================================================================\n", "Improve Pantheon authentication\n", "\n", "--- 7.1/startup.sh\n", "+++ 7.1/startup.sh\n", "@@ -59,15 +59,23 @@\n", " }\n", " \n", " # Pantheon authentication\n", " terminus_login ()\n", " {\n", " \techo-debug \"Authenticating with Pantheon...\"\n", "-\tterminus auth:login'\n", "+\t# This has to be done using the docker user via su to load the user environment\n", "+\t# Note: 'su -' = 'su -l' = 'su --login'\n", " \tlocal output\n", "-\toutput=$(sudo su - docker -c \"terminus auth:login --machine-token=\"$SECRET_TERMINUS_TOKEN}'\" 2>&1\n", "+\toutput=$(sudo su - docker -c \"terminus auth:login --machine-token='${TERMINUS_TOKEN}'\" 2>&1)\n", "+\t #>/dev/null 2>&1\n", "+\tif [[ $? != 0 ]]; then\n", "+\t\techo-debug \"ERROR: Pantheon authentication failed.\"\n", "+\t\techo\n", "+\t\techo \"$output\"\n", "+\t\techo\n", "+\tfi\n", " }\n", " \n", " # Process templates\n", " # Private SSH key\n", " render_tmpl \"$HOME_DIR/.ssh/id_rsa\"\n", " chmod 0600 \"$HOME_DIR/.ssh/id_rsa\"\n", "\n", "\n", "========================================================================================================================\n", "Improve script functionality\n", "\n", "--- 7.0/startup.sh\n", "+++ 7.0/startup.sh\n", "@@ -1,30 +1,145 @@\n", "+#!/bin/bash\n", "+\n", "+# This script is running as root by default.\n", "+# Switching to the docker user can be done via \"gosu docker <command>\".\n", "+\n", "+HOME_DIR='/home/<USER>'\n", "+\n", "+DEBUG=${DEBUG:-0}\n", "+# Turn debugging ON when cli is started in the service mode\n", "+[[ \"$1\" == \"supervisord\" ]] && DEBUG=1\n", "+echo-debug ()\n", "+{\n", "+\t[[ \"$DEBUG\" != 0 ]] && echo \"$(date +\"%F %H:%M:%S\") | $@\"\n", "+}\n", "+\n", "+uid_gid_reset ()\n", "+{\n", "+\tif [[ \"$HOST_UID\" != \"$(id -u docker)\" ]] || [[ \"$HOST_GID\" != \"$(id -g docker)\" ]]; then\n", "+\t\techo-debug \"Updating docker user uid/gid to $HOST_UID/$HOST_GID to match the host user uid/gid...\"\n", "+\t\tusermod -u \"$HOST_UID\" -o docker\n", "+\t\tgroupmod -g \"$HOST_GID\" -o \"$(id -gn docker)\"\n", "+\tfi\n", "+}\n", "+\n", "+xdebug_enable ()\n", "+{\n", "+\techo-debug \"Enabling xdebug...\"\n", "+\tsudo ln -s /opt/docker-php-ext-xdebug.ini /usr/local/etc/php/conf.d/\n", "+}\n", "+\n", "+# Helper function to render configs from go templates using gomplate\n", "+render_tmpl ()\n", "+{\n", "+\tlocal file=\"${1}\"\n", "+\tlocal tmpl=\"${1}.tmpl\"\n", "+\n", "+\tif [[ -f \"${tmpl}\" ]]; then\n", "+\t\techo-debug \"Rendering template: ${tmpl}...\"\n", "+\t\tgomplate --file \"${tmpl}\" --out \"${file}\"\n", "+\telse\n", "+\t\techo-debug \"Error: Template file not found: ${tmpl}\"\n", "+\t\treturn 1\n", "+\tfi\n", "+}\n", "+\n", "+# Helper function to loop through all environment variables prefixed with SECRET_ and\n", "+# convert to the equivalent variable without SECRET.\n", "+# Example: SECRET_TERMINUS_TOKEN => TERMINUS_TOKEN.\n", "+convert_secrets ()\n", "+{\n", "+\teval 'secrets=(${!SECRET_@})'\n", "+\tfor secret_key in \"${secrets[@]}\"; do\n", "+\t\tkey=${secret_key#SECRET_}\n", "+\t\tsecret_value=${!secret_key}\n", "+\n", "+\t\t# Write new variables to /etc/profile.d/secrets.sh to make them available for all users/sessions\n", "+\t\techo \"export ${key}=\\\"${secret_value}\\\"\" | tee -a \"/etc/profile.d/secrets.sh\" >/dev/null\n", "+\n", "+\t\t# Also export new variables here\n", "+\t\t# This makes them available in the server/php-fpm environment\n", "+\t\teval \"export ${key}=${secret_value}\"\n", "+\tdone\n", "+}\n", "+\n", "+# Pantheon authentication\n", "+terminus_login ()\n", "+{\n", "+\techo-debug \"Authenticating with Pantheon...\"\n", "+\t# This has to be done using the docker user via su to load the user environment\n", "+\t# Note: 'su -' = 'su -l' = 'su --login'\n", "+\tlocal output\n", "+\toutput=$(sudo su - docker -c \"terminus auth:login --machine-token='${TERMINUS_TOKEN}'\" 2>&1)\n", "+\t #>/dev/null 2>&1\n", "+\tif [[ $? != 0 ]]; then\n", "+\t\techo-debug \"ERROR: Pantheon authentication failed.\"\n", "+\t\techo\n", "+\t\techo \"$output\"\n", "+\t\techo\n", "+\tfi\n", "+}\n", "+\n", "+# Process templates\n", "+# Private SSH key\n", "+render_tmpl \"$HOME_DIR/.ssh/id_rsa\"\n", "+chmod 0600 \"$HOME_DIR/.ssh/id_rsa\"\n", "+# Acquia Cloud API config\n", "+render_tmpl \"$HOME_DIR/.acquia/cloudapi.conf\"\n", "+\n", "+# Convert all Environment Variables Prefixed with SECRET_\n", "+convert_secrets\n", "+\n", "+# Docker user uid/gid mapping to the host user uid/gid\n", "+[[ \"$HOST_UID\" != \"\" ]] && [[ \"$HOST_GID\" != \"\" ]] && uid_gid_reset\n", "+\n", "+# Enable xdebug\n", "+[[ \"$XDEBUG_ENABLED\" != \"\" ]] && [[ \"$XDEBUG_ENABLED\" != \"0\" ]] && xdebug_enable\n", "+\n", "+# Make sure permissions are correct (after uid/gid change and COPY operations in Dockerfile)\n", "+# To not bloat the image size, permissions on the home folder are reset at runtime.\n", "+echo-debug \"Resetting permissions on $HOME_DIR and /var/www...\"\n", "+chown \"${HOST_UID-:1000}:${HOST_GID:-1000}\" -R \"$HOME_DIR\"\n", "+# <PERSON><PERSON> resets the project root folder permissions to 0:0 when cli is recreated (e.g. an env variable updated).\n", "+# We apply a fix/workaround for this at startup (non-recursive).\n", "+chown \"${HOST_UID-:1000}:${HOST_GID:-1000}\" /var/www\n", "+\n", "+# Automatically authenticate with Pantheon in Terminus token is present\n", "+# Note: this has to happen after th home directory permissions are reset,\n", "+# otherwise the docker user may not have write access to /home/<USER>\n", "+[[ \"$TERMINUS_TOKEN\" != \"\" ]] && terminus_login\n", "+\n", "+# If crontab file is found within project add contents to user crontab file.\n", "+if [[ -f ${PROJECT_ROOT}/.docksal/services/cli/crontab ]]; then\n", "+\techo-debug \"Loading crontab...\"\n", "+\tcat ${PROJECT_ROOT}/.docksal/services/cli/crontab | crontab -u docker -\n", "+fi\n", " #!/bin/bash\n", " \n", " # This script is running as root by default.\n", " # Switching to the docker user can be done via \"gosu docker <command>\".\n", " \n", " HOME_DIR='/home/<USER>'\n", " \n", " DEBUG=${DEBUG:-0}\n", " # Turn debugging ON when cli is started in the service mode\n", " [[ \"$1\" == \"supervisord\" ]] && DEBUG=1\n", " echo-debug ()\n", " {\n", " \t[[ \"$DEBUG\" != 0 ]] && echo \"$(date +\"%F %H:%M:%S\") | $@\"\n", " }\n", " \n", " uid_gid_reset ()\n", " {\n", " \tif [[ \"$HOST_UID\" != \"$(id -u docker)\" ]] || [[ \"$HOST_GID\" != \"$(id -g docker)\" ]]; then\n", " \t\techo-debug \"Updating docker user uid/gid to $HOST_UID/$HOST_GID to match the host user uid/gid...\"\n", " \t\tusermod -u \"$HOST_UID\" -o docker\n", " \t\tgroupmod -g \"$HOST_GID\" -o \"$(id -gn docker)\"\n", " \tfi\n", " }\n", " \n", " xdebug_enable ()\n", " {\n", " \techo-debug \"Enabling xdebug...\"\n", " \tsudo ln -s /opt/docker-php-ext-xdebug.ini /usr/local/etc/php/conf.d/\n", " }\n", " \n", "@@ -110,36 +225,30 @@\n", " \n", " # If crontab file is found within project add contents to user crontab file.\n", " if [[ -f ${PROJECT_ROOT}/.docksal/services/cli/crontab ]]; then\n", " \techo-debug \"Loading crontab...\"\n", " \tcat ${PROJECT_ROOT}/.docksal/services/cli/crontab | crontab -u docker -\n", " fi\n", " \n", " # Initialization steps completed. Create a pid file to mark the container as healthy\n", " echo-debug \"Preliminary initialization completed.\"\n", " touch /var/run/cli\n", " \n", " # Execute a custom startup script if present\n", " if [[ -x ${PROJECT_ROOT}/.docksal/services/cli/startup.sh ]]; then\n", " \techo-debug \"Running custom startup script...\"\n", " \t# TODO: should we source the script instead?\n", " \t${PROJECT_ROOT}/.docksal/services/cli/startup.sh\n", " \tif [[ $? == 0 ]]; then\n", " \t\techo-debug \"Custom startup script executed successfully.\"\n", " \telse\n", " \t\techo-debug \"ERROR: Custom startup script execution failed.\"\n", " \tfi\n", " fi\n", " \n", " # Execute passed CMD arguments\n", " echo-debug \"Passing execution to: $*\"\n", " # Service mode (run as root)\n", " if [[ \"$1\" == \"supervisord\" ]]; then\n", " \texec gosu root supervisord -c /etc/supervisor/supervisord.conf\n", " # Command mode (run as docker user)\n", " else\n", "-\t# This makes sure the environment is set up correctly for the docker user\n", "-\tDOCKSALRC='source $HOME/.docksalrc >/dev/null 2>&1'\n", "-\t# Launch the passed command in an non-interactive bash session under docker user\n", "-\t# $@ does not work here. $* has to be used.\n", "-\texec gosu docker bash -c \"$DOCKSALRC; exec $*\"\n", "-fi\n", "\n", "\n", "========================================================================================================================\n", "Replace exec command\n", "\n", "--- 7.0/startup.sh\n", "+++ 7.0/startup.sh\n", "@@ -116,30 +116,31 @@\n", " \n", " # Initialization steps completed. Create a pid file to mark the container as healthy\n", " echo-debug \"Preliminary initialization completed.\"\n", " touch /var/run/cli\n", " \n", " # Execute a custom startup script if present\n", " if [[ -x ${PROJECT_ROOT}/.docksal/services/cli/startup.sh ]]; then\n", " \techo-debug \"Running custom startup script...\"\n", " \t# TODO: should we source the script instead?\n", " \t${PROJECT_ROOT}/.docksal/services/cli/startup.sh\n", " \tif [[ $? == 0 ]]; then\n", " \t\techo-debug \"Custom startup script executed successfully.\"\n", " \telse\n", " \t\techo-debug \"ERROR: Custom startup script execution failed.\"\n", " \tfi\n", " fi\n", " \n", " # Execute passed CMD arguments\n", " echo-debug \"Passing execution to: $*\"\n", " # Service mode (run as root)\n", " if [[ \"$1\" == \"supervisord\" ]]; then\n", " \texec gosu root supervisord -c /etc/supervisor/supervisord.conf\n", " # Command mode (run as docker user)\n", " else\n", " \t# This makes sure the environment is set up correctly for the docker user\n", " \tDOCKSALRC='source $HOME/.docksalrc >/dev/null 2>&1'\n", " \t# Launch the passed command in an non-interactive bash session under docker user\n", " \t# $@ does not work here. $* has to be used.\n", " \texec gosu docker bash -c \"$DOCKSALRC; exec $*\"\n", " fi\n", "+\texec gosu docker \"$@\"\n", "\n", "\n", "========================================================================================================================\n", "Add missing fi statement\n", "\n", "--- 7.0/startup.sh\n", "+++ 7.0/startup.sh\n", "@@ -116,30 +116,31 @@\n", " \n", " # Initialization steps completed. Create a pid file to mark the container as healthy\n", " echo-debug \"Preliminary initialization completed.\"\n", " touch /var/run/cli\n", " \n", " # Execute a custom startup script if present\n", " if [[ -x ${PROJECT_ROOT}/.docksal/services/cli/startup.sh ]]; then\n", " \techo-debug \"Running custom startup script...\"\n", " \t# TODO: should we source the script instead?\n", " \t${PROJECT_ROOT}/.docksal/services/cli/startup.sh\n", " \tif [[ $? == 0 ]]; then\n", " \t\techo-debug \"Custom startup script executed successfully.\"\n", " \telse\n", " \t\techo-debug \"ERROR: Custom startup script execution failed.\"\n", " \tfi\n", " fi\n", " \n", " # Execute passed CMD arguments\n", " echo-debug \"Passing execution to: $*\"\n", " # Service mode (run as root)\n", " if [[ \"$1\" == \"supervisord\" ]]; then\n", " \texec gosu root supervisord -c /etc/supervisor/supervisord.conf\n", " # Command mode (run as docker user)\n", " else\n", " \t# This makes sure the environment is set up correctly for the docker user\n", " \tDOCKSALRC='source $HOME/.docksalrc >/dev/null 2>&1'\n", " \t# Launch the passed command in an non-interactive bash session under docker user\n", " \t# $@ does not work here. $* has to be used.\n", " \texec gosu docker bash -c \"$DOCKSALRC; exec $*\"\n", " fi\n", "+fi\n", "\n", "\n", "========================================================================================================================\n", "Add debugging and helper functions\n", "\n", "--- 5.6/startup.sh\n", "+++ 5.6/startup.sh\n", "@@ -95,14 +95,115 @@\n", " render_tmpl \"$HOME_DIR/.ssh/id_rsa\"\n", " chmod 0600 \"$HOME_DIR/.ssh/id_rsa\"\n", " # Acquia Cloud API config\n", " render_tmpl \"$HOME_DIR/.acquia/cloudapi.conf\"\n", " \n", " # Convert all Environment Variables Prefixed with SECRET_\n", " convert_secrets\n", "+#!/bin/bash\n", "+\n", "+# This script is running as root by default.\n", "+# Switching to the docker user can be done via \"gosu docker <command>\".\n", "+\n", "+HOME_DIR='/home/<USER>'\n", "+\n", "+DEBUG=${DEBUG:-0}\n", "+# Turn debugging ON when cli is started in the service mode\n", "+[[ \"$1\" == \"supervisord\" ]] && DEBUG=1\n", "+echo-debug ()\n", "+{\n", "+\t[[ \"$DEBUG\" != 0 ]] && echo \"$(date +\"%F %H:%M:%S\") | $@\"\n", "+}\n", "+\n", "+uid_gid_reset ()\n", "+{\n", "+\tif [[ \"$HOST_UID\" != \"$(id -u docker)\" ]] || [[ \"$HOST_GID\" != \"$(id -g docker)\" ]]; then\n", "+\t\techo-debug \"Updating docker user uid/gid to $HOST_UID/$HOST_GID to match the host user uid/gid...\"\n", "+\t\tusermod -u \"$HOST_UID\" -o docker\n", "+\t\tgroupmod -g \"$HOST_GID\" -o \"$(id -gn docker)\"\n", "+\tfi\n", "+}\n", "+\n", "+xdebug_enable ()\n", "+{\n", "+\techo-debug \"Enabling xdebug...\"\n", "+\tsudo ln -s /opt/docker-php-ext-xdebug.ini /usr/local/etc/php/conf.d/\n", "+}\n", "+\n", "+# Helper function to render configs from go templates using gomplate\n", "+render_tmpl ()\n", "+{\n", "+\tlocal file=\"${1}\"\n", "+\tlocal tmpl=\"${1}.tmpl\"\n", "+\n", "+\tif [[ -f \"${tmpl}\" ]]; then\n", "+\t\techo-debug \"Rendering template: ${tmpl}...\"\n", "+\t\tgomplate --file \"${tmpl}\" --out \"${file}\"\n", "+\telse\n", "+\t\techo-debug \"Error: Template file not found: ${tmpl}\"\n", "+\t\treturn 1\n", "+\tfi\n", "+}\n", "+\n", "+# Helper function to loop through all environment variables prefixed with SECRET_ and\n", "+# convert to the equivalent variable without SECRET.\n", "+# Example: SECRET_TERMINUS_TOKEN => TERMINUS_TOKEN.\n", "+convert_secrets ()\n", "+{\n", "+\teval 'secrets=(${!SECRET_@})'\n", "+\tfor secret_key in \"${secrets[@]}\"; do\n", "+\t\tkey=${secret_key#SECRET_}\n", "+\t\tsecret_value=${!secret_key}\n", "+\n", "+\t\t# Write new variables to /etc/profile.d/secrets.sh to make them available for all users/sessions\n", "+\t\techo \"export ${key}=\\\"${secret_value}\\\"\" | tee -a \"/etc/profile.d/secrets.sh\" >/dev/null\n", "+\n", "+\t\t# Also export new variables here\n", "+\t\t# This makes them available in the server/php-fpm environment\n", "+\t\teval \"export ${key}=${secret_value}\"\n", "+\tdone\n", "+}\n", "+\n", "+# Pantheon authentication\n", "+terminus_login ()\n", "+{\n", "+\techo-debug \"Authenticating with Pantheon...\"\n", "+\t# This has to be done using the docker user via su to load the user environment\n", "+\t# Note: 'su -' = 'su -l' = 'su --login'\n", "+\tlocal output\n", "+\toutput=$(sudo su - docker -c \"terminus auth:login --machine-token='${TERMINUS_TOKEN}'\" 2>&1)\n", "+\t #>/dev/null 2>&1\n", "+\tif [[ $? != 0 ]]; then\n", "+\t\techo-debug \"ERROR: Pantheon authentication failed.\"\n", "+\t\techo\n", "+\t\techo \"$output\"\n", "+\t\techo\n", "+\tfi\n", "+}\n", "+\n", "+# Git settings\n", "+git_settings ()\n", "+{\n", "+\tif [[ \"$GIT_USER_EMAIL\" != \"\" ]] && [[ \"$GIT_USER_NAME\" != \"\" ]]; then\n", "+\t\t# These must run as the docker user\n", "+\t\techo-debug \"Configuring git...\"\n", "+\t\tgosu docker git config --global user.email \"${GIT_USER_EMAIL}\"\n", "+\t\tgosu docker git config --global user.name \"${GIT_USER_NAME}\"\n", "+\tfi\n", "+}\n", "+\n", "+# Process templates\n", "+# Private SSH key\n", "+render_tmpl \"$HOME_DIR/.ssh/id_rsa\"\n", "+chmod 0600 \"$HOME_DIR/.ssh/id_rsa\"\n", "+# Acquia Cloud API config\n", "+render_tmpl \"$HOME_DIR/.acquia/cloudapi.conf\"\n", "+\n", "+# Convert all Environment Variables Prefixed with SECRET_\n", "+convert_secrets\n", " \n", " # Docker user uid/gid mapping to the host user uid/gid\n", " [[ \"$HOST_UID\" != \"\" ]] && [[ \"$HOST_GID\" != \"\" ]] && uid_gid_reset\n", " \n", " # Enable xdebug\n", " [[ \"$XDEBUG_ENABLED\" != \"\" ]] && [[ \"$XDEBUG_ENABLED\" != \"0\" ]] && xdebug_enable\n", " \n", "@@ -121,32 +222,7 @@\n", " \n", " # If crontab file is found within project add contents to user crontab file.\n", " if [[ -f ${PROJECT_ROOT}/.docksal/services/cli/crontab ]]; then\n", " \techo-debug \"Loading crontab...\"\n", " \tcat ${PROJECT_ROOT}/.docksal/services/cli/crontab | crontab -u docker -\n", " fi\n", " \n", "-# Initialization steps completed. Create a pid file to mark the container as healthy\n", "-echo-debug \"Preliminary initialization completed.\"\n", "-touch /var/run/cli\n", "-\n", "-# Execute a custom startup script if present\n", "-if [[ -x ${PROJECT_ROOT}/.docksal/services/cli/startup.sh ]]; then\n", "-\techo-debug \"Running custom startup script...\"\n", "-\t# TODO: should we source the script instead?\n", "-\t${PROJECT_ROOT}/.docksal/services/cli/startup.sh\n", "-\tif [[ $? == 0 ]]; then\n", "-\t\techo-debug \"Custom startup script executed successfully.\"\n", "-\telse\n", "-\t\techo-debug \"ERROR: Custom startup script execution failed.\"\n", "-\tfi\n", "-fi\n", "-\n", "-# Execute passed CMD arguments\n", "-echo-debug \"Passing execution to: $*\"\n", "-# Service mode (run as root)\n", "-if [[ \"$1\" == \"supervisord\" ]]; then\n", "-\texec gosu root supervisord -c /etc/supervisor/supervisord.conf\n", "-# Command mode (run as docker user)\n", "-else\n", "-\texec gosu docker \"$@\"\n", "-fi\n", "\n", "\n", "========================================================================================================================\n", "Add git settings\n", "\n", "--- 5.6/startup.sh\n", "+++ 5.6/startup.sh\n", "@@ -146,7 +146,10 @@\n", " # Service mode (run as root)\n", " if [[ \"$1\" == \"supervisord\" ]]; then\n", " \texec gosu root supervisord -c /etc/supervisor/supervisord.conf\n", " # Command mode (run as docker user)\n", " else\n", " \texec gosu docker \"$@\"\n", " fi\n", "+# Apply git settings\n", "+git_settings\n", "+\n", "\n", "\n", "========================================================================================================================\n", "Add custom startup script execution\n", "\n", "--- 5.6/startup.sh\n", "+++ 5.6/startup.sh\n", "@@ -146,7 +146,32 @@\n", " # Service mode (run as root)\n", " if [[ \"$1\" == \"supervisord\" ]]; then\n", " \texec gosu root supervisord -c /etc/supervisor/supervisord.conf\n", " # Command mode (run as docker user)\n", " else\n", " \texec gosu docker \"$@\"\n", " fi\n", "+# Initialization steps completed. Create a pid file to mark the container as healthy\n", "+echo-debug \"Preliminary initialization completed.\"\n", "+touch /var/run/cli\n", "+\n", "+# Execute a custom startup script if present\n", "+if [[ -x ${PROJECT_ROOT}/.docksal/services/cli/startup.sh ]]; then\n", "+\techo-debug \"Running custom startup script...\"\n", "+\t# TODO: should we source the script instead?\n", "+\t${PROJECT_ROOT}/.docksal/services/cli/startup.sh\n", "+\tif [[ $? == 0 ]]; then\n", "+\t\techo-debug \"Custom startup script executed successfully.\"\n", "+\telse\n", "+\t\techo-debug \"ERROR: Custom startup script execution failed.\"\n", "+\tfi\n", "+fi\n", "+\n", "+# Execute passed CMD arguments\n", "+echo-debug \"Passing execution to: $*\"\n", "+# Service mode (run as root)\n", "+if [[ \"$1\" == \"supervisord\" ]]; then\n", "+\texec gosu root supervisord -c /etc/supervisor/supervisord.conf\n", "+# Command mode (run as docker user)\n", "+else\n", "+\texec gosu docker \"$@\"\n", "+fi\n", "\n", "\n", "========================================================================================================================\n", "Add debugging and helper functions\n", "\n", "--- 7.1/startup.sh\n", "+++ 7.1/startup.sh\n", "@@ -88,14 +88,108 @@\n", " \n", " # Convert all Environment Variables Prefixed with SECRET_\n", " convert_secrets\n", " \n", " # Docker user uid/gid mapping to the host user uid/gid\n", " [[ \"$HOST_UID\" != \"\" ]] && [[ \"$HOST_GID\" != \"\" ]] && uid_gid_reset\n", " \n", "+#!/bin/bash\n", "+\n", "+# This script is running as root by default.\n", "+# Switching to the docker user can be done via \"gosu docker <command>\".\n", "+\n", "+HOME_DIR='/home/<USER>'\n", "+\n", "+DEBUG=${DEBUG:-0}\n", "+# Turn debugging ON when cli is started in the service mode\n", "+[[ \"$1\" == \"supervisord\" ]] && DEBUG=1\n", "+echo-debug ()\n", "+{\n", "+\t[[ \"$DEBUG\" != 0 ]] && echo \"$(date +\"%F %H:%M:%S\") | $@\"\n", "+}\n", "+\n", "+uid_gid_reset ()\n", "+{\n", "+\tif [[ \"$HOST_UID\" != \"$(id -u docker)\" ]] || [[ \"$HOST_GID\" != \"$(id -g docker)\" ]]; then\n", "+\t\techo-debug \"Updating docker user uid/gid to $HOST_UID/$HOST_GID to match the host user uid/gid...\"\n", "+\t\tusermod -u \"$HOST_UID\" -o docker\n", "+\t\tgroupmod -g \"$HOST_GID\" -o \"$(id -gn docker)\"\n", "+\tfi\n", "+}\n", "+\n", "+xdebug_enable ()\n", "+{\n", "+\techo-debug \"Enabling xdebug...\"\n", "+\tsudo ln -s /opt/docker-php-ext-xdebug.ini /usr/local/etc/php/conf.d/\n", "+}\n", "+\n", "+# Helper function to render configs from go templates using gomplate\n", "+render_tmpl ()\n", "+{\n", "+\tlocal file=\"${1}\"\n", "+\tlocal tmpl=\"${1}.tmpl\"\n", "+\n", "+\tif [[ -f \"${tmpl}\" ]]; then\n", "+\t\techo-debug \"Rendering template: ${tmpl}...\"\n", "+\t\tgomplate --file \"${tmpl}\" --out \"${file}\"\n", "+\telse\n", "+\t\techo-debug \"Error: Template file not found: ${tmpl}\"\n", "+\t\treturn 1\n", "+\tfi\n", "+}\n", "+\n", "+# Helper function to loop through all environment variables prefixed with SECRET_ and\n", "+# convert to the equivalent variable without SECRET.\n", "+# Example: SECRET_TERMINUS_TOKEN => TERMINUS_TOKEN.\n", "+convert_secrets ()\n", "+{\n", "+\teval 'secrets=(${!SECRET_@})'\n", "+\tfor secret_key in \"${secrets[@]}\"; do\n", "+\t\tkey=${secret_key#SECRET_}\n", "+\t\tsecret_value=${!secret_key}\n", "+\n", "+\t\t# Write new variables to /etc/profile.d/secrets.sh to make them available for all users/sessions\n", "+\t\techo \"export ${key}=\\\"${secret_value}\\\"\" | tee -a \"/etc/profile.d/secrets.sh\" >/dev/null\n", "+\n", "+\t\t# Also export new variables here\n", "+\t\t# This makes them available in the server/php-fpm environment\n", "+\t\teval \"export ${key}=${secret_value}\"\n", "+\tdone\n", "+}\n", "+\n", "+# Pantheon authentication\n", "+terminus_login ()\n", "+{\n", "+\techo-debug \"Authenticating with Pantheon...\"\n", "+\t# This has to be done using the docker user via su to load the user environment\n", "+\t# Note: 'su -' = 'su -l' = 'su --login'\n", "+\tlocal output\n", "+\toutput=$(sudo su - docker -c \"terminus auth:login --machine-token='${TERMINUS_TOKEN}'\" 2>&1)\n", "+\t #>/dev/null 2>&1\n", "+\tif [[ $? != 0 ]]; then\n", "+\t\techo-debug \"ERROR: Pantheon authentication failed.\"\n", "+\t\techo\n", "+\t\techo \"$output\"\n", "+\t\techo\n", "+\tfi\n", "+}\n", "+\n", "+# Process templates\n", "+# Private SSH key\n", "+render_tmpl \"$HOME_DIR/.ssh/id_rsa\"\n", "+chmod 0600 \"$HOME_DIR/.ssh/id_rsa\"\n", "+# Acquia Cloud API config\n", "+render_tmpl \"$HOME_DIR/.acquia/cloudapi.conf\"\n", "+\n", "+# Convert all Environment Variables Prefixed with SECRET_\n", "+convert_secrets\n", "+\n", "+# Docker user uid/gid mapping to the host user uid/gid\n", "+[[ \"$HOST_UID\" != \"\" ]] && [[ \"$HOST_GID\" != \"\" ]] && uid_gid_reset\n", "+\n", " # Enable xdebug\n", " [[ \"$XDEBUG_ENABLED\" != \"\" ]] && [[ \"$XDEBUG_ENABLED\" != \"0\" ]] && xdebug_enable\n", " \n", " # Make sure permissions are correct (after uid/gid change and COPY operations in Dockerfile)\n", " # To not bloat the image size, permissions on the home folder are reset at runtime.\n", " echo-debug \"Resetting permissions on $HOME_DIR and /var/www...\"\n", " chown \"${HOST_UID:-1000}:${HOST_GID:-1000}\" -R \"$HOME_DIR\"\n", "@@ -110,32 +204,7 @@\n", " \n", " # If crontab file is found within project add contents to user crontab file.\n", " if [[ -f ${PROJECT_ROOT}/.docksal/services/cli/crontab ]]; then\n", " \techo-debug \"Loading crontab...\"\n", " \tcat ${PROJECT_ROOT}/.docksal/services/cli/crontab | crontab -u docker -\n", " fi\n", " \n", "-# Initialization steps completed. Create a pid file to mark the container as healthy\n", "-echo-debug \"Preliminary initialization completed.\"\n", "-touch /var/run/cli\n", "-\n", "-# Execute a custom startup script if present\n", "-if [[ -x ${PROJECT_ROOT}/.docksal/services/cli/startup.sh ]]; then\n", "-\techo-debug \"Running custom startup script...\"\n", "-\t# TODO: should we source the script instead?\n", "-\t${PROJECT_ROOT}/.docksal/services/cli/startup.sh\n", "-\tif [[ $? == 0 ]]; then\n", "-\t\techo-debug \"Custom startup script executed successfully.\"\n", "-\telse\n", "-\t\techo-debug \"ERROR: Custom startup script execution failed.\"\n", "-\tfi\n", "-fi\n", "-\n", "-# Execute passed CMD arguments\n", "-echo-debug \"Passing execution to: $*\"\n", "-# Service mode (run as root)\n", "-if [[ \"$1\" == \"supervisord\" ]]; then\n", "-\texec gosu root supervisord -c /etc/supervisor/supervisord.conf\n", "-# Command mode (run as docker user)\n", "-else\n", "-\texec gosu docker \"$@\"\n", "-fi\n", "\n", "\n", "========================================================================================================================\n", "Add git settings\n", "\n", "--- 7.1/startup.sh\n", "+++ 7.1/startup.sh\n", "@@ -135,7 +135,10 @@\n", " # Service mode (run as root)\n", " if [[ \"$1\" == \"supervisord\" ]]; then\n", " \texec gosu root supervisord -c /etc/supervisor/supervisord.conf\n", " # Command mode (run as docker user)\n", " else\n", " \texec gosu docker \"$@\"\n", " fi\n", "+# Apply git settings\n", "+git_settings\n", "+\n", "\n", "\n", "========================================================================================================================\n", "Add custom startup script execution\n", "\n", "--- 7.1/startup.sh\n", "+++ 7.1/startup.sh\n", "@@ -135,7 +135,32 @@\n", " # Service mode (run as root)\n", " if [[ \"$1\" == \"supervisord\" ]]; then\n", " \texec gosu root supervisord -c /etc/supervisor/supervisord.conf\n", " # Command mode (run as docker user)\n", " else\n", " \texec gosu docker \"$@\"\n", " fi\n", "+# Initialization steps completed. Create a pid file to mark the container as healthy\n", "+echo-debug \"Preliminary initialization completed.\"\n", "+touch /var/run/cli\n", "+\n", "+# Execute a custom startup script if present\n", "+if [[ -x ${PROJECT_ROOT}/.docksal/services/cli/startup.sh ]]; then\n", "+\techo-debug \"Running custom startup script...\"\n", "+\t# TODO: should we source the script instead?\n", "+\t${PROJECT_ROOT}/.docksal/services/cli/startup.sh\n", "+\tif [[ $? == 0 ]]; then\n", "+\t\techo-debug \"Custom startup script executed successfully.\"\n", "+\telse\n", "+\t\techo-debug \"ERROR: Custom startup script execution failed.\"\n", "+\tfi\n", "+fi\n", "+\n", "+# Execute passed CMD arguments\n", "+echo-debug \"Passing execution to: $*\"\n", "+# Service mode (run as root)\n", "+if [[ \"$1\" == \"supervisord\" ]]; then\n", "+\texec gosu root supervisord -c /etc/supervisor/supervisord.conf\n", "+# Command mode (run as docker user)\n", "+else\n", "+\texec gosu docker \"$@\"\n", "+fi\n", "\n", "\n", "========================================================================================================================\n", "Refactor startup script\n", "\n", "--- 7.0/startup.sh\n", "+++ 7.0/startup.sh\n", "@@ -1,10 +1,109 @@\n", "+#!/bin/bash\n", "+\n", "+# This script is running as root by default.\n", "+# Switching to the docker user can be done via \"gosu docker <command>\".\n", "+\n", "+HOME_DIR='/home/<USER>'\n", "+\n", "+DEBUG=${DEBUG:-0}\n", "+# Turn debugging ON when cli is started in the service mode\n", "+[[ \"$1\" == \"supervisord\" ]] && DEBUG=1\n", "+echo-debug ()\n", "+{\n", "+\t[[ \"$DEBUG\" != 0 ]] && echo \"$(date +\"%F %H:%M:%S\") | $@\"\n", "+}\n", "+\n", "+uid_gid_reset ()\n", "+{\n", "+\tif [[ \"$HOST_UID\" != \"$(id -u docker)\" ]] || [[ \"$HOST_GID\" != \"$(id -g docker)\" ]]; then\n", "+\t\techo-debug \"Updating docker user uid/gid to $HOST_UID/$HOST_GID to match the host user uid/gid...\"\n", "+\t\tusermod -u \"$HOST_UID\" -o docker\n", "+\t\tgroupmod -g \"$HOST_GID\" -o \"$(id -gn docker)\"\n", "+\tfi\n", "+}\n", "+\n", "+xdebug_enable ()\n", "+{\n", "+\techo-debug \"Enabling xdebug...\"\n", "+\tsudo ln -s /opt/docker-php-ext-xdebug.ini /usr/local/etc/php/conf.d/\n", "+}\n", "+\n", "+add_ssh_key ()\n", "+{\n", "+\techo-debug \"Adding a private SSH key from SECRET_SSH_PRIVATE_KEY...\"\n", "+\t\trender_tmpl \"$HOME_DIR/.ssh/id_rsa\"\n", "+\t\tchmod 0600 \"$HOME_DIR/.ssh/id_rsa\"\n", "+\tfi\n", "+}\n", "+\n", "+# Helper function to render configs from go templates using gomplate\n", "+render_tmpl ()\n", "+{\n", "+\tlocal file=\"${1}\"\n", "+\tlocal tmpl=\"${1}.tmpl\"\n", "+\n", "+\tif [[ -f \"${tmpl}\" ]]; then\n", "+\t\techo-debug \"Rendering template: ${tmpl}...\"\n", "+\t\tgomplate --file \"${tmpl}\" --out \"${file}\"\n", "+\telse\n", "+\t\techo-debug \"Error: Template file not found: ${tmpl}\"\n", "+\t\treturn 1\n", "+\tfi\n", "+}\n", "+\n", "+# Helper function to loop through all environment variables prefixed with SECRET_ and\n", "+# convert to the equivalent variable without SECRET.\n", "+# Example: SECRET_TERMINUS_TOKEN => TERMINUS_TOKEN.\n", "+convert_secrets ()\n", "+{\n", "+\teval 'secrets=(${!SECRET_@})'\n", "+\tfor secret_key in \"${secrets[@]}\"; do\n", "+\t\tkey=${secret_key#SECRET_}\n", "+\t\tsecret_value=${!secret_key}\n", "+\n", "+\t\t# Write new variables to /etc/profile.d/secrets.sh to make them available for all users/sessions\n", "+\t\techo \"export ${key}=\\\"${secret_value}\\\"\" | tee -a \"/etc/profile.d/secrets.sh\" >/dev/null\n", "+\n", "+\t\t# Also export new variables here\n", "+\t\t# This makes them available in the server/php-fpm environment\n", "+\t\teval \"export ${key}=${secret_value}\"\n", "+\tdone\n", "+}\n", "+\n", "+# Pantheon authentication\n", "+terminus_login ()\n", "+{\n", "+\techo-debug \"Authenticating with Pantheon...\"\n", "+\t# This has to be done using the docker user via su to load the user environment\n", "+\t# Note: 'su -' = 'su -l' = 'su --login'\n", "+\tlocal output\n", "+\toutput=$(sudo su - docker -c \"terminus auth:login --machine-token='${TERMINUS_TOKEN}'\" 2>&1)\n", "+\t #>/dev/null 2>&1\n", "+\tif [[ $? != 0 ]]; then\n", "+\t\techo-debug \"ERROR: Pantheon authentication failed.\"\n", "+\t\techo\n", "+\t\techo \"$output\"\n", "+\t\techo\n", "+\tfi\n", "+}\n", "+\n", "+# Git settings\n", "+git_settings ()\n", "+{\n", "+\tif [[ \"$GIT_USER_EMAIL\" != \"\" ]] && [[ \"$GIT_USER_NAME\" != \"\" ]]; then\n", "+\t\t# These must run as the docker user\n", "+\t\techo-debug \"Configuring git...\"\n", "+\t\tgosu docker git config --global user.email \"${GIT_USER_EMAIL}\"\n", "+\t\tgosu docker git config --global user.name \"${GIT_USER_NAME}\"\n", "+\tfi\n", "+}\n", " #!/bin/bash\n", " \n", " # This script is running as root by default.\n", " # Switching to the docker user can be done via \"gosu docker <command>\".\n", " \n", " HOME_DIR='/home/<USER>'\n", " \n", " DEBUG=${DEBUG:-0}\n", " # Turn debugging ON when cli is started in the service mode\n", " [[ \"$1\" == \"supervisord\" ]] && DEBUG=1\n", "@@ -91,72 +190,10 @@\n", " git_settings ()\n", " {\n", " \tif [[ \"$GIT_USER_EMAIL\" != \"\" ]] && [[ \"$GIT_USER_NAME\" != \"\" ]]; then\n", " \t\t# These must run as the docker user\n", " \t\techo-debug \"Configuring git...\"\n", " \t\tgosu docker git config --global user.email \"${GIT_USER_EMAIL}\"\n", " \t\tgosu docker git config --global user.name \"${GIT_USER_NAME}\"\n", " \tfi\n", " }\n", " \n", "-# Process templates\n", "-# Private SSH key\n", "-add_ssh_key\n", "-# Acquia Cloud API config\n", "-render_tmpl \"$HOME_DIR/.acquia/cloudapi.conf\"\n", "-\n", "-# Convert all Environment Variables Prefixed with SECRET_\n", "-convert_secrets\n", "-\n", "-# Docker user uid/gid mapping to the host user uid/gid\n", "-[[ \"$HOST_UID\" != \"\" ]] && [[ \"$HOST_GID\" != \"\" ]] && uid_gid_reset\n", "-\n", "-# Enable xdebug\n", "-[[ \"$XDEBUG_ENABLED\" != \"\" ]] && [[ \"$XDEBUG_ENABLED\" != \"0\" ]] && xdebug_enable\n", "-\n", "-# Make sure permissions are correct (after uid/gid change and COPY operations in Dockerfile)\n", "-# To not bloat the image size, permissions on the home folder are reset at runtime.\n", "-echo-debug \"Resetting permissions on $HOME_DIR and /var/www...\"\n", "-chown \"${HOST_UID:-1000}:${HOST_GID:-1000}\" -R \"$HOME_DIR\"\n", "-# <PERSON><PERSON> resets the project root folder permissions to 0:0 when cli is recreated (e.g. an env variable updated).\n", "-# We apply a fix/workaround for this at startup (non-recursive).\n", "-chown \"${HOST_UID:-1000}:${HOST_GID:-1000}\" /var/www\n", "-\n", "-# Automatically authenticate with Pantheon in Terminus token is present\n", "-# Note: this has to happen after th home directory permissions are reset,\n", "-# otherwise the docker user may not have write access to /home/<USER>\n", "-[[ \"$TERMINUS_TOKEN\" != \"\" ]] && terminus_login\n", "-\n", "-# If crontab file is found within project add contents to user crontab file.\n", "-if [[ -f ${PROJECT_ROOT}/.docksal/services/cli/crontab ]]; then\n", "-\techo-debug \"Loading crontab...\"\n", "-\tcat ${PROJECT_ROOT}/.docksal/services/cli/crontab | crontab -u docker -\n", "-fi\n", "-\n", "-# Apply git settings\n", "-git_settings\n", "-\n", "-# Initialization steps completed. Create a pid file to mark the container as healthy\n", "-echo-debug \"Preliminary initialization completed.\"\n", "-touch /var/run/cli\n", "-\n", "-# Execute a custom startup script if present\n", "-if [[ -x ${PROJECT_ROOT}/.docksal/services/cli/startup.sh ]]; then\n", "-\techo-debug \"Running custom startup script...\"\n", "-\t# TODO: should we source the script instead?\n", "-\t${PROJECT_ROOT}/.docksal/services/cli/startup.sh\n", "-\tif [[ $? == 0 ]]; then\n", "-\t\techo-debug \"Custom startup script executed successfully.\"\n", "-\telse\n", "-\t\techo-debug \"ERROR: Custom startup script execution failed.\"\n", "-\tfi\n", "-fi\n", "-\n", "-# Execute passed CMD arguments\n", "-echo-debug \"Passing execution to: $*\"\n", "-# Service mode (run as root)\n", "-if [[ \"$1\" == \"supervisord\" ]]; then\n", "-\texec gosu root supervisord -c /etc/supervisor/supervisord.conf\n", "-# Command mode (run as docker user)\n", "-else\n", "-\texec gosu docker \"$@\"\n", "-fi\n", "\n", "\n", "========================================================================================================================\n", "Add SSH key injection\n", "\n", "--- 7.0/startup.sh\n", "+++ 7.0/startup.sh\n", "@@ -153,10 +153,13 @@\n", " \n", " # Execute passed CMD arguments\n", " echo-debug \"Passing execution to: $*\"\n", " # Service mode (run as root)\n", " if [[ \"$1\" == \"supervisord\" ]]; then\n", " \texec gosu root supervisord -c /etc/supervisor/supervisord.conf\n", " # Command mode (run as docker user)\n", " else\n", " \texec gosu docker \"$@\"\n", " fi\n", "+# Inject a private SSH key if provided\n", "+[[ \"$SECRET_SSH_PRIVATE_KEY\" != \"\" ]] && add_ssh_key\n", "+\n", "\n", "\n", "========================================================================================================================\n", "Add startup scripts and configurations\n", "\n", "--- 7.0/startup.sh\n", "+++ 7.0/startup.sh\n", "@@ -153,10 +153,69 @@\n", " \n", " # Execute passed CMD arguments\n", " echo-debug \"Passing execution to: $*\"\n", " # Service mode (run as root)\n", " if [[ \"$1\" == \"supervisord\" ]]; then\n", " \texec gosu root supervisord -c /etc/supervisor/supervisord.conf\n", " # Command mode (run as docker user)\n", " else\n", " \texec gosu docker \"$@\"\n", " fi\n", "+# Acquia Cloud API config\n", "+render_tmpl \"$HOME_DIR/.acquia/cloudapi.conf\"\n", "+\n", "+# Convert all Environment Variables Prefixed with SECRET_\n", "+convert_secrets\n", "+\n", "+# Docker user uid/gid mapping to the host user uid/gid\n", "+[[ \"$HOST_UID\" != \"\" ]] && [[ \"$HOST_GID\" != \"\" ]] && uid_gid_reset\n", "+\n", "+# Enable xdebug\n", "+[[ \"$XDEBUG_ENABLED\" != \"\" ]] && [[ \"$XDEBUG_ENABLED\" != \"0\" ]] && xdebug_enable\n", "+\n", "+# Make sure permissions are correct (after uid/gid change and COPY operations in Dockerfile)\n", "+# To not bloat the image size, permissions on the home folder are reset at runtime.\n", "+echo-debug \"Resetting permissions on $HOME_DIR and /var/www...\"\n", "+chown \"${HOST_UID:-1000}:${HOST_GID:-1000}\" -R \"$HOME_DIR\"\n", "+# <PERSON><PERSON> resets the project root folder permissions to 0:0 when cli is recreated (e.g. an env variable updated).\n", "+# We apply a fix/workaround for this at startup (non-recursive).\n", "+chown \"${HOST_UID:-1000}:${HOST_GID:-1000}\" /var/www\n", "+\n", "+# Automatically authenticate with Pantheon in Terminus token is present\n", "+# Note: this has to happen after th home directory permissions are reset,\n", "+# otherwise the docker user may not have write access to /home/<USER>\n", "+[[ \"$TERMINUS_TOKEN\" != \"\" ]] && terminus_login\n", "+\n", "+# If crontab file is found within project add contents to user crontab file.\n", "+if [[ -f ${PROJECT_ROOT}/.docksal/services/cli/crontab ]]; then\n", "+\techo-debug \"Loading crontab...\"\n", "+\tcat ${PROJECT_ROOT}/.docksal/services/cli/crontab | crontab -u docker -\n", "+fi\n", "+\n", "+# Apply git settings\n", "+git_settings\n", "+\n", "+# Initialization steps completed. Create a pid file to mark the container as healthy\n", "+echo-debug \"Preliminary initialization completed.\"\n", "+touch /var/run/cli\n", "+\n", "+# Execute a custom startup script if present\n", "+if [[ -x ${PROJECT_ROOT}/.docksal/services/cli/startup.sh ]]; then\n", "+\techo-debug \"Running custom startup script...\"\n", "+\t# TODO: should we source the script instead?\n", "+\t${PROJECT_ROOT}/.docksal/services/cli/startup.sh\n", "+\tif [[ $? == 0 ]]; then\n", "+\t\techo-debug \"Custom startup script executed successfully.\"\n", "+\telse\n", "+\t\techo-debug \"ERROR: Custom startup script execution failed.\"\n", "+\tfi\n", "+fi\n", "+\n", "+# Execute passed CMD arguments\n", "+echo-debug \"Passing execution to: $*\"\n", "+# Service mode (run as root)\n", "+if [[ \"$1\" == \"supervisord\" ]]; then\n", "+\texec gosu root supervisord -c /etc/supervisor/supervisord.conf\n", "+# Command mode (run as docker user)\n", "+else\n", "+\texec gosu docker \"$@\"\n", "+fi\n", "\n", "\n", "========================================================================================================================\n", "Remove unnecessary keyword\n", "\n", "--- 5.6/startup.sh\n", "+++ 5.6/startup.sh\n", "@@ -27,19 +27,18 @@\n", "@xdebug_enable ()\n", " \techo-debug \"Enabling xdebug...\"\n", " \tsudo ln -s /opt/docker-php-ext-xdebug.ini /usr/local/etc/php/conf.d/\n", " }\n", " \n", " add_ssh_key ()\n", " {\n", " \techo-debug \"Adding a private SSH key from SECRET_SSH_PRIVATE_KEY...\"\n", " \trender_tmpl \"$HOME_DIR/.ssh/id_rsa\"\n", " \tchmod 0600 \"$HOME_DIR/.ssh/id_rsa\"\n", "-\tfi\n", " }\n", " \n", " # Helper function to render configs from go templates using gomplate\n", " render_tmpl ()\n", " {\n", " \tlocal file=\"${1}\"\n", " \tlocal tmpl=\"${1}.tmpl\"\n", " \n", " \tif [[ -f \"${tmpl}\" ]]; then\n", "\n", "\n", "========================================================================================================================\n", "Simplify SSH key addition\n", "\n", "--- 7.2/startup.sh\n", "+++ 7.2/startup.sh\n", "@@ -28,14 +28,13 @@\n", "@xdebug_enable ()\n", " \tsudo ln -s /opt/docker-php-ext-xdebug.ini /usr/local/etc/php/conf.d/\n", " }\n", " \n", " add_ssh_key ()\n", " {\n", "-\techo-debug \"Adding a private SSH key from if [[ \"$SECRET_SSH_PRIVATE_KEY\" != \"\" ]]; then\n", "-\t\trender_tmpl \"$HOME_DIR/.ssh/id_rsa\"\n", "-\t\tchmod 0600 \"$HOME_DIR/.ssh/id_rsa\"\n", "-\tfi\n", "+\techo-debug \"Adding a private SSH key from SECRET_SSH_PRIVATE_KEY...\"\n", "+\trender_tmpl \"$HOME_DIR/.ssh/id_rsa\"\n", "+\tchmod 0600 \"$HOME_DIR/.ssh/id_rsa\"\n", " }\n", " \n", " # Helper function to render configs from go templates using gomplate\n", " render_tmpl ()\n", " {\n", "\n", "\n", "========================================================================================================================\n", "Add documentation for CLI Docker image\n", "\n", "--- README.md\n", "+++ README.md\n", "@@ -113,16 +113,136 @@\n", " ```yml\n", " cli\n", " ...\n", "   environment:\n", "     ...\n", "     - XDEBUG_ENABLED=1\n", "     ...\n", " ```\n", "+# CLI Docker image for Docksal\n", "+\n", "+This image is focused on console tools necessary to develop LAMP stack (and other web) applications.\n", "+\n", "+This image(s) is part of the [Docksal](http://docksal.io) image library.\n", "+\n", "+\n", "+## Features\n", "+\n", "+- php/php-fpm (w/ xdebug), nodejs (via nvm), phyton, ruby\n", "+- Framework specific tools for Drupal, Wordpress, Magento\n", "+- Miscellaneous cli tools for day to day web development\n", "+- Hosting provider cli tools (Acquia, Pantheon, Platform.sh)\n", "+- <PERSON><PERSON> job scheduling\n", "+- Custom startup script support\n", "+- Web based IDE (Cloud9)\n", "+\n", "+\n", "+## Versions and image tag naming convention\n", "+\n", "+- Stable versions\n", "+  - `2.4-php5.6`, `php5.6` - PHP 5.6\n", "+  - `2.4-php7.0`, `php7.0` - PHP 7.0\n", "+  - `2.4-php7.1`, `php7.1` - PHP 7.1\n", "+  - `2.4-php7.2`, `php7.2`, `latest` - PHP 7.2\n", "+- Development versions\n", "+  - `edge-php5.6` - PHP 5.6\n", "+  - `edge-php7.0` - PHP 7.0\n", "+  - `edge-php7.1` - PHP 7.1\n", "+  - `edge-php7.2` - PHP 7.2\n", "+\n", "+\n", "+## Supported languages and tools\n", "+\n", "+### PHP\n", "+\n", "+- php-fpm && php-cli\n", "+- xdebug\n", "+- composer\n", "+- drush (<PERSON><PERSON><PERSON>)\n", "+  - drush launcher with a fallback to a global drush 8 \n", "+  - registry_rebuild module\n", "+  - coder-8.x + phpcs\n", "+- drupal console launcher (Drupal)\n", "+- wp-cli (Wordpress)\n", "+- mg2-codegen (Magento 2)\n", "+\n", "+This image uses the official `php-fpm` images from [Docker Hub](https://hub.docker.com/_/php/) as the base.  \n", "+This means that PHP and all modules are installed from source. Extra modules have to be installed in the same\n", "+manner (installing them with `apt-get` won't work).\n", "+\n", "+### NodeJS\n", "+\n", "+- nvm\n", "+- node\n", "+- npm\n", "+- yarn\n", "+\n", "+NodeJS is installed via `nvm` in the docker user's profile inside the image (`/home/<USER>/.nvm`).\n", "+\n", "+This image follows the LTS release cycle for NodeJS, e.g.:\n", "+\n", "+    Latest LTS Version: 8.11.3 (includes npm 5.6.0)\n", "+    Latest Current Version: 10.7.0 (includes npm 6.1.0) \n", "+\n", "+If you need a different version of node, use `nvm` to install it, e.g, `nvm install 10.7.0`.\n", "+\n", "+Then `nvm use 10.7.0` to use it in the current session or `nvm alias default 10.7.0` to use it by default. \n", "+\n", "+### Python\n", "+\n", "+- python\n", "+\n", "+### Ruby \n", "+\n", "+- ruby\n", "+- gem\n", "+- bundler\n", "+\n", "+### Other notable tools\n", "+\n", "+- git with git-lfs\n", "+- curl, wget\n", "+- zip, unzip\n", "+- mysql, pgsql and mssql cli clients\n", "+- imagemagick\n", "+- mc\n", "+- mhs<PERSON><PERSON>\n", "+- cron\n", "+\n", "+### Hosting provider tools\n", "+\n", "+- Acquia Cloud API drush commands ([Acquia](https://www.acquia.com/)) \n", "+- terminus ([Pantheon](https://pantheon.io/))\n", "+- platform ([Platform.sh](https://platform.sh/))\n", "+\n", "+Also, see the [Secrets](#secrets) section below for more information on managing and using your hosting provider keys.\n", "+\n", "+## Available PHP database drivers\n", "+\n", "+- SQLite - via `sqlite3`, `pdo_sqlite`\n", "+- MySQL - via `mysqli`, `mysqlnd`, `pdo_mysql`\n", "+- PostgreSQL - via `pgsql`, `pdo_pgsql`\n", "+- MSSQL - via `mssql` and `pdo_dblib` for PHP 5.6; `sqlsrv` and `pdo_sqlsrv` for PHP 7.0+\n", "+\n", "+\n", "+## Using PHP Xdebug\n", "+\n", "+Xdebug is disabled by default.\n", "+\n", "+To enable it, run the image with `XDEBUG_ENABLED=1`:\n", "+\n", "+```yml\n", "+cli\n", "+...\n", "+  environment:\n", "+    ...\n", "+    - XDEBUG_ENABLED=1\n", "+    ...\n", "+```\n", " \n", " [See docs](https://docs.docksal.io/en/master/tools/xdebug) on using Xdebug for web and cli PHP debugging.\n", " \n", " \n", " ## Customizing startup\n", " \n", " To run a custom startup script anytime the `cli` container has started, create a `startup.sh` file within the\n", " `.docksal/services/cli` directory. Additionally, make sure that the file is executable as well so that the container\n", "@@ -142,60 +262,8 @@\n", " \n", " The recommended place store secrets in Docksal is the global `$HOME/.docksal/docksal.env` file on the host. From there, \n", " secrets are injected into the `cli` container's environment.\n", " \n", " Below is the list of secrets currently supported.\n", " \n", " `SECRET_SSH_PRIVATE_KEY`\n", " \n", "-Use to pass a private SSH key. The key is stored in `/home/<USER>/.ssh/id_rsa` inside `cli` and will be considered \n", "-by the SSH client **in addition** to the keys loaded in `docksal-ssh-agent` when establishing a SSH connection \n", "-from within `cli`.\n", "-\n", "-`SECRET_ACAPI_EMAIL` and `SECRET_ACAPI_KEY`\n", "-\n", "-Credentials used to authenticate with [Acquia Cloud API](https://docs.acquia.com/acquia-cloud/api).  \n", "-Stored in `/home/<USER>/.acquia/cloudapi.conf` inside `cli`. \n", "-\n", "-Acquia Cloud API can be used via `ac-<command>` group of commands in Drush.\n", "-\n", "-`SECRET_TERMINUS_TOKEN`\n", "-\n", "-Credentials used to authenticate [Terminus](https://pantheon.io/docs/terminus) with Pantheon.\n", "-Stored in `/home/<USER>/.terminus/` inside `cli`.\n", "-\n", "-Terminus is installed and available globally in `cli`.\n", "-\n", "-`SECRET_PLATFORMSH_CLI_TOKEN`\n", "-\n", "-Credentials used to authenticate with the [Platform.sh CLI](https://github.com/platformsh/platformsh-cli) tool.\n", "-Stored in `/home/<USER>/.platform` inside `cli`.\n", "-\n", "-Platform CLI is installed and available globally in `cli`.\n", "-\n", "-\n", "-## Git configuration\n", "-\n", "-When working with git from within the image, it will ask for the `user.email` and `user.name` set before you can commit.\n", "-These can be passed as environment variables and will be applied at the container startup.\n", "-\n", "-```\n", "-GIT_USER_EMAIL=\"***************\"\n", "-GIT_USER_NAME=\"Docksal CLI\"\n", "-``` \n", "-\n", "-\n", "-<a name=\"ide\"></a>\n", "-## Web based IDE (Cloud9)\n", "-\n", "-[Cloud9](https://c9.github.io/core/) is a free, open-source online IDE.\n", "-\n", "-Starting with version 2.3, there is the `ide` flavor of the images, which comes with Cloud9 pre-installed, e.g.:\n", "-\n", "-```\n", "-2.4-php5.6-ide\n", "-2.4-php7.0-ide\n", "-2.4-php7.1-ide\n", "-2.4-php7.2-ide\n", "-``` \n", "-\n", "-[See docs](https://docs.docksal.io/en/master/tools/cloud9/) for using Cloud 9 in Docksal.\n", "\n", "\n", "========================================================================================================================\n", "Add private SSH key support\n", "\n", "--- README.md\n", "+++ README.md\n", "@@ -194,8 +194,9 @@\n", " ```\n", " 2.4-php5.6-ide\n", " 2.4-php7.0-ide\n", " 2.4-php7.1-ide\n", " 2.4-php7.2-ide\n", " ``` \n", " \n", " [See docs](https://docs.docksal.io/en/master/tools/cloud9/) for using Cloud 9 in Docksal.\n", "+Use to pass a private SSH key. The key will be stored in `/home/<USER>/.ssh/id_rsa` inside `cli` and will be considered \n", "\n", "\n", "========================================================================================================================\n", "Clarify SSH client behavior\n", "\n", "--- README.md\n", "+++ README.md\n", "@@ -194,8 +194,11 @@\n", " ```\n", " 2.4-php5.6-ide\n", " 2.4-php7.0-ide\n", " 2.4-php7.1-ide\n", " 2.4-php7.2-ide\n", " ``` \n", " \n", " [See docs](https://docs.docksal.io/en/master/tools/cloud9/) for using Cloud 9 in Docksal.\n", "+by the SSH client **in addition** to the keys loaded in `docksal-ssh-agent` when establishing a SSH connection \n", "+from within `cli`.\n", "+\n", "\n", "\n", "========================================================================================================================\n", "Add private SSH key documentation\n", "\n", "--- README.md\n", "+++ README.md\n", "@@ -194,8 +194,17 @@\n", " ```\n", " 2.4-php5.6-ide\n", " 2.4-php7.0-ide\n", " 2.4-php7.1-ide\n", " 2.4-php7.2-ide\n", " ``` \n", " \n", " [See docs](https://docs.docksal.io/en/master/tools/cloud9/) for using Cloud 9 in Docksal.\n", "+This is useful when you need a project stack to inherit a private SSH key that is not shared with other project stacks \n", "+on the same host (e.g. in shared CI environments).\n", "+\n", "+The value must be base64 encoded, i.e:\n", "+\n", "+```bash\n", "+cat /path/to/some_key_rsa | base64\n", "+```\n", "+\n", "\n", "\n", "========================================================================================================================\n", "Add environment variables and IDE documentation\n", "\n", "--- README.md\n", "+++ README.md\n", "@@ -194,8 +194,56 @@\n", " ```\n", " 2.4-php5.6-ide\n", " 2.4-php7.0-ide\n", " 2.4-php7.1-ide\n", " 2.4-php7.2-ide\n", " ``` \n", " \n", " [See docs](https://docs.docksal.io/en/master/tools/cloud9/) for using Cloud 9 in Docksal.\n", "+`SECRET_ACAPI_EMAIL` and `SECRET_ACAPI_KEY`\n", "+\n", "+Credentials used to authenticate with [Acquia Cloud API](https://docs.acquia.com/acquia-cloud/api).  \n", "+Stored in `/home/<USER>/.acquia/cloudapi.conf` inside `cli`. \n", "+\n", "+Acquia Cloud API can be used via `ac-<command>` group of commands in Drush.\n", "+\n", "+`SECRET_TERMINUS_TOKEN`\n", "+\n", "+Credentials used to authenticate [Terminus](https://pantheon.io/docs/terminus) with Pantheon.\n", "+Stored in `/home/<USER>/.terminus/` inside `cli`.\n", "+\n", "+Terminus is installed and available globally in `cli`.\n", "+\n", "+`SECRET_PLATFORMSH_CLI_TOKEN`\n", "+\n", "+Credentials used to authenticate with the [Platform.sh CLI](https://github.com/platformsh/platformsh-cli) tool.\n", "+Stored in `/home/<USER>/.platform` inside `cli`.\n", "+\n", "+Platform CLI is installed and available globally in `cli`.\n", "+\n", "+\n", "+## Git configuration\n", "+\n", "+When working with git from within the image, it will ask for the `user.email` and `user.name` set before you can commit.\n", "+These can be passed as environment variables and will be applied at the container startup.\n", "+\n", "+```\n", "+GIT_USER_EMAIL=\"***************\"\n", "+GIT_USER_NAME=\"Docksal CLI\"\n", "+``` \n", "+\n", "+\n", "+<a name=\"ide\"></a>\n", "+## Web based IDE (Cloud9)\n", "+\n", "+[Cloud9](https://c9.github.io/core/) is a free, open-source online IDE.\n", "+\n", "+Starting with version 2.3, there is the `ide` flavor of the images, which comes with Cloud9 pre-installed, e.g.:\n", "+\n", "+```\n", "+2.4-php5.6-ide\n", "+2.4-php7.0-ide\n", "+2.4-php7.1-ide\n", "+2.4-php7.2-ide\n", "+``` \n", "+\n", "+[See docs](https://docs.docksal.io/en/master/tools/cloud9/) for using Cloud 9 in Docksal.\n", "\n", "\n", "========================================================================================================================\n", "Extract and refactor startup script\n", "\n", "--- 7.1/startup.sh\n", "+++ 7.1/startup.sh\n", "@@ -91,84 +91,112 @@\n", "@terminus_login ()\n", " \techo-debug \"Authenticating with Pantheon...\"\n", " \t# This has to be done using the docker user via su to load the user environment\n", " \t# Note: Using 'su -l' to initiate a login session and have .profile sourced for the docker user\n", " \tlocal command=\"terminus auth:login --machine-token='${TERMINUS_TOKEN}'\"\n", " \tlocal output=$(su -l docker -c \"${command}\" 2>&1)\n", " \tif [[ $? != 0 ]]; then\n", "+#!/bin/bash\n", "+\n", "+# This script is running as root by default.\n", "+# Switching to the docker user can be done via \"gosu docker <command>\".\n", "+\n", "+HOME_DIR='/home/<USER>'\n", "+\n", "+DEBUG=${DEBUG:-0}\n", "+# Turn debugging ON when cli is started in the service mode\n", "+[[ \"$1\" == \"supervisord\" ]] && DEBUG=1\n", "+echo-debug ()\n", "+{\n", "+\t[[ \"$DEBUG\" != 0 ]] && echo \"$(date +\"%F %H:%M:%S\") | $@\"\n", "+}\n", "+\n", "+uid_gid_reset ()\n", "+{\n", "+\tif [[ \"$HOST_UID\" != \"$(id -u docker)\" ]] || [[ \"$HOST_GID\" != \"$(id -g docker)\" ]]; then\n", "+\t\techo-debug \"Updating docker user uid/gid to $HOST_UID/$HOST_GID to match the host user uid/gid...\"\n", "+\t\tusermod -u \"$HOST_UID\" -o docker\n", "+\t\tgroupmod -g \"$HOST_GID\" -o \"$(id -gn docker)\"\n", "+\tfi\n", "+}\n", "+\n", "+xdebug_enable ()\n", "+{\n", "+\techo-debug \"Enabling xdebug...\"\n", "+\tln -s /opt/docker-php-ext-xdebug.ini /usr/local/etc/php/conf.d/\n", "+}\n", "+\n", "+add_ssh_key ()\n", "+{\n", "+\techo-debug \"Adding a private SSH key from SECRET_SSH_PRIVATE_KEY...\"\n", "+\trender_tmpl \"$HOME_DIR/.ssh/id_rsa\"\n", "+\tchmod 0600 \"$HOME_DIR/.ssh/id_rsa\"\n", "+}\n", "+\n", "+# Helper function to render configs from go templates using gomplate\n", "+render_tmpl ()\n", "+{\n", "+\tlocal file=\"${1}\"\n", "+\tlocal tmpl=\"${1}.tmpl\"\n", "+\n", "+\tif [[ -f \"${tmpl}\" ]]; then\n", "+\t\techo-debug \"Rendering template: ${tmpl}...\"\n", "+\t\tgomplate --file \"${tmpl}\" --out \"${file}\"\n", "+\telse\n", "+\t\techo-debug \"Error: Template file not found: ${tmpl}\"\n", "+\t\treturn 1\n", "+\tfi\n", "+}\n", "+\n", "+# Helper function to loop through all environment variables prefixed with SECRET_ and\n", "+# convert to the equivalent variable without SECRET.\n", "+# Example: SECRET_TERMINUS_TOKEN => TERMINUS_TOKEN.\n", "+convert_secrets ()\n", "+{\n", "+\teval 'secrets=(${!SECRET_@})'\n", "+\tfor secret_key in \"${secrets[@]}\"; do\n", "+\t\tkey=${secret_key#SECRET_}\n", "+\t\tsecret_value=${!secret_key}\n", "+\n", "+\t\t# Write new variables to /etc/profile.d/secrets.sh to make them available for all users/sessions\n", "+\t\techo \"export ${key}=\\\"${secret_value}\\\"\" | tee -a \"/etc/profile.d/secrets.sh\" >/dev/null\n", "+\n", "+\t\t# Also export new variables here\n", "+\t\t# This makes them available in the server/php-fpm environment\n", "+\t\teval \"export ${key}=${secret_value}\"\n", "+\tdone\n", "+}\n", "+\n", "+# Acquia Cloud API login\n", "+acquia_login ()\n", "+{\n", "+\techo-debug \"Authenticating with Acquia...\"\n", "+\t# This has to be done using the docker user via su to load the user environment\n", "+\t# Note: Using 'su -l' to initiate a login session and have .profile sourced for the docker user\n", "+\tlocal command=\"drush ac-api-login --email='${ACAPI_EMAIL}' --key='${ACAPI_KEY}' --endpoint='https://cloudapi.acquia.com/v1' && drush ac-site-list\"\n", "+\tlocal output=$(su -l docker -c \"${command}\" 2>&1)\n", "+\tif [[ $? != 0 ]]; then\n", "+\t\techo-debug \"ERROR: Acquia authentication failed.\"\n", "+\t\techo\n", "+\t\techo \"$output\"\n", "+\t\techo\n", "+\tfi\n", "+}\n", "+\n", "+# Pantheon (terminus) login\n", "+terminus_login ()\n", "+{\n", "+\techo-debug \"Authenticating with Pantheon...\"\n", "+\t# This has to be done using the docker user via su to load the user environment\n", "+\t# Note: Using 'su -l' to initiate a login session and have .profile sourced for the docker user\n", "+\tlocal command=\"terminus auth:login --machine-token='${TERMINUS_TOKEN}'\"\n", "+\tlocal output=$(su -l docker -c \"${command}\" 2>&1)\n", "+\tif [[ $? != 0 ]]; then\n", " \t\techo-debug \"ERROR: Pantheon authentication failed.\"\n", " \t\techo\n", " \t\techo \"$output\"\n", " \t\techo\n", " \tfi\n", " }\n", " \n", " # Git settings\n", " git_settings ()\n", " {\n", "-\tif [[ \"$GIT_USER_EMAIL\" != \"\" ]] && [[ \"$GIT_USER_NAME\" != \"\" ]]; then\n", "-\t\t# These must be run as the docker user\n", "-\t\techo-debug \"Configuring git...\"\n", "-\tgosu docker git config --global user.email \"${GIT_USER_EMAIL}\"\n", "-\t\tgosu docker git config --global user.name \"${GIT_USER_NAME}\"\n", "-}\n", "-\n", "-# Inject a private SSH key if provided\n", "-[[ \"$SECRET_SSH_PRIVATE_KEY\" != \"\" ]] && add_ssh_key\n", "-\n", "-# Convert all Environment Variables Prefixed with SECRET_\n", "-convert_secrets\n", "-\n", "-# Docker user uid/gid mapping to the host user uid/gid\n", "-[[ \"$HOST_UID\" != \"\" ]] && [[ \"$HOST_GID\" != \"\" ]] && uid_gid_reset\n", "-\n", "-# Enable xdebug\n", "-[[ \"$XDEBUG_ENABLED\" != \"\" ]] && [[ \"$XDEBUG_ENABLED\" != \"0\" ]] && xdebug_enable\n", "-\n", "-# Make sure permissions are correct (after uid/gid change and COPY operations in Dockerfile)\n", "-# To not bloat the image size, permissions on the home folder are reset at runtime.\n", "-echo-debug \"Resetting permissions on $HOME_DIR and /var/www...\"\n", "-chown \"${HOST_UID:-1000}:${HOST_GID:-1000}\" -R \"$HOME_DIR\"\n", "-# <PERSON><PERSON> resets the project root folder permissions to 0:0 when cli is recreated (e.g. an env variable updated).\n", "-# We apply a fix/workaround for this at startup (non-recursive).\n", "-chown \"${HOST_UID:-1000}:${HOST_GID:-1000}\" /var/www\n", "-\n", "-# These have to happen after the home directory permissions are reset,\n", "-# otherwise the docker user may not have write access to /home/<USER>\n", "-# Acquia Cloud API config\n", "-[[ \"$ACAPI_EMAIL\" != \"\" ]] && [[ \"$ACAPI_KEY\" != \"\" ]] && acquia_login\n", "-# Automatically authenticate with Pantheon if Terminus token is present\n", "-[[ \"$TERMINUS_TOKEN\" != \"\" ]] && terminus_login\n", "-\n", "-# If crontab file is found within project add contents to user crontab file.\n", "-if [[ -f ${PROJECT_ROOT}/.docksal/services/cli/crontab ]]; then\n", "-\techo-debug \"Loading crontab...\"\n", "-\tcat ${PROJECT_ROOT}/.docksal/services/cli/crontab | crontab -u docker -\n", "-fi\n", "-\n", "-# Apply git settings\n", "-[[ \"$GIT_USER_EMAIL\" != \"\" ]] && [[ \"$GIT_USER_NAME\" != \"\" ]] && git_settings\n", "-\n", "-# Initialization steps completed. Create a pid file to mark the container as healthy\n", "-echo-debug \"Preliminary initialization completed.\"\n", "-touch /var/run/cli\n", "-\n", "-# Execute a custom startup script if present\n", "-if [[ -x ${PROJECT_ROOT}/.docksal/services/cli/startup.sh ]]; then\n", "-\techo-debug \"Running custom startup script...\"\n", "-\t# TODO: should we source the script instead?\n", "-\t${PROJECT_ROOT}/.docksal/services/cli/startup.sh\n", "-\tif [[ $? == 0 ]]; then\n", "-\t\techo-debug \"Custom startup script executed successfully.\"\n", "-\telse\n", "-\t\techo-debug \"ERROR: Custom startup script execution failed.\"\n", "-\tfi\n", "-fi\n", "-\n", "-# Execute passed CMD arguments\n", "-echo-debug \"Passing execution to: $*\"\n", "-# Service mode (run as root)\n", "-if [[ \"$1\" == \"supervisord\" ]]; then\n", "-\texec gosu root supervisord -c /etc/supervisor/supervisord.conf\n", "-# Command mode (run as docker user)\n", "-else\n", "-\texec gosu docker \"$@\"\n", "-fi\n", "\n", "\n", "========================================================================================================================\n", "Configure git user credentials\n", "\n", "--- 7.1/startup.sh\n", "+++ 7.1/startup.sh\n", "@@ -169,6 +169,10 @@\n", " if [[ \"$1\" == \"supervisord\" ]]; then\n", " \texec gosu root supervisord -c /etc/supervisor/supervisord.conf\n", " # Command mode (run as docker user)\n", " else\n", " \texec gosu docker \"$@\"\n", " fi\n", "+\t# These must be run as the docker user\n", "+\techo-debug \"Configuring git...\"\n", "+\tgosu docker git config --global user.email \"${GIT_USER_EMAIL}\"\n", "+\tgosu docker git config --global user.name \"${GIT_USER_NAME}\"\n", "\n", "\n", "========================================================================================================================\n", "Add initialization scripts\n", "\n", "--- 7.1/startup.sh\n", "+++ 7.1/startup.sh\n", "@@ -169,6 +169,69 @@\n", " if [[ \"$1\" == \"supervisord\" ]]; then\n", " \texec gosu root supervisord -c /etc/supervisor/supervisord.conf\n", " # Command mode (run as docker user)\n", " else\n", " \texec gosu docker \"$@\"\n", " fi\n", "+}\n", "+\n", "+# Inject a private SSH key if provided\n", "+[[ \"$SECRET_SSH_PRIVATE_KEY\" != \"\" ]] && add_ssh_key\n", "+\n", "+# Convert all Environment Variables Prefixed with SECRET_\n", "+convert_secrets\n", "+\n", "+# Docker user uid/gid mapping to the host user uid/gid\n", "+[[ \"$HOST_UID\" != \"\" ]] && [[ \"$HOST_GID\" != \"\" ]] && uid_gid_reset\n", "+\n", "+# Enable xdebug\n", "+[[ \"$XDEBUG_ENABLED\" != \"\" ]] && [[ \"$XDEBUG_ENABLED\" != \"0\" ]] && xdebug_enable\n", "+\n", "+# Make sure permissions are correct (after uid/gid change and COPY operations in Dockerfile)\n", "+# To not bloat the image size, permissions on the home folder are reset at runtime.\n", "+echo-debug \"Resetting permissions on $HOME_DIR and /var/www...\"\n", "+chown \"${HOST_UID:-1000}:${HOST_GID:-1000}\" -R \"$HOME_DIR\"\n", "+# <PERSON><PERSON> resets the project root folder permissions to 0:0 when cli is recreated (e.g. an env variable updated).\n", "+# We apply a fix/workaround for this at startup (non-recursive).\n", "+chown \"${HOST_UID:-1000}:${HOST_GID:-1000}\" /var/www\n", "+\n", "+# These have to happen after the home directory permissions are reset,\n", "+# otherwise the docker user may not have write access to /home/<USER>\n", "+# Acquia Cloud API config\n", "+[[ \"$ACAPI_EMAIL\" != \"\" ]] && [[ \"$ACAPI_KEY\" != \"\" ]] && acquia_login\n", "+# Automatically authenticate with Pantheon if Terminus token is present\n", "+[[ \"$TERMINUS_TOKEN\" != \"\" ]] && terminus_login\n", "+\n", "+# If crontab file is found within project add contents to user crontab file.\n", "+if [[ -f ${PROJECT_ROOT}/.docksal/services/cli/crontab ]]; then\n", "+\techo-debug \"Loading crontab...\"\n", "+\tcat ${PROJECT_ROOT}/.docksal/services/cli/crontab | crontab -u docker -\n", "+fi\n", "+\n", "+# Apply git settings\n", "+[[ \"$GIT_USER_EMAIL\" != \"\" ]] && [[ \"$GIT_USER_NAME\" != \"\" ]] && git_settings\n", "+\n", "+# Initialization steps completed. Create a pid file to mark the container as healthy\n", "+echo-debug \"Preliminary initialization completed.\"\n", "+touch /var/run/cli\n", "+\n", "+# Execute a custom startup script if present\n", "+if [[ -x ${PROJECT_ROOT}/.docksal/services/cli/startup.sh ]]; then\n", "+\techo-debug \"Running custom startup script...\"\n", "+\t# TODO: should we source the script instead?\n", "+\t${PROJECT_ROOT}/.docksal/services/cli/startup.sh\n", "+\tif [[ $? == 0 ]]; then\n", "+\t\techo-debug \"Custom startup script executed successfully.\"\n", "+\telse\n", "+\t\techo-debug \"ERROR: Custom startup script execution failed.\"\n", "+\tfi\n", "+fi\n", "+\n", "+# Execute passed CMD arguments\n", "+echo-debug \"Passing execution to: $*\"\n", "+# Service mode (run as root)\n", "+if [[ \"$1\" == \"supervisord\" ]]; then\n", "+\texec gosu root supervisord -c /etc/supervisor/supervisord.conf\n", "+# Command mode (run as docker user)\n", "+else\n", "+\texec gosu docker \"$@\"\n", "+fi\n", "\n", "\n", "========================================================================================================================\n", "Extract and refactor shell script functions\n", "\n", "--- 7.1/startup.sh\n", "+++ 7.1/startup.sh\n", "@@ -96,79 +96,111 @@\n", "@terminus_login ()\n", " \tif [[ $? != 0 ]]; then\n", " \t\techo-debug \"ERROR: Pantheon authentication failed.\"\n", " \t\techo\n", " \t\techo \"$output\"\n", " \t\techo\n", "+#!/bin/bash\n", "+\n", "+# This script is running as root by default.\n", "+# Switching to the docker user can be done via \"gosu docker <command>\".\n", "+\n", "+HOME_DIR='/home/<USER>'\n", "+\n", "+DEBUG=${DEBUG:-0}\n", "+# Turn debugging ON when cli is started in the service mode\n", "+[[ \"$1\" == \"supervisord\" ]] && DEBUG=1\n", "+echo-debug ()\n", "+{\n", "+\t[[ \"$DEBUG\" != 0 ]] && echo \"$(date +\"%F %H:%M:%S\") | $@\"\n", "+}\n", "+\n", "+uid_gid_reset ()\n", "+{\n", "+\tif [[ \"$HOST_UID\" != \"$(id -u docker)\" ]] || [[ \"$HOST_GID\" != \"$(id -g docker)\" ]]; then\n", "+\t\techo-debug \"Updating docker user uid/gid to $HOST_UID/$HOST_GID to match the host user uid/gid...\"\n", "+\t\tusermod -u \"$HOST_UID\" -o docker\n", "+\t\tgroupmod -g \"$HOST_GID\" -o \"$(id -gn docker)\"\n", "+\tfi\n", "+}\n", "+\n", "+xdebug_enable ()\n", "+{\n", "+\techo-debug \"Enabling xdebug...\"\n", "+\tln -s /opt/docker-php-ext-xdebug.ini /usr/local/etc/php/conf.d/\n", "+}\n", "+\n", "+add_ssh_key ()\n", "+{\n", "+\techo-debug \"Adding a private SSH key from SECRET_SSH_PRIVATE_KEY...\"\n", "+\trender_tmpl \"$HOME_DIR/.ssh/id_rsa\"\n", "+\tchmod 0600 \"$HOME_DIR/.ssh/id_rsa\"\n", "+}\n", "+\n", "+# Helper function to render configs from go templates using gomplate\n", "+render_tmpl ()\n", "+{\n", "+\tlocal file=\"${1}\"\n", "+\tlocal tmpl=\"${1}.tmpl\"\n", "+\n", "+\tif [[ -f \"${tmpl}\" ]]; then\n", "+\t\techo-debug \"Rendering template: ${tmpl}...\"\n", "+\t\tgomplate --file \"${tmpl}\" --out \"${file}\"\n", "+\telse\n", "+\t\techo-debug \"Error: Template file not found: ${tmpl}\"\n", "+\t\treturn 1\n", "+\tfi\n", "+}\n", "+\n", "+# Helper function to loop through all environment variables prefixed with SECRET_ and\n", "+# convert to the equivalent variable without SECRET.\n", "+# Example: SECRET_TERMINUS_TOKEN => TERMINUS_TOKEN.\n", "+convert_secrets ()\n", "+{\n", "+\teval 'secrets=(${!SECRET_@})'\n", "+\tfor secret_key in \"${secrets[@]}\"; do\n", "+\t\tkey=${secret_key#SECRET_}\n", "+\t\tsecret_value=${!secret_key}\n", "+\n", "+\t\t# Write new variables to /etc/profile.d/secrets.sh to make them available for all users/sessions\n", "+\t\techo \"export ${key}=\\\"${secret_value}\\\"\" | tee -a \"/etc/profile.d/secrets.sh\" >/dev/null\n", "+\n", "+\t\t# Also export new variables here\n", "+\t\t# This makes them available in the server/php-fpm environment\n", "+\t\teval \"export ${key}=${secret_value}\"\n", "+\tdone\n", "+}\n", "+\n", "+# Acquia Cloud API login\n", "+acquia_login ()\n", "+{\n", "+\techo-debug \"Authenticating with Acquia...\"\n", "+\t# This has to be done using the docker user via su to load the user environment\n", "+\t# Note: Using 'su -l' to initiate a login session and have .profile sourced for the docker user\n", "+\tlocal command=\"drush ac-api-login --email='${ACAPI_EMAIL}' --key='${ACAPI_KEY}' --endpoint='https://cloudapi.acquia.com/v1' && drush ac-site-list\"\n", "+\tlocal output=$(su -l docker -c \"${command}\" 2>&1)\n", "+\tif [[ $? != 0 ]]; then\n", "+\t\techo-debug \"ERROR: Acquia authentication failed.\"\n", "+\t\techo\n", "+\t\techo \"$output\"\n", "+\t\techo\n", "+\tfi\n", "+}\n", "+\n", "+# Pantheon (terminus) login\n", "+terminus_login ()\n", "+{\n", "+\techo-debug \"Authenticating with Pantheon...\"\n", "+\t# This has to be done using the docker user via su to load the user environment\n", "+\t# Note: Using 'su -l' to initiate a login session and have .profile sourced for the docker user\n", "+\tlocal command=\"terminus auth:login --machine-token='${TERMINUS_TOKEN}'\"\n", "+\tlocal output=$(su -l docker -c \"${command}\" 2>&1)\n", "+\tif [[ $? != 0 ]]; then\n", "+\t\techo-debug \"ERROR: Pantheon authentication failed.\"\n", "+\t\techo\n", "+\t\techo \"$output\"\n", "+\t\techo\n", " \tfi\n", " }\n", " \n", " # Git settings\n", " git_settings ()\n", " {\n", "-\t# These must run as the docker user\n", "-\techo-debug \"Configuring git...\"\n", "-\t\tgosu docker git config --global user.email \"${GIT_USER_EMAIL}\"\n", "-\t\tgosu docker git config --global user.name \"${GIT_USER_NAME}\"\n", "-\tfi\n", "-}\n", "-\n", "-# Inject a private SSH key if provided\n", "-[[ \"$SECRET_SSH_PRIVATE_KEY\" != \"\" ]] && add_ssh_key\n", "-\n", "-# Convert all Environment Variables Prefixed with SECRET_\n", "-convert_secrets\n", "-\n", "-# Docker user uid/gid mapping to the host user uid/gid\n", "-[[ \"$HOST_UID\" != \"\" ]] && [[ \"$HOST_GID\" != \"\" ]] && uid_gid_reset\n", "-\n", "-# Enable xdebug\n", "-[[ \"$XDEBUG_ENABLED\" != \"\" ]] && [[ \"$XDEBUG_ENABLED\" != \"0\" ]] && xdebug_enable\n", "-\n", "-# Make sure permissions are correct (after uid/gid change and COPY operations in Dockerfile)\n", "-# To not bloat the image size, permissions on the home folder are reset at runtime.\n", "-echo-debug \"Resetting permissions on $HOME_DIR and /var/www...\"\n", "-chown \"${HOST_UID:-1000}:${HOST_GID:-1000}\" -R \"$HOME_DIR\"\n", "-# <PERSON><PERSON> resets the project root folder permissions to 0:0 when cli is recreated (e.g. an env variable updated).\n", "-# We apply a fix/workaround for this at startup (non-recursive).\n", "-chown \"${HOST_UID:-1000}:${HOST_GID:-1000}\" /var/www\n", "-\n", "-# These have to happen after the home directory permissions are reset,\n", "-# otherwise the docker user may not have write access to /home/<USER>\n", "-# Acquia Cloud API config\n", "-[[ \"$ACAPI_EMAIL\" != \"\" ]] && [[ \"$ACAPI_KEY\" != \"\" ]] && acquia_login\n", "-# Automatically authenticate with Pantheon if Terminus token is present\n", "-[[ \"$TERMINUS_TOKEN\" != \"\" ]] && terminus_login\n", "-\n", "-# If crontab file is found within project add contents to user crontab file.\n", "-if [[ -f ${PROJECT_ROOT}/.docksal/services/cli/crontab ]]; then\n", "-\techo-debug \"Loading crontab...\"\n", "-\tcat ${PROJECT_ROOT}/.docksal/services/cli/crontab | crontab -u docker -\n", "-fi\n", "-\n", "-# Apply git settings\n", "-git_settings\n", "-\n", "-# Initialization steps completed. Create a pid file to mark the container as healthy\n", "-echo-debug \"Preliminary initialization completed.\"\n", "-touch /var/run/cli\n", "-\n", "-# Execute a custom startup script if present\n", "-if [[ -x ${PROJECT_ROOT}/.docksal/services/cli/startup.sh ]]; then\n", "-\techo-debug \"Running custom startup script...\"\n", "-\t# TODO: should we source the script instead?\n", "-\t${PROJECT_ROOT}/.docksal/services/cli/startup.sh\n", "-\tif [[ $? == 0 ]]; then\n", "-\t\techo-debug \"Custom startup script executed successfully.\"\n", "-\telse\n", "-\t\techo-debug \"ERROR: Custom startup script execution failed.\"\n", "-\tfi\n", "-fi\n", "-\n", "-# Execute passed CMD arguments\n", "-echo-debug \"Passing execution to: $*\"\n", "-# Service mode (run as root)\n", "-if [[ \"$1\" == \"supervisord\" ]]; then\n", "-\texec gosu root supervisord -c /etc/supervisor/supervisord.conf\n", "-# Command mode (run as docker user)\n", "-else\n", "-\texec gosu docker \"$@\"\n", "-fi\n", "\n", "\n", "========================================================================================================================\n", "Configure git user settings\n", "\n", "--- 7.1/startup.sh\n", "+++ 7.1/startup.sh\n", "@@ -170,5 +170,9 @@\n", " \texec gosu root supervisord -c /etc/supervisor/supervisord.conf\n", " # Command mode (run as docker user)\n", " else\n", " \texec gosu docker \"$@\"\n", " fi\n", "+\t# These must be run as the docker user\n", "+\techo-debug \"Configuring git...\"\n", "+\tgosu docker git config --global user.email \"${GIT_USER_EMAIL}\"\n", "+\tgosu docker git config --global user.name \"${GIT_USER_NAME}\"\n", "\n", "\n", "========================================================================================================================\n", "Add initialization scripts\n", "\n", "--- 7.1/startup.sh\n", "+++ 7.1/startup.sh\n", "@@ -170,5 +170,68 @@\n", " \texec gosu root supervisord -c /etc/supervisor/supervisord.conf\n", " # Command mode (run as docker user)\n", " else\n", " \texec gosu docker \"$@\"\n", " fi\n", "+}\n", "+\n", "+# Inject a private SSH key if provided\n", "+[[ \"$SECRET_SSH_PRIVATE_KEY\" != \"\" ]] && add_ssh_key\n", "+\n", "+# Convert all Environment Variables Prefixed with SECRET_\n", "+convert_secrets\n", "+\n", "+# Docker user uid/gid mapping to the host user uid/gid\n", "+[[ \"$HOST_UID\" != \"\" ]] && [[ \"$HOST_GID\" != \"\" ]] && uid_gid_reset\n", "+\n", "+# Enable xdebug\n", "+[[ \"$XDEBUG_ENABLED\" != \"\" ]] && [[ \"$XDEBUG_ENABLED\" != \"0\" ]] && xdebug_enable\n", "+\n", "+# Make sure permissions are correct (after uid/gid change and COPY operations in Dockerfile)\n", "+# To not bloat the image size, permissions on the home folder are reset at runtime.\n", "+echo-debug \"Resetting permissions on $HOME_DIR and /var/www...\"\n", "+chown \"${HOST_UID:-1000}:${HOST_GID:-1000}\" -R \"$HOME_DIR\"\n", "+# <PERSON><PERSON> resets the project root folder permissions to 0:0 when cli is recreated (e.g. an env variable updated).\n", "+# We apply a fix/workaround for this at startup (non-recursive).\n", "+chown \"${HOST_UID:-1000}:${HOST_GID:-1000}\" /var/www\n", "+\n", "+# These have to happen after the home directory permissions are reset,\n", "+# otherwise the docker user may not have write access to /home/<USER>\n", "+# Acquia Cloud API config\n", "+[[ \"$ACAPI_EMAIL\" != \"\" ]] && [[ \"$ACAPI_KEY\" != \"\" ]] && acquia_login\n", "+# Automatically authenticate with Pantheon if Terminus token is present\n", "+[[ \"$TERMINUS_TOKEN\" != \"\" ]] && terminus_login\n", "+\n", "+# If crontab file is found within project add contents to user crontab file.\n", "+if [[ -f ${PROJECT_ROOT}/.docksal/services/cli/crontab ]]; then\n", "+\techo-debug \"Loading crontab...\"\n", "+\tcat ${PROJECT_ROOT}/.docksal/services/cli/crontab | crontab -u docker -\n", "+fi\n", "+\n", "+# Apply git settings\n", "+git_settings\n", "+\n", "+# Initialization steps completed. Create a pid file to mark the container as healthy\n", "+echo-debug \"Preliminary initialization completed.\"\n", "+touch /var/run/cli\n", "+\n", "+# Execute a custom startup script if present\n", "+if [[ -x ${PROJECT_ROOT}/.docksal/services/cli/startup.sh ]]; then\n", "+\techo-debug \"Running custom startup script...\"\n", "+\t# TODO: should we source the script instead?\n", "+\t${PROJECT_ROOT}/.docksal/services/cli/startup.sh\n", "+\tif [[ $? == 0 ]]; then\n", "+\t\techo-debug \"Custom startup script executed successfully.\"\n", "+\telse\n", "+\t\techo-debug \"ERROR: Custom startup script execution failed.\"\n", "+\tfi\n", "+fi\n", "+\n", "+# Execute passed CMD arguments\n", "+echo-debug \"Passing execution to: $*\"\n", "+# Service mode (run as root)\n", "+if [[ \"$1\" == \"supervisord\" ]]; then\n", "+\texec gosu root supervisord -c /etc/supervisor/supervisord.conf\n", "+# Command mode (run as docker user)\n", "+else\n", "+\texec gosu docker \"$@\"\n", "+fi\n", "\n", "\n", "========================================================================================================================\n", "Update mail configuration\n", "\n", "--- 5.6/config/php/zz-php.ini\n", "+++ 5.6/config/php/zz-php.ini\n", "@@ -3,16 +3,18 @@\n", " [php]\n", " memory_limit = 1024M\n", " max_execution_time = 600\n", " always_populate_raw_post_data = -1\n", " sendmail_path = /bin/true\n", " date.timezone = UTC\n", " display_errors = On\n", " display_startup_errors = On\n", "+\n", "+[mail]\n", " ; Enable Mailhog integration by default\n", "-sendmail_path = '/usr/local/bin/mhsendmail --smtp-addr=mail:1025'\n", "+sendmail_path = '/usr/bin/msmtp -t --host=mail --port=1025'\n", " \n", " ; Extention settings\n", " [opcache]\n", " opcache.memory_consumption = 128\n", " [blackfire]\n", " blackfire.agent_socket = 'tcp://blackfire:8707'\n", "\n", "\n", "========================================================================================================================\n", "Update sendmail path\n", "\n", "--- 7.1/config/php/zz-php.ini\n", "+++ 7.1/config/php/zz-php.ini\n", "@@ -4,14 +4,16 @@\n", " memory_limit = 1024M\n", " max_execution_time = 600\n", " sendmail_path = /bin/true\n", " date.timezone = UTC\n", " display_errors = On\n", " display_startup_errors = On\n", "+\n", "+[mail]\n", " ; Enable Mailhog integration by default\n", "-sendmail_path = '/usr/local/bin/mhsendmail --smtp-addr=mail:1025'\n", "+sendmail_path = '/usr/bin/msmtp -t --host=mail --port=1025'\n", " \n", " ; Extention settings\n", " [opcache]\n", " opcache.memory_consumption = 128\n", " [blackfire]\n", " blackfire.agent_socket = 'tcp://blackfire:8707'\n", "\n", "\n", "========================================================================================================================\n", "Update mail configuration\n", "\n", "--- 7.2/config/php/zz-php.ini\n", "+++ 7.2/config/php/zz-php.ini\n", "@@ -7,8 +7,10 @@\n", " date.timezone = UTC\n", " display_errors = On\n", " display_startup_errors = On\n", "+\n", "+[mail]\n", " ; Enable Mailhog integration by default\n", "-sendmail_path = '/usr/local/bin/mhsendmail --smtp-addr=mail:1025'\n", "+sendmail_path = '/usr/bin/msmtp -t --host=mail --port=1025'\n", " \n", " ; Extention settings\n", " [opcache]\n", "\n", "\n", "========================================================================================================================\n", "Add shebang line and whitespace\n", "\n", "--- tests/scripts/php-fpm.sh\n", "+++ tests/scripts/php-fpm.sh\n", "@@ -1,11 +1,15 @@\n", "+#!/usr/bin/env bash\n", "+\n", "+export SCRIPT_FILENAME=\"/var/www/docroot/${1}\"\n", "+export REQUEST_URI=/\n", "+export QUERY_STRING=\n", " #!/usr/bin/env bash\n", " \n", " export SCRIPT_FILENAME=\"/var/www/docroot/${1}\"\n", " export REQUEST_URI=/\n", " export QUERY_STRING=\n", " export REQUEST_METHOD=GET\n", " \n", " # \"sed 's/\\xC2\\xA0/ /g'\" - replaces non-breaking spaces (&nbsp) with regular spaces.\n", " # This can be a nightmare to debug, since nbsp's are identical to spaces in a text editor.\n", " # Note: this sed does NOT work on Mac, so make sure it's only run inside a container.\n", "-cgi-fcgi -bind -connect 127.0.0.1:9000 | html2text | sed 's/\\xC2\\xA0/ /g'\n", "\n", "\n", "========================================================================================================================\n", "Add comment\n", "\n", "--- tests/scripts/php-fpm.sh\n", "+++ tests/scripts/php-fpm.sh\n", "@@ -7,5 +7,6 @@\n", " \n", " # \"sed 's/\\xC2\\xA0/ /g'\" - replaces non-breaking spaces (&nbsp) with regular spaces.\n", " # This can be a nightmare to debug, since nbsp's are identical to spaces in a text editor.\n", " # Note: this sed does NOT work on Mac, so make sure it's only run inside a container.\n", " cgi-fcgi -bind -connect 127.0.0.1:9000 | html2text | sed 's/\\xC2\\xA0/ /g'\n", "+# See https://superuser.com/questions/517847/use-sed-to-replace-nbsp-160-hex-00a0-octal-240-non-breaking-space\n", "\n", "\n", "========================================================================================================================\n", "Duplicate command\n", "\n", "--- tests/scripts/php-fpm.sh\n", "+++ tests/scripts/php-fpm.sh\n", "@@ -7,5 +7,6 @@\n", " \n", " # \"sed 's/\\xC2\\xA0/ /g'\" - replaces non-breaking spaces (&nbsp) with regular spaces.\n", " # This can be a nightmare to debug, since nbsp's are identical to spaces in a text editor.\n", " # Note: this sed does NOT work on Mac, so make sure it's only run inside a container.\n", " cgi-fcgi -bind -connect 127.0.0.1:9000 | html2text | sed 's/\\xC2\\xA0/ /g'\n", "+cgi-fcgi -bind -connect 127.0.0.1:9000 | html2text | sed 's/\\xC2\\xA0/ /g'\n", "\n", "\n", "========================================================================================================================\n", "Add comments for xdebug configuration\n", "\n", "--- 7.3/config/php/xdebug.ini\n", "+++ 7.3/config/php/xdebug.ini\n", "@@ -1,6 +1,9 @@\n", " [xdebug]\n", " zend_extension=xdebug.so\n", " xdebug.remote_enable=1\n", " xdebug.remote_autostart=1\n", "+; xdebug.xdebug.remote_host defaults to \"localhost\", which works with VS Code Server web IDE\n", "+; For debugging from the host machine, xdebug.xdebug.remote_host is set to ${DOCKSAL_HOST_IP} at runtime\n", "+; xdebug.xdebug.remote_port is set at runtime: 9000 for host debugging and 9001 for VS Code Server web IDE\n", " xdebug.idekey=xdebug_session\n", " xdebug.max_nesting_level=256\n", "\n", "\n", "========================================================================================================================\n", "Configure xdebug remote settings\n", "\n", "--- 7.3/config/php/xdebug.ini\n", "+++ 7.3/config/php/xdebug.ini\n", "@@ -1,6 +1,9 @@\n", " [xdebug]\n", " zend_extension=xdebug.so\n", " xdebug.remote_enable=1\n", "-xdebug.remote_connect_back=1\n", "-xdebug.remote_port=9000\n", "+xdebug.remote_autostart=1\n", "+; xdebug.xdebug.remote_host defaults to \"localhost\", which works with VS Code Server web IDE\n", "+; For debugging from the host machine, xdebug.xdebug.remote_host is set to ${DOCKSAL_HOST_IP} at runtime\n", "+; xdebug.xdebug.remote_port is set at runtime: 9000 for host debugging and 9001 for VS Code Server web IDE\n", "+xdebug.idekey=xdebug_session\n", " xdebug.max_nesting_level=256\n", "\n", "\n", "========================================================================================================================\n", "Fix directory name\n", "\n", "--- .travis.yml\n", "+++ .travis.yml\n", "@@ -42,6 +42,6 @@\n", "@after_success:\n", "   - docker image ls\n", "   - ${TRAVIS_BUILD_DIR}/scripts/docker-push.sh\n", " \n", " after_failure:\n", "   - cd ${TRAVIS_BUILD_DIR}/${VERSION} && make logs\n", "-  - cd ${TRAVIS_BUILD_DIR}/codeserver && make logs\n", "+  - cd ${TRAVIS_BUILD_DIR}/code-server && make logs\n", "\n", "\n", "========================================================================================================================\n", "Enable remote autostart and set IDE key\n", "\n", "--- 7.1/config/php/xdebug.ini\n", "+++ 7.1/config/php/xdebug.ini\n", "@@ -1,5 +1,9 @@\n", " [xdebug]\n", " zend_extension=xdebug.so\n", " xdebug.remote_enable=1\n", "-xdebug.remote_port=1\n", "+xdebug.remote_autostart=1\n", "+; xdebug.xdebug.remote_host defaults to \"localhost\", which works with VS Code Server web IDE\n", "+; For debugging from the host machine, xdebug.xdebug.remote_host is set to ${DOCKSAL_HOST_IP} at runtime\n", "+; xdebug.xdebug.remote_port is set at runtime: 9000 for host debugging and 9001 for VS Code Server web IDE\n", "+xdebug.idekey=xdebug_session\n", " xdebug.max_nesting_level=256\n", "\n", "\n", "========================================================================================================================\n", "Enable remote autostart\n", "\n", "--- 7.1/config/php/xdebug.ini\n", "+++ 7.1/config/php/xdebug.ini\n", "@@ -1,10 +1,9 @@\n", " [xdebug]\n", " zend_extension=xdebug.so\n", " xdebug.remote_enable=1\n", "-xdebug.remote_connect_back=1\n", "-xdebug.remote_port=1\n", "+xdebug.remote_autostart=1\n", " ; xdebug.xdebug.remote_host defaults to \"localhost\", which works with VS Code Server web IDE\n", " ; For debugging from the host machine, xdebug.xdebug.remote_host is set to ${DOCKSAL_HOST_IP} at runtime\n", " ; xdebug.xdebug.remote_port is set at runtime: 9000 for host debugging and 9001 for VS Code Server web IDE\n", " xdebug.idekey=xdebug_session\n", " xdebug.max_nesting_level=256\n", "\n", "\n", "========================================================================================================================\n", "Enable PHP preload\n", "\n", "--- 7.4/config/php/zz-php.ini\n", "+++ 7.4/config/php/zz-php.ini\n", "@@ -9,10 +9,11 @@\n", " \n", " [mail]\n", " ; Enable Mailhog integration by default\n", " sendmail_path = '/usr/bin/msmtp -t --host=mail --port=1025'\n", " \n", " ; Extention settings\n", " [opcache]\n", " opcache.memory_consumption = 128\n", "+opcache.preload=/var/www/.docksal/etc/php/preload.php\n", " [blackfire]\n", " blackfire.agent_socket = 'tcp://blackfire:8707'\n", "\n", "\n", "========================================================================================================================\n", "Extract and refactor startup script\n", "\n", "--- 7.3/startup.sh\n", "+++ 7.3/startup.sh\n", "@@ -112,101 +112,176 @@\n", "@convert_secrets ()\n", " \t\teval \"export ${key}=${secret_value}\"\n", " \tdone\n", " }\n", " \n", " # Pantheon (terminus) login\n", " terminus_login ()\n", " {\n", " \techo-debug \"Authenticating with Pantheon...\"\n", " \t# This has to be done using the docker user via su to load the user environment\n", " \t# Note: Using 'su -l' to initiate a login session and have .profile sourced for the docker user\n", " \tlocal command=\"terminus auth:login --machine-token='${TERMINUS_TOKEN}'\"\n", " \tlocal output=$(su -l docker -c \"${command}\" 2>&1)\n", " \tif [[ $? != 0 ]]; then\n", " \t\techo-debug \"ERROR: Pantheon authentication failed.\"\n", " \t\techo\n", " \t\techo \"$output\"\n", " \t\techo\n", " \tfi\n", " }\n", " \n", " # Git settings\n", " git_settings ()\n", " {\n", " \t# These must be run as the docker user\n", " \techo-debug \"Configuring git...\"\n", " \t# Set default git settings if none have been passed\n", " \t# See https://github.com/docksal/service-cli/issues/124\n", " \tgosu docker git config --global user.email \"${GIT_USER_EMAIL:-<EMAIL>}\"\n", " \tgosu docker git config --global user.name \"${GIT_USER_NAME:-Docksal CLI}\"\n", " }\n", "+#!/bin/bash\n", "+\n", "+# This script is running as root by default.\n", "+# Switching to the docker user can be done via \"gosu docker <command>\".\n", "+\n", "+HOME_DIR='/home/<USER>'\n", "+\n", "+DEBUG=${DEBUG:-0}\n", "+# Turn debugging ON when cli is started in the service mode\n", "+[[ \"$1\" == \"supervisord\" ]] && DEBUG=1\n", "+echo-debug ()\n", "+{\n", "+\t[[ \"$DEBUG\" != 0 ]] && echo \"$(date +\"%F %H:%M:%S\") | $@\"\n", "+}\n", "+\n", "+uid_gid_reset ()\n", "+{\n", "+\tif [[ \"$HOST_UID\" != \"$(id -u docker)\" ]] || [[ \"$HOST_GID\" != \"$(id -g docker)\" ]]; then\n", "+\t\techo-debug \"Updating docker user uid/gid to $HOST_UID/$HOST_GID to match the host user uid/gid...\"\n", "+\t\tusermod -u \"$HOST_UID\" -o docker\n", "+\t\tgroupmod -g \"$HOST_GID\" -o \"$(id -gn docker)\"\n", "+\tfi\n", "+}\n", "+\n", "+xdebug_enable ()\n", "+{\n", "+\techo-debug \"Enabling xdebug...\"\n", "+\tln -s /opt/docker-php-ext-xdebug.ini /usr/local/etc/php/conf.d/\n", "+}\n", "+\n", "+xhprof_enable ()\n", "+{\n", "+\techo-debug \"Enabling xhprof...\"\n", "+\tcp /opt/docker-php-ext-xhprof.ini /usr/local/etc/php/conf.d/\n", "+\t# Output directory to the ini file\n", "+\techo \"xhprof.output_dir = ${XHPROF_OUTPUT_DIR}\" >> /usr/local/etc/php/conf.d/docker-php-ext-xhprof.ini\n", "+\t# Try to create directory if it doesn't exist\n", "+\tmkdir ${XHPROF_OUTPUT_DIR} || true\n", "+\t# Change owner of directory\n", "+\tchown docker:docker ${XHPROF_OUTPUT_DIR}\n", "+}\n", "+\n", "+ide_mode_enable ()\n", "+{\n", "+\techo-debug \"Enabling web IDE...\"\n", "+\t# Enabled only code-server service (disabled all other services)\n", "+\t# TODO: [v3] split IDE/cli and php-fpm entirely\n", "+\trm -f /etc/supervisor/conf.d/supervisord-*.conf\n", "+\tif [[ \"$IDE_PASSWORD\" != \"\" ]]; then\n", "+\t\texport PASSWORD=\"${IDE_PASSWORD}\"\n", "+\tfi\n", "+\trender_tmpl \"/etc/supervisor/conf.d/supervisord-code-server.conf\"\n", "+\tmkdir -p ${VSCODE_HOME}/User\n", "+\tln -s /opt/code-server/settings.json ${VSCODE_HOME}/User/\n", "+}\n", "+\n", "+# Creates symlinks to project level overrides if they exist\n", "+php_settings ()\n", "+{\n", "+\tphp_ini=/var/www/.docksal/etc/php/php.ini\n", "+\tif [[ -f ${php_ini} ]]; then\n", "+\t\techo-debug \"Found project level overrides for PHP. Including:\"\n", "+\t\techo-debug \"${php_ini}\"\n", "+\t\tln -s /var/www/.docksal/etc/php/php.ini /usr/local/etc/php/conf.d/zzz-php.ini\n", "+\tfi\n", "+\n", "+\tphp_fpm_conf=/var/www/.docksal/etc/php/php-fpm.conf\n", "+\tif [[ -f ${php_fpm_conf} ]]; then\n", "+\t\techo-debug \"Found project level overrides for PHP-FPM. Including:\"\n", "+\t\techo-debug \"${php_fpm_conf}\"\n", "+\t\tln -s ${php_fpm_conf} /usr/local/etc/php-fpm.d/zzz-php-fpm.conf\n", "+\tfi\n", "+}\n", "+\n", "+add_ssh_key ()\n", "+{\n", "+\techo-debug \"Adding a private SSH key from SECRET_SSH_PRIVATE_KEY...\"\n", "+\trender_tmpl \"$HOME_DIR/.ssh/id_rsa\"\n", "+\tchmod 0600 \"$HOME_DIR/.ssh/id_rsa\"\n", "+}\n", "+\n", "+# Helper function to render configs from go templates using gomplate\n", "+render_tmpl ()\n", "+{\n", "+\tlocal file=\"${1}\"\n", "+\tlocal tmpl=\"${1}.tmpl\"\n", "+\n", "+\tif [[ -f \"${tmpl}\" ]]; then\n", "+\t\techo-debug \"Rendering template: ${tmpl}...\"\n", "+\t\tgomplate --file \"${tmpl}\" --out \"${file}\"\n", "+\telse\n", "+\t\techo-debug \"Error: Template file not found: ${tmpl}\"\n", "+\t\treturn 1\n", "+\tfi\n", "+}\n", "+\n", "+# Helper function to loop through all environment variables prefixed with SECRET_ and\n", "+# convert to the equivalent variable without SECRET.\n", "+# Example: SECRET_TERMINUS_TOKEN => TERMINUS_TOKEN.\n", "+convert_secrets ()\n", "+{\n", "+\teval 'secrets=(${!SECRET_@})'\n", "+\tfor secret_key in \"${secrets[@]}\"; do\n", "+\t\tkey=${secret_key#SECRET_}\n", "+\t\tsecret_value=${!secret_key}\n", "+\n", "+\t\t# Write new variables to /etc/profile.d/secrets.sh to make them available for all users/sessions\n", "+\t\techo \"export ${key}=\\\"${secret_value}\\\"\" | tee -a \"/etc/profile.d/secrets.sh\" >/dev/null\n", "+\n", "+\t\t# Also export new variables here\n", "+\t\t# This makes them available in the server/php-fpm environment\n", "+\t\teval \"export ${key}=${secret_value}\"\n", "+\tdone\n", "+}\n", "+\n", "+# Pantheon (terminus) login\n", "+terminus_login ()\n", "+{\n", "+\techo-debug \"Authenticating with Pantheon...\"\n", "+\t# This has to be done using the docker user via su to load the user environment\n", "+\t# Note: Using 'su -l' to initiate a login session and have .profile sourced for the docker user\n", "+\tlocal command=\"terminus auth:login --machine-token='${TERMINUS_TOKEN}'\"\n", "+\tlocal output=$(su -l docker -c \"${command}\" 2>&1)\n", "+\tif [[ $? != 0 ]]; then\n", "+\t\techo-debug \"ERROR: Pantheon authentication failed.\"\n", "+\t\techo\n", "+\t\techo \"$output\"\n", "+\t\techo\n", "+\tfi\n", "+}\n", "+\n", "+# Git settings\n", "+git_settings ()\n", "+{\n", "+\t# These must be run as the docker user\n", "+\techo-debug \"Configuring git...\"\n", "+\t# Set default git settings if none have been passed\n", "+\t# See https://github.com/docksal/service-cli/issues/124\n", "+\tgosu docker git config --global user.email \"${GIT_USER_EMAIL:-<EMAIL>}\"\n", "+\tgosu docker git config --global user.name \"${GIT_USER_NAME:-Docksal CLI}\"\n", "+}\n", " \n", " # Inject a private SSH key if provided\n", " [[ \"$SECRET_SSH_PRIVATE_KEY\" != \"\" ]] && add_ssh_key\n", " \n", " # Set Composer Version\n", "-[[ \"${COMPOSER_VERSION}\" != \"\" ]] && [[ -f /usr/local/bin/composer${COMPOSER_VERSION} ]] &\n", "-# Convert all Environment Variables Prefixed with SECRET_\n", "-convert_secrets\n", "-\n", "-# Docker user uid/gid mapping to the host user uid/gid\n", "-[[ \"$HOST_UID\" != \"\" ]] && [[ \"$HOST_GID\" != \"\" ]] && uid_gid_reset\n", "-\n", "-# Enable xdebug\n", "-[[ \"$XDEBUG_ENABLED\" != \"\" ]] && [[ \"$XDEBUG_ENABLED\" != \"0\" ]] && xdebug_enable\n", "-\n", "-# Enable xdebug\n", "-[[ \"$XHPROF_ENABLED\" != \"\" ]] && [[ \"$XHPROF_ENABLED\" != \"0\" ]] && xhprof_enable\n", "-\n", "-# Enable web IDE\n", "-[[ \"$IDE_ENABLED\" != \"\" ]] && [[ \"$IDE_ENABLED\" != \"0\" ]] && ide_mode_enable\n", "-\n", "-# Include project level PHP settings if found\n", "-php_settings\n", "-\n", "-# Make sure permissions are correct (after uid/gid change and COPY operations in Dockerfile)\n", "-# To not bloat the image size, permissions on the home folder are reset at runtime.\n", "-echo-debug \"Resetting permissions on $HOME_DIR and /var/www...\"\n", "-chown \"${HOST_UID:-1000}:${HOST_GID:-1000}\" -R \"$HOME_DIR\"\n", "-# <PERSON><PERSON> resets the project root folder permissions to 0:0 when cli is recreated (e.g. an env variable updated).\n", "-# We apply a fix/workaround for this at startup (non-recursive).\n", "-chown \"${HOST_UID:-1000}:${HOST_GID:-1000}\" /var/www\n", "-\n", "-# These have to happen after the home directory permissions are reset,\n", "-# otherwise the docker user may not have write access to /home/<USER>\n", "-# Automatically authenticate with Pantheon if Terminus token is present\n", "-[[ \"$TERMINUS_TOKEN\" != \"\" ]] && terminus_login\n", "-\n", "-# If crontab file is found within project add contents to user crontab file.\n", "-if [[ -f ${PROJECT_ROOT}/.docksal/services/cli/crontab ]]; then\n", "-\techo-debug \"Loading crontab...\"\n", "-\tcat ${PROJECT_ROOT}/.docksal/services/cli/crontab | crontab -u docker -\n", "-fi\n", "-\n", "-# Apply git settings\n", "-[[ \"$GIT_USER_EMAIL\" != \"\" ]] && [[ \"$GIT_USER_NAME\" != \"\" ]] && git_settings\n", "-\n", "-# Initialization steps completed. Create a pid file to mark the container as healthy\n", "-echo-debug \"Preliminary initialization completed.\"\n", "-touch /var/run/cli\n", "-\n", "-# Execute a custom startup script if present\n", "-if [[ -x ${PROJECT_ROOT}/.docksal/services/cli/startup.sh ]]; then\n", "-\techo-debug \"Running custom startup script...\"\n", "-\t# TODO: should we source the script instead?\n", "-\tsu -l docker -c \"${PROJECT_ROOT}/.docksal/services/cli/startup.sh\"\n", "-\tif [[ $? == 0 ]]; then\n", "-\t\techo-debug \"Custom startup script executed successfully.\"\n", "-\telse\n", "-\t\techo-debug \"ERROR: Custom startup script execution failed.\"\n", "-\tfi\n", "-fi\n", "-\n", "-# Execute passed CMD arguments\n", "-echo-debug \"Passing execution to: $*\"\n", "-# Service mode (run as root)\n", "-if [[ \"$1\" == \"supervisord\" ]]; then\n", "-\texec gosu root supervisord -c /etc/supervisor/supervisord.conf\n", "-# Command mode (run as docker user)\n", "-else\n", "-\texec gosu docker \"$@\"\n", "-fi\n", "\n", "\n", "========================================================================================================================\n", "Update composer symlink\n", "\n", "--- 7.3/startup.sh\n", "+++ 7.3/startup.sh\n", "@@ -183,30 +183,32 @@\n", " fi\n", " \n", " # Apply git settings\n", " [[ \"$GIT_USER_EMAIL\" != \"\" ]] && [[ \"$GIT_USER_NAME\" != \"\" ]] && git_settings\n", " \n", " # Initialization steps completed. Create a pid file to mark the container as healthy\n", " echo-debug \"Preliminary initialization completed.\"\n", " touch /var/run/cli\n", " \n", " # Execute a custom startup script if present\n", " if [[ -x ${PROJECT_ROOT}/.docksal/services/cli/startup.sh ]]; then\n", " \techo-debug \"Running custom startup script...\"\n", " \t# TODO: should we source the script instead?\n", " \tsu -l docker -c \"${PROJECT_ROOT}/.docksal/services/cli/startup.sh\"\n", " \tif [[ $? == 0 ]]; then\n", " \t\techo-debug \"Custom startup script executed successfully.\"\n", " \telse\n", " \t\techo-debug \"ERROR: Custom startup script execution failed.\"\n", " \tfi\n", " fi\n", " \n", " # Execute passed CMD arguments\n", " echo-debug \"Passing execution to: $*\"\n", " # Service mode (run as root)\n", " if [[ \"$1\" == \"supervisord\" ]]; then\n", " \texec gosu root supervisord -c /etc/supervisor/supervisord.conf\n", " # Command mode (run as docker user)\n", " else\n", " \texec gosu docker \"$@\"\n", " fi\n", "+[[ \"${COMPOSER_VERSION}\" != \"\" ]] && [[ -f /usr/local/bin/composer${COMPOSER_VERSION} ]] && rm -f /usr/local/bin/composer && ln -s /usr/local/bin/composer${COMPOSER_VERSION} /usr/local/bin/composer\n", "+\n", "\n", "\n", "========================================================================================================================\n", "Add initialization steps\n", "\n", "--- 7.3/startup.sh\n", "+++ 7.3/startup.sh\n", "@@ -183,30 +183,95 @@\n", " fi\n", " \n", " # Apply git settings\n", " [[ \"$GIT_USER_EMAIL\" != \"\" ]] && [[ \"$GIT_USER_NAME\" != \"\" ]] && git_settings\n", " \n", " # Initialization steps completed. Create a pid file to mark the container as healthy\n", " echo-debug \"Preliminary initialization completed.\"\n", " touch /var/run/cli\n", " \n", " # Execute a custom startup script if present\n", " if [[ -x ${PROJECT_ROOT}/.docksal/services/cli/startup.sh ]]; then\n", " \techo-debug \"Running custom startup script...\"\n", " \t# TODO: should we source the script instead?\n", " \tsu -l docker -c \"${PROJECT_ROOT}/.docksal/services/cli/startup.sh\"\n", " \tif [[ $? == 0 ]]; then\n", " \t\techo-debug \"Custom startup script executed successfully.\"\n", " \telse\n", " \t\techo-debug \"ERROR: Custom startup script execution failed.\"\n", " \tfi\n", " fi\n", " \n", " # Execute passed CMD arguments\n", " echo-debug \"Passing execution to: $*\"\n", " # Service mode (run as root)\n", " if [[ \"$1\" == \"supervisord\" ]]; then\n", " \texec gosu root supervisord -c /etc/supervisor/supervisord.conf\n", " # Command mode (run as docker user)\n", " else\n", " \texec gosu docker \"$@\"\n", " fi\n", "+# Convert all Environment Variables Prefixed with SECRET_\n", "+convert_secrets\n", "+\n", "+# Docker user uid/gid mapping to the host user uid/gid\n", "+[[ \"$HOST_UID\" != \"\" ]] && [[ \"$HOST_GID\" != \"\" ]] && uid_gid_reset\n", "+\n", "+# Enable xdebug\n", "+[[ \"$XDEBUG_ENABLED\" != \"\" ]] && [[ \"$XDEBUG_ENABLED\" != \"0\" ]] && xdebug_enable\n", "+\n", "+# Enable xdebug\n", "+[[ \"$XHPROF_ENABLED\" != \"\" ]] && [[ \"$XHPROF_ENABLED\" != \"0\" ]] && xhprof_enable\n", "+\n", "+# Enable web IDE\n", "+[[ \"$IDE_ENABLED\" != \"\" ]] && [[ \"$IDE_ENABLED\" != \"0\" ]] && ide_mode_enable\n", "+\n", "+# Include project level PHP settings if found\n", "+php_settings\n", "+\n", "+# Make sure permissions are correct (after uid/gid change and COPY operations in Dockerfile)\n", "+# To not bloat the image size, permissions on the home folder are reset at runtime.\n", "+echo-debug \"Resetting permissions on $HOME_DIR and /var/www...\"\n", "+chown \"${HOST_UID:-1000}:${HOST_GID:-1000}\" -R \"$HOME_DIR\"\n", "+# <PERSON><PERSON> resets the project root folder permissions to 0:0 when cli is recreated (e.g. an env variable updated).\n", "+# We apply a fix/workaround for this at startup (non-recursive).\n", "+chown \"${HOST_UID:-1000}:${HOST_GID:-1000}\" /var/www\n", "+\n", "+# These have to happen after the home directory permissions are reset,\n", "+# otherwise the docker user may not have write access to /home/<USER>\n", "+# Automatically authenticate with Pantheon if Terminus token is present\n", "+[[ \"$TERMINUS_TOKEN\" != \"\" ]] && terminus_login\n", "+\n", "+# If crontab file is found within project add contents to user crontab file.\n", "+if [[ -f ${PROJECT_ROOT}/.docksal/services/cli/crontab ]]; then\n", "+\techo-debug \"Loading crontab...\"\n", "+\tcat ${PROJECT_ROOT}/.docksal/services/cli/crontab | crontab -u docker -\n", "+fi\n", "+\n", "+# Apply git settings\n", "+[[ \"$GIT_USER_EMAIL\" != \"\" ]] && [[ \"$GIT_USER_NAME\" != \"\" ]] && git_settings\n", "+\n", "+# Initialization steps completed. Create a pid file to mark the container as healthy\n", "+echo-debug \"Preliminary initialization completed.\"\n", "+touch /var/run/cli\n", "+\n", "+# Execute a custom startup script if present\n", "+if [[ -x ${PROJECT_ROOT}/.docksal/services/cli/startup.sh ]]; then\n", "+\techo-debug \"Running custom startup script...\"\n", "+\t# TODO: should we source the script instead?\n", "+\tsu -l docker -c \"${PROJECT_ROOT}/.docksal/services/cli/startup.sh\"\n", "+\tif [[ $? == 0 ]]; then\n", "+\t\techo-debug \"Custom startup script executed successfully.\"\n", "+\telse\n", "+\t\techo-debug \"ERROR: Custom startup script execution failed.\"\n", "+\tfi\n", "+fi\n", "+\n", "+# Execute passed CMD arguments\n", "+echo-debug \"Passing execution to: $*\"\n", "+# Service mode (run as root)\n", "+if [[ \"$1\" == \"supervisord\" ]]; then\n", "+\texec gosu root supervisord -c /etc/supervisor/supervisord.conf\n", "+# Command mode (run as docker user)\n", "+else\n", "+\texec gosu docker \"$@\"\n", "+fi\n", "\n", "\n", "========================================================================================================================\n", "Extract and refactor startup script\n", "\n", "--- 7.3/startup.sh\n", "+++ 7.3/startup.sh\n", "@@ -139,75 +139,149 @@\n", "@git_settings ()\n", " \tgosu docker git config --global user.email \"${GIT_USER_EMAIL:-<EMAIL>}\"\n", " \tgosu docker git config --global user.name \"${GIT_USER_NAME:-Docksal CLI}\"\n", " }\n", "+#!/bin/bash\n", "+\n", "+# This script is running as root by default.\n", "+# Switching to the docker user can be done via \"gosu docker <command>\".\n", "+\n", "+HOME_DIR='/home/<USER>'\n", "+\n", "+DEBUG=${DEBUG:-0}\n", "+# Turn debugging ON when cli is started in the service mode\n", "+[[ \"$1\" == \"supervisord\" ]] && DEBUG=1\n", "+echo-debug ()\n", "+{\n", "+\t[[ \"$DEBUG\" != 0 ]] && echo \"$(date +\"%F %H:%M:%S\") | $@\"\n", "+}\n", "+\n", "+uid_gid_reset ()\n", "+{\n", "+\tif [[ \"$HOST_UID\" != \"$(id -u docker)\" ]] || [[ \"$HOST_GID\" != \"$(id -g docker)\" ]]; then\n", "+\t\techo-debug \"Updating docker user uid/gid to $HOST_UID/$HOST_GID to match the host user uid/gid...\"\n", "+\t\tusermod -u \"$HOST_UID\" -o docker\n", "+\t\tgroupmod -g \"$HOST_GID\" -o \"$(id -gn docker)\"\n", "+\tfi\n", "+}\n", "+\n", "+xdebug_enable ()\n", "+{\n", "+\techo-debug \"Enabling xdebug...\"\n", "+\tln -s /opt/docker-php-ext-xdebug.ini /usr/local/etc/php/conf.d/\n", "+}\n", "+\n", "+xhprof_enable ()\n", "+{\n", "+\techo-debug \"Enabling xhprof...\"\n", "+\tcp /opt/docker-php-ext-xhprof.ini /usr/local/etc/php/conf.d/\n", "+\t# Output directory to the ini file\n", "+\techo \"xhprof.output_dir = ${XHPROF_OUTPUT_DIR}\" >> /usr/local/etc/php/conf.d/docker-php-ext-xhprof.ini\n", "+\t# Try to create directory if it doesn't exist\n", "+\tmkdir ${XHPROF_OUTPUT_DIR} || true\n", "+\t# Change owner of directory\n", "+\tchown docker:docker ${XHPROF_OUTPUT_DIR}\n", "+}\n", "+\n", "+ide_mode_enable ()\n", "+{\n", "+\techo-debug \"Enabling web IDE...\"\n", "+\t# Enabled only code-server service (disabled all other services)\n", "+\t# TODO: [v3] split IDE/cli and php-fpm entirely\n", "+\trm -f /etc/supervisor/conf.d/supervisord-*.conf\n", "+\tif [[ \"$IDE_PASSWORD\" != \"\" ]]; then\n", "+\t\texport PASSWORD=\"${IDE_PASSWORD}\"\n", "+\tfi\n", "+\trender_tmpl \"/etc/supervisor/conf.d/supervisord-code-server.conf\"\n", "+\tmkdir -p ${VSCODE_HOME}/User\n", "+\tln -s /opt/code-server/settings.json ${VSCODE_HOME}/User/\n", "+}\n", "+\n", "+# Creates symlinks to project level overrides if they exist\n", "+php_settings ()\n", "+{\n", "+\tphp_ini=/var/www/.docksal/etc/php/php.ini\n", "+\tif [[ -f ${php_ini} ]]; then\n", "+\t\techo-debug \"Found project level overrides for PHP. Including:\"\n", "+\t\techo-debug \"${php_ini}\"\n", "+\t\tln -s /var/www/.docksal/etc/php/php.ini /usr/local/etc/php/conf.d/zzz-php.ini\n", "+\tfi\n", "+\n", "+\tphp_fpm_conf=/var/www/.docksal/etc/php/php-fpm.conf\n", "+\tif [[ -f ${php_fpm_conf} ]]; then\n", "+\t\techo-debug \"Found project level overrides for PHP-FPM. Including:\"\n", "+\t\techo-debug \"${php_fpm_conf}\"\n", "+\t\tln -s ${php_fpm_conf} /usr/local/etc/php-fpm.d/zzz-php-fpm.conf\n", "+\tfi\n", "+}\n", "+\n", "+add_ssh_key ()\n", "+{\n", "+\techo-debug \"Adding a private SSH key from SECRET_SSH_PRIVATE_KEY...\"\n", "+\trender_tmpl \"$HOME_DIR/.ssh/id_rsa\"\n", "+\tchmod 0600 \"$HOME_DIR/.ssh/id_rsa\"\n", "+}\n", "+\n", "+# Helper function to render configs from go templates using gomplate\n", "+render_tmpl ()\n", "+{\n", "+\tlocal file=\"${1}\"\n", "+\tlocal tmpl=\"${1}.tmpl\"\n", "+\n", "+\tif [[ -f \"${tmpl}\" ]]; then\n", "+\t\techo-debug \"Rendering template: ${tmpl}...\"\n", "+\t\tgomplate --file \"${tmpl}\" --out \"${file}\"\n", "+\telse\n", "+\t\techo-debug \"Error: Template file not found: ${tmpl}\"\n", "+\t\treturn 1\n", "+\tfi\n", "+}\n", "+\n", "+# Helper function to loop through all environment variables prefixed with SECRET_ and\n", "+# convert to the equivalent variable without SECRET.\n", "+# Example: SECRET_TERMINUS_TOKEN => TERMINUS_TOKEN.\n", "+convert_secrets ()\n", "+{\n", "+\teval 'secrets=(${!SECRET_@})'\n", "+\tfor secret_key in \"${secrets[@]}\"; do\n", "+\t\tkey=${secret_key#SECRET_}\n", "+\t\tsecret_value=${!secret_key}\n", "+\n", "+\t\t# Write new variables to /etc/profile.d/secrets.sh to make them available for all users/sessions\n", "+\t\techo \"export ${key}=\\\"${secret_value}\\\"\" | tee -a \"/etc/profile.d/secrets.sh\" >/dev/null\n", "+\n", "+\t\t# Also export new variables here\n", "+\t\t# This makes them available in the server/php-fpm environment\n", "+\t\teval \"export ${key}=${secret_value}\"\n", "+\tdone\n", "+}\n", "+\n", "+# Pantheon (terminus) login\n", "+terminus_login ()\n", "+{\n", "+\techo-debug \"Authenticating with Pantheon...\"\n", "+\t# This has to be done using the docker user via su to load the user environment\n", "+\t# Note: Using 'su -l' to initiate a login session and have .profile sourced for the docker user\n", "+\tlocal command=\"terminus auth:login --machine-token='${TERMINUS_TOKEN}'\"\n", "+\tlocal output=$(su -l docker -c \"${command}\" 2>&1)\n", "+\tif [[ $? != 0 ]]; then\n", "+\t\techo-debug \"ERROR: Pantheon authentication failed.\"\n", "+\t\techo\n", "+\t\techo \"$output\"\n", "+\t\techo\n", "+\tfi\n", "+}\n", "+\n", "+# Git settings\n", "+git_settings ()\n", "+{\n", "+\t# These must be run as the docker user\n", "+\techo-debug \"Configuring git...\"\n", "+\t# Set default git settings if none have been passed\n", "+\t# See https://github.com/docksal/service-cli/issues/124\n", "+\tgosu docker git config --global user.email \"${GIT_USER_EMAIL:-<EMAIL>}\"\n", "+\tgosu docker git config --global user.name \"${GIT_USER_NAME:-Docksal CLI}\"\n", "+}\n", " \n", " # Inject a private SSH key if provided\n", " [[ \"$SECRET_SSH_PRIVATE_KEY\" != \"\" ]] && add_ssh_key\n", " \n", " # Set Composer Version\n", "-[[ \"${COMPOSER_DEFAULT_VERSION}\" != \"\" ]] && [[ -f /usr/local/bin/composer${COMPOSER_DEFAULT_VERSION} ]] && ln -s /usr/local/bin/composer${COMPOSER_VERSION} /usr/local/bin/composer\n", "-\n", "-# Convert all Environment Variables Prefixed with SECRET_\n", "-convert_secrets\n", "-\n", "-# Docker user uid/gid mapping to the host user uid/gid\n", "-[[ \"$HOST_UID\" != \"\" ]] && [[ \"$HOST_GID\" != \"\" ]] && uid_gid_reset\n", "-\n", "-# Enable xdebug\n", "-[[ \"$XDEBUG_ENABLED\" != \"\" ]] && [[ \"$XDEBUG_ENABLED\" != \"0\" ]] && xdebug_enable\n", "-\n", "-# Enable xdebug\n", "-[[ \"$XHPROF_ENABLED\" != \"\" ]] && [[ \"$XHPROF_ENABLED\" != \"0\" ]] && xhprof_enable\n", "-\n", "-# Enable web IDE\n", "-[[ \"$IDE_ENABLED\" != \"\" ]] && [[ \"$IDE_ENABLED\" != \"0\" ]] && ide_mode_enable\n", "-\n", "-# Include project level PHP settings if found\n", "-php_settings\n", "-\n", "-# Make sure permissions are correct (after uid/gid change and COPY operations in Dockerfile)\n", "-# To not bloat the image size, permissions on the home folder are reset at runtime.\n", "-echo-debug \"Resetting permissions on $HOME_DIR and /var/www...\"\n", "-chown \"${HOST_UID:-1000}:${HOST_GID:-1000}\" -R \"$HOME_DIR\"\n", "-# <PERSON><PERSON> resets the project root folder permissions to 0:0 when cli is recreated (e.g. an env variable updated).\n", "-# We apply a fix/workaround for this at startup (non-recursive).\n", "-chown \"${HOST_UID:-1000}:${HOST_GID:-1000}\" /var/www\n", "-\n", "-# These have to happen after the home directory permissions are reset,\n", "-# otherwise the docker user may not have write access to /home/<USER>\n", "-# Automatically authenticate with Pantheon if Terminus token is present\n", "-[[ \"$TERMINUS_TOKEN\" != \"\" ]] && terminus_login\n", "-\n", "-# If crontab file is found within project add contents to user crontab file.\n", "-if [[ -f ${PROJECT_ROOT}/.docksal/services/cli/crontab ]]; then\n", "-\techo-debug \"Loading crontab...\"\n", "-\tcat ${PROJECT_ROOT}/.docksal/services/cli/crontab | crontab -u docker -\n", "-fi\n", "-\n", "-# Apply git settings\n", "-[[ \"$GIT_USER_EMAIL\" != \"\" ]] && [[ \"$GIT_USER_NAME\" != \"\" ]] && git_settings\n", "-\n", "-# Initialization steps completed. Create a pid file to mark the container as healthy\n", "-echo-debug \"Preliminary initialization completed.\"\n", "-touch /var/run/cli\n", "-\n", "-# Execute a custom startup script if present\n", "-if [[ -x ${PROJECT_ROOT}/.docksal/services/cli/startup.sh ]]; then\n", "-\techo-debug \"Running custom startup script...\"\n", "-\t# TODO: should we source the script instead?\n", "-\tsu -l docker -c \"${PROJECT_ROOT}/.docksal/services/cli/startup.sh\"\n", "-\tif [[ $? == 0 ]]; then\n", "-\t\techo-debug \"Custom startup script executed successfully.\"\n", "-\telse\n", "-\t\techo-debug \"ERROR: Custom startup script execution failed.\"\n", "-\tfi\n", "-fi\n", "-\n", "-# Execute passed CMD arguments\n", "-echo-debug \"Passing execution to: $*\"\n", "-# Service mode (run as root)\n", "-if [[ \"$1\" == \"supervisord\" ]]; then\n", "-\texec gosu root supervisord -c /etc/supervisor/supervisord.conf\n", "-# Command mode (run as docker user)\n", "-else\n", "-\texec gosu docker \"$@\"\n", "-fi\n", "\n", "\n", "========================================================================================================================\n", "Add composer version symlink\n", "\n", "--- 7.3/startup.sh\n", "+++ 7.3/startup.sh\n", "@@ -211,3 +211,4 @@\n", " else\n", " \texec gosu docker \"$@\"\n", " fi\n", "+[[ \"${COMPOSER_DEFAULT_VERSION}\" != \"\" ]] && [[ -f /usr/local/bin/composer${COMPOSER_DEFAULT_VERSION} ]] && ln -sf /usr/local/bin/composer${COMPOSER_DEFAULT_VERSION} /usr/local/bin/composer\n", "\n", "\n", "========================================================================================================================\n", "Add startup script functionality\n", "\n", "--- 7.3/startup.sh\n", "+++ 7.3/startup.sh\n", "@@ -211,3 +211,69 @@\n", " else\n", " \texec gosu docker \"$@\"\n", " fi\n", "+\n", "+# Convert all Environment Variables Prefixed with SECRET_\n", "+convert_secrets\n", "+\n", "+# Docker user uid/gid mapping to the host user uid/gid\n", "+[[ \"$HOST_UID\" != \"\" ]] && [[ \"$HOST_GID\" != \"\" ]] && uid_gid_reset\n", "+\n", "+# Enable xdebug\n", "+[[ \"$XDEBUG_ENABLED\" != \"\" ]] && [[ \"$XDEBUG_ENABLED\" != \"0\" ]] && xdebug_enable\n", "+\n", "+# Enable xdebug\n", "+[[ \"$XHPROF_ENABLED\" != \"\" ]] && [[ \"$XHPROF_ENABLED\" != \"0\" ]] && xhprof_enable\n", "+\n", "+# Enable web IDE\n", "+[[ \"$IDE_ENABLED\" != \"\" ]] && [[ \"$IDE_ENABLED\" != \"0\" ]] && ide_mode_enable\n", "+\n", "+# Include project level PHP settings if found\n", "+php_settings\n", "+\n", "+# Make sure permissions are correct (after uid/gid change and COPY operations in Dockerfile)\n", "+# To not bloat the image size, permissions on the home folder are reset at runtime.\n", "+echo-debug \"Resetting permissions on $HOME_DIR and /var/www...\"\n", "+chown \"${HOST_UID:-1000}:${HOST_GID:-1000}\" -R \"$HOME_DIR\"\n", "+# <PERSON><PERSON> resets the project root folder permissions to 0:0 when cli is recreated (e.g. an env variable updated).\n", "+# We apply a fix/workaround for this at startup (non-recursive).\n", "+chown \"${HOST_UID:-1000}:${HOST_GID:-1000}\" /var/www\n", "+\n", "+# These have to happen after the home directory permissions are reset,\n", "+# otherwise the docker user may not have write access to /home/<USER>\n", "+# Automatically authenticate with Pantheon if Terminus token is present\n", "+[[ \"$TERMINUS_TOKEN\" != \"\" ]] && terminus_login\n", "+\n", "+# If crontab file is found within project add contents to user crontab file.\n", "+if [[ -f ${PROJECT_ROOT}/.docksal/services/cli/crontab ]]; then\n", "+\techo-debug \"Loading crontab...\"\n", "+\tcat ${PROJECT_ROOT}/.docksal/services/cli/crontab | crontab -u docker -\n", "+fi\n", "+\n", "+# Apply git settings\n", "+[[ \"$GIT_USER_EMAIL\" != \"\" ]] && [[ \"$GIT_USER_NAME\" != \"\" ]] && git_settings\n", "+\n", "+# Initialization steps completed. Create a pid file to mark the container as healthy\n", "+echo-debug \"Preliminary initialization completed.\"\n", "+touch /var/run/cli\n", "+\n", "+# Execute a custom startup script if present\n", "+if [[ -x ${PROJECT_ROOT}/.docksal/services/cli/startup.sh ]]; then\n", "+\techo-debug \"Running custom startup script...\"\n", "+\t# TODO: should we source the script instead?\n", "+\tsu -l docker -c \"${PROJECT_ROOT}/.docksal/services/cli/startup.sh\"\n", "+\tif [[ $? == 0 ]]; then\n", "+\t\techo-debug \"Custom startup script executed successfully.\"\n", "+\telse\n", "+\t\techo-debug \"ERROR: Custom startup script execution failed.\"\n", "+\tfi\n", "+fi\n", "+\n", "+# Execute passed CMD arguments\n", "+echo-debug \"Passing execution to: $*\"\n", "+# Service mode (run as root)\n", "+if [[ \"$1\" == \"supervisord\" ]]; then\n", "+\texec gosu root supervisord -c /etc/supervisor/supervisord.conf\n", "+# Command mode (run as docker user)\n", "+else\n", "+\texec gosu docker \"$@\"\n", "+fi\n", "\n", "\n", "========================================================================================================================\n", "Refactor startup script\n", "\n", "--- 7.4/startup.sh\n", "+++ 7.4/startup.sh\n", "@@ -1,8 +1,141 @@\n", "+#!/bin/bash\n", "+\n", "+# This script is running as root by default.\n", "+# Switching to the docker user can be done via \"gosu docker <command>\".\n", "+\n", "+HOME_DIR='/home/<USER>'\n", "+\n", "+DEBUG=${DEBUG:-0}\n", "+# Turn debugging ON when cli is started in the service mode\n", "+[[ \"$1\" == \"supervisord\" ]] && DEBUG=1\n", "+echo-debug ()\n", "+{\n", "+\t[[ \"$DEBUG\" != 0 ]] && echo \"$(date +\"%F %H:%M:%S\") | $@\"\n", "+}\n", "+\n", "+uid_gid_reset ()\n", "+{\n", "+\tif [[ \"$HOST_UID\" != \"$(id -u docker)\" ]] || [[ \"$HOST_GID\" != \"$(id -g docker)\" ]]; then\n", "+\t\techo-debug \"Updating docker user uid/gid to $HOST_UID/$HOST_GID to match the host user uid/gid...\"\n", "+\t\tusermod -u \"$HOST_UID\" -o docker\n", "+\t\tgroupmod -g \"$HOST_GID\" -o \"$(id -gn docker)\"\n", "+\tfi\n", "+}\n", "+\n", "+xdebug_enable ()\n", "+{\n", "+\techo-debug \"Enabling xdebug...\"\n", "+\tln -s /opt/docker-php-ext-xdebug.ini /usr/local/etc/php/conf.d/\n", "+}\n", "+\n", "+xhprof_enable ()\n", "+{\n", "+\techo-debug \"Enabling xhprof...\"\n", "+\tcp /opt/docker-php-ext-xhprof.ini /usr/local/etc/php/conf.d/\n", "+\t# Output directory to the ini file\n", "+\techo \"xhprof.output_dir = ${XHPROF_OUTPUT_DIR}\" >> /usr/local/etc/php/conf.d/docker-php-ext-xhprof.ini\n", "+\t# Try to create directory if it doesn't exist\n", "+\tmkdir ${XHPROF_OUTPUT_DIR} || true\n", "+\t# Change owner of directory\n", "+\tchown docker:docker ${XHPROF_OUTPUT_DIR}\n", "+}\n", "+\n", "+opcache_preload_enable()\n", "+{\n", "+        echo-debug \"Enabling opcache preload...\"\n", "+        ln -s /opt/docker-php-ext-opcache.ini /usr/local/etc/php/conf.d/\n", "+}\n", "+\n", "+ide_mode_enable ()\n", "+{\n", "+\techo-debug \"Enabling web IDE...\"\n", "+\t# Enabled only code-server service (disabled all other services)\n", "+\t# TODO: [v3] split IDE/cli and php-fpm entirely\n", "+\trm -f /etc/supervisor/conf.d/supervisord-*.conf\n", "+\tif [[ \"$IDE_PASSWORD\" != \"\" ]]; then\n", "+\t\texport PASSWORD=\"${IDE_PASSWORD}\"\n", "+\tfi\n", "+\trender_tmpl \"/etc/supervisor/conf.d/supervisord-code-server.conf\"\n", "+\tmkdir -p ${VSCODE_HOME}/User\n", "+\tln -s /opt/code-server/settings.json ${VSCODE_HOME}/User/\n", "+}\n", "+\n", "+# Creates symlinks to project level overrides if they exist\n", "+php_settings ()\n", "+{\n", "+\tphp_ini=/var/www/.docksal/etc/php/php.ini\n", "+\tif [[ -f ${php_ini} ]]; then\n", "+\t\techo-debug \"Found project level overrides for PHP. Including:\"\n", "+\t\techo-debug \"${php_ini}\"\n", "+\t\tln -s /var/www/.docksal/etc/php/php.ini /usr/local/etc/php/conf.d/zzz-php.ini\n", "+\tfi\n", "+\n", "+\tphp_fpm_conf=/var/www/.docksal/etc/php/php-fpm.conf\n", "+\tif [[ -f ${php_fpm_conf} ]]; then\n", "+\t\techo-debug \"Found project level overrides for PHP-FPM. Including:\"\n", "+\t\techo-debug \"${php_fpm_conf}\"\n", "+\t\tln -s ${php_fpm_conf} /usr/local/etc/php-fpm.d/zzz-php-fpm.conf\n", "+\tfi\n", "+}\n", "+\n", "+add_ssh_key ()\n", "+{\n", "+\techo-debug \"Adding a private SSH key from SECRET_SSH_PRIVATE_KEY...\"\n", "+\trender_tmpl \"$HOME_DIR/.ssh/id_rsa\"\n", "+\tchmod 0600 \"$HOME_DIR/.ssh/id_rsa\"\n", "+}\n", "+\n", "+# Helper function to render configs from go templates using gomplate\n", "+render_tmpl ()\n", "+{\n", "+\tlocal file=\"${1}\"\n", "+\tlocal tmpl=\"${1}.tmpl\"\n", "+\n", "+\tif [[ -f \"${tmpl}\" ]]; then\n", "+\t\techo-debug \"Rendering template: ${tmpl}...\"\n", "+\t\tgomplate --file \"${tmpl}\" --out \"${file}\"\n", "+\telse\n", "+\t\techo-debug \"Error: Template file not found: ${tmpl}\"\n", "+\t\treturn 1\n", "+\tfi\n", "+}\n", "+\n", "+# Helper function to loop through all environment variables prefixed with SECRET_ and\n", "+# convert to the equivalent variable without SECRET.\n", "+# Example: SECRET_TERMINUS_TOKEN => TERMINUS_TOKEN.\n", "+convert_secrets ()\n", "+{\n", "+\teval 'secrets=(${!SECRET_@})'\n", "+\tfor secret_key in \"${secrets[@]}\"; do\n", "+\t\tkey=${secret_key#SECRET_}\n", "+\t\tsecret_value=${!secret_key}\n", "+\n", "+\t\t# Write new variables to /etc/profile.d/secrets.sh to make them available for all users/sessions\n", "+\t\techo \"export ${key}=\\\"${secret_value}\\\"\" | tee -a \"/etc/profile.d/secrets.sh\" >/dev/null\n", "+\n", "+\t\t# Also export new variables here\n", "+\t\t# This makes them available in the server/php-fpm environment\n", "+\t\teval \"export ${key}=${secret_value}\"\n", "+\tdone\n", "+}\n", "+\n", "+# Pantheon (terminus) login\n", "+terminus_login ()\n", "+{\n", "+\techo-debug \"Authenticating with Pantheon...\"\n", "+\t# This has to be done using the docker user via su to load the user environment\n", "+\t# Note: Using 'su -l' to initiate a login session and have .profile sourced for the docker user\n", "+\tlocal command=\"terminus auth:login --machine-token='${TERMINUS_TOKEN}'\"\n", "+\tlocal output=$(su -l docker -c \"${command}\" 2>&1)\n", "+\tif [[ $? != 0 ]]; then\n", "+\t\techo-debug \"ERROR: Pantheon authentication failed.\"\n", "+\t\techo\n", "+\t\techo \"$output\"\n", " #!/bin/bash\n", " \n", " # This script is running as root by default.\n", " # Switching to the docker user can be done via \"gosu docker <command>\".\n", " \n", " HOME_DIR='/home/<USER>'\n", " \n", " DEBUG=${DEBUG:-0}\n", "@@ -145,78 +278,8 @@\n", "@git_settings ()\n", " \tgosu docker git config --global user.email \"${GIT_USER_EMAIL:-<EMAIL>}\"\n", " \tgosu docker git config --global user.name \"${GIT_USER_NAME:-Docksal CLI}\"\n", " }\n", " \n", " # Inject a private SSH key if provided\n", " [[ \"$SECRET_SSH_PRIVATE_KEY\" != \"\" ]] && add_ssh_key\n", " \n", " # Set Composer Version\n", "-[[ \"${COMPOSER_DEFAULT_VERSION}\" != \"\" ]] && [[ -f /usr/local/bin/composer${COMPOSER_DEFAULT_VERSION} ]] && rm -f /usr/local/bin/composer && ln -sf /usr/local/bin/composer${COMPOSER_VERSION} /usr/local/bin/composer\n", "-\n", "-# Convert all Environment Variables Prefixed with SECRET_\n", "-convert_secrets\n", "-\n", "-# Docker user uid/gid mapping to the host user uid/gid\n", "-[[ \"$HOST_UID\" != \"\" ]] && [[ \"$HOST_GID\" != \"\" ]] && uid_gid_reset\n", "-\n", "-# Enable xdebug\n", "-[[ \"$XDEBUG_ENABLED\" != \"\" ]] && [[ \"$XDEBUG_ENABLED\" != \"0\" ]] && xdebug_enable\n", "-\n", "-# Enable xdebug\n", "-[[ \"$XHPROF_ENABLED\" != \"\" ]] && [[ \"$XHPROF_ENABLED\" != \"0\" ]] && xhprof_enable\n", "-\n", "-# Enable opcache preload\n", "-[[ -f \"/var/www/.docksal/etc/php/preload.php\" ]] && opcache_preload_enable\n", "-\n", "-# Enable web IDE\n", "-[[ \"$IDE_ENABLED\" != \"\" ]] && [[ \"$IDE_ENABLED\" != \"0\" ]] && ide_mode_enable\n", "-\n", "-# Include project level PHP settings if found\n", "-php_settings\n", "-\n", "-# Make sure permissions are correct (after uid/gid change and COPY operations in Dockerfile)\n", "-# To not bloat the image size, permissions on the home folder are reset at runtime.\n", "-echo-debug \"Resetting permissions on $HOME_DIR and /var/www...\"\n", "-chown \"${HOST_UID:-1000}:${HOST_GID:-1000}\" -R \"$HOME_DIR\"\n", "-# <PERSON><PERSON> resets the project root folder permissions to 0:0 when cli is recreated (e.g. an env variable updated).\n", "-# We apply a fix/workaround for this at startup (non-recursive).\n", "-chown \"${HOST_UID:-1000}:${HOST_GID:-1000}\" /var/www\n", "-\n", "-# These have to happen after the home directory permissions are reset,\n", "-# otherwise the docker user may not have write access to /home/<USER>\n", "-# Automatically authenticate with Pantheon if Terminus token is present\n", "-[[ \"$TERMINUS_TOKEN\" != \"\" ]] && terminus_login\n", "-\n", "-# If crontab file is found within project add contents to user crontab file.\n", "-if [[ -f ${PROJECT_ROOT}/.docksal/services/cli/crontab ]]; then\n", "-\techo-debug \"Loading crontab...\"\n", "-\tcat ${PROJECT_ROOT}/.docksal/services/cli/crontab | crontab -u docker -\n", "-fi\n", "-\n", "-# Apply git settings\n", "-[[ \"$GIT_USER_EMAIL\" != \"\" ]] && [[ \"$GIT_USER_NAME\" != \"\" ]] && git_settings\n", "-\n", "-# Initialization steps completed. Create a pid file to mark the container as healthy\n", "-echo-debug \"Preliminary initialization completed.\"\n", "-touch /var/run/cli\n", "-\n", "-# Execute a custom startup script if present\n", "-if [[ -x ${PROJECT_ROOT}/.docksal/services/cli/startup.sh ]]; then\n", "-\techo-debug \"Running custom startup script...\"\n", "-\t# TODO: should we source the script instead?\n", "-\tsu -l docker -c \"${PROJECT_ROOT}/.docksal/services/cli/startup.sh\"\n", "-\tif [[ $? == 0 ]]; then\n", "-\t\techo-debug \"Custom startup script executed successfully.\"\n", "-\telse\n", "-\t\techo-debug \"ERROR: Custom startup script execution failed.\"\n", "-\tfi\n", "-fi\n", "-\n", "-# Execute passed CMD arguments\n", "-echo-debug \"Passing execution to: $*\"\n", "-# Service mode (run as root)\n", "-if [[ \"$1\" == \"supervisord\" ]]; then\n", "-\texec gosu root supervisord -c /etc/supervisor/supervisord.conf\n", "-# Command mode (run as docker user)\n", "-else\n", "-\texec gosu docker \"$@\"\n", "-fi\n", "\n", "\n", "========================================================================================================================\n", "Add composer version symlink\n", "\n", "--- 7.4/startup.sh\n", "+++ 7.4/startup.sh\n", "@@ -215,8 +215,9 @@\n", " echo-debug \"Passing execution to: $*\"\n", " # Service mode (run as root)\n", " if [[ \"$1\" == \"supervisord\" ]]; then\n", " \texec gosu root supervisord -c /etc/supervisor/supervisord.conf\n", " # Command mode (run as docker user)\n", " else\n", " \texec gosu docker \"$@\"\n", " fi\n", "+[[ \"${COMPOSER_DEFAULT_VERSION}\" != \"\" ]] && [[ -f /usr/local/bin/composer${COMPOSER_DEFAULT_VERSION} ]] && ln -sf /usr/local/bin/composer${COMPOSER_DEFAULT_VERSION} /usr/local/bin/composer\n", "\n", "\n", "========================================================================================================================\n", "Add initialization steps\n", "\n", "--- 7.4/startup.sh\n", "+++ 7.4/startup.sh\n", "@@ -215,8 +215,77 @@\n", " echo-debug \"Passing execution to: $*\"\n", " # Service mode (run as root)\n", " if [[ \"$1\" == \"supervisord\" ]]; then\n", " \texec gosu root supervisord -c /etc/supervisor/supervisord.conf\n", " # Command mode (run as docker user)\n", " else\n", " \texec gosu docker \"$@\"\n", " fi\n", "+\n", "+# Convert all Environment Variables Prefixed with SECRET_\n", "+convert_secrets\n", "+\n", "+# Docker user uid/gid mapping to the host user uid/gid\n", "+[[ \"$HOST_UID\" != \"\" ]] && [[ \"$HOST_GID\" != \"\" ]] && uid_gid_reset\n", "+\n", "+# Enable xdebug\n", "+[[ \"$XDEBUG_ENABLED\" != \"\" ]] && [[ \"$XDEBUG_ENABLED\" != \"0\" ]] && xdebug_enable\n", "+\n", "+# Enable xdebug\n", "+[[ \"$XHPROF_ENABLED\" != \"\" ]] && [[ \"$XHPROF_ENABLED\" != \"0\" ]] && xhprof_enable\n", "+\n", "+# Enable opcache preload\n", "+[[ -f \"/var/www/.docksal/etc/php/preload.php\" ]] && opcache_preload_enable\n", "+\n", "+# Enable web IDE\n", "+[[ \"$IDE_ENABLED\" != \"\" ]] && [[ \"$IDE_ENABLED\" != \"0\" ]] && ide_mode_enable\n", "+\n", "+# Include project level PHP settings if found\n", "+php_settings\n", "+\n", "+# Make sure permissions are correct (after uid/gid change and COPY operations in Dockerfile)\n", "+# To not bloat the image size, permissions on the home folder are reset at runtime.\n", "+echo-debug \"Resetting permissions on $HOME_DIR and /var/www...\"\n", "+chown \"${HOST_UID:-1000}:${HOST_GID:-1000}\" -R \"$HOME_DIR\"\n", "+# <PERSON><PERSON> resets the project root folder permissions to 0:0 when cli is recreated (e.g. an env variable updated).\n", "+# We apply a fix/workaround for this at startup (non-recursive).\n", "+chown \"${HOST_UID:-1000}:${HOST_GID:-1000}\" /var/www\n", "+\n", "+# These have to happen after the home directory permissions are reset,\n", "+# otherwise the docker user may not have write access to /home/<USER>\n", "+# Automatically authenticate with Pantheon if Terminus token is present\n", "+[[ \"$TERMINUS_TOKEN\" != \"\" ]] && terminus_login\n", "+\n", "+# If crontab file is found within project add contents to user crontab file.\n", "+if [[ -f ${PROJECT_ROOT}/.docksal/services/cli/crontab ]]; then\n", "+\techo-debug \"Loading crontab...\"\n", "+\tcat ${PROJECT_ROOT}/.docksal/services/cli/crontab | crontab -u docker -\n", "+fi\n", "+\n", "+# Apply git settings\n", "+[[ \"$GIT_USER_EMAIL\" != \"\" ]] && [[ \"$GIT_USER_NAME\" != \"\" ]] && git_settings\n", "+\n", "+# Initialization steps completed. Create a pid file to mark the container as healthy\n", "+echo-debug \"Preliminary initialization completed.\"\n", "+touch /var/run/cli\n", "+\n", "+# Execute a custom startup script if present\n", "+if [[ -x ${PROJECT_ROOT}/.docksal/services/cli/startup.sh ]]; then\n", "+\techo-debug \"Running custom startup script...\"\n", "+\t# TODO: should we source the script instead?\n", "+\tsu -l docker -c \"${PROJECT_ROOT}/.docksal/services/cli/startup.sh\"\n", "+\tif [[ $? == 0 ]]; then\n", "+\t\techo-debug \"Custom startup script executed successfully.\"\n", "+\telse\n", "+\t\techo-debug \"ERROR: Custom startup script execution failed.\"\n", "+\tfi\n", "+fi\n", "+\n", "+# Execute passed CMD arguments\n", "+echo-debug \"Passing execution to: $*\"\n", "+# Service mode (run as root)\n", "+if [[ \"$1\" == \"supervisord\" ]]; then\n", "+\texec gosu root supervisord -c /etc/supervisor/supervisord.conf\n", "+# Command mode (run as docker user)\n", "+else\n", "+\texec gosu docker \"$@\"\n", "+fi\n", "\n", "\n", "========================================================================================================================\n", "Refactor startup script\n", "\n", "--- 7.3/startup.sh\n", "+++ 7.3/startup.sh\n", "@@ -1,9 +1,124 @@\n", "+#!/bin/bash\n", "+\n", "+# This script is running as root by default.\n", "+# Switching to the docker user can be done via \"gosu docker <command>\".\n", "+\n", "+HOME_DIR='/home/<USER>'\n", "+\n", "+DEBUG=${DEBUG:-0}\n", "+# Turn debugging ON when cli is started in the service mode\n", "+[[ \"$1\" == \"supervisord\" ]] && DEBUG=1\n", "+echo-debug ()\n", "+{\n", "+\t[[ \"$DEBUG\" != 0 ]] && echo \"$(date +\"%F %H:%M:%S\") | $@\"\n", "+}\n", "+\n", "+uid_gid_reset ()\n", "+{\n", "+\tif [[ \"$HOST_UID\" != \"$(id -u docker)\" ]] || [[ \"$HOST_GID\" != \"$(id -g docker)\" ]]; then\n", "+\t\techo-debug \"Updating docker user uid/gid to $HOST_UID/$HOST_GID to match the host user uid/gid...\"\n", "+\t\tusermod -u \"$HOST_UID\" -o docker\n", "+\t\tgroupmod -g \"$HOST_GID\" -o \"$(id -gn docker)\"\n", "+\tfi\n", "+}\n", "+\n", "+xdebug_enable ()\n", "+{\n", "+\techo-debug \"Enabling xdebug...\"\n", "+\tln -s /opt/docker-php-ext-xdebug.ini /usr/local/etc/php/conf.d/\n", "+}\n", "+\n", "+xhprof_enable ()\n", "+{\n", "+\techo-debug \"Enabling xhprof...\"\n", "+\tcp /opt/docker-php-ext-xhprof.ini /usr/local/etc/php/conf.d/\n", "+\t# Output directory to the ini file\n", "+\techo \"xhprof.output_dir = ${XHPROF_OUTPUT_DIR}\" >> /usr/local/etc/php/conf.d/docker-php-ext-xhprof.ini\n", "+\t# Try to create directory if it doesn't exist\n", "+\tmkdir ${XHPROF_OUTPUT_DIR} || true\n", "+\t# Change owner of directory\n", "+\tchown docker:docker ${XHPROF_OUTPUT_DIR}\n", "+}\n", "+\n", "+ide_mode_enable ()\n", "+{\n", "+\techo-debug \"Enabling web IDE...\"\n", "+\t# Enabled only code-server service (disabled all other services)\n", "+\t# TODO: [v3] split IDE/cli and php-fpm entirely\n", "+\trm -f /etc/supervisor/conf.d/supervisord-*.conf\n", "+\tif [[ \"$IDE_PASSWORD\" != \"\" ]]; then\n", "+\t\texport PASSWORD=\"${IDE_PASSWORD}\"\n", "+\tfi\n", "+\trender_tmpl \"/etc/supervisor/conf.d/supervisord-code-server.conf\"\n", "+\tmkdir -p ${VSCODE_HOME}/User\n", "+\tln -s /opt/code-server/settings.json ${VSCODE_HOME}/User/\n", "+}\n", "+\n", "+# Creates symlinks to project level overrides if they exist\n", "+php_settings ()\n", "+{\n", "+\tphp_ini=/var/www/.docksal/etc/php/php.ini\n", "+\tif [[ -f ${php_ini} ]]; then\n", "+\t\techo-debug \"Found project level overrides for PHP. Including:\"\n", "+\t\techo-debug \"${php_ini}\"\n", "+\t\tln -s /var/www/.docksal/etc/php/php.ini /usr/local/etc/php/conf.d/zzz-php.ini\n", "+\tfi\n", "+\n", "+\tphp_fpm_conf=/var/www/.docksal/etc/php/php-fpm.conf\n", "+\tif [[ -f ${php_fpm_conf} ]]; then\n", "+\t\techo-debug \"Found project level overrides for PHP-FPM. Including:\"\n", "+\t\techo-debug \"${php_fpm_conf}\"\n", "+\t\tln -s ${php_fpm_conf} /usr/local/etc/php-fpm.d/zzz-php-fpm.conf\n", "+\tfi\n", "+}\n", "+\n", "+add_ssh_key ()\n", "+{\n", "+\techo-debug \"Adding a private SSH key from SECRET_SSH_PRIVATE_KEY...\"\n", "+\trender_tmpl \"$HOME_DIR/.ssh/id_rsa\"\n", "+\tchmod 0600 \"$HOME_DIR/.ssh/id_rsa\"\n", "+}\n", "+\n", "+# Helper function to render configs from go templates using gomplate\n", "+render_tmpl ()\n", "+{\n", "+\tlocal file=\"${1}\"\n", "+\tlocal tmpl=\"${1}.tmpl\"\n", "+\n", "+\tif [[ -f \"${tmpl}\" ]]; then\n", "+\t\techo-debug \"Rendering template: ${tmpl}...\"\n", "+\t\t# go<PERSON><PERSON> started throwing an empty line into stderr in v3.7.0, so we have to mute it below\n", "+\t\tgomplate --file \"${tmpl}\" --out \"${file}\" &>/dev/null\n", "+\telse\n", "+\t\techo-debug \"Error: Template file not found: ${tmpl}\"\n", "+\t\treturn 1\n", "+\tfi\n", "+}\n", "+\n", "+# Helper function to loop through all environment variables prefixed with SECRET_ and\n", "+# convert to the equivalent variable without SECRET.\n", "+# Example: SECRET_TERMINUS_TOKEN => TERMINUS_TOKEN.\n", "+convert_secrets ()\n", "+{\n", "+\teval 'secrets=(${!SECRET_@})'\n", "+\tfor secret_key in \"${secrets[@]}\"; do\n", "+\t\tkey=${secret_key#SECRET_}\n", "+\t\tsecret_value=${!secret_key}\n", "+\n", "+\t\t# Write new variables to /etc/profile.d/secrets.sh to make them available for all users/sessions\n", "+\t\techo \"export ${key}=\\\"${secret_value}\\\"\" | tee -a \"/etc/profile.d/secrets.sh\" >/dev/null\n", "+\n", "+\t\t# Also export new variables here\n", "+\t\t# This makes them available in the server/php-fpm environment\n", "+\t\teval \"export ${key}=${secret_value}\"\n", "+\tdone\n", "+}\n", " #!/bin/bash\n", " \n", " # This script is running as root by default.\n", " # Switching to the docker user can be done via \"gosu docker <command>\".\n", " \n", " HOME_DIR='/home/<USER>'\n", " \n", " DEBUG=${DEBUG:-0}\n", " # Turn debugging ON when cli is started in the service mode\n", "@@ -124,91 +239,9 @@\n", "@terminus_login ()\n", " \tlocal output=$(su -l docker -c \"${command}\" 2>&1)\n", " \tif [[ $? != 0 ]]; then\n", " \t\techo-debug \"ERROR: Pantheon authentication failed.\"\n", " \t\techo\n", " \t\techo \"$output\"\n", " \t\techo\n", " \tfi\n", " }\n", " \n", "-# Git settings\n", "-git_settings ()\n", "-{\n", "-\t# These must be run as the docker user\n", "-\techo-debug \"Configuring git...\"\n", "-\t# Set default git settings if none have been passed\n", "-\t# See https://github.com/docksal/service-cli/issues/124\n", "-\tgosu docker git config --global user.email \"${GIT_USER_EMAIL:-<EMAIL>}\"\n", "-\tgosu docker git config --global user.name \"${GIT_USER_NAME:-Docksal CLI}\"\n", "-}\n", "-\n", "-# Inject a private SSH key if provided\n", "-[[ \"$SECRET_SSH_PRIVATE_KEY\" != \"\" ]] && add_ssh_key\n", "-\n", "-# Set Composer Version\n", "-[[ \"${COMPOSER_DEFAULT_VERSION}\" != \"\" ]] && [[ -f /usr/local/bin/composer${COMPOSER_DEFAULT_VERSION} ]] && ln -sf /usr/local/bin/composer${COMPOSER_DEFAULT_VERSION} /usr/local/bin/composer\n", "-\n", "-# Convert all Environment Variables Prefixed with SECRET_\n", "-convert_secrets\n", "-\n", "-# Docker user uid/gid mapping to the host user uid/gid\n", "-[[ \"$HOST_UID\" != \"\" ]] && [[ \"$HOST_GID\" != \"\" ]] && uid_gid_reset\n", "-\n", "-# Enable xdebug\n", "-[[ \"$XDEBUG_ENABLED\" != \"\" ]] && [[ \"$XDEBUG_ENABLED\" != \"0\" ]] && xdebug_enable\n", "-\n", "-# Enable xdebug\n", "-[[ \"$XHPROF_ENABLED\" != \"\" ]] && [[ \"$XHPROF_ENABLED\" != \"0\" ]] && xhprof_enable\n", "-\n", "-# Enable web IDE\n", "-[[ \"$IDE_ENABLED\" != \"\" ]] && [[ \"$IDE_ENABLED\" != \"0\" ]] && ide_mode_enable\n", "-\n", "-# Include project level PHP settings if found\n", "-php_settings\n", "-\n", "-# Make sure permissions are correct (after uid/gid change and COPY operations in Dockerfile)\n", "-# To not bloat the image size, permissions on the home folder are reset at runtime.\n", "-echo-debug \"Resetting permissions on $HOME_DIR and /var/www...\"\n", "-chown \"${HOST_UID:-1000}:${HOST_GID:-1000}\" -R \"$HOME_DIR\"\n", "-# <PERSON><PERSON> resets the project root folder permissions to 0:0 when cli is recreated (e.g. an env variable updated).\n", "-# We apply a fix/workaround for this at startup (non-recursive).\n", "-chown \"${HOST_UID:-1000}:${HOST_GID:-1000}\" /var/www\n", "-\n", "-# These have to happen after the home directory permissions are reset,\n", "-# otherwise the docker user may not have write access to /home/<USER>\n", "-# Automatically authenticate with Pantheon if Terminus token is present\n", "-[[ \"$TERMINUS_TOKEN\" != \"\" ]] && terminus_login\n", "-\n", "-# If crontab file is found within project add contents to user crontab file.\n", "-if [[ -f ${PROJECT_ROOT}/.docksal/services/cli/crontab ]]; then\n", "-\techo-debug \"Loading crontab...\"\n", "-\tcat ${PROJECT_ROOT}/.docksal/services/cli/crontab | crontab -u docker -\n", "-fi\n", "-\n", "-# Apply git settings\n", "-[[ \"$GIT_USER_EMAIL\" != \"\" ]] && [[ \"$GIT_USER_NAME\" != \"\" ]] && git_settings\n", "-\n", "-# Initialization steps completed. Create a pid file to mark the container as healthy\n", "-echo-debug \"Preliminary initialization completed.\"\n", "-touch /var/run/cli\n", "-\n", "-# Execute a custom startup script if present\n", "-if [[ -x ${PROJECT_ROOT}/.docksal/services/cli/startup.sh ]]; then\n", "-\techo-debug \"Running custom startup script...\"\n", "-\t# TODO: should we source the script instead?\n", "-\tsu -l docker -c \"${PROJECT_ROOT}/.docksal/services/cli/startup.sh\"\n", "-\tif [[ $? == 0 ]]; then\n", "-\t\techo-debug \"Custom startup script executed successfully.\"\n", "-\telse\n", "-\t\techo-debug \"ERROR: Custom startup script execution failed.\"\n", "-\tfi\n", "-fi\n", "-\n", "-# Execute passed CMD arguments\n", "-echo-debug \"Passing execution to: $*\"\n", "-# Service mode (run as root)\n", "-if [[ \"$1\" == \"supervisord\" ]]; then\n", "-\texec gosu root supervisord -c /etc/supervisor/supervisord.conf\n", "-# Command mode (run as docker user)\n", "-else\n", "-\texec gosu docker \"$@\"\n", "-fi\n", "\n", "\n", "========================================================================================================================\n", "Add Acquia CLI login functionality\n", "\n", "--- 7.3/startup.sh\n", "+++ 7.3/startup.sh\n", "@@ -206,9 +206,25 @@\n", " # Execute passed CMD arguments\n", " echo-debug \"Passing execution to: $*\"\n", " # Service mode (run as root)\n", " if [[ \"$1\" == \"supervisord\" ]]; then\n", " \texec gosu root supervisord -c /etc/supervisor/supervisord.conf\n", " # Command mode (run as docker user)\n", " else\n", " \texec gosu docker \"$@\"\n", " fi\n", "+# Acquia CLI login\n", "+acli_login ()\n", "+{\n", "+\techo-debug \"Authenticating with Acquia...\"\n", "+\t# This has to be done using the docker user via su to load the user environment\n", "+\t# Note: Using 'su -l' to initiate a login session and have .profile sourced for the docker user\n", "+\tlocal command=\"acli auth:login --key='${ACQUIA_CLI_KEY}' --secret='${ACQUIA_CLI_SECRET}' --no-interaction\"\n", "+\tlocal output=$(su -l docker -c \"${command}\" 2>&1)\n", "+\tif [[ $? != 0 ]]; then\n", "+\t\techo-debug \"ERROR: Acquia authentication failed.\"\n", "+\t\techo\n", "+\t\techo \"$output\"\n", "+\t\techo\n", "+\tfi\n", "+}\n", "+\n", "\n", "\n", "========================================================================================================================\n", "Add initialization steps\n", "\n", "--- 7.3/startup.sh\n", "+++ 7.3/startup.sh\n", "@@ -206,9 +206,91 @@\n", " # Execute passed CMD arguments\n", " echo-debug \"Passing execution to: $*\"\n", " # Service mode (run as root)\n", " if [[ \"$1\" == \"supervisord\" ]]; then\n", " \texec gosu root supervisord -c /etc/supervisor/supervisord.conf\n", " # Command mode (run as docker user)\n", " else\n", " \texec gosu docker \"$@\"\n", " fi\n", "+# Git settings\n", "+git_settings ()\n", "+{\n", "+\t# These must be run as the docker user\n", "+\techo-debug \"Configuring git...\"\n", "+\t# Set default git settings if none have been passed\n", "+\t# See https://github.com/docksal/service-cli/issues/124\n", "+\tgosu docker git config --global user.email \"${GIT_USER_EMAIL:-<EMAIL>}\"\n", "+\tgosu docker git config --global user.name \"${GIT_USER_NAME:-Docksal CLI}\"\n", "+}\n", "+\n", "+# Inject a private SSH key if provided\n", "+[[ \"$SECRET_SSH_PRIVATE_KEY\" != \"\" ]] && add_ssh_key\n", "+\n", "+# Set Composer Version\n", "+[[ \"${COMPOSER_DEFAULT_VERSION}\" != \"\" ]] && [[ -f /usr/local/bin/composer${COMPOSER_DEFAULT_VERSION} ]] && ln -sf /usr/local/bin/composer${COMPOSER_DEFAULT_VERSION} /usr/local/bin/composer\n", "+\n", "+# Convert all Environment Variables Prefixed with SECRET_\n", "+convert_secrets\n", "+\n", "+# Docker user uid/gid mapping to the host user uid/gid\n", "+[[ \"$HOST_UID\" != \"\" ]] && [[ \"$HOST_GID\" != \"\" ]] && uid_gid_reset\n", "+\n", "+# Enable xdebug\n", "+[[ \"$XDEBUG_ENABLED\" != \"\" ]] && [[ \"$XDEBUG_ENABLED\" != \"0\" ]] && xdebug_enable\n", "+\n", "+# Enable xdebug\n", "+[[ \"$XHPROF_ENABLED\" != \"\" ]] && [[ \"$XHPROF_ENABLED\" != \"0\" ]] && xhprof_enable\n", "+\n", "+# Enable web IDE\n", "+[[ \"$IDE_ENABLED\" != \"\" ]] && [[ \"$IDE_ENABLED\" != \"0\" ]] && ide_mode_enable\n", "+\n", "+# Include project level PHP settings if found\n", "+php_settings\n", "+\n", "+# Make sure permissions are correct (after uid/gid change and COPY operations in Dockerfile)\n", "+# To not bloat the image size, permissions on the home folder are reset at runtime.\n", "+echo-debug \"Resetting permissions on $HOME_DIR and /var/www...\"\n", "+chown \"${HOST_UID:-1000}:${HOST_GID:-1000}\" -R \"$HOME_DIR\"\n", "+# <PERSON><PERSON> resets the project root folder permissions to 0:0 when cli is recreated (e.g. an env variable updated).\n", "+# We apply a fix/workaround for this at startup (non-recursive).\n", "+chown \"${HOST_UID:-1000}:${HOST_GID:-1000}\" /var/www\n", "+\n", "+# These have to happen after the home directory permissions are reset,\n", "+# otherwise the docker user may not have write access to /home/<USER>\n", "+# Automatically authenticate with Pantheon if Terminus token is present\n", "+[[ \"$TERMINUS_TOKEN\" != \"\" ]] && terminus_login\n", "+\n", "+# If crontab file is found within project add contents to user crontab file.\n", "+if [[ -f ${PROJECT_ROOT}/.docksal/services/cli/crontab ]]; then\n", "+\techo-debug \"Loading crontab...\"\n", "+\tcat ${PROJECT_ROOT}/.docksal/services/cli/crontab | crontab -u docker -\n", "+fi\n", "+\n", "+# Apply git settings\n", "+[[ \"$GIT_USER_EMAIL\" != \"\" ]] && [[ \"$GIT_USER_NAME\" != \"\" ]] && git_settings\n", "+\n", "+# Initialization steps completed. Create a pid file to mark the container as healthy\n", "+echo-debug \"Preliminary initialization completed.\"\n", "+touch /var/run/cli\n", "+\n", "+# Execute a custom startup script if present\n", "+if [[ -x ${PROJECT_ROOT}/.docksal/services/cli/startup.sh ]]; then\n", "+\techo-debug \"Running custom startup script...\"\n", "+\t# TODO: should we source the script instead?\n", "+\tsu -l docker -c \"${PROJECT_ROOT}/.docksal/services/cli/startup.sh\"\n", "+\tif [[ $? == 0 ]]; then\n", "+\t\techo-debug \"Custom startup script executed successfully.\"\n", "+\telse\n", "+\t\techo-debug \"ERROR: Custom startup script execution failed.\"\n", "+\tfi\n", "+fi\n", "+\n", "+# Execute passed CMD arguments\n", "+echo-debug \"Passing execution to: $*\"\n", "+# Service mode (run as root)\n", "+if [[ \"$1\" == \"supervisord\" ]]; then\n", "+\texec gosu root supervisord -c /etc/supervisor/supervisord.conf\n", "+# Command mode (run as docker user)\n", "+else\n", "+\texec gosu docker \"$@\"\n", "+fi\n", "\n", "\n", "========================================================================================================================\n", "Simplify workflow configuration\n", "\n", "--- .github/workflows/default.yaml\n", "+++ .github/workflows/default.yaml\n", "@@ -1,7 +1,47 @@\n", "+name: <PERSON><PERSON><PERSON> (push\n", "+\n", "+on:\n", "+  push:\n", "+    branches:\n", "+      - master\n", "+      - develop\n", "+      - feature/*\n", "+    tags:\n", "+      - v*\n", "+\n", "+defaults:\n", "+  run:\n", "+    shell: bash\n", "+\n", "+env:\n", "+  IMAGE: docksal/cli\n", "+  LATEST_VERSION: 7.3\n", "+  DOCKSAL_VERSION: develop\n", "+\n", "+jobs:\n", "+  build-test-push:\n", "+    name: Build on ${{ matrix.runs-on }} ${{ matrix.version }}\n", "+    runs-on: ${{ matrix.runs-on }}\n", "+\n", "+    strategy:\n", "+      fail-fast: false # Don't cancel other jobs if one fails\n", "+      matrix:\n", "+        version:\n", "+          - runs-on: [ubuntu-20.04]\n", "+            version: 7.3\n", "+          - runs-on: [ubuntu-20.04,ARM64]\n", "+            version: 7.4\n", "+\n", "+    env:\n", "+      IMAGE: docksal/cli\n", "+      UPSTREAM_IMAGE: debian\n", "+      LATEST_VERSION: 7.3\n", "+      VERSION: ${{ matrix.version }}\n", "+      DOCKSAL_VERSION: develop\n", " name: <PERSON><PERSON><PERSON> (push\n", " \n", " on:\n", "   push:\n", "     branches:\n", "       - master\n", "       - develop\n", "@@ -51,41 +91,15 @@\n", "@jobs:\n", "@  build-test-push:\n", "@    steps:\n", "           git clone https://github.com/bats-core/bats-core.git\n", "           cd bats-core\n", "           sudo ./install.sh /usr/local\n", "           bats -v\n", "       -\n", "         name: <PERSON>out\n", "         uses: actions/checkout@v2\n", "-      -\n", "-        name: Set up QEMU\n", "-        uses: docker/setup-qemu-action@v1\n", "-      -\n", "-        name: Set up Docker Buildx\n", "-        uses: docker/setup-buildx-action@v1\n", "-      -\n", "-        name: <PERSON>\n", "-        run: |\n", "-          docker version\n", "-          docker info\n", "-      -\n", "-        name: <PERSON><PERSON> to <PERSON><PERSON><PERSON><PERSON>\n", "-        uses: docker/login-action@v1\n", "-        with:\n", "-          username: ${{ secrets.DOCKERHUB_USERNAME }}\n", "-          password: ${{ secrets.DOCKERHUB_TOKEN }}\n", "-      -\n", "-        name: <PERSON><PERSON> to GitHub Container Registry\n", "-        uses: docker/login-action@v1\n", "-        with:\n", "-          registry: ghcr.io\n", "-          username: ${{ secrets.GHCR_USERNAME }}\n", "-          password: ${{ secrets.GHCR_TOKEN }}\n", "-#      -\n", "-#        name: Build\n", "-#        working-directory: ${{ steps.docker_meta.outputs.tags }}`\n", "+ ${{ steps.docker_meta.outputs.tags }}`\n", "         # See https://github.com/crazy-max/ghaction-docker-meta\n", "         name: <PERSON><PERSON> meta\n", "         id: docker_meta\n", "         uses: crazy-max/ghaction-docker-meta@v1\n", "         with        # Generate image meta information\n", "         name: Docker image tags\n", "         id: docker_tags\n", "\n", "\n", "========================================================================================================================\n", "Enable QEMU setup\n", "\n", "--- .github/workflows/default.yaml\n", "+++ .github/workflows/default.yaml\n", "@@ -77,16 +77,18 @@\n", "@jobs:\n", "@  build-test-push:\n", "@    steps:\n", "         uses: docker/login-action@v1\n", "         with:\n", "           registry: ghcr.io\n", "           username: ${{ secrets.GHCR_USERNAME }}\n", "           password: ${{ secrets.GHCR_TOKEN }}\n", " #      -\n", " #        name: Build\n", "-#        working-directory: ${{ steps.docker_meta.outputs.tags }}`\n", "-        # See https://github.com/crazy-max/ghaction-docker-meta\n", "+#        working-directory:#      -\n", "+#        name: Set up QEMU\n", "+#        uses: docker/setup-qemu-action@v1\n", "+zy-max/ghaction-docker-meta\n", "         name: <PERSON><PERSON> meta\n", "         id: docker_meta\n", "         uses: crazy-max/ghaction-docker-meta@v1\n", "         with        # Generate image meta information\n", "         name: Docker image tags\n", "         id: docker_tags\n", "         run: make tags\n", "\n", "\n", "========================================================================================================================\n", "Replace Docker meta with Buildx and add checks\n", "\n", "--- .github/workflows/default.yaml\n", "+++ .github/workflows/default.yaml\n", "@@ -78,20 +78,24 @@\n", "@jobs:\n", "@  build-test-push:\n", "@    steps:\n", "         with:\n", "           registry: ghcr.io\n", "           username: ${{ secrets.GHCR_USERNAME }}\n", "           password: ${{ secrets.GHCR_TOKEN }}\n", " #      -\n", " #        name: Build\n", " #        working-directory: ${{ steps.docker_meta.outputs.tags }}`\n", "-        # See https://github.com/crazy-max/ghaction-docker-meta\n", "-        name: <PERSON><PERSON> meta\n", "-        id: docker_meta\n", "-        uses: crazy-max/ghaction-docker-meta@v1\n", "-        with        # Generate image meta information\n", "-        name: <PERSON>er image tags\n", "+        # See https://github.com/cra      -\n", "+        name: Set up Docker Buildx\n", "+        uses: docker/setup-buildx-action@v1\n", "+      -\n", "+        name: <PERSON>\n", "+        run: |\n", "+          docker version\n", "+          docker info\n", "+      -\n", "+: Docker image tags\n", "         id: docker_tags\n", "         run: make tags\n", "       -\n", "         # Build for local use\n", "         name: Build and cache image (local)\n", "         id: docker_build\n", "         uses: docker/build-push-action@v2\n", "\n", "\n", "========================================================================================================================\n", "Fix indentation\n", "\n", "--- .github/workflows/default.yaml\n", "+++ .github/workflows/default.yaml\n", "@@ -83,16 +83,16 @@\n", " #        name: Build\n", " #        working-directory: ${{ steps.docker_meta.outputs.tags }}`\n", "         # See https://github.com/crazy-max/ghaction-docker-meta\n", "         name: <PERSON><PERSON> meta\n", "         id: docker_meta\n", "         uses: crazy-max/ghaction-docker-meta@v1\n", "         with        # Generate image meta information\n", "-        name: <PERSON>er image tags\n", "-        id: docker_tags\n", "+        name        name: <PERSON><PERSON> to <PERSON><PERSON>\n", "+ocker_tags\n", "         run: make tags\n", "       -\n", "         # Build for local use\n", "         name: Build and cache image (local)\n", "         id: docker_build\n", "         uses: docker/build-push-action@v2\n", "         env:\n", "\n", "\n", "========================================================================================================================\n", "Replace Docker build with login action\n", "\n", "--- .github/workflows/default.yaml\n", "+++ .github/workflows/default.yaml\n", "@@ -84,26 +84,25 @@\n", " #        working-directory: ${{ steps.docker_meta.outputs.tags }}`\n", "         # See https://github.com/crazy-max/ghaction-docker-meta\n", "         name: <PERSON><PERSON> meta\n", "         id: docker_meta\n", "         uses: crazy-max/ghaction-docker-meta@v1\n", "         with        # Generate image meta information\n", "         name: Docker image tags\n", "-        id: docker_tags\n", "-        run: make tags\n", "+        id: d        uses: docker/login-action@v1\n", "+        with:\n", "+          username: ${{ secrets.DOCKERHUB_USERNAME }}\n", "+          password: ${{ secrets.DOCKERHUB_TOKEN }}\n", "       -\n", "-        # Build for local use\n", "-        name: Build and cache image (local)\n", "-        id: docker_build\n", "-        uses: docker/build-push-action@v2\n", "-        env:\n", "-          IMAGE_CACHE: ghcr.io/${{ env.IMAGE }}:php${{ env.VERSION }}-build\n", "+        name: <PERSON><PERSON> to GitHub Container Registry\n", "+        uses: docker/login-action@v1\n", "         with:\n", "-          context: ${{ matrix.versionenv.VERSION }}\n", "-          file: ${{ matrix.version }}/Dockerfile\n", "+          registry: ghcr.io\n", "+          username: ${{ secrets.GHCR_USERNAME }}\n", "+          password: ${{ secrets.GHCR_TOKEN }}\n", "           #platforms: linux/amd64,linux/arm64|\n", "             UPSTREAM_IMAGE=${{ env.UPSTREAM_IMAGE }}\n", "             VERSION=${{ env.VERSION }}\n", "           tags: ${{ env.IMAGE }}:php${{ matrix.version }}-build # Tag used locally in tests\n", "           load: true # cache image locally for use by other steps\n", "           #push: true # cannot use \"push\" together with \"load\"\n", "           cache-from: type=registry,ref=ghcr.io/${{ env.IMAGE }}:php${{ matrix.version }}-build\n", "\n", "\n", "========================================================================================================================\n", "Add comments and formatting\n", "\n", "--- .github/workflows/default.yaml\n", "+++ .github/workflows/default.yaml\n", "@@ -96,16 +96,18 @@\n", "@#        working-directory: ${{ steps.docker_meta.outputs.tags }}`\n", "         id: docker_build\n", "         uses: docker/build-push-action@v2\n", "         env:\n", "           IMAGE_CACHE: ghcr.io/${{ env.IMAGE }}:php${{ env.VERSION }}-build\n", "         with:\n", "           context: ${{ matrix.versionenv.VERSION }}\n", "           file: ${{ matrix.version }}/Dockerfile\n", "-          #platforms: linux/amd64,linux/arm64|\n", "-            UPSTREAM_IMAGE=${{ env.UPSTREAM_IMAGE }}\n", "+      -\n", "+        # Calculates docker image tags for the given build context\n", "+        # The output is used in build and push step as `tags: ${{ steps.docker_meta.outputs.tags }}`\n", "+}}\n", "             VERSION=${{ env.VERSION }}\n", "           tags: ${{ env.IMAGE }}:php${{ matrix.version }}-build # Tag used locally in tests\n", "           load: true # cache image locally for use by other steps\n", "           #push: true # cannot use \"push\" together with \"load\"\n", "           cache-from: type=registry,ref=ghcr.io/${{ env.IMAGE }}:php${{ matrix.version }}-build\n", "           cache-to: type=inline # Write the cache metadata into the image configuration\n", "       -\n", "\n", "\n", "========================================================================================================================\n", "Extract and format Docker meta data\n", "\n", "--- .github/workflows/default.yaml\n", "+++ .github/workflows/default.yaml\n", "@@ -97,18 +97,19 @@\n", "@#        working-directory: ${{ steps.docker_meta.outputs.tags }}`\n", "         uses: docker/build-push-action@v2\n", "         env:\n", "           IMAGE_CACHE: ghcr.io/${{ env.IMAGE }}:php${{ env.VERSION }}-build\n", "         with:\n", "           context: ${{ matrix.versionenv.VERSION }}\n", "           file: ${{ matrix.version }}/Dockerfile\n", "           #platforms: linux/amd64,linux/arm64|\n", "-            UPSTREAM_IMAGE=${{ env.UPSTREAM_IMAGE }}\n", "-            VERSION=${{ env.VERSION }}\n", "-          tags: ${{ env.IMAGE }}:php${{ matrix.version }}-build # Tag used locally in tests\n", "-          load: true # cache image locally for use by other steps\n", "+            UPSTREAM_IMAGE=${{ env.UPSTREAM_IMAGE         # See https://github.com/crazy-max/ghaction-docker-meta\n", "+        name: <PERSON><PERSON> meta\n", "+        id: docker_meta\n", "+        uses: crazy-max/ghaction-docker-meta@v1\n", "+ image locally for use by other steps\n", "           #push: true # cannot use \"push\" together with \"load\"\n", "           cache-from: type=registry,ref=ghcr.io/${{ env.IMAGE }}:php${{ matrix.version }}-build\n", "           cache-to: type=inline # Write the cache metadata into the image configuration\n", "       -\n", "         # Print image info\n", "         name: Image info\n", "         run: |\n", "\n", "\n", "========================================================================================================================\n", "Improve Docker caching\n", "\n", "--- .github/workflows/default.yaml\n", "+++ .github/workflows/default.yaml\n", "@@ -100,16 +100,23 @@\n", "@#        working-directory: ${{ steps.docker_meta.outputs.tags }}`\n", "         with:\n", "           context: ${{ matrix.versionenv.VERSION }}\n", "           file: ${{ matrix.version }}/Dockerfile\n", "           #platforms: linux/amd64,linux/arm64|\n", "             UPSTREAM_IMAGE=${{ env.UPSTREAM_IMAGE }}\n", "             VERSION=${{ env.VERSION }}\n", "           tags: ${{ env.IMAGE }}:php${{ matrix.version }}-build # Tag used locally in tests\n", "-          load: true # cache image locally for use by other steps\n", "-          #push: true # cannot use \"push\" together with \"load\"\n", "+          load: true # cache        with:\n", "+          # List of Docker images to use as base name for tags\n", "+          images: |\n", "+            ${{ env.IMAGE }}\n", "+            ghcr.io/${{ env.IMAGE }}\n", "+          tag-sha: true # add git short SHA as Docker tag\n", "+      -\n", "+        # Generate image meta information\n", "+ true # cannot use \"push\" together with \"load\"\n", "           cache-from: type=registry,ref=ghcr.io/${{ env.IMAGE }}:php${{ matrix.version }}-build\n", "           cache-to: type=inline # Write the cache metadata into the image configuration\n", "       -\n", "         # Print image info\n", "         name: Image info\n", "         run: |\n", "           set -xeuo pipefail\n", "\n", "\n", "========================================================================================================================\n", "Split build and tag steps\n", "\n", "--- .github/workflows/default.yaml\n", "+++ .github/workflows/default.yaml\n", "@@ -101,16 +101,20 @@\n", "@#        working-directory: ${{ steps.docker_meta.outputs.tags }}`\n", "@        with:\n", "           context: ${{ matrix.versionenv.VERSION }}\n", "           file: ${{ matrix.version }}/Dockerfile\n", "           #platforms: linux/amd64,linux/arm64|\n", "             UPSTREAM_IMAGE=${{ env.UPSTREAM_IMAGE }}\n", "             VERSION=${{ env.VERSION }}\n", "           tags: ${{ env.IMAGE }}:php${{ matrix.version }}-build # Tag used locally in tests\n", "           load: true # cache image locally for use by other steps\n", "-          #push: true # cannot use \"push\" together with \"load\"\n", "-          cache-from: type=registry,ref=ghcr.io/${{ env.IMAGE }}:php${{ matrix.version }}-build\n", "+          #push:        name: Docker image tags\n", "+        id: docker_tags\n", "+        run: make tags\n", "+      -\n", "+        # Build for local use\n", "+{ matrix.version }}-build\n", "           cache-to: type=inline # Write the cache metadata into the image configuration\n", "       -\n", "         # Print image info\n", "         name: Image info\n", "         run: |\n", "           set -xeuo pipefail\n", "           docker image ls | grep \"${{ env.IMAGE }}\"\n", "\n", "\n", "========================================================================================================================\n", "Update cache configuration\n", "\n", "--- .github/workflows/default.yaml\n", "+++ .github/workflows/default.yaml\n", "@@ -102,16 +102,16 @@\n", "@#        working-directory: ${{ steps.docker_meta.outputs.tags }}`\n", "@        with:\n", "           file: ${{ matrix.version }}/Dockerfile\n", "           #platforms: linux/amd64,linux/arm64|\n", "             UPSTREAM_IMAGE=${{ env.UPSTREAM_IMAGE }}\n", "             VERSION=${{ env.VERSION }}\n", "           tags: ${{ env.IMAGE }}:php${{ matrix.version }}-build # Tag used locally in tests\n", "           load: true # cache image locally for use by other steps\n", "           #push: true # cannot use \"push\" together with \"load\"\n", "-          cache-from: type=registry,ref=ghcr.io/${{ env.IMAGE }}:php${{ matrix.version }}-build\n", "-          cache-to: type=inline # Write the cache metadata into the image configuration\n", "+          cache-from: type=registry,ref=ghcr.io/${{ env.IMAGE }}:php${        name: Build image (local)\n", "+: type=inline # Write the cache metadata into the image configuration\n", "       -\n", "         # Print image info\n", "         name: Image info\n", "         run: |\n", "           set -xeuo pipefail\n", "           docker image ls | grep \"${{ env.IMAGE }}\"\n", "           docker image inspect \"${{ env.IMAGE }}:php${{ matrix.version }}-build\"\n", "\n", "\n", "========================================================================================================================\n", "Update Docker build-push-action\n", "\n", "--- .github/workflows/default.yaml\n", "+++ .github/workflows/default.yaml\n", "@@ -103,20 +103,20 @@\n", "@#        working-directory: ${{ steps.docker_meta.outputs.tags }}`\n", "@        with:\n", "           #platforms: linux/amd64,linux/arm64|\n", "             UPSTREAM_IMAGE=${{ env.UPSTREAM_IMAGE }}\n", "             VERSION=${{ env.VERSION }}\n", "           tags: ${{ env.IMAGE }}:php${{ matrix.version }}-build # Tag used locally in tests\n", "           load: true # cache image locally for use by other steps\n", "           #push: true # cannot use \"push\" together with \"load\"\n", "           cache-from: type=registry,ref=ghcr.io/${{ env.IMAGE }}:php${{ matrix.version }}-build\n", "-          cache-to: type=inline # Write the cache metadata into the image configuration\n", "-      -\n", "-        # Print image info\n", "-        name: Image info\n", "-        run: |\n", "-          set -xeuo pipefail\n", "+          cache-to        id: docker_build\n", "+        uses: docker/build-push-action@v2\n", "+        env:\n", "+          IMAGE_CACHE: ghcr.io/${{ env.IMAGE }}:php${{ env.VERSION }}-build\n", "+        with:\n", "+ail\n", "           docker image ls | grep \"${{ env.IMAGE }}\"\n", "           docker image inspect \"${{ env.IMAGE }}:php${{ matrix.version }}-build\"\n", "       -\n", "         # Cache image layers in the registry\n", "         name: Build and cache (ghcr.io)\n", "         uses: docker/build-push-action@v2\n", "         with:\n", "\n", "\n", "========================================================================================================================\n", "Simplify Docker build step\n", "\n", "--- .github/workflows/default.yaml\n", "+++ .github/workflows/default.yaml\n", "@@ -108,28 +108,23 @@\n", "@#        working-directory: ${{ steps.docker_meta.outputs.tags }}`\n", "@        with:\n", "           #push: true # cannot use \"push\" together with \"load\"\n", "           cache-from: type=registry,ref=ghcr.io/${{ env.IMAGE }}:php${{ matrix.version }}-build\n", "           cache-to: type=inline # Write the cache metadata into the image configuration\n", "       -\n", "         # Print image info\n", "         name: Image info\n", "         run: |\n", "-          set -xeuo pipefail\n", "-          docker image ls | grep \"${{ env.IMAGE }}\"\n", "-          docker image inspect \"${{ env.IMAGE }}:php${{ matrix.version }}-build\"\n", "-      -\n", "-        # Cache image layers in the registry\n", "-        name: Build and cache (ghcr.io)\n", "-        uses: docker/build-push-action@v2\n", "-        with:\n", "-          context: ${{ env.VERSION }}\n", "+          set -xeuo pipef          context: ${{ env.VERSION }}\n", "           file: ${{ env.VERSION }}/Dockerfile\n", "           build-args: |\n", "+            UPSTREAM_IMAGE=${{ env.UPSTREAM_IMAGE }}\n", "             VERSION=${{ env.VERSION }}\n", "-          tags: ${{ env.IMAGE }} # Build cache tag in ghcr.io\n", "-          push: ${{ github.event_name != 'pull_request' }} # Don't push for PRs\n", "+          tags: ${{ env.IMAGE }}:php${{ env.VERSION }}-build # Tag used locally in tests\n", "+          load: true # cache image locally for use by other steps\n", "+          cache-from: type=registry,ref=${{ env.IMAGE_CACHE }}\n", "+on't push for PRs\n", "           cache-to: type=inline # Write the cache metadata into the image configuration\n", "       -\n", "         # Run tests\n", "         name: <PERSON>\n", "         working-directory: ${{ env.VERSION }}\n", "         env:\n", "           SECRET_PLATFORMSH_CLI_TOKEN: ${{ secrets.SECRET_PLATFORMSH_CLI_TOKEN }}\n", "\n", "\n", "========================================================================================================================\n", "<PERSON><PERSON><PERSON> comments\n", "\n", "--- .github/workflows/default.yaml\n", "+++ .github/workflows/default.yaml\n", "@@ -121,18 +121,18 @@\n", "@#        working-directory: ${{ steps.docker_meta.outputs.tags }}`\n", "         uses: docker/build-push-action@v2\n", "         with:\n", "           context: ${{ env.VERSION }}\n", "           file: ${{ env.VERSION }}/Dockerfile\n", "           build-args: |\n", "             VERSION=${{ env.VERSION }}\n", "           tags: ${{ env.IMAGE }} # Build cache tag in ghcr.io\n", "-          push: ${{ github.event_name != 'pull_request' }} # Don't push for PRs\n", "-          cache-to: type=inline # Write the cache metadata into the image configuration\n", "+          push: ${{ github.event_name != 'pull_request' }} # D          cache-to: type=inline # Write the cache metadata into the image configuration\n", "       -\n", "-        # Run tests\n", "+        # Print image info\n", "+ Run tests\n", "         name: <PERSON>\n", "         working-directory: ${{ env.VERSION }}\n", "         env:\n", "           SECRET_PLATFORMSH_CLI_TOKEN: ${{ secrets.SECRET_PLATFORMSH_CLI_TOKEN }}\n", "           SECRET_TERMINUS_TOKEN: ${{ secrets.SECRET_TERMINUS_TOKEN }}\n", "         run: make test\n", "       -\n", "\n", "\n", "========================================================================================================================\n", "Rename workflow step\n", "\n", "--- .github/workflows/default.yaml\n", "+++ .github/workflows/default.yaml\n", "@@ -124,16 +124,16 @@\n", "@#        working-directory: ${{ steps.docker_meta.outputs.tags }}`\n", "@        with:\n", "           file: ${{ env.VERSION }}/Dockerfile\n", "           build-args: |\n", "             VERSION=${{ env.VERSION }}\n", "           tags: ${{ env.IMAGE }} # Build cache tag in ghcr.io\n", "           push: ${{ github.event_name != 'pull_request' }} # Don't push for PRs\n", "           cache-to: type=inline # Write the cache metadata into the image configuration\n", "       -\n", "-        # Run tests\n", "-        name: <PERSON>\n", "+        #        name: Docker image info\n", "+Test\n", "         working-directory: ${{ env.VERSION }}\n", "         env:\n", "           SECRET_PLATFORMSH_CLI_TOKEN: ${{ secrets.SECRET_PLATFORMSH_CLI_TOKEN }}\n", "           SECRET_TERMINUS_TOKEN: ${{ secrets.SECRET_TERMINUS_TOKEN }}\n", "         run: make test\n", "       -\n", "         # Push final image to the registries\n", "\n", "\n", "========================================================================================================================\n", "Add test and caching steps\n", "\n", "--- .github/workflows/default.yaml\n", "+++ .github/workflows/default.yaml\n", "@@ -125,14 +125,32 @@\n", "@#        working-directory: ${{ steps.docker_meta.outputs.tags }}`\n", "@        with:\n", "           build-args: |\n", "             VERSION=${{ env.VERSION }}\n", "           tags: ${{ env.IMAGE }} # Build cache tag in ghcr.io\n", "           push: ${{ github.event_name != 'pull_request' }} # Don't push for PRs\n", "           cache-to: type=inline # Write the cache metadata into the image configuration\n", "       -\n", "         # Run tests\n", "+        name:         run: |\n", "+          set -xeuo pipefail\n", "+          docker image ls | grep \"${{ env.IMAGE }}\"\n", "+          docker image inspect \"${{ env.IMAGE }}:php${{ matrix.version }}-build\"\n", "+      -\n", "+        # Cache image layers in the registry\n", "+        name: Build and cache (ghcr.io)\n", "+        uses: docker/build-push-action@v2\n", "+        with:\n", "+          context: ${{ env.VERSION }}\n", "+          file: ${{ env.VERSION }}/Dockerfile\n", "+          build-args: |\n", "+            VERSION=${{ env.VERSION }}\n", "+          tags: ${{ env.IMAGE }} # Build cache tag in ghcr.io\n", "+          push: ${{ github.event_name != 'pull_request' }} # Don't push for PRs\n", "+          cache-to: type=inline # Write the cache metadata into the image configuration\n", "+      -\n", "+        # Run tests\n", "         name: <PERSON>\n", "         working-directory: ${{ env.VERSION }}\n", "         env:\n", "           SECRET_PLATFORMSH_CLI_TOKEN: ${{ secrets.SECRET_PLATFORMSH_CLI_TOKEN }}\n", "           SECRET_TERMINUS_TOKEN: ${{ secrets.SECRET_TERMINUS_TOKEN }}\n", "         run: make test\n", "       -\n", "\n", "\n", "========================================================================================================================\n", "Use docker-reported architecture\n", "\n", "--- 7.4/tests/essential-binaries.sh\n", "+++ 7.4/tests/essential-binaries.sh\n", "@@ -50,17 +50,19 @@\n", " ping\n", " psql\n", " pv\n", " rsync\n", " sudo\n", " unzip\n", " wget\n", " yq\n", " zip'\n", " \n", "-case \"$(uname -m)\" in\n", "+# Use the docker reported architecture and not the hosts (uname -m).\n", "+# docker arch may not be the same as hosts's arch (e.g., when using a remote docker instance).\n", "+case \"$(docker info -f '{{ .Architecture }}')\" in\n", " \tx86_64) echo \"${binaries_amd64}\" ;;\n", " \tamd64) echo \"${binaries_amd64}\" ;;\n", " \taarch64) echo \"${binaries_arm64}\" ;;\n", " \tarm64) echo \"${binaries_arm64}\" ;;\n", " \t* ) false;;\n", " esac\n", "\n", "\n", "========================================================================================================================\n", "Use docker-reported architecture\n", "\n", "--- 7.3/tests/essential-binaries.sh\n", "+++ 7.3/tests/essential-binaries.sh\n", "@@ -53,14 +53,16 @@\n", " rsync\n", " sudo\n", " unzip\n", " wget\n", " yq\n", " zip'\n", " \n", "-case \"$(uname -m)\" in\n", "+# Use the docker reported architecture and not the hosts (uname -m).\n", "+# docker arch may not be the same as hosts's arch (e.g., when using a remote docker instance).\n", "+case \"$(docker info -f '{{ .Architecture }}')\" in\n", " \tx86_64) echo \"${binaries_amd64}\" ;;\n", " \tamd64) echo \"${binaries_amd64}\" ;;\n", " \taarch64) echo \"${binaries_arm64}\" ;;\n", " \tarm64) echo \"${binaries_arm64}\" ;;\n", " \t* ) false;;\n", " esac\n", "\n", "\n", "========================================================================================================================\n", "Use docker-reported architecture\n", "\n", "--- 7.3/tests/php-modules.sh\n", "+++ 7.3/tests/php-modules.sh\n", "@@ -132,16 +132,18 @@\n", " Zend OPcache\n", " zip\n", " zlib\n", " \n", " [<PERSON><PERSON>]\n", " Zend OPcache\n", " blackfire\n", " '\n", " \n", "+# Use the docker reported architecture and not the hosts (uname -m).\n", "+# docker arch may not be the same as hosts's arch (e.g., when using a remote docker instance).\n", " case \"$(docker info -f '{{ .Architecture }}')\" in\n", " \tx86_64) echo \"${php_modules_amd64}\" ;;\n", " \tamd64) echo \"${php_modules_amd64}\" ;;\n", " \taarch64) echo \"${php_modules_arm64}\" ;;\n", " \tarm64) echo \"${php_modules_arm64}\" ;;\n", " \t* ) false;;\n", " esac\n", "\n", "\n", "========================================================================================================================\n", "Update DOCKSAL_VERSION and job name\n", "\n", "--- .github/workflows/default.yaml\n", "+++ .github/workflows/default.yaml\n", "@@ -14,20 +14,21 @@\n", " defaults:\n", "   run:\n", "     shell: bash\n", " \n", " env:\n", "   IMAGE: docksal/cli\n", "   UPSTREAM_IMAGE: debian\n", "   LATEST_VERSION: 7.3\n", "-  DOCKSAL_\n", "+  DOCKSAL_VERSION: develop\n", "+\n", " jobs:\n", "   build:\n", "-    name: Build: ${{ matrix.version }}\n", "+    name: \"Build: ${{ matrix.version }}/${{ matrix.arch }}\"\n", "     runs-on: ubuntu-20.04\n", " \n", "     strategy:\n", "       fail-fast: false # Don't cancel other jobs if one fails\n", "       matrix:\n", "         include:\n", "           - runs-on: [ubuntu-20.04]\n", "             version: 7.3\n", "\n", "\n", "========================================================================================================================\n", "Simplify matrix configuration\n", "\n", "--- .github/workflows/default.yaml\n", "+++ .github/workflows/default.yaml\n", "@@ -24,24 +24,27 @@\n", "@jobs:\n", "   build:\n", "     name: Build: ${{ matrix.version }}\n", "     runs-on: ubuntu-20.04\n", " \n", "     strategy:\n", "       fail-fast: false # Don't cancel other jobs if one fails\n", "       matrix:\n", "         include:\n", "-          - runs-on: [ubuntu-20.04]\n", "-            version: 7.3\n", "-          - runs-on: [ubuntu-20.04]\n", "-            version: 7.4\n", "-          - runs-on: [ubuntu-20.04,ARM64]\n", "+          -\n", "+            platform: linux/amd64\n", "+            arch: amd64\n", "             version: 7.3\n", "           -\n", "+            platform: linux/amd64\n", "+            arch: amd64\n", "+            version: 7.4\n", "+          -\n", "             platform: linux/arm64\n", "+            arch: arm64\n", "             version: 7.3\n", "           -\n", "             platform: linux/arm64\n", "             arch: arm64\n", "             version: 7.4\n", " \n", "     env:\n", "       ARCH: ${{ matrix.arch }}\n", "\n", "\n", "========================================================================================================================\n", "Remove unnecessary installation steps\n", "\n", "--- .github/workflows/default.yaml\n", "+++ .github/workflows/default.yaml\n", "@@ -42,31 +42,18 @@\n", "@jobs:\n", "@  build:\n", "@    strategy:\n", "             platform: linux/arm64\n", "             arch: arm64\n", "             version: 7.4\n", " \n", "     env:\n", "       ARCH: ${{ matrix.arch }}\n", "       VERSION_PREFIX: php\n", "       VERSION: ${{ matrix.version }}\n", "-      DOCKSAL_VERSION: develop\n", " \n", "     steps:\n", "-      -\n", "-        name: Install prerequisites for tests\n", "-        run: |\n", "-          set -xeuo pipefail\n", "-          sudo apt-get -qq update\n", "-          # Install cgi-fcgi binary used in tests\n", "-          sudo apt-get -y --no-install-recommends install libfcgi-bin\n", "-          # <PERSON><PERSON><PERSON> bats for tests\n", "-          git clone https://github.com/bats-core/bats-core.git\n", "-          cd bats-core\n", "-          sudo ./install.sh /usr/local\n", "-          bats -v\n", "       -\n", "         name: <PERSON>out\n", "         uses: actions/checkout@v2\n", " #      -\n", "         name: Environment variables\n", "         run: |\n", "           # Export variables for further steps\n", "           echo \"GIT_SHA7=${GITHUB_SHA:0:7}\" >> $GITHUB_ENV\n", "\n", "\n", "========================================================================================================================\n", "Uncomment environment variables step\n", "\n", "--- .github/workflows/default.yaml\n", "+++ .github/workflows/default.yaml\n", "@@ -60,17 +60,17 @@\n", "@jobs:\n", "@  build:\n", "@    steps:\n", "           # Install bats for tests\n", "           git clone https://github.com/bats-core/bats-core.git\n", "           cd bats-core\n", "           sudo ./install.sh /usr/local\n", "           bats -v\n", "       -\n", "         name: <PERSON>out\n", "         uses: actions/checkout@v2\n", "-#      -\n", "+      -\n", "         name: Environment variables\n", "         run: |\n", "           # Export variables for further steps\n", "           echo \"GIT_SHA7=${GITHUB_SHA:0:7}\" >> $GITHUB_ENV\n", "           echo \"BUILD_IMAGE_TAG=${IMAGE}:${VERSION_PREFIX}${VERSION}-build\" >> ${GITHUB_ENV}\n", "       -\n", "         # Switch docker context to a remote arm64 host\n", "         name: Switch to arm64 builder host\n", "\n", "\n", "========================================================================================================================\n", "Configure arm64 builder host\n", "\n", "--- .github/workflows/default.yaml\n", "+++ .github/workflows/default.yaml\n", "@@ -72,17 +72,21 @@\n", "@#      -\n", "@        run: |\n", "           echo \"GIT_SHA7=${GITHUB_SHA:0:7}\" >> $GITHUB_ENV\n", "           echo \"BUILD_IMAGE_TAG=${IMAGE}:${VERSION_PREFIX}${VERSION}-build\" >> ${GITHUB_ENV}\n", "       -\n", "         # Switch docker context to a remote arm64 host\n", "         name: Switch to arm64 builder host\n", "         if: ${{ env.ARCH == 'arm64' }}\n", "         uses: arwynfr/actions-docker-context@98fc92878d0b856c1112c79b8d0f45353206e186\n", "         with:\n", "-          docker/setup-buildx-action@v1\n", "+          docker_host: \"ssh://ubuntu@${{ secrets.ARM64_HOST }}\"\n", "+          context_name: arm64-host\n", "+          ssh_key: \"${{ secrets.ARM64_HOST_SSH_KEY }}\"\n", "+          ssh_cert: \"${{ secrets.ARM64_HOST_SSH_CERT }}\"\n", "+          use_context: true\n", "       -\n", "         name: <PERSON>\n", "         run: |\n", "           docker version\n", "           docker info\n", "       -\n", "         name: <PERSON><PERSON> to <PERSON><PERSON>\n", "         uses: docker/login-action@v1\n", "\n", "\n", "========================================================================================================================\n", "Replace login step with build and cache image\n", "\n", "--- .github/workflows/default.yaml\n", "+++ .github/workflows/default.yaml\n", "@@ -85,17 +85,144 @@\n", "@#      -\n", "@        run: |\n", "           docker info\n", "       -\n", "         name: <PERSON><PERSON> to <PERSON><PERSON>\n", "         uses: docker/login-action@v1\n", "         with:\n", "           username: ${{ secrets.DOCKERHUB_USERNAME }}\n", "           password: ${{ secrets.DOCKERHUB_TOKEN }}\n", "       -\n", "-        name: <PERSON><PERSON> to GitHub Container Registry\n", "+        # Build and cache image in the registry\n", "+        name: Build image\n", "+        uses: docker/build-push-action@v2\n", "+        with:\n", "+          context: ${{ env.VERSION }}\n", "+          file: ${{ env.VERSION }}/Dockerfile\n", "+          build-args: VERSION=${{ env.VERSION }}\n", "+          # Push intermediate arch-specific build tag to repo\n", "+          tags: docker.io/${{ env.BUILD_IMAGE_TAG }}-${{ env.GIT_SHA7 }}-${{ env.ARCH }}\n", "+          push: ${{ github.event_name != 'pull_request' }} # Don't push for PRs\n", "+          # BUILD_IMAGE_TAG - persistent multi-arch tag, updated at the end of the build (success or failure)\n", "+          cache-from: type=registry,ref=docker.io/${{ env.BUILD_IMAGE_TAG }}\n", "+          cache-to: type=inline # Write the cache metadata into the image configuration\n", "+\n", "+  test:\n", "+    name: \"Test: ${{ matrix.version }}/${{ matrix.arch }}\"\n", "+    runs-on: ubuntu-20.04\n", "+    needs: build\n", "+    outputs:\n", "+      status: ${{ steps.tests.outputs.status }} # Tests status (used by downstream jobs)\n", "+\n", "+    strategy:\n", "+      fail-fast: false # Don't cancel other jobs if one fails\n", "+      matrix:\n", "+        include:\n", "+          -\n", "+            platform: linux/amd64\n", "+            arch: amd64\n", "+            version: 7.3\n", "+          -\n", "+            platform: linux/amd64\n", "+            arch: amd64\n", "+            version: 7.4\n", "+          -\n", "+            platform: linux/arm64\n", "+            arch: arm64\n", "+            version: 7.3\n", "+          -\n", "+            platform: linux/arm64\n", "+            arch: arm64\n", "+            version: 7.4\n", "+\n", "+    env:\n", "+      ARCH: ${{ matrix.arch }}\n", "+      VERSION_PREFIX: php\n", "+      VERSION: ${{ matrix.version }}\n", "+\n", "+    steps:\n", "+      -\n", "+        name: <PERSON><PERSON>\n", "+        uses: mig4/setup-bats@v1\n", "+        with:\n", "+          bats-version: 1.3.0\n", "+      -\n", "+        name: Checkout\n", "+        uses: actions/checkout@v2\n", "+      -\n", "+        name: Environment variables\n", "+        run: |\n", "+          # Export variables for further steps\n", "+          echo \"GIT_SHA7=${GITHUB_SHA:0:7}\" >> $GITHUB_ENV\n", "+          echo \"BUILD_IMAGE_TAG=${IMAGE}:${VERSION_PREFIX}${VERSION}-build\" >> ${GITHUB_ENV}\n", "+      -\n", "+        # Switch docker context to a remote arm64 host\n", "+        name: Switch to arm64 builder host\n", "+        if: ${{ env.ARCH == 'arm64' }}\n", "+        uses: arwynfr/actions-docker-context@98fc92878d0b856c1112c79b8d0f45353206e186\n", "+        with:\n", "+          docker_host: \"ssh://ubuntu@${{ secrets.ARM64_HOST }}\"\n", "+          context_name: arm64-host\n", "+          ssh_key: \"${{ secrets.ARM64_HOST_SSH_KEY }}\"\n", "+          ssh_cert: \"${{ secrets.ARM64_HOST_SSH_CERT }}\"\n", "+          use_context: true\n", "+      -\n", "+        name: <PERSON>\n", "+        run: |\n", "+          docker version\n", "+          docker info\n", "+      -\n", "+        # Run tests\n", "+        name: Test\n", "+        id: tests\n", "+        working-directory: ${{ env.VERSION }}\n", "+        env:\n", "+          BUILD_IMAGE_TAG: docker.io/${{ env.BUILD_IMAGE_TAG }}-${{ env.GIT_SHA7 }}-${{ env.ARCH }}\n", "+          SECRET_PLATFORMSH_CLI_TOKEN: ${{ secrets.SECRET_PLATFORMSH_CLI_TOKEN }}\n", "+          SECRET_TERMINUS_TOKEN: ${{ secrets.SECRET_TERMINUS_TOKEN }}\n", "+        # Run tests and set output status (used by downstream jobs)\n", "+        run: make test && echo \"::set-output name=status::pass\" || echo \"::set-output name=status::fail\"\n", "+      -\n", "+        # Print image info\n", "+        name: Docker image info\n", "+        env:\n", "+          BUILD_IMAGE_TAG: docker.io/${{ env.BUILD_IMAGE_TAG }}-${{ env.GIT_SHA7 }}-${{ env.ARCH }}\n", "+        run: |\n", "+          set -xeuo pipefail\n", "+          docker image ls | grep \"${{ env.IMAGE }}\"\n", "+          docker image inspect \"${{ env.BUILD_IMAGE_TAG }}\"\n", "+\n", "+  push:\n", "+    name: \"Push: ${{ matrix.version }}/multi\"\n", "+    runs-on: ubuntu-20.04\n", "+    # Wait for test to either succeed or fail\n", "+    needs: test\n", "+    if: always()\n", "+\n", "+    strategy:\n", "+      matrix:\n", "+        version:\n", "+          - 7.3\n", "+          - 7.4\n", "+    env:\n", "+      VERSION_PREFIX: php\n", "+      VERSION: ${{ matrix.version }}\n", "+\n", "+    steps:\n", "+      -\n", "+        name: Checkout\n", "+        uses: actions/checkout@v2\n", "+      -\n", "+        name: Environment variables\n", "+        run: |\n", "+          # Export variables for further steps\n", "+          echo \"GIT_SHA7=${GITHUB_SHA:0:7}\" >> $GITHUB_ENV\n", "+          echo \"BUILD_IMAGE_TAG=${IMAGE}:${VERSION_PREFIX}${VERSION}-build\" >> ${GITHUB_ENV}\n", "+      -\n", "+        # <PERSON>gin to <PERSON><PERSON>\n", "+        name: <PERSON><PERSON> to <PERSON><PERSON>\n", "         uses: docker/login-action@v1\n", "         with:\n", "           username: ${{ secrets.DOCKERHUB_USERNAME }}\n", "           password: ${{ secrets.DOCKERHUB_TOKEN }}\n", "       -\n", "         # Generate image tags (edge, stable, release)\n", "         name: Docker image tags\n", "         id: docker_tags\n", "\n", "\n", "========================================================================================================================\n", "Remove unused GitHub Container Registry login\n", "\n", "--- .github/workflows/default.yaml\n", "+++ .github/workflows/default.yaml\n", "@@ -1,8 +1,80 @@\n", "+name: Docker Build and Push\n", "+\n", "+on:\n", "+  schedule:\n", "+    - cron: '0 10 * * 0' # everyday sunday at 10am\n", "+  push:\n", "+    branches:\n", "+      - master\n", "+      - develop\n", "+      - feature/*\n", "+    tags:\n", "+      - 'v*.*.*'\n", "+\n", "+defaults:\n", "+  run:\n", "+    shell: bash\n", "+\n", "+env:\n", "+  IMAGE: docksal/cli\n", "+  UPSTREAM_IMAGE: debian\n", "+  LATEST_VERSION: 7.3\n", "+  DOCKSAL_VERSION: develop\n", "+\n", "+jobs:\n", "+  build:\n", "+    name: Build on ${{ matrix.runs-on }} ${{ matrix.version }}\n", "+    runs-on: ${{ matrix.runs-on }}\n", "+\n", "+    strategy:\n", "+      fail-fast: false # Don't cancel other jobs if one fails\n", "+      matrix:\n", "+        include:\n", "+          - runs-on: [ubuntu-20.04]\n", "+            version: 7.3\n", "+          - runs-on: [ubuntu-20.04]\n", "+            version: 7.3\n", "+          -\n", "+            platform: linux/amd64\n", "+            version: 7.3\n", "+          -\n", "+            platform: linux/arm64\n", "+            arch: arm64\n", "+            version: 7.4\n", "+\n", "+    env:\n", "+      IMAGE: docksal/cli\n", "+      UPSTREAM_IMAGE: debian\n", "+      LATEST_VERSION: 7.3\n", "+      VERSION: ${{ matrix.version }}\n", "+      DOCKSAL_VERSION: develop\n", "+\n", "+    steps:\n", "+      -\n", "+        name: Checkout\n", "+        uses: actions/checkout@v2\n", "+      -\n", "+#        name: Set up QEMU\n", "+#        uses: docker/setup-qemu-action@v1\n", "+      -\n", "+        # Switch docker context to a remote arm64 host\n", "+        name: Switch to arm64 buildx\n", "+        uses: docker_host: \"ssh://ubuntu@${{ secrets.ARM64_HOST }}\"\n", "+          context_name: arm64-host\n", "+          ssh_key: \"${{ secrets.ARM64_HOST_SSH_KEY }}\"\n", "+          ssh_cert: \"${{ secrets.ARM64_HOST_SSH_CERT }}\"\n", "+          use_context: true\n", "+      -\n", "+        name: <PERSON>\n", "+        run: |\n", "+          docker version\n", "+          docker info\n", "+      -\n", " name: Docker Build and Push\n", " \n", " on:\n", "   schedule:\n", "     - cron: '0 10 * * 0' # everyday sunday at 10am\n", "   push:\n", "     branches:\n", "       - master\n", "@@ -71,52 +143,14 @@\n", "@#        uses: docker/setup-qemu-action@v1\n", "@        run: |\n", "           docker info\n", "       -\n", "         name: <PERSON><PERSON> to <PERSON><PERSON>\n", "         uses: docker/login-action@v1\n", "         with:\n", "           username: ${{ secrets.DOCKERHUB_USERNAME }}\n", "           password: ${{ secrets.DOCKERHUB_TOKEN }}\n", "       -\n", "-        name: <PERSON><PERSON> to GitHub Container Registry\n", "-        uses: docker/login-action@v1\n", "-        with:\n", "-          registry: ghcr.io\n", "-          username: ${{ secrets.GHCR_USERNAME }}\n", "-          password: ${{ secrets.GHCR_TOKEN }}\n", "-      -\n", "-        # Generate image tags (edge, stable, release)\n", "-        name: <PERSON>er image tags\n", "-        id: docker_tags\n", "-        if: ${{ needs.tests.outputs.status == 'pass' }\n", "-        run: .github/scripts/docker-tags\n", "-      -\n", "-        # Build for local use\n", "-        name: Push multi-arch images\n", "-        id: docker_build\n", "-        uses: docker/build-push-action@v2\n", "-        env:\n", "-          TAGS: |\n", "-            ${{ env.BUILD_IMAGE_TAG }}\n", "-            ${{ steps.docker_tags.outputs.tags }}\n", "-          DOCKERHUB_USERNAME: ${{ secrets.DOCKERHUB_USERNAME }} # Needed for docker-tag-delete.sh\n", "-          DOCKERHUB_PASSWORD: ${{ secrets.DOCKERHUB_PASSWORD }} # Needed for docker-tag-delete.sh\n", "-        run: |\n", "-          set -xeuo pipefail\n", "-          IFS=\"${IFS},\" # Also split strings by comma (in case list of tag is comma-separated)\n", "-          for tag in ${TAGS}; do\n", "-            if [[ \"${tag}\" == \"\" ]]; then continue; fi\n", "-            docker manifest create --amend ${tag} \\\n", "-              ${{ env.BUILD_IMAGE_TAG }}-${{ env.GIT_SHA7 }}-amd64 \\\n", "-              ${{ env.BUILD_IMAGE_TAG }}-${{ env.GIT_SHA7 }}-arm64\n", "-            docker manifest inspect ${tag}\n", "-            docker manifest push_cache\n", "-        uses: docker/build-push-action@v2\n", "-        env:\n", "-          IMAGE_CACHE: ghcr.io/${{ env.IMAGE }}:php${{ env.VERSION }}-build\n", "-        with:\n", "-          context: ${{ env.VERSION }}\n", "-          file: ${{ env.VERSION }}/Dockerfile\n", "+ERSION }}/Dockerfile\n", "           build-args: |\n", "             VERSION=${{ env.VERSION }}\n", "           tags: ${{ env.IMAGE_CACHE }} # Build cache tag in ghcr.io\n", "           push: ${{ github/scripts/docker-tag-delete.sh ${{ env.BUILD_IMAGE_TAG }}-${{ env.GIT_SHA7 }}-amd64\n", "           .github/scripts/docker-tag-delete.sh ${{ env.BUILD_IMAGE_TAG }}-${{ env.GIT_SHA7 }}-arm64\n", "\n", "\n", "========================================================================================================================\n", "Add build, test, and push jobs\n", "\n", "--- .github/workflows/default.yaml\n", "+++ .github/workflows/default.yaml\n", "@@ -109,14 +109,140 @@\n", "@#        uses: docker/setup-qemu-action@v1\n", "@        run: |\n", "@            docker manifest create --amend ${tag} \\\n", "               ${{ env.BUILD_IMAGE_TAG }}-${{ env.GIT_SHA7 }}-arm64\n", "             docker manifest inspect ${tag}\n", "             docker manifest push_cache\n", "         uses: docker/build-push-action@v2\n", "         env:\n", "           IMAGE_CACHE: ghcr.io/${{ env.IMAGE }}:php${{ env.VERSION }}-build\n", "         with:\n", "           context: ${{ env.VERSION }}\n", "+          file: ${{ env.V        # Build and cache image in the registry\n", "+        name: Build image\n", "+        uses: docker/build-push-action@v2\n", "+        with:\n", "+          context: ${{ env.VERSION }}\n", "           file: ${{ env.VERSION }}/Dockerfile\n", "-          build-args: |\n", "-            VERSION=${{ env.VERSION }}\n", "+          build-args: VERSION=${{ env.VERSION }}\n", "+          # Push intermediate arch-specific build tag to repo\n", "+          tags: docker.io/${{ env.BUILD_IMAGE_TAG }}-${{ env.GIT_SHA7 }}-${{ env.ARCH }}\n", "+          push: ${{ github.event_name != 'pull_request' }} # Don't push for PRs\n", "+          # BUILD_IMAGE_TAG - persistent multi-arch tag, updated at the end of the build (success or failure)\n", "+          cache-from: type=registry,ref=docker.io/${{ env.BUILD_IMAGE_TAG }}\n", "+          cache-to: type=inline # Write the cache metadata into the image configuration\n", "+\n", "+  test:\n", "+    name: \"Test: ${{ matrix.version }}/${{ matrix.arch }}\"\n", "+    runs-on: ubuntu-20.04\n", "+    needs: build\n", "+    outputs:\n", "+      status: ${{ steps.tests.outputs.status }} # Tests status (used by downstream jobs)\n", "+\n", "+    strategy:\n", "+      fail-fast: false # Don't cancel other jobs if one fails\n", "+      matrix:\n", "+        include:\n", "+          -\n", "+            platform: linux/amd64\n", "+            arch: amd64\n", "+            version: 7.3\n", "+          -\n", "+            platform: linux/amd64\n", "+            arch: amd64\n", "+            version: 7.4\n", "+          -\n", "+            platform: linux/arm64\n", "+            arch: arm64\n", "+            version: 7.3\n", "+          -\n", "+            platform: linux/arm64\n", "+            arch: arm64\n", "+            version: 7.4\n", "+\n", "+    env:\n", "+      ARCH: ${{ matrix.arch }}\n", "+      VERSION_PREFIX: php\n", "+      VERSION: ${{ matrix.version }}\n", "+\n", "+    steps:\n", "+      -\n", "+        name: <PERSON><PERSON>\n", "+        uses: mig4/setup-bats@v1\n", "+        with:\n", "+          bats-version: 1.3.0\n", "+      -\n", "+        name: Checkout\n", "+        uses: actions/checkout@v2\n", "+      -\n", "+        name: Environment variables\n", "+        run: |\n", "+          # Export variables for further steps\n", "+          echo \"GIT_SHA7=${GITHUB_SHA:0:7}\" >> $GITHUB_ENV\n", "+          echo \"BUILD_IMAGE_TAG=${IMAGE}:${VERSION_PREFIX}${VERSION}-build\" >> ${GITHUB_ENV}\n", "+      -\n", "+        # Switch docker context to a remote arm64 host\n", "+        name: Switch to arm64 builder host\n", "+        if: ${{ env.ARCH == 'arm64' }}\n", "+        uses: arwynfr/actions-docker-context@98fc92878d0b856c1112c79b8d0f45353206e186\n", "+        with:\n", "+          docker_host: \"ssh://ubuntu@${{ secrets.ARM64_HOST }}\"\n", "+          context_name: arm64-host\n", "+          ssh_key: \"${{ secrets.ARM64_HOST_SSH_KEY }}\"\n", "+          ssh_cert: \"${{ secrets.ARM64_HOST_SSH_CERT }}\"\n", "+          use_context: true\n", "+      -\n", "+        name: <PERSON>\n", "+        run: |\n", "+          docker version\n", "+          docker info\n", "+      -\n", "+        # Run tests\n", "+        name: Test\n", "+        id: tests\n", "+        working-directory: ${{ env.VERSION }}\n", "+        env:\n", "+          BUILD_IMAGE_TAG: docker.io/${{ env.BUILD_IMAGE_TAG }}-${{ env.GIT_SHA7 }}-${{ env.ARCH }}\n", "+          SECRET_PLATFORMSH_CLI_TOKEN: ${{ secrets.SECRET_PLATFORMSH_CLI_TOKEN }}\n", "+          SECRET_TERMINUS_TOKEN: ${{ secrets.SECRET_TERMINUS_TOKEN }}\n", "+        # Run tests and set output status (used by downstream jobs)\n", "+        run: make test && echo \"::set-output name=status::pass\" || echo \"::set-output name=status::fail\"\n", "+      -\n", "+        # Print image info\n", "+        name: Docker image info\n", "+        env:\n", "+          BUILD_IMAGE_TAG: docker.io/${{ env.BUILD_IMAGE_TAG }}-${{ env.GIT_SHA7 }}-${{ env.ARCH }}\n", "+        run: |\n", "+          set -xeuo pipefail\n", "+          docker image ls | grep \"${{ env.IMAGE }}\"\n", "+          docker image inspect \"${{ env.BUILD_IMAGE_TAG }}\"\n", "+\n", "+  push:\n", "+    name: \"Push: ${{ matrix.version }}/multi\"\n", "+    runs-on: ubuntu-20.04\n", "+    # Wait for test to either succeed or fail\n", "+    needs: test\n", "+    if: always()\n", "+\n", "+    strategy:\n", "+      matrix:\n", "+        version:\n", "+          - 7.3\n", "+          - 7.4\n", "+    env:\n", "+      VERSION_PREFIX: php\n", "+      VERSION: ${{ matrix.version }}\n", "+\n", "+    steps:\n", "+      -\n", "+        name: Checkout\n", "+        uses: actions/checkout@v2\n", "+      -\n", "+        name: Environment variables\n", "+        run: |\n", "+          # Export variables for further steps\n", "+          echo \"GIT_SHA7=${GITHUB_SHA:0:7}\" >> $GITHUB_ENV\n", "+          echo \"BUILD_IMAGE_TAG=${IMAGE}:${VERSION_PREFIX}${VERSION}-build\" >> ${GITHUB_ENV}\n", "+      -\n", "+        # <PERSON>gin to <PERSON><PERSON>\n", "+        name: <PERSON><PERSON> to <PERSON><PERSON>\n", "+        VERSION=${{ env.VERSION }}\n", "           tags: ${{ env.IMAGE_CACHE }} # Build cache tag in ghcr.io\n", "           push: ${{ github/scripts/docker-tag-delete.sh ${{ env.BUILD_IMAGE_TAG }}-${{ env.GIT_SHA7 }}-amd64\n", "           .github/scripts/docker-tag-delete.sh ${{ env.BUILD_IMAGE_TAG }}-${{ env.GIT_SHA7 }}-arm64\n", "\n", "\n", "========================================================================================================================\n", "Add Docker login and image tags steps\n", "\n", "--- .github/workflows/default.yaml\n", "+++ .github/workflows/default.yaml\n", "@@ -111,12 +111,51 @@\n", "@#        uses: docker/setup-qemu-action@v1\n", "@        run: |\n", "             docker manifest push_cache\n", "         uses: docker/build-push-action@v2\n", "         env:\n", "           IMAGE_CACHE: ghcr.io/${{ env.IMAGE }}:php${{ env.VERSION }}-build\n", "         with:\n", "           context: ${{ env.VERSION }}\n", "           file: ${{ env.VERSION }}/Dockerfile\n", "           build-args: |\n", "+            uses: docker/login-action@v1\n", "+        with:\n", "+          registry: ghcr.io\n", "+          username: ${{ secrets.GHCR_USERNAME }}\n", "+          password: ${{ secrets.GHCR_TOKEN }}\n", "+      -\n", "+        # Generate image tags (edge, stable, release)\n", "+        name: Docker image tags\n", "+        id: docker_tags\n", "+        if: ${{ needs.tests.outputs.status == 'pass' }\n", "+        run: .github/scripts/docker-tags\n", "+      -\n", "+        # Build for local use\n", "+        name: Push multi-arch images\n", "+        id: docker_build\n", "+        uses: docker/build-push-action@v2\n", "+        env:\n", "+          TAGS: |\n", "+            ${{ env.BUILD_IMAGE_TAG }}\n", "+            ${{ steps.docker_tags.outputs.tags }}\n", "+          DOCKERHUB_USERNAME: ${{ secrets.DOCKERHUB_USERNAME }} # Needed for docker-tag-delete.sh\n", "+          DOCKERHUB_PASSWORD: ${{ secrets.DOCKERHUB_PASSWORD }} # Needed for docker-tag-delete.sh\n", "+        run: |\n", "+          set -xeuo pipefail\n", "+          IFS=\"${IFS},\" # Also split strings by comma (in case list of tag is comma-separated)\n", "+          for tag in ${TAGS}; do\n", "+            if [[ \"${tag}\" == \"\" ]]; then continue; fi\n", "+            docker manifest create --amend ${tag} \\\n", "+              ${{ env.BUILD_IMAGE_TAG }}-${{ env.GIT_SHA7 }}-amd64 \\\n", "+              ${{ env.BUILD_IMAGE_TAG }}-${{ env.GIT_SHA7 }}-arm64\n", "+            docker manifest inspect ${tag}\n", "+            docker manifest push_cache\n", "+        uses: docker/build-push-action@v2\n", "+        env:\n", "+          IMAGE_CACHE: ghcr.io/${{ env.IMAGE }}:php${{ env.VERSION }}-build\n", "+        with:\n", "+          context: ${{ env.VERSION }}\n", "+          file: ${{ env.VERSION }}/Dockerfile\n", "+          build-args: |\n", "             VERSION=${{ env.VERSION }}\n", "           tags: ${{ env.IMAGE_CACHE }} # Build cache tag in ghcr.io\n", "           push: ${{ github/scripts/docker-tag-delete.sh ${{ env.BUILD_IMAGE_TAG }}-${{ env.GIT_SHA7 }}-amd64\n", "           .github/scripts/docker-tag-delete.sh ${{ env.BUILD_IMAGE_TAG }}-${{ env.GIT_SHA7 }}-arm64\n", "\n", "\n", "========================================================================================================================\n", "Remove commented code\n", "\n", "--- .github/scripts/docker-tags.sh\n", "+++ .github/scripts/docker-tags.sh\n", "@@ -1,21 +1,18 @@\n", " #!/usr/bin/env bash\n", " \n", " # Generates docker images tags for the docker/build-push-action@v2 action depending on the branch/tag.\n", " # Image tag format:\n", " #   develop     => image:[version_prefix][version-]edge[-version_suffix]\n", " #   master      => image:[version_prefix][version][-][version_suffix]\n", " #   semver tag  => image:[version_prefix][version-]major.minor[-version_suffix]\n", " \n", "-# Example config from build environment\n", "-# VERSION_PREFIX = php\n", "-# VERSION = 7.4\n", "-Declare expected variables\n", "+# Declare expected variables\n", " IMAGE=${IMAGE} # docksal/cli\n", " VERSION_PREFIX=${VERSION_PREFIX} # php\n", " VERSION=${VERSION} # 7.4\n", " VERSION_SUFFIX=${VERSION_SUFFIX} # ide\n", " REGISTRY=\"${REGISTRY}\" # ghcr.io\n", " GITHUB_REF=${GITHUB_REF} # refs/heads/develop, refs/heads/master, refs/tags/v1.0.0\n", " registryArr+=(\"docker.io\") # Docker Hub\n", " registryArr+=(\"ghcr.io\") # GitHub Container Registry\n", " \n", "\n", "\n", "========================================================================================================================\n", "Remove unused registry array\n", "\n", "--- .github/scripts/docker-tags.sh\n", "+++ .github/scripts/docker-tags.sh\n", "@@ -10,20 +10,18 @@\n", " # VERSION_PREFIX = php\n", " # VERSION = 7.4\n", " Declare expected variables\n", " IMAGE=${IMAGE} # docksal/cli\n", " VERSION_PREFIX=${VERSION_PREFIX} # php\n", " VERSION=${VERSION} # 7.4\n", " VERSION_SUFFIX=${VERSION_SUFFIX} # ide\n", " REGISTRY=\"${REGISTRY}\" # ghcr.io\n", " GITHUB_REF=${GITHUB_REF} # refs/heads/develop, refs/heads/master, refs/tags/v1.0.0\n", "-registryArr+=(\"docker.io\") # Docker Hub\n", "-registryArr+=(\"ghcr.io\") # GitHub Container Registry\n", " \n", " # Join arguments with hyphen (-) as a delimiter\n", " # Usage: join <arg1> [<argn>]\n", " join() {\n", " \tlocal IFS='-' # join delimiter\n", " \techo \"$*\"\n", " }\n", " \n", " # Image tags\n", "\n", "\n", "========================================================================================================================\n", "Extract and format registry prepending logic\n", "\n", "--- .github/scripts/docker-tags.sh\n", "+++ .github/scripts/docker-tags.sh\n", "@@ -18,18 +18,35 @@\n", " GITHUB_REF=${GITHUB_REF} # refs/heads/develop, refs/heads/master, refs/tags/v1.0.0\n", " registryArr+=(\"docker.io\") # Docker Hub\n", " registryArr+=(\"ghcr.io\") # GitHub Container Registry\n", " \n", " # Join arguments with hyphen (-) as a delimiter\n", " # Usage: join <arg1> [<argn>]\n", " join() {\n", " \tlocal IFS='-' # join delimiter\n", " \techo \"$*\"\n", "+}\n", "+\n", "+# Prints resulting image tags and sets output variable\n", "+set_output() {\n", "+\tlocal -n inputArr=${1}\n", "+\n", "+\tdeclare -a outputArr\n", "+\tfor imageTag in ${inputArr[@]}; do\n", "+\t\t# Prepend registry to imageTag if provided\n", "+\t\t[[ \"${REGISTRY}\" != \"\" ]] && imageTag=\"${REGISTRY}/${imageTag}\"\n", "+\t\toutputArr+=(\"${imageTag}\")\n", "+\tdone\n", "+\n", "+\t# Print with new lines for output in build logs\n", "+\t(IFS=$'\\n'; echo \"${outputArr[*]}\")\n", "+\t# Using newlines in output variables does not seem to work, so we'll use comas\n", "+\t(IFS=$','; echo \"::set-output name=tags::${outputArr[*]}\")\n", " }\n", " \n", " # Image tags\n", " declare -a imageTagArr\n", " \n", " # On every build => build / build-sha7\n", " # Latest build tag (used with cache-from)\n", " #imageTagArr+=(\"${IMAGE}:$(join ${VERSION_PREFIX}${VERSION} ${VERSION_SUFFIX} build)\")\n", " ## Specific build tag - SHA7 (first 7 characters of commit SHA)\n", "\n", "\n", "========================================================================================================================\n", "Remove commented code\n", "\n", "--- .github/scripts/docker-tags.sh\n", "+++ .github/scripts/docker-tags.sh\n", "@@ -23,20 +23,20 @@\n", " # Usage: join <arg1> [<argn>]\n", " join() {\n", " \tlocal IFS='-' # join delimiter\n", " \techo \"$*\"\n", " }\n", " \n", " # Image tags\n", " declare -a imageTagArr\n", " \n", "-# On every build => build / build-sha7\n", "-# Latest build tag (used with cache-from)\n", "+## On every build => build / build-sha7\n", "+## Latest build tag (used with cache-from)\n", " #imageTagArr+=(\"${IMAGE}:$(join ${VERSION_PREFIX}${VERSION} ${VERSION_SUFFIX} build)\")\n", " ## Specific build tag - SHA7 (first 7 characters of commit SHA)\n", " #imageTagArr+=(\"${IMAGE}:$(join ${VERSION_PREFIX}${VERSION} ${VERSION_SUFFIX} build ${GITHUB_SHA:0:7})\")\n", " \n", " # develop => version-edge\n", " if [[ \"${GITHUB_REF}\" == \"refs/heads/develop\" ]]; then\n", " \timageTagArr+=(\"${IMAGE}:$(join ${VERSION_PREFIX}${VERSION} edge ${VERSION_SUFFIX})\")\n", " fi\n", " \n", "\n", "\n", "========================================================================================================================\n", "Refactor Docker build and test workflow\n", "\n", "--- .github/workflows/default.yaml\n", "+++ .github/workflows/default.yaml\n", "@@ -179,100 +179,199 @@\n", "@jobs:\n", "@  test:\n", "@    steps:\n", "           docker info\n", "       -\n", "         # Run tests\n", "         name: <PERSON>\n", "         id: tests\n", "         working-directory: ${{ env.VERSION }}\n", "         env:\n", "           BUILD_IMAGE_TAG: docker.io/${{ env.BUILD_IMAGE_TAG }}-${{ env.GIT_SHA7 }}-${{ env.ARCH }}\n", "           SECRET_PLATFORMSH_CLI_TOKEN: ${{ secrets.SECRET_PLATFORMSH_CLI_TOKEN }}\n", "-          SECRET_TERMINUS_TOKEN: ${{ secrets.SECRET_TERMINUS_TOKEN }}\n", "-        run: |\n", "-          make test\n", "-          ([[ $? == 0 ]] && echo \"pass\" || echo \"fail\") | tee ${{ env.BUILD_IMAGE_TAG }}/test-results-${VERSION_PREFIX}${VERSION}-${ARCH}.txt\n", "-      # Store tests results as an artifact (used by downstream jobs)\n", "-      # Note: Cannot use \"::set-output name=var_name::var_value\" as var_name would need to be dynamic here.\n", "-      # Dynamic variable names cannot be used when mapping step outputs to job outputs.\n", "-      # Step outputs cannot be accessed directly from other jobs. Dead end.\n", "-      - name: Store test results\n", "-        uses: actions/upload-artifact@v2\n", "-        with:\n", "-          name: test-results\n", "-          path: ${{ github.workspace }}/test-results-*.txt\n", "-\n", "+name: Docker Build and Push\n", "+\n", "+on:\n", "+  schedule:\n", "+    - cron: '0 10 * * 0' # everyday sunday at 10am\n", "   push:\n", "-    name: \"Push: ${{ matrix.version }}/multi\"\n", "+    branches:\n", "+      - master\n", "+      - develop\n", "+      - feature/*\n", "+    tags:\n", "+      - 'v*.*.*'\n", "+\n", "+defaults:\n", "+  run:\n", "+    shell: bash\n", "+\n", "+env:\n", "+  IMAGE: docksal/cli\n", "+  UPSTREAM_IMAGE: debian\n", "+  LATEST_VERSION: '7.3'\n", "+  DOCKSAL_VERSION: develop\n", "+\n", "+jobs:\n", "+  build:\n", "+    name: \"Build: ${{ matrix.version }}/${{ matrix.arch }}\"\n", "     runs-on: ubuntu-20.04\n", " \n", "-    # Wait for test to either succeed or fail\n", "-    needs: test\n", "-    if: always()\n", "-\n", "     strategy:\n", "+      fail-fast: false # Don't cancel other jobs if one fails\n", "       matrix:\n", "-        version:\n", "-          - '7.3'\n", "-          - '7.4'\n", "-          - '8.0'\n", "+        include:\n", "+          -\n", "+            platform: linux/amd64\n", "+            arch: amd64\n", "+            version: '7.3'\n", "+          -\n", "+            platform: linux/amd64\n", "+            arch: amd64\n", "+            version: '7.4'\n", "+          -\n", "+            platform: linux/amd64\n", "+            arch: amd64\n", "+            version: '8.0'\n", "+          -\n", "+            platform: linux/arm64\n", "+            arch: arm64\n", "+            version: '7.3'\n", "+          -\n", "+            platform: linux/arm64\n", "+            arch: arm64\n", "+            version: '7.4'\n", "+          -\n", "+            platform: linux/arm64\n", "+            arch: arm64\n", "+            version: '8.0'\n", " \n", "     env:\n", "+      ARCH: ${{ matrix.arch }}\n", "       VERSION_PREFIX: php\n", "       VERSION: ${{ matrix.version }}\n", " \n", "     steps:\n", "       -\n", "         name: <PERSON>out\n", "         uses: actions/checkout@v2\n", "       -\n", "         name: Environment variables\n", "         run: |\n", "           # Export variables for further steps\n", "           echo \"GIT_SHA7=${GITHUB_SHA:0:7}\" >> $GITHUB_ENV\n", "           echo \"BUILD_IMAGE_TAG=${IMAGE}:${VERSION_PREFIX}${VERSION}-build\" >> ${GITHUB_ENV}\n", "       -\n", "-        # <PERSON><PERSON> to <PERSON><PERSON>\n", "+        # Switch docker context to a remote arm64 host\n", "+        name: Switch to arm64 builder host\n", "+        if: ${{ env.ARCH == 'arm64' }}\n", "+        uses: arwynfr/actions-docker-context@98fc92878d0b856c1112c79b8d0f45353206e186\n", "+        with:\n", "+          docker_host: \"ssh://ubuntu@${{ secrets.ARM64_HOST }}\"\n", "+          context_name: arm64-host\n", "+          ssh_key: \"${{ secrets.ARM64_HOST_SSH_KEY }}\"\n", "+          ssh_cert: \"${{ secrets.ARM64_HOST_SSH_CERT }}\"\n", "+          use_context: true\n", "+      -\n", "+        name: <PERSON>\n", "+        run: |\n", "+          docker version\n", "+          docker info\n", "+      -\n", "         name: <PERSON><PERSON> to <PERSON><PERSON>\n", "         uses: docker/login-action@v1\n", "         with:\n", "           username: ${{ secrets.DOCKERHUB_USERNAME }}\n", "           password: ${{ secrets.DOCKERHUB_TOKEN }}\n", "       -\n", "-        name: Retrieve test results\n", "-        uses: actions/download-artifact@v2\n", "-        with:\n", "-          name: test-results\n", "-      -\n", "-        # Generate persistent tags (edge, stable, release)\n", "-        name: <PERSON>er image tags\n", "-        id: docker_tags\n", "-        # Don't push broken builds to persistent tags (both amd64 and arm64 tests must pass)\n", "-        run: |\n", "-          amd64_tests=$(cat test-results-${VERSION_PREFIX}${VERSION}-amd64.txt)\n", "-          arm64_tests=$(cat test-results-${VERSION_PREFIX}${VERSION}-arm64.txt)\n", "-          if [[ \"${amd64_tests}\" == \"pass\" ]] && [[ \"${arm64_tests}\" == \"pass\" ]]; then\n", "-            .github/scripts/docker-tags.sh\n", "-          fi\n", "-      -\n", "-        # Create and push multi-arch image manifests\n", "-        name: Push multi-arch images\n", "+        # Build and cache image in the registry\n", "+        name: Build image\n", "+        uses: docker/build-push-action@v2\n", "+        with:\n", "+          context: ${{ env.VERSION }}\n", "+          file: ${{ env.VERSION }}/Dockerfile\n", "+          build-args: VERSION=${{ env.VERSION }}\n", "+          # Push intermediate arch-specific build tag to repo\n", "+          tags: docker.io/${{ env.BUILD_IMAGE_TAG }}-${{ env.GIT_SHA7 }}-${{ env.ARCH }}\n", "+          push: ${{ github.event_name != 'pull_request' }} # Don't push for PRs\n", "+          # BUILD_IMAGE_TAG - persistent multi-arch tag, updated at the end of the build (success or failure)\n", "+          cache-from: type=registry,ref=docker.io/${{ env.BUILD_IMAGE_TAG }}\n", "+          cache-to: type=inline # Write the cache metadata into the image configuration\n", "+\n", "+  test:\n", "+    name: \"Test: ${{ matrix.version }}/${{ matrix.arch }}\"\n", "+    runs-on: ubuntu-20.04\n", "+    needs: build\n", "+\n", "+    strategy:\n", "+      fail-fast: false # Don't cancel other jobs if one fails\n", "+      matrix:\n", "+        include:\n", "+          -\n", "+            platform: linux/amd64\n", "+            arch: amd64\n", "+            version: '7.3'\n", "+          -\n", "+            platform: linux/amd64\n", "+            arch: amd64\n", "+            version: '7.4'\n", "+          -\n", "+            platform: linux/amd64\n", "+            arch: amd64\n", "+            version: '8.0'\n", "+          -\n", "+            platform: linux/arm64\n", "+            arch: arm64\n", "+            version: '7.3'\n", "+          -\n", "+            platform: linux/arm64\n", "+            arch: arm64\n", "+            version: '7.4'\n", "+          -\n", "+            platform: linux/arm64\n", "+            arch: arm64\n", "+            version: '8.0'\n", "+\n", "+    env:\n", "+      ARCH: ${{ matrix.arch }}\n", "+      VERSION_PREFIX: php\n", "+      VERSION: ${{ matrix.version }}\n", "+\n", "+    steps:\n", "+      -\n", "+        name: <PERSON><PERSON>\n", "+        uses: mig4/setup-bats@v1\n", "+        with:\n", "+          bats-version: '1.3.0'\n", "+      -\n", "+        name: Checkout\n", "+        uses: actions/checkout@v2\n", "+      -\n", "+        name: Environment variables\n", "+        run: |\n", "+          # Export variables for further steps\n", "+          echo \"GIT_SHA7=${GITHUB_SHA:0:7}\" >> $GITHUB_ENV\n", "+          echo \"BUILD_IMAGE_TAG=${IMAGE}:${VERSION_PREFIX}${VERSION}-build\" >> ${GITHUB_ENV}\n", "+      -\n", "+        # Switch docker context to a remote arm64 host\n", "+        name: Switch to arm64 builder host\n", "+        if: ${{ env.ARCH == 'arm64' }}\n", "+        uses: arwynfr/actions-docker-context@98fc92878d0b856c1112c79b8d0f45353206e186\n", "+        with:\n", "+          docker_host: \"ssh://ubuntu@${{ secrets.ARM64_HOST }}\"\n", "+          context_name: arm64-host\n", "+          ssh_key: \"${{ secrets.ARM64_HOST_SSH_KEY }}\"\n", "+          ssh_cert: \"${{ secrets.ARM64_HOST_SSH_CERT }}\"\n", "+          use_context: true\n", "+      -\n", "+        name: <PERSON>\n", "+        run: |\n", "+          docker version\n", "+          docker info\n", "+      -\n", "+        # Run tests\n", "+        name: Test\n", "+        id: tests\n", "+        working-directory: ${{ env.VERSION }}\n", "         env:\n", "-          # build tags are always pushed (build caching, debugging needs)\n", "-          # edge, stage, release are only pushed if tests were successful (see docker_tags step)\n", "-          TAGS: |\n", "-            ${{ env.BUILD_IMAGE_TAG }}\n", "-            ${{ steps.docker_tags.outputs.tags }}\n", "-          DOCKERHUB_USERNAME: ${{ secrets.DOCKERHUB_USERNAME }} # Needed for docker-tag-delete.sh\n", "-          DOCKERHUB_PASSWORD: ${{ secrets.DOCKERHUB_PASSWORD }} # Needed for docker-tag-delete.sh\n", "-        run: |\n", "-          set -xeuo pipefail\n", "-          IFS=\"${IFS},\" # Also split strings by comma (in case list of tag is comma-separated)\n", "-          for tag in ${TAGS}; do\n", "-            if [[ \"${tag}\" == \"\" ]]; then continue; fi\n", "-            docker manifest create --amend ${tag} \\\n", "-              ${{ env.BUILD_IMAGE_TAG }}-${{ env.GIT_SHA7 }}-amd64 \\\n", "-              ${{ env.BUILD_IMAGE_TAG }}-${{ env.GIT_SHA7 }}-arm64\n", "-            docker manifest inspect ${tag}\n", "-            docker manifest push ${tag}\n", "-          done\n", "-          # Clean up intermediate arch-specific image tags (DockerHub only)\n", "-          .github/scripts/docker-tag-delete.sh ${{ env.BUILD_IMAGE_TAG }}-${{ env.GIT_SHA7 }}-amd64\n", "-          .github/scripts/docker-tag-delete.sh ${{ env.BUILD_IMAGE_TAG }}-${{ env.GIT_SHA7 }}-arm64\n", "+          BUILD_IMAGE_TAG: docker.io/${{ env.BUILD_IMAGE_TAG }}-${{ env.GIT_SHA7 }}-${{ env.ARCH }}\n", "+          SECRET_PLATFORMSH_CLI_TOKEN: ${{ secrets.SECRET_PLATFORMSH_CLI_TOKEN }}\n", "+          SECRET_TERMINUS_TOKEN: ${{ secrets.SECRET_TERMINUS_TOKEN }}\n", "+        run: |\n", "+          make test\n", "\n", "\n", "========================================================================================================================\n", "Log test result to file\n", "\n", "--- .github/workflows/default.yaml\n", "+++ .github/workflows/default.yaml\n", "@@ -270,9 +270,10 @@\n", "@jobs:\n", "@  push:\n", "@    steps:\n", "             docker manifest create --amend ${tag} \\\n", "               ${{ env.BUILD_IMAGE_TAG }}-${{ env.GIT_SHA7 }}-amd64 \\\n", "               ${{ env.BUILD_IMAGE_TAG }}-${{ env.GIT_SHA7 }}-arm64\n", "             docker manifest inspect ${tag}\n", "             docker manifest push ${tag}\n", "           done\n", "           # Clean up intermediate arch-specific image tags (DockerHub only)\n", "           .github/scripts/docker-tag-delete.sh ${{ env.BUILD_IMAGE_TAG }}-${{ env.GIT_SHA7 }}-amd64\n", "           .github/scripts/docker-tag-delete.sh ${{ env.BUILD_IMAGE_TAG }}-${{ env.GIT_SHA7 }}-arm64\n", "+          ([[ $? == 0 ]] && echo \"pass\" || echo \"fail\") | tee ${{ github.workspace }}/test-results-${VERSION_PREFIX}${VERSION}-${ARCH}.txt\n", "\n", "\n", "========================================================================================================================\n", "Add test results artifact and push multi-arch images\n", "\n", "--- .github/workflows/default.yaml\n", "+++ .github/workflows/default.yaml\n", "@@ -270,9 +270,96 @@\n", "@jobs:\n", "@  push:\n", "@    steps:\n", "             docker manifest create --amend ${tag} \\\n", "               ${{ env.BUILD_IMAGE_TAG }}-${{ env.GIT_SHA7 }}-amd64 \\\n", "               ${{ env.BUILD_IMAGE_TAG }}-${{ env.GIT_SHA7 }}-arm64\n", "             docker manifest inspect ${tag}\n", "             docker manifest push ${tag}\n", "           done\n", "           # Clean up intermediate arch-specific image tags (DockerHub only)\n", "           .github/scripts/docker-tag-delete.sh ${{ env.BUILD_IMAGE_TAG }}-${{ env.GIT_SHA7 }}-amd64\n", "           .github/scripts/docker-tag-delete.sh ${{ env.BUILD_IMAGE_TAG }}-${{ env.GIT_SHA7 }}-arm64\n", "+      # Store tests results as an artifact (used by downstream jobs)\n", "+      # Note: Cannot use \"::set-output name=var_name::var_value\" as var_name would need to be dynamic here.\n", "+      # Dynamic variable names cannot be used when mapping step outputs to job outputs.\n", "+      # Step outputs cannot be accessed directly from other jobs. Dead end.\n", "+      - name: Store test results\n", "+        uses: actions/upload-artifact@v2\n", "+        with:\n", "+          name: test-results\n", "+          path: ${{ github.workspace }}/test-results-*.txt\n", "+\n", "+  push:\n", "+    name: \"Push: ${{ matrix.version }}/multi\"\n", "+    runs-on: ubuntu-20.04\n", "+\n", "+    # Wait for test to either succeed or fail\n", "+    needs: test\n", "+    if: always()\n", "+\n", "+    strategy:\n", "+      matrix:\n", "+        version:\n", "+          - '7.3'\n", "+          - '7.4'\n", "+          - '8.0'\n", "+\n", "+    env:\n", "+      VERSION_PREFIX: php\n", "+      VERSION: ${{ matrix.version }}\n", "+\n", "+    steps:\n", "+      -\n", "+        name: Checkout\n", "+        uses: actions/checkout@v2\n", "+      -\n", "+        name: Environment variables\n", "+        run: |\n", "+          # Export variables for further steps\n", "+          echo \"GIT_SHA7=${GITHUB_SHA:0:7}\" >> $GITHUB_ENV\n", "+          echo \"BUILD_IMAGE_TAG=${IMAGE}:${VERSION_PREFIX}${VERSION}-build\" >> ${GITHUB_ENV}\n", "+      -\n", "+        # <PERSON>gin to <PERSON><PERSON>\n", "+        name: <PERSON><PERSON> to <PERSON><PERSON>\n", "+        uses: docker/login-action@v1\n", "+        with:\n", "+          username: ${{ secrets.DOCKERHUB_USERNAME }}\n", "+          password: ${{ secrets.DOCKERHUB_TOKEN }}\n", "+      -\n", "+        name: Retrieve test results\n", "+        uses: actions/download-artifact@v2\n", "+        with:\n", "+          name: test-results\n", "+      -\n", "+        # Generate persistent tags (edge, stable, release)\n", "+        name: Docker image tags\n", "+        id: docker_tags\n", "+        # Don't push broken builds to persistent tags (both amd64 and arm64 tests must pass)\n", "+        run: |\n", "+          amd64_tests=$(cat test-results-${VERSION_PREFIX}${VERSION}-amd64.txt)\n", "+          arm64_tests=$(cat test-results-${VERSION_PREFIX}${VERSION}-arm64.txt)\n", "+          if [[ \"${amd64_tests}\" == \"pass\" ]] && [[ \"${arm64_tests}\" == \"pass\" ]]; then\n", "+            .github/scripts/docker-tags.sh\n", "+          fi\n", "+      -\n", "+        # Create and push multi-arch image manifests\n", "+        name: Push multi-arch images\n", "+        env:\n", "+          # build tags are always pushed (build caching, debugging needs)\n", "+          # edge, stage, release are only pushed if tests were successful (see docker_tags step)\n", "+          TAGS: |\n", "+            ${{ env.BUILD_IMAGE_TAG }}\n", "+            ${{ steps.docker_tags.outputs.tags }}\n", "+          DOCKERHUB_USERNAME: ${{ secrets.DOCKERHUB_USERNAME }} # Needed for docker-tag-delete.sh\n", "+          DOCKERHUB_PASSWORD: ${{ secrets.DOCKERHUB_PASSWORD }} # Needed for docker-tag-delete.sh\n", "+        run: |\n", "+          set -xeuo pipefail\n", "+          IFS=\"${IFS},\" # Also split strings by comma (in case list of tag is comma-separated)\n", "+          for tag in ${TAGS}; do\n", "+            if [[ \"${tag}\" == \"\" ]]; then continue; fi\n", "+            docker manifest create --amend ${tag} \\\n", "+              ${{ env.BUILD_IMAGE_TAG }}-${{ env.GIT_SHA7 }}-amd64 \\\n", "+              ${{ env.BUILD_IMAGE_TAG }}-${{ env.GIT_SHA7 }}-arm64\n", "+            docker manifest inspect ${tag}\n", "+            docker manifest push ${tag}\n", "+          done\n", "+          # Clean up intermediate arch-specific image tags (DockerHub only)\n", "+          .github/scripts/docker-tag-delete.sh ${{ env.BUILD_IMAGE_TAG }}-${{ env.GIT_SHA7 }}-amd64\n", "+          .github/scripts/docker-tag-delete.sh ${{ env.BUILD_IMAGE_TAG }}-${{ env.GIT_SHA7 }}-arm64\n", "\n", "\n", "========================================================================================================================\n", "Update cron comment\n", "\n", "--- .github/workflows/default.yaml\n", "+++ .github/workflows/default.yaml\n", "@@ -1,12 +1,12 @@\n", " name: Docker Build and Push\n", " \n", " on:\n", "   schedule:\n", "-    - cron: '0 10 * * 0' # Everyday sunday at 10am\n", "+    - cron: '0 10 * * 0' # Every Sunday at 10AM\n", "   push:\n", "     branches:\n", "       - master\n", "       - develop\n", "       - feature/*\n", "     tags:\n", "       - 'v*.*.*'\n", "\n", "\n", "========================================================================================================================\n", "Clarify workflow comment\n", "\n", "--- .github/workflows/default.yaml\n", "+++ .github/workflows/default.yaml\n", "@@ -6,15 +6,16 @@\n", "@on:\n", "   push:\n", "     branches:\n", "       - master\n", "       - develop\n", "       - feature/*\n", "     tags:\n", "       - 'v*.*.*'\n", "-  workflow_dispatch: # Allow manu\n", "+  workflow_dispatch: # Allow manually triggering a build\n", "+\n", " defaults:\n", "   run:\n", "     shell: bash\n", " \n", " env:\n", "   IMAGE: docksal/cli\n", "   UPSTREAM_IMAGE: debian\n", "\n", "\n", "========================================================================================================================\n", "Simplify test matrix\n", "\n", "--- .github/workflows/default.yaml\n", "+++ .github/workflows/default.yaml\n", "@@ -33,38 +33,32 @@\n", "@jobs:\n", "@  build:\n", "@    strategy:\n", "         include:\n", "           -\n", "             platform: linux/amd64\n", "             arch: amd64\n", "             version: '7.4'\n", "           -\n", "             platform: linux/amd64\n", "             arch: amd64\n", "-            version: '7.4'\n", "-          -\n", "-            platform: linux/amd64\n", "-            arch: amd64\n", "-            version: '8.0'\n", "-          -\n", "-            platform: linux/amd64\n", "-            arch: amd64\n", "-            version: '8.1'\n", "-          -\n", "-            platform: linux/arm64\n", "-            arch: arm64\n", "-            version: '8.0'\n", "-          -\n", "-            platform: linux/amd64\n", "-            arch: amd64\n", "-            version: '8.0'\n", "-          -\n", "-            arch: arm64\n", "-            arch: arm64\n", "-            version: '7.4'\n", "-          -\n", "+            version: '8.0'\n", "+          -\n", "+            platform: linux/amd64\n", "+            arch: amd64\n", "+            version: '8.1'\n", "+          -\n", "+            platform: linux/arm64\n", "+            arch: arm64\n", "+            version: '7.4'\n", "+          -\n", "+            platform: linux/arm64\n", "+            arch: arm64\n", "+            version: '8.0'\n", "+          -\n", "+            platform: linux/arm64\n", "+            arch: arm64\n", "             version: '8.1'\n", " \n", "     env:\n", "       ARCH: ${{ matrix.arch }}\n", "       VERSION_PREFIX: php\n", "       VERSION: ${{ matrix.version }}\n", " \n", "     steps:\n", "\n", "\n", "========================================================================================================================\n", "Add PHP 8.1 version\n", "\n", "--- README.md\n", "+++ README.md\n", "@@ -16,21 +16,23 @@\n", " - [VS Code Server](https://github.com/cdr/code-server) (VS Code in the browser)\n", " - Multi-platform images (amd64/arm64) starting with v3.0.0\n", " \n", " \n", " ## Versions and image tag naming convention\n", " \n", " - Stable versions v3 (amd64/arm64)\n", "-  - `php7.3-3.0`, `php7.3-2`, `php7.3` - PHP 7.3\n", "   - `php7.4-3.0`, `php7.4-2`, `php7.4` - PHP 7.4\n", "   - `php8.0-3.0`, `php8.0`, `php8.0`, `latest` - PHP 8.0\n", "+  - `php8.1-3.0`, `php8.1`, `php8.1`, `latest` - PHP 8.1\n", " - Development versions (amd64/arm64)\n", "-  - `php7.3-edge` - PHP 7.3\n", "   - `php7.4-edge` - PHP 7.4\n", "   - `php8.0-edge` - PHP 8.0\n", "+  - `php8.1-edge` - PHP 8.0\n", "+- Previous stable versions v3 (amd64/arm64)\n", "+  - `php7.3-3.0`, `php7.3-2`, `php7.3` - PHP 7.3 \n", " - Previous stable versions v2 (amd64)\n", "   - `php7.3-2.13`, `php7.3-2`, `php7.3` - PHP 7.3\n", "   - `php7.4-2.13`, `php7.4-2`, `php7.4` - PHP 7.4\n", "   - `php8.0-2.13`, `php8.0`, `php8.0` - PHP 8.0\n", " \n", " \n", " ## PHP\n", "\n", "\n", "========================================================================================================================\n", "Failed to process /mnt/efs/spark-data/user/guy/data-pipeline/next_edit_descriptions_v3_randomctx_shortdesc/part-04197-18a737dc-7c25-44d0-aa7c-6674b146ea3a-c000.zstd.parquet: 'descriptions'\n", "Failed to process /mnt/efs/spark-data/user/guy/data-pipeline/next_edit_descriptions_v3_randomctx_shortdesc/part-04727-18a737dc-7c25-44d0-aa7c-6674b146ea3a-c000.zstd.parquet: 'descriptions'\n", "Add publish job to workflow\n", "\n", "--- .github/workflows/ci.yml\n", "+++ .github/workflows/ci.yml\n", "@@ -36,19 +36,67 @@\n", "@jobs:\n", "@  build:\n", " \n", "     - name: Verify formatting\n", "       run: sbt scalafmtCheckAll\n", " \n", "     - name: Run tests\n", "       if: ${{ !startsWith(matrix.scala, '2.13') }}\n", "       run: sbt --client '++${{ matrix.scala }} clean; test'\n", " \n", "     - name: Run tests and coverage\n", "       if: ${{ startsWith(matrix.scala, '2.13') }}\n", "       run: sbt --client '++${{ matrix.scala }} clean; coverage; test'\n", " \n", "     - name: Coverage\n", "       if: ${{ startsWith(matrix.scala, '2.13') }}\n", "       run: sbt --client '++${{ matrix.scala }} coverageReport'\n", " \n", "     - name: <PERSON>cov\n", "       if: ${{ startsWith(matrix.scala, '2.13') }}\n", "       uses: codecov/codecov-action@v3.1.1\n", "+\n", "+  publish:\n", "+    name: Publish artifacts\n", "+    needs: [build]\n", "+    if: github.event_name != 'pull_request' && (github.ref == 'refs/heads/main' || startsWith(github.ref, 'refs/tags/v'))\n", "+    runs-on: ubuntu-latest\n", "+    steps:\n", "+    - uses: actions/checkout@v3\n", "+\n", "+    - name: Set up JDK 8\n", "+      uses: actions/setup-java@v3.9.0\n", "+      with:\n", "+        java-version: '8'\n", "+        distribution: 'temurin'\n", "+\n", "+    - name: <PERSON><PERSON> sbt\n", "+      uses: actions/cache@v3\n", "+      with:\n", "+        path: |\n", "+          ~/.sbt\n", "+          ~/.ivy2/cache\n", "+          ~/.coursier/cache/v1\n", "+          ~/.cache/coursier/v1\n", "+          ~/AppData/Local/Coursier/Cache/v1\n", "+          ~/Library/Caches/Coursier/v1\n", "+        key: ${{ runner.os }}-sbt-cache-v2-${{ hashFiles('**/*.sbt') }}-${{ hashFiles('project/build.properties') }}\n", "+\n", "+    - name: Publish snapshot\n", "+      if: ! startsWith(github.ref, 'refs/tags/v')\n", "+      run: sbt --client +publish\n", "+      env:\n", "+        SONATYPE_USERNAME: ${{ secrets.SONATYPE_USER }}\n", "+        SONATYPE_PASSWORD: ${{ secrets.SONATYPE_PASSWORD }}\n", "+\n", "+    - name: Setup GPG\n", "+      if: startsWith(github.ref, 'refs/tags/v')\n", "+      run: |\n", "+        echo \"$PGP_SECRET\" | base64 --decode | gpg --batch --import\n", "+      env:\n", "+        PGP_SECRET: ${{ secrets.PGP_SECRET }}\n", "+\n", "+    - name: Publish release\n", "+      if: startsWith(github.ref, 'refs/tags/v')\n", "+      run: sbt --client +publishSigned sonatypeBundleRelease\n", "+      env:\n", "+        SONATYPE_USERNAME: ${{ secrets.SONATYPE_USER }}\n", "+        SONATYPE_PASSWORD: ${{ secrets.SONATYPE_PASSWORD }}\n", "+        PGP_PASSPHRASE: ${{ secrets.PGP_PASSPHRASE }}\n", "\n", "\n", "========================================================================================================================\n", "Update Java version to v3.12.0.\n", "\n", "--- .github/workflows/ci.yml\n", "+++ .github/workflows/ci.yml\n", "@@ -1,43 +1,43 @@\n", " name: CI\n", " \n", " on:\n", "   push:\n", "     branches: [ '*' ]\n", "   pull_request:\n", "     branches: [ '*' ]\n", " \n", " jobs:\n", "   build:\n", "     name: Build and test\n", "     strategy:\n", "       matrix:\n", "         scala: [ 2.11.12, 2.12.18, 2.13.11 ]\n", "     runs-on: ubuntu-latest\n", "     steps:\n", "     - uses: actions/checkout@v3\n", " \n", "     - name: Set up JDK 8\n", "-      uses: actions/setup-java@v3.11.0\n", "+      uses: actions/setup-java@v3.12.0\n", "       with:\n", "         java-version: '8'\n", "         distribution: 'temurin'\n", " \n", "     - name: <PERSON><PERSON> sbt\n", "       uses: actions/cache@v3\n", "       with:\n", "         path: |\n", "           ~/.sbt\n", "           ~/.ivy2/cache\n", "           ~/.coursier/cache/v1\n", "           ~/.cache/coursier/v1\n", "           ~/AppData/Local/Coursier/Cache/v1\n", "           ~/Library/Caches/Coursier/v1\n", "         key: ${{ runner.os }}-sbt-cache-v2-${{ hashFiles('**/*.sbt') }}-${{ hashFiles('project/build.properties') }}\n", " \n", "     - name: Verify formatting\n", "       run: sbt scalafmtCheckAll\n", " \n", "     - name: Run tests\n", "       if: ${{ !startsWith(matrix.scala, '2.13') }}\n", "       run: sbt --client '++${{ matrix.scala }} clean; test'\n", " \n", "\n", "\n", "========================================================================================================================\n", "Update sbt version\n", "\n", "--- project/build.properties\n", "+++ project/build.properties\n", "@@ -1,1 +1,1 @@\n", "-sbt.version = 1.9.3\n", "+sbt.version = 1.9.4\n", "\n", "\n", "========================================================================================================================\n", "Update Scala version to 2.13.13.\n", "\n", "--- .github/workflows/ci.yml\n", "+++ .github/workflows/ci.yml\n", "@@ -1,82 +1,82 @@\n", " name: CI\n", " \n", " on:\n", "   push:\n", "     branches: [ '*' ]\n", "   pull_request:\n", "     branches: [ '*' ]\n", " \n", " jobs:\n", "   build:\n", "     name: Build and test\n", "     strategy:\n", "       matrix:\n", "-        scala: [ 2.11.12, 2.12.18, 2.13.12 ]\n", "+        scala: [ 2.11.12, 2.12.18, 2.13.13 ]\n", "     runs-on: ubuntu-latest\n", "     steps:\n", "     - uses: actions/checkout@v4\n", " \n", "     - name: Set up JDK 8\n", "       uses: actions/setup-java@v4.0.0\n", "       with:\n", "         java-version: '8'\n", "         distribution: 'temurin'\n", " \n", "     - name: <PERSON><PERSON> sbt\n", "       uses: actions/cache@v4\n", "       with:\n", "         path: |\n", "           ~/.sbt\n", "           ~/.ivy2/cache\n", "           ~/.coursier/cache/v1\n", "           ~/.cache/coursier/v1\n", "           ~/AppData/Local/Coursier/Cache/v1\n", "           ~/Library/Caches/Coursier/v1\n", "         key: ${{ runner.os }}-sbt-cache-v2-${{ hashFiles('**/*.sbt') }}-${{ hashFiles('project/build.properties') }}\n", " \n", "     - name: Verify formatting\n", "       run: sbt scalafmtCheckAll\n", " \n", "     - name: Run tests\n", "       if: ${{ !startsWith(matrix.scala, '2.13') }}\n", "       run: sbt --client '++${{ matrix.scala }} clean; test'\n", " \n", "     - name: Run tests and coverage\n", "       if: ${{ startsWith(matrix.scala, '2.13') }}\n", "       run: sbt --client '++${{ matrix.scala }} clean; coverage; test'\n", " \n", "     - name: Coverage\n", "       if: ${{ startsWith(matrix.scala, '2.13') }}\n", "       run: sbt --client '++${{ matrix.scala }} coverageReport'\n", " \n", "     - name: <PERSON>cov\n", "       if: ${{ startsWith(matrix.scala, '2.13') }}\n", "-      uses: codecov/codecov-action@v4.0.1\n", "+      uses: codecov/codecov-action@v4.0.2\n", " \n", "   publish:\n", "     name: Publish artifacts\n", "     needs: [build]\n", "     if: github.event_name != 'pull_request' && (github.ref == 'refs/heads/master' || startsWith(github.ref, 'refs/tags/v'))\n", "     runs-on: ubuntu-latest\n", "     steps:\n", "     - uses: actions/checkout@v4\n", " \n", "     - name: Set up JDK 8\n", "       uses: actions/setup-java@v4.0.0\n", "       with:\n", "         java-version: '8'\n", "         distribution: 'temurin'\n", " \n", "     - name: <PERSON><PERSON> sbt\n", "       uses: actions/cache@v4\n", "       with:\n", "         path: |\n", "           ~/.sbt\n", "           ~/.ivy2/cache\n", "           ~/.coursier/cache/v1\n", "           ~/.cache/coursier/v1\n", "           ~/AppData/Local/Coursier/Cache/v1\n", "           ~/Library/Caches/Coursier/v1\n", "         key: ${{ runner.os }}-sbt-cache-v2-${{ hashFiles('**/*.sbt') }}-${{ hashFiles('project/build.properties') }}\n", " \n", "     - name: Publish snapshot\n", "\n", "\n", "========================================================================================================================\n", "Update codecov-action version\n", "\n", "--- .github/workflows/ci.yml\n", "+++ .github/workflows/ci.yml\n", "@@ -38,33 +38,33 @@\n", "@jobs:\n", "@  build:\n", "@    - name: Verify formatting\n", "       run: sbt scalafmtCheckAll\n", " \n", "     - name: Run tests\n", "       if: ${{ !startsWith(matrix.scala, '2.13') }}\n", "       run: sbt --client '++${{ matrix.scala }} clean; test'\n", " \n", "     - name: Run tests and coverage\n", "       if: ${{ startsWith(matrix.scala, '2.13') }}\n", "       run: sbt --client '++${{ matrix.scala }} clean; coverage; test'\n", " \n", "     - name: Coverage\n", "       if: ${{ startsWith(matrix.scala, '2.13') }}\n", "       run: sbt --client '++${{ matrix.scala }} coverageReport'\n", " \n", "     - name: <PERSON>cov\n", "       if: ${{ startsWith(matrix.scala, '2.13') }}\n", "-      uses: codecov/codecov-action@v4.3.1\n", "+      uses: codecov/codecov-action@v4.4.0\n", " \n", "   publish:\n", "     name: Publish artifacts\n", "     needs: [build]\n", "     if: github.event_name != 'pull_request' && (github.ref == 'refs/heads/master' || startsWith(github.ref, 'refs/tags/v'))\n", "     runs-on: ubuntu-latest\n", "     steps:\n", "     - uses: actions/checkout@v4\n", " \n", "     - name: Set up JDK 8\n", "       uses: actions/setup-java@v4.2.1\n", "       with:\n", "         java-version: '8'\n", "         distribution: 'temurin'\n", " \n", "     - name: <PERSON><PERSON> sbt\n", "\n", "\n", "========================================================================================================================\n", "Update Codecov action version\n", "\n", "--- .github/workflows/ci.yml\n", "+++ .github/workflows/ci.yml\n", "@@ -43,23 +43,23 @@\n", "@jobs:\n", "@  build:\n", " \n", "     - name: Run tests and coverage\n", "       if: ${{ startsWith(matrix.scala, '2.13') }}\n", "       run: sbt --client '++${{ matrix.scala }} clean; coverage; test'\n", " \n", "     - name: Coverage\n", "       if: ${{ startsWith(matrix.scala, '2.13') }}\n", "       run: sbt --client '++${{ matrix.scala }} coverageReport'\n", " \n", "     - name: <PERSON>cov\n", "       if: ${{ startsWith(matrix.scala, '2.13') }}\n", "-      uses: codecov/codecov-action@v4.4.0\n", "+      uses: codecov/codecov-action@v4.4.1\n", " \n", "   publish:\n", "     name: Publish artifacts\n", "     needs: [build]\n", "     if: github.event_name != 'pull_request' && (github.ref == 'refs/heads/master' || startsWith(github.ref, 'refs/tags/v'))\n", "     runs-on: ubuntu-latest\n", "     steps:\n", "     - uses: actions/checkout@v4\n", " \n", "     - name: Set up JDK 8\n", "       uses: actions/setup-java@v4.2.1\n", "\n", "\n", "========================================================================================================================\n", "Failed to process /mnt/efs/spark-data/user/guy/data-pipeline/next_edit_descriptions_v3_randomctx_shortdesc/part-05434-18a737dc-7c25-44d0-aa7c-6674b146ea3a-c000.zstd.parquet: 'descriptions'\n", "Failed to process /mnt/efs/spark-data/user/guy/data-pipeline/next_edit_descriptions_v3_randomctx_shortdesc/part-05538-18a737dc-7c25-44d0-aa7c-6674b146ea3a-c000.zstd.parquet: 'descriptions'\n", "Failed to process /mnt/efs/spark-data/user/guy/data-pipeline/next_edit_descriptions_v3_randomctx_shortdesc/part-05808-18a737dc-7c25-44d0-aa7c-6674b146ea3a-c000.zstd.parquet: 'descriptions'\n", "Failed to process /mnt/efs/spark-data/user/guy/data-pipeline/next_edit_descriptions_v3_randomctx_shortdesc/part-06848-18a737dc-7c25-44d0-aa7c-6674b146ea3a-c000.zstd.parquet: 'descriptions'\n", "Failed to process /mnt/efs/spark-data/user/guy/data-pipeline/next_edit_descriptions_v3_randomctx_shortdesc/part-07323-18a737dc-7c25-44d0-aa7c-6674b146ea3a-c000.zstd.parquet: 'descriptions'\n", "Failed to process /mnt/efs/spark-data/user/guy/data-pipeline/next_edit_descriptions_v3_randomctx_shortdesc/part-07367-18a737dc-7c25-44d0-aa7c-6674b146ea3a-c000.zstd.parquet: 'descriptions'\n", "Failed to process /mnt/efs/spark-data/user/guy/data-pipeline/next_edit_descriptions_v3_randomctx_shortdesc/part-07835-18a737dc-7c25-44d0-aa7c-6674b146ea3a-c000.zstd.parquet: 'descriptions'\n", "Failed to process /mnt/efs/spark-data/user/guy/data-pipeline/next_edit_descriptions_v3_randomctx_shortdesc/part-08078-18a737dc-7c25-44d0-aa7c-6674b146ea3a-c000.zstd.parquet: 'descriptions'\n", "Failed to process /mnt/efs/spark-data/user/guy/data-pipeline/next_edit_descriptions_v3_randomctx_shortdesc/part-08152-18a737dc-7c25-44d0-aa7c-6674b146ea3a-c000.zstd.parquet: 'descriptions'\n", "Failed to process /mnt/efs/spark-data/user/guy/data-pipeline/next_edit_descriptions_v3_randomctx_shortdesc/part-09022-18a737dc-7c25-44d0-aa7c-6674b146ea3a-c000.zstd.parquet: 'descriptions'\n", "Failed to process /mnt/efs/spark-data/user/guy/data-pipeline/next_edit_descriptions_v3_randomctx_shortdesc/part-09757-18a737dc-7c25-44d0-aa7c-6674b146ea3a-c000.zstd.parquet: 'descriptions'\n"]}], "source": ["# Looking for typos\n", "\n", "import pandas\n", "from pathlib import Path\n", "from tqdm import tqdm\n", "\n", "descriptions_root = Path(\n", "    \"/mnt/efs/spark-data/user/guy/data-pipeline/next_edit_descriptions_v3_randomctx_shortdesc\"\n", ")\n", "\n", "max_files = 100\n", "\n", "parquet_files = list(descriptions_root.glob(\"*.parquet\"))[:max_files]\n", "\n", "for parquet_file in parquet_files:\n", "    try:\n", "        df = pandas.read_parquet(parquet_file)\n", "        for desc, diff in zip(\n", "            list(df[\"descriptions\"][0]), list(df[\"description_diff_inputs\"][0])\n", "        ):\n", "            print(desc)\n", "            print()\n", "            print(diff)\n", "            print()\n", "            print(\"=\" * 120)\n", "    except Exception as e:\n", "        print(f\"Failed to process {parquet_file}: {e}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Generate descriptions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.diff_utils.diff_formatter import _split_into_hunks\n", "\n", "the_diff = \"\"\"\\\n", "--- lib/modules.js\n", "+++ lib/modules.js\n", "@@ -94,25 +94,33 @@\n", "@function installModule (app, name, version, onData, onErr, onClose) {\n", "   var npm\n", " \n", "   var opts = {}\n", " \n", "   if (name == app.config.name) {\n", "-\n", "-  if (process.platform == 'win32') {\n", "+    if (process.platform == 'win32') {\n", "       npm = spawn(\n", "-      'cmd',\n", "-        ['/c', 'npm --save install -g --unsafe-perm ' + `${name}@${version}`],\n", "+        'cmd',\n", "+        ['/c', 'npm install -g --unsafe-perm ' + `${name}@${version}`],\n", "         opts\n", "       )\n", "     } else {\n", "       npm = spawn(\n", "         'sudo',\n", "         ['npm', 'install', '-g', '--unsafe-perm', `${name}@${version}`],\n", "         opts\n", "       )\n", "     }\n", "   } else {\n", "+    opts.cwd = app.config.configPath\n", "+\n", "+    if (process.platform == 'win32') {\n", "+      npm = spawn(\n", "+        'cmd',\n", "+        ['/c', 'npm --save install ' + `${name}@${version}`],\n", "+        opts\n", "+      )\n", "+    } else {\n", "       npm = spawn('npm', ['--save', 'install', `${name}@${version}`], opts)\n", "     }\n", "   }\n", " \n", "   npm.stdout.on('data', onData)\n", "\"\"\"\n", "\n", "diff_hunks = _split_into_hunks(\n", "    the_diff, filter_duplicated_file_paths=False, before_lmap=None, after_lmap=None\n", ")\n", "# diffs = compute_suggested_edit_diffs(the_diff)\n", "\n", "for elem in diff_hunks:\n", "    print(elem.text)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas\n", "import pickle\n", "from pathlib import Path\n", "\n", "from base.diff_utils.diff_utils import File, compute_file_diff\n", "from research.next_edits.edit_gen_sampler import EditGenProblem\n", "from research.llm_apis.chat_utils import Llama3ChatClient\n", "\n", "from base.diff_utils.diff_formatter import _split_into_hunks\n", "\n", "\n", "def compute_suggested_edit_diffs(problem: EditGenProblem) -> list[str]:\n", "    current_code = problem.current_code\n", "    new_code = problem.prefix + problem.output.replacement + problem.suffix\n", "    diff = compute_file_diff(\n", "        before_file=File(path=str(problem.current_path), contents=current_code),\n", "        after_file=File(path=str(problem.current_path), contents=new_code),\n", "        use_smart_header=True,\n", "        num_context_lines=5,\n", "    )\n", "\n", "    diff_hunks = _split_into_hunks(\n", "        diff, filter_duplicated_file_paths=False, before_lmap=None, after_lmap=None\n", "    )\n", "    return [hunk.text for hunk in diff_hunks]\n", "\n", "\n", "client = Llama3ChatClient(\"triton\", address=\"*************:8000\", timeout=180)\n", "\n", "# system_prompt = \"\"\"\\\n", "# You are a developer working in my codebase.\n", "# Your task is to extract relevant information from the code change below \\\n", "# and describe it using imperative verb form. The purpose of your description is \\\n", "# to help other developers quickly understand key aspects of the change in the context \\\n", "# of the codebase.\"\"\"\n", "\n", "prompt_template = \"\"\"\\\n", "You are a developer working in my codebase.\n", "Your task is to extract relevant information from the code change below \\\n", "and describe it using imperative verb form. The purpose of your description is \\\n", "to help other developers quickly understand key aspects of the change in the context \\\n", "of the codebase.\n", "\n", "Directly start the response with the description and say nothing else.\n", "Write a short description and no more than 10 words.\n", "Focus on the fields that are changing but don't include where they are.\n", "\n", "\n", "Code change:\n", "```\n", "--- src/org/ensembl/healthcheck/testcase/generic/CheckDeclarations.java\n", "+++ src/org/ensembl/healthcheck/testcase/generic/CheckDeclarations.java\n", "@@ -94,47 +94,47 @@\n", "@public class CheckDeclarations extends SingleDatabaseTestCase {\n", "@        public boolean run(final DatabaseRegistryEntry dbre) {\n", " \n", "                 Connection previousCon = sec.getConnection();\n", " \n", "                 result &= checkAssembly(dbre, sec);\n", " \n", "                 result &= checkRepeats(dbre, sec);\n", " \n", "                 result &= checkGenes(dbre, sec);\n", " \n", "                 return result;\n", "         }\n", " \n", "   private boolean checkAssembly(DatabaseRegistryEntry dbre, DatabaseRegistryEntry sec) {\n", " \n", "     boolean result = true;\n", " \n", "     Connection con = dbre.getConnection();\n", "     Connection previousCon = sec.getConnection();\n", " \n", "     String sql = \"CHECKSUM table assembly\";\n", "     int currentAssembly = DBUtils.getRowCount(con, sql);\n", "     int previousAssembly = DBUtils.getRowCount(previousCon, sql);\n", " \n", "-    if (previousAssembly != currentAssembly)) {\n", "+    if (previousAssembly != currentAssembly) {\n", "       boolean declared = checkDeclaration(dbre, \"assembly\");\n", "       if (!declared) {\n", "         ReportManager.problem(this, con, \"Assembly has changed but has not been declared\");\n", "         result = false;\n", "       }\n", "     }\n", " \n", "     return result;\n", "   }\n", "```\n", "\n", "Response:\n", "Fix typo\n", "\n", "Code change:\n", "```\n", "--- .github/workflows/release.yml\n", "+++ .github/workflows/release.yml\n", "@@ -17,12 +17,12 @@\n", "@jobs:\n", "@  release:\n", "@    - uses: actions/setup-java@v4.0.0\n", "       with:\n", "         distribution: 'temurin'\n", "         java-version: '8'\n", "     - name: <PERSON><PERSON><PERSON> <PERSON>\n", "       uses: coursier/cache-action@v6\n", "-    - name: sbt ci-release-cont ${{ github.ref }}\n", "-      run: ./sbt ci-release-cont\n", "+    - name: sbt ci-release ${{ github.ref }}\n", "+      run: ./sbt ci-release\n", "       env:\n", "         PGP_PASSPHRASE: ${{ secrets.PGP_PASSPHRASE }}\n", "         PGP_SECRET: ${{ secrets.PGP_SECRET }}\n", "         SONATYPE_PASSWORD: ${{ secrets.SONATYPE_PASSWORD }}\n", "         SONATYPE_USERNAME: ${{ secrets.SONATYPE_USERNAME }}\n", "```\n", "\n", "Response:\n", "Rename sbt task to ci-release\n", "\n", "Code change:\n", "```\n", "--- packages/server-admin-ui/src/views/ServerConfig/BasicProvider.js\n", "+++ packages/server-admin-ui/src/views/ServerConfig/BasicProvider.js\n", "@@ -147,13 +147,15 @@\n", "@class LoggingInput extends Component {\n", "     )\n", "   }\n", " }\n", " \n", " class ValidateChecksumInput extends Component {\n", "-  constructor(props) {\n", "+  constructor (props) {\n", "     super(props)\n", "-    this.props.value.validateChecksum = typeof this.props.value.validateChecksum === 'undefined' || this.props.value.validateChecksum\n", "+    this.props.value.validateChecksum =\n", "+      typeof this.props.value.validateChecksum === 'undefined' ||\n", "+      this.props.value.validateChecksum\n", "   }\n", "   render () {\n", "     return (\n", "       <FormGroup row>\n", "         <Col xs='3' md='2'>\n", "```\n", "\n", "Response:\n", "Improve formatting\n", "\n", "Code change:\n", "```\n", "[DIFF_STR]```\n", "\"\"\"\n", "\n", "dataset_path = Path(\n", "    \"/mnt/efs/spark-data/shared/next-edit/stage1/prv2-pr_grouped_10k/S24_10000p\"\n", ")\n", "parquet_files = list(dataset_path.glob(\"*.parquet\"))\n", "remaining_samples = 20\n", "\n", "for parquet_file in parquet_files:\n", "    df = pandas.read_parquet(parquet_file)\n", "    print(\"Columns in the Parquet file:\")\n", "    for column in df.columns:\n", "        print(f\"- {column}\")\n", "\n", "    for row in df.to_dict(orient=\"records\"):\n", "        data = pickle.loads(row[\"pickled_results\"])\n", "\n", "        for change in data:\n", "            # print(change.commit_meta.message)\n", "            # print(change.repo_change)\n", "            # print(change.selected_code)\n", "            # print(change.output.replacement)\n", "            # print()\n", "\n", "            diffs = compute_suggested_edit_diffs(change)\n", "\n", "            for diff in diffs:\n", "                if not diff.strip():\n", "                    print(\"Empty diff, skipping\")\n", "                    continue\n", "\n", "                # prompt = prompt_template.format(diff_str=diff)\n", "                prompt = prompt_template.replace(\"[DIFF_STR]\", diff)\n", "\n", "                # print(\"Prompt:\")\n", "                # print(prompt)\n", "                # print()\n", "\n", "                response = client.generate(messages=[prompt], max_tokens=256)\n", "\n", "                if \"version\" in response:\n", "                    continue\n", "\n", "                print(\"Diff:\")\n", "                print(diff)\n", "                print()\n", "\n", "                print(\"Generated description:\", response)\n", "                print()\n", "\n", "                print(\"=\" * 128)\n", "\n", "                remaining_samples -= 1\n", "                if remaining_samples <= 0:\n", "                    break\n", "            if remaining_samples <= 0:\n", "                break\n", "        if remaining_samples <= 0:\n", "            break\n", "    if remaining_samples <= 0:\n", "        break"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas\n", "from pathlib import Path\n", "\n", "dataset_path = Path(\n", "    \"/mnt/efs/spark-data/user/guy/data-pipeline/next_edit_descriptions/part-09512-18a737dc-7c25-44d0-aa7c-6674b146ea3a-c000.zstd.parquet\"\n", ")\n", "\n", "training_prompt_template = \"\"\"\\\n", "<|begin_of_text|><|start_header_id|>user<|end_header_id|>\n", "You are a developer working in my codebase.\n", "Your task is to extract relevant information from the code change below \\\n", "and describe it using imperative verb form. The purpose of your description is \\\n", "to help other developers quickly understand key aspects of the change in the context \\\n", "of the codebase.\n", "\n", "Directly start the response with the description and say nothing else.\n", "Write no more than 10 words.\n", "Focus on the fields that are changing but don't include where they are.\n", "\n", "Code change:\n", "```\n", "{diff_str}```\n", "<|eot_id|><|start_header_id|>assistant<|end_header_id|>\n", "\"\"\"\n", "\n", "training_label_template = \"{description}<|eot_id|>\"\n", "\n", "df = pandas.read_parquet(dataset_path)\n", "\n", "# print(df.columns)\n", "\n", "for desc, diff in zip(\n", "    list(df[\"descriptions\"][0]), list(df[\"description_diff_inputs\"][0])\n", "):\n", "    prompt = training_prompt_template.format(diff_str=diff)\n", "    label = training_label_template.format(description=desc)\n", "\n", "    if not desc:\n", "        continue\n", "\n", "    print(prompt + label)\n", "    print()\n", "    break"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.tokenizers import create_tokenizer_by_name\n", "from base.prompt_format_next_edit.description_prompt_formatter import (\n", "    RavenDescribePromptFormatter,\n", ")\n", "from base.prompt_format_chat import get_struct_to_tokens_prompt_formatter_by_name\n", "\n", "# Taken from: services/deploy/raven_edit_v2_15b_deploy.jsonnet\n", "tokenizer_name = \"llama3_instruct\"\n", "prompt_formatter_config = RavenDescribePromptFormatter.Config()\n", "chat_prompt_formatter_name = \"llama3\"\n", "\n", "description_tokenizer = create_tokenizer_by_name(tokenizer_name)\n", "description_prompt_formatter = RavenDescribePromptFormatter(\n", "    description_tokenizer,\n", "    config=prompt_formatter_config,\n", ")\n", "chat_prompt_formatter = get_struct_to_tokens_prompt_formatter_by_name(\n", "    chat_prompt_formatter_name,\n", "    description_tokenizer,\n", ")\n", "\n", "structured_prompt = description_prompt_formatter.format_input_from_diff_str(\n", "    \"+ hello\\n- goodbye\\n\"\n", ")\n", "tokenized_prompt = chat_prompt_formatter.format_prompt(structured_prompt)\n", "\n", "# print(structured_prompt)\n", "\n", "print(description_tokenizer.detokenize(tokenized_prompt.tokens))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas\n", "import pickle\n", "from pathlib import Path\n", "\n", "from research.next_edits.edit_gen_sampler import EditGenProblem\n", "from research.llm_apis.chat_utils import Llama3ChatClient\n", "\n", "\n", "def compute_suggested_edit_diff(problem: EditGenProblem) -> str:\n", "    current_code = problem.current_code\n", "    new_code = problem.prefix + problem.output.replacement + problem.suffix\n", "    diff = compute_file_diff(\n", "        before_file=File(path=str(problem.current_path), contents=current_code),\n", "        after_file=File(path=str(problem.current_path), contents=new_code),\n", "        use_smart_header=True,\n", "        num_context_lines=50,\n", "    )\n", "    return diff\n", "\n", "\n", "client = Llama3ChatClient(\"triton\", address=\"*************:8000\", timeout=180)\n", "\n", "system = \"\"\"\\\n", "You are a developer working in my codebase.\n", "Your task is to extract relevant information from the code change below and describe it using imperative verb form. The purpose of your description is to help other developers quickly understand key aspects of the change in the context of the codebase.\n", "\"\"\"\n", "\n", "message = '''\\\n", "Here is the code change delimited by triple backticks:\n", "```\n", "--- research/llm_apis/chat_utils.py\n", "+++ research/llm_apis/chat_utils.py\n", "@@ -207,10 +207,13 @@\n", "@class Llama3ChatClient:\n", "@    def get_prompt_token_length(\n", "         \"\"\"Returns the number of tokens in the prompt.\"\"\"\n", "         return len(self._prepare_prompt_tokens(messages, system_prompt))\n", " \n", "     def get_token_length(self, text: str) -> int:\n", "         return len(self.tokenizer.encode(text, bos=True, eos=True))\n", "+\n", "+    def __str__(self) -> str:\n", "+        return f\"LLaMA 3 {self.server_type} client\"\n", " \n", " \n", " class Llama3LlamaCppApiClient(Llama3ChatClient):\n", "     \"\"\"For backward compatibility with existing pipeline code.\"\"\"\n", " \n", "\n", "```\n", "Directly start the response with the description and say nothing else. Write no more than 10 words.\n", "Focus on the fields that are changing but don't include where they are.\n", "'''\n", "\n", "response = client.generate(system_prompt=system, messages=[message], max_tokens=64)\n", "print(response)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Splitting"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from typing import Iterable\n", "import pandas\n", "import pickle\n", "\n", "from research.model_server.next_edits_handlers import (\n", "    NextEditResult,\n", "    _split_changes_into_hunks,\n", ")\n", "from research.next_edits.edit_gen_sampler import EditGenProblem\n", "from pathlib import Path\n", "\n", "\n", "def compute_suggested_edit_diff_2(problem: EditGenProblem) -> Iterable[str]:\n", "    \"\"\"Compute the diff between the current code and the new code.\"\"\"\n", "    current_code = problem.current_code\n", "    new_code = problem.prefix + problem.output.replacement + problem.suffix\n", "    # return\n", "    change = NextEditResult(\n", "        suggestion_id=\"\",\n", "        path=str(problem.current_path),\n", "        blob_name=\"\",\n", "        char_start=problem.edit_region.start,\n", "        char_end=problem.edit_region.stop,\n", "        existing_code=current_code,\n", "        suggested_code=new_code,\n", "        truncation_char=None,\n", "        change_description=\"\",\n", "        diff_spans=[],  # does it makes sense for this to be empty at this point?\n", "    )\n", "    # How does the number of context lines impact things?\n", "    # in both cases they determine when hunks get merged. In compute_file_diff, only a string is returned.\n", "    for change in _split_changes_into_hunks(\n", "        change, n_context_lines=3\n", "    ):  # 3 is the default argument\n", "        # Have to check that this looks right -- do we even need to use this function?\n", "        # print(change)\n", "        diff = compute_file_diff(\n", "            before_file=File(path=str(change.path), contents=change.existing_code),\n", "            after_file=File(path=str(change.path), contents=change.suggested_code),\n", "            use_smart_header=True,\n", "            num_context_lines=3,\n", "        )\n", "        yield diff\n", "\n", "\n", "dataset_path = Path(\n", "    \"/mnt/efs/spark-data/shared/next-edit/stage1/prv2-pr_grouped_10k/S24_10000p\"\n", ")\n", "parquet_files = list(dataset_path.glob(\"*.parquet\"))\n", "remaining_samples = 10\n", "\n", "for parquet_file in parquet_files:\n", "    df = pandas.read_parquet(parquet_file)\n", "    print(\"Columns in the Parquet file:\")\n", "    for column in df.columns:\n", "        print(f\"- {column}\")\n", "\n", "    for row in df.to_dict(orient=\"records\"):\n", "        data = pickle.loads(row[\"pickled_results\"])\n", "\n", "        for change in data:\n", "            diffs = compute_suggested_edit_diff_2(change)\n", "\n", "            for diff in diffs:\n", "                print(\"=\" * 120)\n", "                print(diff)\n", "\n", "            remaining_samples -= 1\n", "            if remaining_samples <= 0:\n", "                break\n", "        if remaining_samples <= 0:\n", "            break\n", "    if remaining_samples <= 0:\n", "        break"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}