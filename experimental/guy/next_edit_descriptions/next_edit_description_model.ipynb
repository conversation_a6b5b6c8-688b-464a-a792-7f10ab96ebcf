{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Load the trained next-edit descriptions model and use it"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["> Initializing model parallel with size 1\n", "> Initializing DDP with size 1\n"]}], "source": ["from pathlib import Path\n", "from research.models.fastbackward_models import FastBackwardLLM\n", "from megatron.tokenizer.tokenizer import get_tokenizer\n", "from research.models.meta_model import GenerationOptions\n", "\n", "tokenizer = get_tokenizer(\"Llama3InstructTokenizer\")\n", "model = FastBackwardLLM(\n", "    checkpoint_path=Path(\n", "        # \"/mnt/efs/augment/user/guy/checkpoints/next-edit-descriptions/llama3-8b-instruct-next-edit-descriptions-v0-instructfix\"\n", "        \"/mnt/efs/augment/user/guy/checkpoints/next-edit-descriptions/llama3-8b-instruct-next-edit-descriptions-v1-randomctx-shortdesc\"\n", "    ),\n", "    seq_length=4096,\n", "    model_parallel_size=1,\n", ")\n", "model.load()\n", "\n", "\n", "def generate_output(prompt: str, max_tokens=48) -> str:\n", "    \"\"\"Generate output from a raw prompt.\"\"\"\n", "    prompt_tokens = tokenizer.tokenize_unsafe(prompt)\n", "    result = model.raw_generate_tokens(\n", "        prompt_tokens, GenerationOptions(max_generated_tokens=max_tokens)\n", "    ).tokens\n", "\n", "    if not result[-1] == tokenizer.eod_id:\n", "        raise ValueError(\"Output doesn't end with EOD\")\n", "\n", "    result = result[: result.index(tokenizer.eod_id)]\n", "    output = tokenizer.detokenize(result)\n", "    return output"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Add string representation\n"]}], "source": ["prompt1 = r\"\"\"\\\n", "<|begin_of_text|><|start_header_id|>system<|end_header_id|>\n", "\n", "You are a developer working in my codebase.\n", "Your task is to extract relevant information from the code change below and describe it using imperative verb form. The purpose of your description is to help other developers quickly understand key aspects of the change in the context of the codebase.<|eot_id|><|start_header_id|>user<|end_header_id|>\n", "\n", "Here is the code change delimited by triple backticks:\n", "```\n", "--- src/SFA.DAS.ApprenticeCommitments/Application/Commands/UpdateApproval/UpdateApprovalCommandHandler.cs\n", "+++ src/SFA.DAS.ApprenticeCommitments/Application/Commands/UpdateApproval/UpdateApprovalCommandHandler.cs\n", "@@ -15,77 +15,78 @@\n", "@namespace SFA.DAS.ApprenticeCommitments.Application.Commands.UpdateApproval\n", " {\n", "     public class UpdateApprovalCommandHandler : IRequestHandler<UpdateApprovalCommand>\n", "     {\n", "         private readonly ApprenticeCommitmentsService _apprenticeCommitmentsService;\n", "         private readonly CommitmentsV2Service _commitmentsService;\n", "         private readonly TrainingProviderService _trainingProviderService;\n", "         private readonly CoursesService _coursesService;\n", "         private readonly ILogger<UpdateApprovalCommandHandler> _logger;\n", " \n", "         public UpdateApprovalCommandHandler(\n", "             ApprenticeCommitmentsService apprenticeCommitmentsService,\n", "             CommitmentsV2Service commitmentsV2Service,\n", "             TrainingProviderService trainingProviderService,\n", "             CoursesService coursesService,\n", "             ILogger<UpdateApprovalCommandHandler> logger)\n", "         {\n", "             _apprenticeCommitmentsService = apprenticeCommitmentsService;\n", "             _commitmentsService = commitmentsV2Service;\n", "             _trainingProviderService = trainingProviderService;\n", "             _coursesService = coursesService;\n", "             _logger = logger;\n", "         }\n", " \n", "         public async Task<Unit> Handle(\n", "             UpdateApprovalCommand command,\n", "             CancellationToken cancellationToken)\n", "         {\n", "             var (apprenticeship, provider, course) = await GetExternalData(command) ?? default;\n", " \n", "             if (apprenticeship == null) return default;\n", " \n", "             await _apprenticeCommitmentsService.ChangeApproval(new ChangeApprovalRequestData\n", "             {\n", "                 CommitmentsContinuedApprenticeshipId = command.CommitmentsContinuedApprenticeshipId,\n", "                 CommitmentsApprenticeshipId = command.CommitmentsApprenticeshipId,\n", "                 FirstName = apprenticeship.FirstName,\n", "                 LastName = apprenticeship.LastName,\n", "                 DateOfBirth = apprenticeship.DateOfBirth,\n", "                 Email = apprenticeship.Email,\n", "                 EmployerName = apprenticeship.EmployerName,\n", "                 EmployerAccountLegalEntityId = apprenticeship.AccountLegalEntityId,\n", "                 TrainingProviderId = apprenticeship.ProviderId,\n", "                 TrainingProviderName = IsNullOrWhiteSpace(provider.TradingName) ? provider.LegalName : provider.TradingName,\n", "                 DeliveryModel = apprenticeship.DeliveryModel,\n", "                 CourseName = course.Title,\n", "                 CourseLevel = course.Level,\n", "                 CourseDuration = course.TypicalDuration,\n", "                 PlannedStartDate = apprenticeship.StartDate,\n", "                 PlannedEndDate = apprenticeship.EndDate,\n", "                 CommitmentsApprovedOn = command.CommitmentsApprovedOn,\n", "+                EmploymentEndDate = apprenticeship.EmploymentEndDate\n", "             });\n", " \n", "             return default;\n", "         }\n", " \n", "         private async Task<(ApprenticeshipResponse, TrainingProviderResponse, StandardApiResponse)?>\n", "             GetExternalData(UpdateApprovalCommand command)\n", "         {\n", "             var apprenticeship = await _commitmentsService.GetApprenticeshipDetails(\n", "                 command.CommitmentsApprenticeshipId);\n", " \n", "             if (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(apprenticeship.Email))\n", "             {\n", "                 _logger.LogInformation(\"Apprenticeship {apprenticeshipId} does not have an email, no point in continuing\", apprenticeship.Id);\n", "                 return default;\n", "             }\n", " \n", "             var courseCode = apprenticeship.GetCourseCode(_logger);\n", "             if (courseCode is null) return default;\n", " \n", "             var course = _coursesService.GetCourse(courseCode);\n", "             var provider = _trainingProviderService.GetTrainingProviderDetails(apprenticeship.ProviderId);\n", " \n", "             return (apprenticeship, await provider, await course);\n", "         }\n", "     }\n", " }\n", "\\ No newline at end of file\n", "\n", "```\n", "Directly start the response with the description and say nothing else. Write no more than 10 words.\n", "Focus on the fields that are changing but don't include where they are.\n", "<|eot_id|><|start_header_id|>assistant<|end_header_id|>\n", "\"\"\"\n", "\n", "label = \"Add EmploymentEndDate to ChangeApprovalRequestData\"\n", "\n", "prompt2 = '''\\\n", "<|begin_of_text|><|start_header_id|>system<|end_header_id|>\n", "\n", "You are a developer working in my codebase.\n", "Your task is to extract relevant information from the code change below and describe it using imperative verb form. The purpose of your description is to help other developers quickly understand key aspects of the change in the context of the codebase.<|eot_id|><|start_header_id|>user<|end_header_id|>\n", "\n", "Here is the code change delimited by triple backticks:\n", "```\n", "--- scratch.py\n", "+++ scratch.py\n", "@@ -32,10 +32,12 @@\n", "@class Foo:\n", "     def foo1(self):\n", "         print(\"Foo.foo1\")\n", " \n", " \n", " class Bar:\n", "+    \"\"\"The Bar class is in charge of baring.\"\"\"\n", "+\n", "     def __init__(self):\n", "         print(\"Bar.__init__\")\n", " \n", "     def bar(self):\n", "         print(\"Bar.bar\")\n", "\n", "```\n", "Directly start the response with the description and say nothing else. Write no more than 10 words.\n", "Focus on the fields that are changing but don't include where they are.\n", "<|eot_id|><|start_header_id|>assistant<|end_header_id|>\n", "'''\n", "\n", "prompt3 = '''\\\n", "<|begin_of_text|><|start_header_id|>system<|end_header_id|>\n", "\n", "You are a developer working in my codebase.\n", "Your task is to extract relevant information from the code change below and describe it using imperative verb form. The purpose of your description is to help other developers quickly understand key aspects of the change in the context of the codebase.<|eot_id|><|start_header_id|>user<|end_header_id|>\n", "\n", "Here is the code change delimited by triple backticks:\n", "```\n", "--- research/llm_apis/chat_utils.py\n", "+++ research/llm_apis/chat_utils.py\n", "@@ -207,10 +207,13 @@\n", "@class Llama3ChatClient:\n", "@    def get_prompt_token_length(\n", "         \"\"\"Returns the number of tokens in the prompt.\"\"\"\n", "         return len(self._prepare_prompt_tokens(messages, system_prompt))\n", " \n", "     def get_token_length(self, text: str) -> int:\n", "         return len(self.tokenizer.encode(text, bos=True, eos=True))\n", "+\n", "+    def __str__(self) -> str:\n", "+        return f\"LLaMA 3 {self.server_type} client\"\n", " \n", " \n", " class Llama3LlamaCppApiClient(Llama3ChatClient):\n", "     \"\"\"For backward compatibility with existing pipeline code.\"\"\"\n", " \n", "\n", "```\n", "Directly start the response with the description and say nothing else. Write no more than 10 words.\n", "Focus on the fields that are changing but don't include where they are.\n", "<|eot_id|><|start_header_id|>assistant<|end_header_id|>\n", "'''\n", "\n", "output = generate_output(prompt3)\n", "print(output)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}