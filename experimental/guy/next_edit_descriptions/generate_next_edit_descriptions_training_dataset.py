"""Take next-edit descriptions and turn them into an indexed dataset for training.

The dataset can be used to distill a smaller model to generate these descriptions.
"""

import argparse
import concurrent.futures
import random
import shutil
import time
from tqdm import tqdm
from pathlib import Path

import pandas

import megatron.data.indexed_dataset as indexed_dataset
from megatron.data.indexed_dataset import TokenSeqLike

from base.tokenizers import create_tokenizer_by_name
from base.prompt_format_next_edit.description_prompt_formatter import (
    RavenDescribePromptFormatter,
)
from base.prompt_format_chat import get_struct_to_tokens_prompt_formatter_by_name

SUCCESS_FILE = "_SUCCESS"


class DescriptionFormatter:
    def __init__(self):
        # Taken from: services/deploy/raven_edit_v2_15b_deploy.jsonnet
        tokenizer_name = "llama3_instruct"
        prompt_formatter_config = RavenDescribePromptFormatter.Config()
        chat_prompt_formatter_name = "llama3"

        self.tokenizer = create_tokenizer_by_name(tokenizer_name)
        self.description_prompt_formatter = RavenDescribePromptFormatter(
            self.tokenizer,
            config=prompt_formatter_config,
        )
        self.chat_prompt_formatter = get_struct_to_tokens_prompt_formatter_by_name(
            chat_prompt_formatter_name,
            self.tokenizer,
        )

    def format_prompt(
        self,
        description: str,
        diff: str,
        seq_len: int,
        mask_out_input_tokens: bool = True,
    ) -> list[int]:
        structured_prompt = (
            self.description_prompt_formatter.format_input_from_diff_str(diff)
        )
        tokenized_prompt = self.chat_prompt_formatter.format_prompt(
            structured_prompt
        ).tokens

        eos = self.tokenizer.special_tokens.eos

        if mask_out_input_tokens:
            tokenized_prompt = [-token for token in tokenized_prompt]

        label_tokens = self.tokenizer.tokenize_unsafe(description) + [eos]

        sample_tokens = tokenized_prompt + label_tokens
        # print(f"Raw token length: {len(sample_tokens)}")

        if len(sample_tokens) <= seq_len:
            sample_tokens += [-eos] * (seq_len - len(sample_tokens))
        else:
            print(f"WARNING: Sample truncated: {len(sample_tokens)} > {seq_len}")
            sample_tokens = sample_tokens[:seq_len]

        assert len(sample_tokens) == seq_len
        return sample_tokens


def create_indexed_dataset_batches(
    output_path: Path, all_tokens: list[TokenSeqLike], vocab_size: int
):
    builder = indexed_dataset.make_builder(
        str(output_path.with_suffix(".bin")),
        impl="mmap",
        vocab_size=vocab_size,
    )
    num_rows = 0

    for tokens in all_tokens:
        builder.add_item(tokens)
        builder.end_document()
        num_rows += 1

    builder.finalize(str(output_path.with_suffix(".idx")))

    # Rename the file to include the number of rows
    new_output_path = Path(str(output_path) + f"_numrows={num_rows}")
    output_path.with_suffix(".bin").rename(new_output_path.with_suffix(".bin"))
    output_path.with_suffix(".idx").rename(new_output_path.with_suffix(".idx"))


def _combine_indexed_datasets(
    combined_output_path: Path, input_paths: list[Path], vocab_size: int
):
    """Merge indexed datasets into a single one.

    Args:
        combined_output_path: The output path (with no .bin or .idx)
        input_paths: The input paths (can have .bin or .idx)
        vocab_size: The size of the token
    """
    if not input_paths:
        return

    input_paths = [path.with_suffix("") for path in sorted(input_paths)]
    combined_builder = indexed_dataset.make_builder(
        str(combined_output_path.with_suffix(".bin")),
        impl="mmap",
        vocab_size=vocab_size,
    )

    for path in input_paths:
        combined_builder.merge_file_(str(path.with_suffix("")))

    combined_builder.finalize(str(combined_output_path.with_suffix(".idx")))


def get_num_rows(dataset_path: Path) -> int:
    dataset = indexed_dataset.make_dataset(
        str(dataset_path.with_suffix("")), "mmap", skip_warmup=True
    )
    return len(dataset)


# class IndexedDatasetBuilder:
#     """A simple builder for the MMapIndexedDataset."""

#     def __init__(self, path: Path, vocab_size: int):
#         """
#         Args:
#             path: The path to the dataset (no .bin or .idx needed)
#             vocab_size: The size of the tokenizer vocabulary
#         """
#         self.path = path.with_suffix("")
#         self.vocab_size = vocab_size
#         self.builder = indexed_dataset.make_builder(
#             self._get_path(".bin"), impl="mmap", vocab_size=vocab_size
#         )

#     def add_item(self, item: TokenSeqLike):
#         """Add an item to the dataset."""
#         self.builder.add_item(item)
#         self.builder.end_document()

#     def finalize(self):
#         """Must be called after all rows have been added."""
#         self.builder.finalize(self._get_path(".idx"))

#     def _get_path(self, suffix: str) -> str:
#         return str(self.path.with_suffix(suffix))


def combine_indexed_dataset_batches(
    indexed_dataset_path: Path,
    batches_output_path: Path,
    num_validation_samples: int,
    vocab_size: int,
):
    # train_batch_files = []
    # validation_batch_files = []
    # num_collected_validation_samples = 0

    # for partition_file in batches_output_path.glob("*.bin"):
    #     if num_collected_validation_samples < num_validation_samples:
    #         try:
    #             num_rows = get_num_rows(partition_file)
    #         except Exception as e:  # pylint: disable=broad-except
    #             print(f"Failed to get num rows for {partition_file}: {e}")
    #             continue
    #         validation_batch_files.append(partition_file)
    #         num_collected_validation_samples += num_rows
    #     else:
    #         train_batch_files.append(partition_file)

    # _combine_indexed_datasets(
    #     combined_output_path=indexed_dataset_path / "validation_dataset",
    #     input_paths=validation_batch_files,
    #     vocab_size=vocab_size,
    # )

    # _combine_indexed_datasets(
    #     combined_output_path=indexed_dataset_path / "dataset",
    #     input_paths=train_batch_files,
    #     vocab_size=vocab_size,
    # )

    all_batch_files = list(batches_output_path.glob("*.bin"))

    # Combine all samples into a single dataset
    full_dataset_path = indexed_dataset_path / "full_dataset"
    _combine_indexed_datasets(
        combined_output_path=full_dataset_path,
        input_paths=all_batch_files,
        vocab_size=vocab_size,
    )

    # Load the full dataset
    full_dataset = indexed_dataset.make_dataset(
        str(full_dataset_path), "mmap", skip_warmup=True
    )
    assert full_dataset is not None

    # Shuffle the indices
    indices = list(range(len(full_dataset)))
    random.seed(42)
    random.shuffle(indices)

    # Split into train and validation
    validation_indices = indices[:num_validation_samples]
    train_indices = indices[num_validation_samples:]

    train_dataset_path = indexed_dataset_path / "train_dataset"
    validation_dataset_path = indexed_dataset_path / "validation_dataset"

    # Create train and validation datasets
    train_builder = indexed_dataset.make_builder(
        str(train_dataset_path.with_suffix(".bin")),
        impl="mmap",
        vocab_size=vocab_size,
    )

    validation_builder = indexed_dataset.make_builder(
        str(validation_dataset_path.with_suffix(".bin")),
        impl="mmap",
        vocab_size=vocab_size,
    )

    for idx in tqdm(train_indices, desc="Creating train dataset"):
        train_builder.add_item(full_dataset[idx])
        train_builder.end_document()
    train_builder.finalize(str(train_dataset_path.with_suffix(".idx")))
    print(f"Train dataset size: {len(train_indices)}")

    for idx in tqdm(validation_indices, desc="Creating validation dataset"):
        validation_builder.add_item(full_dataset[idx])
        validation_builder.end_document()
    validation_builder.finalize(str(validation_dataset_path.with_suffix(".idx")))
    print(f"Validation dataset size: {len(validation_indices)}")

    # Clean up the full dataset
    full_dataset_path.with_suffix(".bin").unlink()
    full_dataset_path.with_suffix(".idx").unlink()

    # Delete the batches directory
    try:
        shutil.rmtree(batches_output_path)
    except OSError:
        # NOTE: due to the potential file system issue, rm -rf may fail
        # due to `OSError: [Errno 39] Directory not empty`.
        print(f"Failed to delete {batches_output_path}, retrying...")
        time.sleep(5)
        shutil.rmtree(batches_output_path)

    # Cleanup batch folder
    if batches_output_path.exists() and batches_output_path.is_dir():
        assert (
            batches_output_path.name == "batches"
        ), f"Path does not end with 'batches': {batches_output_path}"
        shutil.rmtree(batches_output_path)

    # Declare success
    (indexed_dataset_path / SUCCESS_FILE).touch()


def process_parquet_file(
    batches_output_path: Path,
    parquet_file: Path,
    formatter: DescriptionFormatter,
    seq_len: int,
):
    try:
        output_path = batches_output_path / parquet_file.stem
        if output_path.with_suffix(".idx").exists():
            print(f"Output path {output_path} already exists, skipping")
            return

        df = pandas.read_parquet(parquet_file)
        all_tokens = []

        print(f"Creating indexed dataset from {parquet_file}")

        for description, diff in zip(
            list(df["descriptions"][0]), list(df["description_diff_inputs"][0])
        ):
            if not description.strip() or not diff.strip():
                continue
            tokens = formatter.format_prompt(description, diff, seq_len=seq_len)
            all_tokens.append(tokens)

        print("Calling create_indexed_dataset_batches")

        create_indexed_dataset_batches(
            output_path,
            all_tokens,
            formatter.tokenizer.vocab_size,
        )
    except Exception as e:  # pylint: disable=broad-except
        print(f"Failed to process {parquet_file}: {e}")


def main():
    parser = argparse.ArgumentParser(
        description="Generate next edit descriptions training dataset."
    )
    parser.add_argument(
        "--input_path",
        type=Path,
        default="/mnt/efs/spark-data/user/guy/data-pipeline/next_edit_descriptions/",
        help="Path to input parquet files",
    )
    parser.add_argument(
        "--output_path",
        type=Path,
        default="/mnt/efs/spark-data/user/guy/data-pipeline/next_edit_descriptions_indexed_dataset/",
        help="Path for output indexed dataset",
    )
    parser.add_argument(
        "--seq_len", type=int, default=4096 + 1, help="Sequence length for tokenization"
    )
    parser.add_argument(
        "--num_validation_samples",
        type=int,
        default=12800,
        help="Number of samples to use for validation",
    )
    parser.add_argument(
        "--num_workers",
        type=int,
        default=32,
        help="Number of data worker processes, or 0 to avoid multiprocessing",
    )
    args = parser.parse_args()

    indexed_dataset_output_path = args.output_path

    if indexed_dataset_output_path.exists():
        raise FileExistsError(
            f"Output path {indexed_dataset_output_path} already exists"
        )

    batches_output_path = indexed_dataset_output_path / "batches"
    batches_output_path.mkdir(parents=True, exist_ok=True)

    formatter = DescriptionFormatter()

    parquet_files = list(args.input_path.glob("*.parquet"))

    with concurrent.futures.ThreadPoolExecutor(
        max_workers=args.num_workers
    ) as executor:
        futures = [
            executor.submit(
                process_parquet_file,
                batches_output_path=batches_output_path,
                parquet_file=parquet_file,
                formatter=formatter,
                seq_len=args.seq_len,
            )
            for parquet_file in parquet_files
        ]

        for _ in tqdm(
            concurrent.futures.as_completed(futures),
            total=len(parquet_files),
            desc="Processing parquet files",
        ):
            pass

    combine_indexed_dataset_batches(
        indexed_dataset_output_path,
        batches_output_path,
        num_validation_samples=args.num_validation_samples,
        vocab_size=formatter.tokenizer.vocab_size,
    )


if __name__ == "__main__":
    main()
