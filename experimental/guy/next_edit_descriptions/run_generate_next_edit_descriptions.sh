#!/bin/bash

AUGMENT_SRC_PATH=/home/<USER>/augment

time python3 generate_next_edit_descriptions.py \
    --address_yaml_file ${AUGMENT_SRC_PATH}/experimental/guy/apis/triton_server_addresses.yaml \
    --input_path /mnt/efs/spark-data/shared/next-edit/stage1/prv2-pr_grouped_10k/S24_10000p \
    --output_path /mnt/efs/spark-data/user/guy/data-pipeline/next_edit_descriptions_v3_randomctx_shortdesc_onlya100 \
    --write_every 1 \
    $* |& tee /tmp/next_edit_descriptions_log.txt
