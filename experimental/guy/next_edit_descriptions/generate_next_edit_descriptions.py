"""Generate PR instructions with Triton servers."""

import argparse
import random
from pathlib import Path
import pandas
import pickle
import traceback

from typing import Iterable
from research.model_server.next_edits_handlers import (
    NextEditResult,
    _split_changes_into_hunks,
)

from base.diff_utils.diff_utils import File, compute_file_diff
from research.next_edits.edit_gen_sampler import EditGenProblem

from experimental.guy.apis.process_chat_tasks import (
    process_tasks,
    TaskProcessorInput,
    load_addresses_from_yaml,
)
from research.llm_apis.chat_utils import (
    Llama3ChatClient,
    ChatClient,
    run_health_checks,
)
from experimental.guy.pr_task_descriptions.processify import processify


def get_logging_header(thread_idx: int, task_idx: int) -> str:
    return f"[{thread_idx:03d} task={task_idx:04d}]"


def compute_suggested_edit_diff(problem: EditGenProblem, num_context_lines: int) -> str:
    current_code = problem.current_code
    new_code = problem.prefix + problem.output.replacement + problem.suffix
    diff = compute_file_diff(
        before_file=File(path=str(problem.current_path), contents=current_code),
        after_file=File(path=str(problem.current_path), contents=new_code),
        use_smart_header=True,
        num_context_lines=num_context_lines,
    )
    return diff


def compute_suggested_edit_diff_with_split_hunks(
    problem: EditGenProblem, split_hunk_line_distance: int = 3, n_context_lines: int = 5
) -> Iterable[str]:
    """Compute the diff between the current code and the new code.

    Args:
        problem: the EditGenProblem to compute the diff for
        split_hunk_line_distance: the number of lines between hunks
        n_context_lines: the number of lines of context to include in the diff

    Returns:
        an iterable of strings, each string is a diff hunk
    """
    if not problem.output.changed:
        return

    assert (
        split_hunk_line_distance <= n_context_lines
    ), "must have split_hunk_line_distance <= n_context_lines"

    current_code = problem.current_code
    new_code = problem.prefix + problem.output.replacement + problem.suffix

    # construct a NextEditResult to feed into _split_changes_into_hunks
    constructed_result = NextEditResult(
        suggestion_id="",
        path=str(problem.current_path),
        blob_name="",
        char_start=problem.edit_region.start,
        char_end=problem.edit_region.stop,
        existing_code=current_code,
        suggested_code=new_code,
        truncation_char=None,
        change_description="",
        diff_spans=[],  # these are empty until _split_changes_into_hunks fills them in
    )

    for next_edit_result in _split_changes_into_hunks(
        constructed_result, n_context_lines=split_hunk_line_distance
    ):
        # construct the after file to feed into compute_file_diff
        prefix = current_code[: next_edit_result.char_start]
        replacement = next_edit_result.suggested_code
        suffix = current_code[next_edit_result.char_end :]
        after_code = prefix + replacement + suffix

        yield compute_file_diff(
            before_file=File(path=str(next_edit_result.path), contents=current_code),
            after_file=File(path=str(next_edit_result.path), contents=after_code),
            use_smart_header=True,
            num_context_lines=n_context_lines,
        )


# prompt_template = """\
# You are a developer working in my codebase.
# Your task is to extract relevant information from the code change below \
# and describe it using imperative verb form. The purpose of your description is \
# to help other developers quickly understand key aspects of the change in the context \
# of the codebase.

# Directly start the response with the description and say nothing else.
# Write no more than 10 words.
# Focus on the fields that are changing but don't include where they are.

# Code change:
# ```
# [DIFF_STR]```
# """


prompt_template = """\
You are a developer working in my codebase.
Your task is to extract relevant information from the code change below \
and describe it using imperative verb form. The purpose of your description is \
to help other developers quickly understand key aspects of the change in the context \
of the codebase.

Directly start the response with the description and say nothing else.
Write a short description and no more than 10 words.
Focus on the fields that are changing but don't include where they are.


Code change:
```
--- src/org/ensembl/healthcheck/testcase/generic/CheckDeclarations.java
+++ src/org/ensembl/healthcheck/testcase/generic/CheckDeclarations.java
@@ -94,47 +94,47 @@
@public class CheckDeclarations extends SingleDatabaseTestCase {
@        public boolean run(final DatabaseRegistryEntry dbre) {

                 Connection previousCon = sec.getConnection();

                 result &= checkAssembly(dbre, sec);

                 result &= checkRepeats(dbre, sec);

                 result &= checkGenes(dbre, sec);

                 return result;
         }

   private boolean checkAssembly(DatabaseRegistryEntry dbre, DatabaseRegistryEntry sec) {

     boolean result = true;

     Connection con = dbre.getConnection();
     Connection previousCon = sec.getConnection();

     String sql = "CHECKSUM table assembly";
     int currentAssembly = DBUtils.getRowCount(con, sql);
     int previousAssembly = DBUtils.getRowCount(previousCon, sql);

-    if (previousAssembly != currentAssembly)) {
+    if (previousAssembly != currentAssembly) {
       boolean declared = checkDeclaration(dbre, "assembly");
       if (!declared) {
         ReportManager.problem(this, con, "Assembly has changed but has not been declared");
         result = false;
       }
     }

     return result;
   }
```

Response:
Fix typo

Code change:
```
--- .github/workflows/release.yml
+++ .github/workflows/release.yml
@@ -17,12 +17,12 @@
@jobs:
@  release:
@    - uses: actions/setup-java@v4.0.0
       with:
         distribution: 'temurin'
         java-version: '8'
     - name: Coursier cache
       uses: coursier/cache-action@v6
-    - name: sbt ci-release-cont ${{ github.ref }}
-      run: ./sbt ci-release-cont
+    - name: sbt ci-release ${{ github.ref }}
+      run: ./sbt ci-release
       env:
         PGP_PASSPHRASE: ${{ secrets.PGP_PASSPHRASE }}
         PGP_SECRET: ${{ secrets.PGP_SECRET }}
         SONATYPE_PASSWORD: ${{ secrets.SONATYPE_PASSWORD }}
         SONATYPE_USERNAME: ${{ secrets.SONATYPE_USERNAME }}
```

Response:
Rename sbt task to ci-release

Code change:
```
--- packages/server-admin-ui/src/views/ServerConfig/BasicProvider.js
+++ packages/server-admin-ui/src/views/ServerConfig/BasicProvider.js
@@ -147,13 +147,15 @@
@class LoggingInput extends Component {
     )
   }
 }

 class ValidateChecksumInput extends Component {
-  constructor(props) {
+  constructor (props) {
     super(props)
-    this.props.value.validateChecksum = typeof this.props.value.validateChecksum === 'undefined' || this.props.value.validateChecksum
+    this.props.value.validateChecksum =
+      typeof this.props.value.validateChecksum === 'undefined' ||
+      this.props.value.validateChecksum
   }
   render () {
     return (
       <FormGroup row>
         <Col xs='3' md='2'>
```

Response:
Improve formatting

Code change:
```
[DIFF_STR]```
"""


def process_row(j, row, client, split_hunks: bool):
    data = pickle.loads(row["pickled_results"])

    description_diff_inputs: list[str] = []
    descriptions: list[str] = []
    description_indices: list[int] = []

    def process_diff(diff):
        if not diff.strip():
            # print("Empty diff, skipping")
            return None

        prompt = prompt_template.replace("[DIFF_STR]", diff)
        try:
            response = client.generate(messages=[prompt], max_tokens=256)
        except Exception as e:  # pylint: disable=broad-except
            print(
                f"client.generate() call failed with {e}, prompt length: {len(prompt)}"
            )
            # random_num = random.randint(0, 1000000)
            # prompt_output_filename = Path(
            #     f"/tmp/next-edit-description-failed-prompt-{random_num:06d}.txt"
            # )
            # prompt_output_filename.write_text(prompt)
            # with open(prompt_output_filename, "w") as f:
            #     f.write(prompt)
            raise

        if response.strip().count("\n") > 0:
            print("Skipping multi-line response:")
            print(response)
            return None

        if response.endswith(".") and response.count(".") == 1:
            response = response.replace(".", "")

        # print("Generated description:", response)

        return diff, response

    for i, change in enumerate(data):
        try:
            split_hunk_line_distance = 3

            # We don't do random.randint(3, 30) because that is biased toward
            # the larger values
            num_context_lines = random.sample([3, 4, 5, 6, 7, 8, 9, 10, 30], 1)[0]

            if split_hunks:
                diffs = compute_suggested_edit_diff_with_split_hunks(
                    change, split_hunk_line_distance, num_context_lines
                )
            else:
                diff = compute_suggested_edit_diff(change, num_context_lines)
                diffs = [diff]

            for diff in diffs:
                result = process_diff(diff)
                if result:
                    diff, response = result
                    description_diff_inputs.append(diff)
                    descriptions.append(response)
                    description_indices.append(i)
        except Exception as e:  # pylint: disable=broad-except
            print(f"Failed to process change {j} in row {i}: {e}")
            traceback.print_exc()

    # If one row item fails, the whole row fails, so the generated descriptions
    # are always aligned with the pickled results
    assert len(description_diff_inputs) == len(descriptions) == len(description_indices)

    processed_row = {
        **row,
        "descriptions": descriptions,
        "description_diff_inputs": description_diff_inputs,
        "description_indices": description_indices,
    }

    return processed_row


@processify
def process_parquet_file(
    filename,
    thread_idx: int,
    task_idx: int,
    chat_client: ChatClient,
    output_file: Path,
    skip_repo_names: list[str],
    max_input_length: int,
    split_hunks: bool,
    write_every: int = 50,
) -> None:
    """Process a parquet file with PR data and generate instructions."""
    header = get_logging_header(thread_idx, task_idx)
    try:
        print(f"{header} Processing {filename}")
        partial_output_file = output_file.with_suffix(filename.suffix + ".partial")

        df = pandas.read_parquet(filename)
        print(f"{header} Loaded {filename}")

        results = []

        # processing_times = []
        num_samples = len(df)
        num_samples_since_last_write = 0

        for j, row in enumerate(df.to_dict(orient="records")):
            try:
                print(f"{header} Processing sample {j+1}/{num_samples}")

                processed_row = process_row(
                    j, row, chat_client, split_hunks=split_hunks
                )

                # Keep all the fields from the original PR, and add the generated instructions
                results.append(processed_row)

                print(f"{header} Processed sample {j+1}/{num_samples}")

                num_samples_since_last_write += 1
                if num_samples_since_last_write >= write_every:
                    print(
                        f"{header} Writing partial file {partial_output_file} with {len(results)} samples"
                    )
                    pandas.DataFrame(results).to_parquet(partial_output_file)
                    num_samples_since_last_write = 0
            except Exception as e:  # pylint: disable=broad-except
                print(f"{header} Failed to process row {j} in {filename}: {e}")
                traceback.print_exc()

        if len(results) > 0:
            pandas.DataFrame(results).to_parquet(partial_output_file)
            print(
                f"{header} Processing of {filename} completed, renaming partial file {partial_output_file} to {output_file}"
            )
            partial_output_file.rename(output_file)
        else:
            print(f"{header} No results for {filename}, will not write parquet file")
            if partial_output_file.exists():
                partial_output_file.unlink()

    except Exception as e:  # pylint: disable=broad-except
        print(f"{header} Failed to process task: {e}")
        traceback.print_exc()


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--address",
        type=str,
        default="",
        help="The chat server addresses, comma separated. Example: ********:8000,********:9000",
    )
    parser.add_argument(
        "--address_yaml_file",
        type=Path,
        help="A yaml file that contains server addresses. See docstring for example.",
    )
    parser.add_argument(
        "--max_input_length",
        type=int,
        default=8192,
        help="Max number of tokens in sample prompts.",
    )
    parser.add_argument(
        "--input_path",
        type=str,
        required=True,
        help="The input path to the parquet files.",
    )
    parser.add_argument(
        "--output_path",
        type=str,
        required=True,
        help="Directory in which to save the parquet files.",
    )
    parser.add_argument(
        "--server_request_timeout_secs",
        type=int,
        default=180,
        help="Max number of tokens in sample prompts.",
    )
    parser.add_argument(
        "--skip_health_check",
        action="store_true",
        help="Skip health checks for chat servers",
    )
    parser.add_argument(
        "--split_hunks",
        action="store_true",
        help="Split hunks",
    )
    parser.add_argument(
        "--write_every",
        type=int,
        default=1,
        help="Write every N samples to disk.",
    )
    args = parser.parse_args()

    print("Split hunks:", args.split_hunks)

    input_path = Path(args.input_path)
    output_path = Path(args.output_path)
    output_path.mkdir(parents=True, exist_ok=True)
    skip_repo_names = ["NixOS/nixpkgs", "microsoft/AzureTRE"]

    if args.address and args.address_yaml_file:
        raise ValueError("Must specify either --address or --address_yaml_file")
    elif args.address:
        addresses = args.address.split(",")
    else:
        addresses = load_addresses_from_yaml(args.address_yaml_file)

    if len(addresses) == 0:
        raise ValueError("Must specify --address or --address_yaml_file")

    if not args.skip_health_check:
        run_health_checks(addresses)

    chat_clients: list[ChatClient] = [
        Llama3ChatClient(
            "triton", address=address, timeout=args.server_request_timeout_secs
        )
        for address in addresses
    ]

    def process_task(task_processor_input: TaskProcessorInput):
        filename = task_processor_input.task_data
        output_file = output_path / filename.name
        header = get_logging_header(
            task_processor_input.thread_idx, task_processor_input.task_idx
        )
        if output_file.exists():
            print(
                f"{header} File {filename} was already processed in {output_file}, skipping"
            )
            return
        process_parquet_file(
            filename=filename,
            thread_idx=task_processor_input.thread_idx,
            task_idx=task_processor_input.task_idx,
            chat_client=task_processor_input.chat_client,
            output_file=output_file,
            skip_repo_names=skip_repo_names,
            max_input_length=args.max_input_length,
            split_hunks=args.split_hunks,
            write_every=args.write_every,
        )
        if not output_file.exists():
            print(f"{header} Failed to process {filename}")

    # Process larger files first. This is to avoid waiting for a single large file
    # to get processed on a single server at the end of the pipeline.
    tasks = sorted(
        list(input_path.glob("*.parquet")), key=lambda x: x.stat().st_size, reverse=True
    )
    process_tasks(
        chat_clients=chat_clients,
        tasks=tasks,
        task_processor_fn=process_task,
        per_thread_init_delay=2,
    )
    print("Task processing complete.")


if __name__ == "__main__":
    main()
