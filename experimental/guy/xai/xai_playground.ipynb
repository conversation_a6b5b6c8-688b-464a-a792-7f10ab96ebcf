{"cells": [{"cell_type": "code", "execution_count": 15, "id": "d37681ee", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Message(id='msg_016edZSpvc5wxzH4c1hESe4b', content=[TextBlock(citations=None, text=\"To answer your question about the weather in the capital of France (Paris), I'll need to use the weather tool to get that information.\", type='text'), ToolUseBlock(id='toolu_01Scgtgw84mCghvosKQU7XYc', input={'location': 'Paris', 'unit': 'celsius'}, name='get_current_weather', type='tool_use')], model='claude-3-7-sonnet-20250219', role='assistant', stop_reason='tool_use', stop_sequence=None, type='message', usage=Usage(cache_creation_input_tokens=0, cache_read_input_tokens=0, input_tokens=427, output_tokens=101, service_tier='priority'))\n"]}], "source": ["# call anthropic client with simple messages and tools\n", "\n", "from pathlib import Path\n", "import anthropic\n", "\n", "api_key = (\n", "    Path(\"/home/<USER>/.augment/anthropic_api_token\").read_text(encoding=\"utf8\").strip()\n", ")\n", "client = anthropic.Anthropic(api_key=api_key)\n", "\n", "messages = [\n", "    # {\"role\": \"system\", \"content\": \"You are a helpful assistant.\"},\n", "    {\"role\": \"user\", \"content\": \"What is the weather in the capital of France?\"},\n", "]\n", "\n", "anthropic_tools = [\n", "    {\n", "        \"name\": \"get_current_weather\",\n", "        \"description\": \"Get the current weather in a given location\",\n", "        \"input_schema\": {\n", "            \"type\": \"object\",\n", "            \"properties\": {\n", "                \"location\": {\n", "                    \"type\": \"string\",\n", "                    \"description\": \"The location to get the weather for\",\n", "                },\n", "                \"unit\": {\n", "                    \"type\": \"string\",\n", "                    \"enum\": [\"celsius\", \"fahrenheit\"],\n", "                },\n", "            },\n", "            \"required\": [\"location\"],\n", "        },\n", "    },\n", "]\n", "\n", "response = client.messages.create(\n", "    model=\"claude-3-7-sonnet-latest\",\n", "    system=\"You are a helpful assistant.\",\n", "    messages=messages,\n", "    tools=anthropic_tools,\n", "    max_tokens=1024,\n", ")\n", "\n", "print(response)"]}, {"cell_type": "code", "execution_count": 16, "id": "68021353", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'id': 'msg_016edZSpvc5wxzH4c1hESe4b',\n", " 'content': [{'citations': None,\n", "   'text': \"To answer your question about the weather in the capital of France (Paris), I'll need to use the weather tool to get that information.\",\n", "   'type': 'text'},\n", "  {'id': 'toolu_01Scgtgw84mCghvosKQU7XYc',\n", "   'input': {'location': 'Paris', 'unit': 'celsius'},\n", "   'name': 'get_current_weather',\n", "   'type': 'tool_use'}],\n", " 'model': 'claude-3-7-sonnet-20250219',\n", " 'role': 'assistant',\n", " 'stop_reason': 'tool_use',\n", " 'stop_sequence': None,\n", " 'type': 'message',\n", " 'usage': {'cache_creation_input_tokens': 0,\n", "  'cache_read_input_tokens': 0,\n", "  'input_tokens': 427,\n", "  'output_tokens': 101,\n", "  'service_tier': 'priority'}}"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["response.model_dump()"]}, {"cell_type": "code", "execution_count": null, "id": "34568c70", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ChatCompletion(id='chatcmpl-Bl6EDpn4433RZkcgzH7QmBLVwNRkZ', choices=[Choice(finish_reason='tool_calls', index=0, logprobs=None, message=ChatCompletionMessage(content=None, refusal=None, role='assistant', audio=None, function_call=None, tool_calls=[ChatCompletionMessageToolCall(id='call_B9sgML5ZgvmVxtGCx3Kembu0', function=Function(arguments='{\"location\":\"Paris\"}', name='get_current_weather'), type='function')], annotations=[]))], created=1750565057, model='gpt-4.1-2025-04-14', object='chat.completion', service_tier='default', system_fingerprint='fp_51e1070cf2', usage=CompletionUsage(completion_tokens=15, prompt_tokens=83, total_tokens=98, completion_tokens_details=CompletionTokensDetails(accepted_prediction_tokens=0, audio_tokens=0, reasoning_tokens=0, rejected_prediction_tokens=0), prompt_tokens_details=PromptTokensDetails(audio_tokens=0, cached_tokens=0)))\n", "ChatCompletionMessage(content=None, refusal=None, role='assistant', audio=None, function_call=None, tool_calls=[ChatCompletionMessageToolCall(id='call_B9sgML5ZgvmVxtGCx3Kembu0', function=Function(arguments='{\"location\":\"Paris\"}', name='get_current_weather'), type='function')], annotations=[])\n", "[ChatCompletionMessageToolCall(id='call_B9sgML5ZgvmVxtGCx3Kembu0', function=Function(arguments='{\"location\":\"Paris\"}', name='get_current_weather'), type='function')]\n"]}], "source": ["from pathlib import Path\n", "import openai\n", "\n", "from research.environments import get_eng_secret\n", "\n", "# xAI\n", "# api_key = (\n", "#     Path(\"/home/<USER>/.augment/xai_api_token\").read_text(encoding=\"utf8\").strip()\n", "# )\n", "# base_url = \"https://api.x.ai/v1\"\n", "# model_name = \"grok-3-5-code\"\n", "\n", "# OpenAI\n", "api_key = get_eng_secret(\"research-openai-key\")\n", "base_url = None\n", "model_name = \"gpt-4.1-2025-04-14\"\n", "\n", "client = openai.OpenAI(\n", "    api_key=api_key,\n", "    max_retries=1,\n", "    base_url=base_url,\n", ")\n", "\n", "messages = [\n", "    {\"role\": \"system\", \"content\": \"You are a helpful assistant.\"},\n", "    {\"role\": \"user\", \"content\": \"What is the weather in the capital of France?\"},\n", "]\n", "\n", "openai_tools = [\n", "    {\n", "        \"type\": \"function\",\n", "        \"function\": {\n", "            \"name\": \"get_current_weather\",\n", "            \"description\": \"Get the current weather in a given location\",\n", "            \"parameters\": {\n", "                \"type\": \"object\",\n", "                \"properties\": {\n", "                    \"location\": {\n", "                        \"type\": \"string\",\n", "                        \"description\": \"The location to get the weather for\",\n", "                    },\n", "                    \"unit\": {\n", "                        \"type\": \"string\",\n", "                        \"enum\": [\"celsius\", \"fahrenheit\"],\n", "                    },\n", "                },\n", "                \"required\": [\"location\"],\n", "            },\n", "        },\n", "    },\n", "]\n", "\n", "response = client.chat.completions.create(  # type: ignore\n", "    model=model_name,\n", "    messages=messages,  # type: ignore\n", "    temperature=0.0,\n", "    tools=openai_tools,  # type: ignore\n", "    # tool_choice=tool_choice_param,  # type: ignore\n", "    max_tokens=1024,\n", "    # extra_body=extra_body,\n", ")\n", "\n", "print(type(response))\n", "print(response)\n", "\n", "message = response.choices[0].message\n", "tool_calls = message.tool_calls\n", "\n", "messages.append(\n", "    {\"role\": \"assistant\", \"content\": message.content, \"tool_calls\": tool_calls}\n", ")\n", "\n", "# print(message)\n", "# print(tool_calls)\n", "\n", "# tool_result = \"The temperature is 20 degrees Celsius.\""]}, {"cell_type": "code", "execution_count": 10, "id": "8866d3bb", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "  \"id\": \"chatcmpl-Bl6EDpn4433RZkcgzH7QmBLVwNRkZ\",\n", "  \"choices\": [\n", "    {\n", "      \"finish_reason\": \"tool_calls\",\n", "      \"index\": 0,\n", "      \"logprobs\": null,\n", "      \"message\": {\n", "        \"content\": null,\n", "        \"refusal\": null,\n", "        \"role\": \"assistant\",\n", "        \"audio\": null,\n", "        \"function_call\": null,\n", "        \"tool_calls\": [\n", "          {\n", "            \"id\": \"call_B9sgML5ZgvmVxtGCx3Kembu0\",\n", "            \"function\": {\n", "              \"arguments\": \"{\\\"location\\\":\\\"Paris\\\"}\",\n", "              \"name\": \"get_current_weather\"\n", "            },\n", "            \"type\": \"function\"\n", "          }\n", "        ],\n", "        \"annotations\": []\n", "      }\n", "    }\n", "  ],\n", "  \"created\": 1750565057,\n", "  \"model\": \"gpt-4.1-2025-04-14\",\n", "  \"object\": \"chat.completion\",\n", "  \"service_tier\": \"default\",\n", "  \"system_fingerprint\": \"fp_51e1070cf2\",\n", "  \"usage\": {\n", "    \"completion_tokens\": 15,\n", "    \"prompt_tokens\": 83,\n", "    \"total_tokens\": 98,\n", "    \"completion_tokens_details\": {\n", "      \"accepted_prediction_tokens\": 0,\n", "      \"audio_tokens\": 0,\n", "      \"reasoning_tokens\": 0,\n", "      \"rejected_prediction_tokens\": 0\n", "    },\n", "    \"prompt_tokens_details\": {\n", "      \"audio_tokens\": 0,\n", "      \"cached_tokens\": 0\n", "    }\n", "  }\n", "}\n"]}], "source": ["\n", "# print(type(response))\n", "# print(dir(response))\n", "import json\n", "print(json.dumps(response.model_dump(), indent=2))"]}, {"cell_type": "code", "execution_count": null, "id": "3410fbbb", "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "from research.llm_apis.llm_client import OpenAIDirectClient, TextPrompt\n", "\n", "oai_client = OpenAIDirectClient(\n", "    model_name=\"gpt-4.1-2025-04-14\",\n", ")\n", "oai_response = oai_client.generate(\n", "    [[TextPrompt(\"hello\")]],\n", "    max_tokens=1024,\n", ")\n", "print(\"OpenAI:\")\n", "print(oai_response)\n", "\n", "xai_api_key = (\n", "    Path(\"/home/<USER>/.augment/xai_api_token\").read_text(encoding=\"utf8\").strip()\n", ")\n", "xai_client = OpenAIDirectClient(\n", "    api_key=xai_api_key,\n", "    base_url=\"https://research-models.api.x.ai/research/swe\",\n", "    model_name=\"swe-v6-checkpoint-05-23\",\n", ")\n", "xai_response = xai_client.generate(\n", "    [[TextPrompt(\"hello\")]],\n", "    max_tokens=1024,\n", ")\n", "print(\"xAI:\")\n", "print(xai_response)"]}, {"cell_type": "code", "execution_count": null, "id": "982a89da", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 5}