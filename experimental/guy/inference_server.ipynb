{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'fwd_starcoder' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[1], line 17\u001b[0m\n\u001b[1;32m     14\u001b[0m         context_tokens\u001b[39m.\u001b[39mappend(next_token)\n\u001b[1;32m     15\u001b[0m     \u001b[39mreturn\u001b[39;00m context_tokens\n\u001b[0;32m---> 17\u001b[0m \u001b[39mprint\u001b[39m(my_generate([\u001b[39m1\u001b[39;49m, \u001b[39m2\u001b[39;49m, \u001b[39m3\u001b[39;49m], \u001b[39m10\u001b[39;49m))\n", "Cell \u001b[0;32mIn[1], line 2\u001b[0m, in \u001b[0;36mmy_generate\u001b[0;34m(context_tokens, max_generated_tokens)\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[39mdef\u001b[39;00m \u001b[39mmy_generate\u001b[39m(context_tokens: \u001b[39mlist\u001b[39m[\u001b[39mint\u001b[39m], max_generated_tokens: \u001b[39mint\u001b[39m):\n\u001b[0;32m----> 2\u001b[0m     step_fn, attn_gen \u001b[39m=\u001b[39m fwd_starcoder\u001b[39m.\u001b[39mgenerate_step_fn(model_spec)\n\u001b[1;32m      3\u001b[0m     attn_cache \u001b[39m=\u001b[39m attn_gen(\u001b[39mlen\u001b[39m(context_tokens) \u001b[39m+\u001b[39m max_generated_tokens)\n\u001b[1;32m      5\u001b[0m     \u001b[39m# Process the context, generate the first token\u001b[39;00m\n", "\u001b[0;31mNameError\u001b[0m: name 'fwd_starcoder' is not defined"]}], "source": ["def my_generate(context_tokens: list[int], max_generated_tokens: int):\n", "    step_fn, attn_gen = fwd_starcoder.generate_step_fn(model_spec)\n", "    attn_cache = attn_gen(len(context_tokens) + max_generated_tokens)\n", "\n", "    # Process the context, generate the first token\n", "    scores = step_fn(context_tokens, attn_cache)\n", "    next_token = torch.argmax(scores)\n", "    context_tokens.append(next_token)\n", "\n", "    # Decode\n", "    for _ in range(max_generated_tokens):\n", "        scores = step_fn(tokens=[next_token], attn=attn_cache)\n", "        next_token = torch.argmax(scores)\n", "        context_tokens.append(next_token)\n", "    return context_tokens\n", "\n", "print(my_generate([1, 2, 3], 10))"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"ename": "ModuleNotFoundError", "evalue": "No module named 'services'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mModuleNotFoundError\u001b[0m                       <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[1], line 26\u001b[0m\n\u001b[1;32m     19\u001b[0m \u001b[39m# TODO(guy) tear this out once we have flash attention working in research.\u001b[39;00m\n\u001b[1;32m     20\u001b[0m \u001b[39m# We don't have flash attention installed in research, so we need to mock it out.\u001b[39;00m\n\u001b[1;32m     21\u001b[0m \u001b[39m# This makes \"from third_party.flash_attention import flash_attn\" work as a noop.\u001b[39;00m\n\u001b[1;32m     22\u001b[0m \u001b[39m# Then we turn off flash attention after importing cached_attention.\u001b[39;00m\n\u001b[1;32m     23\u001b[0m sys\u001b[39m.\u001b[39mpath\u001b[39m.\u001b[39mappend(\n\u001b[1;32m     24\u001b[0m     \u001b[39mf\u001b[39m\u001b[39m\"\u001b[39m\u001b[39m{\u001b[39;00mos\u001b[39m.\u001b[39menviron[\u001b[39m'\u001b[39m\u001b[39mAUGMENT_SRC_PATH\u001b[39m\u001b[39m'\u001b[39m]\u001b[39m}\u001b[39;00m\u001b[39m/research/mock/\u001b[39m\u001b[39m\"\u001b[39m)\n\u001b[0;32m---> 26\u001b[0m \u001b[39mimport\u001b[39;00m \u001b[39mcached_attention\u001b[39;00m\n\u001b[1;32m     27\u001b[0m cached_attention\u001b[39m.\u001b[39mUSE_FLASH \u001b[39m=\u001b[39m \u001b[39mFalse\u001b[39;00m\n\u001b[1;32m     29\u001b[0m \u001b[39mimport\u001b[39;00m \u001b[39mfwd_starcoder\u001b[39;00m\n", "File \u001b[0;32m~/augment/services/inference_host/server/continuous_batching/cached_attention.py:9\u001b[0m\n\u001b[1;32m      5\u001b[0m \u001b[39mfrom\u001b[39;00m \u001b[39mtyping\u001b[39;00m \u001b[39mimport\u001b[39;00m List, Tuple\n\u001b[1;32m      7\u001b[0m \u001b[39mimport\u001b[39;00m \u001b[39mtorch\u001b[39;00m\n\u001b[0;32m----> 9\u001b[0m \u001b[39mfrom\u001b[39;00m \u001b[39mservices\u001b[39;00m\u001b[39m.\u001b[39;00m\u001b[39minference_host\u001b[39;00m\u001b[39m.\u001b[39;00m\u001b[39mserver\u001b[39;00m\u001b[39m.\u001b[39;00m\u001b[39mcontinuous_batching\u001b[39;00m \u001b[39mimport\u001b[39;00m positional_embeddings\n\u001b[1;32m     10\u001b[0m \u001b[39mfrom\u001b[39;00m \u001b[39mthird_party\u001b[39;00m\u001b[39m.\u001b[39;00m\u001b[39mflash_attention\u001b[39;00m \u001b[39mimport\u001b[39;00m flash_attn\n\u001b[1;32m     12\u001b[0m USE_FLASH \u001b[39m=\u001b[39m \u001b[39mTrue\u001b[39;00m\n", "\u001b[0;31mModuleNotFoundError\u001b[0m: No module named 'services'"]}], "source": ["import yaml\n", "import torch\n", "import os\n", "import sys\n", "from pathlib import Path\n", "from megatron.tokenizer.tokenizer import get_tokenizer\n", "from typing import Tuple\n", "\n", "# sys.path.append(os.environ[\"HOME\"])\n", "# sys.path.append(os.environ[\"HOME\"] + \"/augment\")\n", "# import fwd_starcoder\n", "# from fwd import ModelSpec\n", "\n", "############ Inference host imports ##########################################\n", "\n", "sys.path.append(\n", "    f\"{os.environ['AUGMENT_SRC_PATH']}/services/inference_host/server/continuous_batching\")\n", "\n", "# TOD<PERSON>(guy) tear this out once we have flash attention working in research.\n", "# We don't have flash attention installed in research, so we need to mock it out.\n", "# This makes \"from third_party.flash_attention import flash_attn\" work as a noop.\n", "# Then we turn off flash attention after importing cached_attention.\n", "sys.path.append(\n", "    f\"{os.environ['AUGMENT_SRC_PATH']}/research/mock/\")\n", "\n", "import cached_attention\n", "cached_attention.USE_FLASH = False\n", "\n", "import fwd_starcoder\n", "from fwd import ModelSpec\n", "\n", "##############################################################################\n", "\n", "# model_path = Path(\"/mnt/efs/augment/checkpoints/starcoderbase-1b_neox\")\n", "# checkpoint_path = model_path / \"checkpoint\"\n", "\n", "model_path = Path(\"/mnt/efs/augment/checkpoints/starcoderbase_neox\")\n", "checkpoint_path = model_path / \"checkpoint-mps1\"\n", "\n", "model_config = yaml.safe_load(open(model_path / \"config.yml\"))\n", "\n", "model_spec = ModelSpec(\n", "    checkpoint_path=str(checkpoint_path),\n", "    num_layers=model_config[\"num-layers\"],\n", "    vocab_size=model_config[\"make_vocab_size_divisible_by\"],\n", "    emb_dim=model_config[\"hidden-size\"],\n", "    num_heads=model_config[\"num-attention-heads\"],\n", "    head_dim=model_config[\"hidden-size\"] // model_config[\"num-attention-heads\"],\n", "    rotary_pct=0,\n", ")\n", "\n", "\n", "def sample(scores: torch.Tensor) -> int:\n", "    # assert len(scores.shape) == 2\n", "    return int(torch.argmax(scores[-1]).cpu().item())\n", "\n", "\n", "import time\n", "    \n", "def load_inference_model(model_spec: ModelSpec):\n", "    start = time.time()\n", "    model = fwd_starcoder.generate_step_fn(model_spec, auto_capture_graphs=False)\n", "    print(f\"Loaded the model in {time.time()-start:.2f} seconds\")\n", "    return model\n", "\n", "\n", "def my_generate(model, context_tokens: list[int], max_generated_tokens: int):\n", "    step_fn, attn_gen = model\n", "    attn_cache = attn_gen(len(context_tokens) + max_generated_tokens)\n", "    generated_tokens = []\n", "\n", "    # Process the context, generate the first token\n", "    start = time.time()\n", "    scores = step_fn(context_tokens, attn_cache)\n", "    next_token = sample(scores)\n", "    generated_tokens.append(next_token)\n", "    print(f\"Processed the context in {time.time()-start:.2f} seconds\")\n", "\n", "    # Decode\n", "    start = time.time()\n", "    for _ in range(max_generated_tokens):\n", "        scores = step_fn([next_token], attn_cache)\n", "        next_token = sample(scores)\n", "        generated_tokens.append(next_token)\n", "    elapsed = time.time() - start\n", "    print(f\"Generation took {elapsed:.2f} seconds, or {elapsed / max_generated_tokens:.3f} seconds/token\")\n", "    return generated_tokens\n", "\n", "model = load_inference_model(model_spec)\n"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'get_tokenizer' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[1], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m tokenizer \u001b[39m=\u001b[39m get_tokenizer(\u001b[39m\"\u001b[39m\u001b[39mStarCoderTokenizer\u001b[39m\u001b[39m\"\u001b[39m)\n\u001b[1;32m      2\u001b[0m tokens \u001b[39m=\u001b[39m tokenizer\u001b[39m.\u001b[39mtokenize(\u001b[39m\"\u001b[39m\u001b[39mdef hello_world():\u001b[39m\u001b[39m\"\u001b[39m)\n\u001b[1;32m      4\u001b[0m result_tokens \u001b[39m=\u001b[39m my_generate(model, tokens, \u001b[39m50\u001b[39m)\n", "\u001b[0;31mNameError\u001b[0m: name 'get_tokenizer' is not defined"]}], "source": ["tokenizer = get_tokenizer(\"StarCoderTokenizer\")\n", "tokens = tokenizer.tokenize(\"def hello_world():\")\n", "\n", "result_tokens = my_generate(model, tokens, 50)\n", "result = tokenizer.detokenize(result_tokens)\n", "print(result)\n", "\n", "# result_tokens = my_generate(model, tokens, 500)\n", "# result_tokens = my_generate(model, tokens, 500)\n", "# result_tokens = my_generate(model, tokens, 500)\n", "# result = tokenizer.detokenize(result_tokens)\n", "# print(result)"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "    return \"Hello World!\"\n", "\n", "@app.route(\"/hello/<name>\")\n", "def hello_name(name):\n", "    return \"Hello \" + name\n"]}], "source": ["from research.models.inference_host_model import InferenceHostModel\n", "from research.models.starcoder_models import StarCoderBase1B_InferenceHost, StarCoderBase16B_InferenceHost\n", "from research.core.model_input import ModelInput\n", "from research.models import GenerationOptions\n", "from pathlib import Path\n", "\n", "# model = StarCoderBase16B_InferenceHost()\n", "model = StarCoderBase1B_InferenceHost()\n", "model.load()\n", "\n", "from research.core.model_input import ModelInput\n", "from research.models import GenerationOptions\n", "\n", "result = model.generate(ModelInput(prefix=\"def hello():\"), GenerationOptions(max_generated_tokens=32))\n", "print(result)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "next_token = 1\n", "generated_tokens = []\n", "start = time.time()\n", "for _ in range(max_generated_tokens):\n", "    scores = step_fn([next_token], attn_cache)\n", "    next_token = sample(scores)\n", "    generated_tokens.append(next_token)\n", "elapsed = time.time() - start\n", "print(f\"Generation took {elapsed:.2f} seconds, or {elapsed / max_generated_tokens:.3f} seconds/token\")"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "    return \"Hello World!\"\n", "\n", "@app.route(\"/hello/<name>\")\n", "def hello_name(name):\n", "    return \"Hello \" + name\n"]}], "source": ["import logging\n", "import time\n", "import torch\n", "import yaml\n", "from pathlib import Path\n", "from typing import Optional\n", "\n", "import os\n", "import sys\n", "sys.path.append(os.environ[\"HOME\"])\n", "sys.path.append(os.environ[\"HOME\"] + \"/augment\")\n", "\n", "from research.models.meta_model import GenerationOptions, GenerativeLanguageModel\n", "from research.core.model_input import ModelInput\n", "from research.core.all_prompt_formatters import (\n", "    <PERSON>bs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "    PromptFormatterStarCoder,\n", ")\n", "\n", "import services.inference_host.server.continuous_batching.fwd_starcoder as fwd_starcoder\n", "from services.inference_host.server.continuous_batching.fwd import ModelSpec\n", "\n", "# logger = logging.getLogger(__file__)\n", "logger = logging.getLogger()\n", "\n", "class InferenceHostModel(GenerativeLanguageModel):\n", "    \"\"\"A model that uses the inference host for inference.\"\"\"\n", "    def __init__(self, model_path: Path, checkpoint_path: Optional[Path] = None):\n", "        if not checkpoint_path:\n", "            checkpoint_path = model_path / \"checkpoint\"\n", "\n", "        model_config = yaml.safe_load(open(model_path / \"config.yml\"))\n", "\n", "        self.seq_length = model_config[\"seq-length\"]\n", "        self.model_spec = ModelSpec(\n", "            checkpoint_path=str(checkpoint_path),\n", "            num_layers=model_config[\"num-layers\"],\n", "            vocab_size=model_config[\"make_vocab_size_divisible_by\"],\n", "            emb_dim=model_config[\"hidden-size\"],\n", "            num_heads=model_config[\"num-attention-heads\"],\n", "            head_dim=model_config[\"hidden-size\"] // model_config[\"num-attention-heads\"],\n", "            rotary_pct=model_config.get(\"rotary_pct\", 0),\n", "        )\n", "\n", "        self.step_fn = None\n", "        self.attn_cache_generator_fn = None\n", "\n", "    def load(self):\n", "        \"\"\"Load the model.\"\"\"\n", "        if self.is_loaded:\n", "            return\n", "        \n", "        logger.info(f\"Loading model {self.name}...\")\n", "        start = time.time()\n", "        self.step_fn, self.attn_cache_generator_fn = fwd_starcoder.generate_step_fn(\n", "            self.model_spec)\n", "        elapsed = time.time() - start\n", "        logger.info(\"Loaded the model in {elapsed:.1f} seconds\")\n", "\n", "    def unload(self):\n", "        \"\"\"Unload the model and free GPU memory.\"\"\"\n", "        self.step_fn = None\n", "        self.attn_cache_generator_fn = None\n", "\n", "    @property\n", "    def is_loaded(self) -> bool:\n", "        \"\"\"Whether the model has been loaded or not.\"\"\"\n", "        return self.step_fn is not None\n", "\n", "    def _sample(self, scores: torch.Tensor) -> int:\n", "        \"\"\"Sam<PERSON> and returns a token.\"\"\"\n", "        assert len(scores.shape) == 2\n", "        return int(torch.argmax(scores[-1]).cpu().item())\n", "\n", "    def raw_generate(\n", "        self,\n", "        prompt_tokens: list[int],\n", "        options: GenerationOptions,\n", "    ) -> str:\n", "        \"\"\"Generate a completion based on the tokenized prompt.\n", "\n", "        Args:\n", "            prompt_tokens: the token list representing the full prompt for the model.\n", "            options: the configuration used during the generation procedure.\n", "\n", "        Returns:\n", "            The generated text.\n", "        \"\"\"\n", "        if options.temperature != 0:\n", "            raise NotImplementedError(\n", "                f\"Only greedy sampling implemented, got temperature={temperature}\")\n", "\n", "        if options.stop_criteria != None:\n", "            raise NotImplementedError(\"Stop criteria not implemented\")\n", "\n", "        if not self.is_loaded:\n", "            raise ValueError(\"Model must be loaded to generate text\")\n", "\n", "        generated_tokens = []\n", "        attn_cache = self.attn_cache_generator_fn(\n", "            len(prompt_tokens) + options.max_generated_tokens)\n", "\n", "        def should_stop(next_token):\n", "            return next_token == self.tokenizer.eod_id\n", "\n", "        next_prompt = prompt_tokens\n", "\n", "        # First time through we process the context, cache it, and sample a token.u\n", "        # On subsequent iterations we only show the model the last sampled tokens\n", "        # -- the rest is cached.\n", "        while len(generated_tokens) < options.max_generated_tokens:\n", "            scores = self.step_fn(next_prompt, attn_cache)\n", "            next_token = self._sample(scores)\n", "            if should_stop(next_token):\n", "                break\n", "            generated_tokens.append(next_token)\n", "            next_prompt = [next_token]\n", "\n", "        # Some tokenizers (SentencePiece, used by LLaMA2) do not have\n", "        # commutative concatenation and detokenization, so we avoid just\n", "        # detokenizing the generated tokens.\n", "        prompt_text = self.tokenizer.detokenize(prompt_tokens)\n", "        all_text = self.tokenizer.detokenize(prompt_tokens + generated_tokens)\n", "        generated_text = all_text[len(prompt_text):]\n", "        return generated_text\n", "\n", "\n", "class StarCoder_InferenceHostModel(InferenceHostModel):\n", "    @classmethod\n", "    def create_default_formatter(cls) -> AbstractPromptFormatter:\n", "        return PromptFormatterStarCoder()\n", "\n", "    @property\n", "    def supports_fim(self) -> bool:\n", "        return True\n", "\n", "    @property\n", "    def supports_retrieval(self) -> bool:\n", "        return True\n", "\n", "\n", "\n", "\n", "\n", "model = StarCoder_InferenceHostModel(\n", "    model_path=Path(\"/mnt/efs/augment/checkpoints/starcoderbase-1b_neox\"))\n", "\n", "model.load()\n", "\n", "result = model.raw_generate(\n", "    model.tokenizer.tokenize(\"def hello():\"),\n", "    GenerationOptions(max_generated_tokens=32))\n", "print(result)"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n"]}], "source": ["result = model.raw_generate(\n", "    model.tokenizer.tokenize(\"def hello():\"),\n", "    GenerationOptions(max_generated_tokens=32))\n", "print(result)"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"text/plain": ["0"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["model.tokenizer.eod_id"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.17"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}