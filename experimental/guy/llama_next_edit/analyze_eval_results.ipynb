{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "import matplotlib.pyplot as plt\n", "\n", "prefix = \"\"\n", "\n", "# root = Path(\"/mnt/efs/augment/user/guy/eval/next_edit_gen_eval/prs.v7.jsonl.zst\")\n", "# root = Path(\"/mnt/efs/augment/user/guy/eval/next_edit_gen_eval/deepseek33b-base-next-edit-v1-2M-samples-s1dot11-fixed-nowarmup-lr35e5--82a69b97-6716-49bc-b284-b088fd21a0cd--FastForward/prs.v7.jsonl.zst\")\n", "\n", "eval_results = [\n", "    {\n", "        \"name\": \"deepseek33b\",\n", "        \"root\": Path(\"/mnt/efs/augment/user/guy/eval/next_edit_gen_eval/deepseek33b-base-next-edit-v1-2M-samples-s1dot11-fixed-nowarmup-lr35e5--82a69b97-6716-49bc-b284-b088fd21a0cd--FastForward/prs.v7.jsonl.zst\"),\n", "        \"prefix\": \"\",\n", "    },\n", "    {\n", "        \"name\": \"llama70b\",\n", "        \"root\": Path(\"/mnt/efs/augment/user/guy/eval/next_edit_gen_eval/llama3-base-next-edit-v1-2M-samples-s1dot11--64k-steps--48c1f7bc-cd86-43c7-8713-1f8dc0ce965c--FastForward/prs.v7.jsonl.zst\"),\n", "        \"prefix\": \"\",\n", "    },\n", "    {\n", "        \"name\": \"sc2\",\n", "        \"root\": Path(\"/mnt/efs/augment/eval/jobs/AGEXMxBe\"),\n", "        \"prefix\": \"000_\",\n", "    },\n", "]\n", "\n", "# variant = \"with_instruct\"\n", "variant = \"no_instruct\"\n", "\n", "fig, ax = plt.subplots()\n", "ax.set_xlabel(\"num samples\")\n", "ax.set_ylabel(\"acc\")\n", "\n", "for results in eval_results:\n", "    name = results[\"name\"]\n", "    root = results[\"root\"]\n", "    prefix = results[\"prefix\"]\n", "\n", "    num_correct = 0\n", "    num_samples = 0\n", "    running_acc = []\n", "\n", "    for path in sorted(root.rglob(f\"{prefix}_{variant}_*/is_correct.txt\")):\n", "        is_correct = path.read_text() == \"True\"\n", "        if is_correct:\n", "            num_correct += 1\n", "        num_samples += 1\n", "        acc = num_correct / num_samples\n", "        running_acc.append(acc)\n", "\n", "    assert running_acc, \"No samples found\"\n", "    print(f\"{name} Correct: {num_correct} out of {num_samples} ({num_correct/num_samples*100:.1f}%)\")\n", "\n", "    ax.plot(running_acc, \"-\", label=name)\n", "\n", "ax.set_xlim(0, 2000)\n", "# ax.set_ylim(0.12, 0.2)\n", "ax.set_title(\"Accuracy on PRs\")\n", "ax.grid()\n", "ax.legend()\n", "\n", "fig.show()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for path in sorted(root.rglob(\"000__no_instruct_*\")):\n", "    gold_change = (path / \"gold_change.txt\").read_text()\n", "    predicted_change = (path / \"predicted_change.txt\").read_text()\n", "    is_correct = (path / \"is_correct.txt\").read_text() == \"True\"\n", "    print()\n", "    print(\"=\" * 120)\n", "    print(\"SAMPLE:\", path.name)\n", "    print(\"CORRECT:\", is_correct)\n", "    print(\"\\nGOLD CHANGE:\")\n", "    print(gold_change)\n", "    print(\"\\nPREDICTED CHANGE:\")\n", "    print(predicted_change)\n", "    print()\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}