{"cells": [{"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [" ```haskell\n", "main :: IO ()\n", "main = putStrLn \"Hello, World!\"\n", "```\n", " ```haskell\n", "main :: IO ()\n", "main = putStrLn \"Hello, World!\"\n", "```"]}], "source": ["from research.llm_apis.chat_utils import OpenAIAPIClient\n", "\n", "deepseek_address = \"*************:8000\"\n", "client = OpenAIAPIClient(address=deepseek_address)\n", "\n", "prompt = \"Write hello world in haskell\"\n", "\n", "# Without streaming\n", "response = client.generate(messages=[prompt], max_tokens=256)\n", "print(response)\n", "\n", "# With streaming\n", "response = \"\"\n", "for response_elem in client.generate_stream(messages=[prompt], max_tokens=256):\n", "    print(response_elem, end=\"\")\n", "    response += response_elem"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Here's a Python script that downloads a GitHub repository, archives\n"]}], "source": ["from pathlib import Path\n", "import os\n", "from research.llm_apis.chat_utils import Llama3ChatClient, OpenAIAPIClient, AnthropicClient\n", "\n", "llama_address = \"**************:8010\"\n", "llama_client = Llama3ChatClient(\"triton\", address=llama_address, timeout=60)\n", "\n", "deepseek_address = \"*************:8000\"\n", "deepseek_client = OpenAIAPIClient(address=deepseek_address)\n", "\n", "api_key = Path(os.environ[\"HOME\"], \".config/anthropic/api_key\").read_text(encoding=\"utf8\").strip()\n", "anthropic_client = AnthropicClient(api_key=api_key)\n", "\n", "client = anthropic_client\n", "\n", "prompt = \"Write a script that downloads a github repo, archives it, and uploads to S3.\"\n", "\n", "response = client.generate(messages=[prompt], max_tokens=12)\n", "print(response)\n", "\n", "# prompt = Path(\"prompt_v1.txt\").read_text(\"utf8\")\n", "# response = \"\"\n", "\n", "# for response_elem in client.generate_stream(messages=[prompt], max_tokens=4096, system_prompt=\"hi\"):\n", "#     print(response_elem, end=\"\")\n", "#     response += response_elem\n", "    \n"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Here is the rewritten code:\n", "\n", "```\n", "\"\"\"Unit tests for update_versions script used for intellij publishing.\"\"\"\n", "\n", "import pytest\n", "import urllib.error\n", "from unittest.mock import MagicMock, patch\n", "\n", "from update_versions import (\n", "    <PERSON><PERSON><PERSON>,\n", "    get_new_version,\n", "    parse_jetbrains_marketplace_data,\n", ")\n", "\n", "\n", "def test_is_semver_string():\n", "    assert Semver.is_semver_string(\"0.0.0\") is True\n", "    assert Semver.is_semver_string(\"1.12.123\") is True\n", "    assert Semver.is_semver_string(\"Nope\") is False\n", "    assert Semver.is_semver_string(\"\") is False\n", "\n", "\n", "def test_from_string():\n", "    assert Semver.from_string(\"0.0.0\") == Semver(0, 0, 0)\n", "    assert Semver.from_string(\"1.12.123\") == Semver(1, 12, 123)\n", "\n", "\n", "def test_from_string_error():\n", "    with pytest.raises(SystemError):\n", "        Semver.from_string(\"nope\")\n", "\n", "\n", "def test_lt():\n", "    assert Semver(0, 0, 0) < Semver(0, 0, 1)\n", "    assert Semver(0, 0, 0) < Semver(0, 1, 0)\n", "    assert Semver(0, 0, 0) < Semver(1, 0, 0)\n", "    assert Semver(0, 0, 1) < Semver(0, 0, 2)\n", "    assert Semver(0, 1, 1) < Semver(0, 2, 2)\n", "    assert Semver(1, 1, 1) < Semver(2, 2, 2)\n", "    assert Semver(1, 1, 1) < Semver(2, 0, 0)\n", "\n", "\n", "def test_eq():\n", "    assert Semver(0, 0, 0) == Semver(0, 0, 0)\n", "    assert Semver(0, 0, 1) == Semver(0, 0, 1)\n", "    assert Semver(0, 1, 0) == Semver(0, 1, 0)\n", "    assert Semver(1, 0, 0) == Semver(1, 0, 0)\n", "    assert Semver(1, 2, 3) == Semver(1, 2, 3)\n", "\n", "\n", "def test_increment():\n", "    assert Semver(0, 0, 0).increment(\"patch\") == Semver(0, 0, 1)\n", "    assert Semver(0, 0, 0).increment(\"minor\") == Semver(0, 1, 0)\n", "    assert Semver(0, 0, 0).increment(\"major\") == Semver(1, 0, 0)\n", "\n", "\n", "def test_increment_err():\n", "    with pytest.raises(SystemError):\n", "        Semver(0, 0, 0).increment(\"other\")\n", "```"]}], "source": ["#question = \"Great, now rewrite the modified code to apply these changes.\"\n", "question = \"Great, now rewrite the selected code to apply these changes.\"\n", "messages = [prompt, response, question]\n", "for response_elem in client.generate_stream(messages=messages, max_tokens=4096):\n", "    print(response_elem, end=\"\")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}