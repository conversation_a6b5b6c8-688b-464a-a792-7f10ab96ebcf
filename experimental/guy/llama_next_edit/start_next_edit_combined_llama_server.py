"""A script to start a model server for next edit generation."""

from typing import Literal

import yaml

from base.prompt_format_next_edit.gen_prompt_formatter import EditGenFormatterConfig
from base.tokenizers import DeepSeekCoderBaseTokenizer, Llama3BaseTokenizer
from research.core.constants import (
    AUGMENT_CHECKPOINTS_ROOT,
    AUGMENT_EFS_ROOT,
    AUGMENT_ROOT,
)
from research.eval.harness import factories
from research.eval.harness.systems.next_edit_combined_system import (
    NextEditCombinedSystem,
)
from research.eval.harness.systems.next_edit_gen_llama_system import (
    NextEditGenLlamaSystem,
)
from research.eval.harness.systems.next_edit_gen_system import NextEditGenSystem
from research.eval.harness.systems.next_edit_location_system import (
    BasicNextEditLocationSystem,
)
from research.eval.harness.systems.next_edit_reranker_system import (
    NextEditRerankerSystem,
)
from research.llm_apis.chat_utils import (
    AnthropicClient,
)
from research.model_server.launch_model_server import main
from research.models.fastforward_llama_models import (
    FastForwardDeepSeekCoderBase33B,
    FastForwardLLAMA3_70B_Base,
)
from research.models.fastforward_models import StarCoder2_FastForward
from research.models.meta_model import GenerationOptions
from research.next_edits.edit_gen_formatters import (
    EditGenPromptFormatter,
)
from research.next_edits.smart_chunking import SmartChunker


def build_location_system() -> BasicNextEditLocationSystem:
    config_path = (
        AUGMENT_ROOT / "research/model_server/configs/next_edit_location_raven.yaml"
    )
    with config_path.open(encoding="utf8") as f:
        config = yaml.safe_load(f)
    system = factories.create_system(config)
    assert isinstance(system, BasicNextEditLocationSystem)
    return system


def build_reranking_system():
    edit_model = StarCoder2_FastForward(
        AUGMENT_CHECKPOINTS_ROOT
        / "next-edit-gen/S1.7-R1.0_no_retrieval-P1.8_context12-100K_repos-starcoder2_7b"
    )

    prompt_formatter = EditGenPromptFormatter(
        edit_model.tokenizer_type(),
        config=EditGenFormatterConfig(diff_context_lines=9),
    )

    edit_model = NextEditGenSystem(
        edit_model,
        generation_options=GenerationOptions(max_generated_tokens=1),
        retriever=None,
        chat_client=None,
        prompt_formatter=prompt_formatter,
    )
    localizer = build_location_system()
    rechunker = SmartChunker(max_chunk_chars=6000)
    reranker = NextEditRerankerSystem(localizer, edit_model, rechunker)
    return reranker


def build_zero_shot_generation_system():
    # # llama_server_address = "*************:8000"  # 1xH100
    # llama_server_address = "*************:8000"  # 8xH100

    # chat_client = Llama3ChatClient(
    #     server_type="triton", address=llama_server_address, timeout=180
    # )

    # TODO(guy) load API key
    api_key = ""
    chat_client = AnthropicClient(api_key=api_key)

    # deepseek_address = "*************:8000"
    # base_url = f"http://{address}/v1"
    # chat_client = OpenAIAPIClient(base_url=base_url, api_key="1234")

    return NextEditGenLlamaSystem(
        prompt_type="sonnet",
        chat_client=chat_client,
        generation_options=GenerationOptions(max_generated_tokens=4096),
    )


def build_finetuned_generation_system(
    checkpoint_path: str, model_type: Literal["llama", "deepseek"]
):
    if model_type == "llama":
        model = FastForwardLLAMA3_70B_Base(checkpoint_path=checkpoint_path)
        tokenizer = Llama3BaseTokenizer()
    elif model_type == "deepseek":
        model = FastForwardDeepSeekCoderBase33B(checkpoint_path=checkpoint_path)
        tokenizer = DeepSeekCoderBaseTokenizer()
    else:
        raise ValueError(f"Unknown model type {model_type=}")

    # 9 diff context lines seem to be a sweet spot
    prompt_formatter = EditGenPromptFormatter(
        tokenizer=tokenizer,
        config=EditGenFormatterConfig(diff_context_lines=9),
    )

    return NextEditGenSystem(
        model,
        generation_options=GenerationOptions(max_generated_tokens=1200),
        retriever=None,
        chat_client=None,
        prompt_formatter=prompt_formatter,
    )


def build_generation_system():
    # return build_zero_shot_generation_system()
    return build_finetuned_generation_system(
        "/mnt/efs/augment/user/guy/checkpoints/llama3-base-next-edit-v1-32ksteps-5af5e1ac-7c8b-4ea7-948d-8d2f1b1c7683-ff",
        "llama",
    )


def build_combined_system():
    gen_system = build_generation_system()
    loc_system = build_reranking_system()
    return NextEditCombinedSystem(
        loc_system,
        gen_system,
        max_changes_to_return=1,
        default_max_changes_to_attempt=18,
        location_score_filter=0.35,
    )


def start_server():
    logger_dir = AUGMENT_EFS_ROOT / "user/guy" / "model_server_logs"
    system = build_combined_system()
    system.load()

    args = ["--port", "5000", "--log_dir", str(logger_dir)]
    # Comment out the line below if you are only using this server locally and are not planning to make it available to others.
    args.extend(["--host", "0.0.0.0"])
    main(
        system,
        input_args=args,
    )


if __name__ == "__main__":
    start_server()
