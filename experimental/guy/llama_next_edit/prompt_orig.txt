I am a developer working on a task in my codebase.

Here is the change I made so far:

```
+++ clients/vscode/update_versions_test.py
@@ -1,9 +1,9 @@
 """Unit tests for update_versions script used for vscode publishing."""
 
 import json
-import unittest
+import pytest
 
 from update_versions import (
     ExtensionVersions,
     Semver,
     get_new_version,
@@ -11,60 +11,54 @@ from update_versions import (
     get_stable_release_ref,
     versions_from_vsce_data,
 )
 
 
-class TestSemver(unittest.TestCase):
+def test_is_semver_string():
     """Semver parsing."""
-
-    def test_is_semver_string(self):
-        self.assertEqual(Semver.is_semver_string("0.0.0"), True)
-        self.assertEqual(Semver.is_semver_string("1.12.123"), True)
-        self.assertEqual(Semver.is_semver_string("Nope"), False)
-        self.assertEqual(Semver.is_semver_string(""), False)
-
-    def test_from_string(self):
-        self.assertEqual(Semver.from_string("0.0.0"), Semver(0, 0, 0))
-        self.assertEqual(Semver.from_string("1.12.123"), Semver(1, 12, 123))
-
-    def test_from_string_error(self):
-        with self.assertRaises(SystemError) as context:
-            Semver.from_string("nope")
-
-        self.assertTrue("Failed to parse version" in str(context.exception))
-
-    def test_lt(self):
-        self.assertTrue(Semver(0, 0, 0) < Semver(0, 0, 1))
-        self.assertTrue(Semver(0, 0, 0) < Semver(0, 1, 0))
-        self.assertTrue(Semver(0, 0, 0) < Semver(1, 0, 0))
-        self.assertTrue(Semver(0, 0, 1) < Semver(0, 0, 2))
-        self.assertTrue(Semver(0, 1, 1) < Semver(0, 2, 2))
-        self.assertTrue(Semver(1, 1, 1) < Semver(2, 2, 2))
-        self.assertTrue(Semver(1, 1, 1) < Semver(2, 0, 0))
-
-    def test_eq(self):
-        self.assertTrue(Semver(0, 0, 0) == Semver(0, 0, 0))
-        self.assertTrue(Semver(0, 0, 1) == Semver(0, 0, 1))
-        self.assertTrue(Semver(0, 1, 0) == Semver(0, 1, 0))
-        self.assertTrue(Semver(1, 0, 0) == Semver(1, 0, 0))
-        self.assertTrue(Semver(1, 2, 3) == Semver(1, 2, 3))
-
-    def test_increment(self):
-        self.assertEqual(Semver(0, 0, 0).increment("patch"), Semver(0, 0, 1))
-        self.assertEqual(Semver(0, 0, 0).increment("minor"), Semver(0, 1, 0))
-        self.assertEqual(Semver(0, 0, 0).increment("major"), Semver(1, 0, 0))
-
-    def test_increment_err(self):
-        with self.assertRaises(SystemError) as context:
-            Semver(0, 0, 0).increment("other")
-
-        self.assertTrue("Unknown update type" in str(context.exception))
-
-    def test_get_channel(self):
-        self.assertEqual(Semver(0, 0, 0).get_channel(), "prerelease")
-        self.assertEqual(Semver(0, 0, 1).get_channel(), "stable")
-        self.assertEqual(Semver(0, 0, 2).get_channel(), "stable")
+    assert Semver.is_semver_string("0.0.0") is True
+    assert Semver.is_semver_string("1.12.123") is True
+    assert Semver.is_semver_string("Nope") is False
+    assert Semver.is_semver_string("") is False
+
+def test_from_string():
+    assert Semver.from_string("0.0.0") == Semver(0, 0, 0)
+    assert Semver.from_string("1.12.123") == Semver(1, 12, 123)
+
+def test_from_string_error():
+    with pytest.raises(SystemError):
+        Semver.from_string("nope")
+
+def test_lt():
+    assert Semver(0, 0, 0) < Semver(0, 0, 1)
+    assert Semver(0, 0, 0) < Semver(0, 1, 0)
+    assert Semver(0, 0, 0) < Semver(1, 0, 0)
+    assert Semver(0, 0, 1) < Semver(0, 0, 2)
+    assert Semver(0, 1, 1) < Semver(0, 2, 2)
+    assert Semver(1, 1, 1) < Semver(2, 2, 2)
+    assert Semver(1, 1, 1) < Semver(2, 0, 0)
+
+def test_eq():
+    assert Semver(0, 0, 0) == Semver(0, 0, 0)
+    assert Semver(0, 0, 1) == Semver(0, 0, 1)
+    assert Semver(0, 1, 0) == Semver(0, 1, 0)
+    assert Semver(1, 0, 0) == Semver(1, 0, 0)
+    assert Semver(1, 2, 3) == Semver(1, 2, 3)
+
+def test_increment():
+    assert Semver(0, 0, 0).increment("patch") == Semver(0, 0, 1)
+    assert Semver(0, 0, 0).increment("minor") == Semver(0, 1, 0)
+    assert Semver(0, 0, 0).increment("major") == Semver(1, 0, 0)
+
+def test_increment_err():
+    with pytest.raises(SystemError):
+        Semver(0, 0, 0).increment("other")
+
+def test_get_channel():
+    assert Semver(0, 0, 0).get_channel() == "prerelease"
+    assert Semver(0, 0, 1).get_channel() == "stable"
+    assert Semver(0, 0, 2).get_channel() == "stable"
 
 
 class TestExtensionVersions(unittest.TestCase):
     """Store and update the marketplace stable + prerelease versions."""
 

```

I would like to continue on my task and edit the file `clients/intellij/update_versions_test.py`.
Here is the contents of the file, with the selected code omitted:

```

[selected code]


class TestParseJetbrainsMarketplaceData(unittest.TestCase):
    """Parse JetBrains Marketplace data."""

    def test_no_versions(self):
        versions = parse_jetbrains_marketplace_data("""<?xml version="1.0" encoding="UTF-8"?>
<plugin-repository/>
""")
        self.assertEqual(versions, [])

    def test_with_versions(self):
        versions = parse_jetbrains_marketplace_data("""<?xml version="1.0" encoding="UTF-8"?>
<plugin-repository>
  <ff>"Completion"</ff>
  <category name="Completion">
    <idea-plugin downloads="15" size="1275925" date="1712612625000" updatedDate="1712612625000" url="">
      <name>Augment</name>
      <id>com.augmentcode</id>
      <description><![CDATA[<p>Augment yourself with the best AI pair programmer</p>]]></description>
      <version>0.0.3</version>
      <vendor email="" url="">Augment Computing</vendor>
      <rating>00</rating>
      <change-notes><![CDATA[<h3>Added</h3>]]></change-notes>
      <download-url>https://plugins.jetbrains.com/plugin/download?updateId=519419</download-url>
      <idea-version min="n/a" max="n/a" since-build="241.0" until-build="241.*"/>
    </idea-plugin>
    <idea-plugin downloads="15" size="1239610" date="1712180135000" updatedDate="1712180135000" url="">
      <name>Augment</name>
      <id>com.augmentcode</id>
      <description><![CDATA[<p>Augment yourself with the best AI pair programmer</p>]]></description>
      <version>0.0.2</version>
      <vendor email="" url="">Augment Computing</vendor>
      <rating>00</rating>
      <change-notes><![CDATA[<h3>Added</h3>]]></change-notes>
      <download-url>https://plugins.jetbrains.com/plugin/download?updateId=516290</download-url>
      <idea-version min="n/a" max="n/a" since-build="241.0" until-build="241.*"/>
    </idea-plugin>
    <idea-plugin downloads="15" size="1312975" date="1712872189000" updatedDate="1712872189000" url="">
      <name>Augment</name>
      <id>com.augmentcode</id>
      <description><![CDATA[<p>Augment yourself with the best AI pair programmer</p>]]></description>
      <version>0.0.4</version>
      <vendor email="" url="">Augment Computing</vendor>
      <rating>00</rating>
      <change-notes><![CDATA[<h3>Added</h3>]]></change-notes>
      <download-url>https://plugins.jetbrains.com/plugin/download?updateId=521304</download-url>
      <idea-version min="n/a" max="n/a" since-build="241.0" until-build="241.*"/>
    </idea-plugin>
  </category>
</plugin-repository>
""")
        self.assertEqual(versions, [Semver(0, 0, 4), Semver(0, 0, 3), Semver(0, 0, 2)])


class TestGetNewVersion(unittest.TestCase):
    """Core of the script that returns the updated version for a channel."""

    def generate_xml_body(self, version_list):
        pieces = []
        for version in version_list:
            pieces.append(f"<idea-plugin><version>{version}</version></idea-plugin>")
        return f"<?xml version=\"1.0\" encoding=\"UTF-8\"?><plugin-repository><category>{"".join(pieces)}</category></plugin-repository>"

    def generate_mock_urlopen(self, version_list):
        response_mock = MagicMock()
        response_mock.getcode.return_value = 200
        response_mock.read.return_value = self.generate_xml_body(version_list)
        response_mock.__enter__.return_value = response_mock
        return response_mock

    def test_no_release_channel(self):
        with self.assertRaises(SystemError) as context:
            get_new_version()
        self.assertTrue("RELEASE_CHANNEL is not defined" in str(context.exception))

    def test_bad_release_channel(self):
        with self.assertRaises(SystemError) as context:
            get_new_version(channel="other")
        self.assertTrue(
            "Release channel must be stable or beta" in str(context.exception)
        )

    def test_urllib_error(self):
        with patch(
            "urllib.request.urlopen",
            side_effect=urllib.error.URLError("Injected error"),
        ):
            with self.assertRaises(SystemError) as context:
                get_new_version(channel="beta")
            self.assertTrue(
                "Request to lookup plugin data failed" in str(context.exception)
            )

    def test_return_version_override(self):
        with patch("urllib.request.urlopen") as urlopen:
            urlopen.return_value = self.generate_mock_urlopen(["0.0.0"])
            self.assertEqual(
                get_new_version(
                    version_override="0.1.0",
                    channel="beta",
                ),
                Semver(0, 1, 0),
            )

            self.assertEqual(
                get_new_version(
                    version_override="0.1.0",
                    channel="stable",
                ),
                Semver(0, 1, 0),
            )

    def test_no_versions(self):
        with patch("urllib.request.urlopen") as urlopen:
            urlopen.return_value = self.generate_mock_urlopen([])
            with self.assertRaises(SystemError) as context:
                get_new_version(
                    channel="beta",
                )
            self.assertTrue(
                "No versions found from Jetbrains Marketplace" in str(context.exception)
            )

    def test_new_beta(self):
        with patch("urllib.request.urlopen") as urlopen:
            urlopen.return_value = self.generate_mock_urlopen(["0.0.1", "0.1.0"])
            self.assertEqual(
                get_new_version(
                    channel="beta",
                ),
                Semver(0, 1, 1),
            )

    def test_beta_to_stable(self):
        with patch("urllib.request.urlopen") as urlopen:
            urlopen.return_value = self.generate_mock_urlopen(["0.0.1", "0.0.2"])
            self.assertEqual(
                get_new_version(
                    channel="stable",
                    stable_release_ref="0.1.0",
                ),
                Semver(0, 1, 0),
            )

    def test_stable_patch_release(self):
        with patch("urllib.request.urlopen") as urlopen:
            urlopen.return_value = self.generate_mock_urlopen(["0.1.0", "0.0.1"])
            self.assertEqual(
                get_new_version(
                    channel="stable",
                    stable_release_ref="commitshaabcdef1234",
                ),
                Semver(0, 1, 1),
            )


if __name__ == "__main__":
    unittest.main()

```

Here is the selected code, which should be edited:
```
"""Unit tests for update_versions script used for intellij publishing."""

import unittest
import urllib.error
from unittest.mock import MagicMock, patch

from update_versions import (
    Semver,
    get_new_version,
    parse_jetbrains_marketplace_data,
)


class TestSemver(unittest.TestCase):
    """Semver parsing."""

    def test_is_semver_string(self):
        self.assertEqual(Semver.is_semver_string("0.0.0"), True)
        self.assertEqual(Semver.is_semver_string("1.12.123"), True)
        self.assertEqual(Semver.is_semver_string("Nope"), False)
        self.assertEqual(Semver.is_semver_string(""), False)

    def test_from_string(self):
        self.assertEqual(Semver.from_string("0.0.0"), Semver(0, 0, 0))
        self.assertEqual(Semver.from_string("1.12.123"), Semver(1, 12, 123))

    def test_from_string_error(self):
        with self.assertRaises(SystemError) as context:
            Semver.from_string("nope")

        self.assertTrue("Failed to parse version" in str(context.exception))

    def test_lt(self):
        self.assertTrue(Semver(0, 0, 0) < Semver(0, 0, 1))
        self.assertTrue(Semver(0, 0, 0) < Semver(0, 1, 0))
        self.assertTrue(Semver(0, 0, 0) < Semver(1, 0, 0))
        self.assertTrue(Semver(0, 0, 1) < Semver(0, 0, 2))
        self.assertTrue(Semver(0, 1, 1) < Semver(0, 2, 2))
        self.assertTrue(Semver(1, 1, 1) < Semver(2, 2, 2))
        self.assertTrue(Semver(1, 1, 1) < Semver(2, 0, 0))

    def test_eq(self):
        self.assertTrue(Semver(0, 0, 0) == Semver(0, 0, 0))
        self.assertTrue(Semver(0, 0, 1) == Semver(0, 0, 1))
        self.assertTrue(Semver(0, 1, 0) == Semver(0, 1, 0))
        self.assertTrue(Semver(1, 0, 0) == Semver(1, 0, 0))
        self.assertTrue(Semver(1, 2, 3) == Semver(1, 2, 3))

    def test_increment(self):
        self.assertEqual(Semver(0, 0, 0).increment("patch"), Semver(0, 0, 1))
        self.assertEqual(Semver(0, 0, 0).increment("minor"), Semver(0, 1, 0))
        self.assertEqual(Semver(0, 0, 0).increment("major"), Semver(1, 0, 0))

    def test_increment_err(self):
        with self.assertRaises(SystemError) as context:
            Semver(0, 0, 0).increment("other")

        self.assertTrue("Unknown update type" in str(context.exception))

```

Please suggest how to edit the selected code, to continue the task.
Use exactly the following format:

Motivation: ... explain the motivation ...

Modified code:
```
... modified code ...
```