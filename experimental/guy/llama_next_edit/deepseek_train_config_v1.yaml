determined:
  description: null
  workspace: Dev
  project: guy

augment:
  podspec_path: "8xH100.yaml"
  # <PERSON><PERSON><PERSON>'s original value
  gpu_count: 64
  # gpu_count: 128
  project_group: "finetuning"

fastbackward_configs:
  - configs/deepseek_base_33b.py

fastbackward_args:
  hf_checkpoint_dir: /mnt/efs/augment/checkpoints/deepseek/deepseek-coder-33b-base

  tokenizer_name: DeepSeekCoderBaseWrappedProdTokenizer
  use_research_tokenizer: True
  visualize_logits_samples: 8
  checkpoint_optimizer_state: false
  log_interval: 100

  train_data_path: /mnt/efs/augment/data/processed/next-edit/gh_pr_train_repartitioned/S1.11.1_6000p_2000f,R1.1_no_retrieval_synth_instruct,P1.10.1_deepseekcoder_context12/train
  eval_data_path: /mnt/efs/augment/data/processed/next-edit/gh_pr_train_repartitioned/S1.11.1_6000p_2000f,R1.1_no_retrieval_synth_instruct,P1.10.1_deepseekcoder_context12/valid
  loss_mask_policy: negative_tokens

  block_size: 8192

  # <PERSON><PERSON><PERSON>'s original params
  # gradient_accumulation_steps: 32
  # batch_size: 2
  # max_iters: 4000
  # warmup_iters: 100
  # lr_decay_iters: 4000
  # # block_size: 7936
  # learning_rate: 1.0e-5
  # min_lr: 1.0e-6
  # decay_lr: True

  # My params
  batch_size: 2
  gradient_accumulation_steps: 8
  max_iters: 8000
  learning_rate: 1.0e-5
  min_lr: 1.0e-6
  decay_lr: True

  eval_interval: 250
  eval_items: 12800

  wandb_project: llama3-next-edit
  run_name: deepseek33b-base-next-edit-v1-2M-samples-s1dot11-fixed
