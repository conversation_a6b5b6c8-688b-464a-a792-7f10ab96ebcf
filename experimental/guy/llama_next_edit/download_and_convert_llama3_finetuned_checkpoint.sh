#!/bin/bash

set -e

# NAME=llama3-base-next-edit-v1-32ksteps
# CHECKPOINT=5af5e1ac-7c8b-4ea7-948d-8d2f1b1c7683
# TP=8

# NAME=llama3-base-next-edit-v1-2M-samples-s1dot11--64k-steps
# CHECKPOINT=48c1f7bc-cd86-43c7-8713-1f8dc0ce965c
# TP=8

NAME=deepseek33b-base-next-edit-v1-2M-samples-s1dot11-fixed-nowarmup-lr35e5
CHECKPOINT=82a69b97-6716-49bc-b284-b088fd21a0cd
TP=4

ROOT=/mnt/efs/augment/user/guy/checkpoints
LOCAL_DIR=${ROOT}/${NAME}-${CHECKPOINT}
FF_DIR=${ROOT}/${NAME}--${CHECKPOINT}--FastForward

mkdir -p $LOCAL_DIR
s3cmd get --recursive --exclude=*zero_pp* --exclude=*code* s3://dev-training-dai/$CHECKPOINT/ $LOCAL_DIR/

mkdir -p $FF_DIR
python ~/augment/experimental/yuri/convert_fb_checkpoint_to_ff.py --input_ckpt_dir $LOCAL_DIR --output_ckpt_dir ${FF_DIR} -m $TP
