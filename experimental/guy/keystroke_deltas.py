from pynput.keyboard import Key, Listener
import time
import argparse
from pathlib import Path
import json
import numpy as np


class KeystrokeDeltasRecorder:
    """Keystroke recorder."""

    def __init__(self, name):
        self.deltas_msec = []
        self.timestamps = []
        self.output_file = Path(f"{name}.jsonl").open("w", encoding="utf8")

    def on_key_press(self, key):
        timestamp = time.time()
        self.timestamps.append(timestamp)
        if len(self.timestamps) > 1:
            delta_msec = 1000 * (self.timestamps[-1] - self.timestamps[-2])
            self.deltas_msec.append(delta_msec)
            output_record = {"timestamp": timestamp, "delta_msec": delta_msec}
            self.output_file.write(json.dumps(output_record) + "\n")

            # mean_delta_msec = np.mean(self.deltas_msec)
            # std_delta_msec = np.std(self.deltas_msec)
            median_delta_msec = np.median(self.deltas_msec)
            max_delta_msec = max(self.deltas_msec)
            min_delta_msec = min(self.deltas_msec)
            percent_deltas_below_50ms = 100 * len([delta for delta in self.deltas_msec if delta < 50]) / len(self.deltas_msec)
            percent_deltas_below_100ms = 100 * len([delta for delta in self.deltas_msec if delta < 100]) / len(self.deltas_msec)

            print(
                "\nDelta stats (msec): ",
                f"\tlast={delta_msec:.1f}"
                # f"\tmin={min_delta_msec:.1f}\tmax={max_delta_msec:.1f}"
                f"\tmedian={median_delta_msec:.1f}, "
                f"\tdeltas_below_50ms={percent_deltas_below_50ms:.1f}%"
                f"\tdeltas_below_100ms={percent_deltas_below_100ms:.1f}%"
            )

    def on_key_release(self, key):
        if key == Key.esc:
            # Stop listener
            return False


def main():
    # parse --name
    parser = argparse.ArgumentParser()
    parser.add_argument("--name", "-n", type=str, default="default")
    args = parser.parse_args()

    recorder = KeystrokeDeltasRecorder(args.name)

    with Listener(
        on_press=recorder.on_key_press, on_release=recorder.on_key_release
    ) as listener:
        listener.join()


if __name__ == "__main__":
    main()
