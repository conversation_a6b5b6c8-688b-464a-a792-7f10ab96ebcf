# context:

user    0m11.003s
sys     0m5.301s
guy-dev-data-processing-lga1 [git:packing_and_fim_pipeline] [0] ~/augment/research/data/spark/pipelines > cat scripts/run_starcoder_pretraining.sh
#!/bin/bash
export PYTHONPATH=${PYTHONPATH}:${HOME}/augment:${HOME}/augment/research/gpt-neox/
export TOKENIZERS_PARALLELISM=false

# chdir to the main pipeline directory
cd "$(dirname "$0")"
cd ..

zip -q -r /tmp/stages.zip stages

cd ~/augment/research/gpt-neox
zip -q -r /tmp/megatron.zip megatron
cd - > /dev/null

time python3.9 pipeline.py \
    --config configs/starcoder_pretraining.yaml \
    --py_file /tmp/stages.zip \
    --py_file /tmp/megatron.zip \
    |& tee log.txt

# natural language instruction:

run that script

# bash command:

scripts/run_starcoder_pretraining.sh

# context:

# natural language instruction:

print the second column of file.txt

# bash command:

cat file.txt | awk '{print $2}'

# context:

print the second column of file.txt

# bash command:

cat file.txt | awk '{print $2}'

# context:

chunked/  raw/  tokenized/
guy-dev-data-processing-lga1 [git:] [0] /mnt/efs/augment-lga1-nvme/data/raw/starcoder > cd raw
guy-dev-data-processing-lga1 [git:] [0] /mnt/efs/augment-lga1-nvme/data/raw/starcoder/raw > ls
'lang=ada'/          'lang=clojure'/       'lang=erlang'/                             'lang=java'/                            'lang=lua'/          'lang=protocol-buffer'/   'lang=smalltalk'/      'lang=typescript'/
'lang=agda'/         'lang=cmake'/         'lang=f-sharp'/                            'lang=java-server-pages'/               'lang=makefile'/     'lang=python'/            'lang=solidity'/       'lang=verilog'/
'lang=alloy'/        'lang=coffeescript'/  'lang=fortran'/                            'lang=javascript'/                      'lang=maple'/        'lang=r'/                 'lang=sparql'/         'lang=vhdl'/
'lang=antlr'/        'lang=common-lisp'/   'lang=git-commits-cleaned'/                'lang=json'/                            'lang=markdown'/     'lang=racket'/            'lang=sql'/            'lang=visual-basic'/
'lang=applescript'/  'lang=cpp'/           'lang=github-issues-filtered-structured'/  'lang=julia'/                           'lang=mathematica'/  'lang=restructuredtext'/  'lang=stan'/           'lang=xslt'/
'lang=assembly'/     'lang=css'/           'lang=glsl'/                               'lang=jupyter-scripts-dedup-filtered'/  'lang=matlab'/       'lang=rmarkdown'/         'lang=standard-ml'/    'lang=yacc'/
'lang=augeas'/       'lang=cuda'/          'lang=go'/                                 'lang=jupyter-structured-clean-dedup'/  'lang=ocaml'/        'lang=ruby'/              'lang=stata'/          'lang=yaml'/
'lang=awk'/          'lang=dart'/          'lang=groovy'/                             'lang=kotlin'/                          'lang=pascal'/       'lang=rust'/              'lang=systemverilog'/  'lang=zig'/
'lang=batchfile'/    'lang=dockerfile'/    'lang=haskell'/                            'lang=lean'/                            'lang=perl'/         'lang=sas'/               'lang=tcl'/
'lang=bluespec'/     'lang=elixir'/        'lang=html'/                               'lang=literate-agda'/                   'lang=php'/          'lang=scala'/             'lang=tcsh'/
'lang=c'/            'lang=elm'/           'lang=idris'/                              'lang=literate-coffeescript'/           'lang=powershell'/   'lang=scheme'/            'lang=tex'/
'lang=c-sharp'/      'lang=emacs-lisp'/    'lang=isabelle'/                           'lang=literate-haskell'/                'lang=prolog'/       'lang=shell'/             'lang=thrift'/
guy-dev-data-processing-lga1 [git:] [0] /mnt/efs/augment-lga1-nvme/data/raw/starcoder/raw > cd lang=python
guy-dev-data-processing-lga1 [git:] [0] /mnt/efs/augment-lga1-nvme/data/raw/starcoder/raw/lang=python > ls
train-00000-of-00059.parquet  train-00009-of-00059.parquet  train-00018-of-00059.parquet  train-00027-of-00059.parquet  train-00036-of-00059.parquet  train-00045-of-00059.parquet  train-00054-of-00059.parquet
train-00001-of-00059.parquet  train-00010-of-00059.parquet  train-00019-of-00059.parquet  train-00028-of-00059.parquet  train-00037-of-00059.parquet  train-00046-of-00059.parquet  train-00055-of-00059.parquet
train-00002-of-00059.parquet  train-00011-of-00059.parquet  train-00020-of-00059.parquet  train-00029-of-00059.parquet  train-00038-of-00059.parquet  train-00047-of-00059.parquet  train-00056-of-00059.parquet
train-00003-of-00059.parquet  train-00012-of-00059.parquet  train-00021-of-00059.parquet  train-00030-of-00059.parquet  train-00039-of-00059.parquet  train-00048-of-00059.parquet  train-00057-of-00059.parquet
train-00004-of-00059.parquet  train-00013-of-00059.parquet  train-00022-of-00059.parquet  train-00031-of-00059.parquet  train-00040-of-00059.parquet  train-00049-of-00059.parquet  train-00058-of-00059.parquet
train-00005-of-00059.parquet  train-00014-of-00059.parquet  train-00023-of-00059.parquet  train-00032-of-00059.parquet  train-00041-of-00059.parquet  train-00050-of-00059.parquet
train-00006-of-00059.parquet  train-00015-of-00059.parquet  train-00024-of-00059.parquet  train-00033-of-00059.parquet  train-00042-of-00059.parquet  train-00051-of-00059.parquet
train-00007-of-00059.parquet  train-00016-of-00059.parquet  train-00025-of-00059.parquet  train-00034-of-00059.parquet  train-00043-of-00059.parquet  train-00052-of-00059.parquet
train-00008-of-00059.parquet  train-00017-of-00059.parquet  train-00026-of-00059.parquet  train-00035-of-00059.parquet  train-00044-of-00059.parquet  train-00053-of-00059.parquet

# natural language instruction:

rename train- to valid-

# bash command:

for f in train-*; do mv "$f" "valid-${f#train-}"; done

# context

guy-dev-data-processing-lga1 [git:] [0] ~ > sqlite3 /mnt/efs/augment-lga1/user/guy/the_stack.sqlite
SQLite version 3.31.1 2020-01-27 19:55:54
Enter ".help" for usage hints.
sqlite> .tables
repo_and_lang_statistics  repo_statistics           the_stack

# natural language instruction:

show the columns of the second table

# bash command:

pragma table_info(repo_statistics);
