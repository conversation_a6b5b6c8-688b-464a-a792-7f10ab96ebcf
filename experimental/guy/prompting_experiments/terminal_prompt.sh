# Examples of English instructions followed by the correct bash command.
# Each example can include previous commands and some of their outputs.

# notes:
# - show a few of the last examples, with their truncated outputs
# - normalize the prompt, just have it be ">"

# Terminal tasks:
# - delete a git branch
# - rename multiple files: change extension from .txt to .txt.bak
# - sum the numbers in the second column
# - sync this directory with the corresponding directory under /mnt/efs/augment-las1
# - how much free memory
# - do what git suggested (push the branch to upstream), needs a failed git push output
# - k8s:
# -- find pods that start with guy-
# -- (with one pod found) kill that pod
# - make a POST request to a server
# - show top 5 largest dirs
# - up to research (when in  ~/augment/research/retrieval/bm25): cd ../../

# [[[History]]]

> git push
fatal: The current branch undo_final has no upstream branch.
To push the current branch and set the remote as upstream, use

    git push --set-upstream origin undo_final

# [[[Command]]]

> au push to there

# [[[Translation]]]

> git push --set-upstream origin undo_final
