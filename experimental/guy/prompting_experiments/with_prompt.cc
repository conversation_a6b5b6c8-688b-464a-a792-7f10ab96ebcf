// Here we write succinct, type-safe code that is easy to read and understand.
// Code is short and to the point, with no unneeded comments and explanations.
//
// We do explicit error handling for some subtle errors that would be difficult
// to debug, but for the most part we rely on the compiler or interpreter to
// raise exceptions when needed.

struct Person {
    char* name;
    std::tm dob;
    std::vector<Person*> parents;
}

int main() {
    Person alice = {
        "Alice",
        {tm_mday: 1, tm_mon: 4, tm_year: 1989},
        {}};

    std::cout << "Alice is " << age << "years old" << std::endl;
}
