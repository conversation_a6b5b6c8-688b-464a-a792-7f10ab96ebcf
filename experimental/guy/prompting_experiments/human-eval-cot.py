# [[[Example]]]

def has_close_elements(numbers: list[float], threshold: float) -> bool:
    """ Check if in given list of numbers, are any two numbers closer to each other than
    given threshold.
    >>> has_close_elements([1.0, 2.0, 3.0], 0.5)
    False
    >>> has_close_elements([1.0, 2.8, 3.0, 4.0, 5.0, 2.0], 0.3)
    True
    """
    # two nested loops to check all pairs of numbers
    for idx, elem in enumerate(numbers):
        for idx2, elem2 in enumerate(numbers):
            # only compare different positions, because numbers at the same
            # position are always equal (and therefore below the threshold)
            if idx != idx2:
                # remember to take absolute value of the difference when
                # computing the distance, because the difference can be
                # negative
                distance = abs(elem - elem2)
                if distance < threshold:
                    # we found two numbers at differen positions that are closer
                    # than the threshold so we can return True
                    return True

    # we went through all pairs of numbers and didn't find any pair that was
    # closer than the threshold, so we return False
    return False


# [[[Test]]]

def check(candidate):
    assert candidate([1.0, 2.0, 3.9, 4.0, 5.0, 2.2], 0.3) == True
    assert candidate([1.0, 2.0, 3.9, 4.0, 5.0, 2.2], 0.05) == False
    assert candidate([1.0, 2.0, 5.9, 4.0, 5.0], 0.95) == True
    assert candidate([1.0, 2.0, 5.9, 4.0, 5.0], 0.8) == False
    assert candidate([1.0, 2.0, 3.0, 4.0, 5.0, 2.0], 0.1) == True
    assert candidate([1.1, 2.2, 3.1, 4.1, 5.1], 1.0) == True
    assert candidate([1.1, 2.2, 3.1, 4.1, 5.1], 0.5) == False


check(has_close_elements)


# [[[Example]]]

from typing import List


def separate_paren_groups(paren_string: str) -> List[str]:
    """ Input to this function is a string containing multiple groups of nested parentheses. Your goal is to
    separate those group into separate strings and return the list of those.
    Separate groups are balanced (each open brace is properly closed) and not nested within each other
    Ignore any spaces in the input string.
    >>> separate_paren_groups('( ) (( )) (( )( ))')
    ['()', '(())', '(()())']
    """
    # plan:
    # - represent the current state of open/closed parens as a stack
    # - go through the parens in the string from left to right, and repeat the following:
    # - each open paren pushes to the stack, each closed paren pops the stack
    # - if we pop the stack and it's empty, we found a complete group

    # result will accumulate the completed groups
    result = []
    # current_string will hold the parens since the last complete group
    current_string = []
    # keep track of the stack depth
    current_depth = 0

    for c in paren_string:
        if c == '(':
            # push to the stack
            current_depth += 1
            current_string.append(c)
        elif c == ')':
            # pop the stack
            current_depth -= 1
            current_string.append(c)

            # if the stack is empty, we found a complete group
            if current_depth == 0:
                # add it to the result and clear the accumulated state
                result.append(''.join(current_string))
                current_string.clear()
        # no else clause: skip all other characters (including whitespace)

    # we finished scanning the string, so result contains all the completed groups
    return result


# [[[Test]]]

def check(candidate):
    assert candidate('(()()) ((())) () ((())()())') == [
        '(()())', '((()))', '()', '((())()())'
    ]
    assert candidate('() (()) ((())) (((())))') == [
        '()', '(())', '((()))', '(((())))'
    ]
    assert candidate('(()(())((())))') == [
        '(()(())((())))'
    ]
    assert candidate('( ) (( )) (( )( ))') == ['()', '(())', '(()())']


check(separate_paren_groups)


# [[[Example]]]

def truncate_number(number: float) -> float:
    """ Given a positive floating point number, it can be decomposed into
    and integer part (largest integer smaller than given number) and decimals
    (leftover part always smaller than 1).

    Return the decimal part of the number.
    >>> truncate_number(3.5)
    0.5
    """
    # compute the integer part
    integer_part = int(number)
    # subtract and return
    return number - integer_part


# [[[Test]]]

def check(candidate):
    assert candidate(3.5) == 0.5, candidate(3.5)
    assert abs(candidate(1.33) - 0.33) < 1e-6
    assert abs(candidate(123.456) - 0.456) < 1e-6

check(truncate_number)


# [[[Example]]]

def below_zero(operations: List[int]) -> bool:
    """ You're given a list of deposit and withdrawal operations on a bank account that starts with
    zero balance. Your task is to detect if at any point the balance of account fallls below zero, and
    at that point function should return True. Otherwise it should return False.
    >>> below_zero([1, 2, 3])
    False
    >>> below_zero([1, 2, -4, 5])
    True
    """
    # high-level plan:
    # - keep track of the current balance
    # - for each operation, add or subtract the amount
    # - if the balance falls below zero, return True
    # - otherwise return False

    # keep track of the current balance
    current_balance = 0

    for operation in operations:
        # add or subtract the amount
        current_balance += operation

        # if the balance falls below zero, return True
        if current_balance < 0:
            return True

    # otherwise return False
    return False


# [[[Test]]]

METADATA = {
    'author': 'jt',
    'dataset': 'test'
}


def check(candidate):
    assert candidate([]) == False
    assert candidate([1, 2, -3, 1, 2, -3]) == False
    assert candidate([1, 2, -4, 5, 6]) == True
    assert candidate([1, -1, 2, -2, 5, -5, 4, -4]) == False
    assert candidate([1, -1, 2, -2, 5, -5, 4, -5]) == True
    assert candidate([1, -2, 2, -2, 5, -5, 4, -4]) == True

check(below_zero)



# [[[Example]]]

from typing import List


def mean_absolute_deviation(numbers: List[float]) -> float:
    """ For a given list of input numbers, calculate Mean Absolute Deviation
    around the mean of this dataset.
    Mean Absolute Deviation is the average absolute difference between each
    element and a centerpoint (mean in this case):
    MAD = average | x - x_mean |
    >>> mean_absolute_deviation([1.0, 2.0, 3.0, 4.0])
    1.0
    """
    # high-level plan:
    # - compute the mean of the numbers
    # - for each number, compute the absolute difference from the mean
    # - return the average of those absolute differences

    # compute the mean
    mean = sum(numbers) / len(numbers)

    # compute the absolute difference from the mean for each number
    absolute_differences = [abs(number - mean) for number in numbers]

    # return the average of those absolute differences
    return sum(absolute_differences) / len(absolute_differences)
