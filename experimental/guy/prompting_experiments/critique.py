# The goal is to critique possible completions for a given code prompt.
# The examples are presented in the following way:
# [[[Example]]] followed by prompt
# [[[Completion A]]] followed by the first completion
# [[[Completion B]]] followed by the second completion
# [[[Critique]]] followed by a step-by-step discussion of which completion is
# better.
# [[[Answer]]] "A is better" or "B is better", the final answer

# [[[Example]]]
def sum_odd_numbers(x):
    """Return the sum of odd numbers in the given list."""

# [[[Completion A]]]
def sum_odd_numbers(x):
    """Return the sum of odd numbers in the given list."""
    return sum(x)

# [[[Completion B]]]
def sum_odd_numbers(x):
    """Return the sum of odd numbers in the given list."""
    return sum([i for i in x if i % 2])

# [[[Critique]]]
The prompt asks for a function that sums all the odd numbers.
Completion A sums all the numbers.
Completion B sums just the odd numbers.

# [[[Answer]]]
B is better

# [[[Example]]]
class Vector2D:
    """Represent a vector in 2D space."""
    def __init__(self, x, y):
        self.x = x
        self.y = y

    def __repr__(self):
        return f"Vector2D({self.x}, {self.y})"

    def __add__(self, other):
        return Vector2D(self.x + other.x, self.y + other.y)


def test_vector():

# [[[Completion A]]]
class Vector2D:
    """Represent a vector in 2D space."""
    def __init__(self, x, y):
        self.x = x
        self.y = y

    def __repr__(self):
        return f"Vector2D({self.x}, {self.y})"

    def __add__(self, other):
        return Vector2D(self.x + other.x, self.y + other.y)


def test_vector():
    """Test the Vector2D class."""
    v1 = Vector2D(1, 2)
    v2 = Vector2D(3, 4)
    v3 = v1 + v2
    assert v3.x == 4
    assert v3.y == 6


# [[[Completion B]]]
class Vector2D:
    """Represent a vector in 2D space."""
    def __init__(self, x, y):
        self.x = x
        self.y = y

    def __repr__(self):
        return f"Vector2D({self.x}, {self.y})"

    def __add__(self, other):
        return Vector2D(self.x + other.x, self.y + other.y)


def test_vector():
    """Test the Vector2D class."""
    v1 = Vector2D(1, 2)
    v2 = Vector2D(3, 4)
    v3 = v1 + v2
    print(v1, v2)

# [[[Critique]]]
The prompt asks for a function that tests the Vector2D class.
Completion A tests that addition is correct.
Completion B prints the vector.

# [[[Answer]]]
A is better

# [[[Example]]]
def reverse_string(s):
    """Returns the reversed string."""

# [[[Completion A]]]
def reverse_string(s):
    """Returns the reversed string."""
    return s[::-1]

# [[[Completion B]]]
def reverse_string(s):
    """Returns the reversed string."""
    return s

# [[[Critique]]]
The prompt asks for a function that reverses a string.
Completion A reverses the string.
Completion B returns the string unchanged.

# [[[Answer]]]
A is better

# [[[Example]]]
import numpy as np

def is_invertible(matrix):
    """Return whether the given matrix is invertible."""

# [[[Completion A]]]
import numpy as np

def is_invertible(matrix):
    """Return whether the given matrix is invertible."""
    return np.linalg.det(matrix) != 0

# [[[Completion B]]]
import numpy as np

def is_invertible(matrix):
    """Return whether the given matrix is invertile."""
    return np.trace(matrix * matrix) == 0

# [[[Critique]]]
Write a function that tests whether a matrix is invertible.
Completion A compares the determinant to zero, which is a criterion for whether a matrix is invertible.
Completion B computes the trace of the matrix square, which is not related to invertibality.

# [[[Result]]]
A is better

# [[[Example]]]
@dataclass
class Person:
    """Represents a person."""
    name: str
    age: int

def find_oldest(persons):

# [[[Completion A]]]
@dataclass
class Person:
    """Represents a person."""
    name: str
    age: int

def find_oldest(persons):
    """Return the oldest person in the given list."""
    return max(persons, key=lambda p: p.age)

# [[[Completion B]]]
@dataclass
class Person:
    """Represents a person."""
    name: str
    age: int

def find_oldest(persons):
    """Return the oldest person in the given list."""
    return max(persons, key=lambda p: p.name)

# [[[Critique]]]
