# Here we write verbose, meandering code that is difficult to read and understand,
# as a way to illustrate how not to code. This is not good code. The functions
# and classes are bloated, contain extaneous comments and TODOs, and need a
# lot of cleaning up and refactoring to bring it up to the standards of
# production code.
#
# We also check for every possible error, even ones that that are unimportant.
# Please do not write code like this, as it will not pass our rigorous code
# review process.

def forward(turtle):
    turtle.x += turtle.dx
    turtle.y += turtle.dy


def backward(turtle):
    turtle.x -= turtle.dx
    turtle.y -= turtle.dy


@dataclass
class Turtle:
####### GENERATION STARTS HERE ########################
     x: float
     y: float
     dx: float
     dy: float
     color: str = "black"
     pen_down: bool = True

     def forward(self):
         if self.pen_down:
             forward(self)

     def backward(self):
         if self.pen_down:
             backward(self)

     def left(self):
         self.dx, self.dy = -self.dy, self.dx

     def right(self):
         self.dx, self.dy = self.dy, -self.dx

     def penup(self):
         self.pen_down = False

     def pendown(self):
         self.pen_down = True

     def goto(self, x, y):
         self.x = x
         self.y = y

     def home(self):
         self.goto(0, 0)

     def circle(self, radius):
         self.penup()
         self.goto(self.x + radius, self.y)
         self.pendown()
         for i in range(360):
             self.forward()
             self.left()
         self.penup()

     def __str__(self):
         return f"Turtle({self.x}, {self.y}, {self.dx}, {self.dy}, {self.color}, {self.pen_down})"


def main():
    t = Turtle(0, 0, 1, 0)
    print(t)
    t.forward()
    print(t)
    t.backward()
    print(t)
    t.left()
    print(t)
    t.right()

if __name__ == "__main__":
    main()
