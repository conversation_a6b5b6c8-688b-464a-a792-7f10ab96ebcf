# We edit short code based on instructions. Every example has three parts:
#
# [[[Example]]] followed by a piece of code.
# [[[Instructions]]] followed by instructions on how to edit it.
# [[[Result]]] followed by the edited piece of code.
#
# We write succinct, type-safe code that is easy to read and understand.
# Code is short and to the point, with no unneeded comments and explanations.
#
# The code is written in a way that is easy to refactor and maintain.
# The code is written in a way that is easy to test.
# The code is written in a way that is easy to debug.
# The code is written in a way that is easy to extend.
#
# Code is documented with minimal docstrings, and with minimal comments that
# explain its purpose and nothing more.
#
# We do explicit error handling for some subtle errors that would be difficult
# to debug, but for the most part we rely on the compiler or interpreter to
# raise exceptions when needed.

# [[[Example]]]
def reverse_string(s):
    """
    Reverse a string.

    >>> reverse_string("abc")
    "cba"
    """
    return s[::-1]

# [[[Instructions]]]
# add type hints

# [[[Result]]]
def reverse_string(s: str) -> str:
    """
    Reverse a string.

    >>> reverse_string("abc")
    'cba'
    """
    return s[::-1]

# [[[Example]]]
"""A simple Flask server that returns the time."""

from flask import Flask
from datetime import datetime

app = Flask(__name__)

@app.route('/')
def get_current_time():
    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    return f"The current time is: {current_time}"

if __name__ == '__main__':
    app.run()

# [[[Instructons]]]
# add an endpoint /date that returns the current date

# [[[Result]]]
"""A simple Flask server that returns the time and date."""

from flask import Flask
from datetime import datetime

app = Flask(__name__)

@app.route('/')
def get_current_time():
    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    return f"The current time is: {current_time}"

@app.route('/date')
def get_current_date():
    current_date = datetime.now().strftime("%Y-%m-%d")
    return f"The current date is: {current_date}"

if __name__ == '__main__':
    app.run()

# [[[Example]]]
"""A turtle that can move in various directions."""

from dataclasses import dataclass
import math


@dataclass
class Turtle:
    """A turtle that can move in various directions."""
    x: float
    y: float
    dx: float = 1
    dy: float = 0


def forward(turtle):
    """Move the turtle one step forward."""
    turtle.x += turtle.dx
    turtle.y += turtle.dy


def backward(turtle):
    """Move the turtle one step backward."""
    turtle.x -= turtle.dx
    turtle.y -= turtle.dy


def turn(turtle, angle):
    """Turn the turtle by the given angle."""
    prev_dx, prev_dy = turtle.dx, turtle.dy
    turtle.dx = prev_dy * math.sin(angle) + prev_dx * math.cos(angle)
    turtle.dy = prev_dy * math.cos(angle) - prev_dx * math.sin(angle)

# [[[Instructions]]]
# add unit tests for forward, backward, and turn

# [[[Result]]]
"""A turtle that can move in various directions."""

from dataclasses import dataclass
import math


@dataclass
class Turtle:
    """A turtle that can move in various directions."""
    x: float
    y: float
    dx: float = 1
    dy: float = 0


def forward(turtle):
    """Move the turtle one step forward."""
    turtle.x += turtle.dx
    turtle.y += turtle.dy


def backward(turtle):
    """Move the turtle one step backward."""
    turtle.x -= turtle.dx
    turtle.y -= turtle.dy


def turn(turtle, angle):
    """Turn the turtle by the given angle."""
    prev_dx, prev_dy = turtle.dx, turtle.dy
    turtle.dx = prev_dy * math.sin(angle) + prev_dx * math.cos(angle)
    turtle.dy = prev_dy * math.cos(angle) - prev_dx * math.sin(angle)


def test_forward():
    turtle = Turtle(0, 0)
    forward(turtle)
    assert turtle.x == 1
    assert turtle.y == 0


def test_backward():
    turtle = Turtle(0, 0)
    backward(turtle)
    assert turtle.x == -1
    assert turtle.y == 0


def test_turn():
    turtle = Turtle(0, 0)
    turn(turtle, math.pi / 2)
    assert turtle.dx == 0
    assert turtle.dy == 1

    turn(turtle, math.pi / 2)
    assert turtle.dx == -1
    assert turtle.dy == 0


# [[[Example]]]
def draw_square(turtle, length):
    for _ in range(4):
        for _ in range(length):
            forward(turtle)
        turn(turtle, math.pi / 2)


# [[[Instructions]]]
# document the function

# [[[Result]]]
def draw_square(turtle, length):
    """Draw a square with the given length."""
    for _ in range(4):
        for _ in range(length):
            forward(turtle)
        turn(turtle, math.pi / 2)
