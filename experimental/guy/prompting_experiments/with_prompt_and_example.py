# Here we write succinct, type-safe code that is easy to read and understand.
# Code is short and to the point, with no unneeded comments and explanations.
#
# We do explicit error handling for some subtle errors that would be difficult
# to debug, but for the most part we rely on the compiler or interpreter to
# raise exceptions when needed.
#
# We use type hints to make our code more readable and to catch errors early.
#
# We use dataclasses to make our code more concise and to make it easier to
# work with data.
#
# Every example is a complete program that can be run as a script. It begins
# with a string of the form [[[Example]]], and continues with a short
# docstring explaining its purpose.
#


# [[[Example]]]
"""A turtle that can move in various directions."""

from dataclasses import dataclass
import math


@dataclass
class Turtle:
    """A turtle that can move in various directions."""
    x: float
    y: float
    dx: float = 1
    dy: float = 0


def forward(turtle):
    """Move the turtle one step forward."""
    turtle.x += turtle.dx
    turtle.y += turtle.dy


def backward(turtle):
    """Move the turtle one step backward."""
    turtle.x -= turtle.dx
    turtle.y -= turtle.dy


def turn(turtle, angle):
    """Turn the turtle by the given angle."""
    prev_dx, prev_dy = turtle.dx, turtle.dy
    turtle.dx = prev_dy * math.sin(angle) + prev_dx * math.cos(angle)
    turtle.dy = prev_dy * math.cos(angle) - prev_dx * math.sin(angle)


def test_turn():
    turtle = Turtle(0, 0)
    turn(turtle, math.pi / 2)
    assert turtle.dx == 0
    assert turtle.dy == 1

    turn(turtle, math.pi / 2)
    assert turtle.dx == -1
    assert turtle.dy == 0


# [[[Example]]]
import requests

def download_file(url):
    """Download a file from the given URL."""
    response = requests.get(url)
    if response.status_code != 200:
        raise RuntimeError(f"Failed to download {url}: {response.status_code}")
    return response.content


# [[[Example]]]
"""A simple Flask server that returns the time."""

from flask import Flask
from datetime import datetime

app = Flask(__name__)

@app.route('/')
def get_current_time():
    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    return f"The current time is: {current_time}"

if __name__ == '__main__':
    app.run()


# [[[Example]]]
"""A 2D vector class."""

class D2Vector:
    """A 2D vector class."""
    def __init__(self, x, y):
        self.x = x
        selfy = y

    def __add__(self, other):
        return D2Vector(self.x + other.x, self.y + other.y)

    def __sub__(self, other):
        return D2Vector(self.x - other.x, self.y - other.y)

    def __mul__(self, scalar):
        return D2Vector(self.x * scalar, self.y * scalar)

    def __
