# For each example, we start by writing out the pseudocode for the function,
# and then we implement that function in 100% valid Python.

# [[[Example]]]

def has_close_elements(numbers: list[float], threshold: float) -> bool:
    """ Check if in given list of numbers, are any two numbers closer to each other than
    given threshold.
    >>> has_close_elements([1.0, 2.0, 3.0], 0.5)
    False
    >>> has_close_elements([1.0, 2.8, 3.0, 4.0, 5.0, 2.0], 0.3)
    True
    """
    # pseudocode:
    # loop over all pairs in the list of numbers
    #   if a pair comes from the same position, skip it (because they are always equal)
    #   if distance(pair) < threshold, return True
    # return False

    # loop over all pairs
    for idx, elem in enumerate(numbers):
        for idx2, elem2 in enumerate(numbers):
            # if a pair comes from the same position, skip it
            if idx != idx2:
                # if distance(pair) < threshold, return True
                distance = abs(elem - elem2)
                if distance < threshold:
                    return True
    return False


# [[[Test]]]

def check(candidate):
    assert candidate([1.0, 2.0, 3.9, 4.0, 5.0, 2.2], 0.3) == True
    assert candidate([1.0, 2.0, 3.9, 4.0, 5.0, 2.2], 0.05) == False
    assert candidate([1.0, 2.0, 5.9, 4.0, 5.0], 0.95) == True
    assert candidate([1.0, 2.0, 5.9, 4.0, 5.0], 0.8) == False
    assert candidate([1.0, 2.0, 3.0, 4.0, 5.0, 2.0], 0.1) == True
    assert candidate([1.1, 2.2, 3.1, 4.1, 5.1], 1.0) == True
    assert candidate([1.1, 2.2, 3.1, 4.1, 5.1], 0.5) == False


check(has_close_elements)


# [[[Example]]]

from typing import List


def separate_paren_groups(paren_string: str) -> List[str]:
    """ Input to this function is a string containing multiple groups of nested parentheses. Your goal is to
    separate those group into separate strings and return the list of those.
    Separate groups are balanced (each open brace is properly closed) and not nested within each other
    Ignore any spaces in the input string.
    >>> separate_paren_groups('( ) (( )) (( )( ))')
    ['()', '(())', '(()())']
    """
    # pseudocode:
    # result = empty list
    # stack = empty stack
    # current_group = empty list
    # loop over characters
    #   if open paren: push to stack
    #   if closed paren: pop. if stack is now empty, add completed group to result
    # return result

    # initialize empty variables
    result = []
    current_group = []
    stack = []

    # loop over characters
    for c in paren_string:
        if c == '(':
            # push to the stack
            stack.append("(")
            current_group.append(c)
        elif c == ')':
            # pop the stack
            stack.pop()
            current_group.append(c)

            # if the stack is empty, add completed group to the result
            if len(stack) == 0:
                result.append(''.join(current_group))
                current_group.clear()

    return result


# [[[Test]]]

def check(candidate):
    assert candidate('(()()) ((())) () ((())()())') == [
        '(()())', '((()))', '()', '((())()())'
    ]
    assert candidate('() (()) ((())) (((())))') == [
        '()', '(())', '((()))', '(((())))'
    ]
    assert candidate('(()(())((())))') == [
        '(()(())((())))'
    ]
    assert candidate('( ) (( )) (( )( ))') == ['()', '(())', '(()())']


check(separate_paren_groups)


# [[[Example]]]

def below_zero(operations: List[int]) -> bool:
    """ You're given a list of deposit and withdrawal operations on a bank account that starts with
    zero balance. Your task is to detect if at any point the balance of account fallls below zero, and
    at that point function should return True. Otherwise it should return False.
    >>> below_zero([1, 2, 3])
    False
    >>> below_zero([1, 2, -4, 5])
    True
    """
    budget = 0

    for op in operations:
        budget += op

        if budget < 0:
            return True

    return False
