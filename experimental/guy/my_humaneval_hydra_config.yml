#
# This file contains an example of an evaluation config
#

# Sections:
#   Systems - specify the system configuration to evaluate
#   Tasks - specify the evaluation tasks for each system
#   Podspec - overide the default podspec, if necessary
#   Determined - name, workspace, project in the determined UI.

systems:
  - name: basic_rag
    model:
      # name: codegen-16b-indiana
      # Indiana-002 2B
      # name: codegen-2b-indiana
      # name: codegen-350M-multi
      name: starcoderbase_1b
      prompt:
        max_prefix_tokens: 4096
        max_suffix_tokens: 0
        max_prompt_tokens: 4096
        #fill_to_context_window: True
    generation_options:
      temperature: 0
      top_k: 0
      top_p: 0
      max_generated_tokens: 280
    retriever:
      name: null
      chunker: line_level
      max_chunk: 20
    # retriever:
    #   name: bm25
    #   chunker: line_level
    #   max_chunk: 20
    #   max_query_lines: 4
    experimental:
      use_fim_when_possible: False
      retriever_top_k: 25
      trim_on_dedent: True
      trim_on_max_lines: null


# Tasks
#   specify the evaluation tasks for each checkpoint
tasks:
  - name: humaneval
    # few_shot: true
    #limit: 2
  #- name: humaneval_fim
  #  variant: multiline_light
  #  # all the tests
  #  # variant: multiline
  #  # run the tests
  #  exec: true
  #  # pass@
  #  iterations: 1

# # Podspec - set the default podspec for all checkpoints
# # See gpt-neox/jobs/templates/podspecs/ for additional options
# # Use the following for small models (<=2B)
# # podspec: gpu-small.yaml
# # Use the following for larger models (>=2B)
# podspec: A40.yaml
#
# # Determined
# # name, workspace, project control location and display in the determined UI.
# #
# # IF YOU DO NOT WANT TO EXECUTE CODE:
# # To disable execution through hydra, choose the batch-eval.yaml below
#
# determined:
#   name: Hydra - 350M-multi, No Retrieval
#   workspace: Dev
#   project: playground
#   # relative to research/gpt-neox
#   # metaconfig: jobs/templates/batch-eval.yaml
#   metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml
