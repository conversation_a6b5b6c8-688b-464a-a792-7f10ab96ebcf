"""
Serve a simple request lookup website.

In order to run this in CW, one needs to authenticate with Google
to get access to BigQuery:

```bash
gcloud auth login
gcloud auth application-default login
```
"""

from flask import Flask, request
import time
from typing import Optional
from google.cloud import bigquery

from base.datasets.gcp_creds import get_gcp_creds
from experimental.guy.request_lookup.lookup_request import (
    find_locations,
    Location,
    get_request_url,
    get_requests_url,
)

app = Flask(__name__)


def _format_locations_as_html(locs: set[Location], request_id: Optional[str]) -> str:
    if not locs:
        return "<h3>No locations found.</h3>"

    if request_id:
        url_column_header = "Request link"
    else:
        url_column_header = "Support link"

    html = f"""\
    <table style="max-width: 600px; margin: 40px auto; padding: 20px; border: 1px solid #ccc; border-radius: 10px; box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);">
    <tr style="background-color: #f5f5f5">
        <th style="border: 1px solid #ddd; padding: 4px; width: 5%; text-align: left">#</th>
        <th style="border: 1px solid #ddd; padding: 4px; width: 25%; text-align: left">{url_column_header}</th>
        <th style="border: 1px solid #ddd; padding: 4px; width: 35%; text-align: left">Shard namespace</th>
        <th style="border: 1px solid #ddd; padding: 4px; width: 30%; text-align: left">Tenant</th>
    </tr>

"""

    for i, loc in enumerate(locs):
        html += f"<tr><td>{i+1}</td>"

        if request_id:
            html += f"<td><a href='{get_request_url(loc, request_id)}'>request</a></td>"
        else:
            html += f"<td><a href='{get_requests_url(loc)}'>support site</a></td>"

        html += f"""\
<td>{loc.shard_namespace}</td>
<td>{loc.tenant}</td>
"""

        html += "</tr>"

    html += "</table>"
    return html


HEADER_HTML = """\
<html>
<title>Request Lookup</title>
<head>
  <style>
    body {
      font-family: Droid Sans, sans-serif;
    }
    h1, h2, h3, h4, h5, h6, p {
        text-align: center;
    }
    table td, table th {
        text-align: left;
    }
    h1, h2 {
        margin-top: 50px;
    }
  </style>
</head>
"""

FOOTER_HTML = """\
</html>
"""

FORM_HTML = """\
<form action="/" method="get" style="max-width: 400px; margin: 40px auto; padding: 20px; border: 1px solid #ccc; border-radius: 10px; box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);">
    <label for="request_id" style="display: block; margin-bottom: 8px;">Request ID:</label>
    <input type="text" id="request_id" name="request_id" style="padding: 8px; width: 100%; box-sizing: border-box; margin-bottom: 16px;"><br>
    <label for="session_id" style="display: block; margin-bottom: 8px;">Session ID:</label>
    <input type="text" id="session_id" name="session_id" style="padding: 8px; width: 100%; box-sizing: border-box; margin-bottom: 16px;"><br>
    <label for="user_id" style="display: block; margin-bottom: 8px;">User ID:</label>
    <input type="text" id="user_id" name="user_id" style="padding: 8px; width: 100%; box-sizing: border-box; margin-bottom: 16px;"><br>

    <label for="max_days" style="display: inline-block; margin-bottom: 8px;">Limit to past </label>
    <input type="text" id="max_days" name="max_days" value="14" style="padding: 5px; width: 12%; box-sizing: border-box; margin-bottom: 16px;">
    <label for="max_days" style="display: inline-block; margin-bottom: 8px;"> days</label>
    <br>

    <input type="submit" value="Submit" id="submitButton" style="padding: 8px; width: 100%; background-color: #2f92db; color: #fff; border: none; border-radius: 5px; cursor: pointer;">
</form>
<script>
    document.getElementById("submitButton").addEventListener("click", function(){
        this.value = "Lookup in progress...";
    });
</script>

"""


# This should really be a /lookup endpoint, but that doesn't work.
# Our HTTPS server proxy drops any URL paths, so we need all requests to go to
# the root endpoint.
def lookup():
    # request_id = request.form.get("request_id")
    # session_id = request.form.get("session_id")
    # user_id = request.form.get("user_id")
    # max_days = int(request.form.get("max_days"))

    request_id = request.args.get("request_id")
    session_id = request.args.get("session_id")
    user_id = request.args.get("user_id")
    max_days = int(request.args.get("max_days"))

    query_html = ""
    if request_id:
        query_html += f"<p>Request ID: {request_id}</p>"
    if session_id:
        query_html += f"<p>Session ID: {session_id}</p>"
    if user_id:
        query_html += f"<p>User ID: {user_id}</p>"

    if not (request_id or session_id or user_id):
        return (
            "Must provide at least one of request_id, session_id, or user_id",
            400,
        )

    start = time.time()
    locs = find_locations(
        app.bigquery_client, request_id, session_id, user_id, max_days
    )
    elapsed = time.time() - start

    locations_html = _format_locations_as_html(locs, request_id)

    html = f"""\
{HEADER_HTML}
<body>
<h2>Lookup results</h2>
{query_html}
<p>Lookup took {elapsed:.1f} seconds.</p>
{locations_html}
<h2>Lookup another</h2>
{FORM_HTML}
</body>
{FOOTER_HTML}
"""

    return html


@app.route("/", methods=["GET"])
def index():
    if request.args.get("request_id", None) is not None:
        return lookup()

    return f"""\
{HEADER_HTML}
<body>
<h1>Request lookup</h1>
<p>Find requests, sessions, and users across all Augment tenants.</p>
{FORM_HTML}
</body>
{FOOTER_HTML}
"""


if __name__ == "__main__":
    app.bigquery_client = bigquery.Client(
        project="system-services-prod", credentials=get_gcp_creds()[0]
    )
    app.run(port=9191, host="0.0.0.0", threaded=True, debug=True)
