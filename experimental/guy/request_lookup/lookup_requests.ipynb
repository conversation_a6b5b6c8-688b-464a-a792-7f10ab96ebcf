{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "In order to run this in a CW dev container, the following is needed:\n", "\n", "Authenticate with Google:\n", "```bash\n", "gcloud auth login\n", "gcloud auth application-default login\n", "```\n", "\"\"\"\n", "\n", "from dataclasses import dataclass\n", "from typing import Optional\n", "\n", "from google.cloud import bigquery\n", "\n", "from base.datasets.gcp_creds import get_gcp_creds\n", "from base.datasets.tenants import DOGFOOD\n", "\n", "\n", "gcp_creds, _ = get_gcp_creds()\n", "\n", "project_id = \"system-services-prod\"\n", "assert DOGFOOD.project_id == project_id, f\"Unexpected project ID: {DOGFOOD.project_id}\"\n", "\n", "bigquery_client = bigquery.Client(project=project_id, credentials=gcp_creds)\n", "\n", "\n", "@dataclass(frozen=True)\n", "class Location:\n", "    tenant: str\n", "    shard_namespace: str\n", "\n", "\n", "def find_locations(\n", "        bigquery_client: bigquery.Client,\n", "        request_id: Optional[str] = None,\n", "        session_id: Optional[str] = None,\n", "        user_id: Optional[str] = None,\n", "        ) -> set[Location]:\n", "    \"\"\"Find the locations of a request, session, or user.\"\"\"\n", "\n", "    tables = [\n", "        \"system-services-prod.us_staging_request_insight_analytics_dataset.customers_request_metadata\",\n", "        \"system-services-prod.us_prod_request_insight_analytics_dataset.customers_request_metadata\",\n", "        \"system-services-dev.dev_request_insight_analytics_dataset.customers_request_metadata\",\n", "    ]\n", "\n", "    queries = []\n", "    if request_id:\n", "        queries.append(f\"request_id = '{request_id}'\")\n", "    if session_id:\n", "        queries.append(f\"session_id = '{session_id}'\")\n", "    if user_id:\n", "        queries.append(f\"user_id = '{user_id}'\")\n", "\n", "    if not queries:\n", "        raise ValueError(\"Must provide at least one of request_id, session_id, or user_id\")\n", "\n", "    where_clause = \" AND \".join(queries)\n", "\n", "    locations = set()\n", "\n", "    for table in tables:\n", "        query = f\"\"\"\n", "            SELECT request_id, session_id, user_id, tenant, shard_namespace\n", "            FROM `{table}` \n", "            WHERE {where_clause}\n", "            LIMIT 20\n", "        \"\"\"\n", "        print(query)\n", "\n", "        query_job = bigquery_client.query(query)\n", "\n", "        # Get the results\n", "        results = query_job.result()\n", "\n", "        # Print the results\n", "        for row in results:\n", "            locations.add(Location(row.tenant, row.shard_namespace))\n", "\n", "    return locations\n", "\n", "\n", "def get_support_site_url(request_location: Location) -> str:\n", "    return f\"https://support.{request_location.shard_namespace}.t.us-central1.prod.augmentcode.com/t/{request_location.tenant}\"\n", "\n", "\n", "def get_request_url(request_location: Location, request_id: str) -> str:\n", "    return f\"{get_support_site_url(request_location)}/request/{request_id}\"\n", "\n", "\n", "def test_get_urls():\n", "    shard_namespace = \"staging-shard-0\"\n", "    tenant = \"dogfood-shard\"\n", "    expected = \"https://support.staging-shard-0.t.us-central1.prod.augmentcode.com/t/dogfood-shard\"\n", "    actual = get_support_site_url(Location(tenant, shard_namespace))\n", "    assert expected == actual, \"Expected: \" + expected + \" Actual: \" + actual\n", "\n", "    request_id = \"aa7aee42-b553-4e38-934c-5d1fcdc19913\"\n", "    expected = \"https://support.staging-shard-0.t.us-central1.prod.augmentcode.com/t/dogfood-shard/request/aa7aee42-b553-4e38-934c-5d1fcdc19913\"\n", "    actual = get_request_url(Location(tenant, shard_namespace), request_id)\n", "    assert expected == actual, \"Expected: \" + expected + \" Actual: \" + actual\n", "\n", "\n", "test_get_urls()\n", "\n", "\n", "# # locs = find_locations(bigquery_client, request_id=\"1565a72a-b33a-41ec-8b14-63ce6a55804f\")\n", "# locs = find_locations(bigquery_client, user_id=\"guy\")\n", "\n", "# for loc in locs:\n", "#     print(loc)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}