"""
Serve a simple request lookup website.

In order to run this in CW, one needs to authenticate with Google
to get access to BigQuery:

```bash
gcloud auth login
gcloud auth application-default login
```
"""

import argparse
from dataclasses import dataclass
from typing import Optional

from google.cloud import bigquery

from base.datasets.gcp_creds import get_gcp_creds
from base.datasets.tenants import DOGFOOD


@dataclass(frozen=True)
class Location:
    tenant: str
    shard_namespace: str

    def __str__(self):
        return f"shard_namespace={self.shard_namespace} tenant={self.tenant}"


def find_locations(
    bigquery_client: bigquery.Client,
    request_id: Optional[str] = None,
    session_id: Optional[str] = None,
    user_id: Optional[str] = None,
    max_days: Optional[int] = None,
) -> set[Location]:
    """Find the locations of a request, session, or user."""

    tables = [
        "system-services-prod.us_staging_request_insight_analytics_dataset.customers_request_metadata",
        "system-services-prod.us_prod_request_insight_analytics_dataset.customers_request_metadata",
        "system-services-prod.eu_prod_request_insight_analytics_dataset.customers_request_metadata",
        # This is probably not needed because most dev deploys don't export to BigQuery,
        # but leaving it here in case we want to enable it in the future.
        # "system-services-dev.dev_request_insight_analytics_dataset.customers_request_metadata",
    ]

    queries = []
    if request_id:
        queries.append(f"request_id = '{request_id}'")
    if session_id:
        queries.append(f"session_id = '{session_id}'")
    if user_id:
        queries.append(f"user_id = '{user_id}'")

    if not queries:
        raise ValueError(
            "Must provide at least one of request_id, session_id, or user_id"
        )

    where_clause = " AND ".join(queries)

    locations = set()

    for table in tables:
        # Setting LIMIT 20 as a very high upper limit.
        # Usually it should find 1-2 locations.
        #
        # TODO(guy) this might not find all locations for session_id or user_id:
        # it might find multiple requests from one location, hit the limit, and
        # therefore not find other locations.
        query = f"""
            SELECT tenant, shard_namespace
            FROM `{table}`
            WHERE {where_clause} AND TIMESTAMP_DIFF(CURRENT_TIMESTAMP(), time, DAY) <= {max_days}
            LIMIT 20
        """

        query_job = bigquery_client.query(query)
        results = query_job.result()
        for row in results:
            locations.add(Location(row.tenant, row.shard_namespace))

    return locations


def get_support_site_url(request_location: Location) -> str:
    return f"https://support.{request_location.shard_namespace}.t.us-central1.prod.augmentcode.com/t/{request_location.tenant}"


def get_requests_url(request_location: Location) -> str:
    return f"{get_support_site_url(request_location)}/requests"


def get_request_url(request_location: Location, request_id: str) -> str:
    return f"{get_support_site_url(request_location)}/request/{request_id}"


def main():
    parser = argparse.ArgumentParser(
        description="Find locations (tenants and namespaces) of a request, session, or user"
    )
    parser.add_argument("--request_id", type=str, help="Request ID to search for")
    parser.add_argument("--session_id", type=str, help="Session ID to search for")
    parser.add_argument("--user_id", type=str, help="User ID to search for")
    parser.add_argument(
        "--max_days",
        type=int,
        default=14,
        help="Max days to search backward (default 14)",
    )
    args = parser.parse_args()

    if not (args.request_id or args.session_id or args.user_id):
        raise ValueError(
            "Must provide at least one of request_id, session_id, or user_id"
        )

    gcp_creds, _ = get_gcp_creds()
    project_id = "system-services-prod"
    assert (
        DOGFOOD.project_id == project_id
    ), f"Unexpected project ID: {DOGFOOD.project_id}"
    bigquery_client = bigquery.Client(project=project_id, credentials=gcp_creds)

    locs = find_locations(
        bigquery_client,
        request_id=args.request_id,
        session_id=args.session_id,
        user_id=args.user_id,
        max_days=args.max_days,
    )

    print(f"\nFound {len(locs)} locations.")

    for i, loc in enumerate(locs):
        print(f"\n{i+1}.\nLocation: {loc}")
        print(f"Support URL: {get_requests_url(loc)}")
        if args.request_id:
            print(f"Request URL: {get_request_url(loc, args.request_id)}")


if __name__ == "__main__":
    main()
