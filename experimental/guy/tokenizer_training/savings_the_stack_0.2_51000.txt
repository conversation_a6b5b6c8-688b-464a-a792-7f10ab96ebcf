File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/genie-bt-mesh-stack.test.jsonl
Token counts: codegen=4298.468053491828 new=3717.323922734027 savings=13.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/wt.test.jsonl
Token counts: codegen=4099.266829865361 new=3554.140758873929 savings=13.3%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/FreeRDP.test.jsonl
Token counts: codegen=4710.477130044843 new=4191.5686098654705 savings=11.0%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/Montage.mem.jsonl
Token counts: codegen=6109.629520295203 new=5749.29926199262 savings=5.9%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/epics-base.test.jsonl
Token counts: codegen=5949.489376523859 new=5598.850923023337 savings=5.9%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/clam.mem.jsonl
Token counts: codegen=5796.107260726073 new=5427.158745874587 savings=6.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/Learning-DIY-RTOS.test.jsonl
Token counts: codegen=5519.8687315634215 new=5134.3486725663715 savings=7.0%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/superlu.test.jsonl
Token counts: codegen=5441.528636884307 new=5060.583333333333 savings=7.0%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/Adrenaline.test.jsonl
Token counts: codegen=5431.2287822878225 new=5049.338347998864 savings=7.0%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/optee_test.test.jsonl
Token counts: codegen=5654.1591482207905 new=5262.947604370972 savings=6.9%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/zpl.mem.jsonl
Token counts: codegen=5585.114700246373 new=5197.687106487818 savings=6.9%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/rgbds.mem.jsonl
Token counts: codegen=5552.22300405954 new=5167.0208389715835 savings=6.9%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/fastpm.test.jsonl
Token counts: codegen=5525.997327632283 new=5140.787814003207 savings=7.0%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/Squirrel-Engine.mem.jsonl
Token counts: codegen=5666.192317476469 new=5234.402442126685 savings=7.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/solanum.mem.jsonl
Token counts: codegen=5560.142446732104 new=5139.780703854441 savings=7.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/stellar-core.test.jsonl
Token counts: codegen=5543.677707305615 new=5119.442565186751 savings=7.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/Libftest.mem.jsonl
Token counts: codegen=5472.466898148148 new=5053.758796296296 savings=7.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/FreeRDP.mem.jsonl
Token counts: codegen=5362.962401688087 new=4936.860924611548 savings=7.9%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/solanum.test.jsonl
Token counts: codegen=5330.907099697885 new=4908.023791540785 savings=7.9%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/rtthread-nano.test.jsonl
Token counts: codegen=5512.964010282776 new=5078.076202717591 savings=7.9%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/EsenthelEngine.mem.jsonl
Token counts: codegen=5498.66991499067 new=5045.338067592785 savings=8.2%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/fastpm.mem.jsonl
Token counts: codegen=5464.004598405886 new=5012.190476190476 savings=8.3%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/PigDev.test.jsonl
Token counts: codegen=5459.1330694275275 new=5006.975436459602 savings=8.3%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/CloverBootloader.mem.jsonl
Token counts: codegen=5710.639155327533 new=5086.715369172429 savings=10.9%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/PigDev.mem.jsonl
Token counts: codegen=5710.26320803107 new=5085.116948779952 savings=10.9%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/xfdashboard.test.jsonl
Token counts: codegen=5708.6054744525545 new=5084.065109489051 savings=10.9%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/rgbds.test.jsonl
Token counts: codegen=5706.28384979949 new=5082.129493255559 savings=10.9%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/rakun.mem.jsonl
Token counts: codegen=5690.3725675283185 new=5068.011545164101 savings=10.9%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/Adrenaline.mem.jsonl
Token counts: codegen=5676.220715522216 new=5054.989829774957 savings=10.9%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/Validity90.test.jsonl
Token counts: codegen=5688.259251259899 new=5066.904175665947 savings=10.9%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/xfdashboard.mem.jsonl
Token counts: codegen=5700.6320660686315 new=5079.02876263705 savings=10.9%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/epk2extract.test.jsonl
Token counts: codegen=5701.165743108838 new=5079.963199772663 savings=10.9%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/zpl.test.jsonl
Token counts: codegen=5693.56086494151 new=5073.32534562212 savings=10.9%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/Montage.test.jsonl
Token counts: codegen=5709.418391746943 new=5103.589191774271 savings=10.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/clam.test.jsonl
Token counts: codegen=5701.715520762424 new=5095.118924438393 savings=10.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/optee_test.mem.jsonl
Token counts: codegen=5741.767840280588 new=5133.134898151895 savings=10.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/CloverBootloader.test.jsonl
Token counts: codegen=5716.5113884555385 new=5081.448985959439 savings=11.1%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/Validity90.mem.jsonl
Token counts: codegen=5742.877414146433 new=5107.7360740234735 savings=11.1%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/aerospike-client-c.mem.jsonl
Token counts: codegen=5739.661637001909 new=5107.196772802858 savings=11.0%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/superlu.mem.jsonl
Token counts: codegen=5693.118130705519 new=5067.489994558975 savings=11.0%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/ZFSin.test.jsonl
Token counts: codegen=5715.700185439971 new=5089.450200394807 savings=11.0%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/lk2nd.mem.jsonl
Token counts: codegen=5659.100748770675 new=5047.06465131873 savings=10.8%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/genie-bt-mesh-stack.mem.jsonl
Token counts: codegen=5522.7417265103195 new=4917.276452568673 savings=11.0%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/Squirrel-Engine.test.jsonl
Token counts: codegen=5532.289647577092 new=4924.931317581098 savings=11.0%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/wt.mem.jsonl
Token counts: codegen=5520.420660590023 new=4906.358080956581 savings=11.1%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/rakun.test.jsonl
Token counts: codegen=5516.7607343941245 new=4903.116425948592 savings=11.1%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/lk2nd.test.jsonl
Token counts: codegen=5495.308675184936 new=4885.2037179364015 savings=11.1%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/FreeFlyOS.test.jsonl
Token counts: codegen=5492.633475694611 new=4882.430298958683 savings=11.1%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/Learning-DIY-RTOS.mem.jsonl
Token counts: codegen=5395.543683562207 new=4786.386605228341 savings=11.3%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/Libftest.test.jsonl
Token counts: codegen=5390.965587967184 new=4782.348222424795 savings=11.3%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/rtthread-nano.mem.jsonl
Token counts: codegen=5463.953859210291 new=4850.68567982848 savings=11.2%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/Open-Vehicle-Monitoring-System-3.mem.jsonl
Token counts: codegen=5712.02385677308 new=5079.775323554789 savings=11.1%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/EsenthelEngine.test.jsonl
Token counts: codegen=5733.047109556162 new=5105.354094625931 savings=10.9%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/epk2extract.mem.jsonl
Token counts: codegen=5728.631794414495 new=5101.816140407766 savings=10.9%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/Open-Vehicle-Monitoring-System-3.test.jsonl
Token counts: codegen=5816.3772858517805 new=5180.893768046199 savings=10.9%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/epics-base.mem.jsonl
Token counts: codegen=5755.465672581343 new=5126.268285006098 savings=10.9%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/stellar-core.mem.jsonl
Token counts: codegen=5719.875 new=5094.7004599314005 savings=10.9%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/aerospike-client-c.test.jsonl
Token counts: codegen=5720.53071864908 new=5095.8303178864635 savings=10.9%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/FreeFlyOS.mem.jsonl
Token counts: codegen=5711.53245089667 new=5087.340423880133 savings=10.9%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/ZFSin.mem.jsonl
Token counts: codegen=5742.0952525867315 new=5117.944309190505 savings=10.9%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/streamex.mem.jsonl
Token counts: codegen=5742.295298447994 new=5117.220088794445 savings=10.9%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/jetty.project.mem.jsonl
Token counts: codegen=5441.30029632212 new=4835.641973156702 savings=11.1%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/deeplearning4j.test.jsonl
Token counts: codegen=5343.375445797726 new=4743.676367673777 savings=11.2%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/FXGLGames.test.jsonl
Token counts: codegen=5335.569221468395 new=4736.720729495533 savings=11.2%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/gctoolkit.mem.jsonl
Token counts: codegen=5314.545925010851 new=4717.81636673233 savings=11.2%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/dynamic-support.test.jsonl
Token counts: codegen=5309.857561723254 new=4713.210242228368 savings=11.2%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/PinDroid.mem.jsonl
Token counts: codegen=5302.424970906068 new=4706.220282626767 savings=11.2%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/jetty.project.test.jsonl
Token counts: codegen=5219.681489644443 new=4628.810682915761 savings=11.3%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/java-iso-tools.mem.jsonl
Token counts: codegen=5205.500726439157 new=4615.9448551964615 savings=11.3%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/gctoolkit.test.jsonl
Token counts: codegen=5199.51707914411 new=4610.55787574117 savings=11.3%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/openzaly.test.jsonl
Token counts: codegen=5208.283378308918 new=4615.031354407607 savings=11.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/divolte-collector.test.jsonl
Token counts: codegen=5203.805512771146 new=4610.961686561417 savings=11.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/threetenbp.test.jsonl
Token counts: codegen=5206.802255904124 new=4613.251129554266 savings=11.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/java-tutorial.test.jsonl
Token counts: codegen=5194.156218047035 new=4601.839851738241 savings=11.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/learn-java-codes.test.jsonl
Token counts: codegen=5172.660487742838 new=4582.077994340402 savings=11.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/java-tron.mem.jsonl
Token counts: codegen=5091.252113970589 new=4503.172579656863 savings=11.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/jamonapi.test.jsonl
Token counts: codegen=5086.201713324155 new=4498.620131558819 savings=11.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/Upchain-wallet.mem.jsonl
Token counts: codegen=5069.521855676384 new=4483.51006731853 savings=11.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/cxf-spring-boot-starter.test.jsonl
Token counts: codegen=5067.816332744656 new=4481.980512758054 savings=11.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/java-tutorial.mem.jsonl
Token counts: codegen=5033.747024708512 new=4451.304929620009 savings=11.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/Audinaut.test.jsonl
Token counts: codegen=5030.296047073023 new=4448.155431502716 savings=11.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/kkbinlog.mem.jsonl
Token counts: codegen=5022.253576268634 new=4440.844421020931 savings=11.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/dokit.test.jsonl
Token counts: codegen=5015.782187744302 new=4434.866648625894 savings=11.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/dynamic-support.mem.jsonl
Token counts: codegen=5003.0279870828845 new=4422.604742255711 savings=11.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/dokit.mem.jsonl
Token counts: codegen=4984.119077380952 new=4405.2414285714285 savings=11.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/vulkanbook.mem.jsonl
Token counts: codegen=4922.831028825873 new=4349.232393953597 savings=11.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/spoofax.mem.jsonl
Token counts: codegen=4882.627496449172 new=4313.384358966927 savings=11.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/java-iso-tools.test.jsonl
Token counts: codegen=4878.55657209033 new=4309.758309206717 savings=11.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/h2database.mem.jsonl
Token counts: codegen=4835.267712750919 new=4267.107888040712 savings=11.8%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/testlink-java-api.test.jsonl
Token counts: codegen=4833.300994462651 new=4265.168324104418 savings=11.8%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/PinDroid.test.jsonl
Token counts: codegen=4831.093288533755 new=4263.145777451507 savings=11.8%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/cxf-spring-boot-starter.mem.jsonl
Token counts: codegen=4826.702919193343 new=4259.21114088281 savings=11.8%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/quincy.mem.jsonl
Token counts: codegen=4807.553855655055 new=4242.096791398815 savings=11.8%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/Quests.mem.jsonl
Token counts: codegen=4795.276382050134 new=4231.117250447627 savings=11.8%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/openremote.test.jsonl
Token counts: codegen=4778.275979083222 new=4215.510736537606 savings=11.8%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/Quests.test.jsonl
Token counts: codegen=4774.082432695246 new=4211.780096129803 savings=11.8%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/FXGLGames.mem.jsonl
Token counts: codegen=4757.339400813301 new=4196.964867631194 savings=11.8%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/Audinaut.mem.jsonl
Token counts: codegen=4749.926782167292 new=4190.154491282277 savings=11.8%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/quincy.test.jsonl
Token counts: codegen=4743.6558876187855 new=4184.561355185237 savings=11.8%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/Android-Cookbook-Examples.test.jsonl
Token counts: codegen=4734.122242066218 new=4176.063717543619 savings=11.8%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/deeplearning4j.mem.jsonl
Token counts: codegen=4539.267498670987 new=3995.4934055641343 savings=12.0%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/jamonapi.mem.jsonl
Token counts: codegen=4528.246322694588 new=3985.5796896682227 savings=12.0%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/consulo.mem.jsonl
Token counts: codegen=3969.194836403968 new=3476.09066075133 savings=12.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/openzaly.mem.jsonl
Token counts: codegen=3978.703789920771 new=3480.647124887691 savings=12.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/Upchain-wallet.test.jsonl
Token counts: codegen=3975.7795434609657 new=3478.0282123987677 savings=12.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/spoofax.test.jsonl
Token counts: codegen=3967.7496286348005 new=3470.97016869137 savings=12.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/kkbinlog.test.jsonl
Token counts: codegen=3966.6110444421843 new=3469.92028882335 savings=12.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/java-tron.test.jsonl
Token counts: codegen=3958.3668334611193 new=3461.4291190831686 savings=12.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/openremote.mem.jsonl
Token counts: codegen=3929.532888313241 new=3435.316429211916 savings=12.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/divolte-collector.mem.jsonl
Token counts: codegen=3924.4562554680665 new=3430.7028950926588 savings=12.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/Android-Cookbook-Examples.mem.jsonl
Token counts: codegen=3908.0351859177217 new=3416.245470727848 savings=12.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/vulkanbook.test.jsonl
Token counts: codegen=3897.881274759061 new=3407.0024438794617 savings=12.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/learn-java-codes.mem.jsonl
Token counts: codegen=3868.7849344764954 new=3380.6932015702205 savings=12.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/consulo.test.jsonl
Token counts: codegen=3736.239741059737 new=3260.2819387322256 savings=12.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/testlink-java-api.mem.jsonl
Token counts: codegen=3732.8893288640047 new=3257.177103285693 savings=12.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/streamex.test.jsonl
Token counts: codegen=3732.9821421996394 new=3257.1960676019 savings=12.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/h2database.test.jsonl
Token counts: codegen=3726.754959245352 new=3251.150068687609 savings=12.8%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/threetenbp.mem.jsonl
Token counts: codegen=3732.979734668518 new=3256.211808347343 savings=12.8%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/okta-sdk-golang.test.jsonl
Token counts: codegen=3731.0302947299224 new=3254.5470399181913 savings=12.8%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/go-spring.mem.jsonl
Token counts: codegen=3721.0148298924105 new=3246.1568951730155 savings=12.8%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/kubermatic.test.jsonl
Token counts: codegen=3714.598447705028 new=3240.85839379082 savings=12.8%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/drago.mem.jsonl
Token counts: codegen=3708.474986911231 new=3235.6175732519723 savings=12.8%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/Xray-core.test.jsonl
Token counts: codegen=3702.7059882611356 new=3230.8208742933275 savings=12.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/gateway-api.test.jsonl
Token counts: codegen=3701.271626484347 new=3229.591813602015 savings=12.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/influx-stress.test.jsonl
Token counts: codegen=3712.423560294144 new=3240.964023085636 savings=12.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/gochain.mem.jsonl
Token counts: codegen=3708.049914584668 new=3239.154975443092 savings=12.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/gateway-api.mem.jsonl
Token counts: codegen=3704.5415667678976 new=3236.173779459037 savings=12.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/go-diagrams.mem.jsonl
Token counts: codegen=3698.5279971666373 new=3231.096936426421 savings=12.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/istio-operator.test.jsonl
Token counts: codegen=3698.309338762503 new=3230.908612906081 savings=12.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/flamingo.test.jsonl
Token counts: codegen=3696.1541101342627 new=3229.0671666873045 savings=12.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/provider-aws.mem.jsonl
Token counts: codegen=3695.4008831250108 new=3229.9364601985294 savings=12.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/agollo.mem.jsonl
Token counts: codegen=3693.3633600250128 new=3228.1668548401103 savings=12.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/go-diagrams.test.jsonl
Token counts: codegen=3691.2052025127546 new=3226.325998681151 savings=12.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/mu.test.jsonl
Token counts: codegen=3690.0052379630915 new=3225.2840467600945 savings=12.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/custom-script-extension-linux.test.jsonl
Token counts: codegen=3689.481133384199 new=3224.8349344523826 savings=12.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/go-glint.test.jsonl
Token counts: codegen=3689.8925178559048 new=3225.26976284585 savings=12.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/cli.mem.jsonl
Token counts: codegen=3687.3585111542193 new=3223.0979977830125 savings=12.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/go-mc.mem.jsonl
Token counts: codegen=3685.9276550126224 new=3222.3217657433343 savings=12.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/drago.test.jsonl
Token counts: codegen=3684.0636555275255 new=3220.728306320628 savings=12.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/influx-stress.mem.jsonl
Token counts: codegen=3693.3310858581503 new=3230.5258919487505 savings=12.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/operator.mem.jsonl
Token counts: codegen=3694.2025288147256 new=3231.54713573026 savings=12.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/jit-compiler.mem.jsonl
Token counts: codegen=3688.448757124219 new=3226.5971297122846 savings=12.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/fabric.mem.jsonl
Token counts: codegen=3699.27692206316 new=3247.9652241240137 savings=12.2%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/mgmt.mem.jsonl
Token counts: codegen=3696.9719738115946 new=3246.7156523308668 savings=12.2%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/jit-compiler.test.jsonl
Token counts: codegen=3695.030762167126 new=3245.031811622721 savings=12.2%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/Knitter.test.jsonl
Token counts: codegen=3701.479499120693 new=3252.704666840357 savings=12.1%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/mgmt.test.jsonl
Token counts: codegen=3700.5030247837117 new=3252.0834092239643 savings=12.1%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/fabric.test.jsonl
Token counts: codegen=3714.6256560047505 new=3268.790688343578 savings=12.0%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/agollo.test.jsonl
Token counts: codegen=3713.856336039534 new=3268.117896223085 savings=12.0%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/circuit.test.jsonl
Token counts: codegen=3713.3863811357073 new=3267.717949951877 savings=12.0%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/okta-sdk-golang.mem.jsonl
Token counts: codegen=3709.542912957151 new=3264.4660379171205 savings=12.0%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/gogstash.test.jsonl
Token counts: codegen=3708.0702214825246 new=3263.1972698758163 savings=12.0%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/gohan.test.jsonl
Token counts: codegen=3706.3633180931674 new=3261.759343926847 savings=12.0%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/provider-aws.test.jsonl
Token counts: codegen=3705.185433728234 new=3261.041202194482 savings=12.0%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/mu.mem.jsonl
Token counts: codegen=3702.2151591183383 new=3258.************ savings=12.0%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/gohan.mem.jsonl
Token counts: codegen=3699.0712374740774 new=3255.93205528028 savings=12.0%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/kubermatic.mem.jsonl
Token counts: codegen=3683.************ new=3242.9813183546385 savings=12.0%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/liquidity.test.jsonl
Token counts: codegen=3685.1616329084227 new=3244.2968014389617 savings=12.0%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/machine-config-operator.test.jsonl
Token counts: codegen=3670.9987964655697 new=3236.23196221816 savings=11.8%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/flamingo.mem.jsonl
Token counts: codegen=3665.7319427763505 new=3231.************ savings=11.8%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/machine-config-operator.mem.jsonl
Token counts: codegen=3655.************ new=3233.6221557646463 savings=11.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/weapp.mem.jsonl
Token counts: codegen=3651.9197683919865 new=3230.4041447127443 savings=11.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/operator.test.jsonl
Token counts: codegen=3651.0288332934433 new=3229.************ savings=11.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/istio-operator.mem.jsonl
Token counts: codegen=3651.5893153614543 new=3230.2368939415255 savings=11.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/go-glint.mem.jsonl
Token counts: codegen=3651.7184415511374 new=3230.************ savings=11.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/liquidity.mem.jsonl
Token counts: codegen=3652.9898012221674 new=3231.6543653859662 savings=11.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/gogstash.mem.jsonl
Token counts: codegen=3649.************ new=3228.6281999130324 savings=11.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/weapp.test.jsonl
Token counts: codegen=3648.************ new=3227.8422225961135 savings=11.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/go-mc.test.jsonl
Token counts: codegen=3647.8295143998316 new=3227.0657557284003 savings=11.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/custom-script-extension-linux.mem.jsonl
Token counts: codegen=3646.************ new=3226.034389533255 savings=11.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/cli.test.jsonl
Token counts: codegen=3645.************ new=3225.************ savings=11.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/go-spring.test.jsonl
Token counts: codegen=3643.643074943352 new=3223.451142753238 savings=11.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/circuit.mem.jsonl
Token counts: codegen=3642.4084491290855 new=3222.386920904745 savings=11.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/gochain.test.jsonl
Token counts: codegen=3641.0427454096784 new=3221.647845302327 savings=11.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/Xray-core.mem.jsonl
Token counts: codegen=3629.6604599612083 new=3212.079523413688 savings=11.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/Knitter.mem.jsonl
Token counts: codegen=3656.054816710453 new=3240.9847950669046 savings=11.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/rapidsms.test.jsonl
Token counts: codegen=3654.9414494094594 new=3239.9845833560116 savings=11.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/autonomous-learning-library.test.jsonl
Token counts: codegen=3653.5024887800896 new=3238.6963008295934 savings=11.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/freeseer.test.jsonl
Token counts: codegen=3652.776845797251 new=3238.017564132193 savings=11.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/freeseer.mem.jsonl
Token counts: codegen=3656.144818315138 new=3241.4779343870514 savings=11.3%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/Conditional_Density_Estimation.mem.jsonl
Token counts: codegen=3655.0502997910958 new=3240.5036354757317 savings=11.3%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/paasta.mem.jsonl
Token counts: codegen=3653.64502445219 new=3238.837899543379 savings=11.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/robosuite.mem.jsonl
Token counts: codegen=3652.257973126113 new=3237.56338028169 savings=11.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/voicebook.mem.jsonl
Token counts: codegen=3650.6595168483314 new=3236.1591059134207 savings=11.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/scout.mem.jsonl
Token counts: codegen=3641.8314308325844 new=3228.2785475714613 savings=11.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/wal-e.test.jsonl
Token counts: codegen=3640.9463373426124 new=3227.4790485002077 savings=11.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/ReAgent.test.jsonl
Token counts: codegen=3639.251188578928 new=3225.874325355904 savings=11.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/community.general.test.jsonl
Token counts: codegen=3637.508856704265 new=3224.1116999265832 savings=11.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/programmingbitcoin.mem.jsonl
Token counts: codegen=3638.6011364091073 new=3225.0382671094926 savings=11.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/deepxde.mem.jsonl
Token counts: codegen=3635.4878591312918 new=3222.2375028304273 savings=11.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/errbot.test.jsonl
Token counts: codegen=3634.828255659121 new=3221.6442476697734 savings=11.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/Conditional_Density_Estimation.test.jsonl
Token counts: codegen=3634.3350415973377 new=3221.1936106489184 savings=11.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/oioioi.mem.jsonl
Token counts: codegen=3619.1266395176644 new=3207.378517029829 savings=11.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/Decoupled-Classification-Refinement.mem.jsonl
Token counts: codegen=3622.0261594126346 new=3209.588487745616 savings=11.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/oioioi.test.jsonl
Token counts: codegen=3616.8082031147064 new=3204.8529869034755 savings=11.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/robosuite.test.jsonl
Token counts: codegen=3616.1359374176895 new=3204.245337933941 savings=11.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/rapidsms.mem.jsonl
Token counts: codegen=3612.917716856534 new=3201.3596253518904 savings=11.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/betamax.mem.jsonl
Token counts: codegen=3611.0767622853837 new=3199.71702205979 savings=11.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/socorro.test.jsonl
Token counts: codegen=3610.306542497997 new=3198.9504288547673 savings=11.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/gmond_python_modules.test.jsonl
Token counts: codegen=3610.495233217775 new=3199.1020984347097 savings=11.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/vision.test.jsonl
Token counts: codegen=3611.8081070444705 new=3200.0701954611045 savings=11.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/wal-e.mem.jsonl
Token counts: codegen=3609.290580033025 new=3197.802151862239 savings=11.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/viztracer.mem.jsonl
Token counts: codegen=3607.9225349345834 new=3196.5448354440327 savings=11.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/robotics-rl-srl.mem.jsonl
Token counts: codegen=3606.8290952873276 new=3195.5435212207667 savings=11.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/python-learning.test.jsonl
Token counts: codegen=3606.002629616547 new=3194.7734604968796 savings=11.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/Decoupled-Classification-Refinement.test.jsonl
Token counts: codegen=3606.1041323394797 new=3194.7893814567806 savings=11.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/Cinders-DS3.mem.jsonl
Token counts: codegen=3606.7738213076323 new=3195.3396457410786 savings=11.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/BERT-KPE.test.jsonl
Token counts: codegen=3607.1932044522555 new=3195.6548720952937 savings=11.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/vision.mem.jsonl
Token counts: codegen=3607.9562356422534 new=3195.9527443574866 savings=11.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/autonomous-learning-library.mem.jsonl
Token counts: codegen=3604.0800145151047 new=3192.4877334404687 savings=11.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/voicebook.test.jsonl
Token counts: codegen=3603.386364225199 new=3191.890017358862 savings=11.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/betamax.test.jsonl
Token counts: codegen=3602.8281828778654 new=3191.3893278072787 savings=11.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/errbot.mem.jsonl
Token counts: codegen=3601.4581758890213 new=3190.1392411615507 savings=11.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/gmond_python_modules.mem.jsonl
Token counts: codegen=3601.305591475715 new=3189.9269901205193 savings=11.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/community.general.mem.jsonl
Token counts: codegen=3594.4496150840923 new=3183.1977225275077 savings=11.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/sir-lancebot.mem.jsonl
Token counts: codegen=3592.677717780735 new=3181.603157948613 savings=11.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/BERT-KPE.mem.jsonl
Token counts: codegen=3593.112245549919 new=3181.853712766909 savings=11.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/sktime.mem.jsonl
Token counts: codegen=3589.8209243515944 new=3178.5732784364554 savings=11.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/web2py.test.jsonl
Token counts: codegen=3591.4114421355944 new=3179.8364665710405 savings=11.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/deepxde.test.jsonl
Token counts: codegen=3590.348719770599 new=3178.884005379755 savings=11.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/ReAgent.mem.jsonl
Token counts: codegen=3584.339765068848 new=3173.326539127796 savings=11.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/paasta.test.jsonl
Token counts: codegen=3584.8919673186933 new=3173.5869628357473 savings=11.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/gpodder.test.jsonl
Token counts: codegen=3584.887000871069 new=3173.562963149988 savings=11.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/robotics-rl-srl.test.jsonl
Token counts: codegen=3584.5307703957997 new=3173.2345643174476 savings=11.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/web2py.mem.jsonl
Token counts: codegen=3589.5839350861743 new=3177.56877594666 savings=11.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/python-learning.mem.jsonl
Token counts: codegen=3587.30954176407 new=3175.4337656788075 savings=11.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/socorro.mem.jsonl
Token counts: codegen=3583.229380570913 new=3171.6508205147493 savings=11.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/viztracer.test.jsonl
Token counts: codegen=3582.8872831645444 new=3171.3316955780606 savings=11.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/Cinders-DS3.test.jsonl
Token counts: codegen=3582.87687762868 new=3171.301371920689 savings=11.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/sktime.test.jsonl
Token counts: codegen=3581.6965539645653 new=3170.106117400105 savings=11.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/sir-lancebot.test.jsonl
Token counts: codegen=3581.0152881516824 new=3169.4900202343183 savings=11.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/PythonJS.test.jsonl
Token counts: codegen=3580.518782537047 new=3169.0536198049963 savings=11.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/scout.test.jsonl
Token counts: codegen=3578.0555036092305 new=3166.8553315630024 savings=11.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/gpodder.mem.jsonl
Token counts: codegen=3577.7595351386362 new=3166.581873894522 savings=11.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/programmingbitcoin.test.jsonl
Token counts: codegen=3577.950636347787 new=3166.739010236358 savings=11.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/PythonJS.mem.jsonl
Token counts: codegen=3574.721020169614 new=3163.927690318088 savings=11.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/ssl-vision.mem.jsonl
Token counts: codegen=3571.65197161079 new=3161.018971635606 savings=11.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/incubator-pagespeed-mod.test.jsonl
Token counts: codegen=3570.5191098150185 new=3159.6483003200415 savings=11.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/OneFLOW.test.jsonl
Token counts: codegen=3566.4647208247015 new=3156.0642325145504 savings=11.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/DOOM-3-BFG.mem.jsonl
Token counts: codegen=3591.324040448504 new=3180.3124610249074 savings=11.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/spring.test.jsonl
Token counts: codegen=3589.448141803647 new=3178.3443525166745 savings=11.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/ssl-vision.test.jsonl
Token counts: codegen=3588.4982882117274 new=3177.458006555785 savings=11.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/Hazel.test.jsonl
Token counts: codegen=3587.1984859697436 new=3176.3125477683825 savings=11.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/AnalogTapeModel.test.jsonl
Token counts: codegen=3586.385387862029 new=3175.586486683161 savings=11.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/libzim.test.jsonl
Token counts: codegen=3585.686940814199 new=3174.960053827316 savings=11.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/kdenlive.mem.jsonl
Token counts: codegen=3591.262514776483 new=3178.8690743287257 savings=11.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/Crystal.mem.jsonl
Token counts: codegen=3590.3684787585244 new=3178.1161473770453 savings=11.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/crossuo.mem.jsonl
Token counts: codegen=3630.3374644764926 new=3211.9301793568625 savings=11.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/HTMLCOIN.mem.jsonl
Token counts: codegen=3633.6695449241543 new=3214.1325670945157 savings=11.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/spring.mem.jsonl
Token counts: codegen=3625.073795284251 new=3207.129958066955 savings=11.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/fs2open.github.com.mem.jsonl
Token counts: codegen=3659.636330870427 new=3240.6610087403137 savings=11.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/Hazel.mem.jsonl
Token counts: codegen=3666.44772814137 new=3246.893073135637 savings=11.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/TranslucentTB.mem.jsonl
Token counts: codegen=3663.4985740911234 new=3244.2883142837895 savings=11.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/scrimmage.test.jsonl
Token counts: codegen=3660.719565802458 new=3241.825479949762 savings=11.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/AnalogTapeModel.mem.jsonl
Token counts: codegen=3658.692083590117 new=3240.002297047454 savings=11.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/Algorithms.mem.jsonl
Token counts: codegen=3656.0502899621592 new=3237.697409372831 savings=11.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/gerona.mem.jsonl
Token counts: codegen=3651.9408459201 new=3233.940745374922 savings=11.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/mongo-cxx-driver.mem.jsonl
Token counts: codegen=3648.202380024713 new=3230.288107668845 savings=11.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/pro-TBB.mem.jsonl
Token counts: codegen=3645.367043737221 new=3227.7919037247757 savings=11.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/libzim.mem.jsonl
Token counts: codegen=3643.0821151476807 new=3225.743609381417 savings=11.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/scrimmage.mem.jsonl
Token counts: codegen=3635.8544380792287 new=3219.3030346597066 savings=11.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/curve.test.jsonl
Token counts: codegen=3636.8645862019584 new=3218.912107158987 savings=11.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/rippled.mem.jsonl
Token counts: codegen=3633.553724865879 new=3214.7552558119246 savings=11.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/virt86.test.jsonl
Token counts: codegen=3633.0506142827553 new=3214.308602138816 savings=11.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/OneFLOW.mem.jsonl
Token counts: codegen=3622.4147224631397 new=3204.810928013877 savings=11.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/gfxreconstruct.test.jsonl
Token counts: codegen=3627.6195346015515 new=3208.6309962300124 savings=11.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/mongo-cxx-driver.test.jsonl
Token counts: codegen=3625.526873343072 new=3206.7290374939134 savings=11.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/pfaedle.test.jsonl
Token counts: codegen=3625.1646799212513 new=3206.3879345780238 savings=11.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/Crystal.test.jsonl
Token counts: codegen=3624.7534142886493 new=3206.0300284388886 savings=11.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/fs2open.github.com.test.jsonl
Token counts: codegen=3629.02480103248 new=3210.6424392342437 savings=11.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/incubator-pagespeed-mod.mem.jsonl
Token counts: codegen=3625.1095244682324 new=3206.1929047978806 savings=11.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/TranslucentTB.test.jsonl
Token counts: codegen=3624.052821358294 new=3205.262447481785 savings=11.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/kdenlive.test.jsonl
Token counts: codegen=3623.40795930072 new=3204.503366824564 savings=11.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/Adafruit_EPD.mem.jsonl
Token counts: codegen=3622.3769837478635 new=3203.5864675222656 savings=11.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/Algorithms.test.jsonl
Token counts: codegen=3621.567097178151 new=3202.8854917276 savings=11.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/immer.test.jsonl
Token counts: codegen=3620.694346627068 new=3202.0997666525245 savings=11.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/curve.mem.jsonl
Token counts: codegen=3617.9161667855196 new=3196.571374638198 savings=11.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/Grid.test.jsonl
Token counts: codegen=3620.1602274155043 new=3198.4624219960633 savings=11.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/HTMLCOIN.test.jsonl
Token counts: codegen=3621.3537178523716 new=3199.2403661489648 savings=11.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/gerona.test.jsonl
Token counts: codegen=3620.08807576769 new=3198.0973902703604 savings=11.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/pro-TBB.test.jsonl
Token counts: codegen=3619.5601296832974 new=3197.638784062715 savings=11.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/gfxreconstruct.mem.jsonl
Token counts: codegen=3644.583513648469 new=3217.830195156458 savings=11.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/rippled.test.jsonl
Token counts: codegen=3643.4915386051143 new=3216.560930338663 savings=11.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/TitanEngine.mem.jsonl
Token counts: codegen=3644.5280688118555 new=3217.1130214000727 savings=11.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/Mesen.test.jsonl
Token counts: codegen=3642.105767739133 new=3215.0766756642156 savings=11.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/Mesen.mem.jsonl
Token counts: codegen=3632.81916391707 new=3207.180702457677 savings=11.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/crossuo.test.jsonl
Token counts: codegen=3649.371267455996 new=3221.9686583783673 savings=11.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/Grid.mem.jsonl
Token counts: codegen=3648.797057238441 new=3221.295293005094 savings=11.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/dropEst.test.jsonl
Token counts: codegen=3647.981824835326 new=3220.58263194275 savings=11.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/Adafruit_EPD.test.jsonl
Token counts: codegen=3647.593487341579 new=3220.2369579136725 savings=11.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/pfaedle.mem.jsonl
Token counts: codegen=3646.590313245672 new=3219.3177945880084 savings=11.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/dropEst.mem.jsonl
Token counts: codegen=3644.181195731559 new=3217.2242960318104 savings=11.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/virt86.mem.jsonl
Token counts: codegen=3642.8594814304247 new=3216.0420856308792 savings=11.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/TitanEngine.test.jsonl
Token counts: codegen=3644.7495718267037 new=3217.3424474284266 savings=11.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/DOOM-3-BFG.test.jsonl
Token counts: codegen=3654.2379276874544 new=3226.700406454744 savings=11.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/immer.mem.jsonl
Token counts: codegen=3657.6493225917175 new=3229.5457124396794 savings=11.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/web.test.jsonl
Token counts: codegen=3651.7422021304446 new=3224.1071565212137 savings=11.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/opentok-rtc.test.jsonl
Token counts: codegen=3651.408278305685 new=3223.785533097117 savings=11.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/extension-save-to-pocket.test.jsonl
Token counts: codegen=3651.0585671725917 new=3223.473418817183 savings=11.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/badgeyay.mem.jsonl
Token counts: codegen=3646.461505216395 new=3219.40080808893 savings=11.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/Scrawl-canvas.mem.jsonl
Token counts: codegen=3646.7535454235453 new=3219.718452249614 savings=11.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/next.mem.jsonl
Token counts: codegen=3645.376462332715 new=3218.4776101448547 savings=11.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/Scrawl-canvas.test.jsonl
Token counts: codegen=3644.8538056217817 new=3218.010438162403 savings=11.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/sepal.test.jsonl
Token counts: codegen=3640.0345861955184 new=3213.637859835418 savings=11.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/badgeyay.test.jsonl
Token counts: codegen=3638.461662235903 new=3212.244078434508 savings=11.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/grabient.test.jsonl
Token counts: codegen=3638.556612445546 new=3212.2365113304822 savings=11.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/next.test.jsonl
Token counts: codegen=3638.0675517727095 new=3211.7945674868383 savings=11.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/airframe-react.test.jsonl
Token counts: codegen=3636.8398007864744 new=3210.512106513364 savings=11.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/delite.mem.jsonl
Token counts: codegen=3638.104909823637 new=3211.761172292714 savings=11.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/synclounge.mem.jsonl
Token counts: codegen=3636.680342170068 new=3210.4649604689885 savings=11.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/service-mocker.mem.jsonl
Token counts: codegen=3635.1633051658164 new=3209.107740752551 savings=11.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/open-pryv.io.mem.jsonl
Token counts: codegen=3632.051724137931 new=3206.1641462880793 savings=11.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/open-pryv.io.test.jsonl
Token counts: codegen=3630.885471666219 new=3205.075729101413 savings=11.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/intern-examples.test.jsonl
Token counts: codegen=3630.282376800644 new=3204.5413315571286 savings=11.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/usgs-lidar.mem.jsonl
Token counts: codegen=3650.862127566536 new=3223.4430939555964 savings=11.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/ui-kit.mem.jsonl
Token counts: codegen=3646.7108450466717 new=3219.607413873486 savings=11.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/dev-cover.test.jsonl
Token counts: codegen=3646.022472578694 new=3218.992690957415 savings=11.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/ieaseMusic.test.jsonl
Token counts: codegen=3645.054725529658 new=3218.12115954673 savings=11.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/sdkjs.mem.jsonl
Token counts: codegen=3731.6354184168654 new=3294.0879134998368 savings=11.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/ProjectABE.test.jsonl
Token counts: codegen=3730.871138552729 new=3293.3844109656707 savings=11.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/extension-save-to-pocket.mem.jsonl
Token counts: codegen=3729.893569954276 new=3292.527528417227 savings=11.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/lwc-recipes-oss.mem.jsonl
Token counts: codegen=3728.543127615889 new=3291.3270552001895 savings=11.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/service-mocker.test.jsonl
Token counts: codegen=3727.9861035718163 new=3290.8324335527677 savings=11.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/apify-js.mem.jsonl
Token counts: codegen=3726.1365147315573 new=3289.1819078525564 savings=11.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/lwc-recipes-oss.test.jsonl
Token counts: codegen=3725.662826935974 new=3288.7585210461953 savings=11.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/ieaseMusic.mem.jsonl
Token counts: codegen=3723.116005714567 new=3286.4264052416374 savings=11.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/omi.mem.jsonl
Token counts: codegen=3697.836784477825 new=3251.171443851271 savings=12.1%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/sepal.mem.jsonl
Token counts: codegen=3684.513884351519 new=3239.1005087039716 savings=12.1%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/ebayui-core.mem.jsonl
Token counts: codegen=3679.4915814876586 new=3234.6113787352197 savings=12.1%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/intern-examples.mem.jsonl
Token counts: codegen=3682.0978460277697 new=3235.867511617295 savings=12.1%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/ProjectABE.mem.jsonl
Token counts: codegen=3683.543387276786 new=3237.1412574404762 savings=12.1%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/showdown.test.jsonl
Token counts: codegen=3683.035307792449 new=3236.6867956109354 savings=12.1%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/usgs-lidar.test.jsonl
Token counts: codegen=3687.745711336947 new=3240.9424087177244 savings=12.1%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/mf-geoadmin3.mem.jsonl
Token counts: codegen=3739.4456319055967 new=3284.5684135333463 savings=12.2%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/Blog.mem.jsonl
Token counts: codegen=3737.516317448544 new=3282.8147413313554 savings=12.2%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/grabient.mem.jsonl
Token counts: codegen=3736.230995858695 new=3281.6066409109017 savings=12.2%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/sdkjs.test.jsonl
Token counts: codegen=3757.512749789432 new=3299.8177636267715 savings=12.2%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/airframe-react.mem.jsonl
Token counts: codegen=3750.9648168834647 new=3293.6660050406667 savings=12.2%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/apify-js.test.jsonl
Token counts: codegen=3750.3184050212294 new=3293.0960033228725 savings=12.2%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/synclounge.test.jsonl
Token counts: codegen=3749.8801196035365 new=3292.6975857804687 savings=12.2%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/B78XH.test.jsonl
Token counts: codegen=3750.42404362509 new=3293.195703925151 savings=12.2%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/mf-geoadmin3.test.jsonl
Token counts: codegen=3761.797748457943 new=3302.665901400529 savings=12.2%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/Blog.test.jsonl
Token counts: codegen=3761.0976198816393 new=3302.037038402684 savings=12.2%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/browserify-shim.test.jsonl
Token counts: codegen=3760.789606606939 new=3301.7662500460865 savings=12.2%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/ebayui-core.test.jsonl
Token counts: codegen=3759.0369950716226 new=3300.209092165262 savings=12.2%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/showdown.mem.jsonl
Token counts: codegen=3758.7468511766656 new=3299.915764372261 savings=12.2%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/opentok-rtc.mem.jsonl
Token counts: codegen=3757.5819001610307 new=3298.8317552334943 savings=12.2%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/browserify-shim.mem.jsonl
Token counts: codegen=3756.766593378288 new=3298.1127567776416 savings=12.2%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/popmotion.test.jsonl
Token counts: codegen=3755.7873755069386 new=3297.260150265309 savings=12.2%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/ui-kit.test.jsonl
Token counts: codegen=3754.2341549943017 new=3295.8542516819234 savings=12.2%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/popmotion.mem.jsonl
Token counts: codegen=3751.3841895055775 new=3293.357829500069 savings=12.2%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/web.mem.jsonl
Token counts: codegen=3740.453913249542 new=3283.050457085046 savings=12.2%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/omi.test.jsonl
Token counts: codegen=3736.632048037051 new=3275.73098533398 savings=12.3%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/dev-cover.mem.jsonl
Token counts: codegen=3734.8092248075923 new=3274.110843006046 savings=12.3%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/delite.test.jsonl
Token counts: codegen=3734.522856297253 new=3273.8759742065095 savings=12.3%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/B78XH.mem.jsonl
Token counts: codegen=3738.3603589067866 new=3277.271412052599 savings=12.3%
