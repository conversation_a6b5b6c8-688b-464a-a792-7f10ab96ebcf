"""The Stack Dataset."""


import re
from pathlib import Path

import datasets
import pandas

logger = datasets.logging.get_logger(__name__)


_DATASET_PATH = Path("/mnt/efs/augment/data/raw/the-stack-dedup.2022-11-19/data")


_DESCRIPTION = """\
Dataset based on a subset of The Stack.
"""


class TheStackConfig(datasets.BuilderConfig):
    """The BuilderConfig for The Stack."""

    def __init__(self, **kwargs):
        """The BuilderConfig for The Stack.

        Args:
          **kwargs: keyword arguments forwarded to super.
        """
        super(TheStackConfig, self).__init__(**kwargs)


class TheStack(datasets.GeneratorBasedBuilder):
    """The Stack."""

    BUILDER_CONFIGS = []

    def __init__(self, *args, dataset_fraction=1.0, **kwargs):
        self.BUILDER_CONFIGS.append(
            TheStackConfig(
                name=f"the_stack_{dataset_fraction}",
                version=datasets.Version("1.0.0", ""),
                description=f"The Stack with fraction {dataset_fraction}",
            ),
        )
        super().__init__(*args, **kwargs)
        self.dataset_fraction = dataset_fraction

    def _info(self):
        return datasets.DatasetInfo(
            description=_DESCRIPTION,
            features=datasets.Features(
                {
                    "content": datasets.Value("string"),
                    "avg_line_length": datasets.Value("float"),
                    "max_line_length": datasets.Value("float"),
                    "alphanum_fraction": datasets.Value("float"),
                    "licenses": [datasets.Value("string")],
                    "repository_name": datasets.Value("string"),
                    "path": datasets.Value("string"),
                    "size": datasets.Value("float"),
                    "lang": datasets.Value("string"),
                }
            ),
        )

    def _get_file_num(self, filepath):
        return int(re.search(r"\d+", filepath.name).group(0))

    def _split_generators(self, _):
        files = list(_DATASET_PATH.rglob("data_*.parquet"))

        return [
            datasets.SplitGenerator(
                name=datasets.Split.TRAIN, gen_kwargs={"files": files}
            ),
        ]

    def _generate_examples(self, files):
        """This function returns the examples in the raw (text) form."""
        # We have filenames from 0000....0100 for each language
        max_file_num = max([self._get_file_num(fname) for fname in files])
        target_file_num = int(max_file_num * self.dataset_fraction)

        for filepath in sorted(files):
            if self._get_file_num(filepath) > target_file_num:
                # Skip this file due to dataset_fraction
                continue

            print("\nThe Stack: loading from", filepath)
            dataframe = pandas.read_parquet(filepath)
            for key, row in dataframe.iterrows():
                yield key, {
                    "content": row["content"],
                    "avg_line_length": row["avg_line_length"],
                    "max_line_length": row["max_line_length"],
                    "alphanum_fraction": row["alphanum_fraction"],
                    "licenses": row["licenses"],
                    "repository_name": row["repository_name"],
                    "path": row["path"],
                    "size": row["size"],
                    "lang": row["lang"],
                }
