"""Train a new tokenizer."""

import argparse

import datasets
from transformers import AutoTokenizer

CACHE_DIR = "/mnt/efs/augment/user/guy/huggingface_dataset_cache"


def get_training_corpus(dataset_fraction):
    """Generator of training samples.

    Args:
        dataset_fraction: Fraction of the dataset to use for training.
    """
    dataset = datasets.load_dataset(
        "the_stack.py",
        cache_dir=CACHE_DIR,
        name=f"the_stack_{dataset_fraction}",
        dataset_fraction=dataset_fraction,
    )
    dataset = dataset["train"]
    for start_idx in range(0, len(dataset), 1000):
        samples = dataset[start_idx : start_idx + 1000]
        yield samples["content"]


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--tokenizer",
        type=str,
        default="the_stack",
        help="name of tokenizer, used when saving",
    )
    parser.add_argument(
        "--dataset_fraction",
        type=float,
        default=0.05,
        help="approximate fraction of the dataset to use for training",
    )
    parser.add_argument(
        "--vocab_size",
        type=int,
        default=51000,
        help="name of tokenizer, used when saving",
    )
    parser.add_argument(
        "--base_tokenizer",
        type=str,
        default="gpt2",
        help=(
            "old tokenizer to use as starting point. "
            "only hyperparameters are reused; training happens from scratch."
        ),
    )
    args = parser.parse_args()

    old_tokenizer = AutoTokenizer.from_pretrained(args.base_tokenizer)
    training_corpus = get_training_corpus(args.dataset_fraction)
    tokenizer = old_tokenizer.train_new_from_iterator(
        training_corpus,
        args.vocab_size,
    )

    save_name = f"{args.tokenizer}_{args.dataset_fraction}_{args.vocab_size}"
    print(f"Saving tokenizer as {save_name}...")
    tokenizer.save_pretrained(save_name)
    print("Tokenizer saved.")


if __name__ == "__main__":
    main()
