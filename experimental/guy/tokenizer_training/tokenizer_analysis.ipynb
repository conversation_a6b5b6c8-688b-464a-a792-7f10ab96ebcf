{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "# Computed by training a few tokenizers with `train_tokenizer.py` on 20% of The\n", "# Stack (filtered to 6 languages and deduped), then evaluating them using\n", "# `evaluate_tokenizer.py` on polycoder. The tokenizers are saved under\n", "# `/mnt/efs/augment/user/guy/tokenizers`.\n", "vocab_sizes = np.array([25000, 51000, 100000])\n", "tokenizer_savings = np.array([0.093, 0.123, 0.143])\n", "\n", "fig, ax = plt.subplots()\n", "ax.plot(vocab_sizes, 100*tokenizer_savings, \".-\")\n", "ax.set_xlabel(\"vocab size\")\n", "ax.set_ylabel(\"Avg. tokens saved vs. CodeGen (%)\")\n", "ax.set_title(\"Tokenizers trained on The Stack, evaled on polycoder\")\n", "ax.grid()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}, "orig_nbformat": 4, "vscode": {"interpreter": {"hash": "31f2aee4e71d21fbe5cf8b01ff0e069b9275f58929596ceb00d14d90e3e16cd6"}}}, "nbformat": 4, "nbformat_minor": 2}