"""Evaluate tokenizers on polycoder."""

import argparse
import json
from pathlib import Path

import numpy as np
from code_tokenizers.hf_codegen_tokenizer import HFCodeGenTokenizer
from transformers import AutoTokenizer


def main():
    """Main."""
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--tokenizer",
        type=str,
        default="my-new-tokenizer",
        help="name of the tokenizer to load",
    )
    parser.add_argument(
        "--dataset_path",
        type=str,
        required=True,
        help="dataset to tokenizer (jsonl files)",
    )
    args = parser.parse_args()
    # gpt2_tokenizer = AutoTokenizer.from_pretrained("gpt2")
    codegen_tokenizer = HFCodeGenTokenizer().tokenizer
    tokenizer = AutoTokenizer.from_pretrained(args.tokenizer)

    token_counts = {
        "codegen_tokenizer": [],
        "tokenizer": [],
    }

    for path in Path(args.dataset_path).rglob("*.jsonl"):
        print(f"File {path}")
        with path.open("r") as file:
            for line in file:
                data = json.loads(line)
                text = data["text"]
                codegen_tokens = codegen_tokenizer.tokenize(text)
                tokens = tokenizer.tokenize(text)
                token_counts["codegen_tokenizer"].append(len(codegen_tokens))
                token_counts["tokenizer"].append(len(tokens))

        codegen_mean = np.mean(token_counts["codegen_tokenizer"])
        new_mean = np.mean(token_counts["tokenizer"])
        savings = (codegen_mean - new_mean) / codegen_mean
        print(
            f"Token counts: codegen={codegen_mean} new={new_mean} savings={100*savings:.1f}%"
        )


if __name__ == "__main__":
    main()
