File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/genie-bt-mesh-stack.test.jsonl
Token counts: codegen=4298.468053491828 new=3629.895988112927 savings=15.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/wt.test.jsonl
Token counts: codegen=4099.266829865361 new=3472.8984088127295 savings=15.3%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/FreeRDP.test.jsonl
Token counts: codegen=4710.477130044843 new=4063.160538116592 savings=13.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/Montage.mem.jsonl
Token counts: codegen=6109.629520295203 new=5633.442804428044 savings=7.8%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/epics-base.test.jsonl
Token counts: codegen=5949.489376523859 new=5486.267850923024 savings=7.8%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/clam.mem.jsonl
Token counts: codegen=5796.107260726073 new=5317.191749174917 savings=8.3%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/Learning-DIY-RTOS.test.jsonl
Token counts: codegen=5519.8687315634215 new=5025.226843657817 savings=9.0%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/superlu.test.jsonl
Token counts: codegen=5441.528636884307 new=4952.53178694158 savings=9.0%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/Adrenaline.test.jsonl
Token counts: codegen=5431.2287822878225 new=4941.633834799886 savings=9.0%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/optee_test.test.jsonl
Token counts: codegen=5654.1591482207905 new=5154.36284673578 savings=8.8%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/zpl.mem.jsonl
Token counts: codegen=5585.114700246373 new=5090.900355871886 savings=8.8%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/rgbds.mem.jsonl
Token counts: codegen=5552.22300405954 new=5060.959675236806 savings=8.8%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/fastpm.test.jsonl
Token counts: codegen=5525.997327632283 new=5035.293158738643 savings=8.9%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/Squirrel-Engine.mem.jsonl
Token counts: codegen=5666.192317476469 new=5122.37395064869 savings=9.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/solanum.mem.jsonl
Token counts: codegen=5560.142446732104 new=5030.850849892267 savings=9.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/stellar-core.test.jsonl
Token counts: codegen=5543.677707305615 new=5011.0483908856 savings=9.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/Libftest.mem.jsonl
Token counts: codegen=5472.466898148148 new=4946.796527777778 savings=9.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/FreeRDP.mem.jsonl
Token counts: codegen=5362.962401688087 new=4827.297717245348 savings=10.0%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/solanum.test.jsonl
Token counts: codegen=5330.907099697885 new=4799.487726586102 savings=10.0%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/rtthread-nano.test.jsonl
Token counts: codegen=5512.964010282776 new=4964.867609254498 savings=9.9%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/EsenthelEngine.mem.jsonl
Token counts: codegen=5498.66991499067 new=4938.178001244039 savings=10.2%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/fastpm.mem.jsonl
Token counts: codegen=5464.004598405886 new=4906.02902105048 savings=10.2%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/PigDev.test.jsonl
Token counts: codegen=5459.1330694275275 new=4900.624949248883 savings=10.2%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/CloverBootloader.mem.jsonl
Token counts: codegen=5710.639155327533 new=4977.13101345825 savings=12.8%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/PigDev.mem.jsonl
Token counts: codegen=5710.26320803107 new=4974.402652597641 savings=12.9%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/xfdashboard.test.jsonl
Token counts: codegen=5708.6054744525545 new=4973.431897810219 savings=12.9%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/rgbds.test.jsonl
Token counts: codegen=5706.28384979949 new=4971.546409041196 savings=12.9%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/rakun.mem.jsonl
Token counts: codegen=5690.3725675283185 new=4957.764086552425 savings=12.9%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/Adrenaline.mem.jsonl
Token counts: codegen=5676.220715522216 new=4945.084824004616 savings=12.9%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/Validity90.test.jsonl
Token counts: codegen=5688.259251259899 new=4956.972282217423 savings=12.9%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/xfdashboard.mem.jsonl
Token counts: codegen=5700.6320660686315 new=4968.897194930942 savings=12.8%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/epk2extract.test.jsonl
Token counts: codegen=5701.165743108838 new=4969.904305200341 savings=12.8%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/zpl.test.jsonl
Token counts: codegen=5693.56086494151 new=4963.438709677419 savings=12.8%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/Montage.test.jsonl
Token counts: codegen=5709.418391746943 new=4994.46225319396 savings=12.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/clam.test.jsonl
Token counts: codegen=5701.715520762424 new=4986.103063308373 savings=12.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/optee_test.mem.jsonl
Token counts: codegen=5741.767840280588 new=5023.703763658438 savings=12.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/CloverBootloader.test.jsonl
Token counts: codegen=5716.5113884555385 new=4972.839438377535 savings=13.0%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/Validity90.mem.jsonl
Token counts: codegen=5742.877414146433 new=4999.10842700118 savings=13.0%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/aerospike-client-c.mem.jsonl
Token counts: codegen=5739.661637001909 new=4998.922399458028 savings=12.9%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/superlu.mem.jsonl
Token counts: codegen=5693.118130705519 new=4959.789855510549 savings=12.9%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/ZFSin.test.jsonl
Token counts: codegen=5715.700185439971 new=4981.119459233116 savings=12.9%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/lk2nd.mem.jsonl
Token counts: codegen=5659.100748770675 new=4940.436298614215 savings=12.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/genie-bt-mesh-stack.mem.jsonl
Token counts: codegen=5522.7417265103195 new=4812.796966805605 savings=12.9%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/Squirrel-Engine.test.jsonl
Token counts: codegen=5532.289647577092 new=4820.009010812975 savings=12.9%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/wt.mem.jsonl
Token counts: codegen=5520.420660590023 new=4800.11447613447 savings=13.0%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/rakun.test.jsonl
Token counts: codegen=5516.7607343941245 new=4796.946829865361 savings=13.0%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/lk2nd.test.jsonl
Token counts: codegen=5495.308675184936 new=4779.748582957056 savings=13.0%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/FreeFlyOS.test.jsonl
Token counts: codegen=5492.633475694611 new=4776.994625461874 savings=13.0%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/Learning-DIY-RTOS.mem.jsonl
Token counts: codegen=5395.543683562207 new=4680.680414252475 savings=13.2%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/Libftest.test.jsonl
Token counts: codegen=5390.965587967184 new=4676.73290793072 savings=13.2%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/rtthread-nano.mem.jsonl
Token counts: codegen=5463.953859210291 new=4743.35907629087 savings=13.2%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/Open-Vehicle-Monitoring-System-3.mem.jsonl
Token counts: codegen=5712.02385677308 new=4962.353451251079 savings=13.1%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/EsenthelEngine.test.jsonl
Token counts: codegen=5733.047109556162 new=4987.028395915545 savings=13.0%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/epk2extract.mem.jsonl
Token counts: codegen=5728.631794414495 new=4983.637347493008 savings=13.0%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/Open-Vehicle-Monitoring-System-3.test.jsonl
Token counts: codegen=5816.3772858517805 new=5059.98704683991 savings=13.0%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/epics-base.mem.jsonl
Token counts: codegen=5755.465672581343 new=5006.67466656175 savings=13.0%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/stellar-core.mem.jsonl
Token counts: codegen=5719.875 new=4975.869738072965 savings=13.0%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/aerospike-client-c.test.jsonl
Token counts: codegen=5720.53071864908 new=4977.074627446403 savings=13.0%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/FreeFlyOS.mem.jsonl
Token counts: codegen=5711.53245089667 new=4968.762130269389 savings=13.0%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/ZFSin.mem.jsonl
Token counts: codegen=5742.0952525867315 new=4998.741326841145 savings=12.9%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/streamex.mem.jsonl
Token counts: codegen=5742.295298447994 new=4998.11816491481 savings=13.0%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/jetty.project.mem.jsonl
Token counts: codegen=5441.30029632212 new=4723.135785253617 savings=13.2%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/deeplearning4j.test.jsonl
Token counts: codegen=5343.375445797726 new=4633.293553596663 savings=13.3%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/FXGLGames.test.jsonl
Token counts: codegen=5335.569221468395 new=4626.50130986767 savings=13.3%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/gctoolkit.mem.jsonl
Token counts: codegen=5314.545925010851 new=4608.008647457514 savings=13.3%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/dynamic-support.test.jsonl
Token counts: codegen=5309.857561723254 new=4603.496318262087 savings=13.3%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/PinDroid.mem.jsonl
Token counts: codegen=5302.424970906068 new=4596.667497921862 savings=13.3%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/jetty.project.test.jsonl
Token counts: codegen=5219.681489644443 new=4521.050465108741 savings=13.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/java-iso-tools.mem.jsonl
Token counts: codegen=5205.500726439157 new=4508.458560681885 savings=13.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/gctoolkit.test.jsonl
Token counts: codegen=5199.51707914411 new=4503.178493168342 savings=13.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/openzaly.test.jsonl
Token counts: codegen=5208.283378308918 new=4507.626509894629 savings=13.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/divolte-collector.test.jsonl
Token counts: codegen=5203.805512771146 new=4503.653574637402 savings=13.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/threetenbp.test.jsonl
Token counts: codegen=5206.802255904124 new=4505.827025987759 savings=13.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/java-tutorial.test.jsonl
Token counts: codegen=5194.156218047035 new=4494.656761247444 savings=13.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/learn-java-codes.test.jsonl
Token counts: codegen=5172.660487742838 new=4475.29493497822 savings=13.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/java-tron.mem.jsonl
Token counts: codegen=5091.252113970589 new=4397.578094362745 savings=13.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/jamonapi.test.jsonl
Token counts: codegen=5086.201713324155 new=4393.129325378614 savings=13.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/Upchain-wallet.mem.jsonl
Token counts: codegen=5069.521855676384 new=4378.377196990466 savings=13.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/cxf-spring-boot-starter.test.jsonl
Token counts: codegen=5067.816332744656 new=4376.881706351623 savings=13.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/java-tutorial.mem.jsonl
Token counts: codegen=5033.747024708512 new=4346.8603274330935 savings=13.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/Audinaut.test.jsonl
Token counts: codegen=5030.296047073023 new=4343.783705491853 savings=13.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/kkbinlog.mem.jsonl
Token counts: codegen=5022.253576268634 new=4336.6091251317575 savings=13.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/dokit.test.jsonl
Token counts: codegen=5015.782187744302 new=4330.757081003067 savings=13.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/dynamic-support.mem.jsonl
Token counts: codegen=5003.0279870828845 new=4318.770212893194 savings=13.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/dokit.mem.jsonl
Token counts: codegen=4984.119077380952 new=4301.791547619047 savings=13.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/vulkanbook.mem.jsonl
Token counts: codegen=4922.831028825873 new=4246.952777126787 savings=13.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/spoofax.mem.jsonl
Token counts: codegen=4882.627496449172 new=4211.949621728166 savings=13.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/java-iso-tools.test.jsonl
Token counts: codegen=4878.55657209033 new=4208.405500868558 savings=13.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/h2database.mem.jsonl
Token counts: codegen=4835.267712750919 new=4167.085241730279 savings=13.8%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/testlink-java-api.test.jsonl
Token counts: codegen=4833.300994462651 new=4165.1947112668095 savings=13.8%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/PinDroid.test.jsonl
Token counts: codegen=4831.093288533755 new=4163.221052037157 savings=13.8%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/cxf-spring-boot-starter.mem.jsonl
Token counts: codegen=4826.702919193343 new=4159.373346495558 savings=13.8%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/quincy.mem.jsonl
Token counts: codegen=4807.553855655055 new=4142.634842657833 savings=13.8%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/Quests.mem.jsonl
Token counts: codegen=4795.276382050134 new=4131.8634456132495 savings=13.8%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/openremote.test.jsonl
Token counts: codegen=4778.275979083222 new=4116.587310858923 savings=13.8%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/Quests.test.jsonl
Token counts: codegen=4774.082432695246 new=4112.92040118912 savings=13.8%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/FXGLGames.mem.jsonl
Token counts: codegen=4757.339400813301 new=4098.455890896013 savings=13.8%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/Audinaut.mem.jsonl
Token counts: codegen=4749.926782167292 new=4091.790305672037 savings=13.9%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/quincy.test.jsonl
Token counts: codegen=4743.6558876187855 new=4086.3228756369645 savings=13.9%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/Android-Cookbook-Examples.test.jsonl
Token counts: codegen=4734.122242066218 new=4078.023299903833 savings=13.9%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/deeplearning4j.mem.jsonl
Token counts: codegen=4539.267498670987 new=3901.848264688758 savings=14.0%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/jamonapi.mem.jsonl
Token counts: codegen=4528.246322694588 new=3892.152466254573 savings=14.0%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/consulo.mem.jsonl
Token counts: codegen=3969.194836403968 new=3394.7327212602954 savings=14.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/openzaly.mem.jsonl
Token counts: codegen=3978.703789920771 new=3399.2675202156333 savings=14.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/Upchain-wallet.test.jsonl
Token counts: codegen=3975.7795434609657 new=3396.7088186695496 savings=14.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/spoofax.test.jsonl
Token counts: codegen=3967.7496286348005 new=3389.8155383269236 savings=14.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/kkbinlog.test.jsonl
Token counts: codegen=3966.6110444421843 new=3388.777911115631 savings=14.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/java-tron.test.jsonl
Token counts: codegen=3958.3668334611193 new=3380.3689721964406 savings=14.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/openremote.mem.jsonl
Token counts: codegen=3929.532888313241 new=3354.848161801335 savings=14.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/divolte-collector.mem.jsonl
Token counts: codegen=3924.4562554680665 new=3350.352282669212 savings=14.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/Android-Cookbook-Examples.mem.jsonl
Token counts: codegen=3908.0351859177217 new=3336.2383306962024 savings=14.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/vulkanbook.test.jsonl
Token counts: codegen=3897.881274759061 new=3327.185045034392 savings=14.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/learn-java-codes.mem.jsonl
Token counts: codegen=3868.7849344764954 new=3301.************* savings=14.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/consulo.test.jsonl
Token counts: codegen=3736.239741059737 new=3183.865660906291 savings=14.8%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/testlink-java-api.mem.jsonl
Token counts: codegen=3732.8893288640047 new=3180.8393804331813 savings=14.8%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/streamex.test.jsonl
Token counts: codegen=3732.9821421996394 new=3180.866305828639 savings=14.8%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/h2database.test.jsonl
Token counts: codegen=3726.754959245352 new=3175.0062643099186 savings=14.8%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/threetenbp.mem.jsonl
Token counts: codegen=3732.979734668518 new=3179.8567356187414 savings=14.8%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/okta-sdk-golang.test.jsonl
Token counts: codegen=3731.0302947299224 new=3178.234907417552 savings=14.8%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/go-spring.mem.jsonl
Token counts: codegen=3721.0148298924105 new=3170.128035039256 savings=14.8%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/kubermatic.test.jsonl
Token counts: codegen=3714.598447705028 new=3164.9816546957827 savings=14.8%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/drago.mem.jsonl
Token counts: codegen=3708.474986911231 new=3159.8847646729614 savings=14.8%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/Xray-core.test.jsonl
Token counts: codegen=3702.7059882611356 new=3155.2359475712074 savings=14.8%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/gateway-api.test.jsonl
Token counts: codegen=3701.271626484347 new=3154.0417776178483 savings=14.8%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/influx-stress.test.jsonl
Token counts: codegen=3712.423560294144 new=3165.42767759219 savings=14.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/gochain.mem.jsonl
Token counts: codegen=3708.049914584668 new=3163.8435653783185 savings=14.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/gateway-api.mem.jsonl
Token counts: codegen=3704.5415667678976 new=3160.959489939084 savings=14.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/go-diagrams.mem.jsonl
Token counts: codegen=3698.5279971666373 new=3156.077297680184 savings=14.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/istio-operator.test.jsonl
Token counts: codegen=3698.309338762503 new=3155.888625298752 savings=14.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/flamingo.test.jsonl
Token counts: codegen=3696.1541101342627 new=3154.098140843077 savings=14.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/provider-aws.mem.jsonl
Token counts: codegen=3695.4008831250108 new=3154.9894476991813 savings=14.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/agollo.mem.jsonl
Token counts: codegen=3693.3633600250128 new=3153.25849820222 savings=14.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/go-diagrams.test.jsonl
Token counts: codegen=3691.2052025127546 new=3151.4822996564053 savings=14.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/mu.test.jsonl
Token counts: codegen=3690.0052379630915 new=3150.469196614403 savings=14.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/custom-script-extension-linux.test.jsonl
Token counts: codegen=3689.481133384199 new=3150.0316813484083 savings=14.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/go-glint.test.jsonl
Token counts: codegen=3689.8925178559048 new=3150.4613237639555 savings=14.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/cli.mem.jsonl
Token counts: codegen=3687.3585111542193 new=3148.340688651794 savings=14.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/go-mc.mem.jsonl
Token counts: codegen=3685.9276550126224 new=3147.5496766607876 savings=14.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/drago.test.jsonl
Token counts: codegen=3684.0636555275255 new=3145.9998963265025 savings=14.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/influx-stress.mem.jsonl
Token counts: codegen=3693.3310858581503 new=3155.6809332482626 savings=14.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/operator.mem.jsonl
Token counts: codegen=3694.2025288147256 new=3156.6636676414932 savings=14.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/jit-compiler.mem.jsonl
Token counts: codegen=3688.448757124219 new=3151.841584838289 savings=14.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/fabric.mem.jsonl
Token counts: codegen=3699.27692206316 new=3173.7228473057344 savings=14.2%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/mgmt.mem.jsonl
Token counts: codegen=3696.9719738115946 new=3172.6375137423493 savings=14.2%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/jit-compiler.test.jsonl
Token counts: codegen=3695.030762167126 new=3170.997146792601 savings=14.2%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/Knitter.test.jsonl
Token counts: codegen=3701.479499120693 new=3178.6768546863805 savings=14.1%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/mgmt.test.jsonl
Token counts: codegen=3700.5030247837117 new=3178.10479411956 savings=14.1%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/fabric.test.jsonl
Token counts: codegen=3714.6256560047505 new=3194.0911264825304 savings=14.0%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/agollo.test.jsonl
Token counts: codegen=3713.856336039534 new=3193.432259410198 savings=14.0%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/circuit.test.jsonl
Token counts: codegen=3713.3863811357073 new=3193.043294834777 savings=14.0%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/okta-sdk-golang.mem.jsonl
Token counts: codegen=3709.542912957151 new=3189.8852078396208 savings=14.0%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/gogstash.test.jsonl
Token counts: codegen=3708.0702214825246 new=3188.651020996031 savings=14.0%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/gohan.test.jsonl
Token counts: codegen=3706.3633180931674 new=3187.265690443457 savings=14.0%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/provider-aws.test.jsonl
Token counts: codegen=3705.185433728234 new=3186.********** savings=14.0%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/mu.mem.jsonl
Token counts: codegen=3702.2151591183383 new=3184.049625230261 savings=14.0%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/gohan.mem.jsonl
Token counts: codegen=3699.0712374740774 new=3181.************ savings=14.0%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/kubermatic.mem.jsonl
Token counts: codegen=3683.************ new=3168.97670270524 savings=14.0%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/liquidity.test.jsonl
Token counts: codegen=3685.1616329084227 new=3170.2722765308517 savings=14.0%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/machine-config-operator.test.jsonl
Token counts: codegen=3670.9987964655697 new=3162.7180530164533 savings=13.8%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/flamingo.mem.jsonl
Token counts: codegen=3665.7319427763505 new=3158.2927162990104 savings=13.8%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/machine-config-operator.mem.jsonl
Token counts: codegen=3655.************ new=3161.42620152633 savings=13.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/weapp.mem.jsonl
Token counts: codegen=3651.9197683919865 new=3158.************ savings=13.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/operator.test.jsonl
Token counts: codegen=3651.0288332934433 new=3157.************ savings=13.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/istio-operator.mem.jsonl
Token counts: codegen=3651.5893153614543 new=3158.1115316430764 savings=13.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/go-glint.mem.jsonl
Token counts: codegen=3651.7184415511374 new=3158.3568797277917 savings=13.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/liquidity.mem.jsonl
Token counts: codegen=3652.9898012221674 new=3159.5238041722273 savings=13.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/gogstash.mem.jsonl
Token counts: codegen=3649.************ new=3156.5804800044884 savings=13.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/weapp.test.jsonl
Token counts: codegen=3648.************ new=3155.7952384958357 savings=13.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/go-mc.test.jsonl
Token counts: codegen=3647.8295143998316 new=3155.035218274823 savings=13.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/custom-script-extension-linux.mem.jsonl
Token counts: codegen=3646.************ new=3154.0301731383424 savings=13.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/cli.test.jsonl
Token counts: codegen=3645.************ new=3153.3673080693497 savings=13.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/go-spring.test.jsonl
Token counts: codegen=3643.643074943352 new=3151.522925000699 savings=13.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/circuit.mem.jsonl
Token counts: codegen=3642.4084491290855 new=3150.484664634999 savings=13.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/gochain.test.jsonl
Token counts: codegen=3641.0427454096784 new=3149.828084263945 savings=13.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/Xray-core.mem.jsonl
Token counts: codegen=3629.6604599612083 new=3140.578165696869 savings=13.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/Knitter.mem.jsonl
Token counts: codegen=3656.054816710453 new=3169.432517049399 savings=13.3%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/rapidsms.test.jsonl
Token counts: codegen=3654.9414494094594 new=3168.455314864203 savings=13.3%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/autonomous-learning-library.test.jsonl
Token counts: codegen=3653.5024887800896 new=3167.1949272405823 savings=13.3%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/freeseer.test.jsonl
Token counts: codegen=3652.776845797251 new=3166.5291534686444 savings=13.3%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/freeseer.mem.jsonl
Token counts: codegen=3656.144818315138 new=3170.0324805822606 savings=13.3%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/Conditional_Density_Estimation.mem.jsonl
Token counts: codegen=3655.0502997910958 new=3169.093030196153 savings=13.3%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/paasta.mem.jsonl
Token counts: codegen=3653.64502445219 new=3167.481735159817 savings=13.3%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/robosuite.mem.jsonl
Token counts: codegen=3652.257973126113 new=3166.237021747342 savings=13.3%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/voicebook.mem.jsonl
Token counts: codegen=3650.6595168483314 new=3164.8554317511216 savings=13.3%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/scout.mem.jsonl
Token counts: codegen=3641.8314308325844 new=3157.123834690346 savings=13.3%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/wal-e.test.jsonl
Token counts: codegen=3640.9463373426124 new=3156.3432961905146 savings=13.3%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/ReAgent.test.jsonl
Token counts: codegen=3639.251188578928 new=3154.778984585303 savings=13.3%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/community.general.test.jsonl
Token counts: codegen=3637.508856704265 new=3153.1084162050324 savings=13.3%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/programmingbitcoin.mem.jsonl
Token counts: codegen=3638.6011364091073 new=3154.037453483254 savings=13.3%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/deepxde.mem.jsonl
Token counts: codegen=3635.4878591312918 new=3151.3119597213527 savings=13.3%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/errbot.test.jsonl
Token counts: codegen=3634.828255659121 new=3150.735326231691 savings=13.3%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/Conditional_Density_Estimation.test.jsonl
Token counts: codegen=3634.3350415973377 new=3150.297677204659 savings=13.3%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/oioioi.mem.jsonl
Token counts: codegen=3619.1266395176644 new=3136.81743177491 savings=13.3%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/Decoupled-Classification-Refinement.mem.jsonl
Token counts: codegen=3622.0261594126346 new=3139.023293893936 savings=13.3%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/oioioi.test.jsonl
Token counts: codegen=3616.8082031147064 new=3134.4022108619465 savings=13.3%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/robosuite.test.jsonl
Token counts: codegen=3616.1359374176895 new=3133.808684085761 savings=13.3%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/rapidsms.mem.jsonl
Token counts: codegen=3612.917716856534 new=3130.988581651714 savings=13.3%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/betamax.mem.jsonl
Token counts: codegen=3611.0767622853837 new=3129.3754108274393 savings=13.3%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/socorro.test.jsonl
Token counts: codegen=3610.306542497997 new=3128.6381464016918 savings=13.3%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/gmond_python_modules.test.jsonl
Token counts: codegen=3610.495233217775 new=3128.789184788318 savings=13.3%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/vision.test.jsonl
Token counts: codegen=3611.8081070444705 new=3129.76082906992 savings=13.3%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/wal-e.mem.jsonl
Token counts: codegen=3609.290580033025 new=3127.5430372447777 savings=13.3%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/viztracer.mem.jsonl
Token counts: codegen=3607.9225349345834 new=3126.323061408908 savings=13.3%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/robotics-rl-srl.mem.jsonl
Token counts: codegen=3606.8290952873276 new=3125.3462066979887 savings=13.3%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/python-learning.test.jsonl
Token counts: codegen=3606.002629616547 new=3124.588994858511 savings=13.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/Decoupled-Classification-Refinement.test.jsonl
Token counts: codegen=3606.1041323394797 new=3124.6166339741076 savings=13.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/Cinders-DS3.mem.jsonl
Token counts: codegen=3606.7738213076323 new=3125.120239645741 savings=13.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/BERT-KPE.test.jsonl
Token counts: codegen=3607.1932044522555 new=3125.4287574041527 savings=13.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/vision.mem.jsonl
Token counts: codegen=3607.9562356422534 new=3125.7409570533036 savings=13.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/autonomous-learning-library.mem.jsonl
Token counts: codegen=3604.0800145151047 new=3122.3458094114903 savings=13.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/voicebook.test.jsonl
Token counts: codegen=3603.386364225199 new=3121.7566520714045 savings=13.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/betamax.test.jsonl
Token counts: codegen=3602.8281828778654 new=3121.26424038337 savings=13.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/errbot.mem.jsonl
Token counts: codegen=3601.4581758890213 new=3120.0526942388324 savings=13.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/gmond_python_modules.mem.jsonl
Token counts: codegen=3601.305591475715 new=3119.8510707081155 savings=13.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/community.general.mem.jsonl
Token counts: codegen=3594.4496150840923 new=3113.4151968130755 savings=13.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/sir-lancebot.mem.jsonl
Token counts: codegen=3592.677717780735 new=3111.8493960487253 savings=13.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/BERT-KPE.mem.jsonl
Token counts: codegen=3593.112245549919 new=3112.0829829919367 savings=13.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/sktime.mem.jsonl
Token counts: codegen=3589.8209243515944 new=3108.890930578323 savings=13.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/web2py.test.jsonl
Token counts: codegen=3591.4114421355944 new=3110.0946698993384 savings=13.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/deepxde.test.jsonl
Token counts: codegen=3590.348719770599 new=3109.165922298069 savings=13.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/ReAgent.mem.jsonl
Token counts: codegen=3584.339765068848 new=3103.737807730727 savings=13.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/paasta.test.jsonl
Token counts: codegen=3584.8919673186933 new=3104.0107338140397 savings=13.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/gpodder.test.jsonl
Token counts: codegen=3584.887000871069 new=3103.989723908954 savings=13.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/robotics-rl-srl.test.jsonl
Token counts: codegen=3584.5307703957997 new=3103.6700196890147 savings=13.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/web2py.mem.jsonl
Token counts: codegen=3589.5839350861743 new=3107.8950685620835 savings=13.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/python-learning.mem.jsonl
Token counts: codegen=3587.30954176407 new=3105.7906015132094 savings=13.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/socorro.mem.jsonl
Token counts: codegen=3583.229380570913 new=3102.120689006732 savings=13.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/viztracer.test.jsonl
Token counts: codegen=3582.8872831645444 new=3101.812393462348 savings=13.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/Cinders-DS3.test.jsonl
Token counts: codegen=3582.87687762868 new=3101.7798793310635 savings=13.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/sktime.test.jsonl
Token counts: codegen=3581.6965539645653 new=3100.614351400655 savings=13.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/sir-lancebot.test.jsonl
Token counts: codegen=3581.0152881516824 new=3100.0111538557617 savings=13.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/PythonJS.test.jsonl
Token counts: codegen=3580.518782537047 new=3099.596471954158 savings=13.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/scout.test.jsonl
Token counts: codegen=3578.0555036092305 new=3097.4392664347783 savings=13.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/gpodder.mem.jsonl
Token counts: codegen=3577.7595351386362 new=3097.1810742133976 savings=13.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/programmingbitcoin.test.jsonl
Token counts: codegen=3577.950636347787 new=3097.3356329854796 savings=13.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/PythonJS.mem.jsonl
Token counts: codegen=3574.721020169614 new=3094.604852147529 savings=13.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/ssl-vision.mem.jsonl
Token counts: codegen=3571.65197161079 new=3091.7682085515053 savings=13.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/incubator-pagespeed-mod.test.jsonl
Token counts: codegen=3570.5191098150185 new=3090.4367639971824 savings=13.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/OneFLOW.test.jsonl
Token counts: codegen=3566.4647208247015 new=3086.9558547893853 savings=13.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/DOOM-3-BFG.mem.jsonl
Token counts: codegen=3591.324040448504 new=3110.589963684384 savings=13.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/spring.test.jsonl
Token counts: codegen=3589.448141803647 new=3108.435798374458 savings=13.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/ssl-vision.test.jsonl
Token counts: codegen=3588.4982882117274 new=3107.574068228724 savings=13.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/Hazel.test.jsonl
Token counts: codegen=3587.1984859697436 new=3106.4496354438365 savings=13.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/AnalogTapeModel.test.jsonl
Token counts: codegen=3586.385387862029 new=3105.737750448746 savings=13.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/libzim.test.jsonl
Token counts: codegen=3585.686940814199 new=3105.130203913391 savings=13.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/kdenlive.mem.jsonl
Token counts: codegen=3591.262514776483 new=3108.795662348315 savings=13.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/Crystal.mem.jsonl
Token counts: codegen=3590.3684787585244 new=3108.0613388274414 savings=13.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/crossuo.mem.jsonl
Token counts: codegen=3630.3374644764926 new=3141.0185960402346 savings=13.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/HTMLCOIN.mem.jsonl
Token counts: codegen=3633.6695449241543 new=3143.1104084014 savings=13.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/spring.mem.jsonl
Token counts: codegen=3625.073795284251 new=3136.2851217891434 savings=13.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/fs2open.github.com.mem.jsonl
Token counts: codegen=3659.636330870427 new=3168.896208776356 savings=13.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/Hazel.mem.jsonl
Token counts: codegen=3666.44772814137 new=3174.928078419029 savings=13.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/TranslucentTB.mem.jsonl
Token counts: codegen=3663.4985740911234 new=3172.385399591314 savings=13.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/scrimmage.test.jsonl
Token counts: codegen=3660.719565802458 new=3169.9785256122723 savings=13.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/AnalogTapeModel.mem.jsonl
Token counts: codegen=3658.692083590117 new=3168.186105664183 savings=13.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/Algorithms.mem.jsonl
Token counts: codegen=3656.0502899621592 new=3165.9390407738297 savings=13.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/gerona.mem.jsonl
Token counts: codegen=3651.9408459201 new=3162.270086692287 savings=13.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/mongo-cxx-driver.mem.jsonl
Token counts: codegen=3648.202380024713 new=3158.7132059088735 savings=13.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/pro-TBB.mem.jsonl
Token counts: codegen=3645.367043737221 new=3156.2743354964887 savings=13.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/libzim.mem.jsonl
Token counts: codegen=3643.0821151476807 new=3154.2848611989834 savings=13.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/scrimmage.mem.jsonl
Token counts: codegen=3635.8544380792287 new=3147.9928225431863 savings=13.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/curve.test.jsonl
Token counts: codegen=3636.8645862019584 new=3147.518092993027 savings=13.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/rippled.mem.jsonl
Token counts: codegen=3633.553724865879 new=3143.554793474942 savings=13.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/virt86.test.jsonl
Token counts: codegen=3633.0506142827553 new=3143.1180055160085 savings=13.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/OneFLOW.mem.jsonl
Token counts: codegen=3622.4147224631397 new=3133.8647766695576 savings=13.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/gfxreconstruct.test.jsonl
Token counts: codegen=3627.6195346015515 new=3137.4819408935305 savings=13.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/mongo-cxx-driver.test.jsonl
Token counts: codegen=3625.526873343072 new=3135.6256884704862 savings=13.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/pfaedle.test.jsonl
Token counts: codegen=3625.1646799212513 new=3135.295102005495 savings=13.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/Crystal.test.jsonl
Token counts: codegen=3624.7534142886493 new=3134.942981649888 savings=13.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/fs2open.github.com.test.jsonl
Token counts: codegen=3629.02480103248 new=3139.4409550440955 savings=13.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/incubator-pagespeed-mod.mem.jsonl
Token counts: codegen=3625.1095244682324 new=3135.092744123687 savings=13.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/TranslucentTB.test.jsonl
Token counts: codegen=3624.052821358294 new=3134.1817795032707 savings=13.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/kdenlive.test.jsonl
Token counts: codegen=3623.40795930072 new=3133.4010026127407 savings=13.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/Adafruit_EPD.mem.jsonl
Token counts: codegen=3622.3769837478635 new=3132.499686846491 savings=13.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/Algorithms.test.jsonl
Token counts: codegen=3621.567097178151 new=3131.81596960596 savings=13.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/immer.test.jsonl
Token counts: codegen=3620.694346627068 new=3131.051898599915 savings=13.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/curve.mem.jsonl
Token counts: codegen=3617.9161667855196 new=3125.5458387516255 savings=13.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/Grid.test.jsonl
Token counts: codegen=3620.1602274155043 new=3127.3503580851866 savings=13.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/HTMLCOIN.test.jsonl
Token counts: codegen=3621.3537178523716 new=3128.0764863059453 savings=13.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/gerona.test.jsonl
Token counts: codegen=3620.08807576769 new=3126.9620848631507 savings=13.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/pro-TBB.test.jsonl
Token counts: codegen=3619.5601296832974 new=3126.5172424577277 savings=13.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/gfxreconstruct.mem.jsonl
Token counts: codegen=3644.583513648469 new=3145.4703000166446 savings=13.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/rippled.test.jsonl
Token counts: codegen=3643.4915386051143 new=3144.2157655695887 savings=13.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/TitanEngine.mem.jsonl
Token counts: codegen=3644.5280688118555 new=3144.723498626872 savings=13.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/Mesen.test.jsonl
Token counts: codegen=3642.105767739133 new=3142.7489580424644 savings=13.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/Mesen.mem.jsonl
Token counts: codegen=3632.81916391707 new=3135.0489376792375 savings=13.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/crossuo.test.jsonl
Token counts: codegen=3649.371267455996 new=3149.4922003493753 savings=13.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/Grid.mem.jsonl
Token counts: codegen=3648.797057238441 new=3148.766439909297 savings=13.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/dropEst.test.jsonl
Token counts: codegen=3647.981824835326 new=3148.070372855168 savings=13.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/Adafruit_EPD.test.jsonl
Token counts: codegen=3647.593487341579 new=3147.730194221134 savings=13.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/pfaedle.mem.jsonl
Token counts: codegen=3646.590313245672 new=3146.8362390211705 savings=13.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/dropEst.mem.jsonl
Token counts: codegen=3644.181195731559 new=3144.7916801915117 savings=13.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/virt86.mem.jsonl
Token counts: codegen=3642.8594814304247 new=3143.639474537788 savings=13.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/TitanEngine.test.jsonl
Token counts: codegen=3644.7495718267037 new=3144.8828680010133 savings=13.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/DOOM-3-BFG.test.jsonl
Token counts: codegen=3654.2379276874544 new=3154.0290685917657 savings=13.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/immer.mem.jsonl
Token counts: codegen=3657.6493225917175 new=3156.832979990712 savings=13.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/web.test.jsonl
Token counts: codegen=3651.7422021304446 new=3151.529228166972 savings=13.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/opentok-rtc.test.jsonl
Token counts: codegen=3651.408278305685 new=3151.217818891931 savings=13.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/extension-save-to-pocket.test.jsonl
Token counts: codegen=3651.0585671725917 new=3150.912053625347 savings=13.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/badgeyay.mem.jsonl
Token counts: codegen=3646.461505216395 new=3146.93498100388 savings=13.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/Scrawl-canvas.mem.jsonl
Token counts: codegen=3646.7535454235453 new=3147.3027601147373 savings=13.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/next.mem.jsonl
Token counts: codegen=3645.376462332715 new=3146.090862613403 savings=13.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/Scrawl-canvas.test.jsonl
Token counts: codegen=3644.8538056217817 new=3145.646655180013 savings=13.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/sepal.test.jsonl
Token counts: codegen=3640.0345861955184 new=3141.378768335483 savings=13.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/badgeyay.test.jsonl
Token counts: codegen=3638.461662235903 new=3140.0174298906636 savings=13.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/grabient.test.jsonl
Token counts: codegen=3638.556612445546 new=3140.0214719635505 savings=13.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/next.test.jsonl
Token counts: codegen=3638.0675517727095 new=3139.5899841160426 savings=13.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/airframe-react.test.jsonl
Token counts: codegen=3636.8398007864744 new=3138.339920554125 savings=13.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/delite.mem.jsonl
Token counts: codegen=3638.104909823637 new=3139.5866251695793 savings=13.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/synclounge.mem.jsonl
Token counts: codegen=3636.680342170068 new=3138.3220506276107 savings=13.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/service-mocker.mem.jsonl
Token counts: codegen=3635.1633051658164 new=3136.996173469388 savings=13.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/open-pryv.io.mem.jsonl
Token counts: codegen=3632.051724137931 new=3134.13778180154 savings=13.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/open-pryv.io.test.jsonl
Token counts: codegen=3630.885471666219 new=3133.080641151846 savings=13.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/intern-examples.test.jsonl
Token counts: codegen=3630.282376800644 new=3132.5591565678155 savings=13.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/usgs-lidar.mem.jsonl
Token counts: codegen=3650.862127566536 new=3151.0971855061516 savings=13.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/ui-kit.mem.jsonl
Token counts: codegen=3646.7108450466717 new=3147.3169693783416 savings=13.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/dev-cover.test.jsonl
Token counts: codegen=3646.022472578694 new=3146.7163754289227 savings=13.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/ieaseMusic.test.jsonl
Token counts: codegen=3645.054725529658 new=3145.8657638276145 savings=13.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/sdkjs.mem.jsonl
Token counts: codegen=3731.6354184168654 new=3219.8777117781356 savings=13.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/ProjectABE.test.jsonl
Token counts: codegen=3730.871138552729 new=3219.1930353173625 savings=13.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/extension-save-to-pocket.mem.jsonl
Token counts: codegen=3729.893569954276 new=3218.34211279985 savings=13.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/lwc-recipes-oss.mem.jsonl
Token counts: codegen=3728.543127615889 new=3217.1690160309563 savings=13.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/service-mocker.test.jsonl
Token counts: codegen=3727.9861035718163 new=3216.685632790833 savings=13.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/apify-js.mem.jsonl
Token counts: codegen=3726.1365147315573 new=3215.0711079985404 savings=13.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/lwc-recipes-oss.test.jsonl
Token counts: codegen=3725.662826935974 new=3214.657422383526 savings=13.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/ieaseMusic.mem.jsonl
Token counts: codegen=3723.116005714567 new=3212.382412926745 savings=13.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/omi.mem.jsonl
Token counts: codegen=3697.836784477825 new=3178.5689275025097 savings=14.0%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/sepal.mem.jsonl
Token counts: codegen=3684.513884351519 new=3166.783777477015 savings=14.1%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/ebayui-core.mem.jsonl
Token counts: codegen=3679.4915814876586 new=3162.3980321086815 savings=14.1%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/intern-examples.mem.jsonl
Token counts: codegen=3682.0978460277697 new=3163.4738925155752 savings=14.1%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/ProjectABE.mem.jsonl
Token counts: codegen=3683.543387276786 new=3164.722498139881 savings=14.1%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/showdown.test.jsonl
Token counts: codegen=3683.035307792449 new=3164.278445229682 savings=14.1%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/usgs-lidar.test.jsonl
Token counts: codegen=3687.745711336947 new=3168.4615205809337 savings=14.1%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/mf-geoadmin3.mem.jsonl
Token counts: codegen=3739.4456319055967 new=3211.260666277031 savings=14.1%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/Blog.mem.jsonl
Token counts: codegen=3737.516317448544 new=3209.541998887447 savings=14.1%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/grabient.mem.jsonl
Token counts: codegen=3736.230995858695 new=3208.3702437533007 savings=14.1%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/sdkjs.test.jsonl
Token counts: codegen=3757.512749789432 new=3226.0257124610102 savings=14.1%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/airframe-react.mem.jsonl
Token counts: codegen=3750.9648168834647 new=3220.0129432509534 savings=14.2%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/apify-js.test.jsonl
Token counts: codegen=3750.3184050212294 new=3219.4552150636882 savings=14.2%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/synclounge.test.jsonl
Token counts: codegen=3749.8801196035365 new=3219.0659942043967 savings=14.2%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/B78XH.test.jsonl
Token counts: codegen=3750.42404362509 new=3219.552243075163 savings=14.2%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/mf-geoadmin3.test.jsonl
Token counts: codegen=3761.797748457943 new=3228.836675610138 savings=14.2%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/Blog.test.jsonl
Token counts: codegen=3761.0976198816393 new=3228.220663335853 savings=14.2%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/browserify-shim.test.jsonl
Token counts: codegen=3760.789606606939 new=3227.9561073627547 savings=14.2%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/ebayui-core.test.jsonl
Token counts: codegen=3759.0369950716226 new=3226.4356593431903 savings=14.2%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/showdown.mem.jsonl
Token counts: codegen=3758.7468511766656 new=3226.1524509262326 savings=14.2%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/opentok-rtc.mem.jsonl
Token counts: codegen=3757.5819001610307 new=3225.095679779158 savings=14.2%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/browserify-shim.mem.jsonl
Token counts: codegen=3756.766593378288 new=3224.3935493960607 savings=14.2%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/popmotion.test.jsonl
Token counts: codegen=3755.7873755069386 new=3223.557434638269 savings=14.2%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/ui-kit.test.jsonl
Token counts: codegen=3754.2341549943017 new=3222.1722363148415 savings=14.2%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/popmotion.mem.jsonl
Token counts: codegen=3751.3841895055775 new=3219.717486113024 savings=14.2%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/web.mem.jsonl
Token counts: codegen=3740.453913249542 new=3209.693338315849 savings=14.2%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/omi.test.jsonl
Token counts: codegen=3736.632048037051 new=3202.7440626851203 savings=14.3%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/dev-cover.mem.jsonl
Token counts: codegen=3734.8092248075923 new=3201.1605460971277 savings=14.3%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/delite.test.jsonl
Token counts: codegen=3734.522856297253 new=3200.933955749276 savings=14.3%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/B78XH.mem.jsonl
Token counts: codegen=3738.3603589067866 new=3204.205241975242 savings=14.3%
