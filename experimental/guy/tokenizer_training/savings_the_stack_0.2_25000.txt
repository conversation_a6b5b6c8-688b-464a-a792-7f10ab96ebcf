File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/genie-bt-mesh-stack.test.jsonl
Token counts: codegen=4298.468053491828 new=3850.9375928677564 savings=10.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/wt.test.jsonl
Token counts: codegen=4099.266829865361 new=3685.845777233782 savings=10.1%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/FreeRDP.test.jsonl
Token counts: codegen=4710.477130044843 new=4337.239461883408 savings=7.9%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/Montage.mem.jsonl
Token counts: codegen=6109.629520295203 new=5927.044280442805 savings=3.0%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/epics-base.test.jsonl
Token counts: codegen=5949.489376523859 new=5771.891675374434 savings=3.0%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/clam.mem.jsonl
Token counts: codegen=5796.107260726073 new=5596.472607260726 savings=3.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/Learning-DIY-RTOS.test.jsonl
Token counts: codegen=5519.8687315634215 new=5303.617404129794 savings=3.9%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/superlu.test.jsonl
Token counts: codegen=5441.528636884307 new=5227.698167239404 savings=3.9%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/Adrenaline.test.jsonl
Token counts: codegen=5431.2287822878225 new=5215.555492478002 savings=4.0%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/optee_test.test.jsonl
Token counts: codegen=5654.1591482207905 new=5431.036704959372 savings=3.9%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/zpl.mem.jsonl
Token counts: codegen=5585.114700246373 new=5363.329592116069 savings=4.0%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/rgbds.mem.jsonl
Token counts: codegen=5552.22300405954 new=5331.627875507443 savings=4.0%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/fastpm.test.jsonl
Token counts: codegen=5525.997327632283 new=5304.497327632283 savings=4.0%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/Squirrel-Engine.mem.jsonl
Token counts: codegen=5666.192317476469 new=5409.296362248791 savings=4.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/solanum.mem.jsonl
Token counts: codegen=5560.142446732104 new=5309.560689490065 savings=4.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/stellar-core.test.jsonl
Token counts: codegen=5543.677707305615 new=5288.548743246418 savings=4.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/Libftest.mem.jsonl
Token counts: codegen=5472.466898148148 new=5220.799537037037 savings=4.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/FreeRDP.mem.jsonl
Token counts: codegen=5362.962401688087 new=5103.513140226357 savings=4.8%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/solanum.test.jsonl
Token counts: codegen=5330.907099697885 new=5073.240370090634 savings=4.8%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/rtthread-nano.test.jsonl
Token counts: codegen=5512.964010282776 new=5250.917003305178 savings=4.8%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/EsenthelEngine.mem.jsonl
Token counts: codegen=5498.66991499067 new=5213.104602944226 savings=5.2%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/fastpm.mem.jsonl
Token counts: codegen=5464.004598405886 new=5178.709585121603 savings=5.2%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/PigDev.test.jsonl
Token counts: codegen=5459.1330694275275 new=5173.568107998376 savings=5.2%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/CloverBootloader.mem.jsonl
Token counts: codegen=5710.639155327533 new=5260.363447096438 savings=7.9%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/PigDev.mem.jsonl
Token counts: codegen=5710.26320803107 new=5259.977943870448 savings=7.9%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/xfdashboard.test.jsonl
Token counts: codegen=5708.6054744525545 new=5258.729635036497 savings=7.9%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/rgbds.test.jsonl
Token counts: codegen=5706.28384979949 new=5256.718993802406 savings=7.9%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/rakun.mem.jsonl
Token counts: codegen=5690.3725675283185 new=5242.203020621551 savings=7.9%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/Adrenaline.mem.jsonl
Token counts: codegen=5676.220715522216 new=5228.502452394691 savings=7.9%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/Validity90.test.jsonl
Token counts: codegen=5688.259251259899 new=5240.569762419006 savings=7.9%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/xfdashboard.mem.jsonl
Token counts: codegen=5700.6320660686315 new=5252.569770753239 savings=7.9%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/epk2extract.test.jsonl
Token counts: codegen=5701.165743108838 new=5253.378019323672 savings=7.9%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/zpl.test.jsonl
Token counts: codegen=5693.56086494151 new=5246.452676355902 savings=7.9%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/Montage.test.jsonl
Token counts: codegen=5709.418391746943 new=5276.764774202364 savings=7.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/clam.test.jsonl
Token counts: codegen=5701.715520762424 new=5268.101293396869 savings=7.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/optee_test.mem.jsonl
Token counts: codegen=5741.767840280588 new=5307.465600971267 savings=7.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/CloverBootloader.test.jsonl
Token counts: codegen=5716.5113884555385 new=5254.629453978159 savings=8.1%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/Validity90.mem.jsonl
Token counts: codegen=5742.877414146433 new=5281.3454635782155 savings=8.0%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/aerospike-client-c.mem.jsonl
Token counts: codegen=5739.661637001909 new=5280.095892098294 savings=8.0%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/superlu.mem.jsonl
Token counts: codegen=5693.118130705519 new=5239.0640831872315 savings=8.0%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/ZFSin.test.jsonl
Token counts: codegen=5715.700185439971 new=5261.864569001616 savings=7.9%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/lk2nd.mem.jsonl
Token counts: codegen=5659.100748770675 new=5217.599351810461 savings=7.8%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/genie-bt-mesh-stack.mem.jsonl
Token counts: codegen=5522.7417265103195 new=5084.6561040526285 savings=7.9%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/Squirrel-Engine.test.jsonl
Token counts: codegen=5532.289647577092 new=5093.009511413697 savings=7.9%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/wt.mem.jsonl
Token counts: codegen=5520.420660590023 new=5075.930412623738 savings=8.1%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/rakun.test.jsonl
Token counts: codegen=5516.7607343941245 new=5072.59241126071 savings=8.1%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/lk2nd.test.jsonl
Token counts: codegen=5495.308675184936 new=5054.2254779517725 savings=8.0%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/FreeFlyOS.test.jsonl
Token counts: codegen=5492.633475694611 new=5051.391669465906 savings=8.0%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/Learning-DIY-RTOS.mem.jsonl
Token counts: codegen=5395.543683562207 new=4956.529129978558 savings=8.1%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/Libftest.test.jsonl
Token counts: codegen=5390.965587967184 new=4952.35546946217 savings=8.1%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/rtthread-nano.mem.jsonl
Token counts: codegen=5463.953859210291 new=5023.814677505807 savings=8.1%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/Open-Vehicle-Monitoring-System-3.mem.jsonl
Token counts: codegen=5712.02385677308 new=5262.459836065574 savings=7.9%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/EsenthelEngine.test.jsonl
Token counts: codegen=5733.047109556162 new=5288.3098327976895 savings=7.8%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/epk2extract.mem.jsonl
Token counts: codegen=5728.631794414495 new=5284.619229054355 savings=7.8%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/Open-Vehicle-Monitoring-System-3.test.jsonl
Token counts: codegen=5816.3772858517805 new=5366.337303496952 savings=7.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/epics-base.mem.jsonl
Token counts: codegen=5755.465672581343 new=5309.650588189007 savings=7.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/stellar-core.mem.jsonl
Token counts: codegen=5719.875 new=5277.084346741503 savings=7.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/aerospike-client-c.test.jsonl
Token counts: codegen=5720.53071864908 new=5278.084627057313 savings=7.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/FreeFlyOS.mem.jsonl
Token counts: codegen=5711.53245089667 new=5269.327459048211 savings=7.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C/ZFSin.mem.jsonl
Token counts: codegen=5742.0952525867315 new=5301.012363055386 savings=7.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/streamex.mem.jsonl
Token counts: codegen=5742.295298447994 new=5300.2553409479 savings=7.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/jetty.project.mem.jsonl
Token counts: codegen=5441.30029632212 new=5008.969565975249 savings=7.9%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/deeplearning4j.test.jsonl
Token counts: codegen=5343.375445797726 new=4914.126505618734 savings=8.0%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/FXGLGames.test.jsonl
Token counts: codegen=5335.569221468395 new=4906.914892187815 savings=8.0%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/gctoolkit.mem.jsonl
Token counts: codegen=5314.545925010851 new=4887.531801943174 savings=8.0%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/dynamic-support.test.jsonl
Token counts: codegen=5309.857561723254 new=4882.785859460901 savings=8.0%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/PinDroid.mem.jsonl
Token counts: codegen=5302.424970906068 new=4875.571072319202 savings=8.1%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/jetty.project.test.jsonl
Token counts: codegen=5219.681489644443 new=4795.466340388293 savings=8.1%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/java-iso-tools.mem.jsonl
Token counts: codegen=5205.500726439157 new=4782.144609821457 savings=8.1%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/gctoolkit.test.jsonl
Token counts: codegen=5199.51707914411 new=4776.646075019335 savings=8.1%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/openzaly.test.jsonl
Token counts: codegen=5208.283378308918 new=4781.202229503983 savings=8.2%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/divolte-collector.test.jsonl
Token counts: codegen=5203.805512771146 new=4776.985496085226 savings=8.2%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/threetenbp.test.jsonl
Token counts: codegen=5206.802255904124 new=4779.401480437082 savings=8.2%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/java-tutorial.test.jsonl
Token counts: codegen=5194.156218047035 new=4767.610493353784 savings=8.2%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/learn-java-codes.test.jsonl
Token counts: codegen=5172.660487742838 new=4747.226447489746 savings=8.2%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/java-tron.mem.jsonl
Token counts: codegen=5091.252113970589 new=4666.035386029412 savings=8.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/jamonapi.test.jsonl
Token counts: codegen=5086.201713324155 new=4661.308245372495 savings=8.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/Upchain-wallet.mem.jsonl
Token counts: codegen=5069.521855676384 new=4645.68917725182 savings=8.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/cxf-spring-boot-starter.test.jsonl
Token counts: codegen=5067.816332744656 new=4644.113300042628 savings=8.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/java-tutorial.mem.jsonl
Token counts: codegen=5033.747024708512 new=4612.401407599831 savings=8.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/Audinaut.test.jsonl
Token counts: codegen=5030.296047073023 new=4609.138805069402 savings=8.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/kkbinlog.mem.jsonl
Token counts: codegen=5022.253576268634 new=4601.588375244692 savings=8.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/dokit.test.jsonl
Token counts: codegen=5015.782187744302 new=4595.41623068134 savings=8.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/dynamic-support.mem.jsonl
Token counts: codegen=5003.0279870828845 new=4582.7497308934335 savings=8.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/dokit.mem.jsonl
Token counts: codegen=4984.119077380952 new=4564.817410714286 savings=8.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/vulkanbook.mem.jsonl
Token counts: codegen=4922.831028825873 new=4507.01045816733 savings=8.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/spoofax.mem.jsonl
Token counts: codegen=4882.627496449172 new=4469.886837299632 savings=8.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/java-iso-tools.test.jsonl
Token counts: codegen=4878.55657209033 new=4466.121163867979 savings=8.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/h2database.mem.jsonl
Token counts: codegen=4835.267712750919 new=4421.778145320894 savings=8.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/testlink-java-api.test.jsonl
Token counts: codegen=4833.300994462651 new=4419.756356650469 savings=8.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/PinDroid.test.jsonl
Token counts: codegen=4831.093288533755 new=4417.662139650451 savings=8.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/cxf-spring-boot-starter.mem.jsonl
Token counts: codegen=4826.702919193343 new=4413.607248625018 savings=8.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/quincy.mem.jsonl
Token counts: codegen=4807.553855655055 new=4395.8682592706955 savings=8.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/Quests.mem.jsonl
Token counts: codegen=4795.276382050134 new=4384.500223813787 savings=8.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/openremote.test.jsonl
Token counts: codegen=4778.275979083222 new=4368.395527369827 savings=8.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/Quests.test.jsonl
Token counts: codegen=4774.082432695246 new=4364.533742672186 savings=8.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/FXGLGames.mem.jsonl
Token counts: codegen=4757.339400813301 new=4349.169354615618 savings=8.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/Audinaut.mem.jsonl
Token counts: codegen=4749.926782167292 new=4342.120585963364 savings=8.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/quincy.test.jsonl
Token counts: codegen=4743.6558876187855 new=4336.327640820824 savings=8.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/Android-Cookbook-Examples.test.jsonl
Token counts: codegen=4734.122242066218 new=4327.5270229427115 savings=8.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/deeplearning4j.mem.jsonl
Token counts: codegen=4539.267498670987 new=4140.707009594208 savings=8.8%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/jamonapi.mem.jsonl
Token counts: codegen=4528.246322694588 new=4130.417080862874 savings=8.8%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/consulo.mem.jsonl
Token counts: codegen=3969.194836403968 new=3602.25283956703 savings=9.2%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/openzaly.mem.jsonl
Token counts: codegen=3978.703789920771 new=3606.9539124397616 savings=9.3%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/Upchain-wallet.test.jsonl
Token counts: codegen=3975.7795434609657 new=3604.243283490749 savings=9.3%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/spoofax.test.jsonl
Token counts: codegen=3967.7496286348005 new=3596.9280467207946 savings=9.3%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/kkbinlog.test.jsonl
Token counts: codegen=3966.6110444421843 new=3595.846943964202 savings=9.3%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/java-tron.test.jsonl
Token counts: codegen=3958.3668334611193 new=3587.154513538598 savings=9.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/openremote.mem.jsonl
Token counts: codegen=3929.532888313241 new=3560.191909933247 savings=9.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/divolte-collector.mem.jsonl
Token counts: codegen=3924.4562554680665 new=3555.4247991728307 savings=9.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/Android-Cookbook-Examples.mem.jsonl
Token counts: codegen=3908.0351859177217 new=3540.4446795886074 savings=9.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/vulkanbook.test.jsonl
Token counts: codegen=3897.881274759061 new=3530.9180512032162 savings=9.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/learn-java-codes.mem.jsonl
Token counts: codegen=3868.7849344764954 new=3503.7618694217135 savings=9.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/consulo.test.jsonl
Token counts: codegen=3736.239741059737 new=3378.897309160657 savings=9.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/testlink-java-api.mem.jsonl
Token counts: codegen=3732.8893288640047 new=3375.6643030794166 savings=9.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/streamex.test.jsonl
Token counts: codegen=3732.9821421996394 new=3375.6815604403696 savings=9.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/h2database.test.jsonl
Token counts: codegen=3726.754959245352 new=3369.376005128675 savings=9.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Java/threetenbp.mem.jsonl
Token counts: codegen=3732.979734668518 new=3374.7660441488197 savings=9.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/okta-sdk-golang.test.jsonl
Token counts: codegen=3731.0302947299224 new=3373.0321573353785 savings=9.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/go-spring.mem.jsonl
Token counts: codegen=3721.0148298924105 new=3364.224320296598 savings=9.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/kubermatic.test.jsonl
Token counts: codegen=3714.598447705028 new=3358.737303927777 savings=9.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/drago.mem.jsonl
Token counts: codegen=3708.474986911231 new=3353.285551804445 savings=9.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/Xray-core.test.jsonl
Token counts: codegen=3702.7059882611356 new=3348.252907709481 savings=9.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/gateway-api.test.jsonl
Token counts: codegen=3701.271626484347 new=3346.976772220223 savings=9.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/influx-stress.test.jsonl
Token counts: codegen=3712.423560294144 new=3358.3229651737715 savings=9.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/gochain.mem.jsonl
Token counts: codegen=3708.049914584668 new=3356.2794860844187 savings=9.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/gateway-api.mem.jsonl
Token counts: codegen=3704.5415667678976 new=3353.1655211607795 savings=9.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/go-diagrams.mem.jsonl
Token counts: codegen=3698.5279971666373 new=3347.830618027271 savings=9.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/istio-operator.test.jsonl
Token counts: codegen=3698.309338762503 new=3347.641249889351 savings=9.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/flamingo.test.jsonl
Token counts: codegen=3696.1541101342627 new=3345.722400099061 savings=9.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/provider-aws.mem.jsonl
Token counts: codegen=3695.4008831250108 new=3346.166385619665 savings=9.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/agollo.mem.jsonl
Token counts: codegen=3693.3633600250128 new=3344.3297667228294 savings=9.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/go-diagrams.test.jsonl
Token counts: codegen=3691.2052025127546 new=3342.404192551973 savings=9.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/mu.test.jsonl
Token counts: codegen=3690.0052379630915 new=3341.3197759123077 savings=9.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/custom-script-extension-linux.test.jsonl
Token counts: codegen=3689.481133384199 new=3340.8512519941737 savings=9.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/go-glint.test.jsonl
Token counts: codegen=3689.8925178559048 new=3341.2813431800846 savings=9.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/cli.mem.jsonl
Token counts: codegen=3687.3585111542193 new=3339.026846335042 savings=9.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/go-mc.mem.jsonl
Token counts: codegen=3685.9276550126224 new=3338.281529895909 savings=9.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/drago.test.jsonl
Token counts: codegen=3684.0636555275255 new=3336.624978401355 savings=9.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/influx-stress.mem.jsonl
Token counts: codegen=3693.3310858581503 new=3346.4612957183012 savings=9.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/operator.mem.jsonl
Token counts: codegen=3694.2025288147256 new=3347.450163426802 savings=9.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/jit-compiler.mem.jsonl
Token counts: codegen=3688.448757124219 new=3342.2986850236903 savings=9.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/fabric.mem.jsonl
Token counts: codegen=3699.27692206316 new=3363.6208589361318 savings=9.1%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/mgmt.mem.jsonl
Token counts: codegen=3696.9719738115946 new=3362.2166942881054 savings=9.1%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/jit-compiler.test.jsonl
Token counts: codegen=3695.030762167126 new=3360.46694214876 savings=9.1%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/Knitter.test.jsonl
Token counts: codegen=3701.479499120693 new=3368.120579039927 savings=9.0%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/mgmt.test.jsonl
Token counts: codegen=3700.5030247837117 new=3367.458384830547 savings=9.0%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/fabric.test.jsonl
Token counts: codegen=3714.6256560047505 new=3384.399059525911 savings=8.9%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/agollo.test.jsonl
Token counts: codegen=3713.856336039534 new=3383.701472900555 savings=8.9%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/circuit.test.jsonl
Token counts: codegen=3713.3863811357073 new=3383.285338466474 savings=8.9%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/okta-sdk-golang.mem.jsonl
Token counts: codegen=3709.542912957151 new=3379.8834304746047 savings=8.9%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/gogstash.test.jsonl
Token counts: codegen=3708.0702214825246 new=3378.5646844194084 savings=8.9%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/gohan.test.jsonl
Token counts: codegen=3706.3633180931674 new=3377.054017329028 savings=8.9%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/provider-aws.test.jsonl
Token counts: codegen=3705.185433728234 new=3376.184511409716 savings=8.9%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/mu.mem.jsonl
Token counts: codegen=3702.2151591183383 new=3373.4979673505686 savings=8.9%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/gohan.mem.jsonl
Token counts: codegen=3699.0712374740774 new=3370.************ savings=8.9%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/kubermatic.mem.jsonl
Token counts: codegen=3683.************ new=3357.4868180182434 savings=8.9%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/liquidity.test.jsonl
Token counts: codegen=3685.1616329084227 new=3358.************ savings=8.9%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/machine-config-operator.test.jsonl
Token counts: codegen=3670.9987964655697 new=3349.9277574649605 savings=8.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/flamingo.mem.jsonl
Token counts: codegen=3665.7319427763505 new=3345.1971813855907 savings=8.7%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/machine-config-operator.mem.jsonl
Token counts: codegen=3655.************ new=3345.4219131317977 savings=8.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/weapp.mem.jsonl
Token counts: codegen=3651.9197683919865 new=3342.1371756219887 savings=8.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/operator.test.jsonl
Token counts: codegen=3651.0288332934433 new=3341.3463796477495 savings=8.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/istio-operator.mem.jsonl
Token counts: codegen=3651.5893153614543 new=3341.************ savings=8.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/go-glint.mem.jsonl
Token counts: codegen=3651.7184415511374 new=3342.129073985546 savings=8.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/liquidity.mem.jsonl
Token counts: codegen=3652.9898012221674 new=3343.************ savings=8.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/gogstash.mem.jsonl
Token counts: codegen=3649.************ new=3340.************ savings=8.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/weapp.test.jsonl
Token counts: codegen=3648.************ new=3339.4747062617425 savings=8.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/go-mc.test.jsonl
Token counts: codegen=3647.8295143998316 new=3338.6732394366195 savings=8.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/custom-script-extension-linux.mem.jsonl
Token counts: codegen=3646.************ new=3337.60330867933 savings=8.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/cli.test.jsonl
Token counts: codegen=3645.************ new=3336.9004985575443 savings=8.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/go-spring.test.jsonl
Token counts: codegen=3643.643074943352 new=3334.9079223431336 savings=8.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/circuit.mem.jsonl
Token counts: codegen=3642.4084491290855 new=3333.80550507451 savings=8.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/gochain.test.jsonl
Token counts: codegen=3641.0427454096784 new=3332.9787667126743 savings=8.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/Xray-core.mem.jsonl
Token counts: codegen=3629.6604599612083 new=3322.923940149626 savings=8.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Go/Knitter.mem.jsonl
Token counts: codegen=3656.054816710453 new=3352.0339626750883 savings=8.3%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/rapidsms.test.jsonl
Token counts: codegen=3654.9414494094594 new=3350.9980133892122 savings=8.3%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/autonomous-learning-library.test.jsonl
Token counts: codegen=3653.5024887800896 new=3349.666190670475 savings=8.3%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/freeseer.test.jsonl
Token counts: codegen=3652.776845797251 new=3348.9710164629755 savings=8.3%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/freeseer.mem.jsonl
Token counts: codegen=3656.144818315138 new=3352.381755472272 savings=8.3%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/Conditional_Density_Estimation.mem.jsonl
Token counts: codegen=3655.0502997910958 new=3351.386269296508 savings=8.3%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/paasta.mem.jsonl
Token counts: codegen=3653.64502445219 new=3349.6593850476884 savings=8.3%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/robosuite.mem.jsonl
Token counts: codegen=3652.257973126113 new=3348.3574955480008 savings=8.3%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/voicebook.mem.jsonl
Token counts: codegen=3650.6595168483314 new=3346.9308282022607 savings=8.3%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/scout.mem.jsonl
Token counts: codegen=3641.8314308325844 new=3338.802430550898 savings=8.3%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/wal-e.test.jsonl
Token counts: codegen=3640.9463373426124 new=3337.9784920283732 savings=8.3%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/ReAgent.test.jsonl
Token counts: codegen=3639.251188578928 new=3336.32190065489 savings=8.3%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/community.general.test.jsonl
Token counts: codegen=3637.508856704265 new=3334.499779750384 savings=8.3%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/programmingbitcoin.mem.jsonl
Token counts: codegen=3638.6011364091073 new=3335.4263668253907 savings=8.3%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/deepxde.mem.jsonl
Token counts: codegen=3635.4878591312918 new=3332.522703357886 savings=8.3%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/errbot.test.jsonl
Token counts: codegen=3634.828255659121 new=3331.9081624500664 savings=8.3%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/Conditional_Density_Estimation.test.jsonl
Token counts: codegen=3634.3350415973377 new=3331.446163061564 savings=8.3%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/oioioi.mem.jsonl
Token counts: codegen=3619.1266395176644 new=3317.1683282208587 savings=8.3%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/Decoupled-Classification-Refinement.mem.jsonl
Token counts: codegen=3622.0261594126346 new=3319.389657722375 savings=8.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/oioioi.test.jsonl
Token counts: codegen=3616.8082031147064 new=3314.493016943793 savings=8.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/robosuite.test.jsonl
Token counts: codegen=3616.1359374176895 new=3313.8710293420427 savings=8.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/rapidsms.mem.jsonl
Token counts: codegen=3612.917716856534 new=3310.8843958009943 savings=8.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/betamax.mem.jsonl
Token counts: codegen=3611.0767622853837 new=3309.186022664528 savings=8.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/socorro.test.jsonl
Token counts: codegen=3610.306542497997 new=3308.3947828142855 savings=8.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/gmond_python_modules.test.jsonl
Token counts: codegen=3610.495233217775 new=3308.545304128585 savings=8.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/vision.test.jsonl
Token counts: codegen=3611.8081070444705 new=3309.546871310508 savings=8.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/wal-e.mem.jsonl
Token counts: codegen=3609.290580033025 new=3307.211949256939 savings=8.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/viztracer.mem.jsonl
Token counts: codegen=3607.9225349345834 new=3305.9141401574184 savings=8.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/robotics-rl-srl.mem.jsonl
Token counts: codegen=3606.8290952873276 new=3304.8760518773474 savings=8.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/python-learning.test.jsonl
Token counts: codegen=3606.002629616547 new=3304.087222156809 savings=8.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/Decoupled-Classification-Refinement.test.jsonl
Token counts: codegen=3606.1041323394797 new=3304.0836406433896 savings=8.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/Cinders-DS3.mem.jsonl
Token counts: codegen=3606.7738213076323 new=3304.814209429539 savings=8.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/BERT-KPE.test.jsonl
Token counts: codegen=3607.1932044522555 new=3305.135416259845 savings=8.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/vision.mem.jsonl
Token counts: codegen=3607.9562356422534 new=3305.472984724007 savings=8.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/autonomous-learning-library.mem.jsonl
Token counts: codegen=3604.0800145151047 new=3301.888220733272 savings=8.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/voicebook.test.jsonl
Token counts: codegen=3603.386364225199 new=3301.2777029302797 savings=8.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/betamax.test.jsonl
Token counts: codegen=3602.8281828778654 new=3300.7606916202562 savings=8.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/errbot.mem.jsonl
Token counts: codegen=3601.4581758890213 new=3299.4684895698533 savings=8.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/gmond_python_modules.mem.jsonl
Token counts: codegen=3601.305591475715 new=3299.2405343195574 savings=8.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/community.general.mem.jsonl
Token counts: codegen=3594.4496150840923 new=3292.2513161434117 savings=8.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/sir-lancebot.mem.jsonl
Token counts: codegen=3592.677717780735 new=3290.617322653291 savings=8.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/BERT-KPE.mem.jsonl
Token counts: codegen=3593.112245549919 new=3290.8683440459 savings=8.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/sktime.mem.jsonl
Token counts: codegen=3589.8209243515944 new=3287.576849275031 savings=8.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/web2py.test.jsonl
Token counts: codegen=3591.4114421355944 new=3288.9149392604627 savings=8.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/deepxde.test.jsonl
Token counts: codegen=3590.348719770599 new=3287.9267135280534 savings=8.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/ReAgent.mem.jsonl
Token counts: codegen=3584.339765068848 new=3282.1896645466386 savings=8.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/paasta.test.jsonl
Token counts: codegen=3584.8919673186933 new=3282.471037644117 savings=8.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/gpodder.test.jsonl
Token counts: codegen=3584.887000871069 new=3282.442414755154 savings=8.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/robotics-rl-srl.test.jsonl
Token counts: codegen=3584.5307703957997 new=3282.1009188206785 savings=8.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/web2py.mem.jsonl
Token counts: codegen=3589.5839350861743 new=3286.5876965656057 savings=8.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/python-learning.mem.jsonl
Token counts: codegen=3587.30954176407 new=3284.4047457456704 savings=8.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/socorro.mem.jsonl
Token counts: codegen=3583.229380570913 new=3280.48634146953 savings=8.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/viztracer.test.jsonl
Token counts: codegen=3582.8872831645444 new=3280.1551940238646 savings=8.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/Cinders-DS3.test.jsonl
Token counts: codegen=3582.87687762868 new=3280.1590727017824 savings=8.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/sktime.test.jsonl
Token counts: codegen=3581.6965539645653 new=3278.9556813354325 savings=8.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/sir-lancebot.test.jsonl
Token counts: codegen=3581.0152881516824 new=3278.3235867202916 savings=8.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/PythonJS.test.jsonl
Token counts: codegen=3580.518782537047 new=3277.86105042384 savings=8.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/scout.test.jsonl
Token counts: codegen=3578.0555036092305 new=3275.5977484384935 savings=8.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/gpodder.mem.jsonl
Token counts: codegen=3577.7595351386362 new=3275.3121994967737 savings=8.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/programmingbitcoin.test.jsonl
Token counts: codegen=3577.950636347787 new=3275.471781524744 savings=8.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/Python/PythonJS.mem.jsonl
Token counts: codegen=3574.721020169614 new=3272.5415578601805 savings=8.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/ssl-vision.mem.jsonl
Token counts: codegen=3571.65197161079 new=3269.5471995433904 savings=8.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/incubator-pagespeed-mod.test.jsonl
Token counts: codegen=3570.5191098150185 new=3268.1154373694812 savings=8.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/OneFLOW.test.jsonl
Token counts: codegen=3566.4647208247015 new=3264.4039903324456 savings=8.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/DOOM-3-BFG.mem.jsonl
Token counts: codegen=3591.324040448504 new=3289.536480686695 savings=8.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/spring.test.jsonl
Token counts: codegen=3589.448141803647 new=3287.593492971778 savings=8.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/ssl-vision.test.jsonl
Token counts: codegen=3588.4982882117274 new=3286.6710938448464 savings=8.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/Hazel.test.jsonl
Token counts: codegen=3587.1984859697436 new=3285.4957842506883 savings=8.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/AnalogTapeModel.test.jsonl
Token counts: codegen=3586.385387862029 new=3284.745306360064 savings=8.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/libzim.test.jsonl
Token counts: codegen=3585.686940814199 new=3284.0908881507166 savings=8.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/kdenlive.mem.jsonl
Token counts: codegen=3591.262514776483 new=3288.1621070661745 savings=8.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/Crystal.mem.jsonl
Token counts: codegen=3590.3684787585244 new=3287.3775753632617 savings=8.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/crossuo.mem.jsonl
Token counts: codegen=3630.3374644764926 new=3322.757131233565 savings=8.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/HTMLCOIN.mem.jsonl
Token counts: codegen=3633.6695449241543 new=3325.175379229872 savings=8.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/spring.mem.jsonl
Token counts: codegen=3625.073795284251 new=3318.092584954515 savings=8.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/fs2open.github.com.mem.jsonl
Token counts: codegen=3659.636330870427 new=3353.1624954946838 savings=8.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/Hazel.mem.jsonl
Token counts: codegen=3666.44772814137 new=3359.6179770228646 savings=8.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/TranslucentTB.mem.jsonl
Token counts: codegen=3663.4985740911234 new=3356.9288280600904 savings=8.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/scrimmage.test.jsonl
Token counts: codegen=3660.719565802458 new=3354.370122005921 savings=8.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/AnalogTapeModel.mem.jsonl
Token counts: codegen=3658.692083590117 new=3352.484721833156 savings=8.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/Algorithms.mem.jsonl
Token counts: codegen=3656.0502899621592 new=3350.087234947717 savings=8.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/gerona.mem.jsonl
Token counts: codegen=3651.9408459201 new=3346.210038877469 savings=8.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/mongo-cxx-driver.mem.jsonl
Token counts: codegen=3648.202380024713 new=3342.374113613341 savings=8.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/pro-TBB.mem.jsonl
Token counts: codegen=3645.367043737221 new=3339.7663903458083 savings=8.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/libzim.mem.jsonl
Token counts: codegen=3643.0821151476807 new=3337.6372082181747 savings=8.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/scrimmage.mem.jsonl
Token counts: codegen=3635.8544380792287 new=3330.9534847714053 savings=8.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/curve.test.jsonl
Token counts: codegen=3636.8645862019584 new=3330.621782090966 savings=8.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/rippled.mem.jsonl
Token counts: codegen=3633.553724865879 new=3326.4848977188467 savings=8.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/virt86.test.jsonl
Token counts: codegen=3633.0506142827553 new=3326.0230342406767 savings=8.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/OneFLOW.mem.jsonl
Token counts: codegen=3622.4147224631397 new=3316.221379011275 savings=8.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/gfxreconstruct.test.jsonl
Token counts: codegen=3627.6195346015515 new=3320.33669887767 savings=8.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/mongo-cxx-driver.test.jsonl
Token counts: codegen=3625.526873343072 new=3318.3406373424227 savings=8.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/pfaedle.test.jsonl
Token counts: codegen=3625.1646799212513 new=3317.983860848495 savings=8.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/Crystal.test.jsonl
Token counts: codegen=3624.7534142886493 new=3317.6147665956596 savings=8.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/fs2open.github.com.test.jsonl
Token counts: codegen=3629.02480103248 new=3322.407550010755 savings=8.4%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/incubator-pagespeed-mod.mem.jsonl
Token counts: codegen=3625.1095244682324 new=3317.772608774301 savings=8.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/TranslucentTB.test.jsonl
Token counts: codegen=3624.052821358294 new=3316.8118385364037 savings=8.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/kdenlive.test.jsonl
Token counts: codegen=3623.40795930072 new=3316.0291012596385 savings=8.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/Adafruit_EPD.mem.jsonl
Token counts: codegen=3622.3769837478635 new=3315.087247752195 savings=8.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/Algorithms.test.jsonl
Token counts: codegen=3621.567097178151 new=3314.357034458606 savings=8.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/immer.test.jsonl
Token counts: codegen=3620.694346627068 new=3313.53595672465 savings=8.5%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/curve.mem.jsonl
Token counts: codegen=3617.9161667855196 new=3307.946442803809 savings=8.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/Grid.test.jsonl
Token counts: codegen=3620.1602274155043 new=3309.9429890689785 savings=8.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/HTMLCOIN.test.jsonl
Token counts: codegen=3621.3537178523716 new=3310.8169463927857 savings=8.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/gerona.test.jsonl
Token counts: codegen=3620.08807576769 new=3309.6363588951936 savings=8.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/pro-TBB.test.jsonl
Token counts: codegen=3619.5601296832974 new=3309.1493442862206 savings=8.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/gfxreconstruct.mem.jsonl
Token counts: codegen=3644.583513648469 new=3330.232918608522 savings=8.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/rippled.test.jsonl
Token counts: codegen=3643.4915386051143 new=3328.975217238018 savings=8.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/TitanEngine.mem.jsonl
Token counts: codegen=3644.5280688118555 new=3329.582548318566 savings=8.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/Mesen.test.jsonl
Token counts: codegen=3642.105767739133 new=3327.471233698406 savings=8.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/Mesen.mem.jsonl
Token counts: codegen=3632.81916391707 new=3319.299053316476 savings=8.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/crossuo.test.jsonl
Token counts: codegen=3649.371267455996 new=3334.564751912881 savings=8.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/Grid.mem.jsonl
Token counts: codegen=3648.797057238441 new=3333.9482830501406 savings=8.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/dropEst.test.jsonl
Token counts: codegen=3647.981824835326 new=3333.213913962755 savings=8.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/Adafruit_EPD.test.jsonl
Token counts: codegen=3647.593487341579 new=3332.859034687427 savings=8.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/pfaedle.mem.jsonl
Token counts: codegen=3646.590313245672 new=3331.901446920851 savings=8.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/dropEst.mem.jsonl
Token counts: codegen=3644.181195731559 new=3329.733587600422 savings=8.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/virt86.mem.jsonl
Token counts: codegen=3642.8594814304247 new=3328.5109167207265 savings=8.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/TitanEngine.test.jsonl
Token counts: codegen=3644.7495718267037 new=3329.8910666328857 savings=8.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/DOOM-3-BFG.test.jsonl
Token counts: codegen=3654.2379276874544 new=3339.5377335598155 savings=8.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/C++/immer.mem.jsonl
Token counts: codegen=3657.6493225917175 new=3342.468057827044 savings=8.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/web.test.jsonl
Token counts: codegen=3651.7422021304446 new=3336.824328950283 savings=8.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/opentok-rtc.test.jsonl
Token counts: codegen=3651.408278305685 new=3336.4876993074568 savings=8.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/extension-save-to-pocket.test.jsonl
Token counts: codegen=3651.0585671725917 new=3336.164489311164 savings=8.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/badgeyay.mem.jsonl
Token counts: codegen=3646.461505216395 new=3331.9447202846404 savings=8.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/Scrawl-canvas.mem.jsonl
Token counts: codegen=3646.7535454235453 new=3332.220207409785 savings=8.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/next.mem.jsonl
Token counts: codegen=3645.376462332715 new=3330.935752593855 savings=8.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/Scrawl-canvas.test.jsonl
Token counts: codegen=3644.8538056217817 new=3330.4445435057 savings=8.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/sepal.test.jsonl
Token counts: codegen=3640.0345861955184 new=3325.9182289948108 savings=8.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/badgeyay.test.jsonl
Token counts: codegen=3638.461662235903 new=3324.4744148393934 savings=8.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/grabient.test.jsonl
Token counts: codegen=3638.556612445546 new=3324.47046480956 savings=8.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/next.test.jsonl
Token counts: codegen=3638.0675517727095 new=3324.0133864796558 savings=8.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/airframe-react.test.jsonl
Token counts: codegen=3636.8398007864744 new=3322.680799249456 savings=8.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/delite.mem.jsonl
Token counts: codegen=3638.104909823637 new=3323.9389813263106 savings=8.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/synclounge.mem.jsonl
Token counts: codegen=3636.680342170068 new=3322.6020777459844 savings=8.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/service-mocker.mem.jsonl
Token counts: codegen=3635.1633051658164 new=3321.1946049904336 savings=8.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/open-pryv.io.mem.jsonl
Token counts: codegen=3632.051724137931 new=3318.117456274748 savings=8.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/open-pryv.io.test.jsonl
Token counts: codegen=3630.885471666219 new=3316.9813262536168 savings=8.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/intern-examples.test.jsonl
Token counts: codegen=3630.282376800644 new=3316.4282476215094 savings=8.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/usgs-lidar.mem.jsonl
Token counts: codegen=3650.862127566536 new=3335.9253841108307 savings=8.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/ui-kit.mem.jsonl
Token counts: codegen=3646.7108450466717 new=3331.9712035392963 savings=8.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/dev-cover.test.jsonl
Token counts: codegen=3646.022472578694 new=3331.3344110121584 savings=8.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/ieaseMusic.test.jsonl
Token counts: codegen=3645.054725529658 new=3330.431102342689 savings=8.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/sdkjs.mem.jsonl
Token counts: codegen=3731.6354184168654 new=3409.102046867433 savings=8.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/ProjectABE.test.jsonl
Token counts: codegen=3730.871138552729 new=3408.3696616448506 savings=8.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/extension-save-to-pocket.mem.jsonl
Token counts: codegen=3729.893569954276 new=3407.481004157655 savings=8.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/lwc-recipes-oss.mem.jsonl
Token counts: codegen=3728.543127615889 new=3406.239121851062 savings=8.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/service-mocker.test.jsonl
Token counts: codegen=3727.9861035718163 new=3405.726838463892 savings=8.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/apify-js.mem.jsonl
Token counts: codegen=3726.1365147315573 new=3404.017458892692 savings=8.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/lwc-recipes-oss.test.jsonl
Token counts: codegen=3725.662826935974 new=3403.5798256341473 savings=8.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/ieaseMusic.mem.jsonl
Token counts: codegen=3723.116005714567 new=3401.16365338194 savings=8.6%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/omi.mem.jsonl
Token counts: codegen=3697.836784477825 new=3363.3476070292636 savings=9.0%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/sepal.mem.jsonl
Token counts: codegen=3684.513884351519 new=3350.869081065945 savings=9.1%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/ebayui-core.mem.jsonl
Token counts: codegen=3679.4915814876586 new=3346.2185924731884 savings=9.1%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/intern-examples.mem.jsonl
Token counts: codegen=3682.0978460277697 new=3347.7331700548502 savings=9.1%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/ProjectABE.mem.jsonl
Token counts: codegen=3683.543387276786 new=3348.9933035714284 savings=9.1%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/showdown.test.jsonl
Token counts: codegen=3683.035307792449 new=3348.522661335317 savings=9.1%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/usgs-lidar.test.jsonl
Token counts: codegen=3687.745711336947 new=3352.8879973222006 savings=9.1%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/mf-geoadmin3.mem.jsonl
Token counts: codegen=3739.4456319055967 new=3398.7000640115775 savings=9.1%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/Blog.mem.jsonl
Token counts: codegen=3737.516317448544 new=3396.895966994252 savings=9.1%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/grabient.mem.jsonl
Token counts: codegen=3736.230995858695 new=3395.6568831818563 savings=9.1%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/sdkjs.test.jsonl
Token counts: codegen=3757.512749789432 new=3414.5757906720596 savings=9.1%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/airframe-react.mem.jsonl
Token counts: codegen=3750.9648168834647 new=3408.2287041054665 savings=9.1%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/apify-js.test.jsonl
Token counts: codegen=3750.3184050212294 new=3407.6386191618976 savings=9.1%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/synclounge.test.jsonl
Token counts: codegen=3749.8801196035365 new=3407.2293508554976 savings=9.1%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/B78XH.test.jsonl
Token counts: codegen=3750.42404362509 new=3407.746996622931 savings=9.1%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/mf-geoadmin3.test.jsonl
Token counts: codegen=3761.797748457943 new=3417.712720936022 savings=9.1%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/Blog.test.jsonl
Token counts: codegen=3761.0976198816393 new=3417.0647941593998 savings=9.1%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/browserify-shim.test.jsonl
Token counts: codegen=3760.789606606939 new=3416.7844633705713 savings=9.1%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/ebayui-core.test.jsonl
Token counts: codegen=3759.0369950716226 new=3415.170641610244 savings=9.1%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/showdown.mem.jsonl
Token counts: codegen=3758.7468511766656 new=3414.8722148565535 savings=9.1%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/opentok-rtc.mem.jsonl
Token counts: codegen=3757.5819001610307 new=3413.7468507016333 savings=9.2%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/browserify-shim.mem.jsonl
Token counts: codegen=3756.766593378288 new=3413.002557427118 savings=9.2%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/popmotion.test.jsonl
Token counts: codegen=3755.7873755069386 new=3412.1233849238097 savings=9.2%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/ui-kit.test.jsonl
Token counts: codegen=3754.2341549943017 new=3410.6732748796 savings=9.2%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/popmotion.mem.jsonl
Token counts: codegen=3751.3841895055775 new=3408.1031262911447 savings=9.2%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/web.mem.jsonl
Token counts: codegen=3740.453913249542 new=3397.3779997630268 savings=9.2%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/omi.test.jsonl
Token counts: codegen=3736.632048037051 new=3389.3785700181306 savings=9.3%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/dev-cover.mem.jsonl
Token counts: codegen=3734.8092248075923 new=3387.701144579394 savings=9.3%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/delite.test.jsonl
Token counts: codegen=3734.522856297253 new=3387.4540766450527 savings=9.3%
File /mnt/efs/augment/data/processed/polycoder.v3/repos_splits/JavaScript/B78XH.mem.jsonl
Token counts: codegen=3738.3603589067866 new=3391.019137512213 savings=9.3%
