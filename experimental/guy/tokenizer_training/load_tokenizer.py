"""Load and evaluate a trained tokenizer."""

import argparse
from pathlib import Path

import colorama
from colorama import Back, Fore, Style
from megatron.tokenizer.tokenizer import get_tokenizer

# from transformers import AutoTokenizer


def prettify(token):
    # return token
    return token.replace("Ġ", " ").replace("Ċ", "\n").replace("ĉ", "\t")


def colorful_tokens(title, tokens, pad):
    fore_colors = [Fore.WHITE, Fore.RED]
    back_colors = [Back.RED, Back.WHITE]
    # back_colors = [Back.RED, Back.WHITE]

    s = f"{title}\n"
    colorful_s = s
    i = 0
    for t in tokens:
        pretty_token = prettify(t)
        s += pretty_token

        # if pretty_token == "\n":
        #     i = 1
        # elif pretty_token.startswith("\n"):
        #     i = 0

        fore = fore_colors[i % len(fore_colors)]
        back = back_colors[i % len(back_colors)]

        # pretty_token = pretty_token.replace("\n", f"\\n{Style.RESET_ALL}\n")

        # if pretty_token.startswith("\n"):
        #     # Put the color commands after the newline so they survive
        #     # splitting into lines
        #     colorful_s += "\n" + str(fore) + str(back) + pretty_token[1:]
        # else:

        colorful_token = f"{fore}{back}{pretty_token}"
        colorful_token = colorful_token.replace(
            "\n", f"\\n{Style.RESET_ALL}\n{fore}{back}"
        )
        colorful_s += colorful_token
        # colorful_s += str(fore) + str(back) + pretty_token

        i += 1

    lines = s.split("\n")
    colorful_lines = colorful_s.split("\n")

    # Colors change the line lengths, so we need to measure length without
    # the colors
    max_line_length = max(map(len, lines))
    padded_lines = [
        colorful_line + " " * (pad + max_line_length - len(line))
        # colorful_line + Style.RESET_ALL
        # colorful_line
        for line, colorful_line in zip(lines, colorful_lines)
    ]
    return padded_lines


def main():
    """Main."""
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--tokenizer",
        type=str,
        default="my-new-tokenizer",
        help="name of the tokenizer to load",
    )
    parser.add_argument(
        "--sample_path", type=str, default=None, help="file to tokenize"
    )
    args = parser.parse_args()
    # gpt2_tokenizer = AutoTokenizer.from_pretrained("gpt2")
    codegen_tokenizer = get_tokenizer("CodeGenTokenizer")
    # tokenizer = AutoTokenizer.from_pretrained(args.tokenizer)
    from megatron.tokenizer.tokenizer import Llama2Tokenizer

    tokenizer = Llama2Tokenizer("/mnt/efs/augment/checkpoints/llama/tokenizer.model")

    colorama.init()

    if args.sample_path:
        with Path(args.sample_path).open("r", encoding="utf8") as sample_file:
            text = sample_file.read()
    else:
        text = """
#include <math.h>

void foo() {
    float sum = 0;
    for (int i = 0; i < N; i++) {
        float x = power(x, i);
        sum += x;
    }
    printf("The sum is %f", sum);
}

int main(int argc, char** argv) {
    foo();
    return 0;
}
"""

    codegen_tokens = [
        codegen_tokenizer.detokenize(token)
        for token in codegen_tokenizer.tokenize(text)
    ]
    codegen_tokens = list(map(prettify, codegen_tokens))
    codegen_lines = colorful_tokens("codegen tokenizer:", codegen_tokens, pad=0)

    tokens = [tokenizer.detokenize(token) for token in tokenizer.tokenize(text)]
    # print(tokens)
    tokens = list(map(prettify, tokens))
    # lines = colorful_tokens("new tokenizer:", tokens, pad=2)
    lines = colorful_tokens("new tokenizer:", tokens, pad=0)

    for line, codegen_line in zip(lines, codegen_lines):
        # print(line + codegen_line)
        print("old:", codegen_line)
        print("new:", line)
        print("-" * 120)

    # print(tokens)
    print(Style.RESET_ALL)
    print("\n")
    print(f"{len(codegen_tokens)} CodeGen tokens")
    print(
        f"{len(tokens)} new tokens, {100*(len(codegen_tokens)-len(tokens))/len(codegen_tokens):.1f}% more efficient"
    )


if __name__ == "__main__":
    main()
