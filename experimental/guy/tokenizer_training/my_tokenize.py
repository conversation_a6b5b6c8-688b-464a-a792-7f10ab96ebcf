"""Simple script for tokenizing something."""

import argparse

from hf_codegen_tokenizer import HFCodeGenTokenizer


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--text",
        type=str,
        default="Hello, World!",
        help="text to tokenize",
    )
    args = parser.parse_args()

    tokenizer = HFCodeGenTokenizer().tokenizer
    print("Tokenizing:")
    print(args.text)
    tokens = tokenizer.tokenize(args.text)

    print("Tokens:")
    print(tokens)


if __name__ == "__main__":
    main()
