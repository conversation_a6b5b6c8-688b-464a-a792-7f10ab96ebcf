#!/bin/bash
python3 evaluate_tokenizer.py --tokenizer /mnt/efs/augment/user/guy/tokenizers/the_stack_0.2_25000 --dataset_path /mnt/efs/augment/data/processed/polycoder.v3/repos_splits > savings_the_stack_0.2_25000.txt
python3 evaluate_tokenizer.py --tokenizer /mnt/efs/augment/user/guy/tokenizers/the_stack_0.2_51000 --dataset_path /mnt/efs/augment/data/processed/polycoder.v3/repos_splits > savings_the_stack_0.2_51000.txt
python3 evaluate_tokenizer.py --tokenizer /mnt/efs/augment/user/guy/tokenizers/the_stack_0.2_100000 --dataset_path /mnt/efs/augment/data/processed/polycoder.v3/repos_splits > savings_the_stack_0.2_100000.txt
