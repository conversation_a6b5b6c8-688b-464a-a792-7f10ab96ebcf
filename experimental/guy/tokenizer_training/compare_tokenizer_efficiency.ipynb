{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Compare a few existing tokenizers on our repo"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Vocab sizes:\n", "codegen\t50328\n", "starcoder\t49168\n", "deepseek\t32035\n", "deepseek_llm\t100028\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Token indices sequence length is longer than the specified maximum sequence length for this model (6773 > 4096). Running this sequence through the model will result in indexing errors\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Tokenizing 5586 files, this will take a few minutes...\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Token indices sequence length is longer than the specified maximum sequence length for this model (17155 > 16384). Running this sequence through the model will result in indexing errors\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Vocab sizes:\n", "codegen\t5064.040637307555\n", "starcoder\t4622.291263873971\n", "deepseek\t5366.0404582885785\n", "deepseek_llm\t5015.734872896527\n"]}], "source": ["from megatron.tokenizer import get_tokenizer\n", "import glob\n", "import os\n", "from pathlib import Path\n", "\n", "tokenizers = {\n", "    \"codegen\": get_tokenizer(\"CodeGenTokenizer\"),\n", "    \"starcoder\": get_tokenizer(\"StarCoderTokenizer\"),\n", "    \"deepseek\": get_tokenizer(\"DeepSeekCoderBaseTokenizer\"), \n", "    \"deepseek_llm\": get_tokenizer(\"DeepSeekLLMBaseTokenizer\"),\n", "}\n", "\n", "print(\"Vocab sizes:\")\n", "for name, t in tokenizers.items():\n", "    print(f\"{name}\\t{t.vocab_size}\")\n", "\n", "extensions = [\".py\", \".ipynb\", \".rs\", \".ts\"]\n", "ignores = [\".git\", \"node_modules\", \"bazel-bin\", \"bazel-out\"]\n", "\n", "os.chdir(\"/home/<USER>/augment\")\n", "paths = []\n", "\n", "for filename in glob.glob(\"**\", recursive=True):\n", "    should_ignore = False\n", "    for ignore in ignores:\n", "        if ignore in filename:\n", "            should_ignore = True\n", "            break\n", "    if should_ignore:\n", "        continue\n", "    if not any(filename.endswith(ext) for ext in extensions):\n", "        continue\n", "    paths.append(filename)\n", "\n", "token_lengths = {\n", "    name: [] for name in tokenizers\n", "}\n", "\n", "print(\"\")\n", "print(f\"Tokenizing {len(paths)} files, this will take a few minutes...\")\n", "print(\"\")\n", "\n", "for path in paths:\n", "    text = Path(path).read_text(encoding=\"utf8\")\n", "    for name, t in tokenizers.items():\n", "        tokens = t.tokenize(text)\n", "        token_lengths[name].append(len(tokens))\n", "\n", "avg_token_lengths = {\n", "    name: sum(lens) / len(lens) for name, lens in token_lengths.items()\n", "}\n", "\n", "print(\"Vocab sizes:\")\n", "for name, avg_len in avg_token_lengths.items():\n", "    print(f\"{name}\\t{avg_len}\")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}