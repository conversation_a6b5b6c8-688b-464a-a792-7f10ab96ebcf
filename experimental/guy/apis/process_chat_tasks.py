"""Process chat tasks by distributing them to servers.

Example:

    chat_clients = [
        Llama3ChatClient("triton", address="********:8000"),
        Llama3ChatClient("triton", address="********:8000"),
    ]

    tasks = [
        "how are you today?",
        "how are you doing?",
    ]

    process_tasks(
        chat_clients=chat_clients,
        tasks=tasks,
        task_processor_fn=example_task_processor,
    )
"""

from pathlib import Path
import threading
from queue import Empty, Queue
from dataclasses import dataclass
import time
from typing import Any, Callable
import yaml

from research.llm_apis.chat_utils import ChatClient


@dataclass
class TaskProcessorInput:
    """The input to a task processor."""

    task_data: Any
    """The data associated with the task."""

    chat_client: ChatClient
    """The client to use for sending chat requests."""

    thread_idx: int
    """The index of the worker thread. Useful for logging."""

    task_idx: int
    """The index of the task in the queue. Useful for logging."""


TaskProcessorFn = Callable[[TaskProcessorInput], None]
"""A function that processes a single task."""


def example_task_processor(inpt: TaskProcessorInput):
    """An example task processor."""
    print(f"[{inpt.thread_idx:03d}] Processing task {inpt.task_idx}: {inpt.task_data}")


def process_tasks(
    chat_clients: list[ChatClient],
    tasks: list,
    task_processor_fn: TaskProcessorFn,
    per_thread_init_delay: float = 0,
):
    """Process the tasks in parallel using all the chat clients.

    Returns once all tasks are processed.
    """
    task_queue = Queue()

    for idx, task in enumerate(tasks):
        task_queue.put((idx, task))

    def _process_task(thread_idx: int, chat_client: ChatClient, task_queue: Queue):
        while True:
            try:
                task_idx, task = task_queue.get(block=True, timeout=10)
                task_processor_fn(
                    TaskProcessorInput(
                        task_data=task,
                        chat_client=chat_client,
                        thread_idx=thread_idx,
                        task_idx=task_idx,
                    )
                )
                task_queue.task_done()
            except Empty:
                break

    threads = []
    for thread_idx, chat_client in enumerate(chat_clients):
        t = threading.Thread(
            target=_process_task,
            args=(thread_idx, chat_client, task_queue),
        )
        t.start()
        threads.append(t)

        # Stagger the threads to avoid overloading the local system
        time.sleep(per_thread_init_delay)

    for t in threads:
        t.join()


def load_addresses_from_yaml(yaml_file: str | Path) -> list[str]:
    """Load server addresses from a yaml file.

    An example yaml file that specifies the addresses:

    ```
    triton_server_addresses:
    mine:
        address: *************
        ports: [8000, 8001, 8002, 8003, 8004, 8005, 8006, 8007]

    cw:
        address: mem-ttl-test
        ports: [8000, 8010, 8020, 8030, 8040, 8050, 8060, 8070]

    gcp:
        addresses:
        - ************
        - *************
        - ***********
        - **************
        - *************
        - **************
        - ************
        - ***********
        ports: [8000, 8010, 8020, 8030, 8040, 8050, 8060, 8070]
    ```
    """
    yaml_file = Path(yaml_file)
    with yaml_file.open("r") as file:
        yaml_data = yaml.safe_load(file)

    addresses = []
    for _, config in yaml_data["triton_server_addresses"].items():
        if "address" in config:
            address = config["address"]
            for port in config["ports"]:
                addresses.append(f"{address}:{port}")
        elif "addresses" in config:
            for address in config["addresses"]:
                for port in config["ports"]:
                    addresses.append(f"{address}:{port}")

    return addresses
