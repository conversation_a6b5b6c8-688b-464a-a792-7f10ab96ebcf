In the following class, rewrite load_llama_hf_checkpoint and load_starcoder_hf_checkpoint and extract common patterns to util methods. Follow these steps:
1. Identify common patterns between these methods that can be refactored
2. Write utility functions that implement these common patterns
3. Rewrite load_llama_hf_checkpoint and load_starcoder_hf_checkpoint to use the utility functions


```python
def get_checkpoint_location(iteration: int | None, output_dir: Path | None):
    if output_dir:
        if iteration is None:
            return (output_dir / "checkpoint_llama_iteration_{iteration}").as_posix()
        else:
            return (output_dir / f"checkpoint_llama_iteration_{iteration}").as_posix()
    else:
        return DETERMINED_LOCATION


class CheckpointManager:
    """A checkpoint manager for fastbackward."""

    def __init__(
        self,
        distributed_context: Context | None = None,
        mp_world_size: int | None = None,
        mp_rank: int | None = None,
    ) -> None:
        if mp_world_size is None and mp_rank is None:
            self.mp_world_size: int = mpu.get_model_parallel_world_size()
            self.mp_rank: int = mpu.get_model_parallel_rank()
        else:
            assert (
                mp_world_size is not None and mp_rank is not None
            ), "Must set both or neither of mp_{world_size,rank}"
            self.mp_world_size: int = mp_world_size
            self.mp_rank: int = mp_rank

        self.context: CheckpointContext | None = (
            distributed_context.checkpoint if distributed_context else None
        )

        self.last_optimizer_location: str | None = None

    def _make_load_context(
        self,
        checkpoint_location: str,
        download_mode: DownloadMode = DownloadMode.LocalWorkersShareDownload,
    ):
        checkpoint_dir = Path(checkpoint_location)
        if not checkpoint_dir.exists():  # Assume it is a Determined storage ID
            assert self.context is not None
            load_context = self.context.restore_path(checkpoint_location, download_mode)
        else:
            load_context = contextlib.nullcontext(checkpoint_dir)
        return load_context


    def load_state_dict_checkpoint(
        self,
        checkpoint_location: str,
    ) -> StateDict:
        with self._make_load_context(checkpoint_location) as checkpoint_dir:
            num_checkpoint_files = len(
                glob.glob(checkpoint_dir.as_posix() + "/consolidated.*.pth")
            )
            assert num_checkpoint_files == self.mp_world_size, (
                f"Expected {self.mp_world_size} files in {checkpoint_dir}, "
                f"but found {num_checkpoint_files}"
            )
            checkpoint_path = checkpoint_dir / f"consolidated.{self.mp_rank:02d}.pth"
            state_dict = torch.load(checkpoint_path, map_location="cuda")

        # The upstream llama2 checkpoints have these extra buffers that aren't defined
        # on the model. We remove them (if present) so that strict module loading works.
        state_dict.pop("rope.freqs", None)
        return state_dict

    def _load_hf_checkpoint(self, checkpoint_location: str) -> tuple[dict, StateDict]:
        with self._make_load_context(checkpoint_location) as checkpoint_dir:
            config_file = checkpoint_dir / "config.json"
            with config_file.open(mode="r") as fh:
                config = json.load(fh)
            model_pattern = checkpoint_dir / "pytorch_model*.bin"
            files = glob.glob(model_pattern.as_posix())
            assert len(files) > 0
            state_dict = torch.load(files[0])
            for f in files[1:]:
                state_dict.update(torch.load(f))
        return config, state_dict

    def _validate_mp_params(self, mp_world_size: int | None, mp_rank: int | None):
        if mp_world_size is None and mp_rank is None:
            mp_world_size = mpu.get_model_parallel_world_size()
            mp_rank = mpu.get_model_parallel_rank()
        else:
            assert (
                mp_world_size is not None and mp_rank is not None
            ), "Must set both or neither of mp_{world_size,rank}"
        return mp_world_size, mp_rank

    def _resize_vocab_weight(
        self,
        state_dict: StateDict,
        new_vocab_size: int | None,
        embedding_key: str,
        lm_head_key: str,
    ):
        if not new_vocab_size:
            return

        # Vocab padding will influence how we MP-split the output weight, so we modify
        # the source statedict to have properly-sized vocab weights before splitting.
        # This is a no-op when the vocab sizes already match
        lm_head_weight = state_dict[lm_head_key]
        state_dict[lm_head_key] = resize_vocab_weight(lm_head_weight, new_vocab_size)
        embedding_weight = state_dict[embedding_key]
        state_dict[embedding_key] = resize_vocab_weight(
            embedding_weight, new_vocab_size
        )


    def load_llama_hf_checkpoint(
        self,
        checkpoint_location: str,
        model_vocab_size: int | None = None,
        mp_world_size: int | None = None,
        mp_rank: int | None = None,
    ) -> StateDict:
        config, state_dict = self._load_hf_checkpoint(checkpoint_location)

        nlayers = config["num_hidden_layers"]
        nheads = config["num_attention_heads"]
        n_kv_heads = config["num_key_value_heads"]
        hidden_size = config["hidden_size"]
        headdim = hidden_size // nheads

        result_sd = {}
        mp_world_size, mp_rank = self._validate_mp_params(mp_world_size, mp_rank)

        self._resize_vocab_weight(
            state_dict, model_vocab_size, "model.embed_tokens.weight", "lm_head.weight"
        )

        # Map over top-level parameter names
        for src, dst, split_dim in _LLAMA_REMAPS:
            result = state_dict[src]
            result_sd[dst] = split_weight_for_model_parallel(
                result,
                split_dim,
                mp_world_size,
                mp_rank,  # type: ignore
            )
            del state_dict[src]

        # Map over per-layer parmeter names
        for layernum in range(nlayers):
            for src_fmt, dst_fmt, split_dim in _LLAMA_LAYER_REMAPS:
                src = src_fmt.format(layernum)
                dst = dst_fmt.format(layernum)
                result = state_dict[src]
                if src.endswith("q_proj.weight"):
                    result = unpermute_qk_weights_from_hf_to_llama(
                        result, nheads, headdim, hidden_size
                    )
                elif src.endswith("k_proj.weight"):
                    result = unpermute_qk_weights_from_hf_to_llama(
                        result, n_kv_heads, headdim, hidden_size
                    )
                result_sd[dst] = split_weight_for_model_parallel(
                    result,
                    split_dim,
                    mp_world_size,
                    mp_rank,  # type: ignore
                )
                del state_dict[src]
        return result_sd

    def load_starcoder_hf_checkpoint(
        self,
        checkpoint_location: str,
        model_vocab_size: int | None = None,
        pos_embeddings_len: int | None = None,
        mp_world_size: int | None = None,
        mp_rank: int | None = None,
    ) -> StateDict:
        config, state_dict = self._load_hf_checkpoint(checkpoint_location)

        nlayers = config["n_layer"]
        nheads = config["n_head"]
        hidden_dim = config["n_embd"]
        headdim = hidden_dim // nheads

        result_sd = {}
        mp_world_size, mp_rank = self._validate_mp_params(mp_world_size, mp_rank)

        self._resize_vocab_weight(
            state_dict, model_vocab_size, "transformer.wte.weight", "lm_head.weight"
        )
        if pos_embeddings_len is not None:
            # Slice off the positional embeddings that we need
            pos_embeddings_weight = state_dict["transformer.wpe.weight"]
            state_dict["transformer.wpe.weight"] = (
                pos_embeddings_weight[:pos_embeddings_len, :].clone().detach()
            )

        for src, dst, split_dim in _STARCODER_REMAPS:
            result = state_dict[src]
            result_sd[dst] = split_weight_for_model_parallel(
                result,
                split_dim,
                mp_world_size,
                mp_rank,
            )
            del state_dict[src]

        for layernum in range(nlayers):
            for src_fmt, dst_fmt, split_dim in _STARCODER_LAYER_REMAPS:
                src = src_fmt.format(layernum)
                dst = dst_fmt.format(layernum)
                result = state_dict[src]
                result_sd[dst] = split_weight_for_model_parallel(
                    result,
                    split_dim,
                    mp_world_size,
                    mp_rank,
                )
                del state_dict[src]
            # Now we handle splitting the QKV matrix by hand
            # This is pretty gross.
            full_weight = state_dict[f"transformer.h.{layernum}.attn.c_attn.weight"]
            full_bias = state_dict[f"transformer.h.{layernum}.attn.c_attn.bias"]
            QKV = [  # Q_{weight, bias}, K_{weight, bias}, V_{weight_bias}
                full_weight[:hidden_dim, :].clone().detach(),
                full_bias[:hidden_dim].clone().detach(),
                full_weight[hidden_dim : hidden_dim + headdim, :].clone().detach(),
                full_bias[hidden_dim : hidden_dim + headdim].clone().detach(),
                full_weight[hidden_dim + headdim : hidden_dim + 2 * headdim, :]
                .clone()
                .detach(),
                full_bias[hidden_dim + headdim : hidden_dim + 2 * headdim]
                .clone()
                .detach(),
            ]
            # NOTE: only Q_{weight, bias} are MP-split, since starcoder has MQA, so K/V
            # aren't split.
            split_QKV = [
                split_weight_for_model_parallel(
                    x,
                    0,
                    mp_world_size,
                    mp_rank,
                )
                for x in QKV[:2]
            ] + QKV[2:]
            idx = 0
            for name in ["q", "k", "v"]:
                for kind in ["weight", "bias"]:
                    key = "layers.{}.attention.w{}.{}".format(layernum, name, kind)
                    result_sd[key] = split_QKV[idx]
                    idx += 1

        return result_sd


    def load_huggingface_checkpoint(
        self, checkpoint_dir: Path
    ) -> tuple[dict, StateDict]:
        config_file = checkpoint_dir / "config.json"
        with config_file.open(mode="r") as file_:
            config = json.load(file_)
        model_pattern = checkpoint_dir / "pytorch_model*.bin"
        files = glob.glob(model_pattern.as_posix())
        assert len(files) > 0
        state_dict = torch.load(files[0])
        for f in files[1:]:
            state_dict.update(torch.load(f))
        return config, state_dict

    def _make_save_context(
        self,
        checkpoint_location: str,
        iteration: int,
        shard: bool = True,
        local_dir_must_exist: bool = False,
    ):
        if checkpoint_location == DETERMINED_LOCATION:
            assert self.context is not None
            metadata = {"steps_completed": iteration}
            save_context = self.context.store_path(metadata, shard=shard)
        else:
            checkpoint_dir = Path(checkpoint_location)
            if local_dir_must_exist:
                assert checkpoint_dir.exists()
            else:
                checkpoint_dir.mkdir(exist_ok=True, parents=True)
            save_context = contextlib.nullcontext((checkpoint_dir, None))
        return save_context

    def save_state_dict_checkpoint(
        self,
        checkpoint_dir: Path,
        model: torch.nn.Module,
        model_args: ModelArgs,
        run_config: dict,
        iter_num: int,
        val_losses: Mapping[str, float],
        tokenizer_model_path: str = "",
    ):
        # Only data-parallel rank 0 saves the state dict
        if mpu.get_data_parallel_rank() == 0:
            torch.save(
                model.state_dict(),
                checkpoint_dir
                / f"consolidated.{mpu.get_model_parallel_rank():02d}.pth",
            )
            # Only master process saves metadata
            if mpu.get_model_parallel_rank() == 0:
                with (checkpoint_dir / "params.json").open("w") as f:
                    json.dump(
                        dataclasses.asdict(model_args),
                        f,
                        indent=2,
                    )
                if tokenizer_model_path and Path(tokenizer_model_path).exists():
                    shutil.copy(
                        tokenizer_model_path,
                        (checkpoint_dir / "tokenizer.model").as_posix(),
                    )
                with (checkpoint_dir / "last_saved_info.json").open("w") as f:
                    json.dump(
                        {
                            "val_losses": val_losses,
                            "iter_num": iter_num,
                            "model_args": dataclasses.asdict(model_args),
                            "config": run_config,
                        },
                        f,
                        indent=2,
                    )

    def save_parallel_optimizer_state(
        self,
        checkpoint_dir: Path,
        optimizer: MixedPrecisionAdamW,
    ):
        rank = torch.distributed.get_rank()
        torch.save(
            optimizer.parallel_state_dict(),
            (checkpoint_dir / f"optimizer.{rank:02d}.pth").as_posix(),
        )

    def checkpoint(
        self,
        full_config: dict[str, Any],
        model_args: ModelArgs,
        model: torch.nn.Module,
        checkpoint_location: str,
        tokenizer_model_path: str,
        optimizer: MixedPrecisionAdamW,
        iter_num: int,
        eval_loss_by_name: Mapping[str, float],
        save_optimizer: bool = True,
    ):

```
