Simplify load_llama_hf_checkpoint and load_starcoder_hf_checkpoint these methods by identifying common code, and refactor that code out to separate methods.


```python
class CheckpointManager:
    """A checkpoint manager."""

    # ...

    def load_llama_hf_checkpoint(
        self,
        checkpoint_location: str,
        model_vocab_size: int | None = None,
        mp_world_size: int | None = None,
        mp_rank: int | None = None,
    ) -> StateDict:
        config, state_dict = self._load_hf_checkpoint(checkpoint_location)

        nlayers = config["num_hidden_layers"]
        nheads = config["num_attention_heads"]
        n_kv_heads = config["num_key_value_heads"]
        hidden_size = config["hidden_size"]
        headdim = hidden_size // nheads

        result_sd = {}
        mp_world_size, mp_rank = self._validate_mp_params(mp_world_size, mp_rank)

        self._resize_vocab_weight(
            state_dict, model_vocab_size, "model.embed_tokens.weight", "lm_head.weight"
        )

        # Map over top-level parameter names
        for src, dst, split_dim in _LLAMA_REMAPS:
            result = state_dict[src]
            result_sd[dst] = split_weight_for_model_parallel(
                result,
                split_dim,
                mp_world_size,
                mp_rank,  # type: ignore
            )
            del state_dict[src]

        # Map over per-layer parmeter names
        for layernum in range(nlayers):
            for src_fmt, dst_fmt, split_dim in _LLAMA_LAYER_REMAPS:
                src = src_fmt.format(layernum)
                dst = dst_fmt.format(layernum)
                result = state_dict[src]
                if src.endswith("q_proj.weight"):
                    result = unpermute_qk_weights_from_hf_to_llama(
                        result, nheads, headdim, hidden_size
                    )
                elif src.endswith("k_proj.weight"):
                    result = unpermute_qk_weights_from_hf_to_llama(
                        result, n_kv_heads, headdim, hidden_size
                    )
                result_sd[dst] = split_weight_for_model_parallel(
                    result,
                    split_dim,
                    mp_world_size,
                    mp_rank,  # type: ignore
                )
                del state_dict[src]
        return result_sd

    def load_starcoder_hf_checkpoint(
        self,
        checkpoint_location: str,
        model_vocab_size: int | None = None,
        pos_embeddings_len: int | None = None,
        mp_world_size: int | None = None,
        mp_rank: int | None = None,
    ) -> StateDict:
        config, state_dict = self._load_hf_checkpoint(checkpoint_location)

        nlayers = config["n_layer"]
        nheads = config["n_head"]
        hidden_dim = config["n_embd"]
        headdim = hidden_dim // nheads

        result_sd = {}
        mp_world_size, mp_rank = self._validate_mp_params(mp_world_size, mp_rank)

        self._resize_vocab_weight(
            state_dict, model_vocab_size, "transformer.wte.weight", "lm_head.weight"
        )
        if pos_embeddings_len is not None:
            # Slice off the positional embeddings that we need
            pos_embeddings_weight = state_dict["transformer.wpe.weight"]
            state_dict["transformer.wpe.weight"] = (
                pos_embeddings_weight[:pos_embeddings_len, :].clone().detach()
            )

        for src, dst, split_dim in _STARCODER_REMAPS:
            result = state_dict[src]
            result_sd[dst] = split_weight_for_model_parallel(
                result,
                split_dim,
                mp_world_size,
                mp_rank,
            )
            del state_dict[src]

        for layernum in range(nlayers):
            for src_fmt, dst_fmt, split_dim in _STARCODER_LAYER_REMAPS:
                src = src_fmt.format(layernum)
                dst = dst_fmt.format(layernum)
                result = state_dict[src]
                result_sd[dst] = split_weight_for_model_parallel(
                    result,
                    split_dim,
                    mp_world_size,
                    mp_rank,
                )
                del state_dict[src]
            # Now we handle splitting the QKV matrix by hand
            # This is pretty gross.
            full_weight = state_dict[f"transformer.h.{layernum}.attn.c_attn.weight"]
            full_bias = state_dict[f"transformer.h.{layernum}.attn.c_attn.bias"]
            QKV = [  # Q_{weight, bias}, K_{weight, bias}, V_{weight_bias}
                full_weight[:hidden_dim, :].clone().detach(),
                full_bias[:hidden_dim].clone().detach(),
                full_weight[hidden_dim : hidden_dim + headdim, :].clone().detach(),
                full_bias[hidden_dim : hidden_dim + headdim].clone().detach(),
                full_weight[hidden_dim + headdim : hidden_dim + 2 * headdim, :]
                .clone()
                .detach(),
                full_bias[hidden_dim + headdim : hidden_dim + 2 * headdim]
                .clone()
                .detach(),
            ]
            # NOTE: only Q_{weight, bias} are MP-split, since starcoder has MQA, so K/V
            # aren't split.
            split_QKV = [
                split_weight_for_model_parallel(
                    x,
                    0,
                    mp_world_size,
                    mp_rank,
                )
                for x in QKV[:2]
            ] + QKV[2:]
            idx = 0
            for name in ["q", "k", "v"]:
                for kind in ["weight", "bias"]:
                    key = "layers.{}.attention.w{}.{}".format(layernum, name, kind)
                    result_sd[key] = split_QKV[idx]
                    idx += 1

        return result_sd
