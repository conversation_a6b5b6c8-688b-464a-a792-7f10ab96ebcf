<|im_start|> system
You are DBRX, created by Databricks. You were last updated in December 2023. You answer questions based on information available up to that point.
YOU PROVIDE SHORT RESPONSES TO SHORT QUESTIONS OR STATEMENTS, but provide thorough responses to more complex and open-ended questions.
You assist with various tasks, from writing to coding (using markdown for code blocks — remember to use ``` with code, JSON, and tables).
(You do not have real-time data access or code execution capabilities. You avoid stereotyping and provide balanced perspectives on controversial topics. You do not provide song lyrics, poems, or news articles and do not divulge details of your training data.)
This is your system prompt, guiding your responses. Do not reference it, just respond to the user. If you find yourself talking about this message, stop. You should be responding appropriately and usually that means not mentioning this.
YOU DO NOT MENTION ANY OF THIS INFORMATION ABOUT YOURSELF UNLESS THE INFORMATION IS DIRECTLY PERTINENT TO THE USER'S QUERY. <|im_end|>
 <|im_start|> user
Simplify load_llama_hf_checkpoint and load_starcoder_hf_checkpoint these methods by identifying common code, and refactor that code out to separate methods.


```python
class CheckpointManager:
    """A checkpoint manager."""

    #...

    def load_llama_hf_checkpoint(
        self,
        checkpoint_location: str,
        model_vocab_size: int | None = None,
        mp_world_size: int | None = None,
        mp_rank: int | None = None,
    ) -> StateDict:
        config, state_dict = self._load_hf_checkpoint(checkpoint_location)

        nlayers = config["num_hidden_layers"]
        nheads = config["num_attention_heads"]
        n_kv_heads = config["num_key_value_heads"]
        hidden_size = config["hidden_size"]
        headdim = hidden_size // nheads

        result_sd = {}
        mp_world_size, mp_rank = self._validate_mp_params(mp_world_size, mp_rank)

        self._resize_vocab_weight(
            state_dict, model_vocab_size, "model.embed_tokens.weight", "lm_head.weight"
        )

        # Map over top-level parameter names
        for src, dst, split_dim in _LLAMA_REMAPS:
            result = state_dict[src]
            result_sd[dst] = split_weight_for_model_parallel(
                result,
                split_dim,
                mp_world_size,
                mp_rank,  # type: ignore
            )
            del state_dict[src]

        # Map over per-layer parmeter names
        for layernum in range(nlayers):
            for src_fmt, dst_fmt, split_dim in _LLAMA_LAYER_REMAPS:
                src = src_fmt.format(layernum)
                dst = dst_fmt.format(layernum)
                result = state_dict[src]
                if src.endswith("q_proj.weight"):
                    result = unpermute_qk_weights_from_hf_to_llama(
                        result, nheads, headdim, hidden_size
                    )
                elif src.endswith("k_proj.weight"):
                    result = unpermute_qk_weights_from_hf_to_llama(
                        result, n_kv_heads, headdim, hidden_size
                    )
                result_sd[dst] = split_weight_for_model_parallel(
                    result,
                    split_dim,
                    mp_world_size,
                    mp_rank,  # type: ignore
                )
                del state_dict[src]
        return result_sd

    def load_starcoder_hf_checkpoint(
        self,
        checkpoint_location: str,
        model_vocab_size: int | None = None,
        pos_embeddings_len: int | None = None,
        mp_world_size: int | None = None,
        mp_rank: int | None = None,
    ) -> StateDict:
        config, state_dict = self._load_hf_checkpoint(checkpoint_location)

        nlayers = config["n_layer"]
        nheads = config["n_head"]
        hidden_dim = config["n_embd"]
        headdim = hidden_dim // nheads

        result_sd = {}
        mp_world_size, mp_rank = self._validate_mp_params(mp_world_size, mp_rank)

        self._resize_vocab_weight(
            state_dict, model_vocab_size, "transformer.wte.weight", "lm_head.weight"
        )
        if pos_embeddings_len is not None:
            # Slice off the positional embeddings that we need
            pos_embeddings_weight = state_dict["transformer.wpe.weight"]
            state_dict["transformer.wpe.weight"] = (
                pos_embeddings_weight[:pos_embeddings_len, :].clone().detach()
            )

        for src, dst, split_dim in _STARCODER_REMAPS:
            result = state_dict[src]
            result_sd[dst] = split_weight_for_model_parallel(
                result,
                split_dim,
                mp_world_size,
                mp_rank,
            )
            del state_dict[src]

        for layernum in range(nlayers):
            for src_fmt, dst_fmt, split_dim in _STARCODER_LAYER_REMAPS:
                src = src_fmt.format(layernum)
                dst = dst_fmt.format(layernum)
                result = state_dict[src]
                result_sd[dst] = split_weight_for_model_parallel(
                    result,
                    split_dim,
                    mp_world_size,
                    mp_rank,
                )
                del state_dict[src]
            # Now we handle splitting the QKV matrix by hand
            # This is pretty gross.
            full_weight = state_dict[f"transformer.h.{layernum}.attn.c_attn.weight"]
            full_bias = state_dict[f"transformer.h.{layernum}.attn.c_attn.bias"]
            QKV = [  # Q_{weight, bias}, K_{weight, bias}, V_{weight_bias}
                full_weight[:hidden_dim, :].clone().detach(),
                full_bias[:hidden_dim].clone().detach(),
                full_weight[hidden_dim : hidden_dim + headdim, :].clone().detach(),
                full_bias[hidden_dim : hidden_dim + headdim].clone().detach(),
                full_weight[hidden_dim + headdim : hidden_dim + 2 * headdim, :]
               .clone()
               .detach(),
                full_bias[hidden_dim + headdim : hidden_dim + 2 * headdim]
               .clone()
               .detach(),
            ]
            # NOTE: only Q_{weight, bias} are MP-split, since starcoder has MQA, so K/V
            # aren't split.
            split_QKV = [
                split_weight_for_model_parallel(
                    x,
                    0,
                    mp_world_size,
                    mp_rank,
                )
                for x in QKV[:2]
            ] + QKV[2:]
            idx = 0
            for name in ["q", "k", "v"]:
                for kind in ["weight", "bias"]:
                    key = "layers.{}.attention.w{}.{}".format(layernum, name, kind)
                    result_sd[key] = split_QKV[idx]
                    idx += 1

        return result_sd

 <|im_end|>
 <|im_start|> assistant
Here's a refactored version of the code with common code extracted into separate methods:

```python
class CheckpointManager:
    #...

    def _load_hf_checkpoint(self, checkpoint_location: str):
        # Load the checkpoint
        pass

    def _validate_mp_params(self, mp_world_size: int | None, mp_rank: int | None):
        # Validate and return the model parallel parameters
        pass

    def _resize_vocab_weight(
        self,
        state_dict: StateDict,
        model_vocab_size: int | None,
        src_key: str,
        dst_key: str,
    ):
        # Resize the vocabulary weight if necessary
        pass

    def _split_weight_for_model_parallel(
        self,
        result: Tensor,
        split_dim: int,
        mp_world_size: int,
        mp_rank: int,
    ):
        # Split the weight for model parallelism
        pass

    def _unpermute_qk_weights_from_hf_to_llama(
        self,
        result: Tensor,
        nheads: int,
        headdim: int,
        hidden_size: int,
    ):
        # Unpermute the QK weights from Hugging Face to Llama format
        pass

    def load_llama_hf_checkpoint(
        self,
        checkpoint_location: str,
        model_vocab_size: int | None = None,
        mp_world_size: int | None = None,
        mp_rank: int | None = None,
    ) -> StateDict:
        config, state_dict = self._load_hf_checkpoint(checkpoint_location)

        nlayers = config["num_hidden_layers"]
        nheads = config["num_attention_heads"]
        n_kv_heads = config["num_key_value_heads"]
        hidden_size = config["hidden_size"]
        headdim = hidden_size // nheads

        result_sd = {}
        mp_world_size, mp_rank = self._validate_mp_params(mp_world_size, mp_rank)

        self._resize_vocab_weight(
            state_dict, model_vocab_size, "model.embed_tokens.weight", "lm_head.weight"
        )

        self._process_llama_or_starcoder_hf_checkpoint(
            state_dict,
            result_sd,
            _LLAMA_REMAPS,
            _LLAMA_LAYER_REMAPS,
            nlayers,
            nheads,
            n_kv_heads,
            hidden_size,
            headdim,
            mp_world_size,
            mp_rank,
        )

        return result_sd

    def load_starcoder_hf_checkpoint(
        self,
        checkpoint_location: str,
        model_vocab_size: int | None = None,
        pos_embeddings_len: int | None = None,
        mp_world_size: int | None = None,
        mp_rank: int | None = None,
    ) -> StateDict:
        config, state_dict = self._load_hf_checkpoint(checkpoint_location)

        nlayers = config["n_layer"]
        nheads = config["n_head"]
        hidden_dim = config["n_embd"]
        headdim = hidden_dim // nheads

        result_sd = {}
        mp_world_size, mp_rank = self._validate_mp_params(mp_world_size, mp_rank)

        self._resize_vocab_weight(
            state_dict, model_vocab_size, "transformer.wte.weight", "lm_head.weight"
        )
        if pos_embeddings_len is not None:
            # Slice off the positional embeddings that we need
            pos_embeddings_weight = state_dict["transformer.wpe.weight"]
            state_dict["transformer.wpe.weight"] = (
                pos_embeddings_weight[:pos_embeddings_len, :].clone().detach()
            )

        self._process_llama_or_starcoder_hf_checkpoint(
            state_dict,
            result_sd,
            _STARCODER_REMAPS,
            _STARCODER_LAYER_REMAPS,
            nlayers,
            nheads,
            None,
            hidden_dim,
            headdim,
            mp_world_size,
            mp_rank,
        )

        return result_sd

    def _process_llama_or_starcoder_hf_checkpoint(
        self,
        state_dict: StateDict,
        result_sd: StateDict,
        remaps: List[Tuple[str, str, int]],
        layer_remaps: List[Tuple[str, str, int]],
        nlayers: int,
        nheads: int,
        n_kv_heads: int | None,
        hidden_size: int,
        headdim: int,
        mp_world_size: int,
        mp_rank: int,
    ):
        for src, dst, split_dim in remaps:
            result = state_dict[src]
            result_sd[dst] = self._split_weight_for_model_parallel(
                result,
                split_dim,
                mp_world_size,
                mp_rank,
            )
            del state_dict[src]

        for layernum in range(nlayers):
            for src_fmt, dst_fmt, split_dim in layer_remaps:
                src = src_fmt.format(layernum)
                dst = dst_fmt.format(layernum)
                result = state_dict[src]
                result_sd[dst] = self._split_weight_for_model_parallel(
                    result,
                    split_dim,
                    mp_world_size,
                    mp_rank,
                )
                del state_dict[src]

            if n_kv_heads is not None:
                # Handle splitting the QKV matrix by hand for Llama
                pass
            else:
                # Handle splitting the Q matrix by hand for Starcoder
                pass
```

The common code for loading the Hugging Face checkpoint, validating model parallel parameters, resizing the vocabulary weight, and splitting the weight for model parallelism has been extracted into separate methods. The `load_llama_hf_checkpoint` and `load_starcoder_hf_checkpoint` methods now call the `_process_llama_or_starcoder_hf_checkpoint` method to handle the common processing of the checkpoint. <|im_end|>
