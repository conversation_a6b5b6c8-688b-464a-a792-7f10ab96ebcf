from transformers import AutoTokenizer, AutoModelForCausalLM
import torch
from pathlib import Path
import argparse


parser = argparse.ArgumentParser()
parser.add_argument("--prompt", type=str, required=True)
parser.add_argument("--output", type=str, required=True)
args = parser.parse_args();

checkpoint_path = "/mnt/efs/augment/checkpoints/databricks/dbrx-instruct"
input_text = Path(args.prompt).read_text()

tokenizer = AutoTokenizer.from_pretrained(checkpoint_path, trust_remote_code=True, token="hf_YOUR_TOKEN")
# model = AutoModelForCausalLM.from_pretrained(checkpoint_path, device_map="auto", torch_dtype=torch.bfloat16, trust_remote_code=True, token="hf_YOUR_TOKEN")
model = AutoModelForCausalLM.from_pretrained(checkpoint_path, device_map="auto", torch_dtype=torch.bfloat16, trust_remote_code=True)

#input_text = "What does it take to build a great LLM?"
messages = [{"role": "user", "content": input_text}]
input_ids = tokenizer.apply_chat_template(messages, return_dict=True, tokenize=True, add_generation_prompt=True, return_tensors="pt").to("cuda")

outputs = model.generate(**input_ids, max_new_tokens=10000)
output_text = tokenizer.decode(outputs[0])
print(output_text)
Path(args.output).write_text(output_text)
