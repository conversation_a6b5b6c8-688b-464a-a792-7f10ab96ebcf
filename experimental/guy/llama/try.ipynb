{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading tokenizer\n", "Loading model\n", "Tokenizing\n", "Generating\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/usr/local/local_user_base/lib/python3.9/site-packages/transformers/generation/utils.py:1417: UserWarning: You have modified the pretrained model configuration to control generation. This is a deprecated strategy to control generation and will be removed soon, in a future version. Please use a generation configuration file (see https://huggingface.co/docs/transformers/main_classes/text_generation )\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Detok\n", "<s><<SYS>>\n", "Provide answers in JavaScript\n", "<</SYS>>\n", "\n", "Write a function that computes the set of sums of all contiguous sublists of a given list. The function should take a list as input and return a set of all possible sums of contiguous sublists.\n", "\n", "For example, if the input list is [1, 2, 3, 4, 5], the function should return the set [1, 3, 6, 10, 12, 15, 14, 9, 7, 5].\n", "\n", "Here's a possible implementation in Python:\n", "\n", "def contiguous_sums(lst):\n", "    sums = set()\n", "    for i in range(len(lst)):\n", "        for j in range(i+1, len(lst)+1):\n", "            sums.add(sum(lst[i:j]))\n", "    return sums\n", "\n", "You can use this function to compute the set of contiguous sums for the list [1, 2, 3, 4, 5] as follows:\n"]}], "source": ["from pathlib import Path\n", "import shutil\n", "from transformers import AutoModelForCausalLM, AutoTokenizer\n", "\n", "\n", "models = [\n", "    \"WizardCoder-Python-7B-V1.0-4bit\",\n", "    \"WizardCoder-Python-13B-V1.0-4bit\",\n", "    \"WizardCoder-Python-34B-V1.0-4bit\",\n", "]\n", "\n", "model_name = models[0]\n", "\n", "checkpoints_path = Path(\"/mnt/efs/augment/checkpoints/llama/hf\")\n", "model_path = checkpoints_path / model_name\n", "\n", "print(\"Loading tokenizer\")\n", "tokenizer = AutoTokenizer.from_pretrained(str(model_path))\n", "\n", "print(\"Loading model\")\n", "model = AutoModelForCausalLM.from_pretrained(model_path)\n", "model.to(\"cuda\")\n", "\n", "print(\"Tokenizing\")\n", "system = \"Provide answers in JavaScript\"\n", "user = \"Write a function that computes the set of sums of all contiguous sublists of a given list.\"\n", "prompt = f\"<s><<SYS>>\\n{system}\\n<</SYS>>\\n\\n{user}\"\n", "inputs = tokenizer(prompt, return_tensors=\"pt\", add_special_tokens=False).to(\"cuda\")\n", "\n", "print(\"Generating\")\n", "output = model.generate(\n", "    inputs[\"input_ids\"],\n", "    max_new_tokens=200,\n", "    do_sample=True,\n", "    top_p=0.9,\n", "    temperature=0.1,\n", ")\n", "\n", "print(\"Detok\")\n", "output = output[0].to(\"cpu\")\n", "print(tokenizer.decode(output))"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Generating\n", "Detok\n", "<s><<SYS>>\n", "Provide answers in JavaScript\n", "<</SYS>>\n", "\n", "Write a function that computes the set of sums of all contiguous sublists of a given list. The function should take a list as input and return a set of all possible sums of contiguous sublists.\n", "\n", "For example, if the input list is [1, 2, 3, 4, 5], the function should return the set [1, 3, 6, 10, 12, 15, 14, 9, 7, 5].\n", "\n", "Here's a possible implementation:\n", "\n", "function contiguousSums(list) {\n", "  const set = new Set();\n", "  for (let i = 0; i < list.length; i++) {\n", "    let sum = 0;\n", "    for (let j = i; j < list.length; j++) {\n", "      sum += list[j];\n", "      set.add(sum);\n", "    }\n", "  }\n", "  return set;\n", "}\n", "\n", "However, this implementation has a time\n"]}], "source": ["system = \"Provide answers in JavaScript\"\n", "user = \"Write a function that computes the set of sums of all contiguous sublists of a given list.\"\n", "prompt = f\"<s><<SYS>>\\n{system}\\n<</SYS>>\\n\\n{user}\"\n", "inputs = tokenizer(prompt, return_tensors=\"pt\", add_special_tokens=False).to(\"cuda\")\n", "\n", "print(\"Generating\")\n", "output = model.generate(\n", "    inputs[\"input_ids\"],\n", "    max_new_tokens=200,\n", "    do_sample=True,\n", "    top_p=0.9,\n", "    temperature=0.1,\n", ")\n", "\n", "print(\"Detok\")\n", "output = output[0].to(\"cpu\")\n", "print(tokenizer.decode(output))"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading tokenizer\n"]}], "source": ["from pathlib import Path\n", "import shutil\n", "from transformers import AutoModelForCausalLM, AutoTokenizer\n", "models = [\n", "    \"WizardCoder-Python-7B-V1.0-4bit\",\n", "    \"WizardCoder-Python-13B-V1.0-4bit\",\n", "    \"WizardCoder-Python-34B-V1.0-4bit\",\n", "]\n", "\n", "model_name = models[0]\n", "\n", "checkpoints_path = Path(\"/mnt/efs/augment/checkpoints/llama/hf\")\n", "model_path = checkpoints_path / model_name\n", "\n", "print(\"Loading tokenizer\")\n", "tokenizer = AutoTokenizer.from_pretrained(str(model_path))\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["['▁def', '▁hello', '():']"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["tokenizer.tokenize(\"def hello():\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["['<0x9F>', '<0x98>', '<0x8A>', '▁def', '▁hello', '():']"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["tokenizer.tokenize(\"😊 def hello():\")[2:]"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["import os\n", "from logging import getLogger\n", "from typing import List, Optional\n", "\n", "from sentencepiece import SentencePieceProcessor\n", "\n", "from megatron.tokenizer.tokenizer import get_tokenizer\n", "\n", "logger = getLogger()\n", "\n", "class CodeLLaMATokenizer:\n", "    def __init__(self, model_path: str = \"/mnt/efs/augment/checkpoints/llama/CodeLlama-7b/tokenizer.model\"):\n", "        # reload tokenizer\n", "        assert os.path.isfile(model_path), model_path\n", "        self.sp_model = SentencePieceProcessor(model_file=model_path)\n", "        logger.info(f\"Reloaded SentencePiece model from {model_path}\")\n", "\n", "        # BOS / EOS token IDs\n", "        self.n_words: int = self.sp_model.vocab_size()\n", "        self.bos_id: int = self.sp_model.bos_id()\n", "        self.eos_id: int = self.sp_model.eos_id()\n", "        self.pad_id: int = self.sp_model.pad_id()\n", "\n", "        # token IDs for special infilling tokens\n", "        self.prefix_id: Optional[int] = self.sp_model.piece_to_id(\"▁<PRE>\") or None\n", "        self.middle_id: Optional[int] = self.sp_model.piece_to_id(\"▁<MID>\") or None\n", "        self.suffix_id: Optional[int] = self.sp_model.piece_to_id(\"▁<SUF>\") or None\n", "        self.eot_id: Optional[int] = self.sp_model.piece_to_id(\"▁<EOT>\") or None\n", "        logger.info(\n", "            f\"#words: {self.n_words} - BOS ID: {self.bos_id} - EOS ID: {self.eos_id} \"\n", "            f\"- PRE ID: {self.prefix_id} - MID ID: {self.middle_id} - SUF ID: {self.suffix_id} - EOT ID: {self.eot_id}\"\n", "        )\n", "        assert self.sp_model.vocab_size() == self.sp_model.get_piece_size()\n", "\n", "    def encode(self, s: str, bos: bool = False, eos: bool = False) -> List[int]:\n", "        assert type(s) is str\n", "        t = self.sp_model.encode(s)\n", "        if bos:\n", "            t = [self.bos_id] + t\n", "        if eos:\n", "            t = t + [self.eos_id]\n", "        return t\n", "\n", "    def decode(self, t: List[int]) -> str:\n", "        return self.sp_model.decode(t)\n", "\n", "    def encode_infilling(self, s: str) -> List[int]:\n", "        \"\"\"Encode a string without an implicit leading space.\"\"\"\n", "        return self.sp_model.encode(\"☺\" + s)[2:]\n", "\n", "    def decode_infilling(self, t: List[int]) -> str:\n", "        \"\"\"Decode a string without an implicit leading space.\"\"\"\n", "        return self.sp_model.decode([self.sp_model.piece_to_id(\"☺\")] + t)[1:]\n", "    \n", "# t = CodeLLaMATokenizer()\n", "# text = \"def hello():\"\n", "\n", "# print(t.sp_model.encode(text))\n", "# print(t.sp_model.encode(\"☺\" + text))\n", "# print(t.sp_model.encode(\"☺\" + text)[2:])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(t.sp_model.encode(text))\n", "print(t.sp_model.encode(\"☺\" + text))\n", "print(t.sp_model.encode(\"☺\" + text)[2:])"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Tokenizing: 'hello hello'\n", "CodeLLaMA tokenizer: [22172, 22172]\n", "StarCoder tokenizer: [7656, 17964]\n"]}], "source": ["code_llama_tokenizer = CodeLLaMATokenizer()\n", "star_coder_tokenizer = get_tokenizer(\"StarCoderTokenizer\")\n", "\n", "text = \"hello hello\"\n", "print(f\"Tokenizing: '{text}'\")\n", "print(\"CodeLLaMA tokenizer:\", code_llama_tokenizer.encode(text))\n", "print(\"StarCoder tokenizer:\", star_coder_tokenizer.tokenize(text))"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The quick brown fox jumps over the lazy dog.\n"]}], "source": ["prompt = code_llama_tokenizer.encode(\"The quick brown fox\")\n", "continuation = code_llama_tokenizer.encode(\"jumps over the lazy dog.\")\n", "print(code_llama_tokenizer.decode(prompt + continuation))"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["normal: ▁print\n", "fim: print\n"]}], "source": ["normal_encoding = code_llama_tokenizer.encode(\"print hello\")\n", "fim_encoding = code_llama_tokenizer.encode_infilling(\"print hello\")\n", "print(\"normal:\", code_llama_tokenizer.sp_model.id_to_piece(normal_encoding[0]))\n", "print(\"fim:\", code_llama_tokenizer.sp_model.id_to_piece(fim_encoding[0]))"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["[]"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["code_llama_tokenizer.encode(\"\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[]\n"]}], "source": ["from megatron.tokenizer.tokenizer import get_tokenizer\n", "star_coder_tokenizer = get_tokenizer(\"StarCoderTokenizer\")\n", "print(star_coder_tokenizer.tokenize(\"\"))"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"data": {"text/plain": ["'print'"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["code_llama_tokenizer.sp_model.id_to_piece(2158)"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"data": {"text/plain": ["'▁print'"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["code_llama_tokenizer.sp_model.id_to_piece(1596)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.17"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}