"""Quantize LLaMA HuggingFace models."""

import shutil
import time
from pathlib import Path

from transformers import AutoModelForCausalLM, AutoTokenizer, GPTQConfig

models = [
    "WizardCoder-Python-7B-V1.0",
    "WizardCoder-Python-13B-V1.0",
    "WizardCoder-Python-34B-V1.0",
    "CodeLlama-7b-Instruct-hf",
    "CodeLlama-13b-Instruct-hf",
    "CodeLlama-34b-Instruct-hf",
    "CodeLlama-7b-hf",
    "CodeLlama-13b-hf",
    "CodeLlama-34b-hf",
    "CodeLlama-7b-Python-hf",
    "CodeLlama-13b-Python-hf",
    "CodeLlama-34b-Python-hf",
]
checkpoints_path = Path("/mnt/efs/augment/checkpoints/llama/hf")
bits = 4
dataset = [
    (
        "auto-gptq is an easy-to-use model quantization library "
        "with user-friendly apis, based on GPTQ algorithm."
    )
]

for model_name in models:
    start = time.time()
    model_path = checkpoints_path / model_name
    saved_model_path = checkpoints_path / f"{model_name}-{bits}bit"
    print(f"\nQuantizing {model_name} at {model_path} ...")

    if saved_model_path.exists():
        print(f"Path {saved_model_path} exists, skipping")
        continue

    tokenizer = AutoTokenizer.from_pretrained(str(model_path))
    gptq_config = GPTQConfig(
        bits=bits, dataset=dataset, tokenizer=tokenizer, disable_exllama=True
    )

    model = AutoModelForCausalLM.from_pretrained(
        model_path, device_map="auto", quantization_config=gptq_config
    )

    # Move entire model to GPU for exllama to work during inference
    model.to("cuda")
    print(f"Saving to {saved_model_path}")
    model.save_pretrained(str(saved_model_path))

    for tokenizer_file_path in model_path.glob("tokenizer*"):
        shutil.copy(tokenizer_file_path, saved_model_path / tokenizer_file_path.name)

    elapsed = time.time() - start
    print(f"Took {elapsed:.1f} seconds")
