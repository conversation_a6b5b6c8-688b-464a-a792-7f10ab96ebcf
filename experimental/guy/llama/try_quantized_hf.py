"""Try generating text from quantized HF models."""

from pathlib import Path

from transformers import AutoModelForCausalLM, AutoTokenizer

models = [
    "WizardCoder-Python-7B-V1.0-4bit",
    "WizardCoder-Python-13B-V1.0-4bit",
    "WizardCoder-Python-34B-V1.0-4bit",
]

model_name = models[0]

checkpoints_path = Path("/mnt/efs/augment/checkpoints/llama/hf")
model_path = checkpoints_path / model_name

print("Loading tokenizer")
tokenizer = AutoTokenizer.from_pretrained(str(model_path))

print("Loading model")
model = AutoModelForCausalLM.from_pretrained(model_path)
model.to("cuda")

print("Tokenizing")
system = "Provide answers in JavaScript"
user = "Write a function that computes the set of sums of all contiguous sublists of a given list."
prompt = f"<s><<SYS>>\n{system}\n<</SYS>>\n\n{user}"
inputs = tokenizer(prompt, return_tensors="pt", add_special_tokens=False).to("cuda")

print("Generating")
output = model.generate(
    inputs["input_ids"],
    max_new_tokens=200,
    do_sample=True,
    top_p=0.9,
    temperature=0.1,
)

print("Detok")
output = output[0].to("cpu")
print(tokenizer.decode(output))
