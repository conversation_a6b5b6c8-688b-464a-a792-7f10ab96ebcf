"""Augment terminal helper."""

import argparse
import json
import re
import signal
import subprocess
import sys

import requests
from colorama import Fore

# MULTI_TURN_PROMPT_TEMPLATE = """<s>[INST] <<SYS>>
# {{ system_prompt }}
# <</SYS>>

# {{ user_msg_1 }} [/INST] {{ model_answer_1 }} </s><s>[INST] {{ user_msg_2 }} [/INST]
# """

PROMPT_TEMPLATE = """<s>[INST] <<SYS>>
{{ system_prompt }}
<</SYS>>

{{ user_msg_1 }} [/INST]
"""

PROMPT_TURN_TEMPLATE = "{{ model_answer_1 }} </s><s>[INST] {{ user_msg_2 }} [/INST]\n"

SYSTEM_PROMPT = """Act as a helpful and accurate terminal assistant. You will be
shown a short instruction, potentially with relevant context. Turn that instruction
into a one-line shell command that executes that instruction. Only response
with the shell command. Do not output any explanations or markdown wrappers.
"""


def ctrl_c_handler(sig, frame):
    del sig
    del frame
    print("")
    sys.exit(1)


def generate_command(args, dialog: list[str]):
    if len(dialog) % 2 != 1:
        raise ValueError("Dialog length must be odd")

    instruction = dialog[0]
    prompt = PROMPT_TEMPLATE
    prompt = prompt.replace("{{ system_prompt }}", SYSTEM_PROMPT)
    prompt = prompt.replace("{{ user_msg_1 }}", instruction)

    for i in range(1, len(dialog), 2):
        model_answer = dialog[i]
        instruction = dialog[i + 1]
        turn_prompt = PROMPT_TURN_TEMPLATE
        turn_prompt = turn_prompt.replace("{{ model_answer_1 }}", model_answer)
        turn_prompt = turn_prompt.replace("{{ user_msg_2 }}", instruction)
        prompt += turn_prompt

    if args.verbose:
        print("prompt:")
        print(prompt)

    payload = {
        "prompt": prompt,
        "n_predict": args.n,
        "temperature": args.temperature,
        "stream": False,
    }
    response = requests.post(args.server, json=payload, timeout=20)

    if response.status_code != 200:
        print("error: request failed with status code", response.status_code)
        sys.exit(1)

    response_content = json.loads(response.content.decode("utf-8"))

    if args.verbose:
        print(json.dumps(response_content, indent=2))

    if not response_content["stopped_eos"]:
        print("error: response did not end with eos")
        sys.exit(1)

    cmd = response_content["content"]
    cmd = cmd.strip()

    if "```" in cmd:
        matches = re.findall(r"```[^\n]*\n(.*?)```", cmd, re.DOTALL)
        cmd = matches[0]
    else:
        cmd = cmd.split("\n")[0]
    cmd = cmd.strip()
    return cmd


def main():
    # catch ctrl-c
    signal.signal(signal.SIGINT, ctrl_c_handler)

    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--server",
        type=str,
        default="http://localhost:8080/completion",
        help="URL of the llama.cpp server",
    )
    parser.add_argument(
        "-n", type=int, default=256, help="max number of tokens to generate"
    )
    parser.add_argument(
        "--temperature",
        type=float,
        default=0.0,
        help="max number of tokens to generate",
    )
    parser.add_argument("-v", "--verbose", action="store_true", help="verbose output")
    args, rest = parser.parse_known_args()
    instruction = " ".join(rest)
    dialog = [instruction]
    is_first_turn = True

    while True:
        cmd = generate_command(args, dialog)
        if is_first_turn:
            print("Enter to execute, enter another instruction to modify")
            is_first_turn = False
        print("> " + Fore.BLUE + cmd + Fore.RESET)

        try:
            user_input = input()
        except EOFError:
            sys.exit(1)
        user_input = user_input.strip()

        if not user_input:
            result = subprocess.run(
                cmd,
                shell=True,
                stdin=sys.stdin,
                stdout=sys.stdout,
                stderr=sys.stderr,
                check=False,
            )
            sys.exit(result.returncode)
        else:
            dialog.append(cmd)
            dialog.append(user_input)


if __name__ == "__main__":
    main()
