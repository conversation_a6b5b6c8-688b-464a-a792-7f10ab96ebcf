{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "import os\n", "sys.path.append(os.environ[\"HOME\"])\n", "\n", "import collections\n", "import logging\n", "from termcolor import colored\n", "import time\n", "from textwrap import dedent\n", "import pyglove as pg\n", "\n", "from research.model_server.launch_model_server import get_docs_from_files\n", "from research.models import GenerationOptions\n", "from research.models.all_models import (\n", "    StarCoderBase1B,\n", "    StarCoderBase3B,\n", "    StarCoderBase7B,\n", "    StarCoderBase16B,\n", ")\n", "from research.models.fastforward_models import (  # pylint: disable=import-outside-toplevel\n", "    StarCoderBase1B_FastForward,\n", "    StarCoderBase3B_FastForward,\n", "    StarCoderBase7B_FastForward,\n", "    StarCoderBase16B_FastForward,\n", ")\n", "\n", "from research.retrieval.types import Document, Chunk\n", "from research.retrieval.retrieval_database import RetrievalDatabase\n", "from research.retrieval.chunking_functions import (\n", "    ScopeAwareChunker,\n", "    LineLevelChunker,\n", ")\n", "from research.retrieval.scorers.good_enough_bm25_scorer import (\n", "    GoodEnoughBM25Scorer,\n", ")\n", "from research.core.model_input import ModelInput\n", "from research.models import StopCriteria\n", "\n", "logging.disable(logging.CRITICAL)  # Disable a lot of annoying logs"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["start = time.time()\n", "# model = StarCoderBase1B()\n", "# model = StarCoderBase16B()\n", "# model = StarCoderBase16B_FastForward()\n", "# model = StarCoderBase1B_FastForward()\n", "model = StarCoderBase7B_FastForward()\n", "# model = LLAMA2Pretrain7B()\n", "model.load()\n", "elapsed = time.time() - start\n", "\n", "print(f\"Loaded model '{model.name}' in {elapsed:.1f} seconds\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from human_eval.data import write_jsonl, read_problems\n", "\n", "def generate_one_completion(prompt):\n", "    print(\"============================================================\")\n", "    print(\"Generating for prompt:\")\n", "    print(prompt)\n", "    options = GenerationOptions(temperature=0.0, max_generated_tokens=256)\n", "    generated = model.generate(ModelInput(prompt, suffix=\"\\n\"), options)\n", "    print(\"generated:\")\n", "    print(generated)\n", "    return generated\n", "\n", "problems = read_problems()\n", "\n", "num_samples_per_task = 1\n", "samples = [\n", "    dict(task_id=task_id, completion=generate_one_completion(problems[task_id][\"prompt\"]))\n", "    for i, task_id in enumerate(problems)\n", "    for _ in range(num_samples_per_task)\n", "    #if i < 2\n", "]\n", "write_jsonl(\"/tmp/samples_7b.jsonl\", samples)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["d = {'completion': 'from typing import List\\n'\n", "               '\\n'\n", "               '\\n'\n", "               'def has_close_elements(numbers: List[float], threshold: float) '\n", "               '-> bool:\\n'\n", "               '    \"\"\" Check if in given list of numbers, are any two numbers '\n", "               'closer to each other than\\n'\n", "               '    given threshold.\\n'\n", "               '    >>> has_close_elements([1.0, 2.0, 3.0], 0.5)\\n'\n", "               '    False\\n'\n", "               '    >>> has_close_elements([1.0, 2.8, 3.0, 4.0, 5.0, 2.0], '\n", "               '0.3)\\n'\n", "               '    True\\n'\n", "               '    \"\"\"\\n'\n", "               }\n", "print(model.generate(ModelInput(d[\"completion\"]), GenerationOptions(max_generated_tokens=64)))\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.17"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}