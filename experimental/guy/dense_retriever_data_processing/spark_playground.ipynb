{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import struct\n", "import pandas as pd\n", "from pyspark.sql import SparkSession\n", "from pyspark.sql.functions import pandas_udf, PandasUDFType, col, count\n", "from pyspark.sql.functions import max as spark_max, sum as spark_sum, length\n", "from pyspark.sql.functions import concat, sha2\n", "from pyspark.sql.types import StructType, StructField, IntegerType, StringType\n", "\n", "# import hf_codegen_tokenizer\n", "from megatron.tokenizer.tokenizer import CodeGenTokenizer\n", "\n", "\n", "def setup_spark(name=\"Spark Playground Notebook\"):\n", "    return (\n", "        SparkSession.builder.appName(name)\n", "        .master(\"spark://guy-dev-data-processing-lga1:7077\")\n", "        .config(\"spark.executor.memory\", \"10g\")\n", "        .config(\"spark.executor.memoryOverhead\", \"6g\")\n", "        .config(\"spark.executor.instances\", \"12\")\n", "        .config(\"spark.executor.cores\", \"5\")\n", "        .config(\"spark.driver.memory\", \"20g\")\n", "        .config(\"spark.driver.memoryOverhead\", \"6g\")\n", "        .config(\"spark.driver.maxResultSize\", \"2g\")\n", "        .config(\"spark.sql.debug.maxToStringFields\", \"4096\")\n", "        .config(\"spark.serializer\", \"org.apache.spark.serializer.KryoSerializer\")\n", "        .config(\"spark.sql.files.maxPartitionBytes\", \"128m\")\n", "        .getOrCreate()\n", "    )\n", "\n", "\n", "def setup_tokenizer():\n", "    \"\"\"Setup and sanity check the tokenizer.\"\"\"\n", "    # tokenizer = hf_codegen_tokenizer.HFCodeGenTokenizer()\n", "    tokenizer = CodeGenTokenizer()\n", "\n", "    if len(tokenizer.tokenizer.vocab) > 2**16 - 1:\n", "        raise ValueError(\n", "            f\"Vocabulary size is too large ({len(tokenizer.tokenizer.vocab)}). Tokens get packed into 16-bit integers.\"\n", "        )\n", "\n", "    return tokenizer\n", "\n", "\n", "tokenizer = setup_tokenizer()\n", "\n", "\n", "# def tokenize_and_pack(tokenizer, text):\n", "def tokenize_and_pack(text):\n", "    token_ids = tokenizer.tokenize(text)\n", "    # H = unsigned short\n", "    packed_data = struct.pack(f\"<{len(token_ids)}H\", *token_ids)\n", "    return bytearray(packed_data)\n", "\n", "\n", "# def unpack_and_detokenize(tokenizer, binary):\n", "def unpack_and_detokenize(binary):\n", "    token_ids = struct.unpack(f\"<{len(binary)//2}H\", binary)\n", "    #return tokenizer.detokenize(token_ids, clean_up_tokenization_spaces=False)\n", "    return tokenizer.detokenize(token_ids)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pyarrow.parquet as pq\n", "import pandas as pd\n", "\n", "from research.data.spark.pipelines.stages.common import pack_tokens, unpack_tokens\n", "\n", "path = \"part-00000-bc7e516d-6401-420c-8a7a-73e58a59eee4-c000.snappy.parquet\"\n", "df = pq.read_table(f\"/mnt/efs/augment-lga1-nvme/user/guy/data-pipeline/the-stack/packed_samples/{path}\")\n", "\n", "\n", "pdf = df.to_pandas()\n", "\n", "for row in pdf[\"packed_samples\"]:\n", "    tokens = unpack_tokens(row)\n", "    text = tokenizer.detokenize(tokens)\n", "    print(\"SAMPLE:\")\n", "    print(text)\n", "    # if \"<|endoftext|>\" in text:\n", "    #     print(\"yey!\")\n", "    #     break"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "\n", "from research.data.spark.pipelines.stages.common import unpack_tokens\n", "\n", "b = bytearray([1, 2, 3, 4])\n", "\n", "pdf = pd.DataFrame({\"tokenized_content\": [b, b, b]})\n", "pdf\n", "\n", "# for row in pdf[\"tokenized_content\"]:\n", "#     print(np.array(unpack_tokens(row)))\n", "\n", "result = pdf[\"tokenized_content\"].apply(lambda packed_tokens: np.array(unpack_tokens(packed_tokens)))\n", "for row in result:\n", "    print(row)\n", "\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# FIM"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from dataclasses import dataclass\n", "from typing import Sequence\n", "import numpy as np\n", "from megatron.tokenizer.tokenizer import AbstractTokenizer, CodeGenTokenizer\n", "\n", "\n", "@dataclass\n", "class FIMConfig:\n", "    \"\"\"Fill-In-the-Middle parameters.\"\"\"\n", "    # fim_rate: Probability of applying the transformation\n", "    fim_rate: float\n", "\n", "    # prefix_tok_id: Token placed in front of the prefix\n", "    prefix_tok_id: int\n", "\n", "    # suffix_tok_id: Token placed in front of the suffix\n", "    suffix_tok_id: int\n", "\n", "    # middle_tok_id: Token placed in front of the middle\n", "    middle_tok_id: int\n", "\n", "    # end_tok_id: Token placed at the end of the middle, to signify end of generation\n", "    end_tok_id: int\n", "\n", "    # pad_tok_id: Padding token, in case FIM made the sample shorter (this can happen)\n", "    pad_tok_id: int\n", "\n", "    # psm_mode: If True (default), use prefix+suffix+middle ordering. Otherwise,\n", "    #     use suffix+prefix+middle.\n", "    psm_mode: bool = True\n", "\n", "\n", "# From https://github.com/NVIDIA/Megatron-LM/commit/a360666ba791c7622c068c962235aa4eb2100ba2\n", "# Originally from https://github.com/EleutherAI/gpt-neox/blob/FIM-clean/megatron/data/gpt2_dataset.py#L339\n", "def fim_transform_segment(\n", "    segment: np.n<PERSON><PERSON>,\n", "    tokenizer: AbstractTokenizer,\n", "    np_rng: np.random.RandomState,\n", "    config: FIMConfig,\n", "    ) -> np.ndarray:\n", "    \"\"\"Apply a character-level FIM transformation with probability fim_rate.\n", "\n", "    Uses SPM ordering. Maintain the same sample length (if transform creates a\n", "    few extra tokens, drop them).\n", "\n", "    Args:\n", "        segment: A tokenized segment of text to be transformed\n", "        tokenizer: The tokenizer\n", "        np_rng: Random number generator\n", "        config: FIM configuration parameters\n", "\n", "    Returns:\n", "        Transformed sample.\n", "    \"\"\"\n", "    if not np_rng.binomial(1, config.fim_rate):\n", "        return segment\n", "\n", "    if segment.shape[0] == 0:\n", "        return segment\n", "\n", "    contents = tokenizer.detokenize(segment)\n", "\n", "    boundaries = list(np_rng.randint(low=1, high=len(contents) - 1, size=2))\n", "    boundaries.sort()\n", "\n", "    prefix = contents[:boundaries[0]]\n", "    middle = contents[boundaries[0]:boundaries[1]]\n", "    suffix = contents[boundaries[1]:]\n", "\n", "    prefix = np.array(tokenizer.tokenize(prefix))\n", "    suffix = np.array(tokenizer.tokenize(suffix))\n", "    middle = np.array(tokenizer.tokenize(middle))\n", "\n", "    # need to make same length as the input\n", "    new_length = prefix.shape[0] + suffix.shape[0] + middle.shape[0] + 4\n", "    diff = new_length - segment.shape[0]\n", "\n", "    if diff > 0:\n", "        # the transformation added tokens, so we drop some\n", "        # if suffix doesn't have enough tokens to drop, do not transform\n", "        if suffix.shape[0] > diff:\n", "            # suffix has enough tokens, drop them from the end\n", "            suffix = suffix[:suffix.shape[0] - diff]\n", "        elif prefix.shape[0] > diff:\n", "            # prefix has enough tokens, drop them from the beginning\n", "            prefix = prefix[diff:]\n", "        else:\n", "            # neither has enough tokens, do not transform\n", "            return segment\n", "    elif diff < 0:\n", "        # the transformation removed tokens, so add padding tokens\n", "        # TODO do we really want to do this? and do we want to add them to the end of the suffix?\n", "        suffix = np.concatenate([suffix, np.full((-1 * diff), config.pad_tok_id)])\n", "\n", "    prefix = np.concatenate([[config.prefix_tok_id], prefix])\n", "    suffix = np.concatenate([[config.suffix_tok_id], suffix])\n", "    middle = np.concatenate([[config.middle_tok_id], middle, [config.end_tok_id]])\n", "\n", "    if config.psm_mode:\n", "        new_segment = np.concatenate([prefix, suffix, middle])\n", "    else:\n", "        new_segment = np.concatenate([suffix, prefix, middle])\n", "\n", "    assert new_segment.shape[0] == segment.shape[0]\n", "    return new_segment\n", "\n", "\n", "def fim_transform_sample(sample: Sequence[int], tokenizer: AbstractTokenizer, np_rng: np.random.RandomState, config: FIMConfig):\n", "    # Code from: https://github.com/EleutherAI/gpt-neox/blob/FIM-clean/megatron/data/gpt2_dataset.py#L109\n", "    sample = np.array(sample, dtype=np.int64)\n", "\n", "    if config.fim_rate != 0:\n", "        if config.fim_rate > 1 or config.fim_rate < 0:\n", "            raise ValueError(f\"FIM rate must be a probability 0 <= rate <= 1, is {config.fim_rate}\")\n", "\n", "        eod = tokenizer.eod_id\n", "        segment_breaks = np.argwhere(sample == eod) # split sample by document\n", "        curr_start_position = 0\n", "\n", "        if segment_breaks.shape != (0, 1):\n", "            # there is an eod in the sample\n", "            for loc in np.nditer(segment_breaks):\n", "                if loc - curr_start_position > 10: # sometimes examples start with EOD or are too short. so avoid this case\n", "                    # include eod in the sample\n", "                    sample[curr_start_position:loc] = \\\n", "                        fim_transform_segment(sample[curr_start_position:loc], tokenizer, np_rng, config)\n", "\n", "                curr_start_position = loc + 1 # jump over the EOD token\n", "\n", "            # do not FIM transform the last segment. this is because the eod\n", "            # token for the last segment is always at the end of the sample,\n", "            # and we want to avoid biasing the model toward learning to output\n", "            # eod in that position.\n", "\n", "        # This code runs for the last segment of a sample (beyond the last eod),\n", "        # and for a sample that has no eod's in it\n", "        if curr_start_position < sample.shape[0]:\n", "            sample[curr_start_position:] = fim_transform_segment(\n", "                sample[curr_start_position:], tokenizer, np_rng, config)\n", "\n", "    return sample\n", "\n", "\n", "\n", "tokenizer = CodeGenTokenizer()\n", "\n", "config = FIMConfig(\n", "    fim_rate = 1,\n", "    prefix_tok_id = tokenizer.tokenize(\"<|fim-prefix|>\")[0],\n", "    suffix_tok_id = tokenizer.tokenize(\"<|fim-suffix|>\")[0],\n", "    middle_tok_id = tokenizer.tokenize(\"<|fim-middle|>\")[0],\n", "    end_tok_id = tokenizer.tokenize(\"<|fim-eos|>\")[0],\n", "    pad_tok_id = tokenizer.pad_id,\n", ")\n", "\n", "np_rng = np.random.RandomState(seed=42)\n", "\n", "text = \"The quick brown fox jumps over the lazy dog twice.<|endoftext|>The quick brown fox jumps over the lazy dog three times and then went home and ate a nice juicy apple.\"\n", "sample = np.array(tokenizer.tokenize(text))\n", "\n", "result = fim_transform_sample(sample, tokenizer, np_rng, config)\n", "detok = tokenizer.detokenize(result)\n", "print(detok)\n", "\n", "\n", "# result = apply_fim_transformation(\n", "#     sample,\n", "#     tokenizer,\n", "#     np_rng,\n", "#     config,\n", "# )\n", "# detok = tokenizer.detokenize(result)\n", "# print(detok)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Read some raw StarCoder data\n", "\n", "import pyarrow.parquet as pa\n", "import pandas as pd\n", "from pathlib import Path\n", "\n", "from megatron.tokenizer.tokenizer import StarCoderTokenizer\n", "\n", "tokenizer = StarCoderTokenizer()\n", "\n", "root = \"/mnt/efs/augment-lga1-nvme/data/raw/starcoder/raw/lang=python\"\n", "\n", "for k, path in enumerate(Path(root).rglob(\"*.parquet\")):\n", "    if k > 0:\n", "        break\n", "\n", "    table = pa.read_table(path)\n", "    pdf = table.to_pandas()\n", "\n", "    for i, row in enumerate(pdf[\"content\"]):\n", "        for line in row.splitlines():\n", "            if \"<filename>\" in line:\n", "                print(line)\n", "        if i > 1000:\n", "            break"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Read some StarCoder data\n", "\n", "import pyarrow.parquet as pa\n", "import pandas as pd\n", "from pathlib import Path\n", "from research.data.spark.pipelines.stages.common import pack_tokens, unpack_tokens\n", "\n", "from megatron.tokenizer.tokenizer import HFGPT2Tokenizer, StarCoderTokenizer\n", "\n", "import re\n", "\n", "# tokenizer = HFGPT2Tokenizer(\"/mnt/efs/augment/checkpoints/starcoderbase\")\n", "tokenizer = StarCoderTokenizer()\n", "\n", "def shorten_segment(segment):\n", "    if len(segment) > 30:\n", "        return segment[:10] + \" ... \" + segment[-10:]\n", "    else:\n", "        return segment\n", "\n", "root = \"/mnt/efs/augment-lga1-nvme/user/guy/data-pipeline/starcoder/packed_samples\"\n", "\n", "for k, path in enumerate(Path(root).rglob(\"*.parquet\")):\n", "    if k > 0:\n", "        break\n", "\n", "    table = pa.read_table(path)\n", "    pdf = table.to_pandas()\n", "\n", "    # for i, row in enumerate(pdf[\"packed_samples\"]):\n", "    for i, row in enumerate(pdf[\"content\"]):\n", "        if i > 4:\n", "            break\n", "        tokens = unpack_tokens(row)\n", "        assert len(tokens) == 4096\n", "\n", "        text = tokenizer.detokenize(tokens)\n", "\n", "        with Path(f\"/tmp/sample_{i}.txt\").open(\"w\", encoding=\"utf8\") as file:\n", "            file.write(text)\n", "\n", "        segments = text.split(\"<|endoftext|>\")\n", "\n", "        for j, segment in enumerate(segments):\n", "            fim_suffix = \"\"\n", "            if \"<fim_prefix>\" in segment:\n", "                m = re.match(r\"<fim_prefix>(.*)<fim_suffix>(.*)<fim_middle>(.*)\", segment, re.MULTILINE | re.DOTALL)\n", "                if m:\n", "                    prefix = m.group(1)\n", "                    suffix = m.group(2)\n", "                    middle = m.group(3)\n", "\n", "                    if j == len(segments) - 1:\n", "                        # The last segment is generally truncated\n", "                        middle += \"...\"\n", "                    else:\n", "                        # Segments before last end with <|endoftext|>, so keep\n", "                        # that for clarity\n", "                        suffix += \"<|endoftext|>\"\n", "\n", "                    decoded_segment = f\"<fim_prefix>{prefix}<fim_middle>{middle}<fim_suffix>{suffix}\"\n", "                    fim_suffix = \"_fim\"\n", "                else:\n", "                    print(\"no match:\")\n", "                    print(segment)\n", "                    raise ValueError(\"\")\n", "                    decoded_segment = segment\n", "            else:\n", "                decoded_segment = segment\n", "\n", "            with Path(f\"/tmp/sample_{i}_segment_{j}{fim_suffix}.txt\").open(\"w\", encoding=\"utf8\") as file:\n", "                file.write(decoded_segment)\n", "\n", "\n", "# root = \"/mnt/efs/augment-lga1-nvme/user/guy/data-pipeline/starcoder/packed_samples/\"\n", "# for path in Path(root).rglob(\"*.parquet\"):\n", "#     table = pa.read_table(str(path))\n", "#     pdf = table.to_pandas()\n", "#     for i, packed in enumerate(pdf[\"packed_samples\"]):\n", "#         tokens = unpack_tokens(packed)\n", "#         # print(tokens)\n", "#         text = tokenizer.detokenize(tokens)\n", "#         segments = text.split(\"<|endoftext|>\")\n", "#         short_segments = map(shorten_segment, segments)\n", "#         print(\"<|endoftext|>\".join(short_segments))\n", "\n", "#         if i > 3:\n", "#             break\n", "\n", "#         # print(tokenizer.detokenize(tokens))\n", "#     break\n", "\n", "\n", "# table = pa.read_table(\"/mnt/efs/augment-lga1-nvme/data/raw/starcoder/raw/lang=python/train-00001-of-00059.parquet\")\n", "# root = \"/mnt/efs/augment-lga1-nvme/data/raw/starcoder/raw\"\n", "# root = \"/mnt/efs/augment-lga1-nvme/data/raw/starcoder/raw/lang=jupyter-scripts-dedup-filtered\"\n", "# root = \"/mnt/efs/augment-lga1-nvme/data/raw/starcoder/raw/lang=git-commits-cleaned\"\n", "# for path in Path(root).rglob(\"*.parquet\"):\n", "#     print(\">>>\", path)\n", "#     table = pa.read_table(str(path))\n", "#     pdf = table.to_pandas()\n", "#     for content in pdf[\"content\"]:\n", "#         if \"<fim_\" in content:\n", "#             print(content)\n", "#             break\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["segments = text.split(\"<|endoftext|>\")\n", "print(f\"Found {len(segments)} segments\")\n", "for segment in segments:\n", "    if \"<fim\" in segment:\n", "        print(\"============= SEGMENT =========================================\")\n", "        print(segment)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Other code"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pyspark.sql.functions as F\n", "\n", "spark = setup_spark()\n", "df = spark.read.parquet(\"/mnt/efs/augment-lga1-nvme/data/raw/starcoder/raw\")\n", "\n", "df.printSchema()\n", "\n", "# df = df.filter(<PERSON>.col(\"lang\") == \"tex\")\n", "df = df.groupBy(\"lang\").agg(F.sum(F.length(\"content\")).alias(\"total_chars\")).sort(\"total_chars\")\n", "df.write.mode(\"overwrite\").csv(\"/tmp/starcoder_language_char_counts.csv\", header=True)\n", "\n", "spark.stop()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas\n", "\n", "pdf = pandas.read_csv(\"/home/<USER>/augment/experimental/guy/datasets/starcoder_language_char_counts.csv\")\n", "\n", "pdf[\"est_tokens\"] = pdf[\"total_chars\"] / 3\n", "\n", "# print(pdf.sort_values(\"est_tokens\", ascending=False).head(20))\n", "\n", "pandas.set_option('display.max_rows', 500)\n", "print(pdf.sort_values(\"est_tokens\", ascending=False))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Show length statistics\n", "import pyspark.sql.functions as F\n", "\n", "spark = setup_spark()\n", "df = spark.read.parquet(\"/mnt/efs/augment-lga1/data/raw/the-stack-dedup.2023-02-04/data\")\n", "\n", "# df = df.limit(100)\n", "df = df.filter(df.langpart == \"c\")\n", "\n", "df_max_len = df.withColumn(F.length(\"content\").alias(\"content_length\"))\n", "df_max_len = df_max_len.groupBy(\"langpart\").agg(F.max(\"content_length\").alias(\"max_length\"))\n", "df_max_len = df_max_len.orderBy(df_max_len.max_length.desc())\n", "df_max_len.show()\n", "\n", "# df_max_size = df.groupBy(\"langpart\").agg(F.max(\"size\").alias(\"max_size\"))\n", "# df_max_size = df_max_size.orderBy(df_max_size.max_size.desc())\n", "# df_max_size.show()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Show some statistics on the tokenized dataset\n", "#\n", "# Total token length:\n", "# 103242720756\n", "#\n", "# Total content length:\n", "# 310095306675\n", "#\n", "\n", "spark = setup_spark()\n", "# df = spark.read.parquet(\"/tmp/the-stack-pipeline/tokenized/part-00382-901b8ad7-a385-4404-a936-67451f451762-c000.snappy.parquet\")\n", "df = spark.read.parquet(\"/tmp/the-stack-pipeline/tokenized\")\n", "\n", "# distinct_values = df.select(col(\"lang\")).distinct().collect()\n", "# print(\"lang:\")\n", "# print(distinct_values)\n", "\n", "print(\"Total token length:\")\n", "df = df.withColumn(\"tokenized_content_length\", length(col(\"tokenized_content\")))\n", "total_token_len = df.select(spark_sum(\"tokenized_content_length\")).collect()[0][0]\n", "print(total_token_len//2)\n", "\n", "print(\"Total content length:\")\n", "df = df.withColumn(\"content_length\", length(col(\"content\")))\n", "total_len = df.select(spark_sum(\"content_length\")).collect()[0][0]\n", "print(total_len)\n", "\n", "spark.stop()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["spark = setup_spark()\n", "\n", "# Define the Pandas UDF\n", "@pandas_udf(\n", "    StructType([\n", "        StructField(\"x\", IntegerType()),\n", "        StructField(\"y\", IntegerType()),\n", "        StructField(\"z\", StringType()),\n", "        StructField(\"sum\", IntegerType()),\n", "        StructField(\"group_row_count\", IntegerType()),\n", "    ]),\n", "    PandasUDFType.GROUPED_MAP)\n", "def add(df: pd.DataFrame) -> pd.DataFrame:\n", "    df[\"z\"] = \"group_\" + df[\"z\"]\n", "    df[\"sum\"] = df[\"x\"] + df[\"y\"]\n", "    df[\"group_row_count\"] = len(df)\n", "    return df\n", "\n", "# Create a Spark DataFrame\n", "data = [(1, 2, \"A\"), (3, 4, \"B\"), (5, 6, \"A\"), (7, 8, \"B\"), (9, 10, \"B\")]\n", "df = spark.createDataFrame(data, [\"x\", \"y\", \"z\"])\n", "\n", "# Group the DataFrame by the \"z\" column\n", "grouped_df = df.groupby(\"z\")\n", "\n", "# Apply the Pandas UDF to each group\n", "result = grouped_df.apply(add)\n", "\n", "# Show the result\n", "result.show()\n", "\n", "print(type(result))\n", "\n", "spark.stop()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pyspark import TaskContext\n", "from pyspark.sql.functions import concat, sha2\n", "\n", "spark = setup_spark()\n", "\n", "df = spark.createDataFrame([\n", "    {\"lang\": \"Python\", \"batch_id\": 1, \"data\": 2},\n", "    {\"lang\": \"Python\", \"batch_id\": 1, \"data\": 3},\n", "    {\"lang\": \"Python\", \"batch_id\": 2, \"data\": 4},\n", "    {\"lang\": \"Python\", \"batch_id\": 2, \"data\": 5},\n", "    {\"lang\": \"Python\", \"batch_id\": 3, \"data\": 6},\n", "    {\"lang\": \"Java\", \"batch_id\": 3, \"data\": 7},\n", "    {\"lang\": \"Java\", \"batch_id\": 4, \"data\": 8},\n", "    {\"lang\": \"Java\", \"batch_id\": 4, \"data\": 9},\n", "    {\"lang\": \"Java\", \"batch_id\": 5, \"data\": 10},\n", "    {\"lang\": \"Java\", \"batch_id\": 5, \"data\": 11},\n", "])\n", "\n", "# df = df.withColumn(\"global_batch_id\", concat(col(\"lang\"), col(\"batch_id\").cast(\"string\")))\n", "# df = df.withColumn(\"hashed_global_batch_id\", sha2(\"global_batch_id\", 256))\n", "# df = df.orderBy(\"hashed_global_batch_id\")\n", "\n", "def do_something(iterable):\n", "    partition_id = TaskContext.get().partitionId()\n", "    for pdf in iterable:\n", "        pdf[\"partition_id\"] = partition_id\n", "        yield pdf\n", "\n", "new_schema = StructType(df.schema.fields + [Struct<PERSON>ield(\"partition_id\", IntegerType(), True)])\n", "\n", "df = df.mapInPandas(do_something, schema=new_schema)\n", "df2.show()\n", "\n", "spark.stop()\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pyspark import TaskContext\n", "from pyspark.sql.functions import concat, sha2\n", "import pyspark.sql.functions as F\n", "\n", "spark = setup_spark()\n", "\n", "df = spark.createDataFrame([\n", "    {\"lang\": \"Python\", \"batch_id\": 1, \"data\": 2},\n", "    {\"lang\": \"Python\", \"batch_id\": 1, \"data\": 3},\n", "    {\"lang\": \"Python\", \"batch_id\": 2, \"data\": 4},\n", "    {\"lang\": \"Python\", \"batch_id\": 2, \"data\": 5},\n", "    {\"lang\": \"Python\", \"batch_id\": 3, \"data\": 6},\n", "    {\"lang\": \"Java\", \"batch_id\": 3, \"data\": 7},\n", "    {\"lang\": \"Java\", \"batch_id\": 4, \"data\": 8},\n", "    {\"lang\": \"Java\", \"batch_id\": 4, \"data\": 9},\n", "    {\"lang\": \"Java\", \"batch_id\": 5, \"data\": 10},\n", "    {\"lang\": \"Java\", \"batch_id\": 5, \"data\": 11},\n", "])\n", "\n", "df.groupBy(\"batch_id\").agg(F.collect_list(\"batch_id\")).limit(2).show()\n", "\n", "# def do_something(iterable):\n", "#     partition_id = TaskContext.get().partitionId()\n", "#     for pdf in iterable:\n", "#         pdf[\"partition_id\"] = partition_id\n", "#         yield pdf\n", "\n", "# new_schema = StructType(df.schema.fields + [Struct<PERSON>ield(\"partition_id\", IntegerType(), True)])\n", "\n", "# print(\"df.repartition(4, <PERSON>.rand())\")\n", "# df1 = df.repartition(4, <PERSON>.rand())\n", "# df1 = df1.mapInPandas(do_something, schema=new_schema)\n", "# df1.show()\n", "\n", "# print(\"df.repartition(4)\")\n", "# df2 = df.repartition(4)\n", "# df2 = df2.mapInPandas(do_something, schema=new_schema)\n", "# df2.show()\n", "\n", "spark.stop()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pyspark.sql.functions import udf, explode\n", "from pyspark.sql.types import StructType, StructField, StringType, IntegerType\n", "\n", "spark = setup_spark()\n", "\n", "# Define the non-Pandas UDF\n", "@udf(\"array<struct<q:string, k:string>>\")\n", "def split_names(name):\n", "    rows = []\n", "    elems = name.split(\" \")\n", "    for pair in zip(elems[0::2], elems[1::2]):\n", "        rows.append({\"q\": pair[0], \"k\": pair[1]})\n", "    return rows\n", "\n", "# Create a Spark DataFrame\n", "data = [(\"a b c d\", 30), (\"e f g h\", 25)]\n", "df = spark.createDataFrame(data, [\"name\", \"age\"])\n", "\n", "print(\"df:\")\n", "df.show()\n", "\n", "df = df.withColumn(\"split_names\", split_names(\"name\"))\n", "df.show()\n", "\n", "# Apply the non-Pandas UDF to the \"name_age\" column and unpack the resulting rows\n", "result = df.select(\"age\", explode(\"split_names\").alias(\"exploded\"))\n", "\n", "result.show()\n", "\n", "# # Expand the exploded column into separate columns\n", "# result = result.selectExpr(\"age\", \"exploded.q as q\", \"exploded.k as k\")\n", "\n", "# # Show the result\n", "# print(\"result:\")\n", "# result.show()\n", "\n", "spark.stop()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["spark = setup_spark()\n", "tokenizer = setup_tokenizer()\n", "\n", "df = spark.read.parquet(\n", "    \"/tmp/the-stack-pipeline/retrieval/\"\n", ")\n", "\n", "print(\"Schema:\")\n", "df.printSchema()\n", "print(\"\")\n", "\n", "for row in df.take(20):\n", "    print(f\"\\n{row['max_stars_repo_name']}\\t{row['max_stars_repo_path']}\")\n", "    query_doc = unpack_and_detokenize(tokenizer, row[\"query_doc\"])\n", "    key_doc = unpack_and_detokenize(tokenizer, row[\"key_doc\"])\n", "    print(\"(token len) query:\", len(row[\"query_doc\"])//2, \"key:\", len(row[\"key_doc\"])//2)\n", "    print(\"(byte len) query:\", len(query_doc), \"key:\", len(key_doc))\n", "    # print(\"\\nquery doc:\\n\", query_doc)\n", "    # print(\"\\nkey doc:\\n\", key_doc)\n", "\n", "spark.stop()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "df = pd.DataFrame([\n", "    {\"a\": 1, \"b\": 2},\n", "    {\"a\": 1, \"b\": 3},\n", "    {\"a\": 2, \"b\": 4},\n", "    {\"a\": 2, \"b\": 5},\n", "    {\"a\": 3, \"b\": 6},\n", "    {\"a\": 3, \"b\": 7},\n", "])\n", "\n", "sampled_df = df.sample(frac=1).drop_duplicates(subset=\"a\")\n", "print(\"sampled_df:\")\n", "print(sampled_df)\n", "df = df.drop(sampled_df.index)\n", "\n", "print(\"\\ndf:\")\n", "print(df)\n", "print(\"len:\", len(df))\n", "\n", "sampled_df = df.sample(frac=1).drop_duplicates(subset=\"a\")\n", "print(\"\\nsampled_df:\")\n", "print(sampled_df)\n", "df = df.drop(sampled_df.index)\n", "\n", "print(\"\\ndf:\")\n", "print(df)\n", "print(\"len:\", len(df))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from types import SimpleNamespace\n", "\n", "class SimpleNamespaceWithDictAccess(SimpleNamespace):\n", "    def __init__(self, *args, **kwargs):\n", "        super().__init__(*args, **kwargs)\n", "\n", "    def __getitem__(self, key):\n", "        return getattr(self, key)\n", "\n", "def nested_dict_to_namespace(d: dict) -> SimpleNamespace:\n", "    \"\"\"\n", "    Converts a nested dict into a namespace structure.\n", "\n", "    Dot notation can be used to access fields:\n", "\n", "        ns = nested_dict_to_namespace({\"a\": \"b\"})\n", "        assert ns.a == \"b\"\n", "\n", "    Args:\n", "        d: The nested dict to convert.\n", "\n", "    Returns:\n", "        A namespace object.\n", "    \"\"\"\n", "    # namespace = SimpleNamespace()\n", "    namespace = SimpleNamespaceWithDictAccess()\n", "    for k, v in d.items():\n", "        if isinstance(v, dict):\n", "            namespace.__setattr__(k, nested_dict_to_namespace(v))\n", "        else:\n", "            namespace.__setattr__(k, v)\n", "    return namespace\n", "\n", "ns = nested_dict_to_namespace({\"a.b\": \"c\"})\n", "dir(ns)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pyspark.sql.functions import rand, row_number, floor, ceil, col\n", "from pyspark.sql import Window\n", "spark = setup_spark()\n", "\n", "df = spark.createDataFrame([\n", "    {\"a\": 1, \"b\": 2},\n", "    {\"a\": 1, \"b\": 3},\n", "    {\"a\": 2, \"b\": 4},\n", "    {\"a\": 2, \"b\": 5},\n", "    {\"a\": 3, \"b\": 6},\n", "    {\"a\": 3, \"b\": 7},\n", "    {\"a\": 4, \"b\": 8},\n", "    {\"a\": 4, \"b\": 9},\n", "    {\"a\": 5, \"b\": 10},\n", "    {\"a\": 5, \"b\": 11},\n", "])\n", "\n", "# print(\"1:\")\n", "# df.show()\n", "\n", "df = df.withColumn(\"random\", rand()).sort(\"random\")#.drop(\"random\")\n", "# print(\"2:\")\n", "# df.show()\n", "\n", "window = Window.orderBy(\"random\")\n", "df = df.withColumn(\"row_number\", row_number().over(window))\n", "df = df.withColumn(\"batch\", ceil(col(\"row_number\") / 2))\n", "print(\"3:\")\n", "df.show()\n", "\n", "spark.stop()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["spark = setup_spark()\n", "\n", "df = spark.read.parquet(\"/tmp/the-stack-pipeline/retrieval\")\n", "\n", "# print(\"Rows:\", df.count())\n", "\n", "num_batches = df.agg(spark_max(\"batch_id\")).collect()[0][0]\n", "print(\"Num batches:\", num_batches)\n", "\n", "df.printSchema()\n", "\n", "# @pandas_udf(StructType)\n", "# def print_batch_id(pandas_df):\n", "#     print(pandas_udf.take(1)[\"batch_id\"])\n", "#     return None\n", "\n", "# df.limit(256).groupBy(\"batch_id\").apply(print_batch_id)\n", "\n", "# df.show(3)\n", "# pandas_df = df.limit(256).toPandas()\n", "\n", "# pandas_df[\"query_text\"] = pandas_df[\"query_doc\"].apply(unpack_and_detokenize)\n", "\n", "for row in df.head(3):\n", "    print(row)\n", "\n", "spark.stop()\n", "\n", "# pandas_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import struct\n", "token_ids = [1, 2, 3]\n", "packed_data = struct.pack(f\"<{len(token_ids)}H\", *token_ids)\n", "print(packed_data)\n", "padding_id = 0\n", "num_pads = 10\n", "packed_data + struct.pack(f\"<{num_pads}H\", *([padding_id] * num_pads))\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import torch\n", "torch.Tensor([1, 2, 3])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "gpt_path = \"/home/<USER>/augment/models/gpt-neox\"\n", "if gpt_path not in sys.path:\n", "    sys.path.append(gpt_path)\n", "\n", "import megatron.data.indexed_dataset as indexed_dataset\n", "from pathlib import Path\n", "\n", "batch_size = 8\n", "\n", "batches_dir = Path(\"/mnt/efs/augment-lga1/data/processed/the-stack-pipeline/dense_retrieval_indexed_dataset/batches\")\n", "for i, path in enumerate(batches_dir.glob(\"*.bin\")):\n", "    ds = indexed_dataset.make_dataset(\n", "        str(path.with_suffix(\"\")),\n", "        \"mmap\",\n", "        skip_warmup=True)\n", "    # print(i, len(ds)/2/batch_size)\n", "    if len(ds) % (2*batch_size) != 0:\n", "        print(f\"Found ds {i} with {len(ds)/(2*batch_size)} batches, path={path}\")\n", "        break\n", "\n", "# ds = indexed_dataset.make_dataset(\n", "#     \"/mnt/efs/augment-lga1/data/processed/the-stack-pipeline/dense_retrieval_indexed_dataset/dense_retrieval\",\n", "#     \"mmap\",\n", "#     skip_warmup=True)\n", "# print(\"Combined:\", len(ds)/2/8)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import hf_codegen_tokenizer\n", "tokenizer = hf_codegen_tokenizer.HFCodeGenTokenizer()\n", "\n", "for i in range(10):\n", "    text = tokenizer.detokenize(ds[i])\n", "    print(len(text.split(\"<|padding|>\")[0]))\n", "    # print(len(ds[i]))\n", "# tokenizer.detokenize(ds[0])\n", "\n", "# ds[0].find(50303)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["help(spark.createDataFrame)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pyspark.sql import SparkSession\n", "from pyspark.sql.types import StructType, StructField, StringType, IntegerType\n", "\n", "def process_partition(iterator):\n", "    for row in iterator:\n", "        print(row)\n", "\n", "spark = SparkSession.builder.appName('foreachPartition Example').getOrCreate()\n", "\n", "# Create a DataFrame\n", "data = [(\"<PERSON>\", \"<PERSON><PERSON>\", 25), (\"<PERSON>\", \"<PERSON><PERSON>\", 30), (\"<PERSON>\", \"<PERSON>\", 40)]\n", "schema = StructType([\n", "    StructField(\"first_name\", StringType(), True),\n", "    StructField(\"last_name\", StringType(), True),\n", "    <PERSON><PERSON><PERSON><PERSON><PERSON>(\"age\", IntegerType(), True)\n", "])\n", "df = spark.createDataFrame(data, schema=schema)\n", "\n", "# Apply a transformation to the DataFrame\n", "# df.rdd.foreachPartition(process_partition)\n", "\n", "# Stop the SparkSession\n", "spark.stop()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "\n", "a = np.array([[1, 4, 5], [10, 11, 12]])\n", "t = np.zeros(10)\n", "t[a[0]] = a[1]\n", "t"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.5"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}