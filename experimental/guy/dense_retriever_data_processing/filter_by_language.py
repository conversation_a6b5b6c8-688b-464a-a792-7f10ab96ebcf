"""Filter the-stack by language using spark."""

from pyspark.sql import SparkSession
from pyspark.sql.functions import col


def main():
    """Main."""
    # create a SparkSession object
    spark = (
        SparkSession.builder.appName("FilterTheStackByLanguage")
        .master("spark://guy-dev-data-processing-lga1:7077")
        .config("spark.executor.memory", "10g")
        .config("spark.executor.instances", "18")
        .config("spark.executor.cores", "5")
        .config("spark.driver.memory", "20g")
        .config("spark.driver.maxResultSize", "2g")
        .config("spark.sql.debug.maxToStringFields", "4096")
        .config("spark.serializer", "org.apache.spark.serializer.KryoSerializer")
        .config("spark.sql.files.maxPartitionBytes", "128m")
        .getOrCreate()
    )

    spark.sparkContext.setLogLevel("INFO")

    print("\nSpark configuration:")
    for conf in spark.sparkContext.getConf().getAll():
        print(f"\t{conf[0]}: {conf[1]}")
    print("\n")

    # load the Parquet dataset
    df = spark.read.parquet("/mnt/efs/igor-temp/the-stack-dedup.2023-02-04.03")
    # df = spark.read.parquet("/mnt/efs/igor-temp/raw/the-stack-dedup.2023-02-04/data")

    # print("\nSchema:")
    # print(df.schema)
    # print("")
    # return

    print("Number of partitions: {}".format(df.rdd.getNumPartitions()))
    print("Partitioner: {}".format(df.rdd.partitioner))
    # print("Partitions structure: {}".format(df.rdd.glom().collect()))

    languages = ["C", "C++", "Python", "Java", "Go", "JavaScript"]

    # filter the dataset by language
    # filtered_df = df.filter(col("lang").isin(languages))
    filtered_df = df.where(col("lang").isin(languages))

    print("Query plan:")
    print(filtered_df.explain())

    # write the filtered data to a new Parquet dataset
    filtered_df.write.parquet("tmp/filtered")

    # stop the SparkSession object
    spark.stop()


if __name__ == "__main__":
    main()
