#!/bin/bash

# can optionally also provide doc_ids. by default, will retrieve from all.
curl -X POST http://127.0.0.1:5050/retrieve \
     -H "Content-Type: application/json" \
     -d '{
       "top_k": 20,
       "message": "Your user message here",
       "chat_history": [
          { "request_message": "user message", "response_text": "model response" }
       ],
       "path": "/path/to/file.py",
       "prefix": "Content before selection",
       "selected_code": "Selected code here",
       "suffix": "Content after selection",
       "prefix_begin": 0,
       "suffix_end": 1000
     }'
