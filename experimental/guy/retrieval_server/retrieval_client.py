import requests
from dataclasses import dataclass, field
from typing import List, Optional


@dataclass
class AddedDocument:
    path: str
    text: str


@dataclass
class RetrievalRequest:
    top_k: int
    message: str
    chat_history: List[dict] = field(default_factory=list)
    path: str = ""
    prefix: str = ""
    selected_code: str = ""
    suffix: str = ""
    prefix_begin: int = 0
    suffix_end: int = 0
    doc_ids: Optional[List[str]] = None


@dataclass
class RetrievedChunk:
    text: str
    path: str
    char_offset: int
    line_offset: int


class RetrievalClient:
    def __init__(self, address: str):
        """Address is in the form of host:port"""
        self.base_url = "http://" + address

    def add_documents(self, documents: List[AddedDocument]) -> dict:
        url = f"{self.base_url}/add_documents"
        payload = {
            "documents": [{"path": doc.path, "text": doc.text} for doc in documents]
        }
        response = requests.post(url, json=payload)
        response.raise_for_status()
        return response.json()

    def add_document(self, path: str, text: str) -> dict:
        """Adds a single document to the retrieval database"""
        return self.add_documents([AddedDocument(path=path, text=text)])

    def remove_all_documents(self) -> dict:
        """Removes all documents from the retrieval database"""
        url = f"{self.base_url}/remove_all_documents"
        response = requests.post(url)
        response.raise_for_status()
        return response.json()

    def retrieve(self, request: RetrievalRequest) -> List[RetrievedChunk]:
        """Retrieves relevant chunks of text based on a given query"""
        url = f"{self.base_url}/retrieve"
        payload = {
            "top_k": request.top_k,
            "message": request.message,
            "chat_history": request.chat_history,
            "path": request.path,
            "prefix": request.prefix,
            "selected_code": request.selected_code,
            "suffix": request.suffix,
            "prefix_begin": request.prefix_begin,
            "suffix_end": request.suffix_end,
            "doc_ids": request.doc_ids,
        }
        response = requests.post(url, json=payload)
        response.raise_for_status()
        json_response = response.json()
        return [RetrievedChunk(**chunk) for chunk in json_response["retrieved_chunks"]]


# Example usage:
if __name__ == "__main__":
    client = RetrievalClient("localhost:5050")

    # Add documents
    docs = [
        AddedDocument(path="/path/to/file1.txt", text="This is the content of file 1"),
        AddedDocument(path="/path/to/file2.txt", text="This is the content of file 2"),
    ]
    result = client.add_documents(docs)
    print("Add documents result:", result)

    # Retrieve
    request = RetrievalRequest(top_k=5, message="Sample query")
    result = client.retrieve(request)
    print("Retrieve result:", result)

    # Remove all documents
    result = client.remove_all_documents()
    print("Remove all documents result:", result)
