#!/bin/bash

ADDRESS=127.0.0.1:5050

curl -X POST http://${ADDRESS}/add_documents \
     -H "Content-Type: application/json" \
     -d '{
       "documents": [
         {
           "path": "/path/to/document1.txt",
           "text": "This is the content of document 1."
         },
         {
           "path": "/path/to/document2.txt",
           "text": "This is the content of document 2."
         }
       ]
     }'


# can optionally also provide doc_ids. by default, will retrieve from all.
curl -X POST http://${ADDRESS}/retrieve \
     -H "Content-Type: application/json" \
     -d '{
       "top_k": 20,
       "message": "Your user message here",
       "chat_history": [
          { "request_message": "user message", "response_text": "model response" }
       ],
       "path": "/path/to/file.py",
       "prefix": "Content before selection",
       "selected_code": "Selected code here",
       "suffix": "Content after selection",
       "prefix_begin": 0,
       "suffix_end": 1000
     }'


curl -X POST http://${ADDRESS}/remove_all_documents \
     -H "Content-Type: application/json" \
     -d '{}'
