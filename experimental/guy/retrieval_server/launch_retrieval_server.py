"""Launch a retrieval server."""

import argparse
from dataclasses import dataclass, replace, field
import logging
from pathlib import Path
from typing import List, Optional
import yaml

from flask import Flask, request, jsonify

from research.core.types import compute_file_id
from research.eval.harness import factories
from research.retrieval.types import DocumentIndex, Document, Chunk
from research.model_server.model_server_app import _parse_json_request
from research.core.chat_prompt_input import ResearchChatPromptInput
from research.retrieval.retrieval_database import RetrievalDatabase
from base.prompt_format.common import Exchange


RETRIEVER_CONFIG = """\
retriever:
  scorer:
    name: dense_scorer_v2_fbwd
    checkpoint_path: /mnt/efs/augment/user/igor/checkpoints/chatanol/chatanol1-16-3
  chunker:
    name: line_level
    max_lines_per_chunk: 30
  query_formatter:
    name: base:chatanol6
    tokenizer_name: rogue
    max_tokens: 1024
  document_formatter:
    name: base:ethanol6-embedding-with-path-key
    tokenizer_name: rogue
    add_path: true
    max_tokens: 1024
"""


class RetrievalServerApp(Flask):
    def __init__(self, name: str):
        super().__init__(name)
        self.retriever: DocumentIndex = factories.create_retriever(
            yaml.safe_load(RETRIEVER_CONFIG)["retriever"]
        )
        assert isinstance(self.retriever, RetrievalDatabase)
        self.retriever.load()


app = RetrievalServerApp("Retrieval Server")


@dataclass
class AddedDocument:
    path: str
    text: str


@dataclass
class AddDocumentsRequest:
    documents: list[AddedDocument]


@app.route("/add_documents", methods=["POST"])
def add_documents():
    request_content: AddDocumentsRequest = _parse_json_request(
        request.json, AddDocumentsRequest
    )
    documents = [
        Document(
            id=compute_file_id(doc.path, doc.text),
            text=doc.text,
            path=doc.path,
            meta=None,
        )
        for doc in request_content.documents
    ]
    app.retriever.add_docs(documents)
    return jsonify(
        {
            "status": "success",
            "message": f"Added {len(documents)} documents",
            "added_doc_ids": [doc.id for doc in documents],
            "total_indexed_docs": len(app.retriever.get_doc_ids()),
        }
    )


@app.route("/remove_all_documents", methods=["POST"])
def remove_all_documents():
    app.retriever.remove_all_docs()
    return jsonify({"status": "success", "message": "All documents removed"})


@dataclass
class RetrievalRequest:
    top_k: int

    message: str
    chat_history: List[dict] = field(default_factory=list)
    path: str = ""
    prefix: str = ""
    selected_code: str = ""
    suffix: str = ""
    prefix_begin: int = 0
    suffix_end: int = 0

    doc_ids: Optional[List[str]] = None
    """None means retrieve from all."""


@app.route("/retrieve", methods=["POST"])
def query():
    request_content: RetrievalRequest = _parse_json_request(
        request.json, RetrievalRequest
    )

    if request_content.doc_ids is None:
        doc_ids = app.retriever.get_doc_ids()
    else:
        doc_ids = request_content.doc_ids

    prompt_input = ResearchChatPromptInput(
        message=request_content.message,
        path=request_content.path,
        prefix=request_content.prefix,
        selected_code=request_content.selected_code,
        suffix=request_content.suffix,
        chat_history=[
            Exchange(
                request_message=turn["request_message"],
                response_text=turn["response_text"],
            )
            for turn in request_content.chat_history
        ],
        prefix_begin=request_content.prefix_begin,
        suffix_end=request_content.suffix_end,
        retrieved_chunks=[],
        doc_ids=doc_ids,
    )

    print(f"Retrieving from {len(doc_ids)} documents")

    retrieved_chunks, retrieved_scores = app.retriever.query(
        prompt_input,
        doc_ids=doc_ids,
        top_k=request_content.top_k,
    )
    print(f"Got {len(retrieved_chunks)} chunks")

    response_chunks = [
        {
            "text": chunk.text,
            "path": chunk.path,
            "char_offset": chunk.char_offset,
            "line_offset": chunk.line_offset,
        }
        for chunk in retrieved_chunks
    ]

    return jsonify(
        {
            "status": "success",
            "retrieved_chunks": response_chunks,
        }
    )


def main():
    parser = argparse.ArgumentParser(description="Launch a retrieval server")
    parser.add_argument(
        "--host", type=str, default="127.0.0.1", help="Host to run the server on"
    )
    parser.add_argument(
        "--port", type=int, default=5050, help="Port to run the server on"
    )
    # parser.add_argument(
    #     "--log_dir", type=Path, default=Path("/tmp"), help="Directory to store logs"
    # )
    args = parser.parse_args()

    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)

    logger.info(f"Starting the retrieval server on {args.host}:{args.port}")
    app.run(host=args.host, port=args.port, debug=False)


if __name__ == "__main__":
    main()
