import argparse
import sqlite3
from pathlib import Path
from base.languages.language_guesser import guess_language


def create_db(db_file: Path) -> None:
    if db_file.exists():
        db_file.unlink()
    conn = sqlite3.connect(str(db_file))
    c = conn.cursor()
    c.execute("""
        CREATE TABLE IF NOT EXISTS files (
            path VARCHAR(255) PRIMARY KEY,
            size INT,
            language VARCHAR(255),
            contents VARCHAR(1000000)
        );
    """)
    c.execute("""
        CREATE TABLE IF NOT EXISTS directories (
            path VARCHAR(255) PRIMARY KEY,
            recursive_size INT
        );
    """)
    conn.commit()
    conn.close()


def should_skip(path: Path) -> bool:
    if path.name.startswith("."):
        return True
    if path.name.startswith("node_modules"):
        return True
    return False


def populate_db(db_file: str, codebase_dir: str) -> None:
    conn = sqlite3.connect(db_file)
    c = conn.cursor()

    codebase_dir_path = Path(codebase_dir)
    for dir_path in codebase_dir_path.rglob("*"):
        if dir_path.is_dir():
            if should_skip(dir_path):
                continue
            rel_path = dir_path.relative_to(codebase_dir_path)
            rec_size = sum(f.stat().st_size for f in dir_path.rglob("*") if f.is_file())
            c.execute(
                "INSERT INTO directories (path, recursive_size) VALUES (?, ?)",
                (str(rel_path), rec_size),
            )

    for file_path in codebase_dir_path.rglob("*"):
        if file_path.is_file():
            if should_skip(file_path):
                continue
            with file_path.open("r", encoding="utf8") as f:
                try:
                    rel_path = file_path.relative_to(codebase_dir_path)
                    contents = f.read()
                    size = len(contents)
                    if len(contents) > 1000000:
                        continue
                    language = guess_language(str(file_path))
                    c.execute(
                        "INSERT INTO files (path, size, language, contents) VALUES (?, ?, ?, ?)",
                        (str(rel_path), size, language, contents),
                    )
                except UnicodeDecodeError:
                    continue

    conn.commit()
    conn.close()


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--db-file",
        type=Path,
        default="/tmp/codebase.db",
        help="Path to the database file",
    )
    parser.add_argument(
        "--codebase-dir",
        type=str,
        default="/home/<USER>/augment_demo",
        help="Path to the codebase directory",
    )

    args = parser.parse_args()

    create_db(args.db_file)
    populate_db(args.db_file, args.codebase_dir)


if __name__ == "__main__":
    main()
