import re
import subprocess
from research.llm_apis.chat_utils import Llama3ChatClient
import pathlib
from tempfile import TemporaryDirectory
import sqlite3
from tabulate import tabulate
import random


REPO_ROOT = "/home/<USER>/augment_demo"


CODE_TEMPLATE = r"""\
Please run this (?:code|query) for me:
```
(.*)
```
"""


def execute_code(code: str, max_output_lines: int) -> str:
    with TemporaryDirectory() as tmp_dir:
        with (pathlib.Path(tmp_dir) / "code.py").open("w") as f:
            f.write(code)
        result = subprocess.run(
            f"python3 {tmp_dir}/code.py",
            shell=True,
            capture_output=True,
            text=True,
            cwd=REPO_ROOT,
            check=False,
        )
        output = result.stdout + result.stderr
        truncated_output = "".join(output.splitlines(keepends=True)[:max_output_lines])
        if len(output) > len(truncated_output):
            truncated_output += "...\n"
        return truncated_output


def contains_tool_use(message: str) -> bool:
    return bool(re.search(CODE_TEMPLATE, message, re.DOTALL))


def execute_dialog_style_code_tools(message: str, max_output_lines: int = 50) -> str:
    match = re.search(CODE_TEMPLATE, message, re.DOTALL)
    assert match

    code = match.group(1)

    print(f"\n[EXECUTING CODE:]\n{code}")
    output = execute_code(code, max_output_lines)
    return output
    # return ""


def execute_dialog_style_sql_tools(message: str, max_output_lines: int = 50) -> str:
    match = re.search(CODE_TEMPLATE, message, re.DOTALL)
    assert match

    query = match.group(1)

    print(f"\n[EXECUTING QUERY:]\n{query}")

    with sqlite3.connect("/tmp/codebase.db") as conn:
        cursor = conn.cursor()
        cursor.execute(query)
        headers = [description[0] for description in cursor.description]
        all_rows = cursor.fetchall()

        # Sample the rows so that we get a typical result rather than just the head
        if len(all_rows) > max_output_lines:
            rows = random.sample(all_rows, max_output_lines)
        else:
            rows = all_rows

        output = tabulate(rows, headers, tablefmt="simple")
        return output


def main():
    address = "**************:8000"
    client = Llama3ChatClient("triton", address=address, timeout=180)

    system_prompt_dialog_code = """\
    You are a helpful coding assistant, operating within a codebase. If needed, you can ask the
    user to run a python script on the codebase and report the results back to help you answer the question.
    Results may be truncated, so make sure to print the most important results first.

    In this project, every file has documentation in another file in the same dir called {FILENAME}.summary.md.
    Every directory has a summary of that directory in a file called directory.summary.md.
    You can access these files if it helps to answer the question.

    To identify code the user should run, wrap the code using this formatting:

    Please run this code for me:
    ```
    ...
    ```
    """

    system_prompt_dialog_sql = """\
    You are a helpful coding assistant, operating within a codebase. The codebase is stored in a SQL
    database with the following schema:

    ```
    CREATE TABLE files (
        path VARCHAR(255) PRIMARY KEY,
        size INT,
        language VARCHAR(255),
        contents VARCHAR(1000000)
    );

    CREATE TABLE directories (
        path VARCHAR(255) PRIMARY KEY,
        recursive_size INT
    );
    ```

    The `summary` fields contain natural-language summaries of the files and directories.

    If needed, you can ask the user to run a SQL query code database and report the
    results back to help you answer the question.  Results may be truncated, so make
    sure to print the most important results first.

    To identify code the user should run, wrap the code using this formatting:

    Please run this query for me:
    ```
    ...
    ```
    """

    # system_prompt = system_prompt_dialog_code
    system_prompt = system_prompt_dialog_sql

    # What are the most common languages in this project?
    # What are the top-level modules in this project?
    # What are the top languages used in research/ ?
    # Are there modules in this project that don't have much test coverage?

    # question = """\
    # what are the most common languages in this project?
    # """

    # questions = [
    #     "what are the most common languages in this project?",
    #     "I mean only programming languages",
    # ]

    questions = [
        "what are the most common languages in this project?",
        "where in the code is C++ used?",
    ]

    # questions = [
    #     "Are there any webhooks or API integrations in this project?"
    # ]

    # questions = [
    # ]

    # questions = [
    #     "what are the top-level modules in this project?",
    #     "what is the purpose of the base/ module?",
    #     # "what's in base/?",
    #     # "maybe the readme file says what's in this directory?",
    # ]

    dialog = [questions.pop(0)]
    print("USER:", dialog[0])

    while True:
        response = client.generate(
            messages=dialog, max_tokens=1024, system_prompt=system_prompt
        )
        dialog.append(response)

        print("\n[ASSISTANT:]\n" + response)

        if contains_tool_use(response):
            exec_output: str = execute_dialog_style_sql_tools(response)
            user_response = f"Here is the output:\n```\n{exec_output}\n```"
            dialog.append(user_response)
            print("\n[EXECUTION OUTPUT:]\n" + user_response)
        else:
            if questions:
                next_question = questions.pop(0)
                dialog.append(next_question)
                print("\nUSER:", next_question)
            else:
                break


if __name__ == "__main__":
    main()
