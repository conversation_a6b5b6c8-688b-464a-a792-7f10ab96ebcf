{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["3511\n"]}], "source": ["from pathlib import Path\n", "\n", "from experimental.guy.summarization.summarize_repo import summarize_file, question\n", "\n", "from research.llm_apis.chat_utils import Llama3ChatClient, ChatClient\n", "\n", "address = \"*************:8080\"\n", "# address = \"127.0.0.1:8080\"\n", "\n", "client = Llama3ChatClient(\"llama.cpp\", address=address, timeout=180)\n", "\n", "path = \"/home/<USER>/jvector/jvector-base/src/main/java/io/github/jbellis/jvector/graph/ConcurrentNeighborMap.java\"\n", "\n", "# response = client.generate(messages=[\"hello\"], max_tokens=32)\n", "# print(response)\n", "\n", "question = question(Path(path))\n", "prompt = client._prepare_prompt_text(messages=[question])\n", "\n", "prompt_tokens = client._prepare_prompt_tokens(messages=[question])\n", "print(len(prompt_tokens))\n", "\n", "# print(prompt)\n", "\n", "# summary = summarize_file(Path(path), client)\n", "# print(summary)\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}