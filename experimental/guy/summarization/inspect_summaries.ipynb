{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "p = Path(\"a/b/c/foo.txt\")\n", "print(len(p.parents))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from dataclasses import asdict\n", "from pathlib import Path\n", "import json\n", "\n", "from research.llm_apis.chat_utils import Llama3ChatClient, ChatClient\n", "from experimental.guy.summarization.summarize_repo import SummaryResult\n", "\n", "\n", "def generate_directory_summaries(input_file: Path, output_file: Path, chat_client: ChatClient, skip_patterns: list[str]) -> None:\n", "    file_summaries = []\n", "    with input_file.open(\"r\") as f:\n", "        for line in f:\n", "            summary = json.loads(line)\n", "            file_summaries.append(summary)\n", "\n", "    # Group file summaries by directory\n", "    dir_summaries: dict[Path, list[str]] = {}\n", "    for summary in file_summaries:\n", "        dir_path = Path(summary[\"path\"]).parent\n", "        if dir_path not in dir_summaries:\n", "            dir_summaries[dir_path] = []\n", "\n", "        full_summary = f\"\"\"\\\n", "File path: {summary[\"path\"]}\n", "File summary:\n", "{summary[\"summary\"]}\n", "\"\"\"\n", "        dir_summaries[dir_path].append(full_summary)\n", "\n", "    def num_path_elements(path: Path) -> int:\n", "        return len(path.parents)\n", "\n", "    def should_skip(dir_path: Path):\n", "        for pattern in skip_patterns:\n", "            if str(dir_path).startswith(pattern):\n", "                return True\n", "        return False\n", "\n", "    # Generate directory-level summaries, going from the leaves up (DFS)\n", "    for dir_path in sorted(dir_summaries, key=num_path_elements, reverse=True):\n", "        if should_skip(dir_path):\n", "            print(f\"Skipping {dir_path}\")\n", "            continue\n", "\n", "        dir_file_summaries = dir_summaries[dir_path]\n", "\n", "        print(\"Summarizing:\", dir_path)\n", "        concatenated_summaries = \"\\n\".join(dir_file_summaries)\n", "        prompt = f\"Generate a directory-level summary based on the following file and directory summaries:\\n\\n{concatenated_summaries}\"\n", "\n", "        # print(prompt)\n", "        token_len = chat_client.get_prompt_token_length(messages=[prompt])\n", "        print(token_len)\n", "\n", "        if token_len > 7000:\n", "            print(f\"Prompt for {dir_path} too long, skipping\")\n", "            continue\n", "\n", "        generated_directory_summary = chat_client.generate(messages=[prompt], max_tokens=2048)\n", "        print(generated_directory_summary)\n", "\n", "        directory_summary_text = f\"Directory path: {dir_path}\\n{generated_directory_summary}\\n\"\n", "\n", "        print(directory_summary_text)\n", "        if dir_path.parent not in dir_summaries:\n", "            dir_summaries[dir_path.parent] = []\n", "        dir_summaries[dir_path.parent].append(directory_summary_text)\n", "\n", "        summary_result = SummaryResult(\n", "            path=str(dir_path),\n", "            summary=generated_directory_summary,\n", "            summary_token_len=chat_client.get_token_length(generated_directory_summary),\n", "            question=prompt,\n", "            prompt_token_len=token_len,\n", "        )\n", "\n", "        with output_file.open(\"a\") as f:\n", "            f.write(json.dumps(asdict(summary_result)) + \"\\n\")\n", "\n", "\n", "address = \"**************:8000\"\n", "client = Llama3ChatClient(\"triton\", address=address, timeout=180)\n", "\n", "generate_directory_summaries(\n", "    input_file=Path(\"augment_demo_summaries.jsonl\"),\n", "    output_file=Path(\"augment_demo_dir_summaries.jsonl\"),\n", "    chat_client=client,\n", "    skip_patterns=[\"experimental/\", \"third_party/\"],\n", ")\n", "\n", "# print(client.generate([\"hello\"], max_tokens=64))\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "import json\n", "import textwrap\n", "from itertools import islice\n", "\n", "token_lengths = []\n", "\n", "with Path(\"augment_demo_summaries.jsonl\").open(\"r\") as f:\n", "    for line in f:\n", "        data = json.loads(line)\n", "        token_lengths.append(data[\"summary_token_len\"])\n", "\n", "mean = sum(token_lengths) / len(token_lengths)\n", "stddev = (sum((x - mean) ** 2 for x in token_lengths) / len(token_lengths)) ** 0.5\n", "\n", "print(f\"Avg: {mean}\")\n", "print(f\"Stddev: {stddev}\")\n", "\n", "with Path(\"augment_demo_summaries.jsonl\").open(\"r\") as f:\n", "    for line in islice(f, 10):\n", "        data = json.loads(line)\n", "        print()\n", "        print(\"=\" * 120)\n", "        print(\"Path:\", data[\"path\"])\n", "        print(\"Summary tokens:\", data[\"summary_token_len\"])\n", "        print()\n", "        # print(data[\"question\"])\n", "        print(data[\"summary\"])\n", "        # for textline in data[\"summary\"].splitlines():\n", "        #     print(textwrap.fill(textline, width=80))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}