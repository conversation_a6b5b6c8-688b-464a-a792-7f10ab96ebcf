"""Summarize files in a repository."""

import argparse
from dataclasses import asdict, dataclass
import json
from pathlib import Path
import threading
from typing import Optional
import textwrap

from experimental.guy.apis.process_chat_tasks import (
    process_tasks,
    TaskProcessorInput,
    load_addresses_from_yaml,
)

from research.llm_apis.chat_utils import (
    Llama3ChatClient,
    ChatClient,
    run_health_checks,
)


@dataclass
class SummaryResult:
    """A dataclass to store the summarization result."""

    path: str
    summary: str
    summary_token_len: int
    question: str
    prompt_token_len: int


def prepare_summary_question(path: Path, relative_path: Path) -> str:
    contents = path.read_text("utf8")
    question = f"""\
Briefly summarize the following code file. Include the following details:
- Describe the code's purpose and how it works
- Be sure to mention the most important parts of the code, including the most important functions, classes, and other elements.
- Frameworks and libraries used in the code, and how they are used
- Which other parts of the codebase this code uses, and how they are used
- Design patterns and architecture used in the code
- Languages used in the code

Path: {relative_path}
Contents:
```
{contents}
```
"""
    return question


def wrap_long_lines(text: str, max_line_length: int = 100) -> str:
    result = ""
    for line in text.splitlines():
        result += textwrap.fill(line, width=max_line_length) + "\n"
    return result


def summarize_file(
    path: Path, relative_path: Path, chat_client: ChatClient
) -> Optional[SummaryResult]:
    try:
        print(f"Summarizing {relative_path}")
        question = prepare_summary_question(path, relative_path)
        prompt_token_len = chat_client.get_prompt_token_length(messages=[question])
        summary = wrap_long_lines(
            chat_client.generate(messages=[question], max_tokens=2048)
        )
        return SummaryResult(
            path=str(relative_path),
            summary=summary,
            summary_token_len=chat_client.get_token_length(summary),
            question=question,
            prompt_token_len=prompt_token_len,
        )
    except Exception as e:  # pylint: disable=broad-except
        print(f"Error while processing {relative_path}: {e}")
        return None


def generate_directory_summaries(
    input_file: Path, output_dir: Path, chat_client: ChatClient
) -> None:
    file_summaries = []
    with input_file.open("r") as f:
        for line in f:
            summary = json.loads(line)
            file_summaries.append(summary)

    # Group file summaries by directory
    dir_summaries = {}
    for summary in file_summaries:
        dir_path = Path(summary["path"]).parent
        if dir_path not in dir_summaries:
            dir_summaries[dir_path] = []
        summary_text = (
            f"File path: {summary['path']}\nFile summary:\n{summary['summary']}\n"
        )
        dir_summaries[dir_path].append(summary_text)

    # Generate directory-level summaries
    # Go from the leaves up (DFS), and add the directory-level summary to the parent directory
    for dir_path in sorted(dir_summaries, key=lambda x: len(x.parents), reverse=True):
        summaries = dir_summaries[dir_path]
        concatenated_summaries = "\n\n".join(summaries)
        prompt = f"Generate a directory-level summary based on the following file-level summaries:\n\n{concatenated_summaries}"
        generated_directory_summary = chat_client.generate(
            messages=[prompt], max_tokens=2048
        )

        directory_summary_text = (
            f"Directory path: {dir_path}\n{generated_directory_summary}\n"
        )

        print(directory_summary_text)
        if dir_path.parent not in dir_summaries:
            dir_summaries[dir_path.parent] = []
        dir_summaries[dir_path.parent].append(directory_summary_text)


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("repo_path", help="Path to the code repository")
    parser.add_argument(
        "--output_jsonl",
        type=Path,
        required=True,
        help="A jsonl path where to save the summaries.",
    )
    parser.add_argument(
        "--address_yaml_file",
        type=Path,
        required=True,
        help="A yaml file that contains server addresses. See docstring for example.",
    )
    parser.add_argument(
        "--extensions",
        default=".java,.py,.c,.h,.sh,.ts,.js,.rs,.go",
        type=str,
        help="Comma-separated list of file extensions to summarize.",
    )
    parser.add_argument(
        "--server_request_timeout_secs",
        type=int,
        default=600,
        help="Max number of tokens in sample prompts.",
    )
    parser.add_argument(
        "--skip_health_check",
        action="store_true",
        help="Skip health checks for chat servers",
    )
    parser.add_argument(
        "--directory_summaries",
        action="store_true",
        help="Skip health checks for chat servers",
    )
    args = parser.parse_args()

    addresses = load_addresses_from_yaml(args.address_yaml_file)

    if not args.skip_health_check:
        run_health_checks(addresses)

    repo_path = Path(args.repo_path)
    extensions = args.extensions.split(",")

    if len(addresses) == 0:
        raise ValueError("Must specify --address_yaml_file")

    chat_clients: list[ChatClient] = [
        Llama3ChatClient(
            "triton", address=address, timeout=args.server_request_timeout_secs
        )
        for address in addresses
    ]

    paths_to_summarize = []

    for path in repo_path.rglob("*"):
        if "." not in path.name:
            continue
        if path.suffix not in extensions:
            continue
        paths_to_summarize.append(str(path))

    print(f"Found {len(paths_to_summarize)} files to summarize.")

    lock = threading.RLock()
    file_summaries: dict[str, SummaryResult] = {}

    def process_file_summary_task(task_processor_input: TaskProcessorInput):
        path = Path(task_processor_input.task_data)
        summary_result = summarize_file(
            path,
            relative_path=path.relative_to(repo_path),
            chat_client=task_processor_input.chat_client,
        )

        if summary_result:
            with lock:
                file_summaries[str(path)] = summary_result
                with args.output_jsonl.open("w") as f:
                    for path, summary_result in file_summaries.items():
                        f.write(json.dumps(asdict(summary_result)) + "\n")
                    print(
                        f"Saved {len(file_summaries)} summaries to {args.output_jsonl}"
                    )

    process_tasks(
        chat_clients=chat_clients,
        tasks=paths_to_summarize,
        task_processor_fn=process_file_summary_task,
        per_thread_init_delay=0,
    )


if __name__ == "__main__":
    main()
