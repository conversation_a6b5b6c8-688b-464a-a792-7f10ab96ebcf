from dataclasses import asdict
from pathlib import Path
import json

from research.llm_apis.chat_utils import Llama3ChatClient, ChatClient
from experimental.guy.summarization.summarize_repo import SummaryResult


def generate_directory_summaries(
    input_file: Path,
    output_file: Path,
    chat_client: ChatClient,
    skip_patterns: list[str],
) -> None:
    file_summaries = []
    with input_file.open("r") as f:
        for line in f:
            summary = json.loads(line)
            file_summaries.append(summary)

    # Group file summaries by directory
    dir_summaries: dict[Path, list[str]] = {}
    for summary in file_summaries:
        dir_path = Path(summary["path"]).parent
        if dir_path not in dir_summaries:
            dir_summaries[dir_path] = []

        full_summary = f"""\
File path: {summary["path"]}
File summary:
{summary["summary"]}
"""
        dir_summaries[dir_path].append(full_summary)

    def num_path_elements(path: Path) -> int:
        return len(path.parents)

    def should_skip(dir_path: Path):
        for pattern in skip_patterns:
            if str(dir_path).startswith(pattern):
                return True
        return False

    # Generate directory-level summaries, going from the leaves up (DFS)
    for dir_path in sorted(dir_summaries, key=num_path_elements, reverse=True):
        if should_skip(dir_path):
            print(f"Skipping {dir_path}")
            continue

        dir_file_summaries = dir_summaries[dir_path]

        print(
            f"Summarizing directory {dir_path} with {len(dir_file_summaries)} summaries"
        )
        concatenated_summaries = "\n".join(dir_file_summaries)
        prompt = f"Generate a directory-level summary based on the following file and directory summaries:\n\n{concatenated_summaries}"

        # print(prompt)
        token_len = chat_client.get_prompt_token_length(messages=[prompt])
        print(token_len)

        if token_len > 7000:
            print(f"Prompt for {dir_path} too long, skipping")
            continue

        generated_directory_summary = chat_client.generate(
            messages=[prompt], max_tokens=2048
        )
        print(generated_directory_summary)

        directory_summary_text = (
            f"Directory path: {dir_path}\n{generated_directory_summary}\n"
        )

        print(directory_summary_text)
        if dir_path.parent not in dir_summaries:
            dir_summaries[dir_path.parent] = []
        dir_summaries[dir_path.parent].append(directory_summary_text)

        summary_result = SummaryResult(
            path=str(dir_path),
            summary=generated_directory_summary,
            summary_token_len=chat_client.get_token_length(generated_directory_summary),
            question=prompt,
            prompt_token_len=token_len,
        )

        with output_file.open("a") as f:
            f.write(json.dumps(asdict(summary_result)) + "\n")


address = "**************:8000"
client = Llama3ChatClient("triton", address=address, timeout=180)

generate_directory_summaries(
    input_file=Path("augment_demo_summaries.jsonl"),
    output_file=Path("augment_demo_dir_summaries.jsonl"),
    chat_client=client,
    skip_patterns=["experimental/", "third_party/"],
)

# print(client.generate(["hello"], max_tokens=64))
