from research.core.model_input import ModelInput
from research.models import GenerationOptions
from research.models.all_models import (
    LLAMA2Pretrain7B,
    StarCoderBase1B,
    StarCoderBase3B,
    StarCoderBase7B,
    StarCoderBase16B,
)
from research.models.fastforward_models import (  # pylint: disable=import-outside-toplevel
    StarCoderBase1B_FastForward,
    StarCoderBase16B_FastForward,
)

# model = StarCoderBase1B()
model = StarCoderBase1B_FastForward()

model.load()

options = GenerationOptions(max_generated_tokens=32)
input = ModelInput(prefix="def hello():")
generated = model.generate(input, options)

print("")
print(input.prefix, end="")
print(generated)
