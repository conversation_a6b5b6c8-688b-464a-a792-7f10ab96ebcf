{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# New schema"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "import pandas\n", "import numpy as np\n", "from pathlib import Path\n", "from typing import Literal\n", "\n", "path = Path(\"experimental/guy/analysis/usage/augment_activity_3.csv\")\n", "\n", "# filtered_tenants = [\"aitutor-turing\", \"aitutor-mercor\", \"dogfood-shard\", \"i0-vanguard0\"]\n", "filtered_tenants = [\"aitutor-turing\", \"aitutor-mercor\"]\n", "\n", "start_date = pandas.to_datetime(\"2024-11-01\")\n", "\n", "raw_df = pandas.read_csv(path)\n", "\n", "# The raw data has separate rows for separate activities (completions/chat/...)\n", "# but we want a single row per session/date/tenant.\n", "\n", "pivoted = raw_df.pivot_table(\n", "    index=[\"date\", \"session_id\", \"tenant\"],\n", "    columns=\"activity_type\",\n", "    values=[\"total\", \"accepted\"],\n", "    fill_value=0,\n", ")\n", "\n", "# Flatten the column names\n", "pivoted.columns = [f\"{value}_{activity}\" for value, activity in pivoted.columns]\n", "\n", "# Reset the index to turn the index back into columns\n", "df = pivoted.reset_index()\n", "\n", "# Rename the columns to match the desired output\n", "df = df.rename(\n", "    columns={\n", "        \"total_COMPLETION\": \"total_completions\",\n", "        \"accepted_COMPLETION\": \"accepted_completions\",\n", "        \"total_CHAT\": \"total_chats\",\n", "        \"accepted_CHAT\": \"accepted_chats\",\n", "        \"total_SUGGESTED_EDITS\": \"total_suggestions\",\n", "        \"accepted_SUGGESTED_EDITS\": \"accepted_suggestions\",\n", "    }\n", ")\n", "\n", "# Ensure all columns are present, even if they weren't in the original data\n", "for col in [\n", "    \"total_completions\",\n", "    \"accepted_completions\",\n", "    \"total_chats\",\n", "    \"accepted_chats\",\n", "    \"total_suggestions\",\n", "    \"accepted_suggestions\",\n", "]:\n", "    if col not in df.columns:\n", "        df[col] = 0\n", "\n", "# Reorder columns to match the desired output\n", "df = df[\n", "    [\n", "        \"date\",\n", "        \"session_id\",\n", "        \"tenant\",\n", "        \"total_completions\",\n", "        \"accepted_completions\",\n", "        \"total_chats\",\n", "        \"accepted_chats\",\n", "        \"total_suggestions\",\n", "        \"accepted_suggestions\",\n", "    ]\n", "]\n", "\n", "# Do some filtering\n", "df = df[~df[\"tenant\"].isin(filtered_tenants)]\n", "\n", "df[\"date\"] = pandas.to_datetime(df[\"date\"])\n", "df = df[df[\"date\"] >= start_date]\n", "\n", "print(\"Columns:\")\n", "print(df.columns)\n", "\n", "print(\"\\nAll nTenants:\")\n", "print(df[\"tenant\"].unique())\n", "\n", "vanguard = \"i0-vanguard0\"\n", "discovery = \"discovery0\"\n", "dogfood = \"dogfood-shard\"\n", "\n", "wave1_tenants = df[df[\"total_suggestions\"] > 0][\"tenant\"].unique().tolist()\n", "wave1_tenants = [t for t in wave1_tenants if t not in [vanguard, discovery, dogfood]]\n", "\n", "print(\"\\nWave1 Tenants:\")\n", "print(wave1_tenants)\n", "\n", "\n", "def select_cohorts(cohort: Literal[\"wave1\", \"vanguard\", \"dogfood\"]) -> pandas.DataFrame:\n", "    if cohort == \"wave1\":\n", "        tenants = wave1_tenants\n", "    elif cohort == \"vanguard\":\n", "        tenants = [vanguard]\n", "    elif cohort == \"dogfood\":\n", "        tenants = [dogfood]\n", "    else:\n", "        raise ValueError(f\"Unknown cohort: {cohort}\")\n", "    return df[df[\"tenant\"].isin(tenants)]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["accepted_completions = df[\"accepted_completions\"]\n", "chats = df[\"total_chats\"]\n", "accepted_next_edit = df[\"accepted_suggestions\"]\n", "\n", "plt.scatter(accepted_completions, chats, s=5)\n", "plt.show()\n", "\n", "np.corrcoef([accepted_completions, chats])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for cohort in [\"dogfood\", \"wave1\", \"vanguard\"]:\n", "    plot_df = select_cohorts(cohort)\n", "\n", "    plot_df[\"one\"] = 1\n", "    chat_users_per_day = (\n", "        plot_df[plot_df[\"total_chats\"] > 0].groupby(\"date\").count()[\"one\"]\n", "    )\n", "    completions_users_per_day = (\n", "        plot_df[plot_df[\"accepted_completions\"] > 0].groupby(\"date\").count()[\"one\"]\n", "    )\n", "    total_completions_users_per_day = (\n", "        plot_df[plot_df[\"total_completions\"] > 0].groupby(\"date\").count()[\"one\"]\n", "    )\n", "    next_edit_users_per_day = (\n", "        plot_df[plot_df[\"accepted_suggestions\"] > 0].groupby(\"date\").count()[\"one\"]\n", "    )\n", "    total_next_edit_users_per_day = (\n", "        plot_df[plot_df[\"total_suggestions\"] > 0].groupby(\"date\").count()[\"one\"]\n", "    )\n", "\n", "    plt.figure()\n", "    plt.plot(total_completions_users_per_day, color=\"b\", label=\"seen a completion\")\n", "    plt.plot(\n", "        completions_users_per_day,\n", "        color=\"b\",\n", "        linestyle=\"--\",\n", "        label=\"accepted a completion\",\n", "    )\n", "    plt.plot(chat_users_per_day, color=\"g\", label=\"chatted\")\n", "    plt.plot(total_next_edit_users_per_day, color=\"orange\", label=\"seen a next-edit\")\n", "    plt.plot(\n", "        next_edit_users_per_day,\n", "        color=\"orange\",\n", "        linestyle=\"--\",\n", "        label=\"accepted a next-edit\",\n", "    )\n", "\n", "    plt.legend(loc=\"upper right\", bbox_to_anchor=(1.45, 1))\n", "    plt.title(f\"{cohort}: Number of daily active users who have...\")\n", "    plt.xticks(rotation=45, ha=\"right\")\n", "    plt.grid()\n", "\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for cohort in [\"dogfood\", \"wave1\", \"vanguard\"]:\n", "    plot_df = select_cohorts(cohort)\n", "\n", "    volume_df = plot_df.groupby(\"date\").sum()\n", "\n", "    plt.figure()\n", "    # plt.plot(volume_df[\"total_completions\"], color=\"b\", ls=\"--\", label=\"total completions\")\n", "    plt.plot(volume_df[\"accepted_completions\"], color=\"b\", label=\"accepted completions\")\n", "    plt.plot(\n", "        volume_df[\"accepted_completions\"] / 6,\n", "        color=\"b\",\n", "        ls=\"-.\",\n", "        label=\"accepted completions / 6\",\n", "    )\n", "    plt.plot(\n", "        volume_df[\"accepted_completions\"] / 10,\n", "        color=\"r\",\n", "        ls=\"-.\",\n", "        label=\"accepted completions / 10\",\n", "    )\n", "\n", "    plt.plot(volume_df[\"total_chats\"], color=\"g\", label=\"total chats\")\n", "    plt.plot(\n", "        volume_df[\"total_suggestions\"], color=\"orange\", ls=\"--\", label=\"total next-edit\"\n", "    )\n", "    plt.plot(\n", "        volume_df[\"accepted_suggestions\"], color=\"orange\", label=\"accepted next-edit\"\n", "    )\n", "\n", "    plt.legend(loc=\"upper right\", bbox_to_anchor=(1.45, 1))\n", "    plt.title(f\"Request volume in {cohort}\")\n", "    plt.xticks(rotation=45, ha=\"right\")\n", "    # plt.ylim(0, 5000)\n", "    plt.grid()\n", "\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Previous schema"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "import pandas\n", "import numpy as np\n", "from pathlib import Path\n", "\n", "path = Path(\"experimental/guy/analysis/usage/bigquery_statistics.csv\")\n", "\n", "filtered_tenants = [\"aitutor-turing\", \"aitutor-mercor\", \"dogfood-shard\", \"i0-vanguard0\"]\n", "\n", "df = pandas.read_csv(path)\n", "df = df[~df[\"tenant\"].isin(filtered_tenants)]\n", "\n", "print(\"Columns:\")\n", "print(df.columns)\n", "\n", "print(\"\\nTenants:\")\n", "print(df[\"tenant\"].unique())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["accepted_completions = df[\"accepted_completions\"]\n", "chats = df[\"total_chats\"]\n", "accepted_next_edit = df[\"accepted_suggestions\"]\n", "\n", "plt.scatter(accepted_completions, chats, s=5)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["np.corrcoef([accepted_completions, chats, accepted_next_edit])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df[\"one\"] = 1\n", "chat_users_per_day = df[df[\"total_chats\"] > 0].groupby(\"date\").count()[\"one\"]\n", "completions_users_per_day = (\n", "    df[df[\"accepted_completions\"] > 0].groupby(\"date\").count()[\"one\"]\n", ")\n", "next_edit_users_per_day = (\n", "    df[df[\"accepted_suggestions\"] > 0].groupby(\"date\").count()[\"one\"]\n", ")\n", "\n", "plt.plot(chat_users_per_day)\n", "plt.plot(completions_users_per_day)\n", "plt.plot(next_edit_users_per_day)\n", "\n", "plt.xticks(rotation=45, ha=\"right\")\n", "\n", "plt.legend([\"chats\", \"completions\", \"next_edit\"])\n", "plt.grid()"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 2}