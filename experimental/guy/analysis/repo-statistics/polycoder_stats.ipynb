{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "import numpy as np\n", "import csv\n", "import collections\n", "\n", "def plot_log_hist(ax, data, nbins=10):\n", "    data = np.array(data)\n", "    logbins = np.geomspace(data.min(), data.max(), nbins)\n", "    ax.hist(data, bins=logbins)\n", "    ax.set_xscale('log')\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "all_token_counts = []\n", "token_counts_by_language = collections.defaultdict(list)\n", "\n", "# Tokens are counted separately for mem/test splits\n", "token_counts_by_repo = collections.defaultdict(lambda: 0)\n", "\n", "with open(\"polycoder.v3_token_counts.tsv\", \"r\") as f:\n", "    reader = csv.reader(f, delimiter=\"\\t\")\n", "    next(reader)  # skip header line\n", "    for line in reader:\n", "        full_repo, count = line\n", "        count = int(count)\n", "\n", "        language, repo_and_split = full_repo.split(\"/\")\n", "        repo, split = repo_and_split.rsplit(\".\", maxsplit=1)\n", "\n", "        # Will effectively sum over the different splits\n", "        token_counts_by_repo[(language, repo)] += count\n", "\n", "for (language, repo), count in token_counts_by_repo.items():\n", "    all_token_counts.append(count)\n", "    token_counts_by_language[language].append(count)\n", "\n", "print(f\"Found {len(all_token_counts)} repositories\")\n", "print(\"Found these languages:\")\n", "\n", "for lang in token_counts_by_language:\n", "    print(\"\\t\", lang)\n", "\n", "#plt.hist(all_token_counts)\n", "fig, ax = plt.subplots()\n", "plot_log_hist(ax, all_token_counts)\n", "ax.set_title(\"polycoder.v3 repository token counts\")\n", "ax.set_xlabel(\"token count\")\n", "ax.set_ylabel(\"num repos\")\n", "\n", "num_langs = len(token_counts_by_language)\n", "assert num_langs % 2 == 0\n", "fig, ax = plt.subplots(ncols=num_langs//2, nrows=2, figsize=(24, 12))\n", "\n", "for i, lang in enumerate(sorted(token_counts_by_language)):\n", "    row = i // 3\n", "    col = i % 3\n", "    plot_log_hist(ax[row][col], token_counts_by_language[lang])\n", "    ax[row][col].set_title(f\"{lang}\")\n", "    ax[row][col].set_xlabel(\"token count\")\n", "    ax[row][col].set_ylabel(\"num repos\")\n", "\n", "plt.show()\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "\n", "token_counts = []\n", "per_language_token_counts = collections.defaultdict(list)\n", "\n", "with Path(\"polycoder.v3_per_file_token_counts.tsv\").open(\"r\") as f:\n", "    header = f.readline()\n", "    for line in f:\n", "        line.strip()\n", "        repo, filename, tokens = line.split(\"\\t\")\n", "        language = repo.split(\"/\")[-2]\n", "        tokens = int(tokens)\n", "        token_counts.append(tokens)\n", "        per_language_token_counts[language].append(tokens)\n", "\n", "fig, ax = plt.subplots()\n", "plot_log_hist(ax, token_counts)\n", "ax.set_title(\"Per-file token counts\")\n", "\n", "fig, axes = plt.subplots(nrows=1, ncols=3, figsize=(12, 4))\n", "print(\"token count statistics per language:\")\n", "print(\"language\\tmedian\\tmean\\tstddev\")\n", "\n", "for ax, language in zip(axes, [\"Python\", \"Java\", \"C\"]):\n", "    #fig, ax = plt.subplots()\n", "    plot_log_hist(ax, per_language_token_counts[language], nbins=20)\n", "    ax.set_title(f\"Per-file token counts in {language}\")\n", "\n", "    mean = int(np.mean(per_language_token_counts[language]))\n", "    median = int(np.median(per_language_token_counts[language]))\n", "    std = int(np.std(per_language_token_counts[language]))\n", "    print(f\"{language}\\t\\t{median}\\t{mean}\\t{std}\")\n", "\n", "plt.show()\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"name": "python", "version": "3.9.16"}, "orig_nbformat": 4, "vscode": {"interpreter": {"hash": "31f2aee4e71d21fbe5cf8b01ff0e069b9275f58929596ceb00d14d90e3e16cd6"}}}, "nbformat": 4, "nbformat_minor": 2}