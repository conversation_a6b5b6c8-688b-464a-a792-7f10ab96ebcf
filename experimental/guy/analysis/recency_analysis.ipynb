{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "sns.set_style(\"whitegrid\")\n", "import pandas as pd\n", "\n", "import json\n", "from pathlib import Path\n", "\n", "with Path(\"/tmp/recency_dump.json\").open(\"r\", encoding=\"utf8\") as f:\n", "    data = json.load(f)\n", "\n", "df = pd.DataFrame(data)\n", "print(list(data.keys()))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["plt.hist(data[\"initial_scores\"], bins=80)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df[df.softmax_scores > 1e-10].sort_values(\"softmax_scores\", ascending=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df[df.final_scores > 1e-10].sort_values(\"final_scores\", ascending=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df[df.recency_scores > 1e-10]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.17"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}