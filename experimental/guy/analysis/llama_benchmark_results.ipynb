{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Analyze and extrapolate Code LLaMA and DeepSeek benchmark results.\n", "\n", "Numbers taken from https://deepseekcoder.github.io/ ."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "# Numbers from the DeepSeek paper\n", "evaluation_results = [\n", "    {\n", "        \"model\": \"code-llama\",\n", "        \"scale\": \"7b\",\n", "        \"scale_num\": 7,\n", "        \"humaneval_python\": 31.7,\n", "        \"humaneval_multi\": 29.2,\n", "        \"mbpp\": 41.6,\n", "        \"ds-1000\": 22.1,\n", "    },\n", "    {\n", "        \"model\": \"code-llama\",\n", "        \"scale\": \"13b\",\n", "        \"scale_num\": 13,\n", "        \"humaneval_python\": 36.0,\n", "        \"humaneval_multi\": 35.4,\n", "        \"mbpp\": 48.4,\n", "        \"ds-1000\": 26.8,\n", "    },\n", "    {\n", "        \"model\": \"code-llama\",\n", "        \"scale\": \"34b\",\n", "        \"scale_num\": 34,\n", "        \"humaneval_python\": 48.2,\n", "        \"humaneval_multi\": 41.0,\n", "        \"mbpp\": 55.2,\n", "        \"ds-1000\": 34.3,\n", "    },\n", "    {\n", "        \"model\": \"deepseek-coder-base\",\n", "        \"scale\": \"1b\",\n", "        \"scale_num\": 1,\n", "        \"humaneval_python\": 34.8,\n", "        \"humaneval_multi\": 28.3,\n", "        \"mbpp\": 46.2,\n", "        \"ds-1000\": 16.2,\n", "    },\n", "    {\n", "        \"model\": \"deepseek-coder-base\",\n", "        \"scale\": \"7b\",\n", "        \"scale_num\": 7,\n", "        \"humaneval_python\": 49.4,\n", "        \"humaneval_multi\": 44.7,\n", "        \"mbpp\": 60.6,\n", "        \"ds-1000\": 30.5,\n", "    },\n", "    {\n", "        \"model\": \"deepseek-coder-base\",\n", "        \"scale\": \"33b\",\n", "        \"scale_num\": 33,\n", "        \"humaneval_python\": 56.1,\n", "        \"humaneval_multi\": 50.3,\n", "        \"mbpp\": 66.0,\n", "        \"ds-1000\": 40.2,\n", "    },\n", "]\n", "\n", "import pandas\n", "df = pandas.DataFrame(evaluation_results)\n", "\n", "df[\"avg\"] = (df[\"humaneval_multi\"] + df[\"humaneval_python\"] + df[\"mbpp\"] + df[\"ds-1000\"]) / 4\n", "\n", "code_llama_df = df[df[\"model\"] == \"code-llama\"]\n", "deepseek_df = df[df[\"model\"] == \"deepseek-coder-base\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "import numpy as np\n", "import pandas as pd\n", "from sklearn.linear_model import LinearRegression\n", "\n", "def extrapolate(x, y, extrapolated_x, x_scale, y_scale):\n", "    transformations = {\n", "        \"linear\": (lambda x: x, lambda x: x),\n", "        \"log\": (np.log, np.exp),\n", "    }\n", "\n", "    x_transform, x_inv_transform = transformations[x_scale]\n", "    y_transform, y_inv_transform = transformations[y_scale]\n", "\n", "    regressor = LinearRegression()  \n", "\n", "    regressor.fit(x_transform(x.reshape([-1, 1])), y_transform(y))\n", "    # regressor.fit(x, y)\n", "\n", "    return y_inv_transform(regressor.predict(x_transform([[extrapolated_x]]))[0])\n", "\n", "x = code_llama_df[\"scale_num\"].to_numpy()\n", "y = code_llama_df[\"avg\"].to_numpy()\n", "x_pred = 70  # for LLaMA 70B\n", "preds = {}\n", "\n", "# for x_scale, y_scale in [(\"linear\", \"linear\"), (\"log\", \"linear\"), (\"log\", \"log\")]:\n", "for x_scale, y_scale in [(\"log\", \"linear\"), (\"log\", \"log\")]:\n", "    y_pred = extrapolate(x, y, x_pred, x_scale, y_scale)\n", "    preds[x_scale, y_scale] = y_pred\n", "    print(f\"{x_scale}, {y_scale} -> {y_pred}\")\n", "\n", "fig, ax = plt.subplots()\n", "ax.plot(code_llama_df[\"scale_num\"], code_llama_df[\"avg\"], \".-\", label=\"Code LLaMA\")\n", "ax.plot(deepseek_df[\"scale_num\"], deepseek_df[\"avg\"], \".-\", label=\"DeepSeek Coder Base\")\n", "for scales, y_pred in preds.items():\n", "    ax.plot([x_pred], [y_pred], \"o\", label=f\"Extrapolated Code LLaMA: {scales}\")\n", "\n", "ax.set_xlabel(\"Params (B)\")\n", "ax.set_ylabel(\"Mean evaluation score\")\n", "ax.set_xscale(\"log\")\n", "# ax.set_yscale(\"log\")\n", "ax.grid(True)\n", "ax.legend()\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}