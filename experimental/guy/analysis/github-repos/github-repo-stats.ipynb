{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>s3_repo_url</th>\n", "      <th>total_commits</th>\n", "      <th>mean_files_changed</th>\n", "      <th>files_changed</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>s3://augment-github/bare-repos/kafka-go-exampl...</td>\n", "      <td>22</td>\n", "      <td>3.318182</td>\n", "      <td>[1, 2, 8, 1, 2, 2, 13, 1, 1, 1, 1, 1, 2, 3, 1,...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>s3://augment-github/bare-repos/frontend-weddin...</td>\n", "      <td>2</td>\n", "      <td>58.500000</td>\n", "      <td>[12, 105]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>s3://augment-github/bare-repos/organizationalA...</td>\n", "      <td>39</td>\n", "      <td>1.538462</td>\n", "      <td>[1, 2, 1, 4, 2, 2, 1, 1, 2, 1, 2, 1, 1, 1, 1, ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>s3://augment-github/bare-repos/embed-folders.t...</td>\n", "      <td>2</td>\n", "      <td>1.500000</td>\n", "      <td>[1, 2]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>s3://augment-github/bare-repos/Introduce-Mysel...</td>\n", "      <td>7</td>\n", "      <td>62.571429</td>\n", "      <td>[1, 19, 84, 1, 110, 99, 124]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>49992</th>\n", "      <td>s3://augment-github/bare-repos/intellij-commun...</td>\n", "      <td>223679</td>\n", "      <td>11.018084</td>\n", "      <td>[6, 1, 3, 1, 1, 1, 7, 2, 4, 1, 3, 13, 2, 2, 1,...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>49993</th>\n", "      <td>s3://augment-github/bare-repos/cmssw.tar.gz</td>\n", "      <td>212992</td>\n", "      <td>10.222849</td>\n", "      <td>[1, 8, 3, 14, 32, 11, 41, 1, 1, 2, 1, 2, 1, 4,...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>49994</th>\n", "      <td>s3://augment-github/bare-repos/mycroft-changes...</td>\n", "      <td>527892</td>\n", "      <td>1.016170</td>\n", "      <td>[1, 1, 1, 2, 2, 3, 1, 2, 2, 1, 3, 1, 1, 2, 2, ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>49995</th>\n", "      <td>s3://augment-github/bare-repos/intel-llvm.tar.gz</td>\n", "      <td>383997</td>\n", "      <td>6.195278</td>\n", "      <td>[2, 1, 11, 1, 5, 1, 1, 1, 3861, 17, 1, 1, 1, 1...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>49996</th>\n", "      <td>s3://augment-github/bare-repos/llvm-project-v2...</td>\n", "      <td>334706</td>\n", "      <td>5.205533</td>\n", "      <td>[1, 1, 55, 2, 2, 54, 1, 37, 1, 1, 4, 1, 2, 4, ...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>49997 rows × 4 columns</p>\n", "</div>"], "text/plain": ["                                             s3_repo_url  total_commits  \\\n", "0      s3://augment-github/bare-repos/kafka-go-exampl...             22   \n", "1      s3://augment-github/bare-repos/frontend-weddin...              2   \n", "2      s3://augment-github/bare-repos/organizationalA...             39   \n", "3      s3://augment-github/bare-repos/embed-folders.t...              2   \n", "4      s3://augment-github/bare-repos/Introduce-<PERSON>sel...              7   \n", "...                                                  ...            ...   \n", "49992  s3://augment-github/bare-repos/intellij-commun...         223679   \n", "49993        s3://augment-github/bare-repos/cmssw.tar.gz         212992   \n", "49994  s3://augment-github/bare-repos/mycroft-changes...         527892   \n", "49995   s3://augment-github/bare-repos/intel-llvm.tar.gz         383997   \n", "49996  s3://augment-github/bare-repos/llvm-project-v2...         334706   \n", "\n", "       mean_files_changed                                      files_changed  \n", "0                3.318182  [1, 2, 8, 1, 2, 2, 13, 1, 1, 1, 1, 1, 2, 3, 1,...  \n", "1               58.500000                                          [12, 105]  \n", "2                1.538462  [1, 2, 1, 4, 2, 2, 1, 1, 2, 1, 2, 1, 1, 1, 1, ...  \n", "3                1.500000                                             [1, 2]  \n", "4               62.571429                       [1, 19, 84, 1, 110, 99, 124]  \n", "...                   ...                                                ...  \n", "49992           11.018084  [6, 1, 3, 1, 1, 1, 7, 2, 4, 1, 3, 13, 2, 2, 1,...  \n", "49993           10.222849  [1, 8, 3, 14, 32, 11, 41, 1, 1, 2, 1, 2, 1, 4,...  \n", "49994            1.016170  [1, 1, 1, 2, 2, 3, 1, 2, 2, 1, 3, 1, 1, 2, 2, ...  \n", "49995            6.195278  [2, 1, 11, 1, 5, 1, 1, 1, 3861, 17, 1, 1, 1, 1...  \n", "49996            5.205533  [1, 1, 55, 2, 2, 54, 1, 37, 1, 1, 4, 1, 2, 4, ...  \n", "\n", "[49997 rows x 4 columns]"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["import json\n", "from pathlib import Path\n", "import numpy as np\n", "import pandas\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "sns.set_style(\"whitegrid\")\n", "\n", "entries = []\n", "\n", "with Path(\"/mnt/efs/augment-lga1-nvme/data-pipeline/github-repos/statistics/github_repo_stats.jsonl\").open(\"r\", encoding=\"utf8\") as file:\n", "    for line in file:\n", "        entry = json.loads(line)\n", "        entries.append(entry)\n", "\n", "df = pandas.DataFrame(entries)\n", "df"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["def plot_log_hist(ax, data, nbins=10):\n", "    data = np.array(data)\n", "    logbins = np.geomspace(data.min(), data.max(), nbins)\n", "    ax.hist(data, bins=logbins)\n", "    ax.set_xscale('log')\n", "\n", "fig, ax = plt.subplots()\n", "plot_log_hist(ax, df[\"total_commits\"], nbins=20)\n", "ax.set_title(f\"Number of commits in {len(df)} GitHub repos\")\n", "\n", "fig, ax = plt.subplots()\n", "plot_log_hist(ax, df[\"mean_files_changed\"], nbins=20)\n", "ax.set_title(f\"Mean number of files changed per commit, in {len(df)} GitHub repos\")\n", "\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"data": {"text/plain": ["1575"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["for threshold in [1024, 16384]:\n", "    len(df[df[\"total_commits\"] >= threshold])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.5"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}