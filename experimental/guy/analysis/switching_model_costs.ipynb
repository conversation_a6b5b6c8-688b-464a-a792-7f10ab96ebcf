{"cells": [{"cell_type": "code", "execution_count": 9, "id": "97fa1513", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[0.6142656 2.31837  ]\n", "[0.6364  2.36625]\n", "expensive\n", "total: 3.64305\n", "\n", "cheap\n", "total: 1.4502\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import random\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "random.seed(42)\n", "\n", "# Claude cost per token\n", "C_nc = 3.75 / 1e6  # Non-cached input\n", "C_c = 0.3 / 1e6  # Cached input\n", "C_o = 15 / 1e6  # Output\n", "\n", "# Haiku cost per token\n", "C_cheap_nc = 1 / 1e6  # Non-cached input\n", "C_cheap_c = 0.08 / 1e6  # Cached input\n", "C_cheap_o = 4 / 1e6  # Output\n", "\n", "# Gemini 2.5 Flash Preview\n", "# C_cheap_nc = 0.15 / 1e6\n", "# C_cheap_c = 0.0375 / 1e6\n", "# C_cheap_o = 0.6 / 1e6\n", "\n", "# Fake numbers\n", "# C_cheap_c = 1/10 / 1e6  # Cached input\n", "# C_cheap_nc = 0.08/10 / 1e6  # Non-cached input\n", "# C_cheap_o = 4/10 / 1e6  # Output\n", "\n", "# Initial tokens (system prompt)\n", "T_init = 10_000\n", "\n", "# Tokens per turn (guess..)\n", "T = 1000\n", "\n", "# Total number of turns\n", "N = 100\n", "# N = 10\n", "\n", "cheap_success_rate = 0.8\n", "\n", "# Frequency of calling <PERSON><PERSON>\n", "# f = 10\n", "\n", "\n", "def cost(f_cheap):\n", "    \"\"\"If f_cheap == 0, only uses expensive model.\"\"\"\n", "    total_cheap_cost = 0\n", "    total_expensive_cost = 0\n", "    n = 1\n", "\n", "    expensive_saw_system_prompt = False\n", "    cheap_saw_system_prompt = False\n", "    retry_turns = 0\n", "\n", "    while n < N + retry_turns:\n", "        turn_cost = 0\n", "\n", "        if f_cheap != 0 and n % f_cheap == 0:\n", "            # Cheap model's turn\n", "            if cheap_saw_system_prompt:\n", "                turn_cost += C_cheap_c * T_init\n", "            else:\n", "                turn_cost += C_cheap_nc * T_init\n", "                cheap_saw_system_prompt = True\n", "            turn_cost += (\n", "                C_cheap_c * (n - f_cheap) * T + C_cheap_nc * f_cheap * T + C_cheap_o * T\n", "            )\n", "            total_cheap_cost += turn_cost\n", "\n", "            if random.random() > cheap_success_rate:\n", "                retry_turns += 1\n", "        else:\n", "            if expensive_saw_system_prompt:\n", "                turn_cost += C_c * T_init\n", "            else:\n", "                turn_cost += C_nc * T_init\n", "                expensive_saw_system_prompt = True\n", "            if f_cheap != 0 and (n - 1) % f_cheap == 0 and n > 1:\n", "                # Expensive model turn, and last turn was not cached by expensive model\n", "                turn_cost += C_c * (n - 2) * T + C_nc * 2 * T + C_o * T\n", "            else:\n", "                # Expensive model turn, and last turn was cached by expensive model\n", "                turn_cost += C_c * (n - 1) * T + C_nc * T + C_o * T\n", "            total_expensive_cost += turn_cost\n", "\n", "        # print(\"turn cost:\", turn_cost)\n", "        n += 1\n", "    return np.array([total_cheap_cost, total_expensive_cost])\n", "\n", "\n", "def cost_averaged(f_cheap, num_runs=50):\n", "    return np.mean([cost(f_cheap) for _ in range(num_runs)], axis=0)\n", "\n", "\n", "print(cost_averaged(2))\n", "print(cost(2))\n", "\n", "\n", "print(\"expensive\")\n", "expensive_cost = cost(0)[1]\n", "print(\"total:\", expensive_cost)\n", "\n", "print(\"\\ncheap\")\n", "cheap_cost = cost(1)[0]\n", "print(\"total:\", cheap_cost)\n", "\n", "fs = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 15, 20, 50]\n", "all_costs = [cost_averaged(f) for f in fs]\n", "cheap_costs = np.array([x[0] for x in all_costs])\n", "expensive_costs = np.array([x[1] for x in all_costs])\n", "costs = cheap_costs + expensive_costs\n", "\n", "plt.figure()\n", "plt.plot(fs, costs, \".-\", label=\"Cheap + expensive\")\n", "# plt.plot(fs, cheap_costs, \"g.-\", label=\"Cheap\")\n", "# plt.plot(fs, expensive_costs, \"m.-\", label=\"Expensive\")\n", "plt.plot(fs, expensive_cost * np.ones_like(fs), \"r--\", label=\"Expensive only\")\n", "plt.xlabel(\"Turn frequency of cheap model\")\n", "plt.ylabel(\"Cost\")\n", "plt.legend()\n", "plt.grid()\n", "# plt.title(f\"{N} turns, {T} tokens per turn, {T_init} system prompt tokens\")\n", "plt.title(\n", "    f\"<PERSON> + <PERSON><PERSON> ({N} turns, {T} tok/turn, {T_init} sys prompt tokens, success rate {cheap_success_rate})\"\n", ")\n", "plt.show()\n", "\n", "cheap_percent_of_turns = 1 / np.array(fs) * 100\n", "savings = (expensive_cost - costs) / expensive_cost * 100\n", "plt.figure()\n", "plt.plot(cheap_percent_of_turns, savings, \".-\", label=\"Savings\")\n", "plt.xlabel(\"Cheap model % of turns\")\n", "plt.ylabel(\"Savings %\")\n", "plt.grid()\n", "plt.title(\n", "    f\"<PERSON> + <PERSON><PERSON> ({N} turns, {T} tok/turn, {T_init} sys prompt tokens, success rate {cheap_success_rate})\"\n", ")\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "3c51db3c", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 5}