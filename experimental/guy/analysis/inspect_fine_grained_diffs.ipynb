{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["Look at diffs from outside the main branch, to see how fine-grained they are."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas\n", "from pathlib import Path\n", "\n", "from research.data.utils.pr_v2 import get_tar_full_path\n", "from research.utils.repo_change_utils import iterate_repo_history\n", "from research.utils.repo_change_utils import RepoChange\n", "\n", "root = Path(\"/mnt/efs/augment/user/guy/fine_grained_commits\")\n", "\n", "for parquet_file in root.glob(\"*.parquet\"):\n", "    print(parquet_file)\n", "    df = pandas.read_parquet(parquet_file)\n", "    break\n", "\n", "print(df.columns)\n", "\n", "# repo_change_objs: list[RepoChange] = []\n", "# for repo_ID, commits in ...:\n", "#   repo_tarball_path = get_tar_full_path(repo_ID)\n", "#   with tempfile.TemporaryDirectory() as temp_dir:\n", "#       temp_path = Path(temp_dir)\n", "#     with tarfile.open(repo_tarball_path, \"r:*\") as tar:\n", "#       tar.extractall(path=temp_path)\n", "#   for commit in commits:\n", "#     repo_change_objs += iterate_repo_history(\n", "#       project_dir=temp_path\n", "#       history=[commit.parent_sha, commit.sha]\n", "#       is_source_file=lambda x: True,\n", "#     )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df[\"message\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas\n", "\n", "from research.utils.repo_change_utils import CommitMeta, patchset_from_repo_change\n", "import tempfile\n", "from pathlib import Path\n", "import tarfile\n", "from experimental.colin.projects.autofix.training.data_pipelines.utils import (\n", "    create_gpu_spark,\n", ")\n", "\n", "NUM_ROWS_TO_PROCESS = 10\n", "\n", "# spark = create_gpu_spark(10, use_h100=True)\n", "# data = spark.read.parquet('gs://gcp-us1-spark-data/shared/pr_v2/inter_commits')\n", "# data = data.limit(NUM_ROWS_TO_PROCESS).toPandas()\n", "\n", "root = Path(\"/mnt/efs/augment/user/guy/fine_grained_commits\")\n", "\n", "for parquet_file in root.glob(\"*.parquet\"):\n", "    print(parquet_file)\n", "    df = pandas.read_parquet(parquet_file)\n", "    break\n", "\n", "data = df\n", "\n", "repo_change_objs: list[RepoChange] = []\n", "repos_processed = 0\n", "repo_tarballs_missing = 0\n", "for _, row in data.iterrows():\n", "    repos_processed += 1\n", "    if not row[\"parents\"]:\n", "        continue\n", "    owner = row[\"owner\"]\n", "    name = row[\"name\"]\n", "    repo_ID = f\"{owner}/{name}\"\n", "    repo_tarball_path = get_tar_full_path(repo_ID)\n", "    with tempfile.TemporaryDirectory() as temp_dir:\n", "        temp_path = Path(temp_dir)\n", "        try:\n", "            with tarfile.open(repo_tarball_path, \"r:*\") as tar:\n", "                tar.extractall(path=temp_path)\n", "        except FileNotFoundError:\n", "            repo_tarballs_missing += 1\n", "            continue\n", "\n", "        parent_commit_meta = CommitMeta(\n", "            sha=row[\"parents\"][0],\n", "            parents=[],\n", "            children=[row[\"sha\"]],\n", "            message=\"\",\n", "            repo_name=repo_ID,\n", "        )\n", "        child_commit_meta = CommitMeta(\n", "            sha=row[\"sha\"],\n", "            parents=[row[\"parents\"][0]],\n", "            children=[],\n", "            message=\"\",\n", "            repo_name=repo_ID,\n", "        )\n", "        repo_change_objs += iterate_repo_history(\n", "            project_dir=temp_path,\n", "            history=[parent_commit_meta, child_commit_meta],\n", "            is_source_file=lambda x: True,\n", "        )\n", "\n", "print(\n", "    f\"repos_processed: {repos_processed}, repo_tarballs_missing: {repo_tarballs_missing}\"\n", ")\n", "print(f\"Num repo changes: {len(repo_change_objs)}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for repo_change in repo_change_objs:\n", "    ps = patchset_from_repo_change(repo_change, num_context_lines=3)\n", "    print(\"====================================\")\n", "    print(ps)"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Setting KUBECONFIG to /home/<USER>/.kube/config\n", "Skipping bazel build.\n", "Setting default log level to \"WARN\".\n", "To adjust logging level use sc.setLogLevel(newLevel). For SparkR, use setLogLevel(newLevel).\n", "24/12/29 05:59:12 WARN TaskSchedulerImpl: Initial job has not accepted any resources; check your cluster UI to ensure that workers are registered and have sufficient resources\n", "24/12/29 05:59:27 WARN TaskSchedulerImpl: Initial job has not accepted any resources; check your cluster UI to ensure that workers are registered and have sufficient resources\n", "24/12/29 05:59:42 WARN TaskSchedulerImpl: Initial job has not accepted any resources; check your cluster UI to ensure that workers are registered and have sufficient resources\n", "24/12/29 05:59:57 WARN TaskSchedulerImpl: Initial job has not accepted any resources; check your cluster UI to ensure that workers are registered and have sufficient resources\n", "24/12/29 06:00:12 WARN TaskSchedulerImpl: Initial job has not accepted any resources; check your cluster UI to ensure that workers are registered and have sufficient resources\n", "24/12/29 06:00:27 WARN TaskSchedulerImpl: Initial job has not accepted any resources; check your cluster UI to ensure that workers are registered and have sufficient resources\n", "24/12/29 06:00:42 WARN TaskSchedulerImpl: Initial job has not accepted any resources; check your cluster UI to ensure that workers are registered and have sufficient resources\n", "24/12/29 06:00:57 WARN TaskSchedulerImpl: Initial job has not accepted any resources; check your cluster UI to ensure that workers are registered and have sufficient resources\n", "24/12/29 06:01:12 WARN TaskSchedulerImpl: Initial job has not accepted any resources; check your cluster UI to ensure that workers are registered and have sufficient resources\n", "                                                                                \r"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "5838bedd565b49bea16e20ec97e78041", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5176 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: 9407e44fba1bd47f7e0cfcd0ba713a9c7cf60994\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: 9407e44fba1bd47f7e0cfcd0ba713a9c7cf60994\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "eba726b7f73f4ae6a2e6ec1734fba8f2", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5179 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: 5595222a266cd670fd1f523b7294a95839c7df75\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: 5595222a266cd670fd1f523b7294a95839c7df75\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "29c6cace8c02481d8728f12a3de9db1c", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5169 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: 4591929760b5b04641e80ee0190acf98c1b80018\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: 4591929760b5b04641e80ee0190acf98c1b80018\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "e053adba945f4ae9bbd28d987fc8e61f", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5151 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: 41b3e4407d90fbe64c0bc927a9cca542c724e19f\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: 41b3e4407d90fbe64c0bc927a9cca542c724e19f\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "6ecd751628a54446891f83bd4a0bfc83", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5165 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: 783c823d1c4b549c949e6ee07a930202a58b22f0\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: 783c823d1c4b549c949e6ee07a930202a58b22f0\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "8406594ede244a959d9330ef86c7ba75", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5159 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: ded9ec7167cb55d86c24b55efe9dadbf5b634802\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: ded9ec7167cb55d86c24b55efe9dadbf5b634802\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "16832a48a4154932b5141942401927f0", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5160 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: 627a848ba057280a48b1184056b2407435cfa82a\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: 627a848ba057280a48b1184056b2407435cfa82a\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "ebe7c89661804cb5a3dca55e37d9c8a8", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5169 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: 450f0495edae03052ff2a0f160896ea9ac04ed52\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: 450f0495edae03052ff2a0f160896ea9ac04ed52\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "2c6d073b4f0e46068b7030231681f188", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5171 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: 265a1aad73a2b4be162bb41ab8ed0bbc2ed55a30\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: 265a1aad73a2b4be162bb41ab8ed0bbc2ed55a30\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "93e7ed84f9a746c0842c54bd87f3719b", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5169 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: beba6c1dd59d8ab4f9ec904b83c3a0e912cf50df\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: beba6c1dd59d8ab4f9ec904b83c3a0e912cf50df\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "187815eb2de34b4c82e208a244c7df78", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5179 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: 0e6090151a4715447efb5f6068182238997bbb4f\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: 0e6090151a4715447efb5f6068182238997bbb4f\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "5d133530333244f2bbab5695f5e18438", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5153 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: a018f0820e9cd98375a153f6dd4dcaa7b313b3a1\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: a018f0820e9cd98375a153f6dd4dcaa7b313b3a1\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "7fc90e1858e24164b08c25ba5e2f5f12", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5165 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: 9daffc695fef103b126d046c7a4c137404548170\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: 9daffc695fef103b126d046c7a4c137404548170\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "6238c72f1003469999db4a89bbc50a35", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5176 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: f41304d82625b178465438b72c1cdaa3b7bb2b5c\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: f41304d82625b178465438b72c1cdaa3b7bb2b5c\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "9c43ff68313c4ef8b254092e9ccf2d93", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5165 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: a87b1ab203cb9bbc9136a82e81ec132d174a540d\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: a87b1ab203cb9bbc9136a82e81ec132d174a540d\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "30221f1a7f7c4a7f8d1d06d54846e6c0", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5171 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: e23ef3f01bf2acd23233d976476e28de469c1551\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: e23ef3f01bf2acd23233d976476e28de469c1551\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "9cc832d1d5754448a3494337534c9d16", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5171 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: d36d2a7021bad79122f12f1d459f60de45e9eaaf\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: d36d2a7021bad79122f12f1d459f60de45e9eaaf\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "aec90bd22b3d407eb470d8bc26c8ad4a", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5169 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: 0b38e9380cdbf041764e3465fa7947eea91c2d3b\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: 0b38e9380cdbf041764e3465fa7947eea91c2d3b\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "9739c00e6ddd40e6944a31bc42a0f6c9", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5176 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: d41ce0017aded65321a15bb9f99e65c062743a12\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: d41ce0017aded65321a15bb9f99e65c062743a12\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "5db6bdace383487f95e066d65caf441d", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5176 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: 2c32189fa31fed1785d07687b4284460d3d92950\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: 2c32189fa31fed1785d07687b4284460d3d92950\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "6fae7c9d57294ba4b4a0c78bbcdcb75b", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5176 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: c0f5d37aa8484cdad2526d43a13fcfa361df29d0\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: c0f5d37aa8484cdad2526d43a13fcfa361df29d0\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "2e79e6b7acaa4ce4bfb3a2e72bc5bf4b", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5165 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: 12b45b27e44bc8f6728fecda7816f511bb63639a\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: 12b45b27e44bc8f6728fecda7816f511bb63639a\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "cc07390f68df4f68bb3b80795c4f650f", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5169 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: b0464552b460f162ed9629dd7f70c0f7d94feba9\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: b0464552b460f162ed9629dd7f70c0f7d94feba9\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "d3d90bcb76194ec0bcd57b3fdddf92a4", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5165 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: c9ce60ecade17d56ff26d98b8f162a1ca78e5992\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: c9ce60ecade17d56ff26d98b8f162a1ca78e5992\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "9ef316be38394169ac9e793e263d0bd6", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5169 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: 557f4ed661b9db777087adbffac75b4432dc724f\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: 557f4ed661b9db777087adbffac75b4432dc724f\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "6f4d63351c1e423e98f6876d6851bb5e", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5165 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: 0f6e385917a57d96aa8a86acb44e45ffff3ae9f9\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: 0f6e385917a57d96aa8a86acb44e45ffff3ae9f9\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "4ab9ac35e88341488de38f433a744692", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5169 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: 74b8380f98b78973068fb188d98c14c536026ffc\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: 74b8380f98b78973068fb188d98c14c536026ffc\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "9884bb10358543bea6ea7a75d7b8512a", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5171 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: fdac5eb15ffac0faf65b6b6fbf46c7eebc48426e\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: fdac5eb15ffac0faf65b6b6fbf46c7eebc48426e\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "1107401e9a1a478bb54060fba23177db", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5159 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: 7479b4a9b721e304ae9e8573c5aaf17d3cb0cb83\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: 7479b4a9b721e304ae9e8573c5aaf17d3cb0cb83\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "8b36e5cd160e496aaae765b2f274033d", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5166 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: ad5e52b51bcf0c0a2be0de65198df55254134ee1\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: ad5e52b51bcf0c0a2be0de65198df55254134ee1\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "c9777a6bcec644f3b1293a57a0f2d38f", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5171 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: 896d2c78a5f3498908a5c7d079eaa74858fa6639\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: 896d2c78a5f3498908a5c7d079eaa74858fa6639\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "d7e2250d98864b21bc633607ec10ce88", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5162 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: 1728ea5b54788643bef6f2da50ce08e082a35e1e\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: 1728ea5b54788643bef6f2da50ce08e082a35e1e\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "6d56ac4dba81465c899b907517e9fbef", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5171 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: 307f1c187eee36c7c7146e3d35d14a37470f8681\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: 307f1c187eee36c7c7146e3d35d14a37470f8681\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "911b6a9990da4dd7995b2754852dc1ef", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5165 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: 1174beb7a0c1d10b8b8c7fb34af96a174abb5ad4\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: 1174beb7a0c1d10b8b8c7fb34af96a174abb5ad4\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "5b5c48dfcfee4510ac558e2f85156d9c", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5166 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: a62667f07fb6153ec007149c3165bd70a380bb4e\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: a62667f07fb6153ec007149c3165bd70a380bb4e\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "da0dd92c76da4c92a362411be1152a93", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5149 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: 511e73af9acc3e69b12a65dd94baaf3f13048406\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: 511e73af9acc3e69b12a65dd94baaf3f13048406\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "747569eb078d418ebd69becbe516e61b", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5149 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: e01ac751243cc81ce2642e7efae4857c5dacedc4\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: e01ac751243cc81ce2642e7efae4857c5dacedc4\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "54bb6453c10c42bb8441f4ac47186fed", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5165 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: c2db347c91650fb47d2e25ccb586e9b593f403fa\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: c2db347c91650fb47d2e25ccb586e9b593f403fa\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "c327eb89eb92401bb2577261c4e4ce37", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5171 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: 63e375b86f56ce42852aaba6dd9dacd232209d5b\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: 63e375b86f56ce42852aaba6dd9dacd232209d5b\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "4f116a34e26a49c68f39b98fbe24fe6c", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5159 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: bc0f3d4b64a9457811c49b7b7b5c5c792e950cdf\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: bc0f3d4b64a9457811c49b7b7b5c5c792e950cdf\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "083a235769f24fa2932aa4382620709b", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5154 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: b7b64b28241b4b5fed1cd4f516e52794e45900b2\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: b7b64b28241b4b5fed1cd4f516e52794e45900b2\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "013bfe744f734faeacdc044d86a0cf33", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5165 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: 3fbff576f38e8440013da9572e777615441e86c3\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: 3fbff576f38e8440013da9572e777615441e86c3\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "f5c0d7cf3e394503a758b2a3d86711f7", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5176 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: de946a3458a3142240c48f8e75642af9d258c6c0\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: de946a3458a3142240c48f8e75642af9d258c6c0\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "e32c0ff39bc442d49e5005a53f4c81d7", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5149 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: cb8f335e0d08ce9d7bcca6630ab3fc5cc5505fd6\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: cb8f335e0d08ce9d7bcca6630ab3fc5cc5505fd6\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "9df48c1b7da9433aa5ec6369101ceb4b", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5172 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: d82efda492784f99f0fca9497cacd988e933570b\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: d82efda492784f99f0fca9497cacd988e933570b\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "9a7d3e4757d14db487c0fd529b5f296e", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5162 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: 4dd9837ab07760903f77adea0577b35ad7dfeb5b\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: 4dd9837ab07760903f77adea0577b35ad7dfeb5b\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "dbb46259b23e49aca1cd47577baee6a2", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5171 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: d4cc3b223dcb4c898de24326ce51a0cb9572b63f\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: d4cc3b223dcb4c898de24326ce51a0cb9572b63f\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "8dc5429cbc3e4befb34f8d41a1fb3408", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5165 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: 61ed591b3e0a38dc78dfb4dad4be2edd38719fd1\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: 61ed591b3e0a38dc78dfb4dad4be2edd38719fd1\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "e0db7e8ab67c4ad6a2ec4496facc797e", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5165 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: 7916197c283c6dbe544484fcb97a0759080a6490\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: 7916197c283c6dbe544484fcb97a0759080a6490\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "efac538d66b74590901126c01bb43b87", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5169 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: 8072ba887812cc4fcec7db4bff5afa5bfc4fb69f\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: 8072ba887812cc4fcec7db4bff5afa5bfc4fb69f\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "cffca05e729a4bf28452af89751afd23", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5171 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: e2f6c7029046227c470072665373ac4dff0d0a3f\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: e2f6c7029046227c470072665373ac4dff0d0a3f\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "c8f20eb8ff414b79b588f35ab66695e6", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5171 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: 0487b6905c9da3cd3eada003b5ab67604529edba\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: 0487b6905c9da3cd3eada003b5ab67604529edba\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "53810f3283274d57bb50afb7fcdf5be0", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5171 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: 769e6835561540a2ef3e288b772791aa4ffaa73f\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: 769e6835561540a2ef3e288b772791aa4ffaa73f\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "da9eb8860f97421ba0194812922ee1b9", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5162 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: 1754ffa804151d678538dab185558070ae378bea\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: 1754ffa804151d678538dab185558070ae378bea\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "f40d834f32f345539eac0556ba6b83fd", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5176 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: bbdeb64e6cf4fc0bbcc34aaabd36c5c728729e84\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: bbdeb64e6cf4fc0bbcc34aaabd36c5c728729e84\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "c685e00cb99c43288ba044584c55ba4f", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5177 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: b816b76399712f55c3d41eae52fe8d5c38ac45ed\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: b816b76399712f55c3d41eae52fe8d5c38ac45ed\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "4cb1ee4d4bf7419988877e6ca88977a8", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5169 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: 66bcaa0c392b422565d841f44db0e36629280283\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: 66bcaa0c392b422565d841f44db0e36629280283\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "d0b40c84e3a94a96b6fa992955144701", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5149 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: a5dc315c33e718c7990ab69957edb0fa46306a94\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: a5dc315c33e718c7990ab69957edb0fa46306a94\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "eb6ee9ae2e1a407187098be7e109a167", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5159 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: 46ba2a84f10bc91ea0e43831b7ec506c43b5619b\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: 46ba2a84f10bc91ea0e43831b7ec506c43b5619b\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "ac21357ca2d54b9c909f5a4a47e58866", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5166 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: 774839be605f0b6413789f0935ce1947cc62a989\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: 774839be605f0b6413789f0935ce1947cc62a989\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "4b79c781666240398f6bb81ac4dfdbdd", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5179 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: 3567498e0a0dc922c355de5379fbf15d7a0d6e89\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: 3567498e0a0dc922c355de5379fbf15d7a0d6e89\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "52fe3955850b4e23a2e66dccdff6545f", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5179 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: 22427c3db4c14eae0193eee126312be2cf1a8cfb\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: 22427c3db4c14eae0193eee126312be2cf1a8cfb\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "24a967413f614383a7d2e3d756d9ec6a", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5165 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: efd180fc213066a5533dc244968d2c8d25d4f023\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: efd180fc213066a5533dc244968d2c8d25d4f023\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "2f7e2d533ddf45ee82737353996c45b5", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5171 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: a879339e3aaa2ee1c0a180ad39162a34ca68414a\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: a879339e3aaa2ee1c0a180ad39162a34ca68414a\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "da8a13d7ba25450e83d3f164fa2f1d9e", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5159 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: 02449e2d0f0483b1a487cfe649f577f4ec987dff\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: 02449e2d0f0483b1a487cfe649f577f4ec987dff\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "cf35e676a8924eb89f98646a3913936a", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5165 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: e254a372cec0eeb9b2285d762b8c5e440d25c884\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: e254a372cec0eeb9b2285d762b8c5e440d25c884\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "cb538f1bd37a41888676794f8f29ca47", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5165 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: 580738dea4c588df838a8dd652438c8602adf066\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: 580738dea4c588df838a8dd652438c8602adf066\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "5fabb29507084c72a2f2fb2935e19848", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5171 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: 8382463316c65ae5e0c4d1645563eb7844e153cb\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: 8382463316c65ae5e0c4d1645563eb7844e153cb\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "f0ae2090d02a46099b847aef7b623866", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5169 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: 6abce0bb9bc87f0011cfb6168738066b386465c2\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: 6abce0bb9bc87f0011cfb6168738066b386465c2\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "f3d86ad906ed476ab6f2e2be9a872a46", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5165 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: a1780e6149a49302a932e29173abca8bb6a841a8\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: a1780e6149a49302a932e29173abca8bb6a841a8\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "ca64622d88a8496a88247562382860d1", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5171 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: 8da49cee0899cb7b6f73f2aa815fa32a5bf6751c\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: 8da49cee0899cb7b6f73f2aa815fa32a5bf6751c\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "41ec69dec3e4468e9e95a2fabd2944e6", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5172 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: 27f750043cf96f4321903a55b9544c7b5f7a2a16\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: 27f750043cf96f4321903a55b9544c7b5f7a2a16\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "83c76339b57848fd9582c44d882fa798", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5171 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: e32384a362dbf95a661d6210966569abcd7a6284\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: e32384a362dbf95a661d6210966569abcd7a6284\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "eb3b9e7567ed49078bfdd57e934417a1", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5165 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: c30c39e72d07237aa2a427c6461850dbfd7d7e1b\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: c30c39e72d07237aa2a427c6461850dbfd7d7e1b\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "79b78400a012407ba42488fcb3e36e19", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5172 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: 40af04eb8b20bc06a0737807989772646971f2f8\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: 40af04eb8b20bc06a0737807989772646971f2f8\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "fb7b6fd5d328462d88559f07cee16f25", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5169 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: 208393c986ecab117ea28920e3037cc23a3bc37c\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: 208393c986ecab117ea28920e3037cc23a3bc37c\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "add7592e889a45ba85f64c9c412b3cff", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5165 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: 4c3ab5c63171c10189f09ae886509cd8b2b9108b\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: 4c3ab5c63171c10189f09ae886509cd8b2b9108b\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "e75c890c67c84008b67884c98b398e58", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5169 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: 252eedf8e59a90126195e9b9262a8589ee888b46\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: 252eedf8e59a90126195e9b9262a8589ee888b46\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "9358b9b5a6ef4612a9e57348b5a3a93b", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5172 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: bdc090f3a0cc6edf8975979118025426602a5dcf\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: bdc090f3a0cc6edf8975979118025426602a5dcf\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "f09f2dbc8f854e4197eca9d9a7ed291f", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5171 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: 74087884b9bd5f699b3e2b8cc64315b9bb3fc551\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: 74087884b9bd5f699b3e2b8cc64315b9bb3fc551\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "3276f8df1f1842eaa79fe6cf015901b8", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5171 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: 49b967c2f33d488ab16f1baa696c1f35400812c4\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: 49b967c2f33d488ab16f1baa696c1f35400812c4\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "ded13b761ebf43c88b31464939430817", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5165 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: acd64dfed193bcaaab8fede32846d1d6d433e271\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: acd64dfed193bcaaab8fede32846d1d6d433e271\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "b59c5f0cc43c4e16a931ec3b9da44ea3", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5169 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: 74783937aebd6af407f961fdc858499fa59ba4ae\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: 74783937aebd6af407f961fdc858499fa59ba4ae\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "61ee611603384d2f8ac322ab5d9e3810", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5171 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: 848250291ecf3cd7c38df01eb9f7334db0df9684\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: 848250291ecf3cd7c38df01eb9f7334db0df9684\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "175450d1b8f3416588a8e945ccb1dafa", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5176 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: 1f235e79d5e911636cb85ab8093c4464e5deb416\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: 1f235e79d5e911636cb85ab8093c4464e5deb416\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "895a61996fad4dedaade0aceaf76c432", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5179 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: edfaed1b7da1a2599dbbdcda861397b9f08101ec\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: edfaed1b7da1a2599dbbdcda861397b9f08101ec\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "0a5d7ef01b9447399016944fb53c6760", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5165 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: e08733f94c81440d19ee6a5fd5e915e9a65395f5\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: e08733f94c81440d19ee6a5fd5e915e9a65395f5\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "ac618327e2af4cc7a342513da3438968", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5154 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: e5d191c362959950f47891ed87f8fae9f41b29b0\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: e5d191c362959950f47891ed87f8fae9f41b29b0\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "5b3bd305d7384ba5921d96a6f9c4b1a1", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5171 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: 957ea5eaaa828ee6a7f96e10103748914bf27971\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: 957ea5eaaa828ee6a7f96e10103748914bf27971\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "2222f0ff06124159bda954d4840044dc", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5171 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: 45818f4e7bb375f99d2d72d169ba3f192f812150\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: 45818f4e7bb375f99d2d72d169ba3f192f812150\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "4a25d141e5c84782ab8e8d5b37594bce", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5169 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: 376c6d6dbcbe3dec41816ae1c5326ccfa6422f49\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: 376c6d6dbcbe3dec41816ae1c5326ccfa6422f49\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "6cc895a8d0b94e34a5e597f6826fe8f5", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5172 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: d44ade21b6d5f0bcd56b80ece2c17e8f200dd186\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: d44ade21b6d5f0bcd56b80ece2c17e8f200dd186\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "42ce410ce60b48be8b728b77b966cd83", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5169 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: 646d223835c20ca6da4ff7457c64adb1cf2fc77a\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: 646d223835c20ca6da4ff7457c64adb1cf2fc77a\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "7452fe85985b428caedef087312a285b", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5171 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: adb0f64315c050b1020c9916a0c38106eb555db3\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: adb0f64315c050b1020c9916a0c38106eb555db3\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "4145a02b05d54ad9ab63e058e641ba6b", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5165 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: 4b7cc6c21ae4c36c60f329bc403378c85ddea3fc\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: 4b7cc6c21ae4c36c60f329bc403378c85ddea3fc\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "ef4184aae73f4ab39fac1264db9e36a2", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5169 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: 914d96c10b7191784a4459fb56108b07e29a9421\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: 914d96c10b7191784a4459fb56108b07e29a9421\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "01ad6ecf223444f3ad584f4577f5f924", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5159 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: 2d7dbf9211206e4ca34c8b97f7f0570fde2c378a\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: 2d7dbf9211206e4ca34c8b97f7f0570fde2c378a\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "93abde8a86d24a9792e3551cbec107dd", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5171 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: 912cd6fe9317ce3f8bc31956899d780e74eff8ce\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: 912cd6fe9317ce3f8bc31956899d780e74eff8ce\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "4e3fb45c3b394a929a6574242843d9ef", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5171 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: 640879ee2c38bde6707f5db9868f9a5e9c1cc66b\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: 640879ee2c38bde6707f5db9868f9a5e9c1cc66b\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "0fad857b7605432ca12bdc9e37124d26", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/5176 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/favicon.png\n", "Commit: 3c77a007a0c6ac57ec80049abd660bc9aec3a888\n", "Project: /tmp/tmpe_a77gyt\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: lib/assets/logo.png\n", "Commit: 3c77a007a0c6ac57ec80049abd660bc9aec3a888\n", "Project: /tmp/tmpe_a77gyt\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "2cfc863ea7c8420eb035c9d9d4e46f5f", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/1189 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: angular/ssr-app/src/favicon.ico\n", "Commit: e156f9053bf682d4475d4c294db12dd991d47766\n", "Project: /tmp/tmp60_6q5kc\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: demo/src/resources/images/angular_gradient_100x100.png\n", "Commit: e156f9053bf682d4475d4c294db12dd991d47766\n", "Project: /tmp/tmp60_6q5kc\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: demo/src/resources/images/reactivity.webp\n", "Commit: e156f9053bf682d4475d4c294db12dd991d47766\n", "Project: /tmp/tmp60_6q5kc\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: demo/static/agnosui-logo.png\n", "Commit: e156f9053bf682d4475d4c294db12dd991d47766\n", "Project: /tmp/tmp60_6q5kc\n", "\n", "fatal: bad object 435e8bff224c07e4da602b1f658a70384ee38882\n", "WARNING:root:Failed to get files from commits e156f9053bf682d4475d4c294db12dd991d47766 and 435e8bff224c07e4da602b1f658a70384ee38882. Fetching now...\n", "From https://github.com/AmadeusITGroup/AgnosUI\n", " * branch              e156f9053bf682d4475d4c294db12dd991d47766 -> FETCH_HEAD\n", "From https://github.com/AmadeusITGroup/AgnosUI\n", " * branch              435e8bff224c07e4da602b1f658a70384ee38882 -> FETCH_HEAD\n", "WARNING:root:Finished fetching commits e156f9053bf682d4475d4c294db12dd991d47766 and 435e8bff224c07e4da602b1f658a70384ee38882 after 0.6 seconds.\n", "WARNING:root:Upon fetch+retry, we have successfully git diffed the commits.\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "1f5cff6f3fed4b6490053e3d5a0ab0b5", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/1197 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: angular/ssr-app/src/favicon.ico\n", "Commit: 968badc31ed3b77f3e4977e626466303f334c00b\n", "Project: /tmp/tmp9pxtv2eu\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: demo/src/resources/images/angular_gradient_100x100.png\n", "Commit: 968badc31ed3b77f3e4977e626466303f334c00b\n", "Project: /tmp/tmp9pxtv2eu\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: demo/src/resources/images/fbasso.webp\n", "Commit: 968badc31ed3b77f3e4977e626466303f334c00b\n", "Project: /tmp/tmp9pxtv2eu\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: demo/src/resources/images/reactivity.webp\n", "Commit: 968badc31ed3b77f3e4977e626466303f334c00b\n", "Project: /tmp/tmp9pxtv2eu\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: demo/static/agnosui-logo.png\n", "Commit: 968badc31ed3b77f3e4977e626466303f334c00b\n", "Project: /tmp/tmp9pxtv2eu\n", "\n", "fatal: bad object cb4def46cc421049e0645e0436ea219653f300da\n", "WARNING:root:Failed to get files from commits 968badc31ed3b77f3e4977e626466303f334c00b and cb4def46cc421049e0645e0436ea219653f300da. Fetching now...\n", "From https://github.com/AmadeusITGroup/AgnosUI\n", " * branch              968badc31ed3b77f3e4977e626466303f334c00b -> FETCH_HEAD\n", "From https://github.com/AmadeusITGroup/AgnosUI\n", " * branch              cb4def46cc421049e0645e0436ea219653f300da -> FETCH_HEAD\n", "WARNING:root:Finished fetching commits 968badc31ed3b77f3e4977e626466303f334c00b and cb4def46cc421049e0645e0436ea219653f300da after 0.7 seconds.\n", "WARNING:root:Upon fetch+retry, we have successfully git diffed the commits.\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "e771391c43a3445c98472f653dba2c39", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/421 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["fatal: bad object 46b67fcc580b9dbf0a72a1eab8a0a03d3ee547d6\n", "WARNING:root:Failed to get files from commits 891ce5559e40cbf6caae2dd737169c0c330d9f70 and 46b67fcc580b9dbf0a72a1eab8a0a03d3ee547d6. Fetching now...\n", "From https://github.com/ConSol-Lab/Pumpkin\n", " * branch              891ce5559e40cbf6caae2dd737169c0c330d9f70 -> FETCH_HEAD\n", "From https://github.com/ConSol-Lab/Pumpkin\n", " * branch              46b67fcc580b9dbf0a72a1eab8a0a03d3ee547d6 -> FETCH_HEAD\n", "WARNING:root:Finished fetching commits 891ce5559e40cbf6caae2dd737169c0c330d9f70 and 46b67fcc580b9dbf0a72a1eab8a0a03d3ee547d6 after 0.6 seconds.\n", "WARNING:root:Upon fetch+retry, we have successfully git diffed the commits.\n", "fatal: not a tree object\n", "WARNING:root:Failed to get files from commit 56d62769849c9921cf636ed6ee6242e585aae382. Fetching...\n", "From https://github.com/ConSol-Lab/Pumpkin\n", " * branch              56d62769849c9921cf636ed6ee6242e585aae382 -> FETCH_HEAD\n", "WARNING:root:Finished fetching commit 56d62769849c9921cf636ed6ee6242e585aae382 after 0.3 seconds.\n", "WARNING:root:Upon fetch+retry, we have successfully git ls-tree'd the commit.\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "5e17922bb9bf4b40a11ca28ed8550195", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/464 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["fatal: bad object 2e7b1c4f1c1d561da0a4805e19d31e2a2f90632c\n", "WARNING:root:Failed to get files from commits 56d62769849c9921cf636ed6ee6242e585aae382 and 2e7b1c4f1c1d561da0a4805e19d31e2a2f90632c. Fetching now...\n", "From https://github.com/ConSol-Lab/Pumpkin\n", " * branch              56d62769849c9921cf636ed6ee6242e585aae382 -> FETCH_HEAD\n", "From https://github.com/ConSol-Lab/Pumpkin\n", " * branch              2e7b1c4f1c1d561da0a4805e19d31e2a2f90632c -> FETCH_HEAD\n", "WARNING:root:Finished fetching commits 56d62769849c9921cf636ed6ee6242e585aae382 and 2e7b1c4f1c1d561da0a4805e19d31e2a2f90632c after 0.6 seconds.\n", "WARNING:root:Upon fetch+retry, we have successfully git diffed the commits.\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "ef869376150e413cbc7aca6bbd2b15f9", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/422 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["fatal: not a tree object\n", "WARNING:root:Failed to get files from commit 1720ec30fa837f8fab7f9cba6a5d4fbb7301d9d5. Fetching...\n", "From https://github.com/ConSol-Lab/Pumpkin\n", " * branch              1720ec30fa837f8fab7f9cba6a5d4fbb7301d9d5 -> FETCH_HEAD\n", "WARNING:root:Finished fetching commit 1720ec30fa837f8fab7f9cba6a5d4fbb7301d9d5 after 0.3 seconds.\n", "WARNING:root:Upon fetch+retry, we have successfully git ls-tree'd the commit.\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "2d6faf54ad024d55aacfe79609d5ef5f", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/473 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["fatal: bad object f9f8b7cf0d5d1ff1ced10639b28e11fe592c0e4c\n", "WARNING:root:Failed to get files from commits 1720ec30fa837f8fab7f9cba6a5d4fbb7301d9d5 and f9f8b7cf0d5d1ff1ced10639b28e11fe592c0e4c. Fetching now...\n", "From https://github.com/ConSol-Lab/Pumpkin\n", " * branch              1720ec30fa837f8fab7f9cba6a5d4fbb7301d9d5 -> FETCH_HEAD\n", "From https://github.com/ConSol-Lab/Pumpkin\n", " * branch              f9f8b7cf0d5d1ff1ced10639b28e11fe592c0e4c -> FETCH_HEAD\n", "WARNING:root:Finished fetching commits 1720ec30fa837f8fab7f9cba6a5d4fbb7301d9d5 and f9f8b7cf0d5d1ff1ced10639b28e11fe592c0e4c after 0.5 seconds.\n", "WARNING:root:Upon fetch+retry, we have successfully git diffed the commits.\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "0263894302544d10b14ad04d08c43e96", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/472 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "1721cfa020d547a4bb502d5cfb4cbbcd", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/472 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["fatal: not a tree object\n", "WARNING:root:Failed to get files from commit 4c266632f11207475b91b7ddaf3b5d4f34c4d9b8. Fetching...\n", "From https://github.com/ConSol-Lab/Pumpkin\n", " * branch              4c266632f11207475b91b7ddaf3b5d4f34c4d9b8 -> FETCH_HEAD\n", "WARNING:root:Finished fetching commit 4c266632f11207475b91b7ddaf3b5d4f34c4d9b8 after 0.3 seconds.\n", "WARNING:root:Upon fetch+retry, we have successfully git ls-tree'd the commit.\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "679749614b284949b50ed52f5552c602", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/473 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["fatal: bad object 6cf6867f348b9819e34a0c03c7c83f9011c1c4f1\n", "WARNING:root:Failed to get files from commits 4c266632f11207475b91b7ddaf3b5d4f34c4d9b8 and 6cf6867f348b9819e34a0c03c7c83f9011c1c4f1. Fetching now...\n", "From https://github.com/ConSol-Lab/Pumpkin\n", " * branch              4c266632f11207475b91b7ddaf3b5d4f34c4d9b8 -> FETCH_HEAD\n", "From https://github.com/ConSol-Lab/Pumpkin\n", " * branch              6cf6867f348b9819e34a0c03c7c83f9011c1c4f1 -> FETCH_HEAD\n", "WARNING:root:Finished fetching commits 4c266632f11207475b91b7ddaf3b5d4f34c4d9b8 and 6cf6867f348b9819e34a0c03c7c83f9011c1c4f1 after 0.6 seconds.\n", "WARNING:root:Upon fetch+retry, we have successfully git diffed the commits.\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "f4d32a9368e24f68b89a7015150ddd86", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/472 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "02d1e4746f8843f9ba3b0093b1885587", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/473 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "d07012f534884d7f85d4eae036df7a62", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/472 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "7e9a72b3177540dc9f321b6a265e0183", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/130 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: .github/images/architecture.png\n", "Commit: 9c021f8b4b6576136a0af12cf858527ad5fae3a6\n", "Project: /tmp/tmp7_y9kje7\n", "\n", "fatal: bad object 2a9257bedcb97df42fc84fe9b32c0dd65e8f3847\n", "WARNING:root:Failed to get files from commits 9c021f8b4b6576136a0af12cf858527ad5fae3a6 and 2a9257bedcb97df42fc84fe9b32c0dd65e8f3847. Fetching now...\n", "From https://github.com/CowDogMoo/ansible-collection-workstation\n", " * branch            9c021f8b4b6576136a0af12cf858527ad5fae3a6 -> FETCH_HEAD\n", "From https://github.com/CowDogMoo/ansible-collection-workstation\n", " * branch            2a9257bedcb97df42fc84fe9b32c0dd65e8f3847 -> FETCH_HEAD\n", "WARNING:root:Finished fetching commits 9c021f8b4b6576136a0af12cf858527ad5fae3a6 and 2a9257bedcb97df42fc84fe9b32c0dd65e8f3847 after 0.6 seconds.\n", "WARNING:root:Upon fetch+retry, we have successfully git diffed the commits.\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "26bf1bbdcdbf4f39a5f73ccb494a7960", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/50 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: gradle/wrapper/gradle-wrapper.jar\n", "Commit: 3406bca1972a14051028a239f03e65b8a5ae23b9\n", "Project: /tmp/tmpfokx8vrr\n", "\n", "fatal: bad object 773a9ea6cb99e0fbea545c7b740a8f7103b395e4\n", "WARNING:root:Failed to get files from commits 3406bca1972a14051028a239f03e65b8a5ae23b9 and 773a9ea6cb99e0fbea545c7b740a8f7103b395e4. Fetching now...\n", "From https://github.com/DanySK/gradle-java-qa\n", " * branch            3406bca1972a14051028a239f03e65b8a5ae23b9 -> FETCH_HEAD\n", "From https://github.com/DanySK/gradle-java-qa\n", " * branch            773a9ea6cb99e0fbea545c7b740a8f7103b395e4 -> FETCH_HEAD\n", "WARNING:root:Finished fetching commits 3406bca1972a14051028a239f03e65b8a5ae23b9 and 773a9ea6cb99e0fbea545c7b740a8f7103b395e4 after 0.8 seconds.\n", "WARNING:root:Upon fetch+retry, we have successfully git diffed the commits.\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "429ad8b4e83d436c9bba4e12184de1e3", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/50 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: gradle/wrapper/gradle-wrapper.jar\n", "Commit: 3f5b530b76dcb39d0dd5ceb043cd73dd0403e6a2\n", "Project: /tmp/tmp930qee_1\n", "\n", "fatal: bad object eab7b42e5fd58f8db5eaf2ec74911cca5414678d\n", "WARNING:root:Failed to get files from commits 3f5b530b76dcb39d0dd5ceb043cd73dd0403e6a2 and eab7b42e5fd58f8db5eaf2ec74911cca5414678d. Fetching now...\n", "From https://github.com/DanySK/gradle-java-qa\n", " * branch            3f5b530b76dcb39d0dd5ceb043cd73dd0403e6a2 -> FETCH_HEAD\n", "From https://github.com/DanySK/gradle-java-qa\n", " * branch            eab7b42e5fd58f8db5eaf2ec74911cca5414678d -> FETCH_HEAD\n", "WARNING:root:Finished fetching commits 3f5b530b76dcb39d0dd5ceb043cd73dd0403e6a2 and eab7b42e5fd58f8db5eaf2ec74911cca5414678d after 0.7 seconds.\n", "WARNING:root:Upon fetch+retry, we have successfully git diffed the commits.\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "5c8bdda5a7fe47f4ba9e4775bc47a6b3", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/50 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: gradle/wrapper/gradle-wrapper.jar\n", "Commit: 9c76fa0b2e7596f40b1690ebf3bd8b7a52d30da8\n", "Project: /tmp/tmpdnbv1goc\n", "\n", "fatal: bad object 3e4119150431832b7400636a5182bff55de6796a\n", "WARNING:root:Failed to get files from commits 9c76fa0b2e7596f40b1690ebf3bd8b7a52d30da8 and 3e4119150431832b7400636a5182bff55de6796a. Fetching now...\n", "From https://github.com/DanySK/gradle-java-qa\n", " * branch            9c76fa0b2e7596f40b1690ebf3bd8b7a52d30da8 -> FETCH_HEAD\n", "From https://github.com/DanySK/gradle-java-qa\n", " * branch            3e4119150431832b7400636a5182bff55de6796a -> FETCH_HEAD\n", "WARNING:root:Finished fetching commits 9c76fa0b2e7596f40b1690ebf3bd8b7a52d30da8 and 3e4119150431832b7400636a5182bff55de6796a after 0.7 seconds.\n", "WARNING:root:Upon fetch+retry, we have successfully git diffed the commits.\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "4e5cae11c7764d1cb37ff14a7ab008a8", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/520 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["fatal: bad object 0ff07e2279e62ad9b01b56694adfd8687ecc2e63:docs/archive\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: docs/archive\n", "Commit: 0ff07e2279e62ad9b01b56694adfd8687ecc2e63\n", "Project: /tmp/tmpi9kynkja\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: docs/manual/src/_images/glasgow-in-case.webp\n", "Commit: 0ff07e2279e62ad9b01b56694adfd8687ecc2e63\n", "Project: /tmp/tmpi9kynkja\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: docs/manual/src/_images/glasgow-pcba.webp\n", "Commit: 0ff07e2279e62ad9b01b56694adfd8687ecc2e63\n", "Project: /tmp/tmpi9kynkja\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: hardware/boards/glasgow/revA/3drender-back.png\n", "Commit: 0ff07e2279e62ad9b01b56694adfd8687ecc2e63\n", "Project: /tmp/tmpi9kynkja\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: hardware/boards/glasgow/revA/3drender-front.png\n", "Commit: 0ff07e2279e62ad9b01b56694adfd8687ecc2e63\n", "Project: /tmp/tmpi9kynkja\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: hardware/boards/glasgow/revA/fabrication.pdf\n", "Commit: 0ff07e2279e62ad9b01b56694adfd8687ecc2e63\n", "Project: /tmp/tmpi9kynkja\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: hardware/boards/glasgow/revA/glasgow-drl_map.pdf\n", "Commit: 0ff07e2279e62ad9b01b56694adfd8687ecc2e63\n", "Project: /tmp/tmpi9kynkja\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: hardware/boards/glasgow/revA/schematics.pdf\n", "Commit: 0ff07e2279e62ad9b01b56694adfd8687ecc2e63\n", "Project: /tmp/tmpi9kynkja\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: hardware/boards/glasgow/revB/3drender-back.png\n", "Commit: 0ff07e2279e62ad9b01b56694adfd8687ecc2e63\n", "Project: /tmp/tmpi9kynkja\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: hardware/boards/glasgow/revB/3drender-front.png\n", "Commit: 0ff07e2279e62ad9b01b56694adfd8687ecc2e63\n", "Project: /tmp/tmpi9kynkja\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: hardware/boards/glasgow/revB/fabrication.pdf\n", "Commit: 0ff07e2279e62ad9b01b56694adfd8687ecc2e63\n", "Project: /tmp/tmpi9kynkja\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: hardware/boards/glasgow/revB/glasgow-drl_map.pdf\n", "Commit: 0ff07e2279e62ad9b01b56694adfd8687ecc2e63\n", "Project: /tmp/tmpi9kynkja\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: hardware/boards/glasgow/revB/schematics.pdf\n", "Commit: 0ff07e2279e62ad9b01b56694adfd8687ecc2e63\n", "Project: /tmp/tmpi9kynkja\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: hardware/boards/glasgow/revC0/3drender-back.png\n", "Commit: 0ff07e2279e62ad9b01b56694adfd8687ecc2e63\n", "Project: /tmp/tmpi9kynkja\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: hardware/boards/glasgow/revC0/3drender-front.png\n", "Commit: 0ff07e2279e62ad9b01b56694adfd8687ecc2e63\n", "Project: /tmp/tmpi9kynkja\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: hardware/boards/glasgow/revC0/fabrication.pdf\n", "Commit: 0ff07e2279e62ad9b01b56694adfd8687ecc2e63\n", "Project: /tmp/tmpi9kynkja\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: hardware/boards/glasgow/revC0/glasgow-drl_map.pdf\n", "Commit: 0ff07e2279e62ad9b01b56694adfd8687ecc2e63\n", "Project: /tmp/tmpi9kynkja\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: hardware/boards/glasgow/revC0/schematics.pdf\n", "Commit: 0ff07e2279e62ad9b01b56694adfd8687ecc2e63\n", "Project: /tmp/tmpi9kynkja\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: hardware/boards/glasgow/revC1/3drender-back.png\n", "Commit: 0ff07e2279e62ad9b01b56694adfd8687ecc2e63\n", "Project: /tmp/tmpi9kynkja\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: hardware/boards/glasgow/revC1/3drender-front.png\n", "Commit: 0ff07e2279e62ad9b01b56694adfd8687ecc2e63\n", "Project: /tmp/tmpi9kynkja\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: hardware/boards/glasgow/revC1/fabrication.pdf\n", "Commit: 0ff07e2279e62ad9b01b56694adfd8687ecc2e63\n", "Project: /tmp/tmpi9kynkja\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: hardware/boards/glasgow/revC1/glasgow-drl_map.pdf\n", "Commit: 0ff07e2279e62ad9b01b56694adfd8687ecc2e63\n", "Project: /tmp/tmpi9kynkja\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: hardware/boards/glasgow/revC1/schematics.pdf\n", "Commit: 0ff07e2279e62ad9b01b56694adfd8687ecc2e63\n", "Project: /tmp/tmpi9kynkja\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: hardware/boards/glasgow/revC2/3drender-back.png\n", "Commit: 0ff07e2279e62ad9b01b56694adfd8687ecc2e63\n", "Project: /tmp/tmpi9kynkja\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: hardware/boards/glasgow/revC2/3drender-front.png\n", "Commit: 0ff07e2279e62ad9b01b56694adfd8687ecc2e63\n", "Project: /tmp/tmpi9kynkja\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: hardware/boards/glasgow/revC2/fabrication.pdf\n", "Commit: 0ff07e2279e62ad9b01b56694adfd8687ecc2e63\n", "Project: /tmp/tmpi9kynkja\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: hardware/boards/glasgow/revC2/glasgow-drl_map.pdf\n", "Commit: 0ff07e2279e62ad9b01b56694adfd8687ecc2e63\n", "Project: /tmp/tmpi9kynkja\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: hardware/boards/glasgow/revC2/schematics.pdf\n", "Commit: 0ff07e2279e62ad9b01b56694adfd8687ecc2e63\n", "Project: /tmp/tmpi9kynkja\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: hardware/boards/glasgow/revC3/3drender-back.png\n", "Commit: 0ff07e2279e62ad9b01b56694adfd8687ecc2e63\n", "Project: /tmp/tmpi9kynkja\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: hardware/boards/glasgow/revC3/3drender-front.png\n", "Commit: 0ff07e2279e62ad9b01b56694adfd8687ecc2e63\n", "Project: /tmp/tmpi9kynkja\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: hardware/boards/glasgow/revC3/fabrication.pdf\n", "Commit: 0ff07e2279e62ad9b01b56694adfd8687ecc2e63\n", "Project: /tmp/tmpi9kynkja\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: hardware/boards/glasgow/revC3/glasgow-drl_map.pdf\n", "Commit: 0ff07e2279e62ad9b01b56694adfd8687ecc2e63\n", "Project: /tmp/tmpi9kynkja\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: hardware/boards/glasgow/revC3/schematics.pdf\n", "Commit: 0ff07e2279e62ad9b01b56694adfd8687ecc2e63\n", "Project: /tmp/tmpi9kynkja\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: hardware/boards/test-jig/pictures/render-bottom.jpg\n", "Commit: 0ff07e2279e62ad9b01b56694adfd8687ecc2e63\n", "Project: /tmp/tmpi9kynkja\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: hardware/boards/test-jig/pictures/render-leds.jpg\n", "Commit: 0ff07e2279e62ad9b01b56694adfd8687ecc2e63\n", "Project: /tmp/tmpi9kynkja\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: hardware/boards/test-jig/pictures/render-top1.jpg\n", "Commit: 0ff07e2279e62ad9b01b56694adfd8687ecc2e63\n", "Project: /tmp/tmpi9kynkja\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: hardware/boards/test-jig/pictures/render-top2.jpg\n", "Commit: 0ff07e2279e62ad9b01b56694adfd8687ecc2e63\n", "Project: /tmp/tmpi9kynkja\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: hardware/boards/test-jig/schematics.pdf\n", "Commit: 0ff07e2279e62ad9b01b56694adfd8687ecc2e63\n", "Project: /tmp/tmpi9kynkja\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: hardware/cases/3d-printed-base/Glasgow_base.f3d\n", "Commit: 0ff07e2279e62ad9b01b56694adfd8687ecc2e63\n", "Project: /tmp/tmpi9kynkja\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: hardware/cases/3d-printed-base/Glasgow_base.png\n", "Commit: 0ff07e2279e62ad9b01b56694adfd8687ecc2e63\n", "Project: /tmp/tmpi9kynkja\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: hardware/cases/3d-printed-base/Glasgow_base.stl\n", "Commit: 0ff07e2279e62ad9b01b56694adfd8687ecc2e63\n", "Project: /tmp/tmpi9kynkja\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: hardware/cases/3d-printed-magnetic-lid/glasgow_magnetic_lid.f3z\n", "Commit: 0ff07e2279e62ad9b01b56694adfd8687ecc2e63\n", "Project: /tmp/tmpi9kynkja\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: hardware/cases/3d-printed-magnetic-lid/glasgow_magnetic_lid.jpg\n", "Commit: 0ff07e2279e62ad9b01b56694adfd8687ecc2e63\n", "Project: /tmp/tmpi9kynkja\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: hardware/cases/3d-printed-magnetic-lid/glasgow_magnetic_lid.stl\n", "Commit: 0ff07e2279e62ad9b01b56694adfd8687ecc2e63\n", "Project: /tmp/tmpi9kynkja\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: hardware/cases/laser-cut/bottom.slvs\n", "Commit: 0ff07e2279e62ad9b01b56694adfd8687ecc2e63\n", "Project: /tmp/tmpi9kynkja\n", "\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: hardware/cases/laser-cut/top.slvs\n", "Commit: 0ff07e2279e62ad9b01b56694adfd8687ecc2e63\n", "Project: /tmp/tmpi9kynkja\n", "\n", "fatal: bad object 0ff07e2279e62ad9b01b56694adfd8687ecc2e63:vendor/libfx2\n", "ERROR:root:Failed to decode file from git. Returning an empty string.File: vendor/libfx2\n", "Commit: 0ff07e2279e62ad9b01b56694adfd8687ecc2e63\n", "Project: /tmp/tmpi9kynkja\n", "\n", "fatal: bad object 38a81ff86237529832e0fb5a105f42abb6b2fd10\n", "WARNING:root:Failed to get files from commits 0ff07e2279e62ad9b01b56694adfd8687ecc2e63 and 38a81ff86237529832e0fb5a105f42abb6b2fd10. Fetching now...\n", "From https://github.com/GlasgowEmbedded/glasgow\n", " * branch            0ff07e2279e62ad9b01b56694adfd8687ecc2e63 -> FETCH_HEAD\n", "From https://github.com/GlasgowEmbedded/glasgow\n", " * branch            38a81ff86237529832e0fb5a105f42abb6b2fd10 -> FETCH_HEAD\n", "WARNING:root:Finished fetching commits 0ff07e2279e62ad9b01b56694adfd8687ecc2e63 and 38a81ff86237529832e0fb5a105f42abb6b2fd10 after 0.6 seconds.\n", "WARNING:root:Upon fetch+retry, we have successfully git diffed the commits.\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "cf19d49d3b6043eb8d456126ede215ea", "version_major": 2, "version_minor": 0}, "text/plain": ["building repo:   0%|          | 0/43 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:root:Failed to decode file from git. Returning an empty string.File: example.png\n", "Commit: 32073c33eafa8692c1dd44b24caab5c24bc8d408\n", "Project: /tmp/tmpi73p1ywa\n", "\n", "fatal: bad object ce176ff221a63ff72b9dceaceefbf62a9ea5a77e\n", "WARNING:root:Failed to get files from commits 32073c33eafa8692c1dd44b24caab5c24bc8d408 and ce176ff221a63ff72b9dceaceefbf62a9ea5a77e. Fetching now...\n", "From https://github.com/HartD92/ha-cos-utilities\n", " * branch            32073c33eafa8692c1dd44b24caab5c24bc8d408 -> FETCH_HEAD\n", "From https://github.com/HartD92/ha-cos-utilities\n", " * branch            ce176ff221a63ff72b9dceaceefbf62a9ea5a77e -> FETCH_HEAD\n", "WARNING:root:Finished fetching commits 32073c33eafa8692c1dd44b24caab5c24bc8d408 and ce176ff221a63ff72b9dceaceefbf62a9ea5a77e after 0.6 seconds.\n", "WARNING:root:Upon fetch+retry, we have successfully git diffed the commits.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["rows_processed: 118, rows_tarballs_missing: 14\n", "Num repos: 15\n", "Num PRs: 25\n", "Num commits: 118\n"]}, {"ename": "TypeError", "evalue": "CoreBPE.encode() takes 2 positional arguments but 3 were given", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mTypeError\u001b[0m                                 Traceback (most recent call last)", "Cell \u001b[0;32mIn[1], line 150\u001b[0m\n\u001b[1;32m    148\u001b[0m     repo_id_to_pr_number_to_diff_token_cts[repo_ID][pr_number] \u001b[38;5;241m=\u001b[39m []\n\u001b[1;32m    149\u001b[0m     \u001b[38;5;28;01mfor\u001b[39;00m diff \u001b[38;5;129;01min\u001b[39;00m diffs:\n\u001b[0;32m--> 150\u001b[0m       repo_id_to_pr_number_to_diff_token_cts[repo_ID][pr_number]\u001b[38;5;241m.\u001b[39mappend(\u001b[38;5;28mlen\u001b[39m(\u001b[43mtok<PERSON><PERSON>\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mtokenize_safe\u001b[49m\u001b[43m(\u001b[49m\u001b[43mdiff\u001b[49m\u001b[43m)\u001b[49m))\n\u001b[1;32m    151\u001b[0m       token_cts\u001b[38;5;241m.\u001b[39mappend(\u001b[38;5;28mlen\u001b[39m(tokenizer\u001b[38;5;241m.\u001b[39mtokenize_safe(diff)))\n\u001b[1;32m    153\u001b[0m median_token_ct \u001b[38;5;241m=\u001b[39m np\u001b[38;5;241m.\u001b[39mmedian(token_cts)\n", "File \u001b[0;32m~/augment/base/tokenizers/tiktoken_starcoder_tokenizer.py:136\u001b[0m, in \u001b[0;36mStarCoderTokenizer.tokenize_safe\u001b[0;34m(self, text)\u001b[0m\n\u001b[1;32m    134\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mtokenize_safe\u001b[39m(\u001b[38;5;28mself\u001b[39m, text: \u001b[38;5;28mstr\u001b[39m) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m \u001b[38;5;28mlist\u001b[39m[\u001b[38;5;28mint\u001b[39m]:\n\u001b[1;32m    135\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"Tokenizes a text string into a list of tokens.\"\"\"\u001b[39;00m\n\u001b[0;32m--> 136\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcore_bpe\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mencode\u001b[49m\u001b[43m(\u001b[49m\u001b[43mtext\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mallowed_special\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mset\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/augment/base/tokenizers/core_bpe.py:72\u001b[0m, in \u001b[0;36mCoreBPE.encode\u001b[0;34m(self, text, allowed_special)\u001b[0m\n\u001b[1;32m     61\u001b[0m \u001b[38;5;250m\u001b[39m\u001b[38;5;124;03m\"\"\"Encode a text into a set of tokens.\u001b[39;00m\n\u001b[1;32m     62\u001b[0m \n\u001b[1;32m     63\u001b[0m \u001b[38;5;124;03mArgs:\u001b[39;00m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m     69\u001b[0m \u001b[38;5;124;03m    Token ids for the encoded text.\u001b[39;00m\n\u001b[1;32m     70\u001b[0m \u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m     71\u001b[0m context \u001b[38;5;241m=\u001b[39m feature_flags\u001b[38;5;241m.\u001b[39mget_global_context()\n\u001b[0;32m---> 72\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_core_bpe\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mencode\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m     73\u001b[0m \u001b[43m    \u001b[49m\u001b[43mtext\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m     74\u001b[0m \u001b[43m    \u001b[49m\u001b[43mallowed_special\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m     75\u001b[0m \u001b[43m    \u001b[49m\u001b[43m_FORCE_REGEX_PRETOKENIZER\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[43mcontext\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m     76\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[0;31mTypeError\u001b[0m: CoreBPE.encode() takes 2 positional arguments but 3 were given"]}], "source": ["from research.data.utils.pr_v2 import get_tar_full_path\n", "from research.utils.repo_change_utils import iterate_repo_history\n", "from research.utils.repo_change_utils import RepoChange\n", "from research.utils.repo_change_utils import CommitMeta\n", "import tempfile\n", "from pathlib import Path\n", "import tarfile\n", "import pyspark.sql.functions as F\n", "from research.utils.repo_change_utils import FileTuple\n", "from base.diff_utils.diff_formatter import (\n", "    format_file_changes_with_ranges,\n", ")\n", "import numpy as np\n", "\n", "NUM_PRS_TO_PROCESS = 25\n", "NUM_REPOS_TO_SAMPLE = 500\n", "\n", "from research.data.spark.utils import k8s_session\n", "\n", "spark = k8s_session(\n", "    max_workers=100,\n", "    conf={\n", "        \"spark.sql.parquet.columnarReaderBatchSize\": \"64\",\n", "        \"spark.sql.execution.arrow.maxRecordsPerBatch\": \"128\",\n", "        \"spark.driver.maxResultSize\": \"25g\",\n", "        \"spark.task.maxFailures\": \"10\",\n", "    },\n", ")\n", "data = spark.read.parquet(\"gs://gcp-us1-spark-data/shared/pr_v2/inter_commits\")\n", "\n", "###\n", "# STEP 1: Sample 500 REPOS and group by PR number\n", "# Artifact is: data (schema: repo_id, pr_number, pr_info)\n", "###\n", "data = data.withColumn(\"repo_id\", <PERSON><PERSON>concat(F.col(\"owner\"), F<PERSON>lit(\"/\"), <PERSON>.col(\"name\")))\n", "# sample 500 repos based on repo_id\n", "sampled_repos = (\n", "    data.select(\"repo_id\")\n", "    .distinct()\n", "    .orderBy(<PERSON><PERSON>rand())\n", "    .limit(NUM_REPOS_TO_SAMPLE)\n", "    .toPandas()[\"repo_id\"]\n", "    .tolist()\n", ")\n", "data = data.filter(data[\"repo_id\"].isin(sampled_repos))\n", "# group by pr_number and collect all relevant info\n", "data = data.groupBy(\"repo_id\", \"pr_number\").agg(\n", "    F.collect_list(F.struct(data.columns)).alias(\"pr_info\")\n", ")\n", "data = data.limit(NUM_PRS_TO_PROCESS).toPandas()\n", "\n", "\n", "###\n", "# STEP 2: Load up the repos and create RepoChange objects\n", "# Artifact is: repo_id_to_pr_number_to_repo_change_objs (schema: repo_id -> pr_number -> list[(commit_info, RepoChange)])\n", "###\n", "repo_id_to_pr_number_to_repo_change_objs: dict[\n", "    str, dict[str, list[tuple[dict, <PERSON><PERSON><PERSON><PERSON><PERSON>]]]\n", "] = dict()\n", "rows_processed = 0\n", "rows_tarballs_missing = 0\n", "for _, row in data.iterrows():\n", "    repo_ID = row[\"repo_id\"]\n", "    pr_number = row[\"pr_number\"]\n", "\n", "    if repo_ID not in repo_id_to_pr_number_to_repo_change_objs:\n", "        repo_id_to_pr_number_to_repo_change_objs[repo_ID] = dict()\n", "    if pr_number not in repo_id_to_pr_number_to_repo_change_objs[repo_ID]:\n", "        repo_id_to_pr_number_to_repo_change_objs[repo_ID][pr_number] = []\n", "\n", "    # load up repo in /tmp\n", "    repo_tarball_path = get_tar_full_path(repo_ID)\n", "    with tempfile.TemporaryDirectory() as temp_dir:\n", "        temp_path = Path(temp_dir)\n", "        try:\n", "            with tarfile.open(repo_tarball_path, \"r:*\") as tar:\n", "                tar.extractall(path=temp_path)\n", "        except FileNotFoundError:\n", "            rows_tarballs_missing += 1\n", "            continue\n", "\n", "        # process commits\n", "        for commit_info in row[\"pr_info\"]:\n", "            rows_processed += 1\n", "            if not commit_info[\"parents\"]:\n", "                continue\n", "            parent_commit_meta = CommitMeta(\n", "                sha=commit_info[\"parents\"][0],\n", "                parents=[],\n", "                children=[commit_info[\"sha\"]],\n", "                message=\"\",\n", "                repo_name=repo_ID,\n", "            )\n", "            child_commit_meta = CommitMeta(\n", "                sha=commit_info[\"sha\"],\n", "                parents=[commit_info[\"parents\"][0]],\n", "                children=[],\n", "                message=\"\",\n", "                repo_name=repo_ID,\n", "            )\n", "            repo_change_objs = list(\n", "                iterate_repo_history(\n", "                    project_dir=temp_path,\n", "                    history=[parent_commit_meta, child_commit_meta],\n", "                    is_source_file=lambda x: True,\n", "                )\n", "            )\n", "            repo_id_to_pr_number_to_repo_change_objs[repo_ID][pr_number].append(\n", "                (commit_info.asDict(), repo_change_objs)\n", "            )\n", "\n", "###\n", "# STEP 3: Compute initial stats and extract diffs from RepoChange objects\n", "# Artifact is: some stats are printed, and then repo_id_to_pr_number_to_diffs (schema: repo_id -> pr_number -> list[str])\n", "###\n", "print(\n", "    f\"rows_processed: {rows_processed}, rows_tarballs_missing: {rows_tarballs_missing}\"\n", ")\n", "print(f\"Num repos: {len(repo_id_to_pr_number_to_repo_change_objs)}\")\n", "num_prs = 0\n", "for (\n", "    repo_ID,\n", "    pr_number_to_repo_change_objs,\n", ") in repo_id_to_pr_number_to_repo_change_objs.items():\n", "    num_prs += len(pr_number_to_repo_change_objs)\n", "print(f\"Num PRs: {num_prs}\")\n", "num_commits = 0\n", "for (\n", "    repo_ID,\n", "    pr_number_to_repo_change_objs,\n", ") in repo_id_to_pr_number_to_repo_change_objs.items():\n", "    for pr_number, repo_change_objs in pr_number_to_repo_change_objs.items():\n", "        num_commits += len(repo_change_objs)\n", "print(f\"Num commits: {num_commits}\")\n", "\n", "repo_id_to_pr_number_to_diffs = dict()\n", "for (\n", "    repo_ID,\n", "    pr_number_to_repo_change_objs,\n", ") in repo_id_to_pr_number_to_repo_change_objs.items():\n", "    repo_id_to_pr_number_to_diffs[repo_ID] = dict()\n", "    for pr_number, repo_change_objs in pr_number_to_repo_change_objs.items():\n", "        repo_id_to_pr_number_to_diffs[repo_ID][pr_number] = []\n", "        for commit_info, repo_change_obj in repo_change_objs:\n", "            file_changes = [\n", "                change.map(FileTuple.to_file)\n", "                for change in repo_change_obj[0].changed_files\n", "            ]\n", "            diff_hunks = format_file_changes_with_ranges(\n", "                changes=file_changes,\n", "                diff_context_lines=3,\n", "                use_smart_header=True,\n", "            )\n", "            diff_str = \"\"\n", "            for hunk in reversed(diff_hunks):\n", "                body_text = hunk.text\n", "                header_text = hunk.path_header(deduplicate_identical_paths=True)\n", "                diff_str += header_text + body_text\n", "\n", "            repo_id_to_pr_number_to_diffs[repo_ID][pr_number].append(diff_str)\n", "\n", "###\n", "# STEP 4: Compute token counts for each diff\n", "# Artifact is: repo_id_to_pr_number_to_diff_token_cts (schema: repo_id -> pr_number -> list[int])\n", "###\n", "from base.tokenizers.tiktoken_starcoder_tokenizer import (\n", "    TiktokenStarCoderTokenizer as StarCoderTokenizer,\n", ")\n", "\n", "tokenizer = StarCoderTokenizer()\n", "repo_id_to_pr_number_to_diff_token_cts = dict()\n", "token_cts = []\n", "for repo_ID, pr_number_to_diffs in repo_id_to_pr_number_to_diffs.items():\n", "    repo_id_to_pr_number_to_diff_token_cts[repo_ID] = dict()\n", "    for pr_number, diffs in pr_number_to_diffs.items():\n", "        repo_id_to_pr_number_to_diff_token_cts[repo_ID][pr_number] = []\n", "        for diff in diffs:\n", "            repo_id_to_pr_number_to_diff_token_cts[repo_ID][pr_number].append(\n", "                len(tokenizer.tokenize_safe(diff))\n", "            )\n", "            token_cts.append(len(tokenizer.tokenize_safe(diff)))\n", "\n", "median_token_ct = np.median(token_cts)\n", "perc25_token_ct = np.percentile(token_cts, 25)\n", "perc75_token_ct = np.percentile(token_cts, 75)\n", "print(f\"perc25_token_ct: {perc25_token_ct}\")\n", "print(f\"median_token_ct: {median_token_ct}\")\n", "print(f\"perc75_token_ct: {perc75_token_ct}\")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}