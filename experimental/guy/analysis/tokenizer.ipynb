{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["# Tokenization notebook"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["==================================================\n", "Original text:\n", "\n", "def foo():\n", "    print(\"Hello, <PERSON>!\")\n", "\n", "    for xyz in range(-100, 100):\n", "        foo = xyz * 2 + 5\n", "        print(\"This is foo:\", foo)\n", "        if foo > 0:\n", "            print(\"It's positive!\")\n", "\n", "==================================================\n", "\n", "Tokenized:\n", "[198, 4299, 22944, 33529, 198, 50284, 4798, 7203, 15496, 11, 2159, 2474, 8, 628, 50284, 1640, 2124, 45579, 287, 2837, 32590, 3064, 11, 1802, 2599, 198, 50280, 21943, 796, 2124, 45579, 1635, 362, 1343, 642, 198, 50280, 4798, 7203, 1212, 318, 22944, 25, 1600, 22944, 8, 198, 50280, 361, 22944, 1875, 657, 25, 198, 50276, 4798, 7203, 1026, 338, 3967, 2474, 8, 198]\n", "['\\\\n', 'def', '_foo', '():', '\\\\n', '    ', 'print', '(\"', 'Hello', ',', '_World', '!\"', ')', '\\\\n\\\\n', '    ', 'for', '_x', 'yz', '_in', '_range', '(-', '100', ',', '_100', '):', '\\\\n', '        ', 'foo', '_=', '_x', 'yz', '_*', '_2', '_+', '_5', '\\\\n', '        ', 'print', '(\"', 'This', '_is', '_foo', ':', '\",', '_foo', ')', '\\\\n', '        ', 'if', '_foo', '_>', '_0', ':', '\\\\n', '            ', 'print', '(\"', 'It', \"'s\", '_positive', '!\"', ')', '\\\\n']\n"]}], "source": ["#\n", "# This is different from the tokenizer we use, and it mangles newlines.\n", "#\n", "\n", "# from megatron.tokenizer.tokenizer import CodeGenTokenizer\n", "from transformers import CodeGenTokenizer, AutoTokenizer\n", "#tokenizer = CodeGenTokenizer.from_pretrained(\"Salesforce/codegen-350M-multi\")\n", "tokenizer = AutoTokenizer.from_pretrained(\"Salesforce/codegen-350M-multi\")\n", "\n", "text = \"\"\"\n", "def foo():\n", "    print(\"Hello, <PERSON>!\")\n", "\n", "    for xyz in range(-100, 100):\n", "        foo = xyz * 2 + 5\n", "        print(\"This is foo:\", foo)\n", "        if foo > 0:\n", "            print(\"It's positive!\")\n", "\"\"\"\n", "\n", "print(\"=\" * 50)\n", "print(\"Original text:\")\n", "print(text)\n", "print(\"=\" * 50)\n", "\n", "def prettify(token):\n", "    return token.replace('Ċ', '\\\\n').replace('Ġ', '_')\n", "\n", "tokens = tokenizer.encode(text)\n", "print(\"\\nTokenized:\")\n", "print(tokens)\n", "tokens = tokenizer.tokenize(text)\n", "print([prettify(t) for t in tokenizer.tokenize(text)])\n", "\n", "# print(\"=\" * 50)\n", "# decoded = tokenizer.decode(tokens)\n", "# print(\"Decoded:\")\n", "# print(decoded)\n", "# print(\"=\" * 50)"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Using cls_token, but it is not set yet.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Help on NoneType object:\n", "\n", "class NoneType(object)\n", " |  Methods defined here:\n", " |  \n", " |  __bool__(self, /)\n", " |      True if self else False\n", " |  \n", " |  __repr__(self, /)\n", " |      Return repr(self).\n", " |  \n", " |  ----------------------------------------------------------------------\n", " |  Static methods defined here:\n", " |  \n", " |  __new__(*args, **kwargs) from builtins.type\n", " |      Create and return a new object.  See help(type) for accurate signature.\n", "\n"]}], "source": ["help(tokenizer.cls_token)"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"text/plain": ["['SPECIAL_TOKENS_ATTRIBUTES',\n", " '__annotations__',\n", " '__call__',\n", " '__class__',\n", " '__delattr__',\n", " '__dict__',\n", " '__dir__',\n", " '__doc__',\n", " '__eq__',\n", " '__format__',\n", " '__ge__',\n", " '__getattribute__',\n", " '__gt__',\n", " '__hash__',\n", " '__init__',\n", " '__init_subclass__',\n", " '__le__',\n", " '__len__',\n", " '__lt__',\n", " '__module__',\n", " '__ne__',\n", " '__new__',\n", " '__reduce__',\n", " '__reduce_ex__',\n", " '__repr__',\n", " '__setattr__',\n", " '__sizeof__',\n", " '__str__',\n", " '__subclasshook__',\n", " '__weakref__',\n", " '_add_tokens',\n", " '_additional_special_tokens',\n", " '_auto_class',\n", " '_batch_encode_plus',\n", " '_bos_token',\n", " '_call_one',\n", " '_cls_token',\n", " '_convert_encoding',\n", " '_convert_id_to_token',\n", " '_convert_token_to_id_with_added_voc',\n", " '_create_repo',\n", " '_decode',\n", " '_decode_use_source_tokenizer',\n", " '_encode_plus',\n", " '_eos_token',\n", " '_eventual_warn_about_too_long_sequence',\n", " '_eventually_correct_t5_max_length',\n", " '_from_pretrained',\n", " '_get_files_timestamps',\n", " '_get_padding_truncation_strategies',\n", " '_in_target_context_manager',\n", " '_mask_token',\n", " '_pad',\n", " '_pad_token',\n", " '_pad_token_type_id',\n", " '_processor_class',\n", " '_save_pretrained',\n", " '_sep_token',\n", " '_set_processor_class',\n", " '_switch_to_input_mode',\n", " '_switch_to_target_mode',\n", " '_tokenizer',\n", " '_unk_token',\n", " '_upload_modified_files',\n", " 'add_prefix_space',\n", " 'add_special_tokens',\n", " 'add_tokens',\n", " 'additional_special_tokens',\n", " 'additional_special_tokens_ids',\n", " 'all_special_ids',\n", " 'all_special_tokens',\n", " 'all_special_tokens_extended',\n", " 'as_target_tokenizer',\n", " 'backend_tokenizer',\n", " 'batch_decode',\n", " 'batch_encode_plus',\n", " 'bos_token',\n", " 'bos_token_id',\n", " 'build_inputs_with_special_tokens',\n", " 'can_save_slow_tokenizer',\n", " 'clean_up_tokenization',\n", " 'cls_token',\n", " 'cls_token_id',\n", " 'convert_ids_to_tokens',\n", " 'convert_tokens_to_ids',\n", " 'convert_tokens_to_string',\n", " 'create_token_type_ids_from_sequences',\n", " 'decode',\n", " 'decoder',\n", " 'deprecation_warnings',\n", " 'encode',\n", " 'encode_plus',\n", " 'eos_token',\n", " 'eos_token_id',\n", " 'from_pretrained',\n", " 'get_added_vocab',\n", " 'get_special_tokens_mask',\n", " 'get_vocab',\n", " 'init_inputs',\n", " 'init_kwargs',\n", " 'is_fast',\n", " 'mask_token',\n", " 'mask_token_id',\n", " 'max_len_sentences_pair',\n", " 'max_len_single_sentence',\n", " 'max_model_input_sizes',\n", " 'model_input_names',\n", " 'model_max_length',\n", " 'name_or_path',\n", " 'num_special_tokens_to_add',\n", " 'pad',\n", " 'pad_token',\n", " 'pad_token_id',\n", " 'pad_token_type_id',\n", " 'padding_side',\n", " 'prepare_for_model',\n", " 'prepare_seq2seq_batch',\n", " 'pretrained_init_configuration',\n", " 'pretrained_vocab_files_map',\n", " 'push_to_hub',\n", " 'register_for_auto_class',\n", " 'sanitize_special_tokens',\n", " 'save_pretrained',\n", " 'save_vocabulary',\n", " 'sep_token',\n", " 'sep_token_id',\n", " 'set_truncation_and_padding',\n", " 'slow_tokenizer_class',\n", " 'special_tokens_map',\n", " 'special_tokens_map_extended',\n", " 'tokenize',\n", " 'train_new_from_iterator',\n", " 'truncate',\n", " 'truncate_sequences',\n", " 'truncation_side',\n", " 'unk_token',\n", " 'unk_token_id',\n", " 'verbose',\n", " 'vocab',\n", " 'vocab_files_names',\n", " 'vocab_size']"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["dir(tokenizer)"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"data": {"text/plain": ["['<|endoftext|>']"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["tokenizer.all_special_tokens_extended"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"data": {"text/plain": ["'<|endoftext|>'"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["tokenizer.unk_token"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"data": {"text/plain": ["50257"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["tokenizer.vocab_size"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'Ġooz': 49564,\n", " 'Ġactivism': 23034,\n", " 'stat': 14269,\n", " 'ĠJesse': 18033,\n", " 'arious': 27129,\n", " 'ĠRecent': 22926,\n", " 'ĠCelest': 47245,\n", " 'cher': 2044,\n", " 'Ġpassages': 22674,\n", " 'Ġsamples': 8405,\n", " 'asp': 5126,\n", " 'Ġcheat': 22705,\n", " 'Ġtun': 6278,\n", " 'NE': 12161,\n", " 'Ġlookup': 35847,\n", " 'Ġmorph': 17488,\n", " 'Ġfought': 8350,\n", " 'ĠLight': 4401,\n", " 'ĠAthena': 21341,\n", " 'Ġpardon': 27322,\n", " 'ĠPass': 6251,\n", " 'Ġwalks': 11114,\n", " 'Ġdesperately': 16459,\n", " 'desc': 20147,\n", " 'Ġcrisis': 4902,\n", " 'Ġcounselors': 44135,\n", " 'ĠSchr': 49871,\n", " 'Ġworsh': 26511,\n", " 'Ġaven': 27968,\n", " 'affected': 43958,\n", " 'ndra': 24631,\n", " 'ĠSang': 30043,\n", " 'ush': 1530,\n", " 'Ġtrying': 2111,\n", " 'Ġlanes': 15296,\n", " 'Ġhollow': 20596,\n", " 'idepress': 25895,\n", " 'cloneembedreportprint': 30899,\n", " \"'),\": 33809,\n", " 'Ġhappen': 1645,\n", " 'Ġrift': 36788,\n", " 'izers': 11341,\n", " 'ZI': 48926,\n", " 'ĠMiranda': 29575,\n", " 'Ġbrief': 4506,\n", " 'Ġcarcin': 28164,\n", " 'Ġholes': 10421,\n", " 'Fund': 24553,\n", " 'Lady': 38887,\n", " 'Order': 18743,\n", " 'phabet': 19557,\n", " 'Ġdental': 22727,\n", " 'ili': 2403,\n", " 'unda': 46535,\n", " 'ĠLonely': 46501,\n", " 'paralle': 37083,\n", " 'boy': 7081,\n", " 'Ġedits': 31671,\n", " 'Ġintelligence': 4430,\n", " 'ĠWhether': 10127,\n", " 'Ġpeaks': 25740,\n", " 'more': 3549,\n", " 'ĠâĪĴ': 9746,\n", " 'Ġoverflowing': 43347,\n", " 'ĠBowl': 8693,\n", " 'ĠAsc': 29469,\n", " 'verty': 8077,\n", " 'French': 24111,\n", " 'ĠTrou': 22141,\n", " 'ĠWalls': 40699,\n", " 'Ġprinciples': 7811,\n", " 'urst': 24962,\n", " 'ĠMushroom': 36482,\n", " 'der': 1082,\n", " 'ĠManhattan': 13458,\n", " 'ĠDHS': 29987,\n", " 'Ger': 38069,\n", " 'ĠDiscover': 29704,\n", " 'Ble': 43413,\n", " 'ĠAssembly': 10006,\n", " 'PLAY': 31519,\n", " 'ĠPapa': 42328,\n", " 'efe': 22521,\n", " 'Ġbroadband': 18729,\n", " 'Ġpods': 37185,\n", " 'Ġworkers': 3259,\n", " 'Ġinvolving': 7411,\n", " 'ĠWOR': 21881,\n", " 'Ġprojected': 13301,\n", " 'ĠAllow': 22507,\n", " 'ĠRepl': 18407,\n", " 'Ġscrim': 32157,\n", " 'Ġassigning': 38875,\n", " 'Ġrematch': 39810,\n", " 'ĠDHCP': 43729,\n", " 'Usage': 28350,\n", " 'Ġlegalization': 23050,\n", " 'dropping': 37554,\n", " 'Ġmafia': 44371,\n", " 'Ġtool': 2891,\n", " 'ĠMad': 4627,\n", " 'Ġstates': 2585,\n", " 'places': 23625,\n", " 'govern': 47866,\n", " 'Ġbriefs': 50011,\n", " 'Ġfloats': 36016,\n", " 'ĠLabrador': 45246,\n", " 'Ġconducive': 45645,\n", " 'ĠWilhelm': 50031,\n", " '17': 1558,\n", " 'WM': 22117,\n", " 'Image': 5159,\n", " 'ĠLiquid': 21020,\n", " 'Ġbob': 29202,\n", " 'ĠMissouri': 11565,\n", " 'Ġeasier': 4577,\n", " 'ĠAfric': 3295,\n", " 'criptions': 24370,\n", " 'Ġtake': 1011,\n", " 'rior': 7701,\n", " 'erver': 18497,\n", " 'take': 20657,\n", " 'Ġaffects': 10975,\n", " 'Ġharmony': 22471,\n", " 'OO': 6684,\n", " 'Ġdifferentiate': 28754,\n", " 'ubric': 29812,\n", " 'Ġdisguised': 32192,\n", " 'ĠBas': 6455,\n", " 'Ġdunk': 35434,\n", " 'ĠLoading': 12320,\n", " 'Ġstorytelling': 23689,\n", " 'racuse': 28268,\n", " 'Ġ271': 33797,\n", " 'establish': 40037,\n", " 'itimate': 30233,\n", " 'ĠWeek': 6119,\n", " 'ĠPhotography': 32461,\n", " 'Ġinstituted': 32954,\n", " 'Ġwinding': 28967,\n", " 'hess': 33979,\n", " 'merce': 11647,\n", " 'aying': 8369,\n", " 'Ġcoincide': 37319,\n", " 'Ġcontraceptive': 37832,\n", " 'ĠFn': 37481,\n", " 'ĠDOT': 42743,\n", " 'books': 12106,\n", " 'ĠCandy': 24680,\n", " 'ĠMorse': 44049,\n", " 'EPA': 40906,\n", " 'ocyte': 43320,\n", " 'Ġrestrooms': 48537,\n", " 'ĠStarts': 50181,\n", " 'ĠPupp': 20926,\n", " 'Ġlev': 23145,\n", " 'ĠProtective': 45004,\n", " 'ĠGerm': 14164,\n", " 'processing': 36948,\n", " 'ĠBieber': 42263,\n", " 'ĠMerlin': 32918,\n", " 'tiny': 44152,\n", " 'wp': 24142,\n", " 'ĠSignificant': 49631,\n", " 'ĠGods': 15391,\n", " 'Ġhiber': 46681,\n", " 'ĠCooke': 46247,\n", " 'ĠRutherford': 49767,\n", " 'Ġdred': 47478,\n", " 'Ġevade': 31236,\n", " 'Ġhawk': 48710,\n", " 'Ġburst': 11173,\n", " 'ĠHUD': 30219,\n", " 'ĠKuwait': 27028,\n", " 'Ġorn': 25322,\n", " 'dead': 25124,\n", " 'ĠVisa': 27645,\n", " 'Ġblinding': 46573,\n", " 'rors': 5965,\n", " 'Ġcustomer': 6491,\n", " 'ĠMaur': 18867,\n", " 'Ġorganised': 20325,\n", " 'ĠPut': 5930,\n", " 'ĠHT': 7154,\n", " 'Ġcombine': 12082,\n", " 'Ġfourteen': 29167,\n", " 'Ġbooming': 32017,\n", " 'weapons': 33999,\n", " 'go': 2188,\n", " 'ĠKard': 37875,\n", " 'Ġobligations': 13675,\n", " 'Ġconscience': 18346,\n", " 'Ġdiplomatic': 13093,\n", " 'Notice': 26396,\n", " 'ĠWisdom': 24075,\n", " 'alls': 5691,\n", " 'Ġheaven': 9538,\n", " 'Ġpeanut': 26636,\n", " 'Common': 17227,\n", " 'ĠPolitics': 17554,\n", " 'versible': 37393,\n", " 'ĠUb': 12021,\n", " 'picking': 48864,\n", " '.}': 44587,\n", " 'Muslims': 36452,\n", " 'Ġread': 1100,\n", " 'Ġintegrates': 48105,\n", " 'Ġstigmat': 43603,\n", " 'rase': 22789,\n", " 'Ġrhythms': 39804,\n", " 'sei': 36455,\n", " 'ĠComed': 37024,\n", " 'Ġfluctuations': 31101,\n", " 'Ġ\"$': 17971,\n", " 'android': 19411,\n", " 'azaki': 32276,\n", " 'Ġ333': 23460,\n", " 'arts': 5889,\n", " 'May': 6747,\n", " 'ĠMeng': 41272,\n", " 'ĠNRL': 41695,\n", " 'Ġbaseless': 49241,\n", " 'ĠTrident': 47907,\n", " 'ĠTechnologies': 21852,\n", " 'å': 161,\n", " 'Style': 21466,\n", " 'Ġcirc': 2498,\n", " 'ĠTyp': 17134,\n", " 'Ġ1959': 23859,\n", " 'ijing': 11030,\n", " 'Ġredeem': 26509,\n", " 'stud': 19149,\n", " 'ĠSurgery': 39037,\n", " 'ombs': 33273,\n", " 'Ġmanifests': 42190,\n", " 'sy': 1837,\n", " 'Ġestrogen': 30541,\n", " 'Ġflyer': 42464,\n", " 'Ġrabb': 19368,\n", " 'Ġpitted': 46852,\n", " 'Pos': 21604,\n", " 'ĠSuc': 47352,\n", " 'Ġtagged': 30509,\n", " 'Ġexempl': 21433,\n", " 'Ġunfamiliar': 22594,\n", " 'ĠYuri': 38450,\n", " 'Ġresolves': 38709,\n", " 'iers': 3183,\n", " 'Ġtiger': 26241,\n", " 'Ġrewritten': 30101,\n", " 'ĠFeet': 43391,\n", " 'ĠUnleashed': 44747,\n", " 'ĠHague': 37206,\n", " 'ĠSagan': 49381,\n", " 'ĠAnkara': 28760,\n", " 'Ġdropped': 5710,\n", " 'ahl': 15668,\n", " 'Ġlucrative': 22958,\n", " 'Ġmonet': 32153,\n", " 'Ġwarns': 22145,\n", " 'Ġartic': 17251,\n", " 'istence': 13274,\n", " 'Ġdiversion': 34851,\n", " 'Ġsparkling': 39072,\n", " 'Ġunderrated': 42308,\n", " 'orer': 11934,\n", " 'ĠPrinting': 44118,\n", " 'ippers': 16415,\n", " 'Ġmiracle': 20820,\n", " 'Ġmarginally': 44108,\n", " 'ĠSah': 22982,\n", " 'Ġmagazine': 7093,\n", " 'license': 43085,\n", " 'Ġprophetic': 49054,\n", " 'val': 2100,\n", " 'ĠGra': 7037,\n", " 'archy': 9282,\n", " 'ĠLanguage': 15417,\n", " 'Ġconsequ': 4937,\n", " 'Ġtort': 7619,\n", " 'Ġpreferences': 15387,\n", " 'sets': 28709,\n", " 'Tracker': 35694,\n", " 'ĠCyr': 40399,\n", " 'Ġnervously': 43324,\n", " 'Ġholdings': 27572,\n", " 'Ġpracticable': 44791,\n", " 'Ġlen': 18896,\n", " 'Ġinsisting': 23630,\n", " 'Ġfinale': 19523,\n", " 'resolution': 29268,\n", " 'Ġvigorously': 31609,\n", " 'ĠQuiet': 37355,\n", " 'Ġdyn': 37860,\n", " 'Ġblurry': 44701,\n", " 'PsyNetMessage': 28666,\n", " 'ĠPrism': 35417,\n", " 'ital': 1287,\n", " 'Ġdiscover': 7073,\n", " 'Ġhatch': 25834,\n", " 'Ġpolygamy': 43111,\n", " 'chn': 1349,\n", " 'Ġrot': 5724,\n", " 'Ġfrightening': 23101,\n", " 'ĠCharm': 30225,\n", " 'ĠEns': 48221,\n", " 'ro': 305,\n", " 'Ġomega': 37615,\n", " 'Trump': 6170,\n", " 'Ġassistants': 29488,\n", " 'ĠSPEC': 28196,\n", " 'ĠElvis': 35169,\n", " 'urable': 11970,\n", " 'Ġbutt': 8530,\n", " 'Ġtenants': 21445,\n", " 'Ġnood': 25099,\n", " 'å½': 37605,\n", " 'Ġuntil': 1566,\n", " 'Ġconsiderable': 11091,\n", " 'Ġ1985': 12863,\n", " 'ĠRank': 10916,\n", " 'Ġconjunction': 17856,\n", " 'Ġdresses': 27309,\n", " 'Ġcleansing': 32784,\n", " 'ĠKa': 11611,\n", " 'Ġdetachment': 42925,\n", " 'Ġsurging': 44298,\n", " 'ĠGhosts': 38389,\n", " 'Ġartificial': 11666,\n", " 'Ġequally': 8603,\n", " 'ĠAcid': 27066,\n", " 'ĠUnloaded': 25926,\n", " 'ĠPe': 2631,\n", " 'ĠCIS': 36159,\n", " 'ĠVish': 36900,\n", " 'Ġcensor': 42081,\n", " 'Ġwatered': 48024,\n", " 'Ġecosystems': 30020,\n", " 'ĠAmendments': 39169,\n", " 'Ġblat': 21451,\n", " 'Ġrespectable': 27721,\n", " 'Ġdi': 2566,\n", " 'aleigh': 30729,\n", " 'encers': 42288,\n", " 'Ġsubsections': 46310,\n", " 'Indiana': 49153,\n", " 'Ġrotation': 13179,\n", " 'Ġaunt': 25949,\n", " 'ĠLD': 27178,\n", " 'Ġpolit': 1408,\n", " 'Ġmanual': 10107,\n", " '404': 26429,\n", " 'ĠKaepernick': 30112,\n", " 'ĠGAME': 30517,\n", " 'Boss': 37310,\n", " 'ocument': 7990,\n", " 'Ġdischarge': 17655,\n", " 'Ġupgrades': 16608,\n", " 'ĠBU': 20571,\n", " 'ĠWireless': 24365,\n", " '658': 38431,\n", " 'anticipated': 45178,\n", " 'cone': 49180,\n", " 'Child': 16424,\n", " 'ĠMoor': 31451,\n", " 'ĠYes': 3363,\n", " 'Ġremote': 6569,\n", " 'ĠJere': 10272,\n", " 'Ġenjoys': 20393,\n", " 'illo': 16111,\n", " 'guards': 33427,\n", " 'carry': 34993,\n", " 'ĠNay': 38808,\n", " 'ĠDimensions': 41265,\n", " 'ĠSearch': 11140,\n", " 'Ġbelong': 5594,\n", " 'Ġquestions': 2683,\n", " 'ĠOpenGL': 30672,\n", " 'Ġgrocery': 16918,\n", " 'Safety': 45372,\n", " 'Ġdystopian': 49483,\n", " 'Ġenrich': 22465,\n", " 'Ġsauces': 50134,\n", " 'isite': 16107,\n", " 'minster': 18462,\n", " 'Ġdenounce': 38639,\n", " 'Bitcoin': 22614,\n", " 'Ġsip': 31145,\n", " 'âĢ¦âĢ¦âĢ¦âĢ¦âĢ¦âĢ¦âĢ¦âĢ¦': 29146,\n", " 'ĠAsian': 7740,\n", " 'Ġcruising': 44339,\n", " 'ĠTrap': 21914,\n", " 'leigh': 42342,\n", " 'manager': 37153,\n", " 'Ġbandwidth': 19484,\n", " 'Ġstaple': 25629,\n", " 'ĠShen': 22323,\n", " 'inarily': 21565,\n", " 'atching': 19775,\n", " 'Ġ720': 26250,\n", " 'ĠMorning': 14410,\n", " 'Ġadds': 6673,\n", " 'ĠZen': 14760,\n", " 'ĠRise': 15648,\n", " 'ĠVermont': 16033,\n", " 'ĠAlz': 21535,\n", " 'Ġcoherent': 24870,\n", " 'ĠStress': 36957,\n", " '470': 27790,\n", " 'ĠLowry': 44024,\n", " 'ĠPJ': 44941,\n", " 'âĨĳ': 48541,\n", " 'Ġindependently': 14799,\n", " 'ĠSundays': 32714,\n", " 'Ġincapable': 23402,\n", " 'Ġsabot': 25118,\n", " 'ĠHardy': 27583,\n", " 'Ġlayoffs': 41965,\n", " 'âĢ¦âĢ¦âĢ¦âĢ¦': 15864,\n", " 'ĠJoyce': 25936,\n", " 'Ġmorals': 35472,\n", " 'Germany': 27079,\n", " 'ĠNarc': 31987,\n", " 'Seattle': 34007,\n", " 'ĠBanana': 40058,\n", " 'cks': 4657,\n", " 'ĠViz': 36339,\n", " 'ĠMcGill': 40595,\n", " 'Ġpersuasion': 41784,\n", " 'Ġidentifiers': 42814,\n", " 'Gab': 46079,\n", " 'ĠHold': 9340,\n", " 'ĠAp': 5949,\n", " 'urances': 31741,\n", " 'parency': 11944,\n", " 'quest': 6138,\n", " 'Ġconstant': 6937,\n", " 'Ġbarrels': 17907,\n", " 'Ġfragmentation': 42965,\n", " '1007': 44318,\n", " 'Ġhistory': 2106,\n", " 'Ġshower': 14643,\n", " 'Christ': 10684,\n", " 'Ġcompared': 3688,\n", " 'Ġfilament': 46538,\n", " 'Ġfle': 5104,\n", " 'Ġloyal': 9112,\n", " 'Ġsplit': 6626,\n", " 'Ġstrict': 7646,\n", " 'ĠCombine': 29176,\n", " 'absor': 46303,\n", " 'Ġratio': 8064,\n", " 'Ġinsists': 17424,\n", " 'Ġpreacher': 39797,\n", " 'Ġfirefighter': 43105,\n", " 'Ġund': 3318,\n", " 'Ġmedically': 35031,\n", " 'NOR': 35510,\n", " 'ADE': 19266,\n", " 'REC': 38827,\n", " 'Ġsourced': 18229,\n", " 'ĠBapt': 18226,\n", " 'Ġpension': 13553,\n", " 'Ġpens': 29707,\n", " 'Ġcomputation': 29964,\n", " 'Ġdistinction': 12941,\n", " 'uality': 25775,\n", " 'Ġinflict': 28640,\n", " 'Brook': 45534,\n", " 'ieth': 19235,\n", " 'Questions': 35741,\n", " 'axy': 6969,\n", " 'jl': 20362,\n", " 'Ġteleportation': 48285,\n", " 'Ġsecretaries': 49431,\n", " 'ĠNi': 11556,\n", " 'Ġvape': 44931,\n", " 'Ġrevert': 34052,\n", " 'ĠDes': 2935,\n", " 'hover': 43753,\n", " 'Ġphenomenon': 10733,\n", " 'Ġwit': 20868,\n", " 'ĠAdmiral': 24646,\n", " 'Ġpolicymakers': 29484,\n", " 'gins': 29878,\n", " 'Ġpolitic': 31723,\n", " 'Ġdefin': 2730,\n", " 'ĠDuke': 11083,\n", " 'Ġresting': 19186,\n", " 'Ġdisclosure': 13019,\n", " 'ĠIL': 14639,\n", " 'aughtered': 32734,\n", " 'Ġphilosophers': 24858,\n", " 'ĠIntake': 48885,\n", " 'York': 49278,\n", " 'Ġaren': 3588,\n", " 'Ġexcuse': 12226,\n", " 'soever': 15485,\n", " 'renheit': 34032,\n", " 'Ġhypotheses': 35125,\n", " 'ĠMarty': 29876,\n", " 'erning': 8917,\n", " 'ected': 11197,\n", " 'ĠDeus': 37365,\n", " 'Ġrs': 44608,\n", " 'Ġmages': 45520,\n", " 'Ġtanker': 46335,\n", " 'veyard': 21563,\n", " 'execute': 41049,\n", " 'Ġtattoo': 18785,\n", " 'itivity': 11365,\n", " 'Strike': 31584,\n", " 'Ġretention': 21545,\n", " '(){': 39893,\n", " 'ĠPeters': 15722,\n", " 'arming': 18052,\n", " 'ĠPriority': 34416,\n", " 'ĠClock': 21328,\n", " 'Ġteach': 4545,\n", " 'obin': 38954,\n", " 'Ġdeepest': 25420,\n", " 'ĠMattis': 36107,\n", " '701': 41583,\n", " 'Ġapps': 6725,\n", " 'ĠBlast': 20641,\n", " 'ord': 585,\n", " 'Ġintimidate': 34903,\n", " 'connect': 8443,\n", " 'anca': 42124,\n", " 'ĠDwar': 21469,\n", " 'Dust': 43767,\n", " 'Ġdozen': 8667,\n", " 'rompt': 45700,\n", " 'appa': 20975,\n", " 'ĠKag': 26785,\n", " 'Ġrandomized': 23925,\n", " 'ox': 1140,\n", " 'ĠCTR': 34577,\n", " 'elope': 47329,\n", " 'Minimum': 44046,\n", " 'Ġmill': 3939,\n", " 'Ġcounts': 9853,\n", " 'Legal': 38263,\n", " 'tw': 4246,\n", " 'Ġcontracting': 29148,\n", " 'Ġpredictor': 41568,\n", " 'Ġty': 1259,\n", " 'bull': 16308,\n", " 'Bey': 21993,\n", " 'ares': 3565,\n", " 'Ġconceptual': 23355,\n", " 'Ġgo': 467,\n", " 'Ġtropes': 43275,\n", " 'ĠCameroon': 44568,\n", " 'Ġformat': 5794,\n", " 'ual': 723,\n", " 'TED': 36493,\n", " 'Ġ24': 1987,\n", " 'ĠChemistry': 27867,\n", " 'ĠParameters': 40117,\n", " 'gression': 32383,\n", " 'ocating': 27123,\n", " 'OME': 13649,\n", " 'ĠHarbor': 21146,\n", " 'ĠCoff': 28584,\n", " 'ĠTolkien': 32447,\n", " 'Ġmainstream': 8661,\n", " 'ĠStamina': 36382,\n", " 'Which': 13828,\n", " 'Ġscam': 19126,\n", " 'ĠHamburg': 32526,\n", " 'Ġsmoking': 9216,\n", " ')/': 20679,\n", " 'Ġgraz': 32703,\n", " 'Ġskewed': 37543,\n", " 'ĠColonial': 37726,\n", " 'Ġtesters': 42891,\n", " 'ĠTitans': 18711,\n", " 'Ġcompetitor': 20319,\n", " 'ĠReplacement': 43986,\n", " 'innon': 45067,\n", " 'Ing': 27682,\n", " 'Ġpastoral': 45950,\n", " 'ĠSomerset': 46123,\n", " 'ertain': 1425,\n", " 'ribed': 8725,\n", " 'rac': 11510,\n", " 'ĠSpa': 40208,\n", " 'Ġcapitals': 44590,\n", " 'Ġmonarch': 26464,\n", " 'Hyd': 40436,\n", " 'ĠLaurel': 43442,\n", " 'Ġav': 1196,\n", " 'sci': 36216,\n", " 'Ġteasing': 34417,\n", " 'lation': 7592,\n", " 'Ġstew': 20798,\n", " 'ĠBahrain': 29066,\n", " 'Ġfibre': 33073,\n", " 'ĠPlex': 47176,\n", " 'ĠAmp': 50161,\n", " 'umbered': 26584,\n", " 'Ġapproving': 33354,\n", " '--------------------------------': 3880,\n", " 'Ġappropri': 4148,\n", " 'Ġtradition': 6761,\n", " 'ĠOK': 7477,\n", " 'ĠLegacy': 14843,\n", " 'ĠThunder': 9850,\n", " 'Ġconditioning': 21143,\n", " 'ĠEclipse': 30991,\n", " 'ĠIv': 16975,\n", " 'Definition': 36621,\n", " 'ĠSuppose': 39200,\n", " 'Ġtrunc': 40122,\n", " 'Added': 13003,\n", " 'Mrs': 27034,\n", " 'Ġdebacle': 40943,\n", " 'ossibility': 43691,\n", " 'Ġshone': 44193,\n", " 'ĠHealthcare': 30289,\n", " 'Ġintermitt': 30598,\n", " 'OUGH': 32632,\n", " 'than': 14813,\n", " 'Ġmisdem': 19914,\n", " 'found': 9275,\n", " 'ptive': 21665,\n", " 'inged': 24431,\n", " 'ĠConvers': 32200,\n", " 'si': 13396,\n", " 'Ġbombings': 26579,\n", " 'ĠAccounts': 35584,\n", " 'ĠTrog': 35791,\n", " 'Training': 44357,\n", " 'Ġcrave': 44434,\n", " 'hs': 11994,\n", " '],[': 38430,\n", " 'ĠGregg': 33742,\n", " 'orthy': 18906,\n", " 'ĠLawn': 46007,\n", " 'ie': 494,\n", " '«ĺ': 45865,\n", " 'ĠSanskrit': 46178,\n", " 'country': 19315,\n", " 'omatic': 13730,\n", " 'Ġencl': 13507,\n", " 'anguage': 9000,\n", " 'Ġprogrammers': 24867,\n", " 'ĠZuckerberg': 32584,\n", " 'Ġemphatically': 49206,\n", " 'Ġdemanded': 12284,\n", " 'style': 7635,\n", " 'ĠGamergate': 37337,\n", " 'ĠPas': 17454,\n", " 'ĠGW': 27164,\n", " 'Ġanimal': 5044,\n", " 'ĠBeard': 41698,\n", " 'ĊĊ': 628,\n", " 'ener': 877,\n", " 'Ġguild': 19806,\n", " 'Ġpotato': 21219,\n", " 'ADS': 47149,\n", " 'Ġmolecule': 27756,\n", " 'ĠTwitter': 3009,\n", " 'Ġcold': 4692,\n", " 'ĠEst': 10062,\n", " 'Ġamd': 39971,\n", " 'Ġcigars': 33204,\n", " 'ãĥ´ãĤ¡': 44444,\n", " 'Ġinverse': 34062,\n", " 'Fake': 49233,\n", " 'ks': 591,\n", " 'Yu': 40728,\n", " 'Ġdaughter': 4957,\n", " 'kos': 46150,\n", " 'Ġagencies': 5942,\n", " 'Disable': 48893,\n", " 'ampire': 13577,\n", " 'rawl': 13132,\n", " 'ĠOx': 10736,\n", " '=\"#': 25698,\n", " 'ĠPerth': 29913,\n", " 'Ġmakeup': 16029,\n", " 'ĠRahman': 46611,\n", " 'oret': 9997,\n", " 'Ġstairs': 16046,\n", " 'omen': 3674,\n", " 'Repe': 47541,\n", " 'Ġwere': 547,\n", " 'é¾į': 11885,\n", " 'Ġadmission': 13938,\n", " '467': 24669,\n", " 'bowl': 36859,\n", " 'Ġthrows': 12542,\n", " 'ĠFranken': 25301,\n", " 'Ġman': 582,\n", " 'Ġex': 409,\n", " 'read': 961,\n", " 'TER': 5781,\n", " 'Big': 12804,\n", " 'Ġthousands': 4138,\n", " '69': 3388,\n", " 'occ': 13966,\n", " 'Ġbedroom': 14043,\n", " \"-'\": 19355,\n", " 'Ġfees': 6642,\n", " 'ridge': 12818,\n", " 'ĠLAN': 24192,\n", " 'Ġstripped': 18818,\n", " 'Ġadministration': 3662,\n", " 'Ġyo': 27406,\n", " 'force': 3174,\n", " 'eff': 14822,\n", " 'coll': 26000,\n", " 'Ġprotocols': 19565,\n", " 'Ġoutgoing': 28181,\n", " 'ĠYuan': 34071,\n", " 'ÃĥÃĤÃĥÃĤÃĥÃĤÃĥÃĤÃĥÃĤÃĥÃĤÃĥÃĤÃĥÃĤÃĥÃĤÃĥÃĤÃĥÃĤÃĥÃĤÃĥÃĤÃĥÃĤÃĥÃĤÃĥÃĤÃĥÃĤÃĥÃĤÃĥÃĤÃĥÃĤÃĥÃĤÃĥÃĤÃĥÃĤÃĥÃĤÃĥÃĤÃĥÃĤÃĥÃĤÃĥÃĤÃĥÃĤÃĥÃĤÃĥÃĤÃĥÃĤ': 35496,\n", " 'ĠLG': 17370,\n", " 'Ġdisagreement': 25800,\n", " 'ä¸Ĭ': 41468,\n", " 'workers': 22896,\n", " 'Ġlil': 42280,\n", " 'Ġply': 35960,\n", " 'alien': 42690,\n", " 'zers': 47031,\n", " 'Ġ760': 48284,\n", " 'Ġclaimant': 50018,\n", " 'ĠRagnar': 32797,\n", " 'Ġdelic': 8675,\n", " 'Certainly': 36001,\n", " 'ĠEnrique': 46879,\n", " 'Ward': 49021,\n", " 'Law': 16966,\n", " 'âĢł': 33912,\n", " 'RG': 48192,\n", " 'ydia': 30708,\n", " 'Ġsubstit': 21436,\n", " 'Edit': 18378,\n", " 'Kevin': 23865,\n", " 'Ġprotestors': 36915,\n", " 'enhagen': 30347,\n", " 'Rex': 47389,\n", " 'Ġnond': 30745,\n", " 'Ġshame': 10195,\n", " 'ored': 1850,\n", " 'ortunate': 13651,\n", " 'Ironically': 44850,\n", " 'ĠâĶľâĶĢâĶĢ': 35306,\n", " 'ĠWeekend': 30537,\n", " 'ORN': 30649,\n", " 'ptroller': 44913,\n", " 'Ġredundancy': 49052,\n", " 'Ġcommenced': 32400,\n", " 'Ġperl': 48746,\n", " 'ĠOF': 3963,\n", " 'Query': 20746,\n", " 'urgical': 31839,\n", " 'Ġprevious': 2180,\n", " 'nexus': 44520,\n", " 'Ġbetting': 22908,\n", " 'Ġdepleted': 34069,\n", " 'ĠCelebration': 44911,\n", " 'Ġtaxpayer': 14776,\n", " 'ye': 5948,\n", " 'Ġgrasp': 13180,\n", " 'Ġcause': 2728,\n", " 'Ġnotion': 9495,\n", " 'earth': 16442,\n", " 'Ġprototype': 14879,\n", " 'anton': 23026,\n", " '227': 24403,\n", " 'Ġriots': 23766,\n", " 'enced': 5864,\n", " 'Ġherein': 24028,\n", " 'ĠTournament': 9595,\n", " 'ĠDominion': 28098,\n", " 'ĠOsiris': 40925,\n", " 'shadow': 19106,\n", " 'Ġcoc': 8954,\n", " 'Ġstitches': 28096,\n", " 'ĠRealm': 23651,\n", " 'Ġ171': 28369,\n", " 'imensional': 16198,\n", " 'Ġsatirical': 40557,\n", " 'cake': 30560,\n", " 'Ġenclosure': 30685,\n", " 'Ġstellar': 25041,\n", " 'Fuck': 34094,\n", " 'Ġapprehended': 41979,\n", " 'Ġnonprofits': 44014,\n", " 'Ġplane': 6614,\n", " 'Ġemail': 3053,\n", " 'Ġshowing': 4478,\n", " 'oped': 19458,\n", " 'Ġexpire': 24264,\n", " 'Street': 34356,\n", " 'i<PERSON><PERSON><PERSON><PERSON>': 36408,\n", " 'Ġrefrain': 25133,\n", " 'ĠMerc': 12185,\n", " 'ĠIDF': 33389,\n", " 'ĠLub': 40753,\n", " 'aunting': 20706,\n", " 'Ġ(~': 31034,\n", " 'Ġtelescopes': 42067,\n", " 'gradation': 26317,\n", " 'Ġbehav': 2955,\n", " 'kered': 28970,\n", " 'ĠPrev': 43280,\n", " 'metal': 28469,\n", " 'Ye': 35543,\n", " 'ĠWCS': 45410,\n", " 'ĠTwin': 14968,\n", " 'anson': 23103,\n", " 'Ġinstallment': 25168,\n", " 'Ġfinishes': 20271,\n", " 'Ġrefill': 47539,\n", " 'Ġgra': 7933,\n", " 'ĠConrad': 39708,\n", " 'Ġmastery': 30677,\n", " 'Ġresear': 4027,\n", " 'Ġnet': 2010,\n", " 'hh': 12337,\n", " 'ĠTyler': 14886,\n", " 'Ġreproduced': 31759,\n", " 'Ġnewspaper': 7533,\n", " 'Ġcush': 24736,\n", " 'OB': 9864,\n", " 'Ġdominant': 11410,\n", " 'Ġconvertible': 41637,\n", " 'Â®,': 45088,\n", " 'Ġuntold': 47872,\n", " 'ired': 1202,\n", " 'Ġlatent': 41270,\n", " 'Ġnations': 7027,\n", " 'ĠBut': 887,\n", " 'file': 7753,\n", " 'ĠEmil': 44272,\n", " 'llular': 32771,\n", " 'Ġproportional': 27111,\n", " 'Ġswamp': 26837,\n", " 'aunts': 43981,\n", " 'Ġproverb': 36950,\n", " 'Ġsurpassed': 30612,\n", " 'Ġmultiplayer': 16913,\n", " 'Ġcarbohydrate': 28205,\n", " 'Pretty': 35700,\n", " 'ĠFederal': 5618,\n", " 'Ġtestified': 15463,\n", " 'bps': 18799,\n", " 'Ġmega': 23465,\n", " 'Ġinsult': 13277,\n", " 'RED': 22083,\n", " 'ĠEv': 4319,\n", " 'Ġjudging': 22989,\n", " 'lance': 23215,\n", " 'Font': 23252,\n", " 'ĠWong': 27247,\n", " 'fight': 15481,\n", " 'Ġwhim': 29923,\n", " 'State': 9012,\n", " 'Jon': 18219,\n", " 'ulture': 6456,\n", " 'Ġangular': 32558,\n", " 'ĠDisclosure': 41806,\n", " 'entimes': 43598,\n", " 'Ġglory': 13476,\n", " 'Ġconception': 19759,\n", " '185': 21652,\n", " 'arij': 39010,\n", " '199': 19104,\n", " 'emetery': 19785,\n", " 'Charlie': 37136,\n", " 'ĠKelley': 34560,\n", " 'ĠLem': 20607,\n", " 'hart': 18647,\n", " 'Ġãĥ': 14524,\n", " 'ĠÎ¼g': 44415,\n", " 'erey': 48023,\n", " 'ĠCDs': 36731,\n", " 'Gl': 9861,\n", " 'leaders': 37553,\n", " 'Ġcaller': 24955,\n", " 'Ġangels': 21981,\n", " 'ĠBlow': 26588,\n", " 'Ġtouted': 28275,\n", " 'ĠAAA': 25734,\n", " 'Ġfraud': 7394,\n", " 'Ġexamining': 17247,\n", " 'Ġcapac': 18457,\n", " 'ĠChecks': 47719,\n", " 'Ġlack': 3092,\n", " 'ifty': 24905,\n", " 'ĠMyth': 18900,\n", " 'ĠSummit': 20014,\n", " 'csv': 40664,\n", " 'Ġworkshops': 25982,\n", " 'umbing': 28149,\n", " 'ĠThur': 36975,\n", " 'adas': 38768,\n", " 'ĠHernandez': 24687,\n", " 'Ġincompatible': 27294,\n", " 'ĠConj': 37587,\n", " 'Ġjer': 13665,\n", " 'Ġreconciliation': 27515,\n", " 'Ġreplace': 6330,\n", " 'aved': 9586,\n", " 'Ġdream': 4320,\n", " '630': 30005,\n", " 'Ġreproductive': 18391,\n", " 'aura': 33830,\n", " 'fortable': 12065,\n", " 'Ġtunnels': 22642,\n", " 'Ġasync': 30351,\n", " 'Ġmisinformation': 32805,\n", " 'Ġsalute': 34967,\n", " 'Ġdrib': 35003,\n", " 'Ġawaiting': 21859,\n", " 'Ġkidding': 26471,\n", " 'ational': 864,\n", " 'Ġ1908': 40417,\n", " 'ĠAT': 5161,\n", " 'ĠDug': 27436,\n", " 'ĠReserved': 33876,\n", " 'ĠProbe': 42600,\n", " 'Ġriver': 7850,\n", " 'Ġhope': 2911,\n", " 'ĠEn': 2039,\n", " 'Ġdynam': 6382,\n", " 'High': 11922,\n", " 'Ġspoke': 5158,\n", " 'ĠErn': 22762,\n", " '397': 33372,\n", " 'ĠFarmers': 34982,\n", " 'iffin': 42022,\n", " 'ollo': 15578,\n", " 'ĠHolmes': 17628,\n", " 'Ġvowel': 48617,\n", " 'ĠTwe': 24205,\n", " 'cohol': 4857,\n", " 'Ġtrough': 45047,\n", " 'ĠKah': 38798,\n", " 'um': 388,\n", " 'ater': 729,\n", " 'liners': 34380,\n", " 'oops': 44860,\n", " 'Ġtreacherous': 45365,\n", " 'Ġnotoriety': 46283,\n", " 'arbon': 42084,\n", " 'ĠEPS': 47013,\n", " 'Ġsubsistence': 48042,\n", " 'ĠSpin': 28002,\n", " 'Ġencryption': 15835,\n", " 'Ġquarterbacks': 23643,\n", " 'Ã¶n': 48863,\n", " 'Ġgul': 47161,\n", " 'Ġpredator': 30135,\n", " 'Ġpunishment': 9837,\n", " 'Ġants': 27842,\n", " 'Ġsimulated': 28590,\n", " 'ãĥ¼ãĥ': 12045,\n", " 'ĠSecondary': 29521,\n", " 'Ġuncond': 31776,\n", " 'Ġstarve': 47141,\n", " 'Ġfronts': 29324,\n", " 'noticed': 31696,\n", " 'Mult': 15205,\n", " 'English': 15823,\n", " 'Ġsanctuary': 24050,\n", " 'Ġgovernors': 26824,\n", " 'Ġ313': 35897,\n", " 'Ġprovinces': 17812,\n", " 'Ġunderstatement': 46196,\n", " 'LET': 28882,\n", " 'enny': 11870,\n", " 'recorded': 47398,\n", " 'ĠSPORTS': 47515,\n", " 'Push': 49222,\n", " 'Ġabnormal': 18801,\n", " 'ĠEPA': 10193,\n", " 'Ġmitigate': 24237,\n", " 'ĠUsually': 19672,\n", " 'nel': 4954,\n", " 'Ġpsychopath': 29670,\n", " 'Ġcrab': 32202,\n", " 'business': 22680,\n", " 'Ġapprehension': 46473,\n", " 'Ġcertific': 8700,\n", " 'ĠKE': 47134,\n", " 'ĠZig': 24992,\n", " 'Ġguardian': 21688,\n", " 'Ġdreadful': 35483,\n", " 'Ġcircumstances': 5917,\n", " 'icer': 16647,\n", " 'ĠCompet': 38558,\n", " 'Ġ457': 47996,\n", " 'Ġmel': 7758,\n", " 'renched': 23437,\n", " 'Ġvitri': 48950,\n", " 'Pop': 16979,\n", " ...}"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["tokenizer.vocab"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.9"}, "orig_nbformat": 4, "vscode": {"interpreter": {"hash": "b0fa6594d8f4cbf19f97940f81e996739fb7646882a419484c72d19e05852a7e"}}}, "nbformat": 4, "nbformat_minor": 2}