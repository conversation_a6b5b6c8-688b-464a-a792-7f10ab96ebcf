{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["Compute token counts for a few repos using Claude and Llama 3.\n", "\n", "Claude token counting is only available via an API, which limits how we can use it."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "from pathlib import Path\n", "\n", "import anthropic\n", "\n", "from research.core.tokenizers import get_tokenizer\n", "\n", "from base.tokenizers import list_tokenizers\n", "\n", "print(\"Available tokenizers:\")\n", "print(list_tokenizers())\n", "\n", "llama3_tokenizer = get_tokenizer(\"llama3_instruct\")\n", "sc2_tokenizer = get_tokenizer(\"starcoder2\")\n", "\n", "api_key = (\n", "    Path(\"/home/<USER>/.config/anthropic/api_key\").read_text(encoding=\"utf8\").strip()\n", ")\n", "client = anthropic.Anthropic(api_key=api_key)\n", "\n", "\n", "def count_claude_tokens(text: str) -> int:\n", "    response = client.messages.count_tokens(\n", "        model=\"claude-3-5-sonnet-20241022\",\n", "        messages=[\n", "            {\n", "                \"role\": \"user\",\n", "                \"content\": text,\n", "            }\n", "        ],\n", "    )\n", "    return response.input_tokens\n", "\n", "\n", "def count_llama_tokens(text: str) -> int:\n", "    return len(llama3_tokenizer.tokenize_safe(text))\n", "\n", "\n", "def count_starcoder2_tokens(text: str) -> int:\n", "    return len(sc2_tokenizer.tokenize_safe(text))\n", "\n", "\n", "repos = {\n", "    \"pandas\": {\n", "        \"path\": \"/home/<USER>/pandas\",\n", "        \"pattern\": \"*.py\",\n", "    },\n", "    \"node\": {\n", "        \"path\": \"/home/<USER>/node\",\n", "        \"pattern\": \"*.js\",\n", "    },\n", "    \"abseil\": {\n", "        \"path\": \"/home/<USER>/abseil-cpp\",\n", "        \"pattern\": \"*.cc\",\n", "    },\n", "}\n", "\n", "\n", "saved_statistics_path = Path(\n", "    \"/mnt/efs/augment/user/guy/analysis/claude_vs_llama_token_counts.json\"\n", ")\n", "\n", "\n", "def generate_data():\n", "    max_files = 2000\n", "\n", "    all_token_counts = {}\n", "\n", "    for repo_name, repo in repos.items():\n", "        print(f\"Processing {repo_name}...\")\n", "        root = Path(repo[\"path\"])\n", "\n", "        if not root.exists():\n", "            raise ValueError(\n", "                f\"Path {root} does not exist -- it should be a cloned repo.\"\n", "            )\n", "\n", "        pattern = repo[\"pattern\"]\n", "\n", "        all_token_counts[repo_name] = {}\n", "        token_counts = all_token_counts[repo_name]\n", "\n", "        for path in root.rglob(pattern):\n", "            text = path.read_text(encoding=\"utf8\")\n", "            if not text:\n", "                continue\n", "            claude_tokens = count_claude_tokens(text)\n", "            llama_tokens = count_llama_tokens(text)\n", "            starcoder_tokens = count_starcoder2_tokens(text)\n", "            token_counts[str(path)] = {\n", "                \"chars\": len(text),\n", "                \"lines\": len(text.splitlines()),\n", "                \"claude\": claude_tokens,\n", "                \"llama\": llama_tokens,\n", "                \"starcoder2\": starcoder_tokens,\n", "            }\n", "            if len(token_counts) % 50 == 0:\n", "                print(len(token_counts))\n", "            if len(token_counts) > max_files:\n", "                break\n", "\n", "    with Path(saved_statistics_path).open(\"w\") as f:\n", "        json.dump(all_token_counts, f, indent=2)\n", "\n", "    return all_token_counts\n", "\n", "\n", "# generate_data()\n", "\n", "all_token_counts = json.load(Path(saved_statistics_path).open(\"r\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "import numpy as np\n", "\n", "for repo_name, token_counts in all_token_counts.items():\n", "    chars = np.array([c[\"chars\"] for c in token_counts.values()])\n", "    lines = np.array([c[\"lines\"] for c in token_counts.values()])\n", "    claude_tokens = np.array([c[\"claude\"] for c in token_counts.values()])\n", "    llama_tokens = np.array([c[\"llama\"] for c in token_counts.values()])\n", "    starcoder_tokens = np.array([c[\"starcoder2\"] for c in token_counts.values()])\n", "\n", "    ratios = claude_tokens / llama_tokens\n", "    avg_ratio = np.mean(ratios)\n", "    max_ratio = np.max(ratios)\n", "    corr = np.corrcoef([chars, lines, claude_tokens, llama_tokens, starcoder_tokens])\n", "\n", "    print(f\"\\n\\nResults for {repo_name} :\")\n", "\n", "    print(\"\\n# Llama 3\")\n", "    print(\n", "        f\"chars per token: {np.mean(chars / llama_tokens):.2f} +- {np.std(chars / llama_tokens):.2f}\"\n", "    )\n", "    print(\n", "        f\"tokens per line: {np.mean(llama_tokens / lines):.2f} +- {np.std(llama_tokens / lines):.2f}\"\n", "    )\n", "\n", "    print(\"\\n# Claude\")\n", "    print(\n", "        f\"chars per token: {np.mean(chars / claude_tokens):.2f} +- {np.std(chars / claude_tokens):.2f}\"\n", "    )\n", "    print(\n", "        f\"tokens per line: {np.mean(claude_tokens / lines):.2f} +- {np.std(claude_tokens / lines):.2f}\"\n", "    )\n", "\n", "    print(\"\\n# <PERSON> vs. Llama 3\")\n", "    print(f\"avg ratio: {avg_ratio}\")\n", "    print(f\"max ratio: {max_ratio}\")\n", "    print(f\"Top 10 ratios: {np.sort(ratios)[-10:]}\")\n", "    print(f\"corr: {corr}\")\n", "\n", "    plt.scatter(claude_tokens, llama_tokens)\n", "    plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}