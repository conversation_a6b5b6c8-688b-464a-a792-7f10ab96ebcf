"""Script to plot daily active users separated into self-serve and enterprise buckets."""

import argparse
from pathlib import Path
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns


def categorize_tenant(tenant: str) -> str:
    """Categorize tenant into 'self-serve' or 'enterprise' or None."""
    tenant = tenant.lower()

    # Skip these tenants
    if "aitutor" in tenant or "tenzero" in tenant:
        return None

    # Self-serve tenants
    if "vanguard" in tenant or "discovery" in tenant:
        return "self-serve"

    # All other tenants are enterprise
    return "enterprise"


def plot_active_users(df: pd.DataFrame, output_path: Path):
    """Create a line plot of daily active users by category."""

    # Add category column
    df["category"] = df["tenant"].apply(categorize_tenant)

    # Remove rows where category is None
    df = df[df["category"].notna()]

    # Group by date and category, sum active users
    daily_users = df.groupby(["date", "category"])["active_users"].sum().reset_index()

    # Pivot the data for plotting
    plot_data = daily_users.pivot(
        index="date", columns="category", values="active_users"
    )

    # Create the plot
    plt.figure(figsize=(12, 6))
    sns.set_style("whitegrid")

    # Plot each line
    for category in ["enterprise", "self-serve"]:
        if category in plot_data.columns:
            plt.plot(
                plot_data.index,
                plot_data[category],
                marker="o",
                label=category.replace("-", " ").title(),
                linewidth=2,
            )

    plt.title("Daily Active Users by Category", pad=20)
    plt.xlabel("Date")
    plt.ylabel("Number of Active Users")
    plt.legend()

    # Rotate x-axis labels for better readability
    plt.xticks(rotation=45)

    # Adjust layout to prevent label cutoff
    plt.tight_layout()

    # Save the plot
    plt.savefig(output_path)
    print(f"Plot saved to {output_path}")


def main():
    parser = argparse.ArgumentParser(description="Plot daily active users by category")
    parser.add_argument(
        "--input_file",
        type=str,
        help="Input CSV file with daily active users data",
        required=True,
    )
    parser.add_argument(
        "--output_file",
        type=str,
        help="Output file path for the plot (PNG)",
        default="active_users_plot.png",
    )
    args = parser.parse_args()

    # Read the data
    df = pd.read_csv(args.input_file)
    df["date"] = pd.to_datetime(df["date"])

    # Create the plot
    plot_active_users(df, Path(args.output_file))


if __name__ == "__main__":
    main()
