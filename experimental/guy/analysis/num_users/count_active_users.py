"""Script to count daily active users per tenant from BigQuery data."""

import argparse
from datetime import datetime, timedelta
import pandas as pd
from google.cloud import bigquery
from pathlib import Path

from base.datasets.gcp_creds import get_gcp_creds


def get_daily_active_users(client: bigquery.Client, date: datetime) -> pd.DataFrame:
    """Query BigQuery for daily active users per tenant for a specific date."""

    query = """
    SELECT
        tenant,
        COUNT(DISTINCT user_id) as active_users,
        DATE(@date) as date
    FROM `system-services-prod.us_prod_request_insight_analytics_dataset.human_request_metadata`
    WHERE DATE(time) = DATE(@date)
    GROUP BY tenant
    ORDER BY active_users DESC
    """

    job_config = bigquery.QueryJobConfig(
        query_parameters=[
            bigquery.ScalarQueryParameter("date", "DATE", date.date()),
        ],
        use_query_cache=True,
        use_legacy_sql=False,
    )

    # Use to_dataframe with a specific API method
    df = (
        client.query(query, job_config=job_config)
        .result()
        .to_dataframe(create_bqstorage_client=False)
    )
    return df


def main():
    parser = argparse.ArgumentParser(description="Count daily active users per tenant")
    parser.add_argument(
        "--start_date",
        type=str,
        help="Start date in YYYY-MM-DD format (default: 2024-01-01)",
        default="2024-01-01",
    )
    parser.add_argument(
        "--end_date",
        type=str,
        help="End date in YYYY-MM-DD format (default: today)",
        default=datetime.today().strftime("%Y-%m-%d"),
    )
    parser.add_argument(
        "--output_dir",
        type=str,
        help="Directory to save results",
        default="/mnt/efs/augment/user/guy/analysis/num_users",
    )
    args = parser.parse_args()

    # Setup BigQuery client
    gcp_creds, _ = get_gcp_creds()
    client = bigquery.Client(project="system-services-prod", credentials=gcp_creds)

    # Parse dates
    start_date = datetime.strptime(args.start_date, "%Y-%m-%d")
    end_date = datetime.strptime(args.end_date, "%Y-%m-%d")

    # Create output directory
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)

    # Initialize results list and output file
    all_results = []
    output_file = output_dir / f"active_users_{args.start_date}_to_{args.end_date}.csv"

    current_date = start_date
    while current_date <= end_date:
        print(f"Processing {current_date.date()}")

        try:
            df = get_daily_active_users(client, current_date)
            all_results.append(df)

            # Save the accumulated results after each day
            if all_results:
                final_df = pd.concat(all_results, ignore_index=True)
                final_df.to_csv(output_file, index=False)
                print(f"Updated results saved to {output_file}")

        except Exception as e:
            print(f"Error processing {current_date.date()}: {e}")

        current_date += timedelta(days=1)

    print(f"\nProcessing complete. Final results saved to {output_file}")


if __name__ == "__main__":
    main()
