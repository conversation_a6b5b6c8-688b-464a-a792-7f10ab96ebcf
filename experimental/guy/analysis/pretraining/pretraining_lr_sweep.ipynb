{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Plot pre-training eval results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import ipywidgets as widgets\n", "from IPython.display import display\n", "import seaborn as sns\n", "\n", "sns.set_style(\"whitegrid\")\n", "\n", "# Exported from: https://docs.google.com/spreadsheets/d/12j031KYqWVc51ivYzgh1BMEtXfUIHuzUqz9bGE_2d24/edit#gid=801091719\n", "raw_df = pandas.read_csv(\"pretraining_experiments_LR_BS.csv\")\n", "\n", "# Drop NaNs and other non-numbers\n", "df = raw_df.dropna()\n", "df = df[df[\"API\"] != \"-\"]\n", "\n", "metrics_to_units = {\n", "    \"API\": \"%\",\n", "    \"CC-Eval\": \"%\",\n", "    \"HumanEval-FIM\": \"%\",\n", "    \"HumanEval\": \"%\",\n", "    \"Hydra Fn\": \"cases\",\n", "    \"Hydra 2-3L Hard\": \"cases\",\n", "    \"Hydra 2-3L\": \"cases\",\n", "    \"MBPP\": \"%\",\n", "}\n", "\n", "total_cases = {\n", "    \"Hydra Fn\": 455,\n", "    \"Hydra 2-3L\": 554,\n", "    \"API\": 853,\n", "    # \"Hydra 2-3L Hard\": None,\n", "}\n", "\n", "# Clean up the data and make it numerical\n", "for metric in metrics:\n", "    df[metric] = df[metric].map(lambda x: x.replace(\"%\", \"\")).astype(float)\n", "\n", "for metric, cases in total_cases.items():\n", "    normalized_metric_name = f\"{metric} (%)\"\n", "    df[normalized_metric_name] = df[metric] / cases * 100\n", "    metrics_to_units[normalized_metric_name] = \"%\"\n", "\n", "metrics = list(metrics_to_units.keys())\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "bs_col = \"Batch size\"\n", "batch_sizes = df[bs_col].unique()\n", "bs_widget = widgets.Select(\n", "    options=batch_sizes,\n", "    value=batch_sizes[0],\n", "    description=bs_col,\n", "    disabled=False,\n", "    rows=len(batch_sizes),\n", ")\n", "\n", "order_col = \"Order\"\n", "orders = df[order_col].unique()\n", "order_widget = widgets.Select(\n", "    options=orders,\n", "    value=orders[0],\n", "    description=order_col,\n", "    disabled=False,\n", "    rows=len(orders),\n", ")\n", "\n", "metrics_widget = widgets.SelectMultiple(\n", "    options=metrics,\n", "    value=metrics,\n", "    rows=len(metrics),\n", "    description=\"Metrics (multi-select with shift/ctrl)\",\n", "    disabled=False\n", ")\n", "\n", "max_lr_col = \"Max LR\"\n", "\n", "def plot_func(batch_size, order, selected_metrics):\n", "    exps = df[(df[bs_col] == batch_size) & (df[order_col] == order)]\n", "\n", "    # plt.clf()  # Clear the current figure\n", "    fig, ax = plt.subplots(figsize=(8, 6))\n", "\n", "    common_x = None\n", "    y_total = None\n", "\n", "    for metric in selected_metrics:\n", "        x = exps[max_lr_col]\n", "\n", "        if common_x is None:\n", "            common_x = x\n", "        else:\n", "            assert all(x == common_x)\n", "\n", "        y = exps[metric]\n", "\n", "        if y_total is None:\n", "            y_total = y\n", "        else:\n", "            y_total += y\n", "\n", "        metric_label = f\"{metric} ({metrics_to_units[metric]})\"\n", "        ax.plot(x, y, \".-\", label=metric_label)\n", "        ax.set_xlabel(max_lr_col)\n", "        ax.set_ylabel(\"eval result\")\n", "        ax.set_xscale(\"log\")\n", "\n", "    y_mean = y_total / len(selected_metrics)\n", "    ax.plot(common_x, y_mean, \".--\", label=\"Mean\")\n", "    ax.grid(True)\n", "    ax.legend(loc='upper left', bbox_to_anchor=(1, 1))\n", "    fig.tight_layout()\n", "    fig.show()\n", "\n", "interactive_plot = widgets.interactive(\n", "    plot_func, batch_size=bs_widget, order=order_widget, selected_metrics=metrics_widget\n", ")\n", "display(interactive_plot)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}