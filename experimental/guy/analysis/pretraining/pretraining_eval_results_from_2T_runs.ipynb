{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Data source: https://docs.google.com/spreadsheets/d/1lX5ZBWjvGGCZb0U1jh3HrJDl0NoSzCuwHj9kCU8TX-U/edit#gid=1484867082\n", "\n", "import numpy as np\n", "import pandas\n", "import matplotlib.pyplot as plt\n", "\n", "from scipy.signal import savgol_filter\n", "import seaborn as sns\n", "\n", "sns.set_style(\"whitegrid\")\n", "\n", "run2_df_high_lr = pandas.read_csv(\"run2_evals.csv\")\n", "run2_df_low_lr = pandas.read_csv(\"run2_low_lr_evals.csv\")\n", "\n", "# Don't include HumanEval1s, HumanEval5s because they are not in the high-LR run\n", "eval_names = [\"HumanEval\", \"HumanEval-FIM\", \"CCEval\", \"MBPP\", \"hydra functions\", \"hydra 2-3L Hard\", \"hydra 2-3L\", \"API\"]\n", "\n", "deepseek_33b = {\n", "    \"HumanEval\": 0.3476,\n", "    \"HumanEval 1s\": 0.3476,\n", "    \"HumanEval 5s\": 0.3537,\n", "    \"HumanEval-FIM\": 0.4048,\n", "    \"CCEval\": 0.3037,\n", "    \"MBPP\": 0.434,\n", "    \"hydra functions\": 157,\n", "    \"hydra 2-3L Hard\": 202,\n", "    \"hydra 2-3L\": 263,\n", "    \"API\": 71,\n", "}\n", "\n", "def smooth(y):\n", "    return savgol_filter(y, 51, 3)\n", "\n", "run2_df_low_lr\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fig, ax = plt.subplots()\n", "\n", "x_axis = \"Steps\"\n", "df = run2_df_low_lr\n", "\n", "colors = [(1, 0, 0), (0, 0.8, 0), (0, 0, 1)]\n", "unsaturated_colors = [[max(0.7, x) for x in c ] for c in colors]\n", "\n", "for i, eval_name in enumerate([\"HumanEval5s\", \"HumanEval1s\", \"HumanEval\"]):\n", "    smoothed = smooth(df[eval_name])\n", "    ax.plot(df[x_axis], df[eval_name], '.', color=unsaturated_colors[i])\n", "    ax.plot(df[x_axis], smoothed, '-', label=f\"{eval_name} ({smoothed[-1]:.2f})\", color=colors[i])\n", "\n", "ax.axhline(deepseek_33b[\"HumanEval\"], color=\"b\", linestyle=\"--\", label=\"DeepSeek 33B\")\n", "\n", "ax.legend()\n", "ax.set_title(\"Few-shot HumanEval on low-LR run\")\n", "\n", "fig.savefig(\"pretraining_2T_humaneval.png\", dpi=300)\n", "fig.show()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "x_axis = \"Steps\"\n", "\n", "ncols = 2\n", "fig, axes = plt.subplots(ncols=ncols, nrows=len(eval_names) // 2, figsize=(12, 5 * len(eval_names)/2))\n", "\n", "for i, eval_name in enumerate(eval_names):\n", "    ax = axes[i // ncols, i % ncols]\n", "\n", "    if eval_name in run2_df_low_lr:\n", "        smoothed_low_lr = smooth(run2_df_low_lr[eval_name])\n", "        final_low_lr_value = smoothed_low_lr[-1]\n", "        ax.plot(run2_df_low_lr[x_axis], run2_df_low_lr[eval_name], '.', color=(0.7, 0.8, 0.7))\n", "        ax.plot(run2_df_low_lr[x_axis], smoothed_low_lr, '-', color=(0, 0.8, 0), label=f\"Low LR ({final_low_lr_value:.2f} final smooth)\")\n", "\n", "    if eval_name in run2_df_high_lr:\n", "        smoothed_high_lr = smooth(run2_df_high_lr[eval_name])\n", "        final_high_lr_value = smoothed_high_lr[-1]\n", "        ax.plot(run2_df_high_lr[x_axis], run2_df_high_lr[eval_name], '.', color=(0.7, 0.7, 1))\n", "        ax.plot(run2_df_high_lr[x_axis], smoothed_high_lr, '-', color=(0, 0, 1), label=f\"High LR ({final_high_lr_value:.2f})\")\n", "\n", "    ax.axhline(deepseek_33b[eval_name], color=\"b\", linestyle=\"--\", label=\"DeepSeek 33B\")\n", "\n", "    ax.legend()\n", "    ax.set_title(eval_name)\n", "    ax.set_xlabel(x_axis)\n", "\n", "fig.tight_layout()\n", "fig.savefig(\"pretraining_2T_evals.png\", dpi=300)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}