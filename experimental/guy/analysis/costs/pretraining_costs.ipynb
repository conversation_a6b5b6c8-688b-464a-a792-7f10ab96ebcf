{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Setup"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import pandas\n", "\n", "gpus_per_node = 8\n", "total_cluster_gpus = 480\n", "total_cluster_nodes = total_cluster_gpus // gpus_per_node\n", "\n", "optimal_tokens_per_param = 20\n", "chinchilla_overtraining_factor = 3\n", "actual_tokens_per_param = optimal_tokens_per_param * chinchilla_overtraining_factor\n", "\n", "secs_per_day = 3600 * 24\n", "secs_per_week = secs_per_day * 7\n", "secs_per_month = secs_per_day * 30.5\n", "secs_per_year = secs_per_day * 365"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# <PERSON>'s measurements on FastBackward"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "cluster_fraction_for_training = 2/3\n", "gpus_for_training = total_cluster_gpus * cluster_fraction_for_training\n", "\n", "tokens_per_sec_on_64xH100 = {\n", "    \"deepseek_33b\": 90e3,\n", "    \"llama_70b\": 37.5e3,\n", "}\n", "\n", "# Measured numbers were tokens/sec on 64xH100, so dividing by number of GPUs\n", "orig_measurement_gpus = 64\n", "\n", "df = pandas.DataFrame({\n", "    \"model\": [\"deepseek_33b\", \"llama_70b\"],\n", "    \"params\": [33e9, 70e9],\n", "    \"tokens_per_sec_per_gpu\": [90e3/orig_measurement_gpus, 37.5e3/orig_measurement_gpus],\n", "})\n", "\n", "df[\"tokens\"] = df[\"params\"] * actual_tokens_per_param\n", "\n", "df[\"training_secs\"] = df[\"tokens\"] / df[\"tokens_per_sec_per_gpu\"] / gpus_for_training\n", "df[\"training_weeks\"] = df[\"training_secs\"] / secs_per_week\n", "\n", "df\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Older Guesstimates"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import pandas\n", "\n", "sns.set_style(\"whitegrid\")\n", "\n", "# model_sizes = np.array([2, 8, 16, 33, 64, 67, 70, 128, 175, 1000, 2000, 2100, 3000]) * 1e9\n", "model_sizes = np.array([34, 70]) * 1e9\n", "#model_sizes = np.array([64, 128]) * 1e9\n", "\n", "# 8x A100 80GB\n", "# cost_per_node_per_year = 70e3\n", "# # Assuming fp16\n", "# # https://www.nvidia.com/content/dam/en-zz/Solutions/Data-Center/a100/pdf/nvidia-a100-datasheet-us-nvidia-1758950-r4-web.pdf\n", "# max_flop_per_sec_per_gpu = 312e12\n", "# utilization = 0.5\n", "# flop_per_sec_per_gpu = max_flop_per_sec_per_gpu * utilization\n", "\n", "# H100\n", "cost_per_node_per_year = 70e3 * 2\n", "# Assumes some fp8 training (fp16 is ~3x faster than A100, fp8 is ~6x faster)\n", "max_flop_per_sec_per_gpu = 312e12 * 3\n", "utilization = 0.4\n", "flop_per_sec_per_gpu = max_flop_per_sec_per_gpu * utilization\n", "\n", "total_flop_per_node_per_year = flop_per_sec_per_gpu * secs_per_year * gpus_per_node\n", "\n", "cost_per_flop = cost_per_node_per_year / total_flop_per_node_per_year \n", "\n", "token_counts = []\n", "training_costs = []\n", "single_node_training_months = []\n", "\n", "cluster_fraction_to_training_months = {\n", "    0.5: [],\n", "    0.66: [],\n", "    0.75: [],\n", "    1: [],\n", "}\n", "\n", "for params in model_sizes:\n", "    tokens = params * actual_tokens_per_param\n", "    token_counts.append(tokens)\n", "\n", "    training_flops = 6 * params * tokens\n", "    training_cost = training_flops * cost_per_flop\n", "    training_costs.append(training_cost)\n", "\n", "    effective_flop_per_sec_per_node = flop_per_sec_per_gpu * gpus_per_node \n", "    training_months_on_single_node = training_flops / effective_flop_per_sec_per_node / secs_per_month\n", "    single_node_training_months.append(training_months_on_single_node)\n", "\n", "    for cluster_fraction in cluster_fraction_to_training_months:\n", "        cluster_fraction_nodes = cluster_fraction * total_cluster_nodes\n", "        cluster_fraction_training_months = training_months_on_single_node / cluster_fraction_nodes\n", "        cluster_fraction_to_training_months[cluster_fraction].append(cluster_fraction_training_months)\n", "\n", "token_counts = np.array(token_counts)\n", "training_costs = np.array(training_costs)\n", "single_node_training_months = np.array(single_node_training_months)\n", "single_gpu_training_hours = single_node_training_months * 30.5 * 24 * gpus_per_node\n", "\n", "df = pandas.DataFrame({\n", "    \"params\": model_sizes,\n", "    \"tokens\": token_counts,\n", "    \"train_cost\": training_costs,\n", "    \"single_node_training_months\": single_node_training_months,\n", "    \"single_gpu_training_hours\": single_gpu_training_hours,\n", "})\n", "\n", "for cluster_fraction in cluster_fraction_to_training_months:\n", "    df[f\"cluster_fraction_{cluster_fraction}_training_weeks\"] = cluster_fraction_to_training_months[cluster_fraction]\n", "    df[f\"cluster_fraction_{cluster_fraction}_training_weeks\"] *= 4.5\n", "\n", "print(df)\n", "\n", "\n", "# fig, ax = plt.subplots()\n", "# ax.plot(model_sizes, token_counts, '.-')\n", "# ax.set_xscale(\"log\")\n", "# ax.set_yscale(\"log\")\n", "# ax.set_xlabel(\"model size\")\n", "# ax.set_xlabel(\"tokens\")\n", "\n", "# fig, ax = plt.subplots()\n", "# ax.plot(model_sizes, training_costs, '.-')\n", "# ax.set_xscale(\"log\")\n", "# ax.set_yscale(\"log\")\n", "# ax.set_xlabel(\"model size\")\n", "# ax.set_ylabel(\"training cost\")\n", "\n", "# fig, ax = plt.subplots()\n", "# ax.plot(model_sizes, single_node_training_months, '.-')\n", "# ax.set_xscale(\"log\")\n", "# ax.set_yscale(\"log\")\n", "# ax.set_xlabel(\"model size\")\n", "# ax.set_ylabel(\"single node training months\")\n", "\n", "fig, ax = plt.subplots()\n", "\n", "for cluster_fraction in cluster_fraction_to_training_months:\n", "    training_weeks = df[f\"cluster_fraction_{cluster_fraction}_training_weeks\"]\n", "    ax.plot(model_sizes, training_weeks, '.-', label=f\"{cluster_fraction}\")\n", "    # ax.set_xscale(\"log\")\n", "    # ax.set_yscale(\"log\")\n", "    ax.set_xlabel(\"model size\")\n", "    ax.set_ylabel(f\"cluster fraction training weeks\")\n", "\n", "ax.legend()\n", "\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas\n", "\n", "def format_size(num, round_digits):\n", "    def format_num(x):\n", "        return str(round(x, round_digits))\n", "\n", "    if abs(num) >= 1e12:\n", "        return format_num(num/1e12) + 'T'\n", "    if abs(num) >= 1e9:\n", "        return format_num(num/1e9) + 'G'\n", "    elif abs(num) >= 1e6:\n", "        return format_num(num/1e6) + 'M'\n", "    elif abs(num) >= 1e3:\n", "        return format_num(num/1e3) + 'K'\n", "    else:\n", "        return format_num(num)\n", "        \n", "\n", "df = pandas.DataFrame(\n", "   zip( # model_sizes.astype(int), \n", "        # token_counts.astype(int), \n", "        # training_costs.astype(int),\n", "        [format_size(s, 1).replace(\".0\", \"\") for s in model_sizes],\n", "        [format_size(t, 1) for t in token_counts],\n", "        training_costs,\n", "        ),\n", "    columns=[\"size\", \"tokens\", \"train cost\"]\n", ")\n", "\n", "print(f\"Assuming {utilization} utilization\")\n", "df.style.format({\n", "    #\"size\": \"{:.2e}\",\n", "    #\"tokens\": \"{:.1e}\",\n", "    \"train cost\": \"${:,.0f}\",\n", "})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["x = zip(# model_sizes.astype(int), \n", "        # token_counts.astype(int), \n", "        # training_costs.astype(int),\n", "        list(map(format_size, model_sizes)),\n", "        list(map(format_size, token_counts)),\n", "        training_costs,\n", "        )\n", "next(x)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4, "vscode": {"interpreter": {"hash": "31f2aee4e71d21fbe5cf8b01ff0e069b9275f58929596ceb00d14d90e3e16cd6"}}}, "nbformat": 4, "nbformat_minor": 2}