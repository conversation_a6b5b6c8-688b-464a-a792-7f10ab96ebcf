{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["Qualitatively compare different retrieval methods on a few cases from our repo."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "from research.retrieval.retrieval_database import RetrievalDatabase\n", "# from research.retrieval.scorers.recency_scorer import RecencyScorer\n", "from research.model_server.launch_model_server import get_docs_from_files\n", "from research.retrieval.chunking_functions import ScopeAwareChunker, LineLevelChunker\n", "from research.core.model_input import ModelInput\n", "\n", "from augment.research.retrieval.retrieval_database import RetrievalDatabase\n", "from augment.research.retrieval.scorers.recency_scorer import RecencyScorer\n", "from augment.research.model_server.launch_model_server import get_docs_from_files\n", "from augment.research.retrieval.chunking_functions import ScopeAwareChunker, LineLevelChunker\n", "from augment.research.core.model_input import ModelInput\n", "\n", "from augment.research.retrieval.scorers.scoring_interface import (\n", "    RetrievalDatabaseScorer,\n", "    get_scorer,\n", "    register_scorer,\n", ")\n", "\n", "from augment.research.retrieval.prompt_formatters import SimpleQueryFormatter, SimpleDocumentFormatter\n", "\n", "docs = get_docs_from_files(Path(\"/home/<USER>/augment\"), [\".py\"])\n", "\n", "chunker = ScopeAwareChunker(max_lines_per_chunk=50)\n", "# chunker = LineLevelChunker(max_lines_per_chunk=40)  # important for ethanol\n", "\n", "retrieval_dbs = {}\n", "\n", "# BM25 retriever\n", "bm25_scorer = get_scorer(\"bm25\")\n", "bm25_scorer.load()\n", "bm25_db = RetrievalDatabase(chunker, bm25_scorer)\n", "bm25_db.add_docs(docs)\n", "retrieval_dbs[\"bm25\"] = bm25_db\n", "\n", "# Diff-based retriever\n", "diff_scorer = get_scorer(\"diff_boykin\")\n", "diff_scorer.load()\n", "diff_db = RetrievalDatabase(chunker, diff_scorer)\n", "diff_db.add_docs(docs)\n", "retrieval_dbs[\"diff_boykin\"] = diff_db\n", "\n", "# Recency retriever\n", "recency_scorer = RecencyScorer()\n", "recency_scorer.load()\n", "recency_db = RetrievalDatabase(chunker, recency_scorer)\n", "recency_db.add_docs(docs)\n", "retrieval_dbs[\"recency\"] = recency_db\n", "\n", "# Perplexity distilled retriever (Ethanol)\n", "# from augment.research.retrieval.scorers.dense_scorer import Contrastive_350M_Scorer\n", "# ppl_scorer = Contrastive_350M_Scorer(\n", "#     checkpoint=\"/mnt/efs/augment/checkpoints/ethanol/ethanol3-01.1_b8192_w8_tg0.01\",\n", "#     query_formatter=SimpleQueryFormatter(max_lines=20),\n", "#     document_formatter=SimpleDocumentFormatter(),\n", "# )\n", "# ppl_scorer.load()\n", "# ppl_db = RetrievalDatabase(\n", "#     LineLevelChunker(max_lines_per_chunk=40),\n", "#     ppl_scorer\n", "# )\n", "# ppl_db.add_docs(docs)\n", "# retrieval_dbs[\"ppl\"] = ppl_db\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Define some retrieval examples\n", "\n", "EXAMPLES = [\n", "    {\n", "        \"id\": 0,\n", "        \"description\": \"look for a unit test of the the given function\",\n", "        \"query\": '''\n", "            def get_files(self) -> list[Document]:\n", "                \"\"\"Returns all the stored files, as Documents.\"\"\"\n", "                docs = []\n", "                for path in self.storage_path.glob(\"*.json\"):\n", "                    file_id = path.stem\n", "                    assert file_id in self.existing_ids\n", "                    with path.open(\"r\", encoding=\"utf8\") as f:\n", "                        data = json.load(f)\n", "                        doc = Document(\n", "                            id=file_id,\n", "                            text=data[\"contents\"],\n", "                            path=data[\"path\"],\n", "                        )\n", "                        docs.append(doc)\n", "                return docs\n", "            ''',\n", "        \"target_chunks\": [\n", "            {\n", "            \"path\": \"research/model_server/tests/test_file_store.py\",\n", "            \"text\": '''\n", "                \"\"\"Test the server's file store.\"\"\"\n", "                class TestFileStore(unittest.TestCase):\n", "                    \"\"\"Test the FileStore.\"\"\"\n", "                    def test_get_files(self):\n", "                        \"\"\"Test get_files() method.\"\"\"\n", "                        records = {\n", "                            self.file_id: {\"text\": self.contents, \"path\": self.path},\n", "                            self.file_id2: {\"text\": self.contents2, \"path\": self.path2},\n", "                            self.file_id3: {\"text\": self.contents3, \"path\": self.path3},\n", "                        }\n", "\n", "                        with tempfile.TemporaryDirectory() as temp_dir:\n", "                            store = FileStore(Path(temp_dir))\n", "                            for file_id, record in records.items():\n", "                                assert store.add_file(file_id, record[\"path\"], record[\"text\"])\n", "\n", "                            docs = store.get_files()\n", "                            self._compare_records_and_docs(records, docs)\n", "                ''',\n", "            }\n", "        ],\n", "    },\n", "    {\n", "        \"id\": 1,\n", "        \"description\": \"look for a function given a unit test\",\n", "        \"query\": '''\n", "            def test_find_missing(self):\n", "                \"\"\"Test find_missing() method.\"\"\"\n", "                with tempfile.TemporaryDirectory() as temp_dir:\n", "                    store = FileStore(Path(temp_dir))\n", "                    assert store.add_file(self.file_id, self.path, self.contents)\n", "                    assert store.add_file(self.file_id2, self.path2, self.contents2)\n", "\n", "                    missing = store.find_missing([self.file_id, self.file_id2, self.file_id3])\n", "                    assert missing == [self.file_id3]\n", "        ''',\n", "        \"target_chunks\": [\n", "            {\n", "            \"path\": \"research/model_server/file_store.py\",\n", "            \"text\": '''\n", "                \"\"\"A persistent store of files for the server.\n", "\n", "                Meant to be used for storing files uploaded by the extension, so they persist\n", "                across server launches.\n", "                \"\"\"\n", "                class FileStore:\n", "                    \"\"\"A persistent store of files.\n", "\n", "                    Comparison with terminology used elsewhere:\n", "                    * blob = a source file that we want to store (here called a file)\n", "                    * blob name = the unique file ID (a hash of the file's contents)\n", "                    \"\"\"\n", "                    def find_missing(self, file_ids: Iterable[str]) -> list[str]:\n", "                        \"\"\"Returns which of the given file IDs are not stored.\"\"\"\n", "                        return list(set(file_ids) - self.existing_ids)\n", "            ''',\n", "            },\n", "        ],\n", "    },\n", "    {\n", "        \"id\": 2,\n", "        \"description\": \"implement a new filtering function in a data pipeline\",\n", "        \"query\": '''\n", "            def filter_by_size(spark, df):\n", "                \"\"\"Filter on total size of text.\n", "\n", "                Args:\n", "                    spark: A Spark session\n", "                    df: Spark DataFrame\n", "\n", "                Returns:\n", "                    Filtered dataset.\n", "                \"\"\"\n", "        ''',\n", "        \"target_chunks\": [\n", "            {\n", "            \"path\": \"research/data/spark/pipelines/stages/the_stack.py\",\n", "            \"text\": '''\n", "                def _filter_by_alphanum_and_line_length(spark, df):\n", "                    \"\"\"Filter on alphanum and line length being in reasonable ranges.\n", "\n", "                    Args:\n", "                        spark: A Spark session\n", "                        df: Spark DataFrame\n", "\n", "                    Returns:\n", "                        Filtered dataset.\n", "                    \"\"\"\n", "                    spark.sparkContext.setJobDescription(\"Filter by alphanum, line length\")\n", "\n", "                    # StarCoder dataset doesn't have these columns, and we want to keep going if\n", "                    # they're missing\n", "                    if (\n", "                        \"avg_line_length\" in df.columns\n", "                        and \"max_line_length\" in df.columns\n", "                        and \"alphanum_fraction\" in df.columns\n", "                    ):\n", "                        return df.filter(\n", "                            (col(\"avg_line_length\") <= 100)\n", "                            & (col(\"max_line_length\") <= 1000)\n", "                            & (col(\"alphanum_fraction\") >= 0.25)\n", "                            & (col(\"alphanum_fraction\") < 0.9)\n", "                        )\n", "                    else:\n", "                        logging.warning(\n", "                            \"Missing filtering columns (which appear in The Stack), \"\n", "                            \"will not filter by alphanum and line length\"\n", "                        )\n", "                        return df\n", "            '''\n", "            },\n", "        ],\n", "    },\n", "    {\n", "        \"id\": 3,\n", "        \"description\": \"implement a new chunker that uses random character locations\",\n", "        \"query\": '''\n", "            class RandomChunker(Chunker):\n", "                \"\"\"Chunks up text randomly.\"\"\"\n", "        ''',\n", "        \"target_chunks\": [\n", "            {\n", "                \"path\": \"research/retrieval/libraries/chunking_functions.py\",\n", "                \"text\": '''\n", "                    @register_chunker(\"line_level\")\n", "                    class LineLevelChunker(Chunker):\n", "                        \"\"\"Split a document into chunks based on line-level chunking.\"\"\"\n", "\n", "                        def __init__(self, max_lines_per_chunk):\n", "                            if max_lines_per_chunk <= 0:\n", "                                raise ValueError(\n", "                                    f\"max_lines_per_chunk must be positive, got {max_lines_per_chunk}\"\n", "                                )\n", "                            self.max_lines_per_chunk = max_lines_per_chunk\n", "                ''',\n", "            },\n", "            {\n", "                \"path\": \"research/retrieval/libraries/chunking_functions.py\",\n", "                \"text\": '''\n", "                    @register_chunker(\"scope_aware\")\n", "                    class ScopeAwareChunker(Chunker):\n", "                        \"\"\"Split a document into chunks based on scope-aware chunking.\"\"\"\n", "\n", "                        def __init__(\n", "                            self,\n", "                            max_lines_per_chunk: int,\n", "                            parse_errored_root: bool = False,\n", "                        ):\n", "                            \"\"\"Con<PERSON><PERSON>ctor.\n", "\n", "                            Args:\n", "                            max_lines_per_chunk: Max number of lines for each chunk.\n", "                            parse_errored_root: If True, allow parse errored root node.\n", "                            \"\"\"\n", "                            self.parser = parsing.ScopeTreeParser(parse_errored_root=parse_errored_root)\n", "                            self.max_lines_per_chunk = max_lines_per_chunk\n", "                ''',\n", "            },\n", "            {\n", "                \"path\": \"research/retrieval/libraries/types.py\",\n", "                \"text\": '''\n", "                    class Chunker(ABC):\n", "                    \"\"\"Splits documents into chunks of text.\"\"\"\n", "\n", "                    @abstractmethod\n", "                    def split_into_chunks(self, doc: Document) -> Optional[list[Chunk]]:\n", "                        \"\"\"This function takes a document and returns a list of chunks.\n", "\n", "                        Returns None if splitting failed.\n", "                        \"\"\"\n", "                ''',\n", "            },\n", "        ]\n", "    },\n", "]\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["########################################################################\n", "############################ Analysis ##################################\n", "########################################################################\n", "\n", "from augment.research.core.model_input import ModelInput\n", "from augment.research.retrieval.types import Chunk\n", "from termcolor import colored\n", "import numpy as np\n", "\n", "############################### Functions ###############################\n", "\n", "def print_chunk(chunk: Chunk, reference_retriever: str, retrievals: dict[str, tuple[int, float]], print_text: bool):\n", "    from_line = chunk.line_offset\n", "    to_line = chunk.line_offset + chunk.length_in_lines - 1\n", "\n", "    output = \"\"\n", "\n", "    rank, score = retrievals[reference_retriever]\n", "    output += f\"{reference_retriever}_rank={rank:02d} {reference_retriever}_score={score:.2f} \"\n", "\n", "    for retriever, (rank, score) in retrievals.items():\n", "        if retriever == reference_retriever:\n", "            continue\n", "        # output += f\"{retriever}_rank={rank:02d} {retriever}_score={score:.2f} \"\n", "        output += f\"{retriever}_rank={rank:02d} \"\n", "\n", "    output += f\"parent={chunk.parent_doc.path} lines=[{from_line}, {to_line}]\"\n", "\n", "    print(colored(output, \"blue\"))\n", "    if print_text:\n", "        print(chunk.text)\n", "\n", "def find_chunk_rank_and_score(target_chunk, chunks, scores):\n", "    for rank, (chunk, score) in enumerate(zip(chunks, scores)):\n", "        if chunk == target_chunk:\n", "            return rank, score\n", "    return -1, -1\n", "\n", "def compute_softmax_scores(scores, skip_first):\n", "    \"\"\"Compute softmax scores, skipping leading entries (presumably ground truth).\"\"\"\n", "    masked_scores = np.array(scores)\n", "    masked_scores[:skip_first] = 0.\n", "    score_shift = np.max(masked_scores)\n", "    exp_scores = np.exp(masked_scores - score_shift)\n", "    softmax_scores = list(exp_scores / np.sum(exp_scores))\n", "    return softmax_scores\n", "\n", "############################## Data ###########################################\n", "\n", "# text = '''\n", "#     def get_files(self) -> list[Document]:\n", "#         \"\"\"Returns all the stored files, as Documents.\"\"\"\n", "#         docs = []\n", "#         for path in self.storage_path.glob(\"*.json\"):\n", "#             file_id = path.stem\n", "#             assert file_id in self.existing_ids\n", "#             with path.open(\"r\", encoding=\"utf8\") as f:\n", "#                 data = json.load(f)\n", "#                 doc = Document(\n", "#                     id=file_id,\n", "#                     text=data[\"contents\"],\n", "#                     path=data[\"path\"],\n", "#                 )\n", "#                 docs.append(doc)\n", "#         return docs\n", "#     '''\n", "\n", "# text = '''\n", "#     def get_files(self) -> list[Document]:\n", "#         \"\"\"Returns all the stored files, as Documents.\"\"\"\n", "#         docs = []\n", "#         for path in self.storage_path.glob(\"*.json\"):\n", "#             file_id = path.stem\n", "#             assert file_id in self.existing_ids\n", "#             with path.open(\"r\", encoding=\"utf8\") as f:\n", "#                 data = json.load(f)\n", "#                 doc = Document(\n", "#                     id=file_id,\n", "#     '''\n", "\n", "# text = '''\n", "#     def test_find_missing(self):\n", "#         \"\"\"Test find_missing() method.\"\"\"\n", "#         with tempfile.TemporaryDirectory() as temp_dir:\n", "#             store = FileStore(Path(temp_dir))\n", "#             assert store.add_file(self.file_id, self.path, self.contents)\n", "#             assert store.add_file(self.file_id2, self.path2, self.contents2)\n", "\n", "#             missing = store.find_missing([self.file_id, self.file_id2, self.file_id3])\n", "#             assert missing == [self.file_id3]\n", "# '''\n", "\n", "text = '''\"\"\"A system based on the Chinchilla model.\"\"\"\n", "\n", "class ChinchillaSystem(AbstractSystem):\n", "    '''\n", "\n", "result_print_filter = None\n", "result_path_filter = None\n", "\n", "# result_print_filter = \"from_yaml_config\"\n", "# result_path_filter = \"/test_file_store.py\"\n", "\n", "example_id = 2\n", "example = [example for example in EXAMPLES if example[\"id\"] == example_id][0]\n", "text = example[\"query\"]\n", "target_paths = [chunk[\"path\"] for chunk in example[\"target_chunks\"]]\n", "\n", "print(colored(\"======= Query =======\", \"blue\"))\n", "print(colored(f\"Description: {example['description']}\", \"blue\"))\n", "print(colored(\"Target paths: \" + \", \".join(target_paths), \"blue\"))\n", "print(text)\n", "\n", "# tab_switches = [\"research/model_server/tests/test_file_store.py\"]\n", "# tab_switches = []\n", "tab_switches = target_paths\n", "\n", "show_top_k = 10\n", "\n", "# Which retrievers to sort results by\n", "# reference_retrievers = [\"ppl\", \"diff_boykin\"]\n", "reference_retrievers = [\"diff_boykin\"]\n", "\n", "################################# Analysis #####################################\n", "\n", "for reference_retriever in reference_retrievers:\n", "    print(colored(f\"\\n======= Results for {reference_retriever} =======\", \"blue\"))\n", "    model_input = ModelInput(prefix=text, extra={\"tab_switch_events\": tab_switches})\n", "\n", "    retrievals: dict[str, tuple[list[Chunk], list[float]]] = {\n", "        name: db.query(model_input)\n", "        for name, db in retrieval_dbs.items()\n", "    }\n", "\n", "    chunks, scores = retrievals[reference_retriever]\n", "\n", "    chunks = chunks[:show_top_k]\n", "    scores = scores[:show_top_k]\n", "\n", "    for rank, (chunk, score) in enumerate(zip(chunks, scores)):\n", "        chunk_retrievals = {}\n", "\n", "        for retriever, (ret_chunks, ret_scores) in retrievals.items():\n", "            ret_rank, ret_score = find_chunk_rank_and_score(chunk, ret_chunks, ret_scores)\n", "            chunk_retrievals[retriever] = (ret_rank, ret_score)\n", "\n", "        print_text = False\n", "        if str(chunk.parent_doc.path) in target_paths:\n", "            print_text = True\n", "        # print_text = True\n", "\n", "        print_chunk(\n", "            chunk, \n", "            reference_retriever=reference_retriever,\n", "            retrievals=chunk_retrievals,\n", "            print_text=print_text)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "chunks, scores = retrievals[\"ppl\"]\n", "# chunks, scores = retrievals[\"diff_boykin\"]\n", "\n", "normalized_scores = (scores - np.mean(scores)) / np.std(scores)\n", "\n", "softmax_scores = np.exp(normalized_scores) / np.sum(np.exp(normalized_scores), axis=0)\n", "\n", "plt.hist(normalized_scores, bins=60)\n", "# plt.hist(softmax_scores, bins=60)\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["list(sorted(normalized_scores, reverse=True))[:10]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from augment.research.retrieval.scorers.recency_scorer import RecencyScorer\n", "recency_scorer = RecencyScorer()\n", "recency_scorer.load()\n", "recency_db = RetrievalDatabase(chunker, recency_scorer)\n", "recency_db.add_docs(docs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["example_id = 0\n", "example = [example for example in EXAMPLES if example[\"id\"] == example_id][0]\n", "text = example[\"query\"]\n", "target_paths = [chunk[\"path\"] for chunk in example[\"target_chunks\"]]\n", "\n", "print(colored(\"======= Query =======\", \"blue\"))\n", "print(colored(f\"Description: {example['description']}\", \"blue\"))\n", "print(colored(\"Target paths: \" + \", \".join(target_paths), \"blue\"))\n", "print(text)\n", "\n", "# tab_switches = [\"research/gpt-neox/tests/model/test_model_masking.py\"]\n", "tab_switches = []\n", "\n", "show_top_k = 15"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}