{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "from pathlib import Path\n", "import json\n", "\n", "eval_root = Path(\"/mnt/efs/augment/user/guy/eval\")\n", "checkpoints_root = Path(\"/mnt/efs/augment/user/guy/checkpoints\")\n", "\n", "experiments = [\"starcoderbase-3b-moe-python\", \"starcoderbase-7b-moe-python\"]\n", "tasks = [\"humaneval\", \"humaneval_1shot\", \"humaneval_fim_light\"]\n", "\n", "fig, axes = plt.subplots(nrows=1, ncols=len(experiments), figsize=(6*len(tasks), 7.5))\n", "\n", "for i, experiment_name in enumerate(experiments):\n", "    ax = axes[i]\n", "    for task in tasks:\n", "        all_steps = []\n", "        all_accs = []\n", "        for checkpoint in sorted(\n", "            checkpoints_root.glob(f\"{experiment_name}-*\")\n", "        ):\n", "            steps = int(checkpoint.name.split(\"-\")[-1].replace(\"steps\", \"\").replace(\"k\", \"000\"))\n", "            eval_path = eval_root / task / checkpoint.name\n", "\n", "            jsonl_paths = list(eval_path.glob(\"*.jsonl\"))\n", "            if len(jsonl_paths) != 1:\n", "                print(f\"Warning: Expected only one jsonl file, found {len(jsonl_paths)} in {eval_path}\")\n", "                continue\n", "            \n", "            jsonl_path = jsonl_paths[0]\n", "            results = json.load(jsonl_path.open(\"r\"))\n", "            pass_1 = results[\"metrics\"][\"pass_at_k\"][\"pass@1\"]\n", "\n", "            all_steps.append(steps)\n", "            all_accs.append(pass_1)\n", "\n", "        ax.plot(all_steps, all_accs, \".-\", label=task)\n", "    ax.set_title(experiment_name)\n", "    ax.grid(True)\n", "    ax.set_xlabel(\"step\")\n", "    ax.set_ylabel(\"pass@1\")\n", "    ax.legend()\n", "\n", "fig.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.17"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}