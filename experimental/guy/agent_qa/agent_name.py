"""Functions for generating random agent names with a tool theme."""

import random

# A collection of tool names that can be used as agent names
TOOL_NAMES = [
    "Anvil",
    "<PERSON>er",
    "Awl",
    "<PERSON>xe",
    "<PERSON><PERSON>",
    "<PERSON><PERSON>",
    "<PERSON><PERSON>",
    "<PERSON><PERSON>",
    "<PERSON><PERSON>",
    "<PERSON>mp<PERSON>",
    "Crowbar",
    "Drill",
    "File",
    "Forge",
    "Grinder",
    "<PERSON><PERSON>aw",
    "Hammer",
    "Lathe",
    "Level",
    "Mallet",
    "Pickaxe",
    "Planer",
    "<PERSON>liers",
    "Prybar",
    "Punch",
    "Rasp",
    "Router",
    "Ruler",
    "Sandpaper",
    "Saw",
    "Screwdriver",
    "Spanner",
    "<PERSON>s",
    "Torch",
    "<PERSON><PERSON>el",
    "Vise",
    "Wedge",
    "Wrench",
]


def generate_agent_name() -> str:
    """Generate a random agent name from the list of tool names.

    Returns:
        str: A randomly selected tool name to use as an agent name.
    """
    return random.choice(TOOL_NAMES)
