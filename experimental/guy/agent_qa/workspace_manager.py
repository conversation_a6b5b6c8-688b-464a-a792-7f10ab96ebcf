"""Tool definitions and utilities."""

from pathlib import Path
from typing import Iterable, Optional

import pathspec
from base.blob_names.python.blob_names import get_blob_name
from experimental.guy.agent_qa.file_utils import safe_read_file
from experimental.guy.agent_qa.prototyping_client import (
    AugmentPrototypingClient,
    UploadContent,
)
from research.core.diff_utils import compute_repo_diff, Repository, File
from base.diff_utils.diff_utils import File as BaseFile
from unidiff import PatchSet
from experimental.guy.agent_qa.workspace_cache import WorkspaceCache
from experimental.guy.agent_qa.user_confirmation import (
    <PERSON>rConfirmationProvider,
    InteractiveUserConfirmationProvider,
)

import multiprocessing
import subprocess
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor


def _compute_blob_name_worker(args):
    """Worker function for computing blob names in parallel."""
    relative_path, content = args
    return get_blob_name(relative_path, content)


def _read_file_worker(args):
    """Worker function for reading files in parallel."""
    path, relative_path = args
    try:
        content = path.read_bytes()
        return relative_path, content
    except (OSError, IOError):
        return None


class PathFilter:
    """Decides whether to accept a path for some purpose."""

    def accepts(self, path: Path) -> bool:
        """Returns whether the path is accepted."""
        raise NotImplementedError()


class GlobPathFilter(PathFilter):
    """Accepts paths that match a glob pattern."""

    def __init__(self, pattern: str):
        self.pattern = pattern

    def accepts(self, path: Path) -> bool:
        return path.match(self.pattern)


class TextFileFilter(PathFilter):
    """Accepts text files, excluding those in gitignore/augmentignore and those over size limit."""

    # Common text file extensions
    TEXT_EXTENSIONS = {
        ".txt",
        ".md",
        ".py",
        ".js",
        ".ts",
        ".jsx",
        ".tsx",
        ".svelte",
        ".java",
        ".c",
        ".cc",
        ".cpp",
        ".h",
        ".hpp",
        ".cs",
        ".go",
        ".rs",
        ".swift",
        ".kt",
        ".scala",
        ".rb",
        ".php",
        ".html",
        ".css",
        ".scss",
        ".sass",
        ".less",
        ".json",
        ".yaml",
        ".yml",
        ".toml",
        ".ini",
        ".cfg",
        ".conf",
        ".sh",
        ".bash",
        ".zsh",
        ".fish",
        ".sql",
        ".graphql",
        ".proto",
        ".xml",
        ".plist",
        ".properties",
        ".gradle",
        ".pom",
        ".bazel",
        ".bzl",
        ".ini",
        ".mod",
        ".hs",
    }

    FILES_LOWERCASE = [name.lower() for name in ("Readme", "BUILD", "Makefile")]

    def __init__(self, root: Path, max_file_size_bytes: int = 1024 * 1024):
        """Initialize the filter.

        Args:
            root: Root directory containing .gitignore and .augmentignore
            max_file_size_bytes: Maximum file size in bytes (default 1MB)
        """
        self.root = root
        self.max_file_size_bytes = max_file_size_bytes
        self.fixed_ignores = [".git"]
        self.gitignore_spec = self._load_ignore_spec(".gitignore")
        self.augmentignore_spec = self._load_ignore_spec(".augmentignore")
        # Cache for gitignore pattern matching results
        self._ignore_cache = {}

    def _load_ignore_spec(self, filename: str) -> Optional[pathspec.PathSpec]:
        """Load and parse an ignore file if it exists."""
        ignore_file = self.root / filename
        if not ignore_file.exists():
            return None

        with open(ignore_file) as f:
            return pathspec.PathSpec.from_lines("gitwildmatch", f)

    def accepts(self, path: Path) -> bool:
        """Returns whether the path is accepted.

        A path is accepted if:
        1. It has a text file extension or a text filename
        2. It's not ignored by .gitignore or .augmentignore
        3. It's not larger than max_file_size_bytes
        """
        # Check if it's a text file based on extension and name
        if (
            path.suffix not in self.TEXT_EXTENSIONS
            and path.name.lower() not in self.FILES_LOWERCASE
        ):
            return False

        # Check if file is readable and get its size
        try:
            file_size = path.stat().st_size
        except (OSError, IOError):
            return False

        # Get path relative to root for gitignore matching
        try:
            relative_path = path.relative_to(self.root)
        except ValueError:
            return False

        # Check fixed ignores
        if any(path.name == ignore for ignore in self.fixed_ignores):
            return False

        # Check cached ignore results first
        str_path = str(relative_path)
        if str_path in self._ignore_cache:
            return not self._ignore_cache[str_path]

        ignored = False
        # Check gitignore patterns
        if self.gitignore_spec and self.gitignore_spec.match_file(str_path):
            ignored = True
        # Check augmentignore patterns
        elif self.augmentignore_spec and self.augmentignore_spec.match_file(str_path):
            ignored = True

        # Cache the result
        self._ignore_cache[str_path] = ignored

        # Check file size
        if not ignored and file_size > self.max_file_size_bytes:
            ignored = True
            self._ignore_cache[str_path] = ignored

        return not ignored


class WorkspaceManager:
    root: Path

    def update(self):
        """Updates the current state of the workspace, and upload missing files.

        Should be called whenever the files in the workspace change.
        """
        raise NotImplementedError()

    def get_blob_names(self) -> list[str]:
        """Returns the blob names in the workspace."""
        raise NotImplementedError()

    def workspace_path(self, path: Path) -> Path:
        """Resolves a path to an absolute path in the workspace."""
        raise NotImplementedError()

    def container_path(self, path: Path) -> Path:
        """Resolves a path to an absolute path in the container workspace, if applicable.

        Args:
            path: Path to resolve
        """
        raise NotImplementedError()


class WorkspaceManagerListener:
    """Listen to workspace manager events."""

    def sync_started(self, workspace_root: Path):
        raise NotImplementedError()

    def sync_finished(self, workspace_root: Path):
        raise NotImplementedError()


class WorkspaceUpdateListener:
    """Listen to workspace update events."""

    def on_update(self, workspace_root: Path):
        """Called after workspace state is updated.

        Args:
            workspace_root: Root directory of the workspace
        """
        raise NotImplementedError()


class NotionManager(WorkspaceManager):
    """Manages Notion export files."""

    def __init__(
        self,
        augment_client: AugmentPrototypingClient,
        notion_export_root: Path,
        update_listener: Optional[WorkspaceUpdateListener] = None,
    ):
        """Initialize the manager.

        Args:
            augment_client: Client for uploading files
            notion_export_root: Root directory for Notion exports
            update_listener: Listens to workspace update events
        """
        self.notion_export_root = notion_export_root
        self.augment_client = augment_client
        self.update_listener = update_listener
        self._absolute_paths: list[Path] = []
        self._blob_names: list[str] = []
        self.update()

    def update(self):
        """Updates the current state of the Notion exports."""
        if not self.notion_export_root:
            return

        self._absolute_paths = [
            path for path in self.notion_export_root.rglob("*") if path.is_file()
        ]
        self._blob_names = [self._get_blob_name(path) for path in self._absolute_paths]
        self._upload_missing_files(self._absolute_paths)

        # Call update listener after state is updated
        if self.update_listener:
            self.update_listener.on_update(self.notion_export_root)

    def get_paths(self) -> Iterable[Path]:
        """Returns all paths in the Notion exports."""
        return self._absolute_paths

    def get_blob_names(self) -> list[str]:
        """Returns the blob names in the Notion exports."""
        return self._blob_names

    def _get_blob_path(self, path: Path) -> str:
        """Returns the blob path for a given path."""
        # Paths end with index.md, so we remove that
        return str(path.relative_to(self.notion_export_root).parent)

    def _get_blob_name(self, path: Path) -> str:
        return get_blob_name(self._get_blob_path(path), path.read_bytes())

    def _upload_missing_files(self, paths: list[Path]):
        """Uploads missing files to the server."""
        to_upload = []
        for path in paths:
            content = path.read_bytes()
            to_upload.append(
                UploadContent(content=content, path=self._get_blob_path(path))
            )
        self.augment_client.upload_blobs_as_needed(to_upload)


class WorkspaceManagerImpl(WorkspaceManager):
    """Manages the workspace files."""

    def __init__(
        self,
        augment_client: AugmentPrototypingClient,
        root: Path,
        path_filter: Optional[PathFilter] = None,
        max_file_size_bytes: int = 1024 * 1024,  # 1MB default
        listener: Optional[WorkspaceManagerListener] = None,
        update_listener: Optional[WorkspaceUpdateListener] = None,
        cache_root: Optional[Path] = None,
        confirmation_provider: Optional[UserConfirmationProvider] = None,
        container_workspace: Optional[Path] = None,
    ):
        """Initialize the workspace manager.

        Args:
            augment_client: Client for uploading files
            root: Root directory of the workspace
            path_filter: Custom path filter (defaults to TextFileFilter)
            max_file_size_bytes: Maximum file size in bytes for TextFileFilter
            listener: Listens to workspace events
            update_listener: Listens to workspace update events
            cache_root: Root directory for cache files. Cache will be stored under
                {cache_root}/blob_cache/. Defaults to ~/.augment/agent
            confirmation_provider: Provider for getting user confirmations.
                If not provided, does not ask for confirmation.
        """
        self.root = root
        self.path_filter = path_filter or TextFileFilter(root, max_file_size_bytes)
        self.augment_client = augment_client
        self.listener = listener
        self.update_listener = update_listener
        # TODO(guy) don't hard code the default cache root like this
        self._cache = WorkspaceCache(
            root, cache_root or Path.home() / ".augment" / "agent"
        )
        self._snapshots = {}  # Maps sequence numbers to workspace states
        self._next_sequence = 0  # First snapshot will be 0 (initial state)
        self._absolute_paths = []
        self._blob_names = []
        self.confirmation_provider = confirmation_provider
        self.container_workspace = container_workspace
        self.update()

    def _get_current_paths(self):
        # Try to use git ls-files if available
        git_ls_files_worked = False

        if (self.root / ".git").exists():
            try:
                # Use git ls-files for tracked files
                result = subprocess.run(
                    ["git", "ls-files", "-z"],
                    cwd=self.root,
                    capture_output=True,
                    text=True,
                    check=True,
                )
                # Split on null bytes and filter empty strings
                tracked_files = [
                    self.root / f
                    for f in result.stdout.split("\0")
                    if f and Path(self.root / f).exists()
                ]

                # Use git ls-files --others for untracked files
                result = subprocess.run(
                    ["git", "ls-files", "--others", "--exclude-standard", "-z"],
                    cwd=self.root,
                    capture_output=True,
                    text=True,
                    check=True,
                )
                # Split on null bytes and filter empty strings
                untracked_files = [
                    self.root / f
                    for f in result.stdout.split("\0")
                    if f and Path(self.root / f).exists()
                ]

                # Combine tracked and untracked files
                all_files = tracked_files + untracked_files
                git_ls_files_worked = True
            except (subprocess.CalledProcessError, FileNotFoundError):
                pass

        if not git_ls_files_worked:
            # Not a git repo or git not available, fall back to rglob
            all_files = list(self.root.rglob("*"))

        # Filter files using our path filter
        current_paths = [
            path
            for path in all_files
            if path.is_file() and self.path_filter.accepts(path)
        ]
        return current_paths

    def update(self):
        """Updates the current state of the workspace, and upload missing files.

        Should be called whenever the files in the workspace change.
        """
        if self.listener:
            self.listener.sync_started(self.root)

        current_paths = self._get_current_paths()

        # Track which files we've seen to detect removals
        seen_paths = set()
        to_upload = []
        new_blob_names = []

        # Group files by whether they need processing
        need_processing = []
        for path in current_paths:
            relative_path = self._get_blob_path(path)
            seen_paths.add(relative_path)
            mtime = path.stat().st_mtime

            # Check if file has changed
            cached_info = self._cache.get_file_info(relative_path)
            if cached_info and cached_info.mtime == mtime:
                # File hasn't changed, use cached blob name
                new_blob_names.append(cached_info.blob_name)
            else:
                # File is new or modified, needs processing
                need_processing.append((path, relative_path))

        # Process files in parallel
        if need_processing:
            # Read files in parallel using threads (I/O bound)
            with ThreadPoolExecutor() as executor:
                file_contents = list(executor.map(_read_file_worker, need_processing))

            # Filter out failed reads
            file_contents = [fc for fc in file_contents if fc is not None]

            # Compute blob names
            blob_names = map(_compute_blob_name_worker, file_contents)

            # Update cache and prepare uploads
            for (relative_path, content), blob_name in zip(file_contents, blob_names):
                new_blob_names.append(blob_name)
                path = self.root / relative_path
                self._cache.update_file(relative_path, blob_name, path.stat().st_mtime)
                to_upload.append(UploadContent(content=content, path=relative_path))

        # Remove files that no longer exist
        for cached_path in list(self._cache._files.keys()):
            if cached_path not in seen_paths:
                self._cache.remove_file(cached_path)

        # Save cache
        self._cache.save()

        # Update internal state
        self._absolute_paths = current_paths
        self._blob_names = new_blob_names

        # Upload any new/modified files
        if to_upload:
            self.augment_client.upload_blobs_as_needed(to_upload)

        if self.listener:
            self.listener.sync_finished(self.root)

        # Call update listener after state is updated
        if self.update_listener:
            self.update_listener.on_update(self.root)

    def workspace_path(self, path: Path | str) -> Path:
        """Given a path, possibly in a container workspace, return the absolute local path."""
        path = Path(path)
        if not path.is_absolute():
            return self.root / path
        if self.container_workspace and path.is_relative_to(self.container_workspace):
            return self.root / path.relative_to(self.container_workspace)
        return path

    def container_path(self, path: Path | str) -> Path:
        """Given a path, possibly in the local workspace, return the absolute container path.
        If there is no container workspace, return the absolute local path.
        """
        path = Path(path)
        if not path.is_absolute():
            if self.container_workspace:
                return self.container_workspace / path
            else:
                return self.root / path
        if self.container_workspace and path.is_relative_to(self.root):
            return self.container_workspace / path.relative_to(self.root)
        return path

    def get_paths(self, relative: bool = False) -> Iterable[Path]:
        """Returns all paths in the workspace."""
        if relative:
            return [path.relative_to(self.root) for path in self._absolute_paths]
        else:
            return self._absolute_paths

    def get_blob_names(self) -> list[str]:
        """Returns the blob names in the workspace."""
        return self._blob_names

    def _get_blob_path(self, path: Path) -> str:
        """Returns the blob path for a given path."""
        return str(path.relative_to(self.root))

    def _upload_missing_files(self, paths: list[Path]):
        """Uploads missing files to the server."""
        to_upload = []
        for path in paths:
            content = path.read_bytes()
            to_upload.append(
                UploadContent(content=content, path=self._get_blob_path(path))
            )
        self.augment_client.upload_blobs_as_needed(to_upload)

    def _get_workspace_state(
        self, paths: list[Path], ignore_errors: bool
    ) -> dict[str, str]:
        result: dict[str, str] = {}
        for path in paths:
            try:
                text = safe_read_file(path)
                if text is None:
                    continue
                result[self._get_blob_path(path)] = text
            except (OSError, IOError):
                if not ignore_errors:
                    raise
        return result

    def snapshot_workspace(self) -> int:
        """Takes a snapshot of current workspace state.

        Calls update() to ensure the workspace is up-to-date before taking the snapshot.

        Returns:
            int: Sequence number of the snapshot. Can be used to revert to this state later.
        """
        self.update()

        sequence = self._next_sequence
        self._snapshots[sequence] = self._get_workspace_state(
            self._absolute_paths, ignore_errors=True
        )
        self._next_sequence += 1
        return sequence

    def _resolve_sequence(self, sequence: int) -> int:
        """Resolves negative sequence numbers to count from the end.

        Args:
            sequence: Sequence number. Negative numbers count from the end.

        Returns:
            Resolved sequence number

        Raises:
            ValueError: If sequence number is invalid
        """
        if sequence >= 0:
            if sequence not in self._snapshots:
                raise ValueError(f"No snapshot with sequence {sequence}")
            return sequence

        # Convert negative index
        resolved = self._next_sequence + sequence
        if resolved < 0 or resolved not in self._snapshots:
            raise ValueError(
                f"No snapshot with sequence {sequence} (resolved to {resolved})"
            )
        return resolved

    def revert_to_snapshot(self, sequence: int):
        """Reverts the workspace to a specific snapshot.

        Args:
            sequence: The sequence number of the snapshot to revert to.
                     Negative numbers count from the end (-1 is last snapshot).
        """
        sequence = self._resolve_sequence(sequence)
        snapshot = self._snapshots[sequence]

        # Remove files that don't exist in the snapshot
        for path in self._absolute_paths:
            relative_path = self._get_blob_path(path)
            if relative_path not in snapshot:
                path.unlink()

        # Write/update files from snapshot
        for relative_path, content in snapshot.items():
            full_path = self.root / relative_path
            full_path.parent.mkdir(parents=True, exist_ok=True)
            full_path.write_text(content)

        # Update internal state
        self.update()

    def revert_to_initial_state(self, skip_confirmation: bool = False):
        """Reverts the workspace to its initial state."""
        if not self._snapshots:
            print("No snapshots exist yet, nothing to revert.")
            return

        # Get initial snapshot
        initial_snapshot = self._snapshots[0]

        # Get current state of files
        current_state = {}
        for path in self._absolute_paths:
            relative_path = self._get_blob_path(path)
            if path.exists():
                try:
                    current_state[relative_path] = path.read_text()
                except UnicodeDecodeError:
                    print(f"Failed to read {relative_path}, trying to read as bytes")
                    current_state[relative_path] = path.read_bytes()

        # Find changed files
        changed_files = []
        removed_files = []
        added_files = []

        # Check for modified and removed files
        for relative_path, initial_content in initial_snapshot.items():
            current_content = current_state.get(relative_path)
            if current_content is None:
                removed_files.append(relative_path)
            elif current_content != initial_content:
                changed_files.append(relative_path)

        # Check for added files
        for relative_path in current_state:
            if relative_path not in initial_snapshot:
                added_files.append(relative_path)

        if not (changed_files or removed_files or added_files):
            print("No changes to revert.")
            return

        # Show changes and ask for confirmation
        message = "\nThe following changes will be reverted:"
        if changed_files:
            message += "\n\nModified files:"
            for file in sorted(changed_files):
                message += f"\n  {file}"
        if added_files:
            message += "\n\nAdded files that will be removed:"
            for file in sorted(added_files):
                message += f"\n  {file}"
        if removed_files:
            message += "\n\nRemoved files that will be restored:"
            for file in sorted(removed_files):
                message += f"\n  {file}"

        if (
            not skip_confirmation
            and self.confirmation_provider
            and not self.confirmation_provider.confirm(message)
        ):
            return

        # Proceed with revert
        self.revert_to_snapshot(0)

    def _snapshot_to_repository(self, snapshot: dict[str, str]) -> Repository:
        """Converts a snapshot to a Repository object for diffing.

        Args:
            snapshot: The snapshot to convert

        Returns:
            A Repository object containing the snapshot's files
        """
        files = []
        for path, content in snapshot.items():
            files.append(BaseFile(path, content))  # Use positional args
        return Repository(files)

    def diff_snapshots(
        self,
        sequence1: int,
        sequence2: int,
        num_context_lines: int = 3,
        ignore_whitespace: bool = False,
    ) -> PatchSet:
        """Compute the diff between two snapshots.

        Args:
            sequence1: First snapshot sequence number. Negative numbers count from the end.
            sequence2: Second snapshot sequence number. Negative numbers count from the end.
            num_context_lines: Number of context lines to include in hunks
            ignore_whitespace: Whether to ignore whitespace changes

        Returns:
            A PatchSet representing the changes between snapshots

        Raises:
            ValueError: If either sequence number doesn't exist
        """
        sequence1 = self._resolve_sequence(sequence1)
        sequence2 = self._resolve_sequence(sequence2)
        repo1 = self._snapshot_to_repository(self._snapshots[sequence1])
        repo2 = self._snapshot_to_repository(self._snapshots[sequence2])

        diff = compute_repo_diff(
            repo1,
            repo2,
            num_context_lines=num_context_lines,
            ignore_whitespace=ignore_whitespace,
        )

        # print(f"Repo diff between {sequence1} and {sequence2}:")
        # print(diff)
        return diff

    def diff_with_current(
        self,
        sequence: int,
        num_context_lines: int = 3,
        ignore_whitespace: bool = False,
    ) -> PatchSet:
        """Compute the diff between a snapshot and the current workspace state.

        Args:
            sequence: Snapshot sequence number. Negative numbers count from the end.
            num_context_lines: Number of context lines to include in hunks
            ignore_whitespace: Whether to ignore whitespace changes

        Returns:
            A PatchSet representing the changes between the snapshot and current state

        Raises:
            ValueError: If sequence number doesn't exist
        """
        sequence = self._resolve_sequence(sequence)
        repo1 = self._snapshot_to_repository(self._snapshots[sequence])

        # Create repository from current state
        # Use rglob to find all files, not just ones we've seen in update()
        current_paths = self._get_current_paths()
        current_state = self._get_workspace_state(current_paths, ignore_errors=True)
        repo2 = self._snapshot_to_repository(current_state)

        return compute_repo_diff(
            repo1,
            repo2,
            num_context_lines=num_context_lines,
            ignore_whitespace=ignore_whitespace,
        )

    def get_last_turn_diff(self) -> Optional[PatchSet]:
        """Get the diff between the last two snapshots.

        Returns:
            A PatchSet representing the changes in the last turn,
            or None if there are no changes or not enough snapshots.
        """
        print(f"Number of snapshots: {len(self._snapshots)}")
        if len(self._snapshots) < 2:
            return None

        last_seq = max(self._snapshots.keys())
        diff = self.diff_snapshots(last_seq - 1, last_seq)
        return diff if diff else None

    def num_snapshots(self) -> int:
        return len(self._snapshots)

    def get_file_contents(self, snapshot_id: int, path: str | Path) -> Optional[str]:
        """Get the contents of a file from a specific snapshot without modifying the workspace.

        Args:
            snapshot_id: The snapshot ID to retrieve the file contents from.
                        Negative numbers count from the end (-1 is last snapshot).
            path: The path to the file, either absolute or relative to workspace root.

        Returns:
            The contents of the file as a string, or None if the file doesn't exist in the snapshot.

        Raises:
            ValueError: If the snapshot ID doesn't exist.
        """
        # Resolve the snapshot ID
        snapshot_id = self._resolve_sequence(snapshot_id)

        # Get the snapshot
        snapshot = self._snapshots[snapshot_id]

        # Convert path to relative path if it's absolute
        path_obj = Path(path)
        if path_obj.is_absolute():
            try:
                relative_path = str(path_obj.relative_to(self.root))
            except ValueError:
                # Path is not relative to workspace root
                return None
        else:
            relative_path = str(path_obj)

        # Return the file contents if it exists in the snapshot
        return snapshot.get(relative_path)
