"""Module for handling integration warnings."""

from typing import Optional
import sys
from termcolor import colored


class IntegrationWarnings:
    """Class to handle integration warnings."""

    def __init__(self, show_warnings: bool = True):
        """Initialize with warning state."""
        self._show_warnings = show_warnings
        self._shown_warnings = set()

    def warn_if_missing(
        self, tool_name: str, config_name: Optional[str] = None, fail: bool = False
    ):
        """Show a warning if a tool is missing credentials.

        Args:
            tool_name: Name of the tool that's missing
            config_name: Optional different name for config lookup
            fail: Whether to raise an error after showing the warning
        """
        if not self._show_warnings:
            return

        if tool_name in self._shown_warnings:
            return

        config_name = config_name or tool_name
        message = f"""
{colored('Integration not configured:', 'cyan', attrs=['bold'])} {tool_name} ({self._get_missing_features(tool_name)})
See setup guide: https://www.notion.so/Autonomous-Agent-Guidebook-177bba10175a80db8e82ecb63ce17761?pvs=4
Disable this warning with --no-integration-warnings
"""
        print(message, file=sys.stderr)
        self._shown_warnings.add(tool_name)

        if fail:
            raise RuntimeError(f"Integration not configured: {tool_name}")

    def _get_missing_features(self, tool_name: str) -> str:
        """Get description of missing features for a tool."""
        features = {
            "web_search": "the ability to search the web for information and documentation",
            "linear": "the ability to query and interact with Linear tickets",
            "notion": "the ability to access and search Notion documentation",
        }
        return features.get(tool_name, "additional features")
