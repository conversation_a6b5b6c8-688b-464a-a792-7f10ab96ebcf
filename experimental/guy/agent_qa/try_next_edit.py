#!/usr/bin/env python3
"""Script to test the next-edit endpoint."""

import argparse
from pathlib import Path
import tempfile
import subprocess
import os

from base.augment_client.client import (
    AugmentClient,
    AugmentModelClient,
    <PERSON>lo<PERSON><PERSON>son,
    UploadContent,
)
from base.blob_names.python.blob_names import get_blob_name
from experimental.guy.agent_qa.prototyping_client import (
    get_dev_deployment_api_proxy_url,
)
from experimental.guy.agent_qa.changes import patch_to_edit_events


def main():
    # Parse arguments
    parser = argparse.ArgumentParser(description="Test the next-edit endpoint")
    parser.add_argument(
        "--auth-token-file",
        type=Path,
        default=Path("~/.augment/token").expanduser(),
        help="The file containing the API token",
    )
    args = parser.parse_args()

    # Read token
    token = args.auth_token_file.read_text("utf8").strip()
    print(f"Using token: {token}")

    # Create client
    client = AugmentClient(
        # url="https://staging-shard-0.api.augmentcode.com",
        url="https://dev-guy.us-central.api.augmentcode.com/",
        token=token,
    )
    print(f"Created client with URL: {client.url}")

    def get_blob(path: Path):
        return UploadContent(
            path_name=str(path),
            content=path.read_text("utf8"),
        )

    # Create test files in a temporary directory
    with tempfile.TemporaryDirectory() as tmp_dir:
        print(f"Created temporary directory: {tmp_dir}")
        os.chdir(str(tmp_dir))
        blobs_to_upload = []

        # Create initial files
        foo_path = Path("foo.py")
        foo_path.write_text("""
def print_it():
    print("hello world")
""")
        blobs_to_upload.append(get_blob(foo_path))

        bar_path = Path("bar.py")
        bar_path.write_text("""
from foo import print_it

def main():
    print_it()
""")
        blobs_to_upload.append(get_blob(bar_path))

        # Initialize git repo
        subprocess.run(["git", "init"], cwd=tmp_dir, check=True)
        subprocess.run(
            ["git", "config", "user.name", "Test User"], cwd=tmp_dir, check=True
        )
        subprocess.run(
            ["git", "config", "user.email", "<EMAIL>"], cwd=tmp_dir, check=True
        )
        subprocess.run(["git", "add", "foo.py"], cwd=tmp_dir, check=True)
        subprocess.run(["git", "add", "bar.py"], cwd=tmp_dir, check=True)
        subprocess.run(
            ["git", "commit", "-m", "Initial commit"], cwd=tmp_dir, check=True
        )
        print("Initialized git repo and committed initial files")

        foo_path.write_text("""
def print_it_out():
    print("hello world")
""")
        blobs_to_upload.append(get_blob(foo_path))

        print("Made changes")

        # Stage and commit the changes
        subprocess.run(["git", "add", "foo.py"], cwd=tmp_dir, check=True)
        subprocess.run(["git", "add", "bar.py"], cwd=tmp_dir, check=True)
        subprocess.run(["git", "commit", "-m", "Make changes"], cwd=tmp_dir, check=True)
        print("Committed changes")

        # Get the git diff
        diff_output = subprocess.run(
            ["git", "diff", "HEAD^", "HEAD"],
            cwd=tmp_dir,
            check=True,
            capture_output=True,
            text=True,
        ).stdout
        print(f"Git diff output:\n{diff_output}")

        # Convert diff to edit events
        edit_events = patch_to_edit_events(diff_output)
        print(f"Edit events:\n{edit_events}")

        uploaded_blob_names = client.batch_upload(blobs_to_upload)

        # blob_names to act on should include the current versions of the files
        # compute the blob name using the current content
        blob_names = [
            get_blob_name(
                path=str(bar_path),
                contents=bar_path.read_text("utf8"),
            ),
            get_blob_name(
                path=str(foo_path),
                contents=foo_path.read_text("utf8"),
            ),
        ]

        print(f"Uploading blobs: {blobs_to_upload}")
        print(f"Uploaded blob names: {uploaded_blob_names}")

        # Create model client
        model_client = AugmentModelClient(client, "raven-edit-v5-15b")
        print(f"Created model client with model: {model_client.model_name}")

        # Convert edit events to dicts
        edit_events_dicts = []
        for event in edit_events:
            event_dict = {
                "path": event.path,
                "before_blob_name": event.before_blob_name,
                "after_blob_name": event.after_blob_name,
                "edits": [
                    {
                        "before_start": edit.before_start,
                        "before_text": edit.before_text,
                        "after_start": edit.after_start,
                        "after_text": edit.after_text,
                    }
                    for edit in event.edits
                ],
            }
            edit_events_dicts.append(event_dict)
        print(f"Edit events dicts:\n{edit_events_dicts}")

        # Get next edit suggestions
        print("Getting next edit suggestions...")
        responses = list(
            model_client.next_edit_stream(
                mode="FOREGROUND",
                # mode="BACKGROUND",
                scope="WORKSPACE",
                # scope="FILE",
                # scope="CURSOR",
                instruction="What should I edit next?",
                prefix="",
                suffix="",
                selected_text="",
                edit_events=edit_events_dicts,
                blobs=BlobsJson(
                    checkpoint_id=None,
                    added_blobs=blob_names,
                    deleted_blobs=[],
                ),
                timeout=60,
            )
        )
        print("Got responses:")
        for response in responses:
            print("\n")
            print(response)
            print("existing_code:")
            print(response.next_edit["existing_code"])
            print("suggested_code:")
            print(response.next_edit["suggested_code"])


if __name__ == "__main__":
    main()
