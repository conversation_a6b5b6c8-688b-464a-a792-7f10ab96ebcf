<script>
  import { onMount } from 'svelte';
  import { parsePatch } from 'diff';

  let diffContent = [];
  let currentFile = null;
  const { ipc<PERSON><PERSON><PERSON> } = window.require('electron');

  async function selectFile() {
    const result = await ipcRenderer.invoke('select-file');
    if (result) {
      currentFile = result.filePath;
      try {
        const patches = parsePatch(result.content);
        diffContent = patches;
      } catch (error) {
        console.error('Error parsing diff:', error);
        diffContent = [];
      }
    }
  }

  async function refreshFile() {
    if (!currentFile) return;
    
    const result = await ipcRenderer.invoke('refresh-file');
    if (result) {
      try {
        const patches = parsePatch(result.content);
        diffContent = patches;
      } catch (error) {
        console.error('Error parsing diff:', error);
        diffContent = [];
      }
    }
  }
</script>

<main>
  <div class="container">
    <div class="button-container">
      <button on:click={selectFile}>Select Patch File</button>
      <button 
        on:click={refreshFile}
        class:disabled={!currentFile}
        disabled={!currentFile}
      >
        Refresh
      </button>
    </div>
    
    {#if diffContent.length > 0}
      {#each diffContent as patch}
        <div class="file-diff">
          <div class="file-header">
            <div class="diff-line info">
              <span style="opacity: 0.7">→</span> {patch.oldFileName || 'old'} → {patch.newFileName || 'new'}
            </div>
          </div>
          <div class="diff-content">
            {#each patch.hunks as hunk}
              <div class="diff-line info">
                @@ -{hunk.oldStart},{hunk.oldLines} +{hunk.newStart},{hunk.newLines} @@
              </div>
              
              {#each hunk.lines as line}
                <div 
                  class="diff-line" 
                  class:addition={line.startsWith('+')} 
                  class:deletion={line.startsWith('-')}
                >
                  {line.slice(1)}
                </div>
              {/each}
            {/each}
          </div>
        </div>
      {/each}
    {:else}
      <div class="empty-state" style="text-align: center; padding: 4rem 2rem; color: #94a3b8;">
        Select a patch file to view changes
      </div>
    {/if}
  </div>
</main>

<style>
  :global(body) {
    margin: 0;
    background-color: #0f172a;
    color: #e2e8f0;
  }

  main {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    min-height: 100vh;
    padding: 2rem;
  }

  .container {
    max-width: 1200px;
    margin: 0 auto;
    background-color: #1e293b;
    border-radius: 12px;
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    padding: 1.5rem;
  }

  .button-container {
    display: flex;
    gap: 12px;
    margin-bottom: 24px;
    padding-bottom: 24px;
    border-bottom: 1px solid #334155;
  }

  button {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background-color: #3b82f6;
    color: white;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.2s ease;
  }

  button:hover:not(:disabled) {
    background-color: #2563eb;
    transform: translateY(-1px);
  }

  button:active:not(:disabled) {
    transform: translateY(0px);
  }

  button:disabled {
    background-color: #475569;
    cursor: not-allowed;
    opacity: 0.7;
  }

  .file-diff {
    margin-bottom: 2rem;
    background-color: #0f172a;
    border-radius: 8px;
    overflow: hidden;
  }

  .file-header {
    background-color: #1e293b;
    padding: 1rem;
    border-bottom: 1px solid #334155;
  }

  .diff-content {
    padding: 1rem;
  }

  .diff-line {
    padding: 4px 8px;
    font-family: 'JetBrains Mono', 'Fira Code', 'Courier New', monospace;
    font-size: 0.875rem;
    line-height: 1.5;
    white-space: pre;
    border-radius: 4px;
    margin: 2px 0;
  }

  .addition {
    background-color: rgba(34, 197, 94, 0.1);
    color: #4ade80;
  }

  .addition::before {
    content: '+';
    color: #4ade80;
    margin-right: 8px;
    opacity: 0.7;
  }

  .deletion {
    background-color: rgba(239, 68, 68, 0.1);
    color: #f87171;
  }

  .deletion::before {
    content: '-';
    color: #f87171;
    margin-right: 8px;
    opacity: 0.7;
  }

  .info {
    color: #94a3b8;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .file-diff {
    animation: fadeIn 0.3s ease-out;
  }
</style>
