"""Tests for web page fetcher tool."""

from unittest.mock import Mock, patch
import pytest
import requests

from experimental.guy.agent_qa.tools.web_page_fetcher_tool import WebPageFetcherTool
from experimental.guy.agent_qa.tools.web_page_fetcher import WebPageError
from research.agents.tools import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ToolImplOutput


class TestWebPageFetcherTool:
    """Tests for WebPageFetcherTool."""

    @pytest.fixture
    def tool(self):
        """Create a WebPageFetcherTool instance."""
        return WebPageFetcherTool(ToolCallLogger())

    def test_fetch_success(self, tool):
        """Test successful page fetch."""
        mock_response = Mock()
        mock_response.text = "<html><body><h1>Test</h1><p>Content</p></body></html>"
        mock_response.raise_for_status = Mock()

        with patch("requests.get", return_value=mock_response):
            result = tool.run_impl({"url": "https://example.com"})

            assert "Test" in result.tool_output
            assert "Content" in result.tool_output
            assert "Successfully fetched" in result.tool_result_message

    def test_fetch_error(self, tool):
        """Test error handling."""
        with patch(
            "requests.get", side_effect=requests.RequestException("Network error")
        ):
            result = tool.run_impl({"url": "https://example.com"})

            assert "Failed to fetch page" in result.tool_output
            assert "Network error" in result.tool_result_message

    def test_invalid_url(self, tool):
        """Test with invalid URL."""
        with patch(
            "requests.get", side_effect=requests.RequestException("Invalid URL")
        ):
            result = tool.run_impl({"url": "not_a_url"})

            assert "Failed to fetch page" in result.tool_output
            assert "Invalid URL" in result.tool_result_message

    def test_timeout(self, tool):
        """Test timeout handling."""
        with patch("requests.get", side_effect=requests.Timeout("Request timed out")):
            result = tool.run_impl({"url": "https://example.com"})

            assert "Failed to fetch page" in result.tool_output
            assert "timed out" in result.tool_result_message.lower()

    def test_html_conversion(self, tool):
        """Test HTML to markdown conversion."""
        mock_response = Mock()
        mock_response.text = """
        <html>
            <body>
                <h1>Title</h1>
                <p>Paragraph with <a href="https://example.com">link</a></p>
                <ul>
                    <li>Item 1</li>
                    <li>Item 2</li>
                </ul>
            </body>
        </html>
        """
        mock_response.raise_for_status = Mock()

        with patch("requests.get", return_value=mock_response):
            result = tool.run_impl({"url": "https://example.com"})

            assert "# Title" in result.tool_output
            assert "[link](https://example.com)" in result.tool_output
            assert "* Item 1" in result.tool_output
            assert "* Item 2" in result.tool_output
