"""End-to-end tests for remote tools functionality in the interactive agent.

These tests verify that the interactive agent can properly use remote tools
like the Google Search API through the Augment API.
"""

import json
import os
import re
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple
from unittest.mock import MagicMock, patch

import pytest
import requests
from termcolor import colored

from base.augment_client.client import (
    AugmentClient,
    RemoteToolId,
    ToolAvailabilityStatus,
    ToolSafety,
)
from experimental.guy.agent_qa.test_cli_agent_e2e import (
    AgentResult,
    execute_agent_command,
    run_agent,
    workspace_dir,
)
from experimental.guy.agent_qa.tools.remote_tool import (
    RemoteTool,
    RemoteToolInfo,
    create_remote_tools,
)
from research.agents.tools import Too<PERSON><PERSON>all<PERSON>ogger, ToolImplOutput


class MockResponse:
    """Mock response for requests."""

    def __init__(
        self, json_data: Dict[str, Any], status_code: int = 200, ok: bool = True
    ):
        self.json_data = json_data
        self.status_code = status_code
        self.ok = ok
        self.text = json.dumps(json_data)

    def json(self) -> Dict[str, Any]:
        return self.json_data


class MockAugmentClient:
    """Mock AugmentClient for testing remote tools."""

    def __init__(
        self,
        mock_tools: List[Dict[str, Any]] = None,
        mock_tool_results: Dict[str, Any] = None,
    ):
        self.mock_tools = mock_tools or []
        self.mock_tool_results = mock_tool_results or {}
        self.calls = []

    def _post(
        self, endpoint: str, json: Dict[str, Any] = None, **kwargs
    ) -> Tuple[MockResponse, str]:
        """Mock the _post method of AugmentClient."""
        self.calls.append({"endpoint": endpoint, "json": json, "kwargs": kwargs})

        if endpoint == "agents/list-remote-tools":
            return MockResponse({"tools": self.mock_tools}), "request_id"

        if endpoint == "agents/run-remote-tool":
            tool_name = json.get("tool_name", "")
            tool_input = json.get("tool_input_json", "{}")

            # Parse the tool input
            try:
                tool_input_dict = json.loads(tool_input)
                query = tool_input_dict.get("query", "")
            except (json.JSONDecodeError, TypeError, AttributeError):
                query = ""

            # Get the mock result for this tool and query
            result = self.mock_tool_results.get(tool_name, {}).get(
                query,
                {
                    "tool_output": f"Mock result for {tool_name} with query '{query}'",
                    "tool_result_message": "Tool executed successfully",
                    "status": 1,  # ExecutionSuccess
                },
            )

            return MockResponse(result), "request_id"

        return MockResponse({"error": "Endpoint not mocked"}), "request_id"

    def list_remote_tools(self, tool_ids=None) -> List[RemoteToolInfo]:
        """Mock the list_remote_tools method of AugmentClient.

        Args:
            tool_ids: Optional list of tool IDs to filter by (not used in mock)
        """
        self.calls.append({"method": "list_remote_tools", "tool_ids": tool_ids})

        # Create RemoteToolInfo objects from mock_tools
        class ToolInfo:
            def __init__(self, data):
                self._data = data

            @property
            def tool_definition(self) -> Dict[str, Any]:
                return self._data.get("tool_definition", {})

            @property
            def remote_tool_id(self) -> int:
                return self._data.get("remote_tool_id", 0)

        return [ToolInfo(tool) for tool in self.mock_tools]

    def run_remote_tool(
        self, tool_name: str, tool_input_json: str, tool_id: Optional[int] = None
    ) -> Any:
        """Mock the run_remote_tool method of AugmentClient."""
        self.calls.append(
            {
                "method": "run_remote_tool",
                "tool_name": tool_name,
                "tool_input_json": tool_input_json,
                "tool_id": tool_id,
            }
        )

        # Parse the tool input
        try:
            tool_input_dict = json.loads(tool_input_json)
            query = tool_input_dict.get("query", "")
        except (json.JSONDecodeError, TypeError, AttributeError):
            query = ""

        # Get the mock result for this tool and query
        result = self.mock_tool_results.get(tool_name, {}).get(
            query,
            {
                "tool_output": f"Mock result for {tool_name} with query '{query}'",
                "tool_result_message": "Tool executed successfully",
                "is_error": False,
            },
        )

        # Create a result object with the expected attributes
        class ToolResult:
            def __init__(self, data):
                self.tool_output = data.get("tool_output", "")
                self.tool_result_message = data.get("tool_result_message", "")
                self.is_error = data.get("is_error", False)

        return ToolResult(result)


# Create mock data for web-search tool
MOCK_WEB_SEARCH_TOOL = {
    "tool_definition": {
        "name": "web-search",
        "description": "Search the web for information. Returns results in markdown format.",
        "input_schema_json": json.dumps(
            {
                "type": "object",
                "properties": {
                    "query": {
                        "type": "string",
                        "description": "The search query to send.",
                    },
                    "num_results": {
                        "type": "integer",
                        "description": "Number of results to return",
                        "default": 5,
                        "minimum": 1,
                        "maximum": 10,
                    },
                },
                "required": ["query"],
            }
        ),
    },
    "remote_tool_id": RemoteToolId.WEB_SEARCH,
    "availability_status": ToolAvailabilityStatus.AVAILABLE,
    "tool_safety": ToolSafety.TOOL_SAFE,
}

# Create mock data for github_api tool
MOCK_GITHUB_API_TOOL = {
    "tool_definition": {
        "name": "github-api",
        "description": "Make GitHub API calls. Response is formatted as yaml.",
        "input_schema_json": json.dumps(
            {
                "type": "object",
                "properties": {
                    "path": {"type": "string", "description": "GitHub API path."},
                    "method": {
                        "type": "string",
                        "description": "HTTP method to use. Usually GET, rarely POST.",
                        "default": "GET",
                        "enum": ["GET", "POST"],
                    },
                    "data": {
                        "description": "Data to send - automatically handled as query params for GET or JSON body for POST.",
                        "type": ["object", "null"],
                        "default": None,
                    },
                    "details": {
                        "type": "boolean",
                        "description": "If true, include all fields in the response.",
                        "default": False,
                    },
                },
                "required": ["path"],
            }
        ),
    },
    "remote_tool_id": RemoteToolId.GITHUB_API,
    "availability_status": ToolAvailabilityStatus.AVAILABLE,
    "tool_safety": ToolSafety.TOOL_SAFE,
}

# Mock search results for "Augment AI"
MOCK_SEARCH_RESULTS_AUGMENT_AI = {
    "tool_output": """Found 5 results:

1. [Augment Code | Developer AI for real work](https://www.augmentcode.com/)
   Augment is the AI platform that truly understands your codebase. Help your development team code faster, make smarter decisions, and unlock collective knowledge.

2. [Augment raises $252M to build AI for developers](https://techcrunch.com/2024/04/24/augment-raises-252m-to-build-ai-for-developers/)
   Apr 24, 2024 — Augment, a startup building AI tools for software developers, today announced that it raised $252 million in funding at a $977 million valuation.

3. [Augment Code on LinkedIn: We're excited to announce that Augment has raised $227M](https://www.linkedin.com/posts/augmentcode_were-excited-to-announce-that-augment-has-activity-7178400324894351360-Wd-K)
   Apr 24, 2024 — We're excited to announce that Augment has raised $227M in funding to build the AI platform that naturally and seamlessly empowers every developer.

4. [Augment Code on LinkedIn: Augment claims the spot for first developer AI](https://www.linkedin.com/posts/augmentcode_augment-claims-the-spot-for-first-developer-activity-7178400324894351360-Wd-K)
   Apr 24, 2024 — Augment claims the spot for first developer AI with lightning fast, complete codebase context in every keystroke.

5. [Augment Code - Crunchbase Company Profile & Funding](https://www.crunchbase.com/organization/augment-code)
   Augment Code is a company building AI tools for software developers, helping teams code faster and make better decisions.
""",
    "tool_result_message": "Found 5 results for 'Augment AI'",
    "is_error": False,
}

# Mock search results for "Python programming"
MOCK_SEARCH_RESULTS_PYTHON = {
    "tool_output": """Found 5 results:

1. [Python.org](https://www.python.org/)
   The official home of the Python Programming Language. Download Python, documentation, community links, and more.

2. [Python (programming language) - Wikipedia](https://en.wikipedia.org/wiki/Python_(programming_language))
   Python is a high-level, general-purpose programming language. Its design philosophy emphasizes code readability with the use of significant indentation.

3. [Python Tutorial - W3Schools](https://www.w3schools.com/python/)
   Python is a popular programming language. Python can be used on a server to create web applications.

4. [Learn Python - Free Interactive Python Tutorial](https://www.learnpython.org/)
   Whether you are an experienced programmer or not, this website is intended for everyone who wishes to learn the Python programming language.

5. [Python Tutorial - Tutorialspoint](https://www.tutorialspoint.com/python/index.htm)
   Python Tutorial - Python is a high-level, interpreted, interactive and object-oriented scripting language. Python is designed to be highly readable.
""",
    "tool_result_message": "Found 5 results for 'Python programming'",
    "is_error": False,
}


@pytest.fixture
def mock_augment_client():
    """Create a mock AugmentClient with predefined tools and results."""
    mock_tools = [MOCK_WEB_SEARCH_TOOL, MOCK_GITHUB_API_TOOL]
    mock_tool_results = {
        "web-search": {
            "Augment AI": MOCK_SEARCH_RESULTS_AUGMENT_AI,
            "Python programming": MOCK_SEARCH_RESULTS_PYTHON,
        }
    }
    return MockAugmentClient(mock_tools=mock_tools, mock_tool_results=mock_tool_results)


@pytest.mark.manual
@pytest.mark.skip_in_ci
def test_list_remote_tools_command() -> None:
    """Test that the --list-remote-tools command works correctly."""
    # Run the agent with the --list-remote-tools flag
    result = execute_agent_command(
        ["--list-remote-tools"],
    )

    # Verify the command executed successfully
    assert result.returncode == 0, "Command failed with non-zero return code"

    # Verify the output contains expected information
    stdout_lower = result.stdout.lower()
    assert "querying available remote tools" in stdout_lower, "Missing header text"
    assert "found" in stdout_lower, "Missing 'found' text"
    assert "remote tools" in stdout_lower, "Missing 'remote tools' text"

    # Verify at least one tool is listed
    assert (
        "google_search" in stdout_lower
        or "github-api" in stdout_lower
        or "web-search" in stdout_lower
    ), "No tools listed"

    # Verify the output contains schema information
    assert "input schema" in stdout_lower, "Missing schema information"
    assert "properties" in stdout_lower, "Missing schema properties"


@pytest.mark.manual
@pytest.mark.skip_in_ci
def test_enable_remote_tools_flag() -> None:
    """Test that the --enable-remote-tools flag works correctly."""
    # Run the agent with the --enable-remote-tools flag
    result = execute_agent_command(
        ["--enable-remote-tools", "--list-tools"],
    )

    # Verify the command executed successfully
    assert result.returncode == 0, "Command failed with non-zero return code"

    # Verify the output contains expected information
    stdout_lower = result.stdout.lower()
    assert "enabling remote tools" in stdout_lower, "Missing enabling message"
    assert "found" in stdout_lower, "Missing 'found' text"
    assert "remote tools" in stdout_lower, "Missing 'remote tools' text"

    # Verify the tools are listed
    assert (
        "google_search" in stdout_lower or "web-search" in stdout_lower
    ), "No search tool listed"


@pytest.mark.manual
@pytest.mark.skip_in_ci
def test_remote_tool_search() -> None:
    """Test that the agent can use the web-search remote tool."""
    # First, list the available remote tools to verify web-search is available
    list_result = execute_agent_command(
        ["--list-remote-tools"],
    )

    # Verify web-search is in the list
    list_stdout_lower = list_result.stdout.lower()
    assert (
        "web-search" in list_stdout_lower
    ), "web-search tool not found in available tools"

    # Run the agent with a search instruction using the correct tool name
    instruction = (
        "Search for information about Python programming using the web-search tool"
    )
    result = run_agent(
        workspace_dir,
        instruction,
        extra_args=["--enable-remote-tools"],
    )

    # Verify the agent used the web-search tool
    stdout_lower = result.stdout.lower()
    assert "web-search" in stdout_lower, "Agent didn't use web-search tool"
    assert "python" in stdout_lower, "Search results don't contain 'python'"
    assert "programming" in stdout_lower, "Search results don't contain 'programming'"

    # Verify the agent found search results
    assert (
        "found" in stdout_lower and "results" in stdout_lower
    ), "No search results found"
    assert (
        "w3schools" in stdout_lower
        or "tutorialspoint" in stdout_lower
        or "python.org" in stdout_lower
    ), "No expected websites in results"


@pytest.mark.manual
@pytest.mark.skip_in_ci
def test_remote_tool_with_specific_tool() -> None:
    """Test that the agent can use a specific remote tool specified with --remote-tool."""
    # First, list the available remote tools to verify web-search is available
    list_result = execute_agent_command(
        ["--list-remote-tools"],
    )

    # Verify web-search is in the list and get its exact name
    list_stdout_lower = list_result.stdout.lower()
    assert (
        "web-search" in list_stdout_lower
    ), "web-search tool not found in available tools"

    # Run the agent with a specific remote tool
    instruction = "Search for information about Python programming"
    result = run_agent(
        workspace_dir,
        instruction,
        extra_args=[
            "--enable-remote-tools",
            "--remote-tool",
            "web-search",  # Use the correct tool name
        ],
    )

    # Verify the agent used the web-search tool
    stdout_lower = result.stdout.lower()
    assert "web-search" in stdout_lower, "Agent didn't use web-search tool"
    assert "python" in stdout_lower, "Search results don't contain 'python'"
    assert "programming" in stdout_lower, "Search results don't contain 'programming'"


def test_remote_tool_class(mock_augment_client):
    """Test the RemoteTool class directly."""
    # Create a mock tool call logger
    tool_call_logger = MagicMock()

    # Create a mock augment client
    mock_client = MockAugmentClient(
        mock_tools=[MOCK_WEB_SEARCH_TOOL],
        mock_tool_results={
            "web-search": {
                "test query": {
                    "tool_output": "Mock search results for test query",
                    "tool_result_message": "Found results for test query",
                    "is_error": False,
                }
            }
        },
    )

    # Get the tool info
    tool_infos = mock_client.list_remote_tools()
    assert len(tool_infos) > 0, "No tool infos returned"

    # Create a RemoteTool instance
    remote_tool = RemoteTool(
        tool_call_logger=tool_call_logger,
        augment_client=mock_client,
        tool_info=tool_infos[0],
    )

    # Verify the tool properties
    assert remote_tool.name == "web-search", "Wrong tool name"
    assert "Search the web" in remote_tool.description, "Wrong tool description"
    assert remote_tool.remote_tool_id == RemoteToolId.WEB_SEARCH, "Wrong tool ID"

    # Test running the tool
    result = remote_tool.run_impl({"query": "test query"})

    # Verify the result
    assert (
        "Mock search results for test query" in result.tool_output
    ), "Wrong tool output"
    assert (
        "Found results for test query" in result.tool_result_message
    ), "Wrong result message"


def test_create_remote_tools_function(mock_augment_client):
    """Test the create_remote_tools function."""
    # Create a mock tool call logger
    tool_call_logger = MagicMock()

    # Create a mock augment client
    mock_client = MockAugmentClient(
        mock_tools=[MOCK_WEB_SEARCH_TOOL, MOCK_GITHUB_API_TOOL]
    )

    # Call create_remote_tools
    tools = create_remote_tools(
        tool_call_logger=tool_call_logger, augment_client=mock_client
    )

    # Verify the tools were created
    assert len(tools) == 2, f"Expected 2 tools, got {len(tools)}"
    assert tools[0].name == "web-search", f"Expected web-search, got {tools[0].name}"
    assert tools[1].name == "github-api", f"Expected github-api, got {tools[1].name}"

    # Test with include_tools
    tools = create_remote_tools(
        tool_call_logger=tool_call_logger,
        augment_client=mock_client,
        include_tools=["github-api"],
    )

    # Verify only the specified tool was created
    assert len(tools) == 1, f"Expected 1 tool, got {len(tools)}"
    assert tools[0].name == "github-api", f"Expected github-api, got {tools[0].name}"


@patch("experimental.guy.agent_qa.tools.remote_tool.AugmentClient")
def test_remote_tool_with_mocked_client(mock_augment_client_class):
    """Test the RemoteTool class with a mocked AugmentClient."""
    # Create a mock instance
    mock_client = MockAugmentClient(
        mock_tools=[MOCK_WEB_SEARCH_TOOL],
        mock_tool_results={
            "web-search": {"Augment AI": MOCK_SEARCH_RESULTS_AUGMENT_AI}
        },
    )

    # Make the mock class return our mock instance
    mock_augment_client_class.return_value = mock_client

    # Create a mock tool call logger
    tool_call_logger = MagicMock()

    # Create a RemoteTool instance
    tools = create_remote_tools(
        tool_call_logger=tool_call_logger, augment_client=mock_client
    )

    # Verify the tool was created
    assert len(tools) == 1, "No tools created"
    remote_tool = tools[0]

    # Test running the tool
    result = remote_tool.run_impl({"query": "Augment AI"})

    # Verify the result
    assert "Found 5 results" in result.tool_output, "Wrong tool output"
    assert "Augment" in result.tool_output, "Search results don't contain 'Augment'"
    assert "AI" in result.tool_output, "Search results don't contain 'AI'"
