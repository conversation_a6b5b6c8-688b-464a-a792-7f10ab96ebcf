"""Tests for standard agent modes."""

from unittest.mock import Mock

from experimental.guy.agent_qa.agent_modes import (
    create_architect_mode,
    create_implementor_mode,
    create_architect_provider,
    create_implementor_provider,
    ARCHITECT_PROMPT,
    IMPLEMENTOR_PROMPT,
)
from research.agents.tools import LLMTool


def test_create_architect_mode():
    """Test creating architect mode."""
    # Create some mock tools
    tool1 = Mock(name="tool1")
    tool2 = Mock(name="tool2")
    tools = [tool1, tool2]

    # Create mode
    mode = create_architect_mode(tools)  # type: ignore

    # Verify mode
    assert mode.system_prompt == ARCHITECT_PROMPT
    assert mode.tools == tools


def test_create_implementor_mode():
    """Test creating implementor mode."""
    # Create some mock tools
    tool1 = Mock(name="tool1")
    tool2 = Mock(name="tool2")
    tools = [tool1, tool2]

    # Create mode
    mode = create_implementor_mode(tools)  # type: ignore

    # Verify mode
    assert mode.system_prompt == IMPLEMENTOR_PROMPT
    assert mode.tools == tools


def test_create_architect_provider():
    """Test creating architect mode provider."""
    # Create some mock tools
    tool1 = Mock(name="tool1")
    tool2 = Mock(name="tool2")
    tools: list[LLMTool] = [tool1, tool2]

    # Create provider
    provider = create_architect_provider(tools)

    # Update mode based on dialog
    provider.update_mode(Mock())

    # Verify provider returns architect mode
    mode = provider.get_current_mode()
    assert mode.system_prompt == ARCHITECT_PROMPT
    assert mode.tools == tools


def test_create_implementor_provider():
    """Test creating implementor mode provider."""
    # Create some mock tools
    tool1 = Mock(name="tool1")
    tool2 = Mock(name="tool2")
    tools: list[LLMTool] = [tool1, tool2]

    # Create provider
    provider = create_implementor_provider(tools)

    # Update mode based on dialog
    provider.update_mode(Mock())

    # Verify provider returns implementor mode
    mode = provider.get_current_mode()
    assert mode.system_prompt == IMPLEMENTOR_PROMPT
    assert mode.tools == tools
