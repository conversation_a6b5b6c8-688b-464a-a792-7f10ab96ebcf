{"cells": [{"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Analyzing 121 commits...\n", "Processing commit 121/121 from 2024-12-22...\n", "Analysis complete!\n", "Results saved to loc_history.csv\n", "\n", "Summary:\n", "Period: 2024-12-22 to 2025-02-11\n", "Initial LOC: 534\n", "Final LOC: 17695\n", "Change: 17161 lines\n", "\n", "Recent history (last 5 days):\n", "2025-02-07: 15040 lines\n", "2025-02-08: 15788 lines\n", "2025-02-09: 15938 lines\n", "2025-02-10: 17582 lines\n", "2025-02-11: 17695 lines\n"]}], "source": ["#!/usr/bin/env python3\n", "import subprocess\n", "import datetime\n", "import csv\n", "import os\n", "from collections import defaultdict\n", "import argparse\n", "from typing import Dict, List, Tuple\n", "\n", "\n", "class GitLOCAnalyzer:\n", "    def __init__(self, target_dir: str, days):\n", "        self.target_dir = target_dir\n", "        self.days = days\n", "        self.file_extensions = {\n", "            \".py\",\n", "            \".java\",\n", "            \".cpp\",\n", "            \".h\",\n", "            \".jsx\",\n", "            \".ts\",\n", "            \".tsx\",\n", "            \".js\",\n", "            \".cc\",\n", "            \".hpp\",\n", "            \".yaml\",\n", "            \".yml\",\n", "        }\n", "\n", "    def get_date_range(self) -> str:\n", "        \"\"\"Calculate the date from months ago.\"\"\"\n", "        today = datetime.datetime.now()\n", "        days_ago = today - datetime.timed<PERSON>ta(days=self.days)\n", "        return days_ago.strftime(\"%Y-%m-%d\")\n", "\n", "    def run_git_command(self, command: List[str]) -> str:\n", "        \"\"\"Execute a git command and return its output.\"\"\"\n", "        try:\n", "            result = subprocess.run(command, capture_output=True, text=True, check=True)\n", "            return result.stdout.strip()\n", "        except subprocess.CalledProcessError as e:\n", "            print(f\"Error executing git command: {e}\")\n", "            return \"\"\n", "\n", "    def get_commit_history(self) -> List[Tuple[str, str]]:\n", "        \"\"\"Get list of commits and their dates.\"\"\"\n", "        self.run_git_command([\"git\", \"checkout\", \"main\"])\n", "\n", "        since_date = self.get_date_range()\n", "        git_log = self.run_git_command(\n", "            [\n", "                \"git\",\n", "                \"log\",\n", "                f\"--since={since_date}\",\n", "                \"--format=%H %as\",\n", "                \"--\",\n", "                self.target_dir,\n", "            ]\n", "        )\n", "\n", "        commits = []\n", "        for line in git_log.split(\"\\n\"):\n", "            if line:\n", "                commit_hash, date = line.split()\n", "                commits.append((commit_hash, date))\n", "        return commits\n", "\n", "    def count_lines(self, commit_hash: str) -> int:\n", "        \"\"\"Count non-empty lines of code for a specific commit.\"\"\"\n", "        # Checkout the commit\n", "        self.run_git_command([\"git\", \"checkout\", commit_hash, \"-q\"])\n", "\n", "        total_lines = 0\n", "        try:\n", "            # Walk through the directory\n", "            for root, _, files in os.walk(self.target_dir):\n", "                for file in files:\n", "                    if any(file.endswith(ext) for ext in self.file_extensions):\n", "                        file_path = os.path.join(root, file)\n", "                        try:\n", "                            with open(file_path, \"r\", encoding=\"utf-8\") as f:\n", "                                # Count non-empty lines\n", "                                lines = sum(1 for line in f if line.strip())\n", "                                total_lines += lines\n", "                        except (UnicodeDecodeError, IOError) as e:\n", "                            print(f\"Error reading {file_path}: {e}\")\n", "        except Exception as e:\n", "            print(f\"Error processing directory: {e}\")\n", "\n", "        return total_lines\n", "\n", "    def analyze(self) -> Dict[str, int]:\n", "        \"\"\"Analyze the repository and return daily LOC counts.\"\"\"\n", "        commits = self.get_commit_history()\n", "        daily_loc = defaultdict(int)\n", "\n", "        print(f\"Analyzing {len(commits)} commits...\")\n", "\n", "        for i, (commit_hash, date) in enumerate(commits, 1):\n", "            print(f\"Processing commit {i}/{len(commits)} from {date}...\", end=\"\\r\")\n", "            loc = self.count_lines(commit_hash)\n", "            # Keep only the last commit for each day\n", "            daily_loc[date] = loc\n", "\n", "        print(\"\\nAnalysis complete!\")\n", "\n", "        # Return to original branch\n", "        self.run_git_command([\"git\", \"checkout\", \"-\"])\n", "\n", "        return dict(daily_loc)\n", "\n", "    def save_results(\n", "        self, daily_loc: Dict[str, int], output_file: str = \"loc_history.csv\"\n", "    ):\n", "        \"\"\"Save results to a CSV file.\"\"\"\n", "        with open(output_file, \"w\", newline=\"\") as f:\n", "            writer = csv.writer(f)\n", "            writer.writerow([\"Date\", \"Lines of Code\"])\n", "            for date in sorted(daily_loc.keys()):\n", "                writer.writerow([date, daily_loc[date]])\n", "        print(f\"Results saved to {output_file}\")\n", "\n", "    def print_summary(self, daily_loc: Dict[str, int]):\n", "        \"\"\"Print a summary of the results.\"\"\"\n", "        if not daily_loc:\n", "            print(\"No data to display\")\n", "            return\n", "\n", "        dates = sorted(daily_loc.keys())\n", "        print(\"\\nSummary:\")\n", "        print(f\"Period: {dates[0]} to {dates[-1]}\")\n", "        print(f\"Initial LOC: {daily_loc[dates[0]]}\")\n", "        print(f\"Final LOC: {daily_loc[dates[-1]]}\")\n", "        print(f\"Change: {daily_loc[dates[-1]] - daily_loc[dates[0]]} lines\")\n", "\n", "        # Print recent history\n", "        print(\"\\nRecent history (last 5 days):\")\n", "        for date in dates[-5:]:\n", "            print(f\"{date}: {daily_loc[date]} lines\")\n", "\n", "\n", "repo_dir = \"/home/<USER>/augment_for_counting\"\n", "folder = \"experimental/guy/agent_qa\"\n", "days = 100\n", "output = \"loc_history.csv\"\n", "\n", "os.chdir(repo_dir)\n", "analyzer = GitLOCAnalyzer(folder, days)\n", "daily_loc = analyzer.analyze()\n", "analyzer.save_results(daily_loc, output)\n", "analyzer.print_summary(daily_loc)"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import pandas\n", "import matplotlib.pyplot as plt\n", "\n", "df = pandas.read_csv(output, parse_dates=[\"Date\"])\n", "df[\"Days\"] = (df[\"Date\"] - df[\"Date\"].min()).dt.days\n", "df.plot(x=\"Days\", y=\"Lines of Code\")\n", "plt.xticks(rotation=90)\n", "plt.grid()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}