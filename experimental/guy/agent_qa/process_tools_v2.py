"""Process management tools using the LLM tool adapter pattern."""

import os
import select
import subprocess
import time
from pathlib import Path
from typing import Any, Optional

from experimental.guy.agent_qa.builtin_tools import OutputCollector
from experimental.guy.agent_qa.command_approval import Command<PERSON><PERSON>rovalManager
from experimental.guy.agent_qa.process_manager import ProcessManager
from experimental.guy.agent_qa.tool_adapter import LLMToolAdapter
from experimental.guy.agent_qa.workspace_manager import WorkspaceManager
from research.agents.tools import (
    DialogMessages,
    LLMTool,
    ToolCallLogger,
    ToolImplOutput,
)

WAIT_SECONDS_DEFAULT = 60


class ProcessTools:
    """A collection of tools for managing long-running processes."""

    def __init__(
        self,
        workspace_manager: WorkspaceManager,
        command_approval_manager: CommandApprovalManager,
        ask_user_permission: bool = True,
        char_budget: int = 100_000,
        cwd: Optional[Path] = None,
    ):
        """Initialize the process tools.

        Args:
            workspace_manager: For managing workspace files
            command_approval_manager: For getting user approval for commands
            ask_user_permission: Whether to ask user permission for commands
            char_budget: Maximum number of characters to store for each stream
            cwd: Default working directory for commands
        """
        self._workspace_manager = workspace_manager
        self._command_approval_manager = command_approval_manager
        self._ask_user_permission = ask_user_permission
        self._process_manager = ProcessManager(char_budget=char_budget)
        self.cwd = cwd or Path.cwd()

    def launch_process(
        self,
        tool_input: dict[str, Any],
        dialog_messages: Optional[DialogMessages] = None,
    ) -> ToolImplOutput:
        """Launch a new process.

        Args:
            tool_input: Dictionary containing command parameters:
                - command: The shell command to execute
                - wait: Optional boolean, whether to wait for process completion
                - wait_seconds: Optional integer, max seconds to wait (default: WAIT_SECONDS_DEFAULT)
                - ask_user_permission: Optional boolean, whether to ask for user approval
                - cwd: Optional string, working directory for the command
            dialog_messages: Optional dialog messages for context

        Returns:
            ToolImplOutput with process results or status

        If wait=True:
            - Launches process with stdout/stderr/stdin pipes
            - Collects output with OutputCollector (70% budget for stdout, 30% for stderr)
            - If process completes within wait_seconds, returns full output and return code
            - If timeout expires, process continues running in background and returns partial output with PID

        If wait=False:
            - Launches background process and returns immediately with PID
            - Process can be managed later with other process tools
        """
        del dialog_messages  # Not in use

        command = tool_input["command"]
        wait = tool_input.get("wait", False)
        wait_seconds = tool_input.get("wait_seconds", WAIT_SECONDS_DEFAULT)

        if self._ask_user_permission or tool_input.get("ask_user_permission", False):
            if not self._command_approval_manager.get_approval(command):
                return ToolImplOutput(
                    "Command not run because user did not give permission.",
                    "Not executing command",
                )

        working_dir = Path(tool_input["cwd"]) if "cwd" in tool_input else self.cwd

        try:
            # If wait is specified, use Popen and wait
            if wait:
                process = subprocess.Popen(
                    command,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    stdin=subprocess.PIPE,  # Always enable stdin
                    text=True,
                    cwd=working_dir,
                    shell=True,
                )

                assert process.stdout is not None
                assert process.stderr is not None
                assert process.stdin is not None

                # Set all streams to non-blocking mode
                os.set_blocking(process.stdout.fileno(), False)
                os.set_blocking(process.stderr.fileno(), False)
                os.set_blocking(process.stdin.fileno(), False)

                # Create output collectors with appropriate budgets
                stdout_collector = OutputCollector(
                    int(self._process_manager.char_budget * 0.7)  # 70% for stdout
                )
                stderr_collector = OutputCollector(
                    int(self._process_manager.char_budget * 0.3)  # 30% for stderr
                )

                start_time = time.time()
                while time.time() - start_time < wait_seconds:
                    # Use select to wait for output (with a timeout)
                    readable, _, _ = select.select(
                        [process.stdout, process.stderr], [], [], 0.1
                    )

                    for stream in readable:
                        line = stream.readline()
                        if line:
                            if stream == process.stdout:
                                stdout_collector.add_line(line)
                            else:
                                stderr_collector.add_line(line)

                    # Check if the process has finished
                    return_code = process.poll()
                    if return_code is not None:
                        # Read any remaining output
                        for line in process.stdout:
                            stdout_collector.add_line(line)
                        for line in process.stderr:
                            stderr_collector.add_line(line)

                        tool_answer = f"""\
Here are the results from executing the command.

<return-code>
{return_code}
</return-code>

<stdout>
{stdout_collector.get_output()}
</stdout>

<stderr>
{stderr_collector.get_output()}
</stderr>
"""
                        result_status = (
                            "succeeded"
                            if return_code == 0
                            else f"failed with error code {return_code}"
                        )
                        num_output_chars = (
                            stdout_collector.total_chars + stderr_collector.total_chars
                        )
                        user_message = f"Command {result_status} ({num_output_chars} characters output)"
                        return ToolImplOutput(tool_answer, user_message)

                # If we get here, the timeout was reached
                tool_answer = f"""\
Command is still running after {wait_seconds} seconds. You can use read_process to get more output
and kill_process to terminate it if needed.

PID {process.pid}

Output so far:

<stdout>
{stdout_collector.get_output()}
</stdout>

<stderr>
{stderr_collector.get_output()}
</stderr>
"""
                # Store the process in ProcessManager so it can be managed later
                self._process_manager._processes[process.pid] = process
                self._process_manager._stdout_buffers[process.pid] = (
                    stdout_collector.lines
                )
                self._process_manager._stderr_buffers[process.pid] = (
                    stderr_collector.lines
                )
                self._process_manager._last_read_stdout[process.pid] = len(
                    stdout_collector.lines
                )
                self._process_manager._last_read_stderr[process.pid] = len(
                    stderr_collector.lines
                )

                return ToolImplOutput(
                    tool_answer,
                    f"Command still running (PID {process.pid})",
                )

            # If wait is not specified, just launch and return PID
            pid = self._process_manager.launch(command, working_dir)
            return ToolImplOutput(
                f"Process launched with PID {pid}",
                f"Launched process {pid}",
            )

        except Exception as e:
            return ToolImplOutput(
                f"Failed to launch process: {str(e)}",
                "Failed to launch process",
            )

    def kill_process(
        self,
        tool_input: dict[str, Any],
        dialog_messages: Optional[DialogMessages] = None,
    ) -> ToolImplOutput:
        """Kill a process."""
        pid = tool_input["process_id"]
        if self._process_manager.kill(pid):
            return ToolImplOutput(
                f"Process {pid} killed",
                f"Killed process {pid}",
            )
        else:
            return ToolImplOutput(
                f"Process {pid} not found",
                f"Process {pid} not found",
            )

    def read_process(
        self,
        tool_input: dict[str, Any],
        dialog_messages: Optional[DialogMessages] = None,
    ) -> ToolImplOutput:
        """Read output from a process."""
        pid = tool_input["process_id"]
        output = self._process_manager.read_output(pid)
        if output is None:
            return ToolImplOutput(
                f"Process {pid} not found",
                f"Process {pid} not found",
            )

        status = "completed" if output.return_code is not None else "still running"
        tool_answer = f"""\
Here is the output from process {pid} (status: {status}):

<stdout>
{output.stdout}
</stdout>

<stderr>
{output.stderr}
</stderr>
"""
        if output.return_code is not None:
            tool_answer += f"""
<return-code>
{output.return_code}
</return-code>
"""
        return ToolImplOutput(
            tool_answer,
            f"Read from process {pid} ({status})",
        )

    def write_process(
        self,
        tool_input: dict[str, Any],
        dialog_messages: Optional[DialogMessages] = None,
    ) -> ToolImplOutput:
        """Write input to a process's stdin."""
        pid = tool_input["process_id"]
        if self._process_manager.write_input(pid, tool_input["input_text"]):
            return ToolImplOutput(
                f"Input written to process {pid}",
                f"Wrote to process {pid}",
            )
        else:
            return ToolImplOutput(
                f"Process {pid} not found or write failed",
                f"Failed to write to process {pid}",
            )

    def list_processes(
        self,
        tool_input: dict[str, Any],
        dialog_messages: Optional[DialogMessages] = None,
    ) -> ToolImplOutput:
        """List all known processes."""
        processes = self._process_manager.list_processes()
        if not processes:
            return ToolImplOutput(
                "No processes found",
                "No processes found",
            )

        # Format process list
        lines = []
        for proc in processes:
            status = f"{proc.state}"
            if proc.return_code is not None:
                status += f" (return code: {proc.return_code})"
            lines.append(f"PID {proc.pid}: {proc.command} - {status}")

        tool_answer = "Here are all known processes:\n\n" + "\n".join(lines)
        return ToolImplOutput(
            tool_answer,
            "Listed processes",
        )


def create_process_tools(
    workspace_manager: WorkspaceManager,
    command_approval_manager: CommandApprovalManager,
    tool_call_logger: ToolCallLogger,
    ask_user_permission: bool = True,
    char_budget: int = 100_000,
    cwd: Optional[Path] = None,
) -> list[LLMTool]:
    """Create all process management tools.

    Args:
        workspace_manager: For managing workspace files
        command_approval_manager: For getting user approval for commands
        tool_call_logger: For logging tool calls
        ask_user_permission: Whether to ask user permission for commands
        char_budget: Maximum number of characters to store for each stream
        cwd: Default working directory for commands

    Returns:
        List of process management tools
    """
    process_tools = ProcessTools(
        workspace_manager=workspace_manager,
        command_approval_manager=command_approval_manager,
        ask_user_permission=ask_user_permission,
        char_budget=char_budget,
        cwd=cwd,
    )

    return [
        LLMToolAdapter(
            tool_call_logger=tool_call_logger,
            tool_func=process_tools.launch_process,
            name="launch_process",
            description=f"""\
Launch a new process with a shell command. A process can be waiting (`wait=true`) or non-waiting (`wait=false`, which is default).

If `wait=true`, launches the process in an interactive terminal, and waits for the process to complete up to
`wait_seconds` seconds (default: {WAIT_SECONDS_DEFAULT}). If the process ends
during this period, the tool call returns. If the timeout expires, the process will continue running in the
background but the tool call will return. You can then interact with the process using the other process tools.

Note: Only one waiting process can be running at a time. If you try to launch a process with `wait=true`
while another is running, the tool will return an error.

If `wait=false`, launches a background process in a separate terminal. This returns immediately, while the
process keeps running in the background.

Notes:
- Use `wait=true` processes when the command is expected to be short, or when you can't
proceed with your task until the process is complete. Use `wait=false` for processes that are
expected to run in the background, such as starting a server you'll need to interact with, or a
long-running process that does not need to complete before proceeding with the task.
- If this tool returns while the process is still running, you can continue to interact with the process
using the other available tools. You can wait for the process, read from it, write to it, kill it, etc.
- You can use this tool to interact with the user's local version control system. Do not use the
retrieval tool for that purpose.
- If there is a more specific tool available that can perform the function, use that tool instead of
this one.""",
            input_schema={
                "type": "object",
                "properties": {
                    "command": {
                        "type": "string",
                        "description": "The command to execute.",
                    },
                    "wait": {
                        "type": "boolean",
                        "description": "Optional: whether to wait for the command to complete.",
                    },
                    "wait_seconds": {
                        "type": "integer",
                        "description": "Optional: number of seconds to wait for the command to complete.",
                    },
                    "ask_user_permission": {
                        "type": "boolean",
                        "description": "Ask the user whether to execute the command.",
                    },
                    "cwd": {
                        "type": "string",
                        "description": "Working directory for the command. If not supplied, uses the current working directory.",
                    },
                },
                "required": ["command"],
            },
        ),
        LLMToolAdapter(
            tool_call_logger=tool_call_logger,
            tool_func=process_tools.kill_process,
            name="kill_process",
            description="Kill a process by its process ID.",
            input_schema={
                "type": "object",
                "properties": {
                    "process_id": {
                        "type": "integer",
                        "description": "Process ID to kill.",
                    },
                },
                "required": ["process_id"],
            },
        ),
        LLMToolAdapter(
            tool_call_logger=tool_call_logger,
            tool_func=process_tools.read_process,
            name="read_process",
            description="Read output from a running process.",
            input_schema={
                "type": "object",
                "properties": {
                    "process_id": {
                        "type": "integer",
                        "description": "Process ID to read from.",
                    },
                },
                "required": ["process_id"],
            },
        ),
        LLMToolAdapter(
            tool_call_logger=tool_call_logger,
            tool_func=process_tools.write_process,
            name="write_process",
            description="Write input to a process's stdin.",
            input_schema={
                "type": "object",
                "properties": {
                    "process_id": {
                        "type": "integer",
                        "description": "Process ID to write to.",
                    },
                    "input_text": {
                        "type": "string",
                        "description": "Text to write to the process's stdin.",
                    },
                },
                "required": ["process_id", "input_text"],
            },
        ),
        LLMToolAdapter(
            tool_call_logger=tool_call_logger,
            tool_func=process_tools.list_processes,
            name="list_processes",
            description="List all known processes and their states.",
            input_schema={
                "type": "object",
                "properties": {},
                "required": [],
            },
        ),
    ]
