"""Test that the StateManager can save and load ToolCallLogger state."""

import tempfile
import unittest
from pathlib import Path
from dataclasses import dataclass, field
from typing import Any, Dict, Optional

from research.agents.tools import (
    DialogMessages,
    ToolCallLogger,
    LoggedToolCall,
    ToolParam,
    ToolInputSchema,
)

from experimental.guy.agent_qa.state_manager import StateManager


class StateManagerToolCallLoggerTest(unittest.TestCase):
    """Test that the StateManager can save and load ToolCallLogger state."""

    def setUp(self):
        """Set up a temporary directory for test files."""
        self.temp_dir = tempfile.TemporaryDirectory()
        self.state_file = Path(self.temp_dir.name) / "test_state.pkl"

    def tearDown(self):
        """Clean up temporary directory."""
        self.temp_dir.cleanup()

    def test_save_and_load_tool_call_logger_state(self):
        """Test saving and loading ToolCallLogger state."""
        # Create a StateManager
        state_manager = StateManager(self.state_file)

        # Create a ToolCallLogger with some logged calls
        tool_call_logger = ToolCallLogger()

        # Create a tool parameter
        tool_param = ToolParam(
            name="test_tool",
            description="A test tool",
            input_schema={"type": "object", "properties": {}},
        )

        # Create a logged tool call
        tool_call = LoggedToolCall(
            tool=tool_param,
            tool_input={"arg1": "value1"},
            started=False,
            tool_output="test result",
            tool_message="Test tool call",
        )
        tool_call_logger.logged_calls.append(tool_call)

        # Save the ToolCallLogger state
        state_manager.update("logged_calls", tool_call_logger.logged_calls)

        # Create a new StateManager with the same state file
        new_state_manager = StateManager(self.state_file)

        # Create a new ToolCallLogger
        new_tool_call_logger = ToolCallLogger()

        # Load the ToolCallLogger state
        logged_calls = new_state_manager.get("logged_calls", None)
        self.assertIsNotNone(logged_calls)
        new_tool_call_logger.logged_calls = logged_calls

        # Verify that the logged calls were loaded correctly
        self.assertEqual(len(new_tool_call_logger.logged_calls), 1)
        loaded_call = new_tool_call_logger.logged_calls[0]
        self.assertEqual(loaded_call.tool.name, "test_tool")
        self.assertEqual(loaded_call.tool_input, {"arg1": "value1"})
        self.assertEqual(loaded_call.tool_output, "test result")
        self.assertFalse(loaded_call.started)

    def test_save_and_load_dialog_and_tool_call_logger_state(self):
        """Test saving and loading both DialogMessages and ToolCallLogger state."""
        # Create a StateManager
        state_manager = StateManager(self.state_file)

        # Create a DialogMessages with some messages
        dialog = DialogMessages()
        dialog.add_user_prompt("Hello")
        dialog.add_model_response([])

        # Create a ToolCallLogger with some logged calls
        tool_call_logger = ToolCallLogger()

        # Create a tool parameter
        tool_param = ToolParam(
            name="test_tool",
            description="A test tool",
            input_schema={"type": "object", "properties": {}},
        )

        # Create a logged tool call
        tool_call = LoggedToolCall(
            tool=tool_param,
            tool_input={"arg1": "value1"},
            started=False,
            tool_output="test result",
            tool_message="Test tool call",
        )
        tool_call_logger.logged_calls.append(tool_call)

        # Save both states
        state_manager.update_many(
            {
                "dialog_messages": dialog,
                "logged_calls": tool_call_logger.logged_calls,
            }
        )

        # Create a new StateManager with the same state file
        new_state_manager = StateManager(self.state_file)

        # Load both states
        new_dialog = new_state_manager.get("dialog_messages", None)
        self.assertIsNotNone(new_dialog)

        new_tool_call_logger = ToolCallLogger()
        logged_calls = new_state_manager.get("logged_calls", None)
        self.assertIsNotNone(logged_calls)
        new_tool_call_logger.logged_calls = logged_calls

        # Verify that the dialog was loaded correctly
        self.assertEqual(len(new_dialog._message_lists), 2)
        self.assertEqual(new_dialog._message_lists[0][0].text, "Hello")

        # Verify that the logged calls were loaded correctly
        self.assertEqual(len(new_tool_call_logger.logged_calls), 1)
        loaded_call = new_tool_call_logger.logged_calls[0]
        self.assertEqual(loaded_call.tool.name, "test_tool")
        self.assertEqual(loaded_call.tool_input, {"arg1": "value1"})
        self.assertEqual(loaded_call.tool_output, "test result")
        self.assertFalse(loaded_call.started)


if __name__ == "__main__":
    unittest.main()
