"""Tests for edit_agent.py."""

import json
import pytest
import re
from pathlib import Path
import shutil
import tempfile
from unittest.mock import Mock, patch

from experimental.guy.agent_qa.edit.edit_multi_file_agent import (
    EditMultiFileAgent,
    EditCompleteTool,
)
from experimental.guy.agent_qa.workspace_manager import WorkspaceManager
from research.agents.tools import DialogMessages, ToolCallLogger, ToolImplOutput
from research.llm_apis.llm_client import LLMClient, TextResult, ToolCall


@pytest.fixture
def mock_llm_client():
    """Create a mock LLM client."""
    client = Mock(spec=LLMClient)
    # Mock the generate method to return a simple response
    client.generate.return_value = (
        [TextResult(text="I'll help you edit the file.")],
        {"input_tokens": 100, "output_tokens": 50},
    )
    return client


@pytest.fixture
def mock_llm_client_with_tool_call():
    """Create a mock LLM client that returns a response with a tool call."""
    client = Mock(spec=LLMClient)
    # Mock the generate method to return a response with a tool call
    tool_call = ToolCall(
        tool_call_id="123",
        tool_name="edit_complete",
        tool_input={"summary_of_changes": "Made the requested changes."},
    )
    client.generate.return_value = (
        [tool_call],
        {"input_tokens": 100, "output_tokens": 50},
    )
    return client


@pytest.fixture
def mock_workspace_manager():
    """Create a mock workspace manager."""
    manager = Mock(spec=WorkspaceManager)
    manager.root = Path("/mock/workspace")
    return manager


@pytest.fixture
def temp_workspace():
    """Create a temporary directory to use as a workspace."""
    with tempfile.TemporaryDirectory() as temp_dir:
        workspace_path = Path(temp_dir)

        # Create some test files
        test_file = workspace_path / "test.py"
        test_file.write_text("def hello():\n    return 'Hello, world!'\n")

        # Create a subdirectory with a file
        subdir = workspace_path / "subdir"
        subdir.mkdir()
        subdir_file = subdir / "helper.py"
        subdir_file.write_text("def helper():\n    return 'I am a helper'\n")

        yield workspace_path


@pytest.fixture
def mock_tool_call_logger():
    """Create a mock tool call logger."""
    return Mock(spec=ToolCallLogger)


def test_edit_multi_file_agent_initialization(
    mock_llm_client, mock_workspace_manager, mock_tool_call_logger
):
    """Test that EditMultiFileAgent initializes correctly."""
    agent = EditMultiFileAgent(
        client=mock_llm_client,
        tool_call_logger=mock_tool_call_logger,
        workspace_manager=mock_workspace_manager,
        system_prompt="You are an AI assistant that helps with code editing.",
        tools=[],
        max_output_tokens_per_turn=1000,
        max_turns=5,
    )

    # Verify the agent was initialized with the correct properties
    assert agent.client == mock_llm_client
    assert agent.workspace_manager == mock_workspace_manager
    assert agent.max_output_tokens == 1000
    assert agent.max_turns == 5
    assert len(agent.tools) == 2  # Should have edit_tool and complete_tool

    # Verify the tools are of the correct type
    assert agent.edit_tool.name == "str_replace_editor"
    assert agent.complete_tool.name == "edit_complete"

    # Verify the initial state
    assert agent.success is False
    assert agent.num_input_tokens == 0
    assert agent.num_output_tokens == 0


def test_edit_multi_file_agent_reset(
    mock_llm_client, mock_workspace_manager, mock_tool_call_logger
):
    """Test the reset method of EditMultiFileAgent."""
    agent = EditMultiFileAgent(
        client=mock_llm_client,
        tool_call_logger=mock_tool_call_logger,
        workspace_manager=mock_workspace_manager,
        system_prompt="You are an AI assistant that helps with code editing.",
        tools=[],
        max_output_tokens_per_turn=1000,
        max_turns=5,
    )

    # Modify the agent state
    agent.success = True
    agent.num_input_tokens = 100
    agent.num_output_tokens = 50

    # Reset the agent
    agent.reset()

    # Verify the state was reset
    assert agent.success is False
    assert agent.num_input_tokens == 0
    assert agent.num_output_tokens == 0


def test_edit_multi_file_agent_get_tool_start_message(
    mock_llm_client, mock_workspace_manager, mock_tool_call_logger
):
    """Test the get_tool_start_message method."""
    agent = EditMultiFileAgent(
        client=mock_llm_client,
        tool_call_logger=mock_tool_call_logger,
        workspace_manager=mock_workspace_manager,
        system_prompt="You are an AI assistant that helps with code editing.",
        tools=[],
        max_output_tokens_per_turn=1000,
        max_turns=5,
    )

    message = agent.get_tool_start_message({"instruction": "Add a new function"})
    assert "Performing edit" in message


def test_edit_multi_file_agent_snapshot_workspace(
    mock_llm_client, mock_workspace_manager, mock_tool_call_logger
):
    """Test the snapshot_workspace method."""
    # Create a temporary directory to simulate a workspace
    with tempfile.TemporaryDirectory() as temp_workspace:
        # Create some test files in the workspace
        workspace_path = Path(temp_workspace)
        test_file1 = workspace_path / "test1.txt"
        test_file1.write_text("Test file 1 content")

        test_dir = workspace_path / "test_dir"
        test_dir.mkdir()
        test_file2 = test_dir / "test2.txt"
        test_file2.write_text("Test file 2 content")

        # Update the mock workspace manager to use our temp directory
        mock_workspace_manager.root = workspace_path

        # Create the agent
        agent = EditMultiFileAgent(
            client=mock_llm_client,
            tool_call_logger=mock_tool_call_logger,
            workspace_manager=mock_workspace_manager,
            system_prompt="You are an AI assistant that helps with code editing.",
            tools=[],
            max_output_tokens_per_turn=1000,
            max_turns=5,
        )

        # Take a snapshot
        snapshot_dir = agent.snapshot_workspace()

        try:
            # Verify the snapshot contains the expected files
            assert (snapshot_dir / "test1.txt").exists()
            assert (snapshot_dir / "test_dir").exists()
            assert (snapshot_dir / "test_dir" / "test2.txt").exists()

            # Verify the content of the files
            assert (snapshot_dir / "test1.txt").read_text() == "Test file 1 content"
            assert (
                snapshot_dir / "test_dir" / "test2.txt"
            ).read_text() == "Test file 2 content"
        finally:
            # Clean up the snapshot directory
            if snapshot_dir.exists():
                shutil.rmtree(snapshot_dir)


def test_wrap_instruction_in_user_message(
    mock_llm_client, mock_workspace_manager, mock_tool_call_logger
):
    """Test the wrap_instruction_in_user_message method."""
    agent = EditMultiFileAgent(
        client=mock_llm_client,
        tool_call_logger=mock_tool_call_logger,
        workspace_manager=mock_workspace_manager,
        system_prompt="You are an AI assistant that helps with code editing.",
        tools=[],
        max_output_tokens_per_turn=1000,
        max_turns=5,
    )

    # Test with a simple instruction
    instruction = "Add a docstring to the function"
    message = agent.wrap_instruction_in_user_message(instruction)

    # Verify the message contains the instruction
    assert instruction in message
    # Verify the message mentions the complete tool
    assert agent.complete_tool.name in message


def test_edit_complete_tool(mock_tool_call_logger):
    """Test the EditCompleteTool class."""
    # Create the tool
    tool = EditCompleteTool(mock_tool_call_logger)

    # Test initial state
    assert tool.name == "edit_complete"
    assert not tool.should_stop
    assert tool.summary == ""

    # Test running the tool
    result = tool.run_impl({"summary": "Fixed the bug"})

    # Verify the result
    assert isinstance(result, ToolImplOutput)
    assert result.tool_output == "Task completed"
    assert result.tool_result_message == "Task completed"

    # Verify the tool state was updated
    assert tool.summary == "Fixed the bug"
    # The should_stop property should now be True
    assert tool.should_stop

    # Test reset
    tool.reset()
    assert not tool.should_stop
    assert tool.summary == ""


@patch.object(EditMultiFileAgent, "snapshot_workspace")
def test_generate_tool_output(
    mock_snapshot, mock_llm_client, mock_workspace_manager, mock_tool_call_logger
):
    """Test the generate_tool_output method."""
    # Setup the snapshot mock to return a temporary directory
    temp_snapshot = Path(tempfile.mkdtemp())
    mock_snapshot.return_value = temp_snapshot

    try:
        # Create the agent
        agent = EditMultiFileAgent(
            client=mock_llm_client,
            tool_call_logger=mock_tool_call_logger,
            workspace_manager=mock_workspace_manager,
            system_prompt="You are an AI assistant that helps with code editing.",
            tools=[],
            max_output_tokens_per_turn=1000,
            max_turns=3,
        )

        # Test with a successful edit
        agent.complete_tool.summary = "Added a new function"
        result = agent.generate_tool_output(temp_snapshot)

        # Verify the result
        assert isinstance(result, ToolImplOutput)
        assert "Added a new function" in result.tool_output
        assert result.tool_result_message == "Edit was completed."

        # Test with an unsuccessful edit
        agent.complete_tool.summary = ""
        result = agent.generate_tool_output(temp_snapshot)

        # Verify the result
        assert isinstance(result, ToolImplOutput)
        assert "did not complete after max turns" in result.tool_output
        assert result.tool_result_message == "Edit was NOT completed."
    finally:
        # Clean up the temporary directory
        if temp_snapshot.exists():
            shutil.rmtree(temp_snapshot)
