import copy
from pathlib import Path
import platform
import shutil
import tempfile
from typing import Any, Callable, Optional

from experimental.guy.agent_qa.changes import RecentChang<PERSON><PERSON>rovider
from experimental.guy.agent_qa.memories import Memories
from experimental.guy.agent_qa.workspace_manager import WorkspaceManager
from research.agents.tools import (
    <PERSON>alogMessages,
    LLMTool,
    Tool<PERSON>allLogger,
    ToolImplOutput,
)
from experimental.guy.agent_qa.builtin_tools import (
    CodebaseKnowledgeTool,
)
from experimental.guy.agent_qa.file_edit.str_replace_editor_tool import (
    StrReplaceEditorTool,
)
from research.core.diff_utils import Repository, compute_repo_diff
from research.llm_apis.llm_client import LLMClient, TextResult


def compare_workspaces(before_dir: Path, after_dir: Path) -> str:
    """Compare two workspace directories and return the diff as a string."""
    before_repo = Repository.load(before_dir)
    after_repo = Repository.load(after_dir)
    patch_set = compute_repo_diff(
        before_repo, after_repo, num_context_lines=3, ignore_whitespace=False
    )
    return str(patch_set)


def get_workspace_prompt_builder(
    workspace_root: Path,
    knowledge_tool: CodebaseKnowledgeTool,
    recent_changes_provider: Optional[RecentChangesProvider] = None,
    pr_changes: bool = False,
    memories: Optional[Memories] = None,
) -> Callable[[], str]:
    def build_workspace_prompt() -> str:
        """Build the system prompt suffix that contains workspace-specific information.

        Args:
            workspace_root: The root directory of the workspace
            knowledge_tool: Tool for accessing codebase knowledge
            recent_changes_provider: Optional provider for recent changes
            pr_changes: Whether to include PR changes in the prompt
            memories: Optional memories to include

        Returns:
            The constructed system prompt suffix
        """
        workspace_prompt = f"""
{memories.get_memories() if memories else ""}
"""
        return workspace_prompt

    return build_workspace_prompt


class EditCompleteTool(LLMTool):
    name = "edit_complete"
    """The model should call this tool when it is done with the edit."""

    description = "Call this tool when you are done with the current edit task, and provide a high-level summary of the changes you've made."
    input_schema = {
        "type": "object",
        "properties": {
            "summary": {
                "type": "string",
                "description": "A high-level description of the changes made. No need to include actual code changes as the diff will be automatically shown.",
            },
        },
        "required": ["summary"],
    }

    def __init__(self, tool_call_logger: ToolCallLogger):
        super().__init__(tool_call_logger)
        self.summary: str = ""

    @property
    def should_stop(self):
        return self.summary != ""

    def reset(self):
        self.summary = ""

    def run_impl(
        self,
        tool_input: dict[str, Any],
        dialog_messages: Optional[DialogMessages] = None,
    ) -> ToolImplOutput:
        assert tool_input["summary"], "Model returned empty summary"
        self.summary = tool_input["summary"]
        return ToolImplOutput("Task completed", "Task completed")

    def get_tool_start_message(self, tool_input: dict[str, Any]) -> str:
        return ""


def build_system_prompt_for_edit_multi_file_agent(
    workspace_root: Path,
    knowledge_tool: CodebaseKnowledgeTool,
    recent_changes_provider: Optional[RecentChangesProvider] = None,
    pr_changes: bool = False,
    memories: Optional[Memories] = None,
    swebench_mode: bool = False,
    swebench_sparse_system_prompt: bool = False,
) -> str:
    """Build the system prompt for the agent.

    Args:
        workspace_root: The root directory of the workspace
        knowledge_tool: Tool for accessing codebase knowledge
        recent_changes_provider: Optional provider for recent changes
        pr_changes: Whether to include PR changes in the prompt
        memories: Optional memories to include

    Returns:
        The constructed system prompt
    """
    workspace_prompt_builder_fn = get_workspace_prompt_builder(
        workspace_root, knowledge_tool, recent_changes_provider, pr_changes, memories
    )
    workspace_prompt = workspace_prompt_builder_fn()

    if swebench_mode:
        if swebench_sparse_system_prompt:
            system_prompt = f"""\
You are an AI assistant helping a software engineer implement pull requests,
and you have access to tools to interact with the engineer's codebase.

Working directory: {workspace_root}
Operating system: {platform.system()}

"""
        else:
            system_prompt = f"""\
You are an AI assistant helping a software engineer implement pull requests,
and you have access to tools to interact with the engineer's codebase.

Working directory: {workspace_root}
Operating system: {platform.system()}

Guidelines:
- You are working in a codebase with other engineers and many different components. Be careful that changes you make in one component don't break other components.
- When designing changes, implement them as a senior software engineer would. This means following best practices such as separating concerns and avoiding leaky interfaces.
- When possible, choose the simpler solution.
- You CANNOT run code or tests, but you can verify your changes through these methods:
    - Use the request_codebase_information tool to confirm correct API usage
    - Analyze your code for potential bugs or edge cases
    - Check consistency with existing codebase patterns
    - Reason through the logic step by step
"""
    else:
        system_prompt = f"""\
# Role
You are an AI assistant, with access to the developer's codebase.
You can read from and write to the codebase using the provided tools.
You CANNOT run code or tests, but you can verify your changes through these methods:
- Use the request_codebase_information tool to confirm correct API usage
- Analyze your code for potential bugs or edge cases
- Check consistency with existing codebase patterns
- Reason through the logic step by step

# Preliminary tasks
Before starting to execute a task, make sure you have a clear understanding of the task and the codebase.
Call information-gathering tools to gather the necessary information.
If you need information about the current state of the codebase, use the request_codebase_information tool.

# Planning
Once you have performed preliminary rounds of information-gathering, come up with a low-level, extremely detailed plan for the actions you want to take.
Provide a bulleted list of each file you think you need to change.
Be sure to be careful and exhaustive.
Feel free to think about in a chain of thought first.
If, in the course of planning, you realize you need more information, feel free to perform more information-gathering steps.

# Making edits
When making edits, use the provided {StrReplaceEditorTool.name} edit tool - do NOT just write a new file.
Before calling the edit tool, ALWAYS first call the request_codebase_information tool asking for highly detailed information about the code you want to edit.
Ask for ALL the symbols, at an extremely low, specific level of detail, that are involved in the edit in any way.
Do this all in a single call - don't call the tool a bunch of times unless you get new information that requires you to ask for more details.
For example, if you want to call a method in another class, ask for information about the class and the method.
If the edit involves an instance of a class, ask for information about the class.
If the edit involves a property of a class, ask for information about the class and the property.
If several of the above apply, ask for all of them in a single call.
When in any doubt, include the symbol or object.
When making changes, be very conservative and respect the codebase.

# Following instructions
Focus on doing what the user asks you to do.
Do NOT do more than the user asked.

# Summary of changes
Once you've made all the changes, call the {EditCompleteTool.name} tool to end the task.
Provide a concise but comprehensive summary of the changes you've made.
Include all relevant information in the summary parameter, as this will be shown to the user.
Do not include detailed explanations in your final message (user will not see the text message) - focus on making the summary complete.

# Recovering from difficulties
If you notice yourself going around in circles, or going down a rabbit hole, for example calling the same tool in similar ways multiple times to accomplish the same task, call {EditCompleteTool.name} prematurely and describe your problems in the summary.

{workspace_prompt}

# Summary of most important instructions
- Search for information to carry out the user request
- Always make a detailed plan before taking any action
- Make sure you have all the information before making edits
- Focus on following user instructions and ask before carrying out any actions beyond the user's instructions
- Summarize your changes when you are done in {EditCompleteTool.name} summary
- If you find yourself repeatedly calling tools without making progress, call {EditCompleteTool.name} prematurely and describe your problems in the summary.
"""

    return system_prompt


class EditMultiFileAgent(LLMTool):
    name = "edit_agent"
    """An agent that performs small scoped editing tasks across multiple files."""

    description = """\
Performs focused code editing tasks such as implementing new functions, fixing failing tests, or refactoring code.
Provide clear instructions about what needs to be changed, but avoid being overly specific or including code snippets.
This tool can modify multiple files and search the codebase for relevant context.
This tool cannot run unit tests or execute code.
"""
    input_schema = {
        "type": "object",
        "properties": {
            "instruction": {
                "type": "string",
                "description": "A detailed description of the code changes to be implemented. Should specify what needs to be modified, added, or removed, but does not need to include actual code snippets.",
            },
        },
        "required": ["instruction"],
    }

    def __init__(
        self,
        client: LLMClient,
        tool_call_logger: ToolCallLogger,
        workspace_manager: WorkspaceManager,
        system_prompt: str,
        tools: list[LLMTool],
        max_output_tokens_per_turn: int,
        max_turns: int,
    ):
        super().__init__(tool_call_logger)
        self.client = client
        self.workspace_manager = workspace_manager
        self.max_output_tokens = max_output_tokens_per_turn
        self.max_turns = max_turns
        self.edit_tool = StrReplaceEditorTool(
            tool_call_logger=tool_call_logger,
            workspace_manager=workspace_manager,
        )
        self.complete_tool = EditCompleteTool(tool_call_logger)
        self.tools = [
            self.edit_tool,
            self.complete_tool,
        ] + tools
        self.system_prompt = system_prompt
        self.reset()

    def reset(self):
        self.success = False
        self.num_input_tokens = 0
        self.num_output_tokens = 0

    def wrap_instruction_in_user_message(
        self, instruction: str, target_file_content: Optional[str] = None
    ) -> str:
        return f"""\
Now, let's focus on the specific task at hand. Make edits to the codebase as specified in the instruction:

{instruction}

After you are done, call the {self.complete_tool.name} tool to mark the end of this task."""

    def generate_aux_data(self) -> dict[str, Any]:
        return {
            "success": self.success,
            "num_input_tokens": self.num_input_tokens,
            "num_output_tokens": self.num_output_tokens,
        }

    def generate_tool_output(self, before_snapshot: Path) -> ToolImplOutput:
        after_snapshot = self.snapshot_workspace()
        try:
            # Generate the diff between the snapshots
            diff = compare_workspaces(before_snapshot, after_snapshot)

            # Format the output message
            if self.complete_tool.should_stop:
                print(f"[EDIT AGENT SUMMARY]\n{self.complete_tool.summary}")
                tool_output = f"The following changes has been made:\n\n{self.complete_tool.summary}\n\nSpecifically, the diff is:\n\n{diff}\n---"
                tool_result_message = "Edit was completed."
                self.success = True
            else:
                tool_output = f"edit_agent did not complete after max turns. The changes generated so far are:\n\n{diff}\n---"
                tool_result_message = "Edit was NOT completed."

            # Create the result object
            result = ToolImplOutput(
                tool_output=tool_output,
                tool_result_message=tool_result_message,
                auxiliary_data=self.generate_aux_data(),
            )
            return result
        finally:
            # Clean up the snapshot directories
            if before_snapshot.exists():
                shutil.rmtree(before_snapshot)
            if after_snapshot.exists():
                shutil.rmtree(after_snapshot)

    def run_impl(
        self,
        tool_input: dict[str, Any],
        dialog_messages: Optional[DialogMessages] = None,
    ) -> ToolImplOutput:
        instruction = tool_input["instruction"]
        print(f"[EDIT AGENT INSTRUCTION]\n{instruction}")
        assert dialog_messages is not None

        dialog_messages = copy.deepcopy(dialog_messages)
        # Drop our own tool call from the dialogue messages
        dialog_messages.drop_tool_calls_from_final_turn()
        dialog_messages.add_user_prompt(
            self.wrap_instruction_in_user_message(
                instruction,
            ),
            allow_append_to_tool_call_results=False,
        )

        # Take a snapshot before running the EditAgent
        before_snapshot = self.snapshot_workspace()

        for turn in range(self.max_turns):
            response, metadata = self.client.generate(
                messages=dialog_messages.get_messages_for_llm_client(),
                max_tokens=self.max_output_tokens,
                tools=[tool.get_tool_param() for tool in self.tools],
                system_prompt=self.system_prompt,
                tool_choice={"type": "auto"},
            )
            dialog_messages.add_model_response(response)

            self.num_input_tokens += metadata["input_tokens"]
            self.num_output_tokens += metadata["output_tokens"]

            pending_tool_calls = dialog_messages.get_pending_tool_calls()

            if len(pending_tool_calls) == 0:
                # No tool calls, continue to the next turn
                continue
            elif len(pending_tool_calls) > 1:
                raise ValueError("Only one tool call per turn is supported")

            tool_call = pending_tool_calls[0]
            try:
                tool = next(t for t in self.tools if t.name == tool_call.tool_name)
            except StopIteration as exc:
                raise ValueError(
                    f"Tool with name {tool_call.tool_name} not found"
                ) from exc

            result = tool.run(tool_call.tool_input, copy.deepcopy(dialog_messages))

            # Handle both ToolResult objects and tuples
            if isinstance(result, tuple):
                tool_result, _ = result
            else:
                tool_result = result

            dialog_messages.add_tool_call_result(tool_call, tool_result)

            if self.complete_tool.should_stop:
                # We don't expect this to be necessary, but just in case:
                # Add a fake model response, so the next turn is the user's
                # turn in case they want to resume
                dialog_messages.add_model_response(
                    [TextResult(text="Completed the task.")]
                )
                return self.generate_tool_output(before_snapshot)

        return self.generate_tool_output(before_snapshot)

    def get_tool_start_message(self, tool_input: dict[str, Any]) -> str:
        return "Performing edit given the instructions."

    def snapshot_workspace(self) -> Path:
        """Take a snapshot of the current workspace by copying it to a temporary directory."""

        temp_dir = Path(tempfile.mkdtemp(prefix="edit_agent_snapshot_"))
        source_dir = self.workspace_manager.root

        def ignore_hidden(_dir_path, file_names):
            return [f for f in file_names if f.startswith(".")]

        shutil.copytree(
            source_dir,
            temp_dir,
            symlinks=True,
            ignore=ignore_hidden,
            dirs_exist_ok=True,
        )
        return temp_dir
