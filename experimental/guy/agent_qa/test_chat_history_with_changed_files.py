"""Test that changed files appear correctly in the chat history."""

import unittest

from research.agents.changed_file import ChangedFile
from research.agents.tools import DialogMessages
from research.llm_apis.llm_client import TextPrompt, TextResult

from experimental.guy.agent_qa.chat_history import get_chat_history


class TestChatHistoryWithChangedFiles(unittest.TestCase):
    """Test that changed files appear correctly in the chat history."""

    def test_changed_files_in_chat_history(self):
        """Test that changed files appear correctly in the chat history."""
        # Create a DialogMessages object
        dialog = DialogMessages()

        # Add a user message
        dialog.add_user_prompt("Can you create a hello world program?")

        # Add an assistant response
        dialog.add_model_response(
            [TextResult("Sure, I'll create a hello world program for you.")]
        )

        # Add workspace changes
        changed_file = ChangedFile.added(
            new_path="hello_world.py", new_contents="print('Hello, world!')"
        )
        dialog.add_workspace_changes([changed_file])

        # Get the chat history
        history = get_chat_history(dialog)

        # Verify that the changed files are included in the chat history
        self.assertIn("chat_history", history)
        self.assertEqual(len(history["chat_history"]), 1)

        # Check that the exchange has changed files
        exchange = history["chat_history"][0]
        self.assertIn("changed_files", exchange)
        self.assertEqual(len(exchange["changed_files"]), 1)

        # Check the changed file details
        changed_file_dict = exchange["changed_files"][0]
        self.assertEqual(changed_file_dict["new_path"], "hello_world.py")
        self.assertEqual(changed_file_dict["new_contents"], "print('Hello, world!')")
        self.assertEqual(changed_file_dict["change_type"], "ADDED")

    def test_multiple_changed_files(self):
        """Test that multiple changed files appear correctly in the chat history."""
        # Create a DialogMessages object
        dialog = DialogMessages()

        # Add a user message
        dialog.add_user_prompt("Can you create a hello world program and a README?")

        # Add an assistant response
        dialog.add_model_response([TextResult("Sure, I'll create both files for you.")])

        # Add workspace changes
        changed_files = [
            ChangedFile.added(
                new_path="hello_world.py", new_contents="print('Hello, world!')"
            ),
            ChangedFile.added(
                new_path="README.md",
                new_contents="# Hello World\n\nA simple hello world program.",
            ),
        ]
        dialog.add_workspace_changes(changed_files)

        # Get the chat history
        history = get_chat_history(dialog)

        # Verify that the changed files are included in the chat history
        self.assertIn("chat_history", history)
        self.assertEqual(len(history["chat_history"]), 1)

        # Check that the exchange has changed files
        exchange = history["chat_history"][0]
        self.assertIn("changed_files", exchange)
        self.assertEqual(len(exchange["changed_files"]), 2)

        # Check the first changed file details
        changed_file_dict = exchange["changed_files"][0]
        self.assertEqual(changed_file_dict["new_path"], "hello_world.py")
        self.assertEqual(changed_file_dict["new_contents"], "print('Hello, world!')")
        self.assertEqual(changed_file_dict["change_type"], "ADDED")

        # Check the second changed file details
        changed_file_dict = exchange["changed_files"][1]
        self.assertEqual(changed_file_dict["new_path"], "README.md")
        self.assertEqual(
            changed_file_dict["new_contents"],
            "# Hello World\n\nA simple hello world program.",
        )
        self.assertEqual(changed_file_dict["change_type"], "ADDED")


if __name__ == "__main__":
    unittest.main()
