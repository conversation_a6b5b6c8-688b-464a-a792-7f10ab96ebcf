"""Command approval management for the agent."""

import json
import os
from pathlib import Path
from typing import Set


class CommandApprovalManager:
    """Manages command approvals, including persistent storage for always-approved commands."""

    def __init__(self, cache_dir: Path):
        """Initialize the command approval manager.

        Args:
            cache_dir: Base cache directory for the agent
        """
        self._session_approved: Set[str] = set()
        self._always_approved_path = cache_dir / "approved_commands.json"
        self._always_approved: Set[str] = self._load_always_approved()

    def _load_always_approved(self) -> Set[str]:
        """Load the set of always-approved commands from disk."""
        if not self._always_approved_path.exists():
            return set()

        try:
            with open(self._always_approved_path, "r") as f:
                return set(json.load(f))
        except (json.JSONDecodeError, IOError):
            return set()

    def _save_always_approved(self):
        """Save the set of always-approved commands to disk."""
        self._always_approved_path.parent.mkdir(parents=True, exist_ok=True)
        with open(self._always_approved_path, "w") as f:
            json.dump(list(self._always_approved), f)

    def is_approved(self, command: str) -> bool:
        """Check if a command is approved either for this session or always."""
        return command in self._session_approved or command in self._always_approved

    def approve_for_session(self, command: str):
        """Approve a command for the current session."""
        self._session_approved.add(command)

    def approve_always(self, command: str):
        """Approve a command permanently."""
        self._always_approved.add(command)
        self._save_always_approved()

    def get_approval(self, command: str) -> bool:
        """Get user approval for a command, handling session and permanent approvals.

        Returns:
            bool: True if the command is approved, False otherwise.
        """
        if self.is_approved(command):
            return True

        print(f"About to run command: {command}")
        print("Options:")
        print("  Y: approve once (default)")
        print("  n: decline")
        print("  s: approve for this session")
        print("  a: approve always (saved for future sessions)")

        while True:
            user_input = input("Run command? [Y/n/s/a] ").lower()
            if not user_input:
                user_input = "y"  # Default to Y

            if user_input in ["y", "Y"]:  # Accept both Y and y
                return True
            elif user_input == "n":
                return False
            elif user_input == "s":
                self.approve_for_session(command)
                return True
            elif user_input == "a":
                self.approve_always(command)
                return True
