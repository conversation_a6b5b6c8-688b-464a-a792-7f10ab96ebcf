from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import List

from experimental.guy.agent_qa.builtin_tools import LLMTool
from research.agents.tools import DialogMessages


@dataclass
class AgentMode:
    """Configuration for a specific agent mode."""

    name: str
    system_prompt: str
    tools: List[LLMTool]

    def with_tools(self, tools: List[LLMTool]) -> "AgentMode":
        """Create a new AgentMode with the same name and system prompt but different tools."""
        return AgentMode(name=self.name, system_prompt=self.system_prompt, tools=tools)


class AgentModeProvider(ABC):
    """Interface for determining agent mode based on dialog history."""

    @abstractmethod
    def get_current_mode(self) -> AgentMode:
        """Get the current agent mode.

        Returns:
            The current agent mode configuration
        """
        pass

    @abstractmethod
    def update_mode(self, dialog_messages: DialogMessages) -> None:
        """Update the current mode based on dialog history.

        Args:
            dialog_messages: The current dialog history
        """
        pass


class ConstantModeProvider(AgentModeProvider):
    """A mode provider that always returns the same mode."""

    def __init__(self, mode: AgentM<PERSON>):
        self.mode = mode

    def get_current_mode(self) -> AgentMode:
        return self.mode

    def update_mode(self, dialog_messages: DialogMessages) -> None:
        # No-op since this provider always returns the same mode
        pass
