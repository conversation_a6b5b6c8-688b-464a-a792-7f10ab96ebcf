#!/usr/bin/env python3
"""Test for the /chat-history-proto command in the CLI agent.

This test verifies that the /chat-history-proto command correctly returns
the chat history in proto format.
"""

import json
import sys
import io
import os
import tempfile
from pathlib import Path
from contextlib import redirect_stdout, redirect_stderr

import pytest
from experimental.guy.agent_qa.test_cli_agent_e2e import (
    run_agent,
    AgentResult,
)
from experimental.guy.agent_qa.interactive_agent import main


@pytest.fixture
def work_dir():
    """Create a temporary directory for the test."""
    with tempfile.TemporaryDirectory() as temp_dir:
        yield Path(temp_dir)


def run_interactive_agent_with_commands(
    work_dir: Path, commands: list[str]
) -> AgentResult:
    """Run the interactive agent with a sequence of commands.

    Args:
        work_dir: The workspace directory
        commands: List of commands to send to the agent

    Returns:
        The result of the agent run
    """
    # Save original environment
    old_env = os.environ.copy()

    try:
        # Capture stdout and stderr
        stdout_buffer = io.StringIO()
        stderr_buffer = io.StringIO()

        with redirect_stdout(stdout_buffer), redirect_stderr(stderr_buffer):
            # Create a temporary file for input commands
            import tempfile

            with tempfile.NamedTemporaryFile(mode="w+") as input_file:
                # Write commands to the file
                for cmd in commands:
                    input_file.write(cmd + "\n")
                input_file.flush()
                input_file.seek(0)

                # Redirect stdin to read from the file
                old_stdin = sys.stdin
                sys.stdin = input_file

                try:
                    # Run the interactive agent
                    args = [
                        "--workspace_root",
                        str(work_dir),
                        "--approve-command-execution",
                        "--no-integration-warnings",
                        "--no-pager",  # Disable pager to avoid test hanging
                    ]
                    returncode = main(args) or 0
                finally:
                    # Restore stdin
                    sys.stdin = old_stdin

        return AgentResult(
            stdout=stdout_buffer.getvalue(),
            stderr=stderr_buffer.getvalue(),
            returncode=returncode,
        )
    finally:
        # Restore original environment
        os.environ.clear()
        os.environ.update(old_env)


@pytest.mark.manual
@pytest.mark.skip_in_ci
def test_chat_history_proto_command(work_dir: Path) -> None:
    """Test that the /chat-history-proto command returns the chat history in proto format."""
    # Run the agent with a question followed by the chat-history-proto command
    commands = [
        "What is the capital of France?",  # Ask a question
        "/chat-history-proto",  # Get the chat history
        "/quit",  # Quit the agent
    ]

    result = run_interactive_agent_with_commands(work_dir, commands)

    # Print the output for debugging
    print("Full output:")
    print(result.stdout)

    # Verify the output contains the expected JSON structure
    # Look for JSON content in the output
    import re

    # More specific pattern to match the JSON output from the chat-history-proto command
    # Look for a JSON object that starts after a prompt line and ends before another prompt or end of string
    json_pattern = r"/tmp/[^\n]+\s+>>>\s+({[\s\S]*?})\s+(/tmp/|$)"
    json_matches = re.findall(json_pattern, result.stdout)

    # Try each potential JSON match
    for json_str in json_matches:
        if isinstance(json_str, tuple):
            json_str = json_str[0]  # Extract the first group if it's a tuple

        try:
            print(f"Trying to parse JSON: {json_str[:100]}...")
            chat_history = json.loads(json_str)

            # Verify the structure
            if "chat_history" in chat_history and len(chat_history["chat_history"]) > 0:
                # Found valid chat history JSON
                exchange = chat_history["chat_history"][0]["exchange"]

                # Verify content
                if (
                    "request_message" in exchange
                    and "capital of France" in exchange["request_message"]
                ):
                    if (
                        "response_text" in exchange
                        and "Paris" in exchange["response_text"]
                    ):
                        print("Chat history proto command test passed!")
                        return
        except json.JSONDecodeError as e:
            # Not valid JSON, try next match
            print(f"JSON decode error: {e}")
            continue
        except Exception as e:
            print(f"Error processing JSON: {e}")
            continue

    # If we get here, no valid chat history JSON was found
    assert False, "No valid chat history JSON found in output"
