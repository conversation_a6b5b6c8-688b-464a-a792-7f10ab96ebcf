"""Integration tests for Google Custom Search API."""

import pytest
from unittest.mock import <PERSON><PERSON><PERSON>, patch
import os
import json
import requests
from pathlib import Path
from experimental.guy.agent_qa.tools.web_search_tool import WebSearchTool
from research.agents.tools import ToolCallLogger


@pytest.fixture
def mock_config(tmp_path):
    """Create a mock config file."""
    config = {
        "api_key": "test_key",  # pragma: allowlist secret
        "search_engine_id": "test_cx",
    }
    config_dir = tmp_path / ".augment" / "agent"
    config_dir.mkdir(parents=True)
    config_path = config_dir / "google_search_api_settings.json"
    config_path.write_text(json.dumps(config))
    os.environ["AUGMENT_AGENT_CONFIG_DIR"] = str(tmp_path / ".augment" / "agent")
    return str(tmp_path)


@pytest.fixture
def mock_response():
    """Create a mock response with search results."""
    response = MagicMock()
    response.json.return_value = {
        "items": [
            {
                "title": "Python Programming Language",
                "link": "https://www.python.org",
                "snippet": "Python is a programming language that lets you work quickly and integrate systems more effectively.",
            },
            {
                "title": "Learn Python",
                "link": "https://www.learnpython.org",
                "snippet": "Free interactive Python tutorial. Learn Python in the most social and fun way.",
            },
            {
                "title": "Python Tutorial",
                "link": "https://www.w3schools.com/python",
                "snippet": "Python is a popular programming language. Python can be used on a server to create web applications.",
            },
        ]
    }
    return response


@pytest.mark.integration
def test_basic_search(mock_response, mock_config):
    """Test a basic search query."""
    with patch(
        "experimental.guy.agent_qa.tools.web_search.requests.get",
        return_value=mock_response,
    ):
        tool = WebSearchTool(ToolCallLogger())
        result = tool.run_impl(
            {"query": "python programming language", "num_results": 3}
        )
        assert "No results found" not in result.tool_output
        assert "Error" not in result.tool_output
        assert (
            "[Python Programming Language](https://www.python.org)"
            in result.tool_output
        )
        assert "programming language that lets you work quickly" in result.tool_output
        assert "Found 3 results" in result.tool_result_message


@pytest.mark.integration
def test_search_with_quotes(mock_response, mock_config):
    """Test search with quoted phrase."""
    # Mock response for AI ethics search
    mock_response.json.return_value = {
        "items": [
            {
                "title": "Artificial Intelligence Ethics",
                "link": "https://example.com/ai-ethics",
                "snippet": "Discussion of artificial intelligence ethics...",
            }
        ]
    }

    with patch(
        "experimental.guy.agent_qa.tools.web_search.requests.get",
        return_value=mock_response,
    ):
        tool = WebSearchTool(ToolCallLogger())
        result = tool.run_impl(
            {"query": '"artificial intelligence" ethics', "num_results": 3}
        )
        assert "artificial intelligence" in result.tool_output.lower()
        assert "Found" in result.tool_result_message


@pytest.mark.integration
def test_search_no_results(mock_config):
    """Test search that returns no results."""
    mock_response = MagicMock()
    mock_response.json.return_value = {}

    with patch(
        "experimental.guy.agent_qa.tools.web_search.requests.get",
        return_value=mock_response,
    ):
        tool = WebSearchTool(ToolCallLogger())
        result = tool.run_impl(
            {"query": "asdkfjasldkfjasldfkjasldkfjalskdfj", "num_results": 3}
        )
        assert "No results found" in result.tool_output
        assert "Found 0 results" in result.tool_result_message


@pytest.mark.integration
def test_search_max_results(mock_config):
    """Test search with maximum number of results."""
    mock_response = MagicMock()
    mock_response.json.return_value = {
        "items": [
            {
                "title": f"Result {i}",
                "link": f"https://example.com/{i}",
                "snippet": f"Description {i}",
            }
            for i in range(10)
        ]
    }

    with patch(
        "experimental.guy.agent_qa.tools.web_search.requests.get",
        return_value=mock_response,
    ):
        tool = WebSearchTool(ToolCallLogger())
        result = tool.run_impl({"query": "test query", "num_results": 10})
        assert "Found 10 results" in result.tool_result_message
        for i in range(10):
            assert f"Result {i}" in result.tool_output


@pytest.mark.integration
def test_search_special_characters(mock_response, mock_config):
    """Test search with special characters."""
    # Mock response for C++ search
    mock_response.json.return_value = {
        "items": [
            {
                "title": "C++ Programming & Debugging",
                "link": "https://example.com/cpp",
                "snippet": "Learn about C++ programming and debugging...",
            }
        ]
    }

    with patch(
        "experimental.guy.agent_qa.tools.web_search.requests.get",
        return_value=mock_response,
    ):
        tool = WebSearchTool(ToolCallLogger())
        result = tool.run_impl(
            {"query": "C++ programming & debugging", "num_results": 3}
        )
        assert "No results found" not in result.tool_output
        assert "Error" not in result.tool_output
        assert "Found 1 results" in result.tool_result_message


@pytest.mark.integration
def test_api_error(mock_config):
    """Test handling of API errors."""
    with patch(
        "experimental.guy.agent_qa.tools.web_search.requests.get",
        side_effect=requests.RequestException("API Error"),
    ):
        tool = WebSearchTool(ToolCallLogger())
        result = tool.run_impl({"query": "test query", "num_results": 3})
        assert "No results found" in result.tool_output
        assert "Search failed" in result.tool_result_message
