"""Tests for interactive_agent.py."""

import os
import tempfile
from pathlib import Path

import pytest

from experimental.guy.agent_qa.interactive_agent import main


def test_main_with_args():
    """Test that main() accepts command line arguments."""
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)

        # Create a dummy auth token file
        token_file = temp_path / "token"
        token_file.write_text("dummy_token")

        # Create a dummy workspace
        workspace = temp_path / "workspace"
        workspace.mkdir()

        # Create a dummy knowledge path
        knowledge_path = temp_path / "knowledge"
        knowledge_path.mkdir()

        # Set up environment variable for token
        os.environ["AUGMENT_API_TOKEN"] = "dummy_token"

        # Test with --list-tools which is safe to run and doesn't require real setup
        args = [
            "-w",
            str(workspace),
            "--knowledge_path",
            str(knowledge_path),
            "--agent-cache-dir",
            str(temp_path),
            "--auth-token-file",
            str(token_file),
            "--list-tools",
            "--no-integration-warnings",  # Avoid warnings about missing integrations
        ]

        # This should run without error and return 0
        assert main(args) == 0
