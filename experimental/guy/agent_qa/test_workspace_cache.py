"""Tests for workspace cache."""

import json
import tempfile
from pathlib import Path
import pytest
from experimental.guy.agent_qa.workspace_cache import WorkspaceCache, FileInfo


@pytest.fixture
def workspace_root():
    """Create a temporary workspace root."""
    with tempfile.TemporaryDirectory() as temp_dir:
        yield Path(temp_dir)


@pytest.fixture
def cache_root():
    """Create a temporary cache root."""
    with tempfile.TemporaryDirectory() as temp_dir:
        yield Path(temp_dir)


def test_cache_initialization(workspace_root, cache_root):
    """Test that cache initializes correctly."""
    cache = WorkspaceCache(workspace_root, cache_root)
    assert cache.workspace_root == workspace_root
    assert cache.cache_root == cache_root
    assert cache.cache_file.parent == cache_root / "blob_cache"


def test_cache_default_root(workspace_root):
    """Test that cache uses default root when none provided."""
    cache = WorkspaceCache(workspace_root)
    assert cache.cache_root == Path.home() / ".augment" / "agent"
    assert cache.cache_file.parent == cache.cache_root / "blob_cache"


def test_cache_file_info(workspace_root, cache_root):
    """Test adding and retrieving file info."""
    cache = WorkspaceCache(workspace_root, cache_root)

    # Add a file
    cache.update_file("test.py", "blob123", 123.45)

    # Get file info
    info = cache.get_file_info("test.py")
    assert info == FileInfo("test.py", "blob123", 123.45)

    # File doesn't exist
    assert cache.get_file_info("nonexistent.py") is None


def test_cache_persistence(workspace_root, cache_root):
    """Test that cache persists to disk."""
    # Create and save cache
    cache1 = WorkspaceCache(workspace_root, cache_root)
    cache1.update_file("test.py", "blob123", 123.45)
    cache1.save()

    # Load cache in new instance
    cache2 = WorkspaceCache(workspace_root, cache_root)
    info = cache2.get_file_info("test.py")
    assert info == FileInfo("test.py", "blob123", 123.45)


def test_cache_remove_file(workspace_root, cache_root):
    """Test removing files from cache."""
    cache = WorkspaceCache(workspace_root, cache_root)

    # Add and then remove a file
    cache.update_file("test.py", "blob123", 123.45)
    assert cache.get_file_info("test.py") is not None

    cache.remove_file("test.py")
    assert cache.get_file_info("test.py") is None


def test_cache_clear(workspace_root, cache_root):
    """Test clearing the cache."""
    cache = WorkspaceCache(workspace_root, cache_root)

    # Add some files
    cache.update_file("test1.py", "blob1", 1.0)
    cache.update_file("test2.py", "blob2", 2.0)

    # Clear cache
    cache.clear()

    assert cache.get_file_info("test1.py") is None
    assert cache.get_file_info("test2.py") is None
    assert not cache.cache_file.exists()


def test_cache_corrupted_file(workspace_root, cache_root):
    """Test handling of corrupted cache file."""
    cache = WorkspaceCache(workspace_root, cache_root)

    # Write invalid JSON
    cache.cache_file.parent.mkdir(parents=True, exist_ok=True)
    with open(cache.cache_file, "w") as f:
        f.write("invalid json")

    # Should start fresh with empty cache
    cache = WorkspaceCache(workspace_root, cache_root)
    assert not cache._files


def test_cache_file_update(workspace_root, cache_root):
    """Test updating file info."""
    cache = WorkspaceCache(workspace_root, cache_root)

    # Add initial file
    cache.update_file("test.py", "blob1", 1.0)

    # Update same file
    cache.update_file("test.py", "blob2", 2.0)

    info = cache.get_file_info("test.py")
    assert info.blob_name == "blob2"
    assert info.mtime == 2.0
