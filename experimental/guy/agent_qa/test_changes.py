"""Tests for changes.py."""

from pathlib import Path
from unittest.mock import Mock, patch

import pytest

from experimental.guy.agent_qa.changes import (
    Recent<PERSON><PERSON>esM<PERSON>,
    RecentC<PERSON>esProvider,
    RecentChangesTool,
    WorkspaceManager,
    edit_events_to_patch,
    patch_to_edit_events,
)
from services.api_proxy.public_api_pb2 import FileEdit, FileEditEvent


@pytest.fixture
def mock_workspace_manager():
    mock = Mock(spec=WorkspaceManager)
    mock.root = Path("/mock/workspace")

    return mock


@pytest.fixture
def recent_changes_provider(mock_workspace_manager):
    return RecentChangesProvider(mock_workspace_manager)


def test_truncate_line():
    provider = RecentChangesProvider(Mock(), max_line_length=10)
    assert provider._truncate_line("Short") == "Short"
    assert (
        provider._truncate_line("This is a long line")
        == "This is a ... [line truncated]"
    )


def test_truncate_output():
    provider = RecentChangesProvider(Mock(), max_output_lines=3)
    short_output = "Line 1\nLine 2"
    assert provider._truncate_output(short_output) == short_output

    long_output = "Line 1\nLine 2\nLine 3\nLine 4\nLine 5"
    expected_output = "Line 1\nLine 2\nLine 3\n... [output truncated, 2 lines omitted]"
    assert provider._truncate_output(long_output) == expected_output


@patch("experimental.guy.agent_qa.changes.subprocess.run")
def test_get_recent_changes_branch_point(mock_run, recent_changes_provider):
    mock_run.side_effect = [
        Mock(stdout="main", returncode=0),  # _get_current_branch
        Mock(stdout="abc123", returncode=0),  # merge-base
        Mock(stdout="diff output", returncode=0),  # git diff
        Mock(stdout="", returncode=0),  # untracked files
    ]

    result = recent_changes_provider.get_recent_changes(
        RecentChangesMode.SINCE_BRANCH_POINT
    )
    assert "Current branch name: main" in result
    assert "Changes since branching point:" in result
    assert "diff output" in result


@patch("experimental.guy.agent_qa.changes.subprocess.run")
def test_get_recent_changes_from_last_commit(mock_run, recent_changes_provider):
    mock_run.side_effect = [
        Mock(stdout="diff output", returncode=0),  # git diff HEAD
        Mock(stdout="", returncode=0),  # untracked files
    ]

    result = recent_changes_provider.get_recent_changes(
        RecentChangesMode.SINCE_LAST_COMMIT
    )
    assert "diff output" in result


@patch("experimental.guy.agent_qa.changes.subprocess.run")
def test_get_recent_changes_staged(mock_run, recent_changes_provider):
    mock_run.return_value = Mock(stdout="staged changes", returncode=0)

    result = recent_changes_provider.get_recent_changes(
        RecentChangesMode.STAGED_CHANGES
    )
    assert "staged changes" in result


@patch("experimental.guy.agent_qa.changes.subprocess.run")
def test_get_recent_changes_unstaged(mock_run, recent_changes_provider):
    mock_run.side_effect = [
        Mock(stdout="unstaged changes", returncode=0),  # git diff
        Mock(stdout="", returncode=0),  # untracked files
    ]

    result = recent_changes_provider.get_recent_changes(
        RecentChangesMode.UNSTAGED_CHANGES
    )
    assert "unstaged changes" in result


@patch("experimental.guy.agent_qa.changes.subprocess.run")
def test_get_recent_changes_with_untracked_files(mock_run, recent_changes_provider):
    mock_run.side_effect = [
        Mock(stdout="diff output", returncode=0),  # git diff HEAD
        Mock(stdout="untracked.py", returncode=0),  # untracked files
    ]

    with patch("pathlib.Path.read_text", return_value="untracked content"):
        result = recent_changes_provider.get_recent_changes(
            RecentChangesMode.SINCE_LAST_COMMIT
        )

    assert "diff output" in result
    assert "diff --git a/untracked.py b/untracked.py" in result
    assert "untracked content" in result


def test_get_recent_changes_invalid_mode(recent_changes_provider):
    result = recent_changes_provider.get_recent_changes("INVALID_MODE")
    assert "Invalid mode" in result


@patch("experimental.guy.agent_qa.changes.subprocess.run")
def test_get_recent_changes_git_error(mock_run, recent_changes_provider):
    mock_run.return_value = Mock(stdout="", stderr="Git error occurred", returncode=1)

    with patch.object(
        recent_changes_provider, "_run_git_command"
    ) as mock_run_git_command:
        mock_run_git_command.return_value = "Git error: Git error occurred"

        result = recent_changes_provider.get_recent_changes(
            RecentChangesMode.STAGED_CHANGES
        )

        assert "Git error" in result
        assert mock_run_git_command.called
        assert mock_run_git_command.call_args[0][0] == ["git", "diff", "--cached"]


def test_truncation_in_get_recent_changes():
    provider = RecentChangesProvider(Mock(), max_line_length=10, max_output_lines=2)

    with patch.object(provider, "_run_git_command") as mock_run:
        mock_run.return_value = (
            "Line 1\nLine 2\nLine 3\nThis is a very long line that should be truncated"
        )

        result = provider.get_recent_changes(RecentChangesMode.STAGED_CHANGES)

        assert "Line 1" in result
        assert "Line 2" in result
        assert "Line 3" not in result
        assert "should be truncated" not in result


def test_patch_to_edit_events_basic():
    """Test basic patch to FileEditEvent conversion."""
    patch = """--- a.txt
+++ a.txt
@@ -1,3 +1,4 @@
 hello
-world
+new world
+extra line
 end
"""
    events = patch_to_edit_events(patch)
    assert len(events) == 1
    event = events[0]
    assert event.path == "a.txt"
    assert len(event.edits) == 1
    edit = event.edits[0]
    assert edit.before_start == 0
    assert edit.before_text == "hello\nworld\nend\n"
    assert edit.after_start == 0
    assert edit.after_text == "hello\nnew world\nextra line\nend\n"


def test_patch_to_edit_events_multiple_files():
    """Test patch with multiple files."""
    patch = """--- a.txt
+++ a.txt
@@ -1 +1 @@
-old
+new
--- b.txt
+++ b.txt
@@ -1 +1 @@
-foo
+bar
"""
    events = patch_to_edit_events(patch)
    assert len(events) == 2
    assert events[0].path == "a.txt"
    assert events[1].path == "b.txt"
    assert events[0].edits[0].before_text == "old\n"
    assert events[0].edits[0].after_text == "new\n"
    assert events[1].edits[0].before_text == "foo\n"
    assert events[1].edits[0].after_text == "bar\n"


def test_patch_to_edit_events_empty():
    """Test empty patch."""
    assert patch_to_edit_events("") == []


def test_patch_to_edit_events_file_addition():
    """Test patch that adds a new file."""
    patch = """--- /dev/null
+++ new.txt
@@ -0,0 +1 @@
+content
"""
    events = patch_to_edit_events(patch)
    assert len(events) == 1
    event = events[0]
    assert event.path == "new.txt"
    assert len(event.edits) == 1
    edit = event.edits[0]
    assert edit.before_start == 0
    assert edit.before_text == ""
    assert edit.after_start == 0
    assert edit.after_text == "content\n"


def test_patch_to_edit_events_file_deletion():
    """Test patch that deletes a file."""
    patch = """--- old.txt
+++ /dev/null
@@ -1 +0,0 @@
-content
"""
    events = patch_to_edit_events(patch)
    assert len(events) == 1
    event = events[0]
    assert event.path == "old.txt"
    assert len(event.edits) == 1
    edit = event.edits[0]
    assert edit.before_start == 0
    assert edit.before_text == "content\n"
    assert edit.after_start == 0
    assert edit.after_text == ""


def test_patch_to_edit_events_invalid():
    """Test invalid patch formats."""
    with pytest.raises(ValueError):
        patch_to_edit_events("--- a.txt\ninvalid")

    with pytest.raises(ValueError):
        patch_to_edit_events("@@ invalid @@ hunk")


def test_edit_events_to_patch_basic():
    """Test basic FileEditEvent to patch conversion."""
    event = FileEditEvent(
        path="a.txt",
        before_blob_name="",
        after_blob_name="",
        edits=[
            FileEdit(
                before_start=0,
                before_text="hello\nworld\n",
                after_start=0,
                after_text="hello\nnew world\n",
            )
        ],
    )
    patch = edit_events_to_patch([event])
    assert "--- a.txt\n" in patch
    assert "+++ a.txt\n" in patch
    assert "-world\n" in patch
    assert "+new world\n" in patch


def test_edit_events_to_patch_empty():
    """Test empty FileEditEvent list."""
    assert edit_events_to_patch([]) == ""


def test_edit_events_to_patch_multiple():
    """Test multiple FileEditEvents."""
    events = [
        FileEditEvent(
            path="a.txt",
            before_blob_name="",
            after_blob_name="",
            edits=[
                FileEdit(
                    before_start=0,
                    before_text="old\n",
                    after_start=0,
                    after_text="new\n",
                )
            ],
        ),
        FileEditEvent(
            path="b.txt",
            before_blob_name="",
            after_blob_name="",
            edits=[
                FileEdit(
                    before_start=0,
                    before_text="foo\n",
                    after_start=0,
                    after_text="bar\n",
                )
            ],
        ),
    ]
    patch = edit_events_to_patch(events)
    assert "--- a.txt\n" in patch
    assert "+++ a.txt\n" in patch
    assert "-old\n" in patch
    assert "+new\n" in patch
    assert "--- b.txt\n" in patch
    assert "+++ b.txt\n" in patch
    assert "-foo\n" in patch
    assert "+bar\n" in patch


def test_blob_names():
    """Test that blob names are computed correctly."""
    patch = """--- a.txt
+++ a.txt
@@ -1,3 +1,4 @@
 hello
-world
+new world
+extra line
 end
"""
    events = patch_to_edit_events(patch)
    assert len(events) == 1
    event = events[0]

    # Compute expected blob names
    from base.blob_names.python.blob_names import get_blob_name

    expected_before = get_blob_name("a.txt", b"hello\nworld\nend\n")
    expected_after = get_blob_name("a.txt", b"hello\nnew world\nextra line\nend\n")

    assert event.before_blob_name == expected_before
    assert event.after_blob_name == expected_after

    # Test file addition/deletion blob names
    patch = """--- /dev/null
+++ new.txt
@@ -0,0 +1 @@
+content
"""
    events = patch_to_edit_events(patch)
    assert len(events) == 1
    event = events[0]
    assert event.before_blob_name == ""  # No blob name for /dev/null
    assert event.after_blob_name == get_blob_name("new.txt", b"content\n")

    patch = """--- old.txt
+++ /dev/null
@@ -1 +0,0 @@
-content
"""
    events = patch_to_edit_events(patch)
    assert len(events) == 1
    event = events[0]
    assert event.before_blob_name == get_blob_name("old.txt", b"content\n")
    assert event.after_blob_name == ""  # No blob name for /dev/null


def test_roundtrip():
    """Test converting patch to events and back."""
    original_patch = """--- a.txt
+++ a.txt
@@ -1,3 +1,4 @@
 hello
-world
+new world
+extra line
 end
"""
    events = patch_to_edit_events(original_patch)
    new_patch = edit_events_to_patch(events)

    # Convert both to events to compare (since whitespace might differ)
    original_events = patch_to_edit_events(original_patch)
    new_events = patch_to_edit_events(new_patch)

    assert len(original_events) == len(new_events)
    for orig_event, new_event in zip(original_events, new_events):
        assert orig_event.path == new_event.path
        assert len(orig_event.edits) == len(new_event.edits)
        for orig_edit, new_edit in zip(orig_event.edits, new_event.edits):
            assert orig_edit.before_start == new_edit.before_start
            assert orig_edit.before_text == new_edit.before_text
            assert orig_edit.after_start == new_edit.after_start
            assert orig_edit.after_text == new_edit.after_text


@pytest.fixture
def mock_tool_call_logger():
    return Mock()


@pytest.fixture
def recent_changes_tool(mock_tool_call_logger, recent_changes_provider):
    return RecentChangesTool(mock_tool_call_logger, recent_changes_provider)


def test_recent_changes_tool_basic(recent_changes_tool):
    """Test basic functionality of RecentChangesTool."""
    with patch.object(
        recent_changes_tool.recent_changes, "get_recent_changes"
    ) as mock_get_changes:
        mock_get_changes.return_value = "test changes"

        # Test each mode
        for mode in RecentChangesMode:
            result = recent_changes_tool.run_impl({"mode": mode.value})
            assert result.tool_output == "test changes"
            assert result.tool_result_message == "test changes"
            mock_get_changes.assert_called_with(mode, False)


def test_recent_changes_tool_name_only(recent_changes_tool):
    """Test name_only parameter of RecentChangesTool."""
    with patch.object(
        recent_changes_tool.recent_changes, "get_recent_changes"
    ) as mock_get_changes:
        mock_get_changes.return_value = "test files"

        result = recent_changes_tool.run_impl(
            {"mode": RecentChangesMode.SINCE_BRANCH_POINT.value, "name_only": True}
        )
        assert result.tool_output == "test files"
        mock_get_changes.assert_called_with(RecentChangesMode.SINCE_BRANCH_POINT, True)


def test_recent_changes_tool_invalid_mode(recent_changes_tool):
    """Test error handling for invalid mode."""
    result = recent_changes_tool.run_impl({"mode": "invalid_mode"})
    assert "Invalid mode" in result.tool_output
    assert "Invalid mode" in result.tool_result_message


def test_recent_changes_tool_start_message(recent_changes_tool):
    """Test the tool's start message."""
    message = recent_changes_tool.get_tool_start_message(
        {"mode": RecentChangesMode.STAGED_CHANGES.value}
    )
    assert "Getting staged changes" in message
    assert RecentChangesMode.STAGED_CHANGES.value in message
