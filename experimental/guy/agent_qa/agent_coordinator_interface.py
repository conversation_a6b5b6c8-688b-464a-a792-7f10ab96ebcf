"""Interface for coordinating communication between agents."""

from dataclasses import dataclass
from datetime import datetime
from typing import Dict, List


@dataclass
class AgentMessage:
    """A message from an agent about what it's currently doing."""

    agent_name: str
    message: str
    timestamp: datetime


class AgentCoordinator:
    """Interface for coordinating communication between agents.

    This interface provides methods for both sending notifications about an agent's
    current actions and receiving messages from other agents.
    """

    def notify_current_action(self, agent_name: str, message: str) -> None:
        """Let other agents know what this agent is currently doing.

        Args:
            agent_name: The name of the agent sending the message
            message: Description of what the agent is currently doing
        """
        raise NotImplementedError()

    def get_messages(self) -> str:
        """Get all pending messages from other agents as a single string.

        Returns:
            A string containing all pending messages, or empty string if none.
            Messages should be formatted in a consistent way, typically with
            the agent name and message content.
        """
        raise NotImplementedError()


class InMemoryAgentCoordinator(AgentCoordinator):
    """In-memory implementation of AgentCoordinator."""

    def __init__(self):
        # List of all messages, newest first
        self._messages: List[AgentMessage] = []
        self._current_agent_name: str | None = None

    def notify_current_action(self, agent_name: str, message: str) -> None:
        """Implementation of notify_current_action that stores messages in memory."""
        self._current_agent_name = agent_name
        self._messages.insert(
            0,
            AgentMessage(
                agent_name=agent_name, message=message, timestamp=datetime.now()
            ),
        )

    def get_messages(self) -> str:
        """Implementation of get_messages that returns messages from memory."""
        if not self._current_agent_name:
            return ""

        # Filter messages from other agents and format them
        other_messages = [
            f"*{msg.agent_name}*: {msg.message}"
            for msg in reversed(self._messages)
            if msg.agent_name != self._current_agent_name
        ]

        return "\n".join(other_messages) if other_messages else ""
