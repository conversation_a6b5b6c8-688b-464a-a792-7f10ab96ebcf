"""Tool implementations."""

import copy
import difflib
import os
import re
import select
import subprocess
import sys
from textwrap import dedent
import time
from pathlib import Path
from typing import Any, Collection, Iterable, List, Optional, Tuple

import asyncio

from base.augment_client.client import (
    AugmentClient,
    AugmentModelClient,
    ClientException,
)
from experimental.guy.agent_qa.memories import (
    Memories,
    maybe_create_memory_from_instruction,
)
from services.api_proxy import public_api_pb2
from base.prompt_format_retrieve.prompt_formatter import ChatRetrieverPromptInput
from experimental.guy.agent_qa.augment_llm_client import llm_messages_to_chat_dialog
from experimental.guy.agent_qa.command_approval import CommandApprovalManager
from experimental.guy.agent_qa.integration_warnings import IntegrationWarnings
from experimental.guy.agent_qa.prototyping_client import (
    AugmentPrototypingClient,
    RetrievedChunk,
)
from experimental.guy.agent_qa.string_utils import truncate_string
from experimental.guy.agent_qa.workspace_manager import (
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    WorkspaceManager,
)
from experimental.guy.agent_qa.file_utils import safe_read_file
from experimental.michiel.research.agentqa.tools import (
    CodebaseRetrievalTool as MichielCodebaseRetrievalTool,
)
from research.agents.tools import (
    DialogMessages,
    LLMTool,
    ToolCallLogger,
    ToolCallParameters,
    ToolImplOutput,
    call_tools,
)
from research.llm_apis.llm_client import LLMClient, ToolCall
from research.retrieval.types import (
    Chunk,
    Document,
    DocumentId,
    DocumentIndex,
    RetrievalScore,
)


class OutputCollector:
    """Helper class to collect and truncate command output."""

    def __init__(self, char_budget: int):
        self.char_budget = char_budget
        self.lines: List[str] = []

    def add_line(self, line: str) -> None:
        """Add a line to the output."""
        self.lines.append(line)

    def get_output(self) -> str:
        """Get the collected output, truncated if necessary."""
        # Join all lines
        output = "".join(self.lines)

        # If output is under budget, return as is
        if len(output) <= self.char_budget:
            return output

        # Otherwise truncate, keeping both start and end
        return truncate_string(output, self.char_budget)

    @property
    def total_chars(self) -> int:
        """Get the total number of characters in the output."""
        return len(self.get_output())


# ExecuteCommandTool class has been removed


class ReadFileTool(LLMTool):
    name = "read_file"
    """Reads a file."""

    description = "Read a file."
    input_schema = {
        "type": "object",
        "properties": {
            "file_path": {
                "type": "string",
                "description": "The path to the file to read.",
            },
        },
        "required": ["file_path"],
    }

    def __init__(
        self, tool_call_logger: ToolCallLogger, root: Path, char_budget: int = 100_000
    ):
        super().__init__(tool_call_logger)
        self.root = root
        self.char_budget = char_budget

    def run_impl(
        self,
        tool_input: dict[str, Any],
        dialog_messages: Optional[DialogMessages] = None,
    ) -> ToolImplOutput:
        file_path = tool_input["file_path"]
        # Always expand user paths
        path = Path(file_path).expanduser()
        if not path.is_absolute():
            path = self.root / path

        if not path.exists():
            return ToolImplOutput("File does not exist", f"File {path} does not exist")

        content = safe_read_file(path)
        if content is None:
            return ToolImplOutput("File is not text.", f"File {path} is not text")

        if len(content) > self.char_budget:
            content = truncate_string(content, self.char_budget)
            return ToolImplOutput(
                content, f"Read file {path} (truncated to ~{self.char_budget} chars)"
            )

        return ToolImplOutput(content, f"Read file {path}")

    def get_tool_start_message(self, tool_input: dict[str, Any]) -> str:
        return f"Reading file {tool_input['file_path']}"


class SaveFileTool(LLMTool):
    name = "save_file"
    """A tool that creates a file."""

    # The overwrite option was removed because the model uses it in unsafe ways,
    # to overwrite existing files without reading them first.
    description = "Save a file. Use this tool to create new files, or to make substantial changes to existing files."
    input_schema = {
        "type": "object",
        "properties": {
            "file_path": {
                "type": "string",
                "description": "The path to the file to create.",
            },
            "file_content": {
                "type": "string",
                "description": "The content of the file to create.",
            },
            "add_last_line_newline": {
                "type": "boolean",
                "description": "Whether to add a newline at the end of the file (default: true).",
            },
        },
        "required": ["file_path", "file_content"],
    }

    def __init__(
        self, tool_call_logger: ToolCallLogger, workspace_manager: WorkspaceManager
    ):
        super().__init__(tool_call_logger)
        self.workspace_manager = workspace_manager

    def run_impl(
        self,
        tool_input: dict[str, Any],
        dialog_messages: Optional[DialogMessages] = None,
    ) -> ToolImplOutput:
        path = self.workspace_manager.root / tool_input["file_path"]

        if path.exists():
            return ToolImplOutput(
                "File already exists, will not overwrite it.",
                f"File {path} already exists, will not save",
            )

        try:
            path.parent.mkdir(parents=True, exist_ok=True)
            content = tool_input["file_content"]
            add_last_line_newline = tool_input.get("add_last_line_newline", True)
            if add_last_line_newline and not content.endswith("\n"):
                content += "\n"
            path.write_text(content)
            self.workspace_manager.update()
            return ToolImplOutput("File saved.", f"Saved file {path}")
        except Exception as exc:
            return ToolImplOutput(f"Failed to save file: {exc}", "Failed to save file.")

    def get_tool_start_message(self, tool_input: dict[str, Any]) -> str:
        return f"Saving file to {tool_input['file_path']}"


class FileEditClient:
    """A client for the Forger smart paste model."""

    # MODEL = "forger-smart-paste-v2-qwen-8b-32k-edit"
    # MODEL = "forger-smart-paste-v2-qwen-14b-32k-edit"
    # MODEL = "forger-v2-fs-qwen-14b-q-32k-edit"
    MODEL = "forger-v2-qwen-14b-q-32k-edit"

    def __init__(
        self,
        augment_public_api_client: AugmentClient,
        timeout: int = 60,
        model: str = MODEL,
    ):
        self.augment_public_api_client = augment_public_api_client
        self.clients_by_model: dict[str, AugmentModelClient] = {}
        self.clients_by_model[model] = AugmentModelClient(
            self.augment_public_api_client, model
        )
        self.timeout = timeout
        self.model = model

    def edit_file(
        self,
        target_file_path: str,
        target_file_content: str,
        edit_plan: str,
        code_block: str,
        detailed_edit_description: Optional[str] = None,
        model: str | None = None,
    ) -> Tuple[str, str]:  # Return both content and request ID
        """
        Edits a target file according to the edit plan and generated code block.

        Args:
            target_file_path: The path to the file to edit.
            target_file_content: The content of the file to edit.
            edit_plan: A language description of the edit to make.
            code_block: The new code. May contain placeholders like '... existing methods here ...'.

        Returns:
            A tuple of (edited_content, request_id)
        """
        max_retries = 3
        retry_delays = [1, 5, 20]  # in seconds

        for retry_attempt in range(max_retries):
            try:
                output = self.edit_file_unsafe(
                    target_file_path=target_file_path,
                    target_file_content=target_file_content,
                    edit_plan=edit_plan,
                    code_block=code_block,
                    detailed_edit_description=detailed_edit_description,
                    model=model or self.model,
                )
                return output
            except Exception as e:
                if retry_attempt == max_retries - 1:
                    raise e
                time.sleep(retry_delays[retry_attempt])

        assert False, "Never should get here."

    def edit_file_unsafe(
        self,
        target_file_path: str,
        target_file_content: str,
        edit_plan: str,
        code_block: str,
        detailed_edit_description: Optional[str] = None,
        model: str = MODEL,
    ) -> Tuple[str, str]:  # Return both content and request ID
        request_id = None
        replacement_start_line, replacement_end_line = None, None
        response = []

        # The Forger model expects 4 fields:
        #   chat_history.request_message : the instruction / edit description
        #   code_block : the generated code (with the placeholders etc.)
        #   target_file_path : the path of the file to edit
        #   target_file_content : the full content of that file
        #
        # It optionally accepts selected_text, prefix, and suffix.
        # It ignores additional chat history.

        chat_history = [
            {
                "request_message": edit_plan,
                "response_text": detailed_edit_description or "",
            }
        ]

        for response_chunks in self.clients_by_model[model].smart_paste_stream(
            selected_text="",
            prefix="",
            suffix="",
            path="",
            chat_history=chat_history,  # type: ignore
            code_block=code_block,
            target_file_path=target_file_path,
            target_file_content=target_file_content,
            timeout=self.timeout,
        ):
            if response_chunks.request_id is not None:
                request_id = response_chunks.request_id
            if response_chunks.replacement_start_line is not None:
                # API returns 1-based line numbers
                replacement_start_line = response_chunks.replacement_start_line - 1
            if response_chunks.replacement_end_line is not None:
                # API returns 1-based line numbers
                replacement_end_line = response_chunks.replacement_end_line - 1
            if response_chunks.replacement_text is not None:
                response.append(response_chunks.replacement_text)

        target_file_lines = target_file_content.splitlines(True)
        target_file_lines[replacement_start_line:replacement_end_line] = response
        return "".join(target_file_lines), "" if request_id is None else request_id


class EditFileTool(LLMTool):
    name = "edit_file"
    """A tool that edits a file.

    Uses Forger, our smart paste model.
    """

    description = "Edit a files. Accepts a file path, a natural language description of the edit plan, and the new code that should replace the edited code."
    input_schema = {
        "type": "object",
        "properties": {
            "file_path": {
                "type": "string",
                "description": "The path to the file to edit.",
            },
            "edit_plan": {
                "type": "string",
                "description": "A language description of the edit to make.",
            },
            "new_code": {
                "type": "string",
                "description": """\
The new code. Placeholders are allowed with comments, for example:
... existing methods here ...
... (rest of the method omitted)

The tool knows how to correctly apply such changes.
""",
            },
        },
        "required": ["file_path", "edit_plan", "new_code"],
    }

    def __init__(
        self,
        tool_call_logger: ToolCallLogger,
        workspace_manager: WorkspaceManager,
        file_edit_client: FileEditClient,
    ):
        super().__init__(tool_call_logger)
        self.workspace_manager = workspace_manager
        self.file_edit_client = file_edit_client

    def run_impl(
        self,
        tool_input: dict[str, Any],
        dialog_messages: Optional[DialogMessages] = None,
    ) -> ToolImplOutput:
        path = Path(tool_input["file_path"])
        abs_path = self.workspace_manager.root / path

        if not abs_path.exists():
            tool_output = f"File {path} does not exist, cannot edit"
            return ToolImplOutput(tool_output, tool_output, {"success": False})

        target_file_content = abs_path.read_text()

        try:
            new_content, request_id = self.file_edit_client.edit_file(
                target_file_path=tool_input["file_path"],
                target_file_content=target_file_content,
                edit_plan=tool_input["edit_plan"],
                code_block=tool_input["new_code"],
            )
        except Exception as exc:
            return ToolImplOutput(
                f"Failed to edit file: {exc}",
                "Failed to edit file.",
                {"success": False},
            )

        abs_path.write_text(new_content)
        self.workspace_manager.update()

        tool_output = f"File {path} edited, request_id={request_id}"

        return ToolImplOutput(tool_output, tool_output, {"success": True})

    def get_tool_start_message(self, tool_input: dict[str, Any]) -> str:
        return f"Editing file {tool_input['file_path']}"


class EditFileToolV2(LLMTool):
    name = "edit_file_v2"
    """A tool that edits a file.

    Uses Forger, our smart paste model.
    """

    description = "Edit a file. Accepts a file path, a description of where/what to edit, and the specific code changes to make."
    input_schema = {
        "type": "object",
        "properties": {
            "file_path": {
                "type": "string",
                "description": "The path to the file to edit.",
            },
            "edit_plan": {
                "type": "string",
                "description": "Description of where in the file to make changes and what needs to be modified.",
            },
            "suggested_edit": {
                "type": "string",
                "description": """\
The suggested changes to the file including any new code that needs to be inserted as well as parts of the surrounding old code to maximize readability. Be concise - include only the necessary context. Placeholders are allowed with comments, for example:
# ... existing methods here ...
// ... (rest of the method omitted) ...

If you remove parts of the old code, highlight that with additional comments:
# ... method foobar() has been removed ...

This suggested change must be easy to read for a human.""",
            },
        },
        "required": ["file_path", "edit_plan", "suggested_edit"],
    }

    def __init__(
        self,
        tool_call_logger: ToolCallLogger,
        workspace_manager: WorkspaceManager,
        file_edit_client: FileEditClient,
    ):
        super().__init__(tool_call_logger)
        self.workspace_manager = workspace_manager
        self.file_edit_client = file_edit_client

    def run_impl(
        self,
        tool_input: dict[str, Any],
        dialog_messages: Optional[DialogMessages] = None,
    ) -> ToolImplOutput:
        path = Path(tool_input["file_path"])
        abs_path = self.workspace_manager.root / path

        if not abs_path.exists():
            tool_output = f"File {path} does not exist, cannot edit"
            return ToolImplOutput(tool_output, tool_output, {"success": False})

        target_file_content = abs_path.read_text()

        try:
            new_content, request_id = self.file_edit_client.edit_file(
                target_file_path=tool_input["file_path"],
                target_file_content=target_file_content,
                edit_plan=tool_input["edit_plan"],
                code_block=tool_input["suggested_edit"],
            )
        except Exception as exc:
            return ToolImplOutput(
                f"Failed to edit file: {exc}",
                "Failed to edit file.",
                {"success": False},
            )

        abs_path.write_text(new_content)
        self.workspace_manager.update()

        tool_output = f"File {path} edited, request_id={request_id}"

        return ToolImplOutput(tool_output, tool_output, {"success": True})

    def get_tool_start_message(self, tool_input: dict[str, Any]) -> str:
        return f"Editing file {tool_input['file_path']}"


class EditFileAgentDesc:
    name = "edit_file_agent"
    """An agent that edits a file."""

    description = """\
Edit a file. Accepts a file path and a short description of the edit.

This tool can edit whole file or parts of the file.
"""
    input_schema = {
        "type": "object",
        "properties": {
            "file_path": {
                "type": "string",
                "description": "The path to the file to edit.",
            },
            "short_edit_description": {
                "type": "string",
                "description": "A brief description of the edit to be made. 1-2 sentences.",
            },
        },
        "required": ["file_path", "short_edit_description"],
    }


class EditFileAgent(LLMTool):
    name = EditFileAgentDesc.name
    description = EditFileAgentDesc.description
    input_schema = EditFileAgentDesc.input_schema

    def __init__(
        self,
        client: LLMClient,
        tool_call_logger: ToolCallLogger,
        workspace_manager: WorkspaceManager,
        file_edit_client: FileEditClient,
        max_output_tokens_per_turn: int,
        max_turns: int,
        review_stage: bool = False,
        edit_tool_name: str = "forger_v1",
    ):
        super().__init__(tool_call_logger)
        self.client = client
        self.workspace_manager = workspace_manager
        self.file_edit_client = file_edit_client
        self.max_output_tokens = max_output_tokens_per_turn
        self.max_turns = max_turns
        self.review_stage = review_stage
        self.edit_tool_name = edit_tool_name

    def run_impl(
        self,
        tool_input: dict[str, Any],
        dialog_messages: Optional[DialogMessages] = None,
    ) -> ToolImplOutput:
        assert dialog_messages is not None

        if self.edit_tool_name == "forger_v1":
            edit_tool = EditFileTool(
                self.tool_call_logger, self.workspace_manager, self.file_edit_client
            )
        elif self.edit_tool_name == "forger_v2":
            edit_tool = EditFileToolV2(
                self.tool_call_logger, self.workspace_manager, self.file_edit_client
            )
        else:
            raise ValueError(f"Unknown edit tool name: {self.edit_tool_name}")

        complete_tool = CompleteTool(self.tool_call_logger)
        tools = [edit_tool, complete_tool]
        tool_params = [tool.get_tool_param() for tool in tools]

        path = Path(tool_input["file_path"])
        abs_path = self.workspace_manager.root / path

        if not abs_path.exists():
            abs_path.parent.mkdir(parents=True, exist_ok=True)
            abs_path.touch()

        target_file_content = abs_path.read_text()

        edit_instruction = f"""\
Edit the file {path} according to the following instructions:

```
{tool_input["short_edit_description"]}
```

Call the supplied tool to perform the edit. Make sure you specify both a description
and a precise listing of the new code. The new code should include every piece of
code that needs to be changed, even if it spans multiple locations in the file.
You can use placeholder comments to separate the different sections.

Here is the full contents of the file. The remainder of this message contains the contents.

{target_file_content}
"""

        review_instruction_template = """\
Please review the changes made. Make sure that:
- The instructions were followed correctly
- No important related changes were missed
- No changes were made that weren't necessary from the instructions

Here is the diff showing the changes made:

{diff}

If the changes are satisfactory, call the complete tool to end the task.
If not, decide which edits are needed now, and call the edit tool again."""

        dialog_messages = copy.deepcopy(dialog_messages)

        # This element is a call to EditFileAgent
        tool_call = dialog_messages.get_pending_tool_calls()[-1]

        dialog_messages.add_tool_call_result(
            ToolCallParameters(
                tool_call_id=tool_call.tool_call_id,
                tool_name=tool_call.tool_name,
                tool_input=tool_call.tool_input,
            ),
            f"""
You have to perform this task using `edit_file` tool.

To recap:
```
{edit_instruction}
```
""",
        )

        # Initial edit stage
        response, metadata = self.client.generate(
            messages=dialog_messages.get_messages_for_llm_client(),
            max_tokens=self.max_output_tokens,
            tools=tool_params,
            tool_choice={"type": "tool", "name": edit_tool.name},
        )
        dialog_messages.add_model_response(response)
        # assert len(dialog_messages.get_pending_tool_calls()) == 1

        remaining_turns = self.max_turns
        while remaining_turns > 0:
            remaining_turns -= 1

            call_tools(
                tools=tools,
                calls_to_make=dialog_messages.get_pending_tool_calls(),
                dialog_messages=dialog_messages,
            )

            if complete_tool.should_stop:
                return ToolImplOutput(complete_tool.answer, f"File {path} edited")

            # Review stage
            if self.review_stage:
                edited_file_content = abs_path.read_text()
                diff = "\n".join(
                    difflib.unified_diff(
                        target_file_content.splitlines(),
                        edited_file_content.splitlines(),
                        fromfile=str(path),
                        tofile=str(path),
                        lineterm="",
                    )
                )
                review_instruction = review_instruction_template.format(diff=diff)
                dialog_messages.add_user_prompt(
                    review_instruction, allow_append_to_tool_call_results=True
                )

                response, metadata = self.client.generate(
                    messages=dialog_messages.get_messages_for_llm_client(),
                    max_tokens=self.max_output_tokens,
                    tools=tool_params,
                )
                dialog_messages.add_model_response(response)
            else:
                return ToolImplOutput(
                    f"File {path} edited. Edit successfully verified.",
                    f"File {path} edited. Edit successfully verified.",
                )

        agent_answer = "Edit agent did not complete after max turns"
        return ToolImplOutput(agent_answer, agent_answer)

    def get_tool_start_message(self, tool_input: dict[str, Any]) -> str:
        return f"Editing file {tool_input['file_path']}"


class BackendEditFileAgent(LLMTool):
    """The backend file agent dispatches a high level edit request to the backend
    where an agent attempts to modify the file contents to satisfy the request
    over multiple turns.

    If that request is successful, the tool saves the new contents to disk and
    updates the workspace manager.
    """

    name = EditFileAgentDesc.name
    description = EditFileAgentDesc.description
    input_schema = EditFileAgentDesc.input_schema

    def __init__(
        self,
        client: AugmentClient,
        tool_call_logger: ToolCallLogger,
        workspace_manager: WorkspaceManager,
    ):
        super().__init__(tool_call_logger)
        self.client = client
        self.workspace_manager = workspace_manager

    def run_impl(
        self,
        tool_input: dict[str, Any],
        dialog_messages: Optional[DialogMessages] = None,
    ) -> ToolImplOutput:
        path = Path(tool_input["file_path"])
        abs_path = self.workspace_manager.root / path

        if not abs_path.exists():
            abs_path.parent.mkdir(parents=True, exist_ok=True)
            abs_path.touch()

        request_message = public_api_pb2.EditFileRequest(
            file_path=tool_input["file_path"],
            edit_summary=tool_input["edit_summary"],
            detailed_edit_description=tool_input["detailed_edit_description"],
            file_contents=abs_path.read_text(),
        )
        try:
            response, request_id = self.client.post_proto(
                "/agents/edit-file",
                request_message,
                public_api_pb2.EditFileResponse(),
            )
        except Exception as exc:
            aux = {}
            if isinstance(exc, ClientException):
                aux["request_id"] = exc.request_id
            return ToolImplOutput(
                f"Failed to edit file: {exc}", "Failed to edit file.", aux
            )

        if response.is_error:
            # Service doesn't actually explain why
            tool_output = "Failed to edit file after max turns"
        else:
            abs_path.write_text(response.modified_file_contents)
            self.workspace_manager.update()
            tool_output = f"File {path} edited, request_id={request_id}"
        return ToolImplOutput(tool_output, tool_output, {"request_id": request_id})


class QueryOnlyDocumentIndex(DocumentIndex[ChatRetrieverPromptInput]):
    """A document index that uses the Augment server.

    Only implements querying.
    """

    def __init__(
        self,
        augment_client: AugmentPrototypingClient,
        workspace_manager: WorkspaceManager,
        max_retrieval_chunks: int,
    ):
        self.augment_client = augment_client
        self.workspace_manager = workspace_manager
        self.max_retrieved_chunks = max_retrieval_chunks

    def query(
        self,
        model_input: ChatRetrieverPromptInput,
        query_meta: Optional[dict] = None,
        doc_ids: Optional[Collection[DocumentId]] = None,
        top_k: Optional[int] = None,
    ) -> tuple[list[Chunk], list[RetrievalScore]]:
        """Return list of document chunks for the given query.

        Args:
            prefix: Unsupported
            suffix: Unsupported
            query_meta: Unsupported
            doc_ids: Unsupported
            top_k: optional limit to the number of retrieved documents. Default
                limit specified during construction.

        Returns:
            A list of chunks, and a list of corresponding retrieval scores.
            The chunks are ordered from high to low score.
        """
        if query_meta:
            raise NotImplementedError("query_meta is not supported")
        if doc_ids:
            raise NotImplementedError("doc_ids is not supported")

        if not top_k or top_k > self.max_retrieved_chunks:
            top_k = self.max_retrieved_chunks

        blob_names = self.workspace_manager.get_blob_names()
        retrieved_chunks: list[RetrievedChunk] = self.augment_client.chat_retrieval(
            query=model_input.message,
            blob_names=blob_names,
            max_chunks=top_k,
        )
        chunks = [
            Chunk(
                id=i,
                text=chunk.text,
                parent_doc=Document(
                    id=chunk.blob_name,
                    path=chunk.path,
                    text="",
                ),
                char_offset=chunk.char_start,
                length=len(chunk.text),
                line_offset=-1,
                length_in_lines=chunk.text.count("\n"),
            )
            for i, chunk in enumerate(retrieved_chunks)
        ]
        scores = [float(-i) for i in range(len(chunks))]
        return chunks, scores

    def add_docs(self, docs: Iterable[Document]) -> None:
        raise NotImplementedError()

    def remove_docs(self, ids: Iterable[DocumentId]) -> list[Document]:
        raise NotImplementedError()

    def remove_all_docs(self) -> list[Document]:
        raise NotImplementedError()

    def get_doc_ids(self):
        raise NotImplementedError()

    def get_docs(self, ids: Iterable[DocumentId]) -> list[Document]:
        raise NotImplementedError()

    def is_in(self, ids: Iterable[DocumentId]) -> list[bool]:
        raise NotImplementedError()

    @classmethod
    def from_yaml_config(cls, config: dict):
        raise NotImplementedError()

    # class CodebaseRetrievalAgent(LLMTool):
    #     """An agent that performs codebase retrieval.

    #     The agent uses a retriever to retrieve chunks, and then uses chat
    #     to filter those down to the most important ones.
    #     """

    #     name = "retrieve_from_codebase"
    #     description = MichielCodebaseRetrievalTool.tool_description

    #     def __init__(
    #         self,
    #         tool_call_logger: ToolCallLogger,
    #         llm_client: LLMClient,
    #         augment_client: AugmentPrototypingClient,
    #         workspace_manager: WorkspaceManager,
    #         token_budget: int = 100_000,
    #     ):
    #         super().__init__(tool_call_logger)
    #         self.llm_client = llm_client
    #         self.augment_client = augment_client
    #         self.workspace_manager = workspace_manager
    #         self.token_budget = token_budget

    #         # fill the budget with 30-line chunks, 10 tokens per chunk
    #         max_result_chunks = token_budget // (30 * 10)

    #         # roughly 3.5 tokens per char
    #         max_tool_chars = int(token_budget * 3.5)

    #         self.codebase_retrieval_tool = CodebaseRetrievalTool(
    #             tool_call_logger,
    #             augment_client,
    #             workspace_manager,
    #             max_tool_chars=max_tool_chars,
    #             max_retrieval_chunks=max_result_chunks * 10,
    #             max_result_chunks=max_result_chunks,
    #         )

    #         self.input_schema = self.codebase_retrieval_tool.input_schema

    #     def run_impl(
    #         self,
    #         tool_input: dict[str, Any],
    #         dialog_messages: Optional[DialogMessages] = None,
    #     ) -> ToolImplOutput:
    #         retrieval_result = self.codebase_retrieval_tool.run_impl(
    #             tool_input, dialog_messages
    #         )

    #         system_prompt = """\
    # You are an agent operating in a user's codebase. Your task is to look at a retrieval
    # request and retrieval results, and decide which results are most relevant for
    # answering the request.
    # """

    #         filter_request = f"""\
    # Here are the results of a codebase retrieval request, including filenames from
    # the codebase, and code snippets from those files. Some of these results are more
    # relevant to the questions, and some are less relevant.

    # Which files are most relevant? Output them from most to least relevant.
    # Use the following format:

    # <retrieved-paths-from-most-to-least-relevant>
    # /first/path
    # /second/path
    # ...
    # </retrieved-paths-from-most-to-least-relevant>

    # <request-schema>
    # {json.dumps(self.input_schema, indent=2)}
    # </request-schema>

    # <retrieval-request>
    # {json.dumps(tool_input, indent=2)}
    # </retrieval-request>

    # <retrieval-result>
    # {retrieval_result}
    # </retrieval-result>
    # """

    #         answer = self.llm_client.generate(
    #             messages=[filter_request], max_tokens=8192, system_prompt=system_prompt
    #         )

    def get_tool_start_message(self, tool_input: dict[str, Any]) -> str:
        requests = [
            request["description"] for request in tool_input["code_section_requests"]
        ]
        return f"Retrieving codebase information: {', '.join(requests)}"


class CodebaseRetrievalTool(LLMTool):
    name = "ask_for_codebase_snippets"
    """A tool that retrieves information from the codebase."""

    description = (
        MichielCodebaseRetrievalTool.tool_description
        + """\

NOTE: The snippets returned by this tool are partial, and do not show the whole file.
If you need to save a whole file, make sure to read the whole file first before changing it.
"""
    )

    def __init__(
        self,
        tool_call_logger: ToolCallLogger,
        augment_client: AugmentPrototypingClient,
        workspace_manager: WorkspaceManager,
        max_tool_chars: int,
        max_retrieval_chunks=1000,
        max_result_chunks=20,
    ):
        super().__init__(tool_call_logger)
        self.augment_client = augment_client
        self.workspace_manager = workspace_manager
        self.max_retrieval_chunks = max_retrieval_chunks
        self.max_result_chunks = max_result_chunks
        self.max_tool_chars = max_tool_chars

        retriever = QueryOnlyDocumentIndex(
            augment_client=self.augment_client,
            workspace_manager=self.workspace_manager,
            max_retrieval_chunks=self.max_retrieval_chunks,
        )

        self.internal_tool = MichielCodebaseRetrievalTool(
            retriever=retriever,
            max_tool_chars=self.max_tool_chars,
            max_num_chunks=self.max_result_chunks,
        )

        self.input_schema = self.internal_tool.get_input_schema()

    def run_impl(
        self,
        tool_input: dict[str, Any],
        dialog_messages: Optional[DialogMessages] = None,
    ) -> ToolImplOutput:
        tool_result, _ = self.internal_tool.activate_tool(
            ToolCall(
                tool_name=self.name,
                tool_input=tool_input,
                tool_call_id="fake-call-id",
            )
        )
        format_tool_result, _ = self.internal_tool.format_tool_result(tool_result)
        return ToolImplOutput(
            format_tool_result.tool_output, "Codebase information retrieved"
        )

    def get_tool_start_message(self, tool_input: dict[str, Any]) -> str:
        # Example outputs:
        # Simple case: "Retrieving codebase information: find main function, get config file"
        # Complex case: "Retrieving codebase information: find main function in **/src/**, get config file containing 'DEBUG_MODE' in **/config/**"
        requests = []
        for request in tool_input["code_section_requests"]:
            parts = []
            parts.append(request["description"])

            if request.get("path"):
                parts.append(f"in {request['path']}")
            if request.get("contains_string"):
                parts.append(f"containing '{request['contains_string']}'")

            requests.append(" ".join(parts))

        return f"Retrieving codebase information: {', '.join(requests)}"


class BackendCodebaseRetrievalTool(LLMTool):
    """A tool that retrieves information from the codebase using an agent backend."""

    name = "request_codebase_information"

    description = dedent(
        """\
        Use this tool to request information from the codebase.
        It will return relevant snippets for the requested information.
        """
    )

    input_schema = {
        "type": "object",
        "properties": {
            "information_request": {
                "type": "string",
                "description": "A description of the information you need.",
            },
        },
        "required": ["information_request"],
    }

    def __init__(
        self,
        client: AugmentClient,
        tool_call_logger: ToolCallLogger,
        workspace_manager: WorkspaceManager,
    ):
        super().__init__(tool_call_logger)
        self.client = client
        self.workspace_manager = workspace_manager

    def run_impl(
        self,
        tool_input: dict[str, Any],
        dialog_messages: Optional[DialogMessages] = None,
    ) -> ToolImplOutput:
        llm_messages = (
            dialog_messages.get_messages_for_llm_client() if dialog_messages else []
        )
        dialog, current_message = llm_messages_to_chat_dialog(llm_messages)
        assert (
            not current_message
        ), "Unexpected user message at end of dialog during tool call"

        request_message = public_api_pb2.CodebaseRetrievalRequest(
            information_request=tool_input["information_request"],
            dialog=dialog,
            blobs=public_api_pb2.Blobs(
                checkpoint_id="",
                added_blobs=self.workspace_manager.get_blob_names(),
                deleted_blobs=[],
            ),
        )
        try:
            response, request_id = self.client.post_proto(
                "/agents/codebase-retrieval",
                request_message,
                public_api_pb2.CodebaseRetrievalResponse(),
            )
        except Exception as exc:
            return ToolImplOutput(
                f"Failed to retrieve codebase information: {exc}",
                "Failed to retrieve codebase information.",
            )
        return ToolImplOutput(
            response.formatted_retrieval,
            "Codebase information retrieved",
            {"request_id": request_id},
        )


class ClarifyTool(LLMTool):
    name = "clarify"
    """A tool that asks the user for clarification."""

    description = """\
Call this tool to ask the user for clarification on the task.

Good examples of when to use this tool:
- If you don't know how to perform some action or if executing a command was declined by the user.
- The request is not clear enough, or there is missing information that is important
  for achieving the task.
- The request requires running commands or code that rely on the developer's environment
  or configuration, but you don't have enough information about the environment.
- The instruction refers to unfamiliar concepts or terms that you do not understand,
  and that are not explained elsewhere in the prmopt.

Important: Before executing a command or code that has side effects, if you don't
see existing knowledge about this command or related task, clarify with the user
if that's the right thing to do.
"""

    input_schema = {
        "type": "object",
        "properties": {
            "clarifying_question": {
                "type": "string",
                "description": "The question to ask the user for clarification.",
            },
        },
        "required": ["clarifying_question"],
    }

    def __init__(
        self, client: LLMClient, memories: Memories, tool_call_logger: ToolCallLogger
    ):
        super().__init__(tool_call_logger)

        self.client = client
        self.memories = memories

    def run_impl(
        self,
        tool_input: dict[str, Any],
        dialog_messages: Optional[DialogMessages] = None,
    ) -> ToolImplOutput:
        clarifying_question = tool_input["clarifying_question"]
        print(f"Model asks clarifying question: {clarifying_question}")
        user_answer = input("Clarification: ")

        assert dialog_messages is not None, "Dialog messages must be provided"
        dialog_messages.add_user_prompt(user_answer)
        try:
            maybe_create_memory_from_instruction(
                user_answer,
                dialog_messages,
                self.client,
                self.memories,
                self.tool_call_logger,
                verbose=False,  # No need for excessive logging here
            )
        except Exception as e:
            print(f"Failed to create memory from instruction: {e}")

        return ToolImplOutput(f"User answered: {user_answer}", "User answered")

    def get_tool_start_message(self, tool_input: dict[str, Any]) -> str:
        return "Asking clarifying question."


class CodebaseKnowledgeTool(LLMTool):
    name = "create_memory"
    """A tool for remembering things things about the codebase."""

    description = """\
Call this tool when you learned something useful about the codebase or the environment,
and want to remember it for the future.

The tool will save the knowledge as a memory, and make it available later.
Each memory is stored as a Markdown file.

Good examples include:
- What steps are needed to setup a new environment
- What do we when encountering a certain error
- How certain components of the system interact with each other
- What is the correct way of achieving certain tasks
- What guidelines should one try to follow when working in the codebase

Bad examples include:
- Knowledge that is only relevant to your current task.

The memories will be provided to you in future tasks. You should decide whether
a memory is likely to be useful in the future. If it is, save it.
If not, do not save it.
"""
    input_schema = {
        "type": "object",
        "properties": {
            "title": {
                "type": "string",
                "description": "A short title for the memory snippet.",
            },
            "content": {
                "type": "string",
                "description": "The knowledge you learned. Be specific, concrete, and detailed. Use Markdown format, with an ## title.",
            },
            "filename": {
                "type": "string",
                "description": "The filename to save this knowledge snippet to. Should be a valid filename ending in .md",
            },
        },
        "required": ["title", "content", "filename"],
    }

    def __init__(
        self,
        tool_call_logger: ToolCallLogger,
        workspace_manager: WorkspaceManager,
        knowledge_path: Path,
    ):
        super().__init__(tool_call_logger)
        self.workspace_manager = workspace_manager
        self.knowledge_dir = knowledge_path

        # Create knowledge directory if it doesn't exist
        self.knowledge_dir.mkdir(exist_ok=True)

    def run_impl(
        self,
        tool_input: dict[str, Any],
        dialog_messages: Optional[DialogMessages] = None,
    ) -> ToolImplOutput:
        filename = tool_input["filename"]
        if not filename.endswith(".md"):
            filename += ".md"

        file_path = self.knowledge_dir / filename
        self.add_knowledge(
            file_path=file_path,
            title=tool_input["title"],
            content=tool_input["content"],
        )
        return ToolImplOutput(
            f"Memory saved to {file_path}",
            f"Memory saved to {file_path}",
        )

    def get_tool_start_message(self, tool_input: dict[str, Any]) -> str:
        return "Saving acquired knowledge snippet"

    def add_knowledge(self, file_path: Path, title: str, content: str):
        file_content = f"## {title}\n\n{content}\n"
        file_path.write_text(file_content)
        # No need to update workspace manager - these files are managed separately

    def has_knowledge(self) -> bool:
        return self.knowledge_dir.exists() and any(self.knowledge_dir.glob("*.md"))

    def get_knowledge(self) -> str:
        if not self.has_knowledge():
            return ""

        # Combine all .md files in the knowledge directory
        knowledge_files = sorted(self.knowledge_dir.glob("*.md"))
        combined_content = []

        for file in knowledge_files:
            if file.is_file():
                content = file.read_text()
                if content:
                    combined_content.append(content)

        return "\n".join(combined_content)


class CompleteTool(LLMTool):
    name = "complete"
    """The model should call this tool when it is done with the task."""

    description = "Call this tool when you are done with the task, and supply your answer or summary."
    input_schema = {
        "type": "object",
        "properties": {
            "answer": {
                "type": "string",
                "description": "The answer to the question, or final summary of actions taken to accomplish the task.",
            },
        },
        "required": ["answer"],
    }

    def __init__(self, tool_call_logger: ToolCallLogger):
        super().__init__(tool_call_logger)
        self.answer: str = ""

    @property
    def should_stop(self):
        return self.answer != ""

    def reset(self):
        self.answer = ""

    def run_impl(
        self,
        tool_input: dict[str, Any],
        dialog_messages: Optional[DialogMessages] = None,
    ) -> ToolImplOutput:
        assert tool_input["answer"], "Model returned empty answer"
        self.answer = tool_input["answer"]
        return ToolImplOutput("Task completed", "Task completed")

    def get_tool_start_message(self, tool_input: dict[str, Any]) -> str:
        return ""


class CompleteFilterStepTool(LLMTool):
    name = "complete_filter_step"
    """The model should call this tool when it has finished running and tests."""

    description = "Call this tool when you have found a failing test, and explain what tests you ran."
    input_schema = {
        "type": "object",
        "properties": {
            "found_failing_test": {
                "type": "boolean",
                "description": "Whether you found a failing test.",
            },
            "answer": {
                "type": "string",
                "description": "Explain what tests you ran. If any failed, list them.",
            },
        },
        "required": ["answer"],
    }

    def __init__(self, tool_call_logger: ToolCallLogger):
        super().__init__(tool_call_logger)
        self.answer: str = ""

    @property
    def should_stop(self):
        return self.answer != ""

    def reset(self):
        self.answer = ""

    def run_impl(
        self,
        tool_input: dict[str, Any],
        dialog_messages: Optional[DialogMessages] = None,
    ) -> ToolImplOutput:
        assert tool_input["answer"], "Model returned empty answer"
        self.answer = tool_input["answer"]
        return ToolImplOutput("Task completed", "Task completed")

    def get_tool_start_message(self, tool_input: dict[str, Any]) -> str:
        return ""
