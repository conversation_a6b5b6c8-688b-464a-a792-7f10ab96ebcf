"""Utilities for testing."""

import os
from pathlib import Path


def get_augment_token_path() -> str | None:
    for token_path in (
        os.environ.get("AUGMENT_TOKEN_PATH"),
        Path("~/.config/augment/api_token").expanduser(),
        Path("~/.augment/token").expanduser(),
        Path("/home/<USER>/.config/augment/api_token"),
    ):
        if token_path and Path(token_path).exists():
            return str(token_path)
    return None


def get_augment_token() -> str:
    """Get the augment token from the environment."""
    token: str | None = os.environ.get("AUGMENT_TOKEN")
    if token is None:
        token_path = get_augment_token_path()
        if token_path:
            token = Path(token_path).read_text().strip()
    assert token, "AUGMENT_TOKEN is not set or found in ~/.augment/token or other paths"
    return token
