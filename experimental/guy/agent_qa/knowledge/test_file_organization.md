## Test File Organization Convention

The codebase follows a specific convention for organizing test files:

1. For each source file `foo.py`, there should be a corresponding test file named `test_foo.py` that contains all tests for that file.

2. Tests for different classes/functions from the same source file should be organized as test classes within the same test file, rather than creating separate test files for each class.

Example:
- Source file: `tools.py`
- Test file: `test_tools.py` (contains all tests for classes/functions defined in tools.py)

This convention helps maintain a clear 1:1 mapping between source files and test files, making it easier to:
- Find tests for a specific file
- Ensure all files have corresponding tests
- Keep related tests organized together
- Maintain a consistent project structure
