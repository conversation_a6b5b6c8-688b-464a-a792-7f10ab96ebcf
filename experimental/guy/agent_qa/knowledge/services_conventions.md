## Test File Naming Convention in services/ and Build System

In the services/ directory, test files follow the naming convention of `foo_test.py` rather than `test_foo.py`. For example:
- Source file: `next_edit_server.py`
- Test file: `next_edit_server_test.py`

The project uses <PERSON><PERSON> as its build system. Bazel is used both for building the code and running tests. This means:
- Tests are defined in BUILD files
- Tests are run using bazel commands like `bazel test //path/to:target`
- Dependencies are managed through <PERSON><PERSON>'s build system
