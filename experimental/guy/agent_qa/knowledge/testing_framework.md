## Testing Framework and Practices

The codebase uses pytest as the main testing framework with the following key characteristics:

1. File Organization:
- Test files use patterns: `test_*.py` or `*_test.py`
- Tests are either in dedicated `test` directories or alongside source files
- Special `conftest.py` files contain shared fixtures and setup

2. Testing Utilities:
- Common utilities in `base/test_utils/`
- Support for property-based testing
- Helpers for random data generation
- Error context handling
- Mock services and emulators

3. Best Practices:
- Tests use pytest fixtures for setup/teardown
- Tests follow Arrange-Act-Assert pattern
- Mock objects used for component isolation
- Tests are atomic and focused
- Both positive and negative cases are tested
- Test functions and fixtures are documented

4. Common Patterns:
- Test files start with docstring explaining purpose
- Test functions prefixed with "test_"
- Fixtures defined with appropriate scope
- Assert statements used for verification
- Mock objects used via unittest.mock
