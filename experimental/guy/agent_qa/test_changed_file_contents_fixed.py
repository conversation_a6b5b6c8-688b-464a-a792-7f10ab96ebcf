#!/usr/bin/env python3
"""Test that ChangedFile objects have correct old_contents and new_contents after the fix."""

import os
import tempfile
import shutil
from pathlib import Path
import unittest
from unittest.mock import MagicMock, patch

from experimental.guy.agent_qa.workspace_manager import WorkspaceManagerImpl
from experimental.guy.agent_qa.interactive_agent import CLIInterface
from research.agents.changed_file import ChangedFile
from research.agents.tools import LoggedWorkspaceChanges, ToolCallLogger


class TestChangedFileContentsFix(unittest.TestCase):
    """Test that ChangedFile objects have correct old_contents and new_contents after the fix."""

    def setUp(self):
        """Set up a temporary workspace."""
        # Create a temporary directory
        self.temp_dir = tempfile.mkdtemp()
        self.workspace_root = Path(self.temp_dir)
        self.cache_dir = tempfile.mkdtemp()

        # Create a test file
        self.test_file_path = self.workspace_root / "test_file.txt"
        self.test_file_content = "This is the original content."
        self.test_file_path.write_text(self.test_file_content)

        # Create a mock AugmentPrototypingClient
        self.mock_client = MagicMock()

        # Create the WorkspaceManagerImpl
        self.workspace_manager = WorkspaceManagerImpl(
            augment_client=self.mock_client,
            root=self.workspace_root,
            cache_root=Path(self.cache_dir),
        )

        # Take an initial snapshot
        self.initial_snapshot = self.workspace_manager.snapshot_workspace()

    def tearDown(self):
        """Clean up the temporary directories."""
        shutil.rmtree(self.temp_dir)
        shutil.rmtree(self.cache_dir)

    def test_fixed_changed_file_contents(self):
        """Test that ChangedFile objects have correct old_contents and new_contents after the fix."""
        # Modify the test file
        new_content = "This is the modified content."
        self.test_file_path.write_text(new_content)

        # Take another snapshot
        self.workspace_manager.snapshot_workspace()

        # Get the diff
        diff = self.workspace_manager.get_last_turn_diff()
        self.assertIsNotNone(diff, "Diff should not be None")

        # We don't need a tool call logger for this test

        # Create a mock CLIInterface with the necessary methods for the test
        class MockCLIInterface:
            def __init__(self, workspace_manager):
                self.workspace_manager = workspace_manager

            def _add_changed_file_to_exchange(self, changed_file, remote_exchange):
                # This method is not needed for the test
                pass

        # We don't need to create a CLIInterface instance for this test
        # Just using the MockCLIInterface class definition

        # Apply the fix from interactive_agent.py
        changed_files = []

        # Get the current snapshot and the previous snapshot
        current_snapshot = self.workspace_manager.num_snapshots() - 1
        previous_snapshot = current_snapshot - 1

        for file_patch in diff:
            path = file_patch.path

            # Helper function to read file contents from a snapshot
            def read_file_from_snapshot(snapshot_id, file_path):
                try:
                    # Temporarily revert to the snapshot
                    self.workspace_manager.revert_to_snapshot(snapshot_id)
                    # Read the file contents
                    file_path_obj = self.workspace_manager.root / file_path
                    if file_path_obj.exists():
                        return file_path_obj.read_text()
                    return ""
                except Exception as e:
                    print(
                        f"Error reading file {file_path} from snapshot {snapshot_id}: {e}"
                    )
                    return ""
                finally:
                    # Revert back to the current snapshot
                    self.workspace_manager.revert_to_snapshot(current_snapshot)

            # Determine change type and paths
            if file_patch.is_added_file:
                # For added files, read the contents from the current snapshot
                new_contents = read_file_from_snapshot(current_snapshot, path)
                changed_files.append(
                    ChangedFile.added(
                        new_path=path,
                        new_contents=new_contents,
                    )
                )
            elif file_patch.is_removed_file:
                # For deleted files, read the contents from the previous snapshot
                old_contents = read_file_from_snapshot(previous_snapshot, path)
                changed_files.append(
                    ChangedFile.deleted(
                        old_path=path,
                        old_contents=old_contents,
                    )
                )
            elif file_patch.is_rename:
                # For renamed files, read the old contents from the previous snapshot
                # and the new contents from the current snapshot
                old_path = file_patch.source_file.replace("a/", "")
                old_contents = read_file_from_snapshot(previous_snapshot, old_path)
                new_contents = read_file_from_snapshot(current_snapshot, path)
                changed_files.append(
                    ChangedFile.renamed(
                        old_path=old_path,
                        new_path=path,
                        old_contents=old_contents,
                        new_contents=new_contents,
                    )
                )
            else:  # Modified
                # For modified files, read the old contents from the previous snapshot
                # and the new contents from the current snapshot
                old_contents = read_file_from_snapshot(previous_snapshot, path)
                new_contents = read_file_from_snapshot(current_snapshot, path)
                changed_files.append(
                    ChangedFile.modified(
                        path=path,
                        old_contents=old_contents,
                        new_contents=new_contents,
                    )
                )

        # Verify that we have one changed file
        self.assertEqual(len(changed_files), 1, "Should have one changed file")

        # Verify that the changed file is a modified file
        self.assertEqual(
            changed_files[0].change_type, "MODIFIED", "File should be modified"
        )

        # Verify that old_contents and new_contents contain the actual file contents
        self.assertEqual(
            changed_files[0].old_contents,
            self.test_file_content,
            "old_contents should contain the original file content",
        )
        self.assertEqual(
            changed_files[0].new_contents,
            new_content,
            "new_contents should contain the modified file content",
        )


if __name__ == "__main__":
    unittest.main()
