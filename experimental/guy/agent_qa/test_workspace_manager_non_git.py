#!/usr/bin/env python3
"""Test that WorkspaceManagerImpl.update() works correctly on non-git workspaces."""

import os
import tempfile
import shutil
from pathlib import Path
import unittest
from unittest.mock import MagicMock

from experimental.guy.agent_qa.workspace_manager import WorkspaceManagerImpl


class TestWorkspaceManagerNonGit(unittest.TestCase):
    """Test WorkspaceManagerImpl on non-git workspaces."""

    def setUp(self):
        """Set up a temporary non-git workspace."""
        # Create a temporary directory
        self.temp_dir = tempfile.mkdtemp()
        self.workspace_root = Path(self.temp_dir)

        # Create some test files
        self.test_files = [
            "file1.txt",
            "file2.py",
            "subdir/file3.js",
            "subdir/nested/file4.md",
        ]

        # Create the files with some content
        for file_path in self.test_files:
            full_path = self.workspace_root / file_path
            full_path.parent.mkdir(parents=True, exist_ok=True)
            full_path.write_text(f"Content of {file_path}")

        # Create a mock AugmentPrototypingClient
        self.mock_client = MagicMock()

        # Create a separate directory for the cache to avoid it being included in the workspace
        self.cache_dir = tempfile.mkdtemp()

        # Create the WorkspaceManagerImpl
        self.workspace_manager = WorkspaceManagerImpl(
            augment_client=self.mock_client,
            root=self.workspace_root,
            cache_root=Path(self.cache_dir),
        )

    def tearDown(self):
        """Clean up the temporary directories."""
        shutil.rmtree(self.temp_dir)
        shutil.rmtree(self.cache_dir)

    def test_update_finds_all_files(self):
        """Test that update() finds all files in a non-git workspace."""
        # Call update() to refresh the workspace state
        self.workspace_manager.update()

        # Get all paths from the workspace manager
        paths = self.workspace_manager.get_paths(relative=True)

        # Convert paths to strings for easier comparison
        path_strings = [str(p) for p in paths]

        # Verify that all test files were found
        for file_path in self.test_files:
            self.assertIn(
                file_path,
                path_strings,
                f"File {file_path} was not found by WorkspaceManagerImpl.update()",
            )

        # Print the actual files found for debugging
        print("Files found by WorkspaceManagerImpl:")
        for path in path_strings:
            print(f"  {path}")

        # Verify the total number of files matches
        self.assertEqual(
            len(path_strings),
            len(self.test_files),
            f"Expected {len(self.test_files)} files, but found {len(path_strings)}",
        )

    def test_update_after_adding_file(self):
        """Test that update() finds new files added to a non-git workspace."""
        # Call update() to refresh the workspace state
        self.workspace_manager.update()

        # Add a new file
        new_file = "new_file.txt"
        (self.workspace_root / new_file).write_text("New file content")

        # Call update() again
        self.workspace_manager.update()

        # Get all paths from the workspace manager
        paths = self.workspace_manager.get_paths(relative=True)

        # Convert paths to strings for easier comparison
        path_strings = [str(p) for p in paths]

        # Verify that the new file was found
        self.assertIn(
            new_file,
            path_strings,
            f"New file {new_file} was not found by WorkspaceManagerImpl.update()",
        )

        # Verify the total number of files matches
        expected_files = self.test_files + [new_file]
        self.assertEqual(
            len(path_strings),
            len(expected_files),
            f"Expected {len(expected_files)} files, but found {len(path_strings)}",
        )

    def test_update_after_removing_file(self):
        """Test that update() correctly handles removed files in a non-git workspace."""
        # Call update() to refresh the workspace state
        self.workspace_manager.update()

        # Remove a file
        file_to_remove = self.test_files[0]
        (self.workspace_root / file_to_remove).unlink()

        # Call update() again
        self.workspace_manager.update()

        # Get all paths from the workspace manager
        paths = self.workspace_manager.get_paths(relative=True)

        # Convert paths to strings for easier comparison
        path_strings = [str(p) for p in paths]

        # Verify that the removed file is no longer found
        self.assertNotIn(
            file_to_remove,
            path_strings,
            f"Removed file {file_to_remove} was still found by WorkspaceManagerImpl.update()",
        )

        # Verify the total number of files matches
        expected_files = self.test_files[1:]  # All files except the removed one
        self.assertEqual(
            len(path_strings),
            len(expected_files),
            f"Expected {len(expected_files)} files, but found {len(path_strings)}",
        )

    def test_update_after_modifying_file(self):
        """Test that update() correctly handles modified files in a non-git workspace."""
        # Call update() to refresh the workspace state
        self.workspace_manager.update()

        # Get the initial blob names
        initial_blob_names = self.workspace_manager.get_blob_names()

        # Modify a file
        file_to_modify = self.test_files[1]
        (self.workspace_root / file_to_modify).write_text("Modified content")

        # Call update() again
        self.workspace_manager.update()

        # Get the new blob names
        new_blob_names = self.workspace_manager.get_blob_names()

        # Verify that the blob names are different (indicating the file was detected as modified)
        self.assertNotEqual(
            initial_blob_names,
            new_blob_names,
            "Blob names should change after file modification",
        )


if __name__ == "__main__":
    unittest.main()
