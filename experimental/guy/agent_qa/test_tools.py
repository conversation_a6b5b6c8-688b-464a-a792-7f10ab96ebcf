"""Tests for the tools module."""

import os
from pathlib import Path
from typing import Any, Optional
from unittest.mock import <PERSON><PERSON><PERSON>, <PERSON><PERSON>, patch

import pytest

from experimental.guy.agent_qa.workspace_manager import (
    GlobPathFilter,
    TextFileFilter,
    WorkspaceManagerImpl,
)
from experimental.guy.agent_qa.user_confirmation import AlwaysConfirmUserConfirmationProvider
from research.agents.tools import DialogMessages, LLMTool, ToolImplOutput


class MockTool(LLMTool):
    def __init__(self, name: str, output: str):
        super().__init__(tool_call_logger=Mock())
        self.name = name
        self.output = output
        self.input_schema = {
            "type": "object",
            "properties": {
                "test_input": {"type": "string", "description": "Test input"}
            },
            "required": ["test_input"],
        }

    def run_impl(
        self,
        tool_input: dict[str, Any],
        dialog_messages: Optional[DialogMessages] = None,
    ) -> "ToolImplOutput":
        return ToolImplOutput(
            tool_output=self.output, tool_result_message="Mock tool called"
        )


class TestTextFileFilter:
    @pytest.fixture
    def temp_workspace(self, tmp_path):
        """Create a temporary workspace with various files."""
        workspace = tmp_path / "workspace"
        workspace.mkdir()

        # Create some text files
        (workspace / "file.py").write_text("print('hello')")
        (workspace / "doc.md").write_text("# Documentation")
        (workspace / "data.json").write_text('{"key": "value"}')

        subdir = workspace / "subdir"
        subdir.mkdir()
        (subdir / "data2.json").write_text('{"key2": "value2"}')
        (subdir / "README").write_text("Readme file")
        (subdir / "readme").write_text("readme file")
        (subdir / "BUILD").write_text("build file")

        # Create a binary file
        (workspace / "image.png").write_bytes(b"\x89PNG\r\n\x1a\n")

        # Create a large text file
        large_file = workspace / "large.txt"
        large_file.write_text("x" * (2 * 1024 * 1024))  # 2MB

        return workspace

    @pytest.fixture
    def gitignore(self, temp_workspace):
        """Create a .gitignore file."""
        gitignore = temp_workspace / ".gitignore"
        gitignore.write_text("""
# Python cache
__pycache__/
*.pyc

# Build output
build/
dist/

# Specific files
secret.txt
*.log
""")
        return gitignore

    @pytest.fixture
    def augmentignore(self, temp_workspace):
        """Create a .augmentignore file."""
        augmentignore = temp_workspace / ".augmentignore"
        augmentignore.write_text("""
# Large data files
data/*.csv
*.parquet

# Test files to ignore
test_ignore_*
""")
        return augmentignore

    def test_text_file_extensions(self, temp_workspace):
        """Test that only text files with allowed extensions are accepted."""
        filter = TextFileFilter(temp_workspace)

        # Should accept text files
        assert filter.accepts(temp_workspace / "file.py")
        assert filter.accepts(temp_workspace / "doc.md")
        assert filter.accepts(temp_workspace / "data.json")
        assert filter.accepts(temp_workspace / "subdir/data2.json")
        assert filter.accepts(temp_workspace / "subdir/README")
        assert filter.accepts(temp_workspace / "subdir/readme")
        assert filter.accepts(temp_workspace / "subdir/BUILD")

        # Should reject binary files
        assert not filter.accepts(temp_workspace / "image.png")
        assert not filter.accepts(temp_workspace / "archive.zip")

    def test_size_limit(self, temp_workspace):
        """Test that files over size limit are rejected."""
        filter = TextFileFilter(temp_workspace, max_file_size_bytes=1024 * 1024)  # 1MB

        # Small file should be accepted
        small_file = temp_workspace / "file.py"
        assert filter.accepts(small_file)

        # Large file should be rejected
        large_file = temp_workspace / "large.txt"
        assert not filter.accepts(large_file)

    def test_gitignore_patterns(self, temp_workspace, gitignore):
        """Test that .gitignore patterns are respected."""
        filter = TextFileFilter(temp_workspace)

        # Create some files that should be ignored
        (temp_workspace / "secret.txt").write_text("secret")
        (temp_workspace / "debug.log").write_text("log")
        (temp_workspace / "__pycache__").mkdir()
        (temp_workspace / "__pycache__/module.pyc").write_text("")

        # Files matching .gitignore patterns should be rejected
        assert not filter.accepts(temp_workspace / "secret.txt")
        assert not filter.accepts(temp_workspace / "debug.log")
        assert not filter.accepts(temp_workspace / "__pycache__/module.pyc")

        # Other files should still be accepted
        assert filter.accepts(temp_workspace / "file.py")

    def test_augmentignore_patterns(self, temp_workspace, augmentignore):
        """Test that .augmentignore patterns are respected."""
        filter = TextFileFilter(temp_workspace)

        # Create data directory with files
        data_dir = temp_workspace / "data"
        data_dir.mkdir()
        (data_dir / "data.csv").write_text("a,b,c")
        (data_dir / "data.parquet").write_text("parquet data")
        (data_dir / "data.json").write_text('{"data": 123}')  # Create the JSON file
        (temp_workspace / "test_ignore_this.py").write_text("test")

        # Files matching .augmentignore patterns should be rejected
        assert not filter.accepts(data_dir / "data.csv")
        assert not filter.accepts(data_dir / "data.parquet")
        assert not filter.accepts(temp_workspace / "test_ignore_this.py")

        # Other files should still be accepted
        assert filter.accepts(temp_workspace / "file.py")
        assert filter.accepts(data_dir / "data.json")

    def test_missing_ignore_files(self, temp_workspace):
        """Test that filter works when ignore files don't exist."""
        # Don't create .gitignore or .augmentignore
        filter = TextFileFilter(temp_workspace)

        # Should still filter based on extension and size
        assert filter.accepts(temp_workspace / "file.py")
        assert not filter.accepts(temp_workspace / "image.png")

    def test_invalid_path(self, temp_workspace):
        """Test handling of invalid paths."""
        filter = TextFileFilter(temp_workspace)

        # Path outside workspace
        outside_path = Path("/some/other/path/file.py")
        assert not filter.accepts(outside_path)

        # Non-existent file
        assert not filter.accepts(temp_workspace / "nonexistent.py")

    def test_stat_error_handling(self, temp_workspace, gitignore, augmentignore):
        """Test handling of stat errors."""
        filter = TextFileFilter(temp_workspace)
        test_file = temp_workspace / "test.py"
        test_file.write_text("test")

        # Create a mock that raises OSError
        error_mock = MagicMock()
        error_mock.side_effect = OSError("Permission denied")

        with patch.object(Path, "stat", error_mock):
            # Should reject file if can't check size
            assert not filter.accepts(test_file)

            # Verify that stat was called
            error_mock.assert_called_once()


class TestWorkspaceManagerImpl:
    @pytest.fixture
    def mock_augment_client(self):
        return Mock()

    @pytest.fixture
    def confirmation_provider(self):
        return AlwaysConfirmUserConfirmationProvider()

    @pytest.fixture
    def temp_workspace(self, tmp_path):
        workspace = tmp_path / "workspace"
        workspace.mkdir()
        (workspace / "file1.txt").write_text("Initial content 1")
        (workspace / "file2.txt").write_text("Initial content 2")
        return workspace

    @pytest.mark.xfail
    def test_init_and_update(self, temp_workspace, mock_augment_client):
        manager = WorkspaceManagerImpl(
            augment_client=mock_augment_client,
            root=temp_workspace,
            path_filter=GlobPathFilter("*.txt"),
        )

        # Check that paths were found
        paths = list(manager.get_paths())
        assert len(paths) == 2
        assert paths[0].name == "file1.txt"

        # Check that blob names were generated
        blob_names = manager.get_blob_names()
        assert len(blob_names) == 2

        # Check that files were uploaded
        mock_augment_client.upload_blobs_as_needed.assert_called_once()

    def test_avoid_directories(self, temp_workspace, mock_augment_client):
        subdir = temp_workspace / "subdir"
        subdir.mkdir()
        (subdir / "file3.txt").write_text("Initial content 3")
        manager = WorkspaceManagerImpl(
            augment_client=mock_augment_client,
            root=temp_workspace,
            path_filter=GlobPathFilter("*"),
        )

        paths = set(manager.get_paths())
        assert len(paths) == 3
        assert (temp_workspace / "subdir") not in paths
        assert paths == {
            temp_workspace / "file1.txt",
            temp_workspace / "file2.txt",
            subdir / "file3.txt",
        }

    def test_revert_to_initial_state(self, mock_augment_client, temp_workspace, confirmation_provider):
        workspace_manager = WorkspaceManagerImpl(
            mock_augment_client, temp_workspace, confirmation_provider=confirmation_provider
        )
        workspace_manager.snapshot_workspace()  # Take initial snapshot

        # Modify existing files and add a new file
        (temp_workspace / "file1.txt").write_text("Modified content 1")
        (temp_workspace / "file2.txt").write_text("Modified content 2")
        (temp_workspace / "file3.txt").write_text("New file content")

        workspace_manager.update()
        workspace_manager.revert_to_initial_state()

        assert (temp_workspace / "file1.txt").read_text() == "Initial content 1"
        assert (temp_workspace / "file2.txt").read_text() == "Initial content 2"
        assert not (temp_workspace / "file3.txt").exists()

    def test_revert_after_file_deletion(self, mock_augment_client, temp_workspace, confirmation_provider):
        workspace_manager = WorkspaceManagerImpl(
            mock_augment_client, temp_workspace, confirmation_provider=confirmation_provider
        )
        workspace_manager.snapshot_workspace()  # Take initial snapshot

        # Delete an existing file
        (temp_workspace / "file1.txt").unlink()

        workspace_manager.update()
        workspace_manager.revert_to_initial_state()

        assert (temp_workspace / "file1.txt").exists()
        assert (temp_workspace / "file1.txt").read_text() == "Initial content 1"
        assert (temp_workspace / "file2.txt").read_text() == "Initial content 2"

    def test_revert_with_subdirectories(self, mock_augment_client, temp_workspace, confirmation_provider):
        # Create a subdirectory with a file
        subdir = temp_workspace / "subdir"
        subdir.mkdir()
        (subdir / "subfile.txt").write_text("Subdir content")

        workspace_manager = WorkspaceManagerImpl(
            mock_augment_client, temp_workspace, confirmation_provider=confirmation_provider
        )
        workspace_manager.snapshot_workspace()  # Take initial snapshot

        # Modify the file in the subdirectory and add a new file
        (subdir / "subfile.txt").write_text("Modified subdir content")
        (subdir / "new_subfile.txt").write_text("New subdir content")

        workspace_manager.update()
        workspace_manager.revert_to_initial_state()

        assert (subdir / "subfile.txt").read_text() == "Subdir content"
        assert not (subdir / "new_subfile.txt").exists()

    def test_revert_multiple_times(self, mock_augment_client, temp_workspace, confirmation_provider):
        workspace_manager = WorkspaceManagerImpl(
            mock_augment_client, temp_workspace, confirmation_provider=confirmation_provider
        )
        workspace_manager.snapshot_workspace()  # Take initial snapshot

        # First modification
        (temp_workspace / "file1.txt").write_text("Modified content 1")
        workspace_manager.update()
        workspace_manager.revert_to_initial_state()

        assert (temp_workspace / "file1.txt").read_text() == "Initial content 1"

        # Second modification
        (temp_workspace / "file2.txt").write_text("Modified content 2")
        workspace_manager.update()
        workspace_manager.revert_to_initial_state()

        assert (temp_workspace / "file2.txt").read_text() == "Initial content 2"

    def test_revert_after_update(self, mock_augment_client, temp_workspace, confirmation_provider):
        workspace_manager = WorkspaceManagerImpl(
            mock_augment_client, temp_workspace, confirmation_provider=confirmation_provider
        )
        workspace_manager.snapshot_workspace()  # Take initial snapshot

        # Modify files and update
        (temp_workspace / "file1.txt").write_text("Modified content 1")
        (temp_workspace / "file3.txt").write_text("New file content")
        workspace_manager.update()

        # Modify files again without updating
        (temp_workspace / "file2.txt").write_text("Modified content 2")
        (temp_workspace / "file3.txt").write_text("Modified new file content")

        workspace_manager.revert_to_initial_state()

        assert (temp_workspace / "file1.txt").read_text() == "Initial content 1"
        assert (temp_workspace / "file2.txt").read_text() == "Initial content 2"
        assert not (temp_workspace / "file3.txt").exists()
