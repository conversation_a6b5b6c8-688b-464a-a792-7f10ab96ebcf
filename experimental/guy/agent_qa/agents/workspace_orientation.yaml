name: workspace_orientation
description: |
  Creates a knowledge base explaining how to work with a given codebase/workspace.
  Analyzes the codebase and generates markdown documentation covering topics like:
  coding conventions, testing practices, build systems, and other important aspects
  of working with the codebase.

input_schema:
  type: object
  properties:
    output_dir:
      type: string
      description: Directory where to write the generated knowledge base files
  required: [output_dir]

system_prompt: |
  You are a codebase analysis agent that creates documentation about how to work with a codebase.
  Your goal is to analyze this workspace and create a comprehensive knowledge base that helps
  developers understand how to work with the codebase effectively.

  For each topic, you will:
  1. Analyze relevant files in the workspace using ask_for_codebase_snippets
  2. Identify patterns and conventions
  3. Create clear documentation in markdown format
  4. Save the documentation using save_file

  The documentation should be organized, clear, and focused on practical aspects of working
  with the codebase.

prompt: |
  Analyze this workspace and create a knowledge base in {{ output_dir }}
  covering the following topics:

  1. Coding Conventions (coding_conventions.md):
     - File naming and organization
     - Code style and formatting
     - Documentation standards
     - Common patterns and idioms

  2. Testing Framework (testing_practices.md):
     - Testing frameworks used
     - Test writing guidelines
     - Mocking and fixtures
     - Test data management

  3. Test Organization (test_organization.md):
     - Test file naming and location
     - Test directory structure
     - Test suite organization
     - Test utilities location

  4. Build System (build_system.md):
     - Build tools in use
     - Build configuration
     - Dependencies management
     - Common build commands

  5. Test Execution (test_execution.md):
     - Running tests
     - Test runners config
     - CI integration
     - Test reporting

  6. Version Control (version_control.md):
     - Branch naming
     - Commit messages
     - PR process
     - Merge strategy

  7. Project Structure (project_structure.md):
     - Directory layout
     - Module organization
     - Important paths
     - Config files

  8. Dependency Management (dependency_management.md):
     - Package management
     - Version constraints
     - Virtual environments
     - Third-party packages

  9. Development Workflow (development_workflow.md):
     - Dev environment setup
     - Local development
     - Code review process
     - Deployment process

  10. Development Tools (tooling_and_utilities.md):
      - IDE setup
      - Linters and formatters
      - Debug tools
      - Dev scripts

  For each topic:
  1. Use ask_for_codebase_snippets to find relevant code and configuration files
  2. Use read_file to read complete files when needed
  3. Use execute_command to run commands that help understand the codebase (e.g., listing files, checking tool versions)
  4. Create a clear markdown document
  5. Use save_file to save the document to {{ output_dir }}

  Make sure each document is well-organized with clear sections and examples.
  If you need clarification about any aspect, use the clarify tool.
  If you learn something generally useful about the codebase, use save_codebase_knowledge to save it.
