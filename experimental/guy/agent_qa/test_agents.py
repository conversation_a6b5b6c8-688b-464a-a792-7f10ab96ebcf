"""Tests for the Agent and PromptedLLMAgent classes."""

from unittest.mock import Mock, patch
from dataclasses import dataclass

import pytest
from research.agents.tools import <PERSON>l<PERSON>all<PERSON><PERSON><PERSON>, ToolParam, DialogMessages

from experimental.guy.agent_qa.agent_mode import (
    AgentMode,
    ConstantModeProvider,
    AgentModeProvider,
)
from experimental.guy.agent_qa.agents import Agent, PromptedLLMAgent, DEFAULT_TOOLS


@dataclass
class MockToolParam(ToolParam):
    """Mock tool parameter for testing."""

    name: str
    description: str
    input_schema: dict


class MockTool:
    """A mock tool for testing."""

    def __init__(self, name: str, output: str):
        self.name = name
        self.output = output
        self.description = f"Mock tool {name}"
        self.input_schema = {
            "type": "object",
            "properties": {
                "input_value": {"type": "string", "description": "Input value"}
            },
            "required": ["input_value"],
        }

    def run(self, tool_input):
        return self.output

    def get_tool_param(self):
        """Get the tool parameter for LLM."""
        return MockToolParam(
            name=self.name,
            description=self.description,
            input_schema=self.input_schema,
        )


@pytest.fixture
def mock_llm_client():
    """Create a mock LLM client."""
    from research.llm_apis.llm_client import TextResult

    client = Mock()
    client.generate.return_value = (
        [TextResult(text="Test result")],
        {},
    )
    return client


@pytest.fixture
def tool_call_logger():
    """Create a tool call logger."""
    return ToolCallLogger()


@pytest.fixture
def mock_mode_provider(mock_tool):
    """Create a mock mode provider with a single tool."""
    mode = AgentMode(name="test_mode", system_prompt="test prompt", tools=[mock_tool])
    return ConstantModeProvider(mode)


def test_update_current_mode(mock_llm_client, tool_call_logger):
    """Test that _update_current_mode correctly updates both tools and system prompt."""
    # Create initial mode with one tool and prompt
    tool1 = MockTool("tool1", "output1")
    tool2 = MockTool("tool2", "output2")
    initial_mode = AgentMode(
        name="mode1",
        system_prompt="initial prompt",
        tools=[tool1],
    )

    # Create a mode provider that returns different modes each time
    class DynamicModeProvider(AgentModeProvider):
        def __init__(self, initial_mode):
            self.current_mode = initial_mode
            self.update_count = 0

        def get_current_mode(self) -> AgentMode:
            return self.current_mode

        def update_mode(self, dialog_messages: DialogMessages) -> None:
            # Change mode on each update
            self.update_count += 1
            self._update_to_next_mode()

        def _update_to_next_mode(self):
            """Update to the next mode in sequence."""
            if self.update_count == 1:
                # First update - switch to mode with tool2
                self.current_mode = AgentMode(
                    name="mode2",
                    system_prompt="second prompt",
                    tools=[tool2],
                )
            elif self.update_count == 2:
                # Second update - switch to mode with both tools
                self.current_mode = AgentMode(
                    name="mode3",
                    system_prompt="third prompt",
                    tools=[tool1, tool2],
                )

    mode_provider = DynamicModeProvider(initial_mode)

    # Create agent with recursive calls enabled
    # This will trigger first update in __init__
    agent = Agent(
        client=mock_llm_client,
        mode_provider=mode_provider,
        tool_call_logger=tool_call_logger,
        allow_recursive_calls=True,
    )

    # Verify initial state after __init__ update
    assert agent.system_prompt == "second prompt"  # First update happened in __init__
    assert len(agent.tools) == 3  # tool2 + complete tool + agent tool
    assert any(tool.name == "tool2" for tool in agent.tools)
    assert any(tool.name == "complete" for tool in agent.tools)
    assert any(isinstance(tool, Agent) for tool in agent.tools)
    assert not any(tool.name == "tool1" for tool in agent.tools)

    # Second update - should switch to mode3 with both tools
    agent._update_current_mode()
    assert agent.system_prompt == "third prompt"
    assert len(agent.tools) == 4  # tool1 + tool2 + complete tool + agent tool
    assert any(tool.name == "tool1" for tool in agent.tools)
    assert any(tool.name == "tool2" for tool in agent.tools)
    assert any(tool.name == "complete" for tool in agent.tools)
    assert any(isinstance(tool, Agent) for tool in agent.tools)

    # Create agent without recursive calls
    # Reset provider first
    mode_provider = DynamicModeProvider(initial_mode)
    agent_no_recursive = Agent(
        client=mock_llm_client,
        mode_provider=mode_provider,
        tool_call_logger=tool_call_logger,
        allow_recursive_calls=False,
    )

    # Verify no agent tool is added but complete tool is
    assert (
        len(agent_no_recursive.tools) == 2
    )  # tool2 + complete tool (after first update)
    assert not any(isinstance(tool, Agent) for tool in agent_no_recursive.tools)
    assert any(tool.name == "complete" for tool in agent_no_recursive.tools)


def test_agent_name(mock_llm_client, tool_call_logger):
    """Test that agent name is properly included in system prompt."""
    # Create agent with a name
    agent = Agent(
        client=mock_llm_client,
        mode_provider=ConstantModeProvider(
            AgentMode(name="test_mode", system_prompt="Test system prompt", tools=[])
        ),
        tool_call_logger=tool_call_logger,
        agent_name="TestAgent",
    )

    # Verify name is included in system prompt
    assert "Your name is TestAgent" in agent._get_system_prompt()

    # Create agent without a name
    agent_no_name = Agent(
        client=mock_llm_client,
        mode_provider=ConstantModeProvider(
            AgentMode(name="test_mode", system_prompt="Test system prompt", tools=[])
        ),
        tool_call_logger=tool_call_logger,
    )

    # Verify name is not included in system prompt
    assert "Your name is" not in agent_no_name._get_system_prompt()


def test_prompted_llm_agent_init(mock_llm_client, tool_call_logger):
    """Test basic initialization."""
    mock_tool = MockTool("mock_tool", "mock output")
    agent = PromptedLLMAgent(
        name="test_agent",
        description="A test agent",
        input_schema={
            "type": "object",
            "properties": {
                "input_value": {"type": "string", "description": "Input value"}
            },
            "required": ["input_value"],
        },
        tools=[mock_tool],
        prompt_template="Process {{ input_value }}",
        client=mock_llm_client,
        tool_call_logger=tool_call_logger,
        max_turns=10,
    )

    assert agent.name == "test_agent"
    assert len(agent.tools) == 1
    assert agent.tools[0].name == "mock_tool"


def test_prompted_llm_agent_template_rendering(mock_llm_client, tool_call_logger):
    """Test that the template is rendered correctly."""
    mock_tool = MockTool("mock_tool", "mock output")
    agent = PromptedLLMAgent(
        name="test_agent",
        description="A test agent",
        input_schema={
            "type": "object",
            "properties": {
                "input_value": {"type": "string", "description": "Input value"}
            },
            "required": ["input_value"],
        },
        tools=[mock_tool],
        prompt_template="Process {{ input_value }}",
        client=mock_llm_client,
        tool_call_logger=tool_call_logger,
        max_turns=10,
    )

    # Mock the Agent class to capture the formatted prompt
    with patch("experimental.guy.agent_qa.agents.Agent") as MockAgent:
        mock_agent_instance = Mock()
        mock_agent_instance.run_agent.return_value = "Test result"
        MockAgent.return_value = mock_agent_instance

        agent.run({"input_value": "test_value"})

        # Verify the prompt was formatted correctly
        mock_agent_instance.run_agent.assert_called_once_with(
            "Process test_value", resume=False
        )


def test_prompted_llm_agent_execution(mock_llm_client, tool_call_logger):
    """Test that the agent executes correctly."""
    mock_tool = MockTool("mock_tool", "mock output")
    agent = PromptedLLMAgent(
        name="test_agent",
        description="A test agent",
        input_schema={
            "type": "object",
            "properties": {
                "input_value": {"type": "string", "description": "Input value"}
            },
            "required": ["input_value"],
        },
        tools=[mock_tool],
        prompt_template="Process {{ input_value }}",
        client=mock_llm_client,
        tool_call_logger=tool_call_logger,
        max_turns=10,
    )

    result = agent.run({"input_value": "test_value"})
    assert result == "Test result"


def test_prompted_llm_agent_invalid_template(mock_llm_client, tool_call_logger):
    """Test that invalid templates raise errors."""
    with pytest.raises(Exception):  # jinja2 raises various exceptions
        PromptedLLMAgent(
            name="test_agent",
            description="A test agent",
            input_schema={
                "type": "object",
                "properties": {
                    "input_value": {"type": "string", "description": "Input value"}
                },
                "required": ["input_value"],
            },
            tools=[],
            prompt_template="{{ unclosed",  # Invalid template
            client=mock_llm_client,
            tool_call_logger=tool_call_logger,
            max_turns=10,
        )


def test_prompted_llm_agent_missing_template_var(mock_llm_client, tool_call_logger):
    """Test that missing template variables raise errors."""
    agent = PromptedLLMAgent(
        name="test_agent",
        description="A test agent",
        input_schema={
            "type": "object",
            "properties": {
                "input_value": {"type": "string", "description": "Input value"}
            },
            "required": ["input_value"],
        },
        tools=[],
        prompt_template="Process {{ missing_var }}",  # Variable not in schema
        client=mock_llm_client,
        tool_call_logger=tool_call_logger,
        max_turns=10,
    )

    with pytest.raises(Exception):  # jinja2 raises UndefinedError
        agent.run({"input_value": "test_value"})


def test_prompted_llm_agent_from_yaml_basic(mock_llm_client, tool_call_logger):
    """Test basic YAML loading functionality."""
    yaml_dict = {
        "name": "test_agent",
        "description": "A test agent",
        "input_schema": {
            "type": "object",
            "properties": {
                "input_value": {"type": "string", "description": "Input value"}
            },
            "required": ["input_value"],
        },
        "system_prompt": "You are a test agent",
        "prompt": "Process {{ input_value }}",
        "tools": ["clarify", "complete"],
    }

    agent = PromptedLLMAgent.from_yaml(
        yaml_dict=yaml_dict,
        client=mock_llm_client,
        tool_call_logger=tool_call_logger,
        available_tools=[
            MockTool("clarify", "clarified"),
            MockTool("complete", "completed"),
        ],
        max_turns=10,
    )

    assert agent.name == "test_agent"
    assert "test agent" in agent.description.lower()
    assert len(agent.tools) == 2


def test_prompted_llm_agent_from_yaml_required_fields(
    mock_llm_client, tool_call_logger
):
    """Test that all required fields are checked."""
    yaml_dict = {
        "name": "test_agent",
        "description": "A test agent",
        "input_schema": {
            "type": "object",
            "properties": {},
        },
        "system_prompt": "You are a test agent",
        "prompt": "Test prompt",
        "tools": [],
    }

    # Test removing each required field
    for field in [
        "name",
        "description",
        "input_schema",
        "prompt",
    ]:
        bad_yaml = yaml_dict.copy()
        del bad_yaml[field]
        with pytest.raises(ValueError, match=f"Missing required field: {field}"):
            PromptedLLMAgent.from_yaml(
                yaml_dict=bad_yaml,
                client=mock_llm_client,
                tool_call_logger=tool_call_logger,
                available_tools=[],
                max_turns=10,
            )


def test_prompted_llm_agent_from_yaml_tool_filtering(mock_llm_client, tool_call_logger):
    """Test that from_yaml only includes tools specified in the YAML."""
    yaml_dict = {
        "name": "test_agent",
        "description": "A test agent",
        "input_schema": {
            "type": "object",
            "properties": {
                "input_value": {"type": "string", "description": "Input value"}
            },
            "required": ["input_value"],
        },
        "system_prompt": "You are a test agent",
        "prompt": "Process {{ input_value }}",
        "tools": ["tool1"],  # Only specify one tool in YAML
    }

    # Create more tools than specified in YAML
    available_tools = [
        MockTool("tool1", "output1"),
        MockTool("tool2", "output2"),
        MockTool("tool3", "output3"),
    ]

    agent = PromptedLLMAgent.from_yaml(
        yaml_dict=yaml_dict,
        client=mock_llm_client,
        tool_call_logger=tool_call_logger,
        available_tools=available_tools,  # Pass all tools
        max_turns=10,
    )

    # Verify only the tool specified in YAML is included
    assert len(agent.tools) == 1
    assert agent.tools[0].name == "tool1"
    assert all(tool.name != "tool2" for tool in agent.tools)
    assert all(tool.name != "tool3" for tool in agent.tools)


def test_prompted_llm_agent_from_yaml_default_tools(mock_llm_client, tool_call_logger):
    """Test that from_yaml uses default tools when no tools section is specified."""
    yaml_dict = {
        "name": "test_agent",
        "description": "A test agent",
        "input_schema": {
            "type": "object",
            "properties": {
                "input_value": {"type": "string", "description": "Input value"}
            },
            "required": ["input_value"],
        },
        "system_prompt": "You are a test agent",
        "prompt": "Process {{ input_value }}",
        # No tools section specified
    }

    # Create available tools including all default tools
    available_tools = [MockTool(name, f"output_{name}") for name in DEFAULT_TOOLS]

    agent = PromptedLLMAgent.from_yaml(
        yaml_dict=yaml_dict,
        client=mock_llm_client,
        tool_call_logger=tool_call_logger,
        available_tools=available_tools,
        max_turns=10,
    )

    # Verify all default tools are included
    assert len(agent.tools) == len(DEFAULT_TOOLS)
    assert all(tool.name in DEFAULT_TOOLS for tool in agent.tools)


def test_prompted_llm_agent_from_yaml_add_tools(mock_llm_client, tool_call_logger):
    """Test that from_yaml correctly handles add_tools section."""
    yaml_dict = {
        "name": "test_agent",
        "description": "A test agent",
        "input_schema": {
            "type": "object",
            "properties": {
                "input_value": {"type": "string", "description": "Input value"}
            },
            "required": ["input_value"],
        },
        "system_prompt": "You are a test agent",
        "prompt": "Process {{ input_value }}",
        "add_tools": ["extra_tool1", "extra_tool2"],
    }

    # Create available tools including default tools and extra tools
    available_tools = [MockTool(name, f"output_{name}") for name in DEFAULT_TOOLS]
    available_tools.extend(
        [
            MockTool("extra_tool1", "extra_output1"),
            MockTool("extra_tool2", "extra_output2"),
        ]
    )

    agent = PromptedLLMAgent.from_yaml(
        yaml_dict=yaml_dict,
        client=mock_llm_client,
        tool_call_logger=tool_call_logger,
        available_tools=available_tools,
        max_turns=10,
    )

    # Verify default tools plus additional tools are included
    assert len(agent.tools) == len(DEFAULT_TOOLS) + 2
    assert all(
        tool.name in DEFAULT_TOOLS + ["extra_tool1", "extra_tool2"]
        for tool in agent.tools
    )


def test_prompted_llm_agent_from_yaml_remove_tools(mock_llm_client, tool_call_logger):
    """Test that from_yaml correctly handles remove_tools section."""
    yaml_dict = {
        "name": "test_agent",
        "description": "A test agent",
        "input_schema": {
            "type": "object",
            "properties": {
                "input_value": {"type": "string", "description": "Input value"}
            },
            "required": ["input_value"],
        },
        "system_prompt": "You are a test agent",
        "prompt": "Process {{ input_value }}",
        "remove_tools": ["save_file", "edit_file_agent"],  # Remove some default tools
    }

    # Create available tools including all default tools
    available_tools = [MockTool(name, f"output_{name}") for name in DEFAULT_TOOLS]

    agent = PromptedLLMAgent.from_yaml(
        yaml_dict=yaml_dict,
        client=mock_llm_client,
        tool_call_logger=tool_call_logger,
        available_tools=available_tools,
        max_turns=10,
    )

    # Verify specified tools are removed
    assert len(agent.tools) == len(DEFAULT_TOOLS) - 2
    assert all(
        tool.name not in ["save_file", "edit_file_agent"] for tool in agent.tools
    )
    assert all(tool.name in DEFAULT_TOOLS for tool in agent.tools)


def test_prompted_llm_agent_from_yaml_add_and_remove_tools(
    mock_llm_client, tool_call_logger
):
    """Test that from_yaml correctly handles both add_tools and remove_tools sections."""
    yaml_dict = {
        "name": "test_agent",
        "description": "A test agent",
        "input_schema": {
            "type": "object",
            "properties": {
                "input_value": {"type": "string", "description": "Input value"}
            },
            "required": ["input_value"],
        },
        "system_prompt": "You are a test agent",
        "prompt": "Process {{ input_value }}",
        "add_tools": ["extra_tool1"],
        "remove_tools": ["save_file"],
    }

    # Create available tools including default tools and extra tool
    available_tools = [MockTool(name, f"output_{name}") for name in DEFAULT_TOOLS]
    available_tools.append(MockTool("extra_tool1", "extra_output1"))

    agent = PromptedLLMAgent.from_yaml(
        yaml_dict=yaml_dict,
        client=mock_llm_client,
        tool_call_logger=tool_call_logger,
        available_tools=available_tools,
        max_turns=10,
    )

    # Verify tools are correctly added and removed
    assert len(agent.tools) == len(DEFAULT_TOOLS) - 1 + 1  # -1 removed, +1 added
    assert any(tool.name == "extra_tool1" for tool in agent.tools)
    assert not any(tool.name == "save_file" for tool in agent.tools)


def test_prompted_llm_agent_from_yaml_tools_precedence(
    mock_llm_client, tool_call_logger
):
    """Test that explicit tools list takes precedence over add_tools and remove_tools."""
    yaml_dict = {
        "name": "test_agent",
        "description": "A test agent",
        "input_schema": {
            "type": "object",
            "properties": {
                "input_value": {"type": "string", "description": "Input value"}
            },
            "required": ["input_value"],
        },
        "system_prompt": "You are a test agent",
        "prompt": "Process {{ input_value }}",
        "tools": ["tool1", "tool2"],  # Explicit tools list
        "add_tools": ["extra_tool1"],  # Should be ignored
        "remove_tools": ["tool1"],  # Should be ignored
    }

    # Create available tools
    available_tools = [
        MockTool("tool1", "output1"),
        MockTool("tool2", "output2"),
        MockTool("extra_tool1", "extra1"),
    ]

    agent = PromptedLLMAgent.from_yaml(
        yaml_dict=yaml_dict,
        client=mock_llm_client,
        tool_call_logger=tool_call_logger,
        available_tools=available_tools,
        max_turns=10,
    )

    # Verify only explicitly listed tools are included
    assert len(agent.tools) == 2
    assert set(tool.name for tool in agent.tools) == {"tool1", "tool2"}
