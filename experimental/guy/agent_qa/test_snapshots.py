#!/usr/bin/env python3
"""Interactive test script for trying out workspace snapshots."""

from pathlib import Path
import tempfile
from experimental.guy.agent_qa.workspace_manager import WorkspaceManagerImpl
from experimental.guy.agent_qa.prototyping_client import UploadContent


class MockClient:
    """Mock client that does nothing."""

    def upload_blobs_as_needed(self, contents: list[UploadContent]):
        """Mock upload that does nothing."""
        pass


def main():
    """Run the interactive test.

    Creates a temporary workspace, performs various file operations,
    takes snapshots, shows diffs between snapshots, and demonstrates
    reverting to previous states.
    """
    # Create a temporary workspace
    with tempfile.TemporaryDirectory() as temp_dir:
        root = Path(temp_dir)
        print(f"\nCreated workspace at {root}")

        # Create initial files
        (root / "file1.txt").write_text("Initial content 1")
        (root / "file2.txt").write_text("Initial content 2")
        print("\nCreated initial files:")
        print("file1.txt: Initial content 1")
        print("file2.txt: Initial content 2")

        # Initialize workspace manager (this takes initial snapshot)
        mock_client = MockClient()
        manager = WorkspaceManagerImpl(
            augment_client=mock_client,
            root=root,
        )
        print("\nInitialized workspace manager (snapshot 0 is initial state)")

        # Modify a file and add a new one
        print("\nModifying file1.txt and adding file3.txt...")
        (root / "file1.txt").write_text("Modified content 1")
        (root / "file3.txt").write_text("New content")
        manager.update()
        snapshot1 = manager.snapshot_workspace()
        print(f"Took snapshot: {snapshot1}")

        # Show diff between snapshots
        print("\nDiff between initial state and first modification:")
        diff = manager.diff_snapshots(0, snapshot1)  # Use 0 for initial state
        for patched_file in diff:
            print(f"\nFile: {patched_file.path}")
            print("Changes:")
            for hunk in patched_file:
                print(hunk)

        # Remove a file
        print("\nRemoving file2.txt...")
        (root / "file2.txt").unlink()
        manager.update()
        snapshot2 = manager.snapshot_workspace()
        print(f"Took snapshot: {snapshot2}")

        # Show diff between snapshots
        print("\nDiff between first and second modification:")
        diff = manager.diff_snapshots(snapshot1, snapshot2)
        for patched_file in diff:
            print(f"\nFile: {patched_file.path}")
            print("Changes:")
            for hunk in patched_file:
                print(hunk)

        # Revert to initial state
        print("\nReverting to initial state...")
        manager.revert_to_snapshot(0)  # Use 0 for initial state

        # Show current files
        print("\nCurrent files after revert:")
        for path in sorted(manager.get_paths()):
            print(f"{path.name}: {path.read_text()}")


if __name__ == "__main__":
    main()
