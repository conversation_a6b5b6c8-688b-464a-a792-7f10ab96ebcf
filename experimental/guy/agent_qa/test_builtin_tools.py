"""Tests for builtin_tools classes."""

import json
import os
import subprocess
from pathlib import Path
from unittest.mock import <PERSON><PERSON><PERSON>, Mock, call, patch

import pytest

from base.augment_client.client import AugmentClient
from experimental.guy.agent_qa.builtin_tools import (
    ClarifyTool,
    CodebaseKnowledgeTool,
    CompleteTool,
    FileEditClient,
    ReadFileTool,
    SaveFileTool,
)
from experimental.guy.agent_qa.changes import RecentChangesTool
from experimental.guy.agent_qa.command_approval import CommandApprovalManager
from experimental.guy.agent_qa.prototyping_client import AugmentPrototypingClient
from experimental.guy.agent_qa.test_utils import get_augment_token
from experimental.guy.agent_qa.workspace_manager import WorkspaceManagerImpl
from research.agents.tools import DialogMessages, ToolCallLogger


@pytest.fixture
def smart_paste_client():
    token = get_augment_token()
    augment_client = AugmentClient(
        url="https://staging-shard-0.api.augmentcode.com",
        token=token,
    )
    return FileEditClient(augment_client)


@pytest.fixture
def mock_tool_call_logger():
    return Mock(spec=ToolCallLogger)


@pytest.fixture
def temp_workspace(tmp_path):
    """Create a temporary workspace with some test files."""
    workspace = tmp_path / "workspace"
    workspace.mkdir()
    test_file = workspace / "test.txt"
    test_file.write_text("test content")
    return workspace


@pytest.fixture
def mock_augment_client():
    mock = Mock()
    mock.upload_blobs_as_needed = Mock(return_value=None)
    return mock


@pytest.fixture
def command_approval_manager(tmp_path):
    return CommandApprovalManager(tmp_path)


class TestFileEditClient:
    def test_edit_file(self, smart_paste_client):
        target_file_path = "test_file.py"
        target_file_content = "def old_function():\n    pass\n"
        edit_plan = "Add a new function called new_function"
        code_block = "def new_function():\n    print('Hello, world!')\n"

        result, request_id = smart_paste_client.edit_file(
            target_file_path, target_file_content, edit_plan, code_block
        )

        expected_result = "def old_function():\n    pass\n\ndef new_function():\n    print('Hello, world!')\n"
        assert result == expected_result

    def test_edit_file_multiple_chunks(self, smart_paste_client):
        target_file_path = "test_file.py"
        target_file_content = "def old_function():\n    pass\n"
        edit_plan = "Add a new function for computing mean using numpy"
        code_block = """\
import numpy as np

# ... existing methods here ...

def new_function(x):
    mean = np.mean(x)
    return mean
"""

        result, request_id = smart_paste_client.edit_file(
            target_file_path, target_file_content, edit_plan, code_block
        )

        expected_result = """\
import numpy as np

def old_function():
    pass

def new_function(x):
    mean = np.mean(x)
    return mean
"""

        assert result == expected_result

    def test_edit_file_with_class(self, smart_paste_client):
        target_file_path = "test_file.py"
        target_file_content = """\
class DialogMessages:
    def __init__(self):
        self.messages = []

    def add_message(self, message):
        self.messages.append(message)
"""
        edit_plan = (
            "Remove the DialogMessages class and update imports to import it from tools"
        )
        code_block = """\
from research.agents.tools import DialogMessages

# ... rest of the file ...
"""

        result, request_id = smart_paste_client.edit_file(
            target_file_path, target_file_content, edit_plan, code_block
        )

        assert "class DialogMessages" not in result
        assert "from research.agents.tools import DialogMessages" in result


class TestReadFileTool:
    def test_read_existing_file(self, mock_tool_call_logger, temp_workspace):
        tool = ReadFileTool(tool_call_logger=mock_tool_call_logger, root=temp_workspace)

        result = tool.run_impl({"file_path": "test.txt"})

        assert result.tool_output == "test content"
        assert "Read file" in result.tool_result_message

    def test_read_nonexistent_file(self, mock_tool_call_logger, temp_workspace):
        tool = ReadFileTool(tool_call_logger=mock_tool_call_logger, root=temp_workspace)

        result = tool.run_impl({"file_path": "nonexistent.txt"})

        assert "does not exist" in result.tool_output
        assert "does not exist" in result.tool_result_message

    def test_read_large_file_with_truncation(
        self, mock_tool_call_logger, temp_workspace
    ):
        """Test that large files are truncated correctly."""
        # Create a large file
        large_file = temp_workspace / "large.txt"
        lines = []
        for i in range(10):
            lines.extend(
                [
                    f"First line of section {i}\n",
                    f"Second line of section {i}\n",
                    f"Third line of section {i}\n",
                    f"Fourth line of section {i}\n",
                    f"Fifth line of section {i}\n",
                ]
            )
        large_file.write_text("".join(lines))

        # Create tool with small budget to force truncation
        tool = ReadFileTool(
            tool_call_logger=mock_tool_call_logger, root=temp_workspace, char_budget=100
        )

        result = tool.run_impl({"file_path": "large.txt"})

        # Check truncation happened
        assert "truncated" in result.tool_result_message
        assert "100" in result.tool_result_message  # Budget should be mentioned

        # Check structure
        lines = result.tool_output.splitlines()
        assert lines[0].startswith("First line of section 0")  # First line preserved
        assert "... additional lines truncated ..." in result.tool_output
        assert lines[-1].startswith("Fifth line of section 9")  # Last line preserved

        # Check length is roughly within budget
        assert len(result.tool_output) <= 150  # Allow some slack over budget

    def test_read_file_under_budget(self, mock_tool_call_logger, temp_workspace):
        """Test that files under budget are not truncated."""
        content = "First line\nSecond line\nThird line\n"
        test_file = temp_workspace / "small.txt"
        test_file.write_text(content)

        # Create tool with budget larger than file
        tool = ReadFileTool(
            tool_call_logger=mock_tool_call_logger,
            root=temp_workspace,
            char_budget=1000,
        )

        result = tool.run_impl({"file_path": "small.txt"})

        # Check no truncation happened
        assert "truncated" not in result.tool_result_message
        assert result.tool_output == content

    def test_read_empty_file_with_truncation(
        self, mock_tool_call_logger, temp_workspace
    ):
        """Test that empty files are handled correctly with truncation enabled."""
        empty_file = temp_workspace / "empty.txt"
        empty_file.touch()

        # Create tool with small budget
        tool = ReadFileTool(
            tool_call_logger=mock_tool_call_logger, root=temp_workspace, char_budget=100
        )

        result = tool.run_impl({"file_path": "empty.txt"})

        assert result.tool_output == ""
        assert "truncated" not in result.tool_result_message


class TestSaveFileTool:
    def test_save_new_file(
        self, mock_tool_call_logger, temp_workspace, mock_augment_client
    ):
        workspace_manager = WorkspaceManagerImpl(
            augment_client=mock_augment_client, root=temp_workspace
        )
        tool = SaveFileTool(
            tool_call_logger=mock_tool_call_logger, workspace_manager=workspace_manager
        )

        result = tool.run_impl({"file_path": "new.txt", "file_content": "new content"})

        assert "File saved" in result.tool_output
        new_file = temp_workspace / "new.txt"
        assert new_file.read_text() == "new content\n"  # Note: newline added by default

    def test_save_existing_file(
        self, mock_tool_call_logger, temp_workspace, mock_augment_client
    ):
        workspace_manager = WorkspaceManagerImpl(
            augment_client=mock_augment_client, root=temp_workspace
        )
        tool = SaveFileTool(
            tool_call_logger=mock_tool_call_logger, workspace_manager=workspace_manager
        )

        result = tool.run_impl(
            {
                "file_path": "test.txt",  # This file already exists
                "file_content": "new content",
            }
        )

        assert "will not overwrite" in result.tool_output
        assert (
            temp_workspace / "test.txt"
        ).read_text() == "test content"  # Original content preserved


class TestClarifyTool:
    def test_clarify(self, mock_tool_call_logger):
        tool = ClarifyTool(
            client=MagicMock(),
            memories=MagicMock(),
            tool_call_logger=mock_tool_call_logger,
        )
        dialog_messages = DialogMessages()

        with patch("builtins.input", return_value="Test answer"), patch(
            "experimental.guy.agent_qa.builtin_tools.maybe_create_memory_from_instruction"
        ) as mock_create_memory:
            result = tool.run_impl(
                {"clarifying_question": "Test question?"},
                dialog_messages=dialog_messages,
            )

        mock_create_memory.assert_called_once_with(
            "Test answer",
            dialog_messages,
            tool.client,
            tool.memories,
            tool.tool_call_logger,
            verbose=False,
        )
        assert "Test answer" in result.tool_output
        assert "User answered" in result.tool_result_message


class TestCodebaseKnowledgeTool:
    def test_save_knowledge(
        self, mock_tool_call_logger, temp_workspace, mock_augment_client
    ):
        workspace_manager = WorkspaceManagerImpl(
            augment_client=mock_augment_client, root=temp_workspace
        )
        knowledge_dir = temp_workspace / "knowledge"
        tool = CodebaseKnowledgeTool(
            tool_call_logger=mock_tool_call_logger,
            workspace_manager=workspace_manager,
            knowledge_path=knowledge_dir,
        )

        result = tool.run_impl(
            {
                "title": "Test Knowledge",
                "content": "This is test knowledge content.",
                "filename": "test_knowledge.md",
            }
        )

        assert (
            "Memory saved" in result.tool_output
        )  # Changed from "Knowledge snippet saved"
        knowledge_file = knowledge_dir / "test_knowledge.md"
        assert knowledge_file.exists()
        content = knowledge_file.read_text()
        assert "## Test Knowledge" in content
        assert "This is test knowledge content." in content

    def test_multiple_knowledge_files(
        self, mock_tool_call_logger, temp_workspace, mock_augment_client
    ):
        workspace_manager = WorkspaceManagerImpl(
            augment_client=mock_augment_client, root=temp_workspace
        )
        knowledge_dir = temp_workspace / "knowledge"
        tool = CodebaseKnowledgeTool(
            tool_call_logger=mock_tool_call_logger,
            workspace_manager=workspace_manager,
            knowledge_path=knowledge_dir,
        )

        # Add first knowledge snippet
        tool.run_impl(
            {
                "title": "First Knowledge",
                "content": "First content.",
                "filename": "first.md",
            }
        )

        # Add second knowledge snippet
        tool.run_impl(
            {
                "title": "Second Knowledge",
                "content": "Second content.",
                "filename": "second.md",
            }
        )

        # Verify both files exist
        first_file = knowledge_dir / "first.md"
        second_file = knowledge_dir / "second.md"
        assert first_file.exists()
        assert second_file.exists()

        # Verify get_knowledge combines both files
        combined = tool.get_knowledge()
        assert "First Knowledge" in combined
        assert "First content." in combined
        assert "Second Knowledge" in combined
        assert "Second content." in combined

    def test_has_knowledge(
        self, mock_tool_call_logger, temp_workspace, mock_augment_client
    ):
        workspace_manager = WorkspaceManagerImpl(
            augment_client=mock_augment_client, root=temp_workspace
        )
        knowledge_dir = temp_workspace / "knowledge"
        tool = CodebaseKnowledgeTool(
            tool_call_logger=mock_tool_call_logger,
            workspace_manager=workspace_manager,
            knowledge_path=knowledge_dir,
        )

        assert not tool.has_knowledge()

        tool.run_impl(
            {
                "title": "Test Knowledge",
                "content": "Test content.",
                "filename": "test.md",
            }
        )

        assert tool.has_knowledge()

    def test_custom_knowledge_path(
        self, mock_tool_call_logger, temp_workspace, mock_augment_client
    ):
        workspace_manager = WorkspaceManagerImpl(
            augment_client=mock_augment_client, root=temp_workspace
        )

        custom_dir = temp_workspace / "custom"
        custom_dir.mkdir(parents=True)

        tool = CodebaseKnowledgeTool(
            tool_call_logger=mock_tool_call_logger,
            workspace_manager=workspace_manager,
            knowledge_path=custom_dir,
        )

        tool.run_impl(
            {
                "title": "Test Knowledge",
                "content": "Test content.",
                "filename": "test.md",
            }
        )

        knowledge_file = custom_dir / "test.md"
        assert knowledge_file.exists()
        assert "Test Knowledge" in knowledge_file.read_text()


class TestCompleteTool:
    def test_complete(self, mock_tool_call_logger):
        tool = CompleteTool(tool_call_logger=mock_tool_call_logger)

        result = tool.run_impl({"answer": "Task completed successfully"})

        assert "Task completed" in result.tool_output
        assert tool.should_stop
        assert tool.answer == "Task completed successfully"

    def test_reset(self, mock_tool_call_logger):
        tool = CompleteTool(tool_call_logger=mock_tool_call_logger)

        tool.run_impl({"answer": "Task completed successfully"})
        assert tool.should_stop

        tool.reset()
        assert not tool.should_stop
        assert tool.answer == ""

    def test_empty_answer(self, mock_tool_call_logger):
        tool = CompleteTool(tool_call_logger=mock_tool_call_logger)

        with pytest.raises(AssertionError):
            tool.run_impl({"answer": ""})


class TestRecentChangesTool:
    @pytest.fixture
    def workspace_dir(self, tmp_path: Path) -> Path:
        """Create a temporary directory for the test workspace."""
        return tmp_path / "workspace"

    @pytest.fixture
    def workspace_manager(self, workspace_dir: Path) -> WorkspaceManagerImpl:
        """Create a workspace manager for the test."""
        workspace_dir.mkdir(parents=True, exist_ok=True)
        mock_client = MagicMock(spec=AugmentPrototypingClient)
        return WorkspaceManagerImpl(mock_client, workspace_dir)

    @pytest.fixture
    def tool(self, workspace_manager: WorkspaceManagerImpl) -> RecentChangesTool:
        """Create the RecentChangesTool instance."""
        return RecentChangesTool(ToolCallLogger(), workspace_manager)

    @pytest.fixture
    def git_repo(self, workspace_dir: Path):
        """Set up a git repository with some commits and changes."""
        workspace_dir.mkdir(parents=True, exist_ok=True)

        def run_git(cmd: list[str]):
            subprocess.run(cmd, cwd=workspace_dir, check=True, capture_output=True)

        # Initialize git repo
        run_git(["git", "init"])
        run_git(["git", "config", "user.email", "<EMAIL>"])
        run_git(["git", "config", "user.name", "Test User"])

        # Create and commit initial file in main
        (workspace_dir / "initial.txt").write_text("initial content")
        run_git(["git", "add", "initial.txt"])
        run_git(["git", "commit", "-m", "Initial commit"])

        # Create main branch
        run_git(["git", "branch", "-M", "main"])

        # Create feature branch
        run_git(["git", "checkout", "-b", "feature"])

        # Add a committed file in feature
        (workspace_dir / "feature.txt").write_text("feature content")
        run_git(["git", "add", "feature.txt"])
        run_git(["git", "commit", "-m", "Add feature"])

        # Create staged changes
        (workspace_dir / "staged.txt").write_text("staged content")
        run_git(["git", "add", "staged.txt"])

        # Create unstaged changes
        (workspace_dir / "unstaged.txt").write_text("unstaged content")
        # Leave unstaged.txt completely untracked

        # Modify existing file (unstaged)
        (workspace_dir / "feature.txt").write_text("modified feature content")

        return workspace_dir

    def test_branch_point_changes(self, tool: RecentChangesTool, git_repo: Path):
        """Test getting changes since branch point."""
        result = tool.run({"mode": "branch_point"})

        # Should show branch name and changes
        assert "Current branch name: feature" in result
        assert "Changes since branching point:" in result
        assert "feature.txt" in result
        assert "staged.txt" in result
        assert "unstaged.txt" in result
        assert "initial.txt" not in result  # Was in main

        # Test name_only - should not include branch name
        result = tool.run({"mode": "branch_point", "name_only": True})
        assert "Current branch name:" not in result
        assert result.strip().split("\n") == [
            "feature.txt",
            "staged.txt",
            "unstaged.txt",
        ]

    def test_last_commit_changes(self, tool: RecentChangesTool, git_repo: Path):
        """Test getting all changes since last commit."""
        result = tool.run({"mode": "last_commit"})

        # Should show staged.txt (staged), unstaged.txt (new),
        # and feature.txt modifications (unstaged)
        assert "staged.txt" in result
        assert "unstaged.txt" in result
        assert "modified feature content" in result
        assert "feature content" in result  # Old content in diff
        assert "initial.txt" not in result  # No changes

        # Test name_only
        result = tool.run({"mode": "last_commit", "name_only": True})
        files = set(result.strip().split("\n"))
        assert files == {"staged.txt", "unstaged.txt", "feature.txt"}

    def test_staged_changes(self, tool: RecentChangesTool, git_repo: Path):
        """Test getting only staged changes."""
        result = tool.run({"mode": "staged"})

        # Should only show staged.txt
        assert "staged.txt" in result
        assert "staged content" in result
        assert "unstaged.txt" not in result
        assert "feature.txt" not in result
        assert "initial.txt" not in result

        # Test name_only
        result = tool.run({"mode": "staged", "name_only": True})
        assert result.strip() == "staged.txt"

    def test_unstaged_changes(self, tool: RecentChangesTool, git_repo: Path):
        """Test getting only unstaged changes."""
        result = tool.run({"mode": "unstaged"})

        # Should show unstaged.txt (new) and feature.txt modifications
        assert "unstaged.txt" in result
        assert "unstaged content" in result
        assert "modified feature content" in result
        assert (
            "diff --git a/staged.txt" not in result
        )  # Make sure staged.txt is not in the diff

        # Test name_only
        result = tool.run({"mode": "unstaged", "name_only": True})
        files = set(result.strip().split("\n"))
        assert files == {"unstaged.txt", "feature.txt"}

    def test_invalid_mode(self, tool: RecentChangesTool, git_repo: Path):
        """Test behavior with invalid mode."""
        result = tool.run({"mode": "invalid"})
        assert "'invalid' is not one of" in result

    def test_git_error_handling(self, tool: RecentChangesTool, workspace_dir: Path):
        """Test handling of git errors (no git repo)."""
        # Don't initialize git repo
        workspace_dir.mkdir(parents=True, exist_ok=True)

        result = tool.run({"mode": "unstaged"})
        assert "Git error" in result


class TestCommandApprovalManager:
    def test_session_approval(self, tmp_path):
        manager = CommandApprovalManager(tmp_path)
        command = "echo test"

        assert not manager.is_approved(command)
        manager.approve_for_session(command)
        assert manager.is_approved(command)

        # New instance shouldn't have session approvals
        manager2 = CommandApprovalManager(tmp_path)
        assert not manager2.is_approved(command)

    def test_always_approval(self, tmp_path):
        manager = CommandApprovalManager(tmp_path)
        command = "echo test"

        assert not manager.is_approved(command)
        manager.approve_always(command)
        assert manager.is_approved(command)

        # New instance should have always-approved commands
        manager2 = CommandApprovalManager(tmp_path)
        assert manager2.is_approved(command)

        # Check the file content
        approval_file = tmp_path / "approved_commands.json"  # Removed "agent" from path
        with open(approval_file) as f:
            saved_commands = json.load(f)
            assert command in saved_commands

    @patch("builtins.input")
    def test_get_approval_responses(self, mock_input, tmp_path):
        manager = CommandApprovalManager(tmp_path)
        command = "echo test"

        # Test 'Y' response (uppercase)
        mock_input.return_value = "Y"
        assert manager.get_approval(command)
        assert not manager.is_approved(command)  # Should not be remembered

        # Test 'y' response (lowercase)
        mock_input.return_value = "y"
        assert manager.get_approval(command)
        assert not manager.is_approved(command)

        # Test empty response (default to 'y')
        mock_input.return_value = ""
        assert manager.get_approval(command)
        assert not manager.is_approved(command)

        # Test 'n' response
        mock_input.return_value = "n"
        assert not manager.get_approval(command)
        assert not manager.is_approved(command)

        # Test 's' response
        mock_input.return_value = "s"
        assert manager.get_approval(command)
        assert manager.is_approved(command)  # Should be approved for session

        # Test 'a' response
        other_command = "echo other"
        mock_input.return_value = "a"
        assert manager.get_approval(other_command)
        assert manager.is_approved(other_command)  # Should be approved always
