from unittest.mock import Mock

from experimental.guy.agent_qa.agent_mode import Agent<PERSON><PERSON>, ConstantMode<PERSON>rovider
from experimental.guy.agent_qa.agents import Agent
from experimental.guy.agent_qa.builtin_tools import CompleteTool
from research.agents.tools import DialogMessages


def test_constant_mode_provider():
    # Create a mode with mock tools
    tool1 = Mock(name="tool1")
    tool2 = Mock(name="tool2")
    mode = AgentMode(
        name="test_mode", system_prompt="test prompt", tools=[tool1, tool2]
    )

    # Create provider
    provider = ConstantModeProvider(mode)

    # Test that it returns the same mode regardless of dialog
    dialog = DialogMessages()
    dialog.add_user_prompt("test message")

    # Test update_mode doesn't change anything
    provider.update_mode(dialog)

    # Test get_current_mode returns the constant mode
    returned_mode = provider.get_current_mode()
    assert returned_mode == mode
    assert returned_mode.system_prompt == "test prompt"
    assert returned_mode.tools == [tool1, tool2]


def test_agent_adds_complete_tool():
    # Create mode with tools
    tool1 = Mock(name="tool1")
    mode = AgentMode(name="test_mode", system_prompt="test prompt", tools=[tool1])
    provider = ConstantModeProvider(mode)

    # Create agent
    agent = Agent(
        client=Mock(),
        tool_call_logger=Mock(),
        mode_provider=provider,
    )

    # Verify agent adds complete tool to mode's tools
    assert len(agent.tools) == 2  # tool1 and complete tool
    assert agent.tools[0] == tool1
    assert isinstance(agent.tools[1], CompleteTool)


def test_agent_recursive_calls():
    # Create mode with tools
    tool1 = Mock(name="tool1")
    mode = AgentMode(name="test_mode", system_prompt="test prompt", tools=[tool1])
    provider = ConstantModeProvider(mode)

    # Create agent with recursive calls enabled
    agent = Agent(
        client=Mock(),
        tool_call_logger=Mock(),
        mode_provider=provider,
        allow_recursive_calls=True,
    )

    # Verify agent adds both complete tool and self
    assert len(agent.tools) == 3  # tool1, complete tool, and self
    assert agent.tools[0] == tool1
    assert isinstance(agent.tools[1], CompleteTool)
    assert agent.tools[2] == agent
