"""Test for the --no-complete-tool flag.

This test verifies that when the --no-complete-tool flag is used,
the agent doesn't have access to the "complete" tool and doesn't
mention it in the system prompt.
"""

import tempfile
from pathlib import Path

from experimental.guy.agent_qa.test_cli_agent_e2e import run_agent


def test_no_complete_tool():
    """Test that the --no-complete-tool flag works correctly."""
    # Create a temporary workspace directory
    with tempfile.TemporaryDirectory() as temp_dir:
        workspace_dir = Path(temp_dir)

        # Run the agent with a simple question
        instruction = "What is the capital of France?"

        # First, run without the flag to verify normal behavior
        result_with_complete = run_agent(
            workspace_dir,
            instruction,
            extra_args=["--use-anthropic-direct"],
        )

        # Then run with the --no-complete-tool flag
        result_without_complete = run_agent(
            workspace_dir,
            instruction,
            extra_args=["--use-anthropic-direct", "--no-complete-tool"],
        )

        # Verify that the complete tool is used in the normal output
        assert (
            "[invoking complete]" in result_with_complete.stdout
        ), "Expected complete tool to be used in normal output"

        # Verify that the complete tool is NOT used when using --no-complete-tool
        assert (
            "[invoking complete]" not in result_without_complete.stdout
        ), "Complete tool was used despite using --no-complete-tool"

        # Also verify that the orientation tools list doesn't include 'complete' when using --no-complete-tool
        with_complete_orientation = (
            "ORIENTATION TOOLS: ['read_file', 'sequential_thinking', 'complete']"
        )
        without_complete_orientation = (
            "ORIENTATION TOOLS: ['read_file', 'sequential_thinking']"
        )

        assert (
            with_complete_orientation in result_with_complete.stdout
        ), "Expected complete tool in orientation tools list"
        assert (
            without_complete_orientation in result_without_complete.stdout
        ), "Complete tool should not be in orientation tools list"

        # Both runs should still answer the question correctly
        assert (
            "paris" in result_with_complete.stdout.lower()
        ), "Agent didn't correctly identify Paris as the capital of France"
        assert (
            "paris" in result_without_complete.stdout.lower()
        ), "Agent didn't correctly identify Paris as the capital of France"


if __name__ == "__main__":
    test_no_complete_tool()
    print("Test passed! The --no-complete-tool flag works correctly.")
