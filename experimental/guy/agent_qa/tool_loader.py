"""Tool loading utilities with dependency management."""

from dataclasses import dataclass
from pathlib import Path
from typing import Dict, List, Optional, Set
import yaml

from research.agents.tools import LLMTool
from research.llm_apis.llm_client import LLMClient
from research.agents.tools import Tool<PERSON><PERSON><PERSON>ogger
from experimental.guy.agent_qa.agents import PromptedLLMAgent, DEFAULT_TOOLS
from experimental.guy.agent_qa.workspace_manager import WorkspaceManagerImpl


@dataclass
class ToolConfig:
    """Configuration for a tool loaded from YAML."""

    yaml_path: Path
    config: dict
    dependencies: Set[str]


class ToolLoader:
    """Manages loading tools from YAML with dependency resolution."""

    def __init__(
        self,
        yaml_files: List[Path],
        logging_client: LLMClient,
        tool_call_logger: ToolCallLogger,
        builtin_tools: List[LLMTool],
        max_output_tokens_per_turn: int = 8192,
        max_turns: int = 10,
        workspace_manager: Optional[WorkspaceManagerImpl] = None,
        system_prompt: str = "",
    ):
        """Initialize the tool loader.

        Args:
            yaml_files: List of YAML files to load
            logging_client: The LLM client to use
            tool_call_logger: Logger for tool calls
            builtin_tools: List of built-in tools
            max_output_tokens_per_turn: Maximum tokens per turn
            max_turns: Maximum number of turns
            workspace_manager: Optional workspace manager
            system_prompt: Optional system prompt prefix
        """
        self.yaml_files = yaml_files
        self.logging_client = logging_client
        self.tool_call_logger = tool_call_logger
        self.builtin_tools = builtin_tools
        self.max_output_tokens_per_turn = max_output_tokens_per_turn
        self.max_turns = max_turns
        self.workspace_manager = workspace_manager
        self.system_prompt = system_prompt

        # Maps tool names to their configs
        self.tool_configs: Dict[str, ToolConfig] = {}
        # Maps tool names to instantiated tools
        self.instantiated_tools: Dict[str, LLMTool] = {t.name: t for t in builtin_tools}

    def load_yaml_configs(self) -> None:
        """Load all YAML configs and extract their dependencies."""
        for yaml_path in self.yaml_files:
            if not yaml_path.exists():
                print(f"Warning: Tool config file {yaml_path} does not exist")
                continue

            try:
                with open(yaml_path) as f:
                    config = yaml.safe_load(f)

                # Validate required fields
                required_fields = ["name", "description", "input_schema", "prompt"]
                for field in required_fields:
                    if field not in config:
                        raise ValueError(
                            f"Missing required field '{field}' in {yaml_path}"
                        )

                # Extract tool dependencies
                if "tools" in config:
                    # If tools field is present, use it directly
                    tool_deps = set(config["tools"])
                else:
                    # If tools field is not present, use DEFAULT_TOOLS
                    tool_deps = set(DEFAULT_TOOLS)
                    # Apply any add_tools/remove_tools modifications
                    if "add_tools" in config:
                        tool_deps.update(config["add_tools"])
                    if "remove_tools" in config:
                        tool_deps.difference_update(config["remove_tools"])

                self.tool_configs[config["name"]] = ToolConfig(
                    yaml_path=yaml_path, config=config, dependencies=tool_deps
                )
            except Exception as e:
                print(f"Error loading tool from {yaml_path}: {e}")
                raise

    def check_circular_dependencies(self) -> None:
        """Check for circular dependencies in the tool graph.

        Raises:
            ValueError: If circular dependencies are found
        """

        def visit(tool_name: str, path: Set[str]) -> None:
            if tool_name in path:
                cycle = " -> ".join(list(path) + [tool_name])
                raise ValueError(f"Circular dependency detected: {cycle}")

            if tool_name not in self.tool_configs:
                return  # Built-in tool or already processed

            path.add(tool_name)
            for dep in self.tool_configs[tool_name].dependencies:
                visit(dep, path)
            path.remove(tool_name)

        for tool_name in self.tool_configs:
            visit(tool_name, set())

    def get_instantiation_order(self) -> List[str]:
        """Return a list of tool names in dependency order (leaves first)."""
        result: List[str] = []
        visited: Set[str] = set()

        def visit(tool_name: str) -> None:
            if tool_name in visited or tool_name in self.instantiated_tools:
                return

            if tool_name not in self.tool_configs:
                return  # Built-in tool

            visited.add(tool_name)
            for dep in self.tool_configs[tool_name].dependencies:
                visit(dep)
            result.append(tool_name)

        for tool_name in self.tool_configs:
            visit(tool_name)

        return result

    def instantiate_tool(self, tool_name: str) -> LLMTool:
        """Instantiate a single tool and its dependencies."""
        if tool_name in self.instantiated_tools:
            return self.instantiated_tools[tool_name]

        config = self.tool_configs[tool_name]

        # First ensure all dependencies are instantiated
        available_tools = list(self.builtin_tools)
        for dep in config.dependencies:
            try:
                if dep not in self.instantiated_tools:
                    self.instantiate_tool(dep)
                available_tools.append(self.instantiated_tools[dep])
            except Exception:
                # print(
                #     f"Warning: can't instantiate tool dependency '{dep}' for {tool_name} (this can usually be ignored)"
                # )
                pass

        # Now instantiate this tool
        tool = PromptedLLMAgent.from_yaml(
            yaml_dict=config.config,
            client=self.logging_client,
            tool_call_logger=self.tool_call_logger,
            available_tools=available_tools,
            max_turns=self.max_turns,
            max_output_tokens_per_turn=self.max_output_tokens_per_turn,
            workspace_manager=self.workspace_manager,
            system_prompt_prefix=self.system_prompt,
        )

        self.instantiated_tools[tool_name] = tool
        return tool

    def load_all_tools(self) -> List[LLMTool]:
        """Load all tools in the correct order.

        Returns:
            List of instantiated tools

        Raises:
            ValueError: If circular dependencies are found
        """
        self.load_yaml_configs()
        self.check_circular_dependencies()

        order = self.get_instantiation_order()
        for tool_name in order:
            try:
                self.instantiate_tool(tool_name)
            except Exception as e:
                import traceback

                traceback.print_exc()
                print(f"Warning: failed instantiating tool {tool_name}: {e}")

        return list(self.instantiated_tools.values())
