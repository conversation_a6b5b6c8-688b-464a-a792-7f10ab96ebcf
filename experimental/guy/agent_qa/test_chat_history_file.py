#!/usr/bin/env python3
"""Test script to verify if the /chat-history-proto {path} command creates a file."""

import os
import tempfile
import subprocess
from pathlib import Path


def test_chat_history_proto_file_creation():
    """Test if the /chat-history-proto {path} command creates a file."""
    # Create a temporary directory for the workspace
    with tempfile.TemporaryDirectory() as workspace_dir:
        # Create a temporary file for the chat history
        chat_history_path = os.path.join(workspace_dir, "chat_history.json")

        # Run the agent with the /chat-history-proto command
        cmd = [
            "python3",
            "-W",
            "ignore",
            "interactive_agent.py",
            "-w",
            workspace_dir,
            "-v",
            "-y",
            "-i",
            "ls",
            "-i",
            f"/chat-history-proto {chat_history_path}",
            "-q",
        ]

        # Run the command
        print(f"Running command: {' '.join(cmd)}")
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            cwd=os.path.dirname(os.path.abspath(__file__)),
        )

        # Print the output for debugging
        print(f"Exit code: {result.returncode}")
        print(f"Stdout: {result.stdout}")
        print(f"Stderr: {result.stderr}")

        # Check if the file was created
        file_exists = os.path.exists(chat_history_path)
        print(f"File exists: {file_exists}")

        # If the file exists, print its contents
        if file_exists:
            with open(chat_history_path, "r") as f:
                print(f"File contents: {f.read()}")

        return file_exists


if __name__ == "__main__":
    result = test_chat_history_proto_file_creation()
    print(f"Test result: {'PASSED' if result else 'FAILED'}")
    exit(0 if result else 1)
