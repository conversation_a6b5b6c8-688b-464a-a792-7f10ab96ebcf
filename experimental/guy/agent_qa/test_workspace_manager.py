"""Tests for workspace manager."""

import pytest
from pathlib import Path
from experimental.guy.agent_qa.workspace_manager import WorkspaceManagerImpl
from experimental.guy.agent_qa.prototyping_client import (
    AugmentPrototypingClient,
    UploadContent,
)


class MockAugmentPrototypingClient:
    """Mock client that doesn't make real HTTP requests."""

    def upload_blobs_as_needed(self, content_list: list[UploadContent]):
        """Mock upload that does nothing."""
        pass


@pytest.fixture
def workspace_dir(tmp_path):
    """Create a temporary workspace directory."""
    return tmp_path / "workspace"


@pytest.fixture
def workspace_manager(workspace_dir):
    """Create a workspace manager."""
    workspace_dir.mkdir(parents=True, exist_ok=True)
    return WorkspaceManagerImpl(
        augment_client=MockAugmentPrototypingClient(),
        root=workspace_dir,
    )


def test_revert_to_initial_state_no_snapshots(workspace_manager):
    """Test that reverting to initial state with no snapshots doesn't crash."""
    # Should not raise any exception
    workspace_manager.revert_to_initial_state()


def test_revert_to_initial_state_with_snapshots(workspace_manager, workspace_dir):
    """Test that reverting to initial state works with snapshots."""
    # Create a file and take a snapshot
    test_file = workspace_dir / "test.txt"
    test_file.write_text("initial")
    workspace_manager.update()
    workspace_manager.snapshot_workspace()

    # Modify the file
    test_file.write_text("modified")
    workspace_manager.update()
    workspace_manager.snapshot_workspace()

    # Revert to initial state
    workspace_manager.revert_to_initial_state()

    # Verify file is back to initial state
    assert test_file.read_text() == "initial"
