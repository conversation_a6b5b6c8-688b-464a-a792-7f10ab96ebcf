"""Performance tests for workspace manager."""

import time
from pathlib import Path
import pytest
from unittest.mock import MagicMock
from experimental.guy.agent_qa.workspace_manager import WorkspaceManagerImpl


class MockClient:
    """Mock client that does nothing."""

    def upload_blobs_as_needed(self, blobs):
        pass


@pytest.mark.skip(
    reason="fails in CI, 6.2 sec > 6 sec, but I don't want to use arbitrary time targets anyway, it's flaky"
)
def test_workspace_manager_perf(tmp_path: Path):
    """Test workspace manager performance."""
    # Create a test workspace with many files
    workspace = tmp_path / "workspace"
    workspace.mkdir()

    # Create 10000 Python files in subdirectories
    for i in range(100):  # 100 directories
        subdir = workspace / f"dir_{i}"
        subdir.mkdir()
        for j in range(100):  # 100 files per directory
            file = subdir / f"file_{j}.py"
            file.write_text(f'''"""Module {i}_{j}."""

import os
import sys
from typing import List, Dict, Optional

class Class_{i}_{j}:
    """Class {i}_{j}."""

    def __init__(self):
        """Initialize."""
        self.value = {i} * {j}

    def method_1(self, x: int) -> int:
        """Method 1."""
        return x + self.value

    def method_2(self, y: int) -> int:
        """Method 2."""
        return y * self.value

def function_{i}_{j}(x: int, y: int) -> int:
    """Function {i}_{j}."""
    obj = Class_{i}_{j}()
    return obj.method_1(x) + obj.method_2(y)
''')

    # Also create some non-Python files that should be ignored
    for i in range(100):
        file = workspace / f"file_{i}.bin"
        file.write_bytes(b"binary content\\n" * 100)

    # Create a .gitignore file
    (workspace / ".gitignore").write_text("""
*.bin
__pycache__/
*.pyc
""")

    # Time workspace manager initialization
    start = time.time()
    manager = WorkspaceManagerImpl(
        augment_client=MockClient(),
        root=workspace,
    )
    init_time = time.time() - start

    # Time workspace update
    start = time.time()
    manager.update()
    update_time = time.time() - start

    # Assert reasonable performance
    # Note: initialization includes first scan since we need it for snapshotting
    assert init_time < 6.0, f"Initialization took {init_time:.1f}s"
    assert update_time < 5.0, f"Update took {update_time:.1f}s"


def test_lazy_init_snapshot(tmp_path: Path):
    """Test that lazy initialization doesn't break snapshotting."""
    # Create a test workspace
    workspace = tmp_path / "workspace"
    workspace.mkdir()

    # Create a Python file
    test_file = workspace / "test.py"
    test_file.write_text('''
def function_1():
    """Function 1."""
    return 1
''')

    # Initialize workspace manager
    manager = WorkspaceManagerImpl(
        augment_client=MockClient(),
        root=workspace,
    )

    # Take initial snapshot (before any get_paths/get_blob_names calls)
    snapshot_0 = manager.snapshot_workspace()

    # Modify the file
    test_file.write_text('''
def function_1():
    """Function 1."""
    return 1

def function_2():
    """Function 2."""
    return 2
''')

    # Take another snapshot
    snapshot_1 = manager.snapshot_workspace()

    # Verify we can revert to initial state
    manager.revert_to_snapshot(snapshot_0)
    assert (
        test_file.read_text().strip()
        == '''
def function_1():
    """Function 1."""
    return 1
'''.strip()
    ), "Failed to revert to initial state"

    # Verify we can revert to second state
    manager.revert_to_snapshot(snapshot_1)
    assert (
        test_file.read_text().strip()
        == '''
def function_1():
    """Function 1."""
    return 1

def function_2():
    """Function 2."""
    return 2
'''.strip()
    ), "Failed to revert to second state"
