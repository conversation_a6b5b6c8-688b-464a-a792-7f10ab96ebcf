"""Tests for the agent name generator."""

from experimental.guy.agent_qa.agent_name import generate_agent_name, TOOL_NAMES


def test_generate_agent_name():
    """Test that generate_agent_name returns a valid tool name."""
    # Test multiple times to ensure randomness works
    for _ in range(100):
        name = generate_agent_name()
        assert name in TOOL_NAMES, f"Generated name {name} not in TOOL_NAMES"


def test_tool_names_unique():
    """Test that there are no duplicate tool names in the list."""
    assert len(TOOL_NAMES) == len(set(TOOL_NAMES)), "TOOL_NAMES contains duplicates"


def test_tool_names_not_empty():
    """Test that the tool names list is not empty."""
    assert len(TOOL_NAMES) > 0, "TOOL_NAMES is empty"
