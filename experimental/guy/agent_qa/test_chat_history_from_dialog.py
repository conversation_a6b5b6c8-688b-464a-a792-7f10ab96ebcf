"""Tests for the get_chat_history function with DialogMessages."""

import unittest

from research.agents.tools import DialogMessages, ToolCallLogger
from research.llm_apis.llm_client import <PERSON><PERSON>rom<PERSON>, Text<PERSON><PERSON><PERSON>, Tool<PERSON>all

from experimental.guy.agent_qa.chat_history import get_chat_history


class TestChatHistoryFromDialog(unittest.TestCase):
    """Tests for the get_chat_history function with DialogMessages."""

    def test_simple_conversation(self):
        """Test a simple conversation with just text messages."""
        # Create a DialogMessages object with a simple conversation
        dialog = DialogMessages()

        # Add a user message
        dialog.add_user_prompt("Hello, how are you?")

        # Add an assistant response
        dialog.add_model_response([TextResult("I'm doing well, thank you for asking!")])

        # Add another user message
        dialog.add_user_prompt("What can you help me with today?")

        # Add another assistant response
        dialog.add_model_response(
            [TextResult("I can help you with a variety of tasks.")]
        )

        # Get the chat history
        history = get_chat_history(dialog)

        # Verify the structure of the history
        self.assertIn("chat_history", history)
        self.assertEqual(len(history["chat_history"]), 2)

        # Check first exchange
        first_exchange = history["chat_history"][0]["exchange"]
        self.assertEqual(first_exchange["request_message"], "Hello, how are you?")
        self.assertEqual(
            first_exchange["response_text"], "I'm doing well, thank you for asking!"
        )
        self.assertEqual(len(first_exchange["request_nodes"]), 1)
        self.assertEqual(len(first_exchange["response_nodes"]), 1)
        self.assertEqual(
            first_exchange["request_nodes"][0]["text_node"]["content"],
            "Hello, how are you?",
        )
        self.assertEqual(
            first_exchange["response_nodes"][0]["content"],
            "I'm doing well, thank you for asking!",
        )

        # Check second exchange
        second_exchange = history["chat_history"][1]["exchange"]
        self.assertEqual(
            second_exchange["request_message"], "What can you help me with today?"
        )
        self.assertEqual(
            second_exchange["response_text"], "I can help you with a variety of tasks."
        )
        self.assertEqual(len(second_exchange["request_nodes"]), 1)
        self.assertEqual(len(second_exchange["response_nodes"]), 1)
        self.assertEqual(
            second_exchange["request_nodes"][0]["text_node"]["content"],
            "What can you help me with today?",
        )
        self.assertEqual(
            second_exchange["response_nodes"][0]["content"],
            "I can help you with a variety of tasks.",
        )

    def test_conversation_with_tool_calls(self):
        """Test a conversation with tool calls."""
        # Create a DialogMessages object with a conversation including tool calls
        dialog = DialogMessages()

        # Add a user message
        dialog.add_user_prompt("What files are in the current directory?")

        # Add an assistant response with a tool call
        tool_call = ToolCall(
            tool_call_id="123", tool_name="shell", tool_input={"command": "ls -la"}
        )
        dialog.add_model_response([TextResult("Let me check that for you."), tool_call])

        # Add a tool result
        dialog.add_tool_call_result(
            parameters=tool_call, result="file1.txt\nfile2.py\nfile3.md"
        )

        # Add an assistant response
        dialog.add_model_response(
            [TextResult("I found 3 files: file1.txt, file2.py, and file3.md.")]
        )

        # Get the chat history
        history = get_chat_history(dialog)

        # Verify the structure of the history
        self.assertIn("chat_history", history)
        self.assertEqual(len(history["chat_history"]), 2)

        # Check first exchange
        first_exchange = history["chat_history"][0]["exchange"]
        self.assertEqual(
            first_exchange["request_message"],
            "What files are in the current directory?",
        )
        self.assertEqual(first_exchange["response_text"], "Let me check that for you.")
        self.assertEqual(len(first_exchange["request_nodes"]), 1)
        self.assertEqual(len(first_exchange["response_nodes"]), 2)
        self.assertEqual(first_exchange["response_nodes"][1]["type"], "TOOL_USE")
        self.assertEqual(
            first_exchange["response_nodes"][1]["tool_use"]["tool_name"], "shell"
        )
        self.assertEqual(
            first_exchange["response_nodes"][1]["tool_use"]["input_json"],
            '{"command": "ls -la"}',
        )

        # Check second exchange
        second_exchange = history["chat_history"][1]["exchange"]
        self.assertEqual(len(second_exchange["request_nodes"]), 1)
        self.assertEqual(second_exchange["request_nodes"][0]["type"], "TOOL_RESULT")
        self.assertEqual(
            second_exchange["request_nodes"][0]["tool_result_node"]["content"],
            "file1.txt\nfile2.py\nfile3.md",
        )
        self.assertEqual(
            second_exchange["response_text"],
            "I found 3 files: file1.txt, file2.py, and file3.md.",
        )


if __name__ == "__main__":
    unittest.main()
