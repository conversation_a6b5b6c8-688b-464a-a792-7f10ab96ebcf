"""Tests for tool_loader.py."""

from pathlib import Path
from unittest.mock import Mock
import pytest
import yaml

from research.agents.tools import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>ogger
from research.llm_apis.llm_client import <PERSON><PERSON><PERSON>
from experimental.guy.agent_qa.tool_loader import Too<PERSON><PERSON><PERSON><PERSON>


class MockTool(LLMTool):
    """A mock tool for testing."""

    def __init__(self, name: str):
        super().__init__(tool_call_logger=Mock())
        self.name = name
        self.description = f"Mock tool {name}"
        self.input_schema = {
            "type": "object",
            "properties": {
                "test_input": {"type": "string", "description": "Test input"}
            },
            "required": ["test_input"],
        }


def create_yaml_file(tmp_path: Path, name: str, dependencies: list[str]) -> Path:
    """Create a YAML file for testing."""
    config = {
        "name": name,
        "description": f"Test tool {name}",
        "input_schema": {
            "type": "object",
            "properties": {
                "test_input": {"type": "string", "description": "Test input"}
            },
            "required": ["test_input"],
        },
        "prompt": "Test prompt for {{ test_input }}",
        "tools": dependencies,
    }

    path = tmp_path / f"{name}.yaml"
    with open(path, "w") as f:
        yaml.dump(config, f)
    return path


def test_load_single_tool(tmp_path):
    """Test loading a single tool with no dependencies."""
    yaml_path = create_yaml_file(tmp_path, "tool1", [])

    mock_client = Mock(spec=LLMClient)
    mock_logger = Mock(spec=ToolCallLogger)
    builtin_tools = [MockTool("builtin1")]

    loader = ToolLoader(
        yaml_files=[yaml_path],
        logging_client=mock_client,
        tool_call_logger=mock_logger,
        builtin_tools=builtin_tools,
    )

    tools = loader.load_all_tools()
    assert len(tools) == 2  # builtin + new tool
    assert {t.name for t in tools} == {"builtin1", "tool1"}


def test_load_linear_dependency_chain(tmp_path):
    """Test loading tools with linear dependencies."""
    # tool3 -> tool2 -> tool1
    yaml1 = create_yaml_file(tmp_path, "tool1", [])
    yaml2 = create_yaml_file(tmp_path, "tool2", ["tool1"])
    yaml3 = create_yaml_file(tmp_path, "tool3", ["tool2"])

    mock_client = Mock(spec=LLMClient)
    mock_logger = Mock(spec=ToolCallLogger)
    builtin_tools = []

    loader = ToolLoader(
        yaml_files=[yaml3, yaml1, yaml2],  # Intentionally out of order
        logging_client=mock_client,
        tool_call_logger=mock_logger,
        builtin_tools=builtin_tools,
    )

    tools = loader.load_all_tools()
    assert len(tools) == 3

    # Check instantiation order by looking at the order in the list
    tool_names = [t.name for t in tools]
    assert tool_names.index("tool1") < tool_names.index("tool2")
    assert tool_names.index("tool2") < tool_names.index("tool3")


def test_detect_circular_dependency(tmp_path):
    """Test that circular dependencies are detected."""
    # tool1 -> tool2 -> tool3 -> tool1
    yaml1 = create_yaml_file(tmp_path, "tool1", ["tool2"])
    yaml2 = create_yaml_file(tmp_path, "tool2", ["tool3"])
    yaml3 = create_yaml_file(tmp_path, "tool3", ["tool1"])

    mock_client = Mock(spec=LLMClient)
    mock_logger = Mock(spec=ToolCallLogger)
    builtin_tools = []

    loader = ToolLoader(
        yaml_files=[yaml1, yaml2, yaml3],
        logging_client=mock_client,
        tool_call_logger=mock_logger,
        builtin_tools=builtin_tools,
    )

    with pytest.raises(ValueError) as exc_info:
        loader.load_all_tools()
    assert "Circular dependency" in str(exc_info.value)


def test_missing_required_field(tmp_path):
    """Test that missing required fields are detected."""
    # Create invalid YAML missing the 'prompt' field
    config = {
        "name": "invalid_tool",
        "description": "Test tool",
        "input_schema": {
            "type": "object",
            "properties": {
                "test_input": {"type": "string", "description": "Test input"}
            },
            "required": ["test_input"],
        },
    }

    yaml_path = tmp_path / "invalid.yaml"
    with open(yaml_path, "w") as f:
        yaml.dump(config, f)

    mock_client = Mock(spec=LLMClient)
    mock_logger = Mock(spec=ToolCallLogger)
    builtin_tools = []

    loader = ToolLoader(
        yaml_files=[yaml_path],
        logging_client=mock_client,
        tool_call_logger=mock_logger,
        builtin_tools=builtin_tools,
    )

    with pytest.raises(ValueError) as exc_info:
        loader.load_all_tools()
    assert "Missing required field 'prompt'" in str(exc_info.value)


def test_dependency_on_builtin_tool(tmp_path):
    """Test that tools can depend on builtin tools."""
    yaml_path = create_yaml_file(tmp_path, "tool1", ["builtin1"])

    mock_client = Mock(spec=LLMClient)
    mock_logger = Mock(spec=ToolCallLogger)
    builtin_tools = [MockTool("builtin1")]

    loader = ToolLoader(
        yaml_files=[yaml_path],
        logging_client=mock_client,
        tool_call_logger=mock_logger,
        builtin_tools=builtin_tools,
    )

    tools = loader.load_all_tools()
    assert len(tools) == 2
    assert {t.name for t in tools} == {"builtin1", "tool1"}


def test_diamond_dependencies(tmp_path):
    """Test diamond-shaped dependencies work correctly."""
    # tool4 depends on tool2 and tool3
    # both tool2 and tool3 depend on tool1
    yaml1 = create_yaml_file(tmp_path, "tool1", [])
    yaml2 = create_yaml_file(tmp_path, "tool2", ["tool1"])
    yaml3 = create_yaml_file(tmp_path, "tool3", ["tool1"])
    yaml4 = create_yaml_file(tmp_path, "tool4", ["tool2", "tool3"])

    mock_client = Mock(spec=LLMClient)
    mock_logger = Mock(spec=ToolCallLogger)
    builtin_tools = []

    loader = ToolLoader(
        yaml_files=[yaml4, yaml2, yaml1, yaml3],  # Intentionally out of order
        logging_client=mock_client,
        tool_call_logger=mock_logger,
        builtin_tools=builtin_tools,
    )

    tools = loader.load_all_tools()
    assert len(tools) == 4

    # Check instantiation order
    tool_names = [t.name for t in tools]
    assert tool_names.index("tool1") < tool_names.index("tool2")
    assert tool_names.index("tool1") < tool_names.index("tool3")
    assert tool_names.index("tool2") < tool_names.index("tool4")
    assert tool_names.index("tool3") < tool_names.index("tool4")
