"""Integration of ToolCallLogger with StateManager.

This module provides a listener implementation that connects ToolCallLogger to StateManager.
"""

from typing import Optional, List, Any

from research.agents.tools import Tool<PERSON>allLogger, ToolCallLoggerUpdateListener
from experimental.guy.agent_qa.state_manager import StateManager


class StateManagerToolCallLoggerListener(ToolCallLoggerUpdateListener):
    """A listener that saves ToolCallLogger state to a StateManager.

    This class implements the ToolCallLoggerUpdateListener interface and saves the
    ToolCallLogger state to a StateManager whenever the ToolCallLogger is updated.
    """

    def __init__(self, state_manager: StateManager):
        """Initialize a StateManagerToolCallLoggerListener.

        Args:
            state_manager: The StateManager to save state to.
        """
        self.state_manager = state_manager

    def on_tool_call_logger_update(self, tool_call_logger: ToolCallLogger) -> None:
        """Save the ToolCallLogger state to the StateManager.

        Args:
            tool_call_logger: The updated ToolCallLogger object.
        """
        save_tool_call_logger_state(tool_call_logger, self.state_manager)


def save_tool_call_logger_state(
    tool_call_logger: ToolCallLogger, state_manager: StateManager
) -> None:
    """Save the state of a ToolCallLogger to a StateManager.

    Args:
        tool_call_logger: The ToolCallLogger to save state from.
        state_manager: The StateManager to save state to.
    """
    state_manager.update("logged_calls", tool_call_logger.logged_calls)


def load_tool_call_logger_state(
    tool_call_logger: ToolCallLogger, state_manager: StateManager
) -> bool:
    """Load the state of a ToolCallLogger from a StateManager.

    Args:
        tool_call_logger: The ToolCallLogger to load state into.
        state_manager: The StateManager to load state from.

    Returns:
        True if state was loaded successfully, False otherwise.
    """
    logged_calls = state_manager.get("logged_calls", None)
    if logged_calls is not None:
        tool_call_logger.logged_calls = logged_calls
        return True
    return False
