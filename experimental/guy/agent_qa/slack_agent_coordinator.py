"""Slack-based agent coordinator for synchronizing agent activities.

This module provides a coordinator that uses Slack as a backend for agent communication.
Agents can broadcast their activities to a channel and read messages from other agents.
"""

from datetime import datetime
from typing import Dict, List, Optional

from slack_sdk import WebClient
from slack_sdk.errors import SlackApiError

from experimental.guy.agent_qa.agent_coordinator_interface import (
    AgentCoordinator,
    AgentMessage,
)


class SlackAgentCoordinator(AgentCoordinator):
    """Coordinates agent communication through a Slack channel."""

    def __init__(self, bot_token: str, channel_name: str):
        """Initialize the coordinator.

        Args:
            bot_token: Slack bot token (xoxb-...)
            channel_name: Name of the Slack channel to use (without the #)
        """
        self.client = WebClient(token=bot_token)
        self.channel_name = channel_name
        self._channel_id = None
        self._current_agent_name: Optional[str] = None
        self._seen_messages: Dict[
            str, datetime
        ] = {}  # Track seen messages by timestamp

    def _get_all_channels(self) -> List[dict]:
        """Get all channels using pagination.

        Returns:
            List of channel objects

        Raises:
            ValueError: If channels cannot be retrieved
        """
        channels = []
        next_cursor = None

        while True:
            try:
                # Get next page of channels
                if next_cursor:
                    response = self.client.conversations_list(
                        cursor=next_cursor, limit=1000
                    )
                else:
                    response = self.client.conversations_list(limit=1000)

                # Add channels from this page
                channels.extend(response["channels"])

                # Get cursor for next page
                next_cursor = response["response_metadata"].get("next_cursor")

                # If no cursor returned, we've reached the end
                if not next_cursor:
                    break

            except SlackApiError as e:
                raise ValueError(f"Error accessing Slack: {e.response['error']}")

        return channels

    def _get_channel_id(self) -> str:
        """Get the channel ID for the configured channel name.

        Returns:
            The channel ID

        Raises:
            ValueError: If channel not found or not accessible
        """
        if self._channel_id is not None:
            return self._channel_id

        try:
            # Get all channels with pagination
            channels = self._get_all_channels()

            # Find our channel
            for channel in channels:
                if channel["name"] == self.channel_name:
                    self._channel_id = channel["id"]
                    return self._channel_id

            raise ValueError(f"Channel #{self.channel_name} not found")

        except SlackApiError as e:
            raise ValueError(f"Error accessing Slack: {e.response['error']}")

    def notify_current_action(self, agent_name: str, message: str) -> None:
        """Let other agents know what this agent is currently doing.

        Args:
            agent_name: Name of the agent sending the message
            message: Description of what the agent is currently doing

        Raises:
            ValueError: If message cannot be sent
        """
        # Store the current agent name for get_messages()
        self._current_agent_name = agent_name

        channel_id = self._get_channel_id()
        try:
            # Format message with agent name
            formatted_message = f"*{agent_name}*: {message}"
            self.client.chat_postMessage(
                channel=channel_id,
                text=formatted_message,
                unfurl_links=False,
                unfurl_media=False,
            )
        except SlackApiError as e:
            raise ValueError(f"Error sending message: {e.response['error']}")

    def get_messages(self) -> str:
        """Get all pending messages as a single string.

        Returns:
            A string containing all pending messages, or empty string if none

        Raises:
            ValueError: If error retrieving messages
        """
        if not self._current_agent_name:
            return ""  # No messages if no agent name set

        try:
            # Get channel history
            channel_id = self._get_channel_id()
            response = self.client.conversations_history(
                channel=channel_id,
                limit=10,  # Get last 10 messages
            )

            # Filter messages from other agents
            messages = []
            for msg in response[
                "messages"
            ]:  # Messages are already in reverse chronological order
                text = msg.get("text", "")
                # Skip messages from the current agent
                if text.startswith(f"*{self._current_agent_name}*:"):
                    continue
                # Only include messages in agent format
                if text.startswith("*") and "*:" in text:
                    messages.append(text)

            if not messages:
                return ""

            # Add coordination guidance when there are messages
            guidance = """\
# Code Coordination Required
Other agents are working on code changes. To prevent merge conflicts:
1. Review their planned changes to understand potential overlaps
2. If you plan to work on the same files/components:
   - Coordinate through messages to avoid conflicting changes
   - Consider sequencing the changes or splitting the work
   - Notify them about your planned changes
3. Keep monitoring messages for updates about their progress

Recent messages from other agents:
"""
            # Return messages with guidance, in the order they were received (newest first)
            return guidance + "\n" + "\n".join(messages)

        except SlackApiError as e:
            raise ValueError(f"Error retrieving messages: {e.response['error']}")
