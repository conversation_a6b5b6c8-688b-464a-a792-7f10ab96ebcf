{"instruction": "move DialogMessages class to tools.py in experimental/guy/agent_qa . only move the class, do not make unrelated changes.", "commit": "d2730111f30ec5bdcb728e95fdf0b1b4857eaa38", "changes": "diff --git a/experimental/guy/agent_qa/agent.py b/experimental/guy/agent_qa/agent.py\nindex e57a145bd3..9f76f75cae 100644\n--- a/experimental/guy/agent_qa/agent.py\n+++ b/experimental/guy/agent_qa/agent.py\n@@ -2,7 +2,7 @@ import json\n from dataclasses import dataclass\n from typing import Any, Tuple\n \n-from research.agents.tools import ToolCallLogger\n+from research.agents.tools import ToolCallLogger, DialogMessages\n from experimental.guy.agent_qa.builtin_tools import CompleteTool, LLMTool\n from experimental.michiel.research.agentqa.tools import (\n     AugmentToolCall,\n@@ -25,112 +25,6 @@ class ToolCallParameters:\n     tool_input: Any\n \n \n-class DialogMessages:\n-    \"\"\"Keeps track of messages that compose a dialog.\n-\n-    A dialog alternates between user and assistant turns. Each turn consists\n-    of one or more messages, represented by GeneralContentBlock.\n-\n-    A user turn consists of one or more prompts and tool results.\n-    An assistant turn consists of a model answer and tool calls.\n-    \"\"\"\n-\n-    def __init__(self):\n-        self._message_lists: list[list[GeneralContentBlock]] = []\n-\n-    def add_user_prompt(self, message: str, allow_append_to_tool_calls: bool = False):\n-        \"\"\"Add a user prompt to the dialog.\n-\n-        Args:\n-            message: The message to add.\n-            allow_append_to_tool_calls: If True, the message will be appended to the\n-                last tool call if it is a tool call.\n-        \"\"\"\n-        if self.is_user_turn():\n-            self._message_lists.append([AugmentTextPrompt(message)])\n-        else:\n-            if allow_append_to_tool_calls:\n-                user_messages = self._message_lists[-1]\n-                for user_message in user_messages:\n-                    if isinstance(user_message, AugmentTextPrompt):\n-                        raise ValueError(\n-                            f\"Last user turn already contains a text prompt: {user_message}\"\n-                        )\n-                user_messages.append(AugmentTextPrompt(message))\n-            else:\n-                self._assert_user_turn()\n-\n-    def add_tool_call_result(self, parameters: ToolCallParameters, result: str):\n-        \"\"\"Add the result of a tool call to the dialog.\"\"\"\n-        self._assert_user_turn()\n-        self._message_lists.append(\n-            [\n-                AugmentToolFormattedResult(\n-                    tool_call_id=parameters.tool_call_id,\n-                    tool_name=parameters.tool_name,\n-                    tool_output=result,\n-                ),\n-            ]\n-        )\n-\n-    def add_model_response(self, response: list[AssistantContentBlock]):\n-        \"\"\"Add the result of a model call to the dialog.\"\"\"\n-        self._assert_assistant_turn()\n-        self._message_lists.append(response)\n-\n-    def get_messages_for_llm_client(self) -> list[list[GeneralContentBlock]]:\n-        \"\"\"Returns messages in the format the LM client expects.\"\"\"\n-        return list(self._message_lists)\n-\n-    def get_pending_tool_calls(self) -> list[ToolCallParameters]:\n-        \"\"\"Returns the tool calls from the last assistant turn.\n-\n-        Returns an empty list of no tool calls are pending.\n-        \"\"\"\n-        self._assert_user_turn()\n-        if len(self._message_lists) == 0:\n-            return []\n-        tool_calls = []\n-        for message in self._message_lists[-1]:\n-            if isinstance(message, AugmentToolCall):\n-                tool_calls.append(\n-                    ToolCallParameters(\n-                        tool_call_id=message.tool_call_id,\n-                        tool_name=message.tool_name,\n-                        tool_input=message.tool_input,\n-                    )\n-                )\n-        return tool_calls\n-\n-    def get_last_model_text_response(self):\n-        \"\"\"Returns the last model response as a string.\"\"\"\n-        self._assert_user_turn()\n-        for message in self._message_lists[-1]:\n-            if isinstance(message, AugmentTextResult):\n-                return message.text\n-        raise ValueError(\"No text response found in last model response\")\n-\n-    def clear(self):\n-        \"\"\"Delete all messages.\"\"\"\n-        self._message_lists = []\n-\n-    def is_user_turn(self):\n-        return len(self._message_lists) % 2 == 0\n-\n-    def is_assistant_turn(self):\n-        return len(self._message_lists) % 2 == 1\n-\n-    def _assert_user_turn(self):\n-        assert (\n-            self.is_user_turn()\n-        ), \"Trying to add user message but it is the assistant's turn\"\n-\n-    def _assert_assistant_turn(self):\n-        assert (\n-            self.is_assistant_turn()\n-        ), \"Trying to add assistant message but it is the user's turn\"\n-\n-\n class Agent(LLMTool):\n     name = \"general_agent\"\n     description = \"\"\"\\\ndiff --git a/experimental/guy/agent_qa/tools.py b/experimental/guy/agent_qa/tools.py\nindex ee65464d71..e28ebb2fae 100644\n--- a/experimental/guy/agent_qa/tools.py\n+++ b/experimental/guy/agent_qa/tools.py\n@@ -22,6 +22,112 @@ ToolInputSchema = dict[str, Any]\n \"\"\"A JSON schema describing the input to a tool.\"\"\"\n \n \n+class DialogMessages:\n+    \"\"\"Keeps track of messages that compose a dialog.\n+\n+    A dialog alternates between user and assistant turns. Each turn consists\n+    of one or more messages, represented by GeneralContentBlock.\n+\n+    A user turn consists of one or more prompts and tool results.\n+    An assistant turn consists of a model answer and tool calls.\n+    \"\"\"\n+\n+    def __init__(self):\n+        self._message_lists: list[list[GeneralContentBlock]] = []\n+\n+    def add_user_prompt(self, message: str, allow_append_to_tool_calls: bool = False):\n+        \"\"\"Add a user prompt to the dialog.\n+\n+        Args:\n+            message: The message to add.\n+            allow_append_to_tool_calls: If True, the message will be appended to the\n+                last tool call if it is a tool call.\n+        \"\"\"\n+        if self.is_user_turn():\n+            self._message_lists.append([AugmentTextPrompt(message)])\n+        else:\n+            if allow_append_to_tool_calls:\n+                user_messages = self._message_lists[-1]\n+                for user_message in user_messages:\n+                    if isinstance(user_message, AugmentTextPrompt):\n+                        raise ValueError(\n+                            f\"Last user turn already contains a text prompt: {user_message}\"\n+                        )\n+                user_messages.append(AugmentTextPrompt(message))\n+            else:\n+                self._assert_user_turn()\n+\n+    def add_tool_call_result(self, parameters: \"ToolCallParameters\", result: str):\n+        \"\"\"Add the result of a tool call to the dialog.\"\"\"\n+        self._assert_user_turn()\n+        self._message_lists.append(\n+            [\n+                AugmentToolFormattedResult(\n+                    tool_call_id=parameters.tool_call_id,\n+                    tool_name=parameters.tool_name,\n+                    tool_output=result,\n+                ),\n+            ]\n+        )\n+\n+    def add_model_response(self, response: list[AssistantContentBlock]):\n+        \"\"\"Add the result of a model call to the dialog.\"\"\"\n+        self._assert_assistant_turn()\n+        self._message_lists.append(response)\n+\n+    def get_messages_for_llm_client(self) -> list[list[GeneralContentBlock]]:\n+        \"\"\"Returns messages in the format the LM client expects.\"\"\"\n+        return list(self._message_lists)\n+\n+    def get_pending_tool_calls(self) -> list[\"ToolCallParameters\"]:\n+        \"\"\"Returns the tool calls from the last assistant turn.\n+\n+        Returns an empty list of no tool calls are pending.\n+        \"\"\"\n+        self._assert_user_turn()\n+        if len(self._message_lists) == 0:\n+            return []\n+        tool_calls = []\n+        for message in self._message_lists[-1]:\n+            if isinstance(message, AugmentToolCall):\n+                tool_calls.append(\n+                    ToolCallParameters(\n+                        tool_call_id=message.tool_call_id,\n+                        tool_name=message.tool_name,\n+                        tool_input=message.tool_input,\n+                    )\n+                )\n+        return tool_calls\n+\n+    def get_last_model_text_response(self):\n+        \"\"\"Returns the last model response as a string.\"\"\"\n+        self._assert_user_turn()\n+        for message in self._message_lists[-1]:\n+            if isinstance(message, AugmentTextResult):\n+                return message.text\n+        raise ValueError(\"No text response found in last model response\")\n+\n+    def clear(self):\n+        \"\"\"Delete all messages.\"\"\"\n+        self._message_lists = []\n+\n+    def is_user_turn(self):\n+        return len(self._message_lists) % 2 == 0\n+\n+    def is_assistant_turn(self):\n+        return len(self._message_lists) % 2 == 1\n+\n+    def _assert_user_turn(self):\n+        assert (\n+            self.is_user_turn()\n+        ), \"Trying to add user message but it is the assistant's turn\"\n+\n+    def _assert_assistant_turn(self):\n+        assert (\n+            self.is_assistant_turn()\n+        ), \"Trying to add assistant message but it is the user's turn\"\n+\n+\n class Tool:\n     \"\"\"A tool that can be called by an LLM.\n \n"}