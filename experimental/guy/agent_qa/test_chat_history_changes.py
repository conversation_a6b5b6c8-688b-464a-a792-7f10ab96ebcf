#!/usr/bin/env python3
"""
Test the changes to the chat_history method in CLIInterface.
"""

import json

from experimental.guy.agent_qa.chat_history import (
    _process_llm_call_messages,
    _process_llm_call_response,
)
from research.llm_apis.llm_client import Text<PERSON>rom<PERSON>, TextResult, ToolCall
from research.agents.tools import LoggedLanguageModelCall
from services.api_proxy.public_api_pb2 import Exchange


def get_logged_llm_call(started=True, messages=None, response=None):
    return LoggedLanguageModelCall(
        started=started,
        messages=messages or [],
        max_tokens=1000,
        system_prompt="You are a helpful assistant.",
        temperature=0.0,
        tools=[],
        tool_choice=None,
        response=response,
        response_metadata={},
    )


# Test the methods
def test_process_llm_call_messages():
    # Create a test exchange
    exchange = Exchange()

    # Create a test LLM call with a text message
    messages = [[TextPrompt(text="Hello, world!")]]
    call = get_logged_llm_call(started=True, messages=messages)

    # Process the messages
    _process_llm_call_messages(call, exchange)

    # Verify that the request node was added with the correct ID
    assert len(exchange.request_nodes) == 1
    assert exchange.request_nodes[0].id == 0
    assert exchange.request_nodes[0].text_node.content == "Hello, world!"

    # Add another message
    messages = [[TextPrompt(text="How are you?")]]
    call = get_logged_llm_call(started=True, messages=messages)

    # Process the messages
    _process_llm_call_messages(call, exchange)

    # Verify that the second request node was added with the correct ID
    assert len(exchange.request_nodes) == 2
    assert exchange.request_nodes[1].id == 1
    assert exchange.request_nodes[1].text_node.content == "How are you?"


def test_process_llm_call_response():
    # Create a test exchange
    exchange = Exchange()

    # Create a test LLM call with a text response
    response = [TextResult(text="I'm fine, thank you!")]
    call = get_logged_llm_call(started=False, response=response)

    # Process the response
    _process_llm_call_response(call, exchange)

    # Verify that the response node was added with the correct ID
    assert len(exchange.response_nodes) == 1
    assert exchange.response_nodes[0].id == 0
    assert exchange.response_nodes[0].content == "I'm fine, thank you!"

    # Add another response
    response = [
        ToolCall(
            tool_call_id="123", tool_name="test_tool", tool_input={"param": "value"}
        )
    ]
    call = get_logged_llm_call(started=False, response=response)

    # Process the response
    _process_llm_call_response(call, exchange)

    # Verify that the second response node was added with the correct ID
    assert len(exchange.response_nodes) == 2
    assert exchange.response_nodes[1].id == 1
    assert exchange.response_nodes[1].tool_use.tool_use_id == "123"
    assert exchange.response_nodes[1].tool_use.tool_name == "test_tool"


if __name__ == "__main__":
    test_process_llm_call_messages()
    test_process_llm_call_response()
    print("All tests passed!")
