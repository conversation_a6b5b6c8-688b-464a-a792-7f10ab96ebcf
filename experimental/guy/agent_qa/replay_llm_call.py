import argparse
import pickle
from pathlib import Path

from research.agents.tools import LoggedLanguageModelCall
from research.llm_apis.llm_client import (
    # LLMClient,
    AnthropicVertexClient,
)


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--pickle_log_file",
        type=str,
        required=True,
        help="Path to the log file",
    )

    args = parser.parse_args()
    log_items = pickle.load(open(args.pickle_log_file, "rb"))

    anthropic_client = AnthropicVertexClient(model_name="claude-3-5-sonnet-v2@20241022")

    last_lm_call = None
    for item in reversed(log_items):
        if isinstance(item, LoggedLanguageModelCall):
            last_lm_call = item
            break

    if not last_lm_call:
        raise ValueError("No LLM call found in log file")

    print("LLM call")
    print(last_lm_call)
    print()

    print("Replaying LLM call")
    response = anthropic_client.generate(
        messages=last_lm_call.messages,
        max_tokens=last_lm_call.max_tokens,
        system_prompt=last_lm_call.system_prompt,
        temperature=last_lm_call.temperature,
        tools=last_lm_call.tools,
        tool_choice=last_lm_call.tool_choice,
    )

    print(response)


if __name__ == "__main__":
    main()
