#!/usr/bin/env python3
"""
Test the chat_history method in CLIInterface.
"""

import json
import sys
from pathlib import Path

from research.agents.tools import (
    ToolCallLogger,
    LoggedLanguageModelCall,
    LoggedToolCall,
    ToolFormattedResult,
)
from research.llm_apis.llm_client import Text<PERSON>rom<PERSON>, TextResult, ToolCall
from chat_history import get_chat_history


def create_mock_llm_call(started=True, text="Hello", response_text=None):
    """Create a mock LLM call."""
    messages = [[TextPrompt(text=text)]]
    response = [TextResult(text=response_text)] if response_text else None
    return LoggedLanguageModelCall(
        started=started,
        messages=messages,
        max_tokens=1000,
        system_prompt="You are a helpful assistant.",
        temperature=0.0,
        tools=[],
        tool_choice=None,
        response=response,
        response_metadata={},
    )


def create_mock_tool_call(
    started=True, tool_name="test_tool", tool_input=None, tool_output=None
):
    """Create a mock tool call."""
    from research.agents.tools import ToolParam

    tool = ToolParam(
        name=tool_name,
        description="A test tool",
        input_schema={"type": "object", "properties": {}},
    )
    return LoggedToolCall(
        started=started,
        tool=tool,
        tool_input=tool_input or {},
        tool_output=tool_output,
        tool_message="Test tool message",
    )


def main():
    """Test the chat_history method."""
    # Create a tool call logger
    tool_call_logger = ToolCallLogger()

    # Add some logged calls
    # Top-level agent start
    tool_call_logger.logged_calls.append(
        create_mock_tool_call(started=True, tool_name="agent")
    )

    # First LLM call (user message and response)
    tool_call_logger.logged_calls.append(
        create_mock_llm_call(started=True, text="Hello, I need help with Python.")
    )
    tool_call_logger.logged_calls.append(
        create_mock_llm_call(
            started=False,
            text="Hello, I need help with Python.",
            response_text="I can help you with Python. What do you need?",
        )
    )

    # Tool call within the agent
    tool_call_logger.logged_calls.append(
        create_mock_tool_call(
            started=True,
            tool_name="web_search",
            tool_input={"query": "Python tutorial"},
        )
    )
    tool_call_logger.logged_calls.append(
        create_mock_tool_call(
            started=False,
            tool_name="web_search",
            tool_input={"query": "Python tutorial"},
            tool_output="Found several Python tutorials...",
        )
    )

    # Second LLM call with tool result
    messages = [[TextPrompt(text="Show me the search results.")]]
    response = [
        ToolCall(
            tool_call_id="tool_1",
            tool_name="web_search",
            tool_input={"query": "Python tutorial"},
        )
    ]
    tool_call_logger.logged_calls.append(
        LoggedLanguageModelCall(
            started=True,
            messages=messages,
            max_tokens=1000,
            system_prompt="You are a helpful assistant.",
            temperature=0.0,
            tools=[],
            tool_choice=None,
            response=None,
            response_metadata={},
        )
    )
    tool_call_logger.logged_calls.append(
        LoggedLanguageModelCall(
            started=False,
            messages=messages,
            max_tokens=1000,
            system_prompt="You are a helpful assistant.",
            temperature=0.0,
            tools=[],
            tool_choice=None,
            response=response,
            response_metadata={},
        )
    )

    # Top-level agent end
    tool_call_logger.logged_calls.append(
        create_mock_tool_call(started=False, tool_name="agent")
    )

    # Get the chat history
    history = get_chat_history(tool_call_logger)

    # Print the chat history
    print(json.dumps(history, indent=2))

    # Verify that the chat history has the expected structure
    assert "chat_history" in history, "chat_history field missing"
    assert len(history["chat_history"]) > 0, "chat_history is empty"

    print("\nTest passed!")
    return 0


if __name__ == "__main__":
    sys.exit(main())
