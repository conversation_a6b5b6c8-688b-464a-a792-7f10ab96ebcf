"""Tests for the Slack-based agent coordinator."""

from unittest.mock import patch

import pytest
from slack_sdk.errors import Slack<PERSON><PERSON><PERSON>rror

from experimental.guy.agent_qa.agent_coordinator_interface import AgentCoordinator
from experimental.guy.agent_qa.slack_agent_coordinator import SlackAgentCoordinator


@pytest.fixture
def mock_slack_client():
    """Create a mock Slack client."""
    with patch("slack_sdk.WebClient") as mock_client:
        yield mock_client.return_value


def test_get_channel_id(mock_slack_client):
    """Test getting channel ID."""
    # Setup mock response
    mock_slack_client.conversations_list.return_value = {
        "channels": [
            {"name": "test-channel", "id": "C123"},
            {"name": "other-channel", "id": "C456"},
        ],
        "response_metadata": {"next_cursor": ""},
    }

    coordinator = SlackAgentCoordinator("xoxb-test-token", "test-channel")
    coordinator.client = mock_slack_client

    # First call should find the channel
    channel_id = coordinator._get_channel_id()
    assert channel_id == "C123"

    # Second call should use cached value
    channel_id = coordinator._get_channel_id()
    assert channel_id == "C123"
    mock_slack_client.conversations_list.assert_called_once()


def test_get_channel_id_not_found(mock_slack_client):
    """Test error when channel not found."""
    mock_slack_client.conversations_list.return_value = {
        "channels": [{"name": "other-channel", "id": "C456"}],
        "response_metadata": {"next_cursor": ""},
    }

    coordinator = SlackAgentCoordinator("xoxb-test-token", "test-channel")
    coordinator.client = mock_slack_client

    with pytest.raises(ValueError, match="Channel #test-channel not found"):
        coordinator._get_channel_id()


def test_get_channel_id_api_error(mock_slack_client):
    """Test handling of Slack API errors."""
    mock_slack_client.conversations_list.side_effect = SlackApiError(
        "Error", {"error": "invalid_auth"}
    )

    coordinator = SlackAgentCoordinator("xoxb-test-token", "test-channel")
    coordinator.client = mock_slack_client

    with pytest.raises(ValueError, match="Error accessing Slack: invalid_auth"):
        coordinator._get_channel_id()


def test_notify_current_action(mock_slack_client):
    """Test notifying current action."""
    # Setup mock response for channel lookup
    mock_slack_client.conversations_list.return_value = {
        "channels": [{"name": "test-channel", "id": "C123"}],
        "response_metadata": {"next_cursor": ""},
    }

    coordinator = SlackAgentCoordinator("xoxb-test-token", "test-channel")
    coordinator.client = mock_slack_client

    # Test sending a message
    coordinator.notify_current_action("TestAgent", "Hello world")

    # Verify the message was sent correctly
    mock_slack_client.chat_postMessage.assert_called_once_with(
        channel="C123",
        text="*TestAgent*: Hello world",
        unfurl_links=False,
        unfurl_media=False,
    )


def test_notify_current_action_error(mock_slack_client):
    """Test error handling when notification fails."""
    # Setup mock response for channel lookup
    mock_slack_client.conversations_list.return_value = {
        "channels": [{"name": "test-channel", "id": "C123"}],
        "response_metadata": {"next_cursor": ""},
    }

    # Setup error for message sending
    mock_slack_client.chat_postMessage.side_effect = SlackApiError(
        "Error", {"error": "channel_not_found"}
    )

    coordinator = SlackAgentCoordinator("xoxb-test-token", "test-channel")
    coordinator.client = mock_slack_client

    with pytest.raises(ValueError, match="Error sending message: channel_not_found"):
        coordinator.notify_current_action("TestAgent", "Hello world")


def test_get_messages(mock_slack_client):
    """Test getting messages."""
    # Setup mock response for channel lookup
    mock_slack_client.conversations_list.return_value = {
        "channels": [{"name": "test-channel", "id": "C123"}],
        "response_metadata": {"next_cursor": ""},
    }

    # Setup mock response for message history
    mock_slack_client.conversations_history.return_value = {
        "messages": [
            {
                "text": "*Agent1*: Message 1",
                "ts": "1612345678.123",
            },
            {
                "text": "*Agent2*: Message 2",
                "ts": "1612345677.123",
            },
            {
                "text": "*TestAgent*: My message",  # Should be filtered out
                "ts": "1612345676.123",
            },
            {
                "text": "Invalid message format",  # Should be filtered out
                "ts": "1612345675.123",
            },
        ]
    }

    coordinator = SlackAgentCoordinator("xoxb-test-token", "test-channel")
    coordinator.client = mock_slack_client

    # Should return empty string when no agent name is set
    assert coordinator.get_messages() == ""

    # Set current agent name via notify_current_action
    coordinator.notify_current_action("TestAgent", "My message")

    # Get messages
    messages = coordinator.get_messages()

    # Verify messages were returned correctly - messages should be in reverse chronological order
    # and include coordination guidance when there are messages
    expected_guidance = """\
# Code Coordination Required
Other agents are working on code changes. To prevent merge conflicts:
1. Review their planned changes to understand potential overlaps
2. If you plan to work on the same files/components:
   - Coordinate through messages to avoid conflicting changes
   - Consider sequencing the changes or splitting the work
   - Notify them about your planned changes
3. Keep monitoring messages for updates about their progress

Recent messages from other agents:"""
    expected_messages = "*Agent1*: Message 1\n*Agent2*: Message 2"
    assert messages == expected_guidance + "\n\n" + expected_messages


def test_get_messages_error(mock_slack_client):
    """Test error handling when getting messages fails."""
    # Setup mock response for channel lookup
    mock_slack_client.conversations_list.return_value = {
        "channels": [{"name": "test-channel", "id": "C123"}],
        "response_metadata": {"next_cursor": ""},
    }

    # Setup error for history retrieval
    mock_slack_client.conversations_history.side_effect = SlackApiError(
        "Error", {"error": "not_in_channel"}
    )

    coordinator = SlackAgentCoordinator("xoxb-test-token", "test-channel")
    coordinator.client = mock_slack_client

    # Set current agent name via notify_current_action
    coordinator.notify_current_action("TestAgent", "My message")

    with pytest.raises(ValueError, match="Error retrieving messages: not_in_channel"):
        coordinator.get_messages()
