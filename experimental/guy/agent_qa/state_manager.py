"""State management for interactive agent.

This module provides a StateManager class that manages the agent's state,
represented by a dictionary. The state can be saved to and loaded from a file.
"""

import json
import logging
import os
import pickle
from pathlib import Path
from typing import Any, Dict, Optional, TypeVar, Generic

logger = logging.getLogger(__name__)

T = TypeVar("T")


class StateManager:
    """Manages the agent's state.

    The state is represented by a dictionary of key-value pairs. The state can be
    saved to and loaded from a file. When a state entry is updated, the entire
    state is automatically saved to the file.
    """

    def __init__(self, state_file: Optional[Path] = None):
        """Initialize the state manager.

        Args:
            state_file: Path to the file where state will be saved. If None, state
                will not be persisted to disk.
        """
        self._state: Dict[str, Any] = {}
        self._state_file = state_file

        # Load state from file if it exists
        if state_file and state_file.exists():
            self._load_state()

    def get(self, key: str, default: T) -> T:
        """Get a state entry.

        Args:
            key: The key for the state entry.
            default: The default value to return if the key doesn't exist.

        Returns:
            The value for the key, or the default if the key doesn't exist.
        """
        return self._state.get(key, default)

    def update(self, key: str, value: Any) -> None:
        """Update a state entry.

        Args:
            key: The key for the state entry.
            value: The value to set.
        """
        self._state[key] = value
        self._save_state()

    def update_many(self, updates: Dict[str, Any]) -> None:
        """Update multiple state entries at once.

        This is more efficient than calling update() multiple times because
        the state is only saved once.

        Args:
            updates: Dictionary of key-value pairs to update.
        """
        self._state.update(updates)
        self._save_state()

    def delete(self, key: str) -> None:
        """Delete a state entry.

        Args:
            key: The key for the state entry to delete.
        """
        if key in self._state:
            del self._state[key]
            self._save_state()

    def clear(self) -> None:
        """Clear all state entries."""
        self._state.clear()
        self._save_state()

    def get_all(self) -> Dict[str, Any]:
        """Get all state entries.

        Returns:
            A copy of the entire state dictionary.
        """
        return self._state.copy()

    def _save_state(self) -> None:
        """Save the state to the file.

        Raises:
            OSError: If there is an error creating the directory or writing to the file.
            pickle.PickleError: If there is an error pickling the state.
        """
        if not self._state_file:
            return

        # Ensure the parent directory exists
        self._state_file.parent.mkdir(parents=True, exist_ok=True)

        # Save the state to the file
        with open(self._state_file, "wb") as f:
            pickle.dump(self._state, f)

    def _load_state(self) -> None:
        """Load the state from the file.

        Raises:
            OSError: If there is an error reading from the file.
            pickle.PickleError: If there is an error unpickling the state.
        """
        if not self._state_file or not self._state_file.exists():
            return

        with open(self._state_file, "rb") as f:
            self._state = pickle.load(f)
