#!/bin/bash

set -e

# Get the absolute path to the repo root
declare -r BASEDIR="$(dirname "$0")"
declare -r AUGMENT="$(readlink -e "$BASEDIR"/../../..)"

# Check if proto stubs exist by trying to generate them
if [[ ! -e "$AUGMENT/tools/deploy_runner/server/deploy_store_pb2_grpc.pyi" ]]; then
    if [[ -z "$AGENT_INSTALL_REQUIREMENTS" ]]; then
        read -p "Research python requirements are required for this, do you want to install them now? [y/N]: " response
    else
        response="y"
    fi
    if [[ "$response" =~ ^[Yy]$ ]]; then
        "$AUGMENT"/research/research-init.sh --cpu --reqs-only
        bazel run //tools/generate_proto_typestubs
        bazel run //base:install
    else
        echo "Cannot proceed without required dependencies"
        exit 1
    fi
fi

export PYTHONPATH="${AUGMENT}${PYTHONPATH:+:$PYTHONPATH}"
exec python3.11 -W ignore "$BASEDIR"/interactive_agent.py "$@"
