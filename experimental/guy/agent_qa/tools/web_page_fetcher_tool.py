"""Web page fetcher tool for the agent."""

from typing import Any, Dict, Optional
from research.agents.tools import (
    <PERSON><PERSON><PERSON>,
    ToolCallLogger,
    ToolImplOutput,
    DialogMessages,
)
from experimental.guy.agent_qa.tools.web_page_fetcher import (
    fetch_and_convert_to_markdown,
    WebPageError,
)


class WebPageFetcherTool(LLMTool):
    """A tool that fetches a web page and converts it to markdown."""

    name = "fetch_web_page"
    description = """Fetch a web page given a URL and convert it to markdown format.
The tool will return the page content in markdown format, preserving links and basic formatting."""

    input_schema = {
        "type": "object",
        "properties": {
            "url": {
                "type": "string",
                "description": "The URL of the web page to fetch",
            },
        },
        "required": ["url"],
    }

    def __init__(self, tool_call_logger: ToolCallLogger):
        super().__init__(tool_call_logger)

    def run_impl(
        self,
        tool_input: Dict[str, Any],
        dialog_messages: Optional[DialogMessages] = None,
    ) -> ToolImplOutput:
        """Run the web page fetcher tool.

        Args:
            tool_input: Dictionary containing the URL to fetch
            dialog_messages: Optional dialog context (not used)

        Returns:
            ToolImplOutput containing markdown formatted page content and status message
        """
        url = tool_input["url"]
        try:
            markdown = fetch_and_convert_to_markdown(url)
            byte_count = len(markdown.encode("utf-8"))
            return ToolImplOutput(
                tool_output=markdown,
                tool_result_message=f"Successfully fetched and converted {url} ({byte_count} bytes)",
            )
        except WebPageError as e:
            error_message = f"Failed to fetch web page: {str(e)}"
            return ToolImplOutput(
                tool_output=error_message, tool_result_message=error_message
            )