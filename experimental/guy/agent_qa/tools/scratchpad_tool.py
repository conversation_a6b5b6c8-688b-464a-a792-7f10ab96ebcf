"""Scratchpad tool for the agent to jot down thoughts and plans.

This tool allows the agent to record insights, plans, and notes during problem-solving.
The scratchpad persists across turns and can be used to track progress and remember
important information.
"""

import json
import time
from pathlib import Path
from typing import Any, Dict, List, Optional
import logging
import uuid

from research.agents.tools import DialogMessages, ToolCallLogger
from experimental.guy.agent_qa.builtin_tools import LLMTool, ToolImplOutput

logger = logging.getLogger(__name__)


class ScratchpadTool(LLMTool):
    """A tool for the agent to jot down thoughts and plans.

    The scratchpad persists across turns and can be used to track progress,
    remember important information, and document insights during problem-solving.
    """

    name = "scratchpad"
    description = """Use this tool to jot down thoughts, insights, or plans.
The scratchpad persists across turns and can be used to:
- Document your understanding of the problem
- Track your progress on complex tasks
- Record insights you've gained
- Update your plan as you work
- Note how to run tests or important commands
- Remember key information you've discovered

The scratchpad is particularly useful for complex tasks where you need to
keep track of multiple pieces of information or steps.
"""
    input_schema = {
        "type": "object",
        "properties": {
            "content": {
                "type": "string",
                "description": "The content to add to the scratchpad",
            },
            "action": {
                "type": "string",
                "enum": ["add", "read", "clear"],
                "description": "Action to perform: add new content, read current content, or clear the scratchpad",
            },
        },
        "required": ["action"],
    }

    def __init__(
        self,
        tool_call_logger: ToolCallLogger,
        cache_dir: Path,
        workspace_root: Path,
    ):
        """Initialize the scratchpad tool.

        Args:
            tool_call_logger: Logger for tool calls
            cache_dir: Directory for agent cache files
            workspace_root: Root directory of the workspace
        """
        super().__init__(tool_call_logger)

        # Create cache directory if it doesn't exist
        self.cache_dir = cache_dir
        self.cache_dir.mkdir(parents=True, exist_ok=True)

        # Create a workspace-specific scratchpad file
        workspace_hash = self._get_workspace_hash(workspace_root)
        self.scratchpad_file = (
            self.cache_dir / f"scratchpad_{workspace_hash}_{uuid.uuid4()}.json"
        )

        # Initialize scratchpad if it doesn't exist
        if not self.scratchpad_file.exists():
            self._initialize_scratchpad()

    def _get_workspace_hash(self, workspace_path: Path) -> str:
        """Generate a hash of the workspace path for the scratchpad filename."""
        import hashlib

        return hashlib.md5(str(workspace_path).encode()).hexdigest()

    def _initialize_scratchpad(self) -> None:
        """Initialize an empty scratchpad file."""
        initial_data = {
            "entries": [],
            "created_at": time.time(),
            "last_updated": time.time(),
        }
        with open(self.scratchpad_file, "w") as f:
            json.dump(initial_data, f, indent=2)

    def _load_scratchpad(self) -> Dict:
        """Load the current scratchpad content."""
        if not self.scratchpad_file.exists():
            self._initialize_scratchpad()

        try:
            with open(self.scratchpad_file, "r") as f:
                return json.load(f)
        except (json.JSONDecodeError, FileNotFoundError):
            # If file is corrupted or missing, initialize a new one
            self._initialize_scratchpad()
            with open(self.scratchpad_file, "r") as f:
                return json.load(f)

    def _save_scratchpad(self, data: Dict) -> None:
        """Save updated scratchpad content."""
        # Update the last_updated timestamp
        data["last_updated"] = time.time()

        with open(self.scratchpad_file, "w") as f:
            json.dump(data, f, indent=2)

    def _format_scratchpad(self, entries: List[Dict]) -> str:
        """Format the scratchpad entries for display."""
        if not entries:
            return "The scratchpad is empty."

        formatted = "# Scratchpad\n\n"
        for i, entry in enumerate(entries, 1):
            timestamp = time.strftime(
                "%Y-%m-%d %H:%M:%S", time.localtime(entry["timestamp"])
            )
            formatted += f"## Entry {i} - {timestamp}\n\n{entry['content']}\n\n"

        return formatted

    def run_impl(
        self,
        tool_input: Dict,
        dialog_messages: Optional[DialogMessages] = None,
    ) -> ToolImplOutput:
        """Run the scratchpad tool.

        Args:
            tool_input: Dictionary containing the action and optional content
            dialog_messages: Optional dialog messages for context

        Returns:
            String response confirming the action or displaying the scratchpad
        """
        action = tool_input.get("action")

        if action not in ["add", "read", "clear"]:
            return ToolImplOutput(
                "Invalid action. Please use 'add', 'read', or 'clear'.",
                "Invalid action specified",
            )

        scratchpad_data = self._load_scratchpad()

        if action == "read":
            formatted_content = self._format_scratchpad(scratchpad_data["entries"])
            logger.warning(f"Scratchpad content:\n-----\n{formatted_content}\n-----\n")
            return ToolImplOutput(formatted_content, "Read scratchpad content")

        elif action == "clear":
            scratchpad_data["entries"] = []
            self._save_scratchpad(scratchpad_data)
            return ToolImplOutput(
                "Scratchpad cleared successfully.", "Cleared scratchpad"
            )

        elif action == "add":
            content = tool_input.get("content")
            if not content:
                return ToolImplOutput(
                    "Error: 'content' is required for 'add' action.",
                    "Missing required content",
                )

            # Add new entry
            new_entry = {
                "timestamp": time.time(),
                "content": content,
            }
            scratchpad_data["entries"].append(new_entry)
            self._save_scratchpad(scratchpad_data)

            # Return both confirmation and the updated scratchpad for convenience
            formatted_content = self._format_scratchpad(scratchpad_data["entries"])
            logger.warning(f"Scratchpad content: {formatted_content}")
            result = f"Added new entry to scratchpad. The scratchpad now has {len(scratchpad_data['entries'])} entries. Here is an updated, full view of the scratchpad:\n\n{formatted_content}"
            return ToolImplOutput(
                result,
                f"Added entry to scratchpad ({len(scratchpad_data['entries'])} total entries)",
            )
        else:
            raise ValueError(f"Invalid action: {action}")

    def get_tool_start_message(self, tool_input: Dict[str, Any]) -> str:
        action = tool_input.get("action", "")
        if action == "add":
            return "Adding entry to scratchpad"
        elif action == "read":
            return "Reading scratchpad content"
        elif action == "clear":
            return "Clearing scratchpad"
        else:
            raise ValueError(f"Invalid action: {action}")
