"""Tests for the SlackSearchTool."""

from research.agents.tools import <PERSON><PERSON><PERSON><PERSON><PERSON>og<PERSON>
from experimental.guy.agent_qa.tools.slack_search_tool import SlackSearchTool
import pytest


@pytest.mark.integration
def test_slack_search():
    """Test the SlackSearchTool's search functionality."""
    # Read user token
    with open("/home/<USER>/.augment/agent/slack_user_token", "r") as f:
        user_token = f.read().strip()

    # Create logger
    logger = ToolCallLogger()

    # Create tool instance
    tool = SlackSearchTool(
        tool_call_logger=logger,
        user_token=user_token,
    )

    # Test searching with different queries
    queries = [
        "python error",
        "meeting tomorrow",
        "important announcement",
    ]

    for query in queries:
        result = tool.run_impl({"query": query})
        # Either found results or explicitly said none found
        assert any(
            x in result.tool_output.lower()
            for x in ["search results", "no matching messages"]
        )
        assert any(
            x in result.tool_result_message.lower() for x in ["found", "no results"]
        )


@pytest.mark.integration
def test_slack_search_invalid_token():
    """Test SlackSearchTool behavior with invalid token."""
    logger = ToolCallLogger()

    # Create tool with invalid token
    tool = SlackSearchTool(
        tool_call_logger=logger,
        user_token="xoxp-invalid-token",
    )

    # Test searching
    result = tool.run_impl({"query": "test"})
    assert "failed to search" in result.tool_output.lower()
    assert "error searching" in result.tool_result_message.lower()
