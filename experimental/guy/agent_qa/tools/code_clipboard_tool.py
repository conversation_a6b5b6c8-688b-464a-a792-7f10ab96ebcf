"""Tool for clipboard operations on code nodes."""

from pathlib import Path
from typing import Any, Dict, Optional

from tree_sitter import Node
from tree_sitter_language_pack import get_parser

from research.agents.tools import Tool<PERSON><PERSON><PERSON><PERSON><PERSON>, ToolImplOutput
from experimental.guy.agent_qa.builtin_tools import LLMTool
from experimental.guy.agent_qa.tools.code_node_utils import (
    LANGUAGE_MAP,
    get_node_name,
    get_node_text,
    find_node,
)


def insert_after_node(
    content: str, target_node: Optional[Node], source_bytes: bytes
) -> str:
    """Insert content after the specified node."""
    if not target_node:
        # Insert at the beginning of the file
        return content + "\n" + source_bytes.decode("utf-8")

    # Insert after the target node
    before = source_bytes[: target_node.end_byte].decode("utf-8")
    after = source_bytes[target_node.end_byte :].decode("utf-8")
    return before + "\n\n" + content + after


class CodeClipboardTool(LLMTool):
    """Tool for clipboard operations on code nodes."""

    def __init__(self, tool_call_logger: Too<PERSON><PERSON><PERSON>Logger, root: Path):
        """Initialize the tool.

        Args:
            tool_call_logger: Logger for tool calls
            root: Root directory for resolving file paths
        """
        super().__init__(tool_call_logger)
        self.root = root
        self.name = "code_clipboard"
        self.description = (
            "Perform clipboard operations (cut/copy/paste) on code nodes. "
            "Nodes are specified as type:name (e.g., 'class:MyClass', 'function:process')."
        )
        self.input_schema = {
            "type": "object",
            "properties": {
                "file_path": {
                    "type": "string",
                    "description": "Path to the file to operate on",
                },
                "node": {
                    "type": "string",
                    "description": "Node specification in format type:name (e.g., 'class:MyClass', 'function:process')",
                },
                "command": {
                    "type": "string",
                    "enum": ["cut", "copy", "paste"],
                    "description": "Operation to perform: cut, copy, or paste",
                },
            },
            "required": ["file_path", "node", "command"],
        }

        # Load parsers for each language
        self.parsers = {}
        for ext, lang_name in LANGUAGE_MAP.items():
            try:
                # Type cast to handle the SupportedLanguage type requirement
                self.parsers[ext] = get_parser(lang_name)  # type: ignore
            except Exception as e:
                print(f"Warning: Failed to load {lang_name} parser: {e}")

        # Initialize clipboard
        self.clipboard = None

    def run_impl(
        self,
        tool_input: Dict[str, Any],
        dialog_messages: Optional[Any] = None,
    ) -> ToolImplOutput:
        """Run the tool implementation."""
        path = self.root / tool_input["file_path"]

        if tool_input["command"] == "paste" and not path.exists():
            # Paste into new file
            path.parent.mkdir(parents=True, exist_ok=True)
            path.touch()

        if not path.exists():
            return ToolImplOutput("File does not exist", f"File {path} does not exist")

        # Get file extension and corresponding parser
        ext = path.suffix.lower()
        if ext not in self.parsers:
            return ToolImplOutput(
                f"Unsupported file type: {ext}", f"Cannot process {ext} files"
            )

        try:
            # Read file content
            content = path.read_bytes()

            # Parse with the correct parser for this file
            parser = self.parsers[ext]
            tree = parser.parse(content)

            # Get command and node spec
            command = tool_input["command"]
            node_spec = tool_input["node"]

            # Find the specified node
            node = find_node(tree.root_node, content, node_spec, debug=False)
            if not node and command != "paste":
                return ToolImplOutput(
                    f"Node not found: {node_spec}",
                    f"Could not find node {node_spec} in {path}",
                )

            if command == "cut":
                # Store node in clipboard and remove from file
                self.clipboard = get_node_text(node, content)

                # Find the start of the line containing the node
                line_start = content.rfind(b"\n", 0, node.start_byte) + 1
                if line_start == 0:  # Node starts at beginning of file
                    line_start = 0

                # Find the end of the line after the node
                line_end = content.find(b"\n", node.end_byte)
                if line_end == -1:  # Node ends at end of file
                    line_end = len(content)
                else:
                    line_end += 1  # Include the newline

                # Remove the node and its surrounding whitespace
                new_content = content[:line_start] + content[line_end:]

                # Clean up double newlines
                while b"\n\n\n" in new_content:
                    new_content = new_content.replace(b"\n\n\n", b"\n\n")

                path.write_bytes(new_content)
                return ToolImplOutput(
                    f"Cut node {node_spec} to clipboard",
                    f"Cut {len(self.clipboard)} bytes from {path}",
                )

            elif command == "copy":
                # Store node in clipboard
                self.clipboard = get_node_text(node, content)
                return ToolImplOutput(
                    f"Copied node {node_spec} to clipboard",
                    f"Copied {len(self.clipboard)} bytes from {path}",
                )

            elif command == "paste":
                # Insert clipboard contents after specified node
                if not self.clipboard:
                    return ToolImplOutput("Clipboard is empty", "No content to paste")
                new_content = insert_after_node(self.clipboard, node, content)
                path.write_bytes(new_content.encode("utf-8"))
                return ToolImplOutput(
                    f"Pasted clipboard after node {node_spec or 'start'}",
                    f"Pasted {len(self.clipboard)} bytes into {path}",
                )

        except Exception as e:
            return ToolImplOutput(
                f"Error processing file: {str(e)}",
                f"Failed to process {path}: {str(e)}",
            )

        raise ValueError("Unreachable code")

    def get_tool_start_message(self, tool_input: Dict[str, Any]) -> str:
        return (
            f"Performing {tool_input['command']} operation on {tool_input['file_path']}"
        )
