"""Tests for the agent coordinator tool."""

from datetime import datetime
from unittest.mock import Mock

import pytest

from experimental.guy.agent_qa.agent_coordinator_interface import AgentMessage
from experimental.guy.agent_qa.tools.agent_coordinator_tool import Agent<PERSON>oordinatorTool
from research.agents.tools import Tool<PERSON><PERSON>Logger


@pytest.fixture
def mock_coordinator():
    """Create a mock coordinator."""
    return Mock()


@pytest.fixture
def tool_call_logger():
    """Create a tool call logger."""
    return ToolCallLogger()


def test_notify_action(mock_coordinator, tool_call_logger):
    """Test notifying other agents."""
    tool = AgentCoordinatorTool(
        tool_call_logger=tool_call_logger,
        coordinator=mock_coordinator,
        agent_name="TestAgent",
    )

    result = tool.run_impl({"action": "notify", "message": "Hello world"})

    # Verify coordinator was called correctly
    mock_coordinator.notify_current_action.assert_called_once_with(
        "TestAgent", "Hello world"
    )

    # Verify tool output
    assert "Successfully" in result.tool_output
    assert "sent successfully" in result.tool_result_message


def test_get_messages_action_with_messages(mock_coordinator, tool_call_logger):
    """Test getting messages when there are messages."""
    tool = AgentCoordinatorTool(
        tool_call_logger=tool_call_logger,
        coordinator=mock_coordinator,
        agent_name="TestAgent",
    )

    # Setup mock messages
    messages = [
        AgentMessage(
            agent_name="Agent1",
            message="Message 1",
            timestamp=datetime(2024, 1, 1, 12, 0, 0),
        ),
        AgentMessage(
            agent_name="Agent2",
            message="Message 2",
            timestamp=datetime(2024, 1, 1, 12, 1, 0),
        ),
    ]
    mock_coordinator.get_messages.return_value = messages

    result = tool.run_impl({"action": "get_messages"})

    # Verify coordinator was called correctly
    mock_coordinator.get_messages.assert_called_once_with("TestAgent")

    # Verify tool output contains formatted messages
    assert "12:00:00" in result.tool_output
    assert "12:01:00" in result.tool_output
    assert "Agent1: Message 1" in result.tool_output
    assert "Agent2: Message 2" in result.tool_output
    assert "Retrieved 2 messages" in result.tool_result_message


def test_get_messages_action_no_messages(mock_coordinator, tool_call_logger):
    """Test getting messages when there are no messages."""
    tool = AgentCoordinatorTool(
        tool_call_logger=tool_call_logger,
        coordinator=mock_coordinator,
        agent_name="TestAgent",
    )

    # Setup mock to return no messages
    mock_coordinator.get_messages.return_value = []

    result = tool.run_impl({"action": "get_messages"})

    # Verify coordinator was called correctly
    mock_coordinator.get_messages.assert_called_once_with("TestAgent")

    # Verify tool output
    assert "No new messages" in result.tool_output
    assert "Retrieved 0 messages" in result.tool_result_message


def test_invalid_action(mock_coordinator, tool_call_logger):
    """Test handling of invalid action."""
    tool = AgentCoordinatorTool(
        tool_call_logger=tool_call_logger,
        coordinator=mock_coordinator,
        agent_name="TestAgent",
    )

    result = tool.run_impl({"action": "invalid"})

    # Verify error message matches JSON schema validation
    assert "'invalid' is not one of ['notify', 'get_messages']" in result.tool_output
    assert "Invalid tool input" in result.tool_result_message


def test_notify_without_message(mock_coordinator, tool_call_logger):
    """Test notify action without message."""
    tool = AgentCoordinatorTool(
        tool_call_logger=tool_call_logger,
        coordinator=mock_coordinator,
        agent_name="TestAgent",
    )

    result = tool.run_impl({"action": "notify"})

    # Verify error message matches validation error
    assert "Message is required for notify action" in result.tool_output
    assert "Message is required for notify action" in result.tool_result_message


def test_coordinator_error(mock_coordinator, tool_call_logger):
    """Test handling of coordinator errors."""
    tool = AgentCoordinatorTool(
        tool_call_logger=tool_call_logger,
        coordinator=mock_coordinator,
        agent_name="TestAgent",
    )

    # Setup mock to raise an error
    mock_coordinator.notify_current_action.side_effect = ValueError("Test error")

    result = tool.run_impl({"action": "notify", "message": "Hello"})

    # Verify error message
    assert "Error" in result.tool_output
    assert "Test error" in result.tool_output
    assert "Error performing notify" in result.tool_result_message
