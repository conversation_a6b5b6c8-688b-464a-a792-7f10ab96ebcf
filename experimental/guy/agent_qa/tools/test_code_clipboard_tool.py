"""Tests for code_clipboard_tool."""

import pytest
from pathlib import Path
from research.agents.tools import <PERSON><PERSON><PERSON><PERSON><PERSON>og<PERSON>
from experimental.guy.agent_qa.tools.code_clipboard_tool import CodeClipboardTool


@pytest.fixture
def tool_call_logger():
    return ToolCallLogger(verbose=True)


@pytest.fixture
def test_files(tmp_path):
    # Create test Python file
    py_file = tmp_path / "test.py"
    py_file.write_text("""
import os
from pathlib import Path

CONSTANT = 42

class TestClass:
    \"\"\"Class docstring.\"\"\"

    def __init__(self, x):
        self.x = x

    def method(self, y):
        \"\"\"Method docstring.\"\"\"
        return self.x + y

def standalone_func():
    \"\"\"Function docstring.\"\"\"
    return 42
""")

    # Create test Go file
    go_file = tmp_path / "test.go"
    go_file.write_text("""
package main

import "fmt"

type Person struct {
    Name string
    Age  int
}

func (p *Person) Say<PERSON>ello() {
    fmt.Printf("Hello, I'm %s\\n", p.Name)
}

func main() {
    p := Person{Name: "Bob", Age: 30}
    p.<PERSON>()
}
""")

    return tmp_path


@pytest.fixture
def clipboard_tool(tool_call_logger, test_files):
    return CodeClipboardTool(tool_call_logger, test_files)


def test_copy_python_class(clipboard_tool, test_files):
    """Test copying a Python class."""
    result = clipboard_tool.run_impl(
        {"file_path": "test.py", "node": "class:TestClass", "command": "copy"}
    )

    assert "Copied node class:TestClass" in result.tool_output
    assert "Class docstring" in clipboard_tool.clipboard
    assert "def __init__" in clipboard_tool.clipboard
    assert "def method" in clipboard_tool.clipboard


def test_cut_python_function(clipboard_tool, test_files):
    """Test cutting a Python function."""
    result = clipboard_tool.run_impl(
        {"file_path": "test.py", "node": "function:standalone_func", "command": "cut"}
    )

    assert "Cut node function:standalone_func" in result.tool_output
    assert "Function docstring" in clipboard_tool.clipboard
    assert "return 42" in clipboard_tool.clipboard

    # Function should be removed from file
    content = (test_files / "test.py").read_text()
    assert "standalone_func" not in content
    assert "Function docstring" not in content


def test_paste_python_function(clipboard_tool, test_files):
    """Test pasting a Python function."""
    # First copy a function
    clipboard_tool.run_impl(
        {"file_path": "test.py", "node": "function:standalone_func", "command": "copy"}
    )

    # Then paste it after the class
    result = clipboard_tool.run_impl(
        {"file_path": "test.py", "node": "class:TestClass", "command": "paste"}
    )

    assert "Pasted clipboard after node class:TestClass" in result.tool_output

    # Function should appear after the class
    content = (test_files / "test.py").read_text()
    class_pos = content.find("class TestClass")
    func_pos = content.find("def standalone_func")
    assert class_pos < func_pos


def test_paste_at_start(clipboard_tool, test_files):
    """Test pasting at the start of a file."""
    # First copy a function
    clipboard_tool.run_impl(
        {"file_path": "test.py", "node": "function:standalone_func", "command": "copy"}
    )

    # Then paste it at the start
    result = clipboard_tool.run_impl(
        {"file_path": "test.py", "node": "", "command": "paste"}
    )

    assert "Pasted clipboard after node start" in result.tool_output

    # Function should appear at the start
    content = (test_files / "test.py").read_text()
    assert content.strip().startswith("def standalone_func")


def test_copy_go_type(clipboard_tool, test_files):
    """Test copying a Go type."""
    result = clipboard_tool.run_impl(
        {"file_path": "test.go", "node": "type:Person", "command": "copy"}
    )

    assert "Copied node type:Person" in result.tool_output
    assert "struct" in clipboard_tool.clipboard
    assert "Name string" in clipboard_tool.clipboard
    assert "Age  int" in clipboard_tool.clipboard


def test_cut_go_function(clipboard_tool, test_files):
    """Test cutting a Go function."""
    result = clipboard_tool.run_impl(
        {"file_path": "test.go", "node": "function:main", "command": "cut"}
    )

    assert "Cut node function:main" in result.tool_output
    assert 'Person{Name: "Bob"' in clipboard_tool.clipboard
    assert "p.SayHello()" in clipboard_tool.clipboard

    # Function should be removed from file
    content = (test_files / "test.go").read_text()
    assert "func main()" not in content
    assert 'p := Person{Name: "Bob"' not in content

    # But SayHello method should still be there
    assert "func (p *Person) SayHello()" in content


def test_paste_go_function(clipboard_tool, test_files):
    """Test pasting a Go function."""
    # First copy a function
    clipboard_tool.run_impl(
        {"file_path": "test.go", "node": "function:main", "command": "copy"}
    )

    # Then paste it after the type
    result = clipboard_tool.run_impl(
        {"file_path": "test.go", "node": "type:Person", "command": "paste"}
    )

    assert "Pasted clipboard after node type:Person" in result.tool_output

    # Function should appear after the type
    content = (test_files / "test.go").read_text()
    type_pos = content.find("type Person struct")
    func_pos = content.find("func main()")
    assert type_pos < func_pos


def test_nonexistent_file(clipboard_tool):
    """Test handling of nonexistent file."""
    result = clipboard_tool.run_impl(
        {"file_path": "nonexistent.py", "node": "class:MyClass", "command": "copy"}
    )
    assert "File does not exist" in result.tool_output


def test_unsupported_extension(clipboard_tool, test_files):
    """Test handling of unsupported file type."""
    # Create file with unsupported extension
    (test_files / "test.xyz").write_text("test content")

    result = clipboard_tool.run_impl(
        {"file_path": "test.xyz", "node": "class:MyClass", "command": "copy"}
    )
    assert "Unsupported file type" in result.tool_output


def test_node_not_found(clipboard_tool):
    """Test handling of nonexistent node."""
    result = clipboard_tool.run_impl(
        {"file_path": "test.py", "node": "class:NonexistentClass", "command": "copy"}
    )
    assert "Node not found" in result.tool_output


def test_paste_empty_clipboard(clipboard_tool):
    """Test pasting with empty clipboard."""
    result = clipboard_tool.run_impl(
        {"file_path": "test.py", "node": "class:TestClass", "command": "paste"}
    )
    assert "Clipboard is empty" in result.tool_output


def test_paste_to_new_file(clipboard_tool, test_files):
    """Test pasting to a new file."""
    # First copy a function
    clipboard_tool.run_impl(
        {"file_path": "test.py", "node": "function:standalone_func", "command": "copy"}
    )

    # Then paste it to a new file
    clipboard_tool.run_impl(
        {
            "file_path": "new_file.py",
            "node": "function:standalone_func",
            "command": "paste",
        }
    )

    # Function should appear in the new file
    content = (test_files / "new_file.py").read_text()
    assert "def standalone_func" in content
