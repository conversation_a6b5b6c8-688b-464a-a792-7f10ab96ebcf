"""Tests for read_file_outline_tool."""

import pytest
from pathlib import Path
from research.agents.tools import <PERSON><PERSON><PERSON><PERSON><PERSON>og<PERSON>
from experimental.guy.agent_qa.tools.read_file_outline_tool import ReadFileOutlineTool


@pytest.fixture
def tool_call_logger():
    return ToolCallLogger(verbose=True)


@pytest.fixture
def test_files(tmp_path):
    # Create test Python file
    py_file = tmp_path / "test.py"
    py_file.write_text("""
import os
from pathlib import Path

CONSTANT = 42
ANOTHER_CONST = "test"

class TestClass:
    def __init__(self):
        self.x = 1

    def method(self, arg):
        return arg + 1

def standalone_func(x, y):
    return x + y
""")

    return tmp_path


@pytest.fixture
def outline_tool(tool_call_logger, test_files):
    return ReadFileOutlineTool(tool_call_logger, test_files)


def test_python_outline(outline_tool):
    result = outline_tool.run_impl({"file_path": "test.py"})

    # Basic outline elements should be present
    assert "import os" in result.tool_output
    assert "from pathlib import Path" in result.tool_output
    assert "CONSTANT = 42" in result.tool_output
    assert 'ANOTHER_CONST = "test"' in result.tool_output

    # Class should be collapsed
    assert "class TestClass:" in result.tool_output
    assert "..." in result.tool_output

    # Function should be collapsed
    assert "def standalone_func(x, y):" in result.tool_output
    assert "..." in result.tool_output

    # Implementation details should not be present
    assert "self.x = 1" not in result.tool_output
    assert "return x + y" not in result.tool_output


def test_python_outline_with_expansion(outline_tool):
    """Test expanding specific sections in Python code."""
    result = outline_tool.run_impl(
        {
            "file_path": "test.py",
            "expand_sections": ["function:standalone_func", "class:TestClass"],
        }
    )

    # Basic outline elements should be present
    assert "import os" in result.tool_output
    assert "from pathlib import Path" in result.tool_output
    assert "CONSTANT = 42" in result.tool_output

    # TestClass should be fully expanded
    assert "class TestClass:" in result.tool_output
    assert "def __init__(self):" in result.tool_output
    assert "def method(self, arg):" in result.tool_output
    assert "..." in result.tool_output  # Methods should be collapsed

    # standalone_func should be fully expanded
    assert "def standalone_func(x, y):" in result.tool_output
    assert "return x + y" in result.tool_output  # Implementation detail

    # Method implementations should not be present
    assert "return arg + 1" not in result.tool_output


def test_go_outline_with_expansion(outline_tool, test_files, capsys):
    """Test expanding specific sections in Go code."""
    # Create test Go file
    go_file = test_files / "test.go"
    go_file.write_text("""
package main

import "fmt"

type Person struct {
    Name string
    Age  int
}

func (p *Person) SayHello() {
    fmt.Printf("Hello, I'm %s\\n", p.Name)
}

func main() {
    p := Person{Name: "Bob", Age: 30}
    p.SayHello()
}
""")

    result = outline_tool.run_impl(
        {
            "file_path": "test.go",
            "debug": True,
            "expand_sections": ["function:main", "type:Person"],
        }
    )

    # Get captured output
    captured = capsys.readouterr()
    print("\nDebug output:")
    print(captured.out)
    print("\nGenerated outline:")
    print(result.tool_output)

    # Basic assertions to verify the output
    assert "package main" in result.tool_output
    assert 'import "fmt"' in result.tool_output

    # Person type should be fully expanded
    assert "type Person struct {" in result.tool_output
    assert "Name string" in result.tool_output
    assert "Age  int" in result.tool_output  # Note: two spaces between Age and int

    # main function should be fully expanded
    assert "func main() {" in result.tool_output
    assert 'p := Person{Name: "Bob", Age: 30}' in result.tool_output
    assert "p.SayHello()" in result.tool_output

    # SayHello method should be collapsed
    assert "func (p *Person) SayHello() {" in result.tool_output
    assert "..." in result.tool_output
    assert "fmt.Printf" not in result.tool_output


def test_nonexistent_file(outline_tool):
    result = outline_tool.run_impl({"file_path": "nonexistent.py"})
    assert "File does not exist" in result.tool_output


def test_unsupported_extension(outline_tool, test_files):
    unsupported = test_files / "test.xyz"
    unsupported.write_text("test content")

    result = outline_tool.run_impl({"file_path": "test.xyz"})
    assert "Unsupported file type" in result.tool_output


def test_python_outline_debug(outline_tool, test_files, capsys):
    """Test with debug output to see node types."""
    # Create a test file with various Python constructs
    test_file = test_files / "debug.py"
    test_file.write_text('''
class TestClass:
    """Class docstring."""

    def __init__(self, x):
        self.x = x

    def method(self, y):
        """Method docstring."""
        return self.x + y

def standalone_func():
    """Function docstring."""
    return 42

CONSTANT = "test"
''')

    # First try without expansion
    print("\nTesting without expansion:")
    result = outline_tool.run_impl(
        {
            "file_path": "debug.py",
            "debug": True,
        }
    )

    # Get captured output
    captured = capsys.readouterr()
    print("\nDebug output (no expansion):")
    print(captured.out)
    print("\nGenerated outline (no expansion):")
    print(result.tool_output)

    # Then try with expansion
    print("\nTesting with expansion:")
    result = outline_tool.run_impl(
        {
            "file_path": "debug.py",
            "debug": True,
            "expand_sections": [
                "class:TestClass"
            ],  # Expand the class to see its structure
        }
    )

    # Get captured output
    captured = capsys.readouterr()
    print("\nDebug output (with expansion):")
    print(captured.out)
    print("\nGenerated outline (with expansion):")
    print(result.tool_output)

    # Basic assertions to verify the output
    assert "Processing Python module" in captured.out
    assert "Found class TestClass" in captured.out
    assert "Found function standalone_func" in captured.out
    assert "Found constant: CONSTANT" in captured.out

    # Verify expansion behavior
    assert "Checking expansion for class:TestClass" in captured.out
    assert "(expanded: True)" in captured.out
    assert "def __init__" in result.tool_output
    assert "def method" in result.tool_output


def test_max_depth(tmp_path: Path) -> None:
    """Test that max_depth parameter limits recursion depth."""
    # Create a file with deeply nested classes
    test_file = tmp_path / "nested.py"
    test_file.write_text('''
class Outer:
    """Outer class."""
    class Middle:
        """Middle class."""
        class Inner:
            """Inner class."""
            class VeryInner:
                """Very inner class."""
                class ExtremelyInner:
                    """Extremely inner class."""
                    def method(self):
                        """A method."""
                        pass
''')

    # Test with different max_depth values
    tool = ReadFileOutlineTool(ToolCallLogger(verbose=True), tmp_path)

    # Test default depth (5)
    result = tool.run_impl(
        {
            "file_path": "nested.py",
            "expand_sections": [
                "class:Outer"
            ],  # Expand outer class to see nested classes
        }
    )
    assert "class Outer:" in result.tool_output
    assert "    class Middle:" in result.tool_output
    assert "        class Inner:" in result.tool_output
    assert "            class VeryInner:" in result.tool_output
    assert "                class ExtremelyInner:" in result.tool_output
    assert "... (max depth reached)" not in result.tool_output

    # Test shallow depth (2)
    result = tool.run_impl(
        {
            "file_path": "nested.py",
            "expand_sections": ["class:Outer"],
            "max_depth": 2,
        }
    )
    assert "class Outer:" in result.tool_output
    assert "    class Middle:" in result.tool_output
    assert "        class Inner:" not in result.tool_output
    assert "... (max depth reached)" in result.tool_output

    # Test deep depth (10)
    result = tool.run_impl(
        {
            "file_path": "nested.py",
            "expand_sections": ["class:Outer"],
            "max_depth": 10,
        }
    )
    assert "class Outer:" in result.tool_output
    assert "    class Middle:" in result.tool_output
    assert "        class Inner:" in result.tool_output
    assert "            class VeryInner:" in result.tool_output
    assert "                class ExtremelyInner:" in result.tool_output
    assert "                    def method" in result.tool_output
    assert "... (max depth reached)" not in result.tool_output


def test_python_outline_imports(outline_tool, test_files):
    """Test that imports are correctly shown in the outline."""
    # Create a test file with various import styles
    test_file = test_files / "imports.py"
    test_file.write_text("""
import os
import sys as system
from pathlib import Path
from typing import List, Optional
from .local_module import something
from ..parent_module import another_thing as thing
from somewhere import (
    this,
    that,
    other,
)

def some_function():
    pass
""")

    result = outline_tool.run_impl({"file_path": "imports.py"})

    # Print the actual output for debugging
    print("\nActual output:")
    print(result.tool_output)
    print("\nLines after stripping:")
    lines = [line.strip() for line in result.tool_output.split("\n") if line.strip()]
    print("\n".join(lines))

    # Verify all import styles are shown
    assert "import os" in result.tool_output
    assert "import sys as system" in result.tool_output
    assert "from pathlib import Path" in result.tool_output
    assert "from typing import List, Optional" in result.tool_output
    assert "from .local_module import something" in result.tool_output
    assert "from ..parent_module import another_thing as thing" in result.tool_output
    assert "from somewhere import (" in result.tool_output
    assert "this," in result.tool_output
    assert "that," in result.tool_output
    assert "other," in result.tool_output

    # Verify imports are shown at the top and function is shown after
    lines = [line.strip() for line in result.tool_output.split("\n") if line.strip()]
    import_section = lines[: lines.index("def some_function():")]
    assert all(
        line.startswith(("import ", "from ", "this", "that", "other", ")"))
        for line in import_section
    )
    assert "def some_function():" in lines
