"""Remote tool implementation for the interactive agent.

This module provides a RemoteTool class that wraps the Augment API's remote tool functionality,
allowing the interactive agent to use remote tools like GitHub API, Linear, Notion, etc.
"""

import json
from typing import Any, Dict, Optional, List

from termcolor import colored

from base.augment_client.client import Augment<PERSON>lient, RemoteToolId, RemoteToolInfo
from research.agents.tools import <PERSON><PERSON><PERSON>, ToolCallLogger, ToolImplOutput


# RemoteToolInfo is imported from base.augment_client.client


class RemoteTool(LLMTool):
    """A tool that calls a remote tool through the Augment API.

    This class wraps a remote tool defined in the Augment API, allowing it to be used
    like any other tool in the interactive agent.
    """

    def __init__(
        self,
        tool_call_logger: Tool<PERSON>allLogger,
        augment_client: AugmentClient,
        tool_info: RemoteToolInfo,
    ):
        """Initialize the remote tool.

        Args:
            tool_call_logger: The tool call logger to use
            augment_client: The Augment API client to use for making remote tool calls
            tool_info: Information about the remote tool from list_remote_tools
        """
        super().__init__(tool_call_logger)
        self.augment_client = augment_client
        self.tool_info = tool_info

        # Set tool properties from the tool info
        self.name = tool_info.tool_definition.get("name", "unknown_remote_tool")
        self.description = tool_info.tool_definition.get(
            "description", "No description available"
        )

        # Parse the input schema
        try:
            schema_json = tool_info.tool_definition.get("input_schema_json", "{}")
            self.input_schema = json.loads(schema_json)
        except json.JSONDecodeError:
            print(
                colored(
                    f"Warning: Could not parse input schema for {self.name}", "yellow"
                )
            )
            self.input_schema = {"type": "object", "properties": {}}

        # Store the remote tool ID
        self.remote_tool_id = tool_info.remote_tool_id

    def run_impl(
        self, tool_input: Dict[str, Any], dialog_messages=None
    ) -> ToolImplOutput:
        """Execute the remote tool with the given input.

        Args:
            tool_input: The input to the tool
            dialog_messages: Optional dialog messages (not used for remote tools)

        Returns:
            The tool output
        """
        try:
            # Convert the input to JSON
            tool_input_json = json.dumps(tool_input)

            # Call the remote tool
            result = self.augment_client.run_remote_tool(
                tool_name=self.name,
                tool_input_json=tool_input_json,
                tool_id=self.remote_tool_id,
            )

            # Check if there was an error
            if result.is_error:
                error_message = (
                    f"Error calling remote tool {self.name}: {result.tool_output}"
                )
                return ToolImplOutput(
                    tool_output=error_message,
                    tool_result_message=error_message,
                )

            # Return the result
            return ToolImplOutput(
                tool_output=result.tool_output,
                tool_result_message=result.tool_result_message
                or "Remote tool executed successfully",
            )
        except Exception as e:
            error_message = f"Error calling remote tool {self.name}: {str(e)}"
            return ToolImplOutput(
                tool_output=error_message,
                tool_result_message=error_message,
            )


def list_remote_tools(
    augment_client: AugmentClient,
) -> list[RemoteToolInfo]:
    """List all available remote tools.

    Args:
        augment_client: The Augment API client to use

    Returns:
        A list of RemoteToolInfo objects
    """
    # TODO(guy) The API should not require tool_ids parameter
    tool_ids = [int(v) for v in RemoteToolId.values()]
    return augment_client.list_remote_tools(tool_ids=tool_ids)


def create_remote_tools(
    tool_call_logger: ToolCallLogger,
    augment_client: AugmentClient,
    include_tools: Optional[list[str]] = None,
) -> list[RemoteTool]:
    """Create RemoteTool instances for all available remote tools.

    Args:
        tool_call_logger: The tool call logger to use
        augment_client: The Augment API client to use
        include_tools: Optional list of tool names to include (if None, include all)

    Returns:
        A list of RemoteTool instances
    """
    tools = []

    try:
        remote_tools = list_remote_tools(augment_client)

        if not remote_tools:
            print(colored("No remote tools available.", "yellow"))
            return []

        print(colored(f"Found {len(remote_tools)} remote tools", "blue"))

        for tool_info in remote_tools:
            tool_name = tool_info.tool_definition.get("name", "")

            # Skip tools not in the include list if specified
            if include_tools is not None and tool_name not in include_tools:
                continue

            # Create a RemoteTool instance
            tool = RemoteTool(
                tool_call_logger=tool_call_logger,
                augment_client=augment_client,
                tool_info=tool_info,
            )

            tools.append(tool)
            print(colored(f"Added remote tool: {tool_name}", "blue"))
    except Exception as e:
        import traceback

        print(colored(f"Error creating remote tools: {e}", "red"))
        print(colored(traceback.format_exc(), "red"))

    return tools
