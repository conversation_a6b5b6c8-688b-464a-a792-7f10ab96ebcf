"""Tests for the Linear tool."""

import json
import pytest
from unittest.mock import <PERSON><PERSON><PERSON>, patch

from research.llm_apis.llm_client import TextResult
from experimental.guy.agent_qa.tools.linear_tool import LinearTool, LinearTicket


@pytest.fixture
def mock_gql_client():
    """Create a mock GQL client."""
    with patch("experimental.guy.agent_qa.tools.linear_tool.Client") as mock:
        yield mock.return_value


@pytest.fixture
def mock_llm_client():
    """Create a mock LLM client."""
    mock = MagicMock()
    mock.generate.return_value = (
        TextResult(
            "query { issues { nodes { id title description state { name } assignee { email } updatedAt } } }"
        ),
        {},
    )
    return mock


@pytest.fixture
def mock_tool_logger():
    """Create a mock tool logger."""
    return MagicMock()


@pytest.fixture
def linear_tool(mock_gql_client, mock_llm_client, mock_tool_logger):
    """Create a LinearTool instance with mocked dependencies."""
    return LinearTool("fake-api-key", mock_llm_client, mock_tool_logger)


def test_graphql_query_generation(linear_tool, mock_llm_client):
    """Test generating GraphQL query using LLM."""
    query = linear_tool._generate_graphql_query("show my tickets", "<EMAIL>")
    assert isinstance(query, str)  # Verify query is generated as string

    # Verify LLM was called with correct prompt
    mock_llm_client.generate.assert_called_once()
    call_args = mock_llm_client.generate.call_args
    assert call_args[1]["max_tokens"] == 8192  # Updated to match standard max tokens
    assert len(call_args[1]["messages"]) == 1
    assert "<EMAIL>" in call_args[1]["messages"][0][0].text
    assert "show my tickets" in call_args[1]["messages"][0][0].text


def test_query_execution(linear_tool, mock_gql_client):
    """Test executing a query and parsing results."""
    mock_gql_client.execute.return_value = {
        "issues": {
            "nodes": [
                {
                    "id": "issue1",
                    "title": "Test Issue",
                    "description": "Test Description",
                    "state": {"name": "In Progress"},
                    "assignee": {"email": "<EMAIL>"},
                    "updatedAt": "2024-01-01T00:00:00Z",
                }
            ]
        }
    }

    result = linear_tool.run_impl(
        {"query": "show my tickets", "user_email": "<EMAIL>"}
    )

    # Verify the output contains the expected information
    assert "Test Issue" in result.tool_output
    assert "In Progress" in result.tool_output
    assert "<EMAIL>" in result.tool_output


def test_linear_ticket_from_node():
    """Test creating LinearTicket from GraphQL node."""
    node = {
        "id": "issue1",
        "title": "Test Issue",
        "description": "Test Description",
        "state": {"name": "In Progress"},
        "assignee": {"email": "<EMAIL>"},
        "updatedAt": "2024-01-01T00:00:00Z",
    }

    ticket = LinearTicket.from_node(node)
    assert ticket.id == "issue1"
    assert ticket.title == "Test Issue"
    assert ticket.description == "Test Description"
    assert ticket.state == "In Progress"
    assert ticket.assignee == "<EMAIL>"
    assert ticket.updated_at == "2024-01-01T00:00:00Z"


def test_input_schema():
    """Test that input schema is correctly defined."""
    assert LinearTool.input_schema["type"] == "object"
    assert "query" in LinearTool.input_schema["properties"]
    assert "user_email" in LinearTool.input_schema["properties"]
    assert "query" in LinearTool.input_schema["required"]
    assert "user_email" in LinearTool.input_schema["required"]


def test_query_validation_rejects_mutation(linear_tool):
    """Test that mutation operations are rejected."""
    mutation_query = """
    mutation DeleteIssue($id: String!) {
        issueDelete(id: $id) {
            success
        }
    }
    """
    with pytest.raises(ValueError) as exc_info:
        linear_tool._execute_query(mutation_query)
    assert "Only 'query' operations are allowed" in str(exc_info.value)


def test_query_validation_rejects_hidden_mutation(linear_tool):
    """Test that queries containing mutation keywords are rejected."""
    sneaky_query = """
    query {
        # This is a mutation in disguise
        issues(filter: { mutation: { eq: true } }) {
            nodes {
                id
            }
        }
    }
    """
    with pytest.raises(ValueError) as exc_info:
        linear_tool._execute_query(sneaky_query)
    assert "contains 'mutation' keyword" in str(exc_info.value)


def test_query_validation_accepts_valid_query(linear_tool):
    """Test that valid query operations are accepted."""
    valid_query = """
    query {
        issues(first: 10) {
            nodes {
                id
                title
            }
        }
    }
    """
    # Mock the execute response
    linear_tool.client.execute.return_value = {
        "issues": {
            "nodes": [
                {
                    "id": "issue1",
                    "title": "Test Issue",
                    "description": None,
                    "state": {"name": "Todo"},
                    "assignee": None,
                    "updatedAt": "2024-01-01T00:00:00Z",
                }
            ]
        }
    }

    # Should not raise any validation errors
    tickets = linear_tool._execute_query(valid_query)
    assert len(tickets) == 1
    assert tickets[0].id == "issue1"
