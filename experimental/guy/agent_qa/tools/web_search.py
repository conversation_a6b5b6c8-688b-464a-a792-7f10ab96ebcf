"""Web search tool using Google Custom Search API."""

import os
import json
import requests
from pathlib import Path
from typing import Optional, List, Tuple
from dataclasses import dataclass


@dataclass
class SearchResult:
    """A search result from a web search."""

    title: str
    url: str
    snippet: str


class WebSearchError(Exception):
    """Raised when web search fails."""

    pass


def get_config_path() -> Path:
    """Get the path to the Google Search API config file.

    The path is determined by:
    1. AUGMENT_AGENT_CONFIG_DIR environment variable if set
    2. Default path ~/.augment/agent otherwise

    Returns:
        Path object pointing to the config file
    """
    config_dir = os.environ.get(
        "AUGMENT_AGENT_CONFIG_DIR", str(Path.home() / ".augment" / "agent")
    )
    return Path(config_dir) / "google_search_api_settings.json"


def get_search_credentials() -> Tuple[str, str]:
    """Get Google Custom Search API credentials.

    Checks in order:
    1. Environment variables GOOGLE_SEARCH_API_KEY and GOOGLE_SEARCH_CX
    2. Config file (location determined by get_config_path())

    Returns:
        Tuple of (api_key, search_engine_id)

    Raises:
        WebSearchError: If credentials cannot be found
    """
    # Check environment variables first
    api_key = os.environ.get("GOOGLE_SEARCH_API_KEY")
    cx = os.environ.get("GOOGLE_SEARCH_CX")

    if api_key and cx:
        return api_key, cx

    # Try to read from config file
    config_path = get_config_path()

    try:
        with open(config_path) as f:
            config = json.load(f)

        api_key = config.get("api_key")
        cx = config.get("search_engine_id")

        if not api_key or not cx:
            raise WebSearchError(
                f"Invalid config in {config_path}. "
                "Must contain 'api_key' and 'search_engine_id'."
            )

        return api_key, cx

    except FileNotFoundError:
        raise WebSearchError(
            f"Google Search API credentials not found. Either:\n"
            f"1. Set GOOGLE_SEARCH_API_KEY and GOOGLE_SEARCH_CX environment variables, or\n"
            f"2. Create config file at {config_path}"
        )
    except json.JSONDecodeError:
        raise WebSearchError(f"Invalid JSON in config file {config_path}")


def web_search(query: str, num_results: int = 5) -> List[SearchResult]:
    """Search the web using Google Custom Search API.

    Args:
        query: The search query
        num_results: Number of results to return (max 10)

    Returns:
        List of SearchResult objects

    Raises:
        WebSearchError: If the search fails
    """
    # Get API credentials
    api_key, cx = get_search_credentials()

    # Limit num_results to 10 (API maximum)
    num_results = min(num_results, 10)

    # Make API request
    url = "https://www.googleapis.com/customsearch/v1"
    params = {
        "key": api_key,
        "cx": cx,
        "q": query,
        "num": num_results,
    }

    try:
        response = requests.get(url, params=params)
        response.raise_for_status()
        data = response.json()

        # Extract search results
        results = []
        for item in data.get("items", []):
            result = SearchResult(
                title=item.get("title", ""),
                url=item.get("link", ""),
                snippet=item.get("snippet", ""),
            )
            results.append(result)

        return results

    except requests.RequestException as e:
        raise WebSearchError(f"Search request failed: {str(e)}")
    except (KeyError, ValueError) as e:
        raise WebSearchError(f"Failed to parse search results: {str(e)}")


def format_results_markdown(results: List[SearchResult]) -> str:
    """Format search results as markdown text.

    Args:
        results: List of SearchResult objects

    Returns:
        Markdown formatted string with search results
    """
    if not results:
        return "No results found."

    formatted_results = []
    for result in results:
        # Format each result as a markdown bullet point with title as link
        formatted_result = f"- [{result.title}]({result.url})\n  {result.snippet}"
        formatted_results.append(formatted_result)

    return "\n\n".join(formatted_results)
