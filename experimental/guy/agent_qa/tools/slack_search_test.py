"""Tests for Slack search functionality."""

from slack_sdk import WebClient
from slack_sdk.errors import SlackApiError
from typing import Dict, Any
import pytest


def is_public_channel(msg: Dict[str, Any]) -> bool:
    """Check if a message is from a public channel using channel ID format.

    Slack channel ID formats:
    - C... - Public or private channel
    - D... - Direct message
    - G... - Multi-person direct message or private channel (group)

    For safety, we also check that it's not marked as private if that info exists.
    """
    channel = msg.get("channel", {})
    channel_id = channel.get("id", "")

    # First check if channel is marked as private
    if channel.get("is_private", False):
        return False

    # Then check channel ID format
    # Only return True for C-prefixed channels that aren't marked private
    return channel_id.startswith("C")


def format_message(msg: Dict[str, Any]) -> str:
    """Format a message for display."""
    text = msg.get("text", "")
    user = msg.get("user", "unknown")
    channel = msg.get("channel", {}).get("name", "unknown")
    permalink = msg.get("permalink", "")
    return f"Channel #{channel}:\n{text}\n" f"By: {user}\nLink: {permalink}\n"


@pytest.mark.integration
def test_slack_search():
    """Test Slack search functionality with public channel filtering."""
    # Read tokens
    # with open("/home/<USER>/.augment/agent/slack_bot_token", "r") as f:
    #     bot_token = f.read().strip()
    with open("/home/<USER>/.augment/agent/slack_user_token", "r") as f:
        user_token = f.read().strip()

    # Create clients
    # bot_client = WebClient(token=bot_token)  # Not used
    user_client = WebClient(token=user_token)

    try:
        # Try searching with user token
        print("\nSearching with user token...")
        response = user_client.search_messages(query="test")
        matches = response.get("messages", {}).get("matches", [])
        print(f"Found {len(matches)} total messages")

        # Filter and print public channel results only
        public_matches = []
        for msg in matches:
            channel_id = msg.get("channel", {}).get("id", "unknown")
            channel_name = msg.get("channel", {}).get("name", "unknown")

            if is_public_channel(msg):
                public_matches.append(msg)
            else:
                id_type = channel_id[0] if channel_id else "unknown"
                print(
                    f"Filtered out message from channel type {id_type}: {channel_name} ({channel_id})"
                )

        print(f"\nFound {len(public_matches)} messages in public channels")

        # Print first 3 public results
        for msg in public_matches[:3]:
            print(f"\n{format_message(msg)}")

    except SlackApiError as e:
        print(f"Search failed: {str(e)}")


if __name__ == "__main__":
    test_slack_search()
