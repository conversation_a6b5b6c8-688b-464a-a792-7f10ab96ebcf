"""Tests for the SlackNotificationTool."""

from research.agents.tools import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from experimental.guy.agent_qa.tools.slack_notification_tool import (
    SlackNotificationTool,
)
import pytest


@pytest.mark.integration
def test_slack_notification():
    """Test the SlackNotificationTool's send functionality."""
    # Read bot token
    with open("/home/<USER>/.augment/agent/slack_bot_token", "r") as f:
        bot_token = f.read().strip()

    # Create logger
    logger = ToolCallLogger()

    # Create tool instance
    tool = SlackNotificationTool(
        tool_call_logger=logger,
        bot_token=bot_token,
        user_email="<EMAIL>",  # Replace with actual user email
    )

    # Test sending a message
    result = tool.run_impl({"message": "Test message from SlackNotificationTool"})
    assert "successfully sent" in result.tool_output.lower()
    assert "sent message to" in result.tool_result_message.lower()


@pytest.mark.integration
def test_slack_notification_invalid_token():
    """Test SlackNotificationTool behavior with invalid token."""
    logger = ToolCallLogger()

    # Create tool with invalid token
    tool = SlackNotificationTool(
        tool_call_logger=logger,
        bot_token="xoxb-invalid-token",
        user_email="<EMAIL>",
    )

    # Test sending a message
    result = tool.run_impl({"message": "Test message"})
    assert "failed to send message" in result.tool_output.lower()
    assert "error sending message" in result.tool_result_message.lower()


@pytest.mark.integration
def test_slack_notification_invalid_email():
    """Test SlackNotificationTool behavior with invalid email."""
    # Read bot token
    with open("/home/<USER>/.augment/agent/slack_bot_token", "r") as f:
        bot_token = f.read().strip()

    logger = ToolCallLogger()

    # Create tool with invalid email
    tool = SlackNotificationTool(
        tool_call_logger=logger,
        bot_token=bot_token,
        user_email="<EMAIL>",
    )

    # Test sending a message
    result = tool.run_impl({"message": "Test message"})
    assert "failed to send message" in result.tool_output.lower()
    assert "error sending message" in result.tool_result_message.lower()
