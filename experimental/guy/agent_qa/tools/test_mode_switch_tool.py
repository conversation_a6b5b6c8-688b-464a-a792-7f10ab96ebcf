"""Tests for mode switch tool."""

from unittest.mock import Mock, patch

from research.llm_apis.llm_client import <PERSON><PERSON>rom<PERSON>, TextResult

from experimental.guy.agent_qa.agent_mode import AgentMode
from experimental.guy.agent_qa.agent_modes import (
    WHEN_TO_USE_ARCHITECT,
    WHEN_TO_USE_IMPLEMENTOR,
    WHEN_TO_USE_RESEARCHER,
    create_architect_mode,
    create_implementor_mode,
    create_researcher_mode,
)
from experimental.guy.agent_qa.tools.mode_switch_tool import (
    AgentModeSwitcher,
    MODE_SELECTION_PROMPT,
)
import pytest


def test_initial_mode_selection():
    """Test that tool selects initial mode correctly using LLM."""
    # Create mock LLM client that suggests architect mode
    mock_llm = Mock()
    mock_llm.generate.return_value = (
        [
            TextResult(
                text="Based on the task requiring research and planning, architect mode would be most appropriate."
            )
        ],
        {},
    )

    # Create tool with mock tools for each mode
    tool1 = Mock(name="tool1")
    tool2 = Mock(name="tool2")
    tools = [tool1, tool2]

    architect_mode = create_architect_mode(tools)  # type: ignore
    implementor_mode = create_implementor_mode(tools)  # type: ignore
    researcher_mode = create_researcher_mode(tools)  # type: ignore

    tool = AgentModeSwitcher(
        tool_call_logger=Mock(),
        llm_client=mock_llm,
        architect_mode=architect_mode,
        implementor_mode=implementor_mode,
        researcher_mode=researcher_mode,
    )

    # Test initial mode selection
    dialog = Mock()
    dialog.is_assistant_turn.return_value = True
    last_instruction = "research how to implement feature X"
    expected_prompt = MODE_SELECTION_PROMPT.replace(
        "[USER_INSTRUCTION]", last_instruction
    )
    dialog.get_messages_for_llm_client.return_value = [
        [TextPrompt(text=expected_prompt)]
    ]
    dialog.get_last_user_prompt.return_value = last_instruction

    tool.update_mode(dialog)
    mode = tool.get_current_mode()
    assert mode.system_prompt == architect_mode.system_prompt
    assert mode.tools == architect_mode.tools

    # Verify LLM was called with mode descriptions
    llm_calls = mock_llm.generate.call_args_list
    assert len(llm_calls) == 1
    messages = llm_calls[0].kwargs["messages"]
    assert len(messages) == 1
    assert len(messages[0]) == 1
    prompt = messages[0][0].text
    assert WHEN_TO_USE_ARCHITECT in prompt
    assert WHEN_TO_USE_IMPLEMENTOR in prompt
    assert WHEN_TO_USE_RESEARCHER in prompt


def test_mode_switching():
    """Test that tool switches modes when used."""
    # Create mock LLM client
    mock_llm = Mock()
    mock_llm.generate.return_value = (
        [TextResult(text="architect")],  # Initial mode
        {},
    )

    # Create tool with mock tools for each mode
    tool1 = Mock(name="tool1")
    tool2 = Mock(name="tool2")
    tools = [tool1, tool2]

    architect_mode = create_architect_mode(tools)  # type: ignore
    implementor_mode = create_implementor_mode(tools)  # type: ignore
    researcher_mode = create_researcher_mode(tools)  # type: ignore

    tool = AgentModeSwitcher(
        tool_call_logger=Mock(),
        llm_client=mock_llm,
        architect_mode=architect_mode,
        implementor_mode=implementor_mode,
        researcher_mode=researcher_mode,
    )

    # Start in architect mode
    dialog = Mock()
    dialog.is_assistant_turn.return_value = True
    last_instruction = "initial task"
    expected_prompt = MODE_SELECTION_PROMPT.replace(
        "[USER_INSTRUCTION]", last_instruction
    )
    dialog.get_messages_for_llm_client.return_value = [
        [TextPrompt(text=expected_prompt)]
    ]
    dialog.get_last_user_prompt.return_value = last_instruction

    tool.update_mode(dialog)
    initial_mode = tool.get_current_mode()
    assert initial_mode.name == architect_mode.name

    # Switch to implementor mode
    result = tool.run_impl(
        {
            "mode": "implementor",
            "reason": "we have a clear plan to implement",
        }
    )

    print(result)

    # Verify mode switched
    new_mode = tool.get_current_mode()
    assert new_mode.name == implementor_mode.name

    # Switch to researcher mode
    tool.run_impl(
        {
            "mode": "researcher",
            "reason": "need to investigate and answer questions",
        }
    )

    # Verify mode switched
    new_mode = tool.get_current_mode()
    assert new_mode.name == researcher_mode.name

    # Switch back to architect mode
    tool.run_impl(
        {
            "mode": "architect",
            "reason": "need to research and plan approach",
        }
    )

    # Verify mode switched back
    final_mode = tool.get_current_mode()
    assert final_mode.name == architect_mode.name


def test_invalid_mode():
    """Test that tool validates mode names."""
    # Create tool with mock tools for each mode
    tool1 = Mock(name="tool1")
    tool2 = Mock(name="tool2")
    tools = [tool1, tool2]

    architect_mode = create_architect_mode(tools)  # type: ignore
    implementor_mode = create_implementor_mode(tools)  # type: ignore
    researcher_mode = create_researcher_mode(tools)  # type: ignore

    tool = AgentModeSwitcher(
        tool_call_logger=Mock(),
        llm_client=Mock(),
        architect_mode=architect_mode,
        implementor_mode=implementor_mode,
        researcher_mode=researcher_mode,
    )

    # Try to switch to invalid mode
    result = tool.run_impl(
        {
            "mode": "invalid_mode",
            "reason": "this mode doesn't exist",
        }
    )
    assert "Invalid mode" in result.tool_output
    assert "architect" in result.tool_output.lower()
    assert "implementor" in result.tool_output.lower()
    assert "researcher" in result.tool_output.lower()


def test_workspace_prompt_fn():
    """Test that build_workspace_prompt_fn is used correctly."""
    # Create mock LLM client
    mock_llm = Mock()
    mock_llm.generate.return_value = (
        [TextResult(text="architect")],  # Initial mode
        {},
    )

    # Create tool with mock tools for each mode
    tool1 = Mock(name="tool1")
    tool2 = Mock(name="tool2")
    tools = [tool1, tool2]

    # Create base modes
    architect_mode = create_architect_mode(tools)  # type: ignore
    implementor_mode = create_implementor_mode(tools)  # type: ignore
    researcher_mode = create_researcher_mode(tools)  # type: ignore

    # Create mock workspace prompt function
    def mock_workspace_prompt_fn() -> str:
        return "WORKSPACE INFO\nCurrent directory: /test\nOS: Linux"

    # Create tool with workspace prompt function
    tool = AgentModeSwitcher(
        tool_call_logger=Mock(),
        llm_client=mock_llm,
        architect_mode=architect_mode,
        implementor_mode=implementor_mode,
        researcher_mode=researcher_mode,
        build_workspace_prompt_fn=mock_workspace_prompt_fn,
    )

    # Switch to implementor mode
    tool.run_impl(
        {
            "mode": "implementor",
            "reason": "we have a clear plan to implement",
        }
    )

    # Get current mode and verify workspace info is appended
    mode = tool.get_current_mode()
    assert mode.name == "implementor"
    assert (
        mode.system_prompt
        == implementor_mode.system_prompt + "\n\n" + mock_workspace_prompt_fn()
    )
    assert mode.tools == implementor_mode.tools

    # Switch to researcher mode
    tool.run_impl(
        {
            "mode": "researcher",
            "reason": "need to investigate",
        }
    )

    # Verify workspace info is appended to new mode
    mode = tool.get_current_mode()
    assert mode.name == "researcher"
    assert (
        mode.system_prompt
        == researcher_mode.system_prompt + "\n\n" + mock_workspace_prompt_fn()
    )
    assert mode.tools == researcher_mode.tools
