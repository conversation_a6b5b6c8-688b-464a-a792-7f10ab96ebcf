"""Slack search tool for finding messages in public channels."""

from typing import Any, Dict, List, Optional
from slack_sdk import Web<PERSON>lient
from slack_sdk.errors import SlackApiError
from research.agents.tools import (
    LLMTool,
    ToolCallLogger,
    ToolImplOutput,
    DialogMessages,
)


class SlackSearchTool(LLMTool):
    """A tool for searching messages in public Slack channels.

    This tool requires a Slack user token (xoxp-) with search:read scope.
    The search.messages API endpoint only works with user tokens, regardless of bot token scopes.
    """

    name = "slack_search"
    description = "Search for messages in public Slack channels."

    input_schema = {
        "type": "object",
        "properties": {
            "query": {
                "type": "string",
                "description": "The search query to find messages.",
            }
        },
        "required": ["query"],
    }

    def __init__(
        self,
        tool_call_logger: ToolCallLogger,
        user_token: str,
    ):
        """Initialize the Slack search tool.

        Args:
            tool_call_logger: Logger for tool calls
            user_token: Slack user token (xoxp-) for search operations
        """
        super().__init__(tool_call_logger)
        self.user_client = WebClient(token=user_token)

    def run_impl(
        self,
        tool_input: Dict[str, Any],
        dialog_messages: Optional[DialogMessages] = None,
    ) -> ToolImplOutput:
        """Run the Slack search tool.

        Args:
            tool_input: Dictionary containing the search query
            dialog_messages: Optional dialog context (not used)

        Returns:
            ToolImplOutput containing search results in markdown format
        """
        query = tool_input["query"]

        try:
            # Use the user client for search
            response = self.user_client.search_messages(query=query)

            # Get all matches
            matches = response.get("messages", {}).get("matches", [])

            # Filter to only include messages from public channels
            public_matches = []
            for match in matches:
                channel_info = match.get("channel", {})
                # Only include messages from public channels
                if not channel_info.get("is_private", True):
                    public_matches.append(
                        {
                            "text": match.get("text", ""),
                            "user": match.get("username", "Unknown"),
                            "channel": channel_info.get("name", "Unknown"),
                            "timestamp": match.get("ts", ""),
                            "permalink": match.get("permalink", ""),
                        }
                    )

            # Format results in markdown
            if not public_matches:
                return ToolImplOutput(
                    tool_output="No matching messages found in public channels.",
                    tool_result_message="No results found",
                )

            # Build markdown output
            markdown = "### Search Results\n\n"
            for match in public_matches:
                markdown += f"**Channel:** #{match['channel']}\n"
                markdown += f"**User:** {match['user']}\n"
                markdown += f"**Message:**\n{match['text']}\n"
                if match["permalink"]:
                    markdown += f"[View in Slack]({match['permalink']})\n"
                markdown += "\n---\n\n"

            return ToolImplOutput(
                tool_output=markdown,
                tool_result_message=f"Found {len(public_matches)} messages",
            )

        except SlackApiError as e:
            return ToolImplOutput(
                tool_output=f"Failed to search messages: {str(e)}",
                tool_result_message=f"Error searching messages: {str(e)}",
            )
