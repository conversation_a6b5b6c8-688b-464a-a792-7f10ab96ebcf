"""Tests for the scratchpad tool."""

import json
import tempfile
import time
from pathlib import Path
from unittest.mock import Magic<PERSON>ock

import pytest

from experimental.guy.agent_qa.tools.scratchpad_tool import ScratchpadTool


@pytest.fixture
def mock_tool_call_logger():
    """Create a mock tool call logger."""
    return MagicMock()


@pytest.fixture
def temp_cache_dir():
    """Create a temporary directory for cache files."""
    with tempfile.TemporaryDirectory() as temp_dir:
        yield Path(temp_dir)


@pytest.fixture
def temp_workspace_root():
    """Create a temporary directory for workspace root."""
    with tempfile.TemporaryDirectory() as temp_dir:
        yield Path(temp_dir)


@pytest.fixture
def scratchpad_tool(mock_tool_call_logger, temp_cache_dir, temp_workspace_root):
    """Create a scratchpad tool instance."""
    return ScratchpadTool(
        tool_call_logger=mock_tool_call_logger,
        cache_dir=temp_cache_dir,
        workspace_root=temp_workspace_root,
    )


def test_initialization(scratchpad_tool, temp_cache_dir):
    """Test that the scratchpad tool initializes correctly."""
    # Check that the scratchpad file was created
    assert scratchpad_tool.scratchpad_file.exists()

    # Check that the file contains the expected initial structure
    with open(scratchpad_tool.scratchpad_file, "r") as f:
        data = json.load(f)

    assert "entries" in data
    assert isinstance(data["entries"], list)
    assert "created_at" in data
    assert "last_updated" in data


def test_add_entry(scratchpad_tool):
    """Test adding an entry to the scratchpad."""
    # Add an entry
    result = scratchpad_tool.run_impl(
        {"action": "add", "content": "Test entry content"}
    )

    # Check the response
    assert "Added new entry" in result.tool_output
    assert "1 entries" in result.tool_output

    # Check that the entry was saved
    with open(scratchpad_tool.scratchpad_file, "r") as f:
        data = json.load(f)

    assert len(data["entries"]) == 1
    assert data["entries"][0]["content"] == "Test entry content"
    assert "timestamp" in data["entries"][0]


def test_read_empty_scratchpad(scratchpad_tool):
    """Test reading an empty scratchpad."""
    result = scratchpad_tool.run_impl({"action": "read"})
    assert "The scratchpad is empty" in result.tool_output


def test_read_with_entries(scratchpad_tool):
    """Test reading a scratchpad with entries."""
    # Add entries
    scratchpad_tool.run_impl({"action": "add", "content": "First entry"})
    scratchpad_tool.run_impl({"action": "add", "content": "Second entry"})

    # Read the scratchpad
    result = scratchpad_tool.run_impl({"action": "read"})

    # Check the response
    assert "# Scratchpad" in result.tool_output
    assert "## Entry 1" in result.tool_output
    assert "First entry" in result.tool_output
    assert "## Entry 2" in result.tool_output
    assert "Second entry" in result.tool_output


def test_clear_scratchpad(scratchpad_tool):
    """Test clearing the scratchpad."""
    # Add an entry
    scratchpad_tool.run_impl({"action": "add", "content": "Test entry"})

    # Clear the scratchpad
    result = scratchpad_tool.run_impl({"action": "clear"})
    assert "Scratchpad cleared successfully" in result.tool_output

    # Check that the scratchpad is empty
    with open(scratchpad_tool.scratchpad_file, "r") as f:
        data = json.load(f)

    assert len(data["entries"]) == 0


def test_invalid_action(scratchpad_tool):
    """Test providing an invalid action."""
    result = scratchpad_tool.run_impl({"action": "invalid"})
    assert "Invalid action" in result.tool_output


def test_add_without_content(scratchpad_tool):
    """Test adding an entry without content."""
    result = scratchpad_tool.run_impl({"action": "add"})
    assert "Error" in result.tool_output
    assert "'content' is required" in result.tool_output


def test_workspace_specific_scratchpads(mock_tool_call_logger, temp_cache_dir):
    """Test that different workspaces get different scratchpads."""
    # Create two workspace roots
    with tempfile.TemporaryDirectory() as temp_dir1, tempfile.TemporaryDirectory() as temp_dir2:
        workspace1 = Path(temp_dir1)
        workspace2 = Path(temp_dir2)

        # Create two scratchpad tools with different workspaces
        scratchpad1 = ScratchpadTool(
            tool_call_logger=mock_tool_call_logger,
            cache_dir=temp_cache_dir,
            workspace_root=workspace1,
        )

        scratchpad2 = ScratchpadTool(
            tool_call_logger=mock_tool_call_logger,
            cache_dir=temp_cache_dir,
            workspace_root=workspace2,
        )

        # Add entries to each scratchpad
        scratchpad1.run_impl({"action": "add", "content": "Workspace 1 entry"})

        scratchpad2.run_impl({"action": "add", "content": "Workspace 2 entry"})

        # Check that each scratchpad has its own entries
        result1 = scratchpad1.run_impl({"action": "read"})
        result2 = scratchpad2.run_impl({"action": "read"})

        assert "Workspace 1 entry" in result1.tool_output
        assert "Workspace 2 entry" not in result1.tool_output

        assert "Workspace 2 entry" in result2.tool_output
        assert "Workspace 1 entry" not in result2.tool_output
