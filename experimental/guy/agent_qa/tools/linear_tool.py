"""Tool for querying Linear tickets using natural language."""

import json
from dataclasses import dataclass
from typing import Any, Dict, List, Optional
from jinja2 import Template

from gql import Client, gql
from gql.transport.requests import RequestsHTTPTransport
from research.agents.tools import (
    <PERSON><PERSON><PERSON>,
    <PERSON>lCallLogger,
    ToolImplOutput,
    DialogMessages,
)
from research.llm_apis.llm_client import LL<PERSON>lient, TextPrompt
from experimental.guy.agent_qa.integration_warnings import IntegrationWarnings


@dataclass
class LinearTicket:
    """Represents a Linear ticket with essential fields."""

    id: str
    title: str
    description: Optional[str]
    state: str
    assignee: Optional[str]
    updated_at: str

    @classmethod
    def from_node(cls, node: Dict[str, Any]) -> "LinearTicket":
        """Create a LinearTicket from a GraphQL node."""
        assignee = node.get("assignee")
        return cls(
            id=node["id"],
            title=node["title"],
            description=node.get("description"),
            state=node["state"]["name"],
            assignee=assignee["email"] if assignee else None,
            updated_at=node["updatedAt"],
        )

    def to_dict(self) -> Dict[str, Any]:
        """Convert ticket to dictionary for JSON serialization."""
        return {
            "id": self.id,
            "title": self.title,
            "description": self.description,
            "state": self.state,
            "assignee": self.assignee,
            "updated_at": self.updated_at,
        }


class LinearTool(LLMTool):
    """Tool for querying Linear tickets using natural language."""

    name = "query_linear"
    description = """\
    Query Linear tickets using natural language. Examples:
    - "show my tickets"
    - "find tickets containing foo"
    - "show my recent ticket"

    This tool cannot modify tickets, only query them.
    """

    input_schema = {
        "type": "object",
        "properties": {
            "query": {
                "type": "string",
                "description": "Natural language query for Linear tickets",
            },
            "user_email": {
                "type": "string",
                "description": "User's email for identifying their tickets",
            },
        },
        "required": ["query", "user_email"],
    }

    QUERY_GENERATION_PROMPT = Template("""\
You are an expert at converting natural language queries about Linear tickets into GraphQL queries.
The available fields in the GraphQL schema are:
- id: Internal ID of the ticket
- identifier: The human-readable ticket ID (e.g., AU-1234). Always include this field in queries.
- title: Title of the ticket
- description: Description of the ticket
- state: { name: string } - State of the ticket
- assignee: { email: string } - Email of the assignee
- updatedAt: Timestamp of last update

Filter examples:
- Filter by assignee: assignee: { email: { eq: "{{ user_email }}" } }
- Filter by title: title: { contains: "search term" }
- Filter by state: state: { type: { in: ["started", "unstarted"] } }

Sorting:
- Sort by update time: orderBy: updatedAt
- Sort by creation time: orderBy: createdAt

Example query to read a ticket's details based on its ID:

    query {
        issue(id: "RU-6316") {
            id
            identifier  # Always include this field
            title
            description
            state {
                name
            }
            assignee {
                name
                email
            }
            labels {
                nodes {
                    name
                }
            }
            team {
                name
            }
            createdAt
            updatedAt
        }
    }

Example query to find tickets containing "database":

    query {           
        issues(filter: { title: { containsIgnoreCase: "database" } }) {
            nodes {  
                id
                identifier  # Always include this field
                title                                                                     
                state {
                    name   
                }     
                assignee {
                    name 
                } 
                createdAt  
                priority 
            }     
        }                 
    }      

Example query to find a user's 10 recently assigned tickets:
                                       
    query {
        issues(
            first: 10,
            orderBy: updatedAt,
            filter: {
                assignee: { email: { eq: "<EMAIL>" } }
            }
        ) {
            nodes {
                id
                identifier  # Always include this field
                title
                state {
                    name
                }
                createdAt
                updatedAt
                priority
                team {
                    name
                }
                labels {
                    nodes {
                        name
                    }
                }
            }
        }
    }

Now for the task at hand.

The user's email is: {{ user_email }}

Convert this natural language query into a GraphQL query:
{{ query }}

Return only the GraphQL query, no other text. The query should be wrapped in a 'query' operation,
like in the examples above. Remember to always include the 'identifier' field in your queries.
""")

    def __init__(
        self,
        api_key: str,
        llm_client: LLMClient,
        tool_call_logger: ToolCallLogger,
        integration_warnings: Optional[IntegrationWarnings] = None,
    ):
        """Initialize the Linear tool.

        Args:
            api_key: Linear API key
            llm_client: Language model client for query generation
            tool_call_logger: Tool call logger
            integration_warnings: Optional integration warnings handler
        """
        super().__init__(tool_call_logger)
        self.transport = RequestsHTTPTransport(
            url="https://api.linear.app/graphql",
            headers={"Authorization": api_key},
        )
        try:
            self.client = Client(
                transport=self.transport
            )  # This will raise if credentials are missing
        except Exception:  # Linear client may raise different exceptions
            if integration_warnings:
                integration_warnings.warn_if_missing("linear")
            raise
        self.llm_client = llm_client

    def _generate_graphql_query(self, natural_query: str, user_email: str) -> str:
        """Generate a GraphQL query using the language model.

        Args:
            natural_query: Natural language query
            user_email: User's email

        Returns:
            GraphQL query string
        """
        prompt = self.QUERY_GENERATION_PROMPT.render(
            query=natural_query,
            user_email=user_email,
        )
        response, _ = self.llm_client.generate(
            messages=[[TextPrompt(text=prompt)]],
            max_tokens=8192,  # Standard max tokens value
        )
        # Response is a list of blocks, get the first one
        if isinstance(response, list):
            return response[0].text.strip()
        # Response is a single TextResult
        return response.text.strip()

    def _validate_query(self, query: str) -> None:
        """Validate that the query is a read-only operation.

        Args:
            query: GraphQL query string

        Raises:
            ValueError: If the query contains mutations or is not a valid query operation
        """
        # Remove comments and whitespace for cleaner validation
        cleaned_query = "\n".join(
            line for line in query.split("\n") if not line.strip().startswith("#")
        ).strip()

        # Check if it's a query operation
        if not cleaned_query.startswith("query"):
            raise ValueError(
                "Invalid operation: Only 'query' operations are allowed. "
                "Mutations are not supported for safety."
            )

        # Additional safety check for mutation keywords
        if "mutation" in cleaned_query.lower():
            raise ValueError(
                "Query contains 'mutation' keyword which is not allowed "
                "for safety reasons."
            )

    def _execute_query(self, query: str) -> List[LinearTicket]:
        """Execute a GraphQL query and return parsed tickets.

        Args:
            query: GraphQL query string

        Returns:
            List of LinearTicket objects

        Raises:
            ValueError: If the query is not a valid read-only operation
        """
        self._validate_query(query)
        result = self.client.execute(gql(query))
        
        # Handle single issue query
        if 'issue' in result:
            return [LinearTicket.from_node(result['issue'])]
            
        # Handle multiple issues query
        if 'issues' in result and 'nodes' in result['issues']:
            return [LinearTicket.from_node(node) for node in result['issues']['nodes']]
            
        return []  # No tickets found

    def run_impl(
        self,
        tool_input: Dict[str, Any],
        dialog_messages: Optional[DialogMessages] = None,
    ) -> ToolImplOutput:
        """Run the tool implementation.

        Args:
            tool_input: Dictionary with "query" and "user_email" keys
            dialog_messages: Optional dialog messages

        Returns:
            ToolImplOutput with formatted ticket information
        """
        natural_query = tool_input["query"]
        user_email = tool_input["user_email"]

        try:
            # Generate and execute GraphQL query
            graphql_query = self._generate_graphql_query(natural_query, user_email)
            tickets = self._execute_query(graphql_query)

            # Format results
            if not tickets:
                return ToolImplOutput(
                    tool_output="No tickets found",
                    tool_result_message="Query returned no results",
                )

            # Format tickets as markdown
            formatted_tickets = []
            for ticket in tickets:
                ticket_md = [
                    f"## {ticket.title} ({ticket.id})",
                    f"**State:** {ticket.state}",
                ]
                if ticket.assignee:
                    ticket_md.append(f"**Assignee:** {ticket.assignee}")
                if ticket.description:
                    ticket_md.append(f"\n{ticket.description}")
                formatted_tickets.append("\n".join(ticket_md))

            result = "\n\n".join(formatted_tickets)
            return ToolImplOutput(
                tool_output=result,
                tool_result_message=f"Found {len(tickets)} tickets",
            )

        except Exception as e:
            return ToolImplOutput(
                tool_output=f"Failed to query Linear: {str(e)}",
                tool_result_message=f"Query failed: {str(e)}",
            )
