"""Slack notification tool for sending messages to users."""

from typing import Any, Dict, Optional
from slack_sdk import WebClient
from slack_sdk.errors import SlackApiError
from research.agents.tools import (
    LLMTool,
    ToolCallLogger,
    ToolImplOutput,
    DialogMessages,
)


class SlackNotificationTool(LLMTool):
    """A tool for sending Slack messages to users.

    This tool requires a Slack bot token (xoxb-) with chat:write and users:read.email scopes.
    """

    name = "slack_notification"
    description = "Send notifications to a Slack user."

    input_schema = {
        "type": "object",
        "properties": {
            "message": {
                "type": "string",
                "description": "The message to send to the user.",
            }
        },
        "required": ["message"],
    }

    def __init__(
        self,
        tool_call_logger: ToolCallLogger,
        user_email: str,
        bot_token: str,
    ):
        """Initialize the Slack notification tool.

        Args:
            tool_call_logger: Logger for tool calls
            user_email: Email of the user to send messages to
            bot_token: Slack bot token (xoxb-) for sending messages
        """
        super().__init__(tool_call_logger)
        self.user_email = user_email
        self.bot_client = WebClient(token=bot_token)

    def run_impl(
        self,
        tool_input: Dict[str, Any],
        dialog_messages: Optional[DialogMessages] = None,
    ) -> ToolImplOutput:
        """Run the Slack notification tool.

        Args:
            tool_input: Dictionary containing the message
            dialog_messages: Optional dialog context (not used)

        Returns:
            ToolImplOutput containing success/failure message
        """
        message = tool_input["message"]

        try:
            # Get user ID from email using bot client
            response = self.bot_client.users_lookupByEmail(email=self.user_email)
            user_id = response["user"]["id"]

            # Send message using bot client
            self.bot_client.chat_postMessage(channel=user_id, text=message)
            return ToolImplOutput(
                tool_output=f"Message successfully sent to {self.user_email}",
                tool_result_message=f"Sent message to {self.user_email}",
            )
        except SlackApiError as e:
            return ToolImplOutput(
                tool_output=f"Failed to send message: {str(e)}",
                tool_result_message=f"Error sending message: {str(e)}",
            )
