"""Utilities for working with code nodes."""

from typing import Optional
from tree_sitter import Node

# Map of file extensions to tree-sitter language names
LANGUAGE_MAP = {
    ".py": "python",
    ".js": "javascript",
    ".ts": "typescript",
    ".go": "go",
    ".rs": "rust",
    ".cpp": "cpp",
    ".c": "c",
    ".java": "java",
}

__all__ = [
    "LANGUAGE_MAP",
    "get_node_name",
    "get_node_text",
    "get_block_content",
    "get_block_content_from_node",
    "get_signature",
    "find_node",
]


def get_node_name(node: Node, node_type: str) -> str:
    """Extract name from a node based on its type."""
    if node_type == "function":
        # For functions, look for identifier
        for child in node.children:
            if child.type == "identifier":
                return child.text.decode("utf-8")
    elif node_type == "class":
        # For classes, look for identifier
        for child in node.children:
            if child.type == "identifier":
                return child.text.decode("utf-8")
    elif node_type == "type":
        # For Go types, look in type_spec -> type_identifier
        for child in node.children:
            if child.type == "type_spec":
                for subchild in child.children:
                    if subchild.type == "type_identifier":
                        return subchild.text.decode("utf-8")
    return ""


def find_node(
    root: Node, source_bytes: bytes, node_spec: str, debug: bool = False
) -> Optional[Node]:
    """Find a node by its type:name specification.

    Args:
        root: Root node to search from
        source_bytes: Source file bytes
        node_spec: Node specification in format type:name (e.g., 'class:MyClass')
        debug: Whether to print debug information

    Returns:
        The matching node, or None if not found
    """
    if not node_spec:
        return None

    try:
        node_type, node_name = node_spec.split(":")
    except ValueError:
        return None

    def visit(node: Node) -> Optional[Node]:
        if debug:
            print(f"Visiting node: {node.type}")
            print(
                f"Text: {source_bytes[node.start_byte:node.end_byte].decode('utf-8')[:50]}..."
            )

        # Get node type
        if node.type in (
            "class_definition",
            "function_definition",
            "method_definition",
        ):
            cur_type = node.type.split("_")[0]  # 'class' or 'function'
            cur_name = get_node_name(node, cur_type)
            if debug:
                print(f"Python node: {cur_type}:{cur_name}")
            if cur_type == node_type and cur_name == node_name:
                return node
        elif node.type == "type_declaration":
            cur_name = get_node_name(node, "type")
            if debug:
                print(f"Go type: {cur_name}")
            if node_type == "type" and cur_name == node_name:
                return node
        elif node.type == "function_declaration":
            cur_name = get_node_name(node, "function")
            if debug:
                print(f"Go function: {cur_name}")
            if node_type == "function" and cur_name == node_name:
                return node
        elif node.type == "method_declaration":
            # For Go methods, look for field_identifier
            for child in node.children:
                if child.type == "field_identifier":
                    cur_name = child.text.decode("utf-8")
                    if debug:
                        print(f"Go method: {cur_name}")
                    if node_type == "function" and cur_name == node_name:
                        return node

        # Recurse into children
        for child in node.children:
            result = visit(child)
            if result:
                return result
        return None

    return visit(root)


def get_node_text(node: Node, source_bytes: bytes) -> str:
    """Get the full text of a node."""
    return source_bytes[node.start_byte : node.end_byte].decode("utf-8")


def get_block_content(node: Node, source_bytes: bytes) -> str:
    """Extract content between braces/colons and end of block."""
    text = source_bytes[node.start_byte : node.end_byte].decode("utf-8")
    if ":" in text:
        block_start = text.find(":") + 1
        lines = text[block_start:].strip().split("\n")
        # Remove empty lines and normalize indentation
        lines = [line.strip() for line in lines if line.strip()]
        # Skip docstrings and comments
        lines = [
            line
            for line in lines
            if not line.startswith('"""') and not line.startswith("#")
        ]
        # For Python, use 4-space indentation
        return "\n".join("    " + line for line in lines)
    elif "{" in text:
        block_start = text.find("{") + 1
        block_end = text.rfind("}")
        if block_end > block_start:
            lines = text[block_start:block_end].strip().split("\n")
            # Remove empty lines and normalize indentation
            lines = [line.strip() for line in lines if line.strip()]
            # Skip comments
            lines = [line for line in lines if not line.startswith("//")]
            # For Go, use 4-space indentation
            return "\n".join("    " + line for line in lines)
    return ""


def get_block_content_from_node(node: Node, source_bytes: bytes) -> str:
    """Extract block content from a node by looking at its block child."""
    for child in node.children:
        if child.type == "block":
            # For Python blocks
            block_text = source_bytes[child.start_byte : child.end_byte].decode("utf-8")
            lines = block_text.strip().split("\n")
            # Remove empty lines and normalize indentation
            lines = [line.strip() for line in lines if line.strip()]
            # Skip docstrings and comments
            lines = [
                line
                for line in lines
                if not line.startswith('"""') and not line.startswith("#")
            ]
            # For Python, use 4-space indentation
            return "\n".join("    " + line for line in lines)
        elif child.type == "field_declaration_list":
            # For Go struct fields
            block_text = source_bytes[child.start_byte : child.end_byte].decode("utf-8")
            lines = block_text.strip().split("\n")
            # Remove empty lines and normalize indentation
            lines = [line.strip() for line in lines if line.strip()]
            # Skip comments
            lines = [line for line in lines if not line.startswith("//")]
            # For Go, use 4-space indentation
            return "\n".join("    " + line for line in lines)
        elif child.type == "struct_type":
            # For Go struct fields
            for subchild in child.children:
                if subchild.type == "field_declaration_list":
                    block_text = source_bytes[
                        subchild.start_byte : subchild.end_byte
                    ].decode("utf-8")
                    lines = block_text.strip().split("\n")
                    # Remove empty lines and normalize indentation
                    lines = [line.strip() for line in lines if line.strip()]
                    # Skip comments
                    lines = [line for line in lines if not line.startswith("//")]
                    # For Go, use 4-space indentation
                    return "\n".join("    " + line for line in lines)
    return ""


def get_signature(node: Node, source_bytes: bytes) -> str:
    """Extract the signature part of a function/class definition."""
    sig_text = source_bytes[node.start_byte : node.end_byte].decode("utf-8")
    if ":" in sig_text:
        sig_end = sig_text.find(":")
        return sig_text[:sig_end].strip()
    elif "{" in sig_text:
        sig_end = sig_text.find("{")
        return sig_text[:sig_end].strip()
    return sig_text.strip()
