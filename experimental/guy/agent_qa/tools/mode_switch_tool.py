"""Tool for recording mode switch recommendations and managing mode selection."""

from copy import deepcopy
from pathlib import Path
import platform
from typing import Any, Callable, Dict, Optional

from research.agents.tools import (
    <PERSON>alogMessages,
    LLMTool,
    ToolCallLogger,
    ToolImplOutput,
    ToolParam,
)
from research.llm_apis.llm_client import <PERSON><PERSON><PERSON>, TextPrompt

from experimental.guy.agent_qa.agent_mode import <PERSON><PERSON><PERSON>, Agent<PERSON>ode<PERSON>rovider
from experimental.guy.agent_qa.agent_modes import (
    WHEN_TO_USE_ARCHITECT,
    WHEN_TO_USE_IMPLEMENTOR,
    WHEN_TO_USE_RESEARCHER,
)
from experimental.guy.agent_qa.builtin_tools import Codebase<PERSON>nowledgeTool
from experimental.guy.agent_qa.memories import Memories
from experimental.guy.agent_qa.changes import (
    RecentChangesMode,
    RecentChangesProvider,
)


MODE_SELECTION_PROMPT = f"""\
The next user instruction is:
[USER_INSTRUCTION]

Given the dialog history, and the last user instruction, determine which mode would be most appropriate to use for accomplishing the task.

Available modes:

ARCHITECT MODE:
{WHEN_TO_USE_ARCHITECT}

IMPLEMENTOR MODE:
{WHEN_TO_USE_IMPLEMENTOR}

RESEARCHER MODE:
{WHEN_TO_USE_RESEARCHER}

Which mode would be most appropriate to use?
Reply with just the mode name in lowercase: either "architect", "implementor", or "researcher"
"""


DEFAULT_MODE = "researcher"


class AgentModeSwitcher(LLMTool, AgentModeProvider):
    """Tool for recording mode switch recommendations and managing mode selection.

    This class serves two purposes:
    1. As a tool, it allows the agent to recommend mode switches
    2. As a provider, it manages mode selection and switching
    """

    name = "mode_switch"
    description = """\
Use this tool when you believe the task would be better handled by a different mode.
The tool will record your recommendation to switch modes."""

    input_schema = {
        "type": "object",
        "properties": {
            "mode": {
                "type": "string",
                "description": "The mode to switch to (architect, implementor, or researcher)",
                "enum": ["architect", "implementor", "researcher"],
            },
            "reason": {
                "type": "string",
                "description": "Why this mode would be better for the current task",
            },
        },
        "required": ["mode", "reason"],
    }

    def __init__(
        self,
        llm_client: LLMClient,
        tool_call_logger: ToolCallLogger,
        architect_mode: AgentMode,
        implementor_mode: AgentMode,
        researcher_mode: AgentMode,
        initial_mode: str = DEFAULT_MODE,
        build_workspace_prompt_fn: Optional[Callable[[], str]] = None,
    ):
        """Initialize the tool.

        Args:
            llm_client: Client for LLM calls
            tool_call_logger: Logger for tool calls
            architect_mode: The architect mode configuration
            implementor_mode: The implementor mode configuration
            researcher_mode: The researcher mode configuration
        """
        super().__init__(tool_call_logger)
        self.llm_client = llm_client
        self.current_mode = initial_mode
        self._modes: Dict[str, AgentMode] = {
            "architect": architect_mode,
            "implementor": implementor_mode,
            "researcher": researcher_mode,
        }
        self.dialog = DialogMessages()
        self.build_workspace_prompt_fn = build_workspace_prompt_fn

    def run_impl(
        self,
        tool_input: dict[str, Any],
        dialog_messages: Optional[DialogMessages] = None,
    ) -> ToolImplOutput:
        """Record a mode switch recommendation.

        Args:
            tool_input: Dictionary with 'mode' and 'reason'
            dialog_messages: Optional dialog history

        Returns:
            ToolImplOutput with the recommendation

        Raises:
            ValueError: If the requested mode doesn't exist
        """
        mode = tool_input["mode"]
        reason = tool_input["reason"]

        # Validate mode exists
        if mode not in self._modes:
            valid_modes = ", ".join(sorted(self._modes.keys()))
            return ToolImplOutput(
                tool_output=f"Invalid mode: {mode}. Valid modes are: {valid_modes}",
                tool_result_message=f"Invalid mode {mode}",
            )

        # Update current mode
        self.current_mode = mode

        message = f"Switching to {mode} mode because: {reason}"
        return ToolImplOutput(
            tool_output=message,
            tool_result_message=message,
        )

    def get_current_mode(self) -> AgentMode:
        """Get the current mode configuration.

        Returns:
            The current AgentMode configuration with system prompt suffix appended if set
        """
        mode = self._modes[self.current_mode]

        # Only append suffix if we have a function and it returns non-empty string
        system_prompt = mode.system_prompt
        if self.build_workspace_prompt_fn is not None:
            suffix = self.build_workspace_prompt_fn()
            if suffix:
                system_prompt = system_prompt + "\n\n" + suffix

        # Create a copy of the mode with the updated system prompt
        mode = AgentMode(
            name=mode.name,
            system_prompt=system_prompt,
            tools=mode.tools,
        )
        return mode

    def update_mode(self, dialog_messages: DialogMessages) -> None:
        """Update the current mode based on dialog history.

        Args:
            dialog_messages: The current dialog history
        """

        print("update_mode: starts with", self.current_mode)

        # Only update mode if it's the assistant's turn
        if not dialog_messages.is_assistant_turn():
            print("update_mode: is assistant turn, returning")
            return

        mode_selection_messages = deepcopy(dialog_messages)
        last_instruction = mode_selection_messages.get_last_user_prompt()
        mode_selection_prompt = MODE_SELECTION_PROMPT.replace(
            "[USER_INSTRUCTION]", last_instruction
        )
        mode_selection_messages.replace_last_user_prompt(mode_selection_prompt)

        # Get LLM's recommendation
        dummy_tool = ToolParam(
            name="dummy",
            description="Dummy tool, do not call me.",
            input_schema={
                "type": "object",
                "properties": {
                    "dummy_input": {
                        "type": "string",
                        "description": "Dummy input",
                    },
                },
                "required": ["dummy_input"],
            },
        )

        response, _ = self.llm_client.generate(
            messages=mode_selection_messages.get_messages_for_llm_client(),
            max_tokens=100,
            temperature=0,  # Use deterministic output
            tools=[dummy_tool],
            tool_choice=None,
        )

        # Default to architect if no mode found
        self.current_mode = DEFAULT_MODE

        # Parse response - look for mode name in the response
        text = response[0].text.strip().lower()
        for mode in self._modes:
            if mode in text:
                self.current_mode = mode

        print("update_mode: new mode is", self.current_mode)
