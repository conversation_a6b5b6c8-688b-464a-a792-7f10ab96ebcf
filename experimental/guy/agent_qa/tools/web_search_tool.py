"""Web search tool for the agent."""

from typing import Any, Dict, Optional
from research.agents.tools import (
    <PERSON><PERSON><PERSON>,
    Tool<PERSON>allLogger,
    ToolImplOutput,
    DialogMessages,
)
from experimental.guy.agent_qa.tools.web_search import (
    web_search,
    format_results_markdown,
    WebSearchError,
    get_search_credentials,
)
from experimental.guy.agent_qa.integration_warnings import IntegrationWarnings


class WebSearchTool(LLMTool):
    """A tool that performs web searches using Google Custom Search API."""

    name = "google_search"
    description = """\
Search the web for information. Returns results in markdown format.
Each result includes the URL, title, and a snippet from the page if available.

This tool uses Google's Custom Search API to find relevant web pages."""

    input_schema = {
        "type": "object",
        "properties": {
            "query": {
                "type": "string",
                "description": "The search query to send.",
            },
            "num_results": {
                "type": "integer",
                "description": "Number of results to return (default: 5, max: 10).",
            },
        },
        "required": ["query"],
    }

    def __init__(
        self,
        tool_call_logger: <PERSON><PERSON><PERSON><PERSON><PERSON>og<PERSON>,
        integration_warnings: Optional[IntegrationWarnings] = None,
    ):
        """Initialize the tool."""
        super().__init__(tool_call_logger)
        self._credentials_available = False
        try:
            get_search_credentials()
            self._credentials_available = True
        except WebSearchError:
            if integration_warnings:
                integration_warnings.warn_if_missing("web_search", fail=False)

    def run_impl(
        self,
        tool_input: Dict[str, Any],
        dialog_messages: Optional[DialogMessages] = None,
    ) -> ToolImplOutput:
        """Run the web search tool.

        Args:
            tool_input: Dictionary with 'query' and optional 'num_results'
            dialog_messages: Not used

        Returns:
            ToolImplOutput with formatted search results
        """
        if not self._credentials_available:
            return ToolImplOutput(
                tool_output=(
                    "Web search is not available because Google Search API credentials "
                    "are not configured. Please set up credentials in "
                    "~/.augment/agent/google_search_api_settings.json or use "
                    "GOOGLE_SEARCH_API_KEY and GOOGLE_SEARCH_CX environment variables."
                ),
                tool_result_message="Search not available - missing credentials",
            )

        query = tool_input["query"]
        num_results = tool_input.get("num_results", 5)

        try:
            results = web_search(query, num_results=num_results)
            markdown = format_results_markdown(results)
            return ToolImplOutput(
                tool_output=markdown,
                tool_result_message=f"Found {len(results)} results",
            )

        except WebSearchError as e:
            error_message = f"Web search failed: {str(e)}"
            return ToolImplOutput(
                tool_output=f"No results found. {error_message}",
                tool_result_message="Search failed",
            )