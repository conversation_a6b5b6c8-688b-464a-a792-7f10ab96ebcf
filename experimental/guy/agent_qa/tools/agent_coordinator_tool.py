"""Tool for coordinating communication between agents."""

import jsonschema
from typing import Any, Optional

from experimental.guy.agent_qa.agent_coordinator_interface import AgentCoordinator
from research.agents.tools import (
    DialogMessages,
    LLMTool,
    ToolCallLogger,
    ToolImplOutput,
)


class AgentCoordinatorTool(LLMTool):
    """Tool for coordinating communication between agents.

    This tool wraps an AgentCoordinator implementation, allowing agents to
    notify other agents about their current actions.
    """

    name = "agent_coordinator"
    description = """\
A tool for coordinating code changes between agents to prevent merge conflicts. Use this tool to inform other agents about files and code sections you are modifying.

When making changes to the codebase, you should:
1. Notify other agents about which files you're editing
2. Specify which classes/functions/sections you're modifying
3. Explain the motivation/purpose of your changes
4. Check messages from other agents to see if anyone is working on the same files

If you notice another agent is working on the same files or related code:
- Review their changes to understand potential conflicts
- Coordinate with them through messages to avoid overlapping changes
- Consider sequencing the changes or splitting the work differently

Messages from other agents will appear automatically in your system prompt.

The tool supports two actions:
notify - Let other agents know what you're doing (requires a message)
get_messages - Get messages from other agents

Example notification:
"I'm modifying the UserAuthenticator class in auth/authenticator.py to add OAuth support. Changes will affect the authenticate() and validate_token() methods."
"""

    input_schema = {
        "type": "object",
        "properties": {
            "action": {
                "type": "string",
                "enum": ["notify", "get_messages"],
                "description": "The action to perform: notify or get_messages",
            },
            "message": {
                "type": "string",
                "description": "The message to send (required for notify action)",
            },
        },
        "required": ["action"],
    }

    def __init__(
        self,
        tool_call_logger: ToolCallLogger,
        coordinator: AgentCoordinator,
        agent_name: str,
    ):
        """Initialize the tool.

        Args:
            tool_call_logger: Logger for tool calls
            coordinator: The coordinator implementation to use
            agent_name: Name of the agent using this tool
        """
        super().__init__(tool_call_logger)
        self.coordinator = coordinator
        self.agent_name = agent_name

    def run_impl(
        self,
        tool_input: dict[str, Any],
        dialog_messages: Optional[DialogMessages] = None,
    ) -> ToolImplOutput:
        """Run the tool implementation.

        Args:
            tool_input: The tool input containing action and optional message
            dialog_messages: Optional dialog messages

        Returns:
            Tool output with success/error message
        """
        try:
            # Validate input first
            self._validate_tool_input(tool_input)

            action = tool_input["action"]

            if action == "notify":
                # Check for required message field
                if "message" not in tool_input:
                    error_msg = "Message is required for notify action"
                    return ToolImplOutput(
                        tool_output=error_msg,
                        tool_result_message=error_msg,
                    )

                message = tool_input["message"]
                self.coordinator.notify_current_action(self.agent_name, message)
                return ToolImplOutput(
                    tool_output="Successfully notified other agents.",
                    tool_result_message="Message sent successfully.",
                )

            elif action == "get_messages":
                messages = self.coordinator.get_messages(self.agent_name)
                if not messages:
                    return ToolImplOutput(
                        tool_output="No new messages from other agents.",
                        tool_result_message="Retrieved 0 messages.",
                    )

                # Format messages
                formatted_messages = []
                for msg in messages:
                    timestamp = msg.timestamp.strftime("%H:%M:%S")
                    formatted_messages.append(
                        f"[{timestamp}] {msg.agent_name}: {msg.message}"
                    )

                output = "\n".join(formatted_messages)
                return ToolImplOutput(
                    tool_output=output,
                    tool_result_message=f"Retrieved {len(messages)} messages.",
                )

            else:
                # This shouldn't happen due to schema validation
                return ToolImplOutput(
                    tool_output=f"Invalid action: {action}",
                    tool_result_message="Invalid tool input",
                )

        except jsonschema.exceptions.ValidationError as e:
            error_msg = f"Invalid tool input: {str(e)}"
            return ToolImplOutput(
                tool_output=error_msg,
                tool_result_message=error_msg,
            )
        except Exception as e:
            error_msg = (
                f"Error performing {tool_input.get('action', '<unknown>')}: {str(e)}"
            )
            return ToolImplOutput(
                tool_output=error_msg,
                tool_result_message=error_msg,
            )

    def get_tool_start_message(self, tool_input: dict[str, Any]) -> str:
        """Get a message describing what the tool is about to do.

        Args:
            tool_input: The tool input

        Returns:
            A descriptive message
        """
        action = tool_input.get("action", "<unknown>")
        if action == "notify":
            return f"Notifying other agents about: {tool_input.get('message', '<no message>')}"
        elif action == "get_messages":
            return "Getting messages from other agents"
        else:
            return f"Unknown action: {action}"
