"""Test the diff_snapshots method of WorkspaceManagerImpl."""

import os
import tempfile
from pathlib import Path
from unittest.mock import MagicMock

import pytest

from experimental.guy.agent_qa.workspace_manager import WorkspaceManagerImpl
from experimental.guy.agent_qa.prototyping_client import AugmentPrototypingClient
from research.agents.changed_file import ChangedFile


def test_diff_snapshots_detects_file_creation():
    """Test that diff_snapshots correctly detects when a file is created."""
    # Create a temporary directory for the test
    with tempfile.TemporaryDirectory() as temp_dir:
        # Create a mock AugmentPrototypingClient
        mock_client = MagicMock(spec=AugmentPrototypingClient)

        # Create a WorkspaceManagerImpl instance
        workspace_manager = WorkspaceManagerImpl(
            augment_client=mock_client, root=Path(temp_dir)
        )

        # Take an initial snapshot of the empty workspace
        workspace_manager.snapshot_workspace()

        # Create a new file
        test_file_path = os.path.join(temp_dir, "test_file.txt")
        with open(test_file_path, "w") as f:
            f.write("Test content")

        # Take another snapshot
        workspace_manager.snapshot_workspace()

        # Get the diff between the snapshots
        diff = workspace_manager.diff_snapshots(-2, -1)

        # Print debugging information
        print(f"Diff: {diff}")
        print(f"Diff type: {type(diff)}")
        print(f"Diff length: {len(diff) if diff is not None else 'N/A'}")

        # Verify that the diff is not None
        assert diff is not None, "diff_snapshots returned None"

        # Verify that the diff contains one file patch
        assert len(diff) == 1, f"Expected 1 file patch, got {len(diff)}"

        # Verify that the file patch is for the created file
        file_patch = diff[0]
        assert (
            file_patch.path == "test_file.txt"
        ), f"Expected path 'test_file.txt', got '{file_patch.path}'"

        # Verify that the file patch indicates the file was added
        assert file_patch.is_added_file, "File patch should indicate the file was added"


def test_diff_snapshots_detects_file_modification():
    """Test that diff_snapshots correctly detects when a file is modified."""
    # Create a temporary directory for the test
    with tempfile.TemporaryDirectory() as temp_dir:
        # Create a mock AugmentPrototypingClient
        mock_client = MagicMock(spec=AugmentPrototypingClient)

        # Create a WorkspaceManagerImpl instance
        workspace_manager = WorkspaceManagerImpl(
            augment_client=mock_client, root=Path(temp_dir)
        )

        # Create a file
        test_file_path = os.path.join(temp_dir, "test_file.txt")
        with open(test_file_path, "w") as f:
            f.write("Initial content")

        # Take an initial snapshot
        workspace_manager.snapshot_workspace()

        # Modify the file
        with open(test_file_path, "w") as f:
            f.write("Modified content")

        # Take another snapshot
        workspace_manager.snapshot_workspace()

        # Get the diff between the snapshots
        diff = workspace_manager.diff_snapshots(-2, -1)

        # Verify that the diff is not None
        assert diff is not None, "diff_snapshots returned None"

        # Verify that the diff contains one file patch
        assert len(diff) == 1, f"Expected 1 file patch, got {len(diff)}"

        # Verify that the file patch is for the modified file
        file_patch = diff[0]
        assert (
            file_patch.path == "test_file.txt"
        ), f"Expected path 'test_file.txt', got '{file_patch.path}'"

        # Verify that the file patch indicates the file was modified (not added or removed)
        assert (
            not file_patch.is_added_file
        ), "File patch should not indicate the file was added"
        assert (
            not file_patch.is_removed_file
        ), "File patch should not indicate the file was removed"


def test_diff_snapshots_detects_file_deletion():
    """Test that diff_snapshots correctly detects when a file is deleted."""
    # Create a temporary directory for the test
    with tempfile.TemporaryDirectory() as temp_dir:
        # Create a mock AugmentPrototypingClient
        mock_client = MagicMock(spec=AugmentPrototypingClient)

        # Create a WorkspaceManagerImpl instance
        workspace_manager = WorkspaceManagerImpl(
            augment_client=mock_client, root=Path(temp_dir)
        )

        # Create a file
        test_file_path = os.path.join(temp_dir, "test_file.txt")
        with open(test_file_path, "w") as f:
            f.write("Test content")

        # Take an initial snapshot
        workspace_manager.snapshot_workspace()

        # Delete the file
        os.remove(test_file_path)

        # Take another snapshot
        workspace_manager.snapshot_workspace()

        # Get the diff between the snapshots
        diff = workspace_manager.diff_snapshots(-2, -1)

        # Verify that the diff is not None
        assert diff is not None, "diff_snapshots returned None"

        # Verify that the diff contains one file patch
        assert len(diff) == 1, f"Expected 1 file patch, got {len(diff)}"

        # Verify that the file patch is for the deleted file
        file_patch = diff[0]
        assert (
            file_patch.path == "test_file.txt"
        ), f"Expected path 'test_file.txt', got '{file_patch.path}'"

        # Verify that the file patch indicates the file was removed
        assert (
            file_patch.is_removed_file
        ), "File patch should indicate the file was removed"


def test_take_snapshot_and_log_changes_with_save_file():
    """Test that _take_snapshot_and_log_changes correctly detects changes made by save_file."""
    # Create a temporary directory for the test
    with tempfile.TemporaryDirectory() as temp_dir:
        # Create a mock AugmentPrototypingClient
        mock_client = MagicMock(spec=AugmentPrototypingClient)

        # Create a WorkspaceManagerImpl instance
        workspace_manager = WorkspaceManagerImpl(
            augment_client=mock_client, root=Path(temp_dir)
        )

        # Take an initial snapshot of the empty workspace
        workspace_manager.snapshot_workspace()

        # Simulate the save_file tool by creating a file
        test_file_path = os.path.join(temp_dir, "test_file.txt")
        with open(test_file_path, "w") as f:
            f.write("Test content")

        # Take another snapshot
        workspace_manager.snapshot_workspace()

        # Create a second file to simulate a change after the snapshot
        test_file2_path = os.path.join(temp_dir, "test_file2.txt")
        with open(test_file2_path, "w") as f:
            f.write("Test content 2")

        # Now, let's create a DialogMessages instance
        from research.agents.tools import DialogMessages, ToolCallLogger

        # Create a DialogMessages instance
        dialog_messages = DialogMessages()

        # Create a ToolCallLogger instance
        tool_call_logger = ToolCallLogger()

        # Call _take_snapshot_and_log_changes
        from experimental.guy.agent_qa.agents import _take_snapshot_and_log_changes

        _take_snapshot_and_log_changes(
            workspace_manager,
            tool_call_logger,
            "Test instruction",
            dialog_messages,
        )

        # Get the diff between the snapshots
        diff = workspace_manager.diff_snapshots(-2, -1)

        # Print debugging information
        print(f"Diff: {diff}")
        print(f"Diff type: {type(diff)}")
        print(f"Diff length: {len(diff) if diff is not None else 'N/A'}")

        # Verify that the diff is not None
        assert diff is not None, "diff_snapshots returned None"

        # Verify that the diff contains one file patch
        assert len(diff) == 1, f"Expected 1 file patch, got {len(diff)}"

        # Verify that the file patch is for the second created file
        file_patch = diff[0]
        assert (
            file_patch.path == "test_file2.txt"
        ), f"Expected path 'test_file2.txt', got '{file_patch.path}'"

        # Verify that the file patch indicates the file was added
        assert file_patch.is_added_file, "File patch should indicate the file was added"

        # We've already created the DialogMessages and ToolCallLogger instances

        # We've already called _take_snapshot_and_log_changes

        # Print debugging information
        print(f"Dialog messages changed files: {dialog_messages._changed_files}")
        print(
            f"Dialog messages changed files length: {len(dialog_messages._changed_files)}"
        )

        # Verify that the dialog_messages has the changed file
        assert (
            len(dialog_messages._changed_files) > 0
        ), "dialog_messages should have changed files"

        # Verify that the changed file is for the second created file
        # The _changed_files attribute is a dictionary, not a list
        changed_files = dialog_messages._changed_files[-1]  # Get the latest exchange
        assert (
            len(changed_files) == 1
        ), f"Expected 1 changed file, got {len(changed_files)}"

        changed_file = changed_files[0]
        assert (
            changed_file.new_path == "test_file2.txt"
        ), f"Expected new_path 'test_file2.txt', got '{changed_file.new_path}'"
        assert (
            changed_file.change_type == "ADDED"
        ), f"Expected change_type 'ADDED', got '{changed_file.change_type}'"
        assert (
            changed_file.new_contents == "Test content 2"
        ), f"Expected new_contents 'Test content 2', got '{changed_file.new_contents}'"


if __name__ == "__main__":
    # Run the tests
    test_diff_snapshots_detects_file_creation()
    test_diff_snapshots_detects_file_modification()
    test_diff_snapshots_detects_file_deletion()
    test_take_snapshot_and_log_changes_with_save_file()
    print("All tests passed!")
