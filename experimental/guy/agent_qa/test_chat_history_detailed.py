#!/usr/bin/env python3
"""
Detailed tests for the chat_history method in CLIInterface.

This test file specifically verifies:
1. The end of an LLM call is correctly identified as the message right after the beginning
2. The latest message (last in the list) is processed and all nodes in that message are included
"""

import json
import sys
from pathlib import Path

from research.agents.tools import (
    ToolCallLogger,
    LoggedLanguageModelCall,
    LoggedToolCall,
    ToolFormattedResult,
)
from research.llm_apis.llm_client import Text<PERSON>rompt, TextResult, ToolCall
from experimental.guy.agent_qa.chat_history import (
    get_chat_history,
)


# Define a mock ToolResult class for testing
class ToolResult:
    def __init__(self, tool_call_id, content, is_error=False):
        self.tool_call_id = tool_call_id
        self.content = content
        self.is_error = is_error


def create_mock_llm_call(started=True, messages=None, response=None):
    """Create a mock LLM call with custom messages and response."""
    return LoggedLanguageModelCall(
        started=started,
        messages=messages or [],
        max_tokens=1000,
        system_prompt="You are a helpful assistant.",
        temperature=0.0,
        tools=[],
        tool_choice=None,
        response=response,
        response_metadata={},
    )


def create_mock_tool_call(
    started=True, tool_name="test_tool", tool_input=None, tool_output=None
):
    """Create a mock tool call."""
    from research.agents.tools import ToolParam

    tool = ToolParam(
        name=tool_name,
        description="A test tool",
        input_schema={"type": "object", "properties": {}},
    )
    return LoggedToolCall(
        started=started,
        tool=tool,
        tool_input=tool_input or {},
        tool_output=tool_output,
        tool_message="Test tool message",
    )


def test_llm_call_end_identification():
    """Test that the end of an LLM call is correctly identified as the message right after the beginning."""
    # Create a tool call logger
    tool_call_logger = ToolCallLogger()

    # Add some logged calls
    # Top-level agent start
    tool_call_logger.logged_calls.append(
        create_mock_tool_call(started=True, tool_name="agent")
    )

    # First LLM call (user message and response)
    tool_call_logger.logged_calls.append(
        create_mock_llm_call(
            started=True,
            messages=[[TextPrompt(text="Hello, I need help with Python.")]],
        )
    )
    tool_call_logger.logged_calls.append(
        create_mock_llm_call(
            started=False,
            messages=[[TextPrompt(text="Hello, I need help with Python.")]],
            response=[TextResult(text="I can help you with Python. What do you need?")],
        )
    )

    # Create a DialogMessages object for testing
    from research.agents.tools import DialogMessages

    dialog = DialogMessages()
    # Add the specific user message and assistant response for this test
    dialog.add_user_prompt("Hello, I need help with Python.")
    dialog.add_model_response(
        [TextResult(text="I can help you with Python. What do you need?")]
    )
    history = get_chat_history(dialog)

    # Verify that the chat history has the expected structure
    assert "chat_history" in history, "chat_history field missing"
    assert len(history["chat_history"]) == 1, "Expected exactly one exchange"

    # Verify that the exchange has both request and response
    exchange = history["chat_history"][0]["exchange"]
    assert (
        exchange["request_message"] == "Hello, I need help with Python."
    ), "Request message incorrect"
    assert (
        exchange["response_text"] == "I can help you with Python. What do you need?"
    ), "Response text incorrect"

    print("Test 1 passed: LLM call end identification works correctly")


def test_latest_message_processing():
    """Test that the latest message (last in the list) is processed and all nodes in that message are included."""
    # Create a tool call logger
    tool_call_logger = ToolCallLogger()

    # Add some logged calls
    # Top-level agent start
    tool_call_logger.logged_calls.append(
        create_mock_tool_call(started=True, tool_name="agent")
    )

    # LLM call with multiple messages (the last one should be used)
    tool_call_logger.logged_calls.append(
        create_mock_llm_call(
            started=True,
            messages=[
                [TextPrompt(text="First message")],
                [TextPrompt(text="Second message")],
                [TextPrompt(text="Third message - this should be used")],
            ],
        )
    )
    tool_call_logger.logged_calls.append(
        create_mock_llm_call(
            started=False,
            messages=[
                [TextPrompt(text="First message")],
                [TextPrompt(text="Second message")],
                [TextPrompt(text="Third message - this should be used")],
            ],
            response=[TextResult(text="Response to the third message")],
        )
    )

    # Create a DialogMessages object for testing
    from research.agents.tools import DialogMessages

    dialog = DialogMessages()
    # Add the specific user message and assistant response for this test
    dialog.add_user_prompt("Third message - this should be used")
    dialog.add_model_response([TextResult(text="Response to the third message")])
    history = get_chat_history(dialog)

    # Verify that the chat history has the expected structure
    assert "chat_history" in history, "chat_history field missing"
    assert len(history["chat_history"]) == 1, "Expected exactly one exchange"

    # Verify that the exchange has the correct request message (from the last message in the list)
    exchange = history["chat_history"][0]["exchange"]
    assert (
        exchange["request_message"] == "Third message - this should be used"
    ), "Request message incorrect"

    print("Test 2 passed: Latest message processing works correctly")


def test_multiple_nodes_in_message():
    """Test that all nodes in the latest message are included in the request."""
    # Create a tool call logger
    tool_call_logger = ToolCallLogger()

    # Add some logged calls
    # Top-level agent start
    tool_call_logger.logged_calls.append(
        create_mock_tool_call(started=True, tool_name="agent")
    )

    # LLM call with a message containing multiple nodes (text and tool result)
    tool_call_logger.logged_calls.append(
        create_mock_llm_call(
            started=True,
            messages=[
                [
                    TextPrompt(text="I need to search for something"),
                    ToolResult(
                        tool_call_id="tool_1", content="Search results for Python"
                    ),
                ]
            ],
        )
    )
    tool_call_logger.logged_calls.append(
        create_mock_llm_call(
            started=False,
            messages=[
                [
                    TextPrompt(text="I need to search for something"),
                    ToolResult(
                        tool_call_id="tool_1", content="Search results for Python"
                    ),
                ]
            ],
            response=[TextResult(text="I found information about Python")],
        )
    )

    # Get the chat history
    # Create a DialogMessages object for testing
    from research.agents.tools import DialogMessages

    dialog = DialogMessages()
    # Add the specific user message for this test
    dialog.add_user_prompt("I need to search for something")
    # Add a model response with a tool call
    dialog.add_model_response(
        [
            ToolCall(
                tool_call_id="tool_1",
                tool_name="search",
                tool_input={"query": "Python"},
            )
        ]
    )
    # Add a tool call result in the next user turn
    from research.agents.tools import ToolCallParameters

    dialog.add_tool_call_result(
        parameters=ToolCallParameters(
            tool_call_id="tool_1",
            tool_name="search",
            tool_input={"query": "Python"},
        ),
        result="Search results for Python",
    )
    # Add the final model response
    dialog.add_model_response([TextResult(text="I found information about Python")])
    history = get_chat_history(dialog)

    # Verify that the chat history has the expected structure
    assert "chat_history" in history, "chat_history field missing"
    assert len(history["chat_history"]) == 2, "Expected exactly two exchanges"

    # Verify that the first exchange has the correct request message
    exchange1 = history["chat_history"][0]["exchange"]
    assert (
        exchange1["request_message"] == "I need to search for something"
    ), "Request message incorrect"

    # Verify the text node in the first exchange
    text_node = exchange1["request_nodes"][0]
    assert text_node["type"] == "TEXT", "First node should be TEXT"
    assert (
        text_node["text_node"]["content"] == "I need to search for something"
    ), "Text content incorrect"

    # Verify that the second exchange has the tool result
    exchange2 = history["chat_history"][1]["exchange"]
    tool_result_node = exchange2["request_nodes"][0]
    assert tool_result_node["type"] == "TOOL_RESULT", "Node should be TOOL_RESULT"
    assert (
        tool_result_node["tool_result_node"]["tool_use_id"] == "tool_1"
    ), "Tool use ID incorrect"
    assert (
        tool_result_node["tool_result_node"]["content"] == "Search results for Python"
    ), "Tool result content incorrect"

    print("Test 3 passed: Multiple nodes in message are correctly included")


def test_ongoing_llm_call():
    """Test handling of an ongoing LLM call (no end call)."""
    # Create a tool call logger
    tool_call_logger = ToolCallLogger()

    # Add some logged calls
    # Top-level agent start
    tool_call_logger.logged_calls.append(
        create_mock_tool_call(started=True, tool_name="agent")
    )

    # LLM call that's still ongoing (no end call)
    tool_call_logger.logged_calls.append(
        create_mock_llm_call(
            started=True, messages=[[TextPrompt(text="This is an ongoing request")]]
        )
    )

    # Get the chat history
    # Create a DialogMessages object for testing
    from research.agents.tools import DialogMessages

    dialog = DialogMessages()
    # Add the specific user message for this test
    dialog.add_user_prompt("This is an ongoing request")
    # No assistant response for this test (simulating an ongoing request)
    history = get_chat_history(dialog)

    # Verify that the chat history has the expected structure
    assert "chat_history" in history, "chat_history field missing"
    assert len(history["chat_history"]) == 1, "Expected exactly one exchange"

    # Verify that the exchange has only the request (no response)
    exchange = history["chat_history"][0]["exchange"]
    assert (
        exchange["request_message"] == "This is an ongoing request"
    ), "Request message incorrect"
    assert (
        "response_text" not in exchange or not exchange["response_text"]
    ), "Response text should be empty"
    assert len(exchange["response_nodes"]) == 0, "Response nodes should be empty"

    print("Test 4 passed: Ongoing LLM call handling works correctly")


# This test has been moved to test_cli_agent_e2e.py


def main():
    """Run all tests."""
    print("\nRunning detailed tests for chat_history method...\n")

    test_llm_call_end_identification()
    test_latest_message_processing()
    test_multiple_nodes_in_message()
    test_ongoing_llm_call()

    print("\nAll tests passed!")
    return 0


if __name__ == "__main__":
    sys.exit(main())
