import argparse
import asyncio
import hashlib
import os
from pathlib import Path
from typing import Optional

import json
from notion_client import AsyncClient
from notion2md.exporter.block import StringExporter


class NotionPageExporter:
    def __init__(self, auth_token: str, output_dir: str | Path):
        self.client = AsyncClient(auth=auth_token)
        os.environ["NOTION_TOKEN"] = auth_token  # For notion2md
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)

    def _get_page_title(self, page: dict) -> str:
        """Extract page title from page object."""
        title_items = page["properties"]["title"]["title"]
        if not title_items:
            return "Untitled"
        return title_items[0]["plain_text"]

    def _is_page_exported(self, page_dir: Path) -> bool:
        """Check if a page was already exported by looking for index.md."""
        return (page_dir / "index.md").exists()

    async def _get_child_pages(self, block_id: str) -> list:
        """Get all child pages for a given block ID."""
        blocks = []
        next_cursor = None

        while True:
            response = await self.client.blocks.children.list(
                block_id=block_id,
                start_cursor=next_cursor,
                page_size=100,
            )
            # Filter only child_page blocks
            child_pages = [b for b in response["results"] if b["type"] == "child_page"]
            blocks.extend(child_pages)

            if not response["has_more"]:
                break

            next_cursor = response["next_cursor"]

        return blocks

    async def export_page_to_markdown(
        self,
        page_id: str,
        parent_path: Optional[Path] = None,
        depth: int = 0,
    ) -> None:
        """Recursively export a page and all its subpages to markdown files."""
        # Format the page_id to the correct format if needed
        page_id = page_id.replace("-", "")

        # Get the page
        page = await self.client.pages.retrieve(page_id=page_id)
        title = self._get_page_title(page)
        indent = "  " * depth

        # Create the current path (always create directory structure)
        current_path = parent_path or self.output_dir
        current_path = current_path / title
        current_path.mkdir(parents=True, exist_ok=True)

        # Check if this specific page's index.md exists
        page_needs_export = not self._is_page_exported(current_path)
        if page_needs_export:
            print(f"{indent}Exporting: {title}")
            # Use notion2md to convert the page content
            exporter = StringExporter(block_id=page_id)
            markdown_content = exporter.export()
        else:
            print(f"{indent}Skipping already exported: {title}")
            markdown_content = ""  # Will still need this for child pages section

        # Get all child pages
        child_pages = await self._get_child_pages(page_id)

        # Always process child pages, regardless of whether this page was exported
        if child_pages:
            # Only modify markdown_content if we're exporting this page
            if page_needs_export:
                markdown_content += "\n\n## Child Pages\n\n"
                for child in child_pages:
                    child_title = child["child_page"]["title"]
                    markdown_content += f"- [{child_title}](./{child_title}/index.md)\n"

            # Always process all child pages
            for child in child_pages:
                await self.export_page_to_markdown(child["id"], current_path, depth + 1)

        # Save the markdown file only if it doesn't exist
        if page_needs_export:
            markdown_path = current_path / "index.md"
            markdown_path.write_text(
                f"# {title}\n\n{markdown_content}", encoding="utf-8"
            )


def get_token(cache_dir: Path) -> str:
    """Get Notion API token from environment or cache file."""
    # First try environment variable
    token = os.environ.get("NOTION_TOKEN")
    if token:
        return token

    # Try reading from cache file
    token_file = cache_dir / "notion_api_token"
    try:
        return token_file.read_text().strip()
    except FileNotFoundError:
        raise ValueError(
            "NOTION_TOKEN environment variable not set and token file not found at "
            f"{token_file}"
        )


async def async_main(args):
    print(f"Starting export from root pages {args.root_page_ids}")

    # Ensure cache directory exists
    cache_dir = Path(args.cache_dir)
    cache_dir.mkdir(parents=True, exist_ok=True)

    # Get token
    token = get_token(cache_dir)

    # Initialize exporter
    exporter = NotionPageExporter(auth_token=token, output_dir=args.output_dir)

    # Export from root pages
    for root_page_id in args.root_page_ids.split(","):
        try:
            await exporter.export_page_to_markdown(root_page_id)
            print("\nExport completed successfully")
        except Exception as e:
            print(f"\nExport failed: {str(e)}")
            raise


def main():
    """Synchronous wrapper for async_main."""
    parser = argparse.ArgumentParser(description="Export Notion pages to Markdown")
    parser.add_argument(
        "--cache-dir",
        type=Path,
        default=Path.home() / ".augment" / "agent",
        help="Cache directory (default: ~/.augment/agent)",
    )
    parser.add_argument(
        "--output-dir",
        type=Path,
        default=Path("/mnt/efs/augment/user/guy/bob/notion_export"),
        help="Output directory for exported pages",
    )
    parser.add_argument(
        "--root-page-ids",
        default="7c71008b-816d-4ed1-8cbb-c735653e0d82,cd766627-bc9b-4fce-a150-f291216f7bff",
        help="Root Notion page ID to start export from (you can get this from the Notion page link)",
    )

    args = parser.parse_args()
    asyncio.run(async_main(args))


if __name__ == "__main__":
    main()
