"""Capture an eval sample on the Augment repo.

An eval sample consists of:
1. A repo commit
2. An agent instruction
3. The expected changes, as a patch to the repo

The script should be called after the changes have been made
(but before they were committed).
"""

import argparse
import json
from pathlib import Path
import subprocess


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--repo_path",
        type=Path,
        default="/home/<USER>/augment_agent_workspace",
        help="Repository location",
    )
    parser.add_argument(
        "--samples_file",
        type=Path,
        default="eval/samples.jsonl",
        help="Samples jsonl file",
    )
    parser.add_argument(
        "--instruction", type=str, required=True, help="The instruction to the agent."
    )
    args = parser.parse_args()

    # Get the last git commit
    response = subprocess.run(
        "git rev-parse HEAD",
        cwd=args.repo_path,
        shell=True,
        capture_output=True,
        check=True,
    )

    commit = response.stdout.decode("utf-8").strip()

    patch = subprocess.run(
        "git diff",
        cwd=args.repo_path,
        shell=True,
        capture_output=True,
        check=True,
    ).stdout.decode("utf-8")

    sample_data = {
        "instruction": args.instruction,
        "commit": commit,
        "changes": patch,
    }

    if not args.samples_file.parent.exists():
        args.samples_file.parent.mkdir(parents=True, exist_ok=True)

    with args.samples_file.open("a", encoding="utf8") as f:
        f.write(json.dumps(sample_data) + "\n")

    print(f"Captured eval sample for commit {commit} in {args.samples_file}")


if __name__ == "__main__":
    main()
