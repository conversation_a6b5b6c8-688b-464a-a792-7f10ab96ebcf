"""End-to-end integration tests for the CLI agent.

These tests verify that external integrations (Notion, Linear, Google Search, Slack)
work correctly. They require proper API credentials to be set up.

To run these tests:
    pytest -v -m integration experimental/guy/agent_qa/test_cli_agent_integrations_e2e.py
"""

import json
import os
import subprocess
from pathlib import Path

import pytest
from experimental.guy.agent_qa.agent_mode import Agent<PERSON><PERSON>, ConstantModeProvider
from experimental.guy.agent_qa.tools.web_search import (
    get_search_credentials,
    WebSearchError,
)
from research.agents.tools import Tool<PERSON>allLogger
from experimental.guy.agent_qa.tools.slack_notification_tool import (
    SlackNotificationTool,
)
from experimental.guy.agent_qa.tools.slack_search_tool import Slack<PERSON>earchTool
from experimental.guy.agent_qa.test_cli_agent_e2e import execute_agent_command
from research.llm_apis.llm_client import FireworksClient


@pytest.fixture
def workspace_dir(tmp_path: Path) -> Path:
    """Create a temporary workspace directory for the agent."""
    return tmp_path


def run_agent(workspace_dir: Path, instruction: str) -> subprocess.CompletedProcess:
    """Run the CLI agent with an instruction.

    Args:
        workspace_dir: The workspace directory for the agent
        instruction: The instruction to give to the agent

    Returns:
        The completed process with stdout and stderr
    """
    user_email = get_user_email()
    return execute_agent_command(
        command=[
            "python",
            "-m",
            "experimental.guy.agent_qa.interactive_agent",
            "--workspace_root",
            str(workspace_dir),
            "--approve-command-execution",  # Don't ask for command approval
            "--notion-allow-create-pages",  # Allow creating Notion pages
            "--notion-writable-pages",  # Allow modifying specific pages
            "18abba10-175a-80cd-b9b1-fb1a11992ead",  # Test page parent ID
            "--enable-slack-notifications",  # Enable Slack notifications
            user_email,  # User's email for Slack
            "-i",
            instruction,  # Pass the instruction
            "-q",  # Quit after instruction is done
        ]
    )


def get_user_email() -> str:
    """Get the user's email from git config."""
    try:
        result = subprocess.run(
            ["git", "config", "user.email"],
            check=True,
            text=True,
            capture_output=True,
        )
        return result.stdout.strip()
    except subprocess.CalledProcessError:
        # TODO better testing email address
        return "<EMAIL>"


@pytest.mark.integration
def test_google_search(workspace_dir: Path) -> None:
    """Test that the agent can perform Google searches."""
    # Check if credentials are available
    try:
        get_search_credentials()
    except WebSearchError as e:
        pytest.skip(f"Google Search credentials not available: {e}")

    # Ask agent to search
    instruction = "use the google_search tool to search for 'Python programming language' and show me the raw results"
    result = run_agent(workspace_dir, instruction)

    # Verify we got search results
    stdout_lower = result.stdout.lower()
    assert "python" in stdout_lower
    assert "found" in stdout_lower  # Should indicate results were found
    assert "programming" in stdout_lower
    # Don't check for URLs since the agent might summarize the results


@pytest.mark.integration
def test_linear_query(workspace_dir: Path) -> None:
    """Test that the agent can query Linear tickets."""
    # Get user email
    user_email = get_user_email()

    # Ask agent to show recent tickets
    instruction = f"use the query_linear tool with user email {user_email} to show my recent tickets"
    result = run_agent(workspace_dir, instruction)

    # Verify we got ticket information or a clear error
    stdout_lower = result.stdout.lower()
    if "error" in stdout_lower or "failed" in stdout_lower:
        pytest.skip("Linear integration not available")

    assert any(word in stdout_lower for word in ["ticket", "issue"])


@pytest.mark.integration
def test_linear_query_by_id(workspace_dir: Path) -> None:
    """Test that the agent can query Linear tickets by ID."""
    # Get user email
    user_email = get_user_email()

    # Ask agent to find a specific ticket by ID
    instruction = f'use the query_linear tool with user email {user_email} to find ticket "AU-123"'
    result = run_agent(workspace_dir, instruction)

    # Verify we got ticket information or a clear error
    stdout_lower = result.stdout.lower()
    if "error" in stdout_lower or "failed" in stdout_lower:
        pytest.skip("Linear integration not available")

    assert "au-123" in stdout_lower, "Should find the specific ticket"
    assert any(
        word in stdout_lower for word in ["title", "description", "status"]
    ), "Should show ticket details"


@pytest.mark.integration
def test_notion_page_read(workspace_dir: Path) -> None:
    """Test that the agent can read Notion pages."""
    # Try to read a known Notion page that has content
    instruction = "use the notion_page tool in read mode to read the page with ID 18abba10-175a-80cd-b9b1-fb1a11992ead, and tell me its title"
    result = run_agent(workspace_dir, instruction)

    # Verify we got page content
    stdout_lower = result.stdout.lower()

    # The integration exists, so we should get a page with the correct title
    assert "slackbot play test instructions" in stdout_lower


@pytest.mark.integration
def test_slack_notification(workspace_dir: Path) -> None:
    """Test that the agent can send Slack notifications."""
    # First check if the notification tool is available
    result = execute_agent_command(
        command=[
            "python",
            "-m",
            "experimental.guy.agent_qa.interactive_agent",
            "--workspace_root",
            str(workspace_dir),
            "--enable-slack-notifications",
            get_user_email(),
            "--list-tools",
        ]
    )

    stdout_lower = result.stdout.lower()
    if "slack_notification" not in stdout_lower:
        print("Available tools:", stdout_lower)  # Print for debugging
        pytest.fail("Slack notification tool should be available")

    # Send a test message
    instruction = 'use the slack_notification tool to send the message "Integration test message". if it was sent successfully, say "message sent successfully"'
    result = run_agent(workspace_dir, instruction)

    # Verify message was sent
    stdout_lower = result.stdout.lower()
    print(f"Response in test_slack_notification: {result.stdout}")
    success_phrases = [
        "message sent successfully",
        "message sent",
        "message successfully sent",
        "was successfully sent",
        "was sent successfully",
        "has been sent",
    ]
    assert any(
        phrase in stdout_lower for phrase in success_phrases
    ), "Message should be sent successfully"


@pytest.mark.integration
def test_slack_search(workspace_dir: Path) -> None:
    """Test that the agent can search Slack messages in public channels."""
    # First check if the user token exists
    user_token_path = Path.home() / ".augment" / "agent" / "slack_user_token"
    if not user_token_path.exists():
        pytest.skip("Slack user token not available")

    # Read user token
    with open(user_token_path, "r") as f:
        user_token = f.read().strip()

    # Create tool instance
    logger = ToolCallLogger()
    tool = SlackSearchTool(
        tool_call_logger=logger,
        user_token=user_token,
    )

    # Test search functionality
    def verify_search_results(query: str, should_find: bool = True) -> None:
        """Verify search results for a query."""
        result = tool.run_impl(
            {
                "query": query,
            }
        )

        # Convert output to lowercase for case-insensitive checks
        output = result.tool_output.lower()

        if should_find:
            # Should find messages
            assert "found" in output, "Should find some messages"
            assert "channel:" in output, "Should show channel names"
            assert "view in slack" in output, "Should include permalinks"
            assert "user:" in output, "Should show message authors"

            # Should only show public channels
            assert "direct message" not in output, "Should not show DMs"
            assert "group message" not in output, "Should not show group messages"
        else:
            # Should handle no results gracefully
            assert (
                "no matching messages" in output
            ), "Should handle no results gracefully"
            assert (
                "public channels" in output
            ), "Should mention public channels in response"

    # Test with a query that should return results
    verify_search_results("test driven development", should_find=True)

    # Test with a query that should not return results
    verify_search_results("xyznonexistentmessage123", should_find=False)

    # Also verify through the agent that the tool is available and working
    instruction = "list all available tools and their descriptions"
    result = run_agent(workspace_dir, instruction)

    # Verify tool is available - check both exact name and natural language descriptions
    stdout_lower = result.stdout.lower()
    assert any(
        indicator in stdout_lower
        for indicator in [
            "slack_search",  # Exact tool name
            "search slack",  # Natural description
            "search messages",  # Alternative description
            "slack messages",  # Another alternative
        ]
    ), "Slack search functionality should be available"


@pytest.mark.integration
def test_fireworks_client(workspace_dir: Path) -> None:
    """Test that the agent can use the Fireworks client for text generation."""
    # Check if Fireworks API key is available
    api_key_path = Path.home() / ".augment" / "fireworks_api_token"
    if not api_key_path.exists():
        pytest.skip("Fireworks API key not available")

    # Read API key
    with open(api_key_path, "r") as f:
        api_key = f.read().strip()

    # Test direct client functionality
    client = FireworksClient(
        model_name=FireworksClient.MODEL_DEEPSEEK_V3,
        api_key=api_key,
    )

    # Simple text generation test
    from research.llm_apis.llm_client import TextPrompt

    messages = [[TextPrompt(text="Write a hello world program in Python.")]]
    response, metadata = client.generate(
        messages=messages,
        max_tokens=1024,
        temperature=0.7,
    )

    # Verify response
    assert response, "Should get a response from Fireworks"
    assert hasattr(response[0], "text"), "Response should have text attribute"
    assert "print" in response[0].text, "Response should contain Python code"
    assert metadata["input_tokens"] > 0, "Should have input tokens"
    assert metadata["output_tokens"] > 0, "Should have output tokens"

    # Skip testing with the agent for now due to serialization issues
    # with the Fireworks client response object
    print("\nSkipping agent integration test for Fireworks client")
    print("Direct client test passed successfully")
