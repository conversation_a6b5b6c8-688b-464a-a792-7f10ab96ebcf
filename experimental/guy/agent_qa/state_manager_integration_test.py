"""Integration tests for the StateManager class.

These tests demonstrate how the StateManager can be used in the interactive agent.
"""

import os
import pickle
import tempfile
import unittest
from pathlib import Path
from typing import Dict, Any, Optional

from experimental.guy.agent_qa.state_manager import StateManager


class MockDialogState:
    """Mock class representing dialog state."""

    def __init__(self, messages=None):
        self.messages = messages or []

    def add_message(self, message):
        self.messages.append(message)
        return self


class MockWorkspaceState:
    """Mock class representing workspace state."""

    def __init__(self, files=None):
        self.files = files or {}

    def add_file(self, path, content):
        self.files[path] = content
        return self


class AgentWithStateManager:
    """Mock agent class that uses StateManager."""

    def __init__(self, state_file: Optional[Path] = None):
        self.state_manager = StateManager(state_file)

        # Initialize dialog state if it doesn't exist
        dialog_state = self.state_manager.get("dialog", MockDialogState())
        self.state_manager.update("dialog", dialog_state)

        # Initialize workspace state if it doesn't exist
        workspace_state = self.state_manager.get("workspace", MockWorkspaceState())
        self.state_manager.update("workspace", workspace_state)

    def add_message(self, message):
        """Add a message to the dialog state."""
        dialog_state = self.state_manager.get("dialog", MockDialogState())
        dialog_state.add_message(message)
        self.state_manager.update("dialog", dialog_state)

    def add_file(self, path, content):
        """Add a file to the workspace state."""
        workspace_state = self.state_manager.get("workspace", MockWorkspaceState())
        workspace_state.add_file(path, content)
        self.state_manager.update("workspace", workspace_state)

    def get_dialog_messages(self):
        """Get all dialog messages."""
        dialog_state = self.state_manager.get("dialog", MockDialogState())
        return dialog_state.messages

    def get_workspace_files(self):
        """Get all workspace files."""
        workspace_state = self.state_manager.get("workspace", MockWorkspaceState())
        return workspace_state.files


class StateManagerIntegrationTest(unittest.TestCase):
    """Integration tests for the StateManager class."""

    def setUp(self):
        """Set up a temporary directory for test files."""
        self.temp_dir = tempfile.TemporaryDirectory()
        self.state_file = Path(self.temp_dir.name) / "agent_state.pkl"

    def tearDown(self):
        """Clean up temporary directory."""
        self.temp_dir.cleanup()

    def test_agent_with_state_manager(self):
        """Test using StateManager with a mock agent."""
        # Create an agent with state manager
        agent = AgentWithStateManager(self.state_file)

        # Add some state
        agent.add_message("Hello")
        agent.add_message("World")
        agent.add_file("file1.txt", "Content 1")
        agent.add_file("file2.txt", "Content 2")

        # Check that the state was saved correctly
        self.assertEqual(agent.get_dialog_messages(), ["Hello", "World"])
        self.assertEqual(
            agent.get_workspace_files(),
            {"file1.txt": "Content 1", "file2.txt": "Content 2"},
        )

        # Create a new agent with the same state file
        agent2 = AgentWithStateManager(self.state_file)

        # Check that the state was loaded correctly
        self.assertEqual(agent2.get_dialog_messages(), ["Hello", "World"])
        self.assertEqual(
            agent2.get_workspace_files(),
            {"file1.txt": "Content 1", "file2.txt": "Content 2"},
        )

        # Add more state to the second agent
        agent2.add_message("!")
        agent2.add_file("file3.txt", "Content 3")

        # Create a third agent with the same state file
        agent3 = AgentWithStateManager(self.state_file)

        # Check that all updates were saved and loaded correctly
        self.assertEqual(agent3.get_dialog_messages(), ["Hello", "World", "!"])
        self.assertEqual(
            agent3.get_workspace_files(),
            {
                "file1.txt": "Content 1",
                "file2.txt": "Content 2",
                "file3.txt": "Content 3",
            },
        )

    def test_agent_with_custom_state(self):
        """Test using StateManager with custom state types."""
        agent = AgentWithStateManager(self.state_file)

        # Add a custom state entry
        agent.state_manager.update("custom", {"key": "value", "nested": [1, 2, 3]})

        # Create a new agent with the same state file
        agent2 = AgentWithStateManager(self.state_file)

        # Check that the custom state was loaded correctly
        self.assertEqual(
            agent2.state_manager.get("custom", None),
            {"key": "value", "nested": [1, 2, 3]},
        )


if __name__ == "__main__":
    unittest.main()
