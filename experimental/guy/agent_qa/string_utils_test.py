"""Tests for string_utils.py."""

from experimental.guy.agent_qa.string_utils import truncate_string


def test_truncate_string_short_text():
    """Test that short texts are not truncated."""
    text = "short\ntext\n"
    assert truncate_string(text, 1000) == text


def test_truncate_string_empty():
    """Test truncating empty string."""
    assert truncate_string("", 100) == ""


def test_truncate_string_single_line():
    """Test truncating a single line."""
    text = "single line\n"
    assert truncate_string(text, 100) == text


def test_truncate_string_long_text():
    """Test truncating a long text."""
    lines = [f"line {i}\n" for i in range(100)]
    text = "".join(lines)
    truncated = truncate_string(text, 100)

    # Should have some prefix lines
    assert truncated.startswith("line 0\n")
    assert "line 1\n" in truncated

    # Should have the truncation marker
    assert "... additional lines truncated ...\n" in truncated

    # Should have some suffix lines
    assert "line 98\n" in truncated
    assert truncated.endswith("line 99\n")

    # Should be roughly the requested size
    assert len(truncated) < 150  # Allow some slack


def test_truncate_string_preserves_line_endings():
    """Test that truncation preserves line endings."""
    text = "first line is longer\nsecond line is also longer\nthird line continues\nfourth line goes on\nfifth line ends it\n"
    truncated = truncate_string(text, 50)

    # Should preserve line endings in prefix
    assert truncated.startswith("first line is longer\n")

    # Should preserve line endings in suffix
    assert truncated.endswith("fifth line ends it\n")

    # Should have exactly one truncation marker line
    assert truncated.count("... additional lines truncated ...") == 1
