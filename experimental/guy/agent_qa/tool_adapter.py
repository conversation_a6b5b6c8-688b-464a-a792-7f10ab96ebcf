from typing import Any, Callable, Optional

from research.agents.tools import (
    DialogMessages,
    LLMTool,
    ToolCallLogger,
    ToolImplOutput,
)


LLMToolFunction = Callable[[dict[str, Any], Optional[DialogMessages]], ToolImplOutput]
"""A method that mimics the run_impl method of LLMTool."""


class LLMToolAdapter(LLMTool):
    """Turns a function of type LLMToolMethod into an LLMTool."""

    def __init__(
        self,
        tool_call_logger: ToolCallLogger,
        tool_func: LLMToolFunction,
        name: str,
        description: str,
        input_schema: dict[str, Any],
    ):  # Added missing colon here
        super().__init__(tool_call_logger)
        self.tool_func = tool_func
        self.name = name
        self.description = description
        self.input_schema = input_schema

    def run_impl(
        self,
        tool_input: dict[str, Any],
        dialog_messages: Optional[DialogMessages] = None,
    ) -> ToolImplOutput:
        return self.tool_func(tool_input, dialog_messages)
