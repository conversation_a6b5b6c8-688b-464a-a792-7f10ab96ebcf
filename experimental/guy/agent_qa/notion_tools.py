"""Tools for interacting with Not<PERSON>."""

import asyncio
import os
from pathlib import Path
from typing import Any, Optional

from notion_client import AsyncClient
from notion_client import errors as notion_errors

from research.agents.tools import (
    DialogMessages,
    LLMTool,
    ToolCallLogger,
    ToolImplOutput,
)
from research.llm_apis.llm_client import Tool<PERSON><PERSON>
from experimental.michiel.research.agentqa.tools import (
    CodebaseRetrievalTool as MichielCodebaseRetrievalTool,
)
from experimental.guy.agent_qa.builtin_tools import QueryOnlyDocumentIndex
from experimental.guy.agent_qa.integration_warnings import IntegrationWarnings
from experimental.guy.agent_qa.prototyping_client import AugmentPrototypingClient
from experimental.guy.agent_qa.workspace_manager import NotionManager


class NotionPageTool(LLMTool):
    """A tool that reads from and writes to Notion pages."""

    name = "notion_page"
    description = """\
Read from or write to a Notion page. This tool can:
- Read existing pages
- Create new pages
- Update existing pages
- Add content blocks (text, headings, lists, code blocks)

The content should be provided in Markdown format. The tool will convert it to appropriate Notion blocks.

You can specify the target page in three ways:
1. Page ID: The Notion page ID (e.g., "7c71008b-816d-4ed1-8cbb-c735653e0d82")
2. Path: The path from root (e.g., "Engineering/Documentation/Setup Guide")
3. URL: The Notion page URL (e.g., "https://www.notion.so/page-title-7c71008b816d4ed18cbbc735653e0d82")
"""
    input_schema = {
        "type": "object",
        "properties": {
            "page_id_or_path": {
                "type": "string",
                "description": "The ID of the page to modify, its path from root (e.g., 'Engineering/Documentation/Setup Guide'), or its Notion URL.",
            },
            "title": {
                "type": "string",
                "description": "The title for the page. Required when creating a new page.",
            },
            "content": {
                "type": "string",
                "description": "The content to write, in Markdown format. Required for create/update/append modes.",
            },
            "mode": {
                "type": "string",
                "enum": ["read", "create", "update", "append", "search"],
                "description": "Whether to read a page, create a new page, update existing content, append to existing content, or search pages.",
            },
            "query": {
                "type": "string",
                "description": "Search query string. Required for search mode.",
            },
        },
        "required": ["page_id_or_path", "mode"],
    }

    def __init__(
        self,
        tool_call_logger: ToolCallLogger,
        cache_dir: Path,
        allowed_pages_to_modify: list[str],
        allow_create_pages: bool = False,
    ):
        """Initialize the tool.

        Args:
            tool_call_logger: The tool call logger
            cache_dir: Directory containing notion_api_token
            allowed_pages_to_modify: List of page IDs that can be modified
            allow_create_pages: Whether to allow creating new pages
        """
        super().__init__(tool_call_logger)
        self.cache_dir = cache_dir
        self.allowed_pages_to_modify = {
            id.replace("-", "") for id in allowed_pages_to_modify
        }
        self.allow_create_pages = allow_create_pages
        self._page_cache: dict[str, dict] = {}  # Cache page lookups

    def _ensure_client(self) -> AsyncClient:
        """Create a new Notion client."""
        token = self._get_token()
        return AsyncClient(auth=token)

    def _get_token(self) -> str:
        """Get Notion API token from environment or cache file."""
        # First try environment variable
        token = os.environ.get("NOTION_TOKEN")
        if token:
            return token

        # Try reading from cache file
        token_file = self.cache_dir / "notion_api_token"
        try:
            return token_file.read_text().strip()
        except FileNotFoundError:
            raise ValueError(
                "NOTION_TOKEN environment variable not set and token file not found at "
                f"{token_file}"
            )

    async def _find_page_by_path(self, path: str) -> Optional[str]:
        """Find a page ID by its path from root.

        Args:
            path: Path from root, e.g., "Engineering/Documentation/Setup Guide"

        Returns:
            The page ID if found, None otherwise
        """
        # Split path into components
        components = [c.strip() for c in path.split("/") if c.strip()]
        if not components:
            return None

        client = self._ensure_client()
        current_id = None

        # For each component, search for a page with that title under the current parent
        for component in components:
            # Use cached result if available
            cache_key = f"{current_id}:{component}"
            if cache_key in self._page_cache:
                current_id = self._page_cache[cache_key]["id"]
                continue

            # Search for pages with this title
            query = {
                "query": component,
                "filter": {"property": "object", "value": "page"},
            }
            if current_id:
                query["filter"]["parent"] = {"page_id": current_id}

            try:
                response = await client.search(**query)
                pages = response.get("results", [])

                # Find exact title match
                for page in pages:
                    title = page["properties"]["title"]["title"]
                    if title and title[0]["plain_text"].strip() == component:
                        current_id = page["id"]
                        self._page_cache[cache_key] = page
                        break
                else:
                    # No exact match found
                    return None
            except Exception:
                return None

        return current_id

    def _is_page_allowed(self, page_id: str) -> bool:
        """Check if we're allowed to modify this page."""
        if not self.allowed_pages_to_modify:
            return False
        normalized_id = page_id.replace("-", "")
        allowed_ids = {p.replace("-", "") for p in self.allowed_pages_to_modify}
        return normalized_id in allowed_ids

    async def _resolve_page_id(self, page_id_or_path: str) -> str:
        """Resolve a page ID or path to a page ID."""
        # If it looks like a UUID, treat it as an ID
        if len(page_id_or_path) == 32 or (
            len(page_id_or_path) == 36 and page_id_or_path.count("-") == 4
        ):
            return page_id_or_path.replace("-", "")

        # Otherwise treat it as a path
        try:
            page_id = await self._find_page_by_path(page_id_or_path)
            if not page_id:
                return ""
            return page_id.replace("-", "")
        except Exception:
            return ""

    async def _create_page(
        self, parent_id: str, title: str, content: str
    ) -> tuple[str, str]:
        """Create a new page under the specified parent."""
        client = self._ensure_client()

        # Create the page first
        new_page = await client.pages.create(
            parent={"page_id": parent_id},
            properties={"title": {"title": [{"text": {"content": title}}]}},
        )

        # Then add the content
        if content:
            await self._append_blocks(new_page["id"], content)

        return f"Created new page {new_page['id']}", f"Created page under {parent_id}"

    async def _update_page(
        self, page_id: str, content: str, title: Optional[str] = None
    ) -> tuple[str, str]:
        """Update an existing page's content."""
        client = self._ensure_client()

        # Update title if provided
        if title:
            await client.pages.update(
                page_id=page_id,
                properties={"title": {"title": [{"text": {"content": title}}]}},
            )

        # Get existing blocks
        blocks = []
        has_more = True
        cursor = None

        while has_more:
            response = await client.blocks.children.list(
                block_id=page_id,
                start_cursor=cursor,
            )
            blocks.extend(response["results"])
            has_more = response["has_more"]
            cursor = response["next_cursor"]

        # Delete existing blocks
        for block in blocks:
            await client.blocks.delete(block_id=block["id"])

        # Convert markdown to blocks and add new content
        if content:
            blocks = self._markdown_to_blocks(content)
            # Add blocks in batches of 100 (Notion API limit)
            for i in range(0, len(blocks), 100):
                batch = blocks[i : i + 100]
                await client.blocks.children.append(
                    block_id=page_id,
                    children=batch,
                )

        return f"Updated page {page_id}", f"Updated page {page_id}"

    def _markdown_to_blocks(self, content: str) -> list[dict]:
        """Convert markdown content to Notion blocks."""
        blocks = []
        current_code_block = []
        code_language = ""
        in_code_block = False

        lines = content.splitlines()
        i = 0
        while i < len(lines):
            line = lines[i]
            stripped = line.strip()

            # Handle code blocks
            if stripped.startswith("```"):
                if in_code_block:
                    # End code block
                    if current_code_block:
                        blocks.append(
                            {
                                "object": "block",
                                "type": "code",
                                "code": {
                                    "rich_text": [
                                        {
                                            "type": "text",
                                            "text": {
                                                "content": "\n".join(current_code_block)
                                            },
                                        }
                                    ],
                                    "language": code_language,
                                },
                            }
                        )
                    current_code_block = []
                    code_language = ""
                    in_code_block = False
                else:
                    # Start code block
                    code_language = stripped[3:].strip() or "plain text"
                    in_code_block = True
                i += 1
                continue

            if in_code_block:
                current_code_block.append(line)
                i += 1
                continue

            # Handle headers
            if stripped.startswith("#"):
                level = 1
                while level < 4 and stripped.startswith("#" * (level + 1)):
                    level += 1
                text = stripped.lstrip("#").strip()
                if text:  # Ensure there's content after the #s
                    blocks.append(
                        {
                            "object": "block",
                            "type": f"heading_{level}",
                            f"heading_{level}": {
                                "rich_text": [
                                    {"type": "text", "text": {"content": text}}
                                ]
                            },
                        }
                    )
                i += 1
                continue

            # Handle bullet lists
            if stripped.startswith("- ") or stripped.startswith("* "):
                text = stripped[2:].strip()
                if text:
                    blocks.append(
                        {
                            "object": "block",
                            "type": "bulleted_list_item",
                            "bulleted_list_item": {
                                "rich_text": [
                                    {"type": "text", "text": {"content": text}}
                                ]
                            },
                        }
                    )
                i += 1
                continue

            # Handle numbered lists
            if any(stripped.startswith(f"{n}. ") for n in range(1, 100)):
                text = stripped[stripped.index(". ") + 2 :].strip()
                if text:
                    blocks.append(
                        {
                            "object": "block",
                            "type": "numbered_list_item",
                            "numbered_list_item": {
                                "rich_text": [
                                    {"type": "text", "text": {"content": text}}
                                ]
                            },
                        }
                    )
                i += 1
                continue

            # Handle blockquotes
            if stripped.startswith("> "):
                text = stripped[2:].strip()
                if text:
                    blocks.append(
                        {
                            "object": "block",
                            "type": "quote",
                            "quote": {
                                "rich_text": [
                                    {"type": "text", "text": {"content": text}}
                                ]
                            },
                        }
                    )
                i += 1
                continue

            # Handle horizontal rules
            if stripped in ["---", "***", "___"]:
                blocks.append({"object": "block", "type": "divider", "divider": {}})
                i += 1
                continue

            # Regular paragraph (including blank lines)
            if stripped:
                blocks.append(
                    {
                        "object": "block",
                        "type": "paragraph",
                        "paragraph": {
                            "rich_text": [{"type": "text", "text": {"content": line}}]
                        },
                    }
                )
            else:
                # Add empty paragraph for spacing
                blocks.append(
                    {
                        "object": "block",
                        "type": "paragraph",
                        "paragraph": {
                            "rich_text": [{"type": "text", "text": {"content": ""}}]
                        },
                    }
                )
            i += 1

        # Handle any remaining code block
        if current_code_block:
            blocks.append(
                {
                    "object": "block",
                    "type": "code",
                    "code": {
                        "rich_text": [
                            {
                                "type": "text",
                                "text": {"content": "\n".join(current_code_block)},
                            }
                        ],
                        "language": code_language,
                    },
                }
            )

        return blocks

    async def _append_blocks(self, page_id: str, content: str) -> tuple[str, str]:
        """Append blocks to a page.

        Args:
            page_id: The page ID
            content: The content to append in markdown format

        Returns:
            Tuple of (tool output, user message)
        """
        try:
            blocks = self._markdown_to_blocks(content)
            await self._append_block_batch(page_id, blocks)
            return "Successfully appended content", f"Appended to page {page_id}"
        except Exception as e:
            return f"Error: {str(e)}", f"Failed to append to page: {str(e)}"

    async def _append_block_batch(
        self, page_id: str, blocks: list[dict], batch_size: int = 100
    ) -> None:
        """Append blocks in batches to avoid API limits.

        Args:
            page_id: The page ID
            blocks: List of block objects to append
            batch_size: Maximum blocks per batch
        """
        client = self._ensure_client()
        for i in range(0, len(blocks), batch_size):
            batch = blocks[i : i + batch_size]
            await client.blocks.children.append(
                block_id=page_id,
                children=batch,
            )

    async def _read_page(self, page_id: str) -> tuple[str, str]:
        """Read a page's content."""
        client = self._ensure_client()

        try:
            # Get the page first to get its title
            page = await client.pages.retrieve(page_id=page_id)
            title = self._get_page_title(page)
            content = [f"# {title}\n"]

            # Get all blocks
            blocks = []
            has_more = True
            cursor = None

            while has_more:
                response = await client.blocks.children.list(
                    block_id=page_id,
                    start_cursor=cursor,
                )
                blocks.extend(response["results"])
                has_more = response["has_more"]
                cursor = response["next_cursor"]

            # Convert blocks to markdown
            for block in blocks:
                block_type = block["type"]
                if block_type == "paragraph":
                    text = block["paragraph"]["rich_text"]
                    if text:
                        content.append(text[0]["plain_text"])
                elif block_type.startswith("heading_"):
                    level = block_type[-1]  # Get the heading level (1, 2, 3)
                    text = block[block_type]["rich_text"]
                    if text:
                        content.append(f"{'#' * int(level)} {text[0]['plain_text']}")
                elif block_type == "bulleted_list_item":
                    text = block["bulleted_list_item"]["rich_text"]
                    if text:
                        content.append(f"- {text[0]['plain_text']}")
                elif block_type == "numbered_list_item":
                    text = block["numbered_list_item"]["rich_text"]
                    if text:
                        content.append(f"1. {text[0]['plain_text']}")
                elif block_type == "code":
                    text = block["code"]["rich_text"]
                    language = block["code"].get("language", "")
                    if text:
                        content.append(f"```{language}")
                        content.append(text[0]["plain_text"])
                        content.append("```")
                elif block_type == "quote":
                    text = block["quote"]["rich_text"]
                    if text:
                        content.append(f"> {text[0]['plain_text']}")
                elif block_type == "divider":
                    content.append("---")

            result = "\n".join(content)
            if len(blocks) == 0:
                return result, f"Read page {page_id} (empty)"
            return result, f"Read page {page_id}"

        except notion_errors.APIResponseError as e:
            if "Could not find page" in str(e):
                return (
                    "Error: Could not find the page. Make sure:\n"
                    "1. The page ID or URL is correct\n"
                    "2. The page exists\n"
                    "3. The Notion integration has access to the page",
                    "Failed to read page: not found",
                )
            return f"Error: {str(e)}", "Failed to read page: API error"
        except Exception as e:
            return f"Error: {str(e)}", "Failed to read page: error"

    def _get_page_title(self, page: dict) -> str:
        """Extract page title from page object."""
        try:
            # Try different title fields
            if "Topic" in page["properties"]:
                title_items = page["properties"]["Topic"]["title"]
                if title_items:
                    return title_items[0]["plain_text"].strip()
            if "Name" in page["properties"]:
                title_items = page["properties"]["Name"]["title"]
                if title_items:
                    return title_items[0]["plain_text"].strip()
            if "title" in page["properties"]:
                title_items = page["properties"]["title"]["title"]
                if title_items:
                    return title_items[0]["plain_text"].strip()
            return "Untitled"
        except (KeyError, IndexError):
            return "Untitled"

    async def _append_to_page(self, page_id: str, content: str) -> tuple[str, str]:
        """Append content to a page."""
        if not self._is_page_allowed(page_id):
            return (
                f"Error: Not allowed to modify page {page_id}",
                f"Failed to append to page: not allowed to modify {page_id}",
            )

        client = self._ensure_client()

        # Convert content to blocks
        blocks = []
        for line in content.split("\n"):
            if line.strip():
                blocks.append(
                    {
                        "object": "block",
                        "type": "paragraph",
                        "paragraph": {
                            "rich_text": [{"type": "text", "text": {"content": line}}]
                        },
                    }
                )

        try:
            # Add blocks in batches of 100 (Notion API limit)
            for i in range(0, len(blocks), 100):
                batch = blocks[i : i + 100]
                await client.blocks.children.append(
                    block_id=page_id,
                    children=batch,
                )
            return (
                f"Successfully appended content to page {page_id}",
                f"Appended to page {page_id}",
            )
        except Exception as e:
            return f"Error: {str(e)}", f"Failed to append to page: {str(e)}"

    async def _search_pages(self, query: str) -> tuple[str, str]:
        """Search Notion pages using the search API."""
        client = self._ensure_client()

        try:
            # Call Notion search API
            response = await client.search(
                query=query, filter={"property": "object", "value": "page"}
            )

            if not response["results"]:
                return "No results found.", "Search complete"

            # Format results as markdown
            results = []
            for page in response["results"]:
                try:
                    title = self._get_page_title(page)
                except (KeyError, IndexError):
                    title = "Untitled"
                page_id = page["id"]
                url = page.get("url", "")
                results.append(f"- [{title}]({url}) (ID: {page_id})")

            return "\n".join(results), "Search complete"
        except Exception as e:
            return f"Error searching pages: {str(e)}", "Search failed"

    async def run_impl_async(
        self,
        tool_input: dict[str, Any],
        dialog_messages: Optional[DialogMessages] = None,
    ) -> ToolImplOutput:
        """Run the tool asynchronously."""
        mode = tool_input["mode"]
        page_id_or_path = tool_input.get("page_id_or_path", "")
        content = tool_input.get("content")
        title = tool_input.get("title")

        # Validate mode
        valid_modes = ["read", "create", "update", "append", "search"]
        if mode not in valid_modes:
            return ToolImplOutput(
                tool_output=f"Error: invalid mode {mode}", tool_result_message="Error"
            )

        # Handle search mode first since it doesn't need page ID
        if mode == "search":
            if "query" not in tool_input:
                return ToolImplOutput(
                    tool_output="Error: query parameter required for search mode",
                    tool_result_message="Error",
                )
            tool_output, user_message = await self._search_pages(tool_input["query"])
            return ToolImplOutput(tool_output, user_message)

        # For create mode, validate title and content first
        if mode == "create":
            if not title:
                return ToolImplOutput(
                    tool_output="Error: title is required for create mode",
                    tool_result_message="Error",
                )
            if not content:
                return ToolImplOutput(
                    tool_output="Error: content is required for create mode",
                    tool_result_message="Error",
                )
            if not self.allow_create_pages:
                return ToolImplOutput(
                    tool_output="Error: Creating new pages is not allowed",
                    tool_result_message="Error",
                )

        # For update/append modes, validate content
        if mode in ["update", "append"] and not content:
            return ToolImplOutput(
                tool_output=f"Error: content is required for {mode} mode",
                tool_result_message="Error",
            )

        # For all other modes, resolve the page ID
        client = self._ensure_client()

        # First check if it's a URL
        if page_id_or_path.startswith("https://www.notion.so/"):
            # Extract page ID from URL
            # URL format: https://www.notion.so/page-title-page-id?pvs=4
            # or: https://www.notion.so/page-id
            try:
                url_parts = page_id_or_path.split("?")[0].split("-")
                page_id = url_parts[-1]
                # Remove any hyphens from the page ID
                page_id = page_id.replace("-", "")
                # Validate it's a UUID
                if len(page_id) == 32:
                    try:
                        int(page_id, 16)
                        is_uuid = True
                    except ValueError:
                        is_uuid = False
                else:
                    is_uuid = False
            except Exception:
                is_uuid = False
        else:
            # Check if it's a UUID
            is_uuid = False
            page_id = page_id_or_path.replace("-", "")
            if len(page_id) == 32:
                # Check if it's all hex digits
                try:
                    int(page_id, 16)
                    is_uuid = True
                except ValueError:
                    is_uuid = False

        if not is_uuid:
            # Search for the page by title
            try:
                response = await client.search(
                    query=page_id_or_path,
                    filter={"property": "object", "value": "page"},
                )
                results = response.get("results", [])
                if results:
                    # Find exact title match
                    for result in results:
                        try:
                            # Try different title fields
                            title_match = None
                            if "Topic" in result["properties"]:
                                title_items = result["properties"]["Topic"]["title"]
                                title_match = (
                                    title_items[0]["plain_text"].strip()
                                    if title_items
                                    else None
                                )
                            if not title_match and "Name" in result["properties"]:
                                title_items = result["properties"]["Name"]["title"]
                                title_match = (
                                    title_items[0]["plain_text"].strip()
                                    if title_items
                                    else None
                                )
                            if not title_match and "title" in result["properties"]:
                                title_items = result["properties"]["title"]["title"]
                                title_match = (
                                    title_items[0]["plain_text"].strip()
                                    if title_items
                                    else None
                                )

                            if title_match:
                                print(f"Checking title: {title_match}")
                                if title_match == page_id_or_path:
                                    page_id = result["id"]
                                    print(f"Found exact match: {page_id}")
                                    break
                        except (KeyError, IndexError):
                            continue
                    else:
                        # No exact match, use first result
                        page_id = results[0]["id"]
                        print(f"Using first result: {page_id}")
                else:
                    # For create mode, use the provided page ID
                    if mode == "create":
                        page_id = page_id_or_path.replace("-", "")
                    else:
                        return ToolImplOutput(
                            tool_output=f"Error: Could not find page {page_id_or_path}",
                            tool_result_message=f"Failed to {mode} page: page not found",
                        )
            except Exception as e:
                return ToolImplOutput(
                    tool_output=f"Error: Failed to search for page: {str(e)}",
                    tool_result_message=f"Failed to {mode} page: search error",
                )

        # Check permissions for write operations
        if mode in ["create", "update", "append"]:
            if not self._is_page_allowed(page_id):
                return ToolImplOutput(
                    tool_output=f"Error: Not allowed to modify page {page_id_or_path}",
                    tool_result_message=f"Failed to {mode} page: not allowed",
                )

        # Handle each mode
        if mode == "create":
            tool_output, user_message = await self._create_page(page_id, title, content)
            return ToolImplOutput(tool_output, user_message)
        elif mode == "update":
            tool_output, user_message = await self._update_page(page_id, content, title)
            return ToolImplOutput(tool_output, user_message)
        elif mode == "append":
            tool_output, user_message = await self._append_blocks(page_id, content)
            return ToolImplOutput(tool_output, user_message)
        elif mode == "read":
            tool_output, user_message = await self._read_page(page_id)
            return ToolImplOutput(tool_output, user_message)
        else:
            return ToolImplOutput(
                tool_output=f"Error: Unknown mode {mode}",
                tool_result_message=f"Failed: unknown mode {mode}",
            )

    def run_impl(
        self,
        tool_input: dict[str, Any],
        dialog_messages: Optional[DialogMessages] = None,
    ) -> ToolImplOutput:
        """Run the tool by calling the async implementation."""
        return asyncio.run(self.run_impl_async(tool_input, dialog_messages))

    def get_tool_start_message(self, tool_input: dict[str, Any]) -> str:
        mode = tool_input["mode"]
        if mode == "search":
            return f"Searching Notion pages for: {tool_input.get('query', '')}"

        page_id_or_path = tool_input.get("page_id_or_path", "")
        if mode == "create":
            return f"Creating new page under {page_id_or_path}"
        elif mode == "update":
            return f"Updating page {page_id_or_path}"
        elif mode == "append":
            return f"Appending to page {page_id_or_path}"
        elif mode == "read":
            return f"Reading page {page_id_or_path}"
        else:
            return f"Unknown mode: {mode}"


class NotionRetrievalTool(LLMTool):
    """A tool that retrieves information from Notion exports."""

    name = "ask_for_notion_snippets"

    description = """\
Retrieve information from Notion documents. This tool searches through exported Notion pages
and returns relevant snippets.

The tool accepts requests similar to the codebase retrieval tool, but focuses on documentation
and knowledge stored in Notion. This tool can help understand software engineering best practices,
how to do certain tasks, and how to orient in the codebase.
"""

    def __init__(
        self,
        tool_call_logger: ToolCallLogger,
        augment_client: AugmentPrototypingClient,
        notion_manager: NotionManager,
        max_tool_chars: int,
        max_retrieval_chunks=1000,
        max_result_chunks=20,
        integration_warnings: Optional[IntegrationWarnings] = None,
    ):
        """Initialize the tool.

        Args:
            tool_call_logger: Tool call logger
            augment_client: Augment client
            notion_manager: Notion manager
            max_tool_chars: Maximum characters in tool output
            max_retrieval_chunks: Maximum chunks to retrieve
            max_result_chunks: Maximum chunks to return
            integration_warnings: Optional integration warnings handler
        """
        super().__init__(tool_call_logger)
        self.augment_client = augment_client
        self.notion_manager = notion_manager
        self.max_retrieval_chunks = max_retrieval_chunks
        self.max_result_chunks = max_result_chunks
        self.max_tool_chars = max_tool_chars

        try:
            # Try to access Notion to check credentials
            self.notion_manager.get_pages()

            retriever = QueryOnlyDocumentIndex(
                augment_client=self.augment_client,
                workspace_manager=self.notion_manager,  # NotionManager implements the same interface
                max_retrieval_chunks=self.max_retrieval_chunks,
            )
        except Exception:  # Notion client may raise different exceptions
            if integration_warnings:
                integration_warnings.warn_if_missing("notion")
            raise

        self.internal_tool = MichielCodebaseRetrievalTool(
            retriever=retriever,
            max_tool_chars=self.max_tool_chars,
            max_num_chunks=self.max_result_chunks,
        )

        self.input_schema = self.internal_tool.get_input_schema()

    def run_impl(
        self,
        tool_input: dict[str, Any],
        dialog_messages: Optional[DialogMessages] = None,
    ) -> ToolImplOutput:
        tool_result, _ = self.internal_tool.activate_tool(
            ToolCall(
                tool_name=self.name,
                tool_input=tool_input,
                tool_call_id="fake-call-id",
            )
        )
        format_tool_result, _ = self.internal_tool.format_tool_result(tool_result)
        return ToolImplOutput(
            format_tool_result.tool_output, "Notion information retrieved"
        )

    def get_tool_start_message(self, tool_input: dict[str, Any]) -> str:
        requests = [
            request["description"] for request in tool_input["code_section_requests"]
        ]
        return f"Retrieving Notion information: {', '.join(requests)}"
