#!/usr/bin/env python3
"""Test for the --no-pager flag in interactive_agent.py."""

import argparse
import io
import sys
from contextlib import redirect_stdout
from unittest.mock import patch

# Import the show_in_pager function directly
from experimental.guy.agent_qa.interactive_agent import show_in_pager


def test_show_in_pager_with_no_pager():
    """Test that show_in_pager prints directly to console when no_pager is True."""
    test_text = "This is a test message"

    # Capture stdout
    stdout_buffer = io.StringIO()
    with redirect_stdout(stdout_buffer):
        # Call show_in_pager with no_pager=True
        show_in_pager(test_text, no_pager=True)

    # Check that the text was printed to stdout
    output = stdout_buffer.getvalue()
    assert test_text in output, f"Expected '{test_text}' in output, got '{output}'"
    print("Test passed: show_in_pager with no_pager=True prints directly to console")


def test_show_in_pager_with_pager():
    """Test that show_in_pager uses the pager when no_pager is False."""
    test_text = "This is a test message"

    # Mock subprocess.Popen to avoid actually launching less
    with patch("subprocess.Popen") as mock_popen:
        # Set up the mock
        mock_process = mock_popen.return_value
        mock_process.communicate.return_value = (None, None)

        # Call show_in_pager with no_pager=False
        show_in_pager(test_text, no_pager=False)

        # Check that Popen was called with the correct arguments
        mock_popen.assert_called_once()
        args, kwargs = mock_popen.call_args
        assert args[0] == ["less", "-RFX"], f"Expected ['less', '-RFX'], got {args[0]}"
        assert kwargs["stdin"] is not None, "Expected stdin to be set"
        assert kwargs["text"] is True, "Expected text to be True"

        # Check that communicate was called with the correct text
        mock_process.communicate.assert_called_once_with(input=test_text)

    print("Test passed: show_in_pager with no_pager=False uses the pager")


def test_cli_interface_with_no_pager_flag():
    """Test that the --no-pager flag is correctly parsed."""
    # Create a parser with just the --no-pager flag
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--no-pager",
        action="store_true",
        help="Disable the pager for command output and print directly to console",
    )

    # Test with --no-pager flag
    args = parser.parse_args(["--no-pager"])
    assert args.no_pager is True, "Expected args.no_pager to be True"

    # Test without --no-pager flag
    args = parser.parse_args([])
    assert args.no_pager is False, "Expected args.no_pager to be False"

    print("Test passed: --no-pager flag is correctly parsed")


if __name__ == "__main__":
    test_show_in_pager_with_no_pager()
    test_show_in_pager_with_pager()
    test_cli_interface_with_no_pager_flag()
    print("All tests passed!")
