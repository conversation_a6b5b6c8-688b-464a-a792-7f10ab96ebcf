"""Utilities for using next-edit."""

from research.core.diff_utils import (
    Repository,
    compute_repo_diff,
    get_source_path,
    get_target_path,
)
from base.blob_names.python.blob_names import get_blob_name
from base.datasets.next_edit import <PERSON><PERSON>hang<PERSON>, WorkingDirectoryChange


def vcs_change_from_repo_change(
    repo_before: Repository, repo_after: Repository
) -> VCSChange:
    """Convert a Repository change to a VCSChange object."""
    patch = compute_repo_diff(repo_before, repo_after)
    changes: list[WorkingDirectoryChange] = []

    # Create maps for easier lookup
    before_files = {f.path: f.contents for f in repo_before.files}
    after_files = {f.path: f.contents for f in repo_after.files}

    # Handle added files
    for patched_file in patch.added_files:
        path = get_target_path(patched_file)
        if path is None:
            raise ValueError(f"Added file has no target path: {patched_file}")
        after_content = after_files[path]
        current_blob = get_blob_name(path, after_content.encode())
        changes.append(
            WorkingDirectoryChange(
                before_path="",
                after_path=path,
                change_type=WorkingDirectoryChange.ChangeType.ADDED,
                head_blob_name="",
                current_blob_name=current_blob,
                indexed_blob_name=current_blob,
            )
        )

    # Handle deleted files
    for patched_file in patch.removed_files:
        path = get_source_path(patched_file)
        if path is None:
            raise ValueError(f"Deleted file has no source path: {patched_file}")
        before_content = before_files[path]
        head_blob = get_blob_name(path, before_content.encode())
        changes.append(
            WorkingDirectoryChange(
                before_path=path,
                after_path="",
                change_type=WorkingDirectoryChange.ChangeType.DELETED,
                head_blob_name=head_blob,
                current_blob_name="",
                indexed_blob_name="",
            )
        )

    # Handle modified files
    for patched_file in patch.modified_files:
        path = get_target_path(patched_file)
        if path is None:
            raise ValueError(f"Modified file has no target path: {patched_file}")
        before_content = before_files[path]
        after_content = after_files[path]
        head_blob = get_blob_name(path, before_content.encode())
        current_blob = get_blob_name(path, after_content.encode())
        changes.append(
            WorkingDirectoryChange(
                before_path=path,
                after_path=path,
                change_type=WorkingDirectoryChange.ChangeType.MODIFIED,
                head_blob_name=head_blob,
                current_blob_name=current_blob,
                indexed_blob_name=current_blob,
            )
        )

    return VCSChange(working_directory_changes=changes)
