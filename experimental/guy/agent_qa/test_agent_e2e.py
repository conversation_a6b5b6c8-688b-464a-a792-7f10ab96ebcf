"""Tests for command execution in interactive agent."""

import os
import pickle
import subprocess
from pathlib import Path
from unittest.mock import patch, MagicMock
import pytest
from experimental.guy.agent_qa.agent_mode import Agent<PERSON><PERSON>, ConstantModeProvider
from experimental.guy.agent_qa.tools.web_search_tool import WebSearchTool
from experimental.guy.agent_qa.tools.web_search import (
    SearchResult,
    get_search_credentials,
    WebSearchError,
)
from experimental.guy.agent_qa.test_utils import get_augment_token
from experimental.guy.agent_qa.tools.web_page_fetcher_tool import WebPageFetcherTool

from base.augment_client.client import AugmentClient
from experimental.guy.agent_qa.agents import Agent
from experimental.guy.agent_qa.builtin_tools import (
    ClarifyTool,
    CodebaseKnowledgeTool,
    CodebaseRetrievalTool,
    EditFileTool,
    FileEditClient,
    LLMTool,
    ReadFileTool,
    SaveFileTool,
    CompleteTool,
)
from experimental.guy.agent_qa.workspace_manager import WorkspaceManagerImpl
from research.agents.tools import Too<PERSON><PERSON><PERSON><PERSON>ogger, DialogMessages
from research.llm_apis.llm_client import (
    # LLMClient,
    AnthropicVertexClient,
    <PERSON><PERSON>rompt,
    TextResult,
    ToolCall,
)
from experimental.guy.agent_qa.prototyping_client import (
    AugmentPrototypingClient,
    get_dev_deployment_api_proxy_url,
)


def test_command_execution_with_backslash(tmp_path: Path):
    """Test command execution with backslash prefix in a git repo."""
    # Setup a git repo with some files
    repo_path = tmp_path / "test_repo"
    repo_path.mkdir()

    # Initialize git repo
    subprocess.run(["git", "init"], cwd=repo_path, check=True)
    subprocess.run(
        ["git", "config", "user.email", "<EMAIL>"], cwd=repo_path, check=True
    )
    subprocess.run(
        ["git", "config", "user.name", "Test User"], cwd=repo_path, check=True
    )

    # Create some test files
    (repo_path / "file1.txt").write_text("test1")
    (repo_path / "file2.txt").write_text("test2")

    # Add and commit files
    subprocess.run(["git", "add", "."], cwd=repo_path, check=True)
    subprocess.run(["git", "commit", "-m", "Initial commit"], cwd=repo_path, check=True)

    # Change to repo directory before running
    old_cwd = os.getcwd()
    os.chdir(repo_path)
    try:
        # Execute ls command
        result = subprocess.run(["ls"], capture_output=True, text=True, check=True)

        # Verify output contains the expected files
        assert "file1.txt" in result.stdout
        assert "file2.txt" in result.stdout
    finally:
        os.chdir(old_cwd)


SYSTEM_PROMPT = """\
You are an AI assistant, with access to the developer's codebase.
You can read from and write to the codebase using the provided tools.
"""


@pytest.fixture
def real_anthropic_client():
    """Create a real AnthropicVertexClient for e2e tests."""
    return AnthropicVertexClient(
        project_id="augment-research-gsc",
        model_name="claude-3-5-sonnet-v2@20241022",
        max_retries=50,
    )


@pytest.fixture
def real_anthropic_haiku_client():
    """Create a real AnthropicVertexClient for e2e tests."""
    return AnthropicVertexClient(
        project_id="augment-research-gsc",
        model_name="claude-3-5-haiku@20241022",
        max_retries=50,
    )


@pytest.fixture
def base_agent(tmp_path, real_anthropic_client):
    token = get_augment_token()

    augment_client = AugmentPrototypingClient(
        get_dev_deployment_api_proxy_url(user_name="guy"), auth_token=token
    )

    workspace_manager = WorkspaceManagerImpl(augment_client, root=Path(tmp_path))

    tool_call_logger = ToolCallLogger(
        verbose=True,
        use_tool_supplied_messages=True,
    )

    # Initialize with minimal tools - specific tests can add what they need
    tools: list[LLMTool] = [
        SaveFileTool(tool_call_logger, workspace_manager),
    ]

    mode = AgentMode(system_prompt=SYSTEM_PROMPT, tools=tools)
    mode_provider = ConstantModeProvider(mode)

    return Agent(
        client=real_anthropic_client,
        mode_provider=mode_provider,
        tool_call_logger=tool_call_logger,
        max_output_tokens_per_turn=8192,
        max_turns=100,
        state_manager=None,
    )


def _get_agent(tmp_path, client):
    token = get_augment_token()

    augment_client = AugmentPrototypingClient(
        get_dev_deployment_api_proxy_url(user_name="guy"), auth_token=token
    )

    workspace_manager = WorkspaceManagerImpl(augment_client, root=Path(tmp_path))

    tool_call_logger = ToolCallLogger(
        verbose=True,
        use_tool_supplied_messages=True,
    )

    tools: list[LLMTool] = [
        SaveFileTool(tool_call_logger, workspace_manager),
        ReadFileTool(tool_call_logger, workspace_manager.root),
        WebPageFetcherTool(tool_call_logger),  # Always include web page fetcher
    ]

    # Only add web search if credentials are available
    try:
        get_search_credentials()
        tools.append(WebSearchTool(tool_call_logger))
    except WebSearchError:
        pass

    mode = AgentMode(name="default", system_prompt=SYSTEM_PROMPT, tools=tools)
    mode_provider = ConstantModeProvider(mode)

    return Agent(
        client=client,
        mode_provider=mode_provider,
        tool_call_logger=tool_call_logger,
        max_output_tokens_per_turn=8192,
        max_turns=100,
        state_manager=None,
    )


@pytest.fixture
def agent(tmp_path, real_anthropic_client):
    return _get_agent(tmp_path, real_anthropic_client)


@pytest.fixture
def haiku_agent(tmp_path, real_anthropic_haiku_client):
    return _get_agent(tmp_path, real_anthropic_haiku_client)


def test_save_file(tmp_path, haiku_agent):
    haiku_agent.run_agent("Write 'hello world' into a file foo.txt")
    expected_file = Path(tmp_path) / "foo.txt"
    assert expected_file.exists()
    actual_contents = expected_file.read_text("utf8").strip()
    assert actual_contents == "hello world"


@pytest.mark.manual
def test_resume_with_files(tmp_path, agent):
    # First create the file
    agent.run_agent("write a 'hello world' python script to foo.py")

    expected_file = Path(tmp_path) / "foo.py"
    assert expected_file.exists()

    # Save the dialog state to a file
    state_file = Path(tmp_path) / "agent_state.pickle"
    agent.dialog.save_messages(state_file)

    # Verify the state file was created
    assert state_file.exists(), "State file was not created"

    # Create a new agent with the saved state
    token = get_augment_token()
    augment_client = AugmentPrototypingClient(
        get_dev_deployment_api_proxy_url(user_name="guy"), auth_token=token
    )
    workspace_manager = WorkspaceManagerImpl(augment_client, root=Path(tmp_path))
    tool_call_logger = ToolCallLogger(verbose=True, use_tool_supplied_messages=True)
    tools = [
        SaveFileTool(tool_call_logger, workspace_manager),
        ReadFileTool(tool_call_logger, workspace_manager.root),
    ]
    mode = AgentMode(name="default", system_prompt=SYSTEM_PROMPT, tools=tools)
    mode_provider = ConstantModeProvider(mode)

    # Create a new dialog from the saved messages
    dialog = DialogMessages.from_saved_messages(state_file)

    new_agent = Agent(
        client=agent.client,
        mode_provider=mode_provider,
        tool_call_logger=tool_call_logger,
        max_output_tokens_per_turn=8192,
        max_turns=100,
    )
    # Set the dialog directly
    new_agent.dialog = dialog
    new_agent.dialog.update_listener = new_agent

    # Run the new agent with resume=True
    new_agent.run_agent(
        "read foo.py, then create a new file foo_with_comment.py that adds a comment at the top explaining what the script does",
        resume=True,
    )

    # Check the new file
    new_file = Path(tmp_path) / "foo_with_comment.py"
    assert new_file.exists(), "New file was not created"
    actual_contents = new_file.read_text("utf8").strip()
    assert "#" in actual_contents, "No comment was added to the file"
    assert "print" in actual_contents, "Original code was lost"


@pytest.mark.manual
def test_resume(tmp_path, agent):
    # First create the file and run the agent
    agent.run_agent("answer with the full phrase: the quick brown fox ...")

    # Save the dialog state to a file
    state_file = Path(tmp_path) / "agent_state2.pickle"
    agent.dialog.save_messages(state_file)

    # Verify the state file was created
    assert state_file.exists(), "State file was not created"

    # Create a new agent with the saved state
    token = get_augment_token()
    augment_client = AugmentPrototypingClient(
        get_dev_deployment_api_proxy_url(user_name="guy"), auth_token=token
    )
    workspace_manager = WorkspaceManagerImpl(augment_client, root=Path(tmp_path))
    tool_call_logger = ToolCallLogger(verbose=True, use_tool_supplied_messages=True)
    tools = [SaveFileTool(tool_call_logger, workspace_manager)]
    mode = AgentMode(name="default", system_prompt=SYSTEM_PROMPT, tools=tools)
    mode_provider = ConstantModeProvider(mode)

    # Create a new dialog from the saved messages
    dialog = DialogMessages.from_saved_messages(state_file)

    new_agent = Agent(
        client=agent.client,
        mode_provider=mode_provider,
        tool_call_logger=tool_call_logger,
        max_output_tokens_per_turn=8192,
        max_turns=100,
    )
    # Set the dialog directly
    new_agent.dialog = dialog
    new_agent.dialog.update_listener = new_agent

    # Run the new agent with resume=True
    result = new_agent.run_agent(
        "repeat your last message",
        resume=True,
    )

    assert (
        "the quick brown fox jumps over the lazy dog" in result.lower()
    ), "Agent didn't repeat the message"
