"""User confirmation utilities."""

from abc import ABC, abstractmethod


class UserConfirmationProvider(ABC):
    """Interface for getting user confirmation."""

    @abstractmethod
    def confirm(self, message: str) -> bool:
        """Ask the user for confirmation.
        
        Args:
            message: The message to show to the user
            
        Returns:
            True if user confirmed, False otherwise
        """
        pass


class InteractiveUserConfirmationProvider(UserConfirmationProvider):
    """Gets confirmation by asking the user interactively."""
    
    def confirm(self, message: str) -> bool:
        print(message)
        response = input("\nDo you want to proceed? [y/N] ").lower()
        if response != 'y':
            print("Operation cancelled.")
            return False
        return True


class AlwaysConfirmUserConfirmationProvider(UserConfirmationProvider):
    """Always confirms. Useful for tests."""
    
    def confirm(self, message: str) -> bool:
        print(message)  # Still print the message for test verification
        return True
