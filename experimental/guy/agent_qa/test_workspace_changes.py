#!/usr/bin/env python3
"""
Test the workspace changes handling in the chat_history method.
"""

from unittest.mock import Mock

import pytest

from experimental.guy.agent_qa.chat_history import (
    get_chat_history,
)
from research.agents.changed_file import ChangedFile
from research.agents.tools import (
    LoggedLanguageModelCall,
    LoggedToolCall,
    LoggedWorkspace<PERSON>hang<PERSON>,
    ToolCallLogger,
)
from research.llm_apis.llm_client import TextPrompt, TextResult, ToolCall


class CLIInterface:
    """Mock CLIInterface for testing."""

    def __init__(self):
        self.tool_call_logger = ToolCallLogger()
        from research.agents.tools import DialogMessages

        self.agent = Mock()
        self.agent.dialog = DialogMessages()

    def chat_history(self) -> dict:
        """Build a RemoteAgentChatHistoryResponse proto from the DialogMessages object."""
        return get_chat_history(self.agent.dialog)


def create_mock_llm_call(started=True, messages=None, response=None):
    """Create a mock LLM call with custom messages and response."""
    return LoggedLanguageModelCall(
        started=started,
        messages=messages or [],
        max_tokens=1000,
        system_prompt="You are a helpful assistant.",
        temperature=0.0,
        tools=[],
        tool_choice=None,
        response=response,
        response_metadata={},
    )


def create_mock_tool_call(started=True, tool_name="test_tool", tool_input=None):
    """Create a mock tool call with custom name and input."""
    return LoggedToolCall(
        started=started,
        tool=Mock(),
        tool_input=tool_input or {},
        tool_output=Mock(),
        tool_message=Mock(),
    )


def test_workspace_changes_with_active_exchange():
    """Test that workspace changes are correctly associated with the current exchange."""
    # Create a CLI interface
    cli = CLIInterface()

    # Add messages to DialogMessages to simulate a conversation

    # First user message
    cli.agent.dialog.add_user_prompt("Create a file called hello.py")

    # First assistant response
    cli.agent.dialog.add_model_response(
        [TextResult(text="I'll create that file for you.")]
    )

    # Add workspace changes
    changed_files = [
        ChangedFile.added(
            new_path="hello.py",
            new_contents="print('Hello, world!')",
        )
    ]
    cli.agent.dialog.add_workspace_changes(changed_files)

    # Get the chat history
    history = cli.chat_history()

    # Verify that we have the expected number of exchanges
    assert "chat_history" in history, "chat_history field missing"
    assert (
        len(history["chat_history"]) == 1
    ), f"Expected 1 exchange, got {len(history['chat_history'])}"

    # Verify that the exchange has the workspace changes
    exchange = history["chat_history"][0]
    assert "changed_files" in exchange, "changed_files field missing"
    assert (
        len(exchange["changed_files"]) == 1
    ), f"Expected 1 changed file, got {len(exchange['changed_files'])}"

    # Verify the changed file details
    changed_file = exchange["changed_files"][0]
    assert (
        changed_file["new_path"] == "hello.py"
    ), f"Expected new_path to be 'hello.py', got '{changed_file['new_path']}'"
    assert (
        "Hello, world!" in changed_file["new_contents"]
    ), f"Expected 'Hello, world!' in new_contents, got '{changed_file['new_contents']}'"

    print(
        "Test passed: Workspace changes are correctly associated with the current exchange"
    )


@pytest.mark.skip(
    "This currently doesn't raise an exception anymore, just prints a warning."
)
def test_workspace_changes_without_active_exchange():
    """Test that an error is raised when workspace changes are encountered without an active exchange."""
    # Create a CLI interface
    cli = CLIInterface()

    # Add workspace changes without an active exchange
    changed_files = [
        ChangedFile.added(new_path="hello.py", new_contents="print('Hello, world!')")
    ]

    # In the new implementation, we can add workspace changes without an active exchange
    # So we'll just verify that it works without raising an error
    cli.agent.dialog.add_workspace_changes(changed_files)

    # Get the chat history
    history = cli.chat_history()

    # Verify that we have the expected number of exchanges
    assert "chat_history" in history, "chat_history field missing"
    # There should be no exchanges since we only added workspace changes
    assert (
        len(history["chat_history"]) == 0
    ), f"Expected 0 exchanges, got {len(history['chat_history'])}"

    # In the current implementation, workspace changes without an active exchange are not included in the history
    # This is a change from the previous behavior, but it's consistent with how DialogMessages works
    # So we'll just verify that the chat_history field exists and is empty

    print(
        "Test passed: Workspace changes without an active exchange are handled correctly"
    )


def test_multiple_workspace_changes():
    """Test that multiple workspace changes are handled correctly."""
    # Create a CLI interface
    cli = CLIInterface()

    # Add messages to DialogMessages to simulate a conversation

    # First user message
    cli.agent.dialog.add_user_prompt("Create two files")

    # First assistant response
    cli.agent.dialog.add_model_response(
        [TextResult(text="I'll create those files for you.")]
    )

    # First workspace change
    changed_files1 = [
        ChangedFile.added(new_path="file1.py", new_contents="print('File 1')")
    ]
    cli.agent.dialog.add_workspace_changes(changed_files1)

    # Second user message
    cli.agent.dialog.add_user_prompt("Now create the second file")

    # Second assistant response
    cli.agent.dialog.add_model_response(
        [TextResult(text="Creating the second file now.")]
    )

    # Second workspace change
    changed_files2 = [
        ChangedFile.added(new_path="file2.py", new_contents="print('File 2')")
    ]
    cli.agent.dialog.add_workspace_changes(changed_files2)

    # Get the chat history
    history = cli.chat_history()

    # Verify that we have the expected number of exchanges
    assert "chat_history" in history, "chat_history field missing"
    assert (
        len(history["chat_history"]) == 2
    ), f"Expected 2 exchanges, got {len(history['chat_history'])}"

    # Verify that the first exchange has the first workspace change
    exchange1 = history["chat_history"][0]
    assert "changed_files" in exchange1, "changed_files field missing in first exchange"
    assert (
        len(exchange1["changed_files"]) == 1
    ), f"Expected 1 changed file in first exchange, got {len(exchange1['changed_files'])}"
    assert (
        exchange1["changed_files"][0]["new_path"] == "file1.py"
    ), f"Expected new_path to be 'file1.py', got '{exchange1['changed_files'][0]['new_path']}'"

    # Verify that the second exchange has the second workspace change
    exchange2 = history["chat_history"][1]
    assert (
        "changed_files" in exchange2
    ), "changed_files field missing in second exchange"
    assert (
        len(exchange2["changed_files"]) == 1
    ), f"Expected 1 changed file in second exchange, got {len(exchange2['changed_files'])}"
    assert (
        exchange2["changed_files"][0]["new_path"] == "file2.py"
    ), f"Expected new_path to be 'file2.py', got '{exchange2['changed_files'][0]['new_path']}'"

    print("Test passed: Multiple workspace changes are handled correctly")


if __name__ == "__main__":
    test_workspace_changes_with_active_exchange()
    test_workspace_changes_without_active_exchange()
    test_multiple_workspace_changes()
    print("All tests passed!")
