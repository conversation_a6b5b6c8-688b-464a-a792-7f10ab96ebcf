#!/usr/bin/env python3
"""Test script to verify if the /chat-history-proto {path} command creates a file."""

import os
import tempfile
from pathlib import Path
import pytest

from experimental.guy.agent_qa.test_cli_agent_e2e import run_agent_interactive


# This test fails because of grpc calls from AugmentPrototypingClient failing with auth errors
# TODO: reenable once auth errors are fixed
@pytest.mark.skip
def test_chat_history_proto_file_creation():
    """Test if the /chat-history-proto {path} command creates a file."""
    # Create a temporary directory for the workspace
    with tempfile.TemporaryDirectory() as temp_dir:
        workspace_dir = Path(temp_dir)

        # Create a file in the workspace
        test_file = workspace_dir / "test.txt"
        test_file.write_text("This is a test file")

        # Create a path for the chat history
        chat_history_path = workspace_dir / "chat_history.json"
        chat_history_path_str = str(chat_history_path)

        # Run the agent with the /chat-history-proto command
        commands = [
            "ls",
            f"/chat-history-proto {chat_history_path_str}",
            "/quit",
        ]

        # Run the agent
        print(f"Running agent with commands: {commands}")
        result = run_agent_interactive(
            workspace_dir,
            commands,
            extra_args=["--no-pager", "--no-integration-warnings"],
        )

        # Print the output for debugging
        print(f"Exit code: {result.returncode}")
        print(f"Stdout: {result.stdout}")
        print(f"Stderr: {result.stderr}")

        # Check if the file was created
        file_exists = chat_history_path.exists()
        print(f"File exists: {file_exists}")

        # If the file exists, print its contents
        if file_exists:
            print(f"File contents: {chat_history_path.read_text()}")

        return file_exists


if __name__ == "__main__":
    result = test_chat_history_proto_file_creation()
    print(f"Test result: {'PASSED' if result else 'FAILED'}")
    exit(0 if result else 1)
