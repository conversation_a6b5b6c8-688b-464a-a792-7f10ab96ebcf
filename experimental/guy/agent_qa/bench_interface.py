"""Bench interface for the interactive agent."""

import asyncio
import json
import logging
from dataclasses import dataclass
from typing import Any, Optional
from urllib.parse import urlencode

import aiohttp
import websockets

from experimental.guy.agent_qa.bench_cli.config import Config
from experimental.guy.agent_qa.agents import Agent
from experimental.guy.agent_qa.builtin_tools import (
    CodebaseKnowledgeTool,
)
from experimental.guy.agent_qa.process_tools_v2 import ProcessTools
from experimental.guy.agent_qa.workspace_manager import WorkspaceManagerImpl

logger = logging.getLogger(__name__)


@dataclass
class ConnectionMetadata:
    """Metadata for WebSocket connection."""

    version: str
    os: str
    working_dir: str
    client_type: str = "terminal"

    def to_json(self) -> str:
        """Convert to JSON string."""
        return json.dumps(
            {
                "version": self.version,
                "os": self.os,
                "working_dir": self.working_dir,
                "type": self.client_type,
            }
        )


@dataclass
class WsMessage:
    """WebSocket message format."""

    action: str
    channel_id: str
    client_type: str
    message: dict[str, Any]

    def to_json(self) -> str:
        """Convert to JSON string."""
        return json.dumps(
            {
                "action": self.action,
                "channelId": self.channel_id,
                "clientType": self.client_type,
                "message": self.message,
            }
        )


def generate_message_id(length: int = 8) -> str:
    """Generate a random message ID."""
    import random
    import string

    chars = string.ascii_letters + string.digits
    return "".join(random.choice(chars) for _ in range(length))


class BenchInterface:
    """Interface between the agent and bench_cli protocol."""

    def __init__(
        self,
        agent: Agent,
        workspace_manager: WorkspaceManagerImpl,
        knowledge_tool: CodebaseKnowledgeTool,
        process_tools: ProcessTools,
    ):
        """Initialize the bench interface."""
        self.agent = agent
        self.workspace_manager = workspace_manager
        self.knowledge_tool = knowledge_tool
        self.process_tools = process_tools

    async def get_ws_url(self, config: Config, token: str) -> str:
        """Get WebSocket URL from server."""
        async with aiohttp.ClientSession() as session:
            async with session.get(
                config.ws_url_endpoint(),
                headers={"Authorization": f"Bearer {token}"},
            ) as resp:
                resp.raise_for_status()
                data = await resp.json()
                return data["url"]

    async def handle_agent_instruction(
        self, command: str, token: str, correlation_id: str | None = None
    ) -> WsMessage:
        """Handle a command by sending it to the agent."""
        try:
            logger.info("Handling command: %s", command)

            # Run the agent with the command
            answer = self.agent.run_agent(command, resume=True)
            logger.info("Agent response: %s", answer)

            # Take snapshot at the end of each turn if workspace_manager is available
            if self.workspace_manager is not None:
                self.workspace_manager.snapshot_workspace()

            # Create response message
            response = WsMessage(
                action="message",
                channel_id=token,
                client_type="server",
                message={
                    "type": "response",
                    "id": generate_message_id(),
                    "data": {
                        "output": answer,
                        "error": None,
                    },
                    "correlationId": correlation_id,
                },
            )
            logger.info("Created response message with ID: %s", response.message["id"])
            return response
        except Exception as e:
            logger.error("Error handling command: %s", e, exc_info=True)
            return WsMessage(
                action="message",
                channel_id=token,
                client_type="server",
                message={
                    "type": "response",
                    "id": generate_message_id(),
                    "data": {
                        "output": "",
                        "error": str(e),
                    },
                    "correlationId": correlation_id,
                },
            )

    async def handle_ws_connection(self, ws_url: str, token: str) -> None:
        """Handle WebSocket connection."""
        # Create connection metadata
        metadata = ConnectionMetadata(
            version="0.1.0",
            os="Linux",  # Assuming Linux, update if needed
            working_dir=str(self.workspace_manager.root),
        )
        logger.info("Created connection metadata: %s", metadata.to_json())

        # Add metadata to URL
        params = {
            "clientType": "server",
            "channelId": token,
            "metadata": metadata.to_json(),
        }
        url = f"{ws_url}?{urlencode(params)}"
        logger.info("Connecting to WebSocket URL: %s", url)

        # Add ping_interval and ping_timeout to keep connection alive
        async with websockets.connect(
            url,
            ping_interval=20,  # Send ping every 20 seconds
            ping_timeout=20,  # Wait 20 seconds for pong response
        ) as websocket:
            logger.info("WebSocket connection established")

            try:
                async for message in websocket:
                    try:
                        data = json.loads(message)
                        logger.debug("Received message: %s", data)

                        if "message" in data and "data" in data["message"]:
                            msg_data = data["message"]["data"]
                            if "command" in msg_data:
                                command = msg_data["command"]
                                logger.info("Received command: %s", command)

                                response = await self.handle_agent_instruction(
                                    command,
                                    token,
                                    data["message"].get("id"),
                                )
                                logger.info("Sending response: %s", response.to_json())
                                await websocket.send(response.to_json())

                    except json.JSONDecodeError:
                        logger.error("Failed to parse message: %s", message)
                    except Exception as e:
                        logger.error("Error handling message: %s", e)

            except websockets.exceptions.ConnectionClosed as e:
                logger.info("WebSocket connection closed: %s", e)

    async def run(self, config: Config, token: str) -> None:
        """Run the bench interface."""
        while True:
            try:
                ws_url = await self.get_ws_url(config, token)
                logger.info("WebSocket URL obtained: %s", ws_url)

                await self.handle_ws_connection(ws_url, token)

            except aiohttp.ClientResponseError as e:
                logger.error("Failed to get WebSocket URL: %s", e)
                if e.status == 401:
                    auth_url = config.auth_url(token)
                    print(f"Please visit {auth_url} to authenticate your session")

            except Exception as e:
                logger.error("Error in WebSocket connection: %s", e)

            await asyncio.sleep(5)  # Wait before reconnecting
