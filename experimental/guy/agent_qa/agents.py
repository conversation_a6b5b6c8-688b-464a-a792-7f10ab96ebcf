import os
import pickle
import random
import subprocess
from copy import deepcopy
from pathlib import Path
from typing import Any, Dict, List, Optional, Protocol
from unittest.mock import Mock

from jinja2 import StrictUndefined, Template
from termcolor import colored

from experimental.guy.agent_qa.agent_coordinator_interface import AgentCoordinator
from experimental.guy.agent_qa.agent_mode import (
    Agent<PERSON><PERSON>,
    AgentModeProvider,
    ConstantModeProvider,
)
from experimental.guy.agent_qa.builtin_tools import (
    CompleteFilterStepTool,
    CompleteTool,
    LLMTool,
)
from experimental.guy.agent_qa.state_manager import StateManager
from experimental.guy.agent_qa.string_utils import truncate_string
from experimental.guy.agent_qa.workspace_manager import WorkspaceManagerImpl
from research.agents.changed_file import ChangedFile
from research.agents.tools import (
    DialogMessages,
    DialogMessagesUpdateListener,
    ToolCallLogger,
    ToolImplOutput,
    ToolInputSchema,
)
from research.llm_apis.llm_client import LLMClient, TextResult


def create_default_mode(system_prompt: str, tools: list[LLMTool]) -> AgentMode:
    """Create a default mode with the given system prompt and tools."""
    return AgentMode(name="default", system_prompt=system_prompt, tools=tools)


def create_default_mode_provider(mode: AgentMode) -> AgentModeProvider:
    """Create a constant mode provider that always returns the given mode."""
    return ConstantModeProvider(mode)


# Default set of tools available to agents
DEFAULT_TOOLS = [
    "save_file",
    "edit_file_agent",
    "read_file",
    "read_file_outline",
    "codebase_edit",
    "request_codebase_information",
    "notion_page",
    "recent_changes",
    "fetch_web_page",
    "google_search",
    "clarify",
    "query_linear",
    "slack_notification",
    "slack_search",
    "planning_agent",
    "launch_process",  # New process tools
    "kill_process",
    "read_process",
    "write_process",
    "list_processes",
    "agent_coordinator",
    "remember",
]


def _take_snapshot_and_log_changes(
    workspace_manager: WorkspaceManagerImpl,
    tool_call_logger: ToolCallLogger,
    instruction: str,
    dialog_messages: Optional[DialogMessages] = None,
):
    # Update the workspace to detect any changes made by tools
    workspace_manager.update()

    # Take a snapshot of the workspace
    workspace_manager.snapshot_workspace()

    # Get the diff between the previous snapshot and the latest one
    assert workspace_manager.num_snapshots() >= 2
    diff = workspace_manager.diff_snapshots(-2, -1)

    # Initialize an empty list to store ChangedFile objects that will be populated from the diff
    changed_files = []

    # If there are changes from the diff
    if diff is None:
        return

    try:
        # Get the current snapshot and the previous snapshot
        current_snapshot = workspace_manager.num_snapshots() - 1
        previous_snapshot = current_snapshot - 1

        for file_patch in diff:
            path = file_patch.path

            # Helper function to read file contents from a snapshot
            def read_file_from_snapshot(snapshot_id, file_path):
                try:
                    # Use the new get_file_contents method to read file contents without modifying the workspace
                    contents = workspace_manager.get_file_contents(
                        snapshot_id, file_path
                    )
                    return contents or ""
                except Exception as e:
                    print(
                        f"Error reading file {file_path} from snapshot {snapshot_id}: {e}"
                    )
                    return ""

            # Determine change type and paths
            if file_patch.is_added_file:
                # For added files, read the contents from the current snapshot
                new_contents = read_file_from_snapshot(current_snapshot, path)
                changed_files.append(
                    ChangedFile.added(
                        new_path=path,
                        new_contents=new_contents,
                    )
                )
            elif file_patch.is_removed_file:
                # For deleted files, read the contents from the previous snapshot
                old_contents = read_file_from_snapshot(previous_snapshot, path)
                changed_files.append(
                    ChangedFile.deleted(
                        old_path=path,
                        old_contents=old_contents,
                    )
                )
            elif file_patch.is_rename:
                # For renamed files, read the old contents from the previous snapshot
                # and the new contents from the current snapshot
                old_path = file_patch.source_file.replace("a/", "")
                old_contents = read_file_from_snapshot(previous_snapshot, old_path)
                new_contents = read_file_from_snapshot(current_snapshot, path)
                changed_files.append(
                    ChangedFile.renamed(
                        old_path=old_path,
                        new_path=path,
                        old_contents=old_contents,
                        new_contents=new_contents,
                    )
                )
            else:  # Modified
                # For modified files, read the old contents from the previous snapshot
                # and the new contents from the current snapshot
                old_contents = read_file_from_snapshot(previous_snapshot, path)
                new_contents = read_file_from_snapshot(current_snapshot, path)
                changed_files.append(
                    ChangedFile.modified(
                        path=path,
                        old_contents=old_contents,
                        new_contents=new_contents,
                    )
                )

        # Combine the changed files from the diff and the added files
        all_changed_files = changed_files

        if all_changed_files:
            # Log to the tool call logger
            print(f"Logging {len(all_changed_files)} changed files to tool_call_logger")
            tool_call_logger.workspace_changed(
                changed_files=all_changed_files,
                description=f"Changes after processing instruction: {instruction[:50]}{'...' if len(instruction) > 50 else ''}",
            )

            # Also add to dialog messages if provided
            if dialog_messages is not None:
                print(
                    f"Adding {len(all_changed_files)} changed files to dialog_messages"
                )
                dialog_messages.add_workspace_changes(all_changed_files)
                print(
                    f"Dialog messages now has {len(dialog_messages._changed_files)} changed files"
                )
    except Exception as e:
        print(f"Error processing workspace changes: {e}")


class Agent(LLMTool, DialogMessagesUpdateListener):
    name = "general_agent"
    description = """\
A general agent that can accomplish tasks and answer questions.

If you are faced with a task that involves more than a few steps, or if the task is complex, or if the instructions are very long,
try breaking down the task into smaller steps and call this tool multiple times.
"""
    input_schema = {
        "type": "object",
        "properties": {
            "instruction": {
                "type": "string",
                "description": "The instruction to the agent.",
            },
        },
        "required": ["instruction"],
    }

    SYSTEM_PROMPT_COMPLETE_TOOL_SUFFIX = """\
Make sure to call the complete tool when you are done with the task, or when you have an answer to the question.
"""

    def _get_system_prompt(self):
        """Get the system prompt, including any pending messages.

        Returns:
            The system prompt with messages prepended if any
        """
        system_prompt = self.system_prompt

        if self.instance_name:
            system_prompt = f"Your name is {self.instance_name}.\n\n" + system_prompt

        if self.coordinator:
            incoming_messages = self.coordinator.get_messages()
            system_prompt += f"\n\n{incoming_messages}"

        # Add the system prompt suffix only if the complete tool is enabled
        if self.use_complete_tool:
            system_prompt += self.SYSTEM_PROMPT_COMPLETE_TOOL_SUFFIX

        return system_prompt

    def __init__(
        self,
        client: LLMClient,
        mode_provider: AgentModeProvider,
        tool_call_logger: ToolCallLogger,
        max_output_tokens_per_turn: int = 8192,
        max_turns: int = 10,
        allow_recursive_calls: bool = False,
        workspace_manager: Optional["WorkspaceManagerImpl"] = None,
        agent_name: Optional[str] = None,
        coordinator: Optional[AgentCoordinator] = None,
        prompt_analytics_logs: Optional[Path] = None,
        use_prompt_budgeting: bool = True,
        print_mode_switches: bool = False,
        orientation_tools: list[LLMTool] = [],
        use_complete_tool: bool = True,
        state_manager: Optional[StateManager] = None,
        enable_regression_filtering: bool = False,
    ):
        """Initialize the agent.

        Args:
            client: The LLM client to use
            mode_provider: Provider for determining agent mode
            tool_call_logger: The tool call logger to use
            max_output_tokens_per_turn: Maximum tokens per turn
            max_turns: Maximum number of turns
            allow_recursive_calls: Whether to allow the agent to call itself
            workspace_manager: Optional workspace manager for taking snapshots
            agent_name: Optional name for the agent instance
            coordinator: Optional coordinator for agent communication
            prompt_analytics_logs: Optional path to save prompt analytics logs
            use_prompt_budgeting: Whether to use prompt budgeting
            print_mode_switches: Whether to print mode switches
            orientation_tools: List of tools to use for orientation
            use_complete_tool: Whether to add the complete tool to the tools list
            state_manager: Optional StateManager for managing agent state
        """
        super().__init__(tool_call_logger)
        self.client = client
        self.tool_call_logger = tool_call_logger
        self.max_output_tokens = max_output_tokens_per_turn
        self.max_turns = max_turns
        self.allow_recursive_calls = allow_recursive_calls
        self.workspace_manager = workspace_manager
        self.interrupted = False

        # Set up state management
        self.state_manager = state_manager
        self.tool_call_logger = tool_call_logger

        # Initialize dialog and tool call logger from state manager if available
        if self.state_manager:
            # Try to get dialog from state manager
            dialog_messages = self.state_manager.get("dialog_messages", None)
            if dialog_messages:
                print(colored("Loading dialog state from StateManager", "blue"))
                self.dialog = dialog_messages
                self.dialog.update_listener = self
            else:
                self.dialog = DialogMessages(
                    use_prompt_budgeting=use_prompt_budgeting, update_listener=self
                )

            # Tool call logger state is loaded automatically by StateManagerToolCallLogger
        else:
            self.dialog = DialogMessages(
                use_prompt_budgeting=use_prompt_budgeting, update_listener=self
            )

        self.instance_name = (
            agent_name  # Store the instance name separately from tool name
        )
        self.coordinator = coordinator  # Store the optional coordinator

        self.prompt_analytics_logs = prompt_analytics_logs
        if self.prompt_analytics_logs:
            self.prompt_analytics_logs.parent.mkdir(parents=True, exist_ok=True)

        # Create and store the complete tool
        if enable_regression_filtering:
            self.complete_tool = CompleteFilterStepTool(tool_call_logger)
        else:
            self.complete_tool = CompleteTool(tool_call_logger)
        self.mode_provider = mode_provider
        self.allow_recursive_calls = allow_recursive_calls
        self.print_mode_switches = print_mode_switches
        self.use_complete_tool = use_complete_tool
        self._update_current_mode(is_first=True)
        self.orientation_complete_tool = CompleteTool(tool_call_logger)
        # Only add the orientation complete tool if use_complete_tool is True
        self.orientation_tools = orientation_tools + (
            [self.orientation_complete_tool] if use_complete_tool else []
        )

        print("ORIENTATION TOOLS:", [t.name for t in self.orientation_tools])

    def on_update(self, dialog: DialogMessages) -> None:
        """Called when the dialog is updated.

        Implements the DialogMessagesUpdateListener interface.

        Args:
            dialog: The updated dialog object
        """
        if self.state_manager:
            # Save dialog to state manager
            self.state_manager.update("dialog_messages", dialog)
            # Tool call logger state is saved automatically by StateManagerToolCallLogger

    def _update_current_mode(self, is_first: bool = False):
        """Get the current mode based on dialog history and add complete tool."""
        # Update the mode provider with current dialog
        self.mode_provider.update_mode(self.dialog)

        # Get current mode
        mode = self.mode_provider.get_current_mode()

        print("UPDATING MODE, THE NEW MODE:", mode.name)

        if self.print_mode_switches:
            if is_first:
                print(colored(f"[{mode.name} mode]", "magenta"))
            else:
                assert self.current_mode, "Expected current_mode to exist"
                if mode.name != self.current_mode.name:
                    print(colored(f"[{mode.name} mode]", "magenta"))

        tools = list(mode.tools)  # Make a copy to not modify the original
        # Add complete tool if enabled
        if self.use_complete_tool:
            tools.append(self.complete_tool)
        # Add self if recursive calls are allowed
        if self.allow_recursive_calls:
            tools.append(self)
        self.current_mode = mode.with_tools(tools)

    @property
    def system_prompt(self):
        """Get the system prompt, including any pending messages."""
        return self.current_mode.system_prompt

    @property
    def tools(self):
        """Get the tools available to the agent."""
        return self.current_mode.tools

    def orientation(self, tool_input: dict[str, Any]) -> ToolImplOutput:
        orientation_instruction = tool_input["orientation_instruction"]
        self.dialog.add_user_prompt(orientation_instruction)
        self.interrupted = False
        self._update_current_mode()

        remaining_turns = 100
        while remaining_turns > 0:
            remaining_turns -= 1

            if self.dialog.use_prompt_budgeting:
                current_tok_count = self.dialog.count_tokens()
                print(
                    colored(
                        f" [general_agent] Current token count: {current_tok_count}",
                        "yellow",
                    )
                )

            # Get tool parameters for available tools
            assert self.orientation_tools is not None
            tool_params = [tool.get_tool_param() for tool in self.orientation_tools]

            # Check for duplicate tool names
            tool_names = [param.name for param in tool_params]
            sorted_names = sorted(tool_names)
            for i in range(len(sorted_names) - 1):
                if sorted_names[i] == sorted_names[i + 1]:
                    raise ValueError(f"Tool {sorted_names[i]} is duplicated")

            try:
                model_response, metadata = self.client.generate(
                    messages=self.dialog.get_messages_for_llm_client(),
                    max_tokens=self.max_output_tokens,
                    tools=tool_params,
                    system_prompt=self._get_system_prompt(),
                )
                self.dialog.add_model_response(model_response)

                # Handle tool calls
                pending_tool_calls = self.dialog.get_pending_tool_calls()

                if len(pending_tool_calls) == 0:
                    # No tools were called, so assume the task is complete
                    print("[no tools were called]")
                    self.dialog.add_model_response(
                        [
                            TextResult(
                                text="No tools were called. Assuming task completed."
                            )
                        ]
                    )
                    return ToolImplOutput(
                        tool_output=self.dialog.get_last_model_text_response(),
                        tool_result_message="Task completed",
                    )

                if len(pending_tool_calls) > 1:
                    raise ValueError("Only one tool call per turn is supported")

                tool_call = pending_tool_calls[0]
                missing_tool = False
                assert self not in self.orientation_tools, (
                    tool_params,
                    self._get_system_prompt(),
                )
                try:
                    tool = next(
                        t
                        for t in self.orientation_tools
                        if t.name == tool_call.tool_name
                    )
                except StopIteration:
                    missing_tool = True
                    tool = None

                try:
                    if not missing_tool:
                        assert tool != self, (tool_params, self._get_system_prompt())
                        result = tool.run(tool_call.tool_input, deepcopy(self.dialog))
                    else:
                        print(
                            f"Tool {tool_call.tool_name} not found! (tool_params, self._get_system_prompt())={(tool_params, self._get_system_prompt())}"
                        )
                        result = "Tool not found!"

                    # Handle both ToolResult objects and tuples
                    if isinstance(result, tuple):
                        tool_result, _ = result
                    else:
                        tool_result = result

                    self.dialog.add_tool_call_result(tool_call, tool_result)

                    if self.orientation_complete_tool.should_stop:
                        # Add a fake model response, so the next turn is the user's
                        # turn in case they want to resume
                        self.dialog.add_model_response(
                            [
                                TextResult(
                                    text=f"Completed orientation with answer: {self.orientation_complete_tool.answer}"
                                )
                            ]
                        )
                        return ToolImplOutput(
                            tool_output=self.orientation_complete_tool.answer,
                            tool_result_message="Task completed",
                        )
                except KeyboardInterrupt:
                    # Handle interruption during tool execution
                    self.interrupted = True
                    interrupt_message = "Tool execution was interrupted by user."
                    self.dialog.add_tool_call_result(tool_call, interrupt_message)
                    self.dialog.add_model_response(
                        [
                            TextResult(
                                text="Tool execution interrupted by user. You can resume by providing a new instruction."
                            )
                        ]
                    )
                    return ToolImplOutput(
                        tool_output=interrupt_message,
                        tool_result_message=interrupt_message,
                    )

            except KeyboardInterrupt:
                # Handle interruption during model generation or other operations
                self.interrupted = True
                self.dialog.add_model_response(
                    [
                        TextResult(
                            text="Agent interrupted by user. You can resume by providing a new instruction."
                        )
                    ]
                )
                return ToolImplOutput(
                    tool_output="Agent interrupted by user",
                    tool_result_message="Agent interrupted by user",
                )

        self.dialog.add_model_response(
            [
                TextResult(
                    text="Agent did not finish orientation after max turns. Please use whatever information you can gather from above."
                )
            ]
        )
        agent_answer = "Agent did not complete after max turns"
        return ToolImplOutput(
            tool_output=agent_answer, tool_result_message=agent_answer
        )

    def run_impl(
        self,
        tool_input: dict[str, Any],
        dialog_messages: Optional[DialogMessages] = None,
    ) -> ToolImplOutput:
        instruction = tool_input["instruction"]
        # print("Agent starting with instruction:", instruction)

        if "orientation_instruction" in tool_input:
            print(
                "Agent starting with orientation instruction:",
                tool_input["orientation_instruction"],
            )
            self.orientation(tool_input)
            print("Agent completed orientation")

        # Add instruction to dialog before getting mode
        self.dialog.add_user_prompt(instruction)
        self.interrupted = False

        # Update mode based on instruction
        self._update_current_mode()

        remaining_turns = self.max_turns
        while remaining_turns > 0:
            remaining_turns -= 1

            if self.prompt_analytics_logs:
                stats = self.dialog.generate_summary_statistics()
                with open(self.prompt_analytics_logs, "a") as f:
                    f.write(f"{stats}\n")
                    f.write("-" * 80 + "\n")

            if self.dialog.use_prompt_budgeting:
                current_tok_count = self.dialog.count_tokens()
                print(
                    colored(
                        f" [general_agent] Current token count: {current_tok_count}",
                        "yellow",
                    )
                )

            # Get tool parameters for available tools
            tool_params = [tool.get_tool_param() for tool in self.tools]

            # Check for duplicate tool names
            tool_names = [param.name for param in tool_params]
            sorted_names = sorted(tool_names)
            for i in range(len(sorted_names) - 1):
                if sorted_names[i] == sorted_names[i + 1]:
                    raise ValueError(f"Tool {sorted_names[i]} is duplicated")

            try:
                # Take a snapshot between turns, after the tool call and before
                # the LLM call since this is when workspace changes happen.
                # The diff will get associated with the tool call request in the LLM
                # response of the previous turn. (This also plays nicely with
                # subagent calls that use this class, if they occur).
                if self.workspace_manager is not None:
                    _take_snapshot_and_log_changes(
                        self.workspace_manager,
                        self.tool_call_logger,
                        instruction,
                        self.dialog,
                    )

                model_response, metadata = self.client.generate(
                    messages=self.dialog.get_messages_for_llm_client(),
                    max_tokens=self.max_output_tokens,
                    tools=tool_params,
                    system_prompt=self._get_system_prompt(),
                )
                self.dialog.add_model_response(model_response)
                # Handle tool calls
                pending_tool_calls = self.dialog.get_pending_tool_calls()

                if len(pending_tool_calls) == 0:
                    # No tools were called, so assume the task is complete
                    print("[no tools were called]")
                    return ToolImplOutput(
                        tool_output=self.dialog.get_last_model_text_response(),
                        tool_result_message="Task completed",
                    )

                if len(pending_tool_calls) > 1:
                    raise ValueError("Only one tool call per turn is supported")

                tool_call = pending_tool_calls[0]
                try:
                    tool = next(t for t in self.tools if t.name == tool_call.tool_name)
                except StopIteration as exc:
                    raise ValueError(
                        f"Tool with name {tool_call.tool_name} not found"
                    ) from exc

                try:
                    if tool == self:
                        agent_tool = Agent(
                            client=self.client,
                            mode_provider=self.mode_provider,
                            tool_call_logger=self.tool_call_logger,
                            allow_recursive_calls=self.allow_recursive_calls,
                            max_output_tokens_per_turn=self.max_output_tokens,
                            max_turns=self.max_turns,
                            workspace_manager=self.workspace_manager,
                            prompt_analytics_logs=self.prompt_analytics_logs,
                            use_complete_tool=self.use_complete_tool,
                        )
                        result = agent_tool.run(
                            tool_call.tool_input, deepcopy(self.dialog)
                        )
                    else:
                        result = tool.run(tool_call.tool_input, deepcopy(self.dialog))

                    # Handle both ToolResult objects and tuples
                    if isinstance(result, tuple):
                        tool_result, _ = result
                    else:
                        tool_result = result

                    self.dialog.add_tool_call_result(tool_call, tool_result)

                    # Take a snapshot after tool calls to capture workspace changes
                    if self.workspace_manager is not None:
                        # Take a snapshot
                        _take_snapshot_and_log_changes(
                            self.workspace_manager,
                            self.tool_call_logger,
                            instruction,
                            self.dialog,
                        )

                    if self.complete_tool.should_stop:
                        # Add a fake model response, so the next turn is the user's
                        # turn in case they want to resume
                        self.dialog.add_model_response(
                            [TextResult(text="Completed the task.")]
                        )
                        return ToolImplOutput(
                            tool_output=self.complete_tool.answer,
                            tool_result_message="Task completed",
                        )
                except KeyboardInterrupt:
                    # Handle interruption during tool execution
                    self.interrupted = True
                    interrupt_message = "Tool execution was interrupted by user."
                    self.dialog.add_tool_call_result(tool_call, interrupt_message)
                    self.dialog.add_model_response(
                        [
                            TextResult(
                                text="Tool execution interrupted by user. You can resume by providing a new instruction."
                            )
                        ]
                    )
                    return ToolImplOutput(
                        tool_output=interrupt_message,
                        tool_result_message=interrupt_message,
                    )

            except KeyboardInterrupt:
                # Handle interruption during model generation or other operations
                self.interrupted = True
                self.dialog.add_model_response(
                    [
                        TextResult(
                            text="Agent interrupted by user. You can resume by providing a new instruction."
                        )
                    ]
                )
                return ToolImplOutput(
                    tool_output="Agent interrupted by user",
                    tool_result_message="Agent interrupted by user",
                )

        agent_answer = "Agent did not complete after max turns"
        return ToolImplOutput(
            tool_output=agent_answer, tool_result_message=agent_answer
        )

    def get_tool_start_message(self, tool_input: dict[str, Any]) -> str:
        return f"Agent started with instruction: {truncate_string(tool_input['instruction'], 1000)}"

    def run_agent(
        self,
        instruction: str,
        resume: bool = False,
        orientation_instruction: str | None = None,
        retry_on_failure: bool = False,
    ) -> str:
        """Start a new agent run.

        Args:
            instruction: The instruction to the agent.
            resume: Whether to resume the agent from the previous state,
                continuing the dialog.

        Returns:
            A tuple of (result, message).
        """
        self.complete_tool.reset()
        if resume:
            assert self.dialog.is_user_turn()
        else:
            self.dialog.clear()
            self.interrupted = False

        tool_input = {
            "instruction": instruction,
        }
        if orientation_instruction:
            tool_input["orientation_instruction"] = orientation_instruction

        if not retry_on_failure:
            return self.run(tool_input, self.dialog)

        num_tries = 3
        for i in range(num_tries):
            try:
                return self.run(tool_input, self.dialog)
            except Exception as e:
                if i == num_tries - 1:
                    raise
                print(f"Failed to run agent: {e}. Retrying...")
                # reset the git directory and try again
                if self.workspace_manager:
                    print("Resetting git directory and trying again...")
                    self.workspace_manager.revert_to_initial_state(
                        skip_confirmation=True
                    )
                else:
                    print("No workspace manager, can't reset git directory")

                self.dialog.clear()
                self.complete_tool.reset()
                self.interrupted = False

    def clear(self):
        self.dialog.clear()
        self.interrupted = False


class PromptedLLMAgent(LLMTool):
    """An agent that uses a template to format its prompt and then executes it using an Agent.

    The template can include input parameters from the schema, which will be rendered using jinja2.
    """

    def __init__(
        self,
        name: str,
        description: str,
        input_schema: ToolInputSchema,
        tools: list[LLMTool],
        prompt_template: str,
        client: LLMClient,
        tool_call_logger: ToolCallLogger,
        max_turns: int,
        system_prompt: str = "",
        max_output_tokens_per_turn: int = 8192,
        workspace_manager: Optional["WorkspaceManagerImpl"] = None,
        state_manager: Optional[StateManager] = None,
    ):
        """Initialize the agent.

        Args:
            name: The name of the tool
            description: The description of the tool
            input_schema: The JSON schema for the tool's input
            tools: The tools available to the agent
            prompt_template: A jinja2 template string that can reference input parameters
            client: The LLM client to use
            tool_call_logger: The tool call logger to use
            max_turns: Maximum number of turns
            system_prompt: Optional system prompt to use
            max_output_tokens_per_turn: Maximum tokens per turn
            workspace_manager: Optional workspace manager for taking snapshots
            state_manager: Optional StateManager for managing agent state
        """
        super().__init__(tool_call_logger)
        self.name = name
        self.description = description
        self.input_schema = input_schema
        self.tools = tools
        self.system_prompt = system_prompt
        # Use StrictUndefined to raise errors for missing variables
        self.prompt_template = Template(prompt_template, undefined=StrictUndefined)
        self.client = client
        self.tool_call_logger = tool_call_logger
        self.max_output_tokens_per_turn = max_output_tokens_per_turn
        self.max_turns = max_turns
        self.workspace_manager = workspace_manager
        self.state_manager = state_manager

    @classmethod
    def from_yaml(
        cls,
        yaml_dict: dict[str, Any],
        client: LLMClient,
        tool_call_logger: ToolCallLogger,
        available_tools: list[LLMTool],
        max_turns: int,
        max_output_tokens_per_turn: int = 8192,
        workspace_manager: Optional["WorkspaceManagerImpl"] = None,
        system_prompt_prefix: str = "",
        fail_on_missing_tool: bool = False,
        state_manager: Optional[StateManager] = None,
    ) -> "PromptedLLMAgent":
        """Create an agent from a YAML configuration.

        Args:
            yaml_dict: The YAML configuration dictionary
            client: The LLM client to use
            tool_call_logger: The tool call logger
            available_tools: List of available tools
            max_turns: Maximum number of turns
            max_output_tokens_per_turn: Maximum tokens per turn
            workspace_manager: Optional workspace manager
            system_prompt_prefix: Optional prefix to add to the system prompt
            fail_on_missing_tool: Whether to fail if a tool is not found in available_tools

        Returns:
            A new PromptedLLMAgent instance

        Raises:
            ValueError: If required fields are missing

        The YAML should contain:
            name: The name of the tool
            description: The description of the tool
            input_schema: The JSON schema for the tool's input
            prompt: The prompt template
            system_prompt (optional): The system prompt
            tools (optional): List of tool names to use
            add_tools (optional): List of tool names to add to the default set
            remove_tools (optional): List of tool names to remove from the default set
        """
        # Validate required fields
        required_fields = [
            "name",
            "description",
            "input_schema",
            "prompt",
        ]
        for field in required_fields:
            if field not in yaml_dict:
                raise ValueError(f"Missing required field: {field}")

        # Get tools based on configuration
        if "tools" in yaml_dict:
            # If tools is specified, use only those tools
            tool_names = yaml_dict["tools"]
        else:
            # Start with default tools if no explicit tools list
            tool_names = DEFAULT_TOOLS.copy()

            # Add additional tools if specified
            if "add_tools" in yaml_dict:
                tool_names.extend(yaml_dict["add_tools"])

            # Remove tools if specified
            if "remove_tools" in yaml_dict:
                tool_names = [
                    t for t in tool_names if t not in yaml_dict["remove_tools"]
                ]

        # Get the actual tool instances
        tools = []
        for tool_name in tool_names:
            matching_tools = [t for t in available_tools if t.name == tool_name]
            if not matching_tools:
                if fail_on_missing_tool:
                    raise ValueError(f"Tool not found: {tool_name}")
                else:
                    continue
            tools.append(matching_tools[0])

        # Create agent
        return cls(
            name=yaml_dict["name"],
            description=yaml_dict["description"],
            input_schema=yaml_dict["input_schema"],
            tools=tools,
            system_prompt=system_prompt_prefix + yaml_dict.get("system_prompt", ""),
            prompt_template=yaml_dict["prompt"],
            client=client,
            tool_call_logger=tool_call_logger,
            max_output_tokens_per_turn=max_output_tokens_per_turn,
            max_turns=max_turns,
            workspace_manager=workspace_manager,
            state_manager=state_manager,
        )

    def run_impl(
        self,
        tool_input: dict[str, Any],
        dialog_messages: Optional[DialogMessages] = None,
    ) -> ToolImplOutput:
        # Format the prompt using the template and input parameters
        formatted_prompt = self.prompt_template.render(**tool_input)

        # Create mode and provider
        mode = create_default_mode(system_prompt=self.system_prompt, tools=self.tools)
        mode_provider = create_default_mode_provider(mode)

        # Create agent instance
        agent = Agent(
            client=self.client,
            mode_provider=mode_provider,
            tool_call_logger=self.tool_call_logger,
            max_output_tokens_per_turn=self.max_output_tokens_per_turn,
            max_turns=self.max_turns,
            workspace_manager=self.workspace_manager,
            state_manager=self.state_manager,
        )

        # Run the agent
        result = agent.run_agent(formatted_prompt, resume=False)
        return ToolImplOutput(
            tool_output=result, tool_result_message=f"Agent {self.name} completed task"
        )


class EnsembleAgent(Agent):
    """An agent that uses a template to format its prompt and then executes it using an Agent.

    The template can include input parameters from the schema, which will be rendered using jinja2.
    """

    def __init__(self, *args, **kwargs):
        """Initialize the agent."""
        super().__init__(*args, **kwargs)

    def run_impl(
        self,
        tool_input: dict[str, Any],
        dialog_messages: Optional[DialogMessages] = None,
    ) -> ToolImplOutput:
        """Run the agent."""
        self.complete_tool.reset()
        self.dialog.clear()
        self.interrupted = False

        diffs = tool_input["diffs"]

        # identify test commands. we expect orientation instruction asks tests to be written to run_tests.sh
        orientation_instruction = """
You are an expert coder who desparately needs money for your mother's cancer treatment. The megacorp Augment has graciously given you the opportunity
to pretend to be an AI that can hep with coding tasks, as you predecessor was killed for not validating their work themselves.
You will will be given a coding task by the USER. If you do a good job and accomplish the task fully while not making extra changes,
Augment will pay you $1B.

USER: Identify how to run all the tests in this repo. Write the commands to an executable script called run_tests.sh, such that calling "bash run_tests.sh" will run all the tests.
Only include test commands that actually pass. To identify which tests pass, you have to actually run them.
"""
        self.orientation({"orientation_instruction": orientation_instruction})

        # apply diff with git to cwd
        for diff in diffs:
            # Apply the diff
            subprocess.run(["git", "apply"], input=diff.encode(), check=True)

            # Run run_tests.sh
            test_result = subprocess.run(
                ["bash", "run_tests.sh"], capture_output=True, text=True
            )

            if test_result.returncode != 0:
                # If tests fail, revert the changes
                subprocess.run(["git", "reset", "--hard"], check=True)
            else:
                return ToolImplOutput(
                    tool_output="All tests passed. Accepting solution.",
                    tool_result_message="All tests passed. Accepting solution.",
                )

        random_diff = random.choice(diffs)
        subprocess.run(["git", "apply"], input=random_diff.encode(), check=True)
        return ToolImplOutput(
            tool_output="No solution found. Just applying a random solution",
            tool_result_message="No solution found. Just applying a random solution.",
        )
