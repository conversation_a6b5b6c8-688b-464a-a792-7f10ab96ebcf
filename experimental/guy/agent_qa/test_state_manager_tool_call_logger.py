"""Test the integration of ToolCallLogger with StateManager."""

import tempfile
import unittest
from pathlib import Path

from research.agents.tools import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, LoggedTool<PERSON>all, Tool<PERSON>aram
from experimental.guy.agent_qa.state_manager import StateManager
from experimental.guy.agent_qa.state_manager_tool_call_logger import (
    StateManagerToolCallLoggerListener,
    save_tool_call_logger_state,
    load_tool_call_logger_state,
)


class TestStateManagerToolCallLogger(unittest.TestCase):
    """Test the integration of ToolCallLogger with StateManager."""

    def setUp(self):
        """Set up a temporary directory for test files."""
        self.temp_dir = tempfile.TemporaryDirectory()
        self.state_file = Path(self.temp_dir.name) / "test_state.pkl"

    def tearDown(self):
        """Clean up temporary directory."""
        self.temp_dir.cleanup()

    def test_save_and_load_functions(self):
        """Test the save_tool_call_logger_state and load_tool_call_logger_state functions."""
        # Create a StateManager
        state_manager = StateManager(self.state_file)

        # Create a ToolCallLogger with some logged calls
        tool_call_logger = ToolCallLogger()

        # Create a tool parameter
        tool_param = ToolParam(
            name="test_tool",
            description="A test tool",
            input_schema={"type": "object", "properties": {}},
        )

        # Create a logged tool call
        tool_call = LoggedToolCall(
            tool=tool_param,
            tool_input={"arg1": "value1"},
            started=False,
            tool_output="test result",
            tool_message="Test tool call",
        )
        tool_call_logger.logged_calls.append(tool_call)

        # Save the ToolCallLogger state
        save_tool_call_logger_state(tool_call_logger, state_manager)

        # Create a new ToolCallLogger
        new_tool_call_logger = ToolCallLogger()

        # Load the ToolCallLogger state
        success = load_tool_call_logger_state(new_tool_call_logger, state_manager)
        self.assertTrue(success)

        # Verify that the logged calls were loaded correctly
        self.assertEqual(len(new_tool_call_logger.logged_calls), 1)
        loaded_call = new_tool_call_logger.logged_calls[0]
        self.assertEqual(loaded_call.tool.name, "test_tool")
        self.assertEqual(loaded_call.tool_input, {"arg1": "value1"})
        self.assertEqual(loaded_call.tool_output, "test result")
        self.assertFalse(loaded_call.started)

    def test_state_manager_tool_call_logger_listener(self):
        """Test the StateManagerToolCallLoggerListener class."""
        # Create a StateManager
        state_manager = StateManager(self.state_file)

        # Create a ToolCallLogger
        tool_call_logger = ToolCallLogger()

        # Create a StateManagerToolCallLoggerListener
        listener = StateManagerToolCallLoggerListener(state_manager)

        # Add the listener to the ToolCallLogger
        tool_call_logger.add_update_listener(listener)

        # We don't need to create a tool parameter here since we're using a mock object

        # Log a tool call
        tool_call_logger.tool_call_started(
            called_tool=type(
                "Tool",
                (),
                {"name": "test_tool", "description": "A test tool", "input_schema": {}},
            ),
            tool_input={"arg1": "value1"},
            tool_started_message="Starting test tool",
        )

        # Create a new ToolCallLogger
        new_tool_call_logger = ToolCallLogger()

        # Load the ToolCallLogger state
        success = load_tool_call_logger_state(new_tool_call_logger, state_manager)
        self.assertTrue(success)

        # Verify that the logged calls were loaded correctly
        self.assertEqual(len(new_tool_call_logger.logged_calls), 1)
        loaded_call = new_tool_call_logger.logged_calls[0]
        self.assertEqual(loaded_call.tool.name, "test_tool")
        self.assertEqual(loaded_call.tool_input, {"arg1": "value1"})
        self.assertTrue(loaded_call.started)

    def test_remove_listener(self):
        """Test removing a listener from a ToolCallLogger."""
        # Create a StateManager
        state_manager = StateManager(self.state_file)

        # Create a ToolCallLogger
        tool_call_logger = ToolCallLogger()

        # Create a StateManagerToolCallLoggerListener
        listener = StateManagerToolCallLoggerListener(state_manager)

        # Add the listener to the ToolCallLogger
        tool_call_logger.add_update_listener(listener)

        # Remove the listener
        tool_call_logger.remove_update_listener(listener)

        # Log a tool call
        tool_call_logger.tool_call_started(
            called_tool=type(
                "Tool",
                (),
                {"name": "test_tool", "description": "A test tool", "input_schema": {}},
            ),
            tool_input={"arg1": "value1"},
            tool_started_message="Starting test tool",
        )

        # Create a new ToolCallLogger
        new_tool_call_logger = ToolCallLogger()

        # Try to load the ToolCallLogger state
        success = load_tool_call_logger_state(new_tool_call_logger, state_manager)
        self.assertFalse(success)

        # Verify that no logged calls were loaded
        self.assertEqual(len(new_tool_call_logger.logged_calls), 0)


if __name__ == "__main__":
    unittest.main()
