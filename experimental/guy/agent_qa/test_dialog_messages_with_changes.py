"""Tests for the DialogMessages class with workspace changes support."""

import unittest
import tempfile
from pathlib import Path

from research.agents.changed_file import ChangedFile
from research.agents.tools import DialogMessages, ToolCallParameters
from research.llm_apis.llm_client import TextResult


class TestDialogMessagesWithChanges(unittest.TestCase):
    """Tests for the DialogMessages class with workspace changes support."""

    def test_add_workspace_changes(self):
        """Test adding workspace changes to a dialog."""
        # Create a DialogMessages object
        dialog = DialogMessages()

        # Add a user message
        dialog.add_user_prompt("Hello")

        # Add an assistant response
        dialog.add_model_response([TextResult("Hi there!")])

        # Add workspace changes
        changed_file = ChangedFile.added(
            new_path="hello.txt", new_contents="Hello, world!"
        )
        dialog.add_workspace_changes([changed_file])

        # Verify that the workspace changes were added
        changes = dialog.get_workspace_changes(1)
        self.assertEqual(len(changes), 1)
        self.assertEqual(changes[0].new_path, "hello.txt")
        self.assertEqual(changes[0].new_contents, "Hello, world!")
        self.assertEqual(changes[0].change_type, "ADDED")

    def test_add_tool_call_result_with_changes(self):
        """Test adding a tool call result with workspace changes."""
        # Create a DialogMessages object
        dialog = DialogMessages()

        # Add a user message
        dialog.add_user_prompt("Can you create a hello world program?")

        # Add an assistant response
        dialog.add_model_response(
            [TextResult("Sure, I'll create a hello world program for you.")]
        )

        # Create a tool call parameter
        tool_param = ToolCallParameters(
            tool_call_id="123",
            tool_name="shell",
            tool_input={"command": "echo 'Hello, world!' > hello.txt"},
        )

        # Create a changed file
        changed_file = ChangedFile.added(
            new_path="hello.txt", new_contents="Hello, world!"
        )

        # Add a tool call result with workspace changes
        dialog.add_tool_call_result(
            parameters=tool_param,
            result="Created hello.txt",
            changed_files=[changed_file],
        )

        # Verify that the workspace changes were added
        changes = dialog.get_workspace_changes(2)
        self.assertEqual(len(changes), 1)
        self.assertEqual(changes[0].new_path, "hello.txt")
        self.assertEqual(changes[0].new_contents, "Hello, world!")
        self.assertEqual(changes[0].change_type, "ADDED")

    def test_save_and_load_with_changes(self):
        """Test saving and loading a dialog with workspace changes."""
        # Create a temporary file
        with tempfile.NamedTemporaryFile(suffix=".pkl") as temp_file:
            temp_path = Path(temp_file.name)

            # Create a DialogMessages object
            dialog = DialogMessages()

            # Add a user message
            dialog.add_user_prompt("Hello")

            # Add an assistant response
            dialog.add_model_response([TextResult("Hi there!")])

            # Add workspace changes
            changed_file = ChangedFile.added(
                new_path="hello.txt", new_contents="Hello, world!"
            )
            dialog.add_workspace_changes([changed_file])

            # Save the dialog
            dialog.save_messages(temp_path)

            # Load the dialog
            loaded_dialog = DialogMessages.from_saved_messages(temp_path)

            # Verify that the workspace changes were loaded
            changes = loaded_dialog.get_workspace_changes(1)
            self.assertEqual(len(changes), 1)
            self.assertEqual(changes[0].new_path, "hello.txt")
            self.assertEqual(changes[0].new_contents, "Hello, world!")
            self.assertEqual(changes[0].change_type, "ADDED")


if __name__ == "__main__":
    unittest.main()
