#!/bin/bash

set -e

if [ $# -lt 1 ]; then
    echo "Usage: $0 <ticket-id> [additional instruction]"
    echo "Example: $0 AU-1234 'Focus on improving test coverage'"
    exit 1
fi

TICKET_ID="$1"
shift
INSTRUCTION="${*:-}"  # Join remaining args with spaces, or empty if none

# Get the absolute path to the repo root
SCRIPT_DIR="$(dirname "$(readlink -f "$0")")"
REPO_ROOT="$(readlink -e "$SCRIPT_DIR"/../../..)"

# Build the agent input JSON
if [ -n "$INSTRUCTION" ]; then
    AGENT_INPUT="{\"ticket_id\": \"$TICKET_ID\", \"instruction\": \"$INSTRUCTION\"}"
else
    AGENT_INPUT="{\"ticket_id\": \"$TICKET_ID\"}"
fi

# Run the interactive agent with the fix_ticket agent
exec "$SCRIPT_DIR"/interactive_agent.sh \
    --agent fix_ticket \
    --agent-input "$AGENT_INPUT" \
    --approve-command-execution
