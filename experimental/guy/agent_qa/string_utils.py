"""String manipulation utilities."""


def truncate_string(text: str, char_budget: int) -> str:
    """Truncate a string by removing lines from the middle.

    Args:
        text: The text to truncate
        char_budget: Approximate number of characters to keep

    Returns:
        A string with roughly char_budget characters, with middle lines removed.
        The string will contain a prefix of complete lines, then a marker line
        indicating truncation, then a suffix of complete lines.
    """
    if len(text) <= char_budget:
        return text

    lines = text.splitlines(keepends=True)
    if not lines:
        return text

    # If we have just one line, return it
    if len(lines) == 1:
        return text

    truncation_marker = "... additional lines truncated ...\n"
    marker_size = len(truncation_marker)

    # Always keep first and last line
    first_line = lines[0]
    last_line = lines[-1]
    first_size = len(first_line)
    last_size = len(last_line)

    # If budget is very small, just return first and last line with marker
    if first_size + marker_size + last_size > char_budget:
        return first_line + truncation_marker + last_line

    # Calculate remaining budget for middle sections
    remaining_budget = char_budget - (first_size + marker_size + last_size)

    # Split remaining budget between prefix and suffix
    prefix_budget = remaining_budget // 2
    suffix_budget = remaining_budget - prefix_budget

    # Add lines to prefix until we exceed budget
    prefix_lines = [first_line]
    prefix_size = first_size
    for line in lines[1:-1]:
        if prefix_size + len(line) > prefix_budget:
            break
        prefix_lines.append(line)
        prefix_size += len(line)

    # Add lines to suffix until we exceed budget, going backwards
    suffix_lines = [last_line]
    suffix_size = last_size
    for line in reversed(lines[1:-1]):
        if suffix_size + len(line) > suffix_budget:
            break
        suffix_lines.insert(0, line)
        suffix_size += len(line)

    # Combine the parts
    return "".join(prefix_lines) + truncation_marker + "".join(suffix_lines)
