"""Tests for web search tool."""

from unittest.mock import Mock, patch
import pytest
import os
import json
import requests
from pathlib import Path

from experimental.guy.agent_qa.tools.web_search_tool import WebSearchTool
from experimental.guy.agent_qa.tools.web_search import SearchResult
from research.agents.tools import ToolCallLogger


@pytest.mark.integration
class TestWebSearchTool:
    """Tests for WebSearchTool."""

    @pytest.fixture
    def tool(self):
        """Create a WebSearchTool instance."""
        return WebSearchTool(ToolCallLogger())

    @pytest.fixture
    def mock_config(self, tmp_path):
        """Create a mock config file."""
        config = {
            "api_key": "test_key",  # pragma: allowlist secret
            "search_engine_id": "test_cx",
        }
        config_dir = tmp_path / ".augment" / "agent"
        config_dir.mkdir(parents=True)
        config_path = config_dir / "google_search_api_settings.json"
        config_path.write_text(json.dumps(config))
        os.environ["AUGMENT_AGENT_CONFIG_DIR"] = str(tmp_path / ".augment" / "agent")
        return str(tmp_path)

    def test_search_success(self, tool, mock_config):
        """Test successful search with mock results."""
        mock_response = Mock()
        mock_response.json.return_value = {
            "items": [
                {
                    "title": "Test Title",
                    "link": "http://example.com",
                    "snippet": "Test description",
                }
            ]
        }

        with patch(
            "experimental.guy.agent_qa.tools.web_search.requests.get",
            return_value=mock_response,
        ):
            result = tool.run_impl({"query": "test query", "num_results": 1})
            assert "Test Title" in result.tool_output
            assert "http://example.com" in result.tool_output
            assert "Test description" in result.tool_output
            assert "Found 1 results" in result.tool_result_message

    def test_search_no_results(self, tool, mock_config):
        """Test search with no results."""
        mock_response = Mock()
        mock_response.json.return_value = {}

        with patch(
            "experimental.guy.agent_qa.tools.web_search.requests.get",
            return_value=mock_response,
        ):
            result = tool.run_impl({"query": "test query"})
            assert "No results found" in result.tool_output
            assert "Found 0 results" in result.tool_result_message

    def test_search_error(self, tool, mock_config):
        """Test search error handling."""
        with patch(
            "experimental.guy.agent_qa.tools.web_search.requests.get",
            side_effect=requests.RequestException("API Error"),
        ):
            result = tool.run_impl({"query": "test query"})
            assert "No results found" in result.tool_output
            assert "Search failed" in result.tool_result_message

    def test_default_num_results(self, tool, mock_config):
        """Test default number of results."""
        mock_response = Mock()
        mock_response.json.return_value = {
            "items": [
                {
                    "title": f"Title {i}",
                    "link": f"http://example.com/{i}",
                    "snippet": f"Description {i}",
                }
                for i in range(5)
            ]
        }

        with patch(
            "experimental.guy.agent_qa.tools.web_search.requests.get",
            return_value=mock_response,
        ):
            result = tool.run_impl({"query": "test query"})
            assert "Found 5 results" in result.tool_result_message
            for i in range(5):
                assert f"Title {i}" in result.tool_output
                assert f"http://example.com/{i}" in result.tool_output
                assert f"Description {i}" in result.tool_output
