"""Tests for workspace snapshot functionality."""

import os
from pathlib import Path
import tempfile
import unittest
from unittest import mock

from experimental.guy.agent_qa.workspace_manager import WorkspaceManagerImpl


class WorkspaceSnapshotsTest(unittest.TestCase):
    def setUp(self):
        self.temp_dir = tempfile.mkdtemp()
        self.root = Path(self.temp_dir)
        self.mock_client = mock.MagicMock()

        # Create initial files first
        (self.root / "file1.txt").write_text("Initial content 1")
        (self.root / "file2.txt").write_text("Initial content 2")

        # Then initialize manager
        self.manager = WorkspaceManagerImpl(
            augment_client=self.mock_client,
            root=self.root,
        )
        self.manager.update()

        # Take initial snapshot
        self.manager.snapshot_workspace()  # This will be snapshot 0

    def tearDown(self):
        # Clean up temp directory
        for root, dirs, files in os.walk(self.temp_dir, topdown=False):
            for name in files:
                os.remove(os.path.join(root, name))
            for name in dirs:
                os.rmdir(os.path.join(root, name))
        os.rmdir(self.temp_dir)

    def test_initial_snapshot(self):
        """Test that initial state (sequence 0) is saved correctly."""
        # Debug information
        print("\nDebug info:")
        print(f"Absolute paths: {list(self.manager._absolute_paths)}")
        print(f"Snapshots: {self.manager._snapshots}")

        initial_state = self.manager._snapshots[0]
        self.assertEqual(len(initial_state), 2)
        self.assertEqual(initial_state["file1.txt"], "Initial content 1")
        self.assertEqual(initial_state["file2.txt"], "Initial content 2")

    def test_snapshot_sequence(self):
        """Test that snapshots are numbered correctly."""
        # Take first snapshot after initial state
        seq1 = self.manager.snapshot_workspace()
        self.assertEqual(seq1, 1)  # First snapshot after initial state is 1

        # Take second snapshot
        seq2 = self.manager.snapshot_workspace()
        self.assertEqual(seq2, 2)

    def test_snapshot_content(self):
        """Test that snapshots capture the correct state."""
        # Take initial snapshot
        seq1 = self.manager.snapshot_workspace()

        # Modify files and take another snapshot
        (self.root / "file1.txt").write_text("Modified content 1")
        (self.root / "file3.txt").write_text("New file content")
        self.manager.update()
        seq2 = self.manager.snapshot_workspace()

        # Verify first snapshot has original content
        snapshot1 = self.manager._snapshots[seq1]
        self.assertEqual(snapshot1["file1.txt"], "Initial content 1")
        self.assertEqual(snapshot1["file2.txt"], "Initial content 2")
        self.assertNotIn("file3.txt", snapshot1)

        # Verify second snapshot has modified content
        snapshot2 = self.manager._snapshots[seq2]
        self.assertEqual(snapshot2["file1.txt"], "Modified content 1")
        self.assertEqual(snapshot2["file2.txt"], "Initial content 2")
        self.assertEqual(snapshot2["file3.txt"], "New file content")

    def test_revert_updates_internal_state(self):
        """Test that reverting updates _absolute_paths and _blob_names."""
        # Initial state has 2 files
        initial_paths = sorted(p.name for p in self.manager._absolute_paths)
        initial_blob_names = sorted(self.manager._blob_names)

        # Add a new file and modify existing
        (self.root / "file3.txt").write_text("New content")
        (self.root / "file1.txt").write_text("Modified content")

        # Ensure files are synced to disk
        import time

        time.sleep(0.1)  # Allow file operations to complete

        self.manager.update()

        # Verify state changed
        modified_paths = sorted(p.name for p in self.manager._absolute_paths)
        modified_blob_names = sorted(self.manager._blob_names)

        # Debug output in case of failure
        print("\nDebug info:")
        print(f"Initial paths: {initial_paths}")
        print(f"Modified paths: {modified_paths}")
        print(f"Initial blob names: {initial_blob_names}")
        print(f"Modified blob names: {modified_blob_names}")

        self.assertNotEqual(initial_paths, modified_paths)
        self.assertNotEqual(initial_blob_names, modified_blob_names)

        # Revert to initial state
        self.manager.revert_to_initial_state()

        # Ensure files are synced after revert
        time.sleep(0.1)  # Allow file operations to complete

        # Verify internal state is restored
        reverted_paths = sorted(p.name for p in self.manager._absolute_paths)
        reverted_blob_names = sorted(self.manager._blob_names)

        # Debug output in case of failure
        print(f"Reverted paths: {reverted_paths}")
        print(f"Reverted blob names: {reverted_blob_names}")

        self.assertEqual(initial_paths, reverted_paths)
        self.assertEqual(initial_blob_names, reverted_blob_names)

    def test_revert_to_snapshot_updates_state(self):
        """Test that reverting to any snapshot updates internal state correctly."""
        # Take snapshot of initial state (2 files)
        snapshot1 = self.manager.snapshot_workspace()
        initial_paths = set(p.name for p in self.manager._absolute_paths)
        initial_blob_names = set(self.manager._blob_names)

        # Add a new file and take another snapshot
        (self.root / "file3.txt").write_text("New content")
        self.manager.update()
        snapshot2 = self.manager.snapshot_workspace()

        # Add another file
        (self.root / "file4.txt").write_text("More content")
        self.manager.update()

        # Revert to first snapshot (2 files)
        self.manager.revert_to_snapshot(snapshot1)
        self.assertEqual(
            initial_paths, set(p.name for p in self.manager._absolute_paths)
        )
        self.assertEqual(initial_blob_names, set(self.manager._blob_names))

        # Revert to second snapshot (3 files)
        self.manager.revert_to_snapshot(snapshot2)
        self.assertEqual(3, len(self.manager._absolute_paths))
        self.assertEqual(3, len(self.manager._blob_names))
        self.assertIn("file3.txt", set(p.name for p in self.manager._absolute_paths))

    def test_diff_snapshots_basic(self):
        """Test basic diffing between snapshots."""
        # Initial snapshot
        snapshot1 = self.manager.snapshot_workspace()

        # Modify a file and create new one
        (self.root / "file1.txt").write_text("Modified content")
        (self.root / "file3.txt").write_text("New content")

        snapshot2 = self.manager.snapshot_workspace()

        # Get the diff
        diff = self.manager.diff_snapshots(snapshot1, snapshot2)

        # Verify diff contents
        self.assertEqual(2, len(diff))  # Should have 2 files in diff

        # Find the modified and new files in diff
        modified_file = next(f for f in diff if f.path == "file1.txt")
        new_file = next(f for f in diff if f.path == "file3.txt")

        # Check modified file
        self.assertFalse(modified_file.is_added_file)
        self.assertFalse(modified_file.is_removed_file)
        self.assertEqual(1, len(modified_file))  # One hunk
        self.assertIn("-Initial content 1\n", modified_file[0].source)
        self.assertIn("+Modified content\n", modified_file[0].target)

        # Check new file
        self.assertTrue(new_file.is_added_file)
        self.assertFalse(new_file.is_removed_file)
        self.assertEqual(1, len(new_file))  # One hunk
        self.assertIn("+New content\n", new_file[0].target)

    def test_diff_snapshots_remove_file(self):
        """Test diffing when files are removed."""
        # Initial snapshot
        snapshot1 = self.manager.snapshot_workspace()

        # Remove a file
        (self.root / "file1.txt").unlink()
        self.manager.update()
        snapshot2 = self.manager.snapshot_workspace()

        # Get the diff
        diff = self.manager.diff_snapshots(snapshot1, snapshot2)

        # Verify diff contents
        self.assertEqual(1, len(diff))  # Should have 1 file in diff
        removed_file = diff[0]

        # Check removed file
        self.assertFalse(removed_file.is_added_file)
        self.assertTrue(removed_file.is_removed_file)
        self.assertEqual("file1.txt", removed_file.path)
        self.assertEqual(1, len(removed_file))  # One hunk
        self.assertIn("-Initial content 1\n", removed_file[0].source)

    def test_diff_snapshots_invalid_sequence(self):
        """Test diffing with invalid sequence numbers."""
        snapshot1 = self.manager.snapshot_workspace()

        with self.assertRaises(ValueError):
            self.manager.diff_snapshots(snapshot1, 999)

        with self.assertRaises(ValueError):
            self.manager.diff_snapshots(999, snapshot1)

    def test_diff_snapshots_whitespace(self):
        """Test diffing with whitespace changes."""
        # Initial snapshot
        snapshot1 = self.manager.snapshot_workspace()

        # Add whitespace to a file
        (self.root / "file1.txt").write_text("Initial content 1  ")  # Added spaces
        self.manager.update()
        snapshot2 = self.manager.snapshot_workspace()

        # Get diffs with and without whitespace ignored
        diff_with_ws = self.manager.diff_snapshots(
            snapshot1, snapshot2, ignore_whitespace=False
        )
        diff_without_ws = self.manager.diff_snapshots(
            snapshot1, snapshot2, ignore_whitespace=True
        )

        # Should show changes when considering whitespace
        self.assertEqual(1, len(diff_with_ws))

        # Should show no changes when ignoring whitespace
        self.assertEqual(0, len(diff_without_ws))

    def test_negative_snapshot_indices(self):
        """Test that negative snapshot indices count from the end."""
        # Take a few snapshots
        self.manager.snapshot_workspace()  # 1
        (self.root / "file1.txt").write_text("Modified 1")
        self.manager.update()
        self.manager.snapshot_workspace()  # 2
        (self.root / "file1.txt").write_text("Modified 2")
        self.manager.update()
        self.manager.snapshot_workspace()  # 3

        # Test negative indices
        self.assertEqual(len(self.manager._snapshots), 4)  # Including initial state

        # -1 should be the last snapshot
        self.manager.revert_to_snapshot(-1)
        self.assertEqual(
            (self.root / "file1.txt").read_text(),
            "Modified 2",
        )

        # -2 should be second to last
        self.manager.revert_to_snapshot(-2)
        self.assertEqual(
            (self.root / "file1.txt").read_text(),
            "Modified 1",
        )

        # -4 should be initial state
        self.manager.revert_to_snapshot(-4)
        self.assertEqual(
            (self.root / "file1.txt").read_text(),
            "Initial content 1",
        )

        # -5 should raise error
        with self.assertRaises(ValueError):
            self.manager.revert_to_snapshot(-5)

        # Test negative indices in diff_snapshots
        diff = self.manager.diff_snapshots(-4, -1)  # Initial to last
        modified_file = next(f for f in diff if f.path == "file1.txt")
        self.assertIn("-Initial content 1\n", modified_file[0].source)
        self.assertIn("+Modified 2\n", modified_file[0].target)

    def test_diff_with_current(self):
        """Test diffing between a snapshot and current state."""
        # Take initial snapshot and modify files
        self.manager.snapshot_workspace()

        # Modify a file and create new one
        (self.root / "file1.txt").write_text("Current content")
        (self.root / "file3.txt").write_text("New content")

        # Get diff without calling update()
        diff = self.manager.diff_with_current(0)  # Compare with initial state

        # Verify diff contents
        self.assertEqual(2, len(diff))  # Should have 2 files in diff

        # Find the modified and new files in diff
        modified_file = next(f for f in diff if f.path == "file1.txt")
        new_file = next(f for f in diff if f.path == "file3.txt")

        # Check modified file
        self.assertFalse(modified_file.is_added_file)
        self.assertFalse(modified_file.is_removed_file)
        self.assertEqual(1, len(modified_file))  # One hunk
        self.assertIn("-Initial content 1\n", modified_file[0].source)
        self.assertIn("+Current content\n", modified_file[0].target)

        # Check new file
        self.assertTrue(new_file.is_added_file)
        self.assertFalse(new_file.is_removed_file)
        self.assertEqual(1, len(new_file))  # One hunk
        self.assertIn("+New content\n", new_file[0].target)

        # Test with negative index
        diff2 = self.manager.diff_with_current(-1)  # Compare with last snapshot
        self.assertEqual(2, len(diff2))  # Should show same changes

        # Test with invalid sequence
        with self.assertRaises(ValueError):
            self.manager.diff_with_current(999)
        with self.assertRaises(ValueError):
            self.manager.diff_with_current(-999)

    def test_diff_with_current_new_files(self):
        """Test diffing with current state when files are added."""
        # Take initial snapshot with no files
        empty_dir = self.root / "empty"
        empty_dir.mkdir()

        # Create workspace manager in empty directory
        manager = WorkspaceManagerImpl(
            augment_client=self.mock_client,
            root=empty_dir,
        )
        manager.snapshot_workspace()  # Initial snapshot (empty)

        # Create two new files
        (empty_dir / "file1.txt").write_text("Content 1")
        (empty_dir / "file2.txt").write_text("Content 2")

        # Get diff without calling update()
        diff = manager.diff_with_current(0)  # Compare with initial state

        # Verify diff contents
        self.assertEqual(2, len(diff))  # Should have 2 files in diff

        # Both files should be marked as new
        for patched_file in diff:
            self.assertTrue(
                patched_file.is_added_file,
                f"File {patched_file.path} should be marked as added",
            )
            self.assertFalse(patched_file.is_removed_file)
            self.assertEqual(1, len(patched_file))  # One hunk

        # Verify file contents
        file1 = next(f for f in diff if f.path == "file1.txt")
        file2 = next(f for f in diff if f.path == "file2.txt")
        self.assertIn("+Content 1\n", file1[0].target)
        self.assertIn("+Content 2\n", file2[0].target)

    def test_diff_with_current_deleted_file(self):
        """Test that diff shows correct content when a file is deleted."""
        # Take initial snapshot with one file
        test_dir = self.root / "test"
        test_dir.mkdir()
        test_file = test_dir / "file.txt"
        test_file.write_text("Initial content\nSecond line\n")

        manager = WorkspaceManagerImpl(
            augment_client=self.mock_client,
            root=test_dir,
        )
        manager.snapshot_workspace()  # Initial snapshot

        # Delete the file
        test_file.unlink()

        # Get diff without calling update()
        diff = manager.diff_with_current(0)  # Compare with initial state

        # Verify diff contents
        self.assertEqual(1, len(diff))  # Should have 1 file in diff
        deleted_file = diff[0]

        # Check file metadata
        self.assertFalse(deleted_file.is_added_file)
        self.assertTrue(deleted_file.is_removed_file)
        self.assertEqual("file.txt", deleted_file.path)

        # Check diff content
        self.assertEqual(1, len(deleted_file))  # One hunk
        self.assertEqual(
            ["-Initial content\n", "-Second line\n"],
            deleted_file[0].source,
            "Diff should show exact content of deleted file",
        )

    def test_get_last_turn_diff(self):
        """Test getting diff between last two snapshots."""
        # Should return None with no snapshots
        self.assertIsNone(self.manager.get_last_turn_diff())

        # Take first snapshot - should still return None
        self.manager.snapshot_workspace()
        self.assertIsNone(self.manager.get_last_turn_diff())

        # Take second snapshot with no changes - should return None
        self.manager.snapshot_workspace()
        self.assertIsNone(self.manager.get_last_turn_diff())

        # Make changes and take another snapshot
        (self.root / "file1.txt").write_text("Modified content")
        self.manager.update()
        self.manager.snapshot_workspace()

        # Should get a diff showing the changes
        diff = self.manager.get_last_turn_diff()
        self.assertIsNotNone(diff)
        self.assertEqual(1, len(diff))  # One file changed
        self.assertEqual("file1.txt", diff[0].path)
        self.assertIn("-Initial content 1\n", diff[0][0].source)
        self.assertIn("+Modified content\n", diff[0][0].target)
