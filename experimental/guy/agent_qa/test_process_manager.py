"""Tests for process manager."""

import time
from pathlib import Path
import pytest
from unittest.mock import Mock

from experimental.guy.agent_qa.process_manager import ProcessManager
from experimental.guy.agent_qa.process_tools import ProcessManagerTool
from research.agents.tools import Tool<PERSON>allLogger
from experimental.guy.agent_qa.workspace_manager import WorkspaceManagerImpl
from experimental.guy.agent_qa.command_approval import CommandApprovalManager


@pytest.fixture
def process_manager():
    """Create a process manager."""
    return ProcessManager()


@pytest.fixture
def mock_tool_call_logger():
    """Create a mock tool call logger."""
    return Mock(spec=ToolCallLogger)


@pytest.fixture
def mock_command_approval_manager():
    """Create a mock command approval manager."""
    return Mock(spec=CommandApprovalManager)


@pytest.fixture
def mock_workspace_manager(tmp_path):
    """Create a mock workspace manager."""
    mock = Mock(spec=WorkspaceManagerImpl)
    mock.root = tmp_path
    return mock


def test_process_launch_and_read(process_manager):
    """Test launching a process and reading its output."""
    # Launch a process that outputs something and exits
    pid = process_manager.launch("echo 'test output' && sleep 0.1 && echo 'more output'")
    assert pid > 0

    # First read should get first output
    output = process_manager.read_output(pid)
    assert output is not None
    assert "test output" in output.stdout
    assert output.return_code is None  # Process still running

    # Wait for process to complete and read all output
    more_output_seen = False
    return_code_seen = False
    for _ in range(50):  # Try up to 50 times (5 seconds)
        output = process_manager.read_output(pid)
        assert output is not None
        
        if "more output" in output.stdout:
            more_output_seen = True
        if output.return_code is not None:
            return_code_seen = True
            assert output.return_code == 0
        
        if more_output_seen and return_code_seen:
            break
            
        time.sleep(0.1)
        
    assert more_output_seen, "Should have seen 'more output'"
    assert return_code_seen, "Should have seen return code"


def test_process_kill(process_manager):
    """Test killing a process."""
    # Launch a long-running process
    pid = process_manager.launch("sleep 10")
    assert pid > 0

    # Kill it
    assert process_manager.kill(pid)

    # Try to read from it
    output = process_manager.read_output(pid)
    assert output is None  # Process should be cleaned up


def test_process_cleanup(process_manager):
    """Test that process resources are cleaned up."""
    # Launch a quick process
    pid = process_manager.launch("echo test")

    # Wait for it to complete
    time.sleep(0.1)

    # Read output until we get return code
    while True:
        output = process_manager.read_output(pid)
        if output is None or output.return_code is not None:
            break

    # Process should be cleaned up
    assert pid not in process_manager._processes
    assert pid not in process_manager._stdout_buffers
    assert pid not in process_manager._stderr_buffers


def test_process_accumulated_output(process_manager):
    """Test that read_output returns all output since last read."""
    # Launch a process that outputs multiple lines with delays
    pid = process_manager.launch("echo 'first line' && sleep 0.1 && echo 'second line' && sleep 0.1 && echo 'third line'")
    assert pid > 0

    # First read should get first line
    output = process_manager.read_output(pid)
    assert output is not None
    assert "first line" in output.stdout
    assert "second line" not in output.stdout
    assert "third line" not in output.stdout
    assert output.return_code is None

    # Wait a bit
    time.sleep(0.15)

    # Second read should get second line
    output = process_manager.read_output(pid)
    assert output is not None
    assert "first line" not in output.stdout  # Already read
    assert "second line" in output.stdout
    assert "third line" not in output.stdout
    assert output.return_code is None

    # Wait for process to complete
    time.sleep(0.15)

    # Final read should get third line and return code
    output = process_manager.read_output(pid)
    assert output is not None
    assert "first line" not in output.stdout  # Already read
    assert "second line" not in output.stdout  # Already read
    assert "third line" in output.stdout
    assert output.return_code == 0


def test_process_manager_tool_launch(
    mock_tool_call_logger, mock_workspace_manager, mock_command_approval_manager
):
    """Test launching a process through the tool."""
    tool = ProcessManagerTool(
        mock_tool_call_logger,
        mock_workspace_manager,
        mock_command_approval_manager,
        ask_user_permission=False,
    )

    # Launch a process
    result = tool.run_impl({"action": "launch", "command": "echo test"})
    assert "Process launched with PID" in result.tool_output


def test_process_manager_tool_kill(
    mock_tool_call_logger, mock_workspace_manager, mock_command_approval_manager
):
    """Test killing a process through the tool."""
    tool = ProcessManagerTool(
        mock_tool_call_logger,
        mock_workspace_manager,
        mock_command_approval_manager,
        ask_user_permission=False,
    )

    # Launch and then kill a process
    result = tool.run_impl({"action": "launch", "command": "sleep 10"})
    pid = int(result.tool_output.split()[-1])  # Extract PID from launch message

    result = tool.run_impl({"action": "kill", "process_id": pid})
    assert f"Process {pid} killed" in result.tool_output


def test_process_manager_tool_read(
    mock_tool_call_logger, mock_workspace_manager, mock_command_approval_manager
):
    """Test reading from a process through the tool."""
    tool = ProcessManagerTool(
        mock_tool_call_logger,
        mock_workspace_manager,
        mock_command_approval_manager,
        ask_user_permission=False,
    )

    # Launch a process
    result = tool.run_impl({"action": "launch", "command": "echo test && sleep 0.1"})
    pid = int(result.tool_output.split()[-1])  # Extract PID from launch message

    # Read output
    result = tool.run_impl({"action": "read", "process_id": pid})
    assert "<stdout>" in result.tool_output
    assert "test" in result.tool_output

    # Wait for process to complete
    time.sleep(0.2)

    # Read again to get return code
    result = tool.run_impl({"action": "read", "process_id": pid})
    assert "<return-code>" in result.tool_output
    assert "0" in result.tool_output


def test_process_manager_tool_permission(
    mock_tool_call_logger, mock_workspace_manager, mock_command_approval_manager
):
    """Test that the tool respects user permission settings."""
    tool = ProcessManagerTool(
        mock_tool_call_logger,
        mock_workspace_manager,
        mock_command_approval_manager,
        ask_user_permission=True,
    )

    # Set up mock to deny permission
    mock_command_approval_manager.get_approval.return_value = False

    # Try to launch a process
    result = tool.run_impl({"action": "launch", "command": "echo test"})
    assert "not run because user did not give permission" in result.tool_output

    # Verify approval was requested
    mock_command_approval_manager.get_approval.assert_called_once()
