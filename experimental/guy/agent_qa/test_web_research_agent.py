from unittest.mock import Mock, patch
import pytest

from research.agents.tools import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>
from experimental.guy.agent_qa.agents import PromptedLLMAgent


class MockWebSearchTool(LLMTool):
    name = "web_search"
    description = "Mock web search tool"
    input_schema = {"type": "object"}

    def run_impl(self, tool_input, dialog_messages=None):
        return "Mock search results", "Searched"


class MockWebPageGetterTool(LLMTool):
    name = "get_web_page"
    description = "Mock web page getter tool"
    input_schema = {"type": "object"}

    def run_impl(self, tool_input, dialog_messages=None):
        return "Mock page content", "Got page"


class MockCompleteTool(LLMTool):
    name = "complete"
    description = "Mock complete tool"
    input_schema = {"type": "object"}

    def run_impl(self, tool_input, dialog_messages=None):
        return "Mock completion", "Completed"


@pytest.fixture
def mock_llm_client():
    """Create a mock LLM client."""
    client = Mock(spec=LLMClient)
    client.generate.return_value = ("Test response", {})
    return client


@pytest.fixture
def tool_call_logger():
    """Create a mock tool call logger."""
    return Mock(spec=ToolCallLogger)


def test_web_research_agent_creation(mock_llm_client, tool_call_logger):
    """Test that the web research agent can be created from YAML."""
    yaml_dict = {
        "name": "web_research",
        "description": "An agent that performs web research",
        "input_schema": {
            "type": "object",
            "properties": {
                "query": {"type": "string", "description": "The research query"}
            },
            "required": ["query"],
        },
        "system_prompt": "You are a web research assistant",
        "prompt": "Research this query: {{ query }}",
        "tools": ["web_search", "get_web_page", "complete"],
    }

    agent = PromptedLLMAgent.from_yaml(
        yaml_dict=yaml_dict,
        client=mock_llm_client,
        tool_call_logger=tool_call_logger,
        available_tools=[
            MockWebSearchTool(tool_call_logger),
            MockWebPageGetterTool(tool_call_logger),
            MockCompleteTool(tool_call_logger),
        ],
        max_turns=10,
    )

    assert agent.name == "web_research"
    assert "web research" in agent.description.lower()
    assert len(agent.tools) == 3  # web_search, get_web_page, complete


def test_web_research_agent_template_rendering(mock_llm_client, tool_call_logger):
    """Test that the agent correctly renders the query template."""
    yaml_dict = {
        "name": "web_research",
        "description": "An agent that performs web research",
        "input_schema": {
            "type": "object",
            "properties": {
                "query": {"type": "string", "description": "The research query"}
            },
            "required": ["query"],
        },
        "system_prompt": "You are a web research assistant",
        "prompt": "Research this query: {{ query }}",
        "tools": ["web_search", "get_web_page", "complete"],
    }

    agent = PromptedLLMAgent.from_yaml(
        yaml_dict=yaml_dict,
        client=mock_llm_client,
        tool_call_logger=tool_call_logger,
        available_tools=[
            MockWebSearchTool(tool_call_logger),
            MockWebPageGetterTool(tool_call_logger),
            MockCompleteTool(tool_call_logger),
        ],
        max_turns=10,
    )

    # Mock the Agent class to capture the formatted prompt
    with patch("experimental.guy.agent_qa.agents.Agent") as MockAgent:
        mock_agent_instance = Mock()
        mock_agent_instance.run_agent.return_value = "Test result"
        MockAgent.return_value = mock_agent_instance

        agent.run({"query": "test query"})

        # Check that Agent was called with correctly formatted prompt
        calls = mock_agent_instance.run_agent.call_args_list
        assert len(calls) == 1
        formatted_prompt = calls[0].args[0]
        assert "Research this query: test query" in formatted_prompt
