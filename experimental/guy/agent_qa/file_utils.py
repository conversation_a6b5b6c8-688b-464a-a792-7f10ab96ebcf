import chardet

from pathlib import Path


def safe_read_file(path: str | Path) -> str | None:
    """Reads a file safely, handling encoding errors."""
    buf = Path(path).read_bytes()
    try:
        # Try utf-8 first because chardet fails on empty files
        text = buf.decode("utf-8")
    except UnicodeDecodeError:
        enc = chardet.detect(buf)
        if enc["encoding"] is None:
            return None
        try:
            text = buf.decode(enc["encoding"])
        except UnicodeDecodeError:
            return None
    return text
