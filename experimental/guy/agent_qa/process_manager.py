"""Process manager for long-running commands."""

import os
import select
import subprocess
from dataclasses import dataclass
from pathlib import Path
from typing import Dict, <PERSON>tional, Tuple

from experimental.guy.agent_qa.string_utils import truncate_string


@dataclass
class ProcessOutput:
    """Output from a process."""
    stdout: str
    stderr: str
    return_code: Optional[int]  # None if process is still running


@dataclass
class ProcessInfo:
    """Information about a process."""
    pid: int
    command: str
    state: str  # "running" or "exited"
    return_code: Optional[int]  # None if still running


class ProcessManager:
    """Manages long-running processes."""

    def __init__(self, char_budget: int = 100_000):
        """Initialize the process manager.
        
        Args:
            char_budget: Maximum number of characters to store for each stream (stdout/stderr)
        """
        self.char_budget = char_budget
        self._processes: Dict[int, subprocess.Popen] = {}
        self._stdout_buffers: Dict[int, list[str]] = {}
        self._stderr_buffers: Dict[int, list[str]] = {}
        self._last_read_stdout: Dict[int, int] = {}  # Maps pid to last read position
        self._last_read_stderr: Dict[int, int] = {}
        self._completed_processes: Dict[int, ProcessInfo] = {}  # Keep info about completed processes

    def launch(self, command: str, cwd: Optional[Path] = None) -> int:
        """Launch a new process.
        
        Args:
            command: The command to run
            cwd: Working directory for the command
            
        Returns:
            The process ID
        """
        process = subprocess.Popen(
            command,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            stdin=subprocess.PIPE,  # Enable stdin
            text=True,
            cwd=cwd,
            shell=True,
        )

        assert process.stdout is not None
        assert process.stderr is not None
        assert process.stdin is not None  # Add stdin assertion

        # Set stdout and stderr to non-blocking mode
        os.set_blocking(process.stdout.fileno(), False)
        os.set_blocking(process.stderr.fileno(), False)

        pid = process.pid
        self._processes[pid] = process
        self._stdout_buffers[pid] = []
        self._stderr_buffers[pid] = []
        self._last_read_stdout[pid] = 0
        self._last_read_stderr[pid] = 0

        return pid

    def write_input(self, pid: int, input_text: str) -> bool:
        """Write input to a process's stdin.
        
        Args:
            pid: Process ID to write to
            input_text: Text to write
            
        Returns:
            True if process was found and input was written, False otherwise
        """
        if pid not in self._processes:
            return False

        process = self._processes[pid]
        assert process.stdin is not None

        try:
            process.stdin.write(input_text)
            if not input_text.endswith("\n"):
                process.stdin.write("\n")
            process.stdin.flush()
            return True
        except (OSError, IOError):
            return False

    def kill(self, pid: int) -> bool:
        """Kill a process.
        
        Args:
            pid: Process ID to kill
            
        Returns:
            True if process was killed, False if it wasn't found
        """
        if pid not in self._processes:
            return False

        process = self._processes[pid]
        process.kill()
        process.wait()

        # Clean up resources
        self._cleanup_process(pid)
        return True

    def read_output(self, pid: int) -> Optional[ProcessOutput]:
        """Read output from a process.
        
        This returns any new output since the last read, and the return code
        if the process has completed.
        
        Args:
            pid: Process ID to read from
            
        Returns:
            ProcessOutput object if process exists, None otherwise
        """
        if pid not in self._processes:
            return None

        process = self._processes[pid]
        assert process.stdout is not None
        assert process.stderr is not None

        # Use select to wait for output (with a timeout)
        readable, _, _ = select.select([process.stdout, process.stderr], [], [], 0.1)

        # Read any available output
        if process.stdout in readable:
            while True:
                line = process.stdout.readline()
                if not line:
                    break
                self._stdout_buffers[pid].append(line)

        if process.stderr in readable:
            while True:
                line = process.stderr.readline()
                if not line:
                    break
                self._stderr_buffers[pid].append(line)

        # Check if process has finished
        return_code = process.poll()
        if return_code is not None:
            # Read any remaining output
            for line in process.stdout:
                self._stdout_buffers[pid].append(line)
            for line in process.stderr:
                self._stderr_buffers[pid].append(line)

            # Get all new output since last read
            stdout = self._get_new_output(pid, is_stdout=True)
            stderr = self._get_new_output(pid, is_stdout=False)

            # Clean up process
            self._cleanup_process(pid)

            return ProcessOutput(stdout, stderr, return_code)

        # Return any new output since last read
        stdout = self._get_new_output(pid, is_stdout=True)
        stderr = self._get_new_output(pid, is_stdout=False)
        return ProcessOutput(stdout, stderr, None)

    def _get_new_output(self, pid: int, is_stdout: bool) -> str:
        """Get new output since last read.
        
        Args:
            pid: Process ID
            is_stdout: True for stdout, False for stderr
            
        Returns:
            New output as a string
        """
        buffer = self._stdout_buffers[pid] if is_stdout else self._stderr_buffers[pid]
        last_read = self._last_read_stdout[pid] if is_stdout else self._last_read_stderr[pid]
        
        if last_read >= len(buffer):
            return ""
            
        output = "".join(buffer[last_read:])
        
        # Update last read position
        if is_stdout:
            self._last_read_stdout[pid] = len(buffer)
        else:
            self._last_read_stderr[pid] = len(buffer)
            
        # Truncate if needed
        if len(output) > self.char_budget:
            output = truncate_string(output, self.char_budget)
            
        return output

    def _cleanup_process(self, pid: int):
        """Clean up resources for a process."""
        if pid in self._processes:
            process = self._processes[pid]
            return_code = process.poll()
            if return_code is not None:
                # Store info about completed process
                self._completed_processes[pid] = ProcessInfo(
                    pid=pid,
                    command=process.args,
                    state="exited",
                    return_code=return_code,
                )
            
            self._processes.pop(pid)
            self._stdout_buffers.pop(pid, None)
            self._stderr_buffers.pop(pid, None)
            self._last_read_stdout.pop(pid, None)
            self._last_read_stderr.pop(pid, None)

    def list_processes(self) -> list[ProcessInfo]:
        """List all known processes.
        
        Returns:
            List of ProcessInfo objects for all processes ever launched,
            including ones that have completed.
        """
        result = []
        # Check running processes
        for pid, process in list(self._processes.items()):
            # Check if process has finished
            return_code = process.poll()
            if return_code is not None:
                # Process finished, clean it up
                self._cleanup_process(pid)
                # It will be added from _completed_processes below
            else:
                result.append(ProcessInfo(
                    pid=pid,
                    command=process.args,
                    state="running",
                    return_code=None,
                ))
        
        # Add completed processes
        result.extend(self._completed_processes.values())
        
        # Sort by PID for consistent ordering
        result.sort(key=lambda p: p.pid)
        return result
