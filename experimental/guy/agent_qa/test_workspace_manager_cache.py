"""Tests for workspace manager cache functionality."""

import tempfile
from pathlib import Path
import pytest
from unittest.mock import Mock, patch
from experimental.guy.agent_qa.workspace_manager import (
    WorkspaceManagerImpl,
    TextFileFilter,
)


@pytest.fixture
def workspace_root():
    """Create a temporary workspace root."""
    with tempfile.TemporaryDirectory() as temp_dir:
        yield Path(temp_dir)


@pytest.fixture
def cache_root():
    """Create a temporary cache root."""
    with tempfile.TemporaryDirectory() as temp_dir:
        yield Path(temp_dir)


@pytest.fixture
def mock_client():
    """Create a mock AugmentPrototypingClient."""
    return Mock()


def test_workspace_cache_initialization(workspace_root, mock_client, cache_root):
    """Test that workspace manager initializes cache correctly."""
    manager = WorkspaceManagerImpl(mock_client, workspace_root, cache_root=cache_root)
    assert manager._cache.cache_root == cache_root


def test_workspace_cache_update_unchanged(workspace_root, mock_client, cache_root):
    """Test that unchanged files don't trigger blob name computation."""
    # Create a test file
    test_file = workspace_root / "test.py"
    test_file.write_text("test content")

    manager = WorkspaceManagerImpl(mock_client, workspace_root, cache_root=cache_root)

    # First update should compute blob name
    manager.update()
    initial_upload_count = mock_client.upload_blobs_as_needed.call_count
    initial_blob_names = manager.get_blob_names()

    # Second update with unchanged file should use cache
    manager.update()
    assert mock_client.upload_blobs_as_needed.call_count == initial_upload_count
    assert manager.get_blob_names() == initial_blob_names


def test_workspace_cache_update_modified(workspace_root, mock_client, cache_root):
    """Test that modified files trigger blob name recomputation."""
    # Create a test file
    test_file = workspace_root / "test.py"
    test_file.write_text("initial content")

    manager = WorkspaceManagerImpl(mock_client, workspace_root, cache_root=cache_root)
    manager.update()
    initial_blob_names = manager.get_blob_names()

    # Modify file
    test_file.write_text("modified content")
    manager.update()

    # Should have new blob name
    assert manager.get_blob_names() != initial_blob_names
    assert mock_client.upload_blobs_as_needed.call_count == 2  # Two uploads


def test_workspace_cache_removed_file(workspace_root, mock_client, cache_root):
    """Test that removed files are handled correctly."""
    # Create a test file
    test_file = workspace_root / "test.py"
    test_file.write_text("test content")

    manager = WorkspaceManagerImpl(mock_client, workspace_root, cache_root=cache_root)
    manager.update()
    _ = manager.get_blob_names()

    # Remove file
    test_file.unlink()
    manager.update()

    # Should have no blob names
    assert not manager.get_blob_names()
    assert test_file.name not in manager._cache._files


def test_workspace_cache_added_file(workspace_root, mock_client, cache_root):
    """Test that added files are handled correctly."""
    manager = WorkspaceManagerImpl(mock_client, workspace_root, cache_root=cache_root)
    manager.update()
    initial_blob_names = manager.get_blob_names()

    # Add new file
    test_file = workspace_root / "test.py"
    test_file.write_text("test content")
    manager.update()

    # Should have one blob name
    assert len(manager.get_blob_names()) == 1
    assert len(initial_blob_names) == 0
    assert mock_client.upload_blobs_as_needed.call_count == 1


def test_workspace_cache_default_root(workspace_root, mock_client):
    """Test that workspace manager uses default cache root when none provided."""
    manager = WorkspaceManagerImpl(mock_client, workspace_root)
    assert manager._cache.cache_root == Path.home() / ".augment" / "agent"
