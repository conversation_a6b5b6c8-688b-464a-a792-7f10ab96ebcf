{"cells": [{"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The autoreload extension is already loaded. To reload it, use:\n", "  %reload_ext autoreload\n", "Successfully processed /tmp/agent_log_20241230_224412.pickle\n", "Successfully processed /tmp/agent_log_20241225_035637.pickle\n", "Successfully processed /tmp/agent_log_20241229_092129.pickle\n", "Successfully processed /tmp/agent_log_20241229_055146.pickle\n", "Successfully processed /tmp/agent_log_20241229_085127.pickle\n", "Successfully processed /tmp/agent_log_20241231_054830.pickle\n", "Successfully processed /tmp/agent_log_20241226_222936.pickle\n", "Failed to load /tmp/agent_log_20241224_052037.pickle\n", "Successfully processed /tmp/agent_log_20241229_035805.pickle\n", "Successfully processed /tmp/agent_log_20241230_210836.pickle\n", "Failed to load /tmp/agent_log_20241225_033756.pickle\n", "Successfully processed /tmp/agent_log_20241231_054021.pickle\n", "Failed to load /tmp/agent_log_20241225_031851.pickle\n", "Successfully processed /tmp/agent_log_20241226_181714.pickle\n", "Successfully processed /tmp/agent_log_20241229_092846.pickle\n", "Successfully processed /tmp/agent_log_20241226_230424.pickle\n", "Successfully processed /tmp/agent_log_20241231_061242.pickle\n", "Successfully processed /tmp/agent_log_20241226_223831.pickle\n", "Successfully processed /tmp/agent_log_20241229_073609.pickle\n", "Successfully processed /tmp/agent_log_20241226_230903.pickle\n", "Successfully processed /tmp/agent_log_20241230_215020.pickle\n", "Successfully processed /tmp/agent_log_20241230_215219.pickle\n", "Successfully processed /tmp/agent_log_20241229_040104.pickle\n", "Successfully processed /tmp/agent_log_20241230_192257.pickle\n", "Successfully processed /tmp/agent_log_20241225_034825.pickle\n", "Successfully processed /tmp/agent_log_20241227_002605.pickle\n", "Successfully processed /tmp/agent_log_20241229_081851.pickle\n", "Successfully processed /tmp/agent_log_20241225_041327.pickle\n", "Failed to load /tmp/agent_log_20241225_033856.pickle\n", "Failed to load /tmp/agent_log_20241225_031932.pickle\n", "Successfully processed /tmp/agent_log_20241230_191439.pickle\n", "Successfully processed /tmp/agent_log_20241231_072702.pickle\n", "Failed to load /tmp/agent_log_20241225_034026.pickle\n", "Successfully processed /tmp/agent_log_20241227_004101.pickle\n", "Successfully processed /tmp/agent_log_20241227_003154.pickle\n", "Failed to load /tmp/agent_log_20241225_031327.pickle\n", "Successfully processed /tmp/agent_log_20241227_003056.pickle\n", "Failed to load /tmp/agent_log_20241225_033541.pickle\n", "Successfully processed /tmp/agent_log_20241230_213013.pickle\n", "Failed to load /tmp/agent_log_20241225_033231.pickle\n", "Successfully processed /tmp/agent_log_20241229_085016.pickle\n", "Successfully processed /tmp/agent_log_20241225_035137.pickle\n", "Successfully processed /tmp/agent_log_20241226_230105.pickle\n", "Successfully processed /tmp/agent_log_20241226_181841.pickle\n", "Failed to load /tmp/agent_log_20241225_034006.pickle\n", "Successfully processed /tmp/agent_log_20241230_203923.pickle\n", "Successfully processed /tmp/agent_log_20241230_210149.pickle\n", "Successfully processed /tmp/agent_log_20241227_200227.pickle\n", "Successfully processed /tmp/agent_log_20241229_072025.pickle\n", "Successfully processed /tmp/agent_log_20241230_193126.pickle\n", "Successfully processed /tmp/agent_log_20241225_034754.pickle\n", "Failed to load /tmp/agent_log_20241225_034136.pickle\n", "Successfully processed /tmp/agent_log_20241230_212110.pickle\n", "Successfully processed /tmp/agent_log_20241226_181733.pickle\n", "Successfully processed /tmp/agent_log_20241229_092328.pickle\n", "Successfully processed /tmp/agent_log_20241230_203416.pickle\n", "Successfully processed /tmp/agent_log_20241225_035329.pickle\n", "Successfully processed /tmp/agent_log_20241229_072441.pickle\n", "Successfully processed /tmp/agent_log_20241229_093134.pickle\n", "Successfully processed /tmp/agent_log_20241229_093246.pickle\n", "Successfully processed /tmp/agent_log_20241227_003306.pickle\n", "Successfully processed /tmp/agent_log_20241229_085736.pickle\n", "Failed to load /tmp/agent_log_20241225_032906.pickle\n", "Successfully processed /tmp/agent_log_20241227_195727.pickle\n", "Successfully processed /tmp/agent_log_20241231_054230.pickle\n", "Successfully processed /tmp/agent_log_20241225_040155.pickle\n", "Successfully processed /tmp/agent_log_20241227_004151.pickle\n", "Successfully processed /tmp/agent_log_20241230_215057.pickle\n", "Successfully processed /tmp/agent_log_20241229_035942.pickle\n", "Successfully processed /tmp/agent_log_20241229_084453.pickle\n", "Successfully processed /tmp/agent_log_20241229_093606.pickle\n", "Successfully processed /tmp/agent_log_20241230_192103.pickle\n", "Failed to load /tmp/agent_log_20241225_033611.pickle\n", "Successfully processed /tmp/agent_log_20241225_034538.pickle\n", "Failed to load /tmp/agent_log_20241225_032052.pickle\n", "Successfully processed /tmp/agent_log_20241226_225409.pickle\n", "Successfully processed /tmp/agent_log_20250103_050533.pickle\n", "Successfully processed /tmp/agent_log_20241229_093033.pickle\n", "Successfully processed /tmp/agent_log_20241229_032316.pickle\n", "Successfully processed /tmp/agent_log_20241225_034657.pickle\n", "Successfully processed /tmp/agent_log_20241226_224541.pickle\n", "Failed to load /tmp/agent_log_20241225_032731.pickle\n", "Failed to load /tmp/agent_log_20241224_052503.pickle\n", "Failed to load /tmp/agent_log_20241225_033947.pickle\n", "Failed to load /tmp/agent_log_20241225_031759.pickle\n", "Successfully processed /tmp/agent_log_20241227_010448.pickle\n", "Successfully processed /tmp/agent_log_20241225_041026.pickle\n", "Successfully processed /tmp/agent_log_20241229_082127.pickle\n", "Successfully processed /tmp/agent_log_20241225_034516.pickle\n", "Successfully processed /tmp/agent_log_20241229_081640.pickle\n", "Successfully processed /tmp/agent_log_20241230_215121.pickle\n", "Failed to load /tmp/agent_log_20241225_033636.pickle\n", "Successfully processed /tmp/agent_log_20241226_233452.pickle\n", "Successfully processed /tmp/agent_log_20241227_004229.pickle\n"]}], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "\n", "import pickle\n", "from pathlib import Path\n", "\n", "from research.agents.tools import ToolCallLogger\n", "\n", "for path in Path(\"/tmp\").glob(\"agent_log_*.pickle\"):\n", "    try:\n", "        logger = ToolCallLogger.from_pickle_file(path)\n", "        html = logger.get_html_representation(truncate_long_outputs=False) + \"\\n\"\n", "        path.with_suffix(\".html\").write_text(html)\n", "        print(f\"Successfully processed {path}\")\n", "    except Exception:\n", "        print(f\"Failed to load {path}\")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}