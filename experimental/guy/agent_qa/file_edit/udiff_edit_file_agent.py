from pathlib import Path
from textwrap import dedent
from typing import Any, Optional

from experimental.guy.agent_qa.file_edit.edit_file_agent import GenericEditFileAgent
from experimental.guy.agent_qa.file_edit.udiff import apply_udiff
from experimental.guy.agent_qa.workspace_manager import (
    WorkspaceManager,
)
from research.agents.tools import (
    DialogMessages,
    LLMTool,
    ToolCallLogger,
    ToolImplOutput,
)
from research.llm_apis.llm_client import LLMClient


class EditFileWithUDiffAgent(GenericEditFileAgent):
    """An agent that edits files using unified diffs.

    This agent uses a language model to generate unified diffs for file edits,
    then applies those diffs to the files.
    """

    def __init__(
        self,
        client: LLMClient,
        tool_call_logger: Too<PERSON><PERSON>allLogger,
        workspace_manager: WorkspaceManager,
        max_output_tokens_per_turn: int,
        max_turns: int = 50,
        review_stage: bool = False,
        reuse_dialog_messages: bool = False,
        review_inputs: bool = False,
        final_review_stage: bool = False,
        normalize_indentation: bool = False,
    ):
        super().__init__(
            client=client,
            tool_call_logger=tool_call_logger,
            workspace_manager=workspace_manager,
            edit_tool=ApplyUdiffTool(tool_call_logger, workspace_manager),
            max_output_tokens_per_turn=max_output_tokens_per_turn,
            max_turns=max_turns,
            review_stage=review_stage,
            reuse_dialog_messages=reuse_dialog_messages,
            review_inputs=review_inputs,
            final_review_stage=final_review_stage,
            normalize_indentation=normalize_indentation,
        )
        self.num_turns = 0

    def _gen_edit_prompt(
        self, tool_input: dict[str, Any], target_file_content: str
    ) -> str:
        return f"""\
Edit the file {tool_input["file_path"]} according to the following instructions:

<short_edit_description>
```
{tool_input["short_edit_description"]}
```
</short_edit_description>

Use `apply_udiff` tool to make the changes. The tool requires:
1. file_path - the path to the file to edit
2. udiff - the unified diff to apply to the file
Break changes into small chunks and use `apply_udiff` to apply one chunk at a time
Always include at least five lines of context around each change.

Here is the full contents of the file:

<file_content>
```
{target_file_content}
```
</file_content>
"""

    def _gen_review_prompt(
        self, tool_input: dict[str, Any], tool_outputs: list[str], diff: str
    ) -> str:
        return f"""\
Here is the diff showing the changes made to {tool_input["file_path"]}:

<diff>
```
{diff}
```
</diff>

If the changes are satisfactory, call the complete tool to end the task.
If not, decide which edits are needed now, and call the `apply_udiff` tool again."""

    def _gen_aux_data(self) -> dict[str, Any]:
        """Generate auxiliary data by collecting data from all ApplyUdiffTool calls in the current session.

        Returns:
            A dictionary containing combined auxiliary data from all udiff tool calls.
        """
        udiff_results = []
        errors = []

        # Go through logged calls to find ApplyUdiffTool calls
        for call in reversed(self.tool_call_logger.logged_calls):
            if (
                hasattr(call, "tool") and call.tool.name == self.name  # type: ignore
            ):
                # We've reached the end of the edit_file_agent calls, so we can stop
                break

            if (
                hasattr(call, "tool")
                and call.tool.name == "apply_udiff"  # type: ignore
                and not call.started  # Only look at completed calls
                and hasattr(call, "auxiliary_data")
                and call.auxiliary_data is not None  # type: ignore
            ):
                # Add udiff result if present
                if "udiff_result" in call.auxiliary_data:  # type: ignore
                    udiff_results.append(call.auxiliary_data["udiff_result"])  # type: ignore
                # Add error message if present
                if "error" in call.auxiliary_data:  # type: ignore
                    errors.append(call.auxiliary_data["error"])  # type: ignore

        return {
            **super()._gen_aux_data(),
            "udiff_results": udiff_results,
            "udiffs_extracted_successfully": len(udiff_results) > 0,
            "errors": errors,
        }


class ApplyUdiffTool(LLMTool):
    """A tool that applies a unified diff to a file."""

    name = "apply_udiff"
    description = "Apply a unified diff (udiff) to a file"
    input_schema = {
        "type": "object",
        "properties": {
            "file_path": {
                "type": "string",
                "description": "Path to the file to modify",
            },
            "udiff": {
                "type": "string",
                "description": "The unified diff to apply",
            },
        },
        "required": ["file_path", "udiff"],
    }

    def __init__(
        self,
        tool_call_logger: ToolCallLogger,
        workspace_manager: WorkspaceManager,
    ):
        super().__init__(tool_call_logger)
        self.workspace_manager = workspace_manager

    def run_impl(
        self,
        tool_input: dict[str, Any],
        dialog_messages: Optional[DialogMessages] = None,
    ) -> ToolImplOutput:
        path = Path(tool_input["file_path"])
        abs_path = self.workspace_manager.root / path

        if not abs_path.exists():
            return ToolImplOutput(
                "File does not exist",
                f"File {path} does not exist",
                auxiliary_data={"success": False},
            )

        target_file_content = abs_path.read_text()

        try:
            result = apply_udiff(
                udiff=tool_input["udiff"],
                original_text=target_file_content,
            )
            if result.error_messages:
                error_messages_str = "\n".join(result.error_messages)
                return ToolImplOutput(
                    f"Failed to apply udiff: {error_messages_str}",
                    f"Failed to apply udiff: {error_messages_str}",
                    auxiliary_data={"udiff_result": result, "success": False},
                )

            abs_path.write_text(result.modified_text)
            self.workspace_manager.update()

            return ToolImplOutput(
                f"Successfully applied udiff to {path}",
                f"File {path} modified",
                auxiliary_data={"udiff_result": result, "success": True},
            )

        except Exception as e:
            return ToolImplOutput(
                f"Failed to apply udiff: {str(e)}",
                "Failed to apply udiff",
                auxiliary_data={"error": str(e), "success": False},
            )

    def get_tool_start_message(self, tool_input: dict[str, Any]) -> str:
        return f"Applying udiff to {tool_input['file_path']}"


def calc_output_stats(outputs: list[ToolImplOutput]) -> dict[str, Any]:
    """Calculate stats for a list of outputs."""
    stats = {
        "total": len(outputs),
        "success": 0,
        "failed": 0,
        "udiffs_extracted": 0,
        "pure_additions": 0,
        "pure_additions_to_non_empty": 0,
        "found_start": 0,
        "total_errors": 0,
        "total_hunks": 0,  # Added total hunks counter
        "success_rate": 0.0,
        "udiff_extraction_rate": 0.0,
        "pure_addition_rate": 0.0,
        "found_start_rate": 0.0,
        "pure_addition_to_non_empty_rate": 0.0,
        "total_lines_to_match": 0,
        "total_lines_matched": 0,
        "total_lines_in_hunks": 0,
        "udiff_results_histogram": {},  # Track frequency of udiff result counts
    }

    for output in outputs:
        aux_data = output.auxiliary_data
        if aux_data.get("udiffs_extracted_successfully"):
            stats["udiffs_extracted"] += 1

            # Count number of udiff results for histogram
            num_results = len(aux_data.get("udiff_results", []))
            stats["udiff_results_histogram"][num_results] = (
                stats["udiff_results_histogram"].get(num_results, 0) + 1
            )

            # Process each UdiffApplyResult
            for result in aux_data.get("udiff_results", []):
                # Process each hunk result
                stats["total_hunks"] += len(
                    result.hunk_apply_results
                )  # Added total hunks count
                for hunk_result in result.hunk_apply_results:
                    if hunk_result.pure_addition:
                        stats["pure_additions"] += 1
                    if hunk_result.pure_addition_to_non_empty_file:
                        stats["pure_additions_to_non_empty"] += 1
                    if hunk_result.found_start:
                        stats["found_start"] += 1
                    stats["total_errors"] += len(hunk_result.error_messages)
                    # Track line-related stats
                    stats["total_lines_to_match"] += hunk_result.num_old_lines_total
                    stats["total_lines_matched"] += hunk_result.num_old_lines_matched
                    stats["total_lines_in_hunks"] += len(hunk_result.hunk.split("\n"))

                stats["total_errors"] += len(result.error_messages)

        else:
            # Add 0 udiff results to histogram for unsuccessful extractions
            stats["udiff_results_histogram"][0] = (
                stats["udiff_results_histogram"].get(0, 0) + 1
            )

        if "Edit agent did not complete after max turns" in output.tool_result_message:
            stats["failed"] += 1
        else:
            stats["success"] += 1

    # Calculate percentages
    total = stats["total"]
    total_hunks = stats["total_hunks"]  # Use the tracked total_hunks instead
    if total > 0:
        stats["success_rate"] = round(stats["success"] / total, 2)
        stats["udiff_extraction_rate"] = round(stats["udiffs_extracted"] / total, 2)
        stats["pure_addition_rate"] = round(
            stats["pure_additions"] / max(total_hunks, 1), 2
        )
        stats["found_start_rate"] = round(stats["found_start"] / max(total_hunks, 1), 2)
        stats["pure_addition_to_non_empty_rate"] = round(
            stats["pure_additions_to_non_empty"] / max(total_hunks, 1),
            2,
        )

        # Calculate line-related stats
        if total_hunks > 0:
            stats["avg_lines_matched"] = round(
                stats["total_lines_matched"] / total_hunks, 2
            )
            stats["avg_lines_per_hunk"] = round(
                stats["total_lines_in_hunks"] / total_hunks, 2
            )
            stats["line_match_rate"] = round(
                stats["total_lines_matched"] / max(stats["total_lines_to_match"], 1),
                2,
            )

    return stats
