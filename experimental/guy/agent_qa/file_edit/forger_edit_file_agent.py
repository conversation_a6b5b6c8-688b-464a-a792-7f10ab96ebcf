from textwrap import dedent
from experimental.guy.agent_qa.builtin_tools import (
    EditFileTool,
    EditFileToolV2,
    FileEditClient,
)
from experimental.guy.agent_qa.file_edit.edit_file_agent import GenericEditFileAgent
from experimental.guy.agent_qa.workspace_manager import WorkspaceManager
from experimental.guy.agent_qa.file_edit.forger_v3_edit_file_tool import EditFileToolV3
from research.agents.tools import ToolCallLogger
from research.llm_apis.llm_client import LLMClient

from typing import Any


class ForgerEditFileAgent(GenericEditFileAgent):
    """An agent that edits files using Forger.

    This agent uses Forger, our smart paste model, to make edits to files.
    """

    def __init__(
        self,
        client: LLMClient,
        tool_call_logger: ToolCallLogger,
        workspace_manager: WorkspaceManager,
        file_edit_client: FileEditClient,
        max_output_tokens_per_turn: int,
        max_turns: int,
        review_stage: bool = False,
        edit_tool_name: str = "forger_v2",
        reuse_dialog_messages: bool = False,
        review_inputs: bool = False,
        final_review_stage: bool = False,
        normalize_indentation: bool = False,
    ):
        self.edit_tool_name = edit_tool_name
        if edit_tool_name == "forger_v1":
            edit_tool = EditFileTool(
                tool_call_logger, workspace_manager, file_edit_client
            )
        elif edit_tool_name == "forger_v2":
            edit_tool = EditFileToolV2(
                tool_call_logger, workspace_manager, file_edit_client
            )
        elif edit_tool_name == "forger_v3":
            edit_tool = EditFileToolV3(
                tool_call_logger, workspace_manager, file_edit_client
            )
        else:
            raise ValueError(f"Unknown edit tool name: {self.edit_tool_name}")

        super().__init__(
            client=client,
            tool_call_logger=tool_call_logger,
            workspace_manager=workspace_manager,
            edit_tool=edit_tool,
            max_output_tokens_per_turn=max_output_tokens_per_turn,
            max_turns=max_turns,
            review_stage=review_stage,
            reuse_dialog_messages=reuse_dialog_messages,
            review_inputs=review_inputs,
            final_review_stage=final_review_stage,
            normalize_indentation=normalize_indentation,
        )

    def _gen_edit_prompt(
        self, tool_input: dict[str, Any], target_file_content: str
    ) -> str:
        return f"""\
Edit the file {tool_input["file_path"]} according to the following instructions:

<short_edit_description>
```
{tool_input["short_edit_description"]}
```
</short_edit_description>

Call the supplied tool to perform the edit. Make sure you specify both a description
and a precise listing of the new code. The new code should include every piece of
code that needs to be changed, even if it spans multiple locations in the file.
You can use placeholder comments to separate the different sections.

Here is the full contents of the file. The remainder of this message contains the contents.

<file_content>
```
{target_file_content}
```
</file_content>
"""

    def _gen_review_prompt(
        self, tool_input: dict[str, Any], tool_outputs: list[str], diff: str
    ) -> str:
        return f"""\
Please review the changes made. Make sure that:
- The instructions were followed correctly
- No important related changes were missed
- No changes were made that weren't necessary from the instructions

Here is the diff showing the changes made:

<diff>
```
{diff}
```
</diff>

If the changes are satisfactory, call the complete tool to end the task.
If not, decide which edits are needed now, and call the edit tool again.
"""

    def _gen_aux_data(self) -> dict[str, Any]:
        """Generate auxiliary data by collecting data from all ForgerEditTool calls in the current session.

        Returns:
            A dictionary containing combined auxiliary data from all forger tool calls.
        """
        request_ids = []
        errors = []

        # Go through logged calls to find ForgerEditTool calls
        for call in reversed(self.tool_call_logger.logged_calls):
            if (
                hasattr(call, "tool") and call.tool.name == self.name  # type: ignore
            ):
                # We've reached the end of the edit_file_agent calls, so we can stop
                break

            if (
                hasattr(call, "tool")
                and call.tool.name == self.edit_tool_name  # type: ignore
                and not call.started  # Only look at completed calls
                and hasattr(call, "tool_output")
                and call.tool_output is not None  # type: ignore
            ):
                # Extract request ID if present
                output = call.tool_output  # type: ignore
                if "request_id=" in output:
                    request_id = output.split("request_id=")[-1].strip()
                    request_ids.append(request_id)
                # Add error message if present
                if "Failed to edit file:" in output:
                    error = output.split("Failed to edit file:")[-1].strip()
                    errors.append(error)

        return {
            **super()._gen_aux_data(),
            "request_ids": request_ids,
            "errors": errors,
        }
