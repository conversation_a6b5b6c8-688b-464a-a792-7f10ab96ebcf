from itertools import takewhile
from typing import List, Tuple
from dataclasses import dataclass, field

from dataclasses_json import DataClassJsonMixin


def fuzzy_compare(
    line1: str,
    line2: str,
    lines1: List[str] | None = None,
    i1: int = 0,
    lines2: List[str] | None = None,
    i2: int = 0,
) -> Tuple[bool, int, int]:
    """Compare two lines ignoring whitespace, case, treating space/underscore as same, and ignoring trailing commas.
    Also handles cases where one line is split across multiple lines.

    Args:
        line1: First line to compare
        line2: Second line to compare
        lines1: Full list of lines containing line1
        i1: Index of line1 in lines1
        lines2: Full list of lines containing line2
        i2: Index of line2 in lines2

    Returns:
        Tuple[bool, int, int]: (match found, lines consumed from first list, lines consumed from second list)
    """

    if lines1 is None:
        lines1 = [line1]
        i1 = 0
    if lines2 is None:
        lines2 = [line2]
        i2 = 0

    def normalize(line: str) -> str:
        if line.startswith("+"):
            line = line[1:]
        if line.startswith("-"):
            line = line[1:]

        # Remove trailing comma
        line = line.rstrip()
        if line.endswith(","):
            line = line[:-1]
        # Replace underscores with spaces, then normalize all whitespace to single space
        line = line.replace("_", " ")
        line = " ".join(line.split())
        # Convert to lowercase
        return line.lower()

    # Handle empty lines
    if not line1.strip() and not line2.strip():
        return True, 1, 1

    # Try simple single line comparison first
    if normalize(line1) == normalize(line2):
        return True, 1, 1

    # If we don't have access to full lines lists, we can't do multi-line matching
    if not all([lines1, lines2, i1 is not None, i2 is not None]):
        return False, 1, 1

    # Try to match multiple lines from either side
    def join_lines(lines: List[str], start: int, count: int) -> str:
        return " ".join(normalize(line) for line in lines[start : start + count])

    # Try combining up to 3 lines from either side
    for n1 in range(1, 4):
        if i1 + n1 > len(lines1):
            continue
        joined1 = join_lines(lines1, i1, n1)

        for n2 in range(1, 4):
            if i2 + n2 > len(lines2):
                continue
            joined2 = join_lines(lines2, i2, n2)

            if joined1 == joined2:
                return True, n1, n2

    return False, 1, 1


def get_new_old_lines(hunk_lines) -> Tuple[List[str], List[str]]:
    old_lines = []
    new_lines = []
    for line in hunk_lines:
        if line.startswith("-"):
            old_lines.append(line)
        elif line.startswith("+"):
            new_lines.append(line)
        else:
            old_lines.append(line)
            new_lines.append(line)
    return old_lines, new_lines


@dataclass
class HunkStartMatch(DataClassJsonMixin):
    start_line: int
    num_lines_matched: int


def find_hunk_start(
    lines: List[str], old_lines: List[str], error_messages: List[str]
) -> HunkStartMatch:
    if not old_lines:
        return HunkStartMatch(start_line=0, num_lines_matched=0)

    # Create a string from the lines we're looking for
    old_lines_without_prefix = [
        line[1:] if line.startswith("-") else line for line in old_lines
    ]

    # If all lines are empty, treat it as a match at the start
    if all(not line.strip() for line in old_lines_without_prefix):
        return HunkStartMatch(start_line=0, num_lines_matched=len(old_lines))

    # Track best partial match for debugging
    best_match_count = 0
    best_match_pos = -1
    best_match_fail_line = 0
    best_match_expected = None
    best_match_actual = None

    # Skip empty lines at the start of old_lines_without_prefix
    first_non_empty = 0
    while (
        first_non_empty < len(old_lines_without_prefix)
        and not old_lines_without_prefix[first_non_empty].strip()
    ):
        first_non_empty += 1

    # If all lines were empty, treat it as a match at the start
    if first_non_empty == len(old_lines_without_prefix):
        return HunkStartMatch(
            start_line=0, num_lines_matched=len(old_lines_without_prefix)
        )

    # Create a hash of the first non-empty line we're looking for
    target_first_line = old_lines_without_prefix[first_non_empty].strip()

    # Scan through lines looking for potential matches of the first line
    i = 0
    while i < len(lines) - (len(old_lines_without_prefix) - first_non_empty) + 1:
        # Skip empty lines in the input if we're looking for a non-empty line
        if not lines[i].strip() and target_first_line:
            i += 1
            continue

        matches, n1, n2 = fuzzy_compare(
            lines[i],
            target_first_line,
            lines,
            i,
            old_lines_without_prefix,
            first_non_empty,
        )

        if matches:
            # Found potential match, verify subsequent lines
            matches = True
            match_count = 1  # Start at 1 for the first match
            j = first_non_empty + n2  # Index in old_lines_without_prefix
            k = n1  # Offset in lines

            while j < len(old_lines_without_prefix):
                # Skip empty lines in old_lines_without_prefix
                while (
                    j < len(old_lines_without_prefix)
                    and not old_lines_without_prefix[j].strip()
                ):
                    j += 1
                    match_count += 1
                if j >= len(old_lines_without_prefix):
                    break

                # Skip empty lines in lines
                while (i + k) < len(lines) and not lines[i + k].strip():
                    k += 1
                if (i + k) >= len(lines):
                    matches = False
                    break

                line_matches, consumed1, consumed2 = fuzzy_compare(
                    lines[i + k],
                    old_lines_without_prefix[j],
                    lines,
                    i + k,
                    old_lines_without_prefix,
                    j,
                )

                if not line_matches:
                    matches = False
                    if match_count > best_match_count:
                        best_match_count = match_count
                        best_match_pos = i
                        best_match_fail_line = k
                        best_match_expected = old_lines_without_prefix[j].strip()
                        best_match_actual = lines[i + k].strip()
                    break
                match_count += 1
                j += consumed2
                k += consumed1

            if matches:
                return HunkStartMatch(start_line=i, num_lines_matched=match_count)
        i += n1

    error_messages.append("\nCould not match old lines from udiff with original text:")
    error_messages.append(f"Best partial match found at position {best_match_pos}")
    error_messages.append(
        f"Matched {best_match_count} out of {len(old_lines_without_prefix)} lines"
    )
    error_messages.append(f"Failed at line {best_match_fail_line}:")
    error_messages.append(f"  Expected: '{best_match_expected}'")
    error_messages.append(f"  Actual:   '{best_match_actual}'")
    error_messages.append("\nContext:")
    start_ctx = max(0, best_match_pos - 2)
    end_ctx = min(len(lines), best_match_pos + best_match_count + 3)
    for idx in range(start_ctx, end_ctx):
        prefix = ">>>" if idx == best_match_pos + best_match_fail_line else "   "
        error_messages.append(f"{prefix} {idx:4d}: {lines[idx]}")

    return HunkStartMatch(start_line=0, num_lines_matched=0)


@dataclass
class HunkLineNumbers(DataClassJsonMixin):
    old_start: int
    old_count: int
    new_start: int
    new_count: int


@dataclass
class Hunk(DataClassJsonMixin):
    hunk_raw: str
    hunk_header: str | None
    line_numbers: HunkLineNumbers | None
    all_lines: List[str]
    old_lines: List[str]
    new_lines: List[str]


@dataclass
class HunkApplyResult(DataClassJsonMixin):
    hunk: str
    modified_text: str
    error_messages: list[str]
    parsed_line_numbers: HunkLineNumbers | None
    matched_line_numbers: HunkLineNumbers | None

    found_start: bool = False
    apply_success: bool = False
    pure_addition: bool = False
    pure_addition_to_non_empty_file: bool = False

    num_old_lines_matched: int = 0
    num_old_lines_total: int = 0


@dataclass
class UdiffApplyResult(DataClassJsonMixin):
    udiff: str
    modified_text: str
    hunk_apply_results: List[HunkApplyResult] = field(default_factory=list)
    error_messages: List[str] = field(default_factory=list)


def strip_empty_lines(text: str) -> str:
    lines = text.splitlines()
    while lines and not lines[0].strip():
        lines.pop(0)
    while lines and not lines[-1].strip():
        lines.pop()
    return "\n".join(lines)


def parse_hunks(udiff: str) -> List[Hunk]:
    """Parse a unified diff string into a list of Hunk objects.

    Args:
        udiff: A unified diff format string

    Returns:
        List of Hunk objects representing each change section
    """
    hunks = []
    current_hunk_lines = []
    current_header = None
    current_line_numbers = None

    # Split and clean input
    lines = udiff.splitlines()
    while lines and not lines[0].strip():
        lines.pop(0)
    while lines and not lines[-1].strip():
        lines.pop()

    # Skip file headers if present
    if lines and lines[0].startswith("---"):
        lines.pop(0)
    if lines and lines[0].startswith("+++"):
        lines.pop(0)

    for line in lines:
        if line.startswith("@@"):
            # If we have a previous hunk, add it to results
            if current_hunk_lines:
                hunk_lines = [
                    line for line in current_hunk_lines if not line.startswith("@@")
                ]
                old_lines, new_lines = get_new_old_lines(hunk_lines)
                hunks.append(
                    Hunk(
                        hunk_raw="\n".join(current_hunk_lines),
                        hunk_header=current_header,
                        line_numbers=current_line_numbers,
                        all_lines=hunk_lines,
                        old_lines=old_lines,
                        new_lines=new_lines,
                    )
                )
                current_hunk_lines = []

            # Parse the hunk header
            current_header = line
            # Parse @@ -1,3 +1,4 @@ format
            try:
                numbers = line.split("@@")[1].strip()
                old_part, new_part = numbers.split(" ")
                old_start = int(old_part.split(",")[0].lstrip("-"))
                old_count = int(old_part.split(",")[1]) if "," in old_part else 1
                new_start = int(new_part.split(",")[0].lstrip("+"))
                new_count = int(new_part.split(",")[1]) if "," in new_part else 1

                current_line_numbers = HunkLineNumbers(
                    old_start=old_start,
                    old_count=old_count,
                    new_start=new_start,
                    new_count=new_count,
                )
            except (IndexError, ValueError):
                current_line_numbers = None

        # skip first context lines in the hunk if they are too short
        # so that we can match by more substantial lines
        if (
            len(current_hunk_lines) == 0
            and len(line.strip()) < 3
            and line.startswith(" ")
        ):
            # skipping first short context lines
            pass
        else:
            current_hunk_lines.append(line)

    # Add the last hunk if there is one
    if current_hunk_lines:
        hunk_lines = [line for line in current_hunk_lines if not line.startswith("@@")]
        old_lines, new_lines = get_new_old_lines(hunk_lines)
        hunks.append(
            Hunk(
                hunk_raw="\n".join(current_hunk_lines),
                hunk_header=current_header,
                line_numbers=current_line_numbers,
                all_lines=hunk_lines,
                old_lines=old_lines,
                new_lines=new_lines,
            )
        )

    return hunks


def _has_enough_context(lines: List[str], hunk: Hunk) -> bool:
    start_context_lines = list(
        takewhile(lambda line: not line.startswith("+"), hunk.all_lines)
    )
    end_context_lines = list(
        takewhile(lambda line: not line.startswith("+"), reversed(hunk.all_lines))
    )
    return len(start_context_lines) == len(hunk.all_lines) or (
        (
            len(start_context_lines) >= 2
            or fuzzy_compare(lines[0], start_context_lines[0])[0]
        )
        and (
            len(end_context_lines) >= 2
            or fuzzy_compare(lines[-1], end_context_lines[0])[0]
        )
    )


def apply_hunk(hunk: Hunk, original_text: str, start_line: int = 0) -> HunkApplyResult:
    result_lines = []
    current_line = start_line
    error_messages = []
    pure_addition = False
    pure_addition_to_non_empty_file = False
    apply_found_start = False
    apply_success = False
    matched_line_numbers = None

    old_lines = hunk.old_lines
    new_lines = hunk.new_lines
    hunk_lines = [line for line in hunk.all_lines if not line.startswith("@@")]

    lines = original_text.splitlines()

    hunk_start_match = HunkStartMatch(start_line=-1, num_lines_matched=0)

    if not old_lines and new_lines:
        pure_addition = True
        # This is an addition to an empty file or at the end of the file
        result_lines.extend(lines)
        result_lines.extend([line[1:] for line in new_lines if line.startswith("+")])

        if len(lines) > 0:
            error_messages.append(
                "Error: udiff does not contain any context even though the target file is not empty"
            )
            pure_addition_to_non_empty_file = True
            apply_success = False
        else:
            apply_success = True
            apply_found_start = True

        # For pure additions, use the end of file as start position
        matched_line_numbers = HunkLineNumbers(
            old_start=len(lines) + start_line,
            old_count=0,
            new_start=len(lines) + start_line,
            new_count=len(new_lines),
        )
    elif not _has_enough_context(lines, hunk):
        error_messages.append(
            f"Error: This hunk does not contain enough context to apply the changes. Please provide more context around changes.\n{hunk.hunk_raw}"
        )
    else:
        # Find the start of the hunk in the original text
        hunk_start_match = find_hunk_start(
            lines[current_line:], old_lines, error_messages
        )
        current_line += hunk_start_match.start_line

        # Compute matched line numbers based on actual position found
        if hunk.line_numbers:
            matched_line_numbers = HunkLineNumbers(
                old_start=current_line + 1,  # 1-based indexing
                old_count=len(old_lines),
                new_start=current_line + 1,  # 1-based indexing
                new_count=len(new_lines),
            )

        if hunk_start_match.num_lines_matched == len(old_lines):
            apply_found_start = True

            # Add unchanged lines before the hunk
            result_lines.extend(lines[:current_line])

            # Apply changes
            hunk_index = 0
            while hunk_index < len(hunk_lines):
                if hunk_lines[hunk_index].startswith("-"):
                    matches, n1, n2 = fuzzy_compare(
                        lines[current_line],
                        hunk_lines[hunk_index][1:],
                        lines,
                        current_line,
                        hunk_lines,
                        hunk_index,
                    )
                    if not matches:
                        if lines[current_line].strip() == "":
                            current_line += 1
                            continue
                        if hunk_lines[hunk_index][1:].strip() == "":
                            hunk_index += 1
                            continue
                        error_msg = (
                            f"Line mismatch at line {current_line + 1}:\n"
                            f"  Expected: '{hunk_lines[hunk_index][1:].strip()}'\n"
                            f"  Found:    '{lines[current_line].strip()}'"
                        )
                        error_messages.append(error_msg)
                        break
                    current_line += n1  # Skip consumed lines from original
                    hunk_index += n2  # Skip consumed lines from diff
                elif hunk_lines[hunk_index].startswith("+"):
                    result_lines.append(hunk_lines[hunk_index][1:])
                    hunk_index += 1
                else:
                    matches, n1, n2 = fuzzy_compare(
                        lines[current_line],
                        hunk_lines[hunk_index][1:],
                        lines,
                        current_line,
                        hunk_lines,
                        hunk_index,
                    )
                    if not matches:
                        if lines[current_line].strip() == "":
                            current_line += 1
                            continue
                        if hunk_lines[hunk_index].strip() == "":
                            hunk_index += 1
                            continue
                        error_msg = (
                            f"Line mismatch at line {current_line + 1}:\n"
                            f"  Expected: '{hunk_lines[hunk_index][1:].strip()}'\n"
                            f"  Found:    '{lines[current_line].strip()}'"
                        )
                        error_messages.append(error_msg)
                        break
                    # Add all consumed lines from original to result
                    for i in range(n1):
                        result_lines.append(lines[current_line + i])
                    current_line += n1
                    hunk_index += n2
            # Add any remaining unchanged lines
            result_lines.extend(lines[current_line:])
            apply_success = True
        else:
            error_messages.append("Could not find matching hunk in original text")

    if apply_success:
        modified_text = "\n".join(result_lines)
    else:
        modified_text = original_text

    return HunkApplyResult(
        hunk=hunk.hunk_raw,
        parsed_line_numbers=hunk.line_numbers,
        matched_line_numbers=matched_line_numbers,
        modified_text=modified_text,
        error_messages=error_messages,
        found_start=apply_found_start,
        apply_success=apply_success,
        pure_addition=pure_addition,
        pure_addition_to_non_empty_file=pure_addition_to_non_empty_file,
        num_old_lines_matched=hunk_start_match.num_lines_matched,
        num_old_lines_total=len(old_lines),
    )


def apply_udiff(udiff: str, original_text: str) -> UdiffApplyResult:
    """Apply a unified diff to original text.

    Args:
        udiff: The unified diff string to apply
        original_text: The original text to apply the diff to

    Returns:
        UdiffApplyResult containing the modified text and any error messages
    """
    print("**** Applying udiff: ****")
    print(udiff)
    print("********")

    if not udiff.strip():
        return UdiffApplyResult(
            udiff=udiff,
            modified_text=original_text,
            error_messages=[],
        )

    hunks = parse_hunks(udiff)

    # Track current position and error messages
    current_text = original_text
    start_line = 0
    all_error_messages = []
    hunk_apply_results = []

    # Apply each hunk sequentially
    for hunk in hunks:
        try:
            result = apply_hunk(hunk, current_text, start_line)
            current_text = result.modified_text
            all_error_messages.extend(result.error_messages)
            hunk_apply_results.append(result)
            if len(result.error_messages) > 0:
                break

            # Update start line for next hunk if line numbers available
            if result.matched_line_numbers:
                start_line = (
                    result.matched_line_numbers.new_start
                    + result.matched_line_numbers.new_count
                    - 1
                )

        except ValueError as e:
            all_error_messages.append(str(e))
            # Create a failed result for this hunk
            hunk_apply_results.append(
                HunkApplyResult(
                    hunk=hunk.hunk_raw,
                    modified_text=current_text,
                    error_messages=[str(e)],
                    parsed_line_numbers=hunk.line_numbers,
                    matched_line_numbers=None,
                    found_start=False,
                    apply_success=False,
                    pure_addition=False,
                    pure_addition_to_non_empty_file=False,
                    num_old_lines_matched=0,
                    num_old_lines_total=len(hunk.old_lines),
                )
            )

    if len(all_error_messages) > 0:
        current_text = original_text

    return UdiffApplyResult(
        udiff=udiff,
        modified_text=current_text,
        error_messages=all_error_messages,
        hunk_apply_results=hunk_apply_results,
    )
