import pytest
from pathlib import Path
from unittest.mock import <PERSON><PERSON>ock
from experimental.guy.agent_qa.file_edit.str_replace_editor_tool_brief_old_str import (
    StrReplaceEditorToolBriefOldStr,
    ToolError,
)
from experimental.guy.agent_qa.workspace_manager import WorkspaceManager
from research.agents.tools import Tool<PERSON>allLogger


@pytest.fixture
def tool(tmp_path):
    workspace_manager = MagicMock()
    workspace_manager.root = tmp_path
    tool_call_logger = ToolCallLogger()
    return StrReplaceEditorToolBriefOldStr(tool_call_logger, workspace_manager)


@pytest.fixture
def test_file(tmp_path):
    file_path = tmp_path / "test.txt"
    content = """line 1
line 2
line 3
line 4
line 5
line 6
line 7
line 8
line 9
line 10
line 11
line 12
line 13
line 14
line 15"""
    file_path.write_text(content)
    return file_path


def test_str_replace_non_sequential_line_numbers(tool, test_file):
    """Test str_replace with non-sequential line numbers."""
    old_str = """     1\tline 1
     2\tline 2
     4\tline 3
     5\tline 4
     6\tline 5"""
    new_str = "new content"
    with pytest.raises(ToolError) as exc:
        tool.str_replace(test_file, old_str, new_str)
    assert "Line number errors" in str(exc.value)
    assert "Line number 4 is not sequential (expected 3)" in str(exc.value)


def test_str_replace_content_mismatch(tool, test_file):
    """Test str_replace with content mismatch."""
    old_str = """     1\tline 1
     2\tline 2
     3\twrong content
     4\tline 4
     5\tline 5"""
    new_str = "new content"
    with pytest.raises(ToolError) as exc:
        tool.str_replace(test_file, old_str, new_str)
    assert "Content mismatches" in str(exc.value)
    assert "expected 'wrong content', found 'line 3'" in str(exc.value)


def test_str_replace_invalid_line_format(tool, test_file):
    """Test str_replace with invalid line format."""
    old_str = """     1\tline 1
     2\tline 2
     invalid line format
     4\tline 4
     5\tline 5"""
    new_str = "new content"
    with pytest.raises(ToolError) as exc:
        tool.str_replace(test_file, old_str, new_str)
    assert "Line number errors" in str(exc.value)
    assert "doesn't have proper line number format" in str(exc.value)


def test_str_replace_with_ellipsis_content_mismatch(tool, test_file):
    """Test str_replace with ellipsis and content mismatch."""
    old_str = """     1\tline 1
     2\tline 2
     3\tline 3
     4\tline 4
     5\tline 5
...
    11\tline 11
    12\twrong content
    13\tline 13
    14\tline 14
    15\tline 15"""
    new_str = "new content"
    with pytest.raises(ToolError) as exc:
        tool.str_replace(test_file, old_str, new_str)
    assert "Content mismatches" in str(exc.value)
    assert "expected 'wrong content', found 'line 12'" in str(exc.value)


def test_str_replace_with_ellipsis_non_sequential_line_numbers(tool, test_file):
    """Test str_replace with ellipsis and non-sequential line numbers."""
    old_str = """     1\tline 1
     2\tline 2
     3\tline 3
     4\tline 4
     5\tline 5
...
    11\tline 11
    13\tline 12
    14\tline 13
    15\tline 14
    16\tline 15"""
    new_str = "new content"
    with pytest.raises(ToolError) as exc:
        tool.str_replace(test_file, old_str, new_str)
    assert "Line number errors" in str(exc.value)
    assert "Line number 13 is not sequential" in str(exc.value)
