import pytest
from pathlib import Path
import tempfile
from unittest.mock import <PERSON><PERSON>ock

from experimental.guy.agent_qa.file_edit.str_replace_editor_tool_with_multiedit import (
    StrReplaceEditorToolWithMultiEdit,
    ToolError,
)
from research.agents.tools import Tool<PERSON>all<PERSON>ogger


def test_multiple_str_replace(tmp_path):
    # Setup
    workspace_manager = MagicMock()
    workspace_manager.root = tmp_path
    test_file = tmp_path / "test.txt"
    test_file.write_text("line1\nline2\nline3\nline4\nline5")

    tool = StrReplaceEditorToolWithMultiEdit(
        tool_call_logger=ToolCallLogger(),
        workspace_manager=workspace_manager,
        ignore_indentation_for_str_replace=False,
    )

    # Test multiple replacements
    result = tool.run_impl(
        {
            "command": "str_replace",
            "path": str(test_file),
            "str_replace_entries": [
                {"old_str": "line2", "new_str": "replaced2"},
                {"old_str": "line4", "new_str": "replaced4"},
            ],
        }
    )
    assert result.success
    assert test_file.read_text() == "line1\nreplaced2\nline3\nreplaced4\nline5"


def test_multiple_inserts(tmp_path):
    # Setup
    workspace_manager = MagicMock()
    workspace_manager.root = tmp_path
    test_file = tmp_path / "test.txt"
    test_file.write_text("line1\nline2\nline3")

    tool = StrReplaceEditorToolWithMultiEdit(
        tool_call_logger=ToolCallLogger(),
        workspace_manager=workspace_manager,
        ignore_indentation_for_str_replace=False,
    )

    # Test multiple inserts
    result = tool.run_impl(
        {
            "command": "insert",
            "path": str(test_file),
            "insert_line_entries": [
                {"insert_line": 1, "new_str": "inserted1"},
                {"insert_line": 2, "new_str": "inserted2"},
            ],
        }
    )
    assert result.success
    assert test_file.read_text() == "line1\ninserted1\nline2\ninserted2\nline3"

    # Test line numbers are relative to original file
    result = tool.run_impl(
        {
            "command": "insert",
            "path": str(test_file),
            "insert_line_entries": [
                {"insert_line": 1, "new_str": "new1"},
                {"insert_line": 1, "new_str": "new2"},
            ],
        }
    )
    assert result.success
    # new2 should come after new1 since they target same line
    expected = "line1\nnew1\nnew2\ninserted1\nline2\ninserted2\nline3"
    assert test_file.read_text() == expected


def test_sequential_operations(tmp_path):
    # Setup
    workspace_manager = MagicMock()
    workspace_manager.root = tmp_path
    test_file = tmp_path / "test.txt"
    test_file.write_text("line1\nline2\nline3\nline4")

    tool = StrReplaceEditorToolWithMultiEdit(
        tool_call_logger=ToolCallLogger(),
        workspace_manager=workspace_manager,
        ignore_indentation_for_str_replace=False,
    )

    # First do str_replace operations
    result = tool.run_impl(
        {
            "command": "str_replace",
            "path": str(test_file),
            "str_replace_entries": [
                {"old_str": "line2", "new_str": "replaced2"},
                {"old_str": "line4", "new_str": "replaced4"},
            ],
        }
    )
    assert result.success
    assert test_file.read_text() == "line1\nreplaced2\nline3\nreplaced4"

    # Then do insert operations
    result = tool.run_impl(
        {
            "command": "insert",
            "path": str(test_file),
            "insert_line_entries": [
                {"insert_line": 1, "new_str": "inserted1"},
                {"insert_line": 3, "new_str": "inserted3"},
            ],
        }
    )
    assert result.success
    expected = "line1\ninserted1\nreplaced2\nline3\ninserted3\nreplaced4"
    assert test_file.read_text() == expected


def test_error_cases(tmp_path):
    # Setup
    workspace_manager = MagicMock()
    workspace_manager.root = tmp_path
    test_file = tmp_path / "test.txt"
    test_file.write_text("line1\nline2\nline3")

    tool = StrReplaceEditorToolWithMultiEdit(
        tool_call_logger=ToolCallLogger(),
        workspace_manager=workspace_manager,
        ignore_indentation_for_str_replace=False,
    )

    # Test invalid line number
    result = tool.run_impl(
        {
            "command": "insert",
            "path": str(test_file),
            "insert_line_entries": [{"insert_line": 10, "new_str": "invalid"}],
        }
    )
    assert not result.success
    assert "Invalid line number: 10" in result.tool_output

    # Test non-existent string
    result = tool.run_impl(
        {
            "command": "str_replace",
            "path": str(test_file),
            "str_replace_entries": [
                {"old_str": "nonexistent", "new_str": "replacement"}
            ],
        }
    )
    assert not result.success
    assert "No replacement was performed" in result.tool_output


def test_empty_file(tmp_path):
    # Setup
    workspace_manager = MagicMock()
    workspace_manager.root = tmp_path
    test_file = tmp_path / "test.txt"
    test_file.write_text("")

    tool = StrReplaceEditorToolWithMultiEdit(
        tool_call_logger=ToolCallLogger(),
        workspace_manager=workspace_manager,
        ignore_indentation_for_str_replace=False,
    )

    # Test insert into empty file
    result = tool.run_impl(
        {
            "command": "insert",
            "path": str(test_file),
            "insert_line_entries": [{"insert_line": 0, "new_str": "first line"}],
        }
    )
    assert result.success
    assert test_file.read_text() == "first line"

    # Test str_replace with empty old_str on empty file
    test_file.write_text("")
    result = tool.run_impl(
        {
            "command": "str_replace",
            "path": str(test_file),
            "str_replace_entries": [{"old_str": "", "new_str": "new content"}],
        }
    )
    assert result.success
    assert test_file.read_text() == "new content"


def test_whitespace_handling(tmp_path):
    # Setup
    workspace_manager = MagicMock()
    workspace_manager.root = tmp_path
    test_file = tmp_path / "test.txt"
    test_file.write_text("    indented1\n        indented2\n    indented3")

    tool = StrReplaceEditorToolWithMultiEdit(
        tool_call_logger=ToolCallLogger(),
        workspace_manager=workspace_manager,
        ignore_indentation_for_str_replace=False,
    )

    # Test exact indentation matching
    result = tool.run_impl(
        {
            "command": "str_replace",
            "path": str(test_file),
            "str_replace_entries": [
                {"old_str": "    indented1", "new_str": "    replaced1"},
                {"old_str": "        indented2", "new_str": "        replaced2"},
            ],
        }
    )
    assert result.success
    assert test_file.read_text() == "    replaced1\n        replaced2\n    indented3"

    # Test with ignore_indentation=True
    tool = StrReplaceEditorToolWithMultiEdit(
        tool_call_logger=ToolCallLogger(),
        workspace_manager=workspace_manager,
        ignore_indentation_for_str_replace=True,
    )
    result = tool.run_impl(
        {
            "command": "str_replace",
            "path": str(test_file),
            "str_replace_entries": [
                {"old_str": "indented3", "new_str": "replaced3"},
            ],
        }
    )
    assert result.success
    assert test_file.read_text() == "    replaced1\n        replaced2\n    replaced3"


# Tests for multiple occurrences and line number disambiguation
def test_multiple_occurrences_without_line_numbers(tmp_path):
    """Test replacing a string that appears multiple times without line numbers."""
    workspace_manager = MagicMock()
    workspace_manager.root = tmp_path
    test_file = tmp_path / "test_file.txt"
    test_content = """This is a test file with multiple occurrences.
Line 2 has some content.
Line 3 has DUPLICATE_STRING.
Line 4 has some content.
Line 5 has some content.
Line 6 has DUPLICATE_STRING.
Line 7 has some content.
Line 8 has some content.
Line 9 has DUPLICATE_STRING.
Line 10 has some content.
"""
    test_file.write_text(test_content)

    tool = StrReplaceEditorToolWithMultiEdit(
        tool_call_logger=ToolCallLogger(),
        workspace_manager=workspace_manager,
    )

    # Try to replace without line numbers (should fail)
    with pytest.raises(ToolError) as exc_info:
        tool.str_replace(test_file, "DUPLICATE_STRING", "REPLACED_STRING")

    assert "Multiple occurrences" in str(exc_info.value)


def test_multiple_occurrences_with_exact_line_numbers(tmp_path):
    """Test replacing a string that appears multiple times with exact line numbers."""
    workspace_manager = MagicMock()
    workspace_manager.root = tmp_path
    test_file = tmp_path / "test_file.txt"
    test_content = """This is a test file with multiple occurrences.
Line 2 has some content.
Line 3 has DUPLICATE_STRING.
Line 4 has some content.
Line 5 has some content.
Line 6 has DUPLICATE_STRING.
Line 7 has some content.
Line 8 has some content.
Line 9 has DUPLICATE_STRING.
Line 10 has some content.
"""
    test_file.write_text(test_content)

    tool = StrReplaceEditorToolWithMultiEdit(
        tool_call_logger=ToolCallLogger(),
        workspace_manager=workspace_manager,
    )

    # Replace with exact line numbers
    result = tool.str_replace(test_file, "DUPLICATE_STRING", "REPLACED_STRING_1", 3, 3)
    assert result.success
    assert "REPLACED_STRING_1" in test_file.read_text()
    assert test_file.read_text().count("DUPLICATE_STRING") == 2


def test_multiple_occurrences_with_approximate_line_numbers(tmp_path):
    """Test replacing a string that appears multiple times with approximate line numbers."""
    workspace_manager = MagicMock()
    workspace_manager.root = tmp_path
    test_file = tmp_path / "test_file.txt"
    test_content = """This is a test file with multiple occurrences.
Line 2 has some content.
Line 3 has DUPLICATE_STRING.
Line 4 has some content.
Line 5 has some content.
Line 6 has DUPLICATE_STRING.
Line 7 has some content.
Line 8 has some content.
Line 9 has DUPLICATE_STRING.
Line 10 has some content.
"""
    test_file.write_text(test_content)

    tool = StrReplaceEditorToolWithMultiEdit(
        tool_call_logger=ToolCallLogger(),
        workspace_manager=workspace_manager,
    )

    # Replace with approximate line numbers
    result = tool.str_replace(
        test_file,
        "DUPLICATE_STRING",
        "REPLACED_STRING_2",
        7,
        7,  # Off by 1 from the actual line 6
    )
    assert result.success
    assert "Warning" in result.tool_output
    assert "REPLACED_STRING_2" in test_file.read_text()


def test_line_numbers_out_of_threshold(tmp_path):
    """Test replacing with line numbers that are too far from any occurrence."""
    workspace_manager = MagicMock()
    workspace_manager.root = tmp_path
    test_file = tmp_path / "test_file.txt"
    test_content = """This is a test file with multiple occurrences.
Line 2 has some content.
Line 3 has DUPLICATE_STRING.
Line 4 has some content.
Line 5 has some content.
Line 6 has DUPLICATE_STRING.
Line 7 has some content.
Line 8 has some content.
Line 9 has DUPLICATE_STRING.
Line 10 has some content.
"""
    test_file.write_text(test_content)

    tool = StrReplaceEditorToolWithMultiEdit(
        tool_call_logger=ToolCallLogger(),
        workspace_manager=workspace_manager,
    )

    # Try to replace with line numbers that are too far
    with pytest.raises(ToolError) as exc_info:
        tool.str_replace(
            test_file,
            "DUPLICATE_STRING",
            "REPLACED_STRING_3",
            1000,
            1000,  # Too far (more than 5 lines difference from any occurrence)
        )

    assert (
        "No occurrence of old_str matches the provided line numbers within threshold"
        in str(exc_info.value)
    )


def test_single_occurrence(tmp_path):
    """Test replacing a string that appears only once in the file."""
    workspace_manager = MagicMock()
    workspace_manager.root = tmp_path
    test_file = tmp_path / "test_file.txt"
    test_content = """First line of content
Second line of content
Third line of content
Unique content
Fifth line of content
"""
    test_file.write_text(test_content)

    tool = StrReplaceEditorToolWithMultiEdit(
        tool_call_logger=ToolCallLogger(),
        workspace_manager=workspace_manager,
    )

    result = tool.str_replace(test_file, "Unique content", "Modified unique content")
    assert result.success
    assert "Modified unique content" in test_file.read_text()


def test_single_occurrence_with_line_numbers(tmp_path):
    """Test replacing a string that appears only once with line numbers."""
    workspace_manager = MagicMock()
    workspace_manager.root = tmp_path
    test_file = tmp_path / "test_file.txt"
    test_content = """First line of content
Second line of content
Third line of content
Unique content
Fifth line of content
"""
    test_file.write_text(test_content)

    tool = StrReplaceEditorToolWithMultiEdit(
        tool_call_logger=ToolCallLogger(),
        workspace_manager=workspace_manager,
    )

    result = tool.str_replace(
        test_file, "Second line of content", "Modified second line", 2, 2
    )
    assert result.success
    # Should include a warning since line numbers are provided but not needed
    assert "Modified second line" in test_file.read_text()


def test_sequential_replacements_with_line_numbers(tmp_path):
    """Test multiple sequential replacements with line numbers."""
    workspace_manager = MagicMock()
    workspace_manager.root = tmp_path
    test_file = tmp_path / "test_file.txt"
    test_content = """First line of content
Second line of content
Third line of content
Duplicate content
Fifth line of content
Sixth line of content
Seventh line of content
Eighth line of content
Ninth line of content
Duplicate content
Eleventh line of content
"""
    test_file.write_text(test_content)

    tool = StrReplaceEditorToolWithMultiEdit(
        tool_call_logger=ToolCallLogger(),
        workspace_manager=workspace_manager,
    )

    # Replace the first occurrence (line 4)
    result = tool.str_replace(
        test_file, "Duplicate content", "Modified content 1", 4, 4
    )
    assert result.success
    assert "Modified content 1" in test_file.read_text()
    assert (
        "Duplicate content" in test_file.read_text()
    )  # Second occurrence still exists

    # Replace the second occurrence (line 10)
    result = tool.str_replace(
        test_file, "Duplicate content", "Modified content 2", 10, 10
    )
    assert result.success
    assert "Modified content 1" in test_file.read_text()
    assert "Modified content 2" in test_file.read_text()
    assert "Duplicate content" not in test_file.read_text()  # All occurrences replaced
