import copy
import time
from pathlib import Path
from typing import Any, Optional

from base.diff_utils.diff_utils import File, compute_file_diff
from experimental.guy.agent_qa.builtin_tools import CompleteTool, EditFileAgentDesc
from experimental.guy.agent_qa.file_edit.file_edit_utils import (
    calc_theoretical_latency,
    extract_xml_content,
)
from experimental.guy.agent_qa.file_edit.indent_utils import (
    apply_indent_type,
    detect_indent_type,
    normalize_indent,
)
from experimental.guy.agent_qa.workspace_manager import (
    WorkspaceManager,
)
from research.agents.tools import (
    DialogMessages,
    LLMTool,
    LoggedToolCall,
    ToolCallLogger,
    ToolCallParameters,
    ToolImplOutput,
    call_tools,
)
from research.llm_apis.llm_client import LLMClient, TextResult


class ReportReviewTool(LLMTool):
    name = "report_review"
    description = "Report the review result"
    input_schema = {
        "type": "object",
        "properties": {
            "review_passed": {
                "type": "boolean",
                "description": "Whether the review result was pass or fail",
            },
            "issue_codes": {
                "type": "array",
                "items": {
                    "type": "string",
                    "description": "Code of the issue found during the review",
                },
                "description": "List of codes of the issues found during the review",
            },
            "review_details": {
                "type": "string",
                "description": "Details of the review result",
            },
        },
        "required": ["review_passed", "issue_codes", "review_details"],
    }

    def __init__(
        self,
        tool_call_logger: ToolCallLogger,
    ):
        super().__init__(tool_call_logger)
        self.review_result: Optional[dict[str, Any]] = None

    def reset(self):
        self.review_result = None

    def merge_results(self, other_results: dict[str, Any] | None):
        if self.review_result is None:
            self.review_result = other_results
        elif other_results is not None:
            self.review_result["issue_codes"].extend(other_results["issue_codes"])
            self.review_result["review_details"] += (
                "\n\n" + other_results["review_details"]
            )
            self.review_result["review_passed"] = (
                self.review_result["review_passed"] and other_results["review_passed"]
            )
            self.review_result["strict_review_passed"] = (
                self.review_result["strict_review_passed"]
                and other_results["strict_review_passed"]
            )

    def run_impl(
        self,
        tool_input: dict[str, Any],
        dialog_messages: Optional[DialogMessages] = None,
    ) -> ToolImplOutput:
        self.review_result = tool_input
        self.review_result["strict_review_passed"] = (
            tool_input["review_passed"] and not tool_input["issue_codes"]
        )
        return ToolImplOutput(
            tool_output=f"Review result reported with passed: {tool_input['review_passed']}",
            tool_result_message=f"Review result reported with passed: {tool_input['review_passed']}",
            auxiliary_data={
                **tool_input,
            },
        )

    def get_tool_start_message(self, tool_input: dict[str, Any]) -> str:
        return f"Reporting review result, passed: {tool_input['review_passed']}"


class GenericEditFileAgent(LLMTool):
    name = EditFileAgentDesc.name
    description = EditFileAgentDesc.description
    input_schema = EditFileAgentDesc.input_schema

    def __init__(
        self,
        client: LLMClient,
        tool_call_logger: ToolCallLogger,
        workspace_manager: WorkspaceManager,
        edit_tool: LLMTool,
        max_output_tokens_per_turn: int,
        max_turns: int,
        review_stage: bool = False,
        reuse_dialog_messages: bool = False,
        review_inputs: bool = False,
        final_review_stage: bool = False,
        normalize_indentation: bool = False,
        retriever_tool: Optional[LLMTool] = None,
    ):
        super().__init__(tool_call_logger)
        self.client = client
        self.workspace_manager = workspace_manager
        self.max_output_tokens = max_output_tokens_per_turn
        self.max_turns = max_turns
        self.review_stage = review_stage
        self.edit_tool = edit_tool
        self.reuse_dialog_messages = reuse_dialog_messages
        self.review_inputs = review_inputs
        self.final_review_stage = final_review_stage
        self.normalize_indentation = normalize_indentation
        self.retriever_tool = retriever_tool

        self.report_review_tool = ReportReviewTool(self.tool_call_logger)
        self.reset()

    def reset(self):
        # stats
        self.success = False
        self.num_wrong_tool_calls = 0
        self.num_wrong_tool_inputs = 0
        self.num_failed_edits = 0
        self.num_successful_edits = 0
        self.num_verify_passed = 0
        self.num_verify_failed = 0
        self.input_review_result: Optional[dict[str, Any]] = None
        self.diff_review_results = []
        self.final_review_stage_result: Optional[dict[str, Any]] = None
        self.edit_latency: Optional[float] = None
        self.num_input_tokens = 0
        self.num_output_tokens = 0

    def _gen_system_prompt(self) -> str:
        return ""

    def _gen_edit_prompt(
        self, tool_input: dict[str, Any], target_file_content: str
    ) -> str:
        raise NotImplementedError()

    def _gen_review_prompt(
        self, tool_input: dict[str, Any], tool_outputs: list[str], diff: str
    ) -> str:
        raise NotImplementedError()

    def _format_edit_tool_input(self, tool_input: dict[str, Any]) -> str:
        raise NotImplementedError()

    def _gen_retriever_prompt_general(self, tool_input: dict[str, Any]) -> str:
        raise NotImplementedError()

    def _gen_retriever_prompt_specific(self, tool_input: dict[str, Any]) -> str:
        raise NotImplementedError()

    def _post_process_tool_calls(
        self, tool_calls: list[ToolCallParameters]
    ) -> list[ToolCallParameters]:
        return tool_calls

    def _gen_aux_data(self) -> dict[str, Any]:
        self.num_total_edits = self.num_successful_edits + self.num_failed_edits
        aux_data: dict[str, Any] = {
            "success": self.success,
            "num_total_edits": self.num_total_edits,
            "num_wrong_tool_calls": self.num_wrong_tool_calls,
            "num_wrong_tool_inputs": self.num_wrong_tool_inputs,
            "num_failed_edits": self.num_failed_edits,
            "num_successful_edits": self.num_successful_edits,
            "num_verify_passed": self.num_verify_passed,
            "num_verify_failed": self.num_verify_failed,
            "edit_latency": self.edit_latency,
            "num_input_tokens": self.num_input_tokens,
            "num_output_tokens": self.num_output_tokens,
            "theoretical_edit_latency": calc_theoretical_latency(
                self.num_total_edits, self.num_input_tokens, self.num_output_tokens
            ),
            # "diff_review_results": self.diff_review_results,
        }
        aux_data |= (
            {f"input_review_result_{k}": v for k, v in self.input_review_result.items()}
            if self.input_review_result
            else {}
        )
        aux_data |= (
            {
                f"final_review_stage_result_{k}": v
                for k, v in self.final_review_stage_result.items()
            }
            if self.final_review_stage_result
            else {}
        )
        all_diff_codes = set(
            [
                code
                for result in self.diff_review_results
                for code in result.get("issue_codes", [])
            ]
        )
        aux_data["all_diff_codes"] = sorted(list(all_diff_codes))
        return aux_data

    def _gen_failed_edit_prompt(
        self, tool_input: dict[str, Any], tool_outputs: list[str]
    ) -> str:
        return f"""\
There was a problem editing the file.
Review the output of the {self.edit_tool.name} tool
Fix the edit and call {self.edit_tool.name} tool again."""

    def do_review_inputs(
        self,
        tool_input: dict[str, Any],
        file_content: str,
        dialog_messages: DialogMessages,
    ):
        review_prompt = f"""\
You are given a task to edit a file. You are given the following inputs:
- file_path: the path to the file to edit
- short_edit_description: a short description of the edit to make
- file_content: the full contents of the file

Review the edit description and the file content and verify that the task makes sense.
Check for the following problems and report them by adding their code to the issue_codes input param of the `report_review` tool:
- DESCRIPTION_NOT_CLEAR: edit description is not clear or ambiguous
- DESCRIPTION_NOT_SPECIFIC: edit description is not specific enough to perform the edit
- FILE_CONTENT_CORRUPTED: file content is corrupted or truncated
- FILE_CONTENT_IRRELEVANT: file content is not relevant to the edit description
- EDITS_ALREADY_PRESENT: edits described in the edit description are already present in the file content
- DUPLICATED_FUNCS_OR_CLASSES: duplicated functions or classes are present in the file content

If no problems are found, call the `report_review` tool with review_passed set to true.
If problems are found, call the `report_review` tool with review_passed set to false and add the codes of the problems found to the issue_codes input param.
Add a detailed description of the problems found to the review_details input param of the `report_review` tool.

Here are the inputs:
<file_path>{tool_input["file_path"]}</file_path>
<short_edit_description>
{tool_input["short_edit_description"]}
</short_edit_description>

<file_content>
```
{file_content}
```
</file_content>
"""
        self.report_review_tool.reset()
        if not self.reuse_dialog_messages:
            dialog_messages = copy.deepcopy(dialog_messages)

        dialog_messages.add_user_prompt(
            review_prompt,
            allow_append_to_tool_call_results=True,
        )
        response, metadata = self.client.generate(
            messages=dialog_messages.get_messages_for_llm_client(),
            max_tokens=self.max_output_tokens,
            tools=[self.report_review_tool.get_tool_param()],
            tool_choice={"type": "tool", "name": ReportReviewTool.name},
        )
        dialog_messages.add_model_response(response)
        call_tools(
            tools=[self.report_review_tool],
            calls_to_make=dialog_messages.get_pending_tool_calls(),
            dialog_messages=dialog_messages,
        )
        self.input_review_result = self.report_review_tool.review_result

    def check_for_placeholder_comments(self, diff: str):
        diff_review_tool = ReportReviewTool(self.tool_call_logger)
        dialog_messages = DialogMessages()

        diff_review_prompt = f"""\
Please review the following diff and check for placeholder comments that were added in the diff.
Ignore any placeholder comments in the context lines or in the removed lines.
Only report placeholder comments that were added in the diff which are prefixed with "+"
Empty comments are not considered placeholder comments.
Code that is commented out is not considered placeholder comments.

If any are found, call the `report_review` tool with review_passed set to false and add the code "PLACEHOLDER_COMMENTS" to the issue_codes input param.
Add a detailed description of the problems found to the review_details input param of the `report_review` tool.
If no problems are found, call the `report_review` tool with review_passed set to true and review_details set to empty string.

examples of placeholder comments:
// ... rest of the imports and code ...
// ... rest of the code ...
# ... other classes unchanged ...`
# ... rest of class unchanged ...`
# ... init and other methods unchanged ...`
// Add at the top of the file with other use statements
# Changed value from 5 to 7
# Added logic to process input

Here is the diff you need to review. Pay special attention to the lines prefixed with "+" since we are going to analyze them later:
```
{diff}
```
"""
        dialog_messages.add_user_prompt(
            diff_review_prompt,
            allow_append_to_tool_call_results=True,
        )
        response, metadata = self.client.generate(
            messages=dialog_messages.get_messages_for_llm_client(),
            max_tokens=self.max_output_tokens,
            tools=[diff_review_tool.get_tool_param()],
            tool_choice={"type": "tool", "name": ReportReviewTool.name},
        )
        dialog_messages.add_model_response(response)
        call_tools(
            tools=[diff_review_tool],
            calls_to_make=dialog_messages.get_pending_tool_calls(),
            dialog_messages=dialog_messages,
        )
        self.diff_review_results.append(diff_review_tool.review_result)
        return diff_review_tool.review_result

    def check_for_change_comments(self, diff: str, dialog_messages: DialogMessages):
        diff_review_tool = ReportReviewTool(self.tool_call_logger)
        local_dialog_messages = DialogMessages()

        diff_review_prompt = f"""\
Please review the following diff and check for change_comments that were added in the diff.
A change_comment is a comment that describes a change that was or should be made to the code
Ignore any change_comments in the context lines or in the removed lines.
Only report change_comments that were added in the diff which are prefixed with "+"
Empty comments are not considered change_comments.

examples of change_comments which are describing changes in the code:
<change_comment_examples>
// Add at the top of the file with other import statements
import math

# Removed function gen_sequences()

assert result == 7 # Changed value from 5 to 7

# Added logic to process input
process(input)

</change_comment_examples>

examples of comments that are similar to change_comments but are not change_comments because they are describing the logic of the code:
<not_change_comment_examples>
// import required modules
import math

# Remove empty lines
lines = [line for line in lines if line.strip()]

assert result == 7 # Check result

# Logic to process input
process(input)

</not_change_comment_examples>

Use the following thinking pattern:
Go through the diff line by line.
    If the line is not prefixed with "+" skip it
    If the line does not contain comments skip it
    If the line is part of a docstring skip it
    Now you have an added comment that needs to be checked if it is a change_comment
    Check current or next line to see what this comment is refering to and cite that line in your analysis.
    Taking into account the comment and the code it is refering to decide if it is a change_comment.
    If the comment describes the logic of the code then it's not a change_comment.
    Go through the provided examples and compare the added comment to the examples.
    If the added comment is similar to any of the examples then it is a change_comment

Use the following thinking pattern for each line in the diff:
<diff_analysis>
    <line_analysis>
        <line>
        copy diff line exactly
        </line>
        <is_addition>
        true if the line is prefixed with "+", false otherwise
        </is_addition>
        <is_comment>
        true if the line contains a comment, false otherwise
        docstrings are NOT considered as comments
        </is_comment>
        <is_docstring>
        true if the line is part of a docstring, false otherwise
        </is_docstring>
        <code_referenced_by_comment>
        cite the line of code that the comment is refering to
        </code_referenced_by_comment>
        <reasoning_for_change_comment>
        provide reasoning for why it should be considered a change_comment
        check if the comment is about the changes made to the code then it's a change_comment. See the examples in <change_comment_examples/>
        </reasoning_for_change_comment>
        <reasoning_for_not_change_comment>
        provide reasoning for why it should not be considered a change_comment
        check if the comment is about the logic of the code then it's not a change_comment. See the examples in <not_change_comment_examples/>
        </reasoning_for_not_change_comment>
        <is_change_comment>
        based on the reasoning above decide if it is a change_comment or not
        </is_change_comment>
    </line_analysis>
</diff_analysis>

If any are found, call the `report_review` tool with review_passed set to false and add the code "CHANGE_COMMENTS" to the issue_codes input param.
Add a detailed description of the problems found to the review_details input param of the `report_review` tool.
If no problems are found, call the `report_review` tool with review_passed set to true and review_details set to empty string.

Here is the diff you need to review. Pay special attention to the lines prefixed with "+" since we are going to analyze them later:
<diff>
```
{diff}
```
</diff>
"""
        local_dialog_messages.add_user_prompt(
            diff_review_prompt,
            allow_append_to_tool_call_results=True,
        )
        pending_tool_calls = []
        num_turns_left = 1
        while len(pending_tool_calls) == 0 or num_turns_left > 0:
            num_turns_left -= 1
            response, metadata = self.client.generate(
                messages=local_dialog_messages.get_messages_for_llm_client(),
                max_tokens=self.max_output_tokens,
                tools=[diff_review_tool.get_tool_param()],
                tool_choice={"type": "auto"},
            )
            local_dialog_messages.add_model_response(response)
            pending_tool_calls = local_dialog_messages.get_pending_tool_calls()
            if len(pending_tool_calls) == 0:
                local_dialog_messages.add_user_prompt(
                    "Please call the `report_review` tool to report the review result."
                )
            elif num_turns_left > 0:
                call_tools(
                    tools=[diff_review_tool],
                    calls_to_make=pending_tool_calls,
                    dialog_messages=local_dialog_messages,
                )
                local_dialog_messages.add_user_prompt(
                    "Double check your analysis", allow_append_to_tool_call_results=True
                )

        call_tools(
            tools=[diff_review_tool],
            calls_to_make=pending_tool_calls,
            dialog_messages=local_dialog_messages,
        )
        dialog_messages._message_lists.extend(local_dialog_messages._message_lists)
        return diff_review_tool.review_result

    def do_diff_review(
        self,
        tool_input: dict[str, Any],
        dialog_messages: DialogMessages,
        abs_path: Path,
        edit_tool_input: dict[str, Any],
    ):
        path = Path(tool_input["file_path"])
        edited_file_content = abs_path.read_text()
        diff = compute_file_diff(
            before_file=File(path=str(path), contents=self.prev_file_content_version),
            after_file=File(path=str(path), contents=edited_file_content),
            same_line_smart_header=True,
            num_context_lines=10,
        )
        self.prev_file_content_version = edited_file_content

        # not used right now
        #         edit_tool_input_desc = []
        #         for k, v in edit_tool_input.items():
        #             if k == "file_path":
        #                 continue
        #             edit_tool_input_desc.append(f"""
        # <{k}>:
        # ```
        # {v}
        # ```
        # </{k}>
        # """)
        #         edit_tool_input_desc_str = "\n".join(edit_tool_input_desc)

        diff_review_prompt = f"""\
Please review the changes made by the `{self.edit_tool.name}` tool call. Use `report_review` tool to report the review result.
Check for the following problems and report them by adding their code to the issue_codes input param of the `report_review` tool:
- MISSING_CHANGES: not all changes requested with `{self.edit_tool.name}` tool call were made
- EXTRA_CHANGES: changes were made that were not requested with `{self.edit_tool.name}` tool call. Ignore any whitespace or empty line changes.
- DOCSTRING_REMOVED: docstrings were removed when they should have been kept.
- EXISTING_COMMENTS_REMOVED: existing comments were removed when they should have been kept.

If no problems are found, call the `report_review` tool with review_passed set to true.
If problems are found, call the `report_review` tool with review_passed set to false and add the codes of the problems found to the issue_codes input param.
Add a detailed description of the problems found to the review_details input param of the `report_review` tool.

These are the changes you need to review provided as a diff:
```diff
{diff}
```

"""
        diff_review_tool = ReportReviewTool(self.tool_call_logger)
        if not self.reuse_dialog_messages:
            dialog_messages = copy.deepcopy(dialog_messages)

        dialog_messages.add_user_prompt(
            diff_review_prompt,
            allow_append_to_tool_call_results=True,
        )
        response, metadata = self.client.generate(
            messages=dialog_messages.get_messages_for_llm_client(),
            max_tokens=self.max_output_tokens,
            tools=[diff_review_tool.get_tool_param()],
            tool_choice={"type": "tool", "name": ReportReviewTool.name},
        )
        dialog_messages.add_model_response(response)
        call_tools(
            tools=[diff_review_tool],
            calls_to_make=dialog_messages.get_pending_tool_calls(),
            dialog_messages=dialog_messages,
        )

        # placeholder_comments_review_result = self.check_for_placeholder_comments(diff)
        # diff_review_tool.merge_results(placeholder_comments_review_result)
        # change_comments_review_result = self.check_for_change_comments(
        #     diff, dialog_messages
        # )
        # diff_review_tool.merge_results(change_comments_review_result)

        self.diff_review_results.append(diff_review_tool.review_result)
        passed = (
            diff_review_tool.review_result
            and diff_review_tool.review_result["review_passed"]
        )
        if passed:
            self.num_verify_passed += 1
        else:
            self.num_verify_failed += 1
        return passed

    def gen_verify_passed_message(self) -> str:
        return f"""
Continue editing the file using {self.edit_tool.name} tool.
If you are done editing, call the {self.complete_tool.name} tool.
"""

    def gen_verify_failed_message(self) -> str:
        return f"""
Fix issues found during the review using {self.edit_tool.name} tool.
"""

    def do_final_review(
        self,
        tool_input: dict[str, Any],
        abs_path: Path,
        original_file_content: str,
        main_dialog_messages: DialogMessages,
    ):
        path = Path(tool_input["file_path"])
        edited_file_content = abs_path.read_text()
        edit_description = tool_input["short_edit_description"]
        diff = compute_file_diff(
            before_file=File(path=str(path), contents=original_file_content),
            after_file=File(path=str(path), contents=edited_file_content),
            same_line_smart_header=True,
            num_context_lines=10,
        )

        analysis_prompt = f"""\
Please review the changes made to the file {tool_input["file_path"]}.
You will be given following inputs:
- full original file content in <original_file_content> tag
- detailed edit description in <edit_description> tag
- full edited file content in <edited_file_content> tag
- diff showing the changes made in <diff> tag
Your task is to verify that the edited file content correctly implements the detailed edit description.

First go through the provided diff and split it into logical hunks. Do not skip hunks even if they only contain formatting or stylistic changes. Describe each hunk in a few words.

Second, go through each hunk and do the following:
- Repeat the hunk exactly including all the context lines
- Find the place in the edited file where the change happened and describe it in a few words(which class/funtion/section)
- If it's a modification
    - find the corresponding code in the original file and repeat it exactly.
    - find the corresponding code in the edited file and repeat it exactly.
- Find corresponding instructions or code block in the edit description that justify this change and repeat them exactly and in full including any
details
- If there are no corresponding instructions or code block clearly state that
- Analyze the changes made and the corresponding instructions or code block.
- Verify that the changes made were explicitly asked for in the edit description or match provided code block exactly.
- If the change is adding new code make sure it was added to the right place as specified in the edit description.
- Look for issues like unintentional code duplication or removal, corrupted lines
- Even if the changes make sense and align with the edit description intent but were not explicitly mentioned in the edit description or code block
report it as a problem.
- Difference in indentation or empty lines are not considered a problem and can be ignored
- Only focus on verifying that the changes match the edit description. Do not report any other problems, errors or potential issues with the code
itself.
- Use the following structure for each hunk analysis. Make sure all the tags are present:
<hunk_analysis>
    <hunk>
    </hunk>
    <original_code>
    </original_code>
    <modified_code>
    </modified_code>
    <corresponding_edit_description>
    </corresponding_edit_description>
    <corresponding_edit_description_code_block>
    </corresponding_edit_description_code_block>
    <location_in_modified_file>
        <after_this_line></after_this_line>
        <in_this_section></in_this_section>
        <after_this_function></after_this_function>
    </location_in_modified_file>
    <expected_location_from_edit_description>
        <after_this_line></after_this_line>
        <in_this_section></in_this_section>
        <after_this_function></after_this_function>
    </expected_location_from_edit_description>
    <analysis>
    </analysis>
</hunk_analysis>

Second go through each instruction in the edit description
- Copy the instruction exactly
- Find corresponding changes in the diff and repeat them exactly
- If there is no corresponding change clearly state that

Third put the detailed analysis result in <analysis_result> tag.

Here are the inputs:
<original_file_content>
{original_file_content}
</original_file_content>

<edit_description>
{edit_description}
</edit_description>

<edited_file_content>
{edited_file_content}
</edited_file_content>

<diff>
{diff}
</diff>
"""

        report_prompt_template = """
You are given the result of the analysis of changes made to a file.
The goal of the analysis was to verify that the edited file content correctly implements the detailed edit description.
Review the analysis and use `report_review` tool to report the review result.
Check for the following problems and report them by adding their code to the issue_codes input param of the `report_review` tool.
Problems are grouped into errors and warnings.
Errors are more severe than warnings and should cause the review to fail.
Warnings are less severe than errors and should not cause the review to fail.
- ERROR_MISSING_CHANGES: not all changes from the detailed edit description were made in the edited file content
- ERROR_EXTRA_MAJOR_CHANGES: changes were made that were not requested in the detailed edit description that are substantial and not just formatting or stylistic changes.
- WARNING_EXTRA_MINOR_CHANGES: changes were made that were not requested in the detailed edit description that are minor and are just formatting or stylistic changes.
- ERROR_CHANGE_NOT_WHERE_EXPECTED: changes were made in the wrong place as specified in the edit description. This is an error if the change location affects the functionality of the code.
- ERROR_ADDITION_NOT_WHERE_EXPECTED: new code was added in the wrong place as specified in the edit description. This is an error if the new code wrong location DOES affect the functionality of the code.
- WARNING_ADDITION_NOT_WHERE_EXPECTED: new code was added in the wrong place as specified in the edit description. This is a warning if the new code wrong location DOES NOT affect the functionality of the code.
- ERROR_DOCSTRING_REMOVED: docstrings were removed when they should have been kept.
- ERROR_EXISTING_COMMENTS_REMOVED: existing comments were removed when they should have been kept.
- ERROR_DUPLICATED_CODE: duplicated code was added when it should not have been.
- ERROR_CORRUPTED_CODE: code looks corrupted after the changes.

If no problems are found or only warnings are found, call the `report_review` tool with review_passed set to true.
If at least one error is found, call the `report_review` tool with review_passed set to false
Add the codes of the problems found to the issue_codes input param.
Add a detailed description of the problems found to the review_details input param of the `report_review` tool.
If no errors or warnings are found then review_details should be empty.
If <analysis_result/> tag is empty, call the `report_review` tool with review_passed set to false and add the code "ANALYSIS_RESULT_MISSING" to the issue_codes input param.

<analysis_result>
{analysis_result}
</analysis_result>
"""
        dialog_messages = DialogMessages()

        dialog_messages.add_user_prompt(
            analysis_prompt,
            allow_append_to_tool_call_results=True,
        )
        response, metadata = self.client.generate(
            messages=dialog_messages.get_messages_for_llm_client(),
            max_tokens=self.max_output_tokens,
        )
        dialog_messages.add_model_response(response)

        analysis_result = extract_xml_content(
            text=response[0].text,
            tag_name="analysis_result",
        )
        report_prompt = report_prompt_template.format(analysis_result=analysis_result)
        report_dialog_messages = DialogMessages()
        report_dialog_messages.add_user_prompt(report_prompt)
        dialog_messages.add_user_prompt(report_prompt)

        final_review_tool = ReportReviewTool(self.tool_call_logger)
        response, metadata = self.client.generate(
            messages=report_dialog_messages.get_messages_for_llm_client(),
            max_tokens=self.max_output_tokens,
            tools=[final_review_tool.get_tool_param()],
            tool_choice={"type": "tool", "name": ReportReviewTool.name},
        )
        dialog_messages.add_model_response(response)
        call_tools(
            tools=[final_review_tool],
            calls_to_make=dialog_messages.get_pending_tool_calls(),
            dialog_messages=dialog_messages,
        )

        placeholder_comments_review_result = self.check_for_placeholder_comments(diff)
        final_review_tool.merge_results(placeholder_comments_review_result)

        change_comments_review_result = self.check_for_change_comments(
            diff, dialog_messages
        )
        final_review_tool.merge_results(change_comments_review_result)

        if main_dialog_messages.is_assistant_turn():
            main_dialog_messages.add_model_response(
                [TextResult(text="Placeholder model response")]
            )
        main_dialog_messages._message_lists.extend(dialog_messages._message_lists)

        self.final_review_stage_result = final_review_tool.review_result
        return final_review_tool.review_result

    def run_impl(
        self,
        tool_input: dict[str, Any],
        dialog_messages: Optional[DialogMessages] = None,
    ) -> ToolImplOutput:
        assert dialog_messages is not None

        # clear stats
        self.num_wrong_tool_calls = 0
        self.num_wrong_tool_inputs = 0
        self.num_failed_edits = 0
        self.num_successful_edits = 0

        self.complete_tool = CompleteTool(self.tool_call_logger)
        tools = [self.edit_tool, self.complete_tool]
        tool_params = [tool.get_tool_param() for tool in tools]

        path = Path(tool_input["file_path"])
        abs_path = self.workspace_manager.root / path

        if not abs_path.exists():
            abs_path.parent.mkdir(parents=True, exist_ok=True)
            abs_path.touch()

        indentation_type = None
        if self.normalize_indentation:
            indentation_type = detect_indent_type(abs_path.read_text())
            if indentation_type is not None and indentation_type.is_mixed:
                indentation_type = None
            if indentation_type is not None:
                try:
                    normalized_content = normalize_indent(
                        abs_path.read_text(), indentation_type
                    )
                    abs_path.write_text(normalized_content)
                except AssertionError:
                    indentation_type = None

        target_file_content = abs_path.read_text()
        self.prev_file_content_version = target_file_content

        if not self.reuse_dialog_messages:
            dialog_messages = copy.deepcopy(dialog_messages)

        # This element is a call to EditFileAgent
        tool_call = dialog_messages.get_pending_tool_calls()[-1]

        dialog_messages.add_tool_call_result(
            ToolCallParameters(
                tool_call_id=tool_call.tool_call_id,
                tool_name=tool_call.tool_name,
                tool_input=tool_call.tool_input,
            ),
            "You are given a task to edit a file.",
        )

        # Call retriever tool before starting the editing process
        if self.retriever_tool is not None:
            # High level retrieval
            general_retriever_prompt = self._gen_retriever_prompt_general(tool_input)
            dialog_messages.add_user_prompt(
                general_retriever_prompt, allow_append_to_tool_call_results=True
            )
            response, metadata = self.client.generate(
                messages=dialog_messages.get_messages_for_llm_client(),
                max_tokens=self.max_output_tokens,
                tools=[self.retriever_tool.get_tool_param()],
                tool_choice={"type": "auto"},
            )
            dialog_messages.add_model_response(response)
            pending_tool_calls = dialog_messages.get_pending_tool_calls()
            if len(pending_tool_calls) > 0:
                call_tools(
                    tools=[self.retriever_tool],
                    calls_to_make=pending_tool_calls,
                    dialog_messages=dialog_messages,
                )

            # Specific retrieval
            specific_retriever_prompt = self._gen_retriever_prompt_specific(tool_input)
            dialog_messages.add_user_prompt(
                specific_retriever_prompt, allow_append_to_tool_call_results=True
            )
            response, metadata = self.client.generate(
                messages=dialog_messages.get_messages_for_llm_client(),
                max_tokens=self.max_output_tokens,
                tools=[self.retriever_tool.get_tool_param()],
                tool_choice={"type": "auto"},
            )
            dialog_messages.add_model_response(response)
            pending_tool_calls = dialog_messages.get_pending_tool_calls()
            if len(pending_tool_calls) > 0:
                call_tools(
                    tools=[self.retriever_tool],
                    calls_to_make=pending_tool_calls,
                    dialog_messages=dialog_messages,
                )

        if self.review_inputs:
            self.do_review_inputs(
                tool_input=tool_input,
                file_content=target_file_content,
                dialog_messages=dialog_messages,
            )

        edit_start_time = time.time()
        self.edit_latency = None
        dialog_messages.add_user_prompt(
            self._gen_edit_prompt(
                tool_input=tool_input,
                target_file_content=target_file_content,
            ),
            allow_append_to_tool_call_results=True,
        )

        # Initial edit stage
        response, metadata = self.client.generate(
            messages=dialog_messages.get_messages_for_llm_client(),
            max_tokens=self.max_output_tokens,
            tools=tool_params,
            system_prompt=self._gen_system_prompt(),
            # auto to allow model to think before calling a tool
            tool_choice={"type": "auto"},
        )
        self.num_input_tokens += metadata["input_tokens"]
        self.num_output_tokens += metadata["output_tokens"]
        dialog_messages.add_model_response(response)
        remaining_turns = self.max_turns
        while remaining_turns > 0:
            remaining_turns -= 1

            # Get pending tool calls
            pending_tool_calls = dialog_messages.get_pending_tool_calls()

            # Check if any tool is not available
            tries_to_call_unavailable_tool = False
            for tool_call in pending_tool_calls:
                if tool_call.tool_name not in [t.name for t in tools]:
                    error_message = (
                        f"Error: Tool '{tool_call.tool_name}' is not available. "
                        f"Please only use the following tools: {', '.join(t.name for t in tools)}"
                    )
                    dialog_messages.add_tool_call_result(tool_call, error_message)
                    tries_to_call_unavailable_tool = True
                    self.num_wrong_tool_calls += 1
                    break
            if tries_to_call_unavailable_tool:
                response, metadata = self.client.generate(
                    messages=dialog_messages.get_messages_for_llm_client(),
                    max_tokens=self.max_output_tokens,
                    system_prompt=self._gen_system_prompt(),
                    tools=tool_params,
                )
                self.num_input_tokens += metadata["input_tokens"]
                self.num_output_tokens += metadata["output_tokens"]
                dialog_messages.add_model_response(response)
                continue

            if len(pending_tool_calls) == 0:
                dialog_messages.add_user_prompt(
                    f"Please call the `{self.edit_tool.name}` tool to edit the file or call the `complete` tool to end the task."
                )
                response, metadata = self.client.generate(
                    messages=dialog_messages.get_messages_for_llm_client(),
                    max_tokens=self.max_output_tokens,
                    system_prompt=self._gen_system_prompt(),
                    tools=tool_params,
                )
                self.num_input_tokens += metadata["input_tokens"]
                self.num_output_tokens += metadata["output_tokens"]
                dialog_messages.add_model_response(response)
                continue

            pending_tool_calls = self._post_process_tool_calls(pending_tool_calls)
            tool_outputs = call_tools(
                tools=tools,
                calls_to_make=pending_tool_calls,
                dialog_messages=dialog_messages,
            )

            if self.complete_tool.should_stop:
                self.success = True
                self.edit_latency = time.time() - edit_start_time
                if self.final_review_stage:
                    self.do_final_review(
                        tool_input=tool_input,
                        abs_path=abs_path,
                        original_file_content=target_file_content,
                        main_dialog_messages=dialog_messages,
                    )
                if indentation_type is not None:
                    abs_path.write_text(
                        apply_indent_type(abs_path.read_text(), indentation_type)
                    )
                return ToolImplOutput(
                    tool_output=self.complete_tool.answer,
                    tool_result_message=f"File {path} edited",
                    auxiliary_data=self._gen_aux_data(),
                )

            logged_tool_call = self.tool_call_logger.logged_calls[-1]
            assert isinstance(logged_tool_call, LoggedToolCall)
            assert logged_tool_call.tool.name == self.edit_tool.name
            edit_tool_success = logged_tool_call.auxiliary_data.get("success", False)

            if edit_tool_success:
                self.num_successful_edits += 1
                passed = True
                if self.review_stage:
                    passed = self.do_diff_review(
                        tool_input=tool_input,
                        dialog_messages=dialog_messages,
                        abs_path=abs_path,
                        edit_tool_input=logged_tool_call.tool_input,
                    )
                if passed:
                    message = self.gen_verify_passed_message()
                    # tool_choice = {"type": "any"}
                else:
                    message = self.gen_verify_failed_message()
                    # tool_choice = {"type": "tool", "name": self.edit_tool.name}

            else:
                self.num_failed_edits += 1
                message = self._gen_failed_edit_prompt(
                    tool_input=tool_input,
                    tool_outputs=tool_outputs,
                )
                # tool_choice = {"type": "tool", "name": self.edit_tool.name}

            if "Invalid tool input" in tool_outputs[-1]:
                self.num_wrong_tool_inputs += 1

            dialog_messages.add_user_prompt(
                message=message,
                allow_append_to_tool_call_results=True,
            )
            response, metadata = self.client.generate(
                messages=dialog_messages.get_messages_for_llm_client(),
                max_tokens=self.max_output_tokens,
                system_prompt=self._gen_system_prompt(),
                tools=tool_params,
                # tool_choice=tool_choice,
                tool_choice={"type": "auto"},
            )
            self.num_input_tokens += metadata["input_tokens"]
            self.num_output_tokens += metadata["output_tokens"]
            dialog_messages.add_model_response(response)

        agent_answer = "Edit agent did not complete after max turns"
        self.success = False
        self.edit_latency = time.time() - edit_start_time
        if indentation_type is not None:
            abs_path.write_text(
                apply_indent_type(abs_path.read_text(), indentation_type)
            )
        return ToolImplOutput(
            tool_output=agent_answer,
            tool_result_message=agent_answer,
            auxiliary_data=self._gen_aux_data(),
        )

    def get_tool_start_message(self, tool_input: dict[str, Any]) -> str:
        return f"Editing file {tool_input['file_path']}"
