from textwrap import dedent
from experimental.guy.agent_qa.builtin_tools import FileEditClient
from experimental.guy.agent_qa.file_edit.edit_file_agent import GenericEditFileAgent
from experimental.guy.agent_qa.file_edit.udiff import apply_udiff

from pathlib import Path
from typing import Any, Optional


from experimental.guy.agent_qa.workspace_manager import (
    WorkspaceManager,
)
from research.agents.tools import (
    DialogMessages,
    LLMTool,
    ToolCallLogger,
    ToolImplOutput,
)
from research.llm_apis.llm_client import LLMClient


class ForgerUdiffEditFileAgent(GenericEditFileAgent):
    """An agent that edits files by generating udiffs
    and then applying them using Forger trained on udiffs"""

    def __init__(
        self,
        client: LLMClient,
        tool_call_logger: ToolCallLogger,
        workspace_manager: WorkspaceManager,
        file_edit_client: FileEditClient,
        max_output_tokens_per_turn: int,
        max_turns: int = 50,
        review_stage: bool = False,
        reuse_dialog_messages: bool = False,
        review_inputs: bool = False,
        final_review_stage: bool = False,
        normalize_indentation: bool = False,
    ):
        super().__init__(
            client=client,
            tool_call_logger=tool_call_logger,
            workspace_manager=workspace_manager,
            edit_tool=ApplyUdiffWithForgerTool(
                tool_call_logger, workspace_manager, file_edit_client
            ),
            max_output_tokens_per_turn=max_output_tokens_per_turn,
            max_turns=max_turns,
            review_stage=review_stage,
            reuse_dialog_messages=reuse_dialog_messages,
            review_inputs=review_inputs,
            final_review_stage=final_review_stage,
            normalize_indentation=normalize_indentation,
        )

    def _gen_edit_prompt(
        self, tool_input: dict[str, Any], target_file_content: str
    ) -> str:
        return f"""\
Edit the file {tool_input["file_path"]} according to the following instructions:

<short_edit_description>
```
{tool_input["short_edit_description"]}
```
</short_edit_description>

Use `apply_udiff` tool to make the changes. The tool requires:
1. file_path - the path to the file to edit
2. udiff - the unified diff to apply to the file
Break changes into small chunks and use `apply_udiff` to apply one chunk at a time

Here is the full contents of the file:

<file_content>
```
{target_file_content}
```
</file_content>
"""


class ApplyUdiffWithForgerTool(LLMTool):
    """A tool that applies a unified diff to a file using Forger"""

    name = "apply_udiff"
    description = "Apply a unified diff (udiff) to a file"
    input_schema = {
        "type": "object",
        "properties": {
            "file_path": {
                "type": "string",
                "description": "Path to the file to modify",
            },
            "udiff": {
                "type": "string",
                "description": "The unified diff to apply",
            },
        },
        "required": ["file_path", "udiff"],
    }

    def __init__(
        self,
        tool_call_logger: ToolCallLogger,
        workspace_manager: WorkspaceManager,
        file_edit_client: FileEditClient,
    ):
        super().__init__(tool_call_logger)
        self.workspace_manager = workspace_manager
        self.file_edit_client = file_edit_client

    def run_impl(
        self,
        tool_input: dict[str, Any],
        dialog_messages: Optional[DialogMessages] = None,
    ) -> ToolImplOutput:
        path = Path(tool_input["file_path"])
        abs_path = self.workspace_manager.root / path

        if not abs_path.exists():
            tool_output = f"File {path} does not exist, cannot edit"
            return ToolImplOutput(tool_output, tool_output, {"success": False})

        target_file_content = abs_path.read_text()

        try:
            new_content, request_id = self.file_edit_client.edit_file(
                target_file_path=tool_input["file_path"],
                target_file_content=target_file_content,
                edit_plan="",
                code_block=tool_input["udiff"],
            )
        except Exception as exc:
            return ToolImplOutput(
                f"Failed to edit file: {exc}",
                "Failed to edit file.",
                {"success": False},
            )

        abs_path.write_text(new_content)
        self.workspace_manager.update()

        tool_output = f"File {path} edited, request_id={request_id}"

        return ToolImplOutput(tool_output, tool_output, {"success": True})

    def get_tool_start_message(self, tool_input: dict[str, Any]) -> str:
        return f"Applying udiff to {tool_input['file_path']}"
