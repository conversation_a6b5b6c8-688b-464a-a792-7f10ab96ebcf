from typing import Any


def extract_str_replace_entries(tool_input: dict[str, Any]) -> list[dict[str, Any]]:
    entries = []

    # Check for the first entry (no index)
    if "old_str" in tool_input and "new_str" in tool_input:
        entry = {
            "old_str": tool_input["old_str"],
            "new_str": tool_input["new_str"],
        }
        if "old_str_start_line_number" in tool_input:
            entry["old_str_start_line_number"] = tool_input["old_str_start_line_number"]
        if "old_str_end_line_number" in tool_input:
            entry["old_str_end_line_number"] = tool_input["old_str_end_line_number"]
        entries.append(entry)

    # Check for additional entries with indices
    index = 1
    while f"old_str_{index}" in tool_input and f"new_str_{index}" in tool_input:
        entry = {
            "old_str": tool_input[f"old_str_{index}"],
            "new_str": tool_input[f"new_str_{index}"],
        }
        if f"old_str_start_line_number_{index}" in tool_input:
            entry["old_str_start_line_number"] = tool_input[
                f"old_str_start_line_number_{index}"
            ]
        if f"old_str_end_line_number_{index}" in tool_input:
            entry["old_str_end_line_number"] = tool_input[
                f"old_str_end_line_number_{index}"
            ]
        entries.append(entry)
        index += 1

    return entries


def extract_insert_line_entries(tool_input: dict[str, Any]) -> list[dict[str, Any]]:
    entries = []

    # Check for the first entry (no index)
    if "insert_line" in tool_input and "new_str" in tool_input:
        entries.append(
            {
                "insert_line": tool_input["insert_line"],
                "new_str": tool_input["new_str"],
            }
        )

    # Check for additional entries with indices
    index = 1
    while f"insert_line_{index}" in tool_input and f"new_str_{index}" in tool_input:
        entries.append(
            {
                "insert_line": tool_input[f"insert_line_{index}"],
                "new_str": tool_input[f"new_str_{index}"],
            }
        )
        index += 1

    return entries
