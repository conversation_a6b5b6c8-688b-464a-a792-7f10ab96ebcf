"""Tests for LineReplaceEditorTool."""

import os
import tempfile
from pathlib import Path
from unittest.mock import Magic<PERSON>ock

import pytest

from experimental.guy.agent_qa.file_edit.line_replace_editor_tool import (
    LineReplaceEditorTool,
)
from experimental.guy.agent_qa.workspace_manager import WorkspaceManagerImpl


@pytest.fixture
def test_file():
    """Create a temporary test file."""
    with tempfile.NamedTemporaryFile(mode="w", delete=False) as f:
        f.write("line 1\nline 2\nline 3\nline 4\nline 5\n")
    yield Path(f.name)
    os.unlink(f.name)


@pytest.fixture
def test_file_with_indentation():
    """Create a temporary test file with indented content."""
    with tempfile.NamedTemporaryFile(mode="w", delete=False) as f:
        f.write("def test():\n    line 1\n    line 2\n    line 3\nline 4\n")
    yield Path(f.name)
    os.unlink(f.name)


@pytest.fixture
def editor():
    """Create a LineReplaceEditorTool instance."""
    tool_call_logger = MagicMock()
    workspace_manager = WorkspaceManagerImpl(
        augment_client=MagicMock(),
        root=Path(tempfile.mkdtemp()),
    )
    return LineReplaceEditorTool(
        tool_call_logger=tool_call_logger, workspace_manager=workspace_manager
    )


def test_str_replace_basic(editor, test_file):
    """Test basic line replacement."""
    result = editor.str_replace(test_file, 2, 3, "new line 2\nnew line 3\n")
    assert result.success
    with open(test_file) as f:
        content = f.read()
    assert content == "line 1\nnew line 2\nnew line 3\nline 4\nline 5\n"


def test_str_replace_single_line(editor, test_file):
    """Test replacing a single line."""
    result = editor.str_replace(test_file, 3, 3, "new line 3\n")
    assert result.success
    with open(test_file) as f:
        content = f.read()
    assert content == "line 1\nline 2\nnew line 3\nline 4\nline 5\n"


def test_str_replace_invalid_start_line(editor, test_file):
    """Test error when start_line is beyond file length."""
    with pytest.raises(Exception) as exc:
        editor.str_replace(test_file, 10, 11, "new line\n")
    assert "beyond the end of file" in str(exc.value)


def test_str_replace_end_line_beyond_file(editor, test_file):
    """Test handling when end_line is beyond file length."""
    result = editor.str_replace(test_file, 4, 10, "new line 4\nnew line 5\n")
    assert result.success
    with open(test_file) as f:
        content = f.read()
    assert content == "line 1\nline 2\nline 3\nnew line 4\nnew line 5\n"


def test_str_replace_invalid_line_range(editor, test_file):
    """Test error when start_line is greater than end_line."""
    with pytest.raises(Exception) as exc:
        editor.str_replace(test_file, 3, 2, "new line\n")
    assert "end_line must be >= start_line" in str(exc.value)


def test_str_replace_start_line_zero(editor, test_file):
    """Test error when start_line is zero."""
    with pytest.raises(Exception) as exc:
        editor.str_replace(test_file, 0, 1, "new line\n")
    assert "start_line must be >= 1" in str(exc.value)


def test_str_replace_undo(editor, test_file):
    """Test that undo functionality works."""
    original_content = test_file.read_text()

    # Make a change
    editor.str_replace(test_file, 2, 3, "new content\n")
    modified_content = test_file.read_text()
    assert modified_content != original_content

    # Undo the change
    editor.undo_edit(test_file)
    restored_content = test_file.read_text()
    assert restored_content == original_content


def test_str_replace_empty_new_str(editor, test_file):
    """Test replacing lines with empty string."""
    result = editor.str_replace(test_file, 2, 3, "")
    assert result.success
    with open(test_file) as f:
        content = f.read()
    assert content == "line 1\nline 4\nline 5\n"


def test_str_replace_with_tabs(editor, test_file):
    """Test that tabs are handled correctly."""
    result = editor.str_replace(test_file, 2, 2, "\tnew line 2\n")
    assert result.success
    with open(test_file) as f:
        content = f.read()
    print(f"Content: {repr(content)}")
    # Check if tab is preserved or expanded based on editor settings
    expected = "line 1\n\tnew line 2\nline 3\nline 4\nline 5\n"
    if editor.expand_tabs:
        expected = expected.expandtabs()
    print(f"Expected: {repr(expected)}")
    assert content == expected
