import pytest
from experimental.guy.agent_qa.file_edit.file_edit_utils import (
    extract_xml_content,
    fuzzy_compare_diffs,
)


@pytest.mark.parametrize(
    "input_text,tag_name,expected",
    [
        # Basic case
        ("<test>content</test>", "test", "content"),
        # With whitespace
        ("<test>  content  </test>", "test", "content"),
        # Multi-line content
        (
            """<test>
        multi
        line
        content
    </test>""",
            "test",
            "multi\n        line\n        content",
        ),
        # Empty content
        ("<test></test>", "test", ""),
        ("<test>   </test>", "test", ""),
        # Different tag names
        ("<data>123</data>", "data", "123"),
        ("<xml>test</xml>", "xml", "test"),
        # Content with special characters
        ("<test>!@#$%^&*()</test>", "test", "!@#$%^&*()"),
        # Content with XML-like characters
        ("<test>contains < and > symbols</test>", "test", "contains < and > symbols"),
        # Tag not found cases
        ("no tags here", "test", None),
        ("<wrong>content</wrong>", "test", None),
        # Nested tags (should only match outer)
        ("<test><inner>nested</inner></test>", "test", "<inner>nested</inner>"),
        # Multiple occurrences (should match first)
        ("<test>first</test><test>second</test>", "test", "first"),
    ],
)
def test_extract_xml_content(input_text, tag_name, expected):
    result = extract_xml_content(input_text, tag_name)
    assert result == expected


def test_extract_xml_content_with_none_input():
    with pytest.raises(AttributeError):
        extract_xml_content(None, "test")


def test_extract_xml_content_with_none_tag():
    with pytest.raises(TypeError):
        extract_xml_content("<test>content</test>", None)


def test_extract_xml_content_with_empty_input():
    assert extract_xml_content("", "test") is None


def test_extract_xml_content_with_empty_tag():
    assert extract_xml_content("<test>content</test>", "") is None


def test_extract_xml_content_with_malformed_xml():
    # Missing closing tag
    assert extract_xml_content("<test>content", "test") is None
    # Missing opening tag
    assert extract_xml_content("content</test>", "test") is None
    # Mismatched tags
    assert extract_xml_content("<test>content</wrong>", "test") is None


# TODO fix
# def test_fuzzy_compare_diffs_match():
#     diff1 = """\
# @@ -2,3 +3,4 @@
#  def hello():
# -    print('hello')
# +    print('hello world')
# +    print('how are you?')
# """
#     diff2 = """\
# @@ -1,3 +1,4 @@
#  def hello():
# -    print('hello')
# +    print('hello world')
# +    print('how are you?')
# """
#     assert fuzzy_compare_diffs(diff1, diff2)

#     diff1 += "     print('I am fine')\n"
#     assert fuzzy_compare_diffs(diff1, diff2)

#     diff1 = """\
# @@ -2,3 +3,4 @@ def hello():
# -    print('hello')
# +    print('hello world')
# +    print('how are you?')
# """
#     diff2 = """\
# @@ -1,3 +1,4 @@
#  def hello():
# -    print('hello')
# +    print('hello world')
# +    print('how are you?')
# """
#     assert fuzzy_compare_diffs(diff1, diff2)

#     diff1 = """\
# @@ -2,3 +3,4 @@ def hello():
# -    print('hello')
# +    print('hello world')
# +    print(
# +        'how are you?'
# +    )
# """
#     diff2 = """\
# @@ -1,3 +1,4 @@
#  def hello():
# -    print('hello')
# +    print('hello world')
# +    print('how are you?')
# """
#     assert fuzzy_compare_diffs(diff1, diff2)

#     diff1 = """\
# @@ -2,3 +3,4 @@ def hello():
# -    print('hello')
# +    print('hello world')
# +    print(
# +        'how are you?'
# +    )
# """
#     diff2 = """\
# @@ -1,3 +1,4 @@
#  def hello():
# -    print('hello')
#      print('hello world')
# +    print('how are you?')
# """
#     assert fuzzy_compare_diffs(diff1, diff2)


# def test_fuzzy_compare_diffs_mismatch():
#     diff1 = """\
# @@ -2,3 +3,4 @@
#  def hello():
# -    print('hello')
# +    print('hello world')
# +    print('how are you?')
# """
#     diff2 = """\
# @@ -1,3 +1,4 @@
#  def hello():
# -    print('hello')
# +    print('hello world')
# +    print('how are you doing?')
# """
#     assert not fuzzy_compare_diffs(diff1, diff2)

#     diff1 = """\
# @@ -2,3 +3,4 @@
#  def hello():
# -    print('hello')
# +    print('hello world')
# +    print('how are you?')
# """
#     diff2 = """\
# @@ -1,3 +1,4 @@
#  def hello():
# -    print('hello')
# +    print('how are you?')
# """
#     assert not fuzzy_compare_diffs(diff1, diff2)
