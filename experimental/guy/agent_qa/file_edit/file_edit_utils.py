import os
import re
from pathlib import Path


def is_path_in_directory(directory: Path, path: Path) -> bool:
    directory = directory.resolve()
    path = path.resolve()
    try:
        path.relative_to(directory)
        return True
    except ValueError:
        return False


def get_augment_token():
    env_names = ["AUGMENT_TOKEN", "AUGMENT_API_TOKEN"]
    for env_name in env_names:
        if env_name in os.environ:
            return os.environ[env_name].strip()
    raise ValueError(f"API token not found in environment variables: {env_names}")


def extract_xml_content(text: str, tag_name: str) -> str | None:
    """Extract text between specified XML tags.

    Args:
        text: Input text containing XML tags
        tag_name: Name of the XML tag to extract content from

    Returns:
        Extracted text between tags, or None if no match found

    Raises:
        AttributeError: If text is None
        TypeError: If tag_name is None
    """
    if text is None:
        raise AttributeError("text cannot be None")
    if tag_name is None:
        raise TypeError("tag_name cannot be None")

    pattern = rf"<{tag_name}>\s*(.*?)\s*</{tag_name}>"
    match = re.search(pattern, text, re.DOTALL)

    if match:
        return match.group(1).strip()
    return None


def calc_theoretical_latency(num_requests, num_input_tokens, num_output_tokens):
    """Calculate the theoretical latency for a given number of requests, input tokens, and output tokens."""
    requests_per_sec = 2
    input_tokens_per_sec = 1000
    output_tokens_per_sec = 60

    request_latency = num_requests / requests_per_sec
    input_latency = num_input_tokens / input_tokens_per_sec
    output_latency = num_output_tokens / output_tokens_per_sec

    return request_latency + input_latency + output_latency


def fuzzy_compare_diffs(diff1: str, diff2: str) -> bool:
    """Compare two diffs and return True if they are similar.
    Ignores empty lines
    Ignores mismatch between line numbers in diff headers
    Ignores changes in whitespace
    Handles different numbers of context lines intelligently
    """

    def normalize_line(line: str) -> str:
        # Remove leading/trailing whitespace
        line = line.strip()
        # Normalize internal whitespace
        parts = line.split()
        return " ".join(parts)

    def extract_changes(diff: str) -> tuple[list[str], list[str]]:
        # Split into lines and filter out empty lines
        lines = [line for line in diff.splitlines() if line.strip()]

        removals = []
        additions = []
        current_addition = []

        for line in lines:
            # Skip diff headers
            if line.startswith("@@") and "@@" in line:
                continue

            # Extract the actual content, ignoring leading spaces
            if line.startswith("-"):
                content = normalize_line(line[1:])
                if content:
                    removals.append(content)
            elif line.startswith("+"):
                content = normalize_line(line[1:])
                if content:
                    # If this line ends with an opening parenthesis or dot,
                    # it's likely part of a multi-line statement
                    if content.endswith(("(", ".")):
                        current_addition = [content]
                    # If we're collecting a multi-line statement and this isn't the end
                    elif current_addition and not content.endswith(")"):
                        current_addition.append(content)
                    # If this is the end of a multi-line statement
                    elif current_addition and content.endswith(")"):
                        current_addition.append(content)
                        additions.append(" ".join(current_addition))
                        current_addition = []
                    # Regular single-line addition
                    else:
                        if current_addition:
                            current_addition.append(content)
                            additions.append(" ".join(current_addition))
                            current_addition = []
                        else:
                            additions.append(content)

        # Handle any remaining multi-line statement
        if current_addition:
            additions.append(" ".join(current_addition))

        return removals, additions

    # Extract changes from both diffs
    removals1, additions1 = extract_changes(diff1)
    removals2, additions2 = extract_changes(diff2)

    # Compare the changes
    return removals1 == removals2 and additions1 == additions2
