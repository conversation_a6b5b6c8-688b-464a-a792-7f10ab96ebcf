from textwrap import dedent
from experimental.guy.agent_qa.builtin_tools import FileEditClient
from experimental.guy.agent_qa.file_edit.edit_file_agent import GenericEditFileAgent
from experimental.guy.agent_qa.file_edit.udiff import apply_udiff

from pathlib import Path
from typing import Any, Optional


from experimental.guy.agent_qa.workspace_manager import (
    WorkspaceManager,
)
from research.agents.tools import (
    DialogMessages,
    LLMTool,
    ToolCallLogger,
    ToolImplOutput,
)
from research.llm_apis.llm_client import LLMClient


class ForgerUdiffWithDescEditFileAgent(GenericEditFileAgent):
    """An agent that edits files by generating udiffs
    and then applying them using Forger trained on udiffs"""

    def __init__(
        self,
        client: LLMClient,
        tool_call_logger: ToolCallLogger,
        workspace_manager: WorkspaceManager,
        file_edit_client: FileEditClient,
        max_output_tokens_per_turn: int,
        max_turns: int = 50,
        review_stage: bool = False,
        reuse_dialog_messages: bool = False,
        review_inputs: bool = False,
        final_review_stage: bool = False,
        normalize_indentation: bool = False,
    ):
        super().__init__(
            client=client,
            tool_call_logger=tool_call_logger,
            workspace_manager=workspace_manager,
            edit_tool=ApplyUdiffDescWithForgerTool(
                tool_call_logger, workspace_manager, file_edit_client
            ),
            max_output_tokens_per_turn=max_output_tokens_per_turn,
            max_turns=max_turns,
            review_stage=review_stage,
            reuse_dialog_messages=reuse_dialog_messages,
            review_inputs=review_inputs,
            final_review_stage=final_review_stage,
            normalize_indentation=normalize_indentation,
        )

    def _gen_edit_prompt(
        self, tool_input: dict[str, Any], target_file_content: str
    ) -> str:
        return f"""\
You are an expert and very meticulous programmer. You are very detail oriented.
Your task is to edit the file {tool_input["file_path"]} according to the instructions given in <short_edit_description> xml tag.
Read the instructions very carefully and take into account all the details.
Before making changes carefully analyze the file content and the instructions.
Come up with a very detailed plan for the changes. Pay attention to every single detail in edit description.
When moving existing code from one place to another make sure to keep formatting as is.
Do not change anything that was not explicitly mentioned in the edit description.
Make only nessessary changes.
Keep formatting the same as in the original file.
Keep the original comments and docstrings.

Use `apply_udiff` tool to make the changes. The tool requires:
1. file_path - the path to the file to edit
2. udiff - the unified diff to apply to the file
3. short_edit_description - a short description of the edit to be made. 1-2 sentences.

Here are the edit instructions:

<short_edit_description>
```
{tool_input["short_edit_description"]}
```
</short_edit_description>


Here is the full contents of the file that needs to be edited:

<file_content>
```
{target_file_content}
```
</file_content>

Here are the edit instructions again:

<short_edit_description>
```
{tool_input["short_edit_description"]}
```
</short_edit_description>
"""

    def _gen_review_prompt(
        self, tool_input: dict[str, Any], tool_outputs: list[str], diff: str
    ) -> str:
        return f"""\
Here is the diff showing the changes made to {tool_input["file_path"]}:

<diff>
```
{diff}
```
</diff>

If the changes are satisfactory, call the complete tool to end the task.
If not, decide which edits are needed now, and call the `apply_udiff` tool again."""


class ApplyUdiffDescWithForgerTool(LLMTool):
    """A tool that applies a unified diff to a file using Forger"""

    name = "apply_udiff"
    description = "Apply a unified diff (udiff) to a file"
    input_schema = {
        "type": "object",
        "properties": {
            "file_path": {
                "type": "string",
                "description": "Path to the file to modify",
            },
            "udiff": {
                "type": "string",
                "description": "The unified diff to apply. Always include at least five lines of context around each change.",
            },
            "short_edit_description": {
                "type": "string",
                "description": "A short description of the edit to be made. 1-2 sentences.",
            },
        },
        "required": ["file_path", "udiff", "short_edit_description"],
    }

    def __init__(
        self,
        tool_call_logger: ToolCallLogger,
        workspace_manager: WorkspaceManager,
        file_edit_client: FileEditClient,
        verify_udiff: bool = True,
    ):
        super().__init__(tool_call_logger)
        self.workspace_manager = workspace_manager
        self.file_edit_client = file_edit_client
        self.verify_udiff = verify_udiff

    def run_impl(
        self,
        tool_input: dict[str, Any],
        dialog_messages: Optional[DialogMessages] = None,
    ) -> ToolImplOutput:
        path = Path(tool_input["file_path"])
        abs_path = self.workspace_manager.root / path

        if not abs_path.exists():
            tool_output = f"File {path} does not exist, cannot edit"
            return ToolImplOutput(tool_output, tool_output, {"success": False})

        target_file_content = abs_path.read_text()

        # hack to make Forger UDIFF work when removing code at the end of the file
        if target_file_content.strip():
            target_file_content += "\naugment_end_of_file"

        try:
            new_content, request_id = self.file_edit_client.edit_file(
                target_file_path=tool_input["file_path"],
                target_file_content=target_file_content,
                edit_plan=tool_input["short_edit_description"],
                code_block=tool_input["udiff"],
            )
            new_content = new_content.replace("\naugment_end_of_file", "")
            # TODO
            # if self.verify_udiff:

            #     result = apply_udiff(
            #         udiff=tool_input["udiff"],
            #         original_text=target_file_content,
            #     )

        except Exception as exc:
            return ToolImplOutput(
                f"Failed to edit file: {exc}",
                "Failed to edit file.",
                {"success": False},
            )

        abs_path.write_text(new_content)
        self.workspace_manager.update()

        tool_output = f"File {path} edited, request_id={request_id}"

        return ToolImplOutput(tool_output, tool_output, {"success": True})

    def get_tool_start_message(self, tool_input: dict[str, Any]) -> str:
        return f"Applying udiff to {tool_input['file_path']}"
