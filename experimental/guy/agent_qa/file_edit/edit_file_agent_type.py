from enum import Enum, auto

from base.augment_client.client import AugmentClient
from experimental.guy.agent_qa.builtin_tools import FileEditClient
from experimental.guy.agent_qa.file_edit.edit_file_agent import GenericEditFileAgent
from experimental.guy.agent_qa.file_edit.file_edit_utils import get_augment_token
from experimental.guy.agent_qa.file_edit.forger_edit_file_agent import (
    ForgerEditFileAgent,
)
from experimental.guy.agent_qa.file_edit.forger_udiff_edit_file_agent import (
    ForgerUdiffEditFileAgent,
)
from experimental.guy.agent_qa.file_edit.forger_udiff_with_desc_edit_file_agent import (
    ForgerUdiffWithDescEditFileAgent,
)
from experimental.guy.agent_qa.file_edit.line_replace_edit_file_agent import (
    LineReplaceEditFileAgent,
)
from experimental.guy.agent_qa.file_edit.str_replace_edit_file_agent import (
    StrReplaceEditFileAgent,
)
from experimental.guy.agent_qa.file_edit.str_replace_edit_file_agent_brief_old_str import (
    StrReplaceEditFileAgentBriefOldStr,
)
from experimental.guy.agent_qa.file_edit.str_replace_edit_file_agent_with_multiedit import (
    StrReplaceEditFileAgentWithMultiEdit,
)
from experimental.guy.agent_qa.file_edit.str_replace_editor_tool_no_view_range import (
    StrReplaceEditorToolNoViewRange,
)
from experimental.guy.agent_qa.file_edit.str_replace_editor_tool_with_multiedit_flat import (
    StrReplaceEditorToolWithMultiEditFlat,
)
from experimental.guy.agent_qa.file_edit.str_replace_editor_tool_with_multiedit_flat_no_i import (
    StrReplaceEditorToolWithMultiEditFlatNoI,
)
from experimental.guy.agent_qa.file_edit.udiff_edit_file_agent import (
    EditFileWithUDiffAgent,
)
from experimental.guy.agent_qa.prototyping_client import (
    get_dev_deployment_api_proxy_url,
    get_staging_api_proxy_url,
)
from experimental.guy.agent_qa.workspace_manager import WorkspaceManagerImpl
from research.agents.tools import ToolCallLogger
from research.llm_apis.llm_client import LLMClient

MAX_OUTPUT_TOKENS_PER_TURN = 8192


class EditFileAgentType(Enum):
    """Supported EditFileAgent implementations."""

    UDIFF = auto()
    FORGER_UDIFF = auto()
    FORGER_UDIFF_DESC = auto()
    FORGER_UDIFF_V3 = auto()
    FORGER_V1 = auto()
    FORGER_V2 = auto()
    FORGER_V3 = auto()
    STR_REPLACE = auto()
    STR_REPLACE_IGNORE_INDENTATION = auto()
    STR_REPLACE_MULTIEDIT = auto()
    STR_REPLACE_MULTIEDIT_FLAT = auto()
    STR_REPLACE_MULTIEDIT_FLAT_NO_I = auto()
    STR_REPLACE_MULTIEDIT_FLAT_NO_I_NO_OUTPUT_SNIPPETS = auto()
    STR_REPLACE_SUGGEST_OLD_STR = auto()
    STR_REPLACE_REPLACE_ALL = auto()
    STR_REPLACE_OLD_LINE_NUMBERS = auto()
    STR_REPLACE_BRIEF_OLD_STR = auto()
    STR_REPLACE_NO_VIEW_RANGE = auto()


def create_edit_agent(
    agent_type: EditFileAgentType,
    llm_client: LLMClient,
    tool_call_logger: ToolCallLogger,
    workspace_manager: WorkspaceManagerImpl,
    **kwargs,
) -> GenericEditFileAgent:
    """Create an instance of the specified EditFileAgent implementation."""
    common_params = {
        "client": llm_client,
        "tool_call_logger": tool_call_logger,
        "workspace_manager": workspace_manager,
        "max_output_tokens_per_turn": MAX_OUTPUT_TOKENS_PER_TURN,
        **kwargs,
    }

    if agent_type == EditFileAgentType.UDIFF:
        return EditFileWithUDiffAgent(**common_params)

    file_edit_client = None
    if (
        agent_type == EditFileAgentType.FORGER_V1
        or agent_type == EditFileAgentType.FORGER_V2
    ):
        augment_client = AugmentClient(get_staging_api_proxy_url(), get_augment_token())
        file_edit_client = FileEditClient(augment_client)

    elif agent_type in {
        EditFileAgentType.FORGER_UDIFF,
        EditFileAgentType.FORGER_UDIFF_DESC,
        EditFileAgentType.FORGER_UDIFF_V3,
        EditFileAgentType.FORGER_V3,
    }:
        augment_client = AugmentClient(
            get_dev_deployment_api_proxy_url(user_name="vpas"), get_augment_token()
        )
        model_map = {
            EditFileAgentType.FORGER_UDIFF: "forger-qwen-udiff-8b-32k-edit",
            EditFileAgentType.FORGER_UDIFF_DESC: "forger-qwen-udiff-cl-gt-wr-8b-32k-edit",
            EditFileAgentType.FORGER_UDIFF_V3: "forger-qwen-udiff-7b-v3-32k-edit",
            EditFileAgentType.FORGER_V3: "forger-v3-qwen-14b-inst-fr-32k-edit",
        }
        file_edit_client = FileEditClient(augment_client, model=model_map[agent_type])

    if file_edit_client:
        common_params["file_edit_client"] = file_edit_client

    agent_map = {
        EditFileAgentType.FORGER_UDIFF: ForgerUdiffEditFileAgent,
        EditFileAgentType.FORGER_UDIFF_DESC: ForgerUdiffWithDescEditFileAgent,
        EditFileAgentType.FORGER_UDIFF_V3: ForgerUdiffWithDescEditFileAgent,
        EditFileAgentType.FORGER_V1: lambda **params: ForgerEditFileAgent(
            **params, edit_tool_name="forger_v1"
        ),
        EditFileAgentType.FORGER_V2: lambda **params: ForgerEditFileAgent(
            **params, edit_tool_name="forger_v2"
        ),
        EditFileAgentType.FORGER_V3: lambda **params: ForgerEditFileAgent(
            **params, edit_tool_name="forger_v3"
        ),
        EditFileAgentType.STR_REPLACE: lambda **params: StrReplaceEditFileAgent(
            **params,
            tool_args=dict(),
        ),
        EditFileAgentType.STR_REPLACE_IGNORE_INDENTATION: lambda **params: StrReplaceEditFileAgent(
            **params,
            tool_args=dict(ignore_indentation_for_str_replace=True),
        ),
        EditFileAgentType.STR_REPLACE_MULTIEDIT: lambda **params: StrReplaceEditFileAgentWithMultiEdit(
            **params,
            tool_args=dict(),
        ),
        EditFileAgentType.STR_REPLACE_MULTIEDIT_FLAT: lambda **params: StrReplaceEditFileAgentWithMultiEdit(
            **params,
            tool_cls=StrReplaceEditorToolWithMultiEditFlat,
            tool_args=dict(),
        ),
        EditFileAgentType.STR_REPLACE_MULTIEDIT_FLAT_NO_I: lambda **params: StrReplaceEditFileAgentWithMultiEdit(
            **params,
            tool_cls=StrReplaceEditorToolWithMultiEditFlatNoI,
            tool_args=dict(),
        ),
        EditFileAgentType.STR_REPLACE_MULTIEDIT_FLAT_NO_I_NO_OUTPUT_SNIPPETS: lambda **params: StrReplaceEditFileAgentWithMultiEdit(
            **params,
            tool_cls=StrReplaceEditorToolWithMultiEditFlatNoI,
            tool_args=dict(
                show_output_snippets=False,
            ),
        ),
        EditFileAgentType.STR_REPLACE_OLD_LINE_NUMBERS: lambda **params: LineReplaceEditFileAgent(
            **params,
            tool_args=dict(),
        ),
        EditFileAgentType.STR_REPLACE_BRIEF_OLD_STR: lambda **params: StrReplaceEditFileAgentBriefOldStr(
            **params,
            tool_args=dict(),
        ),
        EditFileAgentType.STR_REPLACE_NO_VIEW_RANGE: lambda **params: StrReplaceEditFileAgent(
            **params,
            tool_cls=StrReplaceEditorToolNoViewRange,
            tool_args=dict(),
        ),
    }

    if agent_type not in agent_map:
        raise ValueError(f"Unsupported agent type: {agent_type}")

    return agent_map[agent_type](**common_params)


def get_agent_name(agent_type: EditFileAgentType) -> str:
    return f"{agent_type.name}_EditAgent"
