from textwrap import dedent
from typing import Any, Optional

from experimental.guy.agent_qa.file_edit.edit_file_agent import GenericEditFileAgent
from experimental.guy.agent_qa.file_edit.line_replace_editor_tool import (
    LineReplaceEditorTool,
    adjust_parallel_calls,
)
from experimental.guy.agent_qa.workspace_manager import WorkspaceManager
from research.agents.tools import LLMTool, Tool<PERSON>allLogger, ToolCallParameters
from research.llm_apis.llm_client import LLMClient


class LineReplaceEditFileAgent(GenericEditFileAgent):
    """An agent that edits files using line replacement operations.
    This agent uses LineReplaceEditorTool to make edits to files through str_replace and insert commands.
    """

    def __init__(
        self,
        client: LLMClient,
        tool_call_logger: <PERSON>l<PERSON>allLogger,
        workspace_manager: WorkspaceManager,
        max_output_tokens_per_turn: int,
        max_turns: int,
        review_stage: bool = False,
        reuse_dialog_messages: bool = False,
        review_inputs: bool = False,
        final_review_stage: bool = False,
        normalize_indentation: bool = False,
        retriever_tool: Optional[LLMTool] = None,
        tool_args: Optional[dict[str, Any]] = None,
    ):
        edit_tool = LineReplaceEditorTool(
            tool_call_logger=tool_call_logger,
            workspace_manager=workspace_manager,
            **(tool_args or {}),
        )
        super().__init__(
            client=client,
            tool_call_logger=tool_call_logger,
            workspace_manager=workspace_manager,
            edit_tool=edit_tool,
            max_output_tokens_per_turn=max_output_tokens_per_turn,
            max_turns=max_turns,
            review_stage=review_stage,
            reuse_dialog_messages=reuse_dialog_messages,
            review_inputs=review_inputs,
            final_review_stage=final_review_stage,
            normalize_indentation=normalize_indentation,
            retriever_tool=retriever_tool,
        )

    def _post_process_tool_calls(
        self, tool_calls: list[ToolCallParameters]
    ) -> list[ToolCallParameters]:
        return adjust_parallel_calls(tool_calls)

    def _gen_system_prompt(self) -> str:
        return dedent(
            """\
            You are an expert software engineer.
            Your task is to edit given file according to the given instructions using provided tools.
            IMPORTANT: Call as many tools as possible in a single function_calls block so that they can be executed in parallel.
            """
        )

    def _gen_edit_prompt(
        self, tool_input: dict[str, Any], target_file_content: str
    ) -> str:
        abs_path = self.workspace_manager.root / tool_input["file_path"]
        return f"""\
Edit the file {abs_path} according to the following instructions:
<short_edit_description>
```
{tool_input["short_edit_description"]}
```
</short_edit_description>
Call the supplied tool to perform the edit. You can use the following commands:
Make sure to:
- View the file before making any changes to understand the context and line numbers
- Be very precise when specifying the line numbers for the str_replace and insert commands
- Use insert when adding new content at a specific line
- When all edits are done use view command on full file to verify the changes
- If the changes are correct call the complete tool to end the task

IMPORTANT: Try to fit as many str_replace tool calls as possible into a single function_calls block so that they can be executed in parallel.
"""

    def _gen_review_prompt(
        self, tool_input: dict[str, Any], tool_outputs: list[str], diff: str
    ) -> str:
        return f"""\
Please review the changes made. Make sure that:
- The instructions were followed correctly
- No important related changes were missed
- No changes were made that weren't necessary from the instructions
- The string replacements were done correctly without unintended side effects
Here is the diff showing the changes made:
<diff>
```
{diff}
```
</diff>
If the changes are satisfactory, call the complete tool to end the task.
If not, decide which edits are needed now, and call the str_replace_editor tool again.
Remember to use str_replace only for exact matches and insert for new content.
"""

    def _format_edit_tool_input(self, tool_input: dict[str, Any]) -> str:
        """Format the edit tool input for display in error messages."""
        formatted = []
        for k, v in tool_input.items():
            if k == "file_path":
                continue
            formatted.append(f"""
<{k}>
```
{v}
```
</{k}>
""")
        return "\n".join(formatted)

    def _gen_retriever_prompt_general(self, tool_input: dict[str, Any]) -> str:
        return dedent(
            f"""\
            The task is to make an edit: {tool_input["short_edit_description"]}
            Call the request_codebase_information tool to gather information to help make the edit.
            You probably want to know more about the file being edited and the surrounding code around the edit location.
            """
        )

    def _gen_retriever_prompt_specific(self, tool_input: dict[str, Any]) -> str:
        return dedent(
            f"""\
            The task is to make an edit: {tool_input["short_edit_description"]}
            Now you should have a high level understanding of the code relating to the edit.
            In this round, you should request extremely specific, low level information relating to the edit.
            First, write down one by one ALL the symbols and objects that are related to this edit.
            Do not skip anything. Be comprehensive!
            For example, if the edit is to call a method in another class, ask for information about the class and the method.
            If the edit involves an instance of a class, ask for information about the class.
            When in any doubt, include the symbol or object.
            Then, call the request_codebase_information requesting information about each of them in a single request.
            """
        )
