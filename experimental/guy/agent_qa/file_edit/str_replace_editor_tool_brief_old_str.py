import asyncio
from pathlib import Path
from collections import defaultdict
from experimental.guy.agent_qa.file_edit.file_edit_utils import is_path_in_directory
from experimental.guy.agent_qa.file_edit.indent_utils import (
    match_indent,
    match_indent_by_first_line,
)
from experimental.guy.agent_qa.workspace_manager import WorkspaceManager
from research.agents.tools import (
    DialogMessages,
    LLMTool,
    ToolCallLogger,
    ToolCallParameters,
    ToolImplOutput,
)

from typing import Any, Literal, Optional, get_args
import logging

logger = logging.getLogger(__name__)

Command = Literal[
    "view",
    "create",
    "str_replace",
    "insert",
    "undo_edit",
]


def adjust_parallel_calls(
    tool_calls: list[ToolCallParameters],
) -> list[ToolCallParameters]:
    # sort by putting insert calls before str_replace calls
    # sort insert calls by line number
    tool_calls.sort(
        key=lambda x: (
            x.tool_input.get("command") != "insert",
            x.tool_input.get("insert_line", 0),
        )
    )

    # increment line numbers of insert calls after each insert call
    line_shift = 0
    for tool_call in tool_calls:
        if (
            tool_call.tool_input.get("command") == "insert"
            and "insert_line" in tool_call.tool_input
            and "new_str" in tool_call.tool_input
        ):
            tool_call.tool_input["insert_line"] += line_shift
            line_shift += len(tool_call.tool_input["new_str"].splitlines())
    return tool_calls


# Extend ToolImplOutput to add success property
class ExtendedToolImplOutput(ToolImplOutput):
    @property
    def success(self) -> bool:
        """Get success status from metadata."""
        return bool(self.auxiliary_data.get("success", False))


class ToolError(Exception):
    def __init__(self, message: str):
        self.message = message
        super().__init__(message)

    def __str__(self):
        return self.message


SNIPPET_LINES: int = 4

TRUNCATED_MESSAGE: str = "<response clipped><NOTE>To save on context only part of this file has been shown to you. You should retry this tool after you have searched inside the file with `grep -n` in order to find the line numbers of what you are looking for.</NOTE>"
# original value from Anthropic code
# MAX_RESPONSE_LEN: int = 16000
MAX_RESPONSE_LEN: int = 200000


def maybe_truncate(content: str, truncate_after: int | None = MAX_RESPONSE_LEN):
    """Truncate content and append a notice if content exceeds the specified length."""
    return (
        content
        if not truncate_after or len(content) <= truncate_after
        else content[:truncate_after] + TRUNCATED_MESSAGE
    )


async def run(
    cmd: str,
    timeout: float | None = 120.0,  # seconds
    truncate_after: int | None = MAX_RESPONSE_LEN,
):
    """Run a shell command asynchronously with a timeout."""
    process = await asyncio.create_subprocess_shell(
        cmd, stdout=asyncio.subprocess.PIPE, stderr=asyncio.subprocess.PIPE
    )

    try:
        stdout, stderr = await asyncio.wait_for(process.communicate(), timeout=timeout)
        return (
            process.returncode or 0,
            maybe_truncate(stdout.decode(), truncate_after=truncate_after),
            maybe_truncate(stderr.decode(), truncate_after=truncate_after),
        )
    except asyncio.TimeoutError as exc:
        try:
            process.kill()
        except ProcessLookupError:
            pass
        raise TimeoutError(
            f"Command '{cmd}' timed out after {timeout} seconds"
        ) from exc


def run_sync(*args, **kwargs):
    return asyncio.run(run(*args, **kwargs))


class StrReplaceEditorToolBriefOldStr(LLMTool):
    name = "str_replace_editor"
    min_lines_before_and_after_ellipsis = 5

    description = """\
Custom editing tool for viewing, creating and editing files\n
* State is persistent across command calls and discussions with the user\n
* If `path` is a file, `view` displays the result of applying `cat -n`. If `path` is a directory, `view` lists non-hidden files and directories up to 2 levels deep\n
* The `create` command cannot be used if the specified `path` already exists as a file\n
* If a `command` generates a long output, it will be truncated and marked with `<response clipped>` \n
* The `undo_edit` command will revert the last edit made to the file at `path`\n
\n
Notes for using the `str_replace` command:\n
* The `old_str` parameter should be in a line-numbered format (like output from `cat -n`), with each line starting with line number(padded to 6 digits) and tab\n
* For long sections, you can use '...' to match any number of lines between the first and last lines, e.g.:
```
  1234    def my_function():
...
  1238        return True
```
* If you use '...', make sure to include at least 5 lines before and after the '...' to ensure uniqueness\n
* The line numbers are used to locate the exact position in the file, so they must match the actual file\n
* The `new_str` parameter should contain the edited lines that should replace the `old_str` (without line numbers)
"""
    input_schema = {
        "type": "object",
        "properties": {
            "command": {
                "type": "string",
                "enum": ["view", "create", "str_replace", "insert", "undo_edit"],
                "description": "The commands to run. Allowed options are: `view`, `create`, `str_replace`, `insert`, `undo_edit`.",
            },
            "file_text": {
                "description": "Required parameter of `create` command, with the content of the file to be created.",
                "type": "string",
            },
            "insert_line": {
                "description": "Required parameter of `insert` command. The `new_str` will be inserted AFTER the line `insert_line` of `path`.",
                "type": "integer",
            },
            "new_str": {
                "description": "Required parameter of `str_replace` command containing the new string. Required parameter of `insert` command containing the string to insert.",
                "type": "string",
            },
            "old_str": {
                "description": "Required parameter of `str_replace` command containing the string in `path` to replace.",
                "type": "string",
            },
            "path": {
                "description": "Path to file or directory.",
                "type": "string",
            },
            "view_range": {
                "description": "Optional parameter of `view` command when `path` points to a file. If none is given, the full file is shown. If provided, the file will be shown in the indicated line number range, e.g. [11, 12] will show lines 11 and 12. Indexing at 1 to start. Setting `[start_line, -1]` shows all lines from `start_line` to the end of the file.",
                "items": {"type": "integer"},
                "type": "array",
            },
        },
        "required": ["command", "path"],
    }

    # Track file edit history for undo operations
    _file_history = defaultdict(list)

    def __init__(
        self,
        tool_call_logger: ToolCallLogger,
        workspace_manager: WorkspaceManager,
        ignore_indentation_for_str_replace: bool = False,
        expand_tabs: bool = False,
        min_lines_before_and_after_ellipsis: int = 5,
    ):
        super().__init__(tool_call_logger)
        self.workspace_manager = workspace_manager
        self.ignore_indentation_for_str_replace = ignore_indentation_for_str_replace
        self.expand_tabs = expand_tabs
        self.min_lines_before_and_after_ellipsis = min_lines_before_and_after_ellipsis
        self._file_history = defaultdict(list)

    def run_impl(
        self,
        tool_input: dict[str, Any],
        dialog_messages: Optional[DialogMessages] = None,
    ) -> ExtendedToolImplOutput:
        command = tool_input["command"]
        path = tool_input["path"]
        file_text = tool_input.get("file_text")
        view_range = tool_input.get("view_range")
        old_str = tool_input.get("old_str")
        new_str = tool_input.get("new_str")
        insert_line = tool_input.get("insert_line")

        try:
            _path = Path(path)
            if not _path.is_absolute():
                _path = self.workspace_manager.root / _path
            self.validate_path(command, _path)

            workspace_root = self.workspace_manager.root
            if not is_path_in_directory(workspace_root, _path):
                return ExtendedToolImplOutput(
                    f"Path {_path} is outside the workspace root directory: {workspace_root}. You can only access files within the workspace root directory.",
                    f"Path {_path} is outside the workspace root directory: {workspace_root}. You can only access files within the workspace root directory.",
                    {"success": False},
                )
            if command == "view":
                return self.view(_path, view_range)
            elif command == "create":
                if file_text is None:
                    raise ToolError(
                        "Parameter `file_text` is required for command: create"
                    )
                self.write_file(_path, file_text)
                self._file_history[_path].append(file_text)
                return ExtendedToolImplOutput(
                    f"File created successfully at: {_path}",
                    f"File created successfully at: {_path}",
                    {"success": True},
                )
            elif command == "str_replace":
                if old_str is None:
                    raise ToolError(
                        "Parameter `old_str` is required for command: str_replace"
                    )
                if self.ignore_indentation_for_str_replace:
                    return self._str_replace_ignore_indent(_path, old_str, new_str)
                else:
                    return self.str_replace(_path, old_str, new_str)
            elif command == "insert":
                if insert_line is None:
                    raise ToolError(
                        "Parameter `insert_line` is required for command: insert"
                    )
                if new_str is None:
                    raise ToolError(
                        "Parameter `new_str` is required for command: insert"
                    )
                return self.insert(_path, insert_line, new_str)
            elif command == "undo_edit":
                return self.undo_edit(_path)
            raise ToolError(
                f'Unrecognized command {command}. The allowed commands for the {self.name} tool are: {", ".join(get_args(Command))}'
            )
        except ToolError as e:
            return ExtendedToolImplOutput(
                e.message,
                e.message,
                {"success": False},
            )

    def validate_path(self, command: str, path: Path):
        """
        Check that the path/command combination is valid.
        """
        # Check if path exists
        if not path.exists() and command != "create":
            raise ToolError(
                f"The path {path} does not exist. Please provide a valid path."
            )
        if path.exists() and command == "create":
            content = self.read_file(path)
            if content.strip():
                raise ToolError(
                    f"File already exists and is not empty at: {path}. Cannot overwrite non empty files using command `create`."
                )
        # Check if the path points to a directory
        if path.is_dir():
            if command != "view":
                raise ToolError(
                    f"The path {path} is a directory and only the `view` command can be used on directories"
                )

    def view(
        self, path: Path, view_range: Optional[list[int]] = None
    ) -> ExtendedToolImplOutput:
        if path.is_dir():
            if view_range:
                raise ToolError(
                    "The `view_range` parameter is not allowed when `path` points to a directory."
                )

            _, stdout, stderr = run_sync(rf"find {path} -maxdepth 2 -not -path '*/\.*'")
            if not stderr:
                output = f"Here's the files and directories up to 2 levels deep in {path}, excluding hidden items:\n{stdout}\n"
            else:
                output = f"stderr: {stderr}\nstdout: {stdout}\n"
            return ExtendedToolImplOutput(
                output, "Listed directory contents", {"success": not stderr}
            )

        file_content = self.read_file(path)
        file_lines = file_content.split(
            "\n"
        )  # Split into lines early for total line count
        init_line = 1
        if view_range:
            if len(view_range) != 2 or not all(isinstance(i, int) for i in view_range):
                raise ToolError(
                    "Invalid `view_range`. It should be a list of two integers."
                )
            n_lines_file = len(file_lines)
            init_line, final_line = view_range
            if init_line < 1 or init_line > n_lines_file:
                raise ToolError(
                    f"Invalid `view_range`: {view_range}. Its first element `{init_line}` should be within the range of lines of the file: {[1, n_lines_file]}"
                )
            if final_line > n_lines_file:
                raise ToolError(
                    f"Invalid `view_range`: {view_range}. Its second element `{final_line}` should be smaller than the number of lines in the file: `{n_lines_file}`"
                )
            if final_line != -1 and final_line < init_line:
                raise ToolError(
                    f"Invalid `view_range`: {view_range}. Its second element `{final_line}` should be larger or equal than its first `{init_line}`"
                )

            if final_line == -1:
                file_content = "\n".join(file_lines[init_line - 1 :])
            else:
                file_content = "\n".join(file_lines[init_line - 1 : final_line])

        output = self._make_output(
            file_content=file_content,
            file_descriptor=str(path),
            total_lines=len(
                file_lines
            ),  # Use total lines in file, not just the viewed range
            init_line=init_line,
        )
        return ExtendedToolImplOutput(
            output, "Displayed file content", {"success": True}
        )

    def _str_replace_ignore_indent(self, path: Path, old_str: str, new_str: str | None):
        """Replace old_str with new_str in content, ignoring indentation.

        Finds matches in stripped version of text and uses those line numbers
        to perform replacements in original indented version.
        """
        if new_str is None:
            new_str = ""

        content = self.read_file(path)
        if self.expand_tabs:
            content = content.expandtabs()
            old_str = old_str.expandtabs()
            new_str = new_str.expandtabs()

        new_str = match_indent(new_str, content)

        # Split into lines for processing
        content_lines = content.splitlines()
        stripped_content_lines = [line.strip() for line in content.splitlines()]
        stripped_old_str_lines = [line.strip() for line in old_str.splitlines()]

        # Find all potential starting line matches
        matches = []
        for i in range(len(stripped_content_lines) - len(stripped_old_str_lines) + 1):
            is_match = True
            for j, pattern_line in enumerate(stripped_old_str_lines):
                if j == len(stripped_old_str_lines) - 1:
                    if stripped_content_lines[i + j].startswith(pattern_line):
                        # it's a match but last line in old_str is not the full line
                        # we need to append the rest of the line to new_str
                        new_str += stripped_content_lines[i + j][len(pattern_line) :]
                    else:
                        is_match = False
                        break
                elif stripped_content_lines[i + j] != pattern_line:
                    is_match = False
                    break
            if is_match:
                matches.append(i)

        if not matches:
            raise ToolError(
                f"No replacement was performed, old_str \n ```\n{old_str}\n```\n did not appear in {path}."
            )
        if len(matches) > 1:
            # Add 1 to convert to 1-based line numbers for error message
            match_lines = [idx + 1 for idx in matches]
            raise ToolError(
                f"No replacement was performed. Multiple occurrences of old_str \n ```\n{old_str}\n```\n starting at lines {match_lines}. Please ensure it is unique"
            )

        # Get the matching range in the original content
        match_start = matches[0]
        match_end = match_start + len(stripped_old_str_lines)

        # Get the original indented lines
        original_matched_lines = content_lines[match_start:match_end]

        indented_new_str = match_indent_by_first_line(
            new_str, original_matched_lines[0]
        )

        # Create new content by replacing the matched lines
        new_content = [
            *content_lines[:match_start],
            *indented_new_str.splitlines(),
            *content_lines[match_end:],
        ]
        new_content_str = "\n".join(new_content)

        self._file_history[path].append(content)  # Save old content for undo
        path.write_text(new_content_str)

        # Create a snippet of the edited section
        start_line = max(0, match_start - SNIPPET_LINES)
        end_line = match_start + SNIPPET_LINES + new_str.count("\n")
        snippet = "\n".join(new_content[start_line : end_line + 1])

        # Prepare the success message
        success_msg = f"The file {path} has been edited. "
        success_msg += self._make_output(
            file_content=snippet,
            file_descriptor=f"a snippet of {path}",
            total_lines=len(new_content),
            init_line=start_line + 1,
        )
        success_msg += "Review the changes and make sure they are as expected. Edit the file again if necessary."

        return ExtendedToolImplOutput(
            success_msg,
            f"The file {path} has been edited.",
            {"success": True},
        )

    def str_replace(
        self, path: Path, old_str: str, new_str: str | None
    ) -> ExtendedToolImplOutput:
        if new_str is None:
            new_str = ""

        content = self.read_file(path)
        if self.expand_tabs:
            content = content.expandtabs()
            old_str = old_str.expandtabs()
            new_str = new_str.expandtabs()

        if not old_str.strip():
            if content.strip():
                raise ToolError(
                    f"No replacement was performed, old_str is empty which is only allowed when the file is empty. The file {path} is not empty."
                )
            else:
                # replace the whole file with new_str
                new_content = new_str
                self._file_history[path].append(content)  # Save old content for undo
                path.write_text(new_content)
                # Prepare the success message
                success_msg = f"The file {path} has been edited. "
                success_msg += self._make_output(
                    file_content=new_content,
                    file_descriptor=f"{path}",
                    total_lines=len(new_content.split("\n")),
                )
                success_msg += "Review the changes and make sure they are as expected. Edit the file again if necessary."

                return ExtendedToolImplOutput(
                    success_msg,
                    f"The file {path} has been edited.",
                    {"success": True},
                )

        # Parse old_str into lines with line numbers
        old_str_lines = old_str.strip().split("\n")
        if len(old_str_lines) == 0:
            raise ToolError("old_str cannot be empty")

        # Extract line numbers and content from first line to check format
        try:
            # Try to split first line into line number and content
            first_line_parts = old_str_lines[0].split("\t")
            if len(first_line_parts) != 2:
                # No line numbers format
                line_content = old_str_lines[0]
        except (IndexError, ValueError):
            # Treat as no line numbers format
            line_content = old_str_lines[0]

        # For single line
        if len(old_str_lines) == 1:
            content = self.read_file(path)
            search_str = line_content
            if search_str not in content:
                raise ToolError(
                    f"No replacement was performed, old_str \n ```\n{old_str}\n```\n did not appear in {path}."
                )
            occurrences = content.count(search_str)
            if occurrences > 1:
                raise ToolError(
                    f"No replacement was performed. Multiple occurrences of old_str \n ```\n{old_str}\n```\n. Please ensure it is unique"
                )
            new_content = content.replace(search_str, new_str or "")
            self._file_history[path].append(content)
            path.write_text(new_content)
            return ExtendedToolImplOutput(
                f"The file {path} has been edited.",
                f"The file {path} has been edited.",
                {"success": True},
            )

        # Extract line numbers and content
        line_number_errors = []
        line_mismatches = []

        try:
            first_line_num = int(old_str_lines[0].split("\t")[0])
            last_line_num = int(old_str_lines[-1].split("\t")[0])
            first_line_content = old_str_lines[0].split("\t")[1]
            last_line_content = old_str_lines[-1].split("\t")[1]

            # Validate all line numbers are sequential
            ellipsis_idx = None
            for idx, line in enumerate(old_str_lines):
                if line.strip() == "...":
                    ellipsis_idx = idx
                    break

            for j, line in enumerate(old_str_lines):
                if line.strip() == "...":
                    continue

                try:
                    line_parts = line.split("\t")
                    if len(line_parts) != 2:
                        line_number_errors.append(
                            f"Line {j+1} in old_str doesn't have proper line number format"
                        )
                        continue

                    line_num = int(line_parts[0])

                    # Skip first and last line for sequential check
                    if j == 0 or j == len(old_str_lines) - 1:
                        continue  # Skip first and last line

                    # Check if line number is sequential based on position
                    if ellipsis_idx is None or j < ellipsis_idx:
                        # Before ellipsis - should be sequential from first line
                        expected_line_num = first_line_num + j
                        if line_num != expected_line_num:
                            line_number_errors.append(
                                f"Line number {line_num} is not sequential (expected {expected_line_num})"
                            )
                    elif j > ellipsis_idx:
                        # After ellipsis - check if sequential from previous line
                        prev_idx = j - 1
                        while prev_idx > ellipsis_idx:
                            prev_line = old_str_lines[prev_idx]
                            if prev_line.strip() != "...":
                                try:
                                    prev_line_num = int(prev_line.split("\t")[0])
                                    if line_num != prev_line_num + 1:
                                        line_number_errors.append(
                                            f"Line number {line_num} is not sequential (expected {prev_line_num + 1})"
                                        )
                                    break
                                except (ValueError, IndexError):
                                    pass
                            prev_idx -= 1
                except (ValueError, IndexError) as e:
                    line_number_errors.append(
                        f"Error parsing line {j+1} in old_str: {str(e)}"
                    )

            if line_number_errors:
                error_msg = "Line number errors in old_str:\n" + "\n".join(
                    line_number_errors
                )
                raise ToolError(error_msg)

        except (IndexError, ValueError) as e:
            raise ToolError(f"Invalid line number format in old_str: {str(e)}")

        # Check if there's an ellipsis
        has_ellipsis = any(line.strip() == "..." for line in old_str_lines[1:-1])
        if has_ellipsis:
            # Count lines before ellipsis
            lines_before = 0
            for line in old_str_lines:
                if line.strip() == "...":
                    break
                lines_before += 1

            # Count lines after ellipsis
            lines_after = 0
            found_ellipsis = False
            for line in old_str_lines:
                if line.strip() == "...":
                    found_ellipsis = True
                elif found_ellipsis:
                    lines_after += 1

            if (
                lines_before < self.min_lines_before_and_after_ellipsis
                or lines_after < self.min_lines_before_and_after_ellipsis
            ):
                raise ToolError(
                    f"When using '...', you must include at least {self.min_lines_before_and_after_ellipsis} lines "
                    f"before and after. Found {lines_before} lines before and {lines_after} lines after."
                )

            # Get file content lines
            file_lines = content.split("\n")

            # Find matches
            matches = []
            line_mismatches = []
            line_number_errors = []

            for i in range(len(file_lines) - (last_line_num - first_line_num)):
                # First check if first and last lines match
                if (
                    file_lines[i] == first_line_content
                    and file_lines[i + last_line_num - first_line_num]
                    == last_line_content
                ):
                    # Now check all non-ellipsis lines in old_str
                    is_match = True

                    # Skip the ellipsis line
                    ellipsis_idx = None
                    for j, line in enumerate(old_str_lines):
                        if line.strip() == "...":
                            ellipsis_idx = j
                            break

                    # Check all lines before ellipsis
                    for j in (
                        range(1, ellipsis_idx)
                        if ellipsis_idx
                        else range(1, len(old_str_lines) - 1)
                    ):
                        try:
                            line_parts = old_str_lines[j].split("\t")
                            if len(line_parts) != 2:
                                line_number_errors.append(
                                    f"Line {j+1} in old_str doesn't have proper line number format"
                                )
                                is_match = False
                                continue

                            line_num = int(line_parts[0])
                            line_content = line_parts[1]

                            # Check if line number is sequential
                            if line_num != first_line_num + j:
                                line_number_errors.append(
                                    f"Line number {line_num} is not sequential (expected {first_line_num + j})"
                                )
                                is_match = False

                            # Check if content matches
                            if file_lines[i + j] != line_content:
                                line_mismatches.append(
                                    f"Line {line_num}: expected '{line_content}', found '{file_lines[i + j]}'"
                                )
                                is_match = False
                        except (ValueError, IndexError) as e:
                            line_number_errors.append(
                                f"Error parsing line {j+1} in old_str: {str(e)}"
                            )
                            is_match = False

                    # Check all lines after ellipsis
                    if ellipsis_idx:
                        for j in range(ellipsis_idx + 1, len(old_str_lines) - 1):
                            try:
                                line_parts = old_str_lines[j].split("\t")
                                if len(line_parts) != 2:
                                    line_number_errors.append(
                                        f"Line {j+1} in old_str doesn't have proper line number format"
                                    )
                                    is_match = False
                                    continue

                                line_num = int(line_parts[0])
                                line_content = line_parts[1]

                                # Calculate expected position in file
                                file_pos = i + (line_num - first_line_num)

                                # Check if content matches
                                if file_lines[file_pos] != line_content:
                                    line_mismatches.append(
                                        f"Line {line_num}: expected '{line_content}', found '{file_lines[file_pos]}'"
                                    )
                                    is_match = False
                            except (ValueError, IndexError) as e:
                                line_number_errors.append(
                                    f"Error parsing line {j+1} in old_str: {str(e)}"
                                )
                                is_match = False

                    if is_match:
                        matches.append(i)

            if not matches:
                error_msg = f"No replacement was performed, old_str \n ```\n{old_str}\n```\n did not appear in {path}."

                # Add detailed error information if available
                if line_number_errors:
                    error_msg += "\n\nLine number errors:\n" + "\n".join(
                        line_number_errors
                    )

                if line_mismatches:
                    error_msg += "\n\nContent mismatches:\n" + "\n".join(
                        line_mismatches
                    )

                raise ToolError(error_msg)
            elif len(matches) > 1:
                # Add 1 to convert to 1-based line numbers for error message
                match_lines = [idx + 1 for idx in matches]
                raise ToolError(
                    f"No replacement was performed. Multiple occurrences of old_str \n ```\n{old_str}\n```\n starting at lines {match_lines}. Please ensure it is unique"
                )

            # Get the matching range
            match_start = matches[0]
            match_end = match_start + (last_line_num - first_line_num) + 1

            # Create new content
            new_content = (
                file_lines[:match_start] + new_str.split("\n") + file_lines[match_end:]
            )
            new_content_str = "\n".join(new_content)

            self._file_history[path].append(content)  # Save old content for undo
            path.write_text(new_content_str)

            # Create a snippet of the edited section
            start_line = max(0, match_start - SNIPPET_LINES)
            end_line = match_start + SNIPPET_LINES + new_str.count("\n")
            snippet = "\n".join(new_content[start_line : end_line + 1])

            # Prepare the success message
            success_msg = f"The file {path} has been edited. "
            success_msg += self._make_output(
                file_content=snippet,
                file_descriptor=f"a snippet of {path}",
                total_lines=len(new_content),
                init_line=start_line + 1,
            )
            success_msg += "Review the changes and make sure they are as expected. Edit the file again if necessary."

            return ExtendedToolImplOutput(
                success_msg,
                f"The file {path} has been edited.",
                {"success": True},
            )

        # Get file content lines
        file_lines = content.split("\n")

        # Find matches
        matches = []
        line_mismatches = []
        line_number_errors = []

        for i in range(len(file_lines) - (last_line_num - first_line_num)):
            if i + first_line_num > len(file_lines):
                break

            # Check first and last line match
            if (
                file_lines[i] == first_line_content
                and file_lines[i + last_line_num - first_line_num] == last_line_content
            ):
                # For exact match, check all lines
                if not has_ellipsis:
                    is_match = True
                    for j, line in enumerate(old_str_lines[1:-1], start=1):
                        try:
                            line_parts = line.split("\t")
                            if len(line_parts) != 2:
                                line_number_errors.append(
                                    f"Line {j+1} in old_str doesn't have proper line number format"
                                )
                                is_match = False
                                continue

                            line_num = int(line_parts[0])
                            line_content = line_parts[1]

                            # Check if line number is sequential
                            if line_num != first_line_num + j:
                                line_number_errors.append(
                                    f"Line number {line_num} is not sequential (expected {first_line_num + j})"
                                )
                                is_match = False

                            # Check if content matches
                            if file_lines[i + j] != line_content:
                                line_mismatches.append(
                                    f"Line {line_num}: expected '{line_content}', found '{file_lines[i + j]}'"
                                )
                                is_match = False
                        except (ValueError, IndexError) as e:
                            line_number_errors.append(
                                f"Error parsing line {j+1} in old_str: {str(e)}"
                            )
                            is_match = False

                    if is_match:
                        matches.append(i)
                else:
                    # With ellipsis, just first and last line need to match
                    matches.append(i)

        if not matches:
            error_msg = f"No replacement was performed, old_str \n ```\n{old_str}\n```\n did not appear in {path}."

            # Add detailed error information if available
            if line_number_errors:
                error_msg += "\n\nLine number errors:\n" + "\n".join(line_number_errors)

            if line_mismatches:
                error_msg += "\n\nContent mismatches:\n" + "\n".join(line_mismatches)

            raise ToolError(error_msg)
        elif len(matches) > 1:
            # Add 1 to convert to 1-based line numbers for error message
            match_lines = [idx + 1 for idx in matches]
            raise ToolError(
                f"No replacement was performed. Multiple occurrences of old_str \n ```\n{old_str}\n```\n starting at lines {match_lines}. Please ensure it is unique"
            )

        # Get the matching range
        match_start = matches[0]
        match_end = match_start + (last_line_num - first_line_num) + 1

        # Create new content
        new_content = (
            file_lines[:match_start] + new_str.split("\n") + file_lines[match_end:]
        )
        new_content_str = "\n".join(new_content)

        self._file_history[path].append(content)  # Save old content for undo
        path.write_text(new_content_str)

        # Create a snippet of the edited section
        start_line = max(0, match_start - SNIPPET_LINES)
        end_line = match_start + SNIPPET_LINES + new_str.count("\n")
        snippet = "\n".join(new_content[start_line : end_line + 1])

        # Prepare the success message
        success_msg = f"The file {path} has been edited. "
        success_msg += self._make_output(
            file_content=snippet,
            file_descriptor=f"a snippet of {path}",
            total_lines=len(new_content),
            init_line=start_line + 1,
        )
        success_msg += "Review the changes and make sure they are as expected. Edit the file again if necessary."

        return ExtendedToolImplOutput(
            success_msg,
            f"The file {path} has been edited.",
            {"success": True},
        )

    def insert(
        self, path: Path, insert_line: int, new_str: str
    ) -> ExtendedToolImplOutput:
        """Implement the insert command, which inserts new_str at the specified line in the file content."""
        file_text = self.read_file(path)
        if self.expand_tabs:
            file_text = file_text.expandtabs()
            new_str = new_str.expandtabs()
        file_text_lines = file_text.split("\n")
        n_lines_file = len(file_text_lines)

        if insert_line < 0 or insert_line > n_lines_file:
            raise ToolError(
                f"Invalid `insert_line` parameter: {insert_line}. It should be within the range of lines of the file: {[0, n_lines_file]}"
            )

        new_str_lines = new_str.split("\n")
        new_file_text_lines = (
            file_text_lines[:insert_line]
            + new_str_lines
            + file_text_lines[insert_line:]
        )
        snippet_lines = (
            file_text_lines[max(0, insert_line - SNIPPET_LINES) : insert_line]
            + new_str_lines
            + file_text_lines[insert_line : insert_line + SNIPPET_LINES]
        )

        new_file_text = "\n".join(new_file_text_lines)
        snippet = "\n".join(snippet_lines)

        self.write_file(path, new_file_text)
        self._file_history[path].append(file_text)

        success_msg = f"The file {path} has been edited. "
        success_msg += self._make_output(
            file_content=snippet,
            file_descriptor="a snippet of the edited file",
            total_lines=len(new_file_text_lines),
            init_line=max(1, insert_line - SNIPPET_LINES + 1),
        )
        success_msg += "Review the changes and make sure they are as expected (correct indentation, no duplicate lines, etc). Edit the file again if necessary."

        return ExtendedToolImplOutput(
            success_msg,
            "Insert successful",
            {"success": True},
        )

    def undo_edit(self, path: Path) -> ExtendedToolImplOutput:
        """Implement the undo_edit command."""
        if not self._file_history[path]:
            raise ToolError(f"No edit history found for {path}.")

        old_text = self._file_history[path].pop()
        self.write_file(path, old_text)

        formatted_file = self._make_output(
            file_content=old_text,
            file_descriptor=str(path),
            total_lines=len(old_text.split("\n")),
        )
        output = f"Last edit to {path} undone successfully.\n{formatted_file}"

        return ExtendedToolImplOutput(
            output,
            "Undo successful",
            {"success": True},
        )

    def read_file(self, path: Path):
        """Read the content of a file from a given path; raise a ToolError if an error occurs."""
        try:
            return path.read_text()
        except Exception as e:
            raise ToolError(f"Ran into {e} while trying to read {path}") from None

    def write_file(self, path: Path, file: str):
        """Write the content of a file to a given path; raise a ToolError if an error occurs."""
        try:
            path.write_text(file)
        except Exception as e:
            raise ToolError(f"Ran into {e} while trying to write to {path}") from None

    def _make_output(
        self,
        file_content: str,
        file_descriptor: str,
        total_lines: int,
        init_line: int = 1,
    ):
        """Generate output for the CLI based on the content of a file."""
        file_content = maybe_truncate(file_content)
        if self.expand_tabs:
            file_content = file_content.expandtabs()
        file_content = "\n".join(
            [
                f"{i + init_line:6}\t{line}"
                for i, line in enumerate(file_content.split("\n"))
            ]
        )
        return (
            f"Here's the result of running `cat -n` on {file_descriptor}:\n"
            + file_content
            + "\n"
            + f"Total lines in file: {total_lines}\n"
        )

    def get_tool_start_message(self, tool_input: dict[str, Any]) -> str:
        return f"Editing file {tool_input['path']}"
