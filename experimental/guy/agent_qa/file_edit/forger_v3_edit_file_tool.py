from pathlib import Path
from experimental.guy.agent_qa.builtin_tools import (
    FileEditClient,
)
from experimental.guy.agent_qa.workspace_manager import WorkspaceManager
from research.agents.tools import (
    DialogMessages,
    LLMTool,
    ToolCallLogger,
    ToolImplOutput,
)

from typing import Any, Optional


class EditFileToolV3(LLMTool):
    name = "edit_file_v3"
    """A tool that edits a file.

    Uses Forger, our smart paste model.
    """

    description = "Edit a file. Accepts a file path, a short description of the edit and a detailed description of the edit."
    input_schema = {
        "type": "object",
        "properties": {
            "file_path": {
                "type": "string",
                "description": "The path to the file to edit.",
            },
            "short_edit_description": {
                "type": "string",
                "description": "One sentence description of the edit to be made.",
            },
            "detailed_edit_description": {
                "type": "string",
                "description": """\
Provide a detailed edit plan that precisely describes how to modify a file. Your plan should:

Specify exact changes with enough context that another person or tool could implement them without ambiguity

For code changes:
- Use Markdown code blocks to show modified code
- Include sufficient surrounding unmodified code to locate where changes should be made
- Indicate existing code using placeholders like # ... existing methods ... or // ... rest of method ...

When removing or moving code:
- Explicitly identify functions/blocks to be deleted
- Specify the exact destination for moved code
- Include any necessary cleanup (e.g., removing unused imports)

The edit plan should be specific enough that different developers would make identical changes when following it.""",
            },
        },
        "required": [
            "file_path",
            "short_edit_description",
            "detailed_edit_description",
        ],
    }

    MODEL = "forger-v3-qwen-14b-inst-fr-32k-edit"

    def __init__(
        self,
        tool_call_logger: ToolCallLogger,
        workspace_manager: WorkspaceManager,
        file_edit_client: FileEditClient,
    ):
        super().__init__(tool_call_logger)
        self.workspace_manager = workspace_manager
        self.file_edit_client = file_edit_client

    def run_impl(
        self,
        tool_input: dict[str, Any],
        dialog_messages: Optional[DialogMessages] = None,
    ) -> ToolImplOutput:
        path = Path(tool_input["file_path"])
        abs_path = self.workspace_manager.root / path

        if not abs_path.exists():
            tool_output = f"File {path} does not exist, cannot edit"
            return ToolImplOutput(tool_output, tool_output, {"success": False})

        target_file_content = abs_path.read_text()

        try:
            new_content, request_id = self.file_edit_client.edit_file(
                target_file_path=tool_input["file_path"],
                target_file_content=target_file_content,
                edit_plan=tool_input["short_edit_description"],
                code_block="Fake code block just to make sure smart_paste_prompt_formatter is used",
                detailed_edit_description=tool_input["detailed_edit_description"],
                model=self.MODEL,
            )
        except Exception as exc:
            return ToolImplOutput(
                f"Failed to edit file: {exc}",
                "Failed to edit file.",
                {"success": False},
            )

        abs_path.write_text(new_content)
        self.workspace_manager.update()

        tool_output = f"File {path} edited, request_id={request_id}"

        return ToolImplOutput(tool_output, tool_output, {"success": True})

    def get_tool_start_message(self, tool_input: dict[str, Any]) -> str:
        return f"Editing file {tool_input['file_path']}"
