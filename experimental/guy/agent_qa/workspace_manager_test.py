import pytest
from pathlib import Path
from unittest.mock import patch, MagicMock
from experimental.guy.agent_qa.workspace_manager import WorkspaceManagerImpl
from experimental.guy.agent_qa.user_confirmation import AlwaysConfirmUserConfirmationProvider


class MockAugmentClient:
    def upload_blobs_as_needed(self, contents):
        pass


@pytest.fixture
def confirmation_provider():
    return AlwaysConfirmUserConfirmationProvider()


def test_revert_to_initial_state_no_changes(tmp_path, capsys, confirmation_provider):
    # Setup workspace with initial files
    client = MockAugmentClient()
    workspace = WorkspaceManagerImpl(client, tmp_path, confirmation_provider=confirmation_provider)
    initial_file = tmp_path / "test.txt"
    initial_file.write_text("initial content")
    workspace.update()  # This creates the initial snapshot
    workspace.snapshot_workspace()  # Save initial state
    
    # Try to revert - should show message since no changes
    workspace.revert_to_initial_state()
    
    # Verify message and file is unchanged
    captured = capsys.readouterr()
    assert "No changes to revert." in captured.out
    assert initial_file.read_text() == "initial content"


def test_revert_to_initial_state_no_snapshots(tmp_path, capsys, confirmation_provider):
    # Setup empty workspace
    client = MockAugmentClient()
    workspace = WorkspaceManagerImpl(client, tmp_path, confirmation_provider=confirmation_provider)
    
    # Try to revert - should show message since no snapshots
    workspace.revert_to_initial_state()
    
    # Verify message and workspace is still empty
    captured = capsys.readouterr()
    assert "No snapshots exist yet" in captured.out
    assert not list(tmp_path.iterdir())


def test_revert_to_initial_state_with_all_types_of_changes(tmp_path, capsys, confirmation_provider):
    # Setup workspace with initial files
    client = MockAugmentClient()
    workspace = WorkspaceManagerImpl(client, tmp_path, confirmation_provider=confirmation_provider)
    
    # Create initial state with two files
    test_file = tmp_path / "test.txt"
    test_file.write_text("initial content")
    other_file = tmp_path / "other.txt"
    other_file.write_text("other content")
    workspace.update()
    workspace.snapshot_workspace()  # Save initial state
    
    # Make various types of changes:
    # 1. Modify test_file
    # 2. Remove other_file
    # 3. Add new_file
    test_file.write_text("modified content")
    other_file.unlink()
    new_file = tmp_path / "new.txt"
    new_file.write_text("new file")
    workspace.update()
    
    # Revert changes
    workspace.revert_to_initial_state()
    
    # Verify output shows all types of changes
    captured = capsys.readouterr()
    assert "Modified files:" in captured.out
    assert "test.txt" in captured.out
    assert "Added files that will be removed:" in captured.out
    assert "new.txt" in captured.out
    assert "Removed files that will be restored:" in captured.out
    assert "other.txt" in captured.out
    
    # Verify files are reverted correctly
    assert test_file.read_text() == "initial content"  # Modified file restored
    assert other_file.exists()  # Removed file restored
    assert other_file.read_text() == "other content"
    assert not new_file.exists()  # Added file removed


