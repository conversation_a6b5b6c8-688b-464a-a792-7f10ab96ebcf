"""Recent changes and next-edit."""

import json
import re
import subprocess
from enum import Enum
from pathlib import Path
from typing import Any, Iterable, List, Optional, Tuple

from base.augment_client.client import (
    AugmentClient,
    AugmentModelClient,
    BlobsJson,
    NextEditResponse,
)
from base.blob_names.python.blob_names import get_blob_name
from base.diff_utils.diff_utils import File, compute_file_diff
from experimental.guy.agent_qa.workspace_manager import WorkspaceManager
from research.agents.tools import (
    DialogMessages,
    LLMTool,
    ToolCallLogger,
    ToolImplOutput,
)
from research.core.diff_utils import (
    get_source_path,
    get_target_path,
    parse_git_diff_output,
)
from services.api_proxy.public_api_pb2 import FileEdit, FileEditEvent


def patch_to_edit_events(patch: str) -> List[FileEditEvent]:
    """Convert a git diff patch string to FileEditEvent objects.

    Args:
        patch: A git diff patch string.

    Returns:
        A list of FileEditEvent objects representing the changes in the patch.

    Raises:
        ValueError: If the patch is invalid or cannot be parsed.
    """
    if not patch:
        return []

    # Basic validation of patch format
    lines = patch.splitlines()
    if not any(line.startswith("--- ") for line in lines):
        raise ValueError("Invalid patch format: missing '---' line")
    if not any(line.startswith("+++ ") for line in lines):
        raise ValueError("Invalid patch format: missing '+++' line")
    if not any(line.startswith("@@ ") for line in lines):
        raise ValueError("Invalid patch format: missing '@@ ... @@' hunk header")

    try:
        patch_set = parse_git_diff_output(patch)
    except Exception as e:
        raise ValueError(f"Failed to parse patch: {e}")

    events = []
    for patched_file in patch_set:
        before_path = get_source_path(patched_file)
        after_path = get_target_path(patched_file)

        # Skip files that were both added and deleted
        if not before_path and not after_path:
            continue

        edits = []
        for hunk in patched_file:
            # For file additions/deletions, use 0 instead of -1
            before_start = max(0, hunk.source_start - 1)
            after_start = max(0, hunk.target_start - 1)

            # Collect before and after text
            before_lines = []
            after_lines = []
            for line in hunk:
                if line.is_removed:
                    before_lines.append(line.value)
                elif line.is_added:
                    after_lines.append(line.value)
                else:
                    before_lines.append(line.value)
                    after_lines.append(line.value)

            edits.append(
                FileEdit(
                    before_start=before_start,
                    before_text="".join(before_lines),
                    after_start=after_start,
                    after_text="".join(after_lines),
                )
            )

        # Compute blob names for before and after content
        before_text = "".join(edit.before_text for edit in edits)
        after_text = "".join(edit.after_text for edit in edits)
        before_blob_name = (
            get_blob_name(before_path, before_text.encode("utf-8"))
            if before_path
            else ""
        )
        after_blob_name = (
            get_blob_name(after_path, after_text.encode("utf-8")) if after_path else ""
        )

        events.append(
            FileEditEvent(
                path=after_path if after_path else before_path,
                before_blob_name=before_blob_name,
                after_blob_name=after_blob_name,
                edits=edits,
            )
        )

    return events


def edit_events_to_patch(events: List[FileEditEvent]) -> str:
    """Convert FileEditEvent objects to a git diff patch string.

    Args:
        events: A list of FileEditEvent objects.

    Returns:
        A git diff patch string representing the changes.
    """
    if not events:
        return ""

    patches = []

    for event in events:
        # For each event, we need to reconstruct the before and after file contents
        # from the edits
        before_contents = []
        after_contents = []

        # Sort edits by start position
        sorted_edits = sorted(
            event.edits, key=lambda e: (e.before_start, e.after_start)
        )

        last_before_pos = 0
        last_after_pos = 0

        for edit in sorted_edits:
            # Add unchanged text before this edit
            before_contents.append(
                edit.before_text[last_before_pos : edit.before_start]
            )
            after_contents.append(edit.after_text[last_after_pos : edit.after_start])

            # Add the edited text
            before_contents.append(edit.before_text)
            after_contents.append(edit.after_text)

            last_before_pos = edit.before_start + len(edit.before_text)
            last_after_pos = edit.after_start + len(edit.after_text)

        # Create File objects
        before_file = File(path=event.path, contents="".join(before_contents))
        after_file = File(path=event.path, contents="".join(after_contents))

        # Use diff_utils to generate the patch
        patch = compute_file_diff(before_file, after_file)
        patches.append(str(patch))

    return "".join(patches)


class RecentChangesMode(Enum):
    """Mode for selecting recent changes."""

    SINCE_BRANCH_POINT = "branch_point"
    """Changes since branching from main"""

    SINCE_LAST_COMMIT = "last_commit"
    """All changes since last commit (staged + unstaged)"""

    STAGED_CHANGES = "staged"
    """Only staged changes"""

    UNSTAGED_CHANGES = "unstaged"
    """Only unstaged changes"""


class RecentChangesProvider:
    """A class that handles recent git changes in different modes."""

    def __init__(
        self,
        workspace_manager: WorkspaceManager,
        max_line_length: int = 500,
        max_output_lines: int = 2000,
    ):
        self.workspace_manager = workspace_manager
        self.max_line_length = max_line_length
        self.max_output_lines = max_output_lines

    def _run_git_command(self, command: list[str]) -> str:
        """Run a git command and return its output."""
        try:
            result = subprocess.run(
                command,
                capture_output=True,
                text=True,
                check=True,
                cwd=self.workspace_manager.root,
            )
            return result.stdout
        except subprocess.CalledProcessError as e:
            if e.stderr:
                return f"Git error: {e.stderr}"
            return f"Error running git command: {e}"

    def _get_untracked_files_diff(self, name_only: bool = False) -> str:
        """Get untracked files as a git diff output or list of files."""
        untracked = self._run_git_command(
            ["git", "ls-files", "--others", "--exclude-standard"]
        )
        if "Git error" in untracked:
            return untracked

        untracked = untracked.strip()
        if not untracked:
            return ""

        if name_only:
            return untracked

        output = ""
        for file in untracked.split("\n"):
            if file:
                path = Path(self.workspace_manager.root / file)
                try:
                    content = path.read_text()
                    output += f"\ndiff --git a/{file} b/{file}\n"
                    output += "new file mode 100644\n"
                    output += "--- /dev/null\n"
                    output += f"+++ b/{file}\n"
                    output += "@@ -0,0 +1 @@\n"
                    output += f"+{content}"
                    if not content.endswith("\n"):
                        output += "\n\\ No newline at end of file\n"
                except (IOError, OSError, UnicodeDecodeError):
                    continue
        return output

    def _filter_empty_lines(self, output: str) -> str:
        """Filter out empty lines from output."""
        return "\n".join(line for line in output.split("\n") if line.strip())

    def _get_current_branch(self) -> str:
        """Get the name of the current branch."""
        output = self._run_git_command(["git", "rev-parse", "--abbrev-ref", "HEAD"])
        if "Git error" in output:
            return "unknown"
        return output.strip()

    def _truncate_line(self, line: str) -> str:
        """Truncate a single line if it exceeds max_line_length."""
        if len(line) > self.max_line_length:
            return line[: self.max_line_length] + "... [line truncated]"
        return line

    def _truncate_output(self, output: str) -> str:
        """Truncate the entire output if it exceeds max_output_lines."""
        lines = output.splitlines()
        if len(lines) > self.max_output_lines:
            truncated_lines = lines[: self.max_output_lines]
            truncated_lines.append(
                f"... [output truncated, {len(lines) - self.max_output_lines} lines omitted]"
            )
            return "\n".join(truncated_lines)
        return output

    def _get_main_branch(self) -> str:
        """Get the name of the main branch."""
        output = self._run_git_command(
            ["git", "symbolic-ref", "refs/remotes/origin/HEAD"]
        )
        if "Git error" in output:
            return "main"
        return output.strip().replace("refs/remotes/origin/", "")

    def get_recent_changes(
        self, mode: RecentChangesMode, name_only: bool = False
    ) -> str:
        """Get recent changes in the specified mode."""
        name_only_flag = ["--name-only"] if name_only else []

        if mode == RecentChangesMode.SINCE_BRANCH_POINT:
            branch_name = self._get_current_branch()
            if name_only:
                main_branch = self._get_main_branch()
                merge_base = self._run_git_command(
                    ["git", "merge-base", "HEAD", main_branch]
                ).strip()
                if "Git error" in merge_base:
                    return merge_base

                output = self._run_git_command(
                    ["git", "diff", *name_only_flag, merge_base]
                )
                if "Git error" in output:
                    return output

                untracked = self._get_untracked_files_diff(name_only)
                if "Error" in untracked:
                    return untracked
                if untracked:
                    output += "\n" + untracked if output else untracked
            else:
                merge_base = self._run_git_command(
                    ["git", "merge-base", "HEAD", "main"]
                ).strip()
                if "Git error" in merge_base:
                    return merge_base

                changes = self._run_git_command(["git", "diff", merge_base])
                if "Git error" in changes:
                    return changes

                untracked = self._get_untracked_files_diff(False)
                if "Error" in untracked:
                    return untracked
                if untracked:
                    changes += "\n" + untracked if changes else untracked

                output = f"Current branch name: {branch_name}\n\nChanges since branching point:\n{changes}"

        elif mode == RecentChangesMode.SINCE_LAST_COMMIT:
            output = self._run_git_command(["git", "diff", *name_only_flag, "HEAD"])
            if "Git error" in output:
                return output

            untracked = self._get_untracked_files_diff(name_only)
            if "Error" in untracked:
                return untracked
            if untracked:
                output += "\n" + untracked if output else untracked

        elif mode == RecentChangesMode.STAGED_CHANGES:
            output = self._run_git_command(["git", "diff", "--cached", *name_only_flag])

        elif mode == RecentChangesMode.UNSTAGED_CHANGES:
            output = self._run_git_command(["git", "diff", *name_only_flag])
            if "Git error" in output:
                return output

            untracked = self._get_untracked_files_diff(name_only)
            if "Error" in untracked:
                return untracked
            if untracked:
                output += "\n" + untracked if output else untracked

        else:
            output = f"Invalid mode: {mode}"

        if name_only:
            output = self._filter_empty_lines(output)

        # Apply truncation
        truncated_lines = [self._truncate_line(line) for line in output.splitlines()]
        truncated_output = "\n".join(truncated_lines)
        final_output = self._truncate_output(truncated_output)

        return final_output


class RecentChangesTool(LLMTool):
    """A tool that shows recent git changes in different modes."""

    name = "recent_changes"
    description = "Show recent git changes in different modes: changes since branch point, changes since last commit (staged + unstaged), staged changes only, or unstaged changes only."
    input_schema = {
        "type": "object",
        "properties": {
            "mode": {
                "type": "string",
                "enum": [mode.value for mode in RecentChangesMode],
                "description": "The mode determining which changes to show. branch_point=changes since branching from main, last_commit=all changes since last commit (staged + unstaged), staged=only staged changes, unstaged=only unstaged changes.",
            },
            "name_only": {
                "type": "boolean",
                "description": "If true, only show file names without the actual changes.",
                "default": False,
            },
        },
        "required": ["mode"],
    }

    def __init__(
        self, tool_call_logger: ToolCallLogger, workspace_manager: WorkspaceManager
    ):
        super().__init__(tool_call_logger)
        self.recent_changes = RecentChangesProvider(workspace_manager)

    def run_impl(
        self,
        tool_input: dict[str, Any],
        dialog_messages: Optional[DialogMessages] = None,
    ) -> ToolImplOutput:
        mode_str = tool_input["mode"]
        try:
            mode = RecentChangesMode(mode_str)
        except ValueError:
            return ToolImplOutput(
                f"Invalid mode: {mode_str}", f"Invalid mode: {mode_str}"
            )

        name_only = tool_input.get("name_only", False)
        output = self.recent_changes.get_recent_changes(mode, name_only)
        return ToolImplOutput(output, output)

    def get_tool_start_message(self, tool_input: dict[str, Any]) -> str:
        return f"Getting {tool_input['mode']} changes"
